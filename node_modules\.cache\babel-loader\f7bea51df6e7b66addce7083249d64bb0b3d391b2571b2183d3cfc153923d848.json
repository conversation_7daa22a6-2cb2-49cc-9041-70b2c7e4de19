{"ast": null, "code": "import * as React from 'react';\nconst RadioGroupContext = /*#__PURE__*/React.createContext(null);\nexport const RadioGroupContextProvider = RadioGroupContext.Provider;\nexport default RadioGroupContext;\nexport const RadioOptionTypeContext = /*#__PURE__*/React.createContext(null);\nexport const RadioOptionTypeContextProvider = RadioOptionTypeContext.Provider;", "map": {"version": 3, "names": ["React", "RadioGroupContext", "createContext", "RadioGroupContextProvider", "Provider", "RadioOptionTypeContext", "RadioOptionTypeContextProvider"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/radio/context.js"], "sourcesContent": ["import * as React from 'react';\nconst RadioGroupContext = /*#__PURE__*/React.createContext(null);\nexport const RadioGroupContextProvider = RadioGroupContext.Provider;\nexport default RadioGroupContext;\nexport const RadioOptionTypeContext = /*#__PURE__*/React.createContext(null);\nexport const RadioOptionTypeContextProvider = RadioOptionTypeContext.Provider;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,iBAAiB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAChE,OAAO,MAAMC,yBAAyB,GAAGF,iBAAiB,CAACG,QAAQ;AACnE,eAAeH,iBAAiB;AAChC,OAAO,MAAMI,sBAAsB,GAAG,aAAaL,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC5E,OAAO,MAAMI,8BAA8B,GAAGD,sBAAsB,CAACD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}