{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"id\", \"prefixCls\", \"className\", \"items\", \"direction\", \"activeKey\", \"defaultActiveKey\", \"editable\", \"animated\", \"tabPosition\", \"tabBarGutter\", \"tabBarStyle\", \"tabBarExtraContent\", \"locale\", \"more\", \"destroyInactiveTabPane\", \"renderTabBar\", \"onChange\", \"onTabClick\", \"onTabScroll\", \"getPopupContainer\", \"popupClassName\", \"indicator\"];\n// Accessibility https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/Tab_Role\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport isMobile from \"rc-util/es/isMobile\";\nimport * as React from 'react';\nimport { useEffect, useState } from 'react';\nimport TabContext from \"./TabContext\";\nimport TabNavListWrapper from \"./TabNavList/Wrapper\";\nimport TabPanelList from \"./TabPanelList\";\nimport useAnimateConfig from \"./hooks/useAnimateConfig\";\n/**\n * Should added antd:\n * - type\n *\n * Removed:\n * - onNextClick\n * - onPrevClick\n * - keyboard\n */\n\n// Used for accessibility\nvar uuid = 0;\nvar Tabs = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-tabs' : _props$prefixCls,\n    className = props.className,\n    items = props.items,\n    direction = props.direction,\n    activeKey = props.activeKey,\n    defaultActiveKey = props.defaultActiveKey,\n    editable = props.editable,\n    animated = props.animated,\n    _props$tabPosition = props.tabPosition,\n    tabPosition = _props$tabPosition === void 0 ? 'top' : _props$tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    tabBarStyle = props.tabBarStyle,\n    tabBarExtraContent = props.tabBarExtraContent,\n    locale = props.locale,\n    more = props.more,\n    destroyInactiveTabPane = props.destroyInactiveTabPane,\n    renderTabBar = props.renderTabBar,\n    onChange = props.onChange,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll,\n    getPopupContainer = props.getPopupContainer,\n    popupClassName = props.popupClassName,\n    indicator = props.indicator,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var tabs = React.useMemo(function () {\n    return (items || []).filter(function (item) {\n      return item && _typeof(item) === 'object' && 'key' in item;\n    });\n  }, [items]);\n  var rtl = direction === 'rtl';\n  var mergedAnimated = useAnimateConfig(animated);\n\n  // ======================== Mobile ========================\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    mobile = _useState2[0],\n    setMobile = _useState2[1];\n  useEffect(function () {\n    // Only update on the client side\n    setMobile(isMobile());\n  }, []);\n\n  // ====================== Active Key ======================\n  var _useMergedState = useMergedState(function () {\n      var _tabs$;\n      return (_tabs$ = tabs[0]) === null || _tabs$ === void 0 ? void 0 : _tabs$.key;\n    }, {\n      value: activeKey,\n      defaultValue: defaultActiveKey\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedActiveKey = _useMergedState2[0],\n    setMergedActiveKey = _useMergedState2[1];\n  var _useState3 = useState(function () {\n      return tabs.findIndex(function (tab) {\n        return tab.key === mergedActiveKey;\n      });\n    }),\n    _useState4 = _slicedToArray(_useState3, 2),\n    activeIndex = _useState4[0],\n    setActiveIndex = _useState4[1];\n\n  // Reset active key if not exist anymore\n  useEffect(function () {\n    var newActiveIndex = tabs.findIndex(function (tab) {\n      return tab.key === mergedActiveKey;\n    });\n    if (newActiveIndex === -1) {\n      var _tabs$newActiveIndex;\n      newActiveIndex = Math.max(0, Math.min(activeIndex, tabs.length - 1));\n      setMergedActiveKey((_tabs$newActiveIndex = tabs[newActiveIndex]) === null || _tabs$newActiveIndex === void 0 ? void 0 : _tabs$newActiveIndex.key);\n    }\n    setActiveIndex(newActiveIndex);\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), mergedActiveKey, activeIndex]);\n\n  // ===================== Accessibility ====================\n  var _useMergedState3 = useMergedState(null, {\n      value: id\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedId = _useMergedState4[0],\n    setMergedId = _useMergedState4[1];\n\n  // Async generate id to avoid ssr mapping failed\n  useEffect(function () {\n    if (!id) {\n      setMergedId(\"rc-tabs-\".concat(process.env.NODE_ENV === 'test' ? 'test' : uuid));\n      uuid += 1;\n    }\n  }, []);\n\n  // ======================== Events ========================\n  function onInternalTabClick(key, e) {\n    onTabClick === null || onTabClick === void 0 || onTabClick(key, e);\n    var isActiveChanged = key !== mergedActiveKey;\n    setMergedActiveKey(key);\n    if (isActiveChanged) {\n      onChange === null || onChange === void 0 || onChange(key);\n    }\n  }\n\n  // ======================== Render ========================\n  var sharedProps = {\n    id: mergedId,\n    activeKey: mergedActiveKey,\n    animated: mergedAnimated,\n    tabPosition: tabPosition,\n    rtl: rtl,\n    mobile: mobile\n  };\n  var tabNavBarProps = _objectSpread(_objectSpread({}, sharedProps), {}, {\n    editable: editable,\n    locale: locale,\n    more: more,\n    tabBarGutter: tabBarGutter,\n    onTabClick: onInternalTabClick,\n    onTabScroll: onTabScroll,\n    extra: tabBarExtraContent,\n    style: tabBarStyle,\n    panes: null,\n    getPopupContainer: getPopupContainer,\n    popupClassName: popupClassName,\n    indicator: indicator\n  });\n  return /*#__PURE__*/React.createElement(TabContext.Provider, {\n    value: {\n      tabs: tabs,\n      prefixCls: prefixCls\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref,\n    id: id,\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(tabPosition), _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-mobile\"), mobile), \"\".concat(prefixCls, \"-editable\"), editable), \"\".concat(prefixCls, \"-rtl\"), rtl), className)\n  }, restProps), /*#__PURE__*/React.createElement(TabNavListWrapper, _extends({}, tabNavBarProps, {\n    renderTabBar: renderTabBar\n  })), /*#__PURE__*/React.createElement(TabPanelList, _extends({\n    destroyInactiveTabPane: destroyInactiveTabPane\n  }, sharedProps, {\n    animated: mergedAnimated\n  }))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Tabs.displayName = 'Tabs';\n}\nexport default Tabs;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectSpread", "_slicedToArray", "_typeof", "_objectWithoutProperties", "_excluded", "classNames", "useMergedState", "isMobile", "React", "useEffect", "useState", "TabContext", "TabNavListWrapper", "TabPanelList", "useAnimateConfig", "uuid", "Tabs", "forwardRef", "props", "ref", "id", "_props$prefixCls", "prefixCls", "className", "items", "direction", "active<PERSON><PERSON>", "defaultActiveKey", "editable", "animated", "_props$tabPosition", "tabPosition", "tabBarGutter", "tabBarStyle", "tabBarExtraContent", "locale", "more", "destroyInactiveTabPane", "renderTabBar", "onChange", "onTabClick", "onTabScroll", "getPopupContainer", "popupClassName", "indicator", "restProps", "tabs", "useMemo", "filter", "item", "rtl", "mergedAnimated", "_useState", "_useState2", "mobile", "setMobile", "_useMergedState", "_tabs$", "key", "value", "defaultValue", "_useMergedState2", "mergedActiveKey", "setMergedActiveKey", "_useState3", "findIndex", "tab", "_useState4", "activeIndex", "setActiveIndex", "newActiveIndex", "_tabs$newActiveIndex", "Math", "max", "min", "length", "map", "join", "_useMergedState3", "_useMergedState4", "mergedId", "setMergedId", "concat", "process", "env", "NODE_ENV", "onInternalTabClick", "e", "isActiveChanged", "sharedProps", "tabNavBarProps", "extra", "style", "panes", "createElement", "Provider", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-tabs/es/Tabs.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"id\", \"prefixCls\", \"className\", \"items\", \"direction\", \"activeKey\", \"defaultActiveKey\", \"editable\", \"animated\", \"tabPosition\", \"tabBarGutter\", \"tabBarStyle\", \"tabBarExtraContent\", \"locale\", \"more\", \"destroyInactiveTabPane\", \"renderTabBar\", \"onChange\", \"onTabClick\", \"onTabScroll\", \"getPopupContainer\", \"popupClassName\", \"indicator\"];\n// Accessibility https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/Tab_Role\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport isMobile from \"rc-util/es/isMobile\";\nimport * as React from 'react';\nimport { useEffect, useState } from 'react';\nimport TabContext from \"./TabContext\";\nimport TabNavListWrapper from \"./TabNavList/Wrapper\";\nimport TabPanelList from \"./TabPanelList\";\nimport useAnimateConfig from \"./hooks/useAnimateConfig\";\n/**\n * Should added antd:\n * - type\n *\n * Removed:\n * - onNextClick\n * - onPrevClick\n * - keyboard\n */\n\n// Used for accessibility\nvar uuid = 0;\nvar Tabs = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-tabs' : _props$prefixCls,\n    className = props.className,\n    items = props.items,\n    direction = props.direction,\n    activeKey = props.activeKey,\n    defaultActiveKey = props.defaultActiveKey,\n    editable = props.editable,\n    animated = props.animated,\n    _props$tabPosition = props.tabPosition,\n    tabPosition = _props$tabPosition === void 0 ? 'top' : _props$tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    tabBarStyle = props.tabBarStyle,\n    tabBarExtraContent = props.tabBarExtraContent,\n    locale = props.locale,\n    more = props.more,\n    destroyInactiveTabPane = props.destroyInactiveTabPane,\n    renderTabBar = props.renderTabBar,\n    onChange = props.onChange,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll,\n    getPopupContainer = props.getPopupContainer,\n    popupClassName = props.popupClassName,\n    indicator = props.indicator,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var tabs = React.useMemo(function () {\n    return (items || []).filter(function (item) {\n      return item && _typeof(item) === 'object' && 'key' in item;\n    });\n  }, [items]);\n  var rtl = direction === 'rtl';\n  var mergedAnimated = useAnimateConfig(animated);\n\n  // ======================== Mobile ========================\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    mobile = _useState2[0],\n    setMobile = _useState2[1];\n  useEffect(function () {\n    // Only update on the client side\n    setMobile(isMobile());\n  }, []);\n\n  // ====================== Active Key ======================\n  var _useMergedState = useMergedState(function () {\n      var _tabs$;\n      return (_tabs$ = tabs[0]) === null || _tabs$ === void 0 ? void 0 : _tabs$.key;\n    }, {\n      value: activeKey,\n      defaultValue: defaultActiveKey\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedActiveKey = _useMergedState2[0],\n    setMergedActiveKey = _useMergedState2[1];\n  var _useState3 = useState(function () {\n      return tabs.findIndex(function (tab) {\n        return tab.key === mergedActiveKey;\n      });\n    }),\n    _useState4 = _slicedToArray(_useState3, 2),\n    activeIndex = _useState4[0],\n    setActiveIndex = _useState4[1];\n\n  // Reset active key if not exist anymore\n  useEffect(function () {\n    var newActiveIndex = tabs.findIndex(function (tab) {\n      return tab.key === mergedActiveKey;\n    });\n    if (newActiveIndex === -1) {\n      var _tabs$newActiveIndex;\n      newActiveIndex = Math.max(0, Math.min(activeIndex, tabs.length - 1));\n      setMergedActiveKey((_tabs$newActiveIndex = tabs[newActiveIndex]) === null || _tabs$newActiveIndex === void 0 ? void 0 : _tabs$newActiveIndex.key);\n    }\n    setActiveIndex(newActiveIndex);\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), mergedActiveKey, activeIndex]);\n\n  // ===================== Accessibility ====================\n  var _useMergedState3 = useMergedState(null, {\n      value: id\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedId = _useMergedState4[0],\n    setMergedId = _useMergedState4[1];\n\n  // Async generate id to avoid ssr mapping failed\n  useEffect(function () {\n    if (!id) {\n      setMergedId(\"rc-tabs-\".concat(process.env.NODE_ENV === 'test' ? 'test' : uuid));\n      uuid += 1;\n    }\n  }, []);\n\n  // ======================== Events ========================\n  function onInternalTabClick(key, e) {\n    onTabClick === null || onTabClick === void 0 || onTabClick(key, e);\n    var isActiveChanged = key !== mergedActiveKey;\n    setMergedActiveKey(key);\n    if (isActiveChanged) {\n      onChange === null || onChange === void 0 || onChange(key);\n    }\n  }\n\n  // ======================== Render ========================\n  var sharedProps = {\n    id: mergedId,\n    activeKey: mergedActiveKey,\n    animated: mergedAnimated,\n    tabPosition: tabPosition,\n    rtl: rtl,\n    mobile: mobile\n  };\n  var tabNavBarProps = _objectSpread(_objectSpread({}, sharedProps), {}, {\n    editable: editable,\n    locale: locale,\n    more: more,\n    tabBarGutter: tabBarGutter,\n    onTabClick: onInternalTabClick,\n    onTabScroll: onTabScroll,\n    extra: tabBarExtraContent,\n    style: tabBarStyle,\n    panes: null,\n    getPopupContainer: getPopupContainer,\n    popupClassName: popupClassName,\n    indicator: indicator\n  });\n  return /*#__PURE__*/React.createElement(TabContext.Provider, {\n    value: {\n      tabs: tabs,\n      prefixCls: prefixCls\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", _extends({\n    ref: ref,\n    id: id,\n    className: classNames(prefixCls, \"\".concat(prefixCls, \"-\").concat(tabPosition), _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-mobile\"), mobile), \"\".concat(prefixCls, \"-editable\"), editable), \"\".concat(prefixCls, \"-rtl\"), rtl), className)\n  }, restProps), /*#__PURE__*/React.createElement(TabNavListWrapper, _extends({}, tabNavBarProps, {\n    renderTabBar: renderTabBar\n  })), /*#__PURE__*/React.createElement(TabPanelList, _extends({\n    destroyInactiveTabPane: destroyInactiveTabPane\n  }, sharedProps, {\n    animated: mergedAnimated\n  }))));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Tabs.displayName = 'Tabs';\n}\nexport default Tabs;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE,oBAAoB,EAAE,QAAQ,EAAE,MAAM,EAAE,wBAAwB,EAAE,cAAc,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,WAAW,CAAC;AAC5V;AACA,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAIC,IAAI,GAAG,CAAC;AACZ,IAAIC,IAAI,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC7D,IAAIC,EAAE,GAAGF,KAAK,CAACE,EAAE;IACfC,gBAAgB,GAAGH,KAAK,CAACI,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,gBAAgB;IACtEE,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,SAAS,GAAGP,KAAK,CAACO,SAAS;IAC3BC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,gBAAgB,GAAGT,KAAK,CAACS,gBAAgB;IACzCC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,QAAQ,GAAGX,KAAK,CAACW,QAAQ;IACzBC,kBAAkB,GAAGZ,KAAK,CAACa,WAAW;IACtCA,WAAW,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,kBAAkB;IACxEE,YAAY,GAAGd,KAAK,CAACc,YAAY;IACjCC,WAAW,GAAGf,KAAK,CAACe,WAAW;IAC/BC,kBAAkB,GAAGhB,KAAK,CAACgB,kBAAkB;IAC7CC,MAAM,GAAGjB,KAAK,CAACiB,MAAM;IACrBC,IAAI,GAAGlB,KAAK,CAACkB,IAAI;IACjBC,sBAAsB,GAAGnB,KAAK,CAACmB,sBAAsB;IACrDC,YAAY,GAAGpB,KAAK,CAACoB,YAAY;IACjCC,QAAQ,GAAGrB,KAAK,CAACqB,QAAQ;IACzBC,UAAU,GAAGtB,KAAK,CAACsB,UAAU;IAC7BC,WAAW,GAAGvB,KAAK,CAACuB,WAAW;IAC/BC,iBAAiB,GAAGxB,KAAK,CAACwB,iBAAiB;IAC3CC,cAAc,GAAGzB,KAAK,CAACyB,cAAc;IACrCC,SAAS,GAAG1B,KAAK,CAAC0B,SAAS;IAC3BC,SAAS,GAAG1C,wBAAwB,CAACe,KAAK,EAAEd,SAAS,CAAC;EACxD,IAAI0C,IAAI,GAAGtC,KAAK,CAACuC,OAAO,CAAC,YAAY;IACnC,OAAO,CAACvB,KAAK,IAAI,EAAE,EAAEwB,MAAM,CAAC,UAAUC,IAAI,EAAE;MAC1C,OAAOA,IAAI,IAAI/C,OAAO,CAAC+C,IAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,IAAIA,IAAI;IAC5D,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzB,KAAK,CAAC,CAAC;EACX,IAAI0B,GAAG,GAAGzB,SAAS,KAAK,KAAK;EAC7B,IAAI0B,cAAc,GAAGrC,gBAAgB,CAACe,QAAQ,CAAC;;EAE/C;EACA,IAAIuB,SAAS,GAAG1C,QAAQ,CAAC,KAAK,CAAC;IAC7B2C,UAAU,GAAGpD,cAAc,CAACmD,SAAS,EAAE,CAAC,CAAC;IACzCE,MAAM,GAAGD,UAAU,CAAC,CAAC,CAAC;IACtBE,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC3B5C,SAAS,CAAC,YAAY;IACpB;IACA8C,SAAS,CAAChD,QAAQ,CAAC,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIiD,eAAe,GAAGlD,cAAc,CAAC,YAAY;MAC7C,IAAImD,MAAM;MACV,OAAO,CAACA,MAAM,GAAGX,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIW,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACC,GAAG;IAC/E,CAAC,EAAE;MACDC,KAAK,EAAEjC,SAAS;MAChBkC,YAAY,EAAEjC;IAChB,CAAC,CAAC;IACFkC,gBAAgB,GAAG5D,cAAc,CAACuD,eAAe,EAAE,CAAC,CAAC;IACrDM,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,kBAAkB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC1C,IAAIG,UAAU,GAAGtD,QAAQ,CAAC,YAAY;MAClC,OAAOoC,IAAI,CAACmB,SAAS,CAAC,UAAUC,GAAG,EAAE;QACnC,OAAOA,GAAG,CAACR,GAAG,KAAKI,eAAe;MACpC,CAAC,CAAC;IACJ,CAAC,CAAC;IACFK,UAAU,GAAGlE,cAAc,CAAC+D,UAAU,EAAE,CAAC,CAAC;IAC1CI,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC3BE,cAAc,GAAGF,UAAU,CAAC,CAAC,CAAC;;EAEhC;EACA1D,SAAS,CAAC,YAAY;IACpB,IAAI6D,cAAc,GAAGxB,IAAI,CAACmB,SAAS,CAAC,UAAUC,GAAG,EAAE;MACjD,OAAOA,GAAG,CAACR,GAAG,KAAKI,eAAe;IACpC,CAAC,CAAC;IACF,IAAIQ,cAAc,KAAK,CAAC,CAAC,EAAE;MACzB,IAAIC,oBAAoB;MACxBD,cAAc,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACN,WAAW,EAAEtB,IAAI,CAAC6B,MAAM,GAAG,CAAC,CAAC,CAAC;MACpEZ,kBAAkB,CAAC,CAACQ,oBAAoB,GAAGzB,IAAI,CAACwB,cAAc,CAAC,MAAM,IAAI,IAAIC,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACb,GAAG,CAAC;IACnJ;IACAW,cAAc,CAACC,cAAc,CAAC;EAChC,CAAC,EAAE,CAACxB,IAAI,CAAC8B,GAAG,CAAC,UAAUV,GAAG,EAAE;IAC1B,OAAOA,GAAG,CAACR,GAAG;EAChB,CAAC,CAAC,CAACmB,IAAI,CAAC,GAAG,CAAC,EAAEf,eAAe,EAAEM,WAAW,CAAC,CAAC;;EAE5C;EACA,IAAIU,gBAAgB,GAAGxE,cAAc,CAAC,IAAI,EAAE;MACxCqD,KAAK,EAAEvC;IACT,CAAC,CAAC;IACF2D,gBAAgB,GAAG9E,cAAc,CAAC6E,gBAAgB,EAAE,CAAC,CAAC;IACtDE,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAEnC;EACAtE,SAAS,CAAC,YAAY;IACpB,IAAI,CAACW,EAAE,EAAE;MACP6D,WAAW,CAAC,UAAU,CAACC,MAAM,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,GAAG,MAAM,GAAGtE,IAAI,CAAC,CAAC;MAC/EA,IAAI,IAAI,CAAC;IACX;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,SAASuE,kBAAkBA,CAAC5B,GAAG,EAAE6B,CAAC,EAAE;IAClC/C,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,CAACkB,GAAG,EAAE6B,CAAC,CAAC;IAClE,IAAIC,eAAe,GAAG9B,GAAG,KAAKI,eAAe;IAC7CC,kBAAkB,CAACL,GAAG,CAAC;IACvB,IAAI8B,eAAe,EAAE;MACnBjD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACmB,GAAG,CAAC;IAC3D;EACF;;EAEA;EACA,IAAI+B,WAAW,GAAG;IAChBrE,EAAE,EAAE4D,QAAQ;IACZtD,SAAS,EAAEoC,eAAe;IAC1BjC,QAAQ,EAAEsB,cAAc;IACxBpB,WAAW,EAAEA,WAAW;IACxBmB,GAAG,EAAEA,GAAG;IACRI,MAAM,EAAEA;EACV,CAAC;EACD,IAAIoC,cAAc,GAAG1F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEyF,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;IACrE7D,QAAQ,EAAEA,QAAQ;IAClBO,MAAM,EAAEA,MAAM;IACdC,IAAI,EAAEA,IAAI;IACVJ,YAAY,EAAEA,YAAY;IAC1BQ,UAAU,EAAE8C,kBAAkB;IAC9B7C,WAAW,EAAEA,WAAW;IACxBkD,KAAK,EAAEzD,kBAAkB;IACzB0D,KAAK,EAAE3D,WAAW;IAClB4D,KAAK,EAAE,IAAI;IACXnD,iBAAiB,EAAEA,iBAAiB;IACpCC,cAAc,EAAEA,cAAc;IAC9BC,SAAS,EAAEA;EACb,CAAC,CAAC;EACF,OAAO,aAAapC,KAAK,CAACsF,aAAa,CAACnF,UAAU,CAACoF,QAAQ,EAAE;IAC3DpC,KAAK,EAAE;MACLb,IAAI,EAAEA,IAAI;MACVxB,SAAS,EAAEA;IACb;EACF,CAAC,EAAE,aAAad,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAEhG,QAAQ,CAAC;IAClDqB,GAAG,EAAEA,GAAG;IACRC,EAAE,EAAEA,EAAE;IACNG,SAAS,EAAElB,UAAU,CAACiB,SAAS,EAAE,EAAE,CAAC4D,MAAM,CAAC5D,SAAS,EAAE,GAAG,CAAC,CAAC4D,MAAM,CAACnD,WAAW,CAAC,EAAEhC,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACmF,MAAM,CAAC5D,SAAS,EAAE,SAAS,CAAC,EAAEgC,MAAM,CAAC,EAAE,EAAE,CAAC4B,MAAM,CAAC5D,SAAS,EAAE,WAAW,CAAC,EAAEM,QAAQ,CAAC,EAAE,EAAE,CAACsD,MAAM,CAAC5D,SAAS,EAAE,MAAM,CAAC,EAAE4B,GAAG,CAAC,EAAE3B,SAAS;EAC3Q,CAAC,EAAEsB,SAAS,CAAC,EAAE,aAAarC,KAAK,CAACsF,aAAa,CAAClF,iBAAiB,EAAEd,QAAQ,CAAC,CAAC,CAAC,EAAE4F,cAAc,EAAE;IAC9FpD,YAAY,EAAEA;EAChB,CAAC,CAAC,CAAC,EAAE,aAAa9B,KAAK,CAACsF,aAAa,CAACjF,YAAY,EAAEf,QAAQ,CAAC;IAC3DuC,sBAAsB,EAAEA;EAC1B,CAAC,EAAEoD,WAAW,EAAE;IACd5D,QAAQ,EAAEsB;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AACF,IAAIgC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCrE,IAAI,CAACgF,WAAW,GAAG,MAAM;AAC3B;AACA,eAAehF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}