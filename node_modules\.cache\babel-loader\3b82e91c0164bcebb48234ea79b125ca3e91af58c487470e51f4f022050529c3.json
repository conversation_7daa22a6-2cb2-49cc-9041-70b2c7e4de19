{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { RadioOptionTypeContextProvider } from './context';\nimport Radio from './radio';\nconst RadioButton = (props, ref) => {\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const {\n      prefixCls: customizePrefixCls\n    } = props,\n    radioProps = __rest(props, [\"prefixCls\"]);\n  const prefixCls = getPrefixCls('radio', customizePrefixCls);\n  return /*#__PURE__*/React.createElement(RadioOptionTypeContextProvider, {\n    value: \"button\"\n  }, /*#__PURE__*/React.createElement(Radio, Object.assign({\n    prefixCls: prefixCls\n  }, radioProps, {\n    type: \"radio\",\n    ref: ref\n  })));\n};\nexport default /*#__PURE__*/React.forwardRef(RadioButton);", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "ConfigContext", "RadioOptionTypeContextProvider", "Radio", "RadioButton", "props", "ref", "getPrefixCls", "useContext", "prefixCls", "customizePrefixCls", "radioProps", "createElement", "value", "assign", "type", "forwardRef"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/radio/radioButton.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport { ConfigContext } from '../config-provider';\nimport { RadioOptionTypeContextProvider } from './context';\nimport Radio from './radio';\nconst RadioButton = (props, ref) => {\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const {\n      prefixCls: customizePrefixCls\n    } = props,\n    radioProps = __rest(props, [\"prefixCls\"]);\n  const prefixCls = getPrefixCls('radio', customizePrefixCls);\n  return /*#__PURE__*/React.createElement(RadioOptionTypeContextProvider, {\n    value: \"button\"\n  }, /*#__PURE__*/React.createElement(Radio, Object.assign({\n    prefixCls: prefixCls\n  }, radioProps, {\n    type: \"radio\",\n    ref: ref\n  })));\n};\nexport default /*#__PURE__*/React.forwardRef(RadioButton);"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,8BAA8B,QAAQ,WAAW;AAC1D,OAAOC,KAAK,MAAM,SAAS;AAC3B,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;EAClC,MAAM;IACJC;EACF,CAAC,GAAGP,KAAK,CAACQ,UAAU,CAACP,aAAa,CAAC;EACnC,MAAM;MACFQ,SAAS,EAAEC;IACb,CAAC,GAAGL,KAAK;IACTM,UAAU,GAAGzB,MAAM,CAACmB,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;EAC3C,MAAMI,SAAS,GAAGF,YAAY,CAAC,OAAO,EAAEG,kBAAkB,CAAC;EAC3D,OAAO,aAAaV,KAAK,CAACY,aAAa,CAACV,8BAA8B,EAAE;IACtEW,KAAK,EAAE;EACT,CAAC,EAAE,aAAab,KAAK,CAACY,aAAa,CAACT,KAAK,EAAEZ,MAAM,CAACuB,MAAM,CAAC;IACvDL,SAAS,EAAEA;EACb,CAAC,EAAEE,UAAU,EAAE;IACbI,IAAI,EAAE,OAAO;IACbT,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,eAAe,aAAaN,KAAK,CAACgB,UAAU,CAACZ,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}