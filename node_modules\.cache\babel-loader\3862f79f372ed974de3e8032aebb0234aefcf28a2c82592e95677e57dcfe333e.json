{"ast": null, "code": "function compactItemVerticalBorder(token, parentCls) {\n  return {\n    // border collapse\n    [`&-item:not(${parentCls}-last-item)`]: {\n      marginBottom: token.calc(token.lineWidth).mul(-1).equal()\n    },\n    '&-item': {\n      '&:hover,&:focus,&:active': {\n        zIndex: 2\n      },\n      '&[disabled]': {\n        zIndex: 0\n      }\n    }\n  };\n}\nfunction compactItemBorderVerticalRadius(prefixCls, parentCls) {\n  return {\n    [`&-item:not(${parentCls}-first-item):not(${parentCls}-last-item)`]: {\n      borderRadius: 0\n    },\n    [`&-item${parentCls}-first-item:not(${parentCls}-last-item)`]: {\n      [`&, &${prefixCls}-sm, &${prefixCls}-lg`]: {\n        borderEndEndRadius: 0,\n        borderEndStartRadius: 0\n      }\n    },\n    [`&-item${parentCls}-last-item:not(${parentCls}-first-item)`]: {\n      [`&, &${prefixCls}-sm, &${prefixCls}-lg`]: {\n        borderStartStartRadius: 0,\n        borderStartEndRadius: 0\n      }\n    }\n  };\n}\nexport function genCompactItemVerticalStyle(token) {\n  const compactCls = `${token.componentCls}-compact-vertical`;\n  return {\n    [compactCls]: Object.assign(Object.assign({}, compactItemVerticalBorder(token, compactCls)), compactItemBorderVerticalRadius(token.componentCls, compactCls))\n  };\n}", "map": {"version": 3, "names": ["compactItemVerticalBorder", "token", "parentCls", "marginBottom", "calc", "lineWidth", "mul", "equal", "zIndex", "compactItemBorderVerticalRadius", "prefixCls", "borderRadius", "borderEndEndRadius", "borderEndStartRadius", "borderStartStartRadius", "borderStartEndRadius", "genCompactItemVerticalStyle", "compactCls", "componentCls", "Object", "assign"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/style/compact-item-vertical.js"], "sourcesContent": ["function compactItemVerticalBorder(token, parentCls) {\n  return {\n    // border collapse\n    [`&-item:not(${parentCls}-last-item)`]: {\n      marginBottom: token.calc(token.lineWidth).mul(-1).equal()\n    },\n    '&-item': {\n      '&:hover,&:focus,&:active': {\n        zIndex: 2\n      },\n      '&[disabled]': {\n        zIndex: 0\n      }\n    }\n  };\n}\nfunction compactItemBorderVerticalRadius(prefixCls, parentCls) {\n  return {\n    [`&-item:not(${parentCls}-first-item):not(${parentCls}-last-item)`]: {\n      borderRadius: 0\n    },\n    [`&-item${parentCls}-first-item:not(${parentCls}-last-item)`]: {\n      [`&, &${prefixCls}-sm, &${prefixCls}-lg`]: {\n        borderEndEndRadius: 0,\n        borderEndStartRadius: 0\n      }\n    },\n    [`&-item${parentCls}-last-item:not(${parentCls}-first-item)`]: {\n      [`&, &${prefixCls}-sm, &${prefixCls}-lg`]: {\n        borderStartStartRadius: 0,\n        borderStartEndRadius: 0\n      }\n    }\n  };\n}\nexport function genCompactItemVerticalStyle(token) {\n  const compactCls = `${token.componentCls}-compact-vertical`;\n  return {\n    [compactCls]: Object.assign(Object.assign({}, compactItemVerticalBorder(token, compactCls)), compactItemBorderVerticalRadius(token.componentCls, compactCls))\n  };\n}"], "mappings": "AAAA,SAASA,yBAAyBA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACnD,OAAO;IACL;IACA,CAAC,cAAcA,SAAS,aAAa,GAAG;MACtCC,YAAY,EAAEF,KAAK,CAACG,IAAI,CAACH,KAAK,CAACI,SAAS,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;IAC1D,CAAC;IACD,QAAQ,EAAE;MACR,0BAA0B,EAAE;QAC1BC,MAAM,EAAE;MACV,CAAC;MACD,aAAa,EAAE;QACbA,MAAM,EAAE;MACV;IACF;EACF,CAAC;AACH;AACA,SAASC,+BAA+BA,CAACC,SAAS,EAAER,SAAS,EAAE;EAC7D,OAAO;IACL,CAAC,cAAcA,SAAS,oBAAoBA,SAAS,aAAa,GAAG;MACnES,YAAY,EAAE;IAChB,CAAC;IACD,CAAC,SAAST,SAAS,mBAAmBA,SAAS,aAAa,GAAG;MAC7D,CAAC,OAAOQ,SAAS,SAASA,SAAS,KAAK,GAAG;QACzCE,kBAAkB,EAAE,CAAC;QACrBC,oBAAoB,EAAE;MACxB;IACF,CAAC;IACD,CAAC,SAASX,SAAS,kBAAkBA,SAAS,cAAc,GAAG;MAC7D,CAAC,OAAOQ,SAAS,SAASA,SAAS,KAAK,GAAG;QACzCI,sBAAsB,EAAE,CAAC;QACzBC,oBAAoB,EAAE;MACxB;IACF;EACF,CAAC;AACH;AACA,OAAO,SAASC,2BAA2BA,CAACf,KAAK,EAAE;EACjD,MAAMgB,UAAU,GAAG,GAAGhB,KAAK,CAACiB,YAAY,mBAAmB;EAC3D,OAAO;IACL,CAACD,UAAU,GAAGE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpB,yBAAyB,CAACC,KAAK,EAAEgB,UAAU,CAAC,CAAC,EAAER,+BAA+B,CAACR,KAAK,CAACiB,YAAY,EAAED,UAAU,CAAC;EAC9J,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}