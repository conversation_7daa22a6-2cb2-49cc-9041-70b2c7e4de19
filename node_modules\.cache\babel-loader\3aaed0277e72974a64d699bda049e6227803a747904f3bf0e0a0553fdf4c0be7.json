{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\nlet trafficParticipants = new Map(); // 使用 Map 存储所有交通参与者\nlet participantMeshes = new Map(); // 存储交通参与者的 3D 模型\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: '************',\n  port: 11886,\n  username: 'cloud',\n  password: 'A825FBCBB97D1975',\n  clientId: 'V2X_CENTER_CSLG_opentest',\n  topics: ['changli/cloud/v2x/rsu/rsm', 'changli/cloud/v2x/obu/bsm']\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改初始化 MQTT 连接\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const client = mqtt.connect('mqtt://************:11886', {\n      clientId: MQTT_CONFIG.clientId,\n      username: MQTT_CONFIG.username,\n      password: MQTT_CONFIG.password,\n      protocol: 'mqtt',\n      clean: true,\n      connectTimeout: 4000,\n      reconnectPeriod: 1000,\n      rejectUnauthorized: false\n    });\n    client.on('connect', () => {\n      console.log('MQTT连接成功');\n      // 订阅多个主题\n      MQTT_CONFIG.topics.forEach(topic => {\n        client.subscribe(topic, err => {\n          if (err) {\n            console.error(`MQTT订阅失败 ${topic}:`, err);\n          } else {\n            console.log(`MQTT订阅成功: ${topic}`);\n          }\n        });\n      });\n    });\n    client.on('message', (topic, message) => {\n      console.log('收到原始消息:', {\n        topic,\n        message: message.toString()\n      });\n      try {\n        const data = JSON.parse(message.toString());\n        console.log('收到MQTT消息:', data);\n\n        // 根据不同的主题处理不同类型的消息\n        switch (topic) {\n          case 'changli/cloud/v2x/obu/bsm':\n            handleBSMMessage(data);\n            break;\n          case 'changli/cloud/v2x/rsu/rsm':\n            handleRSMMessage(data);\n            break;\n          default:\n            console.log('未处理的主题:', topic);\n        }\n      } catch (error) {\n        console.error('处理MQTT消息失败:', error);\n        console.error('原始消息内容:', message.toString());\n      }\n    });\n    client.on('error', err => {\n      console.error('MQTT错误:', err.message);\n      console.error('错误详情:', err);\n    });\n    client.on('close', () => {\n      console.log('MQTT连接已关闭, 原因:', {\n        connected: client.connected,\n        reconnecting: client.reconnecting,\n        clientId: client.options.clientId\n      });\n    });\n    client.on('offline', () => {\n      console.log('MQTT连接已离线');\n    });\n    client.on('reconnect', () => {\n      console.log('MQTT正在重新连接...', {\n        broker: MQTT_CONFIG.broker,\n        port: MQTT_CONFIG.port,\n        clientId: MQTT_CONFIG.clientId\n      });\n    });\n    console.log('MQTT连接选项:', {\n      url: `mqtt://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}`,\n      clientId: MQTT_CONFIG.clientId,\n      username: MQTT_CONFIG.username,\n      protocol: 'mqtt'\n    });\n    mqttClientRef.current = client;\n  };\n\n  // 处理 BSM 消息\n  const handleBSMMessage = data => {\n    if (data.type === 'BSM' && data.data) {\n      const bsmData = data.data;\n      const newState = {\n        longitude: parseFloat(bsmData.partLong),\n        latitude: parseFloat(bsmData.partLat),\n        speed: parseFloat(bsmData.partSpeed),\n        heading: parseFloat(bsmData.partHeading)\n      };\n      console.log('解析后的车辆状态:', newState);\n\n      // 更新车辆位置和状态\n      if (globalVehicleRef) {\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        globalVehicleRef.position.copy(newPosition);\n        globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n        globalVehicleRef.updateMatrix();\n        globalVehicleRef.updateMatrixWorld(true);\n        setVehicleState(newState);\n        console.log('车辆位置已更新:', newPosition);\n      }\n    }\n  };\n\n  // 处理 RSM 消息\n  const handleRSMMessage = data => {\n    if (data.type === 'RSM' && data.data) {\n      var _rsmData$participants;\n      const rsmData = data.data;\n      console.log('收到RSM数据:', {\n        设备ID: rsmData.rsuId,\n        基准点: {\n          经度: rsmData.posLong,\n          纬度: rsmData.posLat\n        },\n        参与者数量: ((_rsmData$participants = rsmData.participants) === null || _rsmData$participants === void 0 ? void 0 : _rsmData$participants.length) || 0\n      });\n\n      // 处理交通参与者\n      if (rsmData.participants && Array.isArray(rsmData.participants)) {\n        rsmData.participants.forEach(participant => {\n          updateTrafficParticipant(participant);\n        });\n      }\n    }\n  };\n\n  // 更新交通参与者\n  const updateTrafficParticipant = participant => {\n    const id = participant.partPtcId;\n    const type = parseInt(participant.partPtcType);\n\n    // 转换坐标\n    const modelPos = converter.current.wgs84ToModel(parseFloat(participant.partPosLong), parseFloat(participant.partPosLat));\n\n    // 创建或更新参与者数据\n    const participantData = {\n      position: new THREE.Vector3(modelPos.x, 0.5, -modelPos.y),\n      heading: parseFloat(participant.partHeading),\n      speed: parseFloat(participant.partSpeed),\n      type: type,\n      size: {\n        length: parseFloat(participant.partLength) / 100,\n        // 转换为米\n        width: parseFloat(participant.partWidth) / 100,\n        height: parseFloat(participant.partHeight) / 100\n      },\n      lastUpdate: Date.now()\n    };\n\n    // 更新或创建 3D 模型\n    if (!participantMeshes.has(id)) {\n      // 创建新的 3D 模型\n      const mesh = createParticipantMesh(participantData);\n      if (mesh) {\n        scene.add(mesh);\n        participantMeshes.set(id, mesh);\n      }\n    } else {\n      // 更新现有模型\n      const mesh = participantMeshes.get(id);\n      updateParticipantMesh(mesh, participantData);\n    }\n\n    // 更新数据存储\n    trafficParticipants.set(id, participantData);\n  };\n\n  // 创建交通参与者的 3D 模型\n  const createParticipantMesh = data => {\n    let geometry, material;\n    switch (data.type) {\n      case 1:\n        // 机动车\n        geometry = new THREE.BoxGeometry(data.size.length, data.size.height, data.size.width);\n        material = new THREE.MeshPhongMaterial({\n          color: 0x4444ff\n        });\n        break;\n      case 2:\n        // 非机动车\n        geometry = new THREE.BoxGeometry(data.size.length, data.size.height, data.size.width);\n        material = new THREE.MeshPhongMaterial({\n          color: 0x44ff44\n        });\n        break;\n      case 3:\n        // 行人\n        geometry = new THREE.CylinderGeometry(0.3, 0.3, data.size.height, 8);\n        material = new THREE.MeshPhongMaterial({\n          color: 0xff4444\n        });\n        break;\n      default:\n        // 未知类型\n        geometry = new THREE.BoxGeometry(1, 1, 1);\n        material = new THREE.MeshPhongMaterial({\n          color: 0x888888\n        });\n    }\n    const mesh = new THREE.Mesh(geometry, material);\n    mesh.position.copy(data.position);\n    mesh.rotation.y = Math.PI - data.heading * Math.PI / 180;\n    return mesh;\n  };\n\n  // 更新交通参与者的 3D 模型\n  const updateParticipantMesh = (mesh, data) => {\n    // 使用 TWEEN 创建平滑过渡\n    new TWEEN.Tween(mesh.position).to(data.position, 100).easing(TWEEN.Easing.Linear.None).start();\n    new TWEEN.Tween(mesh.rotation).to({\n      y: Math.PI - data.heading * Math.PI / 180\n    }, 100).easing(TWEEN.Easing.Linear.None).start();\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    const scene = new THREE.Scene();\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n        scene.add(model);\n\n        // 在校园模型加载完成后初始化场景\n        await initializeScene();\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 15, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      controls.update();\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 清理函数\n    return () => {\n      var _containerRef$current;\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.end();\n      }\n      window.removeEventListener('resize', handleResize);\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.removeChild(renderer.domElement);\n      renderer.dispose();\n      // 清理所有参与者模型\n      participantMeshes.forEach(mesh => {\n        scene.remove(mesh);\n        mesh.geometry.dispose();\n        mesh.material.dispose();\n      });\n      participantMeshes.clear();\n      trafficParticipants.clear();\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 736,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 738,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 748,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 737,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"pok6xuZ9wkLpUfjRdkdxrFsX29M=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "trafficParticipants", "Map", "participant<PERSON><PERSON><PERSON>", "MQTT_CONFIG", "broker", "port", "username", "password", "clientId", "topics", "BASE_URL", "CampusModel", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "clone", "currentUp", "up", "Tween", "to", "x", "y", "z", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "Math", "PI", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "initMqttClient", "client", "connect", "protocol", "clean", "connectTimeout", "reconnectPeriod", "rejectUnauthorized", "on", "for<PERSON>ach", "topic", "subscribe", "err", "error", "message", "toString", "data", "JSON", "parse", "handleBSMMessage", "handleRSMMessage", "connected", "reconnecting", "options", "url", "type", "bsmData", "newState", "parseFloat", "partLong", "partLat", "partSpeed", "partHeading", "modelPos", "wgs84ToModel", "newPosition", "Vector3", "rotation", "updateMatrix", "updateMatrixWorld", "_rsmData$participants", "rsmData", "设备ID", "rsuId", "基准点", "经度", "posLong", "纬度", "posLat", "参与者数量", "participants", "length", "Array", "isArray", "participant", "updateTrafficParticipant", "id", "partPtcId", "parseInt", "partPtcType", "partPosLong", "partPosLat", "participantData", "size", "partLength", "width", "partWidth", "height", "partHeight", "lastUpdate", "Date", "now", "has", "mesh", "createParticipantMesh", "scene", "add", "set", "get", "updateParticipantMesh", "geometry", "material", "BoxGeometry", "MeshPhongMaterial", "color", "CylinderGeometry", "<PERSON><PERSON>", "Linear", "None", "Scene", "camera", "PerspectiveCamera", "window", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "traverse", "child", "<PERSON><PERSON><PERSON>", "newMaterial", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "map", "name", "children", "xhr", "loaded", "total", "toFixed", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "maxRetries", "attemptLoad", "retriesLeft", "loader", "setTimeout", "model", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "toArray", "相机位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "_containerRef$current", "clearInterval", "end", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "remove", "clear", "ref", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\nlet trafficParticipants = new Map();  // 使用 Map 存储所有交通参与者\nlet participantMeshes = new Map();    // 存储交通参与者的 3D 模型\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: '************',\n  port: 11886,\n  username: 'cloud',\n  password: 'A825FBCBB97D1975',\n  clientId: 'V2X_CENTER_CSLG_opentest',\n  topics: [\n    'changli/cloud/v2x/rsu/rsm',\n    'changli/cloud/v2x/obu/bsm'\n  ]\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改初始化 MQTT 连接\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const client = mqtt.connect('mqtt://************:11886', {\n      clientId: MQTT_CONFIG.clientId,\n      username: MQTT_CONFIG.username,\n      password: MQTT_CONFIG.password,\n      protocol: 'mqtt',\n      clean: true,\n      connectTimeout: 4000,\n      reconnectPeriod: 1000,\n      rejectUnauthorized: false\n    });\n    \n    client.on('connect', () => {\n      console.log('MQTT连接成功');\n      // 订阅多个主题\n      MQTT_CONFIG.topics.forEach(topic => {\n        client.subscribe(topic, (err) => {\n          if (err) {\n            console.error(`MQTT订阅失败 ${topic}:`, err);\n          } else {\n            console.log(`MQTT订阅成功: ${topic}`);\n          }\n        });\n      });\n    });\n    \n    client.on('message', (topic, message) => {\n      console.log('收到原始消息:', {\n        topic,\n        message: message.toString()\n      });\n      \n      try {\n        const data = JSON.parse(message.toString());\n        console.log('收到MQTT消息:', data);\n        \n        // 根据不同的主题处理不同类型的消息\n        switch(topic) {\n          case 'changli/cloud/v2x/obu/bsm':\n            handleBSMMessage(data);\n            break;\n          case 'changli/cloud/v2x/rsu/rsm':\n            handleRSMMessage(data);\n            break;\n          default:\n            console.log('未处理的主题:', topic);\n        }\n      } catch (error) {\n        console.error('处理MQTT消息失败:', error);\n        console.error('原始消息内容:', message.toString());\n      }\n    });\n\n    client.on('error', (err) => {\n      console.error('MQTT错误:', err.message);\n      console.error('错误详情:', err);\n    });\n    \n    client.on('close', () => {\n      console.log('MQTT连接已关闭, 原因:', {\n        connected: client.connected,\n        reconnecting: client.reconnecting,\n        clientId: client.options.clientId\n      });\n    });\n    \n    client.on('offline', () => {\n      console.log('MQTT连接已离线');\n    });\n    \n    client.on('reconnect', () => {\n      console.log('MQTT正在重新连接...', {\n        broker: MQTT_CONFIG.broker,\n        port: MQTT_CONFIG.port,\n        clientId: MQTT_CONFIG.clientId\n      });\n    });\n    \n    console.log('MQTT连接选项:', {\n      url: `mqtt://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}`,\n      clientId: MQTT_CONFIG.clientId,\n      username: MQTT_CONFIG.username,\n      protocol: 'mqtt'\n    });\n    \n    mqttClientRef.current = client;\n  };\n\n  // 处理 BSM 消息\n  const handleBSMMessage = (data) => {\n    if (data.type === 'BSM' && data.data) {\n      const bsmData = data.data;\n      const newState = {\n        longitude: parseFloat(bsmData.partLong),\n        latitude: parseFloat(bsmData.partLat),\n        speed: parseFloat(bsmData.partSpeed),\n        heading: parseFloat(bsmData.partHeading)\n      };\n      \n      console.log('解析后的车辆状态:', newState);\n      \n      // 更新车辆位置和状态\n      if (globalVehicleRef) {\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        \n        globalVehicleRef.position.copy(newPosition);\n        globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n        globalVehicleRef.updateMatrix();\n        globalVehicleRef.updateMatrixWorld(true);\n        \n        setVehicleState(newState);\n        console.log('车辆位置已更新:', newPosition);\n      }\n    }\n  };\n\n  // 处理 RSM 消息\n  const handleRSMMessage = (data) => {\n    if (data.type === 'RSM' && data.data) {\n      const rsmData = data.data;\n      console.log('收到RSM数据:', {\n        设备ID: rsmData.rsuId,\n        基准点: {\n          经度: rsmData.posLong,\n          纬度: rsmData.posLat\n        },\n        参与者数量: rsmData.participants?.length || 0\n      });\n\n      // 处理交通参与者\n      if (rsmData.participants && Array.isArray(rsmData.participants)) {\n        rsmData.participants.forEach(participant => {\n          updateTrafficParticipant(participant);\n        });\n      }\n    }\n  };\n\n  // 更新交通参与者\n  const updateTrafficParticipant = (participant) => {\n    const id = participant.partPtcId;\n    const type = parseInt(participant.partPtcType);\n    \n    // 转换坐标\n    const modelPos = converter.current.wgs84ToModel(\n      parseFloat(participant.partPosLong),\n      parseFloat(participant.partPosLat)\n    );\n    \n    // 创建或更新参与者数据\n    const participantData = {\n      position: new THREE.Vector3(modelPos.x, 0.5, -modelPos.y),\n      heading: parseFloat(participant.partHeading),\n      speed: parseFloat(participant.partSpeed),\n      type: type,\n      size: {\n        length: parseFloat(participant.partLength) / 100,  // 转换为米\n        width: parseFloat(participant.partWidth) / 100,\n        height: parseFloat(participant.partHeight) / 100\n      },\n      lastUpdate: Date.now()\n    };\n    \n    // 更新或创建 3D 模型\n    if (!participantMeshes.has(id)) {\n      // 创建新的 3D 模型\n      const mesh = createParticipantMesh(participantData);\n      if (mesh) {\n        scene.add(mesh);\n        participantMeshes.set(id, mesh);\n      }\n    } else {\n      // 更新现有模型\n      const mesh = participantMeshes.get(id);\n      updateParticipantMesh(mesh, participantData);\n    }\n    \n    // 更新数据存储\n    trafficParticipants.set(id, participantData);\n  };\n\n  // 创建交通参与者的 3D 模型\n  const createParticipantMesh = (data) => {\n    let geometry, material;\n    \n    switch (data.type) {\n      case 1:  // 机动车\n        geometry = new THREE.BoxGeometry(data.size.length, data.size.height, data.size.width);\n        material = new THREE.MeshPhongMaterial({ color: 0x4444ff });\n        break;\n      case 2:  // 非机动车\n        geometry = new THREE.BoxGeometry(data.size.length, data.size.height, data.size.width);\n        material = new THREE.MeshPhongMaterial({ color: 0x44ff44 });\n        break;\n      case 3:  // 行人\n        geometry = new THREE.CylinderGeometry(0.3, 0.3, data.size.height, 8);\n        material = new THREE.MeshPhongMaterial({ color: 0xff4444 });\n        break;\n      default:  // 未知类型\n        geometry = new THREE.BoxGeometry(1, 1, 1);\n        material = new THREE.MeshPhongMaterial({ color: 0x888888 });\n    }\n    \n    const mesh = new THREE.Mesh(geometry, material);\n    mesh.position.copy(data.position);\n    mesh.rotation.y = Math.PI - data.heading * Math.PI / 180;\n    \n    return mesh;\n  };\n\n  // 更新交通参与者的 3D 模型\n  const updateParticipantMesh = (mesh, data) => {\n    // 使用 TWEEN 创建平滑过渡\n    new TWEEN.Tween(mesh.position)\n      .to(data.position, 100)\n      .easing(TWEEN.Easing.Linear.None)\n      .start();\n    \n    new TWEEN.Tween(mesh.rotation)\n      .to({ y: Math.PI - data.heading * Math.PI / 180 }, 100)\n      .easing(TWEEN.Easing.Linear.None)\n      .start();\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    const scene = new THREE.Scene();\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          15,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      \n      controls.update();\n      renderer.render(scene, camera);\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 清理函数\n    return () => {\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.end();\n      }\n      window.removeEventListener('resize', handleResize);\n      containerRef.current?.removeChild(renderer.domElement);\n      renderer.dispose();\n      // 清理所有参与者模型\n      participantMeshes.forEach(mesh => {\n        scene.remove(mesh);\n        mesh.geometry.dispose();\n        mesh.material.dispose();\n      });\n      participantMeshes.clear();\n      trafficParticipants.clear();\n    };\n  }, []);\n\n  return (\n    <>\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;AACrB,IAAIC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAE;AACtC,IAAIC,iBAAiB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAI;;AAEtC;AACA,MAAME,WAAW,GAAG;EAClBC,MAAM,EAAE,cAAc;EACtBC,IAAI,EAAE,KAAK;EACXC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE,0BAA0B;EACpCC,MAAM,EAAE,CACN,2BAA2B,EAC3B,2BAA2B;AAE/B,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;AAExC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAGlC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMmC,UAAU,GAAGnC,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMoC,SAAS,GAAGpC,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMgC,aAAa,GAAGrC,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMsC,eAAe,GAAGtC,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMuC,aAAa,GAAGvC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC;IAC/C2C,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMiD,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGnE,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMoE,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnB,WAAW,CAAC,QAAQ,CAAC;IACrB9B,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACiD,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrB,WAAW,CAAC,QAAQ,CAAC;IACrB9B,UAAU,GAAG,QAAQ;IAErB,IAAIgD,SAAS,CAACI,OAAO,IAAInD,QAAQ,EAAE;MACjC;MACA,MAAMoD,UAAU,GAAGL,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACsB,KAAK,CAAC,CAAC;MACrD,MAAMC,SAAS,GAAGP,SAAS,CAACI,OAAO,CAACI,EAAE,CAACF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAInE,KAAK,CAACsE,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCC,MAAM,CAAC3E,KAAK,CAAC4E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdlB,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACmC,IAAI,CAACd,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDe,KAAK,CAAC,CAAC;;MAEV;MACA,IAAIjF,KAAK,CAACsE,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC3E,KAAK,CAAC4E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdlB,SAAS,CAACI,OAAO,CAACI,EAAE,CAACW,IAAI,CAACZ,SAAS,CAAC;MACtC,CAAC,CAAC,CACDa,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAGpE,QAAQ,CAACqE,MAAM,CAAChB,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAInE,KAAK,CAACsE,KAAK,CAACY,aAAa,CAAC,CAC3BX,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC3E,KAAK,CAAC4E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdjE,QAAQ,CAACqE,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACArB,SAAS,CAACI,OAAO,CAACmB,MAAM,CAACtE,QAAQ,CAACqE,MAAM,CAAC;QACzCrE,QAAQ,CAACuE,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACAnE,QAAQ,CAACiD,OAAO,GAAG,IAAI;;MAEvB;MACAjD,QAAQ,CAACwE,WAAW,GAAG,EAAE;MACzBxE,QAAQ,CAACyE,WAAW,GAAG,GAAG;MAC1BzE,QAAQ,CAAC0E,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;MACtC5E,QAAQ,CAAC6E,aAAa,GAAG,CAAC;MAC1B7E,QAAQ,CAACuE,MAAM,CAAC,CAAC;MAEjBO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BL,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,MAAMK,MAAM,GAAGjG,IAAI,CAACkG,OAAO,CAAC,2BAA2B,EAAE;MACvD5E,QAAQ,EAAEL,WAAW,CAACK,QAAQ;MAC9BF,QAAQ,EAAEH,WAAW,CAACG,QAAQ;MAC9BC,QAAQ,EAAEJ,WAAW,CAACI,QAAQ;MAC9B8E,QAAQ,EAAE,MAAM;MAChBC,KAAK,EAAE,IAAI;MACXC,cAAc,EAAE,IAAI;MACpBC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;IAEFN,MAAM,CAACO,EAAE,CAAC,SAAS,EAAE,MAAM;MACzBb,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACvB;MACA3E,WAAW,CAACM,MAAM,CAACkF,OAAO,CAACC,KAAK,IAAI;QAClCT,MAAM,CAACU,SAAS,CAACD,KAAK,EAAGE,GAAG,IAAK;UAC/B,IAAIA,GAAG,EAAE;YACPjB,OAAO,CAACkB,KAAK,CAAC,YAAYH,KAAK,GAAG,EAAEE,GAAG,CAAC;UAC1C,CAAC,MAAM;YACLjB,OAAO,CAACC,GAAG,CAAC,aAAac,KAAK,EAAE,CAAC;UACnC;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFT,MAAM,CAACO,EAAE,CAAC,SAAS,EAAE,CAACE,KAAK,EAAEI,OAAO,KAAK;MACvCnB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBc,KAAK;QACLI,OAAO,EAAEA,OAAO,CAACC,QAAQ,CAAC;MAC5B,CAAC,CAAC;MAEF,IAAI;QACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC;QAC3CpB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEoB,IAAI,CAAC;;QAE9B;QACA,QAAON,KAAK;UACV,KAAK,2BAA2B;YAC9BS,gBAAgB,CAACH,IAAI,CAAC;YACtB;UACF,KAAK,2BAA2B;YAC9BI,gBAAgB,CAACJ,IAAI,CAAC;YACtB;UACF;YACErB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEc,KAAK,CAAC;QACjC;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdlB,OAAO,CAACkB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;QACnClB,OAAO,CAACkB,KAAK,CAAC,SAAS,EAAEC,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC;MAC9C;IACF,CAAC,CAAC;IAEFd,MAAM,CAACO,EAAE,CAAC,OAAO,EAAGI,GAAG,IAAK;MAC1BjB,OAAO,CAACkB,KAAK,CAAC,SAAS,EAAED,GAAG,CAACE,OAAO,CAAC;MACrCnB,OAAO,CAACkB,KAAK,CAAC,OAAO,EAAED,GAAG,CAAC;IAC7B,CAAC,CAAC;IAEFX,MAAM,CAACO,EAAE,CAAC,OAAO,EAAE,MAAM;MACvBb,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;QAC5ByB,SAAS,EAAEpB,MAAM,CAACoB,SAAS;QAC3BC,YAAY,EAAErB,MAAM,CAACqB,YAAY;QACjChG,QAAQ,EAAE2E,MAAM,CAACsB,OAAO,CAACjG;MAC3B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF2E,MAAM,CAACO,EAAE,CAAC,SAAS,EAAE,MAAM;MACzBb,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IAC1B,CAAC,CAAC;IAEFK,MAAM,CAACO,EAAE,CAAC,WAAW,EAAE,MAAM;MAC3Bb,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE;QAC3B1E,MAAM,EAAED,WAAW,CAACC,MAAM;QAC1BC,IAAI,EAAEF,WAAW,CAACE,IAAI;QACtBG,QAAQ,EAAEL,WAAW,CAACK;MACxB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFqE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;MACvB4B,GAAG,EAAE,UAAUvG,WAAW,CAACC,MAAM,IAAID,WAAW,CAACE,IAAI,EAAE;MACvDG,QAAQ,EAAEL,WAAW,CAACK,QAAQ;MAC9BF,QAAQ,EAAEH,WAAW,CAACG,QAAQ;MAC9B+E,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEFnE,aAAa,CAACgC,OAAO,GAAGiC,MAAM;EAChC,CAAC;;EAED;EACA,MAAMkB,gBAAgB,GAAIH,IAAI,IAAK;IACjC,IAAIA,IAAI,CAACS,IAAI,KAAK,KAAK,IAAIT,IAAI,CAACA,IAAI,EAAE;MACpC,MAAMU,OAAO,GAAGV,IAAI,CAACA,IAAI;MACzB,MAAMW,QAAQ,GAAG;QACftF,SAAS,EAAEuF,UAAU,CAACF,OAAO,CAACG,QAAQ,CAAC;QACvCvF,QAAQ,EAAEsF,UAAU,CAACF,OAAO,CAACI,OAAO,CAAC;QACrCvF,KAAK,EAAEqF,UAAU,CAACF,OAAO,CAACK,SAAS,CAAC;QACpCvF,OAAO,EAAEoF,UAAU,CAACF,OAAO,CAACM,WAAW;MACzC,CAAC;MAEDrC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE+B,QAAQ,CAAC;;MAElC;MACA,IAAItH,gBAAgB,EAAE;QACpB,MAAM4H,QAAQ,GAAGpG,SAAS,CAACmC,OAAO,CAACkE,YAAY,CAACP,QAAQ,CAACtF,SAAS,EAAEsF,QAAQ,CAACrF,QAAQ,CAAC;QACtF,MAAM6F,WAAW,GAAG,IAAIxI,KAAK,CAACyI,OAAO,CAACH,QAAQ,CAAC1D,CAAC,EAAE,GAAG,EAAE,CAAC0D,QAAQ,CAACzD,CAAC,CAAC;QAEnEnE,gBAAgB,CAACuC,QAAQ,CAACmC,IAAI,CAACoD,WAAW,CAAC;QAC3C9H,gBAAgB,CAACgI,QAAQ,CAAC7D,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGkC,QAAQ,CAACnF,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAG;QACxEpF,gBAAgB,CAACiI,YAAY,CAAC,CAAC;QAC/BjI,gBAAgB,CAACkI,iBAAiB,CAAC,IAAI,CAAC;QAExCnG,eAAe,CAACuF,QAAQ,CAAC;QACzBhC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEuC,WAAW,CAAC;MACtC;IACF;EACF,CAAC;;EAED;EACA,MAAMf,gBAAgB,GAAIJ,IAAI,IAAK;IACjC,IAAIA,IAAI,CAACS,IAAI,KAAK,KAAK,IAAIT,IAAI,CAACA,IAAI,EAAE;MAAA,IAAAwB,qBAAA;MACpC,MAAMC,OAAO,GAAGzB,IAAI,CAACA,IAAI;MACzBrB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;QACtB8C,IAAI,EAAED,OAAO,CAACE,KAAK;QACnBC,GAAG,EAAE;UACHC,EAAE,EAAEJ,OAAO,CAACK,OAAO;UACnBC,EAAE,EAAEN,OAAO,CAACO;QACd,CAAC;QACDC,KAAK,EAAE,EAAAT,qBAAA,GAAAC,OAAO,CAACS,YAAY,cAAAV,qBAAA,uBAApBA,qBAAA,CAAsBW,MAAM,KAAI;MACzC,CAAC,CAAC;;MAEF;MACA,IAAIV,OAAO,CAACS,YAAY,IAAIE,KAAK,CAACC,OAAO,CAACZ,OAAO,CAACS,YAAY,CAAC,EAAE;QAC/DT,OAAO,CAACS,YAAY,CAACzC,OAAO,CAAC6C,WAAW,IAAI;UAC1CC,wBAAwB,CAACD,WAAW,CAAC;QACvC,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAID,WAAW,IAAK;IAChD,MAAME,EAAE,GAAGF,WAAW,CAACG,SAAS;IAChC,MAAMhC,IAAI,GAAGiC,QAAQ,CAACJ,WAAW,CAACK,WAAW,CAAC;;IAE9C;IACA,MAAM1B,QAAQ,GAAGpG,SAAS,CAACmC,OAAO,CAACkE,YAAY,CAC7CN,UAAU,CAAC0B,WAAW,CAACM,WAAW,CAAC,EACnChC,UAAU,CAAC0B,WAAW,CAACO,UAAU,CACnC,CAAC;;IAED;IACA,MAAMC,eAAe,GAAG;MACtBlH,QAAQ,EAAE,IAAIjD,KAAK,CAACyI,OAAO,CAACH,QAAQ,CAAC1D,CAAC,EAAE,GAAG,EAAE,CAAC0D,QAAQ,CAACzD,CAAC,CAAC;MACzDhC,OAAO,EAAEoF,UAAU,CAAC0B,WAAW,CAACtB,WAAW,CAAC;MAC5CzF,KAAK,EAAEqF,UAAU,CAAC0B,WAAW,CAACvB,SAAS,CAAC;MACxCN,IAAI,EAAEA,IAAI;MACVsC,IAAI,EAAE;QACJZ,MAAM,EAAEvB,UAAU,CAAC0B,WAAW,CAACU,UAAU,CAAC,GAAG,GAAG;QAAG;QACnDC,KAAK,EAAErC,UAAU,CAAC0B,WAAW,CAACY,SAAS,CAAC,GAAG,GAAG;QAC9CC,MAAM,EAAEvC,UAAU,CAAC0B,WAAW,CAACc,UAAU,CAAC,GAAG;MAC/C,CAAC;MACDC,UAAU,EAAEC,IAAI,CAACC,GAAG,CAAC;IACvB,CAAC;;IAED;IACA,IAAI,CAACvJ,iBAAiB,CAACwJ,GAAG,CAAChB,EAAE,CAAC,EAAE;MAC9B;MACA,MAAMiB,IAAI,GAAGC,qBAAqB,CAACZ,eAAe,CAAC;MACnD,IAAIW,IAAI,EAAE;QACRE,KAAK,CAACC,GAAG,CAACH,IAAI,CAAC;QACfzJ,iBAAiB,CAAC6J,GAAG,CAACrB,EAAE,EAAEiB,IAAI,CAAC;MACjC;IACF,CAAC,MAAM;MACL;MACA,MAAMA,IAAI,GAAGzJ,iBAAiB,CAAC8J,GAAG,CAACtB,EAAE,CAAC;MACtCuB,qBAAqB,CAACN,IAAI,EAAEX,eAAe,CAAC;IAC9C;;IAEA;IACAhJ,mBAAmB,CAAC+J,GAAG,CAACrB,EAAE,EAAEM,eAAe,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMY,qBAAqB,GAAI1D,IAAI,IAAK;IACtC,IAAIgE,QAAQ,EAAEC,QAAQ;IAEtB,QAAQjE,IAAI,CAACS,IAAI;MACf,KAAK,CAAC;QAAG;QACPuD,QAAQ,GAAG,IAAIrL,KAAK,CAACuL,WAAW,CAAClE,IAAI,CAAC+C,IAAI,CAACZ,MAAM,EAAEnC,IAAI,CAAC+C,IAAI,CAACI,MAAM,EAAEnD,IAAI,CAAC+C,IAAI,CAACE,KAAK,CAAC;QACrFgB,QAAQ,GAAG,IAAItL,KAAK,CAACwL,iBAAiB,CAAC;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QAC3D;MACF,KAAK,CAAC;QAAG;QACPJ,QAAQ,GAAG,IAAIrL,KAAK,CAACuL,WAAW,CAAClE,IAAI,CAAC+C,IAAI,CAACZ,MAAM,EAAEnC,IAAI,CAAC+C,IAAI,CAACI,MAAM,EAAEnD,IAAI,CAAC+C,IAAI,CAACE,KAAK,CAAC;QACrFgB,QAAQ,GAAG,IAAItL,KAAK,CAACwL,iBAAiB,CAAC;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QAC3D;MACF,KAAK,CAAC;QAAG;QACPJ,QAAQ,GAAG,IAAIrL,KAAK,CAAC0L,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAErE,IAAI,CAAC+C,IAAI,CAACI,MAAM,EAAE,CAAC,CAAC;QACpEc,QAAQ,GAAG,IAAItL,KAAK,CAACwL,iBAAiB,CAAC;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QAC3D;MACF;QAAU;QACRJ,QAAQ,GAAG,IAAIrL,KAAK,CAACuL,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACzCD,QAAQ,GAAG,IAAItL,KAAK,CAACwL,iBAAiB,CAAC;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;IAC/D;IAEA,MAAMX,IAAI,GAAG,IAAI9K,KAAK,CAAC2L,IAAI,CAACN,QAAQ,EAAEC,QAAQ,CAAC;IAC/CR,IAAI,CAAC7H,QAAQ,CAACmC,IAAI,CAACiC,IAAI,CAACpE,QAAQ,CAAC;IACjC6H,IAAI,CAACpC,QAAQ,CAAC7D,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGuB,IAAI,CAACxE,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAG;IAExD,OAAOgF,IAAI;EACb,CAAC;;EAED;EACA,MAAMM,qBAAqB,GAAGA,CAACN,IAAI,EAAEzD,IAAI,KAAK;IAC5C;IACA,IAAIjH,KAAK,CAACsE,KAAK,CAACoG,IAAI,CAAC7H,QAAQ,CAAC,CAC3B0B,EAAE,CAAC0C,IAAI,CAACpE,QAAQ,EAAE,GAAG,CAAC,CACtB8B,MAAM,CAAC3E,KAAK,CAAC4E,MAAM,CAAC4G,MAAM,CAACC,IAAI,CAAC,CAChCxG,KAAK,CAAC,CAAC;IAEV,IAAIjF,KAAK,CAACsE,KAAK,CAACoG,IAAI,CAACpC,QAAQ,CAAC,CAC3B/D,EAAE,CAAC;MAAEE,CAAC,EAAEgB,IAAI,CAACC,EAAE,GAAGuB,IAAI,CAACxE,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG;IAAI,CAAC,EAAE,GAAG,CAAC,CACtDf,MAAM,CAAC3E,KAAK,CAAC4E,MAAM,CAAC4G,MAAM,CAACC,IAAI,CAAC,CAChCxG,KAAK,CAAC,CAAC;EACZ,CAAC;EAEDxF,SAAS,CAAC,MAAM;IACd,IAAI,CAACmC,YAAY,CAACqC,OAAO,EAAE;;IAE3B;IACA,MAAM2G,KAAK,GAAG,IAAIhL,KAAK,CAAC8L,KAAK,CAAC,CAAC;;IAE/B;IACA,MAAMC,MAAM,GAAG,IAAI/L,KAAK,CAACgM,iBAAiB,CACxC,EAAE,EACFC,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACDJ,MAAM,CAAC9I,QAAQ,CAACiI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCa,MAAM,CAACvG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBvB,SAAS,CAACI,OAAO,GAAG0H,MAAM;;IAE1B;IACA,MAAMK,QAAQ,GAAG,IAAIpM,KAAK,CAACqM,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAACN,MAAM,CAACC,UAAU,EAAED,MAAM,CAACE,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAACR,MAAM,CAACS,gBAAgB,CAAC;IAC/C1K,YAAY,CAACqC,OAAO,CAACsI,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAI7M,KAAK,CAAC8M,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5D9B,KAAK,CAACC,GAAG,CAAC4B,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAI/M,KAAK,CAACgN,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAAC9J,QAAQ,CAACiI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1CF,KAAK,CAACC,GAAG,CAAC8B,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAIjN,KAAK,CAACgN,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAAChK,QAAQ,CAACiI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3CF,KAAK,CAACC,GAAG,CAACgC,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAIlN,KAAK,CAACmN,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAACjK,QAAQ,CAACiI,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCgC,SAAS,CAACE,KAAK,GAAGvH,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7BoH,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxBvC,KAAK,CAACC,GAAG,CAACiC,SAAS,CAAC;;IAEpB;IACAhM,QAAQ,GAAG,IAAIhB,aAAa,CAAC6L,MAAM,EAAEK,QAAQ,CAACQ,UAAU,CAAC;IACzD1L,QAAQ,CAACsM,aAAa,GAAG,IAAI;IAC7BtM,QAAQ,CAACuM,aAAa,GAAG,IAAI;IAC7BvM,QAAQ,CAACwM,kBAAkB,GAAG,KAAK;IACnCxM,QAAQ,CAACwE,WAAW,GAAG,EAAE;IACzBxE,QAAQ,CAACyE,WAAW,GAAG,GAAG;IAC1BzE,QAAQ,CAAC0E,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC5E,QAAQ,CAAC6E,aAAa,GAAG,CAAC;IAC1B7E,QAAQ,CAACqE,MAAM,CAAC2F,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BhK,QAAQ,CAACuE,MAAM,CAAC,CAAC;;IAEjB;IACAO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnB8F,MAAM,EAAE,CAAC,CAACA,MAAM;MAChB7K,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpB+C,SAAS,EAAE,CAAC,CAACA,SAAS,CAACI;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMsJ,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAI9N,UAAU,CAAC,CAAC;QACtC8N,aAAa,CAACC,IAAI,CAChB,GAAGnM,QAAQ,uBAAuB,EACjCoM,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAACjD,KAAK;;UAE/B;UACA,MAAMmD,gBAAgB,GAAG,IAAInO,KAAK,CAACoO,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACG,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAAChD,QAAQ,EAAE;gBAClB;gBACA,MAAMkD,WAAW,GAAG,IAAIxO,KAAK,CAACyO,oBAAoB,CAAC;kBACjDhD,KAAK,EAAE,QAAQ;kBAAO;kBACtBiD,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIN,KAAK,CAAChD,QAAQ,CAACuD,GAAG,EAAE;kBACtBL,WAAW,CAACK,GAAG,GAAGP,KAAK,CAAChD,QAAQ,CAACuD,GAAG;gBACtC;;gBAEA;gBACAP,KAAK,CAAChD,QAAQ,GAAGkD,WAAW;gBAE5BxI,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEqI,KAAK,CAACQ,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMZ,YAAY,CAACa,QAAQ,CAACvF,MAAM,GAAG,CAAC,EAAE;YACtC,MAAM8E,KAAK,GAAGJ,YAAY,CAACa,QAAQ,CAAC,CAAC,CAAC;YACtCZ,gBAAgB,CAAClD,GAAG,CAACqD,KAAK,CAAC;UAC7B;;UAEA;UACAtD,KAAK,CAACC,GAAG,CAACkD,gBAAgB,CAAC;;UAE3B;UACAzN,gBAAgB,GAAGyN,gBAAgB;UAEnCnI,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9B1D,kBAAkB,CAAC,IAAI,CAAC;UACxBsL,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAa,GAAG,IAAK;UACPhJ,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC+I,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDrB,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMsB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMjB,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACAtH,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAI8H,gBAAgB,EAAE;UACpB,MAAMkB,YAAY,GAAG;YACnB3M,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UACD,MAAMyM,UAAU,GAAGpN,SAAS,CAACmC,OAAO,CAACkE,YAAY,CAAC8G,YAAY,CAAC3M,SAAS,EAAE2M,YAAY,CAAC1M,QAAQ,CAAC;UAChGwL,gBAAgB,CAAClL,QAAQ,CAACiI,GAAG,CAACoE,UAAU,CAAC1K,CAAC,EAAE,GAAG,EAAE,CAAC0K,UAAU,CAACzK,CAAC,CAAC;UAC/DsJ,gBAAgB,CAACzF,QAAQ,CAAC7D,CAAC,GAAIgB,IAAI,CAACC,EAAE,GAAGuJ,YAAY,CAACxM,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9EqI,gBAAgB,CAACxF,YAAY,CAAC,CAAC;UAC/BwF,gBAAgB,CAACvF,iBAAiB,CAAC,IAAI,CAAC;UACxC7H,eAAe,GAAGoN,gBAAgB,CAAClL,QAAQ,CAACsB,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAO2C,KAAK,EAAE;QACdlB,OAAO,CAACkB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAMqI,kBAAkB,GAAGA,CAAC1H,GAAG,EAAE2H,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAI5B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAM2B,WAAW,GAAIC,WAAW,IAAK;UACnC1J,OAAO,CAACC,GAAG,CAAC,WAAW4B,GAAG,aAAa6H,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAI1P,UAAU,CAAC,CAAC;UAC/B0P,MAAM,CAAC3B,IAAI,CACTnG,GAAG,EACFoG,IAAI,IAAK;YACRjI,OAAO,CAACC,GAAG,CAAC,WAAW4B,GAAG,EAAE,CAAC;YAC7BgG,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAe,GAAG,IAAK;YACPhJ,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC+I,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAjI,KAAK,IAAK;YACTlB,OAAO,CAACkB,KAAK,CAAC,SAASW,GAAG,EAAE,EAAEX,KAAK,CAAC;YACpC,IAAIwI,WAAW,GAAG,CAAC,EAAE;cACnB1J,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3B2J,UAAU,CAAC,MAAMH,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACL5B,MAAM,CAAC5G,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAEDuI,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAI1P,UAAU,CAAC,CAAC;IAC/B0P,MAAM,CAAC3B,IAAI,CACT,GAAGnM,QAAQ,4BAA4B,EACvC,MAAOoM,IAAI,IAAK;MACd,IAAI;QACF,MAAM4B,KAAK,GAAG5B,IAAI,CAACjD,KAAK;QACxB6E,KAAK,CAACC,KAAK,CAAC5E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxB2E,KAAK,CAAC5M,QAAQ,CAACiI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3BF,KAAK,CAACC,GAAG,CAAC4E,KAAK,CAAC;;QAEhB;QACA,MAAMT,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC,OAAOlI,KAAK,EAAE;QACdlB,OAAO,CAACkB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACA8H,GAAG,IAAK;MACPhJ,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC+I,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAjI,KAAK,IAAK;MACTlB,OAAO,CAACkB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BlB,OAAO,CAACkB,KAAK,CAAC,OAAO,EAAE;QACrB6I,IAAI,EAAE7I,KAAK,CAACY,IAAI;QAChBkI,IAAI,EAAE9I,KAAK,CAACC,OAAO;QACnB8I,KAAK,EAAE,GAAGpO,QAAQ,4BAA4B;QAC9CqO,KAAK,EAAE,GAAGrO,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMsO,OAAO,GAAGA,CAAA,KAAM;MACpBC,qBAAqB,CAACD,OAAO,CAAC;;MAE9B;MACA/P,KAAK,CAACqF,MAAM,CAAC,CAAC;MAEd,IAAIxE,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACiD,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMkM,UAAU,GAAG3P,gBAAgB,CAACuC,QAAQ,CAACsB,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAM+L,eAAe,GAAG5P,gBAAgB,CAACgI,QAAQ,CAAC7D,CAAC;;QAEnD;QACA;QACA,MAAM0L,gBAAgB,GAAG,EAAED,eAAe,GAAGzK,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAM0K,YAAY,GAAG,IAAIxQ,KAAK,CAACyI,OAAO,CACpC,CAAC,EAAE,GAAG5C,IAAI,CAAC4K,GAAG,CAACF,gBAAgB,CAAC,EAChC,EAAE,EACF,CAAC,EAAE,GAAG1K,IAAI,CAAC6K,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACAxE,MAAM,CAAC9I,QAAQ,CAACmC,IAAI,CAACiL,UAAU,CAAC,CAACpF,GAAG,CAACuF,YAAY,CAAC;;QAElD;QACAzE,MAAM,CAACtH,EAAE,CAACyG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAMyF,YAAY,GAAGN,UAAU,CAAC9L,KAAK,CAAC,CAAC;QACvCwH,MAAM,CAACvG,MAAM,CAACmL,YAAY,CAAC;;QAE3B;QACA5E,MAAM,CAAC6E,sBAAsB,CAAC,CAAC;QAC/B7E,MAAM,CAACpD,YAAY,CAAC,CAAC;QACrBoD,MAAM,CAACnD,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACA1H,QAAQ,CAACiD,OAAO,GAAG,KAAK;;QAExB;QACAjD,QAAQ,CAACqE,MAAM,CAACH,IAAI,CAACiL,UAAU,CAAC;QAChCnP,QAAQ,CAACuE,MAAM,CAAC,CAAC;QAEjBO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnB4K,IAAI,EAAER,UAAU,CAACS,OAAO,CAAC,CAAC;UAC1BC,IAAI,EAAEhF,MAAM,CAAC9I,QAAQ,CAAC6N,OAAO,CAAC,CAAC;UAC/BE,IAAI,EAAEL,YAAY,CAACG,OAAO,CAAC,CAAC;UAC5BG,IAAI,EAAElF,MAAM,CAACmF,iBAAiB,CAAC,IAAIlR,KAAK,CAACyI,OAAO,CAAC,CAAC,CAAC,CAACqI,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI7P,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAACiD,OAAO,GAAG,IAAI;;QAEvB;QACA4H,MAAM,CAACtH,EAAE,CAACyG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAIrF,IAAI,CAACsL,GAAG,CAACpF,MAAM,CAAC9I,QAAQ,CAAC4B,CAAC,CAAC,GAAG,EAAE,EAAE;UACpCkH,MAAM,CAAC9I,QAAQ,CAACiI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BhK,QAAQ,CAACqE,MAAM,CAAC2F,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5Ba,MAAM,CAACvG,MAAM,CAACtE,QAAQ,CAACqE,MAAM,CAAC;UAC9BrE,QAAQ,CAACuE,MAAM,CAAC,CAAC;QACnB;MACF;MAEAvE,QAAQ,CAACuE,MAAM,CAAC,CAAC;MACjB2G,QAAQ,CAACgF,MAAM,CAACpG,KAAK,EAAEe,MAAM,CAAC;IAChC,CAAC;IAEDoE,OAAO,CAAC,CAAC;;IAET;IACA,MAAMkB,YAAY,GAAGA,CAAA,KAAM;MACzBtF,MAAM,CAACuF,MAAM,GAAGrF,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,WAAW;MACtDJ,MAAM,CAAC6E,sBAAsB,CAAC,CAAC;MAC/BxE,QAAQ,CAACG,OAAO,CAACN,MAAM,CAACC,UAAU,EAAED,MAAM,CAACE,WAAW,CAAC;IACzD,CAAC;IACDF,MAAM,CAACsF,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACApF,MAAM,CAACuF,aAAa,GAAG,MAAM;MAC3B,IAAIvN,SAAS,CAACI,OAAO,EAAE;QACrBJ,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACiI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzCjH,SAAS,CAACI,OAAO,CAACmB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCvB,SAAS,CAACI,OAAO,CAACsE,YAAY,CAAC,CAAC;QAChC1E,SAAS,CAACI,OAAO,CAACuE,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAI1H,QAAQ,EAAE;UACZA,QAAQ,CAACqE,MAAM,CAAC2F,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BhK,QAAQ,CAACiD,OAAO,GAAG,IAAI;UACvBjD,QAAQ,CAACuE,MAAM,CAAC,CAAC;QACnB;QAEAxE,UAAU,GAAG,QAAQ;QACrB+E,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MAAA,IAAAwL,qBAAA;MACX,IAAI5Q,oBAAoB,EAAE;QACxB6Q,aAAa,CAAC7Q,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;MACA,IAAIwB,aAAa,CAACgC,OAAO,EAAE;QACzBhC,aAAa,CAACgC,OAAO,CAACsN,GAAG,CAAC,CAAC;MAC7B;MACA1F,MAAM,CAAC2F,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;MAClD,CAAAI,qBAAA,GAAAzP,YAAY,CAACqC,OAAO,cAAAoN,qBAAA,uBAApBA,qBAAA,CAAsBI,WAAW,CAACzF,QAAQ,CAACQ,UAAU,CAAC;MACtDR,QAAQ,CAAC0F,OAAO,CAAC,CAAC;MAClB;MACAzQ,iBAAiB,CAACyF,OAAO,CAACgE,IAAI,IAAI;QAChCE,KAAK,CAAC+G,MAAM,CAACjH,IAAI,CAAC;QAClBA,IAAI,CAACO,QAAQ,CAACyG,OAAO,CAAC,CAAC;QACvBhH,IAAI,CAACQ,QAAQ,CAACwG,OAAO,CAAC,CAAC;MACzB,CAAC,CAAC;MACFzQ,iBAAiB,CAAC2Q,KAAK,CAAC,CAAC;MACzB7Q,mBAAmB,CAAC6Q,KAAK,CAAC,CAAC;IAC7B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEzR,OAAA,CAAAE,SAAA;IAAAsO,QAAA,gBACExO,OAAA;MAAK0R,GAAG,EAAEjQ,YAAa;MAACkQ,KAAK,EAAE;QAAE5H,KAAK,EAAE,MAAM;QAAEE,MAAM,EAAE;MAAO;IAAE;MAAA2H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpE/R,OAAA;MAAK2R,KAAK,EAAElP,oBAAqB;MAAA+L,QAAA,gBAC/BxO,OAAA;QACE2R,KAAK,EAAE;UACL,GAAG1O,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/E2I,KAAK,EAAE3I,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFyP,OAAO,EAAErO,kBAAmB;QAAA6K,QAAA,EAC7B;MAED;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/R,OAAA;QACE2R,KAAK,EAAE;UACL,GAAG1O,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/E2I,KAAK,EAAE3I,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFyP,OAAO,EAAEnO,kBAAmB;QAAA2K,QAAA,EAC7B;MAED;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAAvQ,EAAA,CArtBMD,WAAW;AAAA0Q,EAAA,GAAX1Q,WAAW;AAstBjB,SAAS2Q,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAACrI,KAAK,GAAG,GAAG;EAClBqI,MAAM,CAACnI,MAAM,GAAG,EAAE;;EAElB;EACAsI,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAACrI,KAAK,GAAC,CAAC,EAAEqI,MAAM,CAACnI,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAM4I,OAAO,GAAG,IAAIpT,KAAK,CAACqT,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAItT,KAAK,CAACuT,cAAc,CAAC;IAC9C1E,GAAG,EAAEuE,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAIzT,KAAK,CAAC0T,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAAC3D,KAAK,CAAC5E,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAOuI,MAAM;AACf;;AAEA;AACAxH,MAAM,CAAC0H,WAAW,GAAG,CAAC/O,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EAChC,IAAIpE,gBAAgB,EAAE;IACpBA,gBAAgB,CAACuC,QAAQ,CAACiI,GAAG,CAACtG,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACtCpE,gBAAgB,CAACiI,YAAY,CAAC,CAAC;IAC/BjI,gBAAgB,CAACkI,iBAAiB,CAAC,IAAI,CAAC;IACxC5C,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAACrB,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACAmH,MAAM,CAAC2H,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAM7H,MAAM,GAAG6G,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAIhI,MAAM,EAAE;MACV;MACA,MAAMiI,MAAM,GAAGjI,MAAM,CAAC9I,QAAQ,CAACsB,KAAK,CAAC,CAAC;;MAEtC;MACAwH,MAAM,CAAC9I,QAAQ,CAACiI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9Ba,MAAM,CAACtH,EAAE,CAACyG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBa,MAAM,CAACvG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAuG,MAAM,CAACpD,YAAY,CAAC,CAAC;MACrBoD,MAAM,CAACnD,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAM1H,QAAQ,GAAG0R,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAI/S,QAAQ,EAAE;QACZA,QAAQ,CAACqE,MAAM,CAAC2F,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BhK,QAAQ,CAACuE,MAAM,CAAC,CAAC;MACnB;MAEAO,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBiO,GAAG,EAAEF,MAAM,CAAClD,OAAO,CAAC,CAAC;QACrBqD,GAAG,EAAEpI,MAAM,CAAC9I,QAAQ,CAAC6N,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOsD,CAAC,EAAE;IACVpO,OAAO,CAACkB,KAAK,CAAC,YAAY,EAAEkN,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;AAED,eAAetS,WAAW;AAAC,IAAA0Q,EAAA;AAAA6B,YAAA,CAAA7B,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}