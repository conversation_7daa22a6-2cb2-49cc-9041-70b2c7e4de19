{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport axios from 'axios'; // 新增 axios 导入\nimport VideoPlayer from './VideoPlayer'; // 引入摄像头视频播放组件\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel = null; // 存储原始模型数据\nlet skeleton = null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n  bsm: 'changli/cloud/v2x/obu/bsm',\n  rsm: 'changli/cloud/v2x/rsu/rsm',\n  scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',\n  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat' // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (!lastPosition) {\n      lastPosition = newPos.clone();\n      return newPos;\n    }\n    const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n    const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n    const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n    lastPosition.set(filteredX, filteredY, filteredZ);\n    return lastPosition.clone();\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  const lastPos = vehicleLastPositions.get(vehicleId);\n\n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n\n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n\n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (lastRotation === null) {\n      lastRotation = newRotation;\n      return newRotation;\n    }\n\n    // 处理角度跳变（从360度到0度或反之）\n    let diff = newRotation - lastRotation;\n    if (diff > Math.PI) diff -= 2 * Math.PI;\n    if (diff < -Math.PI) diff += 2 * Math.PI;\n    const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n    lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const lastRot = vehicleLastRotations.get(vehicleId);\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n\n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\n// 在文件顶部添加\nconst mixersToCleanup = new Set();\nconst bonesMap = new Map();\n\n// 在文件顶部添加资源管理器\nconst resourceManager = {\n  mixers: new Set(),\n  bones: new Map(),\n  actions: new Map(),\n  models: new Set(),\n  addMixer(mixer, model) {\n    this.mixers.add(mixer);\n    if (model) {\n      this.models.add(model);\n      // 记录骨骼\n      model.traverse(object => {\n        if (object.isBone) {\n          this.bones.set(object.uuid, object);\n        }\n      });\n    }\n    return mixer;\n  },\n  addAction(action, mixer) {\n    if (!this.actions.has(mixer)) {\n      this.actions.set(mixer, new Set());\n    }\n    this.actions.get(mixer).add(action);\n    return action;\n  },\n  removeMixer(mixer) {\n    if (this.mixers.has(mixer)) {\n      try {\n        // 停止并清理所有动作\n        if (this.actions.has(mixer)) {\n          this.actions.get(mixer).forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          this.actions.delete(mixer);\n        }\n\n        // 停止混合器\n        if (typeof mixer.stopAllAction === 'function') {\n          mixer.stopAllAction();\n        }\n\n        // 清理混合器的根对象\n        const root = mixer.getRoot();\n        if (root) {\n          this.models.delete(root);\n          root.traverse(object => {\n            if (object && object.isBone) {\n              this.bones.delete(object.uuid);\n            }\n            if (object && object.animations) {\n              object.animations.length = 0;\n            }\n          });\n\n          // 安全地清理缓存\n          try {\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n\n            // 这里是发生错误的地方，添加防御性检查\n            if (typeof mixer.uncacheClip === 'function') {\n              // 不直接调用uncacheClip(null)，而是获取混合器中的所有动画片段并逐个清理\n              const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n              clips.forEach(clip => {\n                if (clip && clip.uuid) {\n                  mixer.uncacheClip(clip);\n                }\n              });\n            }\n          } catch (e) {\n            console.log('在清理动画混合器时发生非致命错误:', e);\n            // 继续执行其余清理操作\n          }\n        }\n        this.mixers.delete(mixer);\n      } catch (error) {\n        console.error('清理动画混合器时发生错误:', error);\n        // 确保即使出错也会从集合中移除\n        this.mixers.delete(mixer);\n      }\n    }\n  },\n  cleanup() {\n    try {\n      // 清理动作\n      this.actions.forEach((actions, mixer) => {\n        try {\n          actions.forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          actions.clear();\n        } catch (e) {\n          console.log('清理动作时发生非致命错误:', e);\n        }\n      });\n      this.actions.clear();\n\n      // 清理混合器\n      this.mixers.forEach(mixer => {\n        try {\n          if (typeof mixer.stopAllAction === 'function') {\n            mixer.stopAllAction();\n          }\n          const root = mixer.getRoot();\n          if (root) {\n            root.traverse(object => {\n              if (object && object.animations) {\n                object.animations.length = 0;\n              }\n            });\n\n            // 安全清理\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n\n            // 安全清理动画片段\n            try {\n              if (mixer._actions && Array.isArray(mixer._actions)) {\n                const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n                clips.forEach(clip => {\n                  if (clip && clip.uuid && typeof mixer.uncacheClip === 'function') {\n                    mixer.uncacheClip(clip);\n                  }\n                });\n              }\n            } catch (e) {\n              console.log('清理动画片段时发生非致命错误:', e);\n            }\n          }\n        } catch (e) {\n          console.log('清理混合器时发生非致命错误:', e);\n        }\n      });\n      this.mixers.clear();\n\n      // 清理骨骼\n      this.bones.forEach(bone => {\n        if (bone.parent) {\n          bone.parent.remove(bone);\n        }\n        if (bone.matrix) bone.matrix.identity();\n        if (bone.matrixWorld) bone.matrixWorld.identity();\n      });\n      this.bones.clear();\n\n      // 清理模型\n      this.models.forEach(model => {\n        if (model.parent) {\n          model.parent.remove(model);\n        }\n        model.traverse(object => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n          }\n          if (object.animations) {\n            object.animations.length = 0;\n          }\n        });\n      });\n      this.models.clear();\n    } catch (error) {\n      console.error('全局清理过程中发生错误:', error);\n      // 即使发生错误也尝试清空集合\n      this.actions.clear();\n      this.mixers.clear();\n      this.bones.clear();\n      this.models.clear();\n    }\n  }\n};\n\n// 修改创建动画混合器的函数\nconst createAnimationMixer = model => {\n  const mixer = new THREE.AnimationMixer(model);\n  return resourceManager.addMixer(mixer, model);\n};\n\n// 修改动画动作创建的函数\nconst createAction = (clip, mixer, model) => {\n  const action = mixer.clipAction(clip, model);\n  return resourceManager.addAction(action, mixer);\n};\n\n// 在CampusModel组件顶部添加消息缓存\nconst processedMessageIds = new Set();\nconst CampusModel = ({\n  className,\n  onCurrentRSUChange,\n  selectedRSUs\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n\n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n\n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = new Map(); // 使用全局Map存储所有行人的动画混合器（不再使用useRef）\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n  // 设备数据 state\n  const [devicesData, setDevicesData] = useState({\n    devices: []\n  });\n\n  // 动态加载设备数据\n  const loadDevicesData = async () => {\n    let devicesArray = [];\n    try {\n      // const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const apiUrl = BASE_URL;\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      if (response.data && response.data.success) {\n        devicesArray = response.data.data;\n      }\n      console.log('devicesArrayapiUrl', apiUrl);\n      console.log('devicesArray', devicesArray);\n    } catch (error) {\n      try {\n        const response = await fetch('/src/data/devices.json');\n        const json = await response.json();\n        devicesArray = json.devices || [];\n      } catch (e) {\n        console.error('设备数据加载失败', e);\n      }\n    }\n    setDevicesData({\n      devices: devicesArray\n    });\n  };\n  useEffect(() => {\n    loadDevicesData();\n  }, []);\n\n  // 路口数据 state\n  const [intersections, setIntersections] = useState([]);\n  // 动态加载路口数据\n  useEffect(() => {\n    const fetchIntersections = async () => {\n      try {\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const response = await axios.get(`${apiUrl}/api/intersections`);\n        if (response.data && response.data.success) {\n          setIntersections(response.data.data || []);\n        }\n      } catch (error) {\n        console.error('获取路口信息失败:', error);\n      }\n    };\n    fetchIntersections();\n  }, []);\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,\n    // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null,\n    phases: []\n  });\n\n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n\n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n\n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n\n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '65px',\n    // 从 60px 改为 65px\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '65px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({\n    topic: '',\n    content: ''\n  });\n\n  // 设备信息弹框状态\n  const [devicePopover, setDevicePopover] = useState({\n    visible: false,\n    deviceId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null\n  });\n\n  // 设备弹框关闭函数\n  const handleCloseDevicePopover = () => {\n    setDevicePopover({\n      visible: false,\n      deviceId: null,\n      position: {\n        x: 0,\n        y: 0\n      },\n      content: null\n    });\n  };\n\n  // 设备弹框内容渲染函数\n  const renderDevicePopoverContent = device => {\n    if (!device) return null;\n    const isCamera = device.type === 'camera';\n    const imgPath = `${BASE_URL}/images/${device.type}.png`;\n    // 优先使用 REACT_APP_FLV_URL 环境变量\n    const flvBase = process.env.REACT_APP_FLV_URL || 'http://localhost:8000';\n    const flvUrl = device.rtspUrl ? `${flvBase}/live/${device.id}.flv` : null;\n    console.log('flvUrl', flvUrl);\n    console.log('device', device);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '10px',\n        width: 320,\n        maxWidth: 350\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 6,\n          textAlign: 'center'\n        },\n        children: isCamera ? device.rtspUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: 300,\n            height: 180,\n            background: '#000',\n            margin: '0 auto',\n            borderRadius: 6,\n            overflow: 'hidden'\n          },\n          children: /*#__PURE__*/_jsxDEV(VideoPlayer, {\n            deviceId: device.id,\n            rtspUrl: device.rtspUrl\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: 300,\n            height: 180,\n            background: '#222',\n            color: '#fff',\n            lineHeight: '180px',\n            borderRadius: 6,\n            margin: '0 auto'\n          },\n          children: \"\\u65E0\\u89C6\\u9891\\u6D41\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n          src: imgPath,\n          alt: device.type,\n          style: {\n            width: 120,\n            height: 120,\n            objectFit: 'contain',\n            background: '#fff',\n            borderRadius: 8,\n            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 625,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 15,\n          fontWeight: 'bold',\n          marginBottom: 6\n        },\n        children: device.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 13,\n          color: '#888',\n          marginBottom: 6\n        },\n        children: [\"\\u7C7B\\u578B\\uFF1A\", device.type]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 13,\n          color: '#888',\n          marginBottom: 6\n        },\n        children: [\"\\u4F4D\\u7F6E\\uFF1A\", device.location, \" \", device.entrance ? `(${device.entrance})` : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 641,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 13,\n          color: '#888',\n          marginBottom: 6\n        },\n        children: [\"\\u72B6\\u6001\\uFF1A\", device.status === 'online' ? /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#52c41a'\n          },\n          children: \"\\u5728\\u7EBF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 104\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#f5222d'\n          },\n          children: \"\\u79BB\\u7EBF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 151\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 9\n      }, this), device.ipAddress && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 13,\n          color: '#888',\n          marginBottom: 6\n        },\n        children: [\"IP\\uFF1A\", device.ipAddress]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 30\n      }, this), device.manufacturer && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 13,\n          color: '#888',\n          marginBottom: 6\n        },\n        children: [\"\\u5382\\u5546\\uFF1A\", device.manufacturer]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 33\n      }, this), device.model && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 13,\n          color: '#888',\n          marginBottom: 6\n        },\n        children: [\"\\u578B\\u53F7\\uFF1A\", device.model]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 645,\n        columnNumber: 26\n      }, this), device.description && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 13,\n          color: '#888',\n          marginBottom: 6\n        },\n        children: [\"\\u63CF\\u8FF0\\uFF1A\", device.description]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 32\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 624,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n\n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n\n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n\n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos).to({\n          x: 0,\n          y: 300,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        }).start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp).to({\n          x: 0,\n          y: 1,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        }).start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n\n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget).to({\n          x: 0,\n          y: 0,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        }).start();\n\n        // 启用控制器\n        controls.enabled = true;\n\n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x + 50, 70, -modelCoords.y + 50);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n\n      // 如果该路口有红绿灯，自动显示红绿灯弹窗\n      if (intersection.hasTrafficLight !== false && intersection.interId) {\n        console.log(`路口 ${intersection.name} 有红绿灯，准备显示红绿灯状态`);\n\n        // 延迟300ms调用以确保场景已更新\n        setTimeout(() => {\n          // 检查多种可能的ID格式\n          let interId = intersection.interId;\n\n          // 使用全局的showTrafficLightPopup函数显示红绿灯弹窗\n          if (window.showTrafficLightPopup) {\n            window.showTrafficLightPopup(interId);\n            console.log(`已触发路口 ${intersection.name} (ID: ${interId}) 的红绿灯弹窗显示`);\n          } else {\n            console.error('找不到显示红绿灯弹窗的函数');\n          }\n        }, 300);\n      } else {\n        console.log(`路口 ${intersection.name} 没有红绿灯或缺少ID，不显示红绿灯状态`);\n\n        // 如果有弹窗正在显示，则关闭它\n        if (window._setTrafficLightPopover) {\n          window._setTrafficLightPopover({\n            visible: false\n          });\n        }\n      }\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        var _payload$data;\n        // console.log('收到RSM消息:', payload);\n\n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n\n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n          return;\n        }\n\n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n\n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n\n        const participants = ((_payload$data = payload.data) === null || _payload$data === void 0 ? void 0 : _payload$data.participants) || [];\n        const rsuid = payload.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id = deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = type === '3' ? SkeletonUtils.clone(preloadedPeopleModel) : preloadedModel.clone();\n              // 根据类型调整高度和缩放\n              const height = type === '3' ? 2.0 : 1.0;\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n\n              // 如果是行人类型，设置缩放和创建动画\n              if (type === '3') {\n                // newModel.scale.set(0.005, 0.005, 0.005);\n                newModel.scale.set(4, 4, 4);\n\n                // 使用resourceManager创建并管理动画混合器\n                const mixer = createAnimationMixer(newModel);\n                if (peopleBaseModel && peopleBaseModel.animations && peopleBaseModel.animations.length > 0) {\n                  // 只创建一个动作并添加到资源管理器\n                  const action = createAction(peopleBaseModel.animations[0], mixer, newModel);\n                  action.play();\n                }\n\n                // 保存到全局Map中(不再使用useRef)\n                peopleAnimationMixers.set(id, mixer);\n              }\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 1000;\n        const currentIds = new Set(participants.map(p => deviceMac + p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            // 如果是行人，清理动画混合器\n            if (modelData.type === '3' && peopleAnimationMixers.has(id)) {\n              const mixer = peopleAnimationMixers.get(id);\n              // 使用resourceManager清理混合器\n              resourceManager.removeMixer(mixer);\n              peopleAnimationMixers.delete(id);\n            }\n\n            // 从场景移除模型\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n\n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n\n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n\n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n\n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid,\n          // 直接传递车辆ID\n          data: {\n            // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n\n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n\n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n\n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n\n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n\n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n\n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n\n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n\n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n\n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n\n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          scene.add(newVehicleModel);\n\n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1',\n            // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n\n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position).to({\n            y: 0.5\n          }, 500).easing(TWEEN.Easing.Quadratic.Out).start();\n\n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({\n                opacity: 0.6\n              }).to({\n                opacity: 1.0\n              }, 500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function () {\n                child.material.opacity = this.opacity;\n                child.material.needsUpdate = true;\n              }).start();\n            }\n          });\n\n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({\n            opacity: 0.6\n          }).to({\n            opacity: 1.0\n          }, 500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function () {\n            speedLabel.material.opacity = this.opacity;\n            speedLabel.material.needsUpdate = true;\n          }).start();\n\n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n\n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n\n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n\n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n\n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n\n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 增加到10秒，避免频繁清理造成闪烁\n\n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n\n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1;\n            modelData.model.traverse(child => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n\n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n\n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            modelData.model.traverse(child => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n\n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n\n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n\n        try {\n          const payload = JSON.parse(message);\n\n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n\n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n\n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n\n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n\n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n\n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? getDirectionFromCode(phase.trafficDirec) : getPhaseDirection(phaseId);\n\n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n\n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n\n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n\n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n\n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n\n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n\n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n\n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && (window.currentPopoverIdRef.current === modelKey || window.currentPopoverIdRef.current === String(modelKey) || window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n\n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n        return;\n      }\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件，确保包含mac和timestamp\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data,\n          mac: payload.mac,\n          tm: payload.tm\n        }, '*');\n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(parseFloat(rsiData.posLong), parseFloat(rsiData.posLat));\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          switch (eventType) {\n            case '401':\n              // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':\n              // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':\n              // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':\n              // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':\n              // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002':\n              // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':\n              // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n\n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n\n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch (sceneType) {\n          case '2':\n            // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':\n            // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':\n            // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':\n            // 限速提醒\n            const speedLimit = sceneData.eventData1; // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':\n            // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':\n            // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':\n            // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':\n            // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':\n            // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':\n            // 信号灯优先\n            const priorityType = sceneData.eventData1; // 优先类型\n            const duration = sceneData.eventData2; // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 检查消息ID是否已处理过\n          if (message.messageId) {\n            if (processedMessageIds.has(message.messageId)) {\n              // console.log('跳过重复消息:', message.messageId);\n              return;\n            }\n\n            // 添加到已处理集合\n            processedMessageIds.add(message.messageId);\n\n            // 限制缓存大小，防止内存泄漏\n            if (processedMessageIds.size > 1000) {\n              // 转换为数组，删除最早的100个元素\n              const idsArray = Array.from(processedMessageIds);\n              for (let i = 0; i < 100; i++) {\n                processedMessageIds.delete(idsArray[i]);\n              }\n            }\n          }\n\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/vehicle.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n\n        // 检查scene是否初始化\n        if (scene) {\n          scene.add(model);\n\n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } else {\n          console.error('无法添加模型：场景未初始化');\n        }\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画 - 只更新存在的混合器\n      const deltaTime = clock.getDelta();\n\n      // 使用Map而不是ref.current\n      if (peopleAnimationMixers.size > 0) {\n        peopleAnimationMixers.forEach(mixer => {\n          mixer.update(deltaTime);\n        });\n      }\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 200, -50 * Math.sin(adjustedRotation));\n\n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n\n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n\n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n\n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 路口视角模式\n        controls.update();\n      }\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 停止渲染循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 停止所有 TWEEN 动画\n      TWEEN.removeAll();\n\n      // 3. 清理所有动画混合器\n      peopleAnimationMixers.forEach(mixer => {\n        resourceManager.removeMixer(mixer);\n      });\n      peopleAnimationMixers.clear();\n\n      // 4. 清理所有其他资源\n      resourceManager.cleanup();\n\n      // 5. 清理场景中的所有对象\n      if (scene) {\n        const objectsToRemove = [];\n        scene.traverse(object => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n            if (object !== scene) {\n              objectsToRemove.push(object);\n            }\n          }\n        });\n\n        // 从场景中移除对象\n        objectsToRemove.forEach(obj => {\n          if (obj.parent) {\n            obj.parent.remove(obj);\n          }\n        });\n        scene.clear();\n      }\n\n      // 6. 清理渲染器\n      if (renderer) {\n        renderer.setAnimationLoop(null);\n        if (containerRef.current && renderer.domElement && renderer.domElement.parentNode === containerRef.current) {\n          containerRef.current.removeChild(renderer.domElement);\n        }\n        renderer.dispose();\n        renderer.forceContextLoss();\n      }\n\n      // 7. 清理其他资源\n      if (controls) {\n        controls.dispose();\n      }\n\n      // 8. 清理数据结构\n      vehicleLastPositions.clear();\n      vehicleLastRotations.clear();\n      deviceTimestamps.clear();\n      vehicleModels.clear();\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n\n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n\n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n\n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n\n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {\n          // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 2000);\n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n\n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = event => {\n        if (scene && cameraRef.current) {\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current);\n        }\n      };\n\n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n\n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n\n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({\n      color: 0x333333\n    });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n\n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({\n      color: 0x666666\n    });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n\n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          //\n          transparent: false,\n          opacity: 0.1,\n          // 几乎透明\n          depthWrite: false\n        });\n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0); // 放在红绿灯位置\n\n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n\n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500); // 延迟略长于debugTrafficLights\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n\n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n\n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersections && intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        // 查找第一个带有红绿灯的路口\n        const firstTrafficLightIntersection = intersections.find(intersection => intersection.hasTrafficLight !== false && intersection.interId);\n\n        // 如果找到带红绿灯的路口，优先选择它；否则选择第一个路口\n        const targetIntersection = firstTrafficLightIntersection || intersections[0];\n        console.log('自动选择路口:', targetIntersection.name, '具有红绿灯:', firstTrafficLightIntersection ? '是' : '否');\n\n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(targetIntersection.name);\n        }, 2000);\n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersections, selectedIntersection]);\n\n  // 新增：在场景初始化后渲染所有路口entrance的设备图标\n  const renderEntranceDeviceIcons = converterInstance => {\n    if (!scene || typeof scene.traverse !== 'function' || !converterInstance) return;\n    if (!devicesData.devices || devicesData.devices.length === 0) return; // 设备数据未加载时不渲染\n    try {\n      scene.children.filter(obj => obj.userData && obj.userData.isEntranceDeviceIcons).forEach(obj => scene.remove(obj));\n      intersections.forEach(intersection => {\n        if (!intersection.entrances || !Array.isArray(intersection.entrances)) return;\n        intersection.entrances.forEach(entrance => {\n          if (!entrance.longitude || !entrance.latitude || isNaN(parseFloat(entrance.longitude)) || isNaN(parseFloat(entrance.latitude))) {\n            return;\n          }\n          console.log('devicesData', devicesData);\n          const devices = devicesData.devices.filter(d => d.location === intersection.name && d.entrance === entrance.name);\n          if (devices.length === 0) return;\n          // 经纬度转模型坐标\n          console.log('渲染路口', intersection.name, '入口', entrance.name, '设备数量', devices.length);\n          const modelPos = converterInstance.wgs84ToModel(parseFloat(entrance.longitude), parseFloat(entrance.latitude));\n          // 创建一个组用于存放所有图标\n          const group = new THREE.Group();\n          group.position.set(modelPos.x, 10, -modelPos.y); // 上方15米\n          group.userData = {\n            isEntranceDeviceIcons: true\n          };\n          // 图标排成一排，居中\n          const iconSize = 32; // px\n          const iconSpacing = 8; // px\n          const totalWidth = devices.length * iconSize + (devices.length - 1) * iconSpacing;\n          // 以三维单位为准，假设1单位=1米，24px约等于0.6米\n          const size3D = 4.0; // 图标尺寸加大\n          const spacing3D = 0.5; // 图标间距加大\n          const startX = -((devices.length - 1) * (size3D + spacing3D)) / 2;\n          devices.forEach((device, idx) => {\n            // 创建一个平面用于显示SVG图标\n            const textureLoader = new THREE.TextureLoader();\n            const iconPath = `${BASE_URL}/images/${device.type}.svg`;\n            // 图标材质\n            const iconMaterial = new THREE.MeshBasicMaterial({\n              map: textureLoader.load(iconPath),\n              transparent: true,\n              opacity: 1 // 图标完全不透明\n            });\n            // 图标背景板材质\n            const bgMaterial = new THREE.MeshBasicMaterial({\n              color: 0x000000,\n              transparent: true,\n              opacity: 0.7 // 半透明黑色\n            });\n            // 背景板尺寸略大于图标\n            const bgWidth = size3D * 1.25;\n            const bgHeight = size3D * 1.25;\n            // 背景板几何体（圆角矩形）\n            // 由于Three.js没有内置圆角矩形，使用PlaneGeometry近似\n            const bgGeometry = new THREE.PlaneGeometry(bgWidth, bgHeight);\n            const bgMesh = new THREE.Mesh(bgGeometry, bgMaterial);\n            // 图标几何体\n            const iconGeometry = new THREE.PlaneGeometry(size3D, size3D);\n            const iconMesh = new THREE.Mesh(iconGeometry, iconMaterial);\n            // 图标略微前移，避免Z轴重叠闪烁\n            iconMesh.position.set(0, 0, 0.01);\n            // 创建一个组，包含背景和图标\n            const iconGroup = new THREE.Group();\n            iconGroup.add(bgMesh);\n            iconGroup.add(iconMesh);\n            // 整体平移到正确位置\n            iconGroup.position.set(startX + idx * (size3D + spacing3D), 0, 0);\n            // 不再固定旋转角度，后续在动画循环中让其始终面向相机\n            iconGroup.renderOrder = 999; // 提高渲染优先级\n            iconGroup.userData = {\n              deviceId: device.id,\n              deviceType: device.type,\n              entrance: entrance.name,\n              isEntranceDeviceIcon: true\n            };\n            group.add(iconGroup);\n          });\n          // 新增：添加白色半透明光柱，指向设备图标组\n          // 光柱高度等于图标组的y坐标（即10），底部在地面y=0，顶部在图标组中心\n          const pillarHeight = group.position.y; // 10\n          const pillarGeometry = new THREE.CylinderGeometry(0.2, 0.2, pillarHeight, 16);\n          const pillarMaterial = new THREE.MeshBasicMaterial({\n            color: 0xffffff,\n            transparent: true,\n            opacity: 0.7\n          });\n          const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial);\n          // 设置光柱中心在y=0~y=10之间，底部正好在地面\n          pillar.position.set(0, -pillarHeight / 2, 0);\n          // pillar.position.set(0, -pillarHeight, 0);\n          // 可选：添加标记，便于后续查找或清理\n          pillar.userData = {\n            isEntranceDevicePillar: true\n          };\n          group.add(pillar);\n          scene.add(group);\n        });\n      });\n    } catch (e) {\n      console.error('renderEntranceDeviceIcons error', e);\n      return;\n    }\n  };\n\n  // 在场景初始化后调用渲染设备图标\n  useEffect(() => {\n    if (scene && typeof scene.traverse === 'function' && converter.current) {\n      renderEntranceDeviceIcons(converter.current);\n    }\n  }, [scene, converter.current]);\n\n  // 新增：每帧让所有设备图标组始终面向相机（billboard效果）\n  useEffect(() => {\n    if (!scene || !cameraRef.current) return;\n    const animateBillboard = () => {\n      // 遍历所有设备图标组，让其正对相机\n      scene.children.forEach(obj => {\n        if (obj.userData && obj.userData.isEntranceDeviceIcons) {\n          obj.lookAt(cameraRef.current.position.x, obj.position.y, cameraRef.current.position.z);\n        }\n      });\n      requestAnimationFrame(animateBillboard);\n    };\n    animateBillboard();\n  }, [scene]);\n\n  // 修改点击处理函数\n  const handleMouseClick = (event, container, sceneInstance, cameraInstance) => {\n    if (!container || !sceneInstance || !cameraInstance) return;\n    const rect = container.getBoundingClientRect();\n    const mouseX = (event.clientX - rect.left) / container.clientWidth * 2 - 1;\n    const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 1;\n    raycaster.params.Line.threshold = 1;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraInstance);\n    const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n    if (intersects.length > 0) {\n      for (let i = 0; i < intersects.length; i++) {\n        const obj = intersects[i].object;\n        if (obj.parent && obj.parent.userData && obj.parent.userData.isEntranceDeviceIcon) {\n          const deviceId = obj.parent.userData.deviceId;\n          console.log('devicesDatahandleMouseClick', devicesData);\n          const device = devicesData.devices.find(d => d.id === deviceId);\n          if (device) {\n            const x = event.clientX;\n            const y = event.clientY;\n            setDevicePopover({\n              visible: true,\n              deviceId,\n              position: {\n                x,\n                y\n              },\n              content: renderDevicePopoverContent(device)\n            });\n            return; // 命中设备图标后直接返回\n          }\n        }\n      }\n    }\n    // ...原有红绿灯弹框逻辑保持不变...\n    // ... existing code ...\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u533A\\u57DF\\u9009\\u62E9\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2466,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u533A\\u57DF\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      options: intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      },\n      value: selectedIntersection ? selectedIntersection.name : undefined\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2467,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2483,\n      columnNumber: 7\n    }, this), trafficLightPopover.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${trafficLightPopover.position.x}px`,\n        top: `${trafficLightPopover.position.y}px`,\n        transform: 'translate(-50%, -100%)',\n        zIndex: 1003,\n        backgroundColor: 'rgba(0, 0, 0, 0.85)',\n        color: 'white',\n        borderRadius: '4px',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n        padding: '0',\n        maxWidth: '240px',\n        // 缩小最大宽度\n        fontSize: '12px' // 缩小字体\n      },\n      children: [trafficLightPopover.content, /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          position: 'absolute',\n          top: '0px',\n          right: '0px',\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          fontSize: '12px',\n          cursor: 'pointer',\n          padding: '2px 6px'\n        },\n        onClick: () => handleClosePopover(setTrafficLightPopover),\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2504,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2487,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2524,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2534,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2523,\n      columnNumber: 7\n    }, this), devicePopover.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${devicePopover.position.x}px`,\n        top: `${devicePopover.position.y}px`,\n        transform: 'translate(-50%, -100%)',\n        zIndex: 1100,\n        backgroundColor: 'rgba(0, 0, 0, 0.92)',\n        color: 'white',\n        borderRadius: '6px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.35)',\n        padding: 0,\n        minWidth: 320,\n        maxWidth: 350,\n        fontSize: 13\n      },\n      children: [devicePopover.content, /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          position: 'absolute',\n          top: '0px',\n          right: '0px',\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          fontSize: '16px',\n          cursor: 'pointer',\n          padding: '2px 10px',\n          zIndex: 1200\n        },\n        onClick: handleCloseDevicePopover,\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2564,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2546,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"iMnV28R+seJ3aTc69s49CDhOAZU=\");\n_c = CampusModel;\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 12,\n    // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    backgroundColor: parameters.backgroundColor || {\n      r: 255,\n      g: 255,\n      b: 255,\n      a: 0.8\n    },\n    textColor: parameters.textColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n\n  // 设置字体\n  // context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n\n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n\n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  canvas.width = width;\n  canvas.height = height;\n\n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n\n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n\n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n\n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n\n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n\n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n\n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n\n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n\n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = {\n    text: text,\n    params: params\n  };\n  return sprite;\n}\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    try {\n      const [trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`), loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`)]);\n\n      // 处理机动车模型\n      console.log('加载车辆模型...');\n      preloadedVehicleModel = vehicleGltf.scene;\n      preloadedVehicleModel.traverse(child => {\n        if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n            color: 0xff0000,\n            // 0xff0000, //红色 0xffffff,白色\n            metalness: 0.2,\n            roughness: 0.1,\n            envMapIntensity: 1.0\n          });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n        }\n      });\n      console.log('加载非机动车模型...');\n      // 处理非机动车模型\n      preloadedCyclistModel = cyclistGltf.scene;\n      // 设置非机动车模型的缩放\n      preloadedCyclistModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedCyclistModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n      console.log('加载行人模型...');\n      // 处理行人模型\n      preloadedPeopleModel = peopleGltf.scene;\n      // 设置行人模型的缩放\n      // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n      // 保持原始材质\n      preloadedPeopleModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n        if (child.isMesh) {\n          child.castShadow = true;\n        }\n      });\n\n      // 保存行人动画数据\n      console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n      if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n        console.log('找到行人动画:', peopleGltf.animations.length, '个');\n        peopleBaseModel = peopleGltf;\n      } else {\n        console.warn('行人模型没有包含动画数据');\n      }\n      console.log('加载红绿灯模型...');\n\n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      console.log('红绿灯模型：', preloadedTrafficLightModel);\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n\n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n\n        // // 尝试单独加载红绿灯模型\n        // if (!preloadedTrafficLightModel) {\n        //   console.log('正在单独加载红绿灯模型...');\n        //   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n        //   preloadedTrafficLightModel = trafficLightGltf.scene;\n        //   preloadedTrafficLightModel.scale.set(3, 3, 3);\n        //   console.log('红绿灯模型加载成功');\n        // }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = type => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  try {\n    // 创建一个新的警告标记\n    const sprite = createTextSprite(text);\n    sprite.position.set(position.x, 10, -position.y); // 设置位置，稍微升高以便可见\n\n    // 为标记添加一个定时器，在1秒后自动移除\n    setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n        scene.remove(sprite);\n      }\n    }, 100);\n\n    // 将标记添加到场景中\n    scene.add(sprite);\n\n    // console.log('添加场景事件标记:', {\n    //   位置: position,\n    //   文本: text,\n    //   颜色: color\n    // });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = converterInstance => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n\n  // 检查红绿灯模型是否已加载\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载，尝试重新加载...');\n    // 尝试重新加载红绿灯模型\n    const loader = new GLTFLoader();\n    loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`).then(trafficLightGltf => {\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('红绿灯模型重新加载成功，开始创建红绿灯...');\n      // 重新调用创建函数\n      createTrafficLights(converterInstance);\n    }).catch(error => {\n      console.error('红绿灯模型重新加载失败:', error);\n      // 如果加载失败，使用简单的替代物体\n      createFallbackTrafficLights(converterInstance);\n    });\n    return;\n  }\n\n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach(lightObj => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersections.forEach(intersection => {\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      try {\n        // 确保模型存在且可以克隆\n        if (!preloadedTrafficLightModel || !preloadedTrafficLightModel.clone) {\n          throw new Error('红绿灯模型无效或无法克隆');\n        }\n\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n\n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n\n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n\n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n\n        // 设置材质属性\n        trafficLightModel.traverse(child => {\n          if (child.isMesh) {\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            child.renderOrder = 100;\n          }\n        });\n\n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n\n        // 将红绿灯添加到场景中\n        // scene.add(trafficLightModel);\n\n        // ========== 新增：在红绿灯地面添加指北针图标 =============\n        // 创建一个平面用于显示指北针SVG图标\n        const compassTextureLoader = new THREE.TextureLoader();\n        const compassIconPath = `${BASE_URL}/images/compass.svg`; // public目录下的路径 \n        const compassMaterial = new THREE.MeshBasicMaterial({\n          map: compassTextureLoader.load(compassIconPath),\n          transparent: true,\n          opacity: 1\n        });\n        // 平面几何体，5x5米\n        const compassGeometry = new THREE.PlaneGeometry(5, 5);\n        const compassMesh = new THREE.Mesh(compassGeometry, compassMaterial);\n        // 指北针放在红绿灯正下方地面（y=0.1，略高于地面防止Z冲突）\n        compassMesh.position.set(modelPos.x + 20, 1.5, -(modelPos.y + 20));\n        // 指北针朝上，旋转x轴-90度\n        compassMesh.rotation.x = -Math.PI / 2;\n        // 渲染优先级高，避免被地面遮挡\n        compassMesh.renderOrder = 101;\n        // 可选：添加userData标记\n        compassMesh.userData = {\n          type: 'compassIcon',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        // 添加到场景\n        scene.add(compassMesh);\n        // ========== 指北针图标添加结束 =============\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        // createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n\n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = converterInstance => {\n  intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({\n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n\n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n\n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n\n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,\n    // 完全透明\n    depthWrite: false\n  });\n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  trafficLightModel.add(collider);\n\n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n\n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n\n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: 0xFF0000\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n\n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  trafficLightModel.add(lightMesh);\n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = phaseId => {\n  switch (phaseId) {\n    case '1':\n      return '北进口左转';\n    case '2':\n      return '北进口直行';\n    case '3':\n      return '北进口右转';\n    case '5':\n      return '东进口左转';\n    case '6':\n      return '东进口直行';\n    case '7':\n      return '东进口右转';\n    case '9':\n      return '南进口左转';\n    case '10':\n      return '南进口直行';\n    case '11':\n      return '南进口右转';\n    case '13':\n      return '西进口左转';\n    case '14':\n      return '西进口直行';\n    case '15':\n      return '西进口右转';\n    default:\n      return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = dirCode => {\n  switch (dirCode) {\n    case 'N':\n      return '北向南';\n    case 'S':\n      return '南向北';\n    case 'E':\n      return '东向西';\n    case 'W':\n      return '西向东';\n    case 'NE':\n      return '东北向西南';\n    case 'NW':\n      return '西北向东南';\n    case 'SE':\n      return '东南向西北';\n    case 'SW':\n      return '西南向东北';\n    default:\n      return `方向${dirCode}`;\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = setPopoverState => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n\n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n\n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({\n    ...prev,\n    visible: false,\n    content: null,\n    // 清空内容\n    phases: [] // 清空相位信息\n  }));\n  console.log('弹窗已关闭，所有相关资源已清理');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = interId => {\n  try {\n    var _document$querySelect, _document$querySelect2;\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n\n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      return false;\n    }\n\n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n\n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 创建弹出窗口内容\n    let content;\n    if (stateInfo && stateInfo.phases) {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '220px',\n          maxHeight: '300px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          },\n          children: [intersection.name, \" (ID: \", interId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                break;\n            }\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '6px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '4px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: getPhaseDirection(phase.phaseId)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3259,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3263,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 3px',\n                    borderRadius: '2px'\n                  },\n                  children: phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3264,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3262,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3275,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3276,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3274,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3252,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3242,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '6px',\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3232,\n        columnNumber: 9\n      }, this);\n    } else {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          maxWidth: '200px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '8px'\n          },\n          children: intersection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u8DEF\\u53E3ID: \", interId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3289,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2 - 500;\n    const centerY = window.innerHeight / 2 - 500;\n\n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = (_document$querySelect = document.querySelector('#root')) === null || _document$querySelect === void 0 ? void 0 : (_document$querySelect2 = _document$querySelect.__REACT_INSTANCE) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.setTrafficLightPopover;\n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: {\n          x: centerX,\n          y: centerY\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      document.body.appendChild(popover);\n\n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  return list;\n};\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = interId => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n\n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 判断是否有相位数据\n    const hasPhaseData = stateInfo && stateInfo.phases && stateInfo.phases.length > 0;\n    let content;\n\n    // 指北针样式\n    const compassStyle = {\n      position: 'absolute',\n      top: '5px',\n      right: '25px',\n      width: '30px',\n      height: '30px',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      borderRadius: '50%',\n      background: 'rgba(0,0,0,0.1)',\n      zIndex: 10\n    };\n\n    // 指北针组件\n    const CompassIcon = () => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: compassStyle,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          transform: 'rotate(0deg)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#ff5252',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            lineHeight: '14px'\n          },\n          children: \"N\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3444,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            width: 0,\n            height: 0,\n            borderLeft: '6px solid transparent',\n            borderRight: '6px solid transparent',\n            borderBottom: '10px solid #ff5252',\n            marginTop: '-2px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3450,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3438,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 3437,\n      columnNumber: 7\n    }, this);\n    if (hasPhaseData) {\n      // 相位ID与方向/方式的映射\n      const phaseMap = {\n        '1': {\n          dir: 'N',\n          type: 'left'\n        },\n        '2': {\n          dir: 'N',\n          type: 'straight'\n        },\n        '3': {\n          dir: 'N',\n          type: 'right'\n        },\n        '5': {\n          dir: 'E',\n          type: 'left'\n        },\n        '6': {\n          dir: 'E',\n          type: 'straight'\n        },\n        '7': {\n          dir: 'E',\n          type: 'right'\n        },\n        '9': {\n          dir: 'S',\n          type: 'left'\n        },\n        '10': {\n          dir: 'S',\n          type: 'straight'\n        },\n        '11': {\n          dir: 'S',\n          type: 'right'\n        },\n        '13': {\n          dir: 'W',\n          type: 'left'\n        },\n        '14': {\n          dir: 'W',\n          type: 'straight'\n        },\n        '15': {\n          dir: 'W',\n          type: 'right'\n        }\n      };\n      const typeOrder = ['left', 'straight', 'right'];\n      const colorMap = {\n        G: '#00ff00',\n        Y: '#ffff00',\n        R: '#ff0000'\n      };\n      const dirData = {\n        N: {},\n        E: {},\n        S: {},\n        W: {}\n      };\n      stateInfo.phases.forEach(phase => {\n        const map = phaseMap[phase.phaseId];\n        if (map) {\n          dirData[map.dir][map.type] = {\n            color: colorMap[phase.trafficLight] || '#888',\n            remainTime: phase.remainTime\n          };\n        }\n      });\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '260px',\n          background: 'rgba(0,0,0,0.05)',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CompassIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3487,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '8px',\n            fontSize: '15px',\n            textAlign: 'center'\n          },\n          children: [intersection.name, \"\\u706F\\u6001\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3488,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateRows: '60px 60px 60px',\n            gridTemplateColumns: '60px 60px 60px',\n            justifyContent: 'center',\n            alignItems: 'center',\n            background: 'rgba(255,255,255,0.05)',\n            borderRadius: '8px',\n            margin: '0 auto',\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 1,\n              gridColumn: 2,\n              textAlign: 'center',\n              display: 'flex',\n              justifyContent: 'center',\n              width: '100%'\n            },\n            children: typeOrder.map((type, index) => {\n              // 反转左转和右转在数组中的顺序\n              let displayIndex = index;\n              if (index === 0) displayIndex = 2;else if (index === 2) displayIndex = 0;\n              const currentType = typeOrder[displayIndex];\n\n              // 计算与南边对齐的样式\n              const marginStyle = {};\n              if (currentType === 'left') {\n                // 左转箭头 (右侧显示)\n                marginStyle.marginRight = '0px';\n              } else if (currentType === 'straight') {\n                // 直行箭头 (中间显示)\n                marginStyle.marginLeft = '10px';\n                marginStyle.marginRight = '10px';\n              } else if (currentType === 'right') {\n                // 右转箭头 (左侧显示)\n                marginStyle.marginLeft = '0px';\n              }\n              return dirData.N[currentType] &&\n              /*#__PURE__*/\n              // <div key={currentType} style={{\n              //   display: 'flex', \n              //   flexDirection: 'column', \n              //   alignItems: 'center',\n              //   ...marginStyle\n              // }}>\n              _jsxDEV(\"div\", {\n                style: {\n                  marginRight: currentType === 'left' ? 0 : '10px',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    color: dirData.N[currentType].color,\n                    fontWeight: 'bold',\n                    marginBottom: '3px'\n                  },\n                  children: dirData.N[currentType].remainTime\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3530,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: dirData.N[currentType].color,\n                    fontSize: '20px',\n                    lineHeight: '20px'\n                  },\n                  children: currentType === 'left' ? '\\u{1F87A}' : currentType === 'straight' ? '\\u{1F87B}' : '\\u{1F878}'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3531,\n                  columnNumber: 21\n                }, this)]\n              }, currentType, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3529,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3502,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 3,\n              gridColumn: 2,\n              textAlign: 'center',\n              display: 'flex',\n              justifyContent: 'center',\n              width: '100%'\n            },\n            children: typeOrder.map(type => dirData.S[type] && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginRight: type === 'right' ? 0 : '10px',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: dirData.S[type].color,\n                  fontSize: '20px',\n                  lineHeight: '20px'\n                },\n                children: type === 'left' ? '\\u{1F878}' : type === 'straight' ? '\\u{1F879}' : '\\u{1F87A}'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3543,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: dirData.S[type].color,\n                  fontWeight: 'bold',\n                  marginTop: '3px'\n                },\n                children: dirData.S[type].remainTime\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3546,\n                columnNumber: 19\n              }, this)]\n            }, type, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3542,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3540,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 2,\n              gridColumn: 3,\n              textAlign: 'center'\n            },\n            children: typeOrder.map((type, index) => {\n              // 反转左转和右转在数组中的顺序\n              let displayIndex = index;\n              if (index === 0) displayIndex = 2;else if (index === 2) displayIndex = 0;\n              const currentType = typeOrder[displayIndex];\n              return dirData.E[currentType] && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '8px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'flex-start'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: dirData.E[currentType].color,\n                    fontSize: '20px',\n                    lineHeight: '20px'\n                  },\n                  children: currentType === 'left' ? '\\u{1F87B}' : currentType === 'straight' ? '\\u{1F878}' : '\\u{1F879}'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3564,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    color: dirData.E[currentType].color,\n                    fontWeight: 'bold',\n                    marginLeft: '5px'\n                  },\n                  children: dirData.E[currentType].remainTime\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3567,\n                  columnNumber: 21\n                }, this)]\n              }, currentType, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3563,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3553,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 2,\n              gridColumn: 1,\n              textAlign: 'center'\n            },\n            children: typeOrder.map(type => dirData.W[type] && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'flex-end'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: dirData.W[type].color,\n                  fontWeight: 'bold',\n                  marginRight: '5px'\n                },\n                children: dirData.W[type].remainTime\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3582,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: dirData.W[type].color,\n                  fontSize: '20px',\n                  lineHeight: '20px'\n                },\n                children: type === 'left' ? '\\u{1F879}' : type === 'straight' ? '\\u{1F87A}' : '\\u{1F87B}'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3588,\n                columnNumber: 19\n              }, this)]\n            }, type, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3581,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3579,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3489,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            fontSize: '11px',\n            color: '#888',\n            textAlign: 'center'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3595,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3486,\n        columnNumber: 9\n      }, this);\n    } else {\n      // 没有相位数据时显示的内容\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          width: '260px',\n          background: 'rgba(0,0,0,0.05)',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CompassIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3604,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '10px',\n            fontSize: '15px',\n            textAlign: 'center'\n          },\n          children: intersection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3605,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '20px 0',\n            color: '#ff9800',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            background: 'rgba(255,255,255,0.1)',\n            borderRadius: '8px',\n            marginBottom: '10px'\n          },\n          children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3606,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#888',\n            textAlign: 'center'\n          },\n          children: [\"\\u8DEF\\u53E3ID: \", interId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3618,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            fontSize: '11px',\n            color: '#888',\n            textAlign: 'center'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3621,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3603,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 设置弹窗位置在左上角区域\n    const x = 445;\n    const y = 250;\n\n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n\n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n\n    // 更新弹窗状态\n    if (window._setTrafficLightPopover) {\n      window._setTrafficLightPopover({\n        visible: true,\n        interId: interId,\n        position: {\n          x,\n          y\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n\n      // 设置定时更新\n      if (window.trafficLightUpdateTimerRef) {\n        window.trafficLightUpdateTimerRef.current = setInterval(() => {\n          window.showTrafficLightPopup(interId);\n        }, 1000);\n      }\n      return true;\n    } else {\n      console.error('无法找到setTrafficLightPopover函数');\n      return false;\n    }\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = object => {\n  let current = object;\n\n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n\n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n\n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n\n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n\n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n\n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = (x - rect.left) / canvas.clientWidth * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    console.log('归一化坐标:', mouseX, mouseY);\n\n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n\n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n\n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'position:', intersect.object.position.toArray(), 'userData:', intersect.object.userData);\n\n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      return true;\n    }\n\n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', '类型:', obj.type, '位置:', obj.position.toArray(), '距离:', intersect.distance, 'userData:', obj.userData);\n    });\n\n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        var _lightObj$intersectio;\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n\n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n\n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n\n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        if (isVisible) {\n          visibleCount++;\n        }\n        console.log(`红绿灯 ${interId}:`, {\n          名称: ((_lightObj$intersectio = lightObj.intersection) === null || _lightObj$intersectio === void 0 ? void 0 : _lightObj$intersectio.name) || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n\n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  var _trafficLight$interse, _trafficLight$interse2, _trafficLight$interse3;\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch (phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n\n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: (_trafficLight$interse = trafficLight.intersection) === null || _trafficLight$interse === void 0 ? void 0 : _trafficLight$interse.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n\n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = {\n    isLight: true\n  };\n\n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  console.log(`更新路口 ${((_trafficLight$interse2 = trafficLight.intersection) === null || _trafficLight$interse2 === void 0 ? void 0 : _trafficLight$interse2.name) || ((_trafficLight$interse3 = trafficLight.intersection) === null || _trafficLight$interse3 === void 0 ? void 0 : _trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "SkeletonUtils", "mqtt", "Select", "Popover", "axios", "VideoPlayer", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "peopleBaseModel", "skeleton", "lastPosition", "lastRotation", "ALPHA", "vehicleLastPositions", "Map", "vehicleLastRotations", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "process", "env", "REACT_APP_API_URL", "console", "log", "vehicleModels", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "clock", "Clock", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "vehicleId", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "has", "lastPos", "get", "distance", "distanceTo", "MAX_DISTANCE_THRESHOLD", "toFixed", "filteredPos", "Vector3", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "lastRot", "MAX_ANGLE_THRESHOLD", "abs", "TRAFFIC_LIGHT_UPDATE_INTERVAL", "mixersToCleanup", "Set", "bonesMap", "resourceManager", "mixers", "bones", "actions", "models", "addMixer", "mixer", "model", "add", "traverse", "object", "isBone", "uuid", "addAction", "action", "removeMixer", "for<PERSON>ach", "stop", "delete", "stopAllAction", "root", "getRoot", "animations", "length", "uncacheRoot", "uncacheAction", "uncacheClip", "clips", "_actions", "map", "a", "_clip", "filter", "Boolean", "clip", "e", "cleanup", "clear", "bone", "parent", "remove", "matrix", "identity", "matrixWorld", "<PERSON><PERSON><PERSON>", "geometry", "dispose", "material", "createAnimationMixer", "AnimationMixer", "createAction", "clipAction", "processedMessageIds", "CampusModel", "className", "onCurrentRSUChange", "selectedRSUs", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "mapRef", "lastCameraPosition", "lastCameraTarget", "cameraSmoothing", "prevAnimationTimeRef", "Date", "now", "peopleAnimationMixers", "peopleAnimations", "devicesData", "setDevicesData", "devices", "loadDevicesData", "devicesArray", "apiUrl", "success", "intersections", "setIntersections", "fetchIntersections", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "currentPopoverIdRef", "trafficLightUpdateTimerRef", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "devicePopover", "setDevicePopover", "deviceId", "handleCloseDevicePopover", "renderDevicePopoverContent", "device", "isCamera", "type", "imgPath", "flvBase", "REACT_APP_FLV_URL", "flvUrl", "rtspUrl", "id", "style", "max<PERSON><PERSON><PERSON>", "children", "marginBottom", "textAlign", "height", "background", "margin", "overflow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "objectFit", "name", "entrance", "status", "ip<PERSON><PERSON><PERSON>", "manufacturer", "description", "switchToFollowView", "current", "enabled", "switchToGlobalView", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "target", "currentTarget", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "updateMatrix", "updateMatrixWorld", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "i", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "相机位置", "toArray", "目标点", "hasTrafficLight", "setTimeout", "showTrafficLightPopup", "handleMqttMessage", "message", "payload", "JSON", "parse", "_payload$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "participants", "rsuid", "participant", "partPtcId", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "newModel", "rotation", "scale", "play", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "p", "modelData", "bsmData", "bsmid", "newState", "partLong", "partLat", "postMessage", "source", "initialPosition", "initialRotation", "newPosition", "vehicleObj", "newVehicleModel", "child", "newMaterial", "emissive", "Color", "transparent", "needsUpdate", "speedLabel", "createTextSprite", "round", "r", "g", "b", "textColor", "renderOrder", "opacity", "is<PERSON><PERSON>", "Out", "filteredPosition", "timeSinceLastUpdate", "undefined", "originalTran<PERSON>arent", "originalOpacity", "m", "phasesInfo", "phase", "phaseId", "toString", "direction", "trafficDirec", "getDirectionFromCode", "getPhaseDirection", "lightState", "trafficLight", "remainTime", "parseInt", "phaseInfo", "push", "trafficLightKey", "String", "trafficLightModel", "updateTrafficLightVisual", "prev", "<PERSON><PERSON><PERSON>", "strId", "numId", "updateTime", "rsiData", "rsuId", "events", "rtes", "event", "eventId", "rteId", "eventType", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "messageId", "size", "idsArray", "from", "stringify", "onerror", "onclose", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "setIsVehicleLoaded", "xhr", "loaded", "total", "initializeScene", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "deltaTime", "<PERSON><PERSON><PERSON><PERSON>", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "targetCameraPosition", "targetLookAt", "lerp", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "removeAll", "objectsToRemove", "obj", "setAnimationLoop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "forceContextLoss", "handleMainVehicleChange", "intervalId", "setInterval", "removeEventListener", "clearInterval", "timer", "createTrafficLights", "clearTimeout", "handleClick", "handleMouseClick", "initScene", "createSimpleTrafficLight", "BoxGeometry", "MeshBasicMaterial", "<PERSON><PERSON>", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "addClickHelpers", "lightObj", "helperGeometry", "SphereGeometry", "helperMaterial", "depthWrite", "helper<PERSON><PERSON>", "userData", "isClickHelper", "firstTrafficLightIntersection", "targetIntersection", "renderEntranceDeviceIcons", "converterInstance", "isEntranceDeviceIcons", "entrances", "isNaN", "d", "group", "iconSize", "iconSpacing", "totalWidth", "size3D", "spacing3D", "startX", "idx", "textureLoader", "TextureLoader", "iconPath", "iconMaterial", "bgMaterial", "bgWidth", "bgHeight", "bgGeometry", "PlaneGeometry", "bg<PERSON><PERSON>", "iconGeometry", "<PERSON><PERSON><PERSON>", "iconGroup", "deviceType", "isEntranceDeviceIcon", "pillarHeight", "pillarGeometry", "pillarMaterial", "pillar", "isEntranceDevicePillar", "animateBillboard", "container", "sceneInstance", "cameraInstance", "rect", "getBoundingClientRect", "mouseX", "clientX", "clientWidth", "mouseY", "clientY", "clientHeight", "raycaster", "Raycaster", "params", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "intersects", "intersectObjects", "placeholder", "onChange", "options", "label", "bordered", "dropdownStyle", "maxHeight", "ref", "right", "onClick", "handleClosePopover", "min<PERSON><PERSON><PERSON>", "_c", "text", "parameters", "fontFace", "borderThickness", "borderColor", "canvas", "document", "createElement", "context", "getContext", "textWidth", "measureText", "font", "textBaseline", "radius", "beginPath", "moveTo", "lineTo", "arcTo", "closePath", "strokeStyle", "lineWidth", "stroke", "fillStyle", "fill", "fillText", "texture", "CanvasTexture", "minFilter", "LinearFilter", "spriteMaterial", "SpriteMaterial", "sprite", "Sprite", "depthTest", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "trafficLightGltf", "vehicleGltf", "cyclistGltf", "peopleGltf", "all", "loadAsync", "materia", "<PERSON><PERSON><PERSON><PERSON>", "warn", "err", "types", "then", "catch", "createFallbackTrafficLights", "Error", "side", "DoubleSide", "compassTextureLoader", "compassIconPath", "compassMaterial", "compassGeometry", "compassMesh", "colliderGeometry", "colliderMaterial", "collider", "isCollider", "lightGeometry", "lightMaterial", "<PERSON><PERSON><PERSON>", "dirCode", "setPopoverState", "testTrafficLightClick", "_document$querySelect", "_document$querySelect2", "light", "lightModel", "stateInfo", "overflowY", "borderBottom", "paddingBottom", "index", "lightColor", "justifyContent", "marginTop", "toLocaleTimeString", "centerX", "centerY", "__REACT_INSTANCE", "popover", "innerHTML", "body", "closeButton", "listTrafficLights", "list", "numericId", "hasPhaseData", "compassStyle", "alignItems", "CompassIcon", "flexDirection", "borderLeft", "borderRight", "phaseMap", "dir", "typeOrder", "colorMap", "G", "Y", "R", "dirD<PERSON>", "N", "E", "S", "W", "gridTemplateRows", "gridTemplateColumns", "gridRow", "gridColumn", "displayIndex", "currentType", "marginStyle", "marginRight", "marginLeft", "getTrafficLightFromObject", "testClickDetection", "trafficLightObjects", "tlIntersects", "intersect", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "worldPos", "getWorldPosition", "distanceToCamera", "screenPos", "project", "名称", "可见性", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "isLight", "emissiveIntensity", "PointLight", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js';\n\n\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport axios from 'axios'; // 新增 axios 导入\nimport VideoPlayer from './VideoPlayer'; // 引入摄像头视频播放组件\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel= null; // 存储原始模型数据\nlet skeleton =null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n\n\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  const lastPos = vehicleLastPositions.get(vehicleId);\n  \n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n  \n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  \n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  \n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const lastRot = vehicleLastRotations.get(vehicleId);\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  \n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\n// 在文件顶部添加\nconst mixersToCleanup = new Set();\nconst bonesMap = new Map();\n\n// 在文件顶部添加资源管理器\nconst resourceManager = {\n  mixers: new Set(),\n  bones: new Map(),\n  actions: new Map(),\n  models: new Set(),\n  \n  addMixer(mixer, model) {\n    this.mixers.add(mixer);\n    if (model) {\n      this.models.add(model);\n      // 记录骨骼\n      model.traverse(object => {\n        if (object.isBone) {\n          this.bones.set(object.uuid, object);\n        }\n      });\n    }\n    return mixer;\n  },\n  \n  addAction(action, mixer) {\n    if (!this.actions.has(mixer)) {\n      this.actions.set(mixer, new Set());\n    }\n    this.actions.get(mixer).add(action);\n    return action;\n  },\n  \n  removeMixer(mixer) {\n    if (this.mixers.has(mixer)) {\n      try {\n        // 停止并清理所有动作\n        if (this.actions.has(mixer)) {\n          this.actions.get(mixer).forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          this.actions.delete(mixer);\n        }\n        \n        // 停止混合器\n        if (typeof mixer.stopAllAction === 'function') {\n          mixer.stopAllAction();\n        }\n        \n        // 清理混合器的根对象\n        const root = mixer.getRoot();\n        if (root) {\n          this.models.delete(root);\n          root.traverse(object => {\n            if (object && object.isBone) {\n              this.bones.delete(object.uuid);\n            }\n            if (object && object.animations) {\n              object.animations.length = 0;\n            }\n          });\n          \n          // 安全地清理缓存\n          try {\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            \n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n            \n            // 这里是发生错误的地方，添加防御性检查\n            if (typeof mixer.uncacheClip === 'function') {\n              // 不直接调用uncacheClip(null)，而是获取混合器中的所有动画片段并逐个清理\n              const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n              clips.forEach(clip => {\n                if (clip && clip.uuid) {\n                  mixer.uncacheClip(clip);\n                }\n              });\n            }\n          } catch (e) {\n            console.log('在清理动画混合器时发生非致命错误:', e);\n            // 继续执行其余清理操作\n          }\n        }\n        \n        this.mixers.delete(mixer);\n      } catch (error) {\n        console.error('清理动画混合器时发生错误:', error);\n        // 确保即使出错也会从集合中移除\n        this.mixers.delete(mixer);\n      }\n    }\n  },\n  \n  cleanup() {\n    try {\n      // 清理动作\n      this.actions.forEach((actions, mixer) => {\n        try {\n          actions.forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          actions.clear();\n        } catch (e) {\n          console.log('清理动作时发生非致命错误:', e);\n        }\n      });\n      this.actions.clear();\n      \n      // 清理混合器\n      this.mixers.forEach(mixer => {\n        try {\n          if (typeof mixer.stopAllAction === 'function') {\n            mixer.stopAllAction();\n          }\n          \n          const root = mixer.getRoot();\n          if (root) {\n            root.traverse(object => {\n              if (object && object.animations) {\n                object.animations.length = 0;\n              }\n            });\n            \n            // 安全清理\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            \n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n            \n            // 安全清理动画片段\n            try {\n              if (mixer._actions && Array.isArray(mixer._actions)) {\n                const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n                clips.forEach(clip => {\n                  if (clip && clip.uuid && typeof mixer.uncacheClip === 'function') {\n                    mixer.uncacheClip(clip);\n                  }\n                });\n              }\n            } catch (e) {\n              console.log('清理动画片段时发生非致命错误:', e);\n            }\n          }\n        } catch (e) {\n          console.log('清理混合器时发生非致命错误:', e);\n        }\n      });\n      this.mixers.clear();\n      \n      // 清理骨骼\n      this.bones.forEach(bone => {\n        if (bone.parent) {\n          bone.parent.remove(bone);\n        }\n        if (bone.matrix) bone.matrix.identity();\n        if (bone.matrixWorld) bone.matrixWorld.identity();\n      });\n      this.bones.clear();\n      \n      // 清理模型\n      this.models.forEach(model => {\n        if (model.parent) {\n          model.parent.remove(model);\n        }\n        model.traverse(object => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n          }\n          if (object.animations) {\n            object.animations.length = 0;\n          }\n        });\n      });\n      this.models.clear();\n    } catch (error) {\n      console.error('全局清理过程中发生错误:', error);\n      // 即使发生错误也尝试清空集合\n      this.actions.clear();\n      this.mixers.clear();\n      this.bones.clear();\n      this.models.clear();\n    }\n  }\n};\n\n// 修改创建动画混合器的函数\nconst createAnimationMixer = (model) => {\n  const mixer = new THREE.AnimationMixer(model);\n  return resourceManager.addMixer(mixer, model);\n};\n\n// 修改动画动作创建的函数\nconst createAction = (clip, mixer, model) => {\n  const action = mixer.clipAction(clip, model);\n  return resourceManager.addAction(action, mixer);\n};\n\n// 在CampusModel组件顶部添加消息缓存\nconst processedMessageIds = new Set();\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n  \n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n  \n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = new Map(); // 使用全局Map存储所有行人的动画混合器（不再使用useRef）\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n  \n  // 设备数据 state\n  const [devicesData, setDevicesData] = useState({ devices: [] });\n\n  // 动态加载设备数据\n  const loadDevicesData = async () => {\n    let devicesArray = [];\n    try {\n      // const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const apiUrl = BASE_URL;\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      if (response.data && response.data.success) {\n        devicesArray = response.data.data;\n      }\n\n      console.log('devicesArrayapiUrl', apiUrl);\n      console.log('devicesArray', devicesArray);\n    } catch (error) {\n      try {\n        const response = await fetch('/src/data/devices.json');\n        const json = await response.json();\n        devicesArray = json.devices || [];\n      } catch (e) {\n        console.error('设备数据加载失败', e);\n      }\n    }\n    setDevicesData({ devices: devicesArray });\n  };\n  useEffect(() => { loadDevicesData(); }, []);\n  \n // 路口数据 state\n  const [intersections, setIntersections] = useState([]); \n  // 动态加载路口数据\n  useEffect(() => {\n    const fetchIntersections = async () => {\n      try {\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const response = await axios.get(`${apiUrl}/api/intersections`);\n        if (response.data && response.data.success) {\n          setIntersections(response.data.data || []);\n        }\n      } catch (error) {\n        console.error('获取路口信息失败:', error);\n      }\n    };\n    fetchIntersections();\n  }, []);\n\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n  \n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n  \n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n  \n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n  \n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '65px',  // 从 60px 改为 65px\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '65px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 设备信息弹框状态\n  const [devicePopover, setDevicePopover] = useState({\n    visible: false,\n    deviceId: null,\n    position: { x: 0, y: 0 },\n    content: null\n  });\n\n  // 设备弹框关闭函数\n  const handleCloseDevicePopover = () => {\n    setDevicePopover({ visible: false, deviceId: null, position: { x: 0, y: 0 }, content: null });\n  };\n\n  // 设备弹框内容渲染函数\n  const renderDevicePopoverContent = (device) => {\n    if (!device) return null;\n    const isCamera = device.type === 'camera';\n    const imgPath = `${BASE_URL}/images/${device.type}.png`;\n    // 优先使用 REACT_APP_FLV_URL 环境变量\n    const flvBase = process.env.REACT_APP_FLV_URL || 'http://localhost:8000';\n    const flvUrl = device.rtspUrl ? `${flvBase}/live/${device.id}.flv` : null;\n    console.log('flvUrl', flvUrl);\n    console.log('device', device);\n    return (\n      <div style={{ padding: '10px', width: 320, maxWidth: 350 }}>\n        <div style={{ marginBottom: 6, textAlign: 'center' }}>\n          {isCamera ? (\n            device.rtspUrl ? (\n              <div style={{ width: 300, height: 180, background: '#000', margin: '0 auto', borderRadius: 6, overflow: 'hidden' }}>\n                {/* <VideoPlayer deviceId={device.id} rtspUrl={device.rtspUrl} flvUrl={flvUrl} /> */}\n                <VideoPlayer deviceId={device.id} rtspUrl={device.rtspUrl} />\n              </div>\n            ) : (\n              <div style={{ width: 300, height: 180, background: '#222', color: '#fff', lineHeight: '180px', borderRadius: 6, margin: '0 auto' }}>无视频流</div>\n            )\n          ) : (\n            <img src={imgPath} alt={device.type} style={{ width: 120, height: 120, objectFit: 'contain', background: '#fff', borderRadius: 8, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }} />\n          )}\n        </div>\n        <div style={{ fontSize: 15, fontWeight: 'bold', marginBottom: 6 }}>{device.name}</div>\n        <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>类型：{device.type}</div>\n        <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>位置：{device.location} {device.entrance ? `(${device.entrance})` : ''}</div>\n        <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>状态：{device.status === 'online' ? <span style={{ color: '#52c41a' }}>在线</span> : <span style={{ color: '#f5222d' }}>离线</span>}</div>\n        {device.ipAddress && <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>IP：{device.ipAddress}</div>}\n        {device.manufacturer && <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>厂商：{device.manufacturer}</div>}\n        {device.model && <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>型号：{device.model}</div>}\n        {device.description && <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>描述：{device.description}</div>}\n      </div>\n    );\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n      \n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n      \n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n        \n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos)\n          .to({ x: 0, y: 300, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.position.copy(currentPos);\n          })\n          .start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp)\n          .to({ x: 0, y: 1, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.up.copy(currentUp);\n          })\n          .start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n        \n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget)\n          .to({ x: 0, y: 0, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            controls.target.copy(currentTarget);\n            // 确保相机始终朝向目标点\n            cameraRef.current.lookAt(controls.target);\n            controls.update();\n          })\n          .start();\n\n        // 启用控制器\n        controls.enabled = true;\n        \n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersections.find(i => i.name === value);\n\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x+50, 70, -modelCoords.y+50);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n      \n      // 如果该路口有红绿灯，自动显示红绿灯弹窗\n      if (intersection.hasTrafficLight !== false && intersection.interId) {\n        console.log(`路口 ${intersection.name} 有红绿灯，准备显示红绿灯状态`);\n        \n        // 延迟300ms调用以确保场景已更新\n        setTimeout(() => {\n          // 检查多种可能的ID格式\n          let interId = intersection.interId;\n          \n          // 使用全局的showTrafficLightPopup函数显示红绿灯弹窗\n          if (window.showTrafficLightPopup) {\n            window.showTrafficLightPopup(interId);\n            console.log(`已触发路口 ${intersection.name} (ID: ${interId}) 的红绿灯弹窗显示`);\n          } else {\n            console.error('找不到显示红绿灯弹窗的函数');\n          }\n        }, 300);\n      } else {\n        console.log(`路口 ${intersection.name} 没有红绿灯或缺少ID，不显示红绿灯状态`);\n        \n        // 如果有弹窗正在显示，则关闭它\n        if (window._setTrafficLightPopover) {\n          window._setTrafficLightPopover({\n            visible: false\n          });\n        }\n      }\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        // console.log('收到RSM消息:', payload);\n        \n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n        \n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n        \n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n          return;\n        }\n        \n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        \n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n        \n        const participants = payload.data?.participants || [];\n        const rsuid = payload.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id =  deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n          \n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            \n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n            \n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1': // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2': // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3': // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return; // 跳过未知类型\n            }\n            \n            // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = type === '3' ? SkeletonUtils.clone(preloadedPeopleModel) : preloadedModel.clone();\n              // 根据类型调整高度和缩放\n              const height = type === '3' ? 2.0 : 1.0;\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              \n              // 如果是行人类型，设置缩放和创建动画\n              if (type === '3') {\n              // newModel.scale.set(0.005, 0.005, 0.005);\n                newModel.scale.set(4, 4, 4);\n                \n                // 使用resourceManager创建并管理动画混合器\n                const mixer = createAnimationMixer(newModel);\n                \n                if (peopleBaseModel && peopleBaseModel.animations && peopleBaseModel.animations.length > 0) {\n                  // 只创建一个动作并添加到资源管理器\n                  const action = createAction(peopleBaseModel.animations[0], mixer, newModel);\n                  action.play();\n                }\n                \n                // 保存到全局Map中(不再使用useRef)\n                peopleAnimationMixers.set(id, mixer);\n              }\n              \n              scene.add(newModel);\n              \n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n              type: type\n              });\n          } else if (model) {\n              // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n        \n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 1000;\n        const currentIds = new Set(participants.map(p => deviceMac + p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            // 如果是行人，清理动画混合器\n            if (modelData.type === '3' && peopleAnimationMixers.has(id)) {\n              const mixer = peopleAnimationMixers.get(id);\n              // 使用resourceManager清理混合器\n              resourceManager.removeMixer(mixer);\n              peopleAnimationMixers.delete(id);\n            }\n            \n            // 从场景移除模型\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n        \n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n        \n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n        \n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid, // 直接传递车辆ID\n          data: {       // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n        \n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n        \n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n        \n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n        \n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        \n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          \n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n          \n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n              \n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n              \n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n          \n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n          \n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          \n          scene.add(newVehicleModel);\n          \n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n          \n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position)\n            .to({ y: 0.5 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .start();\n          \n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({ opacity: 0.6 })\n                .to({ opacity: 1.0 }, 500)\n                .easing(TWEEN.Easing.Quadratic.Out)\n                .onUpdate(function() {\n                  child.material.opacity = this.opacity;\n                  child.material.needsUpdate = true;\n                })\n                .start();\n            }\n          });\n          \n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({ opacity: 0.6 })\n            .to({ opacity: 1.0 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .onUpdate(function() {\n              speedLabel.material.opacity = this.opacity;\n              speedLabel.material.needsUpdate = true;\n            })\n            .start();\n          \n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n          \n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n          \n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n          \n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n          \n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n        \n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 增加到10秒，避免频繁清理造成闪烁\n        \n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n          \n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1 ;\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n                \n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n            \n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n            \n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            \n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        \n        return;\n      }\n      \n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n        \n        try {\n          const payload = JSON.parse(message);\n          \n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n          \n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n          \n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n          \n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n          \n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              \n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              \n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n              \n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                \n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  \n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? \n                    getDirectionFromCode(phase.trafficDirec) : \n                    getPhaseDirection(phaseId);\n                  \n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  \n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n                  \n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n                  \n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n                  \n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  \n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  \n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n                    \n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n                \n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                \n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n                  \n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && \n                     (window.currentPopoverIdRef.current === modelKey || \n                      window.currentPopoverIdRef.current === String(modelKey) || \n                      window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    \n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n                    \n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n        return;\n      }\n      \n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件，确保包含mac和timestamp\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data,\n          mac: payload.mac,\n          tm: payload.tm\n        }, '*');\n        \n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n        \n        return;\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n        \n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 检查消息ID是否已处理过\n          if (message.messageId) {\n            if (processedMessageIds.has(message.messageId)) {\n              // console.log('跳过重复消息:', message.messageId);\n              return;\n            }\n            \n            // 添加到已处理集合\n            processedMessageIds.add(message.messageId);\n            \n            // 限制缓存大小，防止内存泄漏\n            if (processedMessageIds.size > 1000) {\n              // 转换为数组，删除最早的100个元素\n              const idsArray = Array.from(processedMessageIds);\n              for (let i = 0; i < 100; i++) {\n                processedMessageIds.delete(idsArray[i]);\n              }\n            }\n          }\n          \n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/vehicle.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n        \n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          \n          // 检查scene是否初始化\n          if (scene) {\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画 - 只更新存在的混合器\n      const deltaTime = clock.getDelta();\n      \n      // 使用Map而不是ref.current\n      if (peopleAnimationMixers.size > 0) {\n        peopleAnimationMixers.forEach((mixer) => {\n          mixer.update(deltaTime);\n        });\n      }\n\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          200,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n        \n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        \n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n        \n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n        \n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 停止渲染循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 停止所有 TWEEN 动画\n      TWEEN.removeAll();\n\n      // 3. 清理所有动画混合器\n      peopleAnimationMixers.forEach(mixer => {\n        resourceManager.removeMixer(mixer);\n      });\n      peopleAnimationMixers.clear();\n\n      // 4. 清理所有其他资源\n      resourceManager.cleanup();\n\n      // 5. 清理场景中的所有对象\n      if (scene) {\n        const objectsToRemove = [];\n        scene.traverse((object) => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n            if (object !== scene) {\n              objectsToRemove.push(object);\n            }\n          }\n        });\n        \n        // 从场景中移除对象\n        objectsToRemove.forEach(obj => {\n          if (obj.parent) {\n            obj.parent.remove(obj);\n          }\n        });\n        \n        scene.clear();\n      }\n\n      // 6. 清理渲染器\n      if (renderer) {\n        renderer.setAnimationLoop(null);\n        if (containerRef.current && renderer.domElement && renderer.domElement.parentNode === containerRef.current) {\n          containerRef.current.removeChild(renderer.domElement);\n        }\n        renderer.dispose();\n        renderer.forceContextLoss();\n      }\n\n      // 7. 清理其他资源\n      if (controls) {\n        controls.dispose();\n      }\n\n      // 8. 清理数据结构\n      vehicleLastPositions.clear();\n      vehicleLastRotations.clear();\n      deviceTimestamps.clear();\n      vehicleModels.clear();\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n    \n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n    \n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n    \n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n    \n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 2000);\n      \n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n  \n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current);\n        }\n      };\n      \n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n      \n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n      \n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n    \n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    \n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n    \n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,//\n          transparent: false,\n          opacity: 0.1,  // 几乎透明\n          depthWrite: false\n        });\n        \n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置\n        \n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n        \n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        \n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500);  // 延迟略长于debugTrafficLights\n    \n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n    \n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n  \n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersections && intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        // 查找第一个带有红绿灯的路口\n        const firstTrafficLightIntersection = intersections.find(\n          intersection => intersection.hasTrafficLight !== false && intersection.interId\n        );\n        \n        // 如果找到带红绿灯的路口，优先选择它；否则选择第一个路口\n        const targetIntersection = firstTrafficLightIntersection || intersections[0];\n        \n        console.log('自动选择路口:', targetIntersection.name, \n                    '具有红绿灯:', firstTrafficLightIntersection ? '是' : '否');\n        \n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(targetIntersection.name);\n        }, 2000);\n        \n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersections, selectedIntersection]);\n\n  // 新增：在场景初始化后渲染所有路口entrance的设备图标\n  const renderEntranceDeviceIcons = (converterInstance) => {\n    if (!scene || typeof scene.traverse !== 'function' || !converterInstance) return;\n    if (!devicesData.devices || devicesData.devices.length === 0) return; // 设备数据未加载时不渲染\n    try {\n      scene.children\n        .filter(obj => obj.userData && obj.userData.isEntranceDeviceIcons)\n        .forEach(obj => scene.remove(obj));\n      intersections.forEach(intersection => {\n        if (!intersection.entrances || !Array.isArray(intersection.entrances)) return;\n        intersection.entrances.forEach((entrance) => {\n          if (!entrance.longitude || !entrance.latitude || isNaN(parseFloat(entrance.longitude)) || isNaN(parseFloat(entrance.latitude))) {\n            return;\n          }\n          console.log('devicesData', devicesData);\n          const devices = devicesData.devices.filter(\n            d => d.location === intersection.name && d.entrance === entrance.name\n          );\n          if (devices.length === 0) return;\n          // 经纬度转模型坐标\n          console.log('渲染路口', intersection.name, '入口', entrance.name, '设备数量', devices.length);\n          const modelPos = converterInstance.wgs84ToModel(\n            parseFloat(entrance.longitude),\n            parseFloat(entrance.latitude)\n          );\n          // 创建一个组用于存放所有图标\n          const group = new THREE.Group();\n          group.position.set(modelPos.x, 10, -modelPos.y); // 上方15米\n          group.userData = { isEntranceDeviceIcons: true };\n          // 图标排成一排，居中\n          const iconSize = 32; // px\n          const iconSpacing = 8; // px\n          const totalWidth = devices.length * iconSize + (devices.length - 1) * iconSpacing;\n          // 以三维单位为准，假设1单位=1米，24px约等于0.6米\n          const size3D = 4.0; // 图标尺寸加大\n          const spacing3D = 0.5; // 图标间距加大\n          const startX = -((devices.length - 1) * (size3D + spacing3D)) / 2;\n          devices.forEach((device, idx) => {\n            // 创建一个平面用于显示SVG图标\n            const textureLoader = new THREE.TextureLoader();\n            const iconPath = `${BASE_URL}/images/${device.type}.svg`;\n            // 图标材质\n            const iconMaterial = new THREE.MeshBasicMaterial({\n              map: textureLoader.load(iconPath),\n              transparent: true,\n              opacity: 1 // 图标完全不透明\n            });\n            // 图标背景板材质\n            const bgMaterial = new THREE.MeshBasicMaterial({\n              color: 0x000000,\n              transparent: true,\n              opacity: 0.7 // 半透明黑色\n            });\n            // 背景板尺寸略大于图标\n            const bgWidth = size3D * 1.25;\n            const bgHeight = size3D * 1.25;\n            // 背景板几何体（圆角矩形）\n            // 由于Three.js没有内置圆角矩形，使用PlaneGeometry近似\n            const bgGeometry = new THREE.PlaneGeometry(bgWidth, bgHeight);\n            const bgMesh = new THREE.Mesh(bgGeometry, bgMaterial);\n            // 图标几何体\n            const iconGeometry = new THREE.PlaneGeometry(size3D, size3D);\n            const iconMesh = new THREE.Mesh(iconGeometry, iconMaterial);\n            // 图标略微前移，避免Z轴重叠闪烁\n            iconMesh.position.set(0, 0, 0.01);\n            // 创建一个组，包含背景和图标\n            const iconGroup = new THREE.Group();\n            iconGroup.add(bgMesh);\n            iconGroup.add(iconMesh);\n            // 整体平移到正确位置\n            iconGroup.position.set(startX + idx * (size3D + spacing3D), 0, 0);\n            // 不再固定旋转角度，后续在动画循环中让其始终面向相机\n            iconGroup.renderOrder = 999; // 提高渲染优先级\n            iconGroup.userData = {\n              deviceId: device.id,\n              deviceType: device.type,\n              entrance: entrance.name,\n              isEntranceDeviceIcon: true\n            };\n            group.add(iconGroup);\n          });\n          // 新增：添加白色半透明光柱，指向设备图标组\n          // 光柱高度等于图标组的y坐标（即10），底部在地面y=0，顶部在图标组中心\n          const pillarHeight =  group.position.y; // 10\n          const pillarGeometry = new THREE.CylinderGeometry(0.2, 0.2, pillarHeight, 16);\n          const pillarMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff, transparent: true, opacity: 0.7 });\n          const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial);\n          // 设置光柱中心在y=0~y=10之间，底部正好在地面\n          pillar.position.set(0, -pillarHeight / 2, 0);\n          // pillar.position.set(0, -pillarHeight, 0);\n          // 可选：添加标记，便于后续查找或清理\n          pillar.userData = { isEntranceDevicePillar: true };\n          group.add(pillar);\n          scene.add(group);\n        });\n      });\n\n    } catch (e) {\n      console.error('renderEntranceDeviceIcons error', e);\n      return;\n    }\n  };\n\n  // 在场景初始化后调用渲染设备图标\n  useEffect(() => {\n    if (scene && typeof scene.traverse === 'function' && converter.current) {\n      renderEntranceDeviceIcons(converter.current);\n    }\n  }, [scene, converter.current]);\n\n  // 新增：每帧让所有设备图标组始终面向相机（billboard效果）\n  useEffect(() => {\n    if (!scene || !cameraRef.current) return;\n    const animateBillboard = () => {\n      // 遍历所有设备图标组，让其正对相机\n      scene.children.forEach(obj => {\n        if (obj.userData && obj.userData.isEntranceDeviceIcons) {\n          obj.lookAt(cameraRef.current.position.x, obj.position.y, cameraRef.current.position.z);\n        }\n      });\n      requestAnimationFrame(animateBillboard);\n    };\n    animateBillboard();\n  }, [scene]);\n\n  // 修改点击处理函数\n  const handleMouseClick = (event, container, sceneInstance, cameraInstance) => {\n    if (!container || !sceneInstance || !cameraInstance) return;\n    const rect = container.getBoundingClientRect();\n    const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n    const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 1;\n    raycaster.params.Line.threshold = 1;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraInstance);\n    const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n    if (intersects.length > 0) {\n      for (let i = 0; i < intersects.length; i++) {\n        const obj = intersects[i].object;\n        if (obj.parent && obj.parent.userData && obj.parent.userData.isEntranceDeviceIcon) {\n          const deviceId = obj.parent.userData.deviceId;\n          console.log('devicesDatahandleMouseClick', devicesData);\n          const device = devicesData.devices.find(d => d.id === deviceId);\n          if (device) {\n            const x = event.clientX;\n            const y = event.clientY;\n            setDevicePopover({\n              visible: true,\n              deviceId,\n              position: { x, y },\n              content: renderDevicePopoverContent(device)\n            });\n            return; // 命中设备图标后直接返回\n          }\n        }\n      }\n    }\n    // ...原有红绿灯弹框逻辑保持不变...\n    // ... existing code ...\n  };\n\n\n\n\n  return (\n    <>\n      <span style={labelStyle}>区域选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择区域位置\"\n        onChange={handleIntersectionChange}\n        options={intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n        value={selectedIntersection ? selectedIntersection.name : undefined}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      \n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '4px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n            padding: '0',\n            maxWidth: '240px', // 缩小最大宽度\n            fontSize: '12px' // 缩小字体\n          }}\n        >\n          {trafficLightPopover.content}\n          <button \n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '12px',\n              cursor: 'pointer',\n              padding: '2px 6px'\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n      \n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n      {devicePopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${devicePopover.position.x}px`,\n            top: `${devicePopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1100,\n            backgroundColor: 'rgba(0, 0, 0, 0.92)',\n            color: 'white',\n            borderRadius: '6px',\n            boxShadow: '0 2px 12px rgba(0,0,0,0.35)',\n            padding: 0,\n            minWidth: 320,\n            maxWidth: 350,\n            fontSize: 13\n          }}\n        >\n          {devicePopover.content}\n          <button \n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '16px',\n              cursor: 'pointer',\n              padding: '2px 10px',\n              zIndex: 1200\n            }}\n            onClick={handleCloseDevicePopover}\n          >×</button>\n        </div>\n      )}\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 12, // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    backgroundColor: parameters.backgroundColor || { r: 255, g: 255, b: 255, a: 0.8 },\n    textColor: parameters.textColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  \n  // 设置字体\n  // context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  \n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n  \n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  \n  canvas.width = width;\n  canvas.height = height;\n  \n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n  \n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n  \n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n  \n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n  \n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n  \n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n  \n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n  \n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = { \n    text: text,\n    params: params\n  };\n  \n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    try {\n      const [ trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([\n        loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`)\n        \n    ]);\n\n\n\n    // 处理机动车模型\n    console.log('加载车辆模型...');\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n          color: 0xff0000,  // 0xff0000, //红色 0xffffff,白色\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n      }\n    });\n\n    console.log('加载非机动车模型...');\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    console.log('加载行人模型...');\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n        \n      }\n      if (child.isMesh){\n        child.castShadow = true;\n      }\n    });\n\n\n\n    // 保存行人动画数据\n    console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n    if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n      console.log('找到行人动画:', peopleGltf.animations.length, '个');\n      peopleBaseModel = peopleGltf;\n    } else {\n      console.warn('行人模型没有包含动画数据');\n    }\n\n    console.log('加载红绿灯模型...');\n      \n    // 处理红绿灯模型\n    preloadedTrafficLightModel = trafficLightGltf.scene;\n    console.log('红绿灯模型：', preloadedTrafficLightModel);\n    // 设置红绿灯模型的缩放\n    preloadedTrafficLightModel.scale.set(6, 6, 6);\n    // 保持原始材质\n    preloadedTrafficLightModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 设置材质属性\n        child.material.metalness = 0.2;\n        child.material.roughness = 0.3;\n        child.material.envMapIntensity = 1.2;\n    }\n  });\n\n    console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n      \n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n        \n        // // 尝试单独加载红绿灯模型\n        // if (!preloadedTrafficLightModel) {\n        //   console.log('正在单独加载红绿灯模型...');\n        //   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n        //   preloadedTrafficLightModel = trafficLightGltf.scene;\n        //   preloadedTrafficLightModel.scale.set(3, 3, 3);\n        //   console.log('红绿灯模型加载成功');\n        // }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  \n  try {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在1秒后自动移除\n  setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n    scene.remove(sprite);\n      }\n  }, 100);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  // console.log('添加场景事件标记:', {\n  //   位置: position,\n  //   文本: text,\n  //   颜色: color\n  // });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  \n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  \n  // 检查红绿灯模型是否已加载\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载，尝试重新加载...');\n    // 尝试重新加载红绿灯模型\n    const loader = new GLTFLoader();\n    loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)\n      .then(trafficLightGltf => {\n        preloadedTrafficLightModel = trafficLightGltf.scene;\n        preloadedTrafficLightModel.scale.set(6, 6, 6);\n        preloadedTrafficLightModel.traverse((child) => {\n          if (child.isMesh && child.material) {\n            child.material.metalness = 0.2;\n            child.material.roughness = 0.3;\n            child.material.envMapIntensity = 1.2;\n          }\n        });\n        console.log('红绿灯模型重新加载成功，开始创建红绿灯...');\n        // 重新调用创建函数\n        createTrafficLights(converterInstance);\n      })\n      .catch(error => {\n        console.error('红绿灯模型重新加载失败:', error);\n        // 如果加载失败，使用简单的替代物体\n        createFallbackTrafficLights(converterInstance);\n      });\n    return;\n  }\n  \n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersections.forEach(intersection => {\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      \n      try {\n        // 确保模型存在且可以克隆\n        if (!preloadedTrafficLightModel || !preloadedTrafficLightModel.clone) {\n          throw new Error('红绿灯模型无效或无法克隆');\n        }\n        \n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n        \n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n        \n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n        \n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n        \n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n        \n        // 设置材质属性\n        trafficLightModel.traverse(child => {\n          if (child.isMesh) {\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            child.renderOrder = 100;\n          }\n        });\n        \n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        \n        // 将红绿灯添加到场景中\n        // scene.add(trafficLightModel);\n\n        // ========== 新增：在红绿灯地面添加指北针图标 =============\n        // 创建一个平面用于显示指北针SVG图标\n        const compassTextureLoader = new THREE.TextureLoader();\n        const compassIconPath = `${BASE_URL}/images/compass.svg`; // public目录下的路径 \n        const compassMaterial = new THREE.MeshBasicMaterial({\n          map: compassTextureLoader.load(compassIconPath),\n          transparent: true,\n          opacity: 1\n        });\n        // 平面几何体，5x5米\n        const compassGeometry = new THREE.PlaneGeometry(5, 5);\n        const compassMesh = new THREE.Mesh(compassGeometry, compassMaterial);\n        // 指北针放在红绿灯正下方地面（y=0.1，略高于地面防止Z冲突）\n        compassMesh.position.set(modelPos.x+20, 1.5, -(modelPos.y+20));\n        // 指北针朝上，旋转x轴-90度\n        compassMesh.rotation.x = -Math.PI / 2;\n        // 渲染优先级高，避免被地面遮挡\n        compassMesh.renderOrder = 101;\n        // 可选：添加userData标记\n        compassMesh.userData = {\n          type: 'compassIcon',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        // 添加到场景\n        scene.add(compassMesh);\n        // ========== 指北针图标添加结束 =============\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        \n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        // createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n  \n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance) => {\n  intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({ \n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n  \n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n  \n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n  \n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n  \n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,  // 完全透明\n    depthWrite: false\n  });\n  \n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  \n  trafficLightModel.add(collider);\n  \n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n  \n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n  \n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n  \n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  trafficLightModel.add(lightMesh);\n  \n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  switch(phaseId) {\n    case '1': return '北进口左转';\n    case '2': return '北进口直行';\n    case '3': return '北进口右转';\n    case '5': return '东进口左转';\n    case '6': return '东进口直行';\n    case '7': return '东进口右转';\n    case '9': return '南进口左转';\n    case '10': return '南进口直行';\n    case '11': return '南进口右转';\n    case '13': return '西进口左转';\n    case '14': return '西进口直行';\n    case '15': return '西进口右转';\n    default: return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = (dirCode) => {\n  switch(dirCode) {\n    case 'N': return '北向南';\n    case 'S': return '南向北';\n    case 'E': return '东向西';\n    case 'W': return '西向东';\n    case 'NE': return '东北向西南';\n    case 'NW': return '西北向东南';\n    case 'SE': return '东南向西北';\n    case 'SW': return '西南向东北';\n    default: return `方向${dirCode}`;\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n  \n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n  \n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({ \n    ...prev, \n    visible: false,\n    content: null, // 清空内容\n    phases: []     // 清空相位信息\n  }));\n  \n  console.log('弹窗已关闭，所有相关资源已清理');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = (interId) => {\n  try {\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      \n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      \n      return false;\n    }\n    \n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n    \n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 创建弹出窗口内容\n    let content;\n    \n    if (stateInfo && stateInfo.phases) {\n      content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection.name} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              switch (phase.trafficLight) {\n                case 'G': lightColor = '#00ff00'; break;\n                case 'Y': lightColor = '#ffff00'; break;\n                case 'R': default: lightColor = '#ff0000'; break;\n              }\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {getPhaseDirection(phase.phaseId)}\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      content = (\n        <div style={{ padding: '8px', maxWidth: '200px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>\n          <div>路口ID: {interId}</div>\n          <div>当前无信号灯状态信息</div>\n        </div>\n      );\n    }\n    \n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2 -500;\n    const centerY = window.innerHeight / 2 -500;\n    \n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;\n    \n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: { x: centerX, y: centerY },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      \n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      \n      document.body.appendChild(popover);\n      \n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      \n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  \n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  \n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  \n  return list;\n};\n\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = (interId) => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    \n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    \n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      \n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    \n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    \n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 判断是否有相位数据\n    const hasPhaseData = stateInfo && stateInfo.phases && stateInfo.phases.length > 0;\n    \n    let content;\n    \n    // 指北针样式\n    const compassStyle = {\n      position: 'absolute',\n      top: '5px',\n      right: '25px',\n      width: '30px',\n      height: '30px',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      borderRadius: '50%',\n      background: 'rgba(0,0,0,0.1)',\n      zIndex: 10\n    };\n    \n    // 指北针组件\n    const CompassIcon = () => (\n      <div style={compassStyle}>\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          transform: 'rotate(0deg)'\n        }}>\n          <span style={{\n            color: '#ff5252',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            lineHeight: '14px'\n          }}>N</span>\n          <span style={{\n            width: 0,\n            height: 0,\n            borderLeft: '6px solid transparent',\n            borderRight: '6px solid transparent',\n            borderBottom: '10px solid #ff5252',\n            marginTop: '-2px'\n          }}></span>\n        </div>\n      </div>\n    );\n    \n    if (hasPhaseData) {\n      // 相位ID与方向/方式的映射\n      const phaseMap = {\n        '1': { dir: 'N', type: 'left' }, '2': { dir: 'N', type: 'straight' }, '3': { dir: 'N', type: 'right' },\n        '5': { dir: 'E', type: 'left' }, '6': { dir: 'E', type: 'straight' }, '7': { dir: 'E', type: 'right' },\n        '9': { dir: 'S', type: 'left' }, '10': { dir: 'S', type: 'straight' }, '11': { dir: 'S', type: 'right' },\n        '13': { dir: 'W', type: 'left' }, '14': { dir: 'W', type: 'straight' }, '15': { dir: 'W', type: 'right' }\n      };\n      \n      const typeOrder = ['left', 'straight', 'right'];\n      const colorMap = { G: '#00ff00', Y: '#ffff00', R: '#ff0000' };\n      const dirData = { N: {}, E: {}, S: {}, W: {} };\n      \n      stateInfo.phases.forEach(phase => {\n        const map = phaseMap[phase.phaseId];\n        if (map) {\n          dirData[map.dir][map.type] = {\n            color: colorMap[phase.trafficLight] || '#888',\n            remainTime: phase.remainTime\n          };\n        }\n      });\n      \n      content = (\n        <div style={{ padding: '8px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>\n          <CompassIcon />\n          <div style={{ fontWeight: 'bold', marginBottom: '8px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}灯态</div>\n          <div style={{\n            display: 'grid',\n            gridTemplateRows: '60px 60px 60px',\n            gridTemplateColumns: '60px 60px 60px',\n            justifyContent: 'center',\n            alignItems: 'center',\n            background: 'rgba(255,255,255,0.05)',\n            borderRadius: '8px',\n            margin: '0 auto',\n            position: 'relative'\n          }}>\n            {/* 北 - 左转、直行、右转箭头水平对齐，倒计时在箭头上面 */}\n            {/* <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%', paddingLeft: '5px' }}> */}\n            <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}> \n              {typeOrder.map((type, index) => {\n                // 反转左转和右转在数组中的顺序\n                let displayIndex = index;\n                if (index === 0) displayIndex = 2;\n                else if (index === 2) displayIndex = 0;\n                \n                const currentType = typeOrder[displayIndex];\n                \n                // 计算与南边对齐的样式\n                const marginStyle = {};\n                if (currentType === 'left') { // 左转箭头 (右侧显示)\n                  marginStyle.marginRight = '0px';\n                } else if (currentType === 'straight') { // 直行箭头 (中间显示)\n                  marginStyle.marginLeft = '10px';\n                  marginStyle.marginRight = '10px';\n                } else if (currentType === 'right') { // 右转箭头 (左侧显示)\n                  marginStyle.marginLeft = '0px';\n                }\n                \n                return dirData.N[currentType] && (\n                  // <div key={currentType} style={{\n                  //   display: 'flex', \n                  //   flexDirection: 'column', \n                  //   alignItems: 'center',\n                  //   ...marginStyle\n                  // }}>\n                  <div key={currentType} style={{marginRight: currentType === 'left' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>\n                    <div style={{fontSize:'14px', color: dirData.N[currentType].color, fontWeight:'bold', marginBottom: '3px'}}>{dirData.N[currentType].remainTime}</div>\n                    <span style={{color: dirData.N[currentType].color, fontSize:'20px', lineHeight: '20px'}}>\n                      {currentType === 'left' ? '\\u{1F87A}' : currentType === 'straight' ? '\\u{1F87B}' : '\\u{1F878}'}\n                    </span>\n                  </div>\n                );\n              })}\n            </div>\n            \n            {/* 南 - 左转、直行、右转箭头水平对齐，倒计时在箭头下面 */}\n            <div style={{ gridRow: 3, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}>\n              {typeOrder.map(type => dirData.S[type] && (\n                <div key={type} style={{marginRight: type === 'right' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>\n                  <span style={{color: dirData.S[type].color, fontSize:'20px', lineHeight: '20px'}}>\n                    {type === 'left' ? '\\u{1F878}' : type === 'straight' ? '\\u{1F879}' : '\\u{1F87A}'}\n                  </span>\n                  <div style={{fontSize:'14px', color: dirData.S[type].color, fontWeight:'bold', marginTop: '3px'}}>{dirData.S[type].remainTime}</div>\n                </div>\n              ))} \n            </div>\n            \n            {/* 东 - 倒计时显示在箭头右边 */}\n\n            <div style={{ gridRow: 2, gridColumn: 3, textAlign: 'center' }}>\n              {typeOrder.map((type, index) => {\n                // 反转左转和右转在数组中的顺序\n                let displayIndex = index;\n                if (index === 0) displayIndex = 2;\n                else if (index === 2) displayIndex = 0;\n                \n                const currentType = typeOrder[displayIndex];\n                \n                return dirData.E[currentType] && (\n                  <div key={currentType} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-start'}}>\n                    <span style={{color: dirData.E[currentType].color, fontSize:'20px', lineHeight: '20px'}}>\n                      {currentType === 'left' ? '\\u{1F87B}' : currentType === 'straight' ? '\\u{1F878}' : '\\u{1F879}'}\n                    </span>\n                    <div style={{\n                      fontSize:'14px', \n                      color: dirData.E[currentType].color, \n                      fontWeight:'bold', \n                      marginLeft: '5px'\n                    }}>{dirData.E[currentType].remainTime}</div>\n                  </div>\n                );\n              })}\n            </div>\n            \n            {/* 西 - 倒计时显示在箭头左边 */}\n            <div style={{ gridRow: 2, gridColumn: 1, textAlign: 'center' }}>\n              {typeOrder.map(type => dirData.W[type] && (\n                <div key={type} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}>\n                  <div style={{\n                    fontSize:'14px', \n                    color: dirData.W[type].color, \n                    fontWeight:'bold', \n                    marginRight: '5px'\n                  }}>{dirData.W[type].remainTime}</div>\n                  <span style={{color: dirData.W[type].color, fontSize:'20px', lineHeight: '20px'}}>\n                    {type === 'left' ? '\\u{1F879}' : type === 'straight' ? '\\u{1F87A}' : '\\u{1F87B}'}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n          <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      // 没有相位数据时显示的内容\n      content = (\n        <div style={{ padding: '15px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>\n          <CompassIcon />\n          <div style={{ fontWeight: 'bold', marginBottom: '10px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}</div>\n          <div style={{ \n            textAlign: 'center', \n            padding: '20px 0',\n            color: '#ff9800', \n            fontSize: '14px', \n            fontWeight: 'bold',\n            background: 'rgba(255,255,255,0.1)',\n            borderRadius: '8px',\n            marginBottom: '10px'\n          }}>\n            当前无信号灯状态信息\n          </div>\n          <div style={{ fontSize: '12px', color: '#888', textAlign: 'center' }}>\n            路口ID: {interId}\n          </div>\n          <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    }\n    \n    // 设置弹窗位置在左上角区域\n    const x = 445;\n    const y = 250;\n    \n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n    \n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n    \n    // 更新弹窗状态\n    if (window._setTrafficLightPopover) {\n      window._setTrafficLightPopover({\n        visible: true,\n        interId: interId,\n        position: { x, y },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      // 设置定时更新\n      if (window.trafficLightUpdateTimerRef) {\n        window.trafficLightUpdateTimerRef.current = setInterval(() => {\n          window.showTrafficLightPopup(interId);\n        }, 1000);\n      }\n      \n      return true;\n    } else {\n      console.error('无法找到setTrafficLightPopover函数');\n      return false;\n    }\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n  \n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n  \n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  \n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n    \n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n    \n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n    \n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n    \n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    \n    console.log('归一化坐标:', mouseX, mouseY);\n    \n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    \n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n    \n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n    \n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    \n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', \n                    '距离:', intersect.distance,\n                    'position:', intersect.object.position.toArray(),\n                    'userData:', intersect.object.userData);\n        \n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      \n      return true;\n    }\n    \n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    \n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', \n                  '类型:', obj.type,\n                  '位置:', obj.position.toArray(),\n                  '距离:', intersect.distance,\n                  'userData:', obj.userData);\n    });\n    \n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    \n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n        \n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n        \n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n        \n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        \n        if (isVisible) {\n          visibleCount++;\n        }\n        \n        console.log(`红绿灯 ${interId}:`, {\n          名称: lightObj.intersection?.name || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    \n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n    \n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  \n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch(phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n  \n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ \n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: trafficLight.intersection?.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n  \n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = { isLight: true };\n  \n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  \n  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\n\nexport default CampusModel; \n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAO,KAAKC,aAAa,MAAM,2CAA2C;AAG1E,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACtC,OAAOC,KAAK,MAAM,OAAO,CAAC,CAAC;AAC3B,OAAOC,WAAW,MAAM,eAAe,CAAC,CAAC;AACzC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,0BAA0B,GAAG,IAAI,CAAC,CAAC;AACvC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB,IAAIC,eAAe,GAAE,IAAI,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAE,IAAI;;AAElB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAEpB;AACA,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,MAAMC,oBAAoB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;;AAKxC;AACA,MAAME,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACV;EACEC,GAAG,EAAE,2BAA2B;EAChCC,GAAG,EAAE,2BAA2B;EAChChB,KAAK,EAAE,6BAA6B;EACtCiB,GAAG,EAAE,2BAA2B;EAAG;EACnCC,IAAI,EAAE,4BAA4B,CAAE;AACtC,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AACzEC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;;AAE1D;AACA,MAAMG,aAAa,GAAG,IAAIlB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,IAAImB,gBAAgB,GAAG,IAAInB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAElC;AACA,IAAIoB,gBAAgB,GAAG,IAAI;;AAE3B;AACA,IAAIC,gBAAgB,GAAG,IAAIrB,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,IAAIsB,kBAAkB,GAAG,IAAItB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEpC,MAAMuB,KAAK,GAAG,IAAI1D,KAAK,CAAC2D,KAAK,CAAC,CAAC;;AAE/B;AACA,MAAMC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGf,QAAQ,oBAAoB,CAAC;IAC7D,MAAMgB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,IAAIA,IAAI,CAACE,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;MACzD,MAAMG,WAAW,GAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,IAAI,CAAC;MACrE,IAAIH,WAAW,IAAIA,WAAW,CAACI,KAAK,EAAE;QACpCjB,gBAAgB,GAAGa,WAAW,CAACI,KAAK;QACpCrB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEG,gBAAgB,CAAC;QAC7C,OAAOA,gBAAgB;MACzB;IACF;IACAJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChCG,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjClB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB;AACF,CAAC;;AAED;AACA,MAAMmB,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;EAC5C;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAI,CAACjD,YAAY,EAAE;MACjBA,YAAY,GAAGgD,MAAM,CAACE,KAAK,CAAC,CAAC;MAC7B,OAAOF,MAAM;IACf;IAEA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEpD,YAAY,CAACoD,CAAC,EAAElD,KAAK,CAAC;IAChE,MAAMmD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEtD,YAAY,CAACsD,CAAC,EAAEpD,KAAK,CAAC;IAChE,MAAMqD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAExD,YAAY,CAACwD,CAAC,EAAEtD,KAAK,CAAC;IAEhEF,YAAY,CAACyD,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;IACjD,OAAOvD,YAAY,CAACkD,KAAK,CAAC,CAAC;EAC3B;;EAEA;EACA,IAAI,CAAC/C,oBAAoB,CAACuD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC9C,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;EAEA,MAAMW,OAAO,GAAGxD,oBAAoB,CAACyD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,MAAMY,QAAQ,GAAGF,OAAO,CAACG,UAAU,CAACd,MAAM,CAAC;EAC3C,MAAMe,sBAAsB,GAAG,EAAE,CAAC,CAAC;;EAEnC,IAAIF,QAAQ,GAAGE,sBAAsB,EAAE;IACrC3C,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAUY,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAClE7D,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;;EAEA;EACA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEO,OAAO,CAACP,CAAC,EAAElD,KAAK,CAAC;EAC3D,MAAMmD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEK,OAAO,CAACL,CAAC,EAAEpD,KAAK,CAAC;EAC3D,MAAMqD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAEG,OAAO,CAACH,CAAC,EAAEtD,KAAK,CAAC;EAE3D,MAAM+D,WAAW,GAAG,IAAIhG,KAAK,CAACiG,OAAO,CAACf,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACtEpD,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAEgB,WAAW,CAACf,KAAK,CAAC,CAAC,CAAC;EAExD,OAAOe,WAAW;AACpB,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,WAAW,EAAEnB,SAAS,KAAK;EACjD;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAIhD,YAAY,KAAK,IAAI,EAAE;MACzBA,YAAY,GAAGmE,WAAW;MAC1B,OAAOA,WAAW;IACpB;;IAEA;IACA,IAAIC,IAAI,GAAGD,WAAW,GAAGnE,YAAY;IACrC,IAAIoE,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IAExC,MAAMC,gBAAgB,GAAG7B,aAAa,CAAC1C,YAAY,GAAGoE,IAAI,EAAEpE,YAAY,EAAEC,KAAK,CAAC;IAChFD,YAAY,GAAGuE,gBAAgB;IAC7B,OAAOA,gBAAgB;EACzB;;EAEA;EACA,IAAI,CAACnE,oBAAoB,CAACqD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC5C,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMK,OAAO,GAAGpE,oBAAoB,CAACuD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,IAAIoB,IAAI,GAAGD,WAAW,GAAGK,OAAO;EAChC,IAAIJ,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;;EAExC;EACA,MAAMG,mBAAmB,GAAGJ,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,CAAC;EACzC,IAAID,IAAI,CAACK,GAAG,CAACN,IAAI,CAAC,GAAGK,mBAAmB,EAAE;IACxCtD,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAU,CAACoB,IAAI,GAAG,GAAG,GAAGC,IAAI,CAACC,EAAE,EAAEP,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAChF3D,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMI,gBAAgB,GAAG7B,aAAa,CAAC8B,OAAO,GAAGJ,IAAI,EAAEI,OAAO,EAAEvE,KAAK,CAAC;EACtEG,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEuB,gBAAgB,CAAC;EAErD,OAAOA,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA,MAAMI,6BAA6B,GAAG,CAAC,CAAC,CAAC;AACzC;;AAEA;AACA,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;AACjC,MAAMC,QAAQ,GAAG,IAAI3E,GAAG,CAAC,CAAC;;AAE1B;AACA,MAAM4E,eAAe,GAAG;EACtBC,MAAM,EAAE,IAAIH,GAAG,CAAC,CAAC;EACjBI,KAAK,EAAE,IAAI9E,GAAG,CAAC,CAAC;EAChB+E,OAAO,EAAE,IAAI/E,GAAG,CAAC,CAAC;EAClBgF,MAAM,EAAE,IAAIN,GAAG,CAAC,CAAC;EAEjBO,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACrB,IAAI,CAACN,MAAM,CAACO,GAAG,CAACF,KAAK,CAAC;IACtB,IAAIC,KAAK,EAAE;MACT,IAAI,CAACH,MAAM,CAACI,GAAG,CAACD,KAAK,CAAC;MACtB;MACAA,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAI;QACvB,IAAIA,MAAM,CAACC,MAAM,EAAE;UACjB,IAAI,CAACT,KAAK,CAACzB,GAAG,CAACiC,MAAM,CAACE,IAAI,EAAEF,MAAM,CAAC;QACrC;MACF,CAAC,CAAC;IACJ;IACA,OAAOJ,KAAK;EACd,CAAC;EAEDO,SAASA,CAACC,MAAM,EAAER,KAAK,EAAE;IACvB,IAAI,CAAC,IAAI,CAACH,OAAO,CAACzB,GAAG,CAAC4B,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACH,OAAO,CAAC1B,GAAG,CAAC6B,KAAK,EAAE,IAAIR,GAAG,CAAC,CAAC,CAAC;IACpC;IACA,IAAI,CAACK,OAAO,CAACvB,GAAG,CAAC0B,KAAK,CAAC,CAACE,GAAG,CAACM,MAAM,CAAC;IACnC,OAAOA,MAAM;EACf,CAAC;EAEDC,WAAWA,CAACT,KAAK,EAAE;IACjB,IAAI,IAAI,CAACL,MAAM,CAACvB,GAAG,CAAC4B,KAAK,CAAC,EAAE;MAC1B,IAAI;QACF;QACA,IAAI,IAAI,CAACH,OAAO,CAACzB,GAAG,CAAC4B,KAAK,CAAC,EAAE;UAC3B,IAAI,CAACH,OAAO,CAACvB,GAAG,CAAC0B,KAAK,CAAC,CAACU,OAAO,CAACF,MAAM,IAAI;YACxC,IAAIA,MAAM,IAAI,OAAOA,MAAM,CAACG,IAAI,KAAK,UAAU,EAAE;cAC/CH,MAAM,CAACG,IAAI,CAAC,CAAC;YACf;UACF,CAAC,CAAC;UACF,IAAI,CAACd,OAAO,CAACe,MAAM,CAACZ,KAAK,CAAC;QAC5B;;QAEA;QACA,IAAI,OAAOA,KAAK,CAACa,aAAa,KAAK,UAAU,EAAE;UAC7Cb,KAAK,CAACa,aAAa,CAAC,CAAC;QACvB;;QAEA;QACA,MAAMC,IAAI,GAAGd,KAAK,CAACe,OAAO,CAAC,CAAC;QAC5B,IAAID,IAAI,EAAE;UACR,IAAI,CAAChB,MAAM,CAACc,MAAM,CAACE,IAAI,CAAC;UACxBA,IAAI,CAACX,QAAQ,CAACC,MAAM,IAAI;YACtB,IAAIA,MAAM,IAAIA,MAAM,CAACC,MAAM,EAAE;cAC3B,IAAI,CAACT,KAAK,CAACgB,MAAM,CAACR,MAAM,CAACE,IAAI,CAAC;YAChC;YACA,IAAIF,MAAM,IAAIA,MAAM,CAACY,UAAU,EAAE;cAC/BZ,MAAM,CAACY,UAAU,CAACC,MAAM,GAAG,CAAC;YAC9B;UACF,CAAC,CAAC;;UAEF;UACA,IAAI;YACF,IAAI,OAAOjB,KAAK,CAACkB,WAAW,KAAK,UAAU,EAAE;cAC3ClB,KAAK,CAACkB,WAAW,CAACJ,IAAI,CAAC;YACzB;YAEA,IAAI,OAAOd,KAAK,CAACmB,aAAa,KAAK,UAAU,EAAE;cAC7CnB,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAEL,IAAI,CAAC;YACjC;;YAEA;YACA,IAAI,OAAOd,KAAK,CAACoB,WAAW,KAAK,UAAU,EAAE;cAC3C;cACA,MAAMC,KAAK,GAAGrB,KAAK,CAACsB,QAAQ,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;cACnEN,KAAK,CAACX,OAAO,CAACkB,IAAI,IAAI;gBACpB,IAAIA,IAAI,IAAIA,IAAI,CAACtB,IAAI,EAAE;kBACrBN,KAAK,CAACoB,WAAW,CAACQ,IAAI,CAAC;gBACzB;cACF,CAAC,CAAC;YACJ;UACF,CAAC,CAAC,OAAOC,CAAC,EAAE;YACV/F,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE8F,CAAC,CAAC;YACnC;UACF;QACF;QAEA,IAAI,CAAClC,MAAM,CAACiB,MAAM,CAACZ,KAAK,CAAC;MAC3B,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrC;QACA,IAAI,CAACuC,MAAM,CAACiB,MAAM,CAACZ,KAAK,CAAC;MAC3B;IACF;EACF,CAAC;EAED8B,OAAOA,CAAA,EAAG;IACR,IAAI;MACF;MACA,IAAI,CAACjC,OAAO,CAACa,OAAO,CAAC,CAACb,OAAO,EAAEG,KAAK,KAAK;QACvC,IAAI;UACFH,OAAO,CAACa,OAAO,CAACF,MAAM,IAAI;YACxB,IAAIA,MAAM,IAAI,OAAOA,MAAM,CAACG,IAAI,KAAK,UAAU,EAAE;cAC/CH,MAAM,CAACG,IAAI,CAAC,CAAC;YACf;UACF,CAAC,CAAC;UACFd,OAAO,CAACkC,KAAK,CAAC,CAAC;QACjB,CAAC,CAAC,OAAOF,CAAC,EAAE;UACV/F,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE8F,CAAC,CAAC;QACjC;MACF,CAAC,CAAC;MACF,IAAI,CAAChC,OAAO,CAACkC,KAAK,CAAC,CAAC;;MAEpB;MACA,IAAI,CAACpC,MAAM,CAACe,OAAO,CAACV,KAAK,IAAI;QAC3B,IAAI;UACF,IAAI,OAAOA,KAAK,CAACa,aAAa,KAAK,UAAU,EAAE;YAC7Cb,KAAK,CAACa,aAAa,CAAC,CAAC;UACvB;UAEA,MAAMC,IAAI,GAAGd,KAAK,CAACe,OAAO,CAAC,CAAC;UAC5B,IAAID,IAAI,EAAE;YACRA,IAAI,CAACX,QAAQ,CAACC,MAAM,IAAI;cACtB,IAAIA,MAAM,IAAIA,MAAM,CAACY,UAAU,EAAE;gBAC/BZ,MAAM,CAACY,UAAU,CAACC,MAAM,GAAG,CAAC;cAC9B;YACF,CAAC,CAAC;;YAEF;YACA,IAAI,OAAOjB,KAAK,CAACkB,WAAW,KAAK,UAAU,EAAE;cAC3ClB,KAAK,CAACkB,WAAW,CAACJ,IAAI,CAAC;YACzB;YAEA,IAAI,OAAOd,KAAK,CAACmB,aAAa,KAAK,UAAU,EAAE;cAC7CnB,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAEL,IAAI,CAAC;YACjC;;YAEA;YACA,IAAI;cACF,IAAId,KAAK,CAACsB,QAAQ,IAAIzE,KAAK,CAACC,OAAO,CAACkD,KAAK,CAACsB,QAAQ,CAAC,EAAE;gBACnD,MAAMD,KAAK,GAAGrB,KAAK,CAACsB,QAAQ,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;gBACnEN,KAAK,CAACX,OAAO,CAACkB,IAAI,IAAI;kBACpB,IAAIA,IAAI,IAAIA,IAAI,CAACtB,IAAI,IAAI,OAAON,KAAK,CAACoB,WAAW,KAAK,UAAU,EAAE;oBAChEpB,KAAK,CAACoB,WAAW,CAACQ,IAAI,CAAC;kBACzB;gBACF,CAAC,CAAC;cACJ;YACF,CAAC,CAAC,OAAOC,CAAC,EAAE;cACV/F,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE8F,CAAC,CAAC;YACnC;UACF;QACF,CAAC,CAAC,OAAOA,CAAC,EAAE;UACV/F,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE8F,CAAC,CAAC;QAClC;MACF,CAAC,CAAC;MACF,IAAI,CAAClC,MAAM,CAACoC,KAAK,CAAC,CAAC;;MAEnB;MACA,IAAI,CAACnC,KAAK,CAACc,OAAO,CAACsB,IAAI,IAAI;QACzB,IAAIA,IAAI,CAACC,MAAM,EAAE;UACfD,IAAI,CAACC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAC;QAC1B;QACA,IAAIA,IAAI,CAACG,MAAM,EAAEH,IAAI,CAACG,MAAM,CAACC,QAAQ,CAAC,CAAC;QACvC,IAAIJ,IAAI,CAACK,WAAW,EAAEL,IAAI,CAACK,WAAW,CAACD,QAAQ,CAAC,CAAC;MACnD,CAAC,CAAC;MACF,IAAI,CAACxC,KAAK,CAACmC,KAAK,CAAC,CAAC;;MAElB;MACA,IAAI,CAACjC,MAAM,CAACY,OAAO,CAACT,KAAK,IAAI;QAC3B,IAAIA,KAAK,CAACgC,MAAM,EAAE;UAChBhC,KAAK,CAACgC,MAAM,CAACC,MAAM,CAACjC,KAAK,CAAC;QAC5B;QACAA,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAI;UACvB,IAAIA,MAAM,CAACkC,MAAM,EAAE;YACjB,IAAIlC,MAAM,CAACmC,QAAQ,EAAE;cACnBnC,MAAM,CAACmC,QAAQ,CAACC,OAAO,CAAC,CAAC;YAC3B;YACA,IAAIpC,MAAM,CAACqC,QAAQ,EAAE;cACnB,IAAI5F,KAAK,CAACC,OAAO,CAACsD,MAAM,CAACqC,QAAQ,CAAC,EAAE;gBAClCrC,MAAM,CAACqC,QAAQ,CAAC/B,OAAO,CAAC+B,QAAQ,IAAI;kBAClC,IAAIA,QAAQ,CAAClB,GAAG,EAAEkB,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;kBACxCC,QAAQ,CAACD,OAAO,CAAC,CAAC;gBACpB,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL,IAAIpC,MAAM,CAACqC,QAAQ,CAAClB,GAAG,EAAEnB,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;gBACtDpC,MAAM,CAACqC,QAAQ,CAACD,OAAO,CAAC,CAAC;cAC3B;YACF;UACF;UACA,IAAIpC,MAAM,CAACY,UAAU,EAAE;YACrBZ,MAAM,CAACY,UAAU,CAACC,MAAM,GAAG,CAAC;UAC9B;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAACnB,MAAM,CAACiC,KAAK,CAAC,CAAC;IACrB,CAAC,CAAC,OAAO3E,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC;MACA,IAAI,CAACyC,OAAO,CAACkC,KAAK,CAAC,CAAC;MACpB,IAAI,CAACpC,MAAM,CAACoC,KAAK,CAAC,CAAC;MACnB,IAAI,CAACnC,KAAK,CAACmC,KAAK,CAAC,CAAC;MAClB,IAAI,CAACjC,MAAM,CAACiC,KAAK,CAAC,CAAC;IACrB;EACF;AACF,CAAC;;AAED;AACA,MAAMW,oBAAoB,GAAIzC,KAAK,IAAK;EACtC,MAAMD,KAAK,GAAG,IAAIrH,KAAK,CAACgK,cAAc,CAAC1C,KAAK,CAAC;EAC7C,OAAOP,eAAe,CAACK,QAAQ,CAACC,KAAK,EAAEC,KAAK,CAAC;AAC/C,CAAC;;AAED;AACA,MAAM2C,YAAY,GAAGA,CAAChB,IAAI,EAAE5B,KAAK,EAAEC,KAAK,KAAK;EAC3C,MAAMO,MAAM,GAAGR,KAAK,CAAC6C,UAAU,CAACjB,IAAI,EAAE3B,KAAK,CAAC;EAC5C,OAAOP,eAAe,CAACa,SAAS,CAACC,MAAM,EAAER,KAAK,CAAC;AACjD,CAAC;;AAED;AACA,MAAM8C,mBAAmB,GAAG,IAAItD,GAAG,CAAC,CAAC;AAErC,MAAMuD,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC,kBAAkB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAMC,YAAY,GAAG5K,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM6K,UAAU,GAAG7K,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM8K,SAAS,GAAG9K,MAAM,CAAC,IAAIM,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMyK,aAAa,GAAG/K,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMgL,eAAe,GAAGhL,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMiL,aAAa,GAAGjL,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMkL,iBAAiB,GAAGlL,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMmL,MAAM,GAAGnL,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAMoL,kBAAkB,GAAGpL,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMqL,gBAAgB,GAAGrL,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMsL,eAAe,GAAG,IAAI,CAAC,CAAC;;EAE9B;EACA,MAAMC,oBAAoB,GAAGvL,MAAM,CAACwL,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC/C,MAAMC,qBAAqB,GAAG,IAAIpJ,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC,MAAMqJ,gBAAgB,GAAG3L,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;;EAGrC;EACA,MAAM,CAAC4L,WAAW,EAAEC,cAAc,CAAC,GAAG5L,QAAQ,CAAC;IAAE6L,OAAO,EAAE;EAAG,CAAC,CAAC;;EAE/D;EACA,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAI;MACF;MACA,MAAMC,MAAM,GAAG/I,QAAQ;MACvB,MAAMc,QAAQ,GAAG,MAAMpD,KAAK,CAACkF,GAAG,CAAC,GAAGmG,MAAM,cAAc,CAAC;MACzD,IAAIjI,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACgI,OAAO,EAAE;QAC1CF,YAAY,GAAGhI,QAAQ,CAACE,IAAI,CAACA,IAAI;MACnC;MAEAZ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE0I,MAAM,CAAC;MACzC3I,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEyI,YAAY,CAAC;IAC3C,CAAC,CAAC,OAAOpH,KAAK,EAAE;MACd,IAAI;QACF,MAAMZ,QAAQ,GAAG,MAAMC,KAAK,CAAC,wBAAwB,CAAC;QACtD,MAAME,IAAI,GAAG,MAAMH,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClC6H,YAAY,GAAG7H,IAAI,CAAC2H,OAAO,IAAI,EAAE;MACnC,CAAC,CAAC,OAAOzC,CAAC,EAAE;QACV/F,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEyE,CAAC,CAAC;MAC9B;IACF;IACAwC,cAAc,CAAC;MAAEC,OAAO,EAAEE;IAAa,CAAC,CAAC;EAC3C,CAAC;EACDjM,SAAS,CAAC,MAAM;IAAEgM,eAAe,CAAC,CAAC;EAAE,CAAC,EAAE,EAAE,CAAC;;EAE5C;EACC,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGnM,QAAQ,CAAC,EAAE,CAAC;EACtD;EACAF,SAAS,CAAC,MAAM;IACd,MAAMsM,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACF,MAAMJ,MAAM,GAAG9I,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;QACvE,MAAMW,QAAQ,GAAG,MAAMpD,KAAK,CAACkF,GAAG,CAAC,GAAGmG,MAAM,oBAAoB,CAAC;QAC/D,IAAIjI,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACgI,OAAO,EAAE;UAC1CE,gBAAgB,CAACpI,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOU,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC;IACDyH,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAGN;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtM,QAAQ,CAAC;IAC/CuM,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5M,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAM6M,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,IAAI;IAAG;IACfC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG/N,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAACgO,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhO,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAACiO,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlO,QAAQ,CAAC;IAC7DmO,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,IAAI;IACbtB,QAAQ,EAAE;MAAEzH,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxB8I,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAGxO,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMyO,0BAA0B,GAAGzO,MAAM,CAAC,IAAI,CAAC;;EAE/C;EACA0C,MAAM,CAACgM,uBAAuB,GAAGP,sBAAsB;;EAEvD;EACAzL,MAAM,CAAC8L,mBAAmB,GAAGA,mBAAmB;EAChD9L,MAAM,CAAC+L,0BAA0B,GAAGA,0BAA0B;;EAE9D;EACA,MAAME,uBAAuB,GAAG;IAC9B5B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IAAG;IACd3B,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7B2B,KAAK,EAAE,OAAO;IAAG;IACjB1B,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMiB,UAAU,GAAG;IACjB/B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACX3B,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBwB,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbpB,QAAQ,EAAE,MAAM;IAChBqB,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvC/B,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM,CAACxJ,gBAAgB,CAAC,GAAG1D,QAAQ,CAAC,IAAIqC,GAAG,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAAC6M,UAAU,EAAEC,aAAa,CAAC,GAAGnP,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACwD,gBAAgB,EAAE4L,mBAAmB,CAAC,GAAGpP,QAAQ,CAAC,IAAIqC,GAAG,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACgN,WAAW,EAAEC,cAAc,CAAC,GAAGtP,QAAQ,CAAC;IAAEuP,KAAK,EAAE,EAAE;IAAElB,OAAO,EAAE;EAAG,CAAC,CAAC;;EAE1E;EACA,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzP,QAAQ,CAAC;IACjDmO,OAAO,EAAE,KAAK;IACduB,QAAQ,EAAE,IAAI;IACd5C,QAAQ,EAAE;MAAEzH,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxB8I,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAMsB,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,gBAAgB,CAAC;MAAEtB,OAAO,EAAE,KAAK;MAAEuB,QAAQ,EAAE,IAAI;MAAE5C,QAAQ,EAAE;QAAEzH,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC;MAAE8I,OAAO,EAAE;IAAK,CAAC,CAAC;EAC/F,CAAC;;EAED;EACA,MAAMuB,0BAA0B,GAAIC,MAAM,IAAK;IAC7C,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;IACxB,MAAMC,QAAQ,GAAGD,MAAM,CAACE,IAAI,KAAK,QAAQ;IACzC,MAAMC,OAAO,GAAG,GAAG/M,QAAQ,WAAW4M,MAAM,CAACE,IAAI,MAAM;IACvD;IACA,MAAME,OAAO,GAAG/M,OAAO,CAACC,GAAG,CAAC+M,iBAAiB,IAAI,uBAAuB;IACxE,MAAMC,MAAM,GAAGN,MAAM,CAACO,OAAO,GAAG,GAAGH,OAAO,SAASJ,MAAM,CAACQ,EAAE,MAAM,GAAG,IAAI;IACzEhN,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE6M,MAAM,CAAC;IAC7B9M,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEuM,MAAM,CAAC;IAC7B,oBACE/O,OAAA;MAAKwP,KAAK,EAAE;QAAEhD,OAAO,EAAE,MAAM;QAAEsB,KAAK,EAAE,GAAG;QAAE2B,QAAQ,EAAE;MAAI,CAAE;MAAAC,QAAA,gBACzD1P,OAAA;QAAKwP,KAAK,EAAE;UAAEG,YAAY,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAF,QAAA,EAClDV,QAAQ,GACPD,MAAM,CAACO,OAAO,gBACZtP,OAAA;UAAKwP,KAAK,EAAE;YAAE1B,KAAK,EAAE,GAAG;YAAE+B,MAAM,EAAE,GAAG;YAAEC,UAAU,EAAE,MAAM;YAAEC,MAAM,EAAE,QAAQ;YAAEpD,YAAY,EAAE,CAAC;YAAEqD,QAAQ,EAAE;UAAS,CAAE;UAAAN,QAAA,eAEjH1P,OAAA,CAACF,WAAW;YAAC8O,QAAQ,EAAEG,MAAM,CAACQ,EAAG;YAACD,OAAO,EAAEP,MAAM,CAACO;UAAQ;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,gBAENpQ,OAAA;UAAKwP,KAAK,EAAE;YAAE1B,KAAK,EAAE,GAAG;YAAE+B,MAAM,EAAE,GAAG;YAAEC,UAAU,EAAE,MAAM;YAAE7B,KAAK,EAAE,MAAM;YAAED,UAAU,EAAE,OAAO;YAAErB,YAAY,EAAE,CAAC;YAAEoD,MAAM,EAAE;UAAS,CAAE;UAAAL,QAAA,EAAC;QAAI;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAC9I,gBAEDpQ,OAAA;UAAKqQ,GAAG,EAAEnB,OAAQ;UAACoB,GAAG,EAAEvB,MAAM,CAACE,IAAK;UAACO,KAAK,EAAE;YAAE1B,KAAK,EAAE,GAAG;YAAE+B,MAAM,EAAE,GAAG;YAAEU,SAAS,EAAE,SAAS;YAAET,UAAU,EAAE,MAAM;YAAEnD,YAAY,EAAE,CAAC;YAAEG,SAAS,EAAE;UAA4B;QAAE;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC9K;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNpQ,OAAA;QAAKwP,KAAK,EAAE;UAAE3C,QAAQ,EAAE,EAAE;UAAEqB,UAAU,EAAE,MAAM;UAAEyB,YAAY,EAAE;QAAE,CAAE;QAAAD,QAAA,EAAEX,MAAM,CAACyB;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtFpQ,OAAA;QAAKwP,KAAK,EAAE;UAAE3C,QAAQ,EAAE,EAAE;UAAEoB,KAAK,EAAE,MAAM;UAAE0B,YAAY,EAAE;QAAE,CAAE;QAAAD,QAAA,GAAC,oBAAG,EAACX,MAAM,CAACE,IAAI;MAAA;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpFpQ,OAAA;QAAKwP,KAAK,EAAE;UAAE3C,QAAQ,EAAE,EAAE;UAAEoB,KAAK,EAAE,MAAM;UAAE0B,YAAY,EAAE;QAAE,CAAE;QAAAD,QAAA,GAAC,oBAAG,EAACX,MAAM,CAACnN,QAAQ,EAAC,GAAC,EAACmN,MAAM,CAAC0B,QAAQ,GAAG,IAAI1B,MAAM,CAAC0B,QAAQ,GAAG,GAAG,EAAE;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACxIpQ,OAAA;QAAKwP,KAAK,EAAE;UAAE3C,QAAQ,EAAE,EAAE;UAAEoB,KAAK,EAAE,MAAM;UAAE0B,YAAY,EAAE;QAAE,CAAE;QAAAD,QAAA,GAAC,oBAAG,EAACX,MAAM,CAAC2B,MAAM,KAAK,QAAQ,gBAAG1Q,OAAA;UAAMwP,KAAK,EAAE;YAAEvB,KAAK,EAAE;UAAU,CAAE;UAAAyB,QAAA,EAAC;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,gBAAGpQ,OAAA;UAAMwP,KAAK,EAAE;YAAEvB,KAAK,EAAE;UAAU,CAAE;UAAAyB,QAAA,EAAC;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAChMrB,MAAM,CAAC4B,SAAS,iBAAI3Q,OAAA;QAAKwP,KAAK,EAAE;UAAE3C,QAAQ,EAAE,EAAE;UAAEoB,KAAK,EAAE,MAAM;UAAE0B,YAAY,EAAE;QAAE,CAAE;QAAAD,QAAA,GAAC,UAAG,EAACX,MAAM,CAAC4B,SAAS;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAC7GrB,MAAM,CAAC6B,YAAY,iBAAI5Q,OAAA;QAAKwP,KAAK,EAAE;UAAE3C,QAAQ,EAAE,EAAE;UAAEoB,KAAK,EAAE,MAAM;UAAE0B,YAAY,EAAE;QAAE,CAAE;QAAAD,QAAA,GAAC,oBAAG,EAACX,MAAM,CAAC6B,YAAY;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EACnHrB,MAAM,CAACrI,KAAK,iBAAI1G,OAAA;QAAKwP,KAAK,EAAE;UAAE3C,QAAQ,EAAE,EAAE;UAAEoB,KAAK,EAAE,MAAM;UAAE0B,YAAY,EAAE;QAAE,CAAE;QAAAD,QAAA,GAAC,oBAAG,EAACX,MAAM,CAACrI,KAAK;MAAA;QAAAuJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EACrGrB,MAAM,CAAC8B,WAAW,iBAAI7Q,OAAA;QAAKwP,KAAK,EAAE;UAAE3C,QAAQ,EAAE,EAAE;UAAEoB,KAAK,EAAE,MAAM;UAAE0B,YAAY,EAAE;QAAE,CAAE;QAAAD,QAAA,GAAC,oBAAG,EAACX,MAAM,CAAC8B,WAAW;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/G,CAAC;EAEV,CAAC;;EAED;EACA,MAAMU,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIpQ,UAAU,KAAK,QAAQ,EAAE;MAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB9B,UAAU,GAAG,QAAQ;;MAErB;MACA2J,kBAAkB,CAAC0G,OAAO,GAAG,IAAI;MACjCzG,gBAAgB,CAACyG,OAAO,GAAG,IAAI;MAE/B,IAAIpQ,QAAQ,EAAE;QACZA,QAAQ,CAACqQ,OAAO,GAAG,KAAK;MAC1B;IACF;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIvQ,UAAU,KAAK,QAAQ,EAAE;MAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB9B,UAAU,GAAG,QAAQ;;MAErB;MACA2J,kBAAkB,CAAC0G,OAAO,GAAG,IAAI;MACjCzG,gBAAgB,CAACyG,OAAO,GAAG,IAAI;MAE/B,IAAI/D,SAAS,CAAC+D,OAAO,IAAIpQ,QAAQ,EAAE;QACjC;QACA;QACAqM,SAAS,CAAC+D,OAAO,CAAC/E,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC,MAAMsM,UAAU,GAAGlE,SAAS,CAAC+D,OAAO,CAAC/E,QAAQ,CAAC3H,KAAK,CAAC,CAAC;QACrD,MAAM8M,SAAS,GAAGnE,SAAS,CAAC+D,OAAO,CAACK,EAAE,CAAC/M,KAAK,CAAC,CAAC;;QAE9C;QACA,IAAI7E,KAAK,CAAC6R,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC;UAAE/M,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,GAAG;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAChC4M,MAAM,CAAC/R,KAAK,CAACgS,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACd3E,SAAS,CAAC+D,OAAO,CAAC/E,QAAQ,CAAC4F,IAAI,CAACV,UAAU,CAAC;QAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;QAEV;QACA,IAAIrS,KAAK,CAAC6R,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;UAAE/M,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAC9B4M,MAAM,CAAC/R,KAAK,CAACgS,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACd3E,SAAS,CAAC+D,OAAO,CAACK,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC;QACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC;;QAEV;QACAlR,QAAQ,CAACmR,MAAM,CAAClN,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B,MAAMmN,aAAa,GAAGpR,QAAQ,CAACmR,MAAM,CAACzN,KAAK,CAAC,CAAC;;QAE7C;QACA,IAAI7E,KAAK,CAAC6R,KAAK,CAACU,aAAa,CAAC,CAC3BT,EAAE,CAAC;UAAE/M,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAC9B4M,MAAM,CAAC/R,KAAK,CAACgS,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdhR,QAAQ,CAACmR,MAAM,CAACF,IAAI,CAACG,aAAa,CAAC;UACnC;UACA/E,SAAS,CAAC+D,OAAO,CAACiB,MAAM,CAACrR,QAAQ,CAACmR,MAAM,CAAC;UACzCnR,QAAQ,CAACsR,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;QAEV;QACAlR,QAAQ,CAACqQ,OAAO,GAAG,IAAI;;QAEvB;QACArQ,QAAQ,CAACuR,WAAW,GAAG,EAAE;QACzBvR,QAAQ,CAACwR,WAAW,GAAG,GAAG;QAC1BxR,QAAQ,CAACyR,aAAa,GAAG3M,IAAI,CAACC,EAAE,GAAG,GAAG;QACtC/E,QAAQ,CAAC0R,aAAa,GAAG,CAAC;QAC1B1R,QAAQ,CAACsR,MAAM,CAAC,CAAC;QACjB;QACAjF,SAAS,CAAC+D,OAAO,CAACuB,YAAY,CAAC,CAAC;QAChCtF,SAAS,CAAC+D,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;QAEzChQ,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;UACrBgQ,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAGzH,aAAa,CAAC3H,IAAI,CAACqP,CAAC,IAAIA,CAAC,CAACtC,IAAI,KAAKoC,KAAK,CAAC;IAE9D,IAAIC,YAAY,IAAI7F,SAAS,CAAC+D,OAAO,IAAIpQ,QAAQ,EAAE;MACjDuM,uBAAuB,CAAC2F,YAAY,CAAC;;MAErC;MACA,MAAME,WAAW,GAAGhJ,SAAS,CAACgH,OAAO,CAACiC,YAAY,CAChDC,UAAU,CAACJ,YAAY,CAACpH,SAAS,CAAC,EAClCwH,UAAU,CAACJ,YAAY,CAACnH,QAAQ,CAClC,CAAC;MAEDnJ,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvB0Q,IAAI,EAAEL,YAAY,CAACrC,IAAI;QACvB2C,GAAG,EAAE;UACH1H,SAAS,EAAEoH,YAAY,CAACpH,SAAS;UACjCC,QAAQ,EAAEmH,YAAY,CAACnH;QACzB,CAAC;QACD0H,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACArS,UAAU,GAAG,cAAc;MAC3BoL,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAkB,SAAS,CAAC+D,OAAO,CAAC/E,QAAQ,CAACpH,GAAG,CAACmO,WAAW,CAACxO,CAAC,GAAC,EAAE,EAAE,EAAE,EAAE,CAACwO,WAAW,CAACtO,CAAC,GAAC,EAAE,CAAC;;MAEvE;MACA9D,QAAQ,CAACmR,MAAM,CAAClN,GAAG,CAACmO,WAAW,CAACxO,CAAC,EAAE,CAAC,EAAE,CAACwO,WAAW,CAACtO,CAAC,CAAC;;MAErD;MACAuI,SAAS,CAAC+D,OAAO,CAACiB,MAAM,CAACrR,QAAQ,CAACmR,MAAM,CAAC;;MAEzC;MACAnR,QAAQ,CAACqQ,OAAO,GAAG,IAAI;MACvBrQ,QAAQ,CAACsR,MAAM,CAAC,CAAC;;MAEjB;MACAjF,SAAS,CAAC+D,OAAO,CAACuB,YAAY,CAAC,CAAC;MAChCtF,SAAS,CAAC+D,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;MAEzChQ,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzB0Q,IAAI,EAAEL,YAAY,CAACrC,IAAI;QACvB6C,IAAI,EAAErG,SAAS,CAAC+D,OAAO,CAAC/E,QAAQ,CAACsH,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAE5S,QAAQ,CAACmR,MAAM,CAACwB,OAAO,CAAC,CAAC;QAC9BF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACA,IAAIF,YAAY,CAACW,eAAe,KAAK,KAAK,IAAIX,YAAY,CAACvF,OAAO,EAAE;QAClE/K,OAAO,CAACC,GAAG,CAAC,MAAMqQ,YAAY,CAACrC,IAAI,iBAAiB,CAAC;;QAErD;QACAiD,UAAU,CAAC,MAAM;UACf;UACA,IAAInG,OAAO,GAAGuF,YAAY,CAACvF,OAAO;;UAElC;UACA,IAAI3L,MAAM,CAAC+R,qBAAqB,EAAE;YAChC/R,MAAM,CAAC+R,qBAAqB,CAACpG,OAAO,CAAC;YACrC/K,OAAO,CAACC,GAAG,CAAC,SAASqQ,YAAY,CAACrC,IAAI,SAASlD,OAAO,YAAY,CAAC;UACrE,CAAC,MAAM;YACL/K,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC;UAChC;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLtB,OAAO,CAACC,GAAG,CAAC,MAAMqQ,YAAY,CAACrC,IAAI,sBAAsB,CAAC;;QAE1D;QACA,IAAI7O,MAAM,CAACgM,uBAAuB,EAAE;UAClChM,MAAM,CAACgM,uBAAuB,CAAC;YAC7BN,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAMsG,iBAAiB,GAAGA,CAAClF,KAAK,EAAEmF,OAAO,KAAK;IAC5C,IAAI;MACF,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;MAEnC;MACA,IAAInF,KAAK,KAAKhN,WAAW,CAACO,GAAG,EAAE;QAAA,IAAAgS,aAAA;QAC7B;;QAEA;QACA,MAAMC,SAAS,GAAGJ,OAAO,CAACK,GAAG;QAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;QAEnC;QACA,MAAMC,aAAa,GAAG3R,gBAAgB,CAACqC,GAAG,CAACkP,SAAS,CAAC;;QAErD;QACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;UACrD;UACA;UACA;UACA;UACA;UACA;QACF;;QAEA;QACA3R,gBAAgB,CAACkC,GAAG,CAACqP,SAAS,EAAEE,gBAAgB,CAAC;;QAEjD;QACA;QACA;QACA;QACA;;QAEA,MAAMG,YAAY,GAAG,EAAAN,aAAA,GAAAH,OAAO,CAAC1Q,IAAI,cAAA6Q,aAAA,uBAAZA,aAAA,CAAcM,YAAY,KAAI,EAAE;QACrD,MAAMC,KAAK,GAAGV,OAAO,CAAC1Q,IAAI,CAACoR,KAAK;;QAEhC;QACA,MAAM7J,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;;QAEtB;QACA4J,YAAY,CAACnN,OAAO,CAACqN,WAAW,IAAI;UAClC;UACA;UACA,MAAMjF,EAAE,GAAI0E,SAAS,GAAGO,WAAW,CAACC,SAAS;UAC7C,MAAMxF,IAAI,GAAGuF,WAAW,CAACE,WAAW;UAEpC,IAAGzF,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACE;YACA,MAAM0F,KAAK,GAAG;cACZlJ,SAAS,EAAEwH,UAAU,CAACuB,WAAW,CAACI,WAAW,CAAC;cAC9ClJ,QAAQ,EAAEuH,UAAU,CAACuB,WAAW,CAACK,UAAU,CAAC;cAC5ClJ,KAAK,EAAEsH,UAAU,CAACuB,WAAW,CAACM,SAAS,CAAC;cACxClJ,OAAO,EAAEqH,UAAU,CAACuB,WAAW,CAACO,WAAW;YAC7C,CAAC;YAED,MAAMC,QAAQ,GAAGjL,SAAS,CAACgH,OAAO,CAACiC,YAAY,CAAC2B,KAAK,CAAClJ,SAAS,EAAEkJ,KAAK,CAACjJ,QAAQ,CAAC;;YAEhF;YACA,IAAIuJ,cAAc;YAClB,QAAQhG,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRgG,cAAc,GAAGrU,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRqU,cAAc,GAAGpU,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRoU,cAAc,GAAGnU,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACF,IAAI4F,KAAK,GAAGjE,aAAa,CAACsC,GAAG,CAACwK,EAAE,CAAC;YAEjC,IAAI,CAAC7I,KAAK,IAAIuO,cAAc,EAAE;cAC1B;cACA,MAAMC,QAAQ,GAAGjG,IAAI,KAAK,GAAG,GAAGxP,aAAa,CAAC4E,KAAK,CAACvD,oBAAoB,CAAC,GAAGmU,cAAc,CAAC5Q,KAAK,CAAC,CAAC;cAClG;cACA,MAAMwL,MAAM,GAAGZ,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;cACvCiG,QAAQ,CAAClJ,QAAQ,CAACpH,GAAG,CAACoQ,QAAQ,CAACzQ,CAAC,EAAEsL,MAAM,EAAE,CAACmF,QAAQ,CAACvQ,CAAC,CAAC;cACtDyQ,QAAQ,CAACC,QAAQ,CAAC1Q,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGiP,KAAK,CAAC/I,OAAO,GAAGnG,IAAI,CAACC,EAAE,GAAG,GAAG;;cAE7D;cACA,IAAIuJ,IAAI,KAAK,GAAG,EAAE;gBAClB;gBACEiG,QAAQ,CAACE,KAAK,CAACxQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;gBAE3B;gBACA,MAAM6B,KAAK,GAAG0C,oBAAoB,CAAC+L,QAAQ,CAAC;gBAE5C,IAAIjU,eAAe,IAAIA,eAAe,CAACwG,UAAU,IAAIxG,eAAe,CAACwG,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;kBAC1F;kBACA,MAAMT,MAAM,GAAGoC,YAAY,CAACpI,eAAe,CAACwG,UAAU,CAAC,CAAC,CAAC,EAAEhB,KAAK,EAAEyO,QAAQ,CAAC;kBAC3EjO,MAAM,CAACoO,IAAI,CAAC,CAAC;gBACf;;gBAEA;gBACA1K,qBAAqB,CAAC/F,GAAG,CAAC2K,EAAE,EAAE9I,KAAK,CAAC;cACtC;cAEAzF,KAAK,CAAC2F,GAAG,CAACuO,QAAQ,CAAC;cAEnBzS,aAAa,CAACmC,GAAG,CAAC2K,EAAE,EAAE;gBACpB7I,KAAK,EAAEwO,QAAQ;gBACfI,UAAU,EAAE5K,GAAG;gBACjBuE,IAAI,EAAEA;cACN,CAAC,CAAC;YACN,CAAC,MAAM,IAAIvI,KAAK,EAAE;cACd;cACFA,KAAK,CAACA,KAAK,CAACsF,QAAQ,CAACpH,GAAG,CAACoQ,QAAQ,CAACzQ,CAAC,EAAEmC,KAAK,CAACuI,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC+F,QAAQ,CAACvQ,CAAC,CAAC;cACjFiC,KAAK,CAACA,KAAK,CAACyO,QAAQ,CAAC1Q,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGiP,KAAK,CAAC/I,OAAO,GAAGnG,IAAI,CAACC,EAAE,GAAG,GAAG;cAChEgB,KAAK,CAAC4O,UAAU,GAAG5K,GAAG;cACtBhE,KAAK,CAACA,KAAK,CAAC4L,YAAY,CAAC,CAAC;cAC1B5L,KAAK,CAACA,KAAK,CAAC6L,iBAAiB,CAAC,IAAI,CAAC;YACnC;UACF;QACF,CAAC,CAAC;;QAEF;QACA,MAAMgD,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAIvP,GAAG,CAACqO,YAAY,CAACtM,GAAG,CAACyN,CAAC,IAAIxB,SAAS,GAAGwB,CAAC,CAAChB,SAAS,CAAC,CAAC;QAE1EhS,aAAa,CAAC0E,OAAO,CAAC,CAACuO,SAAS,EAAEnG,EAAE,KAAK;UACvC,IAAI7E,GAAG,GAAGgL,SAAS,CAACJ,UAAU,GAAGC,iBAAiB,IAAI,CAACC,UAAU,CAAC3Q,GAAG,CAAC0K,EAAE,CAAC,EAAE;YACzE;YACA,IAAImG,SAAS,CAACzG,IAAI,KAAK,GAAG,IAAItE,qBAAqB,CAAC9F,GAAG,CAAC0K,EAAE,CAAC,EAAE;cAC3D,MAAM9I,KAAK,GAAGkE,qBAAqB,CAAC5F,GAAG,CAACwK,EAAE,CAAC;cAC3C;cACApJ,eAAe,CAACe,WAAW,CAACT,KAAK,CAAC;cAClCkE,qBAAqB,CAACtD,MAAM,CAACkI,EAAE,CAAC;YAClC;;YAEA;YACAvO,KAAK,CAAC2H,MAAM,CAAC+M,SAAS,CAAChP,KAAK,CAAC;YAC7BjE,aAAa,CAAC4E,MAAM,CAACkI,EAAE,CAAC;UAC1B;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAId,KAAK,KAAKhN,WAAW,CAACM,GAAG,EAAE;QAC7B;;QAEA,MAAM4T,OAAO,GAAG9B,OAAO,CAAC1Q,IAAI;QAC5B,MAAMyS,KAAK,GAAGD,OAAO,CAAC/R,KAAK;QAC3B,MAAMiS,QAAQ,GAAG;UACfpK,SAAS,EAAEwH,UAAU,CAAC0C,OAAO,CAACG,QAAQ,CAAC;UACvCpK,QAAQ,EAAEuH,UAAU,CAAC0C,OAAO,CAACI,OAAO,CAAC;UACrCpK,KAAK,EAAEsH,UAAU,CAAC0C,OAAO,CAACb,SAAS,CAAC;UACpClJ,OAAO,EAAEqH,UAAU,CAAC0C,OAAO,CAACZ,WAAW;QACzC,CAAC;;QAED;QACA;;QAEA;QACApT,MAAM,CAACqU,WAAW,CAAC;UACjB/G,IAAI,EAAE,iBAAiB;UACvBgH,MAAM,EAAE;QACV,CAAC,EAAE,GAAG,CAAC;;QAEP;QACAtU,MAAM,CAACqU,WAAW,CAAC;UACjB/G,IAAI,EAAE,KAAK;UACXrL,KAAK,EAAEgS,KAAK;UAAE;UACdzS,IAAI,EAAE;YAAQ;YACZS,KAAK,EAAEgS,KAAK;YACZd,SAAS,EAAEa,OAAO,CAACb,SAAS;YAC5BiB,OAAO,EAAEJ,OAAO,CAACI,OAAO;YACxBD,QAAQ,EAAEH,OAAO,CAACG,QAAQ;YAC1Bf,WAAW,EAAEY,OAAO,CAACZ;UACvB;QACF,CAAC,EAAE,GAAG,CAAC;;QAEP;QACA,MAAMC,QAAQ,GAAGjL,SAAS,CAACgH,OAAO,CAACiC,YAAY,CAAC6C,QAAQ,CAACpK,SAAS,EAAEoK,QAAQ,CAACnK,QAAQ,CAAC;QACtF,MAAMwK,eAAe,GAAG,IAAI9W,KAAK,CAACiG,OAAO,CAAC2P,QAAQ,CAACzQ,CAAC,EAAE,GAAG,EAAE,CAACyQ,QAAQ,CAACvQ,CAAC,CAAC;QACvE,MAAM0R,eAAe,GAAG1Q,IAAI,CAACC,EAAE,GAAGmQ,QAAQ,CAACjK,OAAO,GAAGnG,IAAI,CAACC,EAAE,GAAG,GAAG;;QAElE;QACA,MAAM0Q,WAAW,GAAGlS,cAAc,CAACgS,eAAe,EAAEN,KAAK,CAAC;QAC1D,MAAMrQ,WAAW,GAAGD,cAAc,CAAC6Q,eAAe,EAAEP,KAAK,CAAC;;QAE1D;QACA,IAAIS,UAAU,GAAG5T,aAAa,CAACsC,GAAG,CAAC6Q,KAAK,CAAC;;QAEzC;QACA,MAAMjS,aAAa,GAAGiS,KAAK,KAAKjT,gBAAgB;QAEhD,IAAI,CAAC0T,UAAU,IAAIzV,qBAAqB,EAAE;UACxC;UACA,MAAM0V,eAAe,GAAG1V,qBAAqB,CAACyD,KAAK,CAAC,CAAC;;UAErD;UACA;UACAiS,eAAe,CAACtK,QAAQ,CAACpH,GAAG,CAACwR,WAAW,CAAC7R,CAAC,EAAE,CAAC,CAAC,EAAE6R,WAAW,CAACzR,CAAC,CAAC;UAC9D2R,eAAe,CAACnB,QAAQ,CAAC1Q,CAAC,GAAGc,WAAW;;UAExC;UACA+Q,eAAe,CAAC1P,QAAQ,CAAE2P,KAAK,IAAK;YAClC,IAAIA,KAAK,CAACxN,MAAM,IAAIwN,KAAK,CAACrN,QAAQ,EAAE;cAClC;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;;cAEA;cACA;cACA;;cAEA;;cAEA,MAAMsN,WAAW,GAAGD,KAAK,CAACrN,QAAQ,CAAC7E,KAAK,CAAC,CAAC;cAC1CkS,KAAK,CAACrN,QAAQ,GAAGsN,WAAW;;cAE5B;cACA,IAAI7S,aAAa,EAAE;gBACjB6S,WAAW,CAACvI,KAAK,CAACrJ,GAAG,CAAC,QAAQ,CAAC;cACjC,CAAC,MAAM;gBACL4R,WAAW,CAACvI,KAAK,CAACrJ,GAAG,CAAC,QAAQ,CAAC;cACjC;cACA4R,WAAW,CAACC,QAAQ,GAAG,IAAIrX,KAAK,CAACsX,KAAK,CAAC,QAAQ,CAAC;cAChDF,WAAW,CAACG,WAAW,GAAG,IAAI;cAC9B;cACAH,WAAW,CAACI,WAAW,GAAG,IAAI;YAChC;UACF,CAAC,CAAC;;UAEF;UACA,MAAMC,UAAU,GAAGC,gBAAgB,CAAC,GAAGrR,IAAI,CAACsR,KAAK,CAAClB,QAAQ,CAAClK,KAAK,CAAC,OAAO,EAAE;YACxEc,eAAe,EAAE9I,aAAa,GAC5B;cAAEqT,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEjP,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAE+O,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAEjP,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCkP,SAAS,EAAE;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEjP,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/C4E,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFqK,UAAU,CAAC7K,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClCiS,UAAU,CAACO,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/BP,UAAU,CAAC3N,QAAQ,CAACmO,OAAO,GAAG,GAAG,CAAC,CAAC;UACnCf,eAAe,CAAC3P,GAAG,CAACkQ,UAAU,CAAC;UAE/B7V,KAAK,CAAC2F,GAAG,CAAC2P,eAAe,CAAC;;UAE1B;UACA7T,aAAa,CAACmC,GAAG,CAACgR,KAAK,EAAE;YACvBlP,KAAK,EAAE4P,eAAe;YACtBhB,UAAU,EAAE7K,IAAI,CAACC,GAAG,CAAC,CAAC;YACtBuE,IAAI,EAAE,GAAG;YAAE;YACXqI,MAAM,EAAE3T,aAAa;YACrBkT,UAAU,EAAEA,UAAU,CAAC;UACzB,CAAC,CAAC;;UAEF;;UAEA;UACA,IAAIrX,KAAK,CAAC6R,KAAK,CAACiF,eAAe,CAACtK,QAAQ,CAAC,CACtCsF,EAAE,CAAC;YAAE7M,CAAC,EAAE;UAAI,CAAC,EAAE,GAAG,CAAC,CACnB8M,MAAM,CAAC/R,KAAK,CAACgS,MAAM,CAACC,SAAS,CAAC8F,GAAG,CAAC,CAClC1F,KAAK,CAAC,CAAC;;UAEV;UACAyE,eAAe,CAAC1P,QAAQ,CAAE2P,KAAK,IAAK;YAClC,IAAIA,KAAK,CAACxN,MAAM,IAAIwN,KAAK,CAACrN,QAAQ,IAAIqN,KAAK,CAACrN,QAAQ,CAACyN,WAAW,EAAE;cAChE,IAAInX,KAAK,CAAC6R,KAAK,CAAC;gBAAEgG,OAAO,EAAE;cAAI,CAAC,CAAC,CAC9B/F,EAAE,CAAC;gBAAE+F,OAAO,EAAE;cAAI,CAAC,EAAE,GAAG,CAAC,CACzB9F,MAAM,CAAC/R,KAAK,CAACgS,MAAM,CAACC,SAAS,CAAC8F,GAAG,CAAC,CAClC5F,QAAQ,CAAC,YAAW;gBACnB4E,KAAK,CAACrN,QAAQ,CAACmO,OAAO,GAAG,IAAI,CAACA,OAAO;gBACrCd,KAAK,CAACrN,QAAQ,CAAC0N,WAAW,GAAG,IAAI;cACnC,CAAC,CAAC,CACD/E,KAAK,CAAC,CAAC;YACZ;UACF,CAAC,CAAC;;UAEF;UACA,IAAIrS,KAAK,CAAC6R,KAAK,CAAC;YAAEgG,OAAO,EAAE;UAAI,CAAC,CAAC,CAC9B/F,EAAE,CAAC;YAAE+F,OAAO,EAAE;UAAI,CAAC,EAAE,GAAG,CAAC,CACzB9F,MAAM,CAAC/R,KAAK,CAACgS,MAAM,CAACC,SAAS,CAAC8F,GAAG,CAAC,CAClC5F,QAAQ,CAAC,YAAW;YACnBkF,UAAU,CAAC3N,QAAQ,CAACmO,OAAO,GAAG,IAAI,CAACA,OAAO;YAC1CR,UAAU,CAAC3N,QAAQ,CAAC0N,WAAW,GAAG,IAAI;UACxC,CAAC,CAAC,CACD/E,KAAK,CAAC,CAAC;;UAEV;UACA,IAAIlO,aAAa,EAAE;YACjBxD,gBAAgB,GAAGmW,eAAe;YAClC9K,eAAe,CAACqK,QAAQ,CAAC;YACzBtT,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEoT,KAAK,CAAC;UACjC;QACF,CAAC,MAAM,IAAIS,UAAU,EAAE;UACrB;UACA,MAAMmB,gBAAgB,GAAGtT,cAAc,CAACkS,WAAW,EAAER,KAAK,CAAC;UAC3D,MAAMjQ,gBAAgB,GAAGL,cAAc,CAACC,WAAW,EAAEqQ,KAAK,CAAC;;UAE3D;UACAS,UAAU,CAAC3P,KAAK,CAACsF,QAAQ,CAAC4F,IAAI,CAAC4F,gBAAgB,CAAC;UAChDnB,UAAU,CAAC3P,KAAK,CAACyO,QAAQ,CAAC1Q,CAAC,GAAGkB,gBAAgB;UAC9C0Q,UAAU,CAAC3P,KAAK,CAAC4L,YAAY,CAAC,CAAC;UAC/B+D,UAAU,CAAC3P,KAAK,CAAC6L,iBAAiB,CAAC,IAAI,CAAC;UACxC8D,UAAU,CAACf,UAAU,GAAG7K,IAAI,CAACC,GAAG,CAAC,CAAC;UAClC2L,UAAU,CAACiB,MAAM,GAAG3T,aAAa,CAAC,CAAC;;UAEnC;UACA,IAAI0S,UAAU,CAACQ,UAAU,EAAE;YACzBR,UAAU,CAACQ,UAAU,CAAC3N,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;YAC5CoN,UAAU,CAAC3P,KAAK,CAACiC,MAAM,CAAC0N,UAAU,CAACQ,UAAU,CAAC;UAChD;;UAEA;UACA,MAAMA,UAAU,GAAGC,gBAAgB,CAAC,GAAGrR,IAAI,CAACsR,KAAK,CAAClB,QAAQ,CAAClK,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE;YAC9Ec,eAAe,EAAE9I,aAAa,GAC5B;cAAEqT,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEjP,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAE+O,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAEjP,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCkP,SAAS,EAAE;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEjP,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/C4E,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFqK,UAAU,CAAC7K,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACnCiS,UAAU,CAACO,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/Bf,UAAU,CAAC3P,KAAK,CAACC,GAAG,CAACkQ,UAAU,CAAC;UAChCR,UAAU,CAACQ,UAAU,GAAGA,UAAU;;UAElC;;UAEA;UACA,IAAIlT,aAAa,EAAE;YACjBxD,gBAAgB,GAAGkW,UAAU,CAAC3P,KAAK;YACnC8E,eAAe,CAACqK,QAAQ,CAAC;UAC3B;QACF;;QAEA;QACA,MAAMnL,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;QACtB,MAAM6K,iBAAiB,GAAG,IAAI,CAAC,CAAC;;QAEhC9S,aAAa,CAAC0E,OAAO,CAAC,CAACuO,SAAS,EAAEnG,EAAE,KAAK;UACvC,MAAMkI,mBAAmB,GAAG/M,GAAG,GAAGgL,SAAS,CAACJ,UAAU;;UAEtD;UACA,IAAImC,mBAAmB,GAAGlC,iBAAiB,GAAG,GAAG,IAAIkC,mBAAmB,IAAIlC,iBAAiB,EAAE;YAC7F;YACA,MAAM8B,OAAO,GAAG,CAAC;YAEjB3B,SAAS,CAAChP,KAAK,CAACE,QAAQ,CAAE2P,KAAK,IAAK;cAClC,IAAIA,KAAK,CAACxN,MAAM,IAAIwN,KAAK,CAACrN,QAAQ,EAAE;gBAClC;gBACA,IAAIqN,KAAK,CAACrN,QAAQ,CAACyN,WAAW,KAAKe,SAAS,EAAE;kBAC5CnB,KAAK,CAACrN,QAAQ,CAACyO,mBAAmB,GAAGpB,KAAK,CAACrN,QAAQ,CAACyN,WAAW,IAAI,KAAK;kBACxEJ,KAAK,CAACrN,QAAQ,CAAC0O,eAAe,GAAGrB,KAAK,CAACrN,QAAQ,CAACmO,OAAO,IAAI,GAAG;gBAChE;;gBAEA;gBACAd,KAAK,CAACrN,QAAQ,CAACyN,WAAW,GAAG,IAAI;gBACjCJ,KAAK,CAACrN,QAAQ,CAACmO,OAAO,GAAGA,OAAO;gBAChCd,KAAK,CAACrN,QAAQ,CAAC0N,WAAW,GAAG,IAAI;cACnC;YACF,CAAC,CAAC;;YAEF;YACA,IAAIlB,SAAS,CAACmB,UAAU,EAAE;cACxBnB,SAAS,CAACmB,UAAU,CAAC3N,QAAQ,CAACmO,OAAO,GAAGA,OAAO;cAC/C3B,SAAS,CAACmB,UAAU,CAAC3N,QAAQ,CAAC0N,WAAW,GAAG,IAAI;YAClD;UACF;UACA;UAAA,KACK,IAAIa,mBAAmB,GAAGlC,iBAAiB,EAAE;YAChD;YACA,IAAIG,SAAS,CAACmB,UAAU,EAAE;cACxBnB,SAAS,CAACmB,UAAU,CAAC3N,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;cAC3CyM,SAAS,CAACmB,UAAU,CAAC3N,QAAQ,CAACD,OAAO,CAAC,CAAC;cACvCyM,SAAS,CAAChP,KAAK,CAACiC,MAAM,CAAC+M,SAAS,CAACmB,UAAU,CAAC;YAC9C;YAEAnB,SAAS,CAAChP,KAAK,CAACE,QAAQ,CAAE2P,KAAK,IAAK;cAClC,IAAIA,KAAK,CAACxN,MAAM,EAAE;gBAChB,IAAIwN,KAAK,CAACrN,QAAQ,EAAE;kBAClB,IAAI5F,KAAK,CAACC,OAAO,CAACgT,KAAK,CAACrN,QAAQ,CAAC,EAAE;oBACjCqN,KAAK,CAACrN,QAAQ,CAAC/B,OAAO,CAAC0Q,CAAC,IAAIA,CAAC,CAAC5O,OAAO,CAAC,CAAC,CAAC;kBAC1C,CAAC,MAAM;oBACLsN,KAAK,CAACrN,QAAQ,CAACD,OAAO,CAAC,CAAC;kBAC1B;gBACF;gBACA,IAAIsN,KAAK,CAACvN,QAAQ,EAAEuN,KAAK,CAACvN,QAAQ,CAACC,OAAO,CAAC,CAAC;cAC9C;YACF,CAAC,CAAC;;YAEF;YACAjI,KAAK,CAAC2H,MAAM,CAAC+M,SAAS,CAAChP,KAAK,CAAC;YAC7BjE,aAAa,CAAC4E,MAAM,CAACkI,EAAE,CAAC;YACxB;YACAjO,oBAAoB,CAAC+F,MAAM,CAACkI,EAAE,CAAC;YAC/B/N,oBAAoB,CAAC6F,MAAM,CAACkI,EAAE,CAAC;YAE/BhN,OAAO,CAACC,GAAG,CAAC,mBAAmB+M,EAAE,EAAE,CAAC;UACtC;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAId,KAAK,KAAKhN,WAAW,CAACS,IAAI,EAAE;QAC9B;;QAEA,IAAI;UACF,MAAM2R,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;UAEnC;UACA,MAAMK,SAAS,GAAGJ,OAAO,CAACK,GAAG;UAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;UAEnC;UACA,MAAMC,aAAa,GAAG3R,gBAAgB,CAACqC,GAAG,CAACkP,SAAS,CAAC;;UAErD;UACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;YACrD;YACA;YACA;YACA;YACA;YACA;UACF;;UAEA;UACA3R,gBAAgB,CAACkC,GAAG,CAACqP,SAAS,EAAEE,gBAAgB,CAAC;;UAEjD;UACA,IAAIN,OAAO,CAAC1Q,IAAI,IAAI0Q,OAAO,CAAC1Q,IAAI,CAACiI,aAAa,IAAI9H,KAAK,CAACC,OAAO,CAACsQ,OAAO,CAAC1Q,IAAI,CAACiI,aAAa,CAAC,EAAE;YAC3FyI,OAAO,CAAC1Q,IAAI,CAACiI,aAAa,CAACjE,OAAO,CAAC0L,YAAY,IAAI;cACjD,MAAMvF,OAAO,GAAGuF,YAAY,CAACvF,OAAO;cAEpC,IAAI,CAACA,OAAO,EAAE;gBACZ/K,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEgP,YAAY,CAAC;gBAC/C;cACF;;cAEA;;cAEA;cACA,IAAIA,YAAY,CAACrF,MAAM,IAAIlK,KAAK,CAACC,OAAO,CAACsP,YAAY,CAACrF,MAAM,CAAC,EAAE;gBAC7D;gBACA,MAAMsK,UAAU,GAAG,EAAE;gBAErBjF,YAAY,CAACrF,MAAM,CAACrG,OAAO,CAAC4Q,KAAK,IAAI;kBACnC;kBACA,IAAI,CAACA,KAAK,CAACC,OAAO,EAAE;oBAClBzV,OAAO,CAACsB,KAAK,CAAC,gBAAgB,EAAEkU,KAAK,CAAC;oBACtC;kBACF;kBAEA,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC;kBACxC;kBACA,MAAMC,SAAS,GAAGH,KAAK,CAACI,YAAY,GAClCC,oBAAoB,CAACL,KAAK,CAACI,YAAY,CAAC,GACxCE,iBAAiB,CAACL,OAAO,CAAC;;kBAE5B;kBACA,MAAMM,UAAU,GAAGP,KAAK,CAACQ,YAAY,IAAI,GAAG,CAAC,CAAC;kBAC9C,MAAMC,UAAU,GAAGC,QAAQ,CAACV,KAAK,CAACS,UAAU,CAAC,IAAI,CAAC;;kBAElD;;kBAEA;kBACA,MAAME,SAAS,GAAG;oBAChBV,OAAO;oBACPE,SAAS;oBACTK,YAAY,EAAED,UAAU;oBACxBE;kBACF,CAAC;;kBAED;kBACAV,UAAU,CAACa,IAAI,CAACD,SAAS,CAAC;;kBAE1B;kBACA;kBACA,IAAIE,eAAe,GAAGC,MAAM,CAACvL,OAAO,CAAC;kBACrC,IAAIwL,iBAAiB,GAAGlW,gBAAgB,CAACmC,GAAG,CAAC6T,eAAe,CAAC;kBAE7D,IAAI,CAACE,iBAAiB,EAAE;oBACtB;oBACAF,eAAe,GAAGH,QAAQ,CAACnL,OAAO,CAAC;oBACnCwL,iBAAiB,GAAGlW,gBAAgB,CAACmC,GAAG,CAAC6T,eAAe,CAAC;kBAC3D;kBAEA,IAAIE,iBAAiB,EAAE;oBACrB;oBACAC,wBAAwB,CAACD,iBAAiB,EAAEJ,SAAS,CAAC;;oBAEtD;oBACA,IAAIzL,oBAAoB,IAAIA,oBAAoB,CAACK,OAAO,KAAKA,OAAO,EAAE;sBACpEF,sBAAsB,CAAC4L,IAAI,KAAK;wBAC9B,GAAGA,IAAI;wBACP3L,OAAO,EAAE,IAAI;wBACb2K,OAAO;wBACPE,SAAS;wBACTvD,KAAK,EAAE2D,UAAU;wBACjBE;sBACF,CAAC,CAAC,CAAC;oBACL;kBACF,CAAC,MAAM;oBACL;kBAAA;gBAEJ,CAAC,CAAC;;gBAEF;gBACA,IAAIS,QAAQ,GAAG,IAAI;gBACnB;gBACA,MAAMC,KAAK,GAAGL,MAAM,CAACvL,OAAO,CAAC;gBAC7B,IAAI1K,gBAAgB,CAACiC,GAAG,CAACqU,KAAK,CAAC,EAAE;kBAC/BD,QAAQ,GAAGC,KAAK;gBAClB,CAAC,MAAM;kBACL;kBACA,MAAMC,KAAK,GAAGV,QAAQ,CAACnL,OAAO,CAAC;kBAC/B,IAAI1K,gBAAgB,CAACiC,GAAG,CAACsU,KAAK,CAAC,EAAE;oBAC/BF,QAAQ,GAAGE,KAAK;kBAClB;gBACF;gBAEA,IAAIF,QAAQ,KAAK,IAAI,EAAE;kBACrB;kBACApW,kBAAkB,CAAC+B,GAAG,CAACqU,QAAQ,EAAE;oBAC/BG,UAAU,EAAE3O,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtB8C,MAAM,EAAEsK;kBACV,CAAC,CAAC;kBACFvV,OAAO,CAACC,GAAG,CAAC,aAAayW,QAAQ,KAAK,OAAOA,QAAQ,aAAa,CAAC;;kBAEnE;kBACA,IAAItX,MAAM,CAAC8L,mBAAmB,KAC1B9L,MAAM,CAAC8L,mBAAmB,CAACsD,OAAO,KAAKkI,QAAQ,IAC/CtX,MAAM,CAAC8L,mBAAmB,CAACsD,OAAO,KAAK8H,MAAM,CAACI,QAAQ,CAAC,IACvDtX,MAAM,CAAC8L,mBAAmB,CAACsD,OAAO,KAAK0H,QAAQ,CAACQ,QAAQ,CAAC,CAAC,EAAE;oBAE9D1W,OAAO,CAACC,GAAG,CAAC,eAAeyW,QAAQ,aAAa,CAAC;oBACjD;oBACAtX,MAAM,CAAC8L,mBAAmB,CAACsD,OAAO,GAAGkI,QAAQ;;oBAE7C;oBACA,IAAItX,MAAM,CAAC+L,0BAA0B,IAAI,CAAC/L,MAAM,CAAC+L,0BAA0B,CAACqD,OAAO,EAAE;sBACnFxO,OAAO,CAACC,GAAG,CAAC,SAASyW,QAAQ,aAAa,CAAC;sBAC3CxF,UAAU,CAAC,MAAM;wBACf9R,MAAM,CAAC+R,qBAAqB,CAACuF,QAAQ,CAAC;sBACxC,CAAC,EAAE,GAAG,CAAC;oBACT;kBACF;gBACF,CAAC,MAAM;kBACL;kBACApW,kBAAkB,CAAC+B,GAAG,CAAC0I,OAAO,EAAE;oBAC9B8L,UAAU,EAAE3O,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtB8C,MAAM,EAAEsK;kBACV,CAAC,CAAC;kBACF;gBACF;cACF,CAAC,MAAM;gBACLvV,OAAO,CAACsB,KAAK,CAAC,eAAe,EAAEgP,YAAY,CAAC;cAC9C;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACLtQ,OAAO,CAACsB,KAAK,CAAC,oCAAoC,EAAEgQ,OAAO,CAAC;UAC9D;QACF,CAAC,CAAC,OAAOhQ,KAAK,EAAE;UACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEA,KAAK,EAAE+P,OAAO,CAAC;QAC9C;QACA;MACF;;MAEA;MACA,IAAInF,KAAK,KAAKhN,WAAW,CAACQ,GAAG,IAAI4R,OAAO,CAAC5E,IAAI,KAAK,KAAK,EAAE;QACvD;;QAEA;QACAtN,MAAM,CAACqU,WAAW,CAAC;UACjB/G,IAAI,EAAE,KAAK;UACX9L,IAAI,EAAE0Q,OAAO,CAAC1Q,IAAI;UAClB+Q,GAAG,EAAEL,OAAO,CAACK,GAAG;UAChBE,EAAE,EAAEP,OAAO,CAACO;QACd,CAAC,EAAE,GAAG,CAAC;QAEP,MAAMiF,OAAO,GAAGxF,OAAO,CAAC1Q,IAAI;QAC5B,MAAMmW,KAAK,GAAGD,OAAO,CAACC,KAAK;QAC3B,MAAMC,MAAM,GAAGF,OAAO,CAACG,IAAI,IAAI,EAAE;QAEjCD,MAAM,CAACpS,OAAO,CAACsS,KAAK,IAAI;UACtB,MAAMC,OAAO,GAAGD,KAAK,CAACE,KAAK;UAC3B,MAAMC,SAAS,GAAGH,KAAK,CAACG,SAAS;UACjC,MAAM/I,WAAW,GAAG4I,KAAK,CAAC5I,WAAW;UACrC,MAAMgJ,SAAS,GAAGJ,KAAK,CAACI,SAAS;UACjC,MAAMC,OAAO,GAAGL,KAAK,CAACK,OAAO;;UAE7B;UACA,MAAM9E,QAAQ,GAAGjL,SAAS,CAACgH,OAAO,CAACiC,YAAY,CAC7CC,UAAU,CAACoG,OAAO,CAACU,OAAO,CAAC,EAC3B9G,UAAU,CAACoG,OAAO,CAACW,MAAM,CAC3B,CAAC;;UAED;UACA,IAAIC,WAAW,GAAG,EAAE;UACpB,IAAIC,YAAY,GAAG,EAAE;UAErB,QAAON,SAAS;YACd,KAAK,KAAK;cAAG;cACXK,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,QAAQ;cACtBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,MAAM;cAAE;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF;cACED,WAAW,GAAGpJ,WAAW,IAAI,MAAM;cACnCqJ,YAAY,GAAG,SAAS;UAC5B;;UAEA;UACAC,iBAAiB,CAACnF,QAAQ,EAAEiF,WAAW,EAAEC,YAAY,CAAC;;UAEtD;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAIzL,KAAK,KAAKhN,WAAW,CAACT,KAAK,IAAI6S,OAAO,CAAC5E,IAAI,KAAK,OAAO,EAAE;QAC3D;;QAEA,MAAMmL,SAAS,GAAGvG,OAAO,CAAC1Q,IAAI;QAC9B,MAAMkX,OAAO,GAAGD,SAAS,CAACC,OAAO;QACjC,MAAMC,SAAS,GAAGF,SAAS,CAACE,SAAS;QACrC,MAAMC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAMvO,QAAQ,GAAG;UACfN,QAAQ,EAAEuH,UAAU,CAACmH,SAAS,CAACrE,OAAO,CAAC;UACvCtK,SAAS,EAAEwH,UAAU,CAACmH,SAAS,CAACtE,QAAQ;QAC1C,CAAC;;QAED;QACA,MAAMd,QAAQ,GAAGjL,SAAS,CAACgH,OAAO,CAACiC,YAAY,CAAChH,QAAQ,CAACP,SAAS,EAAEO,QAAQ,CAACN,QAAQ,CAAC;;QAEtF;QACA,QAAO4O,SAAS;UACd,KAAK,GAAG;YAAG;YACTH,iBAAiB,CAACnF,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;YAClD;UACF,KAAK,KAAK;YAAG;YACXmF,iBAAiB,CAACnF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACXmF,iBAAiB,CAACnF,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;YAC/C;UACF,KAAK,IAAI;YAAG;YACV,MAAMwF,UAAU,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAE;YAC1CN,iBAAiB,CAACnF,QAAQ,EAAE,KAAKwF,UAAU,MAAM,EAAE,SAAS,CAAC;YAC7D;UACF,KAAK,IAAI;YAAG;YACVL,iBAAiB,CAACnF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVmF,iBAAiB,CAACnF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YAAG;YACZmF,iBAAiB,CAACnF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVmF,iBAAiB,CAACnF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVmF,iBAAiB,CAACnF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX,MAAM0F,YAAY,GAAGN,SAAS,CAACK,UAAU,CAAC,CAAE;YAC5C,MAAME,QAAQ,GAAGP,SAAS,CAACQ,UAAU,CAAC,CAAM;YAC5CT,iBAAiB,CAACnF,QAAQ,EAAE,QAAQ6F,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,EAAE,SAAS,CAAC;YAC/F;QACJ;QAEA;MACF;MACA;MACA;MACA;MACA;MACA;MACA;IAEF,CAAC,CAAC,OAAO9W,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAE+P,OAAO,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMkH,cAAc,GAAGA,CAAA,KAAM;IAC3BvY,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAMuY,KAAK,GAAG,QAAQtZ,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnES,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEuY,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChB3Y,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAEDwY,EAAE,CAACG,SAAS,GAAI1B,KAAK,IAAK;MACxB,IAAI;QACF,MAAM7F,OAAO,GAAGE,IAAI,CAACC,KAAK,CAAC0F,KAAK,CAACtW,IAAI,CAAC;;QAEtC;QACA,IAAIyQ,OAAO,CAAC3E,IAAI,KAAK,SAAS,EAAE;UAC9B1M,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEoR,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAAC3E,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAI2E,OAAO,CAAC3E,IAAI,KAAK,SAAS,IAAI2E,OAAO,CAACnF,KAAK,IAAImF,OAAO,CAACC,OAAO,EAAE;UAClE;UACA,IAAID,OAAO,CAACwH,SAAS,EAAE;YACrB,IAAI7R,mBAAmB,CAAC1E,GAAG,CAAC+O,OAAO,CAACwH,SAAS,CAAC,EAAE;cAC9C;cACA;YACF;;YAEA;YACA7R,mBAAmB,CAAC5C,GAAG,CAACiN,OAAO,CAACwH,SAAS,CAAC;;YAE1C;YACA,IAAI7R,mBAAmB,CAAC8R,IAAI,GAAG,IAAI,EAAE;cACnC;cACA,MAAMC,QAAQ,GAAGhY,KAAK,CAACiY,IAAI,CAAChS,mBAAmB,CAAC;cAChD,KAAK,IAAIuJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;gBAC5BvJ,mBAAmB,CAAClC,MAAM,CAACiU,QAAQ,CAACxI,CAAC,CAAC,CAAC;cACzC;YACF;UACF;;UAEA;UACAa,iBAAiB,CAACC,OAAO,CAACnF,KAAK,EAAEqF,IAAI,CAAC0H,SAAS,CAAC5H,OAAO,CAACC,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOhQ,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDmX,EAAE,CAACS,OAAO,GAAI5X,KAAK,IAAK;MACtBtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDmX,EAAE,CAACU,OAAO,GAAG,MAAM;MACjBnZ,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACAiR,UAAU,CAACqH,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACA5Q,aAAa,CAAC6G,OAAO,GAAGiK,EAAE;EAC5B,CAAC;EAEDhc,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6K,YAAY,CAACkH,OAAO,EAAE;;IAE3B;IACA4K,aAAa,CAAC,CAAC;;IAEf;IACA3a,KAAK,GAAG,IAAI5B,KAAK,CAACwc,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAIzc,KAAK,CAAC0c,iBAAiB,CACxC,EAAE,EACFna,MAAM,CAACoa,UAAU,GAAGpa,MAAM,CAACqa,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAAC7P,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCiX,MAAM,CAAC7J,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBhF,SAAS,CAAC+D,OAAO,GAAG8K,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAI7c,KAAK,CAAC8c,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAACza,MAAM,CAACoa,UAAU,EAAEpa,MAAM,CAACqa,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAAC3a,MAAM,CAAC4a,gBAAgB,CAAC;IAC/C1S,YAAY,CAACkH,OAAO,CAACyL,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAItd,KAAK,CAACud,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5D3b,KAAK,CAAC2F,GAAG,CAAC+V,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAIxd,KAAK,CAACyd,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAAC5Q,QAAQ,CAACpH,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1C5D,KAAK,CAAC2F,GAAG,CAACiW,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAI1d,KAAK,CAACyd,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAAC9Q,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3C5D,KAAK,CAAC2F,GAAG,CAACmW,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAI3d,KAAK,CAAC4d,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAAC/Q,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCmY,SAAS,CAACE,KAAK,GAAGxX,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7BqX,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAAC/X,QAAQ,GAAG,GAAG;IACxBhE,KAAK,CAAC2F,GAAG,CAACoW,SAAS,CAAC;;IAEpB;IACApc,QAAQ,GAAG,IAAIrB,aAAa,CAACuc,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzD9b,QAAQ,CAACyc,aAAa,GAAG,IAAI;IAC7Bzc,QAAQ,CAAC0c,aAAa,GAAG,IAAI;IAC7B1c,QAAQ,CAAC2c,kBAAkB,GAAG,KAAK;IACnC3c,QAAQ,CAACuR,WAAW,GAAG,EAAE;IACzBvR,QAAQ,CAACwR,WAAW,GAAG,GAAG;IAC1BxR,QAAQ,CAACyR,aAAa,GAAG3M,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC/E,QAAQ,CAAC0R,aAAa,GAAG,CAAC;IAC1B1R,QAAQ,CAACmR,MAAM,CAAClN,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BjE,QAAQ,CAACsR,MAAM,CAAC,CAAC;;IAEjB;IACA1P,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBqZ,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBlb,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBqM,SAAS,EAAE,CAAC,CAACA,SAAS,CAAC+D;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMwM,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAIte,UAAU,CAAC,CAAC;QACtCse,aAAa,CAACC,IAAI,CAChB,GAAGzb,QAAQ,uBAAuB,EACjC0b,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAAC7c,KAAK;;UAE/B;UACA,MAAM+c,gBAAgB,GAAG,IAAI3e,KAAK,CAAC4e,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAAClX,QAAQ,CAAE2P,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACxN,MAAM,EAAE;cAChB;cACA,IAAIwN,KAAK,CAACrN,QAAQ,EAAE;gBAClB;gBACA,MAAMsN,WAAW,GAAG,IAAIpX,KAAK,CAAC6e,oBAAoB,CAAC;kBACjDhQ,KAAK,EAAE,QAAQ;kBAAO;kBACtBiQ,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAI7H,KAAK,CAACrN,QAAQ,CAAClB,GAAG,EAAE;kBACtBwO,WAAW,CAACxO,GAAG,GAAGuO,KAAK,CAACrN,QAAQ,CAAClB,GAAG;gBACtC;;gBAEA;gBACAuO,KAAK,CAACrN,QAAQ,GAAGsN,WAAW;gBAE5BjU,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE+T,KAAK,CAAC/F,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMsN,YAAY,CAACpO,QAAQ,CAAChI,MAAM,GAAG,CAAC,EAAE;YACtC,MAAM6O,KAAK,GAAGuH,YAAY,CAACpO,QAAQ,CAAC,CAAC,CAAC;YACtCqO,gBAAgB,CAACpX,GAAG,CAAC4P,KAAK,CAAC;UAC7B;;UAEA;UACAvV,KAAK,CAAC2F,GAAG,CAACoX,gBAAgB,CAAC;;UAE3B;UACA5d,gBAAgB,GAAG4d,gBAAgB;UAEnCxb,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9B6b,kBAAkB,CAAC,IAAI,CAAC;UACxBZ,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAO,GAAG,IAAK;UACP/b,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC8b,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAErZ,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDuY,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMe,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA;;QAEA;QACA3D,cAAc,CAAC,CAAC;;QAEhB;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAEF,CAAC,CAAC,OAAOjX,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAM6a,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAIpB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMmB,WAAW,GAAIC,WAAW,IAAK;UACnCvc,OAAO,CAACC,GAAG,CAAC,WAAWmc,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAI1f,UAAU,CAAC,CAAC;UAC/B0f,MAAM,CAACnB,IAAI,CACTe,GAAG,EACFd,IAAI,IAAK;YACRtb,OAAO,CAACC,GAAG,CAAC,WAAWmc,GAAG,EAAE,CAAC;YAC7BlB,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAS,GAAG,IAAK;YACP/b,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC8b,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAErZ,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAtB,KAAK,IAAK;YACTtB,OAAO,CAACsB,KAAK,CAAC,SAAS8a,GAAG,EAAE,EAAE9a,KAAK,CAAC;YACpC,IAAIib,WAAW,GAAG,CAAC,EAAE;cACnBvc,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3BiR,UAAU,CAAC,MAAMoL,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACLpB,MAAM,CAAC7Z,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAEDgb,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAI1f,UAAU,CAAC,CAAC;IAC/B0f,MAAM,CAACnB,IAAI,CACT,GAAGzb,QAAQ,4BAA4B,EACvC,MAAO0b,IAAI,IAAK;MACd,IAAI;QACF,MAAMnX,KAAK,GAAGmX,IAAI,CAAC7c,KAAK;QACxB0F,KAAK,CAAC0O,KAAK,CAACxQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxB8B,KAAK,CAACsF,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAE3B;QACA,IAAI5D,KAAK,EAAE;UACXA,KAAK,CAAC2F,GAAG,CAACD,KAAK,CAAC;;UAEhB;UACA,MAAM+X,eAAe,CAAC,CAAC;QACvB,CAAC,MAAM;UACLlc,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC;QAChC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACAya,GAAG,IAAK;MACP/b,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC8b,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAErZ,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAtB,KAAK,IAAK;MACTtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtB,OAAO,CAACsB,KAAK,CAAC,OAAO,EAAE;QACrBmb,IAAI,EAAEnb,KAAK,CAACoL,IAAI;QAChBgQ,IAAI,EAAEpb,KAAK,CAAC+P,OAAO;QACnBsL,KAAK,EAAE,GAAG/c,QAAQ,4BAA4B;QAC9Cgd,KAAK,EAAE,GAAGhd,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMid,OAAO,GAAGA,CAAA,KAAM;MACpBjV,iBAAiB,CAAC4G,OAAO,GAAGsO,qBAAqB,CAACD,OAAO,CAAC;;MAE1D;MACA5f,KAAK,CAACyS,MAAM,CAAC,CAAC;;MAEd;MACA,MAAMqN,SAAS,GAAGxc,KAAK,CAACyc,QAAQ,CAAC,CAAC;;MAElC;MACA,IAAI5U,qBAAqB,CAAC0Q,IAAI,GAAG,CAAC,EAAE;QAClC1Q,qBAAqB,CAACxD,OAAO,CAAEV,KAAK,IAAK;UACvCA,KAAK,CAACwL,MAAM,CAACqN,SAAS,CAAC;QACzB,CAAC,CAAC;MACJ;MAEA,IAAI5e,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACqQ,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMwO,UAAU,GAAGrf,gBAAgB,CAAC6L,QAAQ,CAAC3H,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMob,eAAe,GAAGtf,gBAAgB,CAACgV,QAAQ,CAAC1Q,CAAC;;QAEnD;QACA;QACA,MAAMib,gBAAgB,GAAG,EAAED,eAAe,GAAGha,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;;QAEnE;QACA,MAAMia,YAAY,GAAG,IAAIvgB,KAAK,CAACiG,OAAO,CACpC,CAAC,EAAE,GAAGI,IAAI,CAACma,GAAG,CAACF,gBAAgB,CAAC,EAChC,GAAG,EACH,CAAC,EAAE,GAAGja,IAAI,CAACoa,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA,MAAMI,oBAAoB,GAAGN,UAAU,CAACnb,KAAK,CAAC,CAAC,CAACsC,GAAG,CAACgZ,YAAY,CAAC;QACjE,MAAMI,YAAY,GAAGP,UAAU,CAACnb,KAAK,CAAC,CAAC;;QAEvC;QACA,IAAI,CAACgG,kBAAkB,CAAC0G,OAAO,EAAE;UAC/B1G,kBAAkB,CAAC0G,OAAO,GAAG+O,oBAAoB,CAACzb,KAAK,CAAC,CAAC;QAC3D;QAEA,IAAI,CAACiG,gBAAgB,CAACyG,OAAO,EAAE;UAC7BzG,gBAAgB,CAACyG,OAAO,GAAGgP,YAAY,CAAC1b,KAAK,CAAC,CAAC;QACjD;;QAEA;QACAgG,kBAAkB,CAAC0G,OAAO,CAACiP,IAAI,CAACF,oBAAoB,EAAE,CAAC,GAAGvV,eAAe,CAAC;QAC1ED,gBAAgB,CAACyG,OAAO,CAACiP,IAAI,CAACD,YAAY,EAAE,CAAC,GAAGxV,eAAe,CAAC;;QAEhE;QACAsR,MAAM,CAAC7P,QAAQ,CAAC4F,IAAI,CAACvH,kBAAkB,CAAC0G,OAAO,CAAC;;QAEhD;QACA8K,MAAM,CAACzK,EAAE,CAACxM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACAiX,MAAM,CAAC7J,MAAM,CAAC1H,gBAAgB,CAACyG,OAAO,CAAC;;QAEvC;QACA8K,MAAM,CAACoE,sBAAsB,CAAC,CAAC;QAC/BpE,MAAM,CAACvJ,YAAY,CAAC,CAAC;QACrBuJ,MAAM,CAACtJ,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACA5R,QAAQ,CAACqQ,OAAO,GAAG,KAAK;;QAExB;QACArQ,QAAQ,CAACmR,MAAM,CAACF,IAAI,CAACtH,gBAAgB,CAACyG,OAAO,CAAC;QAC9CpQ,QAAQ,CAACsR,MAAM,CAAC,CAAC;QAEjB1P,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnB0d,IAAI,EAAEV,UAAU,CAAClM,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAEwI,MAAM,CAAC7P,QAAQ,CAACsH,OAAO,CAAC,CAAC;UAC/B6M,IAAI,EAAE7V,gBAAgB,CAACyG,OAAO,CAACuC,OAAO,CAAC,CAAC;UACxC8M,IAAI,EAAEvE,MAAM,CAACwE,iBAAiB,CAAC,IAAIjhB,KAAK,CAACiG,OAAO,CAAC,CAAC,CAAC,CAACiO,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI5S,UAAU,KAAK,QAAQ,EAAE;QAClC;QACA2J,kBAAkB,CAAC0G,OAAO,GAAG,IAAI;QACjCzG,gBAAgB,CAACyG,OAAO,GAAG,IAAI;;QAE/B;QACApQ,QAAQ,CAACqQ,OAAO,GAAG,IAAI;;QAEvB;QACA6K,MAAM,CAACzK,EAAE,CAACxM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAIa,IAAI,CAACK,GAAG,CAAC+V,MAAM,CAAC7P,QAAQ,CAACvH,CAAC,CAAC,GAAG,EAAE,EAAE;UACpCoX,MAAM,CAAC7P,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BjE,QAAQ,CAACmR,MAAM,CAAClN,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BiX,MAAM,CAAC7J,MAAM,CAACrR,QAAQ,CAACmR,MAAM,CAAC;UAC9BnR,QAAQ,CAACsR,MAAM,CAAC,CAAC;QACnB;;QAEA;QACA;QACA4J,MAAM,CAACvJ,YAAY,CAAC,CAAC;QACrBuJ,MAAM,CAACtJ,iBAAiB,CAAC,IAAI,CAAC;MAEhC,CAAC,MAAM,IAAI7R,UAAU,KAAK,cAAc,EAAE;QACxC;QACA2J,kBAAkB,CAAC0G,OAAO,GAAG,IAAI;QACjCzG,gBAAgB,CAACyG,OAAO,GAAG,IAAI;;QAE/B;QACApQ,QAAQ,CAACsR,MAAM,CAAC,CAAC;MACnB;MAEA,IAAItR,QAAQ,EAAEA,QAAQ,CAACsR,MAAM,CAAC,CAAC;MAC/B,IAAIjR,KAAK,IAAI6a,MAAM,EAAE;QACnBI,QAAQ,CAACqE,MAAM,CAACtf,KAAK,EAAE6a,MAAM,CAAC;MAChC;IACF,CAAC;IAEDuD,OAAO,CAAC,CAAC;;IAET;IACA,MAAMmB,YAAY,GAAGA,CAAA,KAAM;MACzB1E,MAAM,CAAC2E,MAAM,GAAG7e,MAAM,CAACoa,UAAU,GAAGpa,MAAM,CAACqa,WAAW;MACtDH,MAAM,CAACoE,sBAAsB,CAAC,CAAC;MAC/BhE,QAAQ,CAACG,OAAO,CAACza,MAAM,CAACoa,UAAU,EAAEpa,MAAM,CAACqa,WAAW,CAAC;IACzD,CAAC;IACDra,MAAM,CAAC8e,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACA5e,MAAM,CAAC+e,aAAa,GAAG,MAAM;MAC3B,IAAI1T,SAAS,CAAC+D,OAAO,EAAE;QACrB/D,SAAS,CAAC+D,OAAO,CAAC/E,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzCoI,SAAS,CAAC+D,OAAO,CAACiB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjChF,SAAS,CAAC+D,OAAO,CAACuB,YAAY,CAAC,CAAC;QAChCtF,SAAS,CAAC+D,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAI5R,QAAQ,EAAE;UACZA,QAAQ,CAACmR,MAAM,CAAClN,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BjE,QAAQ,CAACqQ,OAAO,GAAG,IAAI;UACvBrQ,QAAQ,CAACsR,MAAM,CAAC,CAAC;QACnB;QAEAvR,UAAU,GAAG,QAAQ;QACrB6B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAI2H,iBAAiB,CAAC4G,OAAO,EAAE;QAC7B4P,oBAAoB,CAACxW,iBAAiB,CAAC4G,OAAO,CAAC;QAC/C5G,iBAAiB,CAAC4G,OAAO,GAAG,IAAI;MAClC;;MAEA;MACAvR,KAAK,CAACohB,SAAS,CAAC,CAAC;;MAEjB;MACAjW,qBAAqB,CAACxD,OAAO,CAACV,KAAK,IAAI;QACrCN,eAAe,CAACe,WAAW,CAACT,KAAK,CAAC;MACpC,CAAC,CAAC;MACFkE,qBAAqB,CAACnC,KAAK,CAAC,CAAC;;MAE7B;MACArC,eAAe,CAACoC,OAAO,CAAC,CAAC;;MAEzB;MACA,IAAIvH,KAAK,EAAE;QACT,MAAM6f,eAAe,GAAG,EAAE;QAC1B7f,KAAK,CAAC4F,QAAQ,CAAEC,MAAM,IAAK;UACzB,IAAIA,MAAM,CAACkC,MAAM,EAAE;YACjB,IAAIlC,MAAM,CAACmC,QAAQ,EAAE;cACnBnC,MAAM,CAACmC,QAAQ,CAACC,OAAO,CAAC,CAAC;YAC3B;YACA,IAAIpC,MAAM,CAACqC,QAAQ,EAAE;cACnB,IAAI5F,KAAK,CAACC,OAAO,CAACsD,MAAM,CAACqC,QAAQ,CAAC,EAAE;gBAClCrC,MAAM,CAACqC,QAAQ,CAAC/B,OAAO,CAAC+B,QAAQ,IAAI;kBAClC,IAAIA,QAAQ,CAAClB,GAAG,EAAEkB,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;kBACxCC,QAAQ,CAACD,OAAO,CAAC,CAAC;gBACpB,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL,IAAIpC,MAAM,CAACqC,QAAQ,CAAClB,GAAG,EAAEnB,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;gBACtDpC,MAAM,CAACqC,QAAQ,CAACD,OAAO,CAAC,CAAC;cAC3B;YACF;YACA,IAAIpC,MAAM,KAAK7F,KAAK,EAAE;cACpB6f,eAAe,CAAClI,IAAI,CAAC9R,MAAM,CAAC;YAC9B;UACF;QACF,CAAC,CAAC;;QAEF;QACAga,eAAe,CAAC1Z,OAAO,CAAC2Z,GAAG,IAAI;UAC7B,IAAIA,GAAG,CAACpY,MAAM,EAAE;YACdoY,GAAG,CAACpY,MAAM,CAACC,MAAM,CAACmY,GAAG,CAAC;UACxB;QACF,CAAC,CAAC;QAEF9f,KAAK,CAACwH,KAAK,CAAC,CAAC;MACf;;MAEA;MACA,IAAIyT,QAAQ,EAAE;QACZA,QAAQ,CAAC8E,gBAAgB,CAAC,IAAI,CAAC;QAC/B,IAAIlX,YAAY,CAACkH,OAAO,IAAIkL,QAAQ,CAACQ,UAAU,IAAIR,QAAQ,CAACQ,UAAU,CAACuE,UAAU,KAAKnX,YAAY,CAACkH,OAAO,EAAE;UAC1GlH,YAAY,CAACkH,OAAO,CAACkQ,WAAW,CAAChF,QAAQ,CAACQ,UAAU,CAAC;QACvD;QACAR,QAAQ,CAAChT,OAAO,CAAC,CAAC;QAClBgT,QAAQ,CAACiF,gBAAgB,CAAC,CAAC;MAC7B;;MAEA;MACA,IAAIvgB,QAAQ,EAAE;QACZA,QAAQ,CAACsI,OAAO,CAAC,CAAC;MACpB;;MAEA;MACA3H,oBAAoB,CAACkH,KAAK,CAAC,CAAC;MAC5BhH,oBAAoB,CAACgH,KAAK,CAAC,CAAC;MAC5B9F,gBAAgB,CAAC8F,KAAK,CAAC,CAAC;MACxB/F,aAAa,CAAC+F,KAAK,CAAC,CAAC;MACrB5F,gBAAgB,CAAC4F,KAAK,CAAC,CAAC;MACxB3F,kBAAkB,CAAC2F,KAAK,CAAC,CAAC;MAE1BjG,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxD,SAAS,CAAC,MAAM;IACd;IACAgE,qBAAqB,CAAC,CAAC;;IAEvB;IACA,MAAMme,uBAAuB,GAAGA,CAAA,KAAM;MACpC5e,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCQ,qBAAqB,CAAC,CAAC;IACzB,CAAC;;IAED;IACArB,MAAM,CAAC8e,gBAAgB,CAAC,oBAAoB,EAAEU,uBAAuB,CAAC;;IAEtE;IACA,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnCre,qBAAqB,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC;;IAET;IACA,OAAO,MAAM;MACXrB,MAAM,CAAC2f,mBAAmB,CAAC,oBAAoB,EAAEH,uBAAuB,CAAC;MACzEI,aAAa,CAACH,UAAU,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApiB,SAAS,CAAC,MAAM;IACd;IACA,IAAIgC,KAAK,IAAI+I,SAAS,CAACgH,OAAO,EAAE;MAC9BxO,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA,MAAMgf,KAAK,GAAG/N,UAAU,CAAC,MAAM;QAC7B,IAAIzS,KAAK,IAAI+I,SAAS,CAACgH,OAAO,EAAE;UAAG;UACjC0Q,mBAAmB,CAAC1X,SAAS,CAACgH,OAAO,CAAC;QACxC;MACF,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAM2Q,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM;MACLjf,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC,EAAE,CAACxB,KAAK,CAAC,CAAC;;EAEX;EACAhC,SAAS,CAAC,MAAM;IACd,IAAI6K,YAAY,CAACkH,OAAO,EAAE;MACxB;MACA,MAAM4Q,WAAW,GAAIlI,KAAK,IAAK;QAC7B,IAAIzY,KAAK,IAAIgM,SAAS,CAAC+D,OAAO,EAAE;UAC9B6Q,gBAAgB,CAACnI,KAAK,EAAE5P,YAAY,CAACkH,OAAO,EAAE/P,KAAK,EAAEgM,SAAS,CAAC+D,OAAO,CAAC;QACzE;MACF,CAAC;;MAED;MACAlH,YAAY,CAACkH,OAAO,CAAC0P,gBAAgB,CAAC,OAAO,EAAEkB,WAAW,CAAC;;MAE3D;MACApf,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,CAAC,CAACqH,YAAY,CAACkH,OAAO,CAAC;;MAEpD;MACA,OAAO,MAAM;QACX,IAAIlH,YAAY,CAACkH,OAAO,EAAE;UACxBlH,YAAY,CAACkH,OAAO,CAACuQ,mBAAmB,CAAC,OAAO,EAAEK,WAAW,CAAC;UAC9Dpf,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACxB,KAAK,EAAEgM,SAAS,CAAC+D,OAAO,CAAC,CAAC;;EAE9B;EACA,MAAM8Q,SAAS,GAAG1iB,WAAW,CAAC,MAAM;IAClCoD,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B;EACF,CAAC,EAAE,CAACqH,YAAY,EAAEwE,aAAa,EAAEzL,gBAAgB,CAAC,CAAC;;EAEnD;EACA,MAAMkf,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAM9Y,QAAQ,GAAG,IAAI5J,KAAK,CAAC2iB,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChD,MAAM7Y,QAAQ,GAAG,IAAI9J,KAAK,CAAC4iB,iBAAiB,CAAC;MAAE/T,KAAK,EAAE;IAAS,CAAC,CAAC;IACjE,MAAM6K,iBAAiB,GAAG,IAAI1Z,KAAK,CAAC6iB,IAAI,CAACjZ,QAAQ,EAAEE,QAAQ,CAAC;;IAE5D;IACA,MAAMgZ,YAAY,GAAG,IAAI9iB,KAAK,CAAC+iB,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC5D,MAAMC,YAAY,GAAG,IAAIhjB,KAAK,CAAC4iB,iBAAiB,CAAC;MAAE/T,KAAK,EAAE;IAAS,CAAC,CAAC;IACrE,MAAMoU,SAAS,GAAG,IAAIjjB,KAAK,CAAC6iB,IAAI,CAACC,YAAY,EAAEE,YAAY,CAAC;IAC5DC,SAAS,CAACrW,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAClCkU,iBAAiB,CAACnS,GAAG,CAAC0b,SAAS,CAAC;IAEhC,OAAOvJ,iBAAiB;EAC1B,CAAC;;EAED;EACA,MAAMwJ,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACthB,KAAK,EAAE;;IAEZ;IACA4B,gBAAgB,CAACuE,OAAO,CAAC,CAACob,QAAQ,EAAEjV,OAAO,KAAK;MAC9C,IAAIiV,QAAQ,CAAC7b,KAAK,EAAE;QAClB;QACA,MAAM8b,cAAc,GAAG,IAAIpjB,KAAK,CAACqjB,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxD,MAAMC,cAAc,GAAG,IAAItjB,KAAK,CAAC4iB,iBAAiB,CAAC;UACjD/T,KAAK,EAAE,QAAQ;UAAC;UAChB0I,WAAW,EAAE,KAAK;UAClBU,OAAO,EAAE,GAAG;UAAG;UACfsL,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,MAAMC,UAAU,GAAG,IAAIxjB,KAAK,CAAC6iB,IAAI,CAACO,cAAc,EAAEE,cAAc,CAAC;QACjEE,UAAU,CAAC5W,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE;;QAEnC;QACAge,UAAU,CAACC,QAAQ,GAAG;UACpB5T,IAAI,EAAE,cAAc;UACpB3B,OAAO,EAAEA,OAAO;UAChBkD,IAAI,EAAE+R,QAAQ,CAAC1P,YAAY,CAACrC,IAAI;UAChCsS,aAAa,EAAE;QACjB,CAAC;;QAED;QACAP,QAAQ,CAAC7b,KAAK,CAACC,GAAG,CAACic,UAAU,CAAC;QAE9BrgB,OAAO,CAACC,GAAG,CAAC,OAAO+f,QAAQ,CAAC1P,YAAY,CAACrC,IAAI,KAAKlD,OAAO,aAAa,CAAC;MACzE;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACAtO,SAAS,CAAC,MAAM;IACd;IACA,MAAMwiB,KAAK,GAAG/N,UAAU,CAAC,MAAM;MAC7B,IAAI7Q,gBAAgB,CAACyY,IAAI,GAAG,CAAC,EAAE;QAC7B9Y,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1B;MACF;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAE;;IAEX,OAAO,MAAMkf,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACAxiB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACA,IAAI0O,0BAA0B,CAACqD,OAAO,EAAE;QACtCwQ,aAAa,CAAC7T,0BAA0B,CAACqD,OAAO,CAAC;QACjDrD,0BAA0B,CAACqD,OAAO,GAAG,IAAI;QACzCxO,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC9B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAxD,SAAS,CAAC,MAAM;IACd,IAAI,CAACmO,mBAAmB,CAACE,OAAO,IAAIK,0BAA0B,CAACqD,OAAO,EAAE;MACtEwQ,aAAa,CAAC7T,0BAA0B,CAACqD,OAAO,CAAC;MACjDrD,0BAA0B,CAACqD,OAAO,GAAG,IAAI;MACzCtD,mBAAmB,CAACsD,OAAO,GAAG,IAAI;MAClCxO,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACnC;EACF,CAAC,EAAE,CAAC2K,mBAAmB,CAACE,OAAO,CAAC,CAAC;;EAEjC;EACArO,SAAS,CAAC,MAAM;IACd;IACA,IAAIoM,aAAa,IAAIA,aAAa,CAAC1D,MAAM,GAAG,CAAC,EAAE;MAC7C;MACA,IAAI,CAACuF,oBAAoB,EAAE;QACzB;QACA,MAAM8V,6BAA6B,GAAG3X,aAAa,CAAC3H,IAAI,CACtDoP,YAAY,IAAIA,YAAY,CAACW,eAAe,KAAK,KAAK,IAAIX,YAAY,CAACvF,OACzE,CAAC;;QAED;QACA,MAAM0V,kBAAkB,GAAGD,6BAA6B,IAAI3X,aAAa,CAAC,CAAC,CAAC;QAE5E7I,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEwgB,kBAAkB,CAACxS,IAAI,EAClC,QAAQ,EAAEuS,6BAA6B,GAAG,GAAG,GAAG,GAAG,CAAC;;QAEhE;QACA,MAAMvB,KAAK,GAAG/N,UAAU,CAAC,MAAM;UAC7Bd,wBAAwB,CAACqQ,kBAAkB,CAACxS,IAAI,CAAC;QACnD,CAAC,EAAE,IAAI,CAAC;QAER,OAAO,MAAMkR,YAAY,CAACF,KAAK,CAAC;MAClC;IACF;EACF,CAAC,EAAE,CAACpW,aAAa,EAAE6B,oBAAoB,CAAC,CAAC;;EAEzC;EACA,MAAMgW,yBAAyB,GAAIC,iBAAiB,IAAK;IACvD,IAAI,CAACliB,KAAK,IAAI,OAAOA,KAAK,CAAC4F,QAAQ,KAAK,UAAU,IAAI,CAACsc,iBAAiB,EAAE;IAC1E,IAAI,CAACrY,WAAW,CAACE,OAAO,IAAIF,WAAW,CAACE,OAAO,CAACrD,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IACtE,IAAI;MACF1G,KAAK,CAAC0O,QAAQ,CACXvH,MAAM,CAAC2Y,GAAG,IAAIA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAACM,qBAAqB,CAAC,CACjEhc,OAAO,CAAC2Z,GAAG,IAAI9f,KAAK,CAAC2H,MAAM,CAACmY,GAAG,CAAC,CAAC;MACpC1V,aAAa,CAACjE,OAAO,CAAC0L,YAAY,IAAI;QACpC,IAAI,CAACA,YAAY,CAACuQ,SAAS,IAAI,CAAC9f,KAAK,CAACC,OAAO,CAACsP,YAAY,CAACuQ,SAAS,CAAC,EAAE;QACvEvQ,YAAY,CAACuQ,SAAS,CAACjc,OAAO,CAAEsJ,QAAQ,IAAK;UAC3C,IAAI,CAACA,QAAQ,CAAChF,SAAS,IAAI,CAACgF,QAAQ,CAAC/E,QAAQ,IAAI2X,KAAK,CAACpQ,UAAU,CAACxC,QAAQ,CAAChF,SAAS,CAAC,CAAC,IAAI4X,KAAK,CAACpQ,UAAU,CAACxC,QAAQ,CAAC/E,QAAQ,CAAC,CAAC,EAAE;YAC9H;UACF;UACAnJ,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEqI,WAAW,CAAC;UACvC,MAAME,OAAO,GAAGF,WAAW,CAACE,OAAO,CAAC5C,MAAM,CACxCmb,CAAC,IAAIA,CAAC,CAAC1hB,QAAQ,KAAKiR,YAAY,CAACrC,IAAI,IAAI8S,CAAC,CAAC7S,QAAQ,KAAKA,QAAQ,CAACD,IACnE,CAAC;UACD,IAAIzF,OAAO,CAACrD,MAAM,KAAK,CAAC,EAAE;UAC1B;UACAnF,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEqQ,YAAY,CAACrC,IAAI,EAAE,IAAI,EAAEC,QAAQ,CAACD,IAAI,EAAE,MAAM,EAAEzF,OAAO,CAACrD,MAAM,CAAC;UACnF,MAAMsN,QAAQ,GAAGkO,iBAAiB,CAAClQ,YAAY,CAC7CC,UAAU,CAACxC,QAAQ,CAAChF,SAAS,CAAC,EAC9BwH,UAAU,CAACxC,QAAQ,CAAC/E,QAAQ,CAC9B,CAAC;UACD;UACA,MAAM6X,KAAK,GAAG,IAAInkB,KAAK,CAAC4e,KAAK,CAAC,CAAC;UAC/BuF,KAAK,CAACvX,QAAQ,CAACpH,GAAG,CAACoQ,QAAQ,CAACzQ,CAAC,EAAE,EAAE,EAAE,CAACyQ,QAAQ,CAACvQ,CAAC,CAAC,CAAC,CAAC;UACjD8e,KAAK,CAACV,QAAQ,GAAG;YAAEM,qBAAqB,EAAE;UAAK,CAAC;UAChD;UACA,MAAMK,QAAQ,GAAG,EAAE,CAAC,CAAC;UACrB,MAAMC,WAAW,GAAG,CAAC,CAAC,CAAC;UACvB,MAAMC,UAAU,GAAG3Y,OAAO,CAACrD,MAAM,GAAG8b,QAAQ,GAAG,CAACzY,OAAO,CAACrD,MAAM,GAAG,CAAC,IAAI+b,WAAW;UACjF;UACA,MAAME,MAAM,GAAG,GAAG,CAAC,CAAC;UACpB,MAAMC,SAAS,GAAG,GAAG,CAAC,CAAC;UACvB,MAAMC,MAAM,GAAG,EAAE,CAAC9Y,OAAO,CAACrD,MAAM,GAAG,CAAC,KAAKic,MAAM,GAAGC,SAAS,CAAC,CAAC,GAAG,CAAC;UACjE7Y,OAAO,CAAC5D,OAAO,CAAC,CAAC4H,MAAM,EAAE+U,GAAG,KAAK;YAC/B;YACA,MAAMC,aAAa,GAAG,IAAI3kB,KAAK,CAAC4kB,aAAa,CAAC,CAAC;YAC/C,MAAMC,QAAQ,GAAG,GAAG9hB,QAAQ,WAAW4M,MAAM,CAACE,IAAI,MAAM;YACxD;YACA,MAAMiV,YAAY,GAAG,IAAI9kB,KAAK,CAAC4iB,iBAAiB,CAAC;cAC/Cha,GAAG,EAAE+b,aAAa,CAACnG,IAAI,CAACqG,QAAQ,CAAC;cACjCtN,WAAW,EAAE,IAAI;cACjBU,OAAO,EAAE,CAAC,CAAC;YACb,CAAC,CAAC;YACF;YACA,MAAM8M,UAAU,GAAG,IAAI/kB,KAAK,CAAC4iB,iBAAiB,CAAC;cAC7C/T,KAAK,EAAE,QAAQ;cACf0I,WAAW,EAAE,IAAI;cACjBU,OAAO,EAAE,GAAG,CAAC;YACf,CAAC,CAAC;YACF;YACA,MAAM+M,OAAO,GAAGT,MAAM,GAAG,IAAI;YAC7B,MAAMU,QAAQ,GAAGV,MAAM,GAAG,IAAI;YAC9B;YACA;YACA,MAAMW,UAAU,GAAG,IAAIllB,KAAK,CAACmlB,aAAa,CAACH,OAAO,EAAEC,QAAQ,CAAC;YAC7D,MAAMG,MAAM,GAAG,IAAIplB,KAAK,CAAC6iB,IAAI,CAACqC,UAAU,EAAEH,UAAU,CAAC;YACrD;YACA,MAAMM,YAAY,GAAG,IAAIrlB,KAAK,CAACmlB,aAAa,CAACZ,MAAM,EAAEA,MAAM,CAAC;YAC5D,MAAMe,QAAQ,GAAG,IAAItlB,KAAK,CAAC6iB,IAAI,CAACwC,YAAY,EAAEP,YAAY,CAAC;YAC3D;YACAQ,QAAQ,CAAC1Y,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;YACjC;YACA,MAAM+f,SAAS,GAAG,IAAIvlB,KAAK,CAAC4e,KAAK,CAAC,CAAC;YACnC2G,SAAS,CAAChe,GAAG,CAAC6d,MAAM,CAAC;YACrBG,SAAS,CAAChe,GAAG,CAAC+d,QAAQ,CAAC;YACvB;YACAC,SAAS,CAAC3Y,QAAQ,CAACpH,GAAG,CAACif,MAAM,GAAGC,GAAG,IAAIH,MAAM,GAAGC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACjE;YACAe,SAAS,CAACvN,WAAW,GAAG,GAAG,CAAC,CAAC;YAC7BuN,SAAS,CAAC9B,QAAQ,GAAG;cACnBjU,QAAQ,EAAEG,MAAM,CAACQ,EAAE;cACnBqV,UAAU,EAAE7V,MAAM,CAACE,IAAI;cACvBwB,QAAQ,EAAEA,QAAQ,CAACD,IAAI;cACvBqU,oBAAoB,EAAE;YACxB,CAAC;YACDtB,KAAK,CAAC5c,GAAG,CAACge,SAAS,CAAC;UACtB,CAAC,CAAC;UACF;UACA;UACA,MAAMG,YAAY,GAAIvB,KAAK,CAACvX,QAAQ,CAACvH,CAAC,CAAC,CAAC;UACxC,MAAMsgB,cAAc,GAAG,IAAI3lB,KAAK,CAAC+iB,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE2C,YAAY,EAAE,EAAE,CAAC;UAC7E,MAAME,cAAc,GAAG,IAAI5lB,KAAK,CAAC4iB,iBAAiB,CAAC;YAAE/T,KAAK,EAAE,QAAQ;YAAE0I,WAAW,EAAE,IAAI;YAAEU,OAAO,EAAE;UAAI,CAAC,CAAC;UACxG,MAAM4N,MAAM,GAAG,IAAI7lB,KAAK,CAAC6iB,IAAI,CAAC8C,cAAc,EAAEC,cAAc,CAAC;UAC7D;UACAC,MAAM,CAACjZ,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,CAACkgB,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC;UAC5C;UACA;UACAG,MAAM,CAACpC,QAAQ,GAAG;YAAEqC,sBAAsB,EAAE;UAAK,CAAC;UAClD3B,KAAK,CAAC5c,GAAG,CAACse,MAAM,CAAC;UACjBjkB,KAAK,CAAC2F,GAAG,CAAC4c,KAAK,CAAC;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOjb,CAAC,EAAE;MACV/F,OAAO,CAACsB,KAAK,CAAC,iCAAiC,EAAEyE,CAAC,CAAC;MACnD;IACF;EACF,CAAC;;EAED;EACAtJ,SAAS,CAAC,MAAM;IACd,IAAIgC,KAAK,IAAI,OAAOA,KAAK,CAAC4F,QAAQ,KAAK,UAAU,IAAImD,SAAS,CAACgH,OAAO,EAAE;MACtEkS,yBAAyB,CAAClZ,SAAS,CAACgH,OAAO,CAAC;IAC9C;EACF,CAAC,EAAE,CAAC/P,KAAK,EAAE+I,SAAS,CAACgH,OAAO,CAAC,CAAC;;EAE9B;EACA/R,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,KAAK,IAAI,CAACgM,SAAS,CAAC+D,OAAO,EAAE;IAClC,MAAMoU,gBAAgB,GAAGA,CAAA,KAAM;MAC7B;MACAnkB,KAAK,CAAC0O,QAAQ,CAACvI,OAAO,CAAC2Z,GAAG,IAAI;QAC5B,IAAIA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAACM,qBAAqB,EAAE;UACtDrC,GAAG,CAAC9O,MAAM,CAAChF,SAAS,CAAC+D,OAAO,CAAC/E,QAAQ,CAACzH,CAAC,EAAEuc,GAAG,CAAC9U,QAAQ,CAACvH,CAAC,EAAEuI,SAAS,CAAC+D,OAAO,CAAC/E,QAAQ,CAACrH,CAAC,CAAC;QACxF;MACF,CAAC,CAAC;MACF0a,qBAAqB,CAAC8F,gBAAgB,CAAC;IACzC,CAAC;IACDA,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACnkB,KAAK,CAAC,CAAC;;EAEX;EACA,MAAM4gB,gBAAgB,GAAGA,CAACnI,KAAK,EAAE2L,SAAS,EAAEC,aAAa,EAAEC,cAAc,KAAK;IAC5E,IAAI,CAACF,SAAS,IAAI,CAACC,aAAa,IAAI,CAACC,cAAc,EAAE;IACrD,MAAMC,IAAI,GAAGH,SAAS,CAACI,qBAAqB,CAAC,CAAC;IAC9C,MAAMC,MAAM,GAAI,CAAChM,KAAK,CAACiM,OAAO,GAAGH,IAAI,CAACrZ,IAAI,IAAIkZ,SAAS,CAACO,WAAW,GAAI,CAAC,GAAG,CAAC;IAC5E,MAAMC,MAAM,GAAG,EAAE,CAACnM,KAAK,CAACoM,OAAO,GAAGN,IAAI,CAAC1X,GAAG,IAAIuX,SAAS,CAACU,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;IAC7E,MAAMC,SAAS,GAAG,IAAI3mB,KAAK,CAAC4mB,SAAS,CAAC,CAAC;IACvCD,SAAS,CAACE,MAAM,CAACC,MAAM,CAACC,SAAS,GAAG,CAAC;IACrCJ,SAAS,CAACE,MAAM,CAACG,IAAI,CAACD,SAAS,GAAG,CAAC;IACnC,MAAME,WAAW,GAAG,IAAIjnB,KAAK,CAACknB,OAAO,CAACb,MAAM,EAAEG,MAAM,CAAC;IACrDG,SAAS,CAACQ,aAAa,CAACF,WAAW,EAAEf,cAAc,CAAC;IACpD,MAAMkB,UAAU,GAAGT,SAAS,CAACU,gBAAgB,CAACpB,aAAa,CAAC3V,QAAQ,EAAE,IAAI,CAAC;IAC3E,IAAI8W,UAAU,CAAC9e,MAAM,GAAG,CAAC,EAAE;MACzB,KAAK,IAAIoL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0T,UAAU,CAAC9e,MAAM,EAAEoL,CAAC,EAAE,EAAE;QAC1C,MAAMgO,GAAG,GAAG0F,UAAU,CAAC1T,CAAC,CAAC,CAACjM,MAAM;QAChC,IAAIia,GAAG,CAACpY,MAAM,IAAIoY,GAAG,CAACpY,MAAM,CAACma,QAAQ,IAAI/B,GAAG,CAACpY,MAAM,CAACma,QAAQ,CAACgC,oBAAoB,EAAE;UACjF,MAAMjW,QAAQ,GAAGkS,GAAG,CAACpY,MAAM,CAACma,QAAQ,CAACjU,QAAQ;UAC7CrM,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEqI,WAAW,CAAC;UACvD,MAAMkE,MAAM,GAAGlE,WAAW,CAACE,OAAO,CAACtH,IAAI,CAAC6f,CAAC,IAAIA,CAAC,CAAC/T,EAAE,KAAKX,QAAQ,CAAC;UAC/D,IAAIG,MAAM,EAAE;YACV,MAAMxK,CAAC,GAAGkV,KAAK,CAACiM,OAAO;YACvB,MAAMjhB,CAAC,GAAGgV,KAAK,CAACoM,OAAO;YACvBlX,gBAAgB,CAAC;cACftB,OAAO,EAAE,IAAI;cACbuB,QAAQ;cACR5C,QAAQ,EAAE;gBAAEzH,CAAC;gBAAEE;cAAE,CAAC;cAClB8I,OAAO,EAAEuB,0BAA0B,CAACC,MAAM;YAC5C,CAAC,CAAC;YACF,OAAO,CAAC;UACV;QACF;MACF;IACF;IACA;IACA;EACF,CAAC;EAKD,oBACE/O,OAAA,CAAAE,SAAA;IAAAwP,QAAA,gBACE1P,OAAA;MAAMwP,KAAK,EAAEzB,UAAW;MAAA2B,QAAA,EAAC;IAAK;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrCpQ,OAAA,CAACL,MAAM;MACL6P,KAAK,EAAE5B,uBAAwB;MAC/B8Y,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAEhU,wBAAyB;MACnCiU,OAAO,EAAExb,aAAa,CAACpD,GAAG,CAAC6K,YAAY,KAAK;QAC1CD,KAAK,EAAEC,YAAY,CAACrC,IAAI;QACxBqW,KAAK,EAAEhU,YAAY,CAACrC;MACtB,CAAC,CAAC,CAAE;MACJ6K,IAAI,EAAC,OAAO;MACZyL,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACb3a,MAAM,EAAE,IAAI;QACZ4a,SAAS,EAAE;MACb,CAAE;MACFpU,KAAK,EAAE3F,oBAAoB,GAAGA,oBAAoB,CAACuD,IAAI,GAAGkH;IAAU;MAAAzH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eACFpQ,OAAA;MAAKinB,GAAG,EAAEpd,YAAa;MAAC2F,KAAK,EAAE;QAAE1B,KAAK,EAAE,MAAM;QAAE+B,MAAM,EAAE;MAAO;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGnEjD,mBAAmB,CAACE,OAAO,iBAC1BrN,OAAA;MACEwP,KAAK,EAAE;QACLxD,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAACzH,CAAC,IAAI;QAC3CsJ,GAAG,EAAE,GAAGV,mBAAmB,CAACnB,QAAQ,CAACvH,CAAC,IAAI;QAC1C0H,SAAS,EAAE,wBAAwB;QACnCC,MAAM,EAAE,IAAI;QACZK,eAAe,EAAE,qBAAqB;QACtCwB,KAAK,EAAE,OAAO;QACdtB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,8BAA8B;QACzCN,OAAO,EAAE,GAAG;QACZiD,QAAQ,EAAE,OAAO;QAAE;QACnB5C,QAAQ,EAAE,MAAM,CAAC;MACnB,CAAE;MAAA6C,QAAA,GAEDvC,mBAAmB,CAACI,OAAO,eAC5BvN,OAAA;QACEwP,KAAK,EAAE;UACLxD,QAAQ,EAAE,UAAU;UACpB6B,GAAG,EAAE,KAAK;UACVqZ,KAAK,EAAE,KAAK;UACZpX,UAAU,EAAE,MAAM;UAClBpD,MAAM,EAAE,MAAM;UACduB,KAAK,EAAE,OAAO;UACdpB,QAAQ,EAAE,MAAM;UAChBD,MAAM,EAAE,SAAS;UACjBJ,OAAO,EAAE;QACX,CAAE;QACF2a,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAACha,sBAAsB,CAAE;QAAAsC,QAAA,EAC3D;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAEDpQ,OAAA;MAAKwP,KAAK,EAAEzD,oBAAqB;MAAA2D,QAAA,gBAC/B1P,OAAA;QACEwP,KAAK,EAAE;UACL,GAAGjD,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFsb,OAAO,EAAErW,kBAAmB;QAAApB,QAAA,EAC7B;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTpQ,OAAA;QACEwP,KAAK,EAAE;UACL,GAAGjD,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFsb,OAAO,EAAElW,kBAAmB;QAAAvB,QAAA,EAC7B;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EACL1B,aAAa,CAACrB,OAAO,iBACpBrN,OAAA;MACEwP,KAAK,EAAE;QACLxD,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGwC,aAAa,CAAC1C,QAAQ,CAACzH,CAAC,IAAI;QACrCsJ,GAAG,EAAE,GAAGa,aAAa,CAAC1C,QAAQ,CAACvH,CAAC,IAAI;QACpC0H,SAAS,EAAE,wBAAwB;QACnCC,MAAM,EAAE,IAAI;QACZK,eAAe,EAAE,qBAAqB;QACtCwB,KAAK,EAAE,OAAO;QACdtB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,6BAA6B;QACxCN,OAAO,EAAE,CAAC;QACV6a,QAAQ,EAAE,GAAG;QACb5X,QAAQ,EAAE,GAAG;QACb5C,QAAQ,EAAE;MACZ,CAAE;MAAA6C,QAAA,GAEDhB,aAAa,CAACnB,OAAO,eACtBvN,OAAA;QACEwP,KAAK,EAAE;UACLxD,QAAQ,EAAE,UAAU;UACpB6B,GAAG,EAAE,KAAK;UACVqZ,KAAK,EAAE,KAAK;UACZpX,UAAU,EAAE,MAAM;UAClBpD,MAAM,EAAE,MAAM;UACduB,KAAK,EAAE,OAAO;UACdpB,QAAQ,EAAE,MAAM;UAChBD,MAAM,EAAE,SAAS;UACjBJ,OAAO,EAAE,UAAU;UACnBJ,MAAM,EAAE;QACV,CAAE;QACF+a,OAAO,EAAEtY,wBAAyB;QAAAa,QAAA,EACnC;MAAC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACN;EAAA,eACD,CAAC;AAEP,CAAC;;AAED;AAAAxG,EAAA,CA3mEMJ,WAAW;AAAA8d,EAAA,GAAX9d,WAAW;AA4mEjB,SAASsN,gBAAgBA,CAACyQ,IAAI,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAMvB,MAAM,GAAG;IACbwB,QAAQ,EAAED,UAAU,CAACC,QAAQ,IAAI,OAAO;IACxC5a,QAAQ,EAAE2a,UAAU,CAAC3a,QAAQ,IAAI,EAAE;IAAE;IACrCqB,UAAU,EAAEsZ,UAAU,CAACtZ,UAAU,IAAI,MAAM;IAC3CwZ,eAAe,EAAEF,UAAU,CAACE,eAAe,IAAI,CAAC;IAChDC,WAAW,EAAEH,UAAU,CAACG,WAAW,IAAI;MAAE3Q,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEjP,CAAC,EAAE;IAAI,CAAC;IACnEwE,eAAe,EAAE+a,UAAU,CAAC/a,eAAe,IAAI;MAAEuK,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEjP,CAAC,EAAE;IAAI,CAAC;IACjFkP,SAAS,EAAEqQ,UAAU,CAACrQ,SAAS,IAAI;MAAEH,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEjP,CAAC,EAAE;IAAI,CAAC;IAC/DuE,OAAO,EAAEgb,UAAU,CAAChb,OAAO,IAAI;EACjC,CAAC;;EAED;EACA,MAAMob,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;EAEvC;EACA;;EAEA;EACA,MAAMC,SAAS,GAAGF,OAAO,CAACG,WAAW,CAACX,IAAI,CAAC,CAACzZ,KAAK;;EAEjD;EACA,MAAMA,KAAK,GAAGma,SAAS,GAAG,CAAC,GAAGhC,MAAM,CAACzZ,OAAO,GAAG,CAAC,GAAGyZ,MAAM,CAACyB,eAAe;EACzE,MAAM7X,MAAM,GAAGoW,MAAM,CAACpZ,QAAQ,GAAG,CAAC,GAAGoZ,MAAM,CAACzZ,OAAO,GAAG,CAAC,GAAGyZ,MAAM,CAACyB,eAAe;EAEhFE,MAAM,CAAC9Z,KAAK,GAAGA,KAAK;EACpB8Z,MAAM,CAAC/X,MAAM,GAAGA,MAAM;;EAEtB;EACAkY,OAAO,CAACI,IAAI,GAAG,GAAGlC,MAAM,CAAC/X,UAAU,IAAI+X,MAAM,CAACpZ,QAAQ,MAAMoZ,MAAM,CAACwB,QAAQ,EAAE;EAC7EM,OAAO,CAACK,YAAY,GAAG,QAAQ;;EAE/B;EACA,MAAMC,MAAM,GAAG,CAAC;EAChBN,OAAO,CAACO,SAAS,CAAC,CAAC;EACnBP,OAAO,CAACQ,MAAM,CAACtC,MAAM,CAACyB,eAAe,GAAGW,MAAM,EAAEpC,MAAM,CAACyB,eAAe,CAAC;EACvEK,OAAO,CAACS,MAAM,CAAC1a,KAAK,GAAGmY,MAAM,CAACyB,eAAe,GAAGW,MAAM,EAAEpC,MAAM,CAACyB,eAAe,CAAC;EAC/EK,OAAO,CAACU,KAAK,CAAC3a,KAAK,GAAGmY,MAAM,CAACyB,eAAe,EAAEzB,MAAM,CAACyB,eAAe,EAAE5Z,KAAK,GAAGmY,MAAM,CAACyB,eAAe,EAAEzB,MAAM,CAACyB,eAAe,GAAGW,MAAM,EAAEA,MAAM,CAAC;EAC9IN,OAAO,CAACS,MAAM,CAAC1a,KAAK,GAAGmY,MAAM,CAACyB,eAAe,EAAE7X,MAAM,GAAGoW,MAAM,CAACyB,eAAe,GAAGW,MAAM,CAAC;EACxFN,OAAO,CAACU,KAAK,CAAC3a,KAAK,GAAGmY,MAAM,CAACyB,eAAe,EAAE7X,MAAM,GAAGoW,MAAM,CAACyB,eAAe,EAAE5Z,KAAK,GAAGmY,MAAM,CAACyB,eAAe,GAAGW,MAAM,EAAExY,MAAM,GAAGoW,MAAM,CAACyB,eAAe,EAAEW,MAAM,CAAC;EAChKN,OAAO,CAACS,MAAM,CAACvC,MAAM,CAACyB,eAAe,GAAGW,MAAM,EAAExY,MAAM,GAAGoW,MAAM,CAACyB,eAAe,CAAC;EAChFK,OAAO,CAACU,KAAK,CAACxC,MAAM,CAACyB,eAAe,EAAE7X,MAAM,GAAGoW,MAAM,CAACyB,eAAe,EAAEzB,MAAM,CAACyB,eAAe,EAAE7X,MAAM,GAAGoW,MAAM,CAACyB,eAAe,GAAGW,MAAM,EAAEA,MAAM,CAAC;EAChJN,OAAO,CAACS,MAAM,CAACvC,MAAM,CAACyB,eAAe,EAAEzB,MAAM,CAACyB,eAAe,GAAGW,MAAM,CAAC;EACvEN,OAAO,CAACU,KAAK,CAACxC,MAAM,CAACyB,eAAe,EAAEzB,MAAM,CAACyB,eAAe,EAAEzB,MAAM,CAACyB,eAAe,GAAGW,MAAM,EAAEpC,MAAM,CAACyB,eAAe,EAAEW,MAAM,CAAC;EAC9HN,OAAO,CAACW,SAAS,CAAC,CAAC;;EAEnB;EACAX,OAAO,CAACY,WAAW,GAAG,QAAQ1C,MAAM,CAAC0B,WAAW,CAAC3Q,CAAC,KAAKiP,MAAM,CAAC0B,WAAW,CAAC1Q,CAAC,KAAKgP,MAAM,CAAC0B,WAAW,CAACzQ,CAAC,KAAK+O,MAAM,CAAC0B,WAAW,CAAC1f,CAAC,GAAG;EAChI8f,OAAO,CAACa,SAAS,GAAG3C,MAAM,CAACyB,eAAe;EAC1CK,OAAO,CAACc,MAAM,CAAC,CAAC;;EAEhB;EACAd,OAAO,CAACe,SAAS,GAAG,QAAQ7C,MAAM,CAACxZ,eAAe,CAACuK,CAAC,KAAKiP,MAAM,CAACxZ,eAAe,CAACwK,CAAC,KAAKgP,MAAM,CAACxZ,eAAe,CAACyK,CAAC,KAAK+O,MAAM,CAACxZ,eAAe,CAACxE,CAAC,GAAG;EAC9I8f,OAAO,CAACgB,IAAI,CAAC,CAAC;;EAEd;EACAhB,OAAO,CAACe,SAAS,GAAG,QAAQ7C,MAAM,CAAC9O,SAAS,CAACH,CAAC,KAAKiP,MAAM,CAAC9O,SAAS,CAACF,CAAC,KAAKgP,MAAM,CAAC9O,SAAS,CAACD,CAAC,KAAK+O,MAAM,CAAC9O,SAAS,CAAClP,CAAC,GAAG;EACtH8f,OAAO,CAACnY,SAAS,GAAG,QAAQ;;EAE5B;EACAmY,OAAO,CAACiB,QAAQ,CAACzB,IAAI,EAAEzZ,KAAK,GAAG,CAAC,EAAE+B,MAAM,GAAG,CAAC,CAAC;;EAE7C;EACA,MAAMoZ,OAAO,GAAG,IAAI7pB,KAAK,CAAC8pB,aAAa,CAACtB,MAAM,CAAC;EAC/CqB,OAAO,CAACE,SAAS,GAAG/pB,KAAK,CAACgqB,YAAY;EACtCH,OAAO,CAACrS,WAAW,GAAG,IAAI;;EAE1B;EACA,MAAMyS,cAAc,GAAG,IAAIjqB,KAAK,CAACkqB,cAAc,CAAC;IAC9CthB,GAAG,EAAEihB,OAAO;IACZtS,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM4S,MAAM,GAAG,IAAInqB,KAAK,CAACoqB,MAAM,CAACH,cAAc,CAAC;EAC/CE,MAAM,CAACnU,KAAK,CAACxQ,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EAC7B2kB,MAAM,CAACrgB,QAAQ,CAACugB,SAAS,GAAG,KAAK,CAAC,CAAC;;EAEnC;EACAF,MAAM,CAAC1G,QAAQ,GAAG;IAChB0E,IAAI,EAAEA,IAAI;IACVtB,MAAM,EAAEA;EACV,CAAC;EAED,OAAOsD,MAAM;AACf;;AAIA;AACA5nB,MAAM,CAAC+nB,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAM7N,MAAM,GAAGgM,QAAQ,CAAC8B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAIhO,MAAM,EAAE;MACV;MACA,MAAMiO,MAAM,GAAGjO,MAAM,CAAC7P,QAAQ,CAAC3H,KAAK,CAAC,CAAC;;MAEtC;MACAwX,MAAM,CAAC7P,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BiX,MAAM,CAACzK,EAAE,CAACxM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBiX,MAAM,CAAC7J,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACA6J,MAAM,CAACvJ,YAAY,CAAC,CAAC;MACrBuJ,MAAM,CAACtJ,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAM5R,QAAQ,GAAGknB,QAAQ,CAAC8B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIppB,QAAQ,EAAE;QACZA,QAAQ,CAACmR,MAAM,CAAClN,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BjE,QAAQ,CAACsR,MAAM,CAAC,CAAC;MACnB;MAEA1P,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBwnB,GAAG,EAAEF,MAAM,CAACxW,OAAO,CAAC,CAAC;QACrB2W,GAAG,EAAEpO,MAAM,CAAC7P,QAAQ,CAACsH,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOhL,CAAC,EAAE;IACV/F,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEyE,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAGD;AACA,MAAMqT,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACFpZ,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAMuc,MAAM,GAAG,IAAI1f,UAAU,CAAC,CAAC;;IAE/B;IACA,IAAI;MACF,MAAM,CAAE6qB,gBAAgB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,UAAU,CAAC,GAAG,MAAM7M,OAAO,CAAC8M,GAAG,CAAC,CAClFvL,MAAM,CAACwL,SAAS,CAAC,GAAGpoB,QAAQ,4BAA4B,CAAC,EAC3D4c,MAAM,CAACwL,SAAS,CAAC,GAAGpoB,QAAQ,uBAAuB,CAAC,EACpD4c,MAAM,CAACwL,SAAS,CAAC,GAAGpoB,QAAQ,uBAAuB,CAAC,EAClD4c,MAAM,CAACwL,SAAS,CAAC,GAAGpoB,QAAQ,sBAAsB,CAAC,CAEtD,CAAC;;MAIF;MACAI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB5B,qBAAqB,GAAGupB,WAAW,CAACnpB,KAAK;MACzCJ,qBAAqB,CAACgG,QAAQ,CAAE2P,KAAK,IAAK;QACxC,IAAIA,KAAK,CAACxN,MAAM,EAAE;UACd,MAAMyN,WAAW,GAAG,IAAIpX,KAAK,CAAC6e,oBAAoB,CAAC;YACnDhQ,KAAK,EAAE,QAAQ;YAAG;YAClBiQ,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,GAAG;YACdC,eAAe,EAAE;UACnB,CAAC,CAAC;;UAEA;UACA,IAAI7H,KAAK,CAACrN,QAAQ,CAAClB,GAAG,EAAE;YACtBwO,WAAW,CAACxO,GAAG,GAAGuO,KAAK,CAACrN,QAAQ,CAAClB,GAAG;UACtC;UACAuO,KAAK,CAACiU,OAAO,GAAGhU,WAAW;QAC/B;MACF,CAAC,CAAC;MAEFjU,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B;MACA3B,qBAAqB,GAAGupB,WAAW,CAACppB,KAAK;MACzC;MACAH,qBAAqB,CAACuU,KAAK,CAACxQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxC;MACA/D,qBAAqB,CAAC+F,QAAQ,CAAE2P,KAAK,IAAK;QACxC,IAAIA,KAAK,CAACxN,MAAM,IAAIwN,KAAK,CAACrN,QAAQ,EAAE;UAClC;UACAqN,KAAK,CAACrN,QAAQ,CAACgV,SAAS,GAAG,GAAG;UAC9B3H,KAAK,CAACrN,QAAQ,CAACiV,SAAS,GAAG,GAAG;UAC9B5H,KAAK,CAACrN,QAAQ,CAACkV,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;MAEF7b,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA1B,oBAAoB,GAAGupB,UAAU,CAACrpB,KAAK;MACvC;MACA;MACA;MACAF,oBAAoB,CAAC8F,QAAQ,CAAE2P,KAAK,IAAK;QACvC,IAAIA,KAAK,CAACxN,MAAM,IAAIwN,KAAK,CAACrN,QAAQ,EAAE;UAClC;UACAqN,KAAK,CAACrN,QAAQ,CAACgV,SAAS,GAAG,GAAG;UAC9B3H,KAAK,CAACrN,QAAQ,CAACiV,SAAS,GAAG,GAAG;UAC9B5H,KAAK,CAACrN,QAAQ,CAACkV,eAAe,GAAG,GAAG;QAEtC;QACA,IAAI7H,KAAK,CAACxN,MAAM,EAAC;UACfwN,KAAK,CAACkU,UAAU,GAAG,IAAI;QACzB;MACF,CAAC,CAAC;;MAIF;MACAloB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE6nB,UAAU,CAAC5iB,UAAU,CAACC,MAAM,EAAE,GAAG,CAAC;MAC5D,IAAI2iB,UAAU,CAAC5iB,UAAU,IAAI4iB,UAAU,CAAC5iB,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7DnF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE6nB,UAAU,CAAC5iB,UAAU,CAACC,MAAM,EAAE,GAAG,CAAC;QACzDzG,eAAe,GAAGopB,UAAU;MAC9B,CAAC,MAAM;QACL9nB,OAAO,CAACmoB,IAAI,CAAC,cAAc,CAAC;MAC9B;MAEAnoB,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;;MAEzB;MACAzB,0BAA0B,GAAGmpB,gBAAgB,CAAClpB,KAAK;MACnDuB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEzB,0BAA0B,CAAC;MACjD;MACAA,0BAA0B,CAACqU,KAAK,CAACxQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C;MACA7D,0BAA0B,CAAC6F,QAAQ,CAAE2P,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAACxN,MAAM,IAAIwN,KAAK,CAACrN,QAAQ,EAAE;UAClC;UACAqN,KAAK,CAACrN,QAAQ,CAACgV,SAAS,GAAG,GAAG;UAC9B3H,KAAK,CAACrN,QAAQ,CAACiV,SAAS,GAAG,GAAG;UAC9B5H,KAAK,CAACrN,QAAQ,CAACkV,eAAe,GAAG,GAAG;QACxC;MACF,CAAC,CAAC;MAEA7b,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACA,IAAI;QACF,IAAI,CAACjD,qBAAqB,EAAE;UAC1B,MAAMupB,WAAW,GAAG,MAAMpL,MAAM,CAACwL,SAAS,CAAC,GAAGpoB,QAAQ,uBAAuB,CAAC;UAC9EvB,qBAAqB,GAAGupB,WAAW,CAACnpB,KAAK;QAC3C;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACF,CAAC,CAAC,OAAO2pB,GAAG,EAAE;QACZpoB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAE8mB,GAAG,CAAC;MAClC;IACF;EACF,CAAC,CAAC,OAAO9mB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;;AAED;AACA,MAAMgX,mBAAmB,GAAI5L,IAAI,IAAK;EACpC,MAAM2b,KAAK,GAAG;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,KAAK,CAAC3b,IAAI,CAAC,IAAI,MAAM;AAC9B,CAAC;;AAED;AACA,MAAMkL,iBAAiB,GAAGA,CAACnO,QAAQ,EAAEub,IAAI,EAAEtZ,KAAK,KAAK;EACnD;EACA,IAAI,CAACjN,KAAK,EAAE;IACVuB,OAAO,CAACmoB,IAAI,CAAC,oBAAoB,CAAC;IAClC;EACF;EAEA,IAAI;IACJ;IACA,MAAMnB,MAAM,GAAGzS,gBAAgB,CAACyQ,IAAI,CAAC;IACrCgC,MAAM,CAACvd,QAAQ,CAACpH,GAAG,CAACoH,QAAQ,CAACzH,CAAC,EAAE,EAAE,EAAE,CAACyH,QAAQ,CAACvH,CAAC,CAAC,CAAC,CAAE;;IAEnD;IACAgP,UAAU,CAAC,MAAM;MACb;MACA,IAAIzS,KAAK,IAAIuoB,MAAM,CAAC7gB,MAAM,EAAE;QAC9B1H,KAAK,CAAC2H,MAAM,CAAC4gB,MAAM,CAAC;MAClB;IACJ,CAAC,EAAE,GAAG,CAAC;;IAEP;IACAvoB,KAAK,CAAC2F,GAAG,CAAC4iB,MAAM,CAAC;;IAEjB;IACA;IACA;IACA;IACA;EACA,CAAC,CAAC,OAAO1lB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;AACF,CAAC;;AAED;AACA,MAAM4d,mBAAmB,GAAIyB,iBAAiB,IAAK;EACjD,IAAI,CAACliB,KAAK,EAAE;IACVuB,OAAO,CAACsB,KAAK,CAAC,gBAAgB,CAAC;IAC/B;EACF;EAEA,IAAI,CAACqf,iBAAiB,EAAE;IACtB3gB,OAAO,CAACsB,KAAK,CAAC,mBAAmB,CAAC;IAClC;EACF;;EAEA;EACA,IAAI,CAAC9C,0BAA0B,EAAE;IAC/BwB,OAAO,CAACsB,KAAK,CAAC,oBAAoB,CAAC;IACnC;IACA,MAAMkb,MAAM,GAAG,IAAI1f,UAAU,CAAC,CAAC;IAC/B0f,MAAM,CAACwL,SAAS,CAAC,GAAGpoB,QAAQ,4BAA4B,CAAC,CACtD0oB,IAAI,CAACX,gBAAgB,IAAI;MACxBnpB,0BAA0B,GAAGmpB,gBAAgB,CAAClpB,KAAK;MACnDD,0BAA0B,CAACqU,KAAK,CAACxQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C7D,0BAA0B,CAAC6F,QAAQ,CAAE2P,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAACxN,MAAM,IAAIwN,KAAK,CAACrN,QAAQ,EAAE;UAClCqN,KAAK,CAACrN,QAAQ,CAACgV,SAAS,GAAG,GAAG;UAC9B3H,KAAK,CAACrN,QAAQ,CAACiV,SAAS,GAAG,GAAG;UAC9B5H,KAAK,CAACrN,QAAQ,CAACkV,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;MACF7b,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;MACAif,mBAAmB,CAACyB,iBAAiB,CAAC;IACxC,CAAC,CAAC,CACD4H,KAAK,CAACjnB,KAAK,IAAI;MACdtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC;MACAknB,2BAA2B,CAAC7H,iBAAiB,CAAC;IAChD,CAAC,CAAC;IACJ;EACF;;EAEA;EACAtgB,gBAAgB,CAACuE,OAAO,CAAEob,QAAQ,IAAK;IACrC,IAAIvhB,KAAK,IAAIuhB,QAAQ,CAAC7b,KAAK,EAAE;MAC3B1F,KAAK,CAAC2H,MAAM,CAAC4Z,QAAQ,CAAC7b,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACF9D,gBAAgB,CAAC4F,KAAK,CAAC,CAAC;;EAExB;EACA4C,aAAa,CAACjE,OAAO,CAAC0L,YAAY,IAAI;IACpC,IAAIA,YAAY,CAACW,eAAe,KAAK,KAAK,EAAE;MAC1CjR,OAAO,CAACC,GAAG,CAAC,UAAUqQ,YAAY,CAACrC,IAAI,kBAAkB,CAAC;MAC1D;IACF;IAEA,IAAIqC,YAAY,CAACnH,QAAQ,IAAImH,YAAY,CAACpH,SAAS,IAAIoH,YAAY,CAACvF,OAAO,EAAE;MAC3E,MAAM0H,QAAQ,GAAGkO,iBAAiB,CAAClQ,YAAY,CAC7CC,UAAU,CAACJ,YAAY,CAACpH,SAAS,CAAC,EAClCwH,UAAU,CAACJ,YAAY,CAACnH,QAAQ,CAClC,CAAC;MAEDnJ,OAAO,CAACC,GAAG,CAAC,SAASqQ,YAAY,CAACrC,IAAI,KAAKqC,YAAY,CAACvF,OAAO,iBAAiB0H,QAAQ,CAACzQ,CAAC,KAAKyQ,QAAQ,CAACvQ,CAAC,GAAG,CAAC;MAE7G,IAAI;QACF;QACA,IAAI,CAAC1D,0BAA0B,IAAI,CAACA,0BAA0B,CAACsD,KAAK,EAAE;UACpE,MAAM,IAAI2mB,KAAK,CAAC,cAAc,CAAC;QACjC;;QAEA;QACA,MAAMlS,iBAAiB,GAAG/X,0BAA0B,CAACsD,KAAK,CAAC,CAAC;;QAE5D;QACAyU,iBAAiB,CAACtI,IAAI,GAAG,OAAOqC,YAAY,CAACrC,IAAI,EAAE;;QAEnD;QACAsI,iBAAiB,CAAC9M,QAAQ,CAACpH,GAAG,CAACoQ,QAAQ,CAACzQ,CAAC,EAAE,EAAE,EAAE,CAACyQ,QAAQ,CAACvQ,CAAC,CAAC;;QAE3D;QACAqU,iBAAiB,CAAC1D,KAAK,CAACxQ,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;QAEvC;QACAkU,iBAAiB,CAAC1B,WAAW,GAAG,GAAG;;QAEnC;QACA0B,iBAAiB,CAAClS,QAAQ,CAAC2P,KAAK,IAAI;UAClC,IAAIA,KAAK,CAACxN,MAAM,EAAE;YAChBwN,KAAK,CAACrN,QAAQ,CAACyN,WAAW,GAAG,KAAK;YAClCJ,KAAK,CAACrN,QAAQ,CAACmO,OAAO,GAAG,GAAG;YAC5Bd,KAAK,CAACrN,QAAQ,CAAC+hB,IAAI,GAAG7rB,KAAK,CAAC8rB,UAAU;YACtC3U,KAAK,CAACrN,QAAQ,CAACyZ,UAAU,GAAG,IAAI;YAChCpM,KAAK,CAACrN,QAAQ,CAACugB,SAAS,GAAG,IAAI;YAC/BlT,KAAK,CAACrN,QAAQ,CAAC0N,WAAW,GAAG,IAAI;YACjCL,KAAK,CAACa,WAAW,GAAG,GAAG;UACzB;QACF,CAAC,CAAC;;QAEF;QACA0B,iBAAiB,CAAC+J,QAAQ,GAAG;UAC3B5T,IAAI,EAAE,cAAc;UACpB3B,OAAO,EAAEuF,YAAY,CAACvF,OAAO;UAC7BkD,IAAI,EAAEqC,YAAY,CAACrC;QACrB,CAAC;;QAED;QACA;;QAEA;QACA;QACA,MAAM2a,oBAAoB,GAAG,IAAI/rB,KAAK,CAAC4kB,aAAa,CAAC,CAAC;QACtD,MAAMoH,eAAe,GAAG,GAAGjpB,QAAQ,qBAAqB,CAAC,CAAC;QAC1D,MAAMkpB,eAAe,GAAG,IAAIjsB,KAAK,CAAC4iB,iBAAiB,CAAC;UAClDha,GAAG,EAAEmjB,oBAAoB,CAACvN,IAAI,CAACwN,eAAe,CAAC;UAC/CzU,WAAW,EAAE,IAAI;UACjBU,OAAO,EAAE;QACX,CAAC,CAAC;QACF;QACA,MAAMiU,eAAe,GAAG,IAAIlsB,KAAK,CAACmlB,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;QACrD,MAAMgH,WAAW,GAAG,IAAInsB,KAAK,CAAC6iB,IAAI,CAACqJ,eAAe,EAAED,eAAe,CAAC;QACpE;QACAE,WAAW,CAACvf,QAAQ,CAACpH,GAAG,CAACoQ,QAAQ,CAACzQ,CAAC,GAAC,EAAE,EAAE,GAAG,EAAE,EAAEyQ,QAAQ,CAACvQ,CAAC,GAAC,EAAE,CAAC,CAAC;QAC9D;QACA8mB,WAAW,CAACpW,QAAQ,CAAC5Q,CAAC,GAAG,CAACkB,IAAI,CAACC,EAAE,GAAG,CAAC;QACrC;QACA6lB,WAAW,CAACnU,WAAW,GAAG,GAAG;QAC7B;QACAmU,WAAW,CAAC1I,QAAQ,GAAG;UACrB5T,IAAI,EAAE,aAAa;UACnB3B,OAAO,EAAEuF,YAAY,CAACvF,OAAO;UAC7BkD,IAAI,EAAEqC,YAAY,CAACrC;QACrB,CAAC;QACD;QACAxP,KAAK,CAAC2F,GAAG,CAAC4kB,WAAW,CAAC;QACtB;;QAEA;QACA3oB,gBAAgB,CAACgC,GAAG,CAACiO,YAAY,CAACvF,OAAO,EAAE;UACzC5G,KAAK,EAAEoS,iBAAiB;UACxBjG,YAAY,EAAEA,YAAY;UAC1B7G,QAAQ,EAAEgJ;QACZ,CAAC,CAAC;QAEFzS,OAAO,CAACC,GAAG,CAAC,SAASqQ,YAAY,CAACrC,IAAI,KAAKqC,YAAY,CAACvF,OAAO,kBAAkB0H,QAAQ,CAACzQ,CAAC,KAAK,CAACyQ,QAAQ,CAACvQ,CAAC,GAAG,CAAC;MACjH,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,QAAQgP,YAAY,CAACrC,IAAI,YAAY,EAAE3M,KAAK,CAAC;QAC3D;QACA;MACF;IACF;EACF,CAAC,CAAC;;EAEF;EACAtB,OAAO,CAACC,GAAG,CAAC,OAAOI,gBAAgB,CAACyY,IAAI,SAAS,CAAC;EAClDzY,gBAAgB,CAACuE,OAAO,CAAC,CAACob,QAAQ,EAAEjV,OAAO,KAAK;IAC9C/K,OAAO,CAACC,GAAG,CAAC,QAAQ8K,OAAO,KAAKiV,QAAQ,CAAC1P,YAAY,CAACrC,IAAI,EAAE,CAAC;EAC/D,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMua,2BAA2B,GAAI7H,iBAAiB,IAAK;EACzD9X,aAAa,CAACjE,OAAO,CAAC0L,YAAY,IAAI;IACpC;IACA,IAAIA,YAAY,CAACW,eAAe,KAAK,KAAK,EAAE;MAC1C;IACF;IAEA,IAAIX,YAAY,CAACnH,QAAQ,IAAImH,YAAY,CAACpH,SAAS,IAAIoH,YAAY,CAACvF,OAAO,EAAE;MAC3E;MACA,MAAM0H,QAAQ,GAAGkO,iBAAiB,CAAClQ,YAAY,CAC7CC,UAAU,CAACJ,YAAY,CAACpH,SAAS,CAAC,EAClCwH,UAAU,CAACJ,YAAY,CAACnH,QAAQ,CAClC,CAAC;MAEDoW,wBAAwB,CAACjP,YAAY,EAAEmC,QAAQ,EAAEkO,iBAAiB,CAAC;IACrE;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMpB,wBAAwB,GAAGA,CAACjP,YAAY,EAAEmC,QAAQ,EAAEkO,iBAAiB,KAAK;EAC9E;EACA,MAAMla,QAAQ,GAAG,IAAI5J,KAAK,CAAC2iB,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAClD,MAAM7Y,QAAQ,GAAG,IAAI9J,KAAK,CAAC4iB,iBAAiB,CAAC;IAC3C/T,KAAK,EAAE,QAAQ;IACf0I,WAAW,EAAE,KAAK;IAClBU,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAMyB,iBAAiB,GAAG,IAAI1Z,KAAK,CAAC6iB,IAAI,CAACjZ,QAAQ,EAAEE,QAAQ,CAAC;;EAE5D;EACA4P,iBAAiB,CAACtI,IAAI,GAAG,SAASqC,YAAY,CAACrC,IAAI,EAAE;;EAErD;EACAsI,iBAAiB,CAAC9M,QAAQ,CAACpH,GAAG,CAACoQ,QAAQ,CAACzQ,CAAC,EAAE,EAAE,EAAE,CAACyQ,QAAQ,CAACvQ,CAAC,CAAC;;EAE3D;EACAqU,iBAAiB,CAAC1B,WAAW,GAAG,GAAG;;EAEnC;EACA0B,iBAAiB,CAAC+J,QAAQ,GAAG;IAC3B5T,IAAI,EAAE,cAAc;IACpB3B,OAAO,EAAEuF,YAAY,CAACvF,OAAO;IAC7BkD,IAAI,EAAEqC,YAAY,CAACrC;EACrB,CAAC;;EAED;EACA,MAAMgb,gBAAgB,GAAG,IAAIpsB,KAAK,CAACqjB,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5D,MAAMgJ,gBAAgB,GAAG,IAAIrsB,KAAK,CAAC4iB,iBAAiB,CAAC;IACnD/T,KAAK,EAAE,QAAQ;IACf0I,WAAW,EAAE,IAAI;IACjBU,OAAO,EAAE,GAAG;IAAG;IACfsL,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAM+I,QAAQ,GAAG,IAAItsB,KAAK,CAAC6iB,IAAI,CAACuJ,gBAAgB,EAAEC,gBAAgB,CAAC;EACnEC,QAAQ,CAAClb,IAAI,GAAG,YAAYqC,YAAY,CAACrC,IAAI,EAAE;EAC/Ckb,QAAQ,CAAC7I,QAAQ,GAAG;IAClB5T,IAAI,EAAE,cAAc;IACpB3B,OAAO,EAAEuF,YAAY,CAACvF,OAAO;IAC7BkD,IAAI,EAAEqC,YAAY,CAACrC,IAAI;IACvBmb,UAAU,EAAE;EACd,CAAC;EAED7S,iBAAiB,CAACnS,GAAG,CAAC+kB,QAAQ,CAAC;;EAE/B;EACA1qB,KAAK,CAAC2F,GAAG,CAACmS,iBAAiB,CAAC;;EAE5B;EACAlW,gBAAgB,CAACgC,GAAG,CAACiO,YAAY,CAACvF,OAAO,EAAE;IACzC5G,KAAK,EAAEoS,iBAAiB;IACxBjG,YAAY,EAAEA,YAAY;IAC1B7G,QAAQ,EAAEgJ;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM4W,aAAa,GAAG,IAAIxsB,KAAK,CAACqjB,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAMoJ,aAAa,GAAG,IAAIzsB,KAAK,CAAC4iB,iBAAiB,CAAC;IAAE/T,KAAK,EAAE;EAAS,CAAC,CAAC;EACtE,MAAM6d,SAAS,GAAG,IAAI1sB,KAAK,CAAC6iB,IAAI,CAAC2J,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAAC9f,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;EAEhC;EACAknB,SAAS,CAACjJ,QAAQ,GAAG;IACnB5T,IAAI,EAAE,cAAc;IACpB3B,OAAO,EAAEuF,YAAY,CAACvF,OAAO;IAC7BkD,IAAI,EAAEqC,YAAY,CAACrC;EACrB,CAAC;EAEDsI,iBAAiB,CAACnS,GAAG,CAACmlB,SAAS,CAAC;EAEhCvpB,OAAO,CAACC,GAAG,CAAC,SAASqQ,YAAY,CAACrC,IAAI,KAAKqC,YAAY,CAACvF,OAAO,kBAAkB0H,QAAQ,CAACzQ,CAAC,KAAK,CAACyQ,QAAQ,CAACvQ,CAAC,GAAG,CAAC;AACjH,CAAC;;AAED;AACA,MAAM4T,iBAAiB,GAAIL,OAAO,IAAK;EACrC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMI,oBAAoB,GAAI2T,OAAO,IAAK;EACxC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAM3E,kBAAkB,GAAI4E,eAAe,IAAK;EAC9C;EACA,IAAIrqB,MAAM,CAAC+L,0BAA0B,IAAI/L,MAAM,CAAC+L,0BAA0B,CAACqD,OAAO,EAAE;IAClFwQ,aAAa,CAAC5f,MAAM,CAAC+L,0BAA0B,CAACqD,OAAO,CAAC;IACxDpP,MAAM,CAAC+L,0BAA0B,CAACqD,OAAO,GAAG,IAAI;IAChDxO,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B;;EAEA;EACA,IAAIb,MAAM,CAAC8L,mBAAmB,EAAE;IAC9B9L,MAAM,CAAC8L,mBAAmB,CAACsD,OAAO,GAAG,IAAI;EAC3C;;EAEA;EACAib,eAAe,CAAChT,IAAI,KAAK;IACvB,GAAGA,IAAI;IACP3L,OAAO,EAAE,KAAK;IACdE,OAAO,EAAE,IAAI;IAAE;IACfC,MAAM,EAAE,EAAE,CAAK;EACjB,CAAC,CAAC,CAAC;EAEHjL,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;AAChC,CAAC;;AAED;AACAb,MAAM,CAACsqB,qBAAqB,GAAI3e,OAAO,IAAK;EAC1C,IAAI;IAAA,IAAA4e,qBAAA,EAAAC,sBAAA;IACF;IACA,MAAM5T,YAAY,GAAG3V,gBAAgB,CAACmC,GAAG,CAACuI,OAAO,IAAI,GAAG,CAAC;IACzD,IAAI,CAACiL,YAAY,EAAE;MACjBhW,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEyJ,OAAO,CAAC;;MAEtC;MACA/K,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxBI,gBAAgB,CAACuE,OAAO,CAAC,CAACilB,KAAK,EAAE7c,EAAE,KAAK;QACtChN,OAAO,CAACC,GAAG,CAAC,KAAK+M,EAAE,KAAK6c,KAAK,CAACvZ,YAAY,CAACrC,IAAI,EAAE,CAAC;MACpD,CAAC,CAAC;MAEF,OAAO,KAAK;IACd;;IAEA;IACA,MAAM6b,UAAU,GAAG9T,YAAY,CAAC7R,KAAK;;IAErC;IACA,MAAM4lB,SAAS,GAAGzpB,kBAAkB,CAACkC,GAAG,CAACuI,OAAO,CAAC;IACjD,MAAMuF,YAAY,GAAG0F,YAAY,CAAC1F,YAAY;;IAE9C;IACA,IAAItF,OAAO;IAEX,IAAI+e,SAAS,IAAIA,SAAS,CAAC9e,MAAM,EAAE;MACjCD,OAAO,gBACLvN,OAAA;QAAKwP,KAAK,EAAE;UAAEhD,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAEkZ,SAAS,EAAE,OAAO;UAAEuF,SAAS,EAAE;QAAO,CAAE;QAAA7c,QAAA,gBACpF1P,OAAA;UAAKwP,KAAK,EAAE;YACVtB,UAAU,EAAE,MAAM;YAClByB,YAAY,EAAE,KAAK;YACnB9C,QAAQ,EAAE,MAAM;YAChB2f,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAA/c,QAAA,GACCmD,YAAY,CAACrC,IAAI,EAAC,QAAM,EAAClD,OAAO,EAAC,GACpC;QAAA;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNpQ,OAAA;UAAA0P,QAAA,EACG4c,SAAS,CAAC9e,MAAM,CAACxF,GAAG,CAAC,CAAC+P,KAAK,EAAE2U,KAAK,KAAK;YACtC,IAAIC,UAAU;YACd,QAAQ5U,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBAAEoU,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;gBAAEA,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;cAAE;gBAASA,UAAU,GAAG,SAAS;gBAAE;YAC7C;YAEA,oBACE3sB,OAAA;cAAiBwP,KAAK,EAAE;gBACtBG,YAAY,EAAE,KAAK;gBACnBlD,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE,KAAK;gBACnBE,QAAQ,EAAE;cACZ,CAAE;cAAA6C,QAAA,gBACA1P,OAAA;gBAAKwP,KAAK,EAAE;kBAAEtB,UAAU,EAAE;gBAAO,CAAE;gBAAAwB,QAAA,EAChC2I,iBAAiB,CAACN,KAAK,CAACC,OAAO;cAAC;gBAAA/H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNpQ,OAAA;gBAAKwP,KAAK,EAAE;kBAAEnD,OAAO,EAAE,MAAM;kBAAEugB,cAAc,EAAE;gBAAgB,CAAE;gBAAAld,QAAA,gBAC/D1P,OAAA;kBAAA0P,QAAA,EAAM;gBAAI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBpQ,OAAA;kBAAMwP,KAAK,EAAE;oBACXvB,KAAK,EAAE0e,UAAU;oBACjBze,UAAU,EAAE,MAAM;oBAClBzB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAA+C,QAAA,EACCqI,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGR,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAG;gBAAI;kBAAAtI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNpQ,OAAA;gBAAKwP,KAAK,EAAE;kBAAEnD,OAAO,EAAE,MAAM;kBAAEugB,cAAc,EAAE;gBAAgB,CAAE;gBAAAld,QAAA,gBAC/D1P,OAAA;kBAAA0P,QAAA,EAAM;gBAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBpQ,OAAA;kBAAMwP,KAAK,EAAE;oBAAEtB,UAAU,EAAE;kBAAO,CAAE;kBAAAwB,QAAA,GAAEqI,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAAvI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAzBEsc,KAAK;cAAAzc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNpQ,OAAA;UAAKwP,KAAK,EAAE;YAAEqd,SAAS,EAAE,KAAK;YAAEhgB,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE;UAAO,CAAE;UAAAyB,QAAA,GAAC,4BAC3D,EAAC,IAAIjF,IAAI,CAAC,CAAC,CAACqiB,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAA7c,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACL7C,OAAO,gBACLvN,OAAA;QAAKwP,KAAK,EAAE;UAAEhD,OAAO,EAAE,KAAK;UAAEiD,QAAQ,EAAE;QAAQ,CAAE;QAAAC,QAAA,gBAChD1P,OAAA;UAAKwP,KAAK,EAAE;YAAEtB,UAAU,EAAE,MAAM;YAAEyB,YAAY,EAAE;UAAM,CAAE;UAAAD,QAAA,EAAEmD,YAAY,CAACrC;QAAI;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClFpQ,OAAA;UAAA0P,QAAA,GAAK,kBAAM,EAACpC,OAAO;QAAA;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1BpQ,OAAA;UAAA0P,QAAA,EAAK;QAAU;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN;IACH;;IAEA;IACA,MAAM2c,OAAO,GAAGprB,MAAM,CAACoa,UAAU,GAAG,CAAC,GAAE,GAAG;IAC1C,MAAMiR,OAAO,GAAGrrB,MAAM,CAACqa,WAAW,GAAG,CAAC,GAAE,GAAG;;IAE3C;IACA,MAAMgQ,eAAe,IAAAE,qBAAA,GAAGrE,QAAQ,CAAC8B,aAAa,CAAC,OAAO,CAAC,cAAAuC,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCe,gBAAgB,cAAAd,sBAAA,uBAAjDA,sBAAA,CAAmD/e,sBAAsB;IAEjG,IAAI4e,eAAe,EAAE;MACnB;MACAA,eAAe,CAAC;QACd3e,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBtB,QAAQ,EAAE;UAAEzH,CAAC,EAAEwoB,OAAO;UAAEtoB,CAAC,EAAEuoB;QAAQ,CAAC;QACpCzf,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAA8e,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE9e,MAAM,KAAI;MAC/B,CAAC,CAAC;MAEFjL,OAAO,CAACC,GAAG,CAAC,SAASqQ,YAAY,CAACrC,IAAI,KAAKlD,OAAO,UAAU,CAAC;MAC7D,OAAO,IAAI;IACb,CAAC,MAAM;MACL;MACA,MAAM4f,OAAO,GAAGrF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CoF,OAAO,CAAC1d,KAAK,CAACxD,QAAQ,GAAG,UAAU;MACnCkhB,OAAO,CAAC1d,KAAK,CAACtD,IAAI,GAAG,GAAG6gB,OAAO,IAAI;MACnCG,OAAO,CAAC1d,KAAK,CAAC3B,GAAG,GAAG,GAAGmf,OAAO,IAAI;MAClCE,OAAO,CAAC1d,KAAK,CAACrD,SAAS,GAAG,wBAAwB;MAClD+gB,OAAO,CAAC1d,KAAK,CAACpD,MAAM,GAAG,MAAM;MAC7B8gB,OAAO,CAAC1d,KAAK,CAAC/C,eAAe,GAAG,qBAAqB;MACrDygB,OAAO,CAAC1d,KAAK,CAACvB,KAAK,GAAG,OAAO;MAC7Bif,OAAO,CAAC1d,KAAK,CAAC7C,YAAY,GAAG,KAAK;MAClCugB,OAAO,CAAC1d,KAAK,CAAC1C,SAAS,GAAG,8BAA8B;MACxDogB,OAAO,CAAC1d,KAAK,CAAChD,OAAO,GAAG,KAAK;MAC7B0gB,OAAO,CAAC1d,KAAK,CAACC,QAAQ,GAAG,OAAO;MAChCyd,OAAO,CAAC1d,KAAK,CAAC3C,QAAQ,GAAG,MAAM;MAE/BqgB,OAAO,CAACC,SAAS,GAAG;AAC1B;AACA,YAAYta,YAAY,CAACrC,IAAI,SAASlD,OAAO;AAC7C;AACA;AACA,qBAAqBA,OAAO;AAC5B,eAAegf,SAAS,GAAG,UAAU,GAAG,YAAY;AACpD;AACA;AACA,OAAO;MAEDzE,QAAQ,CAACuF,IAAI,CAAC5Q,WAAW,CAAC0Q,OAAO,CAAC;;MAElC;MACA,MAAMG,WAAW,GAAGH,OAAO,CAACvD,aAAa,CAAC,QAAQ,CAAC;MACnD,IAAI0D,WAAW,EAAE;QACfA,WAAW,CAAC5M,gBAAgB,CAAC,OAAO,EAAE,MAAM;UAC1CoH,QAAQ,CAACuF,IAAI,CAACnM,WAAW,CAACiM,OAAO,CAAC;QACpC,CAAC,CAAC;MACJ;MAEA3qB,OAAO,CAACC,GAAG,CAAC,gBAAgBqQ,YAAY,CAACrC,IAAI,KAAKlD,OAAO,OAAO,CAAC;MACjE,OAAO,IAAI;IACb;EACF,CAAC,CAAC,OAAOzJ,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACAlC,MAAM,CAAC2rB,iBAAiB,GAAG,MAAM;EAC/B/qB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;EAErB,IAAI,CAACI,gBAAgB,IAAIA,gBAAgB,CAACyY,IAAI,KAAK,CAAC,EAAE;IACpD9Y,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,OAAO,EAAE;EACX;EAEA,MAAM+qB,IAAI,GAAG,EAAE;EACf3qB,gBAAgB,CAACuE,OAAO,CAAC,CAACilB,KAAK,EAAE7c,EAAE,KAAK;IACtChN,OAAO,CAACC,GAAG,CAAC,SAAS+M,EAAE,SAAS6c,KAAK,CAACvZ,YAAY,CAACrC,IAAI,EAAE,CAAC;IAC1D+c,IAAI,CAAC5U,IAAI,CAAC;MACRpJ,EAAE;MACFiB,IAAI,EAAE4b,KAAK,CAACvZ,YAAY,CAACrC,IAAI;MAC7BxE,QAAQ,EAAEogB,KAAK,CAACpgB;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOuhB,IAAI;AACb,CAAC;;AAGD;AACA5rB,MAAM,CAAC+R,qBAAqB,GAAIpG,OAAO,IAAK;EAC1C,IAAI;IACF;IACAA,OAAO,GAAGuL,MAAM,CAACvL,OAAO,CAAC;IAEzB/K,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE8K,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;IAC/E/K,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,gBAAgB,CAACyY,IAAI,CAAC;;IAE3D;IACA,IAAI9C,YAAY,GAAG3V,gBAAgB,CAACmC,GAAG,CAACuI,OAAO,CAAC;IAChD,IAAI,CAACiL,YAAY,EAAE;MACjB;MACA,MAAMiV,SAAS,GAAG/U,QAAQ,CAACnL,OAAO,CAAC;MACnCiL,YAAY,GAAG3V,gBAAgB,CAACmC,GAAG,CAACyoB,SAAS,CAAC;MAE9C,IAAIjV,YAAY,EAAE;QAChBhW,OAAO,CAACC,GAAG,CAAC,UAAUgrB,SAAS,SAAS,CAAC;QACzClgB,OAAO,GAAGkgB,SAAS,CAAC,CAAC;MACvB;IACF;IAEA,IAAI,CAACjV,YAAY,EAAE;MACjBhW,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEyJ,OAAO,CAAC;MACtC,OAAO,KAAK;IACd;IAEA,MAAMgf,SAAS,GAAGzpB,kBAAkB,CAACkC,GAAG,CAACuI,OAAO,CAAC;IACjD,MAAMuF,YAAY,GAAG0F,YAAY,CAAC1F,YAAY;;IAE9C;IACA,MAAM4a,YAAY,GAAGnB,SAAS,IAAIA,SAAS,CAAC9e,MAAM,IAAI8e,SAAS,CAAC9e,MAAM,CAAC9F,MAAM,GAAG,CAAC;IAEjF,IAAI6F,OAAO;;IAEX;IACA,MAAMmgB,YAAY,GAAG;MACnB1hB,QAAQ,EAAE,UAAU;MACpB6B,GAAG,EAAE,KAAK;MACVqZ,KAAK,EAAE,MAAM;MACbpZ,KAAK,EAAE,MAAM;MACb+B,MAAM,EAAE,MAAM;MACdxD,OAAO,EAAE,MAAM;MACfugB,cAAc,EAAE,QAAQ;MACxBe,UAAU,EAAE,QAAQ;MACpBhhB,YAAY,EAAE,KAAK;MACnBmD,UAAU,EAAE,iBAAiB;MAC7B1D,MAAM,EAAE;IACV,CAAC;;IAED;IACA,MAAMwhB,WAAW,GAAGA,CAAA,kBAClB5tB,OAAA;MAAKwP,KAAK,EAAEke,YAAa;MAAAhe,QAAA,eACvB1P,OAAA;QAAKwP,KAAK,EAAE;UACVnD,OAAO,EAAE,MAAM;UACfwhB,aAAa,EAAE,QAAQ;UACvBF,UAAU,EAAE,QAAQ;UACpBxhB,SAAS,EAAE;QACb,CAAE;QAAAuD,QAAA,gBACA1P,OAAA;UAAMwP,KAAK,EAAE;YACXvB,KAAK,EAAE,SAAS;YAChBpB,QAAQ,EAAE,MAAM;YAChBqB,UAAU,EAAE,MAAM;YAClBF,UAAU,EAAE;UACd,CAAE;UAAA0B,QAAA,EAAC;QAAC;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACXpQ,OAAA;UAAMwP,KAAK,EAAE;YACX1B,KAAK,EAAE,CAAC;YACR+B,MAAM,EAAE,CAAC;YACTie,UAAU,EAAE,uBAAuB;YACnCC,WAAW,EAAE,uBAAuB;YACpCvB,YAAY,EAAE,oBAAoB;YAClCK,SAAS,EAAE;UACb;QAAE;UAAA5c,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;IAED,IAAIqd,YAAY,EAAE;MAChB;MACA,MAAMO,QAAQ,GAAG;QACf,GAAG,EAAE;UAAEC,GAAG,EAAE,GAAG;UAAEhf,IAAI,EAAE;QAAO,CAAC;QAAE,GAAG,EAAE;UAAEgf,GAAG,EAAE,GAAG;UAAEhf,IAAI,EAAE;QAAW,CAAC;QAAE,GAAG,EAAE;UAAEgf,GAAG,EAAE,GAAG;UAAEhf,IAAI,EAAE;QAAQ,CAAC;QACtG,GAAG,EAAE;UAAEgf,GAAG,EAAE,GAAG;UAAEhf,IAAI,EAAE;QAAO,CAAC;QAAE,GAAG,EAAE;UAAEgf,GAAG,EAAE,GAAG;UAAEhf,IAAI,EAAE;QAAW,CAAC;QAAE,GAAG,EAAE;UAAEgf,GAAG,EAAE,GAAG;UAAEhf,IAAI,EAAE;QAAQ,CAAC;QACtG,GAAG,EAAE;UAAEgf,GAAG,EAAE,GAAG;UAAEhf,IAAI,EAAE;QAAO,CAAC;QAAE,IAAI,EAAE;UAAEgf,GAAG,EAAE,GAAG;UAAEhf,IAAI,EAAE;QAAW,CAAC;QAAE,IAAI,EAAE;UAAEgf,GAAG,EAAE,GAAG;UAAEhf,IAAI,EAAE;QAAQ,CAAC;QACxG,IAAI,EAAE;UAAEgf,GAAG,EAAE,GAAG;UAAEhf,IAAI,EAAE;QAAO,CAAC;QAAE,IAAI,EAAE;UAAEgf,GAAG,EAAE,GAAG;UAAEhf,IAAI,EAAE;QAAW,CAAC;QAAE,IAAI,EAAE;UAAEgf,GAAG,EAAE,GAAG;UAAEhf,IAAI,EAAE;QAAQ;MAC1G,CAAC;MAED,MAAMif,SAAS,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC;MAC/C,MAAMC,QAAQ,GAAG;QAAEC,CAAC,EAAE,SAAS;QAAEC,CAAC,EAAE,SAAS;QAAEC,CAAC,EAAE;MAAU,CAAC;MAC7D,MAAMC,OAAO,GAAG;QAAEC,CAAC,EAAE,CAAC,CAAC;QAAEC,CAAC,EAAE,CAAC,CAAC;QAAEC,CAAC,EAAE,CAAC,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAE,CAAC;MAE9CrC,SAAS,CAAC9e,MAAM,CAACrG,OAAO,CAAC4Q,KAAK,IAAI;QAChC,MAAM/P,GAAG,GAAGgmB,QAAQ,CAACjW,KAAK,CAACC,OAAO,CAAC;QACnC,IAAIhQ,GAAG,EAAE;UACPumB,OAAO,CAACvmB,GAAG,CAACimB,GAAG,CAAC,CAACjmB,GAAG,CAACiH,IAAI,CAAC,GAAG;YAC3BhB,KAAK,EAAEkgB,QAAQ,CAACpW,KAAK,CAACQ,YAAY,CAAC,IAAI,MAAM;YAC7CC,UAAU,EAAET,KAAK,CAACS;UACpB,CAAC;QACH;MACF,CAAC,CAAC;MAEFjL,OAAO,gBACLvN,OAAA;QAAKwP,KAAK,EAAE;UAAEhD,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAEgC,UAAU,EAAE,kBAAkB;UAAE9D,QAAQ,EAAE;QAAW,CAAE;QAAA0D,QAAA,gBACnG1P,OAAA,CAAC4tB,WAAW;UAAA3d,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfpQ,OAAA;UAAKwP,KAAK,EAAE;YAAEtB,UAAU,EAAE,MAAM;YAAEyB,YAAY,EAAE,KAAK;YAAE9C,QAAQ,EAAE,MAAM;YAAE+C,SAAS,EAAE;UAAS,CAAE;UAAAF,QAAA,GAAEmD,YAAY,CAACrC,IAAI,EAAC,cAAE;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3HpQ,OAAA;UAAKwP,KAAK,EAAE;YACVnD,OAAO,EAAE,MAAM;YACfuiB,gBAAgB,EAAE,gBAAgB;YAClCC,mBAAmB,EAAE,gBAAgB;YACrCjC,cAAc,EAAE,QAAQ;YACxBe,UAAU,EAAE,QAAQ;YACpB7d,UAAU,EAAE,wBAAwB;YACpCnD,YAAY,EAAE,KAAK;YACnBoD,MAAM,EAAE,QAAQ;YAChB/D,QAAQ,EAAE;UACZ,CAAE;UAAA0D,QAAA,gBAGA1P,OAAA;YAAKwP,KAAK,EAAE;cAAEsf,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEnf,SAAS,EAAE,QAAQ;cAAEvD,OAAO,EAAE,MAAM;cAAEugB,cAAc,EAAE,QAAQ;cAAE9e,KAAK,EAAE;YAAO,CAAE;YAAA4B,QAAA,EACtHwe,SAAS,CAAClmB,GAAG,CAAC,CAACiH,IAAI,EAAEyd,KAAK,KAAK;cAC9B;cACA,IAAIsC,YAAY,GAAGtC,KAAK;cACxB,IAAIA,KAAK,KAAK,CAAC,EAAEsC,YAAY,GAAG,CAAC,CAAC,KAC7B,IAAItC,KAAK,KAAK,CAAC,EAAEsC,YAAY,GAAG,CAAC;cAEtC,MAAMC,WAAW,GAAGf,SAAS,CAACc,YAAY,CAAC;;cAE3C;cACA,MAAME,WAAW,GAAG,CAAC,CAAC;cACtB,IAAID,WAAW,KAAK,MAAM,EAAE;gBAAE;gBAC5BC,WAAW,CAACC,WAAW,GAAG,KAAK;cACjC,CAAC,MAAM,IAAIF,WAAW,KAAK,UAAU,EAAE;gBAAE;gBACvCC,WAAW,CAACE,UAAU,GAAG,MAAM;gBAC/BF,WAAW,CAACC,WAAW,GAAG,MAAM;cAClC,CAAC,MAAM,IAAIF,WAAW,KAAK,OAAO,EAAE;gBAAE;gBACpCC,WAAW,CAACE,UAAU,GAAG,KAAK;cAChC;cAEA,OAAOb,OAAO,CAACC,CAAC,CAACS,WAAW,CAAC;cAAA;cAC3B;cACA;cACA;cACA;cACA;cACA;cACAjvB,OAAA;gBAAuBwP,KAAK,EAAE;kBAAC2f,WAAW,EAAEF,WAAW,KAAK,MAAM,GAAG,CAAC,GAAG,MAAM;kBAAE5iB,OAAO,EAAE,MAAM;kBAAEwhB,aAAa,EAAE,QAAQ;kBAAEF,UAAU,EAAE;gBAAQ,CAAE;gBAAAje,QAAA,gBAC/I1P,OAAA;kBAAKwP,KAAK,EAAE;oBAAC3C,QAAQ,EAAC,MAAM;oBAAEoB,KAAK,EAAEsgB,OAAO,CAACC,CAAC,CAACS,WAAW,CAAC,CAAChhB,KAAK;oBAAEC,UAAU,EAAC,MAAM;oBAAEyB,YAAY,EAAE;kBAAK,CAAE;kBAAAD,QAAA,EAAE6e,OAAO,CAACC,CAAC,CAACS,WAAW,CAAC,CAACzW;gBAAU;kBAAAvI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrJpQ,OAAA;kBAAMwP,KAAK,EAAE;oBAACvB,KAAK,EAAEsgB,OAAO,CAACC,CAAC,CAACS,WAAW,CAAC,CAAChhB,KAAK;oBAAEpB,QAAQ,EAAC,MAAM;oBAAEmB,UAAU,EAAE;kBAAM,CAAE;kBAAA0B,QAAA,EACrFuf,WAAW,KAAK,MAAM,GAAG,WAAW,GAAGA,WAAW,KAAK,UAAU,GAAG,WAAW,GAAG;gBAAW;kBAAAhf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA,GAJC6e,WAAW;gBAAAhf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKhB,CACN;YACH,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNpQ,OAAA;YAAKwP,KAAK,EAAE;cAAEsf,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEnf,SAAS,EAAE,QAAQ;cAAEvD,OAAO,EAAE,MAAM;cAAEugB,cAAc,EAAE,QAAQ;cAAE9e,KAAK,EAAE;YAAO,CAAE;YAAA4B,QAAA,EACtHwe,SAAS,CAAClmB,GAAG,CAACiH,IAAI,IAAIsf,OAAO,CAACG,CAAC,CAACzf,IAAI,CAAC,iBACpCjP,OAAA;cAAgBwP,KAAK,EAAE;gBAAC2f,WAAW,EAAElgB,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,MAAM;gBAAE5C,OAAO,EAAE,MAAM;gBAAEwhB,aAAa,EAAE,QAAQ;gBAAEF,UAAU,EAAE;cAAQ,CAAE;cAAAje,QAAA,gBAClI1P,OAAA;gBAAMwP,KAAK,EAAE;kBAACvB,KAAK,EAAEsgB,OAAO,CAACG,CAAC,CAACzf,IAAI,CAAC,CAAChB,KAAK;kBAAEpB,QAAQ,EAAC,MAAM;kBAAEmB,UAAU,EAAE;gBAAM,CAAE;gBAAA0B,QAAA,EAC9ET,IAAI,KAAK,MAAM,GAAG,WAAW,GAAGA,IAAI,KAAK,UAAU,GAAG,WAAW,GAAG;cAAW;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACPpQ,OAAA;gBAAKwP,KAAK,EAAE;kBAAC3C,QAAQ,EAAC,MAAM;kBAAEoB,KAAK,EAAEsgB,OAAO,CAACG,CAAC,CAACzf,IAAI,CAAC,CAAChB,KAAK;kBAAEC,UAAU,EAAC,MAAM;kBAAE2e,SAAS,EAAE;gBAAK,CAAE;gBAAAnd,QAAA,EAAE6e,OAAO,CAACG,CAAC,CAACzf,IAAI,CAAC,CAACuJ;cAAU;gBAAAvI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAJ5HnB,IAAI;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKT,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAINpQ,OAAA;YAAKwP,KAAK,EAAE;cAAEsf,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEnf,SAAS,EAAE;YAAS,CAAE;YAAAF,QAAA,EAC5Dwe,SAAS,CAAClmB,GAAG,CAAC,CAACiH,IAAI,EAAEyd,KAAK,KAAK;cAC9B;cACA,IAAIsC,YAAY,GAAGtC,KAAK;cACxB,IAAIA,KAAK,KAAK,CAAC,EAAEsC,YAAY,GAAG,CAAC,CAAC,KAC7B,IAAItC,KAAK,KAAK,CAAC,EAAEsC,YAAY,GAAG,CAAC;cAEtC,MAAMC,WAAW,GAAGf,SAAS,CAACc,YAAY,CAAC;cAE3C,OAAOT,OAAO,CAACE,CAAC,CAACQ,WAAW,CAAC,iBAC3BjvB,OAAA;gBAAuBwP,KAAK,EAAE;kBAACG,YAAY,EAAC,KAAK;kBAAEtD,OAAO,EAAE,MAAM;kBAAEshB,UAAU,EAAE,QAAQ;kBAAEf,cAAc,EAAE;gBAAY,CAAE;gBAAAld,QAAA,gBACtH1P,OAAA;kBAAMwP,KAAK,EAAE;oBAACvB,KAAK,EAAEsgB,OAAO,CAACE,CAAC,CAACQ,WAAW,CAAC,CAAChhB,KAAK;oBAAEpB,QAAQ,EAAC,MAAM;oBAAEmB,UAAU,EAAE;kBAAM,CAAE;kBAAA0B,QAAA,EACrFuf,WAAW,KAAK,MAAM,GAAG,WAAW,GAAGA,WAAW,KAAK,UAAU,GAAG,WAAW,GAAG;gBAAW;kBAAAhf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC,eACPpQ,OAAA;kBAAKwP,KAAK,EAAE;oBACV3C,QAAQ,EAAC,MAAM;oBACfoB,KAAK,EAAEsgB,OAAO,CAACE,CAAC,CAACQ,WAAW,CAAC,CAAChhB,KAAK;oBACnCC,UAAU,EAAC,MAAM;oBACjBkhB,UAAU,EAAE;kBACd,CAAE;kBAAA1f,QAAA,EAAE6e,OAAO,CAACE,CAAC,CAACQ,WAAW,CAAC,CAACzW;gBAAU;kBAAAvI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GATpC6e,WAAW;gBAAAhf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUhB,CACN;YACH,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNpQ,OAAA;YAAKwP,KAAK,EAAE;cAAEsf,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEnf,SAAS,EAAE;YAAS,CAAE;YAAAF,QAAA,EAC5Dwe,SAAS,CAAClmB,GAAG,CAACiH,IAAI,IAAIsf,OAAO,CAACI,CAAC,CAAC1f,IAAI,CAAC,iBACpCjP,OAAA;cAAgBwP,KAAK,EAAE;gBAACG,YAAY,EAAC,KAAK;gBAAEtD,OAAO,EAAE,MAAM;gBAAEshB,UAAU,EAAE,QAAQ;gBAAEf,cAAc,EAAE;cAAU,CAAE;cAAAld,QAAA,gBAC7G1P,OAAA;gBAAKwP,KAAK,EAAE;kBACV3C,QAAQ,EAAC,MAAM;kBACfoB,KAAK,EAAEsgB,OAAO,CAACI,CAAC,CAAC1f,IAAI,CAAC,CAAChB,KAAK;kBAC5BC,UAAU,EAAC,MAAM;kBACjBihB,WAAW,EAAE;gBACf,CAAE;gBAAAzf,QAAA,EAAE6e,OAAO,CAACI,CAAC,CAAC1f,IAAI,CAAC,CAACuJ;cAAU;gBAAAvI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrCpQ,OAAA;gBAAMwP,KAAK,EAAE;kBAACvB,KAAK,EAAEsgB,OAAO,CAACI,CAAC,CAAC1f,IAAI,CAAC,CAAChB,KAAK;kBAAEpB,QAAQ,EAAC,MAAM;kBAAEmB,UAAU,EAAE;gBAAM,CAAE;gBAAA0B,QAAA,EAC9ET,IAAI,KAAK,MAAM,GAAG,WAAW,GAAGA,IAAI,KAAK,UAAU,GAAG,WAAW,GAAG;cAAW;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA,GATCnB,IAAI;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUT,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpQ,OAAA;UAAKwP,KAAK,EAAE;YAAEqd,SAAS,EAAE,KAAK;YAAEhgB,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE,MAAM;YAAE2B,SAAS,EAAE;UAAS,CAAE;UAAAF,QAAA,GAAC,4BAChF,EAAC,IAAIjF,IAAI,CAAC,CAAC,CAACqiB,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAA7c,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACL;MACA7C,OAAO,gBACLvN,OAAA;QAAKwP,KAAK,EAAE;UAAEhD,OAAO,EAAE,MAAM;UAAEsB,KAAK,EAAE,OAAO;UAAEgC,UAAU,EAAE,kBAAkB;UAAE9D,QAAQ,EAAE;QAAW,CAAE;QAAA0D,QAAA,gBACpG1P,OAAA,CAAC4tB,WAAW;UAAA3d,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfpQ,OAAA;UAAKwP,KAAK,EAAE;YAAEtB,UAAU,EAAE,MAAM;YAAEyB,YAAY,EAAE,MAAM;YAAE9C,QAAQ,EAAE,MAAM;YAAE+C,SAAS,EAAE;UAAS,CAAE;UAAAF,QAAA,EAAEmD,YAAY,CAACrC;QAAI;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1HpQ,OAAA;UAAKwP,KAAK,EAAE;YACVI,SAAS,EAAE,QAAQ;YACnBpD,OAAO,EAAE,QAAQ;YACjByB,KAAK,EAAE,SAAS;YAChBpB,QAAQ,EAAE,MAAM;YAChBqB,UAAU,EAAE,MAAM;YAClB4B,UAAU,EAAE,uBAAuB;YACnCnD,YAAY,EAAE,KAAK;YACnBgD,YAAY,EAAE;UAChB,CAAE;UAAAD,QAAA,EAAC;QAEH;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNpQ,OAAA;UAAKwP,KAAK,EAAE;YAAE3C,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE,MAAM;YAAE2B,SAAS,EAAE;UAAS,CAAE;UAAAF,QAAA,GAAC,kBAC9D,EAACpC,OAAO;QAAA;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACNpQ,OAAA;UAAKwP,KAAK,EAAE;YAAEqd,SAAS,EAAE,KAAK;YAAEhgB,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE,MAAM;YAAE2B,SAAS,EAAE;UAAS,CAAE;UAAAF,QAAA,GAAC,4BAChF,EAAC,IAAIjF,IAAI,CAAC,CAAC,CAACqiB,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAA7c,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH;;IAEA;IACA,MAAM7L,CAAC,GAAG,GAAG;IACb,MAAME,CAAC,GAAG,GAAG;;IAEb;IACA,IAAI9C,MAAM,CAAC8L,mBAAmB,EAAE;MAC9B9L,MAAM,CAAC8L,mBAAmB,CAACsD,OAAO,GAAGzD,OAAO;IAC9C;;IAEA;IACA,IAAI3L,MAAM,CAAC+L,0BAA0B,IAAI/L,MAAM,CAAC+L,0BAA0B,CAACqD,OAAO,EAAE;MAClFwQ,aAAa,CAAC5f,MAAM,CAAC+L,0BAA0B,CAACqD,OAAO,CAAC;MACxDpP,MAAM,CAAC+L,0BAA0B,CAACqD,OAAO,GAAG,IAAI;IAClD;;IAEA;IACA,IAAIpP,MAAM,CAACgM,uBAAuB,EAAE;MAClChM,MAAM,CAACgM,uBAAuB,CAAC;QAC7BN,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBtB,QAAQ,EAAE;UAAEzH,CAAC;UAAEE;QAAE,CAAC;QAClB8I,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAA8e,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE9e,MAAM,KAAI;MAC/B,CAAC,CAAC;;MAEF;MACA,IAAI7L,MAAM,CAAC+L,0BAA0B,EAAE;QACrC/L,MAAM,CAAC+L,0BAA0B,CAACqD,OAAO,GAAGsQ,WAAW,CAAC,MAAM;UAC5D1f,MAAM,CAAC+R,qBAAqB,CAACpG,OAAO,CAAC;QACvC,CAAC,EAAE,IAAI,CAAC;MACV;MAEA,OAAO,IAAI;IACb,CAAC,MAAM;MACL/K,OAAO,CAACsB,KAAK,CAAC,8BAA8B,CAAC;MAC7C,OAAO,KAAK;IACd;EACF,CAAC,CAAC,OAAOA,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAID;AACA,MAAMwrB,yBAAyB,GAAIxoB,MAAM,IAAK;EAC5C,IAAIkK,OAAO,GAAGlK,MAAM;;EAEpB;EACA,IAAIkK,OAAO,IAAIA,OAAO,CAAC8R,QAAQ,IAAI9R,OAAO,CAAC8R,QAAQ,CAAC5T,IAAI,KAAK,cAAc,EAAE;IAC3E1M,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEuO,OAAO,CAACP,IAAI,IAAI,KAAK,CAAC;IAChD,OAAOO,OAAO;EAChB;;EAEA;EACA,OAAOA,OAAO,IAAIA,OAAO,CAACrI,MAAM,EAAE;IAChCqI,OAAO,GAAGA,OAAO,CAACrI,MAAM;IACxB,IAAIqI,OAAO,CAAC8R,QAAQ,IAAI9R,OAAO,CAAC8R,QAAQ,CAAC5T,IAAI,KAAK,cAAc,EAAE;MAChE1M,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEuO,OAAO,CAACP,IAAI,IAAI,KAAK,CAAC;MAChD,OAAOO,OAAO;IAChB;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACApP,MAAM,CAAC2tB,kBAAkB,GAAG,CAAC/qB,CAAC,EAAEE,CAAC,KAAK;EACpC,IAAI;IACFlC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE+B,CAAC,EAAEE,CAAC,CAAC;;IAEnC;IACA,MAAMmjB,MAAM,GAAGC,QAAQ,CAAC8B,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAI,CAAC/B,MAAM,EAAE;MACXrlB,OAAO,CAACsB,KAAK,CAAC,sBAAsB,CAAC;MACrC,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAAC7C,KAAK,IAAI,CAACgM,SAAS,CAAC+D,OAAO,EAAE;MAChCxO,OAAO,CAACsB,KAAK,CAAC,iBAAiB,CAAC;MAChC,OAAO,KAAK;IACd;;IAEA;IACA,IAAIU,CAAC,KAAKmT,SAAS,IAAIjT,CAAC,KAAKiT,SAAS,EAAE;MACtCnT,CAAC,GAAG5C,MAAM,CAACoa,UAAU,GAAG,CAAC;MACzBtX,CAAC,GAAG9C,MAAM,CAACqa,WAAW,GAAG,CAAC;IAC5B;;IAEA;IACA,MAAMuJ,IAAI,GAAGqC,MAAM,CAACpC,qBAAqB,CAAC,CAAC;IAC3C,MAAMC,MAAM,GAAI,CAAClhB,CAAC,GAAGghB,IAAI,CAACrZ,IAAI,IAAI0b,MAAM,CAACjC,WAAW,GAAI,CAAC,GAAG,CAAC;IAC7D,MAAMC,MAAM,GAAG,EAAE,CAACnhB,CAAC,GAAG8gB,IAAI,CAAC1X,GAAG,IAAI+Z,MAAM,CAAC9B,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;IAE9DvjB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEijB,MAAM,EAAEG,MAAM,CAAC;;IAErC;IACA,MAAMG,SAAS,GAAG,IAAI3mB,KAAK,CAAC4mB,SAAS,CAAC,CAAC;IACvCD,SAAS,CAACE,MAAM,CAACC,MAAM,CAACC,SAAS,GAAG,CAAC;IACrCJ,SAAS,CAACE,MAAM,CAACG,IAAI,CAACD,SAAS,GAAG,CAAC;IAEnC,MAAME,WAAW,GAAG,IAAIjnB,KAAK,CAACknB,OAAO,CAACb,MAAM,EAAEG,MAAM,CAAC;IACrDG,SAAS,CAACQ,aAAa,CAACF,WAAW,EAAErZ,SAAS,CAAC+D,OAAO,CAAC;;IAEvD;IACA,MAAMwe,mBAAmB,GAAG,EAAE;IAC9B3sB,gBAAgB,CAACuE,OAAO,CAAC,CAACob,QAAQ,EAAEjV,OAAO,KAAK;MAC9C,IAAIiV,QAAQ,CAAC7b,KAAK,EAAE;QAClB6oB,mBAAmB,CAAC5W,IAAI,CAAC4J,QAAQ,CAAC7b,KAAK,CAAC;QACxCnE,OAAO,CAACC,GAAG,CAAC,SAAS8K,OAAO,QAAQ,CAAC;MACvC;IACF,CAAC,CAAC;;IAEF;IACA/K,OAAO,CAACC,GAAG,CAAC,QAAQ+sB,mBAAmB,CAAC7nB,MAAM,YAAY,CAAC;IAC3D,MAAM8nB,YAAY,GAAGzJ,SAAS,CAACU,gBAAgB,CAAC8I,mBAAmB,EAAE,IAAI,CAAC;IAE1E,IAAIC,YAAY,CAAC9nB,MAAM,GAAG,CAAC,EAAE;MAC3BnF,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1BgtB,YAAY,CAACroB,OAAO,CAAC,CAACsoB,SAAS,EAAE3c,CAAC,KAAK;QACrCvQ,OAAO,CAACC,GAAG,CAAC,MAAMsQ,CAAC,GAAG,EAAE2c,SAAS,CAAC5oB,MAAM,CAAC2J,IAAI,IAAI,KAAK,EAC1C,KAAK,EAAEif,SAAS,CAACzqB,QAAQ,EACzB,WAAW,EAAEyqB,SAAS,CAAC5oB,MAAM,CAACmF,QAAQ,CAACsH,OAAO,CAAC,CAAC,EAChD,WAAW,EAAEmc,SAAS,CAAC5oB,MAAM,CAACgc,QAAQ,CAAC;;QAEnD;QACA,MAAM/B,GAAG,GAAGuO,yBAAyB,CAACI,SAAS,CAAC5oB,MAAM,CAAC;QACvD,IAAIia,GAAG,IAAIA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAAC5T,IAAI,KAAK,cAAc,EAAE;UAC/D1M,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEse,GAAG,CAAC+B,QAAQ,CAACvV,OAAO,CAAC;QAC/C;MACF,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;;IAEA;IACA/K,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,MAAMktB,eAAe,GAAG3J,SAAS,CAACU,gBAAgB,CAACzlB,KAAK,CAAC0O,QAAQ,EAAE,IAAI,CAAC;IAExEnN,OAAO,CAACC,GAAG,CAAC,WAAWktB,eAAe,CAAChoB,MAAM,MAAM,CAAC;IACpDgoB,eAAe,CAACvoB,OAAO,CAAC,CAACsoB,SAAS,EAAE3c,CAAC,KAAK;MACxC,MAAMgO,GAAG,GAAG2O,SAAS,CAAC5oB,MAAM;MAC5BtE,OAAO,CAACC,GAAG,CAAC,QAAQsQ,CAAC,GAAG,EAAEgO,GAAG,CAACtQ,IAAI,IAAI,KAAK,EAC/B,KAAK,EAAEsQ,GAAG,CAAC7R,IAAI,EACf,KAAK,EAAE6R,GAAG,CAAC9U,QAAQ,CAACsH,OAAO,CAAC,CAAC,EAC7B,KAAK,EAAEmc,SAAS,CAACzqB,QAAQ,EACzB,WAAW,EAAE8b,GAAG,CAAC+B,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACAtgB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,IAAImtB,YAAY,GAAG,CAAC;IAEpB/sB,gBAAgB,CAACuE,OAAO,CAAC,CAACob,QAAQ,EAAEjV,OAAO,KAAK;MAC9C,IAAIiV,QAAQ,CAAC7b,KAAK,EAAE;QAAA,IAAAkpB,qBAAA;QAClB;QACA,IAAIC,SAAS,GAAGtN,QAAQ,CAAC7b,KAAK,CAAC2G,OAAO;QACtC,IAAIyiB,cAAc,GAAG,IAAI;;QAEzB;QACA,MAAMC,QAAQ,GAAG,IAAI3wB,KAAK,CAACiG,OAAO,CAAC,CAAC;QACpCkd,QAAQ,CAAC7b,KAAK,CAACspB,gBAAgB,CAACD,QAAQ,CAAC;;QAEzC;QACA,MAAME,gBAAgB,GAAGF,QAAQ,CAAC9qB,UAAU,CAAC+H,SAAS,CAAC+D,OAAO,CAAC/E,QAAQ,CAAC;;QAExE;QACA,MAAMkkB,SAAS,GAAGH,QAAQ,CAAC1rB,KAAK,CAAC,CAAC,CAAC8rB,OAAO,CAACnjB,SAAS,CAAC+D,OAAO,CAAC;QAC7D,IAAItL,IAAI,CAACK,GAAG,CAACoqB,SAAS,CAAC3rB,CAAC,CAAC,GAAG,CAAC,IAAIkB,IAAI,CAACK,GAAG,CAACoqB,SAAS,CAACzrB,CAAC,CAAC,GAAG,CAAC,IAAIyrB,SAAS,CAACvrB,CAAC,GAAG,CAAC,CAAC,IAAIurB,SAAS,CAACvrB,CAAC,GAAG,CAAC,EAAE;UACjGmrB,cAAc,GAAG,KAAK;QACxB;QAEA,IAAID,SAAS,EAAE;UACbF,YAAY,EAAE;QAChB;QAEAptB,OAAO,CAACC,GAAG,CAAC,OAAO8K,OAAO,GAAG,EAAE;UAC7B8iB,EAAE,EAAE,EAAAR,qBAAA,GAAArN,QAAQ,CAAC1P,YAAY,cAAA+c,qBAAA,uBAArBA,qBAAA,CAAuBpf,IAAI,KAAI,IAAI;UACvC6f,GAAG,EAAER,SAAS;UACdS,KAAK,EAAER,cAAc;UACrBS,IAAI,EAAER,QAAQ,CAACzc,OAAO,CAAC,CAAC;UACxBkd,IAAI,EAAE,CAACN,SAAS,CAAC3rB,CAAC,EAAE2rB,SAAS,CAACzrB,CAAC,EAAEyrB,SAAS,CAACvrB,CAAC,CAAC;UAC7C8rB,MAAM,EAAER;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF1tB,OAAO,CAACC,GAAG,CAAC,MAAMmtB,YAAY,IAAI/sB,gBAAgB,CAACyY,IAAI,WAAW,CAAC;;IAEnE;IACA,OAAOqU,eAAe,CAAChoB,MAAM,GAAG,CAAC;EACnC,CAAC,CAAC,OAAO7D,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,OAAO,KAAK;EACd;AACF,CAAC;;AAID;AACA,MAAMkV,wBAAwB,GAAGA,CAACR,YAAY,EAAEG,SAAS,KAAK;EAAA,IAAAgY,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC5D,IAAI,CAACrY,YAAY,IAAI,CAACA,YAAY,CAAC7R,KAAK,IAAI,CAACgS,SAAS,EAAE;IACtD;EACF;;EAEA;EACA,MAAMmY,cAAc,GAAG,EAAE;EACzBtY,YAAY,CAAC7R,KAAK,CAACE,QAAQ,CAAC2P,KAAK,IAAI;IACnC,IAAIA,KAAK,CAACsM,QAAQ,IAAItM,KAAK,CAACsM,QAAQ,CAACiO,OAAO,EAAE;MAC5CD,cAAc,CAAClY,IAAI,CAACpC,KAAK,CAAC;IAC5B;EACF,CAAC,CAAC;EAEFsa,cAAc,CAAC1pB,OAAO,CAACilB,KAAK,IAAI;IAC9B7T,YAAY,CAAC7R,KAAK,CAACiC,MAAM,CAACyjB,KAAK,CAAC;EAClC,CAAC,CAAC;;EAEF;EACA,IAAIO,UAAU;EACd,QAAOjU,SAAS,CAACH,YAAY;IAC3B,KAAK,GAAG;MACNoU,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;MACNA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;IACR;MACEA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;EACJ;;EAEA;EACA,MAAMf,aAAa,GAAG,IAAIxsB,KAAK,CAACqjB,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAMoJ,aAAa,GAAG,IAAIzsB,KAAK,CAAC4iB,iBAAiB,CAAC;IAChD/T,KAAK,EAAE0e,UAAU;IACjBlW,QAAQ,EAAEkW,UAAU;IACpBoE,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAMjF,SAAS,GAAG,IAAI1sB,KAAK,CAAC6iB,IAAI,CAAC2J,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAAC9f,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAClCknB,SAAS,CAACjJ,QAAQ,GAAG;IACnBiO,OAAO,EAAE,IAAI;IACb7hB,IAAI,EAAE,cAAc;IACpB3B,OAAO,GAAAojB,qBAAA,GAAEnY,YAAY,CAAC1F,YAAY,cAAA6d,qBAAA,uBAAzBA,qBAAA,CAA2BpjB,OAAO;IAC3C0K,OAAO,EAAEU,SAAS,CAACV,OAAO;IAC1BE,SAAS,EAAEQ,SAAS,CAACR,SAAS;IAC9BM,UAAU,EAAEE,SAAS,CAACF;EACxB,CAAC;;EAED;EACA,MAAM4T,KAAK,GAAG,IAAIhtB,KAAK,CAAC4xB,UAAU,CAACrE,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;EACrDP,KAAK,CAACpgB,QAAQ,CAACpH,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAC5BwnB,KAAK,CAACvJ,QAAQ,GAAG;IAAEiO,OAAO,EAAE;EAAK,CAAC;;EAElC;EACAvY,YAAY,CAAC7R,KAAK,CAACC,GAAG,CAACmlB,SAAS,CAAC;EACjCvT,YAAY,CAAC7R,KAAK,CAACC,GAAG,CAACylB,KAAK,CAAC;EAE7B7pB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAAmuB,sBAAA,GAAApY,YAAY,CAAC1F,YAAY,cAAA8d,sBAAA,uBAAzBA,sBAAA,CAA2BngB,IAAI,OAAAogB,sBAAA,GAAIrY,YAAY,CAAC1F,YAAY,cAAA+d,sBAAA,uBAAzBA,sBAAA,CAA2BtjB,OAAO,cAAaoL,SAAS,CAACH,YAAY,WAAWG,SAAS,CAACF,UAAU,GAAG,CAAC;AACjK,CAAC;AAED,eAAehP,WAAW;AAAC,IAAA8d,EAAA;AAAA2J,YAAA,CAAA3J,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}