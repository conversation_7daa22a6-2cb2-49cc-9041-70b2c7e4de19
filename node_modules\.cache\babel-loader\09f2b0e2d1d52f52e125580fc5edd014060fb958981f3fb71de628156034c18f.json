{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet scene = null; // 添加scene全局变量\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm'\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n    display: 'flex',\n    alignItems: 'center',\n    padding: '5px 10px'\n  };\n\n  // 添加标签样式\n  const labelStyle = {\n    marginRight: '10px',\n    whiteSpace: 'nowrap',\n    fontSize: '14px',\n    fontWeight: '500',\n    color: '#333'\n  };\n\n  // 修改Select组件的容器样式\n  const selectContainerStyle = {\n    flex: 1\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    try {\n      const messageData = JSON.parse(message.toString());\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        var _messageData$data;\n        console.log('收到RSM消息:', messageData);\n        const participants = ((_messageData$data = messageData.data) === null || _messageData$data === void 0 ? void 0 : _messageData$data.participants) || [];\n        const rsuid = messageData.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id = rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = preloadedModel.clone();\n              // 根据类型调整高度\n              const height = type === '3' ? 0.1 : 0.8; // 行人模型贴近地面\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 0.1 : 0.8, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        const bsmData = messageData.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n\n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n\n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n\n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);\n        }\n        return;\n      }\n\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n        scene.add(model);\n\n        // 在校园模型加载完成后初始化场景\n        await initializeScene();\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 15, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      controls.update();\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 修改退出登录事件监听\n    const handleLogout = () => {\n      console.log('正在清理CampusModel资源...');\n\n      // 关闭WebSocket连接\n      if (mqttClientRef.current) {\n        try {\n          mqttClientRef.current.close();\n          mqttClientRef.current = null;\n          console.log('WebSocket连接已关闭');\n        } catch (error) {\n          console.error('关闭WebSocket连接时出错:', error);\n        }\n      }\n\n      // 清除更新间隔\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n        console.log('已清除更新间隔');\n      }\n\n      // 清理场景\n      if (scene) {\n        try {\n          // 清理所有车辆模型\n          vehicleModels.forEach((modelData, id) => {\n            if (modelData.model) {\n              scene.remove(modelData.model);\n              if (modelData.model.geometry) modelData.model.geometry.dispose();\n              if (modelData.model.material) modelData.model.material.dispose();\n            }\n          });\n          vehicleModels.clear();\n\n          // 清理场景中的其他对象\n          while (scene.children.length > 0) {\n            const object = scene.children[0];\n            scene.remove(object);\n            if (object.geometry) object.geometry.dispose();\n            if (object.material) object.material.dispose();\n          }\n          console.log('场景已清理');\n        } catch (error) {\n          console.error('清理场景时出错:', error);\n        }\n      }\n\n      // 重置全局变量\n      globalVehicleRef = null;\n      globalTrajectory = [];\n      currentPointIndex = 0;\n      targetPosition = null;\n      currentPosition = null;\n      isMoving = false;\n      console.log('所有资源已清理完成');\n    };\n    window.addEventListener('cleanupBeforeLogout', handleLogout);\n\n    // 清理函数\n    return () => {\n      var _containerRef$current;\n      window.removeEventListener('cleanupBeforeLogout', handleLogout);\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        try {\n          mqttClientRef.current.close();\n        } catch (error) {\n          console.error('清理WebSocket时出错:', error);\n        }\n      }\n      window.removeEventListener('resize', handleResize);\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.removeChild(renderer.domElement);\n      renderer.dispose();\n\n      // 清理场景中的所有车辆模型\n      vehicleModels.forEach((model, id) => {\n        scene.remove(model);\n      });\n      vehicleModels.clear();\n\n      // 清理场景\n      while (((_scene = scene) === null || _scene === void 0 ? void 0 : _scene.children.length) > 0) {\n        var _scene;\n        scene.remove(scene.children[0]);\n      }\n      scene = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: intersectionSelectStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        style: labelStyle,\n        children: \"\\u8DEF\\u53E3\\u9009\\u62E9:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 886,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: selectContainerStyle,\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          placeholder: \"\\u8BF7\\u9009\\u62E9\\u8DEF\\u53E3\",\n          onChange: handleIntersectionChange,\n          options: intersectionsData.intersections.map(intersection => ({\n            value: intersection.name,\n            label: intersection.name\n          })),\n          size: \"middle\",\n          bordered: true,\n          style: {\n            width: '100%'\n          },\n          dropdownStyle: {\n            zIndex: 1002,\n            maxHeight: '300px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 888,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 887,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 885,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 905,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 907,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 917,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 906,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"jdGWqGlpLYHi6cjPE5JHj3ruDak=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    const [vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`)]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse(child => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n        // // 保留原始贴图\n        // if (child.material.map) {\n        //   newMaterial.map = child.material.map;\n        // }\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse(child => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse(child => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n    console.log('所有模型预加载成功');\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "Select", "intersectionsData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "scene", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "topics", "bsm", "rsm", "BASE_URL", "vehicleModels", "Map", "CampusModel", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "intersectionSelectStyle", "top", "width", "alignItems", "labelStyle", "marginRight", "whiteSpace", "fontWeight", "color", "selectContainerStyle", "flex", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "clone", "currentUp", "up", "Tween", "to", "x", "y", "z", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "Math", "PI", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "find", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "set", "updateMatrix", "updateMatrixWorld", "相机位置", "toArray", "目标点", "handleMqttMessage", "topic", "message", "error", "toString", "messageData", "JSON", "parse", "type", "_messageData$data", "participants", "data", "rsuid", "now", "Date", "for<PERSON>ach", "participant", "id", "partPtcId", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "model", "get", "newModel", "height", "rotation", "add", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "Set", "map", "p", "modelData", "has", "remove", "delete", "bsmData", "newState", "partLong", "partLat", "newPosition", "Vector3", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "event", "payload", "stringify", "onerror", "onclose", "setTimeout", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "length", "xhr", "loaded", "total", "toFixed", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "handleLogout", "close", "clearInterval", "geometry", "dispose", "clear", "object", "_containerRef$current", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "_scene", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "onChange", "options", "label", "size", "bordered", "dropdownStyle", "maxHeight", "ref", "onClick", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "vehicleGltf", "cyclistGltf", "peopleGltf", "all", "loadAsync", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet scene = null; // 添加scene全局变量\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm'\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n    display: 'flex',\n    alignItems: 'center',\n    padding: '5px 10px',\n  };\n\n  // 添加标签样式\n  const labelStyle = {\n    marginRight: '10px',\n    whiteSpace: 'nowrap',\n    fontSize: '14px',\n    fontWeight: '500',\n    color: '#333',\n  };\n\n  // 修改Select组件的容器样式\n  const selectContainerStyle = {\n    flex: 1,\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    \n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    \n    try {\n      const messageData = JSON.parse(message.toString());\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        console.log('收到RSM消息:', messageData);\n        \n        const participants = messageData.data?.participants || [];\n        const rsuid = messageData.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id =  rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n          // 解析位置和状态信息\n          const state = {\n            longitude: parseFloat(participant.partPosLong),\n            latitude: parseFloat(participant.partPosLat),\n            speed: parseFloat(participant.partSpeed),\n            heading: parseFloat(participant.partHeading)\n          };\n          \n          const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n          \n          // 根据类型选择对应的预加载模型\n          let preloadedModel;\n          switch (type) {\n            case '1': // 机动车\n              preloadedModel = preloadedVehicleModel;\n              break;\n            case '2': // 非机动车\n              preloadedModel = preloadedCyclistModel;\n              break;\n            case '3': // 行人\n              preloadedModel = preloadedPeopleModel;\n              break;\n            default:\n              return; // 跳过未知类型\n          }\n          \n          // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n            // 创建新模型实例\n            const newModel = preloadedModel.clone();\n            // 根据类型调整高度\n            const height = type === '3' ? 0.1 : 0.8; // 行人模型贴近地面\n            newModel.position.set(modelPos.x, height, -modelPos.y);\n            newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            scene.add(newModel);\n            \n            vehicleModels.set(id, {\n              model: newModel,\n              lastUpdate: now,\n              type: type\n            });\n          } else if (model) {\n            // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 0.1 : 0.8, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n          }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        \n        const bsmData = messageData.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        \n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n          \n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n          \n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);\n        }\n        return;\n      }\n      \n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          15,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      \n      controls.update();\n      renderer.render(scene, camera);\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改退出登录事件监听\n    const handleLogout = () => {\n      console.log('正在清理CampusModel资源...');\n      \n      // 关闭WebSocket连接\n      if (mqttClientRef.current) {\n        try {\n          mqttClientRef.current.close();\n          mqttClientRef.current = null;\n          console.log('WebSocket连接已关闭');\n        } catch (error) {\n          console.error('关闭WebSocket连接时出错:', error);\n        }\n      }\n\n      // 清除更新间隔\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n        console.log('已清除更新间隔');\n      }\n\n      // 清理场景\n      if (scene) {\n        try {\n          // 清理所有车辆模型\n          vehicleModels.forEach((modelData, id) => {\n            if (modelData.model) {\n              scene.remove(modelData.model);\n              if (modelData.model.geometry) modelData.model.geometry.dispose();\n              if (modelData.model.material) modelData.model.material.dispose();\n            }\n          });\n          vehicleModels.clear();\n\n          // 清理场景中的其他对象\n          while(scene.children.length > 0) { \n            const object = scene.children[0];\n            scene.remove(object);\n            if (object.geometry) object.geometry.dispose();\n            if (object.material) object.material.dispose();\n          }\n          console.log('场景已清理');\n        } catch (error) {\n          console.error('清理场景时出错:', error);\n        }\n      }\n\n      // 重置全局变量\n      globalVehicleRef = null;\n      globalTrajectory = [];\n      currentPointIndex = 0;\n      targetPosition = null;\n      currentPosition = null;\n      isMoving = false;\n      \n      console.log('所有资源已清理完成');\n    };\n\n    window.addEventListener('cleanupBeforeLogout', handleLogout);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('cleanupBeforeLogout', handleLogout);\n      \n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      \n      if (mqttClientRef.current) {\n        try {\n          mqttClientRef.current.close();\n        } catch (error) {\n          console.error('清理WebSocket时出错:', error);\n        }\n      }\n      \n      window.removeEventListener('resize', handleResize);\n      containerRef.current?.removeChild(renderer.domElement);\n      renderer.dispose();\n      \n      // 清理场景中的所有车辆模型\n      vehicleModels.forEach((model, id) => {\n        scene.remove(model);\n      });\n      vehicleModels.clear();\n      \n      // 清理场景\n      while(scene?.children.length > 0) { \n        scene.remove(scene.children[0]); \n      }\n      \n      scene = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n    };\n  }, []);\n\n  return (\n    <>\n      <div style={intersectionSelectStyle}>\n        <span style={labelStyle}>路口选择:</span>\n        <div style={selectContainerStyle}>\n          <Select\n            placeholder=\"请选择路口\"\n            onChange={handleIntersectionChange}\n            options={intersectionsData.intersections.map(intersection => ({\n              value: intersection.name,\n              label: intersection.name\n            }))}\n            size=\"middle\"\n            bordered={true}\n            style={{ width: '100%' }}\n            dropdownStyle={{ \n              zIndex: 1002,\n              maxHeight: '300px'\n            }}\n          />\n        </div>\n      </div>\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    const [vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([\n      loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/people.glb`)\n    ]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n        // // 保留原始贴图\n        // if (child.material.map) {\n        //   newMaterial.map = child.material.map;\n        // }\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    console.log('所有模型预加载成功');\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,MAAM;AAC7B,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE;IACNC,GAAG,EAAE,2BAA2B;IAChCC,GAAG,EAAE;EACP;AACF,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAGzC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM0C,UAAU,GAAG1C,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM2C,SAAS,GAAG3C,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMuC,aAAa,GAAG5C,MAAM,CAAC,EAAE,CAAC;EAChC,MAAM6C,eAAe,GAAG7C,MAAM,CAAC,CAAC,CAAC;EACjC,MAAM8C,aAAa,GAAG9C,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAAC+C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC;IAC/CkD,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMwD,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG1E,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAAC2E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM4E,uBAAuB,GAAG;IAC9BnB,QAAQ,EAAE,OAAO;IACjBoB,GAAG,EAAE,MAAM;IACXlB,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BkB,KAAK,EAAE,OAAO;IACdjB,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE,2BAA2B;IACtCT,OAAO,EAAE,MAAM;IACfiB,UAAU,EAAE,QAAQ;IACpBd,OAAO,EAAE;EACX,CAAC;;EAED;EACA,MAAMe,UAAU,GAAG;IACjBC,WAAW,EAAE,MAAM;IACnBC,UAAU,EAAE,QAAQ;IACpBZ,QAAQ,EAAE,MAAM;IAChBa,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE;EACT,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAG;IAC3BC,IAAI,EAAE;EACR,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BhC,WAAW,CAAC,QAAQ,CAAC;IACrBnC,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACmE,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BlC,WAAW,CAAC,QAAQ,CAAC;IACrBnC,UAAU,GAAG,QAAQ;IAErB,IAAIqD,SAAS,CAACiB,OAAO,IAAIrE,QAAQ,EAAE;MACjC;MACA,MAAMsE,UAAU,GAAGlB,SAAS,CAACiB,OAAO,CAACjC,QAAQ,CAACmC,KAAK,CAAC,CAAC;MACrD,MAAMC,SAAS,GAAGpB,SAAS,CAACiB,OAAO,CAACI,EAAE,CAACF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAIvF,KAAK,CAAC0F,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCC,MAAM,CAAC/F,KAAK,CAACgG,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACd/B,SAAS,CAACiB,OAAO,CAACjC,QAAQ,CAACgD,IAAI,CAACd,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDe,KAAK,CAAC,CAAC;;MAEV;MACA,IAAIrG,KAAK,CAAC0F,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC/F,KAAK,CAACgG,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACd/B,SAAS,CAACiB,OAAO,CAACI,EAAE,CAACW,IAAI,CAACZ,SAAS,CAAC;MACtC,CAAC,CAAC,CACDa,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAGtF,QAAQ,CAACuF,MAAM,CAAChB,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAIvF,KAAK,CAAC0F,KAAK,CAACY,aAAa,CAAC,CAC3BX,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC/F,KAAK,CAACgG,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdnF,QAAQ,CAACuF,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACAlC,SAAS,CAACiB,OAAO,CAACmB,MAAM,CAACxF,QAAQ,CAACuF,MAAM,CAAC;QACzCvF,QAAQ,CAACyF,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACArF,QAAQ,CAACmE,OAAO,GAAG,IAAI;;MAEvB;MACAnE,QAAQ,CAAC0F,WAAW,GAAG,EAAE;MACzB1F,QAAQ,CAAC2F,WAAW,GAAG,GAAG;MAC1B3F,QAAQ,CAAC4F,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;MACtC9F,QAAQ,CAAC+F,aAAa,GAAG,CAAC;MAC1B/F,QAAQ,CAACyF,MAAM,CAAC,CAAC;MAEjBO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAGpH,iBAAiB,CAACqH,aAAa,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKL,KAAK,CAAC;IAChF,IAAIC,YAAY,IAAInD,SAAS,CAACiB,OAAO,IAAIrE,QAAQ,EAAE;MACjDsD,uBAAuB,CAACiD,YAAY,CAAC;;MAErC;MACA,MAAMK,WAAW,GAAGvF,SAAS,CAACgD,OAAO,CAACwC,YAAY,CAChDC,UAAU,CAACP,YAAY,CAAC1E,SAAS,CAAC,EAClCiF,UAAU,CAACP,YAAY,CAACzE,QAAQ,CAClC,CAAC;MAEDkE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvBc,IAAI,EAAER,YAAY,CAACI,IAAI;QACvBK,GAAG,EAAE;UACHnF,SAAS,EAAE0E,YAAY,CAAC1E,SAAS;UACjCC,QAAQ,EAAEyE,YAAY,CAACzE;QACzB,CAAC;QACDmF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACA7G,UAAU,GAAG,cAAc;MAC3BmC,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAkB,SAAS,CAACiB,OAAO,CAACjC,QAAQ,CAAC8E,GAAG,CAACN,WAAW,CAAChC,CAAC,EAAE,GAAG,EAAE,CAACgC,WAAW,CAAC/B,CAAC,CAAC;;MAElE;MACA7E,QAAQ,CAACuF,MAAM,CAAC2B,GAAG,CAACN,WAAW,CAAChC,CAAC,EAAE,CAAC,EAAE,CAACgC,WAAW,CAAC/B,CAAC,CAAC;;MAErD;MACAzB,SAAS,CAACiB,OAAO,CAACmB,MAAM,CAACxF,QAAQ,CAACuF,MAAM,CAAC;;MAEzC;MACAvF,QAAQ,CAACmE,OAAO,GAAG,IAAI;MACvBnE,QAAQ,CAACyF,MAAM,CAAC,CAAC;;MAEjB;MACArC,SAAS,CAACiB,OAAO,CAAC8C,YAAY,CAAC,CAAC;MAChC/D,SAAS,CAACiB,OAAO,CAAC+C,iBAAiB,CAAC,IAAI,CAAC;MAEzCpB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBc,IAAI,EAAER,YAAY,CAACI,IAAI;QACvBU,IAAI,EAAEjE,SAAS,CAACiB,OAAO,CAACjC,QAAQ,CAACkF,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAEvH,QAAQ,CAACuF,MAAM,CAAC+B,OAAO,CAAC,CAAC;QAC9BL,IAAI,EAAEL;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMY,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC5C,IAAI,CAACtH,KAAK,EAAE;MACV4F,OAAO,CAAC2B,KAAK,CAAC,QAAQ,CAAC;MACvB;IACF;IAEA3B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBwB,KAAK;MACLC,OAAO,EAAEA,OAAO,CAACE,QAAQ,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACL,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;;MAElD;MACA,IAAIH,KAAK,KAAKpH,WAAW,CAACM,MAAM,CAACE,GAAG,IAAIgH,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAAA,IAAAC,iBAAA;QAClEjC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE4B,WAAW,CAAC;QAEpC,MAAMK,YAAY,GAAG,EAAAD,iBAAA,GAAAJ,WAAW,CAACM,IAAI,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBC,YAAY,KAAI,EAAE;QACzD,MAAME,KAAK,GAAGP,WAAW,CAACM,IAAI,CAACC,KAAK;;QAEpC;QACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;QAEtB;QACAH,YAAY,CAACK,OAAO,CAACC,WAAW,IAAI;UAClC;UACA,MAAMC,EAAE,GAAIL,KAAK,GAAGI,WAAW,CAACE,SAAS;UACzC,MAAMV,IAAI,GAAGQ,WAAW,CAACG,WAAW;UAEpC,IAAGX,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACA;YACA,MAAMY,KAAK,GAAG;cACZ/G,SAAS,EAAEiF,UAAU,CAAC0B,WAAW,CAACK,WAAW,CAAC;cAC9C/G,QAAQ,EAAEgF,UAAU,CAAC0B,WAAW,CAACM,UAAU,CAAC;cAC5C/G,KAAK,EAAE+E,UAAU,CAAC0B,WAAW,CAACO,SAAS,CAAC;cACxC/G,OAAO,EAAE8E,UAAU,CAAC0B,WAAW,CAACQ,WAAW;YAC7C,CAAC;YAED,MAAMC,QAAQ,GAAG5H,SAAS,CAACgD,OAAO,CAACwC,YAAY,CAAC+B,KAAK,CAAC/G,SAAS,EAAE+G,KAAK,CAAC9G,QAAQ,CAAC;;YAEhF;YACA,IAAIoH,cAAc;YAClB,QAAQlB,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRkB,cAAc,GAAGjJ,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRiJ,cAAc,GAAGhJ,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRgJ,cAAc,GAAG/I,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACA,IAAIgJ,KAAK,GAAGpI,aAAa,CAACqI,GAAG,CAACX,EAAE,CAAC;YAEjC,IAAI,CAACU,KAAK,IAAID,cAAc,EAAE;cAC5B;cACA,MAAMG,QAAQ,GAAGH,cAAc,CAAC3E,KAAK,CAAC,CAAC;cACvC;cACA,MAAM+E,MAAM,GAAGtB,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;cACzCqB,QAAQ,CAACjH,QAAQ,CAAC8E,GAAG,CAAC+B,QAAQ,CAACrE,CAAC,EAAE0E,MAAM,EAAE,CAACL,QAAQ,CAACpE,CAAC,CAAC;cACtDwE,QAAQ,CAACE,QAAQ,CAAC1E,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAG8C,KAAK,CAAC5G,OAAO,GAAG6D,IAAI,CAACC,EAAE,GAAG,GAAG;cAC7D1F,KAAK,CAACoJ,GAAG,CAACH,QAAQ,CAAC;cAEnBtI,aAAa,CAACmG,GAAG,CAACuB,EAAE,EAAE;gBACpBU,KAAK,EAAEE,QAAQ;gBACfI,UAAU,EAAEpB,GAAG;gBACfL,IAAI,EAAEA;cACR,CAAC,CAAC;YACJ,CAAC,MAAM,IAAImB,KAAK,EAAE;cAChB;cACAA,KAAK,CAACA,KAAK,CAAC/G,QAAQ,CAAC8E,GAAG,CAAC+B,QAAQ,CAACrE,CAAC,EAAEuE,KAAK,CAACnB,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAACiB,QAAQ,CAACpE,CAAC,CAAC;cACjFsE,KAAK,CAACA,KAAK,CAACI,QAAQ,CAAC1E,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAG8C,KAAK,CAAC5G,OAAO,GAAG6D,IAAI,CAACC,EAAE,GAAG,GAAG;cAChEqD,KAAK,CAACM,UAAU,GAAGpB,GAAG;cACtBc,KAAK,CAACA,KAAK,CAAChC,YAAY,CAAC,CAAC;cAC1BgC,KAAK,CAACA,KAAK,CAAC/B,iBAAiB,CAAC,IAAI,CAAC;YACrC;UACA;QACF,CAAC,CAAC;;QAEF;QACA,MAAMsC,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAC1B,YAAY,CAAC2B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACpB,SAAS,CAAC,CAAC;QAE9D3H,aAAa,CAACwH,OAAO,CAAC,CAACwB,SAAS,EAAEtB,EAAE,KAAK;UACvC,IAAIJ,GAAG,GAAG0B,SAAS,CAACN,UAAU,GAAGC,iBAAiB,IAAI,CAACC,UAAU,CAACK,GAAG,CAACvB,EAAE,CAAC,EAAE;YACzErI,KAAK,CAAC6J,MAAM,CAACF,SAAS,CAACZ,KAAK,CAAC;YAC7BpI,aAAa,CAACmJ,MAAM,CAACzB,EAAE,CAAC;YACxBzC,OAAO,CAACC,GAAG,CAAC,oBAAoBwC,EAAE,QAAQsB,SAAS,CAAC/B,IAAI,EAAE,CAAC;UAC7D;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAIP,KAAK,KAAKpH,WAAW,CAACM,MAAM,CAACC,GAAG,IAAIiH,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAClEhC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE4B,WAAW,CAAC;QAEpC,MAAMsC,OAAO,GAAGtC,WAAW,CAACM,IAAI;QAChC,MAAMiC,QAAQ,GAAG;UACfvI,SAAS,EAAEiF,UAAU,CAACqD,OAAO,CAACE,QAAQ,CAAC;UACvCvI,QAAQ,EAAEgF,UAAU,CAACqD,OAAO,CAACG,OAAO,CAAC;UACrCvI,KAAK,EAAE+E,UAAU,CAACqD,OAAO,CAACpB,SAAS,CAAC;UACpC/G,OAAO,EAAE8E,UAAU,CAACqD,OAAO,CAACnB,WAAW;QACzC,CAAC;QAEDhD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEmE,QAAQ,CAAC;;QAElC;QACA,IAAI5K,gBAAgB,EAAE;UACpB,MAAMyJ,QAAQ,GAAG5H,SAAS,CAACgD,OAAO,CAACwC,YAAY,CAACuD,QAAQ,CAACvI,SAAS,EAAEuI,QAAQ,CAACtI,QAAQ,CAAC;UACtF,MAAMyI,WAAW,GAAG,IAAI3L,KAAK,CAAC4L,OAAO,CAACvB,QAAQ,CAACrE,CAAC,EAAE,GAAG,EAAE,CAACqE,QAAQ,CAACpE,CAAC,CAAC;;UAEnE;UACArF,gBAAgB,CAAC4C,QAAQ,CAACgD,IAAI,CAACmF,WAAW,CAAC;UAC3C/K,gBAAgB,CAAC+J,QAAQ,CAAC1E,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGsE,QAAQ,CAACpI,OAAO,GAAG6D,IAAI,CAACC,EAAE,GAAG,GAAG;UACxEtG,gBAAgB,CAAC2H,YAAY,CAAC,CAAC;UAC/B3H,gBAAgB,CAAC4H,iBAAiB,CAAC,IAAI,CAAC;;UAExC;UACAxF,eAAe,CAACwI,QAAQ,CAAC;UACzBpE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsE,WAAW,CAAC;QACtC;QACA;MACF;;MAEA;MACAvE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBwB,KAAK;QACLO,IAAI,EAAEH,WAAW,CAACG,IAAI;QACtBG,IAAI,EAAEN;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd3B,OAAO,CAAC2B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC3B,OAAO,CAAC2B,KAAK,CAAC,SAAS,EAAED,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAM6C,cAAc,GAAGA,CAAA,KAAM;IAC3BzE,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAMyE,KAAK,GAAG,QAAQrK,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnEsF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEyE,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChB7E,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED0E,EAAE,CAACG,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMrD,OAAO,GAAGI,IAAI,CAACC,KAAK,CAACgD,KAAK,CAAC5C,IAAI,CAAC;;QAEtC;QACA,IAAIT,OAAO,CAACM,IAAI,KAAK,SAAS,EAAE;UAC9BhC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEyB,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACM,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIN,OAAO,CAACM,IAAI,KAAK,SAAS,IAAIN,OAAO,CAACD,KAAK,IAAIC,OAAO,CAACsD,OAAO,EAAE;UAClE;UACAxD,iBAAiB,CAACE,OAAO,CAACD,KAAK,EAAEK,IAAI,CAACmD,SAAS,CAACvD,OAAO,CAACsD,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOrD,KAAK,EAAE;QACd3B,OAAO,CAAC2B,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDgD,EAAE,CAACO,OAAO,GAAIvD,KAAK,IAAK;MACtB3B,OAAO,CAAC2B,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDgD,EAAE,CAACQ,OAAO,GAAG,MAAM;MACjBnF,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACAmF,UAAU,CAACX,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACAjJ,aAAa,CAAC6C,OAAO,GAAGsG,EAAE;EAC5B,CAAC;EAEDlM,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0C,YAAY,CAACkD,OAAO,EAAE;;IAE3B;IACAgH,aAAa,CAAC,CAAC;;IAEf;IACAjL,KAAK,GAAG,IAAIxB,KAAK,CAAC0M,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAI3M,KAAK,CAAC4M,iBAAiB,CACxC,EAAE,EACFjL,MAAM,CAACkL,UAAU,GAAGlL,MAAM,CAACmL,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAACnJ,QAAQ,CAAC8E,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCqE,MAAM,CAAC/F,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBpC,SAAS,CAACiB,OAAO,GAAGkH,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAI/M,KAAK,CAACgN,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAACvL,MAAM,CAACkL,UAAU,EAAElL,MAAM,CAACmL,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAACzL,MAAM,CAAC0L,gBAAgB,CAAC;IAC/C9K,YAAY,CAACkD,OAAO,CAAC6H,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAIxN,KAAK,CAACyN,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5DjM,KAAK,CAACoJ,GAAG,CAAC4C,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAI1N,KAAK,CAAC2N,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAAClK,QAAQ,CAAC8E,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1C9G,KAAK,CAACoJ,GAAG,CAAC8C,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAI5N,KAAK,CAAC2N,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAACpK,QAAQ,CAAC8E,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3C9G,KAAK,CAACoJ,GAAG,CAACgD,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAI7N,KAAK,CAAC8N,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAACrK,QAAQ,CAAC8E,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCuF,SAAS,CAACE,KAAK,GAAG9G,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7B2G,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxB1M,KAAK,CAACoJ,GAAG,CAACiD,SAAS,CAAC;;IAEpB;IACAzM,QAAQ,GAAG,IAAIlB,aAAa,CAACyM,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzDnM,QAAQ,CAAC+M,aAAa,GAAG,IAAI;IAC7B/M,QAAQ,CAACgN,aAAa,GAAG,IAAI;IAC7BhN,QAAQ,CAACiN,kBAAkB,GAAG,KAAK;IACnCjN,QAAQ,CAAC0F,WAAW,GAAG,EAAE;IACzB1F,QAAQ,CAAC2F,WAAW,GAAG,GAAG;IAC1B3F,QAAQ,CAAC4F,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC9F,QAAQ,CAAC+F,aAAa,GAAG,CAAC;IAC1B/F,QAAQ,CAACuF,MAAM,CAAC2B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BlH,QAAQ,CAACyF,MAAM,CAAC,CAAC;;IAEjB;IACAO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBsF,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBvL,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBoD,SAAS,EAAE,CAAC,CAACA,SAAS,CAACiB;IACzB,CAAC,CAAC;;IAEF;IACA,MAAM6I,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAIzO,UAAU,CAAC,CAAC;QACtCyO,aAAa,CAACC,IAAI,CAChB,GAAGzM,QAAQ,uBAAuB,EACjC0M,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAACpN,KAAK;;UAE/B;UACA,MAAMsN,gBAAgB,GAAG,IAAI9O,KAAK,CAAC+O,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACG,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAIpP,KAAK,CAACqP,oBAAoB,CAAC;kBACjDlK,KAAK,EAAE,QAAQ;kBAAO;kBACtBmK,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIP,KAAK,CAACE,QAAQ,CAAClE,GAAG,EAAE;kBACtBmE,WAAW,CAACnE,GAAG,GAAGgE,KAAK,CAACE,QAAQ,CAAClE,GAAG;gBACtC;;gBAEA;gBACAgE,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5BhI,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE4H,KAAK,CAAClH,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAM8G,YAAY,CAACY,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMT,KAAK,GAAGJ,YAAY,CAACY,QAAQ,CAAC,CAAC,CAAC;YACtCX,gBAAgB,CAAClE,GAAG,CAACqE,KAAK,CAAC;UAC7B;;UAEA;UACAzN,KAAK,CAACoJ,GAAG,CAACkE,gBAAgB,CAAC;;UAE3B;UACAlO,gBAAgB,GAAGkO,gBAAgB;UAEnC1H,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9BvE,kBAAkB,CAAC,IAAI,CAAC;UACxB0L,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAa,GAAG,IAAK;UACPvI,OAAO,CAACC,GAAG,CAAC,aAAa,CAACsI,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDrB,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMsB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMjB,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACAzC,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAIiD,gBAAgB,EAAE;UACpB,MAAMkB,YAAY,GAAG;YACnB/M,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UAED,MAAM6M,UAAU,GAAGxN,SAAS,CAACgD,OAAO,CAACwC,YAAY,CAAC+H,YAAY,CAAC/M,SAAS,EAAE+M,YAAY,CAAC9M,QAAQ,CAAC;UAChG;UACA4L,gBAAgB,CAACtL,QAAQ,CAAC8E,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACxCwG,gBAAgB,CAACnE,QAAQ,CAAC1E,CAAC,GAAIgB,IAAI,CAACC,EAAE,GAAG8I,YAAY,CAAC5M,OAAO,GAAG6D,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9E4H,gBAAgB,CAACvG,YAAY,CAAC,CAAC;UAC/BuG,gBAAgB,CAACtG,iBAAiB,CAAC,IAAI,CAAC;UACxCvH,eAAe,GAAG6N,gBAAgB,CAACtL,QAAQ,CAACmC,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAOoD,KAAK,EAAE;QACd3B,OAAO,CAAC2B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAMmH,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAI7B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAM4B,WAAW,GAAIC,WAAW,IAAK;UACnClJ,OAAO,CAACC,GAAG,CAAC,WAAW8I,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAItQ,UAAU,CAAC,CAAC;UAC/BsQ,MAAM,CAAC5B,IAAI,CACTwB,GAAG,EACFvB,IAAI,IAAK;YACRxH,OAAO,CAACC,GAAG,CAAC,WAAW8I,GAAG,EAAE,CAAC;YAC7B3B,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAe,GAAG,IAAK;YACPvI,OAAO,CAACC,GAAG,CAAC,SAAS,CAACsI,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACA/G,KAAK,IAAK;YACT3B,OAAO,CAAC2B,KAAK,CAAC,SAASoH,GAAG,EAAE,EAAEpH,KAAK,CAAC;YACpC,IAAIuH,WAAW,GAAG,CAAC,EAAE;cACnBlJ,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3BmF,UAAU,CAAC,MAAM6D,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACL7B,MAAM,CAAC1F,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAEDsH,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAItQ,UAAU,CAAC,CAAC;IAC/BsQ,MAAM,CAAC5B,IAAI,CACT,GAAGzM,QAAQ,4BAA4B,EACvC,MAAO0M,IAAI,IAAK;MACd,IAAI;QACF,MAAMrE,KAAK,GAAGqE,IAAI,CAACpN,KAAK;QACxB+I,KAAK,CAACiG,KAAK,CAAClI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBiC,KAAK,CAAC/G,QAAQ,CAAC8E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3B9G,KAAK,CAACoJ,GAAG,CAACL,KAAK,CAAC;;QAEhB;QACA,MAAMwF,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC,OAAOhH,KAAK,EAAE;QACd3B,OAAO,CAAC2B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACA4G,GAAG,IAAK;MACPvI,OAAO,CAACC,GAAG,CAAC,SAAS,CAACsI,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACA/G,KAAK,IAAK;MACT3B,OAAO,CAAC2B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B3B,OAAO,CAAC2B,KAAK,CAAC,OAAO,EAAE;QACrB0H,IAAI,EAAE1H,KAAK,CAACK,IAAI;QAChBsH,IAAI,EAAE3H,KAAK,CAACD,OAAO;QACnB6H,KAAK,EAAE,GAAGzO,QAAQ,4BAA4B;QAC9C0O,KAAK,EAAE,GAAG1O,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAM2O,OAAO,GAAGA,CAAA,KAAM;MACpBC,qBAAqB,CAACD,OAAO,CAAC;;MAE9B;MACAzQ,KAAK,CAACyG,MAAM,CAAC,CAAC;MAEd,IAAI1F,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACmE,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMwL,UAAU,GAAGnQ,gBAAgB,CAAC4C,QAAQ,CAACmC,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMqL,eAAe,GAAGpQ,gBAAgB,CAAC+J,QAAQ,CAAC1E,CAAC;;QAEnD;QACA;QACA,MAAMgL,gBAAgB,GAAG,EAAED,eAAe,GAAG/J,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAMgK,YAAY,GAAG,IAAIlR,KAAK,CAAC4L,OAAO,CACpC,CAAC,EAAE,GAAG3E,IAAI,CAACkK,GAAG,CAACF,gBAAgB,CAAC,EAChC,EAAE,EACF,CAAC,EAAE,GAAGhK,IAAI,CAACmK,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACAtE,MAAM,CAACnJ,QAAQ,CAACgD,IAAI,CAACuK,UAAU,CAAC,CAACnG,GAAG,CAACsG,YAAY,CAAC;;QAElD;QACAvE,MAAM,CAAC9G,EAAE,CAACyC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAM+I,YAAY,GAAGN,UAAU,CAACpL,KAAK,CAAC,CAAC;QACvCgH,MAAM,CAAC/F,MAAM,CAACyK,YAAY,CAAC;;QAE3B;QACA1E,MAAM,CAAC2E,sBAAsB,CAAC,CAAC;QAC/B3E,MAAM,CAACpE,YAAY,CAAC,CAAC;QACrBoE,MAAM,CAACnE,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACApH,QAAQ,CAACmE,OAAO,GAAG,KAAK;;QAExB;QACAnE,QAAQ,CAACuF,MAAM,CAACH,IAAI,CAACuK,UAAU,CAAC;QAChC3P,QAAQ,CAACyF,MAAM,CAAC,CAAC;QAEjBO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnBkK,IAAI,EAAER,UAAU,CAACrI,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAEkE,MAAM,CAACnJ,QAAQ,CAACkF,OAAO,CAAC,CAAC;UAC/B8I,IAAI,EAAEH,YAAY,CAAC3I,OAAO,CAAC,CAAC;UAC5B+I,IAAI,EAAE9E,MAAM,CAAC+E,iBAAiB,CAAC,IAAI1R,KAAK,CAAC4L,OAAO,CAAC,CAAC,CAAC,CAAClD,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIvH,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAACmE,OAAO,GAAG,IAAI;;QAEvB;QACAoH,MAAM,CAAC9G,EAAE,CAACyC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAIrB,IAAI,CAAC0K,GAAG,CAAChF,MAAM,CAACnJ,QAAQ,CAACyC,CAAC,CAAC,GAAG,EAAE,EAAE;UACpC0G,MAAM,CAACnJ,QAAQ,CAAC8E,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BlH,QAAQ,CAACuF,MAAM,CAAC2B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BqE,MAAM,CAAC/F,MAAM,CAACxF,QAAQ,CAACuF,MAAM,CAAC;UAC9BvF,QAAQ,CAACyF,MAAM,CAAC,CAAC;QACnB;MACF,CAAC,MAAM,IAAI1F,UAAU,KAAK,cAAc,EAAE;QACxC;QACAC,QAAQ,CAACyF,MAAM,CAAC,CAAC;MACnB;MAEAzF,QAAQ,CAACyF,MAAM,CAAC,CAAC;MACjBkG,QAAQ,CAAC6E,MAAM,CAACpQ,KAAK,EAAEmL,MAAM,CAAC;IAChC,CAAC;IAEDkE,OAAO,CAAC,CAAC;;IAET;IACA,MAAMgB,YAAY,GAAGA,CAAA,KAAM;MACzBlF,MAAM,CAACmF,MAAM,GAAGnQ,MAAM,CAACkL,UAAU,GAAGlL,MAAM,CAACmL,WAAW;MACtDH,MAAM,CAAC2E,sBAAsB,CAAC,CAAC;MAC/BvE,QAAQ,CAACG,OAAO,CAACvL,MAAM,CAACkL,UAAU,EAAElL,MAAM,CAACmL,WAAW,CAAC;IACzD,CAAC;IACDnL,MAAM,CAACoQ,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACAlQ,MAAM,CAACqQ,aAAa,GAAG,MAAM;MAC3B,IAAIxN,SAAS,CAACiB,OAAO,EAAE;QACrBjB,SAAS,CAACiB,OAAO,CAACjC,QAAQ,CAAC8E,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC9D,SAAS,CAACiB,OAAO,CAACmB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCpC,SAAS,CAACiB,OAAO,CAAC8C,YAAY,CAAC,CAAC;QAChC/D,SAAS,CAACiB,OAAO,CAAC+C,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAIpH,QAAQ,EAAE;UACZA,QAAQ,CAACuF,MAAM,CAAC2B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BlH,QAAQ,CAACmE,OAAO,GAAG,IAAI;UACvBnE,QAAQ,CAACyF,MAAM,CAAC,CAAC;QACnB;QAEA1F,UAAU,GAAG,QAAQ;QACrBiG,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,MAAM4K,YAAY,GAAGA,CAAA,KAAM;MACzB7K,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;;MAEnC;MACA,IAAIzE,aAAa,CAAC6C,OAAO,EAAE;QACzB,IAAI;UACF7C,aAAa,CAAC6C,OAAO,CAACyM,KAAK,CAAC,CAAC;UAC7BtP,aAAa,CAAC6C,OAAO,GAAG,IAAI;UAC5B2B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;QAC/B,CAAC,CAAC,OAAO0B,KAAK,EAAE;UACd3B,OAAO,CAAC2B,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;QAC3C;MACF;;MAEA;MACA,IAAIhI,oBAAoB,EAAE;QACxBoR,aAAa,CAACpR,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;QAC3BqG,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACxB;;MAEA;MACA,IAAI7F,KAAK,EAAE;QACT,IAAI;UACF;UACAW,aAAa,CAACwH,OAAO,CAAC,CAACwB,SAAS,EAAEtB,EAAE,KAAK;YACvC,IAAIsB,SAAS,CAACZ,KAAK,EAAE;cACnB/I,KAAK,CAAC6J,MAAM,CAACF,SAAS,CAACZ,KAAK,CAAC;cAC7B,IAAIY,SAAS,CAACZ,KAAK,CAAC6H,QAAQ,EAAEjH,SAAS,CAACZ,KAAK,CAAC6H,QAAQ,CAACC,OAAO,CAAC,CAAC;cAChE,IAAIlH,SAAS,CAACZ,KAAK,CAAC4E,QAAQ,EAAEhE,SAAS,CAACZ,KAAK,CAAC4E,QAAQ,CAACkD,OAAO,CAAC,CAAC;YAClE;UACF,CAAC,CAAC;UACFlQ,aAAa,CAACmQ,KAAK,CAAC,CAAC;;UAErB;UACA,OAAM9Q,KAAK,CAACiO,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YAC/B,MAAM6C,MAAM,GAAG/Q,KAAK,CAACiO,QAAQ,CAAC,CAAC,CAAC;YAChCjO,KAAK,CAAC6J,MAAM,CAACkH,MAAM,CAAC;YACpB,IAAIA,MAAM,CAACH,QAAQ,EAAEG,MAAM,CAACH,QAAQ,CAACC,OAAO,CAAC,CAAC;YAC9C,IAAIE,MAAM,CAACpD,QAAQ,EAAEoD,MAAM,CAACpD,QAAQ,CAACkD,OAAO,CAAC,CAAC;UAChD;UACAjL,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;QACtB,CAAC,CAAC,OAAO0B,KAAK,EAAE;UACd3B,OAAO,CAAC2B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;QAClC;MACF;;MAEA;MACAnI,gBAAgB,GAAG,IAAI;MACvBC,gBAAgB,GAAG,EAAE;MACrBC,iBAAiB,GAAG,CAAC;MACrBE,cAAc,GAAG,IAAI;MACrBC,eAAe,GAAG,IAAI;MACtBC,QAAQ,GAAG,KAAK;MAEhBkG,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED1F,MAAM,CAACoQ,gBAAgB,CAAC,qBAAqB,EAAEE,YAAY,CAAC;;IAE5D;IACA,OAAO,MAAM;MAAA,IAAAO,qBAAA;MACX7Q,MAAM,CAAC8Q,mBAAmB,CAAC,qBAAqB,EAAER,YAAY,CAAC;MAE/D,IAAIlR,oBAAoB,EAAE;QACxBoR,aAAa,CAACpR,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;MAEA,IAAI6B,aAAa,CAAC6C,OAAO,EAAE;QACzB,IAAI;UACF7C,aAAa,CAAC6C,OAAO,CAACyM,KAAK,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAOnJ,KAAK,EAAE;UACd3B,OAAO,CAAC2B,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;QACzC;MACF;MAEApH,MAAM,CAAC8Q,mBAAmB,CAAC,QAAQ,EAAEZ,YAAY,CAAC;MAClD,CAAAW,qBAAA,GAAAjQ,YAAY,CAACkD,OAAO,cAAA+M,qBAAA,uBAApBA,qBAAA,CAAsBE,WAAW,CAAC3F,QAAQ,CAACQ,UAAU,CAAC;MACtDR,QAAQ,CAACsF,OAAO,CAAC,CAAC;;MAElB;MACAlQ,aAAa,CAACwH,OAAO,CAAC,CAACY,KAAK,EAAEV,EAAE,KAAK;QACnCrI,KAAK,CAAC6J,MAAM,CAACd,KAAK,CAAC;MACrB,CAAC,CAAC;MACFpI,aAAa,CAACmQ,KAAK,CAAC,CAAC;;MAErB;MACA,OAAM,EAAAK,MAAA,GAAAnR,KAAK,cAAAmR,MAAA,uBAALA,MAAA,CAAOlD,QAAQ,CAACC,MAAM,IAAG,CAAC,EAAE;QAAA,IAAAiD,MAAA;QAChCnR,KAAK,CAAC6J,MAAM,CAAC7J,KAAK,CAACiO,QAAQ,CAAC,CAAC,CAAC,CAAC;MACjC;MAEAjO,KAAK,GAAG,IAAI;MACZH,qBAAqB,GAAG,IAAI;MAC5BC,qBAAqB,GAAG,IAAI;MAC5BC,oBAAoB,GAAG,IAAI;IAC7B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEd,OAAA,CAAAE,SAAA;IAAA8O,QAAA,gBACEhP,OAAA;MAAKmS,KAAK,EAAEjO,uBAAwB;MAAA8K,QAAA,gBAClChP,OAAA;QAAMmS,KAAK,EAAE7N,UAAW;QAAA0K,QAAA,EAAC;MAAK;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrCvS,OAAA;QAAKmS,KAAK,EAAExN,oBAAqB;QAAAqK,QAAA,eAC/BhP,OAAA,CAACH,MAAM;UACL2S,WAAW,EAAC,gCAAO;UACnBC,QAAQ,EAAEzL,wBAAyB;UACnC0L,OAAO,EAAE5S,iBAAiB,CAACqH,aAAa,CAACqD,GAAG,CAACtD,YAAY,KAAK;YAC5DD,KAAK,EAAEC,YAAY,CAACI,IAAI;YACxBqL,KAAK,EAAEzL,YAAY,CAACI;UACtB,CAAC,CAAC,CAAE;UACJsL,IAAI,EAAC,QAAQ;UACbC,QAAQ,EAAE,IAAK;UACfV,KAAK,EAAE;YAAE/N,KAAK,EAAE;UAAO,CAAE;UACzB0O,aAAa,EAAE;YACb3P,MAAM,EAAE,IAAI;YACZ4P,SAAS,EAAE;UACb;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNvS,OAAA;MAAKgT,GAAG,EAAElR,YAAa;MAACqQ,KAAK,EAAE;QAAE/N,KAAK,EAAE,MAAM;QAAE6F,MAAM,EAAE;MAAO;IAAE;MAAAmI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpEvS,OAAA;MAAKmS,KAAK,EAAErP,oBAAqB;MAAAkM,QAAA,gBAC/BhP,OAAA;QACEmS,KAAK,EAAE;UACL,GAAG7O,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/E8B,KAAK,EAAE9B,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFqQ,OAAO,EAAEpO,kBAAmB;QAAAmK,QAAA,EAC7B;MAED;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvS,OAAA;QACEmS,KAAK,EAAE;UACL,GAAG7O,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/E8B,KAAK,EAAE9B,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFqQ,OAAO,EAAElO,kBAAmB;QAAAiK,QAAA,EAC7B;MAED;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAA1Q,EAAA,CAx3BMD,WAAW;AAAAsR,EAAA,GAAXtR,WAAW;AAy3BjB,SAASuR,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAACjP,KAAK,GAAG,GAAG;EAClBiP,MAAM,CAACpJ,MAAM,GAAG,EAAE;;EAElB;EACAuJ,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAACjP,KAAK,GAAC,CAAC,EAAEiP,MAAM,CAACpJ,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAM6J,OAAO,GAAG,IAAIvU,KAAK,CAACwU,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAIzU,KAAK,CAAC0U,cAAc,CAAC;IAC9CzJ,GAAG,EAAEsJ,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAI5U,KAAK,CAAC6U,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAACpE,KAAK,CAAClI,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAOsM,MAAM;AACf;;AAEA;AACAjT,MAAM,CAACmT,WAAW,GAAG,CAAC9O,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EAChC,IAAItF,gBAAgB,EAAE;IACpBA,gBAAgB,CAAC4C,QAAQ,CAAC8E,GAAG,CAACtC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACtCtF,gBAAgB,CAAC2H,YAAY,CAAC,CAAC;IAC/B3H,gBAAgB,CAAC4H,iBAAiB,CAAC,IAAI,CAAC;IACxCpB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAACrB,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACAvE,MAAM,CAACoT,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMpI,MAAM,GAAGoH,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAIvI,MAAM,EAAE;MACV;MACA,MAAMwI,MAAM,GAAGxI,MAAM,CAACnJ,QAAQ,CAACmC,KAAK,CAAC,CAAC;;MAEtC;MACAgH,MAAM,CAACnJ,QAAQ,CAAC8E,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BqE,MAAM,CAAC9G,EAAE,CAACyC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBqE,MAAM,CAAC/F,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACA+F,MAAM,CAACpE,YAAY,CAAC,CAAC;MACrBoE,MAAM,CAACnE,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMpH,QAAQ,GAAG2S,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIhU,QAAQ,EAAE;QACZA,QAAQ,CAACuF,MAAM,CAAC2B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BlH,QAAQ,CAACyF,MAAM,CAAC,CAAC;MACnB;MAEAO,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBgO,GAAG,EAAEF,MAAM,CAACzM,OAAO,CAAC,CAAC;QACrB4M,GAAG,EAAE3I,MAAM,CAACnJ,QAAQ,CAACkF,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAO6M,CAAC,EAAE;IACVnO,OAAO,CAAC2B,KAAK,CAAC,YAAY,EAAEwM,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAM9I,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACFrF,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAMkJ,MAAM,GAAG,IAAItQ,UAAU,CAAC,CAAC;;IAE/B;IACA,MAAM,CAACuV,WAAW,EAAEC,WAAW,EAAEC,UAAU,CAAC,GAAG,MAAMnH,OAAO,CAACoH,GAAG,CAAC,CAC/DpF,MAAM,CAACqF,SAAS,CAAC,GAAG1T,QAAQ,uBAAuB,CAAC,EACpDqO,MAAM,CAACqF,SAAS,CAAC,GAAG1T,QAAQ,uBAAuB,CAAC,EACpDqO,MAAM,CAACqF,SAAS,CAAC,GAAG1T,QAAQ,sBAAsB,CAAC,CACpD,CAAC;;IAEF;IACAb,qBAAqB,GAAGmU,WAAW,CAAChU,KAAK;IACzCH,qBAAqB,CAAC2N,QAAQ,CAAEC,KAAK,IAAK;MACxC,IAAIA,KAAK,CAACC,MAAM,EAAE;QAChBD,KAAK,CAACE,QAAQ,GAAG,IAAInP,KAAK,CAACqP,oBAAoB,CAAC;UAC9ClK,KAAK,EAAE,QAAQ;UACfmK,SAAS,EAAE,GAAG;UACdC,SAAS,EAAE,GAAG;UACdC,eAAe,EAAE;QACnB,CAAC,CAAC;;QAEF;QACA;QACA;QACA;MACF;IACF,CAAC,CAAC;;IAEF;IACAlO,qBAAqB,GAAGmU,WAAW,CAACjU,KAAK;IACzC;IACAF,qBAAqB,CAACkP,KAAK,CAAClI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxC;IACAhH,qBAAqB,CAAC0N,QAAQ,CAAEC,KAAK,IAAK;MACxC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClC;QACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;QAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;QAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;MACtC;IACF,CAAC,CAAC;;IAEF;IACAjO,oBAAoB,GAAGmU,UAAU,CAAClU,KAAK;IACvC;IACAD,oBAAoB,CAACiP,KAAK,CAAClI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvC;IACA/G,oBAAoB,CAACyN,QAAQ,CAAEC,KAAK,IAAK;MACvC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClC;QACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;QAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;QAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;MACtC;IACF,CAAC,CAAC;IAEFpI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,CAAC,OAAO0B,KAAK,EAAE;IACd3B,OAAO,CAAC2B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;AAED,eAAe1G,WAAW;AAAC,IAAAsR,EAAA;AAAAkC,YAAA,CAAAlC,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}