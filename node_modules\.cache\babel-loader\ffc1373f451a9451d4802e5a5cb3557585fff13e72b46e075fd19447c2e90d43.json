{"ast": null, "code": "import { resetComponent } from '../../style';\nimport { genCollapseMotion } from '../../style/motion';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genDraggerStyle from './dragger';\nimport genListStyle from './list';\nimport genMotionStyle from './motion';\nimport { genPictureCardStyle, genPictureStyle } from './picture';\nimport genRtlStyle from './rtl';\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    colorTextDisabled\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: Object.assign(Object.assign({}, resetComponent(token)), {\n      [componentCls]: {\n        outline: 0,\n        \"input[type='file']\": {\n          cursor: 'pointer'\n        }\n      },\n      [`${componentCls}-select`]: {\n        display: 'inline-block'\n      },\n      [`${componentCls}-hidden`]: {\n        display: 'none'\n      },\n      [`${componentCls}-disabled`]: {\n        color: colorTextDisabled,\n        cursor: 'not-allowed'\n      }\n    })\n  };\n};\nexport const prepareComponentToken = token => ({\n  actionsColor: token.colorTextDescription\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Upload', token => {\n  const {\n    fontSizeHeading3,\n    fontHeight,\n    lineWidth,\n    controlHeightLG,\n    calc\n  } = token;\n  const uploadToken = mergeToken(token, {\n    uploadThumbnailSize: calc(fontSizeHeading3).mul(2).equal(),\n    uploadProgressOffset: calc(calc(fontHeight).div(2)).add(lineWidth).equal(),\n    uploadPicCardSize: calc(controlHeightLG).mul(2.55).equal()\n  });\n  return [genBaseStyle(uploadToken), genDraggerStyle(uploadToken), genPictureStyle(uploadToken), genPictureCardStyle(uploadToken), genListStyle(uploadToken), genMotionStyle(uploadToken), genRtlStyle(uploadToken), genCollapseMotion(uploadToken)];\n}, prepareComponentToken);", "map": {"version": 3, "names": ["resetComponent", "genCollapseMotion", "genStyleHooks", "mergeToken", "genDraggerStyle", "genListStyle", "genMotionStyle", "genPictureCardStyle", "genPictureStyle", "genRtlStyle", "genBaseStyle", "token", "componentCls", "colorTextDisabled", "Object", "assign", "outline", "cursor", "display", "color", "prepareComponentToken", "actionsColor", "colorTextDescription", "fontSizeHeading3", "fontHeight", "lineWidth", "controlHeightLG", "calc", "uploadToken", "uploadThumbnailSize", "mul", "equal", "uploadProgressOffset", "div", "add", "uploadPicCardSize"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/upload/style/index.js"], "sourcesContent": ["import { resetComponent } from '../../style';\nimport { genCollapseMotion } from '../../style/motion';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genDraggerStyle from './dragger';\nimport genListStyle from './list';\nimport genMotionStyle from './motion';\nimport { genPictureCardStyle, genPictureStyle } from './picture';\nimport genRtlStyle from './rtl';\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    colorTextDisabled\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: Object.assign(Object.assign({}, resetComponent(token)), {\n      [componentCls]: {\n        outline: 0,\n        \"input[type='file']\": {\n          cursor: 'pointer'\n        }\n      },\n      [`${componentCls}-select`]: {\n        display: 'inline-block'\n      },\n      [`${componentCls}-hidden`]: {\n        display: 'none'\n      },\n      [`${componentCls}-disabled`]: {\n        color: colorTextDisabled,\n        cursor: 'not-allowed'\n      }\n    })\n  };\n};\nexport const prepareComponentToken = token => ({\n  actionsColor: token.colorTextDescription\n});\n// ============================== Export ==============================\nexport default genStyleHooks('Upload', token => {\n  const {\n    fontSizeHeading3,\n    fontHeight,\n    lineWidth,\n    controlHeightLG,\n    calc\n  } = token;\n  const uploadToken = mergeToken(token, {\n    uploadThumbnailSize: calc(fontSizeHeading3).mul(2).equal(),\n    uploadProgressOffset: calc(calc(fontHeight).div(2)).add(lineWidth).equal(),\n    uploadPicCardSize: calc(controlHeightLG).mul(2.55).equal()\n  });\n  return [genBaseStyle(uploadToken), genDraggerStyle(uploadToken), genPictureStyle(uploadToken), genPictureCardStyle(uploadToken), genListStyle(uploadToken), genMotionStyle(uploadToken), genRtlStyle(uploadToken), genCollapseMotion(uploadToken)];\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,cAAc,QAAQ,aAAa;AAC5C,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,OAAOC,eAAe,MAAM,WAAW;AACvC,OAAOC,YAAY,MAAM,QAAQ;AACjC,OAAOC,cAAc,MAAM,UAAU;AACrC,SAASC,mBAAmB,EAAEC,eAAe,QAAQ,WAAW;AAChE,OAAOC,WAAW,MAAM,OAAO;AAC/B,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAGF,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,UAAU,GAAGE,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEf,cAAc,CAACW,KAAK,CAAC,CAAC,EAAE;MACnF,CAACC,YAAY,GAAG;QACdI,OAAO,EAAE,CAAC;QACV,oBAAoB,EAAE;UACpBC,MAAM,EAAE;QACV;MACF,CAAC;MACD,CAAC,GAAGL,YAAY,SAAS,GAAG;QAC1BM,OAAO,EAAE;MACX,CAAC;MACD,CAAC,GAAGN,YAAY,SAAS,GAAG;QAC1BM,OAAO,EAAE;MACX,CAAC;MACD,CAAC,GAAGN,YAAY,WAAW,GAAG;QAC5BO,KAAK,EAAEN,iBAAiB;QACxBI,MAAM,EAAE;MACV;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD,OAAO,MAAMG,qBAAqB,GAAGT,KAAK,KAAK;EAC7CU,YAAY,EAAEV,KAAK,CAACW;AACtB,CAAC,CAAC;AACF;AACA,eAAepB,aAAa,CAAC,QAAQ,EAAES,KAAK,IAAI;EAC9C,MAAM;IACJY,gBAAgB;IAChBC,UAAU;IACVC,SAAS;IACTC,eAAe;IACfC;EACF,CAAC,GAAGhB,KAAK;EACT,MAAMiB,WAAW,GAAGzB,UAAU,CAACQ,KAAK,EAAE;IACpCkB,mBAAmB,EAAEF,IAAI,CAACJ,gBAAgB,CAAC,CAACO,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IAC1DC,oBAAoB,EAAEL,IAAI,CAACA,IAAI,CAACH,UAAU,CAAC,CAACS,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAACT,SAAS,CAAC,CAACM,KAAK,CAAC,CAAC;IAC1EI,iBAAiB,EAAER,IAAI,CAACD,eAAe,CAAC,CAACI,GAAG,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC;EAC3D,CAAC,CAAC;EACF,OAAO,CAACrB,YAAY,CAACkB,WAAW,CAAC,EAAExB,eAAe,CAACwB,WAAW,CAAC,EAAEpB,eAAe,CAACoB,WAAW,CAAC,EAAErB,mBAAmB,CAACqB,WAAW,CAAC,EAAEvB,YAAY,CAACuB,WAAW,CAAC,EAAEtB,cAAc,CAACsB,WAAW,CAAC,EAAEnB,WAAW,CAACmB,WAAW,CAAC,EAAE3B,iBAAiB,CAAC2B,WAAW,CAAC,CAAC;AACpP,CAAC,EAAER,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}