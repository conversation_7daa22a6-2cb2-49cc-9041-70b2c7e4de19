{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useSize from '../config-provider/hooks/useSize';\nimport { RadioGroupContextProvider } from './context';\nimport Radio from './radio';\nimport useStyle from './style';\nimport useId from \"rc-util/es/hooks/useId\";\nconst RadioGroup = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const defaultName = useId();\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    options,\n    buttonStyle = 'outline',\n    disabled,\n    children,\n    size: customizeSize,\n    style,\n    id,\n    optionType,\n    name = defaultName,\n    defaultValue,\n    value: customizedValue,\n    block = false,\n    onChange,\n    onMouseEnter,\n    onMouseLeave,\n    onFocus,\n    onBlur\n  } = props;\n  const [value, setValue] = useMergedState(defaultValue, {\n    value: customizedValue\n  });\n  const onRadioChange = React.useCallback(event => {\n    const lastValue = value;\n    const val = event.target.value;\n    if (!('value' in props)) {\n      setValue(val);\n    }\n    if (val !== lastValue) {\n      onChange === null || onChange === void 0 ? void 0 : onChange(event);\n    }\n  }, [value, setValue, onChange]);\n  const prefixCls = getPrefixCls('radio', customizePrefixCls);\n  const groupPrefixCls = `${prefixCls}-group`;\n  // Style\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  let childrenToRender = children;\n  // 如果存在 options, 优先使用\n  if (options && options.length > 0) {\n    childrenToRender = options.map(option => {\n      if (typeof option === 'string' || typeof option === 'number') {\n        // 此处类型自动推导为 string\n        return /*#__PURE__*/React.createElement(Radio, {\n          key: option.toString(),\n          prefixCls: prefixCls,\n          disabled: disabled,\n          value: option,\n          checked: value === option\n        }, option);\n      }\n      // 此处类型自动推导为 { label: string value: string }\n      return /*#__PURE__*/React.createElement(Radio, {\n        key: `radio-group-value-options-${option.value}`,\n        prefixCls: prefixCls,\n        disabled: option.disabled || disabled,\n        value: option.value,\n        checked: value === option.value,\n        title: option.title,\n        style: option.style,\n        id: option.id,\n        required: option.required\n      }, option.label);\n    });\n  }\n  const mergedSize = useSize(customizeSize);\n  const classString = classNames(groupPrefixCls, `${groupPrefixCls}-${buttonStyle}`, {\n    [`${groupPrefixCls}-${mergedSize}`]: mergedSize,\n    [`${groupPrefixCls}-rtl`]: direction === 'rtl',\n    [`${groupPrefixCls}-block`]: block\n  }, className, rootClassName, hashId, cssVarCls, rootCls);\n  const memoizedValue = React.useMemo(() => ({\n    onChange: onRadioChange,\n    value,\n    disabled,\n    name,\n    optionType,\n    block\n  }), [onRadioChange, value, disabled, name, optionType, block]);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({}, pickAttrs(props, {\n    aria: true,\n    data: true\n  }), {\n    className: classString,\n    style: style,\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    id: id,\n    ref: ref\n  }), /*#__PURE__*/React.createElement(RadioGroupContextProvider, {\n    value: memoizedValue\n  }, childrenToRender)));\n});\nexport default /*#__PURE__*/React.memo(RadioGroup);", "map": {"version": 3, "names": ["React", "classNames", "useMergedState", "pickAttrs", "ConfigContext", "useCSSVarCls", "useSize", "RadioGroupContextProvider", "Radio", "useStyle", "useId", "RadioGroup", "forwardRef", "props", "ref", "getPrefixCls", "direction", "useContext", "defaultName", "prefixCls", "customizePrefixCls", "className", "rootClassName", "options", "buttonStyle", "disabled", "children", "size", "customizeSize", "style", "id", "optionType", "name", "defaultValue", "value", "customizedValue", "block", "onChange", "onMouseEnter", "onMouseLeave", "onFocus", "onBlur", "setValue", "onRadioChange", "useCallback", "event", "lastValue", "val", "target", "groupPrefixCls", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "children<PERSON><PERSON><PERSON><PERSON>", "length", "map", "option", "createElement", "key", "toString", "checked", "title", "required", "label", "mergedSize", "classString", "memoizedValue", "useMemo", "Object", "assign", "aria", "data", "memo"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/radio/group.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useSize from '../config-provider/hooks/useSize';\nimport { RadioGroupContextProvider } from './context';\nimport Radio from './radio';\nimport useStyle from './style';\nimport useId from \"rc-util/es/hooks/useId\";\nconst RadioGroup = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const defaultName = useId();\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    options,\n    buttonStyle = 'outline',\n    disabled,\n    children,\n    size: customizeSize,\n    style,\n    id,\n    optionType,\n    name = defaultName,\n    defaultValue,\n    value: customizedValue,\n    block = false,\n    onChange,\n    onMouseEnter,\n    onMouseLeave,\n    onFocus,\n    onBlur\n  } = props;\n  const [value, setValue] = useMergedState(defaultValue, {\n    value: customizedValue\n  });\n  const onRadioChange = React.useCallback(event => {\n    const lastValue = value;\n    const val = event.target.value;\n    if (!('value' in props)) {\n      setValue(val);\n    }\n    if (val !== lastValue) {\n      onChange === null || onChange === void 0 ? void 0 : onChange(event);\n    }\n  }, [value, setValue, onChange]);\n  const prefixCls = getPrefixCls('radio', customizePrefixCls);\n  const groupPrefixCls = `${prefixCls}-group`;\n  // Style\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  let childrenToRender = children;\n  // 如果存在 options, 优先使用\n  if (options && options.length > 0) {\n    childrenToRender = options.map(option => {\n      if (typeof option === 'string' || typeof option === 'number') {\n        // 此处类型自动推导为 string\n        return /*#__PURE__*/React.createElement(Radio, {\n          key: option.toString(),\n          prefixCls: prefixCls,\n          disabled: disabled,\n          value: option,\n          checked: value === option\n        }, option);\n      }\n      // 此处类型自动推导为 { label: string value: string }\n      return /*#__PURE__*/React.createElement(Radio, {\n        key: `radio-group-value-options-${option.value}`,\n        prefixCls: prefixCls,\n        disabled: option.disabled || disabled,\n        value: option.value,\n        checked: value === option.value,\n        title: option.title,\n        style: option.style,\n        id: option.id,\n        required: option.required\n      }, option.label);\n    });\n  }\n  const mergedSize = useSize(customizeSize);\n  const classString = classNames(groupPrefixCls, `${groupPrefixCls}-${buttonStyle}`, {\n    [`${groupPrefixCls}-${mergedSize}`]: mergedSize,\n    [`${groupPrefixCls}-rtl`]: direction === 'rtl',\n    [`${groupPrefixCls}-block`]: block\n  }, className, rootClassName, hashId, cssVarCls, rootCls);\n  const memoizedValue = React.useMemo(() => ({\n    onChange: onRadioChange,\n    value,\n    disabled,\n    name,\n    optionType,\n    block\n  }), [onRadioChange, value, disabled, name, optionType, block]);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", Object.assign({}, pickAttrs(props, {\n    aria: true,\n    data: true\n  }), {\n    className: classString,\n    style: style,\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    id: id,\n    ref: ref\n  }), /*#__PURE__*/React.createElement(RadioGroupContextProvider, {\n    value: memoizedValue\n  }, childrenToRender)));\n});\nexport default /*#__PURE__*/React.memo(RadioGroup);"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAOC,OAAO,MAAM,kCAAkC;AACtD,SAASC,yBAAyB,QAAQ,WAAW;AACrD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,QAAQ,MAAM,SAAS;AAC9B,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,MAAMC,UAAU,GAAG,aAAaX,KAAK,CAACY,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC/D,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAGhB,KAAK,CAACiB,UAAU,CAACb,aAAa,CAAC;EACnC,MAAMc,WAAW,GAAGR,KAAK,CAAC,CAAC;EAC3B,MAAM;IACJS,SAAS,EAAEC,kBAAkB;IAC7BC,SAAS;IACTC,aAAa;IACbC,OAAO;IACPC,WAAW,GAAG,SAAS;IACvBC,QAAQ;IACRC,QAAQ;IACRC,IAAI,EAAEC,aAAa;IACnBC,KAAK;IACLC,EAAE;IACFC,UAAU;IACVC,IAAI,GAAGd,WAAW;IAClBe,YAAY;IACZC,KAAK,EAAEC,eAAe;IACtBC,KAAK,GAAG,KAAK;IACbC,QAAQ;IACRC,YAAY;IACZC,YAAY;IACZC,OAAO;IACPC;EACF,CAAC,GAAG5B,KAAK;EACT,MAAM,CAACqB,KAAK,EAAEQ,QAAQ,CAAC,GAAGxC,cAAc,CAAC+B,YAAY,EAAE;IACrDC,KAAK,EAAEC;EACT,CAAC,CAAC;EACF,MAAMQ,aAAa,GAAG3C,KAAK,CAAC4C,WAAW,CAACC,KAAK,IAAI;IAC/C,MAAMC,SAAS,GAAGZ,KAAK;IACvB,MAAMa,GAAG,GAAGF,KAAK,CAACG,MAAM,CAACd,KAAK;IAC9B,IAAI,EAAE,OAAO,IAAIrB,KAAK,CAAC,EAAE;MACvB6B,QAAQ,CAACK,GAAG,CAAC;IACf;IACA,IAAIA,GAAG,KAAKD,SAAS,EAAE;MACrBT,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACQ,KAAK,CAAC;IACrE;EACF,CAAC,EAAE,CAACX,KAAK,EAAEQ,QAAQ,EAAEL,QAAQ,CAAC,CAAC;EAC/B,MAAMlB,SAAS,GAAGJ,YAAY,CAAC,OAAO,EAAEK,kBAAkB,CAAC;EAC3D,MAAM6B,cAAc,GAAG,GAAG9B,SAAS,QAAQ;EAC3C;EACA,MAAM+B,OAAO,GAAG7C,YAAY,CAACc,SAAS,CAAC;EACvC,MAAM,CAACgC,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAACU,SAAS,EAAE+B,OAAO,CAAC;EACpE,IAAII,gBAAgB,GAAG5B,QAAQ;EAC/B;EACA,IAAIH,OAAO,IAAIA,OAAO,CAACgC,MAAM,GAAG,CAAC,EAAE;IACjCD,gBAAgB,GAAG/B,OAAO,CAACiC,GAAG,CAACC,MAAM,IAAI;MACvC,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;QAC5D;QACA,OAAO,aAAazD,KAAK,CAAC0D,aAAa,CAAClD,KAAK,EAAE;UAC7CmD,GAAG,EAAEF,MAAM,CAACG,QAAQ,CAAC,CAAC;UACtBzC,SAAS,EAAEA,SAAS;UACpBM,QAAQ,EAAEA,QAAQ;UAClBS,KAAK,EAAEuB,MAAM;UACbI,OAAO,EAAE3B,KAAK,KAAKuB;QACrB,CAAC,EAAEA,MAAM,CAAC;MACZ;MACA;MACA,OAAO,aAAazD,KAAK,CAAC0D,aAAa,CAAClD,KAAK,EAAE;QAC7CmD,GAAG,EAAE,6BAA6BF,MAAM,CAACvB,KAAK,EAAE;QAChDf,SAAS,EAAEA,SAAS;QACpBM,QAAQ,EAAEgC,MAAM,CAAChC,QAAQ,IAAIA,QAAQ;QACrCS,KAAK,EAAEuB,MAAM,CAACvB,KAAK;QACnB2B,OAAO,EAAE3B,KAAK,KAAKuB,MAAM,CAACvB,KAAK;QAC/B4B,KAAK,EAAEL,MAAM,CAACK,KAAK;QACnBjC,KAAK,EAAE4B,MAAM,CAAC5B,KAAK;QACnBC,EAAE,EAAE2B,MAAM,CAAC3B,EAAE;QACbiC,QAAQ,EAAEN,MAAM,CAACM;MACnB,CAAC,EAAEN,MAAM,CAACO,KAAK,CAAC;IAClB,CAAC,CAAC;EACJ;EACA,MAAMC,UAAU,GAAG3D,OAAO,CAACsB,aAAa,CAAC;EACzC,MAAMsC,WAAW,GAAGjE,UAAU,CAACgD,cAAc,EAAE,GAAGA,cAAc,IAAIzB,WAAW,EAAE,EAAE;IACjF,CAAC,GAAGyB,cAAc,IAAIgB,UAAU,EAAE,GAAGA,UAAU;IAC/C,CAAC,GAAGhB,cAAc,MAAM,GAAGjC,SAAS,KAAK,KAAK;IAC9C,CAAC,GAAGiC,cAAc,QAAQ,GAAGb;EAC/B,CAAC,EAAEf,SAAS,EAAEC,aAAa,EAAE8B,MAAM,EAAEC,SAAS,EAAEH,OAAO,CAAC;EACxD,MAAMiB,aAAa,GAAGnE,KAAK,CAACoE,OAAO,CAAC,OAAO;IACzC/B,QAAQ,EAAEM,aAAa;IACvBT,KAAK;IACLT,QAAQ;IACRO,IAAI;IACJD,UAAU;IACVK;EACF,CAAC,CAAC,EAAE,CAACO,aAAa,EAAET,KAAK,EAAET,QAAQ,EAAEO,IAAI,EAAED,UAAU,EAAEK,KAAK,CAAC,CAAC;EAC9D,OAAOe,UAAU,CAAC,aAAanD,KAAK,CAAC0D,aAAa,CAAC,KAAK,EAAEW,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnE,SAAS,CAACU,KAAK,EAAE;IAC3F0D,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE;EACR,CAAC,CAAC,EAAE;IACFnD,SAAS,EAAE6C,WAAW;IACtBrC,KAAK,EAAEA,KAAK;IACZS,YAAY,EAAEA,YAAY;IAC1BC,YAAY,EAAEA,YAAY;IAC1BC,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAEA,MAAM;IACdX,EAAE,EAAEA,EAAE;IACNhB,GAAG,EAAEA;EACP,CAAC,CAAC,EAAE,aAAad,KAAK,CAAC0D,aAAa,CAACnD,yBAAyB,EAAE;IAC9D2B,KAAK,EAAEiC;EACT,CAAC,EAAEb,gBAAgB,CAAC,CAAC,CAAC;AACxB,CAAC,CAAC;AACF,eAAe,aAAatD,KAAK,CAACyE,IAAI,CAAC9D,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}