{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\DeviceManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, Space, Card, Tag, message } from 'antd';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconst ContentArea = styled.div`\n  // padding: 24px;\n  background: white;\n  position: relative;\n  z-index: 1002;\n`;\n_c = ContentArea;\nconst DeviceManagement = ({\n  id\n}, ref) => {\n  _s();\n  const [devices, setDevices] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingDevice, setEditingDevice] = useState(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentDevice, setCurrentDevice] = useState(null);\n  const [currentDeviceType, setCurrentDeviceType] = useState((editingDevice === null || editingDevice === void 0 ? void 0 : editingDevice.type) || null);\n\n  // 获取设备列表\n  const fetchDevices = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/api/devices`);\n      if (response.data.success) {\n        setDevices(response.data.data);\n      } else {\n        throw new Error(response.data.message || '获取设备列表失败');\n      }\n    } catch (error) {\n      console.error('获取设备列表失败:', error);\n      message.error('获取设备列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchDevices();\n  }, []);\n\n  // 处理添加设备\n  const handleAddDevice = () => {\n    setEditingDevice(null);\n    setCurrentDeviceType(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // 处理编辑设备\n  const handleEditDevice = device => {\n    setEditingDevice(device);\n    setCurrentDeviceType(device.type);\n    form.setFieldsValue({\n      name: device.name,\n      type: device.type,\n      status: device.status,\n      location: device.location,\n      ipAddress: device.ipAddress,\n      manufacturer: device.manufacturer,\n      model: device.model,\n      description: device.description,\n      rtspUrl: device.rtspUrl,\n      entrance: device.entrance\n    });\n    setModalVisible(true);\n  };\n\n  // 处理查看设备详情\n  const handleViewDevice = device => {\n    setCurrentDevice(device);\n    setDetailModalVisible(true);\n  };\n\n  // 处理删除设备\n  const handleDeleteDevice = async deviceId => {\n    try {\n      const response = await axios.delete(`${API_BASE_URL}/api/devices/${deviceId}`);\n      if (response.data.success) {\n        message.success('设备删除成功');\n        fetchDevices(); // 重新获取设备列表\n      } else {\n        throw new Error(response.data.message || '删除设备失败');\n      }\n    } catch (error) {\n      console.error('删除设备失败:', error);\n      message.error('删除设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 处理表单提交\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n\n      // 为调试添加日志\n      console.log('表单验证通过，原始数据:', values);\n      console.log('当前设备类型:', currentDeviceType);\n\n      // 确保摄像头设备有 rtspUrl 字段\n      if (values.type === 'camera' || currentDeviceType === 'camera') {\n        // 直接从表单获取 rtspUrl 值，确保它不会丢失\n        const rtspInput = document.querySelector('textarea[placeholder*=\"RTSP\"]');\n        let rtspUrl = '';\n        if (rtspInput) {\n          rtspUrl = rtspInput.value || '';\n          console.log('从DOM元素获取的 RTSP 地址:', rtspUrl);\n        } else {\n          rtspUrl = form.getFieldValue('rtspUrl') || values.rtspUrl || '';\n          console.log('从表单获取的 RTSP 地址:', rtspUrl);\n        }\n\n        // 无论表单中是否有该字段，都强制设置 rtspUrl\n        values.rtspUrl = rtspUrl;\n        console.log('最终设置的 RTSP 地址:', rtspUrl);\n      } else {\n        // 对于非摄像头设备，确保移除 rtspUrl 字段\n        delete values.rtspUrl;\n        console.log('非摄像头设备，移除 rtspUrl 字段');\n      }\n      console.log('提交表单最终数据:', JSON.stringify(values, null, 2));\n      if (editingDevice) {\n        // 编辑设备\n        const response = await axios.put(`${API_BASE_URL}/api/devices/${editingDevice.id}`, values);\n        if (response.data.success) {\n          message.success('设备更新成功');\n          setModalVisible(false);\n          fetchDevices(); // 重新获取设备列表\n        } else {\n          throw new Error(response.data.message || '更新设备失败');\n        }\n      } else {\n        // 添加设备\n        const response = await axios.post(`${API_BASE_URL}/api/devices`, values);\n        if (response.data.success) {\n          message.success('设备添加成功');\n          setModalVisible(false);\n          fetchDevices(); // 重新获取设备列表\n        } else {\n          throw new Error(response.data.message || '添加设备失败');\n        }\n      }\n    } catch (error) {\n      console.error('保存设备失败:', error);\n      message.error('保存设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 修改设备类型选择的处理函数\n  const handleDeviceTypeChange = value => {\n    console.log('设备类型变更为:', value);\n    setCurrentDeviceType(value);\n    if (value === 'camera') {\n      // 如果是摄像头，确保表单中有 rtspUrl 字段\n      const currentRtspUrl = form.getFieldValue('rtspUrl') || '';\n      form.setFieldsValue({\n        type: value,\n        rtspUrl: currentRtspUrl\n      });\n      console.log('设置摄像头 RTSP 初始值:', currentRtspUrl);\n    } else {\n      // 如果不是摄像头，移除 rtspUrl 字段\n      form.setFieldsValue({\n        type: value\n      });\n      form.resetFields(['rtspUrl']);\n      console.log('重置 RTSP 字段');\n    }\n  };\n\n  // 渲染状态标签\n  const renderStatusTag = status => {\n    const statusMap = {\n      online: {\n        color: 'green',\n        text: '在线'\n      },\n      offline: {\n        color: 'gray',\n        text: '离线'\n      },\n      warning: {\n        color: 'orange',\n        text: '警告'\n      },\n      error: {\n        color: 'red',\n        text: '错误'\n      },\n      maintenance: {\n        color: 'blue',\n        text: '维护中'\n      }\n    };\n    const statusInfo = statusMap[status] || {\n      color: 'default',\n      text: status\n    };\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: statusInfo.color,\n      children: statusInfo.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 12\n    }, this);\n  };\n\n  // 设备类型映射\n  const deviceTypeMap = {\n    camera: '摄像头',\n    mmwave_radar: '毫米波雷达',\n    lidar: '激光雷达',\n    rsu: 'RSU',\n    edge_computing: '边缘计算单元',\n    obu: 'OBU'\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '设备名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '设备类型',\n    dataIndex: 'type',\n    key: 'type',\n    render: type => deviceTypeMap[type] || type\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: renderStatusTag\n  }, {\n    title: '所在区域',\n    dataIndex: 'location',\n    key: 'location'\n  }, {\n    title: 'IP地址',\n    dataIndex: 'ipAddress',\n    key: 'ipAddress'\n  }, {\n    title: '安装位置',\n    dataIndex: 'entrance',\n    key: 'entrance'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleViewDevice(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleEditDevice(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        danger: true,\n        onClick: () => handleDeleteDevice(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 暴露 fetchDevices 方法\n  useImperativeHandle(ref, () => ({\n    fetchDevices\n  }));\n  return (\n    /*#__PURE__*/\n    // <div id={id}>\n    _jsxDEV(ContentArea, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\"\n          // icon={<PlusOutlined />}\n          ,\n          onClick: handleAddDevice,\n          children: \"\\u6DFB\\u52A0\\u8BBE\\u5907\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        loading: loading,\n        dataSource: devices,\n        columns: columns,\n        rowKey: \"id\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        title: editingDevice ? '编辑设备' : '添加设备',\n        open: modalVisible,\n        onOk: handleModalOk,\n        onCancel: () => setModalVisible(false),\n        width: 600,\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          layout: \"vertical\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"name\",\n            label: \"\\u8BBE\\u5907\\u540D\\u79F0\",\n            rules: [{\n              required: true,\n              message: '请输入设备名称'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u540D\\u79F0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"type\",\n            label: \"\\u8BBE\\u5907\\u7C7B\\u578B\",\n            rules: [{\n              required: true,\n              message: '请选择设备类型'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u7C7B\\u578B\",\n              onChange: handleDeviceTypeChange,\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"camera\",\n                children: \"\\u6444\\u50CF\\u5934\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"mmwave_radar\",\n                children: \"\\u6BEB\\u7C73\\u6CE2\\u96F7\\u8FBE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"lidar\",\n                children: \"\\u6FC0\\u5149\\u96F7\\u8FBE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"rsu\",\n                children: \"RSU\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"edge_computing\",\n                children: \"\\u8FB9\\u7F18\\u8BA1\\u7B97\\u5355\\u5143\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"obu\",\n                children: \"OBU\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 11\n          }, this), currentDeviceType === 'camera' && /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"rtspUrl\",\n            label: \"RTSP\\u5730\\u5740\",\n            initialValue: \"\",\n            children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n              id: \"rtspUrlInput\",\n              placeholder: \"\\u8BF7\\u8F93\\u5165RTSP\\u5730\\u5740\\uFF0C\\u4F8B\\u5982\\uFF1Artsp://admin:password@192.168.1.100:554/stream1\",\n              autoSize: {\n                minRows: 1,\n                maxRows: 3\n              },\n              defaultValue: \"\",\n              onChange: e => {\n                // 将输入的 RTSP 地址保存到表单\n                form.setFieldsValue({\n                  rtspUrl: e.target.value\n                });\n                console.log('RTSP 地址输入变化:', e.target.value);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"status\",\n            label: \"\\u8BBE\\u5907\\u72B6\\u6001\",\n            rules: [{\n              required: true,\n              message: '请选择设备状态'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u72B6\\u6001\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"online\",\n                children: \"\\u5728\\u7EBF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"offline\",\n                children: \"\\u79BB\\u7EBF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"warning\",\n                children: \"\\u8B66\\u544A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"error\",\n                children: \"\\u9519\\u8BEF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"maintenance\",\n                children: \"\\u7EF4\\u62A4\\u4E2D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"location\",\n            label: \"\\u8BBE\\u5907\\u533A\\u57DF\",\n            rules: [{\n              required: true,\n              message: '请输入设备所在区域'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u6240\\u5728\\u533A\\u57DF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"entrance\",\n            label: \"\\u5B89\\u88C5\\u4F4D\\u7F6E\",\n            rules: [{\n              required: true,\n              message: '请选择安装位置'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u6240\\u5728\\u5B89\\u88C5\\u4F4D\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"ipAddress\",\n            label: \"IP\\u5730\\u5740\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165IP\\u5730\\u5740\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"manufacturer\",\n            label: \"\\u5236\\u9020\\u5546\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u5236\\u9020\\u5546\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"model\",\n            label: \"\\u578B\\u53F7\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u578B\\u53F7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"description\",\n            label: \"\\u8BBE\\u5907\\u63CF\\u8FF0\",\n            children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n              rows: 4,\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u63CF\\u8FF0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        title: \"\\u8BBE\\u5907\\u8BE6\\u60C5\",\n        open: detailModalVisible,\n        onCancel: () => setDetailModalVisible(false),\n        footer: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDetailModalVisible(false),\n          children: \"\\u5173\\u95ED\"\n        }, \"close\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this)],\n        width: 600,\n        children: currentDevice && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BBE\\u5907ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 16\n            }, this), \" \", currentDevice.id]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BBE\\u5907\\u540D\\u79F0:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 16\n            }, this), \" \", currentDevice.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BBE\\u5907\\u7C7B\\u578B:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 16\n            }, this), \" \", deviceTypeMap[currentDevice.type] || currentDevice.type]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u72B6\\u6001:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 16\n            }, this), \" \", renderStatusTag(currentDevice.status)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u4F4D\\u7F6E:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 16\n            }, this), \" \", currentDevice.location]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"IP\\u5730\\u5740:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 16\n            }, this), \" \", currentDevice.ipAddress]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6700\\u540E\\u7EF4\\u62A4\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 16\n            }, this), \" \", currentDevice.lastMaintenance ? new Date(currentDevice.lastMaintenance).toLocaleString() : '无记录']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5B89\\u88C5\\u65E5\\u671F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 16\n            }, this), \" \", currentDevice.installationDate ? new Date(currentDevice.installationDate).toLocaleString() : '无记录']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5236\\u9020\\u5546:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 16\n            }, this), \" \", currentDevice.manufacturer || '未知']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u578B\\u53F7:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 16\n            }, this), \" \", currentDevice.model || '未知']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u63CF\\u8FF0:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 16\n            }, this), \" \", currentDevice.description || '无']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), currentDevice.type === 'camera' && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"RTSP\\u5730\\u5740:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 18\n            }, this), \" \", currentDevice.rtspUrl || '未设置']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this)\n    // </div>\n  );\n};\n_s(DeviceManagement, \"FEvoVtvtZFhj3MG4SXI/8+O84qg=\", false, function () {\n  return [Form.useForm];\n});\n_c2 = DeviceManagement;\nexport default _c3 = /*#__PURE__*/forwardRef(DeviceManagement);\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ContentArea\");\n$RefreshReg$(_c2, \"DeviceManagement\");\n$RefreshReg$(_c3, \"%default%\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "Space", "Card", "Tag", "message", "axios", "jsxDEV", "_jsxDEV", "Option", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ContentArea", "styled", "div", "_c", "DeviceManagement", "id", "ref", "_s", "devices", "setDevices", "loading", "setLoading", "modalVisible", "setModalVisible", "form", "useForm", "editingDevice", "setEditingDevice", "detailModalVisible", "setDetailModalVisible", "currentDevice", "setCurrentDevice", "currentDeviceType", "setCurrentDeviceType", "type", "fetchDevices", "response", "get", "data", "success", "Error", "error", "console", "handleAddDevice", "resetFields", "handleEditDevice", "device", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "status", "location", "ip<PERSON><PERSON><PERSON>", "manufacturer", "model", "description", "rtspUrl", "entrance", "handleViewDevice", "handleDeleteDevice", "deviceId", "delete", "handleModalOk", "values", "validateFields", "log", "rtspInput", "document", "querySelector", "value", "getFieldValue", "JSON", "stringify", "put", "post", "handleDeviceTypeChange", "currentRtspUrl", "renderStatusTag", "statusMap", "online", "color", "text", "offline", "warning", "maintenance", "statusInfo", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "deviceTypeMap", "camera", "mmwave_radar", "lidar", "rsu", "edge_computing", "obu", "columns", "title", "dataIndex", "key", "render", "_", "record", "size", "onClick", "danger", "style", "display", "justifyContent", "marginBottom", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onOk", "onCancel", "width", "layout", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "onChange", "initialValue", "TextArea", "autoSize", "minRows", "maxRows", "defaultValue", "e", "target", "rows", "footer", "lastMaintenance", "Date", "toLocaleString", "installationDate", "_c2", "_c3", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/DeviceManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, Space, Card, Tag, message } from 'antd';\nimport axios from 'axios';\n\nconst { Option } = Select;\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconst ContentArea = styled.div`\n  // padding: 24px;\n  background: white;\n  position: relative;\n  z-index: 1002;\n`;\nconst DeviceManagement = ({ id }, ref) => {\n  const [devices, setDevices] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingDevice, setEditingDevice] = useState(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentDevice, setCurrentDevice] = useState(null);\n  const [currentDeviceType, setCurrentDeviceType] = useState(editingDevice?.type || null);\n\n  // 获取设备列表\n  const fetchDevices = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/api/devices`);\n      if (response.data.success) {\n        setDevices(response.data.data);\n      } else {\n        throw new Error(response.data.message || '获取设备列表失败');\n      }\n    } catch (error) {\n      console.error('获取设备列表失败:', error);\n      message.error('获取设备列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchDevices();\n  }, []);\n\n  // 处理添加设备\n  const handleAddDevice = () => {\n    setEditingDevice(null);\n    setCurrentDeviceType(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // 处理编辑设备\n  const handleEditDevice = (device) => {\n    setEditingDevice(device);\n    setCurrentDeviceType(device.type);\n    form.setFieldsValue({\n      name: device.name,\n      type: device.type,\n      status: device.status,\n      location: device.location,\n      ipAddress: device.ipAddress,\n      manufacturer: device.manufacturer,\n      model: device.model,\n      description: device.description,\n      rtspUrl: device.rtspUrl,\n      entrance: device.entrance\n    });\n    setModalVisible(true);\n  };\n\n  // 处理查看设备详情\n  const handleViewDevice = (device) => {\n    setCurrentDevice(device);\n    setDetailModalVisible(true);\n  };\n\n  // 处理删除设备\n  const handleDeleteDevice = async (deviceId) => {\n    try {\n      const response = await axios.delete(`${API_BASE_URL}/api/devices/${deviceId}`);\n      if (response.data.success) {\n        message.success('设备删除成功');\n        fetchDevices(); // 重新获取设备列表\n      } else {\n        throw new Error(response.data.message || '删除设备失败');\n      }\n    } catch (error) {\n      console.error('删除设备失败:', error);\n      message.error('删除设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 处理表单提交\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      // 为调试添加日志\n      console.log('表单验证通过，原始数据:', values);\n      console.log('当前设备类型:', currentDeviceType);\n      \n      // 确保摄像头设备有 rtspUrl 字段\n      if (values.type === 'camera' || currentDeviceType === 'camera') {\n        // 直接从表单获取 rtspUrl 值，确保它不会丢失\n        const rtspInput = document.querySelector('textarea[placeholder*=\"RTSP\"]');\n        let rtspUrl = '';\n        \n        if (rtspInput) {\n          rtspUrl = rtspInput.value || '';\n          console.log('从DOM元素获取的 RTSP 地址:', rtspUrl);\n        } else {\n          rtspUrl = form.getFieldValue('rtspUrl') || values.rtspUrl || '';\n          console.log('从表单获取的 RTSP 地址:', rtspUrl);\n        }\n        \n        // 无论表单中是否有该字段，都强制设置 rtspUrl\n        values.rtspUrl = rtspUrl;\n        console.log('最终设置的 RTSP 地址:', rtspUrl);\n      } else {\n        // 对于非摄像头设备，确保移除 rtspUrl 字段\n        delete values.rtspUrl;\n        console.log('非摄像头设备，移除 rtspUrl 字段');\n      }\n      \n      console.log('提交表单最终数据:', JSON.stringify(values, null, 2));\n\n      if (editingDevice) {\n        // 编辑设备\n        const response = await axios.put(`${API_BASE_URL}/api/devices/${editingDevice.id}`, values);\n        if (response.data.success) {\n          message.success('设备更新成功');\n          setModalVisible(false);\n          fetchDevices(); // 重新获取设备列表\n        } else {\n          throw new Error(response.data.message || '更新设备失败');\n        }\n      } else {\n        // 添加设备\n        const response = await axios.post(`${API_BASE_URL}/api/devices`, values);\n        if (response.data.success) {\n          message.success('设备添加成功');\n          setModalVisible(false);\n          fetchDevices(); // 重新获取设备列表\n        } else {\n          throw new Error(response.data.message || '添加设备失败');\n        }\n      }\n    } catch (error) {\n      console.error('保存设备失败:', error);\n      message.error('保存设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 修改设备类型选择的处理函数\n  const handleDeviceTypeChange = (value) => {\n    console.log('设备类型变更为:', value);\n    setCurrentDeviceType(value);\n    \n    if (value === 'camera') {\n      // 如果是摄像头，确保表单中有 rtspUrl 字段\n      const currentRtspUrl = form.getFieldValue('rtspUrl') || '';\n      form.setFieldsValue({ \n        type: value,\n        rtspUrl: currentRtspUrl\n      });\n      console.log('设置摄像头 RTSP 初始值:', currentRtspUrl);\n    } else {\n      // 如果不是摄像头，移除 rtspUrl 字段\n      form.setFieldsValue({ type: value });\n      form.resetFields(['rtspUrl']);\n      console.log('重置 RTSP 字段');\n    }\n  };\n\n  // 渲染状态标签\n  const renderStatusTag = (status) => {\n    const statusMap = {\n      online: { color: 'green', text: '在线' },\n      offline: { color: 'gray', text: '离线' },\n      warning: { color: 'orange', text: '警告' },\n      error: { color: 'red', text: '错误' },\n      maintenance: { color: 'blue', text: '维护中' }\n    };\n    \n    const statusInfo = statusMap[status] || { color: 'default', text: status };\n    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;\n  };\n\n  // 设备类型映射\n  const deviceTypeMap = {\n    camera: '摄像头',\n    mmwave_radar: '毫米波雷达',\n    lidar: '激光雷达',\n    rsu: 'RSU',\n    edge_computing: '边缘计算单元',\n    obu: 'OBU'\n  };\n\n  // 表格列定义\n  const columns = [\n    {\n      title: '设备名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '设备类型',\n      dataIndex: 'type',\n      key: 'type',\n      render: (type) => deviceTypeMap[type] || type\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: renderStatusTag\n    },\n    {\n      title: '所在区域',\n      dataIndex: 'location',\n      key: 'location',\n    },\n    {\n      title: 'IP地址',\n      dataIndex: 'ipAddress',\n      key: 'ipAddress',\n    },\n    {\n      title: '安装位置',\n      dataIndex: 'entrance',\n      key: 'entrance',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button type=\"link\" onClick={() => handleViewDevice(record)}>查看</Button>\n          <Button type=\"link\" onClick={() => handleEditDevice(record)}>编辑</Button>\n          <Button type=\"link\" danger onClick={() => handleDeleteDevice(record.id)}>删除</Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 暴露 fetchDevices 方法\n  useImperativeHandle(ref, () => ({\n    fetchDevices\n  }));\n\n  return (\n    // <div id={id}>\n      <ContentArea> \n        {/* // title=\"设备列表\" \n        // extra={<Button type=\"primary\" onClick={handleAddDevice}>添加设备</Button>} */}\n      \n        <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 16 }}>\n        <Button\n          type=\"primary\"\n          // icon={<PlusOutlined />}\n          onClick={handleAddDevice}\n        >\n          添加设备\n        </Button>\n      </div>\n        <Table \n          loading={loading}\n          dataSource={devices} \n          columns={columns} \n          rowKey=\"id\"\n        />\n      \n\n\n      {/* 添加/编辑设备表单 */}\n      <Modal\n        title={editingDevice ? '编辑设备' : '添加设备'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => setModalVisible(false)}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"设备名称\"\n            rules={[{ required: true, message: '请输入设备名称' }]}\n          >\n            <Input placeholder=\"请输入设备名称\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"type\"\n            label=\"设备类型\"\n            rules={[{ required: true, message: '请选择设备类型' }]}\n          >\n            <Select \n              placeholder=\"请选择设备类型\"\n              onChange={handleDeviceTypeChange}\n            >\n              <Option value=\"camera\">摄像头</Option>\n              <Option value=\"mmwave_radar\">毫米波雷达</Option>\n              <Option value=\"lidar\">激光雷达</Option>\n              <Option value=\"rsu\">RSU</Option>\n              <Option value=\"edge_computing\">边缘计算单元</Option>\n              <Option value=\"obu\">OBU</Option>\n            </Select>\n          </Form.Item>\n          \n          {/* 当设备类型为摄像头时显示 RTSP 地址输入 */}\n          {currentDeviceType === 'camera' && (\n            <Form.Item\n              name=\"rtspUrl\"\n              label=\"RTSP地址\"\n              initialValue=\"\"\n            >\n              <Input.TextArea\n                id=\"rtspUrlInput\"\n                placeholder=\"请输入RTSP地址，例如：rtsp://admin:password@192.168.1.100:554/stream1\"\n                autoSize={{ minRows: 1, maxRows: 3 }}\n                defaultValue=\"\"\n                onChange={(e) => {\n                  // 将输入的 RTSP 地址保存到表单\n                  form.setFieldsValue({ rtspUrl: e.target.value });\n                  console.log('RTSP 地址输入变化:', e.target.value);\n                }}\n              />\n            </Form.Item>\n          )}\n          \n          <Form.Item\n            name=\"status\"\n            label=\"设备状态\"\n            rules={[{ required: true, message: '请选择设备状态' }]}\n          >\n            <Select placeholder=\"请选择设备状态\">\n              <Option value=\"online\">在线</Option>\n              <Option value=\"offline\">离线</Option>\n              <Option value=\"warning\">警告</Option>\n              <Option value=\"error\">错误</Option>\n              <Option value=\"maintenance\">维护中</Option>\n            </Select>\n          </Form.Item>\n          \n          <Form.Item\n            name=\"location\"\n            label=\"设备区域\"\n            rules={[{ required: true, message: '请输入设备所在区域' }]}\n          >\n            <Input placeholder=\"请输入设备所在区域\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"entrance\"\n            label=\"安装位置\"\n            rules={[{ required: true, message: '请选择安装位置' }]}\n          >\n            <Input placeholder=\"请输入设备所在安装位置\" />\n            {/* <Select placeholder=\"请选择安装进口\">\n              <Option value=\"东进口\">东进口</Option>\n              <Option value=\"西进口\">西进口</Option>\n              <Option value=\"北进口\">北进口</Option>\n              <Option value=\"南进口\">南进口</Option>\n            </Select> */}\n          </Form.Item>\n\n          <Form.Item\n            name=\"ipAddress\"\n            label=\"IP地址\"\n          >\n            <Input placeholder=\"请输入IP地址\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"manufacturer\"\n            label=\"制造商\"\n          >\n            <Input placeholder=\"请输入制造商\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"model\"\n            label=\"型号\"\n          >\n            <Input placeholder=\"请输入型号\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"description\"\n            label=\"设备描述\"\n          >\n            <Input.TextArea rows={4} placeholder=\"请输入设备描述\" />\n          </Form.Item>\n          \n\n        </Form>\n      </Modal>\n\n      {/* 设备详情模态框 */}\n      <Modal\n        title=\"设备详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={600}\n      >\n        {currentDevice && (\n          <div>\n            <p><strong>设备ID:</strong> {currentDevice.id}</p>\n            <p><strong>设备名称:</strong> {currentDevice.name}</p>\n            <p><strong>设备类型:</strong> {deviceTypeMap[currentDevice.type] || currentDevice.type}</p>\n            <p><strong>状态:</strong> {renderStatusTag(currentDevice.status)}</p>\n            <p><strong>位置:</strong> {currentDevice.location}</p>\n            <p><strong>IP地址:</strong> {currentDevice.ipAddress}</p>\n            <p><strong>最后维护时间:</strong> {currentDevice.lastMaintenance ? new Date(currentDevice.lastMaintenance).toLocaleString() : '无记录'}</p>\n            <p><strong>安装日期:</strong> {currentDevice.installationDate ? new Date(currentDevice.installationDate).toLocaleString() : '无记录'}</p>\n            <p><strong>制造商:</strong> {currentDevice.manufacturer || '未知'}</p>\n            <p><strong>型号:</strong> {currentDevice.model || '未知'}</p>\n            <p><strong>描述:</strong> {currentDevice.description || '无'}</p>\n            {currentDevice.type === 'camera' && (\n              <p><strong>RTSP地址:</strong> {currentDevice.rtspUrl || '未设置'}</p>\n            )}\n          </div>\n        )}\n      </Modal>\n      </ContentArea>\n    // </div>\n  );\n};\n\nexport default forwardRef(DeviceManagement); "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACnF,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,OAAO,QAAQ,MAAM;AAC3F,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC;AAAO,CAAC,GAAGR,MAAM;AACzB,MAAMS,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAC7E,MAAMC,WAAW,GAAGC,MAAM,CAACC,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIH,WAAW;AAMjB,MAAMI,gBAAgB,GAAGA,CAAC;EAAEC;AAAG,CAAC,EAAEC,GAAG,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoC,IAAI,CAAC,GAAG7B,IAAI,CAAC8B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACwC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC4C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,CAAAsC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEQ,IAAI,KAAI,IAAI,CAAC;;EAEvF;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFd,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMe,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,GAAG,CAAC,GAAG/B,YAAY,cAAc,CAAC;MAC/D,IAAI8B,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBpB,UAAU,CAACiB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MAChC,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAACrC,OAAO,IAAI,UAAU,CAAC;MACtD;IACF,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCxC,OAAO,CAACwC,KAAK,CAAC,YAAY,IAAIA,KAAK,CAACxC,OAAO,IAAI,MAAM,CAAC,CAAC;IACzD,CAAC,SAAS;MACRoB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDhC,SAAS,CAAC,MAAM;IACd8C,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMQ,eAAe,GAAGA,CAAA,KAAM;IAC5BhB,gBAAgB,CAAC,IAAI,CAAC;IACtBM,oBAAoB,CAAC,IAAI,CAAC;IAC1BT,IAAI,CAACoB,WAAW,CAAC,CAAC;IAClBrB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsB,gBAAgB,GAAIC,MAAM,IAAK;IACnCnB,gBAAgB,CAACmB,MAAM,CAAC;IACxBb,oBAAoB,CAACa,MAAM,CAACZ,IAAI,CAAC;IACjCV,IAAI,CAACuB,cAAc,CAAC;MAClBC,IAAI,EAAEF,MAAM,CAACE,IAAI;MACjBd,IAAI,EAAEY,MAAM,CAACZ,IAAI;MACjBe,MAAM,EAAEH,MAAM,CAACG,MAAM;MACrBC,QAAQ,EAAEJ,MAAM,CAACI,QAAQ;MACzBC,SAAS,EAAEL,MAAM,CAACK,SAAS;MAC3BC,YAAY,EAAEN,MAAM,CAACM,YAAY;MACjCC,KAAK,EAAEP,MAAM,CAACO,KAAK;MACnBC,WAAW,EAAER,MAAM,CAACQ,WAAW;MAC/BC,OAAO,EAAET,MAAM,CAACS,OAAO;MACvBC,QAAQ,EAAEV,MAAM,CAACU;IACnB,CAAC,CAAC;IACFjC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMkC,gBAAgB,GAAIX,MAAM,IAAK;IACnCf,gBAAgB,CAACe,MAAM,CAAC;IACxBjB,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM6B,kBAAkB,GAAG,MAAOC,QAAQ,IAAK;IAC7C,IAAI;MACF,MAAMvB,QAAQ,GAAG,MAAMlC,KAAK,CAAC0D,MAAM,CAAC,GAAGtD,YAAY,gBAAgBqD,QAAQ,EAAE,CAAC;MAC9E,IAAIvB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBtC,OAAO,CAACsC,OAAO,CAAC,QAAQ,CAAC;QACzBJ,YAAY,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,MAAM;QACL,MAAM,IAAIK,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAACrC,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BxC,OAAO,CAACwC,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACxC,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAM4D,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMtC,IAAI,CAACuC,cAAc,CAAC,CAAC;;MAE1C;MACArB,OAAO,CAACsB,GAAG,CAAC,cAAc,EAAEF,MAAM,CAAC;MACnCpB,OAAO,CAACsB,GAAG,CAAC,SAAS,EAAEhC,iBAAiB,CAAC;;MAEzC;MACA,IAAI8B,MAAM,CAAC5B,IAAI,KAAK,QAAQ,IAAIF,iBAAiB,KAAK,QAAQ,EAAE;QAC9D;QACA,MAAMiC,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,+BAA+B,CAAC;QACzE,IAAIZ,OAAO,GAAG,EAAE;QAEhB,IAAIU,SAAS,EAAE;UACbV,OAAO,GAAGU,SAAS,CAACG,KAAK,IAAI,EAAE;UAC/B1B,OAAO,CAACsB,GAAG,CAAC,oBAAoB,EAAET,OAAO,CAAC;QAC5C,CAAC,MAAM;UACLA,OAAO,GAAG/B,IAAI,CAAC6C,aAAa,CAAC,SAAS,CAAC,IAAIP,MAAM,CAACP,OAAO,IAAI,EAAE;UAC/Db,OAAO,CAACsB,GAAG,CAAC,iBAAiB,EAAET,OAAO,CAAC;QACzC;;QAEA;QACAO,MAAM,CAACP,OAAO,GAAGA,OAAO;QACxBb,OAAO,CAACsB,GAAG,CAAC,gBAAgB,EAAET,OAAO,CAAC;MACxC,CAAC,MAAM;QACL;QACA,OAAOO,MAAM,CAACP,OAAO;QACrBb,OAAO,CAACsB,GAAG,CAAC,sBAAsB,CAAC;MACrC;MAEAtB,OAAO,CAACsB,GAAG,CAAC,WAAW,EAAEM,IAAI,CAACC,SAAS,CAACT,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAEzD,IAAIpC,aAAa,EAAE;QACjB;QACA,MAAMU,QAAQ,GAAG,MAAMlC,KAAK,CAACsE,GAAG,CAAC,GAAGlE,YAAY,gBAAgBoB,aAAa,CAACX,EAAE,EAAE,EAAE+C,MAAM,CAAC;QAC3F,IAAI1B,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzBtC,OAAO,CAACsC,OAAO,CAAC,QAAQ,CAAC;UACzBhB,eAAe,CAAC,KAAK,CAAC;UACtBY,YAAY,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,MAAM;UACL,MAAM,IAAIK,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAACrC,OAAO,IAAI,QAAQ,CAAC;QACpD;MACF,CAAC,MAAM;QACL;QACA,MAAMmC,QAAQ,GAAG,MAAMlC,KAAK,CAACuE,IAAI,CAAC,GAAGnE,YAAY,cAAc,EAAEwD,MAAM,CAAC;QACxE,IAAI1B,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzBtC,OAAO,CAACsC,OAAO,CAAC,QAAQ,CAAC;UACzBhB,eAAe,CAAC,KAAK,CAAC;UACtBY,YAAY,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,MAAM;UACL,MAAM,IAAIK,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAACrC,OAAO,IAAI,QAAQ,CAAC;QACpD;MACF;IACF,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BxC,OAAO,CAACwC,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACxC,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMyE,sBAAsB,GAAIN,KAAK,IAAK;IACxC1B,OAAO,CAACsB,GAAG,CAAC,UAAU,EAAEI,KAAK,CAAC;IAC9BnC,oBAAoB,CAACmC,KAAK,CAAC;IAE3B,IAAIA,KAAK,KAAK,QAAQ,EAAE;MACtB;MACA,MAAMO,cAAc,GAAGnD,IAAI,CAAC6C,aAAa,CAAC,SAAS,CAAC,IAAI,EAAE;MAC1D7C,IAAI,CAACuB,cAAc,CAAC;QAClBb,IAAI,EAAEkC,KAAK;QACXb,OAAO,EAAEoB;MACX,CAAC,CAAC;MACFjC,OAAO,CAACsB,GAAG,CAAC,iBAAiB,EAAEW,cAAc,CAAC;IAChD,CAAC,MAAM;MACL;MACAnD,IAAI,CAACuB,cAAc,CAAC;QAAEb,IAAI,EAAEkC;MAAM,CAAC,CAAC;MACpC5C,IAAI,CAACoB,WAAW,CAAC,CAAC,SAAS,CAAC,CAAC;MAC7BF,OAAO,CAACsB,GAAG,CAAC,YAAY,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMY,eAAe,GAAI3B,MAAM,IAAK;IAClC,MAAM4B,SAAS,GAAG;MAChBC,MAAM,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAK,CAAC;MACtCC,OAAO,EAAE;QAAEF,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAK,CAAC;MACtCE,OAAO,EAAE;QAAEH,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAK,CAAC;MACxCvC,KAAK,EAAE;QAAEsC,KAAK,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAK,CAAC;MACnCG,WAAW,EAAE;QAAEJ,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM;IAC5C,CAAC;IAED,MAAMI,UAAU,GAAGP,SAAS,CAAC5B,MAAM,CAAC,IAAI;MAAE8B,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE/B;IAAO,CAAC;IAC1E,oBAAO7C,OAAA,CAACJ,GAAG;MAAC+E,KAAK,EAAEK,UAAU,CAACL,KAAM;MAAAM,QAAA,EAAED,UAAU,CAACJ;IAAI;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG;IACpBC,MAAM,EAAE,KAAK;IACbC,YAAY,EAAE,OAAO;IACrBC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,KAAK;IACVC,cAAc,EAAE,QAAQ;IACxBC,GAAG,EAAE;EACP,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGnE,IAAI,IAAKwD,aAAa,CAACxD,IAAI,CAAC,IAAIA;EAC3C,CAAC,EACD;IACEgE,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEzB;EACV,CAAC,EACD;IACEsB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAM,kBAChBnG,OAAA,CAACN,KAAK;MAAC0G,IAAI,EAAC,OAAO;MAAAnB,QAAA,gBACjBjF,OAAA,CAACX,MAAM;QAACyC,IAAI,EAAC,MAAM;QAACuE,OAAO,EAAEA,CAAA,KAAMhD,gBAAgB,CAAC8C,MAAM,CAAE;QAAAlB,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxErF,OAAA,CAACX,MAAM;QAACyC,IAAI,EAAC,MAAM;QAACuE,OAAO,EAAEA,CAAA,KAAM5D,gBAAgB,CAAC0D,MAAM,CAAE;QAAAlB,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxErF,OAAA,CAACX,MAAM;QAACyC,IAAI,EAAC,MAAM;QAACwE,MAAM;QAACD,OAAO,EAAEA,CAAA,KAAM/C,kBAAkB,CAAC6C,MAAM,CAACxF,EAAE,CAAE;QAAAsE,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E;EAEX,CAAC,CACF;;EAED;EACAlG,mBAAmB,CAACyB,GAAG,EAAE,OAAO;IAC9BmB;EACF,CAAC,CAAC,CAAC;EAEH;IAAA;IACE;IACE/B,OAAA,CAACM,WAAW;MAAA2E,QAAA,gBAIVjF,OAAA;QAAKuG,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAzB,QAAA,eAC9EjF,OAAA,CAACX,MAAM;UACLyC,IAAI,EAAC;UACL;UAAA;UACAuE,OAAO,EAAE9D,eAAgB;UAAA0C,QAAA,EAC1B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACJrF,OAAA,CAACZ,KAAK;QACJ4B,OAAO,EAAEA,OAAQ;QACjB2F,UAAU,EAAE7F,OAAQ;QACpB+E,OAAO,EAAEA,OAAQ;QACjBe,MAAM,EAAC;MAAI;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAKJrF,OAAA,CAACV,KAAK;QACJwG,KAAK,EAAExE,aAAa,GAAG,MAAM,GAAG,MAAO;QACvCuF,IAAI,EAAE3F,YAAa;QACnB4F,IAAI,EAAErD,aAAc;QACpBsD,QAAQ,EAAEA,CAAA,KAAM5F,eAAe,CAAC,KAAK,CAAE;QACvC6F,KAAK,EAAE,GAAI;QAAA/B,QAAA,eAEXjF,OAAA,CAACT,IAAI;UACH6B,IAAI,EAAEA,IAAK;UACX6F,MAAM,EAAC,UAAU;UAAAhC,QAAA,gBAEjBjF,OAAA,CAACT,IAAI,CAAC2H,IAAI;YACRtE,IAAI,EAAC,MAAM;YACXuE,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAExH,OAAO,EAAE;YAAU,CAAC,CAAE;YAAAoF,QAAA,eAEhDjF,OAAA,CAACR,KAAK;cAAC8H,WAAW,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAEZrF,OAAA,CAACT,IAAI,CAAC2H,IAAI;YACRtE,IAAI,EAAC,MAAM;YACXuE,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAExH,OAAO,EAAE;YAAU,CAAC,CAAE;YAAAoF,QAAA,eAEhDjF,OAAA,CAACP,MAAM;cACL6H,WAAW,EAAC,4CAAS;cACrBC,QAAQ,EAAEjD,sBAAuB;cAAAW,QAAA,gBAEjCjF,OAAA,CAACC,MAAM;gBAAC+D,KAAK,EAAC,QAAQ;gBAAAiB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCrF,OAAA,CAACC,MAAM;gBAAC+D,KAAK,EAAC,cAAc;gBAAAiB,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3CrF,OAAA,CAACC,MAAM;gBAAC+D,KAAK,EAAC,OAAO;gBAAAiB,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCrF,OAAA,CAACC,MAAM;gBAAC+D,KAAK,EAAC,KAAK;gBAAAiB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCrF,OAAA,CAACC,MAAM;gBAAC+D,KAAK,EAAC,gBAAgB;gBAAAiB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9CrF,OAAA,CAACC,MAAM;gBAAC+D,KAAK,EAAC,KAAK;gBAAAiB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EAGXzD,iBAAiB,KAAK,QAAQ,iBAC7B5B,OAAA,CAACT,IAAI,CAAC2H,IAAI;YACRtE,IAAI,EAAC,SAAS;YACduE,KAAK,EAAC,kBAAQ;YACdK,YAAY,EAAC,EAAE;YAAAvC,QAAA,eAEfjF,OAAA,CAACR,KAAK,CAACiI,QAAQ;cACb9G,EAAE,EAAC,cAAc;cACjB2G,WAAW,EAAC,2GAA8D;cAC1EI,QAAQ,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,OAAO,EAAE;cAAE,CAAE;cACrCC,YAAY,EAAC,EAAE;cACfN,QAAQ,EAAGO,CAAC,IAAK;gBACf;gBACA1G,IAAI,CAACuB,cAAc,CAAC;kBAAEQ,OAAO,EAAE2E,CAAC,CAACC,MAAM,CAAC/D;gBAAM,CAAC,CAAC;gBAChD1B,OAAO,CAACsB,GAAG,CAAC,cAAc,EAAEkE,CAAC,CAACC,MAAM,CAAC/D,KAAK,CAAC;cAC7C;YAAE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CACZ,eAEDrF,OAAA,CAACT,IAAI,CAAC2H,IAAI;YACRtE,IAAI,EAAC,QAAQ;YACbuE,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAExH,OAAO,EAAE;YAAU,CAAC,CAAE;YAAAoF,QAAA,eAEhDjF,OAAA,CAACP,MAAM;cAAC6H,WAAW,EAAC,4CAAS;cAAArC,QAAA,gBAC3BjF,OAAA,CAACC,MAAM;gBAAC+D,KAAK,EAAC,QAAQ;gBAAAiB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCrF,OAAA,CAACC,MAAM;gBAAC+D,KAAK,EAAC,SAAS;gBAAAiB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCrF,OAAA,CAACC,MAAM;gBAAC+D,KAAK,EAAC,SAAS;gBAAAiB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCrF,OAAA,CAACC,MAAM;gBAAC+D,KAAK,EAAC,OAAO;gBAAAiB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACjCrF,OAAA,CAACC,MAAM;gBAAC+D,KAAK,EAAC,aAAa;gBAAAiB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZrF,OAAA,CAACT,IAAI,CAAC2H,IAAI;YACRtE,IAAI,EAAC,UAAU;YACfuE,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAExH,OAAO,EAAE;YAAY,CAAC,CAAE;YAAAoF,QAAA,eAElDjF,OAAA,CAACR,KAAK;cAAC8H,WAAW,EAAC;YAAW;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eAEZrF,OAAA,CAACT,IAAI,CAAC2H,IAAI;YACRtE,IAAI,EAAC,UAAU;YACfuE,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAExH,OAAO,EAAE;YAAU,CAAC,CAAE;YAAAoF,QAAA,eAEhDjF,OAAA,CAACR,KAAK;cAAC8H,WAAW,EAAC;YAAa;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAO1B,CAAC,eAEZrF,OAAA,CAACT,IAAI,CAAC2H,IAAI;YACRtE,IAAI,EAAC,WAAW;YAChBuE,KAAK,EAAC,gBAAM;YAAAlC,QAAA,eAEZjF,OAAA,CAACR,KAAK;cAAC8H,WAAW,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAEZrF,OAAA,CAACT,IAAI,CAAC2H,IAAI;YACRtE,IAAI,EAAC,cAAc;YACnBuE,KAAK,EAAC,oBAAK;YAAAlC,QAAA,eAEXjF,OAAA,CAACR,KAAK;cAAC8H,WAAW,EAAC;YAAQ;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAEZrF,OAAA,CAACT,IAAI,CAAC2H,IAAI;YACRtE,IAAI,EAAC,OAAO;YACZuE,KAAK,EAAC,cAAI;YAAAlC,QAAA,eAEVjF,OAAA,CAACR,KAAK;cAAC8H,WAAW,EAAC;YAAO;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAEZrF,OAAA,CAACT,IAAI,CAAC2H,IAAI;YACRtE,IAAI,EAAC,aAAa;YAClBuE,KAAK,EAAC,0BAAM;YAAAlC,QAAA,eAEZjF,OAAA,CAACR,KAAK,CAACiI,QAAQ;cAACO,IAAI,EAAE,CAAE;cAACV,WAAW,EAAC;YAAS;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGRrF,OAAA,CAACV,KAAK;QACJwG,KAAK,EAAC,0BAAM;QACZe,IAAI,EAAErF,kBAAmB;QACzBuF,QAAQ,EAAEA,CAAA,KAAMtF,qBAAqB,CAAC,KAAK,CAAE;QAC7CwG,MAAM,EAAE,cACNjI,OAAA,CAACX,MAAM;UAAagH,OAAO,EAAEA,CAAA,KAAM5E,qBAAqB,CAAC,KAAK,CAAE;UAAAwD,QAAA,EAAC;QAEjE,GAFY,OAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEX,CAAC,CACT;QACF2B,KAAK,EAAE,GAAI;QAAA/B,QAAA,EAEVvD,aAAa,iBACZ1B,OAAA;UAAAiF,QAAA,gBACEjF,OAAA;YAAAiF,QAAA,gBAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3D,aAAa,CAACf,EAAE;UAAA;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDrF,OAAA;YAAAiF,QAAA,gBAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3D,aAAa,CAACkB,IAAI;UAAA;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClDrF,OAAA;YAAAiF,QAAA,gBAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACC,aAAa,CAAC5D,aAAa,CAACI,IAAI,CAAC,IAAIJ,aAAa,CAACI,IAAI;UAAA;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvFrF,OAAA;YAAAiF,QAAA,gBAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACb,eAAe,CAAC9C,aAAa,CAACmB,MAAM,CAAC;UAAA;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnErF,OAAA;YAAAiF,QAAA,gBAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3D,aAAa,CAACoB,QAAQ;UAAA;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpDrF,OAAA;YAAAiF,QAAA,gBAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3D,aAAa,CAACqB,SAAS;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvDrF,OAAA;YAAAiF,QAAA,gBAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3D,aAAa,CAACwG,eAAe,GAAG,IAAIC,IAAI,CAACzG,aAAa,CAACwG,eAAe,CAAC,CAACE,cAAc,CAAC,CAAC,GAAG,KAAK;UAAA;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClIrF,OAAA;YAAAiF,QAAA,gBAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3D,aAAa,CAAC2G,gBAAgB,GAAG,IAAIF,IAAI,CAACzG,aAAa,CAAC2G,gBAAgB,CAAC,CAACD,cAAc,CAAC,CAAC,GAAG,KAAK;UAAA;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClIrF,OAAA;YAAAiF,QAAA,gBAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3D,aAAa,CAACsB,YAAY,IAAI,IAAI;UAAA;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjErF,OAAA;YAAAiF,QAAA,gBAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3D,aAAa,CAACuB,KAAK,IAAI,IAAI;UAAA;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzDrF,OAAA;YAAAiF,QAAA,gBAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3D,aAAa,CAACwB,WAAW,IAAI,GAAG;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC7D3D,aAAa,CAACI,IAAI,KAAK,QAAQ,iBAC9B9B,OAAA;YAAAiF,QAAA,gBAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC3D,aAAa,CAACyB,OAAO,IAAI,KAAK;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAChE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;IACf;EAAA;AAEJ,CAAC;AAACxE,EAAA,CAxaIH,gBAAgB;EAAA,QAILnB,IAAI,CAAC8B,OAAO;AAAA;AAAAiH,GAAA,GAJvB5H,gBAAgB;AA0atB,eAAA6H,GAAA,gBAAerJ,UAAU,CAACwB,gBAAgB,CAAC;AAAC,IAAAD,EAAA,EAAA6H,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAA/H,EAAA;AAAA+H,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}