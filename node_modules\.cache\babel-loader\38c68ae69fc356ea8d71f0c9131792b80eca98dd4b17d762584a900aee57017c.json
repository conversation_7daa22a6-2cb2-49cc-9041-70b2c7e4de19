{"ast": null, "code": "import React,{useState,useEffect,forwardRef,useImperativeHandle}from'react';import{Table,Button,Modal,Form,Input,Select,Space,Card,Tag,message}from'antd';import axios from'axios';import styled from'styled-components';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Option}=Select;const API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:5000';const ContentArea=styled.div`\n  // padding: 24px;\n  background: white;\n  position: relative;\n  z-index: 1002;\n`;const DeviceManagement=(_ref,ref)=>{let{id}=_ref;const[devices,setDevices]=useState([]);const[loading,setLoading]=useState(true);const[modalVisible,setModalVisible]=useState(false);const[form]=Form.useForm();const[editingDevice,setEditingDevice]=useState(null);const[detailModalVisible,setDetailModalVisible]=useState(false);const[currentDevice,setCurrentDevice]=useState(null);const[currentDeviceType,setCurrentDeviceType]=useState((editingDevice===null||editingDevice===void 0?void 0:editingDevice.type)||null);// 获取设备列表\nconst fetchDevices=async()=>{try{setLoading(true);const response=await axios.get(`${API_BASE_URL}/api/devices`);if(response.data.success){setDevices(response.data.data);}else{throw new Error(response.data.message||'获取设备列表失败');}}catch(error){console.error('获取设备列表失败:',error);message.error('获取设备列表失败: '+(error.message||'未知错误'));}finally{setLoading(false);}};useEffect(()=>{fetchDevices();},[]);// 处理添加设备\nconst handleAddDevice=()=>{setEditingDevice(null);setCurrentDeviceType(null);form.resetFields();setModalVisible(true);};// 处理编辑设备\nconst handleEditDevice=device=>{setEditingDevice(device);setCurrentDeviceType(device.type);form.setFieldsValue({name:device.name,type:device.type,status:device.status,location:device.location,ipAddress:device.ipAddress,manufacturer:device.manufacturer,model:device.model,description:device.description,rtspUrl:device.rtspUrl,entrance:device.entrance});setModalVisible(true);};// 处理查看设备详情\nconst handleViewDevice=device=>{setCurrentDevice(device);setDetailModalVisible(true);};// 处理删除设备\nconst handleDeleteDevice=async deviceId=>{try{const response=await axios.delete(`${API_BASE_URL}/api/devices/${deviceId}`);if(response.data.success){message.success('设备删除成功');fetchDevices();// 重新获取设备列表\n}else{throw new Error(response.data.message||'删除设备失败');}}catch(error){console.error('删除设备失败:',error);message.error('删除设备失败: '+(error.message||'未知错误'));}};// 处理表单提交\nconst handleModalOk=async()=>{try{const values=await form.validateFields();// 为调试添加日志\nconsole.log('表单验证通过，原始数据:',values);console.log('当前设备类型:',currentDeviceType);// 确保摄像头设备有 rtspUrl 字段\nif(values.type==='camera'||currentDeviceType==='camera'){// 直接从表单获取 rtspUrl 值，确保它不会丢失\nconst rtspInput=document.querySelector('textarea[placeholder*=\"RTSP\"]');let rtspUrl='';if(rtspInput){rtspUrl=rtspInput.value||'';console.log('从DOM元素获取的 RTSP 地址:',rtspUrl);}else{rtspUrl=form.getFieldValue('rtspUrl')||values.rtspUrl||'';console.log('从表单获取的 RTSP 地址:',rtspUrl);}// 无论表单中是否有该字段，都强制设置 rtspUrl\nvalues.rtspUrl=rtspUrl;console.log('最终设置的 RTSP 地址:',rtspUrl);}else{// 对于非摄像头设备，确保移除 rtspUrl 字段\ndelete values.rtspUrl;console.log('非摄像头设备，移除 rtspUrl 字段');}console.log('提交表单最终数据:',JSON.stringify(values,null,2));if(editingDevice){// 编辑设备\nconst response=await axios.put(`${API_BASE_URL}/api/devices/${editingDevice.id}`,values);if(response.data.success){message.success('设备更新成功');setModalVisible(false);fetchDevices();// 重新获取设备列表\n}else{throw new Error(response.data.message||'更新设备失败');}}else{// 添加设备\nconst response=await axios.post(`${API_BASE_URL}/api/devices`,values);if(response.data.success){message.success('设备添加成功');setModalVisible(false);fetchDevices();// 重新获取设备列表\n}else{throw new Error(response.data.message||'添加设备失败');}}}catch(error){console.error('保存设备失败:',error);message.error('保存设备失败: '+(error.message||'未知错误'));}};// 修改设备类型选择的处理函数\nconst handleDeviceTypeChange=value=>{console.log('设备类型变更为:',value);setCurrentDeviceType(value);if(value==='camera'){// 如果是摄像头，确保表单中有 rtspUrl 字段\nconst currentRtspUrl=form.getFieldValue('rtspUrl')||'';form.setFieldsValue({type:value,rtspUrl:currentRtspUrl});console.log('设置摄像头 RTSP 初始值:',currentRtspUrl);}else{// 如果不是摄像头，移除 rtspUrl 字段\nform.setFieldsValue({type:value});form.resetFields(['rtspUrl']);console.log('重置 RTSP 字段');}};// 渲染状态标签\nconst renderStatusTag=status=>{const statusMap={online:{color:'green',text:'在线'},offline:{color:'gray',text:'离线'},warning:{color:'orange',text:'警告'},error:{color:'red',text:'错误'},maintenance:{color:'blue',text:'维护中'}};const statusInfo=statusMap[status]||{color:'default',text:status};return/*#__PURE__*/_jsx(Tag,{color:statusInfo.color,children:statusInfo.text});};// 设备类型映射\nconst deviceTypeMap={camera:'摄像头',mmwave_radar:'毫米波雷达',lidar:'激光雷达',rsu:'RSU',edge_computing:'边缘计算单元',obu:'OBU'};// 表格列定义\nconst columns=[{title:'设备名称',dataIndex:'name',key:'name'},{title:'设备类型',dataIndex:'type',key:'type',render:type=>deviceTypeMap[type]||type},{title:'状态',dataIndex:'status',key:'status',render:renderStatusTag},{title:'所在区域',dataIndex:'location',key:'location'},{title:'IP地址',dataIndex:'ipAddress',key:'ipAddress'},{title:'安装位置',dataIndex:'entrance',key:'entrance'},{title:'操作',key:'action',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{size:\"small\",children:[/*#__PURE__*/_jsx(Button,{type:\"link\",onClick:()=>handleViewDevice(record),children:\"\\u67E5\\u770B\"}),/*#__PURE__*/_jsx(Button,{type:\"link\",onClick:()=>handleEditDevice(record),children:\"\\u7F16\\u8F91\"}),/*#__PURE__*/_jsx(Button,{type:\"link\",danger:true,onClick:()=>handleDeleteDevice(record.id),children:\"\\u5220\\u9664\"})]})}];// 暴露 fetchDevices 方法\nuseImperativeHandle(ref,()=>({fetchDevices}));return(/*#__PURE__*/// <div id={id}>\n_jsxs(ContentArea,{children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',justifyContent:'flex-end',marginBottom:16},children:/*#__PURE__*/_jsx(Button,{type:\"primary\",onClick:handleAddDevice,children:\"\\u6DFB\\u52A0\\u8BBE\\u5907\"})}),/*#__PURE__*/_jsx(Table,{loading:loading,dataSource:devices,columns:columns,rowKey:\"id\"}),/*#__PURE__*/_jsx(Modal,{title:editingDevice?'编辑设备':'添加设备',open:modalVisible,onOk:handleModalOk,onCancel:()=>setModalVisible(false),width:600,children:/*#__PURE__*/_jsxs(Form,{form:form,layout:\"vertical\",children:[/*#__PURE__*/_jsx(Form.Item,{name:\"name\",label:\"\\u8BBE\\u5907\\u540D\\u79F0\",rules:[{required:true,message:'请输入设备名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u540D\\u79F0\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"type\",label:\"\\u8BBE\\u5907\\u7C7B\\u578B\",rules:[{required:true,message:'请选择设备类型'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u7C7B\\u578B\",onChange:handleDeviceTypeChange,children:[/*#__PURE__*/_jsx(Option,{value:\"camera\",children:\"\\u6444\\u50CF\\u5934\"}),/*#__PURE__*/_jsx(Option,{value:\"mmwave_radar\",children:\"\\u6BEB\\u7C73\\u6CE2\\u96F7\\u8FBE\"}),/*#__PURE__*/_jsx(Option,{value:\"lidar\",children:\"\\u6FC0\\u5149\\u96F7\\u8FBE\"}),/*#__PURE__*/_jsx(Option,{value:\"rsu\",children:\"RSU\"}),/*#__PURE__*/_jsx(Option,{value:\"edge_computing\",children:\"\\u8FB9\\u7F18\\u8BA1\\u7B97\\u5355\\u5143\"}),/*#__PURE__*/_jsx(Option,{value:\"obu\",children:\"OBU\"})]})}),currentDeviceType==='camera'&&/*#__PURE__*/_jsx(Form.Item,{name:\"rtspUrl\",label:\"RTSP\\u5730\\u5740\",initialValue:\"\",children:/*#__PURE__*/_jsx(Input.TextArea,{id:\"rtspUrlInput\",placeholder:\"\\u8BF7\\u8F93\\u5165RTSP\\u5730\\u5740\\uFF0C\\u4F8B\\u5982\\uFF1Artsp://admin:password@192.168.1.100:554/stream1\",autoSize:{minRows:1,maxRows:3},defaultValue:\"\",onChange:e=>{// 将输入的 RTSP 地址保存到表单\nform.setFieldsValue({rtspUrl:e.target.value});console.log('RTSP 地址输入变化:',e.target.value);}})}),/*#__PURE__*/_jsx(Form.Item,{name:\"status\",label:\"\\u8BBE\\u5907\\u72B6\\u6001\",rules:[{required:true,message:'请选择设备状态'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u72B6\\u6001\",children:[/*#__PURE__*/_jsx(Option,{value:\"online\",children:\"\\u5728\\u7EBF\"}),/*#__PURE__*/_jsx(Option,{value:\"offline\",children:\"\\u79BB\\u7EBF\"}),/*#__PURE__*/_jsx(Option,{value:\"warning\",children:\"\\u8B66\\u544A\"}),/*#__PURE__*/_jsx(Option,{value:\"error\",children:\"\\u9519\\u8BEF\"}),/*#__PURE__*/_jsx(Option,{value:\"maintenance\",children:\"\\u7EF4\\u62A4\\u4E2D\"})]})}),/*#__PURE__*/_jsx(Form.Item,{name:\"location\",label:\"\\u8BBE\\u5907\\u533A\\u57DF\",rules:[{required:true,message:'请输入设备所在区域'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u6240\\u5728\\u533A\\u57DF\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"entrance\",label:\"\\u5B89\\u88C5\\u4F4D\\u7F6E\",rules:[{required:true,message:'请选择安装位置'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u6240\\u5728\\u5B89\\u88C5\\u4F4D\\u7F6E\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"ipAddress\",label:\"IP\\u5730\\u5740\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165IP\\u5730\\u5740\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"manufacturer\",label:\"\\u5236\\u9020\\u5546\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5236\\u9020\\u5546\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"model\",label:\"\\u578B\\u53F7\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u578B\\u53F7\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"description\",label:\"\\u8BBE\\u5907\\u63CF\\u8FF0\",children:/*#__PURE__*/_jsx(Input.TextArea,{rows:4,placeholder:\"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u63CF\\u8FF0\"})})]})}),/*#__PURE__*/_jsx(Modal,{title:\"\\u8BBE\\u5907\\u8BE6\\u60C5\",open:detailModalVisible,onCancel:()=>setDetailModalVisible(false),footer:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDetailModalVisible(false),children:\"\\u5173\\u95ED\"},\"close\")],width:600,children:currentDevice&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u8BBE\\u5907ID:\"}),\" \",currentDevice.id]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u8BBE\\u5907\\u540D\\u79F0:\"}),\" \",currentDevice.name]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u8BBE\\u5907\\u7C7B\\u578B:\"}),\" \",deviceTypeMap[currentDevice.type]||currentDevice.type]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u72B6\\u6001:\"}),\" \",renderStatusTag(currentDevice.status)]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u4F4D\\u7F6E:\"}),\" \",currentDevice.location]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"IP\\u5730\\u5740:\"}),\" \",currentDevice.ipAddress]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6700\\u540E\\u7EF4\\u62A4\\u65F6\\u95F4:\"}),\" \",currentDevice.lastMaintenance?new Date(currentDevice.lastMaintenance).toLocaleString():'无记录']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5B89\\u88C5\\u65E5\\u671F:\"}),\" \",currentDevice.installationDate?new Date(currentDevice.installationDate).toLocaleString():'无记录']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5236\\u9020\\u5546:\"}),\" \",currentDevice.manufacturer||'未知']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u578B\\u53F7:\"}),\" \",currentDevice.model||'未知']}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u63CF\\u8FF0:\"}),\" \",currentDevice.description||'无']}),currentDevice.type==='camera'&&/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"RTSP\\u5730\\u5740:\"}),\" \",currentDevice.rtspUrl||'未设置']})]})})]})// </div>\n);};export default/*#__PURE__*/forwardRef(DeviceManagement);", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "Space", "Card", "Tag", "message", "axios", "styled", "jsx", "_jsx", "jsxs", "_jsxs", "Option", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ContentArea", "div", "DeviceManagement", "_ref", "ref", "id", "devices", "setDevices", "loading", "setLoading", "modalVisible", "setModalVisible", "form", "useForm", "editingDevice", "setEditingDevice", "detailModalVisible", "setDetailModalVisible", "currentDevice", "setCurrentDevice", "currentDeviceType", "setCurrentDeviceType", "type", "fetchDevices", "response", "get", "data", "success", "Error", "error", "console", "handleAddDevice", "resetFields", "handleEditDevice", "device", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "status", "location", "ip<PERSON><PERSON><PERSON>", "manufacturer", "model", "description", "rtspUrl", "entrance", "handleViewDevice", "handleDeleteDevice", "deviceId", "delete", "handleModalOk", "values", "validateFields", "log", "rtspInput", "document", "querySelector", "value", "getFieldValue", "JSON", "stringify", "put", "post", "handleDeviceTypeChange", "currentRtspUrl", "renderStatusTag", "statusMap", "online", "color", "text", "offline", "warning", "maintenance", "statusInfo", "children", "deviceTypeMap", "camera", "mmwave_radar", "lidar", "rsu", "edge_computing", "obu", "columns", "title", "dataIndex", "key", "render", "_", "record", "size", "onClick", "danger", "style", "display", "justifyContent", "marginBottom", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onOk", "onCancel", "width", "layout", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "onChange", "initialValue", "TextArea", "autoSize", "minRows", "maxRows", "defaultValue", "e", "target", "rows", "footer", "lastMaintenance", "Date", "toLocaleString", "installationDate"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/DeviceManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, Space, Card, Tag, message } from 'antd';\nimport axios from 'axios';\nimport styled from 'styled-components';\n\nconst { Option } = Select;\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconst ContentArea = styled.div`\n  // padding: 24px;\n  background: white;\n  position: relative;\n  z-index: 1002;\n`;\nconst DeviceManagement = ({ id }, ref) => {\n  const [devices, setDevices] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingDevice, setEditingDevice] = useState(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentDevice, setCurrentDevice] = useState(null);\n  const [currentDeviceType, setCurrentDeviceType] = useState(editingDevice?.type || null);\n\n  // 获取设备列表\n  const fetchDevices = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/api/devices`);\n      if (response.data.success) {\n        setDevices(response.data.data);\n      } else {\n        throw new Error(response.data.message || '获取设备列表失败');\n      }\n    } catch (error) {\n      console.error('获取设备列表失败:', error);\n      message.error('获取设备列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchDevices();\n  }, []);\n\n  // 处理添加设备\n  const handleAddDevice = () => {\n    setEditingDevice(null);\n    setCurrentDeviceType(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // 处理编辑设备\n  const handleEditDevice = (device) => {\n    setEditingDevice(device);\n    setCurrentDeviceType(device.type);\n    form.setFieldsValue({\n      name: device.name,\n      type: device.type,\n      status: device.status,\n      location: device.location,\n      ipAddress: device.ipAddress,\n      manufacturer: device.manufacturer,\n      model: device.model,\n      description: device.description,\n      rtspUrl: device.rtspUrl,\n      entrance: device.entrance\n    });\n    setModalVisible(true);\n  };\n\n  // 处理查看设备详情\n  const handleViewDevice = (device) => {\n    setCurrentDevice(device);\n    setDetailModalVisible(true);\n  };\n\n  // 处理删除设备\n  const handleDeleteDevice = async (deviceId) => {\n    try {\n      const response = await axios.delete(`${API_BASE_URL}/api/devices/${deviceId}`);\n      if (response.data.success) {\n        message.success('设备删除成功');\n        fetchDevices(); // 重新获取设备列表\n      } else {\n        throw new Error(response.data.message || '删除设备失败');\n      }\n    } catch (error) {\n      console.error('删除设备失败:', error);\n      message.error('删除设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 处理表单提交\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      // 为调试添加日志\n      console.log('表单验证通过，原始数据:', values);\n      console.log('当前设备类型:', currentDeviceType);\n      \n      // 确保摄像头设备有 rtspUrl 字段\n      if (values.type === 'camera' || currentDeviceType === 'camera') {\n        // 直接从表单获取 rtspUrl 值，确保它不会丢失\n        const rtspInput = document.querySelector('textarea[placeholder*=\"RTSP\"]');\n        let rtspUrl = '';\n        \n        if (rtspInput) {\n          rtspUrl = rtspInput.value || '';\n          console.log('从DOM元素获取的 RTSP 地址:', rtspUrl);\n        } else {\n          rtspUrl = form.getFieldValue('rtspUrl') || values.rtspUrl || '';\n          console.log('从表单获取的 RTSP 地址:', rtspUrl);\n        }\n        \n        // 无论表单中是否有该字段，都强制设置 rtspUrl\n        values.rtspUrl = rtspUrl;\n        console.log('最终设置的 RTSP 地址:', rtspUrl);\n      } else {\n        // 对于非摄像头设备，确保移除 rtspUrl 字段\n        delete values.rtspUrl;\n        console.log('非摄像头设备，移除 rtspUrl 字段');\n      }\n      \n      console.log('提交表单最终数据:', JSON.stringify(values, null, 2));\n\n      if (editingDevice) {\n        // 编辑设备\n        const response = await axios.put(`${API_BASE_URL}/api/devices/${editingDevice.id}`, values);\n        if (response.data.success) {\n          message.success('设备更新成功');\n          setModalVisible(false);\n          fetchDevices(); // 重新获取设备列表\n        } else {\n          throw new Error(response.data.message || '更新设备失败');\n        }\n      } else {\n        // 添加设备\n        const response = await axios.post(`${API_BASE_URL}/api/devices`, values);\n        if (response.data.success) {\n          message.success('设备添加成功');\n          setModalVisible(false);\n          fetchDevices(); // 重新获取设备列表\n        } else {\n          throw new Error(response.data.message || '添加设备失败');\n        }\n      }\n    } catch (error) {\n      console.error('保存设备失败:', error);\n      message.error('保存设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 修改设备类型选择的处理函数\n  const handleDeviceTypeChange = (value) => {\n    console.log('设备类型变更为:', value);\n    setCurrentDeviceType(value);\n    \n    if (value === 'camera') {\n      // 如果是摄像头，确保表单中有 rtspUrl 字段\n      const currentRtspUrl = form.getFieldValue('rtspUrl') || '';\n      form.setFieldsValue({ \n        type: value,\n        rtspUrl: currentRtspUrl\n      });\n      console.log('设置摄像头 RTSP 初始值:', currentRtspUrl);\n    } else {\n      // 如果不是摄像头，移除 rtspUrl 字段\n      form.setFieldsValue({ type: value });\n      form.resetFields(['rtspUrl']);\n      console.log('重置 RTSP 字段');\n    }\n  };\n\n  // 渲染状态标签\n  const renderStatusTag = (status) => {\n    const statusMap = {\n      online: { color: 'green', text: '在线' },\n      offline: { color: 'gray', text: '离线' },\n      warning: { color: 'orange', text: '警告' },\n      error: { color: 'red', text: '错误' },\n      maintenance: { color: 'blue', text: '维护中' }\n    };\n    \n    const statusInfo = statusMap[status] || { color: 'default', text: status };\n    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;\n  };\n\n  // 设备类型映射\n  const deviceTypeMap = {\n    camera: '摄像头',\n    mmwave_radar: '毫米波雷达',\n    lidar: '激光雷达',\n    rsu: 'RSU',\n    edge_computing: '边缘计算单元',\n    obu: 'OBU'\n  };\n\n  // 表格列定义\n  const columns = [\n    {\n      title: '设备名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '设备类型',\n      dataIndex: 'type',\n      key: 'type',\n      render: (type) => deviceTypeMap[type] || type\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: renderStatusTag\n    },\n    {\n      title: '所在区域',\n      dataIndex: 'location',\n      key: 'location',\n    },\n    {\n      title: 'IP地址',\n      dataIndex: 'ipAddress',\n      key: 'ipAddress',\n    },\n    {\n      title: '安装位置',\n      dataIndex: 'entrance',\n      key: 'entrance',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button type=\"link\" onClick={() => handleViewDevice(record)}>查看</Button>\n          <Button type=\"link\" onClick={() => handleEditDevice(record)}>编辑</Button>\n          <Button type=\"link\" danger onClick={() => handleDeleteDevice(record.id)}>删除</Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 暴露 fetchDevices 方法\n  useImperativeHandle(ref, () => ({\n    fetchDevices\n  }));\n\n  return (\n    // <div id={id}>\n      <ContentArea> \n\n      \n        <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 16 }}>\n        <Button\n          type=\"primary\"\n          onClick={handleAddDevice}\n        >\n          添加设备\n        </Button>\n      </div>\n        <Table \n          loading={loading}\n          dataSource={devices} \n          columns={columns} \n          rowKey=\"id\"\n        />\n      \n\n\n      {/* 添加/编辑设备表单 */}\n      <Modal\n        title={editingDevice ? '编辑设备' : '添加设备'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => setModalVisible(false)}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"设备名称\"\n            rules={[{ required: true, message: '请输入设备名称' }]}\n          >\n            <Input placeholder=\"请输入设备名称\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"type\"\n            label=\"设备类型\"\n            rules={[{ required: true, message: '请选择设备类型' }]}\n          >\n            <Select \n              placeholder=\"请选择设备类型\"\n              onChange={handleDeviceTypeChange}\n            >\n              <Option value=\"camera\">摄像头</Option>\n              <Option value=\"mmwave_radar\">毫米波雷达</Option>\n              <Option value=\"lidar\">激光雷达</Option>\n              <Option value=\"rsu\">RSU</Option>\n              <Option value=\"edge_computing\">边缘计算单元</Option>\n              <Option value=\"obu\">OBU</Option>\n            </Select>\n          </Form.Item>\n          \n          {/* 当设备类型为摄像头时显示 RTSP 地址输入 */}\n          {currentDeviceType === 'camera' && (\n            <Form.Item\n              name=\"rtspUrl\"\n              label=\"RTSP地址\"\n              initialValue=\"\"\n            >\n              <Input.TextArea\n                id=\"rtspUrlInput\"\n                placeholder=\"请输入RTSP地址，例如：rtsp://admin:password@192.168.1.100:554/stream1\"\n                autoSize={{ minRows: 1, maxRows: 3 }}\n                defaultValue=\"\"\n                onChange={(e) => {\n                  // 将输入的 RTSP 地址保存到表单\n                  form.setFieldsValue({ rtspUrl: e.target.value });\n                  console.log('RTSP 地址输入变化:', e.target.value);\n                }}\n              />\n            </Form.Item>\n          )}\n          \n          <Form.Item\n            name=\"status\"\n            label=\"设备状态\"\n            rules={[{ required: true, message: '请选择设备状态' }]}\n          >\n            <Select placeholder=\"请选择设备状态\">\n              <Option value=\"online\">在线</Option>\n              <Option value=\"offline\">离线</Option>\n              <Option value=\"warning\">警告</Option>\n              <Option value=\"error\">错误</Option>\n              <Option value=\"maintenance\">维护中</Option>\n            </Select>\n          </Form.Item>\n          \n          <Form.Item\n            name=\"location\"\n            label=\"设备区域\"\n            rules={[{ required: true, message: '请输入设备所在区域' }]}\n          >\n            <Input placeholder=\"请输入设备所在区域\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"entrance\"\n            label=\"安装位置\"\n            rules={[{ required: true, message: '请选择安装位置' }]}\n          >\n            <Input placeholder=\"请输入设备所在安装位置\" />\n            {/* <Select placeholder=\"请选择安装进口\">\n              <Option value=\"东进口\">东进口</Option>\n              <Option value=\"西进口\">西进口</Option>\n              <Option value=\"北进口\">北进口</Option>\n              <Option value=\"南进口\">南进口</Option>\n            </Select> */}\n          </Form.Item>\n\n          <Form.Item\n            name=\"ipAddress\"\n            label=\"IP地址\"\n          >\n            <Input placeholder=\"请输入IP地址\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"manufacturer\"\n            label=\"制造商\"\n          >\n            <Input placeholder=\"请输入制造商\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"model\"\n            label=\"型号\"\n          >\n            <Input placeholder=\"请输入型号\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"description\"\n            label=\"设备描述\"\n          >\n            <Input.TextArea rows={4} placeholder=\"请输入设备描述\" />\n          </Form.Item>\n          \n\n        </Form>\n      </Modal>\n\n      {/* 设备详情模态框 */}\n      <Modal\n        title=\"设备详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={600}\n      >\n        {currentDevice && (\n          <div>\n            <p><strong>设备ID:</strong> {currentDevice.id}</p>\n            <p><strong>设备名称:</strong> {currentDevice.name}</p>\n            <p><strong>设备类型:</strong> {deviceTypeMap[currentDevice.type] || currentDevice.type}</p>\n            <p><strong>状态:</strong> {renderStatusTag(currentDevice.status)}</p>\n            <p><strong>位置:</strong> {currentDevice.location}</p>\n            <p><strong>IP地址:</strong> {currentDevice.ipAddress}</p>\n            <p><strong>最后维护时间:</strong> {currentDevice.lastMaintenance ? new Date(currentDevice.lastMaintenance).toLocaleString() : '无记录'}</p>\n            <p><strong>安装日期:</strong> {currentDevice.installationDate ? new Date(currentDevice.installationDate).toLocaleString() : '无记录'}</p>\n            <p><strong>制造商:</strong> {currentDevice.manufacturer || '未知'}</p>\n            <p><strong>型号:</strong> {currentDevice.model || '未知'}</p>\n            <p><strong>描述:</strong> {currentDevice.description || '无'}</p>\n            {currentDevice.type === 'camera' && (\n              <p><strong>RTSP地址:</strong> {currentDevice.rtspUrl || '未设置'}</p>\n            )}\n          </div>\n        )}\n      </Modal>\n      </ContentArea>\n    // </div>\n  );\n};\n\nexport default forwardRef(DeviceManagement); "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,UAAU,CAAEC,mBAAmB,KAAQ,OAAO,CACnF,OAASC,KAAK,CAAEC,MAAM,CAAEC,KAAK,CAAEC,IAAI,CAAEC,KAAK,CAAEC,MAAM,CAAEC,KAAK,CAAEC,IAAI,CAAEC,GAAG,CAAEC,OAAO,KAAQ,MAAM,CAC3F,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvC,KAAM,CAAEC,MAAO,CAAC,CAAGX,MAAM,CACzB,KAAM,CAAAY,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CAC7E,KAAM,CAAAC,WAAW,CAAGV,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC,CACD,KAAM,CAAAC,gBAAgB,CAAGA,CAAAC,IAAA,CAASC,GAAG,GAAK,IAAhB,CAAEC,EAAG,CAAC,CAAAF,IAAA,CAC9B,KAAM,CAACG,OAAO,CAAEC,UAAU,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACiC,OAAO,CAAEC,UAAU,CAAC,CAAGlC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACmC,YAAY,CAAEC,eAAe,CAAC,CAAGpC,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACqC,IAAI,CAAC,CAAG9B,IAAI,CAAC+B,OAAO,CAAC,CAAC,CAC7B,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAGxC,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACyC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG1C,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAAC2C,aAAa,CAAEC,gBAAgB,CAAC,CAAG5C,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAAC6C,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG9C,QAAQ,CAAC,CAAAuC,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEQ,IAAI,GAAI,IAAI,CAAC,CAEvF;AACA,KAAM,CAAAC,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACFd,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAe,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAACoC,GAAG,CAAC,GAAG7B,YAAY,cAAc,CAAC,CAC/D,GAAI4B,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CACzBpB,UAAU,CAACiB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC,CAChC,CAAC,IAAM,CACL,KAAM,IAAI,CAAAE,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAACtC,OAAO,EAAI,UAAU,CAAC,CACtD,CACF,CAAE,MAAOyC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjCzC,OAAO,CAACyC,KAAK,CAAC,YAAY,EAAIA,KAAK,CAACzC,OAAO,EAAI,MAAM,CAAC,CAAC,CACzD,CAAC,OAAS,CACRqB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDjC,SAAS,CAAC,IAAM,CACd+C,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAQ,eAAe,CAAGA,CAAA,GAAM,CAC5BhB,gBAAgB,CAAC,IAAI,CAAC,CACtBM,oBAAoB,CAAC,IAAI,CAAC,CAC1BT,IAAI,CAACoB,WAAW,CAAC,CAAC,CAClBrB,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAsB,gBAAgB,CAAIC,MAAM,EAAK,CACnCnB,gBAAgB,CAACmB,MAAM,CAAC,CACxBb,oBAAoB,CAACa,MAAM,CAACZ,IAAI,CAAC,CACjCV,IAAI,CAACuB,cAAc,CAAC,CAClBC,IAAI,CAAEF,MAAM,CAACE,IAAI,CACjBd,IAAI,CAAEY,MAAM,CAACZ,IAAI,CACjBe,MAAM,CAAEH,MAAM,CAACG,MAAM,CACrBC,QAAQ,CAAEJ,MAAM,CAACI,QAAQ,CACzBC,SAAS,CAAEL,MAAM,CAACK,SAAS,CAC3BC,YAAY,CAAEN,MAAM,CAACM,YAAY,CACjCC,KAAK,CAAEP,MAAM,CAACO,KAAK,CACnBC,WAAW,CAAER,MAAM,CAACQ,WAAW,CAC/BC,OAAO,CAAET,MAAM,CAACS,OAAO,CACvBC,QAAQ,CAAEV,MAAM,CAACU,QACnB,CAAC,CAAC,CACFjC,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAkC,gBAAgB,CAAIX,MAAM,EAAK,CACnCf,gBAAgB,CAACe,MAAM,CAAC,CACxBjB,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED;AACA,KAAM,CAAA6B,kBAAkB,CAAG,KAAO,CAAAC,QAAQ,EAAK,CAC7C,GAAI,CACF,KAAM,CAAAvB,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAAC2D,MAAM,CAAC,GAAGpD,YAAY,gBAAgBmD,QAAQ,EAAE,CAAC,CAC9E,GAAIvB,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CACzBvC,OAAO,CAACuC,OAAO,CAAC,QAAQ,CAAC,CACzBJ,YAAY,CAAC,CAAC,CAAE;AAClB,CAAC,IAAM,CACL,KAAM,IAAI,CAAAK,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAACtC,OAAO,EAAI,QAAQ,CAAC,CACpD,CACF,CAAE,MAAOyC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BzC,OAAO,CAACyC,KAAK,CAAC,UAAU,EAAIA,KAAK,CAACzC,OAAO,EAAI,MAAM,CAAC,CAAC,CACvD,CACF,CAAC,CAED;AACA,KAAM,CAAA6D,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAtC,IAAI,CAACuC,cAAc,CAAC,CAAC,CAE1C;AACArB,OAAO,CAACsB,GAAG,CAAC,cAAc,CAAEF,MAAM,CAAC,CACnCpB,OAAO,CAACsB,GAAG,CAAC,SAAS,CAAEhC,iBAAiB,CAAC,CAEzC;AACA,GAAI8B,MAAM,CAAC5B,IAAI,GAAK,QAAQ,EAAIF,iBAAiB,GAAK,QAAQ,CAAE,CAC9D;AACA,KAAM,CAAAiC,SAAS,CAAGC,QAAQ,CAACC,aAAa,CAAC,+BAA+B,CAAC,CACzE,GAAI,CAAAZ,OAAO,CAAG,EAAE,CAEhB,GAAIU,SAAS,CAAE,CACbV,OAAO,CAAGU,SAAS,CAACG,KAAK,EAAI,EAAE,CAC/B1B,OAAO,CAACsB,GAAG,CAAC,oBAAoB,CAAET,OAAO,CAAC,CAC5C,CAAC,IAAM,CACLA,OAAO,CAAG/B,IAAI,CAAC6C,aAAa,CAAC,SAAS,CAAC,EAAIP,MAAM,CAACP,OAAO,EAAI,EAAE,CAC/Db,OAAO,CAACsB,GAAG,CAAC,iBAAiB,CAAET,OAAO,CAAC,CACzC,CAEA;AACAO,MAAM,CAACP,OAAO,CAAGA,OAAO,CACxBb,OAAO,CAACsB,GAAG,CAAC,gBAAgB,CAAET,OAAO,CAAC,CACxC,CAAC,IAAM,CACL;AACA,MAAO,CAAAO,MAAM,CAACP,OAAO,CACrBb,OAAO,CAACsB,GAAG,CAAC,sBAAsB,CAAC,CACrC,CAEAtB,OAAO,CAACsB,GAAG,CAAC,WAAW,CAAEM,IAAI,CAACC,SAAS,CAACT,MAAM,CAAE,IAAI,CAAE,CAAC,CAAC,CAAC,CAEzD,GAAIpC,aAAa,CAAE,CACjB;AACA,KAAM,CAAAU,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAACuE,GAAG,CAAC,GAAGhE,YAAY,gBAAgBkB,aAAa,CAACT,EAAE,EAAE,CAAE6C,MAAM,CAAC,CAC3F,GAAI1B,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CACzBvC,OAAO,CAACuC,OAAO,CAAC,QAAQ,CAAC,CACzBhB,eAAe,CAAC,KAAK,CAAC,CACtBY,YAAY,CAAC,CAAC,CAAE;AAClB,CAAC,IAAM,CACL,KAAM,IAAI,CAAAK,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAACtC,OAAO,EAAI,QAAQ,CAAC,CACpD,CACF,CAAC,IAAM,CACL;AACA,KAAM,CAAAoC,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAACwE,IAAI,CAAC,GAAGjE,YAAY,cAAc,CAAEsD,MAAM,CAAC,CACxE,GAAI1B,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CACzBvC,OAAO,CAACuC,OAAO,CAAC,QAAQ,CAAC,CACzBhB,eAAe,CAAC,KAAK,CAAC,CACtBY,YAAY,CAAC,CAAC,CAAE;AAClB,CAAC,IAAM,CACL,KAAM,IAAI,CAAAK,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAACtC,OAAO,EAAI,QAAQ,CAAC,CACpD,CACF,CACF,CAAE,MAAOyC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BzC,OAAO,CAACyC,KAAK,CAAC,UAAU,EAAIA,KAAK,CAACzC,OAAO,EAAI,MAAM,CAAC,CAAC,CACvD,CACF,CAAC,CAED;AACA,KAAM,CAAA0E,sBAAsB,CAAIN,KAAK,EAAK,CACxC1B,OAAO,CAACsB,GAAG,CAAC,UAAU,CAAEI,KAAK,CAAC,CAC9BnC,oBAAoB,CAACmC,KAAK,CAAC,CAE3B,GAAIA,KAAK,GAAK,QAAQ,CAAE,CACtB;AACA,KAAM,CAAAO,cAAc,CAAGnD,IAAI,CAAC6C,aAAa,CAAC,SAAS,CAAC,EAAI,EAAE,CAC1D7C,IAAI,CAACuB,cAAc,CAAC,CAClBb,IAAI,CAAEkC,KAAK,CACXb,OAAO,CAAEoB,cACX,CAAC,CAAC,CACFjC,OAAO,CAACsB,GAAG,CAAC,iBAAiB,CAAEW,cAAc,CAAC,CAChD,CAAC,IAAM,CACL;AACAnD,IAAI,CAACuB,cAAc,CAAC,CAAEb,IAAI,CAAEkC,KAAM,CAAC,CAAC,CACpC5C,IAAI,CAACoB,WAAW,CAAC,CAAC,SAAS,CAAC,CAAC,CAC7BF,OAAO,CAACsB,GAAG,CAAC,YAAY,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAY,eAAe,CAAI3B,MAAM,EAAK,CAClC,KAAM,CAAA4B,SAAS,CAAG,CAChBC,MAAM,CAAE,CAAEC,KAAK,CAAE,OAAO,CAAEC,IAAI,CAAE,IAAK,CAAC,CACtCC,OAAO,CAAE,CAAEF,KAAK,CAAE,MAAM,CAAEC,IAAI,CAAE,IAAK,CAAC,CACtCE,OAAO,CAAE,CAAEH,KAAK,CAAE,QAAQ,CAAEC,IAAI,CAAE,IAAK,CAAC,CACxCvC,KAAK,CAAE,CAAEsC,KAAK,CAAE,KAAK,CAAEC,IAAI,CAAE,IAAK,CAAC,CACnCG,WAAW,CAAE,CAAEJ,KAAK,CAAE,MAAM,CAAEC,IAAI,CAAE,KAAM,CAC5C,CAAC,CAED,KAAM,CAAAI,UAAU,CAAGP,SAAS,CAAC5B,MAAM,CAAC,EAAI,CAAE8B,KAAK,CAAE,SAAS,CAAEC,IAAI,CAAE/B,MAAO,CAAC,CAC1E,mBAAO7C,IAAA,CAACL,GAAG,EAACgF,KAAK,CAAEK,UAAU,CAACL,KAAM,CAAAM,QAAA,CAAED,UAAU,CAACJ,IAAI,CAAM,CAAC,CAC9D,CAAC,CAED;AACA,KAAM,CAAAM,aAAa,CAAG,CACpBC,MAAM,CAAE,KAAK,CACbC,YAAY,CAAE,OAAO,CACrBC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,KAAK,CACVC,cAAc,CAAE,QAAQ,CACxBC,GAAG,CAAE,KACP,CAAC,CAED;AACA,KAAM,CAAAC,OAAO,CAAG,CACd,CACEC,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MACP,CAAC,CACD,CACEF,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,MAAM,CAAG/D,IAAI,EAAKoD,aAAa,CAACpD,IAAI,CAAC,EAAIA,IAC3C,CAAC,CACD,CACE4D,KAAK,CAAE,IAAI,CACXC,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,MAAM,CAAErB,eACV,CAAC,CACD,CACEkB,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UACP,CAAC,CACD,CACEF,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WACP,CAAC,CACD,CACEF,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UACP,CAAC,CACD,CACEF,KAAK,CAAE,IAAI,CACXE,GAAG,CAAE,QAAQ,CACbC,MAAM,CAAEA,CAACC,CAAC,CAAEC,MAAM,gBAChB7F,KAAA,CAACT,KAAK,EAACuG,IAAI,CAAC,OAAO,CAAAf,QAAA,eACjBjF,IAAA,CAACZ,MAAM,EAAC0C,IAAI,CAAC,MAAM,CAACmE,OAAO,CAAEA,CAAA,GAAM5C,gBAAgB,CAAC0C,MAAM,CAAE,CAAAd,QAAA,CAAC,cAAE,CAAQ,CAAC,cACxEjF,IAAA,CAACZ,MAAM,EAAC0C,IAAI,CAAC,MAAM,CAACmE,OAAO,CAAEA,CAAA,GAAMxD,gBAAgB,CAACsD,MAAM,CAAE,CAAAd,QAAA,CAAC,cAAE,CAAQ,CAAC,cACxEjF,IAAA,CAACZ,MAAM,EAAC0C,IAAI,CAAC,MAAM,CAACoE,MAAM,MAACD,OAAO,CAAEA,CAAA,GAAM3C,kBAAkB,CAACyC,MAAM,CAAClF,EAAE,CAAE,CAAAoE,QAAA,CAAC,cAAE,CAAQ,CAAC,EAC/E,CAEX,CAAC,CACF,CAED;AACA/F,mBAAmB,CAAC0B,GAAG,CAAE,KAAO,CAC9BmB,YACF,CAAC,CAAC,CAAC,CAEH,oBACE;AACE7B,KAAA,CAACM,WAAW,EAAAyE,QAAA,eAGVjF,IAAA,QAAKmG,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,UAAU,CAAEC,YAAY,CAAE,EAAG,CAAE,CAAArB,QAAA,cAC9EjF,IAAA,CAACZ,MAAM,EACL0C,IAAI,CAAC,SAAS,CACdmE,OAAO,CAAE1D,eAAgB,CAAA0C,QAAA,CAC1B,0BAED,CAAQ,CAAC,CACN,CAAC,cACJjF,IAAA,CAACb,KAAK,EACJ6B,OAAO,CAAEA,OAAQ,CACjBuF,UAAU,CAAEzF,OAAQ,CACpB2E,OAAO,CAAEA,OAAQ,CACjBe,MAAM,CAAC,IAAI,CACZ,CAAC,cAKJxG,IAAA,CAACX,KAAK,EACJqG,KAAK,CAAEpE,aAAa,CAAG,MAAM,CAAG,MAAO,CACvCmF,IAAI,CAAEvF,YAAa,CACnBwF,IAAI,CAAEjD,aAAc,CACpBkD,QAAQ,CAAEA,CAAA,GAAMxF,eAAe,CAAC,KAAK,CAAE,CACvCyF,KAAK,CAAE,GAAI,CAAA3B,QAAA,cAEX/E,KAAA,CAACZ,IAAI,EACH8B,IAAI,CAAEA,IAAK,CACXyF,MAAM,CAAC,UAAU,CAAA5B,QAAA,eAEjBjF,IAAA,CAACV,IAAI,CAACwH,IAAI,EACRlE,IAAI,CAAC,MAAM,CACXmE,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAErH,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAqF,QAAA,cAEhDjF,IAAA,CAACT,KAAK,EAAC2H,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,cAEZlH,IAAA,CAACV,IAAI,CAACwH,IAAI,EACRlE,IAAI,CAAC,MAAM,CACXmE,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAErH,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAqF,QAAA,cAEhD/E,KAAA,CAACV,MAAM,EACL0H,WAAW,CAAC,4CAAS,CACrBC,QAAQ,CAAE7C,sBAAuB,CAAAW,QAAA,eAEjCjF,IAAA,CAACG,MAAM,EAAC6D,KAAK,CAAC,QAAQ,CAAAiB,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACnCjF,IAAA,CAACG,MAAM,EAAC6D,KAAK,CAAC,cAAc,CAAAiB,QAAA,CAAC,gCAAK,CAAQ,CAAC,cAC3CjF,IAAA,CAACG,MAAM,EAAC6D,KAAK,CAAC,OAAO,CAAAiB,QAAA,CAAC,0BAAI,CAAQ,CAAC,cACnCjF,IAAA,CAACG,MAAM,EAAC6D,KAAK,CAAC,KAAK,CAAAiB,QAAA,CAAC,KAAG,CAAQ,CAAC,cAChCjF,IAAA,CAACG,MAAM,EAAC6D,KAAK,CAAC,gBAAgB,CAAAiB,QAAA,CAAC,sCAAM,CAAQ,CAAC,cAC9CjF,IAAA,CAACG,MAAM,EAAC6D,KAAK,CAAC,KAAK,CAAAiB,QAAA,CAAC,KAAG,CAAQ,CAAC,EAC1B,CAAC,CACA,CAAC,CAGXrD,iBAAiB,GAAK,QAAQ,eAC7B5B,IAAA,CAACV,IAAI,CAACwH,IAAI,EACRlE,IAAI,CAAC,SAAS,CACdmE,KAAK,CAAC,kBAAQ,CACdK,YAAY,CAAC,EAAE,CAAAnC,QAAA,cAEfjF,IAAA,CAACT,KAAK,CAAC8H,QAAQ,EACbxG,EAAE,CAAC,cAAc,CACjBqG,WAAW,CAAC,2GAA8D,CAC1EI,QAAQ,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,OAAO,CAAE,CAAE,CAAE,CACrCC,YAAY,CAAC,EAAE,CACfN,QAAQ,CAAGO,CAAC,EAAK,CACf;AACAtG,IAAI,CAACuB,cAAc,CAAC,CAAEQ,OAAO,CAAEuE,CAAC,CAACC,MAAM,CAAC3D,KAAM,CAAC,CAAC,CAChD1B,OAAO,CAACsB,GAAG,CAAC,cAAc,CAAE8D,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAC,CAC7C,CAAE,CACH,CAAC,CACO,CACZ,cAEDhE,IAAA,CAACV,IAAI,CAACwH,IAAI,EACRlE,IAAI,CAAC,QAAQ,CACbmE,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAErH,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAqF,QAAA,cAEhD/E,KAAA,CAACV,MAAM,EAAC0H,WAAW,CAAC,4CAAS,CAAAjC,QAAA,eAC3BjF,IAAA,CAACG,MAAM,EAAC6D,KAAK,CAAC,QAAQ,CAAAiB,QAAA,CAAC,cAAE,CAAQ,CAAC,cAClCjF,IAAA,CAACG,MAAM,EAAC6D,KAAK,CAAC,SAAS,CAAAiB,QAAA,CAAC,cAAE,CAAQ,CAAC,cACnCjF,IAAA,CAACG,MAAM,EAAC6D,KAAK,CAAC,SAAS,CAAAiB,QAAA,CAAC,cAAE,CAAQ,CAAC,cACnCjF,IAAA,CAACG,MAAM,EAAC6D,KAAK,CAAC,OAAO,CAAAiB,QAAA,CAAC,cAAE,CAAQ,CAAC,cACjCjF,IAAA,CAACG,MAAM,EAAC6D,KAAK,CAAC,aAAa,CAAAiB,QAAA,CAAC,oBAAG,CAAQ,CAAC,EAClC,CAAC,CACA,CAAC,cAEZjF,IAAA,CAACV,IAAI,CAACwH,IAAI,EACRlE,IAAI,CAAC,UAAU,CACfmE,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAErH,OAAO,CAAE,WAAY,CAAC,CAAE,CAAAqF,QAAA,cAElDjF,IAAA,CAACT,KAAK,EAAC2H,WAAW,CAAC,wDAAW,CAAE,CAAC,CACxB,CAAC,cAEZlH,IAAA,CAACV,IAAI,CAACwH,IAAI,EACRlE,IAAI,CAAC,UAAU,CACfmE,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAErH,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAqF,QAAA,cAEhDjF,IAAA,CAACT,KAAK,EAAC2H,WAAW,CAAC,oEAAa,CAAE,CAAC,CAO1B,CAAC,cAEZlH,IAAA,CAACV,IAAI,CAACwH,IAAI,EACRlE,IAAI,CAAC,WAAW,CAChBmE,KAAK,CAAC,gBAAM,CAAA9B,QAAA,cAEZjF,IAAA,CAACT,KAAK,EAAC2H,WAAW,CAAC,kCAAS,CAAE,CAAC,CACtB,CAAC,cAEZlH,IAAA,CAACV,IAAI,CAACwH,IAAI,EACRlE,IAAI,CAAC,cAAc,CACnBmE,KAAK,CAAC,oBAAK,CAAA9B,QAAA,cAEXjF,IAAA,CAACT,KAAK,EAAC2H,WAAW,CAAC,sCAAQ,CAAE,CAAC,CACrB,CAAC,cAEZlH,IAAA,CAACV,IAAI,CAACwH,IAAI,EACRlE,IAAI,CAAC,OAAO,CACZmE,KAAK,CAAC,cAAI,CAAA9B,QAAA,cAEVjF,IAAA,CAACT,KAAK,EAAC2H,WAAW,CAAC,gCAAO,CAAE,CAAC,CACpB,CAAC,cAEZlH,IAAA,CAACV,IAAI,CAACwH,IAAI,EACRlE,IAAI,CAAC,aAAa,CAClBmE,KAAK,CAAC,0BAAM,CAAA9B,QAAA,cAEZjF,IAAA,CAACT,KAAK,CAAC8H,QAAQ,EAACO,IAAI,CAAE,CAAE,CAACV,WAAW,CAAC,4CAAS,CAAE,CAAC,CACxC,CAAC,EAGR,CAAC,CACF,CAAC,cAGRlH,IAAA,CAACX,KAAK,EACJqG,KAAK,CAAC,0BAAM,CACZe,IAAI,CAAEjF,kBAAmB,CACzBmF,QAAQ,CAAEA,CAAA,GAAMlF,qBAAqB,CAAC,KAAK,CAAE,CAC7CoG,MAAM,CAAE,cACN7H,IAAA,CAACZ,MAAM,EAAa6G,OAAO,CAAEA,CAAA,GAAMxE,qBAAqB,CAAC,KAAK,CAAE,CAAAwD,QAAA,CAAC,cAEjE,EAFY,OAEJ,CAAC,CACT,CACF2B,KAAK,CAAE,GAAI,CAAA3B,QAAA,CAEVvD,aAAa,eACZxB,KAAA,QAAA+E,QAAA,eACE/E,KAAA,MAAA+E,QAAA,eAAGjF,IAAA,WAAAiF,QAAA,CAAQ,iBAAK,CAAQ,CAAC,IAAC,CAACvD,aAAa,CAACb,EAAE,EAAI,CAAC,cAChDX,KAAA,MAAA+E,QAAA,eAAGjF,IAAA,WAAAiF,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACvD,aAAa,CAACkB,IAAI,EAAI,CAAC,cAClD1C,KAAA,MAAA+E,QAAA,eAAGjF,IAAA,WAAAiF,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACC,aAAa,CAACxD,aAAa,CAACI,IAAI,CAAC,EAAIJ,aAAa,CAACI,IAAI,EAAI,CAAC,cACvF5B,KAAA,MAAA+E,QAAA,eAAGjF,IAAA,WAAAiF,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAACT,eAAe,CAAC9C,aAAa,CAACmB,MAAM,CAAC,EAAI,CAAC,cACnE3C,KAAA,MAAA+E,QAAA,eAAGjF,IAAA,WAAAiF,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAACvD,aAAa,CAACoB,QAAQ,EAAI,CAAC,cACpD5C,KAAA,MAAA+E,QAAA,eAAGjF,IAAA,WAAAiF,QAAA,CAAQ,iBAAK,CAAQ,CAAC,IAAC,CAACvD,aAAa,CAACqB,SAAS,EAAI,CAAC,cACvD7C,KAAA,MAAA+E,QAAA,eAAGjF,IAAA,WAAAiF,QAAA,CAAQ,uCAAO,CAAQ,CAAC,IAAC,CAACvD,aAAa,CAACoG,eAAe,CAAG,GAAI,CAAAC,IAAI,CAACrG,aAAa,CAACoG,eAAe,CAAC,CAACE,cAAc,CAAC,CAAC,CAAG,KAAK,EAAI,CAAC,cAClI9H,KAAA,MAAA+E,QAAA,eAAGjF,IAAA,WAAAiF,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACvD,aAAa,CAACuG,gBAAgB,CAAG,GAAI,CAAAF,IAAI,CAACrG,aAAa,CAACuG,gBAAgB,CAAC,CAACD,cAAc,CAAC,CAAC,CAAG,KAAK,EAAI,CAAC,cAClI9H,KAAA,MAAA+E,QAAA,eAAGjF,IAAA,WAAAiF,QAAA,CAAQ,qBAAI,CAAQ,CAAC,IAAC,CAACvD,aAAa,CAACsB,YAAY,EAAI,IAAI,EAAI,CAAC,cACjE9C,KAAA,MAAA+E,QAAA,eAAGjF,IAAA,WAAAiF,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAACvD,aAAa,CAACuB,KAAK,EAAI,IAAI,EAAI,CAAC,cACzD/C,KAAA,MAAA+E,QAAA,eAAGjF,IAAA,WAAAiF,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAACvD,aAAa,CAACwB,WAAW,EAAI,GAAG,EAAI,CAAC,CAC7DxB,aAAa,CAACI,IAAI,GAAK,QAAQ,eAC9B5B,KAAA,MAAA+E,QAAA,eAAGjF,IAAA,WAAAiF,QAAA,CAAQ,mBAAO,CAAQ,CAAC,IAAC,CAACvD,aAAa,CAACyB,OAAO,EAAI,KAAK,EAAI,CAChE,EACE,CACN,CACI,CAAC,EACK,CACf;AAAA,EAEJ,CAAC,CAED,2BAAelE,UAAU,CAACyB,gBAAgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}