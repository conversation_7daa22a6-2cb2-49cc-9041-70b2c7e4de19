{"ast": null, "code": "import toArray from \"rc-util/es/Children/toArray\";\nexport default function useChildren(children) {\n  if (typeof children === 'function') {\n    return children;\n  }\n  const childList = toArray(children);\n  return childList.length <= 1 ? childList[0] : childList;\n}", "map": {"version": 3, "names": ["toArray", "useChildren", "children", "childList", "length"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/form/hooks/useChildren.js"], "sourcesContent": ["import toArray from \"rc-util/es/Children/toArray\";\nexport default function useChildren(children) {\n  if (typeof children === 'function') {\n    return children;\n  }\n  const childList = toArray(children);\n  return childList.length <= 1 ? childList[0] : childList;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,6BAA6B;AACjD,eAAe,SAASC,WAAWA,CAACC,QAAQ,EAAE;EAC5C,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;IAClC,OAAOA,QAAQ;EACjB;EACA,MAAMC,SAAS,GAAGH,OAAO,CAACE,QAAQ,CAAC;EACnC,OAAOC,SAAS,CAACC,MAAM,IAAI,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}