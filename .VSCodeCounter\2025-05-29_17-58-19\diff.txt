Date : 2025-05-29 17:58:19
Directory : g:\AI_tools\cursor\projects\education web
Total : 35 files,  857 codes, -17 comments, -20 blanks, all 820 lines

Languages
+----------------+------------+------------+------------+------------+------------+
| language       | files      | code       | comment    | blank      | total      |
+----------------+------------+------------+------------+------------+------------+
| JavaScript JSX |         10 |        405 |        -65 |        -39 |        301 |
| JSON           |          4 |        193 |          0 |          0 |        193 |
| JavaScript     |          5 |        178 |         48 |          7 |        233 |
| XML            |         14 |         84 |          0 |         12 |         96 |
| Properties     |          1 |         -1 |          0 |          0 |         -1 |
| Markdown       |          1 |         -2 |          0 |          0 |         -2 |
+----------------+------------+------------+------------+------------+------------+

Directories
+-----------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| path                                                                              | files      | code       | comment    | blank      | total      |
+-----------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| .                                                                                 |         35 |        857 |        -17 |        -20 |        820 |
| . (Files)                                                                         |          4 |        100 |          0 |          0 |        100 |
| build                                                                             |          9 |         44 |          4 |          6 |         54 |
| build\images                                                                      |          7 |         42 |          0 |          6 |         48 |
| build\static                                                                      |          2 |          2 |          4 |          0 |          6 |
| build\static\js                                                                   |          2 |          2 |          4 |          0 |          6 |
| public                                                                            |          7 |         42 |          0 |          6 |         48 |
| public\images                                                                     |          7 |         42 |          0 |          6 |         48 |
| server                                                                            |          2 |         86 |         25 |         17 |        128 |
| src                                                                               |         13 |        585 |        -46 |        -49 |        490 |
| src (Files)                                                                       |          1 |         90 |         19 |        -10 |         99 |
| src\components                                                                    |          4 |        321 |       -132 |        -46 |        143 |
| src\data                                                                          |          2 |         90 |          0 |          0 |         90 |
| src\pages                                                                         |          6 |         84 |         67 |          7 |        158 |
+-----------------------------------------------------------------------------------+------------+------------+------------+------------+------------+

Files
+-----------------------------------------------------------------------------------+----------------+------------+------------+------------+------------+
| filename                                                                          | language       | code       | comment    | blank      | total      |
+-----------------------------------------------------------------------------------+----------------+------------+------------+------------+------------+
| g:\AI_tools\cursor\projects\education web\.env                                    | Properties     |         -1 |          0 |          0 |         -1 |
| g:\AI_tools\cursor\projects\education web\README.md                               | Markdown       |         -2 |          0 |          0 |         -2 |
| g:\AI_tools\cursor\projects\education web\build\images\camera.svg                 | XML            |          9 |          0 |          1 |         10 |
| g:\AI_tools\cursor\projects\education web\build\images\compass.svg                | XML            |          1 |          0 |          0 |          1 |
| g:\AI_tools\cursor\projects\education web\build\images\edge_computing.svg         | XML            |          4 |          0 |          1 |          5 |
| g:\AI_tools\cursor\projects\education web\build\images\lidar.svg                  | XML            |          5 |          0 |          1 |          6 |
| g:\AI_tools\cursor\projects\education web\build\images\mmwave_radar.svg           | XML            |          6 |          0 |          1 |          7 |
| g:\AI_tools\cursor\projects\education web\build\images\obu.svg                    | XML            |          9 |          0 |          1 |         10 |
| g:\AI_tools\cursor\projects\education web\build\images\rsu.svg                    | XML            |          8 |          0 |          1 |          9 |
| g:\AI_tools\cursor\projects\education web\build\static\js\main.4c7fc37b.js        | JavaScript     |       -420 |        -65 |        -32 |       -517 |
| g:\AI_tools\cursor\projects\education web\build\static\js\main.92f83330.js        | JavaScript     |        422 |         69 |         32 |        523 |
| g:\AI_tools\cursor\projects\education web\package-lock.json                       | JSON           |        101 |          0 |          0 |        101 |
| g:\AI_tools\cursor\projects\education web\package.json                            | JSON           |          2 |          0 |          0 |          2 |
| g:\AI_tools\cursor\projects\education web\public\images\camera.svg                | XML            |          9 |          0 |          1 |         10 |
| g:\AI_tools\cursor\projects\education web\public\images\compass.svg               | XML            |          1 |          0 |          0 |          1 |
| g:\AI_tools\cursor\projects\education web\public\images\edge_computing.svg        | XML            |          4 |          0 |          1 |          5 |
| g:\AI_tools\cursor\projects\education web\public\images\lidar.svg                 | XML            |          5 |          0 |          1 |          6 |
| g:\AI_tools\cursor\projects\education web\public\images\mmwave_radar.svg          | XML            |          6 |          0 |          1 |          7 |
| g:\AI_tools\cursor\projects\education web\public\images\obu.svg                   | XML            |          9 |          0 |          1 |         10 |
| g:\AI_tools\cursor\projects\education web\public\images\rsu.svg                   | XML            |          8 |          0 |          1 |          9 |
| g:\AI_tools\cursor\projects\education web\server\mqtt-ws-bridge.js                | JavaScript     |         57 |         18 |         16 |         91 |
| g:\AI_tools\cursor\projects\education web\server\stream-server.js                 | JavaScript     |         29 |          7 |          1 |         37 |
| g:\AI_tools\cursor\projects\education web\src\components\CampusModel.jsx          | JavaScript JSX |        360 |       -127 |        -15 |        218 |
| g:\AI_tools\cursor\projects\education web\src\components\DevicePopoverContent.jsx | JavaScript JSX |        150 |         14 |          6 |        170 |
| g:\AI_tools\cursor\projects\education web\src\components\RealTimeEvents.jsx       | JavaScript JSX |       -228 |        -19 |        -37 |       -284 |
| g:\AI_tools\cursor\projects\education web\src\components\VideoPlayer.jsx          | JavaScript JSX |         39 |          0 |          0 |         39 |
| g:\AI_tools\cursor\projects\education web\src\data\devices.json                   | JSON           |         47 |          0 |          0 |         47 |
| g:\AI_tools\cursor\projects\education web\src\data\intersections.json             | JSON           |         43 |          0 |          0 |         43 |
| g:\AI_tools\cursor\projects\education web\src\pages\DeviceManagement.jsx          | JavaScript JSX |         70 |         16 |          7 |         93 |
| g:\AI_tools\cursor\projects\education web\src\pages\DeviceStatus.jsx              | JavaScript JSX |        -41 |          6 |          1 |        -34 |
| g:\AI_tools\cursor\projects\education web\src\pages\RealTimeTraffic.jsx           | JavaScript JSX |         52 |         39 |          6 |         97 |
| g:\AI_tools\cursor\projects\education web\src\pages\RoadMonitoring.jsx            | JavaScript JSX |          1 |          5 |          0 |          6 |
| g:\AI_tools\cursor\projects\education web\src\pages\SystemManagement.jsx          | JavaScript JSX |          3 |          0 |         -7 |         -4 |
| g:\AI_tools\cursor\projects\education web\src\pages\VehicleManagement.jsx         | JavaScript JSX |         -1 |          1 |          0 |          0 |
| g:\AI_tools\cursor\projects\education web\src\server.js                           | JavaScript     |         90 |         19 |        -10 |         99 |
| Total                                                                             |                |        857 |        -17 |        -20 |        820 |
+-----------------------------------------------------------------------------------+----------------+------------+------------+------------+------------+