{"ast": null, "code": "export default function (eccent, sinphi, cosphi) {\n  var con = eccent * sinphi;\n  return cosphi / Math.sqrt(1 - con * con);\n}", "map": {"version": 3, "names": ["eccent", "sinphi", "cosphi", "con", "Math", "sqrt"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/proj4/lib/common/msfnz.js"], "sourcesContent": ["export default function(eccent, sinphi, cosphi) {\n  var con = eccent * sinphi;\n  return cosphi / (Math.sqrt(1 - con * con));\n}"], "mappings": "AAAA,eAAe,UAASA,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;EAC9C,IAAIC,GAAG,GAAGH,MAAM,GAAGC,MAAM;EACzB,OAAOC,MAAM,GAAIE,IAAI,CAACC,IAAI,CAAC,CAAC,GAAGF,GAAG,GAAGA,GAAG,CAAE;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}