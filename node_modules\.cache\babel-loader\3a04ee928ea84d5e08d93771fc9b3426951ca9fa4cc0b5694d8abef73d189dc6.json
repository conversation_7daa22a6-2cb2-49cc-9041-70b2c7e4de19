{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\SystemManagement.jsx\",\n  _s = $RefreshSig$();\n// src/pages/SystemManagement.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, message, Card, Result } from 'antd';\nimport { UserOutlined, LockOutlined, MailOutlined, StopOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport DeviceManagement from './DeviceManagement';\nimport styled from 'styled-components';\nimport devicesData from '../data/devices.json';\nimport intersectionsData from '../data/intersections.json';\nimport VehicleManagement from './VehicleManagement';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\n\n// 修改容器样式，确保整个组件在3D模型上层\nconst SystemContainer = styled.div`\n  position: relative;\n  z-index: 1002;\n  background: white;\n  pointer-events: auto;\n`;\n_c = SystemContainer;\nconst TabsContainer = styled.div`\n  display: flex;\n  margin-bottom: 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: white;\n  position: relative;\n  z-index: 1002;\n  pointer-events: auto;\n`;\n_c2 = TabsContainer;\nconst TabButton = styled.button`\n  padding: 10px 20px;\n  background: ${props => props.active ? '#e6f7ff' : 'white'};\n  border: none;\n  border-bottom: 2px solid ${props => props.active ? '#1890ff' : 'transparent'};\n  color: ${props => props.active ? '#1890ff' : 'rgba(0, 0, 0, 0.65)'};\n  cursor: pointer;\n  font-size: 14px;\n  margin-right: 20px;\n  transition: all 0.3s;\n  pointer-events: auto;\n  \n  &:hover {\n    color: #1890ff;\n  }\n  \n  &:focus {\n    outline: none;\n  }\n`;\n_c3 = TabButton;\nconst ContentArea = styled.div`\n  // padding: 24px;\n  background: white;\n  position: relative;\n  z-index: 1002;\n  // pointer-events: auto;\n`;\n_c4 = ContentArea;\nconst SystemManagement = () => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [users, setUsers] = useState([]);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingUser, setEditingUser] = useState(null);\n  const [activeTab, setActiveTab] = useState('users');\n  const [intersections, setIntersections] = useState([]);\n  const [intersectionModalVisible, setIntersectionModalVisible] = useState(false);\n  const [editingIntersection, setEditingIntersection] = useState(null);\n  const [intersectionForm] = Form.useForm();\n  const [hasPermission, setHasPermission] = useState(false);\n\n  // 检查用户权限\n  useEffect(() => {\n    const checkPermission = () => {\n      try {\n        var _userData$user;\n        const userInfo = localStorage.getItem('user');\n        if (!userInfo) {\n          setHasPermission(false);\n          return;\n        }\n        const userData = JSON.parse(userInfo);\n        const userRole = userData.role || ((_userData$user = userData.user) === null || _userData$user === void 0 ? void 0 : _userData$user.role) || 'user';\n        if (userRole === 'admin') {\n          setHasPermission(true);\n        } else {\n          setHasPermission(false);\n          message.error('您没有权限访问系统管理页面');\n        }\n      } catch (error) {\n        console.error('检查权限失败:', error);\n        setHasPermission(false);\n      }\n    };\n    checkPermission();\n  }, []);\n\n  // 获取用户列表\n  const fetchUsers = async () => {\n    if (!hasPermission) return; // 没有权限时不请求数据\n\n    try {\n      setLoading(true);\n      console.log('开始获取用户列表...');\n      const token = localStorage.getItem('token');\n      console.log('使用令牌获取用户列表:', token);\n\n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/users`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      console.log('API响应:', response.data);\n      if (response.data && response.data.success) {\n        setUsers(response.data.data || []);\n      } else {\n        var _response$data;\n        message.warning('获取用户列表失败: ' + (((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || '未知错误'));\n      }\n    } catch (error) {\n      console.error('API获取用户列表失败:', error);\n      message.error('获取用户列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 提前合并两个useEffect以避免条件渲染导致的钩子数量不一致\n  useEffect(() => {\n    if (hasPermission) {\n      fetchUsers();\n      fetchIntersections();\n\n      // 从 devices.json 中获取唯一的路口名称，排除 OBU 类型设备的位置\n      const uniqueLocations = [...new Set(devicesData.devices.filter(device => device.type !== 'obu') // 排除 OBU 类型设备\n      .map(device => device.location))];\n\n      // 从 intersections.json 中读取现有的路口数据\n      const existingIntersections = intersectionsData.intersections || [];\n\n      // 将路口名称与已有的路口数据合并\n      const mergedIntersections = uniqueLocations.map(location => {\n        const existingIntersection = existingIntersections.find(i => i.name === location);\n        if (existingIntersection) {\n          return existingIntersection;\n        }\n\n        // 如果是新路口，先保存到 intersections.json\n        const newIntersection = {\n          id: `intersection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          name: location,\n          latitude: '',\n          longitude: '',\n          createdAt: new Date().toISOString()\n        };\n\n        // 调用 API 保存新路口\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        axios.post(`${apiUrl}/api/intersections`, newIntersection).catch(error => {\n          console.error('保存新路口失败:', error);\n        });\n        return newIntersection;\n      });\n      setIntersections(mergedIntersections);\n    }\n  }, [activeTab, hasPermission]);\n\n  // 渲染无权限页面或主要内容\n  if (!hasPermission) {\n    return /*#__PURE__*/_jsxDEV(SystemContainer, {\n      children: /*#__PURE__*/_jsxDEV(Result, {\n        status: \"403\",\n        title: \"403\",\n        subTitle: \"\\u62B1\\u6B49\\uFF0C\\u60A8\\u6CA1\\u6709\\u6743\\u9650\\u8BBF\\u95EE\\u6B64\\u9875\\u9762\",\n        icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 17\n        }, this),\n        extra: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          onClick: () => navigate('/'),\n          children: \"\\u8FD4\\u56DE\\u9996\\u9875\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 以下代码只有在hasPermission为true时才会执行\n\n  // 修改标签切换处理函数\n  const handleTabChange = tab => {\n    console.log('切换到标签:', tab);\n    setActiveTab(tab);\n\n    // 切换到设备管理标签时重新获取设备列表\n    if (tab === 'devices') {\n      const deviceManagementRef = document.querySelector('#device-management');\n      if (deviceManagementRef) {\n        deviceManagementRef.fetchDevices();\n      }\n    }\n  };\n  const handleAddUser = () => {\n    setEditingUser(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEditUser = user => {\n    setEditingUser(user);\n    form.setFieldsValue({\n      username: user.username,\n      role: user.role,\n      email: user.email\n    });\n    setModalVisible(true);\n  };\n  const handleDeleteUser = async userId => {\n    try {\n      const token = localStorage.getItem('token');\n\n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n      // 尝试通过API删除\n      try {\n        await axios.delete(`${apiUrl}/api/users/${userId}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        message.success('用户已删除');\n        fetchUsers(); // 重新获取用户列表\n      } catch (error) {\n        console.error('API删除用户失败:', error);\n\n        // 如果API删除失败，从本地存储中删除\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        const updatedUsers = localUsers.filter(user => user.id !== userId);\n        localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        message.success('用户已删除（本地存储）');\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('删除用户失败:', error);\n      message.error('删除用户失败: ' + (error.message || '未知错误'));\n    }\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      console.log('表单数据:', values);\n      const token = localStorage.getItem('token');\n\n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n      // 尝试通过API保存用户\n      try {\n        let response;\n        if (editingUser) {\n          // 更新用户\n          response = await axios.put(`${apiUrl}/api/users/${editingUser.id}`, values, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        } else {\n          // 添加用户\n          response = await axios.post(`${apiUrl}/api/users`, values, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        }\n        console.log('API响应:', response.data);\n        if (response.data && response.data.success) {\n          message.success(editingUser ? '用户已更新' : '用户已添加');\n          setModalVisible(false);\n          form.resetFields();\n          fetchUsers(); // 重新获取用户列表\n        } else {\n          var _response$data2;\n          message.error(((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || '操作失败');\n        }\n      } catch (error) {\n        console.error('API保存用户失败:', error);\n\n        // 如果API保存失败，保存到本地存储\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        if (editingUser) {\n          // 更新用户\n          const updatedUsers = localUsers.map(user => user.id === editingUser.id ? {\n            ...user,\n            ...values\n          } : user);\n          localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        } else {\n          // 添加用户\n          const newUser = {\n            ...values,\n            id: Date.now().toString(),\n            createdAt: new Date().toISOString()\n          };\n          localUsers.push(newUser);\n          localStorage.setItem('localUsers', JSON.stringify(localUsers));\n        }\n        message.success(editingUser ? '用户已更新（本地存储）' : '用户已添加（本地存储）');\n        setModalVisible(false);\n        form.resetFields();\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('保存用户失败:', error);\n      message.error('表单验证失败');\n    }\n  };\n  const handleModalCancel = () => {\n    setModalVisible(false);\n  };\n  const columns = [{\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username'\n  }, {\n    title: '角色',\n    dataIndex: 'role',\n    key: 'role',\n    render: role => {\n      const roleMap = {\n        'admin': '管理员',\n        'monitor': '监控人员',\n        'user': '普通用户',\n        'maintenance': '设备维护人员'\n      };\n      return roleMap[role] || role;\n    }\n  }, {\n    title: '邮箱',\n    dataIndex: 'email',\n    key: 'email'\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    render: date => date ? new Date(date).toLocaleString() : '-'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleEditUser(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        danger: true,\n        onClick: () => handleDeleteUser(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 在组件内添加路口管理相关函数\n  const handleAddIntersection = () => {\n    setEditingIntersection(null);\n    intersectionForm.resetFields();\n    setIntersectionModalVisible(true);\n  };\n  const handleEditIntersection = intersection => {\n    setEditingIntersection(intersection);\n    intersectionForm.setFieldsValue({\n      ...intersection,\n      entrances: intersection.entrances || []\n    });\n    setIntersectionModalVisible(true);\n  };\n  const handleDeleteIntersection = async id => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      await axios.delete(`${apiUrl}/api/intersections/${id}`);\n      message.success('路口删除成功');\n      // 更新路口列表\n      const updatedIntersections = intersections.filter(item => item.id !== id);\n      setIntersections(updatedIntersections);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('删除路口失败:', error);\n      message.error('删除路口失败: ' + (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || '未知错误'));\n    }\n  };\n\n  // 获取路口列表\n  const fetchIntersections = async () => {\n    try {\n      setLoading(true);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/intersections`);\n      if (response.data && response.data.data) {\n        // 确保所有路口都有interId字段\n        const updatedIntersections = response.data.data.map(item => ({\n          ...item,\n          interId: item.interId || `I${item.id.substr(-3)}`\n        }));\n        setIntersections(updatedIntersections);\n      }\n    } catch (error) {\n      console.error('获取路口列表失败:', error);\n      message.error('获取路口列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 更新handleIntersectionModalOk函数\n  const handleIntersectionModalOk = async () => {\n    try {\n      const values = await intersectionForm.validateFields();\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n      // 确保hasTrafficLight字段被正确处理为布尔值\n      if (values.hasTrafficLight !== undefined) {\n        values.hasTrafficLight = Boolean(values.hasTrafficLight);\n      }\n      if (editingIntersection) {\n        // 更新路口\n        const response = await axios.put(`${apiUrl}/api/intersections/${editingIntersection.id}`, values);\n        if (response.data && response.data.success) {\n          message.success('路口更新成功');\n          // 更新路口列表\n          const updatedIntersections = intersections.map(item => item.id === editingIntersection.id ? {\n            ...item,\n            ...values\n          } : item);\n          setIntersections(updatedIntersections);\n\n          // 更新本地JSON文件\n          try {\n            // 读取当前的intersections.json内容\n            const existingIntersections = intersectionsData.intersections || [];\n            // 查找并更新对应路口\n            const updatedIntersectionsData = existingIntersections.map(item => item.id === editingIntersection.id ? {\n              ...item,\n              ...values\n            } : item);\n\n            // 尝试通过API更新JSON文件\n            await axios.post(`${apiUrl}/api/config/update`, {\n              file: 'intersections.json',\n              data: {\n                intersections: updatedIntersectionsData\n              }\n            });\n            console.log('路口数据已同步到JSON文件');\n          } catch (jsonError) {\n            console.error('更新JSON文件失败:', jsonError);\n            message.warning('路口数据已更新，但未能同步到配置文件');\n          }\n        }\n      } else {\n        // 添加新路口\n        const newIntersection = {\n          ...values,\n          id: `intersection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n          hasTrafficLight: values.hasTrafficLight === undefined ? false : Boolean(values.hasTrafficLight)\n        };\n\n        // 如果没有提供interId，则自动生成一个\n        if (!newIntersection.interId) {\n          newIntersection.interId = `I${newIntersection.id.substr(-3)}`;\n        }\n        const response = await axios.post(`${apiUrl}/api/intersections`, newIntersection);\n        if (response.data && response.data.success) {\n          message.success('路口添加成功');\n          setIntersections([...intersections, newIntersection]);\n\n          // 更新本地JSON文件\n          try {\n            // 读取当前的intersections.json内容\n            const existingIntersections = intersectionsData.intersections || [];\n            // 添加新路口\n            const updatedIntersectionsData = [...existingIntersections, newIntersection];\n\n            // 尝试通过API更新JSON文件\n            await axios.post(`${apiUrl}/api/config/update`, {\n              file: 'intersections.json',\n              data: {\n                intersections: updatedIntersectionsData\n              }\n            });\n            console.log('新路口数据已添加到JSON文件');\n          } catch (jsonError) {\n            console.error('更新JSON文件失败:', jsonError);\n            message.warning('路口已添加，但未能同步到配置文件');\n          }\n        }\n      }\n      setIntersectionModalVisible(false);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('保存路口失败:', error);\n      message.error('保存路口失败: ' + (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || '未知错误'));\n    }\n  };\n\n  // 修改 renderContent 函数\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'users':\n        return /*#__PURE__*/_jsxDEV(ContentArea, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'flex-end',\n              marginBottom: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              onClick: handleAddUser,\n              children: \"\\u6DFB\\u52A0\\u7528\\u6237\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Table, {\n            loading: loading,\n            dataSource: users,\n            columns: columns,\n            rowKey: \"id\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this);\n      case 'devices':\n        return /*#__PURE__*/_jsxDEV(DeviceManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 16\n        }, this);\n      case 'intersections':\n        return /*#__PURE__*/_jsxDEV(ContentArea, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'flex-end',\n              marginBottom: 32\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 12\n          }, this), /*#__PURE__*/_jsxDEV(Table, {\n            loading: loading,\n            dataSource: intersections,\n            columns: [{\n              title: '路口名称',\n              dataIndex: 'name',\n              key: 'name'\n            }, {\n              title: '路口ID',\n              dataIndex: 'interId',\n              key: 'interId'\n            }, {\n              title: '纬度',\n              dataIndex: 'latitude',\n              key: 'latitude'\n            }, {\n              title: '经度',\n              dataIndex: 'longitude',\n              key: 'longitude'\n            }, {\n              title: '是否有红绿灯',\n              dataIndex: 'hasTrafficLight',\n              key: 'hasTrafficLight',\n              render: hasLight => hasLight ? '有' : '无'\n            }, {\n              title: '操作',\n              key: 'action',\n              render: (_, record) => /*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  onClick: () => handleEditIntersection(record),\n                  children: \"\\u7F16\\u8F91\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 21\n              }, this)\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this);\n      case 'vehicles':\n        return /*#__PURE__*/_jsxDEV(VehicleManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(SystemContainer, {\n    children: [/*#__PURE__*/_jsxDEV(TabsContainer, {\n      children: [/*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'users',\n        onClick: () => handleTabChange('users'),\n        children: \"\\u7528\\u6237\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'devices',\n        onClick: () => handleTabChange('devices'),\n        children: \"\\u8BBE\\u5907\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'intersections',\n        onClick: () => handleTabChange('intersections'),\n        children: \"\\u8DEF\\u53E3\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'vehicles',\n        onClick: () => handleTabChange('vehicles'),\n        children: \"\\u8F66\\u8F86\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 641,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContentArea, {\n      children: renderContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 649,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingUser ? \"编辑用户\" : \"添加用户\",\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: handleModalCancel,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          role: 'user'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"username\",\n          label: \"\\u7528\\u6237\\u540D\",\n          rules: [{\n            required: true,\n            message: '请输入用户名'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 28\n            }, this),\n            placeholder: \"\\u7528\\u6237\\u540D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 11\n        }, this), !editingUser && /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          label: \"\\u5BC6\\u7801\",\n          rules: [{\n            required: true,\n            message: '请输入密码'\n          }, {\n            min: 6,\n            message: '密码长度至少为6个字符'\n          }],\n          extra: \"\\u5BC6\\u7801\\u957F\\u5EA6\\u81F3\\u5C11\\u4E3A6\\u4E2A\\u5B57\\u7B26\",\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 39\n            }, this),\n            placeholder: \"\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"role\",\n          label: \"\\u89D2\\u8272\",\n          rules: [{\n            required: true,\n            message: '请选择角色'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u89D2\\u8272\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"admin\",\n              children: \"\\u7BA1\\u7406\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"monitor\",\n              children: \"\\u76D1\\u63A7\\u4EBA\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"user\",\n              children: \"\\u666E\\u901A\\u7528\\u6237\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"maintenance\",\n              children: \"\\u8BBE\\u5907\\u7EF4\\u62A4\\u4EBA\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 689,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"email\",\n          label: \"\\u90AE\\u7BB1\",\n          rules: [{\n            type: 'email',\n            message: '请输入有效的邮箱地址'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 28\n            }, this),\n            placeholder: \"\\u90AE\\u7BB1\\uFF08\\u9009\\u586B\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 660,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 653,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingIntersection ? '编辑路口' : '添加路口',\n      open: intersectionModalVisible,\n      onOk: handleIntersectionModalOk,\n      onCancel: () => setIntersectionModalVisible(false),\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: intersectionForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8DEF\\u53E3\\u540D\\u79F0\"\n          // rules={[{ required: true, message: '请输入路口名称' }]}\n          ,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            disabled: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"interId\",\n          label: \"\\u8DEF\\u53E3ID\",\n          rules: [{\n            required: true,\n            message: '请输入路口ID'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8DEF\\u53E3ID\\uFF08\\u59821\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 733,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"latitude\",\n          label: \"\\u7EAC\\u5EA6\",\n          rules: [{\n            required: true,\n            message: '请输入纬度'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u7EAC\\u5EA6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"longitude\",\n          label: \"\\u7ECF\\u5EA6\",\n          rules: [{\n            required: true,\n            message: '请输入经度'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u7ECF\\u5EA6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 747,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"hasTrafficLight\",\n          label: \"\\u662F\\u5426\\u6709\\u7EA2\\u7EFF\\u706F\",\n          valuePropName: \"checked\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u662F\\u5426\\u6709\\u7EA2\\u7EFF\\u706F\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: true,\n              children: \"\\u6709\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: false,\n              children: \"\\u65E0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 754,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.List, {\n          name: \"entrances\",\n          children: fields => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: 'bold',\n                margin: '8px 0 4px 0'\n              },\n              children: \"\\u8FDB\\u53E3\\u4FE1\\u606F\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 17\n            }, this), fields.map(({\n              key,\n              name,\n              ...restField\n            }) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: 8,\n                marginBottom: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                ...restField,\n                name: [name, 'name'],\n                label: \"\\u8FDB\\u53E3\\u540D\\u79F0\",\n                style: {\n                  flex: 1,\n                  marginBottom: 0\n                },\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  disabled: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                ...restField,\n                name: [name, 'latitude'],\n                label: \"\\u7EAC\\u5EA6\",\n                style: {\n                  flex: 1,\n                  marginBottom: 0\n                },\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u7EAC\\u5EA6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 784,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                ...restField,\n                name: [name, 'longitude'],\n                label: \"\\u7ECF\\u5EA6\",\n                style: {\n                  flex: 1,\n                  marginBottom: 0\n                },\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u7ECF\\u5EA6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 792,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 786,\n                columnNumber: 21\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 769,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 764,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 721,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 715,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 621,\n    columnNumber: 5\n  }, this);\n};\n_s(SystemManagement, \"GzMHodrygKCeoqlqfsdpYbV2cEA=\", false, function () {\n  return [useNavigate, Form.useForm, Form.useForm];\n});\n_c5 = SystemManagement;\nexport default SystemManagement;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"SystemContainer\");\n$RefreshReg$(_c2, \"TabsContainer\");\n$RefreshReg$(_c3, \"TabButton\");\n$RefreshReg$(_c4, \"ContentArea\");\n$RefreshReg$(_c5, \"SystemManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "message", "Card", "Result", "UserOutlined", "LockOutlined", "MailOutlined", "StopOutlined", "axios", "DeviceManagement", "styled", "devicesData", "intersectionsData", "VehicleManagement", "useNavigate", "jsxDEV", "_jsxDEV", "Option", "SystemContainer", "div", "_c", "TabsContainer", "_c2", "TabButton", "button", "props", "active", "_c3", "ContentArea", "_c4", "SystemManagement", "_s", "navigate", "loading", "setLoading", "users", "setUsers", "modalVisible", "setModalVisible", "form", "useForm", "editingUser", "setEditingUser", "activeTab", "setActiveTab", "intersections", "setIntersections", "intersectionModalVisible", "setIntersectionModalVisible", "editingIntersection", "setEditingIntersection", "intersectionForm", "hasPermission", "setHasPermission", "checkPermission", "_userData$user", "userInfo", "localStorage", "getItem", "userData", "JSON", "parse", "userRole", "role", "user", "error", "console", "fetchUsers", "log", "token", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "headers", "data", "success", "_response$data", "warning", "fetchIntersections", "uniqueLocations", "Set", "devices", "filter", "device", "type", "map", "location", "existingIntersections", "mergedIntersections", "existingIntersection", "find", "i", "name", "newIntersection", "id", "Date", "now", "Math", "random", "toString", "substr", "latitude", "longitude", "createdAt", "toISOString", "post", "catch", "children", "status", "title", "subTitle", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "extra", "onClick", "handleTabChange", "tab", "deviceManagementRef", "document", "querySelector", "fetchDevices", "handleAddUser", "resetFields", "handleEditUser", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "username", "email", "handleDeleteUser", "userId", "delete", "localUsers", "updatedUsers", "setItem", "stringify", "handleModalOk", "values", "validateFields", "put", "_response$data2", "newUser", "push", "handleModalCancel", "columns", "dataIndex", "key", "render", "roleMap", "date", "toLocaleString", "_", "record", "danger", "handleAddIntersection", "handleEditIntersection", "intersection", "entrances", "handleDeleteIntersection", "updatedIntersections", "item", "_error$response", "_error$response$data", "interId", "handleIntersectionModalOk", "hasTrafficLight", "undefined", "Boolean", "updatedIntersectionsData", "file", "jsonError", "updatedAt", "_error$response2", "_error$response2$data", "renderContent", "style", "display", "justifyContent", "marginBottom", "dataSource", "<PERSON><PERSON><PERSON>", "hasLight", "open", "onOk", "onCancel", "destroyOnClose", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "prefix", "placeholder", "min", "Password", "value", "disabled", "valuePropName", "List", "fields", "fontWeight", "margin", "restField", "gap", "flex", "_c5", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/SystemManagement.jsx"], "sourcesContent": ["// src/pages/SystemManagement.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, message, Card, Result } from 'antd';\nimport { UserOutlined, LockOutlined, MailOutlined, StopOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport DeviceManagement from './DeviceManagement';\nimport styled from 'styled-components';\nimport devicesData from '../data/devices.json';\nimport intersectionsData from '../data/intersections.json';\nimport VehicleManagement from './VehicleManagement';\nimport { useNavigate } from 'react-router-dom';\n\nconst { Option } = Select;\n\n// 修改容器样式，确保整个组件在3D模型上层\nconst SystemContainer = styled.div`\n  position: relative;\n  z-index: 1002;\n  background: white;\n  pointer-events: auto;\n`;\n\nconst TabsContainer = styled.div`\n  display: flex;\n  margin-bottom: 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: white;\n  position: relative;\n  z-index: 1002;\n  pointer-events: auto;\n`;\n\nconst TabButton = styled.button`\n  padding: 10px 20px;\n  background: ${props => props.active ? '#e6f7ff' : 'white'};\n  border: none;\n  border-bottom: 2px solid ${props => props.active ? '#1890ff' : 'transparent'};\n  color: ${props => props.active ? '#1890ff' : 'rgba(0, 0, 0, 0.65)'};\n  cursor: pointer;\n  font-size: 14px;\n  margin-right: 20px;\n  transition: all 0.3s;\n  pointer-events: auto;\n  \n  &:hover {\n    color: #1890ff;\n  }\n  \n  &:focus {\n    outline: none;\n  }\n`;\n\nconst ContentArea = styled.div`\n  // padding: 24px;\n  background: white;\n  position: relative;\n  z-index: 1002;\n  // pointer-events: auto;\n`;\n\nconst SystemManagement = () => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [users, setUsers] = useState([]);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingUser, setEditingUser] = useState(null);\n  const [activeTab, setActiveTab] = useState('users');\n  const [intersections, setIntersections] = useState([]);\n  const [intersectionModalVisible, setIntersectionModalVisible] = useState(false);\n  const [editingIntersection, setEditingIntersection] = useState(null);\n  const [intersectionForm] = Form.useForm();\n  const [hasPermission, setHasPermission] = useState(false);\n\n  // 检查用户权限\n  useEffect(() => {\n    const checkPermission = () => {\n      try {\n        const userInfo = localStorage.getItem('user');\n        if (!userInfo) {\n          setHasPermission(false);\n          return;\n        }\n\n        const userData = JSON.parse(userInfo);\n        const userRole = userData.role || userData.user?.role || 'user';\n        \n        if (userRole === 'admin') {\n          setHasPermission(true);\n        } else {\n          setHasPermission(false);\n          message.error('您没有权限访问系统管理页面');\n        }\n      } catch (error) {\n        console.error('检查权限失败:', error);\n        setHasPermission(false);\n      }\n    };\n\n    checkPermission();\n  }, []);\n\n  // 获取用户列表\n  const fetchUsers = async () => {\n    if (!hasPermission) return; // 没有权限时不请求数据\n    \n    try {\n      setLoading(true);\n      console.log('开始获取用户列表...');\n      \n      const token = localStorage.getItem('token');\n      console.log('使用令牌获取用户列表:', token);\n      \n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/users`, {\n        headers: { \n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      \n      console.log('API响应:', response.data);\n      \n      if (response.data && response.data.success) {\n        setUsers(response.data.data || []);\n      } else {\n        message.warning('获取用户列表失败: ' + (response.data?.message || '未知错误'));\n      }\n    } catch (error) {\n      console.error('API获取用户列表失败:', error);\n      message.error('获取用户列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 提前合并两个useEffect以避免条件渲染导致的钩子数量不一致\n  useEffect(() => {\n    if (hasPermission) {\n      fetchUsers();\n      fetchIntersections();\n      \n      // 从 devices.json 中获取唯一的路口名称，排除 OBU 类型设备的位置\n      const uniqueLocations = [...new Set(\n        devicesData.devices\n          .filter(device => device.type !== 'obu') // 排除 OBU 类型设备\n          .map(device => device.location)\n      )];\n      \n      // 从 intersections.json 中读取现有的路口数据\n      const existingIntersections = intersectionsData.intersections || [];\n      \n      // 将路口名称与已有的路口数据合并\n      const mergedIntersections = uniqueLocations.map(location => {\n        const existingIntersection = existingIntersections.find(i => i.name === location);\n        if (existingIntersection) {\n          return existingIntersection;\n        }\n        \n        // 如果是新路口，先保存到 intersections.json\n        const newIntersection = {\n          id: `intersection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          name: location,\n          latitude: '',\n          longitude: '',\n          createdAt: new Date().toISOString()\n        };\n        \n        // 调用 API 保存新路口\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        axios.post(`${apiUrl}/api/intersections`, newIntersection)\n          .catch(error => {\n            console.error('保存新路口失败:', error);\n          });\n        \n        return newIntersection;\n      });\n      \n      setIntersections(mergedIntersections);\n    }\n  }, [activeTab, hasPermission]);\n\n  // 渲染无权限页面或主要内容\n  if (!hasPermission) {\n    return (\n      <SystemContainer>\n        <Result\n          status=\"403\"\n          title=\"403\"\n          subTitle=\"抱歉，您没有权限访问此页面\"\n          icon={<StopOutlined />}\n          extra={\n            <Button type=\"primary\" onClick={() => navigate('/')}>\n              返回首页\n            </Button>\n          }\n        />\n      </SystemContainer>\n    );\n  }\n\n  // 以下代码只有在hasPermission为true时才会执行\n\n  // 修改标签切换处理函数\n  const handleTabChange = (tab) => {\n    console.log('切换到标签:', tab);\n    setActiveTab(tab);\n    \n    // 切换到设备管理标签时重新获取设备列表\n    if (tab === 'devices') {\n      const deviceManagementRef = document.querySelector('#device-management');\n      if (deviceManagementRef) {\n        deviceManagementRef.fetchDevices();\n      }\n    }\n  };\n\n  const handleAddUser = () => {\n    setEditingUser(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEditUser = (user) => {\n    setEditingUser(user);\n    form.setFieldsValue({\n      username: user.username,\n      role: user.role,\n      email: user.email,\n    });\n    setModalVisible(true);\n  };\n\n  const handleDeleteUser = async (userId) => {\n    try {\n      const token = localStorage.getItem('token');\n      \n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      \n      // 尝试通过API删除\n      try {\n        await axios.delete(`${apiUrl}/api/users/${userId}`, {\n          headers: { \n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        message.success('用户已删除');\n        fetchUsers(); // 重新获取用户列表\n      } catch (error) {\n        console.error('API删除用户失败:', error);\n        \n        // 如果API删除失败，从本地存储中删除\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        const updatedUsers = localUsers.filter(user => user.id !== userId);\n        localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        \n        message.success('用户已删除（本地存储）');\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('删除用户失败:', error);\n      message.error('删除用户失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      console.log('表单数据:', values);\n      \n      const token = localStorage.getItem('token');\n      \n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      \n      // 尝试通过API保存用户\n      try {\n        let response;\n        \n        if (editingUser) {\n          // 更新用户\n          response = await axios.put(`${apiUrl}/api/users/${editingUser.id}`, values, {\n            headers: { \n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        } else {\n          // 添加用户\n          response = await axios.post(`${apiUrl}/api/users`, values, {\n            headers: { \n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        }\n        \n        console.log('API响应:', response.data);\n        \n        if (response.data && response.data.success) {\n          message.success(editingUser ? '用户已更新' : '用户已添加');\n          setModalVisible(false);\n          form.resetFields();\n          fetchUsers(); // 重新获取用户列表\n        } else {\n          message.error(response.data?.message || '操作失败');\n        }\n      } catch (error) {\n        console.error('API保存用户失败:', error);\n        \n        // 如果API保存失败，保存到本地存储\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        \n        if (editingUser) {\n          // 更新用户\n          const updatedUsers = localUsers.map(user => \n            user.id === editingUser.id ? { ...user, ...values } : user\n          );\n          localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        } else {\n          // 添加用户\n          const newUser = {\n            ...values,\n            id: Date.now().toString(),\n            createdAt: new Date().toISOString()\n          };\n          localUsers.push(newUser);\n          localStorage.setItem('localUsers', JSON.stringify(localUsers));\n        }\n        \n        message.success(editingUser ? '用户已更新（本地存储）' : '用户已添加（本地存储）');\n        setModalVisible(false);\n        form.resetFields();\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('保存用户失败:', error);\n      message.error('表单验证失败');\n    }\n  };\n\n  const handleModalCancel = () => {\n    setModalVisible(false);\n  };\n\n  const columns = [\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n    },\n    {\n      title: '角色',\n      dataIndex: 'role',\n      key: 'role',\n      render: (role) => {\n        const roleMap = {\n          'admin': '管理员',\n          'monitor': '监控人员',\n          'user': '普通用户',\n          'maintenance': '设备维护人员'\n        };\n        return roleMap[role] || role;\n      }\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      render: date => date ? new Date(date).toLocaleString() : '-'\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <div>\n          <Button type=\"link\" onClick={() => handleEditUser(record)}>编辑</Button>\n          <Button type=\"link\" danger onClick={() => handleDeleteUser(record.id)}>删除</Button>\n        </div>\n      ),\n    },\n  ];\n\n  // 在组件内添加路口管理相关函数\n  const handleAddIntersection = () => {\n    setEditingIntersection(null);\n    intersectionForm.resetFields();\n    setIntersectionModalVisible(true);\n  };\n\n  const handleEditIntersection = (intersection) => {\n    setEditingIntersection(intersection);\n    intersectionForm.setFieldsValue({\n      ...intersection,\n      entrances: intersection.entrances || []\n    });\n    setIntersectionModalVisible(true);\n  };\n\n  const handleDeleteIntersection = async (id) => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      await axios.delete(`${apiUrl}/api/intersections/${id}`);\n      message.success('路口删除成功');\n      // 更新路口列表\n      const updatedIntersections = intersections.filter(item => item.id !== id);\n      setIntersections(updatedIntersections);\n    } catch (error) {\n      console.error('删除路口失败:', error);\n      message.error('删除路口失败: ' + (error.response?.data?.message || '未知错误'));\n    }\n  };\n\n  // 获取路口列表\n  const fetchIntersections = async () => {\n    try {\n      setLoading(true);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/intersections`);\n      if (response.data && response.data.data) {\n        // 确保所有路口都有interId字段\n        const updatedIntersections = response.data.data.map(item => ({\n          ...item,\n          interId: item.interId || `I${item.id.substr(-3)}`\n        }));\n        setIntersections(updatedIntersections);\n      }\n    } catch (error) {\n      console.error('获取路口列表失败:', error);\n      message.error('获取路口列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 更新handleIntersectionModalOk函数\n  const handleIntersectionModalOk = async () => {\n    try {\n      const values = await intersectionForm.validateFields();\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n      // 确保hasTrafficLight字段被正确处理为布尔值\n      if (values.hasTrafficLight !== undefined) {\n        values.hasTrafficLight = Boolean(values.hasTrafficLight);\n      }\n\n      if (editingIntersection) {\n        // 更新路口\n        const response = await axios.put(\n          `${apiUrl}/api/intersections/${editingIntersection.id}`,\n          values\n        );\n        if (response.data && response.data.success) {\n          message.success('路口更新成功');\n          // 更新路口列表\n          const updatedIntersections = intersections.map(item =>\n            item.id === editingIntersection.id ? { ...item, ...values } : item\n          );\n          setIntersections(updatedIntersections);\n          \n          // 更新本地JSON文件\n          try {\n            // 读取当前的intersections.json内容\n            const existingIntersections = intersectionsData.intersections || [];\n            // 查找并更新对应路口\n            const updatedIntersectionsData = existingIntersections.map(item => \n              item.id === editingIntersection.id ? { ...item, ...values } : item\n            );\n            \n            // 尝试通过API更新JSON文件\n            await axios.post(`${apiUrl}/api/config/update`, {\n              file: 'intersections.json',\n              data: { intersections: updatedIntersectionsData }\n            });\n            \n            console.log('路口数据已同步到JSON文件');\n          } catch (jsonError) {\n            console.error('更新JSON文件失败:', jsonError);\n            message.warning('路口数据已更新，但未能同步到配置文件');\n          }\n        }\n      } else {\n        // 添加新路口\n        const newIntersection = {\n          ...values,\n          id: `intersection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n          hasTrafficLight: values.hasTrafficLight === undefined ? false : Boolean(values.hasTrafficLight)\n        };\n        \n        // 如果没有提供interId，则自动生成一个\n        if (!newIntersection.interId) {\n          newIntersection.interId = `I${newIntersection.id.substr(-3)}`;\n        }\n        \n        const response = await axios.post(`${apiUrl}/api/intersections`, newIntersection);\n        if (response.data && response.data.success) {\n          message.success('路口添加成功');\n          setIntersections([...intersections, newIntersection]);\n          \n          // 更新本地JSON文件\n          try {\n            // 读取当前的intersections.json内容\n            const existingIntersections = intersectionsData.intersections || [];\n            // 添加新路口\n            const updatedIntersectionsData = [...existingIntersections, newIntersection];\n            \n            // 尝试通过API更新JSON文件\n            await axios.post(`${apiUrl}/api/config/update`, {\n              file: 'intersections.json',\n              data: { intersections: updatedIntersectionsData }\n            });\n            \n            console.log('新路口数据已添加到JSON文件');\n          } catch (jsonError) {\n            console.error('更新JSON文件失败:', jsonError);\n            message.warning('路口已添加，但未能同步到配置文件');\n          }\n        }\n      }\n      setIntersectionModalVisible(false);\n    } catch (error) {\n      console.error('保存路口失败:', error);\n      message.error('保存路口失败: ' + (error.response?.data?.message || '未知错误'));\n    }\n  };\n\n  // 修改 renderContent 函数\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'users':\n        return (\n          <ContentArea>\n            <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 16 }}>\n              <Button type=\"primary\" onClick={handleAddUser}>\n                添加用户\n              </Button>\n            </div>\n            <Table\n              loading={loading}\n              dataSource={users}\n              columns={columns}\n              rowKey=\"id\"\n            />\n          </ContentArea>\n        );\n      case 'devices':\n        return <DeviceManagement />;\n      case 'intersections':\n        return (\n          <ContentArea>\n           <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 32 }}>\n            {/* <Button type=\"primary\" onClick={handleAddIntersection}> */}\n              {/* <Button type=\"primary\" >\n                添加路口\n              </Button> */}\n              </div>\n            <Table\n              loading={loading}\n              dataSource={intersections}\n              columns={[\n                {\n                  title: '路口名称',\n                  dataIndex: 'name',\n                  key: 'name',\n                },\n                {\n                  title: '路口ID',\n                  dataIndex: 'interId',\n                  key: 'interId',\n                },\n                {\n                  title: '纬度',\n                  dataIndex: 'latitude',\n                  key: 'latitude',\n                },\n                {\n                  title: '经度',\n                  dataIndex: 'longitude',\n                  key: 'longitude',\n                },\n                {\n                  title: '是否有红绿灯',\n                  dataIndex: 'hasTrafficLight',\n                  key: 'hasTrafficLight',\n                  render: (hasLight) => hasLight ? '有' : '无'\n                },\n                {\n                  title: '操作',\n                  key: 'action',\n                  render: (_, record) => (\n                    <span>\n                      <Button type=\"link\" onClick={() => handleEditIntersection(record)}>\n                        编辑\n                      </Button>\n                    </span>\n                  ),\n                },\n              ]}\n            />\n          </ContentArea>\n        );\n      case 'vehicles':\n        return <VehicleManagement />;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <SystemContainer>\n      <TabsContainer>\n        <TabButton\n          active={activeTab === 'users'}\n          onClick={() => handleTabChange('users')}\n        >\n          用户管理\n        </TabButton>\n        <TabButton\n          active={activeTab === 'devices'}\n          onClick={() => handleTabChange('devices')}\n        >\n          设备管理\n        </TabButton>\n        <TabButton\n          active={activeTab === 'intersections'}\n          onClick={() => handleTabChange('intersections')}\n        >\n          路口管理\n        </TabButton>\n        <TabButton\n          active={activeTab === 'vehicles'}\n          onClick={() => handleTabChange('vehicles')}\n        >\n          车辆管理\n        </TabButton>\n      </TabsContainer>\n      \n      <ContentArea>\n        {renderContent()}\n      </ContentArea>\n      \n      <Modal\n        title={editingUser ? \"编辑用户\" : \"添加用户\"}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            role: 'user'\n          }}\n        >\n          <Form.Item\n            name=\"username\"\n            label=\"用户名\"\n            rules={[{ required: true, message: '请输入用户名' }]}\n          >\n            <Input prefix={<UserOutlined />} placeholder=\"用户名\" />\n          </Form.Item>\n          \n          {!editingUser && (\n            <Form.Item\n              name=\"password\"\n              label=\"密码\"\n              rules={[\n                { required: true, message: '请输入密码' },\n                { min: 6, message: '密码长度至少为6个字符' }\n              ]}\n              extra=\"密码长度至少为6个字符\"\n            >\n              <Input.Password prefix={<LockOutlined />} placeholder=\"密码\" />\n            </Form.Item>\n          )}\n          \n          <Form.Item\n            name=\"role\"\n            label=\"角色\"\n            rules={[{ required: true, message: '请选择角色' }]}\n          >\n            <Select placeholder=\"选择角色\">\n              <Option value=\"admin\">管理员</Option>\n              <Option value=\"monitor\">监控人员</Option>\n              <Option value=\"user\">普通用户</Option>\n              <Option value=\"maintenance\">设备维护人员</Option>\n            </Select>\n          </Form.Item>\n          \n          <Form.Item\n            name=\"email\"\n            label=\"邮箱\"\n            rules={[\n              { type: 'email', message: '请输入有效的邮箱地址' }\n            ]}\n          >\n            <Input prefix={<MailOutlined />} placeholder=\"邮箱（选填）\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n      \n      {/* 添加路口管理 Modal */}\n      <Modal\n        title={editingIntersection ? '编辑路口' : '添加路口'}\n        open={intersectionModalVisible}\n        onOk={handleIntersectionModalOk}\n        onCancel={() => setIntersectionModalVisible(false)}\n      >\n        <Form\n          form={intersectionForm}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"路口名称\"\n            // rules={[{ required: true, message: '请输入路口名称' }]}\n          >\n            {/* <Input placeholder=\"请输入路口名称\" /> */}\n            <Input disabled />\n          </Form.Item>\n          <Form.Item\n            name=\"interId\"\n            label=\"路口ID\"\n            rules={[{ required: true, message: '请输入路口ID' }]}\n          >\n            <Input placeholder=\"请输入路口ID（如1）\" />\n          </Form.Item>\n          <Form.Item\n            name=\"latitude\"\n            label=\"纬度\"\n            rules={[{ required: true, message: '请输入纬度' }]}\n          >\n            <Input placeholder=\"请输入纬度\" />\n          </Form.Item>\n          <Form.Item\n            name=\"longitude\"\n            label=\"经度\"\n            rules={[{ required: true, message: '请输入经度' }]}\n          >\n            <Input placeholder=\"请输入经度\" />\n          </Form.Item>\n          <Form.Item\n            name=\"hasTrafficLight\"\n            label=\"是否有红绿灯\"\n            valuePropName=\"checked\"\n          >\n            <Select placeholder=\"请选择是否有红绿灯\">\n              <Option value={true}>有</Option>\n              <Option value={false}>无</Option>\n            </Select>\n          </Form.Item>\n          <Form.List name=\"entrances\">\n            {(fields) => (\n              <div>\n                <div style={{ fontWeight: 'bold', margin: '8px 0 4px 0' }}>进口信息：</div>\n                {fields.map(({ key, name, ...restField }) => (\n                  <div key={key} style={{ display: 'flex', gap: 8, marginBottom: 8 }}>\n                    <Form.Item\n                      {...restField}\n                      name={[name, 'name']}\n                      label=\"进口名称\"\n                      style={{ flex: 1, marginBottom: 0 }}\n                    >\n                      <Input disabled />\n                    </Form.Item>\n                    <Form.Item\n                      {...restField}\n                      name={[name, 'latitude']}\n                      label=\"纬度\"\n                      style={{ flex: 1, marginBottom: 0 }}\n                    >\n                      <Input placeholder=\"请输入纬度\" />\n                    </Form.Item>\n                    <Form.Item\n                      {...restField}\n                      name={[name, 'longitude']}\n                      label=\"经度\"\n                      style={{ flex: 1, marginBottom: 0 }}\n                    >\n                      <Input placeholder=\"请输入经度\" />\n                    </Form.Item>\n                  </div>\n                ))}\n              </div>\n            )}\n          </Form.List>\n        </Form>\n      </Modal>\n    </SystemContainer>\n  );\n};\n\nexport default SystemManagement;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,MAAM,QAAQ,MAAM;AACvF,SAASC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC1F,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAM;EAAEC;AAAO,CAAC,GAAGjB,MAAM;;AAEzB;AACA,MAAMkB,eAAe,GAAGR,MAAM,CAACS,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,eAAe;AAOrB,MAAMG,aAAa,GAAGX,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GARID,aAAa;AAUnB,MAAME,SAAS,GAAGb,MAAM,CAACc,MAAM;AAC/B;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,OAAO;AAC3D;AACA,6BAA6BD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,aAAa;AAC9E,WAAWD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,qBAAqB;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnBIJ,SAAS;AAqBf,MAAMK,WAAW,GAAGlB,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GANID,WAAW;AAQjB,MAAME,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8C,IAAI,CAAC,GAAGzC,IAAI,CAAC0C,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkD,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsD,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACwD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAAC0D,gBAAgB,CAAC,GAAGrD,IAAI,CAAC0C,OAAO,CAAC,CAAC;EACzC,MAAM,CAACY,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM4D,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAI;QAAA,IAAAC,cAAA;QACF,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC7C,IAAI,CAACF,QAAQ,EAAE;UACbH,gBAAgB,CAAC,KAAK,CAAC;UACvB;QACF;QAEA,MAAMM,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;QACrC,MAAMM,QAAQ,GAAGH,QAAQ,CAACI,IAAI,MAAAR,cAAA,GAAII,QAAQ,CAACK,IAAI,cAAAT,cAAA,uBAAbA,cAAA,CAAeQ,IAAI,KAAI,MAAM;QAE/D,IAAID,QAAQ,KAAK,OAAO,EAAE;UACxBT,gBAAgB,CAAC,IAAI,CAAC;QACxB,CAAC,MAAM;UACLA,gBAAgB,CAAC,KAAK,CAAC;UACvBpD,OAAO,CAACgE,KAAK,CAAC,eAAe,CAAC;QAChC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/BZ,gBAAgB,CAAC,KAAK,CAAC;MACzB;IACF,CAAC;IAEDC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMa,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACf,aAAa,EAAE,OAAO,CAAC;;IAE5B,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAChBgC,OAAO,CAACE,GAAG,CAAC,aAAa,CAAC;MAE1B,MAAMC,KAAK,GAAGZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3CQ,OAAO,CAACE,GAAG,CAAC,aAAa,EAAEC,KAAK,CAAC;;MAEjC;MACA,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMlE,KAAK,CAACmE,GAAG,CAAC,GAAGL,MAAM,YAAY,EAAE;QACtDM,OAAO,EAAE;UACP,eAAe,EAAE,UAAUP,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEFH,OAAO,CAACE,GAAG,CAAC,QAAQ,EAAEM,QAAQ,CAACG,IAAI,CAAC;MAEpC,IAAIH,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;QAC1C1C,QAAQ,CAACsC,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACpC,CAAC,MAAM;QAAA,IAAAE,cAAA;QACL9E,OAAO,CAAC+E,OAAO,CAAC,YAAY,IAAI,EAAAD,cAAA,GAAAL,QAAQ,CAACG,IAAI,cAAAE,cAAA,uBAAbA,cAAA,CAAe9E,OAAO,KAAI,MAAM,CAAC,CAAC;MACpE;IACF,CAAC,CAAC,OAAOgE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpChE,OAAO,CAACgE,KAAK,CAAC,YAAY,IAAIA,KAAK,CAAChE,OAAO,IAAI,MAAM,CAAC,CAAC;IACzD,CAAC,SAAS;MACRiC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAxC,SAAS,CAAC,MAAM;IACd,IAAI0D,aAAa,EAAE;MACjBe,UAAU,CAAC,CAAC;MACZc,kBAAkB,CAAC,CAAC;;MAEpB;MACA,MAAMC,eAAe,GAAG,CAAC,GAAG,IAAIC,GAAG,CACjCxE,WAAW,CAACyE,OAAO,CAChBC,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,KAAK,CAAC,CAAC;MAAA,CACxCC,GAAG,CAACF,MAAM,IAAIA,MAAM,CAACG,QAAQ,CAClC,CAAC,CAAC;;MAEF;MACA,MAAMC,qBAAqB,GAAG9E,iBAAiB,CAACiC,aAAa,IAAI,EAAE;;MAEnE;MACA,MAAM8C,mBAAmB,GAAGT,eAAe,CAACM,GAAG,CAACC,QAAQ,IAAI;QAC1D,MAAMG,oBAAoB,GAAGF,qBAAqB,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKN,QAAQ,CAAC;QACjF,IAAIG,oBAAoB,EAAE;UACxB,OAAOA,oBAAoB;QAC7B;;QAEA;QACA,MAAMI,eAAe,GAAG;UACtBC,EAAE,EAAE,gBAAgBC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAC3ER,IAAI,EAAEN,QAAQ;UACde,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAE,EAAE;UACbC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;QACpC,CAAC;;QAED;QACA,MAAMrC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;QACvEjE,KAAK,CAACoG,IAAI,CAAC,GAAGtC,MAAM,oBAAoB,EAAE0B,eAAe,CAAC,CACvDa,KAAK,CAAC5C,KAAK,IAAI;UACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;QAClC,CAAC,CAAC;QAEJ,OAAO+B,eAAe;MACxB,CAAC,CAAC;MAEFlD,gBAAgB,CAAC6C,mBAAmB,CAAC;IACvC;EACF,CAAC,EAAE,CAAChD,SAAS,EAAES,aAAa,CAAC,CAAC;;EAE9B;EACA,IAAI,CAACA,aAAa,EAAE;IAClB,oBACEpC,OAAA,CAACE,eAAe;MAAA4F,QAAA,eACd9F,OAAA,CAACb,MAAM;QACL4G,MAAM,EAAC,KAAK;QACZC,KAAK,EAAC,KAAK;QACXC,QAAQ,EAAC,gFAAe;QACxBC,IAAI,eAAElG,OAAA,CAACT,YAAY;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBC,KAAK,eACHvG,OAAA,CAACpB,MAAM;UAAC2F,IAAI,EAAC,SAAS;UAACiC,OAAO,EAAEA,CAAA,KAAMxF,QAAQ,CAAC,GAAG,CAAE;UAAA8E,QAAA,EAAC;QAErD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACa,CAAC;EAEtB;;EAEA;;EAEA;EACA,MAAMG,eAAe,GAAIC,GAAG,IAAK;IAC/BxD,OAAO,CAACE,GAAG,CAAC,QAAQ,EAAEsD,GAAG,CAAC;IAC1B9E,YAAY,CAAC8E,GAAG,CAAC;;IAEjB;IACA,IAAIA,GAAG,KAAK,SAAS,EAAE;MACrB,MAAMC,mBAAmB,GAAGC,QAAQ,CAACC,aAAa,CAAC,oBAAoB,CAAC;MACxE,IAAIF,mBAAmB,EAAE;QACvBA,mBAAmB,CAACG,YAAY,CAAC,CAAC;MACpC;IACF;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BrF,cAAc,CAAC,IAAI,CAAC;IACpBH,IAAI,CAACyF,WAAW,CAAC,CAAC;IAClB1F,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM2F,cAAc,GAAIjE,IAAI,IAAK;IAC/BtB,cAAc,CAACsB,IAAI,CAAC;IACpBzB,IAAI,CAAC2F,cAAc,CAAC;MAClBC,QAAQ,EAAEnE,IAAI,CAACmE,QAAQ;MACvBpE,IAAI,EAAEC,IAAI,CAACD,IAAI;MACfqE,KAAK,EAAEpE,IAAI,CAACoE;IACd,CAAC,CAAC;IACF9F,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM+F,gBAAgB,GAAG,MAAOC,MAAM,IAAK;IACzC,IAAI;MACF,MAAMjE,KAAK,GAAGZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAMY,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;MAEvE;MACA,IAAI;QACF,MAAMjE,KAAK,CAAC+H,MAAM,CAAC,GAAGjE,MAAM,cAAcgE,MAAM,EAAE,EAAE;UAClD1D,OAAO,EAAE;YACP,eAAe,EAAE,UAAUP,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QACFpE,OAAO,CAAC6E,OAAO,CAAC,OAAO,CAAC;QACxBX,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,OAAOF,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;;QAElC;QACA,MAAMuE,UAAU,GAAG5E,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;QACzE,MAAM+E,YAAY,GAAGD,UAAU,CAACnD,MAAM,CAACrB,IAAI,IAAIA,IAAI,CAACiC,EAAE,KAAKqC,MAAM,CAAC;QAClE7E,YAAY,CAACiF,OAAO,CAAC,YAAY,EAAE9E,IAAI,CAAC+E,SAAS,CAACF,YAAY,CAAC,CAAC;QAEhExI,OAAO,CAAC6E,OAAO,CAAC,aAAa,CAAC;QAC9BX,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BhE,OAAO,CAACgE,KAAK,CAAC,UAAU,IAAIA,KAAK,CAAChE,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;EAED,MAAM2I,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMtG,IAAI,CAACuG,cAAc,CAAC,CAAC;MAC1C5E,OAAO,CAACE,GAAG,CAAC,OAAO,EAAEyE,MAAM,CAAC;MAE5B,MAAMxE,KAAK,GAAGZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAMY,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;MAEvE;MACA,IAAI;QACF,IAAIC,QAAQ;QAEZ,IAAIjC,WAAW,EAAE;UACf;UACAiC,QAAQ,GAAG,MAAMlE,KAAK,CAACuI,GAAG,CAAC,GAAGzE,MAAM,cAAc7B,WAAW,CAACwD,EAAE,EAAE,EAAE4C,MAAM,EAAE;YAC1EjE,OAAO,EAAE;cACP,eAAe,EAAE,UAAUP,KAAK,EAAE;cAClC,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAK,QAAQ,GAAG,MAAMlE,KAAK,CAACoG,IAAI,CAAC,GAAGtC,MAAM,YAAY,EAAEuE,MAAM,EAAE;YACzDjE,OAAO,EAAE;cACP,eAAe,EAAE,UAAUP,KAAK,EAAE;cAClC,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;QACJ;QAEAH,OAAO,CAACE,GAAG,CAAC,QAAQ,EAAEM,QAAQ,CAACG,IAAI,CAAC;QAEpC,IAAIH,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;UAC1C7E,OAAO,CAAC6E,OAAO,CAACrC,WAAW,GAAG,OAAO,GAAG,OAAO,CAAC;UAChDH,eAAe,CAAC,KAAK,CAAC;UACtBC,IAAI,CAACyF,WAAW,CAAC,CAAC;UAClB7D,UAAU,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,MAAM;UAAA,IAAA6E,eAAA;UACL/I,OAAO,CAACgE,KAAK,CAAC,EAAA+E,eAAA,GAAAtE,QAAQ,CAACG,IAAI,cAAAmE,eAAA,uBAAbA,eAAA,CAAe/I,OAAO,KAAI,MAAM,CAAC;QACjD;MACF,CAAC,CAAC,OAAOgE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;;QAElC;QACA,MAAMuE,UAAU,GAAG5E,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;QAEzE,IAAIjB,WAAW,EAAE;UACf;UACA,MAAMgG,YAAY,GAAGD,UAAU,CAAChD,GAAG,CAACxB,IAAI,IACtCA,IAAI,CAACiC,EAAE,KAAKxD,WAAW,CAACwD,EAAE,GAAG;YAAE,GAAGjC,IAAI;YAAE,GAAG6E;UAAO,CAAC,GAAG7E,IACxD,CAAC;UACDP,YAAY,CAACiF,OAAO,CAAC,YAAY,EAAE9E,IAAI,CAAC+E,SAAS,CAACF,YAAY,CAAC,CAAC;QAClE,CAAC,MAAM;UACL;UACA,MAAMQ,OAAO,GAAG;YACd,GAAGJ,MAAM;YACT5C,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACG,QAAQ,CAAC,CAAC;YACzBI,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;UACpC,CAAC;UACD6B,UAAU,CAACU,IAAI,CAACD,OAAO,CAAC;UACxBxF,YAAY,CAACiF,OAAO,CAAC,YAAY,EAAE9E,IAAI,CAAC+E,SAAS,CAACH,UAAU,CAAC,CAAC;QAChE;QAEAvI,OAAO,CAAC6E,OAAO,CAACrC,WAAW,GAAG,aAAa,GAAG,aAAa,CAAC;QAC5DH,eAAe,CAAC,KAAK,CAAC;QACtBC,IAAI,CAACyF,WAAW,CAAC,CAAC;QAClB7D,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BhE,OAAO,CAACgE,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,MAAMkF,iBAAiB,GAAGA,CAAA,KAAM;IAC9B7G,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAM8G,OAAO,GAAG,CACd;IACEpC,KAAK,EAAE,KAAK;IACZqC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEtC,KAAK,EAAE,IAAI;IACXqC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGxF,IAAI,IAAK;MAChB,MAAMyF,OAAO,GAAG;QACd,OAAO,EAAE,KAAK;QACd,SAAS,EAAE,MAAM;QACjB,MAAM,EAAE,MAAM;QACd,aAAa,EAAE;MACjB,CAAC;MACD,OAAOA,OAAO,CAACzF,IAAI,CAAC,IAAIA,IAAI;IAC9B;EACF,CAAC,EACD;IACEiD,KAAK,EAAE,IAAI;IACXqC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE;EACP,CAAC,EACD;IACEtC,KAAK,EAAE,MAAM;IACbqC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAEE,IAAI,IAAIA,IAAI,GAAG,IAAIvD,IAAI,CAACuD,IAAI,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG;EAC3D,CAAC,EACD;IACE1C,KAAK,EAAE,IAAI;IACXsC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACI,CAAC,EAAEC,MAAM,kBAChB5I,OAAA;MAAA8F,QAAA,gBACE9F,OAAA,CAACpB,MAAM;QAAC2F,IAAI,EAAC,MAAM;QAACiC,OAAO,EAAEA,CAAA,KAAMS,cAAc,CAAC2B,MAAM,CAAE;QAAA9C,QAAA,EAAC;MAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACtEtG,OAAA,CAACpB,MAAM;QAAC2F,IAAI,EAAC,MAAM;QAACsE,MAAM;QAACrC,OAAO,EAAEA,CAAA,KAAMa,gBAAgB,CAACuB,MAAM,CAAC3D,EAAE,CAAE;QAAAa,QAAA,EAAC;MAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E;EAET,CAAC,CACF;;EAED;EACA,MAAMwC,qBAAqB,GAAGA,CAAA,KAAM;IAClC5G,sBAAsB,CAAC,IAAI,CAAC;IAC5BC,gBAAgB,CAAC6E,WAAW,CAAC,CAAC;IAC9BhF,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAM+G,sBAAsB,GAAIC,YAAY,IAAK;IAC/C9G,sBAAsB,CAAC8G,YAAY,CAAC;IACpC7G,gBAAgB,CAAC+E,cAAc,CAAC;MAC9B,GAAG8B,YAAY;MACfC,SAAS,EAAED,YAAY,CAACC,SAAS,IAAI;IACvC,CAAC,CAAC;IACFjH,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAMkH,wBAAwB,GAAG,MAAOjE,EAAE,IAAK;IAC7C,IAAI;MACF,MAAM3B,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMjE,KAAK,CAAC+H,MAAM,CAAC,GAAGjE,MAAM,sBAAsB2B,EAAE,EAAE,CAAC;MACvDhG,OAAO,CAAC6E,OAAO,CAAC,QAAQ,CAAC;MACzB;MACA,MAAMqF,oBAAoB,GAAGtH,aAAa,CAACwC,MAAM,CAAC+E,IAAI,IAAIA,IAAI,CAACnE,EAAE,KAAKA,EAAE,CAAC;MACzEnD,gBAAgB,CAACqH,oBAAoB,CAAC;IACxC,CAAC,CAAC,OAAOlG,KAAK,EAAE;MAAA,IAAAoG,eAAA,EAAAC,oBAAA;MACdpG,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BhE,OAAO,CAACgE,KAAK,CAAC,UAAU,IAAI,EAAAoG,eAAA,GAAApG,KAAK,CAACS,QAAQ,cAAA2F,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBxF,IAAI,cAAAyF,oBAAA,uBAApBA,oBAAA,CAAsBrK,OAAO,KAAI,MAAM,CAAC,CAAC;IACvE;EACF,CAAC;;EAED;EACA,MAAMgF,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF/C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMoC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMlE,KAAK,CAACmE,GAAG,CAAC,GAAGL,MAAM,oBAAoB,CAAC;MAC/D,IAAII,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACA,IAAI,EAAE;QACvC;QACA,MAAMsF,oBAAoB,GAAGzF,QAAQ,CAACG,IAAI,CAACA,IAAI,CAACW,GAAG,CAAC4E,IAAI,KAAK;UAC3D,GAAGA,IAAI;UACPG,OAAO,EAAEH,IAAI,CAACG,OAAO,IAAI,IAAIH,IAAI,CAACnE,EAAE,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QACHzD,gBAAgB,CAACqH,oBAAoB,CAAC;MACxC;IACF,CAAC,CAAC,OAAOlG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjChE,OAAO,CAACgE,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsI,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI;MACF,MAAM3B,MAAM,GAAG,MAAM1F,gBAAgB,CAAC2F,cAAc,CAAC,CAAC;MACtD,MAAMxE,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;MAEvE;MACA,IAAIoE,MAAM,CAAC4B,eAAe,KAAKC,SAAS,EAAE;QACxC7B,MAAM,CAAC4B,eAAe,GAAGE,OAAO,CAAC9B,MAAM,CAAC4B,eAAe,CAAC;MAC1D;MAEA,IAAIxH,mBAAmB,EAAE;QACvB;QACA,MAAMyB,QAAQ,GAAG,MAAMlE,KAAK,CAACuI,GAAG,CAC9B,GAAGzE,MAAM,sBAAsBrB,mBAAmB,CAACgD,EAAE,EAAE,EACvD4C,MACF,CAAC;QACD,IAAInE,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;UAC1C7E,OAAO,CAAC6E,OAAO,CAAC,QAAQ,CAAC;UACzB;UACA,MAAMqF,oBAAoB,GAAGtH,aAAa,CAAC2C,GAAG,CAAC4E,IAAI,IACjDA,IAAI,CAACnE,EAAE,KAAKhD,mBAAmB,CAACgD,EAAE,GAAG;YAAE,GAAGmE,IAAI;YAAE,GAAGvB;UAAO,CAAC,GAAGuB,IAChE,CAAC;UACDtH,gBAAgB,CAACqH,oBAAoB,CAAC;;UAEtC;UACA,IAAI;YACF;YACA,MAAMzE,qBAAqB,GAAG9E,iBAAiB,CAACiC,aAAa,IAAI,EAAE;YACnE;YACA,MAAM+H,wBAAwB,GAAGlF,qBAAqB,CAACF,GAAG,CAAC4E,IAAI,IAC7DA,IAAI,CAACnE,EAAE,KAAKhD,mBAAmB,CAACgD,EAAE,GAAG;cAAE,GAAGmE,IAAI;cAAE,GAAGvB;YAAO,CAAC,GAAGuB,IAChE,CAAC;;YAED;YACA,MAAM5J,KAAK,CAACoG,IAAI,CAAC,GAAGtC,MAAM,oBAAoB,EAAE;cAC9CuG,IAAI,EAAE,oBAAoB;cAC1BhG,IAAI,EAAE;gBAAEhC,aAAa,EAAE+H;cAAyB;YAClD,CAAC,CAAC;YAEF1G,OAAO,CAACE,GAAG,CAAC,gBAAgB,CAAC;UAC/B,CAAC,CAAC,OAAO0G,SAAS,EAAE;YAClB5G,OAAO,CAACD,KAAK,CAAC,aAAa,EAAE6G,SAAS,CAAC;YACvC7K,OAAO,CAAC+E,OAAO,CAAC,oBAAoB,CAAC;UACvC;QACF;MACF,CAAC,MAAM;QACL;QACA,MAAMgB,eAAe,GAAG;UACtB,GAAG6C,MAAM;UACT5C,EAAE,EAAE,gBAAgBC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAC3EG,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;UACnCoE,SAAS,EAAE,IAAI7E,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;UACnC8D,eAAe,EAAE5B,MAAM,CAAC4B,eAAe,KAAKC,SAAS,GAAG,KAAK,GAAGC,OAAO,CAAC9B,MAAM,CAAC4B,eAAe;QAChG,CAAC;;QAED;QACA,IAAI,CAACzE,eAAe,CAACuE,OAAO,EAAE;UAC5BvE,eAAe,CAACuE,OAAO,GAAG,IAAIvE,eAAe,CAACC,EAAE,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/D;QAEA,MAAM7B,QAAQ,GAAG,MAAMlE,KAAK,CAACoG,IAAI,CAAC,GAAGtC,MAAM,oBAAoB,EAAE0B,eAAe,CAAC;QACjF,IAAItB,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;UAC1C7E,OAAO,CAAC6E,OAAO,CAAC,QAAQ,CAAC;UACzBhC,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAEmD,eAAe,CAAC,CAAC;;UAErD;UACA,IAAI;YACF;YACA,MAAMN,qBAAqB,GAAG9E,iBAAiB,CAACiC,aAAa,IAAI,EAAE;YACnE;YACA,MAAM+H,wBAAwB,GAAG,CAAC,GAAGlF,qBAAqB,EAAEM,eAAe,CAAC;;YAE5E;YACA,MAAMxF,KAAK,CAACoG,IAAI,CAAC,GAAGtC,MAAM,oBAAoB,EAAE;cAC9CuG,IAAI,EAAE,oBAAoB;cAC1BhG,IAAI,EAAE;gBAAEhC,aAAa,EAAE+H;cAAyB;YAClD,CAAC,CAAC;YAEF1G,OAAO,CAACE,GAAG,CAAC,iBAAiB,CAAC;UAChC,CAAC,CAAC,OAAO0G,SAAS,EAAE;YAClB5G,OAAO,CAACD,KAAK,CAAC,aAAa,EAAE6G,SAAS,CAAC;YACvC7K,OAAO,CAAC+E,OAAO,CAAC,kBAAkB,CAAC;UACrC;QACF;MACF;MACAhC,2BAA2B,CAAC,KAAK,CAAC;IACpC,CAAC,CAAC,OAAOiB,KAAK,EAAE;MAAA,IAAA+G,gBAAA,EAAAC,qBAAA;MACd/G,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BhE,OAAO,CAACgE,KAAK,CAAC,UAAU,IAAI,EAAA+G,gBAAA,GAAA/G,KAAK,CAACS,QAAQ,cAAAsG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnG,IAAI,cAAAoG,qBAAA,uBAApBA,qBAAA,CAAsBhL,OAAO,KAAI,MAAM,CAAC,CAAC;IACvE;EACF,CAAC;;EAED;EACA,MAAMiL,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQvI,SAAS;MACf,KAAK,OAAO;QACV,oBACE3B,OAAA,CAACY,WAAW;UAAAkF,QAAA,gBACV9F,OAAA;YAAKmK,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,UAAU;cAAEC,YAAY,EAAE;YAAG,CAAE;YAAAxE,QAAA,eAC5E9F,OAAA,CAACpB,MAAM;cAAC2F,IAAI,EAAC,SAAS;cAACiC,OAAO,EAAEO,aAAc;cAAAjB,QAAA,EAAC;YAE/C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNtG,OAAA,CAACrB,KAAK;YACJsC,OAAO,EAAEA,OAAQ;YACjBsJ,UAAU,EAAEpJ,KAAM;YAClBiH,OAAO,EAAEA,OAAQ;YACjBoC,MAAM,EAAC;UAAI;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAElB,KAAK,SAAS;QACZ,oBAAOtG,OAAA,CAACP,gBAAgB;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7B,KAAK,eAAe;QAClB,oBACEtG,OAAA,CAACY,WAAW;UAAAkF,QAAA,gBACX9F,OAAA;YAAKmK,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,UAAU;cAAEC,YAAY,EAAE;YAAG;UAAE;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKtE,CAAC,eACRtG,OAAA,CAACrB,KAAK;YACJsC,OAAO,EAAEA,OAAQ;YACjBsJ,UAAU,EAAE1I,aAAc;YAC1BuG,OAAO,EAAE,CACP;cACEpC,KAAK,EAAE,MAAM;cACbqC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE;YACP,CAAC,EACD;cACEtC,KAAK,EAAE,MAAM;cACbqC,SAAS,EAAE,SAAS;cACpBC,GAAG,EAAE;YACP,CAAC,EACD;cACEtC,KAAK,EAAE,IAAI;cACXqC,SAAS,EAAE,UAAU;cACrBC,GAAG,EAAE;YACP,CAAC,EACD;cACEtC,KAAK,EAAE,IAAI;cACXqC,SAAS,EAAE,WAAW;cACtBC,GAAG,EAAE;YACP,CAAC,EACD;cACEtC,KAAK,EAAE,QAAQ;cACfqC,SAAS,EAAE,iBAAiB;cAC5BC,GAAG,EAAE,iBAAiB;cACtBC,MAAM,EAAGkC,QAAQ,IAAKA,QAAQ,GAAG,GAAG,GAAG;YACzC,CAAC,EACD;cACEzE,KAAK,EAAE,IAAI;cACXsC,GAAG,EAAE,QAAQ;cACbC,MAAM,EAAEA,CAACI,CAAC,EAAEC,MAAM,kBAChB5I,OAAA;gBAAA8F,QAAA,eACE9F,OAAA,CAACpB,MAAM;kBAAC2F,IAAI,EAAC,MAAM;kBAACiC,OAAO,EAAEA,CAAA,KAAMuC,sBAAsB,CAACH,MAAM,CAAE;kBAAA9C,QAAA,EAAC;gBAEnE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAEV,CAAC;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAElB,KAAK,UAAU;QACb,oBAAOtG,OAAA,CAACH,iBAAiB;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9B;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEtG,OAAA,CAACE,eAAe;IAAA4F,QAAA,gBACd9F,OAAA,CAACK,aAAa;MAAAyF,QAAA,gBACZ9F,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEiB,SAAS,KAAK,OAAQ;QAC9B6E,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAAC,OAAO,CAAE;QAAAX,QAAA,EACzC;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZtG,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEiB,SAAS,KAAK,SAAU;QAChC6E,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAAC,SAAS,CAAE;QAAAX,QAAA,EAC3C;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZtG,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEiB,SAAS,KAAK,eAAgB;QACtC6E,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAAC,eAAe,CAAE;QAAAX,QAAA,EACjD;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZtG,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEiB,SAAS,KAAK,UAAW;QACjC6E,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAAC,UAAU,CAAE;QAAAX,QAAA,EAC5C;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEhBtG,OAAA,CAACY,WAAW;MAAAkF,QAAA,EACToE,aAAa,CAAC;IAAC;MAAA/D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEdtG,OAAA,CAACnB,KAAK;MACJmH,KAAK,EAAEvE,WAAW,GAAG,MAAM,GAAG,MAAO;MACrCiJ,IAAI,EAAErJ,YAAa;MACnBsJ,IAAI,EAAE/C,aAAc;MACpBgD,QAAQ,EAAEzC,iBAAkB;MAC5B0C,cAAc;MAAA/E,QAAA,eAEd9F,OAAA,CAAClB,IAAI;QACHyC,IAAI,EAAEA,IAAK;QACXuJ,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACbhI,IAAI,EAAE;QACR,CAAE;QAAA+C,QAAA,gBAEF9F,OAAA,CAAClB,IAAI,CAACkM,IAAI;UACRjG,IAAI,EAAC,UAAU;UACfkG,KAAK,EAAC,oBAAK;UACXC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAElM,OAAO,EAAE;UAAS,CAAC,CAAE;UAAA6G,QAAA,eAE/C9F,OAAA,CAACjB,KAAK;YAACqM,MAAM,eAAEpL,OAAA,CAACZ,YAAY;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAC+E,WAAW,EAAC;UAAK;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,EAEX,CAAC7E,WAAW,iBACXzB,OAAA,CAAClB,IAAI,CAACkM,IAAI;UACRjG,IAAI,EAAC,UAAU;UACfkG,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAElM,OAAO,EAAE;UAAQ,CAAC,EACpC;YAAEqM,GAAG,EAAE,CAAC;YAAErM,OAAO,EAAE;UAAc,CAAC,CAClC;UACFsH,KAAK,EAAC,+DAAa;UAAAT,QAAA,eAEnB9F,OAAA,CAACjB,KAAK,CAACwM,QAAQ;YAACH,MAAM,eAAEpL,OAAA,CAACX,YAAY;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAC+E,WAAW,EAAC;UAAI;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CACZ,eAEDtG,OAAA,CAAClB,IAAI,CAACkM,IAAI;UACRjG,IAAI,EAAC,MAAM;UACXkG,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAElM,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA6G,QAAA,eAE9C9F,OAAA,CAAChB,MAAM;YAACqM,WAAW,EAAC,0BAAM;YAAAvF,QAAA,gBACxB9F,OAAA,CAACC,MAAM;cAACuL,KAAK,EAAC,OAAO;cAAA1F,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClCtG,OAAA,CAACC,MAAM;cAACuL,KAAK,EAAC,SAAS;cAAA1F,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCtG,OAAA,CAACC,MAAM;cAACuL,KAAK,EAAC,MAAM;cAAA1F,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClCtG,OAAA,CAACC,MAAM;cAACuL,KAAK,EAAC,aAAa;cAAA1F,QAAA,EAAC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZtG,OAAA,CAAClB,IAAI,CAACkM,IAAI;UACRjG,IAAI,EAAC,OAAO;UACZkG,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CACL;YAAE3G,IAAI,EAAE,OAAO;YAAEtF,OAAO,EAAE;UAAa,CAAC,CACxC;UAAA6G,QAAA,eAEF9F,OAAA,CAACjB,KAAK;YAACqM,MAAM,eAAEpL,OAAA,CAACV,YAAY;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAC+E,WAAW,EAAC;UAAQ;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRtG,OAAA,CAACnB,KAAK;MACJmH,KAAK,EAAE/D,mBAAmB,GAAG,MAAM,GAAG,MAAO;MAC7CyI,IAAI,EAAE3I,wBAAyB;MAC/B4I,IAAI,EAAEnB,yBAA0B;MAChCoB,QAAQ,EAAEA,CAAA,KAAM5I,2BAA2B,CAAC,KAAK,CAAE;MAAA8D,QAAA,eAEnD9F,OAAA,CAAClB,IAAI;QACHyC,IAAI,EAAEY,gBAAiB;QACvB2I,MAAM,EAAC,UAAU;QAAAhF,QAAA,gBAEjB9F,OAAA,CAAClB,IAAI,CAACkM,IAAI;UACRjG,IAAI,EAAC,MAAM;UACXkG,KAAK,EAAC;UACN;UAAA;UAAAnF,QAAA,eAGA9F,OAAA,CAACjB,KAAK;YAAC0M,QAAQ;UAAA;YAAAtF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACZtG,OAAA,CAAClB,IAAI,CAACkM,IAAI;UACRjG,IAAI,EAAC,SAAS;UACdkG,KAAK,EAAC,gBAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAElM,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6G,QAAA,eAEhD9F,OAAA,CAACjB,KAAK;YAACsM,WAAW,EAAC;UAAa;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACZtG,OAAA,CAAClB,IAAI,CAACkM,IAAI;UACRjG,IAAI,EAAC,UAAU;UACfkG,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAElM,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA6G,QAAA,eAE9C9F,OAAA,CAACjB,KAAK;YAACsM,WAAW,EAAC;UAAO;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACZtG,OAAA,CAAClB,IAAI,CAACkM,IAAI;UACRjG,IAAI,EAAC,WAAW;UAChBkG,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAElM,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA6G,QAAA,eAE9C9F,OAAA,CAACjB,KAAK;YAACsM,WAAW,EAAC;UAAO;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACZtG,OAAA,CAAClB,IAAI,CAACkM,IAAI;UACRjG,IAAI,EAAC,iBAAiB;UACtBkG,KAAK,EAAC,sCAAQ;UACdS,aAAa,EAAC,SAAS;UAAA5F,QAAA,eAEvB9F,OAAA,CAAChB,MAAM;YAACqM,WAAW,EAAC,wDAAW;YAAAvF,QAAA,gBAC7B9F,OAAA,CAACC,MAAM;cAACuL,KAAK,EAAE,IAAK;cAAA1F,QAAA,EAAC;YAAC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/BtG,OAAA,CAACC,MAAM;cAACuL,KAAK,EAAE,KAAM;cAAA1F,QAAA,EAAC;YAAC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZtG,OAAA,CAAClB,IAAI,CAAC6M,IAAI;UAAC5G,IAAI,EAAC,WAAW;UAAAe,QAAA,EACvB8F,MAAM,iBACN5L,OAAA;YAAA8F,QAAA,gBACE9F,OAAA;cAAKmK,KAAK,EAAE;gBAAE0B,UAAU,EAAE,MAAM;gBAAEC,MAAM,EAAE;cAAc,CAAE;cAAAhG,QAAA,EAAC;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACrEsF,MAAM,CAACpH,GAAG,CAAC,CAAC;cAAE8D,GAAG;cAAEvD,IAAI;cAAE,GAAGgH;YAAU,CAAC,kBACtC/L,OAAA;cAAemK,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAE4B,GAAG,EAAE,CAAC;gBAAE1B,YAAY,EAAE;cAAE,CAAE;cAAAxE,QAAA,gBACjE9F,OAAA,CAAClB,IAAI,CAACkM,IAAI;gBAAA,GACJe,SAAS;gBACbhH,IAAI,EAAE,CAACA,IAAI,EAAE,MAAM,CAAE;gBACrBkG,KAAK,EAAC,0BAAM;gBACZd,KAAK,EAAE;kBAAE8B,IAAI,EAAE,CAAC;kBAAE3B,YAAY,EAAE;gBAAE,CAAE;gBAAAxE,QAAA,eAEpC9F,OAAA,CAACjB,KAAK;kBAAC0M,QAAQ;gBAAA;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACZtG,OAAA,CAAClB,IAAI,CAACkM,IAAI;gBAAA,GACJe,SAAS;gBACbhH,IAAI,EAAE,CAACA,IAAI,EAAE,UAAU,CAAE;gBACzBkG,KAAK,EAAC,cAAI;gBACVd,KAAK,EAAE;kBAAE8B,IAAI,EAAE,CAAC;kBAAE3B,YAAY,EAAE;gBAAE,CAAE;gBAAAxE,QAAA,eAEpC9F,OAAA,CAACjB,KAAK;kBAACsM,WAAW,EAAC;gBAAO;kBAAAlF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACZtG,OAAA,CAAClB,IAAI,CAACkM,IAAI;gBAAA,GACJe,SAAS;gBACbhH,IAAI,EAAE,CAACA,IAAI,EAAE,WAAW,CAAE;gBAC1BkG,KAAK,EAAC,cAAI;gBACVd,KAAK,EAAE;kBAAE8B,IAAI,EAAE,CAAC;kBAAE3B,YAAY,EAAE;gBAAE,CAAE;gBAAAxE,QAAA,eAEpC9F,OAAA,CAACjB,KAAK;kBAACsM,WAAW,EAAC;gBAAO;kBAAAlF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA,GAxBJgC,GAAG;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBR,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEtB,CAAC;AAACvF,EAAA,CAruBID,gBAAgB;EAAA,QACHhB,WAAW,EAIbhB,IAAI,CAAC0C,OAAO,EAMA1C,IAAI,CAAC0C,OAAO;AAAA;AAAA0K,GAAA,GAXnCpL,gBAAgB;AAuuBtB,eAAeA,gBAAgB;AAAC,IAAAV,EAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAqL,GAAA;AAAAC,YAAA,CAAA/L,EAAA;AAAA+L,YAAA,CAAA7L,GAAA;AAAA6L,YAAA,CAAAxL,GAAA;AAAAwL,YAAA,CAAAtL,GAAA;AAAAsL,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}