{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"],\n  _excluded2 = [\"fixed\"];\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { EXPAND_COLUMN } from \"../../constant\";\nimport { INTERNAL_COL_DEFINE } from \"../../utils/legacyUtil\";\nimport useWidthColumns from \"./useWidthColumns\";\nexport function convertChildrenToColumns(children) {\n  return toArray(children).filter(function (node) {\n    return /*#__PURE__*/React.isValidElement(node);\n  }).map(function (_ref) {\n    var key = _ref.key,\n      props = _ref.props;\n    var nodeChildren = props.children,\n      restProps = _objectWithoutProperties(props, _excluded);\n    var column = _objectSpread({\n      key: key\n    }, restProps);\n    if (nodeChildren) {\n      column.children = convertChildrenToColumns(nodeChildren);\n    }\n    return column;\n  });\n}\nfunction filterHiddenColumns(columns) {\n  return columns.filter(function (column) {\n    return column && _typeof(column) === 'object' && !column.hidden;\n  }).map(function (column) {\n    var subColumns = column.children;\n    if (subColumns && subColumns.length > 0) {\n      return _objectSpread(_objectSpread({}, column), {}, {\n        children: filterHiddenColumns(subColumns)\n      });\n    }\n    return column;\n  });\n}\nfunction flatColumns(columns) {\n  var parentKey = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'key';\n  return columns.filter(function (column) {\n    return column && _typeof(column) === 'object';\n  }).reduce(function (list, column, index) {\n    var fixed = column.fixed;\n    // Convert `fixed='true'` to `fixed='left'` instead\n    var parsedFixed = fixed === true ? 'left' : fixed;\n    var mergedKey = \"\".concat(parentKey, \"-\").concat(index);\n    var subColumns = column.children;\n    if (subColumns && subColumns.length > 0) {\n      return [].concat(_toConsumableArray(list), _toConsumableArray(flatColumns(subColumns, mergedKey).map(function (subColum) {\n        return _objectSpread({\n          fixed: parsedFixed\n        }, subColum);\n      })));\n    }\n    return [].concat(_toConsumableArray(list), [_objectSpread(_objectSpread({\n      key: mergedKey\n    }, column), {}, {\n      fixed: parsedFixed\n    })]);\n  }, []);\n}\nfunction revertForRtl(columns) {\n  return columns.map(function (column) {\n    var fixed = column.fixed,\n      restProps = _objectWithoutProperties(column, _excluded2);\n\n    // Convert `fixed='left'` to `fixed='right'` instead\n    var parsedFixed = fixed;\n    if (fixed === 'left') {\n      parsedFixed = 'right';\n    } else if (fixed === 'right') {\n      parsedFixed = 'left';\n    }\n    return _objectSpread({\n      fixed: parsedFixed\n    }, restProps);\n  });\n}\n\n/**\n * Parse `columns` & `children` into `columns`.\n */\nfunction useColumns(_ref2, transformColumns) {\n  var prefixCls = _ref2.prefixCls,\n    columns = _ref2.columns,\n    children = _ref2.children,\n    expandable = _ref2.expandable,\n    expandedKeys = _ref2.expandedKeys,\n    columnTitle = _ref2.columnTitle,\n    getRowKey = _ref2.getRowKey,\n    onTriggerExpand = _ref2.onTriggerExpand,\n    expandIcon = _ref2.expandIcon,\n    rowExpandable = _ref2.rowExpandable,\n    expandIconColumnIndex = _ref2.expandIconColumnIndex,\n    direction = _ref2.direction,\n    expandRowByClick = _ref2.expandRowByClick,\n    columnWidth = _ref2.columnWidth,\n    fixed = _ref2.fixed,\n    scrollWidth = _ref2.scrollWidth,\n    clientWidth = _ref2.clientWidth;\n  var baseColumns = React.useMemo(function () {\n    var newColumns = columns || convertChildrenToColumns(children) || [];\n    return filterHiddenColumns(newColumns.slice());\n  }, [columns, children]);\n\n  // ========================== Expand ==========================\n  var withExpandColumns = React.useMemo(function () {\n    if (expandable) {\n      var cloneColumns = baseColumns.slice();\n\n      // >>> Warning if use `expandIconColumnIndex`\n      if (process.env.NODE_ENV !== 'production' && expandIconColumnIndex >= 0) {\n        warning(false, '`expandIconColumnIndex` is deprecated. Please use `Table.EXPAND_COLUMN` in `columns` instead.');\n      }\n\n      // >>> Insert expand column if not exist\n      if (!cloneColumns.includes(EXPAND_COLUMN)) {\n        var expandColIndex = expandIconColumnIndex || 0;\n        if (expandColIndex >= 0 && (expandColIndex || fixed === 'left' || !fixed)) {\n          cloneColumns.splice(expandColIndex, 0, EXPAND_COLUMN);\n        }\n        if (fixed === 'right') {\n          cloneColumns.splice(baseColumns.length, 0, EXPAND_COLUMN);\n        }\n      }\n\n      // >>> Deduplicate additional expand column\n      if (process.env.NODE_ENV !== 'production' && cloneColumns.filter(function (c) {\n        return c === EXPAND_COLUMN;\n      }).length > 1) {\n        warning(false, 'There exist more than one `EXPAND_COLUMN` in `columns`.');\n      }\n      var expandColumnIndex = cloneColumns.indexOf(EXPAND_COLUMN);\n      cloneColumns = cloneColumns.filter(function (column, index) {\n        return column !== EXPAND_COLUMN || index === expandColumnIndex;\n      });\n\n      // >>> Check if expand column need to fixed\n      var prevColumn = baseColumns[expandColumnIndex];\n      var fixedColumn;\n      if (fixed) {\n        fixedColumn = fixed;\n      } else {\n        fixedColumn = prevColumn ? prevColumn.fixed : null;\n      }\n\n      // >>> Create expandable column\n      var expandColumn = _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, INTERNAL_COL_DEFINE, {\n        className: \"\".concat(prefixCls, \"-expand-icon-col\"),\n        columnType: 'EXPAND_COLUMN'\n      }), \"title\", columnTitle), \"fixed\", fixedColumn), \"className\", \"\".concat(prefixCls, \"-row-expand-icon-cell\")), \"width\", columnWidth), \"render\", function render(_, record, index) {\n        var rowKey = getRowKey(record, index);\n        var expanded = expandedKeys.has(rowKey);\n        var recordExpandable = rowExpandable ? rowExpandable(record) : true;\n        var icon = expandIcon({\n          prefixCls: prefixCls,\n          expanded: expanded,\n          expandable: recordExpandable,\n          record: record,\n          onExpand: onTriggerExpand\n        });\n        if (expandRowByClick) {\n          return /*#__PURE__*/React.createElement(\"span\", {\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            }\n          }, icon);\n        }\n        return icon;\n      });\n      return cloneColumns.map(function (col) {\n        return col === EXPAND_COLUMN ? expandColumn : col;\n      });\n    }\n    if (process.env.NODE_ENV !== 'production' && baseColumns.includes(EXPAND_COLUMN)) {\n      warning(false, '`expandable` is not config but there exist `EXPAND_COLUMN` in `columns`.');\n    }\n    return baseColumns.filter(function (col) {\n      return col !== EXPAND_COLUMN;\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [expandable, baseColumns, getRowKey, expandedKeys, expandIcon, direction]);\n\n  // ========================= Transform ========================\n  var mergedColumns = React.useMemo(function () {\n    var finalColumns = withExpandColumns;\n    if (transformColumns) {\n      finalColumns = transformColumns(finalColumns);\n    }\n\n    // Always provides at least one column for table display\n    if (!finalColumns.length) {\n      finalColumns = [{\n        render: function render() {\n          return null;\n        }\n      }];\n    }\n    return finalColumns;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [transformColumns, withExpandColumns, direction]);\n\n  // ========================== Flatten =========================\n  var flattenColumns = React.useMemo(function () {\n    if (direction === 'rtl') {\n      return revertForRtl(flatColumns(mergedColumns));\n    }\n    return flatColumns(mergedColumns);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [mergedColumns, direction, scrollWidth]);\n\n  // ========================= Gap Fixed ========================\n  var hasGapFixed = React.useMemo(function () {\n    // Fixed: left, since old browser not support `findLastIndex`, we should use reverse loop\n    var lastLeftIndex = -1;\n    for (var i = flattenColumns.length - 1; i >= 0; i -= 1) {\n      var colFixed = flattenColumns[i].fixed;\n      if (colFixed === 'left' || colFixed === true) {\n        lastLeftIndex = i;\n        break;\n      }\n    }\n    if (lastLeftIndex >= 0) {\n      for (var _i = 0; _i <= lastLeftIndex; _i += 1) {\n        var _colFixed = flattenColumns[_i].fixed;\n        if (_colFixed !== 'left' && _colFixed !== true) {\n          return true;\n        }\n      }\n    }\n\n    // Fixed: right\n    var firstRightIndex = flattenColumns.findIndex(function (_ref3) {\n      var colFixed = _ref3.fixed;\n      return colFixed === 'right';\n    });\n    if (firstRightIndex >= 0) {\n      for (var _i2 = firstRightIndex; _i2 < flattenColumns.length; _i2 += 1) {\n        var _colFixed2 = flattenColumns[_i2].fixed;\n        if (_colFixed2 !== 'right') {\n          return true;\n        }\n      }\n    }\n    return false;\n  }, [flattenColumns]);\n\n  // ========================= FillWidth ========================\n  var _useWidthColumns = useWidthColumns(flattenColumns, scrollWidth, clientWidth),\n    _useWidthColumns2 = _slicedToArray(_useWidthColumns, 2),\n    filledColumns = _useWidthColumns2[0],\n    realScrollWidth = _useWidthColumns2[1];\n  return [mergedColumns, filledColumns, realScrollWidth, hasGapFixed];\n}\nexport default useColumns;", "map": {"version": 3, "names": ["_slicedToArray", "_defineProperty", "_toConsumableArray", "_typeof", "_objectSpread", "_objectWithoutProperties", "_excluded", "_excluded2", "toArray", "warning", "React", "EXPAND_COLUMN", "INTERNAL_COL_DEFINE", "useWidthColumns", "convertChildrenToColumns", "children", "filter", "node", "isValidElement", "map", "_ref", "key", "props", "nodeChildren", "restProps", "column", "filterHiddenColumns", "columns", "hidden", "subColumns", "length", "flatColumns", "parent<PERSON><PERSON>", "arguments", "undefined", "reduce", "list", "index", "fixed", "parsedFixed", "mergedKey", "concat", "subColum", "revertForRtl", "useColumns", "_ref2", "transformColumns", "prefixCls", "expandable", "expandedKeys", "columnTitle", "getRowKey", "onTriggerExpand", "expandIcon", "rowExpandable", "expandIconColumnIndex", "direction", "expandRowByClick", "columnWidth", "scrollWidth", "clientWidth", "baseColumns", "useMemo", "newColumns", "slice", "withExpandColumns", "cloneColumns", "process", "env", "NODE_ENV", "includes", "expandColIndex", "splice", "c", "expandColumnIndex", "indexOf", "prevColumn", "fixedColumn", "expandColumn", "className", "columnType", "render", "_", "record", "<PERSON><PERSON><PERSON>", "expanded", "has", "recordExpandable", "icon", "onExpand", "createElement", "onClick", "e", "stopPropagation", "col", "mergedColumns", "finalColumns", "flattenColumns", "hasGapFixed", "lastLeftIndex", "i", "colFixed", "_i", "_colFixed", "firstRightIndex", "findIndex", "_ref3", "_i2", "_colFixed2", "_useWidthColumns", "_useWidthColumns2", "filledColumns", "realScrollWidth"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-table/es/hooks/useColumns/index.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\"],\n  _excluded2 = [\"fixed\"];\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { EXPAND_COLUMN } from \"../../constant\";\nimport { INTERNAL_COL_DEFINE } from \"../../utils/legacyUtil\";\nimport useWidthColumns from \"./useWidthColumns\";\nexport function convertChildrenToColumns(children) {\n  return toArray(children).filter(function (node) {\n    return /*#__PURE__*/React.isValidElement(node);\n  }).map(function (_ref) {\n    var key = _ref.key,\n      props = _ref.props;\n    var nodeChildren = props.children,\n      restProps = _objectWithoutProperties(props, _excluded);\n    var column = _objectSpread({\n      key: key\n    }, restProps);\n    if (nodeChildren) {\n      column.children = convertChildrenToColumns(nodeChildren);\n    }\n    return column;\n  });\n}\nfunction filterHiddenColumns(columns) {\n  return columns.filter(function (column) {\n    return column && _typeof(column) === 'object' && !column.hidden;\n  }).map(function (column) {\n    var subColumns = column.children;\n    if (subColumns && subColumns.length > 0) {\n      return _objectSpread(_objectSpread({}, column), {}, {\n        children: filterHiddenColumns(subColumns)\n      });\n    }\n    return column;\n  });\n}\nfunction flatColumns(columns) {\n  var parentKey = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'key';\n  return columns.filter(function (column) {\n    return column && _typeof(column) === 'object';\n  }).reduce(function (list, column, index) {\n    var fixed = column.fixed;\n    // Convert `fixed='true'` to `fixed='left'` instead\n    var parsedFixed = fixed === true ? 'left' : fixed;\n    var mergedKey = \"\".concat(parentKey, \"-\").concat(index);\n    var subColumns = column.children;\n    if (subColumns && subColumns.length > 0) {\n      return [].concat(_toConsumableArray(list), _toConsumableArray(flatColumns(subColumns, mergedKey).map(function (subColum) {\n        return _objectSpread({\n          fixed: parsedFixed\n        }, subColum);\n      })));\n    }\n    return [].concat(_toConsumableArray(list), [_objectSpread(_objectSpread({\n      key: mergedKey\n    }, column), {}, {\n      fixed: parsedFixed\n    })]);\n  }, []);\n}\nfunction revertForRtl(columns) {\n  return columns.map(function (column) {\n    var fixed = column.fixed,\n      restProps = _objectWithoutProperties(column, _excluded2);\n\n    // Convert `fixed='left'` to `fixed='right'` instead\n    var parsedFixed = fixed;\n    if (fixed === 'left') {\n      parsedFixed = 'right';\n    } else if (fixed === 'right') {\n      parsedFixed = 'left';\n    }\n    return _objectSpread({\n      fixed: parsedFixed\n    }, restProps);\n  });\n}\n\n/**\n * Parse `columns` & `children` into `columns`.\n */\nfunction useColumns(_ref2, transformColumns) {\n  var prefixCls = _ref2.prefixCls,\n    columns = _ref2.columns,\n    children = _ref2.children,\n    expandable = _ref2.expandable,\n    expandedKeys = _ref2.expandedKeys,\n    columnTitle = _ref2.columnTitle,\n    getRowKey = _ref2.getRowKey,\n    onTriggerExpand = _ref2.onTriggerExpand,\n    expandIcon = _ref2.expandIcon,\n    rowExpandable = _ref2.rowExpandable,\n    expandIconColumnIndex = _ref2.expandIconColumnIndex,\n    direction = _ref2.direction,\n    expandRowByClick = _ref2.expandRowByClick,\n    columnWidth = _ref2.columnWidth,\n    fixed = _ref2.fixed,\n    scrollWidth = _ref2.scrollWidth,\n    clientWidth = _ref2.clientWidth;\n  var baseColumns = React.useMemo(function () {\n    var newColumns = columns || convertChildrenToColumns(children) || [];\n    return filterHiddenColumns(newColumns.slice());\n  }, [columns, children]);\n\n  // ========================== Expand ==========================\n  var withExpandColumns = React.useMemo(function () {\n    if (expandable) {\n      var cloneColumns = baseColumns.slice();\n\n      // >>> Warning if use `expandIconColumnIndex`\n      if (process.env.NODE_ENV !== 'production' && expandIconColumnIndex >= 0) {\n        warning(false, '`expandIconColumnIndex` is deprecated. Please use `Table.EXPAND_COLUMN` in `columns` instead.');\n      }\n\n      // >>> Insert expand column if not exist\n      if (!cloneColumns.includes(EXPAND_COLUMN)) {\n        var expandColIndex = expandIconColumnIndex || 0;\n        if (expandColIndex >= 0 && (expandColIndex || fixed === 'left' || !fixed)) {\n          cloneColumns.splice(expandColIndex, 0, EXPAND_COLUMN);\n        }\n        if (fixed === 'right') {\n          cloneColumns.splice(baseColumns.length, 0, EXPAND_COLUMN);\n        }\n      }\n\n      // >>> Deduplicate additional expand column\n      if (process.env.NODE_ENV !== 'production' && cloneColumns.filter(function (c) {\n        return c === EXPAND_COLUMN;\n      }).length > 1) {\n        warning(false, 'There exist more than one `EXPAND_COLUMN` in `columns`.');\n      }\n      var expandColumnIndex = cloneColumns.indexOf(EXPAND_COLUMN);\n      cloneColumns = cloneColumns.filter(function (column, index) {\n        return column !== EXPAND_COLUMN || index === expandColumnIndex;\n      });\n\n      // >>> Check if expand column need to fixed\n      var prevColumn = baseColumns[expandColumnIndex];\n      var fixedColumn;\n      if (fixed) {\n        fixedColumn = fixed;\n      } else {\n        fixedColumn = prevColumn ? prevColumn.fixed : null;\n      }\n\n      // >>> Create expandable column\n      var expandColumn = _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, INTERNAL_COL_DEFINE, {\n        className: \"\".concat(prefixCls, \"-expand-icon-col\"),\n        columnType: 'EXPAND_COLUMN'\n      }), \"title\", columnTitle), \"fixed\", fixedColumn), \"className\", \"\".concat(prefixCls, \"-row-expand-icon-cell\")), \"width\", columnWidth), \"render\", function render(_, record, index) {\n        var rowKey = getRowKey(record, index);\n        var expanded = expandedKeys.has(rowKey);\n        var recordExpandable = rowExpandable ? rowExpandable(record) : true;\n        var icon = expandIcon({\n          prefixCls: prefixCls,\n          expanded: expanded,\n          expandable: recordExpandable,\n          record: record,\n          onExpand: onTriggerExpand\n        });\n        if (expandRowByClick) {\n          return /*#__PURE__*/React.createElement(\"span\", {\n            onClick: function onClick(e) {\n              return e.stopPropagation();\n            }\n          }, icon);\n        }\n        return icon;\n      });\n      return cloneColumns.map(function (col) {\n        return col === EXPAND_COLUMN ? expandColumn : col;\n      });\n    }\n    if (process.env.NODE_ENV !== 'production' && baseColumns.includes(EXPAND_COLUMN)) {\n      warning(false, '`expandable` is not config but there exist `EXPAND_COLUMN` in `columns`.');\n    }\n    return baseColumns.filter(function (col) {\n      return col !== EXPAND_COLUMN;\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [expandable, baseColumns, getRowKey, expandedKeys, expandIcon, direction]);\n\n  // ========================= Transform ========================\n  var mergedColumns = React.useMemo(function () {\n    var finalColumns = withExpandColumns;\n    if (transformColumns) {\n      finalColumns = transformColumns(finalColumns);\n    }\n\n    // Always provides at least one column for table display\n    if (!finalColumns.length) {\n      finalColumns = [{\n        render: function render() {\n          return null;\n        }\n      }];\n    }\n    return finalColumns;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [transformColumns, withExpandColumns, direction]);\n\n  // ========================== Flatten =========================\n  var flattenColumns = React.useMemo(function () {\n    if (direction === 'rtl') {\n      return revertForRtl(flatColumns(mergedColumns));\n    }\n    return flatColumns(mergedColumns);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [mergedColumns, direction, scrollWidth]);\n\n  // ========================= Gap Fixed ========================\n  var hasGapFixed = React.useMemo(function () {\n    // Fixed: left, since old browser not support `findLastIndex`, we should use reverse loop\n    var lastLeftIndex = -1;\n    for (var i = flattenColumns.length - 1; i >= 0; i -= 1) {\n      var colFixed = flattenColumns[i].fixed;\n      if (colFixed === 'left' || colFixed === true) {\n        lastLeftIndex = i;\n        break;\n      }\n    }\n    if (lastLeftIndex >= 0) {\n      for (var _i = 0; _i <= lastLeftIndex; _i += 1) {\n        var _colFixed = flattenColumns[_i].fixed;\n        if (_colFixed !== 'left' && _colFixed !== true) {\n          return true;\n        }\n      }\n    }\n\n    // Fixed: right\n    var firstRightIndex = flattenColumns.findIndex(function (_ref3) {\n      var colFixed = _ref3.fixed;\n      return colFixed === 'right';\n    });\n    if (firstRightIndex >= 0) {\n      for (var _i2 = firstRightIndex; _i2 < flattenColumns.length; _i2 += 1) {\n        var _colFixed2 = flattenColumns[_i2].fixed;\n        if (_colFixed2 !== 'right') {\n          return true;\n        }\n      }\n    }\n    return false;\n  }, [flattenColumns]);\n\n  // ========================= FillWidth ========================\n  var _useWidthColumns = useWidthColumns(flattenColumns, scrollWidth, clientWidth),\n    _useWidthColumns2 = _slicedToArray(_useWidthColumns, 2),\n    filledColumns = _useWidthColumns2[0],\n    realScrollWidth = _useWidthColumns2[1];\n  return [mergedColumns, filledColumns, realScrollWidth, hasGapFixed];\n}\nexport default useColumns;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,UAAU,CAAC;EAC1BC,UAAU,GAAG,CAAC,OAAO,CAAC;AACxB,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAO,SAASC,wBAAwBA,CAACC,QAAQ,EAAE;EACjD,OAAOP,OAAO,CAACO,QAAQ,CAAC,CAACC,MAAM,CAAC,UAAUC,IAAI,EAAE;IAC9C,OAAO,aAAaP,KAAK,CAACQ,cAAc,CAACD,IAAI,CAAC;EAChD,CAAC,CAAC,CAACE,GAAG,CAAC,UAAUC,IAAI,EAAE;IACrB,IAAIC,GAAG,GAAGD,IAAI,CAACC,GAAG;MAChBC,KAAK,GAAGF,IAAI,CAACE,KAAK;IACpB,IAAIC,YAAY,GAAGD,KAAK,CAACP,QAAQ;MAC/BS,SAAS,GAAGnB,wBAAwB,CAACiB,KAAK,EAAEhB,SAAS,CAAC;IACxD,IAAImB,MAAM,GAAGrB,aAAa,CAAC;MACzBiB,GAAG,EAAEA;IACP,CAAC,EAAEG,SAAS,CAAC;IACb,IAAID,YAAY,EAAE;MAChBE,MAAM,CAACV,QAAQ,GAAGD,wBAAwB,CAACS,YAAY,CAAC;IAC1D;IACA,OAAOE,MAAM;EACf,CAAC,CAAC;AACJ;AACA,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EACpC,OAAOA,OAAO,CAACX,MAAM,CAAC,UAAUS,MAAM,EAAE;IACtC,OAAOA,MAAM,IAAItB,OAAO,CAACsB,MAAM,CAAC,KAAK,QAAQ,IAAI,CAACA,MAAM,CAACG,MAAM;EACjE,CAAC,CAAC,CAACT,GAAG,CAAC,UAAUM,MAAM,EAAE;IACvB,IAAII,UAAU,GAAGJ,MAAM,CAACV,QAAQ;IAChC,IAAIc,UAAU,IAAIA,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,OAAO1B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqB,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QAClDV,QAAQ,EAAEW,mBAAmB,CAACG,UAAU;MAC1C,CAAC,CAAC;IACJ;IACA,OAAOJ,MAAM;EACf,CAAC,CAAC;AACJ;AACA,SAASM,WAAWA,CAACJ,OAAO,EAAE;EAC5B,IAAIK,SAAS,GAAGC,SAAS,CAACH,MAAM,GAAG,CAAC,IAAIG,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACzF,OAAON,OAAO,CAACX,MAAM,CAAC,UAAUS,MAAM,EAAE;IACtC,OAAOA,MAAM,IAAItB,OAAO,CAACsB,MAAM,CAAC,KAAK,QAAQ;EAC/C,CAAC,CAAC,CAACU,MAAM,CAAC,UAAUC,IAAI,EAAEX,MAAM,EAAEY,KAAK,EAAE;IACvC,IAAIC,KAAK,GAAGb,MAAM,CAACa,KAAK;IACxB;IACA,IAAIC,WAAW,GAAGD,KAAK,KAAK,IAAI,GAAG,MAAM,GAAGA,KAAK;IACjD,IAAIE,SAAS,GAAG,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,GAAG,CAAC,CAACS,MAAM,CAACJ,KAAK,CAAC;IACvD,IAAIR,UAAU,GAAGJ,MAAM,CAACV,QAAQ;IAChC,IAAIc,UAAU,IAAIA,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;MACvC,OAAO,EAAE,CAACW,MAAM,CAACvC,kBAAkB,CAACkC,IAAI,CAAC,EAAElC,kBAAkB,CAAC6B,WAAW,CAACF,UAAU,EAAEW,SAAS,CAAC,CAACrB,GAAG,CAAC,UAAUuB,QAAQ,EAAE;QACvH,OAAOtC,aAAa,CAAC;UACnBkC,KAAK,EAAEC;QACT,CAAC,EAAEG,QAAQ,CAAC;MACd,CAAC,CAAC,CAAC,CAAC;IACN;IACA,OAAO,EAAE,CAACD,MAAM,CAACvC,kBAAkB,CAACkC,IAAI,CAAC,EAAE,CAAChC,aAAa,CAACA,aAAa,CAAC;MACtEiB,GAAG,EAAEmB;IACP,CAAC,EAAEf,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MACda,KAAK,EAAEC;IACT,CAAC,CAAC,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;AACR;AACA,SAASI,YAAYA,CAAChB,OAAO,EAAE;EAC7B,OAAOA,OAAO,CAACR,GAAG,CAAC,UAAUM,MAAM,EAAE;IACnC,IAAIa,KAAK,GAAGb,MAAM,CAACa,KAAK;MACtBd,SAAS,GAAGnB,wBAAwB,CAACoB,MAAM,EAAElB,UAAU,CAAC;;IAE1D;IACA,IAAIgC,WAAW,GAAGD,KAAK;IACvB,IAAIA,KAAK,KAAK,MAAM,EAAE;MACpBC,WAAW,GAAG,OAAO;IACvB,CAAC,MAAM,IAAID,KAAK,KAAK,OAAO,EAAE;MAC5BC,WAAW,GAAG,MAAM;IACtB;IACA,OAAOnC,aAAa,CAAC;MACnBkC,KAAK,EAAEC;IACT,CAAC,EAAEf,SAAS,CAAC;EACf,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,SAASoB,UAAUA,CAACC,KAAK,EAAEC,gBAAgB,EAAE;EAC3C,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BpB,OAAO,GAAGkB,KAAK,CAAClB,OAAO;IACvBZ,QAAQ,GAAG8B,KAAK,CAAC9B,QAAQ;IACzBiC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,YAAY,GAAGJ,KAAK,CAACI,YAAY;IACjCC,WAAW,GAAGL,KAAK,CAACK,WAAW;IAC/BC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,eAAe,GAAGP,KAAK,CAACO,eAAe;IACvCC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,aAAa,GAAGT,KAAK,CAACS,aAAa;IACnCC,qBAAqB,GAAGV,KAAK,CAACU,qBAAqB;IACnDC,SAAS,GAAGX,KAAK,CAACW,SAAS;IAC3BC,gBAAgB,GAAGZ,KAAK,CAACY,gBAAgB;IACzCC,WAAW,GAAGb,KAAK,CAACa,WAAW;IAC/BpB,KAAK,GAAGO,KAAK,CAACP,KAAK;IACnBqB,WAAW,GAAGd,KAAK,CAACc,WAAW;IAC/BC,WAAW,GAAGf,KAAK,CAACe,WAAW;EACjC,IAAIC,WAAW,GAAGnD,KAAK,CAACoD,OAAO,CAAC,YAAY;IAC1C,IAAIC,UAAU,GAAGpC,OAAO,IAAIb,wBAAwB,CAACC,QAAQ,CAAC,IAAI,EAAE;IACpE,OAAOW,mBAAmB,CAACqC,UAAU,CAACC,KAAK,CAAC,CAAC,CAAC;EAChD,CAAC,EAAE,CAACrC,OAAO,EAAEZ,QAAQ,CAAC,CAAC;;EAEvB;EACA,IAAIkD,iBAAiB,GAAGvD,KAAK,CAACoD,OAAO,CAAC,YAAY;IAChD,IAAId,UAAU,EAAE;MACd,IAAIkB,YAAY,GAAGL,WAAW,CAACG,KAAK,CAAC,CAAC;;MAEtC;MACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAId,qBAAqB,IAAI,CAAC,EAAE;QACvE9C,OAAO,CAAC,KAAK,EAAE,+FAA+F,CAAC;MACjH;;MAEA;MACA,IAAI,CAACyD,YAAY,CAACI,QAAQ,CAAC3D,aAAa,CAAC,EAAE;QACzC,IAAI4D,cAAc,GAAGhB,qBAAqB,IAAI,CAAC;QAC/C,IAAIgB,cAAc,IAAI,CAAC,KAAKA,cAAc,IAAIjC,KAAK,KAAK,MAAM,IAAI,CAACA,KAAK,CAAC,EAAE;UACzE4B,YAAY,CAACM,MAAM,CAACD,cAAc,EAAE,CAAC,EAAE5D,aAAa,CAAC;QACvD;QACA,IAAI2B,KAAK,KAAK,OAAO,EAAE;UACrB4B,YAAY,CAACM,MAAM,CAACX,WAAW,CAAC/B,MAAM,EAAE,CAAC,EAAEnB,aAAa,CAAC;QAC3D;MACF;;MAEA;MACA,IAAIwD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIH,YAAY,CAAClD,MAAM,CAAC,UAAUyD,CAAC,EAAE;QAC5E,OAAOA,CAAC,KAAK9D,aAAa;MAC5B,CAAC,CAAC,CAACmB,MAAM,GAAG,CAAC,EAAE;QACbrB,OAAO,CAAC,KAAK,EAAE,yDAAyD,CAAC;MAC3E;MACA,IAAIiE,iBAAiB,GAAGR,YAAY,CAACS,OAAO,CAAChE,aAAa,CAAC;MAC3DuD,YAAY,GAAGA,YAAY,CAAClD,MAAM,CAAC,UAAUS,MAAM,EAAEY,KAAK,EAAE;QAC1D,OAAOZ,MAAM,KAAKd,aAAa,IAAI0B,KAAK,KAAKqC,iBAAiB;MAChE,CAAC,CAAC;;MAEF;MACA,IAAIE,UAAU,GAAGf,WAAW,CAACa,iBAAiB,CAAC;MAC/C,IAAIG,WAAW;MACf,IAAIvC,KAAK,EAAE;QACTuC,WAAW,GAAGvC,KAAK;MACrB,CAAC,MAAM;QACLuC,WAAW,GAAGD,UAAU,GAAGA,UAAU,CAACtC,KAAK,GAAG,IAAI;MACpD;;MAEA;MACA,IAAIwC,YAAY,GAAG7E,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAEW,mBAAmB,EAAE;QAC1ImE,SAAS,EAAE,EAAE,CAACtC,MAAM,CAACM,SAAS,EAAE,kBAAkB,CAAC;QACnDiC,UAAU,EAAE;MACd,CAAC,CAAC,EAAE,OAAO,EAAE9B,WAAW,CAAC,EAAE,OAAO,EAAE2B,WAAW,CAAC,EAAE,WAAW,EAAE,EAAE,CAACpC,MAAM,CAACM,SAAS,EAAE,uBAAuB,CAAC,CAAC,EAAE,OAAO,EAAEW,WAAW,CAAC,EAAE,QAAQ,EAAE,SAASuB,MAAMA,CAACC,CAAC,EAAEC,MAAM,EAAE9C,KAAK,EAAE;QAChL,IAAI+C,MAAM,GAAGjC,SAAS,CAACgC,MAAM,EAAE9C,KAAK,CAAC;QACrC,IAAIgD,QAAQ,GAAGpC,YAAY,CAACqC,GAAG,CAACF,MAAM,CAAC;QACvC,IAAIG,gBAAgB,GAAGjC,aAAa,GAAGA,aAAa,CAAC6B,MAAM,CAAC,GAAG,IAAI;QACnE,IAAIK,IAAI,GAAGnC,UAAU,CAAC;UACpBN,SAAS,EAAEA,SAAS;UACpBsC,QAAQ,EAAEA,QAAQ;UAClBrC,UAAU,EAAEuC,gBAAgB;UAC5BJ,MAAM,EAAEA,MAAM;UACdM,QAAQ,EAAErC;QACZ,CAAC,CAAC;QACF,IAAIK,gBAAgB,EAAE;UACpB,OAAO,aAAa/C,KAAK,CAACgF,aAAa,CAAC,MAAM,EAAE;YAC9CC,OAAO,EAAE,SAASA,OAAOA,CAACC,CAAC,EAAE;cAC3B,OAAOA,CAAC,CAACC,eAAe,CAAC,CAAC;YAC5B;UACF,CAAC,EAAEL,IAAI,CAAC;QACV;QACA,OAAOA,IAAI;MACb,CAAC,CAAC;MACF,OAAOtB,YAAY,CAAC/C,GAAG,CAAC,UAAU2E,GAAG,EAAE;QACrC,OAAOA,GAAG,KAAKnF,aAAa,GAAGmE,YAAY,GAAGgB,GAAG;MACnD,CAAC,CAAC;IACJ;IACA,IAAI3B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIR,WAAW,CAACS,QAAQ,CAAC3D,aAAa,CAAC,EAAE;MAChFF,OAAO,CAAC,KAAK,EAAE,0EAA0E,CAAC;IAC5F;IACA,OAAOoD,WAAW,CAAC7C,MAAM,CAAC,UAAU8E,GAAG,EAAE;MACvC,OAAOA,GAAG,KAAKnF,aAAa;IAC9B,CAAC,CAAC;IACF;EACF,CAAC,EAAE,CAACqC,UAAU,EAAEa,WAAW,EAAEV,SAAS,EAAEF,YAAY,EAAEI,UAAU,EAAEG,SAAS,CAAC,CAAC;;EAE7E;EACA,IAAIuC,aAAa,GAAGrF,KAAK,CAACoD,OAAO,CAAC,YAAY;IAC5C,IAAIkC,YAAY,GAAG/B,iBAAiB;IACpC,IAAInB,gBAAgB,EAAE;MACpBkD,YAAY,GAAGlD,gBAAgB,CAACkD,YAAY,CAAC;IAC/C;;IAEA;IACA,IAAI,CAACA,YAAY,CAAClE,MAAM,EAAE;MACxBkE,YAAY,GAAG,CAAC;QACdf,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;UACxB,OAAO,IAAI;QACb;MACF,CAAC,CAAC;IACJ;IACA,OAAOe,YAAY;IACnB;EACF,CAAC,EAAE,CAAClD,gBAAgB,EAAEmB,iBAAiB,EAAET,SAAS,CAAC,CAAC;;EAEpD;EACA,IAAIyC,cAAc,GAAGvF,KAAK,CAACoD,OAAO,CAAC,YAAY;IAC7C,IAAIN,SAAS,KAAK,KAAK,EAAE;MACvB,OAAOb,YAAY,CAACZ,WAAW,CAACgE,aAAa,CAAC,CAAC;IACjD;IACA,OAAOhE,WAAW,CAACgE,aAAa,CAAC;IACjC;EACF,CAAC,EAAE,CAACA,aAAa,EAAEvC,SAAS,EAAEG,WAAW,CAAC,CAAC;;EAE3C;EACA,IAAIuC,WAAW,GAAGxF,KAAK,CAACoD,OAAO,CAAC,YAAY;IAC1C;IACA,IAAIqC,aAAa,GAAG,CAAC,CAAC;IACtB,KAAK,IAAIC,CAAC,GAAGH,cAAc,CAACnE,MAAM,GAAG,CAAC,EAAEsE,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MACtD,IAAIC,QAAQ,GAAGJ,cAAc,CAACG,CAAC,CAAC,CAAC9D,KAAK;MACtC,IAAI+D,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,IAAI,EAAE;QAC5CF,aAAa,GAAGC,CAAC;QACjB;MACF;IACF;IACA,IAAID,aAAa,IAAI,CAAC,EAAE;MACtB,KAAK,IAAIG,EAAE,GAAG,CAAC,EAAEA,EAAE,IAAIH,aAAa,EAAEG,EAAE,IAAI,CAAC,EAAE;QAC7C,IAAIC,SAAS,GAAGN,cAAc,CAACK,EAAE,CAAC,CAAChE,KAAK;QACxC,IAAIiE,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,IAAI,EAAE;UAC9C,OAAO,IAAI;QACb;MACF;IACF;;IAEA;IACA,IAAIC,eAAe,GAAGP,cAAc,CAACQ,SAAS,CAAC,UAAUC,KAAK,EAAE;MAC9D,IAAIL,QAAQ,GAAGK,KAAK,CAACpE,KAAK;MAC1B,OAAO+D,QAAQ,KAAK,OAAO;IAC7B,CAAC,CAAC;IACF,IAAIG,eAAe,IAAI,CAAC,EAAE;MACxB,KAAK,IAAIG,GAAG,GAAGH,eAAe,EAAEG,GAAG,GAAGV,cAAc,CAACnE,MAAM,EAAE6E,GAAG,IAAI,CAAC,EAAE;QACrE,IAAIC,UAAU,GAAGX,cAAc,CAACU,GAAG,CAAC,CAACrE,KAAK;QAC1C,IAAIsE,UAAU,KAAK,OAAO,EAAE;UAC1B,OAAO,IAAI;QACb;MACF;IACF;IACA,OAAO,KAAK;EACd,CAAC,EAAE,CAACX,cAAc,CAAC,CAAC;;EAEpB;EACA,IAAIY,gBAAgB,GAAGhG,eAAe,CAACoF,cAAc,EAAEtC,WAAW,EAAEC,WAAW,CAAC;IAC9EkD,iBAAiB,GAAG9G,cAAc,CAAC6G,gBAAgB,EAAE,CAAC,CAAC;IACvDE,aAAa,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACpCE,eAAe,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACxC,OAAO,CAACf,aAAa,EAAEgB,aAAa,EAAEC,eAAe,EAAEd,WAAW,CAAC;AACrE;AACA,eAAetD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}