{"ast": null, "code": "// src/pages/RoadMonitoring.jsx\nimport React,{useState,useEffect}from'react';import{Card,List,Descriptions,Spin,Badge,Row,Col}from'antd';import styled from'styled-components';import CollapsibleSidebar from'../components/layout/CollapsibleSidebar';import devicesData from'../data/devices.json';import VideoPlayer from'../components/VideoPlayer';import axios from'axios';// 添加axios导入\n// 添加立即打印，检查导入是否成功\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";console.log('DevicesData import check:',devicesData);// 页面布局容器\nconst PageContainer=styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;// 左侧信息栏容器\nconst LeftSidebar=styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;// 右侧信息栏容器\nconst RightSidebar=styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;// 主内容区域\nconst MainContent=styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props=>props.leftCollapsed&&props.rightCollapsed?'0 24px':props.leftCollapsed?'0 8px 0 24px':props.rightCollapsed?'0 24px 0 8px':'0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props=>props.leftCollapsed&&props.rightCollapsed?'0 24px':props.leftCollapsed?'0 8px 0 0':props.rightCollapsed?'0 0 0 8px':'0'};\n  position: relative;\n  z-index: 1;\n`;// 信息卡片\nconst InfoCard=styled(Card)`\n  margin-bottom: 12px;\n  height: ${props=>props.height||'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;// 视频容器\nconst VideoContainer=styled.div`\n  background: #000;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  margin-bottom: 10px;\n  \n  &:before {\n    content: \"\";\n    display: block;\n    padding-top: 56.25%; // 16:9 宽高比\n  }\n`;// 视频占位符\nconst VideoPlaceholder=styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 13px;\n`;const RoadMonitoring=()=>{const[loading,setLoading]=useState(true);const[intersections,setIntersections]=useState([]);const[selectedIntersection,setSelectedIntersection]=useState(null);// 添加侧边栏折叠状态\nconst[leftCollapsed,setLeftCollapsed]=useState(false);const[rightCollapsed,setRightCollapsed]=useState(false);// 声明一个函数来加载设备数据\nconst loadData=async()=>{try{setLoading(true);// 尝试从API获取设备数据\nlet devicesArray=[];try{const apiUrl=process.env.REACT_APP_API_URL||'http://localhost:5000';const response=await axios.get(`${apiUrl}/api/devices`);if(response.data&&response.data.success){devicesArray=response.data.data;}}catch(error){console.log('从API获取设备失败，使用本地数据',error);// 使用导入的 devicesData\nconsole.log('Imported devicesData:',devicesData);// 确保我们访问的是 devices 数组\ndevicesArray=devicesData.devices||[];}if(devicesArray.length===0){throw new Error('No devices found in the data');}console.log('Processing devices array:',devicesArray);// 过滤掉类型为obu的设备\nconst filteredDevices=devicesArray.filter(device=>device.type!=='obu');// 根据 location 对设备进行分组\nconst groupedDevices=filteredDevices.reduce((acc,device)=>{const location=device.location;if(!location){console.warn('Device missing location:',device);return acc;}if(!acc[location]){acc[location]={id:location,name:location,description:`${location}监控点`,status:'正常',lastUpdate:device.createdAt||new Date().toISOString(),devices:[]};}// 添加 rtspUrl 到设备信息中\nacc[location].devices.push({type:device.type,id:device.id,name:device.name,status:device.status,rtspUrl:device.rtspUrl// 添加 rtspUrl 字段\n});return acc;},{});const intersectionsData=Object.values(groupedDevices);console.log('Final processed intersections:',intersectionsData);if(intersectionsData.length===0){throw new Error('No intersections processed from the data');}else{setIntersections(intersectionsData);if(!selectedIntersection&&intersectionsData.length>0){setSelectedIntersection(intersectionsData[0]);}else if(selectedIntersection){// 如果之前已选择了一个路口，尝试在新数据中找到它\nconst updatedSelected=intersectionsData.find(i=>i.id===selectedIntersection.id);if(updatedSelected){setSelectedIntersection(updatedSelected);}else if(intersectionsData.length>0){setSelectedIntersection(intersectionsData[0]);}}}}catch(error){console.error('Error loading devices data:',error);setIntersections([{id:'error',name:'数据加载错误',description:`无法加载设备数据: ${error.message}`,status:'错误',lastUpdate:new Date().toLocaleString(),devices:[]}]);}finally{setLoading(false);}};// 修改 useEffect，添加定时器定期检查设备数据更新\nuseEffect(()=>{// 首次加载数据\nloadData();// 设置定时器，每300秒检查一次设备数据更新\nconst intervalId=setInterval(async()=>{try{const apiUrl=process.env.REACT_APP_API_URL||'http://localhost:5000';const response=await axios.get(`${apiUrl}/api/devices/lastUpdate`);if(response.data&&response.data.lastUpdate){// 检查上次更新时间，如果有更新则重新加载数据\nif(response.data.lastUpdate>localStorage.getItem('lastDevicesUpdate')){console.log('设备数据有更新，重新加载');loadData();localStorage.setItem('lastDevicesUpdate',response.data.lastUpdate);}}else{// 如果API不可用，使用简单的时间间隔更新\nloadData();}}catch(error){console.warn('检查设备更新失败，直接重新加载数据',error);// 如果API不可用，仍然定期更新数据\nloadData();}},300000);// 300秒检查一次\n// 组件卸载时清除定时器\nreturn()=>clearInterval(intervalId);},[]);// 处理路口选择\nconst handleIntersectionSelect=intersection=>{setSelectedIntersection(intersection);};// 修改 getDeviceSummary 函数以支持更多设备类型\nconst getDeviceSummary=devices=>{const summary={};devices.forEach(device=>{const type=device.type;summary[type]=(summary[type]||0)+1;});return Object.entries(summary).map(_ref=>{let[type,count]=_ref;const typeNames={'camera':'摄像头','rsu':'RSU','mmwave_radar':'毫米波雷达','edge_computing':'边缘计算单元','lidar':'激光雷达'};return`${typeNames[type]||type}: ${count}`;}).join(', ');};// 修改 getCameras 函数以匹配实际的设备类型\nconst getCameras=devices=>{return devices.filter(device=>device.type==='camera');};return/*#__PURE__*/_jsx(Spin,{spinning:loading,tip:\"\\u52A0\\u8F7D\\u4E2D...\",children:/*#__PURE__*/_jsxs(PageContainer,{children:[/*#__PURE__*/_jsx(CollapsibleSidebar,{position:\"left\",collapsed:leftCollapsed,onCollapse:()=>setLeftCollapsed(!leftCollapsed),children:/*#__PURE__*/_jsx(InfoCard,{title:\"\\u8DEF\\u53E3\\u4FE1\\u606F\\u5217\\u8868\",bordered:false,height:\"100%\",children:/*#__PURE__*/_jsx(List,{itemLayout:\"vertical\",dataSource:intersections,renderItem:item=>/*#__PURE__*/_jsxs(List.Item,{onClick:()=>handleIntersectionSelect(item),style:{cursor:'pointer',background:(selectedIntersection===null||selectedIntersection===void 0?void 0:selectedIntersection.id)===item.id?'#e6f7ff':'transparent',padding:'8px',borderRadius:'4px',marginBottom:'8px'},children:[/*#__PURE__*/_jsx(List.Item.Meta,{title:/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'14px',fontWeight:'bold'},children:item.name}),description:/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'12px'},children:item.description})}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',marginTop:'4px'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u8BBE\\u5907\\u914D\\u7F6E\\uFF1A\"}),\" \",getDeviceSummary(item.devices)]}),/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'4px'},children:item.devices.map(device=>/*#__PURE__*/_jsx(Badge,{status:device.status==='online'?'success':'error',text:/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'12px',marginRight:'8px'},children:device.name}),style:{display:'inline-block',marginRight:'8px'}},device.id))})]})]})})})}),/*#__PURE__*/_jsx(MainContent,{leftCollapsed:leftCollapsed,rightCollapsed:rightCollapsed}),/*#__PURE__*/_jsx(CollapsibleSidebar,{position:\"right\",collapsed:rightCollapsed,onCollapse:()=>setRightCollapsed(!rightCollapsed),children:/*#__PURE__*/_jsx(InfoCard,{title:`视频监控 - ${(selectedIntersection===null||selectedIntersection===void 0?void 0:selectedIntersection.name)||''}`,bordered:false,height:\"100%\",children:selectedIntersection?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'12px',fontSize:'13px'},children:/*#__PURE__*/_jsxs(Descriptions,{size:\"small\",column:1,styles:{label:{fontSize:'13px'},content:{fontSize:'13px'}},children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8DEF\\u53E3\\u540D\\u79F0\",children:selectedIntersection.name}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8DEF\\u53E3\\u63CF\\u8FF0\",children:selectedIntersection.description}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u6444\\u50CF\\u5934\\u6570\\u91CF\",children:getCameras(selectedIntersection.devices).length})]})}),getCameras(selectedIntersection.devices).slice(0,4).map(camera=>/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'10px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'13px',marginBottom:'4px',fontWeight:'bold'},children:/*#__PURE__*/_jsx(Badge,{status:camera.status==='online'?'success':'error',text:camera.name})}),/*#__PURE__*/_jsx(VideoContainer,{children:/*#__PURE__*/_jsx(VideoPlaceholder,{children:camera.status==='online'?/*#__PURE__*/_jsx(VideoPlayer,{deviceId:camera.id,rtspUrl:camera.rtspUrl}):/*#__PURE__*/_jsx(_Fragment,{children:\"\\u6444\\u50CF\\u5934\\u79BB\\u7EBF\"})})})]},camera.id)),getCameras(selectedIntersection.devices).length===0&&/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'20px 0'},children:\"\\u8BE5\\u8DEF\\u53E3\\u6CA1\\u6709\\u914D\\u7F6E\\u6444\\u50CF\\u5934\"})]}):/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'20px 0'},children:\"\\u8BF7\\u9009\\u62E9\\u4E00\\u4E2A\\u8DEF\\u53E3\\u67E5\\u770B\\u89C6\\u9891\\u76D1\\u63A7\"})})})]})});};export default RoadMonitoring;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "List", "Descriptions", "Spin", "Badge", "Row", "Col", "styled", "CollapsibleSidebar", "devicesData", "VideoPlayer", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "div", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "InfoCard", "height", "VideoContainer", "VideoPlaceholder", "RoadMonitoring", "loading", "setLoading", "intersections", "setIntersections", "selectedIntersection", "setSelectedIntersection", "setLeftCollapsed", "setRightCollapsed", "loadData", "devicesArray", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "data", "success", "error", "devices", "length", "Error", "filteredDevices", "filter", "device", "type", "groupedDevices", "reduce", "acc", "location", "warn", "id", "name", "description", "status", "lastUpdate", "createdAt", "Date", "toISOString", "push", "rtspUrl", "intersectionsData", "Object", "values", "updatedSelected", "find", "i", "message", "toLocaleString", "intervalId", "setInterval", "localStorage", "getItem", "setItem", "clearInterval", "handleIntersectionSelect", "intersection", "getDeviceSummary", "summary", "for<PERSON>ach", "entries", "map", "_ref", "count", "typeNames", "join", "getCameras", "spinning", "tip", "children", "position", "collapsed", "onCollapse", "title", "bordered", "itemLayout", "dataSource", "renderItem", "item", "<PERSON><PERSON>", "onClick", "style", "cursor", "background", "padding", "borderRadius", "marginBottom", "Meta", "fontSize", "fontWeight", "marginTop", "text", "marginRight", "display", "size", "column", "styles", "label", "content", "slice", "camera", "deviceId", "textAlign"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RoadMonitoring.jsx"], "sourcesContent": ["// src/pages/RoadMonitoring.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Card, List, Descriptions, Spin, Badge, Row, Col } from 'antd';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport devicesData from '../data/devices.json';\nimport VideoPlayer from '../components/VideoPlayer';\nimport axios from 'axios';  // 添加axios导入\n\n// 添加立即打印，检查导入是否成功\nconsole.log('DevicesData import check:', devicesData);\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 24px' : \n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 0' : \n    props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 视频容器\nconst VideoContainer = styled.div`\n  background: #000;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  margin-bottom: 10px;\n  \n  &:before {\n    content: \"\";\n    display: block;\n    padding-top: 56.25%; // 16:9 宽高比\n  }\n`;\n\n// 视频占位符\nconst VideoPlaceholder = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 13px;\n`;\n\nconst RoadMonitoring = () => {\n  const [loading, setLoading] = useState(true);\n  const [intersections, setIntersections] = useState([]);\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n  \n  // 声明一个函数来加载设备数据\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      \n      // 尝试从API获取设备数据\n      let devicesArray = [];\n      try {\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const response = await axios.get(`${apiUrl}/api/devices`);\n        if (response.data && response.data.success) {\n          devicesArray = response.data.data;\n        }\n      } catch (error) {\n        console.log('从API获取设备失败，使用本地数据', error);\n        // 使用导入的 devicesData\n        console.log('Imported devicesData:', devicesData);\n        // 确保我们访问的是 devices 数组\n        devicesArray = devicesData.devices || [];\n      }\n      \n      if (devicesArray.length === 0) {\n        throw new Error('No devices found in the data');\n      }\n      \n      console.log('Processing devices array:', devicesArray);\n      \n      // 过滤掉类型为obu的设备\n      const filteredDevices = devicesArray.filter(device => device.type !== 'obu');\n      \n      // 根据 location 对设备进行分组\n      const groupedDevices = filteredDevices.reduce((acc, device) => {\n        const location = device.location;\n        if (!location) {\n          console.warn('Device missing location:', device);\n          return acc;\n        }\n\n        if (!acc[location]) {\n          acc[location] = {\n            id: location,\n            name: location,\n            description: `${location}监控点`,\n            status: '正常',\n            lastUpdate: device.createdAt || new Date().toISOString(),\n            devices: []\n          };\n        }\n\n        // 添加 rtspUrl 到设备信息中\n        acc[location].devices.push({\n          type: device.type,\n          id: device.id,\n          name: device.name,\n          status: device.status,\n          rtspUrl: device.rtspUrl  // 添加 rtspUrl 字段\n        });\n\n        return acc;\n      }, {});\n\n      const intersectionsData = Object.values(groupedDevices);\n      console.log('Final processed intersections:', intersectionsData);\n\n      if (intersectionsData.length === 0) {\n        throw new Error('No intersections processed from the data');\n      } else {\n        setIntersections(intersectionsData);\n        if (!selectedIntersection && intersectionsData.length > 0) {\n          setSelectedIntersection(intersectionsData[0]);\n        } else if (selectedIntersection) {\n          // 如果之前已选择了一个路口，尝试在新数据中找到它\n          const updatedSelected = intersectionsData.find(i => i.id === selectedIntersection.id);\n          if (updatedSelected) {\n            setSelectedIntersection(updatedSelected);\n          } else if (intersectionsData.length > 0) {\n            setSelectedIntersection(intersectionsData[0]);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error loading devices data:', error);\n      setIntersections([{\n        id: 'error',\n        name: '数据加载错误',\n        description: `无法加载设备数据: ${error.message}`,\n        status: '错误',\n        lastUpdate: new Date().toLocaleString(),\n        devices: []\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  \n  // 修改 useEffect，添加定时器定期检查设备数据更新\n  useEffect(() => {\n    // 首次加载数据\n    loadData();\n    \n    // 设置定时器，每300秒检查一次设备数据更新\n    const intervalId = setInterval(async () => {\n      try {\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const response = await axios.get(`${apiUrl}/api/devices/lastUpdate`);\n        \n        if (response.data && response.data.lastUpdate) {\n          // 检查上次更新时间，如果有更新则重新加载数据\n          if (response.data.lastUpdate > localStorage.getItem('lastDevicesUpdate')) {\n            console.log('设备数据有更新，重新加载');\n            loadData();\n            localStorage.setItem('lastDevicesUpdate', response.data.lastUpdate);\n          }\n        } else {\n          // 如果API不可用，使用简单的时间间隔更新\n          loadData();\n        }\n      } catch (error) {\n        console.warn('检查设备更新失败，直接重新加载数据', error);\n        // 如果API不可用，仍然定期更新数据\n        loadData();\n      }\n    }, 300000); // 300秒检查一次\n    \n    // 组件卸载时清除定时器\n    return () => clearInterval(intervalId);\n  }, []);\n  \n  // 处理路口选择\n  const handleIntersectionSelect = (intersection) => {\n    setSelectedIntersection(intersection);\n  };\n  \n  // 修改 getDeviceSummary 函数以支持更多设备类型\n  const getDeviceSummary = (devices) => {\n    const summary = {};\n    devices.forEach(device => {\n      const type = device.type;\n      summary[type] = (summary[type] || 0) + 1;\n    });\n    \n    return Object.entries(summary).map(([type, count]) => {\n      const typeNames = {\n        'camera': '摄像头',\n        'rsu': 'RSU',\n        'mmwave_radar': '毫米波雷达',\n        'edge_computing': '边缘计算单元',\n        'lidar': '激光雷达'\n      };\n      \n      return `${typeNames[type] || type}: ${count}`;\n    }).join(', ');\n  };\n  \n  // 修改 getCameras 函数以匹配实际的设备类型\n  const getCameras = (devices) => {\n    return devices.filter(device => device.type === 'camera');\n  };\n  \n  return (\n    <Spin spinning={loading} tip=\"加载中...\">\n      <PageContainer>\n        {/* 左侧信息栏 */}\n        <CollapsibleSidebar \n          position=\"left\"\n          collapsed={leftCollapsed}\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\n        >\n          <InfoCard title=\"路口信息列表\" bordered={false} height=\"100%\">\n            <List\n              itemLayout=\"vertical\"\n              dataSource={intersections}\n              renderItem={item => (\n                <List.Item\n                  onClick={() => handleIntersectionSelect(item)}\n                  style={{ \n                    cursor: 'pointer',\n                    background: selectedIntersection?.id === item.id ? '#e6f7ff' : 'transparent',\n                    padding: '8px',\n                    borderRadius: '4px',\n                    marginBottom: '8px'\n                  }}\n                >\n                  <List.Item.Meta\n                    title={<span style={{ fontSize: '14px', fontWeight: 'bold' }}>{item.name}</span>}\n                    description={<span style={{ fontSize: '12px' }}>{item.description}</span>}\n                  />\n                  <div style={{ fontSize: '12px', marginTop: '4px' }}>\n                    <div><strong>设备配置：</strong> {getDeviceSummary(item.devices)}</div>\n                    <div style={{ marginTop: '4px' }}>\n                      {item.devices.map(device => (\n                        <Badge \n                          key={device.id}\n                          status={device.status === 'online' ? 'success' : 'error'} \n                          text={\n                            <span style={{ fontSize: '12px', marginRight: '8px' }}>\n                              {device.name}\n                            </span>\n                          }\n                          style={{ display: 'inline-block', marginRight: '8px' }}\n                        />\n                      ))}\n                    </div>\n                  </div>\n                </List.Item>\n              )}\n            />\n          </InfoCard>\n        </CollapsibleSidebar>\n        \n        {/* 主内容区域 */}\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\n          {/* 主要内容 */}\n        </MainContent>\n        \n        {/* 右侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"right\"\n          collapsed={rightCollapsed}\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\n        >\n          <InfoCard \n            title={`视频监控 - ${selectedIntersection?.name || ''}`} \n            bordered={false} \n            height=\"100%\"\n          >\n            {selectedIntersection ? (\n              <>\n                <div style={{ marginBottom: '12px', fontSize: '13px' }}>\n                  <Descriptions \n                    size=\"small\" \n                    column={1}\n                    styles={{\n                      label: { fontSize: '13px' },\n                      content: { fontSize: '13px' }\n                    }}\n                  >\n                    <Descriptions.Item label=\"路口名称\">{selectedIntersection.name}</Descriptions.Item>\n                    <Descriptions.Item label=\"路口描述\">{selectedIntersection.description}</Descriptions.Item>\n                    <Descriptions.Item label=\"摄像头数量\">{getCameras(selectedIntersection.devices).length}</Descriptions.Item>\n                  </Descriptions>\n                </div>\n                \n                {/* 修改为N行1列的布局 */}\n                {getCameras(selectedIntersection.devices).slice(0, 4).map(camera => (\n                  <div key={camera.id} style={{ marginBottom: '10px' }}>\n                    <div style={{ fontSize: '13px', marginBottom: '4px', fontWeight: 'bold' }}>\n                      <Badge \n                        status={camera.status === 'online' ? 'success' : 'error'} \n                        text={camera.name}\n                      />\n                    </div>\n                    <VideoContainer>\n                      <VideoPlaceholder>\n                        {camera.status === 'online' ? (\n                          <VideoPlayer \n                            deviceId={camera.id} \n                            rtspUrl={camera.rtspUrl} \n                          />\n                        ) : (\n                          <>摄像头离线</>\n                        )}\n                      </VideoPlaceholder>\n                    </VideoContainer>\n                  </div>\n                ))}\n                \n                {getCameras(selectedIntersection.devices).length === 0 && (\n                  <div style={{ textAlign: 'center', padding: '20px 0' }}>\n                    该路口没有配置摄像头\n                  </div>\n                )}\n              </>\n            ) : (\n              <div style={{ textAlign: 'center', padding: '20px 0' }}>\n                请选择一个路口查看视频监控\n              </div>\n            )}\n          </InfoCard>\n        </CollapsibleSidebar>\n      </PageContainer>\n    </Spin>\n  );\n};\n\nexport default RoadMonitoring;"], "mappings": "AAAA;AACA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,IAAI,CAAEC,IAAI,CAAEC,YAAY,CAAEC,IAAI,CAAEC,KAAK,CAAEC,GAAG,CAAEC,GAAG,KAAQ,MAAM,CACtE,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,kBAAkB,KAAM,yCAAyC,CACxE,MAAO,CAAAC,WAAW,KAAM,sBAAsB,CAC9C,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CACnD,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAG;AAE5B;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACAC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEV,WAAW,CAAC,CAErD;AACA,KAAM,CAAAW,aAAa,CAAGb,MAAM,CAACc,GAAG;AAChC;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,WAAW,CAAGf,MAAM,CAACc,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAE,YAAY,CAAGhB,MAAM,CAACc,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAG,WAAW,CAAGjB,MAAM,CAACc,GAAG;AAC9B;AACA;AACA;AACA,aAAaI,KAAK,EAAIA,KAAK,CAACC,aAAa,EAAID,KAAK,CAACE,cAAc,CAAG,QAAQ,CACxEF,KAAK,CAACC,aAAa,CAAG,cAAc,CACpCD,KAAK,CAACE,cAAc,CAAG,cAAc,CAAG,OAAO;AACnD;AACA,YAAYF,KAAK,EAAIA,KAAK,CAACC,aAAa,EAAID,KAAK,CAACE,cAAc,CAAG,QAAQ,CACvEF,KAAK,CAACC,aAAa,CAAG,WAAW,CACjCD,KAAK,CAACE,cAAc,CAAG,WAAW,CAAG,GAAG;AAC5C;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,QAAQ,CAAGrB,MAAM,CAACP,IAAI,CAAC;AAC7B;AACA,YAAYyB,KAAK,EAAIA,KAAK,CAACI,MAAM,EAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAGvB,MAAM,CAACc,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAU,gBAAgB,CAAGxB,MAAM,CAACc,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAW,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGpC,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACqC,aAAa,CAAEC,gBAAgB,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACuC,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGxC,QAAQ,CAAC,IAAI,CAAC,CAEtE;AACA,KAAM,CAAC4B,aAAa,CAAEa,gBAAgB,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAAC6B,cAAc,CAAEa,iBAAiB,CAAC,CAAG1C,QAAQ,CAAC,KAAK,CAAC,CAE3D;AACA,KAAM,CAAA2C,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B,GAAI,CACFP,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,GAAI,CAAAQ,YAAY,CAAG,EAAE,CACrB,GAAI,CACF,KAAM,CAAAC,MAAM,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACvE,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAApC,KAAK,CAACqC,GAAG,CAAC,GAAGL,MAAM,cAAc,CAAC,CACzD,GAAII,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CAC1CR,YAAY,CAAGK,QAAQ,CAACE,IAAI,CAACA,IAAI,CACnC,CACF,CAAE,MAAOE,KAAK,CAAE,CACdjC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAEgC,KAAK,CAAC,CACvC;AACAjC,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEV,WAAW,CAAC,CACjD;AACAiC,YAAY,CAAGjC,WAAW,CAAC2C,OAAO,EAAI,EAAE,CAC1C,CAEA,GAAIV,YAAY,CAACW,MAAM,GAAK,CAAC,CAAE,CAC7B,KAAM,IAAI,CAAAC,KAAK,CAAC,8BAA8B,CAAC,CACjD,CAEApC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEuB,YAAY,CAAC,CAEtD;AACA,KAAM,CAAAa,eAAe,CAAGb,YAAY,CAACc,MAAM,CAACC,MAAM,EAAIA,MAAM,CAACC,IAAI,GAAK,KAAK,CAAC,CAE5E;AACA,KAAM,CAAAC,cAAc,CAAGJ,eAAe,CAACK,MAAM,CAAC,CAACC,GAAG,CAAEJ,MAAM,GAAK,CAC7D,KAAM,CAAAK,QAAQ,CAAGL,MAAM,CAACK,QAAQ,CAChC,GAAI,CAACA,QAAQ,CAAE,CACb5C,OAAO,CAAC6C,IAAI,CAAC,0BAA0B,CAAEN,MAAM,CAAC,CAChD,MAAO,CAAAI,GAAG,CACZ,CAEA,GAAI,CAACA,GAAG,CAACC,QAAQ,CAAC,CAAE,CAClBD,GAAG,CAACC,QAAQ,CAAC,CAAG,CACdE,EAAE,CAAEF,QAAQ,CACZG,IAAI,CAAEH,QAAQ,CACdI,WAAW,CAAE,GAAGJ,QAAQ,KAAK,CAC7BK,MAAM,CAAE,IAAI,CACZC,UAAU,CAAEX,MAAM,CAACY,SAAS,EAAI,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACxDnB,OAAO,CAAE,EACX,CAAC,CACH,CAEA;AACAS,GAAG,CAACC,QAAQ,CAAC,CAACV,OAAO,CAACoB,IAAI,CAAC,CACzBd,IAAI,CAAED,MAAM,CAACC,IAAI,CACjBM,EAAE,CAAEP,MAAM,CAACO,EAAE,CACbC,IAAI,CAAER,MAAM,CAACQ,IAAI,CACjBE,MAAM,CAAEV,MAAM,CAACU,MAAM,CACrBM,OAAO,CAAEhB,MAAM,CAACgB,OAAS;AAC3B,CAAC,CAAC,CAEF,MAAO,CAAAZ,GAAG,CACZ,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN,KAAM,CAAAa,iBAAiB,CAAGC,MAAM,CAACC,MAAM,CAACjB,cAAc,CAAC,CACvDzC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEuD,iBAAiB,CAAC,CAEhE,GAAIA,iBAAiB,CAACrB,MAAM,GAAK,CAAC,CAAE,CAClC,KAAM,IAAI,CAAAC,KAAK,CAAC,0CAA0C,CAAC,CAC7D,CAAC,IAAM,CACLlB,gBAAgB,CAACsC,iBAAiB,CAAC,CACnC,GAAI,CAACrC,oBAAoB,EAAIqC,iBAAiB,CAACrB,MAAM,CAAG,CAAC,CAAE,CACzDf,uBAAuB,CAACoC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAC/C,CAAC,IAAM,IAAIrC,oBAAoB,CAAE,CAC/B;AACA,KAAM,CAAAwC,eAAe,CAAGH,iBAAiB,CAACI,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACf,EAAE,GAAK3B,oBAAoB,CAAC2B,EAAE,CAAC,CACrF,GAAIa,eAAe,CAAE,CACnBvC,uBAAuB,CAACuC,eAAe,CAAC,CAC1C,CAAC,IAAM,IAAIH,iBAAiB,CAACrB,MAAM,CAAG,CAAC,CAAE,CACvCf,uBAAuB,CAACoC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAC/C,CACF,CACF,CACF,CAAE,MAAOvB,KAAK,CAAE,CACdjC,OAAO,CAACiC,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnDf,gBAAgB,CAAC,CAAC,CAChB4B,EAAE,CAAE,OAAO,CACXC,IAAI,CAAE,QAAQ,CACdC,WAAW,CAAE,aAAaf,KAAK,CAAC6B,OAAO,EAAE,CACzCb,MAAM,CAAE,IAAI,CACZC,UAAU,CAAE,GAAI,CAAAE,IAAI,CAAC,CAAC,CAACW,cAAc,CAAC,CAAC,CACvC7B,OAAO,CAAE,EACX,CAAC,CAAC,CAAC,CACL,CAAC,OAAS,CACRlB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACAnC,SAAS,CAAC,IAAM,CACd;AACA0C,QAAQ,CAAC,CAAC,CAEV;AACA,KAAM,CAAAyC,UAAU,CAAGC,WAAW,CAAC,SAAY,CACzC,GAAI,CACF,KAAM,CAAAxC,MAAM,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACvE,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAApC,KAAK,CAACqC,GAAG,CAAC,GAAGL,MAAM,yBAAyB,CAAC,CAEpE,GAAII,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACmB,UAAU,CAAE,CAC7C;AACA,GAAIrB,QAAQ,CAACE,IAAI,CAACmB,UAAU,CAAGgB,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAAE,CACxEnE,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3BsB,QAAQ,CAAC,CAAC,CACV2C,YAAY,CAACE,OAAO,CAAC,mBAAmB,CAAEvC,QAAQ,CAACE,IAAI,CAACmB,UAAU,CAAC,CACrE,CACF,CAAC,IAAM,CACL;AACA3B,QAAQ,CAAC,CAAC,CACZ,CACF,CAAE,MAAOU,KAAK,CAAE,CACdjC,OAAO,CAAC6C,IAAI,CAAC,mBAAmB,CAAEZ,KAAK,CAAC,CACxC;AACAV,QAAQ,CAAC,CAAC,CACZ,CACF,CAAC,CAAE,MAAM,CAAC,CAAE;AAEZ;AACA,MAAO,IAAM8C,aAAa,CAACL,UAAU,CAAC,CACxC,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAM,wBAAwB,CAAIC,YAAY,EAAK,CACjDnD,uBAAuB,CAACmD,YAAY,CAAC,CACvC,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAItC,OAAO,EAAK,CACpC,KAAM,CAAAuC,OAAO,CAAG,CAAC,CAAC,CAClBvC,OAAO,CAACwC,OAAO,CAACnC,MAAM,EAAI,CACxB,KAAM,CAAAC,IAAI,CAAGD,MAAM,CAACC,IAAI,CACxBiC,OAAO,CAACjC,IAAI,CAAC,CAAG,CAACiC,OAAO,CAACjC,IAAI,CAAC,EAAI,CAAC,EAAI,CAAC,CAC1C,CAAC,CAAC,CAEF,MAAO,CAAAiB,MAAM,CAACkB,OAAO,CAACF,OAAO,CAAC,CAACG,GAAG,CAACC,IAAA,EAAmB,IAAlB,CAACrC,IAAI,CAAEsC,KAAK,CAAC,CAAAD,IAAA,CAC/C,KAAM,CAAAE,SAAS,CAAG,CAChB,QAAQ,CAAE,KAAK,CACf,KAAK,CAAE,KAAK,CACZ,cAAc,CAAE,OAAO,CACvB,gBAAgB,CAAE,QAAQ,CAC1B,OAAO,CAAE,MACX,CAAC,CAED,MAAO,GAAGA,SAAS,CAACvC,IAAI,CAAC,EAAIA,IAAI,KAAKsC,KAAK,EAAE,CAC/C,CAAC,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CACf,CAAC,CAED;AACA,KAAM,CAAAC,UAAU,CAAI/C,OAAO,EAAK,CAC9B,MAAO,CAAAA,OAAO,CAACI,MAAM,CAACC,MAAM,EAAIA,MAAM,CAACC,IAAI,GAAK,QAAQ,CAAC,CAC3D,CAAC,CAED,mBACE7C,IAAA,CAACV,IAAI,EAACiG,QAAQ,CAAEnE,OAAQ,CAACoE,GAAG,CAAC,uBAAQ,CAAAC,QAAA,cACnCvF,KAAA,CAACK,aAAa,EAAAkF,QAAA,eAEZzF,IAAA,CAACL,kBAAkB,EACjB+F,QAAQ,CAAC,MAAM,CACfC,SAAS,CAAE9E,aAAc,CACzB+E,UAAU,CAAEA,CAAA,GAAMlE,gBAAgB,CAAC,CAACb,aAAa,CAAE,CAAA4E,QAAA,cAEnDzF,IAAA,CAACe,QAAQ,EAAC8E,KAAK,CAAC,sCAAQ,CAACC,QAAQ,CAAE,KAAM,CAAC9E,MAAM,CAAC,MAAM,CAAAyE,QAAA,cACrDzF,IAAA,CAACZ,IAAI,EACH2G,UAAU,CAAC,UAAU,CACrBC,UAAU,CAAE1E,aAAc,CAC1B2E,UAAU,CAAEC,IAAI,eACdhG,KAAA,CAACd,IAAI,CAAC+G,IAAI,EACRC,OAAO,CAAEA,CAAA,GAAMzB,wBAAwB,CAACuB,IAAI,CAAE,CAC9CG,KAAK,CAAE,CACLC,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,CAAA/E,oBAAoB,SAApBA,oBAAoB,iBAApBA,oBAAoB,CAAE2B,EAAE,IAAK+C,IAAI,CAAC/C,EAAE,CAAG,SAAS,CAAG,aAAa,CAC5EqD,OAAO,CAAE,KAAK,CACdC,YAAY,CAAE,KAAK,CACnBC,YAAY,CAAE,KAChB,CAAE,CAAAjB,QAAA,eAEFzF,IAAA,CAACZ,IAAI,CAAC+G,IAAI,CAACQ,IAAI,EACbd,KAAK,cAAE7F,IAAA,SAAMqG,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,MAAO,CAAE,CAAApB,QAAA,CAAES,IAAI,CAAC9C,IAAI,CAAO,CAAE,CACjFC,WAAW,cAAErD,IAAA,SAAMqG,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAO,CAAE,CAAAnB,QAAA,CAAES,IAAI,CAAC7C,WAAW,CAAO,CAAE,CAC3E,CAAC,cACFnD,KAAA,QAAKmG,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEE,SAAS,CAAE,KAAM,CAAE,CAAArB,QAAA,eACjDvF,KAAA,QAAAuF,QAAA,eAAKzF,IAAA,WAAAyF,QAAA,CAAQ,gCAAK,CAAQ,CAAC,IAAC,CAACZ,gBAAgB,CAACqB,IAAI,CAAC3D,OAAO,CAAC,EAAM,CAAC,cAClEvC,IAAA,QAAKqG,KAAK,CAAE,CAAES,SAAS,CAAE,KAAM,CAAE,CAAArB,QAAA,CAC9BS,IAAI,CAAC3D,OAAO,CAAC0C,GAAG,CAACrC,MAAM,eACtB5C,IAAA,CAACT,KAAK,EAEJ+D,MAAM,CAAEV,MAAM,CAACU,MAAM,GAAK,QAAQ,CAAG,SAAS,CAAG,OAAQ,CACzDyD,IAAI,cACF/G,IAAA,SAAMqG,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEI,WAAW,CAAE,KAAM,CAAE,CAAAvB,QAAA,CACnD7C,MAAM,CAACQ,IAAI,CACR,CACP,CACDiD,KAAK,CAAE,CAAEY,OAAO,CAAE,cAAc,CAAED,WAAW,CAAE,KAAM,CAAE,EAPlDpE,MAAM,CAACO,EAQb,CACF,CAAC,CACC,CAAC,EACH,CAAC,EACG,CACX,CACH,CAAC,CACM,CAAC,CACO,CAAC,cAGrBnD,IAAA,CAACW,WAAW,EAACE,aAAa,CAAEA,aAAc,CAACC,cAAc,CAAEA,cAAe,CAE7D,CAAC,cAGdd,IAAA,CAACL,kBAAkB,EACjB+F,QAAQ,CAAC,OAAO,CAChBC,SAAS,CAAE7E,cAAe,CAC1B8E,UAAU,CAAEA,CAAA,GAAMjE,iBAAiB,CAAC,CAACb,cAAc,CAAE,CAAA2E,QAAA,cAErDzF,IAAA,CAACe,QAAQ,EACP8E,KAAK,CAAE,UAAU,CAAArE,oBAAoB,SAApBA,oBAAoB,iBAApBA,oBAAoB,CAAE4B,IAAI,GAAI,EAAE,EAAG,CACpD0C,QAAQ,CAAE,KAAM,CAChB9E,MAAM,CAAC,MAAM,CAAAyE,QAAA,CAEZjE,oBAAoB,cACnBtB,KAAA,CAAAE,SAAA,EAAAqF,QAAA,eACEzF,IAAA,QAAKqG,KAAK,CAAE,CAAEK,YAAY,CAAE,MAAM,CAAEE,QAAQ,CAAE,MAAO,CAAE,CAAAnB,QAAA,cACrDvF,KAAA,CAACb,YAAY,EACX6H,IAAI,CAAC,OAAO,CACZC,MAAM,CAAE,CAAE,CACVC,MAAM,CAAE,CACNC,KAAK,CAAE,CAAET,QAAQ,CAAE,MAAO,CAAC,CAC3BU,OAAO,CAAE,CAAEV,QAAQ,CAAE,MAAO,CAC9B,CAAE,CAAAnB,QAAA,eAEFzF,IAAA,CAACX,YAAY,CAAC8G,IAAI,EAACkB,KAAK,CAAC,0BAAM,CAAA5B,QAAA,CAAEjE,oBAAoB,CAAC4B,IAAI,CAAoB,CAAC,cAC/EpD,IAAA,CAACX,YAAY,CAAC8G,IAAI,EAACkB,KAAK,CAAC,0BAAM,CAAA5B,QAAA,CAAEjE,oBAAoB,CAAC6B,WAAW,CAAoB,CAAC,cACtFrD,IAAA,CAACX,YAAY,CAAC8G,IAAI,EAACkB,KAAK,CAAC,gCAAO,CAAA5B,QAAA,CAAEH,UAAU,CAAC9D,oBAAoB,CAACe,OAAO,CAAC,CAACC,MAAM,CAAoB,CAAC,EAC1F,CAAC,CACZ,CAAC,CAGL8C,UAAU,CAAC9D,oBAAoB,CAACe,OAAO,CAAC,CAACgF,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACtC,GAAG,CAACuC,MAAM,eAC9DtH,KAAA,QAAqBmG,KAAK,CAAE,CAAEK,YAAY,CAAE,MAAO,CAAE,CAAAjB,QAAA,eACnDzF,IAAA,QAAKqG,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEF,YAAY,CAAE,KAAK,CAAEG,UAAU,CAAE,MAAO,CAAE,CAAApB,QAAA,cACxEzF,IAAA,CAACT,KAAK,EACJ+D,MAAM,CAAEkE,MAAM,CAAClE,MAAM,GAAK,QAAQ,CAAG,SAAS,CAAG,OAAQ,CACzDyD,IAAI,CAAES,MAAM,CAACpE,IAAK,CACnB,CAAC,CACC,CAAC,cACNpD,IAAA,CAACiB,cAAc,EAAAwE,QAAA,cACbzF,IAAA,CAACkB,gBAAgB,EAAAuE,QAAA,CACd+B,MAAM,CAAClE,MAAM,GAAK,QAAQ,cACzBtD,IAAA,CAACH,WAAW,EACV4H,QAAQ,CAAED,MAAM,CAACrE,EAAG,CACpBS,OAAO,CAAE4D,MAAM,CAAC5D,OAAQ,CACzB,CAAC,cAEF5D,IAAA,CAAAI,SAAA,EAAAqF,QAAA,CAAE,gCAAK,CAAE,CACV,CACe,CAAC,CACL,CAAC,GAlBT+B,MAAM,CAACrE,EAmBZ,CACN,CAAC,CAEDmC,UAAU,CAAC9D,oBAAoB,CAACe,OAAO,CAAC,CAACC,MAAM,GAAK,CAAC,eACpDxC,IAAA,QAAKqG,KAAK,CAAE,CAAEqB,SAAS,CAAE,QAAQ,CAAElB,OAAO,CAAE,QAAS,CAAE,CAAAf,QAAA,CAAC,8DAExD,CAAK,CACN,EACD,CAAC,cAEHzF,IAAA,QAAKqG,KAAK,CAAE,CAAEqB,SAAS,CAAE,QAAQ,CAAElB,OAAO,CAAE,QAAS,CAAE,CAAAf,QAAA,CAAC,gFAExD,CAAK,CACN,CACO,CAAC,CACO,CAAC,EACR,CAAC,CACZ,CAAC,CAEX,CAAC,CAED,cAAe,CAAAtE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}