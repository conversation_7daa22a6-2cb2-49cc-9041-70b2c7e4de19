{"ast": null, "code": "// 修复 API 登录\nexport const login = async (username, password) => {\n  try {\n    console.log('尝试登录:', {\n      username,\n      password\n    });\n\n    // 如果是开发环境，可以添加模拟登录\n    if (process.env.NODE_ENV === 'development' && username === 'admin' && password === 'admin123') {\n      console.log('使用开发环境模拟登录');\n      return {\n        success: true,\n        data: {\n          token: 'dev-token-' + Date.now(),\n          user: {\n            username: 'admin',\n            role: 'admin'\n          }\n        }\n      };\n    }\n\n    // 使用完整的 URL\n    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n    // 实际 API 调用\n    const response = await fetch(`${apiUrl}/api/login`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        username,\n        password\n      })\n    });\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    console.error('登录 API 调用失败:', error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["login", "username", "password", "console", "log", "process", "env", "NODE_ENV", "success", "data", "token", "Date", "now", "user", "role", "apiUrl", "REACT_APP_API_URL", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "status", "json", "error"], "sources": ["G:/AI_tools/cursor/projects/education web/src/services/auth.js"], "sourcesContent": ["// 修复 API 登录\r\nexport const login = async (username, password) => {\r\n  try {\r\n    console.log('尝试登录:', { username, password });\r\n    \r\n    // 如果是开发环境，可以添加模拟登录\r\n    if (process.env.NODE_ENV === 'development' && username === 'admin' && password === 'admin123') {\r\n      console.log('使用开发环境模拟登录');\r\n      return {\r\n        success: true,\r\n        data: {\r\n          token: 'dev-token-' + Date.now(),\r\n          user: { username: 'admin', role: 'admin' }\r\n        }\r\n      };\r\n    }\r\n    \r\n    // 使用完整的 URL\r\n    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n    \r\n    // 实际 API 调用\r\n    const response = await fetch(`${apiUrl}/api/login`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json'\r\n      },\r\n      body: JSON.stringify({ username, password })\r\n    });\r\n    \r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n    \r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error('登录 API 调用失败:', error);\r\n    throw error;\r\n  }\r\n}; "], "mappings": "AAAA;AACA,OAAO,MAAMA,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;EACjD,IAAI;IACFC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MAAEH,QAAQ;MAAEC;IAAS,CAAC,CAAC;;IAE5C;IACA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIN,QAAQ,KAAK,OAAO,IAAIC,QAAQ,KAAK,UAAU,EAAE;MAC7FC,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;MACzB,OAAO;QACLI,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJC,KAAK,EAAE,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;UAChCC,IAAI,EAAE;YAAEZ,QAAQ,EAAE,OAAO;YAAEa,IAAI,EAAE;UAAQ;QAC3C;MACF,CAAC;IACH;;IAEA;IACA,MAAMC,MAAM,GAAGV,OAAO,CAACC,GAAG,CAACU,iBAAiB,IAAI,uBAAuB;;IAEvE;IACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGH,MAAM,YAAY,EAAE;MAClDI,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEtB,QAAQ;QAAEC;MAAS,CAAC;IAC7C,CAAC,CAAC;IAEF,IAAI,CAACe,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBR,QAAQ,CAACS,MAAM,EAAE,CAAC;IAC3D;IAEA,MAAMjB,IAAI,GAAG,MAAMQ,QAAQ,CAACU,IAAI,CAAC,CAAC;IAClC,OAAOlB,IAAI;EACb,CAAC,CAAC,OAAOmB,KAAK,EAAE;IACdzB,OAAO,CAACyB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACpC,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}