{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/* global Uint8ClampedArray */\nimport { platformApi } from 'zrender/lib/core/platform.js';\nvar GRADIENT_LEVELS = 256;\nvar HeatmapLayer = /** @class */function () {\n  function HeatmapLayer() {\n    this.blurSize = 30;\n    this.pointSize = 20;\n    this.maxOpacity = 1;\n    this.minOpacity = 0;\n    this._gradientPixels = {\n      inRange: null,\n      outOfRange: null\n    };\n    var canvas = platformApi.createCanvas();\n    this.canvas = canvas;\n  }\n  /**\r\n   * Renders Heatmap and returns the rendered canvas\r\n   * @param data array of data, each has x, y, value\r\n   * @param width canvas width\r\n   * @param height canvas height\r\n   */\n  HeatmapLayer.prototype.update = function (data, width, height, normalize, colorFunc, isInRange) {\n    var brush = this._getBrush();\n    var gradientInRange = this._getGradient(colorFunc, 'inRange');\n    var gradientOutOfRange = this._getGradient(colorFunc, 'outOfRange');\n    var r = this.pointSize + this.blurSize;\n    var canvas = this.canvas;\n    var ctx = canvas.getContext('2d');\n    var len = data.length;\n    canvas.width = width;\n    canvas.height = height;\n    for (var i = 0; i < len; ++i) {\n      var p = data[i];\n      var x = p[0];\n      var y = p[1];\n      var value = p[2];\n      // calculate alpha using value\n      var alpha = normalize(value);\n      // draw with the circle brush with alpha\n      ctx.globalAlpha = alpha;\n      ctx.drawImage(brush, x - r, y - r);\n    }\n    if (!canvas.width || !canvas.height) {\n      // Avoid \"Uncaught DOMException: Failed to execute 'getImageData' on\n      // 'CanvasRenderingContext2D': The source height is 0.\"\n      return canvas;\n    }\n    // colorize the canvas using alpha value and set with gradient\n    var imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n    var pixels = imageData.data;\n    var offset = 0;\n    var pixelLen = pixels.length;\n    var minOpacity = this.minOpacity;\n    var maxOpacity = this.maxOpacity;\n    var diffOpacity = maxOpacity - minOpacity;\n    while (offset < pixelLen) {\n      var alpha = pixels[offset + 3] / 256;\n      var gradientOffset = Math.floor(alpha * (GRADIENT_LEVELS - 1)) * 4;\n      // Simple optimize to ignore the empty data\n      if (alpha > 0) {\n        var gradient = isInRange(alpha) ? gradientInRange : gradientOutOfRange;\n        // Any alpha > 0 will be mapped to [minOpacity, maxOpacity]\n        alpha > 0 && (alpha = alpha * diffOpacity + minOpacity);\n        pixels[offset++] = gradient[gradientOffset];\n        pixels[offset++] = gradient[gradientOffset + 1];\n        pixels[offset++] = gradient[gradientOffset + 2];\n        pixels[offset++] = gradient[gradientOffset + 3] * alpha * 256;\n      } else {\n        offset += 4;\n      }\n    }\n    ctx.putImageData(imageData, 0, 0);\n    return canvas;\n  };\n  /**\r\n   * get canvas of a black circle brush used for canvas to draw later\r\n   */\n  HeatmapLayer.prototype._getBrush = function () {\n    var brushCanvas = this._brushCanvas || (this._brushCanvas = platformApi.createCanvas());\n    // set brush size\n    var r = this.pointSize + this.blurSize;\n    var d = r * 2;\n    brushCanvas.width = d;\n    brushCanvas.height = d;\n    var ctx = brushCanvas.getContext('2d');\n    ctx.clearRect(0, 0, d, d);\n    // in order to render shadow without the distinct circle,\n    // draw the distinct circle in an invisible place,\n    // and use shadowOffset to draw shadow in the center of the canvas\n    ctx.shadowOffsetX = d;\n    ctx.shadowBlur = this.blurSize;\n    // draw the shadow in black, and use alpha and shadow blur to generate\n    // color in color map\n    ctx.shadowColor = '#000';\n    // draw circle in the left to the canvas\n    ctx.beginPath();\n    ctx.arc(-r, r, this.pointSize, 0, Math.PI * 2, true);\n    ctx.closePath();\n    ctx.fill();\n    return brushCanvas;\n  };\n  /**\r\n   * get gradient color map\r\n   * @private\r\n   */\n  HeatmapLayer.prototype._getGradient = function (colorFunc, state) {\n    var gradientPixels = this._gradientPixels;\n    var pixelsSingleState = gradientPixels[state] || (gradientPixels[state] = new Uint8ClampedArray(256 * 4));\n    var color = [0, 0, 0, 0];\n    var off = 0;\n    for (var i = 0; i < 256; i++) {\n      colorFunc[state](i / 255, true, color);\n      pixelsSingleState[off++] = color[0];\n      pixelsSingleState[off++] = color[1];\n      pixelsSingleState[off++] = color[2];\n      pixelsSingleState[off++] = color[3];\n    }\n    return pixelsSingleState;\n  };\n  return HeatmapLayer;\n}();\nexport default HeatmapLayer;", "map": {"version": 3, "names": ["platformApi", "GRADIENT_LEVELS", "Heatmap<PERSON>ayer", "blurSize", "pointSize", "maxOpacity", "minOpacity", "_gradientPixels", "inRange", "outOfRange", "canvas", "createCanvas", "prototype", "update", "data", "width", "height", "normalize", "colorFunc", "isInRange", "brush", "_getBrush", "gradientInRange", "_getGradient", "gradientOutOfRange", "r", "ctx", "getContext", "len", "length", "i", "p", "x", "y", "value", "alpha", "globalAlpha", "drawImage", "imageData", "getImageData", "pixels", "offset", "pixelLen", "diffOpacity", "gradientOffset", "Math", "floor", "gradient", "putImageData", "brushCanvas", "_brush<PERSON>anvas", "d", "clearRect", "shadowOffsetX", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "beginPath", "arc", "PI", "closePath", "fill", "state", "gradientPixels", "pixelsSingleState", "Uint8ClampedArray", "color", "off"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/echarts/lib/chart/heatmap/HeatmapLayer.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/* global Uint8ClampedArray */\nimport { platformApi } from 'zrender/lib/core/platform.js';\nvar GRADIENT_LEVELS = 256;\nvar HeatmapLayer = /** @class */function () {\n  function HeatmapLayer() {\n    this.blurSize = 30;\n    this.pointSize = 20;\n    this.maxOpacity = 1;\n    this.minOpacity = 0;\n    this._gradientPixels = {\n      inRange: null,\n      outOfRange: null\n    };\n    var canvas = platformApi.createCanvas();\n    this.canvas = canvas;\n  }\n  /**\r\n   * Renders Heatmap and returns the rendered canvas\r\n   * @param data array of data, each has x, y, value\r\n   * @param width canvas width\r\n   * @param height canvas height\r\n   */\n  HeatmapLayer.prototype.update = function (data, width, height, normalize, colorFunc, isInRange) {\n    var brush = this._getBrush();\n    var gradientInRange = this._getGradient(colorFunc, 'inRange');\n    var gradientOutOfRange = this._getGradient(colorFunc, 'outOfRange');\n    var r = this.pointSize + this.blurSize;\n    var canvas = this.canvas;\n    var ctx = canvas.getContext('2d');\n    var len = data.length;\n    canvas.width = width;\n    canvas.height = height;\n    for (var i = 0; i < len; ++i) {\n      var p = data[i];\n      var x = p[0];\n      var y = p[1];\n      var value = p[2];\n      // calculate alpha using value\n      var alpha = normalize(value);\n      // draw with the circle brush with alpha\n      ctx.globalAlpha = alpha;\n      ctx.drawImage(brush, x - r, y - r);\n    }\n    if (!canvas.width || !canvas.height) {\n      // Avoid \"Uncaught DOMException: Failed to execute 'getImageData' on\n      // 'CanvasRenderingContext2D': The source height is 0.\"\n      return canvas;\n    }\n    // colorize the canvas using alpha value and set with gradient\n    var imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);\n    var pixels = imageData.data;\n    var offset = 0;\n    var pixelLen = pixels.length;\n    var minOpacity = this.minOpacity;\n    var maxOpacity = this.maxOpacity;\n    var diffOpacity = maxOpacity - minOpacity;\n    while (offset < pixelLen) {\n      var alpha = pixels[offset + 3] / 256;\n      var gradientOffset = Math.floor(alpha * (GRADIENT_LEVELS - 1)) * 4;\n      // Simple optimize to ignore the empty data\n      if (alpha > 0) {\n        var gradient = isInRange(alpha) ? gradientInRange : gradientOutOfRange;\n        // Any alpha > 0 will be mapped to [minOpacity, maxOpacity]\n        alpha > 0 && (alpha = alpha * diffOpacity + minOpacity);\n        pixels[offset++] = gradient[gradientOffset];\n        pixels[offset++] = gradient[gradientOffset + 1];\n        pixels[offset++] = gradient[gradientOffset + 2];\n        pixels[offset++] = gradient[gradientOffset + 3] * alpha * 256;\n      } else {\n        offset += 4;\n      }\n    }\n    ctx.putImageData(imageData, 0, 0);\n    return canvas;\n  };\n  /**\r\n   * get canvas of a black circle brush used for canvas to draw later\r\n   */\n  HeatmapLayer.prototype._getBrush = function () {\n    var brushCanvas = this._brushCanvas || (this._brushCanvas = platformApi.createCanvas());\n    // set brush size\n    var r = this.pointSize + this.blurSize;\n    var d = r * 2;\n    brushCanvas.width = d;\n    brushCanvas.height = d;\n    var ctx = brushCanvas.getContext('2d');\n    ctx.clearRect(0, 0, d, d);\n    // in order to render shadow without the distinct circle,\n    // draw the distinct circle in an invisible place,\n    // and use shadowOffset to draw shadow in the center of the canvas\n    ctx.shadowOffsetX = d;\n    ctx.shadowBlur = this.blurSize;\n    // draw the shadow in black, and use alpha and shadow blur to generate\n    // color in color map\n    ctx.shadowColor = '#000';\n    // draw circle in the left to the canvas\n    ctx.beginPath();\n    ctx.arc(-r, r, this.pointSize, 0, Math.PI * 2, true);\n    ctx.closePath();\n    ctx.fill();\n    return brushCanvas;\n  };\n  /**\r\n   * get gradient color map\r\n   * @private\r\n   */\n  HeatmapLayer.prototype._getGradient = function (colorFunc, state) {\n    var gradientPixels = this._gradientPixels;\n    var pixelsSingleState = gradientPixels[state] || (gradientPixels[state] = new Uint8ClampedArray(256 * 4));\n    var color = [0, 0, 0, 0];\n    var off = 0;\n    for (var i = 0; i < 256; i++) {\n      colorFunc[state](i / 255, true, color);\n      pixelsSingleState[off++] = color[0];\n      pixelsSingleState[off++] = color[1];\n      pixelsSingleState[off++] = color[2];\n      pixelsSingleState[off++] = color[3];\n    }\n    return pixelsSingleState;\n  };\n  return HeatmapLayer;\n}();\nexport default HeatmapLayer;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAW,QAAQ,8BAA8B;AAC1D,IAAIC,eAAe,GAAG,GAAG;AACzB,IAAIC,YAAY,GAAG,aAAa,YAAY;EAC1C,SAASA,YAAYA,CAAA,EAAG;IACtB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,eAAe,GAAG;MACrBC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE;IACd,CAAC;IACD,IAAIC,MAAM,GAAGV,WAAW,CAACW,YAAY,CAAC,CAAC;IACvC,IAAI,CAACD,MAAM,GAAGA,MAAM;EACtB;EACA;AACF;AACA;AACA;AACA;AACA;EACER,YAAY,CAACU,SAAS,CAACC,MAAM,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAE;IAC9F,IAAIC,KAAK,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAC5B,IAAIC,eAAe,GAAG,IAAI,CAACC,YAAY,CAACL,SAAS,EAAE,SAAS,CAAC;IAC7D,IAAIM,kBAAkB,GAAG,IAAI,CAACD,YAAY,CAACL,SAAS,EAAE,YAAY,CAAC;IACnE,IAAIO,CAAC,GAAG,IAAI,CAACrB,SAAS,GAAG,IAAI,CAACD,QAAQ;IACtC,IAAIO,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAIgB,GAAG,GAAGhB,MAAM,CAACiB,UAAU,CAAC,IAAI,CAAC;IACjC,IAAIC,GAAG,GAAGd,IAAI,CAACe,MAAM;IACrBnB,MAAM,CAACK,KAAK,GAAGA,KAAK;IACpBL,MAAM,CAACM,MAAM,GAAGA,MAAM;IACtB,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAE,EAAEE,CAAC,EAAE;MAC5B,IAAIC,CAAC,GAAGjB,IAAI,CAACgB,CAAC,CAAC;MACf,IAAIE,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC;MACZ,IAAIE,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;MACZ,IAAIG,KAAK,GAAGH,CAAC,CAAC,CAAC,CAAC;MAChB;MACA,IAAII,KAAK,GAAGlB,SAAS,CAACiB,KAAK,CAAC;MAC5B;MACAR,GAAG,CAACU,WAAW,GAAGD,KAAK;MACvBT,GAAG,CAACW,SAAS,CAACjB,KAAK,EAAEY,CAAC,GAAGP,CAAC,EAAEQ,CAAC,GAAGR,CAAC,CAAC;IACpC;IACA,IAAI,CAACf,MAAM,CAACK,KAAK,IAAI,CAACL,MAAM,CAACM,MAAM,EAAE;MACnC;MACA;MACA,OAAON,MAAM;IACf;IACA;IACA,IAAI4B,SAAS,GAAGZ,GAAG,CAACa,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE7B,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACM,MAAM,CAAC;IACnE,IAAIwB,MAAM,GAAGF,SAAS,CAACxB,IAAI;IAC3B,IAAI2B,MAAM,GAAG,CAAC;IACd,IAAIC,QAAQ,GAAGF,MAAM,CAACX,MAAM;IAC5B,IAAIvB,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAID,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAIsC,WAAW,GAAGtC,UAAU,GAAGC,UAAU;IACzC,OAAOmC,MAAM,GAAGC,QAAQ,EAAE;MACxB,IAAIP,KAAK,GAAGK,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;MACpC,IAAIG,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACX,KAAK,IAAIlC,eAAe,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;MAClE;MACA,IAAIkC,KAAK,GAAG,CAAC,EAAE;QACb,IAAIY,QAAQ,GAAG5B,SAAS,CAACgB,KAAK,CAAC,GAAGb,eAAe,GAAGE,kBAAkB;QACtE;QACAW,KAAK,GAAG,CAAC,KAAKA,KAAK,GAAGA,KAAK,GAAGQ,WAAW,GAAGrC,UAAU,CAAC;QACvDkC,MAAM,CAACC,MAAM,EAAE,CAAC,GAAGM,QAAQ,CAACH,cAAc,CAAC;QAC3CJ,MAAM,CAACC,MAAM,EAAE,CAAC,GAAGM,QAAQ,CAACH,cAAc,GAAG,CAAC,CAAC;QAC/CJ,MAAM,CAACC,MAAM,EAAE,CAAC,GAAGM,QAAQ,CAACH,cAAc,GAAG,CAAC,CAAC;QAC/CJ,MAAM,CAACC,MAAM,EAAE,CAAC,GAAGM,QAAQ,CAACH,cAAc,GAAG,CAAC,CAAC,GAAGT,KAAK,GAAG,GAAG;MAC/D,CAAC,MAAM;QACLM,MAAM,IAAI,CAAC;MACb;IACF;IACAf,GAAG,CAACsB,YAAY,CAACV,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;IACjC,OAAO5B,MAAM;EACf,CAAC;EACD;AACF;AACA;EACER,YAAY,CAACU,SAAS,CAACS,SAAS,GAAG,YAAY;IAC7C,IAAI4B,WAAW,GAAG,IAAI,CAACC,YAAY,KAAK,IAAI,CAACA,YAAY,GAAGlD,WAAW,CAACW,YAAY,CAAC,CAAC,CAAC;IACvF;IACA,IAAIc,CAAC,GAAG,IAAI,CAACrB,SAAS,GAAG,IAAI,CAACD,QAAQ;IACtC,IAAIgD,CAAC,GAAG1B,CAAC,GAAG,CAAC;IACbwB,WAAW,CAAClC,KAAK,GAAGoC,CAAC;IACrBF,WAAW,CAACjC,MAAM,GAAGmC,CAAC;IACtB,IAAIzB,GAAG,GAAGuB,WAAW,CAACtB,UAAU,CAAC,IAAI,CAAC;IACtCD,GAAG,CAAC0B,SAAS,CAAC,CAAC,EAAE,CAAC,EAAED,CAAC,EAAEA,CAAC,CAAC;IACzB;IACA;IACA;IACAzB,GAAG,CAAC2B,aAAa,GAAGF,CAAC;IACrBzB,GAAG,CAAC4B,UAAU,GAAG,IAAI,CAACnD,QAAQ;IAC9B;IACA;IACAuB,GAAG,CAAC6B,WAAW,GAAG,MAAM;IACxB;IACA7B,GAAG,CAAC8B,SAAS,CAAC,CAAC;IACf9B,GAAG,CAAC+B,GAAG,CAAC,CAAChC,CAAC,EAAEA,CAAC,EAAE,IAAI,CAACrB,SAAS,EAAE,CAAC,EAAEyC,IAAI,CAACa,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC;IACpDhC,GAAG,CAACiC,SAAS,CAAC,CAAC;IACfjC,GAAG,CAACkC,IAAI,CAAC,CAAC;IACV,OAAOX,WAAW;EACpB,CAAC;EACD;AACF;AACA;AACA;EACE/C,YAAY,CAACU,SAAS,CAACW,YAAY,GAAG,UAAUL,SAAS,EAAE2C,KAAK,EAAE;IAChE,IAAIC,cAAc,GAAG,IAAI,CAACvD,eAAe;IACzC,IAAIwD,iBAAiB,GAAGD,cAAc,CAACD,KAAK,CAAC,KAAKC,cAAc,CAACD,KAAK,CAAC,GAAG,IAAIG,iBAAiB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IACzG,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxB,IAAIC,GAAG,GAAG,CAAC;IACX,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;MAC5BZ,SAAS,CAAC2C,KAAK,CAAC,CAAC/B,CAAC,GAAG,GAAG,EAAE,IAAI,EAAEmC,KAAK,CAAC;MACtCF,iBAAiB,CAACG,GAAG,EAAE,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC;MACnCF,iBAAiB,CAACG,GAAG,EAAE,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC;MACnCF,iBAAiB,CAACG,GAAG,EAAE,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC;MACnCF,iBAAiB,CAACG,GAAG,EAAE,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC;IACrC;IACA,OAAOF,iBAAiB;EAC1B,CAAC;EACD,OAAO7D,YAAY;AACrB,CAAC,CAAC,CAAC;AACH,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}