{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useMergedState } from 'rc-util';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { fillTime, isSame } from \"../../utils/dateUtil\";\nexport function offsetPanelDate(generateConfig, picker, date, offset) {\n  switch (picker) {\n    case 'date':\n    case 'week':\n      return generateConfig.addMonth(date, offset);\n    case 'month':\n    case 'quarter':\n      return generateConfig.addYear(date, offset);\n    case 'year':\n      return generateConfig.addYear(date, offset * 10);\n    case 'decade':\n      return generateConfig.addYear(date, offset * 100);\n    default:\n      return date;\n  }\n}\nvar EMPTY_LIST = [];\nexport default function useRangePickerValue(generateConfig, locale, calendarValue, modes, open, activeIndex, pickerMode, multiplePanel) {\n  var defaultPickerValue = arguments.length > 8 && arguments[8] !== undefined ? arguments[8] : EMPTY_LIST;\n  var pickerValue = arguments.length > 9 && arguments[9] !== undefined ? arguments[9] : EMPTY_LIST;\n  var timeDefaultValue = arguments.length > 10 && arguments[10] !== undefined ? arguments[10] : EMPTY_LIST;\n  var onPickerValueChange = arguments.length > 11 ? arguments[11] : undefined;\n  var minDate = arguments.length > 12 ? arguments[12] : undefined;\n  var maxDate = arguments.length > 13 ? arguments[13] : undefined;\n  var isTimePicker = pickerMode === 'time';\n\n  // ======================== Active ========================\n  // `activeIndex` must be valid to avoid getting empty `pickerValue`\n  var mergedActiveIndex = activeIndex || 0;\n\n  // ===================== Picker Value =====================\n  var getDefaultPickerValue = function getDefaultPickerValue(index) {\n    var now = generateConfig.getNow();\n    if (isTimePicker) {\n      now = fillTime(generateConfig, now);\n    }\n    return defaultPickerValue[index] || calendarValue[index] || now;\n  };\n\n  // Align `pickerValue` with `showTime.defaultValue`\n  var _pickerValue = _slicedToArray(pickerValue, 2),\n    startPickerValue = _pickerValue[0],\n    endPickerValue = _pickerValue[1];\n\n  // PickerValue state\n  var _useMergedState = useMergedState(function () {\n      return getDefaultPickerValue(0);\n    }, {\n      value: startPickerValue\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedStartPickerValue = _useMergedState2[0],\n    setStartPickerValue = _useMergedState2[1];\n  var _useMergedState3 = useMergedState(function () {\n      return getDefaultPickerValue(1);\n    }, {\n      value: endPickerValue\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedEndPickerValue = _useMergedState4[0],\n    setEndPickerValue = _useMergedState4[1];\n\n  // Current PickerValue\n  var currentPickerValue = React.useMemo(function () {\n    var current = [mergedStartPickerValue, mergedEndPickerValue][mergedActiveIndex];\n\n    // Merge the `showTime.defaultValue` into `pickerValue`\n    return isTimePicker ? current : fillTime(generateConfig, current, timeDefaultValue[mergedActiveIndex]);\n  }, [isTimePicker, mergedStartPickerValue, mergedEndPickerValue, mergedActiveIndex, generateConfig, timeDefaultValue]);\n  var setCurrentPickerValue = function setCurrentPickerValue(nextPickerValue) {\n    var source = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'panel';\n    var updater = [setStartPickerValue, setEndPickerValue][mergedActiveIndex];\n    updater(nextPickerValue);\n    var clone = [mergedStartPickerValue, mergedEndPickerValue];\n    clone[mergedActiveIndex] = nextPickerValue;\n    if (onPickerValueChange && (!isSame(generateConfig, locale, mergedStartPickerValue, clone[0], pickerMode) || !isSame(generateConfig, locale, mergedEndPickerValue, clone[1], pickerMode))) {\n      onPickerValueChange(clone, {\n        source: source,\n        range: mergedActiveIndex === 1 ? 'end' : 'start',\n        mode: modes\n      });\n    }\n  };\n\n  // ======================== Effect ========================\n  /**\n   * EndDate pickerValue is little different. It should be:\n   * - If date picker (without time), endDate is not same year & month as startDate\n   *   - pickerValue minus one month\n   * - Else pass directly\n   */\n  var getEndDatePickerValue = function getEndDatePickerValue(startDate, endDate) {\n    if (multiplePanel) {\n      // Basic offset\n      var SAME_CHECKER = {\n        date: 'month',\n        week: 'month',\n        month: 'year',\n        quarter: 'year'\n      };\n      var mode = SAME_CHECKER[pickerMode];\n      if (mode && !isSame(generateConfig, locale, startDate, endDate, mode)) {\n        return offsetPanelDate(generateConfig, pickerMode, endDate, -1);\n      }\n\n      // Year offset\n      if (pickerMode === 'year' && startDate) {\n        var srcYear = Math.floor(generateConfig.getYear(startDate) / 10);\n        var tgtYear = Math.floor(generateConfig.getYear(endDate) / 10);\n        if (srcYear !== tgtYear) {\n          return offsetPanelDate(generateConfig, pickerMode, endDate, -1);\n        }\n      }\n    }\n    return endDate;\n  };\n\n  // >>> When switch field, reset the picker value as prev field picker value\n  var prevActiveIndexRef = React.useRef(null);\n  useLayoutEffect(function () {\n    if (open) {\n      if (!defaultPickerValue[mergedActiveIndex]) {\n        var nextPickerValue = isTimePicker ? null : generateConfig.getNow();\n\n        /**\n         * 1. If has prevActiveIndex, use it to avoid panel jump\n         * 2. If current field has value\n         *    - If `activeIndex` is 1 and `calendarValue[0]` is not same panel as `calendarValue[1]`,\n         *      offset `calendarValue[1]` and set it\n         *    - Else use `calendarValue[activeIndex]`\n         * 3. If current field has no value but another field has value, use another field value\n         * 4. Else use now (not any `calendarValue` can ref)\n         */\n\n        if (prevActiveIndexRef.current !== null && prevActiveIndexRef.current !== mergedActiveIndex) {\n          // If from another field, not jump picker value\n          nextPickerValue = [mergedStartPickerValue, mergedEndPickerValue][mergedActiveIndex ^ 1];\n        } else if (calendarValue[mergedActiveIndex]) {\n          // Current field has value\n          nextPickerValue = mergedActiveIndex === 0 ? calendarValue[0] : getEndDatePickerValue(calendarValue[0], calendarValue[1]);\n        } else if (calendarValue[mergedActiveIndex ^ 1]) {\n          // Current field has no value but another field has value\n          nextPickerValue = calendarValue[mergedActiveIndex ^ 1];\n        }\n\n        // Only sync when has value, this will sync in the `min-max` logic\n        if (nextPickerValue) {\n          // nextPickerValue < minDate\n          if (minDate && generateConfig.isAfter(minDate, nextPickerValue)) {\n            nextPickerValue = minDate;\n          }\n\n          // maxDate < nextPickerValue\n          var offsetPickerValue = multiplePanel ? offsetPanelDate(generateConfig, pickerMode, nextPickerValue, 1) : nextPickerValue;\n          if (maxDate && generateConfig.isAfter(offsetPickerValue, maxDate)) {\n            nextPickerValue = multiplePanel ? offsetPanelDate(generateConfig, pickerMode, maxDate, -1) : maxDate;\n          }\n          setCurrentPickerValue(nextPickerValue, 'reset');\n        }\n      }\n    }\n  }, [open, mergedActiveIndex, calendarValue[mergedActiveIndex]]);\n\n  // >>> Reset prevActiveIndex when panel closed\n  React.useEffect(function () {\n    if (open) {\n      prevActiveIndexRef.current = mergedActiveIndex;\n    } else {\n      prevActiveIndexRef.current = null;\n    }\n  }, [open, mergedActiveIndex]);\n\n  // >>> defaultPickerValue: Resync to `defaultPickerValue` for each panel focused\n  useLayoutEffect(function () {\n    if (open && defaultPickerValue) {\n      if (defaultPickerValue[mergedActiveIndex]) {\n        setCurrentPickerValue(defaultPickerValue[mergedActiveIndex], 'reset');\n      }\n    }\n  }, [open, mergedActiveIndex]);\n  return [currentPickerValue, setCurrentPickerValue];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useMergedState", "useLayoutEffect", "React", "fillTime", "isSame", "offsetPanelDate", "generateConfig", "picker", "date", "offset", "addMonth", "addYear", "EMPTY_LIST", "useRangePickerValue", "locale", "calendarValue", "modes", "open", "activeIndex", "pickerMode", "multiplePanel", "defaultPickerValue", "arguments", "length", "undefined", "picker<PERSON><PERSON><PERSON>", "timeDefaultValue", "onPickerValueChange", "minDate", "maxDate", "isTimePicker", "mergedActiveIndex", "getDefaultPickerValue", "index", "now", "getNow", "_pickerValue", "startPickerValue", "endPickerValue", "_useMergedState", "value", "_useMergedState2", "mergedStartPickerValue", "setStartPickerValue", "_useMergedState3", "_useMergedState4", "mergedEndPickerValue", "setEndPickerValue", "currentPickerValue", "useMemo", "current", "setCurrentPickerValue", "nextPickerValue", "source", "updater", "clone", "range", "mode", "getEndDatePickerValue", "startDate", "endDate", "SAME_CHECKER", "week", "month", "quarter", "srcYear", "Math", "floor", "getYear", "tgtYear", "prevActiveIndexRef", "useRef", "isAfter", "offsetPickerValue", "useEffect"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-picker/es/PickerInput/hooks/useRangePickerValue.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useMergedState } from 'rc-util';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport * as React from 'react';\nimport { fillTime, isSame } from \"../../utils/dateUtil\";\nexport function offsetPanelDate(generateConfig, picker, date, offset) {\n  switch (picker) {\n    case 'date':\n    case 'week':\n      return generateConfig.addMonth(date, offset);\n    case 'month':\n    case 'quarter':\n      return generateConfig.addYear(date, offset);\n    case 'year':\n      return generateConfig.addYear(date, offset * 10);\n    case 'decade':\n      return generateConfig.addYear(date, offset * 100);\n    default:\n      return date;\n  }\n}\nvar EMPTY_LIST = [];\nexport default function useRangePickerValue(generateConfig, locale, calendarValue, modes, open, activeIndex, pickerMode, multiplePanel) {\n  var defaultPickerValue = arguments.length > 8 && arguments[8] !== undefined ? arguments[8] : EMPTY_LIST;\n  var pickerValue = arguments.length > 9 && arguments[9] !== undefined ? arguments[9] : EMPTY_LIST;\n  var timeDefaultValue = arguments.length > 10 && arguments[10] !== undefined ? arguments[10] : EMPTY_LIST;\n  var onPickerValueChange = arguments.length > 11 ? arguments[11] : undefined;\n  var minDate = arguments.length > 12 ? arguments[12] : undefined;\n  var maxDate = arguments.length > 13 ? arguments[13] : undefined;\n  var isTimePicker = pickerMode === 'time';\n\n  // ======================== Active ========================\n  // `activeIndex` must be valid to avoid getting empty `pickerValue`\n  var mergedActiveIndex = activeIndex || 0;\n\n  // ===================== Picker Value =====================\n  var getDefaultPickerValue = function getDefaultPickerValue(index) {\n    var now = generateConfig.getNow();\n    if (isTimePicker) {\n      now = fillTime(generateConfig, now);\n    }\n    return defaultPickerValue[index] || calendarValue[index] || now;\n  };\n\n  // Align `pickerValue` with `showTime.defaultValue`\n  var _pickerValue = _slicedToArray(pickerValue, 2),\n    startPickerValue = _pickerValue[0],\n    endPickerValue = _pickerValue[1];\n\n  // PickerValue state\n  var _useMergedState = useMergedState(function () {\n      return getDefaultPickerValue(0);\n    }, {\n      value: startPickerValue\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedStartPickerValue = _useMergedState2[0],\n    setStartPickerValue = _useMergedState2[1];\n  var _useMergedState3 = useMergedState(function () {\n      return getDefaultPickerValue(1);\n    }, {\n      value: endPickerValue\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedEndPickerValue = _useMergedState4[0],\n    setEndPickerValue = _useMergedState4[1];\n\n  // Current PickerValue\n  var currentPickerValue = React.useMemo(function () {\n    var current = [mergedStartPickerValue, mergedEndPickerValue][mergedActiveIndex];\n\n    // Merge the `showTime.defaultValue` into `pickerValue`\n    return isTimePicker ? current : fillTime(generateConfig, current, timeDefaultValue[mergedActiveIndex]);\n  }, [isTimePicker, mergedStartPickerValue, mergedEndPickerValue, mergedActiveIndex, generateConfig, timeDefaultValue]);\n  var setCurrentPickerValue = function setCurrentPickerValue(nextPickerValue) {\n    var source = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'panel';\n    var updater = [setStartPickerValue, setEndPickerValue][mergedActiveIndex];\n    updater(nextPickerValue);\n    var clone = [mergedStartPickerValue, mergedEndPickerValue];\n    clone[mergedActiveIndex] = nextPickerValue;\n    if (onPickerValueChange && (!isSame(generateConfig, locale, mergedStartPickerValue, clone[0], pickerMode) || !isSame(generateConfig, locale, mergedEndPickerValue, clone[1], pickerMode))) {\n      onPickerValueChange(clone, {\n        source: source,\n        range: mergedActiveIndex === 1 ? 'end' : 'start',\n        mode: modes\n      });\n    }\n  };\n\n  // ======================== Effect ========================\n  /**\n   * EndDate pickerValue is little different. It should be:\n   * - If date picker (without time), endDate is not same year & month as startDate\n   *   - pickerValue minus one month\n   * - Else pass directly\n   */\n  var getEndDatePickerValue = function getEndDatePickerValue(startDate, endDate) {\n    if (multiplePanel) {\n      // Basic offset\n      var SAME_CHECKER = {\n        date: 'month',\n        week: 'month',\n        month: 'year',\n        quarter: 'year'\n      };\n      var mode = SAME_CHECKER[pickerMode];\n      if (mode && !isSame(generateConfig, locale, startDate, endDate, mode)) {\n        return offsetPanelDate(generateConfig, pickerMode, endDate, -1);\n      }\n\n      // Year offset\n      if (pickerMode === 'year' && startDate) {\n        var srcYear = Math.floor(generateConfig.getYear(startDate) / 10);\n        var tgtYear = Math.floor(generateConfig.getYear(endDate) / 10);\n        if (srcYear !== tgtYear) {\n          return offsetPanelDate(generateConfig, pickerMode, endDate, -1);\n        }\n      }\n    }\n    return endDate;\n  };\n\n  // >>> When switch field, reset the picker value as prev field picker value\n  var prevActiveIndexRef = React.useRef(null);\n  useLayoutEffect(function () {\n    if (open) {\n      if (!defaultPickerValue[mergedActiveIndex]) {\n        var nextPickerValue = isTimePicker ? null : generateConfig.getNow();\n\n        /**\n         * 1. If has prevActiveIndex, use it to avoid panel jump\n         * 2. If current field has value\n         *    - If `activeIndex` is 1 and `calendarValue[0]` is not same panel as `calendarValue[1]`,\n         *      offset `calendarValue[1]` and set it\n         *    - Else use `calendarValue[activeIndex]`\n         * 3. If current field has no value but another field has value, use another field value\n         * 4. Else use now (not any `calendarValue` can ref)\n         */\n\n        if (prevActiveIndexRef.current !== null && prevActiveIndexRef.current !== mergedActiveIndex) {\n          // If from another field, not jump picker value\n          nextPickerValue = [mergedStartPickerValue, mergedEndPickerValue][mergedActiveIndex ^ 1];\n        } else if (calendarValue[mergedActiveIndex]) {\n          // Current field has value\n          nextPickerValue = mergedActiveIndex === 0 ? calendarValue[0] : getEndDatePickerValue(calendarValue[0], calendarValue[1]);\n        } else if (calendarValue[mergedActiveIndex ^ 1]) {\n          // Current field has no value but another field has value\n          nextPickerValue = calendarValue[mergedActiveIndex ^ 1];\n        }\n\n        // Only sync when has value, this will sync in the `min-max` logic\n        if (nextPickerValue) {\n          // nextPickerValue < minDate\n          if (minDate && generateConfig.isAfter(minDate, nextPickerValue)) {\n            nextPickerValue = minDate;\n          }\n\n          // maxDate < nextPickerValue\n          var offsetPickerValue = multiplePanel ? offsetPanelDate(generateConfig, pickerMode, nextPickerValue, 1) : nextPickerValue;\n          if (maxDate && generateConfig.isAfter(offsetPickerValue, maxDate)) {\n            nextPickerValue = multiplePanel ? offsetPanelDate(generateConfig, pickerMode, maxDate, -1) : maxDate;\n          }\n          setCurrentPickerValue(nextPickerValue, 'reset');\n        }\n      }\n    }\n  }, [open, mergedActiveIndex, calendarValue[mergedActiveIndex]]);\n\n  // >>> Reset prevActiveIndex when panel closed\n  React.useEffect(function () {\n    if (open) {\n      prevActiveIndexRef.current = mergedActiveIndex;\n    } else {\n      prevActiveIndexRef.current = null;\n    }\n  }, [open, mergedActiveIndex]);\n\n  // >>> defaultPickerValue: Resync to `defaultPickerValue` for each panel focused\n  useLayoutEffect(function () {\n    if (open && defaultPickerValue) {\n      if (defaultPickerValue[mergedActiveIndex]) {\n        setCurrentPickerValue(defaultPickerValue[mergedActiveIndex], 'reset');\n      }\n    }\n  }, [open, mergedActiveIndex]);\n  return [currentPickerValue, setCurrentPickerValue];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,cAAc,QAAQ,SAAS;AACxC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,MAAM,QAAQ,sBAAsB;AACvD,OAAO,SAASC,eAAeA,CAACC,cAAc,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAE;EACpE,QAAQF,MAAM;IACZ,KAAK,MAAM;IACX,KAAK,MAAM;MACT,OAAOD,cAAc,CAACI,QAAQ,CAACF,IAAI,EAAEC,MAAM,CAAC;IAC9C,KAAK,OAAO;IACZ,KAAK,SAAS;MACZ,OAAOH,cAAc,CAACK,OAAO,CAACH,IAAI,EAAEC,MAAM,CAAC;IAC7C,KAAK,MAAM;MACT,OAAOH,cAAc,CAACK,OAAO,CAACH,IAAI,EAAEC,MAAM,GAAG,EAAE,CAAC;IAClD,KAAK,QAAQ;MACX,OAAOH,cAAc,CAACK,OAAO,CAACH,IAAI,EAAEC,MAAM,GAAG,GAAG,CAAC;IACnD;MACE,OAAOD,IAAI;EACf;AACF;AACA,IAAII,UAAU,GAAG,EAAE;AACnB,eAAe,SAASC,mBAAmBA,CAACP,cAAc,EAAEQ,MAAM,EAAEC,aAAa,EAAEC,KAAK,EAAEC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,aAAa,EAAE;EACtI,IAAIC,kBAAkB,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGV,UAAU;EACvG,IAAIa,WAAW,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGV,UAAU;EAChG,IAAIc,gBAAgB,GAAGJ,SAAS,CAACC,MAAM,GAAG,EAAE,IAAID,SAAS,CAAC,EAAE,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,EAAE,CAAC,GAAGV,UAAU;EACxG,IAAIe,mBAAmB,GAAGL,SAAS,CAACC,MAAM,GAAG,EAAE,GAAGD,SAAS,CAAC,EAAE,CAAC,GAAGE,SAAS;EAC3E,IAAII,OAAO,GAAGN,SAAS,CAACC,MAAM,GAAG,EAAE,GAAGD,SAAS,CAAC,EAAE,CAAC,GAAGE,SAAS;EAC/D,IAAIK,OAAO,GAAGP,SAAS,CAACC,MAAM,GAAG,EAAE,GAAGD,SAAS,CAAC,EAAE,CAAC,GAAGE,SAAS;EAC/D,IAAIM,YAAY,GAAGX,UAAU,KAAK,MAAM;;EAExC;EACA;EACA,IAAIY,iBAAiB,GAAGb,WAAW,IAAI,CAAC;;EAExC;EACA,IAAIc,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,KAAK,EAAE;IAChE,IAAIC,GAAG,GAAG5B,cAAc,CAAC6B,MAAM,CAAC,CAAC;IACjC,IAAIL,YAAY,EAAE;MAChBI,GAAG,GAAG/B,QAAQ,CAACG,cAAc,EAAE4B,GAAG,CAAC;IACrC;IACA,OAAOb,kBAAkB,CAACY,KAAK,CAAC,IAAIlB,aAAa,CAACkB,KAAK,CAAC,IAAIC,GAAG;EACjE,CAAC;;EAED;EACA,IAAIE,YAAY,GAAGrC,cAAc,CAAC0B,WAAW,EAAE,CAAC,CAAC;IAC/CY,gBAAgB,GAAGD,YAAY,CAAC,CAAC,CAAC;IAClCE,cAAc,GAAGF,YAAY,CAAC,CAAC,CAAC;;EAElC;EACA,IAAIG,eAAe,GAAGvC,cAAc,CAAC,YAAY;MAC7C,OAAOgC,qBAAqB,CAAC,CAAC,CAAC;IACjC,CAAC,EAAE;MACDQ,KAAK,EAAEH;IACT,CAAC,CAAC;IACFI,gBAAgB,GAAG1C,cAAc,CAACwC,eAAe,EAAE,CAAC,CAAC;IACrDG,sBAAsB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5CE,mBAAmB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC3C,IAAIG,gBAAgB,GAAG5C,cAAc,CAAC,YAAY;MAC9C,OAAOgC,qBAAqB,CAAC,CAAC,CAAC;IACjC,CAAC,EAAE;MACDQ,KAAK,EAAEF;IACT,CAAC,CAAC;IACFO,gBAAgB,GAAG9C,cAAc,CAAC6C,gBAAgB,EAAE,CAAC,CAAC;IACtDE,oBAAoB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC1CE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAEzC;EACA,IAAIG,kBAAkB,GAAG9C,KAAK,CAAC+C,OAAO,CAAC,YAAY;IACjD,IAAIC,OAAO,GAAG,CAACR,sBAAsB,EAAEI,oBAAoB,CAAC,CAACf,iBAAiB,CAAC;;IAE/E;IACA,OAAOD,YAAY,GAAGoB,OAAO,GAAG/C,QAAQ,CAACG,cAAc,EAAE4C,OAAO,EAAExB,gBAAgB,CAACK,iBAAiB,CAAC,CAAC;EACxG,CAAC,EAAE,CAACD,YAAY,EAAEY,sBAAsB,EAAEI,oBAAoB,EAAEf,iBAAiB,EAAEzB,cAAc,EAAEoB,gBAAgB,CAAC,CAAC;EACrH,IAAIyB,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,eAAe,EAAE;IAC1E,IAAIC,MAAM,GAAG/B,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO;IACxF,IAAIgC,OAAO,GAAG,CAACX,mBAAmB,EAAEI,iBAAiB,CAAC,CAAChB,iBAAiB,CAAC;IACzEuB,OAAO,CAACF,eAAe,CAAC;IACxB,IAAIG,KAAK,GAAG,CAACb,sBAAsB,EAAEI,oBAAoB,CAAC;IAC1DS,KAAK,CAACxB,iBAAiB,CAAC,GAAGqB,eAAe;IAC1C,IAAIzB,mBAAmB,KAAK,CAACvB,MAAM,CAACE,cAAc,EAAEQ,MAAM,EAAE4B,sBAAsB,EAAEa,KAAK,CAAC,CAAC,CAAC,EAAEpC,UAAU,CAAC,IAAI,CAACf,MAAM,CAACE,cAAc,EAAEQ,MAAM,EAAEgC,oBAAoB,EAAES,KAAK,CAAC,CAAC,CAAC,EAAEpC,UAAU,CAAC,CAAC,EAAE;MACzLQ,mBAAmB,CAAC4B,KAAK,EAAE;QACzBF,MAAM,EAAEA,MAAM;QACdG,KAAK,EAAEzB,iBAAiB,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO;QAChD0B,IAAI,EAAEzC;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI0C,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,SAAS,EAAEC,OAAO,EAAE;IAC7E,IAAIxC,aAAa,EAAE;MACjB;MACA,IAAIyC,YAAY,GAAG;QACjBrD,IAAI,EAAE,OAAO;QACbsD,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE;MACX,CAAC;MACD,IAAIP,IAAI,GAAGI,YAAY,CAAC1C,UAAU,CAAC;MACnC,IAAIsC,IAAI,IAAI,CAACrD,MAAM,CAACE,cAAc,EAAEQ,MAAM,EAAE6C,SAAS,EAAEC,OAAO,EAAEH,IAAI,CAAC,EAAE;QACrE,OAAOpD,eAAe,CAACC,cAAc,EAAEa,UAAU,EAAEyC,OAAO,EAAE,CAAC,CAAC,CAAC;MACjE;;MAEA;MACA,IAAIzC,UAAU,KAAK,MAAM,IAAIwC,SAAS,EAAE;QACtC,IAAIM,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC7D,cAAc,CAAC8D,OAAO,CAACT,SAAS,CAAC,GAAG,EAAE,CAAC;QAChE,IAAIU,OAAO,GAAGH,IAAI,CAACC,KAAK,CAAC7D,cAAc,CAAC8D,OAAO,CAACR,OAAO,CAAC,GAAG,EAAE,CAAC;QAC9D,IAAIK,OAAO,KAAKI,OAAO,EAAE;UACvB,OAAOhE,eAAe,CAACC,cAAc,EAAEa,UAAU,EAAEyC,OAAO,EAAE,CAAC,CAAC,CAAC;QACjE;MACF;IACF;IACA,OAAOA,OAAO;EAChB,CAAC;;EAED;EACA,IAAIU,kBAAkB,GAAGpE,KAAK,CAACqE,MAAM,CAAC,IAAI,CAAC;EAC3CtE,eAAe,CAAC,YAAY;IAC1B,IAAIgB,IAAI,EAAE;MACR,IAAI,CAACI,kBAAkB,CAACU,iBAAiB,CAAC,EAAE;QAC1C,IAAIqB,eAAe,GAAGtB,YAAY,GAAG,IAAI,GAAGxB,cAAc,CAAC6B,MAAM,CAAC,CAAC;;QAEnE;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;QAEQ,IAAImC,kBAAkB,CAACpB,OAAO,KAAK,IAAI,IAAIoB,kBAAkB,CAACpB,OAAO,KAAKnB,iBAAiB,EAAE;UAC3F;UACAqB,eAAe,GAAG,CAACV,sBAAsB,EAAEI,oBAAoB,CAAC,CAACf,iBAAiB,GAAG,CAAC,CAAC;QACzF,CAAC,MAAM,IAAIhB,aAAa,CAACgB,iBAAiB,CAAC,EAAE;UAC3C;UACAqB,eAAe,GAAGrB,iBAAiB,KAAK,CAAC,GAAGhB,aAAa,CAAC,CAAC,CAAC,GAAG2C,qBAAqB,CAAC3C,aAAa,CAAC,CAAC,CAAC,EAAEA,aAAa,CAAC,CAAC,CAAC,CAAC;QAC1H,CAAC,MAAM,IAAIA,aAAa,CAACgB,iBAAiB,GAAG,CAAC,CAAC,EAAE;UAC/C;UACAqB,eAAe,GAAGrC,aAAa,CAACgB,iBAAiB,GAAG,CAAC,CAAC;QACxD;;QAEA;QACA,IAAIqB,eAAe,EAAE;UACnB;UACA,IAAIxB,OAAO,IAAItB,cAAc,CAACkE,OAAO,CAAC5C,OAAO,EAAEwB,eAAe,CAAC,EAAE;YAC/DA,eAAe,GAAGxB,OAAO;UAC3B;;UAEA;UACA,IAAI6C,iBAAiB,GAAGrD,aAAa,GAAGf,eAAe,CAACC,cAAc,EAAEa,UAAU,EAAEiC,eAAe,EAAE,CAAC,CAAC,GAAGA,eAAe;UACzH,IAAIvB,OAAO,IAAIvB,cAAc,CAACkE,OAAO,CAACC,iBAAiB,EAAE5C,OAAO,CAAC,EAAE;YACjEuB,eAAe,GAAGhC,aAAa,GAAGf,eAAe,CAACC,cAAc,EAAEa,UAAU,EAAEU,OAAO,EAAE,CAAC,CAAC,CAAC,GAAGA,OAAO;UACtG;UACAsB,qBAAqB,CAACC,eAAe,EAAE,OAAO,CAAC;QACjD;MACF;IACF;EACF,CAAC,EAAE,CAACnC,IAAI,EAAEc,iBAAiB,EAAEhB,aAAa,CAACgB,iBAAiB,CAAC,CAAC,CAAC;;EAE/D;EACA7B,KAAK,CAACwE,SAAS,CAAC,YAAY;IAC1B,IAAIzD,IAAI,EAAE;MACRqD,kBAAkB,CAACpB,OAAO,GAAGnB,iBAAiB;IAChD,CAAC,MAAM;MACLuC,kBAAkB,CAACpB,OAAO,GAAG,IAAI;IACnC;EACF,CAAC,EAAE,CAACjC,IAAI,EAAEc,iBAAiB,CAAC,CAAC;;EAE7B;EACA9B,eAAe,CAAC,YAAY;IAC1B,IAAIgB,IAAI,IAAII,kBAAkB,EAAE;MAC9B,IAAIA,kBAAkB,CAACU,iBAAiB,CAAC,EAAE;QACzCoB,qBAAqB,CAAC9B,kBAAkB,CAACU,iBAAiB,CAAC,EAAE,OAAO,CAAC;MACvE;IACF;EACF,CAAC,EAAE,CAACd,IAAI,EAAEc,iBAAiB,CAAC,CAAC;EAC7B,OAAO,CAACiB,kBAAkB,EAAEG,qBAAqB,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}