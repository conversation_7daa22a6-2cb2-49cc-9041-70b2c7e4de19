{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"id\", \"mode\", \"prefixCls\", \"backfill\", \"fieldNames\", \"inputValue\", \"searchValue\", \"onSearch\", \"autoClearSearchValue\", \"onSelect\", \"onDeselect\", \"dropdownMatchSelectWidth\", \"filterOption\", \"filterSort\", \"optionFilterProp\", \"optionLabelProp\", \"options\", \"optionRender\", \"children\", \"defaultActiveFirstOption\", \"menuItemSelectedIcon\", \"virtual\", \"direction\", \"listHeight\", \"listItemHeight\", \"labelRender\", \"value\", \"defaultValue\", \"labelInValue\", \"onChange\", \"maxCount\"];\n/**\n * To match accessibility requirement, we always provide an input in the component.\n * Other element will not set `tabIndex` to avoid `onBlur` sequence problem.\n * For focused select, we set `aria-live=\"polite\"` to update the accessibility content.\n *\n * ref:\n * - keyboard: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/listbox_role#Keyboard_interactions\n *\n * New api:\n * - listHeight\n * - listItemHeight\n * - component\n *\n * Remove deprecated api:\n * - multiple\n * - tags\n * - combobox\n * - firstActiveValue\n * - dropdownMenuStyle\n * - openClassName (Not list in api)\n *\n * Update:\n * - `backfill` only support `combobox` mode\n * - `combobox` mode not support `labelInValue` since it's meaningless\n * - `getInputElement` only support `combobox` mode\n * - `onChange` return OptionData instead of ReactNode\n * - `filterOption` `onChange` `onSelect` accept OptionData instead of ReactNode\n * - `combobox` mode trigger `onChange` will get `undefined` if no `value` match in Option\n * - `combobox` mode not support `optionLabelProp`\n */\n\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport BaseSelect, { isMultiple } from \"./BaseSelect\";\nimport OptGroup from \"./OptGroup\";\nimport Option from \"./Option\";\nimport OptionList from \"./OptionList\";\nimport SelectContext from \"./SelectContext\";\nimport useCache from \"./hooks/useCache\";\nimport useFilterOptions from \"./hooks/useFilterOptions\";\nimport useId from \"./hooks/useId\";\nimport useOptions from \"./hooks/useOptions\";\nimport useRefFunc from \"./hooks/useRefFunc\";\nimport { hasValue, isComboNoValue, toArray } from \"./utils/commonUtil\";\nimport { fillFieldNames, flattenOptions, injectPropsWithOption } from \"./utils/valueUtil\";\nimport warningProps, { warningNullOptions } from \"./utils/warningPropsUtil\";\nvar OMIT_DOM_PROPS = ['inputValue'];\nfunction isRawValue(value) {\n  return !value || _typeof(value) !== 'object';\n}\nvar Select = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n    mode = props.mode,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-select' : _props$prefixCls,\n    backfill = props.backfill,\n    fieldNames = props.fieldNames,\n    inputValue = props.inputValue,\n    searchValue = props.searchValue,\n    onSearch = props.onSearch,\n    _props$autoClearSearc = props.autoClearSearchValue,\n    autoClearSearchValue = _props$autoClearSearc === void 0 ? true : _props$autoClearSearc,\n    onSelect = props.onSelect,\n    onDeselect = props.onDeselect,\n    _props$dropdownMatchS = props.dropdownMatchSelectWidth,\n    dropdownMatchSelectWidth = _props$dropdownMatchS === void 0 ? true : _props$dropdownMatchS,\n    filterOption = props.filterOption,\n    filterSort = props.filterSort,\n    optionFilterProp = props.optionFilterProp,\n    optionLabelProp = props.optionLabelProp,\n    options = props.options,\n    optionRender = props.optionRender,\n    children = props.children,\n    defaultActiveFirstOption = props.defaultActiveFirstOption,\n    menuItemSelectedIcon = props.menuItemSelectedIcon,\n    virtual = props.virtual,\n    direction = props.direction,\n    _props$listHeight = props.listHeight,\n    listHeight = _props$listHeight === void 0 ? 200 : _props$listHeight,\n    _props$listItemHeight = props.listItemHeight,\n    listItemHeight = _props$listItemHeight === void 0 ? 20 : _props$listItemHeight,\n    labelRender = props.labelRender,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    labelInValue = props.labelInValue,\n    onChange = props.onChange,\n    maxCount = props.maxCount,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedId = useId(id);\n  var multiple = isMultiple(mode);\n  var childrenAsData = !!(!options && children);\n  var mergedFilterOption = React.useMemo(function () {\n    if (filterOption === undefined && mode === 'combobox') {\n      return false;\n    }\n    return filterOption;\n  }, [filterOption, mode]);\n\n  // ========================= FieldNames =========================\n  var mergedFieldNames = React.useMemo(function () {\n    return fillFieldNames(fieldNames, childrenAsData);\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [\n  // We stringify fieldNames to avoid unnecessary re-renders.\n  JSON.stringify(fieldNames), childrenAsData]\n  /* eslint-enable react-hooks/exhaustive-deps */);\n\n  // =========================== Search ===========================\n  var _useMergedState = useMergedState('', {\n      value: searchValue !== undefined ? searchValue : inputValue,\n      postState: function postState(search) {\n        return search || '';\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedSearchValue = _useMergedState2[0],\n    setSearchValue = _useMergedState2[1];\n\n  // =========================== Option ===========================\n  var parsedOptions = useOptions(options, children, mergedFieldNames, optionFilterProp, optionLabelProp);\n  var valueOptions = parsedOptions.valueOptions,\n    labelOptions = parsedOptions.labelOptions,\n    mergedOptions = parsedOptions.options;\n\n  // ========================= Wrap Value =========================\n  var convert2LabelValues = React.useCallback(function (draftValues) {\n    // Convert to array\n    var valueList = toArray(draftValues);\n\n    // Convert to labelInValue type\n    return valueList.map(function (val) {\n      var rawValue;\n      var rawLabel;\n      var rawKey;\n      var rawDisabled;\n      var rawTitle;\n\n      // Fill label & value\n      if (isRawValue(val)) {\n        rawValue = val;\n      } else {\n        var _val$value;\n        rawKey = val.key;\n        rawLabel = val.label;\n        rawValue = (_val$value = val.value) !== null && _val$value !== void 0 ? _val$value : rawKey;\n      }\n      var option = valueOptions.get(rawValue);\n      if (option) {\n        var _option$key;\n        // Fill missing props\n        if (rawLabel === undefined) rawLabel = option === null || option === void 0 ? void 0 : option[optionLabelProp || mergedFieldNames.label];\n        if (rawKey === undefined) rawKey = (_option$key = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key !== void 0 ? _option$key : rawValue;\n        rawDisabled = option === null || option === void 0 ? void 0 : option.disabled;\n        rawTitle = option === null || option === void 0 ? void 0 : option.title;\n\n        // Warning if label not same as provided\n        if (process.env.NODE_ENV !== 'production' && !optionLabelProp) {\n          var optionLabel = option === null || option === void 0 ? void 0 : option[mergedFieldNames.label];\n          if (optionLabel !== undefined && ! /*#__PURE__*/React.isValidElement(optionLabel) && ! /*#__PURE__*/React.isValidElement(rawLabel) && optionLabel !== rawLabel) {\n            warning(false, '`label` of `value` is not same as `label` in Select options.');\n          }\n        }\n      }\n      return {\n        label: rawLabel,\n        value: rawValue,\n        key: rawKey,\n        disabled: rawDisabled,\n        title: rawTitle\n      };\n    });\n  }, [mergedFieldNames, optionLabelProp, valueOptions]);\n\n  // =========================== Values ===========================\n  var _useMergedState3 = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    internalValue = _useMergedState4[0],\n    setInternalValue = _useMergedState4[1];\n\n  // Merged value with LabelValueType\n  var rawLabeledValues = React.useMemo(function () {\n    var _values$;\n    var newInternalValue = multiple && internalValue === null ? [] : internalValue;\n    var values = convert2LabelValues(newInternalValue);\n\n    // combobox no need save value when it's no value (exclude value equal 0)\n    if (mode === 'combobox' && isComboNoValue((_values$ = values[0]) === null || _values$ === void 0 ? void 0 : _values$.value)) {\n      return [];\n    }\n    return values;\n  }, [internalValue, convert2LabelValues, mode, multiple]);\n\n  // Fill label with cache to avoid option remove\n  var _useCache = useCache(rawLabeledValues, valueOptions),\n    _useCache2 = _slicedToArray(_useCache, 2),\n    mergedValues = _useCache2[0],\n    getMixedOption = _useCache2[1];\n  var displayValues = React.useMemo(function () {\n    // `null` need show as placeholder instead\n    // https://github.com/ant-design/ant-design/issues/25057\n    if (!mode && mergedValues.length === 1) {\n      var firstValue = mergedValues[0];\n      if (firstValue.value === null && (firstValue.label === null || firstValue.label === undefined)) {\n        return [];\n      }\n    }\n    return mergedValues.map(function (item) {\n      var _ref;\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: (_ref = typeof labelRender === 'function' ? labelRender(item) : item.label) !== null && _ref !== void 0 ? _ref : item.value\n      });\n    });\n  }, [mode, mergedValues, labelRender]);\n\n  /** Convert `displayValues` to raw value type set */\n  var rawValues = React.useMemo(function () {\n    return new Set(mergedValues.map(function (val) {\n      return val.value;\n    }));\n  }, [mergedValues]);\n  React.useEffect(function () {\n    if (mode === 'combobox') {\n      var _mergedValues$;\n      var strValue = (_mergedValues$ = mergedValues[0]) === null || _mergedValues$ === void 0 ? void 0 : _mergedValues$.value;\n      setSearchValue(hasValue(strValue) ? String(strValue) : '');\n    }\n  }, [mergedValues]);\n\n  // ======================= Display Option =======================\n  // Create a placeholder item if not exist in `options`\n  var createTagOption = useRefFunc(function (val, label) {\n    var mergedLabel = label !== null && label !== void 0 ? label : val;\n    return _defineProperty(_defineProperty({}, mergedFieldNames.value, val), mergedFieldNames.label, mergedLabel);\n  });\n\n  // Fill tag as option if mode is `tags`\n  var filledTagOptions = React.useMemo(function () {\n    if (mode !== 'tags') {\n      return mergedOptions;\n    }\n\n    // >>> Tag mode\n    var cloneOptions = _toConsumableArray(mergedOptions);\n\n    // Check if value exist in options (include new patch item)\n    var existOptions = function existOptions(val) {\n      return valueOptions.has(val);\n    };\n\n    // Fill current value as option\n    _toConsumableArray(mergedValues).sort(function (a, b) {\n      return a.value < b.value ? -1 : 1;\n    }).forEach(function (item) {\n      var val = item.value;\n      if (!existOptions(val)) {\n        cloneOptions.push(createTagOption(val, item.label));\n      }\n    });\n    return cloneOptions;\n  }, [createTagOption, mergedOptions, valueOptions, mergedValues, mode]);\n  var filteredOptions = useFilterOptions(filledTagOptions, mergedFieldNames, mergedSearchValue, mergedFilterOption, optionFilterProp);\n\n  // Fill options with search value if needed\n  var filledSearchOptions = React.useMemo(function () {\n    if (mode !== 'tags' || !mergedSearchValue || filteredOptions.some(function (item) {\n      return item[optionFilterProp || 'value'] === mergedSearchValue;\n    })) {\n      return filteredOptions;\n    }\n    // ignore when search value equal select input value\n    if (filteredOptions.some(function (item) {\n      return item[mergedFieldNames.value] === mergedSearchValue;\n    })) {\n      return filteredOptions;\n    }\n    // Fill search value as option\n    return [createTagOption(mergedSearchValue)].concat(_toConsumableArray(filteredOptions));\n  }, [createTagOption, optionFilterProp, mode, filteredOptions, mergedSearchValue, mergedFieldNames]);\n  var sorter = function sorter(inputOptions) {\n    var sortedOptions = _toConsumableArray(inputOptions).sort(function (a, b) {\n      return filterSort(a, b, {\n        searchValue: mergedSearchValue\n      });\n    });\n    return sortedOptions.map(function (item) {\n      if (Array.isArray(item.options)) {\n        return _objectSpread(_objectSpread({}, item), {}, {\n          options: item.options.length > 0 ? sorter(item.options) : item.options\n        });\n      }\n      return item;\n    });\n  };\n  var orderedFilteredOptions = React.useMemo(function () {\n    if (!filterSort) {\n      return filledSearchOptions;\n    }\n    return sorter(filledSearchOptions);\n  }, [filledSearchOptions, filterSort, mergedSearchValue]);\n  var displayOptions = React.useMemo(function () {\n    return flattenOptions(orderedFilteredOptions, {\n      fieldNames: mergedFieldNames,\n      childrenAsData: childrenAsData\n    });\n  }, [orderedFilteredOptions, mergedFieldNames, childrenAsData]);\n\n  // =========================== Change ===========================\n  var triggerChange = function triggerChange(values) {\n    var labeledValues = convert2LabelValues(values);\n    setInternalValue(labeledValues);\n    if (onChange && (\n    // Trigger event only when value changed\n    labeledValues.length !== mergedValues.length || labeledValues.some(function (newVal, index) {\n      var _mergedValues$index;\n      return ((_mergedValues$index = mergedValues[index]) === null || _mergedValues$index === void 0 ? void 0 : _mergedValues$index.value) !== (newVal === null || newVal === void 0 ? void 0 : newVal.value);\n    }))) {\n      var returnValues = labelInValue ? labeledValues : labeledValues.map(function (v) {\n        return v.value;\n      });\n      var returnOptions = labeledValues.map(function (v) {\n        return injectPropsWithOption(getMixedOption(v.value));\n      });\n      onChange(\n      // Value\n      multiple ? returnValues : returnValues[0],\n      // Option\n      multiple ? returnOptions : returnOptions[0]);\n    }\n  };\n\n  // ======================= Accessibility ========================\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeValue = _React$useState2[0],\n    setActiveValue = _React$useState2[1];\n  var _React$useState3 = React.useState(0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    accessibilityIndex = _React$useState4[0],\n    setAccessibilityIndex = _React$useState4[1];\n  var mergedDefaultActiveFirstOption = defaultActiveFirstOption !== undefined ? defaultActiveFirstOption : mode !== 'combobox';\n  var onActiveValue = React.useCallback(function (active, index) {\n    var _ref3 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n      _ref3$source = _ref3.source,\n      source = _ref3$source === void 0 ? 'keyboard' : _ref3$source;\n    setAccessibilityIndex(index);\n    if (backfill && mode === 'combobox' && active !== null && source === 'keyboard') {\n      setActiveValue(String(active));\n    }\n  }, [backfill, mode]);\n\n  // ========================= OptionList =========================\n  var triggerSelect = function triggerSelect(val, selected, type) {\n    var getSelectEnt = function getSelectEnt() {\n      var _option$key2;\n      var option = getMixedOption(val);\n      return [labelInValue ? {\n        label: option === null || option === void 0 ? void 0 : option[mergedFieldNames.label],\n        value: val,\n        key: (_option$key2 = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key2 !== void 0 ? _option$key2 : val\n      } : val, injectPropsWithOption(option)];\n    };\n    if (selected && onSelect) {\n      var _getSelectEnt = getSelectEnt(),\n        _getSelectEnt2 = _slicedToArray(_getSelectEnt, 2),\n        wrappedValue = _getSelectEnt2[0],\n        _option = _getSelectEnt2[1];\n      onSelect(wrappedValue, _option);\n    } else if (!selected && onDeselect && type !== 'clear') {\n      var _getSelectEnt3 = getSelectEnt(),\n        _getSelectEnt4 = _slicedToArray(_getSelectEnt3, 2),\n        _wrappedValue = _getSelectEnt4[0],\n        _option2 = _getSelectEnt4[1];\n      onDeselect(_wrappedValue, _option2);\n    }\n  };\n\n  // Used for OptionList selection\n  var onInternalSelect = useRefFunc(function (val, info) {\n    var cloneValues;\n\n    // Single mode always trigger select only with option list\n    var mergedSelect = multiple ? info.selected : true;\n    if (mergedSelect) {\n      cloneValues = multiple ? [].concat(_toConsumableArray(mergedValues), [val]) : [val];\n    } else {\n      cloneValues = mergedValues.filter(function (v) {\n        return v.value !== val;\n      });\n    }\n    triggerChange(cloneValues);\n    triggerSelect(val, mergedSelect);\n\n    // Clean search value if single or configured\n    if (mode === 'combobox') {\n      // setSearchValue(String(val));\n      setActiveValue('');\n    } else if (!isMultiple || autoClearSearchValue) {\n      setSearchValue('');\n      setActiveValue('');\n    }\n  });\n\n  // ======================= Display Change =======================\n  // BaseSelect display values change\n  var onDisplayValuesChange = function onDisplayValuesChange(nextValues, info) {\n    triggerChange(nextValues);\n    var type = info.type,\n      values = info.values;\n    if (type === 'remove' || type === 'clear') {\n      values.forEach(function (item) {\n        triggerSelect(item.value, false, type);\n      });\n    }\n  };\n\n  // =========================== Search ===========================\n  var onInternalSearch = function onInternalSearch(searchText, info) {\n    setSearchValue(searchText);\n    setActiveValue(null);\n\n    // [Submit] Tag mode should flush input\n    if (info.source === 'submit') {\n      var formatted = (searchText || '').trim();\n      // prevent empty tags from appearing when you click the Enter button\n      if (formatted) {\n        var newRawValues = Array.from(new Set([].concat(_toConsumableArray(rawValues), [formatted])));\n        triggerChange(newRawValues);\n        triggerSelect(formatted, true);\n        setSearchValue('');\n      }\n      return;\n    }\n    if (info.source !== 'blur') {\n      if (mode === 'combobox') {\n        triggerChange(searchText);\n      }\n      onSearch === null || onSearch === void 0 || onSearch(searchText);\n    }\n  };\n  var onInternalSearchSplit = function onInternalSearchSplit(words) {\n    var patchValues = words;\n    if (mode !== 'tags') {\n      patchValues = words.map(function (word) {\n        var opt = labelOptions.get(word);\n        return opt === null || opt === void 0 ? void 0 : opt.value;\n      }).filter(function (val) {\n        return val !== undefined;\n      });\n    }\n    var newRawValues = Array.from(new Set([].concat(_toConsumableArray(rawValues), _toConsumableArray(patchValues))));\n    triggerChange(newRawValues);\n    newRawValues.forEach(function (newRawValue) {\n      triggerSelect(newRawValue, true);\n    });\n  };\n\n  // ========================== Context ===========================\n  var selectContext = React.useMemo(function () {\n    var realVirtual = virtual !== false && dropdownMatchSelectWidth !== false;\n    return _objectSpread(_objectSpread({}, parsedOptions), {}, {\n      flattenOptions: displayOptions,\n      onActiveValue: onActiveValue,\n      defaultActiveFirstOption: mergedDefaultActiveFirstOption,\n      onSelect: onInternalSelect,\n      menuItemSelectedIcon: menuItemSelectedIcon,\n      rawValues: rawValues,\n      fieldNames: mergedFieldNames,\n      virtual: realVirtual,\n      direction: direction,\n      listHeight: listHeight,\n      listItemHeight: listItemHeight,\n      childrenAsData: childrenAsData,\n      maxCount: maxCount,\n      optionRender: optionRender\n    });\n  }, [maxCount, parsedOptions, displayOptions, onActiveValue, mergedDefaultActiveFirstOption, onInternalSelect, menuItemSelectedIcon, rawValues, mergedFieldNames, virtual, dropdownMatchSelectWidth, direction, listHeight, listItemHeight, childrenAsData, optionRender]);\n\n  // ========================== Warning ===========================\n  if (process.env.NODE_ENV !== 'production') {\n    warningProps(props);\n    warningNullOptions(mergedOptions, mergedFieldNames);\n  }\n\n  // ==============================================================\n  // ==                          Render                          ==\n  // ==============================================================\n  return /*#__PURE__*/React.createElement(SelectContext.Provider, {\n    value: selectContext\n  }, /*#__PURE__*/React.createElement(BaseSelect, _extends({}, restProps, {\n    // >>> MISC\n    id: mergedId,\n    prefixCls: prefixCls,\n    ref: ref,\n    omitDomProps: OMIT_DOM_PROPS,\n    mode: mode\n    // >>> Values\n    ,\n\n    displayValues: displayValues,\n    onDisplayValuesChange: onDisplayValuesChange\n    // >>> Trigger\n    ,\n\n    direction: direction\n    // >>> Search\n    ,\n\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch,\n    autoClearSearchValue: autoClearSearchValue,\n    onSearchSplit: onInternalSearchSplit,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n    // >>> OptionList\n    ,\n\n    OptionList: OptionList,\n    emptyOptions: !displayOptions.length\n    // >>> Accessibility\n    ,\n\n    activeValue: activeValue,\n    activeDescendantId: \"\".concat(mergedId, \"_list_\").concat(accessibilityIndex)\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Select.displayName = 'Select';\n}\nvar TypedSelect = Select;\nTypedSelect.Option = Option;\nTypedSelect.OptGroup = OptGroup;\nexport default TypedSelect;", "map": {"version": 3, "names": ["_extends", "_toConsumableArray", "_defineProperty", "_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_typeof", "_excluded", "useMergedState", "warning", "React", "BaseSelect", "isMultiple", "OptGroup", "Option", "OptionList", "SelectContext", "useCache", "useFilterOptions", "useId", "useOptions", "useRefFunc", "hasValue", "isComboNoValue", "toArray", "fillFieldNames", "flattenOptions", "injectPropsWithOption", "warningProps", "warningNullOptions", "OMIT_DOM_PROPS", "isRawValue", "value", "Select", "forwardRef", "props", "ref", "id", "mode", "_props$prefixCls", "prefixCls", "backfill", "fieldNames", "inputValue", "searchValue", "onSearch", "_props$autoClearSearc", "autoClearSearchValue", "onSelect", "onDeselect", "_props$dropdownMatchS", "dropdownMatchSelectWidth", "filterOption", "filterSort", "optionFilterProp", "optionLabelProp", "options", "optionRender", "children", "defaultActiveFirstOption", "menuItemSelectedIcon", "virtual", "direction", "_props$listHeight", "listHeight", "_props$listItemHeight", "listItemHeight", "labelRender", "defaultValue", "labelInValue", "onChange", "maxCount", "restProps", "mergedId", "multiple", "childrenAsData", "mergedFilterOption", "useMemo", "undefined", "mergedFieldNames", "JSON", "stringify", "_useMergedState", "postState", "search", "_useMergedState2", "mergedSearchValue", "setSearchValue", "parsedOptions", "valueOptions", "labelOptions", "mergedOptions", "convert2LabelValues", "useCallback", "draftV<PERSON><PERSON>", "valueList", "map", "val", "rawValue", "rawLabel", "<PERSON><PERSON><PERSON>", "rawDisabled", "rawTitle", "_val$value", "key", "label", "option", "get", "_option$key", "disabled", "title", "process", "env", "NODE_ENV", "optionLabel", "isValidElement", "_useMergedState3", "_useMergedState4", "internalValue", "setInternalValue", "rawLabeledValues", "_values$", "newInternalValue", "values", "_useCache", "_useCache2", "mergedValues", "getMixedOption", "displayValues", "length", "firstValue", "item", "_ref", "rawValues", "Set", "useEffect", "_mergedValues$", "strValue", "String", "createTagOption", "mergedLabel", "filledTagOptions", "cloneOptions", "existOptions", "has", "sort", "a", "b", "for<PERSON>ach", "push", "filteredOptions", "filledSearchOptions", "some", "concat", "sorter", "inputOptions", "sortedOptions", "Array", "isArray", "orderedFilteredOptions", "displayOptions", "trigger<PERSON>hange", "labeledV<PERSON>ues", "newVal", "index", "_mergedValues$index", "returnV<PERSON>ues", "v", "returnOptions", "_React$useState", "useState", "_React$useState2", "activeValue", "setActiveValue", "_React$useState3", "_React$useState4", "accessibilityIndex", "setAccessibilityIndex", "mergedDefaultActiveFirstOption", "onActiveValue", "active", "_ref3", "arguments", "_ref3$source", "source", "triggerSelect", "selected", "type", "getSelectEnt", "_option$key2", "_getSelectEnt", "_getSelectEnt2", "wrappedValue", "_option", "_getSelectEnt3", "_getSelectEnt4", "_wrappedValue", "_option2", "onInternalSelect", "info", "clone<PERSON><PERSON>ues", "mergedSelect", "filter", "onDisplayValuesChange", "nextV<PERSON>ues", "onInternalSearch", "searchText", "formatted", "trim", "newRawValues", "from", "onInternalSearchSplit", "words", "patchValues", "word", "opt", "newRawValue", "selectContext", "realVirtual", "createElement", "Provider", "omitDomProps", "onSearchSplit", "emptyOptions", "activeDescendantId", "displayName", "TypedSelect"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-select/es/Select.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"id\", \"mode\", \"prefixCls\", \"backfill\", \"fieldNames\", \"inputValue\", \"searchValue\", \"onSearch\", \"autoClearSearchValue\", \"onSelect\", \"onDeselect\", \"dropdownMatchSelectWidth\", \"filterOption\", \"filterSort\", \"optionFilterProp\", \"optionLabelProp\", \"options\", \"optionRender\", \"children\", \"defaultActiveFirstOption\", \"menuItemSelectedIcon\", \"virtual\", \"direction\", \"listHeight\", \"listItemHeight\", \"labelRender\", \"value\", \"defaultValue\", \"labelInValue\", \"onChange\", \"maxCount\"];\n/**\n * To match accessibility requirement, we always provide an input in the component.\n * Other element will not set `tabIndex` to avoid `onBlur` sequence problem.\n * For focused select, we set `aria-live=\"polite\"` to update the accessibility content.\n *\n * ref:\n * - keyboard: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/listbox_role#Keyboard_interactions\n *\n * New api:\n * - listHeight\n * - listItemHeight\n * - component\n *\n * Remove deprecated api:\n * - multiple\n * - tags\n * - combobox\n * - firstActiveValue\n * - dropdownMenuStyle\n * - openClassName (Not list in api)\n *\n * Update:\n * - `backfill` only support `combobox` mode\n * - `combobox` mode not support `labelInValue` since it's meaningless\n * - `getInputElement` only support `combobox` mode\n * - `onChange` return OptionData instead of ReactNode\n * - `filterOption` `onChange` `onSelect` accept OptionData instead of ReactNode\n * - `combobox` mode trigger `onChange` will get `undefined` if no `value` match in Option\n * - `combobox` mode not support `optionLabelProp`\n */\n\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport BaseSelect, { isMultiple } from \"./BaseSelect\";\nimport OptGroup from \"./OptGroup\";\nimport Option from \"./Option\";\nimport OptionList from \"./OptionList\";\nimport SelectContext from \"./SelectContext\";\nimport useCache from \"./hooks/useCache\";\nimport useFilterOptions from \"./hooks/useFilterOptions\";\nimport useId from \"./hooks/useId\";\nimport useOptions from \"./hooks/useOptions\";\nimport useRefFunc from \"./hooks/useRefFunc\";\nimport { hasValue, isComboNoValue, toArray } from \"./utils/commonUtil\";\nimport { fillFieldNames, flattenOptions, injectPropsWithOption } from \"./utils/valueUtil\";\nimport warningProps, { warningNullOptions } from \"./utils/warningPropsUtil\";\nvar OMIT_DOM_PROPS = ['inputValue'];\nfunction isRawValue(value) {\n  return !value || _typeof(value) !== 'object';\n}\nvar Select = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n    mode = props.mode,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-select' : _props$prefixCls,\n    backfill = props.backfill,\n    fieldNames = props.fieldNames,\n    inputValue = props.inputValue,\n    searchValue = props.searchValue,\n    onSearch = props.onSearch,\n    _props$autoClearSearc = props.autoClearSearchValue,\n    autoClearSearchValue = _props$autoClearSearc === void 0 ? true : _props$autoClearSearc,\n    onSelect = props.onSelect,\n    onDeselect = props.onDeselect,\n    _props$dropdownMatchS = props.dropdownMatchSelectWidth,\n    dropdownMatchSelectWidth = _props$dropdownMatchS === void 0 ? true : _props$dropdownMatchS,\n    filterOption = props.filterOption,\n    filterSort = props.filterSort,\n    optionFilterProp = props.optionFilterProp,\n    optionLabelProp = props.optionLabelProp,\n    options = props.options,\n    optionRender = props.optionRender,\n    children = props.children,\n    defaultActiveFirstOption = props.defaultActiveFirstOption,\n    menuItemSelectedIcon = props.menuItemSelectedIcon,\n    virtual = props.virtual,\n    direction = props.direction,\n    _props$listHeight = props.listHeight,\n    listHeight = _props$listHeight === void 0 ? 200 : _props$listHeight,\n    _props$listItemHeight = props.listItemHeight,\n    listItemHeight = _props$listItemHeight === void 0 ? 20 : _props$listItemHeight,\n    labelRender = props.labelRender,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    labelInValue = props.labelInValue,\n    onChange = props.onChange,\n    maxCount = props.maxCount,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedId = useId(id);\n  var multiple = isMultiple(mode);\n  var childrenAsData = !!(!options && children);\n  var mergedFilterOption = React.useMemo(function () {\n    if (filterOption === undefined && mode === 'combobox') {\n      return false;\n    }\n    return filterOption;\n  }, [filterOption, mode]);\n\n  // ========================= FieldNames =========================\n  var mergedFieldNames = React.useMemo(function () {\n    return fillFieldNames(fieldNames, childrenAsData);\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [\n  // We stringify fieldNames to avoid unnecessary re-renders.\n  JSON.stringify(fieldNames), childrenAsData]\n  /* eslint-enable react-hooks/exhaustive-deps */);\n\n  // =========================== Search ===========================\n  var _useMergedState = useMergedState('', {\n      value: searchValue !== undefined ? searchValue : inputValue,\n      postState: function postState(search) {\n        return search || '';\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedSearchValue = _useMergedState2[0],\n    setSearchValue = _useMergedState2[1];\n\n  // =========================== Option ===========================\n  var parsedOptions = useOptions(options, children, mergedFieldNames, optionFilterProp, optionLabelProp);\n  var valueOptions = parsedOptions.valueOptions,\n    labelOptions = parsedOptions.labelOptions,\n    mergedOptions = parsedOptions.options;\n\n  // ========================= Wrap Value =========================\n  var convert2LabelValues = React.useCallback(function (draftValues) {\n    // Convert to array\n    var valueList = toArray(draftValues);\n\n    // Convert to labelInValue type\n    return valueList.map(function (val) {\n      var rawValue;\n      var rawLabel;\n      var rawKey;\n      var rawDisabled;\n      var rawTitle;\n\n      // Fill label & value\n      if (isRawValue(val)) {\n        rawValue = val;\n      } else {\n        var _val$value;\n        rawKey = val.key;\n        rawLabel = val.label;\n        rawValue = (_val$value = val.value) !== null && _val$value !== void 0 ? _val$value : rawKey;\n      }\n      var option = valueOptions.get(rawValue);\n      if (option) {\n        var _option$key;\n        // Fill missing props\n        if (rawLabel === undefined) rawLabel = option === null || option === void 0 ? void 0 : option[optionLabelProp || mergedFieldNames.label];\n        if (rawKey === undefined) rawKey = (_option$key = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key !== void 0 ? _option$key : rawValue;\n        rawDisabled = option === null || option === void 0 ? void 0 : option.disabled;\n        rawTitle = option === null || option === void 0 ? void 0 : option.title;\n\n        // Warning if label not same as provided\n        if (process.env.NODE_ENV !== 'production' && !optionLabelProp) {\n          var optionLabel = option === null || option === void 0 ? void 0 : option[mergedFieldNames.label];\n          if (optionLabel !== undefined && ! /*#__PURE__*/React.isValidElement(optionLabel) && ! /*#__PURE__*/React.isValidElement(rawLabel) && optionLabel !== rawLabel) {\n            warning(false, '`label` of `value` is not same as `label` in Select options.');\n          }\n        }\n      }\n      return {\n        label: rawLabel,\n        value: rawValue,\n        key: rawKey,\n        disabled: rawDisabled,\n        title: rawTitle\n      };\n    });\n  }, [mergedFieldNames, optionLabelProp, valueOptions]);\n\n  // =========================== Values ===========================\n  var _useMergedState3 = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    internalValue = _useMergedState4[0],\n    setInternalValue = _useMergedState4[1];\n\n  // Merged value with LabelValueType\n  var rawLabeledValues = React.useMemo(function () {\n    var _values$;\n    var newInternalValue = multiple && internalValue === null ? [] : internalValue;\n    var values = convert2LabelValues(newInternalValue);\n\n    // combobox no need save value when it's no value (exclude value equal 0)\n    if (mode === 'combobox' && isComboNoValue((_values$ = values[0]) === null || _values$ === void 0 ? void 0 : _values$.value)) {\n      return [];\n    }\n    return values;\n  }, [internalValue, convert2LabelValues, mode, multiple]);\n\n  // Fill label with cache to avoid option remove\n  var _useCache = useCache(rawLabeledValues, valueOptions),\n    _useCache2 = _slicedToArray(_useCache, 2),\n    mergedValues = _useCache2[0],\n    getMixedOption = _useCache2[1];\n  var displayValues = React.useMemo(function () {\n    // `null` need show as placeholder instead\n    // https://github.com/ant-design/ant-design/issues/25057\n    if (!mode && mergedValues.length === 1) {\n      var firstValue = mergedValues[0];\n      if (firstValue.value === null && (firstValue.label === null || firstValue.label === undefined)) {\n        return [];\n      }\n    }\n    return mergedValues.map(function (item) {\n      var _ref;\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: (_ref = typeof labelRender === 'function' ? labelRender(item) : item.label) !== null && _ref !== void 0 ? _ref : item.value\n      });\n    });\n  }, [mode, mergedValues, labelRender]);\n\n  /** Convert `displayValues` to raw value type set */\n  var rawValues = React.useMemo(function () {\n    return new Set(mergedValues.map(function (val) {\n      return val.value;\n    }));\n  }, [mergedValues]);\n  React.useEffect(function () {\n    if (mode === 'combobox') {\n      var _mergedValues$;\n      var strValue = (_mergedValues$ = mergedValues[0]) === null || _mergedValues$ === void 0 ? void 0 : _mergedValues$.value;\n      setSearchValue(hasValue(strValue) ? String(strValue) : '');\n    }\n  }, [mergedValues]);\n\n  // ======================= Display Option =======================\n  // Create a placeholder item if not exist in `options`\n  var createTagOption = useRefFunc(function (val, label) {\n    var mergedLabel = label !== null && label !== void 0 ? label : val;\n    return _defineProperty(_defineProperty({}, mergedFieldNames.value, val), mergedFieldNames.label, mergedLabel);\n  });\n\n  // Fill tag as option if mode is `tags`\n  var filledTagOptions = React.useMemo(function () {\n    if (mode !== 'tags') {\n      return mergedOptions;\n    }\n\n    // >>> Tag mode\n    var cloneOptions = _toConsumableArray(mergedOptions);\n\n    // Check if value exist in options (include new patch item)\n    var existOptions = function existOptions(val) {\n      return valueOptions.has(val);\n    };\n\n    // Fill current value as option\n    _toConsumableArray(mergedValues).sort(function (a, b) {\n      return a.value < b.value ? -1 : 1;\n    }).forEach(function (item) {\n      var val = item.value;\n      if (!existOptions(val)) {\n        cloneOptions.push(createTagOption(val, item.label));\n      }\n    });\n    return cloneOptions;\n  }, [createTagOption, mergedOptions, valueOptions, mergedValues, mode]);\n  var filteredOptions = useFilterOptions(filledTagOptions, mergedFieldNames, mergedSearchValue, mergedFilterOption, optionFilterProp);\n\n  // Fill options with search value if needed\n  var filledSearchOptions = React.useMemo(function () {\n    if (mode !== 'tags' || !mergedSearchValue || filteredOptions.some(function (item) {\n      return item[optionFilterProp || 'value'] === mergedSearchValue;\n    })) {\n      return filteredOptions;\n    }\n    // ignore when search value equal select input value\n    if (filteredOptions.some(function (item) {\n      return item[mergedFieldNames.value] === mergedSearchValue;\n    })) {\n      return filteredOptions;\n    }\n    // Fill search value as option\n    return [createTagOption(mergedSearchValue)].concat(_toConsumableArray(filteredOptions));\n  }, [createTagOption, optionFilterProp, mode, filteredOptions, mergedSearchValue, mergedFieldNames]);\n  var sorter = function sorter(inputOptions) {\n    var sortedOptions = _toConsumableArray(inputOptions).sort(function (a, b) {\n      return filterSort(a, b, {\n        searchValue: mergedSearchValue\n      });\n    });\n    return sortedOptions.map(function (item) {\n      if (Array.isArray(item.options)) {\n        return _objectSpread(_objectSpread({}, item), {}, {\n          options: item.options.length > 0 ? sorter(item.options) : item.options\n        });\n      }\n      return item;\n    });\n  };\n  var orderedFilteredOptions = React.useMemo(function () {\n    if (!filterSort) {\n      return filledSearchOptions;\n    }\n    return sorter(filledSearchOptions);\n  }, [filledSearchOptions, filterSort, mergedSearchValue]);\n  var displayOptions = React.useMemo(function () {\n    return flattenOptions(orderedFilteredOptions, {\n      fieldNames: mergedFieldNames,\n      childrenAsData: childrenAsData\n    });\n  }, [orderedFilteredOptions, mergedFieldNames, childrenAsData]);\n\n  // =========================== Change ===========================\n  var triggerChange = function triggerChange(values) {\n    var labeledValues = convert2LabelValues(values);\n    setInternalValue(labeledValues);\n    if (onChange && (\n    // Trigger event only when value changed\n    labeledValues.length !== mergedValues.length || labeledValues.some(function (newVal, index) {\n      var _mergedValues$index;\n      return ((_mergedValues$index = mergedValues[index]) === null || _mergedValues$index === void 0 ? void 0 : _mergedValues$index.value) !== (newVal === null || newVal === void 0 ? void 0 : newVal.value);\n    }))) {\n      var returnValues = labelInValue ? labeledValues : labeledValues.map(function (v) {\n        return v.value;\n      });\n      var returnOptions = labeledValues.map(function (v) {\n        return injectPropsWithOption(getMixedOption(v.value));\n      });\n      onChange(\n      // Value\n      multiple ? returnValues : returnValues[0],\n      // Option\n      multiple ? returnOptions : returnOptions[0]);\n    }\n  };\n\n  // ======================= Accessibility ========================\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeValue = _React$useState2[0],\n    setActiveValue = _React$useState2[1];\n  var _React$useState3 = React.useState(0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    accessibilityIndex = _React$useState4[0],\n    setAccessibilityIndex = _React$useState4[1];\n  var mergedDefaultActiveFirstOption = defaultActiveFirstOption !== undefined ? defaultActiveFirstOption : mode !== 'combobox';\n  var onActiveValue = React.useCallback(function (active, index) {\n    var _ref3 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n      _ref3$source = _ref3.source,\n      source = _ref3$source === void 0 ? 'keyboard' : _ref3$source;\n    setAccessibilityIndex(index);\n    if (backfill && mode === 'combobox' && active !== null && source === 'keyboard') {\n      setActiveValue(String(active));\n    }\n  }, [backfill, mode]);\n\n  // ========================= OptionList =========================\n  var triggerSelect = function triggerSelect(val, selected, type) {\n    var getSelectEnt = function getSelectEnt() {\n      var _option$key2;\n      var option = getMixedOption(val);\n      return [labelInValue ? {\n        label: option === null || option === void 0 ? void 0 : option[mergedFieldNames.label],\n        value: val,\n        key: (_option$key2 = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key2 !== void 0 ? _option$key2 : val\n      } : val, injectPropsWithOption(option)];\n    };\n    if (selected && onSelect) {\n      var _getSelectEnt = getSelectEnt(),\n        _getSelectEnt2 = _slicedToArray(_getSelectEnt, 2),\n        wrappedValue = _getSelectEnt2[0],\n        _option = _getSelectEnt2[1];\n      onSelect(wrappedValue, _option);\n    } else if (!selected && onDeselect && type !== 'clear') {\n      var _getSelectEnt3 = getSelectEnt(),\n        _getSelectEnt4 = _slicedToArray(_getSelectEnt3, 2),\n        _wrappedValue = _getSelectEnt4[0],\n        _option2 = _getSelectEnt4[1];\n      onDeselect(_wrappedValue, _option2);\n    }\n  };\n\n  // Used for OptionList selection\n  var onInternalSelect = useRefFunc(function (val, info) {\n    var cloneValues;\n\n    // Single mode always trigger select only with option list\n    var mergedSelect = multiple ? info.selected : true;\n    if (mergedSelect) {\n      cloneValues = multiple ? [].concat(_toConsumableArray(mergedValues), [val]) : [val];\n    } else {\n      cloneValues = mergedValues.filter(function (v) {\n        return v.value !== val;\n      });\n    }\n    triggerChange(cloneValues);\n    triggerSelect(val, mergedSelect);\n\n    // Clean search value if single or configured\n    if (mode === 'combobox') {\n      // setSearchValue(String(val));\n      setActiveValue('');\n    } else if (!isMultiple || autoClearSearchValue) {\n      setSearchValue('');\n      setActiveValue('');\n    }\n  });\n\n  // ======================= Display Change =======================\n  // BaseSelect display values change\n  var onDisplayValuesChange = function onDisplayValuesChange(nextValues, info) {\n    triggerChange(nextValues);\n    var type = info.type,\n      values = info.values;\n    if (type === 'remove' || type === 'clear') {\n      values.forEach(function (item) {\n        triggerSelect(item.value, false, type);\n      });\n    }\n  };\n\n  // =========================== Search ===========================\n  var onInternalSearch = function onInternalSearch(searchText, info) {\n    setSearchValue(searchText);\n    setActiveValue(null);\n\n    // [Submit] Tag mode should flush input\n    if (info.source === 'submit') {\n      var formatted = (searchText || '').trim();\n      // prevent empty tags from appearing when you click the Enter button\n      if (formatted) {\n        var newRawValues = Array.from(new Set([].concat(_toConsumableArray(rawValues), [formatted])));\n        triggerChange(newRawValues);\n        triggerSelect(formatted, true);\n        setSearchValue('');\n      }\n      return;\n    }\n    if (info.source !== 'blur') {\n      if (mode === 'combobox') {\n        triggerChange(searchText);\n      }\n      onSearch === null || onSearch === void 0 || onSearch(searchText);\n    }\n  };\n  var onInternalSearchSplit = function onInternalSearchSplit(words) {\n    var patchValues = words;\n    if (mode !== 'tags') {\n      patchValues = words.map(function (word) {\n        var opt = labelOptions.get(word);\n        return opt === null || opt === void 0 ? void 0 : opt.value;\n      }).filter(function (val) {\n        return val !== undefined;\n      });\n    }\n    var newRawValues = Array.from(new Set([].concat(_toConsumableArray(rawValues), _toConsumableArray(patchValues))));\n    triggerChange(newRawValues);\n    newRawValues.forEach(function (newRawValue) {\n      triggerSelect(newRawValue, true);\n    });\n  };\n\n  // ========================== Context ===========================\n  var selectContext = React.useMemo(function () {\n    var realVirtual = virtual !== false && dropdownMatchSelectWidth !== false;\n    return _objectSpread(_objectSpread({}, parsedOptions), {}, {\n      flattenOptions: displayOptions,\n      onActiveValue: onActiveValue,\n      defaultActiveFirstOption: mergedDefaultActiveFirstOption,\n      onSelect: onInternalSelect,\n      menuItemSelectedIcon: menuItemSelectedIcon,\n      rawValues: rawValues,\n      fieldNames: mergedFieldNames,\n      virtual: realVirtual,\n      direction: direction,\n      listHeight: listHeight,\n      listItemHeight: listItemHeight,\n      childrenAsData: childrenAsData,\n      maxCount: maxCount,\n      optionRender: optionRender\n    });\n  }, [maxCount, parsedOptions, displayOptions, onActiveValue, mergedDefaultActiveFirstOption, onInternalSelect, menuItemSelectedIcon, rawValues, mergedFieldNames, virtual, dropdownMatchSelectWidth, direction, listHeight, listItemHeight, childrenAsData, optionRender]);\n\n  // ========================== Warning ===========================\n  if (process.env.NODE_ENV !== 'production') {\n    warningProps(props);\n    warningNullOptions(mergedOptions, mergedFieldNames);\n  }\n\n  // ==============================================================\n  // ==                          Render                          ==\n  // ==============================================================\n  return /*#__PURE__*/React.createElement(SelectContext.Provider, {\n    value: selectContext\n  }, /*#__PURE__*/React.createElement(BaseSelect, _extends({}, restProps, {\n    // >>> MISC\n    id: mergedId,\n    prefixCls: prefixCls,\n    ref: ref,\n    omitDomProps: OMIT_DOM_PROPS,\n    mode: mode\n    // >>> Values\n    ,\n    displayValues: displayValues,\n    onDisplayValuesChange: onDisplayValuesChange\n    // >>> Trigger\n    ,\n    direction: direction\n    // >>> Search\n    ,\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch,\n    autoClearSearchValue: autoClearSearchValue,\n    onSearchSplit: onInternalSearchSplit,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n    // >>> OptionList\n    ,\n    OptionList: OptionList,\n    emptyOptions: !displayOptions.length\n    // >>> Accessibility\n    ,\n    activeValue: activeValue,\n    activeDescendantId: \"\".concat(mergedId, \"_list_\").concat(accessibilityIndex)\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Select.displayName = 'Select';\n}\nvar TypedSelect = Select;\nTypedSelect.Option = Option;\nTypedSelect.OptGroup = OptGroup;\nexport default TypedSelect;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,OAAO,MAAM,mCAAmC;AACvD,IAAIC,SAAS,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,sBAAsB,EAAE,UAAU,EAAE,YAAY,EAAE,0BAA0B,EAAE,cAAc,EAAE,YAAY,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,0BAA0B,EAAE,sBAAsB,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAAE,aAAa,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,CAAC;AACpe;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,IAAIC,UAAU,QAAQ,cAAc;AACrD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,SAASC,QAAQ,EAAEC,cAAc,EAAEC,OAAO,QAAQ,oBAAoB;AACtE,SAASC,cAAc,EAAEC,cAAc,EAAEC,qBAAqB,QAAQ,mBAAmB;AACzF,OAAOC,YAAY,IAAIC,kBAAkB,QAAQ,0BAA0B;AAC3E,IAAIC,cAAc,GAAG,CAAC,YAAY,CAAC;AACnC,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAO,CAACA,KAAK,IAAI1B,OAAO,CAAC0B,KAAK,CAAC,KAAK,QAAQ;AAC9C;AACA,IAAIC,MAAM,GAAG,aAAavB,KAAK,CAACwB,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC/D,IAAIC,EAAE,GAAGF,KAAK,CAACE,EAAE;IACfC,IAAI,GAAGH,KAAK,CAACG,IAAI;IACjBC,gBAAgB,GAAGJ,KAAK,CAACK,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,WAAW,GAAGA,gBAAgB;IACxEE,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,UAAU,GAAGP,KAAK,CAACO,UAAU;IAC7BC,UAAU,GAAGR,KAAK,CAACQ,UAAU;IAC7BC,WAAW,GAAGT,KAAK,CAACS,WAAW;IAC/BC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,qBAAqB,GAAGX,KAAK,CAACY,oBAAoB;IAClDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IACtFE,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,UAAU,GAAGd,KAAK,CAACc,UAAU;IAC7BC,qBAAqB,GAAGf,KAAK,CAACgB,wBAAwB;IACtDA,wBAAwB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IAC1FE,YAAY,GAAGjB,KAAK,CAACiB,YAAY;IACjCC,UAAU,GAAGlB,KAAK,CAACkB,UAAU;IAC7BC,gBAAgB,GAAGnB,KAAK,CAACmB,gBAAgB;IACzCC,eAAe,GAAGpB,KAAK,CAACoB,eAAe;IACvCC,OAAO,GAAGrB,KAAK,CAACqB,OAAO;IACvBC,YAAY,GAAGtB,KAAK,CAACsB,YAAY;IACjCC,QAAQ,GAAGvB,KAAK,CAACuB,QAAQ;IACzBC,wBAAwB,GAAGxB,KAAK,CAACwB,wBAAwB;IACzDC,oBAAoB,GAAGzB,KAAK,CAACyB,oBAAoB;IACjDC,OAAO,GAAG1B,KAAK,CAAC0B,OAAO;IACvBC,SAAS,GAAG3B,KAAK,CAAC2B,SAAS;IAC3BC,iBAAiB,GAAG5B,KAAK,CAAC6B,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,iBAAiB;IACnEE,qBAAqB,GAAG9B,KAAK,CAAC+B,cAAc;IAC5CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,qBAAqB;IAC9EE,WAAW,GAAGhC,KAAK,CAACgC,WAAW;IAC/BnC,KAAK,GAAGG,KAAK,CAACH,KAAK;IACnBoC,YAAY,GAAGjC,KAAK,CAACiC,YAAY;IACjCC,YAAY,GAAGlC,KAAK,CAACkC,YAAY;IACjCC,QAAQ,GAAGnC,KAAK,CAACmC,QAAQ;IACzBC,QAAQ,GAAGpC,KAAK,CAACoC,QAAQ;IACzBC,SAAS,GAAGnE,wBAAwB,CAAC8B,KAAK,EAAE5B,SAAS,CAAC;EACxD,IAAIkE,QAAQ,GAAGtD,KAAK,CAACkB,EAAE,CAAC;EACxB,IAAIqC,QAAQ,GAAG9D,UAAU,CAAC0B,IAAI,CAAC;EAC/B,IAAIqC,cAAc,GAAG,CAAC,EAAE,CAACnB,OAAO,IAAIE,QAAQ,CAAC;EAC7C,IAAIkB,kBAAkB,GAAGlE,KAAK,CAACmE,OAAO,CAAC,YAAY;IACjD,IAAIzB,YAAY,KAAK0B,SAAS,IAAIxC,IAAI,KAAK,UAAU,EAAE;MACrD,OAAO,KAAK;IACd;IACA,OAAOc,YAAY;EACrB,CAAC,EAAE,CAACA,YAAY,EAAEd,IAAI,CAAC,CAAC;;EAExB;EACA,IAAIyC,gBAAgB,GAAGrE,KAAK,CAACmE,OAAO,CAAC,YAAY;IAC/C,OAAOpD,cAAc,CAACiB,UAAU,EAAEiC,cAAc,CAAC;EACnD,CAAC,EAAE;EACH;EACA;EACAK,IAAI,CAACC,SAAS,CAACvC,UAAU,CAAC,EAAEiC,cAAc;EAC1C,+CAA+C,CAAC;;EAEhD;EACA,IAAIO,eAAe,GAAG1E,cAAc,CAAC,EAAE,EAAE;MACrCwB,KAAK,EAAEY,WAAW,KAAKkC,SAAS,GAAGlC,WAAW,GAAGD,UAAU;MAC3DwC,SAAS,EAAE,SAASA,SAASA,CAACC,MAAM,EAAE;QACpC,OAAOA,MAAM,IAAI,EAAE;MACrB;IACF,CAAC,CAAC;IACFC,gBAAgB,GAAGjF,cAAc,CAAC8E,eAAe,EAAE,CAAC,CAAC;IACrDI,iBAAiB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACvCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAEtC;EACA,IAAIG,aAAa,GAAGpE,UAAU,CAACoC,OAAO,EAAEE,QAAQ,EAAEqB,gBAAgB,EAAEzB,gBAAgB,EAAEC,eAAe,CAAC;EACtG,IAAIkC,YAAY,GAAGD,aAAa,CAACC,YAAY;IAC3CC,YAAY,GAAGF,aAAa,CAACE,YAAY;IACzCC,aAAa,GAAGH,aAAa,CAAChC,OAAO;;EAEvC;EACA,IAAIoC,mBAAmB,GAAGlF,KAAK,CAACmF,WAAW,CAAC,UAAUC,WAAW,EAAE;IACjE;IACA,IAAIC,SAAS,GAAGvE,OAAO,CAACsE,WAAW,CAAC;;IAEpC;IACA,OAAOC,SAAS,CAACC,GAAG,CAAC,UAAUC,GAAG,EAAE;MAClC,IAAIC,QAAQ;MACZ,IAAIC,QAAQ;MACZ,IAAIC,MAAM;MACV,IAAIC,WAAW;MACf,IAAIC,QAAQ;;MAEZ;MACA,IAAIvE,UAAU,CAACkE,GAAG,CAAC,EAAE;QACnBC,QAAQ,GAAGD,GAAG;MAChB,CAAC,MAAM;QACL,IAAIM,UAAU;QACdH,MAAM,GAAGH,GAAG,CAACO,GAAG;QAChBL,QAAQ,GAAGF,GAAG,CAACQ,KAAK;QACpBP,QAAQ,GAAG,CAACK,UAAU,GAAGN,GAAG,CAACjE,KAAK,MAAM,IAAI,IAAIuE,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGH,MAAM;MAC7F;MACA,IAAIM,MAAM,GAAGjB,YAAY,CAACkB,GAAG,CAACT,QAAQ,CAAC;MACvC,IAAIQ,MAAM,EAAE;QACV,IAAIE,WAAW;QACf;QACA,IAAIT,QAAQ,KAAKrB,SAAS,EAAEqB,QAAQ,GAAGO,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACnD,eAAe,IAAIwB,gBAAgB,CAAC0B,KAAK,CAAC;QACxI,IAAIL,MAAM,KAAKtB,SAAS,EAAEsB,MAAM,GAAG,CAACQ,WAAW,GAAGF,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACF,GAAG,MAAM,IAAI,IAAII,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAGV,QAAQ;QACzKG,WAAW,GAAGK,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACG,QAAQ;QAC7EP,QAAQ,GAAGI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACI,KAAK;;QAEvE;QACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAAC1D,eAAe,EAAE;UAC7D,IAAI2D,WAAW,GAAGR,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC3B,gBAAgB,CAAC0B,KAAK,CAAC;UAChG,IAAIS,WAAW,KAAKpC,SAAS,IAAI,EAAE,aAAapE,KAAK,CAACyG,cAAc,CAACD,WAAW,CAAC,IAAI,EAAE,aAAaxG,KAAK,CAACyG,cAAc,CAAChB,QAAQ,CAAC,IAAIe,WAAW,KAAKf,QAAQ,EAAE;YAC9J1F,OAAO,CAAC,KAAK,EAAE,8DAA8D,CAAC;UAChF;QACF;MACF;MACA,OAAO;QACLgG,KAAK,EAAEN,QAAQ;QACfnE,KAAK,EAAEkE,QAAQ;QACfM,GAAG,EAAEJ,MAAM;QACXS,QAAQ,EAAER,WAAW;QACrBS,KAAK,EAAER;MACT,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvB,gBAAgB,EAAExB,eAAe,EAAEkC,YAAY,CAAC,CAAC;;EAErD;EACA,IAAI2B,gBAAgB,GAAG5G,cAAc,CAAC4D,YAAY,EAAE;MAChDpC,KAAK,EAAEA;IACT,CAAC,CAAC;IACFqF,gBAAgB,GAAGjH,cAAc,CAACgH,gBAAgB,EAAE,CAAC,CAAC;IACtDE,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAExC;EACA,IAAIG,gBAAgB,GAAG9G,KAAK,CAACmE,OAAO,CAAC,YAAY;IAC/C,IAAI4C,QAAQ;IACZ,IAAIC,gBAAgB,GAAGhD,QAAQ,IAAI4C,aAAa,KAAK,IAAI,GAAG,EAAE,GAAGA,aAAa;IAC9E,IAAIK,MAAM,GAAG/B,mBAAmB,CAAC8B,gBAAgB,CAAC;;IAElD;IACA,IAAIpF,IAAI,KAAK,UAAU,IAAIf,cAAc,CAAC,CAACkG,QAAQ,GAAGE,MAAM,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIF,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACzF,KAAK,CAAC,EAAE;MAC3H,OAAO,EAAE;IACX;IACA,OAAO2F,MAAM;EACf,CAAC,EAAE,CAACL,aAAa,EAAE1B,mBAAmB,EAAEtD,IAAI,EAAEoC,QAAQ,CAAC,CAAC;;EAExD;EACA,IAAIkD,SAAS,GAAG3G,QAAQ,CAACuG,gBAAgB,EAAE/B,YAAY,CAAC;IACtDoC,UAAU,GAAGzH,cAAc,CAACwH,SAAS,EAAE,CAAC,CAAC;IACzCE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,cAAc,GAAGF,UAAU,CAAC,CAAC,CAAC;EAChC,IAAIG,aAAa,GAAGtH,KAAK,CAACmE,OAAO,CAAC,YAAY;IAC5C;IACA;IACA,IAAI,CAACvC,IAAI,IAAIwF,YAAY,CAACG,MAAM,KAAK,CAAC,EAAE;MACtC,IAAIC,UAAU,GAAGJ,YAAY,CAAC,CAAC,CAAC;MAChC,IAAII,UAAU,CAAClG,KAAK,KAAK,IAAI,KAAKkG,UAAU,CAACzB,KAAK,KAAK,IAAI,IAAIyB,UAAU,CAACzB,KAAK,KAAK3B,SAAS,CAAC,EAAE;QAC9F,OAAO,EAAE;MACX;IACF;IACA,OAAOgD,YAAY,CAAC9B,GAAG,CAAC,UAAUmC,IAAI,EAAE;MACtC,IAAIC,IAAI;MACR,OAAOjI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgI,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QAChD1B,KAAK,EAAE,CAAC2B,IAAI,GAAG,OAAOjE,WAAW,KAAK,UAAU,GAAGA,WAAW,CAACgE,IAAI,CAAC,GAAGA,IAAI,CAAC1B,KAAK,MAAM,IAAI,IAAI2B,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGD,IAAI,CAACnG;MAC/H,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACM,IAAI,EAAEwF,YAAY,EAAE3D,WAAW,CAAC,CAAC;;EAErC;EACA,IAAIkE,SAAS,GAAG3H,KAAK,CAACmE,OAAO,CAAC,YAAY;IACxC,OAAO,IAAIyD,GAAG,CAACR,YAAY,CAAC9B,GAAG,CAAC,UAAUC,GAAG,EAAE;MAC7C,OAAOA,GAAG,CAACjE,KAAK;IAClB,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAAC8F,YAAY,CAAC,CAAC;EAClBpH,KAAK,CAAC6H,SAAS,CAAC,YAAY;IAC1B,IAAIjG,IAAI,KAAK,UAAU,EAAE;MACvB,IAAIkG,cAAc;MAClB,IAAIC,QAAQ,GAAG,CAACD,cAAc,GAAGV,YAAY,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIU,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACxG,KAAK;MACvHuD,cAAc,CAACjE,QAAQ,CAACmH,QAAQ,CAAC,GAAGC,MAAM,CAACD,QAAQ,CAAC,GAAG,EAAE,CAAC;IAC5D;EACF,CAAC,EAAE,CAACX,YAAY,CAAC,CAAC;;EAElB;EACA;EACA,IAAIa,eAAe,GAAGtH,UAAU,CAAC,UAAU4E,GAAG,EAAEQ,KAAK,EAAE;IACrD,IAAImC,WAAW,GAAGnC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGR,GAAG;IAClE,OAAO/F,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE6E,gBAAgB,CAAC/C,KAAK,EAAEiE,GAAG,CAAC,EAAElB,gBAAgB,CAAC0B,KAAK,EAAEmC,WAAW,CAAC;EAC/G,CAAC,CAAC;;EAEF;EACA,IAAIC,gBAAgB,GAAGnI,KAAK,CAACmE,OAAO,CAAC,YAAY;IAC/C,IAAIvC,IAAI,KAAK,MAAM,EAAE;MACnB,OAAOqD,aAAa;IACtB;;IAEA;IACA,IAAImD,YAAY,GAAG7I,kBAAkB,CAAC0F,aAAa,CAAC;;IAEpD;IACA,IAAIoD,YAAY,GAAG,SAASA,YAAYA,CAAC9C,GAAG,EAAE;MAC5C,OAAOR,YAAY,CAACuD,GAAG,CAAC/C,GAAG,CAAC;IAC9B,CAAC;;IAED;IACAhG,kBAAkB,CAAC6H,YAAY,CAAC,CAACmB,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACpD,OAAOD,CAAC,CAAClH,KAAK,GAAGmH,CAAC,CAACnH,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;IACnC,CAAC,CAAC,CAACoH,OAAO,CAAC,UAAUjB,IAAI,EAAE;MACzB,IAAIlC,GAAG,GAAGkC,IAAI,CAACnG,KAAK;MACpB,IAAI,CAAC+G,YAAY,CAAC9C,GAAG,CAAC,EAAE;QACtB6C,YAAY,CAACO,IAAI,CAACV,eAAe,CAAC1C,GAAG,EAAEkC,IAAI,CAAC1B,KAAK,CAAC,CAAC;MACrD;IACF,CAAC,CAAC;IACF,OAAOqC,YAAY;EACrB,CAAC,EAAE,CAACH,eAAe,EAAEhD,aAAa,EAAEF,YAAY,EAAEqC,YAAY,EAAExF,IAAI,CAAC,CAAC;EACtE,IAAIgH,eAAe,GAAGpI,gBAAgB,CAAC2H,gBAAgB,EAAE9D,gBAAgB,EAAEO,iBAAiB,EAAEV,kBAAkB,EAAEtB,gBAAgB,CAAC;;EAEnI;EACA,IAAIiG,mBAAmB,GAAG7I,KAAK,CAACmE,OAAO,CAAC,YAAY;IAClD,IAAIvC,IAAI,KAAK,MAAM,IAAI,CAACgD,iBAAiB,IAAIgE,eAAe,CAACE,IAAI,CAAC,UAAUrB,IAAI,EAAE;MAChF,OAAOA,IAAI,CAAC7E,gBAAgB,IAAI,OAAO,CAAC,KAAKgC,iBAAiB;IAChE,CAAC,CAAC,EAAE;MACF,OAAOgE,eAAe;IACxB;IACA;IACA,IAAIA,eAAe,CAACE,IAAI,CAAC,UAAUrB,IAAI,EAAE;MACvC,OAAOA,IAAI,CAACpD,gBAAgB,CAAC/C,KAAK,CAAC,KAAKsD,iBAAiB;IAC3D,CAAC,CAAC,EAAE;MACF,OAAOgE,eAAe;IACxB;IACA;IACA,OAAO,CAACX,eAAe,CAACrD,iBAAiB,CAAC,CAAC,CAACmE,MAAM,CAACxJ,kBAAkB,CAACqJ,eAAe,CAAC,CAAC;EACzF,CAAC,EAAE,CAACX,eAAe,EAAErF,gBAAgB,EAAEhB,IAAI,EAAEgH,eAAe,EAAEhE,iBAAiB,EAAEP,gBAAgB,CAAC,CAAC;EACnG,IAAI2E,MAAM,GAAG,SAASA,MAAMA,CAACC,YAAY,EAAE;IACzC,IAAIC,aAAa,GAAG3J,kBAAkB,CAAC0J,YAAY,CAAC,CAACV,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACxE,OAAO9F,UAAU,CAAC6F,CAAC,EAAEC,CAAC,EAAE;QACtBvG,WAAW,EAAE0C;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOsE,aAAa,CAAC5D,GAAG,CAAC,UAAUmC,IAAI,EAAE;MACvC,IAAI0B,KAAK,CAACC,OAAO,CAAC3B,IAAI,CAAC3E,OAAO,CAAC,EAAE;QAC/B,OAAOrD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgI,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UAChD3E,OAAO,EAAE2E,IAAI,CAAC3E,OAAO,CAACyE,MAAM,GAAG,CAAC,GAAGyB,MAAM,CAACvB,IAAI,CAAC3E,OAAO,CAAC,GAAG2E,IAAI,CAAC3E;QACjE,CAAC,CAAC;MACJ;MACA,OAAO2E,IAAI;IACb,CAAC,CAAC;EACJ,CAAC;EACD,IAAI4B,sBAAsB,GAAGrJ,KAAK,CAACmE,OAAO,CAAC,YAAY;IACrD,IAAI,CAACxB,UAAU,EAAE;MACf,OAAOkG,mBAAmB;IAC5B;IACA,OAAOG,MAAM,CAACH,mBAAmB,CAAC;EACpC,CAAC,EAAE,CAACA,mBAAmB,EAAElG,UAAU,EAAEiC,iBAAiB,CAAC,CAAC;EACxD,IAAI0E,cAAc,GAAGtJ,KAAK,CAACmE,OAAO,CAAC,YAAY;IAC7C,OAAOnD,cAAc,CAACqI,sBAAsB,EAAE;MAC5CrH,UAAU,EAAEqC,gBAAgB;MAC5BJ,cAAc,EAAEA;IAClB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACoF,sBAAsB,EAAEhF,gBAAgB,EAAEJ,cAAc,CAAC,CAAC;;EAE9D;EACA,IAAIsF,aAAa,GAAG,SAASA,aAAaA,CAACtC,MAAM,EAAE;IACjD,IAAIuC,aAAa,GAAGtE,mBAAmB,CAAC+B,MAAM,CAAC;IAC/CJ,gBAAgB,CAAC2C,aAAa,CAAC;IAC/B,IAAI5F,QAAQ;IACZ;IACA4F,aAAa,CAACjC,MAAM,KAAKH,YAAY,CAACG,MAAM,IAAIiC,aAAa,CAACV,IAAI,CAAC,UAAUW,MAAM,EAAEC,KAAK,EAAE;MAC1F,IAAIC,mBAAmB;MACvB,OAAO,CAAC,CAACA,mBAAmB,GAAGvC,YAAY,CAACsC,KAAK,CAAC,MAAM,IAAI,IAAIC,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACrI,KAAK,OAAOmI,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACnI,KAAK,CAAC;IACzM,CAAC,CAAC,CAAC,EAAE;MACH,IAAIsI,YAAY,GAAGjG,YAAY,GAAG6F,aAAa,GAAGA,aAAa,CAAClE,GAAG,CAAC,UAAUuE,CAAC,EAAE;QAC/E,OAAOA,CAAC,CAACvI,KAAK;MAChB,CAAC,CAAC;MACF,IAAIwI,aAAa,GAAGN,aAAa,CAAClE,GAAG,CAAC,UAAUuE,CAAC,EAAE;QACjD,OAAO5I,qBAAqB,CAACoG,cAAc,CAACwC,CAAC,CAACvI,KAAK,CAAC,CAAC;MACvD,CAAC,CAAC;MACFsC,QAAQ;MACR;MACAI,QAAQ,GAAG4F,YAAY,GAAGA,YAAY,CAAC,CAAC,CAAC;MACzC;MACA5F,QAAQ,GAAG8F,aAAa,GAAGA,aAAa,CAAC,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,IAAIC,eAAe,GAAG/J,KAAK,CAACgK,QAAQ,CAAC,IAAI,CAAC;IACxCC,gBAAgB,GAAGvK,cAAc,CAACqK,eAAe,EAAE,CAAC,CAAC;IACrDG,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,gBAAgB,GAAGpK,KAAK,CAACgK,QAAQ,CAAC,CAAC,CAAC;IACtCK,gBAAgB,GAAG3K,cAAc,CAAC0K,gBAAgB,EAAE,CAAC,CAAC;IACtDE,kBAAkB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACxCE,qBAAqB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC7C,IAAIG,8BAA8B,GAAGvH,wBAAwB,KAAKmB,SAAS,GAAGnB,wBAAwB,GAAGrB,IAAI,KAAK,UAAU;EAC5H,IAAI6I,aAAa,GAAGzK,KAAK,CAACmF,WAAW,CAAC,UAAUuF,MAAM,EAAEhB,KAAK,EAAE;IAC7D,IAAIiB,KAAK,GAAGC,SAAS,CAACrD,MAAM,GAAG,CAAC,IAAIqD,SAAS,CAAC,CAAC,CAAC,KAAKxG,SAAS,GAAGwG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAChFC,YAAY,GAAGF,KAAK,CAACG,MAAM;MAC3BA,MAAM,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,YAAY;IAC9DN,qBAAqB,CAACb,KAAK,CAAC;IAC5B,IAAI3H,QAAQ,IAAIH,IAAI,KAAK,UAAU,IAAI8I,MAAM,KAAK,IAAI,IAAII,MAAM,KAAK,UAAU,EAAE;MAC/EX,cAAc,CAACnC,MAAM,CAAC0C,MAAM,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAAC3I,QAAQ,EAAEH,IAAI,CAAC,CAAC;;EAEpB;EACA,IAAImJ,aAAa,GAAG,SAASA,aAAaA,CAACxF,GAAG,EAAEyF,QAAQ,EAAEC,IAAI,EAAE;IAC9D,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;MACzC,IAAIC,YAAY;MAChB,IAAInF,MAAM,GAAGqB,cAAc,CAAC9B,GAAG,CAAC;MAChC,OAAO,CAAC5B,YAAY,GAAG;QACrBoC,KAAK,EAAEC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC3B,gBAAgB,CAAC0B,KAAK,CAAC;QACrFzE,KAAK,EAAEiE,GAAG;QACVO,GAAG,EAAE,CAACqF,YAAY,GAAGnF,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACF,GAAG,MAAM,IAAI,IAAIqF,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG5F;MACxI,CAAC,GAAGA,GAAG,EAAEtE,qBAAqB,CAAC+E,MAAM,CAAC,CAAC;IACzC,CAAC;IACD,IAAIgF,QAAQ,IAAI1I,QAAQ,EAAE;MACxB,IAAI8I,aAAa,GAAGF,YAAY,CAAC,CAAC;QAChCG,cAAc,GAAG3L,cAAc,CAAC0L,aAAa,EAAE,CAAC,CAAC;QACjDE,YAAY,GAAGD,cAAc,CAAC,CAAC,CAAC;QAChCE,OAAO,GAAGF,cAAc,CAAC,CAAC,CAAC;MAC7B/I,QAAQ,CAACgJ,YAAY,EAAEC,OAAO,CAAC;IACjC,CAAC,MAAM,IAAI,CAACP,QAAQ,IAAIzI,UAAU,IAAI0I,IAAI,KAAK,OAAO,EAAE;MACtD,IAAIO,cAAc,GAAGN,YAAY,CAAC,CAAC;QACjCO,cAAc,GAAG/L,cAAc,CAAC8L,cAAc,EAAE,CAAC,CAAC;QAClDE,aAAa,GAAGD,cAAc,CAAC,CAAC,CAAC;QACjCE,QAAQ,GAAGF,cAAc,CAAC,CAAC,CAAC;MAC9BlJ,UAAU,CAACmJ,aAAa,EAAEC,QAAQ,CAAC;IACrC;EACF,CAAC;;EAED;EACA,IAAIC,gBAAgB,GAAGjL,UAAU,CAAC,UAAU4E,GAAG,EAAEsG,IAAI,EAAE;IACrD,IAAIC,WAAW;;IAEf;IACA,IAAIC,YAAY,GAAG/H,QAAQ,GAAG6H,IAAI,CAACb,QAAQ,GAAG,IAAI;IAClD,IAAIe,YAAY,EAAE;MAChBD,WAAW,GAAG9H,QAAQ,GAAG,EAAE,CAAC+E,MAAM,CAACxJ,kBAAkB,CAAC6H,YAAY,CAAC,EAAE,CAAC7B,GAAG,CAAC,CAAC,GAAG,CAACA,GAAG,CAAC;IACrF,CAAC,MAAM;MACLuG,WAAW,GAAG1E,YAAY,CAAC4E,MAAM,CAAC,UAAUnC,CAAC,EAAE;QAC7C,OAAOA,CAAC,CAACvI,KAAK,KAAKiE,GAAG;MACxB,CAAC,CAAC;IACJ;IACAgE,aAAa,CAACuC,WAAW,CAAC;IAC1Bf,aAAa,CAACxF,GAAG,EAAEwG,YAAY,CAAC;;IAEhC;IACA,IAAInK,IAAI,KAAK,UAAU,EAAE;MACvB;MACAuI,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,MAAM,IAAI,CAACjK,UAAU,IAAImC,oBAAoB,EAAE;MAC9CwC,cAAc,CAAC,EAAE,CAAC;MAClBsF,cAAc,CAAC,EAAE,CAAC;IACpB;EACF,CAAC,CAAC;;EAEF;EACA;EACA,IAAI8B,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,UAAU,EAAEL,IAAI,EAAE;IAC3EtC,aAAa,CAAC2C,UAAU,CAAC;IACzB,IAAIjB,IAAI,GAAGY,IAAI,CAACZ,IAAI;MAClBhE,MAAM,GAAG4E,IAAI,CAAC5E,MAAM;IACtB,IAAIgE,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,OAAO,EAAE;MACzChE,MAAM,CAACyB,OAAO,CAAC,UAAUjB,IAAI,EAAE;QAC7BsD,aAAa,CAACtD,IAAI,CAACnG,KAAK,EAAE,KAAK,EAAE2J,IAAI,CAAC;MACxC,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,IAAIkB,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,UAAU,EAAEP,IAAI,EAAE;IACjEhH,cAAc,CAACuH,UAAU,CAAC;IAC1BjC,cAAc,CAAC,IAAI,CAAC;;IAEpB;IACA,IAAI0B,IAAI,CAACf,MAAM,KAAK,QAAQ,EAAE;MAC5B,IAAIuB,SAAS,GAAG,CAACD,UAAU,IAAI,EAAE,EAAEE,IAAI,CAAC,CAAC;MACzC;MACA,IAAID,SAAS,EAAE;QACb,IAAIE,YAAY,GAAGpD,KAAK,CAACqD,IAAI,CAAC,IAAI5E,GAAG,CAAC,EAAE,CAACmB,MAAM,CAACxJ,kBAAkB,CAACoI,SAAS,CAAC,EAAE,CAAC0E,SAAS,CAAC,CAAC,CAAC,CAAC;QAC7F9C,aAAa,CAACgD,YAAY,CAAC;QAC3BxB,aAAa,CAACsB,SAAS,EAAE,IAAI,CAAC;QAC9BxH,cAAc,CAAC,EAAE,CAAC;MACpB;MACA;IACF;IACA,IAAIgH,IAAI,CAACf,MAAM,KAAK,MAAM,EAAE;MAC1B,IAAIlJ,IAAI,KAAK,UAAU,EAAE;QACvB2H,aAAa,CAAC6C,UAAU,CAAC;MAC3B;MACAjK,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACiK,UAAU,CAAC;IAClE;EACF,CAAC;EACD,IAAIK,qBAAqB,GAAG,SAASA,qBAAqBA,CAACC,KAAK,EAAE;IAChE,IAAIC,WAAW,GAAGD,KAAK;IACvB,IAAI9K,IAAI,KAAK,MAAM,EAAE;MACnB+K,WAAW,GAAGD,KAAK,CAACpH,GAAG,CAAC,UAAUsH,IAAI,EAAE;QACtC,IAAIC,GAAG,GAAG7H,YAAY,CAACiB,GAAG,CAAC2G,IAAI,CAAC;QAChC,OAAOC,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACvL,KAAK;MAC5D,CAAC,CAAC,CAAC0K,MAAM,CAAC,UAAUzG,GAAG,EAAE;QACvB,OAAOA,GAAG,KAAKnB,SAAS;MAC1B,CAAC,CAAC;IACJ;IACA,IAAImI,YAAY,GAAGpD,KAAK,CAACqD,IAAI,CAAC,IAAI5E,GAAG,CAAC,EAAE,CAACmB,MAAM,CAACxJ,kBAAkB,CAACoI,SAAS,CAAC,EAAEpI,kBAAkB,CAACoN,WAAW,CAAC,CAAC,CAAC,CAAC;IACjHpD,aAAa,CAACgD,YAAY,CAAC;IAC3BA,YAAY,CAAC7D,OAAO,CAAC,UAAUoE,WAAW,EAAE;MAC1C/B,aAAa,CAAC+B,WAAW,EAAE,IAAI,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,IAAIC,aAAa,GAAG/M,KAAK,CAACmE,OAAO,CAAC,YAAY;IAC5C,IAAI6I,WAAW,GAAG7J,OAAO,KAAK,KAAK,IAAIV,wBAAwB,KAAK,KAAK;IACzE,OAAOhD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqF,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;MACzD9D,cAAc,EAAEsI,cAAc;MAC9BmB,aAAa,EAAEA,aAAa;MAC5BxH,wBAAwB,EAAEuH,8BAA8B;MACxDlI,QAAQ,EAAEsJ,gBAAgB;MAC1B1I,oBAAoB,EAAEA,oBAAoB;MAC1CyE,SAAS,EAAEA,SAAS;MACpB3F,UAAU,EAAEqC,gBAAgB;MAC5BlB,OAAO,EAAE6J,WAAW;MACpB5J,SAAS,EAAEA,SAAS;MACpBE,UAAU,EAAEA,UAAU;MACtBE,cAAc,EAAEA,cAAc;MAC9BS,cAAc,EAAEA,cAAc;MAC9BJ,QAAQ,EAAEA,QAAQ;MAClBd,YAAY,EAAEA;IAChB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACc,QAAQ,EAAEiB,aAAa,EAAEwE,cAAc,EAAEmB,aAAa,EAAED,8BAA8B,EAAEoB,gBAAgB,EAAE1I,oBAAoB,EAAEyE,SAAS,EAAEtD,gBAAgB,EAAElB,OAAO,EAAEV,wBAAwB,EAAEW,SAAS,EAAEE,UAAU,EAAEE,cAAc,EAAES,cAAc,EAAElB,YAAY,CAAC,CAAC;;EAEzQ;EACA,IAAIsD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCrF,YAAY,CAACO,KAAK,CAAC;IACnBN,kBAAkB,CAAC8D,aAAa,EAAEZ,gBAAgB,CAAC;EACrD;;EAEA;EACA;EACA;EACA,OAAO,aAAarE,KAAK,CAACiN,aAAa,CAAC3M,aAAa,CAAC4M,QAAQ,EAAE;IAC9D5L,KAAK,EAAEyL;EACT,CAAC,EAAE,aAAa/M,KAAK,CAACiN,aAAa,CAAChN,UAAU,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAEwE,SAAS,EAAE;IACtE;IACAnC,EAAE,EAAEoC,QAAQ;IACZjC,SAAS,EAAEA,SAAS;IACpBJ,GAAG,EAAEA,GAAG;IACRyL,YAAY,EAAE/L,cAAc;IAC5BQ,IAAI,EAAEA;IACN;IAAA;;IAEA0F,aAAa,EAAEA,aAAa;IAC5B2E,qBAAqB,EAAEA;IACvB;IAAA;;IAEA7I,SAAS,EAAEA;IACX;IAAA;;IAEAlB,WAAW,EAAE0C,iBAAiB;IAC9BzC,QAAQ,EAAEgK,gBAAgB;IAC1B9J,oBAAoB,EAAEA,oBAAoB;IAC1C+K,aAAa,EAAEX,qBAAqB;IACpChK,wBAAwB,EAAEA;IAC1B;IAAA;;IAEApC,UAAU,EAAEA,UAAU;IACtBgN,YAAY,EAAE,CAAC/D,cAAc,CAAC/B;IAC9B;IAAA;;IAEA2C,WAAW,EAAEA,WAAW;IACxBoD,kBAAkB,EAAE,EAAE,CAACvE,MAAM,CAAChF,QAAQ,EAAE,QAAQ,CAAC,CAACgF,MAAM,CAACuB,kBAAkB;EAC7E,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF,IAAIjE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzChF,MAAM,CAACgM,WAAW,GAAG,QAAQ;AAC/B;AACA,IAAIC,WAAW,GAAGjM,MAAM;AACxBiM,WAAW,CAACpN,MAAM,GAAGA,MAAM;AAC3BoN,WAAW,CAACrN,QAAQ,GAAGA,QAAQ;AAC/B,eAAeqN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}