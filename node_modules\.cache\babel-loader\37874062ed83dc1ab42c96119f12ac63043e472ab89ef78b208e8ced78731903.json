{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport AlertOutlinedSvg from \"@ant-design/icons-svg/es/asn/AlertOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar AlertOutlined = function AlertOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: AlertOutlinedSvg\n  }));\n};\n\n/**![alert](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE5MyA3OTZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNTc0YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjU2M2MwLTE3Ni4yLTE0Mi44LTMxOS0zMTktMzE5UzE5MyAzODYuOCAxOTMgNTYzdjIzM3ptNzItMjMzYzAtMTM2LjQgMTEwLjYtMjQ3IDI0Ny0yNDdzMjQ3IDExMC42IDI0NyAyNDd2MTkzSDQwNFY1ODVjMC01LjUtNC41LTEwLTEwLTEwaC00NGMtNS41IDAtMTAgNC41LTEwIDEwdjE3MWgtNzVWNTYzem0tNDguMS0yNTIuNWwzOS42LTM5LjZjMy4xLTMuMSAzLjEtOC4yIDAtMTEuM2wtNjcuOS02Ny45YTguMDMgOC4wMyAwIDAwLTExLjMgMGwtMzkuNiAzOS42YTguMDMgOC4wMyAwIDAwMCAxMS4zbDY3LjkgNjcuOWMzLjEgMy4xIDguMSAzLjEgMTEuMyAwem02NjkuNi03OS4ybC0zOS42LTM5LjZhOC4wMyA4LjAzIDAgMDAtMTEuMyAwbC02Ny45IDY3LjlhOC4wMyA4LjAzIDAgMDAwIDExLjNsMzkuNiAzOS42YzMuMSAzLjEgOC4yIDMuMSAxMS4zIDBsNjcuOS02Ny45YzMuMS0zLjIgMy4xLTguMiAwLTExLjN6TTgzMiA4OTJIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYyNGMwIDQuNCAzLjYgOCA4IDhoNjg4YzQuNCAwIDgtMy42IDgtOHYtMjRjMC0xNy43LTE0LjMtMzItMzItMzJ6TTQ4NCAxODBoNTZjNC40IDAgOC0zLjYgOC04Vjc2YzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHY5NmMwIDQuNCAzLjYgOCA4IDh6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(AlertOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'AlertOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "AlertOutlinedSvg", "AntdIcon", "Alert<PERSON>ut<PERSON>", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@ant-design/icons/es/icons/AlertOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport AlertOutlinedSvg from \"@ant-design/icons-svg/es/asn/AlertOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar AlertOutlined = function AlertOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: AlertOutlinedSvg\n  }));\n};\n\n/**![alert](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE5MyA3OTZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNTc0YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjU2M2MwLTE3Ni4yLTE0Mi44LTMxOS0zMTktMzE5UzE5MyAzODYuOCAxOTMgNTYzdjIzM3ptNzItMjMzYzAtMTM2LjQgMTEwLjYtMjQ3IDI0Ny0yNDdzMjQ3IDExMC42IDI0NyAyNDd2MTkzSDQwNFY1ODVjMC01LjUtNC41LTEwLTEwLTEwaC00NGMtNS41IDAtMTAgNC41LTEwIDEwdjE3MWgtNzVWNTYzem0tNDguMS0yNTIuNWwzOS42LTM5LjZjMy4xLTMuMSAzLjEtOC4yIDAtMTEuM2wtNjcuOS02Ny45YTguMDMgOC4wMyAwIDAwLTExLjMgMGwtMzkuNiAzOS42YTguMDMgOC4wMyAwIDAwMCAxMS4zbDY3LjkgNjcuOWMzLjEgMy4xIDguMSAzLjEgMTEuMyAwem02NjkuNi03OS4ybC0zOS42LTM5LjZhOC4wMyA4LjAzIDAgMDAtMTEuMyAwbC02Ny45IDY3LjlhOC4wMyA4LjAzIDAgMDAwIDExLjNsMzkuNiAzOS42YzMuMSAzLjEgOC4yIDMuMSAxMS4zIDBsNjcuOS02Ny45YzMuMS0zLjIgMy4xLTguMiAwLTExLjN6TTgzMiA4OTJIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYyNGMwIDQuNCAzLjYgOCA4IDhoNjg4YzQuNCAwIDgtMy42IDgtOHYtMjRjMC0xNy43LTE0LjMtMzItMzItMzJ6TTQ4NCAxODBoNTZjNC40IDAgOC0zLjYgOC04Vjc2YzAtNC40LTMuNi04LTgtOGgtNTZjLTQuNCAwLTggMy42LTggOHY5NmMwIDQuNCAzLjYgOCA4IDh6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(AlertOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'AlertOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,4CAA4C;AACzE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,aAAa,CAAC;AAC1D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,eAAe;AACvC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}