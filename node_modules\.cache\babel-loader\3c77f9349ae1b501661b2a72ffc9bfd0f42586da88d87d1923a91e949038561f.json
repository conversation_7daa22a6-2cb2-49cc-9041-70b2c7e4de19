{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport Input from '../input/Input';\nconst Search = props => {\n  const {\n    placeholder = '',\n    value,\n    prefixCls,\n    disabled,\n    onChange,\n    handleClear\n  } = props;\n  const handleChange = React.useCallback(e => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(e);\n    if (e.target.value === '') {\n      handleClear === null || handleClear === void 0 ? void 0 : handleClear();\n    }\n  }, [onChange]);\n  return /*#__PURE__*/React.createElement(Input, {\n    placeholder: placeholder,\n    className: prefixCls,\n    value: value,\n    onChange: handleChange,\n    disabled: disabled,\n    allowClear: true,\n    prefix: /*#__PURE__*/React.createElement(SearchOutlined, null)\n  });\n};\nif (process.env.NODE_ENV !== 'production') {\n  Search.displayName = 'Search';\n}\nexport default Search;", "map": {"version": 3, "names": ["React", "SearchOutlined", "Input", "Search", "props", "placeholder", "value", "prefixCls", "disabled", "onChange", "handleClear", "handleChange", "useCallback", "e", "target", "createElement", "className", "allowClear", "prefix", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/transfer/search.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport SearchOutlined from \"@ant-design/icons/es/icons/SearchOutlined\";\nimport Input from '../input/Input';\nconst Search = props => {\n  const {\n    placeholder = '',\n    value,\n    prefixCls,\n    disabled,\n    onChange,\n    handleClear\n  } = props;\n  const handleChange = React.useCallback(e => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(e);\n    if (e.target.value === '') {\n      handleClear === null || handleClear === void 0 ? void 0 : handleClear();\n    }\n  }, [onChange]);\n  return /*#__PURE__*/React.createElement(Input, {\n    placeholder: placeholder,\n    className: prefixCls,\n    value: value,\n    onChange: handleChange,\n    disabled: disabled,\n    allowClear: true,\n    prefix: /*#__PURE__*/React.createElement(SearchOutlined, null)\n  });\n};\nif (process.env.NODE_ENV !== 'production') {\n  Search.displayName = 'Search';\n}\nexport default Search;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,2CAA2C;AACtE,OAAOC,KAAK,MAAM,gBAAgB;AAClC,MAAMC,MAAM,GAAGC,KAAK,IAAI;EACtB,MAAM;IACJC,WAAW,GAAG,EAAE;IAChBC,KAAK;IACLC,SAAS;IACTC,QAAQ;IACRC,QAAQ;IACRC;EACF,CAAC,GAAGN,KAAK;EACT,MAAMO,YAAY,GAAGX,KAAK,CAACY,WAAW,CAACC,CAAC,IAAI;IAC1CJ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACI,CAAC,CAAC;IAC/D,IAAIA,CAAC,CAACC,MAAM,CAACR,KAAK,KAAK,EAAE,EAAE;MACzBI,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,CAAC;IACzE;EACF,CAAC,EAAE,CAACD,QAAQ,CAAC,CAAC;EACd,OAAO,aAAaT,KAAK,CAACe,aAAa,CAACb,KAAK,EAAE;IAC7CG,WAAW,EAAEA,WAAW;IACxBW,SAAS,EAAET,SAAS;IACpBD,KAAK,EAAEA,KAAK;IACZG,QAAQ,EAAEE,YAAY;IACtBH,QAAQ,EAAEA,QAAQ;IAClBS,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,aAAalB,KAAK,CAACe,aAAa,CAACd,cAAc,EAAE,IAAI;EAC/D,CAAC,CAAC;AACJ,CAAC;AACD,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzClB,MAAM,CAACmB,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAenB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}