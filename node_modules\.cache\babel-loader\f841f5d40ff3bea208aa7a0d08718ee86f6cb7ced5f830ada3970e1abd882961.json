{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet scene = null; // 添加scene全局变量\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm'\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 在组件顶部添加动画循环的引用\nconst animationFrameRef = useRef(null);\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '300px',\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n\n      // 暂时禁用其他视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n      controls.enabled = false;\n\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: modelCoords.x,\n        y: 300,\n        z: modelCoords.y\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: modelCoords.x,\n        y: 0,\n        z: modelCoords.y\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        cameraRef.current.lookAt(controls.target);\n      }).onComplete(() => {\n        controls.enabled = true;\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n      }).start();\n      console.log('切换到路口视角', {\n        路口名称: intersection.name,\n        目标相机位置: [modelCoords.x, 300, modelCoords.y],\n        目标控制点: [modelCoords.x, 0, modelCoords.y],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    try {\n      const messageData = JSON.parse(message.toString());\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        var _messageData$data;\n        console.log('收到RSM消息:', messageData);\n        const participants = ((_messageData$data = messageData.data) === null || _messageData$data === void 0 ? void 0 : _messageData$data.participants) || [];\n        const rsuid = messageData.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id = rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = preloadedModel.clone();\n              // 根据类型调整高度\n              const height = type === '3' ? 0.1 : 0.8; // 行人模型贴近地面\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 0.1 : 0.8, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        const bsmData = messageData.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n\n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n\n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n\n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);\n        }\n        return;\n      }\n\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n        scene.add(model);\n\n        // 在校园模型加载完成后初始化场景\n        await initializeScene();\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环函数\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      TWEEN.update(); // 确保在每帧更新 TWEEN\n      if (controls) {\n        controls.update();\n      }\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 清理函数\n    return () => {\n      var _containerRef$current;\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n      }\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.end();\n      }\n      window.removeEventListener('resize', handleResize);\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.removeChild(renderer.domElement);\n      renderer.dispose();\n\n      // 清理场景中的所有车辆模型\n      vehicleModels.forEach((model, id) => {\n        scene.remove(model);\n      });\n      vehicleModels.clear();\n\n      // 清理场景\n      while (scene.children.length > 0) {\n        scene.remove(scene.children[0]);\n      }\n      scene = null; // 重置scene\n      preloadedVehicleModel = null; // 重置预加载的模型\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u8DEF\\u53E3\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      options: intersectionsData.intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 756,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 771,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 773,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 783,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 772,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"jdGWqGlpLYHi6cjPE5JHj3ruDak=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    const [vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`)]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse(child => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n        // // 保留原始贴图\n        // if (child.material.map) {\n        //   newMaterial.map = child.material.map;\n        // }\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse(child => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse(child => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n    console.log('所有模型预加载成功');\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "Select", "intersectionsData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "scene", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "topics", "bsm", "rsm", "BASE_URL", "vehicleModels", "Map", "animationFrameRef", "CampusModel", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "intersectionSelectStyle", "top", "width", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "clone", "currentUp", "up", "Tween", "to", "x", "y", "z", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "Math", "PI", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "find", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "onComplete", "路口名称", "handleMqttMessage", "topic", "message", "error", "toString", "messageData", "JSON", "parse", "type", "_messageData$data", "participants", "data", "rsuid", "now", "Date", "for<PERSON>ach", "participant", "id", "partPtcId", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "model", "get", "newModel", "height", "set", "rotation", "add", "lastUpdate", "updateMatrix", "updateMatrixWorld", "CLEANUP_THRESHOLD", "currentIds", "Set", "map", "p", "modelData", "has", "remove", "delete", "bsmData", "newState", "partLong", "partLat", "newPosition", "Vector3", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "event", "payload", "stringify", "onerror", "onclose", "setTimeout", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "MeshStandardMaterial", "color", "metalness", "roughness", "envMapIntensity", "children", "length", "xhr", "loaded", "total", "toFixed", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "render", "handleResize", "aspect", "updateProjectionMatrix", "addEventListener", "setGlobalView", "_containerRef$current", "cancelAnimationFrame", "clearInterval", "end", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "clear", "style", "placeholder", "onChange", "options", "label", "size", "bordered", "dropdownStyle", "maxHeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "onClick", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "toArray", "新位置", "e", "vehicleGltf", "cyclistGltf", "peopleGltf", "all", "loadAsync", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet scene = null; // 添加scene全局变量\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm'\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 在组件顶部添加动画循环的引用\nconst animationFrameRef = useRef(null);\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '300px',\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      // 暂时禁用其他视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n      controls.enabled = false;\n\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ \n          x: modelCoords.x, \n          y: 300,           \n          z: modelCoords.y \n        }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ \n          x: modelCoords.x, \n          y: 0, \n          z: modelCoords.y \n        }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          cameraRef.current.lookAt(controls.target);\n        })\n        .onComplete(() => {\n          controls.enabled = true;\n          controls.minDistance = 50;\n          controls.maxDistance = 500;\n          controls.maxPolarAngle = Math.PI / 2.1;\n          controls.minPolarAngle = 0;\n          controls.update();\n        })\n        .start();\n\n      console.log('切换到路口视角', {\n        路口名称: intersection.name,\n        目标相机位置: [modelCoords.x, 300, modelCoords.y],\n        目标控制点: [modelCoords.x, 0, modelCoords.y],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    \n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    \n    try {\n      const messageData = JSON.parse(message.toString());\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        console.log('收到RSM消息:', messageData);\n        \n        const participants = messageData.data?.participants || [];\n        const rsuid = messageData.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id =  rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n          // 解析位置和状态信息\n          const state = {\n            longitude: parseFloat(participant.partPosLong),\n            latitude: parseFloat(participant.partPosLat),\n            speed: parseFloat(participant.partSpeed),\n            heading: parseFloat(participant.partHeading)\n          };\n          \n          const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n          \n          // 根据类型选择对应的预加载模型\n          let preloadedModel;\n          switch (type) {\n            case '1': // 机动车\n              preloadedModel = preloadedVehicleModel;\n              break;\n            case '2': // 非机动车\n              preloadedModel = preloadedCyclistModel;\n              break;\n            case '3': // 行人\n              preloadedModel = preloadedPeopleModel;\n              break;\n            default:\n              return; // 跳过未知类型\n          }\n          \n          // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n            // 创建新模型实例\n            const newModel = preloadedModel.clone();\n            // 根据类型调整高度\n            const height = type === '3' ? 0.1 : 0.8; // 行人模型贴近地面\n            newModel.position.set(modelPos.x, height, -modelPos.y);\n            newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            scene.add(newModel);\n            \n            vehicleModels.set(id, {\n              model: newModel,\n              lastUpdate: now,\n              type: type\n            });\n          } else if (model) {\n            // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 0.1 : 0.8, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n          }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        \n        const bsmData = messageData.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        \n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n          \n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n          \n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);\n        }\n        return;\n      }\n      \n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环函数\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      TWEEN.update(); // 确保在每帧更新 TWEEN\n      if (controls) {\n        controls.update();\n      }\n      renderer.render(scene, camera);\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 清理函数\n    return () => {\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n      }\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.end();\n      }\n      window.removeEventListener('resize', handleResize);\n      containerRef.current?.removeChild(renderer.domElement);\n      renderer.dispose();\n      \n      // 清理场景中的所有车辆模型\n      vehicleModels.forEach((model, id) => {\n        scene.remove(model);\n      });\n      vehicleModels.clear();\n      \n      // 清理场景\n      while(scene.children.length > 0) { \n        scene.remove(scene.children[0]); \n      }\n      \n      scene = null; // 重置scene\n      preloadedVehicleModel = null; // 重置预加载的模型\n    };\n  }, []);\n\n  return (\n    <>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择路口位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    const [vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([\n      loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/people.glb`)\n    ]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n        // // 保留原始贴图\n        // if (child.material.map) {\n        //   newMaterial.map = child.material.map;\n        // }\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    console.log('所有模型预加载成功');\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,MAAM;AAC7B,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE;IACNC,GAAG,EAAE,2BAA2B;IAChCC,GAAG,EAAE;EACP;AACF,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,MAAMC,iBAAiB,GAAGvC,MAAM,CAAC,IAAI,CAAC;AAEtC,MAAMwC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAG1C,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM2C,UAAU,GAAG3C,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM4C,SAAS,GAAG5C,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMwC,aAAa,GAAG7C,MAAM,CAAC,EAAE,CAAC;EAChC,MAAM8C,eAAe,GAAG9C,MAAM,CAAC,CAAC,CAAC;EACjC,MAAM+C,aAAa,GAAG/C,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACgD,eAAe,EAAEC,kBAAkB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC;IAC/CmD,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxD,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMyD,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG3E,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAAC4E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM6E,uBAAuB,GAAG;IAC9BnB,QAAQ,EAAE,OAAO;IACjBoB,GAAG,EAAE,MAAM;IACXlB,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BkB,KAAK,EAAE,OAAO;IACdjB,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMQ,kBAAkB,GAAGA,CAAA,KAAM;IAC/BxB,WAAW,CAAC,QAAQ,CAAC;IACrBpC,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAAC4D,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B1B,WAAW,CAAC,QAAQ,CAAC;IACrBpC,UAAU,GAAG,QAAQ;IAErB,IAAIsD,SAAS,CAACS,OAAO,IAAI9D,QAAQ,EAAE;MACjC;MACA,MAAM+D,UAAU,GAAGV,SAAS,CAACS,OAAO,CAACzB,QAAQ,CAAC2B,KAAK,CAAC,CAAC;MACrD,MAAMC,SAAS,GAAGZ,SAAS,CAACS,OAAO,CAACI,EAAE,CAACF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAIhF,KAAK,CAACmF,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCC,MAAM,CAACxF,KAAK,CAACyF,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvB,SAAS,CAACS,OAAO,CAACzB,QAAQ,CAACwC,IAAI,CAACd,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDe,KAAK,CAAC,CAAC;;MAEV;MACA,IAAI9F,KAAK,CAACmF,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAACxF,KAAK,CAACyF,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvB,SAAS,CAACS,OAAO,CAACI,EAAE,CAACW,IAAI,CAACZ,SAAS,CAAC;MACtC,CAAC,CAAC,CACDa,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAG/E,QAAQ,CAACgF,MAAM,CAAChB,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAIhF,KAAK,CAACmF,KAAK,CAACY,aAAa,CAAC,CAC3BX,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAACxF,KAAK,CAACyF,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACd5E,QAAQ,CAACgF,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACA1B,SAAS,CAACS,OAAO,CAACmB,MAAM,CAACjF,QAAQ,CAACgF,MAAM,CAAC;QACzChF,QAAQ,CAACkF,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACA9E,QAAQ,CAAC4D,OAAO,GAAG,IAAI;;MAEvB;MACA5D,QAAQ,CAACmF,WAAW,GAAG,EAAE;MACzBnF,QAAQ,CAACoF,WAAW,GAAG,GAAG;MAC1BpF,QAAQ,CAACqF,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;MACtCvF,QAAQ,CAACwF,aAAa,GAAG,CAAC;MAC1BxF,QAAQ,CAACkF,MAAM,CAAC,CAAC;MAEjBO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAG7G,iBAAiB,CAAC8G,aAAa,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKL,KAAK,CAAC;IAChF,IAAIC,YAAY,IAAI3C,SAAS,CAACS,OAAO,IAAI9D,QAAQ,EAAE;MACjDuD,uBAAuB,CAACyC,YAAY,CAAC;;MAErC;MACA,MAAMK,WAAW,GAAG/E,SAAS,CAACwC,OAAO,CAACwC,YAAY,CAChDC,UAAU,CAACP,YAAY,CAAClE,SAAS,CAAC,EAClCyE,UAAU,CAACP,YAAY,CAACjE,QAAQ,CAClC,CAAC;;MAED;MACAhC,UAAU,GAAG,cAAc;MAC3BoC,WAAW,CAAC,cAAc,CAAC;MAC3BnC,QAAQ,CAAC4D,OAAO,GAAG,KAAK;;MAExB;MACA,MAAMG,UAAU,GAAGV,SAAS,CAACS,OAAO,CAACzB,QAAQ,CAAC2B,KAAK,CAAC,CAAC;MACrD,MAAMC,SAAS,GAAGZ,SAAS,CAACS,OAAO,CAACI,EAAE,CAACF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAIhF,KAAK,CAACmF,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAAC;QACFC,CAAC,EAAEgC,WAAW,CAAChC,CAAC;QAChBC,CAAC,EAAE,GAAG;QACNC,CAAC,EAAE8B,WAAW,CAAC/B;MACjB,CAAC,EAAE,IAAI,CAAC,CACPE,MAAM,CAACxF,KAAK,CAACyF,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvB,SAAS,CAACS,OAAO,CAACzB,QAAQ,CAACwC,IAAI,CAACd,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDe,KAAK,CAAC,CAAC;;MAEV;MACA,IAAI9F,KAAK,CAACmF,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAACxF,KAAK,CAACyF,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvB,SAAS,CAACS,OAAO,CAACI,EAAE,CAACW,IAAI,CAACZ,SAAS,CAAC;MACtC,CAAC,CAAC,CACDa,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAG/E,QAAQ,CAACgF,MAAM,CAAChB,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAIhF,KAAK,CAACmF,KAAK,CAACY,aAAa,CAAC,CAC3BX,EAAE,CAAC;QACFC,CAAC,EAAEgC,WAAW,CAAChC,CAAC;QAChBC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE8B,WAAW,CAAC/B;MACjB,CAAC,EAAE,IAAI,CAAC,CACPE,MAAM,CAACxF,KAAK,CAACyF,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACd5E,QAAQ,CAACgF,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC1B,SAAS,CAACS,OAAO,CAACmB,MAAM,CAACjF,QAAQ,CAACgF,MAAM,CAAC;MAC3C,CAAC,CAAC,CACDwB,UAAU,CAAC,MAAM;QAChBxG,QAAQ,CAAC4D,OAAO,GAAG,IAAI;QACvB5D,QAAQ,CAACmF,WAAW,GAAG,EAAE;QACzBnF,QAAQ,CAACoF,WAAW,GAAG,GAAG;QAC1BpF,QAAQ,CAACqF,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;QACtCvF,QAAQ,CAACwF,aAAa,GAAG,CAAC;QAC1BxF,QAAQ,CAACkF,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;MAEVW,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBe,IAAI,EAAET,YAAY,CAACI,IAAI;QACvBT,MAAM,EAAE,CAACU,WAAW,CAAChC,CAAC,EAAE,GAAG,EAAEgC,WAAW,CAAC/B,CAAC,CAAC;QAC3CsB,KAAK,EAAE,CAACS,WAAW,CAAChC,CAAC,EAAE,CAAC,EAAEgC,WAAW,CAAC/B,CAAC,CAAC;QACxCuB,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMa,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC5C,IAAI,CAACxG,KAAK,EAAE;MACVqF,OAAO,CAACoB,KAAK,CAAC,QAAQ,CAAC;MACvB;IACF;IAEApB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBiB,KAAK;MACLC,OAAO,EAAEA,OAAO,CAACE,QAAQ,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACL,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;;MAElD;MACA,IAAIH,KAAK,KAAKtG,WAAW,CAACM,MAAM,CAACE,GAAG,IAAIkG,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAAA,IAAAC,iBAAA;QAClE1B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEqB,WAAW,CAAC;QAEpC,MAAMK,YAAY,GAAG,EAAAD,iBAAA,GAAAJ,WAAW,CAACM,IAAI,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBC,YAAY,KAAI,EAAE;QACzD,MAAME,KAAK,GAAGP,WAAW,CAACM,IAAI,CAACC,KAAK;;QAEpC;QACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;QAEtB;QACAH,YAAY,CAACK,OAAO,CAACC,WAAW,IAAI;UAClC;UACA,MAAMC,EAAE,GAAIL,KAAK,GAAGI,WAAW,CAACE,SAAS;UACzC,MAAMV,IAAI,GAAGQ,WAAW,CAACG,WAAW;UAEpC,IAAGX,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACA;YACA,MAAMY,KAAK,GAAG;cACZhG,SAAS,EAAEyE,UAAU,CAACmB,WAAW,CAACK,WAAW,CAAC;cAC9ChG,QAAQ,EAAEwE,UAAU,CAACmB,WAAW,CAACM,UAAU,CAAC;cAC5ChG,KAAK,EAAEuE,UAAU,CAACmB,WAAW,CAACO,SAAS,CAAC;cACxChG,OAAO,EAAEsE,UAAU,CAACmB,WAAW,CAACQ,WAAW;YAC7C,CAAC;YAED,MAAMC,QAAQ,GAAG7G,SAAS,CAACwC,OAAO,CAACwC,YAAY,CAACwB,KAAK,CAAChG,SAAS,EAAEgG,KAAK,CAAC/F,QAAQ,CAAC;;YAEhF;YACA,IAAIqG,cAAc;YAClB,QAAQlB,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRkB,cAAc,GAAGnI,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRmI,cAAc,GAAGlI,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRkI,cAAc,GAAGjI,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACA,IAAIkI,KAAK,GAAGtH,aAAa,CAACuH,GAAG,CAACX,EAAE,CAAC;YAEjC,IAAI,CAACU,KAAK,IAAID,cAAc,EAAE;cAC5B;cACA,MAAMG,QAAQ,GAAGH,cAAc,CAACpE,KAAK,CAAC,CAAC;cACvC;cACA,MAAMwE,MAAM,GAAGtB,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;cACzCqB,QAAQ,CAAClG,QAAQ,CAACoG,GAAG,CAACN,QAAQ,CAAC9D,CAAC,EAAEmE,MAAM,EAAE,CAACL,QAAQ,CAAC7D,CAAC,CAAC;cACtDiE,QAAQ,CAACG,QAAQ,CAACpE,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGuC,KAAK,CAAC7F,OAAO,GAAGqD,IAAI,CAACC,EAAE,GAAG,GAAG;cAC7DnF,KAAK,CAACuI,GAAG,CAACJ,QAAQ,CAAC;cAEnBxH,aAAa,CAAC0H,GAAG,CAACd,EAAE,EAAE;gBACpBU,KAAK,EAAEE,QAAQ;gBACfK,UAAU,EAAErB,GAAG;gBACfL,IAAI,EAAEA;cACR,CAAC,CAAC;YACJ,CAAC,MAAM,IAAImB,KAAK,EAAE;cAChB;cACAA,KAAK,CAACA,KAAK,CAAChG,QAAQ,CAACoG,GAAG,CAACN,QAAQ,CAAC9D,CAAC,EAAEgE,KAAK,CAACnB,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAACiB,QAAQ,CAAC7D,CAAC,CAAC;cACjF+D,KAAK,CAACA,KAAK,CAACK,QAAQ,CAACpE,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGuC,KAAK,CAAC7F,OAAO,GAAGqD,IAAI,CAACC,EAAE,GAAG,GAAG;cAChE8C,KAAK,CAACO,UAAU,GAAGrB,GAAG;cACtBc,KAAK,CAACA,KAAK,CAACQ,YAAY,CAAC,CAAC;cAC1BR,KAAK,CAACA,KAAK,CAACS,iBAAiB,CAAC,IAAI,CAAC;YACrC;UACA;QACF,CAAC,CAAC;;QAEF;QACA,MAAMC,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAC7B,YAAY,CAAC8B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACvB,SAAS,CAAC,CAAC;QAE9D7G,aAAa,CAAC0G,OAAO,CAAC,CAAC2B,SAAS,EAAEzB,EAAE,KAAK;UACvC,IAAIJ,GAAG,GAAG6B,SAAS,CAACR,UAAU,GAAGG,iBAAiB,IAAI,CAACC,UAAU,CAACK,GAAG,CAAC1B,EAAE,CAAC,EAAE;YACzEvH,KAAK,CAACkJ,MAAM,CAACF,SAAS,CAACf,KAAK,CAAC;YAC7BtH,aAAa,CAACwI,MAAM,CAAC5B,EAAE,CAAC;YACxBlC,OAAO,CAACC,GAAG,CAAC,oBAAoBiC,EAAE,QAAQyB,SAAS,CAAClC,IAAI,EAAE,CAAC;UAC7D;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAIP,KAAK,KAAKtG,WAAW,CAACM,MAAM,CAACC,GAAG,IAAImG,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAClEzB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEqB,WAAW,CAAC;QAEpC,MAAMyC,OAAO,GAAGzC,WAAW,CAACM,IAAI;QAChC,MAAMoC,QAAQ,GAAG;UACf3H,SAAS,EAAEyE,UAAU,CAACiD,OAAO,CAACE,QAAQ,CAAC;UACvC3H,QAAQ,EAAEwE,UAAU,CAACiD,OAAO,CAACG,OAAO,CAAC;UACrC3H,KAAK,EAAEuE,UAAU,CAACiD,OAAO,CAACvB,SAAS,CAAC;UACpChG,OAAO,EAAEsE,UAAU,CAACiD,OAAO,CAACtB,WAAW;QACzC,CAAC;QAEDzC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE+D,QAAQ,CAAC;;QAElC;QACA,IAAIjK,gBAAgB,EAAE;UACpB,MAAM2I,QAAQ,GAAG7G,SAAS,CAACwC,OAAO,CAACwC,YAAY,CAACmD,QAAQ,CAAC3H,SAAS,EAAE2H,QAAQ,CAAC1H,QAAQ,CAAC;UACtF,MAAM6H,WAAW,GAAG,IAAIhL,KAAK,CAACiL,OAAO,CAAC1B,QAAQ,CAAC9D,CAAC,EAAE,GAAG,EAAE,CAAC8D,QAAQ,CAAC7D,CAAC,CAAC;;UAEnE;UACA9E,gBAAgB,CAAC6C,QAAQ,CAACwC,IAAI,CAAC+E,WAAW,CAAC;UAC3CpK,gBAAgB,CAACkJ,QAAQ,CAACpE,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGkE,QAAQ,CAACxH,OAAO,GAAGqD,IAAI,CAACC,EAAE,GAAG,GAAG;UACxE/F,gBAAgB,CAACqJ,YAAY,CAAC,CAAC;UAC/BrJ,gBAAgB,CAACsJ,iBAAiB,CAAC,IAAI,CAAC;;UAExC;UACAjH,eAAe,CAAC4H,QAAQ,CAAC;UACzBhE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEkE,WAAW,CAAC;QACtC;QACA;MACF;;MAEA;MACAnE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBiB,KAAK;QACLO,IAAI,EAAEH,WAAW,CAACG,IAAI;QACtBG,IAAI,EAAEN;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCpB,OAAO,CAACoB,KAAK,CAAC,SAAS,EAAED,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMgD,cAAc,GAAGA,CAAA,KAAM;IAC3BrE,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAMqE,KAAK,GAAG,QAAQ1J,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnE+E,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEqE,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBzE,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAEDsE,EAAE,CAACG,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMxD,OAAO,GAAGI,IAAI,CAACC,KAAK,CAACmD,KAAK,CAAC/C,IAAI,CAAC;;QAEtC;QACA,IAAIT,OAAO,CAACM,IAAI,KAAK,SAAS,EAAE;UAC9BzB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEkB,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACM,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIN,OAAO,CAACM,IAAI,KAAK,SAAS,IAAIN,OAAO,CAACD,KAAK,IAAIC,OAAO,CAACyD,OAAO,EAAE;UAClE;UACA3D,iBAAiB,CAACE,OAAO,CAACD,KAAK,EAAEK,IAAI,CAACsD,SAAS,CAAC1D,OAAO,CAACyD,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOxD,KAAK,EAAE;QACdpB,OAAO,CAACoB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDmD,EAAE,CAACO,OAAO,GAAI1D,KAAK,IAAK;MACtBpB,OAAO,CAACoB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDmD,EAAE,CAACQ,OAAO,GAAG,MAAM;MACjB/E,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACA+E,UAAU,CAACX,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACArI,aAAa,CAACqC,OAAO,GAAGkG,EAAE;EAC5B,CAAC;EAEDvL,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2C,YAAY,CAAC0C,OAAO,EAAE;;IAE3B;IACA4G,aAAa,CAAC,CAAC;;IAEf;IACAtK,KAAK,GAAG,IAAIxB,KAAK,CAAC+L,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAIhM,KAAK,CAACiM,iBAAiB,CACxC,EAAE,EACFtK,MAAM,CAACuK,UAAU,GAAGvK,MAAM,CAACwK,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAACvI,QAAQ,CAACoG,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCmC,MAAM,CAAC3F,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB5B,SAAS,CAACS,OAAO,GAAG8G,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAIpM,KAAK,CAACqM,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAAC5K,MAAM,CAACuK,UAAU,EAAEvK,MAAM,CAACwK,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAAC9K,MAAM,CAAC+K,gBAAgB,CAAC;IAC/ClK,YAAY,CAAC0C,OAAO,CAACyH,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAI7M,KAAK,CAAC8M,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5DtL,KAAK,CAACuI,GAAG,CAAC8C,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAI/M,KAAK,CAACgN,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAACtJ,QAAQ,CAACoG,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1CrI,KAAK,CAACuI,GAAG,CAACgD,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAIjN,KAAK,CAACgN,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAACxJ,QAAQ,CAACoG,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3CrI,KAAK,CAACuI,GAAG,CAACkD,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAIlN,KAAK,CAACmN,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAACzJ,QAAQ,CAACoG,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCqD,SAAS,CAACE,KAAK,GAAG1G,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7BuG,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxB/L,KAAK,CAACuI,GAAG,CAACmD,SAAS,CAAC;;IAEpB;IACA9L,QAAQ,GAAG,IAAIlB,aAAa,CAAC8L,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzDxL,QAAQ,CAACoM,aAAa,GAAG,IAAI;IAC7BpM,QAAQ,CAACqM,aAAa,GAAG,IAAI;IAC7BrM,QAAQ,CAACsM,kBAAkB,GAAG,KAAK;IACnCtM,QAAQ,CAACmF,WAAW,GAAG,EAAE;IACzBnF,QAAQ,CAACoF,WAAW,GAAG,GAAG;IAC1BpF,QAAQ,CAACqF,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IACtCvF,QAAQ,CAACwF,aAAa,GAAG,CAAC;IAC1BxF,QAAQ,CAACgF,MAAM,CAACyD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BzI,QAAQ,CAACkF,MAAM,CAAC,CAAC;;IAEjB;IACAO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBkF,MAAM,EAAE,CAAC,CAACA,MAAM;MAChB5K,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBqD,SAAS,EAAE,CAAC,CAACA,SAAS,CAACS;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMyI,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAI9N,UAAU,CAAC,CAAC;QACtC8N,aAAa,CAACC,IAAI,CAChB,GAAG9L,QAAQ,uBAAuB,EACjC+L,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAACzM,KAAK;;UAE/B;UACA,MAAM2M,gBAAgB,GAAG,IAAInO,KAAK,CAACoO,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACG,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAIzO,KAAK,CAAC0O,oBAAoB,CAAC;kBACjDC,KAAK,EAAE,QAAQ;kBAAO;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIR,KAAK,CAACE,QAAQ,CAAClE,GAAG,EAAE;kBACtBmE,WAAW,CAACnE,GAAG,GAAGgE,KAAK,CAACE,QAAQ,CAAClE,GAAG;gBACtC;;gBAEA;gBACAgE,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5B5H,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEwH,KAAK,CAAC9G,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAM0G,YAAY,CAACa,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMV,KAAK,GAAGJ,YAAY,CAACa,QAAQ,CAAC,CAAC,CAAC;YACtCZ,gBAAgB,CAACpE,GAAG,CAACuE,KAAK,CAAC;UAC7B;;UAEA;UACA9M,KAAK,CAACuI,GAAG,CAACoE,gBAAgB,CAAC;;UAE3B;UACAvN,gBAAgB,GAAGuN,gBAAgB;UAEnCtH,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9B/D,kBAAkB,CAAC,IAAI,CAAC;UACxB8K,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAc,GAAG,IAAK;UACPpI,OAAO,CAACC,GAAG,CAAC,aAAa,CAACmI,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDtB,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMuB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMlB,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACAzC,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAIiD,gBAAgB,EAAE;UACpB,MAAMmB,YAAY,GAAG;YACnBpM,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UAED,MAAMkM,UAAU,GAAG7M,SAAS,CAACwC,OAAO,CAACwC,YAAY,CAAC4H,YAAY,CAACpM,SAAS,EAAEoM,YAAY,CAACnM,QAAQ,CAAC;UAChG;UACAgL,gBAAgB,CAAC1K,QAAQ,CAACoG,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACxCsE,gBAAgB,CAACrE,QAAQ,CAACpE,CAAC,GAAIgB,IAAI,CAACC,EAAE,GAAG2I,YAAY,CAACjM,OAAO,GAAGqD,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9EwH,gBAAgB,CAAClE,YAAY,CAAC,CAAC;UAC/BkE,gBAAgB,CAACjE,iBAAiB,CAAC,IAAI,CAAC;UACxCjJ,eAAe,GAAGkN,gBAAgB,CAAC1K,QAAQ,CAAC2B,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAO6C,KAAK,EAAE;QACdpB,OAAO,CAACoB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAMuH,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAI9B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAM6B,WAAW,GAAIC,WAAW,IAAK;UACnC/I,OAAO,CAACC,GAAG,CAAC,WAAW2I,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAI5P,UAAU,CAAC,CAAC;UAC/B4P,MAAM,CAAC7B,IAAI,CACTyB,GAAG,EACFxB,IAAI,IAAK;YACRpH,OAAO,CAACC,GAAG,CAAC,WAAW2I,GAAG,EAAE,CAAC;YAC7B5B,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAgB,GAAG,IAAK;YACPpI,OAAO,CAACC,GAAG,CAAC,SAAS,CAACmI,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAnH,KAAK,IAAK;YACTpB,OAAO,CAACoB,KAAK,CAAC,SAASwH,GAAG,EAAE,EAAExH,KAAK,CAAC;YACpC,IAAI2H,WAAW,GAAG,CAAC,EAAE;cACnB/I,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3B+E,UAAU,CAAC,MAAM8D,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACL9B,MAAM,CAAC7F,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAED0H,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAI5P,UAAU,CAAC,CAAC;IAC/B4P,MAAM,CAAC7B,IAAI,CACT,GAAG9L,QAAQ,4BAA4B,EACvC,MAAO+L,IAAI,IAAK;MACd,IAAI;QACF,MAAMxE,KAAK,GAAGwE,IAAI,CAACzM,KAAK;QACxBiI,KAAK,CAACqG,KAAK,CAACjG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBJ,KAAK,CAAChG,QAAQ,CAACoG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3BrI,KAAK,CAACuI,GAAG,CAACN,KAAK,CAAC;;QAEhB;QACA,MAAM4F,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC,OAAOpH,KAAK,EAAE;QACdpB,OAAO,CAACoB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACAgH,GAAG,IAAK;MACPpI,OAAO,CAACC,GAAG,CAAC,SAAS,CAACmI,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAnH,KAAK,IAAK;MACTpB,OAAO,CAACoB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpB,OAAO,CAACoB,KAAK,CAAC,OAAO,EAAE;QACrB8H,IAAI,EAAE9H,KAAK,CAACK,IAAI;QAChB0H,IAAI,EAAE/H,KAAK,CAACD,OAAO;QACnBiI,KAAK,EAAE,GAAG/N,QAAQ,4BAA4B;QAC9CgO,KAAK,EAAE,GAAGhO,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMiO,OAAO,GAAGA,CAAA,KAAM;MACpB9N,iBAAiB,CAAC6C,OAAO,GAAGkL,qBAAqB,CAACD,OAAO,CAAC;MAC1D/P,KAAK,CAACkG,MAAM,CAAC,CAAC,CAAC,CAAC;MAChB,IAAIlF,QAAQ,EAAE;QACZA,QAAQ,CAACkF,MAAM,CAAC,CAAC;MACnB;MACA8F,QAAQ,CAACiE,MAAM,CAAC7O,KAAK,EAAEwK,MAAM,CAAC;IAChC,CAAC;IAEDmE,OAAO,CAAC,CAAC;;IAET;IACA,MAAMG,YAAY,GAAGA,CAAA,KAAM;MACzBtE,MAAM,CAACuE,MAAM,GAAG5O,MAAM,CAACuK,UAAU,GAAGvK,MAAM,CAACwK,WAAW;MACtDH,MAAM,CAACwE,sBAAsB,CAAC,CAAC;MAC/BpE,QAAQ,CAACG,OAAO,CAAC5K,MAAM,CAACuK,UAAU,EAAEvK,MAAM,CAACwK,WAAW,CAAC;IACzD,CAAC;IACDxK,MAAM,CAAC8O,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;;IAE/C;IACA3O,MAAM,CAAC+O,aAAa,GAAG,MAAM;MAC3B,IAAIjM,SAAS,CAACS,OAAO,EAAE;QACrBT,SAAS,CAACS,OAAO,CAACzB,QAAQ,CAACoG,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzCpF,SAAS,CAACS,OAAO,CAACmB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC5B,SAAS,CAACS,OAAO,CAAC+E,YAAY,CAAC,CAAC;QAChCxF,SAAS,CAACS,OAAO,CAACgF,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAI9I,QAAQ,EAAE;UACZA,QAAQ,CAACgF,MAAM,CAACyD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BzI,QAAQ,CAAC4D,OAAO,GAAG,IAAI;UACvB5D,QAAQ,CAACkF,MAAM,CAAC,CAAC;QACnB;QAEAnF,UAAU,GAAG,QAAQ;QACrB0F,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MAAA,IAAA6J,qBAAA;MACX,IAAItO,iBAAiB,CAAC6C,OAAO,EAAE;QAC7B0L,oBAAoB,CAACvO,iBAAiB,CAAC6C,OAAO,CAAC;MACjD;MACA,IAAInE,oBAAoB,EAAE;QACxB8P,aAAa,CAAC9P,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;MACA,IAAI8B,aAAa,CAACqC,OAAO,EAAE;QACzBrC,aAAa,CAACqC,OAAO,CAAC4L,GAAG,CAAC,CAAC;MAC7B;MACAnP,MAAM,CAACoP,mBAAmB,CAAC,QAAQ,EAAET,YAAY,CAAC;MAClD,CAAAK,qBAAA,GAAAnO,YAAY,CAAC0C,OAAO,cAAAyL,qBAAA,uBAApBA,qBAAA,CAAsBK,WAAW,CAAC5E,QAAQ,CAACQ,UAAU,CAAC;MACtDR,QAAQ,CAAC6E,OAAO,CAAC,CAAC;;MAElB;MACA9O,aAAa,CAAC0G,OAAO,CAAC,CAACY,KAAK,EAAEV,EAAE,KAAK;QACnCvH,KAAK,CAACkJ,MAAM,CAACjB,KAAK,CAAC;MACrB,CAAC,CAAC;MACFtH,aAAa,CAAC+O,KAAK,CAAC,CAAC;;MAErB;MACA,OAAM1P,KAAK,CAACuN,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QAC/BxN,KAAK,CAACkJ,MAAM,CAAClJ,KAAK,CAACuN,QAAQ,CAAC,CAAC,CAAC,CAAC;MACjC;MAEAvN,KAAK,GAAG,IAAI,CAAC,CAAC;MACdH,qBAAqB,GAAG,IAAI,CAAC,CAAC;IAChC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEZ,OAAA,CAAAE,SAAA;IAAAoO,QAAA,gBACEtO,OAAA,CAACH,MAAM;MACL6Q,KAAK,EAAEvM,uBAAwB;MAC/BwM,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAEnK,wBAAyB;MACnCoK,OAAO,EAAE/Q,iBAAiB,CAAC8G,aAAa,CAACiD,GAAG,CAAClD,YAAY,KAAK;QAC5DD,KAAK,EAAEC,YAAY,CAACI,IAAI;QACxB+J,KAAK,EAAEnK,YAAY,CAACI;MACtB,CAAC,CAAC,CAAE;MACJgK,IAAI,EAAC,OAAO;MACZC,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACb7N,MAAM,EAAE,IAAI;QACZ8N,SAAS,EAAE;MACb;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACFtR,OAAA;MAAKuR,GAAG,EAAExP,YAAa;MAAC2O,KAAK,EAAE;QAAErM,KAAK,EAAE,MAAM;QAAE8E,MAAM,EAAE;MAAO;IAAE;MAAAgI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpEtR,OAAA;MAAK0Q,KAAK,EAAE3N,oBAAqB;MAAAuL,QAAA,gBAC/BtO,OAAA;QACE0Q,KAAK,EAAE;UACL,GAAGnN,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EqL,KAAK,EAAErL,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF2O,OAAO,EAAElN,kBAAmB;QAAAgK,QAAA,EAC7B;MAED;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtR,OAAA;QACE0Q,KAAK,EAAE;UACL,GAAGnN,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EqL,KAAK,EAAErL,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF2O,OAAO,EAAEhN,kBAAmB;QAAA8J,QAAA,EAC7B;MAED;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAAxP,EAAA,CA/uBMD,WAAW;AAAA4P,EAAA,GAAX5P,WAAW;AAgvBjB,SAAS6P,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAACvN,KAAK,GAAG,GAAG;EAClBuN,MAAM,CAACzI,MAAM,GAAG,EAAE;;EAElB;EACA4I,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAACvN,KAAK,GAAC,CAAC,EAAEuN,MAAM,CAACzI,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAMkJ,OAAO,GAAG,IAAI9S,KAAK,CAAC+S,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAIhT,KAAK,CAACiT,cAAc,CAAC;IAC9C3I,GAAG,EAAEwI,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAInT,KAAK,CAACoT,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAACrD,KAAK,CAACjG,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAOsJ,MAAM;AACf;;AAEA;AACAxR,MAAM,CAAC0R,WAAW,GAAG,CAAC5N,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EAChC,IAAI/E,gBAAgB,EAAE;IACpBA,gBAAgB,CAAC6C,QAAQ,CAACoG,GAAG,CAACpE,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACtC/E,gBAAgB,CAACqJ,YAAY,CAAC,CAAC;IAC/BrJ,gBAAgB,CAACsJ,iBAAiB,CAAC,IAAI,CAAC;IACxCrD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAACrB,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACAhE,MAAM,CAAC2R,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMtH,MAAM,GAAGsG,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAIzH,MAAM,EAAE;MACV;MACA,MAAM0H,MAAM,GAAG1H,MAAM,CAACvI,QAAQ,CAAC2B,KAAK,CAAC,CAAC;;MAEtC;MACA4G,MAAM,CAACvI,QAAQ,CAACoG,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BmC,MAAM,CAAC1G,EAAE,CAACuE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBmC,MAAM,CAAC3F,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACA2F,MAAM,CAAC/B,YAAY,CAAC,CAAC;MACrB+B,MAAM,CAAC9B,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAM9I,QAAQ,GAAGkR,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIvS,QAAQ,EAAE;QACZA,QAAQ,CAACgF,MAAM,CAACyD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BzI,QAAQ,CAACkF,MAAM,CAAC,CAAC;MACnB;MAEAO,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxB8M,GAAG,EAAEF,MAAM,CAACG,OAAO,CAAC,CAAC;QACrBC,GAAG,EAAE9H,MAAM,CAACvI,QAAQ,CAACoQ,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOE,CAAC,EAAE;IACVlN,OAAO,CAACoB,KAAK,CAAC,YAAY,EAAE8L,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAMjI,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACFjF,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAM+I,MAAM,GAAG,IAAI5P,UAAU,CAAC,CAAC;;IAE/B;IACA,MAAM,CAAC+T,WAAW,EAAEC,WAAW,EAAEC,UAAU,CAAC,GAAG,MAAMtG,OAAO,CAACuG,GAAG,CAAC,CAC/DtE,MAAM,CAACuE,SAAS,CAAC,GAAGlS,QAAQ,uBAAuB,CAAC,EACpD2N,MAAM,CAACuE,SAAS,CAAC,GAAGlS,QAAQ,uBAAuB,CAAC,EACpD2N,MAAM,CAACuE,SAAS,CAAC,GAAGlS,QAAQ,sBAAsB,CAAC,CACpD,CAAC;;IAEF;IACAb,qBAAqB,GAAG2S,WAAW,CAACxS,KAAK;IACzCH,qBAAqB,CAACgN,QAAQ,CAAEC,KAAK,IAAK;MACxC,IAAIA,KAAK,CAACC,MAAM,EAAE;QAChBD,KAAK,CAACE,QAAQ,GAAG,IAAIxO,KAAK,CAAC0O,oBAAoB,CAAC;UAC9CC,KAAK,EAAE,QAAQ;UACfC,SAAS,EAAE,GAAG;UACdC,SAAS,EAAE,GAAG;UACdC,eAAe,EAAE;QACnB,CAAC,CAAC;;QAEF;QACA;QACA;QACA;MACF;IACF,CAAC,CAAC;;IAEF;IACAxN,qBAAqB,GAAG2S,WAAW,CAACzS,KAAK;IACzC;IACAF,qBAAqB,CAACwO,KAAK,CAACjG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxC;IACAvI,qBAAqB,CAAC+M,QAAQ,CAAEC,KAAK,IAAK;MACxC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClC;QACAF,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;QAC9BN,KAAK,CAACE,QAAQ,CAACK,SAAS,GAAG,GAAG;QAC9BP,KAAK,CAACE,QAAQ,CAACM,eAAe,GAAG,GAAG;MACtC;IACF,CAAC,CAAC;;IAEF;IACAvN,oBAAoB,GAAG2S,UAAU,CAAC1S,KAAK;IACvC;IACAD,oBAAoB,CAACuO,KAAK,CAACjG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvC;IACAtI,oBAAoB,CAAC8M,QAAQ,CAAEC,KAAK,IAAK;MACvC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClC;QACAF,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;QAC9BN,KAAK,CAACE,QAAQ,CAACK,SAAS,GAAG,GAAG;QAC9BP,KAAK,CAACE,QAAQ,CAACM,eAAe,GAAG,GAAG;MACtC;IACF,CAAC,CAAC;IAEFjI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,CAAC,OAAOmB,KAAK,EAAE;IACdpB,OAAO,CAACoB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;AAED,eAAe3F,WAAW;AAAC,IAAA4P,EAAA;AAAAmC,YAAA,CAAAnC,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}