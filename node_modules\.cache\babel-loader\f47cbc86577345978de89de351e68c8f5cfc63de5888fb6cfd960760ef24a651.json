{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FileAddFilledSvg from \"@ant-design/icons-svg/es/asn/FileAddFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FileAddFilled = function FileAddFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FileAddFilledSvg\n  }));\n};\n\n/**![file-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4MCA1ODBIMzcyYTggOCAwIDAwLTggOHY0OGE4IDggMCAwMDggOGgxMDh2MTA4YTggOCAwIDAwOCA4aDQ4YTggOCAwIDAwOC04VjY0NGgxMDhhOCA4IDAgMDA4LTh2LTQ4YTggOCAwIDAwLTgtOEg1NDRWNDcyYTggOCAwIDAwLTgtOGgtNDhhOCA4IDAgMDAtOCA4djEwOHptMzc0LjYtMjkxLjNjNiA2IDkuNCAxNC4xIDkuNCAyMi42VjkyOGMwIDE3LjctMTQuMyAzMi0zMiAzMkgxOTJjLTE3LjcgMC0zMi0xNC4zLTMyLTMyVjk2YzAtMTcuNyAxNC4zLTMyIDMyLTMyaDQyNC43YzguNSAwIDE2LjcgMy40IDIyLjcgOS40bDIxNS4yIDIxNS4zek03OTAuMiAzMjZMNjAyIDEzNy44VjMyNmgxODguMnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FileAddFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FileAddFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "FileAddFilledSvg", "AntdIcon", "FileAddFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@ant-design/icons/es/icons/FileAddFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FileAddFilledSvg from \"@ant-design/icons-svg/es/asn/FileAddFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FileAddFilled = function FileAddFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FileAddFilledSvg\n  }));\n};\n\n/**![file-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4MCA1ODBIMzcyYTggOCAwIDAwLTggOHY0OGE4IDggMCAwMDggOGgxMDh2MTA4YTggOCAwIDAwOCA4aDQ4YTggOCAwIDAwOC04VjY0NGgxMDhhOCA4IDAgMDA4LTh2LTQ4YTggOCAwIDAwLTgtOEg1NDRWNDcyYTggOCAwIDAwLTgtOGgtNDhhOCA4IDAgMDAtOCA4djEwOHptMzc0LjYtMjkxLjNjNiA2IDkuNCAxNC4xIDkuNCAyMi42VjkyOGMwIDE3LjctMTQuMyAzMi0zMiAzMkgxOTJjLTE3LjcgMC0zMi0xNC4zLTMyLTMyVjk2YzAtMTcuNyAxNC4zLTMyIDMyLTMyaDQyNC43YzguNSAwIDE2LjcgMy40IDIyLjcgOS40bDIxNS4yIDIxNS4zek03OTAuMiAzMjZMNjAyIDEzNy44VjMyNmgxODguMnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FileAddFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FileAddFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,4CAA4C;AACzE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACrD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,aAAa,CAAC;AAC1D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,eAAe;AACvC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}