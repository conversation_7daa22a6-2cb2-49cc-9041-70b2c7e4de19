import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { CoordinateConverter } from '../utils/CoordinateConverter';
import * as TWEEN from '@tweenjs/tween.js';
import * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js';


import mqtt from 'mqtt';
import { Select, Popover } from 'antd';
import axios from 'axios'; // 新增 axios 导入
import VideoPlayer from './VideoPlayer'; // 引入摄像头视频播放组件
import DevicePopoverContent from './DevicePopoverContent';
// 全局变量
let globalVehicleRef = null;
let globalTrajectory = [];
let currentPointIndex = 0;
let globalUpdateInterval = null;
let targetPosition = null;  // 新增：目标位置
let currentPosition = null; // 新增：当前位置
let isMoving = false;      // 新增：移动状态标志
let cameraMode = 'global'; // 'global' 或 'follow'
let controls = null; // 保存 controls 的引用

// 添加全局变量来存储预加载的车辆模型
let preloadedVehicleModel = null;
let preloadedCyclistModel = null;  // 新增：非机动车模型
let preloadedPeopleModel = null;   // 新增：行人模型
let preloadedTrafficLightModel = null; // 新增：红绿灯模型
let scene = null; // 添加scene全局变量

let peopleBaseModel= null; // 存储原始模型数据
let skeleton =null;

// 添加滤波相关的变量
let lastPosition = null;
let lastRotation = null;
const ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）

// 为每个车辆单独存储上一次的位置和方向
const vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置
const vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度




// MQTT配置
const MQTT_CONFIG = {
  broker: window.location.hostname,
  port: 8083,
  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中
    bsm: 'changli/cloud/v2x/obu/bsm',
    rsm: 'changli/cloud/v2x/rsu/rsm',
    scene: 'changli/cloud/v2x/obu/scene',
  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题
  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题
};

// 修改所有资源的基础URL
const BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';
console.log('API_URLxxxx:', process.env.REACT_APP_API_URL);

// 添加全局变量来存储所有车辆
const vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型

// 添加全局变量来存储设备时间戳缓存
let deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳

// 添加全局变量来存储主车的bsmId
let mainVehicleBsmId = null;

// 添加红绿灯相关的全局变量
let trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射
let trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射

const clock = new THREE.Clock();

// 添加获取主车bsmId的函数
const fetchMainVehicleBsmId = async () => {
  try {
    const response = await fetch(`${BASE_URL}/api/vehicles/list`);
    const data = await response.json();

    if (data && data.vehicles && Array.isArray(data.vehicles)) {
      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);
      if (mainVehicle && mainVehicle.bsmId) {
        mainVehicleBsmId = mainVehicle.bsmId;
        console.log('获取主车bsmId成功:', mainVehicleBsmId);
        return mainVehicleBsmId;
      }
    }
    console.log('未找到主车，使用默认值 BSM01');
    mainVehicleBsmId = 'BSM01'; // 默认值
    return mainVehicleBsmId;
  } catch (error) {
    console.error('获取主车信息失败:', error);
    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值
    return mainVehicleBsmId;
  }
};

// 添加滤波器函数
const lowPassFilter = (newValue, lastValue, alpha) => {
  if (lastValue === null) return newValue;
  return alpha * newValue + (1 - alpha) * lastValue;
};

// 修改位置滤波函数，针对特定车辆ID进行滤波
const filterPosition = (newPos, vehicleId) => {
  // 如果没有提供车辆ID，使用全局变量（用于主车）
  if (!vehicleId) {
  if (!lastPosition) {
    lastPosition = newPos.clone();
    return newPos;
  }

  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);
  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);
  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);

  lastPosition.set(filteredX, filteredY, filteredZ);
  return lastPosition.clone();
  }

  // 针对特定车辆ID的滤波
  if (!vehicleLastPositions.has(vehicleId)) {
    vehicleLastPositions.set(vehicleId, newPos.clone());
    return newPos;
  }

  const lastPos = vehicleLastPositions.get(vehicleId);

  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置
  const distance = lastPos.distanceTo(newPos);
  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变

  if (distance > MAX_DISTANCE_THRESHOLD) {
    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);
    vehicleLastPositions.set(vehicleId, newPos.clone());
    return newPos;
  }

  // 正常滤波处理
  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);
  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);
  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);

  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);
  vehicleLastPositions.set(vehicleId, filteredPos.clone());

  return filteredPos;
};

// 修改朝向滤波函数，针对特定车辆ID进行滤波
const filterRotation = (newRotation, vehicleId) => {
  // 如果没有提供车辆ID，使用全局变量（用于主车）
  if (!vehicleId) {
  if (lastRotation === null) {
    lastRotation = newRotation;
    return newRotation;
  }

  // 处理角度跳变（从360度到0度或反之）
  let diff = newRotation - lastRotation;
  if (diff > Math.PI) diff -= 2 * Math.PI;
  if (diff < -Math.PI) diff += 2 * Math.PI;

  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);
  lastRotation = filteredRotation;
    return filteredRotation;
  }

  // 针对特定车辆ID的滤波
  if (!vehicleLastRotations.has(vehicleId)) {
    vehicleLastRotations.set(vehicleId, newRotation);
    return newRotation;
  }

  const lastRot = vehicleLastRotations.get(vehicleId);

  // 处理角度跳变（从360度到0度或反之）
  let diff = newRotation - lastRot;
  if (diff > Math.PI) diff -= 2 * Math.PI;
  if (diff < -Math.PI) diff += 2 * Math.PI;

  // 检查是否是大角度变化，如果是则不进行过滤
  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度
  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {
    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);
    vehicleLastRotations.set(vehicleId, newRotation);
    return newRotation;
  }

  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);
  vehicleLastRotations.set(vehicleId, filteredRotation);

  return filteredRotation;
};

// 添加一个常量定义在文件顶部
// ... existing code ...
// 修改红绿灯状态更新频率（秒）
const TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次
// ... existing code ...

// 在文件顶部添加
const mixersToCleanup = new Set();
const bonesMap = new Map();

// 在文件顶部添加资源管理器
const resourceManager = {
  mixers: new Set(),
  bones: new Map(),
  actions: new Map(),
  models: new Set(),

  addMixer(mixer, model) {
    this.mixers.add(mixer);
    if (model) {
      this.models.add(model);
      // 记录骨骼
      model.traverse(object => {
        if (object.isBone) {
          this.bones.set(object.uuid, object);
        }
      });
    }
    return mixer;
  },

  addAction(action, mixer) {
    if (!this.actions.has(mixer)) {
      this.actions.set(mixer, new Set());
    }
    this.actions.get(mixer).add(action);
    return action;
  },

  removeMixer(mixer) {
    if (this.mixers.has(mixer)) {
      try {
        // 停止并清理所有动作
        if (this.actions.has(mixer)) {
          this.actions.get(mixer).forEach(action => {
            if (action && typeof action.stop === 'function') {
              action.stop();
            }
          });
          this.actions.delete(mixer);
        }

        // 停止混合器
        if (typeof mixer.stopAllAction === 'function') {
          mixer.stopAllAction();
        }

        // 清理混合器的根对象
        const root = mixer.getRoot();
        if (root) {
          this.models.delete(root);
          root.traverse(object => {
            if (object && object.isBone) {
              this.bones.delete(object.uuid);
            }
            if (object && object.animations) {
              object.animations.length = 0;
            }
          });

          // 安全地清理缓存
          try {
            if (typeof mixer.uncacheRoot === 'function') {
              mixer.uncacheRoot(root);
            }

            if (typeof mixer.uncacheAction === 'function') {
              mixer.uncacheAction(null, root);
            }

            // 这里是发生错误的地方，添加防御性检查
            if (typeof mixer.uncacheClip === 'function') {
              // 不直接调用uncacheClip(null)，而是获取混合器中的所有动画片段并逐个清理
              const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);
              clips.forEach(clip => {
                if (clip && clip.uuid) {
                  mixer.uncacheClip(clip);
                }
              });
            }
          } catch (e) {
            console.log('在清理动画混合器时发生非致命错误:', e);
            // 继续执行其余清理操作
          }
        }

        this.mixers.delete(mixer);
      } catch (error) {
        console.error('清理动画混合器时发生错误:', error);
        // 确保即使出错也会从集合中移除
        this.mixers.delete(mixer);
      }
    }
  },

  cleanup() {
    try {
      // 清理动作
      this.actions.forEach((actions, mixer) => {
        try {
          actions.forEach(action => {
            if (action && typeof action.stop === 'function') {
              action.stop();
            }
          });
          actions.clear();
        } catch (e) {
          console.log('清理动作时发生非致命错误:', e);
        }
      });
      this.actions.clear();

      // 清理混合器
      this.mixers.forEach(mixer => {
        try {
          if (typeof mixer.stopAllAction === 'function') {
            mixer.stopAllAction();
          }

          const root = mixer.getRoot();
          if (root) {
            root.traverse(object => {
              if (object && object.animations) {
                object.animations.length = 0;
              }
            });

            // 安全清理
            if (typeof mixer.uncacheRoot === 'function') {
              mixer.uncacheRoot(root);
            }

            if (typeof mixer.uncacheAction === 'function') {
              mixer.uncacheAction(null, root);
            }

            // 安全清理动画片段
            try {
              if (mixer._actions && Array.isArray(mixer._actions)) {
                const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);
                clips.forEach(clip => {
                  if (clip && clip.uuid && typeof mixer.uncacheClip === 'function') {
                    mixer.uncacheClip(clip);
                  }
                });
              }
            } catch (e) {
              console.log('清理动画片段时发生非致命错误:', e);
            }
          }
        } catch (e) {
          console.log('清理混合器时发生非致命错误:', e);
        }
      });
      this.mixers.clear();

      // 清理骨骼
      this.bones.forEach(bone => {
        if (bone.parent) {
          bone.parent.remove(bone);
        }
        if (bone.matrix) bone.matrix.identity();
        if (bone.matrixWorld) bone.matrixWorld.identity();
      });
      this.bones.clear();

      // 清理模型
      this.models.forEach(model => {
        if (model.parent) {
          model.parent.remove(model);
        }
        model.traverse(object => {
          if (object.isMesh) {
            if (object.geometry) {
              object.geometry.dispose();
            }
            if (object.material) {
              if (Array.isArray(object.material)) {
                object.material.forEach(material => {
                  if (material.map) material.map.dispose();
                  material.dispose();
                });
              } else {
                if (object.material.map) object.material.map.dispose();
                object.material.dispose();
              }
            }
          }
          if (object.animations) {
            object.animations.length = 0;
          }
        });
      });
      this.models.clear();
    } catch (error) {
      console.error('全局清理过程中发生错误:', error);
      // 即使发生错误也尝试清空集合
      this.actions.clear();
      this.mixers.clear();
      this.bones.clear();
      this.models.clear();
    }
  }
};

// 修改创建动画混合器的函数
const createAnimationMixer = (model) => {
  const mixer = new THREE.AnimationMixer(model);
  return resourceManager.addMixer(mixer, model);
};

// 修改动画动作创建的函数
const createAction = (clip, mixer, model) => {
  const action = mixer.clipAction(clip, model);
  return resourceManager.addAction(action, mixer);
};

// 在CampusModel组件顶部添加消息缓存
const processedMessageIds = new Set();

// 添加事件去重相关的全局变量
let eventListCache = []; // 事件列表缓存，存储所有事件的完整信息
let eventIdCounter = 1; // 事件ID计数器
let eventMarkers = new Map(); // 存储事件标记的映射，key为eventId，value为THREE对象

const CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {
  const containerRef = useRef(null);
  const vehicleRef = useRef(null);
  const converter = useRef(new CoordinateConverter());
  const trajectoryRef = useRef([]);
  const currentPointRef = useRef(0);
  const mqttClientRef = useRef(null);
  const animationFrameRef = useRef(null);
  const mapRef = useRef(null);

  // 添加相机平滑过渡的变量
  const lastCameraPosition = useRef(null);
  const lastCameraTarget = useRef(null);
  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)

  // 添加行人动画相关的引用
  const prevAnimationTimeRef = useRef(Date.now());
  const peopleAnimationMixers = new Map(); // 使用全局Map存储所有行人的动画混合器（不再使用useRef）
  const peopleAnimations = useRef([]); // 存储行人动画数据


  // 设备数据 state
  const [devicesData, setDevicesData] = useState({ devices: [] });

  // 动态加载设备数据
  const loadDevicesData = async () => {
    let devicesArray = [];
    try {
      // const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
      const apiUrl = BASE_URL;
      const response = await axios.get(`${apiUrl}/api/devices`);
      if (response.data && response.data.success) {
        devicesArray = response.data.data;
      }

      console.log('devicesArrayapiUrl', apiUrl);
      console.log('devicesArray', devicesArray);
    } catch (error) {
      try {
        const response = await fetch('/src/data/devices.json');
        const json = await response.json();
        devicesArray = json.devices || [];
      } catch (e) {
        console.error('设备数据加载失败', e);
      }
    }
    setDevicesData({ devices: devicesArray });
  };
  useEffect(() => { loadDevicesData(); }, []);

 // 路口数据 state
  const [intersections, setIntersections] = useState([]);
  // 动态加载路口数据
  useEffect(() => {
    const fetchIntersections = async () => {
      try {
        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
        const response = await axios.get(`${apiUrl}/api/intersections`);
        if (response.data && response.data.success) {
          setIntersections(response.data.data || []);
        }
      } catch (error) {
        console.error('获取路口信息失败:', error);
      }
    };
    fetchIntersections();
  }, []);


  // 添加车辆状态
  const [vehicleState, setVehicleState] = useState({
    longitude: 0,
    latitude: 0,
    speed: 0,
    heading: 0
  });

  // 在 CampusModel 组件中添加状态
  const [viewMode, setViewMode] = useState('global');

  // 添加视角切换按钮的样式
  const buttonContainerStyle = {
    position: 'fixed',
    bottom: '20px',
    left: '50%',
    transform: 'translateX(-50%)',
    zIndex: 1000,  // 改为1000，避免遮挡点击
    display: 'flex',
    gap: '10px'
  };

  const buttonStyle = {
    padding: '8px 16px',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    border: '1px solid #ddd',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '14px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    transition: 'all 0.3s ease'
  };

  // 添加相机引用
  const cameraRef = useRef(null);

  // 添加路口选择相关代码
  const [selectedIntersection, setSelectedIntersection] = useState(null);

  // 添加红绿灯状态弹出窗口相关状态
  const [trafficLightPopover, setTrafficLightPopover] = useState({
    visible: false,
    interId: null,
    position: { x: 0, y: 0 },
    content: null,
    phases: []
  });

  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收
  const currentPopoverIdRef = useRef(null);

  // 添加红绿灯状态自动更新定时器引用
  const trafficLightUpdateTimerRef = useRef(null);

  // 全局存储setTrafficLightPopover函数引用
  window._setTrafficLightPopover = setTrafficLightPopover;

  // 将Ref暴露给全局以便弹窗函数使用
  window.currentPopoverIdRef = currentPopoverIdRef;
  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;

  // 修改路口选择器的样式
  const intersectionSelectStyle = {
    position: 'fixed',
    top: '65px',  // 从 60px 改为 65px
    left: '50%',
    transform: 'translateX(-50%)',
    width: '200px',  // 从 300px 改为 200px
    zIndex: 1001,
    backgroundColor: 'white',
    borderRadius: '4px',
    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
  };

  // 添加文字标签样式
  const labelStyle = {
    position: 'fixed',
    top: '65px',
    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框
    transform: 'translateX(-100%)',
    padding: '0 5px',  // 从 10px 改为 5px，减少内边距
    lineHeight: '32px',
    color: '#fff',
    fontSize: '14px',
    fontWeight: 'bold',
    textShadow: '0 1px 2px rgba(0,0,0,0.3)',
    zIndex: 1001,
  };

  // 添加交通灯映射状态
  const [trafficLightsMap] = useState(new Map());

  // 添加当前RSU状态
  const [currentRSU, setCurrentRSU] = useState(null);

  // 添加设备时间戳状态
  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());

  // 添加最后一条消息状态
  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });

  // 设备信息弹框状态
  const [devicePopover, setDevicePopover] = useState({
    visible: false,
    deviceId: null,
    position: { x: 0, y: 0 },
    content: null
  });

  // 设备弹框关闭函数
  const handleCloseDevicePopover = () => {
    setDevicePopover({ visible: false, deviceId: null, position: { x: 0, y: 0 }, content: null });
  };

  // 设备弹框内容渲染函数
  const renderDevicePopoverContent = (device) => {
    if (!device) return null;
    return <DevicePopoverContent device={device} />;
  };

  // 修改视角切换函数
  const switchToFollowView = () => {
    if (cameraMode !== 'follow') {
      console.log('切换到跟随视角');
      cameraMode = 'follow';

      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡
      lastCameraPosition.current = null;
      lastCameraTarget.current = null;

      if (controls) {
        controls.enabled = false;
      }
    }
  };

  const switchToGlobalView = () => {
    if (cameraMode !== 'global') {
      console.log('切换到全局视角');
      cameraMode = 'global';

      // 重置相机平滑变量
      lastCameraPosition.current = null;
      lastCameraTarget.current = null;

      if (cameraRef.current && controls) {
        // 获取当前相机位置和朝向
        // const currentPos = cameraRef.current.position.clone();
        cameraRef.current.position.set(0, 500, 0);
        const currentPos = cameraRef.current.position.clone();
        const currentUp = cameraRef.current.up.clone();

        // 创建相机位置的补间动画
        new TWEEN.Tween(currentPos)
          .to({ x: 0, y: 300, z: 0 }, 1000)
          .easing(TWEEN.Easing.Quadratic.InOut)
          .onUpdate(() => {
            cameraRef.current.position.copy(currentPos);
          })
          .start();

        // 创建相机上方向的补间动画
        new TWEEN.Tween(currentUp)
          .to({ x: 0, y: 1, z: 0 }, 1000)
          .easing(TWEEN.Easing.Quadratic.InOut)
          .onUpdate(() => {
            cameraRef.current.up.copy(currentUp);
          })
          .start();

        // 获取当前控制器目标点
        controls.target.set(0, 0, 0);
        const currentTarget = controls.target.clone();

        // 创建目标点的补间动画
        new TWEEN.Tween(currentTarget)
          .to({ x: 0, y: 0, z: 0 }, 1000)
          .easing(TWEEN.Easing.Quadratic.InOut)
          .onUpdate(() => {
            controls.target.copy(currentTarget);
            // 确保相机始终朝向目标点
            cameraRef.current.lookAt(controls.target);
            controls.update();
          })
          .start();

        // 启用控制器
        controls.enabled = true;

        // 重置控制器的一些属性
        controls.minDistance = 50;
        controls.maxDistance = 500;
        controls.maxPolarAngle = Math.PI / 2.1;
        controls.minPolarAngle = 0;
        controls.update();
        // 强制更新相机矩阵
        cameraRef.current.updateMatrix();
        cameraRef.current.updateMatrixWorld(true);

        console.log('切换到全局视角', {
          目标相机位置: [0, 300, 0],
          目标控制点: [0, 0, 0],
          动画已启动: true
        });
      }
    }
  };

  // 修改处理路口选择的函数
  const handleIntersectionChange = (value) => {
    const intersection = intersections.find(i => i.name === value);

    if (intersection && cameraRef.current && controls) {
      setSelectedIntersection(intersection);

      // 使用 wgs84ToModel 方法转换经纬度到模型坐标
      const modelCoords = converter.current.wgs84ToModel(
        parseFloat(intersection.longitude),
        parseFloat(intersection.latitude)
      );

      console.log('路口坐标转换结果:', {
        路口名称: intersection.name,
        经纬度: {
          longitude: intersection.longitude,
          latitude: intersection.latitude
        },
        模型坐标: modelCoords
      });

      // 设置为路口视角模式
      cameraMode = 'intersection';
      setViewMode('intersection');

      // 直接设置相机位置
      cameraRef.current.position.set(modelCoords.x+50, 70, -modelCoords.y+50);

      // 直接设置控制器目标点
      controls.target.set(modelCoords.x, 0, -modelCoords.y);

      // 确保相机朝向目标点
      cameraRef.current.lookAt(controls.target);

      // 更新控制器
      controls.enabled = true;
      controls.update();

      // 强制更新相机矩阵
      cameraRef.current.updateMatrix();
      cameraRef.current.updateMatrixWorld(true);

      console.log('相机已直接移动到路口:', {
        路口名称: intersection.name,
        相机位置: cameraRef.current.position.toArray(),
        目标点: controls.target.toArray(),
        模型坐标: modelCoords
      });

      // 如果该路口有红绿灯，自动显示红绿灯弹窗
      if (intersection.hasTrafficLight !== false && intersection.interId) {
        console.log(`路口 ${intersection.name} 有红绿灯，准备显示红绿灯状态`);

        // 延迟300ms调用以确保场景已更新
        setTimeout(() => {
          // 检查多种可能的ID格式
          let interId = intersection.interId;

          // 使用全局的showTrafficLightPopup函数显示红绿灯弹窗
          if (window.showTrafficLightPopup) {
            window.showTrafficLightPopup(interId);
            console.log(`已触发路口 ${intersection.name} (ID: ${interId}) 的红绿灯弹窗显示`);
          } else {
            console.error('找不到显示红绿灯弹窗的函数');
          }
        }, 300);
      } else {
        console.log(`路口 ${intersection.name} 没有红绿灯或缺少ID，不显示红绿灯状态`);

        // 如果有弹窗正在显示，则关闭它
        if (window._setTrafficLightPopover) {
          window._setTrafficLightPopover({
            visible: false
          });
        }
      }
    }
  };

  // 修改处理MQTT消息的函数
  const handleMqttMessage = (topic, message) => {
    try {
      const payload = JSON.parse(message);

      // 处理RSM消息
      if (topic === MQTT_CONFIG.rsm) {
        // console.log('收到RSM消息:', payload);

        // 检查设备时间戳
        const deviceMac = payload.mac;
        const messageTimestamp = payload.tm;

        // 获取该设备的最新时间戳
        const lastTimestamp = deviceTimestamps.get(deviceMac);

        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息
        if (lastTimestamp && messageTimestamp < lastTimestamp) {
          // console.log('忽略过期的RSM消息:', {
          //   设备MAC: deviceMac,
          //   消息时间戳: messageTimestamp,
          //   最新时间戳: lastTimestamp
          // });
          return;
        }

        // 更新设备的最新时间戳
        deviceTimestamps.set(deviceMac, messageTimestamp);

        // console.log('RSM消息时间戳更新:', {
        //   设备MAC: deviceMac,
        //   时间戳: messageTimestamp,
        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp
        // });

        const participants = payload.data?.participants || [];
        const rsuid = payload.data.rsuid;

        // 分类处理不同类型的参与者
        const now = Date.now();

        // 处理所有参与者
        participants.forEach(participant => {
          // const id = participant.partPtcId;
          // const id =  rsuid + participant.partPtcId;
          const id =  deviceMac + participant.partPtcId;
          const type = participant.partPtcType;

          if(type === '3'||type === '2'||type === '1'){
          // if(type === '3'){
          // if(type === '3'||type === '1'){
            // 解析位置和状态信息
            const state = {
              longitude: parseFloat(participant.partPosLong),
              latitude: parseFloat(participant.partPosLat),
              speed: parseFloat(participant.partSpeed),
              heading: parseFloat(participant.partHeading)
            };

            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);

            // 根据类型选择对应的预加载模型
            let preloadedModel;
            switch (type) {
              case '1': // 机动车
                preloadedModel = preloadedVehicleModel;
                break;
              case '2': // 非机动车
                preloadedModel = preloadedCyclistModel;
                break;
              case '3': // 行人
                preloadedModel = preloadedPeopleModel;
                break;
              default:
                return; // 跳过未知类型
            }

            // 获取或创建模型
          let model = vehicleModels.get(id);

          if (!model && preloadedModel) {
              // 创建新模型实例
              const newModel = type === '3' ? SkeletonUtils.clone(preloadedPeopleModel) : preloadedModel.clone();
              // 根据类型调整高度和缩放
              const height = type === '3' ? 2.0 : 1.0;
              newModel.position.set(modelPos.x, height, -modelPos.y);
              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;

              // 如果是行人类型，设置缩放和创建动画
              if (type === '3') {
              // newModel.scale.set(0.005, 0.005, 0.005);
                newModel.scale.set(4, 4, 4);

                // 使用resourceManager创建并管理动画混合器
                const mixer = createAnimationMixer(newModel);

                if (peopleBaseModel && peopleBaseModel.animations && peopleBaseModel.animations.length > 0) {
                  // 只创建一个动作并添加到资源管理器
                  const action = createAction(peopleBaseModel.animations[0], mixer, newModel);
                  action.play();
                }

                // 保存到全局Map中(不再使用useRef)
                peopleAnimationMixers.set(id, mixer);
              }

              scene.add(newModel);

              vehicleModels.set(id, {
                model: newModel,
                lastUpdate: now,
              type: type
              });
          } else if (model) {
              // 更新现有模型
            model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);
            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;
            model.lastUpdate = now;
            model.model.updateMatrix();
            model.model.updateMatrixWorld(true);
            }
          }
        });

        // 清理长时间未更新的模型
        const CLEANUP_THRESHOLD = 1000;
        const currentIds = new Set(participants.map(p => deviceMac + p.partPtcId));

        vehicleModels.forEach((modelData, id) => {
          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {
            // 如果是行人，清理动画混合器
            if (modelData.type === '3' && peopleAnimationMixers.has(id)) {
              const mixer = peopleAnimationMixers.get(id);
              // 使用resourceManager清理混合器
              resourceManager.removeMixer(mixer);
              peopleAnimationMixers.delete(id);
            }

            // 从场景移除模型
            scene.remove(modelData.model);
            vehicleModels.delete(id);
          }
        });
        return;
      }

      // 处理BSM消息
      if (topic === MQTT_CONFIG.bsm) {
        // console.log('收到BSM消息:', payload);

        const bsmData = payload.data;
        const bsmid = bsmData.bsmId;
        const newState = {
          longitude: parseFloat(bsmData.partLong),
          latitude: parseFloat(bsmData.partLat),
          speed: parseFloat(bsmData.partSpeed),
          heading: parseFloat(bsmData.partHeading)
        };

        // console.log('解析后的车辆状态:', newState);
        // console.log('车辆ID:', bsmid);

        // 通知RealTimeTraffic组件已收到真实BSM消息
        window.postMessage({
          type: 'realBsmReceived',
          source: 'CampusModel'
        }, '*');

        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构
        window.postMessage({
          type: 'bsm',
          bsmId: bsmid, // 直接传递车辆ID
          data: {       // 同时提供完整的BSM数据
            bsmId: bsmid,
            partSpeed: bsmData.partSpeed,
            partLat: bsmData.partLat,
            partLong: bsmData.partLong,
            partHeading: bsmData.partHeading
          }
        }, '*');

        // 获取模型位置坐标
        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);
        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);
        const initialRotation = Math.PI - newState.heading * Math.PI / 180;

        // 应用平滑滤波 - 使用已有的滤波函数
        const newPosition = filterPosition(initialPosition, bsmid);
        const newRotation = filterRotation(initialRotation, bsmid);

        // 检查该车辆是否已存在于场景中
        let vehicleObj = vehicleModels.get(bsmid);

        // 检查是否是主车
        const isMainVehicle = bsmid === mainVehicleBsmId;

        if (!vehicleObj && preloadedVehicleModel) {
          // 创建一个新的车辆模型实例
          const newVehicleModel = preloadedVehicleModel.clone();

          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来
          // 这样可以避免车辆突然出现的视觉冲击
          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);
          newVehicleModel.rotation.y = newRotation;

          // 设置BSM车辆为突出的颜色
          newVehicleModel.traverse((child) => {
            if (child.isMesh && child.material) {
              // // 保存原始材质颜色
              // if (!child.userData.originalColor && child.material.color) {
              //   child.userData.originalColor = child.material.color.clone();
              // }
              // // 设置为更鲜艳的颜色
              // if (isMainVehicle) {
              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色
              // } else {
              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色
              // }
              // // 增加材质亮度
              // child.material.emissive = new THREE.Color(0x222222);

              // // 初始设置为半透明
              // child.material.transparent = true;
              // child.material.opacity = 0.6;

              // child.material.needsUpdate = true;

              const newMaterial = child.material.clone();
              child.material = newMaterial;

              // 修改颜色逻辑（与原模型解耦）
              if (isMainVehicle) {
                newMaterial.color.set(0x00BFFF);
              } else {
                newMaterial.color.set(0xFF6347);
              }
              newMaterial.emissive = new THREE.Color(0x222222);
              newMaterial.transparent = true;
              // newMaterial.opacity = 0.6;
              newMaterial.needsUpdate = true;
            }
          });

          // 创建速度显示标签
          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {
            backgroundColor: isMainVehicle ?
              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景
              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景
            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本
            fontSize: 10,
            padding: 8
          });
          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方
          speedLabel.renderOrder = 1000; // 确保在最上层渲染
          speedLabel.material.opacity = 0.6; // 初始半透明
          newVehicleModel.add(speedLabel);

          scene.add(newVehicleModel);

          // 保存车辆引用到车辆模型集合中
          vehicleModels.set(bsmid, {
            model: newVehicleModel,
            lastUpdate: Date.now(),
            type: '1', // 设置为机动车类型
            isMain: isMainVehicle,
            speedLabel: speedLabel // 保存速度标签引用
          });

          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);

          // 使用补间动画使车辆从地下逐渐显示出来
          new TWEEN.Tween(newVehicleModel.position)
            .to({ y: 0.5 }, 500)
            .easing(TWEEN.Easing.Quadratic.Out)
            .start();

          // 使用补间动画使车辆从半透明变为完全不透明
          newVehicleModel.traverse((child) => {
            if (child.isMesh && child.material && child.material.transparent) {
              new TWEEN.Tween({ opacity: 0.6 })
                .to({ opacity: 1.0 }, 500)
                .easing(TWEEN.Easing.Quadratic.Out)
                .onUpdate(function() {
                  child.material.opacity = this.opacity;
                  child.material.needsUpdate = true;
                })
                .start();
            }
          });

          // 为速度标签也添加透明度动画
          new TWEEN.Tween({ opacity: 0.6 })
            .to({ opacity: 1.0 }, 500)
            .easing(TWEEN.Easing.Quadratic.Out)
            .onUpdate(function() {
              speedLabel.material.opacity = this.opacity;
              speedLabel.material.needsUpdate = true;
            })
            .start();

          // 如果是主车，设置全局引用
          if (isMainVehicle) {
            globalVehicleRef = newVehicleModel;
            setVehicleState(newState);
            console.log('设置全局主车引用:', bsmid);
          }
        } else if (vehicleObj) {
          // 应用滤波
          const filteredPosition = filterPosition(newPosition, bsmid);
          const filteredRotation = filterRotation(newRotation, bsmid);

          // 更新现有车辆位置和朝向
          vehicleObj.model.position.copy(filteredPosition);
          vehicleObj.model.rotation.y = filteredRotation;
          vehicleObj.model.updateMatrix();
          vehicleObj.model.updateMatrixWorld(true);
          vehicleObj.lastUpdate = Date.now();
          vehicleObj.isMain = isMainVehicle; // 更新主车状态

          // 更新速度标签文本
          if (vehicleObj.speedLabel) {
            vehicleObj.speedLabel.material.map.dispose();
            vehicleObj.model.remove(vehicleObj.speedLabel);
          }

          // 创建新的速度标签
          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {
            backgroundColor: isMainVehicle ?
              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景
              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景
            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本
            fontSize: 20,
            padding: 8
          });
          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方
          speedLabel.renderOrder = 1000; // 确保在最上层渲染
          vehicleObj.model.add(speedLabel);
          vehicleObj.speedLabel = speedLabel;

          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);

          // 如果是主车，同时更新全局引用和状态（用于相机跟随）
          if (isMainVehicle) {
            globalVehicleRef = vehicleObj.model;
            setVehicleState(newState);
          }
        }

        // 清理长时间未更新的车辆
        const now = Date.now();
        const CLEANUP_THRESHOLD = 1000; // 增加到10秒，避免频繁清理造成闪烁

        vehicleModels.forEach((modelData, id) => {
          const timeSinceLastUpdate = now - modelData.lastUpdate;

          // 对于即将超时的车辆，先降低透明度，而不是直接移除
          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {
            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));
            const opacity = 1 ;

            modelData.model.traverse((child) => {
              if (child.isMesh && child.material) {
                // 如果材质还没有透明度设置，先保存初始状态
                if (child.material.transparent === undefined) {
                  child.material.originalTransparent = child.material.transparent || false;
                  child.material.originalOpacity = child.material.opacity || 1.0;
                }

                // 设置透明度
                child.material.transparent = true;
                child.material.opacity = opacity;
                child.material.needsUpdate = true;
              }
            });

            // 如果有速度标签，也调整其透明度
            if (modelData.speedLabel) {
              modelData.speedLabel.material.opacity = opacity;
              modelData.speedLabel.material.needsUpdate = true;
            }
          }
          // 完全超时，移除车辆
          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {
            // 清理资源
            if (modelData.speedLabel) {
              modelData.speedLabel.material.map.dispose();
              modelData.speedLabel.material.dispose();
              modelData.model.remove(modelData.speedLabel);
            }

            modelData.model.traverse((child) => {
              if (child.isMesh) {
                if (child.material) {
                  if (Array.isArray(child.material)) {
                    child.material.forEach(m => m.dispose());
                  } else {
                    child.material.dispose();
                  }
                }
                if (child.geometry) child.geometry.dispose();
              }
            });

            // 从场景中移除
            scene.remove(modelData.model);
            vehicleModels.delete(id);
            // 同时清除该车辆的滤波缓存
            vehicleLastPositions.delete(id);
            vehicleLastRotations.delete(id);

            console.log(`移除长时间未更新的车辆: ID ${id}`);
          }
        });

        return;
      }

      // SPAT消息处理
      if (topic === MQTT_CONFIG.spat) {
        // console.log('收到SPAT消息:', message);

        try {
          const payload = JSON.parse(message);

          // 检查设备时间戳
          const deviceMac = payload.mac;
          const messageTimestamp = payload.tm;

          // 获取该设备的最新时间戳
          const lastTimestamp = deviceTimestamps.get(deviceMac);

          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息
          if (lastTimestamp && messageTimestamp < lastTimestamp) {
            // console.log('忽略过期的SPAT消息:', {
            //   设备MAC: deviceMac,
            //   消息时间戳: messageTimestamp,
            //   最新时间戳: lastTimestamp
            // });
            return;
          }

          // 更新设备时间戳
          deviceTimestamps.set(deviceMac, messageTimestamp);

          // 修改：访问data.intersections而不是直接访问intersections
          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {
            payload.data.intersections.forEach(intersection => {
              const interId = intersection.interId;

              if (!interId) {
                console.error('SPAT消息缺少interId:', intersection);
                return;
              }

              // console.log(`处理路口ID: ${interId} 的SPAT消息`);

              // 创建所有相位的数组 - 存储到trafficLightStates
              if (intersection.phases && Array.isArray(intersection.phases)) {
                // 构建存储相位信息的数组
                const phasesInfo = [];

                intersection.phases.forEach(phase => {
                  // 修改：使用phaseId而不是id
                  if (!phase.phaseId) {
                    console.error('相位信息缺少phaseId:', phase);
                    return;
                  }

                  const phaseId = phase.phaseId.toString();
                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数
                  const direction = phase.trafficDirec ?
                    getDirectionFromCode(phase.trafficDirec) :
                    getPhaseDirection(phaseId);

                  // 修改：直接从phase中获取信号灯状态和剩余时间
                  const lightState = phase.trafficLight || 'R'; // 默认为红灯
                  const remainTime = parseInt(phase.remainTime) || 0;

                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);

                  // 构建相位信息对象
                  const phaseInfo = {
                    phaseId,
                    direction,
                    trafficLight: lightState,
                    remainTime
                  };

                  // 添加到相位信息数组
                  phasesInfo.push(phaseInfo);

                  // 查找红绿灯模型并更新视觉效果
                  // 尝试使用字符串ID和数字ID查找
                  let trafficLightKey = String(interId);
                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);

                  if (!trafficLightModel) {
                    // 尝试使用数字ID
                    trafficLightKey = parseInt(interId);
                    trafficLightModel = trafficLightsMap.get(trafficLightKey);
                  }

                  if (trafficLightModel) {
                    // 更新交通灯视觉效果
                    updateTrafficLightVisual(trafficLightModel, phaseInfo);

                    // 更新弹出窗信息
                    if (selectedIntersection && selectedIntersection.interId === interId) {
                      setTrafficLightPopover(prev => ({
                        ...prev,
                        visible: true,
                        phaseId,
                        direction,
                        state: lightState,
                        remainTime
                      }));
                    }
                  } else {
                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);
                  }
                });

                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型
                let modelKey = null;
                // 尝试字符串ID
                const strId = String(interId);
                if (trafficLightsMap.has(strId)) {
                  modelKey = strId;
                } else {
                  // 尝试数字ID
                  const numId = parseInt(interId);
                  if (trafficLightsMap.has(numId)) {
                    modelKey = numId;
                  }
                }

                if (modelKey !== null) {
                  // 使用正确的ID类型存储状态信息
                  trafficLightStates.set(modelKey, {
                    updateTime: Date.now(),
                    phases: phasesInfo
                  });
                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);

                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗
                  if (window.currentPopoverIdRef &&
                     (window.currentPopoverIdRef.current === modelKey ||
                      window.currentPopoverIdRef.current === String(modelKey) ||
                      window.currentPopoverIdRef.current === parseInt(modelKey))) {

                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);
                    // 强制更新弹窗ID为当前数据的正确ID类型
                    window.currentPopoverIdRef.current = modelKey;

                    // 如果没有更新定时器则创建一个
                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {
                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);
                      setTimeout(() => {
                        window.showTrafficLightPopup(modelKey);
                      }, 100);
                    }
                  }
                } else {
                  // 如果找不到模型，仍使用原始ID存储
                  trafficLightStates.set(interId, {
                    updateTime: Date.now(),
                    phases: phasesInfo
                  });
                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);
                }
              } else {
                console.error('SPAT消息缺少相位信息:', intersection);
              }
            });
          } else {
            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);
          }
        } catch (error) {
          console.error('解析SPAT消息出错:', error, message);
        }
        return;
      }

      // 处理 RSI 消息
      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {
        // console.log('收到RSI消息:', payload);

        // 发送 RSI 消息到 RealTimeTraffic 组件，确保包含mac和timestamp
        window.postMessage({
          type: 'RSI',
          data: payload.data,
          mac: payload.mac,
          tm: payload.tm
        }, '*');

        const rsiData = payload.data;
        const rsuId = rsiData.rsuId;
        const events = rsiData.rtes || [];

        events.forEach(event => {
          const eventId = event.rteId;
          const eventType = event.eventType;
          const description = event.description;
          const startTime = event.startTime;
          const endTime = event.endTime;

          // 将基准点经纬度转换为模型坐标
          const modelPos = converter.current.wgs84ToModel(
            parseFloat(rsiData.posLong),
            parseFloat(rsiData.posLat)
          );

          // 根据事件类型显示不同的提示或标记
          let warningText = '';
          let warningColor = '';

          switch(eventType) {
            case '401':  // 道路抛洒物
              warningText = '道路抛洒物';
              warningColor = '#ff4d4f';
              break;
            case '404':  // 道路障碍物
              warningText = '道路障碍物';
              warningColor = '#faad14';
              break;
            case '405':  // 行人通过马路
              warningText = '行人通过马路';
              warningColor = '#1890ff';
              break;
            case '904':  // 逆行车辆
              warningText = '逆行车辆';
              warningColor = '#f5222d';
              break;
            case '910':  // 违停车辆
              warningText = '违停车辆';
              warningColor = '#722ed1';
              break;
            case '1002': // 道路施工
              warningText = '道路施工';
              warningColor = '#fa8c16';
              break;
            case '901':  // 车辆超速
              warningText = '车辆超速';
              warningColor = '#eb2f96';
              break;
            default:
              warningText = description || '未知事件';
              warningColor = '#8c8c8c';
          }

          // 显示警告标记
          showWarningMarker(modelPos, warningText, warningColor, eventType, {
            rsuId: payload.data?.rsuId || 'UNKNOWN',
            eventId: eventId,
            description: description
          });

          // console.log('RSI事件处理:', {
          //   事件ID: eventId,
          //   事件类型: eventType,
          //   事件说明: description,
          //   开始时间: startTime,
          //   结束时间: endTime,
          //   位置: modelPos
          // });
        });

        return;
      }

      // 处理场景事件消息
      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {
        // console.log('收到场景事件消息:', payload);

        const sceneData = payload.data;
        const sceneId = sceneData.sceneId;
        const sceneType = sceneData.sceneType;
        const sceneDesc = sceneData.sceneDesc;
        const position = {
          latitude: parseFloat(sceneData.partLat),
          longitude: parseFloat(sceneData.partLong)
        };

        // 将经纬度转换为模型坐标
        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);

        // 根据场景类型显示不同的提示或标记
        switch(sceneType) {
          case '2':  // 交叉路口碰撞预警
            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f', '2', { rsuId: payload.data?.rsuId, sceneData });
            break;
          case '9-5':  // 道路危险状况预警（施工）
            showWarningMarker(modelPos, '道路施工', '#faad14', '1002', { rsuId: payload.data?.rsuId, sceneData });
            break;
          case '9-6':  // 前方有障碍物
            showWarningMarker(modelPos, '前方障碍物', '#ff7a45', '404', { rsuId: payload.data?.rsuId, sceneData });
            break;
          case '10':  // 限速提醒
            const speedLimit = sceneData.eventData1;  // 限速值
            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff', '10', { rsuId: payload.data?.rsuId, sceneData });
            break;
          case '12':  // 交通参与者碰撞预警
            showWarningMarker(modelPos, '碰撞预警', '#f5222d', '12', { rsuId: payload.data?.rsuId, sceneData });
            break;
          case '13':  // 绿波车速引导
            showWarningMarker(modelPos, '绿波引导', '#52c41a', '13', { rsuId: payload.data?.rsuId, sceneData });
            break;
          case '21-8':  // 禁止鸣笛
            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1', '21-8', { rsuId: payload.data?.rsuId, sceneData });
            break;
          case '34':  // 逆行车辆提醒
            showWarningMarker(modelPos, '逆行警告', '#eb2f96', '904', { rsuId: payload.data?.rsuId, sceneData });
            break;
          case '33':  // 违章占道车辆预警
            showWarningMarker(modelPos, '违章占道', '#fa8c16', '910', { rsuId: payload.data?.rsuId, sceneData });
            break;
          case '999':  // 信号灯优先
            const priorityType = sceneData.eventData1;  // 优先类型
            const duration = sceneData.eventData2;      // 优先时长
            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2', '999', { rsuId: payload.data?.rsuId, sceneData });
            break;
        }

        return;
      }
      // 如果不是RSM或BSM消息，则记录为其他类型
      // console.log('未知类型消息:', {
      //   topic,
      //   type: payload.type,
      //   data: payload
      // });

    } catch (error) {
      console.error('处理MQTT消息失败:', error);
      console.error('原始消息内容:', message);
    }
  };

  // 修改初始化MQTT连接函数
  const initMqttClient = () => {
    console.log('正在连接MQTT服务器...');

    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;
    console.log('尝试连接WebSocket:', wsUrl);

    // 创建WebSocket连接，不指定协议
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log('WebSocket连接成功');
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);

        // 处理连接确认消息
        if (message.type === 'connect') {
          console.log('收到连接确认:', message);
          return;
        }

        // 处理心跳消息
        if (message.type === 'ping') {
          return;
        }

        // 处理MQTT消息
        if (message.type === 'message' && message.topic && message.payload) {
          // 检查消息ID是否已处理过
          if (message.messageId) {
            if (processedMessageIds.has(message.messageId)) {
              // console.log('跳过重复消息:', message.messageId);
              return;
            }

            // 添加到已处理集合
            processedMessageIds.add(message.messageId);

            // 限制缓存大小，防止内存泄漏
            if (processedMessageIds.size > 1000) {
              // 转换为数组，删除最早的100个元素
              const idsArray = Array.from(processedMessageIds);
              for (let i = 0; i < 100; i++) {
                processedMessageIds.delete(idsArray[i]);
              }
            }
          }

          // 直接将消息传递给handleMqttMessage处理
          handleMqttMessage(message.topic, JSON.stringify(message.payload));
        }
      } catch (error) {
        console.error('处理WebSocket消息失败:', error);
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
    };

    ws.onclose = () => {
      console.log('WebSocket连接关闭');
      // 5秒后尝试重新连接
      setTimeout(initMqttClient, 5000);
    };

    // 保存WebSocket引用
    mqttClientRef.current = ws;
  };

  useEffect(() => {
    if (!containerRef.current) return;

    // 预加载所有模型
    preloadModels();

    // 创建场景
    scene = new THREE.Scene(); // 使用全局scene变量

    // 创建相机
    const camera = new THREE.PerspectiveCamera(
      60,
      window.innerWidth / window.innerHeight,
      0.1,
      2000
    );
    // camera.position.set(0, 300, 0); // 初始为全局视角
    camera.position.set(0, 100, 0); // 初始为全局视角
    camera.lookAt(0, 0, 0);
    cameraRef.current = camera;

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000);
    renderer.setPixelRatio(window.devicePixelRatio);
    containerRef.current.appendChild(renderer.domElement);

    // 修改光照设置
    // 添加环境光和平行光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8
    scene.add(ambientLight);

    // 添加多个平行光源，从不同角度照亮车辆
    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0
    directionalLight1.position.set(10, 10, 10);
    scene.add(directionalLight1);

    // 添加第二个平行光源，从另一个角度照亮
    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight2.position.set(-10, 8, -10);
    scene.add(directionalLight2);

    // 添加一个聚光灯，专门照亮车辆
    const spotLight = new THREE.SpotLight(0xffffff, 1.0);
    spotLight.position.set(0, 50, 0);
    spotLight.angle = Math.PI / 4;
    spotLight.penumbra = 0.1;
    spotLight.decay = 2;
    spotLight.distance = 200;
    scene.add(spotLight);

    // 创建控制器
    controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.screenSpacePanning = false;
    controls.minDistance = 50;
    controls.maxDistance = 500;
    controls.maxPolarAngle = Math.PI / 2.1;
    controls.minPolarAngle = 0;
    controls.target.set(0, 0, 0);
    controls.update();

    // 打印相机和控制器引用
    console.log('初始化完成', {
      camera: !!camera,
      controls: !!controls,
      cameraRef: !!cameraRef.current
    });

    // 修改加载车辆模型的函数
    const loadVehicleModel = () => {
      return new Promise((resolve, reject) => {
        const vehicleLoader = new GLTFLoader();
        vehicleLoader.load(
          `${BASE_URL}/changli2/vehicle.glb`,
          (gltf) => {
            const vehicleModel = gltf.scene;

            // 创建一个新的Group作为根容器
            const vehicleContainer = new THREE.Group();

            // 调整模型材质
            vehicleModel.traverse((child) => {
              if (child.isMesh) {
                // 检查并调整材质
                if (child.material) {
                  // 创建新的标准材质
                  const newMaterial = new THREE.MeshStandardMaterial({
                    color: 0xffffff,      // 白色
                    metalness: 0.2,       // 降低金属感
                    roughness: 0.1,       // 降低粗糙度
                    envMapIntensity: 1.0  // 环境贴图强度
                  });

                  // 保留原始贴图
                  if (child.material.map) {
                    newMaterial.map = child.material.map;
                  }

                  // 应用新材质
                  child.material = newMaterial;

                  console.log('已调整车辆材质:', child.name);
                }
              }
            });

            // 遍历模型的所有子对象，确保它们都被正确添加到容器中
            while(vehicleModel.children.length > 0) {
              const child = vehicleModel.children[0];
              vehicleContainer.add(child);
            }

            // 确保容器直接添加到场景根节点
            scene.add(vehicleContainer);

            // 保存容器的引用
            globalVehicleRef = vehicleContainer;

            console.log('车辆模型加载成功，使用容器包装');
            setIsVehicleLoaded(true);
            resolve(vehicleContainer);
          },
          (xhr) => {
            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);
          },
          reject
        );
      });
    };

    // 修改初始化流程
    const initializeScene = async () => {
      try {
        // 1. 加载车辆模型
        // const vehicleContainer = await loadVehicleModel();

        // 2. 初始化MQTT客户端
        initMqttClient();

        // 3. 设置初始位置
        // if (vehicleContainer) {
        //   const initialState = {
        //     longitude: 113.0022348,
        //     latitude: 28.0698301,
        //     heading: 0
        //   };

        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);
        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);
        //   vehicleContainer.position.set(0, 1.0, 0);
        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);
        //   vehicleContainer.updateMatrix();
        //   vehicleContainer.updateMatrixWorld(true);
        //   currentPosition = vehicleContainer.position.clone();
        // }

      } catch (error) {
        console.error('初始化场景失败:', error);
      }
    };

    // 添加重试逻辑的加载函数
    const loadModelWithRetry = (url, maxRetries = 3) => {
      return new Promise((resolve, reject) => {
        const attemptLoad = (retriesLeft) => {
          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);

          const loader = new GLTFLoader();
          loader.load(
            url,
            (gltf) => {
              console.log(`模型加载成功: ${url}`);
              resolve(gltf);
            },
            (xhr) => {
              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);
            },
            (error) => {
              console.error(`加载失败: ${url}`, error);
              if (retriesLeft > 0) {
                console.log(`将在 1 秒后重试...`);
                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);
              } else {
                reject(error);
              }
            }
          );
        };

        attemptLoad(maxRetries);
      });
    };

    // 使用重试逻辑加载模型
    const loader = new GLTFLoader();
    loader.load(
      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,
      async (gltf) => {
        try {
          const model = gltf.scene;
          model.scale.set(1, 1, 1);
          model.position.set(0, 0, 0);

          // 检查scene是否初始化
          if (scene) {
          scene.add(model);

          // 在校园模型加载完成后初始化场景
          await initializeScene();
          } else {
            console.error('无法添加模型：场景未初始化');
          }
        } catch (error) {
          console.error('处理模型时出错:', error);
        }
      },
      (xhr) => {
        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);
      },
      (error) => {
        console.error('模型加载错误:', error);
        console.error('错误详情:', {
          错误类型: error.type,
          错误消息: error.message,
          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,
          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`
        });
      }
    );

    // 修改动画循环
    const animate = () => {
      animationFrameRef.current = requestAnimationFrame(animate);

      // 更新 TWEEN 动画
      TWEEN.update();

      // 更新行人动画 - 只更新存在的混合器
      const deltaTime = clock.getDelta();

      // 使用Map而不是ref.current
      if (peopleAnimationMixers.size > 0) {
        peopleAnimationMixers.forEach((mixer) => {
          mixer.update(deltaTime);
        });
      }

      if (cameraMode === 'follow' && globalVehicleRef) {
        // 在跟随模式下禁用控制器
        controls.enabled = false;

        // 获取车辆当前位置
        const vehiclePos = globalVehicleRef.position.clone();

        // 获取车辆旋转角度
        const vehicleRotation = globalVehicleRef.rotation.y;

        // 计算相机偏移量
        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方
        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;

        // 增加距离到 50 单位
        const cameraOffset = new THREE.Vector3(
          -50 * Math.cos(adjustedRotation),
          200,
          -50 * Math.sin(adjustedRotation)
        );

        // 计算目标相机位置和观察点
        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);
        const targetLookAt = vehiclePos.clone();

        // 初始化上一帧数据（如果是首次）
        if (!lastCameraPosition.current) {
          lastCameraPosition.current = targetCameraPosition.clone();
        }

        if (!lastCameraTarget.current) {
          lastCameraTarget.current = targetLookAt.clone();
        }

        // 应用平滑处理 - 使用lerp进行线性插值
        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);
        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);

        // 设置相机位置为平滑后的位置
        camera.position.copy(lastCameraPosition.current);

        // 重置相机方向
        camera.up.set(0, 1, 0);

        // 设置相机观察点为平滑后的目标
        camera.lookAt(lastCameraTarget.current);

        // 强制更新相机矩阵
        camera.updateProjectionMatrix();
        camera.updateMatrix();
        camera.updateMatrixWorld(true);

        // 禁用控制器
        controls.enabled = false;

        // 确保控制器不会覆盖相机设置
        controls.target.copy(lastCameraTarget.current);
        controls.update();

        console.log('相机设置:', {
          车辆位置: vehiclePos.toArray(),
          相机位置: camera.position.toArray(),
          相机目标: lastCameraTarget.current.toArray(),
          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()
        });
      } else if (cameraMode === 'global') {
        // 在全局模式或切换模式时重置平滑变量
        lastCameraPosition.current = null;
        lastCameraTarget.current = null;

        // 在全局模式下启用控制器
        controls.enabled = true;

        // 确保相机的up向量保持正确
        camera.up.set(0, 1, 0);

        // 如果相机位置偏离太多，重置到默认位置
        if (Math.abs(camera.position.y) < 50) {
          camera.position.set(0, 300, 0);
          controls.target.set(0, 0, 0);
          camera.lookAt(controls.target);
          controls.update();
        }

        //         // 强制更新相机矩阵
        // camera.updateProjectionMatrix();
        camera.updateMatrix();
        camera.updateMatrixWorld(true);

      } else if (cameraMode === 'intersection') {
        // 在路口视角模式下也重置平滑变量
        lastCameraPosition.current = null;
        lastCameraTarget.current = null;

        // 路口视角模式
        controls.update();
      }

      if (controls) controls.update();
      if (scene && camera) {
        renderer.render(scene, camera);
      }
    };

    animate();

    // 处理窗口大小变化
    const handleResize = () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    };
    window.addEventListener('resize', handleResize);

    // 添加事件清理定时器
    const eventCleanupInterval = setInterval(() => {
      removeInactiveEvents();
    }, 30000); // 每30秒清理一次非活跃事件

    // 添加全局函数用于手动切换视角
    window.setGlobalView = () => {
      if (cameraRef.current) {
        cameraRef.current.position.set(0, 300, 0);
        cameraRef.current.lookAt(0, 0, 0);
        cameraRef.current.updateMatrix();
        cameraRef.current.updateMatrixWorld(true);

        if (controls) {
          controls.target.set(0, 0, 0);
          controls.enabled = true;
          controls.update();
        }

        cameraMode = 'global';
        console.log('手动切换到全局视角');
        return true;
      }
      return false;
    };

    // // 添加全局测试函数用于验证事件去重功能
    // window.test3DEventDeduplication = () => {
    //   console.log('🧪 开始3D场景事件去重测试');

    //   // 测试位置
    //   const testPosition = { x: 100, y: 100 };

    //   // 测试1：创建新事件
    //   console.log('测试1：创建新的违停车辆事件');
    //   showWarningMarker(testPosition, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });

    //   // 测试2：相同位置的重复事件（应该被去重）
    //   setTimeout(() => {
    //     console.log('测试2：相同位置的重复事件（应该被去重）');
    //     showWarningMarker(testPosition, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });
    //   }, 1000);

    //   // 测试3：稍微不同位置的事件（距离在阈值内，应该被去重）
    //   setTimeout(() => {
    //     console.log('测试3：稍微不同位置的事件（应该被去重）');
    //     showWarningMarker({ x: 105, y: 105 }, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });
    //   }, 2000);

    //   // 测试4：不同类型的事件（应该创建新事件）
    //   setTimeout(() => {
    //     console.log('测试4：不同类型的事件（应该创建新事件）');
    //     showWarningMarker(testPosition, '逆行车辆', '#eb2f96', '904', { rsuId: 'TEST_RSU' });
    //   }, 3000);

    //   // 测试5：查看缓存状态
    //   setTimeout(() => {
    //     console.log('📊 3D场景事件缓存状态:', {
    //       缓存事件数: eventListCache.length,
    //       事件ID计数器: eventIdCounter,
    //       场景标记数: eventMarkers.size,
    //       事件列表: eventListCache.map(e => ({
    //         ID: e.eventId,
    //         类型: e.eventType,
    //         更新次数: e.updateCount,
    //         位置: `${e.position.lat.toFixed(6)}, ${e.position.lng.toFixed(6)}`
    //       }))
    //     });
    //   }, 4000);
    // };

    // 添加手动清理3D事件的全局函数
    window.cleanup3DEvents = () => {
      console.log('🧹 手动清理3D场景事件');
      removeInactiveEvents();
    };

    // 修改清理函数
    return () => {
      console.log('开始清理组件...');

      // 1. 停止渲染循环
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }

      // 2. 清理事件清理定时器
      if (eventCleanupInterval) {
        clearInterval(eventCleanupInterval);
      }

      // 3. 清理所有事件标记
      eventMarkers.forEach((marker) => {
        if (scene && marker) {
          scene.remove(marker);
        }
      });
      eventMarkers.clear();
      eventListCache.length = 0;

      // 4. 停止所有 TWEEN 动画
      TWEEN.removeAll();

      // 3. 清理所有动画混合器
      peopleAnimationMixers.forEach(mixer => {
        resourceManager.removeMixer(mixer);
      });
      peopleAnimationMixers.clear();

      // 4. 清理所有其他资源
      resourceManager.cleanup();

      // 5. 清理场景中的所有对象
      if (scene) {
        const objectsToRemove = [];
        scene.traverse((object) => {
          if (object.isMesh) {
            if (object.geometry) {
              object.geometry.dispose();
            }
            if (object.material) {
              if (Array.isArray(object.material)) {
                object.material.forEach(material => {
                  if (material.map) material.map.dispose();
                  material.dispose();
                });
              } else {
                if (object.material.map) object.material.map.dispose();
                object.material.dispose();
              }
            }
            if (object !== scene) {
              objectsToRemove.push(object);
            }
          }
        });

        // 从场景中移除对象
        objectsToRemove.forEach(obj => {
          if (obj.parent) {
            obj.parent.remove(obj);
          }
        });

        scene.clear();
      }

      // 6. 清理渲染器
      if (renderer) {
        renderer.setAnimationLoop(null);
        if (containerRef.current && renderer.domElement && renderer.domElement.parentNode === containerRef.current) {
          containerRef.current.removeChild(renderer.domElement);
        }
        renderer.dispose();
        renderer.forceContextLoss();
      }

      // 7. 清理其他资源
      if (controls) {
        controls.dispose();
      }

      // 8. 清理数据结构
      vehicleLastPositions.clear();
      vehicleLastRotations.clear();
      deviceTimestamps.clear();
      vehicleModels.clear();
      trafficLightsMap.clear();
      trafficLightStates.clear();

      console.log('组件清理完成');
    };
  }, []);

  // 在组件挂载时获取主车信息和添加事件监听
  useEffect(() => {
    // 初始获取主车信息
    fetchMainVehicleBsmId();

    // 添加自定义事件监听，用于接收主车变更通知
    const handleMainVehicleChange = () => {
      console.log('接收到主车变更通知，重新获取主车信息');
      fetchMainVehicleBsmId();
    };

    // 监听主车变更事件
    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);

    // 定时刷新主车信息（每分钟一次）
    const intervalId = setInterval(() => {
      fetchMainVehicleBsmId();
    }, 60000);

    // 组件卸载时清理事件监听和定时器
    return () => {
      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);
      clearInterval(intervalId);
    };
  }, []);

  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯
  useEffect(() => {
    // 在场景加载后初始化红绿灯
    if (scene && converter.current && intersections && intersections.length > 0) {
      // 延迟一秒创建红绿灯，确保模型已加载
      const timer = setTimeout(() => {
        if (scene && converter.current && intersections && intersections.length > 0) {  // 再次检查，以防延迟期间组件卸载
          createTrafficLights(converter.current, intersections);
        }
      }, 2000);

      return () => clearTimeout(timer);
    } else {
      console.log('场景、坐标转换器或路口数据未准备好，暂不创建红绿灯');
    }
  }, [scene, intersections]);

  // 添加点击事件处理
  useEffect(() => {
    if (containerRef.current) {
      // 定义点击处理函数
      const handleClick = (event) => {
        if (scene && cameraRef.current) {
          handleMouseClick(event, containerRef.current, scene, cameraRef.current);
        }
      };

      // 添加点击事件监听
      containerRef.current.addEventListener('click', handleClick);

      // 记录到控制台
      console.log('已添加点击事件监听器到容器', !!containerRef.current);

      // 清理函数
      return () => {
        if (containerRef.current) {
          containerRef.current.removeEventListener('click', handleClick);
          console.log('已移除点击事件监听器');
        }
      };
    }
  }, [scene, cameraRef.current]);

  // 初始化场景 - 简化为空函数，避免引用错误
  const initScene = useCallback(() => {
    console.log('initScene函数已禁用');
    // 原始实现已移除，避免canvasRef未定义的错误
  }, [containerRef, setCurrentRSU, trafficLightsMap]);

  // 创建简单交通灯模型
  const createSimpleTrafficLight = () => {
    const geometry = new THREE.BoxGeometry(4, 15, 4);
    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });
    const trafficLightModel = new THREE.Mesh(geometry, material);

    // 添加基座
    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);
    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });
    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);
    baseModel.position.set(0, -8.5, 0);
    trafficLightModel.add(baseModel);

    return trafficLightModel;
  };

  // 添加额外的点击检测辅助对象
  const addClickHelpers = () => {
    if (!scene) return;

    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击
    trafficLightsMap.forEach((lightObj, interId) => {
      if (lightObj.model) {
        // 创建一个较大的碰撞检测几何体
        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);
        const helperMaterial = new THREE.MeshBasicMaterial({
          color: 0xff00ff,//
          transparent: false,
          opacity: 0.1,  // 几乎透明
          depthWrite: false
        });

        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);
        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置

        // 标记为click helper
        helperMesh.userData = {
          type: 'trafficLight',
          interId: interId,
          name: lightObj.intersection.name,
          isClickHelper: true
        };

        // 添加到红绿灯模型
        lightObj.model.add(helperMesh);

        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);
      }
    });
  };

  // 在创建红绿灯之后调用
  useEffect(() => {
    // 等待红绿灯创建完成后添加点击辅助对象
    const timer = setTimeout(() => {
      if (trafficLightsMap.size > 0) {
        console.log('添加红绿灯点击辅助对象');
        // addClickHelpers();
      }
    }, 5500);  // 延迟略长于debugTrafficLights

    return () => clearTimeout(timer);
  }, []);

  // // 在组件加载完毕后调用调试函数
  // useEffect(() => {
  //   // 延迟5秒调用调试函数，确保所有模型都已加载
  //   const timer = setTimeout(() => {
  //     console.log('调用场景调试函数');
  //     if (window.debugScene) {
  //       window.debugScene(); // 使用全局函数
  //     } else {
  //       console.error('debugScene函数未定义');
  //     }
  //   }, 5000);

  //   return () => clearTimeout(timer);
  // }, []);

  // 在useEffect中添加定时器清理逻辑
  useEffect(() => {
    return () => {
      // 组件卸载时清理定时器
      if (trafficLightUpdateTimerRef.current) {
        clearInterval(trafficLightUpdateTimerRef.current);
        trafficLightUpdateTimerRef.current = null;
        console.log('已清理红绿灯状态更新定时器');
      }
    };
  }, []); // 空依赖数组确保只在组件挂载和卸载时运行

  // 添加关闭弹窗时清理定时器的逻辑
  useEffect(() => {
    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {
      clearInterval(trafficLightUpdateTimerRef.current);
      trafficLightUpdateTimerRef.current = null;
      currentPopoverIdRef.current = null;
      console.log('弹窗关闭，已清理红绿灯状态更新定时器');
    }
  }, [trafficLightPopover.visible]);

  // 添加自动选择第一个路口的逻辑
  useEffect(() => {
    // 确保路口数据已加载
    if (intersections && intersections.length > 0) {
      // 确保只在组件初次渲染并且未选择路口时执行
      if (!selectedIntersection) {
        // 查找第一个带有红绿灯的路口
        const firstTrafficLightIntersection = intersections.find(
          intersection => intersection.hasTrafficLight !== false && intersection.interId
        );

        // 如果找到带红绿灯的路口，优先选择它；否则选择第一个路口
        const targetIntersection = firstTrafficLightIntersection || intersections[0];

        console.log('自动选择路口:', targetIntersection.name,
                    '具有红绿灯:', firstTrafficLightIntersection ? '是' : '否');

        // 延迟执行，确保场景和相机已初始化
        const timer = setTimeout(() => {
          handleIntersectionChange(targetIntersection.name);
        }, 2000);

        return () => clearTimeout(timer);
      }
    }
  }, [intersections, selectedIntersection]);

  // 新增：在场景初始化后渲染所有路口entrance的设备图标
  const renderEntranceDeviceIcons = (converterInstance) => {
    if (!scene || typeof scene.traverse !== 'function' || !converterInstance) {
      console.warn('设备图标渲染条件不满足:', {
        scene: !!scene,
        sceneTraverse: scene && typeof scene.traverse === 'function',
        converter: !!converterInstance
      });
      return;
    }

    if (!devicesData.devices || devicesData.devices.length === 0) {
      console.warn('设备数据未加载，跳过设备图标渲染');
      return; // 设备数据未加载时不渲染
    }

    if (!intersections || intersections.length === 0) {
      console.warn('路口数据未加载，跳过设备图标渲染');
      return;
    }

    console.log('开始渲染设备图标:', {
      设备总数: devicesData.devices.length,
      路口总数: intersections.length
    });

    try {
      // 清理之前的设备图标
      const existingIcons = scene.children.filter(obj => obj.userData && obj.userData.isEntranceDeviceIcons);
      existingIcons.forEach(obj => scene.remove(obj));
      console.log('清理了', existingIcons.length, '个旧的设备图标');

      let totalRenderedDevices = 0;

      intersections.forEach(intersection => {
        if (!intersection.entrances || !Array.isArray(intersection.entrances)) return;
        intersection.entrances.forEach((entrance) => {
          if (!entrance.longitude || !entrance.latitude || isNaN(parseFloat(entrance.longitude)) || isNaN(parseFloat(entrance.latitude))) {
            return;
          }

          const devices = devicesData.devices.filter(
            d => d.location === intersection.name && d.entrance === entrance.name
          );
          if (devices.length === 0) return;

          // 经纬度转模型坐标
          console.log('渲染路口', intersection.name, '入口', entrance.name, '设备数量', devices.length);
          const modelPos = converterInstance.wgs84ToModel(
            parseFloat(entrance.longitude),
            parseFloat(entrance.latitude)
          );
          // 创建一个组用于存放所有图标
          const group = new THREE.Group();
          group.position.set(modelPos.x, 10, -modelPos.y); // 上方10米
          group.userData = { isEntranceDeviceIcons: true };
          // 图标排成一排，居中
          const iconSize = 32; // px
          const iconSpacing = 8; // px
          const totalWidth = devices.length * iconSize + (devices.length - 1) * iconSpacing;
          // 以三维单位为准，假设1单位=1米，24px约等于0.6米
          const size3D = 4.0; // 图标尺寸加大
          const spacing3D = 0.5; // 图标间距加大
          const startX = -((devices.length - 1) * (size3D + spacing3D)) / 2;
          devices.forEach((device, idx) => {
            // 创建一个平面用于显示SVG图标
            const textureLoader = new THREE.TextureLoader();
            const iconPath = `${BASE_URL}/images/${device.type}.svg`;
            // 图标材质
            const iconMaterial = new THREE.MeshBasicMaterial({
              map: textureLoader.load(iconPath),
              transparent: true,
              opacity: 1 // 图标完全不透明
            });
            // 图标背景板材质
            const bgMaterial = new THREE.MeshBasicMaterial({
              color: 0x000000,
              transparent: true,
              opacity: 0.7 // 半透明黑色
            });
            // 背景板尺寸略大于图标
            const bgWidth = size3D * 1.25;
            const bgHeight = size3D * 1.25;
            // 背景板几何体（圆角矩形）
            // 由于Three.js没有内置圆角矩形，使用PlaneGeometry近似
            const bgGeometry = new THREE.PlaneGeometry(bgWidth, bgHeight);
            const bgMesh = new THREE.Mesh(bgGeometry, bgMaterial);
            // 图标几何体
            const iconGeometry = new THREE.PlaneGeometry(size3D, size3D);
            const iconMesh = new THREE.Mesh(iconGeometry, iconMaterial);
            // 图标略微前移，避免Z轴重叠闪烁
            iconMesh.position.set(0, 0, 0.01);
            // 创建一个组，包含背景和图标
            const iconGroup = new THREE.Group();
            iconGroup.add(bgMesh);
            iconGroup.add(iconMesh);
            // 整体平移到正确位置
            iconGroup.position.set(startX + idx * (size3D + spacing3D), 0, 0);
            // 不再固定旋转角度，后续在动画循环中让其始终面向相机
            iconGroup.renderOrder = 999; // 提高渲染优先级
            iconGroup.userData = {
              deviceId: device.id,
              deviceType: device.type,
              entrance: entrance.name,
              isEntranceDeviceIcon: true
            };
            group.add(iconGroup);
          });
          // 新增：添加白色半透明光柱，指向设备图标组
          // 光柱高度等于图标组的y坐标（即10），底部在地面y=0，顶部在图标组中心
          const pillarHeight =  group.position.y; // 10
          const pillarGeometry = new THREE.CylinderGeometry(0.2, 0.2, pillarHeight, 16);
          const pillarMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff, transparent: true, opacity: 0.7 });
          const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial);
          // 设置光柱中心在y=0~y=10之间，底部正好在地面
          pillar.position.set(0, -pillarHeight / 2, 0);
          // pillar.position.set(0, -pillarHeight, 0);
          // 可选：添加标记，便于后续查找或清理
          pillar.userData = { isEntranceDevicePillar: true };
          group.add(pillar);
          scene.add(group);
        });
      });

    } catch (e) {
      console.error('renderEntranceDeviceIcons error', e);
      return;
    }
  };

  // 在场景初始化后调用渲染设备图标
  useEffect(() => {
    if (scene && typeof scene.traverse === 'function' && converter.current && devicesData.devices) {
      console.log('触发设备图标渲染:', {
        scene: !!scene,
        converter: !!converter.current,
        devicesCount: devicesData.devices?.length || 0,
        intersectionsCount: intersections?.length || 0
      });

      // 延迟渲染，确保所有资源都已准备好
      const timer = setTimeout(() => {
        renderEntranceDeviceIcons(converter.current);
      }, 500); // 延迟500ms

      return () => clearTimeout(timer);
    }
  }, [scene, converter.current, devicesData.devices, intersections]); // 添加intersections依赖

  // 添加额外的重试机制，确保设备图标能够正确渲染
  useEffect(() => {
    if (scene && converter.current && devicesData.devices && intersections) {
      // 延迟3秒后检查是否有设备图标，如果没有则重新渲染
      const retryTimer = setTimeout(() => {
        const existingIcons = scene.children.filter(obj => obj.userData && obj.userData.isEntranceDeviceIcons);
        if (existingIcons.length === 0) {
          console.log('检测到设备图标缺失，执行重试渲染');
          renderEntranceDeviceIcons(converter.current);
        } else {
          console.log('设备图标渲染正常，共', existingIcons.length, '个图标组');
        }
      }, 3000); // 3秒后检查

      return () => clearTimeout(retryTimer);
    }
  }, [scene, converter.current, devicesData.devices, intersections]);

  // 新增：每帧让所有设备图标组和事件图标组始终面向相机（billboard效果）
  useEffect(() => {
    if (!scene || !cameraRef.current) return;
    const animateBillboard = () => {
      // 遍历所有图标组，让其正对相机
      scene.children.forEach(obj => {
        // 设备图标组
        if (obj.userData && obj.userData.isEntranceDeviceIcons) {
          obj.lookAt(cameraRef.current.position.x, obj.position.y, cameraRef.current.position.z);
        }
        // 事件图标组
        if (obj.userData && obj.userData.type === 'eventMarker') {
          obj.lookAt(cameraRef.current.position.x, obj.position.y, cameraRef.current.position.z);
        }
      });
      requestAnimationFrame(animateBillboard);
    };
    animateBillboard();
  }, [scene]);

  // 修改点击处理函数
  const handleMouseClick = (event, container, sceneInstance, cameraInstance) => {
    if (!container || !sceneInstance || !cameraInstance) return;
    const rect = container.getBoundingClientRect();
    const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;
    const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;
    const raycaster = new THREE.Raycaster();
    raycaster.params.Points.threshold = 1;
    raycaster.params.Line.threshold = 1;
    const mouseVector = new THREE.Vector2(mouseX, mouseY);
    raycaster.setFromCamera(mouseVector, cameraInstance);
    const intersects = raycaster.intersectObjects(sceneInstance.children, true);
    if (intersects.length > 0) {
      for (let i = 0; i < intersects.length; i++) {
        const obj = intersects[i].object;
        if (obj.parent && obj.parent.userData && obj.parent.userData.isEntranceDeviceIcon) {
          const deviceId = obj.parent.userData.deviceId;
          console.log('devicesDatahandleMouseClick', devicesData);
          const device = devicesData.devices.find(d => d.id === deviceId);
          if (device) {
            const x = event.clientX;
            const y = event.clientY;
            setDevicePopover({
              visible: true,
              deviceId,
              position: { x, y },
              content: renderDevicePopoverContent(device)
            });
            return; // 命中设备图标后直接返回
          }
        }
      }
    }
    // ...原有红绿灯弹框逻辑保持不变...
    // ... existing code ...
  };




  return (
    <>
      <span style={labelStyle}>区域选择：</span>
      <Select
        style={intersectionSelectStyle}
        placeholder="请选择区域位置"
        onChange={handleIntersectionChange}
        onSelect={handleIntersectionChange} // 添加onSelect事件，确保每次选择都触发
        options={intersections.map(intersection => ({
          value: intersection.name,
          label: intersection.name
        }))}
        size="large"
        bordered={true}
        dropdownStyle={{
          zIndex: 1002,
          maxHeight: '300px'
        }}
        value={selectedIntersection ? selectedIntersection.name : undefined}
      />
      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />

      {/* 添加红绿灯状态弹出窗口 */}
      {trafficLightPopover.visible && (
        <div
          style={{
            position: 'absolute',
            left: `${trafficLightPopover.position.x}px`,
            top: `${trafficLightPopover.position.y}px`,
            transform: 'translate(-50%, -100%)',
            zIndex: 1003,
            backgroundColor: 'rgba(0, 0, 0, 0.85)',
            color: 'white',
            borderRadius: '4px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
            padding: '0',
            maxWidth: '240px', // 缩小最大宽度
            fontSize: '12px' // 缩小字体
          }}
        >
          {trafficLightPopover.content}
          <button
            style={{
              position: 'absolute',
              top: '0px',
              right: '0px',
              background: 'none',
              border: 'none',
              color: 'white',
              fontSize: '12px',
              cursor: 'pointer',
              padding: '2px 6px'
            }}
            onClick={() => handleClosePopover(setTrafficLightPopover)}
          >
            ×
          </button>
        </div>
      )}

      <div style={buttonContainerStyle}>
        <button
          style={{
            ...buttonStyle,
            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',
            color: viewMode === 'follow' ? 'white' : 'black'
          }}
          onClick={switchToFollowView}
        >
          跟随视角
        </button>
        <button
          style={{
            ...buttonStyle,
            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',
            color: viewMode === 'global' ? 'white' : 'black'
          }}
          onClick={switchToGlobalView}
        >
          全局视角
        </button>
      </div>
      {devicePopover.visible && (
        <div
          style={{
            position: 'absolute',
            left: `${devicePopover.position.x}px`,
            top: `${devicePopover.position.y}px`,
            transform: 'translate(-50%, -100%)',
            zIndex: 1100,
            backgroundColor: 'rgba(0, 0, 0, 0.92)',
            color: 'white',
            borderRadius: '6px',
            boxShadow: '0 2px 12px rgba(0,0,0,0.35)',
            padding: 0,
            minWidth: 320,
            maxWidth: 350,
            fontSize: 13
          }}
        >
          {devicePopover.content}
          <button
            style={{
              position: 'absolute',
              top: '0px',
              right: '0px',
              background: 'none',
              border: 'none',
              color: 'white',
              fontSize: '16px',
              cursor: 'pointer',
              padding: '2px 10px',
              zIndex: 1200
            }}
            onClick={handleCloseDevicePopover}
          >×</button>
        </div>
      )}
    </>
  );
};

// 添加创建文字精灵的辅助函数
function createTextSprite(text, parameters = {}) {
  const params = {
    fontFace: parameters.fontFace || 'Arial',
    fontSize: parameters.fontSize || 12, // 从24px调小到16px
    fontWeight: parameters.fontWeight || 'bold',
    borderThickness: parameters.borderThickness || 4,
    borderColor: parameters.borderColor || { r: 0, g: 0, b: 0, a: 1.0 },
    backgroundColor: parameters.backgroundColor || { r: 255, g: 255, b: 255, a: 0.8 },
    textColor: parameters.textColor || { r: 0, g: 0, b: 0, a: 1.0 },
    padding: parameters.padding || 5
  };

  // 创建画布
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');

  // 设置字体
  // context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;

  // 测量文本宽度
  const textWidth = context.measureText(text).width;

  // 设置画布尺寸，考虑边框和填充
  const width = textWidth + 4 * params.padding + 4 * params.borderThickness;
  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;

  canvas.width = width;
  canvas.height = height;

  // 重新设置字体，因为改变画布尺寸会重置上下文
  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;
  context.textBaseline = 'middle';

  // // 只有在有边框或背景时才绘制背景和边框
  if (params.borderThickness > 0 || params.backgroundColor.a > 0) {
   // 绘制背景和边框（圆角矩形）
     const radius = 8;
    context.beginPath();
    context.moveTo(params.borderThickness + radius, params.borderThickness);
    context.lineTo(width - params.borderThickness - radius, params.borderThickness);
    context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);
    context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);
    context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);
    context.lineTo(params.borderThickness + radius, height - params.borderThickness);
    context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);
    context.lineTo(params.borderThickness, params.borderThickness + radius);
    context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);
    context.closePath();

    // 设置背景填充（如果有背景）
    if (params.backgroundColor.a > 0) {
      context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;
      context.fill();
    }

    // 设置边框颜色（如果有边框）
    // console.log('边框', params.borderThickness);
    if (parameters.borderThickness != 0) {
      context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;
      context.lineWidth = params.borderThickness;
      context.stroke();
    }
  }

  // 设置文字颜色
  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;
  context.textAlign = 'center';

  // 绘制文本
  context.fillText(text, width / 2, height / 2);

  // 创建纹理
  const texture = new THREE.CanvasTexture(canvas);
  texture.minFilter = THREE.LinearFilter;
  texture.needsUpdate = true;

  // 创建精灵材质
  const spriteMaterial = new THREE.SpriteMaterial({
    map: texture,
    transparent: true
  });

  // 创建精灵
  const sprite = new THREE.Sprite(spriteMaterial);
  // sprite.scale.set(10, 5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%
  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%
  sprite.material.depthTest = false; // 确保始终可见

  // 保存文本信息到userData，便于后续更新
  sprite.userData = {
    text: text,
    params: params
  };

  return sprite;
}



// 在文件底部添加这个全局函数
window.forceGlobalView = () => {
  try {
    // 获取当前场景中的相机
    const camera = document.querySelector('canvas').parentElement.__THREE_camera;
    if (camera) {
      // 保存旧位置
      const oldPos = camera.position.clone();

      // 设置新位置
      camera.position.set(0, 300, 0);
      camera.up.set(0, 1, 0);
      camera.lookAt(0, 0, 0);

      // 更新矩阵
      camera.updateMatrix();
      camera.updateMatrixWorld(true);

      // 更新控制器
      const controls = document.querySelector('canvas').parentElement.__THREE_controls;
      if (controls) {
        controls.target.set(0, 0, 0);
        controls.update();
      }

      console.log('强制设置全局视角成功', {
        旧位置: oldPos.toArray(),
        新位置: camera.position.toArray()
      });

      return true;
    }
    return false;
  } catch (e) {
    console.error('强制设置全局视角失败', e);
    return false;
  }
};


// 修改车辆模型预加载函数
const preloadModels = async () => {
  try {
    console.log('开始预加载所有模型...');
    const loader = new GLTFLoader();

    // 并行加载所有模型
    try {
      const [ trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([
        loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`),
      loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`),
      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),
        loader.loadAsync(`${BASE_URL}/changli2/people.glb`)

    ]);



    // 处理机动车模型
    console.log('加载车辆模型...');
    preloadedVehicleModel = vehicleGltf.scene;
    preloadedVehicleModel.traverse((child) => {
      if (child.isMesh) {
          const newMaterial = new THREE.MeshStandardMaterial({
          color: 0xff0000,  // 0xff0000, //红色 0xffffff,白色
          metalness: 0.2,
          roughness: 0.1,
          envMapIntensity: 1.0
        });

          // 保留原始贴图
          if (child.material.map) {
            newMaterial.map = child.material.map;
          }
          child.materia = newMaterial;
      }
    });

    console.log('加载非机动车模型...');
    // 处理非机动车模型
    preloadedCyclistModel = cyclistGltf.scene;
    // 设置非机动车模型的缩放
    preloadedCyclistModel.scale.set(2, 2, 2);
    // 保持原始材质
    preloadedCyclistModel.traverse((child) => {
      if (child.isMesh && child.material) {
        // 只调整材质属性，保持原始颜色
        child.material.metalness = 0.1;
        child.material.roughness = 0.8;
        child.material.envMapIntensity = 1.0;
      }
    });

    console.log('加载行人模型...');
    // 处理行人模型
    preloadedPeopleModel = peopleGltf.scene;
    // 设置行人模型的缩放
    // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);
    // 保持原始材质
    preloadedPeopleModel.traverse((child) => {
      if (child.isMesh && child.material) {
        // 只调整材质属性，保持原始颜色
        child.material.metalness = 0.1;
        child.material.roughness = 0.8;
        child.material.envMapIntensity = 1.0;

      }
      if (child.isMesh){
        child.castShadow = true;
      }
    });



    // 保存行人动画数据
    console.log('找到行人动画sss:', peopleGltf.animations.length, '个');
    if (peopleGltf.animations && peopleGltf.animations.length > 0) {
      console.log('找到行人动画:', peopleGltf.animations.length, '个');
      peopleBaseModel = peopleGltf;
    } else {
      console.warn('行人模型没有包含动画数据');
    }

    console.log('加载红绿灯模型...');

    // 处理红绿灯模型
    preloadedTrafficLightModel = trafficLightGltf.scene;
    console.log('红绿灯模型：', preloadedTrafficLightModel);
    // 设置红绿灯模型的缩放
    preloadedTrafficLightModel.scale.set(6, 6, 6);
    // 保持原始材质
    preloadedTrafficLightModel.traverse((child) => {
      if (child.isMesh && child.material) {
        // 设置材质属性
        child.material.metalness = 0.2;
        child.material.roughness = 0.3;
        child.material.envMapIntensity = 1.2;
    }
  });

    console.log('所有模型预加载成功');
    } catch (error) {
      console.error('加载特定模型失败，尝试单独加载:', error);

      // 如果整个Promise.all失败，尝试单独加载关键模型
      try {
        if (!preloadedVehicleModel) {
          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);
          preloadedVehicleModel = vehicleGltf.scene;
        }

        // // 尝试单独加载红绿灯模型
        // if (!preloadedTrafficLightModel) {
        //   console.log('正在单独加载红绿灯模型...');
        //   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);
        //   preloadedTrafficLightModel = trafficLightGltf.scene;
        //   preloadedTrafficLightModel.scale.set(3, 3, 3);
        //   console.log('红绿灯模型加载成功');
        // }
      } catch (err) {
        console.error('单独加载模型也失败:', err);
      }
    }
  } catch (error) {
    console.error('模型预加载失败:', error);
  }
};

// 添加辅助函数：获取优先类型文本
const getPriorityTypeText = (type) => {
  const types = {
    '1': '信号灯保持',
    '2': '绿灯延长',
    '3': '红灯截断',
    '4': '相位插入',
    '5': '相位插入',
    '6': '优先未处理'
  };
  return types[type] || '未知类型';
};

// 添加辅助函数：显示警告标记
// 添加计算两点之间距离的函数
const calculateDistance = (x1, y1, x2, y2) => {

  const c = Math.sqrt((x1-x2)*(x1-x2)+(y1-y2)*(y1-y2));
  return  c;
};

// 获取事件类型的阈值配置
const getEventThresholds = (eventType) => {
  switch(eventType) {
    case '910': // 违停车辆
      return { timeThreshold: 300000, distanceThreshold: 10 }; // 5分钟, 20米
    case '904': // 逆行车辆
      return { timeThreshold: 10000, distanceThreshold: 20 }; // 10秒, 20米
    case '901': // 车辆超速
      return { timeThreshold: 30000, distanceThreshold: 20 }; // 30秒, 50米
    case '401': // 道路抛洒物
    case '404': // 道路障碍物
    case '1002': // 道路施工
      return { timeThreshold: 600000, distanceThreshold: 10 }; // 10分钟, 30米
    case '405': // 行人通过马路
      return { timeThreshold: 10000, distanceThreshold: 10 }; // 10秒, 10米
    default:
      return { timeThreshold: 5000, distanceThreshold: 5 }; // 5秒, 5米
  }
};

// 优化后的事件重复检查逻辑
const checkDuplicateEvent = (eventType, eventKey, currentTime, currentPos) => {
  const { timeThreshold, distanceThreshold } = getEventThresholds(eventType);

  // 遍历事件列表缓存中的所有事件
  for (let i = 0; i < eventListCache.length; i++) {
    const cachedEvent = eventListCache[i];

    // 检查事件类型是否相同
    if (cachedEvent.eventType !== eventType) {
      continue;
    }

    // 计算时间差
    const timeDiff = currentTime - cachedEvent.lastUpdateTime;

    // 检查时间差是否在阈值内
    if (timeDiff > timeThreshold) {
      continue;
    }

    // 计算距离
    const distance = calculateDistance(
      currentPos.x, currentPos.y,
      cachedEvent.position.x, cachedEvent.position.y
    );

    // 检查距离是否在阈值内
    if (distance <= distanceThreshold) {
      // 找到匹配的事件，更新信息
      cachedEvent.eventKey = eventKey;
      cachedEvent.lastUpdateTime = currentTime;
      cachedEvent.position = { ...currentPos };
      cachedEvent.updateCount = (cachedEvent.updateCount || 1) + 1;

      // console.log(`🔄 3D场景检测到重复事件 ${eventType} (ID: ${cachedEvent.eventId})，时间差: ${(timeDiff/1000).toFixed(1)}s，距离: ${distance.toFixed(1)}m，更新次数: ${cachedEvent.updateCount}`);

      return {
        isDuplicate: true,
        eventId: cachedEvent.eventId,
        matchedEvent: cachedEvent
      };
    }
  }

  // 没有找到匹配的事件，创建新事件
  const newEventId = `3D_EVT_${eventIdCounter.toString().padStart(6, '0')}`;
  eventIdCounter++;

  const newEvent = {
    eventId: newEventId,
    eventType: eventType,
    eventKey: eventKey,
    firstDetectedTime: currentTime,
    lastUpdateTime: currentTime,
    position: { ...currentPos },
    updateCount: 1
  };

  // 添加到事件列表缓存
  eventListCache.push(newEvent);



  return {
    isDuplicate: false,
    eventId: newEventId,
    newEvent: newEvent
  };
};

// 删除2s内没有更新的事件
const removeInactiveEvents = () => {
  const currentTime = Date.now();
  const inactiveThreshold = 2000; // 2s

  const initialCount = eventListCache.length;
  const removedEvents = [];

  eventListCache = eventListCache.filter(event => {
    const timeSinceLastUpdate = currentTime - event.lastUpdateTime;
    if (timeSinceLastUpdate > inactiveThreshold) {
      removedEvents.push({
        id: event.eventId,
        type: event.eventType,
        inactiveTime: (timeSinceLastUpdate / 1000).toFixed(1)
      });

      // 从场景中移除对应的标记
      const marker = eventMarkers.get(event.eventId);
      if (marker && scene) {
        scene.remove(marker);
        eventMarkers.delete(event.eventId);
      }

      return false; // 删除该事件
    }
    return true; // 保留该事件
  });

  const removedCount = initialCount - eventListCache.length;
  if (removedCount > 0) {
    console.log(`🗑️ 3D场景删除了 ${removedCount} 个1s内未更新的事件:`);
    removedEvents.forEach(event => {
      console.log(`   - 事件 ${event.id} (类型: ${event.type})，未更新时间: ${event.inactiveTime}秒`);
    });
    console.log(`📊 3D场景当前缓存事件数: ${eventListCache.length}`);
  }
};

// 根据事件类型获取背景颜色
const getEventBackgroundColor = (eventType) => {
  switch(eventType) {
    case '910': // 违停车辆
      return 0xff4d4f; // 红色背景 - 严重违规
    case '904': // 逆行车辆
      return 0xf5222d; // 深红色背景 - 危险行为
    case '901': // 车辆超速
      return 0xfa8c16; // 橙色背景 - 警告
    case '401': // 道路抛洒物
      return 0xfaad14; // 黄色背景 - 注意
    case '404': // 道路障碍物
      return 0xff7a45; // 橙红色背景 - 阻碍
    case '1002': // 道路施工
      return 0x1890ff; // 蓝色背景 - 信息
    case '405': // 行人通过马路
      return 0x52c41a; // 绿色背景 - 正常
    case '2': // 交叉路口碰撞预警
      return 0xff0000; // 纯红色背景 - 紧急
    case '12': // 交通参与者碰撞预警
      return 0xeb2f96; // 粉红色背景 - 预警
    case '13': // 绿波车速引导
      return 0x13c2c2; // 青色背景 - 引导
    case '999': // 信号灯优先
      return 0x722ed1; // 紫色背景 - 优先
    default:
      return 0xffa500; // 默认橙黄色背景
  }
};

// 获取事件类型的中文名称
const getEventTypeName = (eventType) => {
  switch(eventType) {
    case '401': return '抛洒物';
    case '404': return '障碍物';
    case '405': return '行人';
    case '904': return '逆行';
    case '910': return '违停';
    case '1002': return '施工';
    case '901': return '超速';
    case '2': return '碰撞';
    case '12': return '碰撞';
    case '13': return '绿波';
    case '999': return '优先';
    default: return `事件${eventType}`;
  }
};

// 创建事件图标标记
const createEventMarker = (eventType, position, eventId) => {
  if (!scene) {
    console.warn('无法创建事件标记：场景不存在或已卸载');
    return null;
  }

  try {
    // 创建一个组来包含背景和图标
    const markerGroup = new THREE.Group();

    // 1. 创建统一的背景平面（包含图标和文字区域，类似截图中的设计）
    const backgroundGeometry = new THREE.PlaneGeometry(6, 8); // 适当增加宽度和高度以容纳图标和文字
    const backgroundColor = getEventBackgroundColor(eventType); // 根据事件类型获取背景色
    const backgroundMaterial = new THREE.MeshBasicMaterial({
      color: backgroundColor, // 使用事件类型对应的背景颜色
      transparent: true,
      opacity: 0.9, // 提高不透明度，使颜色更加鲜明
      side: THREE.DoubleSide
    });
    const backgroundMesh = new THREE.Mesh(backgroundGeometry, backgroundMaterial);
    backgroundMesh.position.set(0, -1, -0.01); // 向下偏移以居中整个标记，稍微向后作为背景
    backgroundMesh.renderOrder = 998; // 背景渲染优先级
    markerGroup.add(backgroundMesh);

    // 2. 创建图标平面（位于背景上方区域）
    const textureLoader = new THREE.TextureLoader();
    const iconPath = `${BASE_URL}/images/${eventType}.svg`; // 事件图标路径

    const iconMaterial = new THREE.MeshBasicMaterial({
      map: textureLoader.load(iconPath,
        // 加载成功回调
        (texture) => {
          console.log(`事件图标加载成功: 事件${eventType}.svg`);
        },
        // 加载进度回调
        undefined,
        // 加载失败回调
        (error) => {
          console.warn(`事件图标加载失败: 事件${eventType}.svg，使用默认图标`);
          // 可以在这里设置默认图标
        }
      ),
      transparent: true,
      opacity: 1, // 完全不透明
      side: THREE.DoubleSide
    });

    // 创建图标几何体（参考截图中的比例）
    const iconGeometry = new THREE.PlaneGeometry(5, 5); // 宽度5，高度4，类似截图中的车辆图标比例
    const iconMesh = new THREE.Mesh(iconGeometry, iconMaterial);
    iconMesh.position.set(0, 0.3, 0.01); // 图标位于背景上半部分
    iconMesh.renderOrder = 999; // 图标渲染优先级
    markerGroup.add(iconMesh);

    // 3. 创建文字标签（显示在图标正下方，参考截图布局）
    const eventTypeName = getEventTypeName(eventType);
    const textLabel = createTextSprite(eventTypeName, {
      backgroundColor: { r: 0, g: 0, b: 0, a: 0.0 }, // 完全透明背景，与图标共用背景
      textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文字
      fontSize: 14, // 增大字体以提高可读性
      padding: 0, // 无填充
      borderThickness: 0, // 无边框
      fontWeight: 'bold' // 加粗字体，类似截图效果
    });
    textLabel.position.set(0, -3.5, 0.02); // 位于图标正下方，紧贴背景下半部分
    textLabel.renderOrder = 1000; // 确保在最上层渲染
    textLabel.scale.set(6, 3, 1); // 调整文字标签的缩放以适应背景
    markerGroup.add(textLabel);

    // 设置组的位置，高度为15米
    markerGroup.position.set(position.x, 15, -position.y);

    // 让整个组始终面向相机
    // markerGroup.lookAt(0, 15, 0);
// 创建时直接朝向相机，避免角度跳跃
    // if (cameraRef.current) {
    //   markerGroup.lookAt(cameraRef.current.position.x, markerGroup.position.y, cameraRef.current.position.z);
    // } else {
    //   markerGroup.lookAt(0, markerGroup.position.y, 0);
    // }
    // 设置渲染优先级，与设备图标保持一致
    markerGroup.renderOrder = 999;

    // 添加用户数据到组
    markerGroup.userData = {
      type: 'eventMarker',
      eventId: eventId,
      eventType: eventType
    };

    // 添加到场景
    scene.add(markerGroup);

    // 存储标记引用
    eventMarkers.set(eventId, markerGroup);

    console.log(`📍 创建事件标记 ${eventType} (${eventTypeName}) (ID: ${eventId})，位置: (${position.x.toFixed(2)}, ${position.y.toFixed(2)})，背景色: #${backgroundColor.toString(16)}`);

    return markerGroup;
  } catch (error) {
    console.error('创建事件标记时出错:', error);
    return null;
  }
};

// 更新事件标记位置
const updateEventMarkerPosition = (eventId, newPosition) => {
  const markerGroup = eventMarkers.get(eventId);
  if (markerGroup && scene) {
    markerGroup.position.set(newPosition.x, 15, -newPosition.y);
    // 重新设置朝向相机
    // markerGroup.lookAt(0, 15, 0);
    // console.log(`📍 更新事件标记位置 (ID: ${eventId})，新位置: (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)})`);
  }
};

const showWarningMarker = (position, text, color, eventType = '999', eventData = {}) => {
  // 添加安全检查 - 如果场景不存在，则直接返回
  if (!scene) {
    console.warn('无法显示警告标记：场景不存在或已卸载');
    return;
  }

  try {
    // 生成事件Key用于去重
    const currentTime = Date.now();
    const eventKey = `${eventData.rsuId || 'UNKNOWN'}_${eventType}_${position.x.toFixed(0)}_${position.y.toFixed(0)}`;

    // 转换模型坐标为经纬度（用于距离计算）
    // const converter = new CoordinateConverter();
    // const wgs84Pos = converter.modelToWgs84(position.x, position.y);

    // 检查事件去重
    const duplicateResult = checkDuplicateEvent(
      eventType,
      eventKey,
      currentTime,
      position
    );

    const isDuplicate = duplicateResult.isDuplicate;

    // console.log(`🔍 3D场景事件去重检查 - 类型: ${eventType}, EventKey: ${eventKey}, 位置: (${position.x}, ${position.y}), 结果: ${isDuplicate ? '重复' : '新事件'}`);

    if (isDuplicate) {
      // 重复事件，更新现有标记位置
      updateEventMarkerPosition(duplicateResult.eventId, position);
    } else {
      // 新事件，创建新的标记
      createEventMarker(eventType, position, duplicateResult.eventId);
    }

  } catch (error) {
    console.error('显示警告标记时出错:', error);
  }
};

// 添加创建红绿灯模型的函数
const createTrafficLights = (converterInstance, intersections) => {
  if (!scene) {
    console.error('无法创建红绿灯：场景未初始化');
    return;
  }

  if (!converterInstance) {
    console.error('无法创建红绿灯：坐标转换器未初始化');
    return;
  }

  // 检查红绿灯模型是否已加载
  if (!preloadedTrafficLightModel) {
    console.error('红绿灯模型未加载，尝试重新加载...');
    // 尝试重新加载红绿灯模型
    const loader = new GLTFLoader();
    loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)
      .then(trafficLightGltf => {
        preloadedTrafficLightModel = trafficLightGltf.scene;
        preloadedTrafficLightModel.scale.set(6, 6, 6);
        preloadedTrafficLightModel.traverse((child) => {
          if (child.isMesh && child.material) {
            child.material.metalness = 0.2;
            child.material.roughness = 0.3;
            child.material.envMapIntensity = 1.2;
          }
        });
        console.log('红绿灯模型重新加载成功，开始创建红绿灯...');
        // 重新调用创建函数
        createTrafficLights(converterInstance, intersections);
      })
      .catch(error => {
        console.error('红绿灯模型重新加载失败:', error);
        // 如果加载失败，使用简单的替代物体
        createFallbackTrafficLights(converterInstance, intersections);
      });
    return;
  }

  // 先清除现有的红绿灯
  trafficLightsMap.forEach((lightObj) => {
    if (scene && lightObj.model) {
      scene.remove(lightObj.model);
    }
  });
  trafficLightsMap.clear();

  // 为每个路口创建红绿灯模型
  intersections.forEach(intersection => {
    if (intersection.hasTrafficLight === false) {
      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);
      return;
    }

    if (intersection.latitude && intersection.longitude && intersection.interId) {
      const modelPos = converterInstance.wgs84ToModel(
        parseFloat(intersection.longitude),
        parseFloat(intersection.latitude)
      );

      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);

      try {
        // 确保模型存在且可以克隆
        if (!preloadedTrafficLightModel || !preloadedTrafficLightModel.clone) {
          throw new Error('红绿灯模型无效或无法克隆');
        }

        // 创建红绿灯模型
        const trafficLightModel = preloadedTrafficLightModel.clone();

        // 给模型一个名称便于调试
        trafficLightModel.name = `交通灯-${intersection.name}`;

        // 设置位置，离地面高度为15米，提高可见性
        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);

        // 放大红绿灯模型尺寸，使其更容易被点击
        trafficLightModel.scale.set(10, 10, 10);

        // 确保渲染顺序高，避免被其他对象遮挡
        trafficLightModel.renderOrder = 100;

        // 设置材质属性
        trafficLightModel.traverse(child => {
          if (child.isMesh) {
            child.material.transparent = false;
            child.material.opacity = 1.0;
            child.material.side = THREE.DoubleSide;
            child.material.depthWrite = true;
            child.material.depthTest = true;
            child.material.needsUpdate = true;
            child.renderOrder = 100;
          }
        });

        // 添加交互所需的信息
        trafficLightModel.userData = {
          type: 'trafficLight',
          interId: intersection.interId,
          name: intersection.name
        };

        // 将红绿灯添加到场景中
        // scene.add(trafficLightModel);

        // ========== 新增：在红绿灯地面添加指北针图标 =============
        // 创建一个平面用于显示指北针SVG图标
        const compassTextureLoader = new THREE.TextureLoader();
        const compassIconPath = `${BASE_URL}/images/compass.svg`; // public目录下的路径
        const compassMaterial = new THREE.MeshBasicMaterial({
          map: compassTextureLoader.load(compassIconPath),
          transparent: true,
          opacity: 1
        });
        // 平面几何体，5x5米
        const compassGeometry = new THREE.PlaneGeometry(5, 5);
        const compassMesh = new THREE.Mesh(compassGeometry, compassMaterial);
        // 指北针放在红绿灯正下方地面（y=0.1，略高于地面防止Z冲突）
        compassMesh.position.set(modelPos.x+20, 1.5, -(modelPos.y+20));
        // 指北针朝上，旋转x轴-90度
        compassMesh.rotation.x = -Math.PI / 2;
        // 渲染优先级高，避免被地面遮挡
        compassMesh.renderOrder = 101;
        // 可选：添加userData标记
        compassMesh.userData = {
          type: 'compassIcon',
          interId: intersection.interId,
          name: intersection.name
        };
        // 添加到场景
        scene.add(compassMesh);
        // ========== 指北针图标添加结束 =============

        // 存储红绿灯引用
        trafficLightsMap.set(intersection.interId, {
          model: trafficLightModel,
          intersection: intersection,
          position: modelPos
        });

        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);
      } catch (error) {
        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);
        // 如果克隆失败，创建一个简单的替代物体
        // createSimpleTrafficLight(intersection, modelPos, converterInstance);
      }
    }
  });

  // 在控制台输出所有红绿灯的信息
  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);
  trafficLightsMap.forEach((lightObj, interId) => {
    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);
  });
};

// 创建所有路口的替代红绿灯
const createFallbackTrafficLights = (converterInstance, intersections) => {
  intersections.forEach(intersection => {
    // 跳过没有红绿灯的路口
    if (intersection.hasTrafficLight === false) {
      return;
    }

    if (intersection.latitude && intersection.longitude && intersection.interId) {
      // 转换经纬度到模型坐标
      const modelPos = converterInstance.wgs84ToModel(
        parseFloat(intersection.longitude),
        parseFloat(intersection.latitude)
      );

      createSimpleTrafficLight(intersection, modelPos, converterInstance);
    }
  });
};

// 创建简单的替代红绿灯
const createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {
  // 创建一个简单的几何体作为红绿灯 - 增大尺寸
  const geometry = new THREE.BoxGeometry(10, 30, 10);
  const material = new THREE.MeshBasicMaterial({
    color: 0x333333,
    transparent: false,
    opacity: 1.0
  });
  const trafficLightModel = new THREE.Mesh(geometry, material);

  // 给模型一个名称便于调试
  trafficLightModel.name = `简易交通灯-${intersection.name}`;

  // 设置位置 - 提高高度以增加可见性
  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);

  // 设置渲染顺序
  trafficLightModel.renderOrder = 100;

  // 添加交互所需的信息
  trafficLightModel.userData = {
    type: 'trafficLight',
    interId: intersection.interId,
    name: intersection.name
  };

  // 添加一个专门用于点击的大型碰撞体
  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);
  const colliderMaterial = new THREE.MeshBasicMaterial({
    color: 0xff00ff,
    transparent: true,
    opacity: 0.0,  // 完全透明
    depthWrite: false
  });

  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);
  collider.name = `简易交通灯碰撞体-${intersection.name}`;
  collider.userData = {
    type: 'trafficLight',
    interId: intersection.interId,
    name: intersection.name,
    isCollider: true
  };

  trafficLightModel.add(collider);

  // 将红绿灯添加到场景中
  scene.add(trafficLightModel);

  // 存储红绿灯引用
  trafficLightsMap.set(intersection.interId, {
    model: trafficLightModel,
    intersection: intersection,
    position: modelPos
  });

  // 添加一个顶部灯光标识，使其更容易被看到
  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);
  const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });
  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);
  lightMesh.position.set(0, 15, 0);

  // 给灯光相同的userData
  lightMesh.userData = {
    type: 'trafficLight',
    interId: intersection.interId,
    name: intersection.name
  };

  trafficLightModel.add(lightMesh);

  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);
};

// 添加获取相位方向和名称的辅助函数
const getPhaseDirection = (phaseId) => {
  switch(phaseId) {
    case '1': return '北进口左转';
    case '2': return '北进口直行';
    case '3': return '北进口右转';
    case '5': return '东进口左转';
    case '6': return '东进口直行';
    case '7': return '东进口右转';
    case '9': return '南进口左转';
    case '10': return '南进口直行';
    case '11': return '南进口右转';
    case '13': return '西进口左转';
    case '14': return '西进口直行';
    case '15': return '西进口右转';
    default: return `相位${phaseId}`;
  }
};

// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述
const getDirectionFromCode = (dirCode) => {
  switch(dirCode) {
    case 'N': return '北向南';
    case 'S': return '南向北';
    case 'E': return '东向西';
    case 'W': return '西向东';
    case 'NE': return '东北向西南';
    case 'NW': return '西北向东南';
    case 'SE': return '东南向西北';
    case 'SW': return '西南向东北';
    default: return `方向${dirCode}`;
  }
};

// 关闭弹出窗口的处理函数
const handleClosePopover = (setPopoverState) => {
  // 清理定时器
  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {
    clearInterval(window.trafficLightUpdateTimerRef.current);
    window.trafficLightUpdateTimerRef.current = null;
    console.log('已清理红绿灯状态更新定时器');
  }

  // 清理当前弹窗ID
  if (window.currentPopoverIdRef) {
    window.currentPopoverIdRef.current = null;
  }

  // 更新弹窗状态为不可见
  setPopoverState(prev => ({
    ...prev,
    visible: false,
    content: null, // 清空内容
    phases: []     // 清空相位信息
  }));

  console.log('弹窗已关闭，所有相关资源已清理');
};

// 添加一个全局方法用于测试红绿灯点击
window.testTrafficLightClick = (interId) => {
  try {
    // 检查是否有该ID的红绿灯
    const trafficLight = trafficLightsMap.get(interId || '1');
    if (!trafficLight) {
      console.error('未找到指定ID的红绿灯:', interId);

      // 输出所有可用ID
      console.log('可用的红绿灯ID:');
      trafficLightsMap.forEach((light, id) => {
        console.log(`- ${id}: ${light.intersection.name}`);
      });

      return false;
    }

    // 获取红绿灯模型
    const lightModel = trafficLight.model;

    // 模拟点击事件
    const stateInfo = trafficLightStates.get(interId);
    const intersection = trafficLight.intersection;

    // 创建弹出窗口内容
    let content;

    if (stateInfo && stateInfo.phases) {
      content = (
        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>
          <div style={{
            fontWeight: 'bold',
            marginBottom: '6px',
            fontSize: '14px',
            borderBottom: '1px solid #eee',
            paddingBottom: '4px'
          }}>
            {intersection.name} (ID: {interId})
          </div>
          <div>
            {stateInfo.phases.map((phase, index) => {
              let lightColor;
              switch (phase.trafficLight) {
                case 'G': lightColor = '#00ff00'; break;
                case 'Y': lightColor = '#ffff00'; break;
                case 'R': default: lightColor = '#ff0000'; break;
              }

              return (
                <div key={index} style={{
                  marginBottom: '6px',
                  backgroundColor: 'rgba(255,255,255,0.1)',
                  padding: '4px',
                  borderRadius: '4px',
                  fontSize: '12px'
                }}>
                  <div style={{ fontWeight: 'bold' }}>
                    {getPhaseDirection(phase.phaseId)}
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span>灯色: </span>
                    <span style={{
                      color: lightColor,
                      fontWeight: 'bold',
                      backgroundColor: 'rgba(0,0,0,0.3)',
                      padding: '0 3px',
                      borderRadius: '2px'
                    }}>
                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}
                    </span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span>倒计时: </span>
                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>
                  </div>
                </div>
              );
            })}
          </div>
          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>
            更新时间: {new Date().toLocaleTimeString()} (自动刷新)
          </div>
        </div>
      );
    } else {
      content = (
        <div style={{ padding: '8px', maxWidth: '200px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>
          <div>路口ID: {interId}</div>
          <div>当前无信号灯状态信息</div>
        </div>
      );
    }

    // 获取弹窗位置 - 使用中心位置
    const centerX = window.innerWidth / 2 -500;
    const centerY = window.innerHeight / 2 -500;

    // 获取全局的setTrafficLightPopover函数
    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;

    if (setPopoverState) {
      // 直接调用React组件的状态更新函数
      setPopoverState({
        visible: true,
        interId: interId,
        position: { x: centerX, y: centerY },
        content: content,
        phases: stateInfo?.phases || []
      });

      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);
      return true;
    } else {
      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗
      const popover = document.createElement('div');
      popover.style.position = 'absolute';
      popover.style.left = `${centerX}px`;
      popover.style.top = `${centerY}px`;
      popover.style.transform = 'translate(-50%, -100%)';
      popover.style.zIndex = '9999';
      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';
      popover.style.color = 'white';
      popover.style.borderRadius = '4px';
      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';
      popover.style.padding = '8px';
      popover.style.maxWidth = '240px';
      popover.style.fontSize = '12px';

      popover.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;">
          ${intersection.name} (ID: ${interId})
        </div>
        <div>
          <p>路口ID: ${interId}</p>
          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>
        </div>
        <button style="position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;">×</button>
      `;

      document.body.appendChild(popover);

      // 添加关闭按钮点击事件
      const closeButton = popover.querySelector('button');
      if (closeButton) {
        closeButton.addEventListener('click', () => {
          document.body.removeChild(popover);
        });
      }

      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);
      return true;
    }
  } catch (error) {
    console.error('测试红绿灯点击失败:', error);
    return false;
  }
};

// 添加一个全局方法用于显示所有红绿灯ID
window.listTrafficLights = () => {
  console.log('红绿灯列表:');

  if (!trafficLightsMap || trafficLightsMap.size === 0) {
    console.log('当前没有红绿灯对象');
    return [];
  }

  const list = [];
  trafficLightsMap.forEach((light, id) => {
    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);
    list.push({
      id,
      name: light.intersection.name,
      position: light.position
    });
  });

  return list;
};


// 添加全局测试弹窗函数
window.showTrafficLightPopup = (interId) => {
  try {
    // 确保interId为字符串类型
    interId = String(interId);

    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);
    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);

    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型
    let trafficLight = trafficLightsMap.get(interId);
    if (!trafficLight) {
      // 尝试转换为数字查找
      const numericId = parseInt(interId);
      trafficLight = trafficLightsMap.get(numericId);

      if (trafficLight) {
        console.log(`使用数字ID ${numericId} 找到了红绿灯`);
        interId = numericId; // 更新interId为找到的正确类型
      }
    }

    if (!trafficLight) {
      console.error('未找到指定ID的红绿灯:', interId);
      return false;
    }

    const stateInfo = trafficLightStates.get(interId);
    const intersection = trafficLight.intersection;

    // 判断是否有相位数据
    const hasPhaseData = stateInfo && stateInfo.phases && stateInfo.phases.length > 0;

    let content;

    // 指北针样式
    const compassStyle = {
      position: 'absolute',
      top: '5px',
      right: '25px',
      width: '30px',
      height: '30px',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: '50%',
      background: 'rgba(0,0,0,0.1)',
      zIndex: 10
    };

    // 指北针组件
    const CompassIcon = () => (
      <div style={compassStyle}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          transform: 'rotate(0deg)'
        }}>
          <span style={{
            color: '#ff5252',
            fontSize: '14px',
            fontWeight: 'bold',
            lineHeight: '14px'
          }}>N</span>
          <span style={{
            width: 0,
            height: 0,
            borderLeft: '6px solid transparent',
            borderRight: '6px solid transparent',
            borderBottom: '10px solid #ff5252',
            marginTop: '-2px'
          }}></span>
        </div>
      </div>
    );

    if (hasPhaseData) {
      // 相位ID与方向/方式的映射
      const phaseMap = {
        '1': { dir: 'N', type: 'left' }, '2': { dir: 'N', type: 'straight' }, '3': { dir: 'N', type: 'right' },
        '5': { dir: 'E', type: 'left' }, '6': { dir: 'E', type: 'straight' }, '7': { dir: 'E', type: 'right' },
        '9': { dir: 'S', type: 'left' }, '10': { dir: 'S', type: 'straight' }, '11': { dir: 'S', type: 'right' },
        '13': { dir: 'W', type: 'left' }, '14': { dir: 'W', type: 'straight' }, '15': { dir: 'W', type: 'right' }
      };

      const typeOrder = ['left', 'straight', 'right'];
      const colorMap = { G: '#00ff00', Y: '#ffff00', R: '#ff0000' };
      const dirData = { N: {}, E: {}, S: {}, W: {} };

      stateInfo.phases.forEach(phase => {
        const map = phaseMap[phase.phaseId];
        if (map) {
          dirData[map.dir][map.type] = {
            color: colorMap[phase.trafficLight] || '#888',
            remainTime: phase.remainTime
          };
        }
      });

      content = (
        <div style={{ padding: '8px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>
          <CompassIcon />
          <div style={{ fontWeight: 'bold', marginBottom: '8px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}灯态</div>
          <div style={{
            display: 'grid',
            gridTemplateRows: '60px 60px 60px',
            gridTemplateColumns: '60px 60px 60px',
            justifyContent: 'center',
            alignItems: 'center',
            background: 'rgba(255,255,255,0.05)',
            borderRadius: '8px',
            margin: '0 auto',
            position: 'relative'
          }}>
            {/* 北 - 左转、直行、右转箭头水平对齐，倒计时在箭头上面 */}
            {/* <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%', paddingLeft: '5px' }}> */}
            <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}>
              {typeOrder.map((type, index) => {
                // 反转左转和右转在数组中的顺序
                let displayIndex = index;
                if (index === 0) displayIndex = 2;
                else if (index === 2) displayIndex = 0;

                const currentType = typeOrder[displayIndex];

                // 计算与南边对齐的样式
                const marginStyle = {};
                if (currentType === 'left') { // 左转箭头 (右侧显示)
                  marginStyle.marginRight = '0px';
                } else if (currentType === 'straight') { // 直行箭头 (中间显示)
                  marginStyle.marginLeft = '10px';
                  marginStyle.marginRight = '10px';
                } else if (currentType === 'right') { // 右转箭头 (左侧显示)
                  marginStyle.marginLeft = '0px';
                }

                return dirData.N[currentType] && (
                  // <div key={currentType} style={{
                  //   display: 'flex',
                  //   flexDirection: 'column',
                  //   alignItems: 'center',
                  //   ...marginStyle
                  // }}>
                  <div key={currentType} style={{marginRight: currentType === 'left' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>
                    <div style={{fontSize:'14px', color: dirData.N[currentType].color, fontWeight:'bold', marginBottom: '3px'}}>{dirData.N[currentType].remainTime}</div>
                    <span style={{color: dirData.N[currentType].color, fontSize:'20px', lineHeight: '20px'}}>
                      {currentType === 'left' ? '\u{1F87A}' : currentType === 'straight' ? '\u{1F87B}' : '\u{1F878}'}
                    </span>
                  </div>
                );
              })}
            </div>

            {/* 南 - 左转、直行、右转箭头水平对齐，倒计时在箭头下面 */}
            <div style={{ gridRow: 3, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}>
              {typeOrder.map(type => dirData.S[type] && (
                <div key={type} style={{marginRight: type === 'right' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>
                  <span style={{color: dirData.S[type].color, fontSize:'20px', lineHeight: '20px'}}>
                    {type === 'left' ? '\u{1F878}' : type === 'straight' ? '\u{1F879}' : '\u{1F87A}'}
                  </span>
                  <div style={{fontSize:'14px', color: dirData.S[type].color, fontWeight:'bold', marginTop: '3px'}}>{dirData.S[type].remainTime}</div>
                </div>
              ))}
            </div>

            {/* 东 - 倒计时显示在箭头右边 */}

            <div style={{ gridRow: 2, gridColumn: 3, textAlign: 'center' }}>
              {typeOrder.map((type, index) => {
                // 反转左转和右转在数组中的顺序
                let displayIndex = index;
                if (index === 0) displayIndex = 2;
                else if (index === 2) displayIndex = 0;

                const currentType = typeOrder[displayIndex];

                return dirData.E[currentType] && (
                  <div key={currentType} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-start'}}>
                    <span style={{color: dirData.E[currentType].color, fontSize:'20px', lineHeight: '20px'}}>
                      {currentType === 'left' ? '\u{1F87B}' : currentType === 'straight' ? '\u{1F878}' : '\u{1F879}'}
                    </span>
                    <div style={{
                      fontSize:'14px',
                      color: dirData.E[currentType].color,
                      fontWeight:'bold',
                      marginLeft: '5px'
                    }}>{dirData.E[currentType].remainTime}</div>
                  </div>
                );
              })}
            </div>

            {/* 西 - 倒计时显示在箭头左边 */}
            <div style={{ gridRow: 2, gridColumn: 1, textAlign: 'center' }}>
              {typeOrder.map(type => dirData.W[type] && (
                <div key={type} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}>
                  <div style={{
                    fontSize:'14px',
                    color: dirData.W[type].color,
                    fontWeight:'bold',
                    marginRight: '5px'
                  }}>{dirData.W[type].remainTime}</div>
                  <span style={{color: dirData.W[type].color, fontSize:'20px', lineHeight: '20px'}}>
                    {type === 'left' ? '\u{1F879}' : type === 'straight' ? '\u{1F87A}' : '\u{1F87B}'}
                  </span>
                </div>
              ))}
            </div>
          </div>
          <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>
            更新时间: {new Date().toLocaleTimeString()} (自动刷新)
          </div>
        </div>
      );
    } else {
      // 没有相位数据时显示的内容
      content = (
        <div style={{ padding: '15px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>
          <CompassIcon />
          <div style={{ fontWeight: 'bold', marginBottom: '10px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}</div>
          <div style={{
            textAlign: 'center',
            padding: '20px 0',
            color: '#ff9800',
            fontSize: '14px',
            fontWeight: 'bold',
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '8px',
            marginBottom: '10px'
          }}>
            当前无信号灯状态信息
          </div>
          <div style={{ fontSize: '12px', color: '#888', textAlign: 'center' }}>
            路口ID: {interId}
          </div>
          <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>
            更新时间: {new Date().toLocaleTimeString()} (自动刷新)
          </div>
        </div>
      );
    }

    // 设置弹窗位置在左上角区域
    const x = 445;
    const y = 250;

    // 更新当前显示的红绿灯ID引用
    if (window.currentPopoverIdRef) {
      window.currentPopoverIdRef.current = interId;
    }

    // 取消之前的更新定时器（如果存在）
    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {
      clearInterval(window.trafficLightUpdateTimerRef.current);
      window.trafficLightUpdateTimerRef.current = null;
    }

    // 更新弹窗状态
    if (window._setTrafficLightPopover) {
      window._setTrafficLightPopover({
        visible: true,
        interId: interId,
        position: { x, y },
        content: content,
        phases: stateInfo?.phases || []
      });

      // 设置定时更新
      if (window.trafficLightUpdateTimerRef) {
        window.trafficLightUpdateTimerRef.current = setInterval(() => {
          window.showTrafficLightPopup(interId);
        }, 1000);
      }

      return true;
    } else {
      console.error('无法找到setTrafficLightPopover函数');
      return false;
    }
  } catch (error) {
    console.error('显示红绿灯弹窗失败:', error);
    return false;
  }
};



// 帮助函数：从对象或其父对象中找到红绿灯对象
const getTrafficLightFromObject = (object) => {
  let current = object;

  // 如果对象本身就有红绿灯数据，直接返回
  if (current && current.userData && current.userData.type === 'trafficLight') {
    console.log('直接找到红绿灯对象:', current.name || '无名称');
    return current;
  }

  // 向上查找父对象，直到找到红绿灯或到达顶层
  while (current && current.parent) {
    current = current.parent;
    if (current.userData && current.userData.type === 'trafficLight') {
      console.log('从父对象找到红绿灯:', current.name || '无名称');
      return current;
    }
  }

  return null;
};

// 添加调试工具：强制进行点击测试
window.testClickDetection = (x, y) => {
  try {
    console.log('执行强制点击测试 @ 位置:', x, y);

    // 找到渲染器的DOM元素
    const canvas = document.querySelector('canvas');
    if (!canvas) {
      console.error('找不到THREE.js的canvas元素');
      return false;
    }

    // 确保scene和camera已定义
    if (!scene || !cameraRef.current) {
      console.error('scene或camera未定义');
      return false;
    }

    // 如果没有传入坐标，使用屏幕中心点
    if (x === undefined || y === undefined) {
      x = window.innerWidth / 2;
      y = window.innerHeight / 2;
    }

    // 计算归一化设备坐标 (-1 到 +1)
    const rect = canvas.getBoundingClientRect();
    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;
    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;

    console.log('归一化坐标:', mouseX, mouseY);

    // 创建一个射线
    const raycaster = new THREE.Raycaster();
    raycaster.params.Points.threshold = 5;
    raycaster.params.Line.threshold = 5;

    const mouseVector = new THREE.Vector2(mouseX, mouseY);
    raycaster.setFromCamera(mouseVector, cameraRef.current);

    // 收集所有红绿灯对象
    const trafficLightObjects = [];
    trafficLightsMap.forEach((lightObj, interId) => {
      if (lightObj.model) {
        trafficLightObjects.push(lightObj.model);
        console.log(`添加红绿灯 ${interId} 到检测列表`);
      }
    });

    // 直接对红绿灯对象进行碰撞检测
    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);
    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);

    if (tlIntersects.length > 0) {
      console.log('成功点击到红绿灯对象!');
      tlIntersects.forEach((intersect, i) => {
        console.log(`结果 ${i}:`, intersect.object.name || '无名称',
                    '距离:', intersect.distance,
                    'position:', intersect.object.position.toArray(),
                    'userData:', intersect.object.userData);

        // 尝试获取红绿灯ID
        const obj = getTrafficLightFromObject(intersect.object);
        if (obj && obj.userData && obj.userData.type === 'trafficLight') {
          console.log('找到红绿灯ID:', obj.userData.interId);
        }
      });

      return true;
    }

    // 对整个场景进行碰撞检测
    console.log('对整个场景进行碰撞检测...');
    const sceneIntersects = raycaster.intersectObjects(scene.children, true);

    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);
    sceneIntersects.forEach((intersect, i) => {
      const obj = intersect.object;
      console.log(`场景物体 ${i}:`, obj.name || '无名称',
                  '类型:', obj.type,
                  '位置:', obj.position.toArray(),
                  '距离:', intersect.distance,
                  'userData:', obj.userData);
    });

    // 测试红绿灯的可见性
    console.log('检查红绿灯的可见性...');
    let visibleCount = 0;

    trafficLightsMap.forEach((lightObj, interId) => {
      if (lightObj.model) {
        // 检查红绿灯模型是否可见
        let isVisible = lightObj.model.visible;
        let frustumVisible = true;

        // 获取世界位置
        const worldPos = new THREE.Vector3();
        lightObj.model.getWorldPosition(worldPos);

        // 计算到摄像机的距离
        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);

        // 检查是否在视锥体内
        const screenPos = worldPos.clone().project(cameraRef.current);
        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {
          frustumVisible = false;
        }

        if (isVisible) {
          visibleCount++;
        }

        console.log(`红绿灯 ${interId}:`, {
          名称: lightObj.intersection?.name || '未知',
          可见性: isVisible,
          在视锥体内: frustumVisible,
          世界位置: worldPos.toArray(),
          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],
          与摄像机距离: distanceToCamera
        });
      }
    });

    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);

    // 如果未检测到任何交叉点
    return sceneIntersects.length > 0;
  } catch (error) {
    console.error('点击测试失败:', error);
    return false;
  }
};



// 添加更新红绿灯视觉效果的函数
const updateTrafficLightVisual = (trafficLight, phaseInfo) => {
  if (!trafficLight || !trafficLight.model || !phaseInfo) {
    return;
  }

  // 移除旧的灯光模型(如果存在)
  const lightsToRemove = [];
  trafficLight.model.traverse(child => {
    if (child.userData && child.userData.isLight) {
      lightsToRemove.push(child);
    }
  });

  lightsToRemove.forEach(light => {
    trafficLight.model.remove(light);
  });

  // 根据状态获取颜色
  let lightColor;
  switch(phaseInfo.trafficLight) {
    case 'G':
      lightColor = 0x00FF00; // 绿色
      break;
    case 'Y':
      lightColor = 0xFFFF00; // 黄色
      break;
    case 'R':
    default:
      lightColor = 0xFF0000; // 红色
      break;
  }

  // 创建一个球体作为灯光
  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);
  const lightMaterial = new THREE.MeshBasicMaterial({
    color: lightColor,
    emissive: lightColor,
    emissiveIntensity: 1
  });
  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);
  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部
  lightMesh.userData = {
    isLight: true,
    type: 'trafficLight',
    interId: trafficLight.intersection?.interId,
    phaseId: phaseInfo.phaseId,
    direction: phaseInfo.direction,
    remainTime: phaseInfo.remainTime
  };

  // 添加光源使灯光更明显
  const light = new THREE.PointLight(lightColor, 1, 50);
  light.position.set(0, 12, 0);
  light.userData = { isLight: true };

  // 将灯光添加到交通灯模型
  trafficLight.model.add(lightMesh);
  trafficLight.model.add(light);

  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);
};

export default CampusModel;

