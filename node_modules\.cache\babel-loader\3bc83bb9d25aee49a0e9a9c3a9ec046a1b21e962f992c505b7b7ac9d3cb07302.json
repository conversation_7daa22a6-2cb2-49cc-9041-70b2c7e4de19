{"ast": null, "code": "import hypot from './hypot';\nimport log1py from './log1py';\nexport default function (x) {\n  var y = Math.abs(x);\n  y = log1py(y * (1 + y / (hypot(1, y) + 1)));\n  return x < 0 ? -y : y;\n}", "map": {"version": 3, "names": ["hypot", "log1py", "x", "y", "Math", "abs"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/proj4/lib/common/asinhy.js"], "sourcesContent": ["import hypot from './hypot';\nimport log1py from './log1py';\n\nexport default function(x) {\n  var y = Math.abs(x);\n  y = log1py(y * (1 + y / (hypot(1, y) + 1)));\n\n  return x < 0 ? -y : y;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAE7B,eAAe,UAASC,CAAC,EAAE;EACzB,IAAIC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACH,CAAC,CAAC;EACnBC,CAAC,GAAGF,MAAM,CAACE,CAAC,IAAI,CAAC,GAAGA,CAAC,IAAIH,KAAK,CAAC,CAAC,EAAEG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAE3C,OAAOD,CAAC,GAAG,CAAC,GAAG,CAACC,CAAC,GAAGA,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}