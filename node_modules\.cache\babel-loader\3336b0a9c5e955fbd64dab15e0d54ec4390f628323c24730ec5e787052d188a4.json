{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport Select from '../select';\nconst {\n  Option\n} = Select;\nfunction isSelectOptionOrSelectOptGroup(child) {\n  return (child === null || child === void 0 ? void 0 : child.type) && (child.type.isSelectOption || child.type.isSelectOptGroup);\n}\nconst AutoComplete = (props, ref) => {\n  var _a;\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    popupClassName,\n    dropdownClassName,\n    children,\n    dataSource\n  } = props;\n  const childNodes = toArray(children);\n  // ============================= Input =============================\n  let customizeInput;\n  if (childNodes.length === 1 && /*#__PURE__*/React.isValidElement(childNodes[0]) && !isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    [customizeInput] = childNodes;\n  }\n  const getInputElement = customizeInput ? () => customizeInput : undefined;\n  // ============================ Options ============================\n  let optionChildren;\n  // [Legacy] convert `children` or `dataSource` into option children\n  if (childNodes.length && isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    optionChildren = children;\n  } else {\n    optionChildren = dataSource ? dataSource.map(item => {\n      if (/*#__PURE__*/React.isValidElement(item)) {\n        return item;\n      }\n      switch (typeof item) {\n        case 'string':\n          return /*#__PURE__*/React.createElement(Option, {\n            key: item,\n            value: item\n          }, item);\n        case 'object':\n          {\n            const {\n              value: optionValue\n            } = item;\n            return /*#__PURE__*/React.createElement(Option, {\n              key: optionValue,\n              value: optionValue\n            }, item.text);\n          }\n        default:\n          return undefined;\n      }\n    }) : [];\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('AutoComplete');\n    warning.deprecated(!('dataSource' in props), 'dataSource', 'options');\n    process.env.NODE_ENV !== \"production\" ? warning(!customizeInput || !('size' in props), 'usage', 'You need to control style self instead of setting `size` when using customize input.') : void 0;\n    warning.deprecated(!dropdownClassName, 'dropdownClassName', 'popupClassName');\n  }\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('select', customizePrefixCls);\n  // ============================ zIndex ============================\n  const [zIndex] = useZIndex('SelectLike', (_a = props.dropdownStyle) === null || _a === void 0 ? void 0 : _a.zIndex);\n  return /*#__PURE__*/React.createElement(Select, Object.assign({\n    ref: ref,\n    suffixIcon: null\n  }, omit(props, ['dataSource', 'dropdownClassName']), {\n    prefixCls: prefixCls,\n    popupClassName: popupClassName || dropdownClassName,\n    dropdownStyle: Object.assign(Object.assign({}, props.dropdownStyle), {\n      zIndex\n    }),\n    className: classNames(`${prefixCls}-auto-complete`, className),\n    mode: Select.SECRET_COMBOBOX_MODE_DO_NOT_USE,\n    // Internal api\n    getInputElement\n  }), optionChildren);\n};\nconst RefAutoComplete = /*#__PURE__*/React.forwardRef(AutoComplete);\nif (process.env.NODE_ENV !== 'production') {\n  RefAutoComplete.displayName = 'AutoComplete';\n}\nexport default RefAutoComplete;", "map": {"version": 3, "names": ["React", "classNames", "toArray", "omit", "useZIndex", "devUseW<PERSON>ning", "ConfigContext", "Select", "Option", "isSelectOptionOrSelectOptGroup", "child", "type", "isSelectOption", "isSelectOptGroup", "AutoComplete", "props", "ref", "_a", "prefixCls", "customizePrefixCls", "className", "popupClassName", "dropdownClassName", "children", "dataSource", "childNodes", "customizeInput", "length", "isValidElement", "getInputElement", "undefined", "option<PERSON><PERSON><PERSON>n", "map", "item", "createElement", "key", "value", "optionValue", "text", "process", "env", "NODE_ENV", "warning", "deprecated", "getPrefixCls", "useContext", "zIndex", "dropdownStyle", "Object", "assign", "suffixIcon", "mode", "SECRET_COMBOBOX_MODE_DO_NOT_USE", "RefAutoComplete", "forwardRef", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/auto-complete/AutoComplete.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport { useZIndex } from '../_util/hooks/useZIndex';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport Select from '../select';\nconst {\n  Option\n} = Select;\nfunction isSelectOptionOrSelectOptGroup(child) {\n  return (child === null || child === void 0 ? void 0 : child.type) && (child.type.isSelectOption || child.type.isSelectOptGroup);\n}\nconst AutoComplete = (props, ref) => {\n  var _a;\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    popupClassName,\n    dropdownClassName,\n    children,\n    dataSource\n  } = props;\n  const childNodes = toArray(children);\n  // ============================= Input =============================\n  let customizeInput;\n  if (childNodes.length === 1 && /*#__PURE__*/React.isValidElement(childNodes[0]) && !isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    [customizeInput] = childNodes;\n  }\n  const getInputElement = customizeInput ? () => customizeInput : undefined;\n  // ============================ Options ============================\n  let optionChildren;\n  // [Legacy] convert `children` or `dataSource` into option children\n  if (childNodes.length && isSelectOptionOrSelectOptGroup(childNodes[0])) {\n    optionChildren = children;\n  } else {\n    optionChildren = dataSource ? dataSource.map(item => {\n      if (/*#__PURE__*/React.isValidElement(item)) {\n        return item;\n      }\n      switch (typeof item) {\n        case 'string':\n          return /*#__PURE__*/React.createElement(Option, {\n            key: item,\n            value: item\n          }, item);\n        case 'object':\n          {\n            const {\n              value: optionValue\n            } = item;\n            return /*#__PURE__*/React.createElement(Option, {\n              key: optionValue,\n              value: optionValue\n            }, item.text);\n          }\n        default:\n          return undefined;\n      }\n    }) : [];\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('AutoComplete');\n    warning.deprecated(!('dataSource' in props), 'dataSource', 'options');\n    process.env.NODE_ENV !== \"production\" ? warning(!customizeInput || !('size' in props), 'usage', 'You need to control style self instead of setting `size` when using customize input.') : void 0;\n    warning.deprecated(!dropdownClassName, 'dropdownClassName', 'popupClassName');\n  }\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('select', customizePrefixCls);\n  // ============================ zIndex ============================\n  const [zIndex] = useZIndex('SelectLike', (_a = props.dropdownStyle) === null || _a === void 0 ? void 0 : _a.zIndex);\n  return /*#__PURE__*/React.createElement(Select, Object.assign({\n    ref: ref,\n    suffixIcon: null\n  }, omit(props, ['dataSource', 'dropdownClassName']), {\n    prefixCls: prefixCls,\n    popupClassName: popupClassName || dropdownClassName,\n    dropdownStyle: Object.assign(Object.assign({}, props.dropdownStyle), {\n      zIndex\n    }),\n    className: classNames(`${prefixCls}-auto-complete`, className),\n    mode: Select.SECRET_COMBOBOX_MODE_DO_NOT_USE,\n    // Internal api\n    getInputElement\n  }), optionChildren);\n};\nconst RefAutoComplete = /*#__PURE__*/React.forwardRef(AutoComplete);\nif (process.env.NODE_ENV !== 'production') {\n  RefAutoComplete.displayName = 'AutoComplete';\n}\nexport default RefAutoComplete;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,MAAM,MAAM,WAAW;AAC9B,MAAM;EACJC;AACF,CAAC,GAAGD,MAAM;AACV,SAASE,8BAA8BA,CAACC,KAAK,EAAE;EAC7C,OAAO,CAACA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACC,IAAI,MAAMD,KAAK,CAACC,IAAI,CAACC,cAAc,IAAIF,KAAK,CAACC,IAAI,CAACE,gBAAgB,CAAC;AACjI;AACA,MAAMC,YAAY,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;EACnC,IAAIC,EAAE;EACN,MAAM;IACJC,SAAS,EAAEC,kBAAkB;IAC7BC,SAAS;IACTC,cAAc;IACdC,iBAAiB;IACjBC,QAAQ;IACRC;EACF,CAAC,GAAGT,KAAK;EACT,MAAMU,UAAU,GAAGvB,OAAO,CAACqB,QAAQ,CAAC;EACpC;EACA,IAAIG,cAAc;EAClB,IAAID,UAAU,CAACE,MAAM,KAAK,CAAC,IAAI,aAAa3B,KAAK,CAAC4B,cAAc,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAChB,8BAA8B,CAACgB,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;IACjI,CAACC,cAAc,CAAC,GAAGD,UAAU;EAC/B;EACA,MAAMI,eAAe,GAAGH,cAAc,GAAG,MAAMA,cAAc,GAAGI,SAAS;EACzE;EACA,IAAIC,cAAc;EAClB;EACA,IAAIN,UAAU,CAACE,MAAM,IAAIlB,8BAA8B,CAACgB,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;IACtEM,cAAc,GAAGR,QAAQ;EAC3B,CAAC,MAAM;IACLQ,cAAc,GAAGP,UAAU,GAAGA,UAAU,CAACQ,GAAG,CAACC,IAAI,IAAI;MACnD,IAAI,aAAajC,KAAK,CAAC4B,cAAc,CAACK,IAAI,CAAC,EAAE;QAC3C,OAAOA,IAAI;MACb;MACA,QAAQ,OAAOA,IAAI;QACjB,KAAK,QAAQ;UACX,OAAO,aAAajC,KAAK,CAACkC,aAAa,CAAC1B,MAAM,EAAE;YAC9C2B,GAAG,EAAEF,IAAI;YACTG,KAAK,EAAEH;UACT,CAAC,EAAEA,IAAI,CAAC;QACV,KAAK,QAAQ;UACX;YACE,MAAM;cACJG,KAAK,EAAEC;YACT,CAAC,GAAGJ,IAAI;YACR,OAAO,aAAajC,KAAK,CAACkC,aAAa,CAAC1B,MAAM,EAAE;cAC9C2B,GAAG,EAAEE,WAAW;cAChBD,KAAK,EAAEC;YACT,CAAC,EAAEJ,IAAI,CAACK,IAAI,CAAC;UACf;QACF;UACE,OAAOR,SAAS;MACpB;IACF,CAAC,CAAC,GAAG,EAAE;EACT;EACA,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGrC,aAAa,CAAC,cAAc,CAAC;IAC7CqC,OAAO,CAACC,UAAU,CAAC,EAAE,YAAY,IAAI5B,KAAK,CAAC,EAAE,YAAY,EAAE,SAAS,CAAC;IACrEwB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,CAAChB,cAAc,IAAI,EAAE,MAAM,IAAIX,KAAK,CAAC,EAAE,OAAO,EAAE,sFAAsF,CAAC,GAAG,KAAK,CAAC;IAChM2B,OAAO,CAACC,UAAU,CAAC,CAACrB,iBAAiB,EAAE,mBAAmB,EAAE,gBAAgB,CAAC;EAC/E;EACA,MAAM;IACJsB;EACF,CAAC,GAAG5C,KAAK,CAAC6C,UAAU,CAACvC,aAAa,CAAC;EACnC,MAAMY,SAAS,GAAG0B,YAAY,CAAC,QAAQ,EAAEzB,kBAAkB,CAAC;EAC5D;EACA,MAAM,CAAC2B,MAAM,CAAC,GAAG1C,SAAS,CAAC,YAAY,EAAE,CAACa,EAAE,GAAGF,KAAK,CAACgC,aAAa,MAAM,IAAI,IAAI9B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6B,MAAM,CAAC;EACnH,OAAO,aAAa9C,KAAK,CAACkC,aAAa,CAAC3B,MAAM,EAAEyC,MAAM,CAACC,MAAM,CAAC;IAC5DjC,GAAG,EAAEA,GAAG;IACRkC,UAAU,EAAE;EACd,CAAC,EAAE/C,IAAI,CAACY,KAAK,EAAE,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC,EAAE;IACnDG,SAAS,EAAEA,SAAS;IACpBG,cAAc,EAAEA,cAAc,IAAIC,iBAAiB;IACnDyB,aAAa,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAElC,KAAK,CAACgC,aAAa,CAAC,EAAE;MACnED;IACF,CAAC,CAAC;IACF1B,SAAS,EAAEnB,UAAU,CAAC,GAAGiB,SAAS,gBAAgB,EAAEE,SAAS,CAAC;IAC9D+B,IAAI,EAAE5C,MAAM,CAAC6C,+BAA+B;IAC5C;IACAvB;EACF,CAAC,CAAC,EAAEE,cAAc,CAAC;AACrB,CAAC;AACD,MAAMsB,eAAe,GAAG,aAAarD,KAAK,CAACsD,UAAU,CAACxC,YAAY,CAAC;AACnE,IAAIyB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCY,eAAe,CAACE,WAAW,GAAG,cAAc;AAC9C;AACA,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}