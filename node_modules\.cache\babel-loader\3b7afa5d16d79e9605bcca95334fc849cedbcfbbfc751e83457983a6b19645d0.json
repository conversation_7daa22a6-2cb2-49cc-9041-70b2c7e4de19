{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\nimport devicesData from '../data/devices.json'; // 导入设备数据\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel = null; // 存储原始模型数据\nlet skeleton = null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n  bsm: 'changli/cloud/v2x/obu/bsm',\n  rsm: 'changli/cloud/v2x/rsu/rsm',\n  scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',\n  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat' // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (!lastPosition) {\n      lastPosition = newPos.clone();\n      return newPos;\n    }\n    const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n    const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n    const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n    lastPosition.set(filteredX, filteredY, filteredZ);\n    return lastPosition.clone();\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  const lastPos = vehicleLastPositions.get(vehicleId);\n\n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n\n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n\n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (lastRotation === null) {\n      lastRotation = newRotation;\n      return newRotation;\n    }\n\n    // 处理角度跳变（从360度到0度或反之）\n    let diff = newRotation - lastRotation;\n    if (diff > Math.PI) diff -= 2 * Math.PI;\n    if (diff < -Math.PI) diff += 2 * Math.PI;\n    const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n    lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const lastRot = vehicleLastRotations.get(vehicleId);\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n\n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\n// 在文件顶部添加\nconst mixersToCleanup = new Set();\nconst bonesMap = new Map();\n\n// 在文件顶部添加资源管理器\nconst resourceManager = {\n  mixers: new Set(),\n  bones: new Map(),\n  actions: new Map(),\n  models: new Set(),\n  addMixer(mixer, model) {\n    this.mixers.add(mixer);\n    if (model) {\n      this.models.add(model);\n      // 记录骨骼\n      model.traverse(object => {\n        if (object.isBone) {\n          this.bones.set(object.uuid, object);\n        }\n      });\n    }\n    return mixer;\n  },\n  addAction(action, mixer) {\n    if (!this.actions.has(mixer)) {\n      this.actions.set(mixer, new Set());\n    }\n    this.actions.get(mixer).add(action);\n    return action;\n  },\n  removeMixer(mixer) {\n    if (this.mixers.has(mixer)) {\n      try {\n        // 停止并清理所有动作\n        if (this.actions.has(mixer)) {\n          this.actions.get(mixer).forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          this.actions.delete(mixer);\n        }\n\n        // 停止混合器\n        if (typeof mixer.stopAllAction === 'function') {\n          mixer.stopAllAction();\n        }\n\n        // 清理混合器的根对象\n        const root = mixer.getRoot();\n        if (root) {\n          this.models.delete(root);\n          root.traverse(object => {\n            if (object && object.isBone) {\n              this.bones.delete(object.uuid);\n            }\n            if (object && object.animations) {\n              object.animations.length = 0;\n            }\n          });\n\n          // 安全地清理缓存\n          try {\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n\n            // 这里是发生错误的地方，添加防御性检查\n            if (typeof mixer.uncacheClip === 'function') {\n              // 不直接调用uncacheClip(null)，而是获取混合器中的所有动画片段并逐个清理\n              const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n              clips.forEach(clip => {\n                if (clip && clip.uuid) {\n                  mixer.uncacheClip(clip);\n                }\n              });\n            }\n          } catch (e) {\n            console.log('在清理动画混合器时发生非致命错误:', e);\n            // 继续执行其余清理操作\n          }\n        }\n        this.mixers.delete(mixer);\n      } catch (error) {\n        console.error('清理动画混合器时发生错误:', error);\n        // 确保即使出错也会从集合中移除\n        this.mixers.delete(mixer);\n      }\n    }\n  },\n  cleanup() {\n    try {\n      // 清理动作\n      this.actions.forEach((actions, mixer) => {\n        try {\n          actions.forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          actions.clear();\n        } catch (e) {\n          console.log('清理动作时发生非致命错误:', e);\n        }\n      });\n      this.actions.clear();\n\n      // 清理混合器\n      this.mixers.forEach(mixer => {\n        try {\n          if (typeof mixer.stopAllAction === 'function') {\n            mixer.stopAllAction();\n          }\n          const root = mixer.getRoot();\n          if (root) {\n            root.traverse(object => {\n              if (object && object.animations) {\n                object.animations.length = 0;\n              }\n            });\n\n            // 安全清理\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n\n            // 安全清理动画片段\n            try {\n              if (mixer._actions && Array.isArray(mixer._actions)) {\n                const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n                clips.forEach(clip => {\n                  if (clip && clip.uuid && typeof mixer.uncacheClip === 'function') {\n                    mixer.uncacheClip(clip);\n                  }\n                });\n              }\n            } catch (e) {\n              console.log('清理动画片段时发生非致命错误:', e);\n            }\n          }\n        } catch (e) {\n          console.log('清理混合器时发生非致命错误:', e);\n        }\n      });\n      this.mixers.clear();\n\n      // 清理骨骼\n      this.bones.forEach(bone => {\n        if (bone.parent) {\n          bone.parent.remove(bone);\n        }\n        if (bone.matrix) bone.matrix.identity();\n        if (bone.matrixWorld) bone.matrixWorld.identity();\n      });\n      this.bones.clear();\n\n      // 清理模型\n      this.models.forEach(model => {\n        if (model.parent) {\n          model.parent.remove(model);\n        }\n        model.traverse(object => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n          }\n          if (object.animations) {\n            object.animations.length = 0;\n          }\n        });\n      });\n      this.models.clear();\n    } catch (error) {\n      console.error('全局清理过程中发生错误:', error);\n      // 即使发生错误也尝试清空集合\n      this.actions.clear();\n      this.mixers.clear();\n      this.bones.clear();\n      this.models.clear();\n    }\n  }\n};\n\n// 修改创建动画混合器的函数\nconst createAnimationMixer = model => {\n  const mixer = new THREE.AnimationMixer(model);\n  return resourceManager.addMixer(mixer, model);\n};\n\n// 修改动画动作创建的函数\nconst createAction = (clip, mixer, model) => {\n  const action = mixer.clipAction(clip, model);\n  return resourceManager.addAction(action, mixer);\n};\n\n// 在CampusModel组件顶部添加消息缓存\nconst processedMessageIds = new Set();\nconst CampusModel = ({\n  className,\n  onCurrentRSUChange,\n  selectedRSUs\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n\n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n\n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = new Map(); // 使用全局Map存储所有行人的动画混合器（不再使用useRef）\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,\n    // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null,\n    phases: []\n  });\n\n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n\n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n\n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n\n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '65px',\n    // 从 60px 改为 65px\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '65px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({\n    topic: '',\n    content: ''\n  });\n\n  // 1. 在CampusModel组件内添加设备弹框状态\n  const [devicePopover, setDevicePopover] = useState({\n    visible: false,\n    position: {\n      x: 0,\n      y: 0\n    },\n    device: null\n  });\n\n  // 2. 设备弹框关闭函数\n  const handleCloseDevicePopover = () => {\n    setDevicePopover({\n      visible: false,\n      position: {\n        x: 0,\n        y: 0\n      },\n      device: null\n    });\n  };\n\n  // 3. 设备弹框内容渲染函数\n  const renderDevicePopoverContent = device => {\n    if (!device) return null;\n    // 判断是否为摄像头\n    if (device.type === 'camera') {\n      // 摄像头显示视频流\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: 320,\n          height: 180,\n          background: '#000',\n          borderRadius: 6,\n          overflow: 'hidden',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(React.Suspense, {\n          fallback: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#fff',\n              textAlign: 'center',\n              paddingTop: 60\n            },\n            children: \"\\u89C6\\u9891\\u52A0\\u8F7D\\u4E2D...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 37\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(VideoPlayer, {\n            deviceId: device.id,\n            rtspUrl: device.rtspUrl\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '8px',\n            background: 'rgba(0,0,0,0.7)',\n            color: '#fff',\n            fontSize: 13,\n            borderRadius: 6,\n            marginTop: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"\\u540D\\u79F0\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 18\n            }, this), device.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"\\u7C7B\\u578B\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 18\n            }, this), device.type]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"\\u72B6\\u6001\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 18\n            }, this), device.status]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"\\u4F4D\\u7F6E\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 18\n            }, this), device.location]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"IP\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 18\n            }, this), device.ipAddress || '-']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"\\u63CF\\u8FF0\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 18\n            }, this), device.description || '-']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 9\n      }, this);\n    } else {\n      // 其他设备显示图片\n      const imgPath = `${BASE_URL}/images/${device.type}.png`;\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: 220\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            marginBottom: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: imgPath,\n            alt: device.type,\n            style: {\n              width: 80,\n              height: 80,\n              objectFit: 'contain',\n              background: '#fff',\n              borderRadius: 8,\n              border: '1px solid #eee'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '8px',\n            background: 'rgba(0,0,0,0.7)',\n            color: '#fff',\n            fontSize: 13,\n            borderRadius: 6\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"\\u540D\\u79F0\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 18\n            }, this), device.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"\\u7C7B\\u578B\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 18\n            }, this), device.type]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"\\u72B6\\u6001\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 18\n            }, this), device.status]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"\\u4F4D\\u7F6E\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 18\n            }, this), device.location]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"IP\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 18\n            }, this), device.ipAddress || '-']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"b\", {\n              children: \"\\u63CF\\u8FF0\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 18\n            }, this), device.description || '-']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 9\n      }, this);\n    }\n  };\n\n  // 4. 引入VideoPlayer组件\n  // import VideoPlayer from './VideoPlayer';\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n\n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n\n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n\n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos).to({\n          x: 0,\n          y: 300,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        }).start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp).to({\n          x: 0,\n          y: 1,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        }).start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n\n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget).to({\n          x: 0,\n          y: 0,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        }).start();\n\n        // 启用控制器\n        controls.enabled = true;\n\n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x + 50, 70, -modelCoords.y + 50);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n\n      // 如果该路口有红绿灯，自动显示红绿灯弹窗\n      if (intersection.hasTrafficLight !== false && intersection.interId) {\n        console.log(`路口 ${intersection.name} 有红绿灯，准备显示红绿灯状态`);\n\n        // 延迟300ms调用以确保场景已更新\n        setTimeout(() => {\n          // 检查多种可能的ID格式\n          let interId = intersection.interId;\n\n          // 使用全局的showTrafficLightPopup函数显示红绿灯弹窗\n          if (window.showTrafficLightPopup) {\n            window.showTrafficLightPopup(interId);\n            console.log(`已触发路口 ${intersection.name} (ID: ${interId}) 的红绿灯弹窗显示`);\n          } else {\n            console.error('找不到显示红绿灯弹窗的函数');\n          }\n        }, 300);\n      } else {\n        console.log(`路口 ${intersection.name} 没有红绿灯或缺少ID，不显示红绿灯状态`);\n\n        // 如果有弹窗正在显示，则关闭它\n        if (window._setTrafficLightPopover) {\n          window._setTrafficLightPopover({\n            visible: false\n          });\n        }\n      }\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        var _payload$data;\n        // console.log('收到RSM消息:', payload);\n\n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n\n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n          return;\n        }\n\n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n\n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n\n        const participants = ((_payload$data = payload.data) === null || _payload$data === void 0 ? void 0 : _payload$data.participants) || [];\n        const rsuid = payload.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id = deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = type === '3' ? SkeletonUtils.clone(preloadedPeopleModel) : preloadedModel.clone();\n              // 根据类型调整高度和缩放\n              const height = type === '3' ? 2.0 : 1.0;\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n\n              // 如果是行人类型，设置缩放和创建动画\n              if (type === '3') {\n                // newModel.scale.set(0.005, 0.005, 0.005);\n                newModel.scale.set(4, 4, 4);\n\n                // 使用resourceManager创建并管理动画混合器\n                const mixer = createAnimationMixer(newModel);\n                if (peopleBaseModel && peopleBaseModel.animations && peopleBaseModel.animations.length > 0) {\n                  // 只创建一个动作并添加到资源管理器\n                  const action = createAction(peopleBaseModel.animations[0], mixer, newModel);\n                  action.play();\n                }\n\n                // 保存到全局Map中(不再使用useRef)\n                peopleAnimationMixers.set(id, mixer);\n              }\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 1000;\n        const currentIds = new Set(participants.map(p => deviceMac + p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            // 如果是行人，清理动画混合器\n            if (modelData.type === '3' && peopleAnimationMixers.has(id)) {\n              const mixer = peopleAnimationMixers.get(id);\n              // 使用resourceManager清理混合器\n              resourceManager.removeMixer(mixer);\n              peopleAnimationMixers.delete(id);\n            }\n\n            // 从场景移除模型\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n\n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n\n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n\n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n\n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid,\n          // 直接传递车辆ID\n          data: {\n            // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n\n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n\n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n\n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n\n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n\n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n\n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n\n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n\n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n\n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n\n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          scene.add(newVehicleModel);\n\n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1',\n            // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n\n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position).to({\n            y: 0.5\n          }, 500).easing(TWEEN.Easing.Quadratic.Out).start();\n\n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({\n                opacity: 0.6\n              }).to({\n                opacity: 1.0\n              }, 500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function () {\n                child.material.opacity = this.opacity;\n                child.material.needsUpdate = true;\n              }).start();\n            }\n          });\n\n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({\n            opacity: 0.6\n          }).to({\n            opacity: 1.0\n          }, 500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function () {\n            speedLabel.material.opacity = this.opacity;\n            speedLabel.material.needsUpdate = true;\n          }).start();\n\n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n\n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n\n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n\n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n\n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n\n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 增加到10秒，避免频繁清理造成闪烁\n\n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n\n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1;\n            modelData.model.traverse(child => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n\n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n\n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            modelData.model.traverse(child => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n\n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n\n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n\n        try {\n          const payload = JSON.parse(message);\n\n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n\n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n\n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n\n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n\n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n\n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? getDirectionFromCode(phase.trafficDirec) : getPhaseDirection(phaseId);\n\n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n\n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n\n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n\n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n\n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n\n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n\n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n\n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && (window.currentPopoverIdRef.current === modelKey || window.currentPopoverIdRef.current === String(modelKey) || window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n\n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n        return;\n      }\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件，确保包含mac和timestamp\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data,\n          mac: payload.mac,\n          tm: payload.tm\n        }, '*');\n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(parseFloat(rsiData.posLong), parseFloat(rsiData.posLat));\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          switch (eventType) {\n            case '401':\n              // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':\n              // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':\n              // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':\n              // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':\n              // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002':\n              // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':\n              // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n\n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n\n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch (sceneType) {\n          case '2':\n            // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':\n            // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':\n            // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':\n            // 限速提醒\n            const speedLimit = sceneData.eventData1; // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':\n            // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':\n            // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':\n            // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':\n            // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':\n            // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':\n            // 信号灯优先\n            const priorityType = sceneData.eventData1; // 优先类型\n            const duration = sceneData.eventData2; // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 检查消息ID是否已处理过\n          if (message.messageId) {\n            if (processedMessageIds.has(message.messageId)) {\n              // console.log('跳过重复消息:', message.messageId);\n              return;\n            }\n\n            // 添加到已处理集合\n            processedMessageIds.add(message.messageId);\n\n            // 限制缓存大小，防止内存泄漏\n            if (processedMessageIds.size > 1000) {\n              // 转换为数组，删除最早的100个元素\n              const idsArray = Array.from(processedMessageIds);\n              for (let i = 0; i < 100; i++) {\n                processedMessageIds.delete(idsArray[i]);\n              }\n            }\n          }\n\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/vehicle.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n\n        // 检查scene是否初始化\n        if (scene) {\n          scene.add(model);\n\n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } else {\n          console.error('无法添加模型：场景未初始化');\n        }\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画 - 只更新存在的混合器\n      const deltaTime = clock.getDelta();\n\n      // 使用Map而不是ref.current\n      if (peopleAnimationMixers.size > 0) {\n        peopleAnimationMixers.forEach(mixer => {\n          mixer.update(deltaTime);\n        });\n      }\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 200, -50 * Math.sin(adjustedRotation));\n\n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n\n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n\n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n\n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 路口视角模式\n        controls.update();\n      }\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 停止渲染循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 停止所有 TWEEN 动画\n      TWEEN.removeAll();\n\n      // 3. 清理所有动画混合器\n      peopleAnimationMixers.forEach(mixer => {\n        resourceManager.removeMixer(mixer);\n      });\n      peopleAnimationMixers.clear();\n\n      // 4. 清理所有其他资源\n      resourceManager.cleanup();\n\n      // 5. 清理场景中的所有对象\n      if (scene) {\n        const objectsToRemove = [];\n        scene.traverse(object => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n            if (object !== scene) {\n              objectsToRemove.push(object);\n            }\n          }\n        });\n\n        // 从场景中移除对象\n        objectsToRemove.forEach(obj => {\n          if (obj.parent) {\n            obj.parent.remove(obj);\n          }\n        });\n        scene.clear();\n      }\n\n      // 6. 清理渲染器\n      if (renderer) {\n        renderer.setAnimationLoop(null);\n        if (containerRef.current && renderer.domElement && renderer.domElement.parentNode === containerRef.current) {\n          containerRef.current.removeChild(renderer.domElement);\n        }\n        renderer.dispose();\n        renderer.forceContextLoss();\n      }\n\n      // 7. 清理其他资源\n      if (controls) {\n        controls.dispose();\n      }\n\n      // 8. 清理数据结构\n      vehicleLastPositions.clear();\n      vehicleLastRotations.clear();\n      deviceTimestamps.clear();\n      vehicleModels.clear();\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n\n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n\n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n\n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n\n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {\n          // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 2000);\n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n\n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = event => {\n        if (scene && cameraRef.current) {\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current);\n        }\n      };\n\n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n\n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n\n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({\n      color: 0x333333\n    });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n\n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({\n      color: 0x666666\n    });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n\n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          //\n          transparent: false,\n          opacity: 0.1,\n          // 几乎透明\n          depthWrite: false\n        });\n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0); // 放在红绿灯位置\n\n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n\n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500); // 延迟略长于debugTrafficLights\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n\n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n\n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        // 查找第一个带有红绿灯的路口\n        const firstTrafficLightIntersection = intersectionsData.intersections.find(intersection => intersection.hasTrafficLight !== false && intersection.interId);\n\n        // 如果找到带红绿灯的路口，优先选择它；否则选择第一个路口\n        const targetIntersection = firstTrafficLightIntersection || intersectionsData.intersections[0];\n        console.log('自动选择路口:', targetIntersection.name, '具有红绿灯:', firstTrafficLightIntersection ? '是' : '否');\n\n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(targetIntersection.name);\n        }, 2000);\n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n\n  // 新增：在场景初始化后渲染所有路口entrance的设备图标\n  const renderEntranceDeviceIcons = converterInstance => {\n    if (!scene || typeof scene.traverse !== 'function' || !converterInstance) return;\n    try {\n      // 先移除所有旧的设备图标组（用 children.filter 更安全）\n      scene.children.filter(obj => obj.userData && obj.userData.isEntranceDeviceIcons).forEach(obj => scene.remove(obj));\n      // 遍历所有路口\n      intersectionsData.intersections.forEach(intersection => {\n        if (!intersection.entrances || !Array.isArray(intersection.entrances)) return;\n        // 遍历每个路口的所有 entrance\n        intersection.entrances.forEach(entrance => {\n          // 检查经纬度有效性\n          if (!entrance.longitude || !entrance.latitude || isNaN(parseFloat(entrance.longitude)) || isNaN(parseFloat(entrance.latitude))) {\n            return; // 跳过无效经纬度的入口\n          }\n\n          // 查找该路口该entrance下所有设备\n          const devices = devicesData.devices.filter(d => d.location === intersection.name && d.entrance === entrance.name);\n          if (devices.length === 0) return;\n          // 经纬度转模型坐标\n          console.log('渲染路口', intersection.name, '入口', entrance.name, '设备数量', devices.length);\n          const modelPos = converterInstance.wgs84ToModel(parseFloat(entrance.longitude), parseFloat(entrance.latitude));\n          // 创建一个组用于存放所有图标\n          const group = new THREE.Group();\n          group.position.set(modelPos.x, 15, -modelPos.y); // 上方15米\n          group.userData = {\n            isEntranceDeviceIcons: true\n          };\n          // 图标排成一排，居中\n          const iconSize = 32; // px\n          const iconSpacing = 8; // px\n          const totalWidth = devices.length * iconSize + (devices.length - 1) * iconSpacing;\n          // 以三维单位为准，假设1单位=1米，24px约等于0.6米\n          const size3D = 4.0; // 图标尺寸加大\n          const spacing3D = 0.5; // 图标间距加大\n          const startX = -((devices.length - 1) * (size3D + spacing3D)) / 2;\n          devices.forEach((device, idx) => {\n            // 创建一个平面用于显示SVG图标\n            const textureLoader = new THREE.TextureLoader();\n            const iconPath = `${BASE_URL}/images/${device.type}.svg`;\n            // 图标材质\n            const iconMaterial = new THREE.MeshBasicMaterial({\n              map: textureLoader.load(iconPath),\n              transparent: true,\n              opacity: 1 // 图标完全不透明\n            });\n            // 图标背景板材质\n            const bgMaterial = new THREE.MeshBasicMaterial({\n              color: 0x000000,\n              transparent: true,\n              opacity: 0.7 // 半透明黑色\n            });\n            // 背景板尺寸略大于图标\n            const bgWidth = size3D * 1.25;\n            const bgHeight = size3D * 1.25;\n            // 背景板几何体（圆角矩形）\n            // 由于Three.js没有内置圆角矩形，使用PlaneGeometry近似\n            const bgGeometry = new THREE.PlaneGeometry(bgWidth, bgHeight);\n            const bgMesh = new THREE.Mesh(bgGeometry, bgMaterial);\n            // 图标几何体\n            const iconGeometry = new THREE.PlaneGeometry(size3D, size3D);\n            const iconMesh = new THREE.Mesh(iconGeometry, iconMaterial);\n            // 图标略微前移，避免Z轴重叠闪烁\n            iconMesh.position.set(0, 0, 0.01);\n            // 创建一个组，包含背景和图标\n            const iconGroup = new THREE.Group();\n            iconGroup.add(bgMesh);\n            iconGroup.add(iconMesh);\n            // 整体平移到正确位置\n            iconGroup.position.set(startX + idx * (size3D + spacing3D), 0, 0);\n            // 不再固定旋转角度，后续在动画循环中让其始终面向相机\n            iconGroup.renderOrder = 999; // 提高渲染优先级\n            iconGroup.userData = {\n              deviceId: device.id,\n              deviceType: device.type,\n              entrance: entrance.name,\n              isEntranceDeviceIcon: true\n            };\n            group.add(iconGroup);\n          });\n          scene.add(group);\n        });\n      });\n    } catch (e) {\n      console.warn('scene.traverse error', e);\n      return;\n    }\n  };\n\n  // 在场景初始化后调用渲染设备图标\n  useEffect(() => {\n    if (scene && typeof scene.traverse === 'function' && converter.current) {\n      renderEntranceDeviceIcons(converter.current);\n    }\n  }, [scene, converter.current]);\n\n  // 新增：每帧让所有设备图标组始终面向相机（billboard效果）\n  useEffect(() => {\n    if (!scene || !cameraRef.current) return;\n    const animateBillboard = () => {\n      // 遍历所有设备图标组，让其正对相机\n      scene.children.forEach(obj => {\n        if (obj.userData && obj.userData.isEntranceDeviceIcons) {\n          obj.lookAt(cameraRef.current.position.x, obj.position.y, cameraRef.current.position.z);\n        }\n      });\n      requestAnimationFrame(animateBillboard);\n    };\n    animateBillboard();\n  }, [scene]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u533A\\u57DF\\u9009\\u62E9\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2375,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u533A\\u57DF\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      options: intersectionsData.intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      },\n      value: selectedIntersection ? selectedIntersection.name : undefined\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2376,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2392,\n      columnNumber: 7\n    }, this), trafficLightPopover.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${trafficLightPopover.position.x}px`,\n        top: `${trafficLightPopover.position.y}px`,\n        transform: 'translate(-50%, -100%)',\n        zIndex: 1003,\n        backgroundColor: 'rgba(0, 0, 0, 0.85)',\n        color: 'white',\n        borderRadius: '4px',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n        padding: '0',\n        maxWidth: '240px',\n        // 缩小最大宽度\n        fontSize: '12px' // 缩小字体\n      },\n      children: [trafficLightPopover.content, /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          position: 'absolute',\n          top: '0px',\n          right: '0px',\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          fontSize: '12px',\n          cursor: 'pointer',\n          padding: '2px 6px'\n        },\n        onClick: () => handleClosePopover(setTrafficLightPopover),\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2413,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2396,\n      columnNumber: 9\n    }, this), devicePopover.visible && devicePopover.device && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${devicePopover.position.x}px`,\n        top: `${devicePopover.position.y}px`,\n        transform: 'translate(-10px, 10px)',\n        zIndex: 2000,\n        background: 'rgba(0,0,0,0.92)',\n        color: '#fff',\n        borderRadius: '8px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.25)',\n        padding: '0',\n        minWidth: '220px',\n        maxWidth: '340px',\n        fontSize: '13px',\n        pointerEvents: 'auto'\n      },\n      children: [renderDevicePopoverContent(devicePopover.device), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          position: 'absolute',\n          top: '2px',\n          right: '6px',\n          background: 'none',\n          border: 'none',\n          color: '#fff',\n          fontSize: '16px',\n          cursor: 'pointer',\n          zIndex: 10\n        },\n        onClick: handleCloseDevicePopover,\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2453,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2434,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2471,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2481,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2470,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"Tz3MZ24iei8SmEQwBMZQyYXmz80=\");\n_c = CampusModel;\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 12,\n    // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    backgroundColor: parameters.backgroundColor || {\n      r: 255,\n      g: 255,\n      b: 255,\n      a: 0.8\n    },\n    textColor: parameters.textColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n\n  // 设置字体\n  // context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n\n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n\n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  canvas.width = width;\n  canvas.height = height;\n\n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n\n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n\n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n\n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n\n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n\n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n\n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n\n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n\n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = {\n    text: text,\n    params: params\n  };\n  return sprite;\n}\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    try {\n      const [trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`), loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`)]);\n\n      // 处理机动车模型\n      console.log('加载车辆模型...');\n      preloadedVehicleModel = vehicleGltf.scene;\n      preloadedVehicleModel.traverse(child => {\n        if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n            color: 0xff0000,\n            // 0xff0000, //红色 0xffffff,白色\n            metalness: 0.2,\n            roughness: 0.1,\n            envMapIntensity: 1.0\n          });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n        }\n      });\n      console.log('加载非机动车模型...');\n      // 处理非机动车模型\n      preloadedCyclistModel = cyclistGltf.scene;\n      // 设置非机动车模型的缩放\n      preloadedCyclistModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedCyclistModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n      console.log('加载行人模型...');\n      // 处理行人模型\n      preloadedPeopleModel = peopleGltf.scene;\n      // 设置行人模型的缩放\n      // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n      // 保持原始材质\n      preloadedPeopleModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n        if (child.isMesh) {\n          child.castShadow = true;\n        }\n      });\n\n      // 保存行人动画数据\n      console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n      if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n        console.log('找到行人动画:', peopleGltf.animations.length, '个');\n        peopleBaseModel = peopleGltf;\n      } else {\n        console.warn('行人模型没有包含动画数据');\n      }\n      console.log('加载红绿灯模型...');\n\n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      console.log('红绿灯模型：', preloadedTrafficLightModel);\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n\n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n\n        // // 尝试单独加载红绿灯模型\n        // if (!preloadedTrafficLightModel) {\n        //   console.log('正在单独加载红绿灯模型...');\n        //   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n        //   preloadedTrafficLightModel = trafficLightGltf.scene;\n        //   preloadedTrafficLightModel.scale.set(3, 3, 3);\n        //   console.log('红绿灯模型加载成功');\n        // }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = type => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  try {\n    // 创建一个新的警告标记\n    const sprite = createTextSprite(text);\n    sprite.position.set(position.x, 10, -position.y); // 设置位置，稍微升高以便可见\n\n    // 为标记添加一个定时器，在1秒后自动移除\n    setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n        scene.remove(sprite);\n      }\n    }, 100);\n\n    // 将标记添加到场景中\n    scene.add(sprite);\n\n    // console.log('添加场景事件标记:', {\n    //   位置: position,\n    //   文本: text,\n    //   颜色: color\n    // });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = converterInstance => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n\n  // 检查红绿灯模型是否已加载\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载，尝试重新加载...');\n    // 尝试重新加载红绿灯模型\n    const loader = new GLTFLoader();\n    loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`).then(trafficLightGltf => {\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('红绿灯模型重新加载成功，开始创建红绿灯...');\n      // 重新调用创建函数\n      createTrafficLights(converterInstance);\n    }).catch(error => {\n      console.error('红绿灯模型重新加载失败:', error);\n      // 如果加载失败，使用简单的替代物体\n      createFallbackTrafficLights(converterInstance);\n    });\n    return;\n  }\n\n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach(lightObj => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      try {\n        // 确保模型存在且可以克隆\n        if (!preloadedTrafficLightModel || !preloadedTrafficLightModel.clone) {\n          throw new Error('红绿灯模型无效或无法克隆');\n        }\n\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n\n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n\n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n\n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n\n        // 设置材质属性\n        trafficLightModel.traverse(child => {\n          if (child.isMesh) {\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            child.renderOrder = 100;\n          }\n        });\n\n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n\n        // 将红绿灯添加到场景中\n        // scene.add(trafficLightModel);\n\n        // ========== 新增：在红绿灯地面添加指北针图标 =============\n        // 创建一个平面用于显示指北针SVG图标\n        const compassTextureLoader = new THREE.TextureLoader();\n        const compassIconPath = `${BASE_URL}/images/compass.svg`; // public目录下的路径 \n        const compassMaterial = new THREE.MeshBasicMaterial({\n          map: compassTextureLoader.load(compassIconPath),\n          transparent: true,\n          opacity: 1\n        });\n        // 平面几何体，5x5米\n        const compassGeometry = new THREE.PlaneGeometry(5, 5);\n        const compassMesh = new THREE.Mesh(compassGeometry, compassMaterial);\n        // 指北针放在红绿灯正下方地面（y=0.1，略高于地面防止Z冲突）\n        compassMesh.position.set(modelPos.x + 20, 1.5, -(modelPos.y + 20));\n        // 指北针朝上，旋转x轴-90度\n        compassMesh.rotation.x = -Math.PI / 2;\n        // 渲染优先级高，避免被地面遮挡\n        compassMesh.renderOrder = 101;\n        // 可选：添加userData标记\n        compassMesh.userData = {\n          type: 'compassIcon',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        // 添加到场景\n        scene.add(compassMesh);\n        // ========== 指北针图标添加结束 =============\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        // createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n\n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = converterInstance => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({\n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n\n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n\n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n\n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,\n    // 完全透明\n    depthWrite: false\n  });\n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  trafficLightModel.add(collider);\n\n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n\n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n\n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: 0xFF0000\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n\n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  trafficLightModel.add(lightMesh);\n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = phaseId => {\n  switch (phaseId) {\n    case '1':\n      return '北进口左转';\n    case '2':\n      return '北进口直行';\n    case '3':\n      return '北进口右转';\n    case '5':\n      return '东进口左转';\n    case '6':\n      return '东进口直行';\n    case '7':\n      return '东进口右转';\n    case '9':\n      return '南进口左转';\n    case '10':\n      return '南进口直行';\n    case '11':\n      return '南进口右转';\n    case '13':\n      return '西进口左转';\n    case '14':\n      return '西进口直行';\n    case '15':\n      return '西进口右转';\n    default:\n      return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = dirCode => {\n  switch (dirCode) {\n    case 'N':\n      return '北向南';\n    case 'S':\n      return '南向北';\n    case 'E':\n      return '东向西';\n    case 'W':\n      return '西向东';\n    case 'NE':\n      return '东北向西南';\n    case 'NW':\n      return '西北向东南';\n    case 'SE':\n      return '东南向西北';\n    case 'SW':\n      return '西南向东北';\n    default:\n      return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n\n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = (event.clientX - rect.left) / container.clientWidth * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n\n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Line.threshold = 1;\n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  console.log('鼠标点击位置:', mouseX, mouseY);\n\n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n\n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'userData:', intersect.object.userData);\n    });\n\n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n\n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n\n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n\n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  console.log('射线检测到的对象数量:', intersects.length);\n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', 'userData:', obj.userData, '距离:', intersect.distance);\n    });\n\n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n\n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n\n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.1; // 设置一个阈值，只有距离小于此值的才会被选中\n\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n\n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n\n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      console.log(`路口 ${interId} 的距离:`, distance);\n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = {\n          interId,\n          distance\n        };\n      }\n    }\n  });\n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n\n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  console.log('未检测到任何红绿灯点击');\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = setPopoverState => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n\n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n\n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({\n    ...prev,\n    visible: false,\n    content: null,\n    // 清空内容\n    phases: [] // 清空相位信息\n  }));\n  console.log('弹窗已关闭，所有相关资源已清理');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = interId => {\n  try {\n    var _document$querySelect, _document$querySelect2;\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n\n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      return false;\n    }\n\n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n\n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 创建弹出窗口内容\n    let content;\n    if (stateInfo && stateInfo.phases) {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '220px',\n          maxHeight: '300px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          },\n          children: [intersection.name, \" (ID: \", interId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3288,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                break;\n            }\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '6px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '4px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: getPhaseDirection(phase.phaseId)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3314,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 3px',\n                    borderRadius: '2px'\n                  },\n                  children: phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3319,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3317,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3330,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3331,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3329,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3307,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '6px',\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3337,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3287,\n        columnNumber: 9\n      }, this);\n    } else {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          maxWidth: '200px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '8px'\n          },\n          children: intersection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u8DEF\\u53E3ID: \", interId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3347,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3344,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2 - 500;\n    const centerY = window.innerHeight / 2 - 500;\n\n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = (_document$querySelect = document.querySelector('#root')) === null || _document$querySelect === void 0 ? void 0 : (_document$querySelect2 = _document$querySelect.__REACT_INSTANCE) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.setTrafficLightPopover;\n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: {\n          x: centerX,\n          y: centerY\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      document.body.appendChild(popover);\n\n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  return list;\n};\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = interId => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n\n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 判断是否有相位数据\n    const hasPhaseData = stateInfo && stateInfo.phases && stateInfo.phases.length > 0;\n    let content;\n\n    // 指北针样式\n    const compassStyle = {\n      position: 'absolute',\n      top: '5px',\n      right: '25px',\n      width: '30px',\n      height: '30px',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      borderRadius: '50%',\n      background: 'rgba(0,0,0,0.1)',\n      zIndex: 10\n    };\n\n    // 指北针组件\n    const CompassIcon = () => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: compassStyle,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          transform: 'rotate(0deg)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#ff5252',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            lineHeight: '14px'\n          },\n          children: \"N\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3499,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            width: 0,\n            height: 0,\n            borderLeft: '6px solid transparent',\n            borderRight: '6px solid transparent',\n            borderBottom: '10px solid #ff5252',\n            marginTop: '-2px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3505,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3493,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 3492,\n      columnNumber: 7\n    }, this);\n    if (hasPhaseData) {\n      // 相位ID与方向/方式的映射\n      const phaseMap = {\n        '1': {\n          dir: 'N',\n          type: 'left'\n        },\n        '2': {\n          dir: 'N',\n          type: 'straight'\n        },\n        '3': {\n          dir: 'N',\n          type: 'right'\n        },\n        '5': {\n          dir: 'E',\n          type: 'left'\n        },\n        '6': {\n          dir: 'E',\n          type: 'straight'\n        },\n        '7': {\n          dir: 'E',\n          type: 'right'\n        },\n        '9': {\n          dir: 'S',\n          type: 'left'\n        },\n        '10': {\n          dir: 'S',\n          type: 'straight'\n        },\n        '11': {\n          dir: 'S',\n          type: 'right'\n        },\n        '13': {\n          dir: 'W',\n          type: 'left'\n        },\n        '14': {\n          dir: 'W',\n          type: 'straight'\n        },\n        '15': {\n          dir: 'W',\n          type: 'right'\n        }\n      };\n      const typeOrder = ['left', 'straight', 'right'];\n      const colorMap = {\n        G: '#00ff00',\n        Y: '#ffff00',\n        R: '#ff0000'\n      };\n      const dirData = {\n        N: {},\n        E: {},\n        S: {},\n        W: {}\n      };\n      stateInfo.phases.forEach(phase => {\n        const map = phaseMap[phase.phaseId];\n        if (map) {\n          dirData[map.dir][map.type] = {\n            color: colorMap[phase.trafficLight] || '#888',\n            remainTime: phase.remainTime\n          };\n        }\n      });\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '260px',\n          background: 'rgba(0,0,0,0.05)',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CompassIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3542,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '8px',\n            fontSize: '15px',\n            textAlign: 'center'\n          },\n          children: [intersection.name, \"\\u706F\\u6001\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3543,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateRows: '60px 60px 60px',\n            gridTemplateColumns: '60px 60px 60px',\n            justifyContent: 'center',\n            alignItems: 'center',\n            background: 'rgba(255,255,255,0.05)',\n            borderRadius: '8px',\n            margin: '0 auto',\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 1,\n              gridColumn: 2,\n              textAlign: 'center',\n              display: 'flex',\n              justifyContent: 'center',\n              width: '100%'\n            },\n            children: typeOrder.map((type, index) => {\n              // 反转左转和右转在数组中的顺序\n              let displayIndex = index;\n              if (index === 0) displayIndex = 2;else if (index === 2) displayIndex = 0;\n              const currentType = typeOrder[displayIndex];\n\n              // 计算与南边对齐的样式\n              const marginStyle = {};\n              if (currentType === 'left') {\n                // 左转箭头 (右侧显示)\n                marginStyle.marginRight = '0px';\n              } else if (currentType === 'straight') {\n                // 直行箭头 (中间显示)\n                marginStyle.marginLeft = '10px';\n                marginStyle.marginRight = '10px';\n              } else if (currentType === 'right') {\n                // 右转箭头 (左侧显示)\n                marginStyle.marginLeft = '0px';\n              }\n              return dirData.N[currentType] &&\n              /*#__PURE__*/\n              // <div key={currentType} style={{\n              //   display: 'flex', \n              //   flexDirection: 'column', \n              //   alignItems: 'center',\n              //   ...marginStyle\n              // }}>\n              _jsxDEV(\"div\", {\n                style: {\n                  marginRight: currentType === 'left' ? 0 : '10px',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    color: dirData.N[currentType].color,\n                    fontWeight: 'bold',\n                    marginBottom: '3px'\n                  },\n                  children: dirData.N[currentType].remainTime\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3585,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: dirData.N[currentType].color,\n                    fontSize: '20px',\n                    lineHeight: '20px'\n                  },\n                  children: currentType === 'left' ? '\\u{1F87A}' : currentType === 'straight' ? '\\u{1F87B}' : '\\u{1F878}'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3586,\n                  columnNumber: 21\n                }, this)]\n              }, currentType, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3584,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3557,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 3,\n              gridColumn: 2,\n              textAlign: 'center',\n              display: 'flex',\n              justifyContent: 'center',\n              width: '100%'\n            },\n            children: typeOrder.map(type => dirData.S[type] && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginRight: type === 'right' ? 0 : '10px',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: dirData.S[type].color,\n                  fontSize: '20px',\n                  lineHeight: '20px'\n                },\n                children: type === 'left' ? '\\u{1F878}' : type === 'straight' ? '\\u{1F879}' : '\\u{1F87A}'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3598,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: dirData.S[type].color,\n                  fontWeight: 'bold',\n                  marginTop: '3px'\n                },\n                children: dirData.S[type].remainTime\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3601,\n                columnNumber: 19\n              }, this)]\n            }, type, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3597,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3595,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 2,\n              gridColumn: 3,\n              textAlign: 'center'\n            },\n            children: typeOrder.map((type, index) => {\n              // 反转左转和右转在数组中的顺序\n              let displayIndex = index;\n              if (index === 0) displayIndex = 2;else if (index === 2) displayIndex = 0;\n              const currentType = typeOrder[displayIndex];\n              return dirData.E[currentType] && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '8px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'flex-start'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: dirData.E[currentType].color,\n                    fontSize: '20px',\n                    lineHeight: '20px'\n                  },\n                  children: currentType === 'left' ? '\\u{1F87B}' : currentType === 'straight' ? '\\u{1F878}' : '\\u{1F879}'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3619,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    color: dirData.E[currentType].color,\n                    fontWeight: 'bold',\n                    marginLeft: '5px'\n                  },\n                  children: dirData.E[currentType].remainTime\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3622,\n                  columnNumber: 21\n                }, this)]\n              }, currentType, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3618,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3608,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 2,\n              gridColumn: 1,\n              textAlign: 'center'\n            },\n            children: typeOrder.map(type => dirData.W[type] && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'flex-end'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: dirData.W[type].color,\n                  fontWeight: 'bold',\n                  marginRight: '5px'\n                },\n                children: dirData.W[type].remainTime\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3637,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: dirData.W[type].color,\n                  fontSize: '20px',\n                  lineHeight: '20px'\n                },\n                children: type === 'left' ? '\\u{1F879}' : type === 'straight' ? '\\u{1F87A}' : '\\u{1F87B}'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3643,\n                columnNumber: 19\n              }, this)]\n            }, type, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3636,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3634,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3544,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            fontSize: '11px',\n            color: '#888',\n            textAlign: 'center'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3650,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3541,\n        columnNumber: 9\n      }, this);\n    } else {\n      // 没有相位数据时显示的内容\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          width: '260px',\n          background: 'rgba(0,0,0,0.05)',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CompassIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3659,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '10px',\n            fontSize: '15px',\n            textAlign: 'center'\n          },\n          children: intersection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3660,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '20px 0',\n            color: '#ff9800',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            background: 'rgba(255,255,255,0.1)',\n            borderRadius: '8px',\n            marginBottom: '10px'\n          },\n          children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3661,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#888',\n            textAlign: 'center'\n          },\n          children: [\"\\u8DEF\\u53E3ID: \", interId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3673,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            fontSize: '11px',\n            color: '#888',\n            textAlign: 'center'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3676,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3658,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 设置弹窗位置在左上角区域\n    const x = 445;\n    const y = 250;\n\n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n\n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n\n    // 更新弹窗状态\n    if (window._setTrafficLightPopover) {\n      window._setTrafficLightPopover({\n        visible: true,\n        interId: interId,\n        position: {\n          x,\n          y\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n\n      // 设置定时更新\n      if (window.trafficLightUpdateTimerRef) {\n        window.trafficLightUpdateTimerRef.current = setInterval(() => {\n          window.showTrafficLightPopup(interId);\n        }, 1000);\n      }\n      return true;\n    } else {\n      console.error('无法找到setTrafficLightPopover函数');\n      return false;\n    }\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = object => {\n  let current = object;\n\n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n\n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n\n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n\n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n\n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n\n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = (x - rect.left) / canvas.clientWidth * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    console.log('归一化坐标:', mouseX, mouseY);\n\n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n\n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n\n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'position:', intersect.object.position.toArray(), 'userData:', intersect.object.userData);\n\n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      return true;\n    }\n\n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', '类型:', obj.type, '位置:', obj.position.toArray(), '距离:', intersect.distance, 'userData:', obj.userData);\n    });\n\n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        var _lightObj$intersectio;\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n\n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n\n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n\n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        if (isVisible) {\n          visibleCount++;\n        }\n        console.log(`红绿灯 ${interId}:`, {\n          名称: ((_lightObj$intersectio = lightObj.intersection) === null || _lightObj$intersectio === void 0 ? void 0 : _lightObj$intersectio.name) || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n\n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  var _trafficLight$interse, _trafficLight$interse2, _trafficLight$interse3;\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch (phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n\n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: (_trafficLight$interse = trafficLight.intersection) === null || _trafficLight$interse === void 0 ? void 0 : _trafficLight$interse.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n\n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = {\n    isLight: true\n  };\n\n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  console.log(`更新路口 ${((_trafficLight$interse2 = trafficLight.intersection) === null || _trafficLight$interse2 === void 0 ? void 0 : _trafficLight$interse2.name) || ((_trafficLight$interse3 = trafficLight.intersection) === null || _trafficLight$interse3 === void 0 ? void 0 : _trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "SkeletonUtils", "mqtt", "Select", "Popover", "intersectionsData", "devicesData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "peopleBaseModel", "skeleton", "lastPosition", "lastRotation", "ALPHA", "vehicleLastPositions", "Map", "vehicleLastRotations", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "process", "env", "REACT_APP_API_URL", "console", "log", "vehicleModels", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "clock", "Clock", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "vehicleId", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "has", "lastPos", "get", "distance", "distanceTo", "MAX_DISTANCE_THRESHOLD", "toFixed", "filteredPos", "Vector3", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "lastRot", "MAX_ANGLE_THRESHOLD", "abs", "TRAFFIC_LIGHT_UPDATE_INTERVAL", "mixersToCleanup", "Set", "bonesMap", "resourceManager", "mixers", "bones", "actions", "models", "addMixer", "mixer", "model", "add", "traverse", "object", "isBone", "uuid", "addAction", "action", "removeMixer", "for<PERSON>ach", "stop", "delete", "stopAllAction", "root", "getRoot", "animations", "length", "uncacheRoot", "uncacheAction", "uncacheClip", "clips", "_actions", "map", "a", "_clip", "filter", "Boolean", "clip", "e", "cleanup", "clear", "bone", "parent", "remove", "matrix", "identity", "matrixWorld", "<PERSON><PERSON><PERSON>", "geometry", "dispose", "material", "createAnimationMixer", "AnimationMixer", "createAction", "clipAction", "processedMessageIds", "CampusModel", "className", "onCurrentRSUChange", "selectedRSUs", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "mapRef", "lastCameraPosition", "lastCameraTarget", "cameraSmoothing", "prevAnimationTimeRef", "Date", "now", "peopleAnimationMixers", "peopleAnimations", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "currentPopoverIdRef", "trafficLightUpdateTimerRef", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "devicePopover", "setDevicePopover", "device", "handleCloseDevicePopover", "renderDevicePopoverContent", "type", "style", "height", "background", "overflow", "children", "Suspense", "fallback", "textAlign", "paddingTop", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "VideoPlayer", "deviceId", "id", "rtspUrl", "marginTop", "name", "status", "ip<PERSON><PERSON><PERSON>", "description", "imgPath", "marginBottom", "src", "alt", "objectFit", "switchToFollowView", "current", "enabled", "switchToGlobalView", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "target", "currentTarget", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "updateMatrix", "updateMatrixWorld", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "i", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "相机位置", "toArray", "目标点", "hasTrafficLight", "setTimeout", "showTrafficLightPopup", "handleMqttMessage", "message", "payload", "JSON", "parse", "_payload$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "participants", "rsuid", "participant", "partPtcId", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "newModel", "rotation", "scale", "play", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "p", "modelData", "bsmData", "bsmid", "newState", "partLong", "partLat", "postMessage", "source", "initialPosition", "initialRotation", "newPosition", "vehicleObj", "newVehicleModel", "child", "newMaterial", "emissive", "Color", "transparent", "needsUpdate", "speedLabel", "createTextSprite", "round", "r", "g", "b", "textColor", "renderOrder", "opacity", "is<PERSON><PERSON>", "Out", "filteredPosition", "timeSinceLastUpdate", "undefined", "originalTran<PERSON>arent", "originalOpacity", "m", "phasesInfo", "phase", "phaseId", "toString", "direction", "trafficDirec", "getDirectionFromCode", "getPhaseDirection", "lightState", "trafficLight", "remainTime", "parseInt", "phaseInfo", "push", "trafficLightKey", "String", "trafficLightModel", "updateTrafficLightVisual", "prev", "<PERSON><PERSON><PERSON>", "strId", "numId", "updateTime", "rsiData", "rsuId", "events", "rtes", "event", "eventId", "rteId", "eventType", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "messageId", "size", "idsArray", "from", "stringify", "onerror", "onclose", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "setIsVehicleLoaded", "xhr", "loaded", "total", "initializeScene", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "deltaTime", "<PERSON><PERSON><PERSON><PERSON>", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "targetCameraPosition", "targetLookAt", "lerp", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "removeAll", "objectsToRemove", "obj", "setAnimationLoop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "forceContextLoss", "handleMainVehicleChange", "intervalId", "setInterval", "removeEventListener", "clearInterval", "timer", "createTrafficLights", "clearTimeout", "handleClick", "handleMouseClick", "initScene", "createSimpleTrafficLight", "BoxGeometry", "MeshBasicMaterial", "<PERSON><PERSON>", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "addClickHelpers", "lightObj", "helperGeometry", "SphereGeometry", "helperMaterial", "depthWrite", "helper<PERSON><PERSON>", "userData", "isClickHelper", "firstTrafficLightIntersection", "targetIntersection", "renderEntranceDeviceIcons", "converterInstance", "isEntranceDeviceIcons", "entrances", "entrance", "isNaN", "devices", "d", "group", "iconSize", "iconSpacing", "totalWidth", "size3D", "spacing3D", "startX", "idx", "textureLoader", "TextureLoader", "iconPath", "iconMaterial", "bgMaterial", "bgWidth", "bgHeight", "bgGeometry", "PlaneGeometry", "bg<PERSON><PERSON>", "iconGeometry", "<PERSON><PERSON><PERSON>", "iconGroup", "deviceType", "isEntranceDeviceIcon", "warn", "animateBillboard", "placeholder", "onChange", "options", "label", "bordered", "dropdownStyle", "maxHeight", "ref", "max<PERSON><PERSON><PERSON>", "right", "onClick", "handleClosePopover", "min<PERSON><PERSON><PERSON>", "pointerEvents", "_c", "text", "parameters", "params", "fontFace", "borderThickness", "borderColor", "canvas", "document", "createElement", "context", "getContext", "textWidth", "measureText", "font", "textBaseline", "radius", "beginPath", "moveTo", "lineTo", "arcTo", "closePath", "strokeStyle", "lineWidth", "stroke", "fillStyle", "fill", "fillText", "texture", "CanvasTexture", "minFilter", "LinearFilter", "spriteMaterial", "SpriteMaterial", "sprite", "Sprite", "depthTest", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "trafficLightGltf", "vehicleGltf", "cyclistGltf", "peopleGltf", "all", "loadAsync", "materia", "<PERSON><PERSON><PERSON><PERSON>", "err", "types", "then", "catch", "createFallbackTrafficLights", "Error", "side", "DoubleSide", "compassTextureLoader", "compassIconPath", "compassMaterial", "compassGeometry", "compassMesh", "colliderGeometry", "colliderMaterial", "collider", "isCollider", "lightGeometry", "lightMaterial", "<PERSON><PERSON><PERSON>", "dirCode", "container", "sceneInstance", "cameraInstance", "clientX", "clientY", "rect", "getBoundingClientRect", "mouseX", "clientWidth", "mouseY", "clientHeight", "raycaster", "Raycaster", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "trafficLightObjects", "trafficLightIntersects", "intersectObjects", "intersect", "index", "getTrafficLightFromObject", "correctId", "intersects", "closestLight", "worldPos", "getWorldPosition", "screenPos", "project", "dx", "dy", "sqrt", "setPopoverState", "testTrafficLightClick", "_document$querySelect", "_document$querySelect2", "light", "lightModel", "stateInfo", "overflowY", "borderBottom", "paddingBottom", "lightColor", "justifyContent", "toLocaleTimeString", "centerX", "centerY", "__REACT_INSTANCE", "popover", "innerHTML", "body", "closeButton", "listTrafficLights", "list", "numericId", "hasPhaseData", "compassStyle", "alignItems", "CompassIcon", "flexDirection", "borderLeft", "borderRight", "phaseMap", "dir", "typeOrder", "colorMap", "G", "Y", "R", "dirD<PERSON>", "N", "E", "S", "W", "gridTemplateRows", "gridTemplateColumns", "margin", "gridRow", "gridColumn", "displayIndex", "currentType", "marginStyle", "marginRight", "marginLeft", "testClickDetection", "tlIntersects", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "distanceToCamera", "名称", "可见性", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "isLight", "emissiveIntensity", "PointLight", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js';\n\n\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\nimport devicesData from '../data/devices.json'; // 导入设备数据\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel= null; // 存储原始模型数据\nlet skeleton =null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  const lastPos = vehicleLastPositions.get(vehicleId);\n  \n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n  \n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  \n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  \n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const lastRot = vehicleLastRotations.get(vehicleId);\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  \n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\n// 在文件顶部添加\nconst mixersToCleanup = new Set();\nconst bonesMap = new Map();\n\n// 在文件顶部添加资源管理器\nconst resourceManager = {\n  mixers: new Set(),\n  bones: new Map(),\n  actions: new Map(),\n  models: new Set(),\n  \n  addMixer(mixer, model) {\n    this.mixers.add(mixer);\n    if (model) {\n      this.models.add(model);\n      // 记录骨骼\n      model.traverse(object => {\n        if (object.isBone) {\n          this.bones.set(object.uuid, object);\n        }\n      });\n    }\n    return mixer;\n  },\n  \n  addAction(action, mixer) {\n    if (!this.actions.has(mixer)) {\n      this.actions.set(mixer, new Set());\n    }\n    this.actions.get(mixer).add(action);\n    return action;\n  },\n  \n  removeMixer(mixer) {\n    if (this.mixers.has(mixer)) {\n      try {\n        // 停止并清理所有动作\n        if (this.actions.has(mixer)) {\n          this.actions.get(mixer).forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          this.actions.delete(mixer);\n        }\n        \n        // 停止混合器\n        if (typeof mixer.stopAllAction === 'function') {\n          mixer.stopAllAction();\n        }\n        \n        // 清理混合器的根对象\n        const root = mixer.getRoot();\n        if (root) {\n          this.models.delete(root);\n          root.traverse(object => {\n            if (object && object.isBone) {\n              this.bones.delete(object.uuid);\n            }\n            if (object && object.animations) {\n              object.animations.length = 0;\n            }\n          });\n          \n          // 安全地清理缓存\n          try {\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            \n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n            \n            // 这里是发生错误的地方，添加防御性检查\n            if (typeof mixer.uncacheClip === 'function') {\n              // 不直接调用uncacheClip(null)，而是获取混合器中的所有动画片段并逐个清理\n              const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n              clips.forEach(clip => {\n                if (clip && clip.uuid) {\n                  mixer.uncacheClip(clip);\n                }\n              });\n            }\n          } catch (e) {\n            console.log('在清理动画混合器时发生非致命错误:', e);\n            // 继续执行其余清理操作\n          }\n        }\n        \n        this.mixers.delete(mixer);\n      } catch (error) {\n        console.error('清理动画混合器时发生错误:', error);\n        // 确保即使出错也会从集合中移除\n        this.mixers.delete(mixer);\n      }\n    }\n  },\n  \n  cleanup() {\n    try {\n      // 清理动作\n      this.actions.forEach((actions, mixer) => {\n        try {\n          actions.forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          actions.clear();\n        } catch (e) {\n          console.log('清理动作时发生非致命错误:', e);\n        }\n      });\n      this.actions.clear();\n      \n      // 清理混合器\n      this.mixers.forEach(mixer => {\n        try {\n          if (typeof mixer.stopAllAction === 'function') {\n            mixer.stopAllAction();\n          }\n          \n          const root = mixer.getRoot();\n          if (root) {\n            root.traverse(object => {\n              if (object && object.animations) {\n                object.animations.length = 0;\n              }\n            });\n            \n            // 安全清理\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            \n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n            \n            // 安全清理动画片段\n            try {\n              if (mixer._actions && Array.isArray(mixer._actions)) {\n                const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n                clips.forEach(clip => {\n                  if (clip && clip.uuid && typeof mixer.uncacheClip === 'function') {\n                    mixer.uncacheClip(clip);\n                  }\n                });\n              }\n            } catch (e) {\n              console.log('清理动画片段时发生非致命错误:', e);\n            }\n          }\n        } catch (e) {\n          console.log('清理混合器时发生非致命错误:', e);\n        }\n      });\n      this.mixers.clear();\n      \n      // 清理骨骼\n      this.bones.forEach(bone => {\n        if (bone.parent) {\n          bone.parent.remove(bone);\n        }\n        if (bone.matrix) bone.matrix.identity();\n        if (bone.matrixWorld) bone.matrixWorld.identity();\n      });\n      this.bones.clear();\n      \n      // 清理模型\n      this.models.forEach(model => {\n        if (model.parent) {\n          model.parent.remove(model);\n        }\n        model.traverse(object => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n          }\n          if (object.animations) {\n            object.animations.length = 0;\n          }\n        });\n      });\n      this.models.clear();\n    } catch (error) {\n      console.error('全局清理过程中发生错误:', error);\n      // 即使发生错误也尝试清空集合\n      this.actions.clear();\n      this.mixers.clear();\n      this.bones.clear();\n      this.models.clear();\n    }\n  }\n};\n\n// 修改创建动画混合器的函数\nconst createAnimationMixer = (model) => {\n  const mixer = new THREE.AnimationMixer(model);\n  return resourceManager.addMixer(mixer, model);\n};\n\n// 修改动画动作创建的函数\nconst createAction = (clip, mixer, model) => {\n  const action = mixer.clipAction(clip, model);\n  return resourceManager.addAction(action, mixer);\n};\n\n// 在CampusModel组件顶部添加消息缓存\nconst processedMessageIds = new Set();\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n  \n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n  \n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = new Map(); // 使用全局Map存储所有行人的动画混合器（不再使用useRef）\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n  \n  \n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n  \n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n  \n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n  \n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n  \n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '65px',  // 从 60px 改为 65px\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '65px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 1. 在CampusModel组件内添加设备弹框状态\n  const [devicePopover, setDevicePopover] = useState({\n    visible: false,\n    position: { x: 0, y: 0 },\n    device: null\n  });\n\n  // 2. 设备弹框关闭函数\n  const handleCloseDevicePopover = () => {\n    setDevicePopover({ visible: false, position: { x: 0, y: 0 }, device: null });\n  };\n\n  // 3. 设备弹框内容渲染函数\n  const renderDevicePopoverContent = (device) => {\n    if (!device) return null;\n    // 判断是否为摄像头\n    if (device.type === 'camera') {\n      // 摄像头显示视频流\n      return (\n        <div style={{ width: 320, height: 180, background: '#000', borderRadius: 6, overflow: 'hidden', position: 'relative' }}>\n          <React.Suspense fallback={<div style={{color:'#fff',textAlign:'center',paddingTop:60}}>视频加载中...</div>}>\n            <VideoPlayer deviceId={device.id} rtspUrl={device.rtspUrl} />\n          </React.Suspense>\n          <div style={{ padding: '8px', background: 'rgba(0,0,0,0.7)', color: '#fff', fontSize: 13, borderRadius: 6, marginTop: 4 }}>\n            <div><b>名称：</b>{device.name}</div>\n            <div><b>类型：</b>{device.type}</div>\n            <div><b>状态：</b>{device.status}</div>\n            <div><b>位置：</b>{device.location}</div>\n            <div><b>IP：</b>{device.ipAddress || '-'}</div>\n            <div><b>描述：</b>{device.description || '-'}</div>\n          </div>\n        </div>\n      );\n    } else {\n      // 其他设备显示图片\n      const imgPath = `${BASE_URL}/images/${device.type}.png`;\n      return (\n        <div style={{ width: 220 }}>\n          <div style={{ textAlign: 'center', marginBottom: 8 }}>\n            <img src={imgPath} alt={device.type} style={{ width: 80, height: 80, objectFit: 'contain', background: '#fff', borderRadius: 8, border: '1px solid #eee' }} />\n          </div>\n          <div style={{ padding: '8px', background: 'rgba(0,0,0,0.7)', color: '#fff', fontSize: 13, borderRadius: 6 }}>\n            <div><b>名称：</b>{device.name}</div>\n            <div><b>类型：</b>{device.type}</div>\n            <div><b>状态：</b>{device.status}</div>\n            <div><b>位置：</b>{device.location}</div>\n            <div><b>IP：</b>{device.ipAddress || '-'}</div>\n            <div><b>描述：</b>{device.description || '-'}</div>\n          </div>\n        </div>\n      );\n    }\n  };\n\n  // 4. 引入VideoPlayer组件\n  // import VideoPlayer from './VideoPlayer';\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n      \n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n      \n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n        \n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos)\n          .to({ x: 0, y: 300, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.position.copy(currentPos);\n          })\n          .start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp)\n          .to({ x: 0, y: 1, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.up.copy(currentUp);\n          })\n          .start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n        \n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget)\n          .to({ x: 0, y: 0, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            controls.target.copy(currentTarget);\n            // 确保相机始终朝向目标点\n            cameraRef.current.lookAt(controls.target);\n            controls.update();\n          })\n          .start();\n\n        // 启用控制器\n        controls.enabled = true;\n        \n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x+50, 70, -modelCoords.y+50);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n      \n      // 如果该路口有红绿灯，自动显示红绿灯弹窗\n      if (intersection.hasTrafficLight !== false && intersection.interId) {\n        console.log(`路口 ${intersection.name} 有红绿灯，准备显示红绿灯状态`);\n        \n        // 延迟300ms调用以确保场景已更新\n        setTimeout(() => {\n          // 检查多种可能的ID格式\n          let interId = intersection.interId;\n          \n          // 使用全局的showTrafficLightPopup函数显示红绿灯弹窗\n          if (window.showTrafficLightPopup) {\n            window.showTrafficLightPopup(interId);\n            console.log(`已触发路口 ${intersection.name} (ID: ${interId}) 的红绿灯弹窗显示`);\n          } else {\n            console.error('找不到显示红绿灯弹窗的函数');\n          }\n        }, 300);\n      } else {\n        console.log(`路口 ${intersection.name} 没有红绿灯或缺少ID，不显示红绿灯状态`);\n        \n        // 如果有弹窗正在显示，则关闭它\n        if (window._setTrafficLightPopover) {\n          window._setTrafficLightPopover({\n            visible: false\n          });\n        }\n      }\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        // console.log('收到RSM消息:', payload);\n        \n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n        \n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n        \n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n          return;\n        }\n        \n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        \n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n        \n        const participants = payload.data?.participants || [];\n        const rsuid = payload.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id =  deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n          \n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            \n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n            \n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1': // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2': // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3': // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return; // 跳过未知类型\n            }\n            \n            // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = type === '3' ? SkeletonUtils.clone(preloadedPeopleModel) : preloadedModel.clone();\n              // 根据类型调整高度和缩放\n              const height = type === '3' ? 2.0 : 1.0;\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              \n              // 如果是行人类型，设置缩放和创建动画\n              if (type === '3') {\n              // newModel.scale.set(0.005, 0.005, 0.005);\n                newModel.scale.set(4, 4, 4);\n                \n                // 使用resourceManager创建并管理动画混合器\n                const mixer = createAnimationMixer(newModel);\n                \n                if (peopleBaseModel && peopleBaseModel.animations && peopleBaseModel.animations.length > 0) {\n                  // 只创建一个动作并添加到资源管理器\n                  const action = createAction(peopleBaseModel.animations[0], mixer, newModel);\n                  action.play();\n                }\n                \n                // 保存到全局Map中(不再使用useRef)\n                peopleAnimationMixers.set(id, mixer);\n              }\n              \n              scene.add(newModel);\n              \n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n              type: type\n              });\n          } else if (model) {\n              // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n        \n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 1000;\n        const currentIds = new Set(participants.map(p => deviceMac + p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            // 如果是行人，清理动画混合器\n            if (modelData.type === '3' && peopleAnimationMixers.has(id)) {\n              const mixer = peopleAnimationMixers.get(id);\n              // 使用resourceManager清理混合器\n              resourceManager.removeMixer(mixer);\n              peopleAnimationMixers.delete(id);\n            }\n            \n            // 从场景移除模型\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n        \n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n        \n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n        \n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid, // 直接传递车辆ID\n          data: {       // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n        \n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n        \n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n        \n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n        \n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        \n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          \n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n          \n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n              \n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n              \n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n          \n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n          \n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          \n          scene.add(newVehicleModel);\n          \n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n          \n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position)\n            .to({ y: 0.5 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .start();\n          \n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({ opacity: 0.6 })\n                .to({ opacity: 1.0 }, 500)\n                .easing(TWEEN.Easing.Quadratic.Out)\n                .onUpdate(function() {\n                  child.material.opacity = this.opacity;\n                  child.material.needsUpdate = true;\n                })\n                .start();\n            }\n          });\n          \n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({ opacity: 0.6 })\n            .to({ opacity: 1.0 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .onUpdate(function() {\n              speedLabel.material.opacity = this.opacity;\n              speedLabel.material.needsUpdate = true;\n            })\n            .start();\n          \n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n          \n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n          \n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n          \n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n          \n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n        \n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 增加到10秒，避免频繁清理造成闪烁\n        \n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n          \n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1 ;\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n                \n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n            \n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n            \n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            \n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        \n        return;\n      }\n      \n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n        \n        try {\n          const payload = JSON.parse(message);\n          \n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n          \n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n          \n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n          \n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n          \n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              \n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              \n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n              \n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                \n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  \n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? \n                    getDirectionFromCode(phase.trafficDirec) : \n                    getPhaseDirection(phaseId);\n                  \n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  \n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n                  \n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n                  \n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n                  \n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  \n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  \n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n                    \n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n                \n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                \n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n                  \n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && \n                     (window.currentPopoverIdRef.current === modelKey || \n                      window.currentPopoverIdRef.current === String(modelKey) || \n                      window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    \n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n                    \n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n        return;\n      }\n      \n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件，确保包含mac和timestamp\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data,\n          mac: payload.mac,\n          tm: payload.tm\n        }, '*');\n        \n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n        \n        return;\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n        \n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 检查消息ID是否已处理过\n          if (message.messageId) {\n            if (processedMessageIds.has(message.messageId)) {\n              // console.log('跳过重复消息:', message.messageId);\n              return;\n            }\n            \n            // 添加到已处理集合\n            processedMessageIds.add(message.messageId);\n            \n            // 限制缓存大小，防止内存泄漏\n            if (processedMessageIds.size > 1000) {\n              // 转换为数组，删除最早的100个元素\n              const idsArray = Array.from(processedMessageIds);\n              for (let i = 0; i < 100; i++) {\n                processedMessageIds.delete(idsArray[i]);\n              }\n            }\n          }\n          \n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/vehicle.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n        \n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          \n          // 检查scene是否初始化\n          if (scene) {\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画 - 只更新存在的混合器\n      const deltaTime = clock.getDelta();\n      \n      // 使用Map而不是ref.current\n      if (peopleAnimationMixers.size > 0) {\n        peopleAnimationMixers.forEach((mixer) => {\n          mixer.update(deltaTime);\n        });\n      }\n\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          200,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n        \n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        \n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n        \n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n        \n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 停止渲染循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 停止所有 TWEEN 动画\n      TWEEN.removeAll();\n\n      // 3. 清理所有动画混合器\n      peopleAnimationMixers.forEach(mixer => {\n        resourceManager.removeMixer(mixer);\n      });\n      peopleAnimationMixers.clear();\n\n      // 4. 清理所有其他资源\n      resourceManager.cleanup();\n\n      // 5. 清理场景中的所有对象\n      if (scene) {\n        const objectsToRemove = [];\n        scene.traverse((object) => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n            if (object !== scene) {\n              objectsToRemove.push(object);\n            }\n          }\n        });\n        \n        // 从场景中移除对象\n        objectsToRemove.forEach(obj => {\n          if (obj.parent) {\n            obj.parent.remove(obj);\n          }\n        });\n        \n        scene.clear();\n      }\n\n      // 6. 清理渲染器\n      if (renderer) {\n        renderer.setAnimationLoop(null);\n        if (containerRef.current && renderer.domElement && renderer.domElement.parentNode === containerRef.current) {\n          containerRef.current.removeChild(renderer.domElement);\n        }\n        renderer.dispose();\n        renderer.forceContextLoss();\n      }\n\n      // 7. 清理其他资源\n      if (controls) {\n        controls.dispose();\n      }\n\n      // 8. 清理数据结构\n      vehicleLastPositions.clear();\n      vehicleLastRotations.clear();\n      deviceTimestamps.clear();\n      vehicleModels.clear();\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n    \n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n    \n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n    \n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n    \n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 2000);\n      \n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n  \n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current);\n        }\n      };\n      \n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n      \n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n      \n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n    \n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    \n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n    \n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,//\n          transparent: false,\n          opacity: 0.1,  // 几乎透明\n          depthWrite: false\n        });\n        \n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置\n        \n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n        \n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        \n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500);  // 延迟略长于debugTrafficLights\n    \n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n    \n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n  \n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        // 查找第一个带有红绿灯的路口\n        const firstTrafficLightIntersection = intersectionsData.intersections.find(\n          intersection => intersection.hasTrafficLight !== false && intersection.interId\n        );\n        \n        // 如果找到带红绿灯的路口，优先选择它；否则选择第一个路口\n        const targetIntersection = firstTrafficLightIntersection || intersectionsData.intersections[0];\n        \n        console.log('自动选择路口:', targetIntersection.name, \n                    '具有红绿灯:', firstTrafficLightIntersection ? '是' : '否');\n        \n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(targetIntersection.name);\n        }, 2000);\n        \n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n\n  // 新增：在场景初始化后渲染所有路口entrance的设备图标\n  const renderEntranceDeviceIcons = (converterInstance) => {\n      if (!scene || typeof scene.traverse !== 'function' || !converterInstance) return;\n      try {\n        // 先移除所有旧的设备图标组（用 children.filter 更安全）\n        scene.children\n          .filter(obj => obj.userData && obj.userData.isEntranceDeviceIcons)\n          .forEach(obj => scene.remove(obj));\n        // 遍历所有路口\n        intersectionsData.intersections.forEach(intersection => {\n          if (!intersection.entrances || !Array.isArray(intersection.entrances)) return;\n          // 遍历每个路口的所有 entrance\n          intersection.entrances.forEach((entrance) => {\n            // 检查经纬度有效性\n            if (!entrance.longitude || !entrance.latitude || isNaN(parseFloat(entrance.longitude)) || isNaN(parseFloat(entrance.latitude))) {\n              return; // 跳过无效经纬度的入口\n            }\n            \n            // 查找该路口该entrance下所有设备\n            const devices = devicesData.devices.filter(\n              d => d.location === intersection.name && d.entrance === entrance.name\n            );\n            if (devices.length === 0) return;\n            // 经纬度转模型坐标\n            console.log('渲染路口', intersection.name, '入口', entrance.name, '设备数量', devices.length);\n            const modelPos = converterInstance.wgs84ToModel(\n              parseFloat(entrance.longitude),\n              parseFloat(entrance.latitude)\n            );\n            // 创建一个组用于存放所有图标\n            const group = new THREE.Group();\n            group.position.set(modelPos.x, 15, -modelPos.y); // 上方15米\n            group.userData = { isEntranceDeviceIcons: true };\n            // 图标排成一排，居中\n            const iconSize = 32; // px\n            const iconSpacing = 8; // px\n            const totalWidth = devices.length * iconSize + (devices.length - 1) * iconSpacing;\n            // 以三维单位为准，假设1单位=1米，24px约等于0.6米\n            const size3D = 4.0; // 图标尺寸加大\n            const spacing3D = 0.5; // 图标间距加大\n            const startX = -((devices.length - 1) * (size3D + spacing3D)) / 2;\n            devices.forEach((device, idx) => {\n              // 创建一个平面用于显示SVG图标\n              const textureLoader = new THREE.TextureLoader();\n              const iconPath = `${BASE_URL}/images/${device.type}.svg`;\n              // 图标材质\n              const iconMaterial = new THREE.MeshBasicMaterial({\n                map: textureLoader.load(iconPath),\n                transparent: true,\n                opacity: 1 // 图标完全不透明\n              });\n              // 图标背景板材质\n              const bgMaterial = new THREE.MeshBasicMaterial({\n                color: 0x000000,\n                transparent: true,\n                opacity: 0.7 // 半透明黑色\n              });\n              // 背景板尺寸略大于图标\n              const bgWidth = size3D * 1.25;\n              const bgHeight = size3D * 1.25;\n              // 背景板几何体（圆角矩形）\n              // 由于Three.js没有内置圆角矩形，使用PlaneGeometry近似\n              const bgGeometry = new THREE.PlaneGeometry(bgWidth, bgHeight);\n              const bgMesh = new THREE.Mesh(bgGeometry, bgMaterial);\n              // 图标几何体\n              const iconGeometry = new THREE.PlaneGeometry(size3D, size3D);\n              const iconMesh = new THREE.Mesh(iconGeometry, iconMaterial);\n              // 图标略微前移，避免Z轴重叠闪烁\n              iconMesh.position.set(0, 0, 0.01);\n              // 创建一个组，包含背景和图标\n              const iconGroup = new THREE.Group();\n              iconGroup.add(bgMesh);\n              iconGroup.add(iconMesh);\n              // 整体平移到正确位置\n              iconGroup.position.set(startX + idx * (size3D + spacing3D), 0, 0);\n              // 不再固定旋转角度，后续在动画循环中让其始终面向相机\n              iconGroup.renderOrder = 999; // 提高渲染优先级\n              iconGroup.userData = {\n                deviceId: device.id,\n                deviceType: device.type,\n                entrance: entrance.name,\n                isEntranceDeviceIcon: true\n              };\n              group.add(iconGroup);\n            });\n            scene.add(group);\n          });\n        });\n      } catch (e) {\n        console.warn('scene.traverse error', e);\n        return;\n      }\n    };\n\n  // 在场景初始化后调用渲染设备图标\n  useEffect(() => {\n    if (scene && typeof scene.traverse === 'function' && converter.current) {\n      renderEntranceDeviceIcons(converter.current);\n    }\n  }, [scene, converter.current]);\n\n  // 新增：每帧让所有设备图标组始终面向相机（billboard效果）\n  useEffect(() => {\n    if (!scene || !cameraRef.current) return;\n    const animateBillboard = () => {\n      // 遍历所有设备图标组，让其正对相机\n      scene.children.forEach(obj => {\n        if (obj.userData && obj.userData.isEntranceDeviceIcons) {\n          obj.lookAt(cameraRef.current.position.x, obj.position.y, cameraRef.current.position.z);\n        }\n      });\n      requestAnimationFrame(animateBillboard);\n    };\n    animateBillboard();\n  }, [scene]);\n\n  return (\n    <>\n      <span style={labelStyle}>区域选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择区域位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n        value={selectedIntersection ? selectedIntersection.name : undefined}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      \n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '4px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n            padding: '0',\n            maxWidth: '240px', // 缩小最大宽度\n            fontSize: '12px' // 缩小字体\n          }}\n        >\n          {trafficLightPopover.content}\n          <button \n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '12px',\n              cursor: 'pointer',\n              padding: '2px 6px'\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n      \n      {/* 设备信息弹框 */}\n      {devicePopover.visible && devicePopover.device && (\n        <div\n          style={{\n            position: 'absolute',\n            left: `${devicePopover.position.x}px`,\n            top: `${devicePopover.position.y}px`,\n            transform: 'translate(-10px, 10px)',\n            zIndex: 2000,\n            background: 'rgba(0,0,0,0.92)',\n            color: '#fff',\n            borderRadius: '8px',\n            boxShadow: '0 2px 12px rgba(0,0,0,0.25)',\n            padding: '0',\n            minWidth: '220px',\n            maxWidth: '340px',\n            fontSize: '13px',\n            pointerEvents: 'auto'\n          }}\n        >\n          {renderDevicePopoverContent(devicePopover.device)}\n          <button\n            style={{\n              position: 'absolute',\n              top: '2px',\n              right: '6px',\n              background: 'none',\n              border: 'none',\n              color: '#fff',\n              fontSize: '16px',\n              cursor: 'pointer',\n              zIndex: 10\n            }}\n            onClick={handleCloseDevicePopover}\n          >×</button>\n        </div>\n      )}\n      \n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 12, // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    backgroundColor: parameters.backgroundColor || { r: 255, g: 255, b: 255, a: 0.8 },\n    textColor: parameters.textColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  \n  // 设置字体\n  // context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  \n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n  \n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  \n  canvas.width = width;\n  canvas.height = height;\n  \n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n  \n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n  \n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n  \n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n  \n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n  \n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n  \n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n  \n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = { \n    text: text,\n    params: params\n  };\n  \n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    try {\n      const [ trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([\n        loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`)\n        \n    ]);\n\n\n\n    // 处理机动车模型\n    console.log('加载车辆模型...');\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n          color: 0xff0000,  // 0xff0000, //红色 0xffffff,白色\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n      }\n    });\n\n    console.log('加载非机动车模型...');\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    console.log('加载行人模型...');\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n        \n      }\n      if (child.isMesh){\n        child.castShadow = true;\n      }\n    });\n\n\n\n    // 保存行人动画数据\n    console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n    if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n      console.log('找到行人动画:', peopleGltf.animations.length, '个');\n      peopleBaseModel = peopleGltf;\n    } else {\n      console.warn('行人模型没有包含动画数据');\n    }\n\n    console.log('加载红绿灯模型...');\n      \n    // 处理红绿灯模型\n    preloadedTrafficLightModel = trafficLightGltf.scene;\n    console.log('红绿灯模型：', preloadedTrafficLightModel);\n    // 设置红绿灯模型的缩放\n    preloadedTrafficLightModel.scale.set(6, 6, 6);\n    // 保持原始材质\n    preloadedTrafficLightModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 设置材质属性\n        child.material.metalness = 0.2;\n        child.material.roughness = 0.3;\n        child.material.envMapIntensity = 1.2;\n    }\n  });\n\n    console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n      \n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n        \n        // // 尝试单独加载红绿灯模型\n        // if (!preloadedTrafficLightModel) {\n        //   console.log('正在单独加载红绿灯模型...');\n        //   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n        //   preloadedTrafficLightModel = trafficLightGltf.scene;\n        //   preloadedTrafficLightModel.scale.set(3, 3, 3);\n        //   console.log('红绿灯模型加载成功');\n        // }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  \n  try {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在1秒后自动移除\n  setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n    scene.remove(sprite);\n      }\n  }, 100);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  // console.log('添加场景事件标记:', {\n  //   位置: position,\n  //   文本: text,\n  //   颜色: color\n  // });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  \n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  \n  // 检查红绿灯模型是否已加载\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载，尝试重新加载...');\n    // 尝试重新加载红绿灯模型\n    const loader = new GLTFLoader();\n    loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)\n      .then(trafficLightGltf => {\n        preloadedTrafficLightModel = trafficLightGltf.scene;\n        preloadedTrafficLightModel.scale.set(6, 6, 6);\n        preloadedTrafficLightModel.traverse((child) => {\n          if (child.isMesh && child.material) {\n            child.material.metalness = 0.2;\n            child.material.roughness = 0.3;\n            child.material.envMapIntensity = 1.2;\n          }\n        });\n        console.log('红绿灯模型重新加载成功，开始创建红绿灯...');\n        // 重新调用创建函数\n        createTrafficLights(converterInstance);\n      })\n      .catch(error => {\n        console.error('红绿灯模型重新加载失败:', error);\n        // 如果加载失败，使用简单的替代物体\n        createFallbackTrafficLights(converterInstance);\n      });\n    return;\n  }\n  \n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      \n      try {\n        // 确保模型存在且可以克隆\n        if (!preloadedTrafficLightModel || !preloadedTrafficLightModel.clone) {\n          throw new Error('红绿灯模型无效或无法克隆');\n        }\n        \n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n        \n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n        \n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n        \n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n        \n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n        \n        // 设置材质属性\n        trafficLightModel.traverse(child => {\n          if (child.isMesh) {\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            child.renderOrder = 100;\n          }\n        });\n        \n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        \n        // 将红绿灯添加到场景中\n        // scene.add(trafficLightModel);\n\n        // ========== 新增：在红绿灯地面添加指北针图标 =============\n        // 创建一个平面用于显示指北针SVG图标\n        const compassTextureLoader = new THREE.TextureLoader();\n        const compassIconPath = `${BASE_URL}/images/compass.svg`; // public目录下的路径 \n        const compassMaterial = new THREE.MeshBasicMaterial({\n          map: compassTextureLoader.load(compassIconPath),\n          transparent: true,\n          opacity: 1\n        });\n        // 平面几何体，5x5米\n        const compassGeometry = new THREE.PlaneGeometry(5, 5);\n        const compassMesh = new THREE.Mesh(compassGeometry, compassMaterial);\n        // 指北针放在红绿灯正下方地面（y=0.1，略高于地面防止Z冲突）\n        compassMesh.position.set(modelPos.x+20, 1.5, -(modelPos.y+20));\n        // 指北针朝上，旋转x轴-90度\n        compassMesh.rotation.x = -Math.PI / 2;\n        // 渲染优先级高，避免被地面遮挡\n        compassMesh.renderOrder = 101;\n        // 可选：添加userData标记\n        compassMesh.userData = {\n          type: 'compassIcon',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        // 添加到场景\n        scene.add(compassMesh);\n        // ========== 指北针图标添加结束 =============\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        \n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        // createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n  \n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance) => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({ \n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n  \n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n  \n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n  \n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n  \n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,  // 完全透明\n    depthWrite: false\n  });\n  \n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  \n  trafficLightModel.add(collider);\n  \n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n  \n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n  \n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n  \n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  trafficLightModel.add(lightMesh);\n  \n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  switch(phaseId) {\n    case '1': return '北进口左转';\n    case '2': return '北进口直行';\n    case '3': return '北进口右转';\n    case '5': return '东进口左转';\n    case '6': return '东进口直行';\n    case '7': return '东进口右转';\n    case '9': return '南进口左转';\n    case '10': return '南进口直行';\n    case '11': return '南进口右转';\n    case '13': return '西进口左转';\n    case '14': return '西进口直行';\n    case '15': return '西进口右转';\n    default: return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = (dirCode) => {\n  switch(dirCode) {\n    case 'N': return '北向南';\n    case 'S': return '南向北';\n    case 'E': return '东向西';\n    case 'W': return '西向东';\n    case 'NE': return '东北向西南';\n    case 'NW': return '西北向东南';\n    case 'SE': return '东南向西北';\n    case 'SW': return '西南向东北';\n    default: return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  \n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n  \n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n  \n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Line.threshold = 1;\n  \n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  \n  console.log('鼠标点击位置:', mouseX, mouseY);\n  \n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  \n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n  \n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  \n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, \n                 intersect.object.name || '无名称', \n                 '距离:', intersect.distance,\n                 'userData:', intersect.object.userData);\n    });\n    \n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    \n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n      \n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n      \n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n  \n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  \n  console.log('射线检测到的对象数量:', intersects.length);\n  \n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', \n                  'userData:', obj.userData, \n                  '距离:', intersect.distance);\n    });\n    \n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n  \n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n  \n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.1; // 设置一个阈值，只有距离小于此值的才会被选中\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n      \n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n      \n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      \n      console.log(`路口 ${interId} 的距离:`, distance);\n      \n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = { interId, distance };\n      }\n    }\n  });\n  \n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n    \n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  \n  console.log('未检测到任何红绿灯点击');\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n  \n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n  \n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({ \n    ...prev, \n    visible: false,\n    content: null, // 清空内容\n    phases: []     // 清空相位信息\n  }));\n  \n  console.log('弹窗已关闭，所有相关资源已清理');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = (interId) => {\n  try {\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      \n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      \n      return false;\n    }\n    \n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n    \n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 创建弹出窗口内容\n    let content;\n    \n    if (stateInfo && stateInfo.phases) {\n      content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection.name} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              switch (phase.trafficLight) {\n                case 'G': lightColor = '#00ff00'; break;\n                case 'Y': lightColor = '#ffff00'; break;\n                case 'R': default: lightColor = '#ff0000'; break;\n              }\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {getPhaseDirection(phase.phaseId)}\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      content = (\n        <div style={{ padding: '8px', maxWidth: '200px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>\n          <div>路口ID: {interId}</div>\n          <div>当前无信号灯状态信息</div>\n        </div>\n      );\n    }\n    \n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2 -500;\n    const centerY = window.innerHeight / 2 -500;\n    \n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;\n    \n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: { x: centerX, y: centerY },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      \n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      \n      document.body.appendChild(popover);\n      \n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      \n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  \n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  \n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  \n  return list;\n};\n\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = (interId) => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    \n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    \n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      \n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    \n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    \n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 判断是否有相位数据\n    const hasPhaseData = stateInfo && stateInfo.phases && stateInfo.phases.length > 0;\n    \n    let content;\n    \n    // 指北针样式\n    const compassStyle = {\n      position: 'absolute',\n      top: '5px',\n      right: '25px',\n      width: '30px',\n      height: '30px',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      borderRadius: '50%',\n      background: 'rgba(0,0,0,0.1)',\n      zIndex: 10\n    };\n    \n    // 指北针组件\n    const CompassIcon = () => (\n      <div style={compassStyle}>\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          transform: 'rotate(0deg)'\n        }}>\n          <span style={{\n            color: '#ff5252',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            lineHeight: '14px'\n          }}>N</span>\n          <span style={{\n            width: 0,\n            height: 0,\n            borderLeft: '6px solid transparent',\n            borderRight: '6px solid transparent',\n            borderBottom: '10px solid #ff5252',\n            marginTop: '-2px'\n          }}></span>\n        </div>\n      </div>\n    );\n    \n    if (hasPhaseData) {\n      // 相位ID与方向/方式的映射\n      const phaseMap = {\n        '1': { dir: 'N', type: 'left' }, '2': { dir: 'N', type: 'straight' }, '3': { dir: 'N', type: 'right' },\n        '5': { dir: 'E', type: 'left' }, '6': { dir: 'E', type: 'straight' }, '7': { dir: 'E', type: 'right' },\n        '9': { dir: 'S', type: 'left' }, '10': { dir: 'S', type: 'straight' }, '11': { dir: 'S', type: 'right' },\n        '13': { dir: 'W', type: 'left' }, '14': { dir: 'W', type: 'straight' }, '15': { dir: 'W', type: 'right' }\n      };\n      \n      const typeOrder = ['left', 'straight', 'right'];\n      const colorMap = { G: '#00ff00', Y: '#ffff00', R: '#ff0000' };\n      const dirData = { N: {}, E: {}, S: {}, W: {} };\n      \n      stateInfo.phases.forEach(phase => {\n        const map = phaseMap[phase.phaseId];\n        if (map) {\n          dirData[map.dir][map.type] = {\n            color: colorMap[phase.trafficLight] || '#888',\n            remainTime: phase.remainTime\n          };\n        }\n      });\n      \n      content = (\n        <div style={{ padding: '8px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>\n          <CompassIcon />\n          <div style={{ fontWeight: 'bold', marginBottom: '8px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}灯态</div>\n          <div style={{\n            display: 'grid',\n            gridTemplateRows: '60px 60px 60px',\n            gridTemplateColumns: '60px 60px 60px',\n            justifyContent: 'center',\n            alignItems: 'center',\n            background: 'rgba(255,255,255,0.05)',\n            borderRadius: '8px',\n            margin: '0 auto',\n            position: 'relative'\n          }}>\n            {/* 北 - 左转、直行、右转箭头水平对齐，倒计时在箭头上面 */}\n            {/* <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%', paddingLeft: '5px' }}> */}\n            <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}> \n              {typeOrder.map((type, index) => {\n                // 反转左转和右转在数组中的顺序\n                let displayIndex = index;\n                if (index === 0) displayIndex = 2;\n                else if (index === 2) displayIndex = 0;\n                \n                const currentType = typeOrder[displayIndex];\n                \n                // 计算与南边对齐的样式\n                const marginStyle = {};\n                if (currentType === 'left') { // 左转箭头 (右侧显示)\n                  marginStyle.marginRight = '0px';\n                } else if (currentType === 'straight') { // 直行箭头 (中间显示)\n                  marginStyle.marginLeft = '10px';\n                  marginStyle.marginRight = '10px';\n                } else if (currentType === 'right') { // 右转箭头 (左侧显示)\n                  marginStyle.marginLeft = '0px';\n                }\n                \n                return dirData.N[currentType] && (\n                  // <div key={currentType} style={{\n                  //   display: 'flex', \n                  //   flexDirection: 'column', \n                  //   alignItems: 'center',\n                  //   ...marginStyle\n                  // }}>\n                  <div key={currentType} style={{marginRight: currentType === 'left' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>\n                    <div style={{fontSize:'14px', color: dirData.N[currentType].color, fontWeight:'bold', marginBottom: '3px'}}>{dirData.N[currentType].remainTime}</div>\n                    <span style={{color: dirData.N[currentType].color, fontSize:'20px', lineHeight: '20px'}}>\n                      {currentType === 'left' ? '\\u{1F87A}' : currentType === 'straight' ? '\\u{1F87B}' : '\\u{1F878}'}\n                    </span>\n                  </div>\n                );\n              })}\n            </div>\n            \n            {/* 南 - 左转、直行、右转箭头水平对齐，倒计时在箭头下面 */}\n            <div style={{ gridRow: 3, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}>\n              {typeOrder.map(type => dirData.S[type] && (\n                <div key={type} style={{marginRight: type === 'right' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>\n                  <span style={{color: dirData.S[type].color, fontSize:'20px', lineHeight: '20px'}}>\n                    {type === 'left' ? '\\u{1F878}' : type === 'straight' ? '\\u{1F879}' : '\\u{1F87A}'}\n                  </span>\n                  <div style={{fontSize:'14px', color: dirData.S[type].color, fontWeight:'bold', marginTop: '3px'}}>{dirData.S[type].remainTime}</div>\n                </div>\n              ))} \n            </div>\n            \n            {/* 东 - 倒计时显示在箭头右边 */}\n\n            <div style={{ gridRow: 2, gridColumn: 3, textAlign: 'center' }}>\n              {typeOrder.map((type, index) => {\n                // 反转左转和右转在数组中的顺序\n                let displayIndex = index;\n                if (index === 0) displayIndex = 2;\n                else if (index === 2) displayIndex = 0;\n                \n                const currentType = typeOrder[displayIndex];\n                \n                return dirData.E[currentType] && (\n                  <div key={currentType} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-start'}}>\n                    <span style={{color: dirData.E[currentType].color, fontSize:'20px', lineHeight: '20px'}}>\n                      {currentType === 'left' ? '\\u{1F87B}' : currentType === 'straight' ? '\\u{1F878}' : '\\u{1F879}'}\n                    </span>\n                    <div style={{\n                      fontSize:'14px', \n                      color: dirData.E[currentType].color, \n                      fontWeight:'bold', \n                      marginLeft: '5px'\n                    }}>{dirData.E[currentType].remainTime}</div>\n                  </div>\n                );\n              })}\n            </div>\n            \n            {/* 西 - 倒计时显示在箭头左边 */}\n            <div style={{ gridRow: 2, gridColumn: 1, textAlign: 'center' }}>\n              {typeOrder.map(type => dirData.W[type] && (\n                <div key={type} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}>\n                  <div style={{\n                    fontSize:'14px', \n                    color: dirData.W[type].color, \n                    fontWeight:'bold', \n                    marginRight: '5px'\n                  }}>{dirData.W[type].remainTime}</div>\n                  <span style={{color: dirData.W[type].color, fontSize:'20px', lineHeight: '20px'}}>\n                    {type === 'left' ? '\\u{1F879}' : type === 'straight' ? '\\u{1F87A}' : '\\u{1F87B}'}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n          <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      // 没有相位数据时显示的内容\n      content = (\n        <div style={{ padding: '15px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>\n          <CompassIcon />\n          <div style={{ fontWeight: 'bold', marginBottom: '10px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}</div>\n          <div style={{ \n            textAlign: 'center', \n            padding: '20px 0',\n            color: '#ff9800', \n            fontSize: '14px', \n            fontWeight: 'bold',\n            background: 'rgba(255,255,255,0.1)',\n            borderRadius: '8px',\n            marginBottom: '10px'\n          }}>\n            当前无信号灯状态信息\n          </div>\n          <div style={{ fontSize: '12px', color: '#888', textAlign: 'center' }}>\n            路口ID: {interId}\n          </div>\n          <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    }\n    \n    // 设置弹窗位置在左上角区域\n    const x = 445;\n    const y = 250;\n    \n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n    \n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n    \n    // 更新弹窗状态\n    if (window._setTrafficLightPopover) {\n      window._setTrafficLightPopover({\n        visible: true,\n        interId: interId,\n        position: { x, y },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      // 设置定时更新\n      if (window.trafficLightUpdateTimerRef) {\n        window.trafficLightUpdateTimerRef.current = setInterval(() => {\n          window.showTrafficLightPopup(interId);\n        }, 1000);\n      }\n      \n      return true;\n    } else {\n      console.error('无法找到setTrafficLightPopover函数');\n      return false;\n    }\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n  \n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n  \n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  \n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n    \n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n    \n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n    \n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n    \n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    \n    console.log('归一化坐标:', mouseX, mouseY);\n    \n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    \n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n    \n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n    \n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    \n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', \n                    '距离:', intersect.distance,\n                    'position:', intersect.object.position.toArray(),\n                    'userData:', intersect.object.userData);\n        \n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      \n      return true;\n    }\n    \n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    \n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', \n                  '类型:', obj.type,\n                  '位置:', obj.position.toArray(),\n                  '距离:', intersect.distance,\n                  'userData:', obj.userData);\n    });\n    \n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    \n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n        \n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n        \n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n        \n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        \n        if (isVisible) {\n          visibleCount++;\n        }\n        \n        console.log(`红绿灯 ${interId}:`, {\n          名称: lightObj.intersection?.name || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    \n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n    \n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  \n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch(phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n  \n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ \n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: trafficLight.intersection?.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n  \n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = { isLight: true };\n  \n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  \n  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\n\nexport default CampusModel; \n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAO,KAAKC,aAAa,MAAM,2CAA2C;AAG1E,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACtC,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,WAAW,MAAM,sBAAsB,CAAC,CAAC;;AAEhD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,0BAA0B,GAAG,IAAI,CAAC,CAAC;AACvC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB,IAAIC,eAAe,GAAE,IAAI,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAE,IAAI;;AAElB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAEpB;AACA,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,MAAMC,oBAAoB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;;AAExC;AACA,MAAME,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACV;EACEC,GAAG,EAAE,2BAA2B;EAChCC,GAAG,EAAE,2BAA2B;EAChChB,KAAK,EAAE,6BAA6B;EACtCiB,GAAG,EAAE,2BAA2B;EAAG;EACnCC,IAAI,EAAE,4BAA4B,CAAE;AACtC,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AACzEC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;;AAE1D;AACA,MAAMG,aAAa,GAAG,IAAIlB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,IAAImB,gBAAgB,GAAG,IAAInB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAElC;AACA,IAAIoB,gBAAgB,GAAG,IAAI;;AAE3B;AACA,IAAIC,gBAAgB,GAAG,IAAIrB,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,IAAIsB,kBAAkB,GAAG,IAAItB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEpC,MAAMuB,KAAK,GAAG,IAAI1D,KAAK,CAAC2D,KAAK,CAAC,CAAC;;AAE/B;AACA,MAAMC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGf,QAAQ,oBAAoB,CAAC;IAC7D,MAAMgB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,IAAIA,IAAI,CAACE,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;MACzD,MAAMG,WAAW,GAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,IAAI,CAAC;MACrE,IAAIH,WAAW,IAAIA,WAAW,CAACI,KAAK,EAAE;QACpCjB,gBAAgB,GAAGa,WAAW,CAACI,KAAK;QACpCrB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEG,gBAAgB,CAAC;QAC7C,OAAOA,gBAAgB;MACzB;IACF;IACAJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChCG,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjClB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB;AACF,CAAC;;AAED;AACA,MAAMmB,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;EAC5C;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAI,CAACjD,YAAY,EAAE;MACjBA,YAAY,GAAGgD,MAAM,CAACE,KAAK,CAAC,CAAC;MAC7B,OAAOF,MAAM;IACf;IAEA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEpD,YAAY,CAACoD,CAAC,EAAElD,KAAK,CAAC;IAChE,MAAMmD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEtD,YAAY,CAACsD,CAAC,EAAEpD,KAAK,CAAC;IAChE,MAAMqD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAExD,YAAY,CAACwD,CAAC,EAAEtD,KAAK,CAAC;IAEhEF,YAAY,CAACyD,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;IACjD,OAAOvD,YAAY,CAACkD,KAAK,CAAC,CAAC;EAC3B;;EAEA;EACA,IAAI,CAAC/C,oBAAoB,CAACuD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC9C,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;EAEA,MAAMW,OAAO,GAAGxD,oBAAoB,CAACyD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,MAAMY,QAAQ,GAAGF,OAAO,CAACG,UAAU,CAACd,MAAM,CAAC;EAC3C,MAAMe,sBAAsB,GAAG,EAAE,CAAC,CAAC;;EAEnC,IAAIF,QAAQ,GAAGE,sBAAsB,EAAE;IACrC3C,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAUY,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAClE7D,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;;EAEA;EACA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEO,OAAO,CAACP,CAAC,EAAElD,KAAK,CAAC;EAC3D,MAAMmD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEK,OAAO,CAACL,CAAC,EAAEpD,KAAK,CAAC;EAC3D,MAAMqD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAEG,OAAO,CAACH,CAAC,EAAEtD,KAAK,CAAC;EAE3D,MAAM+D,WAAW,GAAG,IAAIhG,KAAK,CAACiG,OAAO,CAACf,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACtEpD,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAEgB,WAAW,CAACf,KAAK,CAAC,CAAC,CAAC;EAExD,OAAOe,WAAW;AACpB,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,WAAW,EAAEnB,SAAS,KAAK;EACjD;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAIhD,YAAY,KAAK,IAAI,EAAE;MACzBA,YAAY,GAAGmE,WAAW;MAC1B,OAAOA,WAAW;IACpB;;IAEA;IACA,IAAIC,IAAI,GAAGD,WAAW,GAAGnE,YAAY;IACrC,IAAIoE,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IAExC,MAAMC,gBAAgB,GAAG7B,aAAa,CAAC1C,YAAY,GAAGoE,IAAI,EAAEpE,YAAY,EAAEC,KAAK,CAAC;IAChFD,YAAY,GAAGuE,gBAAgB;IAC7B,OAAOA,gBAAgB;EACzB;;EAEA;EACA,IAAI,CAACnE,oBAAoB,CAACqD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC5C,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMK,OAAO,GAAGpE,oBAAoB,CAACuD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,IAAIoB,IAAI,GAAGD,WAAW,GAAGK,OAAO;EAChC,IAAIJ,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;;EAExC;EACA,MAAMG,mBAAmB,GAAGJ,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,CAAC;EACzC,IAAID,IAAI,CAACK,GAAG,CAACN,IAAI,CAAC,GAAGK,mBAAmB,EAAE;IACxCtD,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAU,CAACoB,IAAI,GAAG,GAAG,GAAGC,IAAI,CAACC,EAAE,EAAEP,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAChF3D,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMI,gBAAgB,GAAG7B,aAAa,CAAC8B,OAAO,GAAGJ,IAAI,EAAEI,OAAO,EAAEvE,KAAK,CAAC;EACtEG,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEuB,gBAAgB,CAAC;EAErD,OAAOA,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA,MAAMI,6BAA6B,GAAG,CAAC,CAAC,CAAC;AACzC;;AAEA;AACA,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;AACjC,MAAMC,QAAQ,GAAG,IAAI3E,GAAG,CAAC,CAAC;;AAE1B;AACA,MAAM4E,eAAe,GAAG;EACtBC,MAAM,EAAE,IAAIH,GAAG,CAAC,CAAC;EACjBI,KAAK,EAAE,IAAI9E,GAAG,CAAC,CAAC;EAChB+E,OAAO,EAAE,IAAI/E,GAAG,CAAC,CAAC;EAClBgF,MAAM,EAAE,IAAIN,GAAG,CAAC,CAAC;EAEjBO,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACrB,IAAI,CAACN,MAAM,CAACO,GAAG,CAACF,KAAK,CAAC;IACtB,IAAIC,KAAK,EAAE;MACT,IAAI,CAACH,MAAM,CAACI,GAAG,CAACD,KAAK,CAAC;MACtB;MACAA,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAI;QACvB,IAAIA,MAAM,CAACC,MAAM,EAAE;UACjB,IAAI,CAACT,KAAK,CAACzB,GAAG,CAACiC,MAAM,CAACE,IAAI,EAAEF,MAAM,CAAC;QACrC;MACF,CAAC,CAAC;IACJ;IACA,OAAOJ,KAAK;EACd,CAAC;EAEDO,SAASA,CAACC,MAAM,EAAER,KAAK,EAAE;IACvB,IAAI,CAAC,IAAI,CAACH,OAAO,CAACzB,GAAG,CAAC4B,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACH,OAAO,CAAC1B,GAAG,CAAC6B,KAAK,EAAE,IAAIR,GAAG,CAAC,CAAC,CAAC;IACpC;IACA,IAAI,CAACK,OAAO,CAACvB,GAAG,CAAC0B,KAAK,CAAC,CAACE,GAAG,CAACM,MAAM,CAAC;IACnC,OAAOA,MAAM;EACf,CAAC;EAEDC,WAAWA,CAACT,KAAK,EAAE;IACjB,IAAI,IAAI,CAACL,MAAM,CAACvB,GAAG,CAAC4B,KAAK,CAAC,EAAE;MAC1B,IAAI;QACF;QACA,IAAI,IAAI,CAACH,OAAO,CAACzB,GAAG,CAAC4B,KAAK,CAAC,EAAE;UAC3B,IAAI,CAACH,OAAO,CAACvB,GAAG,CAAC0B,KAAK,CAAC,CAACU,OAAO,CAACF,MAAM,IAAI;YACxC,IAAIA,MAAM,IAAI,OAAOA,MAAM,CAACG,IAAI,KAAK,UAAU,EAAE;cAC/CH,MAAM,CAACG,IAAI,CAAC,CAAC;YACf;UACF,CAAC,CAAC;UACF,IAAI,CAACd,OAAO,CAACe,MAAM,CAACZ,KAAK,CAAC;QAC5B;;QAEA;QACA,IAAI,OAAOA,KAAK,CAACa,aAAa,KAAK,UAAU,EAAE;UAC7Cb,KAAK,CAACa,aAAa,CAAC,CAAC;QACvB;;QAEA;QACA,MAAMC,IAAI,GAAGd,KAAK,CAACe,OAAO,CAAC,CAAC;QAC5B,IAAID,IAAI,EAAE;UACR,IAAI,CAAChB,MAAM,CAACc,MAAM,CAACE,IAAI,CAAC;UACxBA,IAAI,CAACX,QAAQ,CAACC,MAAM,IAAI;YACtB,IAAIA,MAAM,IAAIA,MAAM,CAACC,MAAM,EAAE;cAC3B,IAAI,CAACT,KAAK,CAACgB,MAAM,CAACR,MAAM,CAACE,IAAI,CAAC;YAChC;YACA,IAAIF,MAAM,IAAIA,MAAM,CAACY,UAAU,EAAE;cAC/BZ,MAAM,CAACY,UAAU,CAACC,MAAM,GAAG,CAAC;YAC9B;UACF,CAAC,CAAC;;UAEF;UACA,IAAI;YACF,IAAI,OAAOjB,KAAK,CAACkB,WAAW,KAAK,UAAU,EAAE;cAC3ClB,KAAK,CAACkB,WAAW,CAACJ,IAAI,CAAC;YACzB;YAEA,IAAI,OAAOd,KAAK,CAACmB,aAAa,KAAK,UAAU,EAAE;cAC7CnB,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAEL,IAAI,CAAC;YACjC;;YAEA;YACA,IAAI,OAAOd,KAAK,CAACoB,WAAW,KAAK,UAAU,EAAE;cAC3C;cACA,MAAMC,KAAK,GAAGrB,KAAK,CAACsB,QAAQ,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;cACnEN,KAAK,CAACX,OAAO,CAACkB,IAAI,IAAI;gBACpB,IAAIA,IAAI,IAAIA,IAAI,CAACtB,IAAI,EAAE;kBACrBN,KAAK,CAACoB,WAAW,CAACQ,IAAI,CAAC;gBACzB;cACF,CAAC,CAAC;YACJ;UACF,CAAC,CAAC,OAAOC,CAAC,EAAE;YACV/F,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE8F,CAAC,CAAC;YACnC;UACF;QACF;QAEA,IAAI,CAAClC,MAAM,CAACiB,MAAM,CAACZ,KAAK,CAAC;MAC3B,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrC;QACA,IAAI,CAACuC,MAAM,CAACiB,MAAM,CAACZ,KAAK,CAAC;MAC3B;IACF;EACF,CAAC;EAED8B,OAAOA,CAAA,EAAG;IACR,IAAI;MACF;MACA,IAAI,CAACjC,OAAO,CAACa,OAAO,CAAC,CAACb,OAAO,EAAEG,KAAK,KAAK;QACvC,IAAI;UACFH,OAAO,CAACa,OAAO,CAACF,MAAM,IAAI;YACxB,IAAIA,MAAM,IAAI,OAAOA,MAAM,CAACG,IAAI,KAAK,UAAU,EAAE;cAC/CH,MAAM,CAACG,IAAI,CAAC,CAAC;YACf;UACF,CAAC,CAAC;UACFd,OAAO,CAACkC,KAAK,CAAC,CAAC;QACjB,CAAC,CAAC,OAAOF,CAAC,EAAE;UACV/F,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE8F,CAAC,CAAC;QACjC;MACF,CAAC,CAAC;MACF,IAAI,CAAChC,OAAO,CAACkC,KAAK,CAAC,CAAC;;MAEpB;MACA,IAAI,CAACpC,MAAM,CAACe,OAAO,CAACV,KAAK,IAAI;QAC3B,IAAI;UACF,IAAI,OAAOA,KAAK,CAACa,aAAa,KAAK,UAAU,EAAE;YAC7Cb,KAAK,CAACa,aAAa,CAAC,CAAC;UACvB;UAEA,MAAMC,IAAI,GAAGd,KAAK,CAACe,OAAO,CAAC,CAAC;UAC5B,IAAID,IAAI,EAAE;YACRA,IAAI,CAACX,QAAQ,CAACC,MAAM,IAAI;cACtB,IAAIA,MAAM,IAAIA,MAAM,CAACY,UAAU,EAAE;gBAC/BZ,MAAM,CAACY,UAAU,CAACC,MAAM,GAAG,CAAC;cAC9B;YACF,CAAC,CAAC;;YAEF;YACA,IAAI,OAAOjB,KAAK,CAACkB,WAAW,KAAK,UAAU,EAAE;cAC3ClB,KAAK,CAACkB,WAAW,CAACJ,IAAI,CAAC;YACzB;YAEA,IAAI,OAAOd,KAAK,CAACmB,aAAa,KAAK,UAAU,EAAE;cAC7CnB,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAEL,IAAI,CAAC;YACjC;;YAEA;YACA,IAAI;cACF,IAAId,KAAK,CAACsB,QAAQ,IAAIzE,KAAK,CAACC,OAAO,CAACkD,KAAK,CAACsB,QAAQ,CAAC,EAAE;gBACnD,MAAMD,KAAK,GAAGrB,KAAK,CAACsB,QAAQ,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;gBACnEN,KAAK,CAACX,OAAO,CAACkB,IAAI,IAAI;kBACpB,IAAIA,IAAI,IAAIA,IAAI,CAACtB,IAAI,IAAI,OAAON,KAAK,CAACoB,WAAW,KAAK,UAAU,EAAE;oBAChEpB,KAAK,CAACoB,WAAW,CAACQ,IAAI,CAAC;kBACzB;gBACF,CAAC,CAAC;cACJ;YACF,CAAC,CAAC,OAAOC,CAAC,EAAE;cACV/F,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE8F,CAAC,CAAC;YACnC;UACF;QACF,CAAC,CAAC,OAAOA,CAAC,EAAE;UACV/F,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE8F,CAAC,CAAC;QAClC;MACF,CAAC,CAAC;MACF,IAAI,CAAClC,MAAM,CAACoC,KAAK,CAAC,CAAC;;MAEnB;MACA,IAAI,CAACnC,KAAK,CAACc,OAAO,CAACsB,IAAI,IAAI;QACzB,IAAIA,IAAI,CAACC,MAAM,EAAE;UACfD,IAAI,CAACC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAC;QAC1B;QACA,IAAIA,IAAI,CAACG,MAAM,EAAEH,IAAI,CAACG,MAAM,CAACC,QAAQ,CAAC,CAAC;QACvC,IAAIJ,IAAI,CAACK,WAAW,EAAEL,IAAI,CAACK,WAAW,CAACD,QAAQ,CAAC,CAAC;MACnD,CAAC,CAAC;MACF,IAAI,CAACxC,KAAK,CAACmC,KAAK,CAAC,CAAC;;MAElB;MACA,IAAI,CAACjC,MAAM,CAACY,OAAO,CAACT,KAAK,IAAI;QAC3B,IAAIA,KAAK,CAACgC,MAAM,EAAE;UAChBhC,KAAK,CAACgC,MAAM,CAACC,MAAM,CAACjC,KAAK,CAAC;QAC5B;QACAA,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAI;UACvB,IAAIA,MAAM,CAACkC,MAAM,EAAE;YACjB,IAAIlC,MAAM,CAACmC,QAAQ,EAAE;cACnBnC,MAAM,CAACmC,QAAQ,CAACC,OAAO,CAAC,CAAC;YAC3B;YACA,IAAIpC,MAAM,CAACqC,QAAQ,EAAE;cACnB,IAAI5F,KAAK,CAACC,OAAO,CAACsD,MAAM,CAACqC,QAAQ,CAAC,EAAE;gBAClCrC,MAAM,CAACqC,QAAQ,CAAC/B,OAAO,CAAC+B,QAAQ,IAAI;kBAClC,IAAIA,QAAQ,CAAClB,GAAG,EAAEkB,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;kBACxCC,QAAQ,CAACD,OAAO,CAAC,CAAC;gBACpB,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL,IAAIpC,MAAM,CAACqC,QAAQ,CAAClB,GAAG,EAAEnB,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;gBACtDpC,MAAM,CAACqC,QAAQ,CAACD,OAAO,CAAC,CAAC;cAC3B;YACF;UACF;UACA,IAAIpC,MAAM,CAACY,UAAU,EAAE;YACrBZ,MAAM,CAACY,UAAU,CAACC,MAAM,GAAG,CAAC;UAC9B;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAACnB,MAAM,CAACiC,KAAK,CAAC,CAAC;IACrB,CAAC,CAAC,OAAO3E,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC;MACA,IAAI,CAACyC,OAAO,CAACkC,KAAK,CAAC,CAAC;MACpB,IAAI,CAACpC,MAAM,CAACoC,KAAK,CAAC,CAAC;MACnB,IAAI,CAACnC,KAAK,CAACmC,KAAK,CAAC,CAAC;MAClB,IAAI,CAACjC,MAAM,CAACiC,KAAK,CAAC,CAAC;IACrB;EACF;AACF,CAAC;;AAED;AACA,MAAMW,oBAAoB,GAAIzC,KAAK,IAAK;EACtC,MAAMD,KAAK,GAAG,IAAIrH,KAAK,CAACgK,cAAc,CAAC1C,KAAK,CAAC;EAC7C,OAAOP,eAAe,CAACK,QAAQ,CAACC,KAAK,EAAEC,KAAK,CAAC;AAC/C,CAAC;;AAED;AACA,MAAM2C,YAAY,GAAGA,CAAChB,IAAI,EAAE5B,KAAK,EAAEC,KAAK,KAAK;EAC3C,MAAMO,MAAM,GAAGR,KAAK,CAAC6C,UAAU,CAACjB,IAAI,EAAE3B,KAAK,CAAC;EAC5C,OAAOP,eAAe,CAACa,SAAS,CAACC,MAAM,EAAER,KAAK,CAAC;AACjD,CAAC;;AAED;AACA,MAAM8C,mBAAmB,GAAG,IAAItD,GAAG,CAAC,CAAC;AAErC,MAAMuD,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC,kBAAkB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAMC,YAAY,GAAG5K,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM6K,UAAU,GAAG7K,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM8K,SAAS,GAAG9K,MAAM,CAAC,IAAIM,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMyK,aAAa,GAAG/K,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMgL,eAAe,GAAGhL,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMiL,aAAa,GAAGjL,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMkL,iBAAiB,GAAGlL,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMmL,MAAM,GAAGnL,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAMoL,kBAAkB,GAAGpL,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMqL,gBAAgB,GAAGrL,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMsL,eAAe,GAAG,IAAI,CAAC,CAAC;;EAE9B;EACA,MAAMC,oBAAoB,GAAGvL,MAAM,CAACwL,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC/C,MAAMC,qBAAqB,GAAG,IAAIpJ,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC,MAAMqJ,gBAAgB,GAAG3L,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;;EAKrC;EACA,MAAM,CAAC4L,YAAY,EAAEC,eAAe,CAAC,GAAG5L,QAAQ,CAAC;IAC/C6L,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlM,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMmM,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,IAAI;IAAG;IACfC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGrN,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAACsN,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtN,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAACuN,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxN,QAAQ,CAAC;IAC7DyN,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,IAAI;IACbtB,QAAQ,EAAE;MAAE/G,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxBoI,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAG9N,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM+N,0BAA0B,GAAG/N,MAAM,CAAC,IAAI,CAAC;;EAE/C;EACA0C,MAAM,CAACsL,uBAAuB,GAAGP,sBAAsB;;EAEvD;EACA/K,MAAM,CAACoL,mBAAmB,GAAGA,mBAAmB;EAChDpL,MAAM,CAACqL,0BAA0B,GAAGA,0BAA0B;;EAE9D;EACA,MAAME,uBAAuB,GAAG;IAC9B5B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IAAG;IACd3B,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7B2B,KAAK,EAAE,OAAO;IAAG;IACjB1B,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMiB,UAAU,GAAG;IACjB/B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACX3B,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBwB,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbpB,QAAQ,EAAE,MAAM;IAChBqB,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvC/B,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM,CAAC9I,gBAAgB,CAAC,GAAG1D,QAAQ,CAAC,IAAIqC,GAAG,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACmM,UAAU,EAAEC,aAAa,CAAC,GAAGzO,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACwD,gBAAgB,EAAEkL,mBAAmB,CAAC,GAAG1O,QAAQ,CAAC,IAAIqC,GAAG,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACsM,WAAW,EAAEC,cAAc,CAAC,GAAG5O,QAAQ,CAAC;IAAE6O,KAAK,EAAE,EAAE;IAAElB,OAAO,EAAE;EAAG,CAAC,CAAC;;EAE1E;EACA,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAG/O,QAAQ,CAAC;IACjDyN,OAAO,EAAE,KAAK;IACdrB,QAAQ,EAAE;MAAE/G,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxByJ,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,gBAAgB,CAAC;MAAEtB,OAAO,EAAE,KAAK;MAAErB,QAAQ,EAAE;QAAE/G,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC;MAAEyJ,MAAM,EAAE;IAAK,CAAC,CAAC;EAC9E,CAAC;;EAED;EACA,MAAME,0BAA0B,GAAIF,MAAM,IAAK;IAC7C,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;IACxB;IACA,IAAIA,MAAM,CAACG,IAAI,KAAK,QAAQ,EAAE;MAC5B;MACA,oBACErO,OAAA;QAAKsO,KAAK,EAAE;UAAElB,KAAK,EAAE,GAAG;UAAEmB,MAAM,EAAE,GAAG;UAAEC,UAAU,EAAE,MAAM;UAAEvC,YAAY,EAAE,CAAC;UAAEwC,QAAQ,EAAE,QAAQ;UAAEnD,QAAQ,EAAE;QAAW,CAAE;QAAAoD,QAAA,gBACrH1O,OAAA,CAACjB,KAAK,CAAC4P,QAAQ;UAACC,QAAQ,eAAE5O,OAAA;YAAKsO,KAAK,EAAE;cAACf,KAAK,EAAC,MAAM;cAACsB,SAAS,EAAC,QAAQ;cAACC,UAAU,EAAC;YAAE,CAAE;YAAAJ,QAAA,EAAC;UAAQ;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAE;UAAAR,QAAA,eACpG1O,OAAA,CAACmP,WAAW;YAACC,QAAQ,EAAElB,MAAM,CAACmB,EAAG;YAACC,OAAO,EAAEpB,MAAM,CAACoB;UAAQ;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACjBlP,OAAA;UAAKsO,KAAK,EAAE;YAAExC,OAAO,EAAE,KAAK;YAAE0C,UAAU,EAAE,iBAAiB;YAAEjB,KAAK,EAAE,MAAM;YAAEpB,QAAQ,EAAE,EAAE;YAAEF,YAAY,EAAE,CAAC;YAAEsD,SAAS,EAAE;UAAE,CAAE;UAAAb,QAAA,gBACxH1O,OAAA;YAAA0O,QAAA,gBAAK1O,OAAA;cAAA0O,QAAA,EAAG;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAAChB,MAAM,CAACsB,IAAI;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClClP,OAAA;YAAA0O,QAAA,gBAAK1O,OAAA;cAAA0O,QAAA,EAAG;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAAChB,MAAM,CAACG,IAAI;UAAA;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClClP,OAAA;YAAA0O,QAAA,gBAAK1O,OAAA;cAAA0O,QAAA,EAAG;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAAChB,MAAM,CAACuB,MAAM;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpClP,OAAA;YAAA0O,QAAA,gBAAK1O,OAAA;cAAA0O,QAAA,EAAG;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAAChB,MAAM,CAACtM,QAAQ;UAAA;YAAAmN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtClP,OAAA;YAAA0O,QAAA,gBAAK1O,OAAA;cAAA0O,QAAA,EAAG;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAAChB,MAAM,CAACwB,SAAS,IAAI,GAAG;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9ClP,OAAA;YAAA0O,QAAA,gBAAK1O,OAAA;cAAA0O,QAAA,EAAG;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAAChB,MAAM,CAACyB,WAAW,IAAI,GAAG;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV,CAAC,MAAM;MACL;MACA,MAAMU,OAAO,GAAG,GAAGzN,QAAQ,WAAW+L,MAAM,CAACG,IAAI,MAAM;MACvD,oBACErO,OAAA;QAAKsO,KAAK,EAAE;UAAElB,KAAK,EAAE;QAAI,CAAE;QAAAsB,QAAA,gBACzB1O,OAAA;UAAKsO,KAAK,EAAE;YAAEO,SAAS,EAAE,QAAQ;YAAEgB,YAAY,EAAE;UAAE,CAAE;UAAAnB,QAAA,eACnD1O,OAAA;YAAK8P,GAAG,EAAEF,OAAQ;YAACG,GAAG,EAAE7B,MAAM,CAACG,IAAK;YAACC,KAAK,EAAE;cAAElB,KAAK,EAAE,EAAE;cAAEmB,MAAM,EAAE,EAAE;cAAEyB,SAAS,EAAE,SAAS;cAAExB,UAAU,EAAE,MAAM;cAAEvC,YAAY,EAAE,CAAC;cAAED,MAAM,EAAE;YAAiB;UAAE;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3J,CAAC,eACNlP,OAAA;UAAKsO,KAAK,EAAE;YAAExC,OAAO,EAAE,KAAK;YAAE0C,UAAU,EAAE,iBAAiB;YAAEjB,KAAK,EAAE,MAAM;YAAEpB,QAAQ,EAAE,EAAE;YAAEF,YAAY,EAAE;UAAE,CAAE;UAAAyC,QAAA,gBAC1G1O,OAAA;YAAA0O,QAAA,gBAAK1O,OAAA;cAAA0O,QAAA,EAAG;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAAChB,MAAM,CAACsB,IAAI;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClClP,OAAA;YAAA0O,QAAA,gBAAK1O,OAAA;cAAA0O,QAAA,EAAG;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAAChB,MAAM,CAACG,IAAI;UAAA;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClClP,OAAA;YAAA0O,QAAA,gBAAK1O,OAAA;cAAA0O,QAAA,EAAG;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAAChB,MAAM,CAACuB,MAAM;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpClP,OAAA;YAAA0O,QAAA,gBAAK1O,OAAA;cAAA0O,QAAA,EAAG;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAAChB,MAAM,CAACtM,QAAQ;UAAA;YAAAmN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtClP,OAAA;YAAA0O,QAAA,gBAAK1O,OAAA;cAAA0O,QAAA,EAAG;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAAChB,MAAM,CAACwB,SAAS,IAAI,GAAG;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9ClP,OAAA;YAAA0O,QAAA,gBAAK1O,OAAA;cAAA0O,QAAA,EAAG;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EAAChB,MAAM,CAACyB,WAAW,IAAI,GAAG;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;EACF,CAAC;;EAED;EACA;;EAEA;EACA,MAAMe,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIvP,UAAU,KAAK,QAAQ,EAAE;MAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB9B,UAAU,GAAG,QAAQ;;MAErB;MACA2J,kBAAkB,CAAC6F,OAAO,GAAG,IAAI;MACjC5F,gBAAgB,CAAC4F,OAAO,GAAG,IAAI;MAE/B,IAAIvP,QAAQ,EAAE;QACZA,QAAQ,CAACwP,OAAO,GAAG,KAAK;MAC1B;IACF;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI1P,UAAU,KAAK,QAAQ,EAAE;MAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB9B,UAAU,GAAG,QAAQ;;MAErB;MACA2J,kBAAkB,CAAC6F,OAAO,GAAG,IAAI;MACjC5F,gBAAgB,CAAC4F,OAAO,GAAG,IAAI;MAE/B,IAAI5D,SAAS,CAAC4D,OAAO,IAAIvP,QAAQ,EAAE;QACjC;QACA;QACA2L,SAAS,CAAC4D,OAAO,CAAC5E,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC,MAAMyL,UAAU,GAAG/D,SAAS,CAAC4D,OAAO,CAAC5E,QAAQ,CAACjH,KAAK,CAAC,CAAC;QACrD,MAAMiM,SAAS,GAAGhE,SAAS,CAAC4D,OAAO,CAACK,EAAE,CAAClM,KAAK,CAAC,CAAC;;QAE9C;QACA,IAAI7E,KAAK,CAACgR,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC;UAAElM,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,GAAG;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAChC+L,MAAM,CAAClR,KAAK,CAACmR,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdxE,SAAS,CAAC4D,OAAO,CAAC5E,QAAQ,CAACyF,IAAI,CAACV,UAAU,CAAC;QAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;QAEV;QACA,IAAIxR,KAAK,CAACgR,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;UAAElM,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAC9B+L,MAAM,CAAClR,KAAK,CAACmR,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdxE,SAAS,CAAC4D,OAAO,CAACK,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC;QACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC;;QAEV;QACArQ,QAAQ,CAACsQ,MAAM,CAACrM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B,MAAMsM,aAAa,GAAGvQ,QAAQ,CAACsQ,MAAM,CAAC5M,KAAK,CAAC,CAAC;;QAE7C;QACA,IAAI7E,KAAK,CAACgR,KAAK,CAACU,aAAa,CAAC,CAC3BT,EAAE,CAAC;UAAElM,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAC9B+L,MAAM,CAAClR,KAAK,CAACmR,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdnQ,QAAQ,CAACsQ,MAAM,CAACF,IAAI,CAACG,aAAa,CAAC;UACnC;UACA5E,SAAS,CAAC4D,OAAO,CAACiB,MAAM,CAACxQ,QAAQ,CAACsQ,MAAM,CAAC;UACzCtQ,QAAQ,CAACyQ,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;QAEV;QACArQ,QAAQ,CAACwP,OAAO,GAAG,IAAI;;QAEvB;QACAxP,QAAQ,CAAC0Q,WAAW,GAAG,EAAE;QACzB1Q,QAAQ,CAAC2Q,WAAW,GAAG,GAAG;QAC1B3Q,QAAQ,CAAC4Q,aAAa,GAAG9L,IAAI,CAACC,EAAE,GAAG,GAAG;QACtC/E,QAAQ,CAAC6Q,aAAa,GAAG,CAAC;QAC1B7Q,QAAQ,CAACyQ,MAAM,CAAC,CAAC;QACjB;QACA9E,SAAS,CAAC4D,OAAO,CAACuB,YAAY,CAAC,CAAC;QAChCnF,SAAS,CAAC4D,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;QAEzCnP,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;UACrBmP,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAGnS,iBAAiB,CAACoS,aAAa,CAACxO,IAAI,CAACyO,CAAC,IAAIA,CAAC,CAAC1C,IAAI,KAAKuC,KAAK,CAAC;IAEhF,IAAIC,YAAY,IAAI1F,SAAS,CAAC4D,OAAO,IAAIvP,QAAQ,EAAE;MACjD6L,uBAAuB,CAACwF,YAAY,CAAC;;MAErC;MACA,MAAMG,WAAW,GAAGpI,SAAS,CAACmG,OAAO,CAACkC,YAAY,CAChDC,UAAU,CAACL,YAAY,CAACjH,SAAS,CAAC,EAClCsH,UAAU,CAACL,YAAY,CAAChH,QAAQ,CAClC,CAAC;MAEDzI,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvB8P,IAAI,EAAEN,YAAY,CAACxC,IAAI;QACvB+C,GAAG,EAAE;UACHxH,SAAS,EAAEiH,YAAY,CAACjH,SAAS;UACjCC,QAAQ,EAAEgH,YAAY,CAAChH;QACzB,CAAC;QACDwH,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACAzR,UAAU,GAAG,cAAc;MAC3B0K,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAkB,SAAS,CAAC4D,OAAO,CAAC5E,QAAQ,CAAC1G,GAAG,CAACuN,WAAW,CAAC5N,CAAC,GAAC,EAAE,EAAE,EAAE,EAAE,CAAC4N,WAAW,CAAC1N,CAAC,GAAC,EAAE,CAAC;;MAEvE;MACA9D,QAAQ,CAACsQ,MAAM,CAACrM,GAAG,CAACuN,WAAW,CAAC5N,CAAC,EAAE,CAAC,EAAE,CAAC4N,WAAW,CAAC1N,CAAC,CAAC;;MAErD;MACA6H,SAAS,CAAC4D,OAAO,CAACiB,MAAM,CAACxQ,QAAQ,CAACsQ,MAAM,CAAC;;MAEzC;MACAtQ,QAAQ,CAACwP,OAAO,GAAG,IAAI;MACvBxP,QAAQ,CAACyQ,MAAM,CAAC,CAAC;;MAEjB;MACA9E,SAAS,CAAC4D,OAAO,CAACuB,YAAY,CAAC,CAAC;MAChCnF,SAAS,CAAC4D,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;MAEzCnP,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzB8P,IAAI,EAAEN,YAAY,CAACxC,IAAI;QACvBiD,IAAI,EAAEnG,SAAS,CAAC4D,OAAO,CAAC5E,QAAQ,CAACoH,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAEhS,QAAQ,CAACsQ,MAAM,CAACyB,OAAO,CAAC,CAAC;QAC9BF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACA,IAAIH,YAAY,CAACY,eAAe,KAAK,KAAK,IAAIZ,YAAY,CAACpF,OAAO,EAAE;QAClErK,OAAO,CAACC,GAAG,CAAC,MAAMwP,YAAY,CAACxC,IAAI,iBAAiB,CAAC;;QAErD;QACAqD,UAAU,CAAC,MAAM;UACf;UACA,IAAIjG,OAAO,GAAGoF,YAAY,CAACpF,OAAO;;UAElC;UACA,IAAIjL,MAAM,CAACmR,qBAAqB,EAAE;YAChCnR,MAAM,CAACmR,qBAAqB,CAAClG,OAAO,CAAC;YACrCrK,OAAO,CAACC,GAAG,CAAC,SAASwP,YAAY,CAACxC,IAAI,SAAS5C,OAAO,YAAY,CAAC;UACrE,CAAC,MAAM;YACLrK,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC;UAChC;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLtB,OAAO,CAACC,GAAG,CAAC,MAAMwP,YAAY,CAACxC,IAAI,sBAAsB,CAAC;;QAE1D;QACA,IAAI7N,MAAM,CAACsL,uBAAuB,EAAE;UAClCtL,MAAM,CAACsL,uBAAuB,CAAC;YAC7BN,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAMoG,iBAAiB,GAAGA,CAAChF,KAAK,EAAEiF,OAAO,KAAK;IAC5C,IAAI;MACF,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;MAEnC;MACA,IAAIjF,KAAK,KAAKtM,WAAW,CAACO,GAAG,EAAE;QAAA,IAAAoR,aAAA;QAC7B;;QAEA;QACA,MAAMC,SAAS,GAAGJ,OAAO,CAACK,GAAG;QAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;QAEnC;QACA,MAAMC,aAAa,GAAG/Q,gBAAgB,CAACqC,GAAG,CAACsO,SAAS,CAAC;;QAErD;QACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;UACrD;UACA;UACA;UACA;UACA;UACA;QACF;;QAEA;QACA/Q,gBAAgB,CAACkC,GAAG,CAACyO,SAAS,EAAEE,gBAAgB,CAAC;;QAEjD;QACA;QACA;QACA;QACA;;QAEA,MAAMG,YAAY,GAAG,EAAAN,aAAA,GAAAH,OAAO,CAAC9P,IAAI,cAAAiQ,aAAA,uBAAZA,aAAA,CAAcM,YAAY,KAAI,EAAE;QACrD,MAAMC,KAAK,GAAGV,OAAO,CAAC9P,IAAI,CAACwQ,KAAK;;QAEhC;QACA,MAAMjJ,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;;QAEtB;QACAgJ,YAAY,CAACvM,OAAO,CAACyM,WAAW,IAAI;UAClC;UACA;UACA,MAAMvE,EAAE,GAAIgE,SAAS,GAAGO,WAAW,CAACC,SAAS;UAC7C,MAAMxF,IAAI,GAAGuF,WAAW,CAACE,WAAW;UAEpC,IAAGzF,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACE;YACA,MAAM0F,KAAK,GAAG;cACZhJ,SAAS,EAAEsH,UAAU,CAACuB,WAAW,CAACI,WAAW,CAAC;cAC9ChJ,QAAQ,EAAEqH,UAAU,CAACuB,WAAW,CAACK,UAAU,CAAC;cAC5ChJ,KAAK,EAAEoH,UAAU,CAACuB,WAAW,CAACM,SAAS,CAAC;cACxChJ,OAAO,EAAEmH,UAAU,CAACuB,WAAW,CAACO,WAAW;YAC7C,CAAC;YAED,MAAMC,QAAQ,GAAGrK,SAAS,CAACmG,OAAO,CAACkC,YAAY,CAAC2B,KAAK,CAAChJ,SAAS,EAAEgJ,KAAK,CAAC/I,QAAQ,CAAC;;YAEhF;YACA,IAAIqJ,cAAc;YAClB,QAAQhG,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRgG,cAAc,GAAGzT,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRyT,cAAc,GAAGxT,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRwT,cAAc,GAAGvT,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACF,IAAI4F,KAAK,GAAGjE,aAAa,CAACsC,GAAG,CAACsK,EAAE,CAAC;YAEjC,IAAI,CAAC3I,KAAK,IAAI2N,cAAc,EAAE;cAC1B;cACA,MAAMC,QAAQ,GAAGjG,IAAI,KAAK,GAAG,GAAG5O,aAAa,CAAC4E,KAAK,CAACvD,oBAAoB,CAAC,GAAGuT,cAAc,CAAChQ,KAAK,CAAC,CAAC;cAClG;cACA,MAAMkK,MAAM,GAAGF,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;cACvCiG,QAAQ,CAAChJ,QAAQ,CAAC1G,GAAG,CAACwP,QAAQ,CAAC7P,CAAC,EAAEgK,MAAM,EAAE,CAAC6F,QAAQ,CAAC3P,CAAC,CAAC;cACtD6P,QAAQ,CAACC,QAAQ,CAAC9P,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGqO,KAAK,CAAC7I,OAAO,GAAGzF,IAAI,CAACC,EAAE,GAAG,GAAG;;cAE7D;cACA,IAAI2I,IAAI,KAAK,GAAG,EAAE;gBAClB;gBACEiG,QAAQ,CAACE,KAAK,CAAC5P,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;gBAE3B;gBACA,MAAM6B,KAAK,GAAG0C,oBAAoB,CAACmL,QAAQ,CAAC;gBAE5C,IAAIrT,eAAe,IAAIA,eAAe,CAACwG,UAAU,IAAIxG,eAAe,CAACwG,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;kBAC1F;kBACA,MAAMT,MAAM,GAAGoC,YAAY,CAACpI,eAAe,CAACwG,UAAU,CAAC,CAAC,CAAC,EAAEhB,KAAK,EAAE6N,QAAQ,CAAC;kBAC3ErN,MAAM,CAACwN,IAAI,CAAC,CAAC;gBACf;;gBAEA;gBACA9J,qBAAqB,CAAC/F,GAAG,CAACyK,EAAE,EAAE5I,KAAK,CAAC;cACtC;cAEAzF,KAAK,CAAC2F,GAAG,CAAC2N,QAAQ,CAAC;cAEnB7R,aAAa,CAACmC,GAAG,CAACyK,EAAE,EAAE;gBACpB3I,KAAK,EAAE4N,QAAQ;gBACfI,UAAU,EAAEhK,GAAG;gBACjB2D,IAAI,EAAEA;cACN,CAAC,CAAC;YACN,CAAC,MAAM,IAAI3H,KAAK,EAAE;cACd;cACFA,KAAK,CAACA,KAAK,CAAC4E,QAAQ,CAAC1G,GAAG,CAACwP,QAAQ,CAAC7P,CAAC,EAAEmC,KAAK,CAAC2H,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC+F,QAAQ,CAAC3P,CAAC,CAAC;cACjFiC,KAAK,CAACA,KAAK,CAAC6N,QAAQ,CAAC9P,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGqO,KAAK,CAAC7I,OAAO,GAAGzF,IAAI,CAACC,EAAE,GAAG,GAAG;cAChEgB,KAAK,CAACgO,UAAU,GAAGhK,GAAG;cACtBhE,KAAK,CAACA,KAAK,CAAC+K,YAAY,CAAC,CAAC;cAC1B/K,KAAK,CAACA,KAAK,CAACgL,iBAAiB,CAAC,IAAI,CAAC;YACnC;UACF;QACF,CAAC,CAAC;;QAEF;QACA,MAAMiD,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAI3O,GAAG,CAACyN,YAAY,CAAC1L,GAAG,CAAC6M,CAAC,IAAIxB,SAAS,GAAGwB,CAAC,CAAChB,SAAS,CAAC,CAAC;QAE1EpR,aAAa,CAAC0E,OAAO,CAAC,CAAC2N,SAAS,EAAEzF,EAAE,KAAK;UACvC,IAAI3E,GAAG,GAAGoK,SAAS,CAACJ,UAAU,GAAGC,iBAAiB,IAAI,CAACC,UAAU,CAAC/P,GAAG,CAACwK,EAAE,CAAC,EAAE;YACzE;YACA,IAAIyF,SAAS,CAACzG,IAAI,KAAK,GAAG,IAAI1D,qBAAqB,CAAC9F,GAAG,CAACwK,EAAE,CAAC,EAAE;cAC3D,MAAM5I,KAAK,GAAGkE,qBAAqB,CAAC5F,GAAG,CAACsK,EAAE,CAAC;cAC3C;cACAlJ,eAAe,CAACe,WAAW,CAACT,KAAK,CAAC;cAClCkE,qBAAqB,CAACtD,MAAM,CAACgI,EAAE,CAAC;YAClC;;YAEA;YACArO,KAAK,CAAC2H,MAAM,CAACmM,SAAS,CAACpO,KAAK,CAAC;YAC7BjE,aAAa,CAAC4E,MAAM,CAACgI,EAAE,CAAC;UAC1B;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAItB,KAAK,KAAKtM,WAAW,CAACM,GAAG,EAAE;QAC7B;;QAEA,MAAMgT,OAAO,GAAG9B,OAAO,CAAC9P,IAAI;QAC5B,MAAM6R,KAAK,GAAGD,OAAO,CAACnR,KAAK;QAC3B,MAAMqR,QAAQ,GAAG;UACflK,SAAS,EAAEsH,UAAU,CAAC0C,OAAO,CAACG,QAAQ,CAAC;UACvClK,QAAQ,EAAEqH,UAAU,CAAC0C,OAAO,CAACI,OAAO,CAAC;UACrClK,KAAK,EAAEoH,UAAU,CAAC0C,OAAO,CAACb,SAAS,CAAC;UACpChJ,OAAO,EAAEmH,UAAU,CAAC0C,OAAO,CAACZ,WAAW;QACzC,CAAC;;QAED;QACA;;QAEA;QACAxS,MAAM,CAACyT,WAAW,CAAC;UACjB/G,IAAI,EAAE,iBAAiB;UACvBgH,MAAM,EAAE;QACV,CAAC,EAAE,GAAG,CAAC;;QAEP;QACA1T,MAAM,CAACyT,WAAW,CAAC;UACjB/G,IAAI,EAAE,KAAK;UACXzK,KAAK,EAAEoR,KAAK;UAAE;UACd7R,IAAI,EAAE;YAAQ;YACZS,KAAK,EAAEoR,KAAK;YACZd,SAAS,EAAEa,OAAO,CAACb,SAAS;YAC5BiB,OAAO,EAAEJ,OAAO,CAACI,OAAO;YACxBD,QAAQ,EAAEH,OAAO,CAACG,QAAQ;YAC1Bf,WAAW,EAAEY,OAAO,CAACZ;UACvB;QACF,CAAC,EAAE,GAAG,CAAC;;QAEP;QACA,MAAMC,QAAQ,GAAGrK,SAAS,CAACmG,OAAO,CAACkC,YAAY,CAAC6C,QAAQ,CAAClK,SAAS,EAAEkK,QAAQ,CAACjK,QAAQ,CAAC;QACtF,MAAMsK,eAAe,GAAG,IAAIlW,KAAK,CAACiG,OAAO,CAAC+O,QAAQ,CAAC7P,CAAC,EAAE,GAAG,EAAE,CAAC6P,QAAQ,CAAC3P,CAAC,CAAC;QACvE,MAAM8Q,eAAe,GAAG9P,IAAI,CAACC,EAAE,GAAGuP,QAAQ,CAAC/J,OAAO,GAAGzF,IAAI,CAACC,EAAE,GAAG,GAAG;;QAElE;QACA,MAAM8P,WAAW,GAAGtR,cAAc,CAACoR,eAAe,EAAEN,KAAK,CAAC;QAC1D,MAAMzP,WAAW,GAAGD,cAAc,CAACiQ,eAAe,EAAEP,KAAK,CAAC;;QAE1D;QACA,IAAIS,UAAU,GAAGhT,aAAa,CAACsC,GAAG,CAACiQ,KAAK,CAAC;;QAEzC;QACA,MAAMrR,aAAa,GAAGqR,KAAK,KAAKrS,gBAAgB;QAEhD,IAAI,CAAC8S,UAAU,IAAI7U,qBAAqB,EAAE;UACxC;UACA,MAAM8U,eAAe,GAAG9U,qBAAqB,CAACyD,KAAK,CAAC,CAAC;;UAErD;UACA;UACAqR,eAAe,CAACpK,QAAQ,CAAC1G,GAAG,CAAC4Q,WAAW,CAACjR,CAAC,EAAE,CAAC,CAAC,EAAEiR,WAAW,CAAC7Q,CAAC,CAAC;UAC9D+Q,eAAe,CAACnB,QAAQ,CAAC9P,CAAC,GAAGc,WAAW;;UAExC;UACAmQ,eAAe,CAAC9O,QAAQ,CAAE+O,KAAK,IAAK;YAClC,IAAIA,KAAK,CAAC5M,MAAM,IAAI4M,KAAK,CAACzM,QAAQ,EAAE;cAClC;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;;cAEA;cACA;cACA;;cAEA;;cAEA,MAAM0M,WAAW,GAAGD,KAAK,CAACzM,QAAQ,CAAC7E,KAAK,CAAC,CAAC;cAC1CsR,KAAK,CAACzM,QAAQ,GAAG0M,WAAW;;cAE5B;cACA,IAAIjS,aAAa,EAAE;gBACjBiS,WAAW,CAACrI,KAAK,CAAC3I,GAAG,CAAC,QAAQ,CAAC;cACjC,CAAC,MAAM;gBACLgR,WAAW,CAACrI,KAAK,CAAC3I,GAAG,CAAC,QAAQ,CAAC;cACjC;cACAgR,WAAW,CAACC,QAAQ,GAAG,IAAIzW,KAAK,CAAC0W,KAAK,CAAC,QAAQ,CAAC;cAChDF,WAAW,CAACG,WAAW,GAAG,IAAI;cAC9B;cACAH,WAAW,CAACI,WAAW,GAAG,IAAI;YAChC;UACF,CAAC,CAAC;;UAEF;UACA,MAAMC,UAAU,GAAGC,gBAAgB,CAAC,GAAGzQ,IAAI,CAAC0Q,KAAK,CAAClB,QAAQ,CAAChK,KAAK,CAAC,OAAO,EAAE;YACxEc,eAAe,EAAEpI,aAAa,GAC5B;cAAEyS,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAErO,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAEmO,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAErO,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCsO,SAAS,EAAE;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAErO,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/CkE,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFmK,UAAU,CAAC3K,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClCqR,UAAU,CAACO,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/BP,UAAU,CAAC/M,QAAQ,CAACuN,OAAO,GAAG,GAAG,CAAC,CAAC;UACnCf,eAAe,CAAC/O,GAAG,CAACsP,UAAU,CAAC;UAE/BjV,KAAK,CAAC2F,GAAG,CAAC+O,eAAe,CAAC;;UAE1B;UACAjT,aAAa,CAACmC,GAAG,CAACoQ,KAAK,EAAE;YACvBtO,KAAK,EAAEgP,eAAe;YACtBhB,UAAU,EAAEjK,IAAI,CAACC,GAAG,CAAC,CAAC;YACtB2D,IAAI,EAAE,GAAG;YAAE;YACXqI,MAAM,EAAE/S,aAAa;YACrBsS,UAAU,EAAEA,UAAU,CAAC;UACzB,CAAC,CAAC;;UAEF;;UAEA;UACA,IAAIzW,KAAK,CAACgR,KAAK,CAACkF,eAAe,CAACpK,QAAQ,CAAC,CACtCmF,EAAE,CAAC;YAAEhM,CAAC,EAAE;UAAI,CAAC,EAAE,GAAG,CAAC,CACnBiM,MAAM,CAAClR,KAAK,CAACmR,MAAM,CAACC,SAAS,CAAC+F,GAAG,CAAC,CAClC3F,KAAK,CAAC,CAAC;;UAEV;UACA0E,eAAe,CAAC9O,QAAQ,CAAE+O,KAAK,IAAK;YAClC,IAAIA,KAAK,CAAC5M,MAAM,IAAI4M,KAAK,CAACzM,QAAQ,IAAIyM,KAAK,CAACzM,QAAQ,CAAC6M,WAAW,EAAE;cAChE,IAAIvW,KAAK,CAACgR,KAAK,CAAC;gBAAEiG,OAAO,EAAE;cAAI,CAAC,CAAC,CAC9BhG,EAAE,CAAC;gBAAEgG,OAAO,EAAE;cAAI,CAAC,EAAE,GAAG,CAAC,CACzB/F,MAAM,CAAClR,KAAK,CAACmR,MAAM,CAACC,SAAS,CAAC+F,GAAG,CAAC,CAClC7F,QAAQ,CAAC,YAAW;gBACnB6E,KAAK,CAACzM,QAAQ,CAACuN,OAAO,GAAG,IAAI,CAACA,OAAO;gBACrCd,KAAK,CAACzM,QAAQ,CAAC8M,WAAW,GAAG,IAAI;cACnC,CAAC,CAAC,CACDhF,KAAK,CAAC,CAAC;YACZ;UACF,CAAC,CAAC;;UAEF;UACA,IAAIxR,KAAK,CAACgR,KAAK,CAAC;YAAEiG,OAAO,EAAE;UAAI,CAAC,CAAC,CAC9BhG,EAAE,CAAC;YAAEgG,OAAO,EAAE;UAAI,CAAC,EAAE,GAAG,CAAC,CACzB/F,MAAM,CAAClR,KAAK,CAACmR,MAAM,CAACC,SAAS,CAAC+F,GAAG,CAAC,CAClC7F,QAAQ,CAAC,YAAW;YACnBmF,UAAU,CAAC/M,QAAQ,CAACuN,OAAO,GAAG,IAAI,CAACA,OAAO;YAC1CR,UAAU,CAAC/M,QAAQ,CAAC8M,WAAW,GAAG,IAAI;UACxC,CAAC,CAAC,CACDhF,KAAK,CAAC,CAAC;;UAEV;UACA,IAAIrN,aAAa,EAAE;YACjBxD,gBAAgB,GAAGuV,eAAe;YAClC5K,eAAe,CAACmK,QAAQ,CAAC;YACzB1S,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEwS,KAAK,CAAC;UACjC;QACF,CAAC,MAAM,IAAIS,UAAU,EAAE;UACrB;UACA,MAAMmB,gBAAgB,GAAG1S,cAAc,CAACsR,WAAW,EAAER,KAAK,CAAC;UAC3D,MAAMrP,gBAAgB,GAAGL,cAAc,CAACC,WAAW,EAAEyP,KAAK,CAAC;;UAE3D;UACAS,UAAU,CAAC/O,KAAK,CAAC4E,QAAQ,CAACyF,IAAI,CAAC6F,gBAAgB,CAAC;UAChDnB,UAAU,CAAC/O,KAAK,CAAC6N,QAAQ,CAAC9P,CAAC,GAAGkB,gBAAgB;UAC9C8P,UAAU,CAAC/O,KAAK,CAAC+K,YAAY,CAAC,CAAC;UAC/BgE,UAAU,CAAC/O,KAAK,CAACgL,iBAAiB,CAAC,IAAI,CAAC;UACxC+D,UAAU,CAACf,UAAU,GAAGjK,IAAI,CAACC,GAAG,CAAC,CAAC;UAClC+K,UAAU,CAACiB,MAAM,GAAG/S,aAAa,CAAC,CAAC;;UAEnC;UACA,IAAI8R,UAAU,CAACQ,UAAU,EAAE;YACzBR,UAAU,CAACQ,UAAU,CAAC/M,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;YAC5CwM,UAAU,CAAC/O,KAAK,CAACiC,MAAM,CAAC8M,UAAU,CAACQ,UAAU,CAAC;UAChD;;UAEA;UACA,MAAMA,UAAU,GAAGC,gBAAgB,CAAC,GAAGzQ,IAAI,CAAC0Q,KAAK,CAAClB,QAAQ,CAAChK,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE;YAC9Ec,eAAe,EAAEpI,aAAa,GAC5B;cAAEyS,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAErO,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAEmO,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAErO,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCsO,SAAS,EAAE;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAErO,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/CkE,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFmK,UAAU,CAAC3K,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACnCqR,UAAU,CAACO,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/Bf,UAAU,CAAC/O,KAAK,CAACC,GAAG,CAACsP,UAAU,CAAC;UAChCR,UAAU,CAACQ,UAAU,GAAGA,UAAU;;UAElC;;UAEA;UACA,IAAItS,aAAa,EAAE;YACjBxD,gBAAgB,GAAGsV,UAAU,CAAC/O,KAAK;YACnCoE,eAAe,CAACmK,QAAQ,CAAC;UAC3B;QACF;;QAEA;QACA,MAAMvK,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;QACtB,MAAMiK,iBAAiB,GAAG,IAAI,CAAC,CAAC;;QAEhClS,aAAa,CAAC0E,OAAO,CAAC,CAAC2N,SAAS,EAAEzF,EAAE,KAAK;UACvC,MAAMwH,mBAAmB,GAAGnM,GAAG,GAAGoK,SAAS,CAACJ,UAAU;;UAEtD;UACA,IAAImC,mBAAmB,GAAGlC,iBAAiB,GAAG,GAAG,IAAIkC,mBAAmB,IAAIlC,iBAAiB,EAAE;YAC7F;YACA,MAAM8B,OAAO,GAAG,CAAC;YAEjB3B,SAAS,CAACpO,KAAK,CAACE,QAAQ,CAAE+O,KAAK,IAAK;cAClC,IAAIA,KAAK,CAAC5M,MAAM,IAAI4M,KAAK,CAACzM,QAAQ,EAAE;gBAClC;gBACA,IAAIyM,KAAK,CAACzM,QAAQ,CAAC6M,WAAW,KAAKe,SAAS,EAAE;kBAC5CnB,KAAK,CAACzM,QAAQ,CAAC6N,mBAAmB,GAAGpB,KAAK,CAACzM,QAAQ,CAAC6M,WAAW,IAAI,KAAK;kBACxEJ,KAAK,CAACzM,QAAQ,CAAC8N,eAAe,GAAGrB,KAAK,CAACzM,QAAQ,CAACuN,OAAO,IAAI,GAAG;gBAChE;;gBAEA;gBACAd,KAAK,CAACzM,QAAQ,CAAC6M,WAAW,GAAG,IAAI;gBACjCJ,KAAK,CAACzM,QAAQ,CAACuN,OAAO,GAAGA,OAAO;gBAChCd,KAAK,CAACzM,QAAQ,CAAC8M,WAAW,GAAG,IAAI;cACnC;YACF,CAAC,CAAC;;YAEF;YACA,IAAIlB,SAAS,CAACmB,UAAU,EAAE;cACxBnB,SAAS,CAACmB,UAAU,CAAC/M,QAAQ,CAACuN,OAAO,GAAGA,OAAO;cAC/C3B,SAAS,CAACmB,UAAU,CAAC/M,QAAQ,CAAC8M,WAAW,GAAG,IAAI;YAClD;UACF;UACA;UAAA,KACK,IAAIa,mBAAmB,GAAGlC,iBAAiB,EAAE;YAChD;YACA,IAAIG,SAAS,CAACmB,UAAU,EAAE;cACxBnB,SAAS,CAACmB,UAAU,CAAC/M,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;cAC3C6L,SAAS,CAACmB,UAAU,CAAC/M,QAAQ,CAACD,OAAO,CAAC,CAAC;cACvC6L,SAAS,CAACpO,KAAK,CAACiC,MAAM,CAACmM,SAAS,CAACmB,UAAU,CAAC;YAC9C;YAEAnB,SAAS,CAACpO,KAAK,CAACE,QAAQ,CAAE+O,KAAK,IAAK;cAClC,IAAIA,KAAK,CAAC5M,MAAM,EAAE;gBAChB,IAAI4M,KAAK,CAACzM,QAAQ,EAAE;kBAClB,IAAI5F,KAAK,CAACC,OAAO,CAACoS,KAAK,CAACzM,QAAQ,CAAC,EAAE;oBACjCyM,KAAK,CAACzM,QAAQ,CAAC/B,OAAO,CAAC8P,CAAC,IAAIA,CAAC,CAAChO,OAAO,CAAC,CAAC,CAAC;kBAC1C,CAAC,MAAM;oBACL0M,KAAK,CAACzM,QAAQ,CAACD,OAAO,CAAC,CAAC;kBAC1B;gBACF;gBACA,IAAI0M,KAAK,CAAC3M,QAAQ,EAAE2M,KAAK,CAAC3M,QAAQ,CAACC,OAAO,CAAC,CAAC;cAC9C;YACF,CAAC,CAAC;;YAEF;YACAjI,KAAK,CAAC2H,MAAM,CAACmM,SAAS,CAACpO,KAAK,CAAC;YAC7BjE,aAAa,CAAC4E,MAAM,CAACgI,EAAE,CAAC;YACxB;YACA/N,oBAAoB,CAAC+F,MAAM,CAACgI,EAAE,CAAC;YAC/B7N,oBAAoB,CAAC6F,MAAM,CAACgI,EAAE,CAAC;YAE/B9M,OAAO,CAACC,GAAG,CAAC,mBAAmB6M,EAAE,EAAE,CAAC;UACtC;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAItB,KAAK,KAAKtM,WAAW,CAACS,IAAI,EAAE;QAC9B;;QAEA,IAAI;UACF,MAAM+Q,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;UAEnC;UACA,MAAMK,SAAS,GAAGJ,OAAO,CAACK,GAAG;UAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;UAEnC;UACA,MAAMC,aAAa,GAAG/Q,gBAAgB,CAACqC,GAAG,CAACsO,SAAS,CAAC;;UAErD;UACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;YACrD;YACA;YACA;YACA;YACA;YACA;UACF;;UAEA;UACA/Q,gBAAgB,CAACkC,GAAG,CAACyO,SAAS,EAAEE,gBAAgB,CAAC;;UAEjD;UACA,IAAIN,OAAO,CAAC9P,IAAI,IAAI8P,OAAO,CAAC9P,IAAI,CAAC8O,aAAa,IAAI3O,KAAK,CAACC,OAAO,CAAC0P,OAAO,CAAC9P,IAAI,CAAC8O,aAAa,CAAC,EAAE;YAC3FgB,OAAO,CAAC9P,IAAI,CAAC8O,aAAa,CAAC9K,OAAO,CAAC6K,YAAY,IAAI;cACjD,MAAMpF,OAAO,GAAGoF,YAAY,CAACpF,OAAO;cAEpC,IAAI,CAACA,OAAO,EAAE;gBACZrK,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEmO,YAAY,CAAC;gBAC/C;cACF;;cAEA;;cAEA;cACA,IAAIA,YAAY,CAAClF,MAAM,IAAIxJ,KAAK,CAACC,OAAO,CAACyO,YAAY,CAAClF,MAAM,CAAC,EAAE;gBAC7D;gBACA,MAAMoK,UAAU,GAAG,EAAE;gBAErBlF,YAAY,CAAClF,MAAM,CAAC3F,OAAO,CAACgQ,KAAK,IAAI;kBACnC;kBACA,IAAI,CAACA,KAAK,CAACC,OAAO,EAAE;oBAClB7U,OAAO,CAACsB,KAAK,CAAC,gBAAgB,EAAEsT,KAAK,CAAC;oBACtC;kBACF;kBAEA,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC;kBACxC;kBACA,MAAMC,SAAS,GAAGH,KAAK,CAACI,YAAY,GAClCC,oBAAoB,CAACL,KAAK,CAACI,YAAY,CAAC,GACxCE,iBAAiB,CAACL,OAAO,CAAC;;kBAE5B;kBACA,MAAMM,UAAU,GAAGP,KAAK,CAACQ,YAAY,IAAI,GAAG,CAAC,CAAC;kBAC9C,MAAMC,UAAU,GAAGC,QAAQ,CAACV,KAAK,CAACS,UAAU,CAAC,IAAI,CAAC;;kBAElD;;kBAEA;kBACA,MAAME,SAAS,GAAG;oBAChBV,OAAO;oBACPE,SAAS;oBACTK,YAAY,EAAED,UAAU;oBACxBE;kBACF,CAAC;;kBAED;kBACAV,UAAU,CAACa,IAAI,CAACD,SAAS,CAAC;;kBAE1B;kBACA;kBACA,IAAIE,eAAe,GAAGC,MAAM,CAACrL,OAAO,CAAC;kBACrC,IAAIsL,iBAAiB,GAAGtV,gBAAgB,CAACmC,GAAG,CAACiT,eAAe,CAAC;kBAE7D,IAAI,CAACE,iBAAiB,EAAE;oBACtB;oBACAF,eAAe,GAAGH,QAAQ,CAACjL,OAAO,CAAC;oBACnCsL,iBAAiB,GAAGtV,gBAAgB,CAACmC,GAAG,CAACiT,eAAe,CAAC;kBAC3D;kBAEA,IAAIE,iBAAiB,EAAE;oBACrB;oBACAC,wBAAwB,CAACD,iBAAiB,EAAEJ,SAAS,CAAC;;oBAEtD;oBACA,IAAIvL,oBAAoB,IAAIA,oBAAoB,CAACK,OAAO,KAAKA,OAAO,EAAE;sBACpEF,sBAAsB,CAAC0L,IAAI,KAAK;wBAC9B,GAAGA,IAAI;wBACPzL,OAAO,EAAE,IAAI;wBACbyK,OAAO;wBACPE,SAAS;wBACTvD,KAAK,EAAE2D,UAAU;wBACjBE;sBACF,CAAC,CAAC,CAAC;oBACL;kBACF,CAAC,MAAM;oBACL;kBAAA;gBAEJ,CAAC,CAAC;;gBAEF;gBACA,IAAIS,QAAQ,GAAG,IAAI;gBACnB;gBACA,MAAMC,KAAK,GAAGL,MAAM,CAACrL,OAAO,CAAC;gBAC7B,IAAIhK,gBAAgB,CAACiC,GAAG,CAACyT,KAAK,CAAC,EAAE;kBAC/BD,QAAQ,GAAGC,KAAK;gBAClB,CAAC,MAAM;kBACL;kBACA,MAAMC,KAAK,GAAGV,QAAQ,CAACjL,OAAO,CAAC;kBAC/B,IAAIhK,gBAAgB,CAACiC,GAAG,CAAC0T,KAAK,CAAC,EAAE;oBAC/BF,QAAQ,GAAGE,KAAK;kBAClB;gBACF;gBAEA,IAAIF,QAAQ,KAAK,IAAI,EAAE;kBACrB;kBACAxV,kBAAkB,CAAC+B,GAAG,CAACyT,QAAQ,EAAE;oBAC/BG,UAAU,EAAE/N,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtBoC,MAAM,EAAEoK;kBACV,CAAC,CAAC;kBACF3U,OAAO,CAACC,GAAG,CAAC,aAAa6V,QAAQ,KAAK,OAAOA,QAAQ,aAAa,CAAC;;kBAEnE;kBACA,IAAI1W,MAAM,CAACoL,mBAAmB,KAC1BpL,MAAM,CAACoL,mBAAmB,CAACmD,OAAO,KAAKmI,QAAQ,IAC/C1W,MAAM,CAACoL,mBAAmB,CAACmD,OAAO,KAAK+H,MAAM,CAACI,QAAQ,CAAC,IACvD1W,MAAM,CAACoL,mBAAmB,CAACmD,OAAO,KAAK2H,QAAQ,CAACQ,QAAQ,CAAC,CAAC,EAAE;oBAE9D9V,OAAO,CAACC,GAAG,CAAC,eAAe6V,QAAQ,aAAa,CAAC;oBACjD;oBACA1W,MAAM,CAACoL,mBAAmB,CAACmD,OAAO,GAAGmI,QAAQ;;oBAE7C;oBACA,IAAI1W,MAAM,CAACqL,0BAA0B,IAAI,CAACrL,MAAM,CAACqL,0BAA0B,CAACkD,OAAO,EAAE;sBACnF3N,OAAO,CAACC,GAAG,CAAC,SAAS6V,QAAQ,aAAa,CAAC;sBAC3CxF,UAAU,CAAC,MAAM;wBACflR,MAAM,CAACmR,qBAAqB,CAACuF,QAAQ,CAAC;sBACxC,CAAC,EAAE,GAAG,CAAC;oBACT;kBACF;gBACF,CAAC,MAAM;kBACL;kBACAxV,kBAAkB,CAAC+B,GAAG,CAACgI,OAAO,EAAE;oBAC9B4L,UAAU,EAAE/N,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtBoC,MAAM,EAAEoK;kBACV,CAAC,CAAC;kBACF;gBACF;cACF,CAAC,MAAM;gBACL3U,OAAO,CAACsB,KAAK,CAAC,eAAe,EAAEmO,YAAY,CAAC;cAC9C;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACLzP,OAAO,CAACsB,KAAK,CAAC,oCAAoC,EAAEoP,OAAO,CAAC;UAC9D;QACF,CAAC,CAAC,OAAOpP,KAAK,EAAE;UACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEA,KAAK,EAAEmP,OAAO,CAAC;QAC9C;QACA;MACF;;MAEA;MACA,IAAIjF,KAAK,KAAKtM,WAAW,CAACQ,GAAG,IAAIgR,OAAO,CAAC5E,IAAI,KAAK,KAAK,EAAE;QACvD;;QAEA;QACA1M,MAAM,CAACyT,WAAW,CAAC;UACjB/G,IAAI,EAAE,KAAK;UACXlL,IAAI,EAAE8P,OAAO,CAAC9P,IAAI;UAClBmQ,GAAG,EAAEL,OAAO,CAACK,GAAG;UAChBE,EAAE,EAAEP,OAAO,CAACO;QACd,CAAC,EAAE,GAAG,CAAC;QAEP,MAAMiF,OAAO,GAAGxF,OAAO,CAAC9P,IAAI;QAC5B,MAAMuV,KAAK,GAAGD,OAAO,CAACC,KAAK;QAC3B,MAAMC,MAAM,GAAGF,OAAO,CAACG,IAAI,IAAI,EAAE;QAEjCD,MAAM,CAACxR,OAAO,CAAC0R,KAAK,IAAI;UACtB,MAAMC,OAAO,GAAGD,KAAK,CAACE,KAAK;UAC3B,MAAMC,SAAS,GAAGH,KAAK,CAACG,SAAS;UACjC,MAAMrJ,WAAW,GAAGkJ,KAAK,CAAClJ,WAAW;UACrC,MAAMsJ,SAAS,GAAGJ,KAAK,CAACI,SAAS;UACjC,MAAMC,OAAO,GAAGL,KAAK,CAACK,OAAO;;UAE7B;UACA,MAAM9E,QAAQ,GAAGrK,SAAS,CAACmG,OAAO,CAACkC,YAAY,CAC7CC,UAAU,CAACoG,OAAO,CAACU,OAAO,CAAC,EAC3B9G,UAAU,CAACoG,OAAO,CAACW,MAAM,CAC3B,CAAC;;UAED;UACA,IAAIC,WAAW,GAAG,EAAE;UACpB,IAAIC,YAAY,GAAG,EAAE;UAErB,QAAON,SAAS;YACd,KAAK,KAAK;cAAG;cACXK,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,QAAQ;cACtBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,MAAM;cAAE;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF;cACED,WAAW,GAAG1J,WAAW,IAAI,MAAM;cACnC2J,YAAY,GAAG,SAAS;UAC5B;;UAEA;UACAC,iBAAiB,CAACnF,QAAQ,EAAEiF,WAAW,EAAEC,YAAY,CAAC;;UAEtD;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAIvL,KAAK,KAAKtM,WAAW,CAACT,KAAK,IAAIiS,OAAO,CAAC5E,IAAI,KAAK,OAAO,EAAE;QAC3D;;QAEA,MAAMmL,SAAS,GAAGvG,OAAO,CAAC9P,IAAI;QAC9B,MAAMsW,OAAO,GAAGD,SAAS,CAACC,OAAO;QACjC,MAAMC,SAAS,GAAGF,SAAS,CAACE,SAAS;QACrC,MAAMC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAMrO,QAAQ,GAAG;UACfN,QAAQ,EAAEqH,UAAU,CAACmH,SAAS,CAACrE,OAAO,CAAC;UACvCpK,SAAS,EAAEsH,UAAU,CAACmH,SAAS,CAACtE,QAAQ;QAC1C,CAAC;;QAED;QACA,MAAMd,QAAQ,GAAGrK,SAAS,CAACmG,OAAO,CAACkC,YAAY,CAAC9G,QAAQ,CAACP,SAAS,EAAEO,QAAQ,CAACN,QAAQ,CAAC;;QAEtF;QACA,QAAO0O,SAAS;UACd,KAAK,GAAG;YAAG;YACTH,iBAAiB,CAACnF,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;YAClD;UACF,KAAK,KAAK;YAAG;YACXmF,iBAAiB,CAACnF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACXmF,iBAAiB,CAACnF,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;YAC/C;UACF,KAAK,IAAI;YAAG;YACV,MAAMwF,UAAU,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAE;YAC1CN,iBAAiB,CAACnF,QAAQ,EAAE,KAAKwF,UAAU,MAAM,EAAE,SAAS,CAAC;YAC7D;UACF,KAAK,IAAI;YAAG;YACVL,iBAAiB,CAACnF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVmF,iBAAiB,CAACnF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YAAG;YACZmF,iBAAiB,CAACnF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVmF,iBAAiB,CAACnF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVmF,iBAAiB,CAACnF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX,MAAM0F,YAAY,GAAGN,SAAS,CAACK,UAAU,CAAC,CAAE;YAC5C,MAAME,QAAQ,GAAGP,SAAS,CAACQ,UAAU,CAAC,CAAM;YAC5CT,iBAAiB,CAACnF,QAAQ,EAAE,QAAQ6F,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,EAAE,SAAS,CAAC;YAC/F;QACJ;QAEA;MACF;MACA;MACA;MACA;MACA;MACA;MACA;IAEF,CAAC,CAAC,OAAOlW,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEmP,OAAO,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMkH,cAAc,GAAGA,CAAA,KAAM;IAC3B3X,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAM2X,KAAK,GAAG,QAAQ1Y,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnES,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE2X,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChB/X,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED4X,EAAE,CAACG,SAAS,GAAI1B,KAAK,IAAK;MACxB,IAAI;QACF,MAAM7F,OAAO,GAAGE,IAAI,CAACC,KAAK,CAAC0F,KAAK,CAAC1V,IAAI,CAAC;;QAEtC;QACA,IAAI6P,OAAO,CAAC3E,IAAI,KAAK,SAAS,EAAE;UAC9B9L,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEwQ,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAAC3E,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAI2E,OAAO,CAAC3E,IAAI,KAAK,SAAS,IAAI2E,OAAO,CAACjF,KAAK,IAAIiF,OAAO,CAACC,OAAO,EAAE;UAClE;UACA,IAAID,OAAO,CAACwH,SAAS,EAAE;YACrB,IAAIjR,mBAAmB,CAAC1E,GAAG,CAACmO,OAAO,CAACwH,SAAS,CAAC,EAAE;cAC9C;cACA;YACF;;YAEA;YACAjR,mBAAmB,CAAC5C,GAAG,CAACqM,OAAO,CAACwH,SAAS,CAAC;;YAE1C;YACA,IAAIjR,mBAAmB,CAACkR,IAAI,GAAG,IAAI,EAAE;cACnC;cACA,MAAMC,QAAQ,GAAGpX,KAAK,CAACqX,IAAI,CAACpR,mBAAmB,CAAC;cAChD,KAAK,IAAI2I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;gBAC5B3I,mBAAmB,CAAClC,MAAM,CAACqT,QAAQ,CAACxI,CAAC,CAAC,CAAC;cACzC;YACF;UACF;;UAEA;UACAa,iBAAiB,CAACC,OAAO,CAACjF,KAAK,EAAEmF,IAAI,CAAC0H,SAAS,CAAC5H,OAAO,CAACC,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOpP,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDuW,EAAE,CAACS,OAAO,GAAIhX,KAAK,IAAK;MACtBtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDuW,EAAE,CAACU,OAAO,GAAG,MAAM;MACjBvY,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACAqQ,UAAU,CAACqH,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACAhQ,aAAa,CAACgG,OAAO,GAAGkK,EAAE;EAC5B,CAAC;EAEDpb,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6K,YAAY,CAACqG,OAAO,EAAE;;IAE3B;IACA6K,aAAa,CAAC,CAAC;;IAEf;IACA/Z,KAAK,GAAG,IAAI5B,KAAK,CAAC4b,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAI7b,KAAK,CAAC8b,iBAAiB,CACxC,EAAE,EACFvZ,MAAM,CAACwZ,UAAU,GAAGxZ,MAAM,CAACyZ,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAAC3P,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCqW,MAAM,CAAC9J,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB7E,SAAS,CAAC4D,OAAO,GAAG+K,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAIjc,KAAK,CAACkc,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAAC7Z,MAAM,CAACwZ,UAAU,EAAExZ,MAAM,CAACyZ,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAAC/Z,MAAM,CAACga,gBAAgB,CAAC;IAC/C9R,YAAY,CAACqG,OAAO,CAAC0L,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAI1c,KAAK,CAAC2c,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5D/a,KAAK,CAAC2F,GAAG,CAACmV,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAI5c,KAAK,CAAC6c,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAAC1Q,QAAQ,CAAC1G,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1C5D,KAAK,CAAC2F,GAAG,CAACqV,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAI9c,KAAK,CAAC6c,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAAC5Q,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3C5D,KAAK,CAAC2F,GAAG,CAACuV,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAI/c,KAAK,CAACgd,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAAC7Q,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCuX,SAAS,CAACE,KAAK,GAAG5W,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7ByW,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACnX,QAAQ,GAAG,GAAG;IACxBhE,KAAK,CAAC2F,GAAG,CAACwV,SAAS,CAAC;;IAEpB;IACAxb,QAAQ,GAAG,IAAIrB,aAAa,CAAC2b,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzDlb,QAAQ,CAAC6b,aAAa,GAAG,IAAI;IAC7B7b,QAAQ,CAAC8b,aAAa,GAAG,IAAI;IAC7B9b,QAAQ,CAAC+b,kBAAkB,GAAG,KAAK;IACnC/b,QAAQ,CAAC0Q,WAAW,GAAG,EAAE;IACzB1Q,QAAQ,CAAC2Q,WAAW,GAAG,GAAG;IAC1B3Q,QAAQ,CAAC4Q,aAAa,GAAG9L,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC/E,QAAQ,CAAC6Q,aAAa,GAAG,CAAC;IAC1B7Q,QAAQ,CAACsQ,MAAM,CAACrM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BjE,QAAQ,CAACyQ,MAAM,CAAC,CAAC;;IAEjB;IACA7O,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnByY,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBta,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpB2L,SAAS,EAAE,CAAC,CAACA,SAAS,CAAC4D;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMyM,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAI1d,UAAU,CAAC,CAAC;QACtC0d,aAAa,CAACC,IAAI,CAChB,GAAG7a,QAAQ,uBAAuB,EACjC8a,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAACjc,KAAK;;UAE/B;UACA,MAAMmc,gBAAgB,GAAG,IAAI/d,KAAK,CAACge,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACtW,QAAQ,CAAE+O,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAAC5M,MAAM,EAAE;cAChB;cACA,IAAI4M,KAAK,CAACzM,QAAQ,EAAE;gBAClB;gBACA,MAAM0M,WAAW,GAAG,IAAIxW,KAAK,CAACie,oBAAoB,CAAC;kBACjD9P,KAAK,EAAE,QAAQ;kBAAO;kBACtB+P,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAI7H,KAAK,CAACzM,QAAQ,CAAClB,GAAG,EAAE;kBACtB4N,WAAW,CAAC5N,GAAG,GAAG2N,KAAK,CAACzM,QAAQ,CAAClB,GAAG;gBACtC;;gBAEA;gBACA2N,KAAK,CAACzM,QAAQ,GAAG0M,WAAW;gBAE5BrT,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEmT,KAAK,CAACnG,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAM0N,YAAY,CAACxO,QAAQ,CAAChH,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMiO,KAAK,GAAGuH,YAAY,CAACxO,QAAQ,CAAC,CAAC,CAAC;YACtCyO,gBAAgB,CAACxW,GAAG,CAACgP,KAAK,CAAC;UAC7B;;UAEA;UACA3U,KAAK,CAAC2F,GAAG,CAACwW,gBAAgB,CAAC;;UAE3B;UACAhd,gBAAgB,GAAGgd,gBAAgB;UAEnC5a,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9Bib,kBAAkB,CAAC,IAAI,CAAC;UACxBZ,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAO,GAAG,IAAK;UACPnb,OAAO,CAACC,GAAG,CAAC,aAAa,CAACkb,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEzY,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACD2X,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMe,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA;;QAEA;QACA3D,cAAc,CAAC,CAAC;;QAEhB;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAEF,CAAC,CAAC,OAAOrW,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAMia,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAIpB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMmB,WAAW,GAAIC,WAAW,IAAK;UACnC3b,OAAO,CAACC,GAAG,CAAC,WAAWub,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAI9e,UAAU,CAAC,CAAC;UAC/B8e,MAAM,CAACnB,IAAI,CACTe,GAAG,EACFd,IAAI,IAAK;YACR1a,OAAO,CAACC,GAAG,CAAC,WAAWub,GAAG,EAAE,CAAC;YAC7BlB,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAS,GAAG,IAAK;YACPnb,OAAO,CAACC,GAAG,CAAC,SAAS,CAACkb,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEzY,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAtB,KAAK,IAAK;YACTtB,OAAO,CAACsB,KAAK,CAAC,SAASka,GAAG,EAAE,EAAEla,KAAK,CAAC;YACpC,IAAIqa,WAAW,GAAG,CAAC,EAAE;cACnB3b,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3BqQ,UAAU,CAAC,MAAMoL,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACLpB,MAAM,CAACjZ,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAEDoa,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAI9e,UAAU,CAAC,CAAC;IAC/B8e,MAAM,CAACnB,IAAI,CACT,GAAG7a,QAAQ,4BAA4B,EACvC,MAAO8a,IAAI,IAAK;MACd,IAAI;QACF,MAAMvW,KAAK,GAAGuW,IAAI,CAACjc,KAAK;QACxB0F,KAAK,CAAC8N,KAAK,CAAC5P,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxB8B,KAAK,CAAC4E,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAE3B;QACA,IAAI5D,KAAK,EAAE;UACXA,KAAK,CAAC2F,GAAG,CAACD,KAAK,CAAC;;UAEhB;UACA,MAAMmX,eAAe,CAAC,CAAC;QACvB,CAAC,MAAM;UACLtb,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC;QAChC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACA6Z,GAAG,IAAK;MACPnb,OAAO,CAACC,GAAG,CAAC,SAAS,CAACkb,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEzY,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAtB,KAAK,IAAK;MACTtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtB,OAAO,CAACsB,KAAK,CAAC,OAAO,EAAE;QACrBua,IAAI,EAAEva,KAAK,CAACwK,IAAI;QAChBgQ,IAAI,EAAExa,KAAK,CAACmP,OAAO;QACnBsL,KAAK,EAAE,GAAGnc,QAAQ,4BAA4B;QAC9Coc,KAAK,EAAE,GAAGpc,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMqc,OAAO,GAAGA,CAAA,KAAM;MACpBrU,iBAAiB,CAAC+F,OAAO,GAAGuO,qBAAqB,CAACD,OAAO,CAAC;;MAE1D;MACAhf,KAAK,CAAC4R,MAAM,CAAC,CAAC;;MAEd;MACA,MAAMsN,SAAS,GAAG5b,KAAK,CAAC6b,QAAQ,CAAC,CAAC;;MAElC;MACA,IAAIhU,qBAAqB,CAAC8P,IAAI,GAAG,CAAC,EAAE;QAClC9P,qBAAqB,CAACxD,OAAO,CAAEV,KAAK,IAAK;UACvCA,KAAK,CAAC2K,MAAM,CAACsN,SAAS,CAAC;QACzB,CAAC,CAAC;MACJ;MAEA,IAAIhe,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACwP,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMyO,UAAU,GAAGze,gBAAgB,CAACmL,QAAQ,CAACjH,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMwa,eAAe,GAAG1e,gBAAgB,CAACoU,QAAQ,CAAC9P,CAAC;;QAEnD;QACA;QACA,MAAMqa,gBAAgB,GAAG,EAAED,eAAe,GAAGpZ,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;;QAEnE;QACA,MAAMqZ,YAAY,GAAG,IAAI3f,KAAK,CAACiG,OAAO,CACpC,CAAC,EAAE,GAAGI,IAAI,CAACuZ,GAAG,CAACF,gBAAgB,CAAC,EAChC,GAAG,EACH,CAAC,EAAE,GAAGrZ,IAAI,CAACwZ,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA,MAAMI,oBAAoB,GAAGN,UAAU,CAACva,KAAK,CAAC,CAAC,CAACsC,GAAG,CAACoY,YAAY,CAAC;QACjE,MAAMI,YAAY,GAAGP,UAAU,CAACva,KAAK,CAAC,CAAC;;QAEvC;QACA,IAAI,CAACgG,kBAAkB,CAAC6F,OAAO,EAAE;UAC/B7F,kBAAkB,CAAC6F,OAAO,GAAGgP,oBAAoB,CAAC7a,KAAK,CAAC,CAAC;QAC3D;QAEA,IAAI,CAACiG,gBAAgB,CAAC4F,OAAO,EAAE;UAC7B5F,gBAAgB,CAAC4F,OAAO,GAAGiP,YAAY,CAAC9a,KAAK,CAAC,CAAC;QACjD;;QAEA;QACAgG,kBAAkB,CAAC6F,OAAO,CAACkP,IAAI,CAACF,oBAAoB,EAAE,CAAC,GAAG3U,eAAe,CAAC;QAC1ED,gBAAgB,CAAC4F,OAAO,CAACkP,IAAI,CAACD,YAAY,EAAE,CAAC,GAAG5U,eAAe,CAAC;;QAEhE;QACA0Q,MAAM,CAAC3P,QAAQ,CAACyF,IAAI,CAAC1G,kBAAkB,CAAC6F,OAAO,CAAC;;QAEhD;QACA+K,MAAM,CAAC1K,EAAE,CAAC3L,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACAqW,MAAM,CAAC9J,MAAM,CAAC7G,gBAAgB,CAAC4F,OAAO,CAAC;;QAEvC;QACA+K,MAAM,CAACoE,sBAAsB,CAAC,CAAC;QAC/BpE,MAAM,CAACxJ,YAAY,CAAC,CAAC;QACrBwJ,MAAM,CAACvJ,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACA/Q,QAAQ,CAACwP,OAAO,GAAG,KAAK;;QAExB;QACAxP,QAAQ,CAACsQ,MAAM,CAACF,IAAI,CAACzG,gBAAgB,CAAC4F,OAAO,CAAC;QAC9CvP,QAAQ,CAACyQ,MAAM,CAAC,CAAC;QAEjB7O,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnB8c,IAAI,EAAEV,UAAU,CAAClM,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAEwI,MAAM,CAAC3P,QAAQ,CAACoH,OAAO,CAAC,CAAC;UAC/B6M,IAAI,EAAEjV,gBAAgB,CAAC4F,OAAO,CAACwC,OAAO,CAAC,CAAC;UACxC8M,IAAI,EAAEvE,MAAM,CAACwE,iBAAiB,CAAC,IAAIrgB,KAAK,CAACiG,OAAO,CAAC,CAAC,CAAC,CAACqN,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIhS,UAAU,KAAK,QAAQ,EAAE;QAClC;QACA2J,kBAAkB,CAAC6F,OAAO,GAAG,IAAI;QACjC5F,gBAAgB,CAAC4F,OAAO,GAAG,IAAI;;QAE/B;QACAvP,QAAQ,CAACwP,OAAO,GAAG,IAAI;;QAEvB;QACA8K,MAAM,CAAC1K,EAAE,CAAC3L,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAIa,IAAI,CAACK,GAAG,CAACmV,MAAM,CAAC3P,QAAQ,CAAC7G,CAAC,CAAC,GAAG,EAAE,EAAE;UACpCwW,MAAM,CAAC3P,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BjE,QAAQ,CAACsQ,MAAM,CAACrM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BqW,MAAM,CAAC9J,MAAM,CAACxQ,QAAQ,CAACsQ,MAAM,CAAC;UAC9BtQ,QAAQ,CAACyQ,MAAM,CAAC,CAAC;QACnB;;QAEA;QACA;QACA6J,MAAM,CAACxJ,YAAY,CAAC,CAAC;QACrBwJ,MAAM,CAACvJ,iBAAiB,CAAC,IAAI,CAAC;MAEhC,CAAC,MAAM,IAAIhR,UAAU,KAAK,cAAc,EAAE;QACxC;QACA2J,kBAAkB,CAAC6F,OAAO,GAAG,IAAI;QACjC5F,gBAAgB,CAAC4F,OAAO,GAAG,IAAI;;QAE/B;QACAvP,QAAQ,CAACyQ,MAAM,CAAC,CAAC;MACnB;MAEA,IAAIzQ,QAAQ,EAAEA,QAAQ,CAACyQ,MAAM,CAAC,CAAC;MAC/B,IAAIpQ,KAAK,IAAIia,MAAM,EAAE;QACnBI,QAAQ,CAACqE,MAAM,CAAC1e,KAAK,EAAEia,MAAM,CAAC;MAChC;IACF,CAAC;IAEDuD,OAAO,CAAC,CAAC;;IAET;IACA,MAAMmB,YAAY,GAAGA,CAAA,KAAM;MACzB1E,MAAM,CAAC2E,MAAM,GAAGje,MAAM,CAACwZ,UAAU,GAAGxZ,MAAM,CAACyZ,WAAW;MACtDH,MAAM,CAACoE,sBAAsB,CAAC,CAAC;MAC/BhE,QAAQ,CAACG,OAAO,CAAC7Z,MAAM,CAACwZ,UAAU,EAAExZ,MAAM,CAACyZ,WAAW,CAAC;IACzD,CAAC;IACDzZ,MAAM,CAACke,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACAhe,MAAM,CAACme,aAAa,GAAG,MAAM;MAC3B,IAAIxT,SAAS,CAAC4D,OAAO,EAAE;QACrB5D,SAAS,CAAC4D,OAAO,CAAC5E,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC0H,SAAS,CAAC4D,OAAO,CAACiB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC7E,SAAS,CAAC4D,OAAO,CAACuB,YAAY,CAAC,CAAC;QAChCnF,SAAS,CAAC4D,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAI/Q,QAAQ,EAAE;UACZA,QAAQ,CAACsQ,MAAM,CAACrM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BjE,QAAQ,CAACwP,OAAO,GAAG,IAAI;UACvBxP,QAAQ,CAACyQ,MAAM,CAAC,CAAC;QACnB;QAEA1Q,UAAU,GAAG,QAAQ;QACrB6B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAI2H,iBAAiB,CAAC+F,OAAO,EAAE;QAC7B6P,oBAAoB,CAAC5V,iBAAiB,CAAC+F,OAAO,CAAC;QAC/C/F,iBAAiB,CAAC+F,OAAO,GAAG,IAAI;MAClC;;MAEA;MACA1Q,KAAK,CAACwgB,SAAS,CAAC,CAAC;;MAEjB;MACArV,qBAAqB,CAACxD,OAAO,CAACV,KAAK,IAAI;QACrCN,eAAe,CAACe,WAAW,CAACT,KAAK,CAAC;MACpC,CAAC,CAAC;MACFkE,qBAAqB,CAACnC,KAAK,CAAC,CAAC;;MAE7B;MACArC,eAAe,CAACoC,OAAO,CAAC,CAAC;;MAEzB;MACA,IAAIvH,KAAK,EAAE;QACT,MAAMif,eAAe,GAAG,EAAE;QAC1Bjf,KAAK,CAAC4F,QAAQ,CAAEC,MAAM,IAAK;UACzB,IAAIA,MAAM,CAACkC,MAAM,EAAE;YACjB,IAAIlC,MAAM,CAACmC,QAAQ,EAAE;cACnBnC,MAAM,CAACmC,QAAQ,CAACC,OAAO,CAAC,CAAC;YAC3B;YACA,IAAIpC,MAAM,CAACqC,QAAQ,EAAE;cACnB,IAAI5F,KAAK,CAACC,OAAO,CAACsD,MAAM,CAACqC,QAAQ,CAAC,EAAE;gBAClCrC,MAAM,CAACqC,QAAQ,CAAC/B,OAAO,CAAC+B,QAAQ,IAAI;kBAClC,IAAIA,QAAQ,CAAClB,GAAG,EAAEkB,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;kBACxCC,QAAQ,CAACD,OAAO,CAAC,CAAC;gBACpB,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL,IAAIpC,MAAM,CAACqC,QAAQ,CAAClB,GAAG,EAAEnB,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;gBACtDpC,MAAM,CAACqC,QAAQ,CAACD,OAAO,CAAC,CAAC;cAC3B;YACF;YACA,IAAIpC,MAAM,KAAK7F,KAAK,EAAE;cACpBif,eAAe,CAAClI,IAAI,CAAClR,MAAM,CAAC;YAC9B;UACF;QACF,CAAC,CAAC;;QAEF;QACAoZ,eAAe,CAAC9Y,OAAO,CAAC+Y,GAAG,IAAI;UAC7B,IAAIA,GAAG,CAACxX,MAAM,EAAE;YACdwX,GAAG,CAACxX,MAAM,CAACC,MAAM,CAACuX,GAAG,CAAC;UACxB;QACF,CAAC,CAAC;QAEFlf,KAAK,CAACwH,KAAK,CAAC,CAAC;MACf;;MAEA;MACA,IAAI6S,QAAQ,EAAE;QACZA,QAAQ,CAAC8E,gBAAgB,CAAC,IAAI,CAAC;QAC/B,IAAItW,YAAY,CAACqG,OAAO,IAAImL,QAAQ,CAACQ,UAAU,IAAIR,QAAQ,CAACQ,UAAU,CAACuE,UAAU,KAAKvW,YAAY,CAACqG,OAAO,EAAE;UAC1GrG,YAAY,CAACqG,OAAO,CAACmQ,WAAW,CAAChF,QAAQ,CAACQ,UAAU,CAAC;QACvD;QACAR,QAAQ,CAACpS,OAAO,CAAC,CAAC;QAClBoS,QAAQ,CAACiF,gBAAgB,CAAC,CAAC;MAC7B;;MAEA;MACA,IAAI3f,QAAQ,EAAE;QACZA,QAAQ,CAACsI,OAAO,CAAC,CAAC;MACpB;;MAEA;MACA3H,oBAAoB,CAACkH,KAAK,CAAC,CAAC;MAC5BhH,oBAAoB,CAACgH,KAAK,CAAC,CAAC;MAC5B9F,gBAAgB,CAAC8F,KAAK,CAAC,CAAC;MACxB/F,aAAa,CAAC+F,KAAK,CAAC,CAAC;MACrB5F,gBAAgB,CAAC4F,KAAK,CAAC,CAAC;MACxB3F,kBAAkB,CAAC2F,KAAK,CAAC,CAAC;MAE1BjG,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxD,SAAS,CAAC,MAAM;IACd;IACAgE,qBAAqB,CAAC,CAAC;;IAEvB;IACA,MAAMud,uBAAuB,GAAGA,CAAA,KAAM;MACpChe,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCQ,qBAAqB,CAAC,CAAC;IACzB,CAAC;;IAED;IACArB,MAAM,CAACke,gBAAgB,CAAC,oBAAoB,EAAEU,uBAAuB,CAAC;;IAEtE;IACA,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnCzd,qBAAqB,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC;;IAET;IACA,OAAO,MAAM;MACXrB,MAAM,CAAC+e,mBAAmB,CAAC,oBAAoB,EAAEH,uBAAuB,CAAC;MACzEI,aAAa,CAACH,UAAU,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxhB,SAAS,CAAC,MAAM;IACd;IACA,IAAIgC,KAAK,IAAI+I,SAAS,CAACmG,OAAO,EAAE;MAC9B3N,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA,MAAMoe,KAAK,GAAG/N,UAAU,CAAC,MAAM;QAC7B,IAAI7R,KAAK,IAAI+I,SAAS,CAACmG,OAAO,EAAE;UAAG;UACjC2Q,mBAAmB,CAAC9W,SAAS,CAACmG,OAAO,CAAC;QACxC;MACF,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAM4Q,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM;MACLre,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC,EAAE,CAACxB,KAAK,CAAC,CAAC;;EAEX;EACAhC,SAAS,CAAC,MAAM;IACd,IAAI6K,YAAY,CAACqG,OAAO,EAAE;MACxB;MACA,MAAM6Q,WAAW,GAAIlI,KAAK,IAAK;QAC7B,IAAI7X,KAAK,IAAIsL,SAAS,CAAC4D,OAAO,EAAE;UAC9B8Q,gBAAgB,CAACnI,KAAK,EAAEhP,YAAY,CAACqG,OAAO,EAAElP,KAAK,EAAEsL,SAAS,CAAC4D,OAAO,CAAC;QACzE;MACF,CAAC;;MAED;MACArG,YAAY,CAACqG,OAAO,CAAC2P,gBAAgB,CAAC,OAAO,EAAEkB,WAAW,CAAC;;MAE3D;MACAxe,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,CAAC,CAACqH,YAAY,CAACqG,OAAO,CAAC;;MAEpD;MACA,OAAO,MAAM;QACX,IAAIrG,YAAY,CAACqG,OAAO,EAAE;UACxBrG,YAAY,CAACqG,OAAO,CAACwQ,mBAAmB,CAAC,OAAO,EAAEK,WAAW,CAAC;UAC9Dxe,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACxB,KAAK,EAAEsL,SAAS,CAAC4D,OAAO,CAAC,CAAC;;EAE9B;EACA,MAAM+Q,SAAS,GAAG9hB,WAAW,CAAC,MAAM;IAClCoD,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B;EACF,CAAC,EAAE,CAACqH,YAAY,EAAE8D,aAAa,EAAE/K,gBAAgB,CAAC,CAAC;;EAEnD;EACA,MAAMse,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMlY,QAAQ,GAAG,IAAI5J,KAAK,CAAC+hB,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChD,MAAMjY,QAAQ,GAAG,IAAI9J,KAAK,CAACgiB,iBAAiB,CAAC;MAAE7T,KAAK,EAAE;IAAS,CAAC,CAAC;IACjE,MAAM2K,iBAAiB,GAAG,IAAI9Y,KAAK,CAACiiB,IAAI,CAACrY,QAAQ,EAAEE,QAAQ,CAAC;;IAE5D;IACA,MAAMoY,YAAY,GAAG,IAAIliB,KAAK,CAACmiB,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC5D,MAAMC,YAAY,GAAG,IAAIpiB,KAAK,CAACgiB,iBAAiB,CAAC;MAAE7T,KAAK,EAAE;IAAS,CAAC,CAAC;IACrE,MAAMkU,SAAS,GAAG,IAAIriB,KAAK,CAACiiB,IAAI,CAACC,YAAY,EAAEE,YAAY,CAAC;IAC5DC,SAAS,CAACnW,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAClCsT,iBAAiB,CAACvR,GAAG,CAAC8a,SAAS,CAAC;IAEhC,OAAOvJ,iBAAiB;EAC1B,CAAC;;EAED;EACA,MAAMwJ,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAC1gB,KAAK,EAAE;;IAEZ;IACA4B,gBAAgB,CAACuE,OAAO,CAAC,CAACwa,QAAQ,EAAE/U,OAAO,KAAK;MAC9C,IAAI+U,QAAQ,CAACjb,KAAK,EAAE;QAClB;QACA,MAAMkb,cAAc,GAAG,IAAIxiB,KAAK,CAACyiB,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxD,MAAMC,cAAc,GAAG,IAAI1iB,KAAK,CAACgiB,iBAAiB,CAAC;UACjD7T,KAAK,EAAE,QAAQ;UAAC;UAChBwI,WAAW,EAAE,KAAK;UAClBU,OAAO,EAAE,GAAG;UAAG;UACfsL,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,MAAMC,UAAU,GAAG,IAAI5iB,KAAK,CAACiiB,IAAI,CAACO,cAAc,EAAEE,cAAc,CAAC;QACjEE,UAAU,CAAC1W,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE;;QAEnC;QACAod,UAAU,CAACC,QAAQ,GAAG;UACpB5T,IAAI,EAAE,cAAc;UACpBzB,OAAO,EAAEA,OAAO;UAChB4C,IAAI,EAAEmS,QAAQ,CAAC3P,YAAY,CAACxC,IAAI;UAChC0S,aAAa,EAAE;QACjB,CAAC;;QAED;QACAP,QAAQ,CAACjb,KAAK,CAACC,GAAG,CAACqb,UAAU,CAAC;QAE9Bzf,OAAO,CAACC,GAAG,CAAC,OAAOmf,QAAQ,CAAC3P,YAAY,CAACxC,IAAI,KAAK5C,OAAO,aAAa,CAAC;MACzE;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA5N,SAAS,CAAC,MAAM;IACd;IACA,MAAM4hB,KAAK,GAAG/N,UAAU,CAAC,MAAM;MAC7B,IAAIjQ,gBAAgB,CAAC6X,IAAI,GAAG,CAAC,EAAE;QAC7BlY,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1B;MACF;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAE;;IAEX,OAAO,MAAMse,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA5hB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACA,IAAIgO,0BAA0B,CAACkD,OAAO,EAAE;QACtCyQ,aAAa,CAAC3T,0BAA0B,CAACkD,OAAO,CAAC;QACjDlD,0BAA0B,CAACkD,OAAO,GAAG,IAAI;QACzC3N,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC9B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAxD,SAAS,CAAC,MAAM;IACd,IAAI,CAACyN,mBAAmB,CAACE,OAAO,IAAIK,0BAA0B,CAACkD,OAAO,EAAE;MACtEyQ,aAAa,CAAC3T,0BAA0B,CAACkD,OAAO,CAAC;MACjDlD,0BAA0B,CAACkD,OAAO,GAAG,IAAI;MACzCnD,mBAAmB,CAACmD,OAAO,GAAG,IAAI;MAClC3N,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACnC;EACF,CAAC,EAAE,CAACiK,mBAAmB,CAACE,OAAO,CAAC,CAAC;;EAEjC;EACA3N,SAAS,CAAC,MAAM;IACd;IACA,IAAIa,iBAAiB,IAAIA,iBAAiB,CAACoS,aAAa,IAAIpS,iBAAiB,CAACoS,aAAa,CAACvK,MAAM,GAAG,CAAC,EAAE;MACtG;MACA,IAAI,CAAC6E,oBAAoB,EAAE;QACzB;QACA,MAAM4V,6BAA6B,GAAGtiB,iBAAiB,CAACoS,aAAa,CAACxO,IAAI,CACxEuO,YAAY,IAAIA,YAAY,CAACY,eAAe,KAAK,KAAK,IAAIZ,YAAY,CAACpF,OACzE,CAAC;;QAED;QACA,MAAMwV,kBAAkB,GAAGD,6BAA6B,IAAItiB,iBAAiB,CAACoS,aAAa,CAAC,CAAC,CAAC;QAE9F1P,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE4f,kBAAkB,CAAC5S,IAAI,EAClC,QAAQ,EAAE2S,6BAA6B,GAAG,GAAG,GAAG,GAAG,CAAC;;QAEhE;QACA,MAAMvB,KAAK,GAAG/N,UAAU,CAAC,MAAM;UAC7Bf,wBAAwB,CAACsQ,kBAAkB,CAAC5S,IAAI,CAAC;QACnD,CAAC,EAAE,IAAI,CAAC;QAER,OAAO,MAAMsR,YAAY,CAACF,KAAK,CAAC;MAClC;IACF;EACF,CAAC,EAAE,CAAC/gB,iBAAiB,EAAE0M,oBAAoB,CAAC,CAAC;;EAE7C;EACA,MAAM8V,yBAAyB,GAAIC,iBAAiB,IAAK;IACrD,IAAI,CAACthB,KAAK,IAAI,OAAOA,KAAK,CAAC4F,QAAQ,KAAK,UAAU,IAAI,CAAC0b,iBAAiB,EAAE;IAC1E,IAAI;MACF;MACAthB,KAAK,CAAC0N,QAAQ,CACXvG,MAAM,CAAC+X,GAAG,IAAIA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAACM,qBAAqB,CAAC,CACjEpb,OAAO,CAAC+Y,GAAG,IAAIlf,KAAK,CAAC2H,MAAM,CAACuX,GAAG,CAAC,CAAC;MACpC;MACArgB,iBAAiB,CAACoS,aAAa,CAAC9K,OAAO,CAAC6K,YAAY,IAAI;QACtD,IAAI,CAACA,YAAY,CAACwQ,SAAS,IAAI,CAAClf,KAAK,CAACC,OAAO,CAACyO,YAAY,CAACwQ,SAAS,CAAC,EAAE;QACvE;QACAxQ,YAAY,CAACwQ,SAAS,CAACrb,OAAO,CAAEsb,QAAQ,IAAK;UAC3C;UACA,IAAI,CAACA,QAAQ,CAAC1X,SAAS,IAAI,CAAC0X,QAAQ,CAACzX,QAAQ,IAAI0X,KAAK,CAACrQ,UAAU,CAACoQ,QAAQ,CAAC1X,SAAS,CAAC,CAAC,IAAI2X,KAAK,CAACrQ,UAAU,CAACoQ,QAAQ,CAACzX,QAAQ,CAAC,CAAC,EAAE;YAC9H,OAAO,CAAC;UACV;;UAEA;UACA,MAAM2X,OAAO,GAAG7iB,WAAW,CAAC6iB,OAAO,CAACxa,MAAM,CACxCya,CAAC,IAAIA,CAAC,CAAChhB,QAAQ,KAAKoQ,YAAY,CAACxC,IAAI,IAAIoT,CAAC,CAACH,QAAQ,KAAKA,QAAQ,CAACjT,IACnE,CAAC;UACD,IAAImT,OAAO,CAACjb,MAAM,KAAK,CAAC,EAAE;UAC1B;UACAnF,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEwP,YAAY,CAACxC,IAAI,EAAE,IAAI,EAAEiT,QAAQ,CAACjT,IAAI,EAAE,MAAM,EAAEmT,OAAO,CAACjb,MAAM,CAAC;UACnF,MAAM0M,QAAQ,GAAGkO,iBAAiB,CAAClQ,YAAY,CAC7CC,UAAU,CAACoQ,QAAQ,CAAC1X,SAAS,CAAC,EAC9BsH,UAAU,CAACoQ,QAAQ,CAACzX,QAAQ,CAC9B,CAAC;UACD;UACA,MAAM6X,KAAK,GAAG,IAAIzjB,KAAK,CAACge,KAAK,CAAC,CAAC;UAC/ByF,KAAK,CAACvX,QAAQ,CAAC1G,GAAG,CAACwP,QAAQ,CAAC7P,CAAC,EAAE,EAAE,EAAE,CAAC6P,QAAQ,CAAC3P,CAAC,CAAC,CAAC,CAAC;UACjDoe,KAAK,CAACZ,QAAQ,GAAG;YAAEM,qBAAqB,EAAE;UAAK,CAAC;UAChD;UACA,MAAMO,QAAQ,GAAG,EAAE,CAAC,CAAC;UACrB,MAAMC,WAAW,GAAG,CAAC,CAAC,CAAC;UACvB,MAAMC,UAAU,GAAGL,OAAO,CAACjb,MAAM,GAAGob,QAAQ,GAAG,CAACH,OAAO,CAACjb,MAAM,GAAG,CAAC,IAAIqb,WAAW;UACjF;UACA,MAAME,MAAM,GAAG,GAAG,CAAC,CAAC;UACpB,MAAMC,SAAS,GAAG,GAAG,CAAC,CAAC;UACvB,MAAMC,MAAM,GAAG,EAAE,CAACR,OAAO,CAACjb,MAAM,GAAG,CAAC,KAAKub,MAAM,GAAGC,SAAS,CAAC,CAAC,GAAG,CAAC;UACjEP,OAAO,CAACxb,OAAO,CAAC,CAAC+G,MAAM,EAAEkV,GAAG,KAAK;YAC/B;YACA,MAAMC,aAAa,GAAG,IAAIjkB,KAAK,CAACkkB,aAAa,CAAC,CAAC;YAC/C,MAAMC,QAAQ,GAAG,GAAGphB,QAAQ,WAAW+L,MAAM,CAACG,IAAI,MAAM;YACxD;YACA,MAAMmV,YAAY,GAAG,IAAIpkB,KAAK,CAACgiB,iBAAiB,CAAC;cAC/CpZ,GAAG,EAAEqb,aAAa,CAACrG,IAAI,CAACuG,QAAQ,CAAC;cACjCxN,WAAW,EAAE,IAAI;cACjBU,OAAO,EAAE,CAAC,CAAC;YACb,CAAC,CAAC;YACF;YACA,MAAMgN,UAAU,GAAG,IAAIrkB,KAAK,CAACgiB,iBAAiB,CAAC;cAC7C7T,KAAK,EAAE,QAAQ;cACfwI,WAAW,EAAE,IAAI;cACjBU,OAAO,EAAE,GAAG,CAAC;YACf,CAAC,CAAC;YACF;YACA,MAAMiN,OAAO,GAAGT,MAAM,GAAG,IAAI;YAC7B,MAAMU,QAAQ,GAAGV,MAAM,GAAG,IAAI;YAC9B;YACA;YACA,MAAMW,UAAU,GAAG,IAAIxkB,KAAK,CAACykB,aAAa,CAACH,OAAO,EAAEC,QAAQ,CAAC;YAC7D,MAAMG,MAAM,GAAG,IAAI1kB,KAAK,CAACiiB,IAAI,CAACuC,UAAU,EAAEH,UAAU,CAAC;YACrD;YACA,MAAMM,YAAY,GAAG,IAAI3kB,KAAK,CAACykB,aAAa,CAACZ,MAAM,EAAEA,MAAM,CAAC;YAC5D,MAAMe,QAAQ,GAAG,IAAI5kB,KAAK,CAACiiB,IAAI,CAAC0C,YAAY,EAAEP,YAAY,CAAC;YAC3D;YACAQ,QAAQ,CAAC1Y,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;YACjC;YACA,MAAMqf,SAAS,GAAG,IAAI7kB,KAAK,CAACge,KAAK,CAAC,CAAC;YACnC6G,SAAS,CAACtd,GAAG,CAACmd,MAAM,CAAC;YACrBG,SAAS,CAACtd,GAAG,CAACqd,QAAQ,CAAC;YACvB;YACAC,SAAS,CAAC3Y,QAAQ,CAAC1G,GAAG,CAACue,MAAM,GAAGC,GAAG,IAAIH,MAAM,GAAGC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACjE;YACAe,SAAS,CAACzN,WAAW,GAAG,GAAG,CAAC,CAAC;YAC7ByN,SAAS,CAAChC,QAAQ,GAAG;cACnB7S,QAAQ,EAAElB,MAAM,CAACmB,EAAE;cACnB6U,UAAU,EAAEhW,MAAM,CAACG,IAAI;cACvBoU,QAAQ,EAAEA,QAAQ,CAACjT,IAAI;cACvB2U,oBAAoB,EAAE;YACxB,CAAC;YACDtB,KAAK,CAAClc,GAAG,CAACsd,SAAS,CAAC;UACtB,CAAC,CAAC;UACFjjB,KAAK,CAAC2F,GAAG,CAACkc,KAAK,CAAC;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOva,CAAC,EAAE;MACV/F,OAAO,CAAC6hB,IAAI,CAAC,sBAAsB,EAAE9b,CAAC,CAAC;MACvC;IACF;EACF,CAAC;;EAEH;EACAtJ,SAAS,CAAC,MAAM;IACd,IAAIgC,KAAK,IAAI,OAAOA,KAAK,CAAC4F,QAAQ,KAAK,UAAU,IAAImD,SAAS,CAACmG,OAAO,EAAE;MACtEmS,yBAAyB,CAACtY,SAAS,CAACmG,OAAO,CAAC;IAC9C;EACF,CAAC,EAAE,CAAClP,KAAK,EAAE+I,SAAS,CAACmG,OAAO,CAAC,CAAC;;EAE9B;EACAlR,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,KAAK,IAAI,CAACsL,SAAS,CAAC4D,OAAO,EAAE;IAClC,MAAMmU,gBAAgB,GAAGA,CAAA,KAAM;MAC7B;MACArjB,KAAK,CAAC0N,QAAQ,CAACvH,OAAO,CAAC+Y,GAAG,IAAI;QAC5B,IAAIA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAACM,qBAAqB,EAAE;UACtDrC,GAAG,CAAC/O,MAAM,CAAC7E,SAAS,CAAC4D,OAAO,CAAC5E,QAAQ,CAAC/G,CAAC,EAAE2b,GAAG,CAAC5U,QAAQ,CAAC7G,CAAC,EAAE6H,SAAS,CAAC4D,OAAO,CAAC5E,QAAQ,CAAC3G,CAAC,CAAC;QACxF;MACF,CAAC,CAAC;MACF8Z,qBAAqB,CAAC4F,gBAAgB,CAAC;IACzC,CAAC;IACDA,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACrjB,KAAK,CAAC,CAAC;EAEX,oBACEhB,OAAA,CAAAE,SAAA;IAAAwO,QAAA,gBACE1O,OAAA;MAAMsO,KAAK,EAAEjB,UAAW;MAAAqB,QAAA,EAAC;IAAK;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrClP,OAAA,CAACL,MAAM;MACL2O,KAAK,EAAEpB,uBAAwB;MAC/BoX,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAEzS,wBAAyB;MACnC0S,OAAO,EAAE3kB,iBAAiB,CAACoS,aAAa,CAACjK,GAAG,CAACgK,YAAY,KAAK;QAC5DD,KAAK,EAAEC,YAAY,CAACxC,IAAI;QACxBiV,KAAK,EAAEzS,YAAY,CAACxC;MACtB,CAAC,CAAC,CAAE;MACJiL,IAAI,EAAC,OAAO;MACZiK,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACbjZ,MAAM,EAAE,IAAI;QACZkZ,SAAS,EAAE;MACb,CAAE;MACF7S,KAAK,EAAExF,oBAAoB,GAAGA,oBAAoB,CAACiD,IAAI,GAAGsH;IAAU;MAAA/H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eACFlP,OAAA;MAAK6kB,GAAG,EAAEhb,YAAa;MAACyE,KAAK,EAAE;QAAElB,KAAK,EAAE,MAAM;QAAEmB,MAAM,EAAE;MAAO;IAAE;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGnEzC,mBAAmB,CAACE,OAAO,iBAC1B3M,OAAA;MACEsO,KAAK,EAAE;QACLhD,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAAC/G,CAAC,IAAI;QAC3C4I,GAAG,EAAE,GAAGV,mBAAmB,CAACnB,QAAQ,CAAC7G,CAAC,IAAI;QAC1CgH,SAAS,EAAE,wBAAwB;QACnCC,MAAM,EAAE,IAAI;QACZK,eAAe,EAAE,qBAAqB;QACtCwB,KAAK,EAAE,OAAO;QACdtB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,8BAA8B;QACzCN,OAAO,EAAE,GAAG;QACZgZ,QAAQ,EAAE,OAAO;QAAE;QACnB3Y,QAAQ,EAAE,MAAM,CAAC;MACnB,CAAE;MAAAuC,QAAA,GAEDjC,mBAAmB,CAACI,OAAO,eAC5B7M,OAAA;QACEsO,KAAK,EAAE;UACLhD,QAAQ,EAAE,UAAU;UACpB6B,GAAG,EAAE,KAAK;UACV4X,KAAK,EAAE,KAAK;UACZvW,UAAU,EAAE,MAAM;UAClBxC,MAAM,EAAE,MAAM;UACduB,KAAK,EAAE,OAAO;UACdpB,QAAQ,EAAE,MAAM;UAChBD,MAAM,EAAE,SAAS;UACjBJ,OAAO,EAAE;QACX,CAAE;QACFkZ,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAACvY,sBAAsB,CAAE;QAAAgC,QAAA,EAC3D;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,EAGAlB,aAAa,CAACrB,OAAO,IAAIqB,aAAa,CAACE,MAAM,iBAC5ClO,OAAA;MACEsO,KAAK,EAAE;QACLhD,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGwC,aAAa,CAAC1C,QAAQ,CAAC/G,CAAC,IAAI;QACrC4I,GAAG,EAAE,GAAGa,aAAa,CAAC1C,QAAQ,CAAC7G,CAAC,IAAI;QACpCgH,SAAS,EAAE,wBAAwB;QACnCC,MAAM,EAAE,IAAI;QACZ8C,UAAU,EAAE,kBAAkB;QAC9BjB,KAAK,EAAE,MAAM;QACbtB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,6BAA6B;QACxCN,OAAO,EAAE,GAAG;QACZoZ,QAAQ,EAAE,OAAO;QACjBJ,QAAQ,EAAE,OAAO;QACjB3Y,QAAQ,EAAE,MAAM;QAChBgZ,aAAa,EAAE;MACjB,CAAE;MAAAzW,QAAA,GAEDN,0BAA0B,CAACJ,aAAa,CAACE,MAAM,CAAC,eACjDlO,OAAA;QACEsO,KAAK,EAAE;UACLhD,QAAQ,EAAE,UAAU;UACpB6B,GAAG,EAAE,KAAK;UACV4X,KAAK,EAAE,KAAK;UACZvW,UAAU,EAAE,MAAM;UAClBxC,MAAM,EAAE,MAAM;UACduB,KAAK,EAAE,MAAM;UACbpB,QAAQ,EAAE,MAAM;UAChBD,MAAM,EAAE,SAAS;UACjBR,MAAM,EAAE;QACV,CAAE;QACFsZ,OAAO,EAAE7W,wBAAyB;QAAAO,QAAA,EACnC;MAAC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACN,eAEDlP,OAAA;MAAKsO,KAAK,EAAEjD,oBAAqB;MAAAqD,QAAA,gBAC/B1O,OAAA;QACEsO,KAAK,EAAE;UACL,GAAGzC,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF6Z,OAAO,EAAE/U,kBAAmB;QAAAvB,QAAA,EAC7B;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlP,OAAA;QACEsO,KAAK,EAAE;UACL,GAAGzC,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF6Z,OAAO,EAAE5U,kBAAmB;QAAA1B,QAAA,EAC7B;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAAtF,EAAA,CAphEMJ,WAAW;AAAA4b,EAAA,GAAX5b,WAAW;AAqhEjB,SAAS0M,gBAAgBA,CAACmP,IAAI,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAMC,MAAM,GAAG;IACbC,QAAQ,EAAEF,UAAU,CAACE,QAAQ,IAAI,OAAO;IACxCrZ,QAAQ,EAAEmZ,UAAU,CAACnZ,QAAQ,IAAI,EAAE;IAAE;IACrCqB,UAAU,EAAE8X,UAAU,CAAC9X,UAAU,IAAI,MAAM;IAC3CiY,eAAe,EAAEH,UAAU,CAACG,eAAe,IAAI,CAAC;IAChDC,WAAW,EAAEJ,UAAU,CAACI,WAAW,IAAI;MAAEtP,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAErO,CAAC,EAAE;IAAI,CAAC;IACnE8D,eAAe,EAAEuZ,UAAU,CAACvZ,eAAe,IAAI;MAAEqK,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAErO,CAAC,EAAE;IAAI,CAAC;IACjFsO,SAAS,EAAE+O,UAAU,CAAC/O,SAAS,IAAI;MAAEH,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAErO,CAAC,EAAE;IAAI,CAAC;IAC/D6D,OAAO,EAAEwZ,UAAU,CAACxZ,OAAO,IAAI;EACjC,CAAC;;EAED;EACA,MAAM6Z,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;EAEvC;EACA;;EAEA;EACA,MAAMC,SAAS,GAAGF,OAAO,CAACG,WAAW,CAACZ,IAAI,CAAC,CAACjY,KAAK;;EAEjD;EACA,MAAMA,KAAK,GAAG4Y,SAAS,GAAG,CAAC,GAAGT,MAAM,CAACzZ,OAAO,GAAG,CAAC,GAAGyZ,MAAM,CAACE,eAAe;EACzE,MAAMlX,MAAM,GAAGgX,MAAM,CAACpZ,QAAQ,GAAG,CAAC,GAAGoZ,MAAM,CAACzZ,OAAO,GAAG,CAAC,GAAGyZ,MAAM,CAACE,eAAe;EAEhFE,MAAM,CAACvY,KAAK,GAAGA,KAAK;EACpBuY,MAAM,CAACpX,MAAM,GAAGA,MAAM;;EAEtB;EACAuX,OAAO,CAACI,IAAI,GAAG,GAAGX,MAAM,CAAC/X,UAAU,IAAI+X,MAAM,CAACpZ,QAAQ,MAAMoZ,MAAM,CAACC,QAAQ,EAAE;EAC7EM,OAAO,CAACK,YAAY,GAAG,QAAQ;;EAE/B;EACA,MAAMC,MAAM,GAAG,CAAC;EAChBN,OAAO,CAACO,SAAS,CAAC,CAAC;EACnBP,OAAO,CAACQ,MAAM,CAACf,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,CAAC;EACvEK,OAAO,CAACS,MAAM,CAACnZ,KAAK,GAAGmY,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,CAAC;EAC/EK,OAAO,CAACU,KAAK,CAACpZ,KAAK,GAAGmY,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAErY,KAAK,GAAGmY,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEA,MAAM,CAAC;EAC9IN,OAAO,CAACS,MAAM,CAACnZ,KAAK,GAAGmY,MAAM,CAACE,eAAe,EAAElX,MAAM,GAAGgX,MAAM,CAACE,eAAe,GAAGW,MAAM,CAAC;EACxFN,OAAO,CAACU,KAAK,CAACpZ,KAAK,GAAGmY,MAAM,CAACE,eAAe,EAAElX,MAAM,GAAGgX,MAAM,CAACE,eAAe,EAAErY,KAAK,GAAGmY,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAE7X,MAAM,GAAGgX,MAAM,CAACE,eAAe,EAAEW,MAAM,CAAC;EAChKN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAE7X,MAAM,GAAGgX,MAAM,CAACE,eAAe,CAAC;EAChFK,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,EAAElX,MAAM,GAAGgX,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAElX,MAAM,GAAGgX,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEA,MAAM,CAAC;EAChJN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,CAAC;EACvEN,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,EAAEW,MAAM,CAAC;EAC9HN,OAAO,CAACW,SAAS,CAAC,CAAC;;EAEnB;EACAX,OAAO,CAACY,WAAW,GAAG,QAAQnB,MAAM,CAACG,WAAW,CAACtP,CAAC,KAAKmP,MAAM,CAACG,WAAW,CAACrP,CAAC,KAAKkP,MAAM,CAACG,WAAW,CAACpP,CAAC,KAAKiP,MAAM,CAACG,WAAW,CAACzd,CAAC,GAAG;EAChI6d,OAAO,CAACa,SAAS,GAAGpB,MAAM,CAACE,eAAe;EAC1CK,OAAO,CAACc,MAAM,CAAC,CAAC;;EAEhB;EACAd,OAAO,CAACe,SAAS,GAAG,QAAQtB,MAAM,CAACxZ,eAAe,CAACqK,CAAC,KAAKmP,MAAM,CAACxZ,eAAe,CAACsK,CAAC,KAAKkP,MAAM,CAACxZ,eAAe,CAACuK,CAAC,KAAKiP,MAAM,CAACxZ,eAAe,CAAC9D,CAAC,GAAG;EAC9I6d,OAAO,CAACgB,IAAI,CAAC,CAAC;;EAEd;EACAhB,OAAO,CAACe,SAAS,GAAG,QAAQtB,MAAM,CAAChP,SAAS,CAACH,CAAC,KAAKmP,MAAM,CAAChP,SAAS,CAACF,CAAC,KAAKkP,MAAM,CAAChP,SAAS,CAACD,CAAC,KAAKiP,MAAM,CAAChP,SAAS,CAACtO,CAAC,GAAG;EACtH6d,OAAO,CAACjX,SAAS,GAAG,QAAQ;;EAE5B;EACAiX,OAAO,CAACiB,QAAQ,CAAC1B,IAAI,EAAEjY,KAAK,GAAG,CAAC,EAAEmB,MAAM,GAAG,CAAC,CAAC;;EAE7C;EACA,MAAMyY,OAAO,GAAG,IAAI5nB,KAAK,CAAC6nB,aAAa,CAACtB,MAAM,CAAC;EAC/CqB,OAAO,CAACE,SAAS,GAAG9nB,KAAK,CAAC+nB,YAAY;EACtCH,OAAO,CAAChR,WAAW,GAAG,IAAI;;EAE1B;EACA,MAAMoR,cAAc,GAAG,IAAIhoB,KAAK,CAACioB,cAAc,CAAC;IAC9Crf,GAAG,EAAEgf,OAAO;IACZjR,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAMuR,MAAM,GAAG,IAAIloB,KAAK,CAACmoB,MAAM,CAACH,cAAc,CAAC;EAC/CE,MAAM,CAAC9S,KAAK,CAAC5P,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EAC7B0iB,MAAM,CAACpe,QAAQ,CAACse,SAAS,GAAG,KAAK,CAAC,CAAC;;EAEnC;EACAF,MAAM,CAACrF,QAAQ,GAAG;IAChBoD,IAAI,EAAEA,IAAI;IACVE,MAAM,EAAEA;EACV,CAAC;EAED,OAAO+B,MAAM;AACf;;AAIA;AACA3lB,MAAM,CAAC8lB,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMxM,MAAM,GAAG2K,QAAQ,CAAC8B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAI3M,MAAM,EAAE;MACV;MACA,MAAM4M,MAAM,GAAG5M,MAAM,CAAC3P,QAAQ,CAACjH,KAAK,CAAC,CAAC;;MAEtC;MACA4W,MAAM,CAAC3P,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BqW,MAAM,CAAC1K,EAAE,CAAC3L,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBqW,MAAM,CAAC9J,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACA8J,MAAM,CAACxJ,YAAY,CAAC,CAAC;MACrBwJ,MAAM,CAACvJ,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAM/Q,QAAQ,GAAGilB,QAAQ,CAAC8B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAInnB,QAAQ,EAAE;QACZA,QAAQ,CAACsQ,MAAM,CAACrM,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BjE,QAAQ,CAACyQ,MAAM,CAAC,CAAC;MACnB;MAEA7O,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBulB,GAAG,EAAEF,MAAM,CAACnV,OAAO,CAAC,CAAC;QACrBsV,GAAG,EAAE/M,MAAM,CAAC3P,QAAQ,CAACoH,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOpK,CAAC,EAAE;IACV/F,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEyE,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAGD;AACA,MAAMyS,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACFxY,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAM2b,MAAM,GAAG,IAAI9e,UAAU,CAAC,CAAC;;IAE/B;IACA,IAAI;MACF,MAAM,CAAE4oB,gBAAgB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,UAAU,CAAC,GAAG,MAAMxL,OAAO,CAACyL,GAAG,CAAC,CAClFlK,MAAM,CAACmK,SAAS,CAAC,GAAGnmB,QAAQ,4BAA4B,CAAC,EAC3Dgc,MAAM,CAACmK,SAAS,CAAC,GAAGnmB,QAAQ,uBAAuB,CAAC,EACpDgc,MAAM,CAACmK,SAAS,CAAC,GAAGnmB,QAAQ,uBAAuB,CAAC,EAClDgc,MAAM,CAACmK,SAAS,CAAC,GAAGnmB,QAAQ,sBAAsB,CAAC,CAEtD,CAAC;;MAIF;MACAI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB5B,qBAAqB,GAAGsnB,WAAW,CAAClnB,KAAK;MACzCJ,qBAAqB,CAACgG,QAAQ,CAAE+O,KAAK,IAAK;QACxC,IAAIA,KAAK,CAAC5M,MAAM,EAAE;UACd,MAAM6M,WAAW,GAAG,IAAIxW,KAAK,CAACie,oBAAoB,CAAC;YACnD9P,KAAK,EAAE,QAAQ;YAAG;YAClB+P,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,GAAG;YACdC,eAAe,EAAE;UACnB,CAAC,CAAC;;UAEA;UACA,IAAI7H,KAAK,CAACzM,QAAQ,CAAClB,GAAG,EAAE;YACtB4N,WAAW,CAAC5N,GAAG,GAAG2N,KAAK,CAACzM,QAAQ,CAAClB,GAAG;UACtC;UACA2N,KAAK,CAAC4S,OAAO,GAAG3S,WAAW;QAC/B;MACF,CAAC,CAAC;MAEFrT,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B;MACA3B,qBAAqB,GAAGsnB,WAAW,CAACnnB,KAAK;MACzC;MACAH,qBAAqB,CAAC2T,KAAK,CAAC5P,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxC;MACA/D,qBAAqB,CAAC+F,QAAQ,CAAE+O,KAAK,IAAK;QACxC,IAAIA,KAAK,CAAC5M,MAAM,IAAI4M,KAAK,CAACzM,QAAQ,EAAE;UAClC;UACAyM,KAAK,CAACzM,QAAQ,CAACoU,SAAS,GAAG,GAAG;UAC9B3H,KAAK,CAACzM,QAAQ,CAACqU,SAAS,GAAG,GAAG;UAC9B5H,KAAK,CAACzM,QAAQ,CAACsU,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;MAEFjb,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA1B,oBAAoB,GAAGsnB,UAAU,CAACpnB,KAAK;MACvC;MACA;MACA;MACAF,oBAAoB,CAAC8F,QAAQ,CAAE+O,KAAK,IAAK;QACvC,IAAIA,KAAK,CAAC5M,MAAM,IAAI4M,KAAK,CAACzM,QAAQ,EAAE;UAClC;UACAyM,KAAK,CAACzM,QAAQ,CAACoU,SAAS,GAAG,GAAG;UAC9B3H,KAAK,CAACzM,QAAQ,CAACqU,SAAS,GAAG,GAAG;UAC9B5H,KAAK,CAACzM,QAAQ,CAACsU,eAAe,GAAG,GAAG;QAEtC;QACA,IAAI7H,KAAK,CAAC5M,MAAM,EAAC;UACf4M,KAAK,CAAC6S,UAAU,GAAG,IAAI;QACzB;MACF,CAAC,CAAC;;MAIF;MACAjmB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE4lB,UAAU,CAAC3gB,UAAU,CAACC,MAAM,EAAE,GAAG,CAAC;MAC5D,IAAI0gB,UAAU,CAAC3gB,UAAU,IAAI2gB,UAAU,CAAC3gB,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7DnF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE4lB,UAAU,CAAC3gB,UAAU,CAACC,MAAM,EAAE,GAAG,CAAC;QACzDzG,eAAe,GAAGmnB,UAAU;MAC9B,CAAC,MAAM;QACL7lB,OAAO,CAAC6hB,IAAI,CAAC,cAAc,CAAC;MAC9B;MAEA7hB,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;;MAEzB;MACAzB,0BAA0B,GAAGknB,gBAAgB,CAACjnB,KAAK;MACnDuB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEzB,0BAA0B,CAAC;MACjD;MACAA,0BAA0B,CAACyT,KAAK,CAAC5P,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C;MACA7D,0BAA0B,CAAC6F,QAAQ,CAAE+O,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAAC5M,MAAM,IAAI4M,KAAK,CAACzM,QAAQ,EAAE;UAClC;UACAyM,KAAK,CAACzM,QAAQ,CAACoU,SAAS,GAAG,GAAG;UAC9B3H,KAAK,CAACzM,QAAQ,CAACqU,SAAS,GAAG,GAAG;UAC9B5H,KAAK,CAACzM,QAAQ,CAACsU,eAAe,GAAG,GAAG;QACxC;MACF,CAAC,CAAC;MAEAjb,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACA,IAAI;QACF,IAAI,CAACjD,qBAAqB,EAAE;UAC1B,MAAMsnB,WAAW,GAAG,MAAM/J,MAAM,CAACmK,SAAS,CAAC,GAAGnmB,QAAQ,uBAAuB,CAAC;UAC9EvB,qBAAqB,GAAGsnB,WAAW,CAAClnB,KAAK;QAC3C;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACF,CAAC,CAAC,OAAOynB,GAAG,EAAE;QACZlmB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAE4kB,GAAG,CAAC;MAClC;IACF;EACF,CAAC,CAAC,OAAO5kB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;;AAED;AACA,MAAMoW,mBAAmB,GAAI5L,IAAI,IAAK;EACpC,MAAMqa,KAAK,GAAG;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,KAAK,CAACra,IAAI,CAAC,IAAI,MAAM;AAC9B,CAAC;;AAED;AACA,MAAMkL,iBAAiB,GAAGA,CAACjO,QAAQ,EAAE+Z,IAAI,EAAE9X,KAAK,KAAK;EACnD;EACA,IAAI,CAACvM,KAAK,EAAE;IACVuB,OAAO,CAAC6hB,IAAI,CAAC,oBAAoB,CAAC;IAClC;EACF;EAEA,IAAI;IACJ;IACA,MAAMkD,MAAM,GAAGpR,gBAAgB,CAACmP,IAAI,CAAC;IACrCiC,MAAM,CAAChc,QAAQ,CAAC1G,GAAG,CAAC0G,QAAQ,CAAC/G,CAAC,EAAE,EAAE,EAAE,CAAC+G,QAAQ,CAAC7G,CAAC,CAAC,CAAC,CAAE;;IAEnD;IACAoO,UAAU,CAAC,MAAM;MACb;MACA,IAAI7R,KAAK,IAAIsmB,MAAM,CAAC5e,MAAM,EAAE;QAC9B1H,KAAK,CAAC2H,MAAM,CAAC2e,MAAM,CAAC;MAClB;IACJ,CAAC,EAAE,GAAG,CAAC;;IAEP;IACAtmB,KAAK,CAAC2F,GAAG,CAAC2gB,MAAM,CAAC;;IAEjB;IACA;IACA;IACA;IACA;EACA,CAAC,CAAC,OAAOzjB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;AACF,CAAC;;AAED;AACA,MAAMgd,mBAAmB,GAAIyB,iBAAiB,IAAK;EACjD,IAAI,CAACthB,KAAK,EAAE;IACVuB,OAAO,CAACsB,KAAK,CAAC,gBAAgB,CAAC;IAC/B;EACF;EAEA,IAAI,CAACye,iBAAiB,EAAE;IACtB/f,OAAO,CAACsB,KAAK,CAAC,mBAAmB,CAAC;IAClC;EACF;;EAEA;EACA,IAAI,CAAC9C,0BAA0B,EAAE;IAC/BwB,OAAO,CAACsB,KAAK,CAAC,oBAAoB,CAAC;IACnC;IACA,MAAMsa,MAAM,GAAG,IAAI9e,UAAU,CAAC,CAAC;IAC/B8e,MAAM,CAACmK,SAAS,CAAC,GAAGnmB,QAAQ,4BAA4B,CAAC,CACtDwmB,IAAI,CAACV,gBAAgB,IAAI;MACxBlnB,0BAA0B,GAAGknB,gBAAgB,CAACjnB,KAAK;MACnDD,0BAA0B,CAACyT,KAAK,CAAC5P,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C7D,0BAA0B,CAAC6F,QAAQ,CAAE+O,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAAC5M,MAAM,IAAI4M,KAAK,CAACzM,QAAQ,EAAE;UAClCyM,KAAK,CAACzM,QAAQ,CAACoU,SAAS,GAAG,GAAG;UAC9B3H,KAAK,CAACzM,QAAQ,CAACqU,SAAS,GAAG,GAAG;UAC9B5H,KAAK,CAACzM,QAAQ,CAACsU,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;MACFjb,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;MACAqe,mBAAmB,CAACyB,iBAAiB,CAAC;IACxC,CAAC,CAAC,CACDsG,KAAK,CAAC/kB,KAAK,IAAI;MACdtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC;MACAglB,2BAA2B,CAACvG,iBAAiB,CAAC;IAChD,CAAC,CAAC;IACJ;EACF;;EAEA;EACA1f,gBAAgB,CAACuE,OAAO,CAAEwa,QAAQ,IAAK;IACrC,IAAI3gB,KAAK,IAAI2gB,QAAQ,CAACjb,KAAK,EAAE;MAC3B1F,KAAK,CAAC2H,MAAM,CAACgZ,QAAQ,CAACjb,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACF9D,gBAAgB,CAAC4F,KAAK,CAAC,CAAC;;EAExB;EACA3I,iBAAiB,CAACoS,aAAa,CAAC9K,OAAO,CAAC6K,YAAY,IAAI;IACtD,IAAIA,YAAY,CAACY,eAAe,KAAK,KAAK,EAAE;MAC1CrQ,OAAO,CAACC,GAAG,CAAC,UAAUwP,YAAY,CAACxC,IAAI,kBAAkB,CAAC;MAC1D;IACF;IAEA,IAAIwC,YAAY,CAAChH,QAAQ,IAAIgH,YAAY,CAACjH,SAAS,IAAIiH,YAAY,CAACpF,OAAO,EAAE;MAC3E,MAAMwH,QAAQ,GAAGkO,iBAAiB,CAAClQ,YAAY,CAC7CC,UAAU,CAACL,YAAY,CAACjH,SAAS,CAAC,EAClCsH,UAAU,CAACL,YAAY,CAAChH,QAAQ,CAClC,CAAC;MAEDzI,OAAO,CAACC,GAAG,CAAC,SAASwP,YAAY,CAACxC,IAAI,KAAKwC,YAAY,CAACpF,OAAO,iBAAiBwH,QAAQ,CAAC7P,CAAC,KAAK6P,QAAQ,CAAC3P,CAAC,GAAG,CAAC;MAE7G,IAAI;QACF;QACA,IAAI,CAAC1D,0BAA0B,IAAI,CAACA,0BAA0B,CAACsD,KAAK,EAAE;UACpE,MAAM,IAAIykB,KAAK,CAAC,cAAc,CAAC;QACjC;;QAEA;QACA,MAAM5Q,iBAAiB,GAAGnX,0BAA0B,CAACsD,KAAK,CAAC,CAAC;;QAE5D;QACA6T,iBAAiB,CAAC1I,IAAI,GAAG,OAAOwC,YAAY,CAACxC,IAAI,EAAE;;QAEnD;QACA0I,iBAAiB,CAAC5M,QAAQ,CAAC1G,GAAG,CAACwP,QAAQ,CAAC7P,CAAC,EAAE,EAAE,EAAE,CAAC6P,QAAQ,CAAC3P,CAAC,CAAC;;QAE3D;QACAyT,iBAAiB,CAAC1D,KAAK,CAAC5P,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;QAEvC;QACAsT,iBAAiB,CAAC1B,WAAW,GAAG,GAAG;;QAEnC;QACA0B,iBAAiB,CAACtR,QAAQ,CAAC+O,KAAK,IAAI;UAClC,IAAIA,KAAK,CAAC5M,MAAM,EAAE;YAChB4M,KAAK,CAACzM,QAAQ,CAAC6M,WAAW,GAAG,KAAK;YAClCJ,KAAK,CAACzM,QAAQ,CAACuN,OAAO,GAAG,GAAG;YAC5Bd,KAAK,CAACzM,QAAQ,CAAC6f,IAAI,GAAG3pB,KAAK,CAAC4pB,UAAU;YACtCrT,KAAK,CAACzM,QAAQ,CAAC6Y,UAAU,GAAG,IAAI;YAChCpM,KAAK,CAACzM,QAAQ,CAACse,SAAS,GAAG,IAAI;YAC/B7R,KAAK,CAACzM,QAAQ,CAAC8M,WAAW,GAAG,IAAI;YACjCL,KAAK,CAACa,WAAW,GAAG,GAAG;UACzB;QACF,CAAC,CAAC;;QAEF;QACA0B,iBAAiB,CAAC+J,QAAQ,GAAG;UAC3B5T,IAAI,EAAE,cAAc;UACpBzB,OAAO,EAAEoF,YAAY,CAACpF,OAAO;UAC7B4C,IAAI,EAAEwC,YAAY,CAACxC;QACrB,CAAC;;QAED;QACA;;QAEA;QACA;QACA,MAAMyZ,oBAAoB,GAAG,IAAI7pB,KAAK,CAACkkB,aAAa,CAAC,CAAC;QACtD,MAAM4F,eAAe,GAAG,GAAG/mB,QAAQ,qBAAqB,CAAC,CAAC;QAC1D,MAAMgnB,eAAe,GAAG,IAAI/pB,KAAK,CAACgiB,iBAAiB,CAAC;UAClDpZ,GAAG,EAAEihB,oBAAoB,CAACjM,IAAI,CAACkM,eAAe,CAAC;UAC/CnT,WAAW,EAAE,IAAI;UACjBU,OAAO,EAAE;QACX,CAAC,CAAC;QACF;QACA,MAAM2S,eAAe,GAAG,IAAIhqB,KAAK,CAACykB,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;QACrD,MAAMwF,WAAW,GAAG,IAAIjqB,KAAK,CAACiiB,IAAI,CAAC+H,eAAe,EAAED,eAAe,CAAC;QACpE;QACAE,WAAW,CAAC/d,QAAQ,CAAC1G,GAAG,CAACwP,QAAQ,CAAC7P,CAAC,GAAC,EAAE,EAAE,GAAG,EAAE,EAAE6P,QAAQ,CAAC3P,CAAC,GAAC,EAAE,CAAC,CAAC;QAC9D;QACA4kB,WAAW,CAAC9U,QAAQ,CAAChQ,CAAC,GAAG,CAACkB,IAAI,CAACC,EAAE,GAAG,CAAC;QACrC;QACA2jB,WAAW,CAAC7S,WAAW,GAAG,GAAG;QAC7B;QACA6S,WAAW,CAACpH,QAAQ,GAAG;UACrB5T,IAAI,EAAE,aAAa;UACnBzB,OAAO,EAAEoF,YAAY,CAACpF,OAAO;UAC7B4C,IAAI,EAAEwC,YAAY,CAACxC;QACrB,CAAC;QACD;QACAxO,KAAK,CAAC2F,GAAG,CAAC0iB,WAAW,CAAC;QACtB;;QAEA;QACAzmB,gBAAgB,CAACgC,GAAG,CAACoN,YAAY,CAACpF,OAAO,EAAE;UACzClG,KAAK,EAAEwR,iBAAiB;UACxBlG,YAAY,EAAEA,YAAY;UAC1B1G,QAAQ,EAAE8I;QACZ,CAAC,CAAC;QAEF7R,OAAO,CAACC,GAAG,CAAC,SAASwP,YAAY,CAACxC,IAAI,KAAKwC,YAAY,CAACpF,OAAO,kBAAkBwH,QAAQ,CAAC7P,CAAC,KAAK,CAAC6P,QAAQ,CAAC3P,CAAC,GAAG,CAAC;MACjH,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,QAAQmO,YAAY,CAACxC,IAAI,YAAY,EAAE3L,KAAK,CAAC;QAC3D;QACA;MACF;IACF;EACF,CAAC,CAAC;;EAEF;EACAtB,OAAO,CAACC,GAAG,CAAC,OAAOI,gBAAgB,CAAC6X,IAAI,SAAS,CAAC;EAClD7X,gBAAgB,CAACuE,OAAO,CAAC,CAACwa,QAAQ,EAAE/U,OAAO,KAAK;IAC9CrK,OAAO,CAACC,GAAG,CAAC,QAAQoK,OAAO,KAAK+U,QAAQ,CAAC3P,YAAY,CAACxC,IAAI,EAAE,CAAC;EAC/D,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMqZ,2BAA2B,GAAIvG,iBAAiB,IAAK;EACzDziB,iBAAiB,CAACoS,aAAa,CAAC9K,OAAO,CAAC6K,YAAY,IAAI;IACtD;IACA,IAAIA,YAAY,CAACY,eAAe,KAAK,KAAK,EAAE;MAC1C;IACF;IAEA,IAAIZ,YAAY,CAAChH,QAAQ,IAAIgH,YAAY,CAACjH,SAAS,IAAIiH,YAAY,CAACpF,OAAO,EAAE;MAC3E;MACA,MAAMwH,QAAQ,GAAGkO,iBAAiB,CAAClQ,YAAY,CAC7CC,UAAU,CAACL,YAAY,CAACjH,SAAS,CAAC,EAClCsH,UAAU,CAACL,YAAY,CAAChH,QAAQ,CAClC,CAAC;MAEDkW,wBAAwB,CAAClP,YAAY,EAAEoC,QAAQ,EAAEkO,iBAAiB,CAAC;IACrE;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMpB,wBAAwB,GAAGA,CAAClP,YAAY,EAAEoC,QAAQ,EAAEkO,iBAAiB,KAAK;EAC9E;EACA,MAAMtZ,QAAQ,GAAG,IAAI5J,KAAK,CAAC+hB,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAClD,MAAMjY,QAAQ,GAAG,IAAI9J,KAAK,CAACgiB,iBAAiB,CAAC;IAC3C7T,KAAK,EAAE,QAAQ;IACfwI,WAAW,EAAE,KAAK;IAClBU,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAMyB,iBAAiB,GAAG,IAAI9Y,KAAK,CAACiiB,IAAI,CAACrY,QAAQ,EAAEE,QAAQ,CAAC;;EAE5D;EACAgP,iBAAiB,CAAC1I,IAAI,GAAG,SAASwC,YAAY,CAACxC,IAAI,EAAE;;EAErD;EACA0I,iBAAiB,CAAC5M,QAAQ,CAAC1G,GAAG,CAACwP,QAAQ,CAAC7P,CAAC,EAAE,EAAE,EAAE,CAAC6P,QAAQ,CAAC3P,CAAC,CAAC;;EAE3D;EACAyT,iBAAiB,CAAC1B,WAAW,GAAG,GAAG;;EAEnC;EACA0B,iBAAiB,CAAC+J,QAAQ,GAAG;IAC3B5T,IAAI,EAAE,cAAc;IACpBzB,OAAO,EAAEoF,YAAY,CAACpF,OAAO;IAC7B4C,IAAI,EAAEwC,YAAY,CAACxC;EACrB,CAAC;;EAED;EACA,MAAM8Z,gBAAgB,GAAG,IAAIlqB,KAAK,CAACyiB,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5D,MAAM0H,gBAAgB,GAAG,IAAInqB,KAAK,CAACgiB,iBAAiB,CAAC;IACnD7T,KAAK,EAAE,QAAQ;IACfwI,WAAW,EAAE,IAAI;IACjBU,OAAO,EAAE,GAAG;IAAG;IACfsL,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMyH,QAAQ,GAAG,IAAIpqB,KAAK,CAACiiB,IAAI,CAACiI,gBAAgB,EAAEC,gBAAgB,CAAC;EACnEC,QAAQ,CAACha,IAAI,GAAG,YAAYwC,YAAY,CAACxC,IAAI,EAAE;EAC/Cga,QAAQ,CAACvH,QAAQ,GAAG;IAClB5T,IAAI,EAAE,cAAc;IACpBzB,OAAO,EAAEoF,YAAY,CAACpF,OAAO;IAC7B4C,IAAI,EAAEwC,YAAY,CAACxC,IAAI;IACvBia,UAAU,EAAE;EACd,CAAC;EAEDvR,iBAAiB,CAACvR,GAAG,CAAC6iB,QAAQ,CAAC;;EAE/B;EACAxoB,KAAK,CAAC2F,GAAG,CAACuR,iBAAiB,CAAC;;EAE5B;EACAtV,gBAAgB,CAACgC,GAAG,CAACoN,YAAY,CAACpF,OAAO,EAAE;IACzClG,KAAK,EAAEwR,iBAAiB;IACxBlG,YAAY,EAAEA,YAAY;IAC1B1G,QAAQ,EAAE8I;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMsV,aAAa,GAAG,IAAItqB,KAAK,CAACyiB,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAM8H,aAAa,GAAG,IAAIvqB,KAAK,CAACgiB,iBAAiB,CAAC;IAAE7T,KAAK,EAAE;EAAS,CAAC,CAAC;EACtE,MAAMqc,SAAS,GAAG,IAAIxqB,KAAK,CAACiiB,IAAI,CAACqI,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAACte,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;EAEhC;EACAglB,SAAS,CAAC3H,QAAQ,GAAG;IACnB5T,IAAI,EAAE,cAAc;IACpBzB,OAAO,EAAEoF,YAAY,CAACpF,OAAO;IAC7B4C,IAAI,EAAEwC,YAAY,CAACxC;EACrB,CAAC;EAED0I,iBAAiB,CAACvR,GAAG,CAACijB,SAAS,CAAC;EAEhCrnB,OAAO,CAACC,GAAG,CAAC,SAASwP,YAAY,CAACxC,IAAI,KAAKwC,YAAY,CAACpF,OAAO,kBAAkBwH,QAAQ,CAAC7P,CAAC,KAAK,CAAC6P,QAAQ,CAAC3P,CAAC,GAAG,CAAC;AACjH,CAAC;;AAED;AACA,MAAMgT,iBAAiB,GAAIL,OAAO,IAAK;EACrC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMI,oBAAoB,GAAIqS,OAAO,IAAK;EACxC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAM7I,gBAAgB,GAAGA,CAACnI,KAAK,EAAEiR,SAAS,EAAEC,aAAa,EAAEC,cAAc,KAAK;EAC5E,IAAI,CAACF,SAAS,IAAI,CAACC,aAAa,IAAI,CAACC,cAAc,EAAE;EAErDznB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEqW,KAAK,CAACoR,OAAO,EAAEpR,KAAK,CAACqR,OAAO,CAAC;;EAEvD;EACA,MAAMC,IAAI,GAAGL,SAAS,CAACM,qBAAqB,CAAC,CAAC;EAC9C,MAAMC,MAAM,GAAI,CAACxR,KAAK,CAACoR,OAAO,GAAGE,IAAI,CAAC3e,IAAI,IAAIse,SAAS,CAACQ,WAAW,GAAI,CAAC,GAAG,CAAC;EAC5E,MAAMC,MAAM,GAAG,EAAE,CAAC1R,KAAK,CAACqR,OAAO,GAAGC,IAAI,CAAChd,GAAG,IAAI2c,SAAS,CAACU,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;;EAE7E;EACA,MAAMC,SAAS,GAAG,IAAIrrB,KAAK,CAACsrB,SAAS,CAAC,CAAC;EACvCD,SAAS,CAAClF,MAAM,CAACoF,MAAM,CAACC,SAAS,GAAG,CAAC;EACrCH,SAAS,CAAClF,MAAM,CAACsF,IAAI,CAACD,SAAS,GAAG,CAAC;EAEnC,MAAME,WAAW,GAAG,IAAI1rB,KAAK,CAAC2rB,OAAO,CAACV,MAAM,EAAEE,MAAM,CAAC;EACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,EAAEd,cAAc,CAAC;EAEpDznB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE6nB,MAAM,EAAEE,MAAM,CAAC;;EAEtC;EACA,MAAMU,mBAAmB,GAAG,EAAE;EAE9BroB,gBAAgB,CAACuE,OAAO,CAAC,CAACwa,QAAQ,EAAE/U,OAAO,KAAK;IAC9C,IAAI+U,QAAQ,CAACjb,KAAK,EAAE;MAClB;MACAukB,mBAAmB,CAAClT,IAAI,CAAC4J,QAAQ,CAACjb,KAAK,CAAC;MACxC;MACAib,QAAQ,CAACjb,KAAK,CAACiG,OAAO,GAAG,IAAI;MAC7BgV,QAAQ,CAACjb,KAAK,CAAC8P,WAAW,GAAG,IAAI,CAAC,CAAC;MACnC;MACAmL,QAAQ,CAACjb,KAAK,CAACE,QAAQ,CAAC+O,KAAK,IAAI;QAC/BA,KAAK,CAAChJ,OAAO,GAAG,IAAI;QACpBgJ,KAAK,CAACa,WAAW,GAAG,IAAI;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEFjU,OAAO,CAACC,GAAG,CAAC,QAAQyoB,mBAAmB,CAACvjB,MAAM,gBAAgB,CAAC;;EAE/D;EACA,MAAMwjB,sBAAsB,GAAGT,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,EAAE,IAAI,CAAC;EAEpF,IAAIC,sBAAsB,CAACxjB,MAAM,GAAG,CAAC,EAAE;IACrCnF,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE0oB,sBAAsB,CAACxjB,MAAM,CAAC;IACxDwjB,sBAAsB,CAAC/jB,OAAO,CAAC,CAACikB,SAAS,EAAEC,KAAK,KAAK;MACnD9oB,OAAO,CAACC,GAAG,CAAC,QAAQ6oB,KAAK,GAAG,EACjBD,SAAS,CAACvkB,MAAM,CAAC2I,IAAI,IAAI,KAAK,EAC9B,KAAK,EAAE4b,SAAS,CAACpmB,QAAQ,EACzB,WAAW,EAAEomB,SAAS,CAACvkB,MAAM,CAACob,QAAQ,CAAC;IACpD,CAAC,CAAC;;IAEF;IACA,MAAM/B,GAAG,GAAGoL,yBAAyB,CAACJ,sBAAsB,CAAC,CAAC,CAAC,CAACrkB,MAAM,CAAC;IAEvE,IAAIqZ,GAAG,IAAIA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAAC5T,IAAI,KAAK,cAAc,EAAE;MAC/D;MACA,MAAMzB,OAAO,GAAGsT,GAAG,CAAC+B,QAAQ,CAACrV,OAAO;MACpCrK,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoK,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;;MAEhE;MACA,IAAI2e,SAAS,GAAG3e,OAAO;MACvB,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,CAAChK,gBAAgB,CAACiC,GAAG,CAAC+H,OAAO,CAAC,IAAIhK,gBAAgB,CAACiC,GAAG,CAACgT,QAAQ,CAACjL,OAAO,CAAC,CAAC,EAAE;QAC5G2e,SAAS,GAAG1T,QAAQ,CAACjL,OAAO,CAAC;QAC7BrK,OAAO,CAACC,GAAG,CAAC,cAAc+oB,SAAS,EAAE,CAAC;MACxC,CAAC,MAAM,IAAI,OAAO3e,OAAO,KAAK,QAAQ,IAAI,CAAChK,gBAAgB,CAACiC,GAAG,CAAC+H,OAAO,CAAC,IAAIhK,gBAAgB,CAACiC,GAAG,CAACoT,MAAM,CAACrL,OAAO,CAAC,CAAC,EAAE;QACjH2e,SAAS,GAAGtT,MAAM,CAACrL,OAAO,CAAC;QAC3BrK,OAAO,CAACC,GAAG,CAAC,eAAe+oB,SAAS,EAAE,CAAC;MACzC;;MAEA;MACA5pB,MAAM,CAACmR,qBAAqB,CAACyY,SAAS,CAAC;MACvC;IACF;EACF;;EAEA;EACA,MAAMC,UAAU,GAAGf,SAAS,CAACU,gBAAgB,CAACpB,aAAa,CAACrb,QAAQ,EAAE,IAAI,CAAC;EAE3EnM,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgpB,UAAU,CAAC9jB,MAAM,CAAC;EAE7C,IAAI8jB,UAAU,CAAC9jB,MAAM,GAAG,CAAC,EAAE;IACzB;IACA8jB,UAAU,CAACrkB,OAAO,CAAC,CAACikB,SAAS,EAAEC,KAAK,KAAK;MACvC,MAAMnL,GAAG,GAAGkL,SAAS,CAACvkB,MAAM;MAC5BtE,OAAO,CAACC,GAAG,CAAC,UAAU6oB,KAAK,GAAG,EAAEnL,GAAG,CAAC1Q,IAAI,IAAI,KAAK,EACrC,WAAW,EAAE0Q,GAAG,CAAC+B,QAAQ,EACzB,KAAK,EAAEmJ,SAAS,CAACpmB,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACA,KAAK,IAAIkN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsZ,UAAU,CAAC9jB,MAAM,EAAEwK,CAAC,EAAE,EAAE;MAC1C,MAAMgO,GAAG,GAAGoL,yBAAyB,CAACE,UAAU,CAACtZ,CAAC,CAAC,CAACrL,MAAM,CAAC;MAC3D,IAAIqZ,GAAG,IAAIA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAAC5T,IAAI,KAAK,cAAc,EAAE;QAC/D,MAAMzB,OAAO,GAAGsT,GAAG,CAAC+B,QAAQ,CAACrV,OAAO;QACpCjL,MAAM,CAACmR,qBAAqB,CAAClG,OAAO,CAAC;QACrC;MACF;IACF;EACF;;EAEA;EACArK,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;;EAE9B;EACA,IAAIipB,YAAY,GAAG,IAAI;EACvB,IAAIpa,WAAW,GAAG,GAAG,CAAC,CAAC;;EAEvBzO,gBAAgB,CAACuE,OAAO,CAAC,CAACwa,QAAQ,EAAE/U,OAAO,KAAK;IAC9C,IAAI+U,QAAQ,CAACjb,KAAK,EAAE;MAClB,MAAMglB,QAAQ,GAAG,IAAItsB,KAAK,CAACiG,OAAO,CAAC,CAAC;MACpC;MACAsc,QAAQ,CAACjb,KAAK,CAACilB,gBAAgB,CAACD,QAAQ,CAAC;;MAEzC;MACA,MAAME,SAAS,GAAGF,QAAQ,CAACrnB,KAAK,CAAC,CAAC;MAClCunB,SAAS,CAACC,OAAO,CAAC7B,cAAc,CAAC;;MAEjC;MACA,MAAM8B,EAAE,GAAGF,SAAS,CAACrnB,CAAC,GAAG8lB,MAAM;MAC/B,MAAM0B,EAAE,GAAGH,SAAS,CAACnnB,CAAC,GAAG8lB,MAAM;MAC/B,MAAMvlB,QAAQ,GAAGS,IAAI,CAACumB,IAAI,CAACF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;MAE7CxpB,OAAO,CAACC,GAAG,CAAC,MAAMoK,OAAO,OAAO,EAAE5H,QAAQ,CAAC;MAE3C,IAAIA,QAAQ,GAAGqM,WAAW,EAAE;QAC1BA,WAAW,GAAGrM,QAAQ;QACtBymB,YAAY,GAAG;UAAE7e,OAAO;UAAE5H;QAAS,CAAC;MACtC;IACF;EACF,CAAC,CAAC;EAEF,IAAIymB,YAAY,EAAE;IAChBlpB,OAAO,CAACC,GAAG,CAAC,oBAAoBipB,YAAY,CAAC7e,OAAO,SAAS6e,YAAY,CAACzmB,QAAQ,EAAE,CAAC;;IAErF;IACArD,MAAM,CAACmR,qBAAqB,CAAC2Y,YAAY,CAAC7e,OAAO,CAAC;IAClD;EACF;EAEArK,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;AAC5B,CAAC;;AAED;AACA,MAAMyiB,kBAAkB,GAAIgH,eAAe,IAAK;EAC9C;EACA,IAAItqB,MAAM,CAACqL,0BAA0B,IAAIrL,MAAM,CAACqL,0BAA0B,CAACkD,OAAO,EAAE;IAClFyQ,aAAa,CAAChf,MAAM,CAACqL,0BAA0B,CAACkD,OAAO,CAAC;IACxDvO,MAAM,CAACqL,0BAA0B,CAACkD,OAAO,GAAG,IAAI;IAChD3N,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B;;EAEA;EACA,IAAIb,MAAM,CAACoL,mBAAmB,EAAE;IAC9BpL,MAAM,CAACoL,mBAAmB,CAACmD,OAAO,GAAG,IAAI;EAC3C;;EAEA;EACA+b,eAAe,CAAC7T,IAAI,KAAK;IACvB,GAAGA,IAAI;IACPzL,OAAO,EAAE,KAAK;IACdE,OAAO,EAAE,IAAI;IAAE;IACfC,MAAM,EAAE,EAAE,CAAK;EACjB,CAAC,CAAC,CAAC;EAEHvK,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;AAChC,CAAC;;AAED;AACAb,MAAM,CAACuqB,qBAAqB,GAAItf,OAAO,IAAK;EAC1C,IAAI;IAAA,IAAAuf,qBAAA,EAAAC,sBAAA;IACF;IACA,MAAMzU,YAAY,GAAG/U,gBAAgB,CAACmC,GAAG,CAAC6H,OAAO,IAAI,GAAG,CAAC;IACzD,IAAI,CAAC+K,YAAY,EAAE;MACjBpV,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAE+I,OAAO,CAAC;;MAEtC;MACArK,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxBI,gBAAgB,CAACuE,OAAO,CAAC,CAACklB,KAAK,EAAEhd,EAAE,KAAK;QACtC9M,OAAO,CAACC,GAAG,CAAC,KAAK6M,EAAE,KAAKgd,KAAK,CAACra,YAAY,CAACxC,IAAI,EAAE,CAAC;MACpD,CAAC,CAAC;MAEF,OAAO,KAAK;IACd;;IAEA;IACA,MAAM8c,UAAU,GAAG3U,YAAY,CAACjR,KAAK;;IAErC;IACA,MAAM6lB,SAAS,GAAG1pB,kBAAkB,CAACkC,GAAG,CAAC6H,OAAO,CAAC;IACjD,MAAMoF,YAAY,GAAG2F,YAAY,CAAC3F,YAAY;;IAE9C;IACA,IAAInF,OAAO;IAEX,IAAI0f,SAAS,IAAIA,SAAS,CAACzf,MAAM,EAAE;MACjCD,OAAO,gBACL7M,OAAA;QAAKsO,KAAK,EAAE;UAAExC,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAEwX,SAAS,EAAE,OAAO;UAAE4H,SAAS,EAAE;QAAO,CAAE;QAAA9d,QAAA,gBACpF1O,OAAA;UAAKsO,KAAK,EAAE;YACVd,UAAU,EAAE,MAAM;YAClBqC,YAAY,EAAE,KAAK;YACnB1D,QAAQ,EAAE,MAAM;YAChBsgB,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAAhe,QAAA,GACCsD,YAAY,CAACxC,IAAI,EAAC,QAAM,EAAC5C,OAAO,EAAC,GACpC;QAAA;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNlP,OAAA;UAAA0O,QAAA,EACG6d,SAAS,CAACzf,MAAM,CAAC9E,GAAG,CAAC,CAACmP,KAAK,EAAEkU,KAAK,KAAK;YACtC,IAAIsB,UAAU;YACd,QAAQxV,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBAAEgV,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;gBAAEA,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;cAAE;gBAASA,UAAU,GAAG,SAAS;gBAAE;YAC7C;YAEA,oBACE3sB,OAAA;cAAiBsO,KAAK,EAAE;gBACtBuB,YAAY,EAAE,KAAK;gBACnB9D,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE,KAAK;gBACnBE,QAAQ,EAAE;cACZ,CAAE;cAAAuC,QAAA,gBACA1O,OAAA;gBAAKsO,KAAK,EAAE;kBAAEd,UAAU,EAAE;gBAAO,CAAE;gBAAAkB,QAAA,EAChC+I,iBAAiB,CAACN,KAAK,CAACC,OAAO;cAAC;gBAAArI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNlP,OAAA;gBAAKsO,KAAK,EAAE;kBAAE3C,OAAO,EAAE,MAAM;kBAAEihB,cAAc,EAAE;gBAAgB,CAAE;gBAAAle,QAAA,gBAC/D1O,OAAA;kBAAA0O,QAAA,EAAM;gBAAI;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBlP,OAAA;kBAAMsO,KAAK,EAAE;oBACXf,KAAK,EAAEof,UAAU;oBACjBnf,UAAU,EAAE,MAAM;oBAClBzB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAAyC,QAAA,EACCyI,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGR,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAG;gBAAI;kBAAA5I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNlP,OAAA;gBAAKsO,KAAK,EAAE;kBAAE3C,OAAO,EAAE,MAAM;kBAAEihB,cAAc,EAAE;gBAAgB,CAAE;gBAAAle,QAAA,gBAC/D1O,OAAA;kBAAA0O,QAAA,EAAM;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBlP,OAAA;kBAAMsO,KAAK,EAAE;oBAAEd,UAAU,EAAE;kBAAO,CAAE;kBAAAkB,QAAA,GAAEyI,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAA7I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAzBEmc,KAAK;cAAAtc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNlP,OAAA;UAAKsO,KAAK,EAAE;YAAEiB,SAAS,EAAE,KAAK;YAAEpD,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE;UAAO,CAAE;UAAAmB,QAAA,GAAC,4BAC3D,EAAC,IAAIjE,IAAI,CAAC,CAAC,CAACoiB,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAA9d,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACLrC,OAAO,gBACL7M,OAAA;QAAKsO,KAAK,EAAE;UAAExC,OAAO,EAAE,KAAK;UAAEgZ,QAAQ,EAAE;QAAQ,CAAE;QAAApW,QAAA,gBAChD1O,OAAA;UAAKsO,KAAK,EAAE;YAAEd,UAAU,EAAE,MAAM;YAAEqC,YAAY,EAAE;UAAM,CAAE;UAAAnB,QAAA,EAAEsD,YAAY,CAACxC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClFlP,OAAA;UAAA0O,QAAA,GAAK,kBAAM,EAAC9B,OAAO;QAAA;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1BlP,OAAA;UAAA0O,QAAA,EAAK;QAAU;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN;IACH;;IAEA;IACA,MAAM4d,OAAO,GAAGnrB,MAAM,CAACwZ,UAAU,GAAG,CAAC,GAAE,GAAG;IAC1C,MAAM4R,OAAO,GAAGprB,MAAM,CAACyZ,WAAW,GAAG,CAAC,GAAE,GAAG;;IAE3C;IACA,MAAM6Q,eAAe,IAAAE,qBAAA,GAAGvG,QAAQ,CAAC8B,aAAa,CAAC,OAAO,CAAC,cAAAyE,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCa,gBAAgB,cAAAZ,sBAAA,uBAAjDA,sBAAA,CAAmD1f,sBAAsB;IAEjG,IAAIuf,eAAe,EAAE;MACnB;MACAA,eAAe,CAAC;QACdtf,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBtB,QAAQ,EAAE;UAAE/G,CAAC,EAAEuoB,OAAO;UAAEroB,CAAC,EAAEsoB;QAAQ,CAAC;QACpClgB,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAAyf,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEzf,MAAM,KAAI;MAC/B,CAAC,CAAC;MAEFvK,OAAO,CAACC,GAAG,CAAC,SAASwP,YAAY,CAACxC,IAAI,KAAK5C,OAAO,UAAU,CAAC;MAC7D,OAAO,IAAI;IACb,CAAC,MAAM;MACL;MACA,MAAMqgB,OAAO,GAAGrH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CoH,OAAO,CAAC3e,KAAK,CAAChD,QAAQ,GAAG,UAAU;MACnC2hB,OAAO,CAAC3e,KAAK,CAAC9C,IAAI,GAAG,GAAGshB,OAAO,IAAI;MACnCG,OAAO,CAAC3e,KAAK,CAACnB,GAAG,GAAG,GAAG4f,OAAO,IAAI;MAClCE,OAAO,CAAC3e,KAAK,CAAC7C,SAAS,GAAG,wBAAwB;MAClDwhB,OAAO,CAAC3e,KAAK,CAAC5C,MAAM,GAAG,MAAM;MAC7BuhB,OAAO,CAAC3e,KAAK,CAACvC,eAAe,GAAG,qBAAqB;MACrDkhB,OAAO,CAAC3e,KAAK,CAACf,KAAK,GAAG,OAAO;MAC7B0f,OAAO,CAAC3e,KAAK,CAACrC,YAAY,GAAG,KAAK;MAClCghB,OAAO,CAAC3e,KAAK,CAAClC,SAAS,GAAG,8BAA8B;MACxD6gB,OAAO,CAAC3e,KAAK,CAACxC,OAAO,GAAG,KAAK;MAC7BmhB,OAAO,CAAC3e,KAAK,CAACwW,QAAQ,GAAG,OAAO;MAChCmI,OAAO,CAAC3e,KAAK,CAACnC,QAAQ,GAAG,MAAM;MAE/B8gB,OAAO,CAACC,SAAS,GAAG;AAC1B;AACA,YAAYlb,YAAY,CAACxC,IAAI,SAAS5C,OAAO;AAC7C;AACA;AACA,qBAAqBA,OAAO;AAC5B,eAAe2f,SAAS,GAAG,UAAU,GAAG,YAAY;AACpD;AACA;AACA,OAAO;MAED3G,QAAQ,CAACuH,IAAI,CAACvR,WAAW,CAACqR,OAAO,CAAC;;MAElC;MACA,MAAMG,WAAW,GAAGH,OAAO,CAACvF,aAAa,CAAC,QAAQ,CAAC;MACnD,IAAI0F,WAAW,EAAE;QACfA,WAAW,CAACvN,gBAAgB,CAAC,OAAO,EAAE,MAAM;UAC1C+F,QAAQ,CAACuH,IAAI,CAAC9M,WAAW,CAAC4M,OAAO,CAAC;QACpC,CAAC,CAAC;MACJ;MAEA1qB,OAAO,CAACC,GAAG,CAAC,gBAAgBwP,YAAY,CAACxC,IAAI,KAAK5C,OAAO,OAAO,CAAC;MACjE,OAAO,IAAI;IACb;EACF,CAAC,CAAC,OAAO/I,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACAlC,MAAM,CAAC0rB,iBAAiB,GAAG,MAAM;EAC/B9qB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;EAErB,IAAI,CAACI,gBAAgB,IAAIA,gBAAgB,CAAC6X,IAAI,KAAK,CAAC,EAAE;IACpDlY,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,OAAO,EAAE;EACX;EAEA,MAAM8qB,IAAI,GAAG,EAAE;EACf1qB,gBAAgB,CAACuE,OAAO,CAAC,CAACklB,KAAK,EAAEhd,EAAE,KAAK;IACtC9M,OAAO,CAACC,GAAG,CAAC,SAAS6M,EAAE,SAASgd,KAAK,CAACra,YAAY,CAACxC,IAAI,EAAE,CAAC;IAC1D8d,IAAI,CAACvV,IAAI,CAAC;MACR1I,EAAE;MACFG,IAAI,EAAE6c,KAAK,CAACra,YAAY,CAACxC,IAAI;MAC7BlE,QAAQ,EAAE+gB,KAAK,CAAC/gB;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOgiB,IAAI;AACb,CAAC;;AAGD;AACA3rB,MAAM,CAACmR,qBAAqB,GAAIlG,OAAO,IAAK;EAC1C,IAAI;IACF;IACAA,OAAO,GAAGqL,MAAM,CAACrL,OAAO,CAAC;IAEzBrK,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEoK,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;IAC/ErK,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,gBAAgB,CAAC6X,IAAI,CAAC;;IAE3D;IACA,IAAI9C,YAAY,GAAG/U,gBAAgB,CAACmC,GAAG,CAAC6H,OAAO,CAAC;IAChD,IAAI,CAAC+K,YAAY,EAAE;MACjB;MACA,MAAM4V,SAAS,GAAG1V,QAAQ,CAACjL,OAAO,CAAC;MACnC+K,YAAY,GAAG/U,gBAAgB,CAACmC,GAAG,CAACwoB,SAAS,CAAC;MAE9C,IAAI5V,YAAY,EAAE;QAChBpV,OAAO,CAACC,GAAG,CAAC,UAAU+qB,SAAS,SAAS,CAAC;QACzC3gB,OAAO,GAAG2gB,SAAS,CAAC,CAAC;MACvB;IACF;IAEA,IAAI,CAAC5V,YAAY,EAAE;MACjBpV,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAE+I,OAAO,CAAC;MACtC,OAAO,KAAK;IACd;IAEA,MAAM2f,SAAS,GAAG1pB,kBAAkB,CAACkC,GAAG,CAAC6H,OAAO,CAAC;IACjD,MAAMoF,YAAY,GAAG2F,YAAY,CAAC3F,YAAY;;IAE9C;IACA,MAAMwb,YAAY,GAAGjB,SAAS,IAAIA,SAAS,CAACzf,MAAM,IAAIyf,SAAS,CAACzf,MAAM,CAACpF,MAAM,GAAG,CAAC;IAEjF,IAAImF,OAAO;;IAEX;IACA,MAAM4gB,YAAY,GAAG;MACnBniB,QAAQ,EAAE,UAAU;MACpB6B,GAAG,EAAE,KAAK;MACV4X,KAAK,EAAE,MAAM;MACb3X,KAAK,EAAE,MAAM;MACbmB,MAAM,EAAE,MAAM;MACd5C,OAAO,EAAE,MAAM;MACfihB,cAAc,EAAE,QAAQ;MACxBc,UAAU,EAAE,QAAQ;MACpBzhB,YAAY,EAAE,KAAK;MACnBuC,UAAU,EAAE,iBAAiB;MAC7B9C,MAAM,EAAE;IACV,CAAC;;IAED;IACA,MAAMiiB,WAAW,GAAGA,CAAA,kBAClB3tB,OAAA;MAAKsO,KAAK,EAAEmf,YAAa;MAAA/e,QAAA,eACvB1O,OAAA;QAAKsO,KAAK,EAAE;UACV3C,OAAO,EAAE,MAAM;UACfiiB,aAAa,EAAE,QAAQ;UACvBF,UAAU,EAAE,QAAQ;UACpBjiB,SAAS,EAAE;QACb,CAAE;QAAAiD,QAAA,gBACA1O,OAAA;UAAMsO,KAAK,EAAE;YACXf,KAAK,EAAE,SAAS;YAChBpB,QAAQ,EAAE,MAAM;YAChBqB,UAAU,EAAE,MAAM;YAClBF,UAAU,EAAE;UACd,CAAE;UAAAoB,QAAA,EAAC;QAAC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACXlP,OAAA;UAAMsO,KAAK,EAAE;YACXlB,KAAK,EAAE,CAAC;YACRmB,MAAM,EAAE,CAAC;YACTsf,UAAU,EAAE,uBAAuB;YACnCC,WAAW,EAAE,uBAAuB;YACpCrB,YAAY,EAAE,oBAAoB;YAClCld,SAAS,EAAE;UACb;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;IAED,IAAIse,YAAY,EAAE;MAChB;MACA,MAAMO,QAAQ,GAAG;QACf,GAAG,EAAE;UAAEC,GAAG,EAAE,GAAG;UAAE3f,IAAI,EAAE;QAAO,CAAC;QAAE,GAAG,EAAE;UAAE2f,GAAG,EAAE,GAAG;UAAE3f,IAAI,EAAE;QAAW,CAAC;QAAE,GAAG,EAAE;UAAE2f,GAAG,EAAE,GAAG;UAAE3f,IAAI,EAAE;QAAQ,CAAC;QACtG,GAAG,EAAE;UAAE2f,GAAG,EAAE,GAAG;UAAE3f,IAAI,EAAE;QAAO,CAAC;QAAE,GAAG,EAAE;UAAE2f,GAAG,EAAE,GAAG;UAAE3f,IAAI,EAAE;QAAW,CAAC;QAAE,GAAG,EAAE;UAAE2f,GAAG,EAAE,GAAG;UAAE3f,IAAI,EAAE;QAAQ,CAAC;QACtG,GAAG,EAAE;UAAE2f,GAAG,EAAE,GAAG;UAAE3f,IAAI,EAAE;QAAO,CAAC;QAAE,IAAI,EAAE;UAAE2f,GAAG,EAAE,GAAG;UAAE3f,IAAI,EAAE;QAAW,CAAC;QAAE,IAAI,EAAE;UAAE2f,GAAG,EAAE,GAAG;UAAE3f,IAAI,EAAE;QAAQ,CAAC;QACxG,IAAI,EAAE;UAAE2f,GAAG,EAAE,GAAG;UAAE3f,IAAI,EAAE;QAAO,CAAC;QAAE,IAAI,EAAE;UAAE2f,GAAG,EAAE,GAAG;UAAE3f,IAAI,EAAE;QAAW,CAAC;QAAE,IAAI,EAAE;UAAE2f,GAAG,EAAE,GAAG;UAAE3f,IAAI,EAAE;QAAQ;MAC1G,CAAC;MAED,MAAM4f,SAAS,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC;MAC/C,MAAMC,QAAQ,GAAG;QAAEC,CAAC,EAAE,SAAS;QAAEC,CAAC,EAAE,SAAS;QAAEC,CAAC,EAAE;MAAU,CAAC;MAC7D,MAAMC,OAAO,GAAG;QAAEC,CAAC,EAAE,CAAC,CAAC;QAAEC,CAAC,EAAE,CAAC,CAAC;QAAEC,CAAC,EAAE,CAAC,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAE,CAAC;MAE9CnC,SAAS,CAACzf,MAAM,CAAC3F,OAAO,CAACgQ,KAAK,IAAI;QAChC,MAAMnP,GAAG,GAAG+lB,QAAQ,CAAC5W,KAAK,CAACC,OAAO,CAAC;QACnC,IAAIpP,GAAG,EAAE;UACPsmB,OAAO,CAACtmB,GAAG,CAACgmB,GAAG,CAAC,CAAChmB,GAAG,CAACqG,IAAI,CAAC,GAAG;YAC3Bd,KAAK,EAAE2gB,QAAQ,CAAC/W,KAAK,CAACQ,YAAY,CAAC,IAAI,MAAM;YAC7CC,UAAU,EAAET,KAAK,CAACS;UACpB,CAAC;QACH;MACF,CAAC,CAAC;MAEF/K,OAAO,gBACL7M,OAAA;QAAKsO,KAAK,EAAE;UAAExC,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAEoB,UAAU,EAAE,kBAAkB;UAAElD,QAAQ,EAAE;QAAW,CAAE;QAAAoD,QAAA,gBACnG1O,OAAA,CAAC2tB,WAAW;UAAA5e,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACflP,OAAA;UAAKsO,KAAK,EAAE;YAAEd,UAAU,EAAE,MAAM;YAAEqC,YAAY,EAAE,KAAK;YAAE1D,QAAQ,EAAE,MAAM;YAAE0C,SAAS,EAAE;UAAS,CAAE;UAAAH,QAAA,GAAEsD,YAAY,CAACxC,IAAI,EAAC,cAAE;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3HlP,OAAA;UAAKsO,KAAK,EAAE;YACV3C,OAAO,EAAE,MAAM;YACfgjB,gBAAgB,EAAE,gBAAgB;YAClCC,mBAAmB,EAAE,gBAAgB;YACrChC,cAAc,EAAE,QAAQ;YACxBc,UAAU,EAAE,QAAQ;YACpBlf,UAAU,EAAE,wBAAwB;YACpCvC,YAAY,EAAE,KAAK;YACnB4iB,MAAM,EAAE,QAAQ;YAChBvjB,QAAQ,EAAE;UACZ,CAAE;UAAAoD,QAAA,gBAGA1O,OAAA;YAAKsO,KAAK,EAAE;cAAEwgB,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAElgB,SAAS,EAAE,QAAQ;cAAElD,OAAO,EAAE,MAAM;cAAEihB,cAAc,EAAE,QAAQ;cAAExf,KAAK,EAAE;YAAO,CAAE;YAAAsB,QAAA,EACtHuf,SAAS,CAACjmB,GAAG,CAAC,CAACqG,IAAI,EAAEgd,KAAK,KAAK;cAC9B;cACA,IAAI2D,YAAY,GAAG3D,KAAK;cACxB,IAAIA,KAAK,KAAK,CAAC,EAAE2D,YAAY,GAAG,CAAC,CAAC,KAC7B,IAAI3D,KAAK,KAAK,CAAC,EAAE2D,YAAY,GAAG,CAAC;cAEtC,MAAMC,WAAW,GAAGhB,SAAS,CAACe,YAAY,CAAC;;cAE3C;cACA,MAAME,WAAW,GAAG,CAAC,CAAC;cACtB,IAAID,WAAW,KAAK,MAAM,EAAE;gBAAE;gBAC5BC,WAAW,CAACC,WAAW,GAAG,KAAK;cACjC,CAAC,MAAM,IAAIF,WAAW,KAAK,UAAU,EAAE;gBAAE;gBACvCC,WAAW,CAACE,UAAU,GAAG,MAAM;gBAC/BF,WAAW,CAACC,WAAW,GAAG,MAAM;cAClC,CAAC,MAAM,IAAIF,WAAW,KAAK,OAAO,EAAE;gBAAE;gBACpCC,WAAW,CAACE,UAAU,GAAG,KAAK;cAChC;cAEA,OAAOd,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC;cAAA;cAC3B;cACA;cACA;cACA;cACA;cACA;cACAjvB,OAAA;gBAAuBsO,KAAK,EAAE;kBAAC6gB,WAAW,EAAEF,WAAW,KAAK,MAAM,GAAG,CAAC,GAAG,MAAM;kBAAEtjB,OAAO,EAAE,MAAM;kBAAEiiB,aAAa,EAAE,QAAQ;kBAAEF,UAAU,EAAE;gBAAQ,CAAE;gBAAAhf,QAAA,gBAC/I1O,OAAA;kBAAKsO,KAAK,EAAE;oBAACnC,QAAQ,EAAC,MAAM;oBAAEoB,KAAK,EAAE+gB,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAAC1hB,KAAK;oBAAEC,UAAU,EAAC,MAAM;oBAAEqC,YAAY,EAAE;kBAAK,CAAE;kBAAAnB,QAAA,EAAE4f,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAACrX;gBAAU;kBAAA7I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrJlP,OAAA;kBAAMsO,KAAK,EAAE;oBAACf,KAAK,EAAE+gB,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAAC1hB,KAAK;oBAAEpB,QAAQ,EAAC,MAAM;oBAAEmB,UAAU,EAAE;kBAAM,CAAE;kBAAAoB,QAAA,EACrFugB,WAAW,KAAK,MAAM,GAAG,WAAW,GAAGA,WAAW,KAAK,UAAU,GAAG,WAAW,GAAG;gBAAW;kBAAAlgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA,GAJC+f,WAAW;gBAAAlgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKhB,CACN;YACH,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNlP,OAAA;YAAKsO,KAAK,EAAE;cAAEwgB,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAElgB,SAAS,EAAE,QAAQ;cAAElD,OAAO,EAAE,MAAM;cAAEihB,cAAc,EAAE,QAAQ;cAAExf,KAAK,EAAE;YAAO,CAAE;YAAAsB,QAAA,EACtHuf,SAAS,CAACjmB,GAAG,CAACqG,IAAI,IAAIigB,OAAO,CAACG,CAAC,CAACpgB,IAAI,CAAC,iBACpCrO,OAAA;cAAgBsO,KAAK,EAAE;gBAAC6gB,WAAW,EAAE9gB,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,MAAM;gBAAE1C,OAAO,EAAE,MAAM;gBAAEiiB,aAAa,EAAE,QAAQ;gBAAEF,UAAU,EAAE;cAAQ,CAAE;cAAAhf,QAAA,gBAClI1O,OAAA;gBAAMsO,KAAK,EAAE;kBAACf,KAAK,EAAE+gB,OAAO,CAACG,CAAC,CAACpgB,IAAI,CAAC,CAACd,KAAK;kBAAEpB,QAAQ,EAAC,MAAM;kBAAEmB,UAAU,EAAE;gBAAM,CAAE;gBAAAoB,QAAA,EAC9EL,IAAI,KAAK,MAAM,GAAG,WAAW,GAAGA,IAAI,KAAK,UAAU,GAAG,WAAW,GAAG;cAAW;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACPlP,OAAA;gBAAKsO,KAAK,EAAE;kBAACnC,QAAQ,EAAC,MAAM;kBAAEoB,KAAK,EAAE+gB,OAAO,CAACG,CAAC,CAACpgB,IAAI,CAAC,CAACd,KAAK;kBAAEC,UAAU,EAAC,MAAM;kBAAE+B,SAAS,EAAE;gBAAK,CAAE;gBAAAb,QAAA,EAAE4f,OAAO,CAACG,CAAC,CAACpgB,IAAI,CAAC,CAACuJ;cAAU;gBAAA7I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAJ5Hb,IAAI;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKT,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAINlP,OAAA;YAAKsO,KAAK,EAAE;cAAEwgB,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAElgB,SAAS,EAAE;YAAS,CAAE;YAAAH,QAAA,EAC5Duf,SAAS,CAACjmB,GAAG,CAAC,CAACqG,IAAI,EAAEgd,KAAK,KAAK;cAC9B;cACA,IAAI2D,YAAY,GAAG3D,KAAK;cACxB,IAAIA,KAAK,KAAK,CAAC,EAAE2D,YAAY,GAAG,CAAC,CAAC,KAC7B,IAAI3D,KAAK,KAAK,CAAC,EAAE2D,YAAY,GAAG,CAAC;cAEtC,MAAMC,WAAW,GAAGhB,SAAS,CAACe,YAAY,CAAC;cAE3C,OAAOV,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,iBAC3BjvB,OAAA;gBAAuBsO,KAAK,EAAE;kBAACuB,YAAY,EAAC,KAAK;kBAAElE,OAAO,EAAE,MAAM;kBAAE+hB,UAAU,EAAE,QAAQ;kBAAEd,cAAc,EAAE;gBAAY,CAAE;gBAAAle,QAAA,gBACtH1O,OAAA;kBAAMsO,KAAK,EAAE;oBAACf,KAAK,EAAE+gB,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAAC1hB,KAAK;oBAAEpB,QAAQ,EAAC,MAAM;oBAAEmB,UAAU,EAAE;kBAAM,CAAE;kBAAAoB,QAAA,EACrFugB,WAAW,KAAK,MAAM,GAAG,WAAW,GAAGA,WAAW,KAAK,UAAU,GAAG,WAAW,GAAG;gBAAW;kBAAAlgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC,eACPlP,OAAA;kBAAKsO,KAAK,EAAE;oBACVnC,QAAQ,EAAC,MAAM;oBACfoB,KAAK,EAAE+gB,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAAC1hB,KAAK;oBACnCC,UAAU,EAAC,MAAM;oBACjB4hB,UAAU,EAAE;kBACd,CAAE;kBAAA1gB,QAAA,EAAE4f,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAACrX;gBAAU;kBAAA7I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GATpC+f,WAAW;gBAAAlgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUhB,CACN;YACH,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNlP,OAAA;YAAKsO,KAAK,EAAE;cAAEwgB,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAElgB,SAAS,EAAE;YAAS,CAAE;YAAAH,QAAA,EAC5Duf,SAAS,CAACjmB,GAAG,CAACqG,IAAI,IAAIigB,OAAO,CAACI,CAAC,CAACrgB,IAAI,CAAC,iBACpCrO,OAAA;cAAgBsO,KAAK,EAAE;gBAACuB,YAAY,EAAC,KAAK;gBAAElE,OAAO,EAAE,MAAM;gBAAE+hB,UAAU,EAAE,QAAQ;gBAAEd,cAAc,EAAE;cAAU,CAAE;cAAAle,QAAA,gBAC7G1O,OAAA;gBAAKsO,KAAK,EAAE;kBACVnC,QAAQ,EAAC,MAAM;kBACfoB,KAAK,EAAE+gB,OAAO,CAACI,CAAC,CAACrgB,IAAI,CAAC,CAACd,KAAK;kBAC5BC,UAAU,EAAC,MAAM;kBACjB2hB,WAAW,EAAE;gBACf,CAAE;gBAAAzgB,QAAA,EAAE4f,OAAO,CAACI,CAAC,CAACrgB,IAAI,CAAC,CAACuJ;cAAU;gBAAA7I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrClP,OAAA;gBAAMsO,KAAK,EAAE;kBAACf,KAAK,EAAE+gB,OAAO,CAACI,CAAC,CAACrgB,IAAI,CAAC,CAACd,KAAK;kBAAEpB,QAAQ,EAAC,MAAM;kBAAEmB,UAAU,EAAE;gBAAM,CAAE;gBAAAoB,QAAA,EAC9EL,IAAI,KAAK,MAAM,GAAG,WAAW,GAAGA,IAAI,KAAK,UAAU,GAAG,WAAW,GAAG;cAAW;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA,GATCb,IAAI;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUT,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlP,OAAA;UAAKsO,KAAK,EAAE;YAAEiB,SAAS,EAAE,KAAK;YAAEpD,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE,MAAM;YAAEsB,SAAS,EAAE;UAAS,CAAE;UAAAH,QAAA,GAAC,4BAChF,EAAC,IAAIjE,IAAI,CAAC,CAAC,CAACoiB,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAA9d,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACL;MACArC,OAAO,gBACL7M,OAAA;QAAKsO,KAAK,EAAE;UAAExC,OAAO,EAAE,MAAM;UAAEsB,KAAK,EAAE,OAAO;UAAEoB,UAAU,EAAE,kBAAkB;UAAElD,QAAQ,EAAE;QAAW,CAAE;QAAAoD,QAAA,gBACpG1O,OAAA,CAAC2tB,WAAW;UAAA5e,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACflP,OAAA;UAAKsO,KAAK,EAAE;YAAEd,UAAU,EAAE,MAAM;YAAEqC,YAAY,EAAE,MAAM;YAAE1D,QAAQ,EAAE,MAAM;YAAE0C,SAAS,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAEsD,YAAY,CAACxC;QAAI;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1HlP,OAAA;UAAKsO,KAAK,EAAE;YACVO,SAAS,EAAE,QAAQ;YACnB/C,OAAO,EAAE,QAAQ;YACjByB,KAAK,EAAE,SAAS;YAChBpB,QAAQ,EAAE,MAAM;YAChBqB,UAAU,EAAE,MAAM;YAClBgB,UAAU,EAAE,uBAAuB;YACnCvC,YAAY,EAAE,KAAK;YACnB4D,YAAY,EAAE;UAChB,CAAE;UAAAnB,QAAA,EAAC;QAEH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNlP,OAAA;UAAKsO,KAAK,EAAE;YAAEnC,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE,MAAM;YAAEsB,SAAS,EAAE;UAAS,CAAE;UAAAH,QAAA,GAAC,kBAC9D,EAAC9B,OAAO;QAAA;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACNlP,OAAA;UAAKsO,KAAK,EAAE;YAAEiB,SAAS,EAAE,KAAK;YAAEpD,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE,MAAM;YAAEsB,SAAS,EAAE;UAAS,CAAE;UAAAH,QAAA,GAAC,4BAChF,EAAC,IAAIjE,IAAI,CAAC,CAAC,CAACoiB,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAA9d,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH;;IAEA;IACA,MAAM3K,CAAC,GAAG,GAAG;IACb,MAAME,CAAC,GAAG,GAAG;;IAEb;IACA,IAAI9C,MAAM,CAACoL,mBAAmB,EAAE;MAC9BpL,MAAM,CAACoL,mBAAmB,CAACmD,OAAO,GAAGtD,OAAO;IAC9C;;IAEA;IACA,IAAIjL,MAAM,CAACqL,0BAA0B,IAAIrL,MAAM,CAACqL,0BAA0B,CAACkD,OAAO,EAAE;MAClFyQ,aAAa,CAAChf,MAAM,CAACqL,0BAA0B,CAACkD,OAAO,CAAC;MACxDvO,MAAM,CAACqL,0BAA0B,CAACkD,OAAO,GAAG,IAAI;IAClD;;IAEA;IACA,IAAIvO,MAAM,CAACsL,uBAAuB,EAAE;MAClCtL,MAAM,CAACsL,uBAAuB,CAAC;QAC7BN,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBtB,QAAQ,EAAE;UAAE/G,CAAC;UAAEE;QAAE,CAAC;QAClBoI,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAAyf,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEzf,MAAM,KAAI;MAC/B,CAAC,CAAC;;MAEF;MACA,IAAInL,MAAM,CAACqL,0BAA0B,EAAE;QACrCrL,MAAM,CAACqL,0BAA0B,CAACkD,OAAO,GAAGuQ,WAAW,CAAC,MAAM;UAC5D9e,MAAM,CAACmR,qBAAqB,CAAClG,OAAO,CAAC;QACvC,CAAC,EAAE,IAAI,CAAC;MACV;MAEA,OAAO,IAAI;IACb,CAAC,MAAM;MACLrK,OAAO,CAACsB,KAAK,CAAC,8BAA8B,CAAC;MAC7C,OAAO,KAAK;IACd;EACF,CAAC,CAAC,OAAOA,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAID;AACA,MAAMynB,yBAAyB,GAAIzkB,MAAM,IAAK;EAC5C,IAAIqJ,OAAO,GAAGrJ,MAAM;;EAEpB;EACA,IAAIqJ,OAAO,IAAIA,OAAO,CAAC+R,QAAQ,IAAI/R,OAAO,CAAC+R,QAAQ,CAAC5T,IAAI,KAAK,cAAc,EAAE;IAC3E9L,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE0N,OAAO,CAACV,IAAI,IAAI,KAAK,CAAC;IAChD,OAAOU,OAAO;EAChB;;EAEA;EACA,OAAOA,OAAO,IAAIA,OAAO,CAACxH,MAAM,EAAE;IAChCwH,OAAO,GAAGA,OAAO,CAACxH,MAAM;IACxB,IAAIwH,OAAO,CAAC+R,QAAQ,IAAI/R,OAAO,CAAC+R,QAAQ,CAAC5T,IAAI,KAAK,cAAc,EAAE;MAChE9L,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE0N,OAAO,CAACV,IAAI,IAAI,KAAK,CAAC;MAChD,OAAOU,OAAO;IAChB;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACAvO,MAAM,CAAC0tB,kBAAkB,GAAG,CAAC9qB,CAAC,EAAEE,CAAC,KAAK;EACpC,IAAI;IACFlC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE+B,CAAC,EAAEE,CAAC,CAAC;;IAEnC;IACA,MAAMkhB,MAAM,GAAGC,QAAQ,CAAC8B,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAI,CAAC/B,MAAM,EAAE;MACXpjB,OAAO,CAACsB,KAAK,CAAC,sBAAsB,CAAC;MACrC,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAAC7C,KAAK,IAAI,CAACsL,SAAS,CAAC4D,OAAO,EAAE;MAChC3N,OAAO,CAACsB,KAAK,CAAC,iBAAiB,CAAC;MAChC,OAAO,KAAK;IACd;;IAEA;IACA,IAAIU,CAAC,KAAKuS,SAAS,IAAIrS,CAAC,KAAKqS,SAAS,EAAE;MACtCvS,CAAC,GAAG5C,MAAM,CAACwZ,UAAU,GAAG,CAAC;MACzB1W,CAAC,GAAG9C,MAAM,CAACyZ,WAAW,GAAG,CAAC;IAC5B;;IAEA;IACA,MAAM+O,IAAI,GAAGxE,MAAM,CAACyE,qBAAqB,CAAC,CAAC;IAC3C,MAAMC,MAAM,GAAI,CAAC9lB,CAAC,GAAG4lB,IAAI,CAAC3e,IAAI,IAAIma,MAAM,CAAC2E,WAAW,GAAI,CAAC,GAAG,CAAC;IAC7D,MAAMC,MAAM,GAAG,EAAE,CAAC9lB,CAAC,GAAG0lB,IAAI,CAAChd,GAAG,IAAIwY,MAAM,CAAC6E,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;IAE9DjoB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE6nB,MAAM,EAAEE,MAAM,CAAC;;IAErC;IACA,MAAME,SAAS,GAAG,IAAIrrB,KAAK,CAACsrB,SAAS,CAAC,CAAC;IACvCD,SAAS,CAAClF,MAAM,CAACoF,MAAM,CAACC,SAAS,GAAG,CAAC;IACrCH,SAAS,CAAClF,MAAM,CAACsF,IAAI,CAACD,SAAS,GAAG,CAAC;IAEnC,MAAME,WAAW,GAAG,IAAI1rB,KAAK,CAAC2rB,OAAO,CAACV,MAAM,EAAEE,MAAM,CAAC;IACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,EAAExe,SAAS,CAAC4D,OAAO,CAAC;;IAEvD;IACA,MAAM+a,mBAAmB,GAAG,EAAE;IAC9BroB,gBAAgB,CAACuE,OAAO,CAAC,CAACwa,QAAQ,EAAE/U,OAAO,KAAK;MAC9C,IAAI+U,QAAQ,CAACjb,KAAK,EAAE;QAClBukB,mBAAmB,CAAClT,IAAI,CAAC4J,QAAQ,CAACjb,KAAK,CAAC;QACxCnE,OAAO,CAACC,GAAG,CAAC,SAASoK,OAAO,QAAQ,CAAC;MACvC;IACF,CAAC,CAAC;;IAEF;IACArK,OAAO,CAACC,GAAG,CAAC,QAAQyoB,mBAAmB,CAACvjB,MAAM,YAAY,CAAC;IAC3D,MAAM4nB,YAAY,GAAG7E,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,EAAE,IAAI,CAAC;IAE1E,IAAIqE,YAAY,CAAC5nB,MAAM,GAAG,CAAC,EAAE;MAC3BnF,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B8sB,YAAY,CAACnoB,OAAO,CAAC,CAACikB,SAAS,EAAElZ,CAAC,KAAK;QACrC3P,OAAO,CAACC,GAAG,CAAC,MAAM0P,CAAC,GAAG,EAAEkZ,SAAS,CAACvkB,MAAM,CAAC2I,IAAI,IAAI,KAAK,EAC1C,KAAK,EAAE4b,SAAS,CAACpmB,QAAQ,EACzB,WAAW,EAAEomB,SAAS,CAACvkB,MAAM,CAACyE,QAAQ,CAACoH,OAAO,CAAC,CAAC,EAChD,WAAW,EAAE0Y,SAAS,CAACvkB,MAAM,CAACob,QAAQ,CAAC;;QAEnD;QACA,MAAM/B,GAAG,GAAGoL,yBAAyB,CAACF,SAAS,CAACvkB,MAAM,CAAC;QACvD,IAAIqZ,GAAG,IAAIA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAAC5T,IAAI,KAAK,cAAc,EAAE;UAC/D9L,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE0d,GAAG,CAAC+B,QAAQ,CAACrV,OAAO,CAAC;QAC/C;MACF,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;;IAEA;IACArK,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,MAAM+sB,eAAe,GAAG9E,SAAS,CAACU,gBAAgB,CAACnqB,KAAK,CAAC0N,QAAQ,EAAE,IAAI,CAAC;IAExEnM,OAAO,CAACC,GAAG,CAAC,WAAW+sB,eAAe,CAAC7nB,MAAM,MAAM,CAAC;IACpD6nB,eAAe,CAACpoB,OAAO,CAAC,CAACikB,SAAS,EAAElZ,CAAC,KAAK;MACxC,MAAMgO,GAAG,GAAGkL,SAAS,CAACvkB,MAAM;MAC5BtE,OAAO,CAACC,GAAG,CAAC,QAAQ0P,CAAC,GAAG,EAAEgO,GAAG,CAAC1Q,IAAI,IAAI,KAAK,EAC/B,KAAK,EAAE0Q,GAAG,CAAC7R,IAAI,EACf,KAAK,EAAE6R,GAAG,CAAC5U,QAAQ,CAACoH,OAAO,CAAC,CAAC,EAC7B,KAAK,EAAE0Y,SAAS,CAACpmB,QAAQ,EACzB,WAAW,EAAEkb,GAAG,CAAC+B,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACA1f,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,IAAIgtB,YAAY,GAAG,CAAC;IAEpB5sB,gBAAgB,CAACuE,OAAO,CAAC,CAACwa,QAAQ,EAAE/U,OAAO,KAAK;MAC9C,IAAI+U,QAAQ,CAACjb,KAAK,EAAE;QAAA,IAAA+oB,qBAAA;QAClB;QACA,IAAIC,SAAS,GAAG/N,QAAQ,CAACjb,KAAK,CAACiG,OAAO;QACtC,IAAIgjB,cAAc,GAAG,IAAI;;QAEzB;QACA,MAAMjE,QAAQ,GAAG,IAAItsB,KAAK,CAACiG,OAAO,CAAC,CAAC;QACpCsc,QAAQ,CAACjb,KAAK,CAACilB,gBAAgB,CAACD,QAAQ,CAAC;;QAEzC;QACA,MAAMkE,gBAAgB,GAAGlE,QAAQ,CAACzmB,UAAU,CAACqH,SAAS,CAAC4D,OAAO,CAAC5E,QAAQ,CAAC;;QAExE;QACA,MAAMsgB,SAAS,GAAGF,QAAQ,CAACrnB,KAAK,CAAC,CAAC,CAACwnB,OAAO,CAACvf,SAAS,CAAC4D,OAAO,CAAC;QAC7D,IAAIzK,IAAI,CAACK,GAAG,CAAC8lB,SAAS,CAACrnB,CAAC,CAAC,GAAG,CAAC,IAAIkB,IAAI,CAACK,GAAG,CAAC8lB,SAAS,CAACnnB,CAAC,CAAC,GAAG,CAAC,IAAImnB,SAAS,CAACjnB,CAAC,GAAG,CAAC,CAAC,IAAIinB,SAAS,CAACjnB,CAAC,GAAG,CAAC,EAAE;UACjGgrB,cAAc,GAAG,KAAK;QACxB;QAEA,IAAID,SAAS,EAAE;UACbF,YAAY,EAAE;QAChB;QAEAjtB,OAAO,CAACC,GAAG,CAAC,OAAOoK,OAAO,GAAG,EAAE;UAC7BijB,EAAE,EAAE,EAAAJ,qBAAA,GAAA9N,QAAQ,CAAC3P,YAAY,cAAAyd,qBAAA,uBAArBA,qBAAA,CAAuBjgB,IAAI,KAAI,IAAI;UACvCsgB,GAAG,EAAEJ,SAAS;UACdK,KAAK,EAAEJ,cAAc;UACrBK,IAAI,EAAEtE,QAAQ,CAAChZ,OAAO,CAAC,CAAC;UACxBud,IAAI,EAAE,CAACrE,SAAS,CAACrnB,CAAC,EAAEqnB,SAAS,CAACnnB,CAAC,EAAEmnB,SAAS,CAACjnB,CAAC,CAAC;UAC7CurB,MAAM,EAAEN;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEFrtB,OAAO,CAACC,GAAG,CAAC,MAAMgtB,YAAY,IAAI5sB,gBAAgB,CAAC6X,IAAI,WAAW,CAAC;;IAEnE;IACA,OAAO8U,eAAe,CAAC7nB,MAAM,GAAG,CAAC;EACnC,CAAC,CAAC,OAAO7D,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,OAAO,KAAK;EACd;AACF,CAAC;;AAID;AACA,MAAMsU,wBAAwB,GAAGA,CAACR,YAAY,EAAEG,SAAS,KAAK;EAAA,IAAAqY,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC5D,IAAI,CAAC1Y,YAAY,IAAI,CAACA,YAAY,CAACjR,KAAK,IAAI,CAACoR,SAAS,EAAE;IACtD;EACF;;EAEA;EACA,MAAMwY,cAAc,GAAG,EAAE;EACzB3Y,YAAY,CAACjR,KAAK,CAACE,QAAQ,CAAC+O,KAAK,IAAI;IACnC,IAAIA,KAAK,CAACsM,QAAQ,IAAItM,KAAK,CAACsM,QAAQ,CAACsO,OAAO,EAAE;MAC5CD,cAAc,CAACvY,IAAI,CAACpC,KAAK,CAAC;IAC5B;EACF,CAAC,CAAC;EAEF2a,cAAc,CAACnpB,OAAO,CAACklB,KAAK,IAAI;IAC9B1U,YAAY,CAACjR,KAAK,CAACiC,MAAM,CAAC0jB,KAAK,CAAC;EAClC,CAAC,CAAC;;EAEF;EACA,IAAIM,UAAU;EACd,QAAO7U,SAAS,CAACH,YAAY;IAC3B,KAAK,GAAG;MACNgV,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;MACNA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;IACR;MACEA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;EACJ;;EAEA;EACA,MAAMjD,aAAa,GAAG,IAAItqB,KAAK,CAACyiB,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAM8H,aAAa,GAAG,IAAIvqB,KAAK,CAACgiB,iBAAiB,CAAC;IAChD7T,KAAK,EAAEof,UAAU;IACjB9W,QAAQ,EAAE8W,UAAU;IACpB6D,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM5G,SAAS,GAAG,IAAIxqB,KAAK,CAACiiB,IAAI,CAACqI,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAACte,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAClCglB,SAAS,CAAC3H,QAAQ,GAAG;IACnBsO,OAAO,EAAE,IAAI;IACbliB,IAAI,EAAE,cAAc;IACpBzB,OAAO,GAAAujB,qBAAA,GAAExY,YAAY,CAAC3F,YAAY,cAAAme,qBAAA,uBAAzBA,qBAAA,CAA2BvjB,OAAO;IAC3CwK,OAAO,EAAEU,SAAS,CAACV,OAAO;IAC1BE,SAAS,EAAEQ,SAAS,CAACR,SAAS;IAC9BM,UAAU,EAAEE,SAAS,CAACF;EACxB,CAAC;;EAED;EACA,MAAMyU,KAAK,GAAG,IAAIjtB,KAAK,CAACqxB,UAAU,CAAC9D,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;EACrDN,KAAK,CAAC/gB,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAC5BynB,KAAK,CAACpK,QAAQ,GAAG;IAAEsO,OAAO,EAAE;EAAK,CAAC;;EAElC;EACA5Y,YAAY,CAACjR,KAAK,CAACC,GAAG,CAACijB,SAAS,CAAC;EACjCjS,YAAY,CAACjR,KAAK,CAACC,GAAG,CAAC0lB,KAAK,CAAC;EAE7B9pB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAA4tB,sBAAA,GAAAzY,YAAY,CAAC3F,YAAY,cAAAoe,sBAAA,uBAAzBA,sBAAA,CAA2B5gB,IAAI,OAAA6gB,sBAAA,GAAI1Y,YAAY,CAAC3F,YAAY,cAAAqe,sBAAA,uBAAzBA,sBAAA,CAA2BzjB,OAAO,cAAakL,SAAS,CAACH,YAAY,WAAWG,SAAS,CAACF,UAAU,GAAG,CAAC;AACjK,CAAC;AAED,eAAepO,WAAW;AAAC,IAAA4b,EAAA;AAAAsL,YAAA,CAAAtL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}