{"ast": null, "code": "import React from 'react';\nimport { useToken } from '../theme/internal';\nexport const responsiveArray = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];\nconst getResponsiveMap = token => ({\n  xs: `(max-width: ${token.screenXSMax}px)`,\n  sm: `(min-width: ${token.screenSM}px)`,\n  md: `(min-width: ${token.screenMD}px)`,\n  lg: `(min-width: ${token.screenLG}px)`,\n  xl: `(min-width: ${token.screenXL}px)`,\n  xxl: `(min-width: ${token.screenXXL}px)`\n});\n/**\n * Ensures that the breakpoints token are valid, in good order\n * For each breakpoint : screenMin <= screen <= screenMax and screenMax <= nextScreenMin\n */\nconst validateBreakpoints = token => {\n  const indexableToken = token;\n  const revBreakpoints = [].concat(responsiveArray).reverse();\n  revBreakpoints.forEach((breakpoint, i) => {\n    const breakpointUpper = breakpoint.toUpperCase();\n    const screenMin = `screen${breakpointUpper}Min`;\n    const screen = `screen${breakpointUpper}`;\n    if (!(indexableToken[screenMin] <= indexableToken[screen])) {\n      throw new Error(`${screenMin}<=${screen} fails : !(${indexableToken[screenMin]}<=${indexableToken[screen]})`);\n    }\n    if (i < revBreakpoints.length - 1) {\n      const screenMax = `screen${breakpointUpper}Max`;\n      if (!(indexableToken[screen] <= indexableToken[screenMax])) {\n        throw new Error(`${screen}<=${screenMax} fails : !(${indexableToken[screen]}<=${indexableToken[screenMax]})`);\n      }\n      const nextBreakpointUpperMin = revBreakpoints[i + 1].toUpperCase();\n      const nextScreenMin = `screen${nextBreakpointUpperMin}Min`;\n      if (!(indexableToken[screenMax] <= indexableToken[nextScreenMin])) {\n        throw new Error(`${screenMax}<=${nextScreenMin} fails : !(${indexableToken[screenMax]}<=${indexableToken[nextScreenMin]})`);\n      }\n    }\n  });\n  return token;\n};\nexport default function useResponsiveObserver() {\n  const [, token] = useToken();\n  const responsiveMap = getResponsiveMap(validateBreakpoints(token));\n  // To avoid repeat create instance, we add `useMemo` here.\n  return React.useMemo(() => {\n    const subscribers = new Map();\n    let subUid = -1;\n    let screens = {};\n    return {\n      matchHandlers: {},\n      dispatch(pointMap) {\n        screens = pointMap;\n        subscribers.forEach(func => func(screens));\n        return subscribers.size >= 1;\n      },\n      subscribe(func) {\n        if (!subscribers.size) {\n          this.register();\n        }\n        subUid += 1;\n        subscribers.set(subUid, func);\n        func(screens);\n        return subUid;\n      },\n      unsubscribe(paramToken) {\n        subscribers.delete(paramToken);\n        if (!subscribers.size) {\n          this.unregister();\n        }\n      },\n      unregister() {\n        Object.keys(responsiveMap).forEach(screen => {\n          const matchMediaQuery = responsiveMap[screen];\n          const handler = this.matchHandlers[matchMediaQuery];\n          handler === null || handler === void 0 ? void 0 : handler.mql.removeListener(handler === null || handler === void 0 ? void 0 : handler.listener);\n        });\n        subscribers.clear();\n      },\n      register() {\n        Object.keys(responsiveMap).forEach(screen => {\n          const matchMediaQuery = responsiveMap[screen];\n          const listener = _ref => {\n            let {\n              matches\n            } = _ref;\n            this.dispatch(Object.assign(Object.assign({}, screens), {\n              [screen]: matches\n            }));\n          };\n          const mql = window.matchMedia(matchMediaQuery);\n          mql.addListener(listener);\n          this.matchHandlers[matchMediaQuery] = {\n            mql,\n            listener\n          };\n          listener(mql);\n        });\n      },\n      responsiveMap\n    };\n  }, [token]);\n}\nexport const matchScreen = (screens, screenSizes) => {\n  if (screenSizes && typeof screenSizes === 'object') {\n    for (let i = 0; i < responsiveArray.length; i++) {\n      const breakpoint = responsiveArray[i];\n      if (screens[breakpoint] && screenSizes[breakpoint] !== undefined) {\n        return screenSizes[breakpoint];\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["React", "useToken", "responsiveArray", "getResponsiveMap", "token", "xs", "screenXSMax", "sm", "screenSM", "md", "screenMD", "lg", "screenLG", "xl", "screenXL", "xxl", "screenXXL", "validateBreakpoints", "indexableToken", "revBreakpoints", "concat", "reverse", "for<PERSON>ach", "breakpoint", "i", "breakpointUpper", "toUpperCase", "screenMin", "screen", "Error", "length", "screenMax", "nextBreakpointUpperMin", "nextScreenMin", "useResponsiveObserver", "responsiveMap", "useMemo", "subscribers", "Map", "subUid", "screens", "matchHandlers", "dispatch", "pointMap", "func", "size", "subscribe", "register", "set", "unsubscribe", "paramToken", "delete", "unregister", "Object", "keys", "matchMediaQuery", "handler", "mql", "removeListener", "listener", "clear", "_ref", "matches", "assign", "window", "matchMedia", "addListener", "matchScreen", "screenSizes", "undefined"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/_util/responsiveObserver.js"], "sourcesContent": ["import React from 'react';\nimport { useToken } from '../theme/internal';\nexport const responsiveArray = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];\nconst getResponsiveMap = token => ({\n  xs: `(max-width: ${token.screenXSMax}px)`,\n  sm: `(min-width: ${token.screenSM}px)`,\n  md: `(min-width: ${token.screenMD}px)`,\n  lg: `(min-width: ${token.screenLG}px)`,\n  xl: `(min-width: ${token.screenXL}px)`,\n  xxl: `(min-width: ${token.screenXXL}px)`\n});\n/**\n * Ensures that the breakpoints token are valid, in good order\n * For each breakpoint : screenMin <= screen <= screenMax and screenMax <= nextScreenMin\n */\nconst validateBreakpoints = token => {\n  const indexableToken = token;\n  const revBreakpoints = [].concat(responsiveArray).reverse();\n  revBreakpoints.forEach((breakpoint, i) => {\n    const breakpointUpper = breakpoint.toUpperCase();\n    const screenMin = `screen${breakpointUpper}Min`;\n    const screen = `screen${breakpointUpper}`;\n    if (!(indexableToken[screenMin] <= indexableToken[screen])) {\n      throw new Error(`${screenMin}<=${screen} fails : !(${indexableToken[screenMin]}<=${indexableToken[screen]})`);\n    }\n    if (i < revBreakpoints.length - 1) {\n      const screenMax = `screen${breakpointUpper}Max`;\n      if (!(indexableToken[screen] <= indexableToken[screenMax])) {\n        throw new Error(`${screen}<=${screenMax} fails : !(${indexableToken[screen]}<=${indexableToken[screenMax]})`);\n      }\n      const nextBreakpointUpperMin = revBreakpoints[i + 1].toUpperCase();\n      const nextScreenMin = `screen${nextBreakpointUpperMin}Min`;\n      if (!(indexableToken[screenMax] <= indexableToken[nextScreenMin])) {\n        throw new Error(`${screenMax}<=${nextScreenMin} fails : !(${indexableToken[screenMax]}<=${indexableToken[nextScreenMin]})`);\n      }\n    }\n  });\n  return token;\n};\nexport default function useResponsiveObserver() {\n  const [, token] = useToken();\n  const responsiveMap = getResponsiveMap(validateBreakpoints(token));\n  // To avoid repeat create instance, we add `useMemo` here.\n  return React.useMemo(() => {\n    const subscribers = new Map();\n    let subUid = -1;\n    let screens = {};\n    return {\n      matchHandlers: {},\n      dispatch(pointMap) {\n        screens = pointMap;\n        subscribers.forEach(func => func(screens));\n        return subscribers.size >= 1;\n      },\n      subscribe(func) {\n        if (!subscribers.size) {\n          this.register();\n        }\n        subUid += 1;\n        subscribers.set(subUid, func);\n        func(screens);\n        return subUid;\n      },\n      unsubscribe(paramToken) {\n        subscribers.delete(paramToken);\n        if (!subscribers.size) {\n          this.unregister();\n        }\n      },\n      unregister() {\n        Object.keys(responsiveMap).forEach(screen => {\n          const matchMediaQuery = responsiveMap[screen];\n          const handler = this.matchHandlers[matchMediaQuery];\n          handler === null || handler === void 0 ? void 0 : handler.mql.removeListener(handler === null || handler === void 0 ? void 0 : handler.listener);\n        });\n        subscribers.clear();\n      },\n      register() {\n        Object.keys(responsiveMap).forEach(screen => {\n          const matchMediaQuery = responsiveMap[screen];\n          const listener = _ref => {\n            let {\n              matches\n            } = _ref;\n            this.dispatch(Object.assign(Object.assign({}, screens), {\n              [screen]: matches\n            }));\n          };\n          const mql = window.matchMedia(matchMediaQuery);\n          mql.addListener(listener);\n          this.matchHandlers[matchMediaQuery] = {\n            mql,\n            listener\n          };\n          listener(mql);\n        });\n      },\n      responsiveMap\n    };\n  }, [token]);\n}\nexport const matchScreen = (screens, screenSizes) => {\n  if (screenSizes && typeof screenSizes === 'object') {\n    for (let i = 0; i < responsiveArray.length; i++) {\n      const breakpoint = responsiveArray[i];\n      if (screens[breakpoint] && screenSizes[breakpoint] !== undefined) {\n        return screenSizes[breakpoint];\n      }\n    }\n  }\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,mBAAmB;AAC5C,OAAO,MAAMC,eAAe,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACpE,MAAMC,gBAAgB,GAAGC,KAAK,KAAK;EACjCC,EAAE,EAAE,eAAeD,KAAK,CAACE,WAAW,KAAK;EACzCC,EAAE,EAAE,eAAeH,KAAK,CAACI,QAAQ,KAAK;EACtCC,EAAE,EAAE,eAAeL,KAAK,CAACM,QAAQ,KAAK;EACtCC,EAAE,EAAE,eAAeP,KAAK,CAACQ,QAAQ,KAAK;EACtCC,EAAE,EAAE,eAAeT,KAAK,CAACU,QAAQ,KAAK;EACtCC,GAAG,EAAE,eAAeX,KAAK,CAACY,SAAS;AACrC,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAGb,KAAK,IAAI;EACnC,MAAMc,cAAc,GAAGd,KAAK;EAC5B,MAAMe,cAAc,GAAG,EAAE,CAACC,MAAM,CAAClB,eAAe,CAAC,CAACmB,OAAO,CAAC,CAAC;EAC3DF,cAAc,CAACG,OAAO,CAAC,CAACC,UAAU,EAAEC,CAAC,KAAK;IACxC,MAAMC,eAAe,GAAGF,UAAU,CAACG,WAAW,CAAC,CAAC;IAChD,MAAMC,SAAS,GAAG,SAASF,eAAe,KAAK;IAC/C,MAAMG,MAAM,GAAG,SAASH,eAAe,EAAE;IACzC,IAAI,EAAEP,cAAc,CAACS,SAAS,CAAC,IAAIT,cAAc,CAACU,MAAM,CAAC,CAAC,EAAE;MAC1D,MAAM,IAAIC,KAAK,CAAC,GAAGF,SAAS,KAAKC,MAAM,cAAcV,cAAc,CAACS,SAAS,CAAC,KAAKT,cAAc,CAACU,MAAM,CAAC,GAAG,CAAC;IAC/G;IACA,IAAIJ,CAAC,GAAGL,cAAc,CAACW,MAAM,GAAG,CAAC,EAAE;MACjC,MAAMC,SAAS,GAAG,SAASN,eAAe,KAAK;MAC/C,IAAI,EAAEP,cAAc,CAACU,MAAM,CAAC,IAAIV,cAAc,CAACa,SAAS,CAAC,CAAC,EAAE;QAC1D,MAAM,IAAIF,KAAK,CAAC,GAAGD,MAAM,KAAKG,SAAS,cAAcb,cAAc,CAACU,MAAM,CAAC,KAAKV,cAAc,CAACa,SAAS,CAAC,GAAG,CAAC;MAC/G;MACA,MAAMC,sBAAsB,GAAGb,cAAc,CAACK,CAAC,GAAG,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;MAClE,MAAMO,aAAa,GAAG,SAASD,sBAAsB,KAAK;MAC1D,IAAI,EAAEd,cAAc,CAACa,SAAS,CAAC,IAAIb,cAAc,CAACe,aAAa,CAAC,CAAC,EAAE;QACjE,MAAM,IAAIJ,KAAK,CAAC,GAAGE,SAAS,KAAKE,aAAa,cAAcf,cAAc,CAACa,SAAS,CAAC,KAAKb,cAAc,CAACe,aAAa,CAAC,GAAG,CAAC;MAC7H;IACF;EACF,CAAC,CAAC;EACF,OAAO7B,KAAK;AACd,CAAC;AACD,eAAe,SAAS8B,qBAAqBA,CAAA,EAAG;EAC9C,MAAM,GAAG9B,KAAK,CAAC,GAAGH,QAAQ,CAAC,CAAC;EAC5B,MAAMkC,aAAa,GAAGhC,gBAAgB,CAACc,mBAAmB,CAACb,KAAK,CAAC,CAAC;EAClE;EACA,OAAOJ,KAAK,CAACoC,OAAO,CAAC,MAAM;IACzB,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC7B,IAAIC,MAAM,GAAG,CAAC,CAAC;IACf,IAAIC,OAAO,GAAG,CAAC,CAAC;IAChB,OAAO;MACLC,aAAa,EAAE,CAAC,CAAC;MACjBC,QAAQA,CAACC,QAAQ,EAAE;QACjBH,OAAO,GAAGG,QAAQ;QAClBN,WAAW,CAACf,OAAO,CAACsB,IAAI,IAAIA,IAAI,CAACJ,OAAO,CAAC,CAAC;QAC1C,OAAOH,WAAW,CAACQ,IAAI,IAAI,CAAC;MAC9B,CAAC;MACDC,SAASA,CAACF,IAAI,EAAE;QACd,IAAI,CAACP,WAAW,CAACQ,IAAI,EAAE;UACrB,IAAI,CAACE,QAAQ,CAAC,CAAC;QACjB;QACAR,MAAM,IAAI,CAAC;QACXF,WAAW,CAACW,GAAG,CAACT,MAAM,EAAEK,IAAI,CAAC;QAC7BA,IAAI,CAACJ,OAAO,CAAC;QACb,OAAOD,MAAM;MACf,CAAC;MACDU,WAAWA,CAACC,UAAU,EAAE;QACtBb,WAAW,CAACc,MAAM,CAACD,UAAU,CAAC;QAC9B,IAAI,CAACb,WAAW,CAACQ,IAAI,EAAE;UACrB,IAAI,CAACO,UAAU,CAAC,CAAC;QACnB;MACF,CAAC;MACDA,UAAUA,CAAA,EAAG;QACXC,MAAM,CAACC,IAAI,CAACnB,aAAa,CAAC,CAACb,OAAO,CAACM,MAAM,IAAI;UAC3C,MAAM2B,eAAe,GAAGpB,aAAa,CAACP,MAAM,CAAC;UAC7C,MAAM4B,OAAO,GAAG,IAAI,CAACf,aAAa,CAACc,eAAe,CAAC;UACnDC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,GAAG,CAACC,cAAc,CAACF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,QAAQ,CAAC;QAClJ,CAAC,CAAC;QACFtB,WAAW,CAACuB,KAAK,CAAC,CAAC;MACrB,CAAC;MACDb,QAAQA,CAAA,EAAG;QACTM,MAAM,CAACC,IAAI,CAACnB,aAAa,CAAC,CAACb,OAAO,CAACM,MAAM,IAAI;UAC3C,MAAM2B,eAAe,GAAGpB,aAAa,CAACP,MAAM,CAAC;UAC7C,MAAM+B,QAAQ,GAAGE,IAAI,IAAI;YACvB,IAAI;cACFC;YACF,CAAC,GAAGD,IAAI;YACR,IAAI,CAACnB,QAAQ,CAACW,MAAM,CAACU,MAAM,CAACV,MAAM,CAACU,MAAM,CAAC,CAAC,CAAC,EAAEvB,OAAO,CAAC,EAAE;cACtD,CAACZ,MAAM,GAAGkC;YACZ,CAAC,CAAC,CAAC;UACL,CAAC;UACD,MAAML,GAAG,GAAGO,MAAM,CAACC,UAAU,CAACV,eAAe,CAAC;UAC9CE,GAAG,CAACS,WAAW,CAACP,QAAQ,CAAC;UACzB,IAAI,CAAClB,aAAa,CAACc,eAAe,CAAC,GAAG;YACpCE,GAAG;YACHE;UACF,CAAC;UACDA,QAAQ,CAACF,GAAG,CAAC;QACf,CAAC,CAAC;MACJ,CAAC;MACDtB;IACF,CAAC;EACH,CAAC,EAAE,CAAC/B,KAAK,CAAC,CAAC;AACb;AACA,OAAO,MAAM+D,WAAW,GAAGA,CAAC3B,OAAO,EAAE4B,WAAW,KAAK;EACnD,IAAIA,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;IAClD,KAAK,IAAI5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,eAAe,CAAC4B,MAAM,EAAEN,CAAC,EAAE,EAAE;MAC/C,MAAMD,UAAU,GAAGrB,eAAe,CAACsB,CAAC,CAAC;MACrC,IAAIgB,OAAO,CAACjB,UAAU,CAAC,IAAI6C,WAAW,CAAC7C,UAAU,CAAC,KAAK8C,SAAS,EAAE;QAChE,OAAOD,WAAW,CAAC7C,UAAU,CAAC;MAChC;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}