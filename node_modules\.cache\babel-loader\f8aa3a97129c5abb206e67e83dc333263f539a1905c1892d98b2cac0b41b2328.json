{"ast": null, "code": "import React,{useEffect,useRef,useState,useCallback}from'react';import*as THREE from'three';import{GLTFLoader}from'three/examples/jsm/loaders/GLTFLoader';import{OrbitControls}from'three/examples/jsm/controls/OrbitControls';import{CoordinateConverter}from'../utils/CoordinateConverter';import*as TWEEN from'@tweenjs/tween.js';import*as SkeletonUtils from'three/examples/jsm/utils/SkeletonUtils.js';import mqtt from'mqtt';import{Select,Popover}from'antd';import intersectionsData from'../data/intersections.json';// 全局变量\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";let globalVehicleRef=null;let globalTrajectory=[];let currentPointIndex=0;let globalUpdateInterval=null;let targetPosition=null;// 新增：目标位置\nlet currentPosition=null;// 新增：当前位置\nlet isMoving=false;// 新增：移动状态标志\nlet cameraMode='global';// 'global' 或 'follow'\nlet controls=null;// 保存 controls 的引用\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel=null;let preloadedCyclistModel=null;// 新增：非机动车模型\nlet preloadedPeopleModel=null;// 新增：行人模型\nlet preloadedTrafficLightModel=null;// 新增：红绿灯模型\nlet scene=null;// 添加scene全局变量\nlet peopleBaseModel=null;// 存储原始模型数据\nlet skeleton=null;// 添加滤波相关的变量\nlet lastPosition=null;let lastRotation=null;const ALPHA=0.08;// 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions=new Map();// 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations=new Map();// 使用Map存储每个车辆ID的上一次旋转角度\n// MQTT配置\nconst MQTT_CONFIG={broker:window.location.hostname,port:8083,// 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\nbsm:'changli/cloud/v2x/obu/bsm',rsm:'changli/cloud/v2x/rsu/rsm',scene:'changli/cloud/v2x/obu/scene',rsi:'changli/cloud/v2x/rsu/rsi',// 添加 RSI 主题\nspat:'changli/cloud/v2x/rsu/spat'// 添加 SPAT 主题\n};// 修改所有资源的基础URL\nconst BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:5000';console.log('API_URLxxxx:',process.env.REACT_APP_API_URL);// 添加全局变量来存储所有车辆\nconst vehicleModels=new Map();// 使用Map存储车辆ID和对应的3D模型\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps=new Map();// 用于存储每个设备的最新时间戳\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId=null;// 添加红绿灯相关的全局变量\nlet trafficLightsMap=new Map();// 存储路口ID与红绿灯模型的映射\nlet trafficLightStates=new Map();// 存储路口ID与红绿灯状态的映射\nconst clock=new THREE.Clock();// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId=async()=>{try{const response=await fetch(`${BASE_URL}/api/vehicles/list`);const data=await response.json();if(data&&data.vehicles&&Array.isArray(data.vehicles)){const mainVehicle=data.vehicles.find(v=>v.isMainVehicle===true);if(mainVehicle&&mainVehicle.bsmId){mainVehicleBsmId=mainVehicle.bsmId;console.log('获取主车bsmId成功:',mainVehicleBsmId);return mainVehicleBsmId;}}console.log('未找到主车，使用默认值 BSM01');mainVehicleBsmId='BSM01';// 默认值\nreturn mainVehicleBsmId;}catch(error){console.error('获取主车信息失败:',error);mainVehicleBsmId='BSM01';// 出错时使用默认值\nreturn mainVehicleBsmId;}};// 添加滤波器函数\nconst lowPassFilter=(newValue,lastValue,alpha)=>{if(lastValue===null)return newValue;return alpha*newValue+(1-alpha)*lastValue;};// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition=(newPos,vehicleId)=>{// 如果没有提供车辆ID，使用全局变量（用于主车）\nif(!vehicleId){if(!lastPosition){lastPosition=newPos.clone();return newPos;}const filteredX=lowPassFilter(newPos.x,lastPosition.x,ALPHA);const filteredY=lowPassFilter(newPos.y,lastPosition.y,ALPHA);const filteredZ=lowPassFilter(newPos.z,lastPosition.z,ALPHA);lastPosition.set(filteredX,filteredY,filteredZ);return lastPosition.clone();}// 针对特定车辆ID的滤波\nif(!vehicleLastPositions.has(vehicleId)){vehicleLastPositions.set(vehicleId,newPos.clone());return newPos;}const lastPos=vehicleLastPositions.get(vehicleId);// 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\nconst distance=lastPos.distanceTo(newPos);const MAX_DISTANCE_THRESHOLD=50;// 最大距离阈值，超过此距离认为是位置跳变\nif(distance>MAX_DISTANCE_THRESHOLD){console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);vehicleLastPositions.set(vehicleId,newPos.clone());return newPos;}// 正常滤波处理\nconst filteredX=lowPassFilter(newPos.x,lastPos.x,ALPHA);const filteredY=lowPassFilter(newPos.y,lastPos.y,ALPHA);const filteredZ=lowPassFilter(newPos.z,lastPos.z,ALPHA);const filteredPos=new THREE.Vector3(filteredX,filteredY,filteredZ);vehicleLastPositions.set(vehicleId,filteredPos.clone());return filteredPos;};// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation=(newRotation,vehicleId)=>{// 如果没有提供车辆ID，使用全局变量（用于主车）\nif(!vehicleId){if(lastRotation===null){lastRotation=newRotation;return newRotation;}// 处理角度跳变（从360度到0度或反之）\nlet diff=newRotation-lastRotation;if(diff>Math.PI)diff-=2*Math.PI;if(diff<-Math.PI)diff+=2*Math.PI;const filteredRotation=lowPassFilter(lastRotation+diff,lastRotation,ALPHA);lastRotation=filteredRotation;return filteredRotation;}// 针对特定车辆ID的滤波\nif(!vehicleLastRotations.has(vehicleId)){vehicleLastRotations.set(vehicleId,newRotation);return newRotation;}const lastRot=vehicleLastRotations.get(vehicleId);// 处理角度跳变（从360度到0度或反之）\nlet diff=newRotation-lastRot;if(diff>Math.PI)diff-=2*Math.PI;if(diff<-Math.PI)diff+=2*Math.PI;// 检查是否是大角度变化，如果是则不进行过滤\nconst MAX_ANGLE_THRESHOLD=Math.PI/2;// 90度\nif(Math.abs(diff)>MAX_ANGLE_THRESHOLD){console.log(`车辆${vehicleId}朝向变化过大(${(diff*180/Math.PI).toFixed(2)}度)，不进行滤波`);vehicleLastRotations.set(vehicleId,newRotation);return newRotation;}const filteredRotation=lowPassFilter(lastRot+diff,lastRot,ALPHA);vehicleLastRotations.set(vehicleId,filteredRotation);return filteredRotation;};// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL=1;// 每秒更新一次\n// ... existing code ...\nconst CampusModel=_ref=>{let{className,onCurrentRSUChange,selectedRSUs}=_ref;const containerRef=useRef(null);const vehicleRef=useRef(null);const converter=useRef(new CoordinateConverter());const trajectoryRef=useRef([]);const currentPointRef=useRef(0);const mqttClientRef=useRef(null);const animationFrameRef=useRef(null);const mapRef=useRef(null);// 添加相机平滑过渡的变量\nconst lastCameraPosition=useRef(null);const lastCameraTarget=useRef(null);const cameraSmoothing=0.98;// 平滑系数，值越大越平滑 (0-1之间)\n// 添加行人动画相关的引用\nconst prevAnimationTimeRef=useRef(Date.now());const peopleAnimationMixers=useRef(new Map());// 存储所有行人的动画混合器\nconst peopleAnimations=useRef([]);// 存储行人动画数据\n// 添加车辆状态\nconst[vehicleState,setVehicleState]=useState({longitude:0,latitude:0,speed:0,heading:0});// 在 CampusModel 组件中添加状态\nconst[viewMode,setViewMode]=useState('global');// 添加视角切换按钮的样式\nconst buttonContainerStyle={position:'fixed',bottom:'20px',left:'50%',transform:'translateX(-50%)',zIndex:1000,// 改为1000，避免遮挡点击\ndisplay:'flex',gap:'10px'};const buttonStyle={padding:'8px 16px',backgroundColor:'rgba(255, 255, 255, 0.9)',border:'1px solid #ddd',borderRadius:'4px',cursor:'pointer',fontSize:'14px',boxShadow:'0 2px 4px rgba(0,0,0,0.1)',transition:'all 0.3s ease'};// 添加相机引用\nconst cameraRef=useRef(null);// 添加路口选择相关代码\nconst[selectedIntersection,setSelectedIntersection]=useState(null);// 添加红绿灯状态弹出窗口相关状态\nconst[trafficLightPopover,setTrafficLightPopover]=useState({visible:false,interId:null,position:{x:0,y:0},content:null,phases:[]});// 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\nconst currentPopoverIdRef=useRef(null);// 添加红绿灯状态自动更新定时器引用\nconst trafficLightUpdateTimerRef=useRef(null);// 全局存储setTrafficLightPopover函数引用\nwindow._setTrafficLightPopover=setTrafficLightPopover;// 将Ref暴露给全局以便弹窗函数使用\nwindow.currentPopoverIdRef=currentPopoverIdRef;window.trafficLightUpdateTimerRef=trafficLightUpdateTimerRef;// 修改路口选择器的样式\nconst intersectionSelectStyle={position:'fixed',top:'60px',left:'50%',transform:'translateX(-50%)',width:'200px',// 从 300px 改为 200px\nzIndex:1001,backgroundColor:'white',borderRadius:'4px',boxShadow:'0 2px 8px rgba(0,0,0,0.1)'};// 添加文字标签样式\nconst labelStyle={position:'fixed',top:'60px',left:'calc(50% - 90px)',// 从 140px 改为 110px，让文字更靠近选择框\ntransform:'translateX(-100%)',padding:'0 5px',// 从 10px 改为 5px，减少内边距\nlineHeight:'32px',color:'#fff',fontSize:'14px',fontWeight:'bold',textShadow:'0 1px 2px rgba(0,0,0,0.3)',zIndex:1001};// 添加交通灯映射状态\nconst[trafficLightsMap]=useState(new Map());// 添加当前RSU状态\nconst[currentRSU,setCurrentRSU]=useState(null);// 添加设备时间戳状态\nconst[deviceTimestamps,setDeviceTimestamps]=useState(new Map());// 添加最后一条消息状态\nconst[lastMessage,setLastMessage]=useState({topic:'',content:''});// 修改视角切换函数\nconst switchToFollowView=()=>{if(cameraMode!=='follow'){console.log('切换到跟随视角');cameraMode='follow';// 重置相机平滑变量，确保切换视角时不会有突兀的过渡\nlastCameraPosition.current=null;lastCameraTarget.current=null;if(controls){controls.enabled=false;}}};const switchToGlobalView=()=>{if(cameraMode!=='global'){console.log('切换到全局视角');cameraMode='global';// 重置相机平滑变量\nlastCameraPosition.current=null;lastCameraTarget.current=null;if(cameraRef.current&&controls){// 获取当前相机位置和朝向\n// const currentPos = cameraRef.current.position.clone();\ncameraRef.current.position.set(0,500,0);const currentPos=cameraRef.current.position.clone();const currentUp=cameraRef.current.up.clone();// 创建相机位置的补间动画\nnew TWEEN.Tween(currentPos).to({x:0,y:300,z:0},1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(()=>{cameraRef.current.position.copy(currentPos);}).start();// 创建相机上方向的补间动画\nnew TWEEN.Tween(currentUp).to({x:0,y:1,z:0},1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(()=>{cameraRef.current.up.copy(currentUp);}).start();// 获取当前控制器目标点\ncontrols.target.set(0,0,0);const currentTarget=controls.target.clone();// 创建目标点的补间动画\nnew TWEEN.Tween(currentTarget).to({x:0,y:0,z:0},1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(()=>{controls.target.copy(currentTarget);// 确保相机始终朝向目标点\ncameraRef.current.lookAt(controls.target);controls.update();}).start();// 启用控制器\ncontrols.enabled=true;// 重置控制器的一些属性\ncontrols.minDistance=50;controls.maxDistance=500;controls.maxPolarAngle=Math.PI/2.1;controls.minPolarAngle=0;controls.update();// 强制更新相机矩阵\ncameraRef.current.updateMatrix();cameraRef.current.updateMatrixWorld(true);console.log('切换到全局视角',{目标相机位置:[0,300,0],目标控制点:[0,0,0],动画已启动:true});}}};// 修改处理路口选择的函数\nconst handleIntersectionChange=value=>{const intersection=intersectionsData.intersections.find(i=>i.name===value);if(intersection&&cameraRef.current&&controls){setSelectedIntersection(intersection);// 使用 wgs84ToModel 方法转换经纬度到模型坐标\nconst modelCoords=converter.current.wgs84ToModel(parseFloat(intersection.longitude),parseFloat(intersection.latitude));console.log('路口坐标转换结果:',{路口名称:intersection.name,经纬度:{longitude:intersection.longitude,latitude:intersection.latitude},模型坐标:modelCoords});// 设置为路口视角模式\ncameraMode='intersection';setViewMode('intersection');// 直接设置相机位置\ncameraRef.current.position.set(modelCoords.x+50,70,-modelCoords.y+50);// 直接设置控制器目标点\ncontrols.target.set(modelCoords.x,0,-modelCoords.y);// 确保相机朝向目标点\ncameraRef.current.lookAt(controls.target);// 更新控制器\ncontrols.enabled=true;controls.update();// 强制更新相机矩阵\ncameraRef.current.updateMatrix();cameraRef.current.updateMatrixWorld(true);console.log('相机已直接移动到路口:',{路口名称:intersection.name,相机位置:cameraRef.current.position.toArray(),目标点:controls.target.toArray(),模型坐标:modelCoords});}};// 修改处理MQTT消息的函数\nconst handleMqttMessage=(topic,message)=>{try{const payload=JSON.parse(message);// 处理RSM消息\nif(topic===MQTT_CONFIG.rsm){var _payload$data;// console.log('收到RSM消息:', payload);\n// 检查设备时间戳\nconst deviceMac=payload.mac;const messageTimestamp=payload.tm;// 获取该设备的最新时间戳\nconst lastTimestamp=deviceTimestamps.get(deviceMac);// 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\nif(lastTimestamp&&messageTimestamp<lastTimestamp){// console.log('忽略过期的RSM消息:', {\n//   设备MAC: deviceMac,\n//   消息时间戳: messageTimestamp,\n//   最新时间戳: lastTimestamp\n// });\nreturn;}// 更新设备的最新时间戳\ndeviceTimestamps.set(deviceMac,messageTimestamp);// console.log('RSM消息时间戳更新:', {\n//   设备MAC: deviceMac,\n//   时间戳: messageTimestamp,\n//   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n// });\nconst participants=((_payload$data=payload.data)===null||_payload$data===void 0?void 0:_payload$data.participants)||[];const rsuid=payload.data.rsuid;// 分类处理不同类型的参与者\nconst now=Date.now();// 处理所有参与者\nparticipants.forEach(participant=>{// const id = participant.partPtcId;\n// const id =  rsuid + participant.partPtcId;\nconst id=deviceMac+participant.partPtcId;const type=participant.partPtcType;if(type==='3'||type==='2'||type==='1'){// if(type === '3'){\n// if(type === '3'||type === '1'){\n// 解析位置和状态信息\nconst state={longitude:parseFloat(participant.partPosLong),latitude:parseFloat(participant.partPosLat),speed:parseFloat(participant.partSpeed),heading:parseFloat(participant.partHeading)};const modelPos=converter.current.wgs84ToModel(state.longitude,state.latitude);// 根据类型选择对应的预加载模型\nlet preloadedModel;switch(type){case'1':// 机动车\npreloadedModel=preloadedVehicleModel;break;case'2':// 非机动车\npreloadedModel=preloadedCyclistModel;break;case'3':// 行人\npreloadedModel=preloadedPeopleModel;break;default:return;// 跳过未知类型\n}// 获取或创建模型\nlet model=vehicleModels.get(id);if(!model&&preloadedModel){// 创建新模型实例\nconst newModel=type==='3'?SkeletonUtils.clone(preloadedPeopleModel):preloadedModel.clone();// 根据类型调整高度和缩放\nconst height=type==='3'?2.0:1.0;newModel.position.set(modelPos.x,height,-modelPos.y);newModel.rotation.y=Math.PI-state.heading*Math.PI/180;// 如果是行人类型，设置缩放和创建动画\nif(type==='3'){// newModel.scale.set(0.005, 0.005, 0.005);\nnewModel.scale.set(4,4,4);// 创建动画混合器\nconst mixer=new THREE.AnimationMixer(newModel);// 播放行走动画peopleBaseModel\nconst action=mixer.clipAction(peopleBaseModel.animations[0]);action.play();peopleAnimationMixers.current.set(id,mixer);// console.log('找到行人动画tttt:', peopleBaseModel.animations[0]);\n// if(peopleBaseModel){\n//   if ( peopleBaseModel.animations.length > 0) {\n//     peopleBaseModel.animations.forEach((clip) => {\n//       const action = mixer.clipAction(clip);\n//       action.play();\n//       // console.log('找到行人动画tttt:', peopleBaseModel.animations.length, '个');\n//     });\n//     // 保存动画混合器\n//     peopleAnimationMixers.current.set(id, mixer);\n//   }\n// }\n}scene.add(newModel);vehicleModels.set(id,{model:newModel,lastUpdate:now,type:type});}else if(model){// 更新现有模型\nmodel.model.position.set(modelPos.x,model.type==='3'?2.0:1.0,-modelPos.y);model.model.rotation.y=Math.PI-state.heading*Math.PI/180;model.lastUpdate=now;model.model.updateMatrix();model.model.updateMatrixWorld(true);}}});// 清理长时间未更新的模型\nconst CLEANUP_THRESHOLD=1000;const currentIds=new Set(participants.map(p=>p.partPtcId));vehicleModels.forEach((modelData,id)=>{if(now-modelData.lastUpdate>CLEANUP_THRESHOLD&&!currentIds.has(id)){scene.remove(modelData.model);vehicleModels.delete(id);// console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n}});return;}// 处理BSM消息\nif(topic===MQTT_CONFIG.bsm){// console.log('收到BSM消息:', payload);\nconst bsmData=payload.data;const bsmid=bsmData.bsmId;const newState={longitude:parseFloat(bsmData.partLong),latitude:parseFloat(bsmData.partLat),speed:parseFloat(bsmData.partSpeed),heading:parseFloat(bsmData.partHeading)};// console.log('解析后的车辆状态:', newState);\n// console.log('车辆ID:', bsmid);\n// 通知RealTimeTraffic组件已收到真实BSM消息\nwindow.postMessage({type:'realBsmReceived',source:'CampusModel'},'*');// 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\nwindow.postMessage({type:'bsm',bsmId:bsmid,// 直接传递车辆ID\ndata:{// 同时提供完整的BSM数据\nbsmId:bsmid,partSpeed:bsmData.partSpeed,partLat:bsmData.partLat,partLong:bsmData.partLong,partHeading:bsmData.partHeading}},'*');// 获取模型位置坐标\nconst modelPos=converter.current.wgs84ToModel(newState.longitude,newState.latitude);const initialPosition=new THREE.Vector3(modelPos.x,1.0,-modelPos.y);const initialRotation=Math.PI-newState.heading*Math.PI/180;// 应用平滑滤波 - 使用已有的滤波函数\nconst newPosition=filterPosition(initialPosition,bsmid);const newRotation=filterRotation(initialRotation,bsmid);// 检查该车辆是否已存在于场景中\nlet vehicleObj=vehicleModels.get(bsmid);// 检查是否是主车\nconst isMainVehicle=bsmid===mainVehicleBsmId;if(!vehicleObj&&preloadedVehicleModel){// 创建一个新的车辆模型实例\nconst newVehicleModel=preloadedVehicleModel.clone();// 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n// 这样可以避免车辆突然出现的视觉冲击\nnewVehicleModel.position.set(newPosition.x,-5,newPosition.z);newVehicleModel.rotation.y=newRotation;// 设置BSM车辆为突出的颜色\nnewVehicleModel.traverse(child=>{if(child.isMesh&&child.material){// // 保存原始材质颜色\n// if (!child.userData.originalColor && child.material.color) {\n//   child.userData.originalColor = child.material.color.clone();\n// }\n// // 设置为更鲜艳的颜色\n// if (isMainVehicle) {\n//   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n// } else {\n//   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n// }\n// // 增加材质亮度\n// child.material.emissive = new THREE.Color(0x222222);\n// // 初始设置为半透明\n// child.material.transparent = true;\n// child.material.opacity = 0.6;\n// child.material.needsUpdate = true;\nconst newMaterial=child.material.clone();child.material=newMaterial;// 修改颜色逻辑（与原模型解耦）\nif(isMainVehicle){newMaterial.color.set(0x00BFFF);}else{newMaterial.color.set(0xFF6347);}newMaterial.emissive=new THREE.Color(0x222222);newMaterial.transparent=true;// newMaterial.opacity = 0.6;\nnewMaterial.needsUpdate=true;}});// 创建速度显示标签\nconst speedLabel=createTextSprite(`${Math.round(newState.speed)} km/h`,{backgroundColor:isMainVehicle?{r:0,g:191,b:255,a:0.8}:// 主车使用蓝色背景\n{r:255,g:99,b:71,a:0.8},// 其他车辆使用红色背景\ntextColor:{r:255,g:255,b:255,a:1.0},// 白色文本\nfontSize:20,padding:8});speedLabel.position.set(0,7,0);// 位于车辆顶部上方\nspeedLabel.renderOrder=1000;// 确保在最上层渲染\nspeedLabel.material.opacity=0.6;// 初始半透明\nnewVehicleModel.add(speedLabel);scene.add(newVehicleModel);// 保存车辆引用到车辆模型集合中\nvehicleModels.set(bsmid,{model:newVehicleModel,lastUpdate:Date.now(),type:'1',// 设置为机动车类型\nisMain:isMainVehicle,speedLabel:speedLabel// 保存速度标签引用\n});// console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n// 使用补间动画使车辆从地下逐渐显示出来\nnew TWEEN.Tween(newVehicleModel.position).to({y:0.5},500).easing(TWEEN.Easing.Quadratic.Out).start();// 使用补间动画使车辆从半透明变为完全不透明\nnewVehicleModel.traverse(child=>{if(child.isMesh&&child.material&&child.material.transparent){new TWEEN.Tween({opacity:0.6}).to({opacity:1.0},500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function(){child.material.opacity=this.opacity;child.material.needsUpdate=true;}).start();}});// 为速度标签也添加透明度动画\nnew TWEEN.Tween({opacity:0.6}).to({opacity:1.0},500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function(){speedLabel.material.opacity=this.opacity;speedLabel.material.needsUpdate=true;}).start();// 如果是主车，设置全局引用\nif(isMainVehicle){globalVehicleRef=newVehicleModel;setVehicleState(newState);console.log('设置全局主车引用:',bsmid);}}else if(vehicleObj){// 应用滤波\nconst filteredPosition=filterPosition(newPosition,bsmid);const filteredRotation=filterRotation(newRotation,bsmid);// 更新现有车辆位置和朝向\nvehicleObj.model.position.copy(filteredPosition);vehicleObj.model.rotation.y=filteredRotation;vehicleObj.model.updateMatrix();vehicleObj.model.updateMatrixWorld(true);vehicleObj.lastUpdate=Date.now();vehicleObj.isMain=isMainVehicle;// 更新主车状态\n// 更新速度标签文本\nif(vehicleObj.speedLabel){vehicleObj.speedLabel.material.map.dispose();vehicleObj.model.remove(vehicleObj.speedLabel);}// 创建新的速度标签\nconst speedLabel=createTextSprite(`${Math.round(newState.speed*3.6)} km/h`,{backgroundColor:isMainVehicle?{r:0,g:191,b:255,a:0.8}:// 主车使用蓝色背景\n{r:255,g:99,b:71,a:0.8},// 其他车辆使用红色背景\ntextColor:{r:255,g:255,b:255,a:1.0},// 白色文本\nfontSize:20,padding:8});speedLabel.position.set(0,15,0);// 位于车辆顶部上方\nspeedLabel.renderOrder=1000;// 确保在最上层渲染\nvehicleObj.model.add(speedLabel);vehicleObj.speedLabel=speedLabel;// console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n// 如果是主车，同时更新全局引用和状态（用于相机跟随）\nif(isMainVehicle){globalVehicleRef=vehicleObj.model;setVehicleState(newState);}}// 清理长时间未更新的车辆\nconst now=Date.now();const CLEANUP_THRESHOLD=1000;// 增加到10秒，避免频繁清理造成闪烁\nvehicleModels.forEach((modelData,id)=>{const timeSinceLastUpdate=now-modelData.lastUpdate;// 对于即将超时的车辆，先降低透明度，而不是直接移除\nif(timeSinceLastUpdate>CLEANUP_THRESHOLD*0.7&&timeSinceLastUpdate<=CLEANUP_THRESHOLD){// const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\nconst opacity=1;modelData.model.traverse(child=>{if(child.isMesh&&child.material){// 如果材质还没有透明度设置，先保存初始状态\nif(child.material.transparent===undefined){child.material.originalTransparent=child.material.transparent||false;child.material.originalOpacity=child.material.opacity||1.0;}// 设置透明度\nchild.material.transparent=true;child.material.opacity=opacity;child.material.needsUpdate=true;}});// 如果有速度标签，也调整其透明度\nif(modelData.speedLabel){modelData.speedLabel.material.opacity=opacity;modelData.speedLabel.material.needsUpdate=true;}}// 完全超时，移除车辆\nelse if(timeSinceLastUpdate>CLEANUP_THRESHOLD){// 清理资源\nif(modelData.speedLabel){modelData.speedLabel.material.map.dispose();modelData.speedLabel.material.dispose();modelData.model.remove(modelData.speedLabel);}modelData.model.traverse(child=>{if(child.isMesh){if(child.material){if(Array.isArray(child.material)){child.material.forEach(m=>m.dispose());}else{child.material.dispose();}}if(child.geometry)child.geometry.dispose();}});// 从场景中移除\nscene.remove(modelData.model);vehicleModels.delete(id);// 同时清除该车辆的滤波缓存\nvehicleLastPositions.delete(id);vehicleLastRotations.delete(id);console.log(`移除长时间未更新的车辆: ID ${id}`);}});return;}// SPAT消息处理\nif(topic===MQTT_CONFIG.spat){// console.log('收到SPAT消息:', message);\ntry{const payload=JSON.parse(message);// 检查设备时间戳\nconst deviceMac=payload.mac;const messageTimestamp=payload.tm;// 获取该设备的最新时间戳\nconst lastTimestamp=deviceTimestamps.get(deviceMac);// 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\nif(lastTimestamp&&messageTimestamp<lastTimestamp){// console.log('忽略过期的SPAT消息:', {\n//   设备MAC: deviceMac,\n//   消息时间戳: messageTimestamp,\n//   最新时间戳: lastTimestamp\n// });\nreturn;}// 更新设备时间戳\ndeviceTimestamps.set(deviceMac,messageTimestamp);// 修改：访问data.intersections而不是直接访问intersections\nif(payload.data&&payload.data.intersections&&Array.isArray(payload.data.intersections)){payload.data.intersections.forEach(intersection=>{const interId=intersection.interId;if(!interId){console.error('SPAT消息缺少interId:',intersection);return;}// console.log(`处理路口ID: ${interId} 的SPAT消息`);\n// 创建所有相位的数组 - 存储到trafficLightStates\nif(intersection.phases&&Array.isArray(intersection.phases)){// 构建存储相位信息的数组\nconst phasesInfo=[];intersection.phases.forEach(phase=>{// 修改：使用phaseId而不是id\nif(!phase.phaseId){console.error('相位信息缺少phaseId:',phase);return;}const phaseId=phase.phaseId.toString();// 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\nconst direction=phase.trafficDirec?getDirectionFromCode(phase.trafficDirec):getPhaseDirection(phaseId);// 修改：直接从phase中获取信号灯状态和剩余时间\nconst lightState=phase.trafficLight||'R';// 默认为红灯\nconst remainTime=parseInt(phase.remainTime)||0;// console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n// 构建相位信息对象\nconst phaseInfo={phaseId,direction,trafficLight:lightState,remainTime};// 添加到相位信息数组\nphasesInfo.push(phaseInfo);// 查找红绿灯模型并更新视觉效果\n// 尝试使用字符串ID和数字ID查找\nlet trafficLightKey=String(interId);let trafficLightModel=trafficLightsMap.get(trafficLightKey);if(!trafficLightModel){// 尝试使用数字ID\ntrafficLightKey=parseInt(interId);trafficLightModel=trafficLightsMap.get(trafficLightKey);}if(trafficLightModel){// 更新交通灯视觉效果\nupdateTrafficLightVisual(trafficLightModel,phaseInfo);// 更新弹出窗信息\nif(selectedIntersection&&selectedIntersection.interId===interId){setTrafficLightPopover(prev=>({...prev,visible:true,phaseId,direction,state:lightState,remainTime}));}}else{// console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n}});// 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\nlet modelKey=null;// 尝试字符串ID\nconst strId=String(interId);if(trafficLightsMap.has(strId)){modelKey=strId;}else{// 尝试数字ID\nconst numId=parseInt(interId);if(trafficLightsMap.has(numId)){modelKey=numId;}}if(modelKey!==null){// 使用正确的ID类型存储状态信息\ntrafficLightStates.set(modelKey,{updateTime:Date.now(),phases:phasesInfo});console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);// 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\nif(window.currentPopoverIdRef&&(window.currentPopoverIdRef.current===modelKey||window.currentPopoverIdRef.current===String(modelKey)||window.currentPopoverIdRef.current===parseInt(modelKey))){console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);// 强制更新弹窗ID为当前数据的正确ID类型\nwindow.currentPopoverIdRef.current=modelKey;// 如果没有更新定时器则创建一个\nif(window.trafficLightUpdateTimerRef&&!window.trafficLightUpdateTimerRef.current){console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);setTimeout(()=>{window.showTrafficLightPopup(modelKey);},100);}}}else{// 如果找不到模型，仍使用原始ID存储\ntrafficLightStates.set(interId,{updateTime:Date.now(),phases:phasesInfo});// console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n}}else{console.error('SPAT消息缺少相位信息:',intersection);}});}else{console.error('SPAT消息格式错误，缺少data.intersections数组:',payload);}}catch(error){console.error('解析SPAT消息出错:',error,message);}}// 处理 RSI 消息\nif(topic===MQTT_CONFIG.rsi&&payload.type==='RSI'){// console.log('收到RSI消息:', payload);\n// 发送 RSI 消息到 RealTimeTraffic 组件\nwindow.postMessage({type:'RSI',data:payload.data},'*');const rsiData=payload.data;const rsuId=rsiData.rsuId;const events=rsiData.rtes||[];events.forEach(event=>{const eventId=event.rteId;const eventType=event.eventType;const description=event.description;const startTime=event.startTime;const endTime=event.endTime;// 将基准点经纬度转换为模型坐标\nconst modelPos=converter.current.wgs84ToModel(parseFloat(rsiData.posLong),parseFloat(rsiData.posLat));// 根据事件类型显示不同的提示或标记\nlet warningText='';let warningColor='';switch(eventType){case'401':// 道路抛洒物\nwarningText='道路抛洒物';warningColor='#ff4d4f';break;case'404':// 道路障碍物\nwarningText='道路障碍物';warningColor='#faad14';break;case'405':// 行人通过马路\nwarningText='行人通过马路';warningColor='#1890ff';break;case'904':// 逆行车辆\nwarningText='逆行车辆';warningColor='#f5222d';break;case'910':// 违停车辆\nwarningText='违停车辆';warningColor='#722ed1';break;case'1002':// 道路施工\nwarningText='道路施工';warningColor='#fa8c16';break;case'901':// 车辆超速\nwarningText='车辆超速';warningColor='#eb2f96';break;default:warningText=description||'未知事件';warningColor='#8c8c8c';}// 显示警告标记\nshowWarningMarker(modelPos,warningText,warningColor);// console.log('RSI事件处理:', {\n//   事件ID: eventId,\n//   事件类型: eventType,\n//   事件说明: description,\n//   开始时间: startTime,\n//   结束时间: endTime,\n//   位置: modelPos\n// });\n});return;}// 处理场景事件消息\nif(topic===MQTT_CONFIG.scene&&payload.type==='SCENE'){// console.log('收到场景事件消息:', payload);\nconst sceneData=payload.data;const sceneId=sceneData.sceneId;const sceneType=sceneData.sceneType;const sceneDesc=sceneData.sceneDesc;const position={latitude:parseFloat(sceneData.partLat),longitude:parseFloat(sceneData.partLong)};// 将经纬度转换为模型坐标\nconst modelPos=converter.current.wgs84ToModel(position.longitude,position.latitude);// 根据场景类型显示不同的提示或标记\nswitch(sceneType){case'2':// 交叉路口碰撞预警\nshowWarningMarker(modelPos,'交叉路口碰撞预警','#ff4d4f');break;case'9-5':// 道路危险状况预警（施工）\nshowWarningMarker(modelPos,'道路施工','#faad14');break;case'9-6':// 前方有障碍物\nshowWarningMarker(modelPos,'前方障碍物','#ff7a45');break;case'10':// 限速提醒\nconst speedLimit=sceneData.eventData1;// 限速值\nshowWarningMarker(modelPos,`限速${speedLimit}km/h`,'#1890ff');break;case'12':// 交通参与者碰撞预警\nshowWarningMarker(modelPos,'碰撞预警','#f5222d');break;case'13':// 绿波车速引导\nshowWarningMarker(modelPos,'绿波引导','#52c41a');break;case'21-8':// 禁止鸣笛\nshowWarningMarker(modelPos,'禁止鸣笛','#722ed1');break;case'34':// 逆行车辆提醒\nshowWarningMarker(modelPos,'逆行警告','#eb2f96');break;case'33':// 违章占道车辆预警\nshowWarningMarker(modelPos,'违章占道','#fa8c16');break;case'999':// 信号灯优先\nconst priorityType=sceneData.eventData1;// 优先类型\nconst duration=sceneData.eventData2;// 优先时长\nshowWarningMarker(modelPos,`信号优先-${getPriorityTypeText(priorityType)}${duration}秒`,'#13c2c2');break;}return;}// 如果不是RSM或BSM消息，则记录为其他类型\n// console.log('未知类型消息:', {\n//   topic,\n//   type: payload.type,\n//   data: payload\n// });\n}catch(error){console.error('处理MQTT消息失败:',error);console.error('原始消息内容:',message);}};// 修改初始化MQTT连接函数\nconst initMqttClient=()=>{console.log('正在连接MQTT服务器...');const wsUrl=`ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;console.log('尝试连接WebSocket:',wsUrl);// 创建WebSocket连接，不指定协议\nconst ws=new WebSocket(wsUrl);ws.onopen=()=>{console.log('WebSocket连接成功');};ws.onmessage=event=>{try{const message=JSON.parse(event.data);// 处理连接确认消息\nif(message.type==='connect'){console.log('收到连接确认:',message);return;}// 处理心跳消息\nif(message.type==='ping'){return;}// 处理MQTT消息\nif(message.type==='message'&&message.topic&&message.payload){// 直接将消息传递给handleMqttMessage处理\nhandleMqttMessage(message.topic,JSON.stringify(message.payload));}}catch(error){console.error('处理WebSocket消息失败:',error);}};ws.onerror=error=>{console.error('WebSocket错误:',error);};ws.onclose=()=>{console.log('WebSocket连接关闭');// 5秒后尝试重新连接\nsetTimeout(initMqttClient,5000);};// 保存WebSocket引用\nmqttClientRef.current=ws;};useEffect(()=>{if(!containerRef.current)return;// 预加载所有模型\npreloadModels();// 创建场景\nscene=new THREE.Scene();// 使用全局scene变量\n// 创建相机\nconst camera=new THREE.PerspectiveCamera(60,window.innerWidth/window.innerHeight,0.1,2000);// camera.position.set(0, 300, 0); // 初始为全局视角\ncamera.position.set(0,100,0);// 初始为全局视角\ncamera.lookAt(0,0,0);cameraRef.current=camera;// 创建渲染器\nconst renderer=new THREE.WebGLRenderer({antialias:true});renderer.setSize(window.innerWidth,window.innerHeight);renderer.setClearColor(0x000000);renderer.setPixelRatio(window.devicePixelRatio);containerRef.current.appendChild(renderer.domElement);// 修改光照设置\n// 添加环境光和平行光\nconst ambientLight=new THREE.AmbientLight(0xffffff,0.8);// 增加环境光强度从0.5到0.8\nscene.add(ambientLight);// 添加多个平行光源，从不同角度照亮车辆\nconst directionalLight1=new THREE.DirectionalLight(0xffffff,1.0);// 增加强度从0.8到1.0\ndirectionalLight1.position.set(10,10,10);scene.add(directionalLight1);// 添加第二个平行光源，从另一个角度照亮\nconst directionalLight2=new THREE.DirectionalLight(0xffffff,0.8);directionalLight2.position.set(-10,8,-10);scene.add(directionalLight2);// 添加一个聚光灯，专门照亮车辆\nconst spotLight=new THREE.SpotLight(0xffffff,1.0);spotLight.position.set(0,50,0);spotLight.angle=Math.PI/4;spotLight.penumbra=0.1;spotLight.decay=2;spotLight.distance=200;scene.add(spotLight);// 创建控制器\ncontrols=new OrbitControls(camera,renderer.domElement);controls.enableDamping=true;controls.dampingFactor=0.05;controls.screenSpacePanning=false;controls.minDistance=50;controls.maxDistance=500;controls.maxPolarAngle=Math.PI/2.1;controls.minPolarAngle=0;controls.target.set(0,0,0);controls.update();// 打印相机和控制器引用\nconsole.log('初始化完成',{camera:!!camera,controls:!!controls,cameraRef:!!cameraRef.current});// 修改加载车辆模型的函数\nconst loadVehicleModel=()=>{return new Promise((resolve,reject)=>{const vehicleLoader=new GLTFLoader();vehicleLoader.load(`${BASE_URL}/changli2/vehicle.glb`,gltf=>{const vehicleModel=gltf.scene;// 创建一个新的Group作为根容器\nconst vehicleContainer=new THREE.Group();// 调整模型材质\nvehicleModel.traverse(child=>{if(child.isMesh){// 检查并调整材质\nif(child.material){// 创建新的标准材质\nconst newMaterial=new THREE.MeshStandardMaterial({color:0xffffff,// 白色\nmetalness:0.2,// 降低金属感\nroughness:0.1,// 降低粗糙度\nenvMapIntensity:1.0// 环境贴图强度\n});// 保留原始贴图\nif(child.material.map){newMaterial.map=child.material.map;}// 应用新材质\nchild.material=newMaterial;console.log('已调整车辆材质:',child.name);}}});// 遍历模型的所有子对象，确保它们都被正确添加到容器中\nwhile(vehicleModel.children.length>0){const child=vehicleModel.children[0];vehicleContainer.add(child);}// 确保容器直接添加到场景根节点\nscene.add(vehicleContainer);// 保存容器的引用\nglobalVehicleRef=vehicleContainer;console.log('车辆模型加载成功，使用容器包装');setIsVehicleLoaded(true);resolve(vehicleContainer);},xhr=>{console.log(`车辆模型加载进度: ${(xhr.loaded/xhr.total*100).toFixed(2)}%`);},reject);});};// 修改初始化流程\nconst initializeScene=async()=>{try{// 1. 加载车辆模型\n// const vehicleContainer = await loadVehicleModel();\n// 2. 初始化MQTT客户端\ninitMqttClient();// 3. 设置初始位置\n// if (vehicleContainer) {\n//   const initialState = {\n//     longitude: 113.0022348,\n//     latitude: 28.0698301,\n//     heading: 0\n//   };\n//   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n//   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n//   vehicleContainer.position.set(0, 1.0, 0);\n//   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n//   vehicleContainer.updateMatrix();\n//   vehicleContainer.updateMatrixWorld(true);\n//   currentPosition = vehicleContainer.position.clone();\n// }\n}catch(error){console.error('初始化场景失败:',error);}};// 添加重试逻辑的加载函数\nconst loadModelWithRetry=function(url){let maxRetries=arguments.length>1&&arguments[1]!==undefined?arguments[1]:3;return new Promise((resolve,reject)=>{const attemptLoad=retriesLeft=>{console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);const loader=new GLTFLoader();loader.load(url,gltf=>{console.log(`模型加载成功: ${url}`);resolve(gltf);},xhr=>{console.log(`加载进度: ${(xhr.loaded/xhr.total*100).toFixed(2)}%`);},error=>{console.error(`加载失败: ${url}`,error);if(retriesLeft>0){console.log(`将在 1 秒后重试...`);setTimeout(()=>attemptLoad(retriesLeft-1),1000);}else{reject(error);}});};attemptLoad(maxRetries);});};// 使用重试逻辑加载模型\nconst loader=new GLTFLoader();loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`,async gltf=>{try{const model=gltf.scene;model.scale.set(1,1,1);model.position.set(0,0,0);// 检查scene是否初始化\nif(scene){scene.add(model);// 在校园模型加载完成后初始化场景\nawait initializeScene();}else{console.error('无法添加模型：场景未初始化');}}catch(error){console.error('处理模型时出错:',error);}},xhr=>{console.log(`加载进度: ${(xhr.loaded/xhr.total*100).toFixed(2)}%`);},error=>{console.error('模型加载错误:',error);console.error('错误详情:',{错误类型:error.type,错误消息:error.message,加载URL:`${BASE_URL}/changli2/CLG_ALL_1025.glb`,完整URL:`${BASE_URL}/changli2/CLG_ALL_1025.glb`});});// 修改动画循环\nconst animate=()=>{animationFrameRef.current=requestAnimationFrame(animate);// 更新 TWEEN 动画\nTWEEN.update();// 更新行人动画\nconst currentTime=Date.now();// const deltaTime = (currentTime - prevAnimationTimeRef.current) * 10;\nconst deltaTime=clock.getDelta();prevAnimationTimeRef.current=currentTime;peopleAnimationMixers.current.forEach(mixer=>{mixer.update(deltaTime);});if(cameraMode==='follow'&&globalVehicleRef){// 在跟随模式下禁用控制器\ncontrols.enabled=false;// 获取车辆当前位置\nconst vehiclePos=globalVehicleRef.position.clone();// 获取车辆旋转角度\nconst vehicleRotation=globalVehicleRef.rotation.y;// 计算相机偏移量\n// 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\nconst adjustedRotation=-(vehicleRotation-Math.PI)+Math.PI/2*3;// 增加距离到 50 单位\nconst cameraOffset=new THREE.Vector3(-50*Math.cos(adjustedRotation),200,-50*Math.sin(adjustedRotation));// 计算目标相机位置和观察点\nconst targetCameraPosition=vehiclePos.clone().add(cameraOffset);const targetLookAt=vehiclePos.clone();// 初始化上一帧数据（如果是首次）\nif(!lastCameraPosition.current){lastCameraPosition.current=targetCameraPosition.clone();}if(!lastCameraTarget.current){lastCameraTarget.current=targetLookAt.clone();}// 应用平滑处理 - 使用lerp进行线性插值\nlastCameraPosition.current.lerp(targetCameraPosition,1-cameraSmoothing);lastCameraTarget.current.lerp(targetLookAt,1-cameraSmoothing);// 设置相机位置为平滑后的位置\ncamera.position.copy(lastCameraPosition.current);// 重置相机方向\ncamera.up.set(0,1,0);// 设置相机观察点为平滑后的目标\ncamera.lookAt(lastCameraTarget.current);// 强制更新相机矩阵\ncamera.updateProjectionMatrix();camera.updateMatrix();camera.updateMatrixWorld(true);// 禁用控制器\ncontrols.enabled=false;// 确保控制器不会覆盖相机设置\ncontrols.target.copy(lastCameraTarget.current);controls.update();console.log('相机设置:',{车辆位置:vehiclePos.toArray(),相机位置:camera.position.toArray(),相机目标:lastCameraTarget.current.toArray(),相机朝向:camera.getWorldDirection(new THREE.Vector3()).toArray()});}else if(cameraMode==='global'){// 在全局模式或切换模式时重置平滑变量\nlastCameraPosition.current=null;lastCameraTarget.current=null;// 在全局模式下启用控制器\ncontrols.enabled=true;// 确保相机的up向量保持正确\ncamera.up.set(0,1,0);// 如果相机位置偏离太多，重置到默认位置\nif(Math.abs(camera.position.y)<50){camera.position.set(0,300,0);controls.target.set(0,0,0);camera.lookAt(controls.target);controls.update();}//         // 强制更新相机矩阵\n// camera.updateProjectionMatrix();\ncamera.updateMatrix();camera.updateMatrixWorld(true);}else if(cameraMode==='intersection'){// 在路口视角模式下也重置平滑变量\nlastCameraPosition.current=null;lastCameraTarget.current=null;// 路口视角模式\ncontrols.update();}if(controls)controls.update();if(scene&&camera){renderer.render(scene,camera);}};animate();// 处理窗口大小变化\nconst handleResize=()=>{camera.aspect=window.innerWidth/window.innerHeight;camera.updateProjectionMatrix();renderer.setSize(window.innerWidth,window.innerHeight);};window.addEventListener('resize',handleResize);// 添加全局函数用于手动切换视角\nwindow.setGlobalView=()=>{if(cameraRef.current){cameraRef.current.position.set(0,300,0);cameraRef.current.lookAt(0,0,0);cameraRef.current.updateMatrix();cameraRef.current.updateMatrixWorld(true);if(controls){controls.target.set(0,0,0);controls.enabled=true;controls.update();}cameraMode='global';console.log('手动切换到全局视角');return true;}return false;};// 修改清理函数\nreturn()=>{console.log('开始清理组件...');// 1. 首先停止动画循环\nif(animationFrameRef.current){cancelAnimationFrame(animationFrameRef.current);animationFrameRef.current=null;}// 2. 清理定时器\nif(globalUpdateInterval){clearInterval(globalUpdateInterval);globalUpdateInterval=null;}// 3. 关闭 WebSocket 连接\nif(mqttClientRef.current){mqttClientRef.current.close();mqttClientRef.current=null;}// 4. 移除事件监听器\nwindow.removeEventListener('resize',handleResize);// 5. 清理渲染器\nif(renderer&&containerRef.current){containerRef.current.removeChild(renderer.domElement);renderer.dispose();}// 6. 清理场景中的所有车辆模型\nif(vehicleModels){vehicleModels.forEach((modelData,id)=>{if(modelData.model&&scene){scene.remove(modelData.model);}});vehicleModels.clear();}// 7. 清理红绿灯模型\ntrafficLightsMap.forEach(lightObj=>{if(scene&&lightObj.model){scene.remove(lightObj.model);}});trafficLightsMap.clear();trafficLightStates.clear();// 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n// 9. 重置全局变量\nscene=null;controls=null;preloadedVehicleModel=null;preloadedCyclistModel=null;preloadedPeopleModel=null;preloadedTrafficLightModel=null;globalVehicleRef=null;console.log('组件清理完成');};},[]);// 在组件挂载时获取主车信息和添加事件监听\nuseEffect(()=>{// 初始获取主车信息\nfetchMainVehicleBsmId();// 添加自定义事件监听，用于接收主车变更通知\nconst handleMainVehicleChange=()=>{console.log('接收到主车变更通知，重新获取主车信息');fetchMainVehicleBsmId();};// 监听主车变更事件\nwindow.addEventListener('mainVehicleChanged',handleMainVehicleChange);// 定时刷新主车信息（每分钟一次）\nconst intervalId=setInterval(()=>{fetchMainVehicleBsmId();},60000);// 组件卸载时清理事件监听和定时器\nreturn()=>{window.removeEventListener('mainVehicleChanged',handleMainVehicleChange);clearInterval(intervalId);};},[]);// 添加一个useEffect钩子在场景初始化完成后创建红绿灯\nuseEffect(()=>{// 在场景加载后初始化红绿灯\nif(scene&&converter.current){console.log('准备创建红绿灯模型');// 延迟一秒创建红绿灯，确保模型已加载\nconst timer=setTimeout(()=>{if(scene&&converter.current){// 再次检查，以防延迟期间组件卸载\ncreateTrafficLights(converter.current);}},2000);return()=>clearTimeout(timer);}else{console.log('场景或坐标转换器未准备好，暂不创建红绿灯');}},[scene]);// 添加点击事件处理\nuseEffect(()=>{if(containerRef.current){// 定义点击处理函数\nconst handleClick=event=>{if(scene&&cameraRef.current){handleMouseClick(event,containerRef.current,scene,cameraRef.current);}};// 添加点击事件监听\ncontainerRef.current.addEventListener('click',handleClick);// 记录到控制台\nconsole.log('已添加点击事件监听器到容器',!!containerRef.current);// 清理函数\nreturn()=>{if(containerRef.current){containerRef.current.removeEventListener('click',handleClick);console.log('已移除点击事件监听器');}};}},[scene,cameraRef.current]);// 初始化场景 - 简化为空函数，避免引用错误\nconst initScene=useCallback(()=>{console.log('initScene函数已禁用');// 原始实现已移除，避免canvasRef未定义的错误\n},[containerRef,setCurrentRSU,trafficLightsMap]);// 创建简单交通灯模型\nconst createSimpleTrafficLight=()=>{const geometry=new THREE.BoxGeometry(4,15,4);const material=new THREE.MeshBasicMaterial({color:0x333333});const trafficLightModel=new THREE.Mesh(geometry,material);// 添加基座\nconst baseGeometry=new THREE.CylinderGeometry(2,2,2,32);const baseMaterial=new THREE.MeshBasicMaterial({color:0x666666});const baseModel=new THREE.Mesh(baseGeometry,baseMaterial);baseModel.position.set(0,-8.5,0);trafficLightModel.add(baseModel);return trafficLightModel;};// 添加额外的点击检测辅助对象\nconst addClickHelpers=()=>{if(!scene)return;// 为每个红绿灯添加一个透明的大型碰撞体，便于点击\ntrafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){// 创建一个较大的碰撞检测几何体\nconst helperGeometry=new THREE.SphereGeometry(3,3,3);const helperMaterial=new THREE.MeshBasicMaterial({color:0xff00ff,//\ntransparent:false,opacity:0.1,// 几乎透明\ndepthWrite:false});const helperMesh=new THREE.Mesh(helperGeometry,helperMaterial);helperMesh.position.set(0,0,0);// 放在红绿灯位置\n// 标记为click helper\nhelperMesh.userData={type:'trafficLight',interId:interId,name:lightObj.intersection.name,isClickHelper:true};// 添加到红绿灯模型\nlightObj.model.add(helperMesh);console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);}});};// 在创建红绿灯之后调用\nuseEffect(()=>{// 等待红绿灯创建完成后添加点击辅助对象\nconst timer=setTimeout(()=>{if(trafficLightsMap.size>0){console.log('添加红绿灯点击辅助对象');// addClickHelpers();\n}},5500);// 延迟略长于debugTrafficLights\nreturn()=>clearTimeout(timer);},[]);// // 在组件加载完毕后调用调试函数\n// useEffect(() => {\n//   // 延迟5秒调用调试函数，确保所有模型都已加载\n//   const timer = setTimeout(() => {\n//     console.log('调用场景调试函数');\n//     if (window.debugScene) {\n//       window.debugScene(); // 使用全局函数\n//     } else {\n//       console.error('debugScene函数未定义');\n//     }\n//   }, 5000);\n//   return () => clearTimeout(timer);\n// }, []);\n// 在useEffect中添加定时器清理逻辑\nuseEffect(()=>{return()=>{// 组件卸载时清理定时器\nif(trafficLightUpdateTimerRef.current){clearInterval(trafficLightUpdateTimerRef.current);trafficLightUpdateTimerRef.current=null;console.log('已清理红绿灯状态更新定时器');}};},[]);// 空依赖数组确保只在组件挂载和卸载时运行\n// 添加关闭弹窗时清理定时器的逻辑\nuseEffect(()=>{if(!trafficLightPopover.visible&&trafficLightUpdateTimerRef.current){clearInterval(trafficLightUpdateTimerRef.current);trafficLightUpdateTimerRef.current=null;currentPopoverIdRef.current=null;console.log('弹窗关闭，已清理红绿灯状态更新定时器');}},[trafficLightPopover.visible]);// 添加自动选择第一个路口的逻辑\nuseEffect(()=>{// 确保路口数据已加载\nif(intersectionsData&&intersectionsData.intersections&&intersectionsData.intersections.length>0){// 确保只在组件初次渲染并且未选择路口时执行\nif(!selectedIntersection){const firstIntersection=intersectionsData.intersections[0];console.log('自动选择第一个路口:',firstIntersection.name);// 延迟执行，确保场景和相机已初始化\nconst timer=setTimeout(()=>{handleIntersectionChange(firstIntersection.name);},2000);return()=>clearTimeout(timer);}}},[intersectionsData,selectedIntersection]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{style:labelStyle,children:\"\\u533A\\u57DF\\u9009\\u62E9\\uFF1A\"}),/*#__PURE__*/_jsx(Select,{style:intersectionSelectStyle,placeholder:\"\\u8BF7\\u9009\\u62E9\\u533A\\u57DF\\u4F4D\\u7F6E\",onChange:handleIntersectionChange,options:intersectionsData.intersections.map(intersection=>({value:intersection.name,label:intersection.name})),size:\"large\",bordered:true,dropdownStyle:{zIndex:1002,maxHeight:'300px'},value:selectedIntersection?selectedIntersection.name:undefined}),/*#__PURE__*/_jsx(\"div\",{ref:containerRef,style:{width:'100%',height:'100%'}}),trafficLightPopover.visible&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',left:`${trafficLightPopover.position.x}px`,top:`${trafficLightPopover.position.y}px`,transform:'translate(-50%, -100%)',zIndex:1003,backgroundColor:'rgba(0, 0, 0, 0.85)',color:'white',borderRadius:'4px',boxShadow:'0 2px 8px rgba(0, 0, 0, 0.3)',padding:'0',maxWidth:'240px',// 缩小最大宽度\nfontSize:'12px'// 缩小字体\n},children:[trafficLightPopover.content,/*#__PURE__*/_jsx(\"button\",{style:{position:'absolute',top:'0px',right:'0px',background:'none',border:'none',color:'white',fontSize:'12px',cursor:'pointer',padding:'2px 6px'},onClick:()=>handleClosePopover(setTrafficLightPopover),children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:buttonContainerStyle,children:[/*#__PURE__*/_jsx(\"button\",{style:{...buttonStyle,backgroundColor:viewMode==='follow'?'#1890ff':'rgba(255, 255, 255, 0.9)',color:viewMode==='follow'?'white':'black'},onClick:switchToFollowView,children:\"\\u8DDF\\u968F\\u89C6\\u89D2\"}),/*#__PURE__*/_jsx(\"button\",{style:{...buttonStyle,backgroundColor:viewMode==='global'?'#1890ff':'rgba(255, 255, 255, 0.9)',color:viewMode==='global'?'white':'black'},onClick:switchToGlobalView,children:\"\\u5168\\u5C40\\u89C6\\u89D2\"})]})]});};// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text){let parameters=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const params={fontFace:parameters.fontFace||'Arial',fontSize:parameters.fontSize||16,// 从24px调小到16px\nfontWeight:parameters.fontWeight||'bold',borderThickness:parameters.borderThickness||4,borderColor:parameters.borderColor||{r:0,g:0,b:0,a:1.0},backgroundColor:parameters.backgroundColor||{r:255,g:255,b:255,a:0.8},textColor:parameters.textColor||{r:0,g:0,b:0,a:1.0},padding:parameters.padding||5};// 创建画布\nconst canvas=document.createElement('canvas');const context=canvas.getContext('2d');// 设置字体\ncontext.font=`${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;// 测量文本宽度\nconst textWidth=context.measureText(text).width;// 设置画布尺寸，考虑边框和填充\nconst width=textWidth+2*params.padding+2*params.borderThickness;const height=params.fontSize+2*params.padding+2*params.borderThickness;canvas.width=width;canvas.height=height;// 重新设置字体，因为改变画布尺寸会重置上下文\ncontext.font=`${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;context.textBaseline='middle';// 绘制背景和边框（圆角矩形）\nconst radius=8;context.beginPath();context.moveTo(params.borderThickness+radius,params.borderThickness);context.lineTo(width-params.borderThickness-radius,params.borderThickness);context.arcTo(width-params.borderThickness,params.borderThickness,width-params.borderThickness,params.borderThickness+radius,radius);context.lineTo(width-params.borderThickness,height-params.borderThickness-radius);context.arcTo(width-params.borderThickness,height-params.borderThickness,width-params.borderThickness-radius,height-params.borderThickness,radius);context.lineTo(params.borderThickness+radius,height-params.borderThickness);context.arcTo(params.borderThickness,height-params.borderThickness,params.borderThickness,height-params.borderThickness-radius,radius);context.lineTo(params.borderThickness,params.borderThickness+radius);context.arcTo(params.borderThickness,params.borderThickness,params.borderThickness+radius,params.borderThickness,radius);context.closePath();// 设置边框颜色\ncontext.strokeStyle=`rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;context.lineWidth=params.borderThickness;context.stroke();// 设置背景填充\ncontext.fillStyle=`rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;context.fill();// 设置文字颜色\ncontext.fillStyle=`rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;context.textAlign='center';// 绘制文本\ncontext.fillText(text,width/2,height/2);// 创建纹理\nconst texture=new THREE.CanvasTexture(canvas);texture.minFilter=THREE.LinearFilter;texture.needsUpdate=true;// 创建精灵材质\nconst spriteMaterial=new THREE.SpriteMaterial({map:texture,transparent:true});// 创建精灵\nconst sprite=new THREE.Sprite(spriteMaterial);sprite.scale.set(7,3.5,1);// 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\nsprite.material.depthTest=false;// 确保始终可见\n// 保存文本信息到userData，便于后续更新\nsprite.userData={text:text,params:params};return sprite;}// 在文件底部添加这个全局函数\nwindow.forceGlobalView=()=>{try{// 获取当前场景中的相机\nconst camera=document.querySelector('canvas').parentElement.__THREE_camera;if(camera){// 保存旧位置\nconst oldPos=camera.position.clone();// 设置新位置\ncamera.position.set(0,300,0);camera.up.set(0,1,0);camera.lookAt(0,0,0);// 更新矩阵\ncamera.updateMatrix();camera.updateMatrixWorld(true);// 更新控制器\nconst controls=document.querySelector('canvas').parentElement.__THREE_controls;if(controls){controls.target.set(0,0,0);controls.update();}console.log('强制设置全局视角成功',{旧位置:oldPos.toArray(),新位置:camera.position.toArray()});return true;}return false;}catch(e){console.error('强制设置全局视角失败',e);return false;}};// 修改车辆模型预加载函数\nconst preloadModels=async()=>{try{console.log('开始预加载所有模型...');const loader=new GLTFLoader();// 并行加载所有模型\ntry{const[trafficLightGltf,vehicleGltf,cyclistGltf,peopleGltf]=await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`),loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`),loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),loader.loadAsync(`${BASE_URL}/changli2/people.glb`)]);// 处理机动车模型\nconsole.log('加载车辆模型...');preloadedVehicleModel=vehicleGltf.scene;preloadedVehicleModel.traverse(child=>{if(child.isMesh){const newMaterial=new THREE.MeshStandardMaterial({color:0xff0000,// 0xff0000, //红色 0xffffff,白色\nmetalness:0.2,roughness:0.1,envMapIntensity:1.0});// 保留原始贴图\nif(child.material.map){newMaterial.map=child.material.map;}child.materia=newMaterial;}});console.log('加载非机动车模型...');// 处理非机动车模型\npreloadedCyclistModel=cyclistGltf.scene;// 设置非机动车模型的缩放\npreloadedCyclistModel.scale.set(2,2,2);// 保持原始材质\npreloadedCyclistModel.traverse(child=>{if(child.isMesh&&child.material){// 只调整材质属性，保持原始颜色\nchild.material.metalness=0.1;child.material.roughness=0.8;child.material.envMapIntensity=1.0;}});console.log('加载行人模型...');// 处理行人模型\npreloadedPeopleModel=peopleGltf.scene;// 设置行人模型的缩放\n// preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n// 保持原始材质\npreloadedPeopleModel.traverse(child=>{if(child.isMesh&&child.material){// 只调整材质属性，保持原始颜色\nchild.material.metalness=0.1;child.material.roughness=0.8;child.material.envMapIntensity=1.0;}if(child.isMesh){child.castShadow=true;}});// 保存行人动画数据\nconsole.log('找到行人动画sss:',peopleGltf.animations.length,'个');if(peopleGltf.animations&&peopleGltf.animations.length>0){console.log('找到行人动画:',peopleGltf.animations.length,'个');peopleBaseModel=peopleGltf;}else{console.warn('行人模型没有包含动画数据');}console.log('加载红绿灯模型...');// 处理红绿灯模型\npreloadedTrafficLightModel=trafficLightGltf.scene;console.log('红绿灯模型：',preloadedTrafficLightModel);// 设置红绿灯模型的缩放\npreloadedTrafficLightModel.scale.set(6,6,6);// 保持原始材质\npreloadedTrafficLightModel.traverse(child=>{if(child.isMesh&&child.material){// 设置材质属性\nchild.material.metalness=0.2;child.material.roughness=0.3;child.material.envMapIntensity=1.2;}});console.log('所有模型预加载成功');}catch(error){console.error('加载特定模型失败，尝试单独加载:',error);// 如果整个Promise.all失败，尝试单独加载关键模型\ntry{if(!preloadedVehicleModel){const vehicleGltf=await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);preloadedVehicleModel=vehicleGltf.scene;}// // 尝试单独加载红绿灯模型\n// if (!preloadedTrafficLightModel) {\n//   console.log('正在单独加载红绿灯模型...');\n//   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n//   preloadedTrafficLightModel = trafficLightGltf.scene;\n//   preloadedTrafficLightModel.scale.set(3, 3, 3);\n//   console.log('红绿灯模型加载成功');\n// }\n}catch(err){console.error('单独加载模型也失败:',err);}}}catch(error){console.error('模型预加载失败:',error);}};// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText=type=>{const types={'1':'信号灯保持','2':'绿灯延长','3':'红灯截断','4':'相位插入','5':'相位插入','6':'优先未处理'};return types[type]||'未知类型';};// 添加辅助函数：显示警告标记\nconst showWarningMarker=(position,text,color)=>{// 添加安全检查 - 如果场景不存在，则直接返回\nif(!scene){console.warn('无法显示警告标记：场景不存在或已卸载');return;}try{// 创建一个新的警告标记\nconst sprite=createTextSprite(text);sprite.position.set(position.x,10,-position.y);// 设置位置，稍微升高以便可见\n// 为标记添加一个定时器，在1秒后自动移除\nsetTimeout(()=>{// 再次检查场景是否存在\nif(scene&&sprite.parent){scene.remove(sprite);}},100);// 将标记添加到场景中\nscene.add(sprite);// console.log('添加场景事件标记:', {\n//   位置: position,\n//   文本: text,\n//   颜色: color\n// });\n}catch(error){console.error('显示警告标记时出错:',error);}};// 添加创建红绿灯模型的函数\nconst createTrafficLights=converterInstance=>{if(!scene){console.error('无法创建红绿灯：场景未初始化');return;}if(!converterInstance){console.error('无法创建红绿灯：坐标转换器未初始化');return;}// 检查红绿灯模型是否已加载\nif(!preloadedTrafficLightModel){console.error('红绿灯模型未加载，尝试重新加载...');// 尝试重新加载红绿灯模型\nconst loader=new GLTFLoader();loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`).then(trafficLightGltf=>{preloadedTrafficLightModel=trafficLightGltf.scene;preloadedTrafficLightModel.scale.set(6,6,6);preloadedTrafficLightModel.traverse(child=>{if(child.isMesh&&child.material){child.material.metalness=0.2;child.material.roughness=0.3;child.material.envMapIntensity=1.2;}});console.log('红绿灯模型重新加载成功，开始创建红绿灯...');// 重新调用创建函数\ncreateTrafficLights(converterInstance);}).catch(error=>{console.error('红绿灯模型重新加载失败:',error);// 如果加载失败，使用简单的替代物体\ncreateFallbackTrafficLights(converterInstance);});return;}// 先清除现有的红绿灯\ntrafficLightsMap.forEach(lightObj=>{if(scene&&lightObj.model){scene.remove(lightObj.model);}});trafficLightsMap.clear();// 为每个路口创建红绿灯模型\nintersectionsData.intersections.forEach(intersection=>{if(intersection.hasTrafficLight===false){console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);return;}if(intersection.latitude&&intersection.longitude&&intersection.interId){const modelPos=converterInstance.wgs84ToModel(parseFloat(intersection.longitude),parseFloat(intersection.latitude));console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);try{// 确保模型存在且可以克隆\nif(!preloadedTrafficLightModel||!preloadedTrafficLightModel.clone){throw new Error('红绿灯模型无效或无法克隆');}// 创建红绿灯模型\nconst trafficLightModel=preloadedTrafficLightModel.clone();// 给模型一个名称便于调试\ntrafficLightModel.name=`交通灯-${intersection.name}`;// 设置位置，离地面高度为15米，提高可见性\ntrafficLightModel.position.set(modelPos.x,15,-modelPos.y);// 放大红绿灯模型尺寸，使其更容易被点击\ntrafficLightModel.scale.set(10,10,10);// 确保渲染顺序高，避免被其他对象遮挡\ntrafficLightModel.renderOrder=100;// 设置材质属性\ntrafficLightModel.traverse(child=>{if(child.isMesh){child.material.transparent=false;child.material.opacity=1.0;child.material.side=THREE.DoubleSide;child.material.depthWrite=true;child.material.depthTest=true;child.material.needsUpdate=true;child.renderOrder=100;}});// 添加交互所需的信息\ntrafficLightModel.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name};// 将红绿灯添加到场景中\nscene.add(trafficLightModel);// 存储红绿灯引用\ntrafficLightsMap.set(intersection.interId,{model:trafficLightModel,intersection:intersection,position:modelPos});console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);}catch(error){console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`,error);// 如果克隆失败，创建一个简单的替代物体\ncreateSimpleTrafficLight(intersection,modelPos,converterInstance);}}});// 在控制台输出所有红绿灯的信息\nconsole.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);trafficLightsMap.forEach((lightObj,interId)=>{console.log(`- ID ${interId}: ${lightObj.intersection.name}`);});};// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights=converterInstance=>{intersectionsData.intersections.forEach(intersection=>{// 跳过没有红绿灯的路口\nif(intersection.hasTrafficLight===false){return;}if(intersection.latitude&&intersection.longitude&&intersection.interId){// 转换经纬度到模型坐标\nconst modelPos=converterInstance.wgs84ToModel(parseFloat(intersection.longitude),parseFloat(intersection.latitude));createSimpleTrafficLight(intersection,modelPos,converterInstance);}});};// 创建简单的替代红绿灯\nconst createSimpleTrafficLight=(intersection,modelPos,converterInstance)=>{// 创建一个简单的几何体作为红绿灯 - 增大尺寸\nconst geometry=new THREE.BoxGeometry(10,30,10);const material=new THREE.MeshBasicMaterial({color:0x333333,transparent:false,opacity:1.0});const trafficLightModel=new THREE.Mesh(geometry,material);// 给模型一个名称便于调试\ntrafficLightModel.name=`简易交通灯-${intersection.name}`;// 设置位置 - 提高高度以增加可见性\ntrafficLightModel.position.set(modelPos.x,15,-modelPos.y);// 设置渲染顺序\ntrafficLightModel.renderOrder=100;// 添加交互所需的信息\ntrafficLightModel.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name};// 添加一个专门用于点击的大型碰撞体\nconst colliderGeometry=new THREE.SphereGeometry(3,12,12);const colliderMaterial=new THREE.MeshBasicMaterial({color:0xff00ff,transparent:true,opacity:0.0,// 完全透明\ndepthWrite:false});const collider=new THREE.Mesh(colliderGeometry,colliderMaterial);collider.name=`简易交通灯碰撞体-${intersection.name}`;collider.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name,isCollider:true};trafficLightModel.add(collider);// 将红绿灯添加到场景中\nscene.add(trafficLightModel);// 存储红绿灯引用\ntrafficLightsMap.set(intersection.interId,{model:trafficLightModel,intersection:intersection,position:modelPos});// 添加一个顶部灯光标识，使其更容易被看到\nconst lightGeometry=new THREE.SphereGeometry(5,16,16);const lightMaterial=new THREE.MeshBasicMaterial({color:0xFF0000});const lightMesh=new THREE.Mesh(lightGeometry,lightMaterial);lightMesh.position.set(0,15,0);// 给灯光相同的userData\nlightMesh.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name};trafficLightModel.add(lightMesh);console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);};// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection=phaseId=>{switch(phaseId){case'1':return'北进口左转';case'2':return'北进口直行';case'3':return'北进口右转';case'5':return'东进口左转';case'6':return'东进口直行';case'7':return'东进口右转';case'9':return'南进口左转';case'10':return'南进口直行';case'11':return'南进口右转';case'13':return'西进口左转';case'14':return'西进口直行';case'15':return'西进口右转';default:return`相位${phaseId}`;}};// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode=dirCode=>{switch(dirCode){case'N':return'北向南';case'S':return'南向北';case'E':return'东向西';case'W':return'西向东';case'NE':return'东北向西南';case'NW':return'西北向东南';case'SE':return'东南向西北';case'SW':return'西南向东北';default:return`方向${dirCode}`;}};// 修改点击处理函数\nconst handleMouseClick=(event,container,sceneInstance,cameraInstance)=>{if(!container||!sceneInstance||!cameraInstance)return;console.log('触发点击事件处理函数',event.clientX,event.clientY);// 计算鼠标在容器中的相对位置\nconst rect=container.getBoundingClientRect();const mouseX=(event.clientX-rect.left)/container.clientWidth*2-1;const mouseY=-((event.clientY-rect.top)/container.clientHeight)*2+1;// 创建射线\nconst raycaster=new THREE.Raycaster();// 增加检测阈值，使小物体也能被点击到\nraycaster.params.Points.threshold=1;raycaster.params.Line.threshold=1;const mouseVector=new THREE.Vector2(mouseX,mouseY);raycaster.setFromCamera(mouseVector,cameraInstance);console.log('鼠标点击位置:',mouseX,mouseY);// 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\nconst trafficLightObjects=[];trafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){// 将模型添加到数组中\ntrafficLightObjects.push(lightObj.model);// 确保模型是可见的，并且不会被其他对象遮挡\nlightObj.model.visible=true;lightObj.model.renderOrder=1000;// 设置高渲染优先级\n// 确保模型的所有子对象都是可见的\nlightObj.model.traverse(child=>{child.visible=true;child.renderOrder=1000;});}});console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);// 首先：尝试直接检测红绿灯模型集合\nconst trafficLightIntersects=raycaster.intersectObjects(trafficLightObjects,true);if(trafficLightIntersects.length>0){console.log('直接命中红绿灯模型:',trafficLightIntersects.length);trafficLightIntersects.forEach((intersect,index)=>{console.log(`命中对象 ${index}:`,intersect.object.name||'无名称','距离:',intersect.distance,'userData:',intersect.object.userData);});// 获取第一个交点的对象\nconst obj=getTrafficLightFromObject(trafficLightIntersects[0].object);if(obj&&obj.userData&&obj.userData.type==='trafficLight'){// 获取红绿灯ID\nconst interId=obj.userData.interId;console.log('成功检测到红绿灯点击, 路口ID:',interId,'类型:',typeof interId);// 检查trafficLightsMap中可能的ID类型\nlet correctId=interId;if(typeof interId==='string'&&!trafficLightsMap.has(interId)&&trafficLightsMap.has(parseInt(interId))){correctId=parseInt(interId);console.log(`转换ID类型为数字: ${correctId}`);}else if(typeof interId==='number'&&!trafficLightsMap.has(interId)&&trafficLightsMap.has(String(interId))){correctId=String(interId);console.log(`转换ID类型为字符串: ${correctId}`);}// 显示弹窗\nwindow.showTrafficLightPopup(correctId);return;}}// 第二步：检测所有场景对象\nconst intersects=raycaster.intersectObjects(sceneInstance.children,true);console.log('射线检测到的对象数量:',intersects.length);if(intersects.length>0){// 输出所有检测到的对象信息用于调试\nintersects.forEach((intersect,index)=>{const obj=intersect.object;console.log(`检测到的对象 ${index}:`,obj.name||'无名称','userData:',obj.userData,'距离:',intersect.distance);});// 检查是否点击了红绿灯\nfor(let i=0;i<intersects.length;i++){const obj=getTrafficLightFromObject(intersects[i].object);if(obj&&obj.userData&&obj.userData.type==='trafficLight'){const interId=obj.userData.interId;window.showTrafficLightPopup(interId);return;}}}// 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\nconsole.log('尝试查找最接近点击位置的红绿灯');// 将红绿灯模型投影到屏幕坐标中\nlet closestLight=null;let minDistance=0.1;// 设置一个阈值，只有距离小于此值的才会被选中\ntrafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){const worldPos=new THREE.Vector3();// 获取模型的世界坐标\nlightObj.model.getWorldPosition(worldPos);// 将世界坐标投影到屏幕坐标\nconst screenPos=worldPos.clone();screenPos.project(cameraInstance);// 计算鼠标与红绿灯在屏幕上的距离\nconst dx=screenPos.x-mouseX;const dy=screenPos.y-mouseY;const distance=Math.sqrt(dx*dx+dy*dy);console.log(`路口 ${interId} 的距离:`,distance);if(distance<minDistance){minDistance=distance;closestLight={interId,distance};}}});if(closestLight){console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);// 显示弹窗\nwindow.showTrafficLightPopup(closestLight.interId);return;}console.log('未检测到任何红绿灯点击');};// 关闭弹出窗口的处理函数\nconst handleClosePopover=setPopoverState=>{// 清理定时器\nif(window.trafficLightUpdateTimerRef&&window.trafficLightUpdateTimerRef.current){clearInterval(window.trafficLightUpdateTimerRef.current);window.trafficLightUpdateTimerRef.current=null;console.log('已清理红绿灯状态更新定时器');}// 清理当前弹窗ID\nif(window.currentPopoverIdRef){window.currentPopoverIdRef.current=null;}// 更新弹窗状态为不可见\nsetPopoverState(prev=>({...prev,visible:false}));console.log('弹窗已关闭');};// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick=interId=>{try{var _document$querySelect,_document$querySelect2;// 检查是否有该ID的红绿灯\nconst trafficLight=trafficLightsMap.get(interId||'1');if(!trafficLight){console.error('未找到指定ID的红绿灯:',interId);// 输出所有可用ID\nconsole.log('可用的红绿灯ID:');trafficLightsMap.forEach((light,id)=>{console.log(`- ${id}: ${light.intersection.name}`);});return false;}// 获取红绿灯模型\nconst lightModel=trafficLight.model;// 模拟点击事件\nconst stateInfo=trafficLightStates.get(interId);const intersection=trafficLight.intersection;// 创建弹出窗口内容\nlet content;if(stateInfo&&stateInfo.phases){content=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',width:'220px',maxHeight:'300px',overflowY:'auto'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'bold',marginBottom:'6px',fontSize:'14px',borderBottom:'1px solid #eee',paddingBottom:'4px'},children:[intersection.name,\" (ID: \",interId,\")\"]}),/*#__PURE__*/_jsx(\"div\",{children:stateInfo.phases.map((phase,index)=>{let lightColor;switch(phase.trafficLight){case'G':lightColor='#00ff00';break;case'Y':lightColor='#ffff00';break;case'R':default:lightColor='#ff0000';break;}return/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'6px',backgroundColor:'rgba(255,255,255,0.1)',padding:'4px',borderRadius:'4px',fontSize:'12px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'bold'},children:getPhaseDirection(phase.phaseId)}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u706F\\u8272: \"}),/*#__PURE__*/_jsx(\"span\",{style:{color:lightColor,fontWeight:'bold',backgroundColor:'rgba(0,0,0,0.3)',padding:'0 3px',borderRadius:'2px'},children:phase.trafficLight==='R'?'红灯':phase.trafficLight==='Y'?'黄灯':'绿灯'})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u5012\\u8BA1\\u65F6: \"}),/*#__PURE__*/_jsxs(\"span\",{style:{fontWeight:'bold'},children:[phase.remainTime,\" \\u79D2\"]})]})]},index);})}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'6px',fontSize:'10px',color:'#888'},children:[\"\\u66F4\\u65B0\\u65F6\\u95F4: \",new Date().toLocaleTimeString(),\" (\\u81EA\\u52A8\\u5237\\u65B0)\"]})]});}else{content=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',maxWidth:'200px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'bold',marginBottom:'8px'},children:intersection.name}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u8DEF\\u53E3ID: \",interId]}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"})]});}// 获取弹窗位置 - 使用中心位置\nconst centerX=window.innerWidth/2-500;const centerY=window.innerHeight/2-500;// 获取全局的setTrafficLightPopover函数\nconst setPopoverState=(_document$querySelect=document.querySelector('#root'))===null||_document$querySelect===void 0?void 0:(_document$querySelect2=_document$querySelect.__REACT_INSTANCE)===null||_document$querySelect2===void 0?void 0:_document$querySelect2.setTrafficLightPopover;if(setPopoverState){// 直接调用React组件的状态更新函数\nsetPopoverState({visible:true,interId:interId,position:{x:centerX,y:centerY},content:content,phases:(stateInfo===null||stateInfo===void 0?void 0:stateInfo.phases)||[]});console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);return true;}else{// 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\nconst popover=document.createElement('div');popover.style.position='absolute';popover.style.left=`${centerX}px`;popover.style.top=`${centerY}px`;popover.style.transform='translate(-50%, -100%)';popover.style.zIndex='9999';popover.style.backgroundColor='rgba(0, 0, 0, 0.85)';popover.style.color='white';popover.style.borderRadius='4px';popover.style.boxShadow='0 2px 8px rgba(0, 0, 0, 0.3)';popover.style.padding='8px';popover.style.maxWidth='240px';popover.style.fontSize='12px';popover.innerHTML=`\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo?'红绿灯状态已加载':'当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;document.body.appendChild(popover);// 添加关闭按钮点击事件\nconst closeButton=popover.querySelector('button');if(closeButton){closeButton.addEventListener('click',()=>{document.body.removeChild(popover);});}console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);return true;}}catch(error){console.error('测试红绿灯点击失败:',error);return false;}};// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights=()=>{console.log('红绿灯列表:');if(!trafficLightsMap||trafficLightsMap.size===0){console.log('当前没有红绿灯对象');return[];}const list=[];trafficLightsMap.forEach((light,id)=>{console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);list.push({id,name:light.intersection.name,position:light.position});});return list;};// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n//   console.log('开始调试红绿灯模型...');\n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n//       // 添加到场景\n//       scene.add(highlightMesh);\n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup=interId=>{try{// 确保interId为字符串类型\ninterId=String(interId);console.log('调用showTrafficLightPopup函数, 参数ID:',interId,'类型:',typeof interId);console.log('当前trafficLightsMap大小:',trafficLightsMap.size);// 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\nlet trafficLight=trafficLightsMap.get(interId);if(!trafficLight){// 尝试转换为数字查找\nconst numericId=parseInt(interId);trafficLight=trafficLightsMap.get(numericId);if(trafficLight){console.log(`使用数字ID ${numericId} 找到了红绿灯`);interId=numericId;// 更新interId为找到的正确类型\n}}if(!trafficLight){console.error('未找到指定ID的红绿灯:',interId);return false;}const stateInfo=trafficLightStates.get(interId);const intersection=trafficLight.intersection;// 相位ID与方向/方式的映射\nconst phaseMap={'1':{dir:'N',type:'left'},'2':{dir:'N',type:'straight'},'3':{dir:'N',type:'right'},'5':{dir:'E',type:'left'},'6':{dir:'E',type:'straight'},'7':{dir:'E',type:'right'},'9':{dir:'S',type:'left'},'10':{dir:'S',type:'straight'},'11':{dir:'S',type:'right'},'13':{dir:'W',type:'left'},'14':{dir:'W',type:'straight'},'15':{dir:'W',type:'right'}};const typeOrder=['left','straight','right'];const colorMap={G:'#00ff00',Y:'#ffff00',R:'#ff0000'};const dirData={N:{},E:{},S:{},W:{}};if(stateInfo&&stateInfo.phases){stateInfo.phases.forEach(phase=>{const map=phaseMap[phase.phaseId];if(map){dirData[map.dir][map.type]={color:colorMap[phase.trafficLight]||'#888',remainTime:phase.remainTime};}});}const content=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',width:'260px',background:'rgba(0,0,0,0.05)'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'bold',marginBottom:'8px',fontSize:'15px',textAlign:'center'},children:[intersection.name,\"\\u706F\\u6001\"]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateRows:'60px 60px 60px',gridTemplateColumns:'60px 60px 60px',justifyContent:'center',alignItems:'center',background:'rgba(255,255,255,0.05)',borderRadius:'8px',margin:'0 auto',position:'relative'},children:[/*#__PURE__*/_jsx(\"div\",{style:{gridRow:1,gridColumn:2,textAlign:'center'},children:typeOrder.map(type=>dirData.S[type]&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'2px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:dirData.S[type].color,fontWeight:'bold'},children:dirData.S[type].remainTime}),/*#__PURE__*/_jsx(\"span\",{style:{color:dirData.S[type].color,fontSize:'40px',fontWeight:'bold',lineHeight:'40px'},children:type==='left'?'↙':type==='straight'?'↓':'↘'})]},type))}),/*#__PURE__*/_jsx(\"div\",{style:{gridRow:3,gridColumn:2,textAlign:'center'},children:typeOrder.map(type=>dirData.N[type]&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'2px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:dirData.N[type].color,fontSize:'40px',fontWeight:'bold',lineHeight:'40px'},children:type==='left'?'↖':type==='straight'?'↑':'↗'}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:dirData.N[type].color,fontWeight:'bold'},children:dirData.N[type].remainTime})]},type))}),/*#__PURE__*/_jsx(\"div\",{style:{gridRow:2,gridColumn:1,textAlign:'center'},children:typeOrder.map(type=>dirData.E[type]&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'2px',display:'flex',alignItems:'center',justifyContent:'flex-end'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:dirData.E[type].color,fontWeight:'bold',marginRight:'5px',width:'25px',display:'flex',alignItems:'center',justifyContent:'center'},children:dirData.E[type].remainTime}),/*#__PURE__*/_jsx(\"span\",{style:{color:dirData.E[type].color,fontSize:'40px',fontWeight:'bold',lineHeight:'40px',display:'block'},children:type==='left'?'↗':type==='straight'?'→':'↘'})]},type))}),/*#__PURE__*/_jsx(\"div\",{style:{gridRow:2,gridColumn:3,textAlign:'center'},children:typeOrder.map(type=>dirData.W[type]&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'2px',display:'flex',alignItems:'center',justifyContent:'flex-start'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:dirData.W[type].color,fontSize:'40px',fontWeight:'bold',lineHeight:'40px',display:'block'},children:type==='left'?'↙':type==='straight'?'←':'↖'}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:dirData.W[type].color,fontWeight:'bold',marginLeft:'5px',width:'25px',display:'flex',alignItems:'center',justifyContent:'center'},children:dirData.W[type].remainTime})]},type))})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'8px',fontSize:'11px',color:'#888',textAlign:'center'},children:[\"\\u66F4\\u65B0\\u65F6\\u95F4: \",new Date().toLocaleTimeString(),\" (\\u81EA\\u52A8\\u5237\\u65B0)\"]})]});// 设置弹窗位置在屏幕中央\nconst x=window.innerWidth/2-150;const y=window.innerHeight/2-50;// 更新当前显示的红绿灯ID引用\nif(window.currentPopoverIdRef){window.currentPopoverIdRef.current=interId;}// 取消之前的更新定时器（如果存在）\nif(window.trafficLightUpdateTimerRef&&window.trafficLightUpdateTimerRef.current){clearInterval(window.trafficLightUpdateTimerRef.current);window.trafficLightUpdateTimerRef.current=null;}// 更新弹窗状态\nif(window._setTrafficLightPopover){window._setTrafficLightPopover({visible:true,interId:interId,position:{x,y},content:content,phases:(stateInfo===null||stateInfo===void 0?void 0:stateInfo.phases)||[]});// 设置定时更新\nif(window.trafficLightUpdateTimerRef){window.trafficLightUpdateTimerRef.current=setInterval(()=>{window.showTrafficLightPopup(interId);},1000);}return true;}else{console.error('无法找到setTrafficLightPopover函数');return false;}}catch(error){console.error('显示红绿灯弹窗失败:',error);return false;}};// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n//   console.log('开始生成模拟SPAT数据...');\n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject=object=>{let current=object;// 如果对象本身就有红绿灯数据，直接返回\nif(current&&current.userData&&current.userData.type==='trafficLight'){console.log('直接找到红绿灯对象:',current.name||'无名称');return current;}// 向上查找父对象，直到找到红绿灯或到达顶层\nwhile(current&&current.parent){current=current.parent;if(current.userData&&current.userData.type==='trafficLight'){console.log('从父对象找到红绿灯:',current.name||'无名称');return current;}}return null;};// 添加调试工具：强制进行点击测试\nwindow.testClickDetection=(x,y)=>{try{console.log('执行强制点击测试 @ 位置:',x,y);// 找到渲染器的DOM元素\nconst canvas=document.querySelector('canvas');if(!canvas){console.error('找不到THREE.js的canvas元素');return false;}// 确保scene和camera已定义\nif(!scene||!cameraRef.current){console.error('scene或camera未定义');return false;}// 如果没有传入坐标，使用屏幕中心点\nif(x===undefined||y===undefined){x=window.innerWidth/2;y=window.innerHeight/2;}// 计算归一化设备坐标 (-1 到 +1)\nconst rect=canvas.getBoundingClientRect();const mouseX=(x-rect.left)/canvas.clientWidth*2-1;const mouseY=-((y-rect.top)/canvas.clientHeight)*2+1;console.log('归一化坐标:',mouseX,mouseY);// 创建一个射线\nconst raycaster=new THREE.Raycaster();raycaster.params.Points.threshold=5;raycaster.params.Line.threshold=5;const mouseVector=new THREE.Vector2(mouseX,mouseY);raycaster.setFromCamera(mouseVector,cameraRef.current);// 收集所有红绿灯对象\nconst trafficLightObjects=[];trafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){trafficLightObjects.push(lightObj.model);console.log(`添加红绿灯 ${interId} 到检测列表`);}});// 直接对红绿灯对象进行碰撞检测\nconsole.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);const tlIntersects=raycaster.intersectObjects(trafficLightObjects,true);if(tlIntersects.length>0){console.log('成功点击到红绿灯对象!');tlIntersects.forEach((intersect,i)=>{console.log(`结果 ${i}:`,intersect.object.name||'无名称','距离:',intersect.distance,'position:',intersect.object.position.toArray(),'userData:',intersect.object.userData);// 尝试获取红绿灯ID\nconst obj=getTrafficLightFromObject(intersect.object);if(obj&&obj.userData&&obj.userData.type==='trafficLight'){console.log('找到红绿灯ID:',obj.userData.interId);}});return true;}// 对整个场景进行碰撞检测\nconsole.log('对整个场景进行碰撞检测...');const sceneIntersects=raycaster.intersectObjects(scene.children,true);console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);sceneIntersects.forEach((intersect,i)=>{const obj=intersect.object;console.log(`场景物体 ${i}:`,obj.name||'无名称','类型:',obj.type,'位置:',obj.position.toArray(),'距离:',intersect.distance,'userData:',obj.userData);});// 测试红绿灯的可见性\nconsole.log('检查红绿灯的可见性...');let visibleCount=0;trafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){var _lightObj$intersectio;// 检查红绿灯模型是否可见\nlet isVisible=lightObj.model.visible;let frustumVisible=true;// 获取世界位置\nconst worldPos=new THREE.Vector3();lightObj.model.getWorldPosition(worldPos);// 计算到摄像机的距离\nconst distanceToCamera=worldPos.distanceTo(cameraRef.current.position);// 检查是否在视锥体内\nconst screenPos=worldPos.clone().project(cameraRef.current);if(Math.abs(screenPos.x)>1||Math.abs(screenPos.y)>1||screenPos.z<-1||screenPos.z>1){frustumVisible=false;}if(isVisible){visibleCount++;}console.log(`红绿灯 ${interId}:`,{名称:((_lightObj$intersectio=lightObj.intersection)===null||_lightObj$intersectio===void 0?void 0:_lightObj$intersectio.name)||'未知',可见性:isVisible,在视锥体内:frustumVisible,世界位置:worldPos.toArray(),屏幕位置:[screenPos.x,screenPos.y,screenPos.z],与摄像机距离:distanceToCamera});}});console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);// 如果未检测到任何交叉点\nreturn sceneIntersects.length>0;}catch(error){console.error('点击测试失败:',error);return false;}};// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual=(trafficLight,phaseInfo)=>{var _trafficLight$interse,_trafficLight$interse2,_trafficLight$interse3;if(!trafficLight||!trafficLight.model||!phaseInfo){return;}// 移除旧的灯光模型(如果存在)\nconst lightsToRemove=[];trafficLight.model.traverse(child=>{if(child.userData&&child.userData.isLight){lightsToRemove.push(child);}});lightsToRemove.forEach(light=>{trafficLight.model.remove(light);});// 根据状态获取颜色\nlet lightColor;switch(phaseInfo.trafficLight){case'G':lightColor=0x00FF00;// 绿色\nbreak;case'Y':lightColor=0xFFFF00;// 黄色\nbreak;case'R':default:lightColor=0xFF0000;// 红色\nbreak;}// 创建一个球体作为灯光\nconst lightGeometry=new THREE.SphereGeometry(3,16,16);const lightMaterial=new THREE.MeshBasicMaterial({color:lightColor,emissive:lightColor,emissiveIntensity:1});const lightMesh=new THREE.Mesh(lightGeometry,lightMaterial);lightMesh.position.set(0,12,0);// 放在交通灯顶部\nlightMesh.userData={isLight:true,type:'trafficLight',interId:(_trafficLight$interse=trafficLight.intersection)===null||_trafficLight$interse===void 0?void 0:_trafficLight$interse.interId,phaseId:phaseInfo.phaseId,direction:phaseInfo.direction,remainTime:phaseInfo.remainTime};// 添加光源使灯光更明显\nconst light=new THREE.PointLight(lightColor,1,50);light.position.set(0,12,0);light.userData={isLight:true};// 将灯光添加到交通灯模型\ntrafficLight.model.add(lightMesh);trafficLight.model.add(light);console.log(`更新路口 ${((_trafficLight$interse2=trafficLight.intersection)===null||_trafficLight$interse2===void 0?void 0:_trafficLight$interse2.name)||((_trafficLight$interse3=trafficLight.intersection)===null||_trafficLight$interse3===void 0?void 0:_trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);};export default CampusModel;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "SkeletonUtils", "mqtt", "Select", "Popover", "intersectionsData", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "peopleBaseModel", "skeleton", "lastPosition", "lastRotation", "ALPHA", "vehicleLastPositions", "Map", "vehicleLastRotations", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "process", "env", "REACT_APP_API_URL", "console", "log", "vehicleModels", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "clock", "Clock", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "vehicleId", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "has", "lastPos", "get", "distance", "distanceTo", "MAX_DISTANCE_THRESHOLD", "toFixed", "filteredPos", "Vector3", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "lastRot", "MAX_ANGLE_THRESHOLD", "abs", "TRAFFIC_LIGHT_UPDATE_INTERVAL", "CampusModel", "_ref", "className", "onCurrentRSUChange", "selectedRSUs", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "mapRef", "lastCameraPosition", "lastCameraTarget", "cameraSmoothing", "prevAnimationTimeRef", "Date", "now", "peopleAnimationMixers", "peopleAnimations", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "currentPopoverIdRef", "trafficLightUpdateTimerRef", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "switchToFollowView", "current", "enabled", "switchToGlobalView", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "target", "currentTarget", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "updateMatrix", "updateMatrixWorld", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "相机位置", "toArray", "目标点", "handleMqttMessage", "message", "payload", "JSON", "parse", "_payload$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "participants", "rsuid", "for<PERSON>ach", "participant", "id", "partPtcId", "type", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "model", "newModel", "height", "rotation", "scale", "mixer", "AnimationMixer", "action", "clipAction", "animations", "play", "add", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "Set", "map", "p", "modelData", "remove", "delete", "bsmData", "bsmid", "newState", "partLong", "partLat", "postMessage", "source", "initialPosition", "initialRotation", "newPosition", "vehicleObj", "newVehicleModel", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "emissive", "Color", "transparent", "needsUpdate", "speedLabel", "createTextSprite", "round", "r", "g", "b", "a", "textColor", "renderOrder", "opacity", "is<PERSON><PERSON>", "Out", "filteredPosition", "dispose", "timeSinceLastUpdate", "undefined", "originalTran<PERSON>arent", "originalOpacity", "m", "geometry", "phasesInfo", "phase", "phaseId", "toString", "direction", "trafficDirec", "getDirectionFromCode", "getPhaseDirection", "lightState", "trafficLight", "remainTime", "parseInt", "phaseInfo", "push", "trafficLightKey", "String", "trafficLightModel", "updateTrafficLightVisual", "prev", "<PERSON><PERSON><PERSON>", "strId", "numId", "updateTime", "setTimeout", "showTrafficLightPopup", "rsiData", "rsuId", "events", "rtes", "event", "eventId", "rteId", "eventType", "description", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "stringify", "onerror", "onclose", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "length", "setIsVehicleLoaded", "xhr", "loaded", "total", "initializeScene", "loadModelWithRetry", "url", "maxRetries", "arguments", "attemptLoad", "retriesLeft", "loader", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "currentTime", "deltaTime", "<PERSON><PERSON><PERSON><PERSON>", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "targetCameraPosition", "targetLookAt", "lerp", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "clearInterval", "close", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "clear", "lightObj", "handleMainVehicleChange", "intervalId", "setInterval", "timer", "createTrafficLights", "clearTimeout", "handleClick", "handleMouseClick", "initScene", "createSimpleTrafficLight", "BoxGeometry", "MeshBasicMaterial", "<PERSON><PERSON>", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "addClickHelpers", "helperGeometry", "SphereGeometry", "helperMaterial", "depthWrite", "helper<PERSON><PERSON>", "userData", "isClickHelper", "size", "firstIntersection", "style", "placeholder", "onChange", "options", "label", "bordered", "dropdownStyle", "maxHeight", "ref", "max<PERSON><PERSON><PERSON>", "right", "background", "onClick", "handleClosePopover", "text", "parameters", "params", "fontFace", "borderThickness", "borderColor", "canvas", "document", "createElement", "context", "getContext", "font", "textWidth", "measureText", "textBaseline", "radius", "beginPath", "moveTo", "lineTo", "arcTo", "closePath", "strokeStyle", "lineWidth", "stroke", "fillStyle", "fill", "textAlign", "fillText", "texture", "CanvasTexture", "minFilter", "LinearFilter", "spriteMaterial", "SpriteMaterial", "sprite", "Sprite", "depthTest", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "trafficLightGltf", "vehicleGltf", "cyclistGltf", "peopleGltf", "all", "loadAsync", "materia", "<PERSON><PERSON><PERSON><PERSON>", "warn", "err", "types", "parent", "converterInstance", "then", "catch", "createFallbackTrafficLights", "hasTrafficLight", "Error", "side", "DoubleSide", "colliderGeometry", "colliderMaterial", "collider", "isCollider", "lightGeometry", "lightMaterial", "<PERSON><PERSON><PERSON>", "dirCode", "container", "sceneInstance", "cameraInstance", "clientX", "clientY", "rect", "getBoundingClientRect", "mouseX", "clientWidth", "mouseY", "clientHeight", "raycaster", "Raycaster", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "trafficLightObjects", "trafficLightIntersects", "intersectObjects", "intersect", "index", "object", "obj", "getTrafficLightFromObject", "correctId", "intersects", "closestLight", "worldPos", "getWorldPosition", "screenPos", "project", "dx", "dy", "sqrt", "setPopoverState", "testTrafficLightClick", "_document$querySelect", "_document$querySelect2", "light", "lightModel", "stateInfo", "overflowY", "marginBottom", "borderBottom", "paddingBottom", "lightColor", "justifyContent", "marginTop", "toLocaleTimeString", "centerX", "centerY", "__REACT_INSTANCE", "popover", "innerHTML", "body", "closeButton", "listTrafficLights", "list", "numericId", "phaseMap", "dir", "typeOrder", "colorMap", "G", "Y", "R", "dirD<PERSON>", "N", "E", "S", "W", "gridTemplateRows", "gridTemplateColumns", "alignItems", "margin", "gridRow", "gridColumn", "marginRight", "marginLeft", "testClickDetection", "tlIntersects", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "distanceToCamera", "名称", "可见性", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "isLight", "emissiveIntensity", "PointLight"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js';\n\n\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel= null; // 存储原始模型数据\nlet skeleton =null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  const lastPos = vehicleLastPositions.get(vehicleId);\n  \n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n  \n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  \n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  \n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const lastRot = vehicleLastRotations.get(vehicleId);\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  \n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n  \n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n  \n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = useRef(new Map()); // 存储所有行人的动画混合器\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n  \n  \n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n  \n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n  \n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n  \n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n  \n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n      \n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n      \n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n        \n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos)\n          .to({ x: 0, y: 300, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.position.copy(currentPos);\n          })\n          .start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp)\n          .to({ x: 0, y: 1, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.up.copy(currentUp);\n          })\n          .start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n        \n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget)\n          .to({ x: 0, y: 0, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            controls.target.copy(currentTarget);\n            // 确保相机始终朝向目标点\n            cameraRef.current.lookAt(controls.target);\n            controls.update();\n          })\n          .start();\n\n        // 启用控制器\n        controls.enabled = true;\n        \n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x+50, 70, -modelCoords.y+50);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        // console.log('收到RSM消息:', payload);\n        \n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n        \n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n        \n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n      return;\n    }\n    \n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        \n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n        \n        const participants = payload.data?.participants || [];\n        const rsuid = payload.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id =  deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n          // 解析位置和状态信息\n          const state = {\n            longitude: parseFloat(participant.partPosLong),\n            latitude: parseFloat(participant.partPosLat),\n            speed: parseFloat(participant.partSpeed),\n            heading: parseFloat(participant.partHeading)\n          };\n          \n          const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n          \n          // 根据类型选择对应的预加载模型\n          let preloadedModel;\n          switch (type) {\n            case '1': // 机动车\n              preloadedModel = preloadedVehicleModel;\n              break;\n            case '2': // 非机动车\n              preloadedModel = preloadedCyclistModel;\n              break;\n            case '3': // 行人\n              preloadedModel = preloadedPeopleModel;\n              break;\n            default:\n              return; // 跳过未知类型\n          }\n          \n          // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n            // 创建新模型实例\n            const newModel = type === '3' ? SkeletonUtils.clone(preloadedPeopleModel) : preloadedModel.clone();\n            // 根据类型调整高度和缩放\n            const height = type === '3' ? 2.0 : 1.0;\n            newModel.position.set(modelPos.x, height, -modelPos.y);\n            newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n\n            // 如果是行人类型，设置缩放和创建动画\n            if (type === '3') {\n              // newModel.scale.set(0.005, 0.005, 0.005);\n              newModel.scale.set(4, 4, 4);\n              // 创建动画混合器\n              const mixer = new THREE.AnimationMixer(newModel);\n             \n              // 播放行走动画peopleBaseModel\n              const action = mixer.clipAction(peopleBaseModel.animations[0]);\n              action.play();\n\n              peopleAnimationMixers.current.set(id, mixer);\n\n              // console.log('找到行人动画tttt:', peopleBaseModel.animations[0]);\n              // if(peopleBaseModel){\n              //   if ( peopleBaseModel.animations.length > 0) {\n              //     peopleBaseModel.animations.forEach((clip) => {\n              //       const action = mixer.clipAction(clip);\n              //       action.play();\n              //       // console.log('找到行人动画tttt:', peopleBaseModel.animations.length, '个');\n              //     });\n              //     // 保存动画混合器\n              //     peopleAnimationMixers.current.set(id, mixer);\n              //   }\n              // }\n            }\n\n            scene.add(newModel);\n            \n            vehicleModels.set(id, {\n              model: newModel,\n              lastUpdate: now,\n              type: type\n            });\n          } else if (model) {\n            // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n          }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 1000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n        \n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n        \n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n        \n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid, // 直接传递车辆ID\n          data: {       // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n        \n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n        \n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n        \n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n        \n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        \n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          \n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n          \n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n              \n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n              \n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n          \n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n          \n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          \n          scene.add(newVehicleModel);\n          \n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n          \n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position)\n            .to({ y: 0.5 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .start();\n          \n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({ opacity: 0.6 })\n                .to({ opacity: 1.0 }, 500)\n                .easing(TWEEN.Easing.Quadratic.Out)\n                .onUpdate(function() {\n                  child.material.opacity = this.opacity;\n                  child.material.needsUpdate = true;\n                })\n                .start();\n            }\n          });\n          \n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({ opacity: 0.6 })\n            .to({ opacity: 1.0 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .onUpdate(function() {\n              speedLabel.material.opacity = this.opacity;\n              speedLabel.material.needsUpdate = true;\n            })\n            .start();\n          \n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n          \n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n          \n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n          \n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n          \n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n        \n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 增加到10秒，避免频繁清理造成闪烁\n        \n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n          \n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1 ;\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n                \n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n            \n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n            \n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            \n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        \n        return;\n      }\n      \n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n        \n        try {\n          const payload = JSON.parse(message);\n          \n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n          \n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n          \n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n          \n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n          \n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              \n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              \n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n              \n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                \n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  \n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? \n                    getDirectionFromCode(phase.trafficDirec) : \n                    getPhaseDirection(phaseId);\n                  \n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  \n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n                  \n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n                  \n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n                  \n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  \n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  \n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n                    \n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n                \n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                \n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n                  \n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && \n                     (window.currentPopoverIdRef.current === modelKey || \n                      window.currentPopoverIdRef.current === String(modelKey) || \n                      window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    \n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n                    \n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n      }\n      \n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        \n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n        \n        return;\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n        \n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/vehicle.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n        \n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          \n          // 检查scene是否初始化\n          if (scene) {\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画\n      const currentTime = Date.now();\n      // const deltaTime = (currentTime - prevAnimationTimeRef.current) * 10;\n      const deltaTime =clock.getDelta()\n      prevAnimationTimeRef.current = currentTime;\n\n      peopleAnimationMixers.current.forEach((mixer) => {\n        mixer.update(deltaTime);\n      });\n\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          200,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n        \n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        \n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n        \n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n        \n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n      \n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n      \n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach((lightObj) => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n      \n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n      \n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n    \n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n    \n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n    \n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n    \n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 2000);\n      \n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n  \n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current);\n        }\n      };\n      \n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n      \n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n      \n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n    \n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    \n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n    \n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,//\n          transparent: false,\n          opacity: 0.1,  // 几乎透明\n          depthWrite: false\n        });\n        \n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置\n        \n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n        \n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        \n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500);  // 延迟略长于debugTrafficLights\n    \n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n    \n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n  \n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        const firstIntersection = intersectionsData.intersections[0];\n        console.log('自动选择第一个路口:', firstIntersection.name);\n        \n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(firstIntersection.name);\n        }, 2000);\n        \n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n\n  return (\n    <>\n      <span style={labelStyle}>区域选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择区域位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n        value={selectedIntersection ? selectedIntersection.name : undefined}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      \n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '4px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n            padding: '0',\n            maxWidth: '240px', // 缩小最大宽度\n            fontSize: '12px' // 缩小字体\n          }}\n        >\n          {trafficLightPopover.content}\n          <button \n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '12px',\n              cursor: 'pointer',\n              padding: '2px 6px'\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n      \n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 16, // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    backgroundColor: parameters.backgroundColor || { r: 255, g: 255, b: 255, a: 0.8 },\n    textColor: parameters.textColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  \n  // 设置字体\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  \n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n  \n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  \n  canvas.width = width;\n  canvas.height = height;\n  \n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n  \n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n  \n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n  \n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n  \n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n  \n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n  \n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n  \n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = { \n    text: text,\n    params: params\n  };\n  \n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    try {\n      const [ trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([\n        loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`)\n        \n    ]);\n\n\n\n    // 处理机动车模型\n    console.log('加载车辆模型...');\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n          color: 0xff0000,  // 0xff0000, //红色 0xffffff,白色\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n      }\n    });\n\n    console.log('加载非机动车模型...');\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    console.log('加载行人模型...');\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n        \n      }\n      if (child.isMesh){\n        child.castShadow = true;\n      }\n    });\n\n\n\n    // 保存行人动画数据\n    console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n    if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n      console.log('找到行人动画:', peopleGltf.animations.length, '个');\n      peopleBaseModel = peopleGltf;\n    } else {\n      console.warn('行人模型没有包含动画数据');\n    }\n\n    console.log('加载红绿灯模型...');\n      \n    // 处理红绿灯模型\n    preloadedTrafficLightModel = trafficLightGltf.scene;\n    console.log('红绿灯模型：', preloadedTrafficLightModel);\n    // 设置红绿灯模型的缩放\n    preloadedTrafficLightModel.scale.set(6, 6, 6);\n    // 保持原始材质\n    preloadedTrafficLightModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 设置材质属性\n        child.material.metalness = 0.2;\n        child.material.roughness = 0.3;\n        child.material.envMapIntensity = 1.2;\n    }\n  });\n\n    console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n      \n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n        \n        // // 尝试单独加载红绿灯模型\n        // if (!preloadedTrafficLightModel) {\n        //   console.log('正在单独加载红绿灯模型...');\n        //   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n        //   preloadedTrafficLightModel = trafficLightGltf.scene;\n        //   preloadedTrafficLightModel.scale.set(3, 3, 3);\n        //   console.log('红绿灯模型加载成功');\n        // }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  \n  try {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在1秒后自动移除\n  setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n    scene.remove(sprite);\n      }\n  }, 100);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  // console.log('添加场景事件标记:', {\n  //   位置: position,\n  //   文本: text,\n  //   颜色: color\n  // });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  \n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  \n  // 检查红绿灯模型是否已加载\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载，尝试重新加载...');\n    // 尝试重新加载红绿灯模型\n    const loader = new GLTFLoader();\n    loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)\n      .then(trafficLightGltf => {\n        preloadedTrafficLightModel = trafficLightGltf.scene;\n        preloadedTrafficLightModel.scale.set(6, 6, 6);\n        preloadedTrafficLightModel.traverse((child) => {\n          if (child.isMesh && child.material) {\n            child.material.metalness = 0.2;\n            child.material.roughness = 0.3;\n            child.material.envMapIntensity = 1.2;\n          }\n        });\n        console.log('红绿灯模型重新加载成功，开始创建红绿灯...');\n        // 重新调用创建函数\n        createTrafficLights(converterInstance);\n      })\n      .catch(error => {\n        console.error('红绿灯模型重新加载失败:', error);\n        // 如果加载失败，使用简单的替代物体\n        createFallbackTrafficLights(converterInstance);\n      });\n    return;\n  }\n  \n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      \n      try {\n        // 确保模型存在且可以克隆\n        if (!preloadedTrafficLightModel || !preloadedTrafficLightModel.clone) {\n          throw new Error('红绿灯模型无效或无法克隆');\n        }\n        \n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n        \n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n        \n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n        \n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n        \n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n        \n        // 设置材质属性\n        trafficLightModel.traverse(child => {\n          if (child.isMesh) {\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            child.renderOrder = 100;\n          }\n        });\n        \n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        \n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n        \n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        \n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n  \n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance) => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({ \n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n  \n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n  \n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n  \n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n  \n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,  // 完全透明\n    depthWrite: false\n  });\n  \n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  \n  trafficLightModel.add(collider);\n  \n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n  \n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n  \n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n  \n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  trafficLightModel.add(lightMesh);\n  \n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  switch(phaseId) {\n    case '1': return '北进口左转';\n    case '2': return '北进口直行';\n    case '3': return '北进口右转';\n    case '5': return '东进口左转';\n    case '6': return '东进口直行';\n    case '7': return '东进口右转';\n    case '9': return '南进口左转';\n    case '10': return '南进口直行';\n    case '11': return '南进口右转';\n    case '13': return '西进口左转';\n    case '14': return '西进口直行';\n    case '15': return '西进口右转';\n    default: return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = (dirCode) => {\n  switch(dirCode) {\n    case 'N': return '北向南';\n    case 'S': return '南向北';\n    case 'E': return '东向西';\n    case 'W': return '西向东';\n    case 'NE': return '东北向西南';\n    case 'NW': return '西北向东南';\n    case 'SE': return '东南向西北';\n    case 'SW': return '西南向东北';\n    default: return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  \n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n  \n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n  \n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Line.threshold = 1;\n  \n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  \n  console.log('鼠标点击位置:', mouseX, mouseY);\n  \n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  \n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n  \n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  \n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, \n                 intersect.object.name || '无名称', \n                 '距离:', intersect.distance,\n                 'userData:', intersect.object.userData);\n    });\n    \n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    \n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n      \n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n      \n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n  \n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  \n  console.log('射线检测到的对象数量:', intersects.length);\n  \n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', \n                  'userData:', obj.userData, \n                  '距离:', intersect.distance);\n    });\n    \n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n  \n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n  \n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.1; // 设置一个阈值，只有距离小于此值的才会被选中\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n      \n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n      \n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      \n      console.log(`路口 ${interId} 的距离:`, distance);\n      \n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = { interId, distance };\n      }\n    }\n  });\n  \n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n    \n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  \n  console.log('未检测到任何红绿灯点击');\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n  \n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n  \n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({ ...prev, visible: false }));\n  console.log('弹窗已关闭');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = (interId) => {\n  try {\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      \n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      \n      return false;\n    }\n    \n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n    \n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 创建弹出窗口内容\n    let content;\n    \n    if (stateInfo && stateInfo.phases) {\n      content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection.name} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              switch (phase.trafficLight) {\n                case 'G': lightColor = '#00ff00'; break;\n                case 'Y': lightColor = '#ffff00'; break;\n                case 'R': default: lightColor = '#ff0000'; break;\n              }\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {getPhaseDirection(phase.phaseId)}\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      content = (\n        <div style={{ padding: '8px', maxWidth: '200px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>\n          <div>路口ID: {interId}</div>\n          <div>当前无信号灯状态信息</div>\n        </div>\n      );\n    }\n    \n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2 -500;\n    const centerY = window.innerHeight / 2 -500;\n    \n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;\n    \n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: { x: centerX, y: centerY },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      \n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      \n      document.body.appendChild(popover);\n      \n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      \n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  \n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  \n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  \n  return list;\n};\n\n// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n\n//   console.log('开始调试红绿灯模型...');\n  \n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n  \n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n  \n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n      \n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n  \n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n  \n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n      \n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n      \n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n      \n//       // 添加到场景\n//       scene.add(highlightMesh);\n      \n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n      \n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n      \n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n  \n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = (interId) => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    \n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    \n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      \n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    \n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    \n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 相位ID与方向/方式的映射\n    const phaseMap = {\n      '1': { dir: 'N', type: 'left' }, '2': { dir: 'N', type: 'straight' }, '3': { dir: 'N', type: 'right' },\n      '5': { dir: 'E', type: 'left' }, '6': { dir: 'E', type: 'straight' }, '7': { dir: 'E', type: 'right' },\n      '9': { dir: 'S', type: 'left' }, '10': { dir: 'S', type: 'straight' }, '11': { dir: 'S', type: 'right' },\n      '13': { dir: 'W', type: 'left' }, '14': { dir: 'W', type: 'straight' }, '15': { dir: 'W', type: 'right' }\n    };\n    \n    const typeOrder = ['left', 'straight', 'right'];\n    const colorMap = { G: '#00ff00', Y: '#ffff00', R: '#ff0000' };\n    const dirData = { N: {}, E: {}, S: {}, W: {} };\n    \n    if (stateInfo && stateInfo.phases) {\n      stateInfo.phases.forEach(phase => {\n        const map = phaseMap[phase.phaseId];\n        if (map) {\n          dirData[map.dir][map.type] = {\n            color: colorMap[phase.trafficLight] || '#888',\n            remainTime: phase.remainTime\n          };\n        }\n      });\n    }\n    \n    const content = (\n      <div style={{ padding: '8px', width: '260px', background: 'rgba(0,0,0,0.05)' }}>\n        <div style={{ fontWeight: 'bold', marginBottom: '8px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}灯态</div>\n        <div style={{\n          display: 'grid',\n          gridTemplateRows: '60px 60px 60px',\n          gridTemplateColumns: '60px 60px 60px',\n          justifyContent: 'center',\n          alignItems: 'center',\n          background: 'rgba(255,255,255,0.05)',\n          borderRadius: '8px',\n          margin: '0 auto',\n          position: 'relative'\n        }}>\n          {/* 南（位置已与北进口对调） */}\n          <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center' }}>\n            {typeOrder.map(type => dirData.S[type] && (\n              <div key={type} style={{marginBottom:'2px'}}>\n                <div style={{fontSize:'14px', color: dirData.S[type].color, fontWeight:'bold'}}>{dirData.S[type].remainTime}</div>\n                <span style={{color: dirData.S[type].color, fontSize:'40px', fontWeight:'bold', lineHeight: '40px'}}>\n                  {type === 'left' ? '↙' : type === 'straight' ? '↓' : '↘'}\n                </span>\n              </div>\n            ))}\n          </div>\n          {/* 北（位置已与南进口对调） */}\n          <div style={{ gridRow: 3, gridColumn: 2, textAlign: 'center' }}>\n            {typeOrder.map(type => dirData.N[type] && (\n              <div key={type} style={{marginBottom:'2px'}}>\n                <span style={{color: dirData.N[type].color, fontSize:'40px', fontWeight:'bold', lineHeight: '40px'}}>\n                  {type === 'left' ? '↖' : type === 'straight' ? '↑' : '↗'}\n                </span>\n                <div style={{fontSize:'14px', color: dirData.N[type].color, fontWeight:'bold'}}>{dirData.N[type].remainTime}</div>\n              </div>\n            ))}\n          </div>\n          {/* 东（位置已与西进口对调） */}\n          <div style={{ gridRow: 2, gridColumn: 1, textAlign: 'center' }}>\n            {typeOrder.map(type => dirData.E[type] && (\n              <div key={type} style={{marginBottom:'2px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}>\n                <div style={{\n                  fontSize:'14px', \n                  color: dirData.E[type].color, \n                  fontWeight:'bold', \n                  marginRight: '5px',\n                  width: '25px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}>{dirData.E[type].remainTime}</div>\n                <span style={{color: dirData.E[type].color, fontSize:'40px', fontWeight:'bold', lineHeight: '40px', display: 'block'}}>\n                  {type === 'left' ? '↗' : type === 'straight' ? '→' : '↘'}\n                </span>\n              </div>\n            ))}\n          </div>\n          {/* 西（位置已与东进口对调） */}\n          <div style={{ gridRow: 2, gridColumn: 3, textAlign: 'center' }}>\n            {typeOrder.map(type => dirData.W[type] && (\n              <div key={type} style={{marginBottom:'2px', display: 'flex', alignItems: 'center', justifyContent: 'flex-start'}}>\n                <span style={{color: dirData.W[type].color, fontSize:'40px', fontWeight:'bold', lineHeight: '40px', display: 'block'}}>\n                  {type === 'left' ? '↙' : type === 'straight' ? '←' : '↖'}\n                </span>\n                <div style={{\n                  fontSize:'14px', \n                  color: dirData.W[type].color, \n                  fontWeight:'bold', \n                  marginLeft: '5px',\n                  width: '25px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}>{dirData.W[type].remainTime}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n        <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n          更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n        </div>\n      </div>\n    );\n    \n    // 设置弹窗位置在屏幕中央\n    const x = window.innerWidth / 2 - 150;\n    const y = window.innerHeight / 2 - 50;\n    \n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n    \n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n    \n    // 更新弹窗状态\n    if (window._setTrafficLightPopover) {\n      window._setTrafficLightPopover({\n        visible: true,\n        interId: interId,\n        position: { x, y },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      // 设置定时更新\n      if (window.trafficLightUpdateTimerRef) {\n        window.trafficLightUpdateTimerRef.current = setInterval(() => {\n          window.showTrafficLightPopup(interId);\n        }, 1000);\n      }\n      \n      return true;\n    } else {\n      console.error('无法找到setTrafficLightPopover函数');\n      return false;\n    }\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n  \n//   console.log('开始生成模拟SPAT数据...');\n  \n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n  \n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n  \n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n    \n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n      \n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n    \n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n      \n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n      \n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n    \n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n    \n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n    \n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n    \n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n  \n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n  \n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n  \n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n  \n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n  \n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n  \n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  \n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n    \n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n    \n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n    \n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n    \n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    \n    console.log('归一化坐标:', mouseX, mouseY);\n    \n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    \n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n    \n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n    \n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    \n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', \n                    '距离:', intersect.distance,\n                    'position:', intersect.object.position.toArray(),\n                    'userData:', intersect.object.userData);\n        \n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      \n      return true;\n    }\n    \n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    \n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', \n                  '类型:', obj.type,\n                  '位置:', obj.position.toArray(),\n                  '距离:', intersect.distance,\n                  'userData:', obj.userData);\n    });\n    \n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    \n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n        \n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n        \n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n        \n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        \n        if (isVisible) {\n          visibleCount++;\n        }\n        \n        console.log(`红绿灯 ${interId}:`, {\n          名称: lightObj.intersection?.name || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    \n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n    \n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  \n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch(phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n  \n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ \n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: trafficLight.intersection?.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n  \n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = { isLight: true };\n  \n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  \n  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\n\nexport default CampusModel; \n\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,WAAW,KAAQ,OAAO,CACvE,MAAO,GAAK,CAAAC,KAAK,KAAM,OAAO,CAC9B,OAASC,UAAU,KAAQ,uCAAuC,CAClE,OAASC,aAAa,KAAQ,2CAA2C,CACzE,OAASC,mBAAmB,KAAQ,8BAA8B,CAClE,MAAO,GAAK,CAAAC,KAAK,KAAM,mBAAmB,CAC1C,MAAO,GAAK,CAAAC,aAAa,KAAM,2CAA2C,CAG1E,MAAO,CAAAC,IAAI,KAAM,MAAM,CACvB,OAASC,MAAM,CAAEC,OAAO,KAAQ,MAAM,CACtC,MAAO,CAAAC,iBAAiB,KAAM,4BAA4B,CAE1D;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,GAAI,CAAAC,gBAAgB,CAAG,IAAI,CAC3B,GAAI,CAAAC,gBAAgB,CAAG,EAAE,CACzB,GAAI,CAAAC,iBAAiB,CAAG,CAAC,CACzB,GAAI,CAAAC,oBAAoB,CAAG,IAAI,CAC/B,GAAI,CAAAC,cAAc,CAAG,IAAI,CAAG;AAC5B,GAAI,CAAAC,eAAe,CAAG,IAAI,CAAE;AAC5B,GAAI,CAAAC,QAAQ,CAAG,KAAK,CAAO;AAC3B,GAAI,CAAAC,UAAU,CAAG,QAAQ,CAAE;AAC3B,GAAI,CAAAC,QAAQ,CAAG,IAAI,CAAE;AAErB;AACA,GAAI,CAAAC,qBAAqB,CAAG,IAAI,CAChC,GAAI,CAAAC,qBAAqB,CAAG,IAAI,CAAG;AACnC,GAAI,CAAAC,oBAAoB,CAAG,IAAI,CAAI;AACnC,GAAI,CAAAC,0BAA0B,CAAG,IAAI,CAAE;AACvC,GAAI,CAAAC,KAAK,CAAG,IAAI,CAAE;AAElB,GAAI,CAAAC,eAAe,CAAE,IAAI,CAAE;AAC3B,GAAI,CAAAC,QAAQ,CAAE,IAAI,CAElB;AACA,GAAI,CAAAC,YAAY,CAAG,IAAI,CACvB,GAAI,CAAAC,YAAY,CAAG,IAAI,CACvB,KAAM,CAAAC,KAAK,CAAG,IAAI,CAAE;AAEpB;AACA,KAAM,CAAAC,oBAAoB,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAE;AACxC,KAAM,CAAAC,oBAAoB,CAAG,GAAI,CAAAD,GAAG,CAAC,CAAC,CAAE;AAExC;AACA,KAAM,CAAAE,WAAW,CAAG,CAClBC,MAAM,CAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAChCC,IAAI,CAAE,IAAI,CACV;AACEC,GAAG,CAAE,2BAA2B,CAChCC,GAAG,CAAE,2BAA2B,CAChChB,KAAK,CAAE,6BAA6B,CACtCiB,GAAG,CAAE,2BAA2B,CAAG;AACnCC,IAAI,CAAE,4BAA8B;AACtC,CAAC,CAED;AACA,KAAM,CAAAC,QAAQ,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACzEC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC,CAE1D;AACA,KAAM,CAAAG,aAAa,CAAG,GAAI,CAAAlB,GAAG,CAAC,CAAC,CAAE;AAEjC;AACA,GAAI,CAAAmB,gBAAgB,CAAG,GAAI,CAAAnB,GAAG,CAAC,CAAC,CAAE;AAElC;AACA,GAAI,CAAAoB,gBAAgB,CAAG,IAAI,CAE3B;AACA,GAAI,CAAAC,gBAAgB,CAAG,GAAI,CAAArB,GAAG,CAAC,CAAC,CAAE;AAClC,GAAI,CAAAsB,kBAAkB,CAAG,GAAI,CAAAtB,GAAG,CAAC,CAAC,CAAE;AAEpC,KAAM,CAAAuB,KAAK,CAAG,GAAI,CAAA3D,KAAK,CAAC4D,KAAK,CAAC,CAAC,CAE/B;AACA,KAAM,CAAAC,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAGf,QAAQ,oBAAoB,CAAC,CAC7D,KAAM,CAAAgB,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,EAAIA,IAAI,CAACE,QAAQ,EAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,CAAE,CACzD,KAAM,CAAAG,WAAW,CAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,aAAa,GAAK,IAAI,CAAC,CACrE,GAAIH,WAAW,EAAIA,WAAW,CAACI,KAAK,CAAE,CACpCjB,gBAAgB,CAAGa,WAAW,CAACI,KAAK,CACpCrB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEG,gBAAgB,CAAC,CAC7C,MAAO,CAAAA,gBAAgB,CACzB,CACF,CACAJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC,CAChCG,gBAAgB,CAAG,OAAO,CAAE;AAC5B,MAAO,CAAAA,gBAAgB,CACzB,CAAE,MAAOkB,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjClB,gBAAgB,CAAG,OAAO,CAAE;AAC5B,MAAO,CAAAA,gBAAgB,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAAmB,aAAa,CAAGA,CAACC,QAAQ,CAAEC,SAAS,CAAEC,KAAK,GAAK,CACpD,GAAID,SAAS,GAAK,IAAI,CAAE,MAAO,CAAAD,QAAQ,CACvC,MAAO,CAAAE,KAAK,CAAGF,QAAQ,CAAG,CAAC,CAAC,CAAGE,KAAK,EAAID,SAAS,CACnD,CAAC,CAED;AACA,KAAM,CAAAE,cAAc,CAAGA,CAACC,MAAM,CAAEC,SAAS,GAAK,CAC5C;AACA,GAAI,CAACA,SAAS,CAAE,CAChB,GAAI,CAACjD,YAAY,CAAE,CACjBA,YAAY,CAAGgD,MAAM,CAACE,KAAK,CAAC,CAAC,CAC7B,MAAO,CAAAF,MAAM,CACf,CAEA,KAAM,CAAAG,SAAS,CAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,CAAEpD,YAAY,CAACoD,CAAC,CAAElD,KAAK,CAAC,CAChE,KAAM,CAAAmD,SAAS,CAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,CAAEtD,YAAY,CAACsD,CAAC,CAAEpD,KAAK,CAAC,CAChE,KAAM,CAAAqD,SAAS,CAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,CAAExD,YAAY,CAACwD,CAAC,CAAEtD,KAAK,CAAC,CAEhEF,YAAY,CAACyD,GAAG,CAACN,SAAS,CAAEE,SAAS,CAAEE,SAAS,CAAC,CACjD,MAAO,CAAAvD,YAAY,CAACkD,KAAK,CAAC,CAAC,CAC3B,CAEA;AACA,GAAI,CAAC/C,oBAAoB,CAACuD,GAAG,CAACT,SAAS,CAAC,CAAE,CACxC9C,oBAAoB,CAACsD,GAAG,CAACR,SAAS,CAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CACnD,MAAO,CAAAF,MAAM,CACf,CAEA,KAAM,CAAAW,OAAO,CAAGxD,oBAAoB,CAACyD,GAAG,CAACX,SAAS,CAAC,CAEnD;AACA,KAAM,CAAAY,QAAQ,CAAGF,OAAO,CAACG,UAAU,CAACd,MAAM,CAAC,CAC3C,KAAM,CAAAe,sBAAsB,CAAG,EAAE,CAAE;AAEnC,GAAIF,QAAQ,CAAGE,sBAAsB,CAAE,CACrC3C,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAUY,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAClE7D,oBAAoB,CAACsD,GAAG,CAACR,SAAS,CAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CACnD,MAAO,CAAAF,MAAM,CACf,CAEA;AACA,KAAM,CAAAG,SAAS,CAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,CAAEO,OAAO,CAACP,CAAC,CAAElD,KAAK,CAAC,CAC3D,KAAM,CAAAmD,SAAS,CAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,CAAEK,OAAO,CAACL,CAAC,CAAEpD,KAAK,CAAC,CAC3D,KAAM,CAAAqD,SAAS,CAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,CAAEG,OAAO,CAACH,CAAC,CAAEtD,KAAK,CAAC,CAE3D,KAAM,CAAA+D,WAAW,CAAG,GAAI,CAAAjG,KAAK,CAACkG,OAAO,CAACf,SAAS,CAAEE,SAAS,CAAEE,SAAS,CAAC,CACtEpD,oBAAoB,CAACsD,GAAG,CAACR,SAAS,CAAEgB,WAAW,CAACf,KAAK,CAAC,CAAC,CAAC,CAExD,MAAO,CAAAe,WAAW,CACpB,CAAC,CAED;AACA,KAAM,CAAAE,cAAc,CAAGA,CAACC,WAAW,CAAEnB,SAAS,GAAK,CACjD;AACA,GAAI,CAACA,SAAS,CAAE,CAChB,GAAIhD,YAAY,GAAK,IAAI,CAAE,CACzBA,YAAY,CAAGmE,WAAW,CAC1B,MAAO,CAAAA,WAAW,CACpB,CAEA;AACA,GAAI,CAAAC,IAAI,CAAGD,WAAW,CAAGnE,YAAY,CACrC,GAAIoE,IAAI,CAAGC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CACvC,GAAIF,IAAI,CAAG,CAACC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CAExC,KAAM,CAAAC,gBAAgB,CAAG7B,aAAa,CAAC1C,YAAY,CAAGoE,IAAI,CAAEpE,YAAY,CAAEC,KAAK,CAAC,CAChFD,YAAY,CAAGuE,gBAAgB,CAC7B,MAAO,CAAAA,gBAAgB,CACzB,CAEA;AACA,GAAI,CAACnE,oBAAoB,CAACqD,GAAG,CAACT,SAAS,CAAC,CAAE,CACxC5C,oBAAoB,CAACoD,GAAG,CAACR,SAAS,CAAEmB,WAAW,CAAC,CAChD,MAAO,CAAAA,WAAW,CACpB,CAEA,KAAM,CAAAK,OAAO,CAAGpE,oBAAoB,CAACuD,GAAG,CAACX,SAAS,CAAC,CAEnD;AACA,GAAI,CAAAoB,IAAI,CAAGD,WAAW,CAAGK,OAAO,CAChC,GAAIJ,IAAI,CAAGC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CACvC,GAAIF,IAAI,CAAG,CAACC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CAExC;AACA,KAAM,CAAAG,mBAAmB,CAAGJ,IAAI,CAACC,EAAE,CAAG,CAAC,CAAE;AACzC,GAAID,IAAI,CAACK,GAAG,CAACN,IAAI,CAAC,CAAGK,mBAAmB,CAAE,CACxCtD,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAU,CAACoB,IAAI,CAAG,GAAG,CAAGC,IAAI,CAACC,EAAE,EAAEP,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAChF3D,oBAAoB,CAACoD,GAAG,CAACR,SAAS,CAAEmB,WAAW,CAAC,CAChD,MAAO,CAAAA,WAAW,CACpB,CAEA,KAAM,CAAAI,gBAAgB,CAAG7B,aAAa,CAAC8B,OAAO,CAAGJ,IAAI,CAAEI,OAAO,CAAEvE,KAAK,CAAC,CACtEG,oBAAoB,CAACoD,GAAG,CAACR,SAAS,CAAEuB,gBAAgB,CAAC,CAErD,MAAO,CAAAA,gBAAgB,CACzB,CAAC,CAED;AACA;AACA;AACA,KAAM,CAAAI,6BAA6B,CAAG,CAAC,CAAE;AACzC;AAEA,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAqD,IAApD,CAAEC,SAAS,CAAEC,kBAAkB,CAAEC,YAAa,CAAC,CAAAH,IAAA,CAClE,KAAM,CAAAI,YAAY,CAAGrH,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAAsH,UAAU,CAAGtH,MAAM,CAAC,IAAI,CAAC,CAC/B,KAAM,CAAAuH,SAAS,CAAGvH,MAAM,CAAC,GAAI,CAAAM,mBAAmB,CAAC,CAAC,CAAC,CACnD,KAAM,CAAAkH,aAAa,CAAGxH,MAAM,CAAC,EAAE,CAAC,CAChC,KAAM,CAAAyH,eAAe,CAAGzH,MAAM,CAAC,CAAC,CAAC,CACjC,KAAM,CAAA0H,aAAa,CAAG1H,MAAM,CAAC,IAAI,CAAC,CAClC,KAAM,CAAA2H,iBAAiB,CAAG3H,MAAM,CAAC,IAAI,CAAC,CACtC,KAAM,CAAA4H,MAAM,CAAG5H,MAAM,CAAC,IAAI,CAAC,CAE3B;AACA,KAAM,CAAA6H,kBAAkB,CAAG7H,MAAM,CAAC,IAAI,CAAC,CACvC,KAAM,CAAA8H,gBAAgB,CAAG9H,MAAM,CAAC,IAAI,CAAC,CACrC,KAAM,CAAA+H,eAAe,CAAG,IAAI,CAAE;AAE9B;AACA,KAAM,CAAAC,oBAAoB,CAAGhI,MAAM,CAACiI,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAC/C,KAAM,CAAAC,qBAAqB,CAAGnI,MAAM,CAAC,GAAI,CAAAuC,GAAG,CAAC,CAAC,CAAC,CAAE;AACjD,KAAM,CAAA6F,gBAAgB,CAAGpI,MAAM,CAAC,EAAE,CAAC,CAAE;AAKrC;AACA,KAAM,CAACqI,YAAY,CAAEC,eAAe,CAAC,CAAGrI,QAAQ,CAAC,CAC/CsI,SAAS,CAAE,CAAC,CACZC,QAAQ,CAAE,CAAC,CACXC,KAAK,CAAE,CAAC,CACRC,OAAO,CAAE,CACX,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAG3I,QAAQ,CAAC,QAAQ,CAAC,CAElD;AACA,KAAM,CAAA4I,oBAAoB,CAAG,CAC3BC,QAAQ,CAAE,OAAO,CACjBC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,kBAAkB,CAC7BC,MAAM,CAAE,IAAI,CAAG;AACfC,OAAO,CAAE,MAAM,CACfC,GAAG,CAAE,MACP,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG,CAClBC,OAAO,CAAE,UAAU,CACnBC,eAAe,CAAE,0BAA0B,CAC3CC,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,2BAA2B,CACtCC,UAAU,CAAE,eACd,CAAC,CAED;AACA,KAAM,CAAAC,SAAS,CAAG9J,MAAM,CAAC,IAAI,CAAC,CAE9B;AACA,KAAM,CAAC+J,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG/J,QAAQ,CAAC,IAAI,CAAC,CAEtE;AACA,KAAM,CAACgK,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGjK,QAAQ,CAAC,CAC7DkK,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,IAAI,CACbtB,QAAQ,CAAE,CAAEvD,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CACxB4E,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,EACV,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,mBAAmB,CAAGvK,MAAM,CAAC,IAAI,CAAC,CAExC;AACA,KAAM,CAAAwK,0BAA0B,CAAGxK,MAAM,CAAC,IAAI,CAAC,CAE/C;AACA2C,MAAM,CAAC8H,uBAAuB,CAAGP,sBAAsB,CAEvD;AACAvH,MAAM,CAAC4H,mBAAmB,CAAGA,mBAAmB,CAChD5H,MAAM,CAAC6H,0BAA0B,CAAGA,0BAA0B,CAE9D;AACA,KAAM,CAAAE,uBAAuB,CAAG,CAC9B5B,QAAQ,CAAE,OAAO,CACjB6B,GAAG,CAAE,MAAM,CACX3B,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,kBAAkB,CAC7B2B,KAAK,CAAE,OAAO,CAAG;AACjB1B,MAAM,CAAE,IAAI,CACZK,eAAe,CAAE,OAAO,CACxBE,YAAY,CAAE,KAAK,CACnBG,SAAS,CAAE,2BACb,CAAC,CAED;AACA,KAAM,CAAAiB,UAAU,CAAG,CACjB/B,QAAQ,CAAE,OAAO,CACjB6B,GAAG,CAAE,MAAM,CACX3B,IAAI,CAAE,kBAAkB,CAAG;AAC3BC,SAAS,CAAE,mBAAmB,CAC9BK,OAAO,CAAE,OAAO,CAAG;AACnBwB,UAAU,CAAE,MAAM,CAClBC,KAAK,CAAE,MAAM,CACbpB,QAAQ,CAAE,MAAM,CAChBqB,UAAU,CAAE,MAAM,CAClBC,UAAU,CAAE,2BAA2B,CACvC/B,MAAM,CAAE,IACV,CAAC,CAED;AACA,KAAM,CAACtF,gBAAgB,CAAC,CAAG3D,QAAQ,CAAC,GAAI,CAAAsC,GAAG,CAAC,CAAC,CAAC,CAE9C;AACA,KAAM,CAAC2I,UAAU,CAAEC,aAAa,CAAC,CAAGlL,QAAQ,CAAC,IAAI,CAAC,CAElD;AACA,KAAM,CAACyD,gBAAgB,CAAE0H,mBAAmB,CAAC,CAAGnL,QAAQ,CAAC,GAAI,CAAAsC,GAAG,CAAC,CAAC,CAAC,CAEnE;AACA,KAAM,CAAC8I,WAAW,CAAEC,cAAc,CAAC,CAAGrL,QAAQ,CAAC,CAAEsL,KAAK,CAAE,EAAE,CAAElB,OAAO,CAAE,EAAG,CAAC,CAAC,CAE1E;AACA,KAAM,CAAAmB,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAI9J,UAAU,GAAK,QAAQ,CAAE,CAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC,CACtB9B,UAAU,CAAG,QAAQ,CAErB;AACAmG,kBAAkB,CAAC4D,OAAO,CAAG,IAAI,CACjC3D,gBAAgB,CAAC2D,OAAO,CAAG,IAAI,CAE/B,GAAI9J,QAAQ,CAAE,CACZA,QAAQ,CAAC+J,OAAO,CAAG,KAAK,CAC1B,CACF,CACF,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAIjK,UAAU,GAAK,QAAQ,CAAE,CAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC,CACtB9B,UAAU,CAAG,QAAQ,CAErB;AACAmG,kBAAkB,CAAC4D,OAAO,CAAG,IAAI,CACjC3D,gBAAgB,CAAC2D,OAAO,CAAG,IAAI,CAE/B,GAAI3B,SAAS,CAAC2B,OAAO,EAAI9J,QAAQ,CAAE,CACjC;AACA;AACAmI,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAClD,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CACzC,KAAM,CAAAgG,UAAU,CAAG9B,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACzD,KAAK,CAAC,CAAC,CACrD,KAAM,CAAAwG,SAAS,CAAG/B,SAAS,CAAC2B,OAAO,CAACK,EAAE,CAACzG,KAAK,CAAC,CAAC,CAE9C;AACA,GAAI,CAAA9E,KAAK,CAACwL,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC,CAAEzG,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,GAAG,CAAEE,CAAC,CAAE,CAAE,CAAC,CAAE,IAAI,CAAC,CAChCsG,MAAM,CAAC1L,KAAK,CAAC2L,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,IAAM,CACdvC,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACwD,IAAI,CAACV,UAAU,CAAC,CAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC,CAEV;AACA,GAAI,CAAAhM,KAAK,CAACwL,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC,CAAEzG,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CAAE,IAAI,CAAC,CAC9BsG,MAAM,CAAC1L,KAAK,CAAC2L,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,IAAM,CACdvC,SAAS,CAAC2B,OAAO,CAACK,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC,CACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC,CAEV;AACA5K,QAAQ,CAAC6K,MAAM,CAAC5G,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5B,KAAM,CAAA6G,aAAa,CAAG9K,QAAQ,CAAC6K,MAAM,CAACnH,KAAK,CAAC,CAAC,CAE7C;AACA,GAAI,CAAA9E,KAAK,CAACwL,KAAK,CAACU,aAAa,CAAC,CAC3BT,EAAE,CAAC,CAAEzG,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CAAE,IAAI,CAAC,CAC9BsG,MAAM,CAAC1L,KAAK,CAAC2L,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,IAAM,CACd1K,QAAQ,CAAC6K,MAAM,CAACF,IAAI,CAACG,aAAa,CAAC,CACnC;AACA3C,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAAC/K,QAAQ,CAAC6K,MAAM,CAAC,CACzC7K,QAAQ,CAACgL,MAAM,CAAC,CAAC,CACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC,CAEV;AACA5K,QAAQ,CAAC+J,OAAO,CAAG,IAAI,CAEvB;AACA/J,QAAQ,CAACiL,WAAW,CAAG,EAAE,CACzBjL,QAAQ,CAACkL,WAAW,CAAG,GAAG,CAC1BlL,QAAQ,CAACmL,aAAa,CAAGrG,IAAI,CAACC,EAAE,CAAG,GAAG,CACtC/E,QAAQ,CAACoL,aAAa,CAAG,CAAC,CAC1BpL,QAAQ,CAACgL,MAAM,CAAC,CAAC,CACjB;AACA7C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC,CAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC,CAEzC1J,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE,CACrB0J,MAAM,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CACnBC,KAAK,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAChBC,KAAK,CAAE,IACT,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAC,wBAAwB,CAAIC,KAAK,EAAK,CAC1C,KAAM,CAAAC,YAAY,CAAG3M,iBAAiB,CAAC4M,aAAa,CAAC/I,IAAI,CAACgJ,CAAC,EAAIA,CAAC,CAACC,IAAI,GAAKJ,KAAK,CAAC,CAChF,GAAIC,YAAY,EAAIzD,SAAS,CAAC2B,OAAO,EAAI9J,QAAQ,CAAE,CACjDqI,uBAAuB,CAACuD,YAAY,CAAC,CAErC;AACA,KAAM,CAAAI,WAAW,CAAGpG,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAChDC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,CAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC,CAEDjF,OAAO,CAACC,GAAG,CAAC,WAAW,CAAE,CACvBsK,IAAI,CAAEP,YAAY,CAACG,IAAI,CACvBK,GAAG,CAAE,CACHxF,SAAS,CAAEgF,YAAY,CAAChF,SAAS,CACjCC,QAAQ,CAAE+E,YAAY,CAAC/E,QACzB,CAAC,CACDwF,IAAI,CAAEL,WACR,CAAC,CAAC,CAEF;AACAjM,UAAU,CAAG,cAAc,CAC3BkH,WAAW,CAAC,cAAc,CAAC,CAE3B;AACAkB,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAClD,GAAG,CAAC+H,WAAW,CAACpI,CAAC,CAAC,EAAE,CAAE,EAAE,CAAE,CAACoI,WAAW,CAAClI,CAAC,CAAC,EAAE,CAAC,CAEvE;AACA9D,QAAQ,CAAC6K,MAAM,CAAC5G,GAAG,CAAC+H,WAAW,CAACpI,CAAC,CAAE,CAAC,CAAE,CAACoI,WAAW,CAAClI,CAAC,CAAC,CAErD;AACAqE,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAAC/K,QAAQ,CAAC6K,MAAM,CAAC,CAEzC;AACA7K,QAAQ,CAAC+J,OAAO,CAAG,IAAI,CACvB/J,QAAQ,CAACgL,MAAM,CAAC,CAAC,CAEjB;AACA7C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC,CAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC,CAEzC1J,OAAO,CAACC,GAAG,CAAC,aAAa,CAAE,CACzBsK,IAAI,CAAEP,YAAY,CAACG,IAAI,CACvBO,IAAI,CAAEnE,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACoF,OAAO,CAAC,CAAC,CAC1CC,GAAG,CAAExM,QAAQ,CAAC6K,MAAM,CAAC0B,OAAO,CAAC,CAAC,CAC9BF,IAAI,CAAEL,WACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAS,iBAAiB,CAAGA,CAAC7C,KAAK,CAAE8C,OAAO,GAAK,CAC5C,GAAI,CACF,KAAM,CAAAC,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC,CAEnC;AACA,GAAI9C,KAAK,GAAK9I,WAAW,CAACO,GAAG,CAAE,KAAAyL,aAAA,CAC7B;AAEA;AACA,KAAM,CAAAC,SAAS,CAAGJ,OAAO,CAACK,GAAG,CAC7B,KAAM,CAAAC,gBAAgB,CAAGN,OAAO,CAACO,EAAE,CAEnC;AACA,KAAM,CAAAC,aAAa,CAAGpL,gBAAgB,CAACqC,GAAG,CAAC2I,SAAS,CAAC,CAErD;AACA,GAAII,aAAa,EAAIF,gBAAgB,CAAGE,aAAa,CAAE,CACrD;AACA;AACA;AACA;AACA;AACJ,OACF,CAEI;AACApL,gBAAgB,CAACkC,GAAG,CAAC8I,SAAS,CAAEE,gBAAgB,CAAC,CAEjD;AACA;AACA;AACA;AACA;AAEA,KAAM,CAAAG,YAAY,CAAG,EAAAN,aAAA,CAAAH,OAAO,CAACnK,IAAI,UAAAsK,aAAA,iBAAZA,aAAA,CAAcM,YAAY,GAAI,EAAE,CACrD,KAAM,CAAAC,KAAK,CAAGV,OAAO,CAACnK,IAAI,CAAC6K,KAAK,CAEhC;AACA,KAAM,CAAA9G,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CAEtB;AACA6G,YAAY,CAACE,OAAO,CAACC,WAAW,EAAI,CAClC;AACA;AACA,KAAM,CAAAC,EAAE,CAAIT,SAAS,CAAGQ,WAAW,CAACE,SAAS,CAC7C,KAAM,CAAAC,IAAI,CAAGH,WAAW,CAACI,WAAW,CAEpC,GAAGD,IAAI,GAAK,GAAG,EAAEA,IAAI,GAAK,GAAG,EAAEA,IAAI,GAAK,GAAG,CAAC,CAC5C;AACA;AACA;AACA,KAAM,CAAAE,KAAK,CAAG,CACZhH,SAAS,CAAEsF,UAAU,CAACqB,WAAW,CAACM,WAAW,CAAC,CAC9ChH,QAAQ,CAAEqF,UAAU,CAACqB,WAAW,CAACO,UAAU,CAAC,CAC5ChH,KAAK,CAAEoF,UAAU,CAACqB,WAAW,CAACQ,SAAS,CAAC,CACxChH,OAAO,CAAEmF,UAAU,CAACqB,WAAW,CAACS,WAAW,CAC7C,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGrI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAAC2B,KAAK,CAAChH,SAAS,CAAEgH,KAAK,CAAC/G,QAAQ,CAAC,CAEhF;AACA,GAAI,CAAAqH,cAAc,CAClB,OAAQR,IAAI,EACV,IAAK,GAAG,CAAE;AACRQ,cAAc,CAAGjO,qBAAqB,CACtC,MACF,IAAK,GAAG,CAAE;AACRiO,cAAc,CAAGhO,qBAAqB,CACtC,MACF,IAAK,GAAG,CAAE;AACRgO,cAAc,CAAG/N,oBAAoB,CACrC,MACF,QACE,OAAQ;AACZ,CAEA;AACA,GAAI,CAAAgO,KAAK,CAAGrM,aAAa,CAACsC,GAAG,CAACoJ,EAAE,CAAC,CAEjC,GAAI,CAACW,KAAK,EAAID,cAAc,CAAE,CAC5B;AACA,KAAM,CAAAE,QAAQ,CAAGV,IAAI,GAAK,GAAG,CAAG7O,aAAa,CAAC6E,KAAK,CAACvD,oBAAoB,CAAC,CAAG+N,cAAc,CAACxK,KAAK,CAAC,CAAC,CAClG;AACA,KAAM,CAAA2K,MAAM,CAAGX,IAAI,GAAK,GAAG,CAAG,GAAG,CAAG,GAAG,CACvCU,QAAQ,CAACjH,QAAQ,CAAClD,GAAG,CAACgK,QAAQ,CAACrK,CAAC,CAAEyK,MAAM,CAAE,CAACJ,QAAQ,CAACnK,CAAC,CAAC,CACtDsK,QAAQ,CAACE,QAAQ,CAACxK,CAAC,CAAGgB,IAAI,CAACC,EAAE,CAAG6I,KAAK,CAAC7G,OAAO,CAAGjC,IAAI,CAACC,EAAE,CAAG,GAAG,CAE7D;AACA,GAAI2I,IAAI,GAAK,GAAG,CAAE,CAChB;AACAU,QAAQ,CAACG,KAAK,CAACtK,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC3B;AACA,KAAM,CAAAuK,KAAK,CAAG,GAAI,CAAAhQ,KAAK,CAACiQ,cAAc,CAACL,QAAQ,CAAC,CAEhD;AACA,KAAM,CAAAM,MAAM,CAAGF,KAAK,CAACG,UAAU,CAACrO,eAAe,CAACsO,UAAU,CAAC,CAAC,CAAC,CAAC,CAC9DF,MAAM,CAACG,IAAI,CAAC,CAAC,CAEbrI,qBAAqB,CAACsD,OAAO,CAAC7F,GAAG,CAACuJ,EAAE,CAAEgB,KAAK,CAAC,CAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,CAEAnO,KAAK,CAACyO,GAAG,CAACV,QAAQ,CAAC,CAEnBtM,aAAa,CAACmC,GAAG,CAACuJ,EAAE,CAAE,CACpBW,KAAK,CAAEC,QAAQ,CACfW,UAAU,CAAExI,GAAG,CACfmH,IAAI,CAAEA,IACR,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIS,KAAK,CAAE,CAChB;AACAA,KAAK,CAACA,KAAK,CAAChH,QAAQ,CAAClD,GAAG,CAACgK,QAAQ,CAACrK,CAAC,CAAEuK,KAAK,CAACT,IAAI,GAAK,GAAG,CAAG,GAAG,CAAG,GAAG,CAAE,CAACO,QAAQ,CAACnK,CAAC,CAAC,CACjFqK,KAAK,CAACA,KAAK,CAACG,QAAQ,CAACxK,CAAC,CAAGgB,IAAI,CAACC,EAAE,CAAG6I,KAAK,CAAC7G,OAAO,CAAGjC,IAAI,CAACC,EAAE,CAAG,GAAG,CAChEoJ,KAAK,CAACY,UAAU,CAAGxI,GAAG,CACtB4H,KAAK,CAACA,KAAK,CAAC9C,YAAY,CAAC,CAAC,CAC1B8C,KAAK,CAACA,KAAK,CAAC7C,iBAAiB,CAAC,IAAI,CAAC,CACrC,CACA,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAA0D,iBAAiB,CAAG,IAAI,CAC9B,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAC,GAAG,CAAC9B,YAAY,CAAC+B,GAAG,CAACC,CAAC,EAAIA,CAAC,CAAC3B,SAAS,CAAC,CAAC,CAE9D3L,aAAa,CAACwL,OAAO,CAAC,CAAC+B,SAAS,CAAE7B,EAAE,GAAK,CACvC,GAAIjH,GAAG,CAAG8I,SAAS,CAACN,UAAU,CAAGC,iBAAiB,EAAI,CAACC,UAAU,CAAC/K,GAAG,CAACsJ,EAAE,CAAC,CAAE,CACzEnN,KAAK,CAACiP,MAAM,CAACD,SAAS,CAAClB,KAAK,CAAC,CAC7BrM,aAAa,CAACyN,MAAM,CAAC/B,EAAE,CAAC,CACxB;AACF,CACF,CAAC,CAAC,CACF,OACF,CAEA;AACA,GAAI5D,KAAK,GAAK9I,WAAW,CAACM,GAAG,CAAE,CAC7B;AAEA,KAAM,CAAAoO,OAAO,CAAG7C,OAAO,CAACnK,IAAI,CAC5B,KAAM,CAAAiN,KAAK,CAAGD,OAAO,CAACvM,KAAK,CAC3B,KAAM,CAAAyM,QAAQ,CAAG,CACf9I,SAAS,CAAEsF,UAAU,CAACsD,OAAO,CAACG,QAAQ,CAAC,CACvC9I,QAAQ,CAAEqF,UAAU,CAACsD,OAAO,CAACI,OAAO,CAAC,CACrC9I,KAAK,CAAEoF,UAAU,CAACsD,OAAO,CAACzB,SAAS,CAAC,CACpChH,OAAO,CAAEmF,UAAU,CAACsD,OAAO,CAACxB,WAAW,CACzC,CAAC,CAED;AACA;AAEA;AACAhN,MAAM,CAAC6O,WAAW,CAAC,CACjBnC,IAAI,CAAE,iBAAiB,CACvBoC,MAAM,CAAE,aACV,CAAC,CAAE,GAAG,CAAC,CAEP;AACA9O,MAAM,CAAC6O,WAAW,CAAC,CACjBnC,IAAI,CAAE,KAAK,CACXzK,KAAK,CAAEwM,KAAK,CAAE;AACdjN,IAAI,CAAE,CAAQ;AACZS,KAAK,CAAEwM,KAAK,CACZ1B,SAAS,CAAEyB,OAAO,CAACzB,SAAS,CAC5B6B,OAAO,CAAEJ,OAAO,CAACI,OAAO,CACxBD,QAAQ,CAAEH,OAAO,CAACG,QAAQ,CAC1B3B,WAAW,CAAEwB,OAAO,CAACxB,WACvB,CACF,CAAC,CAAE,GAAG,CAAC,CAEP;AACA,KAAM,CAAAC,QAAQ,CAAGrI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAACyD,QAAQ,CAAC9I,SAAS,CAAE8I,QAAQ,CAAC7I,QAAQ,CAAC,CACtF,KAAM,CAAAkJ,eAAe,CAAG,GAAI,CAAAvR,KAAK,CAACkG,OAAO,CAACuJ,QAAQ,CAACrK,CAAC,CAAE,GAAG,CAAE,CAACqK,QAAQ,CAACnK,CAAC,CAAC,CACvE,KAAM,CAAAkM,eAAe,CAAGlL,IAAI,CAACC,EAAE,CAAG2K,QAAQ,CAAC3I,OAAO,CAAGjC,IAAI,CAACC,EAAE,CAAG,GAAG,CAElE;AACA,KAAM,CAAAkL,WAAW,CAAG1M,cAAc,CAACwM,eAAe,CAAEN,KAAK,CAAC,CAC1D,KAAM,CAAA7K,WAAW,CAAGD,cAAc,CAACqL,eAAe,CAAEP,KAAK,CAAC,CAE1D;AACA,GAAI,CAAAS,UAAU,CAAGpO,aAAa,CAACsC,GAAG,CAACqL,KAAK,CAAC,CAEzC;AACA,KAAM,CAAAzM,aAAa,CAAGyM,KAAK,GAAKzN,gBAAgB,CAEhD,GAAI,CAACkO,UAAU,EAAIjQ,qBAAqB,CAAE,CACxC;AACA,KAAM,CAAAkQ,eAAe,CAAGlQ,qBAAqB,CAACyD,KAAK,CAAC,CAAC,CAErD;AACA;AACAyM,eAAe,CAAChJ,QAAQ,CAAClD,GAAG,CAACgM,WAAW,CAACrM,CAAC,CAAE,CAAC,CAAC,CAAEqM,WAAW,CAACjM,CAAC,CAAC,CAC9DmM,eAAe,CAAC7B,QAAQ,CAACxK,CAAC,CAAGc,WAAW,CAExC;AACAuL,eAAe,CAACC,QAAQ,CAAEC,KAAK,EAAK,CAClC,GAAIA,KAAK,CAACC,MAAM,EAAID,KAAK,CAACE,QAAQ,CAAE,CAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA,KAAM,CAAAC,WAAW,CAAGH,KAAK,CAACE,QAAQ,CAAC7M,KAAK,CAAC,CAAC,CAC1C2M,KAAK,CAACE,QAAQ,CAAGC,WAAW,CAE5B;AACA,GAAIxN,aAAa,CAAE,CACjBwN,WAAW,CAACpH,KAAK,CAACnF,GAAG,CAAC,QAAQ,CAAC,CACjC,CAAC,IAAM,CACLuM,WAAW,CAACpH,KAAK,CAACnF,GAAG,CAAC,QAAQ,CAAC,CACjC,CACAuM,WAAW,CAACC,QAAQ,CAAG,GAAI,CAAAjS,KAAK,CAACkS,KAAK,CAAC,QAAQ,CAAC,CAChDF,WAAW,CAACG,WAAW,CAAG,IAAI,CAC9B;AACAH,WAAW,CAACI,WAAW,CAAG,IAAI,CAChC,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,UAAU,CAAGC,gBAAgB,CAAC,GAAGhM,IAAI,CAACiM,KAAK,CAACrB,QAAQ,CAAC5I,KAAK,CAAC,OAAO,CAAE,CACxEc,eAAe,CAAE5E,aAAa,CAC5B,CAAEgO,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAI,CAAC,CAAG;AACnC,CAAEH,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,GAAI,CAAC,CAAG;AACrCC,SAAS,CAAE,CAAEJ,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAI,CAAC,CAAE;AAC/CnJ,QAAQ,CAAE,EAAE,CACZL,OAAO,CAAE,CACX,CAAC,CAAC,CACFkJ,UAAU,CAAC1J,QAAQ,CAAClD,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAE;AAClC4M,UAAU,CAACQ,WAAW,CAAG,IAAI,CAAE;AAC/BR,UAAU,CAACN,QAAQ,CAACe,OAAO,CAAG,GAAG,CAAE;AACnCnB,eAAe,CAACrB,GAAG,CAAC+B,UAAU,CAAC,CAE/BxQ,KAAK,CAACyO,GAAG,CAACqB,eAAe,CAAC,CAE1B;AACArO,aAAa,CAACmC,GAAG,CAACwL,KAAK,CAAE,CACvBtB,KAAK,CAAEgC,eAAe,CACtBpB,UAAU,CAAEzI,IAAI,CAACC,GAAG,CAAC,CAAC,CACtBmH,IAAI,CAAE,GAAG,CAAE;AACX6D,MAAM,CAAEvO,aAAa,CACrB6N,UAAU,CAAEA,UAAW;AACzB,CAAC,CAAC,CAEF;AAEA;AACA,GAAI,CAAAjS,KAAK,CAACwL,KAAK,CAAC+F,eAAe,CAAChJ,QAAQ,CAAC,CACtCkD,EAAE,CAAC,CAAEvG,CAAC,CAAE,GAAI,CAAC,CAAE,GAAG,CAAC,CACnBwG,MAAM,CAAC1L,KAAK,CAAC2L,MAAM,CAACC,SAAS,CAACgH,GAAG,CAAC,CAClC5G,KAAK,CAAC,CAAC,CAEV;AACAuF,eAAe,CAACC,QAAQ,CAAEC,KAAK,EAAK,CAClC,GAAIA,KAAK,CAACC,MAAM,EAAID,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACI,WAAW,CAAE,CAChE,GAAI,CAAA/R,KAAK,CAACwL,KAAK,CAAC,CAAEkH,OAAO,CAAE,GAAI,CAAC,CAAC,CAC9BjH,EAAE,CAAC,CAAEiH,OAAO,CAAE,GAAI,CAAC,CAAE,GAAG,CAAC,CACzBhH,MAAM,CAAC1L,KAAK,CAAC2L,MAAM,CAACC,SAAS,CAACgH,GAAG,CAAC,CAClC9G,QAAQ,CAAC,UAAW,CACnB2F,KAAK,CAACE,QAAQ,CAACe,OAAO,CAAG,IAAI,CAACA,OAAO,CACrCjB,KAAK,CAACE,QAAQ,CAACK,WAAW,CAAG,IAAI,CACnC,CAAC,CAAC,CACDhG,KAAK,CAAC,CAAC,CACZ,CACF,CAAC,CAAC,CAEF;AACA,GAAI,CAAAhM,KAAK,CAACwL,KAAK,CAAC,CAAEkH,OAAO,CAAE,GAAI,CAAC,CAAC,CAC9BjH,EAAE,CAAC,CAAEiH,OAAO,CAAE,GAAI,CAAC,CAAE,GAAG,CAAC,CACzBhH,MAAM,CAAC1L,KAAK,CAAC2L,MAAM,CAACC,SAAS,CAACgH,GAAG,CAAC,CAClC9G,QAAQ,CAAC,UAAW,CACnBmG,UAAU,CAACN,QAAQ,CAACe,OAAO,CAAG,IAAI,CAACA,OAAO,CAC1CT,UAAU,CAACN,QAAQ,CAACK,WAAW,CAAG,IAAI,CACxC,CAAC,CAAC,CACDhG,KAAK,CAAC,CAAC,CAEV;AACA,GAAI5H,aAAa,CAAE,CACjBxD,gBAAgB,CAAG2Q,eAAe,CAClCxJ,eAAe,CAAC+I,QAAQ,CAAC,CACzB9N,OAAO,CAACC,GAAG,CAAC,WAAW,CAAE4N,KAAK,CAAC,CACjC,CACF,CAAC,IAAM,IAAIS,UAAU,CAAE,CACrB;AACA,KAAM,CAAAuB,gBAAgB,CAAGlO,cAAc,CAAC0M,WAAW,CAAER,KAAK,CAAC,CAC3D,KAAM,CAAAzK,gBAAgB,CAAGL,cAAc,CAACC,WAAW,CAAE6K,KAAK,CAAC,CAE3D;AACAS,UAAU,CAAC/B,KAAK,CAAChH,QAAQ,CAACwD,IAAI,CAAC8G,gBAAgB,CAAC,CAChDvB,UAAU,CAAC/B,KAAK,CAACG,QAAQ,CAACxK,CAAC,CAAGkB,gBAAgB,CAC9CkL,UAAU,CAAC/B,KAAK,CAAC9C,YAAY,CAAC,CAAC,CAC/B6E,UAAU,CAAC/B,KAAK,CAAC7C,iBAAiB,CAAC,IAAI,CAAC,CACxC4E,UAAU,CAACnB,UAAU,CAAGzI,IAAI,CAACC,GAAG,CAAC,CAAC,CAClC2J,UAAU,CAACqB,MAAM,CAAGvO,aAAa,CAAE;AAEnC;AACA,GAAIkN,UAAU,CAACW,UAAU,CAAE,CACzBX,UAAU,CAACW,UAAU,CAACN,QAAQ,CAACpB,GAAG,CAACuC,OAAO,CAAC,CAAC,CAC5CxB,UAAU,CAAC/B,KAAK,CAACmB,MAAM,CAACY,UAAU,CAACW,UAAU,CAAC,CAChD,CAEA;AACA,KAAM,CAAAA,UAAU,CAAGC,gBAAgB,CAAC,GAAGhM,IAAI,CAACiM,KAAK,CAACrB,QAAQ,CAAC5I,KAAK,CAAG,GAAG,CAAC,OAAO,CAAE,CAC9Ec,eAAe,CAAE5E,aAAa,CAC5B,CAAEgO,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAI,CAAC,CAAG;AACnC,CAAEH,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,GAAI,CAAC,CAAG;AACrCC,SAAS,CAAE,CAAEJ,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAI,CAAC,CAAE;AAC/CnJ,QAAQ,CAAE,EAAE,CACZL,OAAO,CAAE,CACX,CAAC,CAAC,CACFkJ,UAAU,CAAC1J,QAAQ,CAAClD,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAAE;AACnC4M,UAAU,CAACQ,WAAW,CAAG,IAAI,CAAE;AAC/BnB,UAAU,CAAC/B,KAAK,CAACW,GAAG,CAAC+B,UAAU,CAAC,CAChCX,UAAU,CAACW,UAAU,CAAGA,UAAU,CAElC;AAEA;AACA,GAAI7N,aAAa,CAAE,CACjBxD,gBAAgB,CAAG0Q,UAAU,CAAC/B,KAAK,CACnCxH,eAAe,CAAC+I,QAAQ,CAAC,CAC3B,CACF,CAEA;AACA,KAAM,CAAAnJ,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CACtB,KAAM,CAAAyI,iBAAiB,CAAG,IAAI,CAAE;AAEhClN,aAAa,CAACwL,OAAO,CAAC,CAAC+B,SAAS,CAAE7B,EAAE,GAAK,CACvC,KAAM,CAAAmE,mBAAmB,CAAGpL,GAAG,CAAG8I,SAAS,CAACN,UAAU,CAEtD;AACA,GAAI4C,mBAAmB,CAAG3C,iBAAiB,CAAG,GAAG,EAAI2C,mBAAmB,EAAI3C,iBAAiB,CAAE,CAC7F;AACA,KAAM,CAAAsC,OAAO,CAAG,CAAC,CAEjBjC,SAAS,CAAClB,KAAK,CAACiC,QAAQ,CAAEC,KAAK,EAAK,CAClC,GAAIA,KAAK,CAACC,MAAM,EAAID,KAAK,CAACE,QAAQ,CAAE,CAClC;AACA,GAAIF,KAAK,CAACE,QAAQ,CAACI,WAAW,GAAKiB,SAAS,CAAE,CAC5CvB,KAAK,CAACE,QAAQ,CAACsB,mBAAmB,CAAGxB,KAAK,CAACE,QAAQ,CAACI,WAAW,EAAI,KAAK,CACxEN,KAAK,CAACE,QAAQ,CAACuB,eAAe,CAAGzB,KAAK,CAACE,QAAQ,CAACe,OAAO,EAAI,GAAG,CAChE,CAEA;AACAjB,KAAK,CAACE,QAAQ,CAACI,WAAW,CAAG,IAAI,CACjCN,KAAK,CAACE,QAAQ,CAACe,OAAO,CAAGA,OAAO,CAChCjB,KAAK,CAACE,QAAQ,CAACK,WAAW,CAAG,IAAI,CACnC,CACF,CAAC,CAAC,CAEF;AACA,GAAIvB,SAAS,CAACwB,UAAU,CAAE,CACxBxB,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACe,OAAO,CAAGA,OAAO,CAC/CjC,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACK,WAAW,CAAG,IAAI,CAClD,CACF,CACA;AAAA,IACK,IAAIe,mBAAmB,CAAG3C,iBAAiB,CAAE,CAChD;AACA,GAAIK,SAAS,CAACwB,UAAU,CAAE,CACxBxB,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACpB,GAAG,CAACuC,OAAO,CAAC,CAAC,CAC3CrC,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACmB,OAAO,CAAC,CAAC,CACvCrC,SAAS,CAAClB,KAAK,CAACmB,MAAM,CAACD,SAAS,CAACwB,UAAU,CAAC,CAC9C,CAEAxB,SAAS,CAAClB,KAAK,CAACiC,QAAQ,CAAEC,KAAK,EAAK,CAClC,GAAIA,KAAK,CAACC,MAAM,CAAE,CAChB,GAAID,KAAK,CAACE,QAAQ,CAAE,CAClB,GAAI5N,KAAK,CAACC,OAAO,CAACyN,KAAK,CAACE,QAAQ,CAAC,CAAE,CACjCF,KAAK,CAACE,QAAQ,CAACjD,OAAO,CAACyE,CAAC,EAAIA,CAAC,CAACL,OAAO,CAAC,CAAC,CAAC,CAC1C,CAAC,IAAM,CACLrB,KAAK,CAACE,QAAQ,CAACmB,OAAO,CAAC,CAAC,CAC1B,CACF,CACA,GAAIrB,KAAK,CAAC2B,QAAQ,CAAE3B,KAAK,CAAC2B,QAAQ,CAACN,OAAO,CAAC,CAAC,CAC9C,CACF,CAAC,CAAC,CAEF;AACArR,KAAK,CAACiP,MAAM,CAACD,SAAS,CAAClB,KAAK,CAAC,CAC7BrM,aAAa,CAACyN,MAAM,CAAC/B,EAAE,CAAC,CACxB;AACA7M,oBAAoB,CAAC4O,MAAM,CAAC/B,EAAE,CAAC,CAC/B3M,oBAAoB,CAAC0O,MAAM,CAAC/B,EAAE,CAAC,CAE/B5L,OAAO,CAACC,GAAG,CAAC,mBAAmB2L,EAAE,EAAE,CAAC,CACtC,CACF,CAAC,CAAC,CAEF,OACF,CAEA;AACA,GAAI5D,KAAK,GAAK9I,WAAW,CAACS,IAAI,CAAE,CAC9B;AAEA,GAAI,CACF,KAAM,CAAAoL,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC,CAEnC;AACA,KAAM,CAAAK,SAAS,CAAGJ,OAAO,CAACK,GAAG,CAC7B,KAAM,CAAAC,gBAAgB,CAAGN,OAAO,CAACO,EAAE,CAEnC;AACA,KAAM,CAAAC,aAAa,CAAGpL,gBAAgB,CAACqC,GAAG,CAAC2I,SAAS,CAAC,CAErD;AACA,GAAII,aAAa,EAAIF,gBAAgB,CAAGE,aAAa,CAAE,CACrD;AACA;AACA;AACA;AACA;AACA,OACF,CAEA;AACApL,gBAAgB,CAACkC,GAAG,CAAC8I,SAAS,CAAEE,gBAAgB,CAAC,CAEjD;AACA,GAAIN,OAAO,CAACnK,IAAI,EAAImK,OAAO,CAACnK,IAAI,CAACqJ,aAAa,EAAIlJ,KAAK,CAACC,OAAO,CAAC+J,OAAO,CAACnK,IAAI,CAACqJ,aAAa,CAAC,CAAE,CAC3Fc,OAAO,CAACnK,IAAI,CAACqJ,aAAa,CAACyB,OAAO,CAAC1B,YAAY,EAAI,CACjD,KAAM,CAAAnD,OAAO,CAAGmD,YAAY,CAACnD,OAAO,CAEpC,GAAI,CAACA,OAAO,CAAE,CACZ7G,OAAO,CAACsB,KAAK,CAAC,kBAAkB,CAAE0I,YAAY,CAAC,CAC/C,OACF,CAEA;AAEA;AACA,GAAIA,YAAY,CAACjD,MAAM,EAAIhG,KAAK,CAACC,OAAO,CAACgJ,YAAY,CAACjD,MAAM,CAAC,CAAE,CAC7D;AACA,KAAM,CAAAsJ,UAAU,CAAG,EAAE,CAErBrG,YAAY,CAACjD,MAAM,CAAC2E,OAAO,CAAC4E,KAAK,EAAI,CACnC;AACA,GAAI,CAACA,KAAK,CAACC,OAAO,CAAE,CAClBvQ,OAAO,CAACsB,KAAK,CAAC,gBAAgB,CAAEgP,KAAK,CAAC,CACtC,OACF,CAEA,KAAM,CAAAC,OAAO,CAAGD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC,CACxC;AACA,KAAM,CAAAC,SAAS,CAAGH,KAAK,CAACI,YAAY,CAClCC,oBAAoB,CAACL,KAAK,CAACI,YAAY,CAAC,CACxCE,iBAAiB,CAACL,OAAO,CAAC,CAE5B;AACA,KAAM,CAAAM,UAAU,CAAGP,KAAK,CAACQ,YAAY,EAAI,GAAG,CAAE;AAC9C,KAAM,CAAAC,UAAU,CAAGC,QAAQ,CAACV,KAAK,CAACS,UAAU,CAAC,EAAI,CAAC,CAElD;AAEA;AACA,KAAM,CAAAE,SAAS,CAAG,CAChBV,OAAO,CACPE,SAAS,CACTK,YAAY,CAAED,UAAU,CACxBE,UACF,CAAC,CAED;AACAV,UAAU,CAACa,IAAI,CAACD,SAAS,CAAC,CAE1B;AACA;AACA,GAAI,CAAAE,eAAe,CAAGC,MAAM,CAACvK,OAAO,CAAC,CACrC,GAAI,CAAAwK,iBAAiB,CAAGhR,gBAAgB,CAACmC,GAAG,CAAC2O,eAAe,CAAC,CAE7D,GAAI,CAACE,iBAAiB,CAAE,CACtB;AACAF,eAAe,CAAGH,QAAQ,CAACnK,OAAO,CAAC,CACnCwK,iBAAiB,CAAGhR,gBAAgB,CAACmC,GAAG,CAAC2O,eAAe,CAAC,CAC3D,CAEA,GAAIE,iBAAiB,CAAE,CACrB;AACAC,wBAAwB,CAACD,iBAAiB,CAAEJ,SAAS,CAAC,CAEtD;AACA,GAAIzK,oBAAoB,EAAIA,oBAAoB,CAACK,OAAO,GAAKA,OAAO,CAAE,CACpEF,sBAAsB,CAAC4K,IAAI,GAAK,CAC9B,GAAGA,IAAI,CACP3K,OAAO,CAAE,IAAI,CACb2J,OAAO,CACPE,SAAS,CACTzE,KAAK,CAAE6E,UAAU,CACjBE,UACF,CAAC,CAAC,CAAC,CACL,CACF,CAAC,IAAM,CACL;AAAA,CAEJ,CAAC,CAAC,CAEF;AACA,GAAI,CAAAS,QAAQ,CAAG,IAAI,CACnB;AACA,KAAM,CAAAC,KAAK,CAAGL,MAAM,CAACvK,OAAO,CAAC,CAC7B,GAAIxG,gBAAgB,CAACiC,GAAG,CAACmP,KAAK,CAAC,CAAE,CAC/BD,QAAQ,CAAGC,KAAK,CAClB,CAAC,IAAM,CACL;AACA,KAAM,CAAAC,KAAK,CAAGV,QAAQ,CAACnK,OAAO,CAAC,CAC/B,GAAIxG,gBAAgB,CAACiC,GAAG,CAACoP,KAAK,CAAC,CAAE,CAC/BF,QAAQ,CAAGE,KAAK,CAClB,CACF,CAEA,GAAIF,QAAQ,GAAK,IAAI,CAAE,CACrB;AACAlR,kBAAkB,CAAC+B,GAAG,CAACmP,QAAQ,CAAE,CAC/BG,UAAU,CAAEjN,IAAI,CAACC,GAAG,CAAC,CAAC,CACtBoC,MAAM,CAAEsJ,UACV,CAAC,CAAC,CACFrQ,OAAO,CAACC,GAAG,CAAC,aAAauR,QAAQ,KAAK,MAAO,CAAAA,QAAQ,aAAa,CAAC,CAEnE;AACA,GAAIpS,MAAM,CAAC4H,mBAAmB,GAC1B5H,MAAM,CAAC4H,mBAAmB,CAACkB,OAAO,GAAKsJ,QAAQ,EAC/CpS,MAAM,CAAC4H,mBAAmB,CAACkB,OAAO,GAAKkJ,MAAM,CAACI,QAAQ,CAAC,EACvDpS,MAAM,CAAC4H,mBAAmB,CAACkB,OAAO,GAAK8I,QAAQ,CAACQ,QAAQ,CAAC,CAAC,CAAE,CAE9DxR,OAAO,CAACC,GAAG,CAAC,eAAeuR,QAAQ,aAAa,CAAC,CACjD;AACApS,MAAM,CAAC4H,mBAAmB,CAACkB,OAAO,CAAGsJ,QAAQ,CAE7C;AACA,GAAIpS,MAAM,CAAC6H,0BAA0B,EAAI,CAAC7H,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,CAAE,CACnFlI,OAAO,CAACC,GAAG,CAAC,SAASuR,QAAQ,aAAa,CAAC,CAC3CI,UAAU,CAAC,IAAM,CACfxS,MAAM,CAACyS,qBAAqB,CAACL,QAAQ,CAAC,CACxC,CAAC,CAAE,GAAG,CAAC,CACT,CACF,CACF,CAAC,IAAM,CACL;AACAlR,kBAAkB,CAAC+B,GAAG,CAACwE,OAAO,CAAE,CAC9B8K,UAAU,CAAEjN,IAAI,CAACC,GAAG,CAAC,CAAC,CACtBoC,MAAM,CAAEsJ,UACV,CAAC,CAAC,CACF;AACF,CACF,CAAC,IAAM,CACLrQ,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAE0I,YAAY,CAAC,CAC9C,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACLhK,OAAO,CAACsB,KAAK,CAAC,oCAAoC,CAAEyJ,OAAO,CAAC,CAC9D,CACF,CAAE,MAAOzJ,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAEwJ,OAAO,CAAC,CAC9C,CACF,CAEA;AACA,GAAI9C,KAAK,GAAK9I,WAAW,CAACQ,GAAG,EAAIqL,OAAO,CAACe,IAAI,GAAK,KAAK,CAAE,CACvD;AAEA;AACA1M,MAAM,CAAC6O,WAAW,CAAC,CACjBnC,IAAI,CAAE,KAAK,CACXlL,IAAI,CAAEmK,OAAO,CAACnK,IAChB,CAAC,CAAE,GAAG,CAAC,CAEP,KAAM,CAAAkR,OAAO,CAAG/G,OAAO,CAACnK,IAAI,CAC5B,KAAM,CAAAmR,KAAK,CAAGD,OAAO,CAACC,KAAK,CAC3B,KAAM,CAAAC,MAAM,CAAGF,OAAO,CAACG,IAAI,EAAI,EAAE,CAEjCD,MAAM,CAACtG,OAAO,CAACwG,KAAK,EAAI,CACtB,KAAM,CAAAC,OAAO,CAAGD,KAAK,CAACE,KAAK,CAC3B,KAAM,CAAAC,SAAS,CAAGH,KAAK,CAACG,SAAS,CACjC,KAAM,CAAAC,WAAW,CAAGJ,KAAK,CAACI,WAAW,CACrC,KAAM,CAAAC,SAAS,CAAGL,KAAK,CAACK,SAAS,CACjC,KAAM,CAAAC,OAAO,CAAGN,KAAK,CAACM,OAAO,CAE7B;AACA,KAAM,CAAAnG,QAAQ,CAAGrI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAC7CC,UAAU,CAACwH,OAAO,CAACW,OAAO,CAAC,CAC3BnI,UAAU,CAACwH,OAAO,CAACY,MAAM,CAC3B,CAAC,CAED;AACA,GAAI,CAAAC,WAAW,CAAG,EAAE,CACpB,GAAI,CAAAC,YAAY,CAAG,EAAE,CAErB,OAAOP,SAAS,EACd,IAAK,KAAK,CAAG;AACXM,WAAW,CAAG,OAAO,CACrBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,OAAO,CACrBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,QAAQ,CACtBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,MAAM,CAAE;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,QACED,WAAW,CAAGL,WAAW,EAAI,MAAM,CACnCM,YAAY,CAAG,SAAS,CAC5B,CAEA;AACAC,iBAAiB,CAACxG,QAAQ,CAAEsG,WAAW,CAAEC,YAAY,CAAC,CAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,CAAC,CAAC,CAEF,OACF,CAEA;AACA,GAAI5K,KAAK,GAAK9I,WAAW,CAACT,KAAK,EAAIsM,OAAO,CAACe,IAAI,GAAK,OAAO,CAAE,CAC3D;AAEA,KAAM,CAAAgH,SAAS,CAAG/H,OAAO,CAACnK,IAAI,CAC9B,KAAM,CAAAmS,OAAO,CAAGD,SAAS,CAACC,OAAO,CACjC,KAAM,CAAAC,SAAS,CAAGF,SAAS,CAACE,SAAS,CACrC,KAAM,CAAAC,SAAS,CAAGH,SAAS,CAACG,SAAS,CACrC,KAAM,CAAA1N,QAAQ,CAAG,CACfN,QAAQ,CAAEqF,UAAU,CAACwI,SAAS,CAAC9E,OAAO,CAAC,CACvChJ,SAAS,CAAEsF,UAAU,CAACwI,SAAS,CAAC/E,QAAQ,CAC1C,CAAC,CAED;AACA,KAAM,CAAA1B,QAAQ,CAAGrI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAAC9E,QAAQ,CAACP,SAAS,CAAEO,QAAQ,CAACN,QAAQ,CAAC,CAEtF;AACA,OAAO+N,SAAS,EACd,IAAK,GAAG,CAAG;AACTH,iBAAiB,CAACxG,QAAQ,CAAE,UAAU,CAAE,SAAS,CAAC,CAClD,MACF,IAAK,KAAK,CAAG;AACXwG,iBAAiB,CAACxG,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,KAAK,CAAG;AACXwG,iBAAiB,CAACxG,QAAQ,CAAE,OAAO,CAAE,SAAS,CAAC,CAC/C,MACF,IAAK,IAAI,CAAG;AACV,KAAM,CAAA6G,UAAU,CAAGJ,SAAS,CAACK,UAAU,CAAG;AAC1CN,iBAAiB,CAACxG,QAAQ,CAAE,KAAK6G,UAAU,MAAM,CAAE,SAAS,CAAC,CAC7D,MACF,IAAK,IAAI,CAAG;AACVL,iBAAiB,CAACxG,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,IAAI,CAAG;AACVwG,iBAAiB,CAACxG,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,MAAM,CAAG;AACZwG,iBAAiB,CAACxG,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,IAAI,CAAG;AACVwG,iBAAiB,CAACxG,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,IAAI,CAAG;AACVwG,iBAAiB,CAACxG,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,KAAK,CAAG;AACX,KAAM,CAAA+G,YAAY,CAAGN,SAAS,CAACK,UAAU,CAAG;AAC5C,KAAM,CAAAE,QAAQ,CAAGP,SAAS,CAACQ,UAAU,CAAO;AAC5CT,iBAAiB,CAACxG,QAAQ,CAAE,QAAQkH,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,CAAE,SAAS,CAAC,CAC/F,MACJ,CAEA,OACF,CACA;AACA;AACA;AACA;AACA;AACA;AAEF,CAAE,MAAO/R,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnCtB,OAAO,CAACsB,KAAK,CAAC,SAAS,CAAEwJ,OAAO,CAAC,CACnC,CACF,CAAC,CAED;AACA,KAAM,CAAA0I,cAAc,CAAGA,CAAA,GAAM,CAC3BxT,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAE7B,KAAM,CAAAwT,KAAK,CAAG,QAAQvU,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO,CACnES,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEwT,KAAK,CAAC,CAEpC;AACA,KAAM,CAAAC,EAAE,CAAG,GAAI,CAAAC,SAAS,CAACF,KAAK,CAAC,CAE/BC,EAAE,CAACE,MAAM,CAAG,IAAM,CAChB5T,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC9B,CAAC,CAEDyT,EAAE,CAACG,SAAS,CAAI3B,KAAK,EAAK,CACxB,GAAI,CACF,KAAM,CAAApH,OAAO,CAAGE,IAAI,CAACC,KAAK,CAACiH,KAAK,CAACtR,IAAI,CAAC,CAEtC;AACA,GAAIkK,OAAO,CAACgB,IAAI,GAAK,SAAS,CAAE,CAC9B9L,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE6K,OAAO,CAAC,CAC/B,OACF,CAEA;AACA,GAAIA,OAAO,CAACgB,IAAI,GAAK,MAAM,CAAE,CAC3B,OACF,CAEA;AACA,GAAIhB,OAAO,CAACgB,IAAI,GAAK,SAAS,EAAIhB,OAAO,CAAC9C,KAAK,EAAI8C,OAAO,CAACC,OAAO,CAAE,CAClE;AACAF,iBAAiB,CAACC,OAAO,CAAC9C,KAAK,CAAEgD,IAAI,CAAC8I,SAAS,CAAChJ,OAAO,CAACC,OAAO,CAAC,CAAC,CACnE,CACF,CAAE,MAAOzJ,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CAC1C,CACF,CAAC,CAEDoS,EAAE,CAACK,OAAO,CAAIzS,KAAK,EAAK,CACtBtB,OAAO,CAACsB,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACtC,CAAC,CAEDoS,EAAE,CAACM,OAAO,CAAG,IAAM,CACjBhU,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC5B;AACA2R,UAAU,CAAC4B,cAAc,CAAE,IAAI,CAAC,CAClC,CAAC,CAED;AACArP,aAAa,CAAC+D,OAAO,CAAGwL,EAAE,CAC5B,CAAC,CAEDlX,SAAS,CAAC,IAAM,CACd,GAAI,CAACsH,YAAY,CAACoE,OAAO,CAAE,OAE3B;AACA+L,aAAa,CAAC,CAAC,CAEf;AACAxV,KAAK,CAAG,GAAI,CAAA7B,KAAK,CAACsX,KAAK,CAAC,CAAC,CAAE;AAE3B;AACA,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAvX,KAAK,CAACwX,iBAAiB,CACxC,EAAE,CACFhV,MAAM,CAACiV,UAAU,CAAGjV,MAAM,CAACkV,WAAW,CACtC,GAAG,CACH,IACF,CAAC,CACD;AACAH,MAAM,CAAC5O,QAAQ,CAAClD,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAAE;AAChC8R,MAAM,CAAChL,MAAM,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACtB5C,SAAS,CAAC2B,OAAO,CAAGiM,MAAM,CAE1B;AACA,KAAM,CAAAI,QAAQ,CAAG,GAAI,CAAA3X,KAAK,CAAC4X,aAAa,CAAC,CAAEC,SAAS,CAAE,IAAK,CAAC,CAAC,CAC7DF,QAAQ,CAACG,OAAO,CAACtV,MAAM,CAACiV,UAAU,CAAEjV,MAAM,CAACkV,WAAW,CAAC,CACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC,CAChCJ,QAAQ,CAACK,aAAa,CAACxV,MAAM,CAACyV,gBAAgB,CAAC,CAC/C/Q,YAAY,CAACoE,OAAO,CAAC4M,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC,CAErD;AACA;AACA,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAApY,KAAK,CAACqY,YAAY,CAAC,QAAQ,CAAE,GAAG,CAAC,CAAE;AAC5DxW,KAAK,CAACyO,GAAG,CAAC8H,YAAY,CAAC,CAEvB;AACA,KAAM,CAAAE,iBAAiB,CAAG,GAAI,CAAAtY,KAAK,CAACuY,gBAAgB,CAAC,QAAQ,CAAE,GAAG,CAAC,CAAE;AACrED,iBAAiB,CAAC3P,QAAQ,CAAClD,GAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAC1C5D,KAAK,CAACyO,GAAG,CAACgI,iBAAiB,CAAC,CAE5B;AACA,KAAM,CAAAE,iBAAiB,CAAG,GAAI,CAAAxY,KAAK,CAACuY,gBAAgB,CAAC,QAAQ,CAAE,GAAG,CAAC,CACnEC,iBAAiB,CAAC7P,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,CAAE,CAAC,CAAE,CAAC,EAAE,CAAC,CAC3C5D,KAAK,CAACyO,GAAG,CAACkI,iBAAiB,CAAC,CAE5B;AACA,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAzY,KAAK,CAAC0Y,SAAS,CAAC,QAAQ,CAAE,GAAG,CAAC,CACpDD,SAAS,CAAC9P,QAAQ,CAAClD,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAChCgT,SAAS,CAACE,KAAK,CAAGrS,IAAI,CAACC,EAAE,CAAG,CAAC,CAC7BkS,SAAS,CAACG,QAAQ,CAAG,GAAG,CACxBH,SAAS,CAACI,KAAK,CAAG,CAAC,CACnBJ,SAAS,CAAC5S,QAAQ,CAAG,GAAG,CACxBhE,KAAK,CAACyO,GAAG,CAACmI,SAAS,CAAC,CAEpB;AACAjX,QAAQ,CAAG,GAAI,CAAAtB,aAAa,CAACqX,MAAM,CAAEI,QAAQ,CAACQ,UAAU,CAAC,CACzD3W,QAAQ,CAACsX,aAAa,CAAG,IAAI,CAC7BtX,QAAQ,CAACuX,aAAa,CAAG,IAAI,CAC7BvX,QAAQ,CAACwX,kBAAkB,CAAG,KAAK,CACnCxX,QAAQ,CAACiL,WAAW,CAAG,EAAE,CACzBjL,QAAQ,CAACkL,WAAW,CAAG,GAAG,CAC1BlL,QAAQ,CAACmL,aAAa,CAAGrG,IAAI,CAACC,EAAE,CAAG,GAAG,CACtC/E,QAAQ,CAACoL,aAAa,CAAG,CAAC,CAC1BpL,QAAQ,CAAC6K,MAAM,CAAC5G,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5BjE,QAAQ,CAACgL,MAAM,CAAC,CAAC,CAEjB;AACApJ,OAAO,CAACC,GAAG,CAAC,OAAO,CAAE,CACnBkU,MAAM,CAAE,CAAC,CAACA,MAAM,CAChB/V,QAAQ,CAAE,CAAC,CAACA,QAAQ,CACpBmI,SAAS,CAAE,CAAC,CAACA,SAAS,CAAC2B,OACzB,CAAC,CAAC,CAEF;AACA,KAAM,CAAA2N,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,MAAO,IAAI,CAAAC,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACtC,KAAM,CAAAC,aAAa,CAAG,GAAI,CAAApZ,UAAU,CAAC,CAAC,CACtCoZ,aAAa,CAACC,IAAI,CAChB,GAAGtW,QAAQ,uBAAuB,CACjCuW,IAAI,EAAK,CACR,KAAM,CAAAC,YAAY,CAAGD,IAAI,CAAC1X,KAAK,CAE/B;AACA,KAAM,CAAA4X,gBAAgB,CAAG,GAAI,CAAAzZ,KAAK,CAAC0Z,KAAK,CAAC,CAAC,CAE1C;AACAF,YAAY,CAAC5H,QAAQ,CAAEC,KAAK,EAAK,CAC/B,GAAIA,KAAK,CAACC,MAAM,CAAE,CAChB;AACA,GAAID,KAAK,CAACE,QAAQ,CAAE,CAClB;AACA,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAhS,KAAK,CAAC2Z,oBAAoB,CAAC,CACjD/O,KAAK,CAAE,QAAQ,CAAO;AACtBgP,SAAS,CAAE,GAAG,CAAQ;AACtBC,SAAS,CAAE,GAAG,CAAQ;AACtBC,eAAe,CAAE,GAAK;AACxB,CAAC,CAAC,CAEF;AACA,GAAIjI,KAAK,CAACE,QAAQ,CAACpB,GAAG,CAAE,CACtBqB,WAAW,CAACrB,GAAG,CAAGkB,KAAK,CAACE,QAAQ,CAACpB,GAAG,CACtC,CAEA;AACAkB,KAAK,CAACE,QAAQ,CAAGC,WAAW,CAE5B5O,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEwO,KAAK,CAACtE,IAAI,CAAC,CACrC,CACF,CACF,CAAC,CAAC,CAEF;AACA,MAAMiM,YAAY,CAACO,QAAQ,CAACC,MAAM,CAAG,CAAC,CAAE,CACtC,KAAM,CAAAnI,KAAK,CAAG2H,YAAY,CAACO,QAAQ,CAAC,CAAC,CAAC,CACtCN,gBAAgB,CAACnJ,GAAG,CAACuB,KAAK,CAAC,CAC7B,CAEA;AACAhQ,KAAK,CAACyO,GAAG,CAACmJ,gBAAgB,CAAC,CAE3B;AACAzY,gBAAgB,CAAGyY,gBAAgB,CAEnCrW,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAC9B4W,kBAAkB,CAAC,IAAI,CAAC,CACxBd,OAAO,CAACM,gBAAgB,CAAC,CAC3B,CAAC,CACAS,GAAG,EAAK,CACP9W,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC6W,GAAG,CAACC,MAAM,CAAGD,GAAG,CAACE,KAAK,CAAG,GAAG,EAAEpU,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CACxE,CAAC,CACDoT,MACF,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAiB,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACF;AACA;AAEA;AACAzD,cAAc,CAAC,CAAC,CAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEF,CAAE,MAAOlS,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CAED;AACA,KAAM,CAAA4V,kBAAkB,CAAG,QAAAA,CAACC,GAAG,CAAqB,IAAnB,CAAAC,UAAU,CAAAC,SAAA,CAAAT,MAAA,IAAAS,SAAA,MAAArH,SAAA,CAAAqH,SAAA,IAAG,CAAC,CAC7C,MAAO,IAAI,CAAAvB,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACtC,KAAM,CAAAsB,WAAW,CAAIC,WAAW,EAAK,CACnCvX,OAAO,CAACC,GAAG,CAAC,WAAWkX,GAAG,aAAaI,WAAW,EAAE,CAAC,CAErD,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAA3a,UAAU,CAAC,CAAC,CAC/B2a,MAAM,CAACtB,IAAI,CACTiB,GAAG,CACFhB,IAAI,EAAK,CACRnW,OAAO,CAACC,GAAG,CAAC,WAAWkX,GAAG,EAAE,CAAC,CAC7BpB,OAAO,CAACI,IAAI,CAAC,CACf,CAAC,CACAW,GAAG,EAAK,CACP9W,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC6W,GAAG,CAACC,MAAM,CAAGD,GAAG,CAACE,KAAK,CAAG,GAAG,EAAEpU,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CACpE,CAAC,CACAtB,KAAK,EAAK,CACTtB,OAAO,CAACsB,KAAK,CAAC,SAAS6V,GAAG,EAAE,CAAE7V,KAAK,CAAC,CACpC,GAAIiW,WAAW,CAAG,CAAC,CAAE,CACnBvX,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3B2R,UAAU,CAAC,IAAM0F,WAAW,CAACC,WAAW,CAAG,CAAC,CAAC,CAAE,IAAI,CAAC,CACtD,CAAC,IAAM,CACLvB,MAAM,CAAC1U,KAAK,CAAC,CACf,CACF,CACF,CAAC,CACH,CAAC,CAEDgW,WAAW,CAACF,UAAU,CAAC,CACzB,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAI,MAAM,CAAG,GAAI,CAAA3a,UAAU,CAAC,CAAC,CAC/B2a,MAAM,CAACtB,IAAI,CACT,GAAGtW,QAAQ,4BAA4B,CACvC,KAAO,CAAAuW,IAAI,EAAK,CACd,GAAI,CACF,KAAM,CAAA5J,KAAK,CAAG4J,IAAI,CAAC1X,KAAK,CACxB8N,KAAK,CAACI,KAAK,CAACtK,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACxBkK,KAAK,CAAChH,QAAQ,CAAClD,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAE3B;AACA,GAAI5D,KAAK,CAAE,CACXA,KAAK,CAACyO,GAAG,CAACX,KAAK,CAAC,CAEhB;AACA,KAAM,CAAA0K,eAAe,CAAC,CAAC,CACvB,CAAC,IAAM,CACLjX,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC,CAChC,CACF,CAAE,MAAOA,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CACAwV,GAAG,EAAK,CACP9W,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC6W,GAAG,CAACC,MAAM,CAAGD,GAAG,CAACE,KAAK,CAAG,GAAG,EAAEpU,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CACpE,CAAC,CACAtB,KAAK,EAAK,CACTtB,OAAO,CAACsB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BtB,OAAO,CAACsB,KAAK,CAAC,OAAO,CAAE,CACrBmW,IAAI,CAAEnW,KAAK,CAACwK,IAAI,CAChB4L,IAAI,CAAEpW,KAAK,CAACwJ,OAAO,CACnB6M,KAAK,CAAE,GAAG/X,QAAQ,4BAA4B,CAC9CgY,KAAK,CAAE,GAAGhY,QAAQ,4BACpB,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAiY,OAAO,CAAGA,CAAA,GAAM,CACpBzT,iBAAiB,CAAC8D,OAAO,CAAG4P,qBAAqB,CAACD,OAAO,CAAC,CAE1D;AACA7a,KAAK,CAACoM,MAAM,CAAC,CAAC,CAEd;AACA,KAAM,CAAA2O,WAAW,CAAGrT,IAAI,CAACC,GAAG,CAAC,CAAC,CAC9B;AACA,KAAM,CAAAqT,SAAS,CAAEzX,KAAK,CAAC0X,QAAQ,CAAC,CAAC,CACjCxT,oBAAoB,CAACyD,OAAO,CAAG6P,WAAW,CAE1CnT,qBAAqB,CAACsD,OAAO,CAACwD,OAAO,CAAEkB,KAAK,EAAK,CAC/CA,KAAK,CAACxD,MAAM,CAAC4O,SAAS,CAAC,CACzB,CAAC,CAAC,CAEF,GAAI7Z,UAAU,GAAK,QAAQ,EAAIP,gBAAgB,CAAE,CAC/C;AACAQ,QAAQ,CAAC+J,OAAO,CAAG,KAAK,CAExB;AACA,KAAM,CAAA+P,UAAU,CAAGta,gBAAgB,CAAC2H,QAAQ,CAACzD,KAAK,CAAC,CAAC,CAEpD;AACA,KAAM,CAAAqW,eAAe,CAAGva,gBAAgB,CAAC8O,QAAQ,CAACxK,CAAC,CAEnD;AACA;AACA,KAAM,CAAAkW,gBAAgB,CAAG,EAAED,eAAe,CAAGjV,IAAI,CAACC,EAAE,CAAC,CAAGD,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC,CAEnE;AACA,KAAM,CAAAkV,YAAY,CAAG,GAAI,CAAAzb,KAAK,CAACkG,OAAO,CACpC,CAAC,EAAE,CAAGI,IAAI,CAACoV,GAAG,CAACF,gBAAgB,CAAC,CAChC,GAAG,CACH,CAAC,EAAE,CAAGlV,IAAI,CAACqV,GAAG,CAACH,gBAAgB,CACjC,CAAC,CAED;AACA,KAAM,CAAAI,oBAAoB,CAAGN,UAAU,CAACpW,KAAK,CAAC,CAAC,CAACoL,GAAG,CAACmL,YAAY,CAAC,CACjE,KAAM,CAAAI,YAAY,CAAGP,UAAU,CAACpW,KAAK,CAAC,CAAC,CAEvC;AACA,GAAI,CAACwC,kBAAkB,CAAC4D,OAAO,CAAE,CAC/B5D,kBAAkB,CAAC4D,OAAO,CAAGsQ,oBAAoB,CAAC1W,KAAK,CAAC,CAAC,CAC3D,CAEA,GAAI,CAACyC,gBAAgB,CAAC2D,OAAO,CAAE,CAC7B3D,gBAAgB,CAAC2D,OAAO,CAAGuQ,YAAY,CAAC3W,KAAK,CAAC,CAAC,CACjD,CAEA;AACAwC,kBAAkB,CAAC4D,OAAO,CAACwQ,IAAI,CAACF,oBAAoB,CAAE,CAAC,CAAGhU,eAAe,CAAC,CAC1ED,gBAAgB,CAAC2D,OAAO,CAACwQ,IAAI,CAACD,YAAY,CAAE,CAAC,CAAGjU,eAAe,CAAC,CAEhE;AACA2P,MAAM,CAAC5O,QAAQ,CAACwD,IAAI,CAACzE,kBAAkB,CAAC4D,OAAO,CAAC,CAEhD;AACAiM,MAAM,CAAC5L,EAAE,CAAClG,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAEtB;AACA8R,MAAM,CAAChL,MAAM,CAAC5E,gBAAgB,CAAC2D,OAAO,CAAC,CAEvC;AACAiM,MAAM,CAACwE,sBAAsB,CAAC,CAAC,CAC/BxE,MAAM,CAAC1K,YAAY,CAAC,CAAC,CACrB0K,MAAM,CAACzK,iBAAiB,CAAC,IAAI,CAAC,CAE9B;AACAtL,QAAQ,CAAC+J,OAAO,CAAG,KAAK,CAExB;AACA/J,QAAQ,CAAC6K,MAAM,CAACF,IAAI,CAACxE,gBAAgB,CAAC2D,OAAO,CAAC,CAC9C9J,QAAQ,CAACgL,MAAM,CAAC,CAAC,CAEjBpJ,OAAO,CAACC,GAAG,CAAC,OAAO,CAAE,CACnB2Y,IAAI,CAAEV,UAAU,CAACvN,OAAO,CAAC,CAAC,CAC1BD,IAAI,CAAEyJ,MAAM,CAAC5O,QAAQ,CAACoF,OAAO,CAAC,CAAC,CAC/BkO,IAAI,CAAEtU,gBAAgB,CAAC2D,OAAO,CAACyC,OAAO,CAAC,CAAC,CACxCmO,IAAI,CAAE3E,MAAM,CAAC4E,iBAAiB,CAAC,GAAI,CAAAnc,KAAK,CAACkG,OAAO,CAAC,CAAC,CAAC,CAAC6H,OAAO,CAAC,CAC9D,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIxM,UAAU,GAAK,QAAQ,CAAE,CAClC;AACAmG,kBAAkB,CAAC4D,OAAO,CAAG,IAAI,CACjC3D,gBAAgB,CAAC2D,OAAO,CAAG,IAAI,CAE/B;AACA9J,QAAQ,CAAC+J,OAAO,CAAG,IAAI,CAEvB;AACAgM,MAAM,CAAC5L,EAAE,CAAClG,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAEtB;AACA,GAAIa,IAAI,CAACK,GAAG,CAAC4Q,MAAM,CAAC5O,QAAQ,CAACrD,CAAC,CAAC,CAAG,EAAE,CAAE,CACpCiS,MAAM,CAAC5O,QAAQ,CAAClD,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAC9BjE,QAAQ,CAAC6K,MAAM,CAAC5G,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5B8R,MAAM,CAAChL,MAAM,CAAC/K,QAAQ,CAAC6K,MAAM,CAAC,CAC9B7K,QAAQ,CAACgL,MAAM,CAAC,CAAC,CACnB,CAEA;AACA;AACA+K,MAAM,CAAC1K,YAAY,CAAC,CAAC,CACrB0K,MAAM,CAACzK,iBAAiB,CAAC,IAAI,CAAC,CAEhC,CAAC,IAAM,IAAIvL,UAAU,GAAK,cAAc,CAAE,CACxC;AACAmG,kBAAkB,CAAC4D,OAAO,CAAG,IAAI,CACjC3D,gBAAgB,CAAC2D,OAAO,CAAG,IAAI,CAE/B;AACA9J,QAAQ,CAACgL,MAAM,CAAC,CAAC,CACnB,CAEA,GAAIhL,QAAQ,CAAEA,QAAQ,CAACgL,MAAM,CAAC,CAAC,CAC/B,GAAI3K,KAAK,EAAI0V,MAAM,CAAE,CACnBI,QAAQ,CAACyE,MAAM,CAACva,KAAK,CAAE0V,MAAM,CAAC,CAChC,CACF,CAAC,CAED0D,OAAO,CAAC,CAAC,CAET;AACA,KAAM,CAAAoB,YAAY,CAAGA,CAAA,GAAM,CACzB9E,MAAM,CAAC+E,MAAM,CAAG9Z,MAAM,CAACiV,UAAU,CAAGjV,MAAM,CAACkV,WAAW,CACtDH,MAAM,CAACwE,sBAAsB,CAAC,CAAC,CAC/BpE,QAAQ,CAACG,OAAO,CAACtV,MAAM,CAACiV,UAAU,CAAEjV,MAAM,CAACkV,WAAW,CAAC,CACzD,CAAC,CACDlV,MAAM,CAAC+Z,gBAAgB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CAE/C;AACA7Z,MAAM,CAACga,aAAa,CAAG,IAAM,CAC3B,GAAI7S,SAAS,CAAC2B,OAAO,CAAE,CACrB3B,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAClD,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CACzCkE,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACjC5C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC,CAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC,CAEzC,GAAItL,QAAQ,CAAE,CACZA,QAAQ,CAAC6K,MAAM,CAAC5G,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5BjE,QAAQ,CAAC+J,OAAO,CAAG,IAAI,CACvB/J,QAAQ,CAACgL,MAAM,CAAC,CAAC,CACnB,CAEAjL,UAAU,CAAG,QAAQ,CACrB6B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CAAC,CAED;AACA,MAAO,IAAM,CACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CAExB;AACA,GAAImE,iBAAiB,CAAC8D,OAAO,CAAE,CAC7BmR,oBAAoB,CAACjV,iBAAiB,CAAC8D,OAAO,CAAC,CAC/C9D,iBAAiB,CAAC8D,OAAO,CAAG,IAAI,CAClC,CAEA;AACA,GAAInK,oBAAoB,CAAE,CACxBub,aAAa,CAACvb,oBAAoB,CAAC,CACnCA,oBAAoB,CAAG,IAAI,CAC7B,CAEA;AACA,GAAIoG,aAAa,CAAC+D,OAAO,CAAE,CACzB/D,aAAa,CAAC+D,OAAO,CAACqR,KAAK,CAAC,CAAC,CAC7BpV,aAAa,CAAC+D,OAAO,CAAG,IAAI,CAC9B,CAEA;AACA9I,MAAM,CAACoa,mBAAmB,CAAC,QAAQ,CAAEP,YAAY,CAAC,CAElD;AACA,GAAI1E,QAAQ,EAAIzQ,YAAY,CAACoE,OAAO,CAAE,CACpCpE,YAAY,CAACoE,OAAO,CAACuR,WAAW,CAAClF,QAAQ,CAACQ,UAAU,CAAC,CACrDR,QAAQ,CAACzE,OAAO,CAAC,CAAC,CACpB,CAEA;AACA,GAAI5P,aAAa,CAAE,CACjBA,aAAa,CAACwL,OAAO,CAAC,CAAC+B,SAAS,CAAE7B,EAAE,GAAK,CACvC,GAAI6B,SAAS,CAAClB,KAAK,EAAI9N,KAAK,CAAE,CAC5BA,KAAK,CAACiP,MAAM,CAACD,SAAS,CAAClB,KAAK,CAAC,CAC/B,CACF,CAAC,CAAC,CACFrM,aAAa,CAACwZ,KAAK,CAAC,CAAC,CACvB,CAEA;AACArZ,gBAAgB,CAACqL,OAAO,CAAEiO,QAAQ,EAAK,CACrC,GAAIlb,KAAK,EAAIkb,QAAQ,CAACpN,KAAK,CAAE,CAC3B9N,KAAK,CAACiP,MAAM,CAACiM,QAAQ,CAACpN,KAAK,CAAC,CAC9B,CACF,CAAC,CAAC,CACFlM,gBAAgB,CAACqZ,KAAK,CAAC,CAAC,CACxBpZ,kBAAkB,CAACoZ,KAAK,CAAC,CAAC,CAE1B;AAEA;AACAjb,KAAK,CAAG,IAAI,CACZL,QAAQ,CAAG,IAAI,CACfC,qBAAqB,CAAG,IAAI,CAC5BC,qBAAqB,CAAG,IAAI,CAC5BC,oBAAoB,CAAG,IAAI,CAC3BC,0BAA0B,CAAG,IAAI,CACjCZ,gBAAgB,CAAG,IAAI,CAEvBoC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACvB,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACAzD,SAAS,CAAC,IAAM,CACd;AACAiE,qBAAqB,CAAC,CAAC,CAEvB;AACA,KAAM,CAAAmZ,uBAAuB,CAAGA,CAAA,GAAM,CACpC5Z,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjCQ,qBAAqB,CAAC,CAAC,CACzB,CAAC,CAED;AACArB,MAAM,CAAC+Z,gBAAgB,CAAC,oBAAoB,CAAES,uBAAuB,CAAC,CAEtE;AACA,KAAM,CAAAC,UAAU,CAAGC,WAAW,CAAC,IAAM,CACnCrZ,qBAAqB,CAAC,CAAC,CACzB,CAAC,CAAE,KAAK,CAAC,CAET;AACA,MAAO,IAAM,CACXrB,MAAM,CAACoa,mBAAmB,CAAC,oBAAoB,CAAEI,uBAAuB,CAAC,CACzEN,aAAa,CAACO,UAAU,CAAC,CAC3B,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACArd,SAAS,CAAC,IAAM,CACd;AACA,GAAIiC,KAAK,EAAIuF,SAAS,CAACkE,OAAO,CAAE,CAC9BlI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB;AACA,KAAM,CAAA8Z,KAAK,CAAGnI,UAAU,CAAC,IAAM,CAC7B,GAAInT,KAAK,EAAIuF,SAAS,CAACkE,OAAO,CAAE,CAAG;AACjC8R,mBAAmB,CAAChW,SAAS,CAACkE,OAAO,CAAC,CACxC,CACF,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAM+R,YAAY,CAACF,KAAK,CAAC,CAClC,CAAC,IAAM,CACL/Z,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CACrC,CACF,CAAC,CAAE,CAACxB,KAAK,CAAC,CAAC,CAEX;AACAjC,SAAS,CAAC,IAAM,CACd,GAAIsH,YAAY,CAACoE,OAAO,CAAE,CACxB;AACA,KAAM,CAAAgS,WAAW,CAAIhI,KAAK,EAAK,CAC7B,GAAIzT,KAAK,EAAI8H,SAAS,CAAC2B,OAAO,CAAE,CAC9BiS,gBAAgB,CAACjI,KAAK,CAAEpO,YAAY,CAACoE,OAAO,CAAEzJ,KAAK,CAAE8H,SAAS,CAAC2B,OAAO,CAAC,CACzE,CACF,CAAC,CAED;AACApE,YAAY,CAACoE,OAAO,CAACiR,gBAAgB,CAAC,OAAO,CAAEe,WAAW,CAAC,CAE3D;AACAla,OAAO,CAACC,GAAG,CAAC,eAAe,CAAE,CAAC,CAAC6D,YAAY,CAACoE,OAAO,CAAC,CAEpD;AACA,MAAO,IAAM,CACX,GAAIpE,YAAY,CAACoE,OAAO,CAAE,CACxBpE,YAAY,CAACoE,OAAO,CAACsR,mBAAmB,CAAC,OAAO,CAAEU,WAAW,CAAC,CAC9Dla,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC,CAC3B,CACF,CAAC,CACH,CACF,CAAC,CAAE,CAACxB,KAAK,CAAE8H,SAAS,CAAC2B,OAAO,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAkS,SAAS,CAAGzd,WAAW,CAAC,IAAM,CAClCqD,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAC7B;AACF,CAAC,CAAE,CAAC6D,YAAY,CAAE8D,aAAa,CAAEvH,gBAAgB,CAAC,CAAC,CAEnD;AACA,KAAM,CAAAga,wBAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAAjK,QAAQ,CAAG,GAAI,CAAAxT,KAAK,CAAC0d,WAAW,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAChD,KAAM,CAAA3L,QAAQ,CAAG,GAAI,CAAA/R,KAAK,CAAC2d,iBAAiB,CAAC,CAAE/S,KAAK,CAAE,QAAS,CAAC,CAAC,CACjE,KAAM,CAAA6J,iBAAiB,CAAG,GAAI,CAAAzU,KAAK,CAAC4d,IAAI,CAACpK,QAAQ,CAAEzB,QAAQ,CAAC,CAE5D;AACA,KAAM,CAAA8L,YAAY,CAAG,GAAI,CAAA7d,KAAK,CAAC8d,gBAAgB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,EAAE,CAAC,CAC5D,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAA/d,KAAK,CAAC2d,iBAAiB,CAAC,CAAE/S,KAAK,CAAE,QAAS,CAAC,CAAC,CACrE,KAAM,CAAAoT,SAAS,CAAG,GAAI,CAAAhe,KAAK,CAAC4d,IAAI,CAACC,YAAY,CAAEE,YAAY,CAAC,CAC5DC,SAAS,CAACrV,QAAQ,CAAClD,GAAG,CAAC,CAAC,CAAE,CAAC,GAAG,CAAE,CAAC,CAAC,CAClCgP,iBAAiB,CAACnE,GAAG,CAAC0N,SAAS,CAAC,CAEhC,MAAO,CAAAvJ,iBAAiB,CAC1B,CAAC,CAED;AACA,KAAM,CAAAwJ,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAI,CAACpc,KAAK,CAAE,OAEZ;AACA4B,gBAAgB,CAACqL,OAAO,CAAC,CAACiO,QAAQ,CAAE9S,OAAO,GAAK,CAC9C,GAAI8S,QAAQ,CAACpN,KAAK,CAAE,CAClB;AACA,KAAM,CAAAuO,cAAc,CAAG,GAAI,CAAAle,KAAK,CAACme,cAAc,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACxD,KAAM,CAAAC,cAAc,CAAG,GAAI,CAAApe,KAAK,CAAC2d,iBAAiB,CAAC,CACjD/S,KAAK,CAAE,QAAQ,CAAC;AAChBuH,WAAW,CAAE,KAAK,CAClBW,OAAO,CAAE,GAAG,CAAG;AACfuL,UAAU,CAAE,KACd,CAAC,CAAC,CAEF,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAte,KAAK,CAAC4d,IAAI,CAACM,cAAc,CAAEE,cAAc,CAAC,CACjEE,UAAU,CAAC3V,QAAQ,CAAClD,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAG;AAEnC;AACA6Y,UAAU,CAACC,QAAQ,CAAG,CACpBrP,IAAI,CAAE,cAAc,CACpBjF,OAAO,CAAEA,OAAO,CAChBsD,IAAI,CAAEwP,QAAQ,CAAC3P,YAAY,CAACG,IAAI,CAChCiR,aAAa,CAAE,IACjB,CAAC,CAED;AACAzB,QAAQ,CAACpN,KAAK,CAACW,GAAG,CAACgO,UAAU,CAAC,CAE9Blb,OAAO,CAACC,GAAG,CAAC,OAAO0Z,QAAQ,CAAC3P,YAAY,CAACG,IAAI,KAAKtD,OAAO,aAAa,CAAC,CACzE,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACArK,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAud,KAAK,CAAGnI,UAAU,CAAC,IAAM,CAC7B,GAAIvR,gBAAgB,CAACgb,IAAI,CAAG,CAAC,CAAE,CAC7Brb,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1B;AACF,CACF,CAAC,CAAE,IAAI,CAAC,CAAG;AAEX,MAAO,IAAMga,YAAY,CAACF,KAAK,CAAC,CAClC,CAAC,CAAE,EAAE,CAAC,CAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACAvd,SAAS,CAAC,IAAM,CACd,MAAO,IAAM,CACX;AACA,GAAIyK,0BAA0B,CAACiB,OAAO,CAAE,CACtCoR,aAAa,CAACrS,0BAA0B,CAACiB,OAAO,CAAC,CACjDjB,0BAA0B,CAACiB,OAAO,CAAG,IAAI,CACzClI,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC9B,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAAE;AAER;AACAzD,SAAS,CAAC,IAAM,CACd,GAAI,CAACkK,mBAAmB,CAACE,OAAO,EAAIK,0BAA0B,CAACiB,OAAO,CAAE,CACtEoR,aAAa,CAACrS,0BAA0B,CAACiB,OAAO,CAAC,CACjDjB,0BAA0B,CAACiB,OAAO,CAAG,IAAI,CACzClB,mBAAmB,CAACkB,OAAO,CAAG,IAAI,CAClClI,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACnC,CACF,CAAC,CAAE,CAACyG,mBAAmB,CAACE,OAAO,CAAC,CAAC,CAEjC;AACApK,SAAS,CAAC,IAAM,CACd;AACA,GAAIa,iBAAiB,EAAIA,iBAAiB,CAAC4M,aAAa,EAAI5M,iBAAiB,CAAC4M,aAAa,CAAC2M,MAAM,CAAG,CAAC,CAAE,CACtG;AACA,GAAI,CAACpQ,oBAAoB,CAAE,CACzB,KAAM,CAAA8U,iBAAiB,CAAGje,iBAAiB,CAAC4M,aAAa,CAAC,CAAC,CAAC,CAC5DjK,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEqb,iBAAiB,CAACnR,IAAI,CAAC,CAEjD;AACA,KAAM,CAAA4P,KAAK,CAAGnI,UAAU,CAAC,IAAM,CAC7B9H,wBAAwB,CAACwR,iBAAiB,CAACnR,IAAI,CAAC,CAClD,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAM8P,YAAY,CAACF,KAAK,CAAC,CAClC,CACF,CACF,CAAC,CAAE,CAAC1c,iBAAiB,CAAEmJ,oBAAoB,CAAC,CAAC,CAE7C,mBACE/I,KAAA,CAAAE,SAAA,EAAAgZ,QAAA,eACEpZ,IAAA,SAAMge,KAAK,CAAEjU,UAAW,CAAAqP,QAAA,CAAC,gCAAK,CAAM,CAAC,cACrCpZ,IAAA,CAACJ,MAAM,EACLoe,KAAK,CAAEpU,uBAAwB,CAC/BqU,WAAW,CAAC,4CAAS,CACrBC,QAAQ,CAAE3R,wBAAyB,CACnC4R,OAAO,CAAEre,iBAAiB,CAAC4M,aAAa,CAACsD,GAAG,CAACvD,YAAY,GAAK,CAC5DD,KAAK,CAAEC,YAAY,CAACG,IAAI,CACxBwR,KAAK,CAAE3R,YAAY,CAACG,IACtB,CAAC,CAAC,CAAE,CACJkR,IAAI,CAAC,OAAO,CACZO,QAAQ,CAAE,IAAK,CACfC,aAAa,CAAE,CACblW,MAAM,CAAE,IAAI,CACZmW,SAAS,CAAE,OACb,CAAE,CACF/R,KAAK,CAAEvD,oBAAoB,CAAGA,oBAAoB,CAAC2D,IAAI,CAAG6F,SAAU,CACrE,CAAC,cACFzS,IAAA,QAAKwe,GAAG,CAAEjY,YAAa,CAACyX,KAAK,CAAE,CAAElU,KAAK,CAAE,MAAM,CAAEoF,MAAM,CAAE,MAAO,CAAE,CAAE,CAAC,CAGnE/F,mBAAmB,CAACE,OAAO,eAC1BnJ,KAAA,QACE8d,KAAK,CAAE,CACLhW,QAAQ,CAAE,UAAU,CACpBE,IAAI,CAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAACvD,CAAC,IAAI,CAC3CoF,GAAG,CAAE,GAAGV,mBAAmB,CAACnB,QAAQ,CAACrD,CAAC,IAAI,CAC1CwD,SAAS,CAAE,wBAAwB,CACnCC,MAAM,CAAE,IAAI,CACZK,eAAe,CAAE,qBAAqB,CACtCwB,KAAK,CAAE,OAAO,CACdtB,YAAY,CAAE,KAAK,CACnBG,SAAS,CAAE,8BAA8B,CACzCN,OAAO,CAAE,GAAG,CACZiW,QAAQ,CAAE,OAAO,CAAE;AACnB5V,QAAQ,CAAE,MAAO;AACnB,CAAE,CAAAuQ,QAAA,EAEDjQ,mBAAmB,CAACI,OAAO,cAC5BvJ,IAAA,WACEge,KAAK,CAAE,CACLhW,QAAQ,CAAE,UAAU,CACpB6B,GAAG,CAAE,KAAK,CACV6U,KAAK,CAAE,KAAK,CACZC,UAAU,CAAE,MAAM,CAClBjW,MAAM,CAAE,MAAM,CACduB,KAAK,CAAE,OAAO,CACdpB,QAAQ,CAAE,MAAM,CAChBD,MAAM,CAAE,SAAS,CACjBJ,OAAO,CAAE,SACX,CAAE,CACFoW,OAAO,CAAEA,CAAA,GAAMC,kBAAkB,CAACzV,sBAAsB,CAAE,CAAAgQ,QAAA,CAC3D,MAED,CAAQ,CAAC,EACN,CACN,cAEDlZ,KAAA,QAAK8d,KAAK,CAAEjW,oBAAqB,CAAAqR,QAAA,eAC/BpZ,IAAA,WACEge,KAAK,CAAE,CACL,GAAGzV,WAAW,CACdE,eAAe,CAAEZ,QAAQ,GAAK,QAAQ,CAAG,SAAS,CAAG,0BAA0B,CAC/EoC,KAAK,CAAEpC,QAAQ,GAAK,QAAQ,CAAG,OAAO,CAAG,OAC3C,CAAE,CACF+W,OAAO,CAAElU,kBAAmB,CAAA0O,QAAA,CAC7B,0BAED,CAAQ,CAAC,cACTpZ,IAAA,WACEge,KAAK,CAAE,CACL,GAAGzV,WAAW,CACdE,eAAe,CAAEZ,QAAQ,GAAK,QAAQ,CAAG,SAAS,CAAG,0BAA0B,CAC/EoC,KAAK,CAAEpC,QAAQ,GAAK,QAAQ,CAAG,OAAO,CAAG,OAC3C,CAAE,CACF+W,OAAO,CAAE/T,kBAAmB,CAAAuO,QAAA,CAC7B,0BAED,CAAQ,CAAC,EACN,CAAC,EACN,CAAC,CAEP,CAAC,CAED;AACA,QAAS,CAAAzH,gBAAgBA,CAACmN,IAAI,CAAmB,IAAjB,CAAAC,UAAU,CAAAjF,SAAA,CAAAT,MAAA,IAAAS,SAAA,MAAArH,SAAA,CAAAqH,SAAA,IAAG,CAAC,CAAC,CAC7C,KAAM,CAAAkF,MAAM,CAAG,CACbC,QAAQ,CAAEF,UAAU,CAACE,QAAQ,EAAI,OAAO,CACxCpW,QAAQ,CAAEkW,UAAU,CAAClW,QAAQ,EAAI,EAAE,CAAE;AACrCqB,UAAU,CAAE6U,UAAU,CAAC7U,UAAU,EAAI,MAAM,CAC3CgV,eAAe,CAAEH,UAAU,CAACG,eAAe,EAAI,CAAC,CAChDC,WAAW,CAAEJ,UAAU,CAACI,WAAW,EAAI,CAAEtN,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,GAAI,CAAC,CACnEvJ,eAAe,CAAEsW,UAAU,CAACtW,eAAe,EAAI,CAAEoJ,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAI,CAAC,CACjFC,SAAS,CAAE8M,UAAU,CAAC9M,SAAS,EAAI,CAAEJ,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,GAAI,CAAC,CAC/DxJ,OAAO,CAAEuW,UAAU,CAACvW,OAAO,EAAI,CACjC,CAAC,CAED;AACA,KAAM,CAAA4W,MAAM,CAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC,CAC/C,KAAM,CAAAC,OAAO,CAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC,CAEvC;AACAD,OAAO,CAACE,IAAI,CAAG,GAAGT,MAAM,CAAC9U,UAAU,IAAI8U,MAAM,CAACnW,QAAQ,MAAMmW,MAAM,CAACC,QAAQ,EAAE,CAE7E;AACA,KAAM,CAAAS,SAAS,CAAGH,OAAO,CAACI,WAAW,CAACb,IAAI,CAAC,CAAChV,KAAK,CAEjD;AACA,KAAM,CAAAA,KAAK,CAAG4V,SAAS,CAAG,CAAC,CAAGV,MAAM,CAACxW,OAAO,CAAG,CAAC,CAAGwW,MAAM,CAACE,eAAe,CACzE,KAAM,CAAAhQ,MAAM,CAAG8P,MAAM,CAACnW,QAAQ,CAAG,CAAC,CAAGmW,MAAM,CAACxW,OAAO,CAAG,CAAC,CAAGwW,MAAM,CAACE,eAAe,CAEhFE,MAAM,CAACtV,KAAK,CAAGA,KAAK,CACpBsV,MAAM,CAAClQ,MAAM,CAAGA,MAAM,CAEtB;AACAqQ,OAAO,CAACE,IAAI,CAAG,GAAGT,MAAM,CAAC9U,UAAU,IAAI8U,MAAM,CAACnW,QAAQ,MAAMmW,MAAM,CAACC,QAAQ,EAAE,CAC7EM,OAAO,CAACK,YAAY,CAAG,QAAQ,CAE/B;AACA,KAAM,CAAAC,MAAM,CAAG,CAAC,CAChBN,OAAO,CAACO,SAAS,CAAC,CAAC,CACnBP,OAAO,CAACQ,MAAM,CAACf,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEb,MAAM,CAACE,eAAe,CAAC,CACvEK,OAAO,CAACS,MAAM,CAAClW,KAAK,CAAGkV,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEb,MAAM,CAACE,eAAe,CAAC,CAC/EK,OAAO,CAACU,KAAK,CAACnW,KAAK,CAAGkV,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAEpV,KAAK,CAAGkV,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEA,MAAM,CAAC,CAC9IN,OAAO,CAACS,MAAM,CAAClW,KAAK,CAAGkV,MAAM,CAACE,eAAe,CAAEhQ,MAAM,CAAG8P,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAC,CACxFN,OAAO,CAACU,KAAK,CAACnW,KAAK,CAAGkV,MAAM,CAACE,eAAe,CAAEhQ,MAAM,CAAG8P,MAAM,CAACE,eAAe,CAAEpV,KAAK,CAAGkV,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAE3Q,MAAM,CAAG8P,MAAM,CAACE,eAAe,CAAEW,MAAM,CAAC,CAChKN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAE3Q,MAAM,CAAG8P,MAAM,CAACE,eAAe,CAAC,CAChFK,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,CAAEhQ,MAAM,CAAG8P,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAEhQ,MAAM,CAAG8P,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEA,MAAM,CAAC,CAChJN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAC,CACvEN,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEb,MAAM,CAACE,eAAe,CAAEW,MAAM,CAAC,CAC9HN,OAAO,CAACW,SAAS,CAAC,CAAC,CAEnB;AACAX,OAAO,CAACY,WAAW,CAAG,QAAQnB,MAAM,CAACG,WAAW,CAACtN,CAAC,KAAKmN,MAAM,CAACG,WAAW,CAACrN,CAAC,KAAKkN,MAAM,CAACG,WAAW,CAACpN,CAAC,KAAKiN,MAAM,CAACG,WAAW,CAACnN,CAAC,GAAG,CAChIuN,OAAO,CAACa,SAAS,CAAGpB,MAAM,CAACE,eAAe,CAC1CK,OAAO,CAACc,MAAM,CAAC,CAAC,CAEhB;AACAd,OAAO,CAACe,SAAS,CAAG,QAAQtB,MAAM,CAACvW,eAAe,CAACoJ,CAAC,KAAKmN,MAAM,CAACvW,eAAe,CAACqJ,CAAC,KAAKkN,MAAM,CAACvW,eAAe,CAACsJ,CAAC,KAAKiN,MAAM,CAACvW,eAAe,CAACuJ,CAAC,GAAG,CAC9IuN,OAAO,CAACgB,IAAI,CAAC,CAAC,CAEd;AACAhB,OAAO,CAACe,SAAS,CAAG,QAAQtB,MAAM,CAAC/M,SAAS,CAACJ,CAAC,KAAKmN,MAAM,CAAC/M,SAAS,CAACH,CAAC,KAAKkN,MAAM,CAAC/M,SAAS,CAACF,CAAC,KAAKiN,MAAM,CAAC/M,SAAS,CAACD,CAAC,GAAG,CACtHuN,OAAO,CAACiB,SAAS,CAAG,QAAQ,CAE5B;AACAjB,OAAO,CAACkB,QAAQ,CAAC3B,IAAI,CAAEhV,KAAK,CAAG,CAAC,CAAEoF,MAAM,CAAG,CAAC,CAAC,CAE7C;AACA,KAAM,CAAAwR,OAAO,CAAG,GAAI,CAAArhB,KAAK,CAACshB,aAAa,CAACvB,MAAM,CAAC,CAC/CsB,OAAO,CAACE,SAAS,CAAGvhB,KAAK,CAACwhB,YAAY,CACtCH,OAAO,CAACjP,WAAW,CAAG,IAAI,CAE1B;AACA,KAAM,CAAAqP,cAAc,CAAG,GAAI,CAAAzhB,KAAK,CAAC0hB,cAAc,CAAC,CAC9C/Q,GAAG,CAAE0Q,OAAO,CACZlP,WAAW,CAAE,IACf,CAAC,CAAC,CAEF;AACA,KAAM,CAAAwP,MAAM,CAAG,GAAI,CAAA3hB,KAAK,CAAC4hB,MAAM,CAACH,cAAc,CAAC,CAC/CE,MAAM,CAAC5R,KAAK,CAACtK,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAAE;AAC7Bkc,MAAM,CAAC5P,QAAQ,CAAC8P,SAAS,CAAG,KAAK,CAAE;AAEnC;AACAF,MAAM,CAACpD,QAAQ,CAAG,CAChBkB,IAAI,CAAEA,IAAI,CACVE,MAAM,CAAEA,MACV,CAAC,CAED,MAAO,CAAAgC,MAAM,CACf,CAIA;AACAnf,MAAM,CAACsf,eAAe,CAAG,IAAM,CAC7B,GAAI,CACF;AACA,KAAM,CAAAvK,MAAM,CAAGyI,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc,CAC5E,GAAI1K,MAAM,CAAE,CACV;AACA,KAAM,CAAA2K,MAAM,CAAG3K,MAAM,CAAC5O,QAAQ,CAACzD,KAAK,CAAC,CAAC,CAEtC;AACAqS,MAAM,CAAC5O,QAAQ,CAAClD,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAC9B8R,MAAM,CAAC5L,EAAE,CAAClG,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACtB8R,MAAM,CAAChL,MAAM,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAEtB;AACAgL,MAAM,CAAC1K,YAAY,CAAC,CAAC,CACrB0K,MAAM,CAACzK,iBAAiB,CAAC,IAAI,CAAC,CAE9B;AACA,KAAM,CAAAtL,QAAQ,CAAGwe,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB,CAChF,GAAI3gB,QAAQ,CAAE,CACZA,QAAQ,CAAC6K,MAAM,CAAC5G,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5BjE,QAAQ,CAACgL,MAAM,CAAC,CAAC,CACnB,CAEApJ,OAAO,CAACC,GAAG,CAAC,YAAY,CAAE,CACxB+e,GAAG,CAAEF,MAAM,CAACnU,OAAO,CAAC,CAAC,CACrBsU,GAAG,CAAE9K,MAAM,CAAC5O,QAAQ,CAACoF,OAAO,CAAC,CAC/B,CAAC,CAAC,CAEF,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CAAE,MAAOuU,CAAC,CAAE,CACVlf,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAE4d,CAAC,CAAC,CAC9B,MAAO,MAAK,CACd,CACF,CAAC,CAGD;AACA,KAAM,CAAAjL,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACFjU,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3B,KAAM,CAAAuX,MAAM,CAAG,GAAI,CAAA3a,UAAU,CAAC,CAAC,CAE/B;AACA,GAAI,CACF,KAAM,CAAEsiB,gBAAgB,CAAEC,WAAW,CAAEC,WAAW,CAAEC,UAAU,CAAC,CAAG,KAAM,CAAAxJ,OAAO,CAACyJ,GAAG,CAAC,CAClF/H,MAAM,CAACgI,SAAS,CAAC,GAAG5f,QAAQ,4BAA4B,CAAC,CAC3D4X,MAAM,CAACgI,SAAS,CAAC,GAAG5f,QAAQ,uBAAuB,CAAC,CACpD4X,MAAM,CAACgI,SAAS,CAAC,GAAG5f,QAAQ,uBAAuB,CAAC,CAClD4X,MAAM,CAACgI,SAAS,CAAC,GAAG5f,QAAQ,sBAAsB,CAAC,CAEtD,CAAC,CAIF;AACAI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB5B,qBAAqB,CAAG+gB,WAAW,CAAC3gB,KAAK,CACzCJ,qBAAqB,CAACmQ,QAAQ,CAAEC,KAAK,EAAK,CACxC,GAAIA,KAAK,CAACC,MAAM,CAAE,CACd,KAAM,CAAAE,WAAW,CAAG,GAAI,CAAAhS,KAAK,CAAC2Z,oBAAoB,CAAC,CACnD/O,KAAK,CAAE,QAAQ,CAAG;AAClBgP,SAAS,CAAE,GAAG,CACdC,SAAS,CAAE,GAAG,CACdC,eAAe,CAAE,GACnB,CAAC,CAAC,CAEA;AACA,GAAIjI,KAAK,CAACE,QAAQ,CAACpB,GAAG,CAAE,CACtBqB,WAAW,CAACrB,GAAG,CAAGkB,KAAK,CAACE,QAAQ,CAACpB,GAAG,CACtC,CACAkB,KAAK,CAACgR,OAAO,CAAG7Q,WAAW,CAC/B,CACF,CAAC,CAAC,CAEF5O,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1B;AACA3B,qBAAqB,CAAG+gB,WAAW,CAAC5gB,KAAK,CACzC;AACAH,qBAAqB,CAACqO,KAAK,CAACtK,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACxC;AACA/D,qBAAqB,CAACkQ,QAAQ,CAAEC,KAAK,EAAK,CACxC,GAAIA,KAAK,CAACC,MAAM,EAAID,KAAK,CAACE,QAAQ,CAAE,CAClC;AACAF,KAAK,CAACE,QAAQ,CAAC6H,SAAS,CAAG,GAAG,CAC9B/H,KAAK,CAACE,QAAQ,CAAC8H,SAAS,CAAG,GAAG,CAC9BhI,KAAK,CAACE,QAAQ,CAAC+H,eAAe,CAAG,GAAG,CACtC,CACF,CAAC,CAAC,CAEF1W,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB;AACA1B,oBAAoB,CAAG+gB,UAAU,CAAC7gB,KAAK,CACvC;AACA;AACA;AACAF,oBAAoB,CAACiQ,QAAQ,CAAEC,KAAK,EAAK,CACvC,GAAIA,KAAK,CAACC,MAAM,EAAID,KAAK,CAACE,QAAQ,CAAE,CAClC;AACAF,KAAK,CAACE,QAAQ,CAAC6H,SAAS,CAAG,GAAG,CAC9B/H,KAAK,CAACE,QAAQ,CAAC8H,SAAS,CAAG,GAAG,CAC9BhI,KAAK,CAACE,QAAQ,CAAC+H,eAAe,CAAG,GAAG,CAEtC,CACA,GAAIjI,KAAK,CAACC,MAAM,CAAC,CACfD,KAAK,CAACiR,UAAU,CAAG,IAAI,CACzB,CACF,CAAC,CAAC,CAIF;AACA1f,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEqf,UAAU,CAACtS,UAAU,CAAC4J,MAAM,CAAE,GAAG,CAAC,CAC5D,GAAI0I,UAAU,CAACtS,UAAU,EAAIsS,UAAU,CAACtS,UAAU,CAAC4J,MAAM,CAAG,CAAC,CAAE,CAC7D5W,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEqf,UAAU,CAACtS,UAAU,CAAC4J,MAAM,CAAE,GAAG,CAAC,CACzDlY,eAAe,CAAG4gB,UAAU,CAC9B,CAAC,IAAM,CACLtf,OAAO,CAAC2f,IAAI,CAAC,cAAc,CAAC,CAC9B,CAEA3f,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC,CAEzB;AACAzB,0BAA0B,CAAG2gB,gBAAgB,CAAC1gB,KAAK,CACnDuB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEzB,0BAA0B,CAAC,CACjD;AACAA,0BAA0B,CAACmO,KAAK,CAACtK,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC7C;AACA7D,0BAA0B,CAACgQ,QAAQ,CAAEC,KAAK,EAAK,CAC7C,GAAIA,KAAK,CAACC,MAAM,EAAID,KAAK,CAACE,QAAQ,CAAE,CAClC;AACAF,KAAK,CAACE,QAAQ,CAAC6H,SAAS,CAAG,GAAG,CAC9B/H,KAAK,CAACE,QAAQ,CAAC8H,SAAS,CAAG,GAAG,CAC9BhI,KAAK,CAACE,QAAQ,CAAC+H,eAAe,CAAG,GAAG,CACxC,CACF,CAAC,CAAC,CAEA1W,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB,CAAE,MAAOqB,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CAExC;AACA,GAAI,CACF,GAAI,CAACjD,qBAAqB,CAAE,CAC1B,KAAM,CAAA+gB,WAAW,CAAG,KAAM,CAAA5H,MAAM,CAACgI,SAAS,CAAC,GAAG5f,QAAQ,uBAAuB,CAAC,CAC9EvB,qBAAqB,CAAG+gB,WAAW,CAAC3gB,KAAK,CAC3C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,CAAE,MAAOmhB,GAAG,CAAE,CACZ5f,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAEse,GAAG,CAAC,CAClC,CACF,CACF,CAAE,MAAOte,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CAED;AACA,KAAM,CAAAiS,mBAAmB,CAAIzH,IAAI,EAAK,CACpC,KAAM,CAAA+T,KAAK,CAAG,CACZ,GAAG,CAAE,OAAO,CACZ,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,OACP,CAAC,CACD,MAAO,CAAAA,KAAK,CAAC/T,IAAI,CAAC,EAAI,MAAM,CAC9B,CAAC,CAED;AACA,KAAM,CAAA+G,iBAAiB,CAAGA,CAACtN,QAAQ,CAAE8W,IAAI,CAAE7U,KAAK,GAAK,CACnD;AACA,GAAI,CAAC/I,KAAK,CAAE,CACVuB,OAAO,CAAC2f,IAAI,CAAC,oBAAoB,CAAC,CAClC,OACF,CAEA,GAAI,CACJ;AACA,KAAM,CAAApB,MAAM,CAAGrP,gBAAgB,CAACmN,IAAI,CAAC,CACrCkC,MAAM,CAAChZ,QAAQ,CAAClD,GAAG,CAACkD,QAAQ,CAACvD,CAAC,CAAE,EAAE,CAAE,CAACuD,QAAQ,CAACrD,CAAC,CAAC,CAAG;AAEnD;AACA0P,UAAU,CAAC,IAAM,CACb;AACA,GAAInT,KAAK,EAAI8f,MAAM,CAACuB,MAAM,CAAE,CAC9BrhB,KAAK,CAACiP,MAAM,CAAC6Q,MAAM,CAAC,CAClB,CACJ,CAAC,CAAE,GAAG,CAAC,CAEP;AACA9f,KAAK,CAACyO,GAAG,CAACqR,MAAM,CAAC,CAEjB;AACA;AACA;AACA;AACA;AACA,CAAE,MAAOjd,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CACpC,CACF,CAAC,CAED;AACA,KAAM,CAAA0Y,mBAAmB,CAAI+F,iBAAiB,EAAK,CACjD,GAAI,CAACthB,KAAK,CAAE,CACVuB,OAAO,CAACsB,KAAK,CAAC,gBAAgB,CAAC,CAC/B,OACF,CAEA,GAAI,CAACye,iBAAiB,CAAE,CACtB/f,OAAO,CAACsB,KAAK,CAAC,mBAAmB,CAAC,CAClC,OACF,CAEA;AACA,GAAI,CAAC9C,0BAA0B,CAAE,CAC/BwB,OAAO,CAACsB,KAAK,CAAC,oBAAoB,CAAC,CACnC;AACA,KAAM,CAAAkW,MAAM,CAAG,GAAI,CAAA3a,UAAU,CAAC,CAAC,CAC/B2a,MAAM,CAACgI,SAAS,CAAC,GAAG5f,QAAQ,4BAA4B,CAAC,CACtDogB,IAAI,CAACb,gBAAgB,EAAI,CACxB3gB,0BAA0B,CAAG2gB,gBAAgB,CAAC1gB,KAAK,CACnDD,0BAA0B,CAACmO,KAAK,CAACtK,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC7C7D,0BAA0B,CAACgQ,QAAQ,CAAEC,KAAK,EAAK,CAC7C,GAAIA,KAAK,CAACC,MAAM,EAAID,KAAK,CAACE,QAAQ,CAAE,CAClCF,KAAK,CAACE,QAAQ,CAAC6H,SAAS,CAAG,GAAG,CAC9B/H,KAAK,CAACE,QAAQ,CAAC8H,SAAS,CAAG,GAAG,CAC9BhI,KAAK,CAACE,QAAQ,CAAC+H,eAAe,CAAG,GAAG,CACtC,CACF,CAAC,CAAC,CACF1W,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC,CACrC;AACA+Z,mBAAmB,CAAC+F,iBAAiB,CAAC,CACxC,CAAC,CAAC,CACDE,KAAK,CAAC3e,KAAK,EAAI,CACdtB,OAAO,CAACsB,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACpC;AACA4e,2BAA2B,CAACH,iBAAiB,CAAC,CAChD,CAAC,CAAC,CACJ,OACF,CAEA;AACA1f,gBAAgB,CAACqL,OAAO,CAAEiO,QAAQ,EAAK,CACrC,GAAIlb,KAAK,EAAIkb,QAAQ,CAACpN,KAAK,CAAE,CAC3B9N,KAAK,CAACiP,MAAM,CAACiM,QAAQ,CAACpN,KAAK,CAAC,CAC9B,CACF,CAAC,CAAC,CACFlM,gBAAgB,CAACqZ,KAAK,CAAC,CAAC,CAExB;AACArc,iBAAiB,CAAC4M,aAAa,CAACyB,OAAO,CAAC1B,YAAY,EAAI,CACtD,GAAIA,YAAY,CAACmW,eAAe,GAAK,KAAK,CAAE,CAC1CngB,OAAO,CAACC,GAAG,CAAC,UAAU+J,YAAY,CAACG,IAAI,kBAAkB,CAAC,CAC1D,OACF,CAEA,GAAIH,YAAY,CAAC/E,QAAQ,EAAI+E,YAAY,CAAChF,SAAS,EAAIgF,YAAY,CAACnD,OAAO,CAAE,CAC3E,KAAM,CAAAwF,QAAQ,CAAG0T,iBAAiB,CAAC1V,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,CAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC,CAEDjF,OAAO,CAACC,GAAG,CAAC,SAAS+J,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,iBAAiBwF,QAAQ,CAACrK,CAAC,KAAKqK,QAAQ,CAACnK,CAAC,GAAG,CAAC,CAE7G,GAAI,CACF;AACA,GAAI,CAAC1D,0BAA0B,EAAI,CAACA,0BAA0B,CAACsD,KAAK,CAAE,CACpE,KAAM,IAAI,CAAAse,KAAK,CAAC,cAAc,CAAC,CACjC,CAEA;AACA,KAAM,CAAA/O,iBAAiB,CAAG7S,0BAA0B,CAACsD,KAAK,CAAC,CAAC,CAE5D;AACAuP,iBAAiB,CAAClH,IAAI,CAAG,OAAOH,YAAY,CAACG,IAAI,EAAE,CAEnD;AACAkH,iBAAiB,CAAC9L,QAAQ,CAAClD,GAAG,CAACgK,QAAQ,CAACrK,CAAC,CAAE,EAAE,CAAE,CAACqK,QAAQ,CAACnK,CAAC,CAAC,CAE3D;AACAmP,iBAAiB,CAAC1E,KAAK,CAACtK,GAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAEvC;AACAgP,iBAAiB,CAAC5B,WAAW,CAAG,GAAG,CAEnC;AACA4B,iBAAiB,CAAC7C,QAAQ,CAACC,KAAK,EAAI,CAClC,GAAIA,KAAK,CAACC,MAAM,CAAE,CAChBD,KAAK,CAACE,QAAQ,CAACI,WAAW,CAAG,KAAK,CAClCN,KAAK,CAACE,QAAQ,CAACe,OAAO,CAAG,GAAG,CAC5BjB,KAAK,CAACE,QAAQ,CAAC0R,IAAI,CAAGzjB,KAAK,CAAC0jB,UAAU,CACtC7R,KAAK,CAACE,QAAQ,CAACsM,UAAU,CAAG,IAAI,CAChCxM,KAAK,CAACE,QAAQ,CAAC8P,SAAS,CAAG,IAAI,CAC/BhQ,KAAK,CAACE,QAAQ,CAACK,WAAW,CAAG,IAAI,CACjCP,KAAK,CAACgB,WAAW,CAAG,GAAG,CACzB,CACF,CAAC,CAAC,CAEF;AACA4B,iBAAiB,CAAC8J,QAAQ,CAAG,CAC3BrP,IAAI,CAAE,cAAc,CACpBjF,OAAO,CAAEmD,YAAY,CAACnD,OAAO,CAC7BsD,IAAI,CAAEH,YAAY,CAACG,IACrB,CAAC,CAED;AACA1L,KAAK,CAACyO,GAAG,CAACmE,iBAAiB,CAAC,CAE5B;AACAhR,gBAAgB,CAACgC,GAAG,CAAC2H,YAAY,CAACnD,OAAO,CAAE,CACzC0F,KAAK,CAAE8E,iBAAiB,CACxBrH,YAAY,CAAEA,YAAY,CAC1BzE,QAAQ,CAAE8G,QACZ,CAAC,CAAC,CAEFrM,OAAO,CAACC,GAAG,CAAC,SAAS+J,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkBwF,QAAQ,CAACrK,CAAC,KAAK,CAACqK,QAAQ,CAACnK,CAAC,GAAG,CAAC,CACjH,CAAE,MAAOZ,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,QAAQ0I,YAAY,CAACG,IAAI,YAAY,CAAE7I,KAAK,CAAC,CAC3D;AACA+Y,wBAAwB,CAACrQ,YAAY,CAAEqC,QAAQ,CAAE0T,iBAAiB,CAAC,CACrE,CACF,CACF,CAAC,CAAC,CAEF;AACA/f,OAAO,CAACC,GAAG,CAAC,OAAOI,gBAAgB,CAACgb,IAAI,SAAS,CAAC,CAClDhb,gBAAgB,CAACqL,OAAO,CAAC,CAACiO,QAAQ,CAAE9S,OAAO,GAAK,CAC9C7G,OAAO,CAACC,GAAG,CAAC,QAAQ4G,OAAO,KAAK8S,QAAQ,CAAC3P,YAAY,CAACG,IAAI,EAAE,CAAC,CAC/D,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA+V,2BAA2B,CAAIH,iBAAiB,EAAK,CACzD1iB,iBAAiB,CAAC4M,aAAa,CAACyB,OAAO,CAAC1B,YAAY,EAAI,CACtD;AACA,GAAIA,YAAY,CAACmW,eAAe,GAAK,KAAK,CAAE,CAC1C,OACF,CAEA,GAAInW,YAAY,CAAC/E,QAAQ,EAAI+E,YAAY,CAAChF,SAAS,EAAIgF,YAAY,CAACnD,OAAO,CAAE,CAC3E;AACA,KAAM,CAAAwF,QAAQ,CAAG0T,iBAAiB,CAAC1V,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,CAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC,CAEDoV,wBAAwB,CAACrQ,YAAY,CAAEqC,QAAQ,CAAE0T,iBAAiB,CAAC,CACrE,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA1F,wBAAwB,CAAGA,CAACrQ,YAAY,CAAEqC,QAAQ,CAAE0T,iBAAiB,GAAK,CAC9E;AACA,KAAM,CAAA3P,QAAQ,CAAG,GAAI,CAAAxT,KAAK,CAAC0d,WAAW,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAClD,KAAM,CAAA3L,QAAQ,CAAG,GAAI,CAAA/R,KAAK,CAAC2d,iBAAiB,CAAC,CAC3C/S,KAAK,CAAE,QAAQ,CACfuH,WAAW,CAAE,KAAK,CAClBW,OAAO,CAAE,GACX,CAAC,CAAC,CACF,KAAM,CAAA2B,iBAAiB,CAAG,GAAI,CAAAzU,KAAK,CAAC4d,IAAI,CAACpK,QAAQ,CAAEzB,QAAQ,CAAC,CAE5D;AACA0C,iBAAiB,CAAClH,IAAI,CAAG,SAASH,YAAY,CAACG,IAAI,EAAE,CAErD;AACAkH,iBAAiB,CAAC9L,QAAQ,CAAClD,GAAG,CAACgK,QAAQ,CAACrK,CAAC,CAAE,EAAE,CAAE,CAACqK,QAAQ,CAACnK,CAAC,CAAC,CAE3D;AACAmP,iBAAiB,CAAC5B,WAAW,CAAG,GAAG,CAEnC;AACA4B,iBAAiB,CAAC8J,QAAQ,CAAG,CAC3BrP,IAAI,CAAE,cAAc,CACpBjF,OAAO,CAAEmD,YAAY,CAACnD,OAAO,CAC7BsD,IAAI,CAAEH,YAAY,CAACG,IACrB,CAAC,CAED;AACA,KAAM,CAAAoW,gBAAgB,CAAG,GAAI,CAAA3jB,KAAK,CAACme,cAAc,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CAC5D,KAAM,CAAAyF,gBAAgB,CAAG,GAAI,CAAA5jB,KAAK,CAAC2d,iBAAiB,CAAC,CACnD/S,KAAK,CAAE,QAAQ,CACfuH,WAAW,CAAE,IAAI,CACjBW,OAAO,CAAE,GAAG,CAAG;AACfuL,UAAU,CAAE,KACd,CAAC,CAAC,CAEF,KAAM,CAAAwF,QAAQ,CAAG,GAAI,CAAA7jB,KAAK,CAAC4d,IAAI,CAAC+F,gBAAgB,CAAEC,gBAAgB,CAAC,CACnEC,QAAQ,CAACtW,IAAI,CAAG,YAAYH,YAAY,CAACG,IAAI,EAAE,CAC/CsW,QAAQ,CAACtF,QAAQ,CAAG,CAClBrP,IAAI,CAAE,cAAc,CACpBjF,OAAO,CAAEmD,YAAY,CAACnD,OAAO,CAC7BsD,IAAI,CAAEH,YAAY,CAACG,IAAI,CACvBuW,UAAU,CAAE,IACd,CAAC,CAEDrP,iBAAiB,CAACnE,GAAG,CAACuT,QAAQ,CAAC,CAE/B;AACAhiB,KAAK,CAACyO,GAAG,CAACmE,iBAAiB,CAAC,CAE5B;AACAhR,gBAAgB,CAACgC,GAAG,CAAC2H,YAAY,CAACnD,OAAO,CAAE,CACzC0F,KAAK,CAAE8E,iBAAiB,CACxBrH,YAAY,CAAEA,YAAY,CAC1BzE,QAAQ,CAAE8G,QACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAsU,aAAa,CAAG,GAAI,CAAA/jB,KAAK,CAACme,cAAc,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CACzD,KAAM,CAAA6F,aAAa,CAAG,GAAI,CAAAhkB,KAAK,CAAC2d,iBAAiB,CAAC,CAAE/S,KAAK,CAAE,QAAS,CAAC,CAAC,CACtE,KAAM,CAAAqZ,SAAS,CAAG,GAAI,CAAAjkB,KAAK,CAAC4d,IAAI,CAACmG,aAAa,CAAEC,aAAa,CAAC,CAC9DC,SAAS,CAACtb,QAAQ,CAAClD,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAEhC;AACAwe,SAAS,CAAC1F,QAAQ,CAAG,CACnBrP,IAAI,CAAE,cAAc,CACpBjF,OAAO,CAAEmD,YAAY,CAACnD,OAAO,CAC7BsD,IAAI,CAAEH,YAAY,CAACG,IACrB,CAAC,CAEDkH,iBAAiB,CAACnE,GAAG,CAAC2T,SAAS,CAAC,CAEhC7gB,OAAO,CAACC,GAAG,CAAC,SAAS+J,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkBwF,QAAQ,CAACrK,CAAC,KAAK,CAACqK,QAAQ,CAACnK,CAAC,GAAG,CAAC,CACjH,CAAC,CAED;AACA,KAAM,CAAA0O,iBAAiB,CAAIL,OAAO,EAAK,CACrC,OAAOA,OAAO,EACZ,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,QAAS,MAAO,KAAKA,OAAO,EAAE,CAChC,CACF,CAAC,CAED;AACA,KAAM,CAAAI,oBAAoB,CAAImQ,OAAO,EAAK,CACxC,OAAOA,OAAO,EACZ,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,QAAS,MAAO,KAAKA,OAAO,EAAE,CAChC,CACF,CAAC,CAED;AACA,KAAM,CAAA3G,gBAAgB,CAAGA,CAACjI,KAAK,CAAE6O,SAAS,CAAEC,aAAa,CAAEC,cAAc,GAAK,CAC5E,GAAI,CAACF,SAAS,EAAI,CAACC,aAAa,EAAI,CAACC,cAAc,CAAE,OAErDjhB,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEiS,KAAK,CAACgP,OAAO,CAAEhP,KAAK,CAACiP,OAAO,CAAC,CAEvD;AACA,KAAM,CAAAC,IAAI,CAAGL,SAAS,CAACM,qBAAqB,CAAC,CAAC,CAC9C,KAAM,CAAAC,MAAM,CAAI,CAACpP,KAAK,CAACgP,OAAO,CAAGE,IAAI,CAAC3b,IAAI,EAAIsb,SAAS,CAACQ,WAAW,CAAI,CAAC,CAAG,CAAC,CAC5E,KAAM,CAAAC,MAAM,CAAG,EAAE,CAACtP,KAAK,CAACiP,OAAO,CAAGC,IAAI,CAACha,GAAG,EAAI2Z,SAAS,CAACU,YAAY,CAAC,CAAG,CAAC,CAAG,CAAC,CAE7E;AACA,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAA9kB,KAAK,CAAC+kB,SAAS,CAAC,CAAC,CACvC;AACAD,SAAS,CAACnF,MAAM,CAACqF,MAAM,CAACC,SAAS,CAAG,CAAC,CACrCH,SAAS,CAACnF,MAAM,CAACuF,IAAI,CAACD,SAAS,CAAG,CAAC,CAEnC,KAAM,CAAAE,WAAW,CAAG,GAAI,CAAAnlB,KAAK,CAAColB,OAAO,CAACV,MAAM,CAAEE,MAAM,CAAC,CACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,CAAEd,cAAc,CAAC,CAEpDjhB,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEqhB,MAAM,CAAEE,MAAM,CAAC,CAEtC;AACA,KAAM,CAAAU,mBAAmB,CAAG,EAAE,CAE9B7hB,gBAAgB,CAACqL,OAAO,CAAC,CAACiO,QAAQ,CAAE9S,OAAO,GAAK,CAC9C,GAAI8S,QAAQ,CAACpN,KAAK,CAAE,CAClB;AACA2V,mBAAmB,CAAChR,IAAI,CAACyI,QAAQ,CAACpN,KAAK,CAAC,CACxC;AACAoN,QAAQ,CAACpN,KAAK,CAAC3F,OAAO,CAAG,IAAI,CAC7B+S,QAAQ,CAACpN,KAAK,CAACkD,WAAW,CAAG,IAAI,CAAE;AACnC;AACAkK,QAAQ,CAACpN,KAAK,CAACiC,QAAQ,CAACC,KAAK,EAAI,CAC/BA,KAAK,CAAC7H,OAAO,CAAG,IAAI,CACpB6H,KAAK,CAACgB,WAAW,CAAG,IAAI,CAC1B,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEFzP,OAAO,CAACC,GAAG,CAAC,QAAQiiB,mBAAmB,CAACtL,MAAM,gBAAgB,CAAC,CAE/D;AACA,KAAM,CAAAuL,sBAAsB,CAAGT,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,CAAE,IAAI,CAAC,CAEpF,GAAIC,sBAAsB,CAACvL,MAAM,CAAG,CAAC,CAAE,CACrC5W,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEkiB,sBAAsB,CAACvL,MAAM,CAAC,CACxDuL,sBAAsB,CAACzW,OAAO,CAAC,CAAC2W,SAAS,CAAEC,KAAK,GAAK,CACnDtiB,OAAO,CAACC,GAAG,CAAC,QAAQqiB,KAAK,GAAG,CACjBD,SAAS,CAACE,MAAM,CAACpY,IAAI,EAAI,KAAK,CAC9B,KAAK,CAAEkY,SAAS,CAAC5f,QAAQ,CACzB,WAAW,CAAE4f,SAAS,CAACE,MAAM,CAACpH,QAAQ,CAAC,CACpD,CAAC,CAAC,CAEF;AACA,KAAM,CAAAqH,GAAG,CAAGC,yBAAyB,CAACN,sBAAsB,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC,CAEvE,GAAIC,GAAG,EAAIA,GAAG,CAACrH,QAAQ,EAAIqH,GAAG,CAACrH,QAAQ,CAACrP,IAAI,GAAK,cAAc,CAAE,CAC/D;AACA,KAAM,CAAAjF,OAAO,CAAG2b,GAAG,CAACrH,QAAQ,CAACtU,OAAO,CACpC7G,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAE4G,OAAO,CAAE,KAAK,CAAE,MAAO,CAAAA,OAAO,CAAC,CAEhE;AACA,GAAI,CAAA6b,SAAS,CAAG7b,OAAO,CACvB,GAAI,MAAO,CAAAA,OAAO,GAAK,QAAQ,EAAI,CAACxG,gBAAgB,CAACiC,GAAG,CAACuE,OAAO,CAAC,EAAIxG,gBAAgB,CAACiC,GAAG,CAAC0O,QAAQ,CAACnK,OAAO,CAAC,CAAC,CAAE,CAC5G6b,SAAS,CAAG1R,QAAQ,CAACnK,OAAO,CAAC,CAC7B7G,OAAO,CAACC,GAAG,CAAC,cAAcyiB,SAAS,EAAE,CAAC,CACxC,CAAC,IAAM,IAAI,MAAO,CAAA7b,OAAO,GAAK,QAAQ,EAAI,CAACxG,gBAAgB,CAACiC,GAAG,CAACuE,OAAO,CAAC,EAAIxG,gBAAgB,CAACiC,GAAG,CAAC8O,MAAM,CAACvK,OAAO,CAAC,CAAC,CAAE,CACjH6b,SAAS,CAAGtR,MAAM,CAACvK,OAAO,CAAC,CAC3B7G,OAAO,CAACC,GAAG,CAAC,eAAeyiB,SAAS,EAAE,CAAC,CACzC,CAEA;AACAtjB,MAAM,CAACyS,qBAAqB,CAAC6Q,SAAS,CAAC,CACvC,OACF,CACF,CAEA;AACA,KAAM,CAAAC,UAAU,CAAGjB,SAAS,CAACU,gBAAgB,CAACpB,aAAa,CAACrK,QAAQ,CAAE,IAAI,CAAC,CAE3E3W,OAAO,CAACC,GAAG,CAAC,aAAa,CAAE0iB,UAAU,CAAC/L,MAAM,CAAC,CAE7C,GAAI+L,UAAU,CAAC/L,MAAM,CAAG,CAAC,CAAE,CACzB;AACA+L,UAAU,CAACjX,OAAO,CAAC,CAAC2W,SAAS,CAAEC,KAAK,GAAK,CACvC,KAAM,CAAAE,GAAG,CAAGH,SAAS,CAACE,MAAM,CAC5BviB,OAAO,CAACC,GAAG,CAAC,UAAUqiB,KAAK,GAAG,CAAEE,GAAG,CAACrY,IAAI,EAAI,KAAK,CACrC,WAAW,CAAEqY,GAAG,CAACrH,QAAQ,CACzB,KAAK,CAAEkH,SAAS,CAAC5f,QAAQ,CAAC,CACxC,CAAC,CAAC,CAEF;AACA,IAAK,GAAI,CAAAyH,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGyY,UAAU,CAAC/L,MAAM,CAAE1M,CAAC,EAAE,CAAE,CAC1C,KAAM,CAAAsY,GAAG,CAAGC,yBAAyB,CAACE,UAAU,CAACzY,CAAC,CAAC,CAACqY,MAAM,CAAC,CAC3D,GAAIC,GAAG,EAAIA,GAAG,CAACrH,QAAQ,EAAIqH,GAAG,CAACrH,QAAQ,CAACrP,IAAI,GAAK,cAAc,CAAE,CAC/D,KAAM,CAAAjF,OAAO,CAAG2b,GAAG,CAACrH,QAAQ,CAACtU,OAAO,CACpCzH,MAAM,CAACyS,qBAAqB,CAAChL,OAAO,CAAC,CACrC,OACF,CACF,CACF,CAEA;AACA7G,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAE9B;AACA,GAAI,CAAA2iB,YAAY,CAAG,IAAI,CACvB,GAAI,CAAAvZ,WAAW,CAAG,GAAG,CAAE;AAEvBhJ,gBAAgB,CAACqL,OAAO,CAAC,CAACiO,QAAQ,CAAE9S,OAAO,GAAK,CAC9C,GAAI8S,QAAQ,CAACpN,KAAK,CAAE,CAClB,KAAM,CAAAsW,QAAQ,CAAG,GAAI,CAAAjmB,KAAK,CAACkG,OAAO,CAAC,CAAC,CACpC;AACA6W,QAAQ,CAACpN,KAAK,CAACuW,gBAAgB,CAACD,QAAQ,CAAC,CAEzC;AACA,KAAM,CAAAE,SAAS,CAAGF,QAAQ,CAAC/gB,KAAK,CAAC,CAAC,CAClCihB,SAAS,CAACC,OAAO,CAAC/B,cAAc,CAAC,CAEjC;AACA,KAAM,CAAAgC,EAAE,CAAGF,SAAS,CAAC/gB,CAAC,CAAGsf,MAAM,CAC/B,KAAM,CAAA4B,EAAE,CAAGH,SAAS,CAAC7gB,CAAC,CAAGsf,MAAM,CAC/B,KAAM,CAAA/e,QAAQ,CAAGS,IAAI,CAACigB,IAAI,CAACF,EAAE,CAAGA,EAAE,CAAGC,EAAE,CAAGA,EAAE,CAAC,CAE7CljB,OAAO,CAACC,GAAG,CAAC,MAAM4G,OAAO,OAAO,CAAEpE,QAAQ,CAAC,CAE3C,GAAIA,QAAQ,CAAG4G,WAAW,CAAE,CAC1BA,WAAW,CAAG5G,QAAQ,CACtBmgB,YAAY,CAAG,CAAE/b,OAAO,CAAEpE,QAAS,CAAC,CACtC,CACF,CACF,CAAC,CAAC,CAEF,GAAImgB,YAAY,CAAE,CAChB5iB,OAAO,CAACC,GAAG,CAAC,oBAAoB2iB,YAAY,CAAC/b,OAAO,SAAS+b,YAAY,CAACngB,QAAQ,EAAE,CAAC,CAErF;AACArD,MAAM,CAACyS,qBAAqB,CAAC+Q,YAAY,CAAC/b,OAAO,CAAC,CAClD,OACF,CAEA7G,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAmc,kBAAkB,CAAIgH,eAAe,EAAK,CAC9C;AACA,GAAIhkB,MAAM,CAAC6H,0BAA0B,EAAI7H,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,CAAE,CAClFoR,aAAa,CAACla,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,CAAC,CACxD9I,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,CAAG,IAAI,CAChDlI,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC9B,CAEA;AACA,GAAIb,MAAM,CAAC4H,mBAAmB,CAAE,CAC9B5H,MAAM,CAAC4H,mBAAmB,CAACkB,OAAO,CAAG,IAAI,CAC3C,CAEA;AACAkb,eAAe,CAAC7R,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE3K,OAAO,CAAE,KAAM,CAAC,CAAC,CAAC,CACtD5G,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC,CACtB,CAAC,CAED;AACAb,MAAM,CAACikB,qBAAqB,CAAIxc,OAAO,EAAK,CAC1C,GAAI,KAAAyc,qBAAA,CAAAC,sBAAA,CACF;AACA,KAAM,CAAAzS,YAAY,CAAGzQ,gBAAgB,CAACmC,GAAG,CAACqE,OAAO,EAAI,GAAG,CAAC,CACzD,GAAI,CAACiK,YAAY,CAAE,CACjB9Q,OAAO,CAACsB,KAAK,CAAC,cAAc,CAAEuF,OAAO,CAAC,CAEtC;AACA7G,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxBI,gBAAgB,CAACqL,OAAO,CAAC,CAAC8X,KAAK,CAAE5X,EAAE,GAAK,CACtC5L,OAAO,CAACC,GAAG,CAAC,KAAK2L,EAAE,KAAK4X,KAAK,CAACxZ,YAAY,CAACG,IAAI,EAAE,CAAC,CACpD,CAAC,CAAC,CAEF,MAAO,MAAK,CACd,CAEA;AACA,KAAM,CAAAsZ,UAAU,CAAG3S,YAAY,CAACvE,KAAK,CAErC;AACA,KAAM,CAAAmX,SAAS,CAAGpjB,kBAAkB,CAACkC,GAAG,CAACqE,OAAO,CAAC,CACjD,KAAM,CAAAmD,YAAY,CAAG8G,YAAY,CAAC9G,YAAY,CAE9C;AACA,GAAI,CAAAlD,OAAO,CAEX,GAAI4c,SAAS,EAAIA,SAAS,CAAC3c,MAAM,CAAE,CACjCD,OAAO,cACLrJ,KAAA,QAAK8d,KAAK,CAAE,CAAExV,OAAO,CAAE,KAAK,CAAEsB,KAAK,CAAE,OAAO,CAAEyU,SAAS,CAAE,OAAO,CAAE6H,SAAS,CAAE,MAAO,CAAE,CAAAhN,QAAA,eACpFlZ,KAAA,QAAK8d,KAAK,CAAE,CACV9T,UAAU,CAAE,MAAM,CAClBmc,YAAY,CAAE,KAAK,CACnBxd,QAAQ,CAAE,MAAM,CAChByd,YAAY,CAAE,gBAAgB,CAC9BC,aAAa,CAAE,KACjB,CAAE,CAAAnN,QAAA,EACC3M,YAAY,CAACG,IAAI,CAAC,QAAM,CAACtD,OAAO,CAAC,GACpC,EAAK,CAAC,cACNtJ,IAAA,QAAAoZ,QAAA,CACG+M,SAAS,CAAC3c,MAAM,CAACwG,GAAG,CAAC,CAAC+C,KAAK,CAAEgS,KAAK,GAAK,CACtC,GAAI,CAAAyB,UAAU,CACd,OAAQzT,KAAK,CAACQ,YAAY,EACxB,IAAK,GAAG,CAAEiT,UAAU,CAAG,SAAS,CAAE,MAClC,IAAK,GAAG,CAAEA,UAAU,CAAG,SAAS,CAAE,MAClC,IAAK,GAAG,CAAE,QAASA,UAAU,CAAG,SAAS,CAAE,MAC7C,CAEA,mBACEtmB,KAAA,QAAiB8d,KAAK,CAAE,CACtBqI,YAAY,CAAE,KAAK,CACnB5d,eAAe,CAAE,uBAAuB,CACxCD,OAAO,CAAE,KAAK,CACdG,YAAY,CAAE,KAAK,CACnBE,QAAQ,CAAE,MACZ,CAAE,CAAAuQ,QAAA,eACApZ,IAAA,QAAKge,KAAK,CAAE,CAAE9T,UAAU,CAAE,MAAO,CAAE,CAAAkP,QAAA,CAChC/F,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC,CAC9B,CAAC,cACN9S,KAAA,QAAK8d,KAAK,CAAE,CAAE3V,OAAO,CAAE,MAAM,CAAEoe,cAAc,CAAE,eAAgB,CAAE,CAAArN,QAAA,eAC/DpZ,IAAA,SAAAoZ,QAAA,CAAM,gBAAI,CAAM,CAAC,cACjBpZ,IAAA,SAAMge,KAAK,CAAE,CACX/T,KAAK,CAAEuc,UAAU,CACjBtc,UAAU,CAAE,MAAM,CAClBzB,eAAe,CAAE,iBAAiB,CAClCD,OAAO,CAAE,OAAO,CAChBG,YAAY,CAAE,KAChB,CAAE,CAAAyQ,QAAA,CACCrG,KAAK,CAACQ,YAAY,GAAK,GAAG,CAAG,IAAI,CAAGR,KAAK,CAACQ,YAAY,GAAK,GAAG,CAAG,IAAI,CAAG,IAAI,CACzE,CAAC,EACJ,CAAC,cACNrT,KAAA,QAAK8d,KAAK,CAAE,CAAE3V,OAAO,CAAE,MAAM,CAAEoe,cAAc,CAAE,eAAgB,CAAE,CAAArN,QAAA,eAC/DpZ,IAAA,SAAAoZ,QAAA,CAAM,sBAAK,CAAM,CAAC,cAClBlZ,KAAA,SAAM8d,KAAK,CAAE,CAAE9T,UAAU,CAAE,MAAO,CAAE,CAAAkP,QAAA,EAAErG,KAAK,CAACS,UAAU,CAAC,SAAE,EAAM,CAAC,EAC7D,CAAC,GAzBEuR,KA0BL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,cACN7kB,KAAA,QAAK8d,KAAK,CAAE,CAAE0I,SAAS,CAAE,KAAK,CAAE7d,QAAQ,CAAE,MAAM,CAAEoB,KAAK,CAAE,MAAO,CAAE,CAAAmP,QAAA,EAAC,4BAC3D,CAAC,GAAI,CAAAjS,IAAI,CAAC,CAAC,CAACwf,kBAAkB,CAAC,CAAC,CAAC,6BACzC,EAAK,CAAC,EACH,CACN,CACH,CAAC,IAAM,CACLpd,OAAO,cACLrJ,KAAA,QAAK8d,KAAK,CAAE,CAAExV,OAAO,CAAE,KAAK,CAAEiW,QAAQ,CAAE,OAAQ,CAAE,CAAArF,QAAA,eAChDpZ,IAAA,QAAKge,KAAK,CAAE,CAAE9T,UAAU,CAAE,MAAM,CAAEmc,YAAY,CAAE,KAAM,CAAE,CAAAjN,QAAA,CAAE3M,YAAY,CAACG,IAAI,CAAM,CAAC,cAClF1M,KAAA,QAAAkZ,QAAA,EAAK,kBAAM,CAAC9P,OAAO,EAAM,CAAC,cAC1BtJ,IAAA,QAAAoZ,QAAA,CAAK,8DAAU,CAAK,CAAC,EAClB,CACN,CACH,CAEA;AACA,KAAM,CAAAwN,OAAO,CAAG/kB,MAAM,CAACiV,UAAU,CAAG,CAAC,CAAE,GAAG,CAC1C,KAAM,CAAA+P,OAAO,CAAGhlB,MAAM,CAACkV,WAAW,CAAG,CAAC,CAAE,GAAG,CAE3C;AACA,KAAM,CAAA8O,eAAe,EAAAE,qBAAA,CAAG1G,QAAQ,CAAC+B,aAAa,CAAC,OAAO,CAAC,UAAA2E,qBAAA,kBAAAC,sBAAA,CAA/BD,qBAAA,CAAiCe,gBAAgB,UAAAd,sBAAA,iBAAjDA,sBAAA,CAAmD5c,sBAAsB,CAEjG,GAAIyc,eAAe,CAAE,CACnB;AACAA,eAAe,CAAC,CACdxc,OAAO,CAAE,IAAI,CACbC,OAAO,CAAEA,OAAO,CAChBtB,QAAQ,CAAE,CAAEvD,CAAC,CAAEmiB,OAAO,CAAEjiB,CAAC,CAAEkiB,OAAQ,CAAC,CACpCtd,OAAO,CAAEA,OAAO,CAChBC,MAAM,CAAE,CAAA2c,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE3c,MAAM,GAAI,EAC/B,CAAC,CAAC,CAEF/G,OAAO,CAACC,GAAG,CAAC,SAAS+J,YAAY,CAACG,IAAI,KAAKtD,OAAO,UAAU,CAAC,CAC7D,MAAO,KAAI,CACb,CAAC,IAAM,CACL;AACA,KAAM,CAAAyd,OAAO,CAAG1H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAC7CyH,OAAO,CAAC/I,KAAK,CAAChW,QAAQ,CAAG,UAAU,CACnC+e,OAAO,CAAC/I,KAAK,CAAC9V,IAAI,CAAG,GAAG0e,OAAO,IAAI,CACnCG,OAAO,CAAC/I,KAAK,CAACnU,GAAG,CAAG,GAAGgd,OAAO,IAAI,CAClCE,OAAO,CAAC/I,KAAK,CAAC7V,SAAS,CAAG,wBAAwB,CAClD4e,OAAO,CAAC/I,KAAK,CAAC5V,MAAM,CAAG,MAAM,CAC7B2e,OAAO,CAAC/I,KAAK,CAACvV,eAAe,CAAG,qBAAqB,CACrDse,OAAO,CAAC/I,KAAK,CAAC/T,KAAK,CAAG,OAAO,CAC7B8c,OAAO,CAAC/I,KAAK,CAACrV,YAAY,CAAG,KAAK,CAClCoe,OAAO,CAAC/I,KAAK,CAAClV,SAAS,CAAG,8BAA8B,CACxDie,OAAO,CAAC/I,KAAK,CAACxV,OAAO,CAAG,KAAK,CAC7Bue,OAAO,CAAC/I,KAAK,CAACS,QAAQ,CAAG,OAAO,CAChCsI,OAAO,CAAC/I,KAAK,CAACnV,QAAQ,CAAG,MAAM,CAE/Bke,OAAO,CAACC,SAAS,CAAG;AAC1B;AACA,YAAYva,YAAY,CAACG,IAAI,SAAStD,OAAO;AAC7C;AACA;AACA,qBAAqBA,OAAO;AAC5B,eAAe6c,SAAS,CAAG,UAAU,CAAG,YAAY;AACpD;AACA;AACA,OAAO,CAED9G,QAAQ,CAAC4H,IAAI,CAAC1P,WAAW,CAACwP,OAAO,CAAC,CAElC;AACA,KAAM,CAAAG,WAAW,CAAGH,OAAO,CAAC3F,aAAa,CAAC,QAAQ,CAAC,CACnD,GAAI8F,WAAW,CAAE,CACfA,WAAW,CAACtL,gBAAgB,CAAC,OAAO,CAAE,IAAM,CAC1CyD,QAAQ,CAAC4H,IAAI,CAAC/K,WAAW,CAAC6K,OAAO,CAAC,CACpC,CAAC,CAAC,CACJ,CAEAtkB,OAAO,CAACC,GAAG,CAAC,gBAAgB+J,YAAY,CAACG,IAAI,KAAKtD,OAAO,OAAO,CAAC,CACjE,MAAO,KAAI,CACb,CACF,CAAE,MAAOvF,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACAlC,MAAM,CAACslB,iBAAiB,CAAG,IAAM,CAC/B1kB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CAErB,GAAI,CAACI,gBAAgB,EAAIA,gBAAgB,CAACgb,IAAI,GAAK,CAAC,CAAE,CACpDrb,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB,MAAO,EAAE,CACX,CAEA,KAAM,CAAA0kB,IAAI,CAAG,EAAE,CACftkB,gBAAgB,CAACqL,OAAO,CAAC,CAAC8X,KAAK,CAAE5X,EAAE,GAAK,CACtC5L,OAAO,CAACC,GAAG,CAAC,SAAS2L,EAAE,SAAS4X,KAAK,CAACxZ,YAAY,CAACG,IAAI,EAAE,CAAC,CAC1Dwa,IAAI,CAACzT,IAAI,CAAC,CACRtF,EAAE,CACFzB,IAAI,CAAEqZ,KAAK,CAACxZ,YAAY,CAACG,IAAI,CAC7B5E,QAAQ,CAAEie,KAAK,CAACje,QAClB,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,MAAO,CAAAof,IAAI,CACb,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACAvlB,MAAM,CAACyS,qBAAqB,CAAIhL,OAAO,EAAK,CAC1C,GAAI,CACF;AACAA,OAAO,CAAGuK,MAAM,CAACvK,OAAO,CAAC,CAEzB7G,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAE4G,OAAO,CAAE,KAAK,CAAE,MAAO,CAAAA,OAAO,CAAC,CAC/E7G,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEI,gBAAgB,CAACgb,IAAI,CAAC,CAE3D;AACA,GAAI,CAAAvK,YAAY,CAAGzQ,gBAAgB,CAACmC,GAAG,CAACqE,OAAO,CAAC,CAChD,GAAI,CAACiK,YAAY,CAAE,CACjB;AACA,KAAM,CAAA8T,SAAS,CAAG5T,QAAQ,CAACnK,OAAO,CAAC,CACnCiK,YAAY,CAAGzQ,gBAAgB,CAACmC,GAAG,CAACoiB,SAAS,CAAC,CAE9C,GAAI9T,YAAY,CAAE,CAChB9Q,OAAO,CAACC,GAAG,CAAC,UAAU2kB,SAAS,SAAS,CAAC,CACzC/d,OAAO,CAAG+d,SAAS,CAAE;AACvB,CACF,CAEA,GAAI,CAAC9T,YAAY,CAAE,CACjB9Q,OAAO,CAACsB,KAAK,CAAC,cAAc,CAAEuF,OAAO,CAAC,CACtC,MAAO,MAAK,CACd,CAEA,KAAM,CAAA6c,SAAS,CAAGpjB,kBAAkB,CAACkC,GAAG,CAACqE,OAAO,CAAC,CACjD,KAAM,CAAAmD,YAAY,CAAG8G,YAAY,CAAC9G,YAAY,CAE9C;AACA,KAAM,CAAA6a,QAAQ,CAAG,CACf,GAAG,CAAE,CAAEC,GAAG,CAAE,GAAG,CAAEhZ,IAAI,CAAE,MAAO,CAAC,CAAE,GAAG,CAAE,CAAEgZ,GAAG,CAAE,GAAG,CAAEhZ,IAAI,CAAE,UAAW,CAAC,CAAE,GAAG,CAAE,CAAEgZ,GAAG,CAAE,GAAG,CAAEhZ,IAAI,CAAE,OAAQ,CAAC,CACtG,GAAG,CAAE,CAAEgZ,GAAG,CAAE,GAAG,CAAEhZ,IAAI,CAAE,MAAO,CAAC,CAAE,GAAG,CAAE,CAAEgZ,GAAG,CAAE,GAAG,CAAEhZ,IAAI,CAAE,UAAW,CAAC,CAAE,GAAG,CAAE,CAAEgZ,GAAG,CAAE,GAAG,CAAEhZ,IAAI,CAAE,OAAQ,CAAC,CACtG,GAAG,CAAE,CAAEgZ,GAAG,CAAE,GAAG,CAAEhZ,IAAI,CAAE,MAAO,CAAC,CAAE,IAAI,CAAE,CAAEgZ,GAAG,CAAE,GAAG,CAAEhZ,IAAI,CAAE,UAAW,CAAC,CAAE,IAAI,CAAE,CAAEgZ,GAAG,CAAE,GAAG,CAAEhZ,IAAI,CAAE,OAAQ,CAAC,CACxG,IAAI,CAAE,CAAEgZ,GAAG,CAAE,GAAG,CAAEhZ,IAAI,CAAE,MAAO,CAAC,CAAE,IAAI,CAAE,CAAEgZ,GAAG,CAAE,GAAG,CAAEhZ,IAAI,CAAE,UAAW,CAAC,CAAE,IAAI,CAAE,CAAEgZ,GAAG,CAAE,GAAG,CAAEhZ,IAAI,CAAE,OAAQ,CAC1G,CAAC,CAED,KAAM,CAAAiZ,SAAS,CAAG,CAAC,MAAM,CAAE,UAAU,CAAE,OAAO,CAAC,CAC/C,KAAM,CAAAC,QAAQ,CAAG,CAAEC,CAAC,CAAE,SAAS,CAAEC,CAAC,CAAE,SAAS,CAAEC,CAAC,CAAE,SAAU,CAAC,CAC7D,KAAM,CAAAC,OAAO,CAAG,CAAEC,CAAC,CAAE,CAAC,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAE,CAAC,CAE9C,GAAI9B,SAAS,EAAIA,SAAS,CAAC3c,MAAM,CAAE,CACjC2c,SAAS,CAAC3c,MAAM,CAAC2E,OAAO,CAAC4E,KAAK,EAAI,CAChC,KAAM,CAAA/C,GAAG,CAAGsX,QAAQ,CAACvU,KAAK,CAACC,OAAO,CAAC,CACnC,GAAIhD,GAAG,CAAE,CACP6X,OAAO,CAAC7X,GAAG,CAACuX,GAAG,CAAC,CAACvX,GAAG,CAACzB,IAAI,CAAC,CAAG,CAC3BtE,KAAK,CAAEwd,QAAQ,CAAC1U,KAAK,CAACQ,YAAY,CAAC,EAAI,MAAM,CAC7CC,UAAU,CAAET,KAAK,CAACS,UACpB,CAAC,CACH,CACF,CAAC,CAAC,CACJ,CAEA,KAAM,CAAAjK,OAAO,cACXrJ,KAAA,QAAK8d,KAAK,CAAE,CAAExV,OAAO,CAAE,KAAK,CAAEsB,KAAK,CAAE,OAAO,CAAE6U,UAAU,CAAE,kBAAmB,CAAE,CAAAvF,QAAA,eAC7ElZ,KAAA,QAAK8d,KAAK,CAAE,CAAE9T,UAAU,CAAE,MAAM,CAAEmc,YAAY,CAAE,KAAK,CAAExd,QAAQ,CAAE,MAAM,CAAE2X,SAAS,CAAE,QAAS,CAAE,CAAApH,QAAA,EAAE3M,YAAY,CAACG,IAAI,CAAC,cAAE,EAAK,CAAC,cAC3H1M,KAAA,QAAK8d,KAAK,CAAE,CACV3V,OAAO,CAAE,MAAM,CACf6f,gBAAgB,CAAE,gBAAgB,CAClCC,mBAAmB,CAAE,gBAAgB,CACrC1B,cAAc,CAAE,QAAQ,CACxB2B,UAAU,CAAE,QAAQ,CACpBzJ,UAAU,CAAE,wBAAwB,CACpChW,YAAY,CAAE,KAAK,CACnB0f,MAAM,CAAE,QAAQ,CAChBrgB,QAAQ,CAAE,UACZ,CAAE,CAAAoR,QAAA,eAEApZ,IAAA,QAAKge,KAAK,CAAE,CAAEsK,OAAO,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAE/H,SAAS,CAAE,QAAS,CAAE,CAAApH,QAAA,CAC5DoO,SAAS,CAACxX,GAAG,CAACzB,IAAI,EAAIsZ,OAAO,CAACG,CAAC,CAACzZ,IAAI,CAAC,eACpCrO,KAAA,QAAgB8d,KAAK,CAAE,CAACqI,YAAY,CAAC,KAAK,CAAE,CAAAjN,QAAA,eAC1CpZ,IAAA,QAAKge,KAAK,CAAE,CAACnV,QAAQ,CAAC,MAAM,CAAEoB,KAAK,CAAE4d,OAAO,CAACG,CAAC,CAACzZ,IAAI,CAAC,CAACtE,KAAK,CAAEC,UAAU,CAAC,MAAM,CAAE,CAAAkP,QAAA,CAAEyO,OAAO,CAACG,CAAC,CAACzZ,IAAI,CAAC,CAACiF,UAAU,CAAM,CAAC,cAClHxT,IAAA,SAAMge,KAAK,CAAE,CAAC/T,KAAK,CAAE4d,OAAO,CAACG,CAAC,CAACzZ,IAAI,CAAC,CAACtE,KAAK,CAAEpB,QAAQ,CAAC,MAAM,CAAEqB,UAAU,CAAC,MAAM,CAAEF,UAAU,CAAE,MAAM,CAAE,CAAAoP,QAAA,CACjG7K,IAAI,GAAK,MAAM,CAAG,GAAG,CAAGA,IAAI,GAAK,UAAU,CAAG,GAAG,CAAG,GAAG,CACpD,CAAC,GAJCA,IAKL,CACN,CAAC,CACC,CAAC,cAENvO,IAAA,QAAKge,KAAK,CAAE,CAAEsK,OAAO,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAE/H,SAAS,CAAE,QAAS,CAAE,CAAApH,QAAA,CAC5DoO,SAAS,CAACxX,GAAG,CAACzB,IAAI,EAAIsZ,OAAO,CAACC,CAAC,CAACvZ,IAAI,CAAC,eACpCrO,KAAA,QAAgB8d,KAAK,CAAE,CAACqI,YAAY,CAAC,KAAK,CAAE,CAAAjN,QAAA,eAC1CpZ,IAAA,SAAMge,KAAK,CAAE,CAAC/T,KAAK,CAAE4d,OAAO,CAACC,CAAC,CAACvZ,IAAI,CAAC,CAACtE,KAAK,CAAEpB,QAAQ,CAAC,MAAM,CAAEqB,UAAU,CAAC,MAAM,CAAEF,UAAU,CAAE,MAAM,CAAE,CAAAoP,QAAA,CACjG7K,IAAI,GAAK,MAAM,CAAG,GAAG,CAAGA,IAAI,GAAK,UAAU,CAAG,GAAG,CAAG,GAAG,CACpD,CAAC,cACPvO,IAAA,QAAKge,KAAK,CAAE,CAACnV,QAAQ,CAAC,MAAM,CAAEoB,KAAK,CAAE4d,OAAO,CAACC,CAAC,CAACvZ,IAAI,CAAC,CAACtE,KAAK,CAAEC,UAAU,CAAC,MAAM,CAAE,CAAAkP,QAAA,CAAEyO,OAAO,CAACC,CAAC,CAACvZ,IAAI,CAAC,CAACiF,UAAU,CAAM,CAAC,GAJ1GjF,IAKL,CACN,CAAC,CACC,CAAC,cAENvO,IAAA,QAAKge,KAAK,CAAE,CAAEsK,OAAO,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAE/H,SAAS,CAAE,QAAS,CAAE,CAAApH,QAAA,CAC5DoO,SAAS,CAACxX,GAAG,CAACzB,IAAI,EAAIsZ,OAAO,CAACE,CAAC,CAACxZ,IAAI,CAAC,eACpCrO,KAAA,QAAgB8d,KAAK,CAAE,CAACqI,YAAY,CAAC,KAAK,CAAEhe,OAAO,CAAE,MAAM,CAAE+f,UAAU,CAAE,QAAQ,CAAE3B,cAAc,CAAE,UAAU,CAAE,CAAArN,QAAA,eAC7GpZ,IAAA,QAAKge,KAAK,CAAE,CACVnV,QAAQ,CAAC,MAAM,CACfoB,KAAK,CAAE4d,OAAO,CAACE,CAAC,CAACxZ,IAAI,CAAC,CAACtE,KAAK,CAC5BC,UAAU,CAAC,MAAM,CACjBse,WAAW,CAAE,KAAK,CAClB1e,KAAK,CAAE,MAAM,CACbzB,OAAO,CAAE,MAAM,CACf+f,UAAU,CAAE,QAAQ,CACpB3B,cAAc,CAAE,QAClB,CAAE,CAAArN,QAAA,CAAEyO,OAAO,CAACE,CAAC,CAACxZ,IAAI,CAAC,CAACiF,UAAU,CAAM,CAAC,cACrCxT,IAAA,SAAMge,KAAK,CAAE,CAAC/T,KAAK,CAAE4d,OAAO,CAACE,CAAC,CAACxZ,IAAI,CAAC,CAACtE,KAAK,CAAEpB,QAAQ,CAAC,MAAM,CAAEqB,UAAU,CAAC,MAAM,CAAEF,UAAU,CAAE,MAAM,CAAE3B,OAAO,CAAE,OAAO,CAAE,CAAA+Q,QAAA,CACnH7K,IAAI,GAAK,MAAM,CAAG,GAAG,CAAGA,IAAI,GAAK,UAAU,CAAG,GAAG,CAAG,GAAG,CACpD,CAAC,GAbCA,IAcL,CACN,CAAC,CACC,CAAC,cAENvO,IAAA,QAAKge,KAAK,CAAE,CAAEsK,OAAO,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAE/H,SAAS,CAAE,QAAS,CAAE,CAAApH,QAAA,CAC5DoO,SAAS,CAACxX,GAAG,CAACzB,IAAI,EAAIsZ,OAAO,CAACI,CAAC,CAAC1Z,IAAI,CAAC,eACpCrO,KAAA,QAAgB8d,KAAK,CAAE,CAACqI,YAAY,CAAC,KAAK,CAAEhe,OAAO,CAAE,MAAM,CAAE+f,UAAU,CAAE,QAAQ,CAAE3B,cAAc,CAAE,YAAY,CAAE,CAAArN,QAAA,eAC/GpZ,IAAA,SAAMge,KAAK,CAAE,CAAC/T,KAAK,CAAE4d,OAAO,CAACI,CAAC,CAAC1Z,IAAI,CAAC,CAACtE,KAAK,CAAEpB,QAAQ,CAAC,MAAM,CAAEqB,UAAU,CAAC,MAAM,CAAEF,UAAU,CAAE,MAAM,CAAE3B,OAAO,CAAE,OAAO,CAAE,CAAA+Q,QAAA,CACnH7K,IAAI,GAAK,MAAM,CAAG,GAAG,CAAGA,IAAI,GAAK,UAAU,CAAG,GAAG,CAAG,GAAG,CACpD,CAAC,cACPvO,IAAA,QAAKge,KAAK,CAAE,CACVnV,QAAQ,CAAC,MAAM,CACfoB,KAAK,CAAE4d,OAAO,CAACI,CAAC,CAAC1Z,IAAI,CAAC,CAACtE,KAAK,CAC5BC,UAAU,CAAC,MAAM,CACjBue,UAAU,CAAE,KAAK,CACjB3e,KAAK,CAAE,MAAM,CACbzB,OAAO,CAAE,MAAM,CACf+f,UAAU,CAAE,QAAQ,CACpB3B,cAAc,CAAE,QAClB,CAAE,CAAArN,QAAA,CAAEyO,OAAO,CAACI,CAAC,CAAC1Z,IAAI,CAAC,CAACiF,UAAU,CAAM,CAAC,GAb7BjF,IAcL,CACN,CAAC,CACC,CAAC,EACH,CAAC,cACNrO,KAAA,QAAK8d,KAAK,CAAE,CAAE0I,SAAS,CAAE,KAAK,CAAE7d,QAAQ,CAAE,MAAM,CAAEoB,KAAK,CAAE,MAAM,CAAEuW,SAAS,CAAE,QAAS,CAAE,CAAApH,QAAA,EAAC,4BAChF,CAAC,GAAI,CAAAjS,IAAI,CAAC,CAAC,CAACwf,kBAAkB,CAAC,CAAC,CAAC,6BACzC,EAAK,CAAC,EACH,CACN,CAED;AACA,KAAM,CAAAliB,CAAC,CAAG5C,MAAM,CAACiV,UAAU,CAAG,CAAC,CAAG,GAAG,CACrC,KAAM,CAAAnS,CAAC,CAAG9C,MAAM,CAACkV,WAAW,CAAG,CAAC,CAAG,EAAE,CAErC;AACA,GAAIlV,MAAM,CAAC4H,mBAAmB,CAAE,CAC9B5H,MAAM,CAAC4H,mBAAmB,CAACkB,OAAO,CAAGrB,OAAO,CAC9C,CAEA;AACA,GAAIzH,MAAM,CAAC6H,0BAA0B,EAAI7H,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,CAAE,CAClFoR,aAAa,CAACla,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,CAAC,CACxD9I,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,CAAG,IAAI,CAClD,CAEA;AACA,GAAI9I,MAAM,CAAC8H,uBAAuB,CAAE,CAClC9H,MAAM,CAAC8H,uBAAuB,CAAC,CAC7BN,OAAO,CAAE,IAAI,CACbC,OAAO,CAAEA,OAAO,CAChBtB,QAAQ,CAAE,CAAEvD,CAAC,CAAEE,CAAE,CAAC,CAClB4E,OAAO,CAAEA,OAAO,CAChBC,MAAM,CAAE,CAAA2c,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE3c,MAAM,GAAI,EAC/B,CAAC,CAAC,CAEF;AACA,GAAI3H,MAAM,CAAC6H,0BAA0B,CAAE,CACrC7H,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,CAAG4R,WAAW,CAAC,IAAM,CAC5D1a,MAAM,CAACyS,qBAAqB,CAAChL,OAAO,CAAC,CACvC,CAAC,CAAE,IAAI,CAAC,CACV,CAEA,MAAO,KAAI,CACb,CAAC,IAAM,CACL7G,OAAO,CAACsB,KAAK,CAAC,8BAA8B,CAAC,CAC7C,MAAO,MAAK,CACd,CACF,CAAE,MAAOA,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA,KAAM,CAAAmhB,yBAAyB,CAAIF,MAAM,EAAK,CAC5C,GAAI,CAAAra,OAAO,CAAGqa,MAAM,CAEpB;AACA,GAAIra,OAAO,EAAIA,OAAO,CAACiT,QAAQ,EAAIjT,OAAO,CAACiT,QAAQ,CAACrP,IAAI,GAAK,cAAc,CAAE,CAC3E9L,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEiI,OAAO,CAACiC,IAAI,EAAI,KAAK,CAAC,CAChD,MAAO,CAAAjC,OAAO,CAChB,CAEA;AACA,MAAOA,OAAO,EAAIA,OAAO,CAAC4X,MAAM,CAAE,CAChC5X,OAAO,CAAGA,OAAO,CAAC4X,MAAM,CACxB,GAAI5X,OAAO,CAACiT,QAAQ,EAAIjT,OAAO,CAACiT,QAAQ,CAACrP,IAAI,GAAK,cAAc,CAAE,CAChE9L,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEiI,OAAO,CAACiC,IAAI,EAAI,KAAK,CAAC,CAChD,MAAO,CAAAjC,OAAO,CAChB,CACF,CAEA,MAAO,KAAI,CACb,CAAC,CAED;AACA9I,MAAM,CAAC6mB,kBAAkB,CAAG,CAACjkB,CAAC,CAAEE,CAAC,GAAK,CACpC,GAAI,CACFlC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAE+B,CAAC,CAAEE,CAAC,CAAC,CAEnC;AACA,KAAM,CAAAya,MAAM,CAAGC,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAC/C,GAAI,CAAChC,MAAM,CAAE,CACX3c,OAAO,CAACsB,KAAK,CAAC,sBAAsB,CAAC,CACrC,MAAO,MAAK,CACd,CAEA;AACA,GAAI,CAAC7C,KAAK,EAAI,CAAC8H,SAAS,CAAC2B,OAAO,CAAE,CAChClI,OAAO,CAACsB,KAAK,CAAC,iBAAiB,CAAC,CAChC,MAAO,MAAK,CACd,CAEA;AACA,GAAIU,CAAC,GAAKgO,SAAS,EAAI9N,CAAC,GAAK8N,SAAS,CAAE,CACtChO,CAAC,CAAG5C,MAAM,CAACiV,UAAU,CAAG,CAAC,CACzBnS,CAAC,CAAG9C,MAAM,CAACkV,WAAW,CAAG,CAAC,CAC5B,CAEA;AACA,KAAM,CAAA8M,IAAI,CAAGzE,MAAM,CAAC0E,qBAAqB,CAAC,CAAC,CAC3C,KAAM,CAAAC,MAAM,CAAI,CAACtf,CAAC,CAAGof,IAAI,CAAC3b,IAAI,EAAIkX,MAAM,CAAC4E,WAAW,CAAI,CAAC,CAAG,CAAC,CAC7D,KAAM,CAAAC,MAAM,CAAG,EAAE,CAACtf,CAAC,CAAGkf,IAAI,CAACha,GAAG,EAAIuV,MAAM,CAAC8E,YAAY,CAAC,CAAG,CAAC,CAAG,CAAC,CAE9DzhB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEqhB,MAAM,CAAEE,MAAM,CAAC,CAErC;AACA,KAAM,CAAAE,SAAS,CAAG,GAAI,CAAA9kB,KAAK,CAAC+kB,SAAS,CAAC,CAAC,CACvCD,SAAS,CAACnF,MAAM,CAACqF,MAAM,CAACC,SAAS,CAAG,CAAC,CACrCH,SAAS,CAACnF,MAAM,CAACuF,IAAI,CAACD,SAAS,CAAG,CAAC,CAEnC,KAAM,CAAAE,WAAW,CAAG,GAAI,CAAAnlB,KAAK,CAAColB,OAAO,CAACV,MAAM,CAAEE,MAAM,CAAC,CACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,CAAExb,SAAS,CAAC2B,OAAO,CAAC,CAEvD;AACA,KAAM,CAAAga,mBAAmB,CAAG,EAAE,CAC9B7hB,gBAAgB,CAACqL,OAAO,CAAC,CAACiO,QAAQ,CAAE9S,OAAO,GAAK,CAC9C,GAAI8S,QAAQ,CAACpN,KAAK,CAAE,CAClB2V,mBAAmB,CAAChR,IAAI,CAACyI,QAAQ,CAACpN,KAAK,CAAC,CACxCvM,OAAO,CAACC,GAAG,CAAC,SAAS4G,OAAO,QAAQ,CAAC,CACvC,CACF,CAAC,CAAC,CAEF;AACA7G,OAAO,CAACC,GAAG,CAAC,QAAQiiB,mBAAmB,CAACtL,MAAM,YAAY,CAAC,CAC3D,KAAM,CAAAsP,YAAY,CAAGxE,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,CAAE,IAAI,CAAC,CAE1E,GAAIgE,YAAY,CAACtP,MAAM,CAAG,CAAC,CAAE,CAC3B5W,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1BimB,YAAY,CAACxa,OAAO,CAAC,CAAC2W,SAAS,CAAEnY,CAAC,GAAK,CACrClK,OAAO,CAACC,GAAG,CAAC,MAAMiK,CAAC,GAAG,CAAEmY,SAAS,CAACE,MAAM,CAACpY,IAAI,EAAI,KAAK,CAC1C,KAAK,CAAEkY,SAAS,CAAC5f,QAAQ,CACzB,WAAW,CAAE4f,SAAS,CAACE,MAAM,CAAChd,QAAQ,CAACoF,OAAO,CAAC,CAAC,CAChD,WAAW,CAAE0X,SAAS,CAACE,MAAM,CAACpH,QAAQ,CAAC,CAEnD;AACA,KAAM,CAAAqH,GAAG,CAAGC,yBAAyB,CAACJ,SAAS,CAACE,MAAM,CAAC,CACvD,GAAIC,GAAG,EAAIA,GAAG,CAACrH,QAAQ,EAAIqH,GAAG,CAACrH,QAAQ,CAACrP,IAAI,GAAK,cAAc,CAAE,CAC/D9L,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEuiB,GAAG,CAACrH,QAAQ,CAACtU,OAAO,CAAC,CAC/C,CACF,CAAC,CAAC,CAEF,MAAO,KAAI,CACb,CAEA;AACA7G,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAC7B,KAAM,CAAAkmB,eAAe,CAAGzE,SAAS,CAACU,gBAAgB,CAAC3jB,KAAK,CAACkY,QAAQ,CAAE,IAAI,CAAC,CAExE3W,OAAO,CAACC,GAAG,CAAC,WAAWkmB,eAAe,CAACvP,MAAM,MAAM,CAAC,CACpDuP,eAAe,CAACza,OAAO,CAAC,CAAC2W,SAAS,CAAEnY,CAAC,GAAK,CACxC,KAAM,CAAAsY,GAAG,CAAGH,SAAS,CAACE,MAAM,CAC5BviB,OAAO,CAACC,GAAG,CAAC,QAAQiK,CAAC,GAAG,CAAEsY,GAAG,CAACrY,IAAI,EAAI,KAAK,CAC/B,KAAK,CAAEqY,GAAG,CAAC1W,IAAI,CACf,KAAK,CAAE0W,GAAG,CAACjd,QAAQ,CAACoF,OAAO,CAAC,CAAC,CAC7B,KAAK,CAAE0X,SAAS,CAAC5f,QAAQ,CACzB,WAAW,CAAE+f,GAAG,CAACrH,QAAQ,CAAC,CACxC,CAAC,CAAC,CAEF;AACAnb,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3B,GAAI,CAAAmmB,YAAY,CAAG,CAAC,CAEpB/lB,gBAAgB,CAACqL,OAAO,CAAC,CAACiO,QAAQ,CAAE9S,OAAO,GAAK,CAC9C,GAAI8S,QAAQ,CAACpN,KAAK,CAAE,KAAA8Z,qBAAA,CAClB;AACA,GAAI,CAAAC,SAAS,CAAG3M,QAAQ,CAACpN,KAAK,CAAC3F,OAAO,CACtC,GAAI,CAAA2f,cAAc,CAAG,IAAI,CAEzB;AACA,KAAM,CAAA1D,QAAQ,CAAG,GAAI,CAAAjmB,KAAK,CAACkG,OAAO,CAAC,CAAC,CACpC6W,QAAQ,CAACpN,KAAK,CAACuW,gBAAgB,CAACD,QAAQ,CAAC,CAEzC;AACA,KAAM,CAAA2D,gBAAgB,CAAG3D,QAAQ,CAACngB,UAAU,CAAC6D,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAC,CAExE;AACA,KAAM,CAAAwd,SAAS,CAAGF,QAAQ,CAAC/gB,KAAK,CAAC,CAAC,CAACkhB,OAAO,CAACzc,SAAS,CAAC2B,OAAO,CAAC,CAC7D,GAAIhF,IAAI,CAACK,GAAG,CAACwf,SAAS,CAAC/gB,CAAC,CAAC,CAAG,CAAC,EAAIkB,IAAI,CAACK,GAAG,CAACwf,SAAS,CAAC7gB,CAAC,CAAC,CAAG,CAAC,EAAI6gB,SAAS,CAAC3gB,CAAC,CAAG,CAAC,CAAC,EAAI2gB,SAAS,CAAC3gB,CAAC,CAAG,CAAC,CAAE,CACjGmkB,cAAc,CAAG,KAAK,CACxB,CAEA,GAAID,SAAS,CAAE,CACbF,YAAY,EAAE,CAChB,CAEApmB,OAAO,CAACC,GAAG,CAAC,OAAO4G,OAAO,GAAG,CAAE,CAC7B4f,EAAE,CAAE,EAAAJ,qBAAA,CAAA1M,QAAQ,CAAC3P,YAAY,UAAAqc,qBAAA,iBAArBA,qBAAA,CAAuBlc,IAAI,GAAI,IAAI,CACvCuc,GAAG,CAAEJ,SAAS,CACdK,KAAK,CAAEJ,cAAc,CACrBK,IAAI,CAAE/D,QAAQ,CAAClY,OAAO,CAAC,CAAC,CACxBkc,IAAI,CAAE,CAAC9D,SAAS,CAAC/gB,CAAC,CAAE+gB,SAAS,CAAC7gB,CAAC,CAAE6gB,SAAS,CAAC3gB,CAAC,CAAC,CAC7C0kB,MAAM,CAAEN,gBACV,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEFxmB,OAAO,CAACC,GAAG,CAAC,MAAMmmB,YAAY,IAAI/lB,gBAAgB,CAACgb,IAAI,WAAW,CAAC,CAEnE;AACA,MAAO,CAAA8K,eAAe,CAACvP,MAAM,CAAG,CAAC,CACnC,CAAE,MAAOtV,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B,MAAO,MAAK,CACd,CACF,CAAC,CAID;AACA,KAAM,CAAAgQ,wBAAwB,CAAGA,CAACR,YAAY,CAAEG,SAAS,GAAK,KAAA8V,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAC5D,GAAI,CAACnW,YAAY,EAAI,CAACA,YAAY,CAACvE,KAAK,EAAI,CAAC0E,SAAS,CAAE,CACtD,OACF,CAEA;AACA,KAAM,CAAAiW,cAAc,CAAG,EAAE,CACzBpW,YAAY,CAACvE,KAAK,CAACiC,QAAQ,CAACC,KAAK,EAAI,CACnC,GAAIA,KAAK,CAAC0M,QAAQ,EAAI1M,KAAK,CAAC0M,QAAQ,CAACgM,OAAO,CAAE,CAC5CD,cAAc,CAAChW,IAAI,CAACzC,KAAK,CAAC,CAC5B,CACF,CAAC,CAAC,CAEFyY,cAAc,CAACxb,OAAO,CAAC8X,KAAK,EAAI,CAC9B1S,YAAY,CAACvE,KAAK,CAACmB,MAAM,CAAC8V,KAAK,CAAC,CAClC,CAAC,CAAC,CAEF;AACA,GAAI,CAAAO,UAAU,CACd,OAAO9S,SAAS,CAACH,YAAY,EAC3B,IAAK,GAAG,CACNiT,UAAU,CAAG,QAAQ,CAAE;AACvB,MACF,IAAK,GAAG,CACNA,UAAU,CAAG,QAAQ,CAAE;AACvB,MACF,IAAK,GAAG,CACR,QACEA,UAAU,CAAG,QAAQ,CAAE;AACvB,MACJ,CAEA;AACA,KAAM,CAAApD,aAAa,CAAG,GAAI,CAAA/jB,KAAK,CAACme,cAAc,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CACzD,KAAM,CAAA6F,aAAa,CAAG,GAAI,CAAAhkB,KAAK,CAAC2d,iBAAiB,CAAC,CAChD/S,KAAK,CAAEuc,UAAU,CACjBlV,QAAQ,CAAEkV,UAAU,CACpBqD,iBAAiB,CAAE,CACrB,CAAC,CAAC,CACF,KAAM,CAAAvG,SAAS,CAAG,GAAI,CAAAjkB,KAAK,CAAC4d,IAAI,CAACmG,aAAa,CAAEC,aAAa,CAAC,CAC9DC,SAAS,CAACtb,QAAQ,CAAClD,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAAE;AAClCwe,SAAS,CAAC1F,QAAQ,CAAG,CACnBgM,OAAO,CAAE,IAAI,CACbrb,IAAI,CAAE,cAAc,CACpBjF,OAAO,EAAAkgB,qBAAA,CAAEjW,YAAY,CAAC9G,YAAY,UAAA+c,qBAAA,iBAAzBA,qBAAA,CAA2BlgB,OAAO,CAC3C0J,OAAO,CAAEU,SAAS,CAACV,OAAO,CAC1BE,SAAS,CAAEQ,SAAS,CAACR,SAAS,CAC9BM,UAAU,CAAEE,SAAS,CAACF,UACxB,CAAC,CAED;AACA,KAAM,CAAAyS,KAAK,CAAG,GAAI,CAAA5mB,KAAK,CAACyqB,UAAU,CAACtD,UAAU,CAAE,CAAC,CAAE,EAAE,CAAC,CACrDP,KAAK,CAACje,QAAQ,CAAClD,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAC5BmhB,KAAK,CAACrI,QAAQ,CAAG,CAAEgM,OAAO,CAAE,IAAK,CAAC,CAElC;AACArW,YAAY,CAACvE,KAAK,CAACW,GAAG,CAAC2T,SAAS,CAAC,CACjC/P,YAAY,CAACvE,KAAK,CAACW,GAAG,CAACsW,KAAK,CAAC,CAE7BxjB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAA+mB,sBAAA,CAAAlW,YAAY,CAAC9G,YAAY,UAAAgd,sBAAA,iBAAzBA,sBAAA,CAA2B7c,IAAI,KAAA8c,sBAAA,CAAInW,YAAY,CAAC9G,YAAY,UAAAid,sBAAA,iBAAzBA,sBAAA,CAA2BpgB,OAAO,cAAaoK,SAAS,CAACH,YAAY,WAAWG,SAAS,CAACF,UAAU,GAAG,CAAC,CACjK,CAAC,CAED,cAAe,CAAAtN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}