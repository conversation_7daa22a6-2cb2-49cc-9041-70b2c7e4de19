{"ast": null, "code": "import * as React from 'react';\nexport default function useEvent(callback) {\n  var fnRef = React.useRef();\n  fnRef.current = callback;\n  var memoFn = React.useCallback(function () {\n    var _fnRef$current;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return (_fnRef$current = fnRef.current) === null || _fnRef$current === void 0 ? void 0 : _fnRef$current.call.apply(_fnRef$current, [fnRef].concat(args));\n  }, []);\n  return memoFn;\n}", "map": {"version": 3, "names": ["React", "useEvent", "callback", "fnRef", "useRef", "current", "memoFn", "useCallback", "_fnRef$current", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-util/es/hooks/useEvent.js"], "sourcesContent": ["import * as React from 'react';\nexport default function useEvent(callback) {\n  var fnRef = React.useRef();\n  fnRef.current = callback;\n  var memoFn = React.useCallback(function () {\n    var _fnRef$current;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return (_fnRef$current = fnRef.current) === null || _fnRef$current === void 0 ? void 0 : _fnRef$current.call.apply(_fnRef$current, [fnRef].concat(args));\n  }, []);\n  return memoFn;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,QAAQA,CAACC,QAAQ,EAAE;EACzC,IAAIC,KAAK,GAAGH,KAAK,CAACI,MAAM,CAAC,CAAC;EAC1BD,KAAK,CAACE,OAAO,GAAGH,QAAQ;EACxB,IAAII,MAAM,GAAGN,KAAK,CAACO,WAAW,CAAC,YAAY;IACzC,IAAIC,cAAc;IAClB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACA,OAAO,CAACN,cAAc,GAAGL,KAAK,CAACE,OAAO,MAAM,IAAI,IAAIG,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACO,IAAI,CAACC,KAAK,CAACR,cAAc,EAAE,CAACL,KAAK,CAAC,CAACc,MAAM,CAACL,IAAI,CAAC,CAAC;EAC1J,CAAC,EAAE,EAAE,CAAC;EACN,OAAON,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}