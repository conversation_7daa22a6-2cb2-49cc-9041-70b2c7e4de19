{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel = null; // 存储原始模型数据\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n  bsm: 'changli/cloud/v2x/obu/bsm',\n  rsm: 'changli/cloud/v2x/rsu/rsm',\n  scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',\n  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat' // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (!lastPosition) {\n      lastPosition = newPos.clone();\n      return newPos;\n    }\n    const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n    const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n    const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n    lastPosition.set(filteredX, filteredY, filteredZ);\n    return lastPosition.clone();\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  const lastPos = vehicleLastPositions.get(vehicleId);\n\n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n\n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n\n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (lastRotation === null) {\n      lastRotation = newRotation;\n      return newRotation;\n    }\n\n    // 处理角度跳变（从360度到0度或反之）\n    let diff = newRotation - lastRotation;\n    if (diff > Math.PI) diff -= 2 * Math.PI;\n    if (diff < -Math.PI) diff += 2 * Math.PI;\n    const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n    lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const lastRot = vehicleLastRotations.get(vehicleId);\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n\n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\nconst CampusModel = ({\n  className,\n  onCurrentRSUChange,\n  selectedRSUs\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n\n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n\n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = useRef(new Map()); // 存储所有行人的动画混合器\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,\n    // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null,\n    phases: []\n  });\n\n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n\n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n\n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n\n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({\n    topic: '',\n    content: ''\n  });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n\n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n\n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n\n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos).to({\n          x: 0,\n          y: 300,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        }).start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp).to({\n          x: 0,\n          y: 1,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        }).start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n\n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget).to({\n          x: 0,\n          y: 0,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        }).start();\n\n        // 启用控制器\n        controls.enabled = true;\n\n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x + 50, 70, -modelCoords.y + 50);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        var _payload$data;\n        // console.log('收到RSM消息:', payload);\n\n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n\n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n          return;\n        }\n\n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n\n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n\n        const participants = ((_payload$data = payload.data) === null || _payload$data === void 0 ? void 0 : _payload$data.participants) || [];\n        const rsuid = payload.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id = deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = preloadedModel.clone();\n              // 根据类型调整高度和缩放\n              const height = type === '3' ? 2.0 : 1.0;\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n\n              // 如果是行人类型，设置缩放和创建动画\n              if (type === '3') {\n                newModel.scale.set(0.05, 0.05, 0.05);\n                // 创建动画混合器\n                const mixer = new THREE.AnimationMixer(newModel);\n\n                // 播放行走动画peopleBaseModel\n                // const action = mixer.clipAction(peopleAnimations.current[0]);\n                // const action = mixer.clipAction(peopleBaseModel.animations[0]);\n                // action.play();\n\n                // peopleAnimationMixers.current.set(id, mixer);\n\n                console.log('找到行人动画tttt:', peopleBaseModel.animations.length, '个');\n                if (peopleBaseModel.animations.length > 0) {\n                  peopleBaseModel.animations.forEach(clip => {\n                    const action = mixer.clipAction(clip);\n                    action.play();\n                  });\n                  // 保存动画混合器\n                  peopleAnimationMixers.current.set(id, mixer);\n                }\n              }\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n\n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n\n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n\n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n\n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid,\n          // 直接传递车辆ID\n          data: {\n            // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n\n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n\n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n\n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n\n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n\n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n\n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n\n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n\n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n\n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n\n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          scene.add(newVehicleModel);\n\n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1',\n            // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n\n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position).to({\n            y: 0.5\n          }, 500).easing(TWEEN.Easing.Quadratic.Out).start();\n\n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({\n                opacity: 0.6\n              }).to({\n                opacity: 1.0\n              }, 500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function () {\n                child.material.opacity = this.opacity;\n                child.material.needsUpdate = true;\n              }).start();\n            }\n          });\n\n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({\n            opacity: 0.6\n          }).to({\n            opacity: 1.0\n          }, 500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function () {\n            speedLabel.material.opacity = this.opacity;\n            speedLabel.material.needsUpdate = true;\n          }).start();\n\n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n\n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n\n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n\n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n\n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n\n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 2000; // 增加到10秒，避免频繁清理造成闪烁\n\n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n\n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1;\n            modelData.model.traverse(child => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n\n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n\n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            modelData.model.traverse(child => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n\n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n\n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n\n        try {\n          const payload = JSON.parse(message);\n\n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n\n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n\n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n\n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n\n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n\n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? getDirectionFromCode(phase.trafficDirec) : getPhaseDirection(phaseId);\n\n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n\n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n\n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n\n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n\n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n\n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n\n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n\n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && (window.currentPopoverIdRef.current === modelKey || window.currentPopoverIdRef.current === String(modelKey) || window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n\n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n      }\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(parseFloat(rsiData.posLong), parseFloat(rsiData.posLat));\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          switch (eventType) {\n            case '401':\n              // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':\n              // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':\n              // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':\n              // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':\n              // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002':\n              // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':\n              // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n\n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n\n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch (sceneType) {\n          case '2':\n            // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':\n            // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':\n            // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':\n            // 限速提醒\n            const speedLimit = sceneData.eventData1; // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':\n            // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':\n            // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':\n            // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':\n            // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':\n            // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':\n            // 信号灯优先\n            const priorityType = sceneData.eventData1; // 优先类型\n            const duration = sceneData.eventData2; // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n\n        // 检查scene是否初始化\n        if (scene) {\n          scene.add(model);\n\n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } else {\n          console.error('无法添加模型：场景未初始化');\n        }\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画\n      const currentTime = Date.now();\n      // const deltaTime = (currentTime - prevAnimationTimeRef.current) * 10;\n      const deltaTime = clock.getDelta();\n      prevAnimationTimeRef.current = currentTime;\n      peopleAnimationMixers.current.forEach(mixer => {\n        mixer.update(deltaTime);\n      });\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 200, -50 * Math.sin(adjustedRotation));\n\n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n\n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n\n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n\n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 路口视角模式\n        controls.update();\n      }\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n\n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n\n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach(lightObj => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n\n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n\n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n\n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n\n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n\n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n\n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {\n          // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n\n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = event => {\n        if (scene && cameraRef.current) {\n          console.log('检测到点击事件', event.clientX, event.clientY);\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        } else {\n          console.warn('点击事件处理失败: scene或camera未初始化');\n        }\n      };\n\n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n\n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n\n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({\n      color: 0x333333\n    });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n\n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({\n      color: 0x666666\n    });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n\n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          //\n          transparent: false,\n          opacity: 0.1,\n          // 几乎透明\n          depthWrite: false\n        });\n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0); // 放在红绿灯位置\n\n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n\n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500); // 延迟略长于debugTrafficLights\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n\n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n\n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        const firstIntersection = intersectionsData.intersections[0];\n        console.log('自动选择第一个路口:', firstIntersection.name);\n\n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(firstIntersection.name);\n        }, 2000);\n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u533A\\u57DF\\u9009\\u62E9\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1893,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u533A\\u57DF\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      options: intersectionsData.intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      },\n      value: selectedIntersection ? selectedIntersection.name : undefined\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1894,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1910,\n      columnNumber: 7\n    }, this), trafficLightPopover.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${trafficLightPopover.position.x}px`,\n        top: `${trafficLightPopover.position.y}px`,\n        transform: 'translate(-50%, -100%)',\n        zIndex: 1003,\n        backgroundColor: 'rgba(0, 0, 0, 0.85)',\n        color: 'white',\n        borderRadius: '4px',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n        padding: '0',\n        maxWidth: '240px',\n        // 缩小最大宽度\n        fontSize: '12px' // 缩小字体\n      },\n      children: [trafficLightPopover.content, /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          position: 'absolute',\n          top: '0px',\n          right: '0px',\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          fontSize: '12px',\n          cursor: 'pointer',\n          padding: '2px 6px'\n        },\n        onClick: () => handleClosePopover(setTrafficLightPopover),\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1931,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1914,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1951,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1961,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1950,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"3e67OuhmP6/GO8NQdMA1apTVO28=\");\n_c = CampusModel;\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 16,\n    // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    backgroundColor: parameters.backgroundColor || {\n      r: 255,\n      g: 255,\n      b: 255,\n      a: 0.8\n    },\n    textColor: parameters.textColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n\n  // 设置字体\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n\n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n\n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  canvas.width = width;\n  canvas.height = height;\n\n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n\n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n\n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n\n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n\n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n\n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n\n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n\n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n\n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = {\n    text: text,\n    params: params\n  };\n  return sprite;\n}\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    try {\n      const [trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`), loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`)]);\n\n      // 处理机动车模型\n      console.log('加载车辆模型...');\n      preloadedVehicleModel = vehicleGltf.scene;\n      preloadedVehicleModel.traverse(child => {\n        if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n            color: 0xff0000,\n            // 0xff0000, //红色 0xffffff,白色\n            metalness: 0.2,\n            roughness: 0.1,\n            envMapIntensity: 1.0\n          });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n        }\n      });\n      console.log('加载非机动车模型...');\n      // 处理非机动车模型\n      preloadedCyclistModel = cyclistGltf.scene;\n      // 设置非机动车模型的缩放\n      preloadedCyclistModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedCyclistModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n      console.log('加载行人模型...');\n      // 处理行人模型\n      preloadedPeopleModel = peopleGltf.scene;\n      // 设置行人模型的缩放\n      // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n      // 保持原始材质\n      preloadedPeopleModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n\n      // 保存行人动画数据\n      console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n      if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n        console.log('找到行人动画:', peopleGltf.animations.length, '个');\n        peopleBaseModel = peopleGltf;\n      } else {\n        console.warn('行人模型没有包含动画数据');\n      }\n      console.log('加载红绿灯模型...');\n\n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      console.log('红绿灯模型：', preloadedTrafficLightModel);\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n\n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n\n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = type => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  try {\n    // 创建一个新的警告标记\n    const sprite = createTextSprite(text);\n    sprite.position.set(position.x, 10, -position.y); // 设置位置，稍微升高以便可见\n\n    // 为标记添加一个定时器，在1秒后自动移除\n    setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n        scene.remove(sprite);\n      }\n    }, 100);\n\n    // 将标记添加到场景中\n    scene.add(sprite);\n\n    // console.log('添加场景事件标记:', {\n    //   位置: position,\n    //   文本: text,\n    //   颜色: color\n    // });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = converterInstance => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n\n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach(lightObj => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型 - 只为hasTrafficLight为true的路口创建红绿灯\n  intersectionsData.intersections.forEach(intersection => {\n    // 检查路口是否有红绿灯属性，如果没有或为false，则跳过\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n\n    // 确认路口有经纬度和ID\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n\n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n\n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n\n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n\n        // *** 重要：设置为可被交互 ***\n        trafficLightModel.traverse(child => {\n          // 设置所有子对象可被点击\n          if (child.isMesh) {\n            // 确保材质能够被射线检测到\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n\n            // 设置较高的渲染顺序\n            child.renderOrder = 100;\n          }\n        });\n\n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n\n        // // 添加一个专门用于点击的大型碰撞体\n        // const colliderGeometry = new THREE.SphereGeometry(5, 12, 12);\n        // const colliderMaterial = new THREE.MeshBasicMaterial({\n        //   color: 0xff00ff,\n        //   transparent: true,\n        //   opacity: 0.0,  // 完全透明\n        //   depthWrite: false\n        // });\n\n        // const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n        // collider.name = `交通灯碰撞体-${intersection.name}`;\n        // collider.userData = {\n        //   type: 'trafficLight',\n        //   interId: intersection.interId,\n        //   name: intersection.name,\n        //   isCollider: true\n        // };\n\n        // trafficLightModel.add(collider);\n\n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n\n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = converterInstance => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({\n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n\n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n\n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n\n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,\n    // 完全透明\n    depthWrite: false\n  });\n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  trafficLightModel.add(collider);\n\n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n\n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n\n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: 0xFF0000\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n\n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  trafficLightModel.add(lightMesh);\n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = phaseId => {\n  switch (phaseId) {\n    case '1':\n      return '北进口左转';\n    case '2':\n      return '北进口直行';\n    case '3':\n      return '北进口右转';\n    case '5':\n      return '东进口左转';\n    case '6':\n      return '东进口直行';\n    case '7':\n      return '东进口右转';\n    case '9':\n      return '南进口左转';\n    case '10':\n      return '南进口直行';\n    case '11':\n      return '南进口右转';\n    case '13':\n      return '西进口左转';\n    case '14':\n      return '西进口直行';\n    case '15':\n      return '西进口右转';\n    default:\n      return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = dirCode => {\n  switch (dirCode) {\n    case 'N':\n      return '北向南';\n    case 'S':\n      return '南向北';\n    case 'E':\n      return '东向西';\n    case 'W':\n      return '西向东';\n    case 'NE':\n      return '东北向西南';\n    case 'NW':\n      return '西北向东南';\n    case 'SE':\n      return '东南向西北';\n    case 'SW':\n      return '西南向东北';\n    default:\n      return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n\n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = (event.clientX - rect.left) / container.clientWidth * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n\n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Line.threshold = 1;\n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  console.log('鼠标点击位置:', mouseX, mouseY);\n\n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n\n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'userData:', intersect.object.userData);\n    });\n\n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n\n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n\n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n\n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  console.log('射线检测到的对象数量:', intersects.length);\n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', 'userData:', obj.userData, '距离:', intersect.distance);\n    });\n\n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        console.log('检测到红绿灯点击, 路口ID:', interId);\n\n        // 显示弹窗\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n\n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n\n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.1; // 设置一个阈值，只有距离小于此值的才会被选中\n\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n\n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n\n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      console.log(`路口 ${interId} 的距离:`, distance);\n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = {\n          interId,\n          distance\n        };\n      }\n    }\n  });\n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n\n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  console.log('未检测到任何红绿灯点击');\n\n  // 如果用户点击了其他任何地方，关闭现有的弹窗\n  if (setPopoverState) {\n    setPopoverState(prev => {\n      if (prev.visible) {\n        return {\n          ...prev,\n          visible: false\n        };\n      }\n      return prev;\n    });\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = setPopoverState => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n\n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n\n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({\n    ...prev,\n    visible: false\n  }));\n  console.log('弹窗已关闭');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = interId => {\n  try {\n    var _document$querySelect, _document$querySelect2;\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n\n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      return false;\n    }\n\n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n\n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 创建弹出窗口内容\n    let content;\n    if (stateInfo && stateInfo.phases) {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '220px',\n          maxHeight: '300px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          },\n          children: [intersection.name, \" (ID: \", interId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2740,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                break;\n            }\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '6px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '4px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: getPhaseDirection(phase.phaseId)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2766,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2770,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 3px',\n                    borderRadius: '2px'\n                  },\n                  children: phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2771,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2769,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2782,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2783,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2781,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2759,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2749,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '6px',\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2789,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2739,\n        columnNumber: 9\n      }, this);\n    } else {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          maxWidth: '200px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '8px'\n          },\n          children: intersection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2797,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u8DEF\\u53E3ID: \", interId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2798,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2799,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2796,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2;\n    const centerY = window.innerHeight / 2;\n\n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = (_document$querySelect = document.querySelector('#root')) === null || _document$querySelect === void 0 ? void 0 : (_document$querySelect2 = _document$querySelect.__REACT_INSTANCE) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.setTrafficLightPopover;\n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: {\n          x: centerX,\n          y: centerY\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      document.body.appendChild(popover);\n\n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  return list;\n};\n\n// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n\n//   console.log('开始调试红绿灯模型...');\n\n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n\n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n\n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n\n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n\n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n\n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n\n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n\n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n\n//       // 添加到场景\n//       scene.add(highlightMesh);\n\n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n\n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n\n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n\n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = interId => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    console.log('当前trafficLightStates大小:', trafficLightStates.size);\n\n    // 如果没有指定ID，则使用第一个红绿灯\n    if (!interId && trafficLightsMap.size > 0) {\n      interId = String(Array.from(trafficLightsMap.keys())[0]);\n      console.log('未指定ID，使用第一个红绿灯ID:', interId);\n    }\n\n    // 输出所有可用的红绿灯ID用于调试\n    console.log('所有可用的红绿灯ID:');\n    trafficLightsMap.forEach((light, id) => {\n      var _light$intersection;\n      console.log(`- ${id} (${typeof id}): ${((_light$intersection = light.intersection) === null || _light$intersection === void 0 ? void 0 : _light$intersection.name) || '未知'}`);\n    });\n\n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n\n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n\n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n\n    // 创建更新函数\n    const updateTrafficLightPopover = () => {\n      var _window$currentPopove;\n      // 确保当前弹窗仍然可见\n      if (!window._setTrafficLightPopover) return;\n      const currentId = (_window$currentPopove = window.currentPopoverIdRef) === null || _window$currentPopove === void 0 ? void 0 : _window$currentPopove.current;\n      if (!currentId) return;\n\n      // 重新获取最新的状态信息\n      let stateInfo = trafficLightStates.get(currentId);\n      if (!stateInfo && typeof currentId === 'string') {\n        // 尝试使用数字ID\n        stateInfo = trafficLightStates.get(parseInt(currentId));\n      } else if (!stateInfo && typeof currentId === 'number') {\n        // 尝试使用字符串ID\n        stateInfo = trafficLightStates.get(String(currentId));\n      }\n      if (!stateInfo || !stateInfo.phases || stateInfo.phases.length === 0) {\n        console.log(`路口ID ${currentId} 没有状态信息，跳过更新`);\n        return;\n      }\n\n      // 获取交通灯信息\n      const intersectionLight = trafficLightsMap.get(currentId) || (typeof currentId === 'string' ? trafficLightsMap.get(parseInt(currentId)) : trafficLightsMap.get(String(currentId)));\n      if (!intersectionLight) {\n        console.error(`找不到路口ID ${currentId} 的红绿灯信息，无法更新弹窗`);\n        return;\n      }\n      const intersection = intersectionLight.intersection;\n\n      // 创建更新的弹窗内容\n      const content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '220px',\n          maxHeight: '300px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          },\n          children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", currentId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3068,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            let lightText;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                lightText = '绿灯';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                lightText = '黄灯';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                lightText = '红灯';\n                break;\n            }\n            const direction = getPhaseDirection(phase.phaseId);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '6px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '4px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: [direction, \" (\\u76F8\\u4F4DID: \", phase.phaseId, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3108,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3112,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 3px',\n                    borderRadius: '2px'\n                  },\n                  children: lightText\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3113,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3111,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3124,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3125,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3123,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3101,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3077,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '6px',\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date(stateInfo.updateTime).toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3067,\n        columnNumber: 9\n      }, this);\n\n      // 更新弹窗内容\n      window._setTrafficLightPopover(prev => ({\n        ...prev,\n        content: content,\n        phases: stateInfo.phases\n      }));\n      console.log(`已更新路口 ${(intersection === null || intersection === void 0 ? void 0 : intersection.name) || currentId} 的红绿灯状态弹窗`);\n    };\n\n    // 先执行一次初始更新\n    const updateInitialTrafficLightPopover = () => {\n      // 同样，查找红绿灯状态信息时也尝试字符串和数字两种类型\n      let stateInfo = trafficLightStates.get(interId);\n      if (!stateInfo) {\n        // 尝试使用数字ID\n        const numericId = parseInt(interId);\n        stateInfo = trafficLightStates.get(numericId);\n        if (stateInfo) {\n          console.log(`使用数字ID ${numericId} 找到了红绿灯状态信息`);\n        }\n      }\n      console.log(`路口ID ${interId} 的状态信息:`, stateInfo);\n      const intersection = trafficLight.intersection;\n      console.log('路口信息:', intersection);\n\n      // 创建弹窗内容\n      let content;\n      if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n        // 添加一个检查相位数据的打印\n        console.log('相位数据详情:');\n        stateInfo.phases.forEach((phase, index) => {\n          console.log(`相位 ${index + 1}: ID=${phase.phaseId}, 状态=${phase.trafficLight}, 剩余时间=${phase.remainTime}`);\n        });\n        content = /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '8px',\n            width: '220px',\n            maxHeight: '300px',\n            overflowY: 'auto'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            },\n            children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", interId, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              let lightText;\n              switch (phase.trafficLight) {\n                case 'G':\n                  lightColor = '#00ff00';\n                  lightText = '绿灯';\n                  break;\n                case 'Y':\n                  lightColor = '#ffff00';\n                  lightText = '黄灯';\n                  break;\n                case 'R':\n                default:\n                  lightColor = '#ff0000';\n                  lightText = '红灯';\n                  break;\n              }\n              const direction = getPhaseDirection(phase.phaseId);\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '6px',\n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [direction, \" (\\u76F8\\u4F4DID: \", phase.phaseId, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3218,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u706F\\u8272: \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3222,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: lightColor,\n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    },\n                    children: lightText\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3223,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3221,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u5012\\u8BA1\\u65F6: \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3234,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontWeight: 'bold'\n                    },\n                    children: [phase.remainTime, \" \\u79D2\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3235,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3233,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3211,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '6px',\n              fontSize: '10px',\n              color: '#888'\n            },\n            children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date(stateInfo.updateTime).toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3177,\n          columnNumber: 11\n        }, this);\n      } else {\n        // 如果没有状态信息，创建一个示例内容\n        content = /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '8px',\n            width: '220px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            },\n            children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", interId, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#ff4d4f',\n              fontSize: '12px'\n            },\n            children: \"\\u8BE5\\u8DEF\\u53E3\\u6682\\u65E0\\u7EA2\\u7EFF\\u706F\\u72B6\\u6001\\u4FE1\\u606F\\uFF0C\\u8BF7\\u7B49\\u5F85SPAT\\u6D88\\u606F\\u66F4\\u65B0\\u3002\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '6px',\n              fontSize: '10px',\n              color: '#888'\n            },\n            children: [\"\\u5F53\\u524D\\u65F6\\u95F4: \", new Date().toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3249,\n          columnNumber: 11\n        }, this);\n      }\n\n      // 设置弹窗位置在屏幕中央\n      const x = window.innerWidth / 2;\n      const y = window.innerHeight / 2;\n\n      // 更新弹窗状态\n      if (window._setTrafficLightPopover) {\n        var _stateInfo;\n        console.log('调用_setTrafficLightPopover更新弹窗状态', {\n          visible: true,\n          interId,\n          position: {\n            x,\n            y\n          }\n        });\n        window._setTrafficLightPopover({\n          visible: true,\n          interId: interId,\n          position: {\n            x,\n            y\n          },\n          content: content,\n          phases: ((_stateInfo = stateInfo) === null || _stateInfo === void 0 ? void 0 : _stateInfo.phases) || []\n        });\n        console.log(`已显示路口 ${(intersection === null || intersection === void 0 ? void 0 : intersection.name) || interId} 的红绿灯状态弹窗`);\n\n        // 设置定时更新\n        if (window.trafficLightUpdateTimerRef) {\n          window.trafficLightUpdateTimerRef.current = setInterval(updateTrafficLightPopover, TRAFFIC_LIGHT_UPDATE_INTERVAL * 1000);\n          console.log(`已设置红绿灯状态弹窗定时更新，间隔 ${TRAFFIC_LIGHT_UPDATE_INTERVAL} 秒`);\n        }\n        return true;\n      } else {\n        console.error('无法找到setTrafficLightPopover函数');\n        return false;\n      }\n    };\n    return updateInitialTrafficLightPopover();\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n\n//   console.log('开始生成模拟SPAT数据...');\n\n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n\n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n\n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n\n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n\n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n\n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n\n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n\n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n\n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n\n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n\n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n\n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n\n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n\n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n\n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n\n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = object => {\n  let current = object;\n\n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n\n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n\n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n\n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n\n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n\n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = (x - rect.left) / canvas.clientWidth * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    console.log('归一化坐标:', mouseX, mouseY);\n\n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n\n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n\n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'position:', intersect.object.position.toArray(), 'userData:', intersect.object.userData);\n\n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      return true;\n    }\n\n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', '类型:', obj.type, '位置:', obj.position.toArray(), '距离:', intersect.distance, 'userData:', obj.userData);\n    });\n\n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        var _lightObj$intersectio;\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n\n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n\n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n\n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        if (isVisible) {\n          visibleCount++;\n        }\n        console.log(`红绿灯 ${interId}:`, {\n          名称: ((_lightObj$intersectio = lightObj.intersection) === null || _lightObj$intersectio === void 0 ? void 0 : _lightObj$intersectio.name) || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n\n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  var _trafficLight$interse, _trafficLight$interse2, _trafficLight$interse3;\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch (phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n\n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: (_trafficLight$interse = trafficLight.intersection) === null || _trafficLight$interse === void 0 ? void 0 : _trafficLight$interse.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n\n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = {\n    isLight: true\n  };\n\n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  console.log(`更新路口 ${((_trafficLight$interse2 = trafficLight.intersection) === null || _trafficLight$interse2 === void 0 ? void 0 : _trafficLight$interse2.name) || ((_trafficLight$interse3 = trafficLight.intersection) === null || _trafficLight$interse3 === void 0 ? void 0 : _trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "Select", "Popover", "intersectionsData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "peopleBaseModel", "lastPosition", "lastRotation", "ALPHA", "vehicleLastPositions", "Map", "vehicleLastRotations", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "process", "env", "REACT_APP_API_URL", "console", "log", "vehicleModels", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "clock", "Clock", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "vehicleId", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "has", "lastPos", "get", "distance", "distanceTo", "MAX_DISTANCE_THRESHOLD", "toFixed", "filteredPos", "Vector3", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "lastRot", "MAX_ANGLE_THRESHOLD", "abs", "TRAFFIC_LIGHT_UPDATE_INTERVAL", "CampusModel", "className", "onCurrentRSUChange", "selectedRSUs", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "mapRef", "lastCameraPosition", "lastCameraTarget", "cameraSmoothing", "prevAnimationTimeRef", "Date", "now", "peopleAnimationMixers", "peopleAnimations", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "currentPopoverIdRef", "trafficLightUpdateTimerRef", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "switchToFollowView", "current", "enabled", "switchToGlobalView", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "target", "currentTarget", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "updateMatrix", "updateMatrixWorld", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "相机位置", "toArray", "目标点", "handleMqttMessage", "message", "payload", "JSON", "parse", "_payload$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "participants", "rsuid", "for<PERSON>ach", "participant", "id", "partPtcId", "type", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "model", "newModel", "height", "rotation", "scale", "mixer", "AnimationMixer", "animations", "length", "clip", "action", "clipAction", "play", "add", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "Set", "map", "p", "modelData", "remove", "delete", "bsmData", "bsmid", "newState", "partLong", "partLat", "postMessage", "source", "initialPosition", "initialRotation", "newPosition", "vehicleObj", "newVehicleModel", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "emissive", "Color", "transparent", "needsUpdate", "speedLabel", "createTextSprite", "round", "r", "g", "b", "a", "textColor", "renderOrder", "opacity", "is<PERSON><PERSON>", "Out", "filteredPosition", "dispose", "timeSinceLastUpdate", "undefined", "originalTran<PERSON>arent", "originalOpacity", "m", "geometry", "phasesInfo", "phase", "phaseId", "toString", "direction", "trafficDirec", "getDirectionFromCode", "getPhaseDirection", "lightState", "trafficLight", "remainTime", "parseInt", "phaseInfo", "push", "trafficLightKey", "String", "trafficLightModel", "updateTrafficLightVisual", "prev", "<PERSON><PERSON><PERSON>", "strId", "numId", "updateTime", "setTimeout", "showTrafficLightPopup", "rsiData", "rsuId", "events", "rtes", "event", "eventId", "rteId", "eventType", "description", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "stringify", "onerror", "onclose", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "setIsVehicleLoaded", "xhr", "loaded", "total", "initializeScene", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "currentTime", "deltaTime", "<PERSON><PERSON><PERSON><PERSON>", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "targetCameraPosition", "targetLookAt", "lerp", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "clearInterval", "close", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "clear", "lightObj", "handleMainVehicleChange", "intervalId", "setInterval", "timer", "createTrafficLights", "clearTimeout", "handleClick", "clientX", "clientY", "handleMouseClick", "warn", "initScene", "createSimpleTrafficLight", "BoxGeometry", "MeshBasicMaterial", "<PERSON><PERSON>", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "addClickHelpers", "helperGeometry", "SphereGeometry", "helperMaterial", "depthWrite", "helper<PERSON><PERSON>", "userData", "isClickHelper", "size", "firstIntersection", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "onChange", "options", "label", "bordered", "dropdownStyle", "maxHeight", "ref", "max<PERSON><PERSON><PERSON>", "right", "background", "onClick", "handleClosePopover", "_c", "text", "parameters", "params", "fontFace", "borderThickness", "borderColor", "canvas", "document", "createElement", "context", "getContext", "font", "textWidth", "measureText", "textBaseline", "radius", "beginPath", "moveTo", "lineTo", "arcTo", "closePath", "strokeStyle", "lineWidth", "stroke", "fillStyle", "fill", "textAlign", "fillText", "texture", "CanvasTexture", "minFilter", "LinearFilter", "spriteMaterial", "SpriteMaterial", "sprite", "Sprite", "depthTest", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "trafficLightGltf", "vehicleGltf", "cyclistGltf", "peopleGltf", "all", "loadAsync", "materia", "err", "types", "parent", "converterInstance", "createFallbackTrafficLights", "hasTrafficLight", "side", "DoubleSide", "colliderGeometry", "colliderMaterial", "collider", "isCollider", "lightGeometry", "lightMaterial", "<PERSON><PERSON><PERSON>", "dirCode", "container", "sceneInstance", "cameraInstance", "setPopoverState", "rect", "getBoundingClientRect", "mouseX", "clientWidth", "mouseY", "clientHeight", "raycaster", "Raycaster", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "trafficLightObjects", "trafficLightIntersects", "intersectObjects", "intersect", "index", "object", "obj", "getTrafficLightFromObject", "correctId", "intersects", "closestLight", "worldPos", "getWorldPosition", "screenPos", "project", "dx", "dy", "sqrt", "testTrafficLightClick", "_document$querySelect", "_document$querySelect2", "light", "lightModel", "stateInfo", "overflowY", "marginBottom", "borderBottom", "paddingBottom", "lightColor", "justifyContent", "marginTop", "toLocaleTimeString", "centerX", "centerY", "__REACT_INSTANCE", "popover", "innerHTML", "body", "closeButton", "listTrafficLights", "list", "from", "keys", "_light$intersection", "numericId", "updateTrafficLightPopover", "_window$currentPopove", "currentId", "intersectionLight", "lightText", "updateInitialTrafficLightPopover", "_stateInfo", "testClickDetection", "tlIntersects", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "distanceToCamera", "名称", "可见性", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "isLight", "emissiveIntensity", "PointLight", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel= null; // 存储原始模型数据\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  const lastPos = vehicleLastPositions.get(vehicleId);\n  \n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n  \n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  \n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  \n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const lastRot = vehicleLastRotations.get(vehicleId);\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  \n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n  \n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n  \n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = useRef(new Map()); // 存储所有行人的动画混合器\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n  \n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n  \n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n  \n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n  \n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n  \n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n      \n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n      \n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n        \n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos)\n          .to({ x: 0, y: 300, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.position.copy(currentPos);\n          })\n          .start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp)\n          .to({ x: 0, y: 1, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.up.copy(currentUp);\n          })\n          .start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n        \n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget)\n          .to({ x: 0, y: 0, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            controls.target.copy(currentTarget);\n            // 确保相机始终朝向目标点\n            cameraRef.current.lookAt(controls.target);\n            controls.update();\n          })\n          .start();\n\n        // 启用控制器\n        controls.enabled = true;\n        \n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x+50, 70, -modelCoords.y+50);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        // console.log('收到RSM消息:', payload);\n        \n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n        \n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n        \n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n      return;\n    }\n    \n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        \n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n        \n        const participants = payload.data?.participants || [];\n        const rsuid = payload.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id =  deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n          // 解析位置和状态信息\n          const state = {\n            longitude: parseFloat(participant.partPosLong),\n            latitude: parseFloat(participant.partPosLat),\n            speed: parseFloat(participant.partSpeed),\n            heading: parseFloat(participant.partHeading)\n          };\n          \n          const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n          \n          // 根据类型选择对应的预加载模型\n          let preloadedModel;\n          switch (type) {\n            case '1': // 机动车\n              preloadedModel = preloadedVehicleModel;\n              break;\n            case '2': // 非机动车\n              preloadedModel = preloadedCyclistModel;\n              break;\n            case '3': // 行人\n              preloadedModel = preloadedPeopleModel;\n              break;\n            default:\n              return; // 跳过未知类型\n          }\n          \n          // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n            // 创建新模型实例\n            const newModel = preloadedModel.clone();\n            // 根据类型调整高度和缩放\n            const height = type === '3' ? 2.0 : 1.0;\n            newModel.position.set(modelPos.x, height, -modelPos.y);\n            newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n\n            // 如果是行人类型，设置缩放和创建动画\n            if (type === '3') {\n              newModel.scale.set(0.05, 0.05, 0.05);\n              // 创建动画混合器\n              const mixer = new THREE.AnimationMixer(newModel);\n             \n              // 播放行走动画peopleBaseModel\n              // const action = mixer.clipAction(peopleAnimations.current[0]);\n              // const action = mixer.clipAction(peopleBaseModel.animations[0]);\n              // action.play();\n\n              // peopleAnimationMixers.current.set(id, mixer);\n\n              console.log('找到行人动画tttt:', peopleBaseModel.animations.length, '个');\n              if (peopleBaseModel.animations.length > 0) {\n                peopleBaseModel.animations.forEach((clip) => {\n                  const action = mixer.clipAction(clip);\n                  action.play();\n                });\n                // 保存动画混合器\n                peopleAnimationMixers.current.set(id, mixer);\n              }\n            }\n\n            scene.add(newModel);\n            \n            vehicleModels.set(id, {\n              model: newModel,\n              lastUpdate: now,\n              type: type\n            });\n          } else if (model) {\n            // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n          }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n        \n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n        \n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n        \n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid, // 直接传递车辆ID\n          data: {       // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n        \n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n        \n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n        \n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n        \n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        \n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          \n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n          \n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n              \n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n              \n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n          \n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n          \n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          \n          scene.add(newVehicleModel);\n          \n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n          \n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position)\n            .to({ y: 0.5 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .start();\n          \n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({ opacity: 0.6 })\n                .to({ opacity: 1.0 }, 500)\n                .easing(TWEEN.Easing.Quadratic.Out)\n                .onUpdate(function() {\n                  child.material.opacity = this.opacity;\n                  child.material.needsUpdate = true;\n                })\n                .start();\n            }\n          });\n          \n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({ opacity: 0.6 })\n            .to({ opacity: 1.0 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .onUpdate(function() {\n              speedLabel.material.opacity = this.opacity;\n              speedLabel.material.needsUpdate = true;\n            })\n            .start();\n          \n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n          \n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n          \n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n          \n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n          \n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n        \n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 2000; // 增加到10秒，避免频繁清理造成闪烁\n        \n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n          \n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1 ;\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n                \n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n            \n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n            \n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            \n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        \n        return;\n      }\n      \n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n        \n        try {\n          const payload = JSON.parse(message);\n          \n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n          \n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n          \n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n          \n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n          \n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              \n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              \n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n              \n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                \n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  \n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? \n                    getDirectionFromCode(phase.trafficDirec) : \n                    getPhaseDirection(phaseId);\n                  \n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  \n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n                  \n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n                  \n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n                  \n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  \n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  \n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n                    \n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n                \n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                \n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n                  \n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && \n                     (window.currentPopoverIdRef.current === modelKey || \n                      window.currentPopoverIdRef.current === String(modelKey) || \n                      window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    \n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n                    \n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n      }\n      \n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        \n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n        \n        return;\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n        \n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n        \n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          \n          // 检查scene是否初始化\n          if (scene) {\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画\n      const currentTime = Date.now();\n      // const deltaTime = (currentTime - prevAnimationTimeRef.current) * 10;\n      const deltaTime =clock.getDelta()\n      prevAnimationTimeRef.current = currentTime;\n\n      peopleAnimationMixers.current.forEach((mixer) => {\n        mixer.update(deltaTime);\n      });\n\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          200,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n        \n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        \n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n        \n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n        \n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n      \n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n      \n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach((lightObj) => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n      \n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n      \n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n    \n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n    \n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n    \n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n    \n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      \n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n  \n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          console.log('检测到点击事件', event.clientX, event.clientY);\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        } else {\n          console.warn('点击事件处理失败: scene或camera未初始化');\n        }\n      };\n      \n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n      \n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n      \n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n    \n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    \n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n    \n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,//\n          transparent: false,\n          opacity: 0.1,  // 几乎透明\n          depthWrite: false\n        });\n        \n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置\n        \n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n        \n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        \n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500);  // 延迟略长于debugTrafficLights\n    \n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n    \n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n  \n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        const firstIntersection = intersectionsData.intersections[0];\n        console.log('自动选择第一个路口:', firstIntersection.name);\n        \n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(firstIntersection.name);\n        }, 2000);\n        \n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n\n  return (\n    <>\n      <span style={labelStyle}>区域选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择区域位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n        value={selectedIntersection ? selectedIntersection.name : undefined}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      \n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '4px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n            padding: '0',\n            maxWidth: '240px', // 缩小最大宽度\n            fontSize: '12px' // 缩小字体\n          }}\n        >\n          {trafficLightPopover.content}\n          <button \n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '12px',\n              cursor: 'pointer',\n              padding: '2px 6px'\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n      \n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 16, // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    backgroundColor: parameters.backgroundColor || { r: 255, g: 255, b: 255, a: 0.8 },\n    textColor: parameters.textColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  \n  // 设置字体\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  \n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n  \n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  \n  canvas.width = width;\n  canvas.height = height;\n  \n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n  \n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n  \n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n  \n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n  \n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n  \n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n  \n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n  \n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = { \n    text: text,\n    params: params\n  };\n  \n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    try {\n      const [ trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([\n        loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`)\n        \n    ]);\n\n    // 处理机动车模型\n    console.log('加载车辆模型...');\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n          color: 0xff0000,  // 0xff0000, //红色 0xffffff,白色\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n      }\n    });\n\n    console.log('加载非机动车模型...');\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    console.log('加载行人模型...');\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 保存行人动画数据\n    console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n    if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n      console.log('找到行人动画:', peopleGltf.animations.length, '个');\n      peopleBaseModel = peopleGltf;\n    } else {\n      console.warn('行人模型没有包含动画数据');\n    }\n\n    console.log('加载红绿灯模型...');\n      \n    // 处理红绿灯模型\n    preloadedTrafficLightModel = trafficLightGltf.scene;\n    console.log('红绿灯模型：', preloadedTrafficLightModel);\n    // 设置红绿灯模型的缩放\n    preloadedTrafficLightModel.scale.set(6, 6, 6);\n    // 保持原始材质\n    preloadedTrafficLightModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 设置材质属性\n        child.material.metalness = 0.2;\n        child.material.roughness = 0.3;\n        child.material.envMapIntensity = 1.2;\n    }\n  });\n\n    console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n      \n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n        \n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  \n  try {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在1秒后自动移除\n  setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n    scene.remove(sprite);\n      }\n  }, 100);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  // console.log('添加场景事件标记:', {\n  //   位置: position,\n  //   文本: text,\n  //   颜色: color\n  // });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  \n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  \n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n  \n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型 - 只为hasTrafficLight为true的路口创建红绿灯\n  intersectionsData.intersections.forEach(intersection => {\n    // 检查路口是否有红绿灯属性，如果没有或为false，则跳过\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n    \n    // 确认路口有经纬度和ID\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      \n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n        \n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n        \n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n        \n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n        \n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n        \n        // *** 重要：设置为可被交互 ***\n        trafficLightModel.traverse(child => {\n          // 设置所有子对象可被点击\n          if (child.isMesh) {\n            // 确保材质能够被射线检测到\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            \n            // 设置较高的渲染顺序\n            child.renderOrder = 100;\n          }\n        });\n        \n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        \n        // // 添加一个专门用于点击的大型碰撞体\n        // const colliderGeometry = new THREE.SphereGeometry(5, 12, 12);\n        // const colliderMaterial = new THREE.MeshBasicMaterial({\n        //   color: 0xff00ff,\n        //   transparent: true,\n        //   opacity: 0.0,  // 完全透明\n        //   depthWrite: false\n        // });\n        \n        // const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n        // collider.name = `交通灯碰撞体-${intersection.name}`;\n        // collider.userData = {\n        //   type: 'trafficLight',\n        //   interId: intersection.interId,\n        //   name: intersection.name,\n        //   isCollider: true\n        // };\n        \n        // trafficLightModel.add(collider);\n        \n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n        \n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        \n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n  \n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance) => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({ \n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n  \n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n  \n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n  \n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n  \n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,  // 完全透明\n    depthWrite: false\n  });\n  \n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  \n  trafficLightModel.add(collider);\n  \n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n  \n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n  \n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n  \n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  trafficLightModel.add(lightMesh);\n  \n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  switch(phaseId) {\n    case '1': return '北进口左转';\n    case '2': return '北进口直行';\n    case '3': return '北进口右转';\n    case '5': return '东进口左转';\n    case '6': return '东进口直行';\n    case '7': return '东进口右转';\n    case '9': return '南进口左转';\n    case '10': return '南进口直行';\n    case '11': return '南进口右转';\n    case '13': return '西进口左转';\n    case '14': return '西进口直行';\n    case '15': return '西进口右转';\n    default: return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = (dirCode) => {\n  switch(dirCode) {\n    case 'N': return '北向南';\n    case 'S': return '南向北';\n    case 'E': return '东向西';\n    case 'W': return '西向东';\n    case 'NE': return '东北向西南';\n    case 'NW': return '西北向东南';\n    case 'SE': return '东南向西北';\n    case 'SW': return '西南向东北';\n    default: return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  \n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n  \n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n  \n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Line.threshold = 1;\n  \n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  \n  console.log('鼠标点击位置:', mouseX, mouseY);\n  \n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  \n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n  \n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  \n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, \n                 intersect.object.name || '无名称', \n                 '距离:', intersect.distance,\n                 'userData:', intersect.object.userData);\n    });\n    \n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    \n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n      \n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n      \n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n  \n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  \n  console.log('射线检测到的对象数量:', intersects.length);\n  \n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', \n                  'userData:', obj.userData, \n                  '距离:', intersect.distance);\n    });\n    \n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        console.log('检测到红绿灯点击, 路口ID:', interId);\n        \n        // 显示弹窗\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n  \n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n  \n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.1; // 设置一个阈值，只有距离小于此值的才会被选中\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n      \n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n      \n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      \n      console.log(`路口 ${interId} 的距离:`, distance);\n      \n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = { interId, distance };\n      }\n    }\n  });\n  \n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n    \n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  \n  console.log('未检测到任何红绿灯点击');\n  \n  // 如果用户点击了其他任何地方，关闭现有的弹窗\n  if (setPopoverState) {\n    setPopoverState(prev => {\n      if (prev.visible) {\n        return { ...prev, visible: false };\n      }\n      return prev;\n    });\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n  \n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n  \n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({ ...prev, visible: false }));\n  console.log('弹窗已关闭');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = (interId) => {\n  try {\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      \n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      \n      return false;\n    }\n    \n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n    \n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 创建弹出窗口内容\n    let content;\n    \n    if (stateInfo && stateInfo.phases) {\n      content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection.name} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              switch (phase.trafficLight) {\n                case 'G': lightColor = '#00ff00'; break;\n                case 'Y': lightColor = '#ffff00'; break;\n                case 'R': default: lightColor = '#ff0000'; break;\n              }\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {getPhaseDirection(phase.phaseId)}\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      content = (\n        <div style={{ padding: '8px', maxWidth: '200px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>\n          <div>路口ID: {interId}</div>\n          <div>当前无信号灯状态信息</div>\n        </div>\n      );\n    }\n    \n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2;\n    const centerY = window.innerHeight / 2;\n    \n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;\n    \n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: { x: centerX, y: centerY },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      \n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      \n      document.body.appendChild(popover);\n      \n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      \n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  \n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  \n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  \n  return list;\n};\n\n// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n\n//   console.log('开始调试红绿灯模型...');\n  \n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n  \n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n  \n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n      \n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n  \n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n  \n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n      \n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n      \n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n      \n//       // 添加到场景\n//       scene.add(highlightMesh);\n      \n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n      \n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n      \n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n  \n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = (interId) => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    \n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    console.log('当前trafficLightStates大小:', trafficLightStates.size);\n    \n    // 如果没有指定ID，则使用第一个红绿灯\n    if (!interId && trafficLightsMap.size > 0) {\n      interId = String(Array.from(trafficLightsMap.keys())[0]);\n      console.log('未指定ID，使用第一个红绿灯ID:', interId);\n    }\n    \n    // 输出所有可用的红绿灯ID用于调试\n    console.log('所有可用的红绿灯ID:');\n    trafficLightsMap.forEach((light, id) => {\n      console.log(`- ${id} (${typeof id}): ${light.intersection?.name || '未知'}`);\n    });\n    \n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      \n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    \n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    \n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n    \n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n    \n    // 创建更新函数\n    const updateTrafficLightPopover = () => {\n      // 确保当前弹窗仍然可见\n      if (!window._setTrafficLightPopover) return;\n      \n      const currentId = window.currentPopoverIdRef?.current;\n      if (!currentId) return;\n      \n      // 重新获取最新的状态信息\n      let stateInfo = trafficLightStates.get(currentId);\n      if (!stateInfo && typeof currentId === 'string') {\n        // 尝试使用数字ID\n        stateInfo = trafficLightStates.get(parseInt(currentId));\n      } else if (!stateInfo && typeof currentId === 'number') {\n        // 尝试使用字符串ID\n        stateInfo = trafficLightStates.get(String(currentId));\n      }\n      \n      if (!stateInfo || !stateInfo.phases || stateInfo.phases.length === 0) {\n        console.log(`路口ID ${currentId} 没有状态信息，跳过更新`);\n        return;\n      }\n      \n      // 获取交通灯信息\n      const intersectionLight = trafficLightsMap.get(currentId) || \n                               (typeof currentId === 'string' ? trafficLightsMap.get(parseInt(currentId)) : \n                                trafficLightsMap.get(String(currentId)));\n                                \n      if (!intersectionLight) {\n        console.error(`找不到路口ID ${currentId} 的红绿灯信息，无法更新弹窗`);\n        return;\n      }\n      \n      const intersection = intersectionLight.intersection;\n      \n      // 创建更新的弹窗内容\n      const content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection?.name || '未知路口'} (ID: {currentId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              let lightText;\n              \n              switch (phase.trafficLight) {\n                case 'G':\n                  lightColor = '#00ff00';\n                  lightText = '绿灯';\n                  break;\n                case 'Y':\n                  lightColor = '#ffff00';\n                  lightText = '黄灯';\n                  break;\n                case 'R':\n                default:\n                  lightColor = '#ff0000';\n                  lightText = '红灯';\n                  break;\n              }\n              \n              const direction = getPhaseDirection(phase.phaseId);\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {direction} (相位ID: {phase.phaseId})\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {lightText}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date(stateInfo.updateTime).toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n      \n      // 更新弹窗内容\n      window._setTrafficLightPopover(prev => ({\n        ...prev,\n        content: content,\n        phases: stateInfo.phases\n      }));\n      \n      console.log(`已更新路口 ${intersection?.name || currentId} 的红绿灯状态弹窗`);\n    };\n    \n    // 先执行一次初始更新\n    const updateInitialTrafficLightPopover = () => {\n      // 同样，查找红绿灯状态信息时也尝试字符串和数字两种类型\n      let stateInfo = trafficLightStates.get(interId);\n      if (!stateInfo) {\n        // 尝试使用数字ID\n        const numericId = parseInt(interId);\n        stateInfo = trafficLightStates.get(numericId);\n        \n        if (stateInfo) {\n          console.log(`使用数字ID ${numericId} 找到了红绿灯状态信息`);\n        }\n      }\n      \n      console.log(`路口ID ${interId} 的状态信息:`, stateInfo);\n      \n      const intersection = trafficLight.intersection;\n      console.log('路口信息:', intersection);\n      \n      // 创建弹窗内容\n      let content;\n      \n      if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n        // 添加一个检查相位数据的打印\n        console.log('相位数据详情:');\n        stateInfo.phases.forEach((phase, index) => {\n          console.log(`相位 ${index+1}: ID=${phase.phaseId}, 状态=${phase.trafficLight}, 剩余时间=${phase.remainTime}`);\n        });\n        \n        content = (\n          <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n            <div style={{ \n              fontWeight: 'bold', \n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            }}>\n              {intersection?.name || '未知路口'} (ID: {interId})\n            </div>\n            <div>\n              {stateInfo.phases.map((phase, index) => {\n                let lightColor;\n                let lightText;\n                \n                switch (phase.trafficLight) {\n                  case 'G':\n                    lightColor = '#00ff00';\n                    lightText = '绿灯';\n                    break;\n                  case 'Y':\n                    lightColor = '#ffff00';\n                    lightText = '黄灯';\n                    break;\n                  case 'R':\n                  default:\n                    lightColor = '#ff0000';\n                    lightText = '红灯';\n                    break;\n                }\n                \n                const direction = getPhaseDirection(phase.phaseId);\n                \n                return (\n                  <div key={index} style={{ \n                    marginBottom: '6px', \n                    backgroundColor: 'rgba(255,255,255,0.1)',\n                    padding: '4px',\n                    borderRadius: '4px',\n                    fontSize: '12px'\n                  }}>\n                    <div style={{ fontWeight: 'bold' }}>\n                      {direction} (相位ID: {phase.phaseId})\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <span>灯色: </span>\n                      <span style={{ \n                        color: lightColor, \n                        fontWeight: 'bold',\n                        backgroundColor: 'rgba(0,0,0,0.3)',\n                        padding: '0 3px',\n                        borderRadius: '2px'\n                      }}>\n                        {lightText}\n                      </span>\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <span>倒计时: </span>\n                      <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n            <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n              更新时间: {new Date(stateInfo.updateTime).toLocaleTimeString()} (自动刷新)\n            </div>\n          </div>\n        );\n      } else {\n        // 如果没有状态信息，创建一个示例内容\n        content = (\n          <div style={{ padding: '8px', width: '220px' }}>\n            <div style={{ \n              fontWeight: 'bold', \n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            }}>\n              {intersection?.name || '未知路口'} (ID: {interId})\n            </div>\n            <div style={{ color: '#ff4d4f', fontSize: '12px' }}>\n              该路口暂无红绿灯状态信息，请等待SPAT消息更新。\n            </div>\n            <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n              当前时间: {new Date().toLocaleTimeString()}\n            </div>\n          </div>\n        );\n      }\n      \n      // 设置弹窗位置在屏幕中央\n      const x = window.innerWidth / 2;\n      const y = window.innerHeight / 2;\n      \n      // 更新弹窗状态\n      if (window._setTrafficLightPopover) {\n        console.log('调用_setTrafficLightPopover更新弹窗状态', {\n          visible: true,\n          interId,\n          position: { x, y }\n        });\n        \n        window._setTrafficLightPopover({\n          visible: true,\n          interId: interId,\n          position: { x, y },\n          content: content,\n          phases: stateInfo?.phases || []\n        });\n        \n        console.log(`已显示路口 ${intersection?.name || interId} 的红绿灯状态弹窗`);\n        \n        // 设置定时更新\n        if (window.trafficLightUpdateTimerRef) {\n          window.trafficLightUpdateTimerRef.current = setInterval(updateTrafficLightPopover, TRAFFIC_LIGHT_UPDATE_INTERVAL * 1000);\n          console.log(`已设置红绿灯状态弹窗定时更新，间隔 ${TRAFFIC_LIGHT_UPDATE_INTERVAL} 秒`);\n        }\n        \n        return true;\n      } else {\n        console.error('无法找到setTrafficLightPopover函数');\n        return false;\n      }\n    };\n    \n    return updateInitialTrafficLightPopover();\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n  \n//   console.log('开始生成模拟SPAT数据...');\n  \n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n  \n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n  \n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n    \n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n      \n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n    \n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n      \n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n      \n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n    \n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n    \n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n    \n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n    \n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n  \n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n  \n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n  \n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n  \n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n  \n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n  \n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  \n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n    \n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n    \n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n    \n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n    \n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    \n    console.log('归一化坐标:', mouseX, mouseY);\n    \n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    \n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n    \n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n    \n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    \n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', \n                    '距离:', intersect.distance,\n                    'position:', intersect.object.position.toArray(),\n                    'userData:', intersect.object.userData);\n        \n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      \n      return true;\n    }\n    \n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    \n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', \n                  '类型:', obj.type,\n                  '位置:', obj.position.toArray(),\n                  '距离:', intersect.distance,\n                  'userData:', obj.userData);\n    });\n    \n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    \n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n        \n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n        \n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n        \n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        \n        if (isVisible) {\n          visibleCount++;\n        }\n        \n        console.log(`红绿灯 ${interId}:`, {\n          名称: lightObj.intersection?.name || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    \n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n    \n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  \n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch(phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n  \n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ \n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: trafficLight.intersection?.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n  \n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = { isLight: true };\n  \n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  \n  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\n\nexport default CampusModel; \n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACtC,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,0BAA0B,GAAG,IAAI,CAAC,CAAC;AACvC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB,IAAIC,eAAe,GAAE,IAAI,CAAC,CAAC;;AAE3B;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAEpB;AACA,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,MAAMC,oBAAoB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;;AAExC;AACA,MAAME,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACV;EACEC,GAAG,EAAE,2BAA2B;EAChCC,GAAG,EAAE,2BAA2B;EAChCf,KAAK,EAAE,6BAA6B;EACtCgB,GAAG,EAAE,2BAA2B;EAAG;EACnCC,IAAI,EAAE,4BAA4B,CAAE;AACtC,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AACzEC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;;AAE1D;AACA,MAAMG,aAAa,GAAG,IAAIlB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,IAAImB,gBAAgB,GAAG,IAAInB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAElC;AACA,IAAIoB,gBAAgB,GAAG,IAAI;;AAE3B;AACA,IAAIC,gBAAgB,GAAG,IAAIrB,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,IAAIsB,kBAAkB,GAAG,IAAItB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEpC,MAAMuB,KAAK,GAAG,IAAIvD,KAAK,CAACwD,KAAK,CAAC,CAAC;;AAE/B;AACA,MAAMC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGf,QAAQ,oBAAoB,CAAC;IAC7D,MAAMgB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,IAAIA,IAAI,CAACE,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;MACzD,MAAMG,WAAW,GAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,IAAI,CAAC;MACrE,IAAIH,WAAW,IAAIA,WAAW,CAACI,KAAK,EAAE;QACpCjB,gBAAgB,GAAGa,WAAW,CAACI,KAAK;QACpCrB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEG,gBAAgB,CAAC;QAC7C,OAAOA,gBAAgB;MACzB;IACF;IACAJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChCG,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjClB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB;AACF,CAAC;;AAED;AACA,MAAMmB,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;EAC5C;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAI,CAACjD,YAAY,EAAE;MACjBA,YAAY,GAAGgD,MAAM,CAACE,KAAK,CAAC,CAAC;MAC7B,OAAOF,MAAM;IACf;IAEA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEpD,YAAY,CAACoD,CAAC,EAAElD,KAAK,CAAC;IAChE,MAAMmD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEtD,YAAY,CAACsD,CAAC,EAAEpD,KAAK,CAAC;IAChE,MAAMqD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAExD,YAAY,CAACwD,CAAC,EAAEtD,KAAK,CAAC;IAEhEF,YAAY,CAACyD,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;IACjD,OAAOvD,YAAY,CAACkD,KAAK,CAAC,CAAC;EAC3B;;EAEA;EACA,IAAI,CAAC/C,oBAAoB,CAACuD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC9C,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;EAEA,MAAMW,OAAO,GAAGxD,oBAAoB,CAACyD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,MAAMY,QAAQ,GAAGF,OAAO,CAACG,UAAU,CAACd,MAAM,CAAC;EAC3C,MAAMe,sBAAsB,GAAG,EAAE,CAAC,CAAC;;EAEnC,IAAIF,QAAQ,GAAGE,sBAAsB,EAAE;IACrC3C,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAUY,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAClE7D,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;;EAEA;EACA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEO,OAAO,CAACP,CAAC,EAAElD,KAAK,CAAC;EAC3D,MAAMmD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEK,OAAO,CAACL,CAAC,EAAEpD,KAAK,CAAC;EAC3D,MAAMqD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAEG,OAAO,CAACH,CAAC,EAAEtD,KAAK,CAAC;EAE3D,MAAM+D,WAAW,GAAG,IAAI7F,KAAK,CAAC8F,OAAO,CAACf,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACtEpD,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAEgB,WAAW,CAACf,KAAK,CAAC,CAAC,CAAC;EAExD,OAAOe,WAAW;AACpB,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,WAAW,EAAEnB,SAAS,KAAK;EACjD;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAIhD,YAAY,KAAK,IAAI,EAAE;MACzBA,YAAY,GAAGmE,WAAW;MAC1B,OAAOA,WAAW;IACpB;;IAEA;IACA,IAAIC,IAAI,GAAGD,WAAW,GAAGnE,YAAY;IACrC,IAAIoE,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IAExC,MAAMC,gBAAgB,GAAG7B,aAAa,CAAC1C,YAAY,GAAGoE,IAAI,EAAEpE,YAAY,EAAEC,KAAK,CAAC;IAChFD,YAAY,GAAGuE,gBAAgB;IAC7B,OAAOA,gBAAgB;EACzB;;EAEA;EACA,IAAI,CAACnE,oBAAoB,CAACqD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC5C,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMK,OAAO,GAAGpE,oBAAoB,CAACuD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,IAAIoB,IAAI,GAAGD,WAAW,GAAGK,OAAO;EAChC,IAAIJ,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;;EAExC;EACA,MAAMG,mBAAmB,GAAGJ,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,CAAC;EACzC,IAAID,IAAI,CAACK,GAAG,CAACN,IAAI,CAAC,GAAGK,mBAAmB,EAAE;IACxCtD,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAU,CAACoB,IAAI,GAAG,GAAG,GAAGC,IAAI,CAACC,EAAE,EAAEP,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAChF3D,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMI,gBAAgB,GAAG7B,aAAa,CAAC8B,OAAO,GAAGJ,IAAI,EAAEI,OAAO,EAAEvE,KAAK,CAAC;EACtEG,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEuB,gBAAgB,CAAC;EAErD,OAAOA,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA,MAAMI,6BAA6B,GAAG,CAAC,CAAC,CAAC;AACzC;;AAEA,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC,kBAAkB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAMC,YAAY,GAAGjH,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMkH,UAAU,GAAGlH,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMmH,SAAS,GAAGnH,MAAM,CAAC,IAAIM,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAM8G,aAAa,GAAGpH,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMqH,eAAe,GAAGrH,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMsH,aAAa,GAAGtH,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMuH,iBAAiB,GAAGvH,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMwH,MAAM,GAAGxH,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAMyH,kBAAkB,GAAGzH,MAAM,CAAC,IAAI,CAAC;EACvC,MAAM0H,gBAAgB,GAAG1H,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM2H,eAAe,GAAG,IAAI,CAAC,CAAC;;EAE9B;EACA,MAAMC,oBAAoB,GAAG5H,MAAM,CAAC6H,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC/C,MAAMC,qBAAqB,GAAG/H,MAAM,CAAC,IAAImC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,MAAM6F,gBAAgB,GAAGhI,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;;EAGrC;EACA,MAAM,CAACiI,YAAY,EAAEC,eAAe,CAAC,GAAGjI,QAAQ,CAAC;IAC/CkI,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvI,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMwI,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,IAAI;IAAG;IACfC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG1J,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAAC2J,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3J,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAAC4J,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7J,QAAQ,CAAC;IAC7D8J,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,IAAI;IACbtB,QAAQ,EAAE;MAAEvD,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxB4E,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAGnK,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMoK,0BAA0B,GAAGpK,MAAM,CAAC,IAAI,CAAC;;EAE/C;EACAuC,MAAM,CAAC8H,uBAAuB,GAAGP,sBAAsB;;EAEvD;EACAvH,MAAM,CAAC4H,mBAAmB,GAAGA,mBAAmB;EAChD5H,MAAM,CAAC6H,0BAA0B,GAAGA,0BAA0B;;EAE9D;EACA,MAAME,uBAAuB,GAAG;IAC9B5B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACX3B,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7B2B,KAAK,EAAE,OAAO;IAAG;IACjB1B,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMiB,UAAU,GAAG;IACjB/B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACX3B,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBwB,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbpB,QAAQ,EAAE,MAAM;IAChBqB,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvC/B,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM,CAACtF,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,IAAIkC,GAAG,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAAC2I,UAAU,EAAEC,aAAa,CAAC,GAAG9K,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACqD,gBAAgB,EAAE0H,mBAAmB,CAAC,GAAG/K,QAAQ,CAAC,IAAIkC,GAAG,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAAC8I,WAAW,EAAEC,cAAc,CAAC,GAAGjL,QAAQ,CAAC;IAAEkL,KAAK,EAAE,EAAE;IAAElB,OAAO,EAAE;EAAG,CAAC,CAAC;;EAE1E;EACA,MAAMmB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI7J,UAAU,KAAK,QAAQ,EAAE;MAC3B4B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB7B,UAAU,GAAG,QAAQ;;MAErB;MACAkG,kBAAkB,CAAC4D,OAAO,GAAG,IAAI;MACjC3D,gBAAgB,CAAC2D,OAAO,GAAG,IAAI;MAE/B,IAAI7J,QAAQ,EAAE;QACZA,QAAQ,CAAC8J,OAAO,GAAG,KAAK;MAC1B;IACF;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIhK,UAAU,KAAK,QAAQ,EAAE;MAC3B4B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB7B,UAAU,GAAG,QAAQ;;MAErB;MACAkG,kBAAkB,CAAC4D,OAAO,GAAG,IAAI;MACjC3D,gBAAgB,CAAC2D,OAAO,GAAG,IAAI;MAE/B,IAAI3B,SAAS,CAAC2B,OAAO,IAAI7J,QAAQ,EAAE;QACjC;QACA;QACAkI,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC,MAAMgG,UAAU,GAAG9B,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACzD,KAAK,CAAC,CAAC;QACrD,MAAMwG,SAAS,GAAG/B,SAAS,CAAC2B,OAAO,CAACK,EAAE,CAACzG,KAAK,CAAC,CAAC;;QAE9C;QACA,IAAI1E,KAAK,CAACoL,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC;UAAEzG,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,GAAG;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAChCsG,MAAM,CAACtL,KAAK,CAACuL,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdvC,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACwD,IAAI,CAACV,UAAU,CAAC;QAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;QAEV;QACA,IAAI5L,KAAK,CAACoL,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;UAAEzG,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAC9BsG,MAAM,CAACtL,KAAK,CAACuL,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdvC,SAAS,CAAC2B,OAAO,CAACK,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC;QACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC;;QAEV;QACA3K,QAAQ,CAAC4K,MAAM,CAAC5G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B,MAAM6G,aAAa,GAAG7K,QAAQ,CAAC4K,MAAM,CAACnH,KAAK,CAAC,CAAC;;QAE7C;QACA,IAAI1E,KAAK,CAACoL,KAAK,CAACU,aAAa,CAAC,CAC3BT,EAAE,CAAC;UAAEzG,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAC9BsG,MAAM,CAACtL,KAAK,CAACuL,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdzK,QAAQ,CAAC4K,MAAM,CAACF,IAAI,CAACG,aAAa,CAAC;UACnC;UACA3C,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAAC9K,QAAQ,CAAC4K,MAAM,CAAC;UACzC5K,QAAQ,CAAC+K,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;QAEV;QACA3K,QAAQ,CAAC8J,OAAO,GAAG,IAAI;;QAEvB;QACA9J,QAAQ,CAACgL,WAAW,GAAG,EAAE;QACzBhL,QAAQ,CAACiL,WAAW,GAAG,GAAG;QAC1BjL,QAAQ,CAACkL,aAAa,GAAGrG,IAAI,CAACC,EAAE,GAAG,GAAG;QACtC9E,QAAQ,CAACmL,aAAa,GAAG,CAAC;QAC1BnL,QAAQ,CAAC+K,MAAM,CAAC,CAAC;QACjB;QACA7C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC;QAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;QAEzC1J,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;UACrB0J,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAGxM,iBAAiB,CAACyM,aAAa,CAAC/I,IAAI,CAACgJ,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKJ,KAAK,CAAC;IAChF,IAAIC,YAAY,IAAIzD,SAAS,CAAC2B,OAAO,IAAI7J,QAAQ,EAAE;MACjDoI,uBAAuB,CAACuD,YAAY,CAAC;;MAErC;MACA,MAAMI,WAAW,GAAGpG,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAChDC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,EAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC;MAEDjF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvBsK,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBK,GAAG,EAAE;UACHxF,SAAS,EAAEgF,YAAY,CAAChF,SAAS;UACjCC,QAAQ,EAAE+E,YAAY,CAAC/E;QACzB,CAAC;QACDwF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACAhM,UAAU,GAAG,cAAc;MAC3BiH,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAkB,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAClD,GAAG,CAAC+H,WAAW,CAACpI,CAAC,GAAC,EAAE,EAAE,EAAE,EAAE,CAACoI,WAAW,CAAClI,CAAC,GAAC,EAAE,CAAC;;MAEvE;MACA7D,QAAQ,CAAC4K,MAAM,CAAC5G,GAAG,CAAC+H,WAAW,CAACpI,CAAC,EAAE,CAAC,EAAE,CAACoI,WAAW,CAAClI,CAAC,CAAC;;MAErD;MACAqE,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAAC9K,QAAQ,CAAC4K,MAAM,CAAC;;MAEzC;MACA5K,QAAQ,CAAC8J,OAAO,GAAG,IAAI;MACvB9J,QAAQ,CAAC+K,MAAM,CAAC,CAAC;;MAEjB;MACA7C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC;MAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;MAEzC1J,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBsK,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBO,IAAI,EAAEnE,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACoF,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAEvM,QAAQ,CAAC4K,MAAM,CAAC0B,OAAO,CAAC,CAAC;QAC9BF,IAAI,EAAEL;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAGA,CAAC7C,KAAK,EAAE8C,OAAO,KAAK;IAC5C,IAAI;MACF,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;MAEnC;MACA,IAAI9C,KAAK,KAAK9I,WAAW,CAACO,GAAG,EAAE;QAAA,IAAAyL,aAAA;QAC7B;;QAEA;QACA,MAAMC,SAAS,GAAGJ,OAAO,CAACK,GAAG;QAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;QAEnC;QACA,MAAMC,aAAa,GAAGpL,gBAAgB,CAACqC,GAAG,CAAC2I,SAAS,CAAC;;QAErD;QACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;UACrD;UACA;UACA;UACA;UACA;UACJ;QACF;;QAEI;QACApL,gBAAgB,CAACkC,GAAG,CAAC8I,SAAS,EAAEE,gBAAgB,CAAC;;QAEjD;QACA;QACA;QACA;QACA;;QAEA,MAAMG,YAAY,GAAG,EAAAN,aAAA,GAAAH,OAAO,CAACnK,IAAI,cAAAsK,aAAA,uBAAZA,aAAA,CAAcM,YAAY,KAAI,EAAE;QACrD,MAAMC,KAAK,GAAGV,OAAO,CAACnK,IAAI,CAAC6K,KAAK;;QAEhC;QACA,MAAM9G,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;;QAEtB;QACA6G,YAAY,CAACE,OAAO,CAACC,WAAW,IAAI;UAClC;UACA;UACA,MAAMC,EAAE,GAAIT,SAAS,GAAGQ,WAAW,CAACE,SAAS;UAC7C,MAAMC,IAAI,GAAGH,WAAW,CAACI,WAAW;UAEpC,IAAGD,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACA;YACA,MAAME,KAAK,GAAG;cACZhH,SAAS,EAAEsF,UAAU,CAACqB,WAAW,CAACM,WAAW,CAAC;cAC9ChH,QAAQ,EAAEqF,UAAU,CAACqB,WAAW,CAACO,UAAU,CAAC;cAC5ChH,KAAK,EAAEoF,UAAU,CAACqB,WAAW,CAACQ,SAAS,CAAC;cACxChH,OAAO,EAAEmF,UAAU,CAACqB,WAAW,CAACS,WAAW;YAC7C,CAAC;YAED,MAAMC,QAAQ,GAAGrI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAAC2B,KAAK,CAAChH,SAAS,EAAEgH,KAAK,CAAC/G,QAAQ,CAAC;;YAEhF;YACA,IAAIqH,cAAc;YAClB,QAAQR,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRQ,cAAc,GAAGhO,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRgO,cAAc,GAAG/N,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACR+N,cAAc,GAAG9N,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACA,IAAI+N,KAAK,GAAGrM,aAAa,CAACsC,GAAG,CAACoJ,EAAE,CAAC;YAEjC,IAAI,CAACW,KAAK,IAAID,cAAc,EAAE;cAC5B;cACA,MAAME,QAAQ,GAAGF,cAAc,CAACxK,KAAK,CAAC,CAAC;cACvC;cACA,MAAM2K,MAAM,GAAGX,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;cACvCU,QAAQ,CAACjH,QAAQ,CAAClD,GAAG,CAACgK,QAAQ,CAACrK,CAAC,EAAEyK,MAAM,EAAE,CAACJ,QAAQ,CAACnK,CAAC,CAAC;cACtDsK,QAAQ,CAACE,QAAQ,CAACxK,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAG6I,KAAK,CAAC7G,OAAO,GAAGjC,IAAI,CAACC,EAAE,GAAG,GAAG;;cAE7D;cACA,IAAI2I,IAAI,KAAK,GAAG,EAAE;gBAChBU,QAAQ,CAACG,KAAK,CAACtK,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;gBACpC;gBACA,MAAMuK,KAAK,GAAG,IAAI5P,KAAK,CAAC6P,cAAc,CAACL,QAAQ,CAAC;;gBAEhD;gBACA;gBACA;gBACA;;gBAEA;;gBAEAxM,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEtB,eAAe,CAACmO,UAAU,CAACC,MAAM,EAAE,GAAG,CAAC;gBAClE,IAAIpO,eAAe,CAACmO,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;kBACzCpO,eAAe,CAACmO,UAAU,CAACpB,OAAO,CAAEsB,IAAI,IAAK;oBAC3C,MAAMC,MAAM,GAAGL,KAAK,CAACM,UAAU,CAACF,IAAI,CAAC;oBACrCC,MAAM,CAACE,IAAI,CAAC,CAAC;kBACf,CAAC,CAAC;kBACF;kBACAvI,qBAAqB,CAACsD,OAAO,CAAC7F,GAAG,CAACuJ,EAAE,EAAEgB,KAAK,CAAC;gBAC9C;cACF;cAEAlO,KAAK,CAAC0O,GAAG,CAACZ,QAAQ,CAAC;cAEnBtM,aAAa,CAACmC,GAAG,CAACuJ,EAAE,EAAE;gBACpBW,KAAK,EAAEC,QAAQ;gBACfa,UAAU,EAAE1I,GAAG;gBACfmH,IAAI,EAAEA;cACR,CAAC,CAAC;YACJ,CAAC,MAAM,IAAIS,KAAK,EAAE;cAChB;cACAA,KAAK,CAACA,KAAK,CAAChH,QAAQ,CAAClD,GAAG,CAACgK,QAAQ,CAACrK,CAAC,EAAEuK,KAAK,CAACT,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAACO,QAAQ,CAACnK,CAAC,CAAC;cACjFqK,KAAK,CAACA,KAAK,CAACG,QAAQ,CAACxK,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAG6I,KAAK,CAAC7G,OAAO,GAAGjC,IAAI,CAACC,EAAE,GAAG,GAAG;cAChEoJ,KAAK,CAACc,UAAU,GAAG1I,GAAG;cACtB4H,KAAK,CAACA,KAAK,CAAC9C,YAAY,CAAC,CAAC;cAC1B8C,KAAK,CAACA,KAAK,CAAC7C,iBAAiB,CAAC,IAAI,CAAC;YACrC;UACA;QACF,CAAC,CAAC;;QAEF;QACA,MAAM4D,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAChC,YAAY,CAACiC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC7B,SAAS,CAAC,CAAC;QAE9D3L,aAAa,CAACwL,OAAO,CAAC,CAACiC,SAAS,EAAE/B,EAAE,KAAK;UACvC,IAAIjH,GAAG,GAAGgJ,SAAS,CAACN,UAAU,GAAGC,iBAAiB,IAAI,CAACC,UAAU,CAACjL,GAAG,CAACsJ,EAAE,CAAC,EAAE;YACzElN,KAAK,CAACkP,MAAM,CAACD,SAAS,CAACpB,KAAK,CAAC;YAC7BrM,aAAa,CAAC2N,MAAM,CAACjC,EAAE,CAAC;YACxB5L,OAAO,CAACC,GAAG,CAAC,oBAAoB2L,EAAE,QAAQ+B,SAAS,CAAC7B,IAAI,EAAE,CAAC;UAC7D;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAI9D,KAAK,KAAK9I,WAAW,CAACM,GAAG,EAAE;QAC7B;;QAEA,MAAMsO,OAAO,GAAG/C,OAAO,CAACnK,IAAI;QAC5B,MAAMmN,KAAK,GAAGD,OAAO,CAACzM,KAAK;QAC3B,MAAM2M,QAAQ,GAAG;UACfhJ,SAAS,EAAEsF,UAAU,CAACwD,OAAO,CAACG,QAAQ,CAAC;UACvChJ,QAAQ,EAAEqF,UAAU,CAACwD,OAAO,CAACI,OAAO,CAAC;UACrChJ,KAAK,EAAEoF,UAAU,CAACwD,OAAO,CAAC3B,SAAS,CAAC;UACpChH,OAAO,EAAEmF,UAAU,CAACwD,OAAO,CAAC1B,WAAW;QACzC,CAAC;;QAED;QACA;;QAEA;QACAhN,MAAM,CAAC+O,WAAW,CAAC;UACjBrC,IAAI,EAAE,iBAAiB;UACvBsC,MAAM,EAAE;QACV,CAAC,EAAE,GAAG,CAAC;;QAEP;QACAhP,MAAM,CAAC+O,WAAW,CAAC;UACjBrC,IAAI,EAAE,KAAK;UACXzK,KAAK,EAAE0M,KAAK;UAAE;UACdnN,IAAI,EAAE;YAAQ;YACZS,KAAK,EAAE0M,KAAK;YACZ5B,SAAS,EAAE2B,OAAO,CAAC3B,SAAS;YAC5B+B,OAAO,EAAEJ,OAAO,CAACI,OAAO;YACxBD,QAAQ,EAAEH,OAAO,CAACG,QAAQ;YAC1B7B,WAAW,EAAE0B,OAAO,CAAC1B;UACvB;QACF,CAAC,EAAE,GAAG,CAAC;;QAEP;QACA,MAAMC,QAAQ,GAAGrI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAAC2D,QAAQ,CAAChJ,SAAS,EAAEgJ,QAAQ,CAAC/I,QAAQ,CAAC;QACtF,MAAMoJ,eAAe,GAAG,IAAIrR,KAAK,CAAC8F,OAAO,CAACuJ,QAAQ,CAACrK,CAAC,EAAE,GAAG,EAAE,CAACqK,QAAQ,CAACnK,CAAC,CAAC;QACvE,MAAMoM,eAAe,GAAGpL,IAAI,CAACC,EAAE,GAAG6K,QAAQ,CAAC7I,OAAO,GAAGjC,IAAI,CAACC,EAAE,GAAG,GAAG;;QAElE;QACA,MAAMoL,WAAW,GAAG5M,cAAc,CAAC0M,eAAe,EAAEN,KAAK,CAAC;QAC1D,MAAM/K,WAAW,GAAGD,cAAc,CAACuL,eAAe,EAAEP,KAAK,CAAC;;QAE1D;QACA,IAAIS,UAAU,GAAGtO,aAAa,CAACsC,GAAG,CAACuL,KAAK,CAAC;;QAEzC;QACA,MAAM3M,aAAa,GAAG2M,KAAK,KAAK3N,gBAAgB;QAEhD,IAAI,CAACoO,UAAU,IAAIlQ,qBAAqB,EAAE;UACxC;UACA,MAAMmQ,eAAe,GAAGnQ,qBAAqB,CAACwD,KAAK,CAAC,CAAC;;UAErD;UACA;UACA2M,eAAe,CAAClJ,QAAQ,CAAClD,GAAG,CAACkM,WAAW,CAACvM,CAAC,EAAE,CAAC,CAAC,EAAEuM,WAAW,CAACnM,CAAC,CAAC;UAC9DqM,eAAe,CAAC/B,QAAQ,CAACxK,CAAC,GAAGc,WAAW;;UAExC;UACAyL,eAAe,CAACC,QAAQ,CAAEC,KAAK,IAAK;YAClC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;cAClC;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;;cAEA;cACA;cACA;;cAEA;;cAEA,MAAMC,WAAW,GAAGH,KAAK,CAACE,QAAQ,CAAC/M,KAAK,CAAC,CAAC;cAC1C6M,KAAK,CAACE,QAAQ,GAAGC,WAAW;;cAE5B;cACA,IAAI1N,aAAa,EAAE;gBACjB0N,WAAW,CAACtH,KAAK,CAACnF,GAAG,CAAC,QAAQ,CAAC;cACjC,CAAC,MAAM;gBACLyM,WAAW,CAACtH,KAAK,CAACnF,GAAG,CAAC,QAAQ,CAAC;cACjC;cACAyM,WAAW,CAACC,QAAQ,GAAG,IAAI/R,KAAK,CAACgS,KAAK,CAAC,QAAQ,CAAC;cAChDF,WAAW,CAACG,WAAW,GAAG,IAAI;cAC9B;cACAH,WAAW,CAACI,WAAW,GAAG,IAAI;YAChC;UACF,CAAC,CAAC;;UAEF;UACA,MAAMC,UAAU,GAAGC,gBAAgB,CAAC,GAAGlM,IAAI,CAACmM,KAAK,CAACrB,QAAQ,CAAC9I,KAAK,CAAC,OAAO,EAAE;YACxEc,eAAe,EAAE5E,aAAa,GAC5B;cAAEkO,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCC,SAAS,EAAE;cAAEJ,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/CrJ,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFoJ,UAAU,CAAC5J,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClC8M,UAAU,CAACQ,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/BR,UAAU,CAACN,QAAQ,CAACe,OAAO,GAAG,GAAG,CAAC,CAAC;UACnCnB,eAAe,CAACrB,GAAG,CAAC+B,UAAU,CAAC;UAE/BzQ,KAAK,CAAC0O,GAAG,CAACqB,eAAe,CAAC;;UAE1B;UACAvO,aAAa,CAACmC,GAAG,CAAC0L,KAAK,EAAE;YACvBxB,KAAK,EAAEkC,eAAe;YACtBpB,UAAU,EAAE3I,IAAI,CAACC,GAAG,CAAC,CAAC;YACtBmH,IAAI,EAAE,GAAG;YAAE;YACX+D,MAAM,EAAEzO,aAAa;YACrB+N,UAAU,EAAEA,UAAU,CAAC;UACzB,CAAC,CAAC;;UAEF;;UAEA;UACA,IAAI/R,KAAK,CAACoL,KAAK,CAACiG,eAAe,CAAClJ,QAAQ,CAAC,CACtCkD,EAAE,CAAC;YAAEvG,CAAC,EAAE;UAAI,CAAC,EAAE,GAAG,CAAC,CACnBwG,MAAM,CAACtL,KAAK,CAACuL,MAAM,CAACC,SAAS,CAACkH,GAAG,CAAC,CAClC9G,KAAK,CAAC,CAAC;;UAEV;UACAyF,eAAe,CAACC,QAAQ,CAAEC,KAAK,IAAK;YAClC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACI,WAAW,EAAE;cAChE,IAAI7R,KAAK,CAACoL,KAAK,CAAC;gBAAEoH,OAAO,EAAE;cAAI,CAAC,CAAC,CAC9BnH,EAAE,CAAC;gBAAEmH,OAAO,EAAE;cAAI,CAAC,EAAE,GAAG,CAAC,CACzBlH,MAAM,CAACtL,KAAK,CAACuL,MAAM,CAACC,SAAS,CAACkH,GAAG,CAAC,CAClChH,QAAQ,CAAC,YAAW;gBACnB6F,KAAK,CAACE,QAAQ,CAACe,OAAO,GAAG,IAAI,CAACA,OAAO;gBACrCjB,KAAK,CAACE,QAAQ,CAACK,WAAW,GAAG,IAAI;cACnC,CAAC,CAAC,CACDlG,KAAK,CAAC,CAAC;YACZ;UACF,CAAC,CAAC;;UAEF;UACA,IAAI5L,KAAK,CAACoL,KAAK,CAAC;YAAEoH,OAAO,EAAE;UAAI,CAAC,CAAC,CAC9BnH,EAAE,CAAC;YAAEmH,OAAO,EAAE;UAAI,CAAC,EAAE,GAAG,CAAC,CACzBlH,MAAM,CAACtL,KAAK,CAACuL,MAAM,CAACC,SAAS,CAACkH,GAAG,CAAC,CAClChH,QAAQ,CAAC,YAAW;YACnBqG,UAAU,CAACN,QAAQ,CAACe,OAAO,GAAG,IAAI,CAACA,OAAO;YAC1CT,UAAU,CAACN,QAAQ,CAACK,WAAW,GAAG,IAAI;UACxC,CAAC,CAAC,CACDlG,KAAK,CAAC,CAAC;;UAEV;UACA,IAAI5H,aAAa,EAAE;YACjBvD,gBAAgB,GAAG4Q,eAAe;YAClC1J,eAAe,CAACiJ,QAAQ,CAAC;YACzBhO,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE8N,KAAK,CAAC;UACjC;QACF,CAAC,MAAM,IAAIS,UAAU,EAAE;UACrB;UACA,MAAMuB,gBAAgB,GAAGpO,cAAc,CAAC4M,WAAW,EAAER,KAAK,CAAC;UAC3D,MAAM3K,gBAAgB,GAAGL,cAAc,CAACC,WAAW,EAAE+K,KAAK,CAAC;;UAE3D;UACAS,UAAU,CAACjC,KAAK,CAAChH,QAAQ,CAACwD,IAAI,CAACgH,gBAAgB,CAAC;UAChDvB,UAAU,CAACjC,KAAK,CAACG,QAAQ,CAACxK,CAAC,GAAGkB,gBAAgB;UAC9CoL,UAAU,CAACjC,KAAK,CAAC9C,YAAY,CAAC,CAAC;UAC/B+E,UAAU,CAACjC,KAAK,CAAC7C,iBAAiB,CAAC,IAAI,CAAC;UACxC8E,UAAU,CAACnB,UAAU,GAAG3I,IAAI,CAACC,GAAG,CAAC,CAAC;UAClC6J,UAAU,CAACqB,MAAM,GAAGzO,aAAa,CAAC,CAAC;;UAEnC;UACA,IAAIoN,UAAU,CAACW,UAAU,EAAE;YACzBX,UAAU,CAACW,UAAU,CAACN,QAAQ,CAACpB,GAAG,CAACuC,OAAO,CAAC,CAAC;YAC5CxB,UAAU,CAACjC,KAAK,CAACqB,MAAM,CAACY,UAAU,CAACW,UAAU,CAAC;UAChD;;UAEA;UACA,MAAMA,UAAU,GAAGC,gBAAgB,CAAC,GAAGlM,IAAI,CAACmM,KAAK,CAACrB,QAAQ,CAAC9I,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE;YAC9Ec,eAAe,EAAE5E,aAAa,GAC5B;cAAEkO,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCC,SAAS,EAAE;cAAEJ,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/CrJ,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFoJ,UAAU,CAAC5J,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACnC8M,UAAU,CAACQ,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/BnB,UAAU,CAACjC,KAAK,CAACa,GAAG,CAAC+B,UAAU,CAAC;UAChCX,UAAU,CAACW,UAAU,GAAGA,UAAU;;UAElC;;UAEA;UACA,IAAI/N,aAAa,EAAE;YACjBvD,gBAAgB,GAAG2Q,UAAU,CAACjC,KAAK;YACnCxH,eAAe,CAACiJ,QAAQ,CAAC;UAC3B;QACF;;QAEA;QACA,MAAMrJ,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;QACtB,MAAM2I,iBAAiB,GAAG,IAAI,CAAC,CAAC;;QAEhCpN,aAAa,CAACwL,OAAO,CAAC,CAACiC,SAAS,EAAE/B,EAAE,KAAK;UACvC,MAAMqE,mBAAmB,GAAGtL,GAAG,GAAGgJ,SAAS,CAACN,UAAU;;UAEtD;UACA,IAAI4C,mBAAmB,GAAG3C,iBAAiB,GAAG,GAAG,IAAI2C,mBAAmB,IAAI3C,iBAAiB,EAAE;YAC7F;YACA,MAAMsC,OAAO,GAAG,CAAC;YAEjBjC,SAAS,CAACpB,KAAK,CAACmC,QAAQ,CAAEC,KAAK,IAAK;cAClC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClC;gBACA,IAAIF,KAAK,CAACE,QAAQ,CAACI,WAAW,KAAKiB,SAAS,EAAE;kBAC5CvB,KAAK,CAACE,QAAQ,CAACsB,mBAAmB,GAAGxB,KAAK,CAACE,QAAQ,CAACI,WAAW,IAAI,KAAK;kBACxEN,KAAK,CAACE,QAAQ,CAACuB,eAAe,GAAGzB,KAAK,CAACE,QAAQ,CAACe,OAAO,IAAI,GAAG;gBAChE;;gBAEA;gBACAjB,KAAK,CAACE,QAAQ,CAACI,WAAW,GAAG,IAAI;gBACjCN,KAAK,CAACE,QAAQ,CAACe,OAAO,GAAGA,OAAO;gBAChCjB,KAAK,CAACE,QAAQ,CAACK,WAAW,GAAG,IAAI;cACnC;YACF,CAAC,CAAC;;YAEF;YACA,IAAIvB,SAAS,CAACwB,UAAU,EAAE;cACxBxB,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACe,OAAO,GAAGA,OAAO;cAC/CjC,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACK,WAAW,GAAG,IAAI;YAClD;UACF;UACA;UAAA,KACK,IAAIe,mBAAmB,GAAG3C,iBAAiB,EAAE;YAChD;YACA,IAAIK,SAAS,CAACwB,UAAU,EAAE;cACxBxB,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACpB,GAAG,CAACuC,OAAO,CAAC,CAAC;cAC3CrC,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACmB,OAAO,CAAC,CAAC;cACvCrC,SAAS,CAACpB,KAAK,CAACqB,MAAM,CAACD,SAAS,CAACwB,UAAU,CAAC;YAC9C;YAEAxB,SAAS,CAACpB,KAAK,CAACmC,QAAQ,CAAEC,KAAK,IAAK;cAClC,IAAIA,KAAK,CAACC,MAAM,EAAE;gBAChB,IAAID,KAAK,CAACE,QAAQ,EAAE;kBAClB,IAAI9N,KAAK,CAACC,OAAO,CAAC2N,KAAK,CAACE,QAAQ,CAAC,EAAE;oBACjCF,KAAK,CAACE,QAAQ,CAACnD,OAAO,CAAC2E,CAAC,IAAIA,CAAC,CAACL,OAAO,CAAC,CAAC,CAAC;kBAC1C,CAAC,MAAM;oBACLrB,KAAK,CAACE,QAAQ,CAACmB,OAAO,CAAC,CAAC;kBAC1B;gBACF;gBACA,IAAIrB,KAAK,CAAC2B,QAAQ,EAAE3B,KAAK,CAAC2B,QAAQ,CAACN,OAAO,CAAC,CAAC;cAC9C;YACF,CAAC,CAAC;;YAEF;YACAtR,KAAK,CAACkP,MAAM,CAACD,SAAS,CAACpB,KAAK,CAAC;YAC7BrM,aAAa,CAAC2N,MAAM,CAACjC,EAAE,CAAC;YACxB;YACA7M,oBAAoB,CAAC8O,MAAM,CAACjC,EAAE,CAAC;YAC/B3M,oBAAoB,CAAC4O,MAAM,CAACjC,EAAE,CAAC;YAE/B5L,OAAO,CAACC,GAAG,CAAC,mBAAmB2L,EAAE,EAAE,CAAC;UACtC;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAI5D,KAAK,KAAK9I,WAAW,CAACS,IAAI,EAAE;QAC9B;;QAEA,IAAI;UACF,MAAMoL,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;UAEnC;UACA,MAAMK,SAAS,GAAGJ,OAAO,CAACK,GAAG;UAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;UAEnC;UACA,MAAMC,aAAa,GAAGpL,gBAAgB,CAACqC,GAAG,CAAC2I,SAAS,CAAC;;UAErD;UACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;YACrD;YACA;YACA;YACA;YACA;YACA;UACF;;UAEA;UACApL,gBAAgB,CAACkC,GAAG,CAAC8I,SAAS,EAAEE,gBAAgB,CAAC;;UAEjD;UACA,IAAIN,OAAO,CAACnK,IAAI,IAAImK,OAAO,CAACnK,IAAI,CAACqJ,aAAa,IAAIlJ,KAAK,CAACC,OAAO,CAAC+J,OAAO,CAACnK,IAAI,CAACqJ,aAAa,CAAC,EAAE;YAC3Fc,OAAO,CAACnK,IAAI,CAACqJ,aAAa,CAACyB,OAAO,CAAC1B,YAAY,IAAI;cACjD,MAAMnD,OAAO,GAAGmD,YAAY,CAACnD,OAAO;cAEpC,IAAI,CAACA,OAAO,EAAE;gBACZ7G,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAE0I,YAAY,CAAC;gBAC/C;cACF;;cAEA;;cAEA;cACA,IAAIA,YAAY,CAACjD,MAAM,IAAIhG,KAAK,CAACC,OAAO,CAACgJ,YAAY,CAACjD,MAAM,CAAC,EAAE;gBAC7D;gBACA,MAAMwJ,UAAU,GAAG,EAAE;gBAErBvG,YAAY,CAACjD,MAAM,CAAC2E,OAAO,CAAC8E,KAAK,IAAI;kBACnC;kBACA,IAAI,CAACA,KAAK,CAACC,OAAO,EAAE;oBAClBzQ,OAAO,CAACsB,KAAK,CAAC,gBAAgB,EAAEkP,KAAK,CAAC;oBACtC;kBACF;kBAEA,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC;kBACxC;kBACA,MAAMC,SAAS,GAAGH,KAAK,CAACI,YAAY,GAClCC,oBAAoB,CAACL,KAAK,CAACI,YAAY,CAAC,GACxCE,iBAAiB,CAACL,OAAO,CAAC;;kBAE5B;kBACA,MAAMM,UAAU,GAAGP,KAAK,CAACQ,YAAY,IAAI,GAAG,CAAC,CAAC;kBAC9C,MAAMC,UAAU,GAAGC,QAAQ,CAACV,KAAK,CAACS,UAAU,CAAC,IAAI,CAAC;;kBAElD;;kBAEA;kBACA,MAAME,SAAS,GAAG;oBAChBV,OAAO;oBACPE,SAAS;oBACTK,YAAY,EAAED,UAAU;oBACxBE;kBACF,CAAC;;kBAED;kBACAV,UAAU,CAACa,IAAI,CAACD,SAAS,CAAC;;kBAE1B;kBACA;kBACA,IAAIE,eAAe,GAAGC,MAAM,CAACzK,OAAO,CAAC;kBACrC,IAAI0K,iBAAiB,GAAGlR,gBAAgB,CAACmC,GAAG,CAAC6O,eAAe,CAAC;kBAE7D,IAAI,CAACE,iBAAiB,EAAE;oBACtB;oBACAF,eAAe,GAAGH,QAAQ,CAACrK,OAAO,CAAC;oBACnC0K,iBAAiB,GAAGlR,gBAAgB,CAACmC,GAAG,CAAC6O,eAAe,CAAC;kBAC3D;kBAEA,IAAIE,iBAAiB,EAAE;oBACrB;oBACAC,wBAAwB,CAACD,iBAAiB,EAAEJ,SAAS,CAAC;;oBAEtD;oBACA,IAAI3K,oBAAoB,IAAIA,oBAAoB,CAACK,OAAO,KAAKA,OAAO,EAAE;sBACpEF,sBAAsB,CAAC8K,IAAI,KAAK;wBAC9B,GAAGA,IAAI;wBACP7K,OAAO,EAAE,IAAI;wBACb6J,OAAO;wBACPE,SAAS;wBACT3E,KAAK,EAAE+E,UAAU;wBACjBE;sBACF,CAAC,CAAC,CAAC;oBACL;kBACF,CAAC,MAAM;oBACL;kBAAA;gBAEJ,CAAC,CAAC;;gBAEF;gBACA,IAAIS,QAAQ,GAAG,IAAI;gBACnB;gBACA,MAAMC,KAAK,GAAGL,MAAM,CAACzK,OAAO,CAAC;gBAC7B,IAAIxG,gBAAgB,CAACiC,GAAG,CAACqP,KAAK,CAAC,EAAE;kBAC/BD,QAAQ,GAAGC,KAAK;gBAClB,CAAC,MAAM;kBACL;kBACA,MAAMC,KAAK,GAAGV,QAAQ,CAACrK,OAAO,CAAC;kBAC/B,IAAIxG,gBAAgB,CAACiC,GAAG,CAACsP,KAAK,CAAC,EAAE;oBAC/BF,QAAQ,GAAGE,KAAK;kBAClB;gBACF;gBAEA,IAAIF,QAAQ,KAAK,IAAI,EAAE;kBACrB;kBACApR,kBAAkB,CAAC+B,GAAG,CAACqP,QAAQ,EAAE;oBAC/BG,UAAU,EAAEnN,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtBoC,MAAM,EAAEwJ;kBACV,CAAC,CAAC;kBACFvQ,OAAO,CAACC,GAAG,CAAC,aAAayR,QAAQ,KAAK,OAAOA,QAAQ,aAAa,CAAC;;kBAEnE;kBACA,IAAItS,MAAM,CAAC4H,mBAAmB,KAC1B5H,MAAM,CAAC4H,mBAAmB,CAACkB,OAAO,KAAKwJ,QAAQ,IAC/CtS,MAAM,CAAC4H,mBAAmB,CAACkB,OAAO,KAAKoJ,MAAM,CAACI,QAAQ,CAAC,IACvDtS,MAAM,CAAC4H,mBAAmB,CAACkB,OAAO,KAAKgJ,QAAQ,CAACQ,QAAQ,CAAC,CAAC,EAAE;oBAE9D1R,OAAO,CAACC,GAAG,CAAC,eAAeyR,QAAQ,aAAa,CAAC;oBACjD;oBACAtS,MAAM,CAAC4H,mBAAmB,CAACkB,OAAO,GAAGwJ,QAAQ;;oBAE7C;oBACA,IAAItS,MAAM,CAAC6H,0BAA0B,IAAI,CAAC7H,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,EAAE;sBACnFlI,OAAO,CAACC,GAAG,CAAC,SAASyR,QAAQ,aAAa,CAAC;sBAC3CI,UAAU,CAAC,MAAM;wBACf1S,MAAM,CAAC2S,qBAAqB,CAACL,QAAQ,CAAC;sBACxC,CAAC,EAAE,GAAG,CAAC;oBACT;kBACF;gBACF,CAAC,MAAM;kBACL;kBACApR,kBAAkB,CAAC+B,GAAG,CAACwE,OAAO,EAAE;oBAC9BgL,UAAU,EAAEnN,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtBoC,MAAM,EAAEwJ;kBACV,CAAC,CAAC;kBACF;gBACF;cACF,CAAC,MAAM;gBACLvQ,OAAO,CAACsB,KAAK,CAAC,eAAe,EAAE0I,YAAY,CAAC;cAC9C;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACLhK,OAAO,CAACsB,KAAK,CAAC,oCAAoC,EAAEyJ,OAAO,CAAC;UAC9D;QACF,CAAC,CAAC,OAAOzJ,KAAK,EAAE;UACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEA,KAAK,EAAEwJ,OAAO,CAAC;QAC9C;MACF;;MAEA;MACA,IAAI9C,KAAK,KAAK9I,WAAW,CAACQ,GAAG,IAAIqL,OAAO,CAACe,IAAI,KAAK,KAAK,EAAE;QACvD;;QAEA;QACA1M,MAAM,CAAC+O,WAAW,CAAC;UACjBrC,IAAI,EAAE,KAAK;UACXlL,IAAI,EAAEmK,OAAO,CAACnK;QAChB,CAAC,EAAE,GAAG,CAAC;QAEP,MAAMoR,OAAO,GAAGjH,OAAO,CAACnK,IAAI;QAC5B,MAAMqR,KAAK,GAAGD,OAAO,CAACC,KAAK;QAC3B,MAAMC,MAAM,GAAGF,OAAO,CAACG,IAAI,IAAI,EAAE;QAEjCD,MAAM,CAACxG,OAAO,CAAC0G,KAAK,IAAI;UACtB,MAAMC,OAAO,GAAGD,KAAK,CAACE,KAAK;UAC3B,MAAMC,SAAS,GAAGH,KAAK,CAACG,SAAS;UACjC,MAAMC,WAAW,GAAGJ,KAAK,CAACI,WAAW;UACrC,MAAMC,SAAS,GAAGL,KAAK,CAACK,SAAS;UACjC,MAAMC,OAAO,GAAGN,KAAK,CAACM,OAAO;;UAE7B;UACA,MAAMrG,QAAQ,GAAGrI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAC7CC,UAAU,CAAC0H,OAAO,CAACW,OAAO,CAAC,EAC3BrI,UAAU,CAAC0H,OAAO,CAACY,MAAM,CAC3B,CAAC;;UAED;UACA,IAAIC,WAAW,GAAG,EAAE;UACpB,IAAIC,YAAY,GAAG,EAAE;UAErB,QAAOP,SAAS;YACd,KAAK,KAAK;cAAG;cACXM,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,QAAQ;cACtBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,MAAM;cAAE;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF;cACED,WAAW,GAAGL,WAAW,IAAI,MAAM;cACnCM,YAAY,GAAG,SAAS;UAC5B;;UAEA;UACAC,iBAAiB,CAAC1G,QAAQ,EAAEwG,WAAW,EAAEC,YAAY,CAAC;;UAEtD;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAI9K,KAAK,KAAK9I,WAAW,CAACR,KAAK,IAAIqM,OAAO,CAACe,IAAI,KAAK,OAAO,EAAE;QAC3D;;QAEA,MAAMkH,SAAS,GAAGjI,OAAO,CAACnK,IAAI;QAC9B,MAAMqS,OAAO,GAAGD,SAAS,CAACC,OAAO;QACjC,MAAMC,SAAS,GAAGF,SAAS,CAACE,SAAS;QACrC,MAAMC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAM5N,QAAQ,GAAG;UACfN,QAAQ,EAAEqF,UAAU,CAAC0I,SAAS,CAAC9E,OAAO,CAAC;UACvClJ,SAAS,EAAEsF,UAAU,CAAC0I,SAAS,CAAC/E,QAAQ;QAC1C,CAAC;;QAED;QACA,MAAM5B,QAAQ,GAAGrI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAAC9E,QAAQ,CAACP,SAAS,EAAEO,QAAQ,CAACN,QAAQ,CAAC;;QAEtF;QACA,QAAOiO,SAAS;UACd,KAAK,GAAG;YAAG;YACTH,iBAAiB,CAAC1G,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;YAClD;UACF,KAAK,KAAK;YAAG;YACX0G,iBAAiB,CAAC1G,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX0G,iBAAiB,CAAC1G,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;YAC/C;UACF,KAAK,IAAI;YAAG;YACV,MAAM+G,UAAU,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAE;YAC1CN,iBAAiB,CAAC1G,QAAQ,EAAE,KAAK+G,UAAU,MAAM,EAAE,SAAS,CAAC;YAC7D;UACF,KAAK,IAAI;YAAG;YACVL,iBAAiB,CAAC1G,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV0G,iBAAiB,CAAC1G,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YAAG;YACZ0G,iBAAiB,CAAC1G,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV0G,iBAAiB,CAAC1G,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV0G,iBAAiB,CAAC1G,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX,MAAMiH,YAAY,GAAGN,SAAS,CAACK,UAAU,CAAC,CAAE;YAC5C,MAAME,QAAQ,GAAGP,SAAS,CAACQ,UAAU,CAAC,CAAM;YAC5CT,iBAAiB,CAAC1G,QAAQ,EAAE,QAAQoH,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,EAAE,SAAS,CAAC;YAC/F;QACJ;QAEA;MACF;MACA;MACA;MACA;MACA;MACA;MACA;IAEF,CAAC,CAAC,OAAOjS,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEwJ,OAAO,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAM4I,cAAc,GAAGA,CAAA,KAAM;IAC3B1T,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAM0T,KAAK,GAAG,QAAQzU,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnES,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE0T,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChB9T,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED2T,EAAE,CAACG,SAAS,GAAI3B,KAAK,IAAK;MACxB,IAAI;QACF,MAAMtH,OAAO,GAAGE,IAAI,CAACC,KAAK,CAACmH,KAAK,CAACxR,IAAI,CAAC;;QAEtC;QACA,IAAIkK,OAAO,CAACgB,IAAI,KAAK,SAAS,EAAE;UAC9B9L,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE6K,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACgB,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIhB,OAAO,CAACgB,IAAI,KAAK,SAAS,IAAIhB,OAAO,CAAC9C,KAAK,IAAI8C,OAAO,CAACC,OAAO,EAAE;UAClE;UACAF,iBAAiB,CAACC,OAAO,CAAC9C,KAAK,EAAEgD,IAAI,CAACgJ,SAAS,CAAClJ,OAAO,CAACC,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOzJ,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDsS,EAAE,CAACK,OAAO,GAAI3S,KAAK,IAAK;MACtBtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDsS,EAAE,CAACM,OAAO,GAAG,MAAM;MACjBlU,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACA6R,UAAU,CAAC4B,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACAvP,aAAa,CAAC+D,OAAO,GAAG0L,EAAE;EAC5B,CAAC;EAEDhX,SAAS,CAAC,MAAM;IACd,IAAI,CAACkH,YAAY,CAACoE,OAAO,EAAE;;IAE3B;IACAiM,aAAa,CAAC,CAAC;;IAEf;IACAzV,KAAK,GAAG,IAAI1B,KAAK,CAACoX,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAIrX,KAAK,CAACsX,iBAAiB,CACxC,EAAE,EACFlV,MAAM,CAACmV,UAAU,GAAGnV,MAAM,CAACoV,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAAC9O,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCgS,MAAM,CAAClL,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB5C,SAAS,CAAC2B,OAAO,GAAGmM,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAIzX,KAAK,CAAC0X,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAACxV,MAAM,CAACmV,UAAU,EAAEnV,MAAM,CAACoV,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAAC1V,MAAM,CAAC2V,gBAAgB,CAAC;IAC/CjR,YAAY,CAACoE,OAAO,CAAC8M,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAIlY,KAAK,CAACmY,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5DzW,KAAK,CAAC0O,GAAG,CAAC8H,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAIpY,KAAK,CAACqY,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAAC7P,QAAQ,CAAClD,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1C3D,KAAK,CAAC0O,GAAG,CAACgI,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAItY,KAAK,CAACqY,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAAC/P,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3C3D,KAAK,CAAC0O,GAAG,CAACkI,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAIvY,KAAK,CAACwY,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAAChQ,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCkT,SAAS,CAACE,KAAK,GAAGvS,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7BoS,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAAC9S,QAAQ,GAAG,GAAG;IACxB/D,KAAK,CAAC0O,GAAG,CAACmI,SAAS,CAAC;;IAEpB;IACAlX,QAAQ,GAAG,IAAInB,aAAa,CAACmX,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzD5W,QAAQ,CAACuX,aAAa,GAAG,IAAI;IAC7BvX,QAAQ,CAACwX,aAAa,GAAG,IAAI;IAC7BxX,QAAQ,CAACyX,kBAAkB,GAAG,KAAK;IACnCzX,QAAQ,CAACgL,WAAW,GAAG,EAAE;IACzBhL,QAAQ,CAACiL,WAAW,GAAG,GAAG;IAC1BjL,QAAQ,CAACkL,aAAa,GAAGrG,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC9E,QAAQ,CAACmL,aAAa,GAAG,CAAC;IAC1BnL,QAAQ,CAAC4K,MAAM,CAAC5G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BhE,QAAQ,CAAC+K,MAAM,CAAC,CAAC;;IAEjB;IACApJ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBoU,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBhW,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBkI,SAAS,EAAE,CAAC,CAACA,SAAS,CAAC2B;IACzB,CAAC,CAAC;;IAEF;IACA,MAAM6N,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAIlZ,UAAU,CAAC,CAAC;QACtCkZ,aAAa,CAACC,IAAI,CAChB,GAAGxW,QAAQ,uBAAuB,EACjCyW,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAAC3X,KAAK;;UAE/B;UACA,MAAM6X,gBAAgB,GAAG,IAAIvZ,KAAK,CAACwZ,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAAC5H,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAI9R,KAAK,CAACyZ,oBAAoB,CAAC;kBACjDjP,KAAK,EAAE,QAAQ;kBAAO;kBACtBkP,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIjI,KAAK,CAACE,QAAQ,CAACpB,GAAG,EAAE;kBACtBqB,WAAW,CAACrB,GAAG,GAAGkB,KAAK,CAACE,QAAQ,CAACpB,GAAG;gBACtC;;gBAEA;gBACAkB,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5B9O,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE0O,KAAK,CAACxE,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMmM,YAAY,CAACO,QAAQ,CAAC9J,MAAM,GAAG,CAAC,EAAE;YACtC,MAAM4B,KAAK,GAAG2H,YAAY,CAACO,QAAQ,CAAC,CAAC,CAAC;YACtCN,gBAAgB,CAACnJ,GAAG,CAACuB,KAAK,CAAC;UAC7B;;UAEA;UACAjQ,KAAK,CAAC0O,GAAG,CAACmJ,gBAAgB,CAAC;;UAE3B;UACA1Y,gBAAgB,GAAG0Y,gBAAgB;UAEnCvW,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9B6W,kBAAkB,CAAC,IAAI,CAAC;UACxBb,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAQ,GAAG,IAAK;UACP/W,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC8W,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAErU,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDsT,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMgB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA;;QAEA;QACAxD,cAAc,CAAC,CAAC;;QAEhB;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAEF,CAAC,CAAC,OAAOpS,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAM6V,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAIrB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMoB,WAAW,GAAIC,WAAW,IAAK;UACnCvX,OAAO,CAACC,GAAG,CAAC,WAAWmX,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAIva,UAAU,CAAC,CAAC;UAC/Bua,MAAM,CAACpB,IAAI,CACTgB,GAAG,EACFf,IAAI,IAAK;YACRrW,OAAO,CAACC,GAAG,CAAC,WAAWmX,GAAG,EAAE,CAAC;YAC7BnB,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAU,GAAG,IAAK;YACP/W,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC8W,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAErU,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAtB,KAAK,IAAK;YACTtB,OAAO,CAACsB,KAAK,CAAC,SAAS8V,GAAG,EAAE,EAAE9V,KAAK,CAAC;YACpC,IAAIiW,WAAW,GAAG,CAAC,EAAE;cACnBvX,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3B6R,UAAU,CAAC,MAAMwF,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACLrB,MAAM,CAAC5U,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAEDgW,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAIva,UAAU,CAAC,CAAC;IAC/Bua,MAAM,CAACpB,IAAI,CACT,GAAGxW,QAAQ,4BAA4B,EACvC,MAAOyW,IAAI,IAAK;MACd,IAAI;QACF,MAAM9J,KAAK,GAAG8J,IAAI,CAAC3X,KAAK;QACxB6N,KAAK,CAACI,KAAK,CAACtK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBkK,KAAK,CAAChH,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAE3B;QACA,IAAI3D,KAAK,EAAE;UACXA,KAAK,CAAC0O,GAAG,CAACb,KAAK,CAAC;;UAEhB;UACA,MAAM2K,eAAe,CAAC,CAAC;QACvB,CAAC,MAAM;UACLlX,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC;QAChC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACAyV,GAAG,IAAK;MACP/W,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC8W,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAErU,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAtB,KAAK,IAAK;MACTtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtB,OAAO,CAACsB,KAAK,CAAC,OAAO,EAAE;QACrBmW,IAAI,EAAEnW,KAAK,CAACwK,IAAI;QAChB4L,IAAI,EAAEpW,KAAK,CAACwJ,OAAO;QACnB6M,KAAK,EAAE,GAAG/X,QAAQ,4BAA4B;QAC9CgY,KAAK,EAAE,GAAGhY,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMiY,OAAO,GAAGA,CAAA,KAAM;MACpBzT,iBAAiB,CAAC8D,OAAO,GAAG4P,qBAAqB,CAACD,OAAO,CAAC;;MAE1D;MACAza,KAAK,CAACgM,MAAM,CAAC,CAAC;;MAEd;MACA,MAAM2O,WAAW,GAAGrT,IAAI,CAACC,GAAG,CAAC,CAAC;MAC9B;MACA,MAAMqT,SAAS,GAAEzX,KAAK,CAAC0X,QAAQ,CAAC,CAAC;MACjCxT,oBAAoB,CAACyD,OAAO,GAAG6P,WAAW;MAE1CnT,qBAAqB,CAACsD,OAAO,CAACwD,OAAO,CAAEkB,KAAK,IAAK;QAC/CA,KAAK,CAACxD,MAAM,CAAC4O,SAAS,CAAC;MACzB,CAAC,CAAC;MAEF,IAAI5Z,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAAC8J,OAAO,GAAG,KAAK;;QAExB;QACA,MAAM+P,UAAU,GAAGra,gBAAgB,CAAC0H,QAAQ,CAACzD,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMqW,eAAe,GAAGta,gBAAgB,CAAC6O,QAAQ,CAACxK,CAAC;;QAEnD;QACA;QACA,MAAMkW,gBAAgB,GAAG,EAAED,eAAe,GAAGjV,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;;QAEnE;QACA,MAAMkV,YAAY,GAAG,IAAIrb,KAAK,CAAC8F,OAAO,CACpC,CAAC,EAAE,GAAGI,IAAI,CAACoV,GAAG,CAACF,gBAAgB,CAAC,EAChC,GAAG,EACH,CAAC,EAAE,GAAGlV,IAAI,CAACqV,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA,MAAMI,oBAAoB,GAAGN,UAAU,CAACpW,KAAK,CAAC,CAAC,CAACsL,GAAG,CAACiL,YAAY,CAAC;QACjE,MAAMI,YAAY,GAAGP,UAAU,CAACpW,KAAK,CAAC,CAAC;;QAEvC;QACA,IAAI,CAACwC,kBAAkB,CAAC4D,OAAO,EAAE;UAC/B5D,kBAAkB,CAAC4D,OAAO,GAAGsQ,oBAAoB,CAAC1W,KAAK,CAAC,CAAC;QAC3D;QAEA,IAAI,CAACyC,gBAAgB,CAAC2D,OAAO,EAAE;UAC7B3D,gBAAgB,CAAC2D,OAAO,GAAGuQ,YAAY,CAAC3W,KAAK,CAAC,CAAC;QACjD;;QAEA;QACAwC,kBAAkB,CAAC4D,OAAO,CAACwQ,IAAI,CAACF,oBAAoB,EAAE,CAAC,GAAGhU,eAAe,CAAC;QAC1ED,gBAAgB,CAAC2D,OAAO,CAACwQ,IAAI,CAACD,YAAY,EAAE,CAAC,GAAGjU,eAAe,CAAC;;QAEhE;QACA6P,MAAM,CAAC9O,QAAQ,CAACwD,IAAI,CAACzE,kBAAkB,CAAC4D,OAAO,CAAC;;QAEhD;QACAmM,MAAM,CAAC9L,EAAE,CAAClG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACAgS,MAAM,CAAClL,MAAM,CAAC5E,gBAAgB,CAAC2D,OAAO,CAAC;;QAEvC;QACAmM,MAAM,CAACsE,sBAAsB,CAAC,CAAC;QAC/BtE,MAAM,CAAC5K,YAAY,CAAC,CAAC;QACrB4K,MAAM,CAAC3K,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACArL,QAAQ,CAAC8J,OAAO,GAAG,KAAK;;QAExB;QACA9J,QAAQ,CAAC4K,MAAM,CAACF,IAAI,CAACxE,gBAAgB,CAAC2D,OAAO,CAAC;QAC9C7J,QAAQ,CAAC+K,MAAM,CAAC,CAAC;QAEjBpJ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnB2Y,IAAI,EAAEV,UAAU,CAACvN,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAE2J,MAAM,CAAC9O,QAAQ,CAACoF,OAAO,CAAC,CAAC;UAC/BkO,IAAI,EAAEtU,gBAAgB,CAAC2D,OAAO,CAACyC,OAAO,CAAC,CAAC;UACxCmO,IAAI,EAAEzE,MAAM,CAAC0E,iBAAiB,CAAC,IAAI/b,KAAK,CAAC8F,OAAO,CAAC,CAAC,CAAC,CAAC6H,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIvM,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAkG,kBAAkB,CAAC4D,OAAO,GAAG,IAAI;QACjC3D,gBAAgB,CAAC2D,OAAO,GAAG,IAAI;;QAE/B;QACA7J,QAAQ,CAAC8J,OAAO,GAAG,IAAI;;QAEvB;QACAkM,MAAM,CAAC9L,EAAE,CAAClG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAIa,IAAI,CAACK,GAAG,CAAC8Q,MAAM,CAAC9O,QAAQ,CAACrD,CAAC,CAAC,GAAG,EAAE,EAAE;UACpCmS,MAAM,CAAC9O,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BhE,QAAQ,CAAC4K,MAAM,CAAC5G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BgS,MAAM,CAAClL,MAAM,CAAC9K,QAAQ,CAAC4K,MAAM,CAAC;UAC9B5K,QAAQ,CAAC+K,MAAM,CAAC,CAAC;QACnB;;QAEA;QACA;QACAiL,MAAM,CAAC5K,YAAY,CAAC,CAAC;QACrB4K,MAAM,CAAC3K,iBAAiB,CAAC,IAAI,CAAC;MAEhC,CAAC,MAAM,IAAItL,UAAU,KAAK,cAAc,EAAE;QACxC;QACAkG,kBAAkB,CAAC4D,OAAO,GAAG,IAAI;QACjC3D,gBAAgB,CAAC2D,OAAO,GAAG,IAAI;;QAE/B;QACA7J,QAAQ,CAAC+K,MAAM,CAAC,CAAC;MACnB;MAEA,IAAI/K,QAAQ,EAAEA,QAAQ,CAAC+K,MAAM,CAAC,CAAC;MAC/B,IAAI1K,KAAK,IAAI2V,MAAM,EAAE;QACnBI,QAAQ,CAACuE,MAAM,CAACta,KAAK,EAAE2V,MAAM,CAAC;MAChC;IACF,CAAC;IAEDwD,OAAO,CAAC,CAAC;;IAET;IACA,MAAMoB,YAAY,GAAGA,CAAA,KAAM;MACzB5E,MAAM,CAAC6E,MAAM,GAAG9Z,MAAM,CAACmV,UAAU,GAAGnV,MAAM,CAACoV,WAAW;MACtDH,MAAM,CAACsE,sBAAsB,CAAC,CAAC;MAC/BlE,QAAQ,CAACG,OAAO,CAACxV,MAAM,CAACmV,UAAU,EAAEnV,MAAM,CAACoV,WAAW,CAAC;IACzD,CAAC;IACDpV,MAAM,CAAC+Z,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACA7Z,MAAM,CAACga,aAAa,GAAG,MAAM;MAC3B,IAAI7S,SAAS,CAAC2B,OAAO,EAAE;QACrB3B,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzCkE,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC5C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC;QAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAIrL,QAAQ,EAAE;UACZA,QAAQ,CAAC4K,MAAM,CAAC5G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BhE,QAAQ,CAAC8J,OAAO,GAAG,IAAI;UACvB9J,QAAQ,CAAC+K,MAAM,CAAC,CAAC;QACnB;QAEAhL,UAAU,GAAG,QAAQ;QACrB4B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAImE,iBAAiB,CAAC8D,OAAO,EAAE;QAC7BmR,oBAAoB,CAACjV,iBAAiB,CAAC8D,OAAO,CAAC;QAC/C9D,iBAAiB,CAAC8D,OAAO,GAAG,IAAI;MAClC;;MAEA;MACA,IAAIlK,oBAAoB,EAAE;QACxBsb,aAAa,CAACtb,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;;MAEA;MACA,IAAImG,aAAa,CAAC+D,OAAO,EAAE;QACzB/D,aAAa,CAAC+D,OAAO,CAACqR,KAAK,CAAC,CAAC;QAC7BpV,aAAa,CAAC+D,OAAO,GAAG,IAAI;MAC9B;;MAEA;MACA9I,MAAM,CAACoa,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;;MAElD;MACA,IAAIxE,QAAQ,IAAI3Q,YAAY,CAACoE,OAAO,EAAE;QACpCpE,YAAY,CAACoE,OAAO,CAACuR,WAAW,CAAChF,QAAQ,CAACQ,UAAU,CAAC;QACrDR,QAAQ,CAACzE,OAAO,CAAC,CAAC;MACpB;;MAEA;MACA,IAAI9P,aAAa,EAAE;QACjBA,aAAa,CAACwL,OAAO,CAAC,CAACiC,SAAS,EAAE/B,EAAE,KAAK;UACvC,IAAI+B,SAAS,CAACpB,KAAK,IAAI7N,KAAK,EAAE;YAC5BA,KAAK,CAACkP,MAAM,CAACD,SAAS,CAACpB,KAAK,CAAC;UAC/B;QACF,CAAC,CAAC;QACFrM,aAAa,CAACwZ,KAAK,CAAC,CAAC;MACvB;;MAEA;MACArZ,gBAAgB,CAACqL,OAAO,CAAEiO,QAAQ,IAAK;QACrC,IAAIjb,KAAK,IAAIib,QAAQ,CAACpN,KAAK,EAAE;UAC3B7N,KAAK,CAACkP,MAAM,CAAC+L,QAAQ,CAACpN,KAAK,CAAC;QAC9B;MACF,CAAC,CAAC;MACFlM,gBAAgB,CAACqZ,KAAK,CAAC,CAAC;MACxBpZ,kBAAkB,CAACoZ,KAAK,CAAC,CAAC;;MAE1B;;MAEA;MACAhb,KAAK,GAAG,IAAI;MACZL,QAAQ,GAAG,IAAI;MACfC,qBAAqB,GAAG,IAAI;MAC5BC,qBAAqB,GAAG,IAAI;MAC5BC,oBAAoB,GAAG,IAAI;MAC3BC,0BAA0B,GAAG,IAAI;MACjCZ,gBAAgB,GAAG,IAAI;MAEvBmC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArD,SAAS,CAAC,MAAM;IACd;IACA6D,qBAAqB,CAAC,CAAC;;IAEvB;IACA,MAAMmZ,uBAAuB,GAAGA,CAAA,KAAM;MACpC5Z,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCQ,qBAAqB,CAAC,CAAC;IACzB,CAAC;;IAED;IACArB,MAAM,CAAC+Z,gBAAgB,CAAC,oBAAoB,EAAES,uBAAuB,CAAC;;IAEtE;IACA,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnCrZ,qBAAqB,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC;;IAET;IACA,OAAO,MAAM;MACXrB,MAAM,CAACoa,mBAAmB,CAAC,oBAAoB,EAAEI,uBAAuB,CAAC;MACzEN,aAAa,CAACO,UAAU,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjd,SAAS,CAAC,MAAM;IACd;IACA,IAAI8B,KAAK,IAAIsF,SAAS,CAACkE,OAAO,EAAE;MAC9BlI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA,MAAM8Z,KAAK,GAAGjI,UAAU,CAAC,MAAM;QAC7B,IAAIpT,KAAK,IAAIsF,SAAS,CAACkE,OAAO,EAAE;UAAG;UACjC8R,mBAAmB,CAAChW,SAAS,CAACkE,OAAO,CAAC;QACxC;MACF,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAM+R,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM;MACL/Z,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC,EAAE,CAACvB,KAAK,CAAC,CAAC;;EAEX;EACA9B,SAAS,CAAC,MAAM;IACd,IAAIkH,YAAY,CAACoE,OAAO,EAAE;MACxB;MACA,MAAMgS,WAAW,GAAI9H,KAAK,IAAK;QAC7B,IAAI1T,KAAK,IAAI6H,SAAS,CAAC2B,OAAO,EAAE;UAC9BlI,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEmS,KAAK,CAAC+H,OAAO,EAAE/H,KAAK,CAACgI,OAAO,CAAC;UACpDC,gBAAgB,CAACjI,KAAK,EAAEtO,YAAY,CAACoE,OAAO,EAAExJ,KAAK,EAAE6H,SAAS,CAAC2B,OAAO,EAAEvB,sBAAsB,CAAC;QACjG,CAAC,MAAM;UACL3G,OAAO,CAACsa,IAAI,CAAC,4BAA4B,CAAC;QAC5C;MACF,CAAC;;MAED;MACAxW,YAAY,CAACoE,OAAO,CAACiR,gBAAgB,CAAC,OAAO,EAAEe,WAAW,CAAC;;MAE3D;MACAla,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC6D,YAAY,CAACoE,OAAO,CAAC;;MAEpD;MACA,OAAO,MAAM;QACX,IAAIpE,YAAY,CAACoE,OAAO,EAAE;UACxBpE,YAAY,CAACoE,OAAO,CAACsR,mBAAmB,CAAC,OAAO,EAAEU,WAAW,CAAC;UAC9Dla,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACvB,KAAK,EAAE6H,SAAS,CAAC2B,OAAO,CAAC,CAAC;;EAE9B;EACA,MAAMqS,SAAS,GAAGxd,WAAW,CAAC,MAAM;IAClCiD,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B;EACF,CAAC,EAAE,CAAC6D,YAAY,EAAE8D,aAAa,EAAEvH,gBAAgB,CAAC,CAAC;;EAEnD;EACA,MAAMma,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMlK,QAAQ,GAAG,IAAItT,KAAK,CAACyd,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChD,MAAM5L,QAAQ,GAAG,IAAI7R,KAAK,CAAC0d,iBAAiB,CAAC;MAAElT,KAAK,EAAE;IAAS,CAAC,CAAC;IACjE,MAAM+J,iBAAiB,GAAG,IAAIvU,KAAK,CAAC2d,IAAI,CAACrK,QAAQ,EAAEzB,QAAQ,CAAC;;IAE5D;IACA,MAAM+L,YAAY,GAAG,IAAI5d,KAAK,CAAC6d,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC5D,MAAMC,YAAY,GAAG,IAAI9d,KAAK,CAAC0d,iBAAiB,CAAC;MAAElT,KAAK,EAAE;IAAS,CAAC,CAAC;IACrE,MAAMuT,SAAS,GAAG,IAAI/d,KAAK,CAAC2d,IAAI,CAACC,YAAY,EAAEE,YAAY,CAAC;IAC5DC,SAAS,CAACxV,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAClCkP,iBAAiB,CAACnE,GAAG,CAAC2N,SAAS,CAAC;IAEhC,OAAOxJ,iBAAiB;EAC1B,CAAC;;EAED;EACA,MAAMyJ,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACtc,KAAK,EAAE;;IAEZ;IACA2B,gBAAgB,CAACqL,OAAO,CAAC,CAACiO,QAAQ,EAAE9S,OAAO,KAAK;MAC9C,IAAI8S,QAAQ,CAACpN,KAAK,EAAE;QAClB;QACA,MAAM0O,cAAc,GAAG,IAAIje,KAAK,CAACke,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxD,MAAMC,cAAc,GAAG,IAAIne,KAAK,CAAC0d,iBAAiB,CAAC;UACjDlT,KAAK,EAAE,QAAQ;UAAC;UAChByH,WAAW,EAAE,KAAK;UAClBW,OAAO,EAAE,GAAG;UAAG;UACfwL,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,MAAMC,UAAU,GAAG,IAAIre,KAAK,CAAC2d,IAAI,CAACM,cAAc,EAAEE,cAAc,CAAC;QACjEE,UAAU,CAAC9V,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE;;QAEnC;QACAgZ,UAAU,CAACC,QAAQ,GAAG;UACpBxP,IAAI,EAAE,cAAc;UACpBjF,OAAO,EAAEA,OAAO;UAChBsD,IAAI,EAAEwP,QAAQ,CAAC3P,YAAY,CAACG,IAAI;UAChCoR,aAAa,EAAE;QACjB,CAAC;;QAED;QACA5B,QAAQ,CAACpN,KAAK,CAACa,GAAG,CAACiO,UAAU,CAAC;QAE9Brb,OAAO,CAACC,GAAG,CAAC,OAAO0Z,QAAQ,CAAC3P,YAAY,CAACG,IAAI,KAAKtD,OAAO,aAAa,CAAC;MACzE;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACAjK,SAAS,CAAC,MAAM;IACd;IACA,MAAMmd,KAAK,GAAGjI,UAAU,CAAC,MAAM;MAC7B,IAAIzR,gBAAgB,CAACmb,IAAI,GAAG,CAAC,EAAE;QAC7Bxb,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1B;MACF;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAE;;IAEX,OAAO,MAAMga,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACAnd,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACA,IAAIqK,0BAA0B,CAACiB,OAAO,EAAE;QACtCoR,aAAa,CAACrS,0BAA0B,CAACiB,OAAO,CAAC;QACjDjB,0BAA0B,CAACiB,OAAO,GAAG,IAAI;QACzClI,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC9B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACArD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8J,mBAAmB,CAACE,OAAO,IAAIK,0BAA0B,CAACiB,OAAO,EAAE;MACtEoR,aAAa,CAACrS,0BAA0B,CAACiB,OAAO,CAAC;MACjDjB,0BAA0B,CAACiB,OAAO,GAAG,IAAI;MACzClB,mBAAmB,CAACkB,OAAO,GAAG,IAAI;MAClClI,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACnC;EACF,CAAC,EAAE,CAACyG,mBAAmB,CAACE,OAAO,CAAC,CAAC;;EAEjC;EACAhK,SAAS,CAAC,MAAM;IACd;IACA,IAAIY,iBAAiB,IAAIA,iBAAiB,CAACyM,aAAa,IAAIzM,iBAAiB,CAACyM,aAAa,CAAC8C,MAAM,GAAG,CAAC,EAAE;MACtG;MACA,IAAI,CAACvG,oBAAoB,EAAE;QACzB,MAAMiV,iBAAiB,GAAGje,iBAAiB,CAACyM,aAAa,CAAC,CAAC,CAAC;QAC5DjK,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEwb,iBAAiB,CAACtR,IAAI,CAAC;;QAEjD;QACA,MAAM4P,KAAK,GAAGjI,UAAU,CAAC,MAAM;UAC7BhI,wBAAwB,CAAC2R,iBAAiB,CAACtR,IAAI,CAAC;QAClD,CAAC,EAAE,IAAI,CAAC;QAER,OAAO,MAAM8P,YAAY,CAACF,KAAK,CAAC;MAClC;IACF;EACF,CAAC,EAAE,CAACvc,iBAAiB,EAAEgJ,oBAAoB,CAAC,CAAC;EAE7C,oBACE9I,OAAA,CAAAE,SAAA;IAAAiZ,QAAA,gBACEnZ,OAAA;MAAMge,KAAK,EAAEpU,UAAW;MAAAuP,QAAA,EAAC;IAAK;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrCpe,OAAA,CAACJ,MAAM;MACLoe,KAAK,EAAEvU,uBAAwB;MAC/B4U,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAElS,wBAAyB;MACnCmS,OAAO,EAAEze,iBAAiB,CAACyM,aAAa,CAACwD,GAAG,CAACzD,YAAY,KAAK;QAC5DD,KAAK,EAAEC,YAAY,CAACG,IAAI;QACxB+R,KAAK,EAAElS,YAAY,CAACG;MACtB,CAAC,CAAC,CAAE;MACJqR,IAAI,EAAC,OAAO;MACZW,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACbzW,MAAM,EAAE,IAAI;QACZ0W,SAAS,EAAE;MACb,CAAE;MACFtS,KAAK,EAAEvD,oBAAoB,GAAGA,oBAAoB,CAAC2D,IAAI,GAAG+F;IAAU;MAAAyL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eACFpe,OAAA;MAAK4e,GAAG,EAAExY,YAAa;MAAC4X,KAAK,EAAE;QAAErU,KAAK,EAAE,MAAM;QAAEoF,MAAM,EAAE;MAAO;IAAE;MAAAkP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGnEpV,mBAAmB,CAACE,OAAO,iBAC1BlJ,OAAA;MACEge,KAAK,EAAE;QACLnW,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAACvD,CAAC,IAAI;QAC3CoF,GAAG,EAAE,GAAGV,mBAAmB,CAACnB,QAAQ,CAACrD,CAAC,IAAI;QAC1CwD,SAAS,EAAE,wBAAwB;QACnCC,MAAM,EAAE,IAAI;QACZK,eAAe,EAAE,qBAAqB;QACtCwB,KAAK,EAAE,OAAO;QACdtB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,8BAA8B;QACzCN,OAAO,EAAE,GAAG;QACZwW,QAAQ,EAAE,OAAO;QAAE;QACnBnW,QAAQ,EAAE,MAAM,CAAC;MACnB,CAAE;MAAAyQ,QAAA,GAEDnQ,mBAAmB,CAACI,OAAO,eAC5BpJ,OAAA;QACEge,KAAK,EAAE;UACLnW,QAAQ,EAAE,UAAU;UACpB6B,GAAG,EAAE,KAAK;UACVoV,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,MAAM;UAClBxW,MAAM,EAAE,MAAM;UACduB,KAAK,EAAE,OAAO;UACdpB,QAAQ,EAAE,MAAM;UAChBD,MAAM,EAAE,SAAS;UACjBJ,OAAO,EAAE;QACX,CAAE;QACF2W,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAAChW,sBAAsB,CAAE;QAAAkQ,QAAA,EAC3D;MAED;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAEDpe,OAAA;MAAKge,KAAK,EAAEpW,oBAAqB;MAAAuR,QAAA,gBAC/BnZ,OAAA;QACEge,KAAK,EAAE;UACL,GAAG5V,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFsX,OAAO,EAAEzU,kBAAmB;QAAA4O,QAAA,EAC7B;MAED;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTpe,OAAA;QACEge,KAAK,EAAE;UACL,GAAG5V,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFsX,OAAO,EAAEtU,kBAAmB;QAAAyO,QAAA,EAC7B;MAED;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAAjY,EAAA,CAjvDMJ,WAAW;AAAAmZ,EAAA,GAAXnZ,WAAW;AAkvDjB,SAAS2L,gBAAgBA,CAACyN,IAAI,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAMC,MAAM,GAAG;IACbC,QAAQ,EAAEF,UAAU,CAACE,QAAQ,IAAI,OAAO;IACxC5W,QAAQ,EAAE0W,UAAU,CAAC1W,QAAQ,IAAI,EAAE;IAAE;IACrCqB,UAAU,EAAEqV,UAAU,CAACrV,UAAU,IAAI,MAAM;IAC3CwV,eAAe,EAAEH,UAAU,CAACG,eAAe,IAAI,CAAC;IAChDC,WAAW,EAAEJ,UAAU,CAACI,WAAW,IAAI;MAAE5N,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAI,CAAC;IACnEzJ,eAAe,EAAE8W,UAAU,CAAC9W,eAAe,IAAI;MAAEsJ,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAI,CAAC;IACjFC,SAAS,EAAEoN,UAAU,CAACpN,SAAS,IAAI;MAAEJ,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAI,CAAC;IAC/D1J,OAAO,EAAE+W,UAAU,CAAC/W,OAAO,IAAI;EACjC,CAAC;;EAED;EACA,MAAMoX,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;EAEvC;EACAD,OAAO,CAACE,IAAI,GAAG,GAAGT,MAAM,CAACtV,UAAU,IAAIsV,MAAM,CAAC3W,QAAQ,MAAM2W,MAAM,CAACC,QAAQ,EAAE;;EAE7E;EACA,MAAMS,SAAS,GAAGH,OAAO,CAACI,WAAW,CAACb,IAAI,CAAC,CAACxV,KAAK;;EAEjD;EACA,MAAMA,KAAK,GAAGoW,SAAS,GAAG,CAAC,GAAGV,MAAM,CAAChX,OAAO,GAAG,CAAC,GAAGgX,MAAM,CAACE,eAAe;EACzE,MAAMxQ,MAAM,GAAGsQ,MAAM,CAAC3W,QAAQ,GAAG,CAAC,GAAG2W,MAAM,CAAChX,OAAO,GAAG,CAAC,GAAGgX,MAAM,CAACE,eAAe;EAEhFE,MAAM,CAAC9V,KAAK,GAAGA,KAAK;EACpB8V,MAAM,CAAC1Q,MAAM,GAAGA,MAAM;;EAEtB;EACA6Q,OAAO,CAACE,IAAI,GAAG,GAAGT,MAAM,CAACtV,UAAU,IAAIsV,MAAM,CAAC3W,QAAQ,MAAM2W,MAAM,CAACC,QAAQ,EAAE;EAC7EM,OAAO,CAACK,YAAY,GAAG,QAAQ;;EAE/B;EACA,MAAMC,MAAM,GAAG,CAAC;EAChBN,OAAO,CAACO,SAAS,CAAC,CAAC;EACnBP,OAAO,CAACQ,MAAM,CAACf,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,CAAC;EACvEK,OAAO,CAACS,MAAM,CAAC1W,KAAK,GAAG0V,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,CAAC;EAC/EK,OAAO,CAACU,KAAK,CAAC3W,KAAK,GAAG0V,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAE5V,KAAK,GAAG0V,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEA,MAAM,CAAC;EAC9IN,OAAO,CAACS,MAAM,CAAC1W,KAAK,GAAG0V,MAAM,CAACE,eAAe,EAAExQ,MAAM,GAAGsQ,MAAM,CAACE,eAAe,GAAGW,MAAM,CAAC;EACxFN,OAAO,CAACU,KAAK,CAAC3W,KAAK,GAAG0V,MAAM,CAACE,eAAe,EAAExQ,MAAM,GAAGsQ,MAAM,CAACE,eAAe,EAAE5V,KAAK,GAAG0V,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEnR,MAAM,GAAGsQ,MAAM,CAACE,eAAe,EAAEW,MAAM,CAAC;EAChKN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEnR,MAAM,GAAGsQ,MAAM,CAACE,eAAe,CAAC;EAChFK,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,EAAExQ,MAAM,GAAGsQ,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAExQ,MAAM,GAAGsQ,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEA,MAAM,CAAC;EAChJN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,CAAC;EACvEN,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,EAAEW,MAAM,CAAC;EAC9HN,OAAO,CAACW,SAAS,CAAC,CAAC;;EAEnB;EACAX,OAAO,CAACY,WAAW,GAAG,QAAQnB,MAAM,CAACG,WAAW,CAAC5N,CAAC,KAAKyN,MAAM,CAACG,WAAW,CAAC3N,CAAC,KAAKwN,MAAM,CAACG,WAAW,CAAC1N,CAAC,KAAKuN,MAAM,CAACG,WAAW,CAACzN,CAAC,GAAG;EAChI6N,OAAO,CAACa,SAAS,GAAGpB,MAAM,CAACE,eAAe;EAC1CK,OAAO,CAACc,MAAM,CAAC,CAAC;;EAEhB;EACAd,OAAO,CAACe,SAAS,GAAG,QAAQtB,MAAM,CAAC/W,eAAe,CAACsJ,CAAC,KAAKyN,MAAM,CAAC/W,eAAe,CAACuJ,CAAC,KAAKwN,MAAM,CAAC/W,eAAe,CAACwJ,CAAC,KAAKuN,MAAM,CAAC/W,eAAe,CAACyJ,CAAC,GAAG;EAC9I6N,OAAO,CAACgB,IAAI,CAAC,CAAC;;EAEd;EACAhB,OAAO,CAACe,SAAS,GAAG,QAAQtB,MAAM,CAACrN,SAAS,CAACJ,CAAC,KAAKyN,MAAM,CAACrN,SAAS,CAACH,CAAC,KAAKwN,MAAM,CAACrN,SAAS,CAACF,CAAC,KAAKuN,MAAM,CAACrN,SAAS,CAACD,CAAC,GAAG;EACtH6N,OAAO,CAACiB,SAAS,GAAG,QAAQ;;EAE5B;EACAjB,OAAO,CAACkB,QAAQ,CAAC3B,IAAI,EAAExV,KAAK,GAAG,CAAC,EAAEoF,MAAM,GAAG,CAAC,CAAC;;EAE7C;EACA,MAAMgS,OAAO,GAAG,IAAIzhB,KAAK,CAAC0hB,aAAa,CAACvB,MAAM,CAAC;EAC/CsB,OAAO,CAACE,SAAS,GAAG3hB,KAAK,CAAC4hB,YAAY;EACtCH,OAAO,CAACvP,WAAW,GAAG,IAAI;;EAE1B;EACA,MAAM2P,cAAc,GAAG,IAAI7hB,KAAK,CAAC8hB,cAAc,CAAC;IAC9CrR,GAAG,EAAEgR,OAAO;IACZxP,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM8P,MAAM,GAAG,IAAI/hB,KAAK,CAACgiB,MAAM,CAACH,cAAc,CAAC;EAC/CE,MAAM,CAACpS,KAAK,CAACtK,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EAC7B0c,MAAM,CAAClQ,QAAQ,CAACoQ,SAAS,GAAG,KAAK,CAAC,CAAC;;EAEnC;EACAF,MAAM,CAACzD,QAAQ,GAAG;IAChBuB,IAAI,EAAEA,IAAI;IACVE,MAAM,EAAEA;EACV,CAAC;EAED,OAAOgC,MAAM;AACf;;AAIA;AACA3f,MAAM,CAAC8f,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAM7K,MAAM,GAAG+I,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAIhL,MAAM,EAAE;MACV;MACA,MAAMiL,MAAM,GAAGjL,MAAM,CAAC9O,QAAQ,CAACzD,KAAK,CAAC,CAAC;;MAEtC;MACAuS,MAAM,CAAC9O,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BgS,MAAM,CAAC9L,EAAE,CAAClG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBgS,MAAM,CAAClL,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAkL,MAAM,CAAC5K,YAAY,CAAC,CAAC;MACrB4K,MAAM,CAAC3K,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMrL,QAAQ,GAAG+e,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIlhB,QAAQ,EAAE;QACZA,QAAQ,CAAC4K,MAAM,CAAC5G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BhE,QAAQ,CAAC+K,MAAM,CAAC,CAAC;MACnB;MAEApJ,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBuf,GAAG,EAAEF,MAAM,CAAC3U,OAAO,CAAC,CAAC;QACrB8U,GAAG,EAAEpL,MAAM,CAAC9O,QAAQ,CAACoF,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAO+U,CAAC,EAAE;IACV1f,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEoe,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAMvL,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACFnU,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAMuX,MAAM,GAAG,IAAIva,UAAU,CAAC,CAAC;;IAE/B;IACA,IAAI;MACF,MAAM,CAAE0iB,gBAAgB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,UAAU,CAAC,GAAG,MAAM9J,OAAO,CAAC+J,GAAG,CAAC,CAClFvI,MAAM,CAACwI,SAAS,CAAC,GAAGpgB,QAAQ,4BAA4B,CAAC,EAC3D4X,MAAM,CAACwI,SAAS,CAAC,GAAGpgB,QAAQ,uBAAuB,CAAC,EACpD4X,MAAM,CAACwI,SAAS,CAAC,GAAGpgB,QAAQ,uBAAuB,CAAC,EAClD4X,MAAM,CAACwI,SAAS,CAAC,GAAGpgB,QAAQ,sBAAsB,CAAC,CAEtD,CAAC;;MAEF;MACAI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB3B,qBAAqB,GAAGshB,WAAW,CAAClhB,KAAK;MACzCJ,qBAAqB,CAACoQ,QAAQ,CAAEC,KAAK,IAAK;QACxC,IAAIA,KAAK,CAACC,MAAM,EAAE;UACd,MAAME,WAAW,GAAG,IAAI9R,KAAK,CAACyZ,oBAAoB,CAAC;YACnDjP,KAAK,EAAE,QAAQ;YAAG;YAClBkP,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,GAAG;YACdC,eAAe,EAAE;UACnB,CAAC,CAAC;;UAEA;UACA,IAAIjI,KAAK,CAACE,QAAQ,CAACpB,GAAG,EAAE;YACtBqB,WAAW,CAACrB,GAAG,GAAGkB,KAAK,CAACE,QAAQ,CAACpB,GAAG;UACtC;UACAkB,KAAK,CAACsR,OAAO,GAAGnR,WAAW;QAC/B;MACF,CAAC,CAAC;MAEF9O,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B;MACA1B,qBAAqB,GAAGshB,WAAW,CAACnhB,KAAK;MACzC;MACAH,qBAAqB,CAACoO,KAAK,CAACtK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxC;MACA9D,qBAAqB,CAACmQ,QAAQ,CAAEC,KAAK,IAAK;QACxC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAAC6H,SAAS,GAAG,GAAG;UAC9B/H,KAAK,CAACE,QAAQ,CAAC8H,SAAS,GAAG,GAAG;UAC9BhI,KAAK,CAACE,QAAQ,CAAC+H,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;MAEF5W,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACAzB,oBAAoB,GAAGshB,UAAU,CAACphB,KAAK;MACvC;MACA;MACA;MACAF,oBAAoB,CAACkQ,QAAQ,CAAEC,KAAK,IAAK;QACvC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAAC6H,SAAS,GAAG,GAAG;UAC9B/H,KAAK,CAACE,QAAQ,CAAC8H,SAAS,GAAG,GAAG;UAC9BhI,KAAK,CAACE,QAAQ,CAAC+H,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;;MAEF;MACA5W,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE6f,UAAU,CAAChT,UAAU,CAACC,MAAM,EAAE,GAAG,CAAC;MAC5D,IAAI+S,UAAU,CAAChT,UAAU,IAAIgT,UAAU,CAAChT,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7D/M,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE6f,UAAU,CAAChT,UAAU,CAACC,MAAM,EAAE,GAAG,CAAC;QACzDpO,eAAe,GAAGmhB,UAAU;MAC9B,CAAC,MAAM;QACL9f,OAAO,CAACsa,IAAI,CAAC,cAAc,CAAC;MAC9B;MAEAta,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;;MAEzB;MACAxB,0BAA0B,GAAGkhB,gBAAgB,CAACjhB,KAAK;MACnDsB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAExB,0BAA0B,CAAC;MACjD;MACAA,0BAA0B,CAACkO,KAAK,CAACtK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C;MACA5D,0BAA0B,CAACiQ,QAAQ,CAAEC,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAAC6H,SAAS,GAAG,GAAG;UAC9B/H,KAAK,CAACE,QAAQ,CAAC8H,SAAS,GAAG,GAAG;UAC9BhI,KAAK,CAACE,QAAQ,CAAC+H,eAAe,GAAG,GAAG;QACxC;MACF,CAAC,CAAC;MAEA5W,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACA,IAAI;QACF,IAAI,CAAChD,qBAAqB,EAAE;UAC1B,MAAMshB,WAAW,GAAG,MAAMpI,MAAM,CAACwI,SAAS,CAAC,GAAGpgB,QAAQ,uBAAuB,CAAC;UAC9EtB,qBAAqB,GAAGshB,WAAW,CAAClhB,KAAK;QAC3C;;QAEA;QACA,IAAI,CAACD,0BAA0B,EAAE;UAC/BuB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC7B,MAAM0f,gBAAgB,GAAG,MAAMnI,MAAM,CAACwI,SAAS,CAAC,GAAGpgB,QAAQ,4BAA4B,CAAC;UACxFnB,0BAA0B,GAAGkhB,gBAAgB,CAACjhB,KAAK;UACnDD,0BAA0B,CAACkO,KAAK,CAACtK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC7CrC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOigB,GAAG,EAAE;QACZlgB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAE4e,GAAG,CAAC;MAClC;IACF;EACF,CAAC,CAAC,OAAO5e,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;;AAED;AACA,MAAMmS,mBAAmB,GAAI3H,IAAI,IAAK;EACpC,MAAMqU,KAAK,GAAG;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,KAAK,CAACrU,IAAI,CAAC,IAAI,MAAM;AAC9B,CAAC;;AAED;AACA,MAAMiH,iBAAiB,GAAGA,CAACxN,QAAQ,EAAEsX,IAAI,EAAErV,KAAK,KAAK;EACnD;EACA,IAAI,CAAC9I,KAAK,EAAE;IACVsB,OAAO,CAACsa,IAAI,CAAC,oBAAoB,CAAC;IAClC;EACF;EAEA,IAAI;IACJ;IACA,MAAMyE,MAAM,GAAG3P,gBAAgB,CAACyN,IAAI,CAAC;IACrCkC,MAAM,CAACxZ,QAAQ,CAAClD,GAAG,CAACkD,QAAQ,CAACvD,CAAC,EAAE,EAAE,EAAE,CAACuD,QAAQ,CAACrD,CAAC,CAAC,CAAC,CAAE;;IAEnD;IACA4P,UAAU,CAAC,MAAM;MACb;MACA,IAAIpT,KAAK,IAAIqgB,MAAM,CAACqB,MAAM,EAAE;QAC9B1hB,KAAK,CAACkP,MAAM,CAACmR,MAAM,CAAC;MAClB;IACJ,CAAC,EAAE,GAAG,CAAC;;IAEP;IACArgB,KAAK,CAAC0O,GAAG,CAAC2R,MAAM,CAAC;;IAEjB;IACA;IACA;IACA;IACA;EACA,CAAC,CAAC,OAAOzd,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;AACF,CAAC;;AAED;AACA,MAAM0Y,mBAAmB,GAAIqG,iBAAiB,IAAK;EACjD,IAAI,CAAC3hB,KAAK,EAAE;IACVsB,OAAO,CAACsB,KAAK,CAAC,gBAAgB,CAAC;IAC/B;EACF;EAEA,IAAI,CAAC+e,iBAAiB,EAAE;IACtBrgB,OAAO,CAACsB,KAAK,CAAC,mBAAmB,CAAC;IAClC;EACF;EAEA,IAAI,CAAC7C,0BAA0B,EAAE;IAC/BuB,OAAO,CAACsB,KAAK,CAAC,UAAU,CAAC;IACzB;IACAgf,2BAA2B,CAACD,iBAAiB,CAAC;IAC9C;EACF;;EAEA;EACAhgB,gBAAgB,CAACqL,OAAO,CAAEiO,QAAQ,IAAK;IACrC,IAAIjb,KAAK,IAAIib,QAAQ,CAACpN,KAAK,EAAE;MAC3B7N,KAAK,CAACkP,MAAM,CAAC+L,QAAQ,CAACpN,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACFlM,gBAAgB,CAACqZ,KAAK,CAAC,CAAC;;EAExB;EACAlc,iBAAiB,CAACyM,aAAa,CAACyB,OAAO,CAAC1B,YAAY,IAAI;IACtD;IACA,IAAIA,YAAY,CAACuW,eAAe,KAAK,KAAK,EAAE;MAC1CvgB,OAAO,CAACC,GAAG,CAAC,UAAU+J,YAAY,CAACG,IAAI,kBAAkB,CAAC;MAC1D;IACF;;IAEA;IACA,IAAIH,YAAY,CAAC/E,QAAQ,IAAI+E,YAAY,CAAChF,SAAS,IAAIgF,YAAY,CAACnD,OAAO,EAAE;MAC3E;MACA,MAAMwF,QAAQ,GAAGgU,iBAAiB,CAAChW,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,EAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC;MAEDjF,OAAO,CAACC,GAAG,CAAC,SAAS+J,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,iBAAiBwF,QAAQ,CAACrK,CAAC,KAAKqK,QAAQ,CAACnK,CAAC,GAAG,CAAC;MAE7G,IAAI;QACF;QACA,MAAMqP,iBAAiB,GAAG9S,0BAA0B,CAACqD,KAAK,CAAC,CAAC;;QAE5D;QACAyP,iBAAiB,CAACpH,IAAI,GAAG,OAAOH,YAAY,CAACG,IAAI,EAAE;;QAEnD;QACAoH,iBAAiB,CAAChM,QAAQ,CAAClD,GAAG,CAACgK,QAAQ,CAACrK,CAAC,EAAE,EAAE,EAAE,CAACqK,QAAQ,CAACnK,CAAC,CAAC;;QAE3D;QACAqP,iBAAiB,CAAC5E,KAAK,CAACtK,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;QAEvC;QACAkP,iBAAiB,CAAC5B,WAAW,GAAG,GAAG;;QAEnC;QACA4B,iBAAiB,CAAC7C,QAAQ,CAACC,KAAK,IAAI;UAClC;UACA,IAAIA,KAAK,CAACC,MAAM,EAAE;YAChB;YACAD,KAAK,CAACE,QAAQ,CAACI,WAAW,GAAG,KAAK;YAClCN,KAAK,CAACE,QAAQ,CAACe,OAAO,GAAG,GAAG;YAC5BjB,KAAK,CAACE,QAAQ,CAAC2R,IAAI,GAAGxjB,KAAK,CAACyjB,UAAU;YACtC9R,KAAK,CAACE,QAAQ,CAACuM,UAAU,GAAG,IAAI;YAChCzM,KAAK,CAACE,QAAQ,CAACoQ,SAAS,GAAG,IAAI;YAC/BtQ,KAAK,CAACE,QAAQ,CAACK,WAAW,GAAG,IAAI;;YAEjC;YACAP,KAAK,CAACgB,WAAW,GAAG,GAAG;UACzB;QACF,CAAC,CAAC;;QAEF;QACA4B,iBAAiB,CAAC+J,QAAQ,GAAG;UAC3BxP,IAAI,EAAE,cAAc;UACpBjF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;UAC7BsD,IAAI,EAAEH,YAAY,CAACG;QACrB,CAAC;;QAED;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;;QAEA;QACAzL,KAAK,CAAC0O,GAAG,CAACmE,iBAAiB,CAAC;;QAE5B;QACAlR,gBAAgB,CAACgC,GAAG,CAAC2H,YAAY,CAACnD,OAAO,EAAE;UACzC0F,KAAK,EAAEgF,iBAAiB;UACxBvH,YAAY,EAAEA,YAAY;UAC1BzE,QAAQ,EAAE8G;QACZ,CAAC,CAAC;QAEFrM,OAAO,CAACC,GAAG,CAAC,SAAS+J,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkBwF,QAAQ,CAACrK,CAAC,KAAK,CAACqK,QAAQ,CAACnK,CAAC,GAAG,CAAC;MACjH,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,QAAQ0I,YAAY,CAACG,IAAI,YAAY,EAAE7I,KAAK,CAAC;QAC3D;QACAkZ,wBAAwB,CAACxQ,YAAY,EAAEqC,QAAQ,EAAEgU,iBAAiB,CAAC;MACrE;IACF;EACF,CAAC,CAAC;;EAEF;EACArgB,OAAO,CAACC,GAAG,CAAC,OAAOI,gBAAgB,CAACmb,IAAI,SAAS,CAAC;EAClDnb,gBAAgB,CAACqL,OAAO,CAAC,CAACiO,QAAQ,EAAE9S,OAAO,KAAK;IAC9C7G,OAAO,CAACC,GAAG,CAAC,QAAQ4G,OAAO,KAAK8S,QAAQ,CAAC3P,YAAY,CAACG,IAAI,EAAE,CAAC;EAC/D,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMmW,2BAA2B,GAAID,iBAAiB,IAAK;EACzD7iB,iBAAiB,CAACyM,aAAa,CAACyB,OAAO,CAAC1B,YAAY,IAAI;IACtD;IACA,IAAIA,YAAY,CAACuW,eAAe,KAAK,KAAK,EAAE;MAC1C;IACF;IAEA,IAAIvW,YAAY,CAAC/E,QAAQ,IAAI+E,YAAY,CAAChF,SAAS,IAAIgF,YAAY,CAACnD,OAAO,EAAE;MAC3E;MACA,MAAMwF,QAAQ,GAAGgU,iBAAiB,CAAChW,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,EAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC;MAEDuV,wBAAwB,CAACxQ,YAAY,EAAEqC,QAAQ,EAAEgU,iBAAiB,CAAC;IACrE;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAM7F,wBAAwB,GAAGA,CAACxQ,YAAY,EAAEqC,QAAQ,EAAEgU,iBAAiB,KAAK;EAC9E;EACA,MAAM/P,QAAQ,GAAG,IAAItT,KAAK,CAACyd,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAClD,MAAM5L,QAAQ,GAAG,IAAI7R,KAAK,CAAC0d,iBAAiB,CAAC;IAC3ClT,KAAK,EAAE,QAAQ;IACfyH,WAAW,EAAE,KAAK;IAClBW,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM2B,iBAAiB,GAAG,IAAIvU,KAAK,CAAC2d,IAAI,CAACrK,QAAQ,EAAEzB,QAAQ,CAAC;;EAE5D;EACA0C,iBAAiB,CAACpH,IAAI,GAAG,SAASH,YAAY,CAACG,IAAI,EAAE;;EAErD;EACAoH,iBAAiB,CAAChM,QAAQ,CAAClD,GAAG,CAACgK,QAAQ,CAACrK,CAAC,EAAE,EAAE,EAAE,CAACqK,QAAQ,CAACnK,CAAC,CAAC;;EAE3D;EACAqP,iBAAiB,CAAC5B,WAAW,GAAG,GAAG;;EAEnC;EACA4B,iBAAiB,CAAC+J,QAAQ,GAAG;IAC3BxP,IAAI,EAAE,cAAc;IACpBjF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG;EACrB,CAAC;;EAED;EACA,MAAMuW,gBAAgB,GAAG,IAAI1jB,KAAK,CAACke,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5D,MAAMyF,gBAAgB,GAAG,IAAI3jB,KAAK,CAAC0d,iBAAiB,CAAC;IACnDlT,KAAK,EAAE,QAAQ;IACfyH,WAAW,EAAE,IAAI;IACjBW,OAAO,EAAE,GAAG;IAAG;IACfwL,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMwF,QAAQ,GAAG,IAAI5jB,KAAK,CAAC2d,IAAI,CAAC+F,gBAAgB,EAAEC,gBAAgB,CAAC;EACnEC,QAAQ,CAACzW,IAAI,GAAG,YAAYH,YAAY,CAACG,IAAI,EAAE;EAC/CyW,QAAQ,CAACtF,QAAQ,GAAG;IAClBxP,IAAI,EAAE,cAAc;IACpBjF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG,IAAI;IACvB0W,UAAU,EAAE;EACd,CAAC;EAEDtP,iBAAiB,CAACnE,GAAG,CAACwT,QAAQ,CAAC;;EAE/B;EACAliB,KAAK,CAAC0O,GAAG,CAACmE,iBAAiB,CAAC;;EAE5B;EACAlR,gBAAgB,CAACgC,GAAG,CAAC2H,YAAY,CAACnD,OAAO,EAAE;IACzC0F,KAAK,EAAEgF,iBAAiB;IACxBvH,YAAY,EAAEA,YAAY;IAC1BzE,QAAQ,EAAE8G;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMyU,aAAa,GAAG,IAAI9jB,KAAK,CAACke,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAM6F,aAAa,GAAG,IAAI/jB,KAAK,CAAC0d,iBAAiB,CAAC;IAAElT,KAAK,EAAE;EAAS,CAAC,CAAC;EACtE,MAAMwZ,SAAS,GAAG,IAAIhkB,KAAK,CAAC2d,IAAI,CAACmG,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAACzb,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;EAEhC;EACA2e,SAAS,CAAC1F,QAAQ,GAAG;IACnBxP,IAAI,EAAE,cAAc;IACpBjF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG;EACrB,CAAC;EAEDoH,iBAAiB,CAACnE,GAAG,CAAC4T,SAAS,CAAC;EAEhChhB,OAAO,CAACC,GAAG,CAAC,SAAS+J,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkBwF,QAAQ,CAACrK,CAAC,KAAK,CAACqK,QAAQ,CAACnK,CAAC,GAAG,CAAC;AACjH,CAAC;;AAED;AACA,MAAM4O,iBAAiB,GAAIL,OAAO,IAAK;EACrC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMI,oBAAoB,GAAIoQ,OAAO,IAAK;EACxC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAM5G,gBAAgB,GAAGA,CAACjI,KAAK,EAAE8O,SAAS,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,KAAK;EAC7F,IAAI,CAACH,SAAS,IAAI,CAACC,aAAa,IAAI,CAACC,cAAc,EAAE;EAErDphB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEmS,KAAK,CAAC+H,OAAO,EAAE/H,KAAK,CAACgI,OAAO,CAAC;;EAEvD;EACA,MAAMkH,IAAI,GAAGJ,SAAS,CAACK,qBAAqB,CAAC,CAAC;EAC9C,MAAMC,MAAM,GAAI,CAACpP,KAAK,CAAC+H,OAAO,GAAGmH,IAAI,CAAC7b,IAAI,IAAIyb,SAAS,CAACO,WAAW,GAAI,CAAC,GAAG,CAAC;EAC5E,MAAMC,MAAM,GAAG,EAAE,CAACtP,KAAK,CAACgI,OAAO,GAAGkH,IAAI,CAACla,GAAG,IAAI8Z,SAAS,CAACS,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;;EAE7E;EACA,MAAMC,SAAS,GAAG,IAAI5kB,KAAK,CAAC6kB,SAAS,CAAC,CAAC;EACvC;EACAD,SAAS,CAAC7E,MAAM,CAAC+E,MAAM,CAACC,SAAS,GAAG,CAAC;EACrCH,SAAS,CAAC7E,MAAM,CAACiF,IAAI,CAACD,SAAS,GAAG,CAAC;EAEnC,MAAME,WAAW,GAAG,IAAIjlB,KAAK,CAACklB,OAAO,CAACV,MAAM,EAAEE,MAAM,CAAC;EACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,EAAEb,cAAc,CAAC;EAEpDphB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEuhB,MAAM,EAAEE,MAAM,CAAC;;EAEtC;EACA,MAAMU,mBAAmB,GAAG,EAAE;EAE9B/hB,gBAAgB,CAACqL,OAAO,CAAC,CAACiO,QAAQ,EAAE9S,OAAO,KAAK;IAC9C,IAAI8S,QAAQ,CAACpN,KAAK,EAAE;MAClB;MACA6V,mBAAmB,CAAChR,IAAI,CAACuI,QAAQ,CAACpN,KAAK,CAAC;MACxC;MACAoN,QAAQ,CAACpN,KAAK,CAAC3F,OAAO,GAAG,IAAI;MAC7B+S,QAAQ,CAACpN,KAAK,CAACoD,WAAW,GAAG,IAAI,CAAC,CAAC;MACnC;MACAgK,QAAQ,CAACpN,KAAK,CAACmC,QAAQ,CAACC,KAAK,IAAI;QAC/BA,KAAK,CAAC/H,OAAO,GAAG,IAAI;QACpB+H,KAAK,CAACgB,WAAW,GAAG,IAAI;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF3P,OAAO,CAACC,GAAG,CAAC,QAAQmiB,mBAAmB,CAACrV,MAAM,gBAAgB,CAAC;;EAE/D;EACA,MAAMsV,sBAAsB,GAAGT,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,EAAE,IAAI,CAAC;EAEpF,IAAIC,sBAAsB,CAACtV,MAAM,GAAG,CAAC,EAAE;IACrC/M,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEoiB,sBAAsB,CAACtV,MAAM,CAAC;IACxDsV,sBAAsB,CAAC3W,OAAO,CAAC,CAAC6W,SAAS,EAAEC,KAAK,KAAK;MACnDxiB,OAAO,CAACC,GAAG,CAAC,QAAQuiB,KAAK,GAAG,EACjBD,SAAS,CAACE,MAAM,CAACtY,IAAI,IAAI,KAAK,EAC9B,KAAK,EAAEoY,SAAS,CAAC9f,QAAQ,EACzB,WAAW,EAAE8f,SAAS,CAACE,MAAM,CAACnH,QAAQ,CAAC;IACpD,CAAC,CAAC;;IAEF;IACA,MAAMoH,GAAG,GAAGC,yBAAyB,CAACN,sBAAsB,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC;IAEvE,IAAIC,GAAG,IAAIA,GAAG,CAACpH,QAAQ,IAAIoH,GAAG,CAACpH,QAAQ,CAACxP,IAAI,KAAK,cAAc,EAAE;MAC/D;MACA,MAAMjF,OAAO,GAAG6b,GAAG,CAACpH,QAAQ,CAACzU,OAAO;MACpC7G,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE4G,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;;MAEhE;MACA,IAAI+b,SAAS,GAAG/b,OAAO;MACvB,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,CAACxG,gBAAgB,CAACiC,GAAG,CAACuE,OAAO,CAAC,IAAIxG,gBAAgB,CAACiC,GAAG,CAAC4O,QAAQ,CAACrK,OAAO,CAAC,CAAC,EAAE;QAC5G+b,SAAS,GAAG1R,QAAQ,CAACrK,OAAO,CAAC;QAC7B7G,OAAO,CAACC,GAAG,CAAC,cAAc2iB,SAAS,EAAE,CAAC;MACxC,CAAC,MAAM,IAAI,OAAO/b,OAAO,KAAK,QAAQ,IAAI,CAACxG,gBAAgB,CAACiC,GAAG,CAACuE,OAAO,CAAC,IAAIxG,gBAAgB,CAACiC,GAAG,CAACgP,MAAM,CAACzK,OAAO,CAAC,CAAC,EAAE;QACjH+b,SAAS,GAAGtR,MAAM,CAACzK,OAAO,CAAC;QAC3B7G,OAAO,CAACC,GAAG,CAAC,eAAe2iB,SAAS,EAAE,CAAC;MACzC;;MAEA;MACAxjB,MAAM,CAAC2S,qBAAqB,CAAC6Q,SAAS,CAAC;MACvC;IACF;EACF;;EAEA;EACA,MAAMC,UAAU,GAAGjB,SAAS,CAACU,gBAAgB,CAACnB,aAAa,CAACtK,QAAQ,EAAE,IAAI,CAAC;EAE3E7W,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE4iB,UAAU,CAAC9V,MAAM,CAAC;EAE7C,IAAI8V,UAAU,CAAC9V,MAAM,GAAG,CAAC,EAAE;IACzB;IACA8V,UAAU,CAACnX,OAAO,CAAC,CAAC6W,SAAS,EAAEC,KAAK,KAAK;MACvC,MAAME,GAAG,GAAGH,SAAS,CAACE,MAAM;MAC5BziB,OAAO,CAACC,GAAG,CAAC,UAAUuiB,KAAK,GAAG,EAAEE,GAAG,CAACvY,IAAI,IAAI,KAAK,EACrC,WAAW,EAAEuY,GAAG,CAACpH,QAAQ,EACzB,KAAK,EAAEiH,SAAS,CAAC9f,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACA,KAAK,IAAIyH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2Y,UAAU,CAAC9V,MAAM,EAAE7C,CAAC,EAAE,EAAE;MAC1C,MAAMwY,GAAG,GAAGC,yBAAyB,CAACE,UAAU,CAAC3Y,CAAC,CAAC,CAACuY,MAAM,CAAC;MAC3D,IAAIC,GAAG,IAAIA,GAAG,CAACpH,QAAQ,IAAIoH,GAAG,CAACpH,QAAQ,CAACxP,IAAI,KAAK,cAAc,EAAE;QAC/D,MAAMjF,OAAO,GAAG6b,GAAG,CAACpH,QAAQ,CAACzU,OAAO;QACpC7G,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE4G,OAAO,CAAC;;QAEvC;QACAzH,MAAM,CAAC2S,qBAAqB,CAAClL,OAAO,CAAC;QACrC;MACF;IACF;EACF;;EAEA;EACA7G,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;;EAE9B;EACA,IAAI6iB,YAAY,GAAG,IAAI;EACvB,IAAIzZ,WAAW,GAAG,GAAG,CAAC,CAAC;;EAEvBhJ,gBAAgB,CAACqL,OAAO,CAAC,CAACiO,QAAQ,EAAE9S,OAAO,KAAK;IAC9C,IAAI8S,QAAQ,CAACpN,KAAK,EAAE;MAClB,MAAMwW,QAAQ,GAAG,IAAI/lB,KAAK,CAAC8F,OAAO,CAAC,CAAC;MACpC;MACA6W,QAAQ,CAACpN,KAAK,CAACyW,gBAAgB,CAACD,QAAQ,CAAC;;MAEzC;MACA,MAAME,SAAS,GAAGF,QAAQ,CAACjhB,KAAK,CAAC,CAAC;MAClCmhB,SAAS,CAACC,OAAO,CAAC9B,cAAc,CAAC;;MAEjC;MACA,MAAM+B,EAAE,GAAGF,SAAS,CAACjhB,CAAC,GAAGwf,MAAM;MAC/B,MAAM4B,EAAE,GAAGH,SAAS,CAAC/gB,CAAC,GAAGwf,MAAM;MAC/B,MAAMjf,QAAQ,GAAGS,IAAI,CAACmgB,IAAI,CAACF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;MAE7CpjB,OAAO,CAACC,GAAG,CAAC,MAAM4G,OAAO,OAAO,EAAEpE,QAAQ,CAAC;MAE3C,IAAIA,QAAQ,GAAG4G,WAAW,EAAE;QAC1BA,WAAW,GAAG5G,QAAQ;QACtBqgB,YAAY,GAAG;UAAEjc,OAAO;UAAEpE;QAAS,CAAC;MACtC;IACF;EACF,CAAC,CAAC;EAEF,IAAIqgB,YAAY,EAAE;IAChB9iB,OAAO,CAACC,GAAG,CAAC,oBAAoB6iB,YAAY,CAACjc,OAAO,SAASic,YAAY,CAACrgB,QAAQ,EAAE,CAAC;;IAErF;IACArD,MAAM,CAAC2S,qBAAqB,CAAC+Q,YAAY,CAACjc,OAAO,CAAC;IAClD;EACF;EAEA7G,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;;EAE1B;EACA,IAAIohB,eAAe,EAAE;IACnBA,eAAe,CAAC5P,IAAI,IAAI;MACtB,IAAIA,IAAI,CAAC7K,OAAO,EAAE;QAChB,OAAO;UAAE,GAAG6K,IAAI;UAAE7K,OAAO,EAAE;QAAM,CAAC;MACpC;MACA,OAAO6K,IAAI;IACb,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAMkL,kBAAkB,GAAI0E,eAAe,IAAK;EAC9C;EACA,IAAIjiB,MAAM,CAAC6H,0BAA0B,IAAI7H,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,EAAE;IAClFoR,aAAa,CAACla,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,CAAC;IACxD9I,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,GAAG,IAAI;IAChDlI,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B;;EAEA;EACA,IAAIb,MAAM,CAAC4H,mBAAmB,EAAE;IAC9B5H,MAAM,CAAC4H,mBAAmB,CAACkB,OAAO,GAAG,IAAI;EAC3C;;EAEA;EACAmZ,eAAe,CAAC5P,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAE7K,OAAO,EAAE;EAAM,CAAC,CAAC,CAAC;EACtD5G,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;AACtB,CAAC;;AAED;AACAb,MAAM,CAACkkB,qBAAqB,GAAIzc,OAAO,IAAK;EAC1C,IAAI;IAAA,IAAA0c,qBAAA,EAAAC,sBAAA;IACF;IACA,MAAMxS,YAAY,GAAG3Q,gBAAgB,CAACmC,GAAG,CAACqE,OAAO,IAAI,GAAG,CAAC;IACzD,IAAI,CAACmK,YAAY,EAAE;MACjBhR,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEuF,OAAO,CAAC;;MAEtC;MACA7G,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxBI,gBAAgB,CAACqL,OAAO,CAAC,CAAC+X,KAAK,EAAE7X,EAAE,KAAK;QACtC5L,OAAO,CAACC,GAAG,CAAC,KAAK2L,EAAE,KAAK6X,KAAK,CAACzZ,YAAY,CAACG,IAAI,EAAE,CAAC;MACpD,CAAC,CAAC;MAEF,OAAO,KAAK;IACd;;IAEA;IACA,MAAMuZ,UAAU,GAAG1S,YAAY,CAACzE,KAAK;;IAErC;IACA,MAAMoX,SAAS,GAAGrjB,kBAAkB,CAACkC,GAAG,CAACqE,OAAO,CAAC;IACjD,MAAMmD,YAAY,GAAGgH,YAAY,CAAChH,YAAY;;IAE9C;IACA,IAAIlD,OAAO;IAEX,IAAI6c,SAAS,IAAIA,SAAS,CAAC5c,MAAM,EAAE;MACjCD,OAAO,gBACLpJ,OAAA;QAAKge,KAAK,EAAE;UAAE3V,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAEgV,SAAS,EAAE,OAAO;UAAEuH,SAAS,EAAE;QAAO,CAAE;QAAA/M,QAAA,gBACpFnZ,OAAA;UAAKge,KAAK,EAAE;YACVjU,UAAU,EAAE,MAAM;YAClBoc,YAAY,EAAE,KAAK;YACnBzd,QAAQ,EAAE,MAAM;YAChB0d,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAAlN,QAAA,GACC7M,YAAY,CAACG,IAAI,EAAC,QAAM,EAACtD,OAAO,EAAC,GACpC;QAAA;UAAA8U,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNpe,OAAA;UAAAmZ,QAAA,EACG8M,SAAS,CAAC5c,MAAM,CAAC0G,GAAG,CAAC,CAAC+C,KAAK,EAAEgS,KAAK,KAAK;YACtC,IAAIwB,UAAU;YACd,QAAQxT,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBAAEgT,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;gBAAEA,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;cAAE;gBAASA,UAAU,GAAG,SAAS;gBAAE;YAC7C;YAEA,oBACEtmB,OAAA;cAAiBge,KAAK,EAAE;gBACtBmI,YAAY,EAAE,KAAK;gBACnB7d,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE,KAAK;gBACnBE,QAAQ,EAAE;cACZ,CAAE;cAAAyQ,QAAA,gBACAnZ,OAAA;gBAAKge,KAAK,EAAE;kBAAEjU,UAAU,EAAE;gBAAO,CAAE;gBAAAoP,QAAA,EAChC/F,iBAAiB,CAACN,KAAK,CAACC,OAAO;cAAC;gBAAAkL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNpe,OAAA;gBAAKge,KAAK,EAAE;kBAAE9V,OAAO,EAAE,MAAM;kBAAEqe,cAAc,EAAE;gBAAgB,CAAE;gBAAApN,QAAA,gBAC/DnZ,OAAA;kBAAAmZ,QAAA,EAAM;gBAAI;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBpe,OAAA;kBAAMge,KAAK,EAAE;oBACXlU,KAAK,EAAEwc,UAAU;oBACjBvc,UAAU,EAAE,MAAM;oBAClBzB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAA2Q,QAAA,EACCrG,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGR,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAG;gBAAI;kBAAA2K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNpe,OAAA;gBAAKge,KAAK,EAAE;kBAAE9V,OAAO,EAAE,MAAM;kBAAEqe,cAAc,EAAE;gBAAgB,CAAE;gBAAApN,QAAA,gBAC/DnZ,OAAA;kBAAAmZ,QAAA,EAAM;gBAAK;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBpe,OAAA;kBAAMge,KAAK,EAAE;oBAAEjU,UAAU,EAAE;kBAAO,CAAE;kBAAAoP,QAAA,GAAErG,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAA0K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAzBE0G,KAAK;cAAA7G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNpe,OAAA;UAAKge,KAAK,EAAE;YAAEwI,SAAS,EAAE,KAAK;YAAE9d,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE;UAAO,CAAE;UAAAqP,QAAA,GAAC,4BAC3D,EAAC,IAAInS,IAAI,CAAC,CAAC,CAACyf,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAAxI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACLhV,OAAO,gBACLpJ,OAAA;QAAKge,KAAK,EAAE;UAAE3V,OAAO,EAAE,KAAK;UAAEwW,QAAQ,EAAE;QAAQ,CAAE;QAAA1F,QAAA,gBAChDnZ,OAAA;UAAKge,KAAK,EAAE;YAAEjU,UAAU,EAAE,MAAM;YAAEoc,YAAY,EAAE;UAAM,CAAE;UAAAhN,QAAA,EAAE7M,YAAY,CAACG;QAAI;UAAAwR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClFpe,OAAA;UAAAmZ,QAAA,GAAK,kBAAM,EAAChQ,OAAO;QAAA;UAAA8U,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1Bpe,OAAA;UAAAmZ,QAAA,EAAK;QAAU;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN;IACH;;IAEA;IACA,MAAMsI,OAAO,GAAGhlB,MAAM,CAACmV,UAAU,GAAG,CAAC;IACrC,MAAM8P,OAAO,GAAGjlB,MAAM,CAACoV,WAAW,GAAG,CAAC;;IAEtC;IACA,MAAM6M,eAAe,IAAAkC,qBAAA,GAAGnG,QAAQ,CAAC+B,aAAa,CAAC,OAAO,CAAC,cAAAoE,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCe,gBAAgB,cAAAd,sBAAA,uBAAjDA,sBAAA,CAAmD7c,sBAAsB;IAEjG,IAAI0a,eAAe,EAAE;MACnB;MACAA,eAAe,CAAC;QACdza,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBtB,QAAQ,EAAE;UAAEvD,CAAC,EAAEoiB,OAAO;UAAEliB,CAAC,EAAEmiB;QAAQ,CAAC;QACpCvd,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAA4c,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE5c,MAAM,KAAI;MAC/B,CAAC,CAAC;MAEF/G,OAAO,CAACC,GAAG,CAAC,SAAS+J,YAAY,CAACG,IAAI,KAAKtD,OAAO,UAAU,CAAC;MAC7D,OAAO,IAAI;IACb,CAAC,MAAM;MACL;MACA,MAAM0d,OAAO,GAAGnH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CkH,OAAO,CAAC7I,KAAK,CAACnW,QAAQ,GAAG,UAAU;MACnCgf,OAAO,CAAC7I,KAAK,CAACjW,IAAI,GAAG,GAAG2e,OAAO,IAAI;MACnCG,OAAO,CAAC7I,KAAK,CAACtU,GAAG,GAAG,GAAGid,OAAO,IAAI;MAClCE,OAAO,CAAC7I,KAAK,CAAChW,SAAS,GAAG,wBAAwB;MAClD6e,OAAO,CAAC7I,KAAK,CAAC/V,MAAM,GAAG,MAAM;MAC7B4e,OAAO,CAAC7I,KAAK,CAAC1V,eAAe,GAAG,qBAAqB;MACrDue,OAAO,CAAC7I,KAAK,CAAClU,KAAK,GAAG,OAAO;MAC7B+c,OAAO,CAAC7I,KAAK,CAACxV,YAAY,GAAG,KAAK;MAClCqe,OAAO,CAAC7I,KAAK,CAACrV,SAAS,GAAG,8BAA8B;MACxDke,OAAO,CAAC7I,KAAK,CAAC3V,OAAO,GAAG,KAAK;MAC7Bwe,OAAO,CAAC7I,KAAK,CAACa,QAAQ,GAAG,OAAO;MAChCgI,OAAO,CAAC7I,KAAK,CAACtV,QAAQ,GAAG,MAAM;MAE/Bme,OAAO,CAACC,SAAS,GAAG;AAC1B;AACA,YAAYxa,YAAY,CAACG,IAAI,SAAStD,OAAO;AAC7C;AACA;AACA,qBAAqBA,OAAO;AAC5B,eAAe8c,SAAS,GAAG,UAAU,GAAG,YAAY;AACpD;AACA;AACA,OAAO;MAEDvG,QAAQ,CAACqH,IAAI,CAACzP,WAAW,CAACuP,OAAO,CAAC;;MAElC;MACA,MAAMG,WAAW,GAAGH,OAAO,CAACpF,aAAa,CAAC,QAAQ,CAAC;MACnD,IAAIuF,WAAW,EAAE;QACfA,WAAW,CAACvL,gBAAgB,CAAC,OAAO,EAAE,MAAM;UAC1CiE,QAAQ,CAACqH,IAAI,CAAChL,WAAW,CAAC8K,OAAO,CAAC;QACpC,CAAC,CAAC;MACJ;MAEAvkB,OAAO,CAACC,GAAG,CAAC,gBAAgB+J,YAAY,CAACG,IAAI,KAAKtD,OAAO,OAAO,CAAC;MACjE,OAAO,IAAI;IACb;EACF,CAAC,CAAC,OAAOvF,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACAlC,MAAM,CAACulB,iBAAiB,GAAG,MAAM;EAC/B3kB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;EAErB,IAAI,CAACI,gBAAgB,IAAIA,gBAAgB,CAACmb,IAAI,KAAK,CAAC,EAAE;IACpDxb,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,OAAO,EAAE;EACX;EAEA,MAAM2kB,IAAI,GAAG,EAAE;EACfvkB,gBAAgB,CAACqL,OAAO,CAAC,CAAC+X,KAAK,EAAE7X,EAAE,KAAK;IACtC5L,OAAO,CAACC,GAAG,CAAC,SAAS2L,EAAE,SAAS6X,KAAK,CAACzZ,YAAY,CAACG,IAAI,EAAE,CAAC;IAC1Dya,IAAI,CAACxT,IAAI,CAAC;MACRxF,EAAE;MACFzB,IAAI,EAAEsZ,KAAK,CAACzZ,YAAY,CAACG,IAAI;MAC7B5E,QAAQ,EAAEke,KAAK,CAACle;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOqf,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACAxlB,MAAM,CAAC2S,qBAAqB,GAAIlL,OAAO,IAAK;EAC1C,IAAI;IACF;IACAA,OAAO,GAAGyK,MAAM,CAACzK,OAAO,CAAC;IAEzB7G,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE4G,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;IAC/E7G,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,gBAAgB,CAACmb,IAAI,CAAC;IAC3Dxb,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEK,kBAAkB,CAACkb,IAAI,CAAC;;IAE/D;IACA,IAAI,CAAC3U,OAAO,IAAIxG,gBAAgB,CAACmb,IAAI,GAAG,CAAC,EAAE;MACzC3U,OAAO,GAAGyK,MAAM,CAACvQ,KAAK,CAAC8jB,IAAI,CAACxkB,gBAAgB,CAACykB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD9kB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE4G,OAAO,CAAC;IAC3C;;IAEA;IACA7G,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1BI,gBAAgB,CAACqL,OAAO,CAAC,CAAC+X,KAAK,EAAE7X,EAAE,KAAK;MAAA,IAAAmZ,mBAAA;MACtC/kB,OAAO,CAACC,GAAG,CAAC,KAAK2L,EAAE,KAAK,OAAOA,EAAE,MAAM,EAAAmZ,mBAAA,GAAAtB,KAAK,CAACzZ,YAAY,cAAA+a,mBAAA,uBAAlBA,mBAAA,CAAoB5a,IAAI,KAAI,IAAI,EAAE,CAAC;IAC5E,CAAC,CAAC;;IAEF;IACA,IAAI6G,YAAY,GAAG3Q,gBAAgB,CAACmC,GAAG,CAACqE,OAAO,CAAC;IAChD,IAAI,CAACmK,YAAY,EAAE;MACjB;MACA,MAAMgU,SAAS,GAAG9T,QAAQ,CAACrK,OAAO,CAAC;MACnCmK,YAAY,GAAG3Q,gBAAgB,CAACmC,GAAG,CAACwiB,SAAS,CAAC;MAE9C,IAAIhU,YAAY,EAAE;QAChBhR,OAAO,CAACC,GAAG,CAAC,UAAU+kB,SAAS,SAAS,CAAC;QACzCne,OAAO,GAAGme,SAAS,CAAC,CAAC;MACvB;IACF;IAEA,IAAI,CAAChU,YAAY,EAAE;MACjBhR,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEuF,OAAO,CAAC;MACtC,OAAO,KAAK;IACd;;IAEA;IACA,IAAIzH,MAAM,CAAC4H,mBAAmB,EAAE;MAC9B5H,MAAM,CAAC4H,mBAAmB,CAACkB,OAAO,GAAGrB,OAAO;IAC9C;;IAEA;IACA,IAAIzH,MAAM,CAAC6H,0BAA0B,IAAI7H,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,EAAE;MAClFoR,aAAa,CAACla,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,CAAC;MACxD9I,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,GAAG,IAAI;IAClD;;IAEA;IACA,MAAM+c,yBAAyB,GAAGA,CAAA,KAAM;MAAA,IAAAC,qBAAA;MACtC;MACA,IAAI,CAAC9lB,MAAM,CAAC8H,uBAAuB,EAAE;MAErC,MAAMie,SAAS,IAAAD,qBAAA,GAAG9lB,MAAM,CAAC4H,mBAAmB,cAAAke,qBAAA,uBAA1BA,qBAAA,CAA4Bhd,OAAO;MACrD,IAAI,CAACid,SAAS,EAAE;;MAEhB;MACA,IAAIxB,SAAS,GAAGrjB,kBAAkB,CAACkC,GAAG,CAAC2iB,SAAS,CAAC;MACjD,IAAI,CAACxB,SAAS,IAAI,OAAOwB,SAAS,KAAK,QAAQ,EAAE;QAC/C;QACAxB,SAAS,GAAGrjB,kBAAkB,CAACkC,GAAG,CAAC0O,QAAQ,CAACiU,SAAS,CAAC,CAAC;MACzD,CAAC,MAAM,IAAI,CAACxB,SAAS,IAAI,OAAOwB,SAAS,KAAK,QAAQ,EAAE;QACtD;QACAxB,SAAS,GAAGrjB,kBAAkB,CAACkC,GAAG,CAAC8O,MAAM,CAAC6T,SAAS,CAAC,CAAC;MACvD;MAEA,IAAI,CAACxB,SAAS,IAAI,CAACA,SAAS,CAAC5c,MAAM,IAAI4c,SAAS,CAAC5c,MAAM,CAACgG,MAAM,KAAK,CAAC,EAAE;QACpE/M,OAAO,CAACC,GAAG,CAAC,QAAQklB,SAAS,cAAc,CAAC;QAC5C;MACF;;MAEA;MACA,MAAMC,iBAAiB,GAAG/kB,gBAAgB,CAACmC,GAAG,CAAC2iB,SAAS,CAAC,KAC/B,OAAOA,SAAS,KAAK,QAAQ,GAAG9kB,gBAAgB,CAACmC,GAAG,CAAC0O,QAAQ,CAACiU,SAAS,CAAC,CAAC,GACzE9kB,gBAAgB,CAACmC,GAAG,CAAC8O,MAAM,CAAC6T,SAAS,CAAC,CAAC,CAAC;MAElE,IAAI,CAACC,iBAAiB,EAAE;QACtBplB,OAAO,CAACsB,KAAK,CAAC,WAAW6jB,SAAS,gBAAgB,CAAC;QACnD;MACF;MAEA,MAAMnb,YAAY,GAAGob,iBAAiB,CAACpb,YAAY;;MAEnD;MACA,MAAMlD,OAAO,gBACXpJ,OAAA;QAAKge,KAAK,EAAE;UAAE3V,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAEgV,SAAS,EAAE,OAAO;UAAEuH,SAAS,EAAE;QAAO,CAAE;QAAA/M,QAAA,gBACpFnZ,OAAA;UAAKge,KAAK,EAAE;YACVjU,UAAU,EAAE,MAAM;YAClBoc,YAAY,EAAE,KAAK;YACnBzd,QAAQ,EAAE,MAAM;YAChB0d,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAAlN,QAAA,GACC,CAAA7M,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAACgb,SAAS,EAAC,GACjD;QAAA;UAAAxJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNpe,OAAA;UAAAmZ,QAAA,EACG8M,SAAS,CAAC5c,MAAM,CAAC0G,GAAG,CAAC,CAAC+C,KAAK,EAAEgS,KAAK,KAAK;YACtC,IAAIwB,UAAU;YACd,IAAIqB,SAAS;YAEb,QAAQ7U,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBACNgT,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;cACF,KAAK,GAAG;gBACNrB,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;cACF,KAAK,GAAG;cACR;gBACErB,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;YACJ;YAEA,MAAM1U,SAAS,GAAGG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC;YAElD,oBACE/S,OAAA;cAAiBge,KAAK,EAAE;gBACtBmI,YAAY,EAAE,KAAK;gBACnB7d,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE,KAAK;gBACnBE,QAAQ,EAAE;cACZ,CAAE;cAAAyQ,QAAA,gBACAnZ,OAAA;gBAAKge,KAAK,EAAE;kBAAEjU,UAAU,EAAE;gBAAO,CAAE;gBAAAoP,QAAA,GAChClG,SAAS,EAAC,oBAAQ,EAACH,KAAK,CAACC,OAAO,EAAC,GACpC;cAAA;gBAAAkL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNpe,OAAA;gBAAKge,KAAK,EAAE;kBAAE9V,OAAO,EAAE,MAAM;kBAAEqe,cAAc,EAAE;gBAAgB,CAAE;gBAAApN,QAAA,gBAC/DnZ,OAAA;kBAAAmZ,QAAA,EAAM;gBAAI;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBpe,OAAA;kBAAMge,KAAK,EAAE;oBACXlU,KAAK,EAAEwc,UAAU;oBACjBvc,UAAU,EAAE,MAAM;oBAClBzB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAA2Q,QAAA,EACCwO;gBAAS;kBAAA1J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNpe,OAAA;gBAAKge,KAAK,EAAE;kBAAE9V,OAAO,EAAE,MAAM;kBAAEqe,cAAc,EAAE;gBAAgB,CAAE;gBAAApN,QAAA,gBAC/DnZ,OAAA;kBAAAmZ,QAAA,EAAM;gBAAK;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBpe,OAAA;kBAAMge,KAAK,EAAE;oBAAEjU,UAAU,EAAE;kBAAO,CAAE;kBAAAoP,QAAA,GAAErG,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAA0K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAzBE0G,KAAK;cAAA7G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNpe,OAAA;UAAKge,KAAK,EAAE;YAAEwI,SAAS,EAAE,KAAK;YAAE9d,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE;UAAO,CAAE;UAAAqP,QAAA,GAAC,4BAC3D,EAAC,IAAInS,IAAI,CAACif,SAAS,CAAC9R,UAAU,CAAC,CAACsS,kBAAkB,CAAC,CAAC,EAAC,6BAC7D;QAAA;UAAAxI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;;MAED;MACA1c,MAAM,CAAC8H,uBAAuB,CAACuK,IAAI,KAAK;QACtC,GAAGA,IAAI;QACP3K,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE4c,SAAS,CAAC5c;MACpB,CAAC,CAAC,CAAC;MAEH/G,OAAO,CAACC,GAAG,CAAC,SAAS,CAAA+J,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAIgb,SAAS,WAAW,CAAC;IAClE,CAAC;;IAED;IACA,MAAMG,gCAAgC,GAAGA,CAAA,KAAM;MAC7C;MACA,IAAI3B,SAAS,GAAGrjB,kBAAkB,CAACkC,GAAG,CAACqE,OAAO,CAAC;MAC/C,IAAI,CAAC8c,SAAS,EAAE;QACd;QACA,MAAMqB,SAAS,GAAG9T,QAAQ,CAACrK,OAAO,CAAC;QACnC8c,SAAS,GAAGrjB,kBAAkB,CAACkC,GAAG,CAACwiB,SAAS,CAAC;QAE7C,IAAIrB,SAAS,EAAE;UACb3jB,OAAO,CAACC,GAAG,CAAC,UAAU+kB,SAAS,aAAa,CAAC;QAC/C;MACF;MAEAhlB,OAAO,CAACC,GAAG,CAAC,QAAQ4G,OAAO,SAAS,EAAE8c,SAAS,CAAC;MAEhD,MAAM3Z,YAAY,GAAGgH,YAAY,CAAChH,YAAY;MAC9ChK,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE+J,YAAY,CAAC;;MAElC;MACA,IAAIlD,OAAO;MAEX,IAAI6c,SAAS,IAAIA,SAAS,CAAC5c,MAAM,IAAI4c,SAAS,CAAC5c,MAAM,CAACgG,MAAM,GAAG,CAAC,EAAE;QAChE;QACA/M,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;QACtB0jB,SAAS,CAAC5c,MAAM,CAAC2E,OAAO,CAAC,CAAC8E,KAAK,EAAEgS,KAAK,KAAK;UACzCxiB,OAAO,CAACC,GAAG,CAAC,MAAMuiB,KAAK,GAAC,CAAC,QAAQhS,KAAK,CAACC,OAAO,QAAQD,KAAK,CAACQ,YAAY,UAAUR,KAAK,CAACS,UAAU,EAAE,CAAC;QACvG,CAAC,CAAC;QAEFnK,OAAO,gBACLpJ,OAAA;UAAKge,KAAK,EAAE;YAAE3V,OAAO,EAAE,KAAK;YAAEsB,KAAK,EAAE,OAAO;YAAEgV,SAAS,EAAE,OAAO;YAAEuH,SAAS,EAAE;UAAO,CAAE;UAAA/M,QAAA,gBACpFnZ,OAAA;YAAKge,KAAK,EAAE;cACVjU,UAAU,EAAE,MAAM;cAClBoc,YAAY,EAAE,KAAK;cACnBzd,QAAQ,EAAE,MAAM;cAChB0d,YAAY,EAAE,gBAAgB;cAC9BC,aAAa,EAAE;YACjB,CAAE;YAAAlN,QAAA,GACC,CAAA7M,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAACtD,OAAO,EAAC,GAC/C;UAAA;YAAA8U,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNpe,OAAA;YAAAmZ,QAAA,EACG8M,SAAS,CAAC5c,MAAM,CAAC0G,GAAG,CAAC,CAAC+C,KAAK,EAAEgS,KAAK,KAAK;cACtC,IAAIwB,UAAU;cACd,IAAIqB,SAAS;cAEb,QAAQ7U,KAAK,CAACQ,YAAY;gBACxB,KAAK,GAAG;kBACNgT,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;gBACF,KAAK,GAAG;kBACNrB,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;gBACF,KAAK,GAAG;gBACR;kBACErB,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;cACJ;cAEA,MAAM1U,SAAS,GAAGG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC;cAElD,oBACE/S,OAAA;gBAAiBge,KAAK,EAAE;kBACtBmI,YAAY,EAAE,KAAK;kBACnB7d,eAAe,EAAE,uBAAuB;kBACxCD,OAAO,EAAE,KAAK;kBACdG,YAAY,EAAE,KAAK;kBACnBE,QAAQ,EAAE;gBACZ,CAAE;gBAAAyQ,QAAA,gBACAnZ,OAAA;kBAAKge,KAAK,EAAE;oBAAEjU,UAAU,EAAE;kBAAO,CAAE;kBAAAoP,QAAA,GAChClG,SAAS,EAAC,oBAAQ,EAACH,KAAK,CAACC,OAAO,EAAC,GACpC;gBAAA;kBAAAkL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNpe,OAAA;kBAAKge,KAAK,EAAE;oBAAE9V,OAAO,EAAE,MAAM;oBAAEqe,cAAc,EAAE;kBAAgB,CAAE;kBAAApN,QAAA,gBAC/DnZ,OAAA;oBAAAmZ,QAAA,EAAM;kBAAI;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjBpe,OAAA;oBAAMge,KAAK,EAAE;sBACXlU,KAAK,EAAEwc,UAAU;sBACjBvc,UAAU,EAAE,MAAM;sBAClBzB,eAAe,EAAE,iBAAiB;sBAClCD,OAAO,EAAE,OAAO;sBAChBG,YAAY,EAAE;oBAChB,CAAE;oBAAA2Q,QAAA,EACCwO;kBAAS;oBAAA1J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNpe,OAAA;kBAAKge,KAAK,EAAE;oBAAE9V,OAAO,EAAE,MAAM;oBAAEqe,cAAc,EAAE;kBAAgB,CAAE;kBAAApN,QAAA,gBAC/DnZ,OAAA;oBAAAmZ,QAAA,EAAM;kBAAK;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClBpe,OAAA;oBAAMge,KAAK,EAAE;sBAAEjU,UAAU,EAAE;oBAAO,CAAE;oBAAAoP,QAAA,GAAErG,KAAK,CAACS,UAAU,EAAC,SAAE;kBAAA;oBAAA0K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA,GAzBE0G,KAAK;gBAAA7G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BV,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNpe,OAAA;YAAKge,KAAK,EAAE;cAAEwI,SAAS,EAAE,KAAK;cAAE9d,QAAQ,EAAE,MAAM;cAAEoB,KAAK,EAAE;YAAO,CAAE;YAAAqP,QAAA,GAAC,4BAC3D,EAAC,IAAInS,IAAI,CAACif,SAAS,CAAC9R,UAAU,CAAC,CAACsS,kBAAkB,CAAC,CAAC,EAAC,6BAC7D;UAAA;YAAAxI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MACH,CAAC,MAAM;QACL;QACAhV,OAAO,gBACLpJ,OAAA;UAAKge,KAAK,EAAE;YAAE3V,OAAO,EAAE,KAAK;YAAEsB,KAAK,EAAE;UAAQ,CAAE;UAAAwP,QAAA,gBAC7CnZ,OAAA;YAAKge,KAAK,EAAE;cACVjU,UAAU,EAAE,MAAM;cAClBoc,YAAY,EAAE,KAAK;cACnBzd,QAAQ,EAAE,MAAM;cAChB0d,YAAY,EAAE,gBAAgB;cAC9BC,aAAa,EAAE;YACjB,CAAE;YAAAlN,QAAA,GACC,CAAA7M,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAACtD,OAAO,EAAC,GAC/C;UAAA;YAAA8U,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNpe,OAAA;YAAKge,KAAK,EAAE;cAAElU,KAAK,EAAE,SAAS;cAAEpB,QAAQ,EAAE;YAAO,CAAE;YAAAyQ,QAAA,EAAC;UAEpD;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNpe,OAAA;YAAKge,KAAK,EAAE;cAAEwI,SAAS,EAAE,KAAK;cAAE9d,QAAQ,EAAE,MAAM;cAAEoB,KAAK,EAAE;YAAO,CAAE;YAAAqP,QAAA,GAAC,4BAC3D,EAAC,IAAInS,IAAI,CAAC,CAAC,CAACyf,kBAAkB,CAAC,CAAC;UAAA;YAAAxI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MACH;;MAEA;MACA,MAAM9Z,CAAC,GAAG5C,MAAM,CAACmV,UAAU,GAAG,CAAC;MAC/B,MAAMrS,CAAC,GAAG9C,MAAM,CAACoV,WAAW,GAAG,CAAC;;MAEhC;MACA,IAAIpV,MAAM,CAAC8H,uBAAuB,EAAE;QAAA,IAAAqe,UAAA;QAClCvlB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7C2G,OAAO,EAAE,IAAI;UACbC,OAAO;UACPtB,QAAQ,EAAE;YAAEvD,CAAC;YAAEE;UAAE;QACnB,CAAC,CAAC;QAEF9C,MAAM,CAAC8H,uBAAuB,CAAC;UAC7BN,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEA,OAAO;UAChBtB,QAAQ,EAAE;YAAEvD,CAAC;YAAEE;UAAE,CAAC;UAClB4E,OAAO,EAAEA,OAAO;UAChBC,MAAM,EAAE,EAAAwe,UAAA,GAAA5B,SAAS,cAAA4B,UAAA,uBAATA,UAAA,CAAWxe,MAAM,KAAI;QAC/B,CAAC,CAAC;QAEF/G,OAAO,CAACC,GAAG,CAAC,SAAS,CAAA+J,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAItD,OAAO,WAAW,CAAC;;QAE9D;QACA,IAAIzH,MAAM,CAAC6H,0BAA0B,EAAE;UACrC7H,MAAM,CAAC6H,0BAA0B,CAACiB,OAAO,GAAG4R,WAAW,CAACmL,yBAAyB,EAAEzhB,6BAA6B,GAAG,IAAI,CAAC;UACxHxD,OAAO,CAACC,GAAG,CAAC,qBAAqBuD,6BAA6B,IAAI,CAAC;QACrE;QAEA,OAAO,IAAI;MACb,CAAC,MAAM;QACLxD,OAAO,CAACsB,KAAK,CAAC,8BAA8B,CAAC;QAC7C,OAAO,KAAK;MACd;IACF,CAAC;IAED,OAAOgkB,gCAAgC,CAAC,CAAC;EAC3C,CAAC,CAAC,OAAOhkB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,MAAMqhB,yBAAyB,GAAIF,MAAM,IAAK;EAC5C,IAAIva,OAAO,GAAGua,MAAM;;EAEpB;EACA,IAAIva,OAAO,IAAIA,OAAO,CAACoT,QAAQ,IAAIpT,OAAO,CAACoT,QAAQ,CAACxP,IAAI,KAAK,cAAc,EAAE;IAC3E9L,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEiI,OAAO,CAACiC,IAAI,IAAI,KAAK,CAAC;IAChD,OAAOjC,OAAO;EAChB;;EAEA;EACA,OAAOA,OAAO,IAAIA,OAAO,CAACkY,MAAM,EAAE;IAChClY,OAAO,GAAGA,OAAO,CAACkY,MAAM;IACxB,IAAIlY,OAAO,CAACoT,QAAQ,IAAIpT,OAAO,CAACoT,QAAQ,CAACxP,IAAI,KAAK,cAAc,EAAE;MAChE9L,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEiI,OAAO,CAACiC,IAAI,IAAI,KAAK,CAAC;MAChD,OAAOjC,OAAO;IAChB;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACA9I,MAAM,CAAComB,kBAAkB,GAAG,CAACxjB,CAAC,EAAEE,CAAC,KAAK;EACpC,IAAI;IACFlC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE+B,CAAC,EAAEE,CAAC,CAAC;;IAEnC;IACA,MAAMib,MAAM,GAAGC,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAI,CAAChC,MAAM,EAAE;MACXnd,OAAO,CAACsB,KAAK,CAAC,sBAAsB,CAAC;MACrC,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAAC5C,KAAK,IAAI,CAAC6H,SAAS,CAAC2B,OAAO,EAAE;MAChClI,OAAO,CAACsB,KAAK,CAAC,iBAAiB,CAAC;MAChC,OAAO,KAAK;IACd;;IAEA;IACA,IAAIU,CAAC,KAAKkO,SAAS,IAAIhO,CAAC,KAAKgO,SAAS,EAAE;MACtClO,CAAC,GAAG5C,MAAM,CAACmV,UAAU,GAAG,CAAC;MACzBrS,CAAC,GAAG9C,MAAM,CAACoV,WAAW,GAAG,CAAC;IAC5B;;IAEA;IACA,MAAM8M,IAAI,GAAGnE,MAAM,CAACoE,qBAAqB,CAAC,CAAC;IAC3C,MAAMC,MAAM,GAAI,CAACxf,CAAC,GAAGsf,IAAI,CAAC7b,IAAI,IAAI0X,MAAM,CAACsE,WAAW,GAAI,CAAC,GAAG,CAAC;IAC7D,MAAMC,MAAM,GAAG,EAAE,CAACxf,CAAC,GAAGof,IAAI,CAACla,GAAG,IAAI+V,MAAM,CAACwE,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;IAE9D3hB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEuhB,MAAM,EAAEE,MAAM,CAAC;;IAErC;IACA,MAAME,SAAS,GAAG,IAAI5kB,KAAK,CAAC6kB,SAAS,CAAC,CAAC;IACvCD,SAAS,CAAC7E,MAAM,CAAC+E,MAAM,CAACC,SAAS,GAAG,CAAC;IACrCH,SAAS,CAAC7E,MAAM,CAACiF,IAAI,CAACD,SAAS,GAAG,CAAC;IAEnC,MAAME,WAAW,GAAG,IAAIjlB,KAAK,CAACklB,OAAO,CAACV,MAAM,EAAEE,MAAM,CAAC;IACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,EAAE1b,SAAS,CAAC2B,OAAO,CAAC;;IAEvD;IACA,MAAMka,mBAAmB,GAAG,EAAE;IAC9B/hB,gBAAgB,CAACqL,OAAO,CAAC,CAACiO,QAAQ,EAAE9S,OAAO,KAAK;MAC9C,IAAI8S,QAAQ,CAACpN,KAAK,EAAE;QAClB6V,mBAAmB,CAAChR,IAAI,CAACuI,QAAQ,CAACpN,KAAK,CAAC;QACxCvM,OAAO,CAACC,GAAG,CAAC,SAAS4G,OAAO,QAAQ,CAAC;MACvC;IACF,CAAC,CAAC;;IAEF;IACA7G,OAAO,CAACC,GAAG,CAAC,QAAQmiB,mBAAmB,CAACrV,MAAM,YAAY,CAAC;IAC3D,MAAM0Y,YAAY,GAAG7D,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,EAAE,IAAI,CAAC;IAE1E,IAAIqD,YAAY,CAAC1Y,MAAM,GAAG,CAAC,EAAE;MAC3B/M,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1BwlB,YAAY,CAAC/Z,OAAO,CAAC,CAAC6W,SAAS,EAAErY,CAAC,KAAK;QACrClK,OAAO,CAACC,GAAG,CAAC,MAAMiK,CAAC,GAAG,EAAEqY,SAAS,CAACE,MAAM,CAACtY,IAAI,IAAI,KAAK,EAC1C,KAAK,EAAEoY,SAAS,CAAC9f,QAAQ,EACzB,WAAW,EAAE8f,SAAS,CAACE,MAAM,CAACld,QAAQ,CAACoF,OAAO,CAAC,CAAC,EAChD,WAAW,EAAE4X,SAAS,CAACE,MAAM,CAACnH,QAAQ,CAAC;;QAEnD;QACA,MAAMoH,GAAG,GAAGC,yBAAyB,CAACJ,SAAS,CAACE,MAAM,CAAC;QACvD,IAAIC,GAAG,IAAIA,GAAG,CAACpH,QAAQ,IAAIoH,GAAG,CAACpH,QAAQ,CAACxP,IAAI,KAAK,cAAc,EAAE;UAC/D9L,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEyiB,GAAG,CAACpH,QAAQ,CAACzU,OAAO,CAAC;QAC/C;MACF,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;;IAEA;IACA7G,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,MAAMylB,eAAe,GAAG9D,SAAS,CAACU,gBAAgB,CAAC5jB,KAAK,CAACmY,QAAQ,EAAE,IAAI,CAAC;IAExE7W,OAAO,CAACC,GAAG,CAAC,WAAWylB,eAAe,CAAC3Y,MAAM,MAAM,CAAC;IACpD2Y,eAAe,CAACha,OAAO,CAAC,CAAC6W,SAAS,EAAErY,CAAC,KAAK;MACxC,MAAMwY,GAAG,GAAGH,SAAS,CAACE,MAAM;MAC5BziB,OAAO,CAACC,GAAG,CAAC,QAAQiK,CAAC,GAAG,EAAEwY,GAAG,CAACvY,IAAI,IAAI,KAAK,EAC/B,KAAK,EAAEuY,GAAG,CAAC5W,IAAI,EACf,KAAK,EAAE4W,GAAG,CAACnd,QAAQ,CAACoF,OAAO,CAAC,CAAC,EAC7B,KAAK,EAAE4X,SAAS,CAAC9f,QAAQ,EACzB,WAAW,EAAEigB,GAAG,CAACpH,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACAtb,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,IAAI0lB,YAAY,GAAG,CAAC;IAEpBtlB,gBAAgB,CAACqL,OAAO,CAAC,CAACiO,QAAQ,EAAE9S,OAAO,KAAK;MAC9C,IAAI8S,QAAQ,CAACpN,KAAK,EAAE;QAAA,IAAAqZ,qBAAA;QAClB;QACA,IAAIC,SAAS,GAAGlM,QAAQ,CAACpN,KAAK,CAAC3F,OAAO;QACtC,IAAIkf,cAAc,GAAG,IAAI;;QAEzB;QACA,MAAM/C,QAAQ,GAAG,IAAI/lB,KAAK,CAAC8F,OAAO,CAAC,CAAC;QACpC6W,QAAQ,CAACpN,KAAK,CAACyW,gBAAgB,CAACD,QAAQ,CAAC;;QAEzC;QACA,MAAMgD,gBAAgB,GAAGhD,QAAQ,CAACrgB,UAAU,CAAC6D,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAC;;QAExE;QACA,MAAM0d,SAAS,GAAGF,QAAQ,CAACjhB,KAAK,CAAC,CAAC,CAACohB,OAAO,CAAC3c,SAAS,CAAC2B,OAAO,CAAC;QAC7D,IAAIhF,IAAI,CAACK,GAAG,CAAC0f,SAAS,CAACjhB,CAAC,CAAC,GAAG,CAAC,IAAIkB,IAAI,CAACK,GAAG,CAAC0f,SAAS,CAAC/gB,CAAC,CAAC,GAAG,CAAC,IAAI+gB,SAAS,CAAC7gB,CAAC,GAAG,CAAC,CAAC,IAAI6gB,SAAS,CAAC7gB,CAAC,GAAG,CAAC,EAAE;UACjG0jB,cAAc,GAAG,KAAK;QACxB;QAEA,IAAID,SAAS,EAAE;UACbF,YAAY,EAAE;QAChB;QAEA3lB,OAAO,CAACC,GAAG,CAAC,OAAO4G,OAAO,GAAG,EAAE;UAC7Bmf,EAAE,EAAE,EAAAJ,qBAAA,GAAAjM,QAAQ,CAAC3P,YAAY,cAAA4b,qBAAA,uBAArBA,qBAAA,CAAuBzb,IAAI,KAAI,IAAI;UACvC8b,GAAG,EAAEJ,SAAS;UACdK,KAAK,EAAEJ,cAAc;UACrBK,IAAI,EAAEpD,QAAQ,CAACpY,OAAO,CAAC,CAAC;UACxByb,IAAI,EAAE,CAACnD,SAAS,CAACjhB,CAAC,EAAEihB,SAAS,CAAC/gB,CAAC,EAAE+gB,SAAS,CAAC7gB,CAAC,CAAC;UAC7CikB,MAAM,EAAEN;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF/lB,OAAO,CAACC,GAAG,CAAC,MAAM0lB,YAAY,IAAItlB,gBAAgB,CAACmb,IAAI,WAAW,CAAC;;IAEnE;IACA,OAAOkK,eAAe,CAAC3Y,MAAM,GAAG,CAAC;EACnC,CAAC,CAAC,OAAOzL,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,OAAO,KAAK;EACd;AACF,CAAC;;AAID;AACA,MAAMkQ,wBAAwB,GAAGA,CAACR,YAAY,EAAEG,SAAS,KAAK;EAAA,IAAAmV,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC5D,IAAI,CAACxV,YAAY,IAAI,CAACA,YAAY,CAACzE,KAAK,IAAI,CAAC4E,SAAS,EAAE;IACtD;EACF;;EAEA;EACA,MAAMsV,cAAc,GAAG,EAAE;EACzBzV,YAAY,CAACzE,KAAK,CAACmC,QAAQ,CAACC,KAAK,IAAI;IACnC,IAAIA,KAAK,CAAC2M,QAAQ,IAAI3M,KAAK,CAAC2M,QAAQ,CAACoL,OAAO,EAAE;MAC5CD,cAAc,CAACrV,IAAI,CAACzC,KAAK,CAAC;IAC5B;EACF,CAAC,CAAC;EAEF8X,cAAc,CAAC/a,OAAO,CAAC+X,KAAK,IAAI;IAC9BzS,YAAY,CAACzE,KAAK,CAACqB,MAAM,CAAC6V,KAAK,CAAC;EAClC,CAAC,CAAC;;EAEF;EACA,IAAIO,UAAU;EACd,QAAO7S,SAAS,CAACH,YAAY;IAC3B,KAAK,GAAG;MACNgT,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;MACNA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;IACR;MACEA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;EACJ;;EAEA;EACA,MAAMlD,aAAa,GAAG,IAAI9jB,KAAK,CAACke,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAM6F,aAAa,GAAG,IAAI/jB,KAAK,CAAC0d,iBAAiB,CAAC;IAChDlT,KAAK,EAAEwc,UAAU;IACjBjV,QAAQ,EAAEiV,UAAU;IACpB2C,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM3F,SAAS,GAAG,IAAIhkB,KAAK,CAAC2d,IAAI,CAACmG,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAACzb,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAClC2e,SAAS,CAAC1F,QAAQ,GAAG;IACnBoL,OAAO,EAAE,IAAI;IACb5a,IAAI,EAAE,cAAc;IACpBjF,OAAO,GAAAyf,qBAAA,GAAEtV,YAAY,CAAChH,YAAY,cAAAsc,qBAAA,uBAAzBA,qBAAA,CAA2Bzf,OAAO;IAC3C4J,OAAO,EAAEU,SAAS,CAACV,OAAO;IAC1BE,SAAS,EAAEQ,SAAS,CAACR,SAAS;IAC9BM,UAAU,EAAEE,SAAS,CAACF;EACxB,CAAC;;EAED;EACA,MAAMwS,KAAK,GAAG,IAAIzmB,KAAK,CAAC4pB,UAAU,CAAC5C,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;EACrDP,KAAK,CAACle,QAAQ,CAAClD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAC5BohB,KAAK,CAACnI,QAAQ,GAAG;IAAEoL,OAAO,EAAE;EAAK,CAAC;;EAElC;EACA1V,YAAY,CAACzE,KAAK,CAACa,GAAG,CAAC4T,SAAS,CAAC;EACjChQ,YAAY,CAACzE,KAAK,CAACa,GAAG,CAACqW,KAAK,CAAC;EAE7BzjB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAAsmB,sBAAA,GAAAvV,YAAY,CAAChH,YAAY,cAAAuc,sBAAA,uBAAzBA,sBAAA,CAA2Bpc,IAAI,OAAAqc,sBAAA,GAAIxV,YAAY,CAAChH,YAAY,cAAAwc,sBAAA,uBAAzBA,sBAAA,CAA2B3f,OAAO,cAAasK,SAAS,CAACH,YAAY,WAAWG,SAAS,CAACF,UAAU,GAAG,CAAC;AACjK,CAAC;AAED,eAAexN,WAAW;AAAC,IAAAmZ,EAAA;AAAAiK,YAAA,CAAAjK,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}