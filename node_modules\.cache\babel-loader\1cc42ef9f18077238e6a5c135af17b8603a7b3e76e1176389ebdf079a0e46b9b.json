{"ast": null, "code": "import React,{useState,useEffect,useRef,useCallback}from'react';import{Row,Col,Card,Statistic,List,Table,Descriptions,Spin,Badge}from'antd';import*as echarts from'echarts/core';import{Bar<PERSON>hart}from'echarts/charts';import{GridComponent,TooltipComponent,LegendComponent,TitleComponent}from'echarts/components';import{CanvasRenderer}from'echarts/renderers';import styled from'styled-components';import CollapsibleSidebar from'../components/layout/CollapsibleSidebar';import axios from'axios';import vehiclesData from'../data/vehicles.json';// 注册必要的echarts组件\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";echarts.use([BarChart,GridComponent,TooltipComponent,LegendComponent,TitleComponent,CanvasRenderer]);const StyledCanvas=styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;// 页面布局容器\nconst PageContainer=styled.div`\n  display: flex;\n  height: calc(100vh - 65px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;// 左侧信息栏容器\nconst LeftSidebar=styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;// 右侧信息栏容器\nconst RightSidebar=styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;// 主内容区域\nconst MainContent=styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props=>props.leftCollapsed&&props.rightCollapsed?'0 24px':props.leftCollapsed?'0 8px 0 24px':props.rightCollapsed?'0 24px 0 8px':'0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props=>props.leftCollapsed&&props.rightCollapsed?'0 24px':props.leftCollapsed?'0 8px 0 0':props.rightCollapsed?'0 0 0 8px':'0'};\n  position: relative;\n  z-index: 1;\n`;// 信息卡片\nconst InfoCard=styled(Card)`\n  margin-bottom: 12px;\n  height: ${props=>props.height||'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;// 自定义统计数字组件\nconst CompactStatistic=styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n  \n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;const RealTimeTraffic=()=>{const[loading,setLoading]=useState(true);const[vehicles,setVehicles]=useState([]);const[events,setEvents]=useState(()=>{try{const savedEvents=localStorage.getItem('realTimeTrafficEvents');return savedEvents?JSON.parse(savedEvents):[];}catch(error){console.error('读取事件列表失败:',error);return[];}});const[selectedVehicle,setSelectedVehicle]=useState(null);const[stats,setStats]=useState({totalVehicles:0,onlineVehicles:0,offlineVehicles:0,totalDevices:0,onlineDevices:0,offlineDevices:0});// 存储在线车辆的 BSM ID，初始时为空集合，表示所有车辆离线\nconst[onlineBsmIds,setOnlineBsmIds]=useState(()=>{try{const savedOnlineIds=localStorage.getItem('realTimeTrafficOnlineIds');// 返回空集合，确保所有车辆初始状态为离线\nreturn new Set();}catch(error){console.error('读取在线ID缓存失败:',error);return new Set();}});// 存储最后一次收到 BSM 消息的时间\nconst lastBsmTime=useRef({});const eventChartRef=useRef(null);// 添加侧边栏折叠状态\nconst[leftCollapsed,setLeftCollapsed]=useState(false);const[rightCollapsed,setRightCollapsed]=useState(false);const[eventStats,setEventStats]=useState(()=>{try{const savedStats=localStorage.getItem('realTimeTrafficEventStats');return savedStats?JSON.parse(savedStats):{'401':0,// 道路抛洒物\n'404':0,// 道路障碍物\n'405':0,// 行人通过马路\n'904':0,// 逆行车辆\n'910':0,// 违停车辆\n'1002':0,// 道路施工\n'901':0// 车辆超速\n};}catch(error){console.error('读取事件统计数据失败:',error);return{'401':0,'404':0,'405':0,'904':0,'910':0,'1002':0,'901':0};}});// 添加RSI事件缓存，用于检测重复事件\nconst prevRsiEvents=useRef(new Map());// 添加手动更新车辆状态和位置信息的函数\nconst updateVehicleStatus=useCallback(function(bsmId,status){let speed=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;let lat=arguments.length>3&&arguments[3]!==undefined?arguments[3]:0;let lng=arguments.length>4&&arguments[4]!==undefined?arguments[4]:0;let heading=arguments.length>5&&arguments[5]!==undefined?arguments[5]:0;// 确保速度和航向角是格式化的数值，保留两位小数\nconst formattedSpeed=parseFloat(speed).toFixed(0);const formattedHeading=parseFloat(heading).toFixed(2);console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${formattedSpeed}, 位置=(${lat}, ${lng})`);// 更新在线状态\nif(status==='online'){setOnlineBsmIds(prev=>new Set([...prev,bsmId]));lastBsmTime.current[bsmId]=Date.now();}else{setOnlineBsmIds(prev=>{const newSet=new Set(prev);newSet.delete(bsmId);return newSet;});}// 更新车辆信息\nsetVehicles(prevVehicles=>prevVehicles.map(vehicle=>vehicle.bsmId===bsmId?{...vehicle,status:status,speed:parseFloat(formattedSpeed),// 确保是数值类型\nlat:parseFloat(lat.toFixed(7)),lng:parseFloat(lng.toFixed(7)),heading:parseFloat(formattedHeading)// 确保是数值类型\n}:vehicle));},[]);// 在组件挂载时，暴露updateVehicleStatus函数到window对象\nuseEffect(()=>{window.updateVehicleStatus=updateVehicleStatus;// 用于监听CampusModel是否接收到BSM消息的事件\nconst handleRealBsmReceived=event=>{if(event.data&&event.data.type==='realBsmReceived'){// console.log('收到CampusModel发送的真实BSM消息通知');\n}};window.addEventListener('message',handleRealBsmReceived);// 确保页面刷新时清空在线车辆状态\nconsole.log('组件初始化，重置所有车辆为离线状态');setOnlineBsmIds(new Set());lastBsmTime.current={};return()=>{window.removeEventListener('message',handleRealBsmReceived);delete window.updateVehicleStatus;};},[updateVehicleStatus]);// 使用防抖保存事件列表到 localStorage\nuseEffect(()=>{const saveTimer=setTimeout(()=>{try{localStorage.setItem('realTimeTrafficEvents',JSON.stringify(events));}catch(error){console.error('保存事件列表失败:',error);}},1000);// 延迟1秒保存，避免频繁写入\nreturn()=>clearTimeout(saveTimer);},[events]);// 使用防抖保存事件统计数据到 localStorage\nuseEffect(()=>{const saveTimer=setTimeout(()=>{try{localStorage.setItem('realTimeTrafficEventStats',JSON.stringify(eventStats));}catch(error){console.error('保存事件统计数据失败:',error);}},1000);// 延迟1秒保存，避免频繁写入\nreturn()=>clearTimeout(saveTimer);},[eventStats]);// 获取车辆数据\nconst fetchVehicles=useCallback(async()=>{try{// 先尝试从API获取最新数据\nconsole.log('尝试从API获取最新车辆数据');const apiData=await fetchLatestVehiclesData(true);// 如果API获取成功，直接使用API数据\nif(apiData&&apiData.length>0){console.log('成功从API获取车辆数据，车辆数量:',apiData.length);// 获取所有车辆的BSM ID列表\nconst bsmIds=apiData.map(v=>v.bsmId).filter(id=>id);console.log('所有车辆的BSM ID:',bsmIds);// 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\nconst updatedVehicles=apiData.map(vehicle=>{// 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\nconst isOnline=vehicle.bsmId&&onlineBsmIds.has(vehicle.bsmId);return{...vehicle,plate:vehicle.plateNumber,// 适配表格显示\nstatus:isOnline?'online':'offline',// 只有收到BSM消息的车辆才显示为在线\nspeed:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.speed||0:0:0,lat:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.lat||0:0:0,lng:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.lng||0:0:0,heading:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.heading||0:0:0};});setVehicles(updatedVehicles);// 直接检查更新后的车辆在线状态\nconst onlineCount=updatedVehicles.filter(v=>v.status==='online').length;const totalCount=updatedVehicles.length;console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount-onlineCount}`);// 更新车辆统计数据\nsetStats(prevStats=>({...prevStats,totalVehicles:totalCount,onlineVehicles:onlineCount,offlineVehicles:totalCount-onlineCount}));return;}// 如果API获取失败，回退到本地JSON文件\nconsole.log('API获取失败，回退到本地vehiclesData');const vehiclesList=vehiclesData.vehicles||[];console.log('从vehiclesData获取车辆数据，车辆数量:',vehiclesList.length);// 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\nconst updatedVehicles=vehiclesList.map(vehicle=>{// 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\nconst isOnline=vehicle.bsmId&&onlineBsmIds.has(vehicle.bsmId);return{...vehicle,plate:vehicle.plateNumber,// 适配表格显示\nstatus:isOnline?'online':'offline',// 只有收到BSM消息的车辆才显示为在线\nspeed:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.speed||0:0:0,lat:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.lat||0:0:0,lng:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.lng||0:0:0,heading:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.heading||0:0:0};});setVehicles(updatedVehicles);// 直接检查更新后的车辆在线状态\nconst onlineCount=updatedVehicles.filter(v=>v.status==='online').length;const totalCount=updatedVehicles.length;console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount-onlineCount}`);// 更新车辆统计数据\nsetStats(prevStats=>({...prevStats,totalVehicles:totalCount,onlineVehicles:onlineCount,offlineVehicles:totalCount-onlineCount}));}catch(error){console.error('获取车辆列表失败:',error);}},[onlineBsmIds]);// 获取设备统计数据\nconst fetchDeviceStats=async()=>{try{const apiUrl=process.env.REACT_APP_API_URL||'http://localhost:5000';const response=await axios.get(`${apiUrl}/api/devices`);if(response.data&&response.data.success){const devicesData=response.data.data;// 更新设备统计数据\nsetStats(prevStats=>({...prevStats,totalDevices:devicesData.length,onlineDevices:devicesData.filter(d=>d.status==='online').length,offlineDevices:devicesData.filter(d=>d.status==='offline').length}));}}catch(error){console.error('获取设备统计数据失败:',error);}};// 监听 BSM 消息\nuseEffect(()=>{const handleBsmMessage=event=>{if(event.data&&event.data.type==='bsm'){// 获取bsmId，确保它正确地从消息中提取\nconst bsmData=event.data.data||{};const bsmId=bsmData.bsmId||event.data.bsmId;if(!bsmId){console.error('BSM消息缺少bsmId:',event.data);return;}// console.log('收到BSM消息，ID:', bsmId);\nconst now=Date.now();// 更新最后接收时间\nlastBsmTime.current[bsmId]=now;// 添加到在线bsmId集合\nsetOnlineBsmIds(prev=>new Set([...prev,bsmId]));// 提取正确的BSM数据并格式化为两位小数\nconst speed=parseFloat((parseFloat(bsmData.partSpeed||event.data.speed||0)*3.6).toFixed(0));// 转换为km/h，保留两位小数\nconst lat=parseFloat(parseFloat(bsmData.partLat||event.data.lat||0).toFixed(7));const lng=parseFloat(parseFloat(bsmData.partLong||event.data.lng||0).toFixed(7));const heading=parseFloat(parseFloat(bsmData.partHeading||event.data.heading||0).toFixed(2));// 保留两位小数\n// console.log(`车辆${bsmId}状态: 速度=${speed.toFixed(2)}km/h, 位置=(${lat.toFixed(7)}, ${lng.toFixed(7)}), 航向=${heading.toFixed(2)}°`);\n// 更新车辆状态和位置信息\nsetVehicles(prevVehicles=>{// 检查是否找到对应车辆\nconst foundVehicle=prevVehicles.find(v=>v.bsmId===bsmId);if(!foundVehicle){console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);return prevVehicles;}const updatedVehicles=prevVehicles.map(vehicle=>vehicle.bsmId===bsmId?{...vehicle,status:'online',speed:speed,lat:lat,lng:lng,heading:heading}:vehicle);// 计算更新后的在线车辆数量\nconst onlineCount=updatedVehicles.filter(v=>v.status==='online').length;const totalCount=updatedVehicles.length;// 更新统计数据\nsetStats(prevStats=>({...prevStats,onlineVehicles:onlineCount,offlineVehicles:totalCount-onlineCount}));return updatedVehicles;});}};// 添加消息监听器\nwindow.addEventListener('message',handleBsmMessage);// 清理函数\nreturn()=>{window.removeEventListener('message',handleBsmMessage);};},[]);// 修改检查在线状态的useEffect，同步更新统计信息\nuseEffect(()=>{const checkOnlineStatus=()=>{const now=Date.now();console.log('检查车辆在线状态...');// 只有在特定条件下才将车辆设为离线\n// 例如，只有收到过BSM消息的车辆才会被检查是否超时\nsetOnlineBsmIds(prev=>{const newOnlineBsmIds=new Set(prev);let hasChanges=false;// 仅检查已有最后更新时间的车辆\nprev.forEach(bsmId=>{const lastTime=lastBsmTime.current[bsmId];// 只有收到过BSM消息的车辆才会被检查是否超时\nif(lastTime&&now-lastTime>30000){console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);newOnlineBsmIds.delete(bsmId);hasChanges=true;}});if(hasChanges){// 更新车辆状态\nsetVehicles(prevVehicles=>{const updatedVehicles=prevVehicles.map(vehicle=>{// 只更新有最后更新时间的车辆状态\nif(lastBsmTime.current[vehicle.bsmId]){const isOnline=newOnlineBsmIds.has(vehicle.bsmId);return{...vehicle,status:isOnline?'online':'offline'};}return vehicle;});// 计算更新后的在线车辆数量\nconst onlineCount=updatedVehicles.filter(v=>v.status==='online').length;const totalCount=updatedVehicles.length;// 更新统计数据\nsetStats(prevStats=>({...prevStats,onlineVehicles:onlineCount,offlineVehicles:totalCount-onlineCount}));return updatedVehicles;});}return newOnlineBsmIds;});};const interval=setInterval(checkOnlineStatus,5000);return()=>clearInterval(interval);},[]);// 重置所有车辆的初始状态\nuseEffect(()=>{// 将所有车辆状态重置为离线\nconst resetAllVehicles=()=>{// 只有在没有任何BSM消息的情况下才执行重置\nif(onlineBsmIds.size===0){setVehicles(prevVehicles=>prevVehicles.map(vehicle=>({...vehicle,status:'offline',speed:0,lat:0,lng:0,heading:0})));console.log('已重置所有车辆为离线状态');}};// 初始执行一次\nresetAllVehicles();// 然后每30秒检查一次\nconst interval=setInterval(resetAllVehicles,30000);return()=>clearInterval(interval);},[onlineBsmIds]);// 在组件挂载时获取数据\nuseEffect(()=>{const loadData=async()=>{setLoading(true);fetchVehicles();await fetchDeviceStats();setLoading(false);};loadData();// // 降低更新频率，避免频繁覆盖状态\n// const interval = setInterval(loadData, 300000); // 每60x5秒更新一次\n// return () => clearInterval(interval);\n},[]);// 添加对车辆数据变更的监听\nuseEffect(()=>{console.log('设置车辆数据变更监听');// 监听自定义事件\nconst handleVehiclesDataChanged=()=>{console.log('检测到车辆数据变更事件，重新获取车辆列表');fetchVehicles();};// 监听localStorage变化\nconst handleStorageChange=event=>{if(event.key==='vehiclesLastUpdated'||event.key==='vehiclesData'){console.log('检测到localStorage变化，重新获取车辆列表');fetchVehicles();}};// 添加事件监听器\nwindow.addEventListener('vehiclesDataChanged',handleVehiclesDataChanged);window.addEventListener('storage',handleStorageChange);// 初始检查是否有更新\nconst lastUpdated=localStorage.getItem('vehiclesLastUpdated');if(lastUpdated){console.log('初始检查到vehiclesLastUpdated:',lastUpdated);fetchVehicles();}// 设置更频繁的强制轮询，确保在部署环境中也能获取最新数据\nconst forcedPollingInterval=setInterval(()=>{console.log('强制轮询：重新获取车辆列表');fetchVehicles();},10000);// 每10秒强制刷新一次\n// 清理函数\nreturn()=>{window.removeEventListener('vehiclesDataChanged',handleVehiclesDataChanged);window.removeEventListener('storage',handleStorageChange);clearInterval(forcedPollingInterval);};},[]);// 添加检查本地缓存与API数据是否一致的机制\n// 定义一个单独的API调用函数，用于获取最新的车辆列表数据\nconst fetchLatestVehiclesData=async function(){let returnData=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;try{// 尝试多个可能的API地址\nconst possibleApiUrls=[process.env.REACT_APP_API_URL||'http://localhost:5000'// 'http://localhost:5000',\n// window.location.origin, // 当前站点的根URL\n// `${window.location.origin}/api`, // 当前站点下的/api路径\n// 'http://localhost:5000/api',\n// 'http://127.0.0.1:5000',\n// 'http://127.0.0.1:5000/api'\n];console.log('尝试从多个API地址获取车辆数据');// // 尝试从本地JSON文件直接获取\n// try {\n//   console.log('尝试从本地JSON文件获取数据');\n//   const jsonResponse = await axios.get('/vehicles.json');\n//   if (jsonResponse.data && jsonResponse.data.vehicles) {\n//     console.log('成功从本地JSON文件获取数据:', jsonResponse.data.vehicles.length);\n//     if (returnData) {\n//       return jsonResponse.data.vehicles;\n//     }\n//     processVehiclesData(jsonResponse.data.vehicles);\n//     return jsonResponse.data.vehicles;\n//   }\n// } catch (jsonError) {\n//   console.log('从本地JSON获取失败:', jsonError.message);\n// }\n// 逐个尝试API地址\nlet succeeded=false;let vehiclesData=null;for(const apiUrl of possibleApiUrls){if(succeeded)break;try{console.log(`尝试从 ${apiUrl}/api/vehicles/list 获取数据`);const response=await axios.get(`${apiUrl}/api/vehicles/list`);if(response.data&&response.data.vehicles){console.log(`成功从 ${apiUrl} 获取数据:`,response.data.vehicles.length);vehiclesData=response.data.vehicles;if(returnData){return vehiclesData;}processVehiclesData(response.data.vehicles);succeeded=true;break;}}catch(error){console.log(`从 ${apiUrl} 获取失败:`,error.message);// 继续尝试下一个URL\n}}if(!succeeded&&!returnData){console.log('所有API地址都获取失败，尝试使用vehicles.json');// 尝试从当前页面获取\ntry{const response=await fetch('/vehicles.json');if(response.ok){const data=await response.json();if(data&&data.vehicles){console.log('从public/vehicles.json获取数据成功:',data.vehicles.length);processVehiclesData(data.vehicles);return data.vehicles;}}}catch(e){console.error('从public/vehicles.json获取失败:',e);}}return vehiclesData||[];}catch(error){console.error('获取车辆列表失败:',error);return[];}};// 添加处理车辆数据的辅助函数\nconst processVehiclesData=newVehicles=>{// 与当前列表比较\nif(vehicles.length!==newVehicles.length){console.log('检测到车辆数量变化，从',vehicles.length,'到',newVehicles.length);fetchVehicles();// 重新加载\nreturn;}// 检查是否有新车辆ID\nconst currentIds=new Set(vehicles.map(v=>v.id));const hasNewVehicle=newVehicles.some(v=>!currentIds.has(v.id));if(hasNewVehicle){console.log('检测到新增车辆');fetchVehicles();// 重新加载\n}};// 添加自动选择第一个车辆的逻辑\nuseEffect(()=>{// 当车辆列表加载完成且有车辆数据时\nif(vehicles.length>0&&!selectedVehicle){// 自动选择第一个车辆\nsetSelectedVehicle(vehicles[0]);console.log('已自动选择第一个车辆:',vehicles[0].plateNumber);}},[vehicles,selectedVehicle]);// 修改图表初始化代码\nuseEffect(()=>{let chart=null;let handleResize=null;let lastUpdateTime=new Date();// 确保 DOM 元素存在\nif(!eventChartRef.current){console.error('图表容器未找到');return;}// 检查并清理已存在的图表实例\nlet existingChart=echarts.getInstanceByDom(eventChartRef.current);if(existingChart){existingChart.dispose();}try{// 初始化新的图表实例\nchart=echarts.init(eventChartRef.current);// 设置图表配置\nconst updateChart=()=>{const currentTime=new Date();lastUpdateTime=currentTime;// 事件类型配置\nconst eventTypes=[{type:'401',name:'道路抛洒物',color:'#ff4d4f'},{type:'404',name:'道路障碍物',color:'#faad14'},{type:'405',name:'行人通过马路',color:'#1890ff'},{type:'904',name:'逆行车辆',color:'#f5222d'},{type:'910',name:'违停车辆',color:'#722ed1'},{type:'1002',name:'道路施工',color:'#fa8c16'},{type:'901',name:'车辆超速',color:'#eb2f96'}];// 处理数据\nconst data=eventTypes.map(event=>({value:eventStats[event.type]||0,name:event.name,itemStyle:{color:event.color}})).filter(item=>item.value>0).sort((a,b)=>b.value-a.value);const option={title:{text:`最后更新: ${currentTime.toLocaleTimeString()}`,left:'center',top:-5,textStyle:{fontSize:12,color:'#999'}},grid:{top:30,bottom:0,left:0,right:50,containLabel:true},animation:true,animationDuration:0,animationDurationUpdate:1000,animationEasingUpdate:'quinticInOut',tooltip:{trigger:'axis',axisPointer:{type:'shadow'}},xAxis:{type:'value',show:false,splitLine:{show:false}},yAxis:{type:'category',data:data.map(item=>item.name),axisLabel:{fontSize:12,color:'#666',margin:8},axisTick:{show:false},axisLine:{show:false}},series:[{type:'bar',data:data,barWidth:'50%',label:{show:true,position:'right',formatter:'{c}次',fontSize:12,color:'#666'},itemStyle:{borderRadius:[0,4,4,0]},realtimeSort:false,animationDelay:function(idx){return idx*100;}}]};// 使用 notMerge: false 来保持增量更新\nchart.setOption(option,{notMerge:false,replaceMerge:['series']});};// 初始更新\nupdateChart();// 监听事件统计变化，每分钟更新一次\nconst statsInterval=setInterval(updateChart,60000);// 60000ms = 1分钟\n// 监听窗口大小变化\nhandleResize=()=>{var _chart;(_chart=chart)===null||_chart===void 0?void 0:_chart.resize();};window.addEventListener('resize',handleResize);// 清理函数\nreturn()=>{clearInterval(statsInterval);if(handleResize){window.removeEventListener('resize',handleResize);}if(chart){chart.dispose();}};}catch(error){console.error('初始化图表失败:',error);}},[eventStats]);// 修改 RSI 消息处理逻辑\nuseEffect(()=>{const handleRsiMessage=event=>{try{if(event.data&&event.data.type==='RSI'){const rsiData=event.data.data;if(!rsiData||!rsiData.rtes)return;if(rsiData.rtes.length>0){console.log('RealTimeTraffic 收到 RSI 消息:',event.data);}const latitude=parseFloat(rsiData.posLat);const longitude=parseFloat(rsiData.posLong);const rsuId=rsiData.rsuId;const mac=event.data.mac||'';const timestamp=event.data.tm||Date.now();// 统计本帧所有非重复事件类型\nconst nonDuplicateEventTypes=[];rsiData.rtes.forEach(event=>{const eventType=event.eventType;// const eventKey = `${rsuId}_${mac}_${eventType}`;\nlet latFixed='';let lngFixed='';if(eventType=='904'||eventType=='901'){// latFixed = latitude.toFixed(3);\n// lngFixed = longitude.toFixed(3);\nlatFixed=Math.floor(latitude*Math.pow(10,3))/Math.pow(10,3);lngFixed=Math.floor(longitude*Math.pow(10,3))/Math.pow(10,3);}else{// latFixed = latitude.toFixed(4);\n// lngFixed = longitude.toFixed(4);\nlatFixed=Math.floor(latitude*Math.pow(10,4))/Math.pow(10,4);lngFixed=Math.floor(longitude*Math.pow(10,4))/Math.pow(10,4);}const eventKey=`${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;const isDuplicate=checkDuplicateEvent(eventKey,timestamp,{lat:latitude,lng:longitude},prevRsiEvents.current);// 获取事件类型的中文描述\nlet eventTypeText='';let eventColor='';switch(eventType){case'401':eventTypeText='道路抛洒物';eventColor='#ff4d4f';break;case'404':eventTypeText='道路障碍物';eventColor='#faad14';break;case'405':eventTypeText='行人通过马路';eventColor='#1890ff';break;case'904':eventTypeText='逆行车辆';eventColor='#f5222d';break;case'910':eventTypeText='违停车辆';eventColor='#722ed1';break;case'1002':eventTypeText='道路施工';eventColor='#fa8c16';break;case'901':eventTypeText='车辆超速';eventColor='#eb2f96';break;default:eventTypeText=event.description||'未知事件';eventColor='#8c8c8c';}// 更新事件列表\n// const newEvent = {\n//   key: Date.now() + Math.random(),\n//   type: eventTypeText,\n//   time: new Date().toLocaleTimeString(),\n//   vehicle: rsiData.rsuId || '未知设备',\n//   color: eventColor,\n//   eventType: eventType,\n//   location: {\n//     latitude: latitude,\n//     longitude: longitude\n//   }\n// };\n// setEvents(prev => {\n//   const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n//   return newEvents;\n// });\nif(!isDuplicate){// 更新事件列表\nconst newEvent={key:Date.now()+Math.random(),type:eventTypeText,time:new Date().toLocaleTimeString(),vehicle:rsiData.rsuId||'未知设备',color:eventColor,eventType:eventType,location:{latitude:latitude,longitude:longitude}};setEvents(prev=>{const newEvents=[newEvent,...prev].slice(0,10);// 只保留最近10条记录\nreturn newEvents;});nonDuplicateEventTypes.push(eventType);}else{console.log(`检测到重复事件，不累计统计: ${eventTypeText}`);}});// 只累加一次\nif(nonDuplicateEventTypes.length>0){setEventStats(prev=>{const newStats={...prev};nonDuplicateEventTypes.forEach(eventType=>{newStats[eventType]=(newStats[eventType]||0)+1;});return newStats;});}}}catch(error){console.error('处理 RSI 消息失败:',error);}};// 添加消息监听器\nwindow.addEventListener('message',handleRsiMessage);return()=>{window.removeEventListener('message',handleRsiMessage);};},[]);// 在组件中添加检查重复事件的辅助函数\nconst checkDuplicateEvent=(eventKey,currentTime,currentPos,prevEvents)=>{// 检查是否有相同事件的历史记录\nif(prevEvents.has(eventKey)){const prevEvent=prevEvents.get(eventKey);const timeDiff=currentTime-prevEvent.time;// 如果时间差小于5000ms\nif(timeDiff<5000){// 计算两个位置之间的距离\nconst distance=calculateDistance(currentPos.lat,currentPos.lng,prevEvent.pos.lat,prevEvent.pos.lng);// 如果距离小于5米，认为是同一个事件\nif(distance<5){// 更新时间戳，但保持为重复事件\nprevEvents.set(eventKey,{time:currentTime,pos:currentPos});return true;}}}// 保存当前事件信息用于后续比较\nprevEvents.set(eventKey,{time:currentTime,pos:currentPos});// 不是重复事件\nreturn false;};// 添加计算两点之间距离的函数\nconst calculateDistance=(lat1,lon1,lat2,lon2)=>{// 如果任何坐标为0，表示坐标无效，返回大距离以避免被视为相同事件\nif(lat1===0||lon1===0||lat2===0||lon2===0){return 999;}// 计算两点之间的距离（哈弗辛公式）\nconst R=6371000;// 地球半径，单位：米\nconst dLat=(lat2-lat1)*Math.PI/180;const dLon=(lon2-lon1)*Math.PI/180;const a=Math.sin(dLat/2)*Math.sin(dLat/2)+Math.cos(lat1*Math.PI/180)*Math.cos(lat2*Math.PI/180)*Math.sin(dLon/2)*Math.sin(dLon/2);const c=2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a));const distance=R*c;return distance;};// 处理车辆选择\nconst handleVehicleSelect=vehicle=>{console.log('选择车辆:',vehicle.plateNumber,'状态:',vehicle.status);setSelectedVehicle(vehicle);};// 修改车辆状态更新逻辑，确保同步更新selectedVehicle\nuseEffect(()=>{// 当车辆列表更新后，如果当前有选中的车辆，则更新选中的车辆信息\nif(selectedVehicle){const updatedSelectedVehicle=vehicles.find(v=>v.id===selectedVehicle.id);if(updatedSelectedVehicle&&(updatedSelectedVehicle.status!==selectedVehicle.status||updatedSelectedVehicle.speed!==selectedVehicle.speed||updatedSelectedVehicle.lat!==selectedVehicle.lat||updatedSelectedVehicle.lng!==selectedVehicle.lng||updatedSelectedVehicle.heading!==selectedVehicle.heading)){console.log(`更新选中车辆 ${selectedVehicle.plateNumber} 的信息:`,`状态从 ${selectedVehicle.status} 变为 ${updatedSelectedVehicle.status}`);setSelectedVehicle(updatedSelectedVehicle);}}},[vehicles,selectedVehicle]);// 车辆列表列定义\nconst vehicleColumns=[{title:'车牌号',dataIndex:'plate',key:'plate',width:'40%'},{title:'状态',dataIndex:'status',key:'status',width:'30%',render:status=>/*#__PURE__*/_jsx(Badge,{status:status==='online'?'success':'error',text:status==='online'?'在线':'离线'})},{title:'速度',dataIndex:'speed',key:'speed',width:'30%',render:speed=>`${typeof speed==='number'?speed.toFixed(0):speed} km/h`}];// 修改实时事件列表的渲染\nconst renderEventList=()=>/*#__PURE__*/_jsx(List,{size:\"small\",dataSource:events,renderItem:item=>/*#__PURE__*/_jsx(List.Item,{style:{padding:'8px 0'},children:/*#__PURE__*/_jsx(List.Item.Meta,{title:/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'13px',marginBottom:'4px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:item.color,marginRight:'8px'},children:item.type}),/*#__PURE__*/_jsx(\"span\",{style:{color:'#666',fontSize:'12px'},children:item.time})]}),description:/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#666'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u8BBE\\u5907: \",item.vehicle]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u4F4D\\u7F6E: \",item.location?`${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}`:'未知位置']})]})})}),style:{maxHeight:'calc(100% - 24px)',overflowY:'auto'}});// 添加强制定期从API获取最新数据的机制\nuseEffect(()=>{const apiPollingInterval=setInterval(()=>{console.log('直接从API检查车辆数据更新');fetchLatestVehiclesData();},15000);// 每15秒检查一次API\nreturn()=>clearInterval(apiPollingInterval);},[vehicles]);return/*#__PURE__*/_jsx(Spin,{spinning:loading,tip:\"\\u52A0\\u8F7D\\u4E2D...\",children:/*#__PURE__*/_jsxs(PageContainer,{children:[/*#__PURE__*/_jsxs(CollapsibleSidebar,{position:\"left\",collapsed:leftCollapsed,onCollapse:()=>setLeftCollapsed(!leftCollapsed),children:[/*#__PURE__*/_jsx(InfoCard,{title:\"\\u8F66\\u8F86\\u548C\\u8BBE\\u5907\\u7EDF\\u8BA1\",bordered:false,height:\"160px\",children:/*#__PURE__*/_jsxs(Row,{gutter:[8,1],children:[/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u8F66\\u8F86\\u603B\\u6570\",value:stats.totalVehicles,valueStyle:{color:'#3f8600',display:'flex',justifyContent:'center'}// Style={{}}\n})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u5728\\u7EBF\\u8F66\\u8F86\",value:stats.onlineVehicles// suffix={`/ ${stats.totalVehicles}`} \n,valueStyle:{color:'#3f8600',display:'flex',justifyContent:'center'}})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u79BB\\u7EBF\\u8F66\\u8F86\",value:stats.offlineVehicles// suffix={`/ ${stats.totalVehicles}`} \n,valueStyle:{color:'#cf1322',display:'flex',justifyContent:'center'}})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u8BBE\\u5907\\u603B\\u6570\",value:stats.totalDevices,valueStyle:{color:'#3f8600',display:'flex',justifyContent:'center'}})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u5728\\u7EBF\\u8BBE\\u5907\",value:stats.onlineDevices// suffix={`/ ${stats.totalDevices}`} \n,valueStyle:{color:'#3f8600',display:'flex',justifyContent:'center'}})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u79BB\\u7EBF\\u8BBE\\u5907\",value:stats.offlineDevices// suffix={`/ ${stats.totalDevices}`} \n,valueStyle:{color:'#cf1322',display:'flex',justifyContent:'center'}})})]})}),/*#__PURE__*/_jsx(InfoCard,{title:\"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u5217\\u8868\",bordered:false,height:\"calc(50% - 95px)\",children:renderEventList()}),/*#__PURE__*/_jsx(InfoCard,{title:\"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u7EDF\\u8BA1\",bordered:false,height:\"calc(50% - 95px)\",children:/*#__PURE__*/_jsx(\"div\",{ref:eventChartRef,style:{height:'100%',width:'100%'}})})]}),/*#__PURE__*/_jsx(MainContent,{leftCollapsed:leftCollapsed,rightCollapsed:rightCollapsed}),/*#__PURE__*/_jsxs(CollapsibleSidebar,{position:\"right\",collapsed:rightCollapsed,onCollapse:()=>setRightCollapsed(!rightCollapsed),children:[/*#__PURE__*/_jsx(InfoCard,{title:\"\\u8F66\\u8F86\\u5217\\u8868\",bordered:false,height:\"49%\",children:/*#__PURE__*/_jsx(Table,{dataSource:vehicles,columns:vehicleColumns,rowKey:\"id\",pagination:false,size:\"small\",scroll:{y:180},onRow:record=>({onClick:()=>handleVehicleSelect(record),style:{cursor:'pointer',background:(selectedVehicle===null||selectedVehicle===void 0?void 0:selectedVehicle.id)===record.id?'#e6f7ff':'transparent',fontSize:'13px',padding:'4px 8px'}})})}),/*#__PURE__*/_jsx(InfoCard,{title:\"\\u8F66\\u8F86\\u8BE6\\u7EC6\\u4FE1\\u606F\",bordered:false,height:\"49%\",children:selectedVehicle?/*#__PURE__*/_jsxs(Descriptions,{bordered:true,column:1,size:\"small\",styles:{label:{fontSize:'13px',padding:'4px 8px'},content:{fontSize:'13px',padding:'4px 8px'}},children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8F66\\u724C\\u53F7\",children:selectedVehicle.plateNumber}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u72B6\\u6001\",children:/*#__PURE__*/_jsx(Badge,{status:selectedVehicle.status==='online'?'success':'error',text:selectedVehicle.status==='online'?'在线':'离线'})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7ECF\\u5EA6\",children:selectedVehicle.status==='online'?selectedVehicle.lng.toFixed(7):'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7EAC\\u5EA6\",children:selectedVehicle.status==='online'?selectedVehicle.lat.toFixed(7):'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u901F\\u5EA6\",children:selectedVehicle.status==='online'?`${typeof selectedVehicle.speed==='number'?selectedVehicle.speed.toFixed(0):selectedVehicle.speed} km/h`:'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u822A\\u5411\\u89D2\",children:selectedVehicle.status==='online'?`${typeof selectedVehicle.heading==='number'?selectedVehicle.heading.toFixed(2):selectedVehicle.heading}°`:'N/A'})]}):/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'13px'},children:\"\\u8BF7\\u9009\\u62E9\\u8F66\\u8F86\\u67E5\\u770B\\u8BE6\\u7EC6\\u4FE1\\u606F\"})})]})]})});};export default RealTimeTraffic;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "Row", "Col", "Card", "Statistic", "List", "Table", "Descriptions", "Spin", "Badge", "echarts", "<PERSON><PERSON><PERSON>", "GridComponent", "TooltipComponent", "LegendComponent", "TitleComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "CollapsibleSidebar", "axios", "vehiclesData", "jsx", "_jsx", "jsxs", "_jsxs", "use", "StyledCanvas", "div", "<PERSON><PERSON><PERSON><PERSON>", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "InfoCard", "height", "CompactStatistic", "RealTimeTraffic", "loading", "setLoading", "vehicles", "setVehicles", "events", "setEvents", "savedEvents", "localStorage", "getItem", "JSON", "parse", "error", "console", "selectedVehicle", "setSelectedVehicle", "stats", "setStats", "totalVehicles", "onlineVehicles", "offlineVehicles", "totalDevices", "onlineDevices", "offlineDevices", "onlineBsmIds", "setOnlineBsmIds", "savedOnlineIds", "Set", "lastBsmTime", "eventChartRef", "setLeftCollapsed", "setRightCollapsed", "eventStats", "setEventStats", "savedStats", "prevRsiEvents", "Map", "updateVehicleStatus", "bsmId", "status", "speed", "arguments", "length", "undefined", "lat", "lng", "heading", "formattedSpeed", "parseFloat", "toFixed", "formattedHeading", "log", "prev", "current", "Date", "now", "newSet", "delete", "prevVehicles", "map", "vehicle", "window", "handleRealBsmReceived", "event", "data", "type", "addEventListener", "removeEventListener", "saveTimer", "setTimeout", "setItem", "stringify", "clearTimeout", "fetchVehicles", "apiData", "fetchLatestVehiclesData", "bsmIds", "v", "filter", "id", "updatedVehicles", "isOnline", "has", "plate", "plateNumber", "onlineCount", "totalCount", "prevStats", "vehiclesList", "fetchDeviceStats", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "success", "devicesData", "d", "handleBsmMessage", "bsmData", "partSpeed", "partLat", "partLong", "partHeading", "foundVehicle", "find", "checkOnlineStatus", "newOnlineBsmIds", "has<PERSON><PERSON><PERSON>", "for<PERSON>ach", "lastTime", "interval", "setInterval", "clearInterval", "resetAllVehicles", "size", "loadData", "handleVehiclesDataChanged", "handleStorageChange", "key", "lastUpdated", "forcedPollingInterval", "returnData", "possibleApiUrls", "succeeded", "processVehiclesData", "message", "fetch", "ok", "json", "e", "newVehicles", "currentIds", "hasNewVehicle", "some", "chart", "handleResize", "lastUpdateTime", "existingChart", "getInstanceByDom", "dispose", "init", "updateChart", "currentTime", "eventTypes", "name", "color", "value", "itemStyle", "item", "sort", "a", "b", "option", "title", "text", "toLocaleTimeString", "left", "top", "textStyle", "fontSize", "grid", "bottom", "right", "containLabel", "animation", "animationDuration", "animationDurationUpdate", "animationEasingUpdate", "tooltip", "trigger", "axisPointer", "xAxis", "show", "splitLine", "yAxis", "axisLabel", "margin", "axisTick", "axisLine", "series", "<PERSON><PERSON><PERSON><PERSON>", "label", "position", "formatter", "borderRadius", "realtimeSort", "animationDelay", "idx", "setOption", "notMerge", "replaceMerge", "statsInterval", "_chart", "resize", "handleRsiMessage", "rsiData", "rtes", "latitude", "posLat", "longitude", "posLong", "rsuId", "mac", "timestamp", "tm", "nonDuplicateEventTypes", "eventType", "latFixed", "lngFixed", "Math", "floor", "pow", "eventKey", "isDuplicate", "checkDuplicateEvent", "eventTypeText", "eventColor", "description", "newEvent", "random", "time", "location", "newEvents", "slice", "push", "newStats", "currentPos", "prevEvents", "prevEvent", "timeDiff", "distance", "calculateDistance", "pos", "set", "lat1", "lon1", "lat2", "lon2", "R", "dLat", "PI", "dLon", "sin", "cos", "c", "atan2", "sqrt", "handleVehicleSelect", "updatedSelectedVehicle", "vehicleColumns", "dataIndex", "width", "render", "renderEventList", "dataSource", "renderItem", "<PERSON><PERSON>", "style", "padding", "children", "Meta", "marginBottom", "marginRight", "maxHeight", "overflowY", "apiPollingInterval", "spinning", "tip", "collapsed", "onCollapse", "bordered", "gutter", "span", "display", "justifyContent", "valueStyle", "ref", "columns", "<PERSON><PERSON><PERSON>", "pagination", "scroll", "y", "onRow", "record", "onClick", "cursor", "background", "column", "styles", "content"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RealTimeTraffic.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { Bar<PERSON>hart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\nimport vehiclesData from '../data/vehicles.json';\n\n// 注册必要的echarts组件\necharts.use([BarChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\n\nconst StyledCanvas = styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 65px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 24px' : \n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 0' : \n    props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n  \n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n\nconst RealTimeTraffic = () => {\n  const [loading, setLoading] = useState(true);\n  const [vehicles, setVehicles] = useState([]);\n  const [events, setEvents] = useState(() => {\n    try {\n      const savedEvents = localStorage.getItem('realTimeTrafficEvents');\n      return savedEvents ? JSON.parse(savedEvents) : [];\n    } catch (error) {\n      console.error('读取事件列表失败:', error);\n      return [];\n    }\n  });\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\n  const [stats, setStats] = useState({\n    totalVehicles: 0,\n    onlineVehicles: 0,\n    offlineVehicles: 0,\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0\n  });\n  \n  // 存储在线车辆的 BSM ID，初始时为空集合，表示所有车辆离线\n  const [onlineBsmIds, setOnlineBsmIds] = useState(() => {\n    try {\n      const savedOnlineIds = localStorage.getItem('realTimeTrafficOnlineIds');\n      // 返回空集合，确保所有车辆初始状态为离线\n      return new Set();\n    } catch (error) {\n      console.error('读取在线ID缓存失败:', error);\n      return new Set();\n    }\n  });\n  // 存储最后一次收到 BSM 消息的时间\n  const lastBsmTime = useRef({});\n  \n  const eventChartRef = useRef(null);\n  \n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n  \n  const [eventStats, setEventStats] = useState(() => {\n    try {\n      const savedStats = localStorage.getItem('realTimeTrafficEventStats');\n      return savedStats ? JSON.parse(savedStats) : {\n        '401': 0,  // 道路抛洒物\n        '404': 0,  // 道路障碍物\n        '405': 0,  // 行人通过马路\n        '904': 0,  // 逆行车辆\n        '910': 0,  // 违停车辆\n        '1002': 0, // 道路施工\n        '901': 0   // 车辆超速\n      };\n    } catch (error) {\n      console.error('读取事件统计数据失败:', error);\n      return {\n        '401': 0, '404': 0, '405': 0, '904': 0,\n        '910': 0, '1002': 0, '901': 0\n      };\n    }\n  });\n\n  // 添加RSI事件缓存，用于检测重复事件\n  const prevRsiEvents = useRef(new Map());\n\n  // 添加手动更新车辆状态和位置信息的函数\n  const updateVehicleStatus = useCallback((bsmId, status, speed = 0, lat = 0, lng = 0, heading = 0) => {\n    // 确保速度和航向角是格式化的数值，保留两位小数\n    const formattedSpeed = parseFloat(speed).toFixed(0);\n    const formattedHeading = parseFloat(heading).toFixed(2);\n    \n    console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${formattedSpeed}, 位置=(${lat}, ${lng})`);\n    \n    // 更新在线状态\n    if (status === 'online') {\n      setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n      lastBsmTime.current[bsmId] = Date.now();\n    } else {\n      setOnlineBsmIds(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(bsmId);\n        return newSet;\n      });\n    }\n    \n    // 更新车辆信息\n    setVehicles(prevVehicles => \n      prevVehicles.map(vehicle => \n        vehicle.bsmId === bsmId \n          ? { \n              ...vehicle, \n              status: status,\n              speed: parseFloat(formattedSpeed), // 确保是数值类型\n              lat: parseFloat(lat.toFixed(7)),\n              lng: parseFloat(lng.toFixed(7)),\n              heading: parseFloat(formattedHeading) // 确保是数值类型\n            } \n          : vehicle\n      )\n    );\n  }, []);\n  \n  // 在组件挂载时，暴露updateVehicleStatus函数到window对象\n  useEffect(() => {\n    window.updateVehicleStatus = updateVehicleStatus;\n    \n    // 用于监听CampusModel是否接收到BSM消息的事件\n    const handleRealBsmReceived = (event) => {\n      if (event.data && event.data.type === 'realBsmReceived') {\n        // console.log('收到CampusModel发送的真实BSM消息通知');\n      }\n    };\n\n    window.addEventListener('message', handleRealBsmReceived);\n    \n    // 确保页面刷新时清空在线车辆状态\n    console.log('组件初始化，重置所有车辆为离线状态');\n    setOnlineBsmIds(new Set());\n    lastBsmTime.current = {};\n    \n    return () => {\n      window.removeEventListener('message', handleRealBsmReceived);\n      delete window.updateVehicleStatus;\n    };\n  }, [updateVehicleStatus]);\n\n  // 使用防抖保存事件列表到 localStorage\n  useEffect(() => {\n    const saveTimer = setTimeout(() => {\n      try {\n        localStorage.setItem('realTimeTrafficEvents', JSON.stringify(events));\n      } catch (error) {\n        console.error('保存事件列表失败:', error);\n      }\n    }, 1000); // 延迟1秒保存，避免频繁写入\n    \n    return () => clearTimeout(saveTimer);\n  }, [events]);\n\n  // 使用防抖保存事件统计数据到 localStorage\n  useEffect(() => {\n    const saveTimer = setTimeout(() => {\n      try {\n        localStorage.setItem('realTimeTrafficEventStats', JSON.stringify(eventStats));\n      } catch (error) {\n        console.error('保存事件统计数据失败:', error);\n      }\n    }, 1000); // 延迟1秒保存，避免频繁写入\n    \n    return () => clearTimeout(saveTimer);\n  }, [eventStats]);\n\n  // 获取车辆数据\n  const fetchVehicles = useCallback(async () => {\n    try {\n      // 先尝试从API获取最新数据\n      console.log('尝试从API获取最新车辆数据');\n      const apiData = await fetchLatestVehiclesData(true);\n      \n      // 如果API获取成功，直接使用API数据\n      if (apiData && apiData.length > 0) {\n        console.log('成功从API获取车辆数据，车辆数量:', apiData.length);\n        \n        // 获取所有车辆的BSM ID列表\n        const bsmIds = apiData.map(v => v.bsmId).filter(id => id);\n        console.log('所有车辆的BSM ID:', bsmIds);\n        \n        // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\n        const updatedVehicles = apiData.map(vehicle => {\n          // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\n          const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);\n          \n          return {\n            ...vehicle,\n            plate: vehicle.plateNumber, // 适配表格显示\n            status: isOnline ? 'online' : 'offline', // 只有收到BSM消息的车辆才显示为在线\n            speed: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0) : 0,\n            lat: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0) : 0,\n            lng: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0) : 0,\n            heading: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0) : 0\n          };\n        });\n        \n        setVehicles(updatedVehicles);\n        \n        // 直接检查更新后的车辆在线状态\n        const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n        const totalCount = updatedVehicles.length;\n        \n        console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);\n        \n        // 更新车辆统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalVehicles: totalCount,\n          onlineVehicles: onlineCount,\n          offlineVehicles: totalCount - onlineCount\n        }));\n        \n        return;\n      }\n      \n      // 如果API获取失败，回退到本地JSON文件\n      console.log('API获取失败，回退到本地vehiclesData');\n      const vehiclesList = vehiclesData.vehicles || [];\n      console.log('从vehiclesData获取车辆数据，车辆数量:', vehiclesList.length);\n      \n      // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\n      const updatedVehicles = vehiclesList.map(vehicle => {\n        // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\n        const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);\n        \n        return {\n          ...vehicle,\n          plate: vehicle.plateNumber, // 适配表格显示\n          status: isOnline ? 'online' : 'offline', // 只有收到BSM消息的车辆才显示为在线\n          speed: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0) : 0,\n          lat: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0) : 0,\n          lng: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0) : 0,\n          heading: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0) : 0\n        };\n      });\n      \n      setVehicles(updatedVehicles);\n      \n      // 直接检查更新后的车辆在线状态\n      const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n      const totalCount = updatedVehicles.length;\n      \n      console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);\n      \n      // 更新车辆统计数据\n      setStats(prevStats => ({\n        ...prevStats,\n        totalVehicles: totalCount,\n        onlineVehicles: onlineCount,\n        offlineVehicles: totalCount - onlineCount\n      }));\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n    }\n  }, [onlineBsmIds]);\n\n  // 获取设备统计数据\n  const fetchDeviceStats = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      \n      if (response.data && response.data.success) {\n        const devicesData = response.data.data;\n        \n        // 更新设备统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalDevices: devicesData.length,\n          onlineDevices: devicesData.filter(d => d.status === 'online').length,\n          offlineDevices: devicesData.filter(d => d.status === 'offline').length\n        }));\n      }\n    } catch (error) {\n      console.error('获取设备统计数据失败:', error);\n    }\n  };\n\n  // 监听 BSM 消息\n  useEffect(() => {\n    const handleBsmMessage = (event) => {\n      if (event.data && event.data.type === 'bsm') {\n        // 获取bsmId，确保它正确地从消息中提取\n        const bsmData = event.data.data || {};\n        const bsmId = bsmData.bsmId || event.data.bsmId;\n        \n        if (!bsmId) {\n          console.error('BSM消息缺少bsmId:', event.data);\n          return;\n        }\n        \n        // console.log('收到BSM消息，ID:', bsmId);\n        const now = Date.now();\n        \n        // 更新最后接收时间\n        lastBsmTime.current[bsmId] = now;\n        \n        // 添加到在线bsmId集合\n        setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n        \n        // 提取正确的BSM数据并格式化为两位小数\n        const speed = parseFloat((parseFloat(bsmData.partSpeed || event.data.speed || 0) * 3.6).toFixed(0)); // 转换为km/h，保留两位小数\n        const lat = parseFloat(parseFloat(bsmData.partLat || event.data.lat || 0).toFixed(7));\n        const lng = parseFloat(parseFloat(bsmData.partLong || event.data.lng || 0).toFixed(7));\n        const heading = parseFloat(parseFloat(bsmData.partHeading || event.data.heading || 0).toFixed(2)); // 保留两位小数\n        \n        // console.log(`车辆${bsmId}状态: 速度=${speed.toFixed(2)}km/h, 位置=(${lat.toFixed(7)}, ${lng.toFixed(7)}), 航向=${heading.toFixed(2)}°`);\n        \n        // 更新车辆状态和位置信息\n        setVehicles(prevVehicles => {\n          // 检查是否找到对应车辆\n          const foundVehicle = prevVehicles.find(v => v.bsmId === bsmId);\n          if (!foundVehicle) {\n            console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);\n            return prevVehicles;\n          }\n          \n          const updatedVehicles = prevVehicles.map(vehicle => \n            vehicle.bsmId === bsmId \n              ? { \n                  ...vehicle, \n                  status: 'online',\n                  speed: speed,\n                  lat: lat,\n                  lng: lng,\n                  heading: heading\n                } \n              : vehicle\n          );\n          \n          // 计算更新后的在线车辆数量\n          const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n          const totalCount = updatedVehicles.length;\n          \n          // 更新统计数据\n          setStats(prevStats => ({\n            ...prevStats,\n            onlineVehicles: onlineCount,\n            offlineVehicles: totalCount - onlineCount\n          }));\n          \n          return updatedVehicles;\n        });\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleBsmMessage);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleBsmMessage);\n    };\n  }, []);\n\n  // 修改检查在线状态的useEffect，同步更新统计信息\n  useEffect(() => {\n    const checkOnlineStatus = () => {\n      const now = Date.now();\n      console.log('检查车辆在线状态...');\n      \n      // 只有在特定条件下才将车辆设为离线\n      // 例如，只有收到过BSM消息的车辆才会被检查是否超时\n      setOnlineBsmIds(prev => {\n        const newOnlineBsmIds = new Set(prev);\n        let hasChanges = false;\n        \n        // 仅检查已有最后更新时间的车辆\n        prev.forEach(bsmId => {\n          const lastTime = lastBsmTime.current[bsmId];\n          \n          // 只有收到过BSM消息的车辆才会被检查是否超时\n          if (lastTime && (now - lastTime > 30000)) {\n            console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);\n            newOnlineBsmIds.delete(bsmId);\n            hasChanges = true;\n          }\n        });\n        \n        if (hasChanges) {\n          // 更新车辆状态\n          setVehicles(prevVehicles => {\n            const updatedVehicles = prevVehicles.map(vehicle => {\n              // 只更新有最后更新时间的车辆状态\n              if (lastBsmTime.current[vehicle.bsmId]) {\n                const isOnline = newOnlineBsmIds.has(vehicle.bsmId);\n                return {\n                  ...vehicle,\n                  status: isOnline ? 'online' : 'offline'\n                };\n              }\n              return vehicle;\n            });\n            \n            // 计算更新后的在线车辆数量\n            const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n            const totalCount = updatedVehicles.length;\n            \n            // 更新统计数据\n            setStats(prevStats => ({\n              ...prevStats,\n              onlineVehicles: onlineCount,\n              offlineVehicles: totalCount - onlineCount\n            }));\n            \n            return updatedVehicles;\n          });\n        }\n        \n        return newOnlineBsmIds;\n      });\n    };\n\n    const interval = setInterval(checkOnlineStatus, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // 重置所有车辆的初始状态\n  useEffect(() => {\n    // 将所有车辆状态重置为离线\n    const resetAllVehicles = () => {\n      // 只有在没有任何BSM消息的情况下才执行重置\n      if (onlineBsmIds.size === 0) {\n        setVehicles(prevVehicles => \n          prevVehicles.map(vehicle => ({\n            ...vehicle, \n            status: 'offline',\n            speed: 0,\n            lat: 0,\n            lng: 0,\n            heading: 0\n          }))\n        );\n        \n        console.log('已重置所有车辆为离线状态');\n      }\n    };\n    \n    // 初始执行一次\n    resetAllVehicles();\n    \n    // 然后每30秒检查一次\n    const interval = setInterval(resetAllVehicles, 30000);\n    \n    return () => clearInterval(interval);\n  }, [onlineBsmIds]);\n\n  // 在组件挂载时获取数据\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      fetchVehicles();\n      await fetchDeviceStats();\n      setLoading(false);\n    };\n    \n    loadData();\n    // // 降低更新频率，避免频繁覆盖状态\n    // const interval = setInterval(loadData, 300000); // 每60x5秒更新一次\n    // return () => clearInterval(interval);\n  }, []);\n\n  // 添加对车辆数据变更的监听\n  useEffect(() => {\n    console.log('设置车辆数据变更监听');\n    \n    // 监听自定义事件\n    const handleVehiclesDataChanged = () => {\n      console.log('检测到车辆数据变更事件，重新获取车辆列表');\n      fetchVehicles();\n    };\n    \n    // 监听localStorage变化\n    const handleStorageChange = (event) => {\n      if (event.key === 'vehiclesLastUpdated' || event.key === 'vehiclesData') {\n        console.log('检测到localStorage变化，重新获取车辆列表');\n        fetchVehicles();\n      }\n    };\n    \n    // 添加事件监听器\n    window.addEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n    window.addEventListener('storage', handleStorageChange);\n    \n    // 初始检查是否有更新\n    const lastUpdated = localStorage.getItem('vehiclesLastUpdated');\n    if (lastUpdated) {\n      console.log('初始检查到vehiclesLastUpdated:', lastUpdated);\n      fetchVehicles();\n    }\n    \n    // 设置更频繁的强制轮询，确保在部署环境中也能获取最新数据\n    const forcedPollingInterval = setInterval(() => {\n      console.log('强制轮询：重新获取车辆列表');\n      fetchVehicles();\n    }, 10000); // 每10秒强制刷新一次\n    \n    // 清理函数\n    return () => {\n      window.removeEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n      window.removeEventListener('storage', handleStorageChange);\n      clearInterval(forcedPollingInterval);\n    };\n  }, []);\n\n  // 添加检查本地缓存与API数据是否一致的机制\n  // 定义一个单独的API调用函数，用于获取最新的车辆列表数据\n  const fetchLatestVehiclesData = async (returnData = false) => {\n    try {\n      // 尝试多个可能的API地址\n      const possibleApiUrls = [\n        process.env.REACT_APP_API_URL || 'http://localhost:5000'\n        // 'http://localhost:5000',\n        // window.location.origin, // 当前站点的根URL\n        // `${window.location.origin}/api`, // 当前站点下的/api路径\n        // 'http://localhost:5000/api',\n        // 'http://127.0.0.1:5000',\n        // 'http://127.0.0.1:5000/api'\n      ];\n      \n      console.log('尝试从多个API地址获取车辆数据');\n      \n      // // 尝试从本地JSON文件直接获取\n      // try {\n      //   console.log('尝试从本地JSON文件获取数据');\n      //   const jsonResponse = await axios.get('/vehicles.json');\n      //   if (jsonResponse.data && jsonResponse.data.vehicles) {\n      //     console.log('成功从本地JSON文件获取数据:', jsonResponse.data.vehicles.length);\n      //     if (returnData) {\n      //       return jsonResponse.data.vehicles;\n      //     }\n      //     processVehiclesData(jsonResponse.data.vehicles);\n      //     return jsonResponse.data.vehicles;\n      //   }\n      // } catch (jsonError) {\n      //   console.log('从本地JSON获取失败:', jsonError.message);\n      // }\n      \n      // 逐个尝试API地址\n      let succeeded = false;\n      let vehiclesData = null;\n      \n      for (const apiUrl of possibleApiUrls) {\n        if (succeeded) break;\n        \n        try {\n          console.log(`尝试从 ${apiUrl}/api/vehicles/list 获取数据`);\n          const response = await axios.get(`${apiUrl}/api/vehicles/list`);\n          \n          if (response.data && response.data.vehicles) {\n            console.log(`成功从 ${apiUrl} 获取数据:`, response.data.vehicles.length);\n            vehiclesData = response.data.vehicles;\n            \n            if (returnData) {\n              return vehiclesData;\n            }\n            \n            processVehiclesData(response.data.vehicles);\n            succeeded = true;\n            break;\n          }\n        } catch (error) {\n          console.log(`从 ${apiUrl} 获取失败:`, error.message);\n          // 继续尝试下一个URL\n        }\n      }\n      \n      if (!succeeded && !returnData) {\n        console.log('所有API地址都获取失败，尝试使用vehicles.json');\n        // 尝试从当前页面获取\n        try {\n          const response = await fetch('/vehicles.json');\n          if (response.ok) {\n            const data = await response.json();\n            if (data && data.vehicles) {\n              console.log('从public/vehicles.json获取数据成功:', data.vehicles.length);\n              processVehiclesData(data.vehicles);\n              return data.vehicles;\n            }\n          }\n        } catch (e) {\n          console.error('从public/vehicles.json获取失败:', e);\n        }\n      }\n      \n      return vehiclesData || [];\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n      return [];\n    }\n  };\n\n  // 添加处理车辆数据的辅助函数\n  const processVehiclesData = (newVehicles) => {\n    // 与当前列表比较\n    if (vehicles.length !== newVehicles.length) {\n      console.log('检测到车辆数量变化，从', vehicles.length, '到', newVehicles.length);\n      fetchVehicles(); // 重新加载\n      return;\n    }\n    \n    // 检查是否有新车辆ID\n    const currentIds = new Set(vehicles.map(v => v.id));\n    const hasNewVehicle = newVehicles.some(v => !currentIds.has(v.id));\n    \n    if (hasNewVehicle) {\n      console.log('检测到新增车辆');\n      fetchVehicles(); // 重新加载\n    }\n  };\n\n  // 添加自动选择第一个车辆的逻辑\n  useEffect(() => {\n    // 当车辆列表加载完成且有车辆数据时\n    if (vehicles.length > 0 && !selectedVehicle) {\n      // 自动选择第一个车辆\n      setSelectedVehicle(vehicles[0]);\n      console.log('已自动选择第一个车辆:', vehicles[0].plateNumber);\n    }\n  }, [vehicles, selectedVehicle]);\n  \n  // 修改图表初始化代码\n  useEffect(() => {\n    let chart = null;\n    let handleResize = null;\n    let lastUpdateTime = new Date();\n\n    // 确保 DOM 元素存在\n    if (!eventChartRef.current) {\n      console.error('图表容器未找到');\n      return;\n    }\n\n    // 检查并清理已存在的图表实例\n    let existingChart = echarts.getInstanceByDom(eventChartRef.current);\n    if (existingChart) {\n      existingChart.dispose();\n    }\n\n    try {\n      // 初始化新的图表实例\n      chart = echarts.init(eventChartRef.current);\n      \n      // 设置图表配置\n      const updateChart = () => {\n        const currentTime = new Date();\n        lastUpdateTime = currentTime;\n\n        // 事件类型配置\n        const eventTypes = [\n          { type: '401', name: '道路抛洒物', color: '#ff4d4f' },\n          { type: '404', name: '道路障碍物', color: '#faad14' },\n          { type: '405', name: '行人通过马路', color: '#1890ff' },\n          { type: '904', name: '逆行车辆', color: '#f5222d' },\n          { type: '910', name: '违停车辆', color: '#722ed1' },\n          { type: '1002', name: '道路施工', color: '#fa8c16' },\n          { type: '901', name: '车辆超速', color: '#eb2f96' }\n        ];\n\n        // 处理数据\n        const data = eventTypes\n          .map(event => ({\n            value: eventStats[event.type] || 0,\n            name: event.name,\n            itemStyle: { color: event.color }\n          }))\n          .filter(item => item.value > 0)\n          .sort((a, b) => b.value - a.value);\n\n        const option = {\n          title: {\n            text: `最后更新: ${currentTime.toLocaleTimeString()}`,\n            left: 'center',\n            top: -5,\n            textStyle: {\n              fontSize: 12,\n              color: '#999'\n            }\n          },\n          grid: {\n            top: 30,\n            bottom: 0,\n            left: 0,\n            right: 50,\n            containLabel: true\n          },\n          animation: true,\n          animationDuration: 0,\n          animationDurationUpdate: 1000,\n          animationEasingUpdate: 'quinticInOut',\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'shadow'\n            }\n          },\n          xAxis: {\n            type: 'value',\n            show: false,\n            splitLine: { show: false }\n          },\n          yAxis: {\n            type: 'category',\n            data: data.map(item => item.name),\n            axisLabel: {\n              fontSize: 12,\n              color: '#666',\n              margin: 8\n            },\n            axisTick: { show: false },\n            axisLine: { show: false }\n          },\n          series: [{\n            type: 'bar',\n            data: data,\n            barWidth: '50%',\n            label: {\n              show: true,\n              position: 'right',\n              formatter: '{c}次',\n              fontSize: 12,\n              color: '#666'\n            },\n            itemStyle: {\n              borderRadius: [0, 4, 4, 0]\n            },\n            realtimeSort: false,\n            animationDelay: function (idx) {\n              return idx * 100;\n            }\n          }]\n        };\n\n        // 使用 notMerge: false 来保持增量更新\n        chart.setOption(option, {\n          notMerge: false,\n          replaceMerge: ['series']\n        });\n      };\n\n      // 初始更新\n      updateChart();\n      \n      // 监听事件统计变化，每分钟更新一次\n      const statsInterval = setInterval(updateChart, 60000); // 60000ms = 1分钟\n      \n      // 监听窗口大小变化\n      handleResize = () => {\n        chart?.resize();\n      };\n      window.addEventListener('resize', handleResize);\n\n      // 清理函数\n      return () => {\n        clearInterval(statsInterval);\n        if (handleResize) {\n          window.removeEventListener('resize', handleResize);\n        }\n        if (chart) {\n          chart.dispose();\n        }\n      };\n\n    } catch (error) {\n      console.error('初始化图表失败:', error);\n    }\n  }, [eventStats]);\n  \n  // 修改 RSI 消息处理逻辑\n  useEffect(() => {\n    const handleRsiMessage = (event) => {\n      try {\n        if (event.data && event.data.type === 'RSI') {\n          const rsiData = event.data.data;\n          if (!rsiData || !rsiData.rtes) return;\n          if(rsiData.rtes.length > 0){\n            console.log('RealTimeTraffic 收到 RSI 消息:', event.data);\n          }\n\n          const latitude = parseFloat(rsiData.posLat);\n          const longitude = parseFloat(rsiData.posLong);\n          const rsuId = rsiData.rsuId;\n          const mac = event.data.mac || '';\n          const timestamp = event.data.tm || Date.now();\n\n          // 统计本帧所有非重复事件类型\n          const nonDuplicateEventTypes = [];\n          rsiData.rtes.forEach(event => {\n            const eventType = event.eventType;\n            // const eventKey = `${rsuId}_${mac}_${eventType}`;\n            let latFixed = '';\n            let lngFixed = '';\n            if(eventType== '904' || eventType== '901'){\n              // latFixed = latitude.toFixed(3);\n              // lngFixed = longitude.toFixed(3);\n              latFixed = Math.floor(latitude * Math.pow(10,3))/Math.pow(10,3);\n              lngFixed = Math.floor(longitude* Math.pow(10,3))/Math.pow(10,3);  \n            }\n            else{\n              // latFixed = latitude.toFixed(4);\n              // lngFixed = longitude.toFixed(4);\n              latFixed = Math.floor(latitude * Math.pow(10,4))/Math.pow(10,4);\n              lngFixed = Math.floor(longitude* Math.pow(10,4))/Math.pow(10,4);             \n            }\n            const eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;\n            const isDuplicate = checkDuplicateEvent(\n              eventKey,\n              timestamp,\n              { lat: latitude, lng: longitude },\n              prevRsiEvents.current\n            );\n            // 获取事件类型的中文描述\n            let eventTypeText = '';\n            let eventColor = '';\n            switch(eventType) {\n              case '401': eventTypeText = '道路抛洒物'; eventColor = '#ff4d4f'; break;\n              case '404': eventTypeText = '道路障碍物'; eventColor = '#faad14'; break;\n              case '405': eventTypeText = '行人通过马路'; eventColor = '#1890ff'; break;\n              case '904': eventTypeText = '逆行车辆'; eventColor = '#f5222d'; break;\n              case '910': eventTypeText = '违停车辆'; eventColor = '#722ed1'; break;\n              case '1002': eventTypeText = '道路施工'; eventColor = '#fa8c16'; break;\n              case '901': eventTypeText = '车辆超速'; eventColor = '#eb2f96'; break;\n              default: eventTypeText = event.description || '未知事件'; eventColor = '#8c8c8c';\n            }\n            // 更新事件列表\n            // const newEvent = {\n            //   key: Date.now() + Math.random(),\n            //   type: eventTypeText,\n            //   time: new Date().toLocaleTimeString(),\n            //   vehicle: rsiData.rsuId || '未知设备',\n            //   color: eventColor,\n            //   eventType: eventType,\n            //   location: {\n            //     latitude: latitude,\n            //     longitude: longitude\n            //   }\n\n            // };\n            // setEvents(prev => {\n            //   const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n            //   return newEvents;\n            // });\n            if (!isDuplicate) {\n              // 更新事件列表\n              const newEvent = {\n                key: Date.now() + Math.random(),\n                type: eventTypeText,\n                time: new Date().toLocaleTimeString(),\n                vehicle: rsiData.rsuId || '未知设备',\n                color: eventColor,\n                eventType: eventType,\n                location: {\n                  latitude: latitude,\n                  longitude: longitude\n                }\n\n              };\n              setEvents(prev => {\n                const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n                return newEvents;\n              });\n              \n              nonDuplicateEventTypes.push(eventType);\n            } else {\n              console.log(`检测到重复事件，不累计统计: ${eventTypeText}`);\n            }\n          });\n\n          // 只累加一次\n          if (nonDuplicateEventTypes.length > 0) {\n            setEventStats(prev => {\n              const newStats = { ...prev };\n              nonDuplicateEventTypes.forEach(eventType => {\n                newStats[eventType] = (newStats[eventType] || 0) + 1;\n              });\n              return newStats;\n            });\n          }\n        }\n      } catch (error) {\n        console.error('处理 RSI 消息失败:', error);\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleRsiMessage);\n\n    return () => {\n      window.removeEventListener('message', handleRsiMessage);\n    };\n  }, []);\n  \n  // 在组件中添加检查重复事件的辅助函数\n  const checkDuplicateEvent = (eventKey, currentTime, currentPos, prevEvents) => {\n    // 检查是否有相同事件的历史记录\n    if (prevEvents.has(eventKey)) {\n      const prevEvent = prevEvents.get(eventKey);\n      const timeDiff = currentTime - prevEvent.time;\n      \n      // 如果时间差小于5000ms\n      if (timeDiff < 5000) {\n        // 计算两个位置之间的距离\n        const distance = calculateDistance(\n          currentPos.lat, currentPos.lng,\n          prevEvent.pos.lat, prevEvent.pos.lng\n        );\n        \n        // 如果距离小于5米，认为是同一个事件\n        if (distance < 5) {\n          // 更新时间戳，但保持为重复事件\n          prevEvents.set(eventKey, {\n            time: currentTime,\n            pos: currentPos\n          });\n          return true;\n        }\n      }\n    }\n    \n    // 保存当前事件信息用于后续比较\n    prevEvents.set(eventKey, {\n      time: currentTime,\n      pos: currentPos\n    });\n    \n    // 不是重复事件\n    return false;\n  };\n\n  // 添加计算两点之间距离的函数\n  const calculateDistance = (lat1, lon1, lat2, lon2) => {\n    // 如果任何坐标为0，表示坐标无效，返回大距离以避免被视为相同事件\n    if (lat1 === 0 || lon1 === 0 || lat2 === 0 || lon2 === 0) {\n      return 999;\n    }\n    \n    // 计算两点之间的距离（哈弗辛公式）\n    const R = 6371000; // 地球半径，单位：米\n    const dLat = (lat2 - lat1) * Math.PI / 180;\n    const dLon = (lon2 - lon1) * Math.PI / 180;\n    const a = \n      Math.sin(dLat/2) * Math.sin(dLat/2) +\n      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * \n      Math.sin(dLon/2) * Math.sin(dLon/2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n    const distance = R * c;\n    \n    return distance;\n  };\n  \n  // 处理车辆选择\n  const handleVehicleSelect = (vehicle) => {\n    console.log('选择车辆:', vehicle.plateNumber, '状态:', vehicle.status);\n    setSelectedVehicle(vehicle);\n  };\n  \n  // 修改车辆状态更新逻辑，确保同步更新selectedVehicle\n  useEffect(() => {\n    // 当车辆列表更新后，如果当前有选中的车辆，则更新选中的车辆信息\n    if (selectedVehicle) {\n      const updatedSelectedVehicle = vehicles.find(v => v.id === selectedVehicle.id);\n      if (updatedSelectedVehicle && \n          (updatedSelectedVehicle.status !== selectedVehicle.status || \n           updatedSelectedVehicle.speed !== selectedVehicle.speed ||\n           updatedSelectedVehicle.lat !== selectedVehicle.lat ||\n           updatedSelectedVehicle.lng !== selectedVehicle.lng ||\n           updatedSelectedVehicle.heading !== selectedVehicle.heading)) {\n        console.log(`更新选中车辆 ${selectedVehicle.plateNumber} 的信息:`, \n                   `状态从 ${selectedVehicle.status} 变为 ${updatedSelectedVehicle.status}`);\n        setSelectedVehicle(updatedSelectedVehicle);\n      }\n    }\n  }, [vehicles, selectedVehicle]);\n  \n  // 车辆列表列定义\n  const vehicleColumns = [\n    {\n      title: '车牌号',\n      dataIndex: 'plate',\n      key: 'plate',\n      width: '40%',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: '30%',\n      render: status => (\n        <Badge \n          status={status === 'online' ? 'success' : 'error'} \n          text={status === 'online' ? '在线' : '离线'} \n        />\n      ),\n    },\n    {\n      title: '速度',\n      dataIndex: 'speed',\n      key: 'speed',\n      width: '30%',\n      render: speed => `${typeof speed === 'number' ? speed.toFixed(0) : speed} km/h`,\n    }\n  ];\n  \n  // 修改实时事件列表的渲染\n  const renderEventList = () => (\n    <List\n      size=\"small\"\n      dataSource={events}\n      renderItem={item => (\n        <List.Item style={{ padding: '8px 0' }}>\n          <List.Item.Meta\n            title={\n              <div style={{ fontSize: '13px', marginBottom: '4px' }}>\n                <span style={{ color: item.color, marginRight: '8px' }}>\n                  {item.type}\n                </span>\n                <span style={{ color: '#666', fontSize: '12px' }}>\n                  {item.time}\n                </span>\n              </div>\n            }\n            description={\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                <div>设备: {item.vehicle}</div>\n                <div>位置: {item.location ? \n                  `${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}` : \n                  '未知位置'}\n                </div>\n              </div>\n            }\n          />\n        </List.Item>\n      )}\n      style={{\n        maxHeight: 'calc(100% - 24px)',\n        overflowY: 'auto'\n      }}\n    />\n  );\n  \n  // 添加强制定期从API获取最新数据的机制\n  useEffect(() => {\n    const apiPollingInterval = setInterval(() => {\n      console.log('直接从API检查车辆数据更新');\n      fetchLatestVehiclesData();\n    }, 15000); // 每15秒检查一次API\n    \n    return () => clearInterval(apiPollingInterval);\n  }, [vehicles]);\n\n  return (\n    <Spin spinning={loading} tip=\"加载中...\">\n      <PageContainer>\n        {/* 左侧信息栏 */}\n        <CollapsibleSidebar \n          position=\"left\"\n          collapsed={leftCollapsed}\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\n        >\n          {/* 车辆和设备总数信息栏 */}\n          <InfoCard title=\"车辆和设备统计\" bordered={false} height=\"160px\">\n            <Row gutter={[8, 1]} >\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic \n                  title=\"车辆总数\" \n                  value={stats.totalVehicles} \n                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }} \n                  // Style={{}}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic \n                  title=\"在线车辆\" \n                  value={stats.onlineVehicles} \n                  // suffix={`/ ${stats.totalVehicles}`} \n                  valueStyle={{ color: '#3f8600', display: 'flex',justifyContent: 'center'}} \n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic \n                  title=\"离线车辆\" \n                  value={stats.offlineVehicles} \n                  // suffix={`/ ${stats.totalVehicles}`} \n                  valueStyle={{ color: '#cf1322' ,display: 'flex',justifyContent: 'center' }} \n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic \n                  title=\"设备总数\" \n                  value={stats.totalDevices} \n                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }} \n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic \n                  title=\"在线设备\" \n                  value={stats.onlineDevices} \n                  // suffix={`/ ${stats.totalDevices}`} \n                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }} \n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic \n                  title=\"离线设备\" \n                  value={stats.offlineDevices} \n                  // suffix={`/ ${stats.totalDevices}`} \n                  valueStyle={{ color: '#cf1322',display: 'flex',justifyContent: 'center' }} \n                />\n              </Col>\n            </Row>\n          </InfoCard>\n          \n          {/* 实时事件列表栏 */}\n          <InfoCard title=\"实时事件列表\" bordered={false} height=\"calc(50% - 95px)\">\n            {renderEventList()}\n          </InfoCard>\n          \n          {/* 实时事件统计栏 */}\n          <InfoCard title=\"实时事件统计\" bordered={false} height=\"calc(50% - 95px)\">\n            <div ref={eventChartRef} style={{ height: '100%', width: '100%' }}></div>\n          </InfoCard>\n        </CollapsibleSidebar>\n        \n        {/* 主内容区域 */}\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\n          {/* 主要内容 */}\n        </MainContent>\n        \n        {/* 右侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"right\"\n          collapsed={rightCollapsed}\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\n        >\n          {/* 车辆列表栏 */}\n          <InfoCard title=\"车辆列表\" bordered={false} height=\"49%\">\n            <Table \n              dataSource={vehicles} \n              columns={vehicleColumns} \n              rowKey=\"id\"\n              pagination={false}\n              size=\"small\"\n              scroll={{ y: 180 }}\n              onRow={(record) => ({\n                onClick: () => handleVehicleSelect(record),\n                style: { \n                  cursor: 'pointer',\n                  background: selectedVehicle?.id === record.id ? '#e6f7ff' : 'transparent',\n                  fontSize: '13px',\n                  padding: '4px 8px'\n                }\n              })}\n            />\n          </InfoCard>\n          \n          {/* 车辆详细信息栏 */}\n          <InfoCard title=\"车辆详细信息\" bordered={false} height=\"49%\">\n            {selectedVehicle ? (\n              <Descriptions \n                bordered \n                column={1} \n                size=\"small\"\n                styles={{\n                  label: { fontSize: '13px', padding: '4px 8px' },\n                  content: { fontSize: '13px', padding: '4px 8px' }\n                }}\n              >\n                <Descriptions.Item label=\"车牌号\">{selectedVehicle.plateNumber}</Descriptions.Item>\n                <Descriptions.Item label=\"状态\">\n                  <Badge \n                    status={selectedVehicle.status === 'online' ? 'success' : 'error'} \n                    text={selectedVehicle.status === 'online' ? '在线' : '离线'} \n                  />\n                </Descriptions.Item>\n                <Descriptions.Item label=\"经度\">\n                  {selectedVehicle.status === 'online' ? selectedVehicle.lng.toFixed(7) : 'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"纬度\">\n                  {selectedVehicle.status === 'online' ? selectedVehicle.lat.toFixed(7) : 'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"速度\">\n                  {selectedVehicle.status === 'online' ? \n                    `${typeof selectedVehicle.speed === 'number' ? selectedVehicle.speed.toFixed(0) : selectedVehicle.speed} km/h` : \n                    'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"航向角\">\n                  {selectedVehicle.status === 'online' ? \n                    `${typeof selectedVehicle.heading === 'number' ? selectedVehicle.heading.toFixed(2) : selectedVehicle.heading}°` : \n                    'N/A'}\n                </Descriptions.Item>\n              </Descriptions>\n            ) : (\n              <p style={{ fontSize: '13px' }}>请选择车辆查看详细信息</p>\n            )}\n          </InfoCard>\n        </CollapsibleSidebar>\n      </PageContainer>\n    </Spin>\n  );\n};\n\nexport default RealTimeTraffic;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,OAASC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,SAAS,CAAEC,IAAI,CAAEC,KAAK,CAAEC,YAAY,CAAEC,IAAI,CAAEC,KAAK,KAAQ,MAAM,CACxF,MAAO,GAAK,CAAAC,OAAO,KAAM,cAAc,CACvC,OAASC,QAAQ,KAAQ,gBAAgB,CACzC,OAASC,aAAa,CAAEC,gBAAgB,CAAEC,eAAe,CAAEC,cAAc,KAAQ,oBAAoB,CACrG,OAASC,cAAc,KAAQ,mBAAmB,CAClD,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,kBAAkB,KAAM,yCAAyC,CACxE,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,YAAY,KAAM,uBAAuB,CAEhD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACAd,OAAO,CAACe,GAAG,CAAC,CAACd,QAAQ,CAAEC,aAAa,CAAEC,gBAAgB,CAAEC,eAAe,CAAEC,cAAc,CAAEC,cAAc,CAAC,CAAC,CAEzG,KAAM,CAAAU,YAAY,CAAGT,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAGX,MAAM,CAACU,GAAG;AAChC;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAE,WAAW,CAAGZ,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAG,YAAY,CAAGb,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAI,WAAW,CAAGd,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA,aAAaK,KAAK,EAAIA,KAAK,CAACC,aAAa,EAAID,KAAK,CAACE,cAAc,CAAG,QAAQ,CACxEF,KAAK,CAACC,aAAa,CAAG,cAAc,CACpCD,KAAK,CAACE,cAAc,CAAG,cAAc,CAAG,OAAO;AACnD;AACA,YAAYF,KAAK,EAAIA,KAAK,CAACC,aAAa,EAAID,KAAK,CAACE,cAAc,CAAG,QAAQ,CACvEF,KAAK,CAACC,aAAa,CAAG,WAAW,CACjCD,KAAK,CAACE,cAAc,CAAG,WAAW,CAAG,GAAG;AAC5C;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,QAAQ,CAAGlB,MAAM,CAACd,IAAI,CAAC;AAC7B;AACA,YAAY6B,KAAK,EAAIA,KAAK,CAACI,MAAM,EAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAGpB,MAAM,CAACb,SAAS,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAkC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG3C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC4C,QAAQ,CAAEC,WAAW,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC8C,MAAM,CAAEC,SAAS,CAAC,CAAG/C,QAAQ,CAAC,IAAM,CACzC,GAAI,CACF,KAAM,CAAAgD,WAAW,CAAGC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,CACjE,MAAO,CAAAF,WAAW,CAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAAG,EAAE,CACnD,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,MAAO,EAAE,CACX,CACF,CAAC,CAAC,CACF,KAAM,CAACE,eAAe,CAAEC,kBAAkB,CAAC,CAAGxD,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAACyD,KAAK,CAAEC,QAAQ,CAAC,CAAG1D,QAAQ,CAAC,CACjC2D,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAAC,CACjBC,eAAe,CAAE,CAAC,CAClBC,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAClB,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGlE,QAAQ,CAAC,IAAM,CACrD,GAAI,CACF,KAAM,CAAAmE,cAAc,CAAGlB,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC,CACvE;AACA,MAAO,IAAI,CAAAkB,GAAG,CAAC,CAAC,CAClB,CAAE,MAAOf,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnC,MAAO,IAAI,CAAAe,GAAG,CAAC,CAAC,CAClB,CACF,CAAC,CAAC,CACF;AACA,KAAM,CAAAC,WAAW,CAAGnE,MAAM,CAAC,CAAC,CAAC,CAAC,CAE9B,KAAM,CAAAoE,aAAa,CAAGpE,MAAM,CAAC,IAAI,CAAC,CAElC;AACA,KAAM,CAACkC,aAAa,CAAEmC,gBAAgB,CAAC,CAAGvE,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACqC,cAAc,CAAEmC,iBAAiB,CAAC,CAAGxE,QAAQ,CAAC,KAAK,CAAC,CAE3D,KAAM,CAACyE,UAAU,CAAEC,aAAa,CAAC,CAAG1E,QAAQ,CAAC,IAAM,CACjD,GAAI,CACF,KAAM,CAAA2E,UAAU,CAAG1B,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CACpE,MAAO,CAAAyB,UAAU,CAAGxB,IAAI,CAACC,KAAK,CAACuB,UAAU,CAAC,CAAG,CAC3C,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,MAAM,CAAE,CAAC,CAAE;AACX,KAAK,CAAE,CAAI;AACb,CAAC,CACH,CAAE,MAAOtB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnC,MAAO,CACL,KAAK,CAAE,CAAC,CAAE,KAAK,CAAE,CAAC,CAAE,KAAK,CAAE,CAAC,CAAE,KAAK,CAAE,CAAC,CACtC,KAAK,CAAE,CAAC,CAAE,MAAM,CAAE,CAAC,CAAE,KAAK,CAAE,CAC9B,CAAC,CACH,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAuB,aAAa,CAAG1E,MAAM,CAAC,GAAI,CAAA2E,GAAG,CAAC,CAAC,CAAC,CAEvC;AACA,KAAM,CAAAC,mBAAmB,CAAG3E,WAAW,CAAC,SAAC4E,KAAK,CAAEC,MAAM,CAA+C,IAA7C,CAAAC,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAG,GAAG,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAI,GAAG,CAAAJ,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAK,OAAO,CAAAL,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAC9F;AACA,KAAM,CAAAM,cAAc,CAAGC,UAAU,CAACR,KAAK,CAAC,CAACS,OAAO,CAAC,CAAC,CAAC,CACnD,KAAM,CAAAC,gBAAgB,CAAGF,UAAU,CAACF,OAAO,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAEvDpC,OAAO,CAACsC,GAAG,CAAC,gBAAgBb,KAAK,QAAQC,MAAM,QAAQQ,cAAc,SAASH,GAAG,KAAKC,GAAG,GAAG,CAAC,CAE7F;AACA,GAAIN,MAAM,GAAK,QAAQ,CAAE,CACvBd,eAAe,CAAC2B,IAAI,EAAI,GAAI,CAAAzB,GAAG,CAAC,CAAC,GAAGyB,IAAI,CAAEd,KAAK,CAAC,CAAC,CAAC,CAClDV,WAAW,CAACyB,OAAO,CAACf,KAAK,CAAC,CAAGgB,IAAI,CAACC,GAAG,CAAC,CAAC,CACzC,CAAC,IAAM,CACL9B,eAAe,CAAC2B,IAAI,EAAI,CACtB,KAAM,CAAAI,MAAM,CAAG,GAAI,CAAA7B,GAAG,CAACyB,IAAI,CAAC,CAC5BI,MAAM,CAACC,MAAM,CAACnB,KAAK,CAAC,CACpB,MAAO,CAAAkB,MAAM,CACf,CAAC,CAAC,CACJ,CAEA;AACApD,WAAW,CAACsD,YAAY,EACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,EACtBA,OAAO,CAACtB,KAAK,GAAKA,KAAK,CACnB,CACE,GAAGsB,OAAO,CACVrB,MAAM,CAAEA,MAAM,CACdC,KAAK,CAAEQ,UAAU,CAACD,cAAc,CAAC,CAAE;AACnCH,GAAG,CAAEI,UAAU,CAACJ,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC,CAC/BJ,GAAG,CAAEG,UAAU,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CAC/BH,OAAO,CAAEE,UAAU,CAACE,gBAAgB,CAAE;AACxC,CAAC,CACDU,OACN,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACApG,SAAS,CAAC,IAAM,CACdqG,MAAM,CAACxB,mBAAmB,CAAGA,mBAAmB,CAEhD;AACA,KAAM,CAAAyB,qBAAqB,CAAIC,KAAK,EAAK,CACvC,GAAIA,KAAK,CAACC,IAAI,EAAID,KAAK,CAACC,IAAI,CAACC,IAAI,GAAK,iBAAiB,CAAE,CACvD;AAAA,CAEJ,CAAC,CAEDJ,MAAM,CAACK,gBAAgB,CAAC,SAAS,CAAEJ,qBAAqB,CAAC,CAEzD;AACAjD,OAAO,CAACsC,GAAG,CAAC,mBAAmB,CAAC,CAChC1B,eAAe,CAAC,GAAI,CAAAE,GAAG,CAAC,CAAC,CAAC,CAC1BC,WAAW,CAACyB,OAAO,CAAG,CAAC,CAAC,CAExB,MAAO,IAAM,CACXQ,MAAM,CAACM,mBAAmB,CAAC,SAAS,CAAEL,qBAAqB,CAAC,CAC5D,MAAO,CAAAD,MAAM,CAACxB,mBAAmB,CACnC,CAAC,CACH,CAAC,CAAE,CAACA,mBAAmB,CAAC,CAAC,CAEzB;AACA7E,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4G,SAAS,CAAGC,UAAU,CAAC,IAAM,CACjC,GAAI,CACF7D,YAAY,CAAC8D,OAAO,CAAC,uBAAuB,CAAE5D,IAAI,CAAC6D,SAAS,CAAClE,MAAM,CAAC,CAAC,CACvE,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACnC,CACF,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAM4D,YAAY,CAACJ,SAAS,CAAC,CACtC,CAAC,CAAE,CAAC/D,MAAM,CAAC,CAAC,CAEZ;AACA7C,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4G,SAAS,CAAGC,UAAU,CAAC,IAAM,CACjC,GAAI,CACF7D,YAAY,CAAC8D,OAAO,CAAC,2BAA2B,CAAE5D,IAAI,CAAC6D,SAAS,CAACvC,UAAU,CAAC,CAAC,CAC/E,CAAE,MAAOpB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACrC,CACF,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAM4D,YAAY,CAACJ,SAAS,CAAC,CACtC,CAAC,CAAE,CAACpC,UAAU,CAAC,CAAC,CAEhB;AACA,KAAM,CAAAyC,aAAa,CAAG/G,WAAW,CAAC,SAAY,CAC5C,GAAI,CACF;AACAmD,OAAO,CAACsC,GAAG,CAAC,gBAAgB,CAAC,CAC7B,KAAM,CAAAuB,OAAO,CAAG,KAAM,CAAAC,uBAAuB,CAAC,IAAI,CAAC,CAEnD;AACA,GAAID,OAAO,EAAIA,OAAO,CAAChC,MAAM,CAAG,CAAC,CAAE,CACjC7B,OAAO,CAACsC,GAAG,CAAC,oBAAoB,CAAEuB,OAAO,CAAChC,MAAM,CAAC,CAEjD;AACA,KAAM,CAAAkC,MAAM,CAAGF,OAAO,CAACf,GAAG,CAACkB,CAAC,EAAIA,CAAC,CAACvC,KAAK,CAAC,CAACwC,MAAM,CAACC,EAAE,EAAIA,EAAE,CAAC,CACzDlE,OAAO,CAACsC,GAAG,CAAC,cAAc,CAAEyB,MAAM,CAAC,CAEnC;AACA,KAAM,CAAAI,eAAe,CAAGN,OAAO,CAACf,GAAG,CAACC,OAAO,EAAI,CAC7C;AACA,KAAM,CAAAqB,QAAQ,CAAGrB,OAAO,CAACtB,KAAK,EAAId,YAAY,CAAC0D,GAAG,CAACtB,OAAO,CAACtB,KAAK,CAAC,CAEjE,MAAO,CACL,GAAGsB,OAAO,CACVuB,KAAK,CAAEvB,OAAO,CAACwB,WAAW,CAAE;AAC5B7C,MAAM,CAAE0C,QAAQ,CAAG,QAAQ,CAAG,SAAS,CAAE;AACzCzC,KAAK,CAAEyC,QAAQ,CAAIrD,WAAW,CAACyB,OAAO,CAACO,OAAO,CAACtB,KAAK,CAAC,CAAGsB,OAAO,CAACpB,KAAK,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CACnFI,GAAG,CAAEqC,QAAQ,CAAIrD,WAAW,CAACyB,OAAO,CAACO,OAAO,CAACtB,KAAK,CAAC,CAAGsB,OAAO,CAAChB,GAAG,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CAC/EC,GAAG,CAAEoC,QAAQ,CAAIrD,WAAW,CAACyB,OAAO,CAACO,OAAO,CAACtB,KAAK,CAAC,CAAGsB,OAAO,CAACf,GAAG,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CAC/EC,OAAO,CAAEmC,QAAQ,CAAIrD,WAAW,CAACyB,OAAO,CAACO,OAAO,CAACtB,KAAK,CAAC,CAAGsB,OAAO,CAACd,OAAO,EAAI,CAAC,CAAG,CAAC,CAAI,CACxF,CAAC,CACH,CAAC,CAAC,CAEF1C,WAAW,CAAC4E,eAAe,CAAC,CAE5B;AACA,KAAM,CAAAK,WAAW,CAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,EAAIA,CAAC,CAACtC,MAAM,GAAK,QAAQ,CAAC,CAACG,MAAM,CAC7E,KAAM,CAAA4C,UAAU,CAAGN,eAAe,CAACtC,MAAM,CAEzC7B,OAAO,CAACsC,GAAG,CAAC,cAAcmC,UAAU,QAAQD,WAAW,QAAQC,UAAU,CAAGD,WAAW,EAAE,CAAC,CAE1F;AACApE,QAAQ,CAACsE,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZrE,aAAa,CAAEoE,UAAU,CACzBnE,cAAc,CAAEkE,WAAW,CAC3BjE,eAAe,CAAEkE,UAAU,CAAGD,WAChC,CAAC,CAAC,CAAC,CAEH,OACF,CAEA;AACAxE,OAAO,CAACsC,GAAG,CAAC,2BAA2B,CAAC,CACxC,KAAM,CAAAqC,YAAY,CAAG1G,YAAY,CAACqB,QAAQ,EAAI,EAAE,CAChDU,OAAO,CAACsC,GAAG,CAAC,2BAA2B,CAAEqC,YAAY,CAAC9C,MAAM,CAAC,CAE7D;AACA,KAAM,CAAAsC,eAAe,CAAGQ,YAAY,CAAC7B,GAAG,CAACC,OAAO,EAAI,CAClD;AACA,KAAM,CAAAqB,QAAQ,CAAGrB,OAAO,CAACtB,KAAK,EAAId,YAAY,CAAC0D,GAAG,CAACtB,OAAO,CAACtB,KAAK,CAAC,CAEjE,MAAO,CACL,GAAGsB,OAAO,CACVuB,KAAK,CAAEvB,OAAO,CAACwB,WAAW,CAAE;AAC5B7C,MAAM,CAAE0C,QAAQ,CAAG,QAAQ,CAAG,SAAS,CAAE;AACzCzC,KAAK,CAAEyC,QAAQ,CAAIrD,WAAW,CAACyB,OAAO,CAACO,OAAO,CAACtB,KAAK,CAAC,CAAGsB,OAAO,CAACpB,KAAK,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CACnFI,GAAG,CAAEqC,QAAQ,CAAIrD,WAAW,CAACyB,OAAO,CAACO,OAAO,CAACtB,KAAK,CAAC,CAAGsB,OAAO,CAAChB,GAAG,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CAC/EC,GAAG,CAAEoC,QAAQ,CAAIrD,WAAW,CAACyB,OAAO,CAACO,OAAO,CAACtB,KAAK,CAAC,CAAGsB,OAAO,CAACf,GAAG,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CAC/EC,OAAO,CAAEmC,QAAQ,CAAIrD,WAAW,CAACyB,OAAO,CAACO,OAAO,CAACtB,KAAK,CAAC,CAAGsB,OAAO,CAACd,OAAO,EAAI,CAAC,CAAG,CAAC,CAAI,CACxF,CAAC,CACH,CAAC,CAAC,CAEF1C,WAAW,CAAC4E,eAAe,CAAC,CAE5B;AACA,KAAM,CAAAK,WAAW,CAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,EAAIA,CAAC,CAACtC,MAAM,GAAK,QAAQ,CAAC,CAACG,MAAM,CAC7E,KAAM,CAAA4C,UAAU,CAAGN,eAAe,CAACtC,MAAM,CAEzC7B,OAAO,CAACsC,GAAG,CAAC,cAAcmC,UAAU,QAAQD,WAAW,QAAQC,UAAU,CAAGD,WAAW,EAAE,CAAC,CAE1F;AACApE,QAAQ,CAACsE,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZrE,aAAa,CAAEoE,UAAU,CACzBnE,cAAc,CAAEkE,WAAW,CAC3BjE,eAAe,CAAEkE,UAAU,CAAGD,WAChC,CAAC,CAAC,CAAC,CACL,CAAE,MAAOzE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACnC,CACF,CAAC,CAAE,CAACY,YAAY,CAAC,CAAC,CAElB;AACA,KAAM,CAAAiE,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACvE,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAjH,KAAK,CAACkH,GAAG,CAAC,GAAGL,MAAM,cAAc,CAAC,CAEzD,GAAII,QAAQ,CAAC9B,IAAI,EAAI8B,QAAQ,CAAC9B,IAAI,CAACgC,OAAO,CAAE,CAC1C,KAAM,CAAAC,WAAW,CAAGH,QAAQ,CAAC9B,IAAI,CAACA,IAAI,CAEtC;AACA/C,QAAQ,CAACsE,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZlE,YAAY,CAAE4E,WAAW,CAACvD,MAAM,CAChCpB,aAAa,CAAE2E,WAAW,CAACnB,MAAM,CAACoB,CAAC,EAAIA,CAAC,CAAC3D,MAAM,GAAK,QAAQ,CAAC,CAACG,MAAM,CACpEnB,cAAc,CAAE0E,WAAW,CAACnB,MAAM,CAACoB,CAAC,EAAIA,CAAC,CAAC3D,MAAM,GAAK,SAAS,CAAC,CAACG,MAClE,CAAC,CAAC,CAAC,CACL,CACF,CAAE,MAAO9B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACrC,CACF,CAAC,CAED;AACApD,SAAS,CAAC,IAAM,CACd,KAAM,CAAA2I,gBAAgB,CAAIpC,KAAK,EAAK,CAClC,GAAIA,KAAK,CAACC,IAAI,EAAID,KAAK,CAACC,IAAI,CAACC,IAAI,GAAK,KAAK,CAAE,CAC3C;AACA,KAAM,CAAAmC,OAAO,CAAGrC,KAAK,CAACC,IAAI,CAACA,IAAI,EAAI,CAAC,CAAC,CACrC,KAAM,CAAA1B,KAAK,CAAG8D,OAAO,CAAC9D,KAAK,EAAIyB,KAAK,CAACC,IAAI,CAAC1B,KAAK,CAE/C,GAAI,CAACA,KAAK,CAAE,CACVzB,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEmD,KAAK,CAACC,IAAI,CAAC,CAC1C,OACF,CAEA;AACA,KAAM,CAAAT,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CAEtB;AACA3B,WAAW,CAACyB,OAAO,CAACf,KAAK,CAAC,CAAGiB,GAAG,CAEhC;AACA9B,eAAe,CAAC2B,IAAI,EAAI,GAAI,CAAAzB,GAAG,CAAC,CAAC,GAAGyB,IAAI,CAAEd,KAAK,CAAC,CAAC,CAAC,CAElD;AACA,KAAM,CAAAE,KAAK,CAAGQ,UAAU,CAAC,CAACA,UAAU,CAACoD,OAAO,CAACC,SAAS,EAAItC,KAAK,CAACC,IAAI,CAACxB,KAAK,EAAI,CAAC,CAAC,CAAG,GAAG,EAAES,OAAO,CAAC,CAAC,CAAC,CAAC,CAAE;AACrG,KAAM,CAAAL,GAAG,CAAGI,UAAU,CAACA,UAAU,CAACoD,OAAO,CAACE,OAAO,EAAIvC,KAAK,CAACC,IAAI,CAACpB,GAAG,EAAI,CAAC,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC,CACrF,KAAM,CAAAJ,GAAG,CAAGG,UAAU,CAACA,UAAU,CAACoD,OAAO,CAACG,QAAQ,EAAIxC,KAAK,CAACC,IAAI,CAACnB,GAAG,EAAI,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CACtF,KAAM,CAAAH,OAAO,CAAGE,UAAU,CAACA,UAAU,CAACoD,OAAO,CAACI,WAAW,EAAIzC,KAAK,CAACC,IAAI,CAAClB,OAAO,EAAI,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAE;AAEnG;AAEA;AACA7C,WAAW,CAACsD,YAAY,EAAI,CAC1B;AACA,KAAM,CAAA+C,YAAY,CAAG/C,YAAY,CAACgD,IAAI,CAAC7B,CAAC,EAAIA,CAAC,CAACvC,KAAK,GAAKA,KAAK,CAAC,CAC9D,GAAI,CAACmE,YAAY,CAAE,CACjB5F,OAAO,CAACsC,GAAG,CAAC,eAAeb,KAAK,WAAW,CAAC,CAC5C,MAAO,CAAAoB,YAAY,CACrB,CAEA,KAAM,CAAAsB,eAAe,CAAGtB,YAAY,CAACC,GAAG,CAACC,OAAO,EAC9CA,OAAO,CAACtB,KAAK,GAAKA,KAAK,CACnB,CACE,GAAGsB,OAAO,CACVrB,MAAM,CAAE,QAAQ,CAChBC,KAAK,CAAEA,KAAK,CACZI,GAAG,CAAEA,GAAG,CACRC,GAAG,CAAEA,GAAG,CACRC,OAAO,CAAEA,OACX,CAAC,CACDc,OACN,CAAC,CAED;AACA,KAAM,CAAAyB,WAAW,CAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,EAAIA,CAAC,CAACtC,MAAM,GAAK,QAAQ,CAAC,CAACG,MAAM,CAC7E,KAAM,CAAA4C,UAAU,CAAGN,eAAe,CAACtC,MAAM,CAEzC;AACAzB,QAAQ,CAACsE,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZpE,cAAc,CAAEkE,WAAW,CAC3BjE,eAAe,CAAEkE,UAAU,CAAGD,WAChC,CAAC,CAAC,CAAC,CAEH,MAAO,CAAAL,eAAe,CACxB,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACAnB,MAAM,CAACK,gBAAgB,CAAC,SAAS,CAAEiC,gBAAgB,CAAC,CAEpD;AACA,MAAO,IAAM,CACXtC,MAAM,CAACM,mBAAmB,CAAC,SAAS,CAAEgC,gBAAgB,CAAC,CACzD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA3I,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmJ,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAApD,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CACtB1C,OAAO,CAACsC,GAAG,CAAC,aAAa,CAAC,CAE1B;AACA;AACA1B,eAAe,CAAC2B,IAAI,EAAI,CACtB,KAAM,CAAAwD,eAAe,CAAG,GAAI,CAAAjF,GAAG,CAACyB,IAAI,CAAC,CACrC,GAAI,CAAAyD,UAAU,CAAG,KAAK,CAEtB;AACAzD,IAAI,CAAC0D,OAAO,CAACxE,KAAK,EAAI,CACpB,KAAM,CAAAyE,QAAQ,CAAGnF,WAAW,CAACyB,OAAO,CAACf,KAAK,CAAC,CAE3C;AACA,GAAIyE,QAAQ,EAAKxD,GAAG,CAAGwD,QAAQ,CAAG,KAAM,CAAE,CACxClG,OAAO,CAACsC,GAAG,CAAC,KAAKb,KAAK,kBAAkB,CAAC,CACzCsE,eAAe,CAACnD,MAAM,CAACnB,KAAK,CAAC,CAC7BuE,UAAU,CAAG,IAAI,CACnB,CACF,CAAC,CAAC,CAEF,GAAIA,UAAU,CAAE,CACd;AACAzG,WAAW,CAACsD,YAAY,EAAI,CAC1B,KAAM,CAAAsB,eAAe,CAAGtB,YAAY,CAACC,GAAG,CAACC,OAAO,EAAI,CAClD;AACA,GAAIhC,WAAW,CAACyB,OAAO,CAACO,OAAO,CAACtB,KAAK,CAAC,CAAE,CACtC,KAAM,CAAA2C,QAAQ,CAAG2B,eAAe,CAAC1B,GAAG,CAACtB,OAAO,CAACtB,KAAK,CAAC,CACnD,MAAO,CACL,GAAGsB,OAAO,CACVrB,MAAM,CAAE0C,QAAQ,CAAG,QAAQ,CAAG,SAChC,CAAC,CACH,CACA,MAAO,CAAArB,OAAO,CAChB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAyB,WAAW,CAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,EAAIA,CAAC,CAACtC,MAAM,GAAK,QAAQ,CAAC,CAACG,MAAM,CAC7E,KAAM,CAAA4C,UAAU,CAAGN,eAAe,CAACtC,MAAM,CAEzC;AACAzB,QAAQ,CAACsE,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZpE,cAAc,CAAEkE,WAAW,CAC3BjE,eAAe,CAAEkE,UAAU,CAAGD,WAChC,CAAC,CAAC,CAAC,CAEH,MAAO,CAAAL,eAAe,CACxB,CAAC,CAAC,CACJ,CAEA,MAAO,CAAA4B,eAAe,CACxB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAI,QAAQ,CAAGC,WAAW,CAACN,iBAAiB,CAAE,IAAI,CAAC,CACrD,MAAO,IAAMO,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN;AACAxJ,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA2J,gBAAgB,CAAGA,CAAA,GAAM,CAC7B;AACA,GAAI3F,YAAY,CAAC4F,IAAI,GAAK,CAAC,CAAE,CAC3BhH,WAAW,CAACsD,YAAY,EACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,GAAK,CAC3B,GAAGA,OAAO,CACVrB,MAAM,CAAE,SAAS,CACjBC,KAAK,CAAE,CAAC,CACRI,GAAG,CAAE,CAAC,CACNC,GAAG,CAAE,CAAC,CACNC,OAAO,CAAE,CACX,CAAC,CAAC,CACJ,CAAC,CAEDjC,OAAO,CAACsC,GAAG,CAAC,cAAc,CAAC,CAC7B,CACF,CAAC,CAED;AACAgE,gBAAgB,CAAC,CAAC,CAElB;AACA,KAAM,CAAAH,QAAQ,CAAGC,WAAW,CAACE,gBAAgB,CAAE,KAAK,CAAC,CAErD,MAAO,IAAMD,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,CAACxF,YAAY,CAAC,CAAC,CAElB;AACAhE,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6J,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3BnH,UAAU,CAAC,IAAI,CAAC,CAChBuE,aAAa,CAAC,CAAC,CACf,KAAM,CAAAgB,gBAAgB,CAAC,CAAC,CACxBvF,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAEDmH,QAAQ,CAAC,CAAC,CACV;AACA;AACA;AACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA7J,SAAS,CAAC,IAAM,CACdqD,OAAO,CAACsC,GAAG,CAAC,YAAY,CAAC,CAEzB;AACA,KAAM,CAAAmE,yBAAyB,CAAGA,CAAA,GAAM,CACtCzG,OAAO,CAACsC,GAAG,CAAC,sBAAsB,CAAC,CACnCsB,aAAa,CAAC,CAAC,CACjB,CAAC,CAED;AACA,KAAM,CAAA8C,mBAAmB,CAAIxD,KAAK,EAAK,CACrC,GAAIA,KAAK,CAACyD,GAAG,GAAK,qBAAqB,EAAIzD,KAAK,CAACyD,GAAG,GAAK,cAAc,CAAE,CACvE3G,OAAO,CAACsC,GAAG,CAAC,4BAA4B,CAAC,CACzCsB,aAAa,CAAC,CAAC,CACjB,CACF,CAAC,CAED;AACAZ,MAAM,CAACK,gBAAgB,CAAC,qBAAqB,CAAEoD,yBAAyB,CAAC,CACzEzD,MAAM,CAACK,gBAAgB,CAAC,SAAS,CAAEqD,mBAAmB,CAAC,CAEvD;AACA,KAAM,CAAAE,WAAW,CAAGjH,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAC/D,GAAIgH,WAAW,CAAE,CACf5G,OAAO,CAACsC,GAAG,CAAC,2BAA2B,CAAEsE,WAAW,CAAC,CACrDhD,aAAa,CAAC,CAAC,CACjB,CAEA;AACA,KAAM,CAAAiD,qBAAqB,CAAGT,WAAW,CAAC,IAAM,CAC9CpG,OAAO,CAACsC,GAAG,CAAC,eAAe,CAAC,CAC5BsB,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX;AACA,MAAO,IAAM,CACXZ,MAAM,CAACM,mBAAmB,CAAC,qBAAqB,CAAEmD,yBAAyB,CAAC,CAC5EzD,MAAM,CAACM,mBAAmB,CAAC,SAAS,CAAEoD,mBAAmB,CAAC,CAC1DL,aAAa,CAACQ,qBAAqB,CAAC,CACtC,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA;AACA,KAAM,CAAA/C,uBAAuB,CAAG,cAAAA,CAAA,CAA8B,IAAvB,CAAAgD,UAAU,CAAAlF,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CACvD,GAAI,CACF;AACA,KAAM,CAAAmF,eAAe,CAAG,CACtBjC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBACjC;AACA;AACA;AACA;AACA;AACA;AAAA,CACD,CAEDhF,OAAO,CAACsC,GAAG,CAAC,kBAAkB,CAAC,CAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA,GAAI,CAAA0E,SAAS,CAAG,KAAK,CACrB,GAAI,CAAA/I,YAAY,CAAG,IAAI,CAEvB,IAAK,KAAM,CAAA4G,MAAM,GAAI,CAAAkC,eAAe,CAAE,CACpC,GAAIC,SAAS,CAAE,MAEf,GAAI,CACFhH,OAAO,CAACsC,GAAG,CAAC,OAAOuC,MAAM,yBAAyB,CAAC,CACnD,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAjH,KAAK,CAACkH,GAAG,CAAC,GAAGL,MAAM,oBAAoB,CAAC,CAE/D,GAAII,QAAQ,CAAC9B,IAAI,EAAI8B,QAAQ,CAAC9B,IAAI,CAAC7D,QAAQ,CAAE,CAC3CU,OAAO,CAACsC,GAAG,CAAC,OAAOuC,MAAM,QAAQ,CAAEI,QAAQ,CAAC9B,IAAI,CAAC7D,QAAQ,CAACuC,MAAM,CAAC,CACjE5D,YAAY,CAAGgH,QAAQ,CAAC9B,IAAI,CAAC7D,QAAQ,CAErC,GAAIwH,UAAU,CAAE,CACd,MAAO,CAAA7I,YAAY,CACrB,CAEAgJ,mBAAmB,CAAChC,QAAQ,CAAC9B,IAAI,CAAC7D,QAAQ,CAAC,CAC3C0H,SAAS,CAAG,IAAI,CAChB,MACF,CACF,CAAE,MAAOjH,KAAK,CAAE,CACdC,OAAO,CAACsC,GAAG,CAAC,KAAKuC,MAAM,QAAQ,CAAE9E,KAAK,CAACmH,OAAO,CAAC,CAC/C;AACF,CACF,CAEA,GAAI,CAACF,SAAS,EAAI,CAACF,UAAU,CAAE,CAC7B9G,OAAO,CAACsC,GAAG,CAAC,gCAAgC,CAAC,CAC7C;AACA,GAAI,CACF,KAAM,CAAA2C,QAAQ,CAAG,KAAM,CAAAkC,KAAK,CAAC,gBAAgB,CAAC,CAC9C,GAAIlC,QAAQ,CAACmC,EAAE,CAAE,CACf,KAAM,CAAAjE,IAAI,CAAG,KAAM,CAAA8B,QAAQ,CAACoC,IAAI,CAAC,CAAC,CAClC,GAAIlE,IAAI,EAAIA,IAAI,CAAC7D,QAAQ,CAAE,CACzBU,OAAO,CAACsC,GAAG,CAAC,8BAA8B,CAAEa,IAAI,CAAC7D,QAAQ,CAACuC,MAAM,CAAC,CACjEoF,mBAAmB,CAAC9D,IAAI,CAAC7D,QAAQ,CAAC,CAClC,MAAO,CAAA6D,IAAI,CAAC7D,QAAQ,CACtB,CACF,CACF,CAAE,MAAOgI,CAAC,CAAE,CACVtH,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEuH,CAAC,CAAC,CAChD,CACF,CAEA,MAAO,CAAArJ,YAAY,EAAI,EAAE,CAC3B,CAAE,MAAO8B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA,KAAM,CAAAkH,mBAAmB,CAAIM,WAAW,EAAK,CAC3C;AACA,GAAIjI,QAAQ,CAACuC,MAAM,GAAK0F,WAAW,CAAC1F,MAAM,CAAE,CAC1C7B,OAAO,CAACsC,GAAG,CAAC,aAAa,CAAEhD,QAAQ,CAACuC,MAAM,CAAE,GAAG,CAAE0F,WAAW,CAAC1F,MAAM,CAAC,CACpE+B,aAAa,CAAC,CAAC,CAAE;AACjB,OACF,CAEA;AACA,KAAM,CAAA4D,UAAU,CAAG,GAAI,CAAA1G,GAAG,CAACxB,QAAQ,CAACwD,GAAG,CAACkB,CAAC,EAAIA,CAAC,CAACE,EAAE,CAAC,CAAC,CACnD,KAAM,CAAAuD,aAAa,CAAGF,WAAW,CAACG,IAAI,CAAC1D,CAAC,EAAI,CAACwD,UAAU,CAACnD,GAAG,CAACL,CAAC,CAACE,EAAE,CAAC,CAAC,CAElE,GAAIuD,aAAa,CAAE,CACjBzH,OAAO,CAACsC,GAAG,CAAC,SAAS,CAAC,CACtBsB,aAAa,CAAC,CAAC,CAAE;AACnB,CACF,CAAC,CAED;AACAjH,SAAS,CAAC,IAAM,CACd;AACA,GAAI2C,QAAQ,CAACuC,MAAM,CAAG,CAAC,EAAI,CAAC5B,eAAe,CAAE,CAC3C;AACAC,kBAAkB,CAACZ,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC/BU,OAAO,CAACsC,GAAG,CAAC,aAAa,CAAEhD,QAAQ,CAAC,CAAC,CAAC,CAACiF,WAAW,CAAC,CACrD,CACF,CAAC,CAAE,CAACjF,QAAQ,CAAEW,eAAe,CAAC,CAAC,CAE/B;AACAtD,SAAS,CAAC,IAAM,CACd,GAAI,CAAAgL,KAAK,CAAG,IAAI,CAChB,GAAI,CAAAC,YAAY,CAAG,IAAI,CACvB,GAAI,CAAAC,cAAc,CAAG,GAAI,CAAApF,IAAI,CAAC,CAAC,CAE/B;AACA,GAAI,CAACzB,aAAa,CAACwB,OAAO,CAAE,CAC1BxC,OAAO,CAACD,KAAK,CAAC,SAAS,CAAC,CACxB,OACF,CAEA;AACA,GAAI,CAAA+H,aAAa,CAAGvK,OAAO,CAACwK,gBAAgB,CAAC/G,aAAa,CAACwB,OAAO,CAAC,CACnE,GAAIsF,aAAa,CAAE,CACjBA,aAAa,CAACE,OAAO,CAAC,CAAC,CACzB,CAEA,GAAI,CACF;AACAL,KAAK,CAAGpK,OAAO,CAAC0K,IAAI,CAACjH,aAAa,CAACwB,OAAO,CAAC,CAE3C;AACA,KAAM,CAAA0F,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAA1F,IAAI,CAAC,CAAC,CAC9BoF,cAAc,CAAGM,WAAW,CAE5B;AACA,KAAM,CAAAC,UAAU,CAAG,CACjB,CAAEhF,IAAI,CAAE,KAAK,CAAEiF,IAAI,CAAE,OAAO,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAAElF,IAAI,CAAE,KAAK,CAAEiF,IAAI,CAAE,OAAO,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAAElF,IAAI,CAAE,KAAK,CAAEiF,IAAI,CAAE,QAAQ,CAAEC,KAAK,CAAE,SAAU,CAAC,CACjD,CAAElF,IAAI,CAAE,KAAK,CAAEiF,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC/C,CAAElF,IAAI,CAAE,KAAK,CAAEiF,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC/C,CAAElF,IAAI,CAAE,MAAM,CAAEiF,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAAElF,IAAI,CAAE,KAAK,CAAEiF,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAED;AACA,KAAM,CAAAnF,IAAI,CAAGiF,UAAU,CACpBtF,GAAG,CAACI,KAAK,GAAK,CACbqF,KAAK,CAAEpH,UAAU,CAAC+B,KAAK,CAACE,IAAI,CAAC,EAAI,CAAC,CAClCiF,IAAI,CAAEnF,KAAK,CAACmF,IAAI,CAChBG,SAAS,CAAE,CAAEF,KAAK,CAAEpF,KAAK,CAACoF,KAAM,CAClC,CAAC,CAAC,CAAC,CACFrE,MAAM,CAACwE,IAAI,EAAIA,IAAI,CAACF,KAAK,CAAG,CAAC,CAAC,CAC9BG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAACL,KAAK,CAAGI,CAAC,CAACJ,KAAK,CAAC,CAEpC,KAAM,CAAAM,MAAM,CAAG,CACbC,KAAK,CAAE,CACLC,IAAI,CAAE,SAASZ,WAAW,CAACa,kBAAkB,CAAC,CAAC,EAAE,CACjDC,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,CAAC,CAAC,CACPC,SAAS,CAAE,CACTC,QAAQ,CAAE,EAAE,CACZd,KAAK,CAAE,MACT,CACF,CAAC,CACDe,IAAI,CAAE,CACJH,GAAG,CAAE,EAAE,CACPI,MAAM,CAAE,CAAC,CACTL,IAAI,CAAE,CAAC,CACPM,KAAK,CAAE,EAAE,CACTC,YAAY,CAAE,IAChB,CAAC,CACDC,SAAS,CAAE,IAAI,CACfC,iBAAiB,CAAE,CAAC,CACpBC,uBAAuB,CAAE,IAAI,CAC7BC,qBAAqB,CAAE,cAAc,CACrCC,OAAO,CAAE,CACPC,OAAO,CAAE,MAAM,CACfC,WAAW,CAAE,CACX3G,IAAI,CAAE,QACR,CACF,CAAC,CACD4G,KAAK,CAAE,CACL5G,IAAI,CAAE,OAAO,CACb6G,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,CAAED,IAAI,CAAE,KAAM,CAC3B,CAAC,CACDE,KAAK,CAAE,CACL/G,IAAI,CAAE,UAAU,CAChBD,IAAI,CAAEA,IAAI,CAACL,GAAG,CAAC2F,IAAI,EAAIA,IAAI,CAACJ,IAAI,CAAC,CACjC+B,SAAS,CAAE,CACThB,QAAQ,CAAE,EAAE,CACZd,KAAK,CAAE,MAAM,CACb+B,MAAM,CAAE,CACV,CAAC,CACDC,QAAQ,CAAE,CAAEL,IAAI,CAAE,KAAM,CAAC,CACzBM,QAAQ,CAAE,CAAEN,IAAI,CAAE,KAAM,CAC1B,CAAC,CACDO,MAAM,CAAE,CAAC,CACPpH,IAAI,CAAE,KAAK,CACXD,IAAI,CAAEA,IAAI,CACVsH,QAAQ,CAAE,KAAK,CACfC,KAAK,CAAE,CACLT,IAAI,CAAE,IAAI,CACVU,QAAQ,CAAE,OAAO,CACjBC,SAAS,CAAE,MAAM,CACjBxB,QAAQ,CAAE,EAAE,CACZd,KAAK,CAAE,MACT,CAAC,CACDE,SAAS,CAAE,CACTqC,YAAY,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAC3B,CAAC,CACDC,YAAY,CAAE,KAAK,CACnBC,cAAc,CAAE,QAAAA,CAAUC,GAAG,CAAE,CAC7B,MAAO,CAAAA,GAAG,CAAG,GAAG,CAClB,CACF,CAAC,CACH,CAAC,CAED;AACArD,KAAK,CAACsD,SAAS,CAACpC,MAAM,CAAE,CACtBqC,QAAQ,CAAE,KAAK,CACfC,YAAY,CAAE,CAAC,QAAQ,CACzB,CAAC,CAAC,CACJ,CAAC,CAED;AACAjD,WAAW,CAAC,CAAC,CAEb;AACA,KAAM,CAAAkD,aAAa,CAAGhF,WAAW,CAAC8B,WAAW,CAAE,KAAK,CAAC,CAAE;AAEvD;AACAN,YAAY,CAAGA,CAAA,GAAM,KAAAyD,MAAA,CACnB,CAAAA,MAAA,CAAA1D,KAAK,UAAA0D,MAAA,iBAALA,MAAA,CAAOC,MAAM,CAAC,CAAC,CACjB,CAAC,CACDtI,MAAM,CAACK,gBAAgB,CAAC,QAAQ,CAAEuE,YAAY,CAAC,CAE/C;AACA,MAAO,IAAM,CACXvB,aAAa,CAAC+E,aAAa,CAAC,CAC5B,GAAIxD,YAAY,CAAE,CAChB5E,MAAM,CAACM,mBAAmB,CAAC,QAAQ,CAAEsE,YAAY,CAAC,CACpD,CACA,GAAID,KAAK,CAAE,CACTA,KAAK,CAACK,OAAO,CAAC,CAAC,CACjB,CACF,CAAC,CAEH,CAAE,MAAOjI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CAAE,CAACoB,UAAU,CAAC,CAAC,CAEhB;AACAxE,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4O,gBAAgB,CAAIrI,KAAK,EAAK,CAClC,GAAI,CACF,GAAIA,KAAK,CAACC,IAAI,EAAID,KAAK,CAACC,IAAI,CAACC,IAAI,GAAK,KAAK,CAAE,CAC3C,KAAM,CAAAoI,OAAO,CAAGtI,KAAK,CAACC,IAAI,CAACA,IAAI,CAC/B,GAAI,CAACqI,OAAO,EAAI,CAACA,OAAO,CAACC,IAAI,CAAE,OAC/B,GAAGD,OAAO,CAACC,IAAI,CAAC5J,MAAM,CAAG,CAAC,CAAC,CACzB7B,OAAO,CAACsC,GAAG,CAAC,4BAA4B,CAAEY,KAAK,CAACC,IAAI,CAAC,CACvD,CAEA,KAAM,CAAAuI,QAAQ,CAAGvJ,UAAU,CAACqJ,OAAO,CAACG,MAAM,CAAC,CAC3C,KAAM,CAAAC,SAAS,CAAGzJ,UAAU,CAACqJ,OAAO,CAACK,OAAO,CAAC,CAC7C,KAAM,CAAAC,KAAK,CAAGN,OAAO,CAACM,KAAK,CAC3B,KAAM,CAAAC,GAAG,CAAG7I,KAAK,CAACC,IAAI,CAAC4I,GAAG,EAAI,EAAE,CAChC,KAAM,CAAAC,SAAS,CAAG9I,KAAK,CAACC,IAAI,CAAC8I,EAAE,EAAIxJ,IAAI,CAACC,GAAG,CAAC,CAAC,CAE7C;AACA,KAAM,CAAAwJ,sBAAsB,CAAG,EAAE,CACjCV,OAAO,CAACC,IAAI,CAACxF,OAAO,CAAC/C,KAAK,EAAI,CAC5B,KAAM,CAAAiJ,SAAS,CAAGjJ,KAAK,CAACiJ,SAAS,CACjC;AACA,GAAI,CAAAC,QAAQ,CAAG,EAAE,CACjB,GAAI,CAAAC,QAAQ,CAAG,EAAE,CACjB,GAAGF,SAAS,EAAG,KAAK,EAAIA,SAAS,EAAG,KAAK,CAAC,CACxC;AACA;AACAC,QAAQ,CAAGE,IAAI,CAACC,KAAK,CAACb,QAAQ,CAAGY,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DH,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAACX,SAAS,CAAEU,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CACjE,CAAC,IACG,CACF;AACA;AACAJ,QAAQ,CAAGE,IAAI,CAACC,KAAK,CAACb,QAAQ,CAAGY,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DH,QAAQ,CAAGC,IAAI,CAACC,KAAK,CAACX,SAAS,CAAEU,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CACjE,CACA,KAAM,CAAAC,QAAQ,CAAG,GAAGX,KAAK,IAAIC,GAAG,IAAII,SAAS,IAAIC,QAAQ,IAAIC,QAAQ,EAAE,CACvE,KAAM,CAAAK,WAAW,CAAGC,mBAAmB,CACrCF,QAAQ,CACRT,SAAS,CACT,CAAEjK,GAAG,CAAE2J,QAAQ,CAAE1J,GAAG,CAAE4J,SAAU,CAAC,CACjCtK,aAAa,CAACkB,OAChB,CAAC,CACD;AACA,GAAI,CAAAoK,aAAa,CAAG,EAAE,CACtB,GAAI,CAAAC,UAAU,CAAG,EAAE,CACnB,OAAOV,SAAS,EACd,IAAK,KAAK,CAAES,aAAa,CAAG,OAAO,CAAEC,UAAU,CAAG,SAAS,CAAE,MAC7D,IAAK,KAAK,CAAED,aAAa,CAAG,OAAO,CAAEC,UAAU,CAAG,SAAS,CAAE,MAC7D,IAAK,KAAK,CAAED,aAAa,CAAG,QAAQ,CAAEC,UAAU,CAAG,SAAS,CAAE,MAC9D,IAAK,KAAK,CAAED,aAAa,CAAG,MAAM,CAAEC,UAAU,CAAG,SAAS,CAAE,MAC5D,IAAK,KAAK,CAAED,aAAa,CAAG,MAAM,CAAEC,UAAU,CAAG,SAAS,CAAE,MAC5D,IAAK,MAAM,CAAED,aAAa,CAAG,MAAM,CAAEC,UAAU,CAAG,SAAS,CAAE,MAC7D,IAAK,KAAK,CAAED,aAAa,CAAG,MAAM,CAAEC,UAAU,CAAG,SAAS,CAAE,MAC5D,QAASD,aAAa,CAAG1J,KAAK,CAAC4J,WAAW,EAAI,MAAM,CAAED,UAAU,CAAG,SAAS,CAC9E,CACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA,GAAI,CAACH,WAAW,CAAE,CAChB;AACA,KAAM,CAAAK,QAAQ,CAAG,CACfpG,GAAG,CAAElE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG4J,IAAI,CAACU,MAAM,CAAC,CAAC,CAC/B5J,IAAI,CAAEwJ,aAAa,CACnBK,IAAI,CAAE,GAAI,CAAAxK,IAAI,CAAC,CAAC,CAACuG,kBAAkB,CAAC,CAAC,CACrCjG,OAAO,CAAEyI,OAAO,CAACM,KAAK,EAAI,MAAM,CAChCxD,KAAK,CAAEuE,UAAU,CACjBV,SAAS,CAAEA,SAAS,CACpBe,QAAQ,CAAE,CACRxB,QAAQ,CAAEA,QAAQ,CAClBE,SAAS,CAAEA,SACb,CAEF,CAAC,CACDnM,SAAS,CAAC8C,IAAI,EAAI,CAChB,KAAM,CAAA4K,SAAS,CAAG,CAACJ,QAAQ,CAAE,GAAGxK,IAAI,CAAC,CAAC6K,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAE;AACpD,MAAO,CAAAD,SAAS,CAClB,CAAC,CAAC,CAEFjB,sBAAsB,CAACmB,IAAI,CAAClB,SAAS,CAAC,CACxC,CAAC,IAAM,CACLnM,OAAO,CAACsC,GAAG,CAAC,kBAAkBsK,aAAa,EAAE,CAAC,CAChD,CACF,CAAC,CAAC,CAEF;AACA,GAAIV,sBAAsB,CAACrK,MAAM,CAAG,CAAC,CAAE,CACrCT,aAAa,CAACmB,IAAI,EAAI,CACpB,KAAM,CAAA+K,QAAQ,CAAG,CAAE,GAAG/K,IAAK,CAAC,CAC5B2J,sBAAsB,CAACjG,OAAO,CAACkG,SAAS,EAAI,CAC1CmB,QAAQ,CAACnB,SAAS,CAAC,CAAG,CAACmB,QAAQ,CAACnB,SAAS,CAAC,EAAI,CAAC,EAAI,CAAC,CACtD,CAAC,CAAC,CACF,MAAO,CAAAmB,QAAQ,CACjB,CAAC,CAAC,CACJ,CACF,CACF,CAAE,MAAOvN,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACtC,CACF,CAAC,CAED;AACAiD,MAAM,CAACK,gBAAgB,CAAC,SAAS,CAAEkI,gBAAgB,CAAC,CAEpD,MAAO,IAAM,CACXvI,MAAM,CAACM,mBAAmB,CAAC,SAAS,CAAEiI,gBAAgB,CAAC,CACzD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAoB,mBAAmB,CAAGA,CAACF,QAAQ,CAAEtE,WAAW,CAAEoF,UAAU,CAAEC,UAAU,GAAK,CAC7E;AACA,GAAIA,UAAU,CAACnJ,GAAG,CAACoI,QAAQ,CAAC,CAAE,CAC5B,KAAM,CAAAgB,SAAS,CAAGD,UAAU,CAACtI,GAAG,CAACuH,QAAQ,CAAC,CAC1C,KAAM,CAAAiB,QAAQ,CAAGvF,WAAW,CAAGsF,SAAS,CAACR,IAAI,CAE7C;AACA,GAAIS,QAAQ,CAAG,IAAI,CAAE,CACnB;AACA,KAAM,CAAAC,QAAQ,CAAGC,iBAAiB,CAChCL,UAAU,CAACxL,GAAG,CAAEwL,UAAU,CAACvL,GAAG,CAC9ByL,SAAS,CAACI,GAAG,CAAC9L,GAAG,CAAE0L,SAAS,CAACI,GAAG,CAAC7L,GACnC,CAAC,CAED;AACA,GAAI2L,QAAQ,CAAG,CAAC,CAAE,CAChB;AACAH,UAAU,CAACM,GAAG,CAACrB,QAAQ,CAAE,CACvBQ,IAAI,CAAE9E,WAAW,CACjB0F,GAAG,CAAEN,UACP,CAAC,CAAC,CACF,MAAO,KAAI,CACb,CACF,CACF,CAEA;AACAC,UAAU,CAACM,GAAG,CAACrB,QAAQ,CAAE,CACvBQ,IAAI,CAAE9E,WAAW,CACjB0F,GAAG,CAAEN,UACP,CAAC,CAAC,CAEF;AACA,MAAO,MAAK,CACd,CAAC,CAED;AACA,KAAM,CAAAK,iBAAiB,CAAGA,CAACG,IAAI,CAAEC,IAAI,CAAEC,IAAI,CAAEC,IAAI,GAAK,CACpD;AACA,GAAIH,IAAI,GAAK,CAAC,EAAIC,IAAI,GAAK,CAAC,EAAIC,IAAI,GAAK,CAAC,EAAIC,IAAI,GAAK,CAAC,CAAE,CACxD,MAAO,IAAG,CACZ,CAEA;AACA,KAAM,CAAAC,CAAC,CAAG,OAAO,CAAE;AACnB,KAAM,CAAAC,IAAI,CAAG,CAACH,IAAI,CAAGF,IAAI,EAAIzB,IAAI,CAAC+B,EAAE,CAAG,GAAG,CAC1C,KAAM,CAAAC,IAAI,CAAG,CAACJ,IAAI,CAAGF,IAAI,EAAI1B,IAAI,CAAC+B,EAAE,CAAG,GAAG,CAC1C,KAAM,CAAA1F,CAAC,CACL2D,IAAI,CAACiC,GAAG,CAACH,IAAI,CAAC,CAAC,CAAC,CAAG9B,IAAI,CAACiC,GAAG,CAACH,IAAI,CAAC,CAAC,CAAC,CACnC9B,IAAI,CAACkC,GAAG,CAACT,IAAI,CAAGzB,IAAI,CAAC+B,EAAE,CAAG,GAAG,CAAC,CAAG/B,IAAI,CAACkC,GAAG,CAACP,IAAI,CAAG3B,IAAI,CAAC+B,EAAE,CAAG,GAAG,CAAC,CAC/D/B,IAAI,CAACiC,GAAG,CAACD,IAAI,CAAC,CAAC,CAAC,CAAGhC,IAAI,CAACiC,GAAG,CAACD,IAAI,CAAC,CAAC,CAAC,CACrC,KAAM,CAAAG,CAAC,CAAG,CAAC,CAAGnC,IAAI,CAACoC,KAAK,CAACpC,IAAI,CAACqC,IAAI,CAAChG,CAAC,CAAC,CAAE2D,IAAI,CAACqC,IAAI,CAAC,CAAC,CAAChG,CAAC,CAAC,CAAC,CACtD,KAAM,CAAAgF,QAAQ,CAAGQ,CAAC,CAAGM,CAAC,CAEtB,MAAO,CAAAd,QAAQ,CACjB,CAAC,CAED;AACA,KAAM,CAAAiB,mBAAmB,CAAI7L,OAAO,EAAK,CACvC/C,OAAO,CAACsC,GAAG,CAAC,OAAO,CAAES,OAAO,CAACwB,WAAW,CAAE,KAAK,CAAExB,OAAO,CAACrB,MAAM,CAAC,CAChExB,kBAAkB,CAAC6C,OAAO,CAAC,CAC7B,CAAC,CAED;AACApG,SAAS,CAAC,IAAM,CACd;AACA,GAAIsD,eAAe,CAAE,CACnB,KAAM,CAAA4O,sBAAsB,CAAGvP,QAAQ,CAACuG,IAAI,CAAC7B,CAAC,EAAIA,CAAC,CAACE,EAAE,GAAKjE,eAAe,CAACiE,EAAE,CAAC,CAC9E,GAAI2K,sBAAsB,GACrBA,sBAAsB,CAACnN,MAAM,GAAKzB,eAAe,CAACyB,MAAM,EACxDmN,sBAAsB,CAAClN,KAAK,GAAK1B,eAAe,CAAC0B,KAAK,EACtDkN,sBAAsB,CAAC9M,GAAG,GAAK9B,eAAe,CAAC8B,GAAG,EAClD8M,sBAAsB,CAAC7M,GAAG,GAAK/B,eAAe,CAAC+B,GAAG,EAClD6M,sBAAsB,CAAC5M,OAAO,GAAKhC,eAAe,CAACgC,OAAO,CAAC,CAAE,CAChEjC,OAAO,CAACsC,GAAG,CAAC,UAAUrC,eAAe,CAACsE,WAAW,OAAO,CAC7C,OAAOtE,eAAe,CAACyB,MAAM,OAAOmN,sBAAsB,CAACnN,MAAM,EAAE,CAAC,CAC/ExB,kBAAkB,CAAC2O,sBAAsB,CAAC,CAC5C,CACF,CACF,CAAC,CAAE,CAACvP,QAAQ,CAAEW,eAAe,CAAC,CAAC,CAE/B;AACA,KAAM,CAAA6O,cAAc,CAAG,CACrB,CACEhG,KAAK,CAAE,KAAK,CACZiG,SAAS,CAAE,OAAO,CAClBpI,GAAG,CAAE,OAAO,CACZqI,KAAK,CAAE,KACT,CAAC,CACD,CACElG,KAAK,CAAE,IAAI,CACXiG,SAAS,CAAE,QAAQ,CACnBpI,GAAG,CAAE,QAAQ,CACbqI,KAAK,CAAE,KAAK,CACZC,MAAM,CAAEvN,MAAM,eACZvD,IAAA,CAACb,KAAK,EACJoE,MAAM,CAAEA,MAAM,GAAK,QAAQ,CAAG,SAAS,CAAG,OAAQ,CAClDqH,IAAI,CAAErH,MAAM,GAAK,QAAQ,CAAG,IAAI,CAAG,IAAK,CACzC,CAEL,CAAC,CACD,CACEoH,KAAK,CAAE,IAAI,CACXiG,SAAS,CAAE,OAAO,CAClBpI,GAAG,CAAE,OAAO,CACZqI,KAAK,CAAE,KAAK,CACZC,MAAM,CAAEtN,KAAK,EAAI,GAAG,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAGA,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAAGT,KAAK,OAC1E,CAAC,CACF,CAED;AACA,KAAM,CAAAuN,eAAe,CAAGA,CAAA,gBACtB/Q,IAAA,CAACjB,IAAI,EACHqJ,IAAI,CAAC,OAAO,CACZ4I,UAAU,CAAE3P,MAAO,CACnB4P,UAAU,CAAE3G,IAAI,eACdtK,IAAA,CAACjB,IAAI,CAACmS,IAAI,EAACC,KAAK,CAAE,CAAEC,OAAO,CAAE,OAAQ,CAAE,CAAAC,QAAA,cACrCrR,IAAA,CAACjB,IAAI,CAACmS,IAAI,CAACI,IAAI,EACb3G,KAAK,cACHzK,KAAA,QAAKiR,KAAK,CAAE,CAAElG,QAAQ,CAAE,MAAM,CAAEsG,YAAY,CAAE,KAAM,CAAE,CAAAF,QAAA,eACpDrR,IAAA,SAAMmR,KAAK,CAAE,CAAEhH,KAAK,CAAEG,IAAI,CAACH,KAAK,CAAEqH,WAAW,CAAE,KAAM,CAAE,CAAAH,QAAA,CACpD/G,IAAI,CAACrF,IAAI,CACN,CAAC,cACPjF,IAAA,SAAMmR,KAAK,CAAE,CAAEhH,KAAK,CAAE,MAAM,CAAEc,QAAQ,CAAE,MAAO,CAAE,CAAAoG,QAAA,CAC9C/G,IAAI,CAACwE,IAAI,CACN,CAAC,EACJ,CACN,CACDH,WAAW,cACTzO,KAAA,QAAKiR,KAAK,CAAE,CAAElG,QAAQ,CAAE,MAAM,CAAEd,KAAK,CAAE,MAAO,CAAE,CAAAkH,QAAA,eAC9CnR,KAAA,QAAAmR,QAAA,EAAK,gBAAI,CAAC/G,IAAI,CAAC1F,OAAO,EAAM,CAAC,cAC7B1E,KAAA,QAAAmR,QAAA,EAAK,gBAAI,CAAC/G,IAAI,CAACyE,QAAQ,CACrB,GAAGzE,IAAI,CAACyE,QAAQ,CAACxB,QAAQ,CAACtJ,OAAO,CAAC,CAAC,CAAC,KAAKqG,IAAI,CAACyE,QAAQ,CAACtB,SAAS,CAACxJ,OAAO,CAAC,CAAC,CAAC,EAAE,CAC7E,MAAM,EACH,CAAC,EACH,CACN,CACF,CAAC,CACO,CACX,CACFkN,KAAK,CAAE,CACLM,SAAS,CAAE,mBAAmB,CAC9BC,SAAS,CAAE,MACb,CAAE,CACH,CACF,CAED;AACAlT,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmT,kBAAkB,CAAG1J,WAAW,CAAC,IAAM,CAC3CpG,OAAO,CAACsC,GAAG,CAAC,gBAAgB,CAAC,CAC7BwB,uBAAuB,CAAC,CAAC,CAC3B,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX,MAAO,IAAMuC,aAAa,CAACyJ,kBAAkB,CAAC,CAChD,CAAC,CAAE,CAACxQ,QAAQ,CAAC,CAAC,CAEd,mBACEnB,IAAA,CAACd,IAAI,EAAC0S,QAAQ,CAAE3Q,OAAQ,CAAC4Q,GAAG,CAAC,uBAAQ,CAAAR,QAAA,cACnCnR,KAAA,CAACI,aAAa,EAAA+Q,QAAA,eAEZnR,KAAA,CAACN,kBAAkB,EACjB4M,QAAQ,CAAC,MAAM,CACfsF,SAAS,CAAEnR,aAAc,CACzBoR,UAAU,CAAEA,CAAA,GAAMjP,gBAAgB,CAAC,CAACnC,aAAa,CAAE,CAAA0Q,QAAA,eAGnDrR,IAAA,CAACa,QAAQ,EAAC8J,KAAK,CAAC,4CAAS,CAACqH,QAAQ,CAAE,KAAM,CAAClR,MAAM,CAAC,OAAO,CAAAuQ,QAAA,cACvDnR,KAAA,CAACvB,GAAG,EAACsT,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAAZ,QAAA,eAClBrR,IAAA,CAACpB,GAAG,EAACsT,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjErR,IAAA,CAACe,gBAAgB,EACf4J,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEpI,KAAK,CAACE,aAAc,CAC3BmQ,UAAU,CAAE,CAAElI,KAAK,CAAE,SAAS,CAACgI,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CACxE;AAAA,CACD,CAAC,CACC,CAAC,cACNpS,IAAA,CAACpB,GAAG,EAACsT,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjErR,IAAA,CAACe,gBAAgB,EACf4J,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEpI,KAAK,CAACG,cACb;AAAA,CACAkQ,UAAU,CAAE,CAAElI,KAAK,CAAE,SAAS,CAAEgI,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAQ,CAAE,CAC3E,CAAC,CACC,CAAC,cACNpS,IAAA,CAACpB,GAAG,EAACsT,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjErR,IAAA,CAACe,gBAAgB,EACf4J,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEpI,KAAK,CAACI,eACb;AAAA,CACAiQ,UAAU,CAAE,CAAElI,KAAK,CAAE,SAAS,CAAEgI,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CAAE,CAC5E,CAAC,CACC,CAAC,cACNpS,IAAA,CAACpB,GAAG,EAACsT,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjErR,IAAA,CAACe,gBAAgB,EACf4J,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEpI,KAAK,CAACK,YAAa,CAC1BgQ,UAAU,CAAE,CAAElI,KAAK,CAAE,SAAS,CAACgI,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CAAE,CAC3E,CAAC,CACC,CAAC,cACNpS,IAAA,CAACpB,GAAG,EAACsT,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjErR,IAAA,CAACe,gBAAgB,EACf4J,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEpI,KAAK,CAACM,aACb;AAAA,CACA+P,UAAU,CAAE,CAAElI,KAAK,CAAE,SAAS,CAACgI,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CAAE,CAC3E,CAAC,CACC,CAAC,cACNpS,IAAA,CAACpB,GAAG,EAACsT,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjErR,IAAA,CAACe,gBAAgB,EACf4J,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEpI,KAAK,CAACO,cACb;AAAA,CACA8P,UAAU,CAAE,CAAElI,KAAK,CAAE,SAAS,CAACgI,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CAAE,CAC3E,CAAC,CACC,CAAC,EACH,CAAC,CACE,CAAC,cAGXpS,IAAA,CAACa,QAAQ,EAAC8J,KAAK,CAAC,sCAAQ,CAACqH,QAAQ,CAAE,KAAM,CAAClR,MAAM,CAAC,kBAAkB,CAAAuQ,QAAA,CAChEN,eAAe,CAAC,CAAC,CACV,CAAC,cAGX/Q,IAAA,CAACa,QAAQ,EAAC8J,KAAK,CAAC,sCAAQ,CAACqH,QAAQ,CAAE,KAAM,CAAClR,MAAM,CAAC,kBAAkB,CAAAuQ,QAAA,cACjErR,IAAA,QAAKsS,GAAG,CAAEzP,aAAc,CAACsO,KAAK,CAAE,CAAErQ,MAAM,CAAE,MAAM,CAAE+P,KAAK,CAAE,MAAO,CAAE,CAAM,CAAC,CACjE,CAAC,EACO,CAAC,cAGrB7Q,IAAA,CAACS,WAAW,EAACE,aAAa,CAAEA,aAAc,CAACC,cAAc,CAAEA,cAAe,CAE7D,CAAC,cAGdV,KAAA,CAACN,kBAAkB,EACjB4M,QAAQ,CAAC,OAAO,CAChBsF,SAAS,CAAElR,cAAe,CAC1BmR,UAAU,CAAEA,CAAA,GAAMhP,iBAAiB,CAAC,CAACnC,cAAc,CAAE,CAAAyQ,QAAA,eAGrDrR,IAAA,CAACa,QAAQ,EAAC8J,KAAK,CAAC,0BAAM,CAACqH,QAAQ,CAAE,KAAM,CAAClR,MAAM,CAAC,KAAK,CAAAuQ,QAAA,cAClDrR,IAAA,CAAChB,KAAK,EACJgS,UAAU,CAAE7P,QAAS,CACrBoR,OAAO,CAAE5B,cAAe,CACxB6B,MAAM,CAAC,IAAI,CACXC,UAAU,CAAE,KAAM,CAClBrK,IAAI,CAAC,OAAO,CACZsK,MAAM,CAAE,CAAEC,CAAC,CAAE,GAAI,CAAE,CACnBC,KAAK,CAAGC,MAAM,GAAM,CAClBC,OAAO,CAAEA,CAAA,GAAMrC,mBAAmB,CAACoC,MAAM,CAAC,CAC1C1B,KAAK,CAAE,CACL4B,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,CAAAlR,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEiE,EAAE,IAAK8M,MAAM,CAAC9M,EAAE,CAAG,SAAS,CAAG,aAAa,CACzEkF,QAAQ,CAAE,MAAM,CAChBmG,OAAO,CAAE,SACX,CACF,CAAC,CAAE,CACJ,CAAC,CACM,CAAC,cAGXpR,IAAA,CAACa,QAAQ,EAAC8J,KAAK,CAAC,sCAAQ,CAACqH,QAAQ,CAAE,KAAM,CAAClR,MAAM,CAAC,KAAK,CAAAuQ,QAAA,CACnDvP,eAAe,cACd5B,KAAA,CAACjB,YAAY,EACX+S,QAAQ,MACRiB,MAAM,CAAE,CAAE,CACV7K,IAAI,CAAC,OAAO,CACZ8K,MAAM,CAAE,CACN3G,KAAK,CAAE,CAAEtB,QAAQ,CAAE,MAAM,CAAEmG,OAAO,CAAE,SAAU,CAAC,CAC/C+B,OAAO,CAAE,CAAElI,QAAQ,CAAE,MAAM,CAAEmG,OAAO,CAAE,SAAU,CAClD,CAAE,CAAAC,QAAA,eAEFrR,IAAA,CAACf,YAAY,CAACiS,IAAI,EAAC3E,KAAK,CAAC,oBAAK,CAAA8E,QAAA,CAAEvP,eAAe,CAACsE,WAAW,CAAoB,CAAC,cAChFpG,IAAA,CAACf,YAAY,CAACiS,IAAI,EAAC3E,KAAK,CAAC,cAAI,CAAA8E,QAAA,cAC3BrR,IAAA,CAACb,KAAK,EACJoE,MAAM,CAAEzB,eAAe,CAACyB,MAAM,GAAK,QAAQ,CAAG,SAAS,CAAG,OAAQ,CAClEqH,IAAI,CAAE9I,eAAe,CAACyB,MAAM,GAAK,QAAQ,CAAG,IAAI,CAAG,IAAK,CACzD,CAAC,CACe,CAAC,cACpBvD,IAAA,CAACf,YAAY,CAACiS,IAAI,EAAC3E,KAAK,CAAC,cAAI,CAAA8E,QAAA,CAC1BvP,eAAe,CAACyB,MAAM,GAAK,QAAQ,CAAGzB,eAAe,CAAC+B,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAC5D,CAAC,cACpBjE,IAAA,CAACf,YAAY,CAACiS,IAAI,EAAC3E,KAAK,CAAC,cAAI,CAAA8E,QAAA,CAC1BvP,eAAe,CAACyB,MAAM,GAAK,QAAQ,CAAGzB,eAAe,CAAC8B,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAC5D,CAAC,cACpBjE,IAAA,CAACf,YAAY,CAACiS,IAAI,EAAC3E,KAAK,CAAC,cAAI,CAAA8E,QAAA,CAC1BvP,eAAe,CAACyB,MAAM,GAAK,QAAQ,CAClC,GAAG,MAAO,CAAAzB,eAAe,CAAC0B,KAAK,GAAK,QAAQ,CAAG1B,eAAe,CAAC0B,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAAGnC,eAAe,CAAC0B,KAAK,OAAO,CAC9G,KAAK,CACU,CAAC,cACpBxD,IAAA,CAACf,YAAY,CAACiS,IAAI,EAAC3E,KAAK,CAAC,oBAAK,CAAA8E,QAAA,CAC3BvP,eAAe,CAACyB,MAAM,GAAK,QAAQ,CAClC,GAAG,MAAO,CAAAzB,eAAe,CAACgC,OAAO,GAAK,QAAQ,CAAGhC,eAAe,CAACgC,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,CAAGnC,eAAe,CAACgC,OAAO,GAAG,CAChH,KAAK,CACU,CAAC,EACR,CAAC,cAEf9D,IAAA,MAAGmR,KAAK,CAAE,CAAElG,QAAQ,CAAE,MAAO,CAAE,CAAAoG,QAAA,CAAC,oEAAW,CAAG,CAC/C,CACO,CAAC,EACO,CAAC,EACR,CAAC,CACZ,CAAC,CAEX,CAAC,CAED,cAAe,CAAArQ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}