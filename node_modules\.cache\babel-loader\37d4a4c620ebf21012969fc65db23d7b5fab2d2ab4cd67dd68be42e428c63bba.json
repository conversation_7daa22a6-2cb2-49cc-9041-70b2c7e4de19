{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\RoadMonitoring.jsx\",\n  _s = $RefreshSig$();\n// src/pages/RoadMonitoring.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Card, List, Descriptions, Spin, Badge, Row, Col } from 'antd';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport devicesData from '../data/devices.json';\nimport { FullscreenOutlined } from '@ant-design/icons';\n\n// 添加立即打印，检查导入是否成功\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconsole.log('DevicesData import check:', devicesData);\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\n_c = PageContainer;\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 24px' : props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 0' : props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\n_c2 = MainContent;\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 视频容器\n_c3 = InfoCard;\nconst VideoContainer = styled.div`\n  background: #000;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  margin-bottom: 10px;\n  \n  &:before {\n    content: \"\";\n    display: block;\n    padding-top: 56.25%; // 16:9 宽高比\n  }\n`;\n\n// 添加全屏按钮样式\n_c4 = VideoContainer;\nconst FullscreenButton = styled.div`\n  position: absolute;\n  right: 8px;\n  bottom: 8px;\n  color: white;\n  background-color: rgba(0, 0, 0, 0.5);\n  padding: 4px;\n  border-radius: 4px;\n  cursor: pointer;\n  z-index: 10;\n  transition: all 0.3s;\n\n  &:hover {\n    background-color: rgba(0, 0, 0, 0.7);\n  }\n`;\n\n// 视频占位符\n_c5 = FullscreenButton;\nconst VideoPlaceholder = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 13px;\n`;\n_c6 = VideoPlaceholder;\nconst RoadMonitoring = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [intersections, setIntersections] = useState([]);\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  // 修改 useEffect 部分\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        // 使用导入的 devicesData\n        console.log('Imported devicesData:', devicesData);\n\n        // 确保我们访问的是 devices 数组\n        const devicesArray = devicesData.devices || [];\n        if (devicesArray.length === 0) {\n          throw new Error('No devices found in the data');\n        }\n        console.log('Processing devices array:', devicesArray);\n\n        // 根据 location 对设备进行分组\n        const groupedDevices = devicesArray.reduce((acc, device) => {\n          const location = device.location;\n          if (!location) {\n            console.warn('Device missing location:', device);\n            return acc;\n          }\n          if (!acc[location]) {\n            acc[location] = {\n              id: location,\n              name: location,\n              description: `${location}监控点`,\n              status: '正常',\n              lastUpdate: device.createdAt || new Date().toISOString(),\n              devices: []\n            };\n          }\n\n          // 使用设备原始名称而不是生成新的名称\n          acc[location].devices.push({\n            type: device.type,\n            id: device.id,\n            name: device.name,\n            status: device.status\n          });\n          return acc;\n        }, {});\n        const intersectionsData = Object.values(groupedDevices);\n        console.log('Final processed intersections:', intersectionsData);\n        if (intersectionsData.length === 0) {\n          throw new Error('No intersections processed from the data');\n        } else {\n          setIntersections(intersectionsData);\n          setSelectedIntersection(intersectionsData[0]);\n        }\n      } catch (error) {\n        console.error('Error loading devices data:', error);\n        setIntersections([{\n          id: 'error',\n          name: '数据加载错误',\n          description: `无法加载设备数据: ${error.message}`,\n          status: '错误',\n          lastUpdate: new Date().toLocaleString(),\n          devices: []\n        }]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadData();\n  }, []);\n\n  // 处理路口选择\n  const handleIntersectionSelect = intersection => {\n    setSelectedIntersection(intersection);\n  };\n\n  // 修改 getDeviceSummary 函数以支持更多设备类型\n  const getDeviceSummary = devices => {\n    const summary = {};\n    devices.forEach(device => {\n      const type = device.type;\n      summary[type] = (summary[type] || 0) + 1;\n    });\n    return Object.entries(summary).map(([type, count]) => {\n      const typeNames = {\n        'camera': '摄像头',\n        'rsu': 'RSU',\n        'mmwave_radar': '毫米波雷达',\n        'edge_computing': '边缘计算单元',\n        'lidar': '激光雷达'\n      };\n      return `${typeNames[type] || type}: ${count}`;\n    }).join(', ');\n  };\n\n  // 修改 getCameras 函数以匹配实际的设备类型\n  const getCameras = devices => {\n    return devices.filter(device => device.type === 'camera');\n  };\n\n  // 添加全屏处理函数\n  const handleFullscreen = videoContainer => {\n    if (!videoContainer) return;\n    if (document.fullscreenElement) {\n      document.exitFullscreen();\n    } else {\n      try {\n        videoContainer.requestFullscreen();\n      } catch (error) {\n        console.error('Error attempting to enable fullscreen:', error);\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    spinning: loading,\n    tip: \"\\u52A0\\u8F7D\\u4E2D...\",\n    children: /*#__PURE__*/_jsxDEV(PageContainer, {\n      children: [/*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"left\",\n        collapsed: leftCollapsed,\n        onCollapse: () => setLeftCollapsed(!leftCollapsed),\n        children: /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8DEF\\u53E3\\u4FE1\\u606F\\u5217\\u8868\",\n          bordered: false,\n          height: \"100%\",\n          children: /*#__PURE__*/_jsxDEV(List, {\n            itemLayout: \"vertical\",\n            dataSource: intersections,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              onClick: () => handleIntersectionSelect(item),\n              style: {\n                cursor: 'pointer',\n                background: (selectedIntersection === null || selectedIntersection === void 0 ? void 0 : selectedIntersection.id) === item.id ? '#e6f7ff' : 'transparent',\n                padding: '8px',\n                borderRadius: '4px',\n                marginBottom: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '14px',\n                    fontWeight: 'bold'\n                  },\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 28\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px'\n                  },\n                  children: item.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 34\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '12px',\n                  marginTop: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u8BBE\\u5907\\u914D\\u7F6E\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 26\n                  }, this), \" \", getDeviceSummary(item.devices)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '4px'\n                  },\n                  children: item.devices.map(device => /*#__PURE__*/_jsxDEV(Badge, {\n                    status: device.status === 'online' ? 'success' : 'error',\n                    text: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '12px',\n                        marginRight: '8px'\n                      },\n                      children: device.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 29\n                    }, this),\n                    style: {\n                      display: 'inline-block',\n                      marginRight: '8px'\n                    }\n                  }, device.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        leftCollapsed: leftCollapsed,\n        rightCollapsed: rightCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"right\",\n        collapsed: rightCollapsed,\n        onCollapse: () => setRightCollapsed(!rightCollapsed),\n        children: /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: `视频监控 - ${(selectedIntersection === null || selectedIntersection === void 0 ? void 0 : selectedIntersection.name) || ''}`,\n          bordered: false,\n          height: \"100%\",\n          children: selectedIntersection ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '12px',\n                fontSize: '13px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Descriptions, {\n                size: \"small\",\n                column: 1,\n                styles: {\n                  label: {\n                    fontSize: '13px'\n                  },\n                  content: {\n                    fontSize: '13px'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                  label: \"\\u8DEF\\u53E3\\u540D\\u79F0\",\n                  children: selectedIntersection.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                  label: \"\\u8DEF\\u53E3\\u63CF\\u8FF0\",\n                  children: selectedIntersection.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                  label: \"\\u6444\\u50CF\\u5934\\u6570\\u91CF\",\n                  children: getCameras(selectedIntersection.devices).length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 17\n            }, this), getCameras(selectedIntersection.devices).slice(0, 4).map(camera => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '10px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '13px',\n                  marginBottom: '4px',\n                  fontWeight: 'bold'\n                },\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  status: camera.status === 'online' ? 'success' : 'error',\n                  text: camera.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(VideoContainer, {\n                children: [/*#__PURE__*/_jsxDEV(VideoPlaceholder, {\n                  children: camera.status === 'online' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        textAlign: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: \"\\u89C6\\u9891\\u6D41\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 355,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '11px',\n                          marginTop: '4px'\n                        },\n                        children: \"(\\u5B9E\\u9645\\u9879\\u76EE\\u4E2D\\u8FD9\\u91CC\\u4F1A\\u663E\\u793A\\u5B9E\\u65F6\\u89C6\\u9891)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 356,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: \"\\u6444\\u50CF\\u5934\\u79BB\\u7EBF\"\n                  }, void 0, false)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 351,\n                  columnNumber: 23\n                }, this), camera.status === 'online' && /*#__PURE__*/_jsxDEV(FullscreenButton, {\n                  onClick: e => {\n                    e.stopPropagation();\n                    handleFullscreen(e.currentTarget.parentElement);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(FullscreenOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 21\n              }, this)]\n            }, camera.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 19\n            }, this)), getCameras(selectedIntersection.devices).length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: '20px 0'\n              },\n              children: \"\\u8BE5\\u8DEF\\u53E3\\u6CA1\\u6709\\u914D\\u7F6E\\u6444\\u50CF\\u5934\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '20px 0'\n            },\n            children: \"\\u8BF7\\u9009\\u62E9\\u4E00\\u4E2A\\u8DEF\\u53E3\\u67E5\\u770B\\u89C6\\u9891\\u76D1\\u63A7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 258,\n    columnNumber: 5\n  }, this);\n};\n_s(RoadMonitoring, \"mRMMpGGs4EM8Z7alHD7AKBWx1lg=\");\n_c7 = RoadMonitoring;\nexport default RoadMonitoring;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"InfoCard\");\n$RefreshReg$(_c4, \"VideoContainer\");\n$RefreshReg$(_c5, \"FullscreenButton\");\n$RefreshReg$(_c6, \"VideoPlaceholder\");\n$RefreshReg$(_c7, \"RoadMonitoring\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "List", "Descriptions", "Spin", "Badge", "Row", "Col", "styled", "CollapsibleSidebar", "devicesData", "FullscreenOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "div", "_c", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "_c2", "InfoCard", "height", "_c3", "VideoContainer", "_c4", "FullscreenButton", "_c5", "VideoPlaceholder", "_c6", "RoadMonitoring", "_s", "loading", "setLoading", "intersections", "setIntersections", "selectedIntersection", "setSelectedIntersection", "setLeftCollapsed", "setRightCollapsed", "loadData", "devicesArray", "devices", "length", "Error", "groupedDevices", "reduce", "acc", "device", "location", "warn", "id", "name", "description", "status", "lastUpdate", "createdAt", "Date", "toISOString", "push", "type", "intersectionsData", "Object", "values", "error", "message", "toLocaleString", "handleIntersectionSelect", "intersection", "getDeviceSummary", "summary", "for<PERSON>ach", "entries", "map", "count", "typeNames", "join", "getCameras", "filter", "handleFullscreen", "videoContainer", "document", "fullscreenElement", "exitFullscreen", "requestFullscreen", "spinning", "tip", "children", "position", "collapsed", "onCollapse", "title", "bordered", "itemLayout", "dataSource", "renderItem", "item", "<PERSON><PERSON>", "onClick", "style", "cursor", "background", "padding", "borderRadius", "marginBottom", "Meta", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "text", "marginRight", "display", "size", "column", "styles", "label", "content", "slice", "camera", "textAlign", "e", "stopPropagation", "currentTarget", "parentElement", "_c7", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RoadMonitoring.jsx"], "sourcesContent": ["// src/pages/RoadMonitoring.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Card, List, Descriptions, Spin, Badge, Row, Col } from 'antd';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport devicesData from '../data/devices.json';\nimport { FullscreenOutlined } from '@ant-design/icons';\n\n// 添加立即打印，检查导入是否成功\nconsole.log('DevicesData import check:', devicesData);\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 24px' : \n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 0' : \n    props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 视频容器\nconst VideoContainer = styled.div`\n  background: #000;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  margin-bottom: 10px;\n  \n  &:before {\n    content: \"\";\n    display: block;\n    padding-top: 56.25%; // 16:9 宽高比\n  }\n`;\n\n// 添加全屏按钮样式\nconst FullscreenButton = styled.div`\n  position: absolute;\n  right: 8px;\n  bottom: 8px;\n  color: white;\n  background-color: rgba(0, 0, 0, 0.5);\n  padding: 4px;\n  border-radius: 4px;\n  cursor: pointer;\n  z-index: 10;\n  transition: all 0.3s;\n\n  &:hover {\n    background-color: rgba(0, 0, 0, 0.7);\n  }\n`;\n\n// 视频占位符\nconst VideoPlaceholder = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 13px;\n`;\n\nconst RoadMonitoring = () => {\n  const [loading, setLoading] = useState(true);\n  const [intersections, setIntersections] = useState([]);\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n  \n  // 修改 useEffect 部分\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        // 使用导入的 devicesData\n        console.log('Imported devicesData:', devicesData);\n        \n        // 确保我们访问的是 devices 数组\n        const devicesArray = devicesData.devices || [];\n        \n        if (devicesArray.length === 0) {\n          throw new Error('No devices found in the data');\n        }\n        \n        console.log('Processing devices array:', devicesArray);\n        \n        // 根据 location 对设备进行分组\n        const groupedDevices = devicesArray.reduce((acc, device) => {\n          const location = device.location;\n          if (!location) {\n            console.warn('Device missing location:', device);\n            return acc;\n          }\n\n          if (!acc[location]) {\n            acc[location] = {\n              id: location,\n              name: location,\n              description: `${location}监控点`,\n              status: '正常',\n              lastUpdate: device.createdAt || new Date().toISOString(),\n              devices: []\n            };\n          }\n\n          // 使用设备原始名称而不是生成新的名称\n          acc[location].devices.push({\n            type: device.type,\n            id: device.id,\n            name: device.name,\n            status: device.status\n          });\n\n          return acc;\n        }, {});\n\n        const intersectionsData = Object.values(groupedDevices);\n        console.log('Final processed intersections:', intersectionsData);\n\n        if (intersectionsData.length === 0) {\n          throw new Error('No intersections processed from the data');\n        } else {\n          setIntersections(intersectionsData);\n          setSelectedIntersection(intersectionsData[0]);\n        }\n      } catch (error) {\n        console.error('Error loading devices data:', error);\n        setIntersections([{\n          id: 'error',\n          name: '数据加载错误',\n          description: `无法加载设备数据: ${error.message}`,\n          status: '错误',\n          lastUpdate: new Date().toLocaleString(),\n          devices: []\n        }]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n  \n  // 处理路口选择\n  const handleIntersectionSelect = (intersection) => {\n    setSelectedIntersection(intersection);\n  };\n  \n  // 修改 getDeviceSummary 函数以支持更多设备类型\n  const getDeviceSummary = (devices) => {\n    const summary = {};\n    devices.forEach(device => {\n      const type = device.type;\n      summary[type] = (summary[type] || 0) + 1;\n    });\n    \n    return Object.entries(summary).map(([type, count]) => {\n      const typeNames = {\n        'camera': '摄像头',\n        'rsu': 'RSU',\n        'mmwave_radar': '毫米波雷达',\n        'edge_computing': '边缘计算单元',\n        'lidar': '激光雷达'\n      };\n      \n      return `${typeNames[type] || type}: ${count}`;\n    }).join(', ');\n  };\n  \n  // 修改 getCameras 函数以匹配实际的设备类型\n  const getCameras = (devices) => {\n    return devices.filter(device => device.type === 'camera');\n  };\n  \n  // 添加全屏处理函数\n  const handleFullscreen = (videoContainer) => {\n    if (!videoContainer) return;\n\n    if (document.fullscreenElement) {\n      document.exitFullscreen();\n    } else {\n      try {\n        videoContainer.requestFullscreen();\n      } catch (error) {\n        console.error('Error attempting to enable fullscreen:', error);\n      }\n    }\n  };\n  \n  return (\n    <Spin spinning={loading} tip=\"加载中...\">\n      <PageContainer>\n        {/* 左侧信息栏 */}\n        <CollapsibleSidebar \n          position=\"left\"\n          collapsed={leftCollapsed}\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\n        >\n          <InfoCard title=\"路口信息列表\" bordered={false} height=\"100%\">\n            <List\n              itemLayout=\"vertical\"\n              dataSource={intersections}\n              renderItem={item => (\n                <List.Item\n                  onClick={() => handleIntersectionSelect(item)}\n                  style={{ \n                    cursor: 'pointer',\n                    background: selectedIntersection?.id === item.id ? '#e6f7ff' : 'transparent',\n                    padding: '8px',\n                    borderRadius: '4px',\n                    marginBottom: '8px'\n                  }}\n                >\n                  <List.Item.Meta\n                    title={<span style={{ fontSize: '14px', fontWeight: 'bold' }}>{item.name}</span>}\n                    description={<span style={{ fontSize: '12px' }}>{item.description}</span>}\n                  />\n                  <div style={{ fontSize: '12px', marginTop: '4px' }}>\n                    <div><strong>设备配置：</strong> {getDeviceSummary(item.devices)}</div>\n                    <div style={{ marginTop: '4px' }}>\n                      {item.devices.map(device => (\n                        <Badge \n                          key={device.id}\n                          status={device.status === 'online' ? 'success' : 'error'} \n                          text={\n                            <span style={{ fontSize: '12px', marginRight: '8px' }}>\n                              {device.name}\n                            </span>\n                          }\n                          style={{ display: 'inline-block', marginRight: '8px' }}\n                        />\n                      ))}\n                    </div>\n                  </div>\n                </List.Item>\n              )}\n            />\n          </InfoCard>\n        </CollapsibleSidebar>\n        \n        {/* 主内容区域 */}\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\n          {/* 主要内容 */}\n        </MainContent>\n        \n        {/* 右侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"right\"\n          collapsed={rightCollapsed}\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\n        >\n          <InfoCard \n            title={`视频监控 - ${selectedIntersection?.name || ''}`} \n            bordered={false} \n            height=\"100%\"\n          >\n            {selectedIntersection ? (\n              <>\n                <div style={{ marginBottom: '12px', fontSize: '13px' }}>\n                  <Descriptions \n                    size=\"small\" \n                    column={1}\n                    styles={{\n                      label: { fontSize: '13px' },\n                      content: { fontSize: '13px' }\n                    }}\n                  >\n                    <Descriptions.Item label=\"路口名称\">{selectedIntersection.name}</Descriptions.Item>\n                    <Descriptions.Item label=\"路口描述\">{selectedIntersection.description}</Descriptions.Item>\n                    <Descriptions.Item label=\"摄像头数量\">{getCameras(selectedIntersection.devices).length}</Descriptions.Item>\n                  </Descriptions>\n                </div>\n                \n                {/* 修改视频显示部分 */}\n                {getCameras(selectedIntersection.devices).slice(0, 4).map(camera => (\n                  <div key={camera.id} style={{ marginBottom: '10px' }}>\n                    <div style={{ fontSize: '13px', marginBottom: '4px', fontWeight: 'bold' }}>\n                      <Badge \n                        status={camera.status === 'online' ? 'success' : 'error'} \n                        text={camera.name}\n                      />\n                    </div>\n                    <VideoContainer>\n                      <VideoPlaceholder>\n                        {camera.status === 'online' ? (\n                          <>\n                            <div style={{ textAlign: 'center' }}>\n                              <div>视频流</div>\n                              <div style={{ fontSize: '11px', marginTop: '4px' }}>\n                                (实际项目中这里会显示实时视频)\n                              </div>\n                            </div>\n                          </>\n                        ) : (\n                          <>摄像头离线</>\n                        )}\n                      </VideoPlaceholder>\n                      {camera.status === 'online' && (\n                        <FullscreenButton\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            handleFullscreen(e.currentTarget.parentElement);\n                          }}\n                        >\n                          <FullscreenOutlined />\n                        </FullscreenButton>\n                      )}\n                    </VideoContainer>\n                  </div>\n                ))}\n                \n                {getCameras(selectedIntersection.devices).length === 0 && (\n                  <div style={{ textAlign: 'center', padding: '20px 0' }}>\n                    该路口没有配置摄像头\n                  </div>\n                )}\n              </>\n            ) : (\n              <div style={{ textAlign: 'center', padding: '20px 0' }}>\n                请选择一个路口查看视频监控\n              </div>\n            )}\n          </InfoCard>\n        </CollapsibleSidebar>\n      </PageContainer>\n    </Spin>\n  );\n};\n\nexport default RoadMonitoring;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,IAAI,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,QAAQ,MAAM;AACtE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,yCAAyC;AACxE,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,SAASC,kBAAkB,QAAQ,mBAAmB;;AAEtD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACAC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEP,WAAW,CAAC;;AAErD;AACA,MAAMQ,aAAa,GAAGV,MAAM,CAACW,GAAG;AAChC;AACA;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GANMF,aAAa;AAOnB,MAAMG,WAAW,GAAGb,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMG,YAAY,GAAGd,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMI,WAAW,GAAGf,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA,aAAaK,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACxEF,KAAK,CAACC,aAAa,GAAG,cAAc,GACpCD,KAAK,CAACE,cAAc,GAAG,cAAc,GAAG,OAAO;AACnD;AACA,YAAYF,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACvEF,KAAK,CAACC,aAAa,GAAG,WAAW,GACjCD,KAAK,CAACE,cAAc,GAAG,WAAW,GAAG,GAAG;AAC5C;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfMJ,WAAW;AAgBjB,MAAMK,QAAQ,GAAGpB,MAAM,CAACP,IAAI,CAAC;AAC7B;AACA,YAAYuB,KAAK,IAAIA,KAAK,CAACK,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GA1BMF,QAAQ;AA2Bd,MAAMG,cAAc,GAAGvB,MAAM,CAACW,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAa,GAAA,GAdMD,cAAc;AAepB,MAAME,gBAAgB,GAAGzB,MAAM,CAACW,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAe,GAAA,GAjBMD,gBAAgB;AAkBtB,MAAME,gBAAgB,GAAG3B,MAAM,CAACW,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAXID,gBAAgB;AAatB,MAAME,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAAC0B,aAAa,EAAEoB,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2B,cAAc,EAAEoB,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACAC,SAAS,CAAC,MAAM;IACd,MAAM+C,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACF;QACA/B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEP,WAAW,CAAC;;QAEjD;QACA,MAAMsC,YAAY,GAAGtC,WAAW,CAACuC,OAAO,IAAI,EAAE;QAE9C,IAAID,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;UAC7B,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;QACjD;QAEAnC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE+B,YAAY,CAAC;;QAEtD;QACA,MAAMI,cAAc,GAAGJ,YAAY,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAK;UAC1D,MAAMC,QAAQ,GAAGD,MAAM,CAACC,QAAQ;UAChC,IAAI,CAACA,QAAQ,EAAE;YACbxC,OAAO,CAACyC,IAAI,CAAC,0BAA0B,EAAEF,MAAM,CAAC;YAChD,OAAOD,GAAG;UACZ;UAEA,IAAI,CAACA,GAAG,CAACE,QAAQ,CAAC,EAAE;YAClBF,GAAG,CAACE,QAAQ,CAAC,GAAG;cACdE,EAAE,EAAEF,QAAQ;cACZG,IAAI,EAAEH,QAAQ;cACdI,WAAW,EAAE,GAAGJ,QAAQ,KAAK;cAC7BK,MAAM,EAAE,IAAI;cACZC,UAAU,EAAEP,MAAM,CAACQ,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;cACxDhB,OAAO,EAAE;YACX,CAAC;UACH;;UAEA;UACAK,GAAG,CAACE,QAAQ,CAAC,CAACP,OAAO,CAACiB,IAAI,CAAC;YACzBC,IAAI,EAAEZ,MAAM,CAACY,IAAI;YACjBT,EAAE,EAAEH,MAAM,CAACG,EAAE;YACbC,IAAI,EAAEJ,MAAM,CAACI,IAAI;YACjBE,MAAM,EAAEN,MAAM,CAACM;UACjB,CAAC,CAAC;UAEF,OAAOP,GAAG;QACZ,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,MAAMc,iBAAiB,GAAGC,MAAM,CAACC,MAAM,CAAClB,cAAc,CAAC;QACvDpC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEmD,iBAAiB,CAAC;QAEhE,IAAIA,iBAAiB,CAAClB,MAAM,KAAK,CAAC,EAAE;UAClC,MAAM,IAAIC,KAAK,CAAC,0CAA0C,CAAC;QAC7D,CAAC,MAAM;UACLT,gBAAgB,CAAC0B,iBAAiB,CAAC;UACnCxB,uBAAuB,CAACwB,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC/C;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdvD,OAAO,CAACuD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD7B,gBAAgB,CAAC,CAAC;UAChBgB,EAAE,EAAE,OAAO;UACXC,IAAI,EAAE,QAAQ;UACdC,WAAW,EAAE,aAAaW,KAAK,CAACC,OAAO,EAAE;UACzCX,MAAM,EAAE,IAAI;UACZC,UAAU,EAAE,IAAIE,IAAI,CAAC,CAAC,CAACS,cAAc,CAAC,CAAC;UACvCxB,OAAO,EAAE;QACX,CAAC,CAAC,CAAC;MACL,CAAC,SAAS;QACRT,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDO,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2B,wBAAwB,GAAIC,YAAY,IAAK;IACjD/B,uBAAuB,CAAC+B,YAAY,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAI3B,OAAO,IAAK;IACpC,MAAM4B,OAAO,GAAG,CAAC,CAAC;IAClB5B,OAAO,CAAC6B,OAAO,CAACvB,MAAM,IAAI;MACxB,MAAMY,IAAI,GAAGZ,MAAM,CAACY,IAAI;MACxBU,OAAO,CAACV,IAAI,CAAC,GAAG,CAACU,OAAO,CAACV,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1C,CAAC,CAAC;IAEF,OAAOE,MAAM,CAACU,OAAO,CAACF,OAAO,CAAC,CAACG,GAAG,CAAC,CAAC,CAACb,IAAI,EAAEc,KAAK,CAAC,KAAK;MACpD,MAAMC,SAAS,GAAG;QAChB,QAAQ,EAAE,KAAK;QACf,KAAK,EAAE,KAAK;QACZ,cAAc,EAAE,OAAO;QACvB,gBAAgB,EAAE,QAAQ;QAC1B,OAAO,EAAE;MACX,CAAC;MAED,OAAO,GAAGA,SAAS,CAACf,IAAI,CAAC,IAAIA,IAAI,KAAKc,KAAK,EAAE;IAC/C,CAAC,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;EACf,CAAC;;EAED;EACA,MAAMC,UAAU,GAAInC,OAAO,IAAK;IAC9B,OAAOA,OAAO,CAACoC,MAAM,CAAC9B,MAAM,IAAIA,MAAM,CAACY,IAAI,KAAK,QAAQ,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMmB,gBAAgB,GAAIC,cAAc,IAAK;IAC3C,IAAI,CAACA,cAAc,EAAE;IAErB,IAAIC,QAAQ,CAACC,iBAAiB,EAAE;MAC9BD,QAAQ,CAACE,cAAc,CAAC,CAAC;IAC3B,CAAC,MAAM;MACL,IAAI;QACFH,cAAc,CAACI,iBAAiB,CAAC,CAAC;MACpC,CAAC,CAAC,OAAOpB,KAAK,EAAE;QACdvD,OAAO,CAACuD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;IACF;EACF,CAAC;EAED,oBACE1D,OAAA,CAACT,IAAI;IAACwF,QAAQ,EAAErD,OAAQ;IAACsD,GAAG,EAAC,uBAAQ;IAAAC,QAAA,eACnCjF,OAAA,CAACK,aAAa;MAAA4E,QAAA,gBAEZjF,OAAA,CAACJ,kBAAkB;QACjBsF,QAAQ,EAAC,MAAM;QACfC,SAAS,EAAEvE,aAAc;QACzBwE,UAAU,EAAEA,CAAA,KAAMpD,gBAAgB,CAAC,CAACpB,aAAa,CAAE;QAAAqE,QAAA,eAEnDjF,OAAA,CAACe,QAAQ;UAACsE,KAAK,EAAC,sCAAQ;UAACC,QAAQ,EAAE,KAAM;UAACtE,MAAM,EAAC,MAAM;UAAAiE,QAAA,eACrDjF,OAAA,CAACX,IAAI;YACHkG,UAAU,EAAC,UAAU;YACrBC,UAAU,EAAE5D,aAAc;YAC1B6D,UAAU,EAAEC,IAAI,iBACd1F,OAAA,CAACX,IAAI,CAACsG,IAAI;cACRC,OAAO,EAAEA,CAAA,KAAM/B,wBAAwB,CAAC6B,IAAI,CAAE;cAC9CG,KAAK,EAAE;gBACLC,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,CAAAjE,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEe,EAAE,MAAK6C,IAAI,CAAC7C,EAAE,GAAG,SAAS,GAAG,aAAa;gBAC5EmD,OAAO,EAAE,KAAK;gBACdC,YAAY,EAAE,KAAK;gBACnBC,YAAY,EAAE;cAChB,CAAE;cAAAjB,QAAA,gBAEFjF,OAAA,CAACX,IAAI,CAACsG,IAAI,CAACQ,IAAI;gBACbd,KAAK,eAAErF,OAAA;kBAAM6F,KAAK,EAAE;oBAAEO,QAAQ,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAO,CAAE;kBAAApB,QAAA,EAAES,IAAI,CAAC5C;gBAAI;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAE;gBACjF1D,WAAW,eAAE/C,OAAA;kBAAM6F,KAAK,EAAE;oBAAEO,QAAQ,EAAE;kBAAO,CAAE;kBAAAnB,QAAA,EAAES,IAAI,CAAC3C;gBAAW;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC,eACFzG,OAAA;gBAAK6F,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEM,SAAS,EAAE;gBAAM,CAAE;gBAAAzB,QAAA,gBACjDjF,OAAA;kBAAAiF,QAAA,gBAAKjF,OAAA;oBAAAiF,QAAA,EAAQ;kBAAK;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC1C,gBAAgB,CAAC2B,IAAI,CAACtD,OAAO,CAAC;gBAAA;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClEzG,OAAA;kBAAK6F,KAAK,EAAE;oBAAEa,SAAS,EAAE;kBAAM,CAAE;kBAAAzB,QAAA,EAC9BS,IAAI,CAACtD,OAAO,CAAC+B,GAAG,CAACzB,MAAM,iBACtB1C,OAAA,CAACR,KAAK;oBAEJwD,MAAM,EAAEN,MAAM,CAACM,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;oBACzD2D,IAAI,eACF3G,OAAA;sBAAM6F,KAAK,EAAE;wBAAEO,QAAQ,EAAE,MAAM;wBAAEQ,WAAW,EAAE;sBAAM,CAAE;sBAAA3B,QAAA,EACnDvC,MAAM,CAACI;oBAAI;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CACP;oBACDZ,KAAK,EAAE;sBAAEgB,OAAO,EAAE,cAAc;sBAAED,WAAW,EAAE;oBAAM;kBAAE,GAPlDlE,MAAM,CAACG,EAAE;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQf,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGrBzG,OAAA,CAACU,WAAW;QAACE,aAAa,EAAEA,aAAc;QAACC,cAAc,EAAEA;MAAe;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC,eAGdzG,OAAA,CAACJ,kBAAkB;QACjBsF,QAAQ,EAAC,OAAO;QAChBC,SAAS,EAAEtE,cAAe;QAC1BuE,UAAU,EAAEA,CAAA,KAAMnD,iBAAiB,CAAC,CAACpB,cAAc,CAAE;QAAAoE,QAAA,eAErDjF,OAAA,CAACe,QAAQ;UACPsE,KAAK,EAAE,UAAU,CAAAvD,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEgB,IAAI,KAAI,EAAE,EAAG;UACpDwC,QAAQ,EAAE,KAAM;UAChBtE,MAAM,EAAC,MAAM;UAAAiE,QAAA,EAEZnD,oBAAoB,gBACnB9B,OAAA,CAAAE,SAAA;YAAA+E,QAAA,gBACEjF,OAAA;cAAK6F,KAAK,EAAE;gBAAEK,YAAY,EAAE,MAAM;gBAAEE,QAAQ,EAAE;cAAO,CAAE;cAAAnB,QAAA,eACrDjF,OAAA,CAACV,YAAY;gBACXwH,IAAI,EAAC,OAAO;gBACZC,MAAM,EAAE,CAAE;gBACVC,MAAM,EAAE;kBACNC,KAAK,EAAE;oBAAEb,QAAQ,EAAE;kBAAO,CAAC;kBAC3Bc,OAAO,EAAE;oBAAEd,QAAQ,EAAE;kBAAO;gBAC9B,CAAE;gBAAAnB,QAAA,gBAEFjF,OAAA,CAACV,YAAY,CAACqG,IAAI;kBAACsB,KAAK,EAAC,0BAAM;kBAAAhC,QAAA,EAAEnD,oBAAoB,CAACgB;gBAAI;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eAC/EzG,OAAA,CAACV,YAAY,CAACqG,IAAI;kBAACsB,KAAK,EAAC,0BAAM;kBAAAhC,QAAA,EAAEnD,oBAAoB,CAACiB;gBAAW;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eACtFzG,OAAA,CAACV,YAAY,CAACqG,IAAI;kBAACsB,KAAK,EAAC,gCAAO;kBAAAhC,QAAA,EAAEV,UAAU,CAACzC,oBAAoB,CAACM,OAAO,CAAC,CAACC;gBAAM;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,EAGLlC,UAAU,CAACzC,oBAAoB,CAACM,OAAO,CAAC,CAAC+E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChD,GAAG,CAACiD,MAAM,iBAC9DpH,OAAA;cAAqB6F,KAAK,EAAE;gBAAEK,YAAY,EAAE;cAAO,CAAE;cAAAjB,QAAA,gBACnDjF,OAAA;gBAAK6F,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEF,YAAY,EAAE,KAAK;kBAAEG,UAAU,EAAE;gBAAO,CAAE;gBAAApB,QAAA,eACxEjF,OAAA,CAACR,KAAK;kBACJwD,MAAM,EAAEoE,MAAM,CAACpE,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;kBACzD2D,IAAI,EAAES,MAAM,CAACtE;gBAAK;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNzG,OAAA,CAACkB,cAAc;gBAAA+D,QAAA,gBACbjF,OAAA,CAACsB,gBAAgB;kBAAA2D,QAAA,EACdmC,MAAM,CAACpE,MAAM,KAAK,QAAQ,gBACzBhD,OAAA,CAAAE,SAAA;oBAAA+E,QAAA,eACEjF,OAAA;sBAAK6F,KAAK,EAAE;wBAAEwB,SAAS,EAAE;sBAAS,CAAE;sBAAApC,QAAA,gBAClCjF,OAAA;wBAAAiF,QAAA,EAAK;sBAAG;wBAAAqB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACdzG,OAAA;wBAAK6F,KAAK,EAAE;0BAAEO,QAAQ,EAAE,MAAM;0BAAEM,SAAS,EAAE;wBAAM,CAAE;wBAAAzB,QAAA,EAAC;sBAEpD;wBAAAqB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,gBACN,CAAC,gBAEHzG,OAAA,CAAAE,SAAA;oBAAA+E,QAAA,EAAE;kBAAK,gBAAE;gBACV;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACe,CAAC,EAClBW,MAAM,CAACpE,MAAM,KAAK,QAAQ,iBACzBhD,OAAA,CAACoB,gBAAgB;kBACfwE,OAAO,EAAG0B,CAAC,IAAK;oBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;oBACnB9C,gBAAgB,CAAC6C,CAAC,CAACE,aAAa,CAACC,aAAa,CAAC;kBACjD,CAAE;kBAAAxC,QAAA,eAEFjF,OAAA,CAACF,kBAAkB;oBAAAwG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACnB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACa,CAAC;YAAA,GAhCTW,MAAM,CAACvE,EAAE;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiCd,CACN,CAAC,EAEDlC,UAAU,CAACzC,oBAAoB,CAACM,OAAO,CAAC,CAACC,MAAM,KAAK,CAAC,iBACpDrC,OAAA;cAAK6F,KAAK,EAAE;gBAAEwB,SAAS,EAAE,QAAQ;gBAAErB,OAAO,EAAE;cAAS,CAAE;cAAAf,QAAA,EAAC;YAExD;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA,eACD,CAAC,gBAEHzG,OAAA;YAAK6F,KAAK,EAAE;cAAEwB,SAAS,EAAE,QAAQ;cAAErB,OAAO,EAAE;YAAS,CAAE;YAAAf,QAAA,EAAC;UAExD;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEX,CAAC;AAAChF,EAAA,CA1QID,cAAc;AAAAkG,GAAA,GAAdlG,cAAc;AA4QpB,eAAeA,cAAc;AAAC,IAAAjB,EAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAmG,GAAA;AAAAC,YAAA,CAAApH,EAAA;AAAAoH,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}