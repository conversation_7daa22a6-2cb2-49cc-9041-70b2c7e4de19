{"ast": null, "code": "import { FastColor } from '@ant-design/fast-color';\nimport { initComponentToken } from '../../input/style/token';\nimport { getArrowToken } from '../../style/roundedArrow';\nexport const initPickerPanelToken = token => {\n  const {\n    componentCls,\n    controlHeightLG,\n    paddingXXS,\n    padding\n  } = token;\n  return {\n    pickerCellCls: `${componentCls}-cell`,\n    pickerCellInnerCls: `${componentCls}-cell-inner`,\n    pickerYearMonthCellWidth: token.calc(controlHeightLG).mul(1.5).equal(),\n    pickerQuarterPanelContentHeight: token.calc(controlHeightLG).mul(1.4).equal(),\n    pickerCellPaddingVertical: token.calc(paddingXXS).add(token.calc(paddingXXS).div(2)).equal(),\n    pickerCellBorderGap: 2,\n    // Magic for gap between cells\n    pickerControlIconSize: 7,\n    pickerControlIconMargin: 4,\n    pickerControlIconBorderWidth: 1.5,\n    pickerDatePanelPaddingHorizontal: token.calc(padding).add(token.calc(paddingXXS).div(2)).equal() // 18 in normal\n  };\n};\nexport const initPanelComponentToken = token => {\n  const {\n    colorBgContainerDisabled,\n    controlHeight,\n    controlHeightSM,\n    controlHeightLG,\n    paddingXXS,\n    lineWidth\n  } = token;\n  // Item height default use `controlHeight - 2 * paddingXXS`,\n  // but some case `paddingXXS=0`.\n  // Let's fallback it.\n  const dblPaddingXXS = paddingXXS * 2;\n  const dblLineWidth = lineWidth * 2;\n  const multipleItemHeight = Math.min(controlHeight - dblPaddingXXS, controlHeight - dblLineWidth);\n  const multipleItemHeightSM = Math.min(controlHeightSM - dblPaddingXXS, controlHeightSM - dblLineWidth);\n  const multipleItemHeightLG = Math.min(controlHeightLG - dblPaddingXXS, controlHeightLG - dblLineWidth);\n  // FIXED_ITEM_MARGIN is a hardcode calculation since calc not support rounding\n  const INTERNAL_FIXED_ITEM_MARGIN = Math.floor(paddingXXS / 2);\n  const filledToken = {\n    INTERNAL_FIXED_ITEM_MARGIN,\n    cellHoverBg: token.controlItemBgHover,\n    cellActiveWithRangeBg: token.controlItemBgActive,\n    cellHoverWithRangeBg: new FastColor(token.colorPrimary).lighten(35).toHexString(),\n    cellRangeBorderColor: new FastColor(token.colorPrimary).lighten(20).toHexString(),\n    cellBgDisabled: colorBgContainerDisabled,\n    timeColumnWidth: controlHeightLG * 1.4,\n    timeColumnHeight: 28 * 8,\n    timeCellHeight: 28,\n    cellWidth: controlHeightSM * 1.5,\n    cellHeight: controlHeightSM,\n    textHeight: controlHeightLG,\n    withoutTimeCellHeight: controlHeightLG * 1.65,\n    multipleItemBg: token.colorFillSecondary,\n    multipleItemBorderColor: 'transparent',\n    multipleItemHeight,\n    multipleItemHeightSM,\n    multipleItemHeightLG,\n    multipleSelectorBgDisabled: colorBgContainerDisabled,\n    multipleItemColorDisabled: token.colorTextDisabled,\n    multipleItemBorderColorDisabled: 'transparent'\n  };\n  return filledToken;\n};\nexport const prepareComponentToken = token => Object.assign(Object.assign(Object.assign(Object.assign({}, initComponentToken(token)), initPanelComponentToken(token)), getArrowToken(token)), {\n  presetsWidth: 120,\n  presetsMaxWidth: 200,\n  zIndexPopup: token.zIndexPopupBase + 50\n});", "map": {"version": 3, "names": ["FastColor", "initComponentToken", "getArrowToken", "initPickerPanelToken", "token", "componentCls", "controlHeightLG", "paddingXXS", "padding", "pickerCellCls", "pickerCellInnerCls", "pickerYearMonthCell<PERSON>th", "calc", "mul", "equal", "pickerQuarterPanelContentHeight", "pickerCellPaddingVertical", "add", "div", "pickerCellBorderGap", "pickerControlIconSize", "pickerControlIconMargin", "pickerControlIconBorderWidth", "pickerDatePanelPaddingHorizontal", "initPanelComponentToken", "colorBgContainerDisabled", "controlHeight", "controlHeightSM", "lineWidth", "dblPaddingXXS", "dbl<PERSON><PERSON><PERSON><PERSON><PERSON>", "multipleItemHeight", "Math", "min", "multipleItemHeightSM", "multipleItemHeightLG", "INTERNAL_FIXED_ITEM_MARGIN", "floor", "filledToken", "cellHoverBg", "controlItemBgHover", "cellActiveWithRangeBg", "controlItemBgActive", "cellHoverWithRangeBg", "colorPrimary", "lighten", "toHexString", "cellRangeBorderColor", "cellBgDisabled", "timeColumn<PERSON><PERSON><PERSON>", "timeColumnHeight", "timeCellHeight", "cellWidth", "cellHeight", "textHeight", "withoutTimeCellHeight", "multipleItemBg", "colorFillSecondary", "multipleItemBorderColor", "multipleSelectorBgDisabled", "multipleItemColorDisabled", "colorTextDisabled", "multipleItemBorderColorDisabled", "prepareComponentToken", "Object", "assign", "presetsWidth", "presetsMaxWidth", "zIndexPopup", "zIndexPopupBase"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/date-picker/style/token.js"], "sourcesContent": ["import { FastColor } from '@ant-design/fast-color';\nimport { initComponentToken } from '../../input/style/token';\nimport { getArrowToken } from '../../style/roundedArrow';\nexport const initPickerPanelToken = token => {\n  const {\n    componentCls,\n    controlHeightLG,\n    paddingXXS,\n    padding\n  } = token;\n  return {\n    pickerCellCls: `${componentCls}-cell`,\n    pickerCellInnerCls: `${componentCls}-cell-inner`,\n    pickerYearMonthCellWidth: token.calc(controlHeightLG).mul(1.5).equal(),\n    pickerQuarterPanelContentHeight: token.calc(controlHeightLG).mul(1.4).equal(),\n    pickerCellPaddingVertical: token.calc(paddingXXS).add(token.calc(paddingXXS).div(2)).equal(),\n    pickerCellBorderGap: 2,\n    // Magic for gap between cells\n    pickerControlIconSize: 7,\n    pickerControlIconMargin: 4,\n    pickerControlIconBorderWidth: 1.5,\n    pickerDatePanelPaddingHorizontal: token.calc(padding).add(token.calc(paddingXXS).div(2)).equal() // 18 in normal\n  };\n};\nexport const initPanelComponentToken = token => {\n  const {\n    colorBgContainerDisabled,\n    controlHeight,\n    controlHeightSM,\n    controlHeightLG,\n    paddingXXS,\n    lineWidth\n  } = token;\n  // Item height default use `controlHeight - 2 * paddingXXS`,\n  // but some case `paddingXXS=0`.\n  // Let's fallback it.\n  const dblPaddingXXS = paddingXXS * 2;\n  const dblLineWidth = lineWidth * 2;\n  const multipleItemHeight = Math.min(controlHeight - dblPaddingXXS, controlHeight - dblLineWidth);\n  const multipleItemHeightSM = Math.min(controlHeightSM - dblPaddingXXS, controlHeightSM - dblLineWidth);\n  const multipleItemHeightLG = Math.min(controlHeightLG - dblPaddingXXS, controlHeightLG - dblLineWidth);\n  // FIXED_ITEM_MARGIN is a hardcode calculation since calc not support rounding\n  const INTERNAL_FIXED_ITEM_MARGIN = Math.floor(paddingXXS / 2);\n  const filledToken = {\n    INTERNAL_FIXED_ITEM_MARGIN,\n    cellHoverBg: token.controlItemBgHover,\n    cellActiveWithRangeBg: token.controlItemBgActive,\n    cellHoverWithRangeBg: new FastColor(token.colorPrimary).lighten(35).toHexString(),\n    cellRangeBorderColor: new FastColor(token.colorPrimary).lighten(20).toHexString(),\n    cellBgDisabled: colorBgContainerDisabled,\n    timeColumnWidth: controlHeightLG * 1.4,\n    timeColumnHeight: 28 * 8,\n    timeCellHeight: 28,\n    cellWidth: controlHeightSM * 1.5,\n    cellHeight: controlHeightSM,\n    textHeight: controlHeightLG,\n    withoutTimeCellHeight: controlHeightLG * 1.65,\n    multipleItemBg: token.colorFillSecondary,\n    multipleItemBorderColor: 'transparent',\n    multipleItemHeight,\n    multipleItemHeightSM,\n    multipleItemHeightLG,\n    multipleSelectorBgDisabled: colorBgContainerDisabled,\n    multipleItemColorDisabled: token.colorTextDisabled,\n    multipleItemBorderColorDisabled: 'transparent'\n  };\n  return filledToken;\n};\nexport const prepareComponentToken = token => Object.assign(Object.assign(Object.assign(Object.assign({}, initComponentToken(token)), initPanelComponentToken(token)), getArrowToken(token)), {\n  presetsWidth: 120,\n  presetsMaxWidth: 200,\n  zIndexPopup: token.zIndexPopupBase + 50\n});"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,aAAa,QAAQ,0BAA0B;AACxD,OAAO,MAAMC,oBAAoB,GAAGC,KAAK,IAAI;EAC3C,MAAM;IACJC,YAAY;IACZC,eAAe;IACfC,UAAU;IACVC;EACF,CAAC,GAAGJ,KAAK;EACT,OAAO;IACLK,aAAa,EAAE,GAAGJ,YAAY,OAAO;IACrCK,kBAAkB,EAAE,GAAGL,YAAY,aAAa;IAChDM,wBAAwB,EAAEP,KAAK,CAACQ,IAAI,CAACN,eAAe,CAAC,CAACO,GAAG,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC;IACtEC,+BAA+B,EAAEX,KAAK,CAACQ,IAAI,CAACN,eAAe,CAAC,CAACO,GAAG,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC;IAC7EE,yBAAyB,EAAEZ,KAAK,CAACQ,IAAI,CAACL,UAAU,CAAC,CAACU,GAAG,CAACb,KAAK,CAACQ,IAAI,CAACL,UAAU,CAAC,CAACW,GAAG,CAAC,CAAC,CAAC,CAAC,CAACJ,KAAK,CAAC,CAAC;IAC5FK,mBAAmB,EAAE,CAAC;IACtB;IACAC,qBAAqB,EAAE,CAAC;IACxBC,uBAAuB,EAAE,CAAC;IAC1BC,4BAA4B,EAAE,GAAG;IACjCC,gCAAgC,EAAEnB,KAAK,CAACQ,IAAI,CAACJ,OAAO,CAAC,CAACS,GAAG,CAACb,KAAK,CAACQ,IAAI,CAACL,UAAU,CAAC,CAACW,GAAG,CAAC,CAAC,CAAC,CAAC,CAACJ,KAAK,CAAC,CAAC,CAAC;EACnG,CAAC;AACH,CAAC;AACD,OAAO,MAAMU,uBAAuB,GAAGpB,KAAK,IAAI;EAC9C,MAAM;IACJqB,wBAAwB;IACxBC,aAAa;IACbC,eAAe;IACfrB,eAAe;IACfC,UAAU;IACVqB;EACF,CAAC,GAAGxB,KAAK;EACT;EACA;EACA;EACA,MAAMyB,aAAa,GAAGtB,UAAU,GAAG,CAAC;EACpC,MAAMuB,YAAY,GAAGF,SAAS,GAAG,CAAC;EAClC,MAAMG,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAACP,aAAa,GAAGG,aAAa,EAAEH,aAAa,GAAGI,YAAY,CAAC;EAChG,MAAMI,oBAAoB,GAAGF,IAAI,CAACC,GAAG,CAACN,eAAe,GAAGE,aAAa,EAAEF,eAAe,GAAGG,YAAY,CAAC;EACtG,MAAMK,oBAAoB,GAAGH,IAAI,CAACC,GAAG,CAAC3B,eAAe,GAAGuB,aAAa,EAAEvB,eAAe,GAAGwB,YAAY,CAAC;EACtG;EACA,MAAMM,0BAA0B,GAAGJ,IAAI,CAACK,KAAK,CAAC9B,UAAU,GAAG,CAAC,CAAC;EAC7D,MAAM+B,WAAW,GAAG;IAClBF,0BAA0B;IAC1BG,WAAW,EAAEnC,KAAK,CAACoC,kBAAkB;IACrCC,qBAAqB,EAAErC,KAAK,CAACsC,mBAAmB;IAChDC,oBAAoB,EAAE,IAAI3C,SAAS,CAACI,KAAK,CAACwC,YAAY,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;IACjFC,oBAAoB,EAAE,IAAI/C,SAAS,CAACI,KAAK,CAACwC,YAAY,CAAC,CAACC,OAAO,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;IACjFE,cAAc,EAAEvB,wBAAwB;IACxCwB,eAAe,EAAE3C,eAAe,GAAG,GAAG;IACtC4C,gBAAgB,EAAE,EAAE,GAAG,CAAC;IACxBC,cAAc,EAAE,EAAE;IAClBC,SAAS,EAAEzB,eAAe,GAAG,GAAG;IAChC0B,UAAU,EAAE1B,eAAe;IAC3B2B,UAAU,EAAEhD,eAAe;IAC3BiD,qBAAqB,EAAEjD,eAAe,GAAG,IAAI;IAC7CkD,cAAc,EAAEpD,KAAK,CAACqD,kBAAkB;IACxCC,uBAAuB,EAAE,aAAa;IACtC3B,kBAAkB;IAClBG,oBAAoB;IACpBC,oBAAoB;IACpBwB,0BAA0B,EAAElC,wBAAwB;IACpDmC,yBAAyB,EAAExD,KAAK,CAACyD,iBAAiB;IAClDC,+BAA+B,EAAE;EACnC,CAAC;EACD,OAAOxB,WAAW;AACpB,CAAC;AACD,OAAO,MAAMyB,qBAAqB,GAAG3D,KAAK,IAAI4D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhE,kBAAkB,CAACG,KAAK,CAAC,CAAC,EAAEoB,uBAAuB,CAACpB,KAAK,CAAC,CAAC,EAAEF,aAAa,CAACE,KAAK,CAAC,CAAC,EAAE;EAC5L8D,YAAY,EAAE,GAAG;EACjBC,eAAe,EAAE,GAAG;EACpBC,WAAW,EAAEhE,KAAK,CAACiE,eAAe,GAAG;AACvC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}