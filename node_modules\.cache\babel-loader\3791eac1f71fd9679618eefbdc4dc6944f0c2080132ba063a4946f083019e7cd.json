{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\DeviceStatus.jsx\",\n  _s = $RefreshSig$();\n// src/pages/DeviceStatus.jsx\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge, Tag, message, Divider, Select, Button, Space } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { PieChart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\n\n// 注册必要的echarts组件\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\necharts.use([Pie<PERSON>hart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\n_c = PageContainer;\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n  background-color: #fafafa;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 24px' : props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 0' : props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\n_c2 = MainContent;\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  box-shadow: 0 1px 3px rgba(0,0,0,0.05);\n  transition: all 0.3s ease;\n  \n  &:hover {\n    box-shadow: 0 2px 6px rgba(0,0,0,0.1);\n  }\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n    background-color: #f7f7f7;\n    border-bottom: 1px solid #eee;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n    font-weight: 600;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\n_c3 = InfoCard;\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n  \n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n_c4 = CompactStatistic;\nconst DeviceStatus = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [devices, setDevices] = useState([]);\n  const [selectedDevice, setSelectedDevice] = useState(null);\n  const [stats, setStats] = useState({\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0,\n    normalDevices: 0,\n    warningDevices: 0,\n    errorDevices: 0\n  });\n  const deviceTypeChartRef = useRef(null);\n  const deviceStatusChartRef = useRef(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  // 添加筛选相关状态\n  const [filterType, setFilterType] = useState(null);\n  const [filterValue, setFilterValue] = useState(null);\n  const [secondOptions, setSecondOptions] = useState([]);\n  const [filteredDevices, setFilteredDevices] = useState([]);\n\n  // 修改设备数据获取函数\n  const fetchDevices = async () => {\n    try {\n      setLoading(true);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      if (response.data && response.data.success && response.data.data) {\n        const devicesData = response.data.data;\n\n        // 为每个设备添加健康状态、CPU、内存和磁盘使用率\n        const processedDevices = devicesData.map(device => ({\n          ...device,\n          ip: device.ipAddress,\n          // 确保IP地址的兼容性\n          // health: Math.random() > 0.8 ? (Math.random() > 0.5 ? 'error' : 'warning') : 'normal',\n          health: 'normal',\n          cpu: Math.floor(Math.random() * 100),\n          memory: Math.floor(Math.random() * 100),\n          disk: Math.floor(Math.random() * 100),\n          lastActive: new Date(Date.now() - Math.floor(Math.random() * 3600000)).toLocaleString()\n        }));\n        setDevices(processedDevices);\n        setFilteredDevices(processedDevices); // 初始设置筛选后的设备为全部设备\n        updateCharts(processedDevices);\n\n        // 计算统计数据\n        const stats = {\n          totalDevices: processedDevices.length,\n          onlineDevices: processedDevices.filter(d => d.status === 'online').length,\n          offlineDevices: processedDevices.filter(d => d.status !== 'online').length,\n          normalDevices: processedDevices.filter(d => d.health === 'normal').length,\n          warningDevices: processedDevices.filter(d => d.health === 'warning').length,\n          errorDevices: processedDevices.filter(d => d.health === 'error').length\n        };\n        setStats(stats);\n      } else {\n        console.error('API返回错误:', response.data);\n      }\n    } catch (error) {\n      console.error('获取设备数据失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 更新图表数据的函数\n  const updateCharts = devicesData => {\n    // 设备类型分布数据\n    const typeData = {};\n    devicesData.forEach(device => {\n      const type = deviceTypeMap[device.type] || device.type;\n      typeData[type] = (typeData[type] || 0) + 1;\n    });\n\n    // 设备状态分布数据\n    const statusData = {};\n    devicesData.forEach(device => {\n      const status = deviceStatusMap[device.status] || device.status;\n      statusData[status] = (statusData[status] || 0) + 1;\n    });\n\n    // 自定义颜色方案，匹配截图样式\n    const colorPalette = ['#747474',\n    // 深灰\n    '#FF4D4F',\n    // 红色\n    '#4B96FF',\n    // 蓝色\n    '#52C41A',\n    // 绿色\n    '#FAAD14',\n    // 橙黄色\n    '#FA8C16',\n    // 橙色\n    '#722ED1',\n    // 紫色\n    '#13C2C2',\n    // 青色\n    '#F5222D',\n    // 亮红色\n    '#FADB14' // 黄色\n    ];\n\n    // 更新类型分布图表\n    if (deviceTypeChartRef.current) {\n      const typeChart = echarts.init(deviceTypeChartRef.current);\n      typeChart.setOption({\n        color: colorPalette,\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(50,50,50,0.9)',\n          borderRadius: 4,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: 'horizontal',\n          bottom: 0,\n          left: 'center',\n          itemWidth: 12,\n          itemHeight: 12,\n          textStyle: {\n            fontSize: 12\n          },\n          padding: [30, 10, 5, 10]\n        },\n        series: [{\n          name: '设备类型',\n          type: 'pie',\n          radius: ['40%', '70%'],\n          center: ['50%', '40%'],\n          avoidLabelOverlap: true,\n          itemStyle: {\n            borderRadius: 4,\n            borderColor: '#fff',\n            borderWidth: 2\n          },\n          label: {\n            show: true,\n            formatter: '{d}%',\n            position: 'inside',\n            fontSize: 12,\n            fontWeight: 'bold',\n            color: 'auto',\n            textBorderColor: 'rgba(255, 255, 255, 0.8)',\n            textBorderWidth: 2,\n            textShadow: '0 0 4px rgba(0, 0, 0, 0.5)'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: 14,\n              fontWeight: 'bold'\n            },\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: Object.entries(typeData).map(([name, value]) => ({\n            name,\n            value\n          }))\n        }]\n      });\n\n      // 保存图表实例以便窗口大小调整时使用\n      window.addEventListener('resize', () => {\n        typeChart.resize();\n      });\n    }\n\n    // 更新状态分布图表\n    if (deviceStatusChartRef.current) {\n      const statusChart = echarts.init(deviceStatusChartRef.current);\n      statusChart.setOption({\n        color: colorPalette,\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(50,50,50,0.9)',\n          borderRadius: 4,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: 'horizontal',\n          bottom: 0,\n          left: 'center',\n          itemWidth: 12,\n          itemHeight: 12,\n          textStyle: {\n            fontSize: 12\n          },\n          padding: [30, 10, 5, 10]\n        },\n        series: [{\n          name: '设备状态',\n          type: 'pie',\n          radius: ['40%', '70%'],\n          center: ['50%', '40%'],\n          avoidLabelOverlap: true,\n          itemStyle: {\n            borderRadius: 4,\n            borderColor: '#fff',\n            borderWidth: 2\n          },\n          label: {\n            show: true,\n            formatter: '{d}%',\n            position: 'inside',\n            fontSize: 12,\n            fontWeight: 'bold',\n            color: 'auto',\n            textBorderColor: 'rgba(255, 255, 255, 0.8)',\n            textBorderWidth: 2,\n            textShadow: '0 0 4px rgba(0, 0, 0, 0.5)'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: 14,\n              fontWeight: 'bold'\n            },\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: Object.entries(statusData).map(([name, value]) => ({\n            name,\n            value\n          }))\n        }]\n      });\n\n      // 保存图表实例以便窗口大小调整时使用\n      window.addEventListener('resize', () => {\n        statusChart.resize();\n      });\n    }\n  };\n\n  // 设备类型映射\n  const deviceTypeMap = {\n    camera: '摄像头',\n    mmwave_radar: '毫米波雷达',\n    lidar: '激光雷达',\n    rsu: 'RSU',\n    edge_computing: '边缘计算单元',\n    obu: 'OBU'\n  };\n\n  // 设备状态映射\n  const deviceStatusMap = {\n    'online': '在线',\n    'offline': '离线',\n    'warning': '警告',\n    'error': '错误',\n    'maintenance': '维护中'\n  };\n\n  // 添加筛选相关函数\n  const handleFirstSelectChange = value => {\n    setFilterType(value);\n    setFilterValue(null); // 重置第二个选择框的值\n\n    // 根据第一个选择框的值动态更新第二个选择框的选项\n    if (value) {\n      const uniqueValues = [...new Set(devices.map(device => device[value]))];\n      setSecondOptions(uniqueValues.filter(Boolean).sort()); // 过滤掉空值并排序\n    } else {\n      setSecondOptions([]);\n    }\n  };\n  const handleSecondSelectChange = value => {\n    setFilterValue(value);\n  };\n  const handleFilterSearch = () => {\n    if (!filterType || !filterValue) {\n      // 如果未选择筛选条件，显示全部设备\n      setFilteredDevices(devices);\n    } else {\n      // 根据筛选条件过滤设备\n      const filtered = devices.filter(device => device[filterType] === filterValue);\n      setFilteredDevices(filtered);\n    }\n  };\n  const handleResetFilter = () => {\n    setFilterType(null);\n    setFilterValue(null);\n    setSecondOptions([]);\n    setFilteredDevices(devices);\n  };\n\n  // 获取第二个选择框的选项标签\n  const getSecondSelectOptions = () => {\n    if (!filterType) return [];\n    return secondOptions.map(option => {\n      // 如果筛选条件是设备类型，使用deviceTypeMap进行映射显示\n      if (filterType === 'type') {\n        return {\n          value: option,\n          label: deviceTypeMap[option] || option\n        };\n      }\n      return {\n        value: option,\n        label: option\n      };\n    });\n  };\n\n  // 在组件挂载时获取设备数据\n  useEffect(() => {\n    fetchDevices();\n  }, []);\n\n  // 添加自动选择第一个设备的逻辑，使用filteredDevices替代devices\n  useEffect(() => {\n    if (filteredDevices.length > 0 && !selectedDevice) {\n      setSelectedDevice(filteredDevices[0]);\n      console.log('已自动选择第一个设备:', filteredDevices[0].name);\n    }\n  }, [filteredDevices, selectedDevice]);\n\n  // 处理设备选择\n  const handleDeviceSelect = device => {\n    setSelectedDevice(device);\n  };\n\n  // 设备列表列定义\n  const deviceColumns = [{\n    title: '设备名称',\n    dataIndex: 'name',\n    key: 'name',\n    width: '50%'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: '25%',\n    render: status => /*#__PURE__*/_jsxDEV(Badge, {\n      status: status === 'online' ? 'success' : 'error',\n      text: status === 'online' ? '在线' : '离线'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '健康度',\n    dataIndex: 'health',\n    key: 'health',\n    width: '25%',\n    render: health => {\n      let color = 'green';\n      let text = '正常';\n      if (health === 'warning') {\n        color = 'orange';\n        text = '警告';\n      } else if (health === 'error') {\n        color = 'red';\n        text = '错误';\n      }\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: color,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 16\n      }, this);\n    }\n  }];\n\n  // 获取健康状态颜色\n  const getHealthColor = value => {\n    if (value >= 80) return '#f5222d'; // 红色\n    if (value >= 60) return '#faad14'; // 黄色\n    return '#52c41a'; // 绿色\n  };\n\n  // 添加对echarts实例的清理\n  useEffect(() => {\n    let typeChart = null;\n    let statusChart = null;\n    if (deviceTypeChartRef.current) {\n      typeChart = echarts.init(deviceTypeChartRef.current);\n    }\n    if (deviceStatusChartRef.current) {\n      statusChart = echarts.init(deviceStatusChartRef.current);\n    }\n    const handleResize = () => {\n      var _typeChart, _statusChart;\n      (_typeChart = typeChart) === null || _typeChart === void 0 ? void 0 : _typeChart.resize();\n      (_statusChart = statusChart) === null || _statusChart === void 0 ? void 0 : _statusChart.resize();\n    };\n    window.addEventListener('resize', handleResize);\n    return () => {\n      var _typeChart2, _statusChart2;\n      window.removeEventListener('resize', handleResize);\n      (_typeChart2 = typeChart) === null || _typeChart2 === void 0 ? void 0 : _typeChart2.dispose();\n      (_statusChart2 = statusChart) === null || _statusChart2 === void 0 ? void 0 : _statusChart2.dispose();\n    };\n  }, []);\n\n  // 添加全局样式到文档头\n  useEffect(() => {\n    const styleElement = document.createElement('style');\n    styleElement.innerHTML = globalStyles;\n    document.head.appendChild(styleElement);\n    return () => {\n      document.head.removeChild(styleElement);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    spinning: loading,\n    tip: \"\\u52A0\\u8F7D\\u4E2D...\",\n    children: /*#__PURE__*/_jsxDEV(PageContainer, {\n      children: [/*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"left\",\n        collapsed: leftCollapsed,\n        onCollapse: () => setLeftCollapsed(!leftCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8BBE\\u5907\\u603B\\u6570\\u7EDF\\u8BA1\",\n          bordered: false,\n          height: \"150px\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [8, 8],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u8BBE\\u5907\\u603B\\u6570\",\n                value: stats.totalDevices,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u5728\\u7EBF\\u8BBE\\u5907\",\n                value: stats.onlineDevices,\n                suffix: `/ ${stats.totalDevices}`,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u79BB\\u7EBF\\u8BBE\\u5907\",\n                value: stats.offlineDevices,\n                suffix: `/ ${stats.totalDevices}`,\n                valueStyle: {\n                  color: '#cf1322'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u6B63\\u5E38\\u8BBE\\u5907\",\n                value: stats.normalDevices,\n                suffix: `/ ${stats.totalDevices}`,\n                valueStyle: {\n                  color: '#52c41a'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u8B66\\u544A\\u8BBE\\u5907\",\n                value: stats.warningDevices,\n                suffix: `/ ${stats.totalDevices}`,\n                valueStyle: {\n                  color: '#faad14'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u9519\\u8BEF\\u8BBE\\u5907\",\n                value: stats.errorDevices,\n                suffix: `/ ${stats.totalDevices}`,\n                valueStyle: {\n                  color: '#f5222d'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8BBE\\u5907\\u7C7B\\u578B\\u5206\\u5E03\",\n          bordered: false,\n          height: \"calc(50% - 81px)\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: deviceTypeChartRef,\n            style: {\n              height: '100%',\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8BBE\\u5907\\u72B6\\u6001\\u5206\\u5E03\",\n          bordered: false,\n          height: \"calc(50% - 81px)\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: deviceStatusChartRef,\n            style: {\n              height: '100%',\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        leftCollapsed: leftCollapsed,\n        rightCollapsed: rightCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"right\",\n        collapsed: rightCollapsed,\n        onCollapse: () => setRightCollapsed(!rightCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8BBE\\u5907\\u5217\\u8868\",\n          bordered: false,\n          height: \"50%\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1,\n                overflow: 'auto'\n              },\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                dataSource: filteredDevices,\n                columns: deviceColumns,\n                rowKey: \"id\",\n                pagination: false,\n                size: \"small\",\n                scroll: {\n                  y: true\n                },\n                onRow: record => ({\n                  onClick: () => handleDeviceSelect(record),\n                  style: {\n                    cursor: 'pointer',\n                    background: (selectedDevice === null || selectedDevice === void 0 ? void 0 : selectedDevice.id) === record.id ? '#e6f7ff' : 'transparent',\n                    fontSize: '13px',\n                    padding: '4px 8px'\n                  }\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '8px',\n                padding: '8px 0',\n                borderTop: '1px solid #f0f0f0',\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                flexWrap: 'wrap',\n                gap: '4px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Space, {\n                size: \"small\",\n                style: {\n                  flexWrap: 'wrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u7B5B\\u9009\\u6761\\u4EF6\",\n                  style: {\n                    width: '100px'\n                  },\n                  size: \"small\",\n                  value: filterType,\n                  onChange: handleFirstSelectChange,\n                  allowClear: true,\n                  options: [{\n                    value: 'type',\n                    label: '设备类型'\n                  }, {\n                    value: 'location',\n                    label: '位置'\n                  }]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u7B5B\\u9009\\u503C\",\n                  style: {\n                    width: '120px'\n                  },\n                  size: \"small\",\n                  value: filterValue,\n                  onChange: handleSecondSelectChange,\n                  disabled: !filterType || secondOptions.length === 0,\n                  allowClear: true,\n                  options: getSecondSelectOptions()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  size: \"small\",\n                  onClick: handleFilterSearch,\n                  children: \"\\u67E5\\u8BE2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  onClick: handleResetFilter,\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '12px',\n                  color: '#999'\n                },\n                children: [filteredDevices.length, \" / \", devices.length, \" \\u4E2A\\u8BBE\\u5907\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8BBE\\u5907\\u8BE6\\u7EC6\\u4FE1\\u606F\",\n          bordered: false,\n          height: \"50%\",\n          children: selectedDevice ? /*#__PURE__*/_jsxDEV(Descriptions, {\n            bordered: true,\n            column: 1,\n            size: \"small\",\n            styles: {\n              label: {\n                fontSize: '13px',\n                padding: '4px 8px'\n              },\n              content: {\n                fontSize: '13px',\n                padding: '4px 8px'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8BBE\\u5907\\u540D\\u79F0\",\n              children: selectedDevice.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 718,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8BBE\\u5907\\u7C7B\\u578B\",\n              children: deviceTypeMap[selectedDevice.type] || selectedDevice.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                status: selectedDevice.status === 'online' ? 'success' : 'error',\n                text: selectedDevice.status === 'online' ? '在线' : '离线'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u4F4D\\u7F6E\",\n              children: selectedDevice.location\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"IP\\u5730\\u5740\",\n              children: selectedDevice.ip\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6700\\u540E\\u6D3B\\u52A8\\u65F6\\u95F4\",\n              children: selectedDevice.lastActive\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 17\n            }, this), selectedDevice.status === 'online' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                label: \"CPU\\u4F7F\\u7528\\u7387\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '100px',\n                      height: '8px',\n                      background: '#f0f0f0',\n                      borderRadius: '4px',\n                      overflow: 'hidden',\n                      marginRight: '8px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: `${selectedDevice.cpu}%`,\n                        height: '100%',\n                        background: getHealthColor(selectedDevice.cpu),\n                        borderRadius: '4px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 743,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [selectedDevice.cpu, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 750,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                label: \"\\u5185\\u5B58\\u4F7F\\u7528\\u7387\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '100px',\n                      height: '8px',\n                      background: '#f0f0f0',\n                      borderRadius: '4px',\n                      overflow: 'hidden',\n                      marginRight: '8px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: `${selectedDevice.memory}%`,\n                        height: '100%',\n                        background: getHealthColor(selectedDevice.memory),\n                        borderRadius: '4px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 763,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 755,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [selectedDevice.memory, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 754,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                label: \"\\u78C1\\u76D8\\u4F7F\\u7528\\u7387\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '100px',\n                      height: '8px',\n                      background: '#f0f0f0',\n                      borderRadius: '4px',\n                      overflow: 'hidden',\n                      marginRight: '8px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: `${selectedDevice.disk}%`,\n                        height: '100%',\n                        background: getHealthColor(selectedDevice.disk),\n                        borderRadius: '4px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 783,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 775,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [selectedDevice.disk, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 790,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '13px'\n            },\n            children: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u67E5\\u770B\\u8BE6\\u7EC6\\u4FE1\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 797,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 537,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 536,\n    columnNumber: 5\n  }, this);\n};\n_s(DeviceStatus, \"rgxNK0y1HN2A9NMVWwrQZMeG78A=\");\n_c5 = DeviceStatus;\nconst styles = {\n  /* ... existing styles ... */\n};\n\n// 添加全局样式\nconst globalStyles = `\n  .device-status-container {\n    display: flex;\n    height: 100%;\n  }\n  \n  .device-sidebar {\n    width: 320px;\n    padding: 16px;\n    background-color: #f5f5f5;\n    overflow-y: auto;\n    border-right: 1px solid #e8e8e8;\n  }\n  \n  .device-content {\n    flex: 1;\n    padding: 16px;\n    overflow-y: auto;\n  }\n  \n  .ant-card-head-title {\n    font-weight: bold;\n    font-size: 16px;\n  }\n`;\nexport default DeviceStatus;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"InfoCard\");\n$RefreshReg$(_c4, \"CompactStatistic\");\n$RefreshReg$(_c5, \"DeviceStatus\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Row", "Col", "Card", "Statistic", "List", "Table", "Descriptions", "Spin", "Badge", "Tag", "message", "Divider", "Select", "<PERSON><PERSON>", "Space", "echarts", "<PERSON><PERSON><PERSON>", "GridComponent", "TooltipComponent", "LegendComponent", "TitleComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "CollapsibleSidebar", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "use", "<PERSON><PERSON><PERSON><PERSON>", "div", "_c", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "_c2", "InfoCard", "height", "_c3", "CompactStatistic", "_c4", "DeviceStatus", "_s", "loading", "setLoading", "devices", "setDevices", "selected<PERSON><PERSON><PERSON>", "setSelectedDevice", "stats", "setStats", "totalDevices", "onlineDevices", "offlineDevices", "normalDevices", "warningDevices", "errorDevices", "deviceTypeChartRef", "deviceStatusChartRef", "setLeftCollapsed", "setRightCollapsed", "filterType", "setFilterType", "filterValue", "setFilterValue", "secondOptions", "setSecondOptions", "filteredDevices", "setFilteredDevices", "fetchDevices", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "data", "success", "devicesData", "processedDevices", "map", "device", "ip", "ip<PERSON><PERSON><PERSON>", "health", "cpu", "Math", "floor", "random", "memory", "disk", "lastActive", "Date", "now", "toLocaleString", "updateCharts", "length", "filter", "d", "status", "console", "error", "typeData", "for<PERSON>ach", "type", "deviceTypeMap", "statusData", "deviceStatusMap", "colorPalette", "current", "typeChart", "init", "setOption", "color", "tooltip", "trigger", "formatter", "backgroundColor", "borderRadius", "textStyle", "legend", "orient", "bottom", "left", "itemWidth", "itemHeight", "fontSize", "padding", "series", "name", "radius", "center", "avoidLabelOverlap", "itemStyle", "borderColor", "borderWidth", "label", "show", "position", "fontWeight", "textBorderColor", "textBorder<PERSON>idth", "textShadow", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "labelLine", "Object", "entries", "value", "window", "addEventListener", "resize", "statusChart", "camera", "mmwave_radar", "lidar", "rsu", "edge_computing", "obu", "handleFirstSelectChange", "uniqueValues", "Set", "Boolean", "sort", "handleSecondSelectChange", "handleFilterSearch", "filtered", "handleResetFilter", "getSecondSelectOptions", "option", "log", "handleDeviceSelect", "deviceColumns", "title", "dataIndex", "key", "width", "render", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "getHealthColor", "handleResize", "_typeChart", "_statusChart", "_typeChart2", "_statusChart2", "removeEventListener", "dispose", "styleElement", "document", "createElement", "innerHTML", "globalStyles", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "spinning", "tip", "collapsed", "onCollapse", "bordered", "gutter", "span", "valueStyle", "suffix", "ref", "style", "display", "flexDirection", "flex", "overflow", "dataSource", "columns", "<PERSON><PERSON><PERSON>", "pagination", "size", "scroll", "y", "onRow", "record", "onClick", "cursor", "background", "id", "marginTop", "borderTop", "justifyContent", "alignItems", "flexWrap", "gap", "placeholder", "onChange", "allowClear", "options", "disabled", "column", "styles", "content", "<PERSON><PERSON>", "location", "marginRight", "_c5", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/DeviceStatus.jsx"], "sourcesContent": ["// src/pages/DeviceStatus.jsx\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge, Tag, message, Divider, Select, Button, Space } from 'antd';\r\nimport * as echarts from 'echarts/core';\r\nimport { <PERSON><PERSON><PERSON> } from 'echarts/charts';\r\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\r\nimport { CanvasRenderer } from 'echarts/renderers';\r\nimport styled from 'styled-components';\r\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\r\nimport axios from 'axios';\r\n\r\n// 注册必要的echarts组件\r\necharts.use([Pie<PERSON>hart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\r\n\r\n// 页面布局容器\r\nconst PageContainer = styled.div`\r\n  display: flex;\r\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\r\n  overflow: hidden;\r\n`;\r\n\r\n// 左侧信息栏容器\r\nconst LeftSidebar = styled.div`\r\n  width: 320px;\r\n  height: 100%;\r\n  padding: 0 8px 0 0;\r\n  border-right: 1px solid #f0f0f0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background-color: #fafafa;\r\n`;\r\n\r\n// 右侧信息栏容器\r\nconst RightSidebar = styled.div`\r\n  width: 320px;\r\n  height: 100%;\r\n  padding: 0 0 0 8px;\r\n  border-left: 1px solid #f0f0f0;\r\n  display: flex;\r\n  flex-direction: column;\r\n`;\r\n\r\n// 主内容区域\r\nconst MainContent = styled.div`\r\n  flex: 1;\r\n  height: 100%;\r\n  overflow: hidden;\r\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \r\n    props.leftCollapsed ? '0 8px 0 24px' : \r\n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\r\n  transition: all 0.3s ease;\r\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \r\n    props.leftCollapsed ? '0 8px 0 0' : \r\n    props.rightCollapsed ? '0 0 0 8px' : '0'};\r\n  position: relative;\r\n  z-index: 1;\r\n`;\r\n\r\n// 信息卡片\r\nconst InfoCard = styled(Card)`\r\n  margin-bottom: 12px;\r\n  height: ${props => props.height || 'auto'};\r\n  box-shadow: 0 1px 3px rgba(0,0,0,0.05);\r\n  transition: all 0.3s ease;\r\n  \r\n  &:hover {\r\n    box-shadow: 0 2px 6px rgba(0,0,0,0.1);\r\n  }\r\n  \r\n  .ant-card-head {\r\n    min-height: 40px;\r\n    padding: 0 12px;\r\n    background-color: #f7f7f7;\r\n    border-bottom: 1px solid #eee;\r\n  }\r\n  \r\n  .ant-card-head-title {\r\n    padding: 8px 0;\r\n    font-size: 14px;\r\n    font-weight: 600;\r\n  }\r\n  \r\n  .ant-card-body {\r\n    padding: 12px;\r\n    font-size: 13px;\r\n    height: calc(100% - 40px); // 减去卡片头部高度\r\n    overflow-y: auto;\r\n  }\r\n  \r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n`;\r\n\r\n// 自定义统计数字组件\r\nconst CompactStatistic = styled(Statistic)`\r\n  .ant-statistic-title {\r\n    font-size: 12px;\r\n    margin-bottom: 2px;\r\n  }\r\n  \r\n  .ant-statistic-content {\r\n    font-size: 16px;\r\n  }\r\n`;\r\n\r\nconst DeviceStatus = () => {\r\n  const [loading, setLoading] = useState(true);\r\n  const [devices, setDevices] = useState([]);\r\n  const [selectedDevice, setSelectedDevice] = useState(null);\r\n  const [stats, setStats] = useState({\r\n    totalDevices: 0,\r\n    onlineDevices: 0,\r\n    offlineDevices: 0,\r\n    normalDevices: 0,\r\n    warningDevices: 0,\r\n    errorDevices: 0\r\n  });\r\n  \r\n  const deviceTypeChartRef = useRef(null);\r\n  const deviceStatusChartRef = useRef(null);\r\n  \r\n  // 添加侧边栏折叠状态\r\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\r\n  const [rightCollapsed, setRightCollapsed] = useState(false);\r\n  \r\n  // 添加筛选相关状态\r\n  const [filterType, setFilterType] = useState(null);\r\n  const [filterValue, setFilterValue] = useState(null);\r\n  const [secondOptions, setSecondOptions] = useState([]);\r\n  const [filteredDevices, setFilteredDevices] = useState([]);\r\n  \r\n  // 修改设备数据获取函数\r\n  const fetchDevices = async () => {\r\n    try {\r\n      setLoading(true);\r\n      \r\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n      const response = await axios.get(`${apiUrl}/api/devices`);\r\n      \r\n      if (response.data && response.data.success && response.data.data) {\r\n        const devicesData = response.data.data;\r\n        \r\n        // 为每个设备添加健康状态、CPU、内存和磁盘使用率\r\n        const processedDevices = devicesData.map(device => ({\r\n          ...device,\r\n          ip: device.ipAddress, // 确保IP地址的兼容性\r\n          // health: Math.random() > 0.8 ? (Math.random() > 0.5 ? 'error' : 'warning') : 'normal',\r\n          health: 'normal',\r\n          cpu: Math.floor(Math.random() * 100),\r\n          memory: Math.floor(Math.random() * 100),\r\n          disk: Math.floor(Math.random() * 100),\r\n          lastActive: new Date(Date.now() - Math.floor(Math.random() * 3600000)).toLocaleString()\r\n        }));\r\n        \r\n        setDevices(processedDevices);\r\n        setFilteredDevices(processedDevices); // 初始设置筛选后的设备为全部设备\r\n        updateCharts(processedDevices);\r\n        \r\n        // 计算统计数据\r\n        const stats = {\r\n          totalDevices: processedDevices.length,\r\n          onlineDevices: processedDevices.filter(d => d.status === 'online').length,\r\n          offlineDevices: processedDevices.filter(d => d.status !== 'online').length,\r\n          normalDevices: processedDevices.filter(d => d.health === 'normal').length,\r\n          warningDevices: processedDevices.filter(d => d.health === 'warning').length,\r\n          errorDevices: processedDevices.filter(d => d.health === 'error').length\r\n        };\r\n        \r\n        setStats(stats);\r\n      } else {\r\n        console.error('API返回错误:', response.data);\r\n      }\r\n    } catch (error) {\r\n      console.error('获取设备数据失败:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 更新图表数据的函数\r\n  const updateCharts = (devicesData) => {\r\n    // 设备类型分布数据\r\n    const typeData = {};\r\n    devicesData.forEach(device => {\r\n      const type = deviceTypeMap[device.type] || device.type;\r\n      typeData[type] = (typeData[type] || 0) + 1;\r\n    });\r\n\r\n    // 设备状态分布数据\r\n    const statusData = {};\r\n    devicesData.forEach(device => {\r\n      const status = deviceStatusMap[device.status] || device.status;\r\n      statusData[status] = (statusData[status] || 0) + 1;\r\n    });\r\n\r\n    // 自定义颜色方案，匹配截图样式\r\n    const colorPalette = [\r\n      '#747474', // 深灰\r\n      '#FF4D4F', // 红色\r\n      '#4B96FF', // 蓝色\r\n      '#52C41A', // 绿色\r\n      '#FAAD14', // 橙黄色\r\n      '#FA8C16', // 橙色\r\n      '#722ED1', // 紫色\r\n      '#13C2C2', // 青色\r\n      '#F5222D', // 亮红色\r\n      '#FADB14'  // 黄色\r\n    ];\r\n\r\n    // 更新类型分布图表\r\n    if (deviceTypeChartRef.current) {\r\n      const typeChart = echarts.init(deviceTypeChartRef.current);\r\n      typeChart.setOption({\r\n        color: colorPalette,\r\n        tooltip: { \r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\r\n          backgroundColor: 'rgba(50,50,50,0.9)',\r\n          borderRadius: 4,\r\n          textStyle: {\r\n            color: '#fff'\r\n          }\r\n        },\r\n        legend: {\r\n          orient: 'horizontal',\r\n          bottom: 0,\r\n          left: 'center',\r\n          itemWidth: 12,\r\n          itemHeight: 12,\r\n          textStyle: {\r\n            fontSize: 12\r\n          },\r\n          padding: [30, 10, 5, 10]\r\n        },\r\n        series: [{\r\n          name: '设备类型',\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          center: ['50%', '40%'],\r\n          avoidLabelOverlap: true,\r\n          itemStyle: {\r\n            borderRadius: 4,\r\n            borderColor: '#fff',\r\n            borderWidth: 2\r\n          },\r\n          label: {\r\n            show: true,\r\n            formatter: '{d}%',\r\n            position: 'inside',\r\n            fontSize: 12,\r\n            fontWeight: 'bold',\r\n            color: 'auto',\r\n            textBorderColor: 'rgba(255, 255, 255, 0.8)',\r\n            textBorderWidth: 2,\r\n            textShadow: '0 0 4px rgba(0, 0, 0, 0.5)'\r\n          },\r\n          emphasis: {\r\n            label: {\r\n              show: true,\r\n              fontSize: 14,\r\n              fontWeight: 'bold'\r\n            },\r\n            itemStyle: {\r\n              shadowBlur: 10,\r\n              shadowOffsetX: 0,\r\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n            }\r\n          },\r\n          labelLine: {\r\n            show: false\r\n          },\r\n          data: Object.entries(typeData).map(([name, value]) => ({ name, value }))\r\n        }]\r\n      });\r\n      \r\n      // 保存图表实例以便窗口大小调整时使用\r\n      window.addEventListener('resize', () => {\r\n        typeChart.resize();\r\n      });\r\n    }\r\n\r\n    // 更新状态分布图表\r\n    if (deviceStatusChartRef.current) {\r\n      const statusChart = echarts.init(deviceStatusChartRef.current);\r\n      statusChart.setOption({\r\n        color: colorPalette,\r\n        tooltip: { \r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\r\n          backgroundColor: 'rgba(50,50,50,0.9)',\r\n          borderRadius: 4,\r\n          textStyle: {\r\n            color: '#fff'\r\n          }\r\n        },\r\n        legend: {\r\n          orient: 'horizontal',\r\n          bottom: 0,\r\n          left: 'center',\r\n          itemWidth: 12,\r\n          itemHeight: 12,\r\n          textStyle: {\r\n            fontSize: 12\r\n          },\r\n          padding: [30, 10, 5, 10]\r\n        },\r\n        series: [{\r\n          name: '设备状态',\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          center: ['50%', '40%'],\r\n          avoidLabelOverlap: true,\r\n          itemStyle: {\r\n            borderRadius: 4,\r\n            borderColor: '#fff',\r\n            borderWidth: 2\r\n          },\r\n          label: {\r\n            show: true,\r\n            formatter: '{d}%',\r\n            position: 'inside',\r\n            fontSize: 12,\r\n            fontWeight: 'bold',\r\n            color: 'auto',\r\n            textBorderColor: 'rgba(255, 255, 255, 0.8)',\r\n            textBorderWidth: 2,\r\n            textShadow: '0 0 4px rgba(0, 0, 0, 0.5)'\r\n          },\r\n          emphasis: {\r\n            label: {\r\n              show: true,\r\n              fontSize: 14,\r\n              fontWeight: 'bold'\r\n            },\r\n            itemStyle: {\r\n              shadowBlur: 10,\r\n              shadowOffsetX: 0,\r\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n            }\r\n          },\r\n          labelLine: {\r\n            show: false\r\n          },\r\n          data: Object.entries(statusData).map(([name, value]) => ({ name, value }))\r\n        }]\r\n      });\r\n      \r\n      // 保存图表实例以便窗口大小调整时使用\r\n      window.addEventListener('resize', () => {\r\n        statusChart.resize();\r\n      });\r\n    }\r\n  };\r\n\r\n  // 设备类型映射\r\n  const deviceTypeMap = {\r\n    camera: '摄像头',\r\n    mmwave_radar: '毫米波雷达',\r\n    lidar: '激光雷达',\r\n    rsu: 'RSU',\r\n    edge_computing: '边缘计算单元',\r\n    obu: 'OBU'\r\n  };\r\n\r\n  // 设备状态映射\r\n  const deviceStatusMap = {\r\n    'online': '在线',\r\n    'offline': '离线',\r\n    'warning': '警告',\r\n    'error': '错误',\r\n    'maintenance': '维护中'\r\n  };\r\n\r\n  // 添加筛选相关函数\r\n  const handleFirstSelectChange = (value) => {\r\n    setFilterType(value);\r\n    setFilterValue(null); // 重置第二个选择框的值\r\n    \r\n    // 根据第一个选择框的值动态更新第二个选择框的选项\r\n    if (value) {\r\n      const uniqueValues = [...new Set(devices.map(device => device[value]))];\r\n      setSecondOptions(uniqueValues.filter(Boolean).sort()); // 过滤掉空值并排序\r\n    } else {\r\n      setSecondOptions([]);\r\n    }\r\n  };\r\n\r\n  const handleSecondSelectChange = (value) => {\r\n    setFilterValue(value);\r\n  };\r\n\r\n  const handleFilterSearch = () => {\r\n    if (!filterType || !filterValue) {\r\n      // 如果未选择筛选条件，显示全部设备\r\n      setFilteredDevices(devices);\r\n    } else {\r\n      // 根据筛选条件过滤设备\r\n      const filtered = devices.filter(device => device[filterType] === filterValue);\r\n      setFilteredDevices(filtered);\r\n    }\r\n  };\r\n\r\n  const handleResetFilter = () => {\r\n    setFilterType(null);\r\n    setFilterValue(null);\r\n    setSecondOptions([]);\r\n    setFilteredDevices(devices);\r\n  };\r\n\r\n  // 获取第二个选择框的选项标签\r\n  const getSecondSelectOptions = () => {\r\n    if (!filterType) return [];\r\n    \r\n    return secondOptions.map(option => {\r\n      // 如果筛选条件是设备类型，使用deviceTypeMap进行映射显示\r\n      if (filterType === 'type') {\r\n        return { \r\n          value: option, \r\n          label: deviceTypeMap[option] || option \r\n        };\r\n      }\r\n      return { \r\n        value: option, \r\n        label: option \r\n      };\r\n    });\r\n  };\r\n\r\n  // 在组件挂载时获取设备数据\r\n  useEffect(() => {\r\n    fetchDevices();\r\n  }, []);\r\n  \r\n  // 添加自动选择第一个设备的逻辑，使用filteredDevices替代devices\r\n  useEffect(() => {\r\n    if (filteredDevices.length > 0 && !selectedDevice) {\r\n      setSelectedDevice(filteredDevices[0]);\r\n      console.log('已自动选择第一个设备:', filteredDevices[0].name);\r\n    }\r\n  }, [filteredDevices, selectedDevice]);\r\n  \r\n  // 处理设备选择\r\n  const handleDeviceSelect = (device) => {\r\n    setSelectedDevice(device);\r\n  };\r\n  \r\n  // 设备列表列定义\r\n  const deviceColumns = [\r\n    {\r\n      title: '设备名称',\r\n      dataIndex: 'name',\r\n      key: 'name',\r\n      width: '50%',\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'status',\r\n      key: 'status',\r\n      width: '25%',\r\n      render: status => (\r\n        <Badge \r\n          status={status === 'online' ? 'success' : 'error'} \r\n          text={status === 'online' ? '在线' : '离线'} \r\n        />\r\n      ),\r\n    },\r\n    {\r\n      title: '健康度',\r\n      dataIndex: 'health',\r\n      key: 'health',\r\n      width: '25%',\r\n      render: health => {\r\n        let color = 'green';\r\n        let text = '正常';\r\n        \r\n        if (health === 'warning') {\r\n          color = 'orange';\r\n          text = '警告';\r\n        } else if (health === 'error') {\r\n          color = 'red';\r\n          text = '错误';\r\n        }\r\n        \r\n        return <Tag color={color}>{text}</Tag>;\r\n      },\r\n    }\r\n  ];\r\n  \r\n  // 获取健康状态颜色\r\n  const getHealthColor = (value) => {\r\n    if (value >= 80) return '#f5222d'; // 红色\r\n    if (value >= 60) return '#faad14'; // 黄色\r\n    return '#52c41a'; // 绿色\r\n  };\r\n  \r\n  // 添加对echarts实例的清理\r\n  useEffect(() => {\r\n    let typeChart = null;\r\n    let statusChart = null;\r\n    \r\n    if (deviceTypeChartRef.current) {\r\n      typeChart = echarts.init(deviceTypeChartRef.current);\r\n    }\r\n    \r\n    if (deviceStatusChartRef.current) {\r\n      statusChart = echarts.init(deviceStatusChartRef.current);\r\n    }\r\n    \r\n    const handleResize = () => {\r\n      typeChart?.resize();\r\n      statusChart?.resize();\r\n    };\r\n    \r\n    window.addEventListener('resize', handleResize);\r\n    \r\n    return () => {\r\n      window.removeEventListener('resize', handleResize);\r\n      typeChart?.dispose();\r\n      statusChart?.dispose();\r\n    };\r\n  }, []);\r\n  \r\n  // 添加全局样式到文档头\r\n  useEffect(() => {\r\n    const styleElement = document.createElement('style');\r\n    styleElement.innerHTML = globalStyles;\r\n    document.head.appendChild(styleElement);\r\n    \r\n    return () => {\r\n      document.head.removeChild(styleElement);\r\n    };\r\n  }, []);\r\n  \r\n  return (\r\n    <Spin spinning={loading} tip=\"加载中...\">\r\n      <PageContainer>\r\n        {/* 左侧信息栏 */}\r\n        <CollapsibleSidebar \r\n          position=\"left\"\r\n          collapsed={leftCollapsed}\r\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\r\n        >\r\n          {/* 设备总数统计 */}\r\n          <InfoCard title=\"设备总数统计\" bordered={false} height=\"150px\">\r\n            <Row gutter={[8, 8]}>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"设备总数\" \r\n                  value={stats.totalDevices} \r\n                  valueStyle={{ color: '#3f8600' }} \r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"在线设备\" \r\n                  value={stats.onlineDevices} \r\n                  suffix={`/ ${stats.totalDevices}`} \r\n                  valueStyle={{ color: '#3f8600' }} \r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"离线设备\" \r\n                  value={stats.offlineDevices} \r\n                  suffix={`/ ${stats.totalDevices}`} \r\n                  valueStyle={{ color: '#cf1322' }} \r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"正常设备\" \r\n                  value={stats.normalDevices} \r\n                  suffix={`/ ${stats.totalDevices}`} \r\n                  valueStyle={{ color: '#52c41a' }} \r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"警告设备\" \r\n                  value={stats.warningDevices} \r\n                  suffix={`/ ${stats.totalDevices}`} \r\n                  valueStyle={{ color: '#faad14' }} \r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"错误设备\" \r\n                  value={stats.errorDevices} \r\n                  suffix={`/ ${stats.totalDevices}`} \r\n                  valueStyle={{ color: '#f5222d' }} \r\n                />\r\n              </Col>\r\n            </Row>\r\n          </InfoCard>\r\n          \r\n          {/* 设备类型分布 */}\r\n          <InfoCard title=\"设备类型分布\" bordered={false} height=\"calc(50% - 81px)\">\r\n            <div \r\n              ref={deviceTypeChartRef} \r\n              style={{ \r\n                height: '100%', \r\n                width: '100%'\r\n              }} \r\n            ></div>\r\n          </InfoCard>\r\n          \r\n          {/* 设备状态分布 */}\r\n          <InfoCard title=\"设备状态分布\" bordered={false} height=\"calc(50% - 81px)\">\r\n            <div \r\n              ref={deviceStatusChartRef} \r\n              style={{ \r\n                height: '100%',\r\n                width: '100%'\r\n              }} \r\n            ></div>\r\n          </InfoCard>\r\n        </CollapsibleSidebar>\r\n        \r\n        {/* 主内容区域 */}\r\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\r\n          {/* 保持空白，不显示任何内容 */}\r\n        </MainContent>\r\n        \r\n        {/* 右侧信息栏 */}\r\n        <CollapsibleSidebar\r\n          position=\"right\"\r\n          collapsed={rightCollapsed}\r\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\r\n        >\r\n          {/* 设备列表 */}\r\n          <InfoCard title=\"设备列表\" bordered={false} height=\"50%\">\r\n            <div style={{ \r\n              display: 'flex', \r\n              flexDirection: 'column', \r\n              height: '100%'\r\n            }}>\r\n              <div style={{ flex: 1, overflow: 'auto' }}>\r\n                <Table \r\n                  dataSource={filteredDevices} \r\n                  columns={deviceColumns} \r\n                  rowKey=\"id\"\r\n                  pagination={false}\r\n                  size=\"small\"\r\n                  scroll={{ y: true }}\r\n                  onRow={(record) => ({\r\n                    onClick: () => handleDeviceSelect(record),\r\n                    style: { \r\n                      cursor: 'pointer',\r\n                      background: selectedDevice?.id === record.id ? '#e6f7ff' : 'transparent',\r\n                      fontSize: '13px',\r\n                      padding: '4px 8px'\r\n                    }\r\n                  })}\r\n                />\r\n              </div>\r\n              \r\n              {/* 底部筛选区域 */}\r\n              <div style={{ \r\n                marginTop: '8px', \r\n                padding: '8px 0', \r\n                borderTop: '1px solid #f0f0f0',\r\n                display: 'flex',\r\n                justifyContent: 'space-between',\r\n                alignItems: 'center',\r\n                flexWrap: 'wrap',\r\n                gap: '4px'\r\n              }}>\r\n                <Space size=\"small\" style={{ flexWrap: 'wrap' }}>\r\n                  <Select\r\n                    placeholder=\"筛选条件\"\r\n                    style={{ width: '100px' }}\r\n                    size=\"small\"\r\n                    value={filterType}\r\n                    onChange={handleFirstSelectChange}\r\n                    allowClear\r\n                    options={[\r\n                      { value: 'type', label: '设备类型' },\r\n                      { value: 'location', label: '位置' }\r\n                    ]}\r\n                  />\r\n                  <Select\r\n                    placeholder=\"筛选值\"\r\n                    style={{ width: '120px' }}\r\n                    size=\"small\"\r\n                    value={filterValue}\r\n                    onChange={handleSecondSelectChange}\r\n                    disabled={!filterType || secondOptions.length === 0}\r\n                    allowClear\r\n                    options={getSecondSelectOptions()}\r\n                  />\r\n                  <Button type=\"primary\" size=\"small\" onClick={handleFilterSearch}>\r\n                    查询\r\n                  </Button>\r\n                  <Button size=\"small\" onClick={handleResetFilter}>\r\n                    重置\r\n                  </Button>\r\n                </Space>\r\n                <div style={{ fontSize: '12px', color: '#999' }}>\r\n                  {filteredDevices.length} / {devices.length} 个设备\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </InfoCard>\r\n          \r\n          {/* 设备详细信息 */}\r\n          <InfoCard title=\"设备详细信息\" bordered={false} height=\"50%\">\r\n            {selectedDevice ? (\r\n              <Descriptions \r\n                bordered \r\n                column={1} \r\n                size=\"small\"\r\n                styles={{\r\n                  label: { fontSize: '13px', padding: '4px 8px' },\r\n                  content: { fontSize: '13px', padding: '4px 8px' }\r\n                }}\r\n              >\r\n                <Descriptions.Item label=\"设备名称\">{selectedDevice.name}</Descriptions.Item>\r\n                <Descriptions.Item label=\"设备类型\">\r\n                  {deviceTypeMap[selectedDevice.type] || selectedDevice.type}\r\n                </Descriptions.Item>\r\n                <Descriptions.Item label=\"状态\">\r\n                  <Badge \r\n                    status={selectedDevice.status === 'online' ? 'success' : 'error'} \r\n                    text={selectedDevice.status === 'online' ? '在线' : '离线'} \r\n                  />\r\n                </Descriptions.Item>\r\n                <Descriptions.Item label=\"位置\">{selectedDevice.location}</Descriptions.Item>\r\n                <Descriptions.Item label=\"IP地址\">{selectedDevice.ip}</Descriptions.Item>\r\n                <Descriptions.Item label=\"最后活动时间\">{selectedDevice.lastActive}</Descriptions.Item>\r\n                {selectedDevice.status === 'online' && (\r\n                  <>\r\n                    <Descriptions.Item label=\"CPU使用率\">\r\n                      <div style={{ display: 'flex', alignItems: 'center' }}>\r\n                        <div style={{ \r\n                          width: '100px', \r\n                          height: '8px', \r\n                          background: '#f0f0f0', \r\n                          borderRadius: '4px',\r\n                          overflow: 'hidden',\r\n                          marginRight: '8px'\r\n                        }}>\r\n                          <div style={{ \r\n                            width: `${selectedDevice.cpu}%`, \r\n                            height: '100%', \r\n                            background: getHealthColor(selectedDevice.cpu),\r\n                            borderRadius: '4px'\r\n                          }} />\r\n                        </div>\r\n                        <span>{selectedDevice.cpu}%</span>\r\n                      </div>\r\n                    </Descriptions.Item>\r\n                    <Descriptions.Item label=\"内存使用率\">\r\n                      <div style={{ display: 'flex', alignItems: 'center' }}>\r\n                        <div style={{ \r\n                          width: '100px', \r\n                          height: '8px', \r\n                          background: '#f0f0f0', \r\n                          borderRadius: '4px',\r\n                          overflow: 'hidden',\r\n                          marginRight: '8px'\r\n                        }}>\r\n                          <div style={{ \r\n                            width: `${selectedDevice.memory}%`, \r\n                            height: '100%', \r\n                            background: getHealthColor(selectedDevice.memory),\r\n                            borderRadius: '4px'\r\n                          }} />\r\n                        </div>\r\n                        <span>{selectedDevice.memory}%</span>\r\n                      </div>\r\n                    </Descriptions.Item>\r\n                    <Descriptions.Item label=\"磁盘使用率\">\r\n                      <div style={{ display: 'flex', alignItems: 'center' }}>\r\n                        <div style={{ \r\n                          width: '100px', \r\n                          height: '8px', \r\n                          background: '#f0f0f0', \r\n                          borderRadius: '4px',\r\n                          overflow: 'hidden',\r\n                          marginRight: '8px'\r\n                        }}>\r\n                          <div style={{ \r\n                            width: `${selectedDevice.disk}%`, \r\n                            height: '100%', \r\n                            background: getHealthColor(selectedDevice.disk),\r\n                            borderRadius: '4px'\r\n                          }} />\r\n                        </div>\r\n                        <span>{selectedDevice.disk}%</span>\r\n                      </div>\r\n                    </Descriptions.Item>\r\n                  </>\r\n                )}\r\n              </Descriptions>\r\n            ) : (\r\n              <p style={{ fontSize: '13px' }}>请选择设备查看详细信息</p>\r\n            )}\r\n          </InfoCard>\r\n        </CollapsibleSidebar>\r\n      </PageContainer>\r\n    </Spin>\r\n  );\r\n};\r\n\r\nconst styles = {\r\n  /* ... existing styles ... */\r\n};\r\n\r\n// 添加全局样式\r\nconst globalStyles = `\r\n  .device-status-container {\r\n    display: flex;\r\n    height: 100%;\r\n  }\r\n  \r\n  .device-sidebar {\r\n    width: 320px;\r\n    padding: 16px;\r\n    background-color: #f5f5f5;\r\n    overflow-y: auto;\r\n    border-right: 1px solid #e8e8e8;\r\n  }\r\n  \r\n  .device-content {\r\n    flex: 1;\r\n    padding: 16px;\r\n    overflow-y: auto;\r\n  }\r\n  \r\n  .ant-card-head-title {\r\n    font-weight: bold;\r\n    font-size: 16px;\r\n  }\r\n`;\r\n\r\nexport default DeviceStatus;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AACtI,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,QAAQ,oBAAoB;AACrG,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,yCAAyC;AACxE,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACAb,OAAO,CAACc,GAAG,CAAC,CAACb,QAAQ,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,cAAc,CAAC,CAAC;;AAEzG;AACA,MAAMS,aAAa,GAAGR,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GANMF,aAAa;AAOnB,MAAMG,WAAW,GAAGX,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMG,YAAY,GAAGZ,MAAM,CAACS,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMI,WAAW,GAAGb,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA,aAAaK,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACxEF,KAAK,CAACC,aAAa,GAAG,cAAc,GACpCD,KAAK,CAACE,cAAc,GAAG,cAAc,GAAG,OAAO;AACnD;AACA,YAAYF,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACvEF,KAAK,CAACC,aAAa,GAAG,WAAW,GACjCD,KAAK,CAACE,cAAc,GAAG,WAAW,GAAG,GAAG;AAC5C;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfMJ,WAAW;AAgBjB,MAAMK,QAAQ,GAAGlB,MAAM,CAACpB,IAAI,CAAC;AAC7B;AACA,YAAYkC,KAAK,IAAIA,KAAK,CAACK,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAnCMF,QAAQ;AAoCd,MAAMG,gBAAgB,GAAGrB,MAAM,CAACnB,SAAS,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyC,GAAA,GATID,gBAAgB;AAWtB,MAAME,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsD,cAAc,EAAEC,iBAAiB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACwD,KAAK,EAAEC,QAAQ,CAAC,GAAGzD,QAAQ,CAAC;IACjC0D,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEF,MAAMC,kBAAkB,GAAG9D,MAAM,CAAC,IAAI,CAAC;EACvC,MAAM+D,oBAAoB,GAAG/D,MAAM,CAAC,IAAI,CAAC;;EAEzC;EACA,MAAM,CAACsC,aAAa,EAAE0B,gBAAgB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyC,cAAc,EAAE0B,iBAAiB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACsE,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACwE,aAAa,EAAEC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0E,eAAe,EAAEC,kBAAkB,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM4E,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFzB,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM0B,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMtD,KAAK,CAACuD,GAAG,CAAC,GAAGL,MAAM,cAAc,CAAC;MAEzD,IAAII,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,IAAIH,QAAQ,CAACE,IAAI,CAACA,IAAI,EAAE;QAChE,MAAME,WAAW,GAAGJ,QAAQ,CAACE,IAAI,CAACA,IAAI;;QAEtC;QACA,MAAMG,gBAAgB,GAAGD,WAAW,CAACE,GAAG,CAACC,MAAM,KAAK;UAClD,GAAGA,MAAM;UACTC,EAAE,EAAED,MAAM,CAACE,SAAS;UAAE;UACtB;UACAC,MAAM,EAAE,QAAQ;UAChBC,GAAG,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;UACpCC,MAAM,EAAEH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;UACvCE,IAAI,EAAEJ,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;UACrCG,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAACM,cAAc,CAAC;QACxF,CAAC,CAAC,CAAC;QAEHhD,UAAU,CAACiC,gBAAgB,CAAC;QAC5BX,kBAAkB,CAACW,gBAAgB,CAAC,CAAC,CAAC;QACtCgB,YAAY,CAAChB,gBAAgB,CAAC;;QAE9B;QACA,MAAM9B,KAAK,GAAG;UACZE,YAAY,EAAE4B,gBAAgB,CAACiB,MAAM;UACrC5C,aAAa,EAAE2B,gBAAgB,CAACkB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACH,MAAM;UACzE3C,cAAc,EAAE0B,gBAAgB,CAACkB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACH,MAAM;UAC1E1C,aAAa,EAAEyB,gBAAgB,CAACkB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACd,MAAM,KAAK,QAAQ,CAAC,CAACY,MAAM;UACzEzC,cAAc,EAAEwB,gBAAgB,CAACkB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACd,MAAM,KAAK,SAAS,CAAC,CAACY,MAAM;UAC3ExC,YAAY,EAAEuB,gBAAgB,CAACkB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACd,MAAM,KAAK,OAAO,CAAC,CAACY;QACnE,CAAC;QAED9C,QAAQ,CAACD,KAAK,CAAC;MACjB,CAAC,MAAM;QACLmD,OAAO,CAACC,KAAK,CAAC,UAAU,EAAE3B,QAAQ,CAACE,IAAI,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACRzD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmD,YAAY,GAAIjB,WAAW,IAAK;IACpC;IACA,MAAMwB,QAAQ,GAAG,CAAC,CAAC;IACnBxB,WAAW,CAACyB,OAAO,CAACtB,MAAM,IAAI;MAC5B,MAAMuB,IAAI,GAAGC,aAAa,CAACxB,MAAM,CAACuB,IAAI,CAAC,IAAIvB,MAAM,CAACuB,IAAI;MACtDF,QAAQ,CAACE,IAAI,CAAC,GAAG,CAACF,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5C,CAAC,CAAC;;IAEF;IACA,MAAME,UAAU,GAAG,CAAC,CAAC;IACrB5B,WAAW,CAACyB,OAAO,CAACtB,MAAM,IAAI;MAC5B,MAAMkB,MAAM,GAAGQ,eAAe,CAAC1B,MAAM,CAACkB,MAAM,CAAC,IAAIlB,MAAM,CAACkB,MAAM;MAC9DO,UAAU,CAACP,MAAM,CAAC,GAAG,CAACO,UAAU,CAACP,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;IACpD,CAAC,CAAC;;IAEF;IACA,MAAMS,YAAY,GAAG,CACnB,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS,CAAE;IAAA,CACZ;;IAED;IACA,IAAInD,kBAAkB,CAACoD,OAAO,EAAE;MAC9B,MAAMC,SAAS,GAAGnG,OAAO,CAACoG,IAAI,CAACtD,kBAAkB,CAACoD,OAAO,CAAC;MAC1DC,SAAS,CAACE,SAAS,CAAC;QAClBC,KAAK,EAAEL,YAAY;QACnBM,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,0BAA0B;UACrCC,eAAe,EAAE,oBAAoB;UACrCC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE;YACTN,KAAK,EAAE;UACT;QACF,CAAC;QACDO,MAAM,EAAE;UACNC,MAAM,EAAE,YAAY;UACpBC,MAAM,EAAE,CAAC;UACTC,IAAI,EAAE,QAAQ;UACdC,SAAS,EAAE,EAAE;UACbC,UAAU,EAAE,EAAE;UACdN,SAAS,EAAE;YACTO,QAAQ,EAAE;UACZ,CAAC;UACDC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;QACzB,CAAC;QACDC,MAAM,EAAE,CAAC;UACPC,IAAI,EAAE,MAAM;UACZzB,IAAI,EAAE,KAAK;UACX0B,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,iBAAiB,EAAE,IAAI;UACvBC,SAAS,EAAE;YACTf,YAAY,EAAE,CAAC;YACfgB,WAAW,EAAE,MAAM;YACnBC,WAAW,EAAE;UACf,CAAC;UACDC,KAAK,EAAE;YACLC,IAAI,EAAE,IAAI;YACVrB,SAAS,EAAE,MAAM;YACjBsB,QAAQ,EAAE,QAAQ;YAClBZ,QAAQ,EAAE,EAAE;YACZa,UAAU,EAAE,MAAM;YAClB1B,KAAK,EAAE,MAAM;YACb2B,eAAe,EAAE,0BAA0B;YAC3CC,eAAe,EAAE,CAAC;YAClBC,UAAU,EAAE;UACd,CAAC;UACDC,QAAQ,EAAE;YACRP,KAAK,EAAE;cACLC,IAAI,EAAE,IAAI;cACVX,QAAQ,EAAE,EAAE;cACZa,UAAU,EAAE;YACd,CAAC;YACDN,SAAS,EAAE;cACTW,UAAU,EAAE,EAAE;cACdC,aAAa,EAAE,CAAC;cAChBC,WAAW,EAAE;YACf;UACF,CAAC;UACDC,SAAS,EAAE;YACTV,IAAI,EAAE;UACR,CAAC;UACD7D,IAAI,EAAEwE,MAAM,CAACC,OAAO,CAAC/C,QAAQ,CAAC,CAACtB,GAAG,CAAC,CAAC,CAACiD,IAAI,EAAEqB,KAAK,CAAC,MAAM;YAAErB,IAAI;YAAEqB;UAAM,CAAC,CAAC;QACzE,CAAC;MACH,CAAC,CAAC;;MAEF;MACAC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAM;QACtC1C,SAAS,CAAC2C,MAAM,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI/F,oBAAoB,CAACmD,OAAO,EAAE;MAChC,MAAM6C,WAAW,GAAG/I,OAAO,CAACoG,IAAI,CAACrD,oBAAoB,CAACmD,OAAO,CAAC;MAC9D6C,WAAW,CAAC1C,SAAS,CAAC;QACpBC,KAAK,EAAEL,YAAY;QACnBM,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,0BAA0B;UACrCC,eAAe,EAAE,oBAAoB;UACrCC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE;YACTN,KAAK,EAAE;UACT;QACF,CAAC;QACDO,MAAM,EAAE;UACNC,MAAM,EAAE,YAAY;UACpBC,MAAM,EAAE,CAAC;UACTC,IAAI,EAAE,QAAQ;UACdC,SAAS,EAAE,EAAE;UACbC,UAAU,EAAE,EAAE;UACdN,SAAS,EAAE;YACTO,QAAQ,EAAE;UACZ,CAAC;UACDC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;QACzB,CAAC;QACDC,MAAM,EAAE,CAAC;UACPC,IAAI,EAAE,MAAM;UACZzB,IAAI,EAAE,KAAK;UACX0B,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,iBAAiB,EAAE,IAAI;UACvBC,SAAS,EAAE;YACTf,YAAY,EAAE,CAAC;YACfgB,WAAW,EAAE,MAAM;YACnBC,WAAW,EAAE;UACf,CAAC;UACDC,KAAK,EAAE;YACLC,IAAI,EAAE,IAAI;YACVrB,SAAS,EAAE,MAAM;YACjBsB,QAAQ,EAAE,QAAQ;YAClBZ,QAAQ,EAAE,EAAE;YACZa,UAAU,EAAE,MAAM;YAClB1B,KAAK,EAAE,MAAM;YACb2B,eAAe,EAAE,0BAA0B;YAC3CC,eAAe,EAAE,CAAC;YAClBC,UAAU,EAAE;UACd,CAAC;UACDC,QAAQ,EAAE;YACRP,KAAK,EAAE;cACLC,IAAI,EAAE,IAAI;cACVX,QAAQ,EAAE,EAAE;cACZa,UAAU,EAAE;YACd,CAAC;YACDN,SAAS,EAAE;cACTW,UAAU,EAAE,EAAE;cACdC,aAAa,EAAE,CAAC;cAChBC,WAAW,EAAE;YACf;UACF,CAAC;UACDC,SAAS,EAAE;YACTV,IAAI,EAAE;UACR,CAAC;UACD7D,IAAI,EAAEwE,MAAM,CAACC,OAAO,CAAC3C,UAAU,CAAC,CAAC1B,GAAG,CAAC,CAAC,CAACiD,IAAI,EAAEqB,KAAK,CAAC,MAAM;YAAErB,IAAI;YAAEqB;UAAM,CAAC,CAAC;QAC3E,CAAC;MACH,CAAC,CAAC;;MAEF;MACAC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,MAAM;QACtCE,WAAW,CAACD,MAAM,CAAC,CAAC;MACtB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMhD,aAAa,GAAG;IACpBkD,MAAM,EAAE,KAAK;IACbC,YAAY,EAAE,OAAO;IACrBC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,KAAK;IACVC,cAAc,EAAE,QAAQ;IACxBC,GAAG,EAAE;EACP,CAAC;;EAED;EACA,MAAMrD,eAAe,GAAG;IACtB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;IACf,SAAS,EAAE,IAAI;IACf,OAAO,EAAE,IAAI;IACb,aAAa,EAAE;EACjB,CAAC;;EAED;EACA,MAAMsD,uBAAuB,GAAIX,KAAK,IAAK;IACzCxF,aAAa,CAACwF,KAAK,CAAC;IACpBtF,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAIsF,KAAK,EAAE;MACT,MAAMY,YAAY,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACtH,OAAO,CAACmC,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACqE,KAAK,CAAC,CAAC,CAAC,CAAC;MACvEpF,gBAAgB,CAACgG,YAAY,CAACjE,MAAM,CAACmE,OAAO,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC,MAAM;MACLnG,gBAAgB,CAAC,EAAE,CAAC;IACtB;EACF,CAAC;EAED,MAAMoG,wBAAwB,GAAIhB,KAAK,IAAK;IAC1CtF,cAAc,CAACsF,KAAK,CAAC;EACvB,CAAC;EAED,MAAMiB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAAC1G,UAAU,IAAI,CAACE,WAAW,EAAE;MAC/B;MACAK,kBAAkB,CAACvB,OAAO,CAAC;IAC7B,CAAC,MAAM;MACL;MACA,MAAM2H,QAAQ,GAAG3H,OAAO,CAACoD,MAAM,CAAChB,MAAM,IAAIA,MAAM,CAACpB,UAAU,CAAC,KAAKE,WAAW,CAAC;MAC7EK,kBAAkB,CAACoG,QAAQ,CAAC;IAC9B;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B3G,aAAa,CAAC,IAAI,CAAC;IACnBE,cAAc,CAAC,IAAI,CAAC;IACpBE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,kBAAkB,CAACvB,OAAO,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM6H,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAAC7G,UAAU,EAAE,OAAO,EAAE;IAE1B,OAAOI,aAAa,CAACe,GAAG,CAAC2F,MAAM,IAAI;MACjC;MACA,IAAI9G,UAAU,KAAK,MAAM,EAAE;QACzB,OAAO;UACLyF,KAAK,EAAEqB,MAAM;UACbnC,KAAK,EAAE/B,aAAa,CAACkE,MAAM,CAAC,IAAIA;QAClC,CAAC;MACH;MACA,OAAO;QACLrB,KAAK,EAAEqB,MAAM;QACbnC,KAAK,EAAEmC;MACT,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;;EAED;EACAjL,SAAS,CAAC,MAAM;IACd2E,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3E,SAAS,CAAC,MAAM;IACd,IAAIyE,eAAe,CAAC6B,MAAM,GAAG,CAAC,IAAI,CAACjD,cAAc,EAAE;MACjDC,iBAAiB,CAACmB,eAAe,CAAC,CAAC,CAAC,CAAC;MACrCiC,OAAO,CAACwE,GAAG,CAAC,aAAa,EAAEzG,eAAe,CAAC,CAAC,CAAC,CAAC8D,IAAI,CAAC;IACrD;EACF,CAAC,EAAE,CAAC9D,eAAe,EAAEpB,cAAc,CAAC,CAAC;;EAErC;EACA,MAAM8H,kBAAkB,GAAI5F,MAAM,IAAK;IACrCjC,iBAAiB,CAACiC,MAAM,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM6F,aAAa,GAAG,CACpB;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEhF,MAAM,iBACZ7E,OAAA,CAAClB,KAAK;MACJ+F,MAAM,EAAEA,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;MAClDiF,IAAI,EAAEjF,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;IAAK;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC;EAEL,CAAC,EACD;IACET,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE/F,MAAM,IAAI;MAChB,IAAI6B,KAAK,GAAG,OAAO;MACnB,IAAImE,IAAI,GAAG,IAAI;MAEf,IAAIhG,MAAM,KAAK,SAAS,EAAE;QACxB6B,KAAK,GAAG,QAAQ;QAChBmE,IAAI,GAAG,IAAI;MACb,CAAC,MAAM,IAAIhG,MAAM,KAAK,OAAO,EAAE;QAC7B6B,KAAK,GAAG,KAAK;QACbmE,IAAI,GAAG,IAAI;MACb;MAEA,oBAAO9J,OAAA,CAACjB,GAAG;QAAC4G,KAAK,EAAEA,KAAM;QAAAwE,QAAA,EAAEL;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACxC;EACF,CAAC,CACF;;EAED;EACA,MAAME,cAAc,GAAIpC,KAAK,IAAK;IAChC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACnC,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA5J,SAAS,CAAC,MAAM;IACd,IAAIoH,SAAS,GAAG,IAAI;IACpB,IAAI4C,WAAW,GAAG,IAAI;IAEtB,IAAIjG,kBAAkB,CAACoD,OAAO,EAAE;MAC9BC,SAAS,GAAGnG,OAAO,CAACoG,IAAI,CAACtD,kBAAkB,CAACoD,OAAO,CAAC;IACtD;IAEA,IAAInD,oBAAoB,CAACmD,OAAO,EAAE;MAChC6C,WAAW,GAAG/I,OAAO,CAACoG,IAAI,CAACrD,oBAAoB,CAACmD,OAAO,CAAC;IAC1D;IAEA,MAAM8E,YAAY,GAAGA,CAAA,KAAM;MAAA,IAAAC,UAAA,EAAAC,YAAA;MACzB,CAAAD,UAAA,GAAA9E,SAAS,cAAA8E,UAAA,uBAATA,UAAA,CAAWnC,MAAM,CAAC,CAAC;MACnB,CAAAoC,YAAA,GAAAnC,WAAW,cAAAmC,YAAA,uBAAXA,YAAA,CAAapC,MAAM,CAAC,CAAC;IACvB,CAAC;IAEDF,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEmC,YAAY,CAAC;IAE/C,OAAO,MAAM;MAAA,IAAAG,WAAA,EAAAC,aAAA;MACXxC,MAAM,CAACyC,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;MAClD,CAAAG,WAAA,GAAAhF,SAAS,cAAAgF,WAAA,uBAATA,WAAA,CAAWG,OAAO,CAAC,CAAC;MACpB,CAAAF,aAAA,GAAArC,WAAW,cAAAqC,aAAA,uBAAXA,aAAA,CAAaE,OAAO,CAAC,CAAC;IACxB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvM,SAAS,CAAC,MAAM;IACd,MAAMwM,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACpDF,YAAY,CAACG,SAAS,GAAGC,YAAY;IACrCH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,YAAY,CAAC;IAEvC,OAAO,MAAM;MACXC,QAAQ,CAACI,IAAI,CAACE,WAAW,CAACP,YAAY,CAAC;IACzC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE5K,OAAA,CAACnB,IAAI;IAACuM,QAAQ,EAAE/J,OAAQ;IAACgK,GAAG,EAAC,uBAAQ;IAAAlB,QAAA,eACnCnK,OAAA,CAACI,aAAa;MAAA+J,QAAA,gBAEZnK,OAAA,CAACH,kBAAkB;QACjBuH,QAAQ,EAAC,MAAM;QACfkE,SAAS,EAAE3K,aAAc;QACzB4K,UAAU,EAAEA,CAAA,KAAMlJ,gBAAgB,CAAC,CAAC1B,aAAa,CAAE;QAAAwJ,QAAA,gBAGnDnK,OAAA,CAACc,QAAQ;UAAC2I,KAAK,EAAC,sCAAQ;UAAC+B,QAAQ,EAAE,KAAM;UAACzK,MAAM,EAAC,OAAO;UAAAoJ,QAAA,eACtDnK,OAAA,CAAC1B,GAAG;YAACmN,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;YAAAtB,QAAA,gBAClBnK,OAAA,CAACzB,GAAG;cAACmN,IAAI,EAAE,CAAE;cAAAvB,QAAA,eACXnK,OAAA,CAACiB,gBAAgB;gBACfwI,KAAK,EAAC,0BAAM;gBACZzB,KAAK,EAAErG,KAAK,CAACE,YAAa;gBAC1B8J,UAAU,EAAE;kBAAEhG,KAAK,EAAE;gBAAU;cAAE;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlK,OAAA,CAACzB,GAAG;cAACmN,IAAI,EAAE,CAAE;cAAAvB,QAAA,eACXnK,OAAA,CAACiB,gBAAgB;gBACfwI,KAAK,EAAC,0BAAM;gBACZzB,KAAK,EAAErG,KAAK,CAACG,aAAc;gBAC3B8J,MAAM,EAAE,KAAKjK,KAAK,CAACE,YAAY,EAAG;gBAClC8J,UAAU,EAAE;kBAAEhG,KAAK,EAAE;gBAAU;cAAE;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlK,OAAA,CAACzB,GAAG;cAACmN,IAAI,EAAE,CAAE;cAAAvB,QAAA,eACXnK,OAAA,CAACiB,gBAAgB;gBACfwI,KAAK,EAAC,0BAAM;gBACZzB,KAAK,EAAErG,KAAK,CAACI,cAAe;gBAC5B6J,MAAM,EAAE,KAAKjK,KAAK,CAACE,YAAY,EAAG;gBAClC8J,UAAU,EAAE;kBAAEhG,KAAK,EAAE;gBAAU;cAAE;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlK,OAAA,CAACzB,GAAG;cAACmN,IAAI,EAAE,CAAE;cAAAvB,QAAA,eACXnK,OAAA,CAACiB,gBAAgB;gBACfwI,KAAK,EAAC,0BAAM;gBACZzB,KAAK,EAAErG,KAAK,CAACK,aAAc;gBAC3B4J,MAAM,EAAE,KAAKjK,KAAK,CAACE,YAAY,EAAG;gBAClC8J,UAAU,EAAE;kBAAEhG,KAAK,EAAE;gBAAU;cAAE;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlK,OAAA,CAACzB,GAAG;cAACmN,IAAI,EAAE,CAAE;cAAAvB,QAAA,eACXnK,OAAA,CAACiB,gBAAgB;gBACfwI,KAAK,EAAC,0BAAM;gBACZzB,KAAK,EAAErG,KAAK,CAACM,cAAe;gBAC5B2J,MAAM,EAAE,KAAKjK,KAAK,CAACE,YAAY,EAAG;gBAClC8J,UAAU,EAAE;kBAAEhG,KAAK,EAAE;gBAAU;cAAE;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlK,OAAA,CAACzB,GAAG;cAACmN,IAAI,EAAE,CAAE;cAAAvB,QAAA,eACXnK,OAAA,CAACiB,gBAAgB;gBACfwI,KAAK,EAAC,0BAAM;gBACZzB,KAAK,EAAErG,KAAK,CAACO,YAAa;gBAC1B0J,MAAM,EAAE,KAAKjK,KAAK,CAACE,YAAY,EAAG;gBAClC8J,UAAU,EAAE;kBAAEhG,KAAK,EAAE;gBAAU;cAAE;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGXlK,OAAA,CAACc,QAAQ;UAAC2I,KAAK,EAAC,sCAAQ;UAAC+B,QAAQ,EAAE,KAAM;UAACzK,MAAM,EAAC,kBAAkB;UAAAoJ,QAAA,eACjEnK,OAAA;YACE6L,GAAG,EAAE1J,kBAAmB;YACxB2J,KAAK,EAAE;cACL/K,MAAM,EAAE,MAAM;cACd6I,KAAK,EAAE;YACT;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGXlK,OAAA,CAACc,QAAQ;UAAC2I,KAAK,EAAC,sCAAQ;UAAC+B,QAAQ,EAAE,KAAM;UAACzK,MAAM,EAAC,kBAAkB;UAAAoJ,QAAA,eACjEnK,OAAA;YACE6L,GAAG,EAAEzJ,oBAAqB;YAC1B0J,KAAK,EAAE;cACL/K,MAAM,EAAE,MAAM;cACd6I,KAAK,EAAE;YACT;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGrBlK,OAAA,CAACS,WAAW;QAACE,aAAa,EAAEA,aAAc;QAACC,cAAc,EAAEA;MAAe;QAAAmJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC,eAGdlK,OAAA,CAACH,kBAAkB;QACjBuH,QAAQ,EAAC,OAAO;QAChBkE,SAAS,EAAE1K,cAAe;QAC1B2K,UAAU,EAAEA,CAAA,KAAMjJ,iBAAiB,CAAC,CAAC1B,cAAc,CAAE;QAAAuJ,QAAA,gBAGrDnK,OAAA,CAACc,QAAQ;UAAC2I,KAAK,EAAC,0BAAM;UAAC+B,QAAQ,EAAE,KAAM;UAACzK,MAAM,EAAC,KAAK;UAAAoJ,QAAA,eAClDnK,OAAA;YAAK8L,KAAK,EAAE;cACVC,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE,QAAQ;cACvBjL,MAAM,EAAE;YACV,CAAE;YAAAoJ,QAAA,gBACAnK,OAAA;cAAK8L,KAAK,EAAE;gBAAEG,IAAI,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAA/B,QAAA,eACxCnK,OAAA,CAACrB,KAAK;gBACJwN,UAAU,EAAEtJ,eAAgB;gBAC5BuJ,OAAO,EAAE5C,aAAc;gBACvB6C,MAAM,EAAC,IAAI;gBACXC,UAAU,EAAE,KAAM;gBAClBC,IAAI,EAAC,OAAO;gBACZC,MAAM,EAAE;kBAAEC,CAAC,EAAE;gBAAK,CAAE;gBACpBC,KAAK,EAAGC,MAAM,KAAM;kBAClBC,OAAO,EAAEA,CAAA,KAAMrD,kBAAkB,CAACoD,MAAM,CAAC;kBACzCb,KAAK,EAAE;oBACLe,MAAM,EAAE,SAAS;oBACjBC,UAAU,EAAE,CAAArL,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEsL,EAAE,MAAKJ,MAAM,CAACI,EAAE,GAAG,SAAS,GAAG,aAAa;oBACxEvG,QAAQ,EAAE,MAAM;oBAChBC,OAAO,EAAE;kBACX;gBACF,CAAC;cAAE;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNlK,OAAA;cAAK8L,KAAK,EAAE;gBACVkB,SAAS,EAAE,KAAK;gBAChBvG,OAAO,EAAE,OAAO;gBAChBwG,SAAS,EAAE,mBAAmB;gBAC9BlB,OAAO,EAAE,MAAM;gBACfmB,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE,QAAQ;gBACpBC,QAAQ,EAAE,MAAM;gBAChBC,GAAG,EAAE;cACP,CAAE;cAAAlD,QAAA,gBACAnK,OAAA,CAACZ,KAAK;gBAACmN,IAAI,EAAC,OAAO;gBAACT,KAAK,EAAE;kBAAEsB,QAAQ,EAAE;gBAAO,CAAE;gBAAAjD,QAAA,gBAC9CnK,OAAA,CAACd,MAAM;kBACLoO,WAAW,EAAC,0BAAM;kBAClBxB,KAAK,EAAE;oBAAElC,KAAK,EAAE;kBAAQ,CAAE;kBAC1B2C,IAAI,EAAC,OAAO;kBACZvE,KAAK,EAAEzF,UAAW;kBAClBgL,QAAQ,EAAE5E,uBAAwB;kBAClC6E,UAAU;kBACVC,OAAO,EAAE,CACP;oBAAEzF,KAAK,EAAE,MAAM;oBAAEd,KAAK,EAAE;kBAAO,CAAC,EAChC;oBAAEc,KAAK,EAAE,UAAU;oBAAEd,KAAK,EAAE;kBAAK,CAAC;gBAClC;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFlK,OAAA,CAACd,MAAM;kBACLoO,WAAW,EAAC,oBAAK;kBACjBxB,KAAK,EAAE;oBAAElC,KAAK,EAAE;kBAAQ,CAAE;kBAC1B2C,IAAI,EAAC,OAAO;kBACZvE,KAAK,EAAEvF,WAAY;kBACnB8K,QAAQ,EAAEvE,wBAAyB;kBACnC0E,QAAQ,EAAE,CAACnL,UAAU,IAAII,aAAa,CAAC+B,MAAM,KAAK,CAAE;kBACpD8I,UAAU;kBACVC,OAAO,EAAErE,sBAAsB,CAAC;gBAAE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACFlK,OAAA,CAACb,MAAM;kBAAC+F,IAAI,EAAC,SAAS;kBAACqH,IAAI,EAAC,OAAO;kBAACK,OAAO,EAAE3D,kBAAmB;kBAAAkB,QAAA,EAAC;gBAEjE;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlK,OAAA,CAACb,MAAM;kBAACoN,IAAI,EAAC,OAAO;kBAACK,OAAO,EAAEzD,iBAAkB;kBAAAgB,QAAA,EAAC;gBAEjD;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACRlK,OAAA;gBAAK8L,KAAK,EAAE;kBAAEtF,QAAQ,EAAE,MAAM;kBAAEb,KAAK,EAAE;gBAAO,CAAE;gBAAAwE,QAAA,GAC7CtH,eAAe,CAAC6B,MAAM,EAAC,KAAG,EAACnD,OAAO,CAACmD,MAAM,EAAC,qBAC7C;cAAA;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGXlK,OAAA,CAACc,QAAQ;UAAC2I,KAAK,EAAC,sCAAQ;UAAC+B,QAAQ,EAAE,KAAM;UAACzK,MAAM,EAAC,KAAK;UAAAoJ,QAAA,EACnD1I,cAAc,gBACbzB,OAAA,CAACpB,YAAY;YACX4M,QAAQ;YACRmC,MAAM,EAAE,CAAE;YACVpB,IAAI,EAAC,OAAO;YACZqB,MAAM,EAAE;cACN1G,KAAK,EAAE;gBAAEV,QAAQ,EAAE,MAAM;gBAAEC,OAAO,EAAE;cAAU,CAAC;cAC/CoH,OAAO,EAAE;gBAAErH,QAAQ,EAAE,MAAM;gBAAEC,OAAO,EAAE;cAAU;YAClD,CAAE;YAAA0D,QAAA,gBAEFnK,OAAA,CAACpB,YAAY,CAACkP,IAAI;cAAC5G,KAAK,EAAC,0BAAM;cAAAiD,QAAA,EAAE1I,cAAc,CAACkF;YAAI;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eACzElK,OAAA,CAACpB,YAAY,CAACkP,IAAI;cAAC5G,KAAK,EAAC,0BAAM;cAAAiD,QAAA,EAC5BhF,aAAa,CAAC1D,cAAc,CAACyD,IAAI,CAAC,IAAIzD,cAAc,CAACyD;YAAI;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACpBlK,OAAA,CAACpB,YAAY,CAACkP,IAAI;cAAC5G,KAAK,EAAC,cAAI;cAAAiD,QAAA,eAC3BnK,OAAA,CAAClB,KAAK;gBACJ+F,MAAM,EAAEpD,cAAc,CAACoD,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;gBACjEiF,IAAI,EAAErI,cAAc,CAACoD,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;cAAK;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,eACpBlK,OAAA,CAACpB,YAAY,CAACkP,IAAI;cAAC5G,KAAK,EAAC,cAAI;cAAAiD,QAAA,EAAE1I,cAAc,CAACsM;YAAQ;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC3ElK,OAAA,CAACpB,YAAY,CAACkP,IAAI;cAAC5G,KAAK,EAAC,gBAAM;cAAAiD,QAAA,EAAE1I,cAAc,CAACmC;YAAE;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eACvElK,OAAA,CAACpB,YAAY,CAACkP,IAAI;cAAC5G,KAAK,EAAC,sCAAQ;cAAAiD,QAAA,EAAE1I,cAAc,CAAC4C;YAAU;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,EAChFzI,cAAc,CAACoD,MAAM,KAAK,QAAQ,iBACjC7E,OAAA,CAAAE,SAAA;cAAAiK,QAAA,gBACEnK,OAAA,CAACpB,YAAY,CAACkP,IAAI;gBAAC5G,KAAK,EAAC,uBAAQ;gBAAAiD,QAAA,eAC/BnK,OAAA;kBAAK8L,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEoB,UAAU,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,gBACpDnK,OAAA;oBAAK8L,KAAK,EAAE;sBACVlC,KAAK,EAAE,OAAO;sBACd7I,MAAM,EAAE,KAAK;sBACb+L,UAAU,EAAE,SAAS;sBACrB9G,YAAY,EAAE,KAAK;sBACnBkG,QAAQ,EAAE,QAAQ;sBAClB8B,WAAW,EAAE;oBACf,CAAE;oBAAA7D,QAAA,eACAnK,OAAA;sBAAK8L,KAAK,EAAE;wBACVlC,KAAK,EAAE,GAAGnI,cAAc,CAACsC,GAAG,GAAG;wBAC/BhD,MAAM,EAAE,MAAM;wBACd+L,UAAU,EAAE1C,cAAc,CAAC3I,cAAc,CAACsC,GAAG,CAAC;wBAC9CiC,YAAY,EAAE;sBAChB;oBAAE;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNlK,OAAA;oBAAAmK,QAAA,GAAO1I,cAAc,CAACsC,GAAG,EAAC,GAAC;kBAAA;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACW,CAAC,eACpBlK,OAAA,CAACpB,YAAY,CAACkP,IAAI;gBAAC5G,KAAK,EAAC,gCAAO;gBAAAiD,QAAA,eAC9BnK,OAAA;kBAAK8L,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEoB,UAAU,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,gBACpDnK,OAAA;oBAAK8L,KAAK,EAAE;sBACVlC,KAAK,EAAE,OAAO;sBACd7I,MAAM,EAAE,KAAK;sBACb+L,UAAU,EAAE,SAAS;sBACrB9G,YAAY,EAAE,KAAK;sBACnBkG,QAAQ,EAAE,QAAQ;sBAClB8B,WAAW,EAAE;oBACf,CAAE;oBAAA7D,QAAA,eACAnK,OAAA;sBAAK8L,KAAK,EAAE;wBACVlC,KAAK,EAAE,GAAGnI,cAAc,CAAC0C,MAAM,GAAG;wBAClCpD,MAAM,EAAE,MAAM;wBACd+L,UAAU,EAAE1C,cAAc,CAAC3I,cAAc,CAAC0C,MAAM,CAAC;wBACjD6B,YAAY,EAAE;sBAChB;oBAAE;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNlK,OAAA;oBAAAmK,QAAA,GAAO1I,cAAc,CAAC0C,MAAM,EAAC,GAAC;kBAAA;oBAAA4F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACW,CAAC,eACpBlK,OAAA,CAACpB,YAAY,CAACkP,IAAI;gBAAC5G,KAAK,EAAC,gCAAO;gBAAAiD,QAAA,eAC9BnK,OAAA;kBAAK8L,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEoB,UAAU,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,gBACpDnK,OAAA;oBAAK8L,KAAK,EAAE;sBACVlC,KAAK,EAAE,OAAO;sBACd7I,MAAM,EAAE,KAAK;sBACb+L,UAAU,EAAE,SAAS;sBACrB9G,YAAY,EAAE,KAAK;sBACnBkG,QAAQ,EAAE,QAAQ;sBAClB8B,WAAW,EAAE;oBACf,CAAE;oBAAA7D,QAAA,eACAnK,OAAA;sBAAK8L,KAAK,EAAE;wBACVlC,KAAK,EAAE,GAAGnI,cAAc,CAAC2C,IAAI,GAAG;wBAChCrD,MAAM,EAAE,MAAM;wBACd+L,UAAU,EAAE1C,cAAc,CAAC3I,cAAc,CAAC2C,IAAI,CAAC;wBAC/C4B,YAAY,EAAE;sBAChB;oBAAE;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNlK,OAAA;oBAAAmK,QAAA,GAAO1I,cAAc,CAAC2C,IAAI,EAAC,GAAC;kBAAA;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACW,CAAC;YAAA,eACpB,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CAAC,gBAEflK,OAAA;YAAG8L,KAAK,EAAE;cAAEtF,QAAQ,EAAE;YAAO,CAAE;YAAA2D,QAAA,EAAC;UAAW;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC/C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEX,CAAC;AAAC9I,EAAA,CAzrBID,YAAY;AAAA8M,GAAA,GAAZ9M,YAAY;AA2rBlB,MAAMyM,MAAM,GAAG;EACb;AAAA,CACD;;AAED;AACA,MAAM5C,YAAY,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,eAAe7J,YAAY;AAAC,IAAAb,EAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAA+M,GAAA;AAAAC,YAAA,CAAA5N,EAAA;AAAA4N,YAAA,CAAArN,GAAA;AAAAqN,YAAA,CAAAlN,GAAA;AAAAkN,YAAA,CAAAhN,GAAA;AAAAgN,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}