{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport classNames from 'classnames';\nimport { useEvent, warning } from 'rc-util';\nimport * as React from 'react';\nimport { INTERNAL_HOOKS } from \"../constant\";\nimport { makeImmutable } from \"../context/TableContext\";\nimport Table, { DEFAULT_PREFIX } from \"../Table\";\nimport Grid from \"./BodyGrid\";\nimport { StaticContext } from \"./context\";\nimport getValue from \"rc-util/es/utils/get\";\nvar renderBody = function renderBody(rawData, props) {\n  var ref = props.ref,\n    onScroll = props.onScroll;\n  return /*#__PURE__*/React.createElement(Grid, {\n    ref: ref,\n    data: rawData,\n    onScroll: onScroll\n  });\n};\nfunction VirtualTable(props, ref) {\n  var data = props.data,\n    columns = props.columns,\n    scroll = props.scroll,\n    sticky = props.sticky,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? DEFAULT_PREFIX : _props$prefixCls,\n    className = props.className,\n    listItemHeight = props.listItemHeight,\n    components = props.components,\n    onScroll = props.onScroll;\n  var _ref = scroll || {},\n    scrollX = _ref.x,\n    scrollY = _ref.y;\n\n  // Fill scrollX\n  if (typeof scrollX !== 'number') {\n    if (process.env.NODE_ENV !== 'production') {\n      warning(!scrollX, '`scroll.x` in virtual table must be number.');\n    }\n    scrollX = 1;\n  }\n\n  // Fill scrollY\n  if (typeof scrollY !== 'number') {\n    scrollY = 500;\n    if (process.env.NODE_ENV !== 'production') {\n      warning(false, '`scroll.y` in virtual table must be number.');\n    }\n  }\n  var getComponent = useEvent(function (path, defaultComponent) {\n    return getValue(components, path) || defaultComponent;\n  });\n\n  // Memo this\n  var onInternalScroll = useEvent(onScroll);\n\n  // ========================= Context ==========================\n  var context = React.useMemo(function () {\n    return {\n      sticky: sticky,\n      scrollY: scrollY,\n      listItemHeight: listItemHeight,\n      getComponent: getComponent,\n      onScroll: onInternalScroll\n    };\n  }, [sticky, scrollY, listItemHeight, getComponent, onInternalScroll]);\n\n  // ========================== Render ==========================\n  return /*#__PURE__*/React.createElement(StaticContext.Provider, {\n    value: context\n  }, /*#__PURE__*/React.createElement(Table, _extends({}, props, {\n    className: classNames(className, \"\".concat(prefixCls, \"-virtual\")),\n    scroll: _objectSpread(_objectSpread({}, scroll), {}, {\n      x: scrollX\n    }),\n    components: _objectSpread(_objectSpread({}, components), {}, {\n      // fix https://github.com/ant-design/ant-design/issues/48991\n      body: data !== null && data !== void 0 && data.length ? renderBody : undefined\n    }),\n    columns: columns,\n    internalHooks: INTERNAL_HOOKS,\n    tailor: true,\n    ref: ref\n  })));\n}\nvar RefVirtualTable = /*#__PURE__*/React.forwardRef(VirtualTable);\nif (process.env.NODE_ENV !== 'production') {\n  RefVirtualTable.displayName = 'VirtualTable';\n}\nexport function genVirtualTable(shouldTriggerRender) {\n  return makeImmutable(RefVirtualTable, shouldTriggerRender);\n}\nexport default genVirtualTable();", "map": {"version": 3, "names": ["_extends", "_objectSpread", "classNames", "useEvent", "warning", "React", "INTERNAL_HOOKS", "makeImmutable", "Table", "DEFAULT_PREFIX", "Grid", "StaticContext", "getValue", "renderBody", "rawData", "props", "ref", "onScroll", "createElement", "data", "VirtualTable", "columns", "scroll", "sticky", "_props$prefixCls", "prefixCls", "className", "listItemHeight", "components", "_ref", "scrollX", "x", "scrollY", "y", "process", "env", "NODE_ENV", "getComponent", "path", "defaultComponent", "onInternalScroll", "context", "useMemo", "Provider", "value", "concat", "body", "length", "undefined", "internalHooks", "tailor", "RefVirtualTable", "forwardRef", "displayName", "genVirtualTable", "should<PERSON>rigger<PERSON>ender"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-table/es/VirtualTable/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport classNames from 'classnames';\nimport { useEvent, warning } from 'rc-util';\nimport * as React from 'react';\nimport { INTERNAL_HOOKS } from \"../constant\";\nimport { makeImmutable } from \"../context/TableContext\";\nimport Table, { DEFAULT_PREFIX } from \"../Table\";\nimport Grid from \"./BodyGrid\";\nimport { StaticContext } from \"./context\";\nimport getValue from \"rc-util/es/utils/get\";\nvar renderBody = function renderBody(rawData, props) {\n  var ref = props.ref,\n    onScroll = props.onScroll;\n  return /*#__PURE__*/React.createElement(Grid, {\n    ref: ref,\n    data: rawData,\n    onScroll: onScroll\n  });\n};\nfunction VirtualTable(props, ref) {\n  var data = props.data,\n    columns = props.columns,\n    scroll = props.scroll,\n    sticky = props.sticky,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? DEFAULT_PREFIX : _props$prefixCls,\n    className = props.className,\n    listItemHeight = props.listItemHeight,\n    components = props.components,\n    onScroll = props.onScroll;\n  var _ref = scroll || {},\n    scrollX = _ref.x,\n    scrollY = _ref.y;\n\n  // Fill scrollX\n  if (typeof scrollX !== 'number') {\n    if (process.env.NODE_ENV !== 'production') {\n      warning(!scrollX, '`scroll.x` in virtual table must be number.');\n    }\n    scrollX = 1;\n  }\n\n  // Fill scrollY\n  if (typeof scrollY !== 'number') {\n    scrollY = 500;\n    if (process.env.NODE_ENV !== 'production') {\n      warning(false, '`scroll.y` in virtual table must be number.');\n    }\n  }\n  var getComponent = useEvent(function (path, defaultComponent) {\n    return getValue(components, path) || defaultComponent;\n  });\n\n  // Memo this\n  var onInternalScroll = useEvent(onScroll);\n\n  // ========================= Context ==========================\n  var context = React.useMemo(function () {\n    return {\n      sticky: sticky,\n      scrollY: scrollY,\n      listItemHeight: listItemHeight,\n      getComponent: getComponent,\n      onScroll: onInternalScroll\n    };\n  }, [sticky, scrollY, listItemHeight, getComponent, onInternalScroll]);\n\n  // ========================== Render ==========================\n  return /*#__PURE__*/React.createElement(StaticContext.Provider, {\n    value: context\n  }, /*#__PURE__*/React.createElement(Table, _extends({}, props, {\n    className: classNames(className, \"\".concat(prefixCls, \"-virtual\")),\n    scroll: _objectSpread(_objectSpread({}, scroll), {}, {\n      x: scrollX\n    }),\n    components: _objectSpread(_objectSpread({}, components), {}, {\n      // fix https://github.com/ant-design/ant-design/issues/48991\n      body: data !== null && data !== void 0 && data.length ? renderBody : undefined\n    }),\n    columns: columns,\n    internalHooks: INTERNAL_HOOKS,\n    tailor: true,\n    ref: ref\n  })));\n}\nvar RefVirtualTable = /*#__PURE__*/React.forwardRef(VirtualTable);\nif (process.env.NODE_ENV !== 'production') {\n  RefVirtualTable.displayName = 'VirtualTable';\n}\nexport function genVirtualTable(shouldTriggerRender) {\n  return makeImmutable(RefVirtualTable, shouldTriggerRender);\n}\nexport default genVirtualTable();"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,QAAQ,EAAEC,OAAO,QAAQ,SAAS;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,aAAa,QAAQ,yBAAyB;AACvD,OAAOC,KAAK,IAAIC,cAAc,QAAQ,UAAU;AAChD,OAAOC,IAAI,MAAM,YAAY;AAC7B,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,OAAO,EAAEC,KAAK,EAAE;EACnD,IAAIC,GAAG,GAAGD,KAAK,CAACC,GAAG;IACjBC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;EAC3B,OAAO,aAAaZ,KAAK,CAACa,aAAa,CAACR,IAAI,EAAE;IAC5CM,GAAG,EAAEA,GAAG;IACRG,IAAI,EAAEL,OAAO;IACbG,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ,CAAC;AACD,SAASG,YAAYA,CAACL,KAAK,EAAEC,GAAG,EAAE;EAChC,IAAIG,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACnBE,OAAO,GAAGN,KAAK,CAACM,OAAO;IACvBC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,MAAM,GAAGR,KAAK,CAACQ,MAAM;IACrBC,gBAAgB,GAAGT,KAAK,CAACU,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAGf,cAAc,GAAGe,gBAAgB;IAC3EE,SAAS,GAAGX,KAAK,CAACW,SAAS;IAC3BC,cAAc,GAAGZ,KAAK,CAACY,cAAc;IACrCC,UAAU,GAAGb,KAAK,CAACa,UAAU;IAC7BX,QAAQ,GAAGF,KAAK,CAACE,QAAQ;EAC3B,IAAIY,IAAI,GAAGP,MAAM,IAAI,CAAC,CAAC;IACrBQ,OAAO,GAAGD,IAAI,CAACE,CAAC;IAChBC,OAAO,GAAGH,IAAI,CAACI,CAAC;;EAElB;EACA,IAAI,OAAOH,OAAO,KAAK,QAAQ,EAAE;IAC/B,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzChC,OAAO,CAAC,CAAC0B,OAAO,EAAE,6CAA6C,CAAC;IAClE;IACAA,OAAO,GAAG,CAAC;EACb;;EAEA;EACA,IAAI,OAAOE,OAAO,KAAK,QAAQ,EAAE;IAC/BA,OAAO,GAAG,GAAG;IACb,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzChC,OAAO,CAAC,KAAK,EAAE,6CAA6C,CAAC;IAC/D;EACF;EACA,IAAIiC,YAAY,GAAGlC,QAAQ,CAAC,UAAUmC,IAAI,EAAEC,gBAAgB,EAAE;IAC5D,OAAO3B,QAAQ,CAACgB,UAAU,EAAEU,IAAI,CAAC,IAAIC,gBAAgB;EACvD,CAAC,CAAC;;EAEF;EACA,IAAIC,gBAAgB,GAAGrC,QAAQ,CAACc,QAAQ,CAAC;;EAEzC;EACA,IAAIwB,OAAO,GAAGpC,KAAK,CAACqC,OAAO,CAAC,YAAY;IACtC,OAAO;MACLnB,MAAM,EAAEA,MAAM;MACdS,OAAO,EAAEA,OAAO;MAChBL,cAAc,EAAEA,cAAc;MAC9BU,YAAY,EAAEA,YAAY;MAC1BpB,QAAQ,EAAEuB;IACZ,CAAC;EACH,CAAC,EAAE,CAACjB,MAAM,EAAES,OAAO,EAAEL,cAAc,EAAEU,YAAY,EAAEG,gBAAgB,CAAC,CAAC;;EAErE;EACA,OAAO,aAAanC,KAAK,CAACa,aAAa,CAACP,aAAa,CAACgC,QAAQ,EAAE;IAC9DC,KAAK,EAAEH;EACT,CAAC,EAAE,aAAapC,KAAK,CAACa,aAAa,CAACV,KAAK,EAAER,QAAQ,CAAC,CAAC,CAAC,EAAEe,KAAK,EAAE;IAC7DW,SAAS,EAAExB,UAAU,CAACwB,SAAS,EAAE,EAAE,CAACmB,MAAM,CAACpB,SAAS,EAAE,UAAU,CAAC,CAAC;IAClEH,MAAM,EAAErB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqB,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;MACnDS,CAAC,EAAED;IACL,CAAC,CAAC;IACFF,UAAU,EAAE3B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2B,UAAU,CAAC,EAAE,CAAC,CAAC,EAAE;MAC3D;MACAkB,IAAI,EAAE3B,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,IAAIA,IAAI,CAAC4B,MAAM,GAAGlC,UAAU,GAAGmC;IACvE,CAAC,CAAC;IACF3B,OAAO,EAAEA,OAAO;IAChB4B,aAAa,EAAE3C,cAAc;IAC7B4C,MAAM,EAAE,IAAI;IACZlC,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC,CAAC;AACN;AACA,IAAImC,eAAe,GAAG,aAAa9C,KAAK,CAAC+C,UAAU,CAAChC,YAAY,CAAC;AACjE,IAAIc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCe,eAAe,CAACE,WAAW,GAAG,cAAc;AAC9C;AACA,OAAO,SAASC,eAAeA,CAACC,mBAAmB,EAAE;EACnD,OAAOhD,aAAa,CAAC4C,eAAe,EAAEI,mBAAmB,CAAC;AAC5D;AACA,eAAeD,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}