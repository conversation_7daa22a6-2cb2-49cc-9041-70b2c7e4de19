{"ast": null, "code": "import { toUint8, bytesMatch } from './byte-helpers.js';\nimport { findBox } from './mp4-helpers.js';\nimport { findEbml, EBML_TAGS } from './ebml-helpers.js';\nimport { getId3Offset } from './id3-helpers.js';\nimport { findH264Nal, findH265Nal } from './nal-helpers.js';\nvar CONSTANTS = {\n  // \"webm\" string literal in hex\n  'webm': toUint8([0x77, 0x65, 0x62, 0x6d]),\n  // \"matroska\" string literal in hex\n  'matroska': toUint8([0x6d, 0x61, 0x74, 0x72, 0x6f, 0x73, 0x6b, 0x61]),\n  // \"fLaC\" string literal in hex\n  'flac': toUint8([0x66, 0x4c, 0x61, 0x43]),\n  // \"OggS\" string literal in hex\n  'ogg': toUint8([0x4f, 0x67, 0x67, 0x53]),\n  // ac-3 sync byte, also works for ec-3 as that is simply a codec\n  // of ac-3\n  'ac3': toUint8([0x0b, 0x77]),\n  // \"RIFF\" string literal in hex used for wav and avi\n  'riff': toUint8([0x52, 0x49, 0x46, 0x46]),\n  // \"AVI\" string literal in hex\n  'avi': toUint8([0x41, 0x56, 0x49]),\n  // \"WAVE\" string literal in hex\n  'wav': toUint8([0x57, 0x41, 0x56, 0x45]),\n  // \"ftyp3g\" string literal in hex\n  '3gp': toUint8([0x66, 0x74, 0x79, 0x70, 0x33, 0x67]),\n  // \"ftyp\" string literal in hex\n  'mp4': toUint8([0x66, 0x74, 0x79, 0x70]),\n  // \"styp\" string literal in hex\n  'fmp4': toUint8([0x73, 0x74, 0x79, 0x70]),\n  // \"ftypqt\" string literal in hex\n  'mov': toUint8([0x66, 0x74, 0x79, 0x70, 0x71, 0x74]),\n  // moov string literal in hex\n  'moov': toUint8([0x6D, 0x6F, 0x6F, 0x76]),\n  // moof string literal in hex\n  'moof': toUint8([0x6D, 0x6F, 0x6F, 0x66])\n};\nvar _isLikely = {\n  aac: function aac(bytes) {\n    var offset = getId3Offset(bytes);\n    return bytesMatch(bytes, [0xFF, 0x10], {\n      offset: offset,\n      mask: [0xFF, 0x16]\n    });\n  },\n  mp3: function mp3(bytes) {\n    var offset = getId3Offset(bytes);\n    return bytesMatch(bytes, [0xFF, 0x02], {\n      offset: offset,\n      mask: [0xFF, 0x06]\n    });\n  },\n  webm: function webm(bytes) {\n    var docType = findEbml(bytes, [EBML_TAGS.EBML, EBML_TAGS.DocType])[0]; // check if DocType EBML tag is webm\n\n    return bytesMatch(docType, CONSTANTS.webm);\n  },\n  mkv: function mkv(bytes) {\n    var docType = findEbml(bytes, [EBML_TAGS.EBML, EBML_TAGS.DocType])[0]; // check if DocType EBML tag is matroska\n\n    return bytesMatch(docType, CONSTANTS.matroska);\n  },\n  mp4: function mp4(bytes) {\n    // if this file is another base media file format, it is not mp4\n    if (_isLikely['3gp'](bytes) || _isLikely.mov(bytes)) {\n      return false;\n    } // if this file starts with a ftyp or styp box its mp4\n\n    if (bytesMatch(bytes, CONSTANTS.mp4, {\n      offset: 4\n    }) || bytesMatch(bytes, CONSTANTS.fmp4, {\n      offset: 4\n    })) {\n      return true;\n    } // if this file starts with a moof/moov box its mp4\n\n    if (bytesMatch(bytes, CONSTANTS.moof, {\n      offset: 4\n    }) || bytesMatch(bytes, CONSTANTS.moov, {\n      offset: 4\n    })) {\n      return true;\n    }\n  },\n  mov: function mov(bytes) {\n    return bytesMatch(bytes, CONSTANTS.mov, {\n      offset: 4\n    });\n  },\n  '3gp': function gp(bytes) {\n    return bytesMatch(bytes, CONSTANTS['3gp'], {\n      offset: 4\n    });\n  },\n  ac3: function ac3(bytes) {\n    var offset = getId3Offset(bytes);\n    return bytesMatch(bytes, CONSTANTS.ac3, {\n      offset: offset\n    });\n  },\n  ts: function ts(bytes) {\n    if (bytes.length < 189 && bytes.length >= 1) {\n      return bytes[0] === 0x47;\n    }\n    var i = 0; // check the first 376 bytes for two matching sync bytes\n\n    while (i + 188 < bytes.length && i < 188) {\n      if (bytes[i] === 0x47 && bytes[i + 188] === 0x47) {\n        return true;\n      }\n      i += 1;\n    }\n    return false;\n  },\n  flac: function flac(bytes) {\n    var offset = getId3Offset(bytes);\n    return bytesMatch(bytes, CONSTANTS.flac, {\n      offset: offset\n    });\n  },\n  ogg: function ogg(bytes) {\n    return bytesMatch(bytes, CONSTANTS.ogg);\n  },\n  avi: function avi(bytes) {\n    return bytesMatch(bytes, CONSTANTS.riff) && bytesMatch(bytes, CONSTANTS.avi, {\n      offset: 8\n    });\n  },\n  wav: function wav(bytes) {\n    return bytesMatch(bytes, CONSTANTS.riff) && bytesMatch(bytes, CONSTANTS.wav, {\n      offset: 8\n    });\n  },\n  'h264': function h264(bytes) {\n    // find seq_parameter_set_rbsp\n    return findH264Nal(bytes, 7, 3).length;\n  },\n  'h265': function h265(bytes) {\n    // find video_parameter_set_rbsp or seq_parameter_set_rbsp\n    return findH265Nal(bytes, [32, 33], 3).length;\n  }\n}; // get all the isLikely functions\n// but make sure 'ts' is above h264 and h265\n// but below everything else as it is the least specific\n\nvar isLikelyTypes = Object.keys(_isLikely) // remove ts, h264, h265\n.filter(function (t) {\n  return t !== 'ts' && t !== 'h264' && t !== 'h265';\n}) // add it back to the bottom\n.concat(['ts', 'h264', 'h265']); // make sure we are dealing with uint8 data.\n\nisLikelyTypes.forEach(function (type) {\n  var isLikelyFn = _isLikely[type];\n  _isLikely[type] = function (bytes) {\n    return isLikelyFn(toUint8(bytes));\n  };\n}); // export after wrapping\n\nexport var isLikely = _isLikely; // A useful list of file signatures can be found here\n// https://en.wikipedia.org/wiki/List_of_file_signatures\n\nexport var detectContainerForBytes = function detectContainerForBytes(bytes) {\n  bytes = toUint8(bytes);\n  for (var i = 0; i < isLikelyTypes.length; i++) {\n    var type = isLikelyTypes[i];\n    if (isLikely[type](bytes)) {\n      return type;\n    }\n  }\n  return '';\n}; // fmp4 is not a container\n\nexport var isLikelyFmp4MediaSegment = function isLikelyFmp4MediaSegment(bytes) {\n  return findBox(bytes, ['moof']).length > 0;\n};", "map": {"version": 3, "names": ["toUint8", "bytesMatch", "findBox", "findEbml", "EBML_TAGS", "getId3Offset", "findH264Nal", "findH265Nal", "CONSTANTS", "_isLikely", "aac", "bytes", "offset", "mask", "mp3", "webm", "docType", "EBML", "DocType", "mkv", "<PERSON><PERSON><PERSON>", "mp4", "mov", "fmp4", "moof", "moov", "gp", "ac3", "ts", "length", "i", "flac", "ogg", "avi", "riff", "wav", "h264", "h265", "isLikelyTypes", "Object", "keys", "filter", "t", "concat", "for<PERSON>ach", "type", "isLikelyFn", "isLikely", "detectContainerForBytes", "isLikelyFmp4MediaSegment"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@videojs/vhs-utils/es/containers.js"], "sourcesContent": ["import { toUint8, bytesMatch } from './byte-helpers.js';\nimport { findBox } from './mp4-helpers.js';\nimport { findEbml, EBML_TAGS } from './ebml-helpers.js';\nimport { getId3Offset } from './id3-helpers.js';\nimport { findH264Nal, findH265Nal } from './nal-helpers.js';\nvar CONSTANTS = {\n  // \"webm\" string literal in hex\n  'webm': toUint8([0x77, 0x65, 0x62, 0x6d]),\n  // \"matroska\" string literal in hex\n  'matroska': toUint8([0x6d, 0x61, 0x74, 0x72, 0x6f, 0x73, 0x6b, 0x61]),\n  // \"fLaC\" string literal in hex\n  'flac': toUint8([0x66, 0x4c, 0x61, 0x43]),\n  // \"OggS\" string literal in hex\n  'ogg': toUint8([0x4f, 0x67, 0x67, 0x53]),\n  // ac-3 sync byte, also works for ec-3 as that is simply a codec\n  // of ac-3\n  'ac3': toUint8([0x0b, 0x77]),\n  // \"RIFF\" string literal in hex used for wav and avi\n  'riff': toUint8([0x52, 0x49, 0x46, 0x46]),\n  // \"AVI\" string literal in hex\n  'avi': toUint8([0x41, 0x56, 0x49]),\n  // \"WAVE\" string literal in hex\n  'wav': toUint8([0x57, 0x41, 0x56, 0x45]),\n  // \"ftyp3g\" string literal in hex\n  '3gp': toUint8([0x66, 0x74, 0x79, 0x70, 0x33, 0x67]),\n  // \"ftyp\" string literal in hex\n  'mp4': toUint8([0x66, 0x74, 0x79, 0x70]),\n  // \"styp\" string literal in hex\n  'fmp4': toUint8([0x73, 0x74, 0x79, 0x70]),\n  // \"ftypqt\" string literal in hex\n  'mov': toUint8([0x66, 0x74, 0x79, 0x70, 0x71, 0x74]),\n  // moov string literal in hex\n  'moov': toUint8([0x6D, 0x6F, 0x6F, 0x76]),\n  // moof string literal in hex\n  'moof': toUint8([0x6D, 0x6F, 0x6F, 0x66])\n};\nvar _isLikely = {\n  aac: function aac(bytes) {\n    var offset = getId3Offset(bytes);\n    return bytesMatch(bytes, [0xFF, 0x10], {\n      offset: offset,\n      mask: [0xFF, 0x16]\n    });\n  },\n  mp3: function mp3(bytes) {\n    var offset = getId3Offset(bytes);\n    return bytesMatch(bytes, [0xFF, 0x02], {\n      offset: offset,\n      mask: [0xFF, 0x06]\n    });\n  },\n  webm: function webm(bytes) {\n    var docType = findEbml(bytes, [EBML_TAGS.EBML, EBML_TAGS.DocType])[0]; // check if DocType EBML tag is webm\n\n    return bytesMatch(docType, CONSTANTS.webm);\n  },\n  mkv: function mkv(bytes) {\n    var docType = findEbml(bytes, [EBML_TAGS.EBML, EBML_TAGS.DocType])[0]; // check if DocType EBML tag is matroska\n\n    return bytesMatch(docType, CONSTANTS.matroska);\n  },\n  mp4: function mp4(bytes) {\n    // if this file is another base media file format, it is not mp4\n    if (_isLikely['3gp'](bytes) || _isLikely.mov(bytes)) {\n      return false;\n    } // if this file starts with a ftyp or styp box its mp4\n\n\n    if (bytesMatch(bytes, CONSTANTS.mp4, {\n      offset: 4\n    }) || bytesMatch(bytes, CONSTANTS.fmp4, {\n      offset: 4\n    })) {\n      return true;\n    } // if this file starts with a moof/moov box its mp4\n\n\n    if (bytesMatch(bytes, CONSTANTS.moof, {\n      offset: 4\n    }) || bytesMatch(bytes, CONSTANTS.moov, {\n      offset: 4\n    })) {\n      return true;\n    }\n  },\n  mov: function mov(bytes) {\n    return bytesMatch(bytes, CONSTANTS.mov, {\n      offset: 4\n    });\n  },\n  '3gp': function gp(bytes) {\n    return bytesMatch(bytes, CONSTANTS['3gp'], {\n      offset: 4\n    });\n  },\n  ac3: function ac3(bytes) {\n    var offset = getId3Offset(bytes);\n    return bytesMatch(bytes, CONSTANTS.ac3, {\n      offset: offset\n    });\n  },\n  ts: function ts(bytes) {\n    if (bytes.length < 189 && bytes.length >= 1) {\n      return bytes[0] === 0x47;\n    }\n\n    var i = 0; // check the first 376 bytes for two matching sync bytes\n\n    while (i + 188 < bytes.length && i < 188) {\n      if (bytes[i] === 0x47 && bytes[i + 188] === 0x47) {\n        return true;\n      }\n\n      i += 1;\n    }\n\n    return false;\n  },\n  flac: function flac(bytes) {\n    var offset = getId3Offset(bytes);\n    return bytesMatch(bytes, CONSTANTS.flac, {\n      offset: offset\n    });\n  },\n  ogg: function ogg(bytes) {\n    return bytesMatch(bytes, CONSTANTS.ogg);\n  },\n  avi: function avi(bytes) {\n    return bytesMatch(bytes, CONSTANTS.riff) && bytesMatch(bytes, CONSTANTS.avi, {\n      offset: 8\n    });\n  },\n  wav: function wav(bytes) {\n    return bytesMatch(bytes, CONSTANTS.riff) && bytesMatch(bytes, CONSTANTS.wav, {\n      offset: 8\n    });\n  },\n  'h264': function h264(bytes) {\n    // find seq_parameter_set_rbsp\n    return findH264Nal(bytes, 7, 3).length;\n  },\n  'h265': function h265(bytes) {\n    // find video_parameter_set_rbsp or seq_parameter_set_rbsp\n    return findH265Nal(bytes, [32, 33], 3).length;\n  }\n}; // get all the isLikely functions\n// but make sure 'ts' is above h264 and h265\n// but below everything else as it is the least specific\n\nvar isLikelyTypes = Object.keys(_isLikely) // remove ts, h264, h265\n.filter(function (t) {\n  return t !== 'ts' && t !== 'h264' && t !== 'h265';\n}) // add it back to the bottom\n.concat(['ts', 'h264', 'h265']); // make sure we are dealing with uint8 data.\n\nisLikelyTypes.forEach(function (type) {\n  var isLikelyFn = _isLikely[type];\n\n  _isLikely[type] = function (bytes) {\n    return isLikelyFn(toUint8(bytes));\n  };\n}); // export after wrapping\n\nexport var isLikely = _isLikely; // A useful list of file signatures can be found here\n// https://en.wikipedia.org/wiki/List_of_file_signatures\n\nexport var detectContainerForBytes = function detectContainerForBytes(bytes) {\n  bytes = toUint8(bytes);\n\n  for (var i = 0; i < isLikelyTypes.length; i++) {\n    var type = isLikelyTypes[i];\n\n    if (isLikely[type](bytes)) {\n      return type;\n    }\n  }\n\n  return '';\n}; // fmp4 is not a container\n\nexport var isLikelyFmp4MediaSegment = function isLikelyFmp4MediaSegment(bytes) {\n  return findBox(bytes, ['moof']).length > 0;\n};"], "mappings": "AAAA,SAASA,OAAO,EAAEC,UAAU,QAAQ,mBAAmB;AACvD,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SAASC,QAAQ,EAAEC,SAAS,QAAQ,mBAAmB;AACvD,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,IAAIC,SAAS,GAAG;EACd;EACA,MAAM,EAAER,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACzC;EACA,UAAU,EAAEA,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACrE;EACA,MAAM,EAAEA,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACzC;EACA,KAAK,EAAEA,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACxC;EACA;EACA,KAAK,EAAEA,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;EAC5B;EACA,MAAM,EAAEA,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACzC;EACA,KAAK,EAAEA,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EAClC;EACA,KAAK,EAAEA,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACxC;EACA,KAAK,EAAEA,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACpD;EACA,KAAK,EAAEA,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACxC;EACA,MAAM,EAAEA,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACzC;EACA,KAAK,EAAEA,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACpD;EACA,MAAM,EAAEA,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACzC;EACA,MAAM,EAAEA,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AAC1C,CAAC;AACD,IAAIS,SAAS,GAAG;EACdC,GAAG,EAAE,SAASA,GAAGA,CAACC,KAAK,EAAE;IACvB,IAAIC,MAAM,GAAGP,YAAY,CAACM,KAAK,CAAC;IAChC,OAAOV,UAAU,CAACU,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;MACrCC,MAAM,EAAEA,MAAM;MACdC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI;IACnB,CAAC,CAAC;EACJ,CAAC;EACDC,GAAG,EAAE,SAASA,GAAGA,CAACH,KAAK,EAAE;IACvB,IAAIC,MAAM,GAAGP,YAAY,CAACM,KAAK,CAAC;IAChC,OAAOV,UAAU,CAACU,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;MACrCC,MAAM,EAAEA,MAAM;MACdC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI;IACnB,CAAC,CAAC;EACJ,CAAC;EACDE,IAAI,EAAE,SAASA,IAAIA,CAACJ,KAAK,EAAE;IACzB,IAAIK,OAAO,GAAGb,QAAQ,CAACQ,KAAK,EAAE,CAACP,SAAS,CAACa,IAAI,EAAEb,SAAS,CAACc,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEvE,OAAOjB,UAAU,CAACe,OAAO,EAAER,SAAS,CAACO,IAAI,CAAC;EAC5C,CAAC;EACDI,GAAG,EAAE,SAASA,GAAGA,CAACR,KAAK,EAAE;IACvB,IAAIK,OAAO,GAAGb,QAAQ,CAACQ,KAAK,EAAE,CAACP,SAAS,CAACa,IAAI,EAAEb,SAAS,CAACc,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEvE,OAAOjB,UAAU,CAACe,OAAO,EAAER,SAAS,CAACY,QAAQ,CAAC;EAChD,CAAC;EACDC,GAAG,EAAE,SAASA,GAAGA,CAACV,KAAK,EAAE;IACvB;IACA,IAAIF,SAAS,CAAC,KAAK,CAAC,CAACE,KAAK,CAAC,IAAIF,SAAS,CAACa,GAAG,CAACX,KAAK,CAAC,EAAE;MACnD,OAAO,KAAK;IACd,CAAC,CAAC;;IAGF,IAAIV,UAAU,CAACU,KAAK,EAAEH,SAAS,CAACa,GAAG,EAAE;MACnCT,MAAM,EAAE;IACV,CAAC,CAAC,IAAIX,UAAU,CAACU,KAAK,EAAEH,SAAS,CAACe,IAAI,EAAE;MACtCX,MAAM,EAAE;IACV,CAAC,CAAC,EAAE;MACF,OAAO,IAAI;IACb,CAAC,CAAC;;IAGF,IAAIX,UAAU,CAACU,KAAK,EAAEH,SAAS,CAACgB,IAAI,EAAE;MACpCZ,MAAM,EAAE;IACV,CAAC,CAAC,IAAIX,UAAU,CAACU,KAAK,EAAEH,SAAS,CAACiB,IAAI,EAAE;MACtCb,MAAM,EAAE;IACV,CAAC,CAAC,EAAE;MACF,OAAO,IAAI;IACb;EACF,CAAC;EACDU,GAAG,EAAE,SAASA,GAAGA,CAACX,KAAK,EAAE;IACvB,OAAOV,UAAU,CAACU,KAAK,EAAEH,SAAS,CAACc,GAAG,EAAE;MACtCV,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EACD,KAAK,EAAE,SAASc,EAAEA,CAACf,KAAK,EAAE;IACxB,OAAOV,UAAU,CAACU,KAAK,EAAEH,SAAS,CAAC,KAAK,CAAC,EAAE;MACzCI,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EACDe,GAAG,EAAE,SAASA,GAAGA,CAAChB,KAAK,EAAE;IACvB,IAAIC,MAAM,GAAGP,YAAY,CAACM,KAAK,CAAC;IAChC,OAAOV,UAAU,CAACU,KAAK,EAAEH,SAAS,CAACmB,GAAG,EAAE;MACtCf,MAAM,EAAEA;IACV,CAAC,CAAC;EACJ,CAAC;EACDgB,EAAE,EAAE,SAASA,EAAEA,CAACjB,KAAK,EAAE;IACrB,IAAIA,KAAK,CAACkB,MAAM,GAAG,GAAG,IAAIlB,KAAK,CAACkB,MAAM,IAAI,CAAC,EAAE;MAC3C,OAAOlB,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI;IAC1B;IAEA,IAAImB,CAAC,GAAG,CAAC,CAAC,CAAC;;IAEX,OAAOA,CAAC,GAAG,GAAG,GAAGnB,KAAK,CAACkB,MAAM,IAAIC,CAAC,GAAG,GAAG,EAAE;MACxC,IAAInB,KAAK,CAACmB,CAAC,CAAC,KAAK,IAAI,IAAInB,KAAK,CAACmB,CAAC,GAAG,GAAG,CAAC,KAAK,IAAI,EAAE;QAChD,OAAO,IAAI;MACb;MAEAA,CAAC,IAAI,CAAC;IACR;IAEA,OAAO,KAAK;EACd,CAAC;EACDC,IAAI,EAAE,SAASA,IAAIA,CAACpB,KAAK,EAAE;IACzB,IAAIC,MAAM,GAAGP,YAAY,CAACM,KAAK,CAAC;IAChC,OAAOV,UAAU,CAACU,KAAK,EAAEH,SAAS,CAACuB,IAAI,EAAE;MACvCnB,MAAM,EAAEA;IACV,CAAC,CAAC;EACJ,CAAC;EACDoB,GAAG,EAAE,SAASA,GAAGA,CAACrB,KAAK,EAAE;IACvB,OAAOV,UAAU,CAACU,KAAK,EAAEH,SAAS,CAACwB,GAAG,CAAC;EACzC,CAAC;EACDC,GAAG,EAAE,SAASA,GAAGA,CAACtB,KAAK,EAAE;IACvB,OAAOV,UAAU,CAACU,KAAK,EAAEH,SAAS,CAAC0B,IAAI,CAAC,IAAIjC,UAAU,CAACU,KAAK,EAAEH,SAAS,CAACyB,GAAG,EAAE;MAC3ErB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EACDuB,GAAG,EAAE,SAASA,GAAGA,CAACxB,KAAK,EAAE;IACvB,OAAOV,UAAU,CAACU,KAAK,EAAEH,SAAS,CAAC0B,IAAI,CAAC,IAAIjC,UAAU,CAACU,KAAK,EAAEH,SAAS,CAAC2B,GAAG,EAAE;MAC3EvB,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EACD,MAAM,EAAE,SAASwB,IAAIA,CAACzB,KAAK,EAAE;IAC3B;IACA,OAAOL,WAAW,CAACK,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAACkB,MAAM;EACxC,CAAC;EACD,MAAM,EAAE,SAASQ,IAAIA,CAAC1B,KAAK,EAAE;IAC3B;IACA,OAAOJ,WAAW,CAACI,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAACkB,MAAM;EAC/C;AACF,CAAC,CAAC,CAAC;AACH;AACA;;AAEA,IAAIS,aAAa,GAAGC,MAAM,CAACC,IAAI,CAAC/B,SAAS,CAAC,CAAC;AAAA,CAC1CgC,MAAM,CAAC,UAAUC,CAAC,EAAE;EACnB,OAAOA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,MAAM,IAAIA,CAAC,KAAK,MAAM;AACnD,CAAC,CAAC,CAAC;AAAA,CACFC,MAAM,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;;AAEjCL,aAAa,CAACM,OAAO,CAAC,UAAUC,IAAI,EAAE;EACpC,IAAIC,UAAU,GAAGrC,SAAS,CAACoC,IAAI,CAAC;EAEhCpC,SAAS,CAACoC,IAAI,CAAC,GAAG,UAAUlC,KAAK,EAAE;IACjC,OAAOmC,UAAU,CAAC9C,OAAO,CAACW,KAAK,CAAC,CAAC;EACnC,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;;AAEJ,OAAO,IAAIoC,QAAQ,GAAGtC,SAAS,CAAC,CAAC;AACjC;;AAEA,OAAO,IAAIuC,uBAAuB,GAAG,SAASA,uBAAuBA,CAACrC,KAAK,EAAE;EAC3EA,KAAK,GAAGX,OAAO,CAACW,KAAK,CAAC;EAEtB,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,aAAa,CAACT,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC7C,IAAIe,IAAI,GAAGP,aAAa,CAACR,CAAC,CAAC;IAE3B,IAAIiB,QAAQ,CAACF,IAAI,CAAC,CAAClC,KAAK,CAAC,EAAE;MACzB,OAAOkC,IAAI;IACb;EACF;EAEA,OAAO,EAAE;AACX,CAAC,CAAC,CAAC;;AAEH,OAAO,IAAII,wBAAwB,GAAG,SAASA,wBAAwBA,CAACtC,KAAK,EAAE;EAC7E,OAAOT,OAAO,CAACS,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAACkB,MAAM,GAAG,CAAC;AAC5C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}