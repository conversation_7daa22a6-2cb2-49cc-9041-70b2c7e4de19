{"ast": null, "code": "import React,{useState}from'react';import{Form,Input,But<PERSON>,Card,Typography,Checkbox,message,Spin,Alert}from'antd';import{UserOutlined,LockOutlined}from'@ant-design/icons';import{useNavigate}from'react-router-dom';import styled from'styled-components';import axios from'axios';import{login}from'../services/auth';import'./Login.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title,Text}=Typography;// 设置 axios 默认配置\naxios.defaults.baseURL='http://localhost:5000';// 登录页面容器\nconst LoginContainer=styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n  position: relative;\n  overflow: hidden;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\n    opacity: 0.15;\n    z-index: 0;\n  }\n`;// 登录卡片样式\nconst LoginCard=styled(Card)`\n  width: 400px;\n  border-radius: 8px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\n  z-index: 1;\n  \n  .ant-card-body {\n    padding: 40px;\n  }\n`;// 标题容器\nconst TitleContainer=styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;// Logo样式\nconst Logo=styled.div`\n  width: 80px;\n  height: 80px;\n  margin: 0 auto 20px;\n  background: #1890ff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 40px;\n  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);\n`;// 表单样式\nconst StyledForm=styled(Form)`\n  .ant-form-item-label {\n    font-weight: 500;\n  }\n  \n  .ant-input-affix-wrapper {\n    padding: 12px;\n    border-radius: 6px;\n  }\n  \n  .ant-btn {\n    height: 45px;\n    border-radius: 6px;\n    font-weight: 500;\n  }\n`;// 底部文本样式\nconst FooterText=styled(Text)`\n  display: block;\n  text-align: center;\n  margin-top: 20px;\n  color: #8c8c8c;\n`;const Login=()=>{const[loading,setLoading]=useState(false);const[loginError,setLoginError]=useState('');const navigate=useNavigate();const[form]=Form.useForm();const onFinish=async values=>{setLoading(true);setLoginError('');apiUrl='http://localhost:5000/api';try{// 发送POST请求到登录API\n// const response = await axios.get(`${apiUrl}/api/login`);\nconst response=await axios.post(`${apiUrl}/login`,{// const response = await axios.post('/api/login', {\nusername:values.username,password:values.password});// 检查登录是否成功\nif(response.data.success){// 保存token和用户信息\nlocalStorage.setItem('token',response.data.data.token);localStorage.setItem('user',JSON.stringify(response.data.data.user));localStorage.setItem('isLoggedIn','true');message.success('登录成功！');// 跳转到主页面\nnavigate('/real-time-traffic');}else{// 服务器返回失败信息\nsetLoginError(response.data.message||'登录失败');message.error(response.data.message||'登录失败');}}catch(err){var _err$response,_err$response$data;console.error('登录错误:',err);const errorMsg=((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||'登录失败，请重试！';setLoginError(errorMsg);message.error(errorMsg);}finally{setLoading(false);}};const handleTestLogin=()=>{form.setFieldsValue({username:'admin',password:'admin123'});form.submit();};const directTestLogin=async()=>{setLoading(true);setLoginError('');try{// 模拟成功响应格式\nconst mockResponse={success:true,data:{token:'test-token-123',user:{id:1,username:'test',name:'测试用户',role:'admin'}}};// 保存token和用户信息\nlocalStorage.setItem('token',mockResponse.data.token);localStorage.setItem('user',JSON.stringify(mockResponse.data.user));localStorage.setItem('isLoggedIn','true');message.success('测试登录成功！');// 跳转到主页面\nnavigate('/real-time-traffic');}catch(err){var _err$response2,_err$response2$data;console.error('测试登录错误:',err);const errorMsg=((_err$response2=err.response)===null||_err$response2===void 0?void 0:(_err$response2$data=_err$response2.data)===null||_err$response2$data===void 0?void 0:_err$response2$data.message)||'测试登录失败，请重试！';setLoginError(errorMsg);message.error(errorMsg);}finally{setLoading(false);}};return/*#__PURE__*/_jsx(LoginContainer,{children:/*#__PURE__*/_jsx(Spin,{spinning:loading,tip:\"\\u767B\\u5F55\\u4E2D...\",children:/*#__PURE__*/_jsxs(LoginCard,{children:[/*#__PURE__*/_jsxs(TitleContainer,{children:[/*#__PURE__*/_jsx(Logo,{children:/*#__PURE__*/_jsx(\"i\",{className:\"fas fa-car-alt\"})}),/*#__PURE__*/_jsx(Title,{level:2,style:{margin:0,color:'#1890ff'},children:\"\\u667A\\u80FD\\u7F51\\u8054\\u4EA7\\u5B66\\u7814\\u4E91\\u5E73\\u53F0\"}),/*#__PURE__*/_jsx(Text,{type:\"secondary\",children:\"\\u767B\\u5F55\\u60A8\\u7684\\u8D26\\u6237\\u4EE5\\u7EE7\\u7EED\"})]}),loginError&&/*#__PURE__*/_jsx(Alert,{message:loginError,type:\"error\",showIcon:true,style:{marginBottom:24}}),/*#__PURE__*/_jsxs(StyledForm,{form:form,name:\"login\",initialValues:{remember:true},onFinish:onFinish,size:\"large\",children:[/*#__PURE__*/_jsx(Form.Item,{name:\"username\",rules:[{required:true,message:'请输入用户名!'}],children:/*#__PURE__*/_jsx(Input,{prefix:/*#__PURE__*/_jsx(UserOutlined,{style:{color:'#bfbfbf'}}),placeholder:\"\\u7528\\u6237\\u540D\"})}),/*#__PURE__*/_jsx(Form.Item,{name:\"password\",rules:[{required:true,message:'请输入密码!'}],children:/*#__PURE__*/_jsx(Input.Password,{prefix:/*#__PURE__*/_jsx(LockOutlined,{style:{color:'#bfbfbf'}}),placeholder:\"\\u5BC6\\u7801\"})}),/*#__PURE__*/_jsxs(Form.Item,{children:[/*#__PURE__*/_jsx(Form.Item,{name:\"remember\",valuePropName:\"checked\",noStyle:true,children:/*#__PURE__*/_jsx(Checkbox,{children:\"\\u8BB0\\u4F4F\\u6211\"})}),/*#__PURE__*/_jsx(\"a\",{style:{float:'right'},href:\"#\",children:\"\\u5FD8\\u8BB0\\u5BC6\\u7801?\"})]}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsx(Button,{type:\"primary\",htmlType:\"submit\",block:true,loading:loading,children:\"\\u767B\\u5F55\"})}),/*#__PURE__*/_jsx(Form.Item,{children:/*#__PURE__*/_jsx(Button,{type:\"link\",onClick:handleTestLogin,block:true})})]}),/*#__PURE__*/_jsxs(FooterText,{children:[\"\\xA9 \",new Date().getFullYear(),\" \\u5E0C\\u8FEA\\u667A\\u9A7E \\u7248\\u6743\\u6240\\u6709\"]})]})})});};export default Login;", "map": {"version": 3, "names": ["React", "useState", "Form", "Input", "<PERSON><PERSON>", "Card", "Typography", "Checkbox", "message", "Spin", "<PERSON><PERSON>", "UserOutlined", "LockOutlined", "useNavigate", "styled", "axios", "login", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Text", "defaults", "baseURL", "LoginContainer", "div", "LoginCard", "TitleC<PERSON>r", "Logo", "StyledForm", "FooterText", "<PERSON><PERSON>", "loading", "setLoading", "loginError", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "navigate", "form", "useForm", "onFinish", "values", "apiUrl", "response", "post", "username", "password", "data", "success", "localStorage", "setItem", "token", "JSON", "stringify", "user", "error", "err", "_err$response", "_err$response$data", "console", "errorMsg", "handleTestLogin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submit", "directTestLogin", "mockResponse", "id", "name", "role", "_err$response2", "_err$response2$data", "children", "spinning", "tip", "className", "level", "style", "margin", "color", "type", "showIcon", "marginBottom", "initialValues", "remember", "size", "<PERSON><PERSON>", "rules", "required", "prefix", "placeholder", "Password", "valuePropName", "noStyle", "float", "href", "htmlType", "block", "onClick", "Date", "getFullYear"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/Login.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Form, Input, But<PERSON>, Card, Typography, Checkbox, message, Spin, Alert } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport axios from 'axios';\nimport { login } from '../services/auth';\nimport './Login.css';\n\nconst { Title, Text } = Typography;\n\n// 设置 axios 默认配置\naxios.defaults.baseURL = 'http://localhost:5000';\n\n// 登录页面容器\nconst LoginContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n  position: relative;\n  overflow: hidden;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\n    opacity: 0.15;\n    z-index: 0;\n  }\n`;\n\n// 登录卡片样式\nconst LoginCard = styled(Card)`\n  width: 400px;\n  border-radius: 8px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\n  z-index: 1;\n  \n  .ant-card-body {\n    padding: 40px;\n  }\n`;\n\n// 标题容器\nconst TitleContainer = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n\n// Logo样式\nconst Logo = styled.div`\n  width: 80px;\n  height: 80px;\n  margin: 0 auto 20px;\n  background: #1890ff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 40px;\n  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);\n`;\n\n// 表单样式\nconst StyledForm = styled(Form)`\n  .ant-form-item-label {\n    font-weight: 500;\n  }\n  \n  .ant-input-affix-wrapper {\n    padding: 12px;\n    border-radius: 6px;\n  }\n  \n  .ant-btn {\n    height: 45px;\n    border-radius: 6px;\n    font-weight: 500;\n  }\n`;\n\n// 底部文本样式\nconst FooterText = styled(Text)`\n  display: block;\n  text-align: center;\n  margin-top: 20px;\n  color: #8c8c8c;\n`;\n\nconst Login = () => {\n  const [loading, setLoading] = useState(false);\n  const [loginError, setLoginError] = useState('');\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n\n  const onFinish = async (values) => {\n    setLoading(true);\n    setLoginError('');\n    apiUrl = 'http://localhost:5000/api';\n    try {\n      // 发送POST请求到登录API\n      // const response = await axios.get(`${apiUrl}/api/login`);\n      const response = await axios.post(`${apiUrl}/login`, {\n      // const response = await axios.post('/api/login', {\n        username: values.username,\n        password: values.password\n      });\n      \n      // 检查登录是否成功\n      if (response.data.success) {\n        // 保存token和用户信息\n        localStorage.setItem('token', response.data.data.token);\n        localStorage.setItem('user', JSON.stringify(response.data.data.user));\n        localStorage.setItem('isLoggedIn', 'true');\n        \n        message.success('登录成功！');\n        // 跳转到主页面\n        navigate('/real-time-traffic');\n      } else {\n        // 服务器返回失败信息\n        setLoginError(response.data.message || '登录失败');\n        message.error(response.data.message || '登录失败');\n      }\n    } catch (err) {\n      console.error('登录错误:', err);\n      const errorMsg = err.response?.data?.message || '登录失败，请重试！';\n      setLoginError(errorMsg);\n      message.error(errorMsg);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleTestLogin = () => {\n    form.setFieldsValue({\n      username: 'admin',\n      password: 'admin123'\n    });\n    form.submit();\n  };\n\n  const directTestLogin = async () => {\n    setLoading(true);\n    setLoginError('');\n    try {\n      // 模拟成功响应格式\n      const mockResponse = {\n        success: true,\n        data: {\n          token: 'test-token-123',\n          user: { \n            id: 1, \n            username: 'test', \n            name: '测试用户',\n            role: 'admin'\n          }\n        }\n      };\n      \n      // 保存token和用户信息\n      localStorage.setItem('token', mockResponse.data.token);\n      localStorage.setItem('user', JSON.stringify(mockResponse.data.user));\n      localStorage.setItem('isLoggedIn', 'true');\n      \n      message.success('测试登录成功！');\n      // 跳转到主页面\n      navigate('/real-time-traffic');\n    } catch (err) {\n      console.error('测试登录错误:', err);\n      const errorMsg = err.response?.data?.message || '测试登录失败，请重试！';\n      setLoginError(errorMsg);\n      message.error(errorMsg);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <LoginContainer>\n      <Spin spinning={loading} tip=\"登录中...\">\n        <LoginCard>\n          <TitleContainer>\n            <Logo>\n              <i className=\"fas fa-car-alt\" />\n            </Logo>\n            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>智能网联产学研云平台</Title>\n            <Text type=\"secondary\">登录您的账户以继续</Text>\n          </TitleContainer>\n          \n          {loginError && (\n            <Alert\n              message={loginError}\n              type=\"error\"\n              showIcon\n              style={{ marginBottom: 24 }}\n            />\n          )}\n          \n          <StyledForm\n            form={form}\n            name=\"login\"\n            initialValues={{ remember: true }}\n            onFinish={onFinish}\n            size=\"large\"\n          >\n            <Form.Item\n              name=\"username\"\n              rules={[{ required: true, message: '请输入用户名!' }]}\n            >\n              <Input \n                prefix={<UserOutlined style={{ color: '#bfbfbf' }} />} \n                placeholder=\"用户名\" \n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"password\"\n              rules={[{ required: true, message: '请输入密码!' }]}\n            >\n              <Input.Password \n                prefix={<LockOutlined style={{ color: '#bfbfbf' }} />} \n                placeholder=\"密码\" \n              />\n            </Form.Item>\n\n            <Form.Item>\n              <Form.Item name=\"remember\" valuePropName=\"checked\" noStyle>\n                <Checkbox>记住我</Checkbox>\n              </Form.Item>\n\n              <a style={{ float: 'right' }} href=\"#\">\n                忘记密码?\n              </a>\n            </Form.Item>\n\n            <Form.Item>\n              <Button type=\"primary\" htmlType=\"submit\" block loading={loading}>\n                登录\n              </Button>\n            </Form.Item>\n            \n            <Form.Item>\n              <Button type=\"link\" onClick={handleTestLogin} block>\n                {/* 测试登录 (admin/admin123) */}\n              </Button>\n            </Form.Item>\n          </StyledForm>\n          \n          <FooterText>\n            © {new Date().getFullYear()} 希迪智驾 版权所有\n          </FooterText>\n        </LoginCard>\n      </Spin>\n    </LoginContainer>\n  );\n};\n\nexport default Login; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,IAAI,CAAEC,KAAK,CAAEC,MAAM,CAAEC,IAAI,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,OAAO,CAAEC,IAAI,CAAEC,KAAK,KAAQ,MAAM,CAC5F,OAASC,YAAY,CAAEC,YAAY,KAAQ,mBAAmB,CAC9D,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,KAAK,KAAQ,kBAAkB,CACxC,MAAO,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErB,KAAM,CAAEC,KAAK,CAAEC,IAAK,CAAC,CAAGhB,UAAU,CAElC;AACAS,KAAK,CAACQ,QAAQ,CAACC,OAAO,CAAG,uBAAuB,CAEhD;AACA,KAAM,CAAAC,cAAc,CAAGX,MAAM,CAACY,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,SAAS,CAAGb,MAAM,CAACT,IAAI,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAuB,cAAc,CAAGd,MAAM,CAACY,GAAG;AACjC;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAG,IAAI,CAAGf,MAAM,CAACY,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAI,UAAU,CAAGhB,MAAM,CAACZ,IAAI,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAA6B,UAAU,CAAGjB,MAAM,CAACQ,IAAI,CAAC;AAC/B;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAU,KAAK,CAAGA,CAAA,GAAM,CAClB,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACkC,UAAU,CAAEC,aAAa,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAAoC,QAAQ,CAAGxB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACyB,IAAI,CAAC,CAAGpC,IAAI,CAACqC,OAAO,CAAC,CAAC,CAE7B,KAAM,CAAAC,QAAQ,CAAG,KAAO,CAAAC,MAAM,EAAK,CACjCP,UAAU,CAAC,IAAI,CAAC,CAChBE,aAAa,CAAC,EAAE,CAAC,CACjBM,MAAM,CAAG,2BAA2B,CACpC,GAAI,CACF;AACA;AACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA5B,KAAK,CAAC6B,IAAI,CAAC,GAAGF,MAAM,QAAQ,CAAE,CACrD;AACEG,QAAQ,CAAEJ,MAAM,CAACI,QAAQ,CACzBC,QAAQ,CAAEL,MAAM,CAACK,QACnB,CAAC,CAAC,CAEF;AACA,GAAIH,QAAQ,CAACI,IAAI,CAACC,OAAO,CAAE,CACzB;AACAC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAEP,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACI,KAAK,CAAC,CACvDF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAEE,IAAI,CAACC,SAAS,CAACV,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACO,IAAI,CAAC,CAAC,CACrEL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAE,MAAM,CAAC,CAE1C1C,OAAO,CAACwC,OAAO,CAAC,OAAO,CAAC,CACxB;AACAX,QAAQ,CAAC,oBAAoB,CAAC,CAChC,CAAC,IAAM,CACL;AACAD,aAAa,CAACO,QAAQ,CAACI,IAAI,CAACvC,OAAO,EAAI,MAAM,CAAC,CAC9CA,OAAO,CAAC+C,KAAK,CAACZ,QAAQ,CAACI,IAAI,CAACvC,OAAO,EAAI,MAAM,CAAC,CAChD,CACF,CAAE,MAAOgD,GAAG,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACZC,OAAO,CAACJ,KAAK,CAAC,OAAO,CAAEC,GAAG,CAAC,CAC3B,KAAM,CAAAI,QAAQ,CAAG,EAAAH,aAAA,CAAAD,GAAG,CAACb,QAAQ,UAAAc,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcV,IAAI,UAAAW,kBAAA,iBAAlBA,kBAAA,CAAoBlD,OAAO,GAAI,WAAW,CAC3D4B,aAAa,CAACwB,QAAQ,CAAC,CACvBpD,OAAO,CAAC+C,KAAK,CAACK,QAAQ,CAAC,CACzB,CAAC,OAAS,CACR1B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA2B,eAAe,CAAGA,CAAA,GAAM,CAC5BvB,IAAI,CAACwB,cAAc,CAAC,CAClBjB,QAAQ,CAAE,OAAO,CACjBC,QAAQ,CAAE,UACZ,CAAC,CAAC,CACFR,IAAI,CAACyB,MAAM,CAAC,CAAC,CACf,CAAC,CAED,KAAM,CAAAC,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC9B,UAAU,CAAC,IAAI,CAAC,CAChBE,aAAa,CAAC,EAAE,CAAC,CACjB,GAAI,CACF;AACA,KAAM,CAAA6B,YAAY,CAAG,CACnBjB,OAAO,CAAE,IAAI,CACbD,IAAI,CAAE,CACJI,KAAK,CAAE,gBAAgB,CACvBG,IAAI,CAAE,CACJY,EAAE,CAAE,CAAC,CACLrB,QAAQ,CAAE,MAAM,CAChBsB,IAAI,CAAE,MAAM,CACZC,IAAI,CAAE,OACR,CACF,CACF,CAAC,CAED;AACAnB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAEe,YAAY,CAAClB,IAAI,CAACI,KAAK,CAAC,CACtDF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAEE,IAAI,CAACC,SAAS,CAACY,YAAY,CAAClB,IAAI,CAACO,IAAI,CAAC,CAAC,CACpEL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAE,MAAM,CAAC,CAE1C1C,OAAO,CAACwC,OAAO,CAAC,SAAS,CAAC,CAC1B;AACAX,QAAQ,CAAC,oBAAoB,CAAC,CAChC,CAAE,MAAOmB,GAAG,CAAE,KAAAa,cAAA,CAAAC,mBAAA,CACZX,OAAO,CAACJ,KAAK,CAAC,SAAS,CAAEC,GAAG,CAAC,CAC7B,KAAM,CAAAI,QAAQ,CAAG,EAAAS,cAAA,CAAAb,GAAG,CAACb,QAAQ,UAAA0B,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAActB,IAAI,UAAAuB,mBAAA,iBAAlBA,mBAAA,CAAoB9D,OAAO,GAAI,aAAa,CAC7D4B,aAAa,CAACwB,QAAQ,CAAC,CACvBpD,OAAO,CAAC+C,KAAK,CAACK,QAAQ,CAAC,CACzB,CAAC,OAAS,CACR1B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACEhB,IAAA,CAACO,cAAc,EAAA8C,QAAA,cACbrD,IAAA,CAACT,IAAI,EAAC+D,QAAQ,CAAEvC,OAAQ,CAACwC,GAAG,CAAC,uBAAQ,CAAAF,QAAA,cACnCnD,KAAA,CAACO,SAAS,EAAA4C,QAAA,eACRnD,KAAA,CAACQ,cAAc,EAAA2C,QAAA,eACbrD,IAAA,CAACW,IAAI,EAAA0C,QAAA,cACHrD,IAAA,MAAGwD,SAAS,CAAC,gBAAgB,CAAE,CAAC,CAC5B,CAAC,cACPxD,IAAA,CAACG,KAAK,EAACsD,KAAK,CAAE,CAAE,CAACC,KAAK,CAAE,CAAEC,MAAM,CAAE,CAAC,CAAEC,KAAK,CAAE,SAAU,CAAE,CAAAP,QAAA,CAAC,8DAAU,CAAO,CAAC,cAC3ErD,IAAA,CAACI,IAAI,EAACyD,IAAI,CAAC,WAAW,CAAAR,QAAA,CAAC,wDAAS,CAAM,CAAC,EACzB,CAAC,CAEhBpC,UAAU,eACTjB,IAAA,CAACR,KAAK,EACJF,OAAO,CAAE2B,UAAW,CACpB4C,IAAI,CAAC,OAAO,CACZC,QAAQ,MACRJ,KAAK,CAAE,CAAEK,YAAY,CAAE,EAAG,CAAE,CAC7B,CACF,cAED7D,KAAA,CAACU,UAAU,EACTQ,IAAI,CAAEA,IAAK,CACX6B,IAAI,CAAC,OAAO,CACZe,aAAa,CAAE,CAAEC,QAAQ,CAAE,IAAK,CAAE,CAClC3C,QAAQ,CAAEA,QAAS,CACnB4C,IAAI,CAAC,OAAO,CAAAb,QAAA,eAEZrD,IAAA,CAAChB,IAAI,CAACmF,IAAI,EACRlB,IAAI,CAAC,UAAU,CACfmB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE/E,OAAO,CAAE,SAAU,CAAC,CAAE,CAAA+D,QAAA,cAEhDrD,IAAA,CAACf,KAAK,EACJqF,MAAM,cAAEtE,IAAA,CAACP,YAAY,EAACiE,KAAK,CAAE,CAAEE,KAAK,CAAE,SAAU,CAAE,CAAE,CAAE,CACtDW,WAAW,CAAC,oBAAK,CAClB,CAAC,CACO,CAAC,cAEZvE,IAAA,CAAChB,IAAI,CAACmF,IAAI,EACRlB,IAAI,CAAC,UAAU,CACfmB,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE/E,OAAO,CAAE,QAAS,CAAC,CAAE,CAAA+D,QAAA,cAE/CrD,IAAA,CAACf,KAAK,CAACuF,QAAQ,EACbF,MAAM,cAAEtE,IAAA,CAACN,YAAY,EAACgE,KAAK,CAAE,CAAEE,KAAK,CAAE,SAAU,CAAE,CAAE,CAAE,CACtDW,WAAW,CAAC,cAAI,CACjB,CAAC,CACO,CAAC,cAEZrE,KAAA,CAAClB,IAAI,CAACmF,IAAI,EAAAd,QAAA,eACRrD,IAAA,CAAChB,IAAI,CAACmF,IAAI,EAAClB,IAAI,CAAC,UAAU,CAACwB,aAAa,CAAC,SAAS,CAACC,OAAO,MAAArB,QAAA,cACxDrD,IAAA,CAACX,QAAQ,EAAAgE,QAAA,CAAC,oBAAG,CAAU,CAAC,CACf,CAAC,cAEZrD,IAAA,MAAG0D,KAAK,CAAE,CAAEiB,KAAK,CAAE,OAAQ,CAAE,CAACC,IAAI,CAAC,GAAG,CAAAvB,QAAA,CAAC,2BAEvC,CAAG,CAAC,EACK,CAAC,cAEZrD,IAAA,CAAChB,IAAI,CAACmF,IAAI,EAAAd,QAAA,cACRrD,IAAA,CAACd,MAAM,EAAC2E,IAAI,CAAC,SAAS,CAACgB,QAAQ,CAAC,QAAQ,CAACC,KAAK,MAAC/D,OAAO,CAAEA,OAAQ,CAAAsC,QAAA,CAAC,cAEjE,CAAQ,CAAC,CACA,CAAC,cAEZrD,IAAA,CAAChB,IAAI,CAACmF,IAAI,EAAAd,QAAA,cACRrD,IAAA,CAACd,MAAM,EAAC2E,IAAI,CAAC,MAAM,CAACkB,OAAO,CAAEpC,eAAgB,CAACmC,KAAK,MAE3C,CAAC,CACA,CAAC,EACF,CAAC,cAEb5E,KAAA,CAACW,UAAU,EAAAwC,QAAA,EAAC,OACR,CAAC,GAAI,CAAA2B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,oDAC9B,EAAY,CAAC,EACJ,CAAC,CACR,CAAC,CACO,CAAC,CAErB,CAAC,CAED,cAAe,CAAAnE,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}