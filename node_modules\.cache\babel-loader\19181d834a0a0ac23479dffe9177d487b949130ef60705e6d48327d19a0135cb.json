{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.3; // 滤波系数，值越小滤波效果越强（0-1之间）\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n  bsm: 'changli/cloud/v2x/obu/bsm',\n  rsm: 'changli/cloud/v2x/rsu/rsm',\n  scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',\n  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat' // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 添加位置滤波函数\nconst filterPosition = newPos => {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n};\n\n// 添加朝向滤波函数\nconst filterRotation = newRotation => {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\nconst CampusModel = ({\n  className,\n  onCurrentRSUChange,\n  selectedRSUs\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null); // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,\n    // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null,\n    phases: []\n  });\n\n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n\n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n\n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n\n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({\n    topic: '',\n    content: ''\n  });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    // 安全检查：确保场景和转换器存在\n    if (!scene || !converter.current) {\n      console.warn('无法处理MQTT消息：场景或坐标转换器未初始化或已被销毁');\n      return;\n    }\n    try {\n      // 检查消息是否为空\n      if (!message || message === '{}' || message === 'null') {\n        console.warn('接收到空消息或无效消息:', message);\n        return;\n      }\n      const payload = JSON.parse(message);\n      console.log('原始消息内容:', payload);\n\n      // 检查payload是否有效\n      if (!payload || !payload.data) {\n        console.warn('消息格式无效，缺少data属性:', payload);\n        return;\n      }\n\n      // 处理RSI消息\n      if (topic === MQTT_CONFIG.rsi) {\n        console.log('收到RSI消息:', payload);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        const rsiData = payload.data;\n\n        // 检查rsiData和必要属性是否存在\n        if (!rsiData || !rsiData.rsuId) {\n          console.warn('RSI消息缺少必要的rsuId字段:', rsiData);\n          return;\n        }\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 检查经纬度是否存在\n          if (!rsiData.posLong || !rsiData.posLat) {\n            console.warn('RSI消息缺少位置信息:', rsiData);\n            return;\n          }\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(parseFloat(rsiData.posLong), parseFloat(rsiData.posLat));\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          switch (eventType) {\n            case '401':\n              // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':\n              // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':\n              // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':\n              // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':\n              // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002':\n              // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':\n              // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        console.log('收到BSM消息:', payload);\n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n\n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        const newRotation = Math.PI - newState.heading * Math.PI / 180;\n\n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n\n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          newVehicleModel.position.copy(newPosition);\n          newVehicleModel.rotation.y = newRotation;\n          scene.add(newVehicleModel);\n\n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1',\n            // 设置为机动车类型\n            isMain: isMainVehicle\n          });\n          console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition);\n          const filteredRotation = filterRotation(newRotation);\n\n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n\n          console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n\n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 1秒\n\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n\n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        console.log('收到SPAT消息:', message);\n        try {\n          const payload = JSON.parse(message);\n\n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n\n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            console.log('忽略过期的SPAT消息:', {\n              设备MAC: deviceMac,\n              消息时间戳: messageTimestamp,\n              最新时间戳: lastTimestamp\n            });\n            return;\n          }\n\n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n\n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              console.log(`处理路口ID: ${interId} 的SPAT消息`);\n\n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? getDirectionFromCode(phase.trafficDirec) : getPhaseDirection(phaseId);\n\n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n\n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n\n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n\n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n\n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n\n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n\n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && (window.currentPopoverIdRef.current === modelKey || window.currentPopoverIdRef.current === String(modelKey) || window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n\n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        console.log('收到场景事件消息:', payload);\n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch (sceneType) {\n          case '2':\n            // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':\n            // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':\n            // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':\n            // 限速提醒\n            const speedLimit = sceneData.eventData1; // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':\n            // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':\n            // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':\n            // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':\n            // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':\n            // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':\n            // 信号灯优先\n            const priorityType = sceneData.eventData1; // 优先类型\n            const duration = sceneData.eventData2; // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: payload.type,\n        data: payload\n      });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n\n    // 如果已有连接，先关闭\n    if (mqttClientRef.current) {\n      try {\n        mqttClientRef.current.close();\n      } catch (e) {\n        console.error('关闭已有WebSocket连接失败:', e);\n      }\n      mqttClientRef.current = null;\n    }\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      // 检查组件是否已卸载\n      if (!scene) {\n        console.warn('组件已卸载，忽略WebSocket消息');\n        return;\n      }\n      try {\n        // 检查event.data是否为空\n        if (!event.data) {\n          console.warn('WebSocket接收到空消息');\n          return;\n        }\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息，确保消息格式正确\n        if (message.type === 'message' && message.topic) {\n          // 检查payload是否存在\n          if (!message.payload) {\n            console.warn('WebSocket消息缺少payload:', message);\n            return;\n          }\n\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n\n      // 检查组件是否已卸载\n      if (!scene) {\n        console.log('组件已卸载，不再尝试重连');\n        return;\n      }\n\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n\n        // 检查scene是否初始化\n        if (scene) {\n          scene.add(model);\n\n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } else {\n          console.error('无法添加模型：场景未初始化');\n        }\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 100, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n\n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n\n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach(lightObj => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n\n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n\n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n\n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n\n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n\n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n\n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {\n          // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n\n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = event => {\n        if (scene && cameraRef.current) {\n          console.log('检测到点击事件', event.clientX, event.clientY);\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        } else {\n          console.warn('点击事件处理失败: scene或camera未初始化');\n        }\n      };\n\n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n\n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n\n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({\n      color: 0x333333\n    });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n\n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({\n      color: 0x666666\n    });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n\n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          //\n          transparent: false,\n          opacity: 0.1,\n          // 几乎透明\n          depthWrite: false\n        });\n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0); // 放在红绿灯位置\n\n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n\n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        addClickHelpers();\n      }\n    }, 5500); // 延迟略长于debugTrafficLights\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n\n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n\n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        const firstIntersection = intersectionsData.intersections[0];\n        console.log('自动选择第一个路口:', firstIntersection.name);\n\n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(firstIntersection.name);\n        }, 2000);\n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u533A\\u57DF\\u9009\\u62E9\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1508,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u533A\\u57DF\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      options: intersectionsData.intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      },\n      value: selectedIntersection ? selectedIntersection.name : undefined\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1509,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1525,\n      columnNumber: 7\n    }, this), trafficLightPopover.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${trafficLightPopover.position.x}px`,\n        top: `${trafficLightPopover.position.y}px`,\n        transform: 'translate(-50%, -100%)',\n        zIndex: 1003,\n        backgroundColor: 'rgba(0, 0, 0, 0.85)',\n        color: 'white',\n        borderRadius: '4px',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n        padding: '0',\n        maxWidth: '240px',\n        // 缩小最大宽度\n        fontSize: '12px' // 缩小字体\n      },\n      children: [trafficLightPopover.content, /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          position: 'absolute',\n          top: '0px',\n          right: '0px',\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          fontSize: '12px',\n          cursor: 'pointer',\n          padding: '2px 6px'\n        },\n        onClick: () => handleClosePopover(setTrafficLightPopover),\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1546,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1529,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1566,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1576,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1565,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"hIF9vxbJ25JDaBhcSTMJebTgTk8=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    try {\n      const [vehicleGltf, cyclistGltf, peopleGltf, trafficLightGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`), loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)]);\n\n      // 处理机动车模型\n      preloadedVehicleModel = vehicleGltf.scene;\n      preloadedVehicleModel.traverse(child => {\n        if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n            color: 0xffffff,\n            metalness: 0.2,\n            roughness: 0.1,\n            envMapIntensity: 1.0\n          });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n        }\n      });\n\n      // 处理非机动车模型\n      preloadedCyclistModel = cyclistGltf.scene;\n      // 设置非机动车模型的缩放\n      preloadedCyclistModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedCyclistModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n\n      // 处理行人模型\n      preloadedPeopleModel = peopleGltf.scene;\n      // 设置行人模型的缩放\n      preloadedPeopleModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedPeopleModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n\n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n\n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n\n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = type => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 安全检查：确保场景存在\n  if (!scene) {\n    console.warn('无法显示警告标记：场景未初始化或已被销毁');\n    return;\n  }\n  try {\n    // 创建一个新的警告标记\n    const sprite = createTextSprite(text);\n    sprite.position.set(position.x, 10, -position.y); // 设置位置，稍微升高以便可见\n\n    // 为标记添加一个定时器，在5秒后自动移除\n    setTimeout(() => {\n      if (scene && sprite && sprite.parent) {\n        scene.remove(sprite);\n      }\n    }, 500);\n\n    // 将标记添加到场景中\n    scene.add(sprite);\n    console.log('添加场景事件标记:', {\n      位置: position,\n      文本: text,\n      颜色: color\n    });\n  } catch (error) {\n    console.error('显示警告标记失败:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = converterInstance => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n\n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach(lightObj => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n\n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n\n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n\n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n\n        // *** 重要：设置为可被交互 ***\n        trafficLightModel.traverse(child => {\n          // 设置所有子对象可被点击\n          if (child.isMesh) {\n            // 确保材质能够被射线检测到\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n\n            // 设置较高的渲染顺序\n            child.renderOrder = 100;\n          }\n        });\n\n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n\n        // 添加一个专门用于点击的大型碰撞体\n        const colliderGeometry = new THREE.SphereGeometry(5, 12, 12);\n        const colliderMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          transparent: true,\n          opacity: 0.0,\n          // 完全透明\n          depthWrite: false\n        });\n        const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n        collider.name = `交通灯碰撞体-${intersection.name}`;\n        collider.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name,\n          isCollider: true\n        };\n        trafficLightModel.add(collider);\n\n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n\n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({\n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n\n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n\n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n\n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,\n    // 完全透明\n    depthWrite: false\n  });\n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  trafficLightModel.add(collider);\n\n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n\n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n\n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: 0xFF0000\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n\n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  trafficLightModel.add(lightMesh);\n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = converterInstance => {\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = phaseId => {\n  switch (phaseId) {\n    case '1':\n      return '北进口左转';\n    case '2':\n      return '北进口直行';\n    case '3':\n      return '北进口右转';\n    case '5':\n      return '东进口左转';\n    case '6':\n      return '东进口直行';\n    case '7':\n      return '东进口右转';\n    case '9':\n      return '南进口左转';\n    case '10':\n      return '南进口直行';\n    case '11':\n      return '南进口右转';\n    case '13':\n      return '西进口左转';\n    case '14':\n      return '西进口直行';\n    case '15':\n      return '西进口右转';\n    default:\n      return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = dirCode => {\n  switch (dirCode) {\n    case 'N':\n      return '北向南';\n    case 'S':\n      return '南向北';\n    case 'E':\n      return '东向西';\n    case 'W':\n      return '西向东';\n    case 'NE':\n      return '东北向西南';\n    case 'NW':\n      return '西北向东南';\n    case 'SE':\n      return '东南向西北';\n    case 'SW':\n      return '西南向东北';\n    default:\n      return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n\n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = (event.clientX - rect.left) / container.clientWidth * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n\n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 3;\n  raycaster.params.Line.threshold = 3;\n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  console.log('鼠标点击位置:', mouseX, mouseY);\n\n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n\n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'userData:', intersect.object.userData);\n    });\n\n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n\n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n\n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n\n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  console.log('射线检测到的对象数量:', intersects.length);\n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', 'userData:', obj.userData, '距离:', intersect.distance);\n    });\n\n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        console.log('检测到红绿灯点击, 路口ID:', interId);\n\n        // 显示弹窗\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n\n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n\n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.3; // 设置一个阈值，只有距离小于此值的才会被选中\n\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n\n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n\n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      console.log(`路口 ${interId} 的距离:`, distance);\n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = {\n          interId,\n          distance\n        };\n      }\n    }\n  });\n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n\n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  console.log('未检测到任何红绿灯点击');\n\n  // 如果用户点击了其他任何地方，关闭现有的弹窗\n  if (setPopoverState) {\n    setPopoverState(prev => {\n      if (prev.visible) {\n        return {\n          ...prev,\n          visible: false\n        };\n      }\n      return prev;\n    });\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = setPopoverState => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n\n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n\n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({\n    ...prev,\n    visible: false\n  }));\n  console.log('弹窗已关闭');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = interId => {\n  try {\n    var _document$querySelect, _document$querySelect2;\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n\n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      return false;\n    }\n\n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n\n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 创建弹出窗口内容\n    let content;\n    if (stateInfo && stateInfo.phases) {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '220px',\n          maxHeight: '300px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          },\n          children: [intersection.name, \" (ID: \", interId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                break;\n            }\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '6px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '4px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: getPhaseDirection(phase.phaseId)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2289,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2293,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 3px',\n                    borderRadius: '2px'\n                  },\n                  children: phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2294,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2292,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2305,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2306,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2304,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2282,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '6px',\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2262,\n        columnNumber: 9\n      }, this);\n    } else {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          maxWidth: '200px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '8px'\n          },\n          children: intersection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u8DEF\\u53E3ID: \", interId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2322,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2319,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2;\n    const centerY = window.innerHeight / 2;\n\n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = (_document$querySelect = document.querySelector('#root')) === null || _document$querySelect === void 0 ? void 0 : (_document$querySelect2 = _document$querySelect.__REACT_INSTANCE) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.setTrafficLightPopover;\n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: {\n          x: centerX,\n          y: centerY\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      document.body.appendChild(popover);\n\n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  return list;\n};\n\n// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n\n//   console.log('开始调试红绿灯模型...');\n\n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n\n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n\n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n\n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n\n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n\n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n\n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n\n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n\n//       // 添加到场景\n//       scene.add(highlightMesh);\n\n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n\n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n\n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n\n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = interId => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    console.log('当前trafficLightStates大小:', trafficLightStates.size);\n\n    // 如果没有指定ID，则使用第一个红绿灯\n    if (!interId && trafficLightsMap.size > 0) {\n      interId = String(Array.from(trafficLightsMap.keys())[0]);\n      console.log('未指定ID，使用第一个红绿灯ID:', interId);\n    }\n\n    // 输出所有可用的红绿灯ID用于调试\n    console.log('所有可用的红绿灯ID:');\n    trafficLightsMap.forEach((light, id) => {\n      var _light$intersection;\n      console.log(`- ${id} (${typeof id}): ${((_light$intersection = light.intersection) === null || _light$intersection === void 0 ? void 0 : _light$intersection.name) || '未知'}`);\n    });\n\n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n\n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n\n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n\n    // 创建更新函数\n    const updateTrafficLightPopover = () => {\n      var _window$currentPopove;\n      // 确保当前弹窗仍然可见\n      if (!window._setTrafficLightPopover) return;\n      const currentId = (_window$currentPopove = window.currentPopoverIdRef) === null || _window$currentPopove === void 0 ? void 0 : _window$currentPopove.current;\n      if (!currentId) return;\n\n      // 重新获取最新的状态信息\n      let stateInfo = trafficLightStates.get(currentId);\n      if (!stateInfo && typeof currentId === 'string') {\n        // 尝试使用数字ID\n        stateInfo = trafficLightStates.get(parseInt(currentId));\n      } else if (!stateInfo && typeof currentId === 'number') {\n        // 尝试使用字符串ID\n        stateInfo = trafficLightStates.get(String(currentId));\n      }\n      if (!stateInfo || !stateInfo.phases || stateInfo.phases.length === 0) {\n        console.log(`路口ID ${currentId} 没有状态信息，跳过更新`);\n        return;\n      }\n\n      // 获取交通灯信息\n      const intersectionLight = trafficLightsMap.get(currentId) || (typeof currentId === 'string' ? trafficLightsMap.get(parseInt(currentId)) : trafficLightsMap.get(String(currentId)));\n      if (!intersectionLight) {\n        console.error(`找不到路口ID ${currentId} 的红绿灯信息，无法更新弹窗`);\n        return;\n      }\n      const intersection = intersectionLight.intersection;\n\n      // 创建更新的弹窗内容\n      const content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '220px',\n          maxHeight: '300px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          },\n          children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", currentId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2591,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            let lightText;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                lightText = '绿灯';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                lightText = '黄灯';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                lightText = '红灯';\n                break;\n            }\n            const direction = getPhaseDirection(phase.phaseId);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '6px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '4px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: [direction, \" (\\u76F8\\u4F4DID: \", phase.phaseId, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2631,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2635,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 3px',\n                    borderRadius: '2px'\n                  },\n                  children: lightText\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2636,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2634,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2647,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2648,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2646,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2624,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2600,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '6px',\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date(stateInfo.updateTime).toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2654,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2590,\n        columnNumber: 9\n      }, this);\n\n      // 更新弹窗内容\n      window._setTrafficLightPopover(prev => ({\n        ...prev,\n        content: content,\n        phases: stateInfo.phases\n      }));\n      console.log(`已更新路口 ${(intersection === null || intersection === void 0 ? void 0 : intersection.name) || currentId} 的红绿灯状态弹窗`);\n    };\n\n    // 先执行一次初始更新\n    const updateInitialTrafficLightPopover = () => {\n      // 同样，查找红绿灯状态信息时也尝试字符串和数字两种类型\n      let stateInfo = trafficLightStates.get(interId);\n      if (!stateInfo) {\n        // 尝试使用数字ID\n        const numericId = parseInt(interId);\n        stateInfo = trafficLightStates.get(numericId);\n        if (stateInfo) {\n          console.log(`使用数字ID ${numericId} 找到了红绿灯状态信息`);\n        }\n      }\n      console.log(`路口ID ${interId} 的状态信息:`, stateInfo);\n      const intersection = trafficLight.intersection;\n      console.log('路口信息:', intersection);\n\n      // 创建弹窗内容\n      let content;\n      if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n        // 添加一个检查相位数据的打印\n        console.log('相位数据详情:');\n        stateInfo.phases.forEach((phase, index) => {\n          console.log(`相位 ${index + 1}: ID=${phase.phaseId}, 状态=${phase.trafficLight}, 剩余时间=${phase.remainTime}`);\n        });\n        content = /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '8px',\n            width: '220px',\n            maxHeight: '300px',\n            overflowY: 'auto'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            },\n            children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", interId, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2701,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              let lightText;\n              switch (phase.trafficLight) {\n                case 'G':\n                  lightColor = '#00ff00';\n                  lightText = '绿灯';\n                  break;\n                case 'Y':\n                  lightColor = '#ffff00';\n                  lightText = '黄灯';\n                  break;\n                case 'R':\n                default:\n                  lightColor = '#ff0000';\n                  lightText = '红灯';\n                  break;\n              }\n              const direction = getPhaseDirection(phase.phaseId);\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '6px',\n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [direction, \" (\\u76F8\\u4F4DID: \", phase.phaseId, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2741,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u706F\\u8272: \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2745,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: lightColor,\n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    },\n                    children: lightText\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2746,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2744,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u5012\\u8BA1\\u65F6: \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2757,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontWeight: 'bold'\n                    },\n                    children: [phase.remainTime, \" \\u79D2\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2758,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2756,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2734,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2710,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '6px',\n              fontSize: '10px',\n              color: '#888'\n            },\n            children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date(stateInfo.updateTime).toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2764,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2700,\n          columnNumber: 11\n        }, this);\n      } else {\n        // 如果没有状态信息，创建一个示例内容\n        content = /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '8px',\n            width: '220px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            },\n            children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", interId, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2773,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#ff4d4f',\n              fontSize: '12px'\n            },\n            children: \"\\u8BE5\\u8DEF\\u53E3\\u6682\\u65E0\\u7EA2\\u7EFF\\u706F\\u72B6\\u6001\\u4FE1\\u606F\\uFF0C\\u8BF7\\u7B49\\u5F85SPAT\\u6D88\\u606F\\u66F4\\u65B0\\u3002\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2782,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '6px',\n              fontSize: '10px',\n              color: '#888'\n            },\n            children: [\"\\u5F53\\u524D\\u65F6\\u95F4: \", new Date().toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2785,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2772,\n          columnNumber: 11\n        }, this);\n      }\n\n      // 设置弹窗位置在屏幕中央\n      const x = window.innerWidth / 2;\n      const y = window.innerHeight / 2;\n\n      // 更新弹窗状态\n      if (window._setTrafficLightPopover) {\n        var _stateInfo;\n        console.log('调用_setTrafficLightPopover更新弹窗状态', {\n          visible: true,\n          interId,\n          position: {\n            x,\n            y\n          }\n        });\n        window._setTrafficLightPopover({\n          visible: true,\n          interId: interId,\n          position: {\n            x,\n            y\n          },\n          content: content,\n          phases: ((_stateInfo = stateInfo) === null || _stateInfo === void 0 ? void 0 : _stateInfo.phases) || []\n        });\n        console.log(`已显示路口 ${(intersection === null || intersection === void 0 ? void 0 : intersection.name) || interId} 的红绿灯状态弹窗`);\n\n        // 设置定时更新\n        if (window.trafficLightUpdateTimerRef) {\n          window.trafficLightUpdateTimerRef.current = setInterval(updateTrafficLightPopover, TRAFFIC_LIGHT_UPDATE_INTERVAL * 1000);\n          console.log(`已设置红绿灯状态弹窗定时更新，间隔 ${TRAFFIC_LIGHT_UPDATE_INTERVAL} 秒`);\n        }\n        return true;\n      } else {\n        console.error('无法找到setTrafficLightPopover函数');\n        return false;\n      }\n    };\n    return updateInitialTrafficLightPopover();\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n\n//   console.log('开始生成模拟SPAT数据...');\n\n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n\n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n\n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n\n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n\n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n\n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n\n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n\n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n\n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n\n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n\n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n\n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n\n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n\n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n\n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n\n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = object => {\n  let current = object;\n\n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n\n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n\n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n\n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n\n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n\n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = (x - rect.left) / canvas.clientWidth * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    console.log('归一化坐标:', mouseX, mouseY);\n\n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n\n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n\n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'position:', intersect.object.position.toArray(), 'userData:', intersect.object.userData);\n\n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      return true;\n    }\n\n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', '类型:', obj.type, '位置:', obj.position.toArray(), '距离:', intersect.distance, 'userData:', obj.userData);\n    });\n\n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        var _lightObj$intersectio;\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n\n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n\n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n\n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        if (isVisible) {\n          visibleCount++;\n        }\n        console.log(`红绿灯 ${interId}:`, {\n          名称: ((_lightObj$intersectio = lightObj.intersection) === null || _lightObj$intersectio === void 0 ? void 0 : _lightObj$intersectio.name) || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n\n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  var _trafficLight$interse, _trafficLight$interse2, _trafficLight$interse3;\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch (phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n\n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: (_trafficLight$interse = trafficLight.intersection) === null || _trafficLight$interse === void 0 ? void 0 : _trafficLight$interse.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n\n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = {\n    isLight: true\n  };\n\n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  console.log(`更新路口 ${((_trafficLight$interse2 = trafficLight.intersection) === null || _trafficLight$interse2 === void 0 ? void 0 : _trafficLight$interse2.name) || ((_trafficLight$interse3 = trafficLight.intersection) === null || _trafficLight$interse3 === void 0 ? void 0 : _trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "Select", "Popover", "intersectionsData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "lastPosition", "lastRotation", "ALPHA", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "vehicleModels", "Map", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "console", "log", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "TRAFFIC_LIGHT_UPDATE_INTERVAL", "CampusModel", "className", "onCurrentRSUChange", "selectedRSUs", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "currentPopoverIdRef", "trafficLightUpdateTimerRef", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "updateMatrix", "updateMatrixWorld", "相机位置", "toArray", "目标点", "handleMqttMessage", "message", "warn", "payload", "JSON", "parse", "postMessage", "type", "rsiData", "rsuId", "events", "rtes", "for<PERSON>ach", "event", "eventId", "rteId", "eventType", "description", "startTime", "endTime", "posLong", "posLat", "modelPos", "warningText", "warningColor", "showWarningMarker", "事件ID", "事件类型", "事件说明", "开始时间", "结束时间", "位置", "bsmData", "bsmid", "newState", "partLong", "partLat", "partSpeed", "partHeading", "newPosition", "Vector3", "vehicleObj", "get", "newVehicleModel", "rotation", "add", "model", "lastUpdate", "Date", "now", "is<PERSON><PERSON>", "toFixed", "filteredPosition", "CLEANUP_THRESHOLD", "modelData", "id", "remove", "delete", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "设备MAC", "消息时间戳", "最新时间戳", "phasesInfo", "phase", "phaseId", "toString", "direction", "trafficDirec", "getDirectionFromCode", "getPhaseDirection", "lightState", "trafficLight", "remainTime", "parseInt", "phaseInfo", "push", "trafficLightKey", "String", "trafficLightModel", "updateTrafficLightVisual", "prev", "state", "<PERSON><PERSON><PERSON>", "strId", "has", "numId", "updateTime", "setTimeout", "showTrafficLightPopup", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "close", "e", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "stringify", "onerror", "onclose", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "map", "children", "length", "xhr", "loaded", "total", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "clearInterval", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "clear", "lightObj", "handleMainVehicleChange", "intervalId", "setInterval", "timer", "createTrafficLights", "clearTimeout", "handleClick", "clientX", "clientY", "handleMouseClick", "initScene", "createSimpleTrafficLight", "geometry", "BoxGeometry", "MeshBasicMaterial", "<PERSON><PERSON>", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "addClickHelpers", "helperGeometry", "SphereGeometry", "helperMaterial", "transparent", "opacity", "depthWrite", "helper<PERSON><PERSON>", "userData", "isClickHelper", "size", "firstIntersection", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "onChange", "options", "label", "bordered", "dropdownStyle", "maxHeight", "undefined", "ref", "height", "max<PERSON><PERSON><PERSON>", "right", "background", "onClick", "handleClosePopover", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "sprite", "Sprite", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "vehicleGltf", "cyclistGltf", "peopleGltf", "trafficLightGltf", "all", "loadAsync", "materia", "err", "types", "parent", "文本", "颜色", "converterInstance", "createFallbackTrafficLights", "renderOrder", "side", "DoubleSide", "depthTest", "needsUpdate", "colliderGeometry", "colliderMaterial", "collider", "isCollider", "lightGeometry", "lightMaterial", "<PERSON><PERSON><PERSON>", "dirCode", "container", "sceneInstance", "cameraInstance", "setPopoverState", "rect", "getBoundingClientRect", "mouseX", "clientWidth", "mouseY", "clientHeight", "raycaster", "Raycaster", "params", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "trafficLightObjects", "trafficLightIntersects", "intersectObjects", "intersect", "index", "object", "obj", "getTrafficLightFromObject", "correctId", "intersects", "closestLight", "worldPos", "getWorldPosition", "screenPos", "project", "dx", "dy", "sqrt", "testTrafficLightClick", "_document$querySelect", "_document$querySelect2", "light", "lightModel", "stateInfo", "overflowY", "marginBottom", "borderBottom", "paddingBottom", "lightColor", "justifyContent", "marginTop", "toLocaleTimeString", "centerX", "centerY", "__REACT_INSTANCE", "popover", "innerHTML", "body", "closeButton", "listTrafficLights", "list", "from", "keys", "_light$intersection", "numericId", "updateTrafficLightPopover", "_window$currentPopove", "currentId", "intersectionLight", "lightText", "updateInitialTrafficLightPopover", "_stateInfo", "testClickDetection", "tlIntersects", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "distanceToCamera", "distanceTo", "名称", "可见性", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "isLight", "emissive", "emissiveIntensity", "PointLight", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.3; // 滤波系数，值越小滤波效果越强（0-1之间）\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 添加位置滤波函数\nconst filterPosition = (newPos) => {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n};\n\n// 添加朝向滤波函数\nconst filterRotation = (newRotation) => {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);  // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n  \n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n  \n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n  \n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n  \n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    // 安全检查：确保场景和转换器存在\n    if (!scene || !converter.current) {\n      console.warn('无法处理MQTT消息：场景或坐标转换器未初始化或已被销毁');\n      return;\n    }\n    \n    try {\n      // 检查消息是否为空\n      if (!message || message === '{}' || message === 'null') {\n        console.warn('接收到空消息或无效消息:', message);\n        return;\n      }\n      \n      const payload = JSON.parse(message);\n      console.log('原始消息内容:', payload);\n      \n      // 检查payload是否有效\n      if (!payload || !payload.data) {\n        console.warn('消息格式无效，缺少data属性:', payload);\n        return;\n      }\n      \n      // 处理RSI消息\n      if (topic === MQTT_CONFIG.rsi) {\n        console.log('收到RSI消息:', payload);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        \n        const rsiData = payload.data;\n        \n        // 检查rsiData和必要属性是否存在\n        if (!rsiData || !rsiData.rsuId) {\n          console.warn('RSI消息缺少必要的rsuId字段:', rsiData);\n          return;\n        }\n        \n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 检查经纬度是否存在\n          if (!rsiData.posLong || !rsiData.posLat) {\n            console.warn('RSI消息缺少位置信息:', rsiData);\n            return;\n          }\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        \n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        console.log('收到BSM消息:', payload);\n        \n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n        \n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        const newRotation = Math.PI - newState.heading * Math.PI / 180;\n        \n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n        \n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        \n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          newVehicleModel.position.copy(newPosition);\n          newVehicleModel.rotation.y = newRotation;\n          scene.add(newVehicleModel);\n          \n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle\n          });\n          \n          console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition);\n          const filteredRotation = filterRotation(newRotation);\n          \n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n          \n          console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n        \n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 1秒\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        \n        return;\n      }\n      \n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        console.log('收到SPAT消息:', message);\n        \n        try {\n          const payload = JSON.parse(message);\n          \n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n          \n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n          \n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            console.log('忽略过期的SPAT消息:', {\n              设备MAC: deviceMac,\n              消息时间戳: messageTimestamp,\n              最新时间戳: lastTimestamp\n            });\n            return;\n          }\n          \n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n          \n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              \n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              \n              console.log(`处理路口ID: ${interId} 的SPAT消息`);\n              \n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                \n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  \n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? \n                    getDirectionFromCode(phase.trafficDirec) : \n                    getPhaseDirection(phaseId);\n                  \n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  \n                  console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n                  \n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n                  \n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n                  \n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  \n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  \n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n                    \n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n                \n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                \n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n                  \n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && \n                     (window.currentPopoverIdRef.current === modelKey || \n                      window.currentPopoverIdRef.current === String(modelKey) || \n                      window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    \n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n                    \n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        console.log('收到场景事件消息:', payload);\n        \n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: payload.type,\n        data: payload\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    // 如果已有连接，先关闭\n    if (mqttClientRef.current) {\n      try {\n        mqttClientRef.current.close();\n      } catch (e) {\n        console.error('关闭已有WebSocket连接失败:', e);\n      }\n      mqttClientRef.current = null;\n    }\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      // 检查组件是否已卸载\n      if (!scene) {\n        console.warn('组件已卸载，忽略WebSocket消息');\n        return;\n      }\n      \n      try {\n        // 检查event.data是否为空\n        if (!event.data) {\n          console.warn('WebSocket接收到空消息');\n          return;\n        }\n        \n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息，确保消息格式正确\n        if (message.type === 'message' && message.topic) {\n          // 检查payload是否存在\n          if (!message.payload) {\n            console.warn('WebSocket消息缺少payload:', message);\n            return;\n          }\n          \n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      \n      // 检查组件是否已卸载\n      if (!scene) {\n        console.log('组件已卸载，不再尝试重连');\n        return;\n      }\n      \n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          \n          // 检查scene是否初始化\n          if (scene) {\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          100,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n      \n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n      \n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach((lightObj) => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n      \n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n      \n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n    \n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n    \n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n    \n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n    \n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      \n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n  \n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          console.log('检测到点击事件', event.clientX, event.clientY);\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        } else {\n          console.warn('点击事件处理失败: scene或camera未初始化');\n        }\n      };\n      \n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n      \n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n      \n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n    \n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    \n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n    \n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,//\n          transparent: false,\n          opacity: 0.1,  // 几乎透明\n          depthWrite: false\n        });\n        \n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置\n        \n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n        \n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        \n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        addClickHelpers();\n      }\n    }, 5500);  // 延迟略长于debugTrafficLights\n    \n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n    \n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n  \n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        const firstIntersection = intersectionsData.intersections[0];\n        console.log('自动选择第一个路口:', firstIntersection.name);\n        \n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(firstIntersection.name);\n        }, 2000);\n        \n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n\n  return (\n    <>\n      <span style={labelStyle}>区域选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择区域位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n        value={selectedIntersection ? selectedIntersection.name : undefined}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      \n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '4px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n            padding: '0',\n            maxWidth: '240px', // 缩小最大宽度\n            fontSize: '12px' // 缩小字体\n          }}\n        >\n          {trafficLightPopover.content}\n          <button \n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '12px',\n              cursor: 'pointer',\n              padding: '2px 6px'\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n      \n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    try {\n      const [vehicleGltf, cyclistGltf, peopleGltf, trafficLightGltf] = await Promise.all([\n      loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)\n    ]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n      \n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse((child) => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n      }\n    });\n\n    console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n      \n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n        \n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 安全检查：确保场景存在\n  if (!scene) {\n    console.warn('无法显示警告标记：场景未初始化或已被销毁');\n    return;\n  }\n  \n  try {\n    // 创建一个新的警告标记\n    const sprite = createTextSprite(text);\n    sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n    \n    // 为标记添加一个定时器，在5秒后自动移除\n    setTimeout(() => {\n      if (scene && sprite && sprite.parent) {\n        scene.remove(sprite);\n      }\n    }, 500);\n    \n    // 将标记添加到场景中\n    scene.add(sprite);\n    \n    console.log('添加场景事件标记:', {\n      位置: position,\n      文本: text,\n      颜色: color\n    });\n  } catch (error) {\n    console.error('显示警告标记失败:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  \n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  \n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n  \n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n        \n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n        \n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n        \n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n        \n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n        \n        // *** 重要：设置为可被交互 ***\n        trafficLightModel.traverse(child => {\n          // 设置所有子对象可被点击\n          if (child.isMesh) {\n            // 确保材质能够被射线检测到\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            \n            // 设置较高的渲染顺序\n            child.renderOrder = 100;\n          }\n        });\n        \n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        \n        // 添加一个专门用于点击的大型碰撞体\n        const colliderGeometry = new THREE.SphereGeometry(5, 12, 12);\n        const colliderMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          transparent: true,\n          opacity: 0.0,  // 完全透明\n          depthWrite: false\n        });\n        \n        const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n        collider.name = `交通灯碰撞体-${intersection.name}`;\n        collider.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name,\n          isCollider: true\n        };\n        \n        trafficLightModel.add(collider);\n        \n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n        \n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        \n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n  \n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({ \n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n  \n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n  \n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n  \n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n  \n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,  // 完全透明\n    depthWrite: false\n  });\n  \n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  \n  trafficLightModel.add(collider);\n  \n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n  \n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n  \n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n  \n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  trafficLightModel.add(lightMesh);\n  \n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance) => {\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  switch(phaseId) {\n    case '1': return '北进口左转';\n    case '2': return '北进口直行';\n    case '3': return '北进口右转';\n    case '5': return '东进口左转';\n    case '6': return '东进口直行';\n    case '7': return '东进口右转';\n    case '9': return '南进口左转';\n    case '10': return '南进口直行';\n    case '11': return '南进口右转';\n    case '13': return '西进口左转';\n    case '14': return '西进口直行';\n    case '15': return '西进口右转';\n    default: return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = (dirCode) => {\n  switch(dirCode) {\n    case 'N': return '北向南';\n    case 'S': return '南向北';\n    case 'E': return '东向西';\n    case 'W': return '西向东';\n    case 'NE': return '东北向西南';\n    case 'NW': return '西北向东南';\n    case 'SE': return '东南向西北';\n    case 'SW': return '西南向东北';\n    default: return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  \n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n  \n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n  \n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 3;\n  raycaster.params.Line.threshold = 3;\n  \n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  \n  console.log('鼠标点击位置:', mouseX, mouseY);\n  \n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  \n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n  \n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  \n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, \n                 intersect.object.name || '无名称', \n                 '距离:', intersect.distance,\n                 'userData:', intersect.object.userData);\n    });\n    \n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    \n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n      \n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n      \n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n  \n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  \n  console.log('射线检测到的对象数量:', intersects.length);\n  \n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', \n                  'userData:', obj.userData, \n                  '距离:', intersect.distance);\n    });\n    \n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        console.log('检测到红绿灯点击, 路口ID:', interId);\n        \n        // 显示弹窗\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n  \n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n  \n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.3; // 设置一个阈值，只有距离小于此值的才会被选中\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n      \n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n      \n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      \n      console.log(`路口 ${interId} 的距离:`, distance);\n      \n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = { interId, distance };\n      }\n    }\n  });\n  \n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n    \n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  \n  console.log('未检测到任何红绿灯点击');\n  \n  // 如果用户点击了其他任何地方，关闭现有的弹窗\n  if (setPopoverState) {\n    setPopoverState(prev => {\n      if (prev.visible) {\n        return { ...prev, visible: false };\n      }\n      return prev;\n    });\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n  \n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n  \n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({ ...prev, visible: false }));\n  console.log('弹窗已关闭');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = (interId) => {\n  try {\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      \n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      \n      return false;\n    }\n    \n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n    \n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 创建弹出窗口内容\n    let content;\n    \n    if (stateInfo && stateInfo.phases) {\n      content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection.name} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              switch (phase.trafficLight) {\n                case 'G': lightColor = '#00ff00'; break;\n                case 'Y': lightColor = '#ffff00'; break;\n                case 'R': default: lightColor = '#ff0000'; break;\n              }\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {getPhaseDirection(phase.phaseId)}\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      content = (\n        <div style={{ padding: '8px', maxWidth: '200px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>\n          <div>路口ID: {interId}</div>\n          <div>当前无信号灯状态信息</div>\n        </div>\n      );\n    }\n    \n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2;\n    const centerY = window.innerHeight / 2;\n    \n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;\n    \n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: { x: centerX, y: centerY },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      \n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      \n      document.body.appendChild(popover);\n      \n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      \n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  \n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  \n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  \n  return list;\n};\n\n// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n\n//   console.log('开始调试红绿灯模型...');\n  \n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n  \n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n  \n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n      \n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n  \n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n  \n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n      \n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n      \n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n      \n//       // 添加到场景\n//       scene.add(highlightMesh);\n      \n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n      \n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n      \n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n  \n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = (interId) => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    \n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    console.log('当前trafficLightStates大小:', trafficLightStates.size);\n    \n    // 如果没有指定ID，则使用第一个红绿灯\n    if (!interId && trafficLightsMap.size > 0) {\n      interId = String(Array.from(trafficLightsMap.keys())[0]);\n      console.log('未指定ID，使用第一个红绿灯ID:', interId);\n    }\n    \n    // 输出所有可用的红绿灯ID用于调试\n    console.log('所有可用的红绿灯ID:');\n    trafficLightsMap.forEach((light, id) => {\n      console.log(`- ${id} (${typeof id}): ${light.intersection?.name || '未知'}`);\n    });\n    \n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      \n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    \n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    \n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n    \n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n    \n    // 创建更新函数\n    const updateTrafficLightPopover = () => {\n      // 确保当前弹窗仍然可见\n      if (!window._setTrafficLightPopover) return;\n      \n      const currentId = window.currentPopoverIdRef?.current;\n      if (!currentId) return;\n      \n      // 重新获取最新的状态信息\n      let stateInfo = trafficLightStates.get(currentId);\n      if (!stateInfo && typeof currentId === 'string') {\n        // 尝试使用数字ID\n        stateInfo = trafficLightStates.get(parseInt(currentId));\n      } else if (!stateInfo && typeof currentId === 'number') {\n        // 尝试使用字符串ID\n        stateInfo = trafficLightStates.get(String(currentId));\n      }\n      \n      if (!stateInfo || !stateInfo.phases || stateInfo.phases.length === 0) {\n        console.log(`路口ID ${currentId} 没有状态信息，跳过更新`);\n        return;\n      }\n      \n      // 获取交通灯信息\n      const intersectionLight = trafficLightsMap.get(currentId) || \n                               (typeof currentId === 'string' ? trafficLightsMap.get(parseInt(currentId)) : \n                                trafficLightsMap.get(String(currentId)));\n                                \n      if (!intersectionLight) {\n        console.error(`找不到路口ID ${currentId} 的红绿灯信息，无法更新弹窗`);\n        return;\n      }\n      \n      const intersection = intersectionLight.intersection;\n      \n      // 创建更新的弹窗内容\n      const content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection?.name || '未知路口'} (ID: {currentId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              let lightText;\n              \n              switch (phase.trafficLight) {\n                case 'G':\n                  lightColor = '#00ff00';\n                  lightText = '绿灯';\n                  break;\n                case 'Y':\n                  lightColor = '#ffff00';\n                  lightText = '黄灯';\n                  break;\n                case 'R':\n                default:\n                  lightColor = '#ff0000';\n                  lightText = '红灯';\n                  break;\n              }\n              \n              const direction = getPhaseDirection(phase.phaseId);\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {direction} (相位ID: {phase.phaseId})\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {lightText}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date(stateInfo.updateTime).toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n      \n      // 更新弹窗内容\n      window._setTrafficLightPopover(prev => ({\n        ...prev,\n        content: content,\n        phases: stateInfo.phases\n      }));\n      \n      console.log(`已更新路口 ${intersection?.name || currentId} 的红绿灯状态弹窗`);\n    };\n    \n    // 先执行一次初始更新\n    const updateInitialTrafficLightPopover = () => {\n      // 同样，查找红绿灯状态信息时也尝试字符串和数字两种类型\n      let stateInfo = trafficLightStates.get(interId);\n      if (!stateInfo) {\n        // 尝试使用数字ID\n        const numericId = parseInt(interId);\n        stateInfo = trafficLightStates.get(numericId);\n        \n        if (stateInfo) {\n          console.log(`使用数字ID ${numericId} 找到了红绿灯状态信息`);\n        }\n      }\n      \n      console.log(`路口ID ${interId} 的状态信息:`, stateInfo);\n      \n      const intersection = trafficLight.intersection;\n      console.log('路口信息:', intersection);\n      \n      // 创建弹窗内容\n      let content;\n      \n      if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n        // 添加一个检查相位数据的打印\n        console.log('相位数据详情:');\n        stateInfo.phases.forEach((phase, index) => {\n          console.log(`相位 ${index+1}: ID=${phase.phaseId}, 状态=${phase.trafficLight}, 剩余时间=${phase.remainTime}`);\n        });\n        \n        content = (\n          <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n            <div style={{ \n              fontWeight: 'bold', \n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            }}>\n              {intersection?.name || '未知路口'} (ID: {interId})\n            </div>\n            <div>\n              {stateInfo.phases.map((phase, index) => {\n                let lightColor;\n                let lightText;\n                \n                switch (phase.trafficLight) {\n                  case 'G':\n                    lightColor = '#00ff00';\n                    lightText = '绿灯';\n                    break;\n                  case 'Y':\n                    lightColor = '#ffff00';\n                    lightText = '黄灯';\n                    break;\n                  case 'R':\n                  default:\n                    lightColor = '#ff0000';\n                    lightText = '红灯';\n                    break;\n                }\n                \n                const direction = getPhaseDirection(phase.phaseId);\n                \n                return (\n                  <div key={index} style={{ \n                    marginBottom: '6px', \n                    backgroundColor: 'rgba(255,255,255,0.1)',\n                    padding: '4px',\n                    borderRadius: '4px',\n                    fontSize: '12px'\n                  }}>\n                    <div style={{ fontWeight: 'bold' }}>\n                      {direction} (相位ID: {phase.phaseId})\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <span>灯色: </span>\n                      <span style={{ \n                        color: lightColor, \n                        fontWeight: 'bold',\n                        backgroundColor: 'rgba(0,0,0,0.3)',\n                        padding: '0 3px',\n                        borderRadius: '2px'\n                      }}>\n                        {lightText}\n                      </span>\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <span>倒计时: </span>\n                      <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n            <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n              更新时间: {new Date(stateInfo.updateTime).toLocaleTimeString()} (自动刷新)\n            </div>\n          </div>\n        );\n      } else {\n        // 如果没有状态信息，创建一个示例内容\n        content = (\n          <div style={{ padding: '8px', width: '220px' }}>\n            <div style={{ \n              fontWeight: 'bold', \n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            }}>\n              {intersection?.name || '未知路口'} (ID: {interId})\n            </div>\n            <div style={{ color: '#ff4d4f', fontSize: '12px' }}>\n              该路口暂无红绿灯状态信息，请等待SPAT消息更新。\n            </div>\n            <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n              当前时间: {new Date().toLocaleTimeString()}\n            </div>\n          </div>\n        );\n      }\n      \n      // 设置弹窗位置在屏幕中央\n      const x = window.innerWidth / 2;\n      const y = window.innerHeight / 2;\n      \n      // 更新弹窗状态\n      if (window._setTrafficLightPopover) {\n        console.log('调用_setTrafficLightPopover更新弹窗状态', {\n          visible: true,\n          interId,\n          position: { x, y }\n        });\n        \n        window._setTrafficLightPopover({\n          visible: true,\n          interId: interId,\n          position: { x, y },\n          content: content,\n          phases: stateInfo?.phases || []\n        });\n        \n        console.log(`已显示路口 ${intersection?.name || interId} 的红绿灯状态弹窗`);\n        \n        // 设置定时更新\n        if (window.trafficLightUpdateTimerRef) {\n          window.trafficLightUpdateTimerRef.current = setInterval(updateTrafficLightPopover, TRAFFIC_LIGHT_UPDATE_INTERVAL * 1000);\n          console.log(`已设置红绿灯状态弹窗定时更新，间隔 ${TRAFFIC_LIGHT_UPDATE_INTERVAL} 秒`);\n        }\n        \n        return true;\n      } else {\n        console.error('无法找到setTrafficLightPopover函数');\n        return false;\n      }\n    };\n    \n    return updateInitialTrafficLightPopover();\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n  \n//   console.log('开始生成模拟SPAT数据...');\n  \n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n  \n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n  \n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n    \n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n      \n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n    \n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n      \n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n      \n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n    \n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n    \n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n    \n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n    \n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n  \n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n  \n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n  \n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n  \n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n  \n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n  \n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  \n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n    \n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n    \n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n    \n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n    \n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    \n    console.log('归一化坐标:', mouseX, mouseY);\n    \n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    \n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n    \n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n    \n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    \n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', \n                    '距离:', intersect.distance,\n                    'position:', intersect.object.position.toArray(),\n                    'userData:', intersect.object.userData);\n        \n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      \n      return true;\n    }\n    \n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    \n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', \n                  '类型:', obj.type,\n                  '位置:', obj.position.toArray(),\n                  '距离:', intersect.distance,\n                  'userData:', obj.userData);\n    });\n    \n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    \n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n        \n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n        \n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n        \n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        \n        if (isVisible) {\n          visibleCount++;\n        }\n        \n        console.log(`红绿灯 ${interId}:`, {\n          名称: lightObj.intersection?.name || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    \n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n    \n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  \n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch(phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n  \n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ \n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: trafficLight.intersection?.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n  \n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = { isLight: true };\n  \n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  \n  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\n\nexport default CampusModel; \n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACtC,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,0BAA0B,GAAG,IAAI,CAAC,CAAC;AACvC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,GAAG,CAAC,CAAC;;AAEnB;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACV;EACEC,GAAG,EAAE,2BAA2B;EAChCC,GAAG,EAAE,2BAA2B;EAChCX,KAAK,EAAE,6BAA6B;EACtCY,GAAG,EAAE,2BAA2B;EAAG;EACnCC,IAAI,EAAE,4BAA4B,CAAE;AACtC,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,IAAIC,gBAAgB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;;AAElC;AACA,IAAIE,gBAAgB,GAAG,IAAI;;AAE3B;AACA,IAAIC,gBAAgB,GAAG,IAAIH,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,IAAII,kBAAkB,GAAG,IAAIJ,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEpC;AACA,MAAMK,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGT,QAAQ,oBAAoB,CAAC;IAC7D,MAAMU,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,IAAIA,IAAI,CAACE,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;MACzD,MAAMG,WAAW,GAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,IAAI,CAAC;MACrE,IAAIH,WAAW,IAAIA,WAAW,CAACI,KAAK,EAAE;QACpCf,gBAAgB,GAAGW,WAAW,CAACI,KAAK;QACpCC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEjB,gBAAgB,CAAC;QAC7C,OAAOA,gBAAgB;MACzB;IACF;IACAgB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChCjB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjClB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB;AACF,CAAC;;AAED;AACA,MAAMmB,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAIC,MAAM,IAAK;EACjC,IAAI,CAACzC,YAAY,EAAE;IACjBA,YAAY,GAAGyC,MAAM,CAACC,KAAK,CAAC,CAAC;IAC7B,OAAOD,MAAM;EACf;EAEA,MAAME,SAAS,GAAGP,aAAa,CAACK,MAAM,CAACG,CAAC,EAAE5C,YAAY,CAAC4C,CAAC,EAAE1C,KAAK,CAAC;EAChE,MAAM2C,SAAS,GAAGT,aAAa,CAACK,MAAM,CAACK,CAAC,EAAE9C,YAAY,CAAC8C,CAAC,EAAE5C,KAAK,CAAC;EAChE,MAAM6C,SAAS,GAAGX,aAAa,CAACK,MAAM,CAACO,CAAC,EAAEhD,YAAY,CAACgD,CAAC,EAAE9C,KAAK,CAAC;EAEhEF,YAAY,CAACiD,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACjD,OAAO/C,YAAY,CAAC0C,KAAK,CAAC,CAAC;AAC7B,CAAC;;AAED;AACA,MAAMQ,cAAc,GAAIC,WAAW,IAAK;EACtC,IAAIlD,YAAY,KAAK,IAAI,EAAE;IACzBA,YAAY,GAAGkD,WAAW;IAC1B,OAAOA,WAAW;EACpB;;EAEA;EACA,IAAIC,IAAI,GAAGD,WAAW,GAAGlD,YAAY;EACrC,IAAImD,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EAExC,MAAMC,gBAAgB,GAAGnB,aAAa,CAACnC,YAAY,GAAGmD,IAAI,EAAEnD,YAAY,EAAEC,KAAK,CAAC;EAChFD,YAAY,GAAGsD,gBAAgB;EAC/B,OAAOA,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA,MAAMC,6BAA6B,GAAG,CAAC,CAAC,CAAC;AACzC;;AAEA,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC,kBAAkB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAMC,YAAY,GAAG5F,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM6F,UAAU,GAAG7F,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM8F,SAAS,GAAG9F,MAAM,CAAC,IAAIM,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMyF,aAAa,GAAG/F,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMgG,eAAe,GAAGhG,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMiG,aAAa,GAAGjG,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMkG,iBAAiB,GAAGlG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAE;EACzC,MAAM,CAACmG,eAAe,EAAEC,kBAAkB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAACoG,YAAY,EAAEC,eAAe,CAAC,GAAGrG,QAAQ,CAAC;IAC/CsG,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3G,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAM4G,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,IAAI;IAAG;IACfC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG9H,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAAC+H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/H,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAACgI,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjI,QAAQ,CAAC;IAC7DkI,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,IAAI;IACbtB,QAAQ,EAAE;MAAEpC,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxByD,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAGvI,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMwI,0BAA0B,GAAGxI,MAAM,CAAC,IAAI,CAAC;;EAE/C;EACAmC,MAAM,CAACsG,uBAAuB,GAAGP,sBAAsB;;EAEvD;EACA/F,MAAM,CAACoG,mBAAmB,GAAGA,mBAAmB;EAChDpG,MAAM,CAACqG,0BAA0B,GAAGA,0BAA0B;;EAE9D;EACA,MAAME,uBAAuB,GAAG;IAC9B5B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACX3B,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7B2B,KAAK,EAAE,OAAO;IAAG;IACjB1B,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMiB,UAAU,GAAG;IACjB/B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACX3B,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBwB,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbpB,QAAQ,EAAE,MAAM;IAChBqB,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvC/B,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM,CAAClE,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,IAAI4C,GAAG,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACqG,UAAU,EAAEC,aAAa,CAAC,GAAGlJ,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAAC6C,gBAAgB,EAAEsG,mBAAmB,CAAC,GAAGnJ,QAAQ,CAAC,IAAI4C,GAAG,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACwG,WAAW,EAAEC,cAAc,CAAC,GAAGrJ,QAAQ,CAAC;IAAEsJ,KAAK,EAAE,EAAE;IAAElB,OAAO,EAAE;EAAG,CAAC,CAAC;;EAE1E;EACA,MAAMmB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B5C,WAAW,CAAC,QAAQ,CAAC;IACrBrF,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACiI,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B9C,WAAW,CAAC,QAAQ,CAAC;IACrBrF,UAAU,GAAG,QAAQ;IAErB,IAAIuG,SAAS,CAAC6B,OAAO,IAAInI,QAAQ,EAAE;MACjC;MACA,MAAMoI,UAAU,GAAG9B,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAACtC,KAAK,CAAC,CAAC;MACrD,MAAMqF,SAAS,GAAG/B,SAAS,CAAC6B,OAAO,CAACG,EAAE,CAACtF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAIjE,KAAK,CAACwJ,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC;QAAEtF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,GAAG;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCmF,MAAM,CAAC1J,KAAK,CAAC2J,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvC,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAACwD,IAAI,CAACV,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;MAEV;MACA,IAAIhK,KAAK,CAACwJ,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEtF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BmF,MAAM,CAAC1J,KAAK,CAAC2J,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvC,SAAS,CAAC6B,OAAO,CAACG,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC;MACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAGhJ,QAAQ,CAACiJ,MAAM,CAACjG,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAIjE,KAAK,CAACwJ,KAAK,CAACS,aAAa,CAAC,CAC3BR,EAAE,CAAC;QAAEtF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BmF,MAAM,CAAC1J,KAAK,CAAC2J,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACd7I,QAAQ,CAACiJ,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACA1C,SAAS,CAAC6B,OAAO,CAACe,MAAM,CAAClJ,QAAQ,CAACiJ,MAAM,CAAC;QACzCjJ,QAAQ,CAACmJ,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACA/I,QAAQ,CAACiI,OAAO,GAAG,IAAI;;MAEvB;MACAjI,QAAQ,CAACoJ,WAAW,GAAG,EAAE;MACzBpJ,QAAQ,CAACqJ,WAAW,GAAG,GAAG;MAC1BrJ,QAAQ,CAACsJ,aAAa,GAAG3F,IAAI,CAACC,EAAE,GAAG,GAAG;MACtC5D,QAAQ,CAACuJ,aAAa,GAAG,CAAC;MAC1BvJ,QAAQ,CAACmJ,MAAM,CAAC,CAAC;MAEjB5G,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBgH,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAG1K,iBAAiB,CAAC2K,aAAa,CAAC3H,IAAI,CAAC4H,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKJ,KAAK,CAAC;IAChF,IAAIC,YAAY,IAAIvD,SAAS,CAAC6B,OAAO,IAAInI,QAAQ,EAAE;MACjDwG,uBAAuB,CAACqD,YAAY,CAAC;;MAErC;MACA,MAAMI,WAAW,GAAG3F,SAAS,CAAC6D,OAAO,CAAC+B,YAAY,CAChDC,UAAU,CAACN,YAAY,CAAC9E,SAAS,CAAC,EAClCoF,UAAU,CAACN,YAAY,CAAC7E,QAAQ,CAClC,CAAC;MAEDzC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvB4H,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBK,GAAG,EAAE;UACHtF,SAAS,EAAE8E,YAAY,CAAC9E,SAAS;UACjCC,QAAQ,EAAE6E,YAAY,CAAC7E;QACzB,CAAC;QACDsF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACAlK,UAAU,GAAG,cAAc;MAC3BqF,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAkB,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAC/B,GAAG,CAAC0G,WAAW,CAAC/G,CAAC,EAAE,GAAG,EAAE,CAAC+G,WAAW,CAAC7G,CAAC,CAAC;;MAElE;MACApD,QAAQ,CAACiJ,MAAM,CAAC1F,GAAG,CAAC0G,WAAW,CAAC/G,CAAC,EAAE,CAAC,EAAE,CAAC+G,WAAW,CAAC7G,CAAC,CAAC;;MAErD;MACAkD,SAAS,CAAC6B,OAAO,CAACe,MAAM,CAAClJ,QAAQ,CAACiJ,MAAM,CAAC;;MAEzC;MACAjJ,QAAQ,CAACiI,OAAO,GAAG,IAAI;MACvBjI,QAAQ,CAACmJ,MAAM,CAAC,CAAC;;MAEjB;MACA7C,SAAS,CAAC6B,OAAO,CAACoC,YAAY,CAAC,CAAC;MAChCjE,SAAS,CAAC6B,OAAO,CAACqC,iBAAiB,CAAC,IAAI,CAAC;MAEzCjI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzB4H,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBS,IAAI,EAAEnE,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAACoF,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAE3K,QAAQ,CAACiJ,MAAM,CAACyB,OAAO,CAAC,CAAC;QAC9BJ,IAAI,EAAEL;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMW,iBAAiB,GAAGA,CAAC7C,KAAK,EAAE8C,OAAO,KAAK;IAC5C;IACA,IAAI,CAACxK,KAAK,IAAI,CAACiE,SAAS,CAAC6D,OAAO,EAAE;MAChC5F,OAAO,CAACuI,IAAI,CAAC,8BAA8B,CAAC;MAC5C;IACF;IAEA,IAAI;MACF;MACA,IAAI,CAACD,OAAO,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,MAAM,EAAE;QACtDtI,OAAO,CAACuI,IAAI,CAAC,cAAc,EAAED,OAAO,CAAC;QACrC;MACF;MAEA,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,OAAO,CAAC;MACnCtI,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEuI,OAAO,CAAC;;MAE/B;MACA,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAAClJ,IAAI,EAAE;QAC7BU,OAAO,CAACuI,IAAI,CAAC,kBAAkB,EAAEC,OAAO,CAAC;QACzC;MACF;;MAEA;MACA,IAAIhD,KAAK,KAAKtH,WAAW,CAACQ,GAAG,EAAE;QAC7BsB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEuI,OAAO,CAAC;;QAEhC;QACApK,MAAM,CAACuK,WAAW,CAAC;UACjBC,IAAI,EAAE,KAAK;UACXtJ,IAAI,EAAEkJ,OAAO,CAAClJ;QAChB,CAAC,EAAE,GAAG,CAAC;QAEP,MAAMuJ,OAAO,GAAGL,OAAO,CAAClJ,IAAI;;QAE5B;QACA,IAAI,CAACuJ,OAAO,IAAI,CAACA,OAAO,CAACC,KAAK,EAAE;UAC9B9I,OAAO,CAACuI,IAAI,CAAC,oBAAoB,EAAEM,OAAO,CAAC;UAC3C;QACF;QAEA,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;QAC3B,MAAMC,MAAM,GAAGF,OAAO,CAACG,IAAI,IAAI,EAAE;QAEjCD,MAAM,CAACE,OAAO,CAACC,KAAK,IAAI;UACtB,MAAMC,OAAO,GAAGD,KAAK,CAACE,KAAK;UAC3B,MAAMC,SAAS,GAAGH,KAAK,CAACG,SAAS;UACjC,MAAMC,WAAW,GAAGJ,KAAK,CAACI,WAAW;UACrC,MAAMC,SAAS,GAAGL,KAAK,CAACK,SAAS;UACjC,MAAMC,OAAO,GAAGN,KAAK,CAACM,OAAO;;UAE7B;UACA,IAAI,CAACX,OAAO,CAACY,OAAO,IAAI,CAACZ,OAAO,CAACa,MAAM,EAAE;YACvC1J,OAAO,CAACuI,IAAI,CAAC,cAAc,EAAEM,OAAO,CAAC;YACrC;UACF;;UAEA;UACA,MAAMc,QAAQ,GAAG5H,SAAS,CAAC6D,OAAO,CAAC+B,YAAY,CAC7CC,UAAU,CAACiB,OAAO,CAACY,OAAO,CAAC,EAC3B7B,UAAU,CAACiB,OAAO,CAACa,MAAM,CAC3B,CAAC;;UAED;UACA,IAAIE,WAAW,GAAG,EAAE;UACpB,IAAIC,YAAY,GAAG,EAAE;UAErB,QAAOR,SAAS;YACd,KAAK,KAAK;cAAG;cACXO,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,QAAQ;cACtBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,MAAM;cAAE;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF;cACED,WAAW,GAAGN,WAAW,IAAI,MAAM;cACnCO,YAAY,GAAG,SAAS;UAC5B;;UAEA;UACAC,iBAAiB,CAACH,QAAQ,EAAEC,WAAW,EAAEC,YAAY,CAAC;UAEtD7J,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;YACtB8J,IAAI,EAAEZ,OAAO;YACba,IAAI,EAAEX,SAAS;YACfY,IAAI,EAAEX,WAAW;YACjBY,IAAI,EAAEX,SAAS;YACfY,IAAI,EAAEX,OAAO;YACbY,EAAE,EAAET;UACN,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAInE,KAAK,KAAKtH,WAAW,CAACM,GAAG,EAAE;QAC7BwB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEuI,OAAO,CAAC;QAEhC,MAAM6B,OAAO,GAAG7B,OAAO,CAAClJ,IAAI;QAC5B,MAAMgL,KAAK,GAAGD,OAAO,CAACtK,KAAK;QAC3B,MAAMwK,QAAQ,GAAG;UACf/H,SAAS,EAAEoF,UAAU,CAACyC,OAAO,CAACG,QAAQ,CAAC;UACvC/H,QAAQ,EAAEmF,UAAU,CAACyC,OAAO,CAACI,OAAO,CAAC;UACrC/H,KAAK,EAAEkF,UAAU,CAACyC,OAAO,CAACK,SAAS,CAAC;UACpC/H,OAAO,EAAEiF,UAAU,CAACyC,OAAO,CAACM,WAAW;QACzC,CAAC;QAED3K,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEsK,QAAQ,CAAC;QAClCvK,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEqK,KAAK,CAAC;;QAE3B;QACA,MAAMX,QAAQ,GAAG5H,SAAS,CAAC6D,OAAO,CAAC+B,YAAY,CAAC4C,QAAQ,CAAC/H,SAAS,EAAE+H,QAAQ,CAAC9H,QAAQ,CAAC;QACtF,MAAMmI,WAAW,GAAG,IAAIxO,KAAK,CAACyO,OAAO,CAAClB,QAAQ,CAAChJ,CAAC,EAAE,GAAG,EAAE,CAACgJ,QAAQ,CAAC9I,CAAC,CAAC;QACnE,MAAMK,WAAW,GAAGE,IAAI,CAACC,EAAE,GAAGkJ,QAAQ,CAAC5H,OAAO,GAAGvB,IAAI,CAACC,EAAE,GAAG,GAAG;;QAE9D;QACA,IAAIyJ,UAAU,GAAGjM,aAAa,CAACkM,GAAG,CAACT,KAAK,CAAC;;QAEzC;QACA,MAAMxK,aAAa,GAAGwK,KAAK,KAAKtL,gBAAgB;QAEhD,IAAI,CAAC8L,UAAU,IAAIpN,qBAAqB,EAAE;UACxC;UACA,MAAMsN,eAAe,GAAGtN,qBAAqB,CAAC+C,KAAK,CAAC,CAAC;UACrDuK,eAAe,CAACjI,QAAQ,CAACwD,IAAI,CAACqE,WAAW,CAAC;UAC1CI,eAAe,CAACC,QAAQ,CAACpK,CAAC,GAAGK,WAAW;UACxCpD,KAAK,CAACoN,GAAG,CAACF,eAAe,CAAC;;UAE1B;UACAnM,aAAa,CAACmC,GAAG,CAACsJ,KAAK,EAAE;YACvBa,KAAK,EAAEH,eAAe;YACtBI,UAAU,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;YACtB1C,IAAI,EAAE,GAAG;YAAE;YACX2C,MAAM,EAAEzL;UACV,CAAC,CAAC;UAEFE,OAAO,CAACC,GAAG,CAAC,aAAaqK,KAAK,SAASM,WAAW,CAACjK,CAAC,CAAC6K,OAAO,CAAC,CAAC,CAAC,KAAKZ,WAAW,CAAC/J,CAAC,CAAC2K,OAAO,CAAC,CAAC,CAAC,KAAKZ,WAAW,CAAC7J,CAAC,CAACyK,OAAO,CAAC,CAAC,CAAC,YAAY1L,aAAa,EAAE,CAAC;;UAErJ;UACA,IAAIA,aAAa,EAAE;YACjB7C,gBAAgB,GAAG+N,eAAe;YAClCzI,eAAe,CAACgI,QAAQ,CAAC;YACzBvK,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEqK,KAAK,CAAC;UACjC;QACF,CAAC,MAAM,IAAIQ,UAAU,EAAE;UACrB;UACA,MAAMW,gBAAgB,GAAGlL,cAAc,CAACqK,WAAW,CAAC;UACpD,MAAMtJ,gBAAgB,GAAGL,cAAc,CAACC,WAAW,CAAC;;UAEpD;UACA4J,UAAU,CAACK,KAAK,CAACpI,QAAQ,CAACwD,IAAI,CAACkF,gBAAgB,CAAC;UAChDX,UAAU,CAACK,KAAK,CAACF,QAAQ,CAACpK,CAAC,GAAGS,gBAAgB;UAC9CwJ,UAAU,CAACK,KAAK,CAACnD,YAAY,CAAC,CAAC;UAC/B8C,UAAU,CAACK,KAAK,CAAClD,iBAAiB,CAAC,IAAI,CAAC;UACxC6C,UAAU,CAACM,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;UAClCR,UAAU,CAACS,MAAM,GAAGzL,aAAa,CAAC,CAAC;;UAEnCE,OAAO,CAACC,GAAG,CAAC,cAAcqK,KAAK,SAASmB,gBAAgB,CAAC9K,CAAC,CAAC6K,OAAO,CAAC,CAAC,CAAC,KAAKC,gBAAgB,CAAC5K,CAAC,CAAC2K,OAAO,CAAC,CAAC,CAAC,KAAKC,gBAAgB,CAAC1K,CAAC,CAACyK,OAAO,CAAC,CAAC,CAAC,YAAY1L,aAAa,EAAE,CAAC;;UAErK;UACA,IAAIA,aAAa,EAAE;YACjB7C,gBAAgB,GAAG6N,UAAU,CAACK,KAAK;YACnC5I,eAAe,CAACgI,QAAQ,CAAC;UAC3B;QACF;;QAEA;QACA,MAAMe,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;QACtB,MAAMI,iBAAiB,GAAG,IAAI,CAAC,CAAC;;QAEhC7M,aAAa,CAACoK,OAAO,CAAC,CAAC0C,SAAS,EAAEC,EAAE,KAAK;UACvC,IAAIN,GAAG,GAAGK,SAAS,CAACP,UAAU,GAAGM,iBAAiB,EAAE;YAClD5N,KAAK,CAAC+N,MAAM,CAACF,SAAS,CAACR,KAAK,CAAC;YAC7BtM,aAAa,CAACiN,MAAM,CAACF,EAAE,CAAC;YACxB5L,OAAO,CAACC,GAAG,CAAC,mBAAmB2L,EAAE,EAAE,CAAC;UACtC;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAIpG,KAAK,KAAKtH,WAAW,CAACS,IAAI,EAAE;QAC9BqB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEqI,OAAO,CAAC;QAEjC,IAAI;UACF,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,OAAO,CAAC;;UAEnC;UACA,MAAMyD,SAAS,GAAGvD,OAAO,CAACwD,GAAG;UAC7B,MAAMC,gBAAgB,GAAGzD,OAAO,CAAC0D,EAAE;;UAEnC;UACA,MAAMC,aAAa,GAAGpN,gBAAgB,CAACgM,GAAG,CAACgB,SAAS,CAAC;;UAErD;UACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;YACrDnM,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;cAC1BmM,KAAK,EAAEL,SAAS;cAChBM,KAAK,EAAEJ,gBAAgB;cACvBK,KAAK,EAAEH;YACT,CAAC,CAAC;YACF;UACF;;UAEA;UACApN,gBAAgB,CAACiC,GAAG,CAAC+K,SAAS,EAAEE,gBAAgB,CAAC;;UAEjD;UACA,IAAIzD,OAAO,CAAClJ,IAAI,IAAIkJ,OAAO,CAAClJ,IAAI,CAACiI,aAAa,IAAI9H,KAAK,CAACC,OAAO,CAAC8I,OAAO,CAAClJ,IAAI,CAACiI,aAAa,CAAC,EAAE;YAC3FiB,OAAO,CAAClJ,IAAI,CAACiI,aAAa,CAAC0B,OAAO,CAAC3B,YAAY,IAAI;cACjD,MAAMjD,OAAO,GAAGiD,YAAY,CAACjD,OAAO;cAEpC,IAAI,CAACA,OAAO,EAAE;gBACZrE,OAAO,CAACE,KAAK,CAAC,kBAAkB,EAAEoH,YAAY,CAAC;gBAC/C;cACF;cAEAtH,OAAO,CAACC,GAAG,CAAC,WAAWoE,OAAO,UAAU,CAAC;;cAEzC;cACA,IAAIiD,YAAY,CAAC/C,MAAM,IAAI9E,KAAK,CAACC,OAAO,CAAC4H,YAAY,CAAC/C,MAAM,CAAC,EAAE;gBAC7D;gBACA,MAAMgI,UAAU,GAAG,EAAE;gBAErBjF,YAAY,CAAC/C,MAAM,CAAC0E,OAAO,CAACuD,KAAK,IAAI;kBACnC;kBACA,IAAI,CAACA,KAAK,CAACC,OAAO,EAAE;oBAClBzM,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAEsM,KAAK,CAAC;oBACtC;kBACF;kBAEA,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC;kBACxC;kBACA,MAAMC,SAAS,GAAGH,KAAK,CAACI,YAAY,GAClCC,oBAAoB,CAACL,KAAK,CAACI,YAAY,CAAC,GACxCE,iBAAiB,CAACL,OAAO,CAAC;;kBAE5B;kBACA,MAAMM,UAAU,GAAGP,KAAK,CAACQ,YAAY,IAAI,GAAG,CAAC,CAAC;kBAC9C,MAAMC,UAAU,GAAGC,QAAQ,CAACV,KAAK,CAACS,UAAU,CAAC,IAAI,CAAC;kBAElDjN,OAAO,CAACC,GAAG,CAAC,SAASoE,OAAO,WAAWoI,OAAO,SAASE,SAAS,YAAYI,UAAU,WAAWE,UAAU,GAAG,CAAC;;kBAE/G;kBACA,MAAME,SAAS,GAAG;oBAChBV,OAAO;oBACPE,SAAS;oBACTK,YAAY,EAAED,UAAU;oBACxBE;kBACF,CAAC;;kBAED;kBACAV,UAAU,CAACa,IAAI,CAACD,SAAS,CAAC;;kBAE1B;kBACA;kBACA,IAAIE,eAAe,GAAGC,MAAM,CAACjJ,OAAO,CAAC;kBACrC,IAAIkJ,iBAAiB,GAAGtO,gBAAgB,CAAC8L,GAAG,CAACsC,eAAe,CAAC;kBAE7D,IAAI,CAACE,iBAAiB,EAAE;oBACtB;oBACAF,eAAe,GAAGH,QAAQ,CAAC7I,OAAO,CAAC;oBACnCkJ,iBAAiB,GAAGtO,gBAAgB,CAAC8L,GAAG,CAACsC,eAAe,CAAC;kBAC3D;kBAEA,IAAIE,iBAAiB,EAAE;oBACrB;oBACAC,wBAAwB,CAACD,iBAAiB,EAAEJ,SAAS,CAAC;;oBAEtD;oBACA,IAAInJ,oBAAoB,IAAIA,oBAAoB,CAACK,OAAO,KAAKA,OAAO,EAAE;sBACpEF,sBAAsB,CAACsJ,IAAI,KAAK;wBAC9B,GAAGA,IAAI;wBACPrJ,OAAO,EAAE,IAAI;wBACbqI,OAAO;wBACPE,SAAS;wBACTe,KAAK,EAAEX,UAAU;wBACjBE;sBACF,CAAC,CAAC,CAAC;oBACL;kBACF,CAAC,MAAM;oBACLjN,OAAO,CAACuI,IAAI,CAAC,WAAWlE,OAAO,SAASoI,OAAO,SAAS,CAAC;kBAC3D;gBACF,CAAC,CAAC;;gBAEF;gBACA,IAAIkB,QAAQ,GAAG,IAAI;gBACnB;gBACA,MAAMC,KAAK,GAAGN,MAAM,CAACjJ,OAAO,CAAC;gBAC7B,IAAIpF,gBAAgB,CAAC4O,GAAG,CAACD,KAAK,CAAC,EAAE;kBAC/BD,QAAQ,GAAGC,KAAK;gBAClB,CAAC,MAAM;kBACL;kBACA,MAAME,KAAK,GAAGZ,QAAQ,CAAC7I,OAAO,CAAC;kBAC/B,IAAIpF,gBAAgB,CAAC4O,GAAG,CAACC,KAAK,CAAC,EAAE;oBAC/BH,QAAQ,GAAGG,KAAK;kBAClB;gBACF;gBAEA,IAAIH,QAAQ,KAAK,IAAI,EAAE;kBACrB;kBACAzO,kBAAkB,CAAC8B,GAAG,CAAC2M,QAAQ,EAAE;oBAC/BI,UAAU,EAAE1C,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtB/G,MAAM,EAAEgI;kBACV,CAAC,CAAC;kBACFvM,OAAO,CAACC,GAAG,CAAC,aAAa0N,QAAQ,KAAK,OAAOA,QAAQ,aAAa,CAAC;;kBAEnE;kBACA,IAAIvP,MAAM,CAACoG,mBAAmB,KAC1BpG,MAAM,CAACoG,mBAAmB,CAACoB,OAAO,KAAK+H,QAAQ,IAC/CvP,MAAM,CAACoG,mBAAmB,CAACoB,OAAO,KAAK0H,MAAM,CAACK,QAAQ,CAAC,IACvDvP,MAAM,CAACoG,mBAAmB,CAACoB,OAAO,KAAKsH,QAAQ,CAACS,QAAQ,CAAC,CAAC,EAAE;oBAE9D3N,OAAO,CAACC,GAAG,CAAC,eAAe0N,QAAQ,aAAa,CAAC;oBACjD;oBACAvP,MAAM,CAACoG,mBAAmB,CAACoB,OAAO,GAAG+H,QAAQ;;oBAE7C;oBACA,IAAIvP,MAAM,CAACqG,0BAA0B,IAAI,CAACrG,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,EAAE;sBACnF5F,OAAO,CAACC,GAAG,CAAC,SAAS0N,QAAQ,aAAa,CAAC;sBAC3CK,UAAU,CAAC,MAAM;wBACf5P,MAAM,CAAC6P,qBAAqB,CAACN,QAAQ,CAAC;sBACxC,CAAC,EAAE,GAAG,CAAC;oBACT;kBACF;gBACF,CAAC,MAAM;kBACL;kBACAzO,kBAAkB,CAAC8B,GAAG,CAACqD,OAAO,EAAE;oBAC9B0J,UAAU,EAAE1C,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtB/G,MAAM,EAAEgI;kBACV,CAAC,CAAC;kBACFvM,OAAO,CAACC,GAAG,CAAC,sBAAsBoE,OAAO,YAAY,CAAC;gBACxD;cACF,CAAC,MAAM;gBACLrE,OAAO,CAACE,KAAK,CAAC,eAAe,EAAEoH,YAAY,CAAC;cAC9C;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACLtH,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEsI,OAAO,CAAC;UAC9D;QACF,CAAC,CAAC,OAAOtI,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,aAAa,EAAEA,KAAK,EAAEoI,OAAO,CAAC;QAC9C;MACF;;MAEA;MACA,IAAI9C,KAAK,KAAKtH,WAAW,CAACJ,KAAK,IAAI0K,OAAO,CAACI,IAAI,KAAK,OAAO,EAAE;QAC3D5I,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEuI,OAAO,CAAC;QAEjC,MAAM0F,SAAS,GAAG1F,OAAO,CAAClJ,IAAI;QAC9B,MAAM6O,OAAO,GAAGD,SAAS,CAACC,OAAO;QACjC,MAAMC,SAAS,GAAGF,SAAS,CAACE,SAAS;QACrC,MAAMC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAMtL,QAAQ,GAAG;UACfN,QAAQ,EAAEmF,UAAU,CAACsG,SAAS,CAACzD,OAAO,CAAC;UACvCjI,SAAS,EAAEoF,UAAU,CAACsG,SAAS,CAAC1D,QAAQ;QAC1C,CAAC;;QAED;QACA,MAAMb,QAAQ,GAAG5H,SAAS,CAAC6D,OAAO,CAAC+B,YAAY,CAAC5E,QAAQ,CAACP,SAAS,EAAEO,QAAQ,CAACN,QAAQ,CAAC;;QAEtF;QACA,QAAO2L,SAAS;UACd,KAAK,GAAG;YAAG;YACTtE,iBAAiB,CAACH,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;YAClD;UACF,KAAK,KAAK;YAAG;YACXG,iBAAiB,CAACH,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACXG,iBAAiB,CAACH,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;YAC/C;UACF,KAAK,IAAI;YAAG;YACV,MAAM2E,UAAU,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAE;YAC1CzE,iBAAiB,CAACH,QAAQ,EAAE,KAAK2E,UAAU,MAAM,EAAE,SAAS,CAAC;YAC7D;UACF,KAAK,IAAI;YAAG;YACVxE,iBAAiB,CAACH,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVG,iBAAiB,CAACH,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YAAG;YACZG,iBAAiB,CAACH,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVG,iBAAiB,CAACH,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVG,iBAAiB,CAACH,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX,MAAM6E,YAAY,GAAGN,SAAS,CAACK,UAAU,CAAC,CAAE;YAC5C,MAAME,QAAQ,GAAGP,SAAS,CAACQ,UAAU,CAAC,CAAM;YAC5C5E,iBAAiB,CAACH,QAAQ,EAAE,QAAQgF,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,EAAE,SAAS,CAAC;YAC/F;QACJ;QAEA;MACF;MACA;MACAzO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBuF,KAAK;QACLoD,IAAI,EAAEJ,OAAO,CAACI,IAAI;QAClBtJ,IAAI,EAAEkJ;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOtI,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEoI,OAAO,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMsG,cAAc,GAAGA,CAAA,KAAM;IAC3B5O,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;;IAE7B;IACA,IAAIiC,aAAa,CAAC0D,OAAO,EAAE;MACzB,IAAI;QACF1D,aAAa,CAAC0D,OAAO,CAACiJ,KAAK,CAAC,CAAC;MAC/B,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV9O,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAE4O,CAAC,CAAC;MACxC;MACA5M,aAAa,CAAC0D,OAAO,GAAG,IAAI;IAC9B;IAEA,MAAMmJ,KAAK,GAAG,QAAQ7Q,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnEyB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE8O,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBlP,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED+O,EAAE,CAACG,SAAS,GAAIjG,KAAK,IAAK;MACxB;MACA,IAAI,CAACpL,KAAK,EAAE;QACVkC,OAAO,CAACuI,IAAI,CAAC,qBAAqB,CAAC;QACnC;MACF;MAEA,IAAI;QACF;QACA,IAAI,CAACW,KAAK,CAAC5J,IAAI,EAAE;UACfU,OAAO,CAACuI,IAAI,CAAC,iBAAiB,CAAC;UAC/B;QACF;QAEA,MAAMD,OAAO,GAAGG,IAAI,CAACC,KAAK,CAACQ,KAAK,CAAC5J,IAAI,CAAC;;QAEtC;QACA,IAAIgJ,OAAO,CAACM,IAAI,KAAK,SAAS,EAAE;UAC9B5I,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEqI,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACM,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIN,OAAO,CAACM,IAAI,KAAK,SAAS,IAAIN,OAAO,CAAC9C,KAAK,EAAE;UAC/C;UACA,IAAI,CAAC8C,OAAO,CAACE,OAAO,EAAE;YACpBxI,OAAO,CAACuI,IAAI,CAAC,uBAAuB,EAAED,OAAO,CAAC;YAC9C;UACF;;UAEA;UACAD,iBAAiB,CAACC,OAAO,CAAC9C,KAAK,EAAEiD,IAAI,CAAC2G,SAAS,CAAC9G,OAAO,CAACE,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOtI,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAED8O,EAAE,CAACK,OAAO,GAAInP,KAAK,IAAK;MACtBF,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAED8O,EAAE,CAACM,OAAO,GAAG,MAAM;MACjBtP,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;;MAE5B;MACA,IAAI,CAACnC,KAAK,EAAE;QACVkC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;QAC3B;MACF;;MAEA;MACA+N,UAAU,CAACY,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACA1M,aAAa,CAAC0D,OAAO,GAAGoJ,EAAE;EAC5B,CAAC;EAEDhT,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6F,YAAY,CAAC+D,OAAO,EAAE;;IAE3B;IACA2J,aAAa,CAAC,CAAC;;IAEf;IACAzR,KAAK,GAAG,IAAI1B,KAAK,CAACoT,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAIrT,KAAK,CAACsT,iBAAiB,CACxC,EAAE,EACFtR,MAAM,CAACuR,UAAU,GAAGvR,MAAM,CAACwR,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAAC1M,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCyO,MAAM,CAAC9I,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB5C,SAAS,CAAC6B,OAAO,GAAG6J,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAIzT,KAAK,CAAC0T,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAAC5R,MAAM,CAACuR,UAAU,EAAEvR,MAAM,CAACwR,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAAC9R,MAAM,CAAC+R,gBAAgB,CAAC;IAC/CtO,YAAY,CAAC+D,OAAO,CAACwK,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAIlU,KAAK,CAACmU,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5DzS,KAAK,CAACoN,GAAG,CAACoF,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAIpU,KAAK,CAACqU,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAACzN,QAAQ,CAAC/B,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1ClD,KAAK,CAACoN,GAAG,CAACsF,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAItU,KAAK,CAACqU,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAAC3N,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3ClD,KAAK,CAACoN,GAAG,CAACwF,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAIvU,KAAK,CAACwU,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAAC5N,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChC2P,SAAS,CAACE,KAAK,GAAGzP,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7BsP,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxBlT,KAAK,CAACoN,GAAG,CAACyF,SAAS,CAAC;;IAEpB;IACAlT,QAAQ,GAAG,IAAInB,aAAa,CAACmT,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzD5S,QAAQ,CAACwT,aAAa,GAAG,IAAI;IAC7BxT,QAAQ,CAACyT,aAAa,GAAG,IAAI;IAC7BzT,QAAQ,CAAC0T,kBAAkB,GAAG,KAAK;IACnC1T,QAAQ,CAACoJ,WAAW,GAAG,EAAE;IACzBpJ,QAAQ,CAACqJ,WAAW,GAAG,GAAG;IAC1BrJ,QAAQ,CAACsJ,aAAa,GAAG3F,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC5D,QAAQ,CAACuJ,aAAa,GAAG,CAAC;IAC1BvJ,QAAQ,CAACiJ,MAAM,CAAC1F,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BvD,QAAQ,CAACmJ,MAAM,CAAC,CAAC;;IAEjB;IACA5G,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBwP,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBhS,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBsG,SAAS,EAAE,CAAC,CAACA,SAAS,CAAC6B;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMwL,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAInV,UAAU,CAAC,CAAC;QACtCmV,aAAa,CAACC,IAAI,CAChB,GAAG7S,QAAQ,uBAAuB,EACjC8S,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAAC5T,KAAK;;UAE/B;UACA,MAAM8T,gBAAgB,GAAG,IAAIxV,KAAK,CAACyV,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACG,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAI9V,KAAK,CAAC+V,oBAAoB,CAAC;kBACjDnN,KAAK,EAAE,QAAQ;kBAAO;kBACtBoN,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIP,KAAK,CAACE,QAAQ,CAACM,GAAG,EAAE;kBACtBL,WAAW,CAACK,GAAG,GAAGR,KAAK,CAACE,QAAQ,CAACM,GAAG;gBACtC;;gBAEA;gBACAR,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5BlS,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE8R,KAAK,CAACtK,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMkK,YAAY,CAACa,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMV,KAAK,GAAGJ,YAAY,CAACa,QAAQ,CAAC,CAAC,CAAC;YACtCZ,gBAAgB,CAAC1G,GAAG,CAAC6G,KAAK,CAAC;UAC7B;;UAEA;UACAjU,KAAK,CAACoN,GAAG,CAAC0G,gBAAgB,CAAC;;UAE3B;UACA3U,gBAAgB,GAAG2U,gBAAgB;UAEnC5R,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9BoC,kBAAkB,CAAC,IAAI,CAAC;UACxBiP,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAc,GAAG,IAAK;UACP1S,OAAO,CAACC,GAAG,CAAC,aAAa,CAACyS,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEpH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACD+F,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMsB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMjB,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACAxC,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAIgD,gBAAgB,EAAE;UACpB,MAAMkB,YAAY,GAAG;YACnBtQ,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UAED,MAAMoQ,UAAU,GAAGhR,SAAS,CAAC6D,OAAO,CAAC+B,YAAY,CAACmL,YAAY,CAACtQ,SAAS,EAAEsQ,YAAY,CAACrQ,QAAQ,CAAC;UAChG;UACAmP,gBAAgB,CAAC7O,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACxC4Q,gBAAgB,CAAC3G,QAAQ,CAACpK,CAAC,GAAIO,IAAI,CAACC,EAAE,GAAGyR,YAAY,CAACnQ,OAAO,GAAGvB,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9EuQ,gBAAgB,CAAC5J,YAAY,CAAC,CAAC;UAC/B4J,gBAAgB,CAAC3J,iBAAiB,CAAC,IAAI,CAAC;UACxC3K,eAAe,GAAGsU,gBAAgB,CAAC7O,QAAQ,CAACtC,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAM8S,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAI7B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAM4B,WAAW,GAAIC,WAAW,IAAK;UACnCpT,OAAO,CAACC,GAAG,CAAC,WAAWgT,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAIhX,UAAU,CAAC,CAAC;UAC/BgX,MAAM,CAAC5B,IAAI,CACTwB,GAAG,EACFvB,IAAI,IAAK;YACR1R,OAAO,CAACC,GAAG,CAAC,WAAWgT,GAAG,EAAE,CAAC;YAC7B3B,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAgB,GAAG,IAAK;YACP1S,OAAO,CAACC,GAAG,CAAC,SAAS,CAACyS,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEpH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAtL,KAAK,IAAK;YACTF,OAAO,CAACE,KAAK,CAAC,SAAS+S,GAAG,EAAE,EAAE/S,KAAK,CAAC;YACpC,IAAIkT,WAAW,GAAG,CAAC,EAAE;cACnBpT,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3B+N,UAAU,CAAC,MAAMmF,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACL7B,MAAM,CAACrR,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAEDiT,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAIhX,UAAU,CAAC,CAAC;IAC/BgX,MAAM,CAAC5B,IAAI,CACT,GAAG7S,QAAQ,4BAA4B,EACvC,MAAO8S,IAAI,IAAK;MACd,IAAI;QACF,MAAMvG,KAAK,GAAGuG,IAAI,CAAC5T,KAAK;QACxBqN,KAAK,CAACmI,KAAK,CAACtS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBmK,KAAK,CAACpI,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAE3B;QACA,IAAIlD,KAAK,EAAE;UACXA,KAAK,CAACoN,GAAG,CAACC,KAAK,CAAC;;UAEhB;UACA,MAAM0H,eAAe,CAAC,CAAC;QACvB,CAAC,MAAM;UACL7S,OAAO,CAACE,KAAK,CAAC,eAAe,CAAC;QAChC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACAwS,GAAG,IAAK;MACP1S,OAAO,CAACC,GAAG,CAAC,SAAS,CAACyS,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEpH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAtL,KAAK,IAAK;MACTF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BF,OAAO,CAACE,KAAK,CAAC,OAAO,EAAE;QACrBqT,IAAI,EAAErT,KAAK,CAAC0I,IAAI;QAChB4K,IAAI,EAAEtT,KAAK,CAACoI,OAAO;QACnBmL,KAAK,EAAE,GAAG7U,QAAQ,4BAA4B;QAC9C8U,KAAK,EAAE,GAAG9U,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAM+U,OAAO,GAAGA,CAAA,KAAM;MACpBxR,iBAAiB,CAACyD,OAAO,GAAGgO,qBAAqB,CAACD,OAAO,CAAC;;MAE1D;MACAnX,KAAK,CAACoK,MAAM,CAAC,CAAC;MAEd,IAAIpJ,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACiI,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMmO,UAAU,GAAG5W,gBAAgB,CAAC8F,QAAQ,CAACtC,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMqT,eAAe,GAAG7W,gBAAgB,CAACgO,QAAQ,CAACpK,CAAC;;QAEnD;QACA;QACA,MAAMkT,gBAAgB,GAAG,EAAED,eAAe,GAAG1S,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAM2S,YAAY,GAAG,IAAI5X,KAAK,CAACyO,OAAO,CACpC,CAAC,EAAE,GAAGzJ,IAAI,CAAC6S,GAAG,CAACF,gBAAgB,CAAC,EAChC,GAAG,EACH,CAAC,EAAE,GAAG3S,IAAI,CAAC8S,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACAtE,MAAM,CAAC1M,QAAQ,CAACwD,IAAI,CAACsN,UAAU,CAAC,CAAC3I,GAAG,CAAC8I,YAAY,CAAC;;QAElD;QACAvE,MAAM,CAAC1J,EAAE,CAAC/E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAMmT,YAAY,GAAGN,UAAU,CAACpT,KAAK,CAAC,CAAC;QACvCgP,MAAM,CAAC9I,MAAM,CAACwN,YAAY,CAAC;;QAE3B;QACA1E,MAAM,CAAC2E,sBAAsB,CAAC,CAAC;QAC/B3E,MAAM,CAACzH,YAAY,CAAC,CAAC;QACrByH,MAAM,CAACxH,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACAxK,QAAQ,CAACiI,OAAO,GAAG,KAAK;;QAExB;QACAjI,QAAQ,CAACiJ,MAAM,CAACH,IAAI,CAACsN,UAAU,CAAC;QAChCpW,QAAQ,CAACmJ,MAAM,CAAC,CAAC;QAEjB5G,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnBoU,IAAI,EAAER,UAAU,CAAC1L,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAEuH,MAAM,CAAC1M,QAAQ,CAACoF,OAAO,CAAC,CAAC;UAC/BmM,IAAI,EAAEH,YAAY,CAAChM,OAAO,CAAC,CAAC;UAC5BoM,IAAI,EAAE9E,MAAM,CAAC+E,iBAAiB,CAAC,IAAIpY,KAAK,CAACyO,OAAO,CAAC,CAAC,CAAC,CAAC1C,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI3K,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAACiI,OAAO,GAAG,IAAI;;QAEvB;QACA+J,MAAM,CAAC1J,EAAE,CAAC/E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAII,IAAI,CAACqT,GAAG,CAAChF,MAAM,CAAC1M,QAAQ,CAAClC,CAAC,CAAC,GAAG,EAAE,EAAE;UACpC4O,MAAM,CAAC1M,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BvD,QAAQ,CAACiJ,MAAM,CAAC1F,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5ByO,MAAM,CAAC9I,MAAM,CAAClJ,QAAQ,CAACiJ,MAAM,CAAC;UAC9BjJ,QAAQ,CAACmJ,MAAM,CAAC,CAAC;QACnB;;QAEA;QACA;QACA6I,MAAM,CAACzH,YAAY,CAAC,CAAC;QACrByH,MAAM,CAACxH,iBAAiB,CAAC,IAAI,CAAC;MAEhC,CAAC,MAAM,IAAIzK,UAAU,KAAK,cAAc,EAAE;QACxC;QACAC,QAAQ,CAACmJ,MAAM,CAAC,CAAC;MACnB;MAEA,IAAInJ,QAAQ,EAAEA,QAAQ,CAACmJ,MAAM,CAAC,CAAC;MAC/B,IAAI9I,KAAK,IAAI2R,MAAM,EAAE;QACnBI,QAAQ,CAAC6E,MAAM,CAAC5W,KAAK,EAAE2R,MAAM,CAAC;MAChC;IACF,CAAC;IAEDkE,OAAO,CAAC,CAAC;;IAET;IACA,MAAMgB,YAAY,GAAGA,CAAA,KAAM;MACzBlF,MAAM,CAACmF,MAAM,GAAGxW,MAAM,CAACuR,UAAU,GAAGvR,MAAM,CAACwR,WAAW;MACtDH,MAAM,CAAC2E,sBAAsB,CAAC,CAAC;MAC/BvE,QAAQ,CAACG,OAAO,CAAC5R,MAAM,CAACuR,UAAU,EAAEvR,MAAM,CAACwR,WAAW,CAAC;IACzD,CAAC;IACDxR,MAAM,CAACyW,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACAvW,MAAM,CAAC0W,aAAa,GAAG,MAAM;MAC3B,IAAI/Q,SAAS,CAAC6B,OAAO,EAAE;QACrB7B,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC+C,SAAS,CAAC6B,OAAO,CAACe,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC5C,SAAS,CAAC6B,OAAO,CAACoC,YAAY,CAAC,CAAC;QAChCjE,SAAS,CAAC6B,OAAO,CAACqC,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAIxK,QAAQ,EAAE;UACZA,QAAQ,CAACiJ,MAAM,CAAC1F,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BvD,QAAQ,CAACiI,OAAO,GAAG,IAAI;UACvBjI,QAAQ,CAACmJ,MAAM,CAAC,CAAC;QACnB;QAEApJ,UAAU,GAAG,QAAQ;QACrBwC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAIkC,iBAAiB,CAACyD,OAAO,EAAE;QAC7BmP,oBAAoB,CAAC5S,iBAAiB,CAACyD,OAAO,CAAC;QAC/CzD,iBAAiB,CAACyD,OAAO,GAAG,IAAI;MAClC;;MAEA;MACA,IAAIxI,oBAAoB,EAAE;QACxB4X,aAAa,CAAC5X,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;;MAEA;MACA,IAAI8E,aAAa,CAAC0D,OAAO,EAAE;QACzB1D,aAAa,CAAC0D,OAAO,CAACiJ,KAAK,CAAC,CAAC;QAC7B3M,aAAa,CAAC0D,OAAO,GAAG,IAAI;MAC9B;;MAEA;MACAxH,MAAM,CAAC6W,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;;MAElD;MACA,IAAI9E,QAAQ,IAAIhO,YAAY,CAAC+D,OAAO,EAAE;QACpC/D,YAAY,CAAC+D,OAAO,CAACsP,WAAW,CAACrF,QAAQ,CAACQ,UAAU,CAAC;QACrDR,QAAQ,CAACsF,OAAO,CAAC,CAAC;MACpB;;MAEA;MACA,IAAItW,aAAa,EAAE;QACjBA,aAAa,CAACoK,OAAO,CAAC,CAAC0C,SAAS,EAAEC,EAAE,KAAK;UACvC,IAAID,SAAS,CAACR,KAAK,IAAIrN,KAAK,EAAE;YAC5BA,KAAK,CAAC+N,MAAM,CAACF,SAAS,CAACR,KAAK,CAAC;UAC/B;QACF,CAAC,CAAC;QACFtM,aAAa,CAACuW,KAAK,CAAC,CAAC;MACvB;;MAEA;MACAnW,gBAAgB,CAACgK,OAAO,CAAEoM,QAAQ,IAAK;QACrC,IAAIvX,KAAK,IAAIuX,QAAQ,CAAClK,KAAK,EAAE;UAC3BrN,KAAK,CAAC+N,MAAM,CAACwJ,QAAQ,CAAClK,KAAK,CAAC;QAC9B;MACF,CAAC,CAAC;MACFlM,gBAAgB,CAACmW,KAAK,CAAC,CAAC;MACxBlW,kBAAkB,CAACkW,KAAK,CAAC,CAAC;;MAE1B;;MAEA;MACAtX,KAAK,GAAG,IAAI;MACZL,QAAQ,GAAG,IAAI;MACfC,qBAAqB,GAAG,IAAI;MAC5BC,qBAAqB,GAAG,IAAI;MAC5BC,oBAAoB,GAAG,IAAI;MAC3BC,0BAA0B,GAAG,IAAI;MACjCZ,gBAAgB,GAAG,IAAI;MAEvB+C,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjE,SAAS,CAAC,MAAM;IACd;IACAmD,qBAAqB,CAAC,CAAC;;IAEvB;IACA,MAAMmW,uBAAuB,GAAGA,CAAA,KAAM;MACpCtV,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCd,qBAAqB,CAAC,CAAC;IACzB,CAAC;;IAED;IACAf,MAAM,CAACyW,gBAAgB,CAAC,oBAAoB,EAAES,uBAAuB,CAAC;;IAEtE;IACA,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnCrW,qBAAqB,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC;;IAET;IACA,OAAO,MAAM;MACXf,MAAM,CAAC6W,mBAAmB,CAAC,oBAAoB,EAAEK,uBAAuB,CAAC;MACzEN,aAAa,CAACO,UAAU,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvZ,SAAS,CAAC,MAAM;IACd;IACA,IAAI8B,KAAK,IAAIiE,SAAS,CAAC6D,OAAO,EAAE;MAC9B5F,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA,MAAMwV,KAAK,GAAGzH,UAAU,CAAC,MAAM;QAC7B,IAAIlQ,KAAK,IAAIiE,SAAS,CAAC6D,OAAO,EAAE;UAAG;UACjC8P,mBAAmB,CAAC3T,SAAS,CAAC6D,OAAO,CAAC;QACxC;MACF,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAM+P,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM;MACLzV,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC,EAAE,CAACnC,KAAK,CAAC,CAAC;;EAEX;EACA9B,SAAS,CAAC,MAAM;IACd,IAAI6F,YAAY,CAAC+D,OAAO,EAAE;MACxB;MACA,MAAMgQ,WAAW,GAAI1M,KAAK,IAAK;QAC7B,IAAIpL,KAAK,IAAIiG,SAAS,CAAC6B,OAAO,EAAE;UAC9B5F,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEiJ,KAAK,CAAC2M,OAAO,EAAE3M,KAAK,CAAC4M,OAAO,CAAC;UACpDC,gBAAgB,CAAC7M,KAAK,EAAErH,YAAY,CAAC+D,OAAO,EAAE9H,KAAK,EAAEiG,SAAS,CAAC6B,OAAO,EAAEzB,sBAAsB,CAAC;QACjG,CAAC,MAAM;UACLnE,OAAO,CAACuI,IAAI,CAAC,4BAA4B,CAAC;QAC5C;MACF,CAAC;;MAED;MACA1G,YAAY,CAAC+D,OAAO,CAACiP,gBAAgB,CAAC,OAAO,EAAEe,WAAW,CAAC;;MAE3D;MACA5V,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC4B,YAAY,CAAC+D,OAAO,CAAC;;MAEpD;MACA,OAAO,MAAM;QACX,IAAI/D,YAAY,CAAC+D,OAAO,EAAE;UACxB/D,YAAY,CAAC+D,OAAO,CAACqP,mBAAmB,CAAC,OAAO,EAAEW,WAAW,CAAC;UAC9D5V,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACnC,KAAK,EAAEiG,SAAS,CAAC6B,OAAO,CAAC,CAAC;;EAE9B;EACA,MAAMoQ,SAAS,GAAG7Z,WAAW,CAAC,MAAM;IAClC6D,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B;EACF,CAAC,EAAE,CAAC4B,YAAY,EAAEuD,aAAa,EAAEnG,gBAAgB,CAAC,CAAC;;EAEnD;EACA,MAAMgX,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMC,QAAQ,GAAG,IAAI9Z,KAAK,CAAC+Z,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChD,MAAMlE,QAAQ,GAAG,IAAI7V,KAAK,CAACga,iBAAiB,CAAC;MAAEpR,KAAK,EAAE;IAAS,CAAC,CAAC;IACjE,MAAMuI,iBAAiB,GAAG,IAAInR,KAAK,CAACia,IAAI,CAACH,QAAQ,EAAEjE,QAAQ,CAAC;;IAE5D;IACA,MAAMqE,YAAY,GAAG,IAAIla,KAAK,CAACma,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC5D,MAAMC,YAAY,GAAG,IAAIpa,KAAK,CAACga,iBAAiB,CAAC;MAAEpR,KAAK,EAAE;IAAS,CAAC,CAAC;IACrE,MAAMyR,SAAS,GAAG,IAAIra,KAAK,CAACia,IAAI,CAACC,YAAY,EAAEE,YAAY,CAAC;IAC5DC,SAAS,CAAC1T,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAClCuM,iBAAiB,CAACrC,GAAG,CAACuL,SAAS,CAAC;IAEhC,OAAOlJ,iBAAiB;EAC1B,CAAC;;EAED;EACA,MAAMmJ,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAC5Y,KAAK,EAAE;;IAEZ;IACAmB,gBAAgB,CAACgK,OAAO,CAAC,CAACoM,QAAQ,EAAEhR,OAAO,KAAK;MAC9C,IAAIgR,QAAQ,CAAClK,KAAK,EAAE;QAClB;QACA,MAAMwL,cAAc,GAAG,IAAIva,KAAK,CAACwa,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxD,MAAMC,cAAc,GAAG,IAAIza,KAAK,CAACga,iBAAiB,CAAC;UACjDpR,KAAK,EAAE,QAAQ;UAAC;UAChB8R,WAAW,EAAE,KAAK;UAClBC,OAAO,EAAE,GAAG;UAAG;UACfC,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,MAAMC,UAAU,GAAG,IAAI7a,KAAK,CAACia,IAAI,CAACM,cAAc,EAAEE,cAAc,CAAC;QACjEI,UAAU,CAAClU,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE;;QAEnC;QACAiW,UAAU,CAACC,QAAQ,GAAG;UACpBtO,IAAI,EAAE,cAAc;UACpBvE,OAAO,EAAEA,OAAO;UAChBoD,IAAI,EAAE4N,QAAQ,CAAC/N,YAAY,CAACG,IAAI;UAChC0P,aAAa,EAAE;QACjB,CAAC;;QAED;QACA9B,QAAQ,CAAClK,KAAK,CAACD,GAAG,CAAC+L,UAAU,CAAC;QAE9BjX,OAAO,CAACC,GAAG,CAAC,OAAOoV,QAAQ,CAAC/N,YAAY,CAACG,IAAI,KAAKpD,OAAO,aAAa,CAAC;MACzE;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACArI,SAAS,CAAC,MAAM;IACd;IACA,MAAMyZ,KAAK,GAAGzH,UAAU,CAAC,MAAM;MAC7B,IAAI/O,gBAAgB,CAACmY,IAAI,GAAG,CAAC,EAAE;QAC7BpX,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1ByW,eAAe,CAAC,CAAC;MACnB;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAE;;IAEX,OAAO,MAAMf,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACAzZ,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACA,IAAIyI,0BAA0B,CAACmB,OAAO,EAAE;QACtCoP,aAAa,CAACvQ,0BAA0B,CAACmB,OAAO,CAAC;QACjDnB,0BAA0B,CAACmB,OAAO,GAAG,IAAI;QACzC5F,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC9B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAjE,SAAS,CAAC,MAAM;IACd,IAAI,CAACkI,mBAAmB,CAACE,OAAO,IAAIK,0BAA0B,CAACmB,OAAO,EAAE;MACtEoP,aAAa,CAACvQ,0BAA0B,CAACmB,OAAO,CAAC;MACjDnB,0BAA0B,CAACmB,OAAO,GAAG,IAAI;MACzCpB,mBAAmB,CAACoB,OAAO,GAAG,IAAI;MAClC5F,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACnC;EACF,CAAC,EAAE,CAACiE,mBAAmB,CAACE,OAAO,CAAC,CAAC;;EAEjC;EACApI,SAAS,CAAC,MAAM;IACd;IACA,IAAIY,iBAAiB,IAAIA,iBAAiB,CAAC2K,aAAa,IAAI3K,iBAAiB,CAAC2K,aAAa,CAACkL,MAAM,GAAG,CAAC,EAAE;MACtG;MACA,IAAI,CAACzO,oBAAoB,EAAE;QACzB,MAAMqT,iBAAiB,GAAGza,iBAAiB,CAAC2K,aAAa,CAAC,CAAC,CAAC;QAC5DvH,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEoX,iBAAiB,CAAC5P,IAAI,CAAC;;QAEjD;QACA,MAAMgO,KAAK,GAAGzH,UAAU,CAAC,MAAM;UAC7B5G,wBAAwB,CAACiQ,iBAAiB,CAAC5P,IAAI,CAAC;QAClD,CAAC,EAAE,IAAI,CAAC;QAER,OAAO,MAAMkO,YAAY,CAACF,KAAK,CAAC;MAClC;IACF;EACF,CAAC,EAAE,CAAC7Y,iBAAiB,EAAEoH,oBAAoB,CAAC,CAAC;EAE7C,oBACElH,OAAA,CAAAE,SAAA;IAAAwV,QAAA,gBACE1V,OAAA;MAAMwa,KAAK,EAAExS,UAAW;MAAA0N,QAAA,EAAC;IAAK;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrC5a,OAAA,CAACJ,MAAM;MACL4a,KAAK,EAAE3S,uBAAwB;MAC/BgT,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAExQ,wBAAyB;MACnCyQ,OAAO,EAAEjb,iBAAiB,CAAC2K,aAAa,CAACgL,GAAG,CAACjL,YAAY,KAAK;QAC5DD,KAAK,EAAEC,YAAY,CAACG,IAAI;QACxBqQ,KAAK,EAAExQ,YAAY,CAACG;MACtB,CAAC,CAAC,CAAE;MACJ2P,IAAI,EAAC,OAAO;MACZW,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACb7U,MAAM,EAAE,IAAI;QACZ8U,SAAS,EAAE;MACb,CAAE;MACF5Q,KAAK,EAAErD,oBAAoB,GAAGA,oBAAoB,CAACyD,IAAI,GAAGyQ;IAAU;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eACF5a,OAAA;MAAKqb,GAAG,EAAEtW,YAAa;MAACyV,KAAK,EAAE;QAAEzS,KAAK,EAAE,MAAM;QAAEuT,MAAM,EAAE;MAAO;IAAE;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGnExT,mBAAmB,CAACE,OAAO,iBAC1BtH,OAAA;MACEwa,KAAK,EAAE;QACLvU,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAACpC,CAAC,IAAI;QAC3CiE,GAAG,EAAE,GAAGV,mBAAmB,CAACnB,QAAQ,CAAClC,CAAC,IAAI;QAC1CqC,SAAS,EAAE,wBAAwB;QACnCC,MAAM,EAAE,IAAI;QACZK,eAAe,EAAE,qBAAqB;QACtCwB,KAAK,EAAE,OAAO;QACdtB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,8BAA8B;QACzCN,OAAO,EAAE,GAAG;QACZ8U,QAAQ,EAAE,OAAO;QAAE;QACnBzU,QAAQ,EAAE,MAAM,CAAC;MACnB,CAAE;MAAA4O,QAAA,GAEDtO,mBAAmB,CAACI,OAAO,eAC5BxH,OAAA;QACEwa,KAAK,EAAE;UACLvU,QAAQ,EAAE,UAAU;UACpB6B,GAAG,EAAE,KAAK;UACV0T,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,MAAM;UAClB9U,MAAM,EAAE,MAAM;UACduB,KAAK,EAAE,OAAO;UACdpB,QAAQ,EAAE,MAAM;UAChBD,MAAM,EAAE,SAAS;UACjBJ,OAAO,EAAE;QACX,CAAE;QACFiV,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAACtU,sBAAsB,CAAE;QAAAqO,QAAA,EAC3D;MAED;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAED5a,OAAA;MAAKwa,KAAK,EAAExU,oBAAqB;MAAA0P,QAAA,gBAC/B1V,OAAA;QACEwa,KAAK,EAAE;UACL,GAAGhU,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF4V,OAAO,EAAE/S,kBAAmB;QAAA+M,QAAA,EAC7B;MAED;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT5a,OAAA;QACEwa,KAAK,EAAE;UACL,GAAGhU,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF4V,OAAO,EAAE7S,kBAAmB;QAAA6M,QAAA,EAC7B;MAED;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAA9V,EAAA,CAr7CMJ,WAAW;AAAAkX,EAAA,GAAXlX,WAAW;AAs7CjB,SAASmX,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAAChU,KAAK,GAAG,GAAG;EAClBgU,MAAM,CAACT,MAAM,GAAG,EAAE;;EAElB;EACAY,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAAChU,KAAK,GAAC,CAAC,EAAEgU,MAAM,CAACT,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAMkB,OAAO,GAAG,IAAIld,KAAK,CAACmd,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAIpd,KAAK,CAACqd,cAAc,CAAC;IAC9ClH,GAAG,EAAE+G,OAAO;IACZxC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM4C,MAAM,GAAG,IAAItd,KAAK,CAACud,MAAM,CAACH,cAAc,CAAC;EAC/CE,MAAM,CAACpG,KAAK,CAACtS,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAO0Y,MAAM;AACf;;AAIA;AACAtb,MAAM,CAACwb,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMnK,MAAM,GAAGqJ,QAAQ,CAACe,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAItK,MAAM,EAAE;MACV;MACA,MAAMuK,MAAM,GAAGvK,MAAM,CAAC1M,QAAQ,CAACtC,KAAK,CAAC,CAAC;;MAEtC;MACAgP,MAAM,CAAC1M,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9ByO,MAAM,CAAC1J,EAAE,CAAC/E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtByO,MAAM,CAAC9I,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACA8I,MAAM,CAACzH,YAAY,CAAC,CAAC;MACrByH,MAAM,CAACxH,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMxK,QAAQ,GAAGqb,QAAQ,CAACe,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIxc,QAAQ,EAAE;QACZA,QAAQ,CAACiJ,MAAM,CAAC1F,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BvD,QAAQ,CAACmJ,MAAM,CAAC,CAAC;MACnB;MAEA5G,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBia,GAAG,EAAEF,MAAM,CAAC7R,OAAO,CAAC,CAAC;QACrBgS,GAAG,EAAE1K,MAAM,CAAC1M,QAAQ,CAACoF,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAO2G,CAAC,EAAE;IACV9O,OAAO,CAACE,KAAK,CAAC,YAAY,EAAE4O,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAMS,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACFvP,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAMoT,MAAM,GAAG,IAAIhX,UAAU,CAAC,CAAC;;IAE/B;IACA,IAAI;MACF,MAAM,CAAC+d,WAAW,EAAEC,WAAW,EAAEC,UAAU,EAAEC,gBAAgB,CAAC,GAAG,MAAMlJ,OAAO,CAACmJ,GAAG,CAAC,CACnFnH,MAAM,CAACoH,SAAS,CAAC,GAAG7b,QAAQ,uBAAuB,CAAC,EACpDyU,MAAM,CAACoH,SAAS,CAAC,GAAG7b,QAAQ,uBAAuB,CAAC,EAClDyU,MAAM,CAACoH,SAAS,CAAC,GAAG7b,QAAQ,sBAAsB,CAAC,EACnDyU,MAAM,CAACoH,SAAS,CAAC,GAAG7b,QAAQ,4BAA4B,CAAC,CAC5D,CAAC;;MAEF;MACAlB,qBAAqB,GAAG0c,WAAW,CAACtc,KAAK;MACzCJ,qBAAqB,CAACoU,QAAQ,CAAEC,KAAK,IAAK;QACxC,IAAIA,KAAK,CAACC,MAAM,EAAE;UACd,MAAME,WAAW,GAAG,IAAI9V,KAAK,CAAC+V,oBAAoB,CAAC;YACnDnN,KAAK,EAAE,QAAQ;YACfoN,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,GAAG;YACdC,eAAe,EAAE;UACnB,CAAC,CAAC;;UAEA;UACA,IAAIP,KAAK,CAACE,QAAQ,CAACM,GAAG,EAAE;YACtBL,WAAW,CAACK,GAAG,GAAGR,KAAK,CAACE,QAAQ,CAACM,GAAG;UACtC;UACAR,KAAK,CAAC2I,OAAO,GAAGxI,WAAW;QAC/B;MACF,CAAC,CAAC;;MAEF;MACAvU,qBAAqB,GAAG0c,WAAW,CAACvc,KAAK;MACzC;MACAH,qBAAqB,CAAC2V,KAAK,CAACtS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxC;MACArD,qBAAqB,CAACmU,QAAQ,CAAEC,KAAK,IAAK;QACxC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;UAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;UAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;;MAEF;MACA1U,oBAAoB,GAAG0c,UAAU,CAACxc,KAAK;MACvC;MACAF,oBAAoB,CAAC0V,KAAK,CAACtS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACvC;MACApD,oBAAoB,CAACkU,QAAQ,CAAEC,KAAK,IAAK;QACvC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;UAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;UAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;;MAEA;MACAzU,0BAA0B,GAAG0c,gBAAgB,CAACzc,KAAK;MACnD;MACAD,0BAA0B,CAACyV,KAAK,CAACtS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C;MACAnD,0BAA0B,CAACiU,QAAQ,CAAEC,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;UAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;UAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;QACxC;MACF,CAAC,CAAC;MAEFtS,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACA,IAAI;QACF,IAAI,CAACxC,qBAAqB,EAAE;UAC1B,MAAM0c,WAAW,GAAG,MAAM/G,MAAM,CAACoH,SAAS,CAAC,GAAG7b,QAAQ,uBAAuB,CAAC;UAC9ElB,qBAAqB,GAAG0c,WAAW,CAACtc,KAAK;QAC3C;;QAEA;QACA,IAAI,CAACD,0BAA0B,EAAE;UAC/BmC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC7B,MAAMsa,gBAAgB,GAAG,MAAMlH,MAAM,CAACoH,SAAS,CAAC,GAAG7b,QAAQ,4BAA4B,CAAC;UACxFf,0BAA0B,GAAG0c,gBAAgB,CAACzc,KAAK;UACnDD,0BAA0B,CAACyV,KAAK,CAACtS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC7ChB,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QAC1B;MACF,CAAC,CAAC,OAAO0a,GAAG,EAAE;QACZ3a,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEya,GAAG,CAAC;MAClC;IACF;EACF,CAAC,CAAC,OAAOza,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;;AAED;AACA,MAAMyO,mBAAmB,GAAI/F,IAAI,IAAK;EACpC,MAAMgS,KAAK,GAAG;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,KAAK,CAAChS,IAAI,CAAC,IAAI,MAAM;AAC9B,CAAC;;AAED;AACA,MAAMkB,iBAAiB,GAAGA,CAAC/G,QAAQ,EAAE6V,IAAI,EAAE5T,KAAK,KAAK;EACnD;EACA,IAAI,CAAClH,KAAK,EAAE;IACVkC,OAAO,CAACuI,IAAI,CAAC,sBAAsB,CAAC;IACpC;EACF;EAEA,IAAI;IACF;IACA,MAAMmR,MAAM,GAAGf,gBAAgB,CAACC,IAAI,CAAC;IACrCc,MAAM,CAAC3W,QAAQ,CAAC/B,GAAG,CAAC+B,QAAQ,CAACpC,CAAC,EAAE,EAAE,EAAE,CAACoC,QAAQ,CAAClC,CAAC,CAAC,CAAC,CAAE;;IAEnD;IACAmN,UAAU,CAAC,MAAM;MACf,IAAIlQ,KAAK,IAAI4b,MAAM,IAAIA,MAAM,CAACmB,MAAM,EAAE;QACpC/c,KAAK,CAAC+N,MAAM,CAAC6N,MAAM,CAAC;MACtB;IACF,CAAC,EAAE,GAAG,CAAC;;IAEP;IACA5b,KAAK,CAACoN,GAAG,CAACwO,MAAM,CAAC;IAEjB1Z,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;MACvBmK,EAAE,EAAErH,QAAQ;MACZ+X,EAAE,EAAElC,IAAI;MACRmC,EAAE,EAAE/V;IACN,CAAC,CAAC;EACJ,CAAC,CAAC,OAAO9E,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;EACnC;AACF,CAAC;;AAED;AACA,MAAMwV,mBAAmB,GAAIsF,iBAAiB,IAAK;EACjD,IAAI,CAACld,KAAK,EAAE;IACVkC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAC;IAC/B;EACF;EAEA,IAAI,CAAC8a,iBAAiB,EAAE;IACtBhb,OAAO,CAACE,KAAK,CAAC,mBAAmB,CAAC;IAClC;EACF;EAEA,IAAI,CAACrC,0BAA0B,EAAE;IAC/BmC,OAAO,CAACE,KAAK,CAAC,UAAU,CAAC;IACzB;IACA+a,2BAA2B,CAACD,iBAAiB,CAAC;IAC9C;EACF;;EAEA;EACA/b,gBAAgB,CAACgK,OAAO,CAAEoM,QAAQ,IAAK;IACrC,IAAIvX,KAAK,IAAIuX,QAAQ,CAAClK,KAAK,EAAE;MAC3BrN,KAAK,CAAC+N,MAAM,CAACwJ,QAAQ,CAAClK,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACFlM,gBAAgB,CAACmW,KAAK,CAAC,CAAC;;EAExB;EACAxY,iBAAiB,CAAC2K,aAAa,CAAC0B,OAAO,CAAC3B,YAAY,IAAI;IACtD,IAAIA,YAAY,CAAC7E,QAAQ,IAAI6E,YAAY,CAAC9E,SAAS,IAAI8E,YAAY,CAACjD,OAAO,EAAE;MAC3E;MACA,MAAMsF,QAAQ,GAAGqR,iBAAiB,CAACrT,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAC9E,SAAS,CAAC,EAClCoF,UAAU,CAACN,YAAY,CAAC7E,QAAQ,CAClC,CAAC;MAED,IAAI;QACF;QACA,MAAM8K,iBAAiB,GAAG1P,0BAA0B,CAAC4C,KAAK,CAAC,CAAC;;QAE5D;QACA8M,iBAAiB,CAAC9F,IAAI,GAAG,OAAOH,YAAY,CAACG,IAAI,EAAE;;QAEnD;QACA8F,iBAAiB,CAACxK,QAAQ,CAAC/B,GAAG,CAAC2I,QAAQ,CAAChJ,CAAC,EAAE,EAAE,EAAE,CAACgJ,QAAQ,CAAC9I,CAAC,CAAC;;QAE3D;QACA0M,iBAAiB,CAAC+F,KAAK,CAACtS,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;QAEvC;QACAuM,iBAAiB,CAAC2N,WAAW,GAAG,GAAG;;QAEnC;QACA3N,iBAAiB,CAACuE,QAAQ,CAACC,KAAK,IAAI;UAClC;UACA,IAAIA,KAAK,CAACC,MAAM,EAAE;YAChB;YACAD,KAAK,CAACE,QAAQ,CAAC6E,WAAW,GAAG,KAAK;YAClC/E,KAAK,CAACE,QAAQ,CAAC8E,OAAO,GAAG,GAAG;YAC5BhF,KAAK,CAACE,QAAQ,CAACkJ,IAAI,GAAG/e,KAAK,CAACgf,UAAU;YACtCrJ,KAAK,CAACE,QAAQ,CAAC+E,UAAU,GAAG,IAAI;YAChCjF,KAAK,CAACE,QAAQ,CAACoJ,SAAS,GAAG,IAAI;YAC/BtJ,KAAK,CAACE,QAAQ,CAACqJ,WAAW,GAAG,IAAI;;YAEjC;YACAvJ,KAAK,CAACmJ,WAAW,GAAG,GAAG;UACzB;QACF,CAAC,CAAC;;QAEF;QACA3N,iBAAiB,CAAC2J,QAAQ,GAAG;UAC3BtO,IAAI,EAAE,cAAc;UACpBvE,OAAO,EAAEiD,YAAY,CAACjD,OAAO;UAC7BoD,IAAI,EAAEH,YAAY,CAACG;QACrB,CAAC;;QAED;QACA,MAAM8T,gBAAgB,GAAG,IAAInf,KAAK,CAACwa,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;QAC5D,MAAM4E,gBAAgB,GAAG,IAAIpf,KAAK,CAACga,iBAAiB,CAAC;UACnDpR,KAAK,EAAE,QAAQ;UACf8R,WAAW,EAAE,IAAI;UACjBC,OAAO,EAAE,GAAG;UAAG;UACfC,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,MAAMyE,QAAQ,GAAG,IAAIrf,KAAK,CAACia,IAAI,CAACkF,gBAAgB,EAAEC,gBAAgB,CAAC;QACnEC,QAAQ,CAAChU,IAAI,GAAG,UAAUH,YAAY,CAACG,IAAI,EAAE;QAC7CgU,QAAQ,CAACvE,QAAQ,GAAG;UAClBtO,IAAI,EAAE,cAAc;UACpBvE,OAAO,EAAEiD,YAAY,CAACjD,OAAO;UAC7BoD,IAAI,EAAEH,YAAY,CAACG,IAAI;UACvBiU,UAAU,EAAE;QACd,CAAC;QAEDnO,iBAAiB,CAACrC,GAAG,CAACuQ,QAAQ,CAAC;;QAE/B;QACA3d,KAAK,CAACoN,GAAG,CAACqC,iBAAiB,CAAC;;QAE5B;QACAtO,gBAAgB,CAAC+B,GAAG,CAACsG,YAAY,CAACjD,OAAO,EAAE;UACzC8G,KAAK,EAAEoC,iBAAiB;UACxBjG,YAAY,EAAEA,YAAY;UAC1BvE,QAAQ,EAAE4G;QACZ,CAAC,CAAC;QAEF3J,OAAO,CAACC,GAAG,CAAC,SAASqH,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACjD,OAAO,kBAAkBsF,QAAQ,CAAChJ,CAAC,KAAK,CAACgJ,QAAQ,CAAC9I,CAAC,GAAG,CAAC;MACjH,CAAC,CAAC,OAAOX,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,QAAQoH,YAAY,CAACG,IAAI,YAAY,EAAEvH,KAAK,CAAC;QAC3D;QACA+V,wBAAwB,CAAC3O,YAAY,EAAEqC,QAAQ,EAAEqR,iBAAiB,CAAC;MACrE;IACF;EACF,CAAC,CAAC;;EAEF;EACAhb,OAAO,CAACC,GAAG,CAAC,OAAOhB,gBAAgB,CAACmY,IAAI,SAAS,CAAC;EAClDnY,gBAAgB,CAACgK,OAAO,CAAC,CAACoM,QAAQ,EAAEhR,OAAO,KAAK;IAC9CrE,OAAO,CAACC,GAAG,CAAC,QAAQoE,OAAO,KAAKgR,QAAQ,CAAC/N,YAAY,CAACG,IAAI,EAAE,CAAC;EAC/D,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMwO,wBAAwB,GAAGA,CAAC3O,YAAY,EAAEqC,QAAQ,EAAEqR,iBAAiB,KAAK;EAC9E;EACA,MAAM9E,QAAQ,GAAG,IAAI9Z,KAAK,CAAC+Z,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAClD,MAAMlE,QAAQ,GAAG,IAAI7V,KAAK,CAACga,iBAAiB,CAAC;IAC3CpR,KAAK,EAAE,QAAQ;IACf8R,WAAW,EAAE,KAAK;IAClBC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAMxJ,iBAAiB,GAAG,IAAInR,KAAK,CAACia,IAAI,CAACH,QAAQ,EAAEjE,QAAQ,CAAC;;EAE5D;EACA1E,iBAAiB,CAAC9F,IAAI,GAAG,SAASH,YAAY,CAACG,IAAI,EAAE;;EAErD;EACA8F,iBAAiB,CAACxK,QAAQ,CAAC/B,GAAG,CAAC2I,QAAQ,CAAChJ,CAAC,EAAE,EAAE,EAAE,CAACgJ,QAAQ,CAAC9I,CAAC,CAAC;;EAE3D;EACA0M,iBAAiB,CAAC2N,WAAW,GAAG,GAAG;;EAEnC;EACA3N,iBAAiB,CAAC2J,QAAQ,GAAG;IAC3BtO,IAAI,EAAE,cAAc;IACpBvE,OAAO,EAAEiD,YAAY,CAACjD,OAAO;IAC7BoD,IAAI,EAAEH,YAAY,CAACG;EACrB,CAAC;;EAED;EACA,MAAM8T,gBAAgB,GAAG,IAAInf,KAAK,CAACwa,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5D,MAAM4E,gBAAgB,GAAG,IAAIpf,KAAK,CAACga,iBAAiB,CAAC;IACnDpR,KAAK,EAAE,QAAQ;IACf8R,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE,GAAG;IAAG;IACfC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMyE,QAAQ,GAAG,IAAIrf,KAAK,CAACia,IAAI,CAACkF,gBAAgB,EAAEC,gBAAgB,CAAC;EACnEC,QAAQ,CAAChU,IAAI,GAAG,YAAYH,YAAY,CAACG,IAAI,EAAE;EAC/CgU,QAAQ,CAACvE,QAAQ,GAAG;IAClBtO,IAAI,EAAE,cAAc;IACpBvE,OAAO,EAAEiD,YAAY,CAACjD,OAAO;IAC7BoD,IAAI,EAAEH,YAAY,CAACG,IAAI;IACvBiU,UAAU,EAAE;EACd,CAAC;EAEDnO,iBAAiB,CAACrC,GAAG,CAACuQ,QAAQ,CAAC;;EAE/B;EACA3d,KAAK,CAACoN,GAAG,CAACqC,iBAAiB,CAAC;;EAE5B;EACAtO,gBAAgB,CAAC+B,GAAG,CAACsG,YAAY,CAACjD,OAAO,EAAE;IACzC8G,KAAK,EAAEoC,iBAAiB;IACxBjG,YAAY,EAAEA,YAAY;IAC1BvE,QAAQ,EAAE4G;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMgS,aAAa,GAAG,IAAIvf,KAAK,CAACwa,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAMgF,aAAa,GAAG,IAAIxf,KAAK,CAACga,iBAAiB,CAAC;IAAEpR,KAAK,EAAE;EAAS,CAAC,CAAC;EACtE,MAAM6W,SAAS,GAAG,IAAIzf,KAAK,CAACia,IAAI,CAACsF,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAAC9Y,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;EAEhC;EACA6a,SAAS,CAAC3E,QAAQ,GAAG;IACnBtO,IAAI,EAAE,cAAc;IACpBvE,OAAO,EAAEiD,YAAY,CAACjD,OAAO;IAC7BoD,IAAI,EAAEH,YAAY,CAACG;EACrB,CAAC;EAED8F,iBAAiB,CAACrC,GAAG,CAAC2Q,SAAS,CAAC;EAEhC7b,OAAO,CAACC,GAAG,CAAC,SAASqH,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACjD,OAAO,kBAAkBsF,QAAQ,CAAChJ,CAAC,KAAK,CAACgJ,QAAQ,CAAC9I,CAAC,GAAG,CAAC;AACjH,CAAC;;AAED;AACA,MAAMoa,2BAA2B,GAAID,iBAAiB,IAAK;EACzDpe,iBAAiB,CAAC2K,aAAa,CAAC0B,OAAO,CAAC3B,YAAY,IAAI;IACtD,IAAIA,YAAY,CAAC7E,QAAQ,IAAI6E,YAAY,CAAC9E,SAAS,IAAI8E,YAAY,CAACjD,OAAO,EAAE;MAC3E;MACA,MAAMsF,QAAQ,GAAGqR,iBAAiB,CAACrT,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAC9E,SAAS,CAAC,EAClCoF,UAAU,CAACN,YAAY,CAAC7E,QAAQ,CAClC,CAAC;MAEDwT,wBAAwB,CAAC3O,YAAY,EAAEqC,QAAQ,EAAEqR,iBAAiB,CAAC;IACrE;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMlO,iBAAiB,GAAIL,OAAO,IAAK;EACrC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMI,oBAAoB,GAAIiP,OAAO,IAAK;EACxC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAM/F,gBAAgB,GAAGA,CAAC7M,KAAK,EAAE6S,SAAS,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,KAAK;EAC7F,IAAI,CAACH,SAAS,IAAI,CAACC,aAAa,IAAI,CAACC,cAAc,EAAE;EAErDjc,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEiJ,KAAK,CAAC2M,OAAO,EAAE3M,KAAK,CAAC4M,OAAO,CAAC;;EAEvD;EACA,MAAMqG,IAAI,GAAGJ,SAAS,CAACK,qBAAqB,CAAC,CAAC;EAC9C,MAAMC,MAAM,GAAI,CAACnT,KAAK,CAAC2M,OAAO,GAAGsG,IAAI,CAAClZ,IAAI,IAAI8Y,SAAS,CAACO,WAAW,GAAI,CAAC,GAAG,CAAC;EAC5E,MAAMC,MAAM,GAAG,EAAE,CAACrT,KAAK,CAAC4M,OAAO,GAAGqG,IAAI,CAACvX,GAAG,IAAImX,SAAS,CAACS,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;;EAE7E;EACA,MAAMC,SAAS,GAAG,IAAIrgB,KAAK,CAACsgB,SAAS,CAAC,CAAC;EACvC;EACAD,SAAS,CAACE,MAAM,CAACC,MAAM,CAACC,SAAS,GAAG,CAAC;EACrCJ,SAAS,CAACE,MAAM,CAACG,IAAI,CAACD,SAAS,GAAG,CAAC;EAEnC,MAAME,WAAW,GAAG,IAAI3gB,KAAK,CAAC4gB,OAAO,CAACX,MAAM,EAAEE,MAAM,CAAC;EACrDE,SAAS,CAACQ,aAAa,CAACF,WAAW,EAAEd,cAAc,CAAC;EAEpDjc,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEoc,MAAM,EAAEE,MAAM,CAAC;;EAEtC;EACA,MAAMW,mBAAmB,GAAG,EAAE;EAE9Bje,gBAAgB,CAACgK,OAAO,CAAC,CAACoM,QAAQ,EAAEhR,OAAO,KAAK;IAC9C,IAAIgR,QAAQ,CAAClK,KAAK,EAAE;MAClB;MACA+R,mBAAmB,CAAC9P,IAAI,CAACiI,QAAQ,CAAClK,KAAK,CAAC;MACxC;MACAkK,QAAQ,CAAClK,KAAK,CAAC/G,OAAO,GAAG,IAAI;MAC7BiR,QAAQ,CAAClK,KAAK,CAAC+P,WAAW,GAAG,IAAI,CAAC,CAAC;MACnC;MACA7F,QAAQ,CAAClK,KAAK,CAAC2G,QAAQ,CAACC,KAAK,IAAI;QAC/BA,KAAK,CAAC3N,OAAO,GAAG,IAAI;QACpB2N,KAAK,CAACmJ,WAAW,GAAG,IAAI;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEFlb,OAAO,CAACC,GAAG,CAAC,QAAQid,mBAAmB,CAACzK,MAAM,gBAAgB,CAAC;;EAE/D;EACA,MAAM0K,sBAAsB,GAAGV,SAAS,CAACW,gBAAgB,CAACF,mBAAmB,EAAE,IAAI,CAAC;EAEpF,IAAIC,sBAAsB,CAAC1K,MAAM,GAAG,CAAC,EAAE;IACrCzS,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkd,sBAAsB,CAAC1K,MAAM,CAAC;IACxD0K,sBAAsB,CAAClU,OAAO,CAAC,CAACoU,SAAS,EAAEC,KAAK,KAAK;MACnDtd,OAAO,CAACC,GAAG,CAAC,QAAQqd,KAAK,GAAG,EACjBD,SAAS,CAACE,MAAM,CAAC9V,IAAI,IAAI,KAAK,EAC9B,KAAK,EAAE4V,SAAS,CAACrM,QAAQ,EACzB,WAAW,EAAEqM,SAAS,CAACE,MAAM,CAACrG,QAAQ,CAAC;IACpD,CAAC,CAAC;;IAEF;IACA,MAAMsG,GAAG,GAAGC,yBAAyB,CAACN,sBAAsB,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC;IAEvE,IAAIC,GAAG,IAAIA,GAAG,CAACtG,QAAQ,IAAIsG,GAAG,CAACtG,QAAQ,CAACtO,IAAI,KAAK,cAAc,EAAE;MAC/D;MACA,MAAMvE,OAAO,GAAGmZ,GAAG,CAACtG,QAAQ,CAAC7S,OAAO;MACpCrE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoE,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;;MAEhE;MACA,IAAIqZ,SAAS,GAAGrZ,OAAO;MACvB,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,CAACpF,gBAAgB,CAAC4O,GAAG,CAACxJ,OAAO,CAAC,IAAIpF,gBAAgB,CAAC4O,GAAG,CAACX,QAAQ,CAAC7I,OAAO,CAAC,CAAC,EAAE;QAC5GqZ,SAAS,GAAGxQ,QAAQ,CAAC7I,OAAO,CAAC;QAC7BrE,OAAO,CAACC,GAAG,CAAC,cAAcyd,SAAS,EAAE,CAAC;MACxC,CAAC,MAAM,IAAI,OAAOrZ,OAAO,KAAK,QAAQ,IAAI,CAACpF,gBAAgB,CAAC4O,GAAG,CAACxJ,OAAO,CAAC,IAAIpF,gBAAgB,CAAC4O,GAAG,CAACP,MAAM,CAACjJ,OAAO,CAAC,CAAC,EAAE;QACjHqZ,SAAS,GAAGpQ,MAAM,CAACjJ,OAAO,CAAC;QAC3BrE,OAAO,CAACC,GAAG,CAAC,eAAeyd,SAAS,EAAE,CAAC;MACzC;;MAEA;MACAtf,MAAM,CAAC6P,qBAAqB,CAACyP,SAAS,CAAC;MACvC;IACF;EACF;;EAEA;EACA,MAAMC,UAAU,GAAGlB,SAAS,CAACW,gBAAgB,CAACpB,aAAa,CAACxJ,QAAQ,EAAE,IAAI,CAAC;EAE3ExS,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE0d,UAAU,CAAClL,MAAM,CAAC;EAE7C,IAAIkL,UAAU,CAAClL,MAAM,GAAG,CAAC,EAAE;IACzB;IACAkL,UAAU,CAAC1U,OAAO,CAAC,CAACoU,SAAS,EAAEC,KAAK,KAAK;MACvC,MAAME,GAAG,GAAGH,SAAS,CAACE,MAAM;MAC5Bvd,OAAO,CAACC,GAAG,CAAC,UAAUqd,KAAK,GAAG,EAAEE,GAAG,CAAC/V,IAAI,IAAI,KAAK,EACrC,WAAW,EAAE+V,GAAG,CAACtG,QAAQ,EACzB,KAAK,EAAEmG,SAAS,CAACrM,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACA,KAAK,IAAIxJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmW,UAAU,CAAClL,MAAM,EAAEjL,CAAC,EAAE,EAAE;MAC1C,MAAMgW,GAAG,GAAGC,yBAAyB,CAACE,UAAU,CAACnW,CAAC,CAAC,CAAC+V,MAAM,CAAC;MAC3D,IAAIC,GAAG,IAAIA,GAAG,CAACtG,QAAQ,IAAIsG,GAAG,CAACtG,QAAQ,CAACtO,IAAI,KAAK,cAAc,EAAE;QAC/D,MAAMvE,OAAO,GAAGmZ,GAAG,CAACtG,QAAQ,CAAC7S,OAAO;QACpCrE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoE,OAAO,CAAC;;QAEvC;QACAjG,MAAM,CAAC6P,qBAAqB,CAAC5J,OAAO,CAAC;QACrC;MACF;IACF;EACF;;EAEA;EACArE,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;;EAE9B;EACA,IAAI2d,YAAY,GAAG,IAAI;EACvB,IAAI/W,WAAW,GAAG,GAAG,CAAC,CAAC;;EAEvB5H,gBAAgB,CAACgK,OAAO,CAAC,CAACoM,QAAQ,EAAEhR,OAAO,KAAK;IAC9C,IAAIgR,QAAQ,CAAClK,KAAK,EAAE;MAClB,MAAM0S,QAAQ,GAAG,IAAIzhB,KAAK,CAACyO,OAAO,CAAC,CAAC;MACpC;MACAwK,QAAQ,CAAClK,KAAK,CAAC2S,gBAAgB,CAACD,QAAQ,CAAC;;MAEzC;MACA,MAAME,SAAS,GAAGF,QAAQ,CAACpd,KAAK,CAAC,CAAC;MAClCsd,SAAS,CAACC,OAAO,CAAC/B,cAAc,CAAC;;MAEjC;MACA,MAAMgC,EAAE,GAAGF,SAAS,CAACpd,CAAC,GAAG0b,MAAM;MAC/B,MAAM6B,EAAE,GAAGH,SAAS,CAACld,CAAC,GAAG0b,MAAM;MAC/B,MAAMvL,QAAQ,GAAG5P,IAAI,CAAC+c,IAAI,CAACF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;MAE7Cle,OAAO,CAACC,GAAG,CAAC,MAAMoE,OAAO,OAAO,EAAE2M,QAAQ,CAAC;MAE3C,IAAIA,QAAQ,GAAGnK,WAAW,EAAE;QAC1BA,WAAW,GAAGmK,QAAQ;QACtB4M,YAAY,GAAG;UAAEvZ,OAAO;UAAE2M;QAAS,CAAC;MACtC;IACF;EACF,CAAC,CAAC;EAEF,IAAI4M,YAAY,EAAE;IAChB5d,OAAO,CAACC,GAAG,CAAC,oBAAoB2d,YAAY,CAACvZ,OAAO,SAASuZ,YAAY,CAAC5M,QAAQ,EAAE,CAAC;;IAErF;IACA5S,MAAM,CAAC6P,qBAAqB,CAAC2P,YAAY,CAACvZ,OAAO,CAAC;IAClD;EACF;EAEArE,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;;EAE1B;EACA,IAAIic,eAAe,EAAE;IACnBA,eAAe,CAACzO,IAAI,IAAI;MACtB,IAAIA,IAAI,CAACrJ,OAAO,EAAE;QAChB,OAAO;UAAE,GAAGqJ,IAAI;UAAErJ,OAAO,EAAE;QAAM,CAAC;MACpC;MACA,OAAOqJ,IAAI;IACb,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAMgL,kBAAkB,GAAIyD,eAAe,IAAK;EAC9C;EACA,IAAI9d,MAAM,CAACqG,0BAA0B,IAAIrG,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,EAAE;IAClFoP,aAAa,CAAC5W,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,CAAC;IACxDxH,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,GAAG,IAAI;IAChD5F,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B;;EAEA;EACA,IAAI7B,MAAM,CAACoG,mBAAmB,EAAE;IAC9BpG,MAAM,CAACoG,mBAAmB,CAACoB,OAAO,GAAG,IAAI;EAC3C;;EAEA;EACAsW,eAAe,CAACzO,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAErJ,OAAO,EAAE;EAAM,CAAC,CAAC,CAAC;EACtDpE,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;AACtB,CAAC;;AAED;AACA7B,MAAM,CAACggB,qBAAqB,GAAI/Z,OAAO,IAAK;EAC1C,IAAI;IAAA,IAAAga,qBAAA,EAAAC,sBAAA;IACF;IACA,MAAMtR,YAAY,GAAG/N,gBAAgB,CAAC8L,GAAG,CAAC1G,OAAO,IAAI,GAAG,CAAC;IACzD,IAAI,CAAC2I,YAAY,EAAE;MACjBhN,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEmE,OAAO,CAAC;;MAEtC;MACArE,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxBhB,gBAAgB,CAACgK,OAAO,CAAC,CAACsV,KAAK,EAAE3S,EAAE,KAAK;QACtC5L,OAAO,CAACC,GAAG,CAAC,KAAK2L,EAAE,KAAK2S,KAAK,CAACjX,YAAY,CAACG,IAAI,EAAE,CAAC;MACpD,CAAC,CAAC;MAEF,OAAO,KAAK;IACd;;IAEA;IACA,MAAM+W,UAAU,GAAGxR,YAAY,CAAC7B,KAAK;;IAErC;IACA,MAAMsT,SAAS,GAAGvf,kBAAkB,CAAC6L,GAAG,CAAC1G,OAAO,CAAC;IACjD,MAAMiD,YAAY,GAAG0F,YAAY,CAAC1F,YAAY;;IAE9C;IACA,IAAIhD,OAAO;IAEX,IAAIma,SAAS,IAAIA,SAAS,CAACla,MAAM,EAAE;MACjCD,OAAO,gBACLxH,OAAA;QAAKwa,KAAK,EAAE;UAAE/T,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAEoT,SAAS,EAAE,OAAO;UAAEyG,SAAS,EAAE;QAAO,CAAE;QAAAlM,QAAA,gBACpF1V,OAAA;UAAKwa,KAAK,EAAE;YACVrS,UAAU,EAAE,MAAM;YAClB0Z,YAAY,EAAE,KAAK;YACnB/a,QAAQ,EAAE,MAAM;YAChBgb,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAArM,QAAA,GACClL,YAAY,CAACG,IAAI,EAAC,QAAM,EAACpD,OAAO,EAAC,GACpC;QAAA;UAAAkT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN5a,OAAA;UAAA0V,QAAA,EACGiM,SAAS,CAACla,MAAM,CAACgO,GAAG,CAAC,CAAC/F,KAAK,EAAE8Q,KAAK,KAAK;YACtC,IAAIwB,UAAU;YACd,QAAQtS,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBAAE8R,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;gBAAEA,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;cAAE;gBAASA,UAAU,GAAG,SAAS;gBAAE;YAC7C;YAEA,oBACEhiB,OAAA;cAAiBwa,KAAK,EAAE;gBACtBqH,YAAY,EAAE,KAAK;gBACnBnb,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE,KAAK;gBACnBE,QAAQ,EAAE;cACZ,CAAE;cAAA4O,QAAA,gBACA1V,OAAA;gBAAKwa,KAAK,EAAE;kBAAErS,UAAU,EAAE;gBAAO,CAAE;gBAAAuN,QAAA,EAChC1F,iBAAiB,CAACN,KAAK,CAACC,OAAO;cAAC;gBAAA8K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACN5a,OAAA;gBAAKwa,KAAK,EAAE;kBAAElU,OAAO,EAAE,MAAM;kBAAE2b,cAAc,EAAE;gBAAgB,CAAE;gBAAAvM,QAAA,gBAC/D1V,OAAA;kBAAA0V,QAAA,EAAM;gBAAI;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjB5a,OAAA;kBAAMwa,KAAK,EAAE;oBACXtS,KAAK,EAAE8Z,UAAU;oBACjB7Z,UAAU,EAAE,MAAM;oBAClBzB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAA8O,QAAA,EACChG,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGR,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAG;gBAAI;kBAAAuK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN5a,OAAA;gBAAKwa,KAAK,EAAE;kBAAElU,OAAO,EAAE,MAAM;kBAAE2b,cAAc,EAAE;gBAAgB,CAAE;gBAAAvM,QAAA,gBAC/D1V,OAAA;kBAAA0V,QAAA,EAAM;gBAAK;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClB5a,OAAA;kBAAMwa,KAAK,EAAE;oBAAErS,UAAU,EAAE;kBAAO,CAAE;kBAAAuN,QAAA,GAAEhG,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAAsK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAzBE4F,KAAK;cAAA/F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN5a,OAAA;UAAKwa,KAAK,EAAE;YAAE0H,SAAS,EAAE,KAAK;YAAEpb,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE;UAAO,CAAE;UAAAwN,QAAA,GAAC,4BAC3D,EAAC,IAAInH,IAAI,CAAC,CAAC,CAAC4T,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAA1H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACLpT,OAAO,gBACLxH,OAAA;QAAKwa,KAAK,EAAE;UAAE/T,OAAO,EAAE,KAAK;UAAE8U,QAAQ,EAAE;QAAQ,CAAE;QAAA7F,QAAA,gBAChD1V,OAAA;UAAKwa,KAAK,EAAE;YAAErS,UAAU,EAAE,MAAM;YAAE0Z,YAAY,EAAE;UAAM,CAAE;UAAAnM,QAAA,EAAElL,YAAY,CAACG;QAAI;UAAA8P,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClF5a,OAAA;UAAA0V,QAAA,GAAK,kBAAM,EAACnO,OAAO;QAAA;UAAAkT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1B5a,OAAA;UAAA0V,QAAA,EAAK;QAAU;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN;IACH;;IAEA;IACA,MAAMwH,OAAO,GAAG9gB,MAAM,CAACuR,UAAU,GAAG,CAAC;IACrC,MAAMwP,OAAO,GAAG/gB,MAAM,CAACwR,WAAW,GAAG,CAAC;;IAEtC;IACA,MAAMsM,eAAe,IAAAmC,qBAAA,GAAGvF,QAAQ,CAACe,aAAa,CAAC,OAAO,CAAC,cAAAwE,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCe,gBAAgB,cAAAd,sBAAA,uBAAjDA,sBAAA,CAAmDna,sBAAsB;IAEjG,IAAI+X,eAAe,EAAE;MACnB;MACAA,eAAe,CAAC;QACd9X,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBtB,QAAQ,EAAE;UAAEpC,CAAC,EAAEue,OAAO;UAAEre,CAAC,EAAEse;QAAQ,CAAC;QACpC7a,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAAka,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEla,MAAM,KAAI;MAC/B,CAAC,CAAC;MAEFvE,OAAO,CAACC,GAAG,CAAC,SAASqH,YAAY,CAACG,IAAI,KAAKpD,OAAO,UAAU,CAAC;MAC7D,OAAO,IAAI;IACb,CAAC,MAAM;MACL;MACA,MAAMgb,OAAO,GAAGvG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CsG,OAAO,CAAC/H,KAAK,CAACvU,QAAQ,GAAG,UAAU;MACnCsc,OAAO,CAAC/H,KAAK,CAACrU,IAAI,GAAG,GAAGic,OAAO,IAAI;MACnCG,OAAO,CAAC/H,KAAK,CAAC1S,GAAG,GAAG,GAAGua,OAAO,IAAI;MAClCE,OAAO,CAAC/H,KAAK,CAACpU,SAAS,GAAG,wBAAwB;MAClDmc,OAAO,CAAC/H,KAAK,CAACnU,MAAM,GAAG,MAAM;MAC7Bkc,OAAO,CAAC/H,KAAK,CAAC9T,eAAe,GAAG,qBAAqB;MACrD6b,OAAO,CAAC/H,KAAK,CAACtS,KAAK,GAAG,OAAO;MAC7Bqa,OAAO,CAAC/H,KAAK,CAAC5T,YAAY,GAAG,KAAK;MAClC2b,OAAO,CAAC/H,KAAK,CAACzT,SAAS,GAAG,8BAA8B;MACxDwb,OAAO,CAAC/H,KAAK,CAAC/T,OAAO,GAAG,KAAK;MAC7B8b,OAAO,CAAC/H,KAAK,CAACe,QAAQ,GAAG,OAAO;MAChCgH,OAAO,CAAC/H,KAAK,CAAC1T,QAAQ,GAAG,MAAM;MAE/Byb,OAAO,CAACC,SAAS,GAAG;AAC1B;AACA,YAAYhY,YAAY,CAACG,IAAI,SAASpD,OAAO;AAC7C;AACA;AACA,qBAAqBA,OAAO;AAC5B,eAAeoa,SAAS,GAAG,UAAU,GAAG,YAAY;AACpD;AACA;AACA,OAAO;MAED3F,QAAQ,CAACyG,IAAI,CAACnP,WAAW,CAACiP,OAAO,CAAC;;MAElC;MACA,MAAMG,WAAW,GAAGH,OAAO,CAACxF,aAAa,CAAC,QAAQ,CAAC;MACnD,IAAI2F,WAAW,EAAE;QACfA,WAAW,CAAC3K,gBAAgB,CAAC,OAAO,EAAE,MAAM;UAC1CiE,QAAQ,CAACyG,IAAI,CAACrK,WAAW,CAACmK,OAAO,CAAC;QACpC,CAAC,CAAC;MACJ;MAEArf,OAAO,CAACC,GAAG,CAAC,gBAAgBqH,YAAY,CAACG,IAAI,KAAKpD,OAAO,OAAO,CAAC;MACjE,OAAO,IAAI;IACb;EACF,CAAC,CAAC,OAAOnE,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA9B,MAAM,CAACqhB,iBAAiB,GAAG,MAAM;EAC/Bzf,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;EAErB,IAAI,CAAChB,gBAAgB,IAAIA,gBAAgB,CAACmY,IAAI,KAAK,CAAC,EAAE;IACpDpX,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,OAAO,EAAE;EACX;EAEA,MAAMyf,IAAI,GAAG,EAAE;EACfzgB,gBAAgB,CAACgK,OAAO,CAAC,CAACsV,KAAK,EAAE3S,EAAE,KAAK;IACtC5L,OAAO,CAACC,GAAG,CAAC,SAAS2L,EAAE,SAAS2S,KAAK,CAACjX,YAAY,CAACG,IAAI,EAAE,CAAC;IAC1DiY,IAAI,CAACtS,IAAI,CAAC;MACRxB,EAAE;MACFnE,IAAI,EAAE8W,KAAK,CAACjX,YAAY,CAACG,IAAI;MAC7B1E,QAAQ,EAAEwb,KAAK,CAACxb;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAO2c,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACAthB,MAAM,CAAC6P,qBAAqB,GAAI5J,OAAO,IAAK;EAC1C,IAAI;IACF;IACAA,OAAO,GAAGiJ,MAAM,CAACjJ,OAAO,CAAC;IAEzBrE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEoE,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;IAC/ErE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEhB,gBAAgB,CAACmY,IAAI,CAAC;IAC3DpX,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEf,kBAAkB,CAACkY,IAAI,CAAC;;IAE/D;IACA,IAAI,CAAC/S,OAAO,IAAIpF,gBAAgB,CAACmY,IAAI,GAAG,CAAC,EAAE;MACzC/S,OAAO,GAAGiJ,MAAM,CAAC7N,KAAK,CAACkgB,IAAI,CAAC1gB,gBAAgB,CAAC2gB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD5f,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoE,OAAO,CAAC;IAC3C;;IAEA;IACArE,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1BhB,gBAAgB,CAACgK,OAAO,CAAC,CAACsV,KAAK,EAAE3S,EAAE,KAAK;MAAA,IAAAiU,mBAAA;MACtC7f,OAAO,CAACC,GAAG,CAAC,KAAK2L,EAAE,KAAK,OAAOA,EAAE,MAAM,EAAAiU,mBAAA,GAAAtB,KAAK,CAACjX,YAAY,cAAAuY,mBAAA,uBAAlBA,mBAAA,CAAoBpY,IAAI,KAAI,IAAI,EAAE,CAAC;IAC5E,CAAC,CAAC;;IAEF;IACA,IAAIuF,YAAY,GAAG/N,gBAAgB,CAAC8L,GAAG,CAAC1G,OAAO,CAAC;IAChD,IAAI,CAAC2I,YAAY,EAAE;MACjB;MACA,MAAM8S,SAAS,GAAG5S,QAAQ,CAAC7I,OAAO,CAAC;MACnC2I,YAAY,GAAG/N,gBAAgB,CAAC8L,GAAG,CAAC+U,SAAS,CAAC;MAE9C,IAAI9S,YAAY,EAAE;QAChBhN,OAAO,CAACC,GAAG,CAAC,UAAU6f,SAAS,SAAS,CAAC;QACzCzb,OAAO,GAAGyb,SAAS,CAAC,CAAC;MACvB;IACF;IAEA,IAAI,CAAC9S,YAAY,EAAE;MACjBhN,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEmE,OAAO,CAAC;MACtC,OAAO,KAAK;IACd;;IAEA;IACA,IAAIjG,MAAM,CAACoG,mBAAmB,EAAE;MAC9BpG,MAAM,CAACoG,mBAAmB,CAACoB,OAAO,GAAGvB,OAAO;IAC9C;;IAEA;IACA,IAAIjG,MAAM,CAACqG,0BAA0B,IAAIrG,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,EAAE;MAClFoP,aAAa,CAAC5W,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,CAAC;MACxDxH,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,GAAG,IAAI;IAClD;;IAEA;IACA,MAAMma,yBAAyB,GAAGA,CAAA,KAAM;MAAA,IAAAC,qBAAA;MACtC;MACA,IAAI,CAAC5hB,MAAM,CAACsG,uBAAuB,EAAE;MAErC,MAAMub,SAAS,IAAAD,qBAAA,GAAG5hB,MAAM,CAACoG,mBAAmB,cAAAwb,qBAAA,uBAA1BA,qBAAA,CAA4Bpa,OAAO;MACrD,IAAI,CAACqa,SAAS,EAAE;;MAEhB;MACA,IAAIxB,SAAS,GAAGvf,kBAAkB,CAAC6L,GAAG,CAACkV,SAAS,CAAC;MACjD,IAAI,CAACxB,SAAS,IAAI,OAAOwB,SAAS,KAAK,QAAQ,EAAE;QAC/C;QACAxB,SAAS,GAAGvf,kBAAkB,CAAC6L,GAAG,CAACmC,QAAQ,CAAC+S,SAAS,CAAC,CAAC;MACzD,CAAC,MAAM,IAAI,CAACxB,SAAS,IAAI,OAAOwB,SAAS,KAAK,QAAQ,EAAE;QACtD;QACAxB,SAAS,GAAGvf,kBAAkB,CAAC6L,GAAG,CAACuC,MAAM,CAAC2S,SAAS,CAAC,CAAC;MACvD;MAEA,IAAI,CAACxB,SAAS,IAAI,CAACA,SAAS,CAACla,MAAM,IAAIka,SAAS,CAACla,MAAM,CAACkO,MAAM,KAAK,CAAC,EAAE;QACpEzS,OAAO,CAACC,GAAG,CAAC,QAAQggB,SAAS,cAAc,CAAC;QAC5C;MACF;;MAEA;MACA,MAAMC,iBAAiB,GAAGjhB,gBAAgB,CAAC8L,GAAG,CAACkV,SAAS,CAAC,KAC/B,OAAOA,SAAS,KAAK,QAAQ,GAAGhhB,gBAAgB,CAAC8L,GAAG,CAACmC,QAAQ,CAAC+S,SAAS,CAAC,CAAC,GACzEhhB,gBAAgB,CAAC8L,GAAG,CAACuC,MAAM,CAAC2S,SAAS,CAAC,CAAC,CAAC;MAElE,IAAI,CAACC,iBAAiB,EAAE;QACtBlgB,OAAO,CAACE,KAAK,CAAC,WAAW+f,SAAS,gBAAgB,CAAC;QACnD;MACF;MAEA,MAAM3Y,YAAY,GAAG4Y,iBAAiB,CAAC5Y,YAAY;;MAEnD;MACA,MAAMhD,OAAO,gBACXxH,OAAA;QAAKwa,KAAK,EAAE;UAAE/T,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAEoT,SAAS,EAAE,OAAO;UAAEyG,SAAS,EAAE;QAAO,CAAE;QAAAlM,QAAA,gBACpF1V,OAAA;UAAKwa,KAAK,EAAE;YACVrS,UAAU,EAAE,MAAM;YAClB0Z,YAAY,EAAE,KAAK;YACnB/a,QAAQ,EAAE,MAAM;YAChBgb,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAArM,QAAA,GACC,CAAAlL,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAACwY,SAAS,EAAC,GACjD;QAAA;UAAA1I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN5a,OAAA;UAAA0V,QAAA,EACGiM,SAAS,CAACla,MAAM,CAACgO,GAAG,CAAC,CAAC/F,KAAK,EAAE8Q,KAAK,KAAK;YACtC,IAAIwB,UAAU;YACd,IAAIqB,SAAS;YAEb,QAAQ3T,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBACN8R,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;cACF,KAAK,GAAG;gBACNrB,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;cACF,KAAK,GAAG;cACR;gBACErB,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;YACJ;YAEA,MAAMxT,SAAS,GAAGG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC;YAElD,oBACE3P,OAAA;cAAiBwa,KAAK,EAAE;gBACtBqH,YAAY,EAAE,KAAK;gBACnBnb,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE,KAAK;gBACnBE,QAAQ,EAAE;cACZ,CAAE;cAAA4O,QAAA,gBACA1V,OAAA;gBAAKwa,KAAK,EAAE;kBAAErS,UAAU,EAAE;gBAAO,CAAE;gBAAAuN,QAAA,GAChC7F,SAAS,EAAC,oBAAQ,EAACH,KAAK,CAACC,OAAO,EAAC,GACpC;cAAA;gBAAA8K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5a,OAAA;gBAAKwa,KAAK,EAAE;kBAAElU,OAAO,EAAE,MAAM;kBAAE2b,cAAc,EAAE;gBAAgB,CAAE;gBAAAvM,QAAA,gBAC/D1V,OAAA;kBAAA0V,QAAA,EAAM;gBAAI;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjB5a,OAAA;kBAAMwa,KAAK,EAAE;oBACXtS,KAAK,EAAE8Z,UAAU;oBACjB7Z,UAAU,EAAE,MAAM;oBAClBzB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAA8O,QAAA,EACC2N;gBAAS;kBAAA5I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACN5a,OAAA;gBAAKwa,KAAK,EAAE;kBAAElU,OAAO,EAAE,MAAM;kBAAE2b,cAAc,EAAE;gBAAgB,CAAE;gBAAAvM,QAAA,gBAC/D1V,OAAA;kBAAA0V,QAAA,EAAM;gBAAK;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClB5a,OAAA;kBAAMwa,KAAK,EAAE;oBAAErS,UAAU,EAAE;kBAAO,CAAE;kBAAAuN,QAAA,GAAEhG,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAAsK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAzBE4F,KAAK;cAAA/F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN5a,OAAA;UAAKwa,KAAK,EAAE;YAAE0H,SAAS,EAAE,KAAK;YAAEpb,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE;UAAO,CAAE;UAAAwN,QAAA,GAAC,4BAC3D,EAAC,IAAInH,IAAI,CAACoT,SAAS,CAAC1Q,UAAU,CAAC,CAACkR,kBAAkB,CAAC,CAAC,EAAC,6BAC7D;QAAA;UAAA1H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;;MAED;MACAtZ,MAAM,CAACsG,uBAAuB,CAAC+I,IAAI,KAAK;QACtC,GAAGA,IAAI;QACPnJ,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAEka,SAAS,CAACla;MACpB,CAAC,CAAC,CAAC;MAEHvE,OAAO,CAACC,GAAG,CAAC,SAAS,CAAAqH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAIwY,SAAS,WAAW,CAAC;IAClE,CAAC;;IAED;IACA,MAAMG,gCAAgC,GAAGA,CAAA,KAAM;MAC7C;MACA,IAAI3B,SAAS,GAAGvf,kBAAkB,CAAC6L,GAAG,CAAC1G,OAAO,CAAC;MAC/C,IAAI,CAACoa,SAAS,EAAE;QACd;QACA,MAAMqB,SAAS,GAAG5S,QAAQ,CAAC7I,OAAO,CAAC;QACnCoa,SAAS,GAAGvf,kBAAkB,CAAC6L,GAAG,CAAC+U,SAAS,CAAC;QAE7C,IAAIrB,SAAS,EAAE;UACbze,OAAO,CAACC,GAAG,CAAC,UAAU6f,SAAS,aAAa,CAAC;QAC/C;MACF;MAEA9f,OAAO,CAACC,GAAG,CAAC,QAAQoE,OAAO,SAAS,EAAEoa,SAAS,CAAC;MAEhD,MAAMnX,YAAY,GAAG0F,YAAY,CAAC1F,YAAY;MAC9CtH,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEqH,YAAY,CAAC;;MAElC;MACA,IAAIhD,OAAO;MAEX,IAAIma,SAAS,IAAIA,SAAS,CAACla,MAAM,IAAIka,SAAS,CAACla,MAAM,CAACkO,MAAM,GAAG,CAAC,EAAE;QAChE;QACAzS,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;QACtBwe,SAAS,CAACla,MAAM,CAAC0E,OAAO,CAAC,CAACuD,KAAK,EAAE8Q,KAAK,KAAK;UACzCtd,OAAO,CAACC,GAAG,CAAC,MAAMqd,KAAK,GAAC,CAAC,QAAQ9Q,KAAK,CAACC,OAAO,QAAQD,KAAK,CAACQ,YAAY,UAAUR,KAAK,CAACS,UAAU,EAAE,CAAC;QACvG,CAAC,CAAC;QAEF3I,OAAO,gBACLxH,OAAA;UAAKwa,KAAK,EAAE;YAAE/T,OAAO,EAAE,KAAK;YAAEsB,KAAK,EAAE,OAAO;YAAEoT,SAAS,EAAE,OAAO;YAAEyG,SAAS,EAAE;UAAO,CAAE;UAAAlM,QAAA,gBACpF1V,OAAA;YAAKwa,KAAK,EAAE;cACVrS,UAAU,EAAE,MAAM;cAClB0Z,YAAY,EAAE,KAAK;cACnB/a,QAAQ,EAAE,MAAM;cAChBgb,YAAY,EAAE,gBAAgB;cAC9BC,aAAa,EAAE;YACjB,CAAE;YAAArM,QAAA,GACC,CAAAlL,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAACpD,OAAO,EAAC,GAC/C;UAAA;YAAAkT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5a,OAAA;YAAA0V,QAAA,EACGiM,SAAS,CAACla,MAAM,CAACgO,GAAG,CAAC,CAAC/F,KAAK,EAAE8Q,KAAK,KAAK;cACtC,IAAIwB,UAAU;cACd,IAAIqB,SAAS;cAEb,QAAQ3T,KAAK,CAACQ,YAAY;gBACxB,KAAK,GAAG;kBACN8R,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;gBACF,KAAK,GAAG;kBACNrB,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;gBACF,KAAK,GAAG;gBACR;kBACErB,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;cACJ;cAEA,MAAMxT,SAAS,GAAGG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC;cAElD,oBACE3P,OAAA;gBAAiBwa,KAAK,EAAE;kBACtBqH,YAAY,EAAE,KAAK;kBACnBnb,eAAe,EAAE,uBAAuB;kBACxCD,OAAO,EAAE,KAAK;kBACdG,YAAY,EAAE,KAAK;kBACnBE,QAAQ,EAAE;gBACZ,CAAE;gBAAA4O,QAAA,gBACA1V,OAAA;kBAAKwa,KAAK,EAAE;oBAAErS,UAAU,EAAE;kBAAO,CAAE;kBAAAuN,QAAA,GAChC7F,SAAS,EAAC,oBAAQ,EAACH,KAAK,CAACC,OAAO,EAAC,GACpC;gBAAA;kBAAA8K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN5a,OAAA;kBAAKwa,KAAK,EAAE;oBAAElU,OAAO,EAAE,MAAM;oBAAE2b,cAAc,EAAE;kBAAgB,CAAE;kBAAAvM,QAAA,gBAC/D1V,OAAA;oBAAA0V,QAAA,EAAM;kBAAI;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjB5a,OAAA;oBAAMwa,KAAK,EAAE;sBACXtS,KAAK,EAAE8Z,UAAU;sBACjB7Z,UAAU,EAAE,MAAM;sBAClBzB,eAAe,EAAE,iBAAiB;sBAClCD,OAAO,EAAE,OAAO;sBAChBG,YAAY,EAAE;oBAChB,CAAE;oBAAA8O,QAAA,EACC2N;kBAAS;oBAAA5I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN5a,OAAA;kBAAKwa,KAAK,EAAE;oBAAElU,OAAO,EAAE,MAAM;oBAAE2b,cAAc,EAAE;kBAAgB,CAAE;kBAAAvM,QAAA,gBAC/D1V,OAAA;oBAAA0V,QAAA,EAAM;kBAAK;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClB5a,OAAA;oBAAMwa,KAAK,EAAE;sBAAErS,UAAU,EAAE;oBAAO,CAAE;oBAAAuN,QAAA,GAAEhG,KAAK,CAACS,UAAU,EAAC,SAAE;kBAAA;oBAAAsK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA,GAzBE4F,KAAK;gBAAA/F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BV,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5a,OAAA;YAAKwa,KAAK,EAAE;cAAE0H,SAAS,EAAE,KAAK;cAAEpb,QAAQ,EAAE,MAAM;cAAEoB,KAAK,EAAE;YAAO,CAAE;YAAAwN,QAAA,GAAC,4BAC3D,EAAC,IAAInH,IAAI,CAACoT,SAAS,CAAC1Q,UAAU,CAAC,CAACkR,kBAAkB,CAAC,CAAC,EAAC,6BAC7D;UAAA;YAAA1H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MACH,CAAC,MAAM;QACL;QACApT,OAAO,gBACLxH,OAAA;UAAKwa,KAAK,EAAE;YAAE/T,OAAO,EAAE,KAAK;YAAEsB,KAAK,EAAE;UAAQ,CAAE;UAAA2N,QAAA,gBAC7C1V,OAAA;YAAKwa,KAAK,EAAE;cACVrS,UAAU,EAAE,MAAM;cAClB0Z,YAAY,EAAE,KAAK;cACnB/a,QAAQ,EAAE,MAAM;cAChBgb,YAAY,EAAE,gBAAgB;cAC9BC,aAAa,EAAE;YACjB,CAAE;YAAArM,QAAA,GACC,CAAAlL,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAACpD,OAAO,EAAC,GAC/C;UAAA;YAAAkT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5a,OAAA;YAAKwa,KAAK,EAAE;cAAEtS,KAAK,EAAE,SAAS;cAAEpB,QAAQ,EAAE;YAAO,CAAE;YAAA4O,QAAA,EAAC;UAEpD;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5a,OAAA;YAAKwa,KAAK,EAAE;cAAE0H,SAAS,EAAE,KAAK;cAAEpb,QAAQ,EAAE,MAAM;cAAEoB,KAAK,EAAE;YAAO,CAAE;YAAAwN,QAAA,GAAC,4BAC3D,EAAC,IAAInH,IAAI,CAAC,CAAC,CAAC4T,kBAAkB,CAAC,CAAC;UAAA;YAAA1H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MACH;;MAEA;MACA,MAAM/W,CAAC,GAAGvC,MAAM,CAACuR,UAAU,GAAG,CAAC;MAC/B,MAAM9O,CAAC,GAAGzC,MAAM,CAACwR,WAAW,GAAG,CAAC;;MAEhC;MACA,IAAIxR,MAAM,CAACsG,uBAAuB,EAAE;QAAA,IAAA2b,UAAA;QAClCrgB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7CmE,OAAO,EAAE,IAAI;UACbC,OAAO;UACPtB,QAAQ,EAAE;YAAEpC,CAAC;YAAEE;UAAE;QACnB,CAAC,CAAC;QAEFzC,MAAM,CAACsG,uBAAuB,CAAC;UAC7BN,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEA,OAAO;UAChBtB,QAAQ,EAAE;YAAEpC,CAAC;YAAEE;UAAE,CAAC;UAClByD,OAAO,EAAEA,OAAO;UAChBC,MAAM,EAAE,EAAA8b,UAAA,GAAA5B,SAAS,cAAA4B,UAAA,uBAATA,UAAA,CAAW9b,MAAM,KAAI;QAC/B,CAAC,CAAC;QAEFvE,OAAO,CAACC,GAAG,CAAC,SAAS,CAAAqH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAIpD,OAAO,WAAW,CAAC;;QAE9D;QACA,IAAIjG,MAAM,CAACqG,0BAA0B,EAAE;UACrCrG,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,GAAG4P,WAAW,CAACuK,yBAAyB,EAAExe,6BAA6B,GAAG,IAAI,CAAC;UACxHvB,OAAO,CAACC,GAAG,CAAC,qBAAqBsB,6BAA6B,IAAI,CAAC;QACrE;QAEA,OAAO,IAAI;MACb,CAAC,MAAM;QACLvB,OAAO,CAACE,KAAK,CAAC,8BAA8B,CAAC;QAC7C,OAAO,KAAK;MACd;IACF,CAAC;IAED,OAAOkgB,gCAAgC,CAAC,CAAC;EAC3C,CAAC,CAAC,OAAOlgB,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,MAAMud,yBAAyB,GAAIF,MAAM,IAAK;EAC5C,IAAI3X,OAAO,GAAG2X,MAAM;;EAEpB;EACA,IAAI3X,OAAO,IAAIA,OAAO,CAACsR,QAAQ,IAAItR,OAAO,CAACsR,QAAQ,CAACtO,IAAI,KAAK,cAAc,EAAE;IAC3E5I,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE2F,OAAO,CAAC6B,IAAI,IAAI,KAAK,CAAC;IAChD,OAAO7B,OAAO;EAChB;;EAEA;EACA,OAAOA,OAAO,IAAIA,OAAO,CAACiV,MAAM,EAAE;IAChCjV,OAAO,GAAGA,OAAO,CAACiV,MAAM;IACxB,IAAIjV,OAAO,CAACsR,QAAQ,IAAItR,OAAO,CAACsR,QAAQ,CAACtO,IAAI,KAAK,cAAc,EAAE;MAChE5I,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE2F,OAAO,CAAC6B,IAAI,IAAI,KAAK,CAAC;MAChD,OAAO7B,OAAO;IAChB;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACAxH,MAAM,CAACkiB,kBAAkB,GAAG,CAAC3f,CAAC,EAAEE,CAAC,KAAK;EACpC,IAAI;IACFb,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEU,CAAC,EAAEE,CAAC,CAAC;;IAEnC;IACA,MAAMgY,MAAM,GAAGC,QAAQ,CAACe,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAI,CAAChB,MAAM,EAAE;MACX7Y,OAAO,CAACE,KAAK,CAAC,sBAAsB,CAAC;MACrC,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAACpC,KAAK,IAAI,CAACiG,SAAS,CAAC6B,OAAO,EAAE;MAChC5F,OAAO,CAACE,KAAK,CAAC,iBAAiB,CAAC;MAChC,OAAO,KAAK;IACd;;IAEA;IACA,IAAIS,CAAC,KAAKuX,SAAS,IAAIrX,CAAC,KAAKqX,SAAS,EAAE;MACtCvX,CAAC,GAAGvC,MAAM,CAACuR,UAAU,GAAG,CAAC;MACzB9O,CAAC,GAAGzC,MAAM,CAACwR,WAAW,GAAG,CAAC;IAC5B;;IAEA;IACA,MAAMuM,IAAI,GAAGtD,MAAM,CAACuD,qBAAqB,CAAC,CAAC;IAC3C,MAAMC,MAAM,GAAI,CAAC1b,CAAC,GAAGwb,IAAI,CAAClZ,IAAI,IAAI4V,MAAM,CAACyD,WAAW,GAAI,CAAC,GAAG,CAAC;IAC7D,MAAMC,MAAM,GAAG,EAAE,CAAC1b,CAAC,GAAGsb,IAAI,CAACvX,GAAG,IAAIiU,MAAM,CAAC2D,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;IAE9Dxc,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEoc,MAAM,EAAEE,MAAM,CAAC;;IAErC;IACA,MAAME,SAAS,GAAG,IAAIrgB,KAAK,CAACsgB,SAAS,CAAC,CAAC;IACvCD,SAAS,CAACE,MAAM,CAACC,MAAM,CAACC,SAAS,GAAG,CAAC;IACrCJ,SAAS,CAACE,MAAM,CAACG,IAAI,CAACD,SAAS,GAAG,CAAC;IAEnC,MAAME,WAAW,GAAG,IAAI3gB,KAAK,CAAC4gB,OAAO,CAACX,MAAM,EAAEE,MAAM,CAAC;IACrDE,SAAS,CAACQ,aAAa,CAACF,WAAW,EAAEhZ,SAAS,CAAC6B,OAAO,CAAC;;IAEvD;IACA,MAAMsX,mBAAmB,GAAG,EAAE;IAC9Bje,gBAAgB,CAACgK,OAAO,CAAC,CAACoM,QAAQ,EAAEhR,OAAO,KAAK;MAC9C,IAAIgR,QAAQ,CAAClK,KAAK,EAAE;QAClB+R,mBAAmB,CAAC9P,IAAI,CAACiI,QAAQ,CAAClK,KAAK,CAAC;QACxCnL,OAAO,CAACC,GAAG,CAAC,SAASoE,OAAO,QAAQ,CAAC;MACvC;IACF,CAAC,CAAC;;IAEF;IACArE,OAAO,CAACC,GAAG,CAAC,QAAQid,mBAAmB,CAACzK,MAAM,YAAY,CAAC;IAC3D,MAAM8N,YAAY,GAAG9D,SAAS,CAACW,gBAAgB,CAACF,mBAAmB,EAAE,IAAI,CAAC;IAE1E,IAAIqD,YAAY,CAAC9N,MAAM,GAAG,CAAC,EAAE;MAC3BzS,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1BsgB,YAAY,CAACtX,OAAO,CAAC,CAACoU,SAAS,EAAE7V,CAAC,KAAK;QACrCxH,OAAO,CAACC,GAAG,CAAC,MAAMuH,CAAC,GAAG,EAAE6V,SAAS,CAACE,MAAM,CAAC9V,IAAI,IAAI,KAAK,EAC1C,KAAK,EAAE4V,SAAS,CAACrM,QAAQ,EACzB,WAAW,EAAEqM,SAAS,CAACE,MAAM,CAACxa,QAAQ,CAACoF,OAAO,CAAC,CAAC,EAChD,WAAW,EAAEkV,SAAS,CAACE,MAAM,CAACrG,QAAQ,CAAC;;QAEnD;QACA,MAAMsG,GAAG,GAAGC,yBAAyB,CAACJ,SAAS,CAACE,MAAM,CAAC;QACvD,IAAIC,GAAG,IAAIA,GAAG,CAACtG,QAAQ,IAAIsG,GAAG,CAACtG,QAAQ,CAACtO,IAAI,KAAK,cAAc,EAAE;UAC/D5I,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEud,GAAG,CAACtG,QAAQ,CAAC7S,OAAO,CAAC;QAC/C;MACF,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;;IAEA;IACArE,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,MAAMugB,eAAe,GAAG/D,SAAS,CAACW,gBAAgB,CAACtf,KAAK,CAAC0U,QAAQ,EAAE,IAAI,CAAC;IAExExS,OAAO,CAACC,GAAG,CAAC,WAAWugB,eAAe,CAAC/N,MAAM,MAAM,CAAC;IACpD+N,eAAe,CAACvX,OAAO,CAAC,CAACoU,SAAS,EAAE7V,CAAC,KAAK;MACxC,MAAMgW,GAAG,GAAGH,SAAS,CAACE,MAAM;MAC5Bvd,OAAO,CAACC,GAAG,CAAC,QAAQuH,CAAC,GAAG,EAAEgW,GAAG,CAAC/V,IAAI,IAAI,KAAK,EAC/B,KAAK,EAAE+V,GAAG,CAAC5U,IAAI,EACf,KAAK,EAAE4U,GAAG,CAACza,QAAQ,CAACoF,OAAO,CAAC,CAAC,EAC7B,KAAK,EAAEkV,SAAS,CAACrM,QAAQ,EACzB,WAAW,EAAEwM,GAAG,CAACtG,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACAlX,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,IAAIwgB,YAAY,GAAG,CAAC;IAEpBxhB,gBAAgB,CAACgK,OAAO,CAAC,CAACoM,QAAQ,EAAEhR,OAAO,KAAK;MAC9C,IAAIgR,QAAQ,CAAClK,KAAK,EAAE;QAAA,IAAAuV,qBAAA;QAClB;QACA,IAAIC,SAAS,GAAGtL,QAAQ,CAAClK,KAAK,CAAC/G,OAAO;QACtC,IAAIwc,cAAc,GAAG,IAAI;;QAEzB;QACA,MAAM/C,QAAQ,GAAG,IAAIzhB,KAAK,CAACyO,OAAO,CAAC,CAAC;QACpCwK,QAAQ,CAAClK,KAAK,CAAC2S,gBAAgB,CAACD,QAAQ,CAAC;;QAEzC;QACA,MAAMgD,gBAAgB,GAAGhD,QAAQ,CAACiD,UAAU,CAAC/c,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAC;;QAExE;QACA,MAAMgb,SAAS,GAAGF,QAAQ,CAACpd,KAAK,CAAC,CAAC,CAACud,OAAO,CAACja,SAAS,CAAC6B,OAAO,CAAC;QAC7D,IAAIxE,IAAI,CAACqT,GAAG,CAACsJ,SAAS,CAACpd,CAAC,CAAC,GAAG,CAAC,IAAIS,IAAI,CAACqT,GAAG,CAACsJ,SAAS,CAACld,CAAC,CAAC,GAAG,CAAC,IAAIkd,SAAS,CAAChd,CAAC,GAAG,CAAC,CAAC,IAAIgd,SAAS,CAAChd,CAAC,GAAG,CAAC,EAAE;UACjG6f,cAAc,GAAG,KAAK;QACxB;QAEA,IAAID,SAAS,EAAE;UACbF,YAAY,EAAE;QAChB;QAEAzgB,OAAO,CAACC,GAAG,CAAC,OAAOoE,OAAO,GAAG,EAAE;UAC7B0c,EAAE,EAAE,EAAAL,qBAAA,GAAArL,QAAQ,CAAC/N,YAAY,cAAAoZ,qBAAA,uBAArBA,qBAAA,CAAuBjZ,IAAI,KAAI,IAAI;UACvCuZ,GAAG,EAAEL,SAAS;UACdM,KAAK,EAAEL,cAAc;UACrBM,IAAI,EAAErD,QAAQ,CAAC1V,OAAO,CAAC,CAAC;UACxBgZ,IAAI,EAAE,CAACpD,SAAS,CAACpd,CAAC,EAAEod,SAAS,CAACld,CAAC,EAAEkd,SAAS,CAAChd,CAAC,CAAC;UAC7CqgB,MAAM,EAAEP;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF7gB,OAAO,CAACC,GAAG,CAAC,MAAMwgB,YAAY,IAAIxhB,gBAAgB,CAACmY,IAAI,WAAW,CAAC;;IAEnE;IACA,OAAOoJ,eAAe,CAAC/N,MAAM,GAAG,CAAC;EACnC,CAAC,CAAC,OAAOvS,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,OAAO,KAAK;EACd;AACF,CAAC;;AAID;AACA,MAAMsN,wBAAwB,GAAGA,CAACR,YAAY,EAAEG,SAAS,KAAK;EAAA,IAAAkU,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC5D,IAAI,CAACvU,YAAY,IAAI,CAACA,YAAY,CAAC7B,KAAK,IAAI,CAACgC,SAAS,EAAE;IACtD;EACF;;EAEA;EACA,MAAMqU,cAAc,GAAG,EAAE;EACzBxU,YAAY,CAAC7B,KAAK,CAAC2G,QAAQ,CAACC,KAAK,IAAI;IACnC,IAAIA,KAAK,CAACmF,QAAQ,IAAInF,KAAK,CAACmF,QAAQ,CAACuK,OAAO,EAAE;MAC5CD,cAAc,CAACpU,IAAI,CAAC2E,KAAK,CAAC;IAC5B;EACF,CAAC,CAAC;EAEFyP,cAAc,CAACvY,OAAO,CAACsV,KAAK,IAAI;IAC9BvR,YAAY,CAAC7B,KAAK,CAACU,MAAM,CAAC0S,KAAK,CAAC;EAClC,CAAC,CAAC;;EAEF;EACA,IAAIO,UAAU;EACd,QAAO3R,SAAS,CAACH,YAAY;IAC3B,KAAK,GAAG;MACN8R,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;MACNA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;IACR;MACEA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;EACJ;;EAEA;EACA,MAAMnD,aAAa,GAAG,IAAIvf,KAAK,CAACwa,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAMgF,aAAa,GAAG,IAAIxf,KAAK,CAACga,iBAAiB,CAAC;IAChDpR,KAAK,EAAE8Z,UAAU;IACjB4C,QAAQ,EAAE5C,UAAU;IACpB6C,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM9F,SAAS,GAAG,IAAIzf,KAAK,CAACia,IAAI,CAACsF,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAAC9Y,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAClC6a,SAAS,CAAC3E,QAAQ,GAAG;IACnBuK,OAAO,EAAE,IAAI;IACb7Y,IAAI,EAAE,cAAc;IACpBvE,OAAO,GAAAgd,qBAAA,GAAErU,YAAY,CAAC1F,YAAY,cAAA+Z,qBAAA,uBAAzBA,qBAAA,CAA2Bhd,OAAO;IAC3CoI,OAAO,EAAEU,SAAS,CAACV,OAAO;IAC1BE,SAAS,EAAEQ,SAAS,CAACR,SAAS;IAC9BM,UAAU,EAAEE,SAAS,CAACF;EACxB,CAAC;;EAED;EACA,MAAMsR,KAAK,GAAG,IAAIniB,KAAK,CAACwlB,UAAU,CAAC9C,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;EACrDP,KAAK,CAACxb,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAC5Bud,KAAK,CAACrH,QAAQ,GAAG;IAAEuK,OAAO,EAAE;EAAK,CAAC;;EAElC;EACAzU,YAAY,CAAC7B,KAAK,CAACD,GAAG,CAAC2Q,SAAS,CAAC;EACjC7O,YAAY,CAAC7B,KAAK,CAACD,GAAG,CAACqT,KAAK,CAAC;EAE7Bve,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAAqhB,sBAAA,GAAAtU,YAAY,CAAC1F,YAAY,cAAAga,sBAAA,uBAAzBA,sBAAA,CAA2B7Z,IAAI,OAAA8Z,sBAAA,GAAIvU,YAAY,CAAC1F,YAAY,cAAAia,sBAAA,uBAAzBA,sBAAA,CAA2Bld,OAAO,cAAa8I,SAAS,CAACH,YAAY,WAAWG,SAAS,CAACF,UAAU,GAAG,CAAC;AACjK,CAAC;AAED,eAAezL,WAAW;AAAC,IAAAkX,EAAA;AAAAmJ,YAAA,CAAAnJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}