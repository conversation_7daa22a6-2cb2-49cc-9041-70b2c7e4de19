{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport AxisView from './AxisView.js';\nimport * as cartesianAxisHelper from '../../coord/cartesian/cartesianAxisHelper.js';\nimport { rectCoordAxisBuildSplitArea, rectCoordAxisHandleRemove } from './axisSplitHelper.js';\nimport { isIntervalOrLogScale } from '../../scale/helper.js';\nvar axisBuilderAttrs = ['axisLine', 'axisTickLabel', 'axisName'];\nvar selfBuilderAttrs = ['splitArea', 'splitLine', 'minorSplitLine'];\nvar CartesianAxisView = /** @class */function (_super) {\n  __extends(CartesianAxisView, _super);\n  function CartesianAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CartesianAxisView.type;\n    _this.axisPointerClass = 'CartesianAxisPointer';\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  CartesianAxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    this.group.removeAll();\n    var oldAxisGroup = this._axisGroup;\n    this._axisGroup = new graphic.Group();\n    this.group.add(this._axisGroup);\n    if (!axisModel.get('show')) {\n      return;\n    }\n    var gridModel = axisModel.getCoordSysModel();\n    var layout = cartesianAxisHelper.layout(gridModel, axisModel);\n    var axisBuilder = new AxisBuilder(axisModel, zrUtil.extend({\n      handleAutoShown: function (elementType) {\n        var cartesians = gridModel.coordinateSystem.getCartesians();\n        for (var i = 0; i < cartesians.length; i++) {\n          if (isIntervalOrLogScale(cartesians[i].getOtherAxis(axisModel.axis).scale)) {\n            // Still show axis tick or axisLine if other axis is value / log\n            return true;\n          }\n        }\n        // Not show axisTick or axisLine if other axis is category / time\n        return false;\n      }\n    }, layout));\n    zrUtil.each(axisBuilderAttrs, axisBuilder.add, axisBuilder);\n    this._axisGroup.add(axisBuilder.getGroup());\n    zrUtil.each(selfBuilderAttrs, function (name) {\n      if (axisModel.get([name, 'show'])) {\n        axisElementBuilders[name](this, this._axisGroup, axisModel, gridModel);\n      }\n    }, this);\n    // THIS is a special case for bar racing chart.\n    // Update the axis label from the natural initial layout to\n    // sorted layout should has no animation.\n    var isInitialSortFromBarRacing = payload && payload.type === 'changeAxisOrder' && payload.isInitSort;\n    if (!isInitialSortFromBarRacing) {\n      graphic.groupTransition(oldAxisGroup, this._axisGroup, axisModel);\n    }\n    _super.prototype.render.call(this, axisModel, ecModel, api, payload);\n  };\n  CartesianAxisView.prototype.remove = function () {\n    rectCoordAxisHandleRemove(this);\n  };\n  CartesianAxisView.type = 'cartesianAxis';\n  return CartesianAxisView;\n}(AxisView);\nvar axisElementBuilders = {\n  splitLine: function (axisView, axisGroup, axisModel, gridModel) {\n    var axis = axisModel.axis;\n    if (axis.scale.isBlank()) {\n      return;\n    }\n    var splitLineModel = axisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    var showMinLine = splitLineModel.get('showMinLine') !== false;\n    var showMaxLine = splitLineModel.get('showMaxLine') !== false;\n    lineColors = zrUtil.isArray(lineColors) ? lineColors : [lineColors];\n    var gridRect = gridModel.coordinateSystem.getRect();\n    var isHorizontal = axis.isHorizontal();\n    var lineCount = 0;\n    var ticksCoords = axis.getTicksCoords({\n      tickModel: splitLineModel\n    });\n    var p1 = [];\n    var p2 = [];\n    var lineStyle = lineStyleModel.getLineStyle();\n    for (var i = 0; i < ticksCoords.length; i++) {\n      var tickCoord = axis.toGlobalCoord(ticksCoords[i].coord);\n      if (i === 0 && !showMinLine || i === ticksCoords.length - 1 && !showMaxLine) {\n        continue;\n      }\n      var tickValue = ticksCoords[i].tickValue;\n      if (isHorizontal) {\n        p1[0] = tickCoord;\n        p1[1] = gridRect.y;\n        p2[0] = tickCoord;\n        p2[1] = gridRect.y + gridRect.height;\n      } else {\n        p1[0] = gridRect.x;\n        p1[1] = tickCoord;\n        p2[0] = gridRect.x + gridRect.width;\n        p2[1] = tickCoord;\n      }\n      var colorIndex = lineCount++ % lineColors.length;\n      var line = new graphic.Line({\n        anid: tickValue != null ? 'line_' + tickValue : null,\n        autoBatch: true,\n        shape: {\n          x1: p1[0],\n          y1: p1[1],\n          x2: p2[0],\n          y2: p2[1]\n        },\n        style: zrUtil.defaults({\n          stroke: lineColors[colorIndex]\n        }, lineStyle),\n        silent: true\n      });\n      graphic.subPixelOptimizeLine(line.shape, lineStyle.lineWidth);\n      axisGroup.add(line);\n    }\n  },\n  minorSplitLine: function (axisView, axisGroup, axisModel, gridModel) {\n    var axis = axisModel.axis;\n    var minorSplitLineModel = axisModel.getModel('minorSplitLine');\n    var lineStyleModel = minorSplitLineModel.getModel('lineStyle');\n    var gridRect = gridModel.coordinateSystem.getRect();\n    var isHorizontal = axis.isHorizontal();\n    var minorTicksCoords = axis.getMinorTicksCoords();\n    if (!minorTicksCoords.length) {\n      return;\n    }\n    var p1 = [];\n    var p2 = [];\n    var lineStyle = lineStyleModel.getLineStyle();\n    for (var i = 0; i < minorTicksCoords.length; i++) {\n      for (var k = 0; k < minorTicksCoords[i].length; k++) {\n        var tickCoord = axis.toGlobalCoord(minorTicksCoords[i][k].coord);\n        if (isHorizontal) {\n          p1[0] = tickCoord;\n          p1[1] = gridRect.y;\n          p2[0] = tickCoord;\n          p2[1] = gridRect.y + gridRect.height;\n        } else {\n          p1[0] = gridRect.x;\n          p1[1] = tickCoord;\n          p2[0] = gridRect.x + gridRect.width;\n          p2[1] = tickCoord;\n        }\n        var line = new graphic.Line({\n          anid: 'minor_line_' + minorTicksCoords[i][k].tickValue,\n          autoBatch: true,\n          shape: {\n            x1: p1[0],\n            y1: p1[1],\n            x2: p2[0],\n            y2: p2[1]\n          },\n          style: lineStyle,\n          silent: true\n        });\n        graphic.subPixelOptimizeLine(line.shape, lineStyle.lineWidth);\n        axisGroup.add(line);\n      }\n    }\n  },\n  splitArea: function (axisView, axisGroup, axisModel, gridModel) {\n    rectCoordAxisBuildSplitArea(axisView, axisGroup, axisModel, gridModel);\n  }\n};\nvar CartesianXAxisView = /** @class */function (_super) {\n  __extends(CartesianXAxisView, _super);\n  function CartesianXAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CartesianXAxisView.type;\n    return _this;\n  }\n  CartesianXAxisView.type = 'xAxis';\n  return CartesianXAxisView;\n}(CartesianAxisView);\nexport { CartesianXAxisView };\nvar CartesianYAxisView = /** @class */function (_super) {\n  __extends(CartesianYAxisView, _super);\n  function CartesianYAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CartesianXAxisView.type;\n    return _this;\n  }\n  CartesianYAxisView.type = 'yAxis';\n  return CartesianYAxisView;\n}(CartesianAxisView);\nexport { CartesianYAxisView };\nexport default CartesianAxisView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "graphic", "AxisBuilder", "AxisView", "cartesianAxisHelper", "rectCoordAxisBuildSplitArea", "rectCoordAxisHandleRemove", "isIntervalOrLogScale", "axisBuilderAttrs", "selfBuilderAttrs", "CartesianAxis<PERSON>iew", "_super", "_this", "apply", "arguments", "type", "axisPointerClass", "prototype", "render", "axisModel", "ecModel", "api", "payload", "group", "removeAll", "oldAxisGroup", "_axisGroup", "Group", "add", "get", "gridModel", "getCoordSysModel", "layout", "axisBuilder", "extend", "handleAutoShown", "elementType", "cartesians", "coordinateSystem", "getCartesians", "i", "length", "getOtherAxis", "axis", "scale", "each", "getGroup", "name", "axisElementBuilders", "isInitialSortFromBarRacing", "isInitSort", "groupTransition", "call", "remove", "splitLine", "axisView", "axisGroup", "isBlank", "splitLineModel", "getModel", "lineStyleModel", "lineColors", "showMinLine", "showMaxLine", "isArray", "gridRect", "getRect", "isHorizontal", "lineCount", "ticksCoords", "getTicksCoords", "tickModel", "p1", "p2", "lineStyle", "getLineStyle", "tickCoord", "toGlobalCoord", "coord", "tickValue", "y", "height", "x", "width", "colorIndex", "line", "Line", "anid", "autoBatch", "shape", "x1", "y1", "x2", "y2", "style", "defaults", "stroke", "silent", "subPixelOptimizeLine", "lineWidth", "minorSplitLine", "minorSplitLineModel", "minorTicksCoords", "getMinorTicksCoords", "k", "splitArea", "CartesianXAxisView", "CartesianYAxisView"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/echarts/lib/component/axis/CartesianAxisView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport AxisView from './AxisView.js';\nimport * as cartesianAxisHelper from '../../coord/cartesian/cartesianAxisHelper.js';\nimport { rectCoordAxisBuildSplitArea, rectCoordAxisHandleRemove } from './axisSplitHelper.js';\nimport { isIntervalOrLogScale } from '../../scale/helper.js';\nvar axisBuilderAttrs = ['axisLine', 'axisTickLabel', 'axisName'];\nvar selfBuilderAttrs = ['splitArea', 'splitLine', 'minorSplitLine'];\nvar CartesianAxisView = /** @class */function (_super) {\n  __extends(CartesianAxisView, _super);\n  function CartesianAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CartesianAxisView.type;\n    _this.axisPointerClass = 'CartesianAxisPointer';\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  CartesianAxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    this.group.removeAll();\n    var oldAxisGroup = this._axisGroup;\n    this._axisGroup = new graphic.Group();\n    this.group.add(this._axisGroup);\n    if (!axisModel.get('show')) {\n      return;\n    }\n    var gridModel = axisModel.getCoordSysModel();\n    var layout = cartesianAxisHelper.layout(gridModel, axisModel);\n    var axisBuilder = new AxisBuilder(axisModel, zrUtil.extend({\n      handleAutoShown: function (elementType) {\n        var cartesians = gridModel.coordinateSystem.getCartesians();\n        for (var i = 0; i < cartesians.length; i++) {\n          if (isIntervalOrLogScale(cartesians[i].getOtherAxis(axisModel.axis).scale)) {\n            // Still show axis tick or axisLine if other axis is value / log\n            return true;\n          }\n        }\n        // Not show axisTick or axisLine if other axis is category / time\n        return false;\n      }\n    }, layout));\n    zrUtil.each(axisBuilderAttrs, axisBuilder.add, axisBuilder);\n    this._axisGroup.add(axisBuilder.getGroup());\n    zrUtil.each(selfBuilderAttrs, function (name) {\n      if (axisModel.get([name, 'show'])) {\n        axisElementBuilders[name](this, this._axisGroup, axisModel, gridModel);\n      }\n    }, this);\n    // THIS is a special case for bar racing chart.\n    // Update the axis label from the natural initial layout to\n    // sorted layout should has no animation.\n    var isInitialSortFromBarRacing = payload && payload.type === 'changeAxisOrder' && payload.isInitSort;\n    if (!isInitialSortFromBarRacing) {\n      graphic.groupTransition(oldAxisGroup, this._axisGroup, axisModel);\n    }\n    _super.prototype.render.call(this, axisModel, ecModel, api, payload);\n  };\n  CartesianAxisView.prototype.remove = function () {\n    rectCoordAxisHandleRemove(this);\n  };\n  CartesianAxisView.type = 'cartesianAxis';\n  return CartesianAxisView;\n}(AxisView);\nvar axisElementBuilders = {\n  splitLine: function (axisView, axisGroup, axisModel, gridModel) {\n    var axis = axisModel.axis;\n    if (axis.scale.isBlank()) {\n      return;\n    }\n    var splitLineModel = axisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    var showMinLine = splitLineModel.get('showMinLine') !== false;\n    var showMaxLine = splitLineModel.get('showMaxLine') !== false;\n    lineColors = zrUtil.isArray(lineColors) ? lineColors : [lineColors];\n    var gridRect = gridModel.coordinateSystem.getRect();\n    var isHorizontal = axis.isHorizontal();\n    var lineCount = 0;\n    var ticksCoords = axis.getTicksCoords({\n      tickModel: splitLineModel\n    });\n    var p1 = [];\n    var p2 = [];\n    var lineStyle = lineStyleModel.getLineStyle();\n    for (var i = 0; i < ticksCoords.length; i++) {\n      var tickCoord = axis.toGlobalCoord(ticksCoords[i].coord);\n      if (i === 0 && !showMinLine || i === ticksCoords.length - 1 && !showMaxLine) {\n        continue;\n      }\n      var tickValue = ticksCoords[i].tickValue;\n      if (isHorizontal) {\n        p1[0] = tickCoord;\n        p1[1] = gridRect.y;\n        p2[0] = tickCoord;\n        p2[1] = gridRect.y + gridRect.height;\n      } else {\n        p1[0] = gridRect.x;\n        p1[1] = tickCoord;\n        p2[0] = gridRect.x + gridRect.width;\n        p2[1] = tickCoord;\n      }\n      var colorIndex = lineCount++ % lineColors.length;\n      var line = new graphic.Line({\n        anid: tickValue != null ? 'line_' + tickValue : null,\n        autoBatch: true,\n        shape: {\n          x1: p1[0],\n          y1: p1[1],\n          x2: p2[0],\n          y2: p2[1]\n        },\n        style: zrUtil.defaults({\n          stroke: lineColors[colorIndex]\n        }, lineStyle),\n        silent: true\n      });\n      graphic.subPixelOptimizeLine(line.shape, lineStyle.lineWidth);\n      axisGroup.add(line);\n    }\n  },\n  minorSplitLine: function (axisView, axisGroup, axisModel, gridModel) {\n    var axis = axisModel.axis;\n    var minorSplitLineModel = axisModel.getModel('minorSplitLine');\n    var lineStyleModel = minorSplitLineModel.getModel('lineStyle');\n    var gridRect = gridModel.coordinateSystem.getRect();\n    var isHorizontal = axis.isHorizontal();\n    var minorTicksCoords = axis.getMinorTicksCoords();\n    if (!minorTicksCoords.length) {\n      return;\n    }\n    var p1 = [];\n    var p2 = [];\n    var lineStyle = lineStyleModel.getLineStyle();\n    for (var i = 0; i < minorTicksCoords.length; i++) {\n      for (var k = 0; k < minorTicksCoords[i].length; k++) {\n        var tickCoord = axis.toGlobalCoord(minorTicksCoords[i][k].coord);\n        if (isHorizontal) {\n          p1[0] = tickCoord;\n          p1[1] = gridRect.y;\n          p2[0] = tickCoord;\n          p2[1] = gridRect.y + gridRect.height;\n        } else {\n          p1[0] = gridRect.x;\n          p1[1] = tickCoord;\n          p2[0] = gridRect.x + gridRect.width;\n          p2[1] = tickCoord;\n        }\n        var line = new graphic.Line({\n          anid: 'minor_line_' + minorTicksCoords[i][k].tickValue,\n          autoBatch: true,\n          shape: {\n            x1: p1[0],\n            y1: p1[1],\n            x2: p2[0],\n            y2: p2[1]\n          },\n          style: lineStyle,\n          silent: true\n        });\n        graphic.subPixelOptimizeLine(line.shape, lineStyle.lineWidth);\n        axisGroup.add(line);\n      }\n    }\n  },\n  splitArea: function (axisView, axisGroup, axisModel, gridModel) {\n    rectCoordAxisBuildSplitArea(axisView, axisGroup, axisModel, gridModel);\n  }\n};\nvar CartesianXAxisView = /** @class */function (_super) {\n  __extends(CartesianXAxisView, _super);\n  function CartesianXAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CartesianXAxisView.type;\n    return _this;\n  }\n  CartesianXAxisView.type = 'xAxis';\n  return CartesianXAxisView;\n}(CartesianAxisView);\nexport { CartesianXAxisView };\nvar CartesianYAxisView = /** @class */function (_super) {\n  __extends(CartesianYAxisView, _super);\n  function CartesianYAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CartesianXAxisView.type;\n    return _this;\n  }\n  CartesianYAxisView.type = 'yAxis';\n  return CartesianYAxisView;\n}(CartesianAxisView);\nexport { CartesianYAxisView };\nexport default CartesianAxisView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAO,KAAKC,mBAAmB,MAAM,8CAA8C;AACnF,SAASC,2BAA2B,EAAEC,yBAAyB,QAAQ,sBAAsB;AAC7F,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,IAAIC,gBAAgB,GAAG,CAAC,UAAU,EAAE,eAAe,EAAE,UAAU,CAAC;AAChE,IAAIC,gBAAgB,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,gBAAgB,CAAC;AACnE,IAAIC,iBAAiB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACrDZ,SAAS,CAACW,iBAAiB,EAAEC,MAAM,CAAC;EACpC,SAASD,iBAAiBA,CAAA,EAAG;IAC3B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,iBAAiB,CAACK,IAAI;IACnCH,KAAK,CAACI,gBAAgB,GAAG,sBAAsB;IAC/C,OAAOJ,KAAK;EACd;EACA;AACF;AACA;EACEF,iBAAiB,CAACO,SAAS,CAACC,MAAM,GAAG,UAAUC,SAAS,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,EAAE;IAC/E,IAAI,CAACC,KAAK,CAACC,SAAS,CAAC,CAAC;IACtB,IAAIC,YAAY,GAAG,IAAI,CAACC,UAAU;IAClC,IAAI,CAACA,UAAU,GAAG,IAAIzB,OAAO,CAAC0B,KAAK,CAAC,CAAC;IACrC,IAAI,CAACJ,KAAK,CAACK,GAAG,CAAC,IAAI,CAACF,UAAU,CAAC;IAC/B,IAAI,CAACP,SAAS,CAACU,GAAG,CAAC,MAAM,CAAC,EAAE;MAC1B;IACF;IACA,IAAIC,SAAS,GAAGX,SAAS,CAACY,gBAAgB,CAAC,CAAC;IAC5C,IAAIC,MAAM,GAAG5B,mBAAmB,CAAC4B,MAAM,CAACF,SAAS,EAAEX,SAAS,CAAC;IAC7D,IAAIc,WAAW,GAAG,IAAI/B,WAAW,CAACiB,SAAS,EAAEnB,MAAM,CAACkC,MAAM,CAAC;MACzDC,eAAe,EAAE,SAAAA,CAAUC,WAAW,EAAE;QACtC,IAAIC,UAAU,GAAGP,SAAS,CAACQ,gBAAgB,CAACC,aAAa,CAAC,CAAC;QAC3D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;UAC1C,IAAIjC,oBAAoB,CAAC8B,UAAU,CAACG,CAAC,CAAC,CAACE,YAAY,CAACvB,SAAS,CAACwB,IAAI,CAAC,CAACC,KAAK,CAAC,EAAE;YAC1E;YACA,OAAO,IAAI;UACb;QACF;QACA;QACA,OAAO,KAAK;MACd;IACF,CAAC,EAAEZ,MAAM,CAAC,CAAC;IACXhC,MAAM,CAAC6C,IAAI,CAACrC,gBAAgB,EAAEyB,WAAW,CAACL,GAAG,EAAEK,WAAW,CAAC;IAC3D,IAAI,CAACP,UAAU,CAACE,GAAG,CAACK,WAAW,CAACa,QAAQ,CAAC,CAAC,CAAC;IAC3C9C,MAAM,CAAC6C,IAAI,CAACpC,gBAAgB,EAAE,UAAUsC,IAAI,EAAE;MAC5C,IAAI5B,SAAS,CAACU,GAAG,CAAC,CAACkB,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE;QACjCC,mBAAmB,CAACD,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAACrB,UAAU,EAAEP,SAAS,EAAEW,SAAS,CAAC;MACxE;IACF,CAAC,EAAE,IAAI,CAAC;IACR;IACA;IACA;IACA,IAAImB,0BAA0B,GAAG3B,OAAO,IAAIA,OAAO,CAACP,IAAI,KAAK,iBAAiB,IAAIO,OAAO,CAAC4B,UAAU;IACpG,IAAI,CAACD,0BAA0B,EAAE;MAC/BhD,OAAO,CAACkD,eAAe,CAAC1B,YAAY,EAAE,IAAI,CAACC,UAAU,EAAEP,SAAS,CAAC;IACnE;IACAR,MAAM,CAACM,SAAS,CAACC,MAAM,CAACkC,IAAI,CAAC,IAAI,EAAEjC,SAAS,EAAEC,OAAO,EAAEC,GAAG,EAAEC,OAAO,CAAC;EACtE,CAAC;EACDZ,iBAAiB,CAACO,SAAS,CAACoC,MAAM,GAAG,YAAY;IAC/C/C,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EACDI,iBAAiB,CAACK,IAAI,GAAG,eAAe;EACxC,OAAOL,iBAAiB;AAC1B,CAAC,CAACP,QAAQ,CAAC;AACX,IAAI6C,mBAAmB,GAAG;EACxBM,SAAS,EAAE,SAAAA,CAAUC,QAAQ,EAAEC,SAAS,EAAErC,SAAS,EAAEW,SAAS,EAAE;IAC9D,IAAIa,IAAI,GAAGxB,SAAS,CAACwB,IAAI;IACzB,IAAIA,IAAI,CAACC,KAAK,CAACa,OAAO,CAAC,CAAC,EAAE;MACxB;IACF;IACA,IAAIC,cAAc,GAAGvC,SAAS,CAACwC,QAAQ,CAAC,WAAW,CAAC;IACpD,IAAIC,cAAc,GAAGF,cAAc,CAACC,QAAQ,CAAC,WAAW,CAAC;IACzD,IAAIE,UAAU,GAAGD,cAAc,CAAC/B,GAAG,CAAC,OAAO,CAAC;IAC5C,IAAIiC,WAAW,GAAGJ,cAAc,CAAC7B,GAAG,CAAC,aAAa,CAAC,KAAK,KAAK;IAC7D,IAAIkC,WAAW,GAAGL,cAAc,CAAC7B,GAAG,CAAC,aAAa,CAAC,KAAK,KAAK;IAC7DgC,UAAU,GAAG7D,MAAM,CAACgE,OAAO,CAACH,UAAU,CAAC,GAAGA,UAAU,GAAG,CAACA,UAAU,CAAC;IACnE,IAAII,QAAQ,GAAGnC,SAAS,CAACQ,gBAAgB,CAAC4B,OAAO,CAAC,CAAC;IACnD,IAAIC,YAAY,GAAGxB,IAAI,CAACwB,YAAY,CAAC,CAAC;IACtC,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,WAAW,GAAG1B,IAAI,CAAC2B,cAAc,CAAC;MACpCC,SAAS,EAAEb;IACb,CAAC,CAAC;IACF,IAAIc,EAAE,GAAG,EAAE;IACX,IAAIC,EAAE,GAAG,EAAE;IACX,IAAIC,SAAS,GAAGd,cAAc,CAACe,YAAY,CAAC,CAAC;IAC7C,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,WAAW,CAAC5B,MAAM,EAAED,CAAC,EAAE,EAAE;MAC3C,IAAIoC,SAAS,GAAGjC,IAAI,CAACkC,aAAa,CAACR,WAAW,CAAC7B,CAAC,CAAC,CAACsC,KAAK,CAAC;MACxD,IAAItC,CAAC,KAAK,CAAC,IAAI,CAACsB,WAAW,IAAItB,CAAC,KAAK6B,WAAW,CAAC5B,MAAM,GAAG,CAAC,IAAI,CAACsB,WAAW,EAAE;QAC3E;MACF;MACA,IAAIgB,SAAS,GAAGV,WAAW,CAAC7B,CAAC,CAAC,CAACuC,SAAS;MACxC,IAAIZ,YAAY,EAAE;QAChBK,EAAE,CAAC,CAAC,CAAC,GAAGI,SAAS;QACjBJ,EAAE,CAAC,CAAC,CAAC,GAAGP,QAAQ,CAACe,CAAC;QAClBP,EAAE,CAAC,CAAC,CAAC,GAAGG,SAAS;QACjBH,EAAE,CAAC,CAAC,CAAC,GAAGR,QAAQ,CAACe,CAAC,GAAGf,QAAQ,CAACgB,MAAM;MACtC,CAAC,MAAM;QACLT,EAAE,CAAC,CAAC,CAAC,GAAGP,QAAQ,CAACiB,CAAC;QAClBV,EAAE,CAAC,CAAC,CAAC,GAAGI,SAAS;QACjBH,EAAE,CAAC,CAAC,CAAC,GAAGR,QAAQ,CAACiB,CAAC,GAAGjB,QAAQ,CAACkB,KAAK;QACnCV,EAAE,CAAC,CAAC,CAAC,GAAGG,SAAS;MACnB;MACA,IAAIQ,UAAU,GAAGhB,SAAS,EAAE,GAAGP,UAAU,CAACpB,MAAM;MAChD,IAAI4C,IAAI,GAAG,IAAIpF,OAAO,CAACqF,IAAI,CAAC;QAC1BC,IAAI,EAAER,SAAS,IAAI,IAAI,GAAG,OAAO,GAAGA,SAAS,GAAG,IAAI;QACpDS,SAAS,EAAE,IAAI;QACfC,KAAK,EAAE;UACLC,EAAE,EAAElB,EAAE,CAAC,CAAC,CAAC;UACTmB,EAAE,EAAEnB,EAAE,CAAC,CAAC,CAAC;UACToB,EAAE,EAAEnB,EAAE,CAAC,CAAC,CAAC;UACToB,EAAE,EAAEpB,EAAE,CAAC,CAAC;QACV,CAAC;QACDqB,KAAK,EAAE9F,MAAM,CAAC+F,QAAQ,CAAC;UACrBC,MAAM,EAAEnC,UAAU,CAACuB,UAAU;QAC/B,CAAC,EAAEV,SAAS,CAAC;QACbuB,MAAM,EAAE;MACV,CAAC,CAAC;MACFhG,OAAO,CAACiG,oBAAoB,CAACb,IAAI,CAACI,KAAK,EAAEf,SAAS,CAACyB,SAAS,CAAC;MAC7D3C,SAAS,CAAC5B,GAAG,CAACyD,IAAI,CAAC;IACrB;EACF,CAAC;EACDe,cAAc,EAAE,SAAAA,CAAU7C,QAAQ,EAAEC,SAAS,EAAErC,SAAS,EAAEW,SAAS,EAAE;IACnE,IAAIa,IAAI,GAAGxB,SAAS,CAACwB,IAAI;IACzB,IAAI0D,mBAAmB,GAAGlF,SAAS,CAACwC,QAAQ,CAAC,gBAAgB,CAAC;IAC9D,IAAIC,cAAc,GAAGyC,mBAAmB,CAAC1C,QAAQ,CAAC,WAAW,CAAC;IAC9D,IAAIM,QAAQ,GAAGnC,SAAS,CAACQ,gBAAgB,CAAC4B,OAAO,CAAC,CAAC;IACnD,IAAIC,YAAY,GAAGxB,IAAI,CAACwB,YAAY,CAAC,CAAC;IACtC,IAAImC,gBAAgB,GAAG3D,IAAI,CAAC4D,mBAAmB,CAAC,CAAC;IACjD,IAAI,CAACD,gBAAgB,CAAC7D,MAAM,EAAE;MAC5B;IACF;IACA,IAAI+B,EAAE,GAAG,EAAE;IACX,IAAIC,EAAE,GAAG,EAAE;IACX,IAAIC,SAAS,GAAGd,cAAc,CAACe,YAAY,CAAC,CAAC;IAC7C,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8D,gBAAgB,CAAC7D,MAAM,EAAED,CAAC,EAAE,EAAE;MAChD,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,gBAAgB,CAAC9D,CAAC,CAAC,CAACC,MAAM,EAAE+D,CAAC,EAAE,EAAE;QACnD,IAAI5B,SAAS,GAAGjC,IAAI,CAACkC,aAAa,CAACyB,gBAAgB,CAAC9D,CAAC,CAAC,CAACgE,CAAC,CAAC,CAAC1B,KAAK,CAAC;QAChE,IAAIX,YAAY,EAAE;UAChBK,EAAE,CAAC,CAAC,CAAC,GAAGI,SAAS;UACjBJ,EAAE,CAAC,CAAC,CAAC,GAAGP,QAAQ,CAACe,CAAC;UAClBP,EAAE,CAAC,CAAC,CAAC,GAAGG,SAAS;UACjBH,EAAE,CAAC,CAAC,CAAC,GAAGR,QAAQ,CAACe,CAAC,GAAGf,QAAQ,CAACgB,MAAM;QACtC,CAAC,MAAM;UACLT,EAAE,CAAC,CAAC,CAAC,GAAGP,QAAQ,CAACiB,CAAC;UAClBV,EAAE,CAAC,CAAC,CAAC,GAAGI,SAAS;UACjBH,EAAE,CAAC,CAAC,CAAC,GAAGR,QAAQ,CAACiB,CAAC,GAAGjB,QAAQ,CAACkB,KAAK;UACnCV,EAAE,CAAC,CAAC,CAAC,GAAGG,SAAS;QACnB;QACA,IAAIS,IAAI,GAAG,IAAIpF,OAAO,CAACqF,IAAI,CAAC;UAC1BC,IAAI,EAAE,aAAa,GAAGe,gBAAgB,CAAC9D,CAAC,CAAC,CAACgE,CAAC,CAAC,CAACzB,SAAS;UACtDS,SAAS,EAAE,IAAI;UACfC,KAAK,EAAE;YACLC,EAAE,EAAElB,EAAE,CAAC,CAAC,CAAC;YACTmB,EAAE,EAAEnB,EAAE,CAAC,CAAC,CAAC;YACToB,EAAE,EAAEnB,EAAE,CAAC,CAAC,CAAC;YACToB,EAAE,EAAEpB,EAAE,CAAC,CAAC;UACV,CAAC;UACDqB,KAAK,EAAEpB,SAAS;UAChBuB,MAAM,EAAE;QACV,CAAC,CAAC;QACFhG,OAAO,CAACiG,oBAAoB,CAACb,IAAI,CAACI,KAAK,EAAEf,SAAS,CAACyB,SAAS,CAAC;QAC7D3C,SAAS,CAAC5B,GAAG,CAACyD,IAAI,CAAC;MACrB;IACF;EACF,CAAC;EACDoB,SAAS,EAAE,SAAAA,CAAUlD,QAAQ,EAAEC,SAAS,EAAErC,SAAS,EAAEW,SAAS,EAAE;IAC9DzB,2BAA2B,CAACkD,QAAQ,EAAEC,SAAS,EAAErC,SAAS,EAAEW,SAAS,CAAC;EACxE;AACF,CAAC;AACD,IAAI4E,kBAAkB,GAAG,aAAa,UAAU/F,MAAM,EAAE;EACtDZ,SAAS,CAAC2G,kBAAkB,EAAE/F,MAAM,CAAC;EACrC,SAAS+F,kBAAkBA,CAAA,EAAG;IAC5B,IAAI9F,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAG2F,kBAAkB,CAAC3F,IAAI;IACpC,OAAOH,KAAK;EACd;EACA8F,kBAAkB,CAAC3F,IAAI,GAAG,OAAO;EACjC,OAAO2F,kBAAkB;AAC3B,CAAC,CAAChG,iBAAiB,CAAC;AACpB,SAASgG,kBAAkB;AAC3B,IAAIC,kBAAkB,GAAG,aAAa,UAAUhG,MAAM,EAAE;EACtDZ,SAAS,CAAC4G,kBAAkB,EAAEhG,MAAM,CAAC;EACrC,SAASgG,kBAAkBA,CAAA,EAAG;IAC5B,IAAI/F,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAG2F,kBAAkB,CAAC3F,IAAI;IACpC,OAAOH,KAAK;EACd;EACA+F,kBAAkB,CAAC5F,IAAI,GAAG,OAAO;EACjC,OAAO4F,kBAAkB;AAC3B,CAAC,CAACjG,iBAAiB,CAAC;AACpB,SAASiG,kBAAkB;AAC3B,eAAejG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}