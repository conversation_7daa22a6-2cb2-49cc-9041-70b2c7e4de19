{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Item } from 'rc-menu';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport { cloneElement } from '../_util/reactNode';\nimport { SiderContext } from '../layout/Sider';\nimport Tooltip from '../tooltip';\nimport MenuContext from './MenuContext';\nconst MenuItem = props => {\n  var _a;\n  const {\n    className,\n    children,\n    icon,\n    title,\n    danger,\n    extra\n  } = props;\n  const {\n    prefixCls,\n    firstLevel,\n    direction,\n    disableMenuItemTitleTooltip,\n    inlineCollapsed: isInlineCollapsed\n  } = React.useContext(MenuContext);\n  const renderItemChildren = inlineCollapsed => {\n    const label = children === null || children === void 0 ? void 0 : children[0];\n    const wrapNode = /*#__PURE__*/React.createElement(\"span\", {\n      className: classNames(`${prefixCls}-title-content`, {\n        [`${prefixCls}-title-content-with-extra`]: !!extra || extra === 0\n      })\n    }, children);\n    // inline-collapsed.md demo 依赖 span 来隐藏文字,有 icon 属性，则内部包裹一个 span\n    // ref: https://github.com/ant-design/ant-design/pull/23456\n    if (!icon || /*#__PURE__*/React.isValidElement(children) && children.type === 'span') {\n      if (children && inlineCollapsed && firstLevel && typeof label === 'string') {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: `${prefixCls}-inline-collapsed-noicon`\n        }, label.charAt(0));\n      }\n    }\n    return wrapNode;\n  };\n  const {\n    siderCollapsed\n  } = React.useContext(SiderContext);\n  let tooltipTitle = title;\n  if (typeof title === 'undefined') {\n    tooltipTitle = firstLevel ? children : '';\n  } else if (title === false) {\n    tooltipTitle = '';\n  }\n  const tooltipProps = {\n    title: tooltipTitle\n  };\n  if (!siderCollapsed && !isInlineCollapsed) {\n    tooltipProps.title = null;\n    // Reset `open` to fix control mode tooltip display not correct\n    // ref: https://github.com/ant-design/ant-design/issues/16742\n    tooltipProps.open = false;\n  }\n  const childrenLength = toArray(children).length;\n  let returnNode = /*#__PURE__*/React.createElement(Item, Object.assign({}, omit(props, ['title', 'icon', 'danger']), {\n    className: classNames({\n      [`${prefixCls}-item-danger`]: danger,\n      [`${prefixCls}-item-only-child`]: (icon ? childrenLength + 1 : childrenLength) === 1\n    }, className),\n    title: typeof title === 'string' ? title : undefined\n  }), cloneElement(icon, {\n    className: classNames(/*#__PURE__*/React.isValidElement(icon) ? (_a = icon.props) === null || _a === void 0 ? void 0 : _a.className : '', `${prefixCls}-item-icon`)\n  }), renderItemChildren(isInlineCollapsed));\n  if (!disableMenuItemTitleTooltip) {\n    returnNode = /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, tooltipProps, {\n      placement: direction === 'rtl' ? 'left' : 'right',\n      classNames: {\n        root: `${prefixCls}-inline-collapsed-tooltip`\n      }\n    }), returnNode);\n  }\n  return returnNode;\n};\nexport default MenuItem;", "map": {"version": 3, "names": ["React", "classNames", "<PERSON><PERSON>", "toArray", "omit", "cloneElement", "SiderContext", "<PERSON><PERSON><PERSON>", "MenuContext", "MenuItem", "props", "_a", "className", "children", "icon", "title", "danger", "extra", "prefixCls", "firstLevel", "direction", "disableMenuItemTitleTooltip", "inlineCollapsed", "isInlineCollapsed", "useContext", "renderItemChildren", "label", "wrapNode", "createElement", "isValidElement", "type", "char<PERSON>t", "siderCollapsed", "tooltipTitle", "tooltipProps", "open", "<PERSON><PERSON><PERSON><PERSON>", "length", "returnNode", "Object", "assign", "undefined", "placement", "root"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/menu/MenuItem.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Item } from 'rc-menu';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport omit from \"rc-util/es/omit\";\nimport { cloneElement } from '../_util/reactNode';\nimport { SiderContext } from '../layout/Sider';\nimport Tooltip from '../tooltip';\nimport MenuContext from './MenuContext';\nconst MenuItem = props => {\n  var _a;\n  const {\n    className,\n    children,\n    icon,\n    title,\n    danger,\n    extra\n  } = props;\n  const {\n    prefixCls,\n    firstLevel,\n    direction,\n    disableMenuItemTitleTooltip,\n    inlineCollapsed: isInlineCollapsed\n  } = React.useContext(MenuContext);\n  const renderItemChildren = inlineCollapsed => {\n    const label = children === null || children === void 0 ? void 0 : children[0];\n    const wrapNode = /*#__PURE__*/React.createElement(\"span\", {\n      className: classNames(`${prefixCls}-title-content`, {\n        [`${prefixCls}-title-content-with-extra`]: !!extra || extra === 0\n      })\n    }, children);\n    // inline-collapsed.md demo 依赖 span 来隐藏文字,有 icon 属性，则内部包裹一个 span\n    // ref: https://github.com/ant-design/ant-design/pull/23456\n    if (!icon || /*#__PURE__*/React.isValidElement(children) && children.type === 'span') {\n      if (children && inlineCollapsed && firstLevel && typeof label === 'string') {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: `${prefixCls}-inline-collapsed-noicon`\n        }, label.charAt(0));\n      }\n    }\n    return wrapNode;\n  };\n  const {\n    siderCollapsed\n  } = React.useContext(SiderContext);\n  let tooltipTitle = title;\n  if (typeof title === 'undefined') {\n    tooltipTitle = firstLevel ? children : '';\n  } else if (title === false) {\n    tooltipTitle = '';\n  }\n  const tooltipProps = {\n    title: tooltipTitle\n  };\n  if (!siderCollapsed && !isInlineCollapsed) {\n    tooltipProps.title = null;\n    // Reset `open` to fix control mode tooltip display not correct\n    // ref: https://github.com/ant-design/ant-design/issues/16742\n    tooltipProps.open = false;\n  }\n  const childrenLength = toArray(children).length;\n  let returnNode = /*#__PURE__*/React.createElement(Item, Object.assign({}, omit(props, ['title', 'icon', 'danger']), {\n    className: classNames({\n      [`${prefixCls}-item-danger`]: danger,\n      [`${prefixCls}-item-only-child`]: (icon ? childrenLength + 1 : childrenLength) === 1\n    }, className),\n    title: typeof title === 'string' ? title : undefined\n  }), cloneElement(icon, {\n    className: classNames(/*#__PURE__*/React.isValidElement(icon) ? (_a = icon.props) === null || _a === void 0 ? void 0 : _a.className : '', `${prefixCls}-item-icon`)\n  }), renderItemChildren(isInlineCollapsed));\n  if (!disableMenuItemTitleTooltip) {\n    returnNode = /*#__PURE__*/React.createElement(Tooltip, Object.assign({}, tooltipProps, {\n      placement: direction === 'rtl' ? 'left' : 'right',\n      classNames: {\n        root: `${prefixCls}-inline-collapsed-tooltip`\n      }\n    }), returnNode);\n  }\n  return returnNode;\n};\nexport default MenuItem;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,IAAI,QAAQ,SAAS;AAC9B,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,WAAW,MAAM,eAAe;AACvC,MAAMC,QAAQ,GAAGC,KAAK,IAAI;EACxB,IAAIC,EAAE;EACN,MAAM;IACJC,SAAS;IACTC,QAAQ;IACRC,IAAI;IACJC,KAAK;IACLC,MAAM;IACNC;EACF,CAAC,GAAGP,KAAK;EACT,MAAM;IACJQ,SAAS;IACTC,UAAU;IACVC,SAAS;IACTC,2BAA2B;IAC3BC,eAAe,EAAEC;EACnB,CAAC,GAAGvB,KAAK,CAACwB,UAAU,CAAChB,WAAW,CAAC;EACjC,MAAMiB,kBAAkB,GAAGH,eAAe,IAAI;IAC5C,MAAMI,KAAK,GAAGb,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;IAC7E,MAAMc,QAAQ,GAAG,aAAa3B,KAAK,CAAC4B,aAAa,CAAC,MAAM,EAAE;MACxDhB,SAAS,EAAEX,UAAU,CAAC,GAAGiB,SAAS,gBAAgB,EAAE;QAClD,CAAC,GAAGA,SAAS,2BAA2B,GAAG,CAAC,CAACD,KAAK,IAAIA,KAAK,KAAK;MAClE,CAAC;IACH,CAAC,EAAEJ,QAAQ,CAAC;IACZ;IACA;IACA,IAAI,CAACC,IAAI,IAAI,aAAad,KAAK,CAAC6B,cAAc,CAAChB,QAAQ,CAAC,IAAIA,QAAQ,CAACiB,IAAI,KAAK,MAAM,EAAE;MACpF,IAAIjB,QAAQ,IAAIS,eAAe,IAAIH,UAAU,IAAI,OAAOO,KAAK,KAAK,QAAQ,EAAE;QAC1E,OAAO,aAAa1B,KAAK,CAAC4B,aAAa,CAAC,KAAK,EAAE;UAC7ChB,SAAS,EAAE,GAAGM,SAAS;QACzB,CAAC,EAAEQ,KAAK,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC;MACrB;IACF;IACA,OAAOJ,QAAQ;EACjB,CAAC;EACD,MAAM;IACJK;EACF,CAAC,GAAGhC,KAAK,CAACwB,UAAU,CAAClB,YAAY,CAAC;EAClC,IAAI2B,YAAY,GAAGlB,KAAK;EACxB,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;IAChCkB,YAAY,GAAGd,UAAU,GAAGN,QAAQ,GAAG,EAAE;EAC3C,CAAC,MAAM,IAAIE,KAAK,KAAK,KAAK,EAAE;IAC1BkB,YAAY,GAAG,EAAE;EACnB;EACA,MAAMC,YAAY,GAAG;IACnBnB,KAAK,EAAEkB;EACT,CAAC;EACD,IAAI,CAACD,cAAc,IAAI,CAACT,iBAAiB,EAAE;IACzCW,YAAY,CAACnB,KAAK,GAAG,IAAI;IACzB;IACA;IACAmB,YAAY,CAACC,IAAI,GAAG,KAAK;EAC3B;EACA,MAAMC,cAAc,GAAGjC,OAAO,CAACU,QAAQ,CAAC,CAACwB,MAAM;EAC/C,IAAIC,UAAU,GAAG,aAAatC,KAAK,CAAC4B,aAAa,CAAC1B,IAAI,EAAEqC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpC,IAAI,CAACM,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE;IAClHE,SAAS,EAAEX,UAAU,CAAC;MACpB,CAAC,GAAGiB,SAAS,cAAc,GAAGF,MAAM;MACpC,CAAC,GAAGE,SAAS,kBAAkB,GAAG,CAACJ,IAAI,GAAGsB,cAAc,GAAG,CAAC,GAAGA,cAAc,MAAM;IACrF,CAAC,EAAExB,SAAS,CAAC;IACbG,KAAK,EAAE,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG0B;EAC7C,CAAC,CAAC,EAAEpC,YAAY,CAACS,IAAI,EAAE;IACrBF,SAAS,EAAEX,UAAU,CAAC,aAAaD,KAAK,CAAC6B,cAAc,CAACf,IAAI,CAAC,GAAG,CAACH,EAAE,GAAGG,IAAI,CAACJ,KAAK,MAAM,IAAI,IAAIC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,SAAS,GAAG,EAAE,EAAE,GAAGM,SAAS,YAAY;EACpK,CAAC,CAAC,EAAEO,kBAAkB,CAACF,iBAAiB,CAAC,CAAC;EAC1C,IAAI,CAACF,2BAA2B,EAAE;IAChCiB,UAAU,GAAG,aAAatC,KAAK,CAAC4B,aAAa,CAACrB,OAAO,EAAEgC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,YAAY,EAAE;MACrFQ,SAAS,EAAEtB,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;MACjDnB,UAAU,EAAE;QACV0C,IAAI,EAAE,GAAGzB,SAAS;MACpB;IACF,CAAC,CAAC,EAAEoB,UAAU,CAAC;EACjB;EACA,OAAOA,UAAU;AACnB,CAAC;AACD,eAAe7B,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}