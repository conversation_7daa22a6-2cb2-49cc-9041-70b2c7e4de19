{"ast": null, "code": "import React,{useState,useEffect,useRef,useCallback}from'react';import{Row,Col,Card,Statistic,List,Table,Descriptions,Spin,Badge,Button,Select}from'antd';import*as echarts from'echarts/core';import{Bar<PERSON>hart}from'echarts/charts';import{GridComponent,TooltipComponent,LegendComponent,TitleComponent}from'echarts/components';import{CanvasRenderer}from'echarts/renderers';import styled from'styled-components';import CollapsibleSidebar from'../components/layout/CollapsibleSidebar';import axios from'axios';import vehiclesData from'../data/vehicles.json';import intersectionsData from'../data/intersections.json';// 导入路口数据\n// 注册必要的echarts组件\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";echarts.use([BarChart,GridComponent,TooltipComponent,LegendComponent,TitleComponent,Canvas<PERSON>enderer]);const StyledCanvas=styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;// 页面布局容器\nconst PageContainer=styled.div`\n  display: flex;\n  height: calc(100vh - 65px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;// 左侧信息栏容器\nconst LeftSidebar=styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;// 右侧信息栏容器\nconst RightSidebar=styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;// 主内容区域\nconst MainContent=styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props=>props.leftCollapsed&&props.rightCollapsed?'0 24px':props.leftCollapsed?'0 8px 0 24px':props.rightCollapsed?'0 24px 0 8px':'0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props=>props.leftCollapsed&&props.rightCollapsed?'0 24px':props.leftCollapsed?'0 8px 0 0':props.rightCollapsed?'0 0 0 8px':'0'};\n  position: relative;\n  z-index: 1;\n`;// 信息卡片\nconst InfoCard=styled(Card)`\n  margin-bottom: 12px;\n  height: ${props=>props.height||'auto'};\n\n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n\n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n\n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n`;// 自定义统计数字组件\nconst CompactStatistic=styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n\n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;const RealTimeTraffic=()=>{const[loading,setLoading]=useState(true);const[vehicles,setVehicles]=useState([]);const[events,setEvents]=useState([]);const[selectedVehicle,setSelectedVehicle]=useState(null);const[stats,setStats]=useState({totalVehicles:0,onlineVehicles:0,offlineVehicles:0,totalDevices:0,onlineDevices:0,offlineDevices:0});// 存储在线车辆的 BSM ID，初始时为空集合，表示所有车辆离线\nconst[onlineBsmIds,setOnlineBsmIds]=useState(()=>{try{const savedOnlineIds=localStorage.getItem('realTimeTrafficOnlineIds');// 返回空集合，确保所有车辆初始状态为离线\nreturn new Set();}catch(error){console.error('读取在线ID缓存失败:',error);return new Set();}});// 存储最后一次收到 BSM 消息的时间\nconst lastBsmTime=useRef({});const eventChartRef=useRef(null);// 添加侧边栏折叠状态\nconst[leftCollapsed,setLeftCollapsed]=useState(false);const[rightCollapsed,setRightCollapsed]=useState(false);// 添加时间段选择状态\nconst[selectedTimeRange,setSelectedTimeRange]=useState('24h');const[eventStats,setEventStats]=useState({'401':0,// 道路抛洒物\n'404':0,// 道路障碍物\n'405':0,// 行人通过马路\n'904':0,// 逆行车辆\n'910':0,// 违停车辆\n'1002':0,// 道路施工\n'901':0// 车辆超速\n});// 添加RSI事件缓存，用于检测重复事件\nconst prevRsiEvents=useRef(new Map());// 事件列表缓存，存储所有事件的完整信息\nconst eventListCache=useRef([]);// 事件ID计数器\nconst eventIdCounter=useRef(1);// ========== 数据库API调用函数 ==========\n/**\n   * 存储实时事件到数据库\n   */const storeEventToDatabase=async eventData=>{try{const apiUrl=process.env.REACT_APP_API_URL||'http://localhost:5000';const response=await axios.post(`${apiUrl}/api/events/store`,eventData);if(response.data&&response.data.success){console.log(`✅ 事件已存储到${response.data.storage}:`,eventData.eventTypeText);return true;}else{console.error('❌ 存储事件失败:',response.data);return false;}}catch(error){console.error('❌ 存储事件到数据库失败:',error);return false;}};/**\n   * 从数据库获取事件统计\n   */const fetchEventStatsFromDatabase=async function(){let timeRange=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'24h';try{// console.log(`timeRange);\nconsole.log('实际的时间段0',timeRange);const apiUrl=process.env.REACT_APP_API_URL||'http://localhost:5000';const response=await axios.get(`${apiUrl}/api/events/stats?timeRange=${timeRange}`);if(response.data&&response.data.success){console.log(`📊 从${response.data.storage}获取事件统计(${timeRange}):`,response.data.data);return response.data.data;}else{console.error('❌ 获取事件统计失败:',response.data);return null;}}catch(error){console.error('❌ 从数据库获取事件统计失败:',error);return null;}};/**\n   * 从数据库获取最近事件\n   */const fetchRecentEventsFromDatabase=async function(){let limit=arguments.length>0&&arguments[0]!==undefined?arguments[0]:10;try{const apiUrl=process.env.REACT_APP_API_URL||'http://localhost:5000';const response=await axios.get(`${apiUrl}/api/events/recent?limit=${limit}`);if(response.data&&response.data.success){console.log(`📋 从${response.data.storage}获取最近事件:`,response.data.data.length,'条');return response.data.data;}else{console.error('❌ 获取最近事件失败:',response.data);return[];}}catch(error){console.error('❌ 从数据库获取最近事件失败:',error);return[];}};// 添加手动更新车辆状态和位置信息的函数\nconst updateVehicleStatus=useCallback(function(bsmId,status){let speed=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;let lat=arguments.length>3&&arguments[3]!==undefined?arguments[3]:0;let lng=arguments.length>4&&arguments[4]!==undefined?arguments[4]:0;let heading=arguments.length>5&&arguments[5]!==undefined?arguments[5]:0;// 确保速度和航向角是格式化的数值，保留两位小数\nconst formattedSpeed=parseFloat(speed).toFixed(0);const formattedHeading=parseFloat(heading).toFixed(2);console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${formattedSpeed}, 位置=(${lat}, ${lng})`);// 更新在线状态\nif(status==='online'){setOnlineBsmIds(prev=>new Set([...prev,bsmId]));lastBsmTime.current[bsmId]=Date.now();}else{setOnlineBsmIds(prev=>{const newSet=new Set(prev);newSet.delete(bsmId);return newSet;});}// 更新车辆信息\nsetVehicles(prevVehicles=>prevVehicles.map(vehicle=>vehicle.bsmId===bsmId?{...vehicle,status:status,speed:parseFloat(formattedSpeed),// 确保是数值类型\nlat:parseFloat(lat.toFixed(7)),lng:parseFloat(lng.toFixed(7)),heading:parseFloat(formattedHeading)// 确保是数值类型\n}:vehicle));},[]);// 在组件挂载时，暴露updateVehicleStatus函数到window对象\nuseEffect(()=>{window.updateVehicleStatus=updateVehicleStatus;// 用于监听CampusModel是否接收到BSM消息的事件\nconst handleRealBsmReceived=event=>{if(event.data&&event.data.type==='realBsmReceived'){// console.log('收到CampusModel发送的真实BSM消息通知');\n}};window.addEventListener('message',handleRealBsmReceived);// 确保页面刷新时清空在线车辆状态\nconsole.log('组件初始化，重置所有车辆为离线状态');setOnlineBsmIds(new Set());lastBsmTime.current={};return()=>{window.removeEventListener('message',handleRealBsmReceived);delete window.updateVehicleStatus;};},[updateVehicleStatus]);// 从数据库加载事件数据\nconst loadEventsFromDatabase=async function(){let timeRange=arguments.length>0&&arguments[0]!==undefined?arguments[0]:selectedTimeRange;try{// 加载最近事件\nconst recentEvents=await fetchRecentEventsFromDatabase(10);if(recentEvents&&recentEvents.length>0){setEvents(recentEvents);console.log('✅ 从数据库加载了',recentEvents.length,'条最近事件');}// 加载事件统计（使用选择的时间段）\nconsole.log('实际的时间段1',timeRange);const stats=await fetchEventStatsFromDatabase(timeRange);if(stats){setEventStats(prevStats=>({...prevStats,...stats}));console.log(`✅ 从数据库加载了事件统计数据(${timeRange})`);}}catch(error){console.error('❌ 从数据库加载事件数据失败:',error);}};// 处理时间段选择变化\nconst handleTimeRangeChange=value=>{console.log('📅 时间段选择变化:',value);setSelectedTimeRange(value);// 立即重新加载事件统计数据\nloadEventsFromDatabase(value);};// 组件挂载时从数据库加载数据\nuseEffect(()=>{loadEventsFromDatabase();},[]);// 监听时间段变化，重新加载事件统计数据\nuseEffect(()=>{loadEventsFromDatabase(selectedTimeRange);},[selectedTimeRange]);// 定期从数据库刷新数据（每30秒）\nuseEffect(()=>{const refreshInterval=setInterval(()=>{console.log('🔄 定期刷新事件数据...','当前时间段:',selectedTimeRange);loadEventsFromDatabase(selectedTimeRange);},30000);// 30秒刷新一次\nreturn()=>clearInterval(refreshInterval);},[selectedTimeRange]);// 添加 selectedTimeRange 作为依赖\n// 定期清理过期事件（每5分钟）\nuseEffect(()=>{const cleanupInterval=setInterval(()=>{cleanupExpiredEvents();},300000);// 5分钟清理一次\nreturn()=>clearInterval(cleanupInterval);},[]);// 定期删除1分钟内没有更新的事件（每30秒检查一次）\nuseEffect(()=>{const removeInactiveInterval=setInterval(()=>{removeInactiveEvents();},30000);// 30秒检查一次\nreturn()=>clearInterval(removeInactiveInterval);},[]);// 获取车辆数据\nconst fetchVehicles=useCallback(async()=>{try{// 先尝试从API获取最新数据\nconsole.log('尝试从API获取最新车辆数据');const apiData=await fetchLatestVehiclesData(true);// 如果API获取成功，直接使用API数据\nif(apiData&&apiData.length>0){console.log('成功从API获取车辆数据，车辆数量:',apiData.length);// 获取所有车辆的BSM ID列表\nconst bsmIds=apiData.map(v=>v.bsmId).filter(id=>id);console.log('所有车辆的BSM ID:',bsmIds);// 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\nconst updatedVehicles=apiData.map(vehicle=>{// 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\nconst isOnline=vehicle.bsmId&&onlineBsmIds.has(vehicle.bsmId);return{...vehicle,plate:vehicle.plateNumber,// 适配表格显示\nstatus:isOnline?'online':'offline',// 只有收到BSM消息的车辆才显示为在线\nspeed:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.speed||0:0:0,lat:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.lat||0:0:0,lng:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.lng||0:0:0,heading:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.heading||0:0:0};});setVehicles(updatedVehicles);// 直接检查更新后的车辆在线状态\nconst onlineCount=updatedVehicles.filter(v=>v.status==='online').length;const totalCount=updatedVehicles.length;console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount-onlineCount}`);// 更新车辆统计数据\nsetStats(prevStats=>({...prevStats,totalVehicles:totalCount,onlineVehicles:onlineCount,offlineVehicles:totalCount-onlineCount}));return;}// 如果API获取失败，回退到本地JSON文件\nconsole.log('API获取失败，回退到本地vehiclesData');const vehiclesList=vehiclesData.vehicles||[];console.log('从vehiclesData获取车辆数据，车辆数量:',vehiclesList.length);// 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\nconst updatedVehicles=vehiclesList.map(vehicle=>{// 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\nconst isOnline=vehicle.bsmId&&onlineBsmIds.has(vehicle.bsmId);return{...vehicle,plate:vehicle.plateNumber,// 适配表格显示\nstatus:isOnline?'online':'offline',// 只有收到BSM消息的车辆才显示为在线\nspeed:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.speed||0:0:0,lat:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.lat||0:0:0,lng:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.lng||0:0:0,heading:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.heading||0:0:0};});setVehicles(updatedVehicles);// 直接检查更新后的车辆在线状态\nconst onlineCount=updatedVehicles.filter(v=>v.status==='online').length;const totalCount=updatedVehicles.length;console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount-onlineCount}`);// 更新车辆统计数据\nsetStats(prevStats=>({...prevStats,totalVehicles:totalCount,onlineVehicles:onlineCount,offlineVehicles:totalCount-onlineCount}));}catch(error){console.error('获取车辆列表失败:',error);}},[onlineBsmIds]);// 获取设备统计数据\nconst fetchDeviceStats=async()=>{try{const apiUrl=process.env.REACT_APP_API_URL||'http://localhost:5000';const response=await axios.get(`${apiUrl}/api/devices`);if(response.data&&response.data.success){const devicesData=response.data.data;// 更新设备统计数据\nsetStats(prevStats=>({...prevStats,totalDevices:devicesData.length,onlineDevices:devicesData.filter(d=>d.status==='online').length,offlineDevices:devicesData.filter(d=>d.status==='offline').length}));}}catch(error){console.error('获取设备统计数据失败:',error);}};// 监听 BSM 消息\nuseEffect(()=>{const handleBsmMessage=event=>{if(event.data&&event.data.type==='bsm'){// 获取bsmId，确保它正确地从消息中提取\nconst bsmData=event.data.data||{};const bsmId=bsmData.bsmId||event.data.bsmId;if(!bsmId){console.error('BSM消息缺少bsmId:',event.data);return;}// console.log('收到BSM消息，ID:', bsmId);\nconst now=Date.now();// 更新最后接收时间\nlastBsmTime.current[bsmId]=now;// 添加到在线bsmId集合\nsetOnlineBsmIds(prev=>new Set([...prev,bsmId]));// 提取正确的BSM数据并格式化为两位小数\nconst speed=parseFloat((parseFloat(bsmData.partSpeed||event.data.speed||0)*3.6).toFixed(0));// 转换为km/h，保留两位小数\nconst lat=parseFloat(parseFloat(bsmData.partLat||event.data.lat||0).toFixed(7));const lng=parseFloat(parseFloat(bsmData.partLong||event.data.lng||0).toFixed(7));const heading=parseFloat(parseFloat(bsmData.partHeading||event.data.heading||0).toFixed(2));// 保留两位小数\n// console.log(`车辆${bsmId}状态: 速度=${speed.toFixed(2)}km/h, 位置=(${lat.toFixed(7)}, ${lng.toFixed(7)}), 航向=${heading.toFixed(2)}°`);\n// 更新车辆状态和位置信息\nsetVehicles(prevVehicles=>{// 检查是否找到对应车辆\nconst foundVehicle=prevVehicles.find(v=>v.bsmId===bsmId);if(!foundVehicle){console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);return prevVehicles;}const updatedVehicles=prevVehicles.map(vehicle=>vehicle.bsmId===bsmId?{...vehicle,status:'online',speed:speed,lat:lat,lng:lng,heading:heading}:vehicle);// 计算更新后的在线车辆数量\nconst onlineCount=updatedVehicles.filter(v=>v.status==='online').length;const totalCount=updatedVehicles.length;// 更新统计数据\nsetStats(prevStats=>({...prevStats,onlineVehicles:onlineCount,offlineVehicles:totalCount-onlineCount}));return updatedVehicles;});}};// 添加消息监听器\nwindow.addEventListener('message',handleBsmMessage);// 清理函数\nreturn()=>{window.removeEventListener('message',handleBsmMessage);};},[]);// 修改检查在线状态的useEffect，同步更新统计信息\nuseEffect(()=>{const checkOnlineStatus=()=>{const now=Date.now();console.log('检查车辆在线状态...');// 只有在特定条件下才将车辆设为离线\n// 例如，只有收到过BSM消息的车辆才会被检查是否超时\nsetOnlineBsmIds(prev=>{const newOnlineBsmIds=new Set(prev);let hasChanges=false;// 仅检查已有最后更新时间的车辆\nprev.forEach(bsmId=>{const lastTime=lastBsmTime.current[bsmId];// 只有收到过BSM消息的车辆才会被检查是否超时\nif(lastTime&&now-lastTime>30000){console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);newOnlineBsmIds.delete(bsmId);hasChanges=true;}});if(hasChanges){// 更新车辆状态\nsetVehicles(prevVehicles=>{const updatedVehicles=prevVehicles.map(vehicle=>{// 只更新有最后更新时间的车辆状态\nif(lastBsmTime.current[vehicle.bsmId]){const isOnline=newOnlineBsmIds.has(vehicle.bsmId);return{...vehicle,status:isOnline?'online':'offline'};}return vehicle;});// 计算更新后的在线车辆数量\nconst onlineCount=updatedVehicles.filter(v=>v.status==='online').length;const totalCount=updatedVehicles.length;// 更新统计数据\nsetStats(prevStats=>({...prevStats,onlineVehicles:onlineCount,offlineVehicles:totalCount-onlineCount}));return updatedVehicles;});}return newOnlineBsmIds;});};const interval=setInterval(checkOnlineStatus,5000);return()=>clearInterval(interval);},[]);// 重置所有车辆的初始状态\nuseEffect(()=>{// 将所有车辆状态重置为离线\nconst resetAllVehicles=()=>{// 只有在没有任何BSM消息的情况下才执行重置\nif(onlineBsmIds.size===0){setVehicles(prevVehicles=>prevVehicles.map(vehicle=>({...vehicle,status:'offline',speed:0,lat:0,lng:0,heading:0})));console.log('已重置所有车辆为离线状态');}};// 初始执行一次\nresetAllVehicles();// 然后每30秒检查一次\nconst interval=setInterval(resetAllVehicles,30000);return()=>clearInterval(interval);},[onlineBsmIds]);// 在组件挂载时获取数据\nuseEffect(()=>{const loadData=async()=>{setLoading(true);fetchVehicles();await fetchDeviceStats();setLoading(false);};loadData();// // 降低更新频率，避免频繁覆盖状态\n// const interval = setInterval(loadData, 300000); // 每60x5秒更新一次\n// return () => clearInterval(interval);\n},[]);// 添加对车辆数据变更的监听\nuseEffect(()=>{console.log('设置车辆数据变更监听');// 监听自定义事件\nconst handleVehiclesDataChanged=()=>{console.log('检测到车辆数据变更事件，重新获取车辆列表');fetchVehicles();};// 监听localStorage变化\nconst handleStorageChange=event=>{if(event.key==='vehiclesLastUpdated'||event.key==='vehiclesData'){console.log('检测到localStorage变化，重新获取车辆列表');fetchVehicles();}};// 添加事件监听器\nwindow.addEventListener('vehiclesDataChanged',handleVehiclesDataChanged);window.addEventListener('storage',handleStorageChange);// 初始检查是否有更新\nconst lastUpdated=localStorage.getItem('vehiclesLastUpdated');if(lastUpdated){console.log('初始检查到vehiclesLastUpdated:',lastUpdated);fetchVehicles();}// 设置更频繁的强制轮询，确保在部署环境中也能获取最新数据\nconst forcedPollingInterval=setInterval(()=>{console.log('强制轮询：重新获取车辆列表');fetchVehicles();},10000);// 每10秒强制刷新一次\n// 清理函数\nreturn()=>{window.removeEventListener('vehiclesDataChanged',handleVehiclesDataChanged);window.removeEventListener('storage',handleStorageChange);clearInterval(forcedPollingInterval);};},[]);// 添加检查本地缓存与API数据是否一致的机制\n// 定义一个单独的API调用函数，用于获取最新的车辆列表数据\nconst fetchLatestVehiclesData=async function(){let returnData=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;try{// 尝试多个可能的API地址\nconst possibleApiUrls=[process.env.REACT_APP_API_URL||'http://localhost:5000'// 'http://localhost:5000',\n// window.location.origin, // 当前站点的根URL\n// `${window.location.origin}/api`, // 当前站点下的/api路径\n// 'http://localhost:5000/api',\n// 'http://127.0.0.1:5000',\n// 'http://127.0.0.1:5000/api'\n];console.log('尝试从多个API地址获取车辆数据');// // 尝试从本地JSON文件直接获取\n// try {\n//   console.log('尝试从本地JSON文件获取数据');\n//   const jsonResponse = await axios.get('/vehicles.json');\n//   if (jsonResponse.data && jsonResponse.data.vehicles) {\n//     console.log('成功从本地JSON文件获取数据:', jsonResponse.data.vehicles.length);\n//     if (returnData) {\n//       return jsonResponse.data.vehicles;\n//     }\n//     processVehiclesData(jsonResponse.data.vehicles);\n//     return jsonResponse.data.vehicles;\n//   }\n// } catch (jsonError) {\n//   console.log('从本地JSON获取失败:', jsonError.message);\n// }\n// 逐个尝试API地址\nlet succeeded=false;let vehiclesData=null;for(const apiUrl of possibleApiUrls){if(succeeded)break;try{console.log(`尝试从 ${apiUrl}/api/vehicles/list 获取数据`);const response=await axios.get(`${apiUrl}/api/vehicles/list`);if(response.data&&response.data.vehicles){console.log(`成功从 ${apiUrl} 获取数据:`,response.data.vehicles.length);vehiclesData=response.data.vehicles;if(returnData){return vehiclesData;}processVehiclesData(response.data.vehicles);succeeded=true;break;}}catch(error){console.log(`从 ${apiUrl} 获取失败:`,error.message);// 继续尝试下一个URL\n}}if(!succeeded&&!returnData){console.log('所有API地址都获取失败，尝试使用vehicles.json');// 尝试从当前页面获取\ntry{const response=await fetch('/vehicles.json');if(response.ok){const data=await response.json();if(data&&data.vehicles){console.log('从public/vehicles.json获取数据成功:',data.vehicles.length);processVehiclesData(data.vehicles);return data.vehicles;}}}catch(e){console.error('从public/vehicles.json获取失败:',e);}}return vehiclesData||[];}catch(error){console.error('获取车辆列表失败:',error);return[];}};// 添加处理车辆数据的辅助函数\nconst processVehiclesData=newVehicles=>{// 与当前列表比较\nif(vehicles.length!==newVehicles.length){console.log('检测到车辆数量变化，从',vehicles.length,'到',newVehicles.length);fetchVehicles();// 重新加载\nreturn;}// 检查是否有新车辆ID\nconst currentIds=new Set(vehicles.map(v=>v.id));const hasNewVehicle=newVehicles.some(v=>!currentIds.has(v.id));if(hasNewVehicle){console.log('检测到新增车辆');fetchVehicles();// 重新加载\n}};// 添加自动选择第一个车辆的逻辑\nuseEffect(()=>{// 当车辆列表加载完成且有车辆数据时\nif(vehicles.length>0&&!selectedVehicle){// 自动选择第一个车辆\nsetSelectedVehicle(vehicles[0]);console.log('已自动选择第一个车辆:',vehicles[0].plateNumber);}},[vehicles,selectedVehicle]);// 修改图表初始化代码\nuseEffect(()=>{let chart=null;let handleResize=null;let lastUpdateTime=new Date();// 确保 DOM 元素存在\nif(!eventChartRef.current){console.error('图表容器未找到');return;}// 检查并清理已存在的图表实例\nlet existingChart=echarts.getInstanceByDom(eventChartRef.current);if(existingChart){existingChart.dispose();}try{// 初始化新的图表实例\nchart=echarts.init(eventChartRef.current);// 设置图表配置\nconst updateChart=()=>{const currentTime=new Date();lastUpdateTime=currentTime;// 事件类型配置\n// const eventTypes = [\n//   { type: '401', name: '道路抛洒物', color: '#ff4d4f' },\n//   { type: '404', name: '道路障碍物', color: '#faad14' },\n//   { type: '405', name: '行人通过马路', color: '#1890ff' },\n//   { type: '904', name: '逆行车辆', color: '#f5222d' },\n//   { type: '910', name: '违停车辆', color: '#722ed1' },\n//   { type: '1002', name: '道路施工', color: '#fa8c16' },\n//   { type: '901', name: '车辆超速', color: '#eb2f96' }\n// ];\nconst eventTypes=[{type:'401',name:'道路抛洒物',color:'#faad14'},{type:'404',name:'道路障碍物',color:'#ff7a45'},{type:'405',name:'行人通过马路',color:'#52c41a'},{type:'904',name:'逆行车辆',color:'#f5222d'},{type:'910',name:'违停车辆',color:'#ff4d4f'},{type:'1002',name:'道路施工',color:'#1890ff'},{type:'901',name:'车辆超速',color:'#fa8c16'}];// 处理数据\nconst data=eventTypes.map(event=>({value:eventStats[event.type]||0,name:event.name,itemStyle:{color:event.color}})).filter(item=>item.value>0).sort((a,b)=>b.value-a.value);const option={title:{text:`最后更新: ${currentTime.toLocaleTimeString()}`,left:'center',top:-3,textStyle:{fontSize:12,color:'#999'}},grid:{top:30,bottom:0,left:0,right:50,containLabel:true},animation:true,animationDuration:0,animationDurationUpdate:1000,animationEasingUpdate:'quinticInOut',tooltip:{trigger:'axis',axisPointer:{type:'shadow'}},xAxis:{type:'value',show:false,splitLine:{show:false}},yAxis:{type:'category',data:data.map(item=>item.name),axisLabel:{fontSize:12,color:'#666',margin:8},axisTick:{show:false},axisLine:{show:false}},series:[{type:'bar',data:data,barWidth:'50%',label:{show:true,position:'right',formatter:'{c}次',fontSize:12,color:'#666'},itemStyle:{borderRadius:[0,4,4,0]},realtimeSort:false,animationDelay:function(idx){return idx*100;}}]};// 使用 notMerge: false 来保持增量更新\nchart.setOption(option,{notMerge:false,replaceMerge:['series']});};// 初始更新\nupdateChart();// 监听事件统计变化，每分钟更新一次\nconst statsInterval=setInterval(updateChart,60000);// 60000ms = 1分钟\n// 监听窗口大小变化\nhandleResize=()=>{var _chart;(_chart=chart)===null||_chart===void 0?void 0:_chart.resize();};window.addEventListener('resize',handleResize);// 清理函数\nreturn()=>{clearInterval(statsInterval);if(handleResize){window.removeEventListener('resize',handleResize);}if(chart){chart.dispose();}};}catch(error){console.error('初始化图表失败:',error);}},[eventStats]);// 修改 RSI 消息处理逻辑\nuseEffect(()=>{const handleRsiMessage=event=>{try{if(event.data&&event.data.type==='RSI'){const rsiData=event.data.data;if(!rsiData||!rsiData.rtes)return;// if(rsiData.rtes.length > 0){\n//   // console.log('RealTimeTraffic 收到 RSI 消息:', event.data);\n//   if(parseFloat(rsiData.posLong) < 113.0){\n//     console.log('位置不在测试范围的 RSI 消息:', event.data);\n//     console.log('收到RSI消息，但位置不在测试范围内，忽略');\n//     return;\n//   }\n// }\nconst latitude=parseFloat(rsiData.posLat);const longitude=parseFloat(rsiData.posLong);const rsuId=rsiData.rsuId;const mac=event.data.mac||'';const timestamp=event.data.tm||Date.now();// 统计本帧所有非重复事件类型\nconst nonDuplicateEventTypes=[];rsiData.rtes.forEach(event=>{const eventType=event.eventType;// 根据事件类型设置不同的位置精度和去重策略\nlet latFixed='';let lngFixed='';let eventKey='';if(eventType==='904'||eventType==='901'){// 逆行和超速：使用3位小数精度（约111米范围）\nlatFixed=Math.floor(latitude*Math.pow(10,3))/Math.pow(10,3);lngFixed=Math.floor(longitude*Math.pow(10,3))/Math.pow(10,3);eventKey=`${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;}else if(eventType==='910'){// 违停车辆：使用2位小数精度（约1.1公里范围），忽略MAC地址\n// 这样可以将相近位置的违停事件归为同一类\nlatFixed=Math.floor(latitude*Math.pow(10,4))/Math.pow(10,4);lngFixed=Math.floor(longitude*Math.pow(10,4))/Math.pow(10,4);eventKey=`${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;}else{// 其他事件：使用4位小数精度（约11米范围）\nlatFixed=Math.floor(latitude*Math.pow(10,4))/Math.pow(10,4);lngFixed=Math.floor(longitude*Math.pow(10,4))/Math.pow(10,4);eventKey=`${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;}const duplicateResult=checkDuplicateEvent(eventType,eventKey,timestamp,{lat:latitude,lng:longitude});const isDuplicate=duplicateResult.isDuplicate;// 获取事件类型的中文描述\nlet eventTypeText='';let eventColor='';switch(eventType){case'401':eventTypeText='道路抛洒物';eventColor='#faad14';break;case'404':eventTypeText='道路障碍物';eventColor='#ff7a45';break;case'405':eventTypeText='行人通过马路';eventColor='#52c41a';break;case'904':eventTypeText='逆行车辆';eventColor='#f5222d';break;case'910':eventTypeText='违停车辆';eventColor='#ff4d4f';break;case'1002':eventTypeText='道路施工';eventColor='#1890ff';break;case'901':eventTypeText='车辆超速';eventColor='#fa8c16';break;default:eventTypeText=event.description||'未知事件';eventColor='#8c8c8c';}// 更新事件列表\n// const newEvent = {\n//   key: Date.now() + Math.random(),\n//   type: eventTypeText,\n//   time: new Date().toLocaleTimeString(),\n//   vehicle: rsiData.rsuId || '未知设备',\n//   color: eventColor,\n//   eventType: eventType,\n//   location: {\n//     latitude: latitude,\n//     longitude: longitude\n//   }\n// };\n// setEvents(prev => {\n//   const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n//   return newEvents;\n// });\nif(!isDuplicate){// 创建事件数据，使用新的事件ID\nconst eventData={eventId:duplicateResult.eventId,eventType:eventType,eventTypeText:eventTypeText,rsuId:rsiData.rsuId||'未知设备',mac:mac,latitude:latitude,longitude:longitude,site:getNearestIntersectionName(latitude,longitude),eventKey:eventKey,color:eventColor,timestamp:new Date().toISOString()};// 存储到数据库\nstoreEventToDatabase(eventData).then(success=>{if(success){console.log(`✅ 事件已存储到数据库: ${eventTypeText}`);// 更新本地事件列表（用于实时显示）\nconst newEvent={key:Date.now()+Math.random(),type:eventTypeText,time:new Date().toLocaleTimeString(),rsuId:rsiData.rsuId||'未知设备',color:eventColor,eventType:eventType,location:{latitude:latitude,longitude:longitude},site:eventData.site};setEvents(prev=>{const newEvents=[newEvent,...prev].slice(0,10);// 只保留最近10条记录\nreturn newEvents;});}else{console.error(`❌ 事件存储失败: ${eventTypeText}`);}});nonDuplicateEventTypes.push(eventType);}else{// console.log(`检测到重复事件，不累计统计: ${eventTypeText}`);\n}});// 如果有新事件，从数据库刷新统计数据\nif(nonDuplicateEventTypes.length>0){console.log(`📊 检测到 ${nonDuplicateEventTypes.length} 个新事件，刷新统计数据`);// 延迟1秒后从数据库获取最新统计，确保数据已存储\nconsole.log('实际的时间段2',selectedTimeRange);setTimeout(async()=>{const stats=await fetchEventStatsFromDatabase(selectedTimeRange);if(stats){setEventStats(prevStats=>({...prevStats,...stats}));console.log('✅ 事件统计数据已从数据库更新，时间段:',selectedTimeRange);}},1000);}}}catch(error){console.error('处理 RSI 消息失败:',error);}};// 添加消息监听器\nwindow.addEventListener('message',handleRsiMessage);return()=>{window.removeEventListener('message',handleRsiMessage);};},[]);// 获取事件类型的阈值配置\nconst getEventThresholds=eventType=>{switch(eventType){case'910':// 违停车辆\nreturn{timeThreshold:300000,distanceThreshold:10};// 5分钟, 20米\ncase'904':// 逆行车辆\nreturn{timeThreshold:10000,distanceThreshold:20};// 10秒, 20米\ncase'901':// 车辆超速\nreturn{timeThreshold:30000,distanceThreshold:20};// 30秒, 50米\ncase'401':// 道路抛洒物\ncase'404':// 道路障碍物\ncase'1002':// 道路施工\nreturn{timeThreshold:600000,distanceThreshold:5};// 10分钟, 30米\ncase'405':// 行人通过马路\nreturn{timeThreshold:10000,distanceThreshold:10};// 10秒, 10米\ndefault:return{timeThreshold:5000,distanceThreshold:5};// 5秒, 5米\n}};// 优化后的事件重复检查逻辑\nconst checkDuplicateEvent=(eventType,eventKey,currentTime,currentPos)=>{const{timeThreshold,distanceThreshold}=getEventThresholds(eventType);// 遍历事件列表缓存中的所有事件\nfor(let i=0;i<eventListCache.current.length;i++){const cachedEvent=eventListCache.current[i];// 检查事件类型是否相同\nif(cachedEvent.eventType!==eventType){continue;}// 计算时间差\nconst timeDiff=currentTime-cachedEvent.lastUpdateTime;// 检查时间差是否在阈值内\nif(timeDiff>timeThreshold){continue;}// 计算距离\nconst distance=calculateDistance(currentPos.lat,currentPos.lng,cachedEvent.position.lat,cachedEvent.position.lng);// 检查距离是否在阈值内\nif(distance<=distanceThreshold){// 找到匹配的事件，更新信息\ncachedEvent.eventKey=eventKey;cachedEvent.lastUpdateTime=currentTime;cachedEvent.position={...currentPos};cachedEvent.updateCount=(cachedEvent.updateCount||1)+1;// console.log(`🔄 检测到重复事件 ${eventType} (ID: ${cachedEvent.eventId})，时间差: ${(timeDiff/1000).toFixed(1)}s，距离: ${distance.toFixed(1)}m，更新次数: ${cachedEvent.updateCount}`);\nreturn{isDuplicate:true,eventId:cachedEvent.eventId,matchedEvent:cachedEvent};}}// 没有找到匹配的事件，创建新事件\nconst newEventId=`EVT_${eventIdCounter.current.toString().padStart(6,'0')}`;eventIdCounter.current++;const newEvent={eventId:newEventId,eventType:eventType,eventKey:eventKey,firstDetectedTime:currentTime,lastUpdateTime:currentTime,position:{...currentPos},updateCount:1};// 添加到事件列表缓存\neventListCache.current.push(newEvent);console.log(`✨ 创建新事件 ${eventType} (ID: ${newEventId})，位置: (${currentPos.lat.toFixed(6)}, ${currentPos.lng.toFixed(6)})`);return{isDuplicate:false,eventId:newEventId,newEvent:newEvent};};// 清理过期事件的函数\nconst cleanupExpiredEvents=()=>{const currentTime=Date.now();const maxAge=3600000;// 1小时\nconst initialCount=eventListCache.current.length;eventListCache.current=eventListCache.current.filter(event=>{const age=currentTime-event.lastUpdateTime;return age<=maxAge;});const removedCount=initialCount-eventListCache.current.length;if(removedCount>0){console.log(`🧹 清理了 ${removedCount} 个过期事件，当前缓存事件数: ${eventListCache.current.length}`);}};// 删除1分钟内没有更新的事件\nconst removeInactiveEvents=()=>{const currentTime=Date.now();const inactiveThreshold=60000;// 1分钟\nconst initialCount=eventListCache.current.length;const removedEvents=[];eventListCache.current=eventListCache.current.filter(event=>{const timeSinceLastUpdate=currentTime-event.lastUpdateTime;if(timeSinceLastUpdate>inactiveThreshold){removedEvents.push({id:event.eventId,type:event.eventType,inactiveTime:(timeSinceLastUpdate/1000).toFixed(1)});return false;// 删除该事件\n}return true;// 保留该事件\n});const removedCount=initialCount-eventListCache.current.length;if(removedCount>0){console.log(`🗑️ 删除了 ${removedCount} 个1分钟内未更新的事件:`);removedEvents.forEach(event=>{console.log(`   - 事件 ${event.id} (类型: ${event.type})，未更新时间: ${event.inactiveTime}秒`);});console.log(`📊 当前缓存事件数: ${eventListCache.current.length}`);}};// 添加计算两点之间距离的函数\nconst calculateDistance=(lat1,lon1,lat2,lon2)=>{if(lat1===0||lon1===0||lat2===0||lon2===0){return 999;}const R=6371000;const dLat=(lat2-lat1)*Math.PI/180;const dLon=(lon2-lon1)*Math.PI/180;const a=Math.sin(dLat/2)*Math.sin(dLat/2)+Math.cos(lat1*Math.PI/180)*Math.cos(lat2*Math.PI/180)*Math.sin(dLon/2)*Math.sin(dLon/2);const c=2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a));return R*c;};// 根据坐标查找最近的路口名称\nconst getNearestIntersectionName=(lat,lng)=>{// 从 intersections.json 获取所有路口\nconst intersections=intersectionsData.intersections||[];let minDistance=Infinity;let nearestName='未知地点';intersections.forEach(inter=>{const interLat=parseFloat(inter.latitude);const interLng=parseFloat(inter.longitude);const dist=calculateDistance(lat,lng,interLat,interLng);if(dist<minDistance){minDistance=dist;nearestName=inter.name;}});return nearestName;};// 处理车辆选择\nconst handleVehicleSelect=vehicle=>{console.log('选择车辆:',vehicle.plateNumber,'状态:',vehicle.status);setSelectedVehicle(vehicle);};// 修改车辆状态更新逻辑，确保同步更新selectedVehicle\nuseEffect(()=>{// 当车辆列表更新后，如果当前有选中的车辆，则更新选中的车辆信息\nif(selectedVehicle){const updatedSelectedVehicle=vehicles.find(v=>v.id===selectedVehicle.id);if(updatedSelectedVehicle&&(updatedSelectedVehicle.status!==selectedVehicle.status||updatedSelectedVehicle.speed!==selectedVehicle.speed||updatedSelectedVehicle.lat!==selectedVehicle.lat||updatedSelectedVehicle.lng!==selectedVehicle.lng||updatedSelectedVehicle.heading!==selectedVehicle.heading)){console.log(`更新选中车辆 ${selectedVehicle.plateNumber} 的信息:`,`状态从 ${selectedVehicle.status} 变为 ${updatedSelectedVehicle.status}`);setSelectedVehicle(updatedSelectedVehicle);}}},[vehicles,selectedVehicle]);// 车辆列表列定义\nconst vehicleColumns=[{title:'车牌号',dataIndex:'plate',key:'plate',width:'40%'},{title:'状态',dataIndex:'status',key:'status',width:'30%',render:status=>/*#__PURE__*/_jsx(Badge,{status:status==='online'?'success':'error',text:status==='online'?'在线':'离线'})},{title:'速度',dataIndex:'speed',key:'speed',width:'30%',render:speed=>`${typeof speed==='number'?speed.toFixed(0):speed} km/h`}];// 修改实时事件列表的渲染\nconst renderEventList=()=>/*#__PURE__*/_jsx(List,{size:\"small\",dataSource:events,renderItem:item=>/*#__PURE__*/_jsx(List.Item,{style:{padding:'8px 0'},children:/*#__PURE__*/_jsx(List.Item.Meta,{title:/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'13px',marginBottom:'4px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:item.color,marginRight:'8px'},children:item.type}),/*#__PURE__*/_jsx(\"span\",{style:{color:'#666',fontSize:'12px'},children:item.time})]}),description:/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#666'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u5730\\u70B9: \",item.site||'未知地点']}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u4F4D\\u7F6E: \",item.location?`${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}`:'未知位置']})]})})}),style:{maxHeight:'calc(100% - 24px)',overflowY:'auto'}});// 添加强制定期从API获取最新数据的机制\nuseEffect(()=>{const apiPollingInterval=setInterval(()=>{console.log('直接从API检查车辆数据更新');fetchLatestVehiclesData();},15000);// 每15秒检查一次API\nreturn()=>clearInterval(apiPollingInterval);},[vehicles]);return/*#__PURE__*/_jsx(Spin,{spinning:loading,tip:\"\\u52A0\\u8F7D\\u4E2D...\",children:/*#__PURE__*/_jsxs(PageContainer,{children:[/*#__PURE__*/_jsxs(CollapsibleSidebar,{position:\"left\",collapsed:leftCollapsed,onCollapse:()=>setLeftCollapsed(!leftCollapsed),children:[/*#__PURE__*/_jsx(InfoCard,{title:\"\\u8F66\\u8F86\\u548C\\u8BBE\\u5907\\u7EDF\\u8BA1\",bordered:false,height:\"160px\",children:/*#__PURE__*/_jsxs(Row,{gutter:[8,1],children:[/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u8F66\\u8F86\\u603B\\u6570\",value:stats.totalVehicles,valueStyle:{color:'#3f8600',display:'flex',justifyContent:'center'}// Style={{}}\n})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u5728\\u7EBF\\u8F66\\u8F86\",value:stats.onlineVehicles// suffix={`/ ${stats.totalVehicles}`}\n,valueStyle:{color:'#3f8600',display:'flex',justifyContent:'center'}})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u79BB\\u7EBF\\u8F66\\u8F86\",value:stats.offlineVehicles// suffix={`/ ${stats.totalVehicles}`}\n,valueStyle:{color:'#cf1322',display:'flex',justifyContent:'center'}})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u8BBE\\u5907\\u603B\\u6570\",value:stats.totalDevices,valueStyle:{color:'#3f8600',display:'flex',justifyContent:'center'}})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u5728\\u7EBF\\u8BBE\\u5907\",value:stats.onlineDevices// suffix={`/ ${stats.totalDevices}`}\n,valueStyle:{color:'#3f8600',display:'flex',justifyContent:'center'}})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u79BB\\u7EBF\\u8BBE\\u5907\",value:stats.offlineDevices// suffix={`/ ${stats.totalDevices}`}\n,valueStyle:{color:'#cf1322',display:'flex',justifyContent:'center'}})})]})}),/*#__PURE__*/_jsx(InfoCard,{title:\"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u5217\\u8868\",bordered:false,height:\"calc(50% - 95px)\"// extra={\n//   <div>\n//     <Button\n//       size=\"small\"\n//       type=\"link\"\n//       onClick={() => {\n//         console.log('🧪 查看事件缓存状态');\n//         console.log('📊 当前事件缓存状态:', {\n//           缓存事件数: eventListCache.current.length,\n//           事件ID计数器: eventIdCounter.current,\n//           事件列表: eventListCache.current.map(e => {\n//             const timeSinceUpdate = (Date.now() - e.lastUpdateTime) / 1000;\n//             return {\n//               ID: e.eventId,\n//               类型: e.eventType,\n//               更新次数: e.updateCount,\n//               位置: `${e.position.lat.toFixed(6)}, ${e.position.lng.toFixed(6)}`,\n//               未更新时间: `${timeSinceUpdate.toFixed(1)}秒`\n//             };\n//           })\n//         });\n//       }}\n//     >\n//       查看缓存\n//     </Button>\n//     <Button\n//       size=\"small\"\n//       type=\"link\"\n//       onClick={() => {\n//         console.log('🗑️ 手动删除非活跃事件');\n//         removeInactiveEvents();\n//       }}\n//     >\n//       删除非活跃\n//     </Button>\n//     <Button\n//       size=\"small\"\n//       type=\"link\"\n//       onClick={() => {\n//         console.log('🧹 手动清理过期事件');\n//         cleanupExpiredEvents();\n//       }}\n//     >\n//       清理过期\n//     </Button>\n//   </div>\n// }\n,children:renderEventList()}),/*#__PURE__*/_jsx(InfoCard,{title:\"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u7EDF\\u8BA1\",bordered:false,height:\"calc(50% - 95px)\",extra:/*#__PURE__*/_jsx(Select,{value:selectedTimeRange,onChange:handleTimeRangeChange,onSelect:handleTimeRangeChange,size:\"small\",style:{width:80},options:[{value:'1h',label:'1小时'},{value:'24h',label:'1天'},{value:'7d',label:'7天'},{value:'all',label:'全部'}]}),children:/*#__PURE__*/_jsx(\"div\",{ref:eventChartRef,style:{height:'100%',width:'100%'}})})]}),/*#__PURE__*/_jsx(MainContent,{leftCollapsed:leftCollapsed,rightCollapsed:rightCollapsed}),/*#__PURE__*/_jsxs(CollapsibleSidebar,{position:\"right\",collapsed:rightCollapsed,onCollapse:()=>setRightCollapsed(!rightCollapsed),children:[/*#__PURE__*/_jsx(InfoCard,{title:\"\\u8F66\\u8F86\\u5217\\u8868\",bordered:false,height:\"50%\",children:/*#__PURE__*/_jsx(Table,{dataSource:vehicles,columns:vehicleColumns,rowKey:\"id\",pagination:false,size:\"small\",scroll:{y:180},onRow:record=>({onClick:()=>handleVehicleSelect(record),style:{cursor:'pointer',background:(selectedVehicle===null||selectedVehicle===void 0?void 0:selectedVehicle.id)===record.id?'#e6f7ff':'transparent',fontSize:'13px',padding:'4px 8px'}})})}),/*#__PURE__*/_jsx(InfoCard,{title:\"\\u8F66\\u8F86\\u8BE6\\u7EC6\\u4FE1\\u606F\",bordered:false,height:\"50%\",children:selectedVehicle?/*#__PURE__*/_jsxs(Descriptions,{bordered:true,column:1,size:\"small\",styles:{label:{fontSize:'13px',padding:'4px 8px'},content:{fontSize:'13px',padding:'4px 8px'}},children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8F66\\u724C\\u53F7\",children:selectedVehicle.plateNumber}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u72B6\\u6001\",children:/*#__PURE__*/_jsx(Badge,{status:selectedVehicle.status==='online'?'success':'error',text:selectedVehicle.status==='online'?'在线':'离线'})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7ECF\\u5EA6\",children:selectedVehicle.status==='online'?selectedVehicle.lng.toFixed(7):'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7EAC\\u5EA6\",children:selectedVehicle.status==='online'?selectedVehicle.lat.toFixed(7):'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u901F\\u5EA6\",children:selectedVehicle.status==='online'?`${typeof selectedVehicle.speed==='number'?selectedVehicle.speed.toFixed(0):selectedVehicle.speed} km/h`:'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u822A\\u5411\\u89D2\",children:selectedVehicle.status==='online'?`${typeof selectedVehicle.heading==='number'?selectedVehicle.heading.toFixed(2):selectedVehicle.heading}°`:'N/A'})]}):/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'13px'},children:\"\\u8BF7\\u9009\\u62E9\\u8F66\\u8F86\\u67E5\\u770B\\u8BE6\\u7EC6\\u4FE1\\u606F\"})})]})]})});};export default RealTimeTraffic;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "Row", "Col", "Card", "Statistic", "List", "Table", "Descriptions", "Spin", "Badge", "<PERSON><PERSON>", "Select", "echarts", "<PERSON><PERSON><PERSON>", "GridComponent", "TooltipComponent", "LegendComponent", "TitleComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "CollapsibleSidebar", "axios", "vehiclesData", "intersectionsData", "jsx", "_jsx", "jsxs", "_jsxs", "use", "StyledCanvas", "div", "<PERSON><PERSON><PERSON><PERSON>", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "InfoCard", "height", "CompactStatistic", "RealTimeTraffic", "loading", "setLoading", "vehicles", "setVehicles", "events", "setEvents", "selectedVehicle", "setSelectedVehicle", "stats", "setStats", "totalVehicles", "onlineVehicles", "offlineVehicles", "totalDevices", "onlineDevices", "offlineDevices", "onlineBsmIds", "setOnlineBsmIds", "savedOnlineIds", "localStorage", "getItem", "Set", "error", "console", "lastBsmTime", "eventChartRef", "setLeftCollapsed", "setRightCollapsed", "selectedTimeRange", "setSelectedTimeRange", "eventStats", "setEventStats", "prevRsiEvents", "Map", "eventListCache", "eventIdCounter", "storeEventToDatabase", "eventData", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "post", "data", "success", "log", "storage", "eventTypeText", "fetchEventStatsFromDatabase", "timeRange", "arguments", "length", "undefined", "get", "fetchRecentEventsFromDatabase", "limit", "updateVehicleStatus", "bsmId", "status", "speed", "lat", "lng", "heading", "formattedSpeed", "parseFloat", "toFixed", "formattedHeading", "prev", "current", "Date", "now", "newSet", "delete", "prevVehicles", "map", "vehicle", "window", "handleRealBsmReceived", "event", "type", "addEventListener", "removeEventListener", "loadEventsFromDatabase", "recentEvents", "prevStats", "handleTimeRangeChange", "value", "refreshInterval", "setInterval", "clearInterval", "cleanupInterval", "cleanupExpiredEvents", "removeInactiveInterval", "removeInactiveEvents", "fetchVehicles", "apiData", "fetchLatestVehiclesData", "bsmIds", "v", "filter", "id", "updatedVehicles", "isOnline", "has", "plate", "plateNumber", "onlineCount", "totalCount", "vehiclesList", "fetchDeviceStats", "devicesData", "d", "handleBsmMessage", "bsmData", "partSpeed", "partLat", "partLong", "partHeading", "foundVehicle", "find", "checkOnlineStatus", "newOnlineBsmIds", "has<PERSON><PERSON><PERSON>", "for<PERSON>ach", "lastTime", "interval", "resetAllVehicles", "size", "loadData", "handleVehiclesDataChanged", "handleStorageChange", "key", "lastUpdated", "forcedPollingInterval", "returnData", "possibleApiUrls", "succeeded", "processVehiclesData", "message", "fetch", "ok", "json", "e", "newVehicles", "currentIds", "hasNewVehicle", "some", "chart", "handleResize", "lastUpdateTime", "existingChart", "getInstanceByDom", "dispose", "init", "updateChart", "currentTime", "eventTypes", "name", "color", "itemStyle", "item", "sort", "a", "b", "option", "title", "text", "toLocaleTimeString", "left", "top", "textStyle", "fontSize", "grid", "bottom", "right", "containLabel", "animation", "animationDuration", "animationDurationUpdate", "animationEasingUpdate", "tooltip", "trigger", "axisPointer", "xAxis", "show", "splitLine", "yAxis", "axisLabel", "margin", "axisTick", "axisLine", "series", "<PERSON><PERSON><PERSON><PERSON>", "label", "position", "formatter", "borderRadius", "realtimeSort", "animationDelay", "idx", "setOption", "notMerge", "replaceMerge", "statsInterval", "_chart", "resize", "handleRsiMessage", "rsiData", "rtes", "latitude", "posLat", "longitude", "posLong", "rsuId", "mac", "timestamp", "tm", "nonDuplicateEventTypes", "eventType", "latFixed", "lngFixed", "eventKey", "Math", "floor", "pow", "duplicateResult", "checkDuplicateEvent", "isDuplicate", "eventColor", "description", "eventId", "site", "getNearestIntersectionName", "toISOString", "then", "newEvent", "random", "time", "location", "newEvents", "slice", "push", "setTimeout", "getEventThresholds", "timeT<PERSON><PERSON>old", "distanceThreshold", "currentPos", "i", "cachedEvent", "timeDiff", "distance", "calculateDistance", "updateCount", "matchedEvent", "newEventId", "toString", "padStart", "firstDetectedTime", "maxAge", "initialCount", "age", "removedCount", "inactiveThreshold", "removedEvents", "timeSinceLastUpdate", "inactiveTime", "lat1", "lon1", "lat2", "lon2", "R", "dLat", "PI", "dLon", "sin", "cos", "c", "atan2", "sqrt", "intersections", "minDistance", "Infinity", "nearestName", "inter", "interLat", "interLng", "dist", "handleVehicleSelect", "updatedSelectedVehicle", "vehicleColumns", "dataIndex", "width", "render", "renderEventList", "dataSource", "renderItem", "<PERSON><PERSON>", "style", "padding", "children", "Meta", "marginBottom", "marginRight", "maxHeight", "overflowY", "apiPollingInterval", "spinning", "tip", "collapsed", "onCollapse", "bordered", "gutter", "span", "display", "justifyContent", "valueStyle", "extra", "onChange", "onSelect", "options", "ref", "columns", "<PERSON><PERSON><PERSON>", "pagination", "scroll", "y", "onRow", "record", "onClick", "cursor", "background", "column", "styles", "content"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RealTimeTraffic.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge, Button, Select } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { Bar<PERSON>hart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\nimport vehiclesData from '../data/vehicles.json';\nimport intersectionsData from '../data/intersections.json'; // 导入路口数据\n\n// 注册必要的echarts组件\necharts.use([BarChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\n\nconst StyledCanvas = styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 65px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' :\n    props.leftCollapsed ? '0 8px 0 24px' :\n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' :\n    props.leftCollapsed ? '0 8px 0 0' :\n    props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n\n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n\n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n\n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n\n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n\nconst RealTimeTraffic = () => {\n  const [loading, setLoading] = useState(true);\n  const [vehicles, setVehicles] = useState([]);\n  const [events, setEvents] = useState([]);\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\n  const [stats, setStats] = useState({\n    totalVehicles: 0,\n    onlineVehicles: 0,\n    offlineVehicles: 0,\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0\n  });\n\n  // 存储在线车辆的 BSM ID，初始时为空集合，表示所有车辆离线\n  const [onlineBsmIds, setOnlineBsmIds] = useState(() => {\n    try {\n      const savedOnlineIds = localStorage.getItem('realTimeTrafficOnlineIds');\n      // 返回空集合，确保所有车辆初始状态为离线\n      return new Set();\n    } catch (error) {\n      console.error('读取在线ID缓存失败:', error);\n      return new Set();\n    }\n  });\n  // 存储最后一次收到 BSM 消息的时间\n  const lastBsmTime = useRef({});\n\n  const eventChartRef = useRef(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  // 添加时间段选择状态\n  const [selectedTimeRange, setSelectedTimeRange] = useState('24h');\n\n  const [eventStats, setEventStats] = useState({\n    '401': 0,  // 道路抛洒物\n    '404': 0,  // 道路障碍物\n    '405': 0,  // 行人通过马路\n    '904': 0,  // 逆行车辆\n    '910': 0,  // 违停车辆\n    '1002': 0, // 道路施工\n    '901': 0   // 车辆超速\n  });\n\n  // 添加RSI事件缓存，用于检测重复事件\n  const prevRsiEvents = useRef(new Map());\n\n  // 事件列表缓存，存储所有事件的完整信息\n  const eventListCache = useRef([]);\n\n  // 事件ID计数器\n  const eventIdCounter = useRef(1);\n\n  // ========== 数据库API调用函数 ==========\n\n  /**\n   * 存储实时事件到数据库\n   */\n  const storeEventToDatabase = async (eventData) => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.post(`${apiUrl}/api/events/store`, eventData);\n\n      if (response.data && response.data.success) {\n        console.log(`✅ 事件已存储到${response.data.storage}:`, eventData.eventTypeText);\n        return true;\n      } else {\n        console.error('❌ 存储事件失败:', response.data);\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ 存储事件到数据库失败:', error);\n      return false;\n    }\n  };\n\n  /**\n   * 从数据库获取事件统计\n   */\n  const fetchEventStatsFromDatabase = async (timeRange = '24h') => {\n    try {\n      // console.log(`timeRange);\n      console.log('实际的时间段0', timeRange);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/events/stats?timeRange=${timeRange}`);\n\n      if (response.data && response.data.success) {\n        console.log(`📊 从${response.data.storage}获取事件统计(${timeRange}):`, response.data.data);\n        return response.data.data;\n      } else {\n        console.error('❌ 获取事件统计失败:', response.data);\n        return null;\n      }\n    } catch (error) {\n      console.error('❌ 从数据库获取事件统计失败:', error);\n      return null;\n    }\n  };\n\n  /**\n   * 从数据库获取最近事件\n   */\n  const fetchRecentEventsFromDatabase = async (limit = 10) => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/events/recent?limit=${limit}`);\n\n      if (response.data && response.data.success) {\n        console.log(`📋 从${response.data.storage}获取最近事件:`, response.data.data.length, '条');\n        return response.data.data;\n      } else {\n        console.error('❌ 获取最近事件失败:', response.data);\n        return [];\n      }\n    } catch (error) {\n      console.error('❌ 从数据库获取最近事件失败:', error);\n      return [];\n    }\n  };\n\n  // 添加手动更新车辆状态和位置信息的函数\n  const updateVehicleStatus = useCallback((bsmId, status, speed = 0, lat = 0, lng = 0, heading = 0) => {\n    // 确保速度和航向角是格式化的数值，保留两位小数\n    const formattedSpeed = parseFloat(speed).toFixed(0);\n    const formattedHeading = parseFloat(heading).toFixed(2);\n\n    console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${formattedSpeed}, 位置=(${lat}, ${lng})`);\n\n    // 更新在线状态\n    if (status === 'online') {\n      setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n      lastBsmTime.current[bsmId] = Date.now();\n    } else {\n      setOnlineBsmIds(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(bsmId);\n        return newSet;\n      });\n    }\n\n    // 更新车辆信息\n    setVehicles(prevVehicles =>\n      prevVehicles.map(vehicle =>\n        vehicle.bsmId === bsmId\n          ? {\n              ...vehicle,\n              status: status,\n              speed: parseFloat(formattedSpeed), // 确保是数值类型\n              lat: parseFloat(lat.toFixed(7)),\n              lng: parseFloat(lng.toFixed(7)),\n              heading: parseFloat(formattedHeading) // 确保是数值类型\n            }\n          : vehicle\n      )\n    );\n  }, []);\n\n  // 在组件挂载时，暴露updateVehicleStatus函数到window对象\n  useEffect(() => {\n    window.updateVehicleStatus = updateVehicleStatus;\n\n    // 用于监听CampusModel是否接收到BSM消息的事件\n    const handleRealBsmReceived = (event) => {\n      if (event.data && event.data.type === 'realBsmReceived') {\n        // console.log('收到CampusModel发送的真实BSM消息通知');\n      }\n    };\n\n    window.addEventListener('message', handleRealBsmReceived);\n\n    // 确保页面刷新时清空在线车辆状态\n    console.log('组件初始化，重置所有车辆为离线状态');\n    setOnlineBsmIds(new Set());\n    lastBsmTime.current = {};\n\n    return () => {\n      window.removeEventListener('message', handleRealBsmReceived);\n      delete window.updateVehicleStatus;\n    };\n  }, [updateVehicleStatus]);\n\n  // 从数据库加载事件数据\n  const loadEventsFromDatabase = async (timeRange = selectedTimeRange) => {\n    \n    try {\n      // 加载最近事件\n      const recentEvents = await fetchRecentEventsFromDatabase(10);\n      if (recentEvents && recentEvents.length > 0) {\n        setEvents(recentEvents);\n        console.log('✅ 从数据库加载了', recentEvents.length, '条最近事件');\n      }\n\n      // 加载事件统计（使用选择的时间段）\n      console.log('实际的时间段1', timeRange);\n      const stats = await fetchEventStatsFromDatabase(timeRange);\n      if (stats) {\n        setEventStats(prevStats => ({\n          ...prevStats,\n          ...stats\n        }));\n        console.log(`✅ 从数据库加载了事件统计数据(${timeRange})`);\n      }\n    } catch (error) {\n      console.error('❌ 从数据库加载事件数据失败:', error);\n    }\n  };\n\n  // 处理时间段选择变化\n  const handleTimeRangeChange = (value) => {\n    console.log('📅 时间段选择变化:', value);\n    setSelectedTimeRange(value);\n    // 立即重新加载事件统计数据\n    loadEventsFromDatabase(value);\n  };\n\n  // 组件挂载时从数据库加载数据\n  useEffect(() => {\n    loadEventsFromDatabase();\n  }, []);\n\n  // 监听时间段变化，重新加载事件统计数据\n  useEffect(() => {\n    loadEventsFromDatabase(selectedTimeRange);\n  }, [selectedTimeRange]);\n\n  // 定期从数据库刷新数据（每30秒）\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      console.log('🔄 定期刷新事件数据...', '当前时间段:', selectedTimeRange);\n      loadEventsFromDatabase(selectedTimeRange);\n    }, 30000); // 30秒刷新一次\n\n    return () => clearInterval(refreshInterval);\n  }, [selectedTimeRange]); // 添加 selectedTimeRange 作为依赖\n\n  // 定期清理过期事件（每5分钟）\n  useEffect(() => {\n    const cleanupInterval = setInterval(() => {\n      cleanupExpiredEvents();\n    }, 300000); // 5分钟清理一次\n\n    return () => clearInterval(cleanupInterval);\n  }, []);\n\n  // 定期删除1分钟内没有更新的事件（每30秒检查一次）\n  useEffect(() => {\n    const removeInactiveInterval = setInterval(() => {\n      removeInactiveEvents();\n    }, 30000); // 30秒检查一次\n\n    return () => clearInterval(removeInactiveInterval);\n  }, []);\n\n  // 获取车辆数据\n  const fetchVehicles = useCallback(async () => {\n    try {\n      // 先尝试从API获取最新数据\n      console.log('尝试从API获取最新车辆数据');\n      const apiData = await fetchLatestVehiclesData(true);\n\n      // 如果API获取成功，直接使用API数据\n      if (apiData && apiData.length > 0) {\n        console.log('成功从API获取车辆数据，车辆数量:', apiData.length);\n\n        // 获取所有车辆的BSM ID列表\n        const bsmIds = apiData.map(v => v.bsmId).filter(id => id);\n        console.log('所有车辆的BSM ID:', bsmIds);\n\n        // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\n        const updatedVehicles = apiData.map(vehicle => {\n          // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\n          const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);\n\n          return {\n            ...vehicle,\n            plate: vehicle.plateNumber, // 适配表格显示\n            status: isOnline ? 'online' : 'offline', // 只有收到BSM消息的车辆才显示为在线\n            speed: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0) : 0,\n            lat: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0) : 0,\n            lng: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0) : 0,\n            heading: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0) : 0\n          };\n        });\n\n        setVehicles(updatedVehicles);\n\n        // 直接检查更新后的车辆在线状态\n        const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n        const totalCount = updatedVehicles.length;\n\n        console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);\n\n        // 更新车辆统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalVehicles: totalCount,\n          onlineVehicles: onlineCount,\n          offlineVehicles: totalCount - onlineCount\n        }));\n\n        return;\n      }\n\n      // 如果API获取失败，回退到本地JSON文件\n      console.log('API获取失败，回退到本地vehiclesData');\n      const vehiclesList = vehiclesData.vehicles || [];\n      console.log('从vehiclesData获取车辆数据，车辆数量:', vehiclesList.length);\n\n      // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\n      const updatedVehicles = vehiclesList.map(vehicle => {\n        // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\n        const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);\n\n        return {\n          ...vehicle,\n          plate: vehicle.plateNumber, // 适配表格显示\n          status: isOnline ? 'online' : 'offline', // 只有收到BSM消息的车辆才显示为在线\n          speed: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0) : 0,\n          lat: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0) : 0,\n          lng: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0) : 0,\n          heading: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0) : 0\n        };\n      });\n\n      setVehicles(updatedVehicles);\n\n      // 直接检查更新后的车辆在线状态\n      const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n      const totalCount = updatedVehicles.length;\n\n      console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);\n\n      // 更新车辆统计数据\n      setStats(prevStats => ({\n        ...prevStats,\n        totalVehicles: totalCount,\n        onlineVehicles: onlineCount,\n        offlineVehicles: totalCount - onlineCount\n      }));\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n    }\n  }, [onlineBsmIds]);\n\n  // 获取设备统计数据\n  const fetchDeviceStats = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n\n      if (response.data && response.data.success) {\n        const devicesData = response.data.data;\n\n        // 更新设备统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalDevices: devicesData.length,\n          onlineDevices: devicesData.filter(d => d.status === 'online').length,\n          offlineDevices: devicesData.filter(d => d.status === 'offline').length\n        }));\n      }\n    } catch (error) {\n      console.error('获取设备统计数据失败:', error);\n    }\n  };\n\n  // 监听 BSM 消息\n  useEffect(() => {\n    const handleBsmMessage = (event) => {\n      if (event.data && event.data.type === 'bsm') {\n        // 获取bsmId，确保它正确地从消息中提取\n        const bsmData = event.data.data || {};\n        const bsmId = bsmData.bsmId || event.data.bsmId;\n\n        if (!bsmId) {\n          console.error('BSM消息缺少bsmId:', event.data);\n          return;\n        }\n\n        // console.log('收到BSM消息，ID:', bsmId);\n        const now = Date.now();\n\n        // 更新最后接收时间\n        lastBsmTime.current[bsmId] = now;\n\n        // 添加到在线bsmId集合\n        setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n\n        // 提取正确的BSM数据并格式化为两位小数\n        const speed = parseFloat((parseFloat(bsmData.partSpeed || event.data.speed || 0) * 3.6).toFixed(0)); // 转换为km/h，保留两位小数\n        const lat = parseFloat(parseFloat(bsmData.partLat || event.data.lat || 0).toFixed(7));\n        const lng = parseFloat(parseFloat(bsmData.partLong || event.data.lng || 0).toFixed(7));\n        const heading = parseFloat(parseFloat(bsmData.partHeading || event.data.heading || 0).toFixed(2)); // 保留两位小数\n\n        // console.log(`车辆${bsmId}状态: 速度=${speed.toFixed(2)}km/h, 位置=(${lat.toFixed(7)}, ${lng.toFixed(7)}), 航向=${heading.toFixed(2)}°`);\n\n        // 更新车辆状态和位置信息\n        setVehicles(prevVehicles => {\n          // 检查是否找到对应车辆\n          const foundVehicle = prevVehicles.find(v => v.bsmId === bsmId);\n          if (!foundVehicle) {\n            console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);\n            return prevVehicles;\n          }\n\n          const updatedVehicles = prevVehicles.map(vehicle =>\n            vehicle.bsmId === bsmId\n              ? {\n                  ...vehicle,\n                  status: 'online',\n                  speed: speed,\n                  lat: lat,\n                  lng: lng,\n                  heading: heading\n                }\n              : vehicle\n          );\n\n          // 计算更新后的在线车辆数量\n          const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n          const totalCount = updatedVehicles.length;\n\n          // 更新统计数据\n          setStats(prevStats => ({\n            ...prevStats,\n            onlineVehicles: onlineCount,\n            offlineVehicles: totalCount - onlineCount\n          }));\n\n          return updatedVehicles;\n        });\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleBsmMessage);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleBsmMessage);\n    };\n  }, []);\n\n  // 修改检查在线状态的useEffect，同步更新统计信息\n  useEffect(() => {\n    const checkOnlineStatus = () => {\n      const now = Date.now();\n      console.log('检查车辆在线状态...');\n\n      // 只有在特定条件下才将车辆设为离线\n      // 例如，只有收到过BSM消息的车辆才会被检查是否超时\n      setOnlineBsmIds(prev => {\n        const newOnlineBsmIds = new Set(prev);\n        let hasChanges = false;\n\n        // 仅检查已有最后更新时间的车辆\n        prev.forEach(bsmId => {\n          const lastTime = lastBsmTime.current[bsmId];\n\n          // 只有收到过BSM消息的车辆才会被检查是否超时\n          if (lastTime && (now - lastTime > 30000)) {\n            console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);\n            newOnlineBsmIds.delete(bsmId);\n            hasChanges = true;\n          }\n        });\n\n        if (hasChanges) {\n          // 更新车辆状态\n          setVehicles(prevVehicles => {\n            const updatedVehicles = prevVehicles.map(vehicle => {\n              // 只更新有最后更新时间的车辆状态\n              if (lastBsmTime.current[vehicle.bsmId]) {\n                const isOnline = newOnlineBsmIds.has(vehicle.bsmId);\n                return {\n                  ...vehicle,\n                  status: isOnline ? 'online' : 'offline'\n                };\n              }\n              return vehicle;\n            });\n\n            // 计算更新后的在线车辆数量\n            const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n            const totalCount = updatedVehicles.length;\n\n            // 更新统计数据\n            setStats(prevStats => ({\n              ...prevStats,\n              onlineVehicles: onlineCount,\n              offlineVehicles: totalCount - onlineCount\n            }));\n\n            return updatedVehicles;\n          });\n        }\n\n        return newOnlineBsmIds;\n      });\n    };\n\n    const interval = setInterval(checkOnlineStatus, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // 重置所有车辆的初始状态\n  useEffect(() => {\n    // 将所有车辆状态重置为离线\n    const resetAllVehicles = () => {\n      // 只有在没有任何BSM消息的情况下才执行重置\n      if (onlineBsmIds.size === 0) {\n        setVehicles(prevVehicles =>\n          prevVehicles.map(vehicle => ({\n            ...vehicle,\n            status: 'offline',\n            speed: 0,\n            lat: 0,\n            lng: 0,\n            heading: 0\n          }))\n        );\n\n        console.log('已重置所有车辆为离线状态');\n      }\n    };\n\n    // 初始执行一次\n    resetAllVehicles();\n\n    // 然后每30秒检查一次\n    const interval = setInterval(resetAllVehicles, 30000);\n\n    return () => clearInterval(interval);\n  }, [onlineBsmIds]);\n\n  // 在组件挂载时获取数据\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      fetchVehicles();\n      await fetchDeviceStats();\n      setLoading(false);\n    };\n\n    loadData();\n    // // 降低更新频率，避免频繁覆盖状态\n    // const interval = setInterval(loadData, 300000); // 每60x5秒更新一次\n    // return () => clearInterval(interval);\n  }, []);\n\n  // 添加对车辆数据变更的监听\n  useEffect(() => {\n    console.log('设置车辆数据变更监听');\n\n    // 监听自定义事件\n    const handleVehiclesDataChanged = () => {\n      console.log('检测到车辆数据变更事件，重新获取车辆列表');\n      fetchVehicles();\n    };\n\n    // 监听localStorage变化\n    const handleStorageChange = (event) => {\n      if (event.key === 'vehiclesLastUpdated' || event.key === 'vehiclesData') {\n        console.log('检测到localStorage变化，重新获取车辆列表');\n        fetchVehicles();\n      }\n    };\n\n    // 添加事件监听器\n    window.addEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n    window.addEventListener('storage', handleStorageChange);\n\n    // 初始检查是否有更新\n    const lastUpdated = localStorage.getItem('vehiclesLastUpdated');\n    if (lastUpdated) {\n      console.log('初始检查到vehiclesLastUpdated:', lastUpdated);\n      fetchVehicles();\n    }\n\n    // 设置更频繁的强制轮询，确保在部署环境中也能获取最新数据\n    const forcedPollingInterval = setInterval(() => {\n      console.log('强制轮询：重新获取车辆列表');\n      fetchVehicles();\n    }, 10000); // 每10秒强制刷新一次\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n      window.removeEventListener('storage', handleStorageChange);\n      clearInterval(forcedPollingInterval);\n    };\n  }, []);\n\n  // 添加检查本地缓存与API数据是否一致的机制\n  // 定义一个单独的API调用函数，用于获取最新的车辆列表数据\n  const fetchLatestVehiclesData = async (returnData = false) => {\n    try {\n      // 尝试多个可能的API地址\n      const possibleApiUrls = [\n        process.env.REACT_APP_API_URL || 'http://localhost:5000'\n        // 'http://localhost:5000',\n        // window.location.origin, // 当前站点的根URL\n        // `${window.location.origin}/api`, // 当前站点下的/api路径\n        // 'http://localhost:5000/api',\n        // 'http://127.0.0.1:5000',\n        // 'http://127.0.0.1:5000/api'\n      ];\n\n      console.log('尝试从多个API地址获取车辆数据');\n\n      // // 尝试从本地JSON文件直接获取\n      // try {\n      //   console.log('尝试从本地JSON文件获取数据');\n      //   const jsonResponse = await axios.get('/vehicles.json');\n      //   if (jsonResponse.data && jsonResponse.data.vehicles) {\n      //     console.log('成功从本地JSON文件获取数据:', jsonResponse.data.vehicles.length);\n      //     if (returnData) {\n      //       return jsonResponse.data.vehicles;\n      //     }\n      //     processVehiclesData(jsonResponse.data.vehicles);\n      //     return jsonResponse.data.vehicles;\n      //   }\n      // } catch (jsonError) {\n      //   console.log('从本地JSON获取失败:', jsonError.message);\n      // }\n\n      // 逐个尝试API地址\n      let succeeded = false;\n      let vehiclesData = null;\n\n      for (const apiUrl of possibleApiUrls) {\n        if (succeeded) break;\n\n        try {\n          console.log(`尝试从 ${apiUrl}/api/vehicles/list 获取数据`);\n          const response = await axios.get(`${apiUrl}/api/vehicles/list`);\n\n          if (response.data && response.data.vehicles) {\n            console.log(`成功从 ${apiUrl} 获取数据:`, response.data.vehicles.length);\n            vehiclesData = response.data.vehicles;\n\n            if (returnData) {\n              return vehiclesData;\n            }\n\n            processVehiclesData(response.data.vehicles);\n            succeeded = true;\n            break;\n          }\n        } catch (error) {\n          console.log(`从 ${apiUrl} 获取失败:`, error.message);\n          // 继续尝试下一个URL\n        }\n      }\n\n      if (!succeeded && !returnData) {\n        console.log('所有API地址都获取失败，尝试使用vehicles.json');\n        // 尝试从当前页面获取\n        try {\n          const response = await fetch('/vehicles.json');\n          if (response.ok) {\n            const data = await response.json();\n            if (data && data.vehicles) {\n              console.log('从public/vehicles.json获取数据成功:', data.vehicles.length);\n              processVehiclesData(data.vehicles);\n              return data.vehicles;\n            }\n          }\n        } catch (e) {\n          console.error('从public/vehicles.json获取失败:', e);\n        }\n      }\n\n      return vehiclesData || [];\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n      return [];\n    }\n  };\n\n  // 添加处理车辆数据的辅助函数\n  const processVehiclesData = (newVehicles) => {\n    // 与当前列表比较\n    if (vehicles.length !== newVehicles.length) {\n      console.log('检测到车辆数量变化，从', vehicles.length, '到', newVehicles.length);\n      fetchVehicles(); // 重新加载\n      return;\n    }\n\n    // 检查是否有新车辆ID\n    const currentIds = new Set(vehicles.map(v => v.id));\n    const hasNewVehicle = newVehicles.some(v => !currentIds.has(v.id));\n\n    if (hasNewVehicle) {\n      console.log('检测到新增车辆');\n      fetchVehicles(); // 重新加载\n    }\n  };\n\n  // 添加自动选择第一个车辆的逻辑\n  useEffect(() => {\n    // 当车辆列表加载完成且有车辆数据时\n    if (vehicles.length > 0 && !selectedVehicle) {\n      // 自动选择第一个车辆\n      setSelectedVehicle(vehicles[0]);\n      console.log('已自动选择第一个车辆:', vehicles[0].plateNumber);\n    }\n  }, [vehicles, selectedVehicle]);\n\n  // 修改图表初始化代码\n  useEffect(() => {\n    let chart = null;\n    let handleResize = null;\n    let lastUpdateTime = new Date();\n\n    // 确保 DOM 元素存在\n    if (!eventChartRef.current) {\n      console.error('图表容器未找到');\n      return;\n    }\n\n    // 检查并清理已存在的图表实例\n    let existingChart = echarts.getInstanceByDom(eventChartRef.current);\n    if (existingChart) {\n      existingChart.dispose();\n    }\n\n    try {\n      // 初始化新的图表实例\n      chart = echarts.init(eventChartRef.current);\n\n      // 设置图表配置\n      const updateChart = () => {\n        const currentTime = new Date();\n        lastUpdateTime = currentTime;\n\n        // 事件类型配置\n        // const eventTypes = [\n        //   { type: '401', name: '道路抛洒物', color: '#ff4d4f' },\n        //   { type: '404', name: '道路障碍物', color: '#faad14' },\n        //   { type: '405', name: '行人通过马路', color: '#1890ff' },\n        //   { type: '904', name: '逆行车辆', color: '#f5222d' },\n        //   { type: '910', name: '违停车辆', color: '#722ed1' },\n        //   { type: '1002', name: '道路施工', color: '#fa8c16' },\n        //   { type: '901', name: '车辆超速', color: '#eb2f96' }\n        // ];\n        const eventTypes = [\n          { type: '401', name: '道路抛洒物', color: '#faad14' },\n          { type: '404', name: '道路障碍物', color: '#ff7a45' },\n          { type: '405', name: '行人通过马路', color: '#52c41a' },\n          { type: '904', name: '逆行车辆', color: '#f5222d' },\n          { type: '910', name: '违停车辆', color: '#ff4d4f' },\n          { type: '1002', name: '道路施工', color: '#1890ff' },\n          { type: '901', name: '车辆超速', color: '#fa8c16' }\n        ];\n\n        // 处理数据\n        const data = eventTypes\n          .map(event => ({\n            value: eventStats[event.type] || 0,\n            name: event.name,\n            itemStyle: { color: event.color }\n          }))\n          .filter(item => item.value > 0)\n          .sort((a, b) => b.value - a.value);\n\n        const option = {\n          title: {\n            text: `最后更新: ${currentTime.toLocaleTimeString()}`,\n            left: 'center',\n            top: -3,\n            textStyle: {\n              fontSize: 12,\n              color: '#999'\n            }\n          },\n          grid: {\n            top: 30,\n            bottom: 0,\n            left: 0,\n            right: 50,\n            containLabel: true\n          },\n          animation: true,\n          animationDuration: 0,\n          animationDurationUpdate: 1000,\n          animationEasingUpdate: 'quinticInOut',\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'shadow'\n            }\n          },\n          xAxis: {\n            type: 'value',\n            show: false,\n            splitLine: { show: false }\n          },\n          yAxis: {\n            type: 'category',\n            data: data.map(item => item.name),\n            axisLabel: {\n              fontSize: 12,\n              color: '#666',\n              margin: 8\n            },\n            axisTick: { show: false },\n            axisLine: { show: false }\n          },\n          series: [{\n            type: 'bar',\n            data: data,\n            barWidth: '50%',\n            label: {\n              show: true,\n              position: 'right',\n              formatter: '{c}次',\n              fontSize: 12,\n              color: '#666'\n            },\n            itemStyle: {\n              borderRadius: [0, 4, 4, 0]\n            },\n            realtimeSort: false,\n            animationDelay: function (idx) {\n              return idx * 100;\n            }\n          }]\n        };\n\n        // 使用 notMerge: false 来保持增量更新\n        chart.setOption(option, {\n          notMerge: false,\n          replaceMerge: ['series']\n        });\n      };\n\n      // 初始更新\n      updateChart();\n\n      // 监听事件统计变化，每分钟更新一次\n      const statsInterval = setInterval(updateChart, 60000); // 60000ms = 1分钟\n\n      // 监听窗口大小变化\n      handleResize = () => {\n        chart?.resize();\n      };\n      window.addEventListener('resize', handleResize);\n\n      // 清理函数\n      return () => {\n        clearInterval(statsInterval);\n        if (handleResize) {\n          window.removeEventListener('resize', handleResize);\n        }\n        if (chart) {\n          chart.dispose();\n        }\n      };\n\n    } catch (error) {\n      console.error('初始化图表失败:', error);\n    }\n  }, [eventStats]);\n\n  // 修改 RSI 消息处理逻辑\n  useEffect(() => {\n    const handleRsiMessage = (event) => {\n      try {\n        if (event.data && event.data.type === 'RSI') {\n          const rsiData = event.data.data;\n          if (!rsiData || !rsiData.rtes) return;\n          // if(rsiData.rtes.length > 0){\n          //   // console.log('RealTimeTraffic 收到 RSI 消息:', event.data);\n          //   if(parseFloat(rsiData.posLong) < 113.0){\n          //     console.log('位置不在测试范围的 RSI 消息:', event.data);\n          //     console.log('收到RSI消息，但位置不在测试范围内，忽略');\n          //     return;\n          //   }\n          // }\n\n          const latitude = parseFloat(rsiData.posLat);\n          const longitude = parseFloat(rsiData.posLong);\n          const rsuId = rsiData.rsuId;\n          const mac = event.data.mac || '';\n          const timestamp = event.data.tm || Date.now();\n\n          // 统计本帧所有非重复事件类型\n          const nonDuplicateEventTypes = [];\n          rsiData.rtes.forEach(event => {\n            const eventType = event.eventType;\n\n            // 根据事件类型设置不同的位置精度和去重策略\n            let latFixed = '';\n            let lngFixed = '';\n            let eventKey = '';\n\n            if(eventType === '904' || eventType === '901'){\n              // 逆行和超速：使用3位小数精度（约111米范围）\n              latFixed = Math.floor(latitude * Math.pow(10,3))/Math.pow(10,3);\n              lngFixed = Math.floor(longitude* Math.pow(10,3))/Math.pow(10,3);\n              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;\n            }\n            else if(eventType === '910'){\n              // 违停车辆：使用2位小数精度（约1.1公里范围），忽略MAC地址\n              // 这样可以将相近位置的违停事件归为同一类\n              latFixed = Math.floor(latitude * Math.pow(10,4))/Math.pow(10,4);\n              lngFixed = Math.floor(longitude* Math.pow(10,4))/Math.pow(10,4);\n              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;\n            }\n            else{\n              // 其他事件：使用4位小数精度（约11米范围）\n              latFixed = Math.floor(latitude * Math.pow(10,4))/Math.pow(10,4);\n              lngFixed = Math.floor(longitude* Math.pow(10,4))/Math.pow(10,4);\n              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;\n            }\n\n            const duplicateResult = checkDuplicateEvent(\n              eventType,\n              eventKey,\n              timestamp,\n              { lat: latitude, lng: longitude }\n            );\n\n            const isDuplicate = duplicateResult.isDuplicate;\n            // 获取事件类型的中文描述\n            let eventTypeText = '';\n            let eventColor = '';\n            switch(eventType) {\n              case '401': eventTypeText = '道路抛洒物'; eventColor = '#faad14'; break;\n              case '404': eventTypeText = '道路障碍物'; eventColor = '#ff7a45'; break;\n              case '405': eventTypeText = '行人通过马路'; eventColor = '#52c41a'; break;\n              case '904': eventTypeText = '逆行车辆'; eventColor = '#f5222d'; break;\n              case '910': eventTypeText = '违停车辆'; eventColor = '#ff4d4f'; break;\n              case '1002': eventTypeText = '道路施工'; eventColor = '#1890ff'; break;\n              case '901': eventTypeText = '车辆超速'; eventColor = '#fa8c16'; break;\n              default: eventTypeText = event.description || '未知事件'; eventColor = '#8c8c8c';\n            }\n            // 更新事件列表\n            // const newEvent = {\n            //   key: Date.now() + Math.random(),\n            //   type: eventTypeText,\n            //   time: new Date().toLocaleTimeString(),\n            //   vehicle: rsiData.rsuId || '未知设备',\n            //   color: eventColor,\n            //   eventType: eventType,\n            //   location: {\n            //     latitude: latitude,\n            //     longitude: longitude\n            //   }\n\n            // };\n            // setEvents(prev => {\n            //   const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n            //   return newEvents;\n            // });\n            if (!isDuplicate) {\n              // 创建事件数据，使用新的事件ID\n              const eventData = {\n                eventId: duplicateResult.eventId,\n                eventType: eventType,\n                eventTypeText: eventTypeText,\n                rsuId: rsiData.rsuId || '未知设备',\n                mac: mac,\n                latitude: latitude,\n                longitude: longitude,\n                site: getNearestIntersectionName(latitude, longitude),\n                eventKey: eventKey,\n                color: eventColor,\n                timestamp: new Date().toISOString()\n              };\n\n              // 存储到数据库\n              storeEventToDatabase(eventData).then(success => {\n                if (success) {\n                  console.log(`✅ 事件已存储到数据库: ${eventTypeText}`);\n\n                  // 更新本地事件列表（用于实时显示）\n                  const newEvent = {\n                    key: Date.now() + Math.random(),\n                    type: eventTypeText,\n                    time: new Date().toLocaleTimeString(),\n                    rsuId: rsiData.rsuId || '未知设备',\n                    color: eventColor,\n                    eventType: eventType,\n                    location: {\n                      latitude: latitude,\n                      longitude: longitude\n                    },\n                    site: eventData.site\n                  };\n\n                  setEvents(prev => {\n                    const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n                    return newEvents;\n                  });\n                } else {\n                  console.error(`❌ 事件存储失败: ${eventTypeText}`);\n                }\n              });\n\n              nonDuplicateEventTypes.push(eventType);\n            } else {\n              // console.log(`检测到重复事件，不累计统计: ${eventTypeText}`);\n            }\n          });\n\n          // 如果有新事件，从数据库刷新统计数据\n          if (nonDuplicateEventTypes.length > 0) {\n            console.log(`📊 检测到 ${nonDuplicateEventTypes.length} 个新事件，刷新统计数据`);\n\n            // 延迟1秒后从数据库获取最新统计，确保数据已存储\n            console.log('实际的时间段2', selectedTimeRange);\n            setTimeout(async () => {\n              const stats = await fetchEventStatsFromDatabase(selectedTimeRange);\n              if (stats) {\n                setEventStats(prevStats => ({\n                  ...prevStats,\n                  ...stats\n                }));\n                console.log('✅ 事件统计数据已从数据库更新，时间段:', selectedTimeRange);\n              }\n            }, 1000);\n          }\n        }\n      } catch (error) {\n        console.error('处理 RSI 消息失败:', error);\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleRsiMessage);\n\n    return () => {\n      window.removeEventListener('message', handleRsiMessage);\n    };\n  }, []);\n\n  // 获取事件类型的阈值配置\n  const getEventThresholds = (eventType) => {\n    switch(eventType) {\n      case '910': // 违停车辆\n        return { timeThreshold: 300000, distanceThreshold: 10 }; // 5分钟, 20米\n      case '904': // 逆行车辆\n        return { timeThreshold: 10000, distanceThreshold: 20 }; // 10秒, 20米\n      case '901': // 车辆超速\n        return { timeThreshold: 30000, distanceThreshold: 20 }; // 30秒, 50米\n      case '401': // 道路抛洒物\n      case '404': // 道路障碍物\n      case '1002': // 道路施工\n        return { timeThreshold: 600000, distanceThreshold: 5 }; // 10分钟, 30米\n      case '405': // 行人通过马路\n        return { timeThreshold: 10000, distanceThreshold: 10 }; // 10秒, 10米\n      default:\n        return { timeThreshold: 5000, distanceThreshold: 5 }; // 5秒, 5米\n    }\n  };\n\n  // 优化后的事件重复检查逻辑\n  const checkDuplicateEvent = (eventType, eventKey, currentTime, currentPos) => {\n    const { timeThreshold, distanceThreshold } = getEventThresholds(eventType);\n\n    // 遍历事件列表缓存中的所有事件\n    for (let i = 0; i < eventListCache.current.length; i++) {\n      const cachedEvent = eventListCache.current[i];\n\n      // 检查事件类型是否相同\n      if (cachedEvent.eventType !== eventType) {\n        continue;\n      }\n\n      // 计算时间差\n      const timeDiff = currentTime - cachedEvent.lastUpdateTime;\n\n      // 检查时间差是否在阈值内\n      if (timeDiff > timeThreshold) {\n        continue;\n      }\n\n      // 计算距离\n      const distance = calculateDistance(\n        currentPos.lat, currentPos.lng,\n        cachedEvent.position.lat, cachedEvent.position.lng\n      );\n\n      // 检查距离是否在阈值内\n      if (distance <= distanceThreshold) {\n        // 找到匹配的事件，更新信息\n        cachedEvent.eventKey = eventKey;\n        cachedEvent.lastUpdateTime = currentTime;\n        cachedEvent.position = { ...currentPos };\n        cachedEvent.updateCount = (cachedEvent.updateCount || 1) + 1;\n\n        // console.log(`🔄 检测到重复事件 ${eventType} (ID: ${cachedEvent.eventId})，时间差: ${(timeDiff/1000).toFixed(1)}s，距离: ${distance.toFixed(1)}m，更新次数: ${cachedEvent.updateCount}`);\n\n        return {\n          isDuplicate: true,\n          eventId: cachedEvent.eventId,\n          matchedEvent: cachedEvent\n        };\n      }\n    }\n\n    // 没有找到匹配的事件，创建新事件\n    const newEventId = `EVT_${eventIdCounter.current.toString().padStart(6, '0')}`;\n    eventIdCounter.current++;\n\n    const newEvent = {\n      eventId: newEventId,\n      eventType: eventType,\n      eventKey: eventKey,\n      firstDetectedTime: currentTime,\n      lastUpdateTime: currentTime,\n      position: { ...currentPos },\n      updateCount: 1\n    };\n\n    // 添加到事件列表缓存\n    eventListCache.current.push(newEvent);\n\n    console.log(`✨ 创建新事件 ${eventType} (ID: ${newEventId})，位置: (${currentPos.lat.toFixed(6)}, ${currentPos.lng.toFixed(6)})`);\n\n    return {\n      isDuplicate: false,\n      eventId: newEventId,\n      newEvent: newEvent\n    };\n  };\n\n  // 清理过期事件的函数\n  const cleanupExpiredEvents = () => {\n    const currentTime = Date.now();\n    const maxAge = 3600000; // 1小时\n\n    const initialCount = eventListCache.current.length;\n    eventListCache.current = eventListCache.current.filter(event => {\n      const age = currentTime - event.lastUpdateTime;\n      return age <= maxAge;\n    });\n\n    const removedCount = initialCount - eventListCache.current.length;\n    if (removedCount > 0) {\n      console.log(`🧹 清理了 ${removedCount} 个过期事件，当前缓存事件数: ${eventListCache.current.length}`);\n    }\n  };\n\n  // 删除1分钟内没有更新的事件\n  const removeInactiveEvents = () => {\n    const currentTime = Date.now();\n    const inactiveThreshold = 60000; // 1分钟\n\n    const initialCount = eventListCache.current.length;\n    const removedEvents = [];\n\n    eventListCache.current = eventListCache.current.filter(event => {\n      const timeSinceLastUpdate = currentTime - event.lastUpdateTime;\n      if (timeSinceLastUpdate > inactiveThreshold) {\n        removedEvents.push({\n          id: event.eventId,\n          type: event.eventType,\n          inactiveTime: (timeSinceLastUpdate / 1000).toFixed(1)\n        });\n        return false; // 删除该事件\n      }\n      return true; // 保留该事件\n    });\n\n    const removedCount = initialCount - eventListCache.current.length;\n    if (removedCount > 0) {\n      console.log(`🗑️ 删除了 ${removedCount} 个1分钟内未更新的事件:`);\n      removedEvents.forEach(event => {\n        console.log(`   - 事件 ${event.id} (类型: ${event.type})，未更新时间: ${event.inactiveTime}秒`);\n      });\n      console.log(`📊 当前缓存事件数: ${eventListCache.current.length}`);\n    }\n  };\n\n  // 添加计算两点之间距离的函数\n  const calculateDistance = (lat1, lon1, lat2, lon2) => {\n    if (lat1 === 0 || lon1 === 0 || lat2 === 0 || lon2 === 0) {\n      return 999;\n    }\n    const R = 6371000;\n    const dLat = (lat2 - lat1) * Math.PI / 180;\n    const dLon = (lon2 - lon1) * Math.PI / 180;\n    const a =\n      Math.sin(dLat/2) * Math.sin(dLat/2) +\n      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *\n      Math.sin(dLon/2) * Math.sin(dLon/2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n    return R * c;\n  };\n\n  // 根据坐标查找最近的路口名称\n  const getNearestIntersectionName = (lat, lng) => {\n    // 从 intersections.json 获取所有路口\n    const intersections = intersectionsData.intersections || [];\n    let minDistance = Infinity;\n    let nearestName = '未知地点';\n    intersections.forEach(inter => {\n      const interLat = parseFloat(inter.latitude);\n      const interLng = parseFloat(inter.longitude);\n      const dist = calculateDistance(lat, lng, interLat, interLng);\n      if (dist < minDistance) {\n        minDistance = dist;\n        nearestName = inter.name;\n      }\n    });\n    return nearestName;\n  };\n\n  // 处理车辆选择\n  const handleVehicleSelect = (vehicle) => {\n    console.log('选择车辆:', vehicle.plateNumber, '状态:', vehicle.status);\n    setSelectedVehicle(vehicle);\n  };\n\n  // 修改车辆状态更新逻辑，确保同步更新selectedVehicle\n  useEffect(() => {\n    // 当车辆列表更新后，如果当前有选中的车辆，则更新选中的车辆信息\n    if (selectedVehicle) {\n      const updatedSelectedVehicle = vehicles.find(v => v.id === selectedVehicle.id);\n      if (updatedSelectedVehicle &&\n          (updatedSelectedVehicle.status !== selectedVehicle.status ||\n           updatedSelectedVehicle.speed !== selectedVehicle.speed ||\n           updatedSelectedVehicle.lat !== selectedVehicle.lat ||\n           updatedSelectedVehicle.lng !== selectedVehicle.lng ||\n           updatedSelectedVehicle.heading !== selectedVehicle.heading)) {\n        console.log(`更新选中车辆 ${selectedVehicle.plateNumber} 的信息:`,\n                   `状态从 ${selectedVehicle.status} 变为 ${updatedSelectedVehicle.status}`);\n        setSelectedVehicle(updatedSelectedVehicle);\n      }\n    }\n  }, [vehicles, selectedVehicle]);\n\n  // 车辆列表列定义\n  const vehicleColumns = [\n    {\n      title: '车牌号',\n      dataIndex: 'plate',\n      key: 'plate',\n      width: '40%',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: '30%',\n      render: status => (\n        <Badge\n          status={status === 'online' ? 'success' : 'error'}\n          text={status === 'online' ? '在线' : '离线'}\n        />\n      ),\n    },\n    {\n      title: '速度',\n      dataIndex: 'speed',\n      key: 'speed',\n      width: '30%',\n      render: speed => `${typeof speed === 'number' ? speed.toFixed(0) : speed} km/h`,\n    }\n  ];\n\n  // 修改实时事件列表的渲染\n  const renderEventList = () => (\n    <List\n      size=\"small\"\n      dataSource={events}\n      renderItem={item => (\n        <List.Item style={{ padding: '8px 0' }}>\n          <List.Item.Meta\n            title={\n              <div style={{ fontSize: '13px', marginBottom: '4px' }}>\n                <span style={{ color: item.color, marginRight: '8px' }}>\n                  {item.type}\n                </span>\n                <span style={{ color: '#666', fontSize: '12px' }}>\n                  {item.time}\n                </span>\n              </div>\n            }\n            description={\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                {/* 显示地点（site） */}\n                <div>地点: {item.site || '未知地点'}</div>\n                <div>位置: {item.location ?\n                  `${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}` :\n                  '未知位置'}\n                </div>\n              </div>\n            }\n          />\n        </List.Item>\n      )}\n      style={{\n        maxHeight: 'calc(100% - 24px)',\n        overflowY: 'auto'\n      }}\n    />\n  );\n\n  // 添加强制定期从API获取最新数据的机制\n  useEffect(() => {\n    const apiPollingInterval = setInterval(() => {\n      console.log('直接从API检查车辆数据更新');\n      fetchLatestVehiclesData();\n    }, 15000); // 每15秒检查一次API\n\n    return () => clearInterval(apiPollingInterval);\n  }, [vehicles]);\n\n  return (\n    <Spin spinning={loading} tip=\"加载中...\">\n      <PageContainer>\n        {/* 左侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"left\"\n          collapsed={leftCollapsed}\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\n        >\n          {/* 车辆和设备总数信息栏 */}\n          <InfoCard title=\"车辆和设备统计\" bordered={false} height=\"160px\">\n            <Row gutter={[8, 1]} >\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"车辆总数\"\n                  value={stats.totalVehicles}\n                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }}\n                  // Style={{}}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"在线车辆\"\n                  value={stats.onlineVehicles}\n                  // suffix={`/ ${stats.totalVehicles}`}\n                  valueStyle={{ color: '#3f8600', display: 'flex',justifyContent: 'center'}}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"离线车辆\"\n                  value={stats.offlineVehicles}\n                  // suffix={`/ ${stats.totalVehicles}`}\n                  valueStyle={{ color: '#cf1322' ,display: 'flex',justifyContent: 'center' }}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"设备总数\"\n                  value={stats.totalDevices}\n                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"在线设备\"\n                  value={stats.onlineDevices}\n                  // suffix={`/ ${stats.totalDevices}`}\n                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"离线设备\"\n                  value={stats.offlineDevices}\n                  // suffix={`/ ${stats.totalDevices}`}\n                  valueStyle={{ color: '#cf1322',display: 'flex',justifyContent: 'center' }}\n                />\n              </Col>\n            </Row>\n          </InfoCard>\n\n          {/* 实时事件列表栏 */}\n          <InfoCard\n            title=\"实时事件列表\"\n            bordered={false}\n            height=\"calc(50% - 95px)\"\n            // extra={\n            //   <div>\n            //     <Button\n            //       size=\"small\"\n            //       type=\"link\"\n            //       onClick={() => {\n            //         console.log('🧪 查看事件缓存状态');\n            //         console.log('📊 当前事件缓存状态:', {\n            //           缓存事件数: eventListCache.current.length,\n            //           事件ID计数器: eventIdCounter.current,\n            //           事件列表: eventListCache.current.map(e => {\n            //             const timeSinceUpdate = (Date.now() - e.lastUpdateTime) / 1000;\n            //             return {\n            //               ID: e.eventId,\n            //               类型: e.eventType,\n            //               更新次数: e.updateCount,\n            //               位置: `${e.position.lat.toFixed(6)}, ${e.position.lng.toFixed(6)}`,\n            //               未更新时间: `${timeSinceUpdate.toFixed(1)}秒`\n            //             };\n            //           })\n            //         });\n            //       }}\n            //     >\n            //       查看缓存\n            //     </Button>\n            //     <Button\n            //       size=\"small\"\n            //       type=\"link\"\n            //       onClick={() => {\n            //         console.log('🗑️ 手动删除非活跃事件');\n            //         removeInactiveEvents();\n            //       }}\n            //     >\n            //       删除非活跃\n            //     </Button>\n            //     <Button\n            //       size=\"small\"\n            //       type=\"link\"\n            //       onClick={() => {\n            //         console.log('🧹 手动清理过期事件');\n            //         cleanupExpiredEvents();\n            //       }}\n            //     >\n            //       清理过期\n            //     </Button>\n            //   </div>\n            // }\n          >\n            {renderEventList()}\n          </InfoCard>\n\n          {/* 实时事件统计栏 */}\n          <InfoCard\n            title=\"实时事件统计\"\n            bordered={false}\n            height=\"calc(50% - 95px)\"\n            extra={\n              <Select\n                value={selectedTimeRange}\n                onChange={handleTimeRangeChange}\n                onSelect={handleTimeRangeChange}\n                size=\"small\"\n                style={{ width: 80 }}\n                options={[\n                  { value: '1h', label: '1小时' },\n                  { value: '24h', label: '1天' },\n                  { value: '7d', label: '7天' },\n                  { value: 'all', label: '全部' }\n                ]}\n              />\n            }\n          >\n            <div ref={eventChartRef} style={{ height: '100%', width: '100%' }}></div>\n          </InfoCard>\n        </CollapsibleSidebar>\n\n        {/* 主内容区域 */}\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\n          {/* 主要内容 */}\n        </MainContent>\n\n        {/* 右侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"right\"\n          collapsed={rightCollapsed}\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\n        >\n          {/* 车辆列表栏 */}\n          <InfoCard title=\"车辆列表\" bordered={false} height=\"50%\">\n            <Table\n              dataSource={vehicles}\n              columns={vehicleColumns}\n              rowKey=\"id\"\n              pagination={false}\n              size=\"small\"\n              scroll={{ y: 180 }}\n              onRow={(record) => ({\n                onClick: () => handleVehicleSelect(record),\n                style: {\n                  cursor: 'pointer',\n                  background: selectedVehicle?.id === record.id ? '#e6f7ff' : 'transparent',\n                  fontSize: '13px',\n                  padding: '4px 8px'\n                }\n              })}\n            />\n          </InfoCard>\n\n          {/* 车辆详细信息栏 */}\n          <InfoCard title=\"车辆详细信息\" bordered={false} height=\"50%\">\n            {selectedVehicle ? (\n              <Descriptions\n                bordered\n                column={1}\n                size=\"small\"\n                styles={{\n                  label: { fontSize: '13px', padding: '4px 8px' },\n                  content: { fontSize: '13px', padding: '4px 8px' }\n                }}\n              >\n                <Descriptions.Item label=\"车牌号\">{selectedVehicle.plateNumber}</Descriptions.Item>\n                <Descriptions.Item label=\"状态\">\n                  <Badge\n                    status={selectedVehicle.status === 'online' ? 'success' : 'error'}\n                    text={selectedVehicle.status === 'online' ? '在线' : '离线'}\n                  />\n                </Descriptions.Item>\n                <Descriptions.Item label=\"经度\">\n                  {selectedVehicle.status === 'online' ? selectedVehicle.lng.toFixed(7) : 'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"纬度\">\n                  {selectedVehicle.status === 'online' ? selectedVehicle.lat.toFixed(7) : 'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"速度\">\n                  {selectedVehicle.status === 'online' ?\n                    `${typeof selectedVehicle.speed === 'number' ? selectedVehicle.speed.toFixed(0) : selectedVehicle.speed} km/h` :\n                    'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"航向角\">\n                  {selectedVehicle.status === 'online' ?\n                    `${typeof selectedVehicle.heading === 'number' ? selectedVehicle.heading.toFixed(2) : selectedVehicle.heading}°` :\n                    'N/A'}\n                </Descriptions.Item>\n              </Descriptions>\n            ) : (\n              <p style={{ fontSize: '13px' }}>请选择车辆查看详细信息</p>\n            )}\n          </InfoCard>\n        </CollapsibleSidebar>\n      </PageContainer>\n    </Spin>\n  );\n};\n\nexport default RealTimeTraffic;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,OAASC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,SAAS,CAAEC,IAAI,CAAEC,KAAK,CAAEC,YAAY,CAAEC,IAAI,CAAEC,KAAK,CAAEC,MAAM,CAAEC,MAAM,KAAQ,MAAM,CACxG,MAAO,GAAK,CAAAC,OAAO,KAAM,cAAc,CACvC,OAASC,QAAQ,KAAQ,gBAAgB,CACzC,OAASC,aAAa,CAAEC,gBAAgB,CAAEC,eAAe,CAAEC,cAAc,KAAQ,oBAAoB,CACrG,OAASC,cAAc,KAAQ,mBAAmB,CAClD,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,kBAAkB,KAAM,yCAAyC,CACxE,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,YAAY,KAAM,uBAAuB,CAChD,MAAO,CAAAC,iBAAiB,KAAM,4BAA4B,CAAE;AAE5D;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACAf,OAAO,CAACgB,GAAG,CAAC,CAACf,QAAQ,CAAEC,aAAa,CAAEC,gBAAgB,CAAEC,eAAe,CAAEC,cAAc,CAAEC,cAAc,CAAC,CAAC,CAEzG,KAAM,CAAAW,YAAY,CAAGV,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAGZ,MAAM,CAACW,GAAG;AAChC;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAE,WAAW,CAAGb,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAG,YAAY,CAAGd,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAI,WAAW,CAAGf,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA,aAAaK,KAAK,EAAIA,KAAK,CAACC,aAAa,EAAID,KAAK,CAACE,cAAc,CAAG,QAAQ,CACxEF,KAAK,CAACC,aAAa,CAAG,cAAc,CACpCD,KAAK,CAACE,cAAc,CAAG,cAAc,CAAG,OAAO;AACnD;AACA,YAAYF,KAAK,EAAIA,KAAK,CAACC,aAAa,EAAID,KAAK,CAACE,cAAc,CAAG,QAAQ,CACvEF,KAAK,CAACC,aAAa,CAAG,WAAW,CACjCD,KAAK,CAACE,cAAc,CAAG,WAAW,CAAG,GAAG;AAC5C;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,QAAQ,CAAGnB,MAAM,CAAChB,IAAI,CAAC;AAC7B;AACA,YAAYgC,KAAK,EAAIA,KAAK,CAACI,MAAM,EAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAGrB,MAAM,CAACf,SAAS,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAqC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG9C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC+C,QAAQ,CAAEC,WAAW,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACiD,MAAM,CAAEC,SAAS,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACmD,eAAe,CAAEC,kBAAkB,CAAC,CAAGpD,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAACqD,KAAK,CAAEC,QAAQ,CAAC,CAAGtD,QAAQ,CAAC,CACjCuD,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAAC,CACjBC,eAAe,CAAE,CAAC,CAClBC,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAClB,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG9D,QAAQ,CAAC,IAAM,CACrD,GAAI,CACF,KAAM,CAAA+D,cAAc,CAAGC,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC,CACvE;AACA,MAAO,IAAI,CAAAC,GAAG,CAAC,CAAC,CAClB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnC,MAAO,IAAI,CAAAD,GAAG,CAAC,CAAC,CAClB,CACF,CAAC,CAAC,CACF;AACA,KAAM,CAAAG,WAAW,CAAGnE,MAAM,CAAC,CAAC,CAAC,CAAC,CAE9B,KAAM,CAAAoE,aAAa,CAAGpE,MAAM,CAAC,IAAI,CAAC,CAElC;AACA,KAAM,CAACqC,aAAa,CAAEgC,gBAAgB,CAAC,CAAGvE,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACwC,cAAc,CAAEgC,iBAAiB,CAAC,CAAGxE,QAAQ,CAAC,KAAK,CAAC,CAE3D;AACA,KAAM,CAACyE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1E,QAAQ,CAAC,KAAK,CAAC,CAEjE,KAAM,CAAC2E,UAAU,CAAEC,aAAa,CAAC,CAAG5E,QAAQ,CAAC,CAC3C,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,MAAM,CAAE,CAAC,CAAE;AACX,KAAK,CAAE,CAAI;AACb,CAAC,CAAC,CAEF;AACA,KAAM,CAAA6E,aAAa,CAAG3E,MAAM,CAAC,GAAI,CAAA4E,GAAG,CAAC,CAAC,CAAC,CAEvC;AACA,KAAM,CAAAC,cAAc,CAAG7E,MAAM,CAAC,EAAE,CAAC,CAEjC;AACA,KAAM,CAAA8E,cAAc,CAAG9E,MAAM,CAAC,CAAC,CAAC,CAEhC;AAEA;AACF;AACA,KACE,KAAM,CAAA+E,oBAAoB,CAAG,KAAO,CAAAC,SAAS,EAAK,CAChD,GAAI,CACF,KAAM,CAAAC,MAAM,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACvE,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA/D,KAAK,CAACgE,IAAI,CAAC,GAAGL,MAAM,mBAAmB,CAAED,SAAS,CAAC,CAE1E,GAAIK,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CAC1CtB,OAAO,CAACuB,GAAG,CAAC,WAAWJ,QAAQ,CAACE,IAAI,CAACG,OAAO,GAAG,CAAEV,SAAS,CAACW,aAAa,CAAC,CACzE,MAAO,KAAI,CACb,CAAC,IAAM,CACLzB,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEoB,QAAQ,CAACE,IAAI,CAAC,CACzC,MAAO,MAAK,CACd,CACF,CAAE,MAAOtB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrC,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACF;AACA,KACE,KAAM,CAAA2B,2BAA2B,CAAG,cAAAA,CAAA,CAA6B,IAAtB,CAAAC,SAAS,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC1D,GAAI,CACF;AACA5B,OAAO,CAACuB,GAAG,CAAC,SAAS,CAAEI,SAAS,CAAC,CACjC,KAAM,CAAAZ,MAAM,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACvE,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA/D,KAAK,CAAC2E,GAAG,CAAC,GAAGhB,MAAM,+BAA+BY,SAAS,EAAE,CAAC,CAErF,GAAIR,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CAC1CtB,OAAO,CAACuB,GAAG,CAAC,OAAOJ,QAAQ,CAACE,IAAI,CAACG,OAAO,UAAUG,SAAS,IAAI,CAAER,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC,CACpF,MAAO,CAAAF,QAAQ,CAACE,IAAI,CAACA,IAAI,CAC3B,CAAC,IAAM,CACLrB,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEoB,QAAQ,CAACE,IAAI,CAAC,CAC3C,MAAO,KAAI,CACb,CACF,CAAE,MAAOtB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvC,MAAO,KAAI,CACb,CACF,CAAC,CAED;AACF;AACA,KACE,KAAM,CAAAiC,6BAA6B,CAAG,cAAAA,CAAA,CAAsB,IAAf,CAAAC,KAAK,CAAAL,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACrD,GAAI,CACF,KAAM,CAAAb,MAAM,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACvE,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA/D,KAAK,CAAC2E,GAAG,CAAC,GAAGhB,MAAM,4BAA4BkB,KAAK,EAAE,CAAC,CAE9E,GAAId,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CAC1CtB,OAAO,CAACuB,GAAG,CAAC,OAAOJ,QAAQ,CAACE,IAAI,CAACG,OAAO,SAAS,CAAEL,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACQ,MAAM,CAAE,GAAG,CAAC,CAClF,MAAO,CAAAV,QAAQ,CAACE,IAAI,CAACA,IAAI,CAC3B,CAAC,IAAM,CACLrB,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEoB,QAAQ,CAACE,IAAI,CAAC,CAC3C,MAAO,EAAE,CACX,CACF,CAAE,MAAOtB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvC,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA,KAAM,CAAAmC,mBAAmB,CAAGnG,WAAW,CAAC,SAACoG,KAAK,CAAEC,MAAM,CAA+C,IAA7C,CAAAC,KAAK,CAAAT,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAU,GAAG,CAAAV,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAW,GAAG,CAAAX,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAY,OAAO,CAAAZ,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAC9F;AACA,KAAM,CAAAa,cAAc,CAAGC,UAAU,CAACL,KAAK,CAAC,CAACM,OAAO,CAAC,CAAC,CAAC,CACnD,KAAM,CAAAC,gBAAgB,CAAGF,UAAU,CAACF,OAAO,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAEvD3C,OAAO,CAACuB,GAAG,CAAC,gBAAgBY,KAAK,QAAQC,MAAM,QAAQK,cAAc,SAASH,GAAG,KAAKC,GAAG,GAAG,CAAC,CAE7F;AACA,GAAIH,MAAM,GAAK,QAAQ,CAAE,CACvB1C,eAAe,CAACmD,IAAI,EAAI,GAAI,CAAA/C,GAAG,CAAC,CAAC,GAAG+C,IAAI,CAAEV,KAAK,CAAC,CAAC,CAAC,CAClDlC,WAAW,CAAC6C,OAAO,CAACX,KAAK,CAAC,CAAGY,IAAI,CAACC,GAAG,CAAC,CAAC,CACzC,CAAC,IAAM,CACLtD,eAAe,CAACmD,IAAI,EAAI,CACtB,KAAM,CAAAI,MAAM,CAAG,GAAI,CAAAnD,GAAG,CAAC+C,IAAI,CAAC,CAC5BI,MAAM,CAACC,MAAM,CAACf,KAAK,CAAC,CACpB,MAAO,CAAAc,MAAM,CACf,CAAC,CAAC,CACJ,CAEA;AACArE,WAAW,CAACuE,YAAY,EACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,EACtBA,OAAO,CAAClB,KAAK,GAAKA,KAAK,CACnB,CACE,GAAGkB,OAAO,CACVjB,MAAM,CAAEA,MAAM,CACdC,KAAK,CAAEK,UAAU,CAACD,cAAc,CAAC,CAAE;AACnCH,GAAG,CAAEI,UAAU,CAACJ,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC,CAC/BJ,GAAG,CAAEG,UAAU,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CAC/BH,OAAO,CAAEE,UAAU,CAACE,gBAAgB,CAAE;AACxC,CAAC,CACDS,OACN,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACAxH,SAAS,CAAC,IAAM,CACdyH,MAAM,CAACpB,mBAAmB,CAAGA,mBAAmB,CAEhD;AACA,KAAM,CAAAqB,qBAAqB,CAAIC,KAAK,EAAK,CACvC,GAAIA,KAAK,CAACnC,IAAI,EAAImC,KAAK,CAACnC,IAAI,CAACoC,IAAI,GAAK,iBAAiB,CAAE,CACvD;AAAA,CAEJ,CAAC,CAEDH,MAAM,CAACI,gBAAgB,CAAC,SAAS,CAAEH,qBAAqB,CAAC,CAEzD;AACAvD,OAAO,CAACuB,GAAG,CAAC,mBAAmB,CAAC,CAChC7B,eAAe,CAAC,GAAI,CAAAI,GAAG,CAAC,CAAC,CAAC,CAC1BG,WAAW,CAAC6C,OAAO,CAAG,CAAC,CAAC,CAExB,MAAO,IAAM,CACXQ,MAAM,CAACK,mBAAmB,CAAC,SAAS,CAAEJ,qBAAqB,CAAC,CAC5D,MAAO,CAAAD,MAAM,CAACpB,mBAAmB,CACnC,CAAC,CACH,CAAC,CAAE,CAACA,mBAAmB,CAAC,CAAC,CAEzB;AACA,KAAM,CAAA0B,sBAAsB,CAAG,cAAAA,CAAA,CAAyC,IAAlC,CAAAjC,SAAS,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAGvB,iBAAiB,CAEjE,GAAI,CACF;AACA,KAAM,CAAAwD,YAAY,CAAG,KAAM,CAAA7B,6BAA6B,CAAC,EAAE,CAAC,CAC5D,GAAI6B,YAAY,EAAIA,YAAY,CAAChC,MAAM,CAAG,CAAC,CAAE,CAC3C/C,SAAS,CAAC+E,YAAY,CAAC,CACvB7D,OAAO,CAACuB,GAAG,CAAC,WAAW,CAAEsC,YAAY,CAAChC,MAAM,CAAE,OAAO,CAAC,CACxD,CAEA;AACA7B,OAAO,CAACuB,GAAG,CAAC,SAAS,CAAEI,SAAS,CAAC,CACjC,KAAM,CAAA1C,KAAK,CAAG,KAAM,CAAAyC,2BAA2B,CAACC,SAAS,CAAC,CAC1D,GAAI1C,KAAK,CAAE,CACTuB,aAAa,CAACsD,SAAS,GAAK,CAC1B,GAAGA,SAAS,CACZ,GAAG7E,KACL,CAAC,CAAC,CAAC,CACHe,OAAO,CAACuB,GAAG,CAAC,mBAAmBI,SAAS,GAAG,CAAC,CAC9C,CACF,CAAE,MAAO5B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACzC,CACF,CAAC,CAED;AACA,KAAM,CAAAgE,qBAAqB,CAAIC,KAAK,EAAK,CACvChE,OAAO,CAACuB,GAAG,CAAC,aAAa,CAAEyC,KAAK,CAAC,CACjC1D,oBAAoB,CAAC0D,KAAK,CAAC,CAC3B;AACAJ,sBAAsB,CAACI,KAAK,CAAC,CAC/B,CAAC,CAED;AACAnI,SAAS,CAAC,IAAM,CACd+H,sBAAsB,CAAC,CAAC,CAC1B,CAAC,CAAE,EAAE,CAAC,CAEN;AACA/H,SAAS,CAAC,IAAM,CACd+H,sBAAsB,CAACvD,iBAAiB,CAAC,CAC3C,CAAC,CAAE,CAACA,iBAAiB,CAAC,CAAC,CAEvB;AACAxE,SAAS,CAAC,IAAM,CACd,KAAM,CAAAoI,eAAe,CAAGC,WAAW,CAAC,IAAM,CACxClE,OAAO,CAACuB,GAAG,CAAC,gBAAgB,CAAE,QAAQ,CAAElB,iBAAiB,CAAC,CAC1DuD,sBAAsB,CAACvD,iBAAiB,CAAC,CAC3C,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX,MAAO,IAAM8D,aAAa,CAACF,eAAe,CAAC,CAC7C,CAAC,CAAE,CAAC5D,iBAAiB,CAAC,CAAC,CAAE;AAEzB;AACAxE,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuI,eAAe,CAAGF,WAAW,CAAC,IAAM,CACxCG,oBAAoB,CAAC,CAAC,CACxB,CAAC,CAAE,MAAM,CAAC,CAAE;AAEZ,MAAO,IAAMF,aAAa,CAACC,eAAe,CAAC,CAC7C,CAAC,CAAE,EAAE,CAAC,CAEN;AACAvI,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyI,sBAAsB,CAAGJ,WAAW,CAAC,IAAM,CAC/CK,oBAAoB,CAAC,CAAC,CACxB,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX,MAAO,IAAMJ,aAAa,CAACG,sBAAsB,CAAC,CACpD,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAE,aAAa,CAAGzI,WAAW,CAAC,SAAY,CAC5C,GAAI,CACF;AACAiE,OAAO,CAACuB,GAAG,CAAC,gBAAgB,CAAC,CAC7B,KAAM,CAAAkD,OAAO,CAAG,KAAM,CAAAC,uBAAuB,CAAC,IAAI,CAAC,CAEnD;AACA,GAAID,OAAO,EAAIA,OAAO,CAAC5C,MAAM,CAAG,CAAC,CAAE,CACjC7B,OAAO,CAACuB,GAAG,CAAC,oBAAoB,CAAEkD,OAAO,CAAC5C,MAAM,CAAC,CAEjD;AACA,KAAM,CAAA8C,MAAM,CAAGF,OAAO,CAACrB,GAAG,CAACwB,CAAC,EAAIA,CAAC,CAACzC,KAAK,CAAC,CAAC0C,MAAM,CAACC,EAAE,EAAIA,EAAE,CAAC,CACzD9E,OAAO,CAACuB,GAAG,CAAC,cAAc,CAAEoD,MAAM,CAAC,CAEnC;AACA,KAAM,CAAAI,eAAe,CAAGN,OAAO,CAACrB,GAAG,CAACC,OAAO,EAAI,CAC7C;AACA,KAAM,CAAA2B,QAAQ,CAAG3B,OAAO,CAAClB,KAAK,EAAI1C,YAAY,CAACwF,GAAG,CAAC5B,OAAO,CAAClB,KAAK,CAAC,CAEjE,MAAO,CACL,GAAGkB,OAAO,CACV6B,KAAK,CAAE7B,OAAO,CAAC8B,WAAW,CAAE;AAC5B/C,MAAM,CAAE4C,QAAQ,CAAG,QAAQ,CAAG,SAAS,CAAE;AACzC3C,KAAK,CAAE2C,QAAQ,CAAI/E,WAAW,CAAC6C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAAChB,KAAK,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CACnFC,GAAG,CAAE0C,QAAQ,CAAI/E,WAAW,CAAC6C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAACf,GAAG,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CAC/EC,GAAG,CAAEyC,QAAQ,CAAI/E,WAAW,CAAC6C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAACd,GAAG,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CAC/EC,OAAO,CAAEwC,QAAQ,CAAI/E,WAAW,CAAC6C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAACb,OAAO,EAAI,CAAC,CAAG,CAAC,CAAI,CACxF,CAAC,CACH,CAAC,CAAC,CAEF5D,WAAW,CAACmG,eAAe,CAAC,CAE5B;AACA,KAAM,CAAAK,WAAW,CAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,EAAIA,CAAC,CAACxC,MAAM,GAAK,QAAQ,CAAC,CAACP,MAAM,CAC7E,KAAM,CAAAwD,UAAU,CAAGN,eAAe,CAAClD,MAAM,CAEzC7B,OAAO,CAACuB,GAAG,CAAC,cAAc8D,UAAU,QAAQD,WAAW,QAAQC,UAAU,CAAGD,WAAW,EAAE,CAAC,CAE1F;AACAlG,QAAQ,CAAC4E,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZ3E,aAAa,CAAEkG,UAAU,CACzBjG,cAAc,CAAEgG,WAAW,CAC3B/F,eAAe,CAAEgG,UAAU,CAAGD,WAChC,CAAC,CAAC,CAAC,CAEH,OACF,CAEA;AACApF,OAAO,CAACuB,GAAG,CAAC,2BAA2B,CAAC,CACxC,KAAM,CAAA+D,YAAY,CAAGjI,YAAY,CAACsB,QAAQ,EAAI,EAAE,CAChDqB,OAAO,CAACuB,GAAG,CAAC,2BAA2B,CAAE+D,YAAY,CAACzD,MAAM,CAAC,CAE7D;AACA,KAAM,CAAAkD,eAAe,CAAGO,YAAY,CAAClC,GAAG,CAACC,OAAO,EAAI,CAClD;AACA,KAAM,CAAA2B,QAAQ,CAAG3B,OAAO,CAAClB,KAAK,EAAI1C,YAAY,CAACwF,GAAG,CAAC5B,OAAO,CAAClB,KAAK,CAAC,CAEjE,MAAO,CACL,GAAGkB,OAAO,CACV6B,KAAK,CAAE7B,OAAO,CAAC8B,WAAW,CAAE;AAC5B/C,MAAM,CAAE4C,QAAQ,CAAG,QAAQ,CAAG,SAAS,CAAE;AACzC3C,KAAK,CAAE2C,QAAQ,CAAI/E,WAAW,CAAC6C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAAChB,KAAK,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CACnFC,GAAG,CAAE0C,QAAQ,CAAI/E,WAAW,CAAC6C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAACf,GAAG,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CAC/EC,GAAG,CAAEyC,QAAQ,CAAI/E,WAAW,CAAC6C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAACd,GAAG,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CAC/EC,OAAO,CAAEwC,QAAQ,CAAI/E,WAAW,CAAC6C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAACb,OAAO,EAAI,CAAC,CAAG,CAAC,CAAI,CACxF,CAAC,CACH,CAAC,CAAC,CAEF5D,WAAW,CAACmG,eAAe,CAAC,CAE5B;AACA,KAAM,CAAAK,WAAW,CAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,EAAIA,CAAC,CAACxC,MAAM,GAAK,QAAQ,CAAC,CAACP,MAAM,CAC7E,KAAM,CAAAwD,UAAU,CAAGN,eAAe,CAAClD,MAAM,CAEzC7B,OAAO,CAACuB,GAAG,CAAC,cAAc8D,UAAU,QAAQD,WAAW,QAAQC,UAAU,CAAGD,WAAW,EAAE,CAAC,CAE1F;AACAlG,QAAQ,CAAC4E,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZ3E,aAAa,CAAEkG,UAAU,CACzBjG,cAAc,CAAEgG,WAAW,CAC3B/F,eAAe,CAAEgG,UAAU,CAAGD,WAChC,CAAC,CAAC,CAAC,CACL,CAAE,MAAOrF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACnC,CACF,CAAC,CAAE,CAACN,YAAY,CAAC,CAAC,CAElB;AACA,KAAM,CAAA8F,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAAxE,MAAM,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACvE,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA/D,KAAK,CAAC2E,GAAG,CAAC,GAAGhB,MAAM,cAAc,CAAC,CAEzD,GAAII,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CAC1C,KAAM,CAAAkE,WAAW,CAAGrE,QAAQ,CAACE,IAAI,CAACA,IAAI,CAEtC;AACAnC,QAAQ,CAAC4E,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZxE,YAAY,CAAEkG,WAAW,CAAC3D,MAAM,CAChCtC,aAAa,CAAEiG,WAAW,CAACX,MAAM,CAACY,CAAC,EAAIA,CAAC,CAACrD,MAAM,GAAK,QAAQ,CAAC,CAACP,MAAM,CACpErC,cAAc,CAAEgG,WAAW,CAACX,MAAM,CAACY,CAAC,EAAIA,CAAC,CAACrD,MAAM,GAAK,SAAS,CAAC,CAACP,MAClE,CAAC,CAAC,CAAC,CACL,CACF,CAAE,MAAO9B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACrC,CACF,CAAC,CAED;AACAlE,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6J,gBAAgB,CAAIlC,KAAK,EAAK,CAClC,GAAIA,KAAK,CAACnC,IAAI,EAAImC,KAAK,CAACnC,IAAI,CAACoC,IAAI,GAAK,KAAK,CAAE,CAC3C;AACA,KAAM,CAAAkC,OAAO,CAAGnC,KAAK,CAACnC,IAAI,CAACA,IAAI,EAAI,CAAC,CAAC,CACrC,KAAM,CAAAc,KAAK,CAAGwD,OAAO,CAACxD,KAAK,EAAIqB,KAAK,CAACnC,IAAI,CAACc,KAAK,CAE/C,GAAI,CAACA,KAAK,CAAE,CACVnC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEyD,KAAK,CAACnC,IAAI,CAAC,CAC1C,OACF,CAEA;AACA,KAAM,CAAA2B,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CAEtB;AACA/C,WAAW,CAAC6C,OAAO,CAACX,KAAK,CAAC,CAAGa,GAAG,CAEhC;AACAtD,eAAe,CAACmD,IAAI,EAAI,GAAI,CAAA/C,GAAG,CAAC,CAAC,GAAG+C,IAAI,CAAEV,KAAK,CAAC,CAAC,CAAC,CAElD;AACA,KAAM,CAAAE,KAAK,CAAGK,UAAU,CAAC,CAACA,UAAU,CAACiD,OAAO,CAACC,SAAS,EAAIpC,KAAK,CAACnC,IAAI,CAACgB,KAAK,EAAI,CAAC,CAAC,CAAG,GAAG,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,CAAE;AACrG,KAAM,CAAAL,GAAG,CAAGI,UAAU,CAACA,UAAU,CAACiD,OAAO,CAACE,OAAO,EAAIrC,KAAK,CAACnC,IAAI,CAACiB,GAAG,EAAI,CAAC,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC,CACrF,KAAM,CAAAJ,GAAG,CAAGG,UAAU,CAACA,UAAU,CAACiD,OAAO,CAACG,QAAQ,EAAItC,KAAK,CAACnC,IAAI,CAACkB,GAAG,EAAI,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CACtF,KAAM,CAAAH,OAAO,CAAGE,UAAU,CAACA,UAAU,CAACiD,OAAO,CAACI,WAAW,EAAIvC,KAAK,CAACnC,IAAI,CAACmB,OAAO,EAAI,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAE;AAEnG;AAEA;AACA/D,WAAW,CAACuE,YAAY,EAAI,CAC1B;AACA,KAAM,CAAA6C,YAAY,CAAG7C,YAAY,CAAC8C,IAAI,CAACrB,CAAC,EAAIA,CAAC,CAACzC,KAAK,GAAKA,KAAK,CAAC,CAC9D,GAAI,CAAC6D,YAAY,CAAE,CACjBhG,OAAO,CAACuB,GAAG,CAAC,eAAeY,KAAK,WAAW,CAAC,CAC5C,MAAO,CAAAgB,YAAY,CACrB,CAEA,KAAM,CAAA4B,eAAe,CAAG5B,YAAY,CAACC,GAAG,CAACC,OAAO,EAC9CA,OAAO,CAAClB,KAAK,GAAKA,KAAK,CACnB,CACE,GAAGkB,OAAO,CACVjB,MAAM,CAAE,QAAQ,CAChBC,KAAK,CAAEA,KAAK,CACZC,GAAG,CAAEA,GAAG,CACRC,GAAG,CAAEA,GAAG,CACRC,OAAO,CAAEA,OACX,CAAC,CACDa,OACN,CAAC,CAED;AACA,KAAM,CAAA+B,WAAW,CAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,EAAIA,CAAC,CAACxC,MAAM,GAAK,QAAQ,CAAC,CAACP,MAAM,CAC7E,KAAM,CAAAwD,UAAU,CAAGN,eAAe,CAAClD,MAAM,CAEzC;AACA3C,QAAQ,CAAC4E,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZ1E,cAAc,CAAEgG,WAAW,CAC3B/F,eAAe,CAAEgG,UAAU,CAAGD,WAChC,CAAC,CAAC,CAAC,CAEH,MAAO,CAAAL,eAAe,CACxB,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACAzB,MAAM,CAACI,gBAAgB,CAAC,SAAS,CAAEgC,gBAAgB,CAAC,CAEpD;AACA,MAAO,IAAM,CACXpC,MAAM,CAACK,mBAAmB,CAAC,SAAS,CAAE+B,gBAAgB,CAAC,CACzD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA7J,SAAS,CAAC,IAAM,CACd,KAAM,CAAAqK,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAlD,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CACtBhD,OAAO,CAACuB,GAAG,CAAC,aAAa,CAAC,CAE1B;AACA;AACA7B,eAAe,CAACmD,IAAI,EAAI,CACtB,KAAM,CAAAsD,eAAe,CAAG,GAAI,CAAArG,GAAG,CAAC+C,IAAI,CAAC,CACrC,GAAI,CAAAuD,UAAU,CAAG,KAAK,CAEtB;AACAvD,IAAI,CAACwD,OAAO,CAAClE,KAAK,EAAI,CACpB,KAAM,CAAAmE,QAAQ,CAAGrG,WAAW,CAAC6C,OAAO,CAACX,KAAK,CAAC,CAE3C;AACA,GAAImE,QAAQ,EAAKtD,GAAG,CAAGsD,QAAQ,CAAG,KAAM,CAAE,CACxCtG,OAAO,CAACuB,GAAG,CAAC,KAAKY,KAAK,kBAAkB,CAAC,CACzCgE,eAAe,CAACjD,MAAM,CAACf,KAAK,CAAC,CAC7BiE,UAAU,CAAG,IAAI,CACnB,CACF,CAAC,CAAC,CAEF,GAAIA,UAAU,CAAE,CACd;AACAxH,WAAW,CAACuE,YAAY,EAAI,CAC1B,KAAM,CAAA4B,eAAe,CAAG5B,YAAY,CAACC,GAAG,CAACC,OAAO,EAAI,CAClD;AACA,GAAIpD,WAAW,CAAC6C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAE,CACtC,KAAM,CAAA6C,QAAQ,CAAGmB,eAAe,CAAClB,GAAG,CAAC5B,OAAO,CAAClB,KAAK,CAAC,CACnD,MAAO,CACL,GAAGkB,OAAO,CACVjB,MAAM,CAAE4C,QAAQ,CAAG,QAAQ,CAAG,SAChC,CAAC,CACH,CACA,MAAO,CAAA3B,OAAO,CAChB,CAAC,CAAC,CAEF;AACA,KAAM,CAAA+B,WAAW,CAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,EAAIA,CAAC,CAACxC,MAAM,GAAK,QAAQ,CAAC,CAACP,MAAM,CAC7E,KAAM,CAAAwD,UAAU,CAAGN,eAAe,CAAClD,MAAM,CAEzC;AACA3C,QAAQ,CAAC4E,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZ1E,cAAc,CAAEgG,WAAW,CAC3B/F,eAAe,CAAEgG,UAAU,CAAGD,WAChC,CAAC,CAAC,CAAC,CAEH,MAAO,CAAAL,eAAe,CACxB,CAAC,CAAC,CACJ,CAEA,MAAO,CAAAoB,eAAe,CACxB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAI,QAAQ,CAAGrC,WAAW,CAACgC,iBAAiB,CAAE,IAAI,CAAC,CACrD,MAAO,IAAM/B,aAAa,CAACoC,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN;AACA1K,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA2K,gBAAgB,CAAGA,CAAA,GAAM,CAC7B;AACA,GAAI/G,YAAY,CAACgH,IAAI,GAAK,CAAC,CAAE,CAC3B7H,WAAW,CAACuE,YAAY,EACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,GAAK,CAC3B,GAAGA,OAAO,CACVjB,MAAM,CAAE,SAAS,CACjBC,KAAK,CAAE,CAAC,CACRC,GAAG,CAAE,CAAC,CACNC,GAAG,CAAE,CAAC,CACNC,OAAO,CAAE,CACX,CAAC,CAAC,CACJ,CAAC,CAEDxC,OAAO,CAACuB,GAAG,CAAC,cAAc,CAAC,CAC7B,CACF,CAAC,CAED;AACAiF,gBAAgB,CAAC,CAAC,CAElB;AACA,KAAM,CAAAD,QAAQ,CAAGrC,WAAW,CAACsC,gBAAgB,CAAE,KAAK,CAAC,CAErD,MAAO,IAAMrC,aAAa,CAACoC,QAAQ,CAAC,CACtC,CAAC,CAAE,CAAC9G,YAAY,CAAC,CAAC,CAElB;AACA5D,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6K,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3BhI,UAAU,CAAC,IAAI,CAAC,CAChB8F,aAAa,CAAC,CAAC,CACf,KAAM,CAAAe,gBAAgB,CAAC,CAAC,CACxB7G,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAEDgI,QAAQ,CAAC,CAAC,CACV;AACA;AACA;AACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA7K,SAAS,CAAC,IAAM,CACdmE,OAAO,CAACuB,GAAG,CAAC,YAAY,CAAC,CAEzB;AACA,KAAM,CAAAoF,yBAAyB,CAAGA,CAAA,GAAM,CACtC3G,OAAO,CAACuB,GAAG,CAAC,sBAAsB,CAAC,CACnCiD,aAAa,CAAC,CAAC,CACjB,CAAC,CAED;AACA,KAAM,CAAAoC,mBAAmB,CAAIpD,KAAK,EAAK,CACrC,GAAIA,KAAK,CAACqD,GAAG,GAAK,qBAAqB,EAAIrD,KAAK,CAACqD,GAAG,GAAK,cAAc,CAAE,CACvE7G,OAAO,CAACuB,GAAG,CAAC,4BAA4B,CAAC,CACzCiD,aAAa,CAAC,CAAC,CACjB,CACF,CAAC,CAED;AACAlB,MAAM,CAACI,gBAAgB,CAAC,qBAAqB,CAAEiD,yBAAyB,CAAC,CACzErD,MAAM,CAACI,gBAAgB,CAAC,SAAS,CAAEkD,mBAAmB,CAAC,CAEvD;AACA,KAAM,CAAAE,WAAW,CAAGlH,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAC/D,GAAIiH,WAAW,CAAE,CACf9G,OAAO,CAACuB,GAAG,CAAC,2BAA2B,CAAEuF,WAAW,CAAC,CACrDtC,aAAa,CAAC,CAAC,CACjB,CAEA;AACA,KAAM,CAAAuC,qBAAqB,CAAG7C,WAAW,CAAC,IAAM,CAC9ClE,OAAO,CAACuB,GAAG,CAAC,eAAe,CAAC,CAC5BiD,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX;AACA,MAAO,IAAM,CACXlB,MAAM,CAACK,mBAAmB,CAAC,qBAAqB,CAAEgD,yBAAyB,CAAC,CAC5ErD,MAAM,CAACK,mBAAmB,CAAC,SAAS,CAAEiD,mBAAmB,CAAC,CAC1DzC,aAAa,CAAC4C,qBAAqB,CAAC,CACtC,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA;AACA,KAAM,CAAArC,uBAAuB,CAAG,cAAAA,CAAA,CAA8B,IAAvB,CAAAsC,UAAU,CAAApF,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CACvD,GAAI,CACF;AACA,KAAM,CAAAqF,eAAe,CAAG,CACtBjG,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBACjC;AACA;AACA;AACA;AACA;AACA;AAAA,CACD,CAEDlB,OAAO,CAACuB,GAAG,CAAC,kBAAkB,CAAC,CAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA,GAAI,CAAA2F,SAAS,CAAG,KAAK,CACrB,GAAI,CAAA7J,YAAY,CAAG,IAAI,CAEvB,IAAK,KAAM,CAAA0D,MAAM,GAAI,CAAAkG,eAAe,CAAE,CACpC,GAAIC,SAAS,CAAE,MAEf,GAAI,CACFlH,OAAO,CAACuB,GAAG,CAAC,OAAOR,MAAM,yBAAyB,CAAC,CACnD,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAA/D,KAAK,CAAC2E,GAAG,CAAC,GAAGhB,MAAM,oBAAoB,CAAC,CAE/D,GAAII,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAAC1C,QAAQ,CAAE,CAC3CqB,OAAO,CAACuB,GAAG,CAAC,OAAOR,MAAM,QAAQ,CAAEI,QAAQ,CAACE,IAAI,CAAC1C,QAAQ,CAACkD,MAAM,CAAC,CACjExE,YAAY,CAAG8D,QAAQ,CAACE,IAAI,CAAC1C,QAAQ,CAErC,GAAIqI,UAAU,CAAE,CACd,MAAO,CAAA3J,YAAY,CACrB,CAEA8J,mBAAmB,CAAChG,QAAQ,CAACE,IAAI,CAAC1C,QAAQ,CAAC,CAC3CuI,SAAS,CAAG,IAAI,CAChB,MACF,CACF,CAAE,MAAOnH,KAAK,CAAE,CACdC,OAAO,CAACuB,GAAG,CAAC,KAAKR,MAAM,QAAQ,CAAEhB,KAAK,CAACqH,OAAO,CAAC,CAC/C;AACF,CACF,CAEA,GAAI,CAACF,SAAS,EAAI,CAACF,UAAU,CAAE,CAC7BhH,OAAO,CAACuB,GAAG,CAAC,gCAAgC,CAAC,CAC7C;AACA,GAAI,CACF,KAAM,CAAAJ,QAAQ,CAAG,KAAM,CAAAkG,KAAK,CAAC,gBAAgB,CAAC,CAC9C,GAAIlG,QAAQ,CAACmG,EAAE,CAAE,CACf,KAAM,CAAAjG,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACoG,IAAI,CAAC,CAAC,CAClC,GAAIlG,IAAI,EAAIA,IAAI,CAAC1C,QAAQ,CAAE,CACzBqB,OAAO,CAACuB,GAAG,CAAC,8BAA8B,CAAEF,IAAI,CAAC1C,QAAQ,CAACkD,MAAM,CAAC,CACjEsF,mBAAmB,CAAC9F,IAAI,CAAC1C,QAAQ,CAAC,CAClC,MAAO,CAAA0C,IAAI,CAAC1C,QAAQ,CACtB,CACF,CACF,CAAE,MAAO6I,CAAC,CAAE,CACVxH,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEyH,CAAC,CAAC,CAChD,CACF,CAEA,MAAO,CAAAnK,YAAY,EAAI,EAAE,CAC3B,CAAE,MAAO0C,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA,KAAM,CAAAoH,mBAAmB,CAAIM,WAAW,EAAK,CAC3C;AACA,GAAI9I,QAAQ,CAACkD,MAAM,GAAK4F,WAAW,CAAC5F,MAAM,CAAE,CAC1C7B,OAAO,CAACuB,GAAG,CAAC,aAAa,CAAE5C,QAAQ,CAACkD,MAAM,CAAE,GAAG,CAAE4F,WAAW,CAAC5F,MAAM,CAAC,CACpE2C,aAAa,CAAC,CAAC,CAAE;AACjB,OACF,CAEA;AACA,KAAM,CAAAkD,UAAU,CAAG,GAAI,CAAA5H,GAAG,CAACnB,QAAQ,CAACyE,GAAG,CAACwB,CAAC,EAAIA,CAAC,CAACE,EAAE,CAAC,CAAC,CACnD,KAAM,CAAA6C,aAAa,CAAGF,WAAW,CAACG,IAAI,CAAChD,CAAC,EAAI,CAAC8C,UAAU,CAACzC,GAAG,CAACL,CAAC,CAACE,EAAE,CAAC,CAAC,CAElE,GAAI6C,aAAa,CAAE,CACjB3H,OAAO,CAACuB,GAAG,CAAC,SAAS,CAAC,CACtBiD,aAAa,CAAC,CAAC,CAAE;AACnB,CACF,CAAC,CAED;AACA3I,SAAS,CAAC,IAAM,CACd;AACA,GAAI8C,QAAQ,CAACkD,MAAM,CAAG,CAAC,EAAI,CAAC9C,eAAe,CAAE,CAC3C;AACAC,kBAAkB,CAACL,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC/BqB,OAAO,CAACuB,GAAG,CAAC,aAAa,CAAE5C,QAAQ,CAAC,CAAC,CAAC,CAACwG,WAAW,CAAC,CACrD,CACF,CAAC,CAAE,CAACxG,QAAQ,CAAEI,eAAe,CAAC,CAAC,CAE/B;AACAlD,SAAS,CAAC,IAAM,CACd,GAAI,CAAAgM,KAAK,CAAG,IAAI,CAChB,GAAI,CAAAC,YAAY,CAAG,IAAI,CACvB,GAAI,CAAAC,cAAc,CAAG,GAAI,CAAAhF,IAAI,CAAC,CAAC,CAE/B;AACA,GAAI,CAAC7C,aAAa,CAAC4C,OAAO,CAAE,CAC1B9C,OAAO,CAACD,KAAK,CAAC,SAAS,CAAC,CACxB,OACF,CAEA;AACA,GAAI,CAAAiI,aAAa,CAAGrL,OAAO,CAACsL,gBAAgB,CAAC/H,aAAa,CAAC4C,OAAO,CAAC,CACnE,GAAIkF,aAAa,CAAE,CACjBA,aAAa,CAACE,OAAO,CAAC,CAAC,CACzB,CAEA,GAAI,CACF;AACAL,KAAK,CAAGlL,OAAO,CAACwL,IAAI,CAACjI,aAAa,CAAC4C,OAAO,CAAC,CAE3C;AACA,KAAM,CAAAsF,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAtF,IAAI,CAAC,CAAC,CAC9BgF,cAAc,CAAGM,WAAW,CAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAM,CAAAC,UAAU,CAAG,CACjB,CAAE7E,IAAI,CAAE,KAAK,CAAE8E,IAAI,CAAE,OAAO,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAAE/E,IAAI,CAAE,KAAK,CAAE8E,IAAI,CAAE,OAAO,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAAE/E,IAAI,CAAE,KAAK,CAAE8E,IAAI,CAAE,QAAQ,CAAEC,KAAK,CAAE,SAAU,CAAC,CACjD,CAAE/E,IAAI,CAAE,KAAK,CAAE8E,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC/C,CAAE/E,IAAI,CAAE,KAAK,CAAE8E,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC/C,CAAE/E,IAAI,CAAE,MAAM,CAAE8E,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAAE/E,IAAI,CAAE,KAAK,CAAE8E,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAED;AACA,KAAM,CAAAnH,IAAI,CAAGiH,UAAU,CACpBlF,GAAG,CAACI,KAAK,GAAK,CACbQ,KAAK,CAAEzD,UAAU,CAACiD,KAAK,CAACC,IAAI,CAAC,EAAI,CAAC,CAClC8E,IAAI,CAAE/E,KAAK,CAAC+E,IAAI,CAChBE,SAAS,CAAE,CAAED,KAAK,CAAEhF,KAAK,CAACgF,KAAM,CAClC,CAAC,CAAC,CAAC,CACF3D,MAAM,CAAC6D,IAAI,EAAIA,IAAI,CAAC1E,KAAK,CAAG,CAAC,CAAC,CAC9B2E,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAAC7E,KAAK,CAAG4E,CAAC,CAAC5E,KAAK,CAAC,CAEpC,KAAM,CAAA8E,MAAM,CAAG,CACbC,KAAK,CAAE,CACLC,IAAI,CAAE,SAASX,WAAW,CAACY,kBAAkB,CAAC,CAAC,EAAE,CACjDC,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,CAAC,CAAC,CACPC,SAAS,CAAE,CACTC,QAAQ,CAAE,EAAE,CACZb,KAAK,CAAE,MACT,CACF,CAAC,CACDc,IAAI,CAAE,CACJH,GAAG,CAAE,EAAE,CACPI,MAAM,CAAE,CAAC,CACTL,IAAI,CAAE,CAAC,CACPM,KAAK,CAAE,EAAE,CACTC,YAAY,CAAE,IAChB,CAAC,CACDC,SAAS,CAAE,IAAI,CACfC,iBAAiB,CAAE,CAAC,CACpBC,uBAAuB,CAAE,IAAI,CAC7BC,qBAAqB,CAAE,cAAc,CACrCC,OAAO,CAAE,CACPC,OAAO,CAAE,MAAM,CACfC,WAAW,CAAE,CACXvG,IAAI,CAAE,QACR,CACF,CAAC,CACDwG,KAAK,CAAE,CACLxG,IAAI,CAAE,OAAO,CACbyG,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,CAAED,IAAI,CAAE,KAAM,CAC3B,CAAC,CACDE,KAAK,CAAE,CACL3G,IAAI,CAAE,UAAU,CAChBpC,IAAI,CAAEA,IAAI,CAAC+B,GAAG,CAACsF,IAAI,EAAIA,IAAI,CAACH,IAAI,CAAC,CACjC8B,SAAS,CAAE,CACThB,QAAQ,CAAE,EAAE,CACZb,KAAK,CAAE,MAAM,CACb8B,MAAM,CAAE,CACV,CAAC,CACDC,QAAQ,CAAE,CAAEL,IAAI,CAAE,KAAM,CAAC,CACzBM,QAAQ,CAAE,CAAEN,IAAI,CAAE,KAAM,CAC1B,CAAC,CACDO,MAAM,CAAE,CAAC,CACPhH,IAAI,CAAE,KAAK,CACXpC,IAAI,CAAEA,IAAI,CACVqJ,QAAQ,CAAE,KAAK,CACfC,KAAK,CAAE,CACLT,IAAI,CAAE,IAAI,CACVU,QAAQ,CAAE,OAAO,CACjBC,SAAS,CAAE,MAAM,CACjBxB,QAAQ,CAAE,EAAE,CACZb,KAAK,CAAE,MACT,CAAC,CACDC,SAAS,CAAE,CACTqC,YAAY,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAC3B,CAAC,CACDC,YAAY,CAAE,KAAK,CACnBC,cAAc,CAAE,QAAAA,CAAUC,GAAG,CAAE,CAC7B,MAAO,CAAAA,GAAG,CAAG,GAAG,CAClB,CACF,CAAC,CACH,CAAC,CAED;AACApD,KAAK,CAACqD,SAAS,CAACpC,MAAM,CAAE,CACtBqC,QAAQ,CAAE,KAAK,CACfC,YAAY,CAAE,CAAC,QAAQ,CACzB,CAAC,CAAC,CACJ,CAAC,CAED;AACAhD,WAAW,CAAC,CAAC,CAEb;AACA,KAAM,CAAAiD,aAAa,CAAGnH,WAAW,CAACkE,WAAW,CAAE,KAAK,CAAC,CAAE;AAEvD;AACAN,YAAY,CAAGA,CAAA,GAAM,KAAAwD,MAAA,CACnB,CAAAA,MAAA,CAAAzD,KAAK,UAAAyD,MAAA,iBAALA,MAAA,CAAOC,MAAM,CAAC,CAAC,CACjB,CAAC,CACDjI,MAAM,CAACI,gBAAgB,CAAC,QAAQ,CAAEoE,YAAY,CAAC,CAE/C;AACA,MAAO,IAAM,CACX3D,aAAa,CAACkH,aAAa,CAAC,CAC5B,GAAIvD,YAAY,CAAE,CAChBxE,MAAM,CAACK,mBAAmB,CAAC,QAAQ,CAAEmE,YAAY,CAAC,CACpD,CACA,GAAID,KAAK,CAAE,CACTA,KAAK,CAACK,OAAO,CAAC,CAAC,CACjB,CACF,CAAC,CAEH,CAAE,MAAOnI,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CAAE,CAACQ,UAAU,CAAC,CAAC,CAEhB;AACA1E,SAAS,CAAC,IAAM,CACd,KAAM,CAAA2P,gBAAgB,CAAIhI,KAAK,EAAK,CAClC,GAAI,CACF,GAAIA,KAAK,CAACnC,IAAI,EAAImC,KAAK,CAACnC,IAAI,CAACoC,IAAI,GAAK,KAAK,CAAE,CAC3C,KAAM,CAAAgI,OAAO,CAAGjI,KAAK,CAACnC,IAAI,CAACA,IAAI,CAC/B,GAAI,CAACoK,OAAO,EAAI,CAACA,OAAO,CAACC,IAAI,CAAE,OAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,KAAM,CAAAC,QAAQ,CAAGjJ,UAAU,CAAC+I,OAAO,CAACG,MAAM,CAAC,CAC3C,KAAM,CAAAC,SAAS,CAAGnJ,UAAU,CAAC+I,OAAO,CAACK,OAAO,CAAC,CAC7C,KAAM,CAAAC,KAAK,CAAGN,OAAO,CAACM,KAAK,CAC3B,KAAM,CAAAC,GAAG,CAAGxI,KAAK,CAACnC,IAAI,CAAC2K,GAAG,EAAI,EAAE,CAChC,KAAM,CAAAC,SAAS,CAAGzI,KAAK,CAACnC,IAAI,CAAC6K,EAAE,EAAInJ,IAAI,CAACC,GAAG,CAAC,CAAC,CAE7C;AACA,KAAM,CAAAmJ,sBAAsB,CAAG,EAAE,CACjCV,OAAO,CAACC,IAAI,CAACrF,OAAO,CAAC7C,KAAK,EAAI,CAC5B,KAAM,CAAA4I,SAAS,CAAG5I,KAAK,CAAC4I,SAAS,CAEjC;AACA,GAAI,CAAAC,QAAQ,CAAG,EAAE,CACjB,GAAI,CAAAC,QAAQ,CAAG,EAAE,CACjB,GAAI,CAAAC,QAAQ,CAAG,EAAE,CAEjB,GAAGH,SAAS,GAAK,KAAK,EAAIA,SAAS,GAAK,KAAK,CAAC,CAC5C;AACAC,QAAQ,CAAGG,IAAI,CAACC,KAAK,CAACd,QAAQ,CAAGa,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DJ,QAAQ,CAAGE,IAAI,CAACC,KAAK,CAACZ,SAAS,CAAEW,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DH,QAAQ,CAAG,GAAGR,KAAK,IAAIC,GAAG,IAAII,SAAS,IAAIC,QAAQ,IAAIC,QAAQ,EAAE,CACnE,CAAC,IACI,IAAGF,SAAS,GAAK,KAAK,CAAC,CAC1B;AACA;AACAC,QAAQ,CAAGG,IAAI,CAACC,KAAK,CAACd,QAAQ,CAAGa,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DJ,QAAQ,CAAGE,IAAI,CAACC,KAAK,CAACZ,SAAS,CAAEW,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DH,QAAQ,CAAG,GAAGR,KAAK,IAAIC,GAAG,IAAII,SAAS,IAAIC,QAAQ,IAAIC,QAAQ,EAAE,CACnE,CAAC,IACG,CACF;AACAD,QAAQ,CAAGG,IAAI,CAACC,KAAK,CAACd,QAAQ,CAAGa,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DJ,QAAQ,CAAGE,IAAI,CAACC,KAAK,CAACZ,SAAS,CAAEW,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DH,QAAQ,CAAG,GAAGR,KAAK,IAAIC,GAAG,IAAII,SAAS,IAAIC,QAAQ,IAAIC,QAAQ,EAAE,CACnE,CAEA,KAAM,CAAAK,eAAe,CAAGC,mBAAmB,CACzCR,SAAS,CACTG,QAAQ,CACRN,SAAS,CACT,CAAE3J,GAAG,CAAEqJ,QAAQ,CAAEpJ,GAAG,CAAEsJ,SAAU,CAClC,CAAC,CAED,KAAM,CAAAgB,WAAW,CAAGF,eAAe,CAACE,WAAW,CAC/C;AACA,GAAI,CAAApL,aAAa,CAAG,EAAE,CACtB,GAAI,CAAAqL,UAAU,CAAG,EAAE,CACnB,OAAOV,SAAS,EACd,IAAK,KAAK,CAAE3K,aAAa,CAAG,OAAO,CAAEqL,UAAU,CAAG,SAAS,CAAE,MAC7D,IAAK,KAAK,CAAErL,aAAa,CAAG,OAAO,CAAEqL,UAAU,CAAG,SAAS,CAAE,MAC7D,IAAK,KAAK,CAAErL,aAAa,CAAG,QAAQ,CAAEqL,UAAU,CAAG,SAAS,CAAE,MAC9D,IAAK,KAAK,CAAErL,aAAa,CAAG,MAAM,CAAEqL,UAAU,CAAG,SAAS,CAAE,MAC5D,IAAK,KAAK,CAAErL,aAAa,CAAG,MAAM,CAAEqL,UAAU,CAAG,SAAS,CAAE,MAC5D,IAAK,MAAM,CAAErL,aAAa,CAAG,MAAM,CAAEqL,UAAU,CAAG,SAAS,CAAE,MAC7D,IAAK,KAAK,CAAErL,aAAa,CAAG,MAAM,CAAEqL,UAAU,CAAG,SAAS,CAAE,MAC5D,QAASrL,aAAa,CAAG+B,KAAK,CAACuJ,WAAW,EAAI,MAAM,CAAED,UAAU,CAAG,SAAS,CAC9E,CACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA,GAAI,CAACD,WAAW,CAAE,CAChB;AACA,KAAM,CAAA/L,SAAS,CAAG,CAChBkM,OAAO,CAAEL,eAAe,CAACK,OAAO,CAChCZ,SAAS,CAAEA,SAAS,CACpB3K,aAAa,CAAEA,aAAa,CAC5BsK,KAAK,CAAEN,OAAO,CAACM,KAAK,EAAI,MAAM,CAC9BC,GAAG,CAAEA,GAAG,CACRL,QAAQ,CAAEA,QAAQ,CAClBE,SAAS,CAAEA,SAAS,CACpBoB,IAAI,CAAEC,0BAA0B,CAACvB,QAAQ,CAAEE,SAAS,CAAC,CACrDU,QAAQ,CAAEA,QAAQ,CAClB/D,KAAK,CAAEsE,UAAU,CACjBb,SAAS,CAAE,GAAI,CAAAlJ,IAAI,CAAC,CAAC,CAACoK,WAAW,CAAC,CACpC,CAAC,CAED;AACAtM,oBAAoB,CAACC,SAAS,CAAC,CAACsM,IAAI,CAAC9L,OAAO,EAAI,CAC9C,GAAIA,OAAO,CAAE,CACXtB,OAAO,CAACuB,GAAG,CAAC,gBAAgBE,aAAa,EAAE,CAAC,CAE5C;AACA,KAAM,CAAA4L,QAAQ,CAAG,CACfxG,GAAG,CAAE9D,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGwJ,IAAI,CAACc,MAAM,CAAC,CAAC,CAC/B7J,IAAI,CAAEhC,aAAa,CACnB8L,IAAI,CAAE,GAAI,CAAAxK,IAAI,CAAC,CAAC,CAACkG,kBAAkB,CAAC,CAAC,CACrC8C,KAAK,CAAEN,OAAO,CAACM,KAAK,EAAI,MAAM,CAC9BvD,KAAK,CAAEsE,UAAU,CACjBV,SAAS,CAAEA,SAAS,CACpBoB,QAAQ,CAAE,CACR7B,QAAQ,CAAEA,QAAQ,CAClBE,SAAS,CAAEA,SACb,CAAC,CACDoB,IAAI,CAAEnM,SAAS,CAACmM,IAClB,CAAC,CAEDnO,SAAS,CAAC+D,IAAI,EAAI,CAChB,KAAM,CAAA4K,SAAS,CAAG,CAACJ,QAAQ,CAAE,GAAGxK,IAAI,CAAC,CAAC6K,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAE;AACpD,MAAO,CAAAD,SAAS,CAClB,CAAC,CAAC,CACJ,CAAC,IAAM,CACLzN,OAAO,CAACD,KAAK,CAAC,aAAa0B,aAAa,EAAE,CAAC,CAC7C,CACF,CAAC,CAAC,CAEF0K,sBAAsB,CAACwB,IAAI,CAACvB,SAAS,CAAC,CACxC,CAAC,IAAM,CACL;AAAA,CAEJ,CAAC,CAAC,CAEF;AACA,GAAID,sBAAsB,CAACtK,MAAM,CAAG,CAAC,CAAE,CACrC7B,OAAO,CAACuB,GAAG,CAAC,UAAU4K,sBAAsB,CAACtK,MAAM,cAAc,CAAC,CAElE;AACA7B,OAAO,CAACuB,GAAG,CAAC,SAAS,CAAElB,iBAAiB,CAAC,CACzCuN,UAAU,CAAC,SAAY,CACrB,KAAM,CAAA3O,KAAK,CAAG,KAAM,CAAAyC,2BAA2B,CAACrB,iBAAiB,CAAC,CAClE,GAAIpB,KAAK,CAAE,CACTuB,aAAa,CAACsD,SAAS,GAAK,CAC1B,GAAGA,SAAS,CACZ,GAAG7E,KACL,CAAC,CAAC,CAAC,CACHe,OAAO,CAACuB,GAAG,CAAC,sBAAsB,CAAElB,iBAAiB,CAAC,CACxD,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CACF,CACF,CAAE,MAAON,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACtC,CACF,CAAC,CAED;AACAuD,MAAM,CAACI,gBAAgB,CAAC,SAAS,CAAE8H,gBAAgB,CAAC,CAEpD,MAAO,IAAM,CACXlI,MAAM,CAACK,mBAAmB,CAAC,SAAS,CAAE6H,gBAAgB,CAAC,CACzD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAqC,kBAAkB,CAAIzB,SAAS,EAAK,CACxC,OAAOA,SAAS,EACd,IAAK,KAAK,CAAE;AACV,MAAO,CAAE0B,aAAa,CAAE,MAAM,CAAEC,iBAAiB,CAAE,EAAG,CAAC,CAAE;AAC3D,IAAK,KAAK,CAAE;AACV,MAAO,CAAED,aAAa,CAAE,KAAK,CAAEC,iBAAiB,CAAE,EAAG,CAAC,CAAE;AAC1D,IAAK,KAAK,CAAE;AACV,MAAO,CAAED,aAAa,CAAE,KAAK,CAAEC,iBAAiB,CAAE,EAAG,CAAC,CAAE;AAC1D,IAAK,KAAK,CAAE;AACZ,IAAK,KAAK,CAAE;AACZ,IAAK,MAAM,CAAE;AACX,MAAO,CAAED,aAAa,CAAE,MAAM,CAAEC,iBAAiB,CAAE,CAAE,CAAC,CAAE;AAC1D,IAAK,KAAK,CAAE;AACV,MAAO,CAAED,aAAa,CAAE,KAAK,CAAEC,iBAAiB,CAAE,EAAG,CAAC,CAAE;AAC1D,QACE,MAAO,CAAED,aAAa,CAAE,IAAI,CAAEC,iBAAiB,CAAE,CAAE,CAAC,CAAE;AAC1D,CACF,CAAC,CAED;AACA,KAAM,CAAAnB,mBAAmB,CAAGA,CAACR,SAAS,CAAEG,QAAQ,CAAElE,WAAW,CAAE2F,UAAU,GAAK,CAC5E,KAAM,CAAEF,aAAa,CAAEC,iBAAkB,CAAC,CAAGF,kBAAkB,CAACzB,SAAS,CAAC,CAE1E;AACA,IAAK,GAAI,CAAA6B,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGtN,cAAc,CAACmC,OAAO,CAACjB,MAAM,CAAEoM,CAAC,EAAE,CAAE,CACtD,KAAM,CAAAC,WAAW,CAAGvN,cAAc,CAACmC,OAAO,CAACmL,CAAC,CAAC,CAE7C;AACA,GAAIC,WAAW,CAAC9B,SAAS,GAAKA,SAAS,CAAE,CACvC,SACF,CAEA;AACA,KAAM,CAAA+B,QAAQ,CAAG9F,WAAW,CAAG6F,WAAW,CAACnG,cAAc,CAEzD;AACA,GAAIoG,QAAQ,CAAGL,aAAa,CAAE,CAC5B,SACF,CAEA;AACA,KAAM,CAAAM,QAAQ,CAAGC,iBAAiB,CAChCL,UAAU,CAAC1L,GAAG,CAAE0L,UAAU,CAACzL,GAAG,CAC9B2L,WAAW,CAACtD,QAAQ,CAACtI,GAAG,CAAE4L,WAAW,CAACtD,QAAQ,CAACrI,GACjD,CAAC,CAED;AACA,GAAI6L,QAAQ,EAAIL,iBAAiB,CAAE,CACjC;AACAG,WAAW,CAAC3B,QAAQ,CAAGA,QAAQ,CAC/B2B,WAAW,CAACnG,cAAc,CAAGM,WAAW,CACxC6F,WAAW,CAACtD,QAAQ,CAAG,CAAE,GAAGoD,UAAW,CAAC,CACxCE,WAAW,CAACI,WAAW,CAAG,CAACJ,WAAW,CAACI,WAAW,EAAI,CAAC,EAAI,CAAC,CAE5D;AAEA,MAAO,CACLzB,WAAW,CAAE,IAAI,CACjBG,OAAO,CAAEkB,WAAW,CAAClB,OAAO,CAC5BuB,YAAY,CAAEL,WAChB,CAAC,CACH,CACF,CAEA;AACA,KAAM,CAAAM,UAAU,CAAG,OAAO5N,cAAc,CAACkC,OAAO,CAAC2L,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CAC9E9N,cAAc,CAACkC,OAAO,EAAE,CAExB,KAAM,CAAAuK,QAAQ,CAAG,CACfL,OAAO,CAAEwB,UAAU,CACnBpC,SAAS,CAAEA,SAAS,CACpBG,QAAQ,CAAEA,QAAQ,CAClBoC,iBAAiB,CAAEtG,WAAW,CAC9BN,cAAc,CAAEM,WAAW,CAC3BuC,QAAQ,CAAE,CAAE,GAAGoD,UAAW,CAAC,CAC3BM,WAAW,CAAE,CACf,CAAC,CAED;AACA3N,cAAc,CAACmC,OAAO,CAAC6K,IAAI,CAACN,QAAQ,CAAC,CAErCrN,OAAO,CAACuB,GAAG,CAAC,WAAW6K,SAAS,SAASoC,UAAU,UAAUR,UAAU,CAAC1L,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,KAAKqL,UAAU,CAACzL,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAExH,MAAO,CACLkK,WAAW,CAAE,KAAK,CAClBG,OAAO,CAAEwB,UAAU,CACnBnB,QAAQ,CAAEA,QACZ,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAAhJ,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAgE,WAAW,CAAGtF,IAAI,CAACC,GAAG,CAAC,CAAC,CAC9B,KAAM,CAAA4L,MAAM,CAAG,OAAO,CAAE;AAExB,KAAM,CAAAC,YAAY,CAAGlO,cAAc,CAACmC,OAAO,CAACjB,MAAM,CAClDlB,cAAc,CAACmC,OAAO,CAAGnC,cAAc,CAACmC,OAAO,CAAC+B,MAAM,CAACrB,KAAK,EAAI,CAC9D,KAAM,CAAAsL,GAAG,CAAGzG,WAAW,CAAG7E,KAAK,CAACuE,cAAc,CAC9C,MAAO,CAAA+G,GAAG,EAAIF,MAAM,CACtB,CAAC,CAAC,CAEF,KAAM,CAAAG,YAAY,CAAGF,YAAY,CAAGlO,cAAc,CAACmC,OAAO,CAACjB,MAAM,CACjE,GAAIkN,YAAY,CAAG,CAAC,CAAE,CACpB/O,OAAO,CAACuB,GAAG,CAAC,UAAUwN,YAAY,mBAAmBpO,cAAc,CAACmC,OAAO,CAACjB,MAAM,EAAE,CAAC,CACvF,CACF,CAAC,CAED;AACA,KAAM,CAAA0C,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAA8D,WAAW,CAAGtF,IAAI,CAACC,GAAG,CAAC,CAAC,CAC9B,KAAM,CAAAgM,iBAAiB,CAAG,KAAK,CAAE;AAEjC,KAAM,CAAAH,YAAY,CAAGlO,cAAc,CAACmC,OAAO,CAACjB,MAAM,CAClD,KAAM,CAAAoN,aAAa,CAAG,EAAE,CAExBtO,cAAc,CAACmC,OAAO,CAAGnC,cAAc,CAACmC,OAAO,CAAC+B,MAAM,CAACrB,KAAK,EAAI,CAC9D,KAAM,CAAA0L,mBAAmB,CAAG7G,WAAW,CAAG7E,KAAK,CAACuE,cAAc,CAC9D,GAAImH,mBAAmB,CAAGF,iBAAiB,CAAE,CAC3CC,aAAa,CAACtB,IAAI,CAAC,CACjB7I,EAAE,CAAEtB,KAAK,CAACwJ,OAAO,CACjBvJ,IAAI,CAAED,KAAK,CAAC4I,SAAS,CACrB+C,YAAY,CAAE,CAACD,mBAAmB,CAAG,IAAI,EAAEvM,OAAO,CAAC,CAAC,CACtD,CAAC,CAAC,CACF,MAAO,MAAK,CAAE;AAChB,CACA,MAAO,KAAI,CAAE;AACf,CAAC,CAAC,CAEF,KAAM,CAAAoM,YAAY,CAAGF,YAAY,CAAGlO,cAAc,CAACmC,OAAO,CAACjB,MAAM,CACjE,GAAIkN,YAAY,CAAG,CAAC,CAAE,CACpB/O,OAAO,CAACuB,GAAG,CAAC,WAAWwN,YAAY,eAAe,CAAC,CACnDE,aAAa,CAAC5I,OAAO,CAAC7C,KAAK,EAAI,CAC7BxD,OAAO,CAACuB,GAAG,CAAC,WAAWiC,KAAK,CAACsB,EAAE,SAAStB,KAAK,CAACC,IAAI,YAAYD,KAAK,CAAC2L,YAAY,GAAG,CAAC,CACtF,CAAC,CAAC,CACFnP,OAAO,CAACuB,GAAG,CAAC,eAAeZ,cAAc,CAACmC,OAAO,CAACjB,MAAM,EAAE,CAAC,CAC7D,CACF,CAAC,CAED;AACA,KAAM,CAAAwM,iBAAiB,CAAGA,CAACe,IAAI,CAAEC,IAAI,CAAEC,IAAI,CAAEC,IAAI,GAAK,CACpD,GAAIH,IAAI,GAAK,CAAC,EAAIC,IAAI,GAAK,CAAC,EAAIC,IAAI,GAAK,CAAC,EAAIC,IAAI,GAAK,CAAC,CAAE,CACxD,MAAO,IAAG,CACZ,CACA,KAAM,CAAAC,CAAC,CAAG,OAAO,CACjB,KAAM,CAAAC,IAAI,CAAG,CAACH,IAAI,CAAGF,IAAI,EAAI5C,IAAI,CAACkD,EAAE,CAAG,GAAG,CAC1C,KAAM,CAAAC,IAAI,CAAG,CAACJ,IAAI,CAAGF,IAAI,EAAI7C,IAAI,CAACkD,EAAE,CAAG,GAAG,CAC1C,KAAM,CAAA9G,CAAC,CACL4D,IAAI,CAACoD,GAAG,CAACH,IAAI,CAAC,CAAC,CAAC,CAAGjD,IAAI,CAACoD,GAAG,CAACH,IAAI,CAAC,CAAC,CAAC,CACnCjD,IAAI,CAACqD,GAAG,CAACT,IAAI,CAAG5C,IAAI,CAACkD,EAAE,CAAG,GAAG,CAAC,CAAGlD,IAAI,CAACqD,GAAG,CAACP,IAAI,CAAG9C,IAAI,CAACkD,EAAE,CAAG,GAAG,CAAC,CAC/DlD,IAAI,CAACoD,GAAG,CAACD,IAAI,CAAC,CAAC,CAAC,CAAGnD,IAAI,CAACoD,GAAG,CAACD,IAAI,CAAC,CAAC,CAAC,CACrC,KAAM,CAAAG,CAAC,CAAG,CAAC,CAAGtD,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACwD,IAAI,CAACpH,CAAC,CAAC,CAAE4D,IAAI,CAACwD,IAAI,CAAC,CAAC,CAACpH,CAAC,CAAC,CAAC,CACtD,MAAO,CAAA4G,CAAC,CAAGM,CAAC,CACd,CAAC,CAED;AACA,KAAM,CAAA5C,0BAA0B,CAAGA,CAAC5K,GAAG,CAAEC,GAAG,GAAK,CAC/C;AACA,KAAM,CAAA0N,aAAa,CAAG3S,iBAAiB,CAAC2S,aAAa,EAAI,EAAE,CAC3D,GAAI,CAAAC,WAAW,CAAGC,QAAQ,CAC1B,GAAI,CAAAC,WAAW,CAAG,MAAM,CACxBH,aAAa,CAAC5J,OAAO,CAACgK,KAAK,EAAI,CAC7B,KAAM,CAAAC,QAAQ,CAAG5N,UAAU,CAAC2N,KAAK,CAAC1E,QAAQ,CAAC,CAC3C,KAAM,CAAA4E,QAAQ,CAAG7N,UAAU,CAAC2N,KAAK,CAACxE,SAAS,CAAC,CAC5C,KAAM,CAAA2E,IAAI,CAAGnC,iBAAiB,CAAC/L,GAAG,CAAEC,GAAG,CAAE+N,QAAQ,CAAEC,QAAQ,CAAC,CAC5D,GAAIC,IAAI,CAAGN,WAAW,CAAE,CACtBA,WAAW,CAAGM,IAAI,CAClBJ,WAAW,CAAGC,KAAK,CAAC9H,IAAI,CAC1B,CACF,CAAC,CAAC,CACF,MAAO,CAAA6H,WAAW,CACpB,CAAC,CAED;AACA,KAAM,CAAAK,mBAAmB,CAAIpN,OAAO,EAAK,CACvCrD,OAAO,CAACuB,GAAG,CAAC,OAAO,CAAE8B,OAAO,CAAC8B,WAAW,CAAE,KAAK,CAAE9B,OAAO,CAACjB,MAAM,CAAC,CAChEpD,kBAAkB,CAACqE,OAAO,CAAC,CAC7B,CAAC,CAED;AACAxH,SAAS,CAAC,IAAM,CACd;AACA,GAAIkD,eAAe,CAAE,CACnB,KAAM,CAAA2R,sBAAsB,CAAG/R,QAAQ,CAACsH,IAAI,CAACrB,CAAC,EAAIA,CAAC,CAACE,EAAE,GAAK/F,eAAe,CAAC+F,EAAE,CAAC,CAC9E,GAAI4L,sBAAsB,GACrBA,sBAAsB,CAACtO,MAAM,GAAKrD,eAAe,CAACqD,MAAM,EACxDsO,sBAAsB,CAACrO,KAAK,GAAKtD,eAAe,CAACsD,KAAK,EACtDqO,sBAAsB,CAACpO,GAAG,GAAKvD,eAAe,CAACuD,GAAG,EAClDoO,sBAAsB,CAACnO,GAAG,GAAKxD,eAAe,CAACwD,GAAG,EAClDmO,sBAAsB,CAAClO,OAAO,GAAKzD,eAAe,CAACyD,OAAO,CAAC,CAAE,CAChExC,OAAO,CAACuB,GAAG,CAAC,UAAUxC,eAAe,CAACoG,WAAW,OAAO,CAC7C,OAAOpG,eAAe,CAACqD,MAAM,OAAOsO,sBAAsB,CAACtO,MAAM,EAAE,CAAC,CAC/EpD,kBAAkB,CAAC0R,sBAAsB,CAAC,CAC5C,CACF,CACF,CAAC,CAAE,CAAC/R,QAAQ,CAAEI,eAAe,CAAC,CAAC,CAE/B;AACA,KAAM,CAAA4R,cAAc,CAAG,CACrB,CACE5H,KAAK,CAAE,KAAK,CACZ6H,SAAS,CAAE,OAAO,CAClB/J,GAAG,CAAE,OAAO,CACZgK,KAAK,CAAE,KACT,CAAC,CACD,CACE9H,KAAK,CAAE,IAAI,CACX6H,SAAS,CAAE,QAAQ,CACnB/J,GAAG,CAAE,QAAQ,CACbgK,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE1O,MAAM,eACZ5E,IAAA,CAAChB,KAAK,EACJ4F,MAAM,CAAEA,MAAM,GAAK,QAAQ,CAAG,SAAS,CAAG,OAAQ,CAClD4G,IAAI,CAAE5G,MAAM,GAAK,QAAQ,CAAG,IAAI,CAAG,IAAK,CACzC,CAEL,CAAC,CACD,CACE2G,KAAK,CAAE,IAAI,CACX6H,SAAS,CAAE,OAAO,CAClB/J,GAAG,CAAE,OAAO,CACZgK,KAAK,CAAE,KAAK,CACZC,MAAM,CAAEzO,KAAK,EAAI,GAAG,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAGA,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAAGN,KAAK,OAC1E,CAAC,CACF,CAED;AACA,KAAM,CAAA0O,eAAe,CAAGA,CAAA,gBACtBvT,IAAA,CAACpB,IAAI,EACHqK,IAAI,CAAC,OAAO,CACZuK,UAAU,CAAEnS,MAAO,CACnBoS,UAAU,CAAEvI,IAAI,eACdlL,IAAA,CAACpB,IAAI,CAAC8U,IAAI,EAACC,KAAK,CAAE,CAAEC,OAAO,CAAE,OAAQ,CAAE,CAAAC,QAAA,cACrC7T,IAAA,CAACpB,IAAI,CAAC8U,IAAI,CAACI,IAAI,EACbvI,KAAK,cACHrL,KAAA,QAAKyT,KAAK,CAAE,CAAE9H,QAAQ,CAAE,MAAM,CAAEkI,YAAY,CAAE,KAAM,CAAE,CAAAF,QAAA,eACpD7T,IAAA,SAAM2T,KAAK,CAAE,CAAE3I,KAAK,CAAEE,IAAI,CAACF,KAAK,CAAEgJ,WAAW,CAAE,KAAM,CAAE,CAAAH,QAAA,CACpD3I,IAAI,CAACjF,IAAI,CACN,CAAC,cACPjG,IAAA,SAAM2T,KAAK,CAAE,CAAE3I,KAAK,CAAE,MAAM,CAAEa,QAAQ,CAAE,MAAO,CAAE,CAAAgI,QAAA,CAC9C3I,IAAI,CAAC6E,IAAI,CACN,CAAC,EACJ,CACN,CACDR,WAAW,cACTrP,KAAA,QAAKyT,KAAK,CAAE,CAAE9H,QAAQ,CAAE,MAAM,CAAEb,KAAK,CAAE,MAAO,CAAE,CAAA6I,QAAA,eAE9C3T,KAAA,QAAA2T,QAAA,EAAK,gBAAI,CAAC3I,IAAI,CAACuE,IAAI,EAAI,MAAM,EAAM,CAAC,cACpCvP,KAAA,QAAA2T,QAAA,EAAK,gBAAI,CAAC3I,IAAI,CAAC8E,QAAQ,CACrB,GAAG9E,IAAI,CAAC8E,QAAQ,CAAC7B,QAAQ,CAAChJ,OAAO,CAAC,CAAC,CAAC,KAAK+F,IAAI,CAAC8E,QAAQ,CAAC3B,SAAS,CAAClJ,OAAO,CAAC,CAAC,CAAC,EAAE,CAC7E,MAAM,EACH,CAAC,EACH,CACN,CACF,CAAC,CACO,CACX,CACFwO,KAAK,CAAE,CACLM,SAAS,CAAE,mBAAmB,CAC9BC,SAAS,CAAE,MACb,CAAE,CACH,CACF,CAED;AACA7V,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8V,kBAAkB,CAAGzN,WAAW,CAAC,IAAM,CAC3ClE,OAAO,CAACuB,GAAG,CAAC,gBAAgB,CAAC,CAC7BmD,uBAAuB,CAAC,CAAC,CAC3B,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX,MAAO,IAAMP,aAAa,CAACwN,kBAAkB,CAAC,CAChD,CAAC,CAAE,CAAChT,QAAQ,CAAC,CAAC,CAEd,mBACEnB,IAAA,CAACjB,IAAI,EAACqV,QAAQ,CAAEnT,OAAQ,CAACoT,GAAG,CAAC,uBAAQ,CAAAR,QAAA,cACnC3T,KAAA,CAACI,aAAa,EAAAuT,QAAA,eAEZ3T,KAAA,CAACP,kBAAkB,EACjByN,QAAQ,CAAC,MAAM,CACfkH,SAAS,CAAE3T,aAAc,CACzB4T,UAAU,CAAEA,CAAA,GAAM5R,gBAAgB,CAAC,CAAChC,aAAa,CAAE,CAAAkT,QAAA,eAGnD7T,IAAA,CAACa,QAAQ,EAAC0K,KAAK,CAAC,4CAAS,CAACiJ,QAAQ,CAAE,KAAM,CAAC1T,MAAM,CAAC,OAAO,CAAA+S,QAAA,cACvD3T,KAAA,CAAC1B,GAAG,EAACiW,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAAZ,QAAA,eAClB7T,IAAA,CAACvB,GAAG,EAACiW,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjE7T,IAAA,CAACe,gBAAgB,EACfwK,KAAK,CAAC,0BAAM,CACZ/E,KAAK,CAAE/E,KAAK,CAACE,aAAc,CAC3BkT,UAAU,CAAE,CAAE7J,KAAK,CAAE,SAAS,CAAC2J,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CACxE;AAAA,CACD,CAAC,CACC,CAAC,cACN5U,IAAA,CAACvB,GAAG,EAACiW,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjE7T,IAAA,CAACe,gBAAgB,EACfwK,KAAK,CAAC,0BAAM,CACZ/E,KAAK,CAAE/E,KAAK,CAACG,cACb;AAAA,CACAiT,UAAU,CAAE,CAAE7J,KAAK,CAAE,SAAS,CAAE2J,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAQ,CAAE,CAC3E,CAAC,CACC,CAAC,cACN5U,IAAA,CAACvB,GAAG,EAACiW,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjE7T,IAAA,CAACe,gBAAgB,EACfwK,KAAK,CAAC,0BAAM,CACZ/E,KAAK,CAAE/E,KAAK,CAACI,eACb;AAAA,CACAgT,UAAU,CAAE,CAAE7J,KAAK,CAAE,SAAS,CAAE2J,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CAAE,CAC5E,CAAC,CACC,CAAC,cACN5U,IAAA,CAACvB,GAAG,EAACiW,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjE7T,IAAA,CAACe,gBAAgB,EACfwK,KAAK,CAAC,0BAAM,CACZ/E,KAAK,CAAE/E,KAAK,CAACK,YAAa,CAC1B+S,UAAU,CAAE,CAAE7J,KAAK,CAAE,SAAS,CAAC2J,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CAAE,CAC3E,CAAC,CACC,CAAC,cACN5U,IAAA,CAACvB,GAAG,EAACiW,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjE7T,IAAA,CAACe,gBAAgB,EACfwK,KAAK,CAAC,0BAAM,CACZ/E,KAAK,CAAE/E,KAAK,CAACM,aACb;AAAA,CACA8S,UAAU,CAAE,CAAE7J,KAAK,CAAE,SAAS,CAAC2J,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CAAE,CAC3E,CAAC,CACC,CAAC,cACN5U,IAAA,CAACvB,GAAG,EAACiW,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjE7T,IAAA,CAACe,gBAAgB,EACfwK,KAAK,CAAC,0BAAM,CACZ/E,KAAK,CAAE/E,KAAK,CAACO,cACb;AAAA,CACA6S,UAAU,CAAE,CAAE7J,KAAK,CAAE,SAAS,CAAC2J,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CAAE,CAC3E,CAAC,CACC,CAAC,EACH,CAAC,CACE,CAAC,cAGX5U,IAAA,CAACa,QAAQ,EACP0K,KAAK,CAAC,sCAAQ,CACdiJ,QAAQ,CAAE,KAAM,CAChB1T,MAAM,CAAC,kBACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CAAA+S,QAAA,CAECN,eAAe,CAAC,CAAC,CACV,CAAC,cAGXvT,IAAA,CAACa,QAAQ,EACP0K,KAAK,CAAC,sCAAQ,CACdiJ,QAAQ,CAAE,KAAM,CAChB1T,MAAM,CAAC,kBAAkB,CACzBgU,KAAK,cACH9U,IAAA,CAACd,MAAM,EACLsH,KAAK,CAAE3D,iBAAkB,CACzBkS,QAAQ,CAAExO,qBAAsB,CAChCyO,QAAQ,CAAEzO,qBAAsB,CAChC0C,IAAI,CAAC,OAAO,CACZ0K,KAAK,CAAE,CAAEN,KAAK,CAAE,EAAG,CAAE,CACrB4B,OAAO,CAAE,CACP,CAAEzO,KAAK,CAAE,IAAI,CAAE2G,KAAK,CAAE,KAAM,CAAC,CAC7B,CAAE3G,KAAK,CAAE,KAAK,CAAE2G,KAAK,CAAE,IAAK,CAAC,CAC7B,CAAE3G,KAAK,CAAE,IAAI,CAAE2G,KAAK,CAAE,IAAK,CAAC,CAC5B,CAAE3G,KAAK,CAAE,KAAK,CAAE2G,KAAK,CAAE,IAAK,CAAC,CAC7B,CACH,CACF,CAAA0G,QAAA,cAED7T,IAAA,QAAKkV,GAAG,CAAExS,aAAc,CAACiR,KAAK,CAAE,CAAE7S,MAAM,CAAE,MAAM,CAAEuS,KAAK,CAAE,MAAO,CAAE,CAAM,CAAC,CACjE,CAAC,EACO,CAAC,cAGrBrT,IAAA,CAACS,WAAW,EAACE,aAAa,CAAEA,aAAc,CAACC,cAAc,CAAEA,cAAe,CAE7D,CAAC,cAGdV,KAAA,CAACP,kBAAkB,EACjByN,QAAQ,CAAC,OAAO,CAChBkH,SAAS,CAAE1T,cAAe,CAC1B2T,UAAU,CAAEA,CAAA,GAAM3R,iBAAiB,CAAC,CAAChC,cAAc,CAAE,CAAAiT,QAAA,eAGrD7T,IAAA,CAACa,QAAQ,EAAC0K,KAAK,CAAC,0BAAM,CAACiJ,QAAQ,CAAE,KAAM,CAAC1T,MAAM,CAAC,KAAK,CAAA+S,QAAA,cAClD7T,IAAA,CAACnB,KAAK,EACJ2U,UAAU,CAAErS,QAAS,CACrBgU,OAAO,CAAEhC,cAAe,CACxBiC,MAAM,CAAC,IAAI,CACXC,UAAU,CAAE,KAAM,CAClBpM,IAAI,CAAC,OAAO,CACZqM,MAAM,CAAE,CAAEC,CAAC,CAAE,GAAI,CAAE,CACnBC,KAAK,CAAGC,MAAM,GAAM,CAClBC,OAAO,CAAEA,CAAA,GAAMzC,mBAAmB,CAACwC,MAAM,CAAC,CAC1C9B,KAAK,CAAE,CACLgC,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,CAAArU,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE+F,EAAE,IAAKmO,MAAM,CAACnO,EAAE,CAAG,SAAS,CAAG,aAAa,CACzEuE,QAAQ,CAAE,MAAM,CAChB+H,OAAO,CAAE,SACX,CACF,CAAC,CAAE,CACJ,CAAC,CACM,CAAC,cAGX5T,IAAA,CAACa,QAAQ,EAAC0K,KAAK,CAAC,sCAAQ,CAACiJ,QAAQ,CAAE,KAAM,CAAC1T,MAAM,CAAC,KAAK,CAAA+S,QAAA,CACnDtS,eAAe,cACdrB,KAAA,CAACpB,YAAY,EACX0V,QAAQ,MACRqB,MAAM,CAAE,CAAE,CACV5M,IAAI,CAAC,OAAO,CACZ6M,MAAM,CAAE,CACN3I,KAAK,CAAE,CAAEtB,QAAQ,CAAE,MAAM,CAAE+H,OAAO,CAAE,SAAU,CAAC,CAC/CmC,OAAO,CAAE,CAAElK,QAAQ,CAAE,MAAM,CAAE+H,OAAO,CAAE,SAAU,CAClD,CAAE,CAAAC,QAAA,eAEF7T,IAAA,CAAClB,YAAY,CAAC4U,IAAI,EAACvG,KAAK,CAAC,oBAAK,CAAA0G,QAAA,CAAEtS,eAAe,CAACoG,WAAW,CAAoB,CAAC,cAChF3H,IAAA,CAAClB,YAAY,CAAC4U,IAAI,EAACvG,KAAK,CAAC,cAAI,CAAA0G,QAAA,cAC3B7T,IAAA,CAAChB,KAAK,EACJ4F,MAAM,CAAErD,eAAe,CAACqD,MAAM,GAAK,QAAQ,CAAG,SAAS,CAAG,OAAQ,CAClE4G,IAAI,CAAEjK,eAAe,CAACqD,MAAM,GAAK,QAAQ,CAAG,IAAI,CAAG,IAAK,CACzD,CAAC,CACe,CAAC,cACpB5E,IAAA,CAAClB,YAAY,CAAC4U,IAAI,EAACvG,KAAK,CAAC,cAAI,CAAA0G,QAAA,CAC1BtS,eAAe,CAACqD,MAAM,GAAK,QAAQ,CAAGrD,eAAe,CAACwD,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAC5D,CAAC,cACpBnF,IAAA,CAAClB,YAAY,CAAC4U,IAAI,EAACvG,KAAK,CAAC,cAAI,CAAA0G,QAAA,CAC1BtS,eAAe,CAACqD,MAAM,GAAK,QAAQ,CAAGrD,eAAe,CAACuD,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAC5D,CAAC,cACpBnF,IAAA,CAAClB,YAAY,CAAC4U,IAAI,EAACvG,KAAK,CAAC,cAAI,CAAA0G,QAAA,CAC1BtS,eAAe,CAACqD,MAAM,GAAK,QAAQ,CAClC,GAAG,MAAO,CAAArD,eAAe,CAACsD,KAAK,GAAK,QAAQ,CAAGtD,eAAe,CAACsD,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAAG5D,eAAe,CAACsD,KAAK,OAAO,CAC9G,KAAK,CACU,CAAC,cACpB7E,IAAA,CAAClB,YAAY,CAAC4U,IAAI,EAACvG,KAAK,CAAC,oBAAK,CAAA0G,QAAA,CAC3BtS,eAAe,CAACqD,MAAM,GAAK,QAAQ,CAClC,GAAG,MAAO,CAAArD,eAAe,CAACyD,OAAO,GAAK,QAAQ,CAAGzD,eAAe,CAACyD,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,CAAG5D,eAAe,CAACyD,OAAO,GAAG,CAChH,KAAK,CACU,CAAC,EACR,CAAC,cAEfhF,IAAA,MAAG2T,KAAK,CAAE,CAAE9H,QAAQ,CAAE,MAAO,CAAE,CAAAgI,QAAA,CAAC,oEAAW,CAAG,CAC/C,CACO,CAAC,EACO,CAAC,EACR,CAAC,CACZ,CAAC,CAEX,CAAC,CAED,cAAe,CAAA7S,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}