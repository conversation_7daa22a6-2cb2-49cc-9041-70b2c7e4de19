{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"key\", \"forceRender\", \"style\", \"className\", \"destroyInactiveTabPane\"];\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport * as React from 'react';\nimport TabContext from \"../TabContext\";\nimport TabPane from \"./TabPane\";\nvar TabPanelList = function TabPanelList(props) {\n  var id = props.id,\n    activeKey = props.activeKey,\n    animated = props.animated,\n    tabPosition = props.tabPosition,\n    destroyInactiveTabPane = props.destroyInactiveTabPane;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var tabPaneAnimated = animated.tabPane;\n  var tabPanePrefixCls = \"\".concat(prefixCls, \"-tabpane\");\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content-holder\"))\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), \"\".concat(prefixCls, \"-content-\").concat(tabPosition), _defineProperty({}, \"\".concat(prefixCls, \"-content-animated\"), tabPaneAnimated))\n  }, tabs.map(function (item) {\n    var key = item.key,\n      forceRender = item.forceRender,\n      paneStyle = item.style,\n      paneClassName = item.className,\n      itemDestroyInactiveTabPane = item.destroyInactiveTabPane,\n      restTabProps = _objectWithoutProperties(item, _excluded);\n    var active = key === activeKey;\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      key: key,\n      visible: active,\n      forceRender: forceRender,\n      removeOnLeave: !!(destroyInactiveTabPane || itemDestroyInactiveTabPane),\n      leavedClassName: \"\".concat(tabPanePrefixCls, \"-hidden\")\n    }, animated.tabPaneMotion), function (_ref, ref) {\n      var motionStyle = _ref.style,\n        motionClassName = _ref.className;\n      return /*#__PURE__*/React.createElement(TabPane, _extends({}, restTabProps, {\n        prefixCls: tabPanePrefixCls,\n        id: id,\n        tabKey: key,\n        animated: tabPaneAnimated,\n        active: active,\n        style: _objectSpread(_objectSpread({}, paneStyle), motionStyle),\n        className: classNames(paneClassName, motionClassName),\n        ref: ref\n      }));\n    });\n  })));\n};\nexport default TabPanelList;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_objectWithoutProperties", "_defineProperty", "_excluded", "classNames", "CSSMotion", "React", "TabContext", "TabPane", "TabPanelList", "props", "id", "active<PERSON><PERSON>", "animated", "tabPosition", "destroyInactiveTabPane", "_React$useContext", "useContext", "prefixCls", "tabs", "tabPaneAnimated", "tabPane", "tabPanePrefixCls", "concat", "createElement", "className", "map", "item", "key", "forceRender", "paneStyle", "style", "paneClassName", "itemDestroyInactiveTabPane", "restTabProps", "active", "visible", "removeOnLeave", "leavedClassName", "tabPaneMotion", "_ref", "ref", "motionStyle", "motionClassName", "tabKey"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-tabs/es/TabPanelList/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"key\", \"forceRender\", \"style\", \"className\", \"destroyInactiveTabPane\"];\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport * as React from 'react';\nimport TabContext from \"../TabContext\";\nimport TabPane from \"./TabPane\";\nvar TabPanelList = function TabPanelList(props) {\n  var id = props.id,\n    activeKey = props.activeKey,\n    animated = props.animated,\n    tabPosition = props.tabPosition,\n    destroyInactiveTabPane = props.destroyInactiveTabPane;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var tabPaneAnimated = animated.tabPane;\n  var tabPanePrefixCls = \"\".concat(prefixCls, \"-tabpane\");\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content-holder\"))\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-content\"), \"\".concat(prefixCls, \"-content-\").concat(tabPosition), _defineProperty({}, \"\".concat(prefixCls, \"-content-animated\"), tabPaneAnimated))\n  }, tabs.map(function (item) {\n    var key = item.key,\n      forceRender = item.forceRender,\n      paneStyle = item.style,\n      paneClassName = item.className,\n      itemDestroyInactiveTabPane = item.destroyInactiveTabPane,\n      restTabProps = _objectWithoutProperties(item, _excluded);\n    var active = key === activeKey;\n    return /*#__PURE__*/React.createElement(CSSMotion, _extends({\n      key: key,\n      visible: active,\n      forceRender: forceRender,\n      removeOnLeave: !!(destroyInactiveTabPane || itemDestroyInactiveTabPane),\n      leavedClassName: \"\".concat(tabPanePrefixCls, \"-hidden\")\n    }, animated.tabPaneMotion), function (_ref, ref) {\n      var motionStyle = _ref.style,\n        motionClassName = _ref.className;\n      return /*#__PURE__*/React.createElement(TabPane, _extends({}, restTabProps, {\n        prefixCls: tabPanePrefixCls,\n        id: id,\n        tabKey: key,\n        animated: tabPaneAnimated,\n        active: active,\n        style: _objectSpread(_objectSpread({}, paneStyle), motionStyle),\n        className: classNames(paneClassName, motionClassName),\n        ref: ref\n      }));\n    });\n  })));\n};\nexport default TabPanelList;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,SAAS,GAAG,CAAC,KAAK,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,wBAAwB,CAAC;AACtF,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,OAAO,MAAM,WAAW;AAC/B,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,KAAK,EAAE;EAC9C,IAAIC,EAAE,GAAGD,KAAK,CAACC,EAAE;IACfC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,WAAW,GAAGJ,KAAK,CAACI,WAAW;IAC/BC,sBAAsB,GAAGL,KAAK,CAACK,sBAAsB;EACvD,IAAIC,iBAAiB,GAAGV,KAAK,CAACW,UAAU,CAACV,UAAU,CAAC;IAClDW,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,IAAI,GAAGH,iBAAiB,CAACG,IAAI;EAC/B,IAAIC,eAAe,GAAGP,QAAQ,CAACQ,OAAO;EACtC,IAAIC,gBAAgB,GAAG,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,UAAU,CAAC;EACvD,OAAO,aAAaZ,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAErB,UAAU,CAAC,EAAE,CAACmB,MAAM,CAACL,SAAS,EAAE,iBAAiB,CAAC;EAC/D,CAAC,EAAE,aAAaZ,KAAK,CAACkB,aAAa,CAAC,KAAK,EAAE;IACzCC,SAAS,EAAErB,UAAU,CAAC,EAAE,CAACmB,MAAM,CAACL,SAAS,EAAE,UAAU,CAAC,EAAE,EAAE,CAACK,MAAM,CAACL,SAAS,EAAE,WAAW,CAAC,CAACK,MAAM,CAACT,WAAW,CAAC,EAAEZ,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACqB,MAAM,CAACL,SAAS,EAAE,mBAAmB,CAAC,EAAEE,eAAe,CAAC;EAChM,CAAC,EAAED,IAAI,CAACO,GAAG,CAAC,UAAUC,IAAI,EAAE;IAC1B,IAAIC,GAAG,GAAGD,IAAI,CAACC,GAAG;MAChBC,WAAW,GAAGF,IAAI,CAACE,WAAW;MAC9BC,SAAS,GAAGH,IAAI,CAACI,KAAK;MACtBC,aAAa,GAAGL,IAAI,CAACF,SAAS;MAC9BQ,0BAA0B,GAAGN,IAAI,CAACZ,sBAAsB;MACxDmB,YAAY,GAAGjC,wBAAwB,CAAC0B,IAAI,EAAExB,SAAS,CAAC;IAC1D,IAAIgC,MAAM,GAAGP,GAAG,KAAKhB,SAAS;IAC9B,OAAO,aAAaN,KAAK,CAACkB,aAAa,CAACnB,SAAS,EAAEN,QAAQ,CAAC;MAC1D6B,GAAG,EAAEA,GAAG;MACRQ,OAAO,EAAED,MAAM;MACfN,WAAW,EAAEA,WAAW;MACxBQ,aAAa,EAAE,CAAC,EAAEtB,sBAAsB,IAAIkB,0BAA0B,CAAC;MACvEK,eAAe,EAAE,EAAE,CAACf,MAAM,CAACD,gBAAgB,EAAE,SAAS;IACxD,CAAC,EAAET,QAAQ,CAAC0B,aAAa,CAAC,EAAE,UAAUC,IAAI,EAAEC,GAAG,EAAE;MAC/C,IAAIC,WAAW,GAAGF,IAAI,CAACT,KAAK;QAC1BY,eAAe,GAAGH,IAAI,CAACf,SAAS;MAClC,OAAO,aAAanB,KAAK,CAACkB,aAAa,CAAChB,OAAO,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEmC,YAAY,EAAE;QAC1EhB,SAAS,EAAEI,gBAAgB;QAC3BX,EAAE,EAAEA,EAAE;QACNiC,MAAM,EAAEhB,GAAG;QACXf,QAAQ,EAAEO,eAAe;QACzBe,MAAM,EAAEA,MAAM;QACdJ,KAAK,EAAE/B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8B,SAAS,CAAC,EAAEY,WAAW,CAAC;QAC/DjB,SAAS,EAAErB,UAAU,CAAC4B,aAAa,EAAEW,eAAe,CAAC;QACrDF,GAAG,EAAEA;MACP,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,eAAehC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}