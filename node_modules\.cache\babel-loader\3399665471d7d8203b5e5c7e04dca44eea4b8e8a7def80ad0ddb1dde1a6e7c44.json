{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\layout\\\\MainLayout.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, createContext } from 'react';\nimport { Layout, Menu, Button, message, Space, Typography, Card, Avatar } from 'antd';\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\nimport { BarChartOutlined, DesktopOutlined, CarOutlined, SettingOutlined, LogoutOutlined, UserOutlined } from '@ant-design/icons';\nimport CampusModel from '../../components/CampusModel';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Content\n} = Layout;\nconst {\n  Text\n} = Typography;\n\n// 创建Context\nexport const CampusModelContext = /*#__PURE__*/createContext(null);\n\n// 样式组件\nconst MapContainer = styled.div`\n  position: fixed;\n  top: 64px;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1;\n  pointer-events: auto;\n  background: #000;\n  overflow: hidden;\n  height: calc(100vh - 64px);\n  \n  & canvas {\n    display: block !important;\n    width: 100% !important;\n    height: 100% !important;\n    touch-action: none !important;\n  }\n`;\n_c = MapContainer;\nconst ContentWrapper = styled.div`\n  position: relative;\n  z-index: 11;\n  pointer-events: none;\n  width: 100%;\n  height: 100%;\n  \n  .ant-layout-sider,\n  .collapsible-sidebar,\n  .ant-card,\n  .ant-list,\n  .ant-table-wrapper,\n  .ant-descriptions,\n  .ant-statistic,\n  .chart-container {\n    pointer-events: auto;\n    background: rgba(255, 255, 255, 0.9);\n  }\n\n  .ant-layout-sider *,\n  .collapsible-sidebar * {\n    pointer-events: auto;\n  }\n`;\n_c2 = ContentWrapper;\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 64px);\n  overflow: hidden;\n  margin: 0;\n  padding: 0;\n`;\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  position: relative;\n  z-index: 1;\n`;\nconst SidebarContainer = styled.div`\n  position: relative;\n  width: ${props => props.$collapsed ? '0' : '320px'};\n  height: 100%;\n  transition: all 0.3s ease;\n  overflow: visible;\n  pointer-events: auto;\n  background: rgba(255, 255, 255, 0.9);\n  border-${props => props.$position === 'left' ? 'right' : 'left'}: ${props => props.$collapsed ? '0' : '1px'} solid #f0f0f0;\n  flex-shrink: 0;\n  margin: 0;\n  padding: 0;\n\n  .collapse-button {\n    position: absolute;\n    top: 50%;\n    ${props => props.$position === 'left' ? 'right: -24px' : 'left: -24px'};\n    transform: translateY(-50%);\n    width: 24px;\n    height: 80px;\n    padding: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background: rgba(255, 255, 255, 0.95);\n    border: 1px solid #f0f0f0;\n    border-radius: ${props => props.$position === 'left' ? '0 4px 4px 0' : '4px 0 0 4px'};\n    z-index: 20;\n    pointer-events: auto;\n    box-shadow: ${props => props.$position === 'left' ? '2px 0 8px rgba(0, 0, 0, 0.15)' : '-2px 0 8px rgba(0, 0, 0, 0.15)'};\n\n    &:hover {\n      background: #fff !important;\n      border-color: #d9d9d9 !important;\n      color: #1890ff !important;\n    }\n\n    &:focus {\n      background: #fff !important;\n      border-color: #d9d9d9 !important;\n    }\n\n    .anticon {\n      color: rgba(0, 0, 0, 0.45);\n      font-size: 12px;\n    }\n\n    &:hover .anticon {\n      color: #1890ff;\n    }\n  }\n`;\nconst InfoCard = styled(Card)`\n  margin: 0;\n  height: ${props => props.height || 'auto'};\n  border-radius: 0;\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px);\n    overflow-y: auto;\n  }\n`;\nconst MainLayout = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [currentUser, setCurrentUser] = useState(null);\n  const [username, setUsername] = useState('');\n  useEffect(() => {\n    const userInfo = localStorage.getItem('user');\n    if (userInfo) {\n      try {\n        var _userData$user, _userData$user2;\n        const userData = JSON.parse(userInfo);\n        // 兼容不同的用户数据格式\n        setUsername(userData.username || (// 直接存储的用户名\n        (_userData$user = userData.user) === null || _userData$user === void 0 ? void 0 : _userData$user.username) || (// 存储在user对象中的用户名\n        (_userData$user2 = userData.user) === null || _userData$user2 === void 0 ? void 0 : _userData$user2.name) ||\n        // 用户的显示名称\n        '用户' // 默认值\n        );\n      } catch (error) {\n        console.error('解析用户信息失败:', error);\n        setUsername('用户');\n      }\n    }\n  }, []);\n  const menuItems = [{\n    key: 'real-time-traffic',\n    icon: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 13\n    }, this),\n    label: '实时交通'\n  }, {\n    key: 'device-status',\n    icon: /*#__PURE__*/_jsxDEV(DesktopOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 13\n    }, this),\n    label: '设备状态'\n  }, {\n    key: 'road-monitoring',\n    icon: /*#__PURE__*/_jsxDEV(CarOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 13\n    }, this),\n    label: '路网监控'\n  }, {\n    key: 'system-management',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 13\n    }, this),\n    label: '系统管理'\n  }];\n  const handleMenuClick = e => {\n    navigate(`/${e.key}`);\n  };\n  const handleLogout = () => {\n    // 显示加载提示\n    message.loading('正在安全退出系统...', 1);\n\n    // 触发一个自定义事件，通知CampusModel组件清理资源\n    const cleanupEvent = new CustomEvent('cleanupBeforeLogout');\n    window.dispatchEvent(cleanupEvent);\n\n    // 添加延迟确保组件有足够时间清理\n    setTimeout(() => {\n      try {\n        // 清除所有用户相关的数据\n        localStorage.clear();\n        // 跳转到登录页面\n        navigate('/login', {\n          replace: true\n        });\n        // 显示退出成功提示\n        message.success('已成功退出登录');\n      } catch (error) {\n        console.error('退出登录时发生错误:', error);\n        message.error('退出登录失败，请重试');\n      }\n    }, 1000);\n  };\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    style: {\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Header, {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        background: '#fff',\n        padding: '0 24px',\n        borderBottom: '1px solid #f0f0f0',\n        position: 'relative',\n        zIndex: 12\n      },\n      children: [/*#__PURE__*/_jsxDEV(Menu, {\n        mode: \"horizontal\",\n        selectedKeys: [location.pathname.split('/')[1]],\n        onClick: handleMenuClick,\n        items: menuItems,\n        style: {\n          flex: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 27\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"username\",\n            children: username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 19\n          }, this),\n          onClick: handleLogout,\n          children: \"\\u9000\\u51FA\\u767B\\u5F55\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MapContainer, {\n      children: /*#__PURE__*/_jsxDEV(CampusModel, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Content, {\n      style: {\n        padding: '0',\n        position: 'relative',\n        background: 'transparent',\n        pointerEvents: 'none',\n        zIndex: 11\n      },\n      children: /*#__PURE__*/_jsxDEV(ContentWrapper, {\n        children: /*#__PURE__*/_jsxDEV(CampusModelContext.Provider, {\n          value: true,\n          children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 231,\n    columnNumber: 5\n  }, this);\n};\n_s(MainLayout, \"mtTZi0LhvxvI9yrsYaJeH36JoCI=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c3 = MainLayout;\nexport default MainLayout;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"MapContainer\");\n$RefreshReg$(_c2, \"ContentWrapper\");\n$RefreshReg$(_c3, \"MainLayout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "createContext", "Layout", "<PERSON><PERSON>", "<PERSON><PERSON>", "message", "Space", "Typography", "Card", "Avatar", "Outlet", "useNavigate", "useLocation", "BarChartOutlined", "DesktopOutlined", "CarOutlined", "SettingOutlined", "LogoutOutlined", "UserOutlined", "CampusModel", "styled", "jsxDEV", "_jsxDEV", "Header", "Content", "Text", "CampusModelContext", "MapContainer", "div", "_c", "ContentWrapper", "_c2", "<PERSON><PERSON><PERSON><PERSON>", "MainContent", "SidebarContainer", "props", "$collapsed", "$position", "InfoCard", "height", "MainLayout", "_s", "navigate", "location", "currentUser", "setCurrentUser", "username", "setUsername", "userInfo", "localStorage", "getItem", "_userData$user", "_userData$user2", "userData", "JSON", "parse", "user", "name", "error", "console", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "handleMenuClick", "e", "handleLogout", "loading", "cleanupEvent", "CustomEvent", "window", "dispatchEvent", "setTimeout", "clear", "replace", "success", "style", "minHeight", "children", "display", "alignItems", "background", "padding", "borderBottom", "position", "zIndex", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "split", "onClick", "items", "flex", "className", "pointerEvents", "Provider", "value", "_c3", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/layout/MainLayout.jsx"], "sourcesContent": ["import React, { useState, useEffect, createContext } from 'react';\r\nimport { Layout, Menu, Button, message, Space, Typography, Card, Avatar } from 'antd';\r\nimport { Outlet, useNavigate, useLocation } from 'react-router-dom';\r\nimport {\r\n  BarChartOutlined,\r\n  DesktopOutlined,\r\n  CarOutlined,\r\n  SettingOutlined,\r\n  LogoutOutlined,\r\n  UserOutlined\r\n} from '@ant-design/icons';\r\nimport CampusModel from '../../components/CampusModel';\r\nimport styled from 'styled-components';\r\n\r\nconst { Header, Content } = Layout;\r\nconst { Text } = Typography;\r\n\r\n// 创建Context\r\nexport const CampusModelContext = createContext(null);\r\n\r\n// 样式组件\r\nconst MapContainer = styled.div`\r\n  position: fixed;\r\n  top: 64px;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 1;\r\n  pointer-events: auto;\r\n  background: #000;\r\n  overflow: hidden;\r\n  height: calc(100vh - 64px);\r\n  \r\n  & canvas {\r\n    display: block !important;\r\n    width: 100% !important;\r\n    height: 100% !important;\r\n    touch-action: none !important;\r\n  }\r\n`;\r\n\r\nconst ContentWrapper = styled.div`\r\n  position: relative;\r\n  z-index: 11;\r\n  pointer-events: none;\r\n  width: 100%;\r\n  height: 100%;\r\n  \r\n  .ant-layout-sider,\r\n  .collapsible-sidebar,\r\n  .ant-card,\r\n  .ant-list,\r\n  .ant-table-wrapper,\r\n  .ant-descriptions,\r\n  .ant-statistic,\r\n  .chart-container {\r\n    pointer-events: auto;\r\n    background: rgba(255, 255, 255, 0.9);\r\n  }\r\n\r\n  .ant-layout-sider *,\r\n  .collapsible-sidebar * {\r\n    pointer-events: auto;\r\n  }\r\n`;\r\n\r\nconst PageContainer = styled.div`\r\n  display: flex;\r\n  height: calc(100vh - 64px);\r\n  overflow: hidden;\r\n  margin: 0;\r\n  padding: 0;\r\n`;\r\n\r\nconst MainContent = styled.div`\r\n  flex: 1;\r\n  height: 100%;\r\n  overflow: hidden;\r\n  padding: 0;\r\n  margin: 0;\r\n  position: relative;\r\n  z-index: 1;\r\n`;\r\n\r\nconst SidebarContainer = styled.div`\r\n  position: relative;\r\n  width: ${props => props.$collapsed ? '0' : '320px'};\r\n  height: 100%;\r\n  transition: all 0.3s ease;\r\n  overflow: visible;\r\n  pointer-events: auto;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-${props => props.$position === 'left' ? 'right' : 'left'}: ${props => props.$collapsed ? '0' : '1px'} solid #f0f0f0;\r\n  flex-shrink: 0;\r\n  margin: 0;\r\n  padding: 0;\r\n\r\n  .collapse-button {\r\n    position: absolute;\r\n    top: 50%;\r\n    ${props => props.$position === 'left' ? 'right: -24px' : 'left: -24px'};\r\n    transform: translateY(-50%);\r\n    width: 24px;\r\n    height: 80px;\r\n    padding: 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: rgba(255, 255, 255, 0.95);\r\n    border: 1px solid #f0f0f0;\r\n    border-radius: ${props => props.$position === 'left' ? '0 4px 4px 0' : '4px 0 0 4px'};\r\n    z-index: 20;\r\n    pointer-events: auto;\r\n    box-shadow: ${props => props.$position === 'left' ? \r\n      '2px 0 8px rgba(0, 0, 0, 0.15)' : \r\n      '-2px 0 8px rgba(0, 0, 0, 0.15)'\r\n    };\r\n\r\n    &:hover {\r\n      background: #fff !important;\r\n      border-color: #d9d9d9 !important;\r\n      color: #1890ff !important;\r\n    }\r\n\r\n    &:focus {\r\n      background: #fff !important;\r\n      border-color: #d9d9d9 !important;\r\n    }\r\n\r\n    .anticon {\r\n      color: rgba(0, 0, 0, 0.45);\r\n      font-size: 12px;\r\n    }\r\n\r\n    &:hover .anticon {\r\n      color: #1890ff;\r\n    }\r\n  }\r\n`;\r\n\r\nconst InfoCard = styled(Card)`\r\n  margin: 0;\r\n  height: ${props => props.height || 'auto'};\r\n  border-radius: 0;\r\n  \r\n  .ant-card-body {\r\n    padding: 12px;\r\n    font-size: 13px;\r\n    height: calc(100% - 40px);\r\n    overflow-y: auto;\r\n  }\r\n`;\r\n\r\nconst MainLayout = () => {\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [currentUser, setCurrentUser] = useState(null);\r\n  const [username, setUsername] = useState('');\r\n\r\n  useEffect(() => {\r\n    const userInfo = localStorage.getItem('user');\r\n    if (userInfo) {\r\n      try {\r\n        const userData = JSON.parse(userInfo);\r\n        // 兼容不同的用户数据格式\r\n        setUsername(\r\n          userData.username || // 直接存储的用户名\r\n          userData.user?.username || // 存储在user对象中的用户名\r\n          userData.user?.name || // 用户的显示名称\r\n          '用户' // 默认值\r\n        );\r\n      } catch (error) {\r\n        console.error('解析用户信息失败:', error);\r\n        setUsername('用户');\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  const menuItems = [\r\n    {\r\n      key: 'real-time-traffic',\r\n      icon: <BarChartOutlined />,\r\n      label: '实时交通',\r\n    },\r\n    {\r\n      key: 'device-status',\r\n      icon: <DesktopOutlined />,\r\n      label: '设备状态',\r\n    },\r\n    {\r\n      key: 'road-monitoring',\r\n      icon: <CarOutlined />,\r\n      label: '路网监控',\r\n    },\r\n    {\r\n      key: 'system-management',\r\n      icon: <SettingOutlined />,\r\n      label: '系统管理',\r\n    },\r\n  ];\r\n\r\n  const handleMenuClick = (e) => {\r\n    navigate(`/${e.key}`);\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    // 显示加载提示\r\n    message.loading('正在安全退出系统...', 1);\r\n\r\n    // 触发一个自定义事件，通知CampusModel组件清理资源\r\n    const cleanupEvent = new CustomEvent('cleanupBeforeLogout');\r\n    window.dispatchEvent(cleanupEvent);\r\n\r\n    // 添加延迟确保组件有足够时间清理\r\n    setTimeout(() => {\r\n      try {\r\n        // 清除所有用户相关的数据\r\n        localStorage.clear();\r\n        // 跳转到登录页面\r\n        navigate('/login', { replace: true });\r\n        // 显示退出成功提示\r\n        message.success('已成功退出登录');\r\n      } catch (error) {\r\n        console.error('退出登录时发生错误:', error);\r\n        message.error('退出登录失败，请重试');\r\n      }\r\n    }, 1000);\r\n  };\r\n\r\n  return (\r\n    <Layout style={{ minHeight: '100vh' }}>\r\n      <Header style={{ \r\n        display: 'flex', \r\n        alignItems: 'center', \r\n        background: '#fff', \r\n        padding: '0 24px',\r\n        borderBottom: '1px solid #f0f0f0',\r\n        position: 'relative',\r\n        zIndex: 12\r\n      }}>\r\n        <Menu\r\n          mode=\"horizontal\"\r\n          selectedKeys={[location.pathname.split('/')[1]]}\r\n          onClick={handleMenuClick}\r\n          items={menuItems}\r\n          style={{ flex: 1 }}\r\n        />\r\n        <Space>\r\n          <div className=\"user-info\">\r\n            <Avatar icon={<UserOutlined />} />\r\n            <span className=\"username\">{username}</span>\r\n          </div>\r\n          <Button \r\n            icon={<LogoutOutlined />} \r\n            onClick={handleLogout}\r\n          >\r\n            退出登录\r\n          </Button>\r\n        </Space>\r\n      </Header>\r\n      \r\n      {/* 添加固定的地图容器 */}\r\n      <MapContainer>\r\n        <CampusModel />\r\n      </MapContainer>\r\n\r\n      {/* 内容区域包装器 */}\r\n      <Content style={{ \r\n        padding: '0',\r\n        position: 'relative', \r\n        background: 'transparent',\r\n        pointerEvents: 'none',\r\n        zIndex: 11\r\n      }}>\r\n        <ContentWrapper>\r\n          <CampusModelContext.Provider value={true}>\r\n            <Outlet />\r\n          </CampusModelContext.Provider>\r\n        </ContentWrapper>\r\n      </Content>\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport default MainLayout; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,aAAa,QAAQ,OAAO;AACjE,SAASC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,UAAU,EAAEC,IAAI,EAAEC,MAAM,QAAQ,MAAM;AACrF,SAASC,MAAM,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACnE,SACEC,gBAAgB,EAChBC,eAAe,EACfC,WAAW,EACXC,eAAe,EACfC,cAAc,EACdC,YAAY,QACP,mBAAmB;AAC1B,OAAOC,WAAW,MAAM,8BAA8B;AACtD,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAM;EAAEC,MAAM;EAAEC;AAAQ,CAAC,GAAGtB,MAAM;AAClC,MAAM;EAAEuB;AAAK,CAAC,GAAGlB,UAAU;;AAE3B;AACA,OAAO,MAAMmB,kBAAkB,gBAAGzB,aAAa,CAAC,IAAI,CAAC;;AAErD;AACA,MAAM0B,YAAY,GAAGP,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAlBIF,YAAY;AAoBlB,MAAMG,cAAc,GAAGV,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAvBID,cAAc;AAyBpB,MAAME,aAAa,GAAGZ,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMK,WAAW,GAAGb,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMM,gBAAgB,GAAGd,MAAM,CAACQ,GAAG;AACnC;AACA,WAAWO,KAAK,IAAIA,KAAK,CAACC,UAAU,GAAG,GAAG,GAAG,OAAO;AACpD;AACA;AACA;AACA;AACA;AACA,WAAWD,KAAK,IAAIA,KAAK,CAACE,SAAS,KAAK,MAAM,GAAG,OAAO,GAAG,MAAM,KAAKF,KAAK,IAAIA,KAAK,CAACC,UAAU,GAAG,GAAG,GAAG,KAAK;AAC7G;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,KAAK,IAAIA,KAAK,CAACE,SAAS,KAAK,MAAM,GAAG,cAAc,GAAG,aAAa;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqBF,KAAK,IAAIA,KAAK,CAACE,SAAS,KAAK,MAAM,GAAG,aAAa,GAAG,aAAa;AACxF;AACA;AACA,kBAAkBF,KAAK,IAAIA,KAAK,CAACE,SAAS,KAAK,MAAM,GAC/C,+BAA+B,GAC/B,gCAAgC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CACC;AAED,MAAMC,QAAQ,GAAGlB,MAAM,CAACZ,IAAI,CAAC;AAC7B;AACA,YAAY2B,KAAK,IAAIA,KAAK,CAACI,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAMC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAMgC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAMgD,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC7C,IAAIF,QAAQ,EAAE;MACZ,IAAI;QAAA,IAAAG,cAAA,EAAAC,eAAA;QACF,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACP,QAAQ,CAAC;QACrC;QACAD,WAAW,CACTM,QAAQ,CAACP,QAAQ,KAAI;QAAA,CAAAK,cAAA,GACrBE,QAAQ,CAACG,IAAI,cAAAL,cAAA,uBAAbA,cAAA,CAAeL,QAAQ,MAAI;QAAA,CAAAM,eAAA,GAC3BC,QAAQ,CAACG,IAAI,cAAAJ,eAAA,uBAAbA,eAAA,CAAeK,IAAI;QAAI;QACvB,IAAI,CAAC;QACP,CAAC;MACH,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCX,WAAW,CAAC,IAAI,CAAC;MACnB;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMa,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,mBAAmB;IACxBC,IAAI,eAAExC,OAAA,CAACT,gBAAgB;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,eAAe;IACpBC,IAAI,eAAExC,OAAA,CAACR,eAAe;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,iBAAiB;IACtBC,IAAI,eAAExC,OAAA,CAACP,WAAW;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,mBAAmB;IACxBC,IAAI,eAAExC,OAAA,CAACN,eAAe;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,eAAe,GAAIC,CAAC,IAAK;IAC7B3B,QAAQ,CAAC,IAAI2B,CAAC,CAACR,GAAG,EAAE,CAAC;EACvB,CAAC;EAED,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzB;IACAjE,OAAO,CAACkE,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;;IAEjC;IACA,MAAMC,YAAY,GAAG,IAAIC,WAAW,CAAC,qBAAqB,CAAC;IAC3DC,MAAM,CAACC,aAAa,CAACH,YAAY,CAAC;;IAElC;IACAI,UAAU,CAAC,MAAM;MACf,IAAI;QACF;QACA3B,YAAY,CAAC4B,KAAK,CAAC,CAAC;QACpB;QACAnC,QAAQ,CAAC,QAAQ,EAAE;UAAEoC,OAAO,EAAE;QAAK,CAAC,CAAC;QACrC;QACAzE,OAAO,CAAC0E,OAAO,CAAC,SAAS,CAAC;MAC5B,CAAC,CAAC,OAAOrB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QAClCrD,OAAO,CAACqD,KAAK,CAAC,YAAY,CAAC;MAC7B;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,oBACEpC,OAAA,CAACpB,MAAM;IAAC8E,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBACpC5D,OAAA,CAACC,MAAM;MAACyD,KAAK,EAAE;QACbG,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,UAAU,EAAE,MAAM;QAClBC,OAAO,EAAE,QAAQ;QACjBC,YAAY,EAAE,mBAAmB;QACjCC,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAE;MACV,CAAE;MAAAP,QAAA,gBACA5D,OAAA,CAACnB,IAAI;QACHuF,IAAI,EAAC,YAAY;QACjBC,YAAY,EAAE,CAAChD,QAAQ,CAACiD,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAE;QAChDC,OAAO,EAAE1B,eAAgB;QACzB2B,KAAK,EAAEnC,SAAU;QACjBoB,KAAK,EAAE;UAAEgB,IAAI,EAAE;QAAE;MAAE;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eACF5C,OAAA,CAAChB,KAAK;QAAA4E,QAAA,gBACJ5D,OAAA;UAAK2E,SAAS,EAAC,WAAW;UAAAf,QAAA,gBACxB5D,OAAA,CAACb,MAAM;YAACqD,IAAI,eAAExC,OAAA,CAACJ,YAAY;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClC5C,OAAA;YAAM2E,SAAS,EAAC,UAAU;YAAAf,QAAA,EAAEpC;UAAQ;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACN5C,OAAA,CAAClB,MAAM;UACL0D,IAAI,eAAExC,OAAA,CAACL,cAAc;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzB4B,OAAO,EAAExB,YAAa;UAAAY,QAAA,EACvB;QAED;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGT5C,OAAA,CAACK,YAAY;MAAAuD,QAAA,eACX5D,OAAA,CAACH,WAAW;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGf5C,OAAA,CAACE,OAAO;MAACwD,KAAK,EAAE;QACdM,OAAO,EAAE,GAAG;QACZE,QAAQ,EAAE,UAAU;QACpBH,UAAU,EAAE,aAAa;QACzBa,aAAa,EAAE,MAAM;QACrBT,MAAM,EAAE;MACV,CAAE;MAAAP,QAAA,eACA5D,OAAA,CAACQ,cAAc;QAAAoD,QAAA,eACb5D,OAAA,CAACI,kBAAkB,CAACyE,QAAQ;UAACC,KAAK,EAAE,IAAK;UAAAlB,QAAA,eACvC5D,OAAA,CAACZ,MAAM;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACiB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEb,CAAC;AAACzB,EAAA,CAjIID,UAAU;EAAA,QACG7B,WAAW,EACXC,WAAW;AAAA;AAAAyF,GAAA,GAFxB7D,UAAU;AAmIhB,eAAeA,UAAU;AAAC,IAAAX,EAAA,EAAAE,GAAA,EAAAsE,GAAA;AAAAC,YAAA,CAAAzE,EAAA;AAAAyE,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}