{"ast": null, "code": "var win;\nif (typeof window !== \"undefined\") {\n  win = window;\n} else if (typeof global !== \"undefined\") {\n  win = global;\n} else if (typeof self !== \"undefined\") {\n  win = self;\n} else {\n  win = {};\n}\nmodule.exports = win;", "map": {"version": 3, "names": ["win", "window", "global", "self", "module", "exports"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/global/window.js"], "sourcesContent": ["var win;\n\nif (typeof window !== \"undefined\") {\n    win = window;\n} else if (typeof global !== \"undefined\") {\n    win = global;\n} else if (typeof self !== \"undefined\"){\n    win = self;\n} else {\n    win = {};\n}\n\nmodule.exports = win;\n"], "mappings": "AAAA,IAAIA,GAAG;AAEP,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EAC/BD,GAAG,GAAGC,MAAM;AAChB,CAAC,MAAM,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACtCF,GAAG,GAAGE,MAAM;AAChB,CAAC,MAAM,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAC;EACnCH,GAAG,GAAGG,IAAI;AACd,CAAC,MAAM;EACHH,GAAG,GAAG,CAAC,CAAC;AACZ;AAEAI,MAAM,CAACC,OAAO,GAAGL,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}