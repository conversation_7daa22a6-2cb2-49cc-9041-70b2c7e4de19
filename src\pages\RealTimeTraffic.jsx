import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge, Button, Select } from 'antd';
import * as echarts from 'echarts/core';
import { Bar<PERSON>hart } from 'echarts/charts';
import { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import styled from 'styled-components';
import CollapsibleSidebar from '../components/layout/CollapsibleSidebar';
import axios from 'axios';
import vehiclesData from '../data/vehicles.json';
import intersectionsData from '../data/intersections.json'; // 导入路口数据

// 注册必要的echarts组件
echarts.use([BarChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);

const StyledCanvas = styled.div`
  width: 100%;
  height: 600px;
  background: #f0f2f5;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  z-index: 1;
`;

// 页面布局容器
const PageContainer = styled.div`
  display: flex;
  height: calc(100vh - 65px); // 减去顶部导航栏高度和内边距
  overflow: hidden;
`;

// 左侧信息栏容器
const LeftSidebar = styled.div`
  width: 320px;
  height: 100%;
  padding: 0 8px 0 0;
  border-right: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
`;

// 右侧信息栏容器
const RightSidebar = styled.div`
  width: 320px;
  height: 100%;
  padding: 0 0 0 8px;
  border-left: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
`;

// 主内容区域
const MainContent = styled.div`
  flex: 1;
  height: 100%;
  overflow: hidden;
  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' :
    props.leftCollapsed ? '0 8px 0 24px' :
    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};
  transition: all 0.3s ease;
  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' :
    props.leftCollapsed ? '0 8px 0 0' :
    props.rightCollapsed ? '0 0 0 8px' : '0'};
  position: relative;
  z-index: 1;
`;

// 信息卡片
const InfoCard = styled(Card)`
  margin-bottom: 12px;
  height: ${props => props.height || 'auto'};

  .ant-card-head {
    min-height: 40px;
    padding: 0 12px;
  }

  .ant-card-head-title {
    padding: 8px 0;
    font-size: 14px;
  }

  .ant-card-body {
    padding: 12px;
    font-size: 13px;
    height: calc(100% - 40px); // 减去卡片头部高度
    overflow-y: auto;
  }

  &:last-child {
    margin-bottom: 0;
  }
`;

// 自定义统计数字组件
const CompactStatistic = styled(Statistic)`
  .ant-statistic-title {
    font-size: 12px;
    margin-bottom: 2px;
  }

  .ant-statistic-content {
    font-size: 16px;
  }
`;

const RealTimeTraffic = () => {
  const [loading, setLoading] = useState(true);
  const [vehicles, setVehicles] = useState([]);
  const [events, setEvents] = useState([]);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [stats, setStats] = useState({
    totalVehicles: 0,
    onlineVehicles: 0,
    offlineVehicles: 0,
    totalDevices: 0,
    onlineDevices: 0,
    offlineDevices: 0
  });

  // 存储在线车辆的 BSM ID，初始时为空集合，表示所有车辆离线
  const [onlineBsmIds, setOnlineBsmIds] = useState(() => {
    try {
      const savedOnlineIds = localStorage.getItem('realTimeTrafficOnlineIds');
      // 返回空集合，确保所有车辆初始状态为离线
      return new Set();
    } catch (error) {
      console.error('读取在线ID缓存失败:', error);
      return new Set();
    }
  });
  // 存储最后一次收到 BSM 消息的时间
  const lastBsmTime = useRef({});

  const eventChartRef = useRef(null);

  // 添加侧边栏折叠状态
  const [leftCollapsed, setLeftCollapsed] = useState(false);
  const [rightCollapsed, setRightCollapsed] = useState(false);

  // 添加时间段选择状态
  const [selectedTimeRange, setSelectedTimeRange] = useState('24h');

  const [eventStats, setEventStats] = useState({
    '401': 0,  // 道路抛洒物
    '404': 0,  // 道路障碍物
    '405': 0,  // 行人通过马路
    '904': 0,  // 逆行车辆
    '910': 0,  // 违停车辆
    '1002': 0, // 道路施工
    '901': 0   // 车辆超速
  });

  // 添加RSI事件缓存，用于检测重复事件
  const prevRsiEvents = useRef(new Map());

  // 事件列表缓存，存储所有事件的完整信息
  const eventListCache = useRef([]);

  // 事件ID计数器
  const eventIdCounter = useRef(1);

  // ========== 数据库API调用函数 ==========

  /**
   * 存储实时事件到数据库
   */
  const storeEventToDatabase = async (eventData) => {
    try {
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
      const response = await axios.post(`${apiUrl}/api/events/store`, eventData);

      if (response.data && response.data.success) {
        console.log(`✅ 事件已存储到${response.data.storage}:`, eventData.eventTypeText);
        return true;
      } else {
        console.error('❌ 存储事件失败:', response.data);
        return false;
      }
    } catch (error) {
      console.error('❌ 存储事件到数据库失败:', error);
      return false;
    }
  };

  /**
   * 从数据库获取事件统计
   */
  const fetchEventStatsFromDatabase = async (timeRange = '24h') => {
    try {
      // console.log(`timeRange);
      console.log('实际的时间段0', timeRange);
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
      const response = await axios.get(`${apiUrl}/api/events/stats?timeRange=${timeRange}`);

      if (response.data && response.data.success) {
        console.log(`📊 从${response.data.storage}获取事件统计(${timeRange}):`, response.data.data);
        return response.data.data;
      } else {
        console.error('❌ 获取事件统计失败:', response.data);
        return null;
      }
    } catch (error) {
      console.error('❌ 从数据库获取事件统计失败:', error);
      return null;
    }
  };

  /**
   * 从数据库获取最近事件
   */
  const fetchRecentEventsFromDatabase = async (limit = 10) => {
    try {
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
      const response = await axios.get(`${apiUrl}/api/events/recent?limit=${limit}`);

      if (response.data && response.data.success) {
        console.log(`📋 从${response.data.storage}获取最近事件:`, response.data.data.length, '条');
        return response.data.data;
      } else {
        console.error('❌ 获取最近事件失败:', response.data);
        return [];
      }
    } catch (error) {
      console.error('❌ 从数据库获取最近事件失败:', error);
      return [];
    }
  };

  // 添加手动更新车辆状态和位置信息的函数
  const updateVehicleStatus = useCallback((bsmId, status, speed = 0, lat = 0, lng = 0, heading = 0) => {
    // 确保速度和航向角是格式化的数值，保留两位小数
    const formattedSpeed = parseFloat(speed).toFixed(0);
    const formattedHeading = parseFloat(heading).toFixed(2);

    console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${formattedSpeed}, 位置=(${lat}, ${lng})`);

    // 更新在线状态
    if (status === 'online') {
      setOnlineBsmIds(prev => new Set([...prev, bsmId]));
      lastBsmTime.current[bsmId] = Date.now();
    } else {
      setOnlineBsmIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(bsmId);
        return newSet;
      });
    }

    // 更新车辆信息
    setVehicles(prevVehicles =>
      prevVehicles.map(vehicle =>
        vehicle.bsmId === bsmId
          ? {
              ...vehicle,
              status: status,
              speed: parseFloat(formattedSpeed), // 确保是数值类型
              lat: parseFloat(lat.toFixed(7)),
              lng: parseFloat(lng.toFixed(7)),
              heading: parseFloat(formattedHeading) // 确保是数值类型
            }
          : vehicle
      )
    );
  }, []);

  // 在组件挂载时，暴露updateVehicleStatus函数到window对象
  useEffect(() => {
    window.updateVehicleStatus = updateVehicleStatus;

    // 用于监听CampusModel是否接收到BSM消息的事件
    const handleRealBsmReceived = (event) => {
      if (event.data && event.data.type === 'realBsmReceived') {
        // console.log('收到CampusModel发送的真实BSM消息通知');
      }
    };

    window.addEventListener('message', handleRealBsmReceived);

    // 确保页面刷新时清空在线车辆状态
    console.log('组件初始化，重置所有车辆为离线状态');
    setOnlineBsmIds(new Set());
    lastBsmTime.current = {};

    return () => {
      window.removeEventListener('message', handleRealBsmReceived);
      delete window.updateVehicleStatus;
    };
  }, [updateVehicleStatus]);

  // 从数据库加载事件数据
  const loadEventsFromDatabase = async (timeRange = selectedTimeRange) => {
    
    try {
      // 加载最近事件
      const recentEvents = await fetchRecentEventsFromDatabase(10);
      if (recentEvents && recentEvents.length > 0) {
        setEvents(recentEvents);
        console.log('✅ 从数据库加载了', recentEvents.length, '条最近事件');
      }

      // 加载事件统计（使用选择的时间段）
      console.log('实际的时间段1', timeRange);
      const stats = await fetchEventStatsFromDatabase(timeRange);
      if (stats) {
        setEventStats(prevStats => ({
          ...prevStats,
          ...stats
        }));
        console.log(`✅ 从数据库加载了事件统计数据(${timeRange})`);
      }
    } catch (error) {
      console.error('❌ 从数据库加载事件数据失败:', error);
    }
  };

  // 处理时间段选择变化
  const handleTimeRangeChange = (value) => {
    console.log('📅 时间段选择变化:', value);
    setSelectedTimeRange(value);
    // 立即重新加载事件统计数据
    loadEventsFromDatabase(value);
  };

  // 组件挂载时从数据库加载数据
  useEffect(() => {
    loadEventsFromDatabase();
  }, []);

  // 监听时间段变化，重新加载事件统计数据
  useEffect(() => {
    loadEventsFromDatabase(selectedTimeRange);
  }, [selectedTimeRange]);

  // 定期从数据库刷新数据（每30秒）
  useEffect(() => {
    const refreshInterval = setInterval(() => {
      console.log('🔄 定期刷新事件数据...', '当前时间段:', selectedTimeRange);
      loadEventsFromDatabase(selectedTimeRange);
    }, 30000); // 30秒刷新一次

    return () => clearInterval(refreshInterval);
  }, [selectedTimeRange]); // 添加 selectedTimeRange 作为依赖

  // 定期清理过期事件（每5分钟）
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      cleanupExpiredEvents();
    }, 300000); // 5分钟清理一次

    return () => clearInterval(cleanupInterval);
  }, []);

  // 定期删除1分钟内没有更新的事件（每30秒检查一次）
  useEffect(() => {
    const removeInactiveInterval = setInterval(() => {
      removeInactiveEvents();
    }, 30000); // 30秒检查一次

    return () => clearInterval(removeInactiveInterval);
  }, []);

  // 获取车辆数据
  const fetchVehicles = useCallback(async () => {
    try {
      // 先尝试从API获取最新数据
      console.log('尝试从API获取最新车辆数据');
      const apiData = await fetchLatestVehiclesData(true);

      // 如果API获取成功，直接使用API数据
      if (apiData && apiData.length > 0) {
        console.log('成功从API获取车辆数据，车辆数量:', apiData.length);

        // 获取所有车辆的BSM ID列表
        const bsmIds = apiData.map(v => v.bsmId).filter(id => id);
        console.log('所有车辆的BSM ID:', bsmIds);

        // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线
        const updatedVehicles = apiData.map(vehicle => {
          // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线
          const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);

          return {
            ...vehicle,
            plate: vehicle.plateNumber, // 适配表格显示
            status: isOnline ? 'online' : 'offline', // 只有收到BSM消息的车辆才显示为在线
            speed: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0) : 0,
            lat: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0) : 0,
            lng: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0) : 0,
            heading: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0) : 0
          };
        });

        setVehicles(updatedVehicles);

        // 直接检查更新后的车辆在线状态
        const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;
        const totalCount = updatedVehicles.length;

        console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);

        // 更新车辆统计数据
        setStats(prevStats => ({
          ...prevStats,
          totalVehicles: totalCount,
          onlineVehicles: onlineCount,
          offlineVehicles: totalCount - onlineCount
        }));

        return;
      }

      // 如果API获取失败，回退到本地JSON文件
      console.log('API获取失败，回退到本地vehiclesData');
      const vehiclesList = vehiclesData.vehicles || [];
      console.log('从vehiclesData获取车辆数据，车辆数量:', vehiclesList.length);

      // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线
      const updatedVehicles = vehiclesList.map(vehicle => {
        // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线
        const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);

        return {
          ...vehicle,
          plate: vehicle.plateNumber, // 适配表格显示
          status: isOnline ? 'online' : 'offline', // 只有收到BSM消息的车辆才显示为在线
          speed: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0) : 0,
          lat: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0) : 0,
          lng: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0) : 0,
          heading: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0) : 0
        };
      });

      setVehicles(updatedVehicles);

      // 直接检查更新后的车辆在线状态
      const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;
      const totalCount = updatedVehicles.length;

      console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);

      // 更新车辆统计数据
      setStats(prevStats => ({
        ...prevStats,
        totalVehicles: totalCount,
        onlineVehicles: onlineCount,
        offlineVehicles: totalCount - onlineCount
      }));
    } catch (error) {
      console.error('获取车辆列表失败:', error);
    }
  }, [onlineBsmIds]);

  // 获取设备统计数据
  const fetchDeviceStats = async () => {
    try {
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
      const response = await axios.get(`${apiUrl}/api/devices`);

      if (response.data && response.data.success) {
        const devicesData = response.data.data;

        // 更新设备统计数据
        setStats(prevStats => ({
          ...prevStats,
          totalDevices: devicesData.length,
          onlineDevices: devicesData.filter(d => d.status === 'online').length,
          offlineDevices: devicesData.filter(d => d.status === 'offline').length
        }));
      }
    } catch (error) {
      console.error('获取设备统计数据失败:', error);
    }
  };

  // 监听 BSM 消息
  useEffect(() => {
    const handleBsmMessage = (event) => {
      if (event.data && event.data.type === 'bsm') {
        // 获取bsmId，确保它正确地从消息中提取
        const bsmData = event.data.data || {};
        const bsmId = bsmData.bsmId || event.data.bsmId;

        if (!bsmId) {
          console.error('BSM消息缺少bsmId:', event.data);
          return;
        }

        // console.log('收到BSM消息，ID:', bsmId);
        const now = Date.now();

        // 更新最后接收时间
        lastBsmTime.current[bsmId] = now;

        // 添加到在线bsmId集合
        setOnlineBsmIds(prev => new Set([...prev, bsmId]));

        // 提取正确的BSM数据并格式化为两位小数
        const speed = parseFloat((parseFloat(bsmData.partSpeed || event.data.speed || 0) * 3.6).toFixed(0)); // 转换为km/h，保留两位小数
        const lat = parseFloat(parseFloat(bsmData.partLat || event.data.lat || 0).toFixed(7));
        const lng = parseFloat(parseFloat(bsmData.partLong || event.data.lng || 0).toFixed(7));
        const heading = parseFloat(parseFloat(bsmData.partHeading || event.data.heading || 0).toFixed(2)); // 保留两位小数

        // console.log(`车辆${bsmId}状态: 速度=${speed.toFixed(2)}km/h, 位置=(${lat.toFixed(7)}, ${lng.toFixed(7)}), 航向=${heading.toFixed(2)}°`);

        // 更新车辆状态和位置信息
        setVehicles(prevVehicles => {
          // 检查是否找到对应车辆
          const foundVehicle = prevVehicles.find(v => v.bsmId === bsmId);
          if (!foundVehicle) {
            console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);
            return prevVehicles;
          }

          const updatedVehicles = prevVehicles.map(vehicle =>
            vehicle.bsmId === bsmId
              ? {
                  ...vehicle,
                  status: 'online',
                  speed: speed,
                  lat: lat,
                  lng: lng,
                  heading: heading
                }
              : vehicle
          );

          // 计算更新后的在线车辆数量
          const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;
          const totalCount = updatedVehicles.length;

          // 更新统计数据
          setStats(prevStats => ({
            ...prevStats,
            onlineVehicles: onlineCount,
            offlineVehicles: totalCount - onlineCount
          }));

          return updatedVehicles;
        });
      }
    };

    // 添加消息监听器
    window.addEventListener('message', handleBsmMessage);

    // 清理函数
    return () => {
      window.removeEventListener('message', handleBsmMessage);
    };
  }, []);

  // 修改检查在线状态的useEffect，同步更新统计信息
  useEffect(() => {
    const checkOnlineStatus = () => {
      const now = Date.now();
      console.log('检查车辆在线状态...');

      // 只有在特定条件下才将车辆设为离线
      // 例如，只有收到过BSM消息的车辆才会被检查是否超时
      setOnlineBsmIds(prev => {
        const newOnlineBsmIds = new Set(prev);
        let hasChanges = false;

        // 仅检查已有最后更新时间的车辆
        prev.forEach(bsmId => {
          const lastTime = lastBsmTime.current[bsmId];

          // 只有收到过BSM消息的车辆才会被检查是否超时
          if (lastTime && (now - lastTime > 30000)) {
            console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);
            newOnlineBsmIds.delete(bsmId);
            hasChanges = true;
          }
        });

        if (hasChanges) {
          // 更新车辆状态
          setVehicles(prevVehicles => {
            const updatedVehicles = prevVehicles.map(vehicle => {
              // 只更新有最后更新时间的车辆状态
              if (lastBsmTime.current[vehicle.bsmId]) {
                const isOnline = newOnlineBsmIds.has(vehicle.bsmId);
                return {
                  ...vehicle,
                  status: isOnline ? 'online' : 'offline'
                };
              }
              return vehicle;
            });

            // 计算更新后的在线车辆数量
            const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;
            const totalCount = updatedVehicles.length;

            // 更新统计数据
            setStats(prevStats => ({
              ...prevStats,
              onlineVehicles: onlineCount,
              offlineVehicles: totalCount - onlineCount
            }));

            return updatedVehicles;
          });
        }

        return newOnlineBsmIds;
      });
    };

    const interval = setInterval(checkOnlineStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  // 重置所有车辆的初始状态
  useEffect(() => {
    // 将所有车辆状态重置为离线
    const resetAllVehicles = () => {
      // 只有在没有任何BSM消息的情况下才执行重置
      if (onlineBsmIds.size === 0) {
        setVehicles(prevVehicles =>
          prevVehicles.map(vehicle => ({
            ...vehicle,
            status: 'offline',
            speed: 0,
            lat: 0,
            lng: 0,
            heading: 0
          }))
        );

        console.log('已重置所有车辆为离线状态');
      }
    };

    // 初始执行一次
    resetAllVehicles();

    // 然后每30秒检查一次
    const interval = setInterval(resetAllVehicles, 30000);

    return () => clearInterval(interval);
  }, [onlineBsmIds]);

  // 在组件挂载时获取数据
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      fetchVehicles();
      await fetchDeviceStats();
      setLoading(false);
    };

    loadData();
    // // 降低更新频率，避免频繁覆盖状态
    // const interval = setInterval(loadData, 300000); // 每60x5秒更新一次
    // return () => clearInterval(interval);
  }, []);

  // 添加对车辆数据变更的监听
  useEffect(() => {
    console.log('设置车辆数据变更监听');

    // 监听自定义事件
    const handleVehiclesDataChanged = () => {
      console.log('检测到车辆数据变更事件，重新获取车辆列表');
      fetchVehicles();
    };

    // 监听localStorage变化
    const handleStorageChange = (event) => {
      if (event.key === 'vehiclesLastUpdated' || event.key === 'vehiclesData') {
        console.log('检测到localStorage变化，重新获取车辆列表');
        fetchVehicles();
      }
    };

    // 添加事件监听器
    window.addEventListener('vehiclesDataChanged', handleVehiclesDataChanged);
    window.addEventListener('storage', handleStorageChange);

    // 初始检查是否有更新
    const lastUpdated = localStorage.getItem('vehiclesLastUpdated');
    if (lastUpdated) {
      console.log('初始检查到vehiclesLastUpdated:', lastUpdated);
      fetchVehicles();
    }

    // 设置更频繁的强制轮询，确保在部署环境中也能获取最新数据
    const forcedPollingInterval = setInterval(() => {
      console.log('强制轮询：重新获取车辆列表');
      fetchVehicles();
    }, 10000); // 每10秒强制刷新一次

    // 清理函数
    return () => {
      window.removeEventListener('vehiclesDataChanged', handleVehiclesDataChanged);
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(forcedPollingInterval);
    };
  }, []);

  // 添加检查本地缓存与API数据是否一致的机制
  // 定义一个单独的API调用函数，用于获取最新的车辆列表数据
  const fetchLatestVehiclesData = async (returnData = false) => {
    try {
      // 尝试多个可能的API地址
      const possibleApiUrls = [
        process.env.REACT_APP_API_URL || 'http://localhost:5000'
        // 'http://localhost:5000',
        // window.location.origin, // 当前站点的根URL
        // `${window.location.origin}/api`, // 当前站点下的/api路径
        // 'http://localhost:5000/api',
        // 'http://127.0.0.1:5000',
        // 'http://127.0.0.1:5000/api'
      ];

      console.log('尝试从多个API地址获取车辆数据');

      // // 尝试从本地JSON文件直接获取
      // try {
      //   console.log('尝试从本地JSON文件获取数据');
      //   const jsonResponse = await axios.get('/vehicles.json');
      //   if (jsonResponse.data && jsonResponse.data.vehicles) {
      //     console.log('成功从本地JSON文件获取数据:', jsonResponse.data.vehicles.length);
      //     if (returnData) {
      //       return jsonResponse.data.vehicles;
      //     }
      //     processVehiclesData(jsonResponse.data.vehicles);
      //     return jsonResponse.data.vehicles;
      //   }
      // } catch (jsonError) {
      //   console.log('从本地JSON获取失败:', jsonError.message);
      // }

      // 逐个尝试API地址
      let succeeded = false;
      let vehiclesData = null;

      for (const apiUrl of possibleApiUrls) {
        if (succeeded) break;

        try {
          console.log(`尝试从 ${apiUrl}/api/vehicles/list 获取数据`);
          const response = await axios.get(`${apiUrl}/api/vehicles/list`);

          if (response.data && response.data.vehicles) {
            console.log(`成功从 ${apiUrl} 获取数据:`, response.data.vehicles.length);
            vehiclesData = response.data.vehicles;

            if (returnData) {
              return vehiclesData;
            }

            processVehiclesData(response.data.vehicles);
            succeeded = true;
            break;
          }
        } catch (error) {
          console.log(`从 ${apiUrl} 获取失败:`, error.message);
          // 继续尝试下一个URL
        }
      }

      if (!succeeded && !returnData) {
        console.log('所有API地址都获取失败，尝试使用vehicles.json');
        // 尝试从当前页面获取
        try {
          const response = await fetch('/vehicles.json');
          if (response.ok) {
            const data = await response.json();
            if (data && data.vehicles) {
              console.log('从public/vehicles.json获取数据成功:', data.vehicles.length);
              processVehiclesData(data.vehicles);
              return data.vehicles;
            }
          }
        } catch (e) {
          console.error('从public/vehicles.json获取失败:', e);
        }
      }

      return vehiclesData || [];
    } catch (error) {
      console.error('获取车辆列表失败:', error);
      return [];
    }
  };

  // 添加处理车辆数据的辅助函数
  const processVehiclesData = (newVehicles) => {
    // 与当前列表比较
    if (vehicles.length !== newVehicles.length) {
      console.log('检测到车辆数量变化，从', vehicles.length, '到', newVehicles.length);
      fetchVehicles(); // 重新加载
      return;
    }

    // 检查是否有新车辆ID
    const currentIds = new Set(vehicles.map(v => v.id));
    const hasNewVehicle = newVehicles.some(v => !currentIds.has(v.id));

    if (hasNewVehicle) {
      console.log('检测到新增车辆');
      fetchVehicles(); // 重新加载
    }
  };

  // 添加自动选择第一个车辆的逻辑
  useEffect(() => {
    // 当车辆列表加载完成且有车辆数据时
    if (vehicles.length > 0 && !selectedVehicle) {
      // 自动选择第一个车辆
      setSelectedVehicle(vehicles[0]);
      console.log('已自动选择第一个车辆:', vehicles[0].plateNumber);
    }
  }, [vehicles, selectedVehicle]);

  // 修改图表初始化代码
  useEffect(() => {
    let chart = null;
    let handleResize = null;
    let lastUpdateTime = new Date();

    // 确保 DOM 元素存在
    if (!eventChartRef.current) {
      console.error('图表容器未找到');
      return;
    }

    // 检查并清理已存在的图表实例
    let existingChart = echarts.getInstanceByDom(eventChartRef.current);
    if (existingChart) {
      existingChart.dispose();
    }

    try {
      // 初始化新的图表实例
      chart = echarts.init(eventChartRef.current);

      // 设置图表配置
      const updateChart = () => {
        const currentTime = new Date();
        lastUpdateTime = currentTime;

        // 事件类型配置
        // const eventTypes = [
        //   { type: '401', name: '道路抛洒物', color: '#ff4d4f' },
        //   { type: '404', name: '道路障碍物', color: '#faad14' },
        //   { type: '405', name: '行人通过马路', color: '#1890ff' },
        //   { type: '904', name: '逆行车辆', color: '#f5222d' },
        //   { type: '910', name: '违停车辆', color: '#722ed1' },
        //   { type: '1002', name: '道路施工', color: '#fa8c16' },
        //   { type: '901', name: '车辆超速', color: '#eb2f96' }
        // ];
        const eventTypes = [
          { type: '401', name: '道路抛洒物', color: '#faad14' },
          { type: '404', name: '道路障碍物', color: '#ff7a45' },
          { type: '405', name: '行人通过马路', color: '#52c41a' },
          { type: '904', name: '逆行车辆', color: '#f5222d' },
          { type: '910', name: '违停车辆', color: '#ff4d4f' },
          { type: '1002', name: '道路施工', color: '#1890ff' },
          { type: '901', name: '车辆超速', color: '#fa8c16' }
        ];

        // 处理数据
        const data = eventTypes
          .map(event => ({
            value: eventStats[event.type] || 0,
            name: event.name,
            itemStyle: { color: event.color }
          }))
          .filter(item => item.value > 0)
          .sort((a, b) => b.value - a.value);

        const option = {
          title: {
            text: `最后更新: ${currentTime.toLocaleTimeString()}`,
            left: 'center',
            top: -3,
            textStyle: {
              fontSize: 12,
              color: '#999'
            }
          },
          grid: {
            top: 30,
            bottom: 0,
            left: 0,
            right: 50,
            containLabel: true
          },
          animation: true,
          animationDuration: 0,
          animationDurationUpdate: 1000,
          animationEasingUpdate: 'quinticInOut',
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          xAxis: {
            type: 'value',
            show: false,
            splitLine: { show: false }
          },
          yAxis: {
            type: 'category',
            data: data.map(item => item.name),
            axisLabel: {
              fontSize: 12,
              color: '#666',
              margin: 8
            },
            axisTick: { show: false },
            axisLine: { show: false }
          },
          series: [{
            type: 'bar',
            data: data,
            barWidth: '50%',
            label: {
              show: true,
              position: 'right',
              formatter: '{c}次',
              fontSize: 12,
              color: '#666'
            },
            itemStyle: {
              borderRadius: [0, 4, 4, 0]
            },
            realtimeSort: false,
            animationDelay: function (idx) {
              return idx * 100;
            }
          }]
        };

        // 使用 notMerge: false 来保持增量更新
        chart.setOption(option, {
          notMerge: false,
          replaceMerge: ['series']
        });
      };

      // 初始更新
      updateChart();

      // 监听事件统计变化，每分钟更新一次
      const statsInterval = setInterval(updateChart, 60000); // 60000ms = 1分钟

      // 监听窗口大小变化
      handleResize = () => {
        chart?.resize();
      };
      window.addEventListener('resize', handleResize);

      // 清理函数
      return () => {
        clearInterval(statsInterval);
        if (handleResize) {
          window.removeEventListener('resize', handleResize);
        }
        if (chart) {
          chart.dispose();
        }
      };

    } catch (error) {
      console.error('初始化图表失败:', error);
    }
  }, [eventStats]);

  // 修改 RSI 消息处理逻辑
  useEffect(() => {
    const handleRsiMessage = (event) => {
      try {
        if (event.data && event.data.type === 'RSI') {
          const rsiData = event.data.data;
          if (!rsiData || !rsiData.rtes) return;
          // if(rsiData.rtes.length > 0){
          //   // console.log('RealTimeTraffic 收到 RSI 消息:', event.data);
          //   if(parseFloat(rsiData.posLong) < 113.0){
          //     console.log('位置不在测试范围的 RSI 消息:', event.data);
          //     console.log('收到RSI消息，但位置不在测试范围内，忽略');
          //     return;
          //   }
          // }

          const latitude = parseFloat(rsiData.posLat);
          const longitude = parseFloat(rsiData.posLong);
          const rsuId = rsiData.rsuId;
          const mac = event.data.mac || '';
          const timestamp = event.data.tm || Date.now();

          // 统计本帧所有非重复事件类型
          const nonDuplicateEventTypes = [];
          rsiData.rtes.forEach(event => {
            const eventType = event.eventType;

            // 根据事件类型设置不同的位置精度和去重策略
            let latFixed = '';
            let lngFixed = '';
            let eventKey = '';

            if(eventType === '904' || eventType === '901'){
              // 逆行和超速：使用3位小数精度（约111米范围）
              latFixed = Math.floor(latitude * Math.pow(10,3))/Math.pow(10,3);
              lngFixed = Math.floor(longitude* Math.pow(10,3))/Math.pow(10,3);
              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;
            }
            else if(eventType === '910'){
              // 违停车辆：使用2位小数精度（约1.1公里范围），忽略MAC地址
              // 这样可以将相近位置的违停事件归为同一类
              latFixed = Math.floor(latitude * Math.pow(10,4))/Math.pow(10,4);
              lngFixed = Math.floor(longitude* Math.pow(10,4))/Math.pow(10,4);
              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;
            }
            else{
              // 其他事件：使用4位小数精度（约11米范围）
              latFixed = Math.floor(latitude * Math.pow(10,4))/Math.pow(10,4);
              lngFixed = Math.floor(longitude* Math.pow(10,4))/Math.pow(10,4);
              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;
            }

            const duplicateResult = checkDuplicateEvent(
              eventType,
              eventKey,
              timestamp,
              { lat: latitude, lng: longitude }
            );

            const isDuplicate = duplicateResult.isDuplicate;
            // 获取事件类型的中文描述
            let eventTypeText = '';
            let eventColor = '';
            switch(eventType) {
              case '401': eventTypeText = '道路抛洒物'; eventColor = '#faad14'; break;
              case '404': eventTypeText = '道路障碍物'; eventColor = '#ff7a45'; break;
              case '405': eventTypeText = '行人通过马路'; eventColor = '#52c41a'; break;
              case '904': eventTypeText = '逆行车辆'; eventColor = '#f5222d'; break;
              case '910': eventTypeText = '违停车辆'; eventColor = '#ff4d4f'; break;
              case '1002': eventTypeText = '道路施工'; eventColor = '#1890ff'; break;
              case '901': eventTypeText = '车辆超速'; eventColor = '#fa8c16'; break;
              default: eventTypeText = event.description || '未知事件'; eventColor = '#8c8c8c';
            }
            // 更新事件列表
            // const newEvent = {
            //   key: Date.now() + Math.random(),
            //   type: eventTypeText,
            //   time: new Date().toLocaleTimeString(),
            //   vehicle: rsiData.rsuId || '未知设备',
            //   color: eventColor,
            //   eventType: eventType,
            //   location: {
            //     latitude: latitude,
            //     longitude: longitude
            //   }

            // };
            // setEvents(prev => {
            //   const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录
            //   return newEvents;
            // });
            if (!isDuplicate) {
              // 创建事件数据，使用新的事件ID
              const eventData = {
                eventId: duplicateResult.eventId,
                eventType: eventType,
                eventTypeText: eventTypeText,
                rsuId: rsiData.rsuId || '未知设备',
                mac: mac,
                latitude: latitude,
                longitude: longitude,
                site: getNearestIntersectionName(latitude, longitude),
                eventKey: eventKey,
                color: eventColor,
                timestamp: new Date().toISOString()
              };

              // 存储到数据库
              storeEventToDatabase(eventData).then(success => {
                if (success) {
                  console.log(`✅ 事件已存储到数据库: ${eventTypeText}`);

                  // 更新本地事件列表（用于实时显示）
                  const newEvent = {
                    key: Date.now() + Math.random(),
                    type: eventTypeText,
                    time: new Date().toLocaleTimeString(),
                    rsuId: rsiData.rsuId || '未知设备',
                    color: eventColor,
                    eventType: eventType,
                    location: {
                      latitude: latitude,
                      longitude: longitude
                    },
                    site: eventData.site
                  };

                  setEvents(prev => {
                    const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录
                    return newEvents;
                  });
                } else {
                  console.error(`❌ 事件存储失败: ${eventTypeText}`);
                }
              });

              nonDuplicateEventTypes.push(eventType);
            } else {
              // console.log(`检测到重复事件，不累计统计: ${eventTypeText}`);
            }
          });

          // 如果有新事件，从数据库刷新统计数据
          if (nonDuplicateEventTypes.length > 0) {
            console.log(`📊 检测到 ${nonDuplicateEventTypes.length} 个新事件，刷新统计数据`);

            // 延迟1秒后从数据库获取最新统计，确保数据已存储
            console.log('实际的时间段2', selectedTimeRange);
            setTimeout(async () => {
              const stats = await fetchEventStatsFromDatabase(selectedTimeRange);
              if (stats) {
                setEventStats(prevStats => ({
                  ...prevStats,
                  ...stats
                }));
                console.log('✅ 事件统计数据已从数据库更新，时间段:', selectedTimeRange);
              }
            }, 1000);
          }
        }
      } catch (error) {
        console.error('处理 RSI 消息失败:', error);
      }
    };

    // 添加消息监听器
    window.addEventListener('message', handleRsiMessage);

    return () => {
      window.removeEventListener('message', handleRsiMessage);
    };
  }, []);

  // 获取事件类型的阈值配置
  const getEventThresholds = (eventType) => {
    switch(eventType) {
      case '910': // 违停车辆
        return { timeThreshold: 300000, distanceThreshold: 10 }; // 5分钟, 20米
      case '904': // 逆行车辆
        return { timeThreshold: 10000, distanceThreshold: 20 }; // 10秒, 20米
      case '901': // 车辆超速
        return { timeThreshold: 30000, distanceThreshold: 20 }; // 30秒, 50米
      case '401': // 道路抛洒物
      case '404': // 道路障碍物
      case '1002': // 道路施工
        return { timeThreshold: 600000, distanceThreshold: 5 }; // 10分钟, 30米
      case '405': // 行人通过马路
        return { timeThreshold: 10000, distanceThreshold: 10 }; // 10秒, 10米
      default:
        return { timeThreshold: 5000, distanceThreshold: 5 }; // 5秒, 5米
    }
  };

  // 优化后的事件重复检查逻辑
  const checkDuplicateEvent = (eventType, eventKey, currentTime, currentPos) => {
    const { timeThreshold, distanceThreshold } = getEventThresholds(eventType);

    // 遍历事件列表缓存中的所有事件
    for (let i = 0; i < eventListCache.current.length; i++) {
      const cachedEvent = eventListCache.current[i];

      // 检查事件类型是否相同
      if (cachedEvent.eventType !== eventType) {
        continue;
      }

      // 计算时间差
      const timeDiff = currentTime - cachedEvent.lastUpdateTime;

      // 检查时间差是否在阈值内
      if (timeDiff > timeThreshold) {
        continue;
      }

      // 计算距离
      const distance = calculateDistance(
        currentPos.lat, currentPos.lng,
        cachedEvent.position.lat, cachedEvent.position.lng
      );

      // 检查距离是否在阈值内
      if (distance <= distanceThreshold) {
        // 找到匹配的事件，更新信息
        cachedEvent.eventKey = eventKey;
        cachedEvent.lastUpdateTime = currentTime;
        cachedEvent.position = { ...currentPos };
        cachedEvent.updateCount = (cachedEvent.updateCount || 1) + 1;

        // console.log(`🔄 检测到重复事件 ${eventType} (ID: ${cachedEvent.eventId})，时间差: ${(timeDiff/1000).toFixed(1)}s，距离: ${distance.toFixed(1)}m，更新次数: ${cachedEvent.updateCount}`);

        return {
          isDuplicate: true,
          eventId: cachedEvent.eventId,
          matchedEvent: cachedEvent
        };
      }
    }

    // 没有找到匹配的事件，创建新事件
    const newEventId = `EVT_${eventIdCounter.current.toString().padStart(6, '0')}`;
    eventIdCounter.current++;

    const newEvent = {
      eventId: newEventId,
      eventType: eventType,
      eventKey: eventKey,
      firstDetectedTime: currentTime,
      lastUpdateTime: currentTime,
      position: { ...currentPos },
      updateCount: 1
    };

    // 添加到事件列表缓存
    eventListCache.current.push(newEvent);

    console.log(`✨ 创建新事件 ${eventType} (ID: ${newEventId})，位置: (${currentPos.lat.toFixed(6)}, ${currentPos.lng.toFixed(6)})`);

    return {
      isDuplicate: false,
      eventId: newEventId,
      newEvent: newEvent
    };
  };

  // 清理过期事件的函数
  const cleanupExpiredEvents = () => {
    const currentTime = Date.now();
    const maxAge = 3600000; // 1小时

    const initialCount = eventListCache.current.length;
    eventListCache.current = eventListCache.current.filter(event => {
      const age = currentTime - event.lastUpdateTime;
      return age <= maxAge;
    });

    const removedCount = initialCount - eventListCache.current.length;
    if (removedCount > 0) {
      console.log(`🧹 清理了 ${removedCount} 个过期事件，当前缓存事件数: ${eventListCache.current.length}`);
    }
  };

  // 删除1分钟内没有更新的事件
  const removeInactiveEvents = () => {
    const currentTime = Date.now();
    const inactiveThreshold = 60000; // 1分钟

    const initialCount = eventListCache.current.length;
    const removedEvents = [];

    eventListCache.current = eventListCache.current.filter(event => {
      const timeSinceLastUpdate = currentTime - event.lastUpdateTime;
      if (timeSinceLastUpdate > inactiveThreshold) {
        removedEvents.push({
          id: event.eventId,
          type: event.eventType,
          inactiveTime: (timeSinceLastUpdate / 1000).toFixed(1)
        });
        return false; // 删除该事件
      }
      return true; // 保留该事件
    });

    const removedCount = initialCount - eventListCache.current.length;
    if (removedCount > 0) {
      console.log(`🗑️ 删除了 ${removedCount} 个1分钟内未更新的事件:`);
      removedEvents.forEach(event => {
        console.log(`   - 事件 ${event.id} (类型: ${event.type})，未更新时间: ${event.inactiveTime}秒`);
      });
      console.log(`📊 当前缓存事件数: ${eventListCache.current.length}`);
    }
  };

  // 添加计算两点之间距离的函数
  const calculateDistance = (lat1, lon1, lat2, lon2) => {
    if (lat1 === 0 || lon1 === 0 || lat2 === 0 || lon2 === 0) {
      return 999;
    }
    const R = 6371000;
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // 根据坐标查找最近的路口名称
  const getNearestIntersectionName = (lat, lng) => {
    // 从 intersections.json 获取所有路口
    const intersections = intersectionsData.intersections || [];
    let minDistance = Infinity;
    let nearestName = '未知地点';
    intersections.forEach(inter => {
      const interLat = parseFloat(inter.latitude);
      const interLng = parseFloat(inter.longitude);
      const dist = calculateDistance(lat, lng, interLat, interLng);
      if (dist < minDistance) {
        minDistance = dist;
        nearestName = inter.name;
      }
    });
    return nearestName;
  };

  // 处理车辆选择
  const handleVehicleSelect = (vehicle) => {
    console.log('选择车辆:', vehicle.plateNumber, '状态:', vehicle.status);
    setSelectedVehicle(vehicle);
  };

  // 修改车辆状态更新逻辑，确保同步更新selectedVehicle
  useEffect(() => {
    // 当车辆列表更新后，如果当前有选中的车辆，则更新选中的车辆信息
    if (selectedVehicle) {
      const updatedSelectedVehicle = vehicles.find(v => v.id === selectedVehicle.id);
      if (updatedSelectedVehicle &&
          (updatedSelectedVehicle.status !== selectedVehicle.status ||
           updatedSelectedVehicle.speed !== selectedVehicle.speed ||
           updatedSelectedVehicle.lat !== selectedVehicle.lat ||
           updatedSelectedVehicle.lng !== selectedVehicle.lng ||
           updatedSelectedVehicle.heading !== selectedVehicle.heading)) {
        console.log(`更新选中车辆 ${selectedVehicle.plateNumber} 的信息:`,
                   `状态从 ${selectedVehicle.status} 变为 ${updatedSelectedVehicle.status}`);
        setSelectedVehicle(updatedSelectedVehicle);
      }
    }
  }, [vehicles, selectedVehicle]);

  // 车辆列表列定义
  const vehicleColumns = [
    {
      title: '车牌号',
      dataIndex: 'plate',
      key: 'plate',
      width: '40%',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: '30%',
      render: status => (
        <Badge
          status={status === 'online' ? 'success' : 'error'}
          text={status === 'online' ? '在线' : '离线'}
        />
      ),
    },
    {
      title: '速度',
      dataIndex: 'speed',
      key: 'speed',
      width: '30%',
      render: speed => `${typeof speed === 'number' ? speed.toFixed(0) : speed} km/h`,
    }
  ];

  // 修改实时事件列表的渲染
  const renderEventList = () => (
    <List
      size="small"
      dataSource={events}
      renderItem={item => (
        <List.Item style={{ padding: '8px 0' }}>
          <List.Item.Meta
            title={
              <div style={{ fontSize: '13px', marginBottom: '4px' }}>
                <span style={{ color: item.color, marginRight: '8px' }}>
                  {item.type}
                </span>
                <span style={{ color: '#666', fontSize: '12px' }}>
                  {item.time}
                </span>
              </div>
            }
            description={
              <div style={{ fontSize: '12px', color: '#666' }}>
                {/* 显示地点（site） */}
                <div>地点: {item.site || '未知地点'}</div>
                <div>位置: {item.location ?
                  `${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}` :
                  '未知位置'}
                </div>
              </div>
            }
          />
        </List.Item>
      )}
      style={{
        maxHeight: 'calc(100% - 24px)',
        overflowY: 'auto'
      }}
    />
  );

  // 添加强制定期从API获取最新数据的机制
  useEffect(() => {
    const apiPollingInterval = setInterval(() => {
      console.log('直接从API检查车辆数据更新');
      fetchLatestVehiclesData();
    }, 15000); // 每15秒检查一次API

    return () => clearInterval(apiPollingInterval);
  }, [vehicles]);

  return (
    <Spin spinning={loading} tip="加载中...">
      <PageContainer>
        {/* 左侧信息栏 */}
        <CollapsibleSidebar
          position="left"
          collapsed={leftCollapsed}
          onCollapse={() => setLeftCollapsed(!leftCollapsed)}
        >
          {/* 车辆和设备总数信息栏 */}
          <InfoCard title="车辆和设备统计" bordered={false} height="160px">
            <Row gutter={[8, 1]} >
              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>
                <CompactStatistic
                  title="车辆总数"
                  value={stats.totalVehicles}
                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }}
                  // Style={{}}
                />
              </Col>
              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>
                <CompactStatistic
                  title="在线车辆"
                  value={stats.onlineVehicles}
                  // suffix={`/ ${stats.totalVehicles}`}
                  valueStyle={{ color: '#3f8600', display: 'flex',justifyContent: 'center'}}
                />
              </Col>
              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>
                <CompactStatistic
                  title="离线车辆"
                  value={stats.offlineVehicles}
                  // suffix={`/ ${stats.totalVehicles}`}
                  valueStyle={{ color: '#cf1322' ,display: 'flex',justifyContent: 'center' }}
                />
              </Col>
              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>
                <CompactStatistic
                  title="设备总数"
                  value={stats.totalDevices}
                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }}
                />
              </Col>
              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>
                <CompactStatistic
                  title="在线设备"
                  value={stats.onlineDevices}
                  // suffix={`/ ${stats.totalDevices}`}
                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }}
                />
              </Col>
              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>
                <CompactStatistic
                  title="离线设备"
                  value={stats.offlineDevices}
                  // suffix={`/ ${stats.totalDevices}`}
                  valueStyle={{ color: '#cf1322',display: 'flex',justifyContent: 'center' }}
                />
              </Col>
            </Row>
          </InfoCard>

          {/* 实时事件列表栏 */}
          <InfoCard
            title="实时事件列表"
            bordered={false}
            height="calc(50% - 95px)"
            // extra={
            //   <div>
            //     <Button
            //       size="small"
            //       type="link"
            //       onClick={() => {
            //         console.log('🧪 查看事件缓存状态');
            //         console.log('📊 当前事件缓存状态:', {
            //           缓存事件数: eventListCache.current.length,
            //           事件ID计数器: eventIdCounter.current,
            //           事件列表: eventListCache.current.map(e => {
            //             const timeSinceUpdate = (Date.now() - e.lastUpdateTime) / 1000;
            //             return {
            //               ID: e.eventId,
            //               类型: e.eventType,
            //               更新次数: e.updateCount,
            //               位置: `${e.position.lat.toFixed(6)}, ${e.position.lng.toFixed(6)}`,
            //               未更新时间: `${timeSinceUpdate.toFixed(1)}秒`
            //             };
            //           })
            //         });
            //       }}
            //     >
            //       查看缓存
            //     </Button>
            //     <Button
            //       size="small"
            //       type="link"
            //       onClick={() => {
            //         console.log('🗑️ 手动删除非活跃事件');
            //         removeInactiveEvents();
            //       }}
            //     >
            //       删除非活跃
            //     </Button>
            //     <Button
            //       size="small"
            //       type="link"
            //       onClick={() => {
            //         console.log('🧹 手动清理过期事件');
            //         cleanupExpiredEvents();
            //       }}
            //     >
            //       清理过期
            //     </Button>
            //   </div>
            // }
          >
            {renderEventList()}
          </InfoCard>

          {/* 实时事件统计栏 */}
          <InfoCard
            title="实时事件统计"
            bordered={false}
            height="calc(50% - 95px)"
            extra={
              <Select
                value={selectedTimeRange}
                onChange={handleTimeRangeChange}
                onSelect={handleTimeRangeChange}
                size="small"
                style={{ width: 80 }}
                options={[
                  { value: '1h', label: '1小时' },
                  { value: '24h', label: '1天' },
                  { value: '7d', label: '7天' },
                  { value: 'all', label: '全部' }
                ]}
              />
            }
          >
            <div ref={eventChartRef} style={{ height: '100%', width: '100%' }}></div>
          </InfoCard>
        </CollapsibleSidebar>

        {/* 主内容区域 */}
        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>
          {/* 主要内容 */}
        </MainContent>

        {/* 右侧信息栏 */}
        <CollapsibleSidebar
          position="right"
          collapsed={rightCollapsed}
          onCollapse={() => setRightCollapsed(!rightCollapsed)}
        >
          {/* 车辆列表栏 */}
          <InfoCard title="车辆列表" bordered={false} height="50%">
            <Table
              dataSource={vehicles}
              columns={vehicleColumns}
              rowKey="id"
              pagination={false}
              size="small"
              scroll={{ y: 180 }}
              onRow={(record) => ({
                onClick: () => handleVehicleSelect(record),
                style: {
                  cursor: 'pointer',
                  background: selectedVehicle?.id === record.id ? '#e6f7ff' : 'transparent',
                  fontSize: '13px',
                  padding: '4px 8px'
                }
              })}
            />
          </InfoCard>

          {/* 车辆详细信息栏 */}
          <InfoCard title="车辆详细信息" bordered={false} height="50%">
            {selectedVehicle ? (
              <Descriptions
                bordered
                column={1}
                size="small"
                styles={{
                  label: { fontSize: '13px', padding: '4px 8px' },
                  content: { fontSize: '13px', padding: '4px 8px' }
                }}
              >
                <Descriptions.Item label="车牌号">{selectedVehicle.plateNumber}</Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Badge
                    status={selectedVehicle.status === 'online' ? 'success' : 'error'}
                    text={selectedVehicle.status === 'online' ? '在线' : '离线'}
                  />
                </Descriptions.Item>
                <Descriptions.Item label="经度">
                  {selectedVehicle.status === 'online' ? selectedVehicle.lng.toFixed(7) : 'N/A'}
                </Descriptions.Item>
                <Descriptions.Item label="纬度">
                  {selectedVehicle.status === 'online' ? selectedVehicle.lat.toFixed(7) : 'N/A'}
                </Descriptions.Item>
                <Descriptions.Item label="速度">
                  {selectedVehicle.status === 'online' ?
                    `${typeof selectedVehicle.speed === 'number' ? selectedVehicle.speed.toFixed(0) : selectedVehicle.speed} km/h` :
                    'N/A'}
                </Descriptions.Item>
                <Descriptions.Item label="航向角">
                  {selectedVehicle.status === 'online' ?
                    `${typeof selectedVehicle.heading === 'number' ? selectedVehicle.heading.toFixed(2) : selectedVehicle.heading}°` :
                    'N/A'}
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <p style={{ fontSize: '13px' }}>请选择车辆查看详细信息</p>
            )}
          </InfoCard>
        </CollapsibleSidebar>
      </PageContainer>
    </Spin>
  );
};

export default RealTimeTraffic;