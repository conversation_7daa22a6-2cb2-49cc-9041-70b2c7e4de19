{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\VideoPlayer.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport flvjs from 'flv.js';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VideoPlayer = ({\n  deviceId,\n  rtspUrl\n}) => {\n  _s();\n  const videoRef = useRef(null);\n  const [error, setError] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [hasRtspUrl, setHasRtspUrl] = useState(!!rtspUrl);\n  const [debugInfo, setDebugInfo] = useState('');\n  useEffect(() => {\n    if (!rtspUrl) {\n      setHasRtspUrl(false);\n      setLoading(false);\n      return;\n    }\n    setHasRtspUrl(true);\n    setLoading(true);\n    setError(null);\n    let flvPlayer = null;\n    let retryCount = 0;\n    const maxRetries = 10;\n    let retryTimeout = null;\n    const loadVideo = () => {\n      if (flvjs.isSupported()) {\n        const flvUrl = `http://localhost:8000/live/${deviceId}.flv`;\n        setDebugInfo(`正在检查流状态...`);\n        fetch('http://localhost:8001/api/streams').then(response => response.json()).catch(() => {\n          console.log('API 请求失败，直接尝试连接流');\n          return {\n            directPlay: true\n          };\n        }).then(streams => {\n          if (streams.directPlay) {\n            return fetch(flvUrl, {\n              method: 'HEAD'\n            });\n          }\n          const streamPath = `/live/${deviceId}`;\n          console.log('检查流:', {\n            streamPath,\n            availableStreams: streams\n          });\n          if (streams && streams[streamPath]) {\n            console.log('流已发布，开始播放');\n            return fetch(flvUrl, {\n              method: 'HEAD'\n            });\n          } else {\n            setDebugInfo(`等待流发布... (${retryCount + 1}/${maxRetries})`);\n            throw new Error('流尚未发布');\n          }\n        }).then(response => {\n          if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n          }\n          console.log('流可以访问，开始初始化播放器');\n          initializePlayer(flvUrl);\n        }).catch(error => {\n          console.error('流不可访问:', error);\n          setDebugInfo(`流不可访问: ${error.message}`);\n          if (retryCount < maxRetries) {\n            retryCount++;\n            console.log(`第 ${retryCount} 次重试...`);\n            const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);\n            console.log(`等待 ${delay / 1000} 秒后重试`);\n            retryTimeout = setTimeout(loadVideo, delay);\n          } else {\n            setError('无法访问视频流，请检查服务器状态');\n            setLoading(false);\n          }\n        });\n      } else {\n        setError('您的浏览器不支持FLV视频流');\n        setLoading(false);\n      }\n    };\n    const initializePlayer = flvUrl => {\n      console.log('开始初始化 FLV 播放器');\n      if (flvPlayer) {\n        flvPlayer.destroy();\n        flvPlayer = null;\n      }\n      flvPlayer = flvjs.createPlayer({\n        type: 'flv',\n        url: flvUrl,\n        isLive: true,\n        hasAudio: false,\n        hasVideo: true,\n        cors: true,\n        enableStashBuffer: true,\n        stashInitialSize: 1024,\n        enableWorker: true\n      });\n      flvPlayer.on(flvjs.Events.LOADING_COMPLETE, () => {\n        console.log('视频加载完成');\n        setLoading(false);\n      });\n      flvPlayer.on(flvjs.Events.MEDIA_INFO, info => {\n        console.log('媒体信息:', info);\n        setDebugInfo(`媒体信息: ${JSON.stringify(info)}`);\n      });\n      flvPlayer.on(flvjs.Events.ERROR, (errorType, errorDetail) => {\n        console.error('FLV播放器错误:', errorType, errorDetail);\n        setError(`加载失败: ${errorType} - ${errorDetail}`);\n        setDebugInfo(`错误详情: ${JSON.stringify(errorDetail)}`);\n        setLoading(false);\n      });\n      try {\n        console.log('正在加载视频元素');\n        flvPlayer.attachMediaElement(videoRef.current);\n        flvPlayer.load();\n        console.log('正在开始播放');\n        flvPlayer.play().catch(e => {\n          console.error('播放失败:', e);\n          setDebugInfo(`播放失败: ${e.message}`);\n        });\n      } catch (e) {\n        console.error('播放器初始化失败:', e);\n        setDebugInfo(`初始化失败: ${e.message}`);\n      }\n    };\n    loadVideo();\n    return () => {\n      if (retryTimeout) {\n        clearTimeout(retryTimeout);\n      }\n      if (flvPlayer) {\n        flvPlayer.pause();\n        flvPlayer.unload();\n        flvPlayer.detachMediaElement();\n        flvPlayer.destroy();\n      }\n    };\n  }, [deviceId, rtspUrl]);\n  if (!hasRtspUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u89C6\\u9891\\u6D41\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '11px',\n          marginTop: '4px'\n        },\n        children: \"(\\u8BE5\\u6444\\u50CF\\u5934\\u672A\\u914D\\u7F6ERTSP\\u5730\\u5740)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this);\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u89C6\\u9891\\u6D41...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '11px',\n          marginTop: '4px'\n        },\n        children: \"(\\u6B63\\u5728\\u8FDE\\u63A5\\u5230\\u6444\\u50CF\\u5934)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: '#666',\n          marginTop: '4px'\n        },\n        children: debugInfo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u89C6\\u9891\\u6D41\\u9519\\u8BEF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '11px',\n          marginTop: '4px'\n        },\n        children: [\"(\", error, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"video\", {\n    ref: videoRef,\n    controls: false,\n    autoPlay: true,\n    muted: true,\n    playsInline: true,\n    style: {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 184,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoPlayer, \"LNz5LnDmD09HstajTKIFF00zcDE=\");\n_c = VideoPlayer;\nexport default VideoPlayer;\nvar _c;\n$RefreshReg$(_c, \"VideoPlayer\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "flvjs", "jsxDEV", "_jsxDEV", "VideoPlayer", "deviceId", "rtspUrl", "_s", "videoRef", "error", "setError", "loading", "setLoading", "hasRtspUrl", "setHasRtspUrl", "debugInfo", "setDebugInfo", "flvPlayer", "retryCount", "maxRetries", "retryTimeout", "loadVideo", "isSupported", "flvUrl", "fetch", "then", "response", "json", "catch", "console", "log", "directPlay", "streams", "method", "streamPath", "availableStreams", "Error", "ok", "status", "initializePlayer", "message", "delay", "Math", "min", "pow", "setTimeout", "destroy", "createPlayer", "type", "url", "isLive", "hasAudio", "hasVideo", "cors", "enableStashBuffer", "stashInitialSize", "enableWorker", "on", "Events", "LOADING_COMPLETE", "MEDIA_INFO", "info", "JSON", "stringify", "ERROR", "errorType", "errorDetail", "attachMediaElement", "current", "load", "play", "e", "clearTimeout", "pause", "unload", "detachMediaElement", "style", "textAlign", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "marginTop", "color", "ref", "controls", "autoPlay", "muted", "playsInline", "width", "height", "objectFit", "_c", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/VideoPlayer.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport flvjs from 'flv.js';\n\nconst VideoPlayer = ({ deviceId, rtspUrl }) => {\n  const videoRef = useRef(null);\n  const [error, setError] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [hasRtspUrl, setHasRtspUrl] = useState(!!rtspUrl);\n  const [debugInfo, setDebugInfo] = useState('');\n\n  useEffect(() => {\n    if (!rtspUrl) {\n      setHasRtspUrl(false);\n      setLoading(false);\n      return;\n    }\n\n    setHasRtspUrl(true);\n    setLoading(true);\n    setError(null);\n\n    let flvPlayer = null;\n    let retryCount = 0;\n    const maxRetries = 10;\n    let retryTimeout = null;\n\n    const loadVideo = () => {\n      if (flvjs.isSupported()) {\n        const flvUrl = `http://localhost:8000/live/${deviceId}.flv`;\n        setDebugInfo(`正在检查流状态...`);\n        \n        fetch('http://localhost:8001/api/streams')\n          .then(response => response.json())\n          .catch(() => {\n            console.log('API 请求失败，直接尝试连接流');\n            return { directPlay: true };\n          })\n          .then(streams => {\n            if (streams.directPlay) {\n              return fetch(flvUrl, { method: 'HEAD' });\n            }\n\n            const streamPath = `/live/${deviceId}`;\n            console.log('检查流:', { streamPath, availableStreams: streams });\n            \n            if (streams && streams[streamPath]) {\n              console.log('流已发布，开始播放');\n              return fetch(flvUrl, { method: 'HEAD' });\n            } else {\n              setDebugInfo(`等待流发布... (${retryCount + 1}/${maxRetries})`);\n              throw new Error('流尚未发布');\n            }\n          })\n          .then(response => {\n            if (!response.ok) {\n              throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            console.log('流可以访问，开始初始化播放器');\n            initializePlayer(flvUrl);\n          })\n          .catch(error => {\n            console.error('流不可访问:', error);\n            setDebugInfo(`流不可访问: ${error.message}`);\n            if (retryCount < maxRetries) {\n              retryCount++;\n              console.log(`第 ${retryCount} 次重试...`);\n              const delay = Math.min(1000 * Math.pow(2, retryCount), 10000);\n              console.log(`等待 ${delay/1000} 秒后重试`);\n              retryTimeout = setTimeout(loadVideo, delay);\n            } else {\n              setError('无法访问视频流，请检查服务器状态');\n              setLoading(false);\n            }\n          });\n      } else {\n        setError('您的浏览器不支持FLV视频流');\n        setLoading(false);\n      }\n    };\n\n    const initializePlayer = (flvUrl) => {\n      console.log('开始初始化 FLV 播放器');\n      if (flvPlayer) {\n        flvPlayer.destroy();\n        flvPlayer = null;\n      }\n\n      flvPlayer = flvjs.createPlayer({\n        type: 'flv',\n        url: flvUrl,\n        isLive: true,\n        hasAudio: false,\n        hasVideo: true,\n        cors: true,\n        enableStashBuffer: true,\n        stashInitialSize: 1024,\n        enableWorker: true\n      });\n\n      flvPlayer.on(flvjs.Events.LOADING_COMPLETE, () => {\n        console.log('视频加载完成');\n        setLoading(false);\n      });\n\n      flvPlayer.on(flvjs.Events.MEDIA_INFO, (info) => {\n        console.log('媒体信息:', info);\n        setDebugInfo(`媒体信息: ${JSON.stringify(info)}`);\n      });\n\n      flvPlayer.on(flvjs.Events.ERROR, (errorType, errorDetail) => {\n        console.error('FLV播放器错误:', errorType, errorDetail);\n        setError(`加载失败: ${errorType} - ${errorDetail}`);\n        setDebugInfo(`错误详情: ${JSON.stringify(errorDetail)}`);\n        setLoading(false);\n      });\n\n      try {\n        console.log('正在加载视频元素');\n        flvPlayer.attachMediaElement(videoRef.current);\n        flvPlayer.load();\n        console.log('正在开始播放');\n        flvPlayer.play().catch(e => {\n          console.error('播放失败:', e);\n          setDebugInfo(`播放失败: ${e.message}`);\n        });\n      } catch (e) {\n        console.error('播放器初始化失败:', e);\n        setDebugInfo(`初始化失败: ${e.message}`);\n      }\n    };\n\n    loadVideo();\n\n    return () => {\n      if (retryTimeout) {\n        clearTimeout(retryTimeout);\n      }\n      if (flvPlayer) {\n        flvPlayer.pause();\n        flvPlayer.unload();\n        flvPlayer.detachMediaElement();\n        flvPlayer.destroy();\n      }\n    };\n  }, [deviceId, rtspUrl]);\n\n  if (!hasRtspUrl) {\n    return (\n      <div style={{ textAlign: 'center' }}>\n        <div>视频流</div>\n        <div style={{ fontSize: '11px', marginTop: '4px' }}>\n          (该摄像头未配置RTSP地址)\n        </div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center' }}>\n        <div>正在加载视频流...</div>\n        <div style={{ fontSize: '11px', marginTop: '4px' }}>\n          (正在连接到摄像头)\n        </div>\n        <div style={{ fontSize: '10px', color: '#666', marginTop: '4px' }}>\n          {debugInfo}\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{ textAlign: 'center' }}>\n        <div>视频流错误</div>\n        <div style={{ fontSize: '11px', marginTop: '4px' }}>\n          ({error})\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <video\n      ref={videoRef}\n      controls={false}\n      autoPlay\n      muted\n      playsInline\n      style={{ width: '100%', height: '100%', objectFit: 'cover' }}\n    />\n  );\n};\n\nexport default VideoPlayer; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,KAAK,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAMC,QAAQ,GAAGT,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAACM,OAAO,CAAC;EACvD,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAE9CF,SAAS,CAAC,MAAM;IACd,IAAI,CAACQ,OAAO,EAAE;MACZQ,aAAa,CAAC,KAAK,CAAC;MACpBF,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;IAEAE,aAAa,CAAC,IAAI,CAAC;IACnBF,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAIO,SAAS,GAAG,IAAI;IACpB,IAAIC,UAAU,GAAG,CAAC;IAClB,MAAMC,UAAU,GAAG,EAAE;IACrB,IAAIC,YAAY,GAAG,IAAI;IAEvB,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACtB,IAAIpB,KAAK,CAACqB,WAAW,CAAC,CAAC,EAAE;QACvB,MAAMC,MAAM,GAAG,8BAA8BlB,QAAQ,MAAM;QAC3DW,YAAY,CAAC,YAAY,CAAC;QAE1BQ,KAAK,CAAC,mCAAmC,CAAC,CACvCC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACjCC,KAAK,CAAC,MAAM;UACXC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;UAC/B,OAAO;YAAEC,UAAU,EAAE;UAAK,CAAC;QAC7B,CAAC,CAAC,CACDN,IAAI,CAACO,OAAO,IAAI;UACf,IAAIA,OAAO,CAACD,UAAU,EAAE;YACtB,OAAOP,KAAK,CAACD,MAAM,EAAE;cAAEU,MAAM,EAAE;YAAO,CAAC,CAAC;UAC1C;UAEA,MAAMC,UAAU,GAAG,SAAS7B,QAAQ,EAAE;UACtCwB,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE;YAAEI,UAAU;YAAEC,gBAAgB,EAAEH;UAAQ,CAAC,CAAC;UAE9D,IAAIA,OAAO,IAAIA,OAAO,CAACE,UAAU,CAAC,EAAE;YAClCL,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;YACxB,OAAON,KAAK,CAACD,MAAM,EAAE;cAAEU,MAAM,EAAE;YAAO,CAAC,CAAC;UAC1C,CAAC,MAAM;YACLjB,YAAY,CAAC,aAAaE,UAAU,GAAG,CAAC,IAAIC,UAAU,GAAG,CAAC;YAC1D,MAAM,IAAIiB,KAAK,CAAC,OAAO,CAAC;UAC1B;QACF,CAAC,CAAC,CACDX,IAAI,CAACC,QAAQ,IAAI;UAChB,IAAI,CAACA,QAAQ,CAACW,EAAE,EAAE;YAChB,MAAM,IAAID,KAAK,CAAC,uBAAuBV,QAAQ,CAACY,MAAM,EAAE,CAAC;UAC3D;UACAT,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC7BS,gBAAgB,CAAChB,MAAM,CAAC;QAC1B,CAAC,CAAC,CACDK,KAAK,CAACnB,KAAK,IAAI;UACdoB,OAAO,CAACpB,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;UAC9BO,YAAY,CAAC,UAAUP,KAAK,CAAC+B,OAAO,EAAE,CAAC;UACvC,IAAItB,UAAU,GAAGC,UAAU,EAAE;YAC3BD,UAAU,EAAE;YACZW,OAAO,CAACC,GAAG,CAAC,KAAKZ,UAAU,SAAS,CAAC;YACrC,MAAMuB,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,GAAGD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE1B,UAAU,CAAC,EAAE,KAAK,CAAC;YAC7DW,OAAO,CAACC,GAAG,CAAC,MAAMW,KAAK,GAAC,IAAI,OAAO,CAAC;YACpCrB,YAAY,GAAGyB,UAAU,CAACxB,SAAS,EAAEoB,KAAK,CAAC;UAC7C,CAAC,MAAM;YACL/B,QAAQ,CAAC,kBAAkB,CAAC;YAC5BE,UAAU,CAAC,KAAK,CAAC;UACnB;QACF,CAAC,CAAC;MACN,CAAC,MAAM;QACLF,QAAQ,CAAC,gBAAgB,CAAC;QAC1BE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,MAAM2B,gBAAgB,GAAIhB,MAAM,IAAK;MACnCM,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B,IAAIb,SAAS,EAAE;QACbA,SAAS,CAAC6B,OAAO,CAAC,CAAC;QACnB7B,SAAS,GAAG,IAAI;MAClB;MAEAA,SAAS,GAAGhB,KAAK,CAAC8C,YAAY,CAAC;QAC7BC,IAAI,EAAE,KAAK;QACXC,GAAG,EAAE1B,MAAM;QACX2B,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,KAAK;QACfC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE,IAAI;QACVC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,YAAY,EAAE;MAChB,CAAC,CAAC;MAEFvC,SAAS,CAACwC,EAAE,CAACxD,KAAK,CAACyD,MAAM,CAACC,gBAAgB,EAAE,MAAM;QAChD9B,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;QACrBlB,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC;MAEFK,SAAS,CAACwC,EAAE,CAACxD,KAAK,CAACyD,MAAM,CAACE,UAAU,EAAGC,IAAI,IAAK;QAC9ChC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE+B,IAAI,CAAC;QAC1B7C,YAAY,CAAC,SAAS8C,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,EAAE,CAAC;MAC/C,CAAC,CAAC;MAEF5C,SAAS,CAACwC,EAAE,CAACxD,KAAK,CAACyD,MAAM,CAACM,KAAK,EAAE,CAACC,SAAS,EAAEC,WAAW,KAAK;QAC3DrC,OAAO,CAACpB,KAAK,CAAC,WAAW,EAAEwD,SAAS,EAAEC,WAAW,CAAC;QAClDxD,QAAQ,CAAC,SAASuD,SAAS,MAAMC,WAAW,EAAE,CAAC;QAC/ClD,YAAY,CAAC,SAAS8C,IAAI,CAACC,SAAS,CAACG,WAAW,CAAC,EAAE,CAAC;QACpDtD,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC;MAEF,IAAI;QACFiB,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;QACvBb,SAAS,CAACkD,kBAAkB,CAAC3D,QAAQ,CAAC4D,OAAO,CAAC;QAC9CnD,SAAS,CAACoD,IAAI,CAAC,CAAC;QAChBxC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;QACrBb,SAAS,CAACqD,IAAI,CAAC,CAAC,CAAC1C,KAAK,CAAC2C,CAAC,IAAI;UAC1B1C,OAAO,CAACpB,KAAK,CAAC,OAAO,EAAE8D,CAAC,CAAC;UACzBvD,YAAY,CAAC,SAASuD,CAAC,CAAC/B,OAAO,EAAE,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC,CAAC,OAAO+B,CAAC,EAAE;QACV1C,OAAO,CAACpB,KAAK,CAAC,WAAW,EAAE8D,CAAC,CAAC;QAC7BvD,YAAY,CAAC,UAAUuD,CAAC,CAAC/B,OAAO,EAAE,CAAC;MACrC;IACF,CAAC;IAEDnB,SAAS,CAAC,CAAC;IAEX,OAAO,MAAM;MACX,IAAID,YAAY,EAAE;QAChBoD,YAAY,CAACpD,YAAY,CAAC;MAC5B;MACA,IAAIH,SAAS,EAAE;QACbA,SAAS,CAACwD,KAAK,CAAC,CAAC;QACjBxD,SAAS,CAACyD,MAAM,CAAC,CAAC;QAClBzD,SAAS,CAAC0D,kBAAkB,CAAC,CAAC;QAC9B1D,SAAS,CAAC6B,OAAO,CAAC,CAAC;MACrB;IACF,CAAC;EACH,CAAC,EAAE,CAACzC,QAAQ,EAAEC,OAAO,CAAC,CAAC;EAEvB,IAAI,CAACO,UAAU,EAAE;IACf,oBACEV,OAAA;MAAKyE,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBAClC3E,OAAA;QAAA2E,QAAA,EAAK;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACd/E,OAAA;QAAKyE,KAAK,EAAE;UAAEO,QAAQ,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAM,CAAE;QAAAN,QAAA,EAAC;MAEpD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIvE,OAAO,EAAE;IACX,oBACER,OAAA;MAAKyE,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBAClC3E,OAAA;QAAA2E,QAAA,EAAK;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrB/E,OAAA;QAAKyE,KAAK,EAAE;UAAEO,QAAQ,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAM,CAAE;QAAAN,QAAA,EAAC;MAEpD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN/E,OAAA;QAAKyE,KAAK,EAAE;UAAEO,QAAQ,EAAE,MAAM;UAAEE,KAAK,EAAE,MAAM;UAAED,SAAS,EAAE;QAAM,CAAE;QAAAN,QAAA,EAC/D/D;MAAS;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIzE,KAAK,EAAE;IACT,oBACEN,OAAA;MAAKyE,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBAClC3E,OAAA;QAAA2E,QAAA,EAAK;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAChB/E,OAAA;QAAKyE,KAAK,EAAE;UAAEO,QAAQ,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAM,CAAE;QAAAN,QAAA,GAAC,GACjD,EAACrE,KAAK,EAAC,GACV;MAAA;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/E,OAAA;IACEmF,GAAG,EAAE9E,QAAS;IACd+E,QAAQ,EAAE,KAAM;IAChBC,QAAQ;IACRC,KAAK;IACLC,WAAW;IACXd,KAAK,EAAE;MAAEe,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAQ;EAAE;IAAAd,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9D,CAAC;AAEN,CAAC;AAAC3E,EAAA,CA7LIH,WAAW;AAAA0F,EAAA,GAAX1F,WAAW;AA+LjB,eAAeA,WAAW;AAAC,IAAA0F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}