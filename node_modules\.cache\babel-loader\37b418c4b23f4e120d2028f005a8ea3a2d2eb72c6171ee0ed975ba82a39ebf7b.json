{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\RealTimeEvents.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, List, Statistic, Row, Col, Divider } from 'antd';\nimport { PieChart } from 'react-minimal-pie-chart';\n\n// 事件类型映射表\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EVENT_TYPES = {\n  '401': {\n    name: '道路抛洒物',\n    color: '#ff4d4f',\n    category: '变道'\n  },\n  '404': {\n    name: '道路障碍物',\n    color: '#faad14',\n    category: '停止'\n  },\n  '405': {\n    name: '行人通过马路',\n    color: '#1890ff',\n    category: '变道'\n  },\n  '904': {\n    name: '逆行车辆',\n    color: '#f5222d',\n    category: '超速'\n  },\n  '910': {\n    name: '违停车辆',\n    color: '#722ed1',\n    category: '停止'\n  },\n  '1002': {\n    name: '道路施工',\n    color: '#fa8c16',\n    category: '急刹车'\n  },\n  '901': {\n    name: '车辆超速',\n    color: '#eb2f96',\n    category: '超速'\n  }\n};\nconst RealTimeEvents = () => {\n  _s();\n  // 状态管理\n  const [events, setEvents] = useState([]);\n  const [statistics, setStatistics] = useState({\n    total: 0,\n    byType: {}\n  });\n\n  // 订阅 WebSocket 消息\n  useEffect(() => {\n    const ws = new WebSocket(`ws://${window.location.hostname}:8083/mqtt`);\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n        if (message.type === 'message' && message.topic === 'changli/cloud/v2x/rsu/rsi' && message.payload.type === 'RSI') {\n          const rsiData = message.payload.data;\n          const newEvents = (rsiData.rtes || []).map(event => {\n            var _EVENT_TYPES$event$ev;\n            return {\n              id: event.rteId,\n              type: event.eventType,\n              description: event.description || ((_EVENT_TYPES$event$ev = EVENT_TYPES[event.eventType]) === null || _EVENT_TYPES$event$ev === void 0 ? void 0 : _EVENT_TYPES$event$ev.name) || '未知事件',\n              time: new Date().toLocaleTimeString(),\n              startTime: event.startTime,\n              endTime: event.endTime\n            };\n          });\n\n          // 更新事件列表\n          setEvents(prevEvents => {\n            const allEvents = [...newEvents, ...prevEvents];\n            // 只保留最近的10条事件\n            return allEvents.slice(0, 10);\n          });\n\n          // 更新统计信息\n          updateStatistics(newEvents);\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    return () => {\n      ws.close();\n    };\n  }, []);\n\n  // 更新统计信息\n  const updateStatistics = newEvents => {\n    setStatistics(prev => {\n      const newStats = {\n        ...prev\n      };\n      newStats.total = prev.total + newEvents.length;\n      newEvents.forEach(event => {\n        var _EVENT_TYPES$event$ty;\n        const category = ((_EVENT_TYPES$event$ty = EVENT_TYPES[event.type]) === null || _EVENT_TYPES$event$ty === void 0 ? void 0 : _EVENT_TYPES$event$ty.category) || '其他';\n        newStats.byType[category] = (newStats.byType[category] || 0) + 1;\n      });\n      return newStats;\n    });\n  };\n\n  // 生成饼图数据\n  const getPieChartData = () => {\n    return Object.entries(statistics.byType).map(([category, count]) => ({\n      title: category,\n      value: count,\n      color: getCategoryColor(category)\n    }));\n  };\n\n  // 获取类别颜色\n  const getCategoryColor = category => {\n    const colors = {\n      '超速': '#1890ff',\n      '急刹车': '#52c41a',\n      '变道': '#faad14',\n      '启动': '#f5222d',\n      '停止': '#722ed1'\n    };\n    return colors[category] || '#8c8c8c';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u8F66\\u8F86\\u548C\\u8BBE\\u5907\\u603B\\u6570\\u4FE1\\u606F\",\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8F66\\u8F86\\u603B\\u6570\",\n            value: 3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5728\\u7EBF\\u8F66\\u8F86\",\n            value: \"0 / 3\",\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u79BB\\u7EBF\\u8F66\\u8F86\",\n            value: \"3 / 3\",\n            valueStyle: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        style: {\n          margin: '12px 0'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8BBE\\u5907\\u603B\\u6570\",\n            value: 13\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5728\\u7EBF\\u8BBE\\u5907\",\n            value: \"11 / 13\",\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          span: 8,\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u79BB\\u7EBF\\u8BBE\\u5907\",\n            value: \"2 / 13\",\n            valueStyle: {\n              color: '#ff4d4f'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u5217\\u8868\",\n      size: \"small\",\n      style: {\n        marginTop: '20px'\n      },\n      children: events.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px'\n        },\n        children: \"\\u6682\\u65E0\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(List, {\n        size: \"small\",\n        dataSource: events,\n        renderItem: event => {\n          var _EVENT_TYPES$event$ty2;\n          return /*#__PURE__*/_jsxDEV(List.Item, {\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                width: '100%',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: ((_EVENT_TYPES$event$ty2 = EVENT_TYPES[event.type]) === null || _EVENT_TYPES$event$ty2 === void 0 ? void 0 : _EVENT_TYPES$event$ty2.color) || '#8c8c8c'\n                },\n                children: event.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#8c8c8c'\n                },\n                children: event.time\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u7EDF\\u8BA1\",\n      size: \"small\",\n      style: {\n        marginTop: '20px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          height: '200px'\n        },\n        children: /*#__PURE__*/_jsxDEV(PieChart, {\n          data: getPieChartData(),\n          label: ({\n            dataEntry\n          }) => `${dataEntry.title}`,\n          labelStyle: {\n            fontSize: '8px',\n            fill: '#fff'\n          },\n          labelPosition: 70,\n          lineWidth: 20,\n          animate: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(RealTimeEvents, \"b65N3y6Vb/f+PLXLomf0oRV3wYU=\");\n_c = RealTimeEvents;\nexport default RealTimeEvents;\nvar _c;\n$RefreshReg$(_c, \"RealTimeEvents\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "List", "Statistic", "Row", "Col", "Divider", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "EVENT_TYPES", "name", "color", "category", "RealTimeEvents", "_s", "events", "setEvents", "statistics", "setStatistics", "total", "byType", "ws", "WebSocket", "window", "location", "hostname", "onmessage", "event", "message", "JSON", "parse", "data", "type", "topic", "payload", "rsiData", "newEvents", "rtes", "map", "_EVENT_TYPES$event$ev", "id", "rteId", "eventType", "description", "time", "Date", "toLocaleTimeString", "startTime", "endTime", "prevEvents", "allEvents", "slice", "updateStatistics", "error", "console", "close", "prev", "newStats", "length", "for<PERSON>ach", "_EVENT_TYPES$event$ty", "getPieChartData", "Object", "entries", "count", "title", "value", "getCategoryColor", "colors", "style", "padding", "children", "size", "gutter", "span", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "valueStyle", "margin", "marginTop", "textAlign", "dataSource", "renderItem", "_EVENT_TYPES$event$ty2", "<PERSON><PERSON>", "display", "justifyContent", "width", "fontSize", "height", "label", "dataEntry", "labelStyle", "fill", "labelPosition", "lineWidth", "animate", "_c", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/RealTimeEvents.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Card, List, Statistic, Row, Col, Divider } from 'antd';\r\nimport { PieChart } from 'react-minimal-pie-chart';\r\n\r\n// 事件类型映射表\r\nconst EVENT_TYPES = {\r\n  '401': { name: '道路抛洒物', color: '#ff4d4f', category: '变道' },\r\n  '404': { name: '道路障碍物', color: '#faad14', category: '停止' },\r\n  '405': { name: '行人通过马路', color: '#1890ff', category: '变道' },\r\n  '904': { name: '逆行车辆', color: '#f5222d', category: '超速' },\r\n  '910': { name: '违停车辆', color: '#722ed1', category: '停止' },\r\n  '1002': { name: '道路施工', color: '#fa8c16', category: '急刹车' },\r\n  '901': { name: '车辆超速', color: '#eb2f96', category: '超速' }\r\n};\r\n\r\nconst RealTimeEvents = () => {\r\n  // 状态管理\r\n  const [events, setEvents] = useState([]);\r\n  const [statistics, setStatistics] = useState({\r\n    total: 0,\r\n    byType: {}\r\n  });\r\n\r\n  // 订阅 WebSocket 消息\r\n  useEffect(() => {\r\n    const ws = new WebSocket(`ws://${window.location.hostname}:8083/mqtt`);\r\n    \r\n    ws.onmessage = (event) => {\r\n      try {\r\n        const message = JSON.parse(event.data);\r\n        if (message.type === 'message' && \r\n            message.topic === 'changli/cloud/v2x/rsu/rsi' && \r\n            message.payload.type === 'RSI') {\r\n          \r\n          const rsiData = message.payload.data;\r\n          const newEvents = (rsiData.rtes || []).map(event => ({\r\n            id: event.rteId,\r\n            type: event.eventType,\r\n            description: event.description || EVENT_TYPES[event.eventType]?.name || '未知事件',\r\n            time: new Date().toLocaleTimeString(),\r\n            startTime: event.startTime,\r\n            endTime: event.endTime\r\n          }));\r\n\r\n          // 更新事件列表\r\n          setEvents(prevEvents => {\r\n            const allEvents = [...newEvents, ...prevEvents];\r\n            // 只保留最近的10条事件\r\n            return allEvents.slice(0, 10);\r\n          });\r\n\r\n          // 更新统计信息\r\n          updateStatistics(newEvents);\r\n        }\r\n      } catch (error) {\r\n        console.error('处理WebSocket消息失败:', error);\r\n      }\r\n    };\r\n\r\n    return () => {\r\n      ws.close();\r\n    };\r\n  }, []);\r\n\r\n  // 更新统计信息\r\n  const updateStatistics = (newEvents) => {\r\n    setStatistics(prev => {\r\n      const newStats = { ...prev };\r\n      newStats.total = prev.total + newEvents.length;\r\n      \r\n      newEvents.forEach(event => {\r\n        const category = EVENT_TYPES[event.type]?.category || '其他';\r\n        newStats.byType[category] = (newStats.byType[category] || 0) + 1;\r\n      });\r\n      \r\n      return newStats;\r\n    });\r\n  };\r\n\r\n  // 生成饼图数据\r\n  const getPieChartData = () => {\r\n    return Object.entries(statistics.byType).map(([category, count]) => ({\r\n      title: category,\r\n      value: count,\r\n      color: getCategoryColor(category)\r\n    }));\r\n  };\r\n\r\n  // 获取类别颜色\r\n  const getCategoryColor = (category) => {\r\n    const colors = {\r\n      '超速': '#1890ff',\r\n      '急刹车': '#52c41a',\r\n      '变道': '#faad14',\r\n      '启动': '#f5222d',\r\n      '停止': '#722ed1'\r\n    };\r\n    return colors[category] || '#8c8c8c';\r\n  };\r\n\r\n  return (\r\n    <div style={{ padding: '20px' }}>\r\n      <Card title=\"车辆和设备总数信息\" size=\"small\">\r\n        <Row gutter={16}>\r\n          <Col span={8}>\r\n            <Statistic title=\"车辆总数\" value={3} />\r\n          </Col>\r\n          <Col span={8}>\r\n            <Statistic title=\"在线车辆\" value=\"0 / 3\" valueStyle={{ color: '#52c41a' }} />\r\n          </Col>\r\n          <Col span={8}>\r\n            <Statistic title=\"离线车辆\" value=\"3 / 3\" valueStyle={{ color: '#ff4d4f' }} />\r\n          </Col>\r\n        </Row>\r\n        <Divider style={{ margin: '12px 0' }} />\r\n        <Row gutter={16}>\r\n          <Col span={8}>\r\n            <Statistic title=\"设备总数\" value={13} />\r\n          </Col>\r\n          <Col span={8}>\r\n            <Statistic title=\"在线设备\" value=\"11 / 13\" valueStyle={{ color: '#52c41a' }} />\r\n          </Col>\r\n          <Col span={8}>\r\n            <Statistic title=\"离线设备\" value=\"2 / 13\" valueStyle={{ color: '#ff4d4f' }} />\r\n          </Col>\r\n        </Row>\r\n      </Card>\r\n\r\n      <Card title=\"实时事件列表\" size=\"small\" style={{ marginTop: '20px' }}>\r\n        {events.length === 0 ? (\r\n          <div style={{ textAlign: 'center', padding: '20px' }}>\r\n            暂无数据\r\n          </div>\r\n        ) : (\r\n          <List\r\n            size=\"small\"\r\n            dataSource={events}\r\n            renderItem={event => (\r\n              <List.Item>\r\n                <div style={{ \r\n                  display: 'flex', \r\n                  justifyContent: 'space-between', \r\n                  width: '100%',\r\n                  fontSize: '12px'\r\n                }}>\r\n                  <span style={{ \r\n                    color: EVENT_TYPES[event.type]?.color || '#8c8c8c'\r\n                  }}>\r\n                    {event.description}\r\n                  </span>\r\n                  <span style={{ color: '#8c8c8c' }}>{event.time}</span>\r\n                </div>\r\n              </List.Item>\r\n            )}\r\n          />\r\n        )}\r\n      </Card>\r\n\r\n      <Card title=\"实时事件统计\" size=\"small\" style={{ marginTop: '20px' }}>\r\n        <div style={{ height: '200px' }}>\r\n          <PieChart\r\n            data={getPieChartData()}\r\n            label={({ dataEntry }) => `${dataEntry.title}`}\r\n            labelStyle={{ fontSize: '8px', fill: '#fff' }}\r\n            labelPosition={70}\r\n            lineWidth={20}\r\n            animate\r\n          />\r\n        </div>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RealTimeEvents; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,OAAO,QAAQ,MAAM;AAC/D,SAASC,QAAQ,QAAQ,yBAAyB;;AAElD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG;EAClB,KAAK,EAAE;IAAEC,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAK,CAAC;EAC1D,KAAK,EAAE;IAAEF,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAK,CAAC;EAC1D,KAAK,EAAE;IAAEF,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAK,CAAC;EAC3D,KAAK,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAK,CAAC;EACzD,KAAK,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAK,CAAC;EACzD,MAAM,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAM,CAAC;EAC3D,KAAK,EAAE;IAAEF,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAK;AAC1D,CAAC;AAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC;IAC3CqB,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;EACX,CAAC,CAAC;;EAEF;EACArB,SAAS,CAAC,MAAM;IACd,MAAMsB,EAAE,GAAG,IAAIC,SAAS,CAAC,QAAQC,MAAM,CAACC,QAAQ,CAACC,QAAQ,YAAY,CAAC;IAEtEJ,EAAE,CAACK,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACI,IAAI,CAAC;QACtC,IAAIH,OAAO,CAACI,IAAI,KAAK,SAAS,IAC1BJ,OAAO,CAACK,KAAK,KAAK,2BAA2B,IAC7CL,OAAO,CAACM,OAAO,CAACF,IAAI,KAAK,KAAK,EAAE;UAElC,MAAMG,OAAO,GAAGP,OAAO,CAACM,OAAO,CAACH,IAAI;UACpC,MAAMK,SAAS,GAAG,CAACD,OAAO,CAACE,IAAI,IAAI,EAAE,EAAEC,GAAG,CAACX,KAAK;YAAA,IAAAY,qBAAA;YAAA,OAAK;cACnDC,EAAE,EAAEb,KAAK,CAACc,KAAK;cACfT,IAAI,EAAEL,KAAK,CAACe,SAAS;cACrBC,WAAW,EAAEhB,KAAK,CAACgB,WAAW,MAAAJ,qBAAA,GAAI9B,WAAW,CAACkB,KAAK,CAACe,SAAS,CAAC,cAAAH,qBAAA,uBAA5BA,qBAAA,CAA8B7B,IAAI,KAAI,MAAM;cAC9EkC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;cACrCC,SAAS,EAAEpB,KAAK,CAACoB,SAAS;cAC1BC,OAAO,EAAErB,KAAK,CAACqB;YACjB,CAAC;UAAA,CAAC,CAAC;;UAEH;UACAhC,SAAS,CAACiC,UAAU,IAAI;YACtB,MAAMC,SAAS,GAAG,CAAC,GAAGd,SAAS,EAAE,GAAGa,UAAU,CAAC;YAC/C;YACA,OAAOC,SAAS,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;UAC/B,CAAC,CAAC;;UAEF;UACAC,gBAAgB,CAAChB,SAAS,CAAC;QAC7B;MACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAED,OAAO,MAAM;MACXhC,EAAE,CAACkC,KAAK,CAAC,CAAC;IACZ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMH,gBAAgB,GAAIhB,SAAS,IAAK;IACtClB,aAAa,CAACsC,IAAI,IAAI;MACpB,MAAMC,QAAQ,GAAG;QAAE,GAAGD;MAAK,CAAC;MAC5BC,QAAQ,CAACtC,KAAK,GAAGqC,IAAI,CAACrC,KAAK,GAAGiB,SAAS,CAACsB,MAAM;MAE9CtB,SAAS,CAACuB,OAAO,CAAChC,KAAK,IAAI;QAAA,IAAAiC,qBAAA;QACzB,MAAMhD,QAAQ,GAAG,EAAAgD,qBAAA,GAAAnD,WAAW,CAACkB,KAAK,CAACK,IAAI,CAAC,cAAA4B,qBAAA,uBAAvBA,qBAAA,CAAyBhD,QAAQ,KAAI,IAAI;QAC1D6C,QAAQ,CAACrC,MAAM,CAACR,QAAQ,CAAC,GAAG,CAAC6C,QAAQ,CAACrC,MAAM,CAACR,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;MAClE,CAAC,CAAC;MAEF,OAAO6C,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAOC,MAAM,CAACC,OAAO,CAAC9C,UAAU,CAACG,MAAM,CAAC,CAACkB,GAAG,CAAC,CAAC,CAAC1B,QAAQ,EAAEoD,KAAK,CAAC,MAAM;MACnEC,KAAK,EAAErD,QAAQ;MACfsD,KAAK,EAAEF,KAAK;MACZrD,KAAK,EAAEwD,gBAAgB,CAACvD,QAAQ;IAClC,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMuD,gBAAgB,GAAIvD,QAAQ,IAAK;IACrC,MAAMwD,MAAM,GAAG;MACb,IAAI,EAAE,SAAS;MACf,KAAK,EAAE,SAAS;MAChB,IAAI,EAAE,SAAS;MACf,IAAI,EAAE,SAAS;MACf,IAAI,EAAE;IACR,CAAC;IACD,OAAOA,MAAM,CAACxD,QAAQ,CAAC,IAAI,SAAS;EACtC,CAAC;EAED,oBACEJ,OAAA;IAAK6D,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9B/D,OAAA,CAACR,IAAI;MAACiE,KAAK,EAAC,wDAAW;MAACO,IAAI,EAAC,OAAO;MAAAD,QAAA,gBAClC/D,OAAA,CAACL,GAAG;QAACsE,MAAM,EAAE,EAAG;QAAAF,QAAA,gBACd/D,OAAA,CAACJ,GAAG;UAACsE,IAAI,EAAE,CAAE;UAAAH,QAAA,eACX/D,OAAA,CAACN,SAAS;YAAC+D,KAAK,EAAC,0BAAM;YAACC,KAAK,EAAE;UAAE;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACNtE,OAAA,CAACJ,GAAG;UAACsE,IAAI,EAAE,CAAE;UAAAH,QAAA,eACX/D,OAAA,CAACN,SAAS;YAAC+D,KAAK,EAAC,0BAAM;YAACC,KAAK,EAAC,OAAO;YAACa,UAAU,EAAE;cAAEpE,KAAK,EAAE;YAAU;UAAE;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eACNtE,OAAA,CAACJ,GAAG;UAACsE,IAAI,EAAE,CAAE;UAAAH,QAAA,eACX/D,OAAA,CAACN,SAAS;YAAC+D,KAAK,EAAC,0BAAM;YAACC,KAAK,EAAC,OAAO;YAACa,UAAU,EAAE;cAAEpE,KAAK,EAAE;YAAU;UAAE;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNtE,OAAA,CAACH,OAAO;QAACgE,KAAK,EAAE;UAAEW,MAAM,EAAE;QAAS;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxCtE,OAAA,CAACL,GAAG;QAACsE,MAAM,EAAE,EAAG;QAAAF,QAAA,gBACd/D,OAAA,CAACJ,GAAG;UAACsE,IAAI,EAAE,CAAE;UAAAH,QAAA,eACX/D,OAAA,CAACN,SAAS;YAAC+D,KAAK,EAAC,0BAAM;YAACC,KAAK,EAAE;UAAG;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACNtE,OAAA,CAACJ,GAAG;UAACsE,IAAI,EAAE,CAAE;UAAAH,QAAA,eACX/D,OAAA,CAACN,SAAS;YAAC+D,KAAK,EAAC,0BAAM;YAACC,KAAK,EAAC,SAAS;YAACa,UAAU,EAAE;cAAEpE,KAAK,EAAE;YAAU;UAAE;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACNtE,OAAA,CAACJ,GAAG;UAACsE,IAAI,EAAE,CAAE;UAAAH,QAAA,eACX/D,OAAA,CAACN,SAAS;YAAC+D,KAAK,EAAC,0BAAM;YAACC,KAAK,EAAC,QAAQ;YAACa,UAAU,EAAE;cAAEpE,KAAK,EAAE;YAAU;UAAE;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPtE,OAAA,CAACR,IAAI;MAACiE,KAAK,EAAC,sCAAQ;MAACO,IAAI,EAAC,OAAO;MAACH,KAAK,EAAE;QAAEY,SAAS,EAAE;MAAO,CAAE;MAAAV,QAAA,EAC5DxD,MAAM,CAAC2C,MAAM,KAAK,CAAC,gBAClBlD,OAAA;QAAK6D,KAAK,EAAE;UAAEa,SAAS,EAAE,QAAQ;UAAEZ,OAAO,EAAE;QAAO,CAAE;QAAAC,QAAA,EAAC;MAEtD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAENtE,OAAA,CAACP,IAAI;QACHuE,IAAI,EAAC,OAAO;QACZW,UAAU,EAAEpE,MAAO;QACnBqE,UAAU,EAAEzD,KAAK;UAAA,IAAA0D,sBAAA;UAAA,oBACf7E,OAAA,CAACP,IAAI,CAACqF,IAAI;YAAAf,QAAA,eACR/D,OAAA;cAAK6D,KAAK,EAAE;gBACVkB,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAE,eAAe;gBAC/BC,KAAK,EAAE,MAAM;gBACbC,QAAQ,EAAE;cACZ,CAAE;cAAAnB,QAAA,gBACA/D,OAAA;gBAAM6D,KAAK,EAAE;kBACX1D,KAAK,EAAE,EAAA0E,sBAAA,GAAA5E,WAAW,CAACkB,KAAK,CAACK,IAAI,CAAC,cAAAqD,sBAAA,uBAAvBA,sBAAA,CAAyB1E,KAAK,KAAI;gBAC3C,CAAE;gBAAA4D,QAAA,EACC5C,KAAK,CAACgB;cAAW;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACPtE,OAAA;gBAAM6D,KAAK,EAAE;kBAAE1D,KAAK,EAAE;gBAAU,CAAE;gBAAA4D,QAAA,EAAE5C,KAAK,CAACiB;cAAI;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;MACZ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEPtE,OAAA,CAACR,IAAI;MAACiE,KAAK,EAAC,sCAAQ;MAACO,IAAI,EAAC,OAAO;MAACH,KAAK,EAAE;QAAEY,SAAS,EAAE;MAAO,CAAE;MAAAV,QAAA,eAC7D/D,OAAA;QAAK6D,KAAK,EAAE;UAAEsB,MAAM,EAAE;QAAQ,CAAE;QAAApB,QAAA,eAC9B/D,OAAA,CAACF,QAAQ;UACPyB,IAAI,EAAE8B,eAAe,CAAC,CAAE;UACxB+B,KAAK,EAAEA,CAAC;YAAEC;UAAU,CAAC,KAAK,GAAGA,SAAS,CAAC5B,KAAK,EAAG;UAC/C6B,UAAU,EAAE;YAAEJ,QAAQ,EAAE,KAAK;YAAEK,IAAI,EAAE;UAAO,CAAE;UAC9CC,aAAa,EAAE,EAAG;UAClBC,SAAS,EAAE,EAAG;UACdC,OAAO;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAChE,EAAA,CA7JID,cAAc;AAAAsF,EAAA,GAAdtF,cAAc;AA+JpB,eAAeA,cAAc;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}