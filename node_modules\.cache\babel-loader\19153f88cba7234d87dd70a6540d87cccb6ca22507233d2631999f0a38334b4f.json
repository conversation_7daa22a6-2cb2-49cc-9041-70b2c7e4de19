{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\DevicePopoverContent.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport VideoPlayer from './VideoPlayer'; // 引入摄像头视频播放组件\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n/**\r\n * 设备弹窗内容组件\r\n * @param {object} props.device 设备对象\r\n */\nconst DevicePopoverContent = ({\n  device\n}) => {\n  _s();\n  if (!device) return null;\n  const isCamera = device.type === 'camera';\n  const isRSU = device.type === 'rsu';\n  const imgPath = `${BASE_URL}/images/${device.type}.png`;\n  const flvBase = process.env.REACT_APP_FLV_URL || 'http://localhost:8000';\n  const flvUrl = device.rtspUrl ? `${flvBase}/live/${device.id}.flv` : null;\n\n  // ========== RSU MQTT消息下发相关本地状态 ==========\n  const [messageType, setMessageType] = useState('RSI'); // 消息类型，默认RSI\n  const [mqttSending, setMqttSending] = useState(false); // MQTT下发状态\n  const [mqttResult, setMqttResult] = useState(''); // MQTT下发结果\n  const [selectedDataFile, setSelectedDataFile] = useState(null); // 选中的数据文件\n  const [fileContent, setFileContent] = useState(''); // 文件内容（16进制字符串）\n\n  // 处理数据文件选择\n  const handleDataFileChange = e => {\n    const file = e.target.files[0];\n    setSelectedDataFile(file);\n    setMqttResult('');\n    if (file) {\n      // 读取文件内容\n      const reader = new FileReader();\n      reader.onload = event => {\n        try {\n          const content = event.target.result;\n          // 将文件内容转换为16进制字符串\n          const hexString = convertToHexString(content);\n          setFileContent(hexString);\n          setMqttResult(`文件 \"${file.name}\" 加载成功，数据长度: ${hexString.length} 字符`);\n        } catch (error) {\n          setMqttResult(`文件读取失败: ${error.message}`);\n          setFileContent('');\n        }\n      };\n      reader.onerror = () => {\n        setMqttResult('文件读取失败');\n        setFileContent('');\n      };\n      // 以二进制方式读取文件\n      reader.readAsArrayBuffer(file);\n    } else {\n      setFileContent('');\n    }\n  };\n\n  // 将文件内容转换为16进制字符串\n  const convertToHexString = arrayBuffer => {\n    const uint8Array = new Uint8Array(arrayBuffer);\n    return Array.from(uint8Array).map(byte => byte.toString(16).padStart(2, '0').toUpperCase()).join('');\n  };\n\n  // 处理MQTT消息下发\n  const handleMqttSend = async () => {\n    if (!device.mac) {\n      setMqttResult('RSU设备未配置MAC地址，无法下发MQTT消息');\n      return;\n    }\n    if (!fileContent) {\n      setMqttResult('请先选择并加载数据文件');\n      return;\n    }\n    setMqttSending(true);\n    setMqttResult('正在下发MQTT消息...');\n    const startTime = Date.now();\n    try {\n      const apiUrl = BASE_URL + '/api/rsu/send-mqtt';\n      console.log('开始发送MQTT请求:', {\n        url: apiUrl,\n        mac: device.mac,\n        messageType: messageType,\n        dataLength: fileContent.length\n      });\n      const resp = await axios.post(apiUrl, {\n        mac: device.mac,\n        messageType: messageType,\n        deviceId: device.id,\n        deviceName: device.name,\n        fileContent: fileContent // 传递文件内容的16进制字符串\n      }, {\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        timeout: 15000 // 增加到15秒\n      });\n      const duration = Date.now() - startTime;\n      console.log(`MQTT请求完成，耗时: ${duration}ms`, resp.data);\n      if (resp.data && resp.data.success) {\n        setMqttResult(`${messageType}消息下发成功！Topic: ${resp.data.topic} (耗时: ${duration}ms)`);\n      } else {\n        var _resp$data;\n        setMqttResult('MQTT消息下发失败：' + (((_resp$data = resp.data) === null || _resp$data === void 0 ? void 0 : _resp$data.message) || '未知错误'));\n      }\n    } catch (err) {\n      const duration = Date.now() - startTime;\n      console.error('MQTT请求失败:', err);\n      let errorMessage = 'MQTT消息下发失败：';\n      if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {\n        errorMessage += `请求超时 (${duration}ms)，请检查MQTT服务状态`;\n      } else if (err.response) {\n        var _err$response$data;\n        // 服务器返回了错误响应\n        errorMessage += ((_err$response$data = err.response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || `服务器错误 (${err.response.status})`;\n      } else if (err.request) {\n        // 请求发出但没有收到响应\n        errorMessage += '网络连接失败，请检查服务器状态';\n      } else {\n        // 其他错误\n        errorMessage += err.message || '未知错误';\n      }\n      setMqttResult(errorMessage);\n    } finally {\n      setMqttSending(false);\n    }\n  };\n\n  // ========== 弹窗内容渲染 ==========\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '10px',\n      width: 320,\n      maxWidth: 350\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 6,\n        textAlign: 'center'\n      },\n      children: isCamera ? device.rtspUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: 300,\n          height: 180,\n          background: '#000',\n          margin: '0 auto',\n          borderRadius: 6,\n          overflow: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(VideoPlayer, {\n          deviceId: device.id,\n          rtspUrl: device.rtspUrl\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: 300,\n          height: 180,\n          background: '#222',\n          color: '#fff',\n          lineHeight: '180px',\n          borderRadius: 6,\n          margin: '0 auto'\n        },\n        children: \"\\u65E0\\u89C6\\u9891\\u6D41\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n        src: imgPath,\n        alt: device.type,\n        style: {\n          width: 120,\n          height: 120,\n          objectFit: 'contain',\n          background: '#fff',\n          borderRadius: 8,\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 15,\n        fontWeight: 'bold',\n        marginBottom: 6\n      },\n      children: device.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 13,\n        color: '#888',\n        marginBottom: 6\n      },\n      children: [\"\\u7C7B\\u578B\\uFF1A\", device.type]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 13,\n        color: '#888',\n        marginBottom: 6\n      },\n      children: [\"\\u4F4D\\u7F6E\\uFF1A\", device.location, \" \", device.entrance ? `(${device.entrance})` : '']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 13,\n        color: '#888',\n        marginBottom: 6\n      },\n      children: [\"\\u72B6\\u6001\\uFF1A\", device.status === 'online' ? /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: '#52c41a'\n        },\n        children: \"\\u5728\\u7EBF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 102\n      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: '#f5222d'\n        },\n        children: \"\\u79BB\\u7EBF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 149\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this), device.ipAddress && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 13,\n        color: '#888',\n        marginBottom: 6\n      },\n      children: [\"IP\\uFF1A\", device.ipAddress]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 28\n    }, this), device.manufacturer && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 13,\n        color: '#888',\n        marginBottom: 6\n      },\n      children: [\"\\u5382\\u5546\\uFF1A\", device.manufacturer]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 31\n    }, this), device.model && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 13,\n        color: '#888',\n        marginBottom: 6\n      },\n      children: [\"\\u578B\\u53F7\\uFF1A\", device.model]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 24\n    }, this), device.description && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 13,\n        color: '#888',\n        marginBottom: 6\n      },\n      children: [\"\\u63CF\\u8FF0\\uFF1A\", device.description]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 30\n    }, this), isRSU && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: 16,\n        padding: 12,\n        background: '#fff',\n        borderRadius: 10,\n        boxShadow: '0 2px 8px rgba(0,0,0,0.07)',\n        border: '1px solid #e6e6e6'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold',\n          fontSize: 14,\n          marginBottom: 8,\n          color: '#52c41a',\n          letterSpacing: 1\n        },\n        children: \"\\u6D88\\u606F\\u4E0B\\u53D1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 10\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 13,\n            color: '#333',\n            marginRight: 6\n          },\n          children: \"\\u6D88\\u606F\\u7C7B\\u578B\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: messageType,\n          onChange: e => {\n            setMessageType(e.target.value);\n            setMqttResult(''); // 清空之前的结果\n            setFileContent(''); // 清空文件内容\n            setSelectedDataFile(null); // 清空选中的文件\n          },\n          style: {\n            width: '100%',\n            padding: '4px 6px',\n            borderRadius: 4,\n            border: '1px solid #d9d9d9',\n            fontSize: 13,\n            color: '#333',\n            backgroundColor: '#f9f9f9'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"RSI\",\n            children: \"RSI - \\u8DEF\\u4FA7\\u4E8B\\u4EF6\\u548C\\u6807\\u724C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"MAP\",\n            children: \"MAP - \\u5730\\u56FE\\u6D88\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 10\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 13,\n            color: '#333',\n            marginBottom: 4,\n            display: 'block'\n          },\n          children: [\"\\u9009\\u62E9\", messageType, \"\\u6570\\u636E\\u6587\\u4EF6\\uFF1A\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          onChange: handleDataFileChange,\n          accept: \",.per,.bin,.dat,.hex\",\n          style: {\n            width: '100%',\n            color: '#333',\n            fontSize: 13,\n            padding: '4px 6px',\n            borderRadius: 4,\n            border: '1px solid #d9d9d9',\n            backgroundColor: '#f9f9f9'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this), selectedDataFile && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: 12,\n            color: '#666',\n            marginTop: 4\n          },\n          children: [\"\\u5DF2\\u9009\\u62E9: \", selectedDataFile.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this), fileContent && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 10\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 13,\n            color: '#333',\n            marginBottom: 4,\n            display: 'block'\n          },\n          children: \"\\u6570\\u636E\\u9884\\u89C8 (\\u524D100\\u5B57\\u7B26):\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: 12,\n            color: '#666',\n            backgroundColor: '#f5f5f5',\n            padding: 6,\n            borderRadius: 4,\n            border: '1px solid #e6e6e6',\n            fontFamily: 'monospace',\n            wordBreak: 'break-all',\n            maxHeight: 60,\n            overflow: 'auto'\n          },\n          children: [fileContent.substring(0, 100), fileContent.length > 100 ? '...' : '']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: 12,\n            color: '#999',\n            marginTop: 2\n          },\n          children: [\"\\u603B\\u957F\\u5EA6: \", fileContent.length, \" \\u5B57\\u7B26\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleMqttSend,\n        disabled: mqttSending || !device.mac || !fileContent,\n        style: {\n          width: '100%',\n          padding: 7,\n          background: device.mac && fileContent ? '#52c41a' : '#d9d9d9',\n          color: '#fff',\n          border: 'none',\n          borderRadius: 4,\n          cursor: mqttSending || !device.mac || !fileContent ? 'not-allowed' : 'pointer',\n          fontWeight: 'bold',\n          fontSize: 15,\n          letterSpacing: 1\n        },\n        children: mqttSending ? '正在下发...' : `下发${messageType}消息`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this), mqttResult && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 10,\n          color: mqttResult.includes('成功') ? '#52c41a' : '#f5222d',\n          fontSize: 13,\n          wordBreak: 'break-word'\n        },\n        children: mqttResult\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 12,\n          color: '#666',\n          marginTop: 6\n        },\n        children: [\"MAC\\u5730\\u5740\\uFF1A\", device.mac || '未配置']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(DevicePopoverContent, \"OCnK3svJsj03JkP4PJxhP7jyhRU=\");\n_c = DevicePopoverContent;\nexport default DevicePopoverContent;\nvar _c;\n$RefreshReg$(_c, \"DevicePopoverContent\");", "map": {"version": 3, "names": ["React", "useState", "axios", "VideoPlayer", "jsxDEV", "_jsxDEV", "BASE_URL", "process", "env", "REACT_APP_API_URL", "DevicePopoverContent", "device", "_s", "isCamera", "type", "isRSU", "imgPath", "flvBase", "REACT_APP_FLV_URL", "flvUrl", "rtspUrl", "id", "messageType", "setMessageType", "mqttSending", "setMqttSending", "mqttResult", "setMqttResult", "selectedDataFile", "setSelectedDataFile", "fileContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDataFileChange", "e", "file", "target", "files", "reader", "FileReader", "onload", "event", "content", "result", "hexString", "convertToHexString", "name", "length", "error", "message", "onerror", "readAsA<PERSON>y<PERSON><PERSON>er", "arrayBuffer", "uint8Array", "Uint8Array", "Array", "from", "map", "byte", "toString", "padStart", "toUpperCase", "join", "handleMqttSend", "mac", "startTime", "Date", "now", "apiUrl", "console", "log", "url", "dataLength", "resp", "post", "deviceId", "deviceName", "headers", "timeout", "duration", "data", "success", "topic", "_resp$data", "err", "errorMessage", "code", "includes", "response", "_err$response$data", "status", "request", "style", "padding", "width", "max<PERSON><PERSON><PERSON>", "children", "marginBottom", "textAlign", "height", "background", "margin", "borderRadius", "overflow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "lineHeight", "src", "alt", "objectFit", "boxShadow", "fontSize", "fontWeight", "location", "entrance", "ip<PERSON><PERSON><PERSON>", "manufacturer", "model", "description", "marginTop", "border", "letterSpacing", "marginRight", "value", "onChange", "backgroundColor", "display", "accept", "fontFamily", "wordBreak", "maxHeight", "substring", "onClick", "disabled", "cursor", "_c", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/DevicePopoverContent.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport axios from 'axios';\r\nimport VideoPlayer from './VideoPlayer'; // 引入摄像头视频播放组件\r\n\r\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n\r\n/**\r\n * 设备弹窗内容组件\r\n * @param {object} props.device 设备对象\r\n */\r\nconst DevicePopoverContent = ({ device }) => {\r\n  if (!device) return null;\r\n  const isCamera = device.type === 'camera';\r\n  const isRSU = device.type === 'rsu';\r\n  const imgPath = `${BASE_URL}/images/${device.type}.png`;\r\n  const flvBase = process.env.REACT_APP_FLV_URL || 'http://localhost:8000';\r\n  const flvUrl = device.rtspUrl ? `${flvBase}/live/${device.id}.flv` : null;\r\n\r\n  // ========== RSU MQTT消息下发相关本地状态 ==========\r\n  const [messageType, setMessageType] = useState('RSI'); // 消息类型，默认RSI\r\n  const [mqttSending, setMqttSending] = useState(false); // MQTT下发状态\r\n  const [mqttResult, setMqttResult] = useState(''); // MQTT下发结果\r\n  const [selectedDataFile, setSelectedDataFile] = useState(null); // 选中的数据文件\r\n  const [fileContent, setFileContent] = useState(''); // 文件内容（16进制字符串）\r\n\r\n  // 处理数据文件选择\r\n  const handleDataFileChange = (e) => {\r\n    const file = e.target.files[0];\r\n    setSelectedDataFile(file);\r\n    setMqttResult('');\r\n\r\n    if (file) {\r\n      // 读取文件内容\r\n      const reader = new FileReader();\r\n      reader.onload = (event) => {\r\n        try {\r\n          const content = event.target.result;\r\n          // 将文件内容转换为16进制字符串\r\n          const hexString = convertToHexString(content);\r\n          setFileContent(hexString);\r\n          setMqttResult(`文件 \"${file.name}\" 加载成功，数据长度: ${hexString.length} 字符`);\r\n        } catch (error) {\r\n          setMqttResult(`文件读取失败: ${error.message}`);\r\n          setFileContent('');\r\n        }\r\n      };\r\n      reader.onerror = () => {\r\n        setMqttResult('文件读取失败');\r\n        setFileContent('');\r\n      };\r\n      // 以二进制方式读取文件\r\n      reader.readAsArrayBuffer(file);\r\n    } else {\r\n      setFileContent('');\r\n    }\r\n  };\r\n\r\n  // 将文件内容转换为16进制字符串\r\n  const convertToHexString = (arrayBuffer) => {\r\n    const uint8Array = new Uint8Array(arrayBuffer);\r\n    return Array.from(uint8Array)\r\n      .map(byte => byte.toString(16).padStart(2, '0').toUpperCase())\r\n      .join('');\r\n  };\r\n\r\n  // 处理MQTT消息下发\r\n  const handleMqttSend = async () => {\r\n    if (!device.mac) {\r\n      setMqttResult('RSU设备未配置MAC地址，无法下发MQTT消息');\r\n      return;\r\n    }\r\n    if (!fileContent) {\r\n      setMqttResult('请先选择并加载数据文件');\r\n      return;\r\n    }\r\n    setMqttSending(true);\r\n    setMqttResult('正在下发MQTT消息...');\r\n\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n      const apiUrl = BASE_URL + '/api/rsu/send-mqtt';\r\n      console.log('开始发送MQTT请求:', {\r\n        url: apiUrl,\r\n        mac: device.mac,\r\n        messageType: messageType,\r\n        dataLength: fileContent.length\r\n      });\r\n\r\n      const resp = await axios.post(apiUrl, {\r\n        mac: device.mac,\r\n        messageType: messageType,\r\n        deviceId: device.id,\r\n        deviceName: device.name,\r\n        fileContent: fileContent // 传递文件内容的16进制字符串\r\n      }, {\r\n        headers: { 'Content-Type': 'application/json' },\r\n        timeout: 15000 // 增加到15秒\r\n      });\r\n\r\n      const duration = Date.now() - startTime;\r\n      console.log(`MQTT请求完成，耗时: ${duration}ms`, resp.data);\r\n\r\n      if (resp.data && resp.data.success) {\r\n        setMqttResult(`${messageType}消息下发成功！Topic: ${resp.data.topic} (耗时: ${duration}ms)`);\r\n      } else {\r\n        setMqttResult('MQTT消息下发失败：' + (resp.data?.message || '未知错误'));\r\n      }\r\n    } catch (err) {\r\n      const duration = Date.now() - startTime;\r\n      console.error('MQTT请求失败:', err);\r\n\r\n      let errorMessage = 'MQTT消息下发失败：';\r\n      if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {\r\n        errorMessage += `请求超时 (${duration}ms)，请检查MQTT服务状态`;\r\n      } else if (err.response) {\r\n        // 服务器返回了错误响应\r\n        errorMessage += err.response.data?.message || `服务器错误 (${err.response.status})`;\r\n      } else if (err.request) {\r\n        // 请求发出但没有收到响应\r\n        errorMessage += '网络连接失败，请检查服务器状态';\r\n      } else {\r\n        // 其他错误\r\n        errorMessage += err.message || '未知错误';\r\n      }\r\n\r\n      setMqttResult(errorMessage);\r\n    } finally {\r\n      setMqttSending(false);\r\n    }\r\n  };\r\n\r\n  // ========== 弹窗内容渲染 ==========\r\n  return (\r\n    <div style={{ padding: '10px', width: 320, maxWidth: 350 }}>\r\n      <div style={{ marginBottom: 6, textAlign: 'center' }}>\r\n        {isCamera ? (\r\n          device.rtspUrl ? (\r\n            <div style={{ width: 300, height: 180, background: '#000', margin: '0 auto', borderRadius: 6, overflow: 'hidden' }}>\r\n              {/* 这里可以放摄像头视频组件 */}\r\n              {/* <video src={flvUrl} controls style={{ width: '100%', height: '100%' }} /> */}\r\n              <VideoPlayer deviceId={device.id} rtspUrl={device.rtspUrl} />\r\n            </div>\r\n          ) : (\r\n            <div style={{ width: 300, height: 180, background: '#222', color: '#fff', lineHeight: '180px', borderRadius: 6, margin: '0 auto' }}>无视频流</div>\r\n          )\r\n        ) : (\r\n          <img src={imgPath} alt={device.type} style={{ width: 120, height: 120, objectFit: 'contain', background: '#fff', borderRadius: 8, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }} />\r\n        )}\r\n      </div>\r\n      <div style={{ fontSize: 15, fontWeight: 'bold', marginBottom: 6 }}>{device.name}</div>\r\n      <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>类型：{device.type}</div>\r\n      <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>位置：{device.location} {device.entrance ? `(${device.entrance})` : ''}</div>\r\n      <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>状态：{device.status === 'online' ? <span style={{ color: '#52c41a' }}>在线</span> : <span style={{ color: '#f5222d' }}>离线</span>}</div>\r\n      {device.ipAddress && <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>IP：{device.ipAddress}</div>}\r\n      {device.manufacturer && <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>厂商：{device.manufacturer}</div>}\r\n      {device.model && <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>型号：{device.model}</div>}\r\n      {device.description && <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>描述：{device.description}</div>}\r\n      {/* ========== RSU MQTT消息下发功能区域 ========== */}\r\n      {isRSU && (\r\n        <div style={{\r\n          marginTop: 16,\r\n          padding: 12,\r\n          background: '#fff',\r\n          borderRadius: 10,\r\n          boxShadow: '0 2px 8px rgba(0,0,0,0.07)',\r\n          border: '1px solid #e6e6e6',\r\n        }}>\r\n          {/* MQTT消息下发区域 */}\r\n          <div style={{\r\n            fontWeight: 'bold',\r\n            fontSize: 14,\r\n            marginBottom: 8,\r\n            color: '#52c41a',\r\n            letterSpacing: 1\r\n          }}>消息下发</div>\r\n\r\n          {/* 消息类型选择 */}\r\n          <div style={{ marginBottom: 10 }}>\r\n            <span style={{ fontSize: 13, color: '#333', marginRight: 6 }}>消息类型：</span>\r\n            <select\r\n              value={messageType}\r\n              onChange={(e) => {\r\n                setMessageType(e.target.value);\r\n                setMqttResult(''); // 清空之前的结果\r\n                setFileContent(''); // 清空文件内容\r\n                setSelectedDataFile(null); // 清空选中的文件\r\n              }}\r\n              style={{\r\n                width: '100%',\r\n                padding: '4px 6px',\r\n                borderRadius: 4,\r\n                border: '1px solid #d9d9d9',\r\n                fontSize: 13,\r\n                color: '#333',\r\n                backgroundColor: '#f9f9f9'\r\n              }}\r\n            >\r\n              <option value=\"RSI\">RSI - 路侧事件和标牌</option>\r\n              <option value=\"MAP\">MAP - 地图消息</option>\r\n            </select>\r\n          </div>\r\n\r\n          {/* 数据文件选择 */}\r\n          <div style={{ marginBottom: 10 }}>\r\n            <span style={{ fontSize: 13, color: '#333', marginBottom: 4, display: 'block' }}>\r\n              选择{messageType}数据文件：\r\n            </span>\r\n            <input\r\n              type=\"file\"\r\n              onChange={handleDataFileChange}\r\n              accept=\",.per,.bin,.dat,.hex\"\r\n              style={{\r\n                width: '100%',\r\n                color: '#333',\r\n                fontSize: 13,\r\n                padding: '4px 6px',\r\n                borderRadius: 4,\r\n                border: '1px solid #d9d9d9',\r\n                backgroundColor: '#f9f9f9',\r\n              }}\r\n            />\r\n            {selectedDataFile && (\r\n              <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>\r\n                已选择: {selectedDataFile.name}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* 文件内容预览 */}\r\n          {fileContent && (\r\n            <div style={{ marginBottom: 10 }}>\r\n              <span style={{ fontSize: 13, color: '#333', marginBottom: 4, display: 'block' }}>\r\n                数据预览 (前100字符):\r\n              </span>\r\n              <div style={{\r\n                fontSize: 12,\r\n                color: '#666',\r\n                backgroundColor: '#f5f5f5',\r\n                padding: 6,\r\n                borderRadius: 4,\r\n                border: '1px solid #e6e6e6',\r\n                fontFamily: 'monospace',\r\n                wordBreak: 'break-all',\r\n                maxHeight: 60,\r\n                overflow: 'auto'\r\n              }}>\r\n                {fileContent.substring(0, 100)}{fileContent.length > 100 ? '...' : ''}\r\n              </div>\r\n              <div style={{ fontSize: 12, color: '#999', marginTop: 2 }}>\r\n                总长度: {fileContent.length} 字符\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* 下发按钮 */}\r\n          <button\r\n            onClick={handleMqttSend}\r\n            disabled={mqttSending || !device.mac || !fileContent}\r\n            style={{\r\n              width: '100%',\r\n              padding: 7,\r\n              background: (device.mac && fileContent) ? '#52c41a' : '#d9d9d9',\r\n              color: '#fff',\r\n              border: 'none',\r\n              borderRadius: 4,\r\n              cursor: (mqttSending || !device.mac || !fileContent) ? 'not-allowed' : 'pointer',\r\n              fontWeight: 'bold',\r\n              fontSize: 15,\r\n              letterSpacing: 1\r\n            }}\r\n          >\r\n            {mqttSending ? '正在下发...' : `下发${messageType}消息`}\r\n          </button>\r\n\r\n          {/* 结果显示 */}\r\n          {mqttResult && (\r\n            <div style={{\r\n              marginTop: 10,\r\n              color: mqttResult.includes('成功') ? '#52c41a' : '#f5222d',\r\n              fontSize: 13,\r\n              wordBreak: 'break-word'\r\n            }}>\r\n              {mqttResult}\r\n            </div>\r\n          )}\r\n\r\n          {/* 设备信息 */}\r\n          <div style={{ fontSize: 12, color: '#666', marginTop: 6 }}>\r\n            MAC地址：{device.mac || '未配置'}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DevicePopoverContent;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,eAAe,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAEzE;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC3C,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EACxB,MAAME,QAAQ,GAAGF,MAAM,CAACG,IAAI,KAAK,QAAQ;EACzC,MAAMC,KAAK,GAAGJ,MAAM,CAACG,IAAI,KAAK,KAAK;EACnC,MAAME,OAAO,GAAG,GAAGV,QAAQ,WAAWK,MAAM,CAACG,IAAI,MAAM;EACvD,MAAMG,OAAO,GAAGV,OAAO,CAACC,GAAG,CAACU,iBAAiB,IAAI,uBAAuB;EACxE,MAAMC,MAAM,GAAGR,MAAM,CAACS,OAAO,GAAG,GAAGH,OAAO,SAASN,MAAM,CAACU,EAAE,MAAM,GAAG,IAAI;;EAEzE;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACvD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACvD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM+B,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9BP,mBAAmB,CAACK,IAAI,CAAC;IACzBP,aAAa,CAAC,EAAE,CAAC;IAEjB,IAAIO,IAAI,EAAE;MACR;MACA,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,KAAK,IAAK;QACzB,IAAI;UACF,MAAMC,OAAO,GAAGD,KAAK,CAACL,MAAM,CAACO,MAAM;UACnC;UACA,MAAMC,SAAS,GAAGC,kBAAkB,CAACH,OAAO,CAAC;UAC7CV,cAAc,CAACY,SAAS,CAAC;UACzBhB,aAAa,CAAC,OAAOO,IAAI,CAACW,IAAI,gBAAgBF,SAAS,CAACG,MAAM,KAAK,CAAC;QACtE,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdpB,aAAa,CAAC,WAAWoB,KAAK,CAACC,OAAO,EAAE,CAAC;UACzCjB,cAAc,CAAC,EAAE,CAAC;QACpB;MACF,CAAC;MACDM,MAAM,CAACY,OAAO,GAAG,MAAM;QACrBtB,aAAa,CAAC,QAAQ,CAAC;QACvBI,cAAc,CAAC,EAAE,CAAC;MACpB,CAAC;MACD;MACAM,MAAM,CAACa,iBAAiB,CAAChB,IAAI,CAAC;IAChC,CAAC,MAAM;MACLH,cAAc,CAAC,EAAE,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAMa,kBAAkB,GAAIO,WAAW,IAAK;IAC1C,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAACF,WAAW,CAAC;IAC9C,OAAOG,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC,CAC1BI,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAC7DC,IAAI,CAAC,EAAE,CAAC;EACb,CAAC;;EAED;EACA,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACnD,MAAM,CAACoD,GAAG,EAAE;MACfpC,aAAa,CAAC,0BAA0B,CAAC;MACzC;IACF;IACA,IAAI,CAACG,WAAW,EAAE;MAChBH,aAAa,CAAC,aAAa,CAAC;MAC5B;IACF;IACAF,cAAc,CAAC,IAAI,CAAC;IACpBE,aAAa,CAAC,eAAe,CAAC;IAE9B,MAAMqC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAE5B,IAAI;MACF,MAAMC,MAAM,GAAG7D,QAAQ,GAAG,oBAAoB;MAC9C8D,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBC,GAAG,EAAEH,MAAM;QACXJ,GAAG,EAAEpD,MAAM,CAACoD,GAAG;QACfzC,WAAW,EAAEA,WAAW;QACxBiD,UAAU,EAAEzC,WAAW,CAACgB;MAC1B,CAAC,CAAC;MAEF,MAAM0B,IAAI,GAAG,MAAMtE,KAAK,CAACuE,IAAI,CAACN,MAAM,EAAE;QACpCJ,GAAG,EAAEpD,MAAM,CAACoD,GAAG;QACfzC,WAAW,EAAEA,WAAW;QACxBoD,QAAQ,EAAE/D,MAAM,CAACU,EAAE;QACnBsD,UAAU,EAAEhE,MAAM,CAACkC,IAAI;QACvBf,WAAW,EAAEA,WAAW,CAAC;MAC3B,CAAC,EAAE;QACD8C,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,OAAO,EAAE,KAAK,CAAC;MACjB,CAAC,CAAC;MAEF,MAAMC,QAAQ,GAAGb,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;MACvCI,OAAO,CAACC,GAAG,CAAC,gBAAgBS,QAAQ,IAAI,EAAEN,IAAI,CAACO,IAAI,CAAC;MAEpD,IAAIP,IAAI,CAACO,IAAI,IAAIP,IAAI,CAACO,IAAI,CAACC,OAAO,EAAE;QAClCrD,aAAa,CAAC,GAAGL,WAAW,iBAAiBkD,IAAI,CAACO,IAAI,CAACE,KAAK,SAASH,QAAQ,KAAK,CAAC;MACrF,CAAC,MAAM;QAAA,IAAAI,UAAA;QACLvD,aAAa,CAAC,aAAa,IAAI,EAAAuD,UAAA,GAAAV,IAAI,CAACO,IAAI,cAAAG,UAAA,uBAATA,UAAA,CAAWlC,OAAO,KAAI,MAAM,CAAC,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOmC,GAAG,EAAE;MACZ,MAAML,QAAQ,GAAGb,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;MACvCI,OAAO,CAACrB,KAAK,CAAC,WAAW,EAAEoC,GAAG,CAAC;MAE/B,IAAIC,YAAY,GAAG,aAAa;MAChC,IAAID,GAAG,CAACE,IAAI,KAAK,cAAc,IAAIF,GAAG,CAACnC,OAAO,CAACsC,QAAQ,CAAC,SAAS,CAAC,EAAE;QAClEF,YAAY,IAAI,SAASN,QAAQ,iBAAiB;MACpD,CAAC,MAAM,IAAIK,GAAG,CAACI,QAAQ,EAAE;QAAA,IAAAC,kBAAA;QACvB;QACAJ,YAAY,IAAI,EAAAI,kBAAA,GAAAL,GAAG,CAACI,QAAQ,CAACR,IAAI,cAAAS,kBAAA,uBAAjBA,kBAAA,CAAmBxC,OAAO,KAAI,UAAUmC,GAAG,CAACI,QAAQ,CAACE,MAAM,GAAG;MAChF,CAAC,MAAM,IAAIN,GAAG,CAACO,OAAO,EAAE;QACtB;QACAN,YAAY,IAAI,iBAAiB;MACnC,CAAC,MAAM;QACL;QACAA,YAAY,IAAID,GAAG,CAACnC,OAAO,IAAI,MAAM;MACvC;MAEArB,aAAa,CAACyD,YAAY,CAAC;IAC7B,CAAC,SAAS;MACR3D,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,oBACEpB,OAAA;IAAKsF,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,KAAK,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBACzD1F,OAAA;MAAKsF,KAAK,EAAE;QAAEK,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAF,QAAA,EAClDlF,QAAQ,GACPF,MAAM,CAACS,OAAO,gBACZf,OAAA;QAAKsF,KAAK,EAAE;UAAEE,KAAK,EAAE,GAAG;UAAEK,MAAM,EAAE,GAAG;UAAEC,UAAU,EAAE,MAAM;UAAEC,MAAM,EAAE,QAAQ;UAAEC,YAAY,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAE;QAAAP,QAAA,eAGjH1F,OAAA,CAACF,WAAW;UAACuE,QAAQ,EAAE/D,MAAM,CAACU,EAAG;UAACD,OAAO,EAAET,MAAM,CAACS;QAAQ;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,gBAENrG,OAAA;QAAKsF,KAAK,EAAE;UAAEE,KAAK,EAAE,GAAG;UAAEK,MAAM,EAAE,GAAG;UAAEC,UAAU,EAAE,MAAM;UAAEQ,KAAK,EAAE,MAAM;UAAEC,UAAU,EAAE,OAAO;UAAEP,YAAY,EAAE,CAAC;UAAED,MAAM,EAAE;QAAS,CAAE;QAAAL,QAAA,EAAC;MAAI;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAC9I,gBAEDrG,OAAA;QAAKwG,GAAG,EAAE7F,OAAQ;QAAC8F,GAAG,EAAEnG,MAAM,CAACG,IAAK;QAAC6E,KAAK,EAAE;UAAEE,KAAK,EAAE,GAAG;UAAEK,MAAM,EAAE,GAAG;UAAEa,SAAS,EAAE,SAAS;UAAEZ,UAAU,EAAE,MAAM;UAAEE,YAAY,EAAE,CAAC;UAAEW,SAAS,EAAE;QAA4B;MAAE;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC9K;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACNrG,OAAA;MAAKsF,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEC,UAAU,EAAE,MAAM;QAAElB,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,EAAEpF,MAAM,CAACkC;IAAI;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACtFrG,OAAA;MAAKsF,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEN,KAAK,EAAE,MAAM;QAAEX,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,oBAAG,EAACpF,MAAM,CAACG,IAAI;IAAA;MAAAyF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACpFrG,OAAA;MAAKsF,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEN,KAAK,EAAE,MAAM;QAAEX,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,oBAAG,EAACpF,MAAM,CAACwG,QAAQ,EAAC,GAAC,EAACxG,MAAM,CAACyG,QAAQ,GAAG,IAAIzG,MAAM,CAACyG,QAAQ,GAAG,GAAG,EAAE;IAAA;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACxIrG,OAAA;MAAKsF,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEN,KAAK,EAAE,MAAM;QAAEX,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,oBAAG,EAACpF,MAAM,CAAC8E,MAAM,KAAK,QAAQ,gBAAGpF,OAAA;QAAMsF,KAAK,EAAE;UAAEgB,KAAK,EAAE;QAAU,CAAE;QAAAZ,QAAA,EAAC;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBAAGrG,OAAA;QAAMsF,KAAK,EAAE;UAAEgB,KAAK,EAAE;QAAU,CAAE;QAAAZ,QAAA,EAAC;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAChM/F,MAAM,CAAC0G,SAAS,iBAAIhH,OAAA;MAAKsF,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEN,KAAK,EAAE,MAAM;QAAEX,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,UAAG,EAACpF,MAAM,CAAC0G,SAAS;IAAA;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAC7G/F,MAAM,CAAC2G,YAAY,iBAAIjH,OAAA;MAAKsF,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEN,KAAK,EAAE,MAAM;QAAEX,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,oBAAG,EAACpF,MAAM,CAAC2G,YAAY;IAAA;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACnH/F,MAAM,CAAC4G,KAAK,iBAAIlH,OAAA;MAAKsF,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEN,KAAK,EAAE,MAAM;QAAEX,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,oBAAG,EAACpF,MAAM,CAAC4G,KAAK;IAAA;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACrG/F,MAAM,CAAC6G,WAAW,iBAAInH,OAAA;MAAKsF,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEN,KAAK,EAAE,MAAM;QAAEX,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,oBAAG,EAACpF,MAAM,CAAC6G,WAAW;IAAA;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAEjH3F,KAAK,iBACJV,OAAA;MAAKsF,KAAK,EAAE;QACV8B,SAAS,EAAE,EAAE;QACb7B,OAAO,EAAE,EAAE;QACXO,UAAU,EAAE,MAAM;QAClBE,YAAY,EAAE,EAAE;QAChBW,SAAS,EAAE,4BAA4B;QACvCU,MAAM,EAAE;MACV,CAAE;MAAA3B,QAAA,gBAEA1F,OAAA;QAAKsF,KAAK,EAAE;UACVuB,UAAU,EAAE,MAAM;UAClBD,QAAQ,EAAE,EAAE;UACZjB,YAAY,EAAE,CAAC;UACfW,KAAK,EAAE,SAAS;UAChBgB,aAAa,EAAE;QACjB,CAAE;QAAA5B,QAAA,EAAC;MAAI;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAGbrG,OAAA;QAAKsF,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAG,CAAE;QAAAD,QAAA,gBAC/B1F,OAAA;UAAMsF,KAAK,EAAE;YAAEsB,QAAQ,EAAE,EAAE;YAAEN,KAAK,EAAE,MAAM;YAAEiB,WAAW,EAAE;UAAE,CAAE;UAAA7B,QAAA,EAAC;QAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1ErG,OAAA;UACEwH,KAAK,EAAEvG,WAAY;UACnBwG,QAAQ,EAAG7F,CAAC,IAAK;YACfV,cAAc,CAACU,CAAC,CAACE,MAAM,CAAC0F,KAAK,CAAC;YAC9BlG,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;YACnBI,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;YACpBF,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;UAC7B,CAAE;UACF8D,KAAK,EAAE;YACLE,KAAK,EAAE,MAAM;YACbD,OAAO,EAAE,SAAS;YAClBS,YAAY,EAAE,CAAC;YACfqB,MAAM,EAAE,mBAAmB;YAC3BT,QAAQ,EAAE,EAAE;YACZN,KAAK,EAAE,MAAM;YACboB,eAAe,EAAE;UACnB,CAAE;UAAAhC,QAAA,gBAEF1F,OAAA;YAAQwH,KAAK,EAAC,KAAK;YAAA9B,QAAA,EAAC;UAAa;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CrG,OAAA;YAAQwH,KAAK,EAAC,KAAK;YAAA9B,QAAA,EAAC;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNrG,OAAA;QAAKsF,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAG,CAAE;QAAAD,QAAA,gBAC/B1F,OAAA;UAAMsF,KAAK,EAAE;YAAEsB,QAAQ,EAAE,EAAE;YAAEN,KAAK,EAAE,MAAM;YAAEX,YAAY,EAAE,CAAC;YAAEgC,OAAO,EAAE;UAAQ,CAAE;UAAAjC,QAAA,GAAC,cAC7E,EAACzE,WAAW,EAAC,gCACjB;QAAA;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPrG,OAAA;UACES,IAAI,EAAC,MAAM;UACXgH,QAAQ,EAAE9F,oBAAqB;UAC/BiG,MAAM,EAAC,sBAAsB;UAC7BtC,KAAK,EAAE;YACLE,KAAK,EAAE,MAAM;YACbc,KAAK,EAAE,MAAM;YACbM,QAAQ,EAAE,EAAE;YACZrB,OAAO,EAAE,SAAS;YAClBS,YAAY,EAAE,CAAC;YACfqB,MAAM,EAAE,mBAAmB;YAC3BK,eAAe,EAAE;UACnB;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACD9E,gBAAgB,iBACfvB,OAAA;UAAKsF,KAAK,EAAE;YAAEsB,QAAQ,EAAE,EAAE;YAAEN,KAAK,EAAE,MAAM;YAAEc,SAAS,EAAE;UAAE,CAAE;UAAA1B,QAAA,GAAC,sBACpD,EAACnE,gBAAgB,CAACiB,IAAI;QAAA;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL5E,WAAW,iBACVzB,OAAA;QAAKsF,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAG,CAAE;QAAAD,QAAA,gBAC/B1F,OAAA;UAAMsF,KAAK,EAAE;YAAEsB,QAAQ,EAAE,EAAE;YAAEN,KAAK,EAAE,MAAM;YAAEX,YAAY,EAAE,CAAC;YAAEgC,OAAO,EAAE;UAAQ,CAAE;UAAAjC,QAAA,EAAC;QAEjF;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPrG,OAAA;UAAKsF,KAAK,EAAE;YACVsB,QAAQ,EAAE,EAAE;YACZN,KAAK,EAAE,MAAM;YACboB,eAAe,EAAE,SAAS;YAC1BnC,OAAO,EAAE,CAAC;YACVS,YAAY,EAAE,CAAC;YACfqB,MAAM,EAAE,mBAAmB;YAC3BQ,UAAU,EAAE,WAAW;YACvBC,SAAS,EAAE,WAAW;YACtBC,SAAS,EAAE,EAAE;YACb9B,QAAQ,EAAE;UACZ,CAAE;UAAAP,QAAA,GACCjE,WAAW,CAACuG,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAEvG,WAAW,CAACgB,MAAM,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE;QAAA;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eACNrG,OAAA;UAAKsF,KAAK,EAAE;YAAEsB,QAAQ,EAAE,EAAE;YAAEN,KAAK,EAAE,MAAM;YAAEc,SAAS,EAAE;UAAE,CAAE;UAAA1B,QAAA,GAAC,sBACpD,EAACjE,WAAW,CAACgB,MAAM,EAAC,eAC3B;QAAA;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDrG,OAAA;QACEiI,OAAO,EAAExE,cAAe;QACxByE,QAAQ,EAAE/G,WAAW,IAAI,CAACb,MAAM,CAACoD,GAAG,IAAI,CAACjC,WAAY;QACrD6D,KAAK,EAAE;UACLE,KAAK,EAAE,MAAM;UACbD,OAAO,EAAE,CAAC;UACVO,UAAU,EAAGxF,MAAM,CAACoD,GAAG,IAAIjC,WAAW,GAAI,SAAS,GAAG,SAAS;UAC/D6E,KAAK,EAAE,MAAM;UACbe,MAAM,EAAE,MAAM;UACdrB,YAAY,EAAE,CAAC;UACfmC,MAAM,EAAGhH,WAAW,IAAI,CAACb,MAAM,CAACoD,GAAG,IAAI,CAACjC,WAAW,GAAI,aAAa,GAAG,SAAS;UAChFoF,UAAU,EAAE,MAAM;UAClBD,QAAQ,EAAE,EAAE;UACZU,aAAa,EAAE;QACjB,CAAE;QAAA5B,QAAA,EAEDvE,WAAW,GAAG,SAAS,GAAG,KAAKF,WAAW;MAAI;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,EAGRhF,UAAU,iBACTrB,OAAA;QAAKsF,KAAK,EAAE;UACV8B,SAAS,EAAE,EAAE;UACbd,KAAK,EAAEjF,UAAU,CAAC4D,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;UACxD2B,QAAQ,EAAE,EAAE;UACZkB,SAAS,EAAE;QACb,CAAE;QAAApC,QAAA,EACCrE;MAAU;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACN,eAGDrG,OAAA;QAAKsF,KAAK,EAAE;UAAEsB,QAAQ,EAAE,EAAE;UAAEN,KAAK,EAAE,MAAM;UAAEc,SAAS,EAAE;QAAE,CAAE;QAAA1B,QAAA,GAAC,uBACnD,EAACpF,MAAM,CAACoD,GAAG,IAAI,KAAK;MAAA;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9F,EAAA,CA7RIF,oBAAoB;AAAA+H,EAAA,GAApB/H,oBAAoB;AA+R1B,eAAeA,oBAAoB;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}