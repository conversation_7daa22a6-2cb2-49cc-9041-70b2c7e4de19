{"ast": null, "code": "const extendsObject = function () {\n  const result = Object.assign({}, arguments.length <= 0 ? undefined : arguments[0]);\n  for (let i = 1; i < arguments.length; i++) {\n    const obj = i < 0 || arguments.length <= i ? undefined : arguments[i];\n    if (obj) {\n      Object.keys(obj).forEach(key => {\n        const val = obj[key];\n        if (val !== undefined) {\n          result[key] = val;\n        }\n      });\n    }\n  }\n  return result;\n};\nexport default extendsObject;", "map": {"version": 3, "names": ["extendsObject", "result", "Object", "assign", "arguments", "length", "undefined", "i", "obj", "keys", "for<PERSON>ach", "key", "val"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/_util/extendsObject.js"], "sourcesContent": ["const extendsObject = function () {\n  const result = Object.assign({}, arguments.length <= 0 ? undefined : arguments[0]);\n  for (let i = 1; i < arguments.length; i++) {\n    const obj = i < 0 || arguments.length <= i ? undefined : arguments[i];\n    if (obj) {\n      Object.keys(obj).forEach(key => {\n        const val = obj[key];\n        if (val !== undefined) {\n          result[key] = val;\n        }\n      });\n    }\n  }\n  return result;\n};\nexport default extendsObject;"], "mappings": "AAAA,MAAMA,aAAa,GAAG,SAAAA,CAAA,EAAY;EAChC,MAAMC,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEC,SAAS,CAACC,MAAM,IAAI,CAAC,GAAGC,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,CAAC;EAClF,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;IACzC,MAAMC,GAAG,GAAGD,CAAC,GAAG,CAAC,IAAIH,SAAS,CAACC,MAAM,IAAIE,CAAC,GAAGD,SAAS,GAAGF,SAAS,CAACG,CAAC,CAAC;IACrE,IAAIC,GAAG,EAAE;MACPN,MAAM,CAACO,IAAI,CAACD,GAAG,CAAC,CAACE,OAAO,CAACC,GAAG,IAAI;QAC9B,MAAMC,GAAG,<PERSON>G<PERSON>,<PERSON>G,CAACG,<PERSON>G,CAAC;QACpB,IAAIC,GAAG,KAAKN,SAAS,EAAE;UACrBL,MAAM,CAACU,GAAG,CAAC,GAAGC,GAAG;QACnB;MACF,CAAC,CAAC;IACJ;EACF;EACA,OAAOX,MAAM;AACf,CAAC;AACD,eAAeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}