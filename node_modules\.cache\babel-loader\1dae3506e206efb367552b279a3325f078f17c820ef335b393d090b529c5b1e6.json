{"ast": null, "code": "/*\n  references:\n    Formules et constantes pour le Calcul pour la\n    projection cylindrique conforme à axe oblique et pour la transformation entre\n    des systèmes de référence.\n    http://www.swisstopo.admin.ch/internet/swisstopo/fr/home/<USER>/survey/sys/refsys/switzerland.parsysrelated1.31216.downloadList.77004.DownloadFile.tmp/swissprojectionfr.pdf\n  */\n\nexport function init() {\n  var phy0 = this.lat0;\n  this.lambda0 = this.long0;\n  var sinPhy0 = Math.sin(phy0);\n  var semiMajorAxis = this.a;\n  var invF = this.rf;\n  var flattening = 1 / invF;\n  var e2 = 2 * flattening - Math.pow(flattening, 2);\n  var e = this.e = Math.sqrt(e2);\n  this.R = this.k0 * semiMajorAxis * Math.sqrt(1 - e2) / (1 - e2 * Math.pow(sinPhy0, 2));\n  this.alpha = Math.sqrt(1 + e2 / (1 - e2) * Math.pow(Math.cos(phy0), 4));\n  this.b0 = Math.asin(sinPhy0 / this.alpha);\n  var k1 = Math.log(Math.tan(Math.PI / 4 + this.b0 / 2));\n  var k2 = Math.log(Math.tan(Math.PI / 4 + phy0 / 2));\n  var k3 = Math.log((1 + e * sinPhy0) / (1 - e * sinPhy0));\n  this.K = k1 - this.alpha * k2 + this.alpha * e / 2 * k3;\n}\nexport function forward(p) {\n  var Sa1 = Math.log(Math.tan(Math.PI / 4 - p.y / 2));\n  var Sa2 = this.e / 2 * Math.log((1 + this.e * Math.sin(p.y)) / (1 - this.e * Math.sin(p.y)));\n  var S = -this.alpha * (Sa1 + Sa2) + this.K;\n\n  // spheric latitude\n  var b = 2 * (Math.atan(Math.exp(S)) - Math.PI / 4);\n\n  // spheric longitude\n  var I = this.alpha * (p.x - this.lambda0);\n\n  // psoeudo equatorial rotation\n  var rotI = Math.atan(Math.sin(I) / (Math.sin(this.b0) * Math.tan(b) + Math.cos(this.b0) * Math.cos(I)));\n  var rotB = Math.asin(Math.cos(this.b0) * Math.sin(b) - Math.sin(this.b0) * Math.cos(b) * Math.cos(I));\n  p.y = this.R / 2 * Math.log((1 + Math.sin(rotB)) / (1 - Math.sin(rotB))) + this.y0;\n  p.x = this.R * rotI + this.x0;\n  return p;\n}\nexport function inverse(p) {\n  var Y = p.x - this.x0;\n  var X = p.y - this.y0;\n  var rotI = Y / this.R;\n  var rotB = 2 * (Math.atan(Math.exp(X / this.R)) - Math.PI / 4);\n  var b = Math.asin(Math.cos(this.b0) * Math.sin(rotB) + Math.sin(this.b0) * Math.cos(rotB) * Math.cos(rotI));\n  var I = Math.atan(Math.sin(rotI) / (Math.cos(this.b0) * Math.cos(rotI) - Math.sin(this.b0) * Math.tan(rotB)));\n  var lambda = this.lambda0 + I / this.alpha;\n  var S = 0;\n  var phy = b;\n  var prevPhy = -1000;\n  var iteration = 0;\n  while (Math.abs(phy - prevPhy) > 0.0000001) {\n    if (++iteration > 20) {\n      //...reportError(\"omercFwdInfinity\");\n      return;\n    }\n    //S = Math.log(Math.tan(Math.PI / 4 + phy / 2));\n    S = 1 / this.alpha * (Math.log(Math.tan(Math.PI / 4 + b / 2)) - this.K) + this.e * Math.log(Math.tan(Math.PI / 4 + Math.asin(this.e * Math.sin(phy)) / 2));\n    prevPhy = phy;\n    phy = 2 * Math.atan(Math.exp(S)) - Math.PI / 2;\n  }\n  p.x = lambda;\n  p.y = phy;\n  return p;\n}\nexport var names = [\"somerc\"];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};", "map": {"version": 3, "names": ["init", "phy0", "lat0", "lambda0", "long0", "sinPhy0", "Math", "sin", "semiMajorAxis", "a", "invF", "rf", "flattening", "e2", "pow", "e", "sqrt", "R", "k0", "alpha", "cos", "b0", "asin", "k1", "log", "tan", "PI", "k2", "k3", "K", "forward", "p", "Sa1", "y", "Sa2", "S", "b", "atan", "exp", "I", "x", "rotI", "rotB", "y0", "x0", "inverse", "Y", "X", "lambda", "phy", "prevPhy", "iteration", "abs", "names"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/proj4/lib/projections/somerc.js"], "sourcesContent": ["/*\n  references:\n    Formules et constantes pour le Calcul pour la\n    projection cylindrique conforme à axe oblique et pour la transformation entre\n    des systèmes de référence.\n    http://www.swisstopo.admin.ch/internet/swisstopo/fr/home/<USER>/survey/sys/refsys/switzerland.parsysrelated1.31216.downloadList.77004.DownloadFile.tmp/swissprojectionfr.pdf\n  */\n\nexport function init() {\n  var phy0 = this.lat0;\n  this.lambda0 = this.long0;\n  var sinPhy0 = Math.sin(phy0);\n  var semiMajorAxis = this.a;\n  var invF = this.rf;\n  var flattening = 1 / invF;\n  var e2 = 2 * flattening - Math.pow(flattening, 2);\n  var e = this.e = Math.sqrt(e2);\n  this.R = this.k0 * semiMajorAxis * Math.sqrt(1 - e2) / (1 - e2 * Math.pow(sinPhy0, 2));\n  this.alpha = Math.sqrt(1 + e2 / (1 - e2) * Math.pow(Math.cos(phy0), 4));\n  this.b0 = Math.asin(sinPhy0 / this.alpha);\n  var k1 = Math.log(Math.tan(Math.PI / 4 + this.b0 / 2));\n  var k2 = Math.log(Math.tan(Math.PI / 4 + phy0 / 2));\n  var k3 = Math.log((1 + e * sinPhy0) / (1 - e * sinPhy0));\n  this.K = k1 - this.alpha * k2 + this.alpha * e / 2 * k3;\n}\n\nexport function forward(p) {\n  var Sa1 = Math.log(Math.tan(Math.PI / 4 - p.y / 2));\n  var Sa2 = this.e / 2 * Math.log((1 + this.e * Math.sin(p.y)) / (1 - this.e * Math.sin(p.y)));\n  var S = -this.alpha * (Sa1 + Sa2) + this.K;\n\n  // spheric latitude\n  var b = 2 * (Math.atan(Math.exp(S)) - Math.PI / 4);\n\n  // spheric longitude\n  var I = this.alpha * (p.x - this.lambda0);\n\n  // psoeudo equatorial rotation\n  var rotI = Math.atan(Math.sin(I) / (Math.sin(this.b0) * Math.tan(b) + Math.cos(this.b0) * Math.cos(I)));\n\n  var rotB = Math.asin(Math.cos(this.b0) * Math.sin(b) - Math.sin(this.b0) * Math.cos(b) * Math.cos(I));\n\n  p.y = this.R / 2 * Math.log((1 + Math.sin(rotB)) / (1 - Math.sin(rotB))) + this.y0;\n  p.x = this.R * rotI + this.x0;\n  return p;\n}\n\nexport function inverse(p) {\n  var Y = p.x - this.x0;\n  var X = p.y - this.y0;\n\n  var rotI = Y / this.R;\n  var rotB = 2 * (Math.atan(Math.exp(X / this.R)) - Math.PI / 4);\n\n  var b = Math.asin(Math.cos(this.b0) * Math.sin(rotB) + Math.sin(this.b0) * Math.cos(rotB) * Math.cos(rotI));\n  var I = Math.atan(Math.sin(rotI) / (Math.cos(this.b0) * Math.cos(rotI) - Math.sin(this.b0) * Math.tan(rotB)));\n\n  var lambda = this.lambda0 + I / this.alpha;\n\n  var S = 0;\n  var phy = b;\n  var prevPhy = -1000;\n  var iteration = 0;\n  while (Math.abs(phy - prevPhy) > 0.0000001) {\n    if (++iteration > 20) {\n      //...reportError(\"omercFwdInfinity\");\n      return;\n    }\n    //S = Math.log(Math.tan(Math.PI / 4 + phy / 2));\n    S = 1 / this.alpha * (Math.log(Math.tan(Math.PI / 4 + b / 2)) - this.K) + this.e * Math.log(Math.tan(Math.PI / 4 + Math.asin(this.e * Math.sin(phy)) / 2));\n    prevPhy = phy;\n    phy = 2 * Math.atan(Math.exp(S)) - Math.PI / 2;\n  }\n\n  p.x = lambda;\n  p.y = phy;\n  return p;\n}\n\nexport var names = [\"somerc\"];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASA,IAAIA,CAAA,EAAG;EACrB,IAAIC,IAAI,GAAG,IAAI,CAACC,IAAI;EACpB,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,KAAK;EACzB,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAACN,IAAI,CAAC;EAC5B,IAAIO,aAAa,GAAG,IAAI,CAACC,CAAC;EAC1B,IAAIC,IAAI,GAAG,IAAI,CAACC,EAAE;EAClB,IAAIC,UAAU,GAAG,CAAC,GAAGF,IAAI;EACzB,IAAIG,EAAE,GAAG,CAAC,GAAGD,UAAU,GAAGN,IAAI,CAACQ,GAAG,CAACF,UAAU,EAAE,CAAC,CAAC;EACjD,IAAIG,CAAC,GAAG,IAAI,CAACA,CAAC,GAAGT,IAAI,CAACU,IAAI,CAACH,EAAE,CAAC;EAC9B,IAAI,CAACI,CAAC,GAAG,IAAI,CAACC,EAAE,GAAGV,aAAa,GAAGF,IAAI,CAACU,IAAI,CAAC,CAAC,GAAGH,EAAE,CAAC,IAAI,CAAC,GAAGA,EAAE,GAAGP,IAAI,CAACQ,GAAG,CAACT,OAAO,EAAE,CAAC,CAAC,CAAC;EACtF,IAAI,CAACc,KAAK,GAAGb,IAAI,CAACU,IAAI,CAAC,CAAC,GAAGH,EAAE,IAAI,CAAC,GAAGA,EAAE,CAAC,GAAGP,IAAI,CAACQ,GAAG,CAACR,IAAI,CAACc,GAAG,CAACnB,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;EACvE,IAAI,CAACoB,EAAE,GAAGf,IAAI,CAACgB,IAAI,CAACjB,OAAO,GAAG,IAAI,CAACc,KAAK,CAAC;EACzC,IAAII,EAAE,GAAGjB,IAAI,CAACkB,GAAG,CAAClB,IAAI,CAACmB,GAAG,CAACnB,IAAI,CAACoB,EAAE,GAAG,CAAC,GAAG,IAAI,CAACL,EAAE,GAAG,CAAC,CAAC,CAAC;EACtD,IAAIM,EAAE,GAAGrB,IAAI,CAACkB,GAAG,CAAClB,IAAI,CAACmB,GAAG,CAACnB,IAAI,CAACoB,EAAE,GAAG,CAAC,GAAGzB,IAAI,GAAG,CAAC,CAAC,CAAC;EACnD,IAAI2B,EAAE,GAAGtB,IAAI,CAACkB,GAAG,CAAC,CAAC,CAAC,GAAGT,CAAC,GAAGV,OAAO,KAAK,CAAC,GAAGU,CAAC,GAAGV,OAAO,CAAC,CAAC;EACxD,IAAI,CAACwB,CAAC,GAAGN,EAAE,GAAG,IAAI,CAACJ,KAAK,GAAGQ,EAAE,GAAG,IAAI,CAACR,KAAK,GAAGJ,CAAC,GAAG,CAAC,GAAGa,EAAE;AACzD;AAEA,OAAO,SAASE,OAAOA,CAACC,CAAC,EAAE;EACzB,IAAIC,GAAG,GAAG1B,IAAI,CAACkB,GAAG,CAAClB,IAAI,CAACmB,GAAG,CAACnB,IAAI,CAACoB,EAAE,GAAG,CAAC,GAAGK,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,CAAC;EACnD,IAAIC,GAAG,GAAG,IAAI,CAACnB,CAAC,GAAG,CAAC,GAAGT,IAAI,CAACkB,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAACT,CAAC,GAAGT,IAAI,CAACC,GAAG,CAACwB,CAAC,CAACE,CAAC,CAAC,KAAK,CAAC,GAAG,IAAI,CAAClB,CAAC,GAAGT,IAAI,CAACC,GAAG,CAACwB,CAAC,CAACE,CAAC,CAAC,CAAC,CAAC;EAC5F,IAAIE,CAAC,GAAG,CAAC,IAAI,CAAChB,KAAK,IAAIa,GAAG,GAAGE,GAAG,CAAC,GAAG,IAAI,CAACL,CAAC;;EAE1C;EACA,IAAIO,CAAC,GAAG,CAAC,IAAI9B,IAAI,CAAC+B,IAAI,CAAC/B,IAAI,CAACgC,GAAG,CAACH,CAAC,CAAC,CAAC,GAAG7B,IAAI,CAACoB,EAAE,GAAG,CAAC,CAAC;;EAElD;EACA,IAAIa,CAAC,GAAG,IAAI,CAACpB,KAAK,IAAIY,CAAC,CAACS,CAAC,GAAG,IAAI,CAACrC,OAAO,CAAC;;EAEzC;EACA,IAAIsC,IAAI,GAAGnC,IAAI,CAAC+B,IAAI,CAAC/B,IAAI,CAACC,GAAG,CAACgC,CAAC,CAAC,IAAIjC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACc,EAAE,CAAC,GAAGf,IAAI,CAACmB,GAAG,CAACW,CAAC,CAAC,GAAG9B,IAAI,CAACc,GAAG,CAAC,IAAI,CAACC,EAAE,CAAC,GAAGf,IAAI,CAACc,GAAG,CAACmB,CAAC,CAAC,CAAC,CAAC;EAEvG,IAAIG,IAAI,GAAGpC,IAAI,CAACgB,IAAI,CAAChB,IAAI,CAACc,GAAG,CAAC,IAAI,CAACC,EAAE,CAAC,GAAGf,IAAI,CAACC,GAAG,CAAC6B,CAAC,CAAC,GAAG9B,IAAI,CAACC,GAAG,CAAC,IAAI,CAACc,EAAE,CAAC,GAAGf,IAAI,CAACc,GAAG,CAACgB,CAAC,CAAC,GAAG9B,IAAI,CAACc,GAAG,CAACmB,CAAC,CAAC,CAAC;EAErGR,CAAC,CAACE,CAAC,GAAG,IAAI,CAAChB,CAAC,GAAG,CAAC,GAAGX,IAAI,CAACkB,GAAG,CAAC,CAAC,CAAC,GAAGlB,IAAI,CAACC,GAAG,CAACmC,IAAI,CAAC,KAAK,CAAC,GAAGpC,IAAI,CAACC,GAAG,CAACmC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAACC,EAAE;EAClFZ,CAAC,CAACS,CAAC,GAAG,IAAI,CAACvB,CAAC,GAAGwB,IAAI,GAAG,IAAI,CAACG,EAAE;EAC7B,OAAOb,CAAC;AACV;AAEA,OAAO,SAASc,OAAOA,CAACd,CAAC,EAAE;EACzB,IAAIe,CAAC,GAAGf,CAAC,CAACS,CAAC,GAAG,IAAI,CAACI,EAAE;EACrB,IAAIG,CAAC,GAAGhB,CAAC,CAACE,CAAC,GAAG,IAAI,CAACU,EAAE;EAErB,IAAIF,IAAI,GAAGK,CAAC,GAAG,IAAI,CAAC7B,CAAC;EACrB,IAAIyB,IAAI,GAAG,CAAC,IAAIpC,IAAI,CAAC+B,IAAI,CAAC/B,IAAI,CAACgC,GAAG,CAACS,CAAC,GAAG,IAAI,CAAC9B,CAAC,CAAC,CAAC,GAAGX,IAAI,CAACoB,EAAE,GAAG,CAAC,CAAC;EAE9D,IAAIU,CAAC,GAAG9B,IAAI,CAACgB,IAAI,CAAChB,IAAI,CAACc,GAAG,CAAC,IAAI,CAACC,EAAE,CAAC,GAAGf,IAAI,CAACC,GAAG,CAACmC,IAAI,CAAC,GAAGpC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACc,EAAE,CAAC,GAAGf,IAAI,CAACc,GAAG,CAACsB,IAAI,CAAC,GAAGpC,IAAI,CAACc,GAAG,CAACqB,IAAI,CAAC,CAAC;EAC3G,IAAIF,CAAC,GAAGjC,IAAI,CAAC+B,IAAI,CAAC/B,IAAI,CAACC,GAAG,CAACkC,IAAI,CAAC,IAAInC,IAAI,CAACc,GAAG,CAAC,IAAI,CAACC,EAAE,CAAC,GAAGf,IAAI,CAACc,GAAG,CAACqB,IAAI,CAAC,GAAGnC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACc,EAAE,CAAC,GAAGf,IAAI,CAACmB,GAAG,CAACiB,IAAI,CAAC,CAAC,CAAC;EAE7G,IAAIM,MAAM,GAAG,IAAI,CAAC7C,OAAO,GAAGoC,CAAC,GAAG,IAAI,CAACpB,KAAK;EAE1C,IAAIgB,CAAC,GAAG,CAAC;EACT,IAAIc,GAAG,GAAGb,CAAC;EACX,IAAIc,OAAO,GAAG,CAAC,IAAI;EACnB,IAAIC,SAAS,GAAG,CAAC;EACjB,OAAO7C,IAAI,CAAC8C,GAAG,CAACH,GAAG,GAAGC,OAAO,CAAC,GAAG,SAAS,EAAE;IAC1C,IAAI,EAAEC,SAAS,GAAG,EAAE,EAAE;MACpB;MACA;IACF;IACA;IACAhB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAChB,KAAK,IAAIb,IAAI,CAACkB,GAAG,CAAClB,IAAI,CAACmB,GAAG,CAACnB,IAAI,CAACoB,EAAE,GAAG,CAAC,GAAGU,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAACP,CAAC,CAAC,GAAG,IAAI,CAACd,CAAC,GAAGT,IAAI,CAACkB,GAAG,CAAClB,IAAI,CAACmB,GAAG,CAACnB,IAAI,CAACoB,EAAE,GAAG,CAAC,GAAGpB,IAAI,CAACgB,IAAI,CAAC,IAAI,CAACP,CAAC,GAAGT,IAAI,CAACC,GAAG,CAAC0C,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1JC,OAAO,GAAGD,GAAG;IACbA,GAAG,GAAG,CAAC,GAAG3C,IAAI,CAAC+B,IAAI,CAAC/B,IAAI,CAACgC,GAAG,CAACH,CAAC,CAAC,CAAC,GAAG7B,IAAI,CAACoB,EAAE,GAAG,CAAC;EAChD;EAEAK,CAAC,CAACS,CAAC,GAAGQ,MAAM;EACZjB,CAAC,CAACE,CAAC,GAAGgB,GAAG;EACT,OAAOlB,CAAC;AACV;AAEA,OAAO,IAAIsB,KAAK,GAAG,CAAC,QAAQ,CAAC;AAC7B,eAAe;EACbrD,IAAI,EAAEA,IAAI;EACV8B,OAAO,EAAEA,OAAO;EAChBe,OAAO,EAAEA,OAAO;EAChBQ,KAAK,EAAEA;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}