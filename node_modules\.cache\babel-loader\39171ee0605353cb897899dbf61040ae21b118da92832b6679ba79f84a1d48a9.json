{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport { __extends } from \"tslib\";\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { bind, each, clone, trim, isString, isFunction, isArray, isObject, extend } from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport TooltipHTMLContent from './TooltipHTMLContent.js';\nimport TooltipRichContent from './TooltipRichContent.js';\nimport { convertToColorString, encodeHTML, formatTpl } from '../../util/format.js';\nimport { parsePercent } from '../../util/number.js';\nimport { Rect } from '../../util/graphic.js';\nimport findPointFromSeries from '../axisPointer/findPointFromSeries.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport Model from '../../model/Model.js';\nimport * as globalListener from '../axisPointer/globalListener.js';\nimport * as axisHelper from '../../coord/axisHelper.js';\nimport * as axisPointerViewHelper from '../axisPointer/viewHelper.js';\nimport { getTooltipRenderMode, preParseFinder, queryReferringComponents } from '../../util/model.js';\nimport ComponentView from '../../view/Component.js';\nimport { format as timeFormat } from '../../util/time.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { shouldTooltipConfine } from './helper.js';\nimport { normalizeTooltipFormatResult } from '../../model/mixin/dataFormat.js';\nimport { createTooltipMarkup, buildTooltipMarkup, TooltipMarkupStyleCreator } from './tooltipMarkup.js';\nimport { findEventDispatcher } from '../../util/event.js';\nimport { clear, createOrUpdate } from '../../util/throttle.js';\nvar proxyRect = new Rect({\n  shape: {\n    x: -1,\n    y: -1,\n    width: 2,\n    height: 2\n  }\n});\nvar TooltipView = /** @class */function (_super) {\n  __extends(TooltipView, _super);\n  function TooltipView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TooltipView.type;\n    return _this;\n  }\n  TooltipView.prototype.init = function (ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    }\n    var tooltipModel = ecModel.getComponent('tooltip');\n    var renderMode = this._renderMode = getTooltipRenderMode(tooltipModel.get('renderMode'));\n    this._tooltipContent = renderMode === 'richText' ? new TooltipRichContent(api) : new TooltipHTMLContent(api, {\n      appendTo: tooltipModel.get('appendToBody', true) ? 'body' : tooltipModel.get('appendTo', true)\n    });\n  };\n  TooltipView.prototype.render = function (tooltipModel, ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    }\n    // Reset\n    this.group.removeAll();\n    this._tooltipModel = tooltipModel;\n    this._ecModel = ecModel;\n    this._api = api;\n    var tooltipContent = this._tooltipContent;\n    tooltipContent.update(tooltipModel);\n    tooltipContent.setEnterable(tooltipModel.get('enterable'));\n    this._initGlobalListener();\n    this._keepShow();\n    // PENDING\n    // `mousemove` event will be triggered very frequently when the mouse moves fast,\n    // which causes that the `updatePosition` function was also called frequently.\n    // In Chrome with devtools open and Firefox, tooltip looks laggy and shakes. See #14695 #16101\n    // To avoid frequent triggering,\n    // consider throttling it in 50ms when transition is enabled\n    if (this._renderMode !== 'richText' && tooltipModel.get('transitionDuration')) {\n      createOrUpdate(this, '_updatePosition', 50, 'fixRate');\n    } else {\n      clear(this, '_updatePosition');\n    }\n  };\n  TooltipView.prototype._initGlobalListener = function () {\n    var tooltipModel = this._tooltipModel;\n    var triggerOn = tooltipModel.get('triggerOn');\n    globalListener.register('itemTooltip', this._api, bind(function (currTrigger, e, dispatchAction) {\n      // If 'none', it is not controlled by mouse totally.\n      if (triggerOn !== 'none') {\n        if (triggerOn.indexOf(currTrigger) >= 0) {\n          this._tryShow(e, dispatchAction);\n        } else if (currTrigger === 'leave') {\n          this._hide(dispatchAction);\n        }\n      }\n    }, this));\n  };\n  TooltipView.prototype._keepShow = function () {\n    var tooltipModel = this._tooltipModel;\n    var ecModel = this._ecModel;\n    var api = this._api;\n    var triggerOn = tooltipModel.get('triggerOn');\n    // Try to keep the tooltip show when refreshing\n    if (this._lastX != null && this._lastY != null\n    // When user is willing to control tooltip totally using API,\n    // self.manuallyShowTip({x, y}) might cause tooltip hide,\n    // which is not expected.\n    && triggerOn !== 'none' && triggerOn !== 'click') {\n      var self_1 = this;\n      clearTimeout(this._refreshUpdateTimeout);\n      this._refreshUpdateTimeout = setTimeout(function () {\n        // Show tip next tick after other charts are rendered\n        // In case highlight action has wrong result\n        // FIXME\n        !api.isDisposed() && self_1.manuallyShowTip(tooltipModel, ecModel, api, {\n          x: self_1._lastX,\n          y: self_1._lastY,\n          dataByCoordSys: self_1._lastDataByCoordSys\n        });\n      });\n    }\n  };\n  /**\r\n   * Show tip manually by\r\n   * dispatchAction({\r\n   *     type: 'showTip',\r\n   *     x: 10,\r\n   *     y: 10\r\n   * });\r\n   * Or\r\n   * dispatchAction({\r\n   *      type: 'showTip',\r\n   *      seriesIndex: 0,\r\n   *      dataIndex or dataIndexInside or name\r\n   * });\r\n   *\r\n   *  TODO Batch\r\n   */\n  TooltipView.prototype.manuallyShowTip = function (tooltipModel, ecModel, api, payload) {\n    if (payload.from === this.uid || env.node || !api.getDom()) {\n      return;\n    }\n    var dispatchAction = makeDispatchAction(payload, api);\n    // Reset ticket\n    this._ticket = '';\n    // When triggered from axisPointer.\n    var dataByCoordSys = payload.dataByCoordSys;\n    var cmptRef = findComponentReference(payload, ecModel, api);\n    if (cmptRef) {\n      var rect = cmptRef.el.getBoundingRect().clone();\n      rect.applyTransform(cmptRef.el.transform);\n      this._tryShow({\n        offsetX: rect.x + rect.width / 2,\n        offsetY: rect.y + rect.height / 2,\n        target: cmptRef.el,\n        position: payload.position,\n        // When manully trigger, the mouse is not on the el, so we'd better to\n        // position tooltip on the bottom of the el and display arrow is possible.\n        positionDefault: 'bottom'\n      }, dispatchAction);\n    } else if (payload.tooltip && payload.x != null && payload.y != null) {\n      var el = proxyRect;\n      el.x = payload.x;\n      el.y = payload.y;\n      el.update();\n      getECData(el).tooltipConfig = {\n        name: null,\n        option: payload.tooltip\n      };\n      // Manually show tooltip while view is not using zrender elements.\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        target: el\n      }, dispatchAction);\n    } else if (dataByCoordSys) {\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        position: payload.position,\n        dataByCoordSys: dataByCoordSys,\n        tooltipOption: payload.tooltipOption\n      }, dispatchAction);\n    } else if (payload.seriesIndex != null) {\n      if (this._manuallyAxisShowTip(tooltipModel, ecModel, api, payload)) {\n        return;\n      }\n      var pointInfo = findPointFromSeries(payload, ecModel);\n      var cx = pointInfo.point[0];\n      var cy = pointInfo.point[1];\n      if (cx != null && cy != null) {\n        this._tryShow({\n          offsetX: cx,\n          offsetY: cy,\n          target: pointInfo.el,\n          position: payload.position,\n          // When manully trigger, the mouse is not on the el, so we'd better to\n          // position tooltip on the bottom of the el and display arrow is possible.\n          positionDefault: 'bottom'\n        }, dispatchAction);\n      }\n    } else if (payload.x != null && payload.y != null) {\n      // FIXME\n      // should wrap dispatchAction like `axisPointer/globalListener` ?\n      api.dispatchAction({\n        type: 'updateAxisPointer',\n        x: payload.x,\n        y: payload.y\n      });\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        position: payload.position,\n        target: api.getZr().findHover(payload.x, payload.y).target\n      }, dispatchAction);\n    }\n  };\n  TooltipView.prototype.manuallyHideTip = function (tooltipModel, ecModel, api, payload) {\n    var tooltipContent = this._tooltipContent;\n    if (this._tooltipModel) {\n      tooltipContent.hideLater(this._tooltipModel.get('hideDelay'));\n    }\n    this._lastX = this._lastY = this._lastDataByCoordSys = null;\n    if (payload.from !== this.uid) {\n      this._hide(makeDispatchAction(payload, api));\n    }\n  };\n  // Be compatible with previous design, that is, when tooltip.type is 'axis' and\n  // dispatchAction 'showTip' with seriesIndex and dataIndex will trigger axis pointer\n  // and tooltip.\n  TooltipView.prototype._manuallyAxisShowTip = function (tooltipModel, ecModel, api, payload) {\n    var seriesIndex = payload.seriesIndex;\n    var dataIndex = payload.dataIndex;\n    // @ts-ignore\n    var coordSysAxesInfo = ecModel.getComponent('axisPointer').coordSysAxesInfo;\n    if (seriesIndex == null || dataIndex == null || coordSysAxesInfo == null) {\n      return;\n    }\n    var seriesModel = ecModel.getSeriesByIndex(seriesIndex);\n    if (!seriesModel) {\n      return;\n    }\n    var data = seriesModel.getData();\n    var tooltipCascadedModel = buildTooltipModel([data.getItemModel(dataIndex), seriesModel, (seriesModel.coordinateSystem || {}).model], this._tooltipModel);\n    if (tooltipCascadedModel.get('trigger') !== 'axis') {\n      return;\n    }\n    api.dispatchAction({\n      type: 'updateAxisPointer',\n      seriesIndex: seriesIndex,\n      dataIndex: dataIndex,\n      position: payload.position\n    });\n    return true;\n  };\n  TooltipView.prototype._tryShow = function (e, dispatchAction) {\n    var el = e.target;\n    var tooltipModel = this._tooltipModel;\n    if (!tooltipModel) {\n      return;\n    }\n    // Save mouse x, mouse y. So we can try to keep showing the tip if chart is refreshed\n    this._lastX = e.offsetX;\n    this._lastY = e.offsetY;\n    var dataByCoordSys = e.dataByCoordSys;\n    if (dataByCoordSys && dataByCoordSys.length) {\n      this._showAxisTooltip(dataByCoordSys, e);\n    } else if (el) {\n      var ecData = getECData(el);\n      if (ecData.ssrType === 'legend') {\n        // Don't trigger tooltip for legend tooltip item\n        return;\n      }\n      this._lastDataByCoordSys = null;\n      var seriesDispatcher_1;\n      var cmptDispatcher_1;\n      findEventDispatcher(el, function (target) {\n        // Always show item tooltip if mouse is on the element with dataIndex\n        if (getECData(target).dataIndex != null) {\n          seriesDispatcher_1 = target;\n          return true;\n        }\n        // Tooltip provided directly. Like legend.\n        if (getECData(target).tooltipConfig != null) {\n          cmptDispatcher_1 = target;\n          return true;\n        }\n      }, true);\n      if (seriesDispatcher_1) {\n        this._showSeriesItemTooltip(e, seriesDispatcher_1, dispatchAction);\n      } else if (cmptDispatcher_1) {\n        this._showComponentItemTooltip(e, cmptDispatcher_1, dispatchAction);\n      } else {\n        this._hide(dispatchAction);\n      }\n    } else {\n      this._lastDataByCoordSys = null;\n      this._hide(dispatchAction);\n    }\n  };\n  TooltipView.prototype._showOrMove = function (tooltipModel, cb) {\n    // showDelay is used in this case: tooltip.enterable is set\n    // as true. User intent to move mouse into tooltip and click\n    // something. `showDelay` makes it easier to enter the content\n    // but tooltip do not move immediately.\n    var delay = tooltipModel.get('showDelay');\n    cb = bind(cb, this);\n    clearTimeout(this._showTimout);\n    delay > 0 ? this._showTimout = setTimeout(cb, delay) : cb();\n  };\n  TooltipView.prototype._showAxisTooltip = function (dataByCoordSys, e) {\n    var ecModel = this._ecModel;\n    var globalTooltipModel = this._tooltipModel;\n    var point = [e.offsetX, e.offsetY];\n    var singleTooltipModel = buildTooltipModel([e.tooltipOption], globalTooltipModel);\n    var renderMode = this._renderMode;\n    var cbParamsList = [];\n    var articleMarkup = createTooltipMarkup('section', {\n      blocks: [],\n      noHeader: true\n    });\n    // Only for legacy: `Serise['formatTooltip']` returns a string.\n    var markupTextArrLegacy = [];\n    var markupStyleCreator = new TooltipMarkupStyleCreator();\n    each(dataByCoordSys, function (itemCoordSys) {\n      each(itemCoordSys.dataByAxis, function (axisItem) {\n        var axisModel = ecModel.getComponent(axisItem.axisDim + 'Axis', axisItem.axisIndex);\n        var axisValue = axisItem.value;\n        if (!axisModel || axisValue == null) {\n          return;\n        }\n        var axisValueLabel = axisPointerViewHelper.getValueLabel(axisValue, axisModel.axis, ecModel, axisItem.seriesDataIndices, axisItem.valueLabelOpt);\n        var axisSectionMarkup = createTooltipMarkup('section', {\n          header: axisValueLabel,\n          noHeader: !trim(axisValueLabel),\n          sortBlocks: true,\n          blocks: []\n        });\n        articleMarkup.blocks.push(axisSectionMarkup);\n        each(axisItem.seriesDataIndices, function (idxItem) {\n          var series = ecModel.getSeriesByIndex(idxItem.seriesIndex);\n          var dataIndex = idxItem.dataIndexInside;\n          var cbParams = series.getDataParams(dataIndex);\n          // Can't find data.\n          if (cbParams.dataIndex < 0) {\n            return;\n          }\n          cbParams.axisDim = axisItem.axisDim;\n          cbParams.axisIndex = axisItem.axisIndex;\n          cbParams.axisType = axisItem.axisType;\n          cbParams.axisId = axisItem.axisId;\n          cbParams.axisValue = axisHelper.getAxisRawValue(axisModel.axis, {\n            value: axisValue\n          });\n          cbParams.axisValueLabel = axisValueLabel;\n          // Pre-create marker style for makers. Users can assemble richText\n          // text in `formatter` callback and use those markers style.\n          cbParams.marker = markupStyleCreator.makeTooltipMarker('item', convertToColorString(cbParams.color), renderMode);\n          var seriesTooltipResult = normalizeTooltipFormatResult(series.formatTooltip(dataIndex, true, null));\n          var frag = seriesTooltipResult.frag;\n          if (frag) {\n            var valueFormatter = buildTooltipModel([series], globalTooltipModel).get('valueFormatter');\n            axisSectionMarkup.blocks.push(valueFormatter ? extend({\n              valueFormatter: valueFormatter\n            }, frag) : frag);\n          }\n          if (seriesTooltipResult.text) {\n            markupTextArrLegacy.push(seriesTooltipResult.text);\n          }\n          cbParamsList.push(cbParams);\n        });\n      });\n    });\n    // In most cases, the second axis is displays upper on the first one.\n    // So we reverse it to look better.\n    articleMarkup.blocks.reverse();\n    markupTextArrLegacy.reverse();\n    var positionExpr = e.position;\n    var orderMode = singleTooltipModel.get('order');\n    var builtMarkupText = buildTooltipMarkup(articleMarkup, markupStyleCreator, renderMode, orderMode, ecModel.get('useUTC'), singleTooltipModel.get('textStyle'));\n    builtMarkupText && markupTextArrLegacy.unshift(builtMarkupText);\n    var blockBreak = renderMode === 'richText' ? '\\n\\n' : '<br/>';\n    var allMarkupText = markupTextArrLegacy.join(blockBreak);\n    this._showOrMove(singleTooltipModel, function () {\n      if (this._updateContentNotChangedOnAxis(dataByCoordSys, cbParamsList)) {\n        this._updatePosition(singleTooltipModel, positionExpr, point[0], point[1], this._tooltipContent, cbParamsList);\n      } else {\n        this._showTooltipContent(singleTooltipModel, allMarkupText, cbParamsList, Math.random() + '', point[0], point[1], positionExpr, null, markupStyleCreator);\n      }\n    });\n    // Do not trigger events here, because this branch only be entered\n    // from dispatchAction.\n  };\n  TooltipView.prototype._showSeriesItemTooltip = function (e, dispatcher, dispatchAction) {\n    var ecModel = this._ecModel;\n    var ecData = getECData(dispatcher);\n    // Use dataModel in element if possible\n    // Used when mouseover on a element like markPoint or edge\n    // In which case, the data is not main data in series.\n    var seriesIndex = ecData.seriesIndex;\n    var seriesModel = ecModel.getSeriesByIndex(seriesIndex);\n    // For example, graph link.\n    var dataModel = ecData.dataModel || seriesModel;\n    var dataIndex = ecData.dataIndex;\n    var dataType = ecData.dataType;\n    var data = dataModel.getData(dataType);\n    var renderMode = this._renderMode;\n    var positionDefault = e.positionDefault;\n    var tooltipModel = buildTooltipModel([data.getItemModel(dataIndex), dataModel, seriesModel && (seriesModel.coordinateSystem || {}).model], this._tooltipModel, positionDefault ? {\n      position: positionDefault\n    } : null);\n    var tooltipTrigger = tooltipModel.get('trigger');\n    if (tooltipTrigger != null && tooltipTrigger !== 'item') {\n      return;\n    }\n    var params = dataModel.getDataParams(dataIndex, dataType);\n    var markupStyleCreator = new TooltipMarkupStyleCreator();\n    // Pre-create marker style for makers. Users can assemble richText\n    // text in `formatter` callback and use those markers style.\n    params.marker = markupStyleCreator.makeTooltipMarker('item', convertToColorString(params.color), renderMode);\n    var seriesTooltipResult = normalizeTooltipFormatResult(dataModel.formatTooltip(dataIndex, false, dataType));\n    var orderMode = tooltipModel.get('order');\n    var valueFormatter = tooltipModel.get('valueFormatter');\n    var frag = seriesTooltipResult.frag;\n    var markupText = frag ? buildTooltipMarkup(valueFormatter ? extend({\n      valueFormatter: valueFormatter\n    }, frag) : frag, markupStyleCreator, renderMode, orderMode, ecModel.get('useUTC'), tooltipModel.get('textStyle')) : seriesTooltipResult.text;\n    var asyncTicket = 'item_' + dataModel.name + '_' + dataIndex;\n    this._showOrMove(tooltipModel, function () {\n      this._showTooltipContent(tooltipModel, markupText, params, asyncTicket, e.offsetX, e.offsetY, e.position, e.target, markupStyleCreator);\n    });\n    // FIXME\n    // duplicated showtip if manuallyShowTip is called from dispatchAction.\n    dispatchAction({\n      type: 'showTip',\n      dataIndexInside: dataIndex,\n      dataIndex: data.getRawIndex(dataIndex),\n      seriesIndex: seriesIndex,\n      from: this.uid\n    });\n  };\n  TooltipView.prototype._showComponentItemTooltip = function (e, el, dispatchAction) {\n    var isHTMLRenderMode = this._renderMode === 'html';\n    var ecData = getECData(el);\n    var tooltipConfig = ecData.tooltipConfig;\n    var tooltipOpt = tooltipConfig.option || {};\n    var encodeHTMLContent = tooltipOpt.encodeHTMLContent;\n    if (isString(tooltipOpt)) {\n      var content = tooltipOpt;\n      tooltipOpt = {\n        content: content,\n        // Fixed formatter\n        formatter: content\n      };\n      // when `tooltipConfig.option` is a string rather than an object,\n      // we can't know if the content needs to be encoded\n      // for the sake of security, encode it by default.\n      encodeHTMLContent = true;\n    }\n    if (encodeHTMLContent && isHTMLRenderMode && tooltipOpt.content) {\n      // clone might be unnecessary?\n      tooltipOpt = clone(tooltipOpt);\n      tooltipOpt.content = encodeHTML(tooltipOpt.content);\n    }\n    var tooltipModelCascade = [tooltipOpt];\n    var cmpt = this._ecModel.getComponent(ecData.componentMainType, ecData.componentIndex);\n    if (cmpt) {\n      tooltipModelCascade.push(cmpt);\n    }\n    // In most cases, component tooltip formatter has different params with series tooltip formatter,\n    // so that they cannot share the same formatter. Since the global tooltip formatter is used for series\n    // by convention, we do not use it as the default formatter for component.\n    tooltipModelCascade.push({\n      formatter: tooltipOpt.content\n    });\n    var positionDefault = e.positionDefault;\n    var subTooltipModel = buildTooltipModel(tooltipModelCascade, this._tooltipModel, positionDefault ? {\n      position: positionDefault\n    } : null);\n    var defaultHtml = subTooltipModel.get('content');\n    var asyncTicket = Math.random() + '';\n    // PENDING: this case do not support richText style yet.\n    var markupStyleCreator = new TooltipMarkupStyleCreator();\n    // Do not check whether `trigger` is 'none' here, because `trigger`\n    // only works on coordinate system. In fact, we have not found case\n    // that requires setting `trigger` nothing on component yet.\n    this._showOrMove(subTooltipModel, function () {\n      // Use formatterParams from element defined in component\n      // Avoid users modify it.\n      var formatterParams = clone(subTooltipModel.get('formatterParams') || {});\n      this._showTooltipContent(subTooltipModel, defaultHtml, formatterParams, asyncTicket, e.offsetX, e.offsetY, e.position, el, markupStyleCreator);\n    });\n    // If not dispatch showTip, tip may be hide triggered by axis.\n    dispatchAction({\n      type: 'showTip',\n      from: this.uid\n    });\n  };\n  TooltipView.prototype._showTooltipContent = function (\n  // Use Model<TooltipOption> insteadof TooltipModel because this model may be from series or other options.\n  // Instead of top level tooltip.\n  tooltipModel, defaultHtml, params, asyncTicket, x, y, positionExpr, el, markupStyleCreator) {\n    // Reset ticket\n    this._ticket = '';\n    if (!tooltipModel.get('showContent') || !tooltipModel.get('show')) {\n      return;\n    }\n    var tooltipContent = this._tooltipContent;\n    tooltipContent.setEnterable(tooltipModel.get('enterable'));\n    var formatter = tooltipModel.get('formatter');\n    positionExpr = positionExpr || tooltipModel.get('position');\n    var html = defaultHtml;\n    var nearPoint = this._getNearestPoint([x, y], params, tooltipModel.get('trigger'), tooltipModel.get('borderColor'));\n    var nearPointColor = nearPoint.color;\n    if (formatter) {\n      if (isString(formatter)) {\n        var useUTC = tooltipModel.ecModel.get('useUTC');\n        var params0 = isArray(params) ? params[0] : params;\n        var isTimeAxis = params0 && params0.axisType && params0.axisType.indexOf('time') >= 0;\n        html = formatter;\n        if (isTimeAxis) {\n          html = timeFormat(params0.axisValue, html, useUTC);\n        }\n        html = formatTpl(html, params, true);\n      } else if (isFunction(formatter)) {\n        var callback = bind(function (cbTicket, html) {\n          if (cbTicket === this._ticket) {\n            tooltipContent.setContent(html, markupStyleCreator, tooltipModel, nearPointColor, positionExpr);\n            this._updatePosition(tooltipModel, positionExpr, x, y, tooltipContent, params, el);\n          }\n        }, this);\n        this._ticket = asyncTicket;\n        html = formatter(params, asyncTicket, callback);\n      } else {\n        html = formatter;\n      }\n    }\n    tooltipContent.setContent(html, markupStyleCreator, tooltipModel, nearPointColor, positionExpr);\n    tooltipContent.show(tooltipModel, nearPointColor);\n    this._updatePosition(tooltipModel, positionExpr, x, y, tooltipContent, params, el);\n  };\n  TooltipView.prototype._getNearestPoint = function (point, tooltipDataParams, trigger, borderColor) {\n    if (trigger === 'axis' || isArray(tooltipDataParams)) {\n      return {\n        color: borderColor || (this._renderMode === 'html' ? '#fff' : 'none')\n      };\n    }\n    if (!isArray(tooltipDataParams)) {\n      return {\n        color: borderColor || tooltipDataParams.color || tooltipDataParams.borderColor\n      };\n    }\n  };\n  TooltipView.prototype._updatePosition = function (tooltipModel, positionExpr, x,\n  // Mouse x\n  y,\n  // Mouse y\n  content, params, el) {\n    var viewWidth = this._api.getWidth();\n    var viewHeight = this._api.getHeight();\n    positionExpr = positionExpr || tooltipModel.get('position');\n    var contentSize = content.getSize();\n    var align = tooltipModel.get('align');\n    var vAlign = tooltipModel.get('verticalAlign');\n    var rect = el && el.getBoundingRect().clone();\n    el && rect.applyTransform(el.transform);\n    if (isFunction(positionExpr)) {\n      // Callback of position can be an array or a string specify the position\n      positionExpr = positionExpr([x, y], params, content.el, rect, {\n        viewSize: [viewWidth, viewHeight],\n        contentSize: contentSize.slice()\n      });\n    }\n    if (isArray(positionExpr)) {\n      x = parsePercent(positionExpr[0], viewWidth);\n      y = parsePercent(positionExpr[1], viewHeight);\n    } else if (isObject(positionExpr)) {\n      var boxLayoutPosition = positionExpr;\n      boxLayoutPosition.width = contentSize[0];\n      boxLayoutPosition.height = contentSize[1];\n      var layoutRect = getLayoutRect(boxLayoutPosition, {\n        width: viewWidth,\n        height: viewHeight\n      });\n      x = layoutRect.x;\n      y = layoutRect.y;\n      align = null;\n      // When positionExpr is left/top/right/bottom,\n      // align and verticalAlign will not work.\n      vAlign = null;\n    }\n    // Specify tooltip position by string 'top' 'bottom' 'left' 'right' around graphic element\n    else if (isString(positionExpr) && el) {\n      var pos = calcTooltipPosition(positionExpr, rect, contentSize, tooltipModel.get('borderWidth'));\n      x = pos[0];\n      y = pos[1];\n    } else {\n      var pos = refixTooltipPosition(x, y, content, viewWidth, viewHeight, align ? null : 20, vAlign ? null : 20);\n      x = pos[0];\n      y = pos[1];\n    }\n    align && (x -= isCenterAlign(align) ? contentSize[0] / 2 : align === 'right' ? contentSize[0] : 0);\n    vAlign && (y -= isCenterAlign(vAlign) ? contentSize[1] / 2 : vAlign === 'bottom' ? contentSize[1] : 0);\n    if (shouldTooltipConfine(tooltipModel)) {\n      var pos = confineTooltipPosition(x, y, content, viewWidth, viewHeight);\n      x = pos[0];\n      y = pos[1];\n    }\n    content.moveTo(x, y);\n  };\n  // FIXME\n  // Should we remove this but leave this to user?\n  TooltipView.prototype._updateContentNotChangedOnAxis = function (dataByCoordSys, cbParamsList) {\n    var lastCoordSys = this._lastDataByCoordSys;\n    var lastCbParamsList = this._cbParamsList;\n    var contentNotChanged = !!lastCoordSys && lastCoordSys.length === dataByCoordSys.length;\n    contentNotChanged && each(lastCoordSys, function (lastItemCoordSys, indexCoordSys) {\n      var lastDataByAxis = lastItemCoordSys.dataByAxis || [];\n      var thisItemCoordSys = dataByCoordSys[indexCoordSys] || {};\n      var thisDataByAxis = thisItemCoordSys.dataByAxis || [];\n      contentNotChanged = contentNotChanged && lastDataByAxis.length === thisDataByAxis.length;\n      contentNotChanged && each(lastDataByAxis, function (lastItem, indexAxis) {\n        var thisItem = thisDataByAxis[indexAxis] || {};\n        var lastIndices = lastItem.seriesDataIndices || [];\n        var newIndices = thisItem.seriesDataIndices || [];\n        contentNotChanged = contentNotChanged && lastItem.value === thisItem.value && lastItem.axisType === thisItem.axisType && lastItem.axisId === thisItem.axisId && lastIndices.length === newIndices.length;\n        contentNotChanged && each(lastIndices, function (lastIdxItem, j) {\n          var newIdxItem = newIndices[j];\n          contentNotChanged = contentNotChanged && lastIdxItem.seriesIndex === newIdxItem.seriesIndex && lastIdxItem.dataIndex === newIdxItem.dataIndex;\n        });\n        // check is cbParams data value changed\n        lastCbParamsList && each(lastItem.seriesDataIndices, function (idxItem) {\n          var seriesIdx = idxItem.seriesIndex;\n          var cbParams = cbParamsList[seriesIdx];\n          var lastCbParams = lastCbParamsList[seriesIdx];\n          if (cbParams && lastCbParams && lastCbParams.data !== cbParams.data) {\n            contentNotChanged = false;\n          }\n        });\n      });\n    });\n    this._lastDataByCoordSys = dataByCoordSys;\n    this._cbParamsList = cbParamsList;\n    return !!contentNotChanged;\n  };\n  TooltipView.prototype._hide = function (dispatchAction) {\n    // Do not directly hideLater here, because this behavior may be prevented\n    // in dispatchAction when showTip is dispatched.\n    // FIXME\n    // duplicated hideTip if manuallyHideTip is called from dispatchAction.\n    this._lastDataByCoordSys = null;\n    dispatchAction({\n      type: 'hideTip',\n      from: this.uid\n    });\n  };\n  TooltipView.prototype.dispose = function (ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    }\n    clear(this, '_updatePosition');\n    this._tooltipContent.dispose();\n    globalListener.unregister('itemTooltip', api);\n  };\n  TooltipView.type = 'tooltip';\n  return TooltipView;\n}(ComponentView);\n/**\r\n * From top to bottom. (the last one should be globalTooltipModel);\r\n */\nfunction buildTooltipModel(modelCascade, globalTooltipModel, defaultTooltipOption) {\n  // Last is always tooltip model.\n  var ecModel = globalTooltipModel.ecModel;\n  var resultModel;\n  if (defaultTooltipOption) {\n    resultModel = new Model(defaultTooltipOption, ecModel, ecModel);\n    resultModel = new Model(globalTooltipModel.option, resultModel, ecModel);\n  } else {\n    resultModel = globalTooltipModel;\n  }\n  for (var i = modelCascade.length - 1; i >= 0; i--) {\n    var tooltipOpt = modelCascade[i];\n    if (tooltipOpt) {\n      if (tooltipOpt instanceof Model) {\n        tooltipOpt = tooltipOpt.get('tooltip', true);\n      }\n      // In each data item tooltip can be simply write:\n      // {\n      //  value: 10,\n      //  tooltip: 'Something you need to know'\n      // }\n      if (isString(tooltipOpt)) {\n        tooltipOpt = {\n          formatter: tooltipOpt\n        };\n      }\n      if (tooltipOpt) {\n        resultModel = new Model(tooltipOpt, resultModel, ecModel);\n      }\n    }\n  }\n  return resultModel;\n}\nfunction makeDispatchAction(payload, api) {\n  return payload.dispatchAction || bind(api.dispatchAction, api);\n}\nfunction refixTooltipPosition(x, y, content, viewWidth, viewHeight, gapH, gapV) {\n  var size = content.getSize();\n  var width = size[0];\n  var height = size[1];\n  if (gapH != null) {\n    // Add extra 2 pixels for this case:\n    // At present the \"values\" in default tooltip are using CSS `float: right`.\n    // When the right edge of the tooltip box is on the right side of the\n    // viewport, the `float` layout might push the \"values\" to the second line.\n    if (x + width + gapH + 2 > viewWidth) {\n      x -= width + gapH;\n    } else {\n      x += gapH;\n    }\n  }\n  if (gapV != null) {\n    if (y + height + gapV > viewHeight) {\n      y -= height + gapV;\n    } else {\n      y += gapV;\n    }\n  }\n  return [x, y];\n}\nfunction confineTooltipPosition(x, y, content, viewWidth, viewHeight) {\n  var size = content.getSize();\n  var width = size[0];\n  var height = size[1];\n  x = Math.min(x + width, viewWidth) - width;\n  y = Math.min(y + height, viewHeight) - height;\n  x = Math.max(x, 0);\n  y = Math.max(y, 0);\n  return [x, y];\n}\nfunction calcTooltipPosition(position, rect, contentSize, borderWidth) {\n  var domWidth = contentSize[0];\n  var domHeight = contentSize[1];\n  var offset = Math.ceil(Math.SQRT2 * borderWidth) + 8;\n  var x = 0;\n  var y = 0;\n  var rectWidth = rect.width;\n  var rectHeight = rect.height;\n  switch (position) {\n    case 'inside':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n      break;\n    case 'top':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y - domHeight - offset;\n      break;\n    case 'bottom':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y + rectHeight + offset;\n      break;\n    case 'left':\n      x = rect.x - domWidth - offset;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n      break;\n    case 'right':\n      x = rect.x + rectWidth + offset;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n  }\n  return [x, y];\n}\nfunction isCenterAlign(align) {\n  return align === 'center' || align === 'middle';\n}\n/**\r\n * Find target component by payload like:\r\n * ```js\r\n * { legendId: 'some_id', name: 'xxx' }\r\n * { toolboxIndex: 1, name: 'xxx' }\r\n * { geoName: 'some_name', name: 'xxx' }\r\n * ```\r\n * PENDING: at present only\r\n *\r\n * If not found, return null/undefined.\r\n */\nfunction findComponentReference(payload, ecModel, api) {\n  var queryOptionMap = preParseFinder(payload).queryOptionMap;\n  var componentMainType = queryOptionMap.keys()[0];\n  if (!componentMainType || componentMainType === 'series') {\n    return;\n  }\n  var queryResult = queryReferringComponents(ecModel, componentMainType, queryOptionMap.get(componentMainType), {\n    useDefault: false,\n    enableAll: false,\n    enableNone: false\n  });\n  var model = queryResult.models[0];\n  if (!model) {\n    return;\n  }\n  var view = api.getViewOfComponentModel(model);\n  var el;\n  view.group.traverse(function (subEl) {\n    var tooltipConfig = getECData(subEl).tooltipConfig;\n    if (tooltipConfig && tooltipConfig.name === payload.name) {\n      el = subEl;\n      return true; // stop\n    }\n  });\n  if (el) {\n    return {\n      componentMainType: componentMainType,\n      componentIndex: model.componentIndex,\n      el: el\n    };\n  }\n}\nexport default TooltipView;", "map": {"version": 3, "names": ["__extends", "bind", "each", "clone", "trim", "isString", "isFunction", "isArray", "isObject", "extend", "env", "TooltipHTMLContent", "TooltipRichContent", "convertToColorString", "encodeHTML", "formatTpl", "parsePercent", "Rect", "findPointFromSeries", "getLayoutRect", "Model", "globalListener", "axisHelper", "axisPointerViewHelper", "getTooltipRenderMode", "preParseFinder", "queryReferringComponents", "ComponentView", "format", "timeFormat", "getECData", "shouldTooltipConfine", "normalizeTooltipFormatResult", "createTooltipMarkup", "buildTooltipMarkup", "TooltipMarkupStyleCreator", "find<PERSON>vent<PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "createOrUpdate", "proxyRect", "shape", "x", "y", "width", "height", "TooltipView", "_super", "_this", "apply", "arguments", "type", "prototype", "init", "ecModel", "api", "node", "getDom", "tooltipModel", "getComponent", "renderMode", "_renderMode", "get", "_tooltipContent", "appendTo", "render", "group", "removeAll", "_tooltipModel", "_ecModel", "_api", "tooltipContent", "update", "setEnterable", "_initGlobalListener", "_keepShow", "triggerOn", "register", "currTrigger", "e", "dispatchAction", "indexOf", "_tryShow", "_hide", "_lastX", "_lastY", "self_1", "clearTimeout", "_refreshUpdateTimeout", "setTimeout", "isDisposed", "manuallyShowTip", "dataByCoordSys", "_lastDataByCoordSys", "payload", "from", "uid", "makeDispatchAction", "_ticket", "cmptRef", "findComponentReference", "rect", "el", "getBoundingRect", "applyTransform", "transform", "offsetX", "offsetY", "target", "position", "<PERSON><PERSON><PERSON><PERSON>", "tooltip", "tooltipConfig", "name", "option", "tooltipOption", "seriesIndex", "_manuallyAxisShowTip", "pointInfo", "cx", "point", "cy", "getZr", "findHover", "manuallyHideTip", "hideLater", "dataIndex", "coordSysAxesInfo", "seriesModel", "getSeriesByIndex", "data", "getData", "tooltipCascadedModel", "buildTooltipModel", "getItemModel", "coordinateSystem", "model", "length", "_showAxisTooltip", "ecData", "ssrType", "seriesDispatcher_1", "cmptDispatcher_1", "_showSeriesItemTooltip", "_showComponentItemTooltip", "_showOrMove", "cb", "delay", "_showTimout", "globalTooltipModel", "singleTooltipModel", "cbParamsList", "articleMarkup", "blocks", "<PERSON><PERSON><PERSON><PERSON>", "markupTextArrLegacy", "markupStyleCreator", "itemCoordSys", "dataByAxis", "axisItem", "axisModel", "axisDim", "axisIndex", "axisValue", "value", "axisValueLabel", "getValueLabel", "axis", "seriesDataIndices", "valueLabelOpt", "axisSectionMarkup", "header", "sortBlocks", "push", "idxItem", "series", "dataIndexInside", "cbParams", "getDataParams", "axisType", "axisId", "getAxisRawValue", "marker", "makeTooltipMarker", "color", "seriesTooltipResult", "formatTooltip", "frag", "valueFormatter", "text", "reverse", "positionExpr", "orderMode", "builtMarkupText", "unshift", "blockBreak", "allMarkupText", "join", "_updateContentNotChangedOnAxis", "_updatePosition", "_showTooltipContent", "Math", "random", "dispatcher", "dataModel", "dataType", "tooltipTrigger", "params", "markupText", "asyncTicket", "getRawIndex", "isHTMLRenderMode", "tooltipOpt", "encodeHTMLContent", "content", "formatter", "tooltipModelCascade", "cmpt", "componentMainType", "componentIndex", "subTooltipModel", "defaultHtml", "formatterParams", "html", "nearPoint", "_getNearestPoint", "nearPointColor", "useUTC", "params0", "isTimeAxis", "callback", "cbTicket", "<PERSON><PERSON><PERSON><PERSON>", "show", "tooltipDataParams", "trigger", "borderColor", "viewWidth", "getWidth", "viewHeight", "getHeight", "contentSize", "getSize", "align", "vAlign", "viewSize", "slice", "boxLayoutPosition", "layoutRect", "pos", "calcTooltipPosition", "refixTooltipPosition", "isCenterAlign", "confineTooltipPosition", "moveTo", "lastCoordSys", "lastCbParamsList", "_cbParamsList", "contentNotChanged", "lastItemCoordSys", "indexCoordSys", "lastDataByAxis", "thisItemCoordSys", "thisDataByAxis", "lastItem", "indexAxis", "thisItem", "lastIndices", "newIndices", "lastIdxItem", "j", "newIdxItem", "seriesIdx", "lastCbParams", "dispose", "unregister", "modelCascade", "defaultTooltipOption", "resultModel", "i", "gapH", "gapV", "size", "min", "max", "borderWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "domHeight", "offset", "ceil", "SQRT2", "rectWidth", "rectHeight", "queryOptionMap", "keys", "query<PERSON><PERSON>ult", "useDefault", "enableAll", "enableNone", "models", "view", "getViewOfComponentModel", "traverse", "subEl"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/echarts/lib/component/tooltip/TooltipView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport { __extends } from \"tslib\";\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { bind, each, clone, trim, isString, isFunction, isArray, isObject, extend } from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport TooltipHTMLContent from './TooltipHTMLContent.js';\nimport TooltipRichContent from './TooltipRichContent.js';\nimport { convertToColorString, encodeHTML, formatTpl } from '../../util/format.js';\nimport { parsePercent } from '../../util/number.js';\nimport { Rect } from '../../util/graphic.js';\nimport findPointFromSeries from '../axisPointer/findPointFromSeries.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport Model from '../../model/Model.js';\nimport * as globalListener from '../axisPointer/globalListener.js';\nimport * as axisHelper from '../../coord/axisHelper.js';\nimport * as axisPointerViewHelper from '../axisPointer/viewHelper.js';\nimport { getTooltipRenderMode, preParseFinder, queryReferringComponents } from '../../util/model.js';\nimport ComponentView from '../../view/Component.js';\nimport { format as timeFormat } from '../../util/time.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { shouldTooltipConfine } from './helper.js';\nimport { normalizeTooltipFormatResult } from '../../model/mixin/dataFormat.js';\nimport { createTooltipMarkup, buildTooltipMarkup, TooltipMarkupStyleCreator } from './tooltipMarkup.js';\nimport { findEventDispatcher } from '../../util/event.js';\nimport { clear, createOrUpdate } from '../../util/throttle.js';\nvar proxyRect = new Rect({\n  shape: {\n    x: -1,\n    y: -1,\n    width: 2,\n    height: 2\n  }\n});\nvar TooltipView = /** @class */function (_super) {\n  __extends(TooltipView, _super);\n  function TooltipView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TooltipView.type;\n    return _this;\n  }\n  TooltipView.prototype.init = function (ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    }\n    var tooltipModel = ecModel.getComponent('tooltip');\n    var renderMode = this._renderMode = getTooltipRenderMode(tooltipModel.get('renderMode'));\n    this._tooltipContent = renderMode === 'richText' ? new TooltipRichContent(api) : new TooltipHTMLContent(api, {\n      appendTo: tooltipModel.get('appendToBody', true) ? 'body' : tooltipModel.get('appendTo', true)\n    });\n  };\n  TooltipView.prototype.render = function (tooltipModel, ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    }\n    // Reset\n    this.group.removeAll();\n    this._tooltipModel = tooltipModel;\n    this._ecModel = ecModel;\n    this._api = api;\n    var tooltipContent = this._tooltipContent;\n    tooltipContent.update(tooltipModel);\n    tooltipContent.setEnterable(tooltipModel.get('enterable'));\n    this._initGlobalListener();\n    this._keepShow();\n    // PENDING\n    // `mousemove` event will be triggered very frequently when the mouse moves fast,\n    // which causes that the `updatePosition` function was also called frequently.\n    // In Chrome with devtools open and Firefox, tooltip looks laggy and shakes. See #14695 #16101\n    // To avoid frequent triggering,\n    // consider throttling it in 50ms when transition is enabled\n    if (this._renderMode !== 'richText' && tooltipModel.get('transitionDuration')) {\n      createOrUpdate(this, '_updatePosition', 50, 'fixRate');\n    } else {\n      clear(this, '_updatePosition');\n    }\n  };\n  TooltipView.prototype._initGlobalListener = function () {\n    var tooltipModel = this._tooltipModel;\n    var triggerOn = tooltipModel.get('triggerOn');\n    globalListener.register('itemTooltip', this._api, bind(function (currTrigger, e, dispatchAction) {\n      // If 'none', it is not controlled by mouse totally.\n      if (triggerOn !== 'none') {\n        if (triggerOn.indexOf(currTrigger) >= 0) {\n          this._tryShow(e, dispatchAction);\n        } else if (currTrigger === 'leave') {\n          this._hide(dispatchAction);\n        }\n      }\n    }, this));\n  };\n  TooltipView.prototype._keepShow = function () {\n    var tooltipModel = this._tooltipModel;\n    var ecModel = this._ecModel;\n    var api = this._api;\n    var triggerOn = tooltipModel.get('triggerOn');\n    // Try to keep the tooltip show when refreshing\n    if (this._lastX != null && this._lastY != null\n    // When user is willing to control tooltip totally using API,\n    // self.manuallyShowTip({x, y}) might cause tooltip hide,\n    // which is not expected.\n    && triggerOn !== 'none' && triggerOn !== 'click') {\n      var self_1 = this;\n      clearTimeout(this._refreshUpdateTimeout);\n      this._refreshUpdateTimeout = setTimeout(function () {\n        // Show tip next tick after other charts are rendered\n        // In case highlight action has wrong result\n        // FIXME\n        !api.isDisposed() && self_1.manuallyShowTip(tooltipModel, ecModel, api, {\n          x: self_1._lastX,\n          y: self_1._lastY,\n          dataByCoordSys: self_1._lastDataByCoordSys\n        });\n      });\n    }\n  };\n  /**\r\n   * Show tip manually by\r\n   * dispatchAction({\r\n   *     type: 'showTip',\r\n   *     x: 10,\r\n   *     y: 10\r\n   * });\r\n   * Or\r\n   * dispatchAction({\r\n   *      type: 'showTip',\r\n   *      seriesIndex: 0,\r\n   *      dataIndex or dataIndexInside or name\r\n   * });\r\n   *\r\n   *  TODO Batch\r\n   */\n  TooltipView.prototype.manuallyShowTip = function (tooltipModel, ecModel, api, payload) {\n    if (payload.from === this.uid || env.node || !api.getDom()) {\n      return;\n    }\n    var dispatchAction = makeDispatchAction(payload, api);\n    // Reset ticket\n    this._ticket = '';\n    // When triggered from axisPointer.\n    var dataByCoordSys = payload.dataByCoordSys;\n    var cmptRef = findComponentReference(payload, ecModel, api);\n    if (cmptRef) {\n      var rect = cmptRef.el.getBoundingRect().clone();\n      rect.applyTransform(cmptRef.el.transform);\n      this._tryShow({\n        offsetX: rect.x + rect.width / 2,\n        offsetY: rect.y + rect.height / 2,\n        target: cmptRef.el,\n        position: payload.position,\n        // When manully trigger, the mouse is not on the el, so we'd better to\n        // position tooltip on the bottom of the el and display arrow is possible.\n        positionDefault: 'bottom'\n      }, dispatchAction);\n    } else if (payload.tooltip && payload.x != null && payload.y != null) {\n      var el = proxyRect;\n      el.x = payload.x;\n      el.y = payload.y;\n      el.update();\n      getECData(el).tooltipConfig = {\n        name: null,\n        option: payload.tooltip\n      };\n      // Manually show tooltip while view is not using zrender elements.\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        target: el\n      }, dispatchAction);\n    } else if (dataByCoordSys) {\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        position: payload.position,\n        dataByCoordSys: dataByCoordSys,\n        tooltipOption: payload.tooltipOption\n      }, dispatchAction);\n    } else if (payload.seriesIndex != null) {\n      if (this._manuallyAxisShowTip(tooltipModel, ecModel, api, payload)) {\n        return;\n      }\n      var pointInfo = findPointFromSeries(payload, ecModel);\n      var cx = pointInfo.point[0];\n      var cy = pointInfo.point[1];\n      if (cx != null && cy != null) {\n        this._tryShow({\n          offsetX: cx,\n          offsetY: cy,\n          target: pointInfo.el,\n          position: payload.position,\n          // When manully trigger, the mouse is not on the el, so we'd better to\n          // position tooltip on the bottom of the el and display arrow is possible.\n          positionDefault: 'bottom'\n        }, dispatchAction);\n      }\n    } else if (payload.x != null && payload.y != null) {\n      // FIXME\n      // should wrap dispatchAction like `axisPointer/globalListener` ?\n      api.dispatchAction({\n        type: 'updateAxisPointer',\n        x: payload.x,\n        y: payload.y\n      });\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        position: payload.position,\n        target: api.getZr().findHover(payload.x, payload.y).target\n      }, dispatchAction);\n    }\n  };\n  TooltipView.prototype.manuallyHideTip = function (tooltipModel, ecModel, api, payload) {\n    var tooltipContent = this._tooltipContent;\n    if (this._tooltipModel) {\n      tooltipContent.hideLater(this._tooltipModel.get('hideDelay'));\n    }\n    this._lastX = this._lastY = this._lastDataByCoordSys = null;\n    if (payload.from !== this.uid) {\n      this._hide(makeDispatchAction(payload, api));\n    }\n  };\n  // Be compatible with previous design, that is, when tooltip.type is 'axis' and\n  // dispatchAction 'showTip' with seriesIndex and dataIndex will trigger axis pointer\n  // and tooltip.\n  TooltipView.prototype._manuallyAxisShowTip = function (tooltipModel, ecModel, api, payload) {\n    var seriesIndex = payload.seriesIndex;\n    var dataIndex = payload.dataIndex;\n    // @ts-ignore\n    var coordSysAxesInfo = ecModel.getComponent('axisPointer').coordSysAxesInfo;\n    if (seriesIndex == null || dataIndex == null || coordSysAxesInfo == null) {\n      return;\n    }\n    var seriesModel = ecModel.getSeriesByIndex(seriesIndex);\n    if (!seriesModel) {\n      return;\n    }\n    var data = seriesModel.getData();\n    var tooltipCascadedModel = buildTooltipModel([data.getItemModel(dataIndex), seriesModel, (seriesModel.coordinateSystem || {}).model], this._tooltipModel);\n    if (tooltipCascadedModel.get('trigger') !== 'axis') {\n      return;\n    }\n    api.dispatchAction({\n      type: 'updateAxisPointer',\n      seriesIndex: seriesIndex,\n      dataIndex: dataIndex,\n      position: payload.position\n    });\n    return true;\n  };\n  TooltipView.prototype._tryShow = function (e, dispatchAction) {\n    var el = e.target;\n    var tooltipModel = this._tooltipModel;\n    if (!tooltipModel) {\n      return;\n    }\n    // Save mouse x, mouse y. So we can try to keep showing the tip if chart is refreshed\n    this._lastX = e.offsetX;\n    this._lastY = e.offsetY;\n    var dataByCoordSys = e.dataByCoordSys;\n    if (dataByCoordSys && dataByCoordSys.length) {\n      this._showAxisTooltip(dataByCoordSys, e);\n    } else if (el) {\n      var ecData = getECData(el);\n      if (ecData.ssrType === 'legend') {\n        // Don't trigger tooltip for legend tooltip item\n        return;\n      }\n      this._lastDataByCoordSys = null;\n      var seriesDispatcher_1;\n      var cmptDispatcher_1;\n      findEventDispatcher(el, function (target) {\n        // Always show item tooltip if mouse is on the element with dataIndex\n        if (getECData(target).dataIndex != null) {\n          seriesDispatcher_1 = target;\n          return true;\n        }\n        // Tooltip provided directly. Like legend.\n        if (getECData(target).tooltipConfig != null) {\n          cmptDispatcher_1 = target;\n          return true;\n        }\n      }, true);\n      if (seriesDispatcher_1) {\n        this._showSeriesItemTooltip(e, seriesDispatcher_1, dispatchAction);\n      } else if (cmptDispatcher_1) {\n        this._showComponentItemTooltip(e, cmptDispatcher_1, dispatchAction);\n      } else {\n        this._hide(dispatchAction);\n      }\n    } else {\n      this._lastDataByCoordSys = null;\n      this._hide(dispatchAction);\n    }\n  };\n  TooltipView.prototype._showOrMove = function (tooltipModel, cb) {\n    // showDelay is used in this case: tooltip.enterable is set\n    // as true. User intent to move mouse into tooltip and click\n    // something. `showDelay` makes it easier to enter the content\n    // but tooltip do not move immediately.\n    var delay = tooltipModel.get('showDelay');\n    cb = bind(cb, this);\n    clearTimeout(this._showTimout);\n    delay > 0 ? this._showTimout = setTimeout(cb, delay) : cb();\n  };\n  TooltipView.prototype._showAxisTooltip = function (dataByCoordSys, e) {\n    var ecModel = this._ecModel;\n    var globalTooltipModel = this._tooltipModel;\n    var point = [e.offsetX, e.offsetY];\n    var singleTooltipModel = buildTooltipModel([e.tooltipOption], globalTooltipModel);\n    var renderMode = this._renderMode;\n    var cbParamsList = [];\n    var articleMarkup = createTooltipMarkup('section', {\n      blocks: [],\n      noHeader: true\n    });\n    // Only for legacy: `Serise['formatTooltip']` returns a string.\n    var markupTextArrLegacy = [];\n    var markupStyleCreator = new TooltipMarkupStyleCreator();\n    each(dataByCoordSys, function (itemCoordSys) {\n      each(itemCoordSys.dataByAxis, function (axisItem) {\n        var axisModel = ecModel.getComponent(axisItem.axisDim + 'Axis', axisItem.axisIndex);\n        var axisValue = axisItem.value;\n        if (!axisModel || axisValue == null) {\n          return;\n        }\n        var axisValueLabel = axisPointerViewHelper.getValueLabel(axisValue, axisModel.axis, ecModel, axisItem.seriesDataIndices, axisItem.valueLabelOpt);\n        var axisSectionMarkup = createTooltipMarkup('section', {\n          header: axisValueLabel,\n          noHeader: !trim(axisValueLabel),\n          sortBlocks: true,\n          blocks: []\n        });\n        articleMarkup.blocks.push(axisSectionMarkup);\n        each(axisItem.seriesDataIndices, function (idxItem) {\n          var series = ecModel.getSeriesByIndex(idxItem.seriesIndex);\n          var dataIndex = idxItem.dataIndexInside;\n          var cbParams = series.getDataParams(dataIndex);\n          // Can't find data.\n          if (cbParams.dataIndex < 0) {\n            return;\n          }\n          cbParams.axisDim = axisItem.axisDim;\n          cbParams.axisIndex = axisItem.axisIndex;\n          cbParams.axisType = axisItem.axisType;\n          cbParams.axisId = axisItem.axisId;\n          cbParams.axisValue = axisHelper.getAxisRawValue(axisModel.axis, {\n            value: axisValue\n          });\n          cbParams.axisValueLabel = axisValueLabel;\n          // Pre-create marker style for makers. Users can assemble richText\n          // text in `formatter` callback and use those markers style.\n          cbParams.marker = markupStyleCreator.makeTooltipMarker('item', convertToColorString(cbParams.color), renderMode);\n          var seriesTooltipResult = normalizeTooltipFormatResult(series.formatTooltip(dataIndex, true, null));\n          var frag = seriesTooltipResult.frag;\n          if (frag) {\n            var valueFormatter = buildTooltipModel([series], globalTooltipModel).get('valueFormatter');\n            axisSectionMarkup.blocks.push(valueFormatter ? extend({\n              valueFormatter: valueFormatter\n            }, frag) : frag);\n          }\n          if (seriesTooltipResult.text) {\n            markupTextArrLegacy.push(seriesTooltipResult.text);\n          }\n          cbParamsList.push(cbParams);\n        });\n      });\n    });\n    // In most cases, the second axis is displays upper on the first one.\n    // So we reverse it to look better.\n    articleMarkup.blocks.reverse();\n    markupTextArrLegacy.reverse();\n    var positionExpr = e.position;\n    var orderMode = singleTooltipModel.get('order');\n    var builtMarkupText = buildTooltipMarkup(articleMarkup, markupStyleCreator, renderMode, orderMode, ecModel.get('useUTC'), singleTooltipModel.get('textStyle'));\n    builtMarkupText && markupTextArrLegacy.unshift(builtMarkupText);\n    var blockBreak = renderMode === 'richText' ? '\\n\\n' : '<br/>';\n    var allMarkupText = markupTextArrLegacy.join(blockBreak);\n    this._showOrMove(singleTooltipModel, function () {\n      if (this._updateContentNotChangedOnAxis(dataByCoordSys, cbParamsList)) {\n        this._updatePosition(singleTooltipModel, positionExpr, point[0], point[1], this._tooltipContent, cbParamsList);\n      } else {\n        this._showTooltipContent(singleTooltipModel, allMarkupText, cbParamsList, Math.random() + '', point[0], point[1], positionExpr, null, markupStyleCreator);\n      }\n    });\n    // Do not trigger events here, because this branch only be entered\n    // from dispatchAction.\n  };\n  TooltipView.prototype._showSeriesItemTooltip = function (e, dispatcher, dispatchAction) {\n    var ecModel = this._ecModel;\n    var ecData = getECData(dispatcher);\n    // Use dataModel in element if possible\n    // Used when mouseover on a element like markPoint or edge\n    // In which case, the data is not main data in series.\n    var seriesIndex = ecData.seriesIndex;\n    var seriesModel = ecModel.getSeriesByIndex(seriesIndex);\n    // For example, graph link.\n    var dataModel = ecData.dataModel || seriesModel;\n    var dataIndex = ecData.dataIndex;\n    var dataType = ecData.dataType;\n    var data = dataModel.getData(dataType);\n    var renderMode = this._renderMode;\n    var positionDefault = e.positionDefault;\n    var tooltipModel = buildTooltipModel([data.getItemModel(dataIndex), dataModel, seriesModel && (seriesModel.coordinateSystem || {}).model], this._tooltipModel, positionDefault ? {\n      position: positionDefault\n    } : null);\n    var tooltipTrigger = tooltipModel.get('trigger');\n    if (tooltipTrigger != null && tooltipTrigger !== 'item') {\n      return;\n    }\n    var params = dataModel.getDataParams(dataIndex, dataType);\n    var markupStyleCreator = new TooltipMarkupStyleCreator();\n    // Pre-create marker style for makers. Users can assemble richText\n    // text in `formatter` callback and use those markers style.\n    params.marker = markupStyleCreator.makeTooltipMarker('item', convertToColorString(params.color), renderMode);\n    var seriesTooltipResult = normalizeTooltipFormatResult(dataModel.formatTooltip(dataIndex, false, dataType));\n    var orderMode = tooltipModel.get('order');\n    var valueFormatter = tooltipModel.get('valueFormatter');\n    var frag = seriesTooltipResult.frag;\n    var markupText = frag ? buildTooltipMarkup(valueFormatter ? extend({\n      valueFormatter: valueFormatter\n    }, frag) : frag, markupStyleCreator, renderMode, orderMode, ecModel.get('useUTC'), tooltipModel.get('textStyle')) : seriesTooltipResult.text;\n    var asyncTicket = 'item_' + dataModel.name + '_' + dataIndex;\n    this._showOrMove(tooltipModel, function () {\n      this._showTooltipContent(tooltipModel, markupText, params, asyncTicket, e.offsetX, e.offsetY, e.position, e.target, markupStyleCreator);\n    });\n    // FIXME\n    // duplicated showtip if manuallyShowTip is called from dispatchAction.\n    dispatchAction({\n      type: 'showTip',\n      dataIndexInside: dataIndex,\n      dataIndex: data.getRawIndex(dataIndex),\n      seriesIndex: seriesIndex,\n      from: this.uid\n    });\n  };\n  TooltipView.prototype._showComponentItemTooltip = function (e, el, dispatchAction) {\n    var isHTMLRenderMode = this._renderMode === 'html';\n    var ecData = getECData(el);\n    var tooltipConfig = ecData.tooltipConfig;\n    var tooltipOpt = tooltipConfig.option || {};\n    var encodeHTMLContent = tooltipOpt.encodeHTMLContent;\n    if (isString(tooltipOpt)) {\n      var content = tooltipOpt;\n      tooltipOpt = {\n        content: content,\n        // Fixed formatter\n        formatter: content\n      };\n      // when `tooltipConfig.option` is a string rather than an object,\n      // we can't know if the content needs to be encoded\n      // for the sake of security, encode it by default.\n      encodeHTMLContent = true;\n    }\n    if (encodeHTMLContent && isHTMLRenderMode && tooltipOpt.content) {\n      // clone might be unnecessary?\n      tooltipOpt = clone(tooltipOpt);\n      tooltipOpt.content = encodeHTML(tooltipOpt.content);\n    }\n    var tooltipModelCascade = [tooltipOpt];\n    var cmpt = this._ecModel.getComponent(ecData.componentMainType, ecData.componentIndex);\n    if (cmpt) {\n      tooltipModelCascade.push(cmpt);\n    }\n    // In most cases, component tooltip formatter has different params with series tooltip formatter,\n    // so that they cannot share the same formatter. Since the global tooltip formatter is used for series\n    // by convention, we do not use it as the default formatter for component.\n    tooltipModelCascade.push({\n      formatter: tooltipOpt.content\n    });\n    var positionDefault = e.positionDefault;\n    var subTooltipModel = buildTooltipModel(tooltipModelCascade, this._tooltipModel, positionDefault ? {\n      position: positionDefault\n    } : null);\n    var defaultHtml = subTooltipModel.get('content');\n    var asyncTicket = Math.random() + '';\n    // PENDING: this case do not support richText style yet.\n    var markupStyleCreator = new TooltipMarkupStyleCreator();\n    // Do not check whether `trigger` is 'none' here, because `trigger`\n    // only works on coordinate system. In fact, we have not found case\n    // that requires setting `trigger` nothing on component yet.\n    this._showOrMove(subTooltipModel, function () {\n      // Use formatterParams from element defined in component\n      // Avoid users modify it.\n      var formatterParams = clone(subTooltipModel.get('formatterParams') || {});\n      this._showTooltipContent(subTooltipModel, defaultHtml, formatterParams, asyncTicket, e.offsetX, e.offsetY, e.position, el, markupStyleCreator);\n    });\n    // If not dispatch showTip, tip may be hide triggered by axis.\n    dispatchAction({\n      type: 'showTip',\n      from: this.uid\n    });\n  };\n  TooltipView.prototype._showTooltipContent = function (\n  // Use Model<TooltipOption> insteadof TooltipModel because this model may be from series or other options.\n  // Instead of top level tooltip.\n  tooltipModel, defaultHtml, params, asyncTicket, x, y, positionExpr, el, markupStyleCreator) {\n    // Reset ticket\n    this._ticket = '';\n    if (!tooltipModel.get('showContent') || !tooltipModel.get('show')) {\n      return;\n    }\n    var tooltipContent = this._tooltipContent;\n    tooltipContent.setEnterable(tooltipModel.get('enterable'));\n    var formatter = tooltipModel.get('formatter');\n    positionExpr = positionExpr || tooltipModel.get('position');\n    var html = defaultHtml;\n    var nearPoint = this._getNearestPoint([x, y], params, tooltipModel.get('trigger'), tooltipModel.get('borderColor'));\n    var nearPointColor = nearPoint.color;\n    if (formatter) {\n      if (isString(formatter)) {\n        var useUTC = tooltipModel.ecModel.get('useUTC');\n        var params0 = isArray(params) ? params[0] : params;\n        var isTimeAxis = params0 && params0.axisType && params0.axisType.indexOf('time') >= 0;\n        html = formatter;\n        if (isTimeAxis) {\n          html = timeFormat(params0.axisValue, html, useUTC);\n        }\n        html = formatTpl(html, params, true);\n      } else if (isFunction(formatter)) {\n        var callback = bind(function (cbTicket, html) {\n          if (cbTicket === this._ticket) {\n            tooltipContent.setContent(html, markupStyleCreator, tooltipModel, nearPointColor, positionExpr);\n            this._updatePosition(tooltipModel, positionExpr, x, y, tooltipContent, params, el);\n          }\n        }, this);\n        this._ticket = asyncTicket;\n        html = formatter(params, asyncTicket, callback);\n      } else {\n        html = formatter;\n      }\n    }\n    tooltipContent.setContent(html, markupStyleCreator, tooltipModel, nearPointColor, positionExpr);\n    tooltipContent.show(tooltipModel, nearPointColor);\n    this._updatePosition(tooltipModel, positionExpr, x, y, tooltipContent, params, el);\n  };\n  TooltipView.prototype._getNearestPoint = function (point, tooltipDataParams, trigger, borderColor) {\n    if (trigger === 'axis' || isArray(tooltipDataParams)) {\n      return {\n        color: borderColor || (this._renderMode === 'html' ? '#fff' : 'none')\n      };\n    }\n    if (!isArray(tooltipDataParams)) {\n      return {\n        color: borderColor || tooltipDataParams.color || tooltipDataParams.borderColor\n      };\n    }\n  };\n  TooltipView.prototype._updatePosition = function (tooltipModel, positionExpr, x,\n  // Mouse x\n  y,\n  // Mouse y\n  content, params, el) {\n    var viewWidth = this._api.getWidth();\n    var viewHeight = this._api.getHeight();\n    positionExpr = positionExpr || tooltipModel.get('position');\n    var contentSize = content.getSize();\n    var align = tooltipModel.get('align');\n    var vAlign = tooltipModel.get('verticalAlign');\n    var rect = el && el.getBoundingRect().clone();\n    el && rect.applyTransform(el.transform);\n    if (isFunction(positionExpr)) {\n      // Callback of position can be an array or a string specify the position\n      positionExpr = positionExpr([x, y], params, content.el, rect, {\n        viewSize: [viewWidth, viewHeight],\n        contentSize: contentSize.slice()\n      });\n    }\n    if (isArray(positionExpr)) {\n      x = parsePercent(positionExpr[0], viewWidth);\n      y = parsePercent(positionExpr[1], viewHeight);\n    } else if (isObject(positionExpr)) {\n      var boxLayoutPosition = positionExpr;\n      boxLayoutPosition.width = contentSize[0];\n      boxLayoutPosition.height = contentSize[1];\n      var layoutRect = getLayoutRect(boxLayoutPosition, {\n        width: viewWidth,\n        height: viewHeight\n      });\n      x = layoutRect.x;\n      y = layoutRect.y;\n      align = null;\n      // When positionExpr is left/top/right/bottom,\n      // align and verticalAlign will not work.\n      vAlign = null;\n    }\n    // Specify tooltip position by string 'top' 'bottom' 'left' 'right' around graphic element\n    else if (isString(positionExpr) && el) {\n      var pos = calcTooltipPosition(positionExpr, rect, contentSize, tooltipModel.get('borderWidth'));\n      x = pos[0];\n      y = pos[1];\n    } else {\n      var pos = refixTooltipPosition(x, y, content, viewWidth, viewHeight, align ? null : 20, vAlign ? null : 20);\n      x = pos[0];\n      y = pos[1];\n    }\n    align && (x -= isCenterAlign(align) ? contentSize[0] / 2 : align === 'right' ? contentSize[0] : 0);\n    vAlign && (y -= isCenterAlign(vAlign) ? contentSize[1] / 2 : vAlign === 'bottom' ? contentSize[1] : 0);\n    if (shouldTooltipConfine(tooltipModel)) {\n      var pos = confineTooltipPosition(x, y, content, viewWidth, viewHeight);\n      x = pos[0];\n      y = pos[1];\n    }\n    content.moveTo(x, y);\n  };\n  // FIXME\n  // Should we remove this but leave this to user?\n  TooltipView.prototype._updateContentNotChangedOnAxis = function (dataByCoordSys, cbParamsList) {\n    var lastCoordSys = this._lastDataByCoordSys;\n    var lastCbParamsList = this._cbParamsList;\n    var contentNotChanged = !!lastCoordSys && lastCoordSys.length === dataByCoordSys.length;\n    contentNotChanged && each(lastCoordSys, function (lastItemCoordSys, indexCoordSys) {\n      var lastDataByAxis = lastItemCoordSys.dataByAxis || [];\n      var thisItemCoordSys = dataByCoordSys[indexCoordSys] || {};\n      var thisDataByAxis = thisItemCoordSys.dataByAxis || [];\n      contentNotChanged = contentNotChanged && lastDataByAxis.length === thisDataByAxis.length;\n      contentNotChanged && each(lastDataByAxis, function (lastItem, indexAxis) {\n        var thisItem = thisDataByAxis[indexAxis] || {};\n        var lastIndices = lastItem.seriesDataIndices || [];\n        var newIndices = thisItem.seriesDataIndices || [];\n        contentNotChanged = contentNotChanged && lastItem.value === thisItem.value && lastItem.axisType === thisItem.axisType && lastItem.axisId === thisItem.axisId && lastIndices.length === newIndices.length;\n        contentNotChanged && each(lastIndices, function (lastIdxItem, j) {\n          var newIdxItem = newIndices[j];\n          contentNotChanged = contentNotChanged && lastIdxItem.seriesIndex === newIdxItem.seriesIndex && lastIdxItem.dataIndex === newIdxItem.dataIndex;\n        });\n        // check is cbParams data value changed\n        lastCbParamsList && each(lastItem.seriesDataIndices, function (idxItem) {\n          var seriesIdx = idxItem.seriesIndex;\n          var cbParams = cbParamsList[seriesIdx];\n          var lastCbParams = lastCbParamsList[seriesIdx];\n          if (cbParams && lastCbParams && lastCbParams.data !== cbParams.data) {\n            contentNotChanged = false;\n          }\n        });\n      });\n    });\n    this._lastDataByCoordSys = dataByCoordSys;\n    this._cbParamsList = cbParamsList;\n    return !!contentNotChanged;\n  };\n  TooltipView.prototype._hide = function (dispatchAction) {\n    // Do not directly hideLater here, because this behavior may be prevented\n    // in dispatchAction when showTip is dispatched.\n    // FIXME\n    // duplicated hideTip if manuallyHideTip is called from dispatchAction.\n    this._lastDataByCoordSys = null;\n    dispatchAction({\n      type: 'hideTip',\n      from: this.uid\n    });\n  };\n  TooltipView.prototype.dispose = function (ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    }\n    clear(this, '_updatePosition');\n    this._tooltipContent.dispose();\n    globalListener.unregister('itemTooltip', api);\n  };\n  TooltipView.type = 'tooltip';\n  return TooltipView;\n}(ComponentView);\n/**\r\n * From top to bottom. (the last one should be globalTooltipModel);\r\n */\nfunction buildTooltipModel(modelCascade, globalTooltipModel, defaultTooltipOption) {\n  // Last is always tooltip model.\n  var ecModel = globalTooltipModel.ecModel;\n  var resultModel;\n  if (defaultTooltipOption) {\n    resultModel = new Model(defaultTooltipOption, ecModel, ecModel);\n    resultModel = new Model(globalTooltipModel.option, resultModel, ecModel);\n  } else {\n    resultModel = globalTooltipModel;\n  }\n  for (var i = modelCascade.length - 1; i >= 0; i--) {\n    var tooltipOpt = modelCascade[i];\n    if (tooltipOpt) {\n      if (tooltipOpt instanceof Model) {\n        tooltipOpt = tooltipOpt.get('tooltip', true);\n      }\n      // In each data item tooltip can be simply write:\n      // {\n      //  value: 10,\n      //  tooltip: 'Something you need to know'\n      // }\n      if (isString(tooltipOpt)) {\n        tooltipOpt = {\n          formatter: tooltipOpt\n        };\n      }\n      if (tooltipOpt) {\n        resultModel = new Model(tooltipOpt, resultModel, ecModel);\n      }\n    }\n  }\n  return resultModel;\n}\nfunction makeDispatchAction(payload, api) {\n  return payload.dispatchAction || bind(api.dispatchAction, api);\n}\nfunction refixTooltipPosition(x, y, content, viewWidth, viewHeight, gapH, gapV) {\n  var size = content.getSize();\n  var width = size[0];\n  var height = size[1];\n  if (gapH != null) {\n    // Add extra 2 pixels for this case:\n    // At present the \"values\" in default tooltip are using CSS `float: right`.\n    // When the right edge of the tooltip box is on the right side of the\n    // viewport, the `float` layout might push the \"values\" to the second line.\n    if (x + width + gapH + 2 > viewWidth) {\n      x -= width + gapH;\n    } else {\n      x += gapH;\n    }\n  }\n  if (gapV != null) {\n    if (y + height + gapV > viewHeight) {\n      y -= height + gapV;\n    } else {\n      y += gapV;\n    }\n  }\n  return [x, y];\n}\nfunction confineTooltipPosition(x, y, content, viewWidth, viewHeight) {\n  var size = content.getSize();\n  var width = size[0];\n  var height = size[1];\n  x = Math.min(x + width, viewWidth) - width;\n  y = Math.min(y + height, viewHeight) - height;\n  x = Math.max(x, 0);\n  y = Math.max(y, 0);\n  return [x, y];\n}\nfunction calcTooltipPosition(position, rect, contentSize, borderWidth) {\n  var domWidth = contentSize[0];\n  var domHeight = contentSize[1];\n  var offset = Math.ceil(Math.SQRT2 * borderWidth) + 8;\n  var x = 0;\n  var y = 0;\n  var rectWidth = rect.width;\n  var rectHeight = rect.height;\n  switch (position) {\n    case 'inside':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n      break;\n    case 'top':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y - domHeight - offset;\n      break;\n    case 'bottom':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y + rectHeight + offset;\n      break;\n    case 'left':\n      x = rect.x - domWidth - offset;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n      break;\n    case 'right':\n      x = rect.x + rectWidth + offset;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n  }\n  return [x, y];\n}\nfunction isCenterAlign(align) {\n  return align === 'center' || align === 'middle';\n}\n/**\r\n * Find target component by payload like:\r\n * ```js\r\n * { legendId: 'some_id', name: 'xxx' }\r\n * { toolboxIndex: 1, name: 'xxx' }\r\n * { geoName: 'some_name', name: 'xxx' }\r\n * ```\r\n * PENDING: at present only\r\n *\r\n * If not found, return null/undefined.\r\n */\nfunction findComponentReference(payload, ecModel, api) {\n  var queryOptionMap = preParseFinder(payload).queryOptionMap;\n  var componentMainType = queryOptionMap.keys()[0];\n  if (!componentMainType || componentMainType === 'series') {\n    return;\n  }\n  var queryResult = queryReferringComponents(ecModel, componentMainType, queryOptionMap.get(componentMainType), {\n    useDefault: false,\n    enableAll: false,\n    enableNone: false\n  });\n  var model = queryResult.models[0];\n  if (!model) {\n    return;\n  }\n  var view = api.getViewOfComponentModel(model);\n  var el;\n  view.group.traverse(function (subEl) {\n    var tooltipConfig = getECData(subEl).tooltipConfig;\n    if (tooltipConfig && tooltipConfig.name === payload.name) {\n      el = subEl;\n      return true; // stop\n    }\n  });\n  if (el) {\n    return {\n      componentMainType: componentMainType,\n      componentIndex: model.componentIndex,\n      el: el\n    };\n  }\n}\nexport default TooltipView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,0BAA0B;AACnH,OAAOC,GAAG,MAAM,yBAAyB;AACzC,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,SAASC,oBAAoB,EAAEC,UAAU,EAAEC,SAAS,QAAQ,sBAAsB;AAClF,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,IAAI,QAAQ,uBAAuB;AAC5C,OAAOC,mBAAmB,MAAM,uCAAuC;AACvE,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAO,KAAKC,cAAc,MAAM,kCAAkC;AAClE,OAAO,KAAKC,UAAU,MAAM,2BAA2B;AACvD,OAAO,KAAKC,qBAAqB,MAAM,8BAA8B;AACrE,SAASC,oBAAoB,EAAEC,cAAc,EAAEC,wBAAwB,QAAQ,qBAAqB;AACpG,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,MAAM,IAAIC,UAAU,QAAQ,oBAAoB;AACzD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,oBAAoB,QAAQ,aAAa;AAClD,SAASC,4BAA4B,QAAQ,iCAAiC;AAC9E,SAASC,mBAAmB,EAAEC,kBAAkB,EAAEC,yBAAyB,QAAQ,oBAAoB;AACvG,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,KAAK,EAAEC,cAAc,QAAQ,wBAAwB;AAC9D,IAAIC,SAAS,GAAG,IAAItB,IAAI,CAAC;EACvBuB,KAAK,EAAE;IACLC,CAAC,EAAE,CAAC,CAAC;IACLC,CAAC,EAAE,CAAC,CAAC;IACLC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AACF,IAAIC,WAAW,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC/C9C,SAAS,CAAC6C,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAAA,EAAG;IACrB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,WAAW,CAACK,IAAI;IAC7B,OAAOH,KAAK;EACd;EACAF,WAAW,CAACM,SAAS,CAACC,IAAI,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAE;IACnD,IAAI5C,GAAG,CAAC6C,IAAI,IAAI,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,EAAE;MAC7B;IACF;IACA,IAAIC,YAAY,GAAGJ,OAAO,CAACK,YAAY,CAAC,SAAS,CAAC;IAClD,IAAIC,UAAU,GAAG,IAAI,CAACC,WAAW,GAAGpC,oBAAoB,CAACiC,YAAY,CAACI,GAAG,CAAC,YAAY,CAAC,CAAC;IACxF,IAAI,CAACC,eAAe,GAAGH,UAAU,KAAK,UAAU,GAAG,IAAI/C,kBAAkB,CAAC0C,GAAG,CAAC,GAAG,IAAI3C,kBAAkB,CAAC2C,GAAG,EAAE;MAC3GS,QAAQ,EAAEN,YAAY,CAACI,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,MAAM,GAAGJ,YAAY,CAACI,GAAG,CAAC,UAAU,EAAE,IAAI;IAC/F,CAAC,CAAC;EACJ,CAAC;EACDhB,WAAW,CAACM,SAAS,CAACa,MAAM,GAAG,UAAUP,YAAY,EAAEJ,OAAO,EAAEC,GAAG,EAAE;IACnE,IAAI5C,GAAG,CAAC6C,IAAI,IAAI,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,EAAE;MAC7B;IACF;IACA;IACA,IAAI,CAACS,KAAK,CAACC,SAAS,CAAC,CAAC;IACtB,IAAI,CAACC,aAAa,GAAGV,YAAY;IACjC,IAAI,CAACW,QAAQ,GAAGf,OAAO;IACvB,IAAI,CAACgB,IAAI,GAAGf,GAAG;IACf,IAAIgB,cAAc,GAAG,IAAI,CAACR,eAAe;IACzCQ,cAAc,CAACC,MAAM,CAACd,YAAY,CAAC;IACnCa,cAAc,CAACE,YAAY,CAACf,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC,CAAC;IAC1D,IAAI,CAACY,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACd,WAAW,KAAK,UAAU,IAAIH,YAAY,CAACI,GAAG,CAAC,oBAAoB,CAAC,EAAE;MAC7EvB,cAAc,CAAC,IAAI,EAAE,iBAAiB,EAAE,EAAE,EAAE,SAAS,CAAC;IACxD,CAAC,MAAM;MACLD,KAAK,CAAC,IAAI,EAAE,iBAAiB,CAAC;IAChC;EACF,CAAC;EACDQ,WAAW,CAACM,SAAS,CAACsB,mBAAmB,GAAG,YAAY;IACtD,IAAIhB,YAAY,GAAG,IAAI,CAACU,aAAa;IACrC,IAAIQ,SAAS,GAAGlB,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC;IAC7CxC,cAAc,CAACuD,QAAQ,CAAC,aAAa,EAAE,IAAI,CAACP,IAAI,EAAEpE,IAAI,CAAC,UAAU4E,WAAW,EAAEC,CAAC,EAAEC,cAAc,EAAE;MAC/F;MACA,IAAIJ,SAAS,KAAK,MAAM,EAAE;QACxB,IAAIA,SAAS,CAACK,OAAO,CAACH,WAAW,CAAC,IAAI,CAAC,EAAE;UACvC,IAAI,CAACI,QAAQ,CAACH,CAAC,EAAEC,cAAc,CAAC;QAClC,CAAC,MAAM,IAAIF,WAAW,KAAK,OAAO,EAAE;UAClC,IAAI,CAACK,KAAK,CAACH,cAAc,CAAC;QAC5B;MACF;IACF,CAAC,EAAE,IAAI,CAAC,CAAC;EACX,CAAC;EACDlC,WAAW,CAACM,SAAS,CAACuB,SAAS,GAAG,YAAY;IAC5C,IAAIjB,YAAY,GAAG,IAAI,CAACU,aAAa;IACrC,IAAId,OAAO,GAAG,IAAI,CAACe,QAAQ;IAC3B,IAAId,GAAG,GAAG,IAAI,CAACe,IAAI;IACnB,IAAIM,SAAS,GAAGlB,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC;IAC7C;IACA,IAAI,IAAI,CAACsB,MAAM,IAAI,IAAI,IAAI,IAAI,CAACC,MAAM,IAAI;IAC1C;IACA;IACA;IAAA,GACGT,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,OAAO,EAAE;MAChD,IAAIU,MAAM,GAAG,IAAI;MACjBC,YAAY,CAAC,IAAI,CAACC,qBAAqB,CAAC;MACxC,IAAI,CAACA,qBAAqB,GAAGC,UAAU,CAAC,YAAY;QAClD;QACA;QACA;QACA,CAAClC,GAAG,CAACmC,UAAU,CAAC,CAAC,IAAIJ,MAAM,CAACK,eAAe,CAACjC,YAAY,EAAEJ,OAAO,EAAEC,GAAG,EAAE;UACtEb,CAAC,EAAE4C,MAAM,CAACF,MAAM;UAChBzC,CAAC,EAAE2C,MAAM,CAACD,MAAM;UAChBO,cAAc,EAAEN,MAAM,CAACO;QACzB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE/C,WAAW,CAACM,SAAS,CAACuC,eAAe,GAAG,UAAUjC,YAAY,EAAEJ,OAAO,EAAEC,GAAG,EAAEuC,OAAO,EAAE;IACrF,IAAIA,OAAO,CAACC,IAAI,KAAK,IAAI,CAACC,GAAG,IAAIrF,GAAG,CAAC6C,IAAI,IAAI,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,EAAE;MAC1D;IACF;IACA,IAAIuB,cAAc,GAAGiB,kBAAkB,CAACH,OAAO,EAAEvC,GAAG,CAAC;IACrD;IACA,IAAI,CAAC2C,OAAO,GAAG,EAAE;IACjB;IACA,IAAIN,cAAc,GAAGE,OAAO,CAACF,cAAc;IAC3C,IAAIO,OAAO,GAAGC,sBAAsB,CAACN,OAAO,EAAExC,OAAO,EAAEC,GAAG,CAAC;IAC3D,IAAI4C,OAAO,EAAE;MACX,IAAIE,IAAI,GAAGF,OAAO,CAACG,EAAE,CAACC,eAAe,CAAC,CAAC,CAACnG,KAAK,CAAC,CAAC;MAC/CiG,IAAI,CAACG,cAAc,CAACL,OAAO,CAACG,EAAE,CAACG,SAAS,CAAC;MACzC,IAAI,CAACvB,QAAQ,CAAC;QACZwB,OAAO,EAAEL,IAAI,CAAC3D,CAAC,GAAG2D,IAAI,CAACzD,KAAK,GAAG,CAAC;QAChC+D,OAAO,EAAEN,IAAI,CAAC1D,CAAC,GAAG0D,IAAI,CAACxD,MAAM,GAAG,CAAC;QACjC+D,MAAM,EAAET,OAAO,CAACG,EAAE;QAClBO,QAAQ,EAAEf,OAAO,CAACe,QAAQ;QAC1B;QACA;QACAC,eAAe,EAAE;MACnB,CAAC,EAAE9B,cAAc,CAAC;IACpB,CAAC,MAAM,IAAIc,OAAO,CAACiB,OAAO,IAAIjB,OAAO,CAACpD,CAAC,IAAI,IAAI,IAAIoD,OAAO,CAACnD,CAAC,IAAI,IAAI,EAAE;MACpE,IAAI2D,EAAE,GAAG9D,SAAS;MAClB8D,EAAE,CAAC5D,CAAC,GAAGoD,OAAO,CAACpD,CAAC;MAChB4D,EAAE,CAAC3D,CAAC,GAAGmD,OAAO,CAACnD,CAAC;MAChB2D,EAAE,CAAC9B,MAAM,CAAC,CAAC;MACXzC,SAAS,CAACuE,EAAE,CAAC,CAACU,aAAa,GAAG;QAC5BC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAEpB,OAAO,CAACiB;MAClB,CAAC;MACD;MACA,IAAI,CAAC7B,QAAQ,CAAC;QACZwB,OAAO,EAAEZ,OAAO,CAACpD,CAAC;QAClBiE,OAAO,EAAEb,OAAO,CAACnD,CAAC;QAClBiE,MAAM,EAAEN;MACV,CAAC,EAAEtB,cAAc,CAAC;IACpB,CAAC,MAAM,IAAIY,cAAc,EAAE;MACzB,IAAI,CAACV,QAAQ,CAAC;QACZwB,OAAO,EAAEZ,OAAO,CAACpD,CAAC;QAClBiE,OAAO,EAAEb,OAAO,CAACnD,CAAC;QAClBkE,QAAQ,EAAEf,OAAO,CAACe,QAAQ;QAC1BjB,cAAc,EAAEA,cAAc;QAC9BuB,aAAa,EAAErB,OAAO,CAACqB;MACzB,CAAC,EAAEnC,cAAc,CAAC;IACpB,CAAC,MAAM,IAAIc,OAAO,CAACsB,WAAW,IAAI,IAAI,EAAE;MACtC,IAAI,IAAI,CAACC,oBAAoB,CAAC3D,YAAY,EAAEJ,OAAO,EAAEC,GAAG,EAAEuC,OAAO,CAAC,EAAE;QAClE;MACF;MACA,IAAIwB,SAAS,GAAGnG,mBAAmB,CAAC2E,OAAO,EAAExC,OAAO,CAAC;MACrD,IAAIiE,EAAE,GAAGD,SAAS,CAACE,KAAK,CAAC,CAAC,CAAC;MAC3B,IAAIC,EAAE,GAAGH,SAAS,CAACE,KAAK,CAAC,CAAC,CAAC;MAC3B,IAAID,EAAE,IAAI,IAAI,IAAIE,EAAE,IAAI,IAAI,EAAE;QAC5B,IAAI,CAACvC,QAAQ,CAAC;UACZwB,OAAO,EAAEa,EAAE;UACXZ,OAAO,EAAEc,EAAE;UACXb,MAAM,EAAEU,SAAS,CAAChB,EAAE;UACpBO,QAAQ,EAAEf,OAAO,CAACe,QAAQ;UAC1B;UACA;UACAC,eAAe,EAAE;QACnB,CAAC,EAAE9B,cAAc,CAAC;MACpB;IACF,CAAC,MAAM,IAAIc,OAAO,CAACpD,CAAC,IAAI,IAAI,IAAIoD,OAAO,CAACnD,CAAC,IAAI,IAAI,EAAE;MACjD;MACA;MACAY,GAAG,CAACyB,cAAc,CAAC;QACjB7B,IAAI,EAAE,mBAAmB;QACzBT,CAAC,EAAEoD,OAAO,CAACpD,CAAC;QACZC,CAAC,EAAEmD,OAAO,CAACnD;MACb,CAAC,CAAC;MACF,IAAI,CAACuC,QAAQ,CAAC;QACZwB,OAAO,EAAEZ,OAAO,CAACpD,CAAC;QAClBiE,OAAO,EAAEb,OAAO,CAACnD,CAAC;QAClBkE,QAAQ,EAAEf,OAAO,CAACe,QAAQ;QAC1BD,MAAM,EAAErD,GAAG,CAACmE,KAAK,CAAC,CAAC,CAACC,SAAS,CAAC7B,OAAO,CAACpD,CAAC,EAAEoD,OAAO,CAACnD,CAAC,CAAC,CAACiE;MACtD,CAAC,EAAE5B,cAAc,CAAC;IACpB;EACF,CAAC;EACDlC,WAAW,CAACM,SAAS,CAACwE,eAAe,GAAG,UAAUlE,YAAY,EAAEJ,OAAO,EAAEC,GAAG,EAAEuC,OAAO,EAAE;IACrF,IAAIvB,cAAc,GAAG,IAAI,CAACR,eAAe;IACzC,IAAI,IAAI,CAACK,aAAa,EAAE;MACtBG,cAAc,CAACsD,SAAS,CAAC,IAAI,CAACzD,aAAa,CAACN,GAAG,CAAC,WAAW,CAAC,CAAC;IAC/D;IACA,IAAI,CAACsB,MAAM,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACQ,mBAAmB,GAAG,IAAI;IAC3D,IAAIC,OAAO,CAACC,IAAI,KAAK,IAAI,CAACC,GAAG,EAAE;MAC7B,IAAI,CAACb,KAAK,CAACc,kBAAkB,CAACH,OAAO,EAAEvC,GAAG,CAAC,CAAC;IAC9C;EACF,CAAC;EACD;EACA;EACA;EACAT,WAAW,CAACM,SAAS,CAACiE,oBAAoB,GAAG,UAAU3D,YAAY,EAAEJ,OAAO,EAAEC,GAAG,EAAEuC,OAAO,EAAE;IAC1F,IAAIsB,WAAW,GAAGtB,OAAO,CAACsB,WAAW;IACrC,IAAIU,SAAS,GAAGhC,OAAO,CAACgC,SAAS;IACjC;IACA,IAAIC,gBAAgB,GAAGzE,OAAO,CAACK,YAAY,CAAC,aAAa,CAAC,CAACoE,gBAAgB;IAC3E,IAAIX,WAAW,IAAI,IAAI,IAAIU,SAAS,IAAI,IAAI,IAAIC,gBAAgB,IAAI,IAAI,EAAE;MACxE;IACF;IACA,IAAIC,WAAW,GAAG1E,OAAO,CAAC2E,gBAAgB,CAACb,WAAW,CAAC;IACvD,IAAI,CAACY,WAAW,EAAE;MAChB;IACF;IACA,IAAIE,IAAI,GAAGF,WAAW,CAACG,OAAO,CAAC,CAAC;IAChC,IAAIC,oBAAoB,GAAGC,iBAAiB,CAAC,CAACH,IAAI,CAACI,YAAY,CAACR,SAAS,CAAC,EAAEE,WAAW,EAAE,CAACA,WAAW,CAACO,gBAAgB,IAAI,CAAC,CAAC,EAAEC,KAAK,CAAC,EAAE,IAAI,CAACpE,aAAa,CAAC;IACzJ,IAAIgE,oBAAoB,CAACtE,GAAG,CAAC,SAAS,CAAC,KAAK,MAAM,EAAE;MAClD;IACF;IACAP,GAAG,CAACyB,cAAc,CAAC;MACjB7B,IAAI,EAAE,mBAAmB;MACzBiE,WAAW,EAAEA,WAAW;MACxBU,SAAS,EAAEA,SAAS;MACpBjB,QAAQ,EAAEf,OAAO,CAACe;IACpB,CAAC,CAAC;IACF,OAAO,IAAI;EACb,CAAC;EACD/D,WAAW,CAACM,SAAS,CAAC8B,QAAQ,GAAG,UAAUH,CAAC,EAAEC,cAAc,EAAE;IAC5D,IAAIsB,EAAE,GAAGvB,CAAC,CAAC6B,MAAM;IACjB,IAAIlD,YAAY,GAAG,IAAI,CAACU,aAAa;IACrC,IAAI,CAACV,YAAY,EAAE;MACjB;IACF;IACA;IACA,IAAI,CAAC0B,MAAM,GAAGL,CAAC,CAAC2B,OAAO;IACvB,IAAI,CAACrB,MAAM,GAAGN,CAAC,CAAC4B,OAAO;IACvB,IAAIf,cAAc,GAAGb,CAAC,CAACa,cAAc;IACrC,IAAIA,cAAc,IAAIA,cAAc,CAAC6C,MAAM,EAAE;MAC3C,IAAI,CAACC,gBAAgB,CAAC9C,cAAc,EAAEb,CAAC,CAAC;IAC1C,CAAC,MAAM,IAAIuB,EAAE,EAAE;MACb,IAAIqC,MAAM,GAAG5G,SAAS,CAACuE,EAAE,CAAC;MAC1B,IAAIqC,MAAM,CAACC,OAAO,KAAK,QAAQ,EAAE;QAC/B;QACA;MACF;MACA,IAAI,CAAC/C,mBAAmB,GAAG,IAAI;MAC/B,IAAIgD,kBAAkB;MACtB,IAAIC,gBAAgB;MACpBzG,mBAAmB,CAACiE,EAAE,EAAE,UAAUM,MAAM,EAAE;QACxC;QACA,IAAI7E,SAAS,CAAC6E,MAAM,CAAC,CAACkB,SAAS,IAAI,IAAI,EAAE;UACvCe,kBAAkB,GAAGjC,MAAM;UAC3B,OAAO,IAAI;QACb;QACA;QACA,IAAI7E,SAAS,CAAC6E,MAAM,CAAC,CAACI,aAAa,IAAI,IAAI,EAAE;UAC3C8B,gBAAgB,GAAGlC,MAAM;UACzB,OAAO,IAAI;QACb;MACF,CAAC,EAAE,IAAI,CAAC;MACR,IAAIiC,kBAAkB,EAAE;QACtB,IAAI,CAACE,sBAAsB,CAAChE,CAAC,EAAE8D,kBAAkB,EAAE7D,cAAc,CAAC;MACpE,CAAC,MAAM,IAAI8D,gBAAgB,EAAE;QAC3B,IAAI,CAACE,yBAAyB,CAACjE,CAAC,EAAE+D,gBAAgB,EAAE9D,cAAc,CAAC;MACrE,CAAC,MAAM;QACL,IAAI,CAACG,KAAK,CAACH,cAAc,CAAC;MAC5B;IACF,CAAC,MAAM;MACL,IAAI,CAACa,mBAAmB,GAAG,IAAI;MAC/B,IAAI,CAACV,KAAK,CAACH,cAAc,CAAC;IAC5B;EACF,CAAC;EACDlC,WAAW,CAACM,SAAS,CAAC6F,WAAW,GAAG,UAAUvF,YAAY,EAAEwF,EAAE,EAAE;IAC9D;IACA;IACA;IACA;IACA,IAAIC,KAAK,GAAGzF,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC;IACzCoF,EAAE,GAAGhJ,IAAI,CAACgJ,EAAE,EAAE,IAAI,CAAC;IACnB3D,YAAY,CAAC,IAAI,CAAC6D,WAAW,CAAC;IAC9BD,KAAK,GAAG,CAAC,GAAG,IAAI,CAACC,WAAW,GAAG3D,UAAU,CAACyD,EAAE,EAAEC,KAAK,CAAC,GAAGD,EAAE,CAAC,CAAC;EAC7D,CAAC;EACDpG,WAAW,CAACM,SAAS,CAACsF,gBAAgB,GAAG,UAAU9C,cAAc,EAAEb,CAAC,EAAE;IACpE,IAAIzB,OAAO,GAAG,IAAI,CAACe,QAAQ;IAC3B,IAAIgF,kBAAkB,GAAG,IAAI,CAACjF,aAAa;IAC3C,IAAIoD,KAAK,GAAG,CAACzC,CAAC,CAAC2B,OAAO,EAAE3B,CAAC,CAAC4B,OAAO,CAAC;IAClC,IAAI2C,kBAAkB,GAAGjB,iBAAiB,CAAC,CAACtD,CAAC,CAACoC,aAAa,CAAC,EAAEkC,kBAAkB,CAAC;IACjF,IAAIzF,UAAU,GAAG,IAAI,CAACC,WAAW;IACjC,IAAI0F,YAAY,GAAG,EAAE;IACrB,IAAIC,aAAa,GAAGtH,mBAAmB,CAAC,SAAS,EAAE;MACjDuH,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF;IACA,IAAIC,mBAAmB,GAAG,EAAE;IAC5B,IAAIC,kBAAkB,GAAG,IAAIxH,yBAAyB,CAAC,CAAC;IACxDjC,IAAI,CAACyF,cAAc,EAAE,UAAUiE,YAAY,EAAE;MAC3C1J,IAAI,CAAC0J,YAAY,CAACC,UAAU,EAAE,UAAUC,QAAQ,EAAE;QAChD,IAAIC,SAAS,GAAG1G,OAAO,CAACK,YAAY,CAACoG,QAAQ,CAACE,OAAO,GAAG,MAAM,EAAEF,QAAQ,CAACG,SAAS,CAAC;QACnF,IAAIC,SAAS,GAAGJ,QAAQ,CAACK,KAAK;QAC9B,IAAI,CAACJ,SAAS,IAAIG,SAAS,IAAI,IAAI,EAAE;UACnC;QACF;QACA,IAAIE,cAAc,GAAG7I,qBAAqB,CAAC8I,aAAa,CAACH,SAAS,EAAEH,SAAS,CAACO,IAAI,EAAEjH,OAAO,EAAEyG,QAAQ,CAACS,iBAAiB,EAAET,QAAQ,CAACU,aAAa,CAAC;QAChJ,IAAIC,iBAAiB,GAAGxI,mBAAmB,CAAC,SAAS,EAAE;UACrDyI,MAAM,EAAEN,cAAc;UACtBX,QAAQ,EAAE,CAACrJ,IAAI,CAACgK,cAAc,CAAC;UAC/BO,UAAU,EAAE,IAAI;UAChBnB,MAAM,EAAE;QACV,CAAC,CAAC;QACFD,aAAa,CAACC,MAAM,CAACoB,IAAI,CAACH,iBAAiB,CAAC;QAC5CvK,IAAI,CAAC4J,QAAQ,CAACS,iBAAiB,EAAE,UAAUM,OAAO,EAAE;UAClD,IAAIC,MAAM,GAAGzH,OAAO,CAAC2E,gBAAgB,CAAC6C,OAAO,CAAC1D,WAAW,CAAC;UAC1D,IAAIU,SAAS,GAAGgD,OAAO,CAACE,eAAe;UACvC,IAAIC,QAAQ,GAAGF,MAAM,CAACG,aAAa,CAACpD,SAAS,CAAC;UAC9C;UACA,IAAImD,QAAQ,CAACnD,SAAS,GAAG,CAAC,EAAE;YAC1B;UACF;UACAmD,QAAQ,CAAChB,OAAO,GAAGF,QAAQ,CAACE,OAAO;UACnCgB,QAAQ,CAACf,SAAS,GAAGH,QAAQ,CAACG,SAAS;UACvCe,QAAQ,CAACE,QAAQ,GAAGpB,QAAQ,CAACoB,QAAQ;UACrCF,QAAQ,CAACG,MAAM,GAAGrB,QAAQ,CAACqB,MAAM;UACjCH,QAAQ,CAACd,SAAS,GAAG5I,UAAU,CAAC8J,eAAe,CAACrB,SAAS,CAACO,IAAI,EAAE;YAC9DH,KAAK,EAAED;UACT,CAAC,CAAC;UACFc,QAAQ,CAACZ,cAAc,GAAGA,cAAc;UACxC;UACA;UACAY,QAAQ,CAACK,MAAM,GAAG1B,kBAAkB,CAAC2B,iBAAiB,CAAC,MAAM,EAAEzK,oBAAoB,CAACmK,QAAQ,CAACO,KAAK,CAAC,EAAE5H,UAAU,CAAC;UAChH,IAAI6H,mBAAmB,GAAGxJ,4BAA4B,CAAC8I,MAAM,CAACW,aAAa,CAAC5D,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;UACnG,IAAI6D,IAAI,GAAGF,mBAAmB,CAACE,IAAI;UACnC,IAAIA,IAAI,EAAE;YACR,IAAIC,cAAc,GAAGvD,iBAAiB,CAAC,CAAC0C,MAAM,CAAC,EAAE1B,kBAAkB,CAAC,CAACvF,GAAG,CAAC,gBAAgB,CAAC;YAC1F4G,iBAAiB,CAACjB,MAAM,CAACoB,IAAI,CAACe,cAAc,GAAGlL,MAAM,CAAC;cACpDkL,cAAc,EAAEA;YAClB,CAAC,EAAED,IAAI,CAAC,GAAGA,IAAI,CAAC;UAClB;UACA,IAAIF,mBAAmB,CAACI,IAAI,EAAE;YAC5BlC,mBAAmB,CAACkB,IAAI,CAACY,mBAAmB,CAACI,IAAI,CAAC;UACpD;UACAtC,YAAY,CAACsB,IAAI,CAACI,QAAQ,CAAC;QAC7B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;IACA;IACAzB,aAAa,CAACC,MAAM,CAACqC,OAAO,CAAC,CAAC;IAC9BnC,mBAAmB,CAACmC,OAAO,CAAC,CAAC;IAC7B,IAAIC,YAAY,GAAGhH,CAAC,CAAC8B,QAAQ;IAC7B,IAAImF,SAAS,GAAG1C,kBAAkB,CAACxF,GAAG,CAAC,OAAO,CAAC;IAC/C,IAAImI,eAAe,GAAG9J,kBAAkB,CAACqH,aAAa,EAAEI,kBAAkB,EAAEhG,UAAU,EAAEoI,SAAS,EAAE1I,OAAO,CAACQ,GAAG,CAAC,QAAQ,CAAC,EAAEwF,kBAAkB,CAACxF,GAAG,CAAC,WAAW,CAAC,CAAC;IAC9JmI,eAAe,IAAItC,mBAAmB,CAACuC,OAAO,CAACD,eAAe,CAAC;IAC/D,IAAIE,UAAU,GAAGvI,UAAU,KAAK,UAAU,GAAG,MAAM,GAAG,OAAO;IAC7D,IAAIwI,aAAa,GAAGzC,mBAAmB,CAAC0C,IAAI,CAACF,UAAU,CAAC;IACxD,IAAI,CAAClD,WAAW,CAACK,kBAAkB,EAAE,YAAY;MAC/C,IAAI,IAAI,CAACgD,8BAA8B,CAAC1G,cAAc,EAAE2D,YAAY,CAAC,EAAE;QACrE,IAAI,CAACgD,eAAe,CAACjD,kBAAkB,EAAEyC,YAAY,EAAEvE,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAACzD,eAAe,EAAEwF,YAAY,CAAC;MAChH,CAAC,MAAM;QACL,IAAI,CAACiD,mBAAmB,CAAClD,kBAAkB,EAAE8C,aAAa,EAAE7C,YAAY,EAAEkD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAElF,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAEuE,YAAY,EAAE,IAAI,EAAEnC,kBAAkB,CAAC;MAC3J;IACF,CAAC,CAAC;IACF;IACA;EACF,CAAC;EACD9G,WAAW,CAACM,SAAS,CAAC2F,sBAAsB,GAAG,UAAUhE,CAAC,EAAE4H,UAAU,EAAE3H,cAAc,EAAE;IACtF,IAAI1B,OAAO,GAAG,IAAI,CAACe,QAAQ;IAC3B,IAAIsE,MAAM,GAAG5G,SAAS,CAAC4K,UAAU,CAAC;IAClC;IACA;IACA;IACA,IAAIvF,WAAW,GAAGuB,MAAM,CAACvB,WAAW;IACpC,IAAIY,WAAW,GAAG1E,OAAO,CAAC2E,gBAAgB,CAACb,WAAW,CAAC;IACvD;IACA,IAAIwF,SAAS,GAAGjE,MAAM,CAACiE,SAAS,IAAI5E,WAAW;IAC/C,IAAIF,SAAS,GAAGa,MAAM,CAACb,SAAS;IAChC,IAAI+E,QAAQ,GAAGlE,MAAM,CAACkE,QAAQ;IAC9B,IAAI3E,IAAI,GAAG0E,SAAS,CAACzE,OAAO,CAAC0E,QAAQ,CAAC;IACtC,IAAIjJ,UAAU,GAAG,IAAI,CAACC,WAAW;IACjC,IAAIiD,eAAe,GAAG/B,CAAC,CAAC+B,eAAe;IACvC,IAAIpD,YAAY,GAAG2E,iBAAiB,CAAC,CAACH,IAAI,CAACI,YAAY,CAACR,SAAS,CAAC,EAAE8E,SAAS,EAAE5E,WAAW,IAAI,CAACA,WAAW,CAACO,gBAAgB,IAAI,CAAC,CAAC,EAAEC,KAAK,CAAC,EAAE,IAAI,CAACpE,aAAa,EAAE0C,eAAe,GAAG;MAC/KD,QAAQ,EAAEC;IACZ,CAAC,GAAG,IAAI,CAAC;IACT,IAAIgG,cAAc,GAAGpJ,YAAY,CAACI,GAAG,CAAC,SAAS,CAAC;IAChD,IAAIgJ,cAAc,IAAI,IAAI,IAAIA,cAAc,KAAK,MAAM,EAAE;MACvD;IACF;IACA,IAAIC,MAAM,GAAGH,SAAS,CAAC1B,aAAa,CAACpD,SAAS,EAAE+E,QAAQ,CAAC;IACzD,IAAIjD,kBAAkB,GAAG,IAAIxH,yBAAyB,CAAC,CAAC;IACxD;IACA;IACA2K,MAAM,CAACzB,MAAM,GAAG1B,kBAAkB,CAAC2B,iBAAiB,CAAC,MAAM,EAAEzK,oBAAoB,CAACiM,MAAM,CAACvB,KAAK,CAAC,EAAE5H,UAAU,CAAC;IAC5G,IAAI6H,mBAAmB,GAAGxJ,4BAA4B,CAAC2K,SAAS,CAAClB,aAAa,CAAC5D,SAAS,EAAE,KAAK,EAAE+E,QAAQ,CAAC,CAAC;IAC3G,IAAIb,SAAS,GAAGtI,YAAY,CAACI,GAAG,CAAC,OAAO,CAAC;IACzC,IAAI8H,cAAc,GAAGlI,YAAY,CAACI,GAAG,CAAC,gBAAgB,CAAC;IACvD,IAAI6H,IAAI,GAAGF,mBAAmB,CAACE,IAAI;IACnC,IAAIqB,UAAU,GAAGrB,IAAI,GAAGxJ,kBAAkB,CAACyJ,cAAc,GAAGlL,MAAM,CAAC;MACjEkL,cAAc,EAAEA;IAClB,CAAC,EAAED,IAAI,CAAC,GAAGA,IAAI,EAAE/B,kBAAkB,EAAEhG,UAAU,EAAEoI,SAAS,EAAE1I,OAAO,CAACQ,GAAG,CAAC,QAAQ,CAAC,EAAEJ,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC,CAAC,GAAG2H,mBAAmB,CAACI,IAAI;IAC5I,IAAIoB,WAAW,GAAG,OAAO,GAAGL,SAAS,CAAC3F,IAAI,GAAG,GAAG,GAAGa,SAAS;IAC5D,IAAI,CAACmB,WAAW,CAACvF,YAAY,EAAE,YAAY;MACzC,IAAI,CAAC8I,mBAAmB,CAAC9I,YAAY,EAAEsJ,UAAU,EAAED,MAAM,EAAEE,WAAW,EAAElI,CAAC,CAAC2B,OAAO,EAAE3B,CAAC,CAAC4B,OAAO,EAAE5B,CAAC,CAAC8B,QAAQ,EAAE9B,CAAC,CAAC6B,MAAM,EAAEgD,kBAAkB,CAAC;IACzI,CAAC,CAAC;IACF;IACA;IACA5E,cAAc,CAAC;MACb7B,IAAI,EAAE,SAAS;MACf6H,eAAe,EAAElD,SAAS;MAC1BA,SAAS,EAAEI,IAAI,CAACgF,WAAW,CAACpF,SAAS,CAAC;MACtCV,WAAW,EAAEA,WAAW;MACxBrB,IAAI,EAAE,IAAI,CAACC;IACb,CAAC,CAAC;EACJ,CAAC;EACDlD,WAAW,CAACM,SAAS,CAAC4F,yBAAyB,GAAG,UAAUjE,CAAC,EAAEuB,EAAE,EAAEtB,cAAc,EAAE;IACjF,IAAImI,gBAAgB,GAAG,IAAI,CAACtJ,WAAW,KAAK,MAAM;IAClD,IAAI8E,MAAM,GAAG5G,SAAS,CAACuE,EAAE,CAAC;IAC1B,IAAIU,aAAa,GAAG2B,MAAM,CAAC3B,aAAa;IACxC,IAAIoG,UAAU,GAAGpG,aAAa,CAACE,MAAM,IAAI,CAAC,CAAC;IAC3C,IAAImG,iBAAiB,GAAGD,UAAU,CAACC,iBAAiB;IACpD,IAAI/M,QAAQ,CAAC8M,UAAU,CAAC,EAAE;MACxB,IAAIE,OAAO,GAAGF,UAAU;MACxBA,UAAU,GAAG;QACXE,OAAO,EAAEA,OAAO;QAChB;QACAC,SAAS,EAAED;MACb,CAAC;MACD;MACA;MACA;MACAD,iBAAiB,GAAG,IAAI;IAC1B;IACA,IAAIA,iBAAiB,IAAIF,gBAAgB,IAAIC,UAAU,CAACE,OAAO,EAAE;MAC/D;MACAF,UAAU,GAAGhN,KAAK,CAACgN,UAAU,CAAC;MAC9BA,UAAU,CAACE,OAAO,GAAGvM,UAAU,CAACqM,UAAU,CAACE,OAAO,CAAC;IACrD;IACA,IAAIE,mBAAmB,GAAG,CAACJ,UAAU,CAAC;IACtC,IAAIK,IAAI,GAAG,IAAI,CAACpJ,QAAQ,CAACV,YAAY,CAACgF,MAAM,CAAC+E,iBAAiB,EAAE/E,MAAM,CAACgF,cAAc,CAAC;IACtF,IAAIF,IAAI,EAAE;MACRD,mBAAmB,CAAC3C,IAAI,CAAC4C,IAAI,CAAC;IAChC;IACA;IACA;IACA;IACAD,mBAAmB,CAAC3C,IAAI,CAAC;MACvB0C,SAAS,EAAEH,UAAU,CAACE;IACxB,CAAC,CAAC;IACF,IAAIxG,eAAe,GAAG/B,CAAC,CAAC+B,eAAe;IACvC,IAAI8G,eAAe,GAAGvF,iBAAiB,CAACmF,mBAAmB,EAAE,IAAI,CAACpJ,aAAa,EAAE0C,eAAe,GAAG;MACjGD,QAAQ,EAAEC;IACZ,CAAC,GAAG,IAAI,CAAC;IACT,IAAI+G,WAAW,GAAGD,eAAe,CAAC9J,GAAG,CAAC,SAAS,CAAC;IAChD,IAAImJ,WAAW,GAAGR,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;IACpC;IACA,IAAI9C,kBAAkB,GAAG,IAAIxH,yBAAyB,CAAC,CAAC;IACxD;IACA;IACA;IACA,IAAI,CAAC6G,WAAW,CAAC2E,eAAe,EAAE,YAAY;MAC5C;MACA;MACA,IAAIE,eAAe,GAAG1N,KAAK,CAACwN,eAAe,CAAC9J,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;MACzE,IAAI,CAAC0I,mBAAmB,CAACoB,eAAe,EAAEC,WAAW,EAAEC,eAAe,EAAEb,WAAW,EAAElI,CAAC,CAAC2B,OAAO,EAAE3B,CAAC,CAAC4B,OAAO,EAAE5B,CAAC,CAAC8B,QAAQ,EAAEP,EAAE,EAAEsD,kBAAkB,CAAC;IAChJ,CAAC,CAAC;IACF;IACA5E,cAAc,CAAC;MACb7B,IAAI,EAAE,SAAS;MACf4C,IAAI,EAAE,IAAI,CAACC;IACb,CAAC,CAAC;EACJ,CAAC;EACDlD,WAAW,CAACM,SAAS,CAACoJ,mBAAmB,GAAG;EAC5C;EACA;EACA9I,YAAY,EAAEmK,WAAW,EAAEd,MAAM,EAAEE,WAAW,EAAEvK,CAAC,EAAEC,CAAC,EAAEoJ,YAAY,EAAEzF,EAAE,EAAEsD,kBAAkB,EAAE;IAC1F;IACA,IAAI,CAAC1D,OAAO,GAAG,EAAE;IACjB,IAAI,CAACxC,YAAY,CAACI,GAAG,CAAC,aAAa,CAAC,IAAI,CAACJ,YAAY,CAACI,GAAG,CAAC,MAAM,CAAC,EAAE;MACjE;IACF;IACA,IAAIS,cAAc,GAAG,IAAI,CAACR,eAAe;IACzCQ,cAAc,CAACE,YAAY,CAACf,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC,CAAC;IAC1D,IAAIyJ,SAAS,GAAG7J,YAAY,CAACI,GAAG,CAAC,WAAW,CAAC;IAC7CiI,YAAY,GAAGA,YAAY,IAAIrI,YAAY,CAACI,GAAG,CAAC,UAAU,CAAC;IAC3D,IAAIiK,IAAI,GAAGF,WAAW;IACtB,IAAIG,SAAS,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAACvL,CAAC,EAAEC,CAAC,CAAC,EAAEoK,MAAM,EAAErJ,YAAY,CAACI,GAAG,CAAC,SAAS,CAAC,EAAEJ,YAAY,CAACI,GAAG,CAAC,aAAa,CAAC,CAAC;IACnH,IAAIoK,cAAc,GAAGF,SAAS,CAACxC,KAAK;IACpC,IAAI+B,SAAS,EAAE;MACb,IAAIjN,QAAQ,CAACiN,SAAS,CAAC,EAAE;QACvB,IAAIY,MAAM,GAAGzK,YAAY,CAACJ,OAAO,CAACQ,GAAG,CAAC,QAAQ,CAAC;QAC/C,IAAIsK,OAAO,GAAG5N,OAAO,CAACuM,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM;QAClD,IAAIsB,UAAU,GAAGD,OAAO,IAAIA,OAAO,CAACjD,QAAQ,IAAIiD,OAAO,CAACjD,QAAQ,CAAClG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;QACrF8I,IAAI,GAAGR,SAAS;QAChB,IAAIc,UAAU,EAAE;UACdN,IAAI,GAAGjM,UAAU,CAACsM,OAAO,CAACjE,SAAS,EAAE4D,IAAI,EAAEI,MAAM,CAAC;QACpD;QACAJ,IAAI,GAAG/M,SAAS,CAAC+M,IAAI,EAAEhB,MAAM,EAAE,IAAI,CAAC;MACtC,CAAC,MAAM,IAAIxM,UAAU,CAACgN,SAAS,CAAC,EAAE;QAChC,IAAIe,QAAQ,GAAGpO,IAAI,CAAC,UAAUqO,QAAQ,EAAER,IAAI,EAAE;UAC5C,IAAIQ,QAAQ,KAAK,IAAI,CAACrI,OAAO,EAAE;YAC7B3B,cAAc,CAACiK,UAAU,CAACT,IAAI,EAAEnE,kBAAkB,EAAElG,YAAY,EAAEwK,cAAc,EAAEnC,YAAY,CAAC;YAC/F,IAAI,CAACQ,eAAe,CAAC7I,YAAY,EAAEqI,YAAY,EAAErJ,CAAC,EAAEC,CAAC,EAAE4B,cAAc,EAAEwI,MAAM,EAAEzG,EAAE,CAAC;UACpF;QACF,CAAC,EAAE,IAAI,CAAC;QACR,IAAI,CAACJ,OAAO,GAAG+G,WAAW;QAC1Bc,IAAI,GAAGR,SAAS,CAACR,MAAM,EAAEE,WAAW,EAAEqB,QAAQ,CAAC;MACjD,CAAC,MAAM;QACLP,IAAI,GAAGR,SAAS;MAClB;IACF;IACAhJ,cAAc,CAACiK,UAAU,CAACT,IAAI,EAAEnE,kBAAkB,EAAElG,YAAY,EAAEwK,cAAc,EAAEnC,YAAY,CAAC;IAC/FxH,cAAc,CAACkK,IAAI,CAAC/K,YAAY,EAAEwK,cAAc,CAAC;IACjD,IAAI,CAAC3B,eAAe,CAAC7I,YAAY,EAAEqI,YAAY,EAAErJ,CAAC,EAAEC,CAAC,EAAE4B,cAAc,EAAEwI,MAAM,EAAEzG,EAAE,CAAC;EACpF,CAAC;EACDxD,WAAW,CAACM,SAAS,CAAC6K,gBAAgB,GAAG,UAAUzG,KAAK,EAAEkH,iBAAiB,EAAEC,OAAO,EAAEC,WAAW,EAAE;IACjG,IAAID,OAAO,KAAK,MAAM,IAAInO,OAAO,CAACkO,iBAAiB,CAAC,EAAE;MACpD,OAAO;QACLlD,KAAK,EAAEoD,WAAW,KAAK,IAAI,CAAC/K,WAAW,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;MACtE,CAAC;IACH;IACA,IAAI,CAACrD,OAAO,CAACkO,iBAAiB,CAAC,EAAE;MAC/B,OAAO;QACLlD,KAAK,EAAEoD,WAAW,IAAIF,iBAAiB,CAAClD,KAAK,IAAIkD,iBAAiB,CAACE;MACrE,CAAC;IACH;EACF,CAAC;EACD9L,WAAW,CAACM,SAAS,CAACmJ,eAAe,GAAG,UAAU7I,YAAY,EAAEqI,YAAY,EAAErJ,CAAC;EAC/E;EACAC,CAAC;EACD;EACA2K,OAAO,EAAEP,MAAM,EAAEzG,EAAE,EAAE;IACnB,IAAIuI,SAAS,GAAG,IAAI,CAACvK,IAAI,CAACwK,QAAQ,CAAC,CAAC;IACpC,IAAIC,UAAU,GAAG,IAAI,CAACzK,IAAI,CAAC0K,SAAS,CAAC,CAAC;IACtCjD,YAAY,GAAGA,YAAY,IAAIrI,YAAY,CAACI,GAAG,CAAC,UAAU,CAAC;IAC3D,IAAImL,WAAW,GAAG3B,OAAO,CAAC4B,OAAO,CAAC,CAAC;IACnC,IAAIC,KAAK,GAAGzL,YAAY,CAACI,GAAG,CAAC,OAAO,CAAC;IACrC,IAAIsL,MAAM,GAAG1L,YAAY,CAACI,GAAG,CAAC,eAAe,CAAC;IAC9C,IAAIuC,IAAI,GAAGC,EAAE,IAAIA,EAAE,CAACC,eAAe,CAAC,CAAC,CAACnG,KAAK,CAAC,CAAC;IAC7CkG,EAAE,IAAID,IAAI,CAACG,cAAc,CAACF,EAAE,CAACG,SAAS,CAAC;IACvC,IAAIlG,UAAU,CAACwL,YAAY,CAAC,EAAE;MAC5B;MACAA,YAAY,GAAGA,YAAY,CAAC,CAACrJ,CAAC,EAAEC,CAAC,CAAC,EAAEoK,MAAM,EAAEO,OAAO,CAAChH,EAAE,EAAED,IAAI,EAAE;QAC5DgJ,QAAQ,EAAE,CAACR,SAAS,EAAEE,UAAU,CAAC;QACjCE,WAAW,EAAEA,WAAW,CAACK,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ;IACA,IAAI9O,OAAO,CAACuL,YAAY,CAAC,EAAE;MACzBrJ,CAAC,GAAGzB,YAAY,CAAC8K,YAAY,CAAC,CAAC,CAAC,EAAE8C,SAAS,CAAC;MAC5ClM,CAAC,GAAG1B,YAAY,CAAC8K,YAAY,CAAC,CAAC,CAAC,EAAEgD,UAAU,CAAC;IAC/C,CAAC,MAAM,IAAItO,QAAQ,CAACsL,YAAY,CAAC,EAAE;MACjC,IAAIwD,iBAAiB,GAAGxD,YAAY;MACpCwD,iBAAiB,CAAC3M,KAAK,GAAGqM,WAAW,CAAC,CAAC,CAAC;MACxCM,iBAAiB,CAAC1M,MAAM,GAAGoM,WAAW,CAAC,CAAC,CAAC;MACzC,IAAIO,UAAU,GAAGpO,aAAa,CAACmO,iBAAiB,EAAE;QAChD3M,KAAK,EAAEiM,SAAS;QAChBhM,MAAM,EAAEkM;MACV,CAAC,CAAC;MACFrM,CAAC,GAAG8M,UAAU,CAAC9M,CAAC;MAChBC,CAAC,GAAG6M,UAAU,CAAC7M,CAAC;MAChBwM,KAAK,GAAG,IAAI;MACZ;MACA;MACAC,MAAM,GAAG,IAAI;IACf;IACA;IAAA,KACK,IAAI9O,QAAQ,CAACyL,YAAY,CAAC,IAAIzF,EAAE,EAAE;MACrC,IAAImJ,GAAG,GAAGC,mBAAmB,CAAC3D,YAAY,EAAE1F,IAAI,EAAE4I,WAAW,EAAEvL,YAAY,CAACI,GAAG,CAAC,aAAa,CAAC,CAAC;MAC/FpB,CAAC,GAAG+M,GAAG,CAAC,CAAC,CAAC;MACV9M,CAAC,GAAG8M,GAAG,CAAC,CAAC,CAAC;IACZ,CAAC,MAAM;MACL,IAAIA,GAAG,GAAGE,oBAAoB,CAACjN,CAAC,EAAEC,CAAC,EAAE2K,OAAO,EAAEuB,SAAS,EAAEE,UAAU,EAAEI,KAAK,GAAG,IAAI,GAAG,EAAE,EAAEC,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;MAC3G1M,CAAC,GAAG+M,GAAG,CAAC,CAAC,CAAC;MACV9M,CAAC,GAAG8M,GAAG,CAAC,CAAC,CAAC;IACZ;IACAN,KAAK,KAAKzM,CAAC,IAAIkN,aAAa,CAACT,KAAK,CAAC,GAAGF,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGE,KAAK,KAAK,OAAO,GAAGF,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClGG,MAAM,KAAKzM,CAAC,IAAIiN,aAAa,CAACR,MAAM,CAAC,GAAGH,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGG,MAAM,KAAK,QAAQ,GAAGH,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACtG,IAAIjN,oBAAoB,CAAC0B,YAAY,CAAC,EAAE;MACtC,IAAI+L,GAAG,GAAGI,sBAAsB,CAACnN,CAAC,EAAEC,CAAC,EAAE2K,OAAO,EAAEuB,SAAS,EAAEE,UAAU,CAAC;MACtErM,CAAC,GAAG+M,GAAG,CAAC,CAAC,CAAC;MACV9M,CAAC,GAAG8M,GAAG,CAAC,CAAC,CAAC;IACZ;IACAnC,OAAO,CAACwC,MAAM,CAACpN,CAAC,EAAEC,CAAC,CAAC;EACtB,CAAC;EACD;EACA;EACAG,WAAW,CAACM,SAAS,CAACkJ,8BAA8B,GAAG,UAAU1G,cAAc,EAAE2D,YAAY,EAAE;IAC7F,IAAIwG,YAAY,GAAG,IAAI,CAAClK,mBAAmB;IAC3C,IAAImK,gBAAgB,GAAG,IAAI,CAACC,aAAa;IACzC,IAAIC,iBAAiB,GAAG,CAAC,CAACH,YAAY,IAAIA,YAAY,CAACtH,MAAM,KAAK7C,cAAc,CAAC6C,MAAM;IACvFyH,iBAAiB,IAAI/P,IAAI,CAAC4P,YAAY,EAAE,UAAUI,gBAAgB,EAAEC,aAAa,EAAE;MACjF,IAAIC,cAAc,GAAGF,gBAAgB,CAACrG,UAAU,IAAI,EAAE;MACtD,IAAIwG,gBAAgB,GAAG1K,cAAc,CAACwK,aAAa,CAAC,IAAI,CAAC,CAAC;MAC1D,IAAIG,cAAc,GAAGD,gBAAgB,CAACxG,UAAU,IAAI,EAAE;MACtDoG,iBAAiB,GAAGA,iBAAiB,IAAIG,cAAc,CAAC5H,MAAM,KAAK8H,cAAc,CAAC9H,MAAM;MACxFyH,iBAAiB,IAAI/P,IAAI,CAACkQ,cAAc,EAAE,UAAUG,QAAQ,EAAEC,SAAS,EAAE;QACvE,IAAIC,QAAQ,GAAGH,cAAc,CAACE,SAAS,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAIE,WAAW,GAAGH,QAAQ,CAAChG,iBAAiB,IAAI,EAAE;QAClD,IAAIoG,UAAU,GAAGF,QAAQ,CAAClG,iBAAiB,IAAI,EAAE;QACjD0F,iBAAiB,GAAGA,iBAAiB,IAAIM,QAAQ,CAACpG,KAAK,KAAKsG,QAAQ,CAACtG,KAAK,IAAIoG,QAAQ,CAACrF,QAAQ,KAAKuF,QAAQ,CAACvF,QAAQ,IAAIqF,QAAQ,CAACpF,MAAM,KAAKsF,QAAQ,CAACtF,MAAM,IAAIuF,WAAW,CAAClI,MAAM,KAAKmI,UAAU,CAACnI,MAAM;QACxMyH,iBAAiB,IAAI/P,IAAI,CAACwQ,WAAW,EAAE,UAAUE,WAAW,EAAEC,CAAC,EAAE;UAC/D,IAAIC,UAAU,GAAGH,UAAU,CAACE,CAAC,CAAC;UAC9BZ,iBAAiB,GAAGA,iBAAiB,IAAIW,WAAW,CAACzJ,WAAW,KAAK2J,UAAU,CAAC3J,WAAW,IAAIyJ,WAAW,CAAC/I,SAAS,KAAKiJ,UAAU,CAACjJ,SAAS;QAC/I,CAAC,CAAC;QACF;QACAkI,gBAAgB,IAAI7P,IAAI,CAACqQ,QAAQ,CAAChG,iBAAiB,EAAE,UAAUM,OAAO,EAAE;UACtE,IAAIkG,SAAS,GAAGlG,OAAO,CAAC1D,WAAW;UACnC,IAAI6D,QAAQ,GAAG1B,YAAY,CAACyH,SAAS,CAAC;UACtC,IAAIC,YAAY,GAAGjB,gBAAgB,CAACgB,SAAS,CAAC;UAC9C,IAAI/F,QAAQ,IAAIgG,YAAY,IAAIA,YAAY,CAAC/I,IAAI,KAAK+C,QAAQ,CAAC/C,IAAI,EAAE;YACnEgI,iBAAiB,GAAG,KAAK;UAC3B;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACrK,mBAAmB,GAAGD,cAAc;IACzC,IAAI,CAACqK,aAAa,GAAG1G,YAAY;IACjC,OAAO,CAAC,CAAC2G,iBAAiB;EAC5B,CAAC;EACDpN,WAAW,CAACM,SAAS,CAAC+B,KAAK,GAAG,UAAUH,cAAc,EAAE;IACtD;IACA;IACA;IACA;IACA,IAAI,CAACa,mBAAmB,GAAG,IAAI;IAC/Bb,cAAc,CAAC;MACb7B,IAAI,EAAE,SAAS;MACf4C,IAAI,EAAE,IAAI,CAACC;IACb,CAAC,CAAC;EACJ,CAAC;EACDlD,WAAW,CAACM,SAAS,CAAC8N,OAAO,GAAG,UAAU5N,OAAO,EAAEC,GAAG,EAAE;IACtD,IAAI5C,GAAG,CAAC6C,IAAI,IAAI,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC,EAAE;MAC7B;IACF;IACAnB,KAAK,CAAC,IAAI,EAAE,iBAAiB,CAAC;IAC9B,IAAI,CAACyB,eAAe,CAACmN,OAAO,CAAC,CAAC;IAC9B5P,cAAc,CAAC6P,UAAU,CAAC,aAAa,EAAE5N,GAAG,CAAC;EAC/C,CAAC;EACDT,WAAW,CAACK,IAAI,GAAG,SAAS;EAC5B,OAAOL,WAAW;AACpB,CAAC,CAAClB,aAAa,CAAC;AAChB;AACA;AACA;AACA,SAASyG,iBAAiBA,CAAC+I,YAAY,EAAE/H,kBAAkB,EAAEgI,oBAAoB,EAAE;EACjF;EACA,IAAI/N,OAAO,GAAG+F,kBAAkB,CAAC/F,OAAO;EACxC,IAAIgO,WAAW;EACf,IAAID,oBAAoB,EAAE;IACxBC,WAAW,GAAG,IAAIjQ,KAAK,CAACgQ,oBAAoB,EAAE/N,OAAO,EAAEA,OAAO,CAAC;IAC/DgO,WAAW,GAAG,IAAIjQ,KAAK,CAACgI,kBAAkB,CAACnC,MAAM,EAAEoK,WAAW,EAAEhO,OAAO,CAAC;EAC1E,CAAC,MAAM;IACLgO,WAAW,GAAGjI,kBAAkB;EAClC;EACA,KAAK,IAAIkI,CAAC,GAAGH,YAAY,CAAC3I,MAAM,GAAG,CAAC,EAAE8I,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACjD,IAAInE,UAAU,GAAGgE,YAAY,CAACG,CAAC,CAAC;IAChC,IAAInE,UAAU,EAAE;MACd,IAAIA,UAAU,YAAY/L,KAAK,EAAE;QAC/B+L,UAAU,GAAGA,UAAU,CAACtJ,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;MAC9C;MACA;MACA;MACA;MACA;MACA;MACA,IAAIxD,QAAQ,CAAC8M,UAAU,CAAC,EAAE;QACxBA,UAAU,GAAG;UACXG,SAAS,EAAEH;QACb,CAAC;MACH;MACA,IAAIA,UAAU,EAAE;QACdkE,WAAW,GAAG,IAAIjQ,KAAK,CAAC+L,UAAU,EAAEkE,WAAW,EAAEhO,OAAO,CAAC;MAC3D;IACF;EACF;EACA,OAAOgO,WAAW;AACpB;AACA,SAASrL,kBAAkBA,CAACH,OAAO,EAAEvC,GAAG,EAAE;EACxC,OAAOuC,OAAO,CAACd,cAAc,IAAI9E,IAAI,CAACqD,GAAG,CAACyB,cAAc,EAAEzB,GAAG,CAAC;AAChE;AACA,SAASoM,oBAAoBA,CAACjN,CAAC,EAAEC,CAAC,EAAE2K,OAAO,EAAEuB,SAAS,EAAEE,UAAU,EAAEyC,IAAI,EAAEC,IAAI,EAAE;EAC9E,IAAIC,IAAI,GAAGpE,OAAO,CAAC4B,OAAO,CAAC,CAAC;EAC5B,IAAItM,KAAK,GAAG8O,IAAI,CAAC,CAAC,CAAC;EACnB,IAAI7O,MAAM,GAAG6O,IAAI,CAAC,CAAC,CAAC;EACpB,IAAIF,IAAI,IAAI,IAAI,EAAE;IAChB;IACA;IACA;IACA;IACA,IAAI9O,CAAC,GAAGE,KAAK,GAAG4O,IAAI,GAAG,CAAC,GAAG3C,SAAS,EAAE;MACpCnM,CAAC,IAAIE,KAAK,GAAG4O,IAAI;IACnB,CAAC,MAAM;MACL9O,CAAC,IAAI8O,IAAI;IACX;EACF;EACA,IAAIC,IAAI,IAAI,IAAI,EAAE;IAChB,IAAI9O,CAAC,GAAGE,MAAM,GAAG4O,IAAI,GAAG1C,UAAU,EAAE;MAClCpM,CAAC,IAAIE,MAAM,GAAG4O,IAAI;IACpB,CAAC,MAAM;MACL9O,CAAC,IAAI8O,IAAI;IACX;EACF;EACA,OAAO,CAAC/O,CAAC,EAAEC,CAAC,CAAC;AACf;AACA,SAASkN,sBAAsBA,CAACnN,CAAC,EAAEC,CAAC,EAAE2K,OAAO,EAAEuB,SAAS,EAAEE,UAAU,EAAE;EACpE,IAAI2C,IAAI,GAAGpE,OAAO,CAAC4B,OAAO,CAAC,CAAC;EAC5B,IAAItM,KAAK,GAAG8O,IAAI,CAAC,CAAC,CAAC;EACnB,IAAI7O,MAAM,GAAG6O,IAAI,CAAC,CAAC,CAAC;EACpBhP,CAAC,GAAG+J,IAAI,CAACkF,GAAG,CAACjP,CAAC,GAAGE,KAAK,EAAEiM,SAAS,CAAC,GAAGjM,KAAK;EAC1CD,CAAC,GAAG8J,IAAI,CAACkF,GAAG,CAAChP,CAAC,GAAGE,MAAM,EAAEkM,UAAU,CAAC,GAAGlM,MAAM;EAC7CH,CAAC,GAAG+J,IAAI,CAACmF,GAAG,CAAClP,CAAC,EAAE,CAAC,CAAC;EAClBC,CAAC,GAAG8J,IAAI,CAACmF,GAAG,CAACjP,CAAC,EAAE,CAAC,CAAC;EAClB,OAAO,CAACD,CAAC,EAAEC,CAAC,CAAC;AACf;AACA,SAAS+M,mBAAmBA,CAAC7I,QAAQ,EAAER,IAAI,EAAE4I,WAAW,EAAE4C,WAAW,EAAE;EACrE,IAAIC,QAAQ,GAAG7C,WAAW,CAAC,CAAC,CAAC;EAC7B,IAAI8C,SAAS,GAAG9C,WAAW,CAAC,CAAC,CAAC;EAC9B,IAAI+C,MAAM,GAAGvF,IAAI,CAACwF,IAAI,CAACxF,IAAI,CAACyF,KAAK,GAAGL,WAAW,CAAC,GAAG,CAAC;EACpD,IAAInP,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIwP,SAAS,GAAG9L,IAAI,CAACzD,KAAK;EAC1B,IAAIwP,UAAU,GAAG/L,IAAI,CAACxD,MAAM;EAC5B,QAAQgE,QAAQ;IACd,KAAK,QAAQ;MACXnE,CAAC,GAAG2D,IAAI,CAAC3D,CAAC,GAAGyP,SAAS,GAAG,CAAC,GAAGL,QAAQ,GAAG,CAAC;MACzCnP,CAAC,GAAG0D,IAAI,CAAC1D,CAAC,GAAGyP,UAAU,GAAG,CAAC,GAAGL,SAAS,GAAG,CAAC;MAC3C;IACF,KAAK,KAAK;MACRrP,CAAC,GAAG2D,IAAI,CAAC3D,CAAC,GAAGyP,SAAS,GAAG,CAAC,GAAGL,QAAQ,GAAG,CAAC;MACzCnP,CAAC,GAAG0D,IAAI,CAAC1D,CAAC,GAAGoP,SAAS,GAAGC,MAAM;MAC/B;IACF,KAAK,QAAQ;MACXtP,CAAC,GAAG2D,IAAI,CAAC3D,CAAC,GAAGyP,SAAS,GAAG,CAAC,GAAGL,QAAQ,GAAG,CAAC;MACzCnP,CAAC,GAAG0D,IAAI,CAAC1D,CAAC,GAAGyP,UAAU,GAAGJ,MAAM;MAChC;IACF,KAAK,MAAM;MACTtP,CAAC,GAAG2D,IAAI,CAAC3D,CAAC,GAAGoP,QAAQ,GAAGE,MAAM;MAC9BrP,CAAC,GAAG0D,IAAI,CAAC1D,CAAC,GAAGyP,UAAU,GAAG,CAAC,GAAGL,SAAS,GAAG,CAAC;MAC3C;IACF,KAAK,OAAO;MACVrP,CAAC,GAAG2D,IAAI,CAAC3D,CAAC,GAAGyP,SAAS,GAAGH,MAAM;MAC/BrP,CAAC,GAAG0D,IAAI,CAAC1D,CAAC,GAAGyP,UAAU,GAAG,CAAC,GAAGL,SAAS,GAAG,CAAC;EAC/C;EACA,OAAO,CAACrP,CAAC,EAAEC,CAAC,CAAC;AACf;AACA,SAASiN,aAAaA,CAACT,KAAK,EAAE;EAC5B,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,QAAQ;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS/I,sBAAsBA,CAACN,OAAO,EAAExC,OAAO,EAAEC,GAAG,EAAE;EACrD,IAAI8O,cAAc,GAAG3Q,cAAc,CAACoE,OAAO,CAAC,CAACuM,cAAc;EAC3D,IAAI3E,iBAAiB,GAAG2E,cAAc,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD,IAAI,CAAC5E,iBAAiB,IAAIA,iBAAiB,KAAK,QAAQ,EAAE;IACxD;EACF;EACA,IAAI6E,WAAW,GAAG5Q,wBAAwB,CAAC2B,OAAO,EAAEoK,iBAAiB,EAAE2E,cAAc,CAACvO,GAAG,CAAC4J,iBAAiB,CAAC,EAAE;IAC5G8E,UAAU,EAAE,KAAK;IACjBC,SAAS,EAAE,KAAK;IAChBC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,IAAIlK,KAAK,GAAG+J,WAAW,CAACI,MAAM,CAAC,CAAC,CAAC;EACjC,IAAI,CAACnK,KAAK,EAAE;IACV;EACF;EACA,IAAIoK,IAAI,GAAGrP,GAAG,CAACsP,uBAAuB,CAACrK,KAAK,CAAC;EAC7C,IAAIlC,EAAE;EACNsM,IAAI,CAAC1O,KAAK,CAAC4O,QAAQ,CAAC,UAAUC,KAAK,EAAE;IACnC,IAAI/L,aAAa,GAAGjF,SAAS,CAACgR,KAAK,CAAC,CAAC/L,aAAa;IAClD,IAAIA,aAAa,IAAIA,aAAa,CAACC,IAAI,KAAKnB,OAAO,CAACmB,IAAI,EAAE;MACxDX,EAAE,GAAGyM,KAAK;MACV,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC,CAAC;EACF,IAAIzM,EAAE,EAAE;IACN,OAAO;MACLoH,iBAAiB,EAAEA,iBAAiB;MACpCC,cAAc,EAAEnF,KAAK,CAACmF,cAAc;MACpCrH,EAAE,EAAEA;IACN,CAAC;EACH;AACF;AACA,eAAexD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}