{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genBaseStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    iconCls,\n    avatarBg,\n    avatarColor,\n    containerSize,\n    containerSizeLG,\n    containerSizeSM,\n    textFontSize,\n    textFontSizeLG,\n    textFontSizeSM,\n    borderRadius,\n    borderRadiusLG,\n    borderRadiusSM,\n    lineWidth,\n    lineType\n  } = token;\n  // Avatar size style\n  const avatarSizeStyle = (size, fontSize, radius) => ({\n    width: size,\n    height: size,\n    borderRadius: '50%',\n    [`&${componentCls}-square`]: {\n      borderRadius: radius\n    },\n    [`&${componentCls}-icon`]: {\n      fontSize,\n      [`> ${iconCls}`]: {\n        margin: 0\n      }\n    }\n  });\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      display: 'inline-flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      overflow: 'hidden',\n      color: avatarColor,\n      whiteSpace: 'nowrap',\n      textAlign: 'center',\n      verticalAlign: 'middle',\n      background: avatarBg,\n      border: `${unit(lineWidth)} ${lineType} transparent`,\n      '&-image': {\n        background: 'transparent'\n      },\n      [`${antCls}-image-img`]: {\n        display: 'block'\n      }\n    }), avatarSizeStyle(containerSize, textFontSize, borderRadius)), {\n      '&-lg': Object.assign({}, avatarSizeStyle(containerSizeLG, textFontSizeLG, borderRadiusLG)),\n      '&-sm': Object.assign({}, avatarSizeStyle(containerSizeSM, textFontSizeSM, borderRadiusSM)),\n      '> img': {\n        display: 'block',\n        width: '100%',\n        height: '100%',\n        objectFit: 'cover'\n      }\n    })\n  };\n};\nconst genGroupStyle = token => {\n  const {\n    componentCls,\n    groupBorderColor,\n    groupOverlapping,\n    groupSpace\n  } = token;\n  return {\n    [`${componentCls}-group`]: {\n      display: 'inline-flex',\n      [componentCls]: {\n        borderColor: groupBorderColor\n      },\n      '> *:not(:first-child)': {\n        marginInlineStart: groupOverlapping\n      }\n    },\n    [`${componentCls}-group-popover`]: {\n      [`${componentCls} + ${componentCls}`]: {\n        marginInlineStart: groupSpace\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM,\n    fontSize,\n    fontSizeLG,\n    fontSizeXL,\n    fontSizeHeading3,\n    marginXS,\n    marginXXS,\n    colorBorderBg\n  } = token;\n  return {\n    containerSize: controlHeight,\n    containerSizeLG: controlHeightLG,\n    containerSizeSM: controlHeightSM,\n    textFontSize: Math.round((fontSizeLG + fontSizeXL) / 2),\n    textFontSizeLG: fontSizeHeading3,\n    textFontSizeSM: fontSize,\n    groupSpace: marginXXS,\n    groupOverlapping: -marginXS,\n    groupBorderColor: colorBorderBg\n  };\n};\nexport default genStyleHooks('Avatar', token => {\n  const {\n    colorTextLightSolid,\n    colorTextPlaceholder\n  } = token;\n  const avatarToken = mergeToken(token, {\n    avatarBg: colorTextPlaceholder,\n    avatarColor: colorTextLightSolid\n  });\n  return [genBaseStyle(avatarToken), genGroupStyle(avatarToken)];\n}, prepareComponentToken);", "map": {"version": 3, "names": ["unit", "resetComponent", "genStyleHooks", "mergeToken", "genBaseStyle", "token", "antCls", "componentCls", "iconCls", "avatarBg", "avatarColor", "containerSize", "containerSizeLG", "containerSizeSM", "textFontSize", "textFontSizeLG", "textFontSizeSM", "borderRadius", "borderRadiusLG", "borderRadiusSM", "lineWidth", "lineType", "avatarSizeStyle", "size", "fontSize", "radius", "width", "height", "margin", "Object", "assign", "position", "display", "justifyContent", "alignItems", "overflow", "color", "whiteSpace", "textAlign", "verticalAlign", "background", "border", "objectFit", "genGroupStyle", "groupBorderColor", "groupOverlapping", "groupSpace", "borderColor", "marginInlineStart", "prepareComponentToken", "controlHeight", "controlHeightLG", "controlHeightSM", "fontSizeLG", "fontSizeXL", "fontSizeHeading3", "marginXS", "marginXXS", "colorBorderBg", "Math", "round", "colorTextLightSolid", "colorTextPlaceholder", "avatarToken"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/avatar/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genBaseStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    iconCls,\n    avatarBg,\n    avatarColor,\n    containerSize,\n    containerSizeLG,\n    containerSizeSM,\n    textFontSize,\n    textFontSizeLG,\n    textFontSizeSM,\n    borderRadius,\n    borderRadiusLG,\n    borderRadiusSM,\n    lineWidth,\n    lineType\n  } = token;\n  // Avatar size style\n  const avatarSizeStyle = (size, fontSize, radius) => ({\n    width: size,\n    height: size,\n    borderRadius: '50%',\n    [`&${componentCls}-square`]: {\n      borderRadius: radius\n    },\n    [`&${componentCls}-icon`]: {\n      fontSize,\n      [`> ${iconCls}`]: {\n        margin: 0\n      }\n    }\n  });\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      display: 'inline-flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      overflow: 'hidden',\n      color: avatarColor,\n      whiteSpace: 'nowrap',\n      textAlign: 'center',\n      verticalAlign: 'middle',\n      background: avatarBg,\n      border: `${unit(lineWidth)} ${lineType} transparent`,\n      '&-image': {\n        background: 'transparent'\n      },\n      [`${antCls}-image-img`]: {\n        display: 'block'\n      }\n    }), avatarSizeStyle(containerSize, textFontSize, borderRadius)), {\n      '&-lg': Object.assign({}, avatarSizeStyle(containerSizeLG, textFontSizeLG, borderRadiusLG)),\n      '&-sm': Object.assign({}, avatarSizeStyle(containerSizeSM, textFontSizeSM, borderRadiusSM)),\n      '> img': {\n        display: 'block',\n        width: '100%',\n        height: '100%',\n        objectFit: 'cover'\n      }\n    })\n  };\n};\nconst genGroupStyle = token => {\n  const {\n    componentCls,\n    groupBorderColor,\n    groupOverlapping,\n    groupSpace\n  } = token;\n  return {\n    [`${componentCls}-group`]: {\n      display: 'inline-flex',\n      [componentCls]: {\n        borderColor: groupBorderColor\n      },\n      '> *:not(:first-child)': {\n        marginInlineStart: groupOverlapping\n      }\n    },\n    [`${componentCls}-group-popover`]: {\n      [`${componentCls} + ${componentCls}`]: {\n        marginInlineStart: groupSpace\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM,\n    fontSize,\n    fontSizeLG,\n    fontSizeXL,\n    fontSizeHeading3,\n    marginXS,\n    marginXXS,\n    colorBorderBg\n  } = token;\n  return {\n    containerSize: controlHeight,\n    containerSizeLG: controlHeightLG,\n    containerSizeSM: controlHeightSM,\n    textFontSize: Math.round((fontSizeLG + fontSizeXL) / 2),\n    textFontSizeLG: fontSizeHeading3,\n    textFontSizeSM: fontSize,\n    groupSpace: marginXXS,\n    groupOverlapping: -marginXS,\n    groupBorderColor: colorBorderBg\n  };\n};\nexport default genStyleHooks('Avatar', token => {\n  const {\n    colorTextLightSolid,\n    colorTextPlaceholder\n  } = token;\n  const avatarToken = mergeToken(token, {\n    avatarBg: colorTextPlaceholder,\n    avatarColor: colorTextLightSolid\n  });\n  return [genBaseStyle(avatarToken), genGroupStyle(avatarToken)];\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,MAAM;IACNC,YAAY;IACZC,OAAO;IACPC,QAAQ;IACRC,WAAW;IACXC,aAAa;IACbC,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC,cAAc;IACdC,cAAc;IACdC,YAAY;IACZC,cAAc;IACdC,cAAc;IACdC,SAAS;IACTC;EACF,CAAC,GAAGhB,KAAK;EACT;EACA,MAAMiB,eAAe,GAAGA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,MAAM;IACnDC,KAAK,EAAEH,IAAI;IACXI,MAAM,EAAEJ,IAAI;IACZN,YAAY,EAAE,KAAK;IACnB,CAAC,IAAIV,YAAY,SAAS,GAAG;MAC3BU,YAAY,EAAEQ;IAChB,CAAC;IACD,CAAC,IAAIlB,YAAY,OAAO,GAAG;MACzBiB,QAAQ;MACR,CAAC,KAAKhB,OAAO,EAAE,GAAG;QAChBoB,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC;EACF,OAAO;IACL,CAACrB,YAAY,GAAGsB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE7B,cAAc,CAACI,KAAK,CAAC,CAAC,EAAE;MAClG0B,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,aAAa;MACtBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,KAAK,EAAE1B,WAAW;MAClB2B,UAAU,EAAE,QAAQ;MACpBC,SAAS,EAAE,QAAQ;MACnBC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE/B,QAAQ;MACpBgC,MAAM,EAAE,GAAGzC,IAAI,CAACoB,SAAS,CAAC,IAAIC,QAAQ,cAAc;MACpD,SAAS,EAAE;QACTmB,UAAU,EAAE;MACd,CAAC;MACD,CAAC,GAAGlC,MAAM,YAAY,GAAG;QACvB0B,OAAO,EAAE;MACX;IACF,CAAC,CAAC,EAAEV,eAAe,CAACX,aAAa,EAAEG,YAAY,EAAEG,YAAY,CAAC,CAAC,EAAE;MAC/D,MAAM,EAAEY,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,eAAe,CAACV,eAAe,EAAEG,cAAc,EAAEG,cAAc,CAAC,CAAC;MAC3F,MAAM,EAAEW,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,eAAe,CAACT,eAAe,EAAEG,cAAc,EAAEG,cAAc,CAAC,CAAC;MAC3F,OAAO,EAAE;QACPa,OAAO,EAAE,OAAO;QAChBN,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACde,SAAS,EAAE;MACb;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD,MAAMC,aAAa,GAAGtC,KAAK,IAAI;EAC7B,MAAM;IACJE,YAAY;IACZqC,gBAAgB;IAChBC,gBAAgB;IAChBC;EACF,CAAC,GAAGzC,KAAK;EACT,OAAO;IACL,CAAC,GAAGE,YAAY,QAAQ,GAAG;MACzByB,OAAO,EAAE,aAAa;MACtB,CAACzB,YAAY,GAAG;QACdwC,WAAW,EAAEH;MACf,CAAC;MACD,uBAAuB,EAAE;QACvBI,iBAAiB,EAAEH;MACrB;IACF,CAAC;IACD,CAAC,GAAGtC,YAAY,gBAAgB,GAAG;MACjC,CAAC,GAAGA,YAAY,MAAMA,YAAY,EAAE,GAAG;QACrCyC,iBAAiB,EAAEF;MACrB;IACF;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMG,qBAAqB,GAAG5C,KAAK,IAAI;EAC5C,MAAM;IACJ6C,aAAa;IACbC,eAAe;IACfC,eAAe;IACf5B,QAAQ;IACR6B,UAAU;IACVC,UAAU;IACVC,gBAAgB;IAChBC,QAAQ;IACRC,SAAS;IACTC;EACF,CAAC,GAAGrD,KAAK;EACT,OAAO;IACLM,aAAa,EAAEuC,aAAa;IAC5BtC,eAAe,EAAEuC,eAAe;IAChCtC,eAAe,EAAEuC,eAAe;IAChCtC,YAAY,EAAE6C,IAAI,CAACC,KAAK,CAAC,CAACP,UAAU,GAAGC,UAAU,IAAI,CAAC,CAAC;IACvDvC,cAAc,EAAEwC,gBAAgB;IAChCvC,cAAc,EAAEQ,QAAQ;IACxBsB,UAAU,EAAEW,SAAS;IACrBZ,gBAAgB,EAAE,CAACW,QAAQ;IAC3BZ,gBAAgB,EAAEc;EACpB,CAAC;AACH,CAAC;AACD,eAAexD,aAAa,CAAC,QAAQ,EAAEG,KAAK,IAAI;EAC9C,MAAM;IACJwD,mBAAmB;IACnBC;EACF,CAAC,GAAGzD,KAAK;EACT,MAAM0D,WAAW,GAAG5D,UAAU,CAACE,KAAK,EAAE;IACpCI,QAAQ,EAAEqD,oBAAoB;IAC9BpD,WAAW,EAAEmD;EACf,CAAC,CAAC;EACF,OAAO,CAACzD,YAAY,CAAC2D,WAAW,CAAC,EAAEpB,aAAa,CAACoB,WAAW,CAAC,CAAC;AAChE,CAAC,EAAEd,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}