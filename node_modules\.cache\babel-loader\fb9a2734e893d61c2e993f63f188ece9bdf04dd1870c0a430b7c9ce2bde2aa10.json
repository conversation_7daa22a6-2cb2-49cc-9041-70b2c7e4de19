{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\RealTimeTraffic.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { PieChart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\nimport vehiclesData from '../data/vehicles.json';\n\n// 注册必要的echarts组件\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\necharts.use([<PERSON><PERSON><PERSON>, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\nconst StyledCanvas = styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\n_c = PageContainer;\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 24px' : props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 0' : props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\n_c2 = MainContent;\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\n_c3 = InfoCard;\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n  \n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n_c4 = CompactStatistic;\nconst RealTimeTraffic = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [vehicles, setVehicles] = useState([]);\n  const [events, setEvents] = useState([]);\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\n  const [stats, setStats] = useState({\n    totalVehicles: 0,\n    onlineVehicles: 0,\n    offlineVehicles: 0,\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0\n  });\n\n  // 存储在线车辆的 BSM ID\n  const [onlineBsmIds, setOnlineBsmIds] = useState(new Set());\n  // 存储最后一次收到 BSM 消息的时间\n  const lastBsmTime = useRef({});\n  const eventChartRef = useRef(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n  const [eventStats, setEventStats] = useState({\n    '401': 0,\n    // 道路抛洒物\n    '404': 0,\n    // 道路障碍物\n    '405': 0,\n    // 行人通过马路\n    '904': 0,\n    // 逆行车辆\n    '910': 0,\n    // 违停车辆\n    '1002': 0,\n    // 道路施工\n    '901': 0 // 车辆超速\n  });\n\n  // 获取车辆数据\n  const fetchVehicles = () => {\n    try {\n      // 从本地 JSON 文件获取车辆数据\n      const vehiclesList = vehiclesData.vehicles || [];\n\n      // 更新车辆数据，并根据 BSM ID 是否在线来设置状态\n      const updatedVehicles = vehiclesList.map(vehicle => ({\n        ...vehicle,\n        plate: vehicle.plateNumber,\n        // 适配表格显示\n        status: onlineBsmIds.has(vehicle.bsmId) ? 'online' : 'offline',\n        speed: 0,\n        // 初始速度设为 0，后续通过 BSM 消息更新\n        lat: 0,\n        // 初始位置设为 0，后续通过 BSM 消息更新\n        lng: 0,\n        // 初始位置设为 0，后续通过 BSM 消息更新\n        heading: 0 // 初始航向角设为 0，后续通过 BSM 消息更新\n      }));\n      setVehicles(updatedVehicles);\n\n      // 更新车辆统计数据\n      setStats(prevStats => ({\n        ...prevStats,\n        totalVehicles: updatedVehicles.length,\n        onlineVehicles: updatedVehicles.filter(v => v.status === 'online').length,\n        offlineVehicles: updatedVehicles.filter(v => v.status === 'offline').length\n      }));\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n    }\n  };\n\n  // 获取设备统计数据\n  const fetchDeviceStats = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      if (response.data && response.data.success) {\n        const devicesData = response.data.data;\n\n        // 更新设备统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalDevices: devicesData.length,\n          onlineDevices: devicesData.filter(d => d.status === 'online').length,\n          offlineDevices: devicesData.filter(d => d.status === 'offline').length\n        }));\n      }\n    } catch (error) {\n      console.error('获取设备统计数据失败:', error);\n    }\n  };\n\n  // 监听 BSM 消息\n  useEffect(() => {\n    const handleBsmMessage = event => {\n      if (event.data && event.data.type === 'bsm') {\n        const bsmId = event.data.bsmId;\n        const now = Date.now();\n\n        // 更新最后接收时间\n        lastBsmTime.current[bsmId] = now;\n        setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n\n        // 更新车辆状态和位置信息\n        setVehicles(prevVehicles => prevVehicles.map(vehicle => vehicle.bsmId === bsmId ? {\n          ...vehicle,\n          status: 'online',\n          speed: event.data.speed || 0,\n          lat: event.data.lat || 0,\n          lng: event.data.lng || 0,\n          heading: event.data.heading || 0\n        } : vehicle));\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleBsmMessage);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleBsmMessage);\n    };\n  }, []);\n\n  // 定期检查在线状态\n  useEffect(() => {\n    const checkOnlineStatus = () => {\n      const now = Date.now();\n      // 如果超过30秒没有收到BSM消息，则认为车辆离线\n      setOnlineBsmIds(prev => {\n        const newOnlineBsmIds = new Set(prev);\n        let hasChanges = false;\n        newOnlineBsmIds.forEach(bsmId => {\n          if (now - (lastBsmTime.current[bsmId] || 0) > 30000) {\n            newOnlineBsmIds.delete(bsmId);\n            hasChanges = true;\n          }\n        });\n        if (hasChanges) {\n          // 更新车辆状态\n          setVehicles(prevVehicles => prevVehicles.map(vehicle => ({\n            ...vehicle,\n            status: newOnlineBsmIds.has(vehicle.bsmId) ? 'online' : 'offline'\n          })));\n        }\n        return newOnlineBsmIds;\n      });\n    };\n    const interval = setInterval(checkOnlineStatus, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // 在组件挂载时获取数据\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      fetchVehicles();\n      await fetchDeviceStats();\n      setLoading(false);\n    };\n    loadData();\n    // 每30秒更新一次数据\n    const interval = setInterval(loadData, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // 修改图表初始化代码\n  useEffect(() => {\n    let chart = null;\n    let handleResize = null;\n\n    // 确保 DOM 元素存在\n    if (!eventChartRef.current) {\n      console.error('图表容器未找到');\n      return;\n    }\n\n    // 检查并清理已存在的图表实例\n    let existingChart = echarts.getInstanceByDom(eventChartRef.current);\n    if (existingChart) {\n      existingChart.dispose();\n    }\n    try {\n      // 初始化新的图表实例\n      chart = echarts.init(eventChartRef.current);\n\n      // 设置图表配置\n      const updateChart = () => {\n        const option = {\n          title: {\n            text: '事件类型分布',\n            left: 'center',\n            top: 0,\n            textStyle: {\n              fontSize: 14\n            }\n          },\n          tooltip: {\n            trigger: 'item',\n            formatter: '{a} <br/>{b}: {c} ({d}%)'\n          },\n          legend: {\n            orient: 'horizontal',\n            bottom: 0,\n            left: 'center',\n            itemWidth: 12,\n            itemHeight: 12,\n            textStyle: {\n              fontSize: 10\n            }\n          },\n          series: [{\n            name: '事件类型',\n            type: 'pie',\n            radius: ['30%', '60%'],\n            center: ['50%', '50%'],\n            avoidLabelOverlap: true,\n            itemStyle: {\n              borderRadius: 4,\n              borderColor: '#fff',\n              borderWidth: 1\n            },\n            label: {\n              show: true,\n              position: 'outside',\n              formatter: '{b}\\n{c}次',\n              fontSize: 10,\n              color: '#666'\n            },\n            labelLine: {\n              show: true,\n              length: 10,\n              length2: 10,\n              smooth: true\n            },\n            data: [{\n              value: eventStats['401'],\n              name: '道路抛洒物',\n              itemStyle: {\n                color: '#ff4d4f'\n              }\n            }, {\n              value: eventStats['404'],\n              name: '道路障碍物',\n              itemStyle: {\n                color: '#faad14'\n              }\n            }, {\n              value: eventStats['405'],\n              name: '行人通过马路',\n              itemStyle: {\n                color: '#1890ff'\n              }\n            }, {\n              value: eventStats['904'],\n              name: '逆行车辆',\n              itemStyle: {\n                color: '#f5222d'\n              }\n            }, {\n              value: eventStats['910'],\n              name: '违停车辆',\n              itemStyle: {\n                color: '#722ed1'\n              }\n            }, {\n              value: eventStats['1002'],\n              name: '道路施工',\n              itemStyle: {\n                color: '#fa8c16'\n              }\n            }, {\n              value: eventStats['901'],\n              name: '车辆超速',\n              itemStyle: {\n                color: '#eb2f96'\n              }\n            }].filter(item => item.value > 0) // 只显示有数据的事件类型\n          }]\n        };\n        chart.setOption(option);\n      };\n\n      // 初始更新\n      updateChart();\n\n      // 监听事件统计变化\n      const statsInterval = setInterval(updateChart, 1000);\n\n      // 监听窗口大小变化\n      handleResize = () => {\n        var _chart;\n        (_chart = chart) === null || _chart === void 0 ? void 0 : _chart.resize();\n      };\n      window.addEventListener('resize', handleResize);\n\n      // 清理函数\n      return () => {\n        clearInterval(statsInterval);\n        if (handleResize) {\n          window.removeEventListener('resize', handleResize);\n        }\n        if (chart) {\n          chart.dispose();\n        }\n      };\n    } catch (error) {\n      console.error('初始化图表失败:', error);\n    }\n  }, [eventStats]);\n\n  // 修改 RSI 消息处理逻辑\n  useEffect(() => {\n    const handleRsiMessage = event => {\n      try {\n        if (event.data && event.data.type === 'RSI') {\n          console.log('RealTimeTraffic 收到 RSI 消息:', event.data);\n          const rsiData = event.data.data;\n          if (!rsiData || !rsiData.rtes) {\n            console.warn('RSI 消息数据格式不正确:', rsiData);\n            return;\n          }\n          const events = rsiData.rtes;\n          const latitude = parseFloat(rsiData.posLat);\n          const longitude = parseFloat(rsiData.posLong);\n          events.forEach(event => {\n            const eventType = event.eventType;\n            const description = event.description;\n            const time = new Date().toLocaleTimeString();\n\n            // 获取事件类型的中文描述\n            let eventTypeText = '';\n            let eventColor = '';\n            switch (eventType) {\n              case '401':\n                eventTypeText = '道路抛洒物';\n                eventColor = '#ff4d4f';\n                break;\n              case '404':\n                eventTypeText = '道路障碍物';\n                eventColor = '#faad14';\n                break;\n              case '405':\n                eventTypeText = '行人通过马路';\n                eventColor = '#1890ff';\n                break;\n              case '904':\n                eventTypeText = '逆行车辆';\n                eventColor = '#f5222d';\n                break;\n              case '910':\n                eventTypeText = '违停车辆';\n                eventColor = '#722ed1';\n                break;\n              case '1002':\n                eventTypeText = '道路施工';\n                eventColor = '#fa8c16';\n                break;\n              case '901':\n                eventTypeText = '车辆超速';\n                eventColor = '#eb2f96';\n                break;\n              default:\n                eventTypeText = description || '未知事件';\n                eventColor = '#8c8c8c';\n            }\n\n            // 更新事件列表\n            const newEvent = {\n              key: Date.now() + Math.random(),\n              type: eventTypeText,\n              time: time,\n              vehicle: rsiData.rsuId || '未知设备',\n              color: eventColor,\n              eventType: eventType,\n              location: {\n                latitude: latitude,\n                longitude: longitude\n              }\n            };\n            setEvents(prev => {\n              const newEvents = [newEvent, ...prev].slice(0, 10);\n              console.log('更新后的事件列表:', newEvents);\n              return newEvents;\n            });\n\n            // 更新事件统计\n            if (eventType) {\n              setEventStats(prev => {\n                const newStats = {\n                  ...prev,\n                  [eventType]: (prev[eventType] || 0) + 1\n                };\n                console.log('更新后的事件统计:', newStats);\n                return newStats;\n              });\n            }\n          });\n        }\n      } catch (error) {\n        console.error('处理 RSI 消息时出错:', error);\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleRsiMessage);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleRsiMessage);\n    };\n  }, []);\n\n  // 处理车辆选择\n  const handleVehicleSelect = vehicle => {\n    setSelectedVehicle(vehicle);\n  };\n\n  // 车辆列表列定义\n  const vehicleColumns = [{\n    title: '车牌号',\n    dataIndex: 'plate',\n    key: 'plate',\n    width: '40%'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: '30%',\n    render: status => /*#__PURE__*/_jsxDEV(Badge, {\n      status: status === 'online' ? 'success' : 'error',\n      text: status === 'online' ? '在线' : '离线'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '速度',\n    dataIndex: 'speed',\n    key: 'speed',\n    width: '30%',\n    render: speed => `${speed} km/h`\n  }];\n\n  // 修改实时事件列表的渲染\n  const renderEventList = () => /*#__PURE__*/_jsxDEV(List, {\n    size: \"small\",\n    dataSource: events,\n    renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n      style: {\n        padding: '8px 0'\n      },\n      children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n        title: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '13px',\n            marginBottom: '4px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: item.color,\n              marginRight: '8px'\n            },\n            children: item.type\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#666',\n              fontSize: '12px'\n            },\n            children: item.time\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 15\n        }, this),\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#666'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u8BBE\\u5907: \", item.vehicle]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 555,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u4F4D\\u7F6E: \", item.location ? `${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}` : '未知位置']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 541,\n      columnNumber: 9\n    }, this),\n    style: {\n      maxHeight: 'calc(100% - 24px)',\n      overflowY: 'auto'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 537,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    spinning: loading,\n    tip: \"\\u52A0\\u8F7D\\u4E2D...\",\n    children: /*#__PURE__*/_jsxDEV(PageContainer, {\n      children: [/*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"left\",\n        collapsed: leftCollapsed,\n        onCollapse: () => setLeftCollapsed(!leftCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u548C\\u8BBE\\u5907\\u603B\\u6570\\u4FE1\\u606F\",\n          bordered: false,\n          height: \"150px\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [8, 8],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u8F66\\u8F86\\u603B\\u6570\",\n                value: stats.totalVehicles,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 585,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u5728\\u7EBF\\u8F66\\u8F86\",\n                value: stats.onlineVehicles,\n                suffix: `/ ${stats.totalVehicles}`,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u79BB\\u7EBF\\u8F66\\u8F86\",\n                value: stats.offlineVehicles,\n                suffix: `/ ${stats.totalVehicles}`,\n                valueStyle: {\n                  color: '#cf1322'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u8BBE\\u5907\\u603B\\u6570\",\n                value: stats.totalDevices,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u5728\\u7EBF\\u8BBE\\u5907\",\n                value: stats.onlineDevices,\n                suffix: `/ ${stats.totalDevices}`,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u79BB\\u7EBF\\u8BBE\\u5907\",\n                value: stats.offlineDevices,\n                suffix: `/ ${stats.totalDevices}`,\n                valueStyle: {\n                  color: '#cf1322'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u5217\\u8868\",\n          bordered: false,\n          height: \"calc(50% - 81px)\",\n          children: renderEventList()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u7EDF\\u8BA1\",\n          bordered: false,\n          height: \"calc(50% - 81px)\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: eventChartRef,\n            style: {\n              height: '100%',\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        leftCollapsed: leftCollapsed,\n        rightCollapsed: rightCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 645,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"right\",\n        collapsed: rightCollapsed,\n        onCollapse: () => setRightCollapsed(!rightCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u5217\\u8868\",\n          bordered: false,\n          height: \"50%\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            dataSource: vehicles,\n            columns: vehicleColumns,\n            rowKey: \"id\",\n            pagination: false,\n            size: \"small\",\n            scroll: {\n              y: 180\n            },\n            onRow: record => ({\n              onClick: () => handleVehicleSelect(record),\n              style: {\n                cursor: 'pointer',\n                background: (selectedVehicle === null || selectedVehicle === void 0 ? void 0 : selectedVehicle.id) === record.id ? '#e6f7ff' : 'transparent',\n                fontSize: '13px',\n                padding: '4px 8px'\n              }\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u8BE6\\u7EC6\\u4FE1\\u606F\",\n          bordered: false,\n          height: \"50%\",\n          children: selectedVehicle ? /*#__PURE__*/_jsxDEV(Descriptions, {\n            bordered: true,\n            column: 1,\n            size: \"small\",\n            styles: {\n              label: {\n                fontSize: '13px',\n                padding: '4px 8px'\n              },\n              content: {\n                fontSize: '13px',\n                padding: '4px 8px'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8F66\\u724C\\u53F7\",\n              children: selectedVehicle.plateNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                status: selectedVehicle.status === 'online' ? 'success' : 'error',\n                text: selectedVehicle.status === 'online' ? '在线' : '离线'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7ECF\\u5EA6\",\n              children: selectedVehicle.lng.toFixed(7)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7EAC\\u5EA6\",\n              children: selectedVehicle.lat.toFixed(7)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u901F\\u5EA6\",\n              children: [selectedVehicle.speed, \" km/h\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u822A\\u5411\\u89D2\",\n              children: [selectedVehicle.heading, \"\\xB0\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '13px'\n            },\n            children: \"\\u8BF7\\u9009\\u62E9\\u8F66\\u8F86\\u67E5\\u770B\\u8BE6\\u7EC6\\u4FE1\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 701,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 650,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 574,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 573,\n    columnNumber: 5\n  }, this);\n};\n_s(RealTimeTraffic, \"te1ekI66dK3GgDc4sPkfr5OVp3M=\");\n_c5 = RealTimeTraffic;\nexport default RealTimeTraffic;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"InfoCard\");\n$RefreshReg$(_c4, \"CompactStatistic\");\n$RefreshReg$(_c5, \"RealTimeTraffic\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Row", "Col", "Card", "Statistic", "List", "Table", "Descriptions", "Spin", "Badge", "echarts", "<PERSON><PERSON><PERSON>", "GridComponent", "TooltipComponent", "LegendComponent", "TitleComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "CollapsibleSidebar", "axios", "vehiclesData", "jsxDEV", "_jsxDEV", "use", "StyledCanvas", "div", "<PERSON><PERSON><PERSON><PERSON>", "_c", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "_c2", "InfoCard", "height", "_c3", "CompactStatistic", "_c4", "RealTimeTraffic", "_s", "loading", "setLoading", "vehicles", "setVehicles", "events", "setEvents", "selectedVehicle", "setSelectedVehicle", "stats", "setStats", "totalVehicles", "onlineVehicles", "offlineVehicles", "totalDevices", "onlineDevices", "offlineDevices", "onlineBsmIds", "setOnlineBsmIds", "Set", "lastBsmTime", "eventChartRef", "setLeftCollapsed", "setRightCollapsed", "eventStats", "setEventStats", "fetchVehicles", "vehiclesList", "updatedVehicles", "map", "vehicle", "plate", "plateNumber", "status", "has", "bsmId", "speed", "lat", "lng", "heading", "prevStats", "length", "filter", "v", "error", "console", "fetchDeviceStats", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "data", "success", "devicesData", "d", "handleBsmMessage", "event", "type", "now", "Date", "current", "prev", "prevVehicles", "window", "addEventListener", "removeEventListener", "checkOnlineStatus", "newOnlineBsmIds", "has<PERSON><PERSON><PERSON>", "for<PERSON>ach", "delete", "interval", "setInterval", "clearInterval", "loadData", "chart", "handleResize", "existingChart", "getInstanceByDom", "dispose", "init", "updateChart", "option", "title", "text", "left", "top", "textStyle", "fontSize", "tooltip", "trigger", "formatter", "legend", "orient", "bottom", "itemWidth", "itemHeight", "series", "name", "radius", "center", "avoidLabelOverlap", "itemStyle", "borderRadius", "borderColor", "borderWidth", "label", "show", "position", "color", "labelLine", "length2", "smooth", "value", "item", "setOption", "statsInterval", "_chart", "resize", "handleRsiMessage", "log", "rsiData", "rtes", "warn", "latitude", "parseFloat", "posLat", "longitude", "posLong", "eventType", "description", "time", "toLocaleTimeString", "eventTypeText", "eventColor", "newEvent", "key", "Math", "random", "rsuId", "location", "newEvents", "slice", "newStats", "handleVehicleSelect", "vehicleColumns", "dataIndex", "width", "render", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderEventList", "size", "dataSource", "renderItem", "<PERSON><PERSON>", "style", "padding", "children", "Meta", "marginBottom", "marginRight", "toFixed", "maxHeight", "overflowY", "spinning", "tip", "collapsed", "onCollapse", "bordered", "gutter", "span", "valueStyle", "suffix", "ref", "columns", "<PERSON><PERSON><PERSON>", "pagination", "scroll", "y", "onRow", "record", "onClick", "cursor", "background", "id", "column", "styles", "content", "_c5", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RealTimeTraffic.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { <PERSON><PERSON>hart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\nimport vehiclesData from '../data/vehicles.json';\n\n// 注册必要的echarts组件\necharts.use([PieChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\n\nconst StyledCanvas = styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 24px' : \n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 0' : \n    props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n  \n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n\nconst RealTimeTraffic = () => {\n  const [loading, setLoading] = useState(true);\n  const [vehicles, setVehicles] = useState([]);\n  const [events, setEvents] = useState([]);\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\n  const [stats, setStats] = useState({\n    totalVehicles: 0,\n    onlineVehicles: 0,\n    offlineVehicles: 0,\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0\n  });\n  \n  // 存储在线车辆的 BSM ID\n  const [onlineBsmIds, setOnlineBsmIds] = useState(new Set());\n  // 存储最后一次收到 BSM 消息的时间\n  const lastBsmTime = useRef({});\n  \n  const eventChartRef = useRef(null);\n  \n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n  \n  const [eventStats, setEventStats] = useState({\n    '401': 0,  // 道路抛洒物\n    '404': 0,  // 道路障碍物\n    '405': 0,  // 行人通过马路\n    '904': 0,  // 逆行车辆\n    '910': 0,  // 违停车辆\n    '1002': 0, // 道路施工\n    '901': 0   // 车辆超速\n  });\n\n  // 获取车辆数据\n  const fetchVehicles = () => {\n    try {\n      // 从本地 JSON 文件获取车辆数据\n      const vehiclesList = vehiclesData.vehicles || [];\n      \n      // 更新车辆数据，并根据 BSM ID 是否在线来设置状态\n      const updatedVehicles = vehiclesList.map(vehicle => ({\n        ...vehicle,\n        plate: vehicle.plateNumber, // 适配表格显示\n        status: onlineBsmIds.has(vehicle.bsmId) ? 'online' : 'offline',\n        speed: 0, // 初始速度设为 0，后续通过 BSM 消息更新\n        lat: 0,   // 初始位置设为 0，后续通过 BSM 消息更新\n        lng: 0,   // 初始位置设为 0，后续通过 BSM 消息更新\n        heading: 0 // 初始航向角设为 0，后续通过 BSM 消息更新\n      }));\n      \n      setVehicles(updatedVehicles);\n      \n      // 更新车辆统计数据\n      setStats(prevStats => ({\n        ...prevStats,\n        totalVehicles: updatedVehicles.length,\n        onlineVehicles: updatedVehicles.filter(v => v.status === 'online').length,\n        offlineVehicles: updatedVehicles.filter(v => v.status === 'offline').length\n      }));\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n    }\n  };\n\n  // 获取设备统计数据\n  const fetchDeviceStats = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      \n      if (response.data && response.data.success) {\n        const devicesData = response.data.data;\n        \n        // 更新设备统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalDevices: devicesData.length,\n          onlineDevices: devicesData.filter(d => d.status === 'online').length,\n          offlineDevices: devicesData.filter(d => d.status === 'offline').length\n        }));\n      }\n    } catch (error) {\n      console.error('获取设备统计数据失败:', error);\n    }\n  };\n\n  // 监听 BSM 消息\n  useEffect(() => {\n    const handleBsmMessage = (event) => {\n      if (event.data && event.data.type === 'bsm') {\n        const bsmId = event.data.bsmId;\n        const now = Date.now();\n        \n        // 更新最后接收时间\n        lastBsmTime.current[bsmId] = now;\n        \n        setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n        \n        // 更新车辆状态和位置信息\n        setVehicles(prevVehicles => \n          prevVehicles.map(vehicle => \n            vehicle.bsmId === bsmId \n              ? { \n                  ...vehicle, \n                  status: 'online',\n                  speed: event.data.speed || 0,\n                  lat: event.data.lat || 0,\n                  lng: event.data.lng || 0,\n                  heading: event.data.heading || 0\n                } \n              : vehicle\n          )\n        );\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleBsmMessage);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleBsmMessage);\n    };\n  }, []);\n\n  // 定期检查在线状态\n  useEffect(() => {\n    const checkOnlineStatus = () => {\n      const now = Date.now();\n      // 如果超过30秒没有收到BSM消息，则认为车辆离线\n      setOnlineBsmIds(prev => {\n        const newOnlineBsmIds = new Set(prev);\n        let hasChanges = false;\n        newOnlineBsmIds.forEach(bsmId => {\n          if (now - (lastBsmTime.current[bsmId] || 0) > 30000) {\n            newOnlineBsmIds.delete(bsmId);\n            hasChanges = true;\n          }\n        });\n        if (hasChanges) {\n          // 更新车辆状态\n          setVehicles(prevVehicles => \n            prevVehicles.map(vehicle => ({\n              ...vehicle,\n              status: newOnlineBsmIds.has(vehicle.bsmId) ? 'online' : 'offline'\n            }))\n          );\n        }\n        return newOnlineBsmIds;\n      });\n    };\n\n    const interval = setInterval(checkOnlineStatus, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // 在组件挂载时获取数据\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      fetchVehicles();\n      await fetchDeviceStats();\n      setLoading(false);\n    };\n    \n    loadData();\n    // 每30秒更新一次数据\n    const interval = setInterval(loadData, 30000);\n    return () => clearInterval(interval);\n  }, []);\n  \n  // 修改图表初始化代码\n  useEffect(() => {\n    let chart = null;\n    let handleResize = null;\n\n    // 确保 DOM 元素存在\n    if (!eventChartRef.current) {\n      console.error('图表容器未找到');\n      return;\n    }\n\n    // 检查并清理已存在的图表实例\n    let existingChart = echarts.getInstanceByDom(eventChartRef.current);\n    if (existingChart) {\n      existingChart.dispose();\n    }\n\n    try {\n      // 初始化新的图表实例\n      chart = echarts.init(eventChartRef.current);\n      \n      // 设置图表配置\n      const updateChart = () => {\n        const option = {\n          title: {\n            text: '事件类型分布',\n            left: 'center',\n            top: 0,\n            textStyle: {\n              fontSize: 14\n            }\n          },\n          tooltip: {\n            trigger: 'item',\n            formatter: '{a} <br/>{b}: {c} ({d}%)'\n          },\n          legend: {\n            orient: 'horizontal',\n            bottom: 0,\n            left: 'center',\n            itemWidth: 12,\n            itemHeight: 12,\n            textStyle: {\n              fontSize: 10\n            }\n          },\n          series: [\n            {\n              name: '事件类型',\n              type: 'pie',\n              radius: ['30%', '60%'],\n              center: ['50%', '50%'],\n              avoidLabelOverlap: true,\n              itemStyle: {\n                borderRadius: 4,\n                borderColor: '#fff',\n                borderWidth: 1\n              },\n              label: {\n                show: true,\n                position: 'outside',\n                formatter: '{b}\\n{c}次',\n                fontSize: 10,\n                color: '#666'\n              },\n              labelLine: {\n                show: true,\n                length: 10,\n                length2: 10,\n                smooth: true\n              },\n              data: [\n                { value: eventStats['401'], name: '道路抛洒物', itemStyle: { color: '#ff4d4f' } },\n                { value: eventStats['404'], name: '道路障碍物', itemStyle: { color: '#faad14' } },\n                { value: eventStats['405'], name: '行人通过马路', itemStyle: { color: '#1890ff' } },\n                { value: eventStats['904'], name: '逆行车辆', itemStyle: { color: '#f5222d' } },\n                { value: eventStats['910'], name: '违停车辆', itemStyle: { color: '#722ed1' } },\n                { value: eventStats['1002'], name: '道路施工', itemStyle: { color: '#fa8c16' } },\n                { value: eventStats['901'], name: '车辆超速', itemStyle: { color: '#eb2f96' } }\n              ].filter(item => item.value > 0) // 只显示有数据的事件类型\n            }\n          ]\n        };\n\n        chart.setOption(option);\n      };\n      \n      // 初始更新\n      updateChart();\n      \n      // 监听事件统计变化\n      const statsInterval = setInterval(updateChart, 1000);\n      \n      // 监听窗口大小变化\n      handleResize = () => {\n        chart?.resize();\n      };\n      window.addEventListener('resize', handleResize);\n\n      // 清理函数\n      return () => {\n        clearInterval(statsInterval);\n        if (handleResize) {\n          window.removeEventListener('resize', handleResize);\n        }\n        if (chart) {\n          chart.dispose();\n        }\n      };\n\n    } catch (error) {\n      console.error('初始化图表失败:', error);\n    }\n  }, [eventStats]);\n  \n  // 修改 RSI 消息处理逻辑\n  useEffect(() => {\n    const handleRsiMessage = (event) => {\n      try {\n        if (event.data && event.data.type === 'RSI') {\n          console.log('RealTimeTraffic 收到 RSI 消息:', event.data);\n          \n          const rsiData = event.data.data;\n          if (!rsiData || !rsiData.rtes) {\n            console.warn('RSI 消息数据格式不正确:', rsiData);\n            return;\n          }\n\n          const events = rsiData.rtes;\n          const latitude = parseFloat(rsiData.posLat);\n          const longitude = parseFloat(rsiData.posLong);\n          \n          events.forEach(event => {\n            const eventType = event.eventType;\n            const description = event.description;\n            const time = new Date().toLocaleTimeString();\n            \n            // 获取事件类型的中文描述\n            let eventTypeText = '';\n            let eventColor = '';\n            switch(eventType) {\n              case '401': \n                eventTypeText = '道路抛洒物';\n                eventColor = '#ff4d4f';\n                break;\n              case '404': \n                eventTypeText = '道路障碍物';\n                eventColor = '#faad14';\n                break;\n              case '405': \n                eventTypeText = '行人通过马路';\n                eventColor = '#1890ff';\n                break;\n              case '904': \n                eventTypeText = '逆行车辆';\n                eventColor = '#f5222d';\n                break;\n              case '910': \n                eventTypeText = '违停车辆';\n                eventColor = '#722ed1';\n                break;\n              case '1002': \n                eventTypeText = '道路施工';\n                eventColor = '#fa8c16';\n                break;\n              case '901': \n                eventTypeText = '车辆超速';\n                eventColor = '#eb2f96';\n                break;\n              default: \n                eventTypeText = description || '未知事件';\n                eventColor = '#8c8c8c';\n            }\n            \n            // 更新事件列表\n            const newEvent = {\n              key: Date.now() + Math.random(),\n              type: eventTypeText,\n              time: time,\n              vehicle: rsiData.rsuId || '未知设备',\n              color: eventColor,\n              eventType: eventType,\n              location: {\n                latitude: latitude,\n                longitude: longitude\n              }\n            };\n            \n            setEvents(prev => {\n              const newEvents = [newEvent, ...prev].slice(0, 10);\n              console.log('更新后的事件列表:', newEvents);\n              return newEvents;\n            });\n            \n            // 更新事件统计\n            if (eventType) {\n              setEventStats(prev => {\n                const newStats = {\n                  ...prev,\n                  [eventType]: (prev[eventType] || 0) + 1\n                };\n                console.log('更新后的事件统计:', newStats);\n                return newStats;\n              });\n            }\n          });\n        }\n      } catch (error) {\n        console.error('处理 RSI 消息时出错:', error);\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleRsiMessage);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleRsiMessage);\n    };\n  }, []);\n  \n  // 处理车辆选择\n  const handleVehicleSelect = (vehicle) => {\n    setSelectedVehicle(vehicle);\n  };\n  \n  // 车辆列表列定义\n  const vehicleColumns = [\n    {\n      title: '车牌号',\n      dataIndex: 'plate',\n      key: 'plate',\n      width: '40%',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: '30%',\n      render: status => (\n        <Badge \n          status={status === 'online' ? 'success' : 'error'} \n          text={status === 'online' ? '在线' : '离线'} \n        />\n      ),\n    },\n    {\n      title: '速度',\n      dataIndex: 'speed',\n      key: 'speed',\n      width: '30%',\n      render: speed => `${speed} km/h`,\n    }\n  ];\n  \n  // 修改实时事件列表的渲染\n  const renderEventList = () => (\n    <List\n      size=\"small\"\n      dataSource={events}\n      renderItem={item => (\n        <List.Item style={{ padding: '8px 0' }}>\n          <List.Item.Meta\n            title={\n              <div style={{ fontSize: '13px', marginBottom: '4px' }}>\n                <span style={{ color: item.color, marginRight: '8px' }}>\n                  {item.type}\n                </span>\n                <span style={{ color: '#666', fontSize: '12px' }}>\n                  {item.time}\n                </span>\n              </div>\n            }\n            description={\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                <div>设备: {item.vehicle}</div>\n                <div>位置: {item.location ? \n                  `${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}` : \n                  '未知位置'}\n                </div>\n              </div>\n            }\n          />\n        </List.Item>\n      )}\n      style={{\n        maxHeight: 'calc(100% - 24px)',\n        overflowY: 'auto'\n      }}\n    />\n  );\n  \n  return (\n    <Spin spinning={loading} tip=\"加载中...\">\n      <PageContainer>\n        {/* 左侧信息栏 */}\n        <CollapsibleSidebar \n          position=\"left\"\n          collapsed={leftCollapsed}\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\n        >\n          {/* 车辆和设备总数信息栏 */}\n          <InfoCard title=\"车辆和设备总数信息\" bordered={false} height=\"150px\">\n            <Row gutter={[8, 8]}>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"车辆总数\" \n                  value={stats.totalVehicles} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"在线车辆\" \n                  value={stats.onlineVehicles} \n                  suffix={`/ ${stats.totalVehicles}`} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"离线车辆\" \n                  value={stats.offlineVehicles} \n                  suffix={`/ ${stats.totalVehicles}`} \n                  valueStyle={{ color: '#cf1322' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"设备总数\" \n                  value={stats.totalDevices} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"在线设备\" \n                  value={stats.onlineDevices} \n                  suffix={`/ ${stats.totalDevices}`} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"离线设备\" \n                  value={stats.offlineDevices} \n                  suffix={`/ ${stats.totalDevices}`} \n                  valueStyle={{ color: '#cf1322' }} \n                />\n              </Col>\n            </Row>\n          </InfoCard>\n          \n          {/* 实时事件列表栏 */}\n          <InfoCard title=\"实时事件列表\" bordered={false} height=\"calc(50% - 81px)\">\n            {renderEventList()}\n          </InfoCard>\n          \n          {/* 实时事件统计栏 */}\n          <InfoCard title=\"实时事件统计\" bordered={false} height=\"calc(50% - 81px)\">\n            <div ref={eventChartRef} style={{ height: '100%', width: '100%' }}></div>\n          </InfoCard>\n        </CollapsibleSidebar>\n        \n        {/* 主内容区域 */}\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\n          {/* 主要内容 */}\n        </MainContent>\n        \n        {/* 右侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"right\"\n          collapsed={rightCollapsed}\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\n        >\n          {/* 车辆列表栏 */}\n          <InfoCard title=\"车辆列表\" bordered={false} height=\"50%\">\n            <Table \n              dataSource={vehicles} \n              columns={vehicleColumns} \n              rowKey=\"id\"\n              pagination={false}\n              size=\"small\"\n              scroll={{ y: 180 }}\n              onRow={(record) => ({\n                onClick: () => handleVehicleSelect(record),\n                style: { \n                  cursor: 'pointer',\n                  background: selectedVehicle?.id === record.id ? '#e6f7ff' : 'transparent',\n                  fontSize: '13px',\n                  padding: '4px 8px'\n                }\n              })}\n            />\n          </InfoCard>\n          \n          {/* 车辆详细信息栏 */}\n          <InfoCard title=\"车辆详细信息\" bordered={false} height=\"50%\">\n            {selectedVehicle ? (\n              <Descriptions \n                bordered \n                column={1} \n                size=\"small\"\n                styles={{\n                  label: { fontSize: '13px', padding: '4px 8px' },\n                  content: { fontSize: '13px', padding: '4px 8px' }\n                }}\n              >\n                <Descriptions.Item label=\"车牌号\">{selectedVehicle.plateNumber}</Descriptions.Item>\n                <Descriptions.Item label=\"状态\">\n                  <Badge \n                    status={selectedVehicle.status === 'online' ? 'success' : 'error'} \n                    text={selectedVehicle.status === 'online' ? '在线' : '离线'} \n                  />\n                </Descriptions.Item>\n                <Descriptions.Item label=\"经度\">{selectedVehicle.lng.toFixed(7)}</Descriptions.Item>\n                <Descriptions.Item label=\"纬度\">{selectedVehicle.lat.toFixed(7)}</Descriptions.Item>\n                <Descriptions.Item label=\"速度\">{selectedVehicle.speed} km/h</Descriptions.Item>\n                <Descriptions.Item label=\"航向角\">{selectedVehicle.heading}°</Descriptions.Item>\n              </Descriptions>\n            ) : (\n              <p style={{ fontSize: '13px' }}>请选择车辆查看详细信息</p>\n            )}\n          </InfoCard>\n        </CollapsibleSidebar>\n      </PageContainer>\n    </Spin>\n  );\n};\n\nexport default RealTimeTraffic;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AACxF,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,QAAQ,oBAAoB;AACrG,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,yCAAyC;AACxE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,uBAAuB;;AAEhD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAZ,OAAO,CAACa,GAAG,CAAC,CAACZ,QAAQ,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,cAAc,CAAC,CAAC;AAEzG,MAAMQ,YAAY,GAAGP,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMC,aAAa,GAAGT,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA,CAAC;;AAED;AAAAE,EAAA,GANMD,aAAa;AAOnB,MAAME,WAAW,GAAGX,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMI,YAAY,GAAGZ,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMK,WAAW,GAAGb,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA,aAAaM,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACxEF,KAAK,CAACC,aAAa,GAAG,cAAc,GACpCD,KAAK,CAACE,cAAc,GAAG,cAAc,GAAG,OAAO;AACnD;AACA,YAAYF,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACvEF,KAAK,CAACC,aAAa,GAAG,WAAW,GACjCD,KAAK,CAACE,cAAc,GAAG,WAAW,GAAG,GAAG;AAC5C;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfMJ,WAAW;AAgBjB,MAAMK,QAAQ,GAAGlB,MAAM,CAACd,IAAI,CAAC;AAC7B;AACA,YAAY4B,KAAK,IAAIA,KAAK,CAACK,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GA1BMF,QAAQ;AA2Bd,MAAMG,gBAAgB,GAAGrB,MAAM,CAACb,SAAS,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,GAAA,GATID,gBAAgB;AAWtB,MAAME,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8C,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgD,MAAM,EAAEC,SAAS,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkD,eAAe,EAAEC,kBAAkB,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoD,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAC;IACjCsD,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,IAAI8D,GAAG,CAAC,CAAC,CAAC;EAC3D;EACA,MAAMC,WAAW,GAAG7D,MAAM,CAAC,CAAC,CAAC,CAAC;EAE9B,MAAM8D,aAAa,GAAG9D,MAAM,CAAC,IAAI,CAAC;;EAElC;EACA,MAAM,CAACgC,aAAa,EAAE+B,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmC,cAAc,EAAE+B,iBAAiB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM,CAACmE,UAAU,EAAEC,aAAa,CAAC,GAAGpE,QAAQ,CAAC;IAC3C,KAAK,EAAE,CAAC;IAAG;IACX,KAAK,EAAE,CAAC;IAAG;IACX,KAAK,EAAE,CAAC;IAAG;IACX,KAAK,EAAE,CAAC;IAAG;IACX,KAAK,EAAE,CAAC;IAAG;IACX,MAAM,EAAE,CAAC;IAAE;IACX,KAAK,EAAE,CAAC,CAAG;EACb,CAAC,CAAC;;EAEF;EACA,MAAMqE,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI;MACF;MACA,MAAMC,YAAY,GAAGhD,YAAY,CAACwB,QAAQ,IAAI,EAAE;;MAEhD;MACA,MAAMyB,eAAe,GAAGD,YAAY,CAACE,GAAG,CAACC,OAAO,KAAK;QACnD,GAAGA,OAAO;QACVC,KAAK,EAAED,OAAO,CAACE,WAAW;QAAE;QAC5BC,MAAM,EAAEhB,YAAY,CAACiB,GAAG,CAACJ,OAAO,CAACK,KAAK,CAAC,GAAG,QAAQ,GAAG,SAAS;QAC9DC,KAAK,EAAE,CAAC;QAAE;QACVC,GAAG,EAAE,CAAC;QAAI;QACVC,GAAG,EAAE,CAAC;QAAI;QACVC,OAAO,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC;MAEHnC,WAAW,CAACwB,eAAe,CAAC;;MAE5B;MACAlB,QAAQ,CAAC8B,SAAS,KAAK;QACrB,GAAGA,SAAS;QACZ7B,aAAa,EAAEiB,eAAe,CAACa,MAAM;QACrC7B,cAAc,EAAEgB,eAAe,CAACc,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACV,MAAM,KAAK,QAAQ,CAAC,CAACQ,MAAM;QACzE5B,eAAe,EAAEe,eAAe,CAACc,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACV,MAAM,KAAK,SAAS,CAAC,CAACQ;MACvE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMzE,KAAK,CAAC0E,GAAG,CAAC,GAAGL,MAAM,cAAc,CAAC;MAEzD,IAAII,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1C,MAAMC,WAAW,GAAGJ,QAAQ,CAACE,IAAI,CAACA,IAAI;;QAEtC;QACA3C,QAAQ,CAAC8B,SAAS,KAAK;UACrB,GAAGA,SAAS;UACZ1B,YAAY,EAAEyC,WAAW,CAACd,MAAM;UAChC1B,aAAa,EAAEwC,WAAW,CAACb,MAAM,CAACc,CAAC,IAAIA,CAAC,CAACvB,MAAM,KAAK,QAAQ,CAAC,CAACQ,MAAM;UACpEzB,cAAc,EAAEuC,WAAW,CAACb,MAAM,CAACc,CAAC,IAAIA,CAAC,CAACvB,MAAM,KAAK,SAAS,CAAC,CAACQ;QAClE,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;;EAED;EACAtF,SAAS,CAAC,MAAM;IACd,MAAMmG,gBAAgB,GAAIC,KAAK,IAAK;MAClC,IAAIA,KAAK,CAACL,IAAI,IAAIK,KAAK,CAACL,IAAI,CAACM,IAAI,KAAK,KAAK,EAAE;QAC3C,MAAMxB,KAAK,GAAGuB,KAAK,CAACL,IAAI,CAAClB,KAAK;QAC9B,MAAMyB,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;QAEtB;QACAxC,WAAW,CAAC0C,OAAO,CAAC3B,KAAK,CAAC,GAAGyB,GAAG;QAEhC1C,eAAe,CAAC6C,IAAI,IAAI,IAAI5C,GAAG,CAAC,CAAC,GAAG4C,IAAI,EAAE5B,KAAK,CAAC,CAAC,CAAC;;QAElD;QACA/B,WAAW,CAAC4D,YAAY,IACtBA,YAAY,CAACnC,GAAG,CAACC,OAAO,IACtBA,OAAO,CAACK,KAAK,KAAKA,KAAK,GACnB;UACE,GAAGL,OAAO;UACVG,MAAM,EAAE,QAAQ;UAChBG,KAAK,EAAEsB,KAAK,CAACL,IAAI,CAACjB,KAAK,IAAI,CAAC;UAC5BC,GAAG,EAAEqB,KAAK,CAACL,IAAI,CAAChB,GAAG,IAAI,CAAC;UACxBC,GAAG,EAAEoB,KAAK,CAACL,IAAI,CAACf,GAAG,IAAI,CAAC;UACxBC,OAAO,EAAEmB,KAAK,CAACL,IAAI,CAACd,OAAO,IAAI;QACjC,CAAC,GACDT,OACN,CACF,CAAC;MACH;IACF,CAAC;;IAED;IACAmC,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAET,gBAAgB,CAAC;;IAEpD;IACA,OAAO,MAAM;MACXQ,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEV,gBAAgB,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnG,SAAS,CAAC,MAAM;IACd,MAAM8G,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,MAAMR,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB;MACA1C,eAAe,CAAC6C,IAAI,IAAI;QACtB,MAAMM,eAAe,GAAG,IAAIlD,GAAG,CAAC4C,IAAI,CAAC;QACrC,IAAIO,UAAU,GAAG,KAAK;QACtBD,eAAe,CAACE,OAAO,CAACpC,KAAK,IAAI;UAC/B,IAAIyB,GAAG,IAAIxC,WAAW,CAAC0C,OAAO,CAAC3B,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,EAAE;YACnDkC,eAAe,CAACG,MAAM,CAACrC,KAAK,CAAC;YAC7BmC,UAAU,GAAG,IAAI;UACnB;QACF,CAAC,CAAC;QACF,IAAIA,UAAU,EAAE;UACd;UACAlE,WAAW,CAAC4D,YAAY,IACtBA,YAAY,CAACnC,GAAG,CAACC,OAAO,KAAK;YAC3B,GAAGA,OAAO;YACVG,MAAM,EAAEoC,eAAe,CAACnC,GAAG,CAACJ,OAAO,CAACK,KAAK,CAAC,GAAG,QAAQ,GAAG;UAC1D,CAAC,CAAC,CACJ,CAAC;QACH;QACA,OAAOkC,eAAe;MACxB,CAAC,CAAC;IACJ,CAAC;IAED,MAAMI,QAAQ,GAAGC,WAAW,CAACN,iBAAiB,EAAE,IAAI,CAAC;IACrD,OAAO,MAAMO,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnH,SAAS,CAAC,MAAM;IACd,MAAMsH,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B1E,UAAU,CAAC,IAAI,CAAC;MAChBwB,aAAa,CAAC,CAAC;MACf,MAAMoB,gBAAgB,CAAC,CAAC;MACxB5C,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAED0E,QAAQ,CAAC,CAAC;IACV;IACA,MAAMH,QAAQ,GAAGC,WAAW,CAACE,QAAQ,EAAE,KAAK,CAAC;IAC7C,OAAO,MAAMD,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnH,SAAS,CAAC,MAAM;IACd,IAAIuH,KAAK,GAAG,IAAI;IAChB,IAAIC,YAAY,GAAG,IAAI;;IAEvB;IACA,IAAI,CAACzD,aAAa,CAACyC,OAAO,EAAE;MAC1BjB,OAAO,CAACD,KAAK,CAAC,SAAS,CAAC;MACxB;IACF;;IAEA;IACA,IAAImC,aAAa,GAAG9G,OAAO,CAAC+G,gBAAgB,CAAC3D,aAAa,CAACyC,OAAO,CAAC;IACnE,IAAIiB,aAAa,EAAE;MACjBA,aAAa,CAACE,OAAO,CAAC,CAAC;IACzB;IAEA,IAAI;MACF;MACAJ,KAAK,GAAG5G,OAAO,CAACiH,IAAI,CAAC7D,aAAa,CAACyC,OAAO,CAAC;;MAE3C;MACA,MAAMqB,WAAW,GAAGA,CAAA,KAAM;QACxB,MAAMC,MAAM,GAAG;UACbC,KAAK,EAAE;YACLC,IAAI,EAAE,QAAQ;YACdC,IAAI,EAAE,QAAQ;YACdC,GAAG,EAAE,CAAC;YACNC,SAAS,EAAE;cACTC,QAAQ,EAAE;YACZ;UACF,CAAC;UACDC,OAAO,EAAE;YACPC,OAAO,EAAE,MAAM;YACfC,SAAS,EAAE;UACb,CAAC;UACDC,MAAM,EAAE;YACNC,MAAM,EAAE,YAAY;YACpBC,MAAM,EAAE,CAAC;YACTT,IAAI,EAAE,QAAQ;YACdU,SAAS,EAAE,EAAE;YACbC,UAAU,EAAE,EAAE;YACdT,SAAS,EAAE;cACTC,QAAQ,EAAE;YACZ;UACF,CAAC;UACDS,MAAM,EAAE,CACN;YACEC,IAAI,EAAE,MAAM;YACZzC,IAAI,EAAE,KAAK;YACX0C,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;YACtBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;YACtBC,iBAAiB,EAAE,IAAI;YACvBC,SAAS,EAAE;cACTC,YAAY,EAAE,CAAC;cACfC,WAAW,EAAE,MAAM;cACnBC,WAAW,EAAE;YACf,CAAC;YACDC,KAAK,EAAE;cACLC,IAAI,EAAE,IAAI;cACVC,QAAQ,EAAE,SAAS;cACnBjB,SAAS,EAAE,WAAW;cACtBH,QAAQ,EAAE,EAAE;cACZqB,KAAK,EAAE;YACT,CAAC;YACDC,SAAS,EAAE;cACTH,IAAI,EAAE,IAAI;cACVpE,MAAM,EAAE,EAAE;cACVwE,OAAO,EAAE,EAAE;cACXC,MAAM,EAAE;YACV,CAAC;YACD7D,IAAI,EAAE,CACJ;cAAE8D,KAAK,EAAE3F,UAAU,CAAC,KAAK,CAAC;cAAE4E,IAAI,EAAE,OAAO;cAAEI,SAAS,EAAE;gBAAEO,KAAK,EAAE;cAAU;YAAE,CAAC,EAC5E;cAAEI,KAAK,EAAE3F,UAAU,CAAC,KAAK,CAAC;cAAE4E,IAAI,EAAE,OAAO;cAAEI,SAAS,EAAE;gBAAEO,KAAK,EAAE;cAAU;YAAE,CAAC,EAC5E;cAAEI,KAAK,EAAE3F,UAAU,CAAC,KAAK,CAAC;cAAE4E,IAAI,EAAE,QAAQ;cAAEI,SAAS,EAAE;gBAAEO,KAAK,EAAE;cAAU;YAAE,CAAC,EAC7E;cAAEI,KAAK,EAAE3F,UAAU,CAAC,KAAK,CAAC;cAAE4E,IAAI,EAAE,MAAM;cAAEI,SAAS,EAAE;gBAAEO,KAAK,EAAE;cAAU;YAAE,CAAC,EAC3E;cAAEI,KAAK,EAAE3F,UAAU,CAAC,KAAK,CAAC;cAAE4E,IAAI,EAAE,MAAM;cAAEI,SAAS,EAAE;gBAAEO,KAAK,EAAE;cAAU;YAAE,CAAC,EAC3E;cAAEI,KAAK,EAAE3F,UAAU,CAAC,MAAM,CAAC;cAAE4E,IAAI,EAAE,MAAM;cAAEI,SAAS,EAAE;gBAAEO,KAAK,EAAE;cAAU;YAAE,CAAC,EAC5E;cAAEI,KAAK,EAAE3F,UAAU,CAAC,KAAK,CAAC;cAAE4E,IAAI,EAAE,MAAM;cAAEI,SAAS,EAAE;gBAAEO,KAAK,EAAE;cAAU;YAAE,CAAC,CAC5E,CAACrE,MAAM,CAAC0E,IAAI,IAAIA,IAAI,CAACD,KAAK,GAAG,CAAC,CAAC,CAAC;UACnC,CAAC;QAEL,CAAC;QAEDtC,KAAK,CAACwC,SAAS,CAACjC,MAAM,CAAC;MACzB,CAAC;;MAED;MACAD,WAAW,CAAC,CAAC;;MAEb;MACA,MAAMmC,aAAa,GAAG5C,WAAW,CAACS,WAAW,EAAE,IAAI,CAAC;;MAEpD;MACAL,YAAY,GAAGA,CAAA,KAAM;QAAA,IAAAyC,MAAA;QACnB,CAAAA,MAAA,GAAA1C,KAAK,cAAA0C,MAAA,uBAALA,MAAA,CAAOC,MAAM,CAAC,CAAC;MACjB,CAAC;MACDvD,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEY,YAAY,CAAC;;MAE/C;MACA,OAAO,MAAM;QACXH,aAAa,CAAC2C,aAAa,CAAC;QAC5B,IAAIxC,YAAY,EAAE;UAChBb,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEW,YAAY,CAAC;QACpD;QACA,IAAID,KAAK,EAAE;UACTA,KAAK,CAACI,OAAO,CAAC,CAAC;QACjB;MACF,CAAC;IAEH,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACpB,UAAU,CAAC,CAAC;;EAEhB;EACAlE,SAAS,CAAC,MAAM;IACd,MAAMmK,gBAAgB,GAAI/D,KAAK,IAAK;MAClC,IAAI;QACF,IAAIA,KAAK,CAACL,IAAI,IAAIK,KAAK,CAACL,IAAI,CAACM,IAAI,KAAK,KAAK,EAAE;UAC3Cd,OAAO,CAAC6E,GAAG,CAAC,4BAA4B,EAAEhE,KAAK,CAACL,IAAI,CAAC;UAErD,MAAMsE,OAAO,GAAGjE,KAAK,CAACL,IAAI,CAACA,IAAI;UAC/B,IAAI,CAACsE,OAAO,IAAI,CAACA,OAAO,CAACC,IAAI,EAAE;YAC7B/E,OAAO,CAACgF,IAAI,CAAC,gBAAgB,EAAEF,OAAO,CAAC;YACvC;UACF;UAEA,MAAMtH,MAAM,GAAGsH,OAAO,CAACC,IAAI;UAC3B,MAAME,QAAQ,GAAGC,UAAU,CAACJ,OAAO,CAACK,MAAM,CAAC;UAC3C,MAAMC,SAAS,GAAGF,UAAU,CAACJ,OAAO,CAACO,OAAO,CAAC;UAE7C7H,MAAM,CAACkE,OAAO,CAACb,KAAK,IAAI;YACtB,MAAMyE,SAAS,GAAGzE,KAAK,CAACyE,SAAS;YACjC,MAAMC,WAAW,GAAG1E,KAAK,CAAC0E,WAAW;YACrC,MAAMC,IAAI,GAAG,IAAIxE,IAAI,CAAC,CAAC,CAACyE,kBAAkB,CAAC,CAAC;;YAE5C;YACA,IAAIC,aAAa,GAAG,EAAE;YACtB,IAAIC,UAAU,GAAG,EAAE;YACnB,QAAOL,SAAS;cACd,KAAK,KAAK;gBACRI,aAAa,GAAG,OAAO;gBACvBC,UAAU,GAAG,SAAS;gBACtB;cACF,KAAK,KAAK;gBACRD,aAAa,GAAG,OAAO;gBACvBC,UAAU,GAAG,SAAS;gBACtB;cACF,KAAK,KAAK;gBACRD,aAAa,GAAG,QAAQ;gBACxBC,UAAU,GAAG,SAAS;gBACtB;cACF,KAAK,KAAK;gBACRD,aAAa,GAAG,MAAM;gBACtBC,UAAU,GAAG,SAAS;gBACtB;cACF,KAAK,KAAK;gBACRD,aAAa,GAAG,MAAM;gBACtBC,UAAU,GAAG,SAAS;gBACtB;cACF,KAAK,MAAM;gBACTD,aAAa,GAAG,MAAM;gBACtBC,UAAU,GAAG,SAAS;gBACtB;cACF,KAAK,KAAK;gBACRD,aAAa,GAAG,MAAM;gBACtBC,UAAU,GAAG,SAAS;gBACtB;cACF;gBACED,aAAa,GAAGH,WAAW,IAAI,MAAM;gBACrCI,UAAU,GAAG,SAAS;YAC1B;;YAEA;YACA,MAAMC,QAAQ,GAAG;cACfC,GAAG,EAAE7E,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG+E,IAAI,CAACC,MAAM,CAAC,CAAC;cAC/BjF,IAAI,EAAE4E,aAAa;cACnBF,IAAI,EAAEA,IAAI;cACVvG,OAAO,EAAE6F,OAAO,CAACkB,KAAK,IAAI,MAAM;cAChC9B,KAAK,EAAEyB,UAAU;cACjBL,SAAS,EAAEA,SAAS;cACpBW,QAAQ,EAAE;gBACRhB,QAAQ,EAAEA,QAAQ;gBAClBG,SAAS,EAAEA;cACb;YACF,CAAC;YAED3H,SAAS,CAACyD,IAAI,IAAI;cAChB,MAAMgF,SAAS,GAAG,CAACN,QAAQ,EAAE,GAAG1E,IAAI,CAAC,CAACiF,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;cAClDnG,OAAO,CAAC6E,GAAG,CAAC,WAAW,EAAEqB,SAAS,CAAC;cACnC,OAAOA,SAAS;YAClB,CAAC,CAAC;;YAEF;YACA,IAAIZ,SAAS,EAAE;cACb1G,aAAa,CAACsC,IAAI,IAAI;gBACpB,MAAMkF,QAAQ,GAAG;kBACf,GAAGlF,IAAI;kBACP,CAACoE,SAAS,GAAG,CAACpE,IAAI,CAACoE,SAAS,CAAC,IAAI,CAAC,IAAI;gBACxC,CAAC;gBACDtF,OAAO,CAAC6E,GAAG,CAAC,WAAW,EAAEuB,QAAQ,CAAC;gBAClC,OAAOA,QAAQ;cACjB,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAOrG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACvC;IACF,CAAC;;IAED;IACAqB,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEuD,gBAAgB,CAAC;;IAEpD;IACA,OAAO,MAAM;MACXxD,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEsD,gBAAgB,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyB,mBAAmB,GAAIpH,OAAO,IAAK;IACvCtB,kBAAkB,CAACsB,OAAO,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMqH,cAAc,GAAG,CACrB;IACE9D,KAAK,EAAE,KAAK;IACZ+D,SAAS,EAAE,OAAO;IAClBV,GAAG,EAAE,OAAO;IACZW,KAAK,EAAE;EACT,CAAC,EACD;IACEhE,KAAK,EAAE,IAAI;IACX+D,SAAS,EAAE,QAAQ;IACnBV,GAAG,EAAE,QAAQ;IACbW,KAAK,EAAE,KAAK;IACZC,MAAM,EAAErH,MAAM,iBACZpD,OAAA,CAACb,KAAK;MACJiE,MAAM,EAAEA,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;MAClDqD,IAAI,EAAErD,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;IAAK;MAAAsH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC;EAEL,CAAC,EACD;IACErE,KAAK,EAAE,IAAI;IACX+D,SAAS,EAAE,OAAO;IAClBV,GAAG,EAAE,OAAO;IACZW,KAAK,EAAE,KAAK;IACZC,MAAM,EAAElH,KAAK,IAAI,GAAGA,KAAK;EAC3B,CAAC,CACF;;EAED;EACA,MAAMuH,eAAe,GAAGA,CAAA,kBACtB9K,OAAA,CAACjB,IAAI;IACHgM,IAAI,EAAC,OAAO;IACZC,UAAU,EAAExJ,MAAO;IACnByJ,UAAU,EAAE1C,IAAI,iBACdvI,OAAA,CAACjB,IAAI,CAACmM,IAAI;MAACC,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAQ,CAAE;MAAAC,QAAA,eACrCrL,OAAA,CAACjB,IAAI,CAACmM,IAAI,CAACI,IAAI;QACb9E,KAAK,eACHxG,OAAA;UAAKmL,KAAK,EAAE;YAAEtE,QAAQ,EAAE,MAAM;YAAE0E,YAAY,EAAE;UAAM,CAAE;UAAAF,QAAA,gBACpDrL,OAAA;YAAMmL,KAAK,EAAE;cAAEjD,KAAK,EAAEK,IAAI,CAACL,KAAK;cAAEsD,WAAW,EAAE;YAAM,CAAE;YAAAH,QAAA,EACpD9C,IAAI,CAACzD;UAAI;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACP7K,OAAA;YAAMmL,KAAK,EAAE;cAAEjD,KAAK,EAAE,MAAM;cAAErB,QAAQ,EAAE;YAAO,CAAE;YAAAwE,QAAA,EAC9C9C,IAAI,CAACiB;UAAI;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;QACDtB,WAAW,eACTvJ,OAAA;UAAKmL,KAAK,EAAE;YAAEtE,QAAQ,EAAE,MAAM;YAAEqB,KAAK,EAAE;UAAO,CAAE;UAAAmD,QAAA,gBAC9CrL,OAAA;YAAAqL,QAAA,GAAK,gBAAI,EAAC9C,IAAI,CAACtF,OAAO;UAAA;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7B7K,OAAA;YAAAqL,QAAA,GAAK,gBAAI,EAAC9C,IAAI,CAAC0B,QAAQ,GACrB,GAAG1B,IAAI,CAAC0B,QAAQ,CAAChB,QAAQ,CAACwC,OAAO,CAAC,CAAC,CAAC,KAAKlD,IAAI,CAAC0B,QAAQ,CAACb,SAAS,CAACqC,OAAO,CAAC,CAAC,CAAC,EAAE,GAC7E,MAAM;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CACX;IACFM,KAAK,EAAE;MACLO,SAAS,EAAE,mBAAmB;MAC9BC,SAAS,EAAE;IACb;EAAE;IAAAjB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;EAED,oBACE7K,OAAA,CAACd,IAAI;IAAC0M,QAAQ,EAAExK,OAAQ;IAACyK,GAAG,EAAC,uBAAQ;IAAAR,QAAA,eACnCrL,OAAA,CAACI,aAAa;MAAAiL,QAAA,gBAEZrL,OAAA,CAACJ,kBAAkB;QACjBqI,QAAQ,EAAC,MAAM;QACf6D,SAAS,EAAEpL,aAAc;QACzBqL,UAAU,EAAEA,CAAA,KAAMtJ,gBAAgB,CAAC,CAAC/B,aAAa,CAAE;QAAA2K,QAAA,gBAGnDrL,OAAA,CAACa,QAAQ;UAAC2F,KAAK,EAAC,wDAAW;UAACwF,QAAQ,EAAE,KAAM;UAAClL,MAAM,EAAC,OAAO;UAAAuK,QAAA,eACzDrL,OAAA,CAACrB,GAAG;YAACsN,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;YAAAZ,QAAA,gBAClBrL,OAAA,CAACpB,GAAG;cAACsN,IAAI,EAAE,CAAE;cAAAb,QAAA,eACXrL,OAAA,CAACgB,gBAAgB;gBACfwF,KAAK,EAAC,0BAAM;gBACZ8B,KAAK,EAAE1G,KAAK,CAACE,aAAc;gBAC3BqK,UAAU,EAAE;kBAAEjE,KAAK,EAAE;gBAAU;cAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7K,OAAA,CAACpB,GAAG;cAACsN,IAAI,EAAE,CAAE;cAAAb,QAAA,eACXrL,OAAA,CAACgB,gBAAgB;gBACfwF,KAAK,EAAC,0BAAM;gBACZ8B,KAAK,EAAE1G,KAAK,CAACG,cAAe;gBAC5BqK,MAAM,EAAE,KAAKxK,KAAK,CAACE,aAAa,EAAG;gBACnCqK,UAAU,EAAE;kBAAEjE,KAAK,EAAE;gBAAU;cAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7K,OAAA,CAACpB,GAAG;cAACsN,IAAI,EAAE,CAAE;cAAAb,QAAA,eACXrL,OAAA,CAACgB,gBAAgB;gBACfwF,KAAK,EAAC,0BAAM;gBACZ8B,KAAK,EAAE1G,KAAK,CAACI,eAAgB;gBAC7BoK,MAAM,EAAE,KAAKxK,KAAK,CAACE,aAAa,EAAG;gBACnCqK,UAAU,EAAE;kBAAEjE,KAAK,EAAE;gBAAU;cAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7K,OAAA,CAACpB,GAAG;cAACsN,IAAI,EAAE,CAAE;cAAAb,QAAA,eACXrL,OAAA,CAACgB,gBAAgB;gBACfwF,KAAK,EAAC,0BAAM;gBACZ8B,KAAK,EAAE1G,KAAK,CAACK,YAAa;gBAC1BkK,UAAU,EAAE;kBAAEjE,KAAK,EAAE;gBAAU;cAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7K,OAAA,CAACpB,GAAG;cAACsN,IAAI,EAAE,CAAE;cAAAb,QAAA,eACXrL,OAAA,CAACgB,gBAAgB;gBACfwF,KAAK,EAAC,0BAAM;gBACZ8B,KAAK,EAAE1G,KAAK,CAACM,aAAc;gBAC3BkK,MAAM,EAAE,KAAKxK,KAAK,CAACK,YAAY,EAAG;gBAClCkK,UAAU,EAAE;kBAAEjE,KAAK,EAAE;gBAAU;cAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7K,OAAA,CAACpB,GAAG;cAACsN,IAAI,EAAE,CAAE;cAAAb,QAAA,eACXrL,OAAA,CAACgB,gBAAgB;gBACfwF,KAAK,EAAC,0BAAM;gBACZ8B,KAAK,EAAE1G,KAAK,CAACO,cAAe;gBAC5BiK,MAAM,EAAE,KAAKxK,KAAK,CAACK,YAAY,EAAG;gBAClCkK,UAAU,EAAE;kBAAEjE,KAAK,EAAE;gBAAU;cAAE;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGX7K,OAAA,CAACa,QAAQ;UAAC2F,KAAK,EAAC,sCAAQ;UAACwF,QAAQ,EAAE,KAAM;UAAClL,MAAM,EAAC,kBAAkB;UAAAuK,QAAA,EAChEP,eAAe,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGX7K,OAAA,CAACa,QAAQ;UAAC2F,KAAK,EAAC,sCAAQ;UAACwF,QAAQ,EAAE,KAAM;UAAClL,MAAM,EAAC,kBAAkB;UAAAuK,QAAA,eACjErL,OAAA;YAAKqM,GAAG,EAAE7J,aAAc;YAAC2I,KAAK,EAAE;cAAErK,MAAM,EAAE,MAAM;cAAE0J,KAAK,EAAE;YAAO;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGrB7K,OAAA,CAACQ,WAAW;QAACE,aAAa,EAAEA,aAAc;QAACC,cAAc,EAAEA;MAAe;QAAA+J,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC,eAGd7K,OAAA,CAACJ,kBAAkB;QACjBqI,QAAQ,EAAC,OAAO;QAChB6D,SAAS,EAAEnL,cAAe;QAC1BoL,UAAU,EAAEA,CAAA,KAAMrJ,iBAAiB,CAAC,CAAC/B,cAAc,CAAE;QAAA0K,QAAA,gBAGrDrL,OAAA,CAACa,QAAQ;UAAC2F,KAAK,EAAC,0BAAM;UAACwF,QAAQ,EAAE,KAAM;UAAClL,MAAM,EAAC,KAAK;UAAAuK,QAAA,eAClDrL,OAAA,CAAChB,KAAK;YACJgM,UAAU,EAAE1J,QAAS;YACrBgL,OAAO,EAAEhC,cAAe;YACxBiC,MAAM,EAAC,IAAI;YACXC,UAAU,EAAE,KAAM;YAClBzB,IAAI,EAAC,OAAO;YACZ0B,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAE;YACnBC,KAAK,EAAGC,MAAM,KAAM;cAClBC,OAAO,EAAEA,CAAA,KAAMxC,mBAAmB,CAACuC,MAAM,CAAC;cAC1CzB,KAAK,EAAE;gBACL2B,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,CAAArL,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsL,EAAE,MAAKJ,MAAM,CAACI,EAAE,GAAG,SAAS,GAAG,aAAa;gBACzEnG,QAAQ,EAAE,MAAM;gBAChBuE,OAAO,EAAE;cACX;YACF,CAAC;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGX7K,OAAA,CAACa,QAAQ;UAAC2F,KAAK,EAAC,sCAAQ;UAACwF,QAAQ,EAAE,KAAM;UAAClL,MAAM,EAAC,KAAK;UAAAuK,QAAA,EACnD3J,eAAe,gBACd1B,OAAA,CAACf,YAAY;YACX+M,QAAQ;YACRiB,MAAM,EAAE,CAAE;YACVlC,IAAI,EAAC,OAAO;YACZmC,MAAM,EAAE;cACNnF,KAAK,EAAE;gBAAElB,QAAQ,EAAE,MAAM;gBAAEuE,OAAO,EAAE;cAAU,CAAC;cAC/C+B,OAAO,EAAE;gBAAEtG,QAAQ,EAAE,MAAM;gBAAEuE,OAAO,EAAE;cAAU;YAClD,CAAE;YAAAC,QAAA,gBAEFrL,OAAA,CAACf,YAAY,CAACiM,IAAI;cAACnD,KAAK,EAAC,oBAAK;cAAAsD,QAAA,EAAE3J,eAAe,CAACyB;YAAW;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAChF7K,OAAA,CAACf,YAAY,CAACiM,IAAI;cAACnD,KAAK,EAAC,cAAI;cAAAsD,QAAA,eAC3BrL,OAAA,CAACb,KAAK;gBACJiE,MAAM,EAAE1B,eAAe,CAAC0B,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;gBAClEqD,IAAI,EAAE/E,eAAe,CAAC0B,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;cAAK;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,eACpB7K,OAAA,CAACf,YAAY,CAACiM,IAAI;cAACnD,KAAK,EAAC,cAAI;cAAAsD,QAAA,EAAE3J,eAAe,CAAC+B,GAAG,CAACgI,OAAO,CAAC,CAAC;YAAC;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAClF7K,OAAA,CAACf,YAAY,CAACiM,IAAI;cAACnD,KAAK,EAAC,cAAI;cAAAsD,QAAA,EAAE3J,eAAe,CAAC8B,GAAG,CAACiI,OAAO,CAAC,CAAC;YAAC;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAClF7K,OAAA,CAACf,YAAY,CAACiM,IAAI;cAACnD,KAAK,EAAC,cAAI;cAAAsD,QAAA,GAAE3J,eAAe,CAAC6B,KAAK,EAAC,OAAK;YAAA;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAC9E7K,OAAA,CAACf,YAAY,CAACiM,IAAI;cAACnD,KAAK,EAAC,oBAAK;cAAAsD,QAAA,GAAE3J,eAAe,CAACgC,OAAO,EAAC,MAAC;YAAA;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,gBAEf7K,OAAA;YAAGmL,KAAK,EAAE;cAAEtE,QAAQ,EAAE;YAAO,CAAE;YAAAwE,QAAA,EAAC;UAAW;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC/C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEX,CAAC;AAAC1J,EAAA,CAzlBID,eAAe;AAAAkM,GAAA,GAAflM,eAAe;AA2lBrB,eAAeA,eAAe;AAAC,IAAAb,EAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAmM,GAAA;AAAAC,YAAA,CAAAhN,EAAA;AAAAgN,YAAA,CAAAzM,GAAA;AAAAyM,YAAA,CAAAtM,GAAA;AAAAsM,YAAA,CAAApM,GAAA;AAAAoM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}