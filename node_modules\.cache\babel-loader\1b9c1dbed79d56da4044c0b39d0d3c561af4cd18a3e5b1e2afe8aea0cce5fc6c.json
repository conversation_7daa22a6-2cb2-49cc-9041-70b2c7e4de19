{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\nimport devicesData from '../data/devices.json'; // 导入设备数据\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel = null; // 存储原始模型数据\nlet skeleton = null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n  bsm: 'changli/cloud/v2x/obu/bsm',\n  rsm: 'changli/cloud/v2x/rsu/rsm',\n  scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',\n  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat' // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (!lastPosition) {\n      lastPosition = newPos.clone();\n      return newPos;\n    }\n    const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n    const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n    const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n    lastPosition.set(filteredX, filteredY, filteredZ);\n    return lastPosition.clone();\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  const lastPos = vehicleLastPositions.get(vehicleId);\n\n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n\n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n\n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (lastRotation === null) {\n      lastRotation = newRotation;\n      return newRotation;\n    }\n\n    // 处理角度跳变（从360度到0度或反之）\n    let diff = newRotation - lastRotation;\n    if (diff > Math.PI) diff -= 2 * Math.PI;\n    if (diff < -Math.PI) diff += 2 * Math.PI;\n    const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n    lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const lastRot = vehicleLastRotations.get(vehicleId);\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n\n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\n// 在文件顶部添加\nconst mixersToCleanup = new Set();\nconst bonesMap = new Map();\n\n// 在文件顶部添加资源管理器\nconst resourceManager = {\n  mixers: new Set(),\n  bones: new Map(),\n  actions: new Map(),\n  models: new Set(),\n  addMixer(mixer, model) {\n    this.mixers.add(mixer);\n    if (model) {\n      this.models.add(model);\n      // 记录骨骼\n      model.traverse(object => {\n        if (object.isBone) {\n          this.bones.set(object.uuid, object);\n        }\n      });\n    }\n    return mixer;\n  },\n  addAction(action, mixer) {\n    if (!this.actions.has(mixer)) {\n      this.actions.set(mixer, new Set());\n    }\n    this.actions.get(mixer).add(action);\n    return action;\n  },\n  removeMixer(mixer) {\n    if (this.mixers.has(mixer)) {\n      try {\n        // 停止并清理所有动作\n        if (this.actions.has(mixer)) {\n          this.actions.get(mixer).forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          this.actions.delete(mixer);\n        }\n\n        // 停止混合器\n        if (typeof mixer.stopAllAction === 'function') {\n          mixer.stopAllAction();\n        }\n\n        // 清理混合器的根对象\n        const root = mixer.getRoot();\n        if (root) {\n          this.models.delete(root);\n          root.traverse(object => {\n            if (object && object.isBone) {\n              this.bones.delete(object.uuid);\n            }\n            if (object && object.animations) {\n              object.animations.length = 0;\n            }\n          });\n\n          // 安全地清理缓存\n          try {\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n\n            // 这里是发生错误的地方，添加防御性检查\n            if (typeof mixer.uncacheClip === 'function') {\n              // 不直接调用uncacheClip(null)，而是获取混合器中的所有动画片段并逐个清理\n              const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n              clips.forEach(clip => {\n                if (clip && clip.uuid) {\n                  mixer.uncacheClip(clip);\n                }\n              });\n            }\n          } catch (e) {\n            console.log('在清理动画混合器时发生非致命错误:', e);\n            // 继续执行其余清理操作\n          }\n        }\n        this.mixers.delete(mixer);\n      } catch (error) {\n        console.error('清理动画混合器时发生错误:', error);\n        // 确保即使出错也会从集合中移除\n        this.mixers.delete(mixer);\n      }\n    }\n  },\n  cleanup() {\n    try {\n      // 清理动作\n      this.actions.forEach((actions, mixer) => {\n        try {\n          actions.forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          actions.clear();\n        } catch (e) {\n          console.log('清理动作时发生非致命错误:', e);\n        }\n      });\n      this.actions.clear();\n\n      // 清理混合器\n      this.mixers.forEach(mixer => {\n        try {\n          if (typeof mixer.stopAllAction === 'function') {\n            mixer.stopAllAction();\n          }\n          const root = mixer.getRoot();\n          if (root) {\n            root.traverse(object => {\n              if (object && object.animations) {\n                object.animations.length = 0;\n              }\n            });\n\n            // 安全清理\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n\n            // 安全清理动画片段\n            try {\n              if (mixer._actions && Array.isArray(mixer._actions)) {\n                const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n                clips.forEach(clip => {\n                  if (clip && clip.uuid && typeof mixer.uncacheClip === 'function') {\n                    mixer.uncacheClip(clip);\n                  }\n                });\n              }\n            } catch (e) {\n              console.log('清理动画片段时发生非致命错误:', e);\n            }\n          }\n        } catch (e) {\n          console.log('清理混合器时发生非致命错误:', e);\n        }\n      });\n      this.mixers.clear();\n\n      // 清理骨骼\n      this.bones.forEach(bone => {\n        if (bone.parent) {\n          bone.parent.remove(bone);\n        }\n        if (bone.matrix) bone.matrix.identity();\n        if (bone.matrixWorld) bone.matrixWorld.identity();\n      });\n      this.bones.clear();\n\n      // 清理模型\n      this.models.forEach(model => {\n        if (model.parent) {\n          model.parent.remove(model);\n        }\n        model.traverse(object => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n          }\n          if (object.animations) {\n            object.animations.length = 0;\n          }\n        });\n      });\n      this.models.clear();\n    } catch (error) {\n      console.error('全局清理过程中发生错误:', error);\n      // 即使发生错误也尝试清空集合\n      this.actions.clear();\n      this.mixers.clear();\n      this.bones.clear();\n      this.models.clear();\n    }\n  }\n};\n\n// 修改创建动画混合器的函数\nconst createAnimationMixer = model => {\n  const mixer = new THREE.AnimationMixer(model);\n  return resourceManager.addMixer(mixer, model);\n};\n\n// 修改动画动作创建的函数\nconst createAction = (clip, mixer, model) => {\n  const action = mixer.clipAction(clip, model);\n  return resourceManager.addAction(action, mixer);\n};\n\n// 在CampusModel组件顶部添加消息缓存\nconst processedMessageIds = new Set();\nconst CampusModel = ({\n  className,\n  onCurrentRSUChange,\n  selectedRSUs\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n\n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n\n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = new Map(); // 使用全局Map存储所有行人的动画混合器（不再使用useRef）\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,\n    // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null,\n    phases: []\n  });\n\n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n\n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n\n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n\n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '65px',\n    // 从 60px 改为 65px\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '65px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({\n    topic: '',\n    content: ''\n  });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n\n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n\n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n\n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos).to({\n          x: 0,\n          y: 300,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        }).start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp).to({\n          x: 0,\n          y: 1,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        }).start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n\n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget).to({\n          x: 0,\n          y: 0,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        }).start();\n\n        // 启用控制器\n        controls.enabled = true;\n\n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x + 50, 70, -modelCoords.y + 50);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n\n      // 如果该路口有红绿灯，自动显示红绿灯弹窗\n      if (intersection.hasTrafficLight !== false && intersection.interId) {\n        console.log(`路口 ${intersection.name} 有红绿灯，准备显示红绿灯状态`);\n\n        // 延迟300ms调用以确保场景已更新\n        setTimeout(() => {\n          // 检查多种可能的ID格式\n          let interId = intersection.interId;\n\n          // 使用全局的showTrafficLightPopup函数显示红绿灯弹窗\n          if (window.showTrafficLightPopup) {\n            window.showTrafficLightPopup(interId);\n            console.log(`已触发路口 ${intersection.name} (ID: ${interId}) 的红绿灯弹窗显示`);\n          } else {\n            console.error('找不到显示红绿灯弹窗的函数');\n          }\n        }, 300);\n      } else {\n        console.log(`路口 ${intersection.name} 没有红绿灯或缺少ID，不显示红绿灯状态`);\n\n        // 如果有弹窗正在显示，则关闭它\n        if (window._setTrafficLightPopover) {\n          window._setTrafficLightPopover({\n            visible: false\n          });\n        }\n      }\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        var _payload$data;\n        // console.log('收到RSM消息:', payload);\n\n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n\n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n          return;\n        }\n\n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n\n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n\n        const participants = ((_payload$data = payload.data) === null || _payload$data === void 0 ? void 0 : _payload$data.participants) || [];\n        const rsuid = payload.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id = deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = type === '3' ? SkeletonUtils.clone(preloadedPeopleModel) : preloadedModel.clone();\n              // 根据类型调整高度和缩放\n              const height = type === '3' ? 2.0 : 1.0;\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n\n              // 如果是行人类型，设置缩放和创建动画\n              if (type === '3') {\n                // newModel.scale.set(0.005, 0.005, 0.005);\n                newModel.scale.set(4, 4, 4);\n\n                // 使用resourceManager创建并管理动画混合器\n                const mixer = createAnimationMixer(newModel);\n                if (peopleBaseModel && peopleBaseModel.animations && peopleBaseModel.animations.length > 0) {\n                  // 只创建一个动作并添加到资源管理器\n                  const action = createAction(peopleBaseModel.animations[0], mixer, newModel);\n                  action.play();\n                }\n\n                // 保存到全局Map中(不再使用useRef)\n                peopleAnimationMixers.set(id, mixer);\n              }\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 1000;\n        const currentIds = new Set(participants.map(p => deviceMac + p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            // 如果是行人，清理动画混合器\n            if (modelData.type === '3' && peopleAnimationMixers.has(id)) {\n              const mixer = peopleAnimationMixers.get(id);\n              // 使用resourceManager清理混合器\n              resourceManager.removeMixer(mixer);\n              peopleAnimationMixers.delete(id);\n            }\n\n            // 从场景移除模型\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n\n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n\n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n\n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n\n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid,\n          // 直接传递车辆ID\n          data: {\n            // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n\n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n\n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n\n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n\n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n\n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n\n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n\n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n\n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n\n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n\n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          scene.add(newVehicleModel);\n\n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1',\n            // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n\n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position).to({\n            y: 0.5\n          }, 500).easing(TWEEN.Easing.Quadratic.Out).start();\n\n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({\n                opacity: 0.6\n              }).to({\n                opacity: 1.0\n              }, 500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function () {\n                child.material.opacity = this.opacity;\n                child.material.needsUpdate = true;\n              }).start();\n            }\n          });\n\n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({\n            opacity: 0.6\n          }).to({\n            opacity: 1.0\n          }, 500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function () {\n            speedLabel.material.opacity = this.opacity;\n            speedLabel.material.needsUpdate = true;\n          }).start();\n\n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n\n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n\n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n\n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n\n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n\n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 增加到10秒，避免频繁清理造成闪烁\n\n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n\n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1;\n            modelData.model.traverse(child => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n\n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n\n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            modelData.model.traverse(child => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n\n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n\n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n\n        try {\n          const payload = JSON.parse(message);\n\n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n\n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n\n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n\n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n\n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n\n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? getDirectionFromCode(phase.trafficDirec) : getPhaseDirection(phaseId);\n\n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n\n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n\n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n\n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n\n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n\n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n\n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n\n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && (window.currentPopoverIdRef.current === modelKey || window.currentPopoverIdRef.current === String(modelKey) || window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n\n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n        return;\n      }\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件，确保包含mac和timestamp\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data,\n          mac: payload.mac,\n          tm: payload.tm\n        }, '*');\n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(parseFloat(rsiData.posLong), parseFloat(rsiData.posLat));\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          switch (eventType) {\n            case '401':\n              // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':\n              // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':\n              // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':\n              // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':\n              // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002':\n              // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':\n              // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n\n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n\n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch (sceneType) {\n          case '2':\n            // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':\n            // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':\n            // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':\n            // 限速提醒\n            const speedLimit = sceneData.eventData1; // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':\n            // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':\n            // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':\n            // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':\n            // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':\n            // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':\n            // 信号灯优先\n            const priorityType = sceneData.eventData1; // 优先类型\n            const duration = sceneData.eventData2; // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 检查消息ID是否已处理过\n          if (message.messageId) {\n            if (processedMessageIds.has(message.messageId)) {\n              // console.log('跳过重复消息:', message.messageId);\n              return;\n            }\n\n            // 添加到已处理集合\n            processedMessageIds.add(message.messageId);\n\n            // 限制缓存大小，防止内存泄漏\n            if (processedMessageIds.size > 1000) {\n              // 转换为数组，删除最早的100个元素\n              const idsArray = Array.from(processedMessageIds);\n              for (let i = 0; i < 100; i++) {\n                processedMessageIds.delete(idsArray[i]);\n              }\n            }\n          }\n\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/vehicle.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n\n        // 检查scene是否初始化\n        if (scene) {\n          scene.add(model);\n\n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } else {\n          console.error('无法添加模型：场景未初始化');\n        }\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画 - 只更新存在的混合器\n      const deltaTime = clock.getDelta();\n\n      // 使用Map而不是ref.current\n      if (peopleAnimationMixers.size > 0) {\n        peopleAnimationMixers.forEach(mixer => {\n          mixer.update(deltaTime);\n        });\n      }\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 200, -50 * Math.sin(adjustedRotation));\n\n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n\n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n\n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n\n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 路口视角模式\n        controls.update();\n      }\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 停止渲染循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 停止所有 TWEEN 动画\n      TWEEN.removeAll();\n\n      // 3. 清理所有动画混合器\n      peopleAnimationMixers.forEach(mixer => {\n        resourceManager.removeMixer(mixer);\n      });\n      peopleAnimationMixers.clear();\n\n      // 4. 清理所有其他资源\n      resourceManager.cleanup();\n\n      // 5. 清理场景中的所有对象\n      if (scene) {\n        const objectsToRemove = [];\n        scene.traverse(object => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n            if (object !== scene) {\n              objectsToRemove.push(object);\n            }\n          }\n        });\n\n        // 从场景中移除对象\n        objectsToRemove.forEach(obj => {\n          if (obj.parent) {\n            obj.parent.remove(obj);\n          }\n        });\n        scene.clear();\n      }\n\n      // 6. 清理渲染器\n      if (renderer) {\n        renderer.setAnimationLoop(null);\n        if (containerRef.current && renderer.domElement && renderer.domElement.parentNode === containerRef.current) {\n          containerRef.current.removeChild(renderer.domElement);\n        }\n        renderer.dispose();\n        renderer.forceContextLoss();\n      }\n\n      // 7. 清理其他资源\n      if (controls) {\n        controls.dispose();\n      }\n\n      // 8. 清理数据结构\n      vehicleLastPositions.clear();\n      vehicleLastRotations.clear();\n      deviceTimestamps.clear();\n      vehicleModels.clear();\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n\n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n\n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n\n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n\n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {\n          // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 2000);\n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n\n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = event => {\n        if (scene && cameraRef.current) {\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current);\n        }\n      };\n\n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n\n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n\n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({\n      color: 0x333333\n    });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n\n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({\n      color: 0x666666\n    });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n\n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          //\n          transparent: false,\n          opacity: 0.1,\n          // 几乎透明\n          depthWrite: false\n        });\n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0); // 放在红绿灯位置\n\n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n\n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500); // 延迟略长于debugTrafficLights\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n\n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n\n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        // 查找第一个带有红绿灯的路口\n        const firstTrafficLightIntersection = intersectionsData.intersections.find(intersection => intersection.hasTrafficLight !== false && intersection.interId);\n\n        // 如果找到带红绿灯的路口，优先选择它；否则选择第一个路口\n        const targetIntersection = firstTrafficLightIntersection || intersectionsData.intersections[0];\n        console.log('自动选择路口:', targetIntersection.name, '具有红绿灯:', firstTrafficLightIntersection ? '是' : '否');\n\n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(targetIntersection.name);\n        }, 2000);\n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n\n  // 新增：在场景初始化后渲染所有路口entrance的设备图标\n  const renderEntranceDeviceIcons = converterInstance => {\n    if (!scene || typeof scene.traverse !== 'function' || !converterInstance) return;\n    try {\n      // 先移除所有旧的设备图标组（用 children.filter 更安全）\n      scene.children.filter(obj => obj.userData && obj.userData.isEntranceDeviceIcons).forEach(obj => scene.remove(obj));\n      // 遍历所有路口\n      intersectionsData.intersections.forEach(intersection => {\n        if (!intersection.entrances || !Array.isArray(intersection.entrances)) return;\n        // 遍历每个路口的所有 entrance\n        intersection.entrances.forEach(entrance => {\n          // 检查经纬度有效性\n          if (!entrance.longitude || !entrance.latitude || isNaN(parseFloat(entrance.longitude)) || isNaN(parseFloat(entrance.latitude))) {\n            return; // 跳过无效经纬度的入口\n          }\n\n          // 查找该路口该entrance下所有设备\n          const devices = devicesData.devices.filter(d => d.location === intersection.name && d.entrance === entrance.name);\n          if (devices.length === 0) return;\n          // 经纬度转模型坐标\n          console.log('渲染路口', intersection.name, '入口', entrance.name, '设备数量', devices.length);\n          const modelPos = converterInstance.wgs84ToModel(parseFloat(entrance.longitude), parseFloat(entrance.latitude));\n          // 创建一个组用于存放所有图标\n          const group = new THREE.Group();\n          group.position.set(modelPos.x, 15, -modelPos.y); // 上方15米\n          group.userData = {\n            isEntranceDeviceIcons: true\n          };\n          // 图标排成一排，居中\n          const iconSize = 24; // px\n          const iconSpacing = 8; // px\n          const totalWidth = devices.length * iconSize + (devices.length - 1) * iconSpacing;\n          // 以三维单位为准，假设1单位=1米，24px约等于0.6米\n          const size3D = 2.0; // 图标尺寸加大\n          const spacing3D = 0.5; // 图标间距加大\n          const startX = -((devices.length - 1) * (size3D + spacing3D)) / 2;\n          devices.forEach((device, idx) => {\n            // 创建一个平面用于显示SVG图标\n            const textureLoader = new THREE.TextureLoader();\n            const iconPath = `${BASE_URL}/images/${device.type}.svg`;\n            // 图标材质\n            const iconMaterial = new THREE.MeshBasicMaterial({\n              map: textureLoader.load(iconPath),\n              transparent: true,\n              opacity: 1 // 图标完全不透明\n            });\n            // 图标背景板材质\n            const bgMaterial = new THREE.MeshBasicMaterial({\n              color: 0x000000,\n              transparent: true,\n              opacity: 0.7 // 半透明黑色\n            });\n            // 背景板尺寸略大于图标\n            const bgWidth = size3D * 1.25;\n            const bgHeight = size3D * 1.25;\n            // 背景板几何体（圆角矩形）\n            // 由于Three.js没有内置圆角矩形，使用PlaneGeometry近似\n            const bgGeometry = new THREE.PlaneGeometry(bgWidth, bgHeight);\n            const bgMesh = new THREE.Mesh(bgGeometry, bgMaterial);\n            // 图标几何体\n            const iconGeometry = new THREE.PlaneGeometry(size3D, size3D);\n            const iconMesh = new THREE.Mesh(iconGeometry, iconMaterial);\n            // 图标略微前移，避免Z轴重叠闪烁\n            iconMesh.position.set(0, 0, 0.01);\n            // 创建一个组，包含背景和图标\n            const iconGroup = new THREE.Group();\n            iconGroup.add(bgMesh);\n            iconGroup.add(iconMesh);\n            // 整体平移到正确位置\n            iconGroup.position.set(startX + idx * (size3D + spacing3D), 0, 0);\n            // 不再固定旋转角度，后续在动画循环中让其始终面向相机\n            iconGroup.renderOrder = 999; // 提高渲染优先级\n            iconGroup.userData = {\n              deviceId: device.id,\n              deviceType: device.type,\n              entrance: entrance.name,\n              isEntranceDeviceIcon: true\n            };\n            group.add(iconGroup);\n          });\n          scene.add(group);\n        });\n      });\n    } catch (e) {\n      console.warn('scene.traverse error', e);\n      return;\n    }\n  };\n\n  // 在场景初始化后调用渲染设备图标\n  useEffect(() => {\n    if (scene && typeof scene.traverse === 'function' && converter.current) {\n      renderEntranceDeviceIcons(converter.current);\n    }\n  }, [scene, converter.current]);\n\n  // 新增：每帧让所有设备图标组始终面向相机（billboard效果）\n  useEffect(() => {\n    if (!scene || !cameraRef.current) return;\n    const animateBillboard = () => {\n      // 遍历所有设备图标组，让其正对相机\n      scene.children.forEach(obj => {\n        if (obj.userData && obj.userData.isEntranceDeviceIcons) {\n          obj.lookAt(cameraRef.current.position.x, obj.position.y, cameraRef.current.position.z);\n        }\n      });\n      requestAnimationFrame(animateBillboard);\n    };\n    animateBillboard();\n  }, [scene]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u533A\\u57DF\\u9009\\u62E9\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2318,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u533A\\u57DF\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      options: intersectionsData.intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      },\n      value: selectedIntersection ? selectedIntersection.name : undefined\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2319,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2335,\n      columnNumber: 7\n    }, this), trafficLightPopover.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${trafficLightPopover.position.x}px`,\n        top: `${trafficLightPopover.position.y}px`,\n        transform: 'translate(-50%, -100%)',\n        zIndex: 1003,\n        backgroundColor: 'rgba(0, 0, 0, 0.85)',\n        color: 'white',\n        borderRadius: '4px',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n        padding: '0',\n        maxWidth: '240px',\n        // 缩小最大宽度\n        fontSize: '12px' // 缩小字体\n      },\n      children: [trafficLightPopover.content, /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          position: 'absolute',\n          top: '0px',\n          right: '0px',\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          fontSize: '12px',\n          cursor: 'pointer',\n          padding: '2px 6px'\n        },\n        onClick: () => handleClosePopover(setTrafficLightPopover),\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2356,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2339,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2376,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2386,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2375,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"9dMqdnZB8KAut/QRPMaMvcL13co=\");\n_c = CampusModel;\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 12,\n    // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    backgroundColor: parameters.backgroundColor || {\n      r: 255,\n      g: 255,\n      b: 255,\n      a: 0.8\n    },\n    textColor: parameters.textColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n\n  // 设置字体\n  // context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n\n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n\n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  canvas.width = width;\n  canvas.height = height;\n\n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n\n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n\n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n\n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n\n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n\n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n\n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n\n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n\n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = {\n    text: text,\n    params: params\n  };\n  return sprite;\n}\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    try {\n      const [trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`), loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`)]);\n\n      // 处理机动车模型\n      console.log('加载车辆模型...');\n      preloadedVehicleModel = vehicleGltf.scene;\n      preloadedVehicleModel.traverse(child => {\n        if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n            color: 0xff0000,\n            // 0xff0000, //红色 0xffffff,白色\n            metalness: 0.2,\n            roughness: 0.1,\n            envMapIntensity: 1.0\n          });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n        }\n      });\n      console.log('加载非机动车模型...');\n      // 处理非机动车模型\n      preloadedCyclistModel = cyclistGltf.scene;\n      // 设置非机动车模型的缩放\n      preloadedCyclistModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedCyclistModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n      console.log('加载行人模型...');\n      // 处理行人模型\n      preloadedPeopleModel = peopleGltf.scene;\n      // 设置行人模型的缩放\n      // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n      // 保持原始材质\n      preloadedPeopleModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n        if (child.isMesh) {\n          child.castShadow = true;\n        }\n      });\n\n      // 保存行人动画数据\n      console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n      if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n        console.log('找到行人动画:', peopleGltf.animations.length, '个');\n        peopleBaseModel = peopleGltf;\n      } else {\n        console.warn('行人模型没有包含动画数据');\n      }\n      console.log('加载红绿灯模型...');\n\n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      console.log('红绿灯模型：', preloadedTrafficLightModel);\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n\n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n\n        // // 尝试单独加载红绿灯模型\n        // if (!preloadedTrafficLightModel) {\n        //   console.log('正在单独加载红绿灯模型...');\n        //   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n        //   preloadedTrafficLightModel = trafficLightGltf.scene;\n        //   preloadedTrafficLightModel.scale.set(3, 3, 3);\n        //   console.log('红绿灯模型加载成功');\n        // }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = type => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  try {\n    // 创建一个新的警告标记\n    const sprite = createTextSprite(text);\n    sprite.position.set(position.x, 10, -position.y); // 设置位置，稍微升高以便可见\n\n    // 为标记添加一个定时器，在1秒后自动移除\n    setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n        scene.remove(sprite);\n      }\n    }, 100);\n\n    // 将标记添加到场景中\n    scene.add(sprite);\n\n    // console.log('添加场景事件标记:', {\n    //   位置: position,\n    //   文本: text,\n    //   颜色: color\n    // });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = converterInstance => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n\n  // 检查红绿灯模型是否已加载\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载，尝试重新加载...');\n    // 尝试重新加载红绿灯模型\n    const loader = new GLTFLoader();\n    loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`).then(trafficLightGltf => {\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('红绿灯模型重新加载成功，开始创建红绿灯...');\n      // 重新调用创建函数\n      createTrafficLights(converterInstance);\n    }).catch(error => {\n      console.error('红绿灯模型重新加载失败:', error);\n      // 如果加载失败，使用简单的替代物体\n      createFallbackTrafficLights(converterInstance);\n    });\n    return;\n  }\n\n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach(lightObj => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      try {\n        // 确保模型存在且可以克隆\n        if (!preloadedTrafficLightModel || !preloadedTrafficLightModel.clone) {\n          throw new Error('红绿灯模型无效或无法克隆');\n        }\n\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n\n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n\n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n\n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n\n        // 设置材质属性\n        trafficLightModel.traverse(child => {\n          if (child.isMesh) {\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            child.renderOrder = 100;\n          }\n        });\n\n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n\n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n\n        // ========== 新增：在红绿灯地面添加指北针图标 =============\n        // 创建一个平面用于显示指北针SVG图标\n        const compassTextureLoader = new THREE.TextureLoader();\n        const compassIconPath = '${BASE_URL}/images/compass.svg'; // public目录下的路径\n        const compassMaterial = new THREE.MeshBasicMaterial({\n          map: compassTextureLoader.load(compassIconPath),\n          transparent: false,\n          opacity: 1\n        });\n        // 平面几何体，6x6米\n        const compassGeometry = new THREE.PlaneGeometry(3, 3);\n        const compassMesh = new THREE.Mesh(compassGeometry, compassMaterial);\n        // 指北针放在红绿灯正下方地面（y=0.1，略高于地面防止Z冲突）\n        compassMesh.position.set(modelPos.x, 4, -modelPos.y);\n        // 指北针朝上，旋转x轴-90度\n        compassMesh.rotation.x = -Math.PI / 2;\n        // 渲染优先级高，避免被地面遮挡\n        compassMesh.renderOrder = 101;\n        // 可选：添加userData标记\n        compassMesh.userData = {\n          type: 'compassIcon',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        // 添加到场景\n        scene.add(compassMesh);\n        // ========== 指北针图标添加结束 =============\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n\n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = converterInstance => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({\n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n\n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n\n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n\n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,\n    // 完全透明\n    depthWrite: false\n  });\n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  trafficLightModel.add(collider);\n\n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n\n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n\n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: 0xFF0000\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n\n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  trafficLightModel.add(lightMesh);\n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = phaseId => {\n  switch (phaseId) {\n    case '1':\n      return '北进口左转';\n    case '2':\n      return '北进口直行';\n    case '3':\n      return '北进口右转';\n    case '5':\n      return '东进口左转';\n    case '6':\n      return '东进口直行';\n    case '7':\n      return '东进口右转';\n    case '9':\n      return '南进口左转';\n    case '10':\n      return '南进口直行';\n    case '11':\n      return '南进口右转';\n    case '13':\n      return '西进口左转';\n    case '14':\n      return '西进口直行';\n    case '15':\n      return '西进口右转';\n    default:\n      return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = dirCode => {\n  switch (dirCode) {\n    case 'N':\n      return '北向南';\n    case 'S':\n      return '南向北';\n    case 'E':\n      return '东向西';\n    case 'W':\n      return '西向东';\n    case 'NE':\n      return '东北向西南';\n    case 'NW':\n      return '西北向东南';\n    case 'SE':\n      return '东南向西北';\n    case 'SW':\n      return '西南向东北';\n    default:\n      return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n\n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = (event.clientX - rect.left) / container.clientWidth * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n\n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Line.threshold = 1;\n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  console.log('鼠标点击位置:', mouseX, mouseY);\n\n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n\n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'userData:', intersect.object.userData);\n    });\n\n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n\n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n\n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n\n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  console.log('射线检测到的对象数量:', intersects.length);\n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', 'userData:', obj.userData, '距离:', intersect.distance);\n    });\n\n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n\n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n\n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.1; // 设置一个阈值，只有距离小于此值的才会被选中\n\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n\n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n\n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      console.log(`路口 ${interId} 的距离:`, distance);\n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = {\n          interId,\n          distance\n        };\n      }\n    }\n  });\n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n\n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  console.log('未检测到任何红绿灯点击');\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = setPopoverState => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n\n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n\n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({\n    ...prev,\n    visible: false,\n    content: null,\n    // 清空内容\n    phases: [] // 清空相位信息\n  }));\n  console.log('弹窗已关闭，所有相关资源已清理');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = interId => {\n  try {\n    var _document$querySelect, _document$querySelect2;\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n\n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      return false;\n    }\n\n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n\n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 创建弹出窗口内容\n    let content;\n    if (stateInfo && stateInfo.phases) {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '220px',\n          maxHeight: '300px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          },\n          children: [intersection.name, \" (ID: \", interId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                break;\n            }\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '6px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '4px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: getPhaseDirection(phase.phaseId)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3225,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 3px',\n                    borderRadius: '2px'\n                  },\n                  children: phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3226,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3224,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3237,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3238,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3236,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3214,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '6px',\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3194,\n        columnNumber: 9\n      }, this);\n    } else {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          maxWidth: '200px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '8px'\n          },\n          children: intersection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3252,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u8DEF\\u53E3ID: \", interId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3251,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2 - 500;\n    const centerY = window.innerHeight / 2 - 500;\n\n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = (_document$querySelect = document.querySelector('#root')) === null || _document$querySelect === void 0 ? void 0 : (_document$querySelect2 = _document$querySelect.__REACT_INSTANCE) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.setTrafficLightPopover;\n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: {\n          x: centerX,\n          y: centerY\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      document.body.appendChild(popover);\n\n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  return list;\n};\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = interId => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n\n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 判断是否有相位数据\n    const hasPhaseData = stateInfo && stateInfo.phases && stateInfo.phases.length > 0;\n    let content;\n\n    // 指北针样式\n    const compassStyle = {\n      position: 'absolute',\n      top: '5px',\n      right: '25px',\n      width: '30px',\n      height: '30px',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      borderRadius: '50%',\n      background: 'rgba(0,0,0,0.1)',\n      zIndex: 10\n    };\n\n    // 指北针组件\n    const CompassIcon = () => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: compassStyle,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          transform: 'rotate(0deg)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#ff5252',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            lineHeight: '14px'\n          },\n          children: \"N\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            width: 0,\n            height: 0,\n            borderLeft: '6px solid transparent',\n            borderRight: '6px solid transparent',\n            borderBottom: '10px solid #ff5252',\n            marginTop: '-2px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3412,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3400,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 3399,\n      columnNumber: 7\n    }, this);\n    if (hasPhaseData) {\n      // 相位ID与方向/方式的映射\n      const phaseMap = {\n        '1': {\n          dir: 'N',\n          type: 'left'\n        },\n        '2': {\n          dir: 'N',\n          type: 'straight'\n        },\n        '3': {\n          dir: 'N',\n          type: 'right'\n        },\n        '5': {\n          dir: 'E',\n          type: 'left'\n        },\n        '6': {\n          dir: 'E',\n          type: 'straight'\n        },\n        '7': {\n          dir: 'E',\n          type: 'right'\n        },\n        '9': {\n          dir: 'S',\n          type: 'left'\n        },\n        '10': {\n          dir: 'S',\n          type: 'straight'\n        },\n        '11': {\n          dir: 'S',\n          type: 'right'\n        },\n        '13': {\n          dir: 'W',\n          type: 'left'\n        },\n        '14': {\n          dir: 'W',\n          type: 'straight'\n        },\n        '15': {\n          dir: 'W',\n          type: 'right'\n        }\n      };\n      const typeOrder = ['left', 'straight', 'right'];\n      const colorMap = {\n        G: '#00ff00',\n        Y: '#ffff00',\n        R: '#ff0000'\n      };\n      const dirData = {\n        N: {},\n        E: {},\n        S: {},\n        W: {}\n      };\n      stateInfo.phases.forEach(phase => {\n        const map = phaseMap[phase.phaseId];\n        if (map) {\n          dirData[map.dir][map.type] = {\n            color: colorMap[phase.trafficLight] || '#888',\n            remainTime: phase.remainTime\n          };\n        }\n      });\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '260px',\n          background: 'rgba(0,0,0,0.05)',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CompassIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3449,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '8px',\n            fontSize: '15px',\n            textAlign: 'center'\n          },\n          children: [intersection.name, \"\\u706F\\u6001\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3450,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateRows: '60px 60px 60px',\n            gridTemplateColumns: '60px 60px 60px',\n            justifyContent: 'center',\n            alignItems: 'center',\n            background: 'rgba(255,255,255,0.05)',\n            borderRadius: '8px',\n            margin: '0 auto',\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 1,\n              gridColumn: 2,\n              textAlign: 'center',\n              display: 'flex',\n              justifyContent: 'center',\n              width: '100%'\n            },\n            children: typeOrder.map((type, index) => {\n              // 反转左转和右转在数组中的顺序\n              let displayIndex = index;\n              if (index === 0) displayIndex = 2;else if (index === 2) displayIndex = 0;\n              const currentType = typeOrder[displayIndex];\n\n              // 计算与南边对齐的样式\n              const marginStyle = {};\n              if (currentType === 'left') {\n                // 左转箭头 (右侧显示)\n                marginStyle.marginRight = '0px';\n              } else if (currentType === 'straight') {\n                // 直行箭头 (中间显示)\n                marginStyle.marginLeft = '10px';\n                marginStyle.marginRight = '10px';\n              } else if (currentType === 'right') {\n                // 右转箭头 (左侧显示)\n                marginStyle.marginLeft = '0px';\n              }\n              return dirData.N[currentType] &&\n              /*#__PURE__*/\n              // <div key={currentType} style={{\n              //   display: 'flex', \n              //   flexDirection: 'column', \n              //   alignItems: 'center',\n              //   ...marginStyle\n              // }}>\n              _jsxDEV(\"div\", {\n                style: {\n                  marginRight: currentType === 'left' ? 0 : '10px',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    color: dirData.N[currentType].color,\n                    fontWeight: 'bold',\n                    marginBottom: '3px'\n                  },\n                  children: dirData.N[currentType].remainTime\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3492,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: dirData.N[currentType].color,\n                    fontSize: '20px',\n                    lineHeight: '20px'\n                  },\n                  children: currentType === 'left' ? '\\u{1F87A}' : currentType === 'straight' ? '\\u{1F87B}' : '\\u{1F878}'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3493,\n                  columnNumber: 21\n                }, this)]\n              }, currentType, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3491,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3464,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 3,\n              gridColumn: 2,\n              textAlign: 'center',\n              display: 'flex',\n              justifyContent: 'center',\n              width: '100%'\n            },\n            children: typeOrder.map(type => dirData.S[type] && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginRight: type === 'right' ? 0 : '10px',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: dirData.S[type].color,\n                  fontSize: '20px',\n                  lineHeight: '20px'\n                },\n                children: type === 'left' ? '\\u{1F878}' : type === 'straight' ? '\\u{1F879}' : '\\u{1F87A}'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3505,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: dirData.S[type].color,\n                  fontWeight: 'bold',\n                  marginTop: '3px'\n                },\n                children: dirData.S[type].remainTime\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3508,\n                columnNumber: 19\n              }, this)]\n            }, type, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3504,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3502,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 2,\n              gridColumn: 3,\n              textAlign: 'center'\n            },\n            children: typeOrder.map((type, index) => {\n              // 反转左转和右转在数组中的顺序\n              let displayIndex = index;\n              if (index === 0) displayIndex = 2;else if (index === 2) displayIndex = 0;\n              const currentType = typeOrder[displayIndex];\n              return dirData.E[currentType] && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '8px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'flex-start'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: dirData.E[currentType].color,\n                    fontSize: '20px',\n                    lineHeight: '20px'\n                  },\n                  children: currentType === 'left' ? '\\u{1F87B}' : currentType === 'straight' ? '\\u{1F878}' : '\\u{1F879}'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3526,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    color: dirData.E[currentType].color,\n                    fontWeight: 'bold',\n                    marginLeft: '5px'\n                  },\n                  children: dirData.E[currentType].remainTime\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3529,\n                  columnNumber: 21\n                }, this)]\n              }, currentType, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3525,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3515,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 2,\n              gridColumn: 1,\n              textAlign: 'center'\n            },\n            children: typeOrder.map(type => dirData.W[type] && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'flex-end'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: dirData.W[type].color,\n                  fontWeight: 'bold',\n                  marginRight: '5px'\n                },\n                children: dirData.W[type].remainTime\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3544,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: dirData.W[type].color,\n                  fontSize: '20px',\n                  lineHeight: '20px'\n                },\n                children: type === 'left' ? '\\u{1F879}' : type === 'straight' ? '\\u{1F87A}' : '\\u{1F87B}'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3550,\n                columnNumber: 19\n              }, this)]\n            }, type, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3543,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3541,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3451,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            fontSize: '11px',\n            color: '#888',\n            textAlign: 'center'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3557,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3448,\n        columnNumber: 9\n      }, this);\n    } else {\n      // 没有相位数据时显示的内容\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          width: '260px',\n          background: 'rgba(0,0,0,0.05)',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CompassIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3566,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '10px',\n            fontSize: '15px',\n            textAlign: 'center'\n          },\n          children: intersection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3567,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '20px 0',\n            color: '#ff9800',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            background: 'rgba(255,255,255,0.1)',\n            borderRadius: '8px',\n            marginBottom: '10px'\n          },\n          children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3568,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#888',\n            textAlign: 'center'\n          },\n          children: [\"\\u8DEF\\u53E3ID: \", interId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3580,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            fontSize: '11px',\n            color: '#888',\n            textAlign: 'center'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3583,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3565,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 设置弹窗位置在左上角区域\n    const x = 445;\n    const y = 250;\n\n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n\n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n\n    // 更新弹窗状态\n    if (window._setTrafficLightPopover) {\n      window._setTrafficLightPopover({\n        visible: true,\n        interId: interId,\n        position: {\n          x,\n          y\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n\n      // 设置定时更新\n      if (window.trafficLightUpdateTimerRef) {\n        window.trafficLightUpdateTimerRef.current = setInterval(() => {\n          window.showTrafficLightPopup(interId);\n        }, 1000);\n      }\n      return true;\n    } else {\n      console.error('无法找到setTrafficLightPopover函数');\n      return false;\n    }\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = object => {\n  let current = object;\n\n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n\n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n\n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n\n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n\n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n\n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = (x - rect.left) / canvas.clientWidth * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    console.log('归一化坐标:', mouseX, mouseY);\n\n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n\n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n\n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'position:', intersect.object.position.toArray(), 'userData:', intersect.object.userData);\n\n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      return true;\n    }\n\n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', '类型:', obj.type, '位置:', obj.position.toArray(), '距离:', intersect.distance, 'userData:', obj.userData);\n    });\n\n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        var _lightObj$intersectio;\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n\n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n\n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n\n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        if (isVisible) {\n          visibleCount++;\n        }\n        console.log(`红绿灯 ${interId}:`, {\n          名称: ((_lightObj$intersectio = lightObj.intersection) === null || _lightObj$intersectio === void 0 ? void 0 : _lightObj$intersectio.name) || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n\n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  var _trafficLight$interse, _trafficLight$interse2, _trafficLight$interse3;\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch (phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n\n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: (_trafficLight$interse = trafficLight.intersection) === null || _trafficLight$interse === void 0 ? void 0 : _trafficLight$interse.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n\n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = {\n    isLight: true\n  };\n\n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  console.log(`更新路口 ${((_trafficLight$interse2 = trafficLight.intersection) === null || _trafficLight$interse2 === void 0 ? void 0 : _trafficLight$interse2.name) || ((_trafficLight$interse3 = trafficLight.intersection) === null || _trafficLight$interse3 === void 0 ? void 0 : _trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "SkeletonUtils", "mqtt", "Select", "Popover", "intersectionsData", "devicesData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "peopleBaseModel", "skeleton", "lastPosition", "lastRotation", "ALPHA", "vehicleLastPositions", "Map", "vehicleLastRotations", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "process", "env", "REACT_APP_API_URL", "console", "log", "vehicleModels", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "clock", "Clock", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "vehicleId", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "has", "lastPos", "get", "distance", "distanceTo", "MAX_DISTANCE_THRESHOLD", "toFixed", "filteredPos", "Vector3", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "lastRot", "MAX_ANGLE_THRESHOLD", "abs", "TRAFFIC_LIGHT_UPDATE_INTERVAL", "mixersToCleanup", "Set", "bonesMap", "resourceManager", "mixers", "bones", "actions", "models", "addMixer", "mixer", "model", "add", "traverse", "object", "isBone", "uuid", "addAction", "action", "removeMixer", "for<PERSON>ach", "stop", "delete", "stopAllAction", "root", "getRoot", "animations", "length", "uncacheRoot", "uncacheAction", "uncacheClip", "clips", "_actions", "map", "a", "_clip", "filter", "Boolean", "clip", "e", "cleanup", "clear", "bone", "parent", "remove", "matrix", "identity", "matrixWorld", "<PERSON><PERSON><PERSON>", "geometry", "dispose", "material", "createAnimationMixer", "AnimationMixer", "createAction", "clipAction", "processedMessageIds", "CampusModel", "className", "onCurrentRSUChange", "selectedRSUs", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "mapRef", "lastCameraPosition", "lastCameraTarget", "cameraSmoothing", "prevAnimationTimeRef", "Date", "now", "peopleAnimationMixers", "peopleAnimations", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "currentPopoverIdRef", "trafficLightUpdateTimerRef", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "switchToFollowView", "current", "enabled", "switchToGlobalView", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "target", "currentTarget", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "updateMatrix", "updateMatrixWorld", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "相机位置", "toArray", "目标点", "hasTrafficLight", "setTimeout", "showTrafficLightPopup", "handleMqttMessage", "message", "payload", "JSON", "parse", "_payload$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "participants", "rsuid", "participant", "id", "partPtcId", "type", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "newModel", "height", "rotation", "scale", "play", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "p", "modelData", "bsmData", "bsmid", "newState", "partLong", "partLat", "postMessage", "source", "initialPosition", "initialRotation", "newPosition", "vehicleObj", "newVehicleModel", "child", "newMaterial", "emissive", "Color", "transparent", "needsUpdate", "speedLabel", "createTextSprite", "round", "r", "g", "b", "textColor", "renderOrder", "opacity", "is<PERSON><PERSON>", "Out", "filteredPosition", "timeSinceLastUpdate", "undefined", "originalTran<PERSON>arent", "originalOpacity", "m", "phasesInfo", "phase", "phaseId", "toString", "direction", "trafficDirec", "getDirectionFromCode", "getPhaseDirection", "lightState", "trafficLight", "remainTime", "parseInt", "phaseInfo", "push", "trafficLightKey", "String", "trafficLightModel", "updateTrafficLightVisual", "prev", "<PERSON><PERSON><PERSON>", "strId", "numId", "updateTime", "rsiData", "rsuId", "events", "rtes", "event", "eventId", "rteId", "eventType", "description", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "messageId", "size", "idsArray", "from", "stringify", "onerror", "onclose", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "setIsVehicleLoaded", "xhr", "loaded", "total", "initializeScene", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "deltaTime", "<PERSON><PERSON><PERSON><PERSON>", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "targetCameraPosition", "targetLookAt", "lerp", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "removeAll", "objectsToRemove", "obj", "setAnimationLoop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "forceContextLoss", "handleMainVehicleChange", "intervalId", "setInterval", "removeEventListener", "clearInterval", "timer", "createTrafficLights", "clearTimeout", "handleClick", "handleMouseClick", "initScene", "createSimpleTrafficLight", "BoxGeometry", "MeshBasicMaterial", "<PERSON><PERSON>", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "addClickHelpers", "lightObj", "helperGeometry", "SphereGeometry", "helperMaterial", "depthWrite", "helper<PERSON><PERSON>", "userData", "isClickHelper", "firstTrafficLightIntersection", "targetIntersection", "renderEntranceDeviceIcons", "converterInstance", "isEntranceDeviceIcons", "entrances", "entrance", "isNaN", "devices", "d", "group", "iconSize", "iconSpacing", "totalWidth", "size3D", "spacing3D", "startX", "device", "idx", "textureLoader", "TextureLoader", "iconPath", "iconMaterial", "bgMaterial", "bgWidth", "bgHeight", "bgGeometry", "PlaneGeometry", "bg<PERSON><PERSON>", "iconGeometry", "<PERSON><PERSON><PERSON>", "iconGroup", "deviceId", "deviceType", "isEntranceDeviceIcon", "warn", "animateBillboard", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "onChange", "options", "label", "bordered", "dropdownStyle", "maxHeight", "ref", "max<PERSON><PERSON><PERSON>", "right", "background", "onClick", "handleClosePopover", "_c", "text", "parameters", "params", "fontFace", "borderThickness", "borderColor", "canvas", "document", "createElement", "context", "getContext", "textWidth", "measureText", "font", "textBaseline", "radius", "beginPath", "moveTo", "lineTo", "arcTo", "closePath", "strokeStyle", "lineWidth", "stroke", "fillStyle", "fill", "textAlign", "fillText", "texture", "CanvasTexture", "minFilter", "LinearFilter", "spriteMaterial", "SpriteMaterial", "sprite", "Sprite", "depthTest", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "trafficLightGltf", "vehicleGltf", "cyclistGltf", "peopleGltf", "all", "loadAsync", "materia", "<PERSON><PERSON><PERSON><PERSON>", "err", "types", "then", "catch", "createFallbackTrafficLights", "Error", "side", "DoubleSide", "compassTextureLoader", "compassIconPath", "compassMaterial", "compassGeometry", "compassMesh", "colliderGeometry", "colliderMaterial", "collider", "isCollider", "lightGeometry", "lightMaterial", "<PERSON><PERSON><PERSON>", "dirCode", "container", "sceneInstance", "cameraInstance", "clientX", "clientY", "rect", "getBoundingClientRect", "mouseX", "clientWidth", "mouseY", "clientHeight", "raycaster", "Raycaster", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "trafficLightObjects", "trafficLightIntersects", "intersectObjects", "intersect", "index", "getTrafficLightFromObject", "correctId", "intersects", "closestLight", "worldPos", "getWorldPosition", "screenPos", "project", "dx", "dy", "sqrt", "setPopoverState", "testTrafficLightClick", "_document$querySelect", "_document$querySelect2", "light", "lightModel", "stateInfo", "overflowY", "marginBottom", "borderBottom", "paddingBottom", "lightColor", "justifyContent", "marginTop", "toLocaleTimeString", "centerX", "centerY", "__REACT_INSTANCE", "popover", "innerHTML", "body", "closeButton", "listTrafficLights", "list", "numericId", "hasPhaseData", "compassStyle", "alignItems", "CompassIcon", "flexDirection", "borderLeft", "borderRight", "phaseMap", "dir", "typeOrder", "colorMap", "G", "Y", "R", "dirD<PERSON>", "N", "E", "S", "W", "gridTemplateRows", "gridTemplateColumns", "margin", "gridRow", "gridColumn", "displayIndex", "currentType", "marginStyle", "marginRight", "marginLeft", "testClickDetection", "tlIntersects", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "distanceToCamera", "名称", "可见性", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "isLight", "emissiveIntensity", "PointLight", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js';\n\n\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\nimport devicesData from '../data/devices.json'; // 导入设备数据\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel= null; // 存储原始模型数据\nlet skeleton =null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  const lastPos = vehicleLastPositions.get(vehicleId);\n  \n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n  \n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  \n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  \n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const lastRot = vehicleLastRotations.get(vehicleId);\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  \n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\n// 在文件顶部添加\nconst mixersToCleanup = new Set();\nconst bonesMap = new Map();\n\n// 在文件顶部添加资源管理器\nconst resourceManager = {\n  mixers: new Set(),\n  bones: new Map(),\n  actions: new Map(),\n  models: new Set(),\n  \n  addMixer(mixer, model) {\n    this.mixers.add(mixer);\n    if (model) {\n      this.models.add(model);\n      // 记录骨骼\n      model.traverse(object => {\n        if (object.isBone) {\n          this.bones.set(object.uuid, object);\n        }\n      });\n    }\n    return mixer;\n  },\n  \n  addAction(action, mixer) {\n    if (!this.actions.has(mixer)) {\n      this.actions.set(mixer, new Set());\n    }\n    this.actions.get(mixer).add(action);\n    return action;\n  },\n  \n  removeMixer(mixer) {\n    if (this.mixers.has(mixer)) {\n      try {\n        // 停止并清理所有动作\n        if (this.actions.has(mixer)) {\n          this.actions.get(mixer).forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          this.actions.delete(mixer);\n        }\n        \n        // 停止混合器\n        if (typeof mixer.stopAllAction === 'function') {\n          mixer.stopAllAction();\n        }\n        \n        // 清理混合器的根对象\n        const root = mixer.getRoot();\n        if (root) {\n          this.models.delete(root);\n          root.traverse(object => {\n            if (object && object.isBone) {\n              this.bones.delete(object.uuid);\n            }\n            if (object && object.animations) {\n              object.animations.length = 0;\n            }\n          });\n          \n          // 安全地清理缓存\n          try {\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            \n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n            \n            // 这里是发生错误的地方，添加防御性检查\n            if (typeof mixer.uncacheClip === 'function') {\n              // 不直接调用uncacheClip(null)，而是获取混合器中的所有动画片段并逐个清理\n              const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n              clips.forEach(clip => {\n                if (clip && clip.uuid) {\n                  mixer.uncacheClip(clip);\n                }\n              });\n            }\n          } catch (e) {\n            console.log('在清理动画混合器时发生非致命错误:', e);\n            // 继续执行其余清理操作\n          }\n        }\n        \n        this.mixers.delete(mixer);\n      } catch (error) {\n        console.error('清理动画混合器时发生错误:', error);\n        // 确保即使出错也会从集合中移除\n        this.mixers.delete(mixer);\n      }\n    }\n  },\n  \n  cleanup() {\n    try {\n      // 清理动作\n      this.actions.forEach((actions, mixer) => {\n        try {\n          actions.forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          actions.clear();\n        } catch (e) {\n          console.log('清理动作时发生非致命错误:', e);\n        }\n      });\n      this.actions.clear();\n      \n      // 清理混合器\n      this.mixers.forEach(mixer => {\n        try {\n          if (typeof mixer.stopAllAction === 'function') {\n            mixer.stopAllAction();\n          }\n          \n          const root = mixer.getRoot();\n          if (root) {\n            root.traverse(object => {\n              if (object && object.animations) {\n                object.animations.length = 0;\n              }\n            });\n            \n            // 安全清理\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            \n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n            \n            // 安全清理动画片段\n            try {\n              if (mixer._actions && Array.isArray(mixer._actions)) {\n                const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n                clips.forEach(clip => {\n                  if (clip && clip.uuid && typeof mixer.uncacheClip === 'function') {\n                    mixer.uncacheClip(clip);\n                  }\n                });\n              }\n            } catch (e) {\n              console.log('清理动画片段时发生非致命错误:', e);\n            }\n          }\n        } catch (e) {\n          console.log('清理混合器时发生非致命错误:', e);\n        }\n      });\n      this.mixers.clear();\n      \n      // 清理骨骼\n      this.bones.forEach(bone => {\n        if (bone.parent) {\n          bone.parent.remove(bone);\n        }\n        if (bone.matrix) bone.matrix.identity();\n        if (bone.matrixWorld) bone.matrixWorld.identity();\n      });\n      this.bones.clear();\n      \n      // 清理模型\n      this.models.forEach(model => {\n        if (model.parent) {\n          model.parent.remove(model);\n        }\n        model.traverse(object => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n          }\n          if (object.animations) {\n            object.animations.length = 0;\n          }\n        });\n      });\n      this.models.clear();\n    } catch (error) {\n      console.error('全局清理过程中发生错误:', error);\n      // 即使发生错误也尝试清空集合\n      this.actions.clear();\n      this.mixers.clear();\n      this.bones.clear();\n      this.models.clear();\n    }\n  }\n};\n\n// 修改创建动画混合器的函数\nconst createAnimationMixer = (model) => {\n  const mixer = new THREE.AnimationMixer(model);\n  return resourceManager.addMixer(mixer, model);\n};\n\n// 修改动画动作创建的函数\nconst createAction = (clip, mixer, model) => {\n  const action = mixer.clipAction(clip, model);\n  return resourceManager.addAction(action, mixer);\n};\n\n// 在CampusModel组件顶部添加消息缓存\nconst processedMessageIds = new Set();\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n  \n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n  \n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = new Map(); // 使用全局Map存储所有行人的动画混合器（不再使用useRef）\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n  \n  \n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n  \n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n  \n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n  \n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n  \n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '65px',  // 从 60px 改为 65px\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '65px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n      \n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n      \n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n        \n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos)\n          .to({ x: 0, y: 300, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.position.copy(currentPos);\n          })\n          .start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp)\n          .to({ x: 0, y: 1, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.up.copy(currentUp);\n          })\n          .start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n        \n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget)\n          .to({ x: 0, y: 0, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            controls.target.copy(currentTarget);\n            // 确保相机始终朝向目标点\n            cameraRef.current.lookAt(controls.target);\n            controls.update();\n          })\n          .start();\n\n        // 启用控制器\n        controls.enabled = true;\n        \n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x+50, 70, -modelCoords.y+50);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n      \n      // 如果该路口有红绿灯，自动显示红绿灯弹窗\n      if (intersection.hasTrafficLight !== false && intersection.interId) {\n        console.log(`路口 ${intersection.name} 有红绿灯，准备显示红绿灯状态`);\n        \n        // 延迟300ms调用以确保场景已更新\n        setTimeout(() => {\n          // 检查多种可能的ID格式\n          let interId = intersection.interId;\n          \n          // 使用全局的showTrafficLightPopup函数显示红绿灯弹窗\n          if (window.showTrafficLightPopup) {\n            window.showTrafficLightPopup(interId);\n            console.log(`已触发路口 ${intersection.name} (ID: ${interId}) 的红绿灯弹窗显示`);\n          } else {\n            console.error('找不到显示红绿灯弹窗的函数');\n          }\n        }, 300);\n      } else {\n        console.log(`路口 ${intersection.name} 没有红绿灯或缺少ID，不显示红绿灯状态`);\n        \n        // 如果有弹窗正在显示，则关闭它\n        if (window._setTrafficLightPopover) {\n          window._setTrafficLightPopover({\n            visible: false\n          });\n        }\n      }\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        // console.log('收到RSM消息:', payload);\n        \n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n        \n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n        \n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n          return;\n        }\n        \n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        \n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n        \n        const participants = payload.data?.participants || [];\n        const rsuid = payload.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id =  deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n          \n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            \n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n            \n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1': // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2': // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3': // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return; // 跳过未知类型\n            }\n            \n            // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = type === '3' ? SkeletonUtils.clone(preloadedPeopleModel) : preloadedModel.clone();\n              // 根据类型调整高度和缩放\n              const height = type === '3' ? 2.0 : 1.0;\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              \n              // 如果是行人类型，设置缩放和创建动画\n              if (type === '3') {\n              // newModel.scale.set(0.005, 0.005, 0.005);\n                newModel.scale.set(4, 4, 4);\n                \n                // 使用resourceManager创建并管理动画混合器\n                const mixer = createAnimationMixer(newModel);\n                \n                if (peopleBaseModel && peopleBaseModel.animations && peopleBaseModel.animations.length > 0) {\n                  // 只创建一个动作并添加到资源管理器\n                  const action = createAction(peopleBaseModel.animations[0], mixer, newModel);\n                  action.play();\n                }\n                \n                // 保存到全局Map中(不再使用useRef)\n                peopleAnimationMixers.set(id, mixer);\n              }\n              \n              scene.add(newModel);\n              \n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n              type: type\n              });\n          } else if (model) {\n              // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n        \n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 1000;\n        const currentIds = new Set(participants.map(p => deviceMac + p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            // 如果是行人，清理动画混合器\n            if (modelData.type === '3' && peopleAnimationMixers.has(id)) {\n              const mixer = peopleAnimationMixers.get(id);\n              // 使用resourceManager清理混合器\n              resourceManager.removeMixer(mixer);\n              peopleAnimationMixers.delete(id);\n            }\n            \n            // 从场景移除模型\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n        \n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n        \n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n        \n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid, // 直接传递车辆ID\n          data: {       // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n        \n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n        \n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n        \n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n        \n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        \n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          \n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n          \n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n              \n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n              \n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n          \n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n          \n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          \n          scene.add(newVehicleModel);\n          \n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n          \n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position)\n            .to({ y: 0.5 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .start();\n          \n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({ opacity: 0.6 })\n                .to({ opacity: 1.0 }, 500)\n                .easing(TWEEN.Easing.Quadratic.Out)\n                .onUpdate(function() {\n                  child.material.opacity = this.opacity;\n                  child.material.needsUpdate = true;\n                })\n                .start();\n            }\n          });\n          \n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({ opacity: 0.6 })\n            .to({ opacity: 1.0 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .onUpdate(function() {\n              speedLabel.material.opacity = this.opacity;\n              speedLabel.material.needsUpdate = true;\n            })\n            .start();\n          \n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n          \n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n          \n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n          \n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n          \n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n        \n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 增加到10秒，避免频繁清理造成闪烁\n        \n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n          \n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1 ;\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n                \n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n            \n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n            \n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            \n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        \n        return;\n      }\n      \n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n        \n        try {\n          const payload = JSON.parse(message);\n          \n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n          \n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n          \n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n          \n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n          \n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              \n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              \n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n              \n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                \n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  \n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? \n                    getDirectionFromCode(phase.trafficDirec) : \n                    getPhaseDirection(phaseId);\n                  \n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  \n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n                  \n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n                  \n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n                  \n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  \n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  \n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n                    \n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n                \n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                \n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n                  \n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && \n                     (window.currentPopoverIdRef.current === modelKey || \n                      window.currentPopoverIdRef.current === String(modelKey) || \n                      window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    \n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n                    \n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n        return;\n      }\n      \n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件，确保包含mac和timestamp\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data,\n          mac: payload.mac,\n          tm: payload.tm\n        }, '*');\n        \n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n        \n        return;\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n        \n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 检查消息ID是否已处理过\n          if (message.messageId) {\n            if (processedMessageIds.has(message.messageId)) {\n              // console.log('跳过重复消息:', message.messageId);\n              return;\n            }\n            \n            // 添加到已处理集合\n            processedMessageIds.add(message.messageId);\n            \n            // 限制缓存大小，防止内存泄漏\n            if (processedMessageIds.size > 1000) {\n              // 转换为数组，删除最早的100个元素\n              const idsArray = Array.from(processedMessageIds);\n              for (let i = 0; i < 100; i++) {\n                processedMessageIds.delete(idsArray[i]);\n              }\n            }\n          }\n          \n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/vehicle.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n        \n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          \n          // 检查scene是否初始化\n          if (scene) {\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画 - 只更新存在的混合器\n      const deltaTime = clock.getDelta();\n      \n      // 使用Map而不是ref.current\n      if (peopleAnimationMixers.size > 0) {\n        peopleAnimationMixers.forEach((mixer) => {\n          mixer.update(deltaTime);\n        });\n      }\n\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          200,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n        \n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        \n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n        \n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n        \n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 停止渲染循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 停止所有 TWEEN 动画\n      TWEEN.removeAll();\n\n      // 3. 清理所有动画混合器\n      peopleAnimationMixers.forEach(mixer => {\n        resourceManager.removeMixer(mixer);\n      });\n      peopleAnimationMixers.clear();\n\n      // 4. 清理所有其他资源\n      resourceManager.cleanup();\n\n      // 5. 清理场景中的所有对象\n      if (scene) {\n        const objectsToRemove = [];\n        scene.traverse((object) => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n            if (object !== scene) {\n              objectsToRemove.push(object);\n            }\n          }\n        });\n        \n        // 从场景中移除对象\n        objectsToRemove.forEach(obj => {\n          if (obj.parent) {\n            obj.parent.remove(obj);\n          }\n        });\n        \n        scene.clear();\n      }\n\n      // 6. 清理渲染器\n      if (renderer) {\n        renderer.setAnimationLoop(null);\n        if (containerRef.current && renderer.domElement && renderer.domElement.parentNode === containerRef.current) {\n          containerRef.current.removeChild(renderer.domElement);\n        }\n        renderer.dispose();\n        renderer.forceContextLoss();\n      }\n\n      // 7. 清理其他资源\n      if (controls) {\n        controls.dispose();\n      }\n\n      // 8. 清理数据结构\n      vehicleLastPositions.clear();\n      vehicleLastRotations.clear();\n      deviceTimestamps.clear();\n      vehicleModels.clear();\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n    \n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n    \n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n    \n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n    \n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 2000);\n      \n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n  \n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current);\n        }\n      };\n      \n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n      \n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n      \n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n    \n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    \n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n    \n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,//\n          transparent: false,\n          opacity: 0.1,  // 几乎透明\n          depthWrite: false\n        });\n        \n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置\n        \n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n        \n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        \n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500);  // 延迟略长于debugTrafficLights\n    \n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n    \n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n  \n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        // 查找第一个带有红绿灯的路口\n        const firstTrafficLightIntersection = intersectionsData.intersections.find(\n          intersection => intersection.hasTrafficLight !== false && intersection.interId\n        );\n        \n        // 如果找到带红绿灯的路口，优先选择它；否则选择第一个路口\n        const targetIntersection = firstTrafficLightIntersection || intersectionsData.intersections[0];\n        \n        console.log('自动选择路口:', targetIntersection.name, \n                    '具有红绿灯:', firstTrafficLightIntersection ? '是' : '否');\n        \n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(targetIntersection.name);\n        }, 2000);\n        \n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n\n  // 新增：在场景初始化后渲染所有路口entrance的设备图标\n  const renderEntranceDeviceIcons = (converterInstance) => {\n      if (!scene || typeof scene.traverse !== 'function' || !converterInstance) return;\n      try {\n        // 先移除所有旧的设备图标组（用 children.filter 更安全）\n        scene.children\n          .filter(obj => obj.userData && obj.userData.isEntranceDeviceIcons)\n          .forEach(obj => scene.remove(obj));\n        // 遍历所有路口\n        intersectionsData.intersections.forEach(intersection => {\n          if (!intersection.entrances || !Array.isArray(intersection.entrances)) return;\n          // 遍历每个路口的所有 entrance\n          intersection.entrances.forEach((entrance) => {\n            // 检查经纬度有效性\n            if (!entrance.longitude || !entrance.latitude || isNaN(parseFloat(entrance.longitude)) || isNaN(parseFloat(entrance.latitude))) {\n              return; // 跳过无效经纬度的入口\n            }\n            \n            // 查找该路口该entrance下所有设备\n            const devices = devicesData.devices.filter(\n              d => d.location === intersection.name && d.entrance === entrance.name\n            );\n            if (devices.length === 0) return;\n            // 经纬度转模型坐标\n            console.log('渲染路口', intersection.name, '入口', entrance.name, '设备数量', devices.length);\n            const modelPos = converterInstance.wgs84ToModel(\n              parseFloat(entrance.longitude),\n              parseFloat(entrance.latitude)\n            );\n            // 创建一个组用于存放所有图标\n            const group = new THREE.Group();\n            group.position.set(modelPos.x, 15, -modelPos.y); // 上方15米\n            group.userData = { isEntranceDeviceIcons: true };\n            // 图标排成一排，居中\n            const iconSize = 24; // px\n            const iconSpacing = 8; // px\n            const totalWidth = devices.length * iconSize + (devices.length - 1) * iconSpacing;\n            // 以三维单位为准，假设1单位=1米，24px约等于0.6米\n            const size3D = 2.0; // 图标尺寸加大\n            const spacing3D = 0.5; // 图标间距加大\n            const startX = -((devices.length - 1) * (size3D + spacing3D)) / 2;\n            devices.forEach((device, idx) => {\n              // 创建一个平面用于显示SVG图标\n              const textureLoader = new THREE.TextureLoader();\n              const iconPath = `${BASE_URL}/images/${device.type}.svg`;\n              // 图标材质\n              const iconMaterial = new THREE.MeshBasicMaterial({\n                map: textureLoader.load(iconPath),\n                transparent: true,\n                opacity: 1 // 图标完全不透明\n              });\n              // 图标背景板材质\n              const bgMaterial = new THREE.MeshBasicMaterial({\n                color: 0x000000,\n                transparent: true,\n                opacity: 0.7 // 半透明黑色\n              });\n              // 背景板尺寸略大于图标\n              const bgWidth = size3D * 1.25;\n              const bgHeight = size3D * 1.25;\n              // 背景板几何体（圆角矩形）\n              // 由于Three.js没有内置圆角矩形，使用PlaneGeometry近似\n              const bgGeometry = new THREE.PlaneGeometry(bgWidth, bgHeight);\n              const bgMesh = new THREE.Mesh(bgGeometry, bgMaterial);\n              // 图标几何体\n              const iconGeometry = new THREE.PlaneGeometry(size3D, size3D);\n              const iconMesh = new THREE.Mesh(iconGeometry, iconMaterial);\n              // 图标略微前移，避免Z轴重叠闪烁\n              iconMesh.position.set(0, 0, 0.01);\n              // 创建一个组，包含背景和图标\n              const iconGroup = new THREE.Group();\n              iconGroup.add(bgMesh);\n              iconGroup.add(iconMesh);\n              // 整体平移到正确位置\n              iconGroup.position.set(startX + idx * (size3D + spacing3D), 0, 0);\n              // 不再固定旋转角度，后续在动画循环中让其始终面向相机\n              iconGroup.renderOrder = 999; // 提高渲染优先级\n              iconGroup.userData = {\n                deviceId: device.id,\n                deviceType: device.type,\n                entrance: entrance.name,\n                isEntranceDeviceIcon: true\n              };\n              group.add(iconGroup);\n            });\n            scene.add(group);\n          });\n        });\n      } catch (e) {\n        console.warn('scene.traverse error', e);\n        return;\n      }\n    };\n\n  // 在场景初始化后调用渲染设备图标\n  useEffect(() => {\n    if (scene && typeof scene.traverse === 'function' && converter.current) {\n      renderEntranceDeviceIcons(converter.current);\n    }\n  }, [scene, converter.current]);\n\n  // 新增：每帧让所有设备图标组始终面向相机（billboard效果）\n  useEffect(() => {\n    if (!scene || !cameraRef.current) return;\n    const animateBillboard = () => {\n      // 遍历所有设备图标组，让其正对相机\n      scene.children.forEach(obj => {\n        if (obj.userData && obj.userData.isEntranceDeviceIcons) {\n          obj.lookAt(cameraRef.current.position.x, obj.position.y, cameraRef.current.position.z);\n        }\n      });\n      requestAnimationFrame(animateBillboard);\n    };\n    animateBillboard();\n  }, [scene]);\n\n  return (\n    <>\n      <span style={labelStyle}>区域选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择区域位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n        value={selectedIntersection ? selectedIntersection.name : undefined}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      \n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '4px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n            padding: '0',\n            maxWidth: '240px', // 缩小最大宽度\n            fontSize: '12px' // 缩小字体\n          }}\n        >\n          {trafficLightPopover.content}\n          <button \n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '12px',\n              cursor: 'pointer',\n              padding: '2px 6px'\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n      \n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 12, // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    backgroundColor: parameters.backgroundColor || { r: 255, g: 255, b: 255, a: 0.8 },\n    textColor: parameters.textColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  \n  // 设置字体\n  // context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  \n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n  \n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  \n  canvas.width = width;\n  canvas.height = height;\n  \n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n  \n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n  \n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n  \n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n  \n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n  \n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n  \n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n  \n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = { \n    text: text,\n    params: params\n  };\n  \n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    try {\n      const [ trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([\n        loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`)\n        \n    ]);\n\n\n\n    // 处理机动车模型\n    console.log('加载车辆模型...');\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n          color: 0xff0000,  // 0xff0000, //红色 0xffffff,白色\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n      }\n    });\n\n    console.log('加载非机动车模型...');\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    console.log('加载行人模型...');\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n        \n      }\n      if (child.isMesh){\n        child.castShadow = true;\n      }\n    });\n\n\n\n    // 保存行人动画数据\n    console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n    if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n      console.log('找到行人动画:', peopleGltf.animations.length, '个');\n      peopleBaseModel = peopleGltf;\n    } else {\n      console.warn('行人模型没有包含动画数据');\n    }\n\n    console.log('加载红绿灯模型...');\n      \n    // 处理红绿灯模型\n    preloadedTrafficLightModel = trafficLightGltf.scene;\n    console.log('红绿灯模型：', preloadedTrafficLightModel);\n    // 设置红绿灯模型的缩放\n    preloadedTrafficLightModel.scale.set(6, 6, 6);\n    // 保持原始材质\n    preloadedTrafficLightModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 设置材质属性\n        child.material.metalness = 0.2;\n        child.material.roughness = 0.3;\n        child.material.envMapIntensity = 1.2;\n    }\n  });\n\n    console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n      \n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n        \n        // // 尝试单独加载红绿灯模型\n        // if (!preloadedTrafficLightModel) {\n        //   console.log('正在单独加载红绿灯模型...');\n        //   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n        //   preloadedTrafficLightModel = trafficLightGltf.scene;\n        //   preloadedTrafficLightModel.scale.set(3, 3, 3);\n        //   console.log('红绿灯模型加载成功');\n        // }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  \n  try {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在1秒后自动移除\n  setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n    scene.remove(sprite);\n      }\n  }, 100);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  // console.log('添加场景事件标记:', {\n  //   位置: position,\n  //   文本: text,\n  //   颜色: color\n  // });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  \n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  \n  // 检查红绿灯模型是否已加载\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载，尝试重新加载...');\n    // 尝试重新加载红绿灯模型\n    const loader = new GLTFLoader();\n    loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)\n      .then(trafficLightGltf => {\n        preloadedTrafficLightModel = trafficLightGltf.scene;\n        preloadedTrafficLightModel.scale.set(6, 6, 6);\n        preloadedTrafficLightModel.traverse((child) => {\n          if (child.isMesh && child.material) {\n            child.material.metalness = 0.2;\n            child.material.roughness = 0.3;\n            child.material.envMapIntensity = 1.2;\n          }\n        });\n        console.log('红绿灯模型重新加载成功，开始创建红绿灯...');\n        // 重新调用创建函数\n        createTrafficLights(converterInstance);\n      })\n      .catch(error => {\n        console.error('红绿灯模型重新加载失败:', error);\n        // 如果加载失败，使用简单的替代物体\n        createFallbackTrafficLights(converterInstance);\n      });\n    return;\n  }\n  \n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      \n      try {\n        // 确保模型存在且可以克隆\n        if (!preloadedTrafficLightModel || !preloadedTrafficLightModel.clone) {\n          throw new Error('红绿灯模型无效或无法克隆');\n        }\n        \n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n        \n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n        \n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n        \n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n        \n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n        \n        // 设置材质属性\n        trafficLightModel.traverse(child => {\n          if (child.isMesh) {\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            child.renderOrder = 100;\n          }\n        });\n        \n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        \n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n\n        // ========== 新增：在红绿灯地面添加指北针图标 =============\n        // 创建一个平面用于显示指北针SVG图标\n        const compassTextureLoader = new THREE.TextureLoader();\n        const compassIconPath = '${BASE_URL}/images/compass.svg'; // public目录下的路径\n        const compassMaterial = new THREE.MeshBasicMaterial({\n          map: compassTextureLoader.load(compassIconPath),\n\n          transparent: false,\n          opacity: 1\n        });\n        // 平面几何体，6x6米\n        const compassGeometry = new THREE.PlaneGeometry(3, 3);\n        const compassMesh = new THREE.Mesh(compassGeometry, compassMaterial);\n        // 指北针放在红绿灯正下方地面（y=0.1，略高于地面防止Z冲突）\n        compassMesh.position.set(modelPos.x, 4, -modelPos.y);\n        // 指北针朝上，旋转x轴-90度\n        compassMesh.rotation.x = -Math.PI / 2;\n        // 渲染优先级高，避免被地面遮挡\n        compassMesh.renderOrder = 101;\n        // 可选：添加userData标记\n        compassMesh.userData = {\n          type: 'compassIcon',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        // 添加到场景\n        scene.add(compassMesh);\n        // ========== 指北针图标添加结束 =============\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        \n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n  \n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance) => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({ \n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n  \n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n  \n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n  \n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n  \n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,  // 完全透明\n    depthWrite: false\n  });\n  \n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  \n  trafficLightModel.add(collider);\n  \n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n  \n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n  \n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n  \n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  trafficLightModel.add(lightMesh);\n  \n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  switch(phaseId) {\n    case '1': return '北进口左转';\n    case '2': return '北进口直行';\n    case '3': return '北进口右转';\n    case '5': return '东进口左转';\n    case '6': return '东进口直行';\n    case '7': return '东进口右转';\n    case '9': return '南进口左转';\n    case '10': return '南进口直行';\n    case '11': return '南进口右转';\n    case '13': return '西进口左转';\n    case '14': return '西进口直行';\n    case '15': return '西进口右转';\n    default: return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = (dirCode) => {\n  switch(dirCode) {\n    case 'N': return '北向南';\n    case 'S': return '南向北';\n    case 'E': return '东向西';\n    case 'W': return '西向东';\n    case 'NE': return '东北向西南';\n    case 'NW': return '西北向东南';\n    case 'SE': return '东南向西北';\n    case 'SW': return '西南向东北';\n    default: return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  \n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n  \n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n  \n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Line.threshold = 1;\n  \n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  \n  console.log('鼠标点击位置:', mouseX, mouseY);\n  \n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  \n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n  \n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  \n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, \n                 intersect.object.name || '无名称', \n                 '距离:', intersect.distance,\n                 'userData:', intersect.object.userData);\n    });\n    \n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    \n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n      \n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n      \n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n  \n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  \n  console.log('射线检测到的对象数量:', intersects.length);\n  \n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', \n                  'userData:', obj.userData, \n                  '距离:', intersect.distance);\n    });\n    \n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n  \n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n  \n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.1; // 设置一个阈值，只有距离小于此值的才会被选中\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n      \n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n      \n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      \n      console.log(`路口 ${interId} 的距离:`, distance);\n      \n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = { interId, distance };\n      }\n    }\n  });\n  \n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n    \n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  \n  console.log('未检测到任何红绿灯点击');\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n  \n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n  \n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({ \n    ...prev, \n    visible: false,\n    content: null, // 清空内容\n    phases: []     // 清空相位信息\n  }));\n  \n  console.log('弹窗已关闭，所有相关资源已清理');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = (interId) => {\n  try {\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      \n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      \n      return false;\n    }\n    \n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n    \n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 创建弹出窗口内容\n    let content;\n    \n    if (stateInfo && stateInfo.phases) {\n      content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection.name} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              switch (phase.trafficLight) {\n                case 'G': lightColor = '#00ff00'; break;\n                case 'Y': lightColor = '#ffff00'; break;\n                case 'R': default: lightColor = '#ff0000'; break;\n              }\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {getPhaseDirection(phase.phaseId)}\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      content = (\n        <div style={{ padding: '8px', maxWidth: '200px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>\n          <div>路口ID: {interId}</div>\n          <div>当前无信号灯状态信息</div>\n        </div>\n      );\n    }\n    \n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2 -500;\n    const centerY = window.innerHeight / 2 -500;\n    \n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;\n    \n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: { x: centerX, y: centerY },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      \n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      \n      document.body.appendChild(popover);\n      \n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      \n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  \n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  \n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  \n  return list;\n};\n\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = (interId) => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    \n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    \n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      \n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    \n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    \n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 判断是否有相位数据\n    const hasPhaseData = stateInfo && stateInfo.phases && stateInfo.phases.length > 0;\n    \n    let content;\n    \n    // 指北针样式\n    const compassStyle = {\n      position: 'absolute',\n      top: '5px',\n      right: '25px',\n      width: '30px',\n      height: '30px',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      borderRadius: '50%',\n      background: 'rgba(0,0,0,0.1)',\n      zIndex: 10\n    };\n    \n    // 指北针组件\n    const CompassIcon = () => (\n      <div style={compassStyle}>\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          transform: 'rotate(0deg)'\n        }}>\n          <span style={{\n            color: '#ff5252',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            lineHeight: '14px'\n          }}>N</span>\n          <span style={{\n            width: 0,\n            height: 0,\n            borderLeft: '6px solid transparent',\n            borderRight: '6px solid transparent',\n            borderBottom: '10px solid #ff5252',\n            marginTop: '-2px'\n          }}></span>\n        </div>\n      </div>\n    );\n    \n    if (hasPhaseData) {\n      // 相位ID与方向/方式的映射\n      const phaseMap = {\n        '1': { dir: 'N', type: 'left' }, '2': { dir: 'N', type: 'straight' }, '3': { dir: 'N', type: 'right' },\n        '5': { dir: 'E', type: 'left' }, '6': { dir: 'E', type: 'straight' }, '7': { dir: 'E', type: 'right' },\n        '9': { dir: 'S', type: 'left' }, '10': { dir: 'S', type: 'straight' }, '11': { dir: 'S', type: 'right' },\n        '13': { dir: 'W', type: 'left' }, '14': { dir: 'W', type: 'straight' }, '15': { dir: 'W', type: 'right' }\n      };\n      \n      const typeOrder = ['left', 'straight', 'right'];\n      const colorMap = { G: '#00ff00', Y: '#ffff00', R: '#ff0000' };\n      const dirData = { N: {}, E: {}, S: {}, W: {} };\n      \n      stateInfo.phases.forEach(phase => {\n        const map = phaseMap[phase.phaseId];\n        if (map) {\n          dirData[map.dir][map.type] = {\n            color: colorMap[phase.trafficLight] || '#888',\n            remainTime: phase.remainTime\n          };\n        }\n      });\n      \n      content = (\n        <div style={{ padding: '8px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>\n          <CompassIcon />\n          <div style={{ fontWeight: 'bold', marginBottom: '8px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}灯态</div>\n          <div style={{\n            display: 'grid',\n            gridTemplateRows: '60px 60px 60px',\n            gridTemplateColumns: '60px 60px 60px',\n            justifyContent: 'center',\n            alignItems: 'center',\n            background: 'rgba(255,255,255,0.05)',\n            borderRadius: '8px',\n            margin: '0 auto',\n            position: 'relative'\n          }}>\n            {/* 北 - 左转、直行、右转箭头水平对齐，倒计时在箭头上面 */}\n            {/* <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%', paddingLeft: '5px' }}> */}\n            <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}> \n              {typeOrder.map((type, index) => {\n                // 反转左转和右转在数组中的顺序\n                let displayIndex = index;\n                if (index === 0) displayIndex = 2;\n                else if (index === 2) displayIndex = 0;\n                \n                const currentType = typeOrder[displayIndex];\n                \n                // 计算与南边对齐的样式\n                const marginStyle = {};\n                if (currentType === 'left') { // 左转箭头 (右侧显示)\n                  marginStyle.marginRight = '0px';\n                } else if (currentType === 'straight') { // 直行箭头 (中间显示)\n                  marginStyle.marginLeft = '10px';\n                  marginStyle.marginRight = '10px';\n                } else if (currentType === 'right') { // 右转箭头 (左侧显示)\n                  marginStyle.marginLeft = '0px';\n                }\n                \n                return dirData.N[currentType] && (\n                  // <div key={currentType} style={{\n                  //   display: 'flex', \n                  //   flexDirection: 'column', \n                  //   alignItems: 'center',\n                  //   ...marginStyle\n                  // }}>\n                  <div key={currentType} style={{marginRight: currentType === 'left' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>\n                    <div style={{fontSize:'14px', color: dirData.N[currentType].color, fontWeight:'bold', marginBottom: '3px'}}>{dirData.N[currentType].remainTime}</div>\n                    <span style={{color: dirData.N[currentType].color, fontSize:'20px', lineHeight: '20px'}}>\n                      {currentType === 'left' ? '\\u{1F87A}' : currentType === 'straight' ? '\\u{1F87B}' : '\\u{1F878}'}\n                    </span>\n                  </div>\n                );\n              })}\n            </div>\n            \n            {/* 南 - 左转、直行、右转箭头水平对齐，倒计时在箭头下面 */}\n            <div style={{ gridRow: 3, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}>\n              {typeOrder.map(type => dirData.S[type] && (\n                <div key={type} style={{marginRight: type === 'right' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>\n                  <span style={{color: dirData.S[type].color, fontSize:'20px', lineHeight: '20px'}}>\n                    {type === 'left' ? '\\u{1F878}' : type === 'straight' ? '\\u{1F879}' : '\\u{1F87A}'}\n                  </span>\n                  <div style={{fontSize:'14px', color: dirData.S[type].color, fontWeight:'bold', marginTop: '3px'}}>{dirData.S[type].remainTime}</div>\n                </div>\n              ))} \n            </div>\n            \n            {/* 东 - 倒计时显示在箭头右边 */}\n\n            <div style={{ gridRow: 2, gridColumn: 3, textAlign: 'center' }}>\n              {typeOrder.map((type, index) => {\n                // 反转左转和右转在数组中的顺序\n                let displayIndex = index;\n                if (index === 0) displayIndex = 2;\n                else if (index === 2) displayIndex = 0;\n                \n                const currentType = typeOrder[displayIndex];\n                \n                return dirData.E[currentType] && (\n                  <div key={currentType} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-start'}}>\n                    <span style={{color: dirData.E[currentType].color, fontSize:'20px', lineHeight: '20px'}}>\n                      {currentType === 'left' ? '\\u{1F87B}' : currentType === 'straight' ? '\\u{1F878}' : '\\u{1F879}'}\n                    </span>\n                    <div style={{\n                      fontSize:'14px', \n                      color: dirData.E[currentType].color, \n                      fontWeight:'bold', \n                      marginLeft: '5px'\n                    }}>{dirData.E[currentType].remainTime}</div>\n                  </div>\n                );\n              })}\n            </div>\n            \n            {/* 西 - 倒计时显示在箭头左边 */}\n            <div style={{ gridRow: 2, gridColumn: 1, textAlign: 'center' }}>\n              {typeOrder.map(type => dirData.W[type] && (\n                <div key={type} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}>\n                  <div style={{\n                    fontSize:'14px', \n                    color: dirData.W[type].color, \n                    fontWeight:'bold', \n                    marginRight: '5px'\n                  }}>{dirData.W[type].remainTime}</div>\n                  <span style={{color: dirData.W[type].color, fontSize:'20px', lineHeight: '20px'}}>\n                    {type === 'left' ? '\\u{1F879}' : type === 'straight' ? '\\u{1F87A}' : '\\u{1F87B}'}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n          <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      // 没有相位数据时显示的内容\n      content = (\n        <div style={{ padding: '15px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>\n          <CompassIcon />\n          <div style={{ fontWeight: 'bold', marginBottom: '10px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}</div>\n          <div style={{ \n            textAlign: 'center', \n            padding: '20px 0',\n            color: '#ff9800', \n            fontSize: '14px', \n            fontWeight: 'bold',\n            background: 'rgba(255,255,255,0.1)',\n            borderRadius: '8px',\n            marginBottom: '10px'\n          }}>\n            当前无信号灯状态信息\n          </div>\n          <div style={{ fontSize: '12px', color: '#888', textAlign: 'center' }}>\n            路口ID: {interId}\n          </div>\n          <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    }\n    \n    // 设置弹窗位置在左上角区域\n    const x = 445;\n    const y = 250;\n    \n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n    \n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n    \n    // 更新弹窗状态\n    if (window._setTrafficLightPopover) {\n      window._setTrafficLightPopover({\n        visible: true,\n        interId: interId,\n        position: { x, y },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      // 设置定时更新\n      if (window.trafficLightUpdateTimerRef) {\n        window.trafficLightUpdateTimerRef.current = setInterval(() => {\n          window.showTrafficLightPopup(interId);\n        }, 1000);\n      }\n      \n      return true;\n    } else {\n      console.error('无法找到setTrafficLightPopover函数');\n      return false;\n    }\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n  \n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n  \n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  \n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n    \n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n    \n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n    \n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n    \n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    \n    console.log('归一化坐标:', mouseX, mouseY);\n    \n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    \n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n    \n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n    \n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    \n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', \n                    '距离:', intersect.distance,\n                    'position:', intersect.object.position.toArray(),\n                    'userData:', intersect.object.userData);\n        \n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      \n      return true;\n    }\n    \n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    \n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', \n                  '类型:', obj.type,\n                  '位置:', obj.position.toArray(),\n                  '距离:', intersect.distance,\n                  'userData:', obj.userData);\n    });\n    \n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    \n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n        \n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n        \n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n        \n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        \n        if (isVisible) {\n          visibleCount++;\n        }\n        \n        console.log(`红绿灯 ${interId}:`, {\n          名称: lightObj.intersection?.name || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    \n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n    \n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  \n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch(phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n  \n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ \n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: trafficLight.intersection?.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n  \n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = { isLight: true };\n  \n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  \n  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\n\nexport default CampusModel; \n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAO,KAAKC,aAAa,MAAM,2CAA2C;AAG1E,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACtC,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,WAAW,MAAM,sBAAsB,CAAC,CAAC;;AAEhD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,0BAA0B,GAAG,IAAI,CAAC,CAAC;AACvC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB,IAAIC,eAAe,GAAE,IAAI,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAE,IAAI;;AAElB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAEpB;AACA,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,MAAMC,oBAAoB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;;AAExC;AACA,MAAME,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACV;EACEC,GAAG,EAAE,2BAA2B;EAChCC,GAAG,EAAE,2BAA2B;EAChChB,KAAK,EAAE,6BAA6B;EACtCiB,GAAG,EAAE,2BAA2B;EAAG;EACnCC,IAAI,EAAE,4BAA4B,CAAE;AACtC,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AACzEC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;;AAE1D;AACA,MAAMG,aAAa,GAAG,IAAIlB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,IAAImB,gBAAgB,GAAG,IAAInB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAElC;AACA,IAAIoB,gBAAgB,GAAG,IAAI;;AAE3B;AACA,IAAIC,gBAAgB,GAAG,IAAIrB,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,IAAIsB,kBAAkB,GAAG,IAAItB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEpC,MAAMuB,KAAK,GAAG,IAAI1D,KAAK,CAAC2D,KAAK,CAAC,CAAC;;AAE/B;AACA,MAAMC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGf,QAAQ,oBAAoB,CAAC;IAC7D,MAAMgB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,IAAIA,IAAI,CAACE,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;MACzD,MAAMG,WAAW,GAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,IAAI,CAAC;MACrE,IAAIH,WAAW,IAAIA,WAAW,CAACI,KAAK,EAAE;QACpCjB,gBAAgB,GAAGa,WAAW,CAACI,KAAK;QACpCrB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEG,gBAAgB,CAAC;QAC7C,OAAOA,gBAAgB;MACzB;IACF;IACAJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChCG,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjClB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB;AACF,CAAC;;AAED;AACA,MAAMmB,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;EAC5C;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAI,CAACjD,YAAY,EAAE;MACjBA,YAAY,GAAGgD,MAAM,CAACE,KAAK,CAAC,CAAC;MAC7B,OAAOF,MAAM;IACf;IAEA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEpD,YAAY,CAACoD,CAAC,EAAElD,KAAK,CAAC;IAChE,MAAMmD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEtD,YAAY,CAACsD,CAAC,EAAEpD,KAAK,CAAC;IAChE,MAAMqD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAExD,YAAY,CAACwD,CAAC,EAAEtD,KAAK,CAAC;IAEhEF,YAAY,CAACyD,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;IACjD,OAAOvD,YAAY,CAACkD,KAAK,CAAC,CAAC;EAC3B;;EAEA;EACA,IAAI,CAAC/C,oBAAoB,CAACuD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC9C,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;EAEA,MAAMW,OAAO,GAAGxD,oBAAoB,CAACyD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,MAAMY,QAAQ,GAAGF,OAAO,CAACG,UAAU,CAACd,MAAM,CAAC;EAC3C,MAAMe,sBAAsB,GAAG,EAAE,CAAC,CAAC;;EAEnC,IAAIF,QAAQ,GAAGE,sBAAsB,EAAE;IACrC3C,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAUY,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAClE7D,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;;EAEA;EACA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEO,OAAO,CAACP,CAAC,EAAElD,KAAK,CAAC;EAC3D,MAAMmD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEK,OAAO,CAACL,CAAC,EAAEpD,KAAK,CAAC;EAC3D,MAAMqD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAEG,OAAO,CAACH,CAAC,EAAEtD,KAAK,CAAC;EAE3D,MAAM+D,WAAW,GAAG,IAAIhG,KAAK,CAACiG,OAAO,CAACf,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACtEpD,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAEgB,WAAW,CAACf,KAAK,CAAC,CAAC,CAAC;EAExD,OAAOe,WAAW;AACpB,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,WAAW,EAAEnB,SAAS,KAAK;EACjD;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAIhD,YAAY,KAAK,IAAI,EAAE;MACzBA,YAAY,GAAGmE,WAAW;MAC1B,OAAOA,WAAW;IACpB;;IAEA;IACA,IAAIC,IAAI,GAAGD,WAAW,GAAGnE,YAAY;IACrC,IAAIoE,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IAExC,MAAMC,gBAAgB,GAAG7B,aAAa,CAAC1C,YAAY,GAAGoE,IAAI,EAAEpE,YAAY,EAAEC,KAAK,CAAC;IAChFD,YAAY,GAAGuE,gBAAgB;IAC7B,OAAOA,gBAAgB;EACzB;;EAEA;EACA,IAAI,CAACnE,oBAAoB,CAACqD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC5C,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMK,OAAO,GAAGpE,oBAAoB,CAACuD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,IAAIoB,IAAI,GAAGD,WAAW,GAAGK,OAAO;EAChC,IAAIJ,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;;EAExC;EACA,MAAMG,mBAAmB,GAAGJ,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,CAAC;EACzC,IAAID,IAAI,CAACK,GAAG,CAACN,IAAI,CAAC,GAAGK,mBAAmB,EAAE;IACxCtD,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAU,CAACoB,IAAI,GAAG,GAAG,GAAGC,IAAI,CAACC,EAAE,EAAEP,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAChF3D,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMI,gBAAgB,GAAG7B,aAAa,CAAC8B,OAAO,GAAGJ,IAAI,EAAEI,OAAO,EAAEvE,KAAK,CAAC;EACtEG,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEuB,gBAAgB,CAAC;EAErD,OAAOA,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA,MAAMI,6BAA6B,GAAG,CAAC,CAAC,CAAC;AACzC;;AAEA;AACA,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;AACjC,MAAMC,QAAQ,GAAG,IAAI3E,GAAG,CAAC,CAAC;;AAE1B;AACA,MAAM4E,eAAe,GAAG;EACtBC,MAAM,EAAE,IAAIH,GAAG,CAAC,CAAC;EACjBI,KAAK,EAAE,IAAI9E,GAAG,CAAC,CAAC;EAChB+E,OAAO,EAAE,IAAI/E,GAAG,CAAC,CAAC;EAClBgF,MAAM,EAAE,IAAIN,GAAG,CAAC,CAAC;EAEjBO,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACrB,IAAI,CAACN,MAAM,CAACO,GAAG,CAACF,KAAK,CAAC;IACtB,IAAIC,KAAK,EAAE;MACT,IAAI,CAACH,MAAM,CAACI,GAAG,CAACD,KAAK,CAAC;MACtB;MACAA,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAI;QACvB,IAAIA,MAAM,CAACC,MAAM,EAAE;UACjB,IAAI,CAACT,KAAK,CAACzB,GAAG,CAACiC,MAAM,CAACE,IAAI,EAAEF,MAAM,CAAC;QACrC;MACF,CAAC,CAAC;IACJ;IACA,OAAOJ,KAAK;EACd,CAAC;EAEDO,SAASA,CAACC,MAAM,EAAER,KAAK,EAAE;IACvB,IAAI,CAAC,IAAI,CAACH,OAAO,CAACzB,GAAG,CAAC4B,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACH,OAAO,CAAC1B,GAAG,CAAC6B,KAAK,EAAE,IAAIR,GAAG,CAAC,CAAC,CAAC;IACpC;IACA,IAAI,CAACK,OAAO,CAACvB,GAAG,CAAC0B,KAAK,CAAC,CAACE,GAAG,CAACM,MAAM,CAAC;IACnC,OAAOA,MAAM;EACf,CAAC;EAEDC,WAAWA,CAACT,KAAK,EAAE;IACjB,IAAI,IAAI,CAACL,MAAM,CAACvB,GAAG,CAAC4B,KAAK,CAAC,EAAE;MAC1B,IAAI;QACF;QACA,IAAI,IAAI,CAACH,OAAO,CAACzB,GAAG,CAAC4B,KAAK,CAAC,EAAE;UAC3B,IAAI,CAACH,OAAO,CAACvB,GAAG,CAAC0B,KAAK,CAAC,CAACU,OAAO,CAACF,MAAM,IAAI;YACxC,IAAIA,MAAM,IAAI,OAAOA,MAAM,CAACG,IAAI,KAAK,UAAU,EAAE;cAC/CH,MAAM,CAACG,IAAI,CAAC,CAAC;YACf;UACF,CAAC,CAAC;UACF,IAAI,CAACd,OAAO,CAACe,MAAM,CAACZ,KAAK,CAAC;QAC5B;;QAEA;QACA,IAAI,OAAOA,KAAK,CAACa,aAAa,KAAK,UAAU,EAAE;UAC7Cb,KAAK,CAACa,aAAa,CAAC,CAAC;QACvB;;QAEA;QACA,MAAMC,IAAI,GAAGd,KAAK,CAACe,OAAO,CAAC,CAAC;QAC5B,IAAID,IAAI,EAAE;UACR,IAAI,CAAChB,MAAM,CAACc,MAAM,CAACE,IAAI,CAAC;UACxBA,IAAI,CAACX,QAAQ,CAACC,MAAM,IAAI;YACtB,IAAIA,MAAM,IAAIA,MAAM,CAACC,MAAM,EAAE;cAC3B,IAAI,CAACT,KAAK,CAACgB,MAAM,CAACR,MAAM,CAACE,IAAI,CAAC;YAChC;YACA,IAAIF,MAAM,IAAIA,MAAM,CAACY,UAAU,EAAE;cAC/BZ,MAAM,CAACY,UAAU,CAACC,MAAM,GAAG,CAAC;YAC9B;UACF,CAAC,CAAC;;UAEF;UACA,IAAI;YACF,IAAI,OAAOjB,KAAK,CAACkB,WAAW,KAAK,UAAU,EAAE;cAC3ClB,KAAK,CAACkB,WAAW,CAACJ,IAAI,CAAC;YACzB;YAEA,IAAI,OAAOd,KAAK,CAACmB,aAAa,KAAK,UAAU,EAAE;cAC7CnB,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAEL,IAAI,CAAC;YACjC;;YAEA;YACA,IAAI,OAAOd,KAAK,CAACoB,WAAW,KAAK,UAAU,EAAE;cAC3C;cACA,MAAMC,KAAK,GAAGrB,KAAK,CAACsB,QAAQ,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;cACnEN,KAAK,CAACX,OAAO,CAACkB,IAAI,IAAI;gBACpB,IAAIA,IAAI,IAAIA,IAAI,CAACtB,IAAI,EAAE;kBACrBN,KAAK,CAACoB,WAAW,CAACQ,IAAI,CAAC;gBACzB;cACF,CAAC,CAAC;YACJ;UACF,CAAC,CAAC,OAAOC,CAAC,EAAE;YACV/F,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE8F,CAAC,CAAC;YACnC;UACF;QACF;QAEA,IAAI,CAAClC,MAAM,CAACiB,MAAM,CAACZ,KAAK,CAAC;MAC3B,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrC;QACA,IAAI,CAACuC,MAAM,CAACiB,MAAM,CAACZ,KAAK,CAAC;MAC3B;IACF;EACF,CAAC;EAED8B,OAAOA,CAAA,EAAG;IACR,IAAI;MACF;MACA,IAAI,CAACjC,OAAO,CAACa,OAAO,CAAC,CAACb,OAAO,EAAEG,KAAK,KAAK;QACvC,IAAI;UACFH,OAAO,CAACa,OAAO,CAACF,MAAM,IAAI;YACxB,IAAIA,MAAM,IAAI,OAAOA,MAAM,CAACG,IAAI,KAAK,UAAU,EAAE;cAC/CH,MAAM,CAACG,IAAI,CAAC,CAAC;YACf;UACF,CAAC,CAAC;UACFd,OAAO,CAACkC,KAAK,CAAC,CAAC;QACjB,CAAC,CAAC,OAAOF,CAAC,EAAE;UACV/F,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE8F,CAAC,CAAC;QACjC;MACF,CAAC,CAAC;MACF,IAAI,CAAChC,OAAO,CAACkC,KAAK,CAAC,CAAC;;MAEpB;MACA,IAAI,CAACpC,MAAM,CAACe,OAAO,CAACV,KAAK,IAAI;QAC3B,IAAI;UACF,IAAI,OAAOA,KAAK,CAACa,aAAa,KAAK,UAAU,EAAE;YAC7Cb,KAAK,CAACa,aAAa,CAAC,CAAC;UACvB;UAEA,MAAMC,IAAI,GAAGd,KAAK,CAACe,OAAO,CAAC,CAAC;UAC5B,IAAID,IAAI,EAAE;YACRA,IAAI,CAACX,QAAQ,CAACC,MAAM,IAAI;cACtB,IAAIA,MAAM,IAAIA,MAAM,CAACY,UAAU,EAAE;gBAC/BZ,MAAM,CAACY,UAAU,CAACC,MAAM,GAAG,CAAC;cAC9B;YACF,CAAC,CAAC;;YAEF;YACA,IAAI,OAAOjB,KAAK,CAACkB,WAAW,KAAK,UAAU,EAAE;cAC3ClB,KAAK,CAACkB,WAAW,CAACJ,IAAI,CAAC;YACzB;YAEA,IAAI,OAAOd,KAAK,CAACmB,aAAa,KAAK,UAAU,EAAE;cAC7CnB,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAEL,IAAI,CAAC;YACjC;;YAEA;YACA,IAAI;cACF,IAAId,KAAK,CAACsB,QAAQ,IAAIzE,KAAK,CAACC,OAAO,CAACkD,KAAK,CAACsB,QAAQ,CAAC,EAAE;gBACnD,MAAMD,KAAK,GAAGrB,KAAK,CAACsB,QAAQ,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;gBACnEN,KAAK,CAACX,OAAO,CAACkB,IAAI,IAAI;kBACpB,IAAIA,IAAI,IAAIA,IAAI,CAACtB,IAAI,IAAI,OAAON,KAAK,CAACoB,WAAW,KAAK,UAAU,EAAE;oBAChEpB,KAAK,CAACoB,WAAW,CAACQ,IAAI,CAAC;kBACzB;gBACF,CAAC,CAAC;cACJ;YACF,CAAC,CAAC,OAAOC,CAAC,EAAE;cACV/F,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE8F,CAAC,CAAC;YACnC;UACF;QACF,CAAC,CAAC,OAAOA,CAAC,EAAE;UACV/F,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE8F,CAAC,CAAC;QAClC;MACF,CAAC,CAAC;MACF,IAAI,CAAClC,MAAM,CAACoC,KAAK,CAAC,CAAC;;MAEnB;MACA,IAAI,CAACnC,KAAK,CAACc,OAAO,CAACsB,IAAI,IAAI;QACzB,IAAIA,IAAI,CAACC,MAAM,EAAE;UACfD,IAAI,CAACC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAC;QAC1B;QACA,IAAIA,IAAI,CAACG,MAAM,EAAEH,IAAI,CAACG,MAAM,CAACC,QAAQ,CAAC,CAAC;QACvC,IAAIJ,IAAI,CAACK,WAAW,EAAEL,IAAI,CAACK,WAAW,CAACD,QAAQ,CAAC,CAAC;MACnD,CAAC,CAAC;MACF,IAAI,CAACxC,KAAK,CAACmC,KAAK,CAAC,CAAC;;MAElB;MACA,IAAI,CAACjC,MAAM,CAACY,OAAO,CAACT,KAAK,IAAI;QAC3B,IAAIA,KAAK,CAACgC,MAAM,EAAE;UAChBhC,KAAK,CAACgC,MAAM,CAACC,MAAM,CAACjC,KAAK,CAAC;QAC5B;QACAA,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAI;UACvB,IAAIA,MAAM,CAACkC,MAAM,EAAE;YACjB,IAAIlC,MAAM,CAACmC,QAAQ,EAAE;cACnBnC,MAAM,CAACmC,QAAQ,CAACC,OAAO,CAAC,CAAC;YAC3B;YACA,IAAIpC,MAAM,CAACqC,QAAQ,EAAE;cACnB,IAAI5F,KAAK,CAACC,OAAO,CAACsD,MAAM,CAACqC,QAAQ,CAAC,EAAE;gBAClCrC,MAAM,CAACqC,QAAQ,CAAC/B,OAAO,CAAC+B,QAAQ,IAAI;kBAClC,IAAIA,QAAQ,CAAClB,GAAG,EAAEkB,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;kBACxCC,QAAQ,CAACD,OAAO,CAAC,CAAC;gBACpB,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL,IAAIpC,MAAM,CAACqC,QAAQ,CAAClB,GAAG,EAAEnB,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;gBACtDpC,MAAM,CAACqC,QAAQ,CAACD,OAAO,CAAC,CAAC;cAC3B;YACF;UACF;UACA,IAAIpC,MAAM,CAACY,UAAU,EAAE;YACrBZ,MAAM,CAACY,UAAU,CAACC,MAAM,GAAG,CAAC;UAC9B;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAACnB,MAAM,CAACiC,KAAK,CAAC,CAAC;IACrB,CAAC,CAAC,OAAO3E,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC;MACA,IAAI,CAACyC,OAAO,CAACkC,KAAK,CAAC,CAAC;MACpB,IAAI,CAACpC,MAAM,CAACoC,KAAK,CAAC,CAAC;MACnB,IAAI,CAACnC,KAAK,CAACmC,KAAK,CAAC,CAAC;MAClB,IAAI,CAACjC,MAAM,CAACiC,KAAK,CAAC,CAAC;IACrB;EACF;AACF,CAAC;;AAED;AACA,MAAMW,oBAAoB,GAAIzC,KAAK,IAAK;EACtC,MAAMD,KAAK,GAAG,IAAIrH,KAAK,CAACgK,cAAc,CAAC1C,KAAK,CAAC;EAC7C,OAAOP,eAAe,CAACK,QAAQ,CAACC,KAAK,EAAEC,KAAK,CAAC;AAC/C,CAAC;;AAED;AACA,MAAM2C,YAAY,GAAGA,CAAChB,IAAI,EAAE5B,KAAK,EAAEC,KAAK,KAAK;EAC3C,MAAMO,MAAM,GAAGR,KAAK,CAAC6C,UAAU,CAACjB,IAAI,EAAE3B,KAAK,CAAC;EAC5C,OAAOP,eAAe,CAACa,SAAS,CAACC,MAAM,EAAER,KAAK,CAAC;AACjD,CAAC;;AAED;AACA,MAAM8C,mBAAmB,GAAG,IAAItD,GAAG,CAAC,CAAC;AAErC,MAAMuD,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC,kBAAkB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAMC,YAAY,GAAG5K,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM6K,UAAU,GAAG7K,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM8K,SAAS,GAAG9K,MAAM,CAAC,IAAIM,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMyK,aAAa,GAAG/K,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMgL,eAAe,GAAGhL,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMiL,aAAa,GAAGjL,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMkL,iBAAiB,GAAGlL,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMmL,MAAM,GAAGnL,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAMoL,kBAAkB,GAAGpL,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMqL,gBAAgB,GAAGrL,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMsL,eAAe,GAAG,IAAI,CAAC,CAAC;;EAE9B;EACA,MAAMC,oBAAoB,GAAGvL,MAAM,CAACwL,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC/C,MAAMC,qBAAqB,GAAG,IAAIpJ,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC,MAAMqJ,gBAAgB,GAAG3L,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;;EAKrC;EACA,MAAM,CAAC4L,YAAY,EAAEC,eAAe,CAAC,GAAG5L,QAAQ,CAAC;IAC/C6L,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlM,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMmM,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,IAAI;IAAG;IACfC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGrN,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAACsN,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtN,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAACuN,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxN,QAAQ,CAAC;IAC7DyN,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,IAAI;IACbtB,QAAQ,EAAE;MAAE/G,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxBoI,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAG9N,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM+N,0BAA0B,GAAG/N,MAAM,CAAC,IAAI,CAAC;;EAE/C;EACA0C,MAAM,CAACsL,uBAAuB,GAAGP,sBAAsB;;EAEvD;EACA/K,MAAM,CAACoL,mBAAmB,GAAGA,mBAAmB;EAChDpL,MAAM,CAACqL,0BAA0B,GAAGA,0BAA0B;;EAE9D;EACA,MAAME,uBAAuB,GAAG;IAC9B5B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IAAG;IACd3B,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7B2B,KAAK,EAAE,OAAO;IAAG;IACjB1B,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMiB,UAAU,GAAG;IACjB/B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACX3B,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBwB,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbpB,QAAQ,EAAE,MAAM;IAChBqB,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvC/B,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM,CAAC9I,gBAAgB,CAAC,GAAG1D,QAAQ,CAAC,IAAIqC,GAAG,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACmM,UAAU,EAAEC,aAAa,CAAC,GAAGzO,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACwD,gBAAgB,EAAEkL,mBAAmB,CAAC,GAAG1O,QAAQ,CAAC,IAAIqC,GAAG,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACsM,WAAW,EAAEC,cAAc,CAAC,GAAG5O,QAAQ,CAAC;IAAE6O,KAAK,EAAE,EAAE;IAAElB,OAAO,EAAE;EAAG,CAAC,CAAC;;EAE1E;EACA,MAAMmB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAItN,UAAU,KAAK,QAAQ,EAAE;MAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB9B,UAAU,GAAG,QAAQ;;MAErB;MACA2J,kBAAkB,CAAC4D,OAAO,GAAG,IAAI;MACjC3D,gBAAgB,CAAC2D,OAAO,GAAG,IAAI;MAE/B,IAAItN,QAAQ,EAAE;QACZA,QAAQ,CAACuN,OAAO,GAAG,KAAK;MAC1B;IACF;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIzN,UAAU,KAAK,QAAQ,EAAE;MAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB9B,UAAU,GAAG,QAAQ;;MAErB;MACA2J,kBAAkB,CAAC4D,OAAO,GAAG,IAAI;MACjC3D,gBAAgB,CAAC2D,OAAO,GAAG,IAAI;MAE/B,IAAI3B,SAAS,CAAC2B,OAAO,IAAItN,QAAQ,EAAE;QACjC;QACA;QACA2L,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC,MAAMwJ,UAAU,GAAG9B,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACjH,KAAK,CAAC,CAAC;QACrD,MAAMgK,SAAS,GAAG/B,SAAS,CAAC2B,OAAO,CAACK,EAAE,CAACjK,KAAK,CAAC,CAAC;;QAE9C;QACA,IAAI7E,KAAK,CAAC+O,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC;UAAEjK,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,GAAG;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAChC8J,MAAM,CAACjP,KAAK,CAACkP,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdvC,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACwD,IAAI,CAACV,UAAU,CAAC;QAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;QAEV;QACA,IAAIvP,KAAK,CAAC+O,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;UAAEjK,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAC9B8J,MAAM,CAACjP,KAAK,CAACkP,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdvC,SAAS,CAAC2B,OAAO,CAACK,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC;QACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC;;QAEV;QACApO,QAAQ,CAACqO,MAAM,CAACpK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B,MAAMqK,aAAa,GAAGtO,QAAQ,CAACqO,MAAM,CAAC3K,KAAK,CAAC,CAAC;;QAE7C;QACA,IAAI7E,KAAK,CAAC+O,KAAK,CAACU,aAAa,CAAC,CAC3BT,EAAE,CAAC;UAAEjK,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAC9B8J,MAAM,CAACjP,KAAK,CAACkP,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdlO,QAAQ,CAACqO,MAAM,CAACF,IAAI,CAACG,aAAa,CAAC;UACnC;UACA3C,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAACvO,QAAQ,CAACqO,MAAM,CAAC;UACzCrO,QAAQ,CAACwO,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;QAEV;QACApO,QAAQ,CAACuN,OAAO,GAAG,IAAI;;QAEvB;QACAvN,QAAQ,CAACyO,WAAW,GAAG,EAAE;QACzBzO,QAAQ,CAAC0O,WAAW,GAAG,GAAG;QAC1B1O,QAAQ,CAAC2O,aAAa,GAAG7J,IAAI,CAACC,EAAE,GAAG,GAAG;QACtC/E,QAAQ,CAAC4O,aAAa,GAAG,CAAC;QAC1B5O,QAAQ,CAACwO,MAAM,CAAC,CAAC;QACjB;QACA7C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC;QAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;QAEzClN,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;UACrBkN,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAGlQ,iBAAiB,CAACmQ,aAAa,CAACvM,IAAI,CAACwM,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKJ,KAAK,CAAC;IAEhF,IAAIC,YAAY,IAAIzD,SAAS,CAAC2B,OAAO,IAAItN,QAAQ,EAAE;MACjD6L,uBAAuB,CAACuD,YAAY,CAAC;;MAErC;MACA,MAAMI,WAAW,GAAGpG,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAChDC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,EAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC;MAEDzI,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvB8N,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBK,GAAG,EAAE;UACHxF,SAAS,EAAEgF,YAAY,CAAChF,SAAS;UACjCC,QAAQ,EAAE+E,YAAY,CAAC/E;QACzB,CAAC;QACDwF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACAzP,UAAU,GAAG,cAAc;MAC3B0K,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAkB,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAC1G,GAAG,CAACuL,WAAW,CAAC5L,CAAC,GAAC,EAAE,EAAE,EAAE,EAAE,CAAC4L,WAAW,CAAC1L,CAAC,GAAC,EAAE,CAAC;;MAEvE;MACA9D,QAAQ,CAACqO,MAAM,CAACpK,GAAG,CAACuL,WAAW,CAAC5L,CAAC,EAAE,CAAC,EAAE,CAAC4L,WAAW,CAAC1L,CAAC,CAAC;;MAErD;MACA6H,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAACvO,QAAQ,CAACqO,MAAM,CAAC;;MAEzC;MACArO,QAAQ,CAACuN,OAAO,GAAG,IAAI;MACvBvN,QAAQ,CAACwO,MAAM,CAAC,CAAC;;MAEjB;MACA7C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC;MAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;MAEzClN,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzB8N,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBO,IAAI,EAAEnE,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACoF,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAEhQ,QAAQ,CAACqO,MAAM,CAAC0B,OAAO,CAAC,CAAC;QAC9BF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACA,IAAIJ,YAAY,CAACa,eAAe,KAAK,KAAK,IAAIb,YAAY,CAACnD,OAAO,EAAE;QAClErK,OAAO,CAACC,GAAG,CAAC,MAAMuN,YAAY,CAACG,IAAI,iBAAiB,CAAC;;QAErD;QACAW,UAAU,CAAC,MAAM;UACf;UACA,IAAIjE,OAAO,GAAGmD,YAAY,CAACnD,OAAO;;UAElC;UACA,IAAIjL,MAAM,CAACmP,qBAAqB,EAAE;YAChCnP,MAAM,CAACmP,qBAAqB,CAAClE,OAAO,CAAC;YACrCrK,OAAO,CAACC,GAAG,CAAC,SAASuN,YAAY,CAACG,IAAI,SAAStD,OAAO,YAAY,CAAC;UACrE,CAAC,MAAM;YACLrK,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC;UAChC;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLtB,OAAO,CAACC,GAAG,CAAC,MAAMuN,YAAY,CAACG,IAAI,sBAAsB,CAAC;;QAE1D;QACA,IAAIvO,MAAM,CAACsL,uBAAuB,EAAE;UAClCtL,MAAM,CAACsL,uBAAuB,CAAC;YAC7BN,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAMoE,iBAAiB,GAAGA,CAAChD,KAAK,EAAEiD,OAAO,KAAK;IAC5C,IAAI;MACF,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;MAEnC;MACA,IAAIjD,KAAK,KAAKtM,WAAW,CAACO,GAAG,EAAE;QAAA,IAAAoP,aAAA;QAC7B;;QAEA;QACA,MAAMC,SAAS,GAAGJ,OAAO,CAACK,GAAG;QAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;QAEnC;QACA,MAAMC,aAAa,GAAG/O,gBAAgB,CAACqC,GAAG,CAACsM,SAAS,CAAC;;QAErD;QACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;UACrD;UACA;UACA;UACA;UACA;UACA;QACF;;QAEA;QACA/O,gBAAgB,CAACkC,GAAG,CAACyM,SAAS,EAAEE,gBAAgB,CAAC;;QAEjD;QACA;QACA;QACA;QACA;;QAEA,MAAMG,YAAY,GAAG,EAAAN,aAAA,GAAAH,OAAO,CAAC9N,IAAI,cAAAiO,aAAA,uBAAZA,aAAA,CAAcM,YAAY,KAAI,EAAE;QACrD,MAAMC,KAAK,GAAGV,OAAO,CAAC9N,IAAI,CAACwO,KAAK;;QAEhC;QACA,MAAMjH,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;;QAEtB;QACAgH,YAAY,CAACvK,OAAO,CAACyK,WAAW,IAAI;UAClC;UACA;UACA,MAAMC,EAAE,GAAIR,SAAS,GAAGO,WAAW,CAACE,SAAS;UAC7C,MAAMC,IAAI,GAAGH,WAAW,CAACI,WAAW;UAEpC,IAAGD,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACE;YACA,MAAME,KAAK,GAAG;cACZlH,SAAS,EAAEsF,UAAU,CAACuB,WAAW,CAACM,WAAW,CAAC;cAC9ClH,QAAQ,EAAEqF,UAAU,CAACuB,WAAW,CAACO,UAAU,CAAC;cAC5ClH,KAAK,EAAEoF,UAAU,CAACuB,WAAW,CAACQ,SAAS,CAAC;cACxClH,OAAO,EAAEmF,UAAU,CAACuB,WAAW,CAACS,WAAW;YAC7C,CAAC;YAED,MAAMC,QAAQ,GAAGvI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAAC6B,KAAK,CAAClH,SAAS,EAAEkH,KAAK,CAACjH,QAAQ,CAAC;;YAEhF;YACA,IAAIuH,cAAc;YAClB,QAAQR,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRQ,cAAc,GAAG3R,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACR2R,cAAc,GAAG1R,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACR0R,cAAc,GAAGzR,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACF,IAAI4F,KAAK,GAAGjE,aAAa,CAACsC,GAAG,CAAC8M,EAAE,CAAC;YAEjC,IAAI,CAACnL,KAAK,IAAI6L,cAAc,EAAE;cAC1B;cACA,MAAMC,QAAQ,GAAGT,IAAI,KAAK,GAAG,GAAGtS,aAAa,CAAC4E,KAAK,CAACvD,oBAAoB,CAAC,GAAGyR,cAAc,CAAClO,KAAK,CAAC,CAAC;cAClG;cACA,MAAMoO,MAAM,GAAGV,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;cACvCS,QAAQ,CAAClH,QAAQ,CAAC1G,GAAG,CAAC0N,QAAQ,CAAC/N,CAAC,EAAEkO,MAAM,EAAE,CAACH,QAAQ,CAAC7N,CAAC,CAAC;cACtD+N,QAAQ,CAACE,QAAQ,CAACjO,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGuM,KAAK,CAAC/G,OAAO,GAAGzF,IAAI,CAACC,EAAE,GAAG,GAAG;;cAE7D;cACA,IAAIqM,IAAI,KAAK,GAAG,EAAE;gBAClB;gBACES,QAAQ,CAACG,KAAK,CAAC/N,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;gBAE3B;gBACA,MAAM6B,KAAK,GAAG0C,oBAAoB,CAACqJ,QAAQ,CAAC;gBAE5C,IAAIvR,eAAe,IAAIA,eAAe,CAACwG,UAAU,IAAIxG,eAAe,CAACwG,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;kBAC1F;kBACA,MAAMT,MAAM,GAAGoC,YAAY,CAACpI,eAAe,CAACwG,UAAU,CAAC,CAAC,CAAC,EAAEhB,KAAK,EAAE+L,QAAQ,CAAC;kBAC3EvL,MAAM,CAAC2L,IAAI,CAAC,CAAC;gBACf;;gBAEA;gBACAjI,qBAAqB,CAAC/F,GAAG,CAACiN,EAAE,EAAEpL,KAAK,CAAC;cACtC;cAEAzF,KAAK,CAAC2F,GAAG,CAAC6L,QAAQ,CAAC;cAEnB/P,aAAa,CAACmC,GAAG,CAACiN,EAAE,EAAE;gBACpBnL,KAAK,EAAE8L,QAAQ;gBACfK,UAAU,EAAEnI,GAAG;gBACjBqH,IAAI,EAAEA;cACN,CAAC,CAAC;YACN,CAAC,MAAM,IAAIrL,KAAK,EAAE;cACd;cACFA,KAAK,CAACA,KAAK,CAAC4E,QAAQ,CAAC1G,GAAG,CAAC0N,QAAQ,CAAC/N,CAAC,EAAEmC,KAAK,CAACqL,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAACO,QAAQ,CAAC7N,CAAC,CAAC;cACjFiC,KAAK,CAACA,KAAK,CAACgM,QAAQ,CAACjO,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGuM,KAAK,CAAC/G,OAAO,GAAGzF,IAAI,CAACC,EAAE,GAAG,GAAG;cAChEgB,KAAK,CAACmM,UAAU,GAAGnI,GAAG;cACtBhE,KAAK,CAACA,KAAK,CAAC8I,YAAY,CAAC,CAAC;cAC1B9I,KAAK,CAACA,KAAK,CAAC+I,iBAAiB,CAAC,IAAI,CAAC;YACnC;UACF;QACF,CAAC,CAAC;;QAEF;QACA,MAAMqD,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAI9M,GAAG,CAACyL,YAAY,CAAC1J,GAAG,CAACgL,CAAC,IAAI3B,SAAS,GAAG2B,CAAC,CAAClB,SAAS,CAAC,CAAC;QAE1ErP,aAAa,CAAC0E,OAAO,CAAC,CAAC8L,SAAS,EAAEpB,EAAE,KAAK;UACvC,IAAInH,GAAG,GAAGuI,SAAS,CAACJ,UAAU,GAAGC,iBAAiB,IAAI,CAACC,UAAU,CAAClO,GAAG,CAACgN,EAAE,CAAC,EAAE;YACzE;YACA,IAAIoB,SAAS,CAAClB,IAAI,KAAK,GAAG,IAAIpH,qBAAqB,CAAC9F,GAAG,CAACgN,EAAE,CAAC,EAAE;cAC3D,MAAMpL,KAAK,GAAGkE,qBAAqB,CAAC5F,GAAG,CAAC8M,EAAE,CAAC;cAC3C;cACA1L,eAAe,CAACe,WAAW,CAACT,KAAK,CAAC;cAClCkE,qBAAqB,CAACtD,MAAM,CAACwK,EAAE,CAAC;YAClC;;YAEA;YACA7Q,KAAK,CAAC2H,MAAM,CAACsK,SAAS,CAACvM,KAAK,CAAC;YAC7BjE,aAAa,CAAC4E,MAAM,CAACwK,EAAE,CAAC;UAC1B;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAI9D,KAAK,KAAKtM,WAAW,CAACM,GAAG,EAAE;QAC7B;;QAEA,MAAMmR,OAAO,GAAGjC,OAAO,CAAC9N,IAAI;QAC5B,MAAMgQ,KAAK,GAAGD,OAAO,CAACtP,KAAK;QAC3B,MAAMwP,QAAQ,GAAG;UACfrI,SAAS,EAAEsF,UAAU,CAAC6C,OAAO,CAACG,QAAQ,CAAC;UACvCrI,QAAQ,EAAEqF,UAAU,CAAC6C,OAAO,CAACI,OAAO,CAAC;UACrCrI,KAAK,EAAEoF,UAAU,CAAC6C,OAAO,CAACd,SAAS,CAAC;UACpClH,OAAO,EAAEmF,UAAU,CAAC6C,OAAO,CAACb,WAAW;QACzC,CAAC;;QAED;QACA;;QAEA;QACA1Q,MAAM,CAAC4R,WAAW,CAAC;UACjBxB,IAAI,EAAE,iBAAiB;UACvByB,MAAM,EAAE;QACV,CAAC,EAAE,GAAG,CAAC;;QAEP;QACA7R,MAAM,CAAC4R,WAAW,CAAC;UACjBxB,IAAI,EAAE,KAAK;UACXnO,KAAK,EAAEuP,KAAK;UAAE;UACdhQ,IAAI,EAAE;YAAQ;YACZS,KAAK,EAAEuP,KAAK;YACZf,SAAS,EAAEc,OAAO,CAACd,SAAS;YAC5BkB,OAAO,EAAEJ,OAAO,CAACI,OAAO;YACxBD,QAAQ,EAAEH,OAAO,CAACG,QAAQ;YAC1BhB,WAAW,EAAEa,OAAO,CAACb;UACvB;QACF,CAAC,EAAE,GAAG,CAAC;;QAEP;QACA,MAAMC,QAAQ,GAAGvI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAACgD,QAAQ,CAACrI,SAAS,EAAEqI,QAAQ,CAACpI,QAAQ,CAAC;QACtF,MAAMyI,eAAe,GAAG,IAAIrU,KAAK,CAACiG,OAAO,CAACiN,QAAQ,CAAC/N,CAAC,EAAE,GAAG,EAAE,CAAC+N,QAAQ,CAAC7N,CAAC,CAAC;QACvE,MAAMiP,eAAe,GAAGjO,IAAI,CAACC,EAAE,GAAG0N,QAAQ,CAAClI,OAAO,GAAGzF,IAAI,CAACC,EAAE,GAAG,GAAG;;QAElE;QACA,MAAMiO,WAAW,GAAGzP,cAAc,CAACuP,eAAe,EAAEN,KAAK,CAAC;QAC1D,MAAM5N,WAAW,GAAGD,cAAc,CAACoO,eAAe,EAAEP,KAAK,CAAC;;QAE1D;QACA,IAAIS,UAAU,GAAGnR,aAAa,CAACsC,GAAG,CAACoO,KAAK,CAAC;;QAEzC;QACA,MAAMxP,aAAa,GAAGwP,KAAK,KAAKxQ,gBAAgB;QAEhD,IAAI,CAACiR,UAAU,IAAIhT,qBAAqB,EAAE;UACxC;UACA,MAAMiT,eAAe,GAAGjT,qBAAqB,CAACyD,KAAK,CAAC,CAAC;;UAErD;UACA;UACAwP,eAAe,CAACvI,QAAQ,CAAC1G,GAAG,CAAC+O,WAAW,CAACpP,CAAC,EAAE,CAAC,CAAC,EAAEoP,WAAW,CAAChP,CAAC,CAAC;UAC9DkP,eAAe,CAACnB,QAAQ,CAACjO,CAAC,GAAGc,WAAW;;UAExC;UACAsO,eAAe,CAACjN,QAAQ,CAAEkN,KAAK,IAAK;YAClC,IAAIA,KAAK,CAAC/K,MAAM,IAAI+K,KAAK,CAAC5K,QAAQ,EAAE;cAClC;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;;cAEA;cACA;cACA;;cAEA;;cAEA,MAAM6K,WAAW,GAAGD,KAAK,CAAC5K,QAAQ,CAAC7E,KAAK,CAAC,CAAC;cAC1CyP,KAAK,CAAC5K,QAAQ,GAAG6K,WAAW;;cAE5B;cACA,IAAIpQ,aAAa,EAAE;gBACjBoQ,WAAW,CAACxG,KAAK,CAAC3I,GAAG,CAAC,QAAQ,CAAC;cACjC,CAAC,MAAM;gBACLmP,WAAW,CAACxG,KAAK,CAAC3I,GAAG,CAAC,QAAQ,CAAC;cACjC;cACAmP,WAAW,CAACC,QAAQ,GAAG,IAAI5U,KAAK,CAAC6U,KAAK,CAAC,QAAQ,CAAC;cAChDF,WAAW,CAACG,WAAW,GAAG,IAAI;cAC9B;cACAH,WAAW,CAACI,WAAW,GAAG,IAAI;YAChC;UACF,CAAC,CAAC;;UAEF;UACA,MAAMC,UAAU,GAAGC,gBAAgB,CAAC,GAAG5O,IAAI,CAAC6O,KAAK,CAAClB,QAAQ,CAACnI,KAAK,CAAC,OAAO,EAAE;YACxEc,eAAe,EAAEpI,aAAa,GAC5B;cAAE4Q,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAExM,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAEsM,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAExM,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCyM,SAAS,EAAE;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAExM,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/CkE,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFsI,UAAU,CAAC9I,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClCwP,UAAU,CAACO,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/BP,UAAU,CAAClL,QAAQ,CAAC0L,OAAO,GAAG,GAAG,CAAC,CAAC;UACnCf,eAAe,CAAClN,GAAG,CAACyN,UAAU,CAAC;UAE/BpT,KAAK,CAAC2F,GAAG,CAACkN,eAAe,CAAC;;UAE1B;UACApR,aAAa,CAACmC,GAAG,CAACuO,KAAK,EAAE;YACvBzM,KAAK,EAAEmN,eAAe;YACtBhB,UAAU,EAAEpI,IAAI,CAACC,GAAG,CAAC,CAAC;YACtBqH,IAAI,EAAE,GAAG;YAAE;YACX8C,MAAM,EAAElR,aAAa;YACrByQ,UAAU,EAAEA,UAAU,CAAC;UACzB,CAAC,CAAC;;UAEF;;UAEA;UACA,IAAI5U,KAAK,CAAC+O,KAAK,CAACsF,eAAe,CAACvI,QAAQ,CAAC,CACtCkD,EAAE,CAAC;YAAE/J,CAAC,EAAE;UAAI,CAAC,EAAE,GAAG,CAAC,CACnBgK,MAAM,CAACjP,KAAK,CAACkP,MAAM,CAACC,SAAS,CAACmG,GAAG,CAAC,CAClC/F,KAAK,CAAC,CAAC;;UAEV;UACA8E,eAAe,CAACjN,QAAQ,CAAEkN,KAAK,IAAK;YAClC,IAAIA,KAAK,CAAC/K,MAAM,IAAI+K,KAAK,CAAC5K,QAAQ,IAAI4K,KAAK,CAAC5K,QAAQ,CAACgL,WAAW,EAAE;cAChE,IAAI1U,KAAK,CAAC+O,KAAK,CAAC;gBAAEqG,OAAO,EAAE;cAAI,CAAC,CAAC,CAC9BpG,EAAE,CAAC;gBAAEoG,OAAO,EAAE;cAAI,CAAC,EAAE,GAAG,CAAC,CACzBnG,MAAM,CAACjP,KAAK,CAACkP,MAAM,CAACC,SAAS,CAACmG,GAAG,CAAC,CAClCjG,QAAQ,CAAC,YAAW;gBACnBiF,KAAK,CAAC5K,QAAQ,CAAC0L,OAAO,GAAG,IAAI,CAACA,OAAO;gBACrCd,KAAK,CAAC5K,QAAQ,CAACiL,WAAW,GAAG,IAAI;cACnC,CAAC,CAAC,CACDpF,KAAK,CAAC,CAAC;YACZ;UACF,CAAC,CAAC;;UAEF;UACA,IAAIvP,KAAK,CAAC+O,KAAK,CAAC;YAAEqG,OAAO,EAAE;UAAI,CAAC,CAAC,CAC9BpG,EAAE,CAAC;YAAEoG,OAAO,EAAE;UAAI,CAAC,EAAE,GAAG,CAAC,CACzBnG,MAAM,CAACjP,KAAK,CAACkP,MAAM,CAACC,SAAS,CAACmG,GAAG,CAAC,CAClCjG,QAAQ,CAAC,YAAW;YACnBuF,UAAU,CAAClL,QAAQ,CAAC0L,OAAO,GAAG,IAAI,CAACA,OAAO;YAC1CR,UAAU,CAAClL,QAAQ,CAACiL,WAAW,GAAG,IAAI;UACxC,CAAC,CAAC,CACDpF,KAAK,CAAC,CAAC;;UAEV;UACA,IAAIpL,aAAa,EAAE;YACjBxD,gBAAgB,GAAG0T,eAAe;YAClC/I,eAAe,CAACsI,QAAQ,CAAC;YACzB7Q,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE2Q,KAAK,CAAC;UACjC;QACF,CAAC,MAAM,IAAIS,UAAU,EAAE;UACrB;UACA,MAAMmB,gBAAgB,GAAG7Q,cAAc,CAACyP,WAAW,EAAER,KAAK,CAAC;UAC3D,MAAMxN,gBAAgB,GAAGL,cAAc,CAACC,WAAW,EAAE4N,KAAK,CAAC;;UAE3D;UACAS,UAAU,CAAClN,KAAK,CAAC4E,QAAQ,CAACwD,IAAI,CAACiG,gBAAgB,CAAC;UAChDnB,UAAU,CAAClN,KAAK,CAACgM,QAAQ,CAACjO,CAAC,GAAGkB,gBAAgB;UAC9CiO,UAAU,CAAClN,KAAK,CAAC8I,YAAY,CAAC,CAAC;UAC/BoE,UAAU,CAAClN,KAAK,CAAC+I,iBAAiB,CAAC,IAAI,CAAC;UACxCmE,UAAU,CAACf,UAAU,GAAGpI,IAAI,CAACC,GAAG,CAAC,CAAC;UAClCkJ,UAAU,CAACiB,MAAM,GAAGlR,aAAa,CAAC,CAAC;;UAEnC;UACA,IAAIiQ,UAAU,CAACQ,UAAU,EAAE;YACzBR,UAAU,CAACQ,UAAU,CAAClL,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;YAC5C2K,UAAU,CAAClN,KAAK,CAACiC,MAAM,CAACiL,UAAU,CAACQ,UAAU,CAAC;UAChD;;UAEA;UACA,MAAMA,UAAU,GAAGC,gBAAgB,CAAC,GAAG5O,IAAI,CAAC6O,KAAK,CAAClB,QAAQ,CAACnI,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE;YAC9Ec,eAAe,EAAEpI,aAAa,GAC5B;cAAE4Q,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAExM,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAEsM,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAExM,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCyM,SAAS,EAAE;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAExM,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/CkE,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFsI,UAAU,CAAC9I,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACnCwP,UAAU,CAACO,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/Bf,UAAU,CAAClN,KAAK,CAACC,GAAG,CAACyN,UAAU,CAAC;UAChCR,UAAU,CAACQ,UAAU,GAAGA,UAAU;;UAElC;;UAEA;UACA,IAAIzQ,aAAa,EAAE;YACjBxD,gBAAgB,GAAGyT,UAAU,CAAClN,KAAK;YACnCoE,eAAe,CAACsI,QAAQ,CAAC;UAC3B;QACF;;QAEA;QACA,MAAM1I,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;QACtB,MAAMoI,iBAAiB,GAAG,IAAI,CAAC,CAAC;;QAEhCrQ,aAAa,CAAC0E,OAAO,CAAC,CAAC8L,SAAS,EAAEpB,EAAE,KAAK;UACvC,MAAMmD,mBAAmB,GAAGtK,GAAG,GAAGuI,SAAS,CAACJ,UAAU;;UAEtD;UACA,IAAImC,mBAAmB,GAAGlC,iBAAiB,GAAG,GAAG,IAAIkC,mBAAmB,IAAIlC,iBAAiB,EAAE;YAC7F;YACA,MAAM8B,OAAO,GAAG,CAAC;YAEjB3B,SAAS,CAACvM,KAAK,CAACE,QAAQ,CAAEkN,KAAK,IAAK;cAClC,IAAIA,KAAK,CAAC/K,MAAM,IAAI+K,KAAK,CAAC5K,QAAQ,EAAE;gBAClC;gBACA,IAAI4K,KAAK,CAAC5K,QAAQ,CAACgL,WAAW,KAAKe,SAAS,EAAE;kBAC5CnB,KAAK,CAAC5K,QAAQ,CAACgM,mBAAmB,GAAGpB,KAAK,CAAC5K,QAAQ,CAACgL,WAAW,IAAI,KAAK;kBACxEJ,KAAK,CAAC5K,QAAQ,CAACiM,eAAe,GAAGrB,KAAK,CAAC5K,QAAQ,CAAC0L,OAAO,IAAI,GAAG;gBAChE;;gBAEA;gBACAd,KAAK,CAAC5K,QAAQ,CAACgL,WAAW,GAAG,IAAI;gBACjCJ,KAAK,CAAC5K,QAAQ,CAAC0L,OAAO,GAAGA,OAAO;gBAChCd,KAAK,CAAC5K,QAAQ,CAACiL,WAAW,GAAG,IAAI;cACnC;YACF,CAAC,CAAC;;YAEF;YACA,IAAIlB,SAAS,CAACmB,UAAU,EAAE;cACxBnB,SAAS,CAACmB,UAAU,CAAClL,QAAQ,CAAC0L,OAAO,GAAGA,OAAO;cAC/C3B,SAAS,CAACmB,UAAU,CAAClL,QAAQ,CAACiL,WAAW,GAAG,IAAI;YAClD;UACF;UACA;UAAA,KACK,IAAIa,mBAAmB,GAAGlC,iBAAiB,EAAE;YAChD;YACA,IAAIG,SAAS,CAACmB,UAAU,EAAE;cACxBnB,SAAS,CAACmB,UAAU,CAAClL,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;cAC3CgK,SAAS,CAACmB,UAAU,CAAClL,QAAQ,CAACD,OAAO,CAAC,CAAC;cACvCgK,SAAS,CAACvM,KAAK,CAACiC,MAAM,CAACsK,SAAS,CAACmB,UAAU,CAAC;YAC9C;YAEAnB,SAAS,CAACvM,KAAK,CAACE,QAAQ,CAAEkN,KAAK,IAAK;cAClC,IAAIA,KAAK,CAAC/K,MAAM,EAAE;gBAChB,IAAI+K,KAAK,CAAC5K,QAAQ,EAAE;kBAClB,IAAI5F,KAAK,CAACC,OAAO,CAACuQ,KAAK,CAAC5K,QAAQ,CAAC,EAAE;oBACjC4K,KAAK,CAAC5K,QAAQ,CAAC/B,OAAO,CAACiO,CAAC,IAAIA,CAAC,CAACnM,OAAO,CAAC,CAAC,CAAC;kBAC1C,CAAC,MAAM;oBACL6K,KAAK,CAAC5K,QAAQ,CAACD,OAAO,CAAC,CAAC;kBAC1B;gBACF;gBACA,IAAI6K,KAAK,CAAC9K,QAAQ,EAAE8K,KAAK,CAAC9K,QAAQ,CAACC,OAAO,CAAC,CAAC;cAC9C;YACF,CAAC,CAAC;;YAEF;YACAjI,KAAK,CAAC2H,MAAM,CAACsK,SAAS,CAACvM,KAAK,CAAC;YAC7BjE,aAAa,CAAC4E,MAAM,CAACwK,EAAE,CAAC;YACxB;YACAvQ,oBAAoB,CAAC+F,MAAM,CAACwK,EAAE,CAAC;YAC/BrQ,oBAAoB,CAAC6F,MAAM,CAACwK,EAAE,CAAC;YAE/BtP,OAAO,CAACC,GAAG,CAAC,mBAAmBqP,EAAE,EAAE,CAAC;UACtC;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAI9D,KAAK,KAAKtM,WAAW,CAACS,IAAI,EAAE;QAC9B;;QAEA,IAAI;UACF,MAAM+O,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;UAEnC;UACA,MAAMK,SAAS,GAAGJ,OAAO,CAACK,GAAG;UAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;UAEnC;UACA,MAAMC,aAAa,GAAG/O,gBAAgB,CAACqC,GAAG,CAACsM,SAAS,CAAC;;UAErD;UACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;YACrD;YACA;YACA;YACA;YACA;YACA;UACF;;UAEA;UACA/O,gBAAgB,CAACkC,GAAG,CAACyM,SAAS,EAAEE,gBAAgB,CAAC;;UAEjD;UACA,IAAIN,OAAO,CAAC9N,IAAI,IAAI8N,OAAO,CAAC9N,IAAI,CAAC6M,aAAa,IAAI1M,KAAK,CAACC,OAAO,CAAC0N,OAAO,CAAC9N,IAAI,CAAC6M,aAAa,CAAC,EAAE;YAC3FiB,OAAO,CAAC9N,IAAI,CAAC6M,aAAa,CAAC7I,OAAO,CAAC4I,YAAY,IAAI;cACjD,MAAMnD,OAAO,GAAGmD,YAAY,CAACnD,OAAO;cAEpC,IAAI,CAACA,OAAO,EAAE;gBACZrK,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEkM,YAAY,CAAC;gBAC/C;cACF;;cAEA;;cAEA;cACA,IAAIA,YAAY,CAACjD,MAAM,IAAIxJ,KAAK,CAACC,OAAO,CAACwM,YAAY,CAACjD,MAAM,CAAC,EAAE;gBAC7D;gBACA,MAAMuI,UAAU,GAAG,EAAE;gBAErBtF,YAAY,CAACjD,MAAM,CAAC3F,OAAO,CAACmO,KAAK,IAAI;kBACnC;kBACA,IAAI,CAACA,KAAK,CAACC,OAAO,EAAE;oBAClBhT,OAAO,CAACsB,KAAK,CAAC,gBAAgB,EAAEyR,KAAK,CAAC;oBACtC;kBACF;kBAEA,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC;kBACxC;kBACA,MAAMC,SAAS,GAAGH,KAAK,CAACI,YAAY,GAClCC,oBAAoB,CAACL,KAAK,CAACI,YAAY,CAAC,GACxCE,iBAAiB,CAACL,OAAO,CAAC;;kBAE5B;kBACA,MAAMM,UAAU,GAAGP,KAAK,CAACQ,YAAY,IAAI,GAAG,CAAC,CAAC;kBAC9C,MAAMC,UAAU,GAAGC,QAAQ,CAACV,KAAK,CAACS,UAAU,CAAC,IAAI,CAAC;;kBAElD;;kBAEA;kBACA,MAAME,SAAS,GAAG;oBAChBV,OAAO;oBACPE,SAAS;oBACTK,YAAY,EAAED,UAAU;oBACxBE;kBACF,CAAC;;kBAED;kBACAV,UAAU,CAACa,IAAI,CAACD,SAAS,CAAC;;kBAE1B;kBACA;kBACA,IAAIE,eAAe,GAAGC,MAAM,CAACxJ,OAAO,CAAC;kBACrC,IAAIyJ,iBAAiB,GAAGzT,gBAAgB,CAACmC,GAAG,CAACoR,eAAe,CAAC;kBAE7D,IAAI,CAACE,iBAAiB,EAAE;oBACtB;oBACAF,eAAe,GAAGH,QAAQ,CAACpJ,OAAO,CAAC;oBACnCyJ,iBAAiB,GAAGzT,gBAAgB,CAACmC,GAAG,CAACoR,eAAe,CAAC;kBAC3D;kBAEA,IAAIE,iBAAiB,EAAE;oBACrB;oBACAC,wBAAwB,CAACD,iBAAiB,EAAEJ,SAAS,CAAC;;oBAEtD;oBACA,IAAI1J,oBAAoB,IAAIA,oBAAoB,CAACK,OAAO,KAAKA,OAAO,EAAE;sBACpEF,sBAAsB,CAAC6J,IAAI,KAAK;wBAC9B,GAAGA,IAAI;wBACP5J,OAAO,EAAE,IAAI;wBACb4I,OAAO;wBACPE,SAAS;wBACTxD,KAAK,EAAE4D,UAAU;wBACjBE;sBACF,CAAC,CAAC,CAAC;oBACL;kBACF,CAAC,MAAM;oBACL;kBAAA;gBAEJ,CAAC,CAAC;;gBAEF;gBACA,IAAIS,QAAQ,GAAG,IAAI;gBACnB;gBACA,MAAMC,KAAK,GAAGL,MAAM,CAACxJ,OAAO,CAAC;gBAC7B,IAAIhK,gBAAgB,CAACiC,GAAG,CAAC4R,KAAK,CAAC,EAAE;kBAC/BD,QAAQ,GAAGC,KAAK;gBAClB,CAAC,MAAM;kBACL;kBACA,MAAMC,KAAK,GAAGV,QAAQ,CAACpJ,OAAO,CAAC;kBAC/B,IAAIhK,gBAAgB,CAACiC,GAAG,CAAC6R,KAAK,CAAC,EAAE;oBAC/BF,QAAQ,GAAGE,KAAK;kBAClB;gBACF;gBAEA,IAAIF,QAAQ,KAAK,IAAI,EAAE;kBACrB;kBACA3T,kBAAkB,CAAC+B,GAAG,CAAC4R,QAAQ,EAAE;oBAC/BG,UAAU,EAAElM,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtBoC,MAAM,EAAEuI;kBACV,CAAC,CAAC;kBACF9S,OAAO,CAACC,GAAG,CAAC,aAAagU,QAAQ,KAAK,OAAOA,QAAQ,aAAa,CAAC;;kBAEnE;kBACA,IAAI7U,MAAM,CAACoL,mBAAmB,KAC1BpL,MAAM,CAACoL,mBAAmB,CAACkB,OAAO,KAAKuI,QAAQ,IAC/C7U,MAAM,CAACoL,mBAAmB,CAACkB,OAAO,KAAKmI,MAAM,CAACI,QAAQ,CAAC,IACvD7U,MAAM,CAACoL,mBAAmB,CAACkB,OAAO,KAAK+H,QAAQ,CAACQ,QAAQ,CAAC,CAAC,EAAE;oBAE9DjU,OAAO,CAACC,GAAG,CAAC,eAAegU,QAAQ,aAAa,CAAC;oBACjD;oBACA7U,MAAM,CAACoL,mBAAmB,CAACkB,OAAO,GAAGuI,QAAQ;;oBAE7C;oBACA,IAAI7U,MAAM,CAACqL,0BAA0B,IAAI,CAACrL,MAAM,CAACqL,0BAA0B,CAACiB,OAAO,EAAE;sBACnF1L,OAAO,CAACC,GAAG,CAAC,SAASgU,QAAQ,aAAa,CAAC;sBAC3C3F,UAAU,CAAC,MAAM;wBACflP,MAAM,CAACmP,qBAAqB,CAAC0F,QAAQ,CAAC;sBACxC,CAAC,EAAE,GAAG,CAAC;oBACT;kBACF;gBACF,CAAC,MAAM;kBACL;kBACA3T,kBAAkB,CAAC+B,GAAG,CAACgI,OAAO,EAAE;oBAC9B+J,UAAU,EAAElM,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtBoC,MAAM,EAAEuI;kBACV,CAAC,CAAC;kBACF;gBACF;cACF,CAAC,MAAM;gBACL9S,OAAO,CAACsB,KAAK,CAAC,eAAe,EAAEkM,YAAY,CAAC;cAC9C;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACLxN,OAAO,CAACsB,KAAK,CAAC,oCAAoC,EAAEoN,OAAO,CAAC;UAC9D;QACF,CAAC,CAAC,OAAOpN,KAAK,EAAE;UACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEA,KAAK,EAAEmN,OAAO,CAAC;QAC9C;QACA;MACF;;MAEA;MACA,IAAIjD,KAAK,KAAKtM,WAAW,CAACQ,GAAG,IAAIgP,OAAO,CAACc,IAAI,KAAK,KAAK,EAAE;QACvD;;QAEA;QACApQ,MAAM,CAAC4R,WAAW,CAAC;UACjBxB,IAAI,EAAE,KAAK;UACX5O,IAAI,EAAE8N,OAAO,CAAC9N,IAAI;UAClBmO,GAAG,EAAEL,OAAO,CAACK,GAAG;UAChBE,EAAE,EAAEP,OAAO,CAACO;QACd,CAAC,EAAE,GAAG,CAAC;QAEP,MAAMoF,OAAO,GAAG3F,OAAO,CAAC9N,IAAI;QAC5B,MAAM0T,KAAK,GAAGD,OAAO,CAACC,KAAK;QAC3B,MAAMC,MAAM,GAAGF,OAAO,CAACG,IAAI,IAAI,EAAE;QAEjCD,MAAM,CAAC3P,OAAO,CAAC6P,KAAK,IAAI;UACtB,MAAMC,OAAO,GAAGD,KAAK,CAACE,KAAK;UAC3B,MAAMC,SAAS,GAAGH,KAAK,CAACG,SAAS;UACjC,MAAMC,WAAW,GAAGJ,KAAK,CAACI,WAAW;UACrC,MAAMC,SAAS,GAAGL,KAAK,CAACK,SAAS;UACjC,MAAMC,OAAO,GAAGN,KAAK,CAACM,OAAO;;UAE7B;UACA,MAAMhF,QAAQ,GAAGvI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAC7CC,UAAU,CAACuG,OAAO,CAACW,OAAO,CAAC,EAC3BlH,UAAU,CAACuG,OAAO,CAACY,MAAM,CAC3B,CAAC;;UAED;UACA,IAAIC,WAAW,GAAG,EAAE;UACpB,IAAIC,YAAY,GAAG,EAAE;UAErB,QAAOP,SAAS;YACd,KAAK,KAAK;cAAG;cACXM,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,QAAQ;cACtBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,MAAM;cAAE;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF;cACED,WAAW,GAAGL,WAAW,IAAI,MAAM;cACnCM,YAAY,GAAG,SAAS;UAC5B;;UAEA;UACAC,iBAAiB,CAACrF,QAAQ,EAAEmF,WAAW,EAAEC,YAAY,CAAC;;UAEtD;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAI3J,KAAK,KAAKtM,WAAW,CAACT,KAAK,IAAIiQ,OAAO,CAACc,IAAI,KAAK,OAAO,EAAE;QAC3D;;QAEA,MAAM6F,SAAS,GAAG3G,OAAO,CAAC9N,IAAI;QAC9B,MAAM0U,OAAO,GAAGD,SAAS,CAACC,OAAO;QACjC,MAAMC,SAAS,GAAGF,SAAS,CAACE,SAAS;QACrC,MAAMC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAMzM,QAAQ,GAAG;UACfN,QAAQ,EAAEqF,UAAU,CAACuH,SAAS,CAACtE,OAAO,CAAC;UACvCvI,SAAS,EAAEsF,UAAU,CAACuH,SAAS,CAACvE,QAAQ;QAC1C,CAAC;;QAED;QACA,MAAMf,QAAQ,GAAGvI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAAC9E,QAAQ,CAACP,SAAS,EAAEO,QAAQ,CAACN,QAAQ,CAAC;;QAEtF;QACA,QAAO8M,SAAS;UACd,KAAK,GAAG;YAAG;YACTH,iBAAiB,CAACrF,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;YAClD;UACF,KAAK,KAAK;YAAG;YACXqF,iBAAiB,CAACrF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACXqF,iBAAiB,CAACrF,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;YAC/C;UACF,KAAK,IAAI;YAAG;YACV,MAAM0F,UAAU,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAE;YAC1CN,iBAAiB,CAACrF,QAAQ,EAAE,KAAK0F,UAAU,MAAM,EAAE,SAAS,CAAC;YAC7D;UACF,KAAK,IAAI;YAAG;YACVL,iBAAiB,CAACrF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVqF,iBAAiB,CAACrF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YAAG;YACZqF,iBAAiB,CAACrF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVqF,iBAAiB,CAACrF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVqF,iBAAiB,CAACrF,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX,MAAM4F,YAAY,GAAGN,SAAS,CAACK,UAAU,CAAC,CAAE;YAC5C,MAAME,QAAQ,GAAGP,SAAS,CAACQ,UAAU,CAAC,CAAM;YAC5CT,iBAAiB,CAACrF,QAAQ,EAAE,QAAQ+F,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,EAAE,SAAS,CAAC;YAC/F;QACJ;QAEA;MACF;MACA;MACA;MACA;MACA;MACA;MACA;IAEF,CAAC,CAAC,OAAOtU,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEmN,OAAO,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMsH,cAAc,GAAGA,CAAA,KAAM;IAC3B/V,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAM+V,KAAK,GAAG,QAAQ9W,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnES,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE+V,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBnW,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAEDgW,EAAE,CAACG,SAAS,GAAI3B,KAAK,IAAK;MACxB,IAAI;QACF,MAAMhG,OAAO,GAAGE,IAAI,CAACC,KAAK,CAAC6F,KAAK,CAAC7T,IAAI,CAAC;;QAEtC;QACA,IAAI6N,OAAO,CAACe,IAAI,KAAK,SAAS,EAAE;UAC9BxP,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEwO,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACe,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIf,OAAO,CAACe,IAAI,KAAK,SAAS,IAAIf,OAAO,CAACjD,KAAK,IAAIiD,OAAO,CAACC,OAAO,EAAE;UAClE;UACA,IAAID,OAAO,CAAC4H,SAAS,EAAE;YACrB,IAAIrP,mBAAmB,CAAC1E,GAAG,CAACmM,OAAO,CAAC4H,SAAS,CAAC,EAAE;cAC9C;cACA;YACF;;YAEA;YACArP,mBAAmB,CAAC5C,GAAG,CAACqK,OAAO,CAAC4H,SAAS,CAAC;;YAE1C;YACA,IAAIrP,mBAAmB,CAACsP,IAAI,GAAG,IAAI,EAAE;cACnC;cACA,MAAMC,QAAQ,GAAGxV,KAAK,CAACyV,IAAI,CAACxP,mBAAmB,CAAC;cAChD,KAAK,IAAI0G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;gBAC5B1G,mBAAmB,CAAClC,MAAM,CAACyR,QAAQ,CAAC7I,CAAC,CAAC,CAAC;cACzC;YACF;UACF;;UAEA;UACAc,iBAAiB,CAACC,OAAO,CAACjD,KAAK,EAAEmD,IAAI,CAAC8H,SAAS,CAAChI,OAAO,CAACC,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOpN,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAED2U,EAAE,CAACS,OAAO,GAAIpV,KAAK,IAAK;MACtBtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAED2U,EAAE,CAACU,OAAO,GAAG,MAAM;MACjB3W,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACAqO,UAAU,CAACyH,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACApO,aAAa,CAAC+D,OAAO,GAAGuK,EAAE;EAC5B,CAAC;EAEDxZ,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6K,YAAY,CAACoE,OAAO,EAAE;;IAE3B;IACAkL,aAAa,CAAC,CAAC;;IAEf;IACAnY,KAAK,GAAG,IAAI5B,KAAK,CAACga,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAIja,KAAK,CAACka,iBAAiB,CACxC,EAAE,EACF3X,MAAM,CAAC4X,UAAU,GAAG5X,MAAM,CAAC6X,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAAC/N,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCyU,MAAM,CAACnK,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB5C,SAAS,CAAC2B,OAAO,GAAGoL,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAIra,KAAK,CAACsa,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAACjY,MAAM,CAAC4X,UAAU,EAAE5X,MAAM,CAAC6X,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAACnY,MAAM,CAACoY,gBAAgB,CAAC;IAC/ClQ,YAAY,CAACoE,OAAO,CAAC+L,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAI9a,KAAK,CAAC+a,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5DnZ,KAAK,CAAC2F,GAAG,CAACuT,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAIhb,KAAK,CAACib,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAAC9O,QAAQ,CAAC1G,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1C5D,KAAK,CAAC2F,GAAG,CAACyT,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAIlb,KAAK,CAACib,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAAChP,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3C5D,KAAK,CAAC2F,GAAG,CAAC2T,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAInb,KAAK,CAACob,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAACjP,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChC2V,SAAS,CAACE,KAAK,GAAGhV,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7B6U,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACvV,QAAQ,GAAG,GAAG;IACxBhE,KAAK,CAAC2F,GAAG,CAAC4T,SAAS,CAAC;;IAEpB;IACA5Z,QAAQ,GAAG,IAAIrB,aAAa,CAAC+Z,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzDtZ,QAAQ,CAACia,aAAa,GAAG,IAAI;IAC7Bja,QAAQ,CAACka,aAAa,GAAG,IAAI;IAC7Bla,QAAQ,CAACma,kBAAkB,GAAG,KAAK;IACnCna,QAAQ,CAACyO,WAAW,GAAG,EAAE;IACzBzO,QAAQ,CAAC0O,WAAW,GAAG,GAAG;IAC1B1O,QAAQ,CAAC2O,aAAa,GAAG7J,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC/E,QAAQ,CAAC4O,aAAa,GAAG,CAAC;IAC1B5O,QAAQ,CAACqO,MAAM,CAACpK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BjE,QAAQ,CAACwO,MAAM,CAAC,CAAC;;IAEjB;IACA5M,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnB6W,MAAM,EAAE,CAAC,CAACA,MAAM;MAChB1Y,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpB2L,SAAS,EAAE,CAAC,CAACA,SAAS,CAAC2B;IACzB,CAAC,CAAC;;IAEF;IACA,MAAM8M,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAI9b,UAAU,CAAC,CAAC;QACtC8b,aAAa,CAACC,IAAI,CAChB,GAAGjZ,QAAQ,uBAAuB,EACjCkZ,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAACra,KAAK;;UAE/B;UACA,MAAMua,gBAAgB,GAAG,IAAInc,KAAK,CAACoc,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAAC1U,QAAQ,CAAEkN,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAAC/K,MAAM,EAAE;cAChB;cACA,IAAI+K,KAAK,CAAC5K,QAAQ,EAAE;gBAClB;gBACA,MAAM6K,WAAW,GAAG,IAAI3U,KAAK,CAACqc,oBAAoB,CAAC;kBACjDlO,KAAK,EAAE,QAAQ;kBAAO;kBACtBmO,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAI9H,KAAK,CAAC5K,QAAQ,CAAClB,GAAG,EAAE;kBACtB+L,WAAW,CAAC/L,GAAG,GAAG8L,KAAK,CAAC5K,QAAQ,CAAClB,GAAG;gBACtC;;gBAEA;gBACA8L,KAAK,CAAC5K,QAAQ,GAAG6K,WAAW;gBAE5BxR,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsR,KAAK,CAAC5D,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMoL,YAAY,CAACO,QAAQ,CAACnU,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMoM,KAAK,GAAGwH,YAAY,CAACO,QAAQ,CAAC,CAAC,CAAC;YACtCN,gBAAgB,CAAC5U,GAAG,CAACmN,KAAK,CAAC;UAC7B;;UAEA;UACA9S,KAAK,CAAC2F,GAAG,CAAC4U,gBAAgB,CAAC;;UAE3B;UACApb,gBAAgB,GAAGob,gBAAgB;UAEnChZ,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9BsZ,kBAAkB,CAAC,IAAI,CAAC;UACxBb,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAQ,GAAG,IAAK;UACPxZ,OAAO,CAACC,GAAG,CAAC,aAAa,CAACuZ,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAE9W,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACD+V,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMgB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA;;QAEA;QACA5D,cAAc,CAAC,CAAC;;QAEhB;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAEF,CAAC,CAAC,OAAOzU,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAMsY,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAIrB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMoB,WAAW,GAAIC,WAAW,IAAK;UACnCha,OAAO,CAACC,GAAG,CAAC,WAAW4Z,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAInd,UAAU,CAAC,CAAC;UAC/Bmd,MAAM,CAACpB,IAAI,CACTgB,GAAG,EACFf,IAAI,IAAK;YACR9Y,OAAO,CAACC,GAAG,CAAC,WAAW4Z,GAAG,EAAE,CAAC;YAC7BnB,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAU,GAAG,IAAK;YACPxZ,OAAO,CAACC,GAAG,CAAC,SAAS,CAACuZ,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAE9W,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAtB,KAAK,IAAK;YACTtB,OAAO,CAACsB,KAAK,CAAC,SAASuY,GAAG,EAAE,EAAEvY,KAAK,CAAC;YACpC,IAAI0Y,WAAW,GAAG,CAAC,EAAE;cACnBha,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3BqO,UAAU,CAAC,MAAMyL,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACLrB,MAAM,CAACrX,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAEDyY,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAInd,UAAU,CAAC,CAAC;IAC/Bmd,MAAM,CAACpB,IAAI,CACT,GAAGjZ,QAAQ,4BAA4B,EACvC,MAAOkZ,IAAI,IAAK;MACd,IAAI;QACF,MAAM3U,KAAK,GAAG2U,IAAI,CAACra,KAAK;QACxB0F,KAAK,CAACiM,KAAK,CAAC/N,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxB8B,KAAK,CAAC4E,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAE3B;QACA,IAAI5D,KAAK,EAAE;UACXA,KAAK,CAAC2F,GAAG,CAACD,KAAK,CAAC;;UAEhB;UACA,MAAMwV,eAAe,CAAC,CAAC;QACvB,CAAC,MAAM;UACL3Z,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC;QAChC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACAkY,GAAG,IAAK;MACPxZ,OAAO,CAACC,GAAG,CAAC,SAAS,CAACuZ,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAE9W,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAtB,KAAK,IAAK;MACTtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtB,OAAO,CAACsB,KAAK,CAAC,OAAO,EAAE;QACrB4Y,IAAI,EAAE5Y,KAAK,CAACkO,IAAI;QAChB2K,IAAI,EAAE7Y,KAAK,CAACmN,OAAO;QACnB2L,KAAK,EAAE,GAAGxa,QAAQ,4BAA4B;QAC9Cya,KAAK,EAAE,GAAGza,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAM0a,OAAO,GAAGA,CAAA,KAAM;MACpB1S,iBAAiB,CAAC8D,OAAO,GAAG6O,qBAAqB,CAACD,OAAO,CAAC;;MAE1D;MACArd,KAAK,CAAC2P,MAAM,CAAC,CAAC;;MAEd;MACA,MAAM4N,SAAS,GAAGja,KAAK,CAACka,QAAQ,CAAC,CAAC;;MAElC;MACA,IAAIrS,qBAAqB,CAACkO,IAAI,GAAG,CAAC,EAAE;QAClClO,qBAAqB,CAACxD,OAAO,CAAEV,KAAK,IAAK;UACvCA,KAAK,CAAC0I,MAAM,CAAC4N,SAAS,CAAC;QACzB,CAAC,CAAC;MACJ;MAEA,IAAIrc,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACuN,OAAO,GAAG,KAAK;;QAExB;QACA,MAAM+O,UAAU,GAAG9c,gBAAgB,CAACmL,QAAQ,CAACjH,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAM6Y,eAAe,GAAG/c,gBAAgB,CAACuS,QAAQ,CAACjO,CAAC;;QAEnD;QACA;QACA,MAAM0Y,gBAAgB,GAAG,EAAED,eAAe,GAAGzX,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;;QAEnE;QACA,MAAM0X,YAAY,GAAG,IAAIhe,KAAK,CAACiG,OAAO,CACpC,CAAC,EAAE,GAAGI,IAAI,CAAC4X,GAAG,CAACF,gBAAgB,CAAC,EAChC,GAAG,EACH,CAAC,EAAE,GAAG1X,IAAI,CAAC6X,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA,MAAMI,oBAAoB,GAAGN,UAAU,CAAC5Y,KAAK,CAAC,CAAC,CAACsC,GAAG,CAACyW,YAAY,CAAC;QACjE,MAAMI,YAAY,GAAGP,UAAU,CAAC5Y,KAAK,CAAC,CAAC;;QAEvC;QACA,IAAI,CAACgG,kBAAkB,CAAC4D,OAAO,EAAE;UAC/B5D,kBAAkB,CAAC4D,OAAO,GAAGsP,oBAAoB,CAAClZ,KAAK,CAAC,CAAC;QAC3D;QAEA,IAAI,CAACiG,gBAAgB,CAAC2D,OAAO,EAAE;UAC7B3D,gBAAgB,CAAC2D,OAAO,GAAGuP,YAAY,CAACnZ,KAAK,CAAC,CAAC;QACjD;;QAEA;QACAgG,kBAAkB,CAAC4D,OAAO,CAACwP,IAAI,CAACF,oBAAoB,EAAE,CAAC,GAAGhT,eAAe,CAAC;QAC1ED,gBAAgB,CAAC2D,OAAO,CAACwP,IAAI,CAACD,YAAY,EAAE,CAAC,GAAGjT,eAAe,CAAC;;QAEhE;QACA8O,MAAM,CAAC/N,QAAQ,CAACwD,IAAI,CAACzE,kBAAkB,CAAC4D,OAAO,CAAC;;QAEhD;QACAoL,MAAM,CAAC/K,EAAE,CAAC1J,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACAyU,MAAM,CAACnK,MAAM,CAAC5E,gBAAgB,CAAC2D,OAAO,CAAC;;QAEvC;QACAoL,MAAM,CAACqE,sBAAsB,CAAC,CAAC;QAC/BrE,MAAM,CAAC7J,YAAY,CAAC,CAAC;QACrB6J,MAAM,CAAC5J,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACA9O,QAAQ,CAACuN,OAAO,GAAG,KAAK;;QAExB;QACAvN,QAAQ,CAACqO,MAAM,CAACF,IAAI,CAACxE,gBAAgB,CAAC2D,OAAO,CAAC;QAC9CtN,QAAQ,CAACwO,MAAM,CAAC,CAAC;QAEjB5M,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnBmb,IAAI,EAAEV,UAAU,CAACvM,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAE4I,MAAM,CAAC/N,QAAQ,CAACoF,OAAO,CAAC,CAAC;UAC/BkN,IAAI,EAAEtT,gBAAgB,CAAC2D,OAAO,CAACyC,OAAO,CAAC,CAAC;UACxCmN,IAAI,EAAExE,MAAM,CAACyE,iBAAiB,CAAC,IAAI1e,KAAK,CAACiG,OAAO,CAAC,CAAC,CAAC,CAACqL,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIhQ,UAAU,KAAK,QAAQ,EAAE;QAClC;QACA2J,kBAAkB,CAAC4D,OAAO,GAAG,IAAI;QACjC3D,gBAAgB,CAAC2D,OAAO,GAAG,IAAI;;QAE/B;QACAtN,QAAQ,CAACuN,OAAO,GAAG,IAAI;;QAEvB;QACAmL,MAAM,CAAC/K,EAAE,CAAC1J,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAIa,IAAI,CAACK,GAAG,CAACuT,MAAM,CAAC/N,QAAQ,CAAC7G,CAAC,CAAC,GAAG,EAAE,EAAE;UACpC4U,MAAM,CAAC/N,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BjE,QAAQ,CAACqO,MAAM,CAACpK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5ByU,MAAM,CAACnK,MAAM,CAACvO,QAAQ,CAACqO,MAAM,CAAC;UAC9BrO,QAAQ,CAACwO,MAAM,CAAC,CAAC;QACnB;;QAEA;QACA;QACAkK,MAAM,CAAC7J,YAAY,CAAC,CAAC;QACrB6J,MAAM,CAAC5J,iBAAiB,CAAC,IAAI,CAAC;MAEhC,CAAC,MAAM,IAAI/O,UAAU,KAAK,cAAc,EAAE;QACxC;QACA2J,kBAAkB,CAAC4D,OAAO,GAAG,IAAI;QACjC3D,gBAAgB,CAAC2D,OAAO,GAAG,IAAI;;QAE/B;QACAtN,QAAQ,CAACwO,MAAM,CAAC,CAAC;MACnB;MAEA,IAAIxO,QAAQ,EAAEA,QAAQ,CAACwO,MAAM,CAAC,CAAC;MAC/B,IAAInO,KAAK,IAAIqY,MAAM,EAAE;QACnBI,QAAQ,CAACsE,MAAM,CAAC/c,KAAK,EAAEqY,MAAM,CAAC;MAChC;IACF,CAAC;IAEDwD,OAAO,CAAC,CAAC;;IAET;IACA,MAAMmB,YAAY,GAAGA,CAAA,KAAM;MACzB3E,MAAM,CAAC4E,MAAM,GAAGtc,MAAM,CAAC4X,UAAU,GAAG5X,MAAM,CAAC6X,WAAW;MACtDH,MAAM,CAACqE,sBAAsB,CAAC,CAAC;MAC/BjE,QAAQ,CAACG,OAAO,CAACjY,MAAM,CAAC4X,UAAU,EAAE5X,MAAM,CAAC6X,WAAW,CAAC;IACzD,CAAC;IACD7X,MAAM,CAACuc,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACArc,MAAM,CAACwc,aAAa,GAAG,MAAM;MAC3B,IAAI7R,SAAS,CAAC2B,OAAO,EAAE;QACrB3B,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC0H,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC5C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC;QAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAI9O,QAAQ,EAAE;UACZA,QAAQ,CAACqO,MAAM,CAACpK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BjE,QAAQ,CAACuN,OAAO,GAAG,IAAI;UACvBvN,QAAQ,CAACwO,MAAM,CAAC,CAAC;QACnB;QAEAzO,UAAU,GAAG,QAAQ;QACrB6B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAI2H,iBAAiB,CAAC8D,OAAO,EAAE;QAC7BmQ,oBAAoB,CAACjU,iBAAiB,CAAC8D,OAAO,CAAC;QAC/C9D,iBAAiB,CAAC8D,OAAO,GAAG,IAAI;MAClC;;MAEA;MACAzO,KAAK,CAAC6e,SAAS,CAAC,CAAC;;MAEjB;MACA1T,qBAAqB,CAACxD,OAAO,CAACV,KAAK,IAAI;QACrCN,eAAe,CAACe,WAAW,CAACT,KAAK,CAAC;MACpC,CAAC,CAAC;MACFkE,qBAAqB,CAACnC,KAAK,CAAC,CAAC;;MAE7B;MACArC,eAAe,CAACoC,OAAO,CAAC,CAAC;;MAEzB;MACA,IAAIvH,KAAK,EAAE;QACT,MAAMsd,eAAe,GAAG,EAAE;QAC1Btd,KAAK,CAAC4F,QAAQ,CAAEC,MAAM,IAAK;UACzB,IAAIA,MAAM,CAACkC,MAAM,EAAE;YACjB,IAAIlC,MAAM,CAACmC,QAAQ,EAAE;cACnBnC,MAAM,CAACmC,QAAQ,CAACC,OAAO,CAAC,CAAC;YAC3B;YACA,IAAIpC,MAAM,CAACqC,QAAQ,EAAE;cACnB,IAAI5F,KAAK,CAACC,OAAO,CAACsD,MAAM,CAACqC,QAAQ,CAAC,EAAE;gBAClCrC,MAAM,CAACqC,QAAQ,CAAC/B,OAAO,CAAC+B,QAAQ,IAAI;kBAClC,IAAIA,QAAQ,CAAClB,GAAG,EAAEkB,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;kBACxCC,QAAQ,CAACD,OAAO,CAAC,CAAC;gBACpB,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL,IAAIpC,MAAM,CAACqC,QAAQ,CAAClB,GAAG,EAAEnB,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;gBACtDpC,MAAM,CAACqC,QAAQ,CAACD,OAAO,CAAC,CAAC;cAC3B;YACF;YACA,IAAIpC,MAAM,KAAK7F,KAAK,EAAE;cACpBsd,eAAe,CAACpI,IAAI,CAACrP,MAAM,CAAC;YAC9B;UACF;QACF,CAAC,CAAC;;QAEF;QACAyX,eAAe,CAACnX,OAAO,CAACoX,GAAG,IAAI;UAC7B,IAAIA,GAAG,CAAC7V,MAAM,EAAE;YACd6V,GAAG,CAAC7V,MAAM,CAACC,MAAM,CAAC4V,GAAG,CAAC;UACxB;QACF,CAAC,CAAC;QAEFvd,KAAK,CAACwH,KAAK,CAAC,CAAC;MACf;;MAEA;MACA,IAAIiR,QAAQ,EAAE;QACZA,QAAQ,CAAC+E,gBAAgB,CAAC,IAAI,CAAC;QAC/B,IAAI3U,YAAY,CAACoE,OAAO,IAAIwL,QAAQ,CAACQ,UAAU,IAAIR,QAAQ,CAACQ,UAAU,CAACwE,UAAU,KAAK5U,YAAY,CAACoE,OAAO,EAAE;UAC1GpE,YAAY,CAACoE,OAAO,CAACyQ,WAAW,CAACjF,QAAQ,CAACQ,UAAU,CAAC;QACvD;QACAR,QAAQ,CAACxQ,OAAO,CAAC,CAAC;QAClBwQ,QAAQ,CAACkF,gBAAgB,CAAC,CAAC;MAC7B;;MAEA;MACA,IAAIhe,QAAQ,EAAE;QACZA,QAAQ,CAACsI,OAAO,CAAC,CAAC;MACpB;;MAEA;MACA3H,oBAAoB,CAACkH,KAAK,CAAC,CAAC;MAC5BhH,oBAAoB,CAACgH,KAAK,CAAC,CAAC;MAC5B9F,gBAAgB,CAAC8F,KAAK,CAAC,CAAC;MACxB/F,aAAa,CAAC+F,KAAK,CAAC,CAAC;MACrB5F,gBAAgB,CAAC4F,KAAK,CAAC,CAAC;MACxB3F,kBAAkB,CAAC2F,KAAK,CAAC,CAAC;MAE1BjG,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxD,SAAS,CAAC,MAAM;IACd;IACAgE,qBAAqB,CAAC,CAAC;;IAEvB;IACA,MAAM4b,uBAAuB,GAAGA,CAAA,KAAM;MACpCrc,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCQ,qBAAqB,CAAC,CAAC;IACzB,CAAC;;IAED;IACArB,MAAM,CAACuc,gBAAgB,CAAC,oBAAoB,EAAEU,uBAAuB,CAAC;;IAEtE;IACA,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnC9b,qBAAqB,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC;;IAET;IACA,OAAO,MAAM;MACXrB,MAAM,CAACod,mBAAmB,CAAC,oBAAoB,EAAEH,uBAAuB,CAAC;MACzEI,aAAa,CAACH,UAAU,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7f,SAAS,CAAC,MAAM;IACd;IACA,IAAIgC,KAAK,IAAI+I,SAAS,CAACkE,OAAO,EAAE;MAC9B1L,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA,MAAMyc,KAAK,GAAGpO,UAAU,CAAC,MAAM;QAC7B,IAAI7P,KAAK,IAAI+I,SAAS,CAACkE,OAAO,EAAE;UAAG;UACjCiR,mBAAmB,CAACnV,SAAS,CAACkE,OAAO,CAAC;QACxC;MACF,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAMkR,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM;MACL1c,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC,EAAE,CAACxB,KAAK,CAAC,CAAC;;EAEX;EACAhC,SAAS,CAAC,MAAM;IACd,IAAI6K,YAAY,CAACoE,OAAO,EAAE;MACxB;MACA,MAAMmR,WAAW,GAAIpI,KAAK,IAAK;QAC7B,IAAIhW,KAAK,IAAIsL,SAAS,CAAC2B,OAAO,EAAE;UAC9BoR,gBAAgB,CAACrI,KAAK,EAAEnN,YAAY,CAACoE,OAAO,EAAEjN,KAAK,EAAEsL,SAAS,CAAC2B,OAAO,CAAC;QACzE;MACF,CAAC;;MAED;MACApE,YAAY,CAACoE,OAAO,CAACiQ,gBAAgB,CAAC,OAAO,EAAEkB,WAAW,CAAC;;MAE3D;MACA7c,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,CAAC,CAACqH,YAAY,CAACoE,OAAO,CAAC;;MAEpD;MACA,OAAO,MAAM;QACX,IAAIpE,YAAY,CAACoE,OAAO,EAAE;UACxBpE,YAAY,CAACoE,OAAO,CAAC8Q,mBAAmB,CAAC,OAAO,EAAEK,WAAW,CAAC;UAC9D7c,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACxB,KAAK,EAAEsL,SAAS,CAAC2B,OAAO,CAAC,CAAC;;EAE9B;EACA,MAAMqR,SAAS,GAAGngB,WAAW,CAAC,MAAM;IAClCoD,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B;EACF,CAAC,EAAE,CAACqH,YAAY,EAAE8D,aAAa,EAAE/K,gBAAgB,CAAC,CAAC;;EAEnD;EACA,MAAM2c,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMvW,QAAQ,GAAG,IAAI5J,KAAK,CAACogB,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChD,MAAMtW,QAAQ,GAAG,IAAI9J,KAAK,CAACqgB,iBAAiB,CAAC;MAAElS,KAAK,EAAE;IAAS,CAAC,CAAC;IACjE,MAAM8I,iBAAiB,GAAG,IAAIjX,KAAK,CAACsgB,IAAI,CAAC1W,QAAQ,EAAEE,QAAQ,CAAC;;IAE5D;IACA,MAAMyW,YAAY,GAAG,IAAIvgB,KAAK,CAACwgB,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC5D,MAAMC,YAAY,GAAG,IAAIzgB,KAAK,CAACqgB,iBAAiB,CAAC;MAAElS,KAAK,EAAE;IAAS,CAAC,CAAC;IACrE,MAAMuS,SAAS,GAAG,IAAI1gB,KAAK,CAACsgB,IAAI,CAACC,YAAY,EAAEE,YAAY,CAAC;IAC5DC,SAAS,CAACxU,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAClCyR,iBAAiB,CAAC1P,GAAG,CAACmZ,SAAS,CAAC;IAEhC,OAAOzJ,iBAAiB;EAC1B,CAAC;;EAED;EACA,MAAM0J,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAC/e,KAAK,EAAE;;IAEZ;IACA4B,gBAAgB,CAACuE,OAAO,CAAC,CAAC6Y,QAAQ,EAAEpT,OAAO,KAAK;MAC9C,IAAIoT,QAAQ,CAACtZ,KAAK,EAAE;QAClB;QACA,MAAMuZ,cAAc,GAAG,IAAI7gB,KAAK,CAAC8gB,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxD,MAAMC,cAAc,GAAG,IAAI/gB,KAAK,CAACqgB,iBAAiB,CAAC;UACjDlS,KAAK,EAAE,QAAQ;UAAC;UAChB2G,WAAW,EAAE,KAAK;UAClBU,OAAO,EAAE,GAAG;UAAG;UACfwL,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,MAAMC,UAAU,GAAG,IAAIjhB,KAAK,CAACsgB,IAAI,CAACO,cAAc,EAAEE,cAAc,CAAC;QACjEE,UAAU,CAAC/U,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE;;QAEnC;QACAyb,UAAU,CAACC,QAAQ,GAAG;UACpBvO,IAAI,EAAE,cAAc;UACpBnF,OAAO,EAAEA,OAAO;UAChBsD,IAAI,EAAE8P,QAAQ,CAACjQ,YAAY,CAACG,IAAI;UAChCqQ,aAAa,EAAE;QACjB,CAAC;;QAED;QACAP,QAAQ,CAACtZ,KAAK,CAACC,GAAG,CAAC0Z,UAAU,CAAC;QAE9B9d,OAAO,CAACC,GAAG,CAAC,OAAOwd,QAAQ,CAACjQ,YAAY,CAACG,IAAI,KAAKtD,OAAO,aAAa,CAAC;MACzE;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA5N,SAAS,CAAC,MAAM;IACd;IACA,MAAMigB,KAAK,GAAGpO,UAAU,CAAC,MAAM;MAC7B,IAAIjO,gBAAgB,CAACiW,IAAI,GAAG,CAAC,EAAE;QAC7BtW,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1B;MACF;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAE;;IAEX,OAAO,MAAM2c,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACAjgB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACA,IAAIgO,0BAA0B,CAACiB,OAAO,EAAE;QACtC+Q,aAAa,CAAChS,0BAA0B,CAACiB,OAAO,CAAC;QACjDjB,0BAA0B,CAACiB,OAAO,GAAG,IAAI;QACzC1L,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC9B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAxD,SAAS,CAAC,MAAM;IACd,IAAI,CAACyN,mBAAmB,CAACE,OAAO,IAAIK,0BAA0B,CAACiB,OAAO,EAAE;MACtE+Q,aAAa,CAAChS,0BAA0B,CAACiB,OAAO,CAAC;MACjDjB,0BAA0B,CAACiB,OAAO,GAAG,IAAI;MACzClB,mBAAmB,CAACkB,OAAO,GAAG,IAAI;MAClC1L,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACnC;EACF,CAAC,EAAE,CAACiK,mBAAmB,CAACE,OAAO,CAAC,CAAC;;EAEjC;EACA3N,SAAS,CAAC,MAAM;IACd;IACA,IAAIa,iBAAiB,IAAIA,iBAAiB,CAACmQ,aAAa,IAAInQ,iBAAiB,CAACmQ,aAAa,CAACtI,MAAM,GAAG,CAAC,EAAE;MACtG;MACA,IAAI,CAAC6E,oBAAoB,EAAE;QACzB;QACA,MAAMiU,6BAA6B,GAAG3gB,iBAAiB,CAACmQ,aAAa,CAACvM,IAAI,CACxEsM,YAAY,IAAIA,YAAY,CAACa,eAAe,KAAK,KAAK,IAAIb,YAAY,CAACnD,OACzE,CAAC;;QAED;QACA,MAAM6T,kBAAkB,GAAGD,6BAA6B,IAAI3gB,iBAAiB,CAACmQ,aAAa,CAAC,CAAC,CAAC;QAE9FzN,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEie,kBAAkB,CAACvQ,IAAI,EAClC,QAAQ,EAAEsQ,6BAA6B,GAAG,GAAG,GAAG,GAAG,CAAC;;QAEhE;QACA,MAAMvB,KAAK,GAAGpO,UAAU,CAAC,MAAM;UAC7BhB,wBAAwB,CAAC4Q,kBAAkB,CAACvQ,IAAI,CAAC;QACnD,CAAC,EAAE,IAAI,CAAC;QAER,OAAO,MAAMiP,YAAY,CAACF,KAAK,CAAC;MAClC;IACF;EACF,CAAC,EAAE,CAACpf,iBAAiB,EAAE0M,oBAAoB,CAAC,CAAC;;EAE7C;EACA,MAAMmU,yBAAyB,GAAIC,iBAAiB,IAAK;IACrD,IAAI,CAAC3f,KAAK,IAAI,OAAOA,KAAK,CAAC4F,QAAQ,KAAK,UAAU,IAAI,CAAC+Z,iBAAiB,EAAE;IAC1E,IAAI;MACF;MACA3f,KAAK,CAAC6a,QAAQ,CACX1T,MAAM,CAACoW,GAAG,IAAIA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAACM,qBAAqB,CAAC,CACjEzZ,OAAO,CAACoX,GAAG,IAAIvd,KAAK,CAAC2H,MAAM,CAAC4V,GAAG,CAAC,CAAC;MACpC;MACA1e,iBAAiB,CAACmQ,aAAa,CAAC7I,OAAO,CAAC4I,YAAY,IAAI;QACtD,IAAI,CAACA,YAAY,CAAC8Q,SAAS,IAAI,CAACvd,KAAK,CAACC,OAAO,CAACwM,YAAY,CAAC8Q,SAAS,CAAC,EAAE;QACvE;QACA9Q,YAAY,CAAC8Q,SAAS,CAAC1Z,OAAO,CAAE2Z,QAAQ,IAAK;UAC3C;UACA,IAAI,CAACA,QAAQ,CAAC/V,SAAS,IAAI,CAAC+V,QAAQ,CAAC9V,QAAQ,IAAI+V,KAAK,CAAC1Q,UAAU,CAACyQ,QAAQ,CAAC/V,SAAS,CAAC,CAAC,IAAIgW,KAAK,CAAC1Q,UAAU,CAACyQ,QAAQ,CAAC9V,QAAQ,CAAC,CAAC,EAAE;YAC9H,OAAO,CAAC;UACV;;UAEA;UACA,MAAMgW,OAAO,GAAGlhB,WAAW,CAACkhB,OAAO,CAAC7Y,MAAM,CACxC8Y,CAAC,IAAIA,CAAC,CAACrf,QAAQ,KAAKmO,YAAY,CAACG,IAAI,IAAI+Q,CAAC,CAACH,QAAQ,KAAKA,QAAQ,CAAC5Q,IACnE,CAAC;UACD,IAAI8Q,OAAO,CAACtZ,MAAM,KAAK,CAAC,EAAE;UAC1B;UACAnF,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEuN,YAAY,CAACG,IAAI,EAAE,IAAI,EAAE4Q,QAAQ,CAAC5Q,IAAI,EAAE,MAAM,EAAE8Q,OAAO,CAACtZ,MAAM,CAAC;UACnF,MAAM4K,QAAQ,GAAGqO,iBAAiB,CAACvQ,YAAY,CAC7CC,UAAU,CAACyQ,QAAQ,CAAC/V,SAAS,CAAC,EAC9BsF,UAAU,CAACyQ,QAAQ,CAAC9V,QAAQ,CAC9B,CAAC;UACD;UACA,MAAMkW,KAAK,GAAG,IAAI9hB,KAAK,CAACoc,KAAK,CAAC,CAAC;UAC/B0F,KAAK,CAAC5V,QAAQ,CAAC1G,GAAG,CAAC0N,QAAQ,CAAC/N,CAAC,EAAE,EAAE,EAAE,CAAC+N,QAAQ,CAAC7N,CAAC,CAAC,CAAC,CAAC;UACjDyc,KAAK,CAACZ,QAAQ,GAAG;YAAEM,qBAAqB,EAAE;UAAK,CAAC;UAChD;UACA,MAAMO,QAAQ,GAAG,EAAE,CAAC,CAAC;UACrB,MAAMC,WAAW,GAAG,CAAC,CAAC,CAAC;UACvB,MAAMC,UAAU,GAAGL,OAAO,CAACtZ,MAAM,GAAGyZ,QAAQ,GAAG,CAACH,OAAO,CAACtZ,MAAM,GAAG,CAAC,IAAI0Z,WAAW;UACjF;UACA,MAAME,MAAM,GAAG,GAAG,CAAC,CAAC;UACpB,MAAMC,SAAS,GAAG,GAAG,CAAC,CAAC;UACvB,MAAMC,MAAM,GAAG,EAAE,CAACR,OAAO,CAACtZ,MAAM,GAAG,CAAC,KAAK4Z,MAAM,GAAGC,SAAS,CAAC,CAAC,GAAG,CAAC;UACjEP,OAAO,CAAC7Z,OAAO,CAAC,CAACsa,MAAM,EAAEC,GAAG,KAAK;YAC/B;YACA,MAAMC,aAAa,GAAG,IAAIviB,KAAK,CAACwiB,aAAa,CAAC,CAAC;YAC/C,MAAMC,QAAQ,GAAG,GAAG1f,QAAQ,WAAWsf,MAAM,CAAC1P,IAAI,MAAM;YACxD;YACA,MAAM+P,YAAY,GAAG,IAAI1iB,KAAK,CAACqgB,iBAAiB,CAAC;cAC/CzX,GAAG,EAAE2Z,aAAa,CAACvG,IAAI,CAACyG,QAAQ,CAAC;cACjC3N,WAAW,EAAE,IAAI;cACjBU,OAAO,EAAE,CAAC,CAAC;YACb,CAAC,CAAC;YACF;YACA,MAAMmN,UAAU,GAAG,IAAI3iB,KAAK,CAACqgB,iBAAiB,CAAC;cAC7ClS,KAAK,EAAE,QAAQ;cACf2G,WAAW,EAAE,IAAI;cACjBU,OAAO,EAAE,GAAG,CAAC;YACf,CAAC,CAAC;YACF;YACA,MAAMoN,OAAO,GAAGV,MAAM,GAAG,IAAI;YAC7B,MAAMW,QAAQ,GAAGX,MAAM,GAAG,IAAI;YAC9B;YACA;YACA,MAAMY,UAAU,GAAG,IAAI9iB,KAAK,CAAC+iB,aAAa,CAACH,OAAO,EAAEC,QAAQ,CAAC;YAC7D,MAAMG,MAAM,GAAG,IAAIhjB,KAAK,CAACsgB,IAAI,CAACwC,UAAU,EAAEH,UAAU,CAAC;YACrD;YACA,MAAMM,YAAY,GAAG,IAAIjjB,KAAK,CAAC+iB,aAAa,CAACb,MAAM,EAAEA,MAAM,CAAC;YAC5D,MAAMgB,QAAQ,GAAG,IAAIljB,KAAK,CAACsgB,IAAI,CAAC2C,YAAY,EAAEP,YAAY,CAAC;YAC3D;YACAQ,QAAQ,CAAChX,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;YACjC;YACA,MAAM2d,SAAS,GAAG,IAAInjB,KAAK,CAACoc,KAAK,CAAC,CAAC;YACnC+G,SAAS,CAAC5b,GAAG,CAACyb,MAAM,CAAC;YACrBG,SAAS,CAAC5b,GAAG,CAAC2b,QAAQ,CAAC;YACvB;YACAC,SAAS,CAACjX,QAAQ,CAAC1G,GAAG,CAAC4c,MAAM,GAAGE,GAAG,IAAIJ,MAAM,GAAGC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACjE;YACAgB,SAAS,CAAC5N,WAAW,GAAG,GAAG,CAAC,CAAC;YAC7B4N,SAAS,CAACjC,QAAQ,GAAG;cACnBkC,QAAQ,EAAEf,MAAM,CAAC5P,EAAE;cACnB4Q,UAAU,EAAEhB,MAAM,CAAC1P,IAAI;cACvB+O,QAAQ,EAAEA,QAAQ,CAAC5Q,IAAI;cACvBwS,oBAAoB,EAAE;YACxB,CAAC;YACDxB,KAAK,CAACva,GAAG,CAAC4b,SAAS,CAAC;UACtB,CAAC,CAAC;UACFvhB,KAAK,CAAC2F,GAAG,CAACua,KAAK,CAAC;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO5Y,CAAC,EAAE;MACV/F,OAAO,CAACogB,IAAI,CAAC,sBAAsB,EAAEra,CAAC,CAAC;MACvC;IACF;EACF,CAAC;;EAEH;EACAtJ,SAAS,CAAC,MAAM;IACd,IAAIgC,KAAK,IAAI,OAAOA,KAAK,CAAC4F,QAAQ,KAAK,UAAU,IAAImD,SAAS,CAACkE,OAAO,EAAE;MACtEyS,yBAAyB,CAAC3W,SAAS,CAACkE,OAAO,CAAC;IAC9C;EACF,CAAC,EAAE,CAACjN,KAAK,EAAE+I,SAAS,CAACkE,OAAO,CAAC,CAAC;;EAE9B;EACAjP,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,KAAK,IAAI,CAACsL,SAAS,CAAC2B,OAAO,EAAE;IAClC,MAAM2U,gBAAgB,GAAGA,CAAA,KAAM;MAC7B;MACA5hB,KAAK,CAAC6a,QAAQ,CAAC1U,OAAO,CAACoX,GAAG,IAAI;QAC5B,IAAIA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAACM,qBAAqB,EAAE;UACtDrC,GAAG,CAACrP,MAAM,CAAC5C,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAC/G,CAAC,EAAEga,GAAG,CAACjT,QAAQ,CAAC7G,CAAC,EAAE6H,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAC3G,CAAC,CAAC;QACxF;MACF,CAAC,CAAC;MACFmY,qBAAqB,CAAC8F,gBAAgB,CAAC;IACzC,CAAC;IACDA,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAAC5hB,KAAK,CAAC,CAAC;EAEX,oBACEhB,OAAA,CAAAE,SAAA;IAAA2b,QAAA,gBACE7b,OAAA;MAAM6iB,KAAK,EAAExV,UAAW;MAAAwO,QAAA,EAAC;IAAK;MAAAiH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrCjjB,OAAA,CAACL,MAAM;MACLkjB,KAAK,EAAE3V,uBAAwB;MAC/BgW,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAEtT,wBAAyB;MACnCuT,OAAO,EAAEvjB,iBAAiB,CAACmQ,aAAa,CAAChI,GAAG,CAAC+H,YAAY,KAAK;QAC5DD,KAAK,EAAEC,YAAY,CAACG,IAAI;QACxBmT,KAAK,EAAEtT,YAAY,CAACG;MACtB,CAAC,CAAC,CAAE;MACJ2I,IAAI,EAAC,OAAO;MACZyK,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACb7X,MAAM,EAAE,IAAI;QACZ8X,SAAS,EAAE;MACb,CAAE;MACF1T,KAAK,EAAEvD,oBAAoB,GAAGA,oBAAoB,CAAC2D,IAAI,GAAG+E;IAAU;MAAA6N,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eACFjjB,OAAA;MAAKyjB,GAAG,EAAE5Z,YAAa;MAACgZ,KAAK,EAAE;QAAEzV,KAAK,EAAE,MAAM;QAAEqF,MAAM,EAAE;MAAO;IAAE;MAAAqQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGnExW,mBAAmB,CAACE,OAAO,iBAC1B3M,OAAA;MACE6iB,KAAK,EAAE;QACLvX,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAAC/G,CAAC,IAAI;QAC3C4I,GAAG,EAAE,GAAGV,mBAAmB,CAACnB,QAAQ,CAAC7G,CAAC,IAAI;QAC1CgH,SAAS,EAAE,wBAAwB;QACnCC,MAAM,EAAE,IAAI;QACZK,eAAe,EAAE,qBAAqB;QACtCwB,KAAK,EAAE,OAAO;QACdtB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,8BAA8B;QACzCN,OAAO,EAAE,GAAG;QACZ4X,QAAQ,EAAE,OAAO;QAAE;QACnBvX,QAAQ,EAAE,MAAM,CAAC;MACnB,CAAE;MAAA0P,QAAA,GAEDpP,mBAAmB,CAACI,OAAO,eAC5B7M,OAAA;QACE6iB,KAAK,EAAE;UACLvX,QAAQ,EAAE,UAAU;UACpB6B,GAAG,EAAE,KAAK;UACVwW,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,MAAM;UAClB5X,MAAM,EAAE,MAAM;UACduB,KAAK,EAAE,OAAO;UACdpB,QAAQ,EAAE,MAAM;UAChBD,MAAM,EAAE,SAAS;UACjBJ,OAAO,EAAE;QACX,CAAE;QACF+X,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAACpX,sBAAsB,CAAE;QAAAmP,QAAA,EAC3D;MAED;QAAAiH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAEDjjB,OAAA;MAAK6iB,KAAK,EAAExX,oBAAqB;MAAAwQ,QAAA,gBAC/B7b,OAAA;QACE6iB,KAAK,EAAE;UACL,GAAGhX,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF0Y,OAAO,EAAE7V,kBAAmB;QAAA6N,QAAA,EAC7B;MAED;QAAAiH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjjB,OAAA;QACE6iB,KAAK,EAAE;UACL,GAAGhX,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF0Y,OAAO,EAAE1V,kBAAmB;QAAA0N,QAAA,EAC7B;MAED;QAAAiH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAArZ,EAAA,CAr7DMJ,WAAW;AAAAua,EAAA,GAAXva,WAAW;AAs7DjB,SAAS6K,gBAAgBA,CAAC2P,IAAI,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAMC,MAAM,GAAG;IACbC,QAAQ,EAAEF,UAAU,CAACE,QAAQ,IAAI,OAAO;IACxChY,QAAQ,EAAE8X,UAAU,CAAC9X,QAAQ,IAAI,EAAE;IAAE;IACrCqB,UAAU,EAAEyW,UAAU,CAACzW,UAAU,IAAI,MAAM;IAC3C4W,eAAe,EAAEH,UAAU,CAACG,eAAe,IAAI,CAAC;IAChDC,WAAW,EAAEJ,UAAU,CAACI,WAAW,IAAI;MAAE9P,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAExM,CAAC,EAAE;IAAI,CAAC;IACnE8D,eAAe,EAAEkY,UAAU,CAAClY,eAAe,IAAI;MAAEwI,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAExM,CAAC,EAAE;IAAI,CAAC;IACjFyM,SAAS,EAAEuP,UAAU,CAACvP,SAAS,IAAI;MAAEH,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAExM,CAAC,EAAE;IAAI,CAAC;IAC/D6D,OAAO,EAAEmY,UAAU,CAACnY,OAAO,IAAI;EACjC,CAAC;;EAED;EACA,MAAMwY,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;EAEvC;EACA;;EAEA;EACA,MAAMC,SAAS,GAAGF,OAAO,CAACG,WAAW,CAACZ,IAAI,CAAC,CAAC5W,KAAK;;EAEjD;EACA,MAAMA,KAAK,GAAGuX,SAAS,GAAG,CAAC,GAAGT,MAAM,CAACpY,OAAO,GAAG,CAAC,GAAGoY,MAAM,CAACE,eAAe;EACzE,MAAM3R,MAAM,GAAGyR,MAAM,CAAC/X,QAAQ,GAAG,CAAC,GAAG+X,MAAM,CAACpY,OAAO,GAAG,CAAC,GAAGoY,MAAM,CAACE,eAAe;EAEhFE,MAAM,CAAClX,KAAK,GAAGA,KAAK;EACpBkX,MAAM,CAAC7R,MAAM,GAAGA,MAAM;;EAEtB;EACAgS,OAAO,CAACI,IAAI,GAAG,GAAGX,MAAM,CAAC1W,UAAU,IAAI0W,MAAM,CAAC/X,QAAQ,MAAM+X,MAAM,CAACC,QAAQ,EAAE;EAC7EM,OAAO,CAACK,YAAY,GAAG,QAAQ;;EAE/B;EACA,MAAMC,MAAM,GAAG,CAAC;EAChBN,OAAO,CAACO,SAAS,CAAC,CAAC;EACnBP,OAAO,CAACQ,MAAM,CAACf,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,CAAC;EACvEK,OAAO,CAACS,MAAM,CAAC9X,KAAK,GAAG8W,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,CAAC;EAC/EK,OAAO,CAACU,KAAK,CAAC/X,KAAK,GAAG8W,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAEhX,KAAK,GAAG8W,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEA,MAAM,CAAC;EAC9IN,OAAO,CAACS,MAAM,CAAC9X,KAAK,GAAG8W,MAAM,CAACE,eAAe,EAAE3R,MAAM,GAAGyR,MAAM,CAACE,eAAe,GAAGW,MAAM,CAAC;EACxFN,OAAO,CAACU,KAAK,CAAC/X,KAAK,GAAG8W,MAAM,CAACE,eAAe,EAAE3R,MAAM,GAAGyR,MAAM,CAACE,eAAe,EAAEhX,KAAK,GAAG8W,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEtS,MAAM,GAAGyR,MAAM,CAACE,eAAe,EAAEW,MAAM,CAAC;EAChKN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEtS,MAAM,GAAGyR,MAAM,CAACE,eAAe,CAAC;EAChFK,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,EAAE3R,MAAM,GAAGyR,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAE3R,MAAM,GAAGyR,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEA,MAAM,CAAC;EAChJN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,CAAC;EACvEN,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,EAAEW,MAAM,CAAC;EAC9HN,OAAO,CAACW,SAAS,CAAC,CAAC;;EAEnB;EACAX,OAAO,CAACY,WAAW,GAAG,QAAQnB,MAAM,CAACG,WAAW,CAAC9P,CAAC,KAAK2P,MAAM,CAACG,WAAW,CAAC7P,CAAC,KAAK0P,MAAM,CAACG,WAAW,CAAC5P,CAAC,KAAKyP,MAAM,CAACG,WAAW,CAACpc,CAAC,GAAG;EAChIwc,OAAO,CAACa,SAAS,GAAGpB,MAAM,CAACE,eAAe;EAC1CK,OAAO,CAACc,MAAM,CAAC,CAAC;;EAEhB;EACAd,OAAO,CAACe,SAAS,GAAG,QAAQtB,MAAM,CAACnY,eAAe,CAACwI,CAAC,KAAK2P,MAAM,CAACnY,eAAe,CAACyI,CAAC,KAAK0P,MAAM,CAACnY,eAAe,CAAC0I,CAAC,KAAKyP,MAAM,CAACnY,eAAe,CAAC9D,CAAC,GAAG;EAC9Iwc,OAAO,CAACgB,IAAI,CAAC,CAAC;;EAEd;EACAhB,OAAO,CAACe,SAAS,GAAG,QAAQtB,MAAM,CAACxP,SAAS,CAACH,CAAC,KAAK2P,MAAM,CAACxP,SAAS,CAACF,CAAC,KAAK0P,MAAM,CAACxP,SAAS,CAACD,CAAC,KAAKyP,MAAM,CAACxP,SAAS,CAACzM,CAAC,GAAG;EACtHwc,OAAO,CAACiB,SAAS,GAAG,QAAQ;;EAE5B;EACAjB,OAAO,CAACkB,QAAQ,CAAC3B,IAAI,EAAE5W,KAAK,GAAG,CAAC,EAAEqF,MAAM,GAAG,CAAC,CAAC;;EAE7C;EACA,MAAMmT,OAAO,GAAG,IAAIxmB,KAAK,CAACymB,aAAa,CAACvB,MAAM,CAAC;EAC/CsB,OAAO,CAACE,SAAS,GAAG1mB,KAAK,CAAC2mB,YAAY;EACtCH,OAAO,CAACzR,WAAW,GAAG,IAAI;;EAE1B;EACA,MAAM6R,cAAc,GAAG,IAAI5mB,KAAK,CAAC6mB,cAAc,CAAC;IAC9Cje,GAAG,EAAE4d,OAAO;IACZ1R,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAMgS,MAAM,GAAG,IAAI9mB,KAAK,CAAC+mB,MAAM,CAACH,cAAc,CAAC;EAC/CE,MAAM,CAACvT,KAAK,CAAC/N,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EAC7BshB,MAAM,CAAChd,QAAQ,CAACkd,SAAS,GAAG,KAAK,CAAC,CAAC;;EAEnC;EACAF,MAAM,CAAC5F,QAAQ,GAAG;IAChB0D,IAAI,EAAEA,IAAI;IACVE,MAAM,EAAEA;EACV,CAAC;EAED,OAAOgC,MAAM;AACf;;AAIA;AACAvkB,MAAM,CAAC0kB,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMhN,MAAM,GAAGkL,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAInN,MAAM,EAAE;MACV;MACA,MAAMoN,MAAM,GAAGpN,MAAM,CAAC/N,QAAQ,CAACjH,KAAK,CAAC,CAAC;;MAEtC;MACAgV,MAAM,CAAC/N,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9ByU,MAAM,CAAC/K,EAAE,CAAC1J,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtByU,MAAM,CAACnK,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAmK,MAAM,CAAC7J,YAAY,CAAC,CAAC;MACrB6J,MAAM,CAAC5J,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAM9O,QAAQ,GAAG4jB,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAI/lB,QAAQ,EAAE;QACZA,QAAQ,CAACqO,MAAM,CAACpK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BjE,QAAQ,CAACwO,MAAM,CAAC,CAAC;MACnB;MAEA5M,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBmkB,GAAG,EAAEF,MAAM,CAAC/V,OAAO,CAAC,CAAC;QACrBkW,GAAG,EAAEvN,MAAM,CAAC/N,QAAQ,CAACoF,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOpI,CAAC,EAAE;IACV/F,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEyE,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAGD;AACA,MAAM6Q,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACF5W,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAMga,MAAM,GAAG,IAAInd,UAAU,CAAC,CAAC;;IAE/B;IACA,IAAI;MACF,MAAM,CAAEwnB,gBAAgB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,UAAU,CAAC,GAAG,MAAMhM,OAAO,CAACiM,GAAG,CAAC,CAClFzK,MAAM,CAAC0K,SAAS,CAAC,GAAG/kB,QAAQ,4BAA4B,CAAC,EAC3Dqa,MAAM,CAAC0K,SAAS,CAAC,GAAG/kB,QAAQ,uBAAuB,CAAC,EACpDqa,MAAM,CAAC0K,SAAS,CAAC,GAAG/kB,QAAQ,uBAAuB,CAAC,EAClDqa,MAAM,CAAC0K,SAAS,CAAC,GAAG/kB,QAAQ,sBAAsB,CAAC,CAEtD,CAAC;;MAIF;MACAI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB5B,qBAAqB,GAAGkmB,WAAW,CAAC9lB,KAAK;MACzCJ,qBAAqB,CAACgG,QAAQ,CAAEkN,KAAK,IAAK;QACxC,IAAIA,KAAK,CAAC/K,MAAM,EAAE;UACd,MAAMgL,WAAW,GAAG,IAAI3U,KAAK,CAACqc,oBAAoB,CAAC;YACnDlO,KAAK,EAAE,QAAQ;YAAG;YAClBmO,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,GAAG;YACdC,eAAe,EAAE;UACnB,CAAC,CAAC;;UAEA;UACA,IAAI9H,KAAK,CAAC5K,QAAQ,CAAClB,GAAG,EAAE;YACtB+L,WAAW,CAAC/L,GAAG,GAAG8L,KAAK,CAAC5K,QAAQ,CAAClB,GAAG;UACtC;UACA8L,KAAK,CAACqT,OAAO,GAAGpT,WAAW;QAC/B;MACF,CAAC,CAAC;MAEFxR,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B;MACA3B,qBAAqB,GAAGkmB,WAAW,CAAC/lB,KAAK;MACzC;MACAH,qBAAqB,CAAC8R,KAAK,CAAC/N,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxC;MACA/D,qBAAqB,CAAC+F,QAAQ,CAAEkN,KAAK,IAAK;QACxC,IAAIA,KAAK,CAAC/K,MAAM,IAAI+K,KAAK,CAAC5K,QAAQ,EAAE;UAClC;UACA4K,KAAK,CAAC5K,QAAQ,CAACwS,SAAS,GAAG,GAAG;UAC9B5H,KAAK,CAAC5K,QAAQ,CAACyS,SAAS,GAAG,GAAG;UAC9B7H,KAAK,CAAC5K,QAAQ,CAAC0S,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;MAEFrZ,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA1B,oBAAoB,GAAGkmB,UAAU,CAAChmB,KAAK;MACvC;MACA;MACA;MACAF,oBAAoB,CAAC8F,QAAQ,CAAEkN,KAAK,IAAK;QACvC,IAAIA,KAAK,CAAC/K,MAAM,IAAI+K,KAAK,CAAC5K,QAAQ,EAAE;UAClC;UACA4K,KAAK,CAAC5K,QAAQ,CAACwS,SAAS,GAAG,GAAG;UAC9B5H,KAAK,CAAC5K,QAAQ,CAACyS,SAAS,GAAG,GAAG;UAC9B7H,KAAK,CAAC5K,QAAQ,CAAC0S,eAAe,GAAG,GAAG;QAEtC;QACA,IAAI9H,KAAK,CAAC/K,MAAM,EAAC;UACf+K,KAAK,CAACsT,UAAU,GAAG,IAAI;QACzB;MACF,CAAC,CAAC;;MAIF;MACA7kB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEwkB,UAAU,CAACvf,UAAU,CAACC,MAAM,EAAE,GAAG,CAAC;MAC5D,IAAIsf,UAAU,CAACvf,UAAU,IAAIuf,UAAU,CAACvf,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7DnF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEwkB,UAAU,CAACvf,UAAU,CAACC,MAAM,EAAE,GAAG,CAAC;QACzDzG,eAAe,GAAG+lB,UAAU;MAC9B,CAAC,MAAM;QACLzkB,OAAO,CAACogB,IAAI,CAAC,cAAc,CAAC;MAC9B;MAEApgB,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;;MAEzB;MACAzB,0BAA0B,GAAG8lB,gBAAgB,CAAC7lB,KAAK;MACnDuB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEzB,0BAA0B,CAAC;MACjD;MACAA,0BAA0B,CAAC4R,KAAK,CAAC/N,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C;MACA7D,0BAA0B,CAAC6F,QAAQ,CAAEkN,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAAC/K,MAAM,IAAI+K,KAAK,CAAC5K,QAAQ,EAAE;UAClC;UACA4K,KAAK,CAAC5K,QAAQ,CAACwS,SAAS,GAAG,GAAG;UAC9B5H,KAAK,CAAC5K,QAAQ,CAACyS,SAAS,GAAG,GAAG;UAC9B7H,KAAK,CAAC5K,QAAQ,CAAC0S,eAAe,GAAG,GAAG;QACxC;MACF,CAAC,CAAC;MAEArZ,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACA,IAAI;QACF,IAAI,CAACjD,qBAAqB,EAAE;UAC1B,MAAMkmB,WAAW,GAAG,MAAMtK,MAAM,CAAC0K,SAAS,CAAC,GAAG/kB,QAAQ,uBAAuB,CAAC;UAC9EvB,qBAAqB,GAAGkmB,WAAW,CAAC9lB,KAAK;QAC3C;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACF,CAAC,CAAC,OAAOqmB,GAAG,EAAE;QACZ9kB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEwjB,GAAG,CAAC;MAClC;IACF;EACF,CAAC,CAAC,OAAOxjB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;;AAED;AACA,MAAMwU,mBAAmB,GAAItG,IAAI,IAAK;EACpC,MAAMuV,KAAK,GAAG;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,KAAK,CAACvV,IAAI,CAAC,IAAI,MAAM;AAC9B,CAAC;;AAED;AACA,MAAM4F,iBAAiB,GAAGA,CAACrM,QAAQ,EAAE0Y,IAAI,EAAEzW,KAAK,KAAK;EACnD;EACA,IAAI,CAACvM,KAAK,EAAE;IACVuB,OAAO,CAACogB,IAAI,CAAC,oBAAoB,CAAC;IAClC;EACF;EAEA,IAAI;IACJ;IACA,MAAMuD,MAAM,GAAG7R,gBAAgB,CAAC2P,IAAI,CAAC;IACrCkC,MAAM,CAAC5a,QAAQ,CAAC1G,GAAG,CAAC0G,QAAQ,CAAC/G,CAAC,EAAE,EAAE,EAAE,CAAC+G,QAAQ,CAAC7G,CAAC,CAAC,CAAC,CAAE;;IAEnD;IACAoM,UAAU,CAAC,MAAM;MACb;MACA,IAAI7P,KAAK,IAAIklB,MAAM,CAACxd,MAAM,EAAE;QAC9B1H,KAAK,CAAC2H,MAAM,CAACud,MAAM,CAAC;MAClB;IACJ,CAAC,EAAE,GAAG,CAAC;;IAEP;IACAllB,KAAK,CAAC2F,GAAG,CAACuf,MAAM,CAAC;;IAEjB;IACA;IACA;IACA;IACA;EACA,CAAC,CAAC,OAAOriB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;AACF,CAAC;;AAED;AACA,MAAMqb,mBAAmB,GAAIyB,iBAAiB,IAAK;EACjD,IAAI,CAAC3f,KAAK,EAAE;IACVuB,OAAO,CAACsB,KAAK,CAAC,gBAAgB,CAAC;IAC/B;EACF;EAEA,IAAI,CAAC8c,iBAAiB,EAAE;IACtBpe,OAAO,CAACsB,KAAK,CAAC,mBAAmB,CAAC;IAClC;EACF;;EAEA;EACA,IAAI,CAAC9C,0BAA0B,EAAE;IAC/BwB,OAAO,CAACsB,KAAK,CAAC,oBAAoB,CAAC;IACnC;IACA,MAAM2Y,MAAM,GAAG,IAAInd,UAAU,CAAC,CAAC;IAC/Bmd,MAAM,CAAC0K,SAAS,CAAC,GAAG/kB,QAAQ,4BAA4B,CAAC,CACtDolB,IAAI,CAACV,gBAAgB,IAAI;MACxB9lB,0BAA0B,GAAG8lB,gBAAgB,CAAC7lB,KAAK;MACnDD,0BAA0B,CAAC4R,KAAK,CAAC/N,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C7D,0BAA0B,CAAC6F,QAAQ,CAAEkN,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAAC/K,MAAM,IAAI+K,KAAK,CAAC5K,QAAQ,EAAE;UAClC4K,KAAK,CAAC5K,QAAQ,CAACwS,SAAS,GAAG,GAAG;UAC9B5H,KAAK,CAAC5K,QAAQ,CAACyS,SAAS,GAAG,GAAG;UAC9B7H,KAAK,CAAC5K,QAAQ,CAAC0S,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;MACFrZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;MACA0c,mBAAmB,CAACyB,iBAAiB,CAAC;IACxC,CAAC,CAAC,CACD6G,KAAK,CAAC3jB,KAAK,IAAI;MACdtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC;MACA4jB,2BAA2B,CAAC9G,iBAAiB,CAAC;IAChD,CAAC,CAAC;IACJ;EACF;;EAEA;EACA/d,gBAAgB,CAACuE,OAAO,CAAE6Y,QAAQ,IAAK;IACrC,IAAIhf,KAAK,IAAIgf,QAAQ,CAACtZ,KAAK,EAAE;MAC3B1F,KAAK,CAAC2H,MAAM,CAACqX,QAAQ,CAACtZ,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACF9D,gBAAgB,CAAC4F,KAAK,CAAC,CAAC;;EAExB;EACA3I,iBAAiB,CAACmQ,aAAa,CAAC7I,OAAO,CAAC4I,YAAY,IAAI;IACtD,IAAIA,YAAY,CAACa,eAAe,KAAK,KAAK,EAAE;MAC1CrO,OAAO,CAACC,GAAG,CAAC,UAAUuN,YAAY,CAACG,IAAI,kBAAkB,CAAC;MAC1D;IACF;IAEA,IAAIH,YAAY,CAAC/E,QAAQ,IAAI+E,YAAY,CAAChF,SAAS,IAAIgF,YAAY,CAACnD,OAAO,EAAE;MAC3E,MAAM0F,QAAQ,GAAGqO,iBAAiB,CAACvQ,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,EAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC;MAEDzI,OAAO,CAACC,GAAG,CAAC,SAASuN,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,iBAAiB0F,QAAQ,CAAC/N,CAAC,KAAK+N,QAAQ,CAAC7N,CAAC,GAAG,CAAC;MAE7G,IAAI;QACF;QACA,IAAI,CAAC1D,0BAA0B,IAAI,CAACA,0BAA0B,CAACsD,KAAK,EAAE;UACpE,MAAM,IAAIqjB,KAAK,CAAC,cAAc,CAAC;QACjC;;QAEA;QACA,MAAMrR,iBAAiB,GAAGtV,0BAA0B,CAACsD,KAAK,CAAC,CAAC;;QAE5D;QACAgS,iBAAiB,CAACnG,IAAI,GAAG,OAAOH,YAAY,CAACG,IAAI,EAAE;;QAEnD;QACAmG,iBAAiB,CAAC/K,QAAQ,CAAC1G,GAAG,CAAC0N,QAAQ,CAAC/N,CAAC,EAAE,EAAE,EAAE,CAAC+N,QAAQ,CAAC7N,CAAC,CAAC;;QAE3D;QACA4R,iBAAiB,CAAC1D,KAAK,CAAC/N,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;QAEvC;QACAyR,iBAAiB,CAAC1B,WAAW,GAAG,GAAG;;QAEnC;QACA0B,iBAAiB,CAACzP,QAAQ,CAACkN,KAAK,IAAI;UAClC,IAAIA,KAAK,CAAC/K,MAAM,EAAE;YAChB+K,KAAK,CAAC5K,QAAQ,CAACgL,WAAW,GAAG,KAAK;YAClCJ,KAAK,CAAC5K,QAAQ,CAAC0L,OAAO,GAAG,GAAG;YAC5Bd,KAAK,CAAC5K,QAAQ,CAACye,IAAI,GAAGvoB,KAAK,CAACwoB,UAAU;YACtC9T,KAAK,CAAC5K,QAAQ,CAACkX,UAAU,GAAG,IAAI;YAChCtM,KAAK,CAAC5K,QAAQ,CAACkd,SAAS,GAAG,IAAI;YAC/BtS,KAAK,CAAC5K,QAAQ,CAACiL,WAAW,GAAG,IAAI;YACjCL,KAAK,CAACa,WAAW,GAAG,GAAG;UACzB;QACF,CAAC,CAAC;;QAEF;QACA0B,iBAAiB,CAACiK,QAAQ,GAAG;UAC3BvO,IAAI,EAAE,cAAc;UACpBnF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;UAC7BsD,IAAI,EAAEH,YAAY,CAACG;QACrB,CAAC;;QAED;QACAlP,KAAK,CAAC2F,GAAG,CAAC0P,iBAAiB,CAAC;;QAE5B;QACA;QACA,MAAMwR,oBAAoB,GAAG,IAAIzoB,KAAK,CAACwiB,aAAa,CAAC,CAAC;QACtD,MAAMkG,eAAe,GAAG,gCAAgC,CAAC,CAAC;QAC1D,MAAMC,eAAe,GAAG,IAAI3oB,KAAK,CAACqgB,iBAAiB,CAAC;UAClDzX,GAAG,EAAE6f,oBAAoB,CAACzM,IAAI,CAAC0M,eAAe,CAAC;UAE/C5T,WAAW,EAAE,KAAK;UAClBU,OAAO,EAAE;QACX,CAAC,CAAC;QACF;QACA,MAAMoT,eAAe,GAAG,IAAI5oB,KAAK,CAAC+iB,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;QACrD,MAAM8F,WAAW,GAAG,IAAI7oB,KAAK,CAACsgB,IAAI,CAACsI,eAAe,EAAED,eAAe,CAAC;QACpE;QACAE,WAAW,CAAC3c,QAAQ,CAAC1G,GAAG,CAAC0N,QAAQ,CAAC/N,CAAC,EAAE,CAAC,EAAE,CAAC+N,QAAQ,CAAC7N,CAAC,CAAC;QACpD;QACAwjB,WAAW,CAACvV,QAAQ,CAACnO,CAAC,GAAG,CAACkB,IAAI,CAACC,EAAE,GAAG,CAAC;QACrC;QACAuiB,WAAW,CAACtT,WAAW,GAAG,GAAG;QAC7B;QACAsT,WAAW,CAAC3H,QAAQ,GAAG;UACrBvO,IAAI,EAAE,aAAa;UACnBnF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;UAC7BsD,IAAI,EAAEH,YAAY,CAACG;QACrB,CAAC;QACD;QACAlP,KAAK,CAAC2F,GAAG,CAACshB,WAAW,CAAC;QACtB;;QAEA;QACArlB,gBAAgB,CAACgC,GAAG,CAACmL,YAAY,CAACnD,OAAO,EAAE;UACzClG,KAAK,EAAE2P,iBAAiB;UACxBtG,YAAY,EAAEA,YAAY;UAC1BzE,QAAQ,EAAEgH;QACZ,CAAC,CAAC;QAEF/P,OAAO,CAACC,GAAG,CAAC,SAASuN,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkB0F,QAAQ,CAAC/N,CAAC,KAAK,CAAC+N,QAAQ,CAAC7N,CAAC,GAAG,CAAC;MACjH,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,QAAQkM,YAAY,CAACG,IAAI,YAAY,EAAErM,KAAK,CAAC;QAC3D;QACA0b,wBAAwB,CAACxP,YAAY,EAAEuC,QAAQ,EAAEqO,iBAAiB,CAAC;MACrE;IACF;EACF,CAAC,CAAC;;EAEF;EACApe,OAAO,CAACC,GAAG,CAAC,OAAOI,gBAAgB,CAACiW,IAAI,SAAS,CAAC;EAClDjW,gBAAgB,CAACuE,OAAO,CAAC,CAAC6Y,QAAQ,EAAEpT,OAAO,KAAK;IAC9CrK,OAAO,CAACC,GAAG,CAAC,QAAQoK,OAAO,KAAKoT,QAAQ,CAACjQ,YAAY,CAACG,IAAI,EAAE,CAAC;EAC/D,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMuX,2BAA2B,GAAI9G,iBAAiB,IAAK;EACzD9gB,iBAAiB,CAACmQ,aAAa,CAAC7I,OAAO,CAAC4I,YAAY,IAAI;IACtD;IACA,IAAIA,YAAY,CAACa,eAAe,KAAK,KAAK,EAAE;MAC1C;IACF;IAEA,IAAIb,YAAY,CAAC/E,QAAQ,IAAI+E,YAAY,CAAChF,SAAS,IAAIgF,YAAY,CAACnD,OAAO,EAAE;MAC3E;MACA,MAAM0F,QAAQ,GAAGqO,iBAAiB,CAACvQ,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,EAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC;MAEDuU,wBAAwB,CAACxP,YAAY,EAAEuC,QAAQ,EAAEqO,iBAAiB,CAAC;IACrE;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMpB,wBAAwB,GAAGA,CAACxP,YAAY,EAAEuC,QAAQ,EAAEqO,iBAAiB,KAAK;EAC9E;EACA,MAAM3X,QAAQ,GAAG,IAAI5J,KAAK,CAACogB,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAClD,MAAMtW,QAAQ,GAAG,IAAI9J,KAAK,CAACqgB,iBAAiB,CAAC;IAC3ClS,KAAK,EAAE,QAAQ;IACf2G,WAAW,EAAE,KAAK;IAClBU,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAMyB,iBAAiB,GAAG,IAAIjX,KAAK,CAACsgB,IAAI,CAAC1W,QAAQ,EAAEE,QAAQ,CAAC;;EAE5D;EACAmN,iBAAiB,CAACnG,IAAI,GAAG,SAASH,YAAY,CAACG,IAAI,EAAE;;EAErD;EACAmG,iBAAiB,CAAC/K,QAAQ,CAAC1G,GAAG,CAAC0N,QAAQ,CAAC/N,CAAC,EAAE,EAAE,EAAE,CAAC+N,QAAQ,CAAC7N,CAAC,CAAC;;EAE3D;EACA4R,iBAAiB,CAAC1B,WAAW,GAAG,GAAG;;EAEnC;EACA0B,iBAAiB,CAACiK,QAAQ,GAAG;IAC3BvO,IAAI,EAAE,cAAc;IACpBnF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG;EACrB,CAAC;;EAED;EACA,MAAMgY,gBAAgB,GAAG,IAAI9oB,KAAK,CAAC8gB,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5D,MAAMiI,gBAAgB,GAAG,IAAI/oB,KAAK,CAACqgB,iBAAiB,CAAC;IACnDlS,KAAK,EAAE,QAAQ;IACf2G,WAAW,EAAE,IAAI;IACjBU,OAAO,EAAE,GAAG;IAAG;IACfwL,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMgI,QAAQ,GAAG,IAAIhpB,KAAK,CAACsgB,IAAI,CAACwI,gBAAgB,EAAEC,gBAAgB,CAAC;EACnEC,QAAQ,CAAClY,IAAI,GAAG,YAAYH,YAAY,CAACG,IAAI,EAAE;EAC/CkY,QAAQ,CAAC9H,QAAQ,GAAG;IAClBvO,IAAI,EAAE,cAAc;IACpBnF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG,IAAI;IACvBmY,UAAU,EAAE;EACd,CAAC;EAEDhS,iBAAiB,CAAC1P,GAAG,CAACyhB,QAAQ,CAAC;;EAE/B;EACApnB,KAAK,CAAC2F,GAAG,CAAC0P,iBAAiB,CAAC;;EAE5B;EACAzT,gBAAgB,CAACgC,GAAG,CAACmL,YAAY,CAACnD,OAAO,EAAE;IACzClG,KAAK,EAAE2P,iBAAiB;IACxBtG,YAAY,EAAEA,YAAY;IAC1BzE,QAAQ,EAAEgH;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMgW,aAAa,GAAG,IAAIlpB,KAAK,CAAC8gB,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAMqI,aAAa,GAAG,IAAInpB,KAAK,CAACqgB,iBAAiB,CAAC;IAAElS,KAAK,EAAE;EAAS,CAAC,CAAC;EACtE,MAAMib,SAAS,GAAG,IAAIppB,KAAK,CAACsgB,IAAI,CAAC4I,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAACld,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;EAEhC;EACA4jB,SAAS,CAAClI,QAAQ,GAAG;IACnBvO,IAAI,EAAE,cAAc;IACpBnF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG;EACrB,CAAC;EAEDmG,iBAAiB,CAAC1P,GAAG,CAAC6hB,SAAS,CAAC;EAEhCjmB,OAAO,CAACC,GAAG,CAAC,SAASuN,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkB0F,QAAQ,CAAC/N,CAAC,KAAK,CAAC+N,QAAQ,CAAC7N,CAAC,GAAG,CAAC;AACjH,CAAC;;AAED;AACA,MAAMmR,iBAAiB,GAAIL,OAAO,IAAK;EACrC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMI,oBAAoB,GAAI8S,OAAO,IAAK;EACxC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMpJ,gBAAgB,GAAGA,CAACrI,KAAK,EAAE0R,SAAS,EAAEC,aAAa,EAAEC,cAAc,KAAK;EAC5E,IAAI,CAACF,SAAS,IAAI,CAACC,aAAa,IAAI,CAACC,cAAc,EAAE;EAErDrmB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEwU,KAAK,CAAC6R,OAAO,EAAE7R,KAAK,CAAC8R,OAAO,CAAC;;EAEvD;EACA,MAAMC,IAAI,GAAGL,SAAS,CAACM,qBAAqB,CAAC,CAAC;EAC9C,MAAMC,MAAM,GAAI,CAACjS,KAAK,CAAC6R,OAAO,GAAGE,IAAI,CAACvd,IAAI,IAAIkd,SAAS,CAACQ,WAAW,GAAI,CAAC,GAAG,CAAC;EAC5E,MAAMC,MAAM,GAAG,EAAE,CAACnS,KAAK,CAAC8R,OAAO,GAAGC,IAAI,CAAC5b,GAAG,IAAIub,SAAS,CAACU,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;;EAE7E;EACA,MAAMC,SAAS,GAAG,IAAIjqB,KAAK,CAACkqB,SAAS,CAAC,CAAC;EACvC;EACAD,SAAS,CAACnF,MAAM,CAACqF,MAAM,CAACC,SAAS,GAAG,CAAC;EACrCH,SAAS,CAACnF,MAAM,CAACuF,IAAI,CAACD,SAAS,GAAG,CAAC;EAEnC,MAAME,WAAW,GAAG,IAAItqB,KAAK,CAACuqB,OAAO,CAACV,MAAM,EAAEE,MAAM,CAAC;EACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,EAAEd,cAAc,CAAC;EAEpDrmB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEymB,MAAM,EAAEE,MAAM,CAAC;;EAEtC;EACA,MAAMU,mBAAmB,GAAG,EAAE;EAE9BjnB,gBAAgB,CAACuE,OAAO,CAAC,CAAC6Y,QAAQ,EAAEpT,OAAO,KAAK;IAC9C,IAAIoT,QAAQ,CAACtZ,KAAK,EAAE;MAClB;MACAmjB,mBAAmB,CAAC3T,IAAI,CAAC8J,QAAQ,CAACtZ,KAAK,CAAC;MACxC;MACAsZ,QAAQ,CAACtZ,KAAK,CAACiG,OAAO,GAAG,IAAI;MAC7BqT,QAAQ,CAACtZ,KAAK,CAACiO,WAAW,GAAG,IAAI,CAAC,CAAC;MACnC;MACAqL,QAAQ,CAACtZ,KAAK,CAACE,QAAQ,CAACkN,KAAK,IAAI;QAC/BA,KAAK,CAACnH,OAAO,GAAG,IAAI;QACpBmH,KAAK,CAACa,WAAW,GAAG,IAAI;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEFpS,OAAO,CAACC,GAAG,CAAC,QAAQqnB,mBAAmB,CAACniB,MAAM,gBAAgB,CAAC;;EAE/D;EACA,MAAMoiB,sBAAsB,GAAGT,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,EAAE,IAAI,CAAC;EAEpF,IAAIC,sBAAsB,CAACpiB,MAAM,GAAG,CAAC,EAAE;IACrCnF,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEsnB,sBAAsB,CAACpiB,MAAM,CAAC;IACxDoiB,sBAAsB,CAAC3iB,OAAO,CAAC,CAAC6iB,SAAS,EAAEC,KAAK,KAAK;MACnD1nB,OAAO,CAACC,GAAG,CAAC,QAAQynB,KAAK,GAAG,EACjBD,SAAS,CAACnjB,MAAM,CAACqJ,IAAI,IAAI,KAAK,EAC9B,KAAK,EAAE8Z,SAAS,CAAChlB,QAAQ,EACzB,WAAW,EAAEglB,SAAS,CAACnjB,MAAM,CAACyZ,QAAQ,CAAC;IACpD,CAAC,CAAC;;IAEF;IACA,MAAM/B,GAAG,GAAG2L,yBAAyB,CAACJ,sBAAsB,CAAC,CAAC,CAAC,CAACjjB,MAAM,CAAC;IAEvE,IAAI0X,GAAG,IAAIA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAACvO,IAAI,KAAK,cAAc,EAAE;MAC/D;MACA,MAAMnF,OAAO,GAAG2R,GAAG,CAAC+B,QAAQ,CAAC1T,OAAO;MACpCrK,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoK,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;;MAEhE;MACA,IAAIud,SAAS,GAAGvd,OAAO;MACvB,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,CAAChK,gBAAgB,CAACiC,GAAG,CAAC+H,OAAO,CAAC,IAAIhK,gBAAgB,CAACiC,GAAG,CAACmR,QAAQ,CAACpJ,OAAO,CAAC,CAAC,EAAE;QAC5Gud,SAAS,GAAGnU,QAAQ,CAACpJ,OAAO,CAAC;QAC7BrK,OAAO,CAACC,GAAG,CAAC,cAAc2nB,SAAS,EAAE,CAAC;MACxC,CAAC,MAAM,IAAI,OAAOvd,OAAO,KAAK,QAAQ,IAAI,CAAChK,gBAAgB,CAACiC,GAAG,CAAC+H,OAAO,CAAC,IAAIhK,gBAAgB,CAACiC,GAAG,CAACuR,MAAM,CAACxJ,OAAO,CAAC,CAAC,EAAE;QACjHud,SAAS,GAAG/T,MAAM,CAACxJ,OAAO,CAAC;QAC3BrK,OAAO,CAACC,GAAG,CAAC,eAAe2nB,SAAS,EAAE,CAAC;MACzC;;MAEA;MACAxoB,MAAM,CAACmP,qBAAqB,CAACqZ,SAAS,CAAC;MACvC;IACF;EACF;;EAEA;EACA,MAAMC,UAAU,GAAGf,SAAS,CAACU,gBAAgB,CAACpB,aAAa,CAAC9M,QAAQ,EAAE,IAAI,CAAC;EAE3EtZ,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE4nB,UAAU,CAAC1iB,MAAM,CAAC;EAE7C,IAAI0iB,UAAU,CAAC1iB,MAAM,GAAG,CAAC,EAAE;IACzB;IACA0iB,UAAU,CAACjjB,OAAO,CAAC,CAAC6iB,SAAS,EAAEC,KAAK,KAAK;MACvC,MAAM1L,GAAG,GAAGyL,SAAS,CAACnjB,MAAM;MAC5BtE,OAAO,CAACC,GAAG,CAAC,UAAUynB,KAAK,GAAG,EAAE1L,GAAG,CAACrO,IAAI,IAAI,KAAK,EACrC,WAAW,EAAEqO,GAAG,CAAC+B,QAAQ,EACzB,KAAK,EAAE0J,SAAS,CAAChlB,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACA,KAAK,IAAIiL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGma,UAAU,CAAC1iB,MAAM,EAAEuI,CAAC,EAAE,EAAE;MAC1C,MAAMsO,GAAG,GAAG2L,yBAAyB,CAACE,UAAU,CAACna,CAAC,CAAC,CAACpJ,MAAM,CAAC;MAC3D,IAAI0X,GAAG,IAAIA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAACvO,IAAI,KAAK,cAAc,EAAE;QAC/D,MAAMnF,OAAO,GAAG2R,GAAG,CAAC+B,QAAQ,CAAC1T,OAAO;QACpCjL,MAAM,CAACmP,qBAAqB,CAAClE,OAAO,CAAC;QACrC;MACF;IACF;EACF;;EAEA;EACArK,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;;EAE9B;EACA,IAAI6nB,YAAY,GAAG,IAAI;EACvB,IAAIjb,WAAW,GAAG,GAAG,CAAC,CAAC;;EAEvBxM,gBAAgB,CAACuE,OAAO,CAAC,CAAC6Y,QAAQ,EAAEpT,OAAO,KAAK;IAC9C,IAAIoT,QAAQ,CAACtZ,KAAK,EAAE;MAClB,MAAM4jB,QAAQ,GAAG,IAAIlrB,KAAK,CAACiG,OAAO,CAAC,CAAC;MACpC;MACA2a,QAAQ,CAACtZ,KAAK,CAAC6jB,gBAAgB,CAACD,QAAQ,CAAC;;MAEzC;MACA,MAAME,SAAS,GAAGF,QAAQ,CAACjmB,KAAK,CAAC,CAAC;MAClCmmB,SAAS,CAACC,OAAO,CAAC7B,cAAc,CAAC;;MAEjC;MACA,MAAM8B,EAAE,GAAGF,SAAS,CAACjmB,CAAC,GAAG0kB,MAAM;MAC/B,MAAM0B,EAAE,GAAGH,SAAS,CAAC/lB,CAAC,GAAG0kB,MAAM;MAC/B,MAAMnkB,QAAQ,GAAGS,IAAI,CAACmlB,IAAI,CAACF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;MAE7CpoB,OAAO,CAACC,GAAG,CAAC,MAAMoK,OAAO,OAAO,EAAE5H,QAAQ,CAAC;MAE3C,IAAIA,QAAQ,GAAGoK,WAAW,EAAE;QAC1BA,WAAW,GAAGpK,QAAQ;QACtBqlB,YAAY,GAAG;UAAEzd,OAAO;UAAE5H;QAAS,CAAC;MACtC;IACF;EACF,CAAC,CAAC;EAEF,IAAIqlB,YAAY,EAAE;IAChB9nB,OAAO,CAACC,GAAG,CAAC,oBAAoB6nB,YAAY,CAACzd,OAAO,SAASyd,YAAY,CAACrlB,QAAQ,EAAE,CAAC;;IAErF;IACArD,MAAM,CAACmP,qBAAqB,CAACuZ,YAAY,CAACzd,OAAO,CAAC;IAClD;EACF;EAEArK,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;AAC5B,CAAC;;AAED;AACA,MAAMshB,kBAAkB,GAAI+G,eAAe,IAAK;EAC9C;EACA,IAAIlpB,MAAM,CAACqL,0BAA0B,IAAIrL,MAAM,CAACqL,0BAA0B,CAACiB,OAAO,EAAE;IAClF+Q,aAAa,CAACrd,MAAM,CAACqL,0BAA0B,CAACiB,OAAO,CAAC;IACxDtM,MAAM,CAACqL,0BAA0B,CAACiB,OAAO,GAAG,IAAI;IAChD1L,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B;;EAEA;EACA,IAAIb,MAAM,CAACoL,mBAAmB,EAAE;IAC9BpL,MAAM,CAACoL,mBAAmB,CAACkB,OAAO,GAAG,IAAI;EAC3C;;EAEA;EACA4c,eAAe,CAACtU,IAAI,KAAK;IACvB,GAAGA,IAAI;IACP5J,OAAO,EAAE,KAAK;IACdE,OAAO,EAAE,IAAI;IAAE;IACfC,MAAM,EAAE,EAAE,CAAK;EACjB,CAAC,CAAC,CAAC;EAEHvK,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;AAChC,CAAC;;AAED;AACAb,MAAM,CAACmpB,qBAAqB,GAAIle,OAAO,IAAK;EAC1C,IAAI;IAAA,IAAAme,qBAAA,EAAAC,sBAAA;IACF;IACA,MAAMlV,YAAY,GAAGlT,gBAAgB,CAACmC,GAAG,CAAC6H,OAAO,IAAI,GAAG,CAAC;IACzD,IAAI,CAACkJ,YAAY,EAAE;MACjBvT,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAE+I,OAAO,CAAC;;MAEtC;MACArK,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxBI,gBAAgB,CAACuE,OAAO,CAAC,CAAC8jB,KAAK,EAAEpZ,EAAE,KAAK;QACtCtP,OAAO,CAACC,GAAG,CAAC,KAAKqP,EAAE,KAAKoZ,KAAK,CAAClb,YAAY,CAACG,IAAI,EAAE,CAAC;MACpD,CAAC,CAAC;MAEF,OAAO,KAAK;IACd;;IAEA;IACA,MAAMgb,UAAU,GAAGpV,YAAY,CAACpP,KAAK;;IAErC;IACA,MAAMykB,SAAS,GAAGtoB,kBAAkB,CAACkC,GAAG,CAAC6H,OAAO,CAAC;IACjD,MAAMmD,YAAY,GAAG+F,YAAY,CAAC/F,YAAY;;IAE9C;IACA,IAAIlD,OAAO;IAEX,IAAIse,SAAS,IAAIA,SAAS,CAACre,MAAM,EAAE;MACjCD,OAAO,gBACL7M,OAAA;QAAK6iB,KAAK,EAAE;UAAE/W,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAEoW,SAAS,EAAE,OAAO;UAAE4H,SAAS,EAAE;QAAO,CAAE;QAAAvP,QAAA,gBACpF7b,OAAA;UAAK6iB,KAAK,EAAE;YACVrV,UAAU,EAAE,MAAM;YAClB6d,YAAY,EAAE,KAAK;YACnBlf,QAAQ,EAAE,MAAM;YAChBmf,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAA1P,QAAA,GACC9L,YAAY,CAACG,IAAI,EAAC,QAAM,EAACtD,OAAO,EAAC,GACpC;QAAA;UAAAkW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNjjB,OAAA;UAAA6b,QAAA,EACGsP,SAAS,CAACre,MAAM,CAAC9E,GAAG,CAAC,CAACsN,KAAK,EAAE2U,KAAK,KAAK;YACtC,IAAIuB,UAAU;YACd,QAAQlW,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBAAE0V,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;gBAAEA,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;cAAE;gBAASA,UAAU,GAAG,SAAS;gBAAE;YAC7C;YAEA,oBACExrB,OAAA;cAAiB6iB,KAAK,EAAE;gBACtBwI,YAAY,EAAE,KAAK;gBACnBtf,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE,KAAK;gBACnBE,QAAQ,EAAE;cACZ,CAAE;cAAA0P,QAAA,gBACA7b,OAAA;gBAAK6iB,KAAK,EAAE;kBAAErV,UAAU,EAAE;gBAAO,CAAE;gBAAAqO,QAAA,EAChCjG,iBAAiB,CAACN,KAAK,CAACC,OAAO;cAAC;gBAAAuN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNjjB,OAAA;gBAAK6iB,KAAK,EAAE;kBAAElX,OAAO,EAAE,MAAM;kBAAE8f,cAAc,EAAE;gBAAgB,CAAE;gBAAA5P,QAAA,gBAC/D7b,OAAA;kBAAA6b,QAAA,EAAM;gBAAI;kBAAAiH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBjjB,OAAA;kBAAM6iB,KAAK,EAAE;oBACXtV,KAAK,EAAEie,UAAU;oBACjBhe,UAAU,EAAE,MAAM;oBAClBzB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAA4P,QAAA,EACCvG,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGR,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAG;gBAAI;kBAAAgN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNjjB,OAAA;gBAAK6iB,KAAK,EAAE;kBAAElX,OAAO,EAAE,MAAM;kBAAE8f,cAAc,EAAE;gBAAgB,CAAE;gBAAA5P,QAAA,gBAC/D7b,OAAA;kBAAA6b,QAAA,EAAM;gBAAK;kBAAAiH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBjjB,OAAA;kBAAM6iB,KAAK,EAAE;oBAAErV,UAAU,EAAE;kBAAO,CAAE;kBAAAqO,QAAA,GAAEvG,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAA+M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAzBEgH,KAAK;cAAAnH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNjjB,OAAA;UAAK6iB,KAAK,EAAE;YAAE6I,SAAS,EAAE,KAAK;YAAEvf,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE;UAAO,CAAE;UAAAsO,QAAA,GAAC,4BAC3D,EAAC,IAAIpR,IAAI,CAAC,CAAC,CAACkhB,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAA7I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACLpW,OAAO,gBACL7M,OAAA;QAAK6iB,KAAK,EAAE;UAAE/W,OAAO,EAAE,KAAK;UAAE4X,QAAQ,EAAE;QAAQ,CAAE;QAAA7H,QAAA,gBAChD7b,OAAA;UAAK6iB,KAAK,EAAE;YAAErV,UAAU,EAAE,MAAM;YAAE6d,YAAY,EAAE;UAAM,CAAE;UAAAxP,QAAA,EAAE9L,YAAY,CAACG;QAAI;UAAA4S,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClFjjB,OAAA;UAAA6b,QAAA,GAAK,kBAAM,EAACjP,OAAO;QAAA;UAAAkW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1BjjB,OAAA;UAAA6b,QAAA,EAAK;QAAU;UAAAiH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN;IACH;;IAEA;IACA,MAAM2I,OAAO,GAAGjqB,MAAM,CAAC4X,UAAU,GAAG,CAAC,GAAE,GAAG;IAC1C,MAAMsS,OAAO,GAAGlqB,MAAM,CAAC6X,WAAW,GAAG,CAAC,GAAE,GAAG;;IAE3C;IACA,MAAMqR,eAAe,IAAAE,qBAAA,GAAGxG,QAAQ,CAAC+B,aAAa,CAAC,OAAO,CAAC,cAAAyE,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCe,gBAAgB,cAAAd,sBAAA,uBAAjDA,sBAAA,CAAmDte,sBAAsB;IAEjG,IAAIme,eAAe,EAAE;MACnB;MACAA,eAAe,CAAC;QACdle,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBtB,QAAQ,EAAE;UAAE/G,CAAC,EAAEqnB,OAAO;UAAEnnB,CAAC,EAAEonB;QAAQ,CAAC;QACpChf,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAAqe,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEre,MAAM,KAAI;MAC/B,CAAC,CAAC;MAEFvK,OAAO,CAACC,GAAG,CAAC,SAASuN,YAAY,CAACG,IAAI,KAAKtD,OAAO,UAAU,CAAC;MAC7D,OAAO,IAAI;IACb,CAAC,MAAM;MACL;MACA,MAAMmf,OAAO,GAAGxH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CuH,OAAO,CAAClJ,KAAK,CAACvX,QAAQ,GAAG,UAAU;MACnCygB,OAAO,CAAClJ,KAAK,CAACrX,IAAI,GAAG,GAAGogB,OAAO,IAAI;MACnCG,OAAO,CAAClJ,KAAK,CAAC1V,GAAG,GAAG,GAAG0e,OAAO,IAAI;MAClCE,OAAO,CAAClJ,KAAK,CAACpX,SAAS,GAAG,wBAAwB;MAClDsgB,OAAO,CAAClJ,KAAK,CAACnX,MAAM,GAAG,MAAM;MAC7BqgB,OAAO,CAAClJ,KAAK,CAAC9W,eAAe,GAAG,qBAAqB;MACrDggB,OAAO,CAAClJ,KAAK,CAACtV,KAAK,GAAG,OAAO;MAC7Bwe,OAAO,CAAClJ,KAAK,CAAC5W,YAAY,GAAG,KAAK;MAClC8f,OAAO,CAAClJ,KAAK,CAACzW,SAAS,GAAG,8BAA8B;MACxD2f,OAAO,CAAClJ,KAAK,CAAC/W,OAAO,GAAG,KAAK;MAC7BigB,OAAO,CAAClJ,KAAK,CAACa,QAAQ,GAAG,OAAO;MAChCqI,OAAO,CAAClJ,KAAK,CAAC1W,QAAQ,GAAG,MAAM;MAE/B4f,OAAO,CAACC,SAAS,GAAG;AAC1B;AACA,YAAYjc,YAAY,CAACG,IAAI,SAAStD,OAAO;AAC7C;AACA;AACA,qBAAqBA,OAAO;AAC5B,eAAeue,SAAS,GAAG,UAAU,GAAG,YAAY;AACpD;AACA;AACA,OAAO;MAED5G,QAAQ,CAAC0H,IAAI,CAACjS,WAAW,CAAC+R,OAAO,CAAC;;MAElC;MACA,MAAMG,WAAW,GAAGH,OAAO,CAACzF,aAAa,CAAC,QAAQ,CAAC;MACnD,IAAI4F,WAAW,EAAE;QACfA,WAAW,CAAChO,gBAAgB,CAAC,OAAO,EAAE,MAAM;UAC1CqG,QAAQ,CAAC0H,IAAI,CAACvN,WAAW,CAACqN,OAAO,CAAC;QACpC,CAAC,CAAC;MACJ;MAEAxpB,OAAO,CAACC,GAAG,CAAC,gBAAgBuN,YAAY,CAACG,IAAI,KAAKtD,OAAO,OAAO,CAAC;MACjE,OAAO,IAAI;IACb;EACF,CAAC,CAAC,OAAO/I,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACAlC,MAAM,CAACwqB,iBAAiB,GAAG,MAAM;EAC/B5pB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;EAErB,IAAI,CAACI,gBAAgB,IAAIA,gBAAgB,CAACiW,IAAI,KAAK,CAAC,EAAE;IACpDtW,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,OAAO,EAAE;EACX;EAEA,MAAM4pB,IAAI,GAAG,EAAE;EACfxpB,gBAAgB,CAACuE,OAAO,CAAC,CAAC8jB,KAAK,EAAEpZ,EAAE,KAAK;IACtCtP,OAAO,CAACC,GAAG,CAAC,SAASqP,EAAE,SAASoZ,KAAK,CAAClb,YAAY,CAACG,IAAI,EAAE,CAAC;IAC1Dkc,IAAI,CAAClW,IAAI,CAAC;MACRrE,EAAE;MACF3B,IAAI,EAAE+a,KAAK,CAAClb,YAAY,CAACG,IAAI;MAC7B5E,QAAQ,EAAE2f,KAAK,CAAC3f;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAO8gB,IAAI;AACb,CAAC;;AAGD;AACAzqB,MAAM,CAACmP,qBAAqB,GAAIlE,OAAO,IAAK;EAC1C,IAAI;IACF;IACAA,OAAO,GAAGwJ,MAAM,CAACxJ,OAAO,CAAC;IAEzBrK,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEoK,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;IAC/ErK,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,gBAAgB,CAACiW,IAAI,CAAC;;IAE3D;IACA,IAAI/C,YAAY,GAAGlT,gBAAgB,CAACmC,GAAG,CAAC6H,OAAO,CAAC;IAChD,IAAI,CAACkJ,YAAY,EAAE;MACjB;MACA,MAAMuW,SAAS,GAAGrW,QAAQ,CAACpJ,OAAO,CAAC;MACnCkJ,YAAY,GAAGlT,gBAAgB,CAACmC,GAAG,CAACsnB,SAAS,CAAC;MAE9C,IAAIvW,YAAY,EAAE;QAChBvT,OAAO,CAACC,GAAG,CAAC,UAAU6pB,SAAS,SAAS,CAAC;QACzCzf,OAAO,GAAGyf,SAAS,CAAC,CAAC;MACvB;IACF;IAEA,IAAI,CAACvW,YAAY,EAAE;MACjBvT,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAE+I,OAAO,CAAC;MACtC,OAAO,KAAK;IACd;IAEA,MAAMue,SAAS,GAAGtoB,kBAAkB,CAACkC,GAAG,CAAC6H,OAAO,CAAC;IACjD,MAAMmD,YAAY,GAAG+F,YAAY,CAAC/F,YAAY;;IAE9C;IACA,MAAMuc,YAAY,GAAGnB,SAAS,IAAIA,SAAS,CAACre,MAAM,IAAIqe,SAAS,CAACre,MAAM,CAACpF,MAAM,GAAG,CAAC;IAEjF,IAAImF,OAAO;;IAEX;IACA,MAAM0f,YAAY,GAAG;MACnBjhB,QAAQ,EAAE,UAAU;MACpB6B,GAAG,EAAE,KAAK;MACVwW,KAAK,EAAE,MAAM;MACbvW,KAAK,EAAE,MAAM;MACbqF,MAAM,EAAE,MAAM;MACd9G,OAAO,EAAE,MAAM;MACf8f,cAAc,EAAE,QAAQ;MACxBe,UAAU,EAAE,QAAQ;MACpBvgB,YAAY,EAAE,KAAK;MACnB2X,UAAU,EAAE,iBAAiB;MAC7BlY,MAAM,EAAE;IACV,CAAC;;IAED;IACA,MAAM+gB,WAAW,GAAGA,CAAA,kBAClBzsB,OAAA;MAAK6iB,KAAK,EAAE0J,YAAa;MAAA1Q,QAAA,eACvB7b,OAAA;QAAK6iB,KAAK,EAAE;UACVlX,OAAO,EAAE,MAAM;UACf+gB,aAAa,EAAE,QAAQ;UACvBF,UAAU,EAAE,QAAQ;UACpB/gB,SAAS,EAAE;QACb,CAAE;QAAAoQ,QAAA,gBACA7b,OAAA;UAAM6iB,KAAK,EAAE;YACXtV,KAAK,EAAE,SAAS;YAChBpB,QAAQ,EAAE,MAAM;YAChBqB,UAAU,EAAE,MAAM;YAClBF,UAAU,EAAE;UACd,CAAE;UAAAuO,QAAA,EAAC;QAAC;UAAAiH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACXjjB,OAAA;UAAM6iB,KAAK,EAAE;YACXzV,KAAK,EAAE,CAAC;YACRqF,MAAM,EAAE,CAAC;YACTka,UAAU,EAAE,uBAAuB;YACnCC,WAAW,EAAE,uBAAuB;YACpCtB,YAAY,EAAE,oBAAoB;YAClCI,SAAS,EAAE;UACb;QAAE;UAAA5I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;IAED,IAAIqJ,YAAY,EAAE;MAChB;MACA,MAAMO,QAAQ,GAAG;QACf,GAAG,EAAE;UAAEC,GAAG,EAAE,GAAG;UAAE/a,IAAI,EAAE;QAAO,CAAC;QAAE,GAAG,EAAE;UAAE+a,GAAG,EAAE,GAAG;UAAE/a,IAAI,EAAE;QAAW,CAAC;QAAE,GAAG,EAAE;UAAE+a,GAAG,EAAE,GAAG;UAAE/a,IAAI,EAAE;QAAQ,CAAC;QACtG,GAAG,EAAE;UAAE+a,GAAG,EAAE,GAAG;UAAE/a,IAAI,EAAE;QAAO,CAAC;QAAE,GAAG,EAAE;UAAE+a,GAAG,EAAE,GAAG;UAAE/a,IAAI,EAAE;QAAW,CAAC;QAAE,GAAG,EAAE;UAAE+a,GAAG,EAAE,GAAG;UAAE/a,IAAI,EAAE;QAAQ,CAAC;QACtG,GAAG,EAAE;UAAE+a,GAAG,EAAE,GAAG;UAAE/a,IAAI,EAAE;QAAO,CAAC;QAAE,IAAI,EAAE;UAAE+a,GAAG,EAAE,GAAG;UAAE/a,IAAI,EAAE;QAAW,CAAC;QAAE,IAAI,EAAE;UAAE+a,GAAG,EAAE,GAAG;UAAE/a,IAAI,EAAE;QAAQ,CAAC;QACxG,IAAI,EAAE;UAAE+a,GAAG,EAAE,GAAG;UAAE/a,IAAI,EAAE;QAAO,CAAC;QAAE,IAAI,EAAE;UAAE+a,GAAG,EAAE,GAAG;UAAE/a,IAAI,EAAE;QAAW,CAAC;QAAE,IAAI,EAAE;UAAE+a,GAAG,EAAE,GAAG;UAAE/a,IAAI,EAAE;QAAQ;MAC1G,CAAC;MAED,MAAMgb,SAAS,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC;MAC/C,MAAMC,QAAQ,GAAG;QAAEC,CAAC,EAAE,SAAS;QAAEC,CAAC,EAAE,SAAS;QAAEC,CAAC,EAAE;MAAU,CAAC;MAC7D,MAAMC,OAAO,GAAG;QAAEC,CAAC,EAAE,CAAC,CAAC;QAAEC,CAAC,EAAE,CAAC,CAAC;QAAEC,CAAC,EAAE,CAAC,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAE,CAAC;MAE9CrC,SAAS,CAACre,MAAM,CAAC3F,OAAO,CAACmO,KAAK,IAAI;QAChC,MAAMtN,GAAG,GAAG6kB,QAAQ,CAACvX,KAAK,CAACC,OAAO,CAAC;QACnC,IAAIvN,GAAG,EAAE;UACPolB,OAAO,CAACplB,GAAG,CAAC8kB,GAAG,CAAC,CAAC9kB,GAAG,CAAC+J,IAAI,CAAC,GAAG;YAC3BxE,KAAK,EAAEyf,QAAQ,CAAC1X,KAAK,CAACQ,YAAY,CAAC,IAAI,MAAM;YAC7CC,UAAU,EAAET,KAAK,CAACS;UACpB,CAAC;QACH;MACF,CAAC,CAAC;MAEFlJ,OAAO,gBACL7M,OAAA;QAAK6iB,KAAK,EAAE;UAAE/W,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAEwW,UAAU,EAAE,kBAAkB;UAAEtY,QAAQ,EAAE;QAAW,CAAE;QAAAuQ,QAAA,gBACnG7b,OAAA,CAACysB,WAAW;UAAA3J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfjjB,OAAA;UAAK6iB,KAAK,EAAE;YAAErV,UAAU,EAAE,MAAM;YAAE6d,YAAY,EAAE,KAAK;YAAElf,QAAQ,EAAE,MAAM;YAAEuZ,SAAS,EAAE;UAAS,CAAE;UAAA7J,QAAA,GAAE9L,YAAY,CAACG,IAAI,EAAC,cAAE;QAAA;UAAA4S,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3HjjB,OAAA;UAAK6iB,KAAK,EAAE;YACVlX,OAAO,EAAE,MAAM;YACf8hB,gBAAgB,EAAE,gBAAgB;YAClCC,mBAAmB,EAAE,gBAAgB;YACrCjC,cAAc,EAAE,QAAQ;YACxBe,UAAU,EAAE,QAAQ;YACpB5I,UAAU,EAAE,wBAAwB;YACpC3X,YAAY,EAAE,KAAK;YACnB0hB,MAAM,EAAE,QAAQ;YAChBriB,QAAQ,EAAE;UACZ,CAAE;UAAAuQ,QAAA,gBAGA7b,OAAA;YAAK6iB,KAAK,EAAE;cAAE+K,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEnI,SAAS,EAAE,QAAQ;cAAE/Z,OAAO,EAAE,MAAM;cAAE8f,cAAc,EAAE,QAAQ;cAAEre,KAAK,EAAE;YAAO,CAAE;YAAAyO,QAAA,EACtHkR,SAAS,CAAC/kB,GAAG,CAAC,CAAC+J,IAAI,EAAEkY,KAAK,KAAK;cAC9B;cACA,IAAI6D,YAAY,GAAG7D,KAAK;cACxB,IAAIA,KAAK,KAAK,CAAC,EAAE6D,YAAY,GAAG,CAAC,CAAC,KAC7B,IAAI7D,KAAK,KAAK,CAAC,EAAE6D,YAAY,GAAG,CAAC;cAEtC,MAAMC,WAAW,GAAGhB,SAAS,CAACe,YAAY,CAAC;;cAE3C;cACA,MAAME,WAAW,GAAG,CAAC,CAAC;cACtB,IAAID,WAAW,KAAK,MAAM,EAAE;gBAAE;gBAC5BC,WAAW,CAACC,WAAW,GAAG,KAAK;cACjC,CAAC,MAAM,IAAIF,WAAW,KAAK,UAAU,EAAE;gBAAE;gBACvCC,WAAW,CAACE,UAAU,GAAG,MAAM;gBAC/BF,WAAW,CAACC,WAAW,GAAG,MAAM;cAClC,CAAC,MAAM,IAAIF,WAAW,KAAK,OAAO,EAAE;gBAAE;gBACpCC,WAAW,CAACE,UAAU,GAAG,KAAK;cAChC;cAEA,OAAOd,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC;cAAA;cAC3B;cACA;cACA;cACA;cACA;cACA;cACA/tB,OAAA;gBAAuB6iB,KAAK,EAAE;kBAACoL,WAAW,EAAEF,WAAW,KAAK,MAAM,GAAG,CAAC,GAAG,MAAM;kBAAEpiB,OAAO,EAAE,MAAM;kBAAE+gB,aAAa,EAAE,QAAQ;kBAAEF,UAAU,EAAE;gBAAQ,CAAE;gBAAA3Q,QAAA,gBAC/I7b,OAAA;kBAAK6iB,KAAK,EAAE;oBAAC1W,QAAQ,EAAC,MAAM;oBAAEoB,KAAK,EAAE6f,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAACxgB,KAAK;oBAAEC,UAAU,EAAC,MAAM;oBAAE6d,YAAY,EAAE;kBAAK,CAAE;kBAAAxP,QAAA,EAAEuR,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAAChY;gBAAU;kBAAA+M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrJjjB,OAAA;kBAAM6iB,KAAK,EAAE;oBAACtV,KAAK,EAAE6f,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAACxgB,KAAK;oBAAEpB,QAAQ,EAAC,MAAM;oBAAEmB,UAAU,EAAE;kBAAM,CAAE;kBAAAuO,QAAA,EACrFkS,WAAW,KAAK,MAAM,GAAG,WAAW,GAAGA,WAAW,KAAK,UAAU,GAAG,WAAW,GAAG;gBAAW;kBAAAjL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA,GAJC8K,WAAW;gBAAAjL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKhB,CACN;YACH,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNjjB,OAAA;YAAK6iB,KAAK,EAAE;cAAE+K,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEnI,SAAS,EAAE,QAAQ;cAAE/Z,OAAO,EAAE,MAAM;cAAE8f,cAAc,EAAE,QAAQ;cAAEre,KAAK,EAAE;YAAO,CAAE;YAAAyO,QAAA,EACtHkR,SAAS,CAAC/kB,GAAG,CAAC+J,IAAI,IAAIqb,OAAO,CAACG,CAAC,CAACxb,IAAI,CAAC,iBACpC/R,OAAA;cAAgB6iB,KAAK,EAAE;gBAACoL,WAAW,EAAElc,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,MAAM;gBAAEpG,OAAO,EAAE,MAAM;gBAAE+gB,aAAa,EAAE,QAAQ;gBAAEF,UAAU,EAAE;cAAQ,CAAE;cAAA3Q,QAAA,gBAClI7b,OAAA;gBAAM6iB,KAAK,EAAE;kBAACtV,KAAK,EAAE6f,OAAO,CAACG,CAAC,CAACxb,IAAI,CAAC,CAACxE,KAAK;kBAAEpB,QAAQ,EAAC,MAAM;kBAAEmB,UAAU,EAAE;gBAAM,CAAE;gBAAAuO,QAAA,EAC9E9J,IAAI,KAAK,MAAM,GAAG,WAAW,GAAGA,IAAI,KAAK,UAAU,GAAG,WAAW,GAAG;cAAW;gBAAA+Q,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACPjjB,OAAA;gBAAK6iB,KAAK,EAAE;kBAAC1W,QAAQ,EAAC,MAAM;kBAAEoB,KAAK,EAAE6f,OAAO,CAACG,CAAC,CAACxb,IAAI,CAAC,CAACxE,KAAK;kBAAEC,UAAU,EAAC,MAAM;kBAAEke,SAAS,EAAE;gBAAK,CAAE;gBAAA7P,QAAA,EAAEuR,OAAO,CAACG,CAAC,CAACxb,IAAI,CAAC,CAACgE;cAAU;gBAAA+M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAJ5HlR,IAAI;cAAA+Q,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKT,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAINjjB,OAAA;YAAK6iB,KAAK,EAAE;cAAE+K,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEnI,SAAS,EAAE;YAAS,CAAE;YAAA7J,QAAA,EAC5DkR,SAAS,CAAC/kB,GAAG,CAAC,CAAC+J,IAAI,EAAEkY,KAAK,KAAK;cAC9B;cACA,IAAI6D,YAAY,GAAG7D,KAAK;cACxB,IAAIA,KAAK,KAAK,CAAC,EAAE6D,YAAY,GAAG,CAAC,CAAC,KAC7B,IAAI7D,KAAK,KAAK,CAAC,EAAE6D,YAAY,GAAG,CAAC;cAEtC,MAAMC,WAAW,GAAGhB,SAAS,CAACe,YAAY,CAAC;cAE3C,OAAOV,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,iBAC3B/tB,OAAA;gBAAuB6iB,KAAK,EAAE;kBAACwI,YAAY,EAAC,KAAK;kBAAE1f,OAAO,EAAE,MAAM;kBAAE6gB,UAAU,EAAE,QAAQ;kBAAEf,cAAc,EAAE;gBAAY,CAAE;gBAAA5P,QAAA,gBACtH7b,OAAA;kBAAM6iB,KAAK,EAAE;oBAACtV,KAAK,EAAE6f,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAACxgB,KAAK;oBAAEpB,QAAQ,EAAC,MAAM;oBAAEmB,UAAU,EAAE;kBAAM,CAAE;kBAAAuO,QAAA,EACrFkS,WAAW,KAAK,MAAM,GAAG,WAAW,GAAGA,WAAW,KAAK,UAAU,GAAG,WAAW,GAAG;gBAAW;kBAAAjL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC,eACPjjB,OAAA;kBAAK6iB,KAAK,EAAE;oBACV1W,QAAQ,EAAC,MAAM;oBACfoB,KAAK,EAAE6f,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAACxgB,KAAK;oBACnCC,UAAU,EAAC,MAAM;oBACjB0gB,UAAU,EAAE;kBACd,CAAE;kBAAArS,QAAA,EAAEuR,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAAChY;gBAAU;kBAAA+M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GATpC8K,WAAW;gBAAAjL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUhB,CACN;YACH,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNjjB,OAAA;YAAK6iB,KAAK,EAAE;cAAE+K,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEnI,SAAS,EAAE;YAAS,CAAE;YAAA7J,QAAA,EAC5DkR,SAAS,CAAC/kB,GAAG,CAAC+J,IAAI,IAAIqb,OAAO,CAACI,CAAC,CAACzb,IAAI,CAAC,iBACpC/R,OAAA;cAAgB6iB,KAAK,EAAE;gBAACwI,YAAY,EAAC,KAAK;gBAAE1f,OAAO,EAAE,MAAM;gBAAE6gB,UAAU,EAAE,QAAQ;gBAAEf,cAAc,EAAE;cAAU,CAAE;cAAA5P,QAAA,gBAC7G7b,OAAA;gBAAK6iB,KAAK,EAAE;kBACV1W,QAAQ,EAAC,MAAM;kBACfoB,KAAK,EAAE6f,OAAO,CAACI,CAAC,CAACzb,IAAI,CAAC,CAACxE,KAAK;kBAC5BC,UAAU,EAAC,MAAM;kBACjBygB,WAAW,EAAE;gBACf,CAAE;gBAAApS,QAAA,EAAEuR,OAAO,CAACI,CAAC,CAACzb,IAAI,CAAC,CAACgE;cAAU;gBAAA+M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrCjjB,OAAA;gBAAM6iB,KAAK,EAAE;kBAACtV,KAAK,EAAE6f,OAAO,CAACI,CAAC,CAACzb,IAAI,CAAC,CAACxE,KAAK;kBAAEpB,QAAQ,EAAC,MAAM;kBAAEmB,UAAU,EAAE;gBAAM,CAAE;gBAAAuO,QAAA,EAC9E9J,IAAI,KAAK,MAAM,GAAG,WAAW,GAAGA,IAAI,KAAK,UAAU,GAAG,WAAW,GAAG;cAAW;gBAAA+Q,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA,GATClR,IAAI;cAAA+Q,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUT,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNjjB,OAAA;UAAK6iB,KAAK,EAAE;YAAE6I,SAAS,EAAE,KAAK;YAAEvf,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE,MAAM;YAAEmY,SAAS,EAAE;UAAS,CAAE;UAAA7J,QAAA,GAAC,4BAChF,EAAC,IAAIpR,IAAI,CAAC,CAAC,CAACkhB,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAA7I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACL;MACApW,OAAO,gBACL7M,OAAA;QAAK6iB,KAAK,EAAE;UAAE/W,OAAO,EAAE,MAAM;UAAEsB,KAAK,EAAE,OAAO;UAAEwW,UAAU,EAAE,kBAAkB;UAAEtY,QAAQ,EAAE;QAAW,CAAE;QAAAuQ,QAAA,gBACpG7b,OAAA,CAACysB,WAAW;UAAA3J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACfjjB,OAAA;UAAK6iB,KAAK,EAAE;YAAErV,UAAU,EAAE,MAAM;YAAE6d,YAAY,EAAE,MAAM;YAAElf,QAAQ,EAAE,MAAM;YAAEuZ,SAAS,EAAE;UAAS,CAAE;UAAA7J,QAAA,EAAE9L,YAAY,CAACG;QAAI;UAAA4S,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1HjjB,OAAA;UAAK6iB,KAAK,EAAE;YACV6C,SAAS,EAAE,QAAQ;YACnB5Z,OAAO,EAAE,QAAQ;YACjByB,KAAK,EAAE,SAAS;YAChBpB,QAAQ,EAAE,MAAM;YAChBqB,UAAU,EAAE,MAAM;YAClBoW,UAAU,EAAE,uBAAuB;YACnC3X,YAAY,EAAE,KAAK;YACnBof,YAAY,EAAE;UAChB,CAAE;UAAAxP,QAAA,EAAC;QAEH;UAAAiH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNjjB,OAAA;UAAK6iB,KAAK,EAAE;YAAE1W,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE,MAAM;YAAEmY,SAAS,EAAE;UAAS,CAAE;UAAA7J,QAAA,GAAC,kBAC9D,EAACjP,OAAO;QAAA;UAAAkW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACNjjB,OAAA;UAAK6iB,KAAK,EAAE;YAAE6I,SAAS,EAAE,KAAK;YAAEvf,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE,MAAM;YAAEmY,SAAS,EAAE;UAAS,CAAE;UAAA7J,QAAA,GAAC,4BAChF,EAAC,IAAIpR,IAAI,CAAC,CAAC,CAACkhB,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAA7I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH;;IAEA;IACA,MAAM1e,CAAC,GAAG,GAAG;IACb,MAAME,CAAC,GAAG,GAAG;;IAEb;IACA,IAAI9C,MAAM,CAACoL,mBAAmB,EAAE;MAC9BpL,MAAM,CAACoL,mBAAmB,CAACkB,OAAO,GAAGrB,OAAO;IAC9C;;IAEA;IACA,IAAIjL,MAAM,CAACqL,0BAA0B,IAAIrL,MAAM,CAACqL,0BAA0B,CAACiB,OAAO,EAAE;MAClF+Q,aAAa,CAACrd,MAAM,CAACqL,0BAA0B,CAACiB,OAAO,CAAC;MACxDtM,MAAM,CAACqL,0BAA0B,CAACiB,OAAO,GAAG,IAAI;IAClD;;IAEA;IACA,IAAItM,MAAM,CAACsL,uBAAuB,EAAE;MAClCtL,MAAM,CAACsL,uBAAuB,CAAC;QAC7BN,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBtB,QAAQ,EAAE;UAAE/G,CAAC;UAAEE;QAAE,CAAC;QAClBoI,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAAqe,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEre,MAAM,KAAI;MAC/B,CAAC,CAAC;;MAEF;MACA,IAAInL,MAAM,CAACqL,0BAA0B,EAAE;QACrCrL,MAAM,CAACqL,0BAA0B,CAACiB,OAAO,GAAG6Q,WAAW,CAAC,MAAM;UAC5Dnd,MAAM,CAACmP,qBAAqB,CAAClE,OAAO,CAAC;QACvC,CAAC,EAAE,IAAI,CAAC;MACV;MAEA,OAAO,IAAI;IACb,CAAC,MAAM;MACLrK,OAAO,CAACsB,KAAK,CAAC,8BAA8B,CAAC;MAC7C,OAAO,KAAK;IACd;EACF,CAAC,CAAC,OAAOA,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAID;AACA,MAAMqmB,yBAAyB,GAAIrjB,MAAM,IAAK;EAC5C,IAAIoH,OAAO,GAAGpH,MAAM;;EAEpB;EACA,IAAIoH,OAAO,IAAIA,OAAO,CAACqS,QAAQ,IAAIrS,OAAO,CAACqS,QAAQ,CAACvO,IAAI,KAAK,cAAc,EAAE;IAC3ExP,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEyL,OAAO,CAACiC,IAAI,IAAI,KAAK,CAAC;IAChD,OAAOjC,OAAO;EAChB;;EAEA;EACA,OAAOA,OAAO,IAAIA,OAAO,CAACvF,MAAM,EAAE;IAChCuF,OAAO,GAAGA,OAAO,CAACvF,MAAM;IACxB,IAAIuF,OAAO,CAACqS,QAAQ,IAAIrS,OAAO,CAACqS,QAAQ,CAACvO,IAAI,KAAK,cAAc,EAAE;MAChExP,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEyL,OAAO,CAACiC,IAAI,IAAI,KAAK,CAAC;MAChD,OAAOjC,OAAO;IAChB;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACAtM,MAAM,CAACwsB,kBAAkB,GAAG,CAAC5pB,CAAC,EAAEE,CAAC,KAAK;EACpC,IAAI;IACFlC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE+B,CAAC,EAAEE,CAAC,CAAC;;IAEnC;IACA,MAAM6f,MAAM,GAAGC,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAI,CAAChC,MAAM,EAAE;MACX/hB,OAAO,CAACsB,KAAK,CAAC,sBAAsB,CAAC;MACrC,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAAC7C,KAAK,IAAI,CAACsL,SAAS,CAAC2B,OAAO,EAAE;MAChC1L,OAAO,CAACsB,KAAK,CAAC,iBAAiB,CAAC;MAChC,OAAO,KAAK;IACd;;IAEA;IACA,IAAIU,CAAC,KAAK0Q,SAAS,IAAIxQ,CAAC,KAAKwQ,SAAS,EAAE;MACtC1Q,CAAC,GAAG5C,MAAM,CAAC4X,UAAU,GAAG,CAAC;MACzB9U,CAAC,GAAG9C,MAAM,CAAC6X,WAAW,GAAG,CAAC;IAC5B;;IAEA;IACA,MAAMuP,IAAI,GAAGzE,MAAM,CAAC0E,qBAAqB,CAAC,CAAC;IAC3C,MAAMC,MAAM,GAAI,CAAC1kB,CAAC,GAAGwkB,IAAI,CAACvd,IAAI,IAAI8Y,MAAM,CAAC4E,WAAW,GAAI,CAAC,GAAG,CAAC;IAC7D,MAAMC,MAAM,GAAG,EAAE,CAAC1kB,CAAC,GAAGskB,IAAI,CAAC5b,GAAG,IAAImX,MAAM,CAAC8E,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;IAE9D7mB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEymB,MAAM,EAAEE,MAAM,CAAC;;IAErC;IACA,MAAME,SAAS,GAAG,IAAIjqB,KAAK,CAACkqB,SAAS,CAAC,CAAC;IACvCD,SAAS,CAACnF,MAAM,CAACqF,MAAM,CAACC,SAAS,GAAG,CAAC;IACrCH,SAAS,CAACnF,MAAM,CAACuF,IAAI,CAACD,SAAS,GAAG,CAAC;IAEnC,MAAME,WAAW,GAAG,IAAItqB,KAAK,CAACuqB,OAAO,CAACV,MAAM,EAAEE,MAAM,CAAC;IACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,EAAEpd,SAAS,CAAC2B,OAAO,CAAC;;IAEvD;IACA,MAAM4b,mBAAmB,GAAG,EAAE;IAC9BjnB,gBAAgB,CAACuE,OAAO,CAAC,CAAC6Y,QAAQ,EAAEpT,OAAO,KAAK;MAC9C,IAAIoT,QAAQ,CAACtZ,KAAK,EAAE;QAClBmjB,mBAAmB,CAAC3T,IAAI,CAAC8J,QAAQ,CAACtZ,KAAK,CAAC;QACxCnE,OAAO,CAACC,GAAG,CAAC,SAASoK,OAAO,QAAQ,CAAC;MACvC;IACF,CAAC,CAAC;;IAEF;IACArK,OAAO,CAACC,GAAG,CAAC,QAAQqnB,mBAAmB,CAACniB,MAAM,YAAY,CAAC;IAC3D,MAAM0mB,YAAY,GAAG/E,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,EAAE,IAAI,CAAC;IAE1E,IAAIuE,YAAY,CAAC1mB,MAAM,GAAG,CAAC,EAAE;MAC3BnF,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B4rB,YAAY,CAACjnB,OAAO,CAAC,CAAC6iB,SAAS,EAAE/Z,CAAC,KAAK;QACrC1N,OAAO,CAACC,GAAG,CAAC,MAAMyN,CAAC,GAAG,EAAE+Z,SAAS,CAACnjB,MAAM,CAACqJ,IAAI,IAAI,KAAK,EAC1C,KAAK,EAAE8Z,SAAS,CAAChlB,QAAQ,EACzB,WAAW,EAAEglB,SAAS,CAACnjB,MAAM,CAACyE,QAAQ,CAACoF,OAAO,CAAC,CAAC,EAChD,WAAW,EAAEsZ,SAAS,CAACnjB,MAAM,CAACyZ,QAAQ,CAAC;;QAEnD;QACA,MAAM/B,GAAG,GAAG2L,yBAAyB,CAACF,SAAS,CAACnjB,MAAM,CAAC;QACvD,IAAI0X,GAAG,IAAIA,GAAG,CAAC+B,QAAQ,IAAI/B,GAAG,CAAC+B,QAAQ,CAACvO,IAAI,KAAK,cAAc,EAAE;UAC/DxP,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE+b,GAAG,CAAC+B,QAAQ,CAAC1T,OAAO,CAAC;QAC/C;MACF,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;;IAEA;IACArK,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,MAAM6rB,eAAe,GAAGhF,SAAS,CAACU,gBAAgB,CAAC/oB,KAAK,CAAC6a,QAAQ,EAAE,IAAI,CAAC;IAExEtZ,OAAO,CAACC,GAAG,CAAC,WAAW6rB,eAAe,CAAC3mB,MAAM,MAAM,CAAC;IACpD2mB,eAAe,CAAClnB,OAAO,CAAC,CAAC6iB,SAAS,EAAE/Z,CAAC,KAAK;MACxC,MAAMsO,GAAG,GAAGyL,SAAS,CAACnjB,MAAM;MAC5BtE,OAAO,CAACC,GAAG,CAAC,QAAQyN,CAAC,GAAG,EAAEsO,GAAG,CAACrO,IAAI,IAAI,KAAK,EAC/B,KAAK,EAAEqO,GAAG,CAACxM,IAAI,EACf,KAAK,EAAEwM,GAAG,CAACjT,QAAQ,CAACoF,OAAO,CAAC,CAAC,EAC7B,KAAK,EAAEsZ,SAAS,CAAChlB,QAAQ,EACzB,WAAW,EAAEuZ,GAAG,CAAC+B,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACA/d,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,IAAI8rB,YAAY,GAAG,CAAC;IAEpB1rB,gBAAgB,CAACuE,OAAO,CAAC,CAAC6Y,QAAQ,EAAEpT,OAAO,KAAK;MAC9C,IAAIoT,QAAQ,CAACtZ,KAAK,EAAE;QAAA,IAAA6nB,qBAAA;QAClB;QACA,IAAIC,SAAS,GAAGxO,QAAQ,CAACtZ,KAAK,CAACiG,OAAO;QACtC,IAAI8hB,cAAc,GAAG,IAAI;;QAEzB;QACA,MAAMnE,QAAQ,GAAG,IAAIlrB,KAAK,CAACiG,OAAO,CAAC,CAAC;QACpC2a,QAAQ,CAACtZ,KAAK,CAAC6jB,gBAAgB,CAACD,QAAQ,CAAC;;QAEzC;QACA,MAAMoE,gBAAgB,GAAGpE,QAAQ,CAACrlB,UAAU,CAACqH,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAC;;QAExE;QACA,MAAMkf,SAAS,GAAGF,QAAQ,CAACjmB,KAAK,CAAC,CAAC,CAAComB,OAAO,CAACne,SAAS,CAAC2B,OAAO,CAAC;QAC7D,IAAIxI,IAAI,CAACK,GAAG,CAAC0kB,SAAS,CAACjmB,CAAC,CAAC,GAAG,CAAC,IAAIkB,IAAI,CAACK,GAAG,CAAC0kB,SAAS,CAAC/lB,CAAC,CAAC,GAAG,CAAC,IAAI+lB,SAAS,CAAC7lB,CAAC,GAAG,CAAC,CAAC,IAAI6lB,SAAS,CAAC7lB,CAAC,GAAG,CAAC,EAAE;UACjG8pB,cAAc,GAAG,KAAK;QACxB;QAEA,IAAID,SAAS,EAAE;UACbF,YAAY,EAAE;QAChB;QAEA/rB,OAAO,CAACC,GAAG,CAAC,OAAOoK,OAAO,GAAG,EAAE;UAC7B+hB,EAAE,EAAE,EAAAJ,qBAAA,GAAAvO,QAAQ,CAACjQ,YAAY,cAAAwe,qBAAA,uBAArBA,qBAAA,CAAuBre,IAAI,KAAI,IAAI;UACvC0e,GAAG,EAAEJ,SAAS;UACdK,KAAK,EAAEJ,cAAc;UACrBK,IAAI,EAAExE,QAAQ,CAAC5Z,OAAO,CAAC,CAAC;UACxBqe,IAAI,EAAE,CAACvE,SAAS,CAACjmB,CAAC,EAAEimB,SAAS,CAAC/lB,CAAC,EAAE+lB,SAAS,CAAC7lB,CAAC,CAAC;UAC7CqqB,MAAM,EAAEN;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEFnsB,OAAO,CAACC,GAAG,CAAC,MAAM8rB,YAAY,IAAI1rB,gBAAgB,CAACiW,IAAI,WAAW,CAAC;;IAEnE;IACA,OAAOwV,eAAe,CAAC3mB,MAAM,GAAG,CAAC;EACnC,CAAC,CAAC,OAAO7D,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,OAAO,KAAK;EACd;AACF,CAAC;;AAID;AACA,MAAMyS,wBAAwB,GAAGA,CAACR,YAAY,EAAEG,SAAS,KAAK;EAAA,IAAAgZ,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC5D,IAAI,CAACrZ,YAAY,IAAI,CAACA,YAAY,CAACpP,KAAK,IAAI,CAACuP,SAAS,EAAE;IACtD;EACF;;EAEA;EACA,MAAMmZ,cAAc,GAAG,EAAE;EACzBtZ,YAAY,CAACpP,KAAK,CAACE,QAAQ,CAACkN,KAAK,IAAI;IACnC,IAAIA,KAAK,CAACwM,QAAQ,IAAIxM,KAAK,CAACwM,QAAQ,CAAC+O,OAAO,EAAE;MAC5CD,cAAc,CAAClZ,IAAI,CAACpC,KAAK,CAAC;IAC5B;EACF,CAAC,CAAC;EAEFsb,cAAc,CAACjoB,OAAO,CAAC8jB,KAAK,IAAI;IAC9BnV,YAAY,CAACpP,KAAK,CAACiC,MAAM,CAACsiB,KAAK,CAAC;EAClC,CAAC,CAAC;;EAEF;EACA,IAAIO,UAAU;EACd,QAAOvV,SAAS,CAACH,YAAY;IAC3B,KAAK,GAAG;MACN0V,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;MACNA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;IACR;MACEA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;EACJ;;EAEA;EACA,MAAMlD,aAAa,GAAG,IAAIlpB,KAAK,CAAC8gB,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAMqI,aAAa,GAAG,IAAInpB,KAAK,CAACqgB,iBAAiB,CAAC;IAChDlS,KAAK,EAAEie,UAAU;IACjBxX,QAAQ,EAAEwX,UAAU;IACpB8D,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM9G,SAAS,GAAG,IAAIppB,KAAK,CAACsgB,IAAI,CAAC4I,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAACld,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAClC4jB,SAAS,CAAClI,QAAQ,GAAG;IACnB+O,OAAO,EAAE,IAAI;IACbtd,IAAI,EAAE,cAAc;IACpBnF,OAAO,GAAAqiB,qBAAA,GAAEnZ,YAAY,CAAC/F,YAAY,cAAAkf,qBAAA,uBAAzBA,qBAAA,CAA2BriB,OAAO;IAC3C2I,OAAO,EAAEU,SAAS,CAACV,OAAO;IAC1BE,SAAS,EAAEQ,SAAS,CAACR,SAAS;IAC9BM,UAAU,EAAEE,SAAS,CAACF;EACxB,CAAC;;EAED;EACA,MAAMkV,KAAK,GAAG,IAAI7rB,KAAK,CAACmwB,UAAU,CAAC/D,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;EACrDP,KAAK,CAAC3f,QAAQ,CAAC1G,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAC5BqmB,KAAK,CAAC3K,QAAQ,GAAG;IAAE+O,OAAO,EAAE;EAAK,CAAC;;EAElC;EACAvZ,YAAY,CAACpP,KAAK,CAACC,GAAG,CAAC6hB,SAAS,CAAC;EACjC1S,YAAY,CAACpP,KAAK,CAACC,GAAG,CAACskB,KAAK,CAAC;EAE7B1oB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAA0sB,sBAAA,GAAApZ,YAAY,CAAC/F,YAAY,cAAAmf,sBAAA,uBAAzBA,sBAAA,CAA2Bhf,IAAI,OAAAif,sBAAA,GAAIrZ,YAAY,CAAC/F,YAAY,cAAAof,sBAAA,uBAAzBA,sBAAA,CAA2BviB,OAAO,cAAaqJ,SAAS,CAACH,YAAY,WAAWG,SAAS,CAACF,UAAU,GAAG,CAAC;AACjK,CAAC;AAED,eAAevM,WAAW;AAAC,IAAAua,EAAA;AAAAyL,YAAA,CAAAzL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}