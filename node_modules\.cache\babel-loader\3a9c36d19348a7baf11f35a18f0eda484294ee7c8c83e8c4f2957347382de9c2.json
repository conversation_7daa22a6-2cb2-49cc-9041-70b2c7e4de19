{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport NotificationTwoToneSvg from \"@ant-design/icons-svg/es/asn/NotificationTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar NotificationTwoTone = function NotificationTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: NotificationTwoToneSvg\n  }));\n};\n\n/**![notification](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTIyOS42IDY3OC4xYy0zLjcgMTEuNi01LjYgMjMuOS01LjYgMzYuNCAwLTEyLjUgMi0yNC44IDUuNy0zNi40aC0uMXptNzYuMy0yNjAuMkgxODR2MTg4LjJoMTIxLjlsMTIuOSA1LjJMODQwIDgyMC43VjIwMy4zTDMxOC44IDQxMi43eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODgwIDExMmMtMy44IDAtNy43LjctMTEuNiAyLjNMMjkyIDM0NS45SDEyOGMtOC44IDAtMTYgNy40LTE2IDE2LjZ2Mjk5YzAgOS4yIDcuMiAxNi42IDE2IDE2LjZoMTAxLjdjLTMuNyAxMS42LTUuNyAyMy45LTUuNyAzNi40IDAgNjUuOSA1My44IDExOS41IDEyMCAxMTkuNSA1NS40IDAgMTAyLjEtMzcuNiAxMTUuOS04OC40bDQwOC42IDE2NC4yYzMuOSAxLjUgNy44IDIuMyAxMS42IDIuMyAxNi45IDAgMzItMTQuMiAzMi0zMy4yVjE0NS4yQzkxMiAxMjYuMiA4OTcgMTEyIDg4MCAxMTJ6TTM0NCA3NjIuM2MtMjYuNSAwLTQ4LTIxLjQtNDgtNDcuOCAwLTExLjIgMy45LTIxLjkgMTEtMzAuNGw4NC45IDM0LjFjLTIgMjQuNi0yMi43IDQ0LjEtNDcuOSA0NC4xem00OTYgNTguNEwzMTguOCA2MTEuM2wtMTIuOS01LjJIMTg0VjQxNy45aDEyMS45bDEyLjktNS4yTDg0MCAyMDMuM3Y2MTcuNHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(NotificationTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'NotificationTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "NotificationTwoToneSvg", "AntdIcon", "NotificationTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@ant-design/icons/es/icons/NotificationTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport NotificationTwoToneSvg from \"@ant-design/icons-svg/es/asn/NotificationTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar NotificationTwoTone = function NotificationTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: NotificationTwoToneSvg\n  }));\n};\n\n/**![notification](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTIyOS42IDY3OC4xYy0zLjcgMTEuNi01LjYgMjMuOS01LjYgMzYuNCAwLTEyLjUgMi0yNC44IDUuNy0zNi40aC0uMXptNzYuMy0yNjAuMkgxODR2MTg4LjJoMTIxLjlsMTIuOSA1LjJMODQwIDgyMC43VjIwMy4zTDMxOC44IDQxMi43eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNODgwIDExMmMtMy44IDAtNy43LjctMTEuNiAyLjNMMjkyIDM0NS45SDEyOGMtOC44IDAtMTYgNy40LTE2IDE2LjZ2Mjk5YzAgOS4yIDcuMiAxNi42IDE2IDE2LjZoMTAxLjdjLTMuNyAxMS42LTUuNyAyMy45LTUuNyAzNi40IDAgNjUuOSA1My44IDExOS41IDEyMCAxMTkuNSA1NS40IDAgMTAyLjEtMzcuNiAxMTUuOS04OC40bDQwOC42IDE2NC4yYzMuOSAxLjUgNy44IDIuMyAxMS42IDIuMyAxNi45IDAgMzItMTQuMiAzMi0zMy4yVjE0NS4yQzkxMiAxMjYuMiA4OTcgMTEyIDg4MCAxMTJ6TTM0NCA3NjIuM2MtMjYuNSAwLTQ4LTIxLjQtNDgtNDcuOCAwLTExLjIgMy45LTIxLjkgMTEtMzAuNGw4NC45IDM0LjFjLTIgMjQuNi0yMi43IDQ0LjEtNDcuOSA0NC4xem00OTYgNTguNEwzMTguOCA2MTEuM2wtMTIuOS01LjJIMTg0VjQxNy45aDEyMS45bDEyLjktNS4yTDg0MCAyMDMuM3Y2MTcuNHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(NotificationTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'NotificationTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,mBAAmB,GAAG,SAASA,mBAAmBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,mBAAmB,CAAC;AAChE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,qBAAqB;AAC7C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}