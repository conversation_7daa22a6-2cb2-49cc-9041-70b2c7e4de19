{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\DeviceStatus.jsx\",\n  _s = $RefreshSig$();\n// src/pages/DeviceStatus.jsx\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge, Tag, message, Divider, Select, Button, Space } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { PieChart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\n\n// 修改所有资源的基础URL\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n// 注册必要的echarts组件\necharts.use([PieChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\n\n// 页面布局容器   // height: calc(100vh - 96px - env(safe-area-inset-bottom)); // 减去顶部导航栏高度和内边距\n// const PageContainer = styled.div`\n//   display: flex;\n//   height: calc(100vh - 96px ); // 减去顶部导航栏高度和内边距\n//   overflow: hidden;\n// `;\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 65px );\n  overflow: hidden;\n`;\n// 左侧信息栏容器\n_c = PageContainer;\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n  background-color: #fafafa;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 24px' : props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 0' : props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\n_c2 = MainContent;\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  box-shadow: 0 1px 3px rgba(0,0,0,0.05);\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: 0 2px 6px rgba(0,0,0,0.1);\n  }\n\n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n    background-color: #f7f7f7;\n    border-bottom: 1px solid #eee;\n  }\n\n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n    font-weight: 600;\n  }\n\n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\n_c3 = InfoCard;\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n\n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\nconst DeviceStatus = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [devices, setDevices] = useState([]);\n  const [selectedDevice, setSelectedDevice] = useState(null);\n  const [stats, setStats] = useState({\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0,\n    normalDevices: 0,\n    warningDevices: 0,\n    errorDevices: 0\n  });\n\n  // 新增：ping检测相关状态\n  const [pingLoading, setPingLoading] = useState(false);\n  const [lastPingTime, setLastPingTime] = useState(null);\n  const [pingResults, setPingResults] = useState({});\n  const deviceTypeChartRef = useRef(null);\n  const deviceStatusChartRef = useRef(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  // 添加筛选相关状态\n  const [filterType, setFilterType] = useState(null);\n  const [filterValue, setFilterValue] = useState(null);\n  const [secondOptions, setSecondOptions] = useState([]);\n  const [filteredDevices, setFilteredDevices] = useState([]);\n\n  // ========== Ping检测相关函数 ==========\n\n  /**\n   * 执行ping检测\n   */\n  const performPingCheck = async (showLoading = true) => {\n    try {\n      if (showLoading) {\n        setPingLoading(true);\n      }\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      console.log('开始ping检测所有设备...');\n      const response = await axios.post(`${apiUrl}/api/devices/ping`, {}, {\n        timeout: 60000 // 60秒超时\n      });\n      if (response.data && response.data.success) {\n        const {\n          results,\n          stats: pingStats,\n          duration\n        } = response.data.data;\n        console.log('Ping检测完成:', {\n          总设备数: pingStats.total,\n          在线: pingStats.online,\n          离线: pingStats.offline,\n          无IP: pingStats.noIp,\n          耗时: `${duration}ms`\n        });\n\n        // 更新ping结果\n        setPingResults(results);\n        setLastPingTime(new Date());\n\n        // 根据ping结果更新设备状态\n        updateDevicesWithPingResults(results);\n        message.success(`Ping检测完成：${pingStats.online}个在线，${pingStats.offline}个离线 (耗时${duration}ms)`);\n      } else {\n        console.error('Ping检测API返回错误:', response.data);\n        message.error('Ping检测失败');\n      }\n    } catch (error) {\n      console.error('Ping检测失败:', error);\n      message.error(`Ping检测失败: ${error.message}`);\n    } finally {\n      if (showLoading) {\n        setPingLoading(false);\n      }\n    }\n  };\n\n  /**\n   * 根据ping结果更新设备状态\n   */\n  const updateDevicesWithPingResults = pingResults => {\n    console.log('🔄 开始根据ping结果更新设备状态...');\n    console.log('Ping结果数量:', Object.keys(pingResults).length);\n    setDevices(prevDevices => {\n      console.log('更新前设备状态统计:', {\n        总数: prevDevices.length,\n        在线: prevDevices.filter(d => d.status === 'online').length,\n        离线: prevDevices.filter(d => d.status === 'offline').length\n      });\n      const updatedDevices = prevDevices.map(device => {\n        const pingResult = pingResults[device.id];\n        if (pingResult) {\n          // 根据ping结果更新设备状态\n          let newStatus = device.status;\n          let newHealth = device.health;\n          if (pingResult.reason === 'no_ip') {\n            // 没有IP地址的设备标记为离线状态，健康度为警告\n            newStatus = 'offline';\n            newHealth = 'warning';\n            console.log(`设备 ${device.name}: 无IP地址 → 离线(警告)`);\n          } else if (pingResult.isOnline) {\n            newStatus = 'online';\n            newHealth = 'normal';\n            console.log(`设备 ${device.name}: Ping通 → 在线`);\n          } else {\n            newStatus = 'offline';\n            newHealth = 'error';\n            console.log(`设备 ${device.name}: Ping不通 → 离线(错误)`);\n          }\n          return {\n            ...device,\n            status: newStatus,\n            health: newHealth,\n            lastPingTime: pingResult.timestamp,\n            responseTime: pingResult.responseTime,\n            pingStatus: pingResult.isOnline ? 'online' : 'offline'\n          };\n        } else {\n          // 没有ping结果的设备，标记为未知状态\n          console.log(`设备 ${device.name}: 无ping结果 → 保持原状态`);\n          return device;\n        }\n      });\n      console.log('更新后设备状态统计:', {\n        总数: updatedDevices.length,\n        在线: updatedDevices.filter(d => d.status === 'online').length,\n        离线: updatedDevices.filter(d => d.status === 'offline').length\n      });\n\n      // 更新筛选后的设备列表\n      setFilteredDevices(updatedDevices);\n\n      // 重新计算统计数据和更新图表\n      console.log('🔄 调用updateCharts更新图表...');\n      updateCharts(updatedDevices);\n      updateStats(updatedDevices);\n      return updatedDevices;\n    });\n  };\n\n  /**\n   * 更新统计数据\n   */\n  const updateStats = devicesData => {\n    const newStats = {\n      totalDevices: devicesData.length,\n      onlineDevices: devicesData.filter(d => d.status === 'online').length,\n      offlineDevices: devicesData.filter(d => d.status !== 'online').length,\n      normalDevices: devicesData.filter(d => d.health === 'normal').length,\n      warningDevices: devicesData.filter(d => d.health === 'warning').length,\n      errorDevices: devicesData.filter(d => d.health === 'error').length\n    };\n    setStats(newStats);\n  };\n\n  // 修改设备数据获取函数\n  const fetchDevices = async () => {\n    try {\n      setLoading(true);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      if (response.data && response.data.success && response.data.data) {\n        const devicesData = response.data.data;\n\n        // 为每个设备添加健康状态、CPU、内存和磁盘使用率\n        const processedDevices = devicesData.map(device => ({\n          ...device,\n          ip: device.ipAddress,\n          // 确保IP地址的兼容性\n          mac: device.mac,\n          health: 'normal',\n          // 初始健康状态，将通过ping检测更新\n          cpu: Math.floor(Math.random() * 100),\n          memory: Math.floor(Math.random() * 100),\n          disk: Math.floor(Math.random() * 100),\n          lastActive: new Date(Date.now() - Math.floor(Math.random() * 3600000)).toLocaleString(),\n          lastPingTime: null,\n          responseTime: null,\n          pingStatus: null\n        }));\n        setDevices(processedDevices);\n        setFilteredDevices(processedDevices); // 初始设置筛选后的设备为全部设备\n        updateCharts(processedDevices);\n        updateStats(processedDevices);\n\n        // 获取设备数据后立即执行一次ping检测\n        setTimeout(() => {\n          performPingCheck(false); // 不显示loading，因为页面已经在loading中\n        }, 1000);\n      } else {\n        console.error('API返回错误:', response.data);\n      }\n    } catch (error) {\n      console.error('获取设备数据失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 添加图表实例引用\n  const typeChartRef = useRef(null);\n  const statusChartRef = useRef(null);\n\n  // 更新图表数据的函数\n  const updateCharts = devicesData => {\n    console.log('更新图表数据:', {\n      设备总数: devicesData.length,\n      在线设备: devicesData.filter(d => d.status === 'online').length,\n      离线设备: devicesData.filter(d => d.status === 'offline').length\n    });\n\n    // 设备类型分布数据\n    const typeData = {};\n    devicesData.forEach(device => {\n      const type = deviceTypeMap[device.type] || device.type;\n      typeData[type] = (typeData[type] || 0) + 1;\n    });\n\n    // 设备状态分布数据\n    const statusData = {};\n    devicesData.forEach(device => {\n      const status = deviceStatusMap[device.status] || device.status;\n      statusData[status] = (statusData[status] || 0) + 1;\n    });\n    console.log('状态分布数据:', statusData);\n\n    // 自定义颜色方案，匹配截图样式\n    const colorPalette = ['#52C41A',\n    // 绿色\n    '#FF4D4F',\n    // 红色\n    '#4B96FF',\n    // 蓝色\n    '#FAAD14',\n    // 橙黄色\n    '#FA8C16',\n    // 橙色\n    '#722ED1',\n    // 紫色\n    '#13C2C2',\n    // 青色\n    '#F5222D',\n    // 亮红色\n    '#FADB14',\n    // 黄色\n    '#747474' // 深灰\n    ];\n\n    // 更新类型分布图表\n    if (deviceTypeChartRef.current) {\n      // 获取或创建图表实例\n      let typeChart = typeChartRef.current;\n      if (!typeChart) {\n        typeChart = echarts.init(deviceTypeChartRef.current);\n        typeChartRef.current = typeChart;\n      }\n      typeChart.setOption({\n        color: colorPalette,\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(50,50,50,0.9)',\n          borderRadius: 4,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: 'horizontal',\n          bottom: 0,\n          left: 'center',\n          itemWidth: 12,\n          itemHeight: 12,\n          textStyle: {\n            fontSize: 12\n          },\n          padding: [30, 10, 5, 10]\n        },\n        series: [{\n          name: '设备类型',\n          type: 'pie',\n          radius: ['40%', '70%'],\n          center: ['50%', '40%'],\n          avoidLabelOverlap: true,\n          itemStyle: {\n            borderRadius: 4,\n            borderColor: '#fff',\n            borderWidth: 2\n          },\n          label: {\n            show: true,\n            formatter: '{d}%',\n            position: 'inside',\n            fontSize: 12,\n            fontWeight: 'bold',\n            color: 'auto',\n            textBorderColor: 'rgba(255, 255, 255, 0.8)',\n            textBorderWidth: 2,\n            textShadow: '0 0 4px rgba(0, 0, 0, 0.5)'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: 14,\n              fontWeight: 'bold'\n            },\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: Object.entries(typeData).map(([name, value]) => ({\n            name,\n            value\n          }))\n        }]\n      });\n\n      // 图表实例已保存在ref中，resize事件在useEffect中统一处理\n    }\n\n    // 更新状态分布图表\n    if (deviceStatusChartRef.current) {\n      // 获取或创建图表实例\n      let statusChart = statusChartRef.current;\n      if (!statusChart) {\n        statusChart = echarts.init(deviceStatusChartRef.current);\n        statusChartRef.current = statusChart;\n      }\n      statusChart.setOption({\n        color: colorPalette,\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(50,50,50,0.9)',\n          borderRadius: 4,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: 'horizontal',\n          bottom: 0,\n          left: 'center',\n          itemWidth: 12,\n          itemHeight: 12,\n          textStyle: {\n            fontSize: 12\n          },\n          padding: [30, 10, 5, 10]\n        },\n        series: [{\n          name: '设备状态',\n          type: 'pie',\n          radius: ['40%', '70%'],\n          center: ['50%', '40%'],\n          avoidLabelOverlap: true,\n          itemStyle: {\n            borderRadius: 4,\n            borderColor: '#fff',\n            borderWidth: 2\n          },\n          label: {\n            show: true,\n            formatter: '{d}%',\n            position: 'inside',\n            fontSize: 12,\n            fontWeight: 'bold',\n            color: 'auto',\n            textBorderColor: 'rgba(255, 255, 255, 0.8)',\n            textBorderWidth: 2,\n            textShadow: '0 0 4px rgba(0, 0, 0, 0.5)'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: 14,\n              fontWeight: 'bold'\n            },\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: Object.entries(statusData).map(([name, value]) => ({\n            name,\n            value\n          }))\n        }]\n      });\n\n      // 图表实例已保存在ref中，resize事件在useEffect中统一处理\n    }\n  };\n\n  // 设备类型映射\n  const deviceTypeMap = {\n    camera: '摄像头',\n    mmwave_radar: '毫米波雷达',\n    lidar: '激光雷达',\n    rsu: 'RSU',\n    edge_computing: '边缘计算单元',\n    obu: 'OBU'\n  };\n\n  // 设备状态映射\n  const deviceStatusMap = {\n    'online': '在线',\n    'offline': '离线',\n    'warning': '警告',\n    'error': '错误',\n    'maintenance': '维护中'\n  };\n\n  // 添加筛选相关函数\n  const handleFirstSelectChange = value => {\n    setFilterType(value);\n    setFilterValue(null); // 重置第二个选择框的值\n\n    // 根据第一个选择框的值动态更新第二个选择框的选项\n    if (value) {\n      const uniqueValues = [...new Set(devices.map(device => device[value]))];\n      setSecondOptions(uniqueValues.filter(Boolean).sort()); // 过滤掉空值并排序\n    } else {\n      setSecondOptions([]);\n    }\n  };\n  const handleSecondSelectChange = value => {\n    setFilterValue(value);\n  };\n  const handleFilterSearch = () => {\n    if (!filterType || !filterValue) {\n      // 如果未选择筛选条件，显示全部设备\n      setFilteredDevices(devices);\n    } else {\n      // 根据筛选条件过滤设备\n      const filtered = devices.filter(device => device[filterType] === filterValue);\n      setFilteredDevices(filtered);\n    }\n  };\n  const handleResetFilter = () => {\n    setFilterType(null);\n    setFilterValue(null);\n    setSecondOptions([]);\n    setFilteredDevices(devices);\n  };\n\n  // 获取第二个选择框的选项标签\n  const getSecondSelectOptions = () => {\n    if (!filterType) return [];\n    return secondOptions.map(option => {\n      // 如果筛选条件是设备类型，使用deviceTypeMap进行映射显示\n      if (filterType === 'type') {\n        return {\n          value: option,\n          label: deviceTypeMap[option] || option\n        };\n      }\n      return {\n        value: option,\n        label: option\n      };\n    });\n  };\n\n  // ========== 定时ping检测 ==========\n\n  // 自动ping检测定时器 - 每300秒（5分钟）自动检测\n  useEffect(() => {\n    let intervalId;\n\n    // 自动开启ping检测，固定间隔300秒\n    intervalId = setInterval(() => {\n      console.log('执行定时ping检测 (间隔: 300秒)');\n      performPingCheck(false); // 定时检测不显示loading\n    }, 300 * 1000); // 300秒 = 5分钟\n\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n      }\n    };\n  }, [performPingCheck]);\n\n  // 在组件挂载时获取设备数据\n  useEffect(() => {\n    fetchDevices();\n  }, []);\n\n  // 添加自动选择第一个设备的逻辑，使用filteredDevices替代devices\n  useEffect(() => {\n    if (filteredDevices.length > 0 && !selectedDevice) {\n      setSelectedDevice(filteredDevices[0]);\n      console.log('已自动选择第一个设备:', filteredDevices[0].name);\n    }\n  }, [filteredDevices, selectedDevice]);\n\n  // 处理设备选择\n  const handleDeviceSelect = device => {\n    setSelectedDevice(device);\n  };\n\n  // 设备列表列定义\n  const deviceColumns = [{\n    title: '设备名称',\n    dataIndex: 'name',\n    key: 'name',\n    width: '40%',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 13,\n          fontWeight: 500\n        },\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: '30%',\n    render: (status, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: record.pingStatus && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 11,\n          marginTop: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Badge, {\n          status: record.pingStatus === 'online' ? 'success' : 'error',\n          text: record.pingStatus === 'online' ? '在线' : '离线'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 651,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 649,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '健康度',\n    dataIndex: 'health',\n    key: 'health',\n    width: '30%',\n    render: health => {\n      let color = 'green';\n      let text = '正常';\n      if (health === 'warning') {\n        color = 'orange';\n        text = '警告';\n      } else if (health === 'error') {\n        color = 'red';\n        text = '错误';\n      }\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: color,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 16\n      }, this);\n    }\n  }];\n\n  // 获取健康状态颜色\n  const getHealthColor = value => {\n    if (value >= 80) return '#f5222d'; // 红色\n    if (value >= 60) return '#faad14'; // 黄色\n    return '#52c41a'; // 绿色\n  };\n\n  // 添加对echarts实例的清理和resize处理\n  useEffect(() => {\n    const handleResize = () => {\n      var _typeChartRef$current, _statusChartRef$curre;\n      (_typeChartRef$current = typeChartRef.current) === null || _typeChartRef$current === void 0 ? void 0 : _typeChartRef$current.resize();\n      (_statusChartRef$curre = statusChartRef.current) === null || _statusChartRef$curre === void 0 ? void 0 : _statusChartRef$curre.resize();\n    };\n    window.addEventListener('resize', handleResize);\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      // 清理图表实例\n      if (typeChartRef.current) {\n        typeChartRef.current.dispose();\n        typeChartRef.current = null;\n      }\n      if (statusChartRef.current) {\n        statusChartRef.current.dispose();\n        statusChartRef.current = null;\n      }\n    };\n  }, []);\n\n  // 添加全局样式到文档头\n  useEffect(() => {\n    const styleElement = document.createElement('style');\n    styleElement.innerHTML = globalStyles;\n    document.head.appendChild(styleElement);\n    return () => {\n      document.head.removeChild(styleElement);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    spinning: loading,\n    tip: \"\\u52A0\\u8F7D\\u4E2D...\",\n    children: /*#__PURE__*/_jsxDEV(PageContainer, {\n      children: [/*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"left\",\n        collapsed: leftCollapsed,\n        onCollapse: () => setLeftCollapsed(!leftCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8DEF\\u4FA7\\u8BBE\\u5907\\u4F4D\\u7F6E\\u603B\\u56FE\",\n          bordered: false,\n          height: \"240px\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: `${BASE_URL}/images/overview_map.png`,\n              alt: \"\\u8DEF\\u4FA7\\u8BBE\\u5907\\u4F4D\\u7F6E\\u603B\\u56FE\",\n              style: {\n                maxWidth: '100%',\n                maxHeight: '100%',\n                objectFit: 'contain',\n                borderRadius: 8,\n                boxShadow: '0 2px 8px rgba(0,0,0,0.08)'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8BBE\\u5907\\u7C7B\\u578B\\u5206\\u5E03\",\n          bordered: false,\n          height: \"calc(50% - 135px)\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: deviceTypeChartRef,\n            style: {\n              height: '100%',\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8BBE\\u5907\\u72B6\\u6001\\u5206\\u5E03\",\n          bordered: false,\n          height: \"calc(50% - 135px)\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: deviceStatusChartRef,\n            style: {\n              height: '100%',\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 757,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 756,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 728,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        leftCollapsed: leftCollapsed,\n        rightCollapsed: rightCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 768,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"right\",\n        collapsed: rightCollapsed,\n        onCollapse: () => setRightCollapsed(!rightCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8BBE\\u5907\\u5217\\u8868\",\n          bordered: false,\n          height: \"50%\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 8,\n                padding: '6px 8px',\n                borderBottom: '1px solid #f0f0f0',\n                backgroundColor: '#f9f9f9',\n                borderRadius: '4px'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: 11,\n                  color: '#666',\n                  textAlign: 'center'\n                },\n                children: lastPingTime ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\uD83D\\uDD04 \\u81EA\\u52A8\\u68C0\\u6D4B\\u4E2D (5\\u5206\\u949F\\u95F4\\u9694) | \\u6700\\u540E\\u68C0\\u6D4B: \", lastPingTime.toLocaleTimeString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 795,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\uD83D\\uDD04 \\u81EA\\u52A8\\u68C0\\u6D4B\\u4E2D (5\\u5206\\u949F\\u95F4\\u9694) | \\u7B49\\u5F85\\u9996\\u6B21\\u68C0\\u6D4B...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 799,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1,\n                overflow: 'auto'\n              },\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                dataSource: filteredDevices,\n                columns: deviceColumns,\n                rowKey: \"id\",\n                pagination: false,\n                size: \"small\",\n                scroll: {\n                  y: true\n                },\n                onRow: record => ({\n                  onClick: () => handleDeviceSelect(record),\n                  style: {\n                    cursor: 'pointer',\n                    background: (selectedDevice === null || selectedDevice === void 0 ? void 0 : selectedDevice.id) === record.id ? '#e6f7ff' : 'transparent',\n                    fontSize: '13px',\n                    padding: '4px 8px'\n                  }\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '8px',\n                padding: '8px 0',\n                borderTop: '1px solid #f0f0f0',\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                flexWrap: 'wrap',\n                gap: '4px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Space, {\n                size: \"small\",\n                style: {\n                  flexWrap: 'wrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u7B5B\\u9009\\u6761\\u4EF6\",\n                  style: {\n                    width: '100px'\n                  },\n                  size: \"small\",\n                  value: filterType,\n                  onChange: handleFirstSelectChange,\n                  allowClear: true,\n                  options: [{\n                    value: 'type',\n                    label: '设备类型'\n                  }, {\n                    value: 'location',\n                    label: '位置'\n                  }]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  placeholder: \"\\u7B5B\\u9009\\u503C\",\n                  style: {\n                    width: '120px'\n                  },\n                  size: \"small\",\n                  value: filterValue,\n                  onChange: handleSecondSelectChange,\n                  disabled: !filterType || secondOptions.length === 0,\n                  allowClear: true,\n                  options: getSecondSelectOptions()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  size: \"small\",\n                  onClick: handleFilterSearch,\n                  children: \"\\u67E5\\u8BE2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 858,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  onClick: handleResetFilter,\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 861,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 835,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '12px',\n                  color: '#999'\n                },\n                children: [filteredDevices.length, \" / \", devices.length, \" \\u4E2A\\u8BBE\\u5907\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8BBE\\u5907\\u8BE6\\u7EC6\\u4FE1\\u606F\",\n          bordered: false,\n          height: \"50%\",\n          children: selectedDevice ? /*#__PURE__*/_jsxDEV(Descriptions, {\n            bordered: true,\n            column: 1,\n            size: \"small\",\n            styles: {\n              label: {\n                fontSize: '13px',\n                padding: '4px 8px'\n              },\n              content: {\n                fontSize: '13px',\n                padding: '4px 8px'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8BBE\\u5907\\u540D\\u79F0\",\n              children: selectedDevice.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 884,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8BBE\\u5907\\u7C7B\\u578B\",\n              children: deviceTypeMap[selectedDevice.type] || selectedDevice.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 885,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                status: selectedDevice.status === 'online' ? 'success' : 'error',\n                text: selectedDevice.status === 'online' ? '在线' : '离线'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 889,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u4F4D\\u7F6E\",\n              children: [selectedDevice.location, \" \", selectedDevice.entrance ? `(${selectedDevice.entrance})` : '']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"IP\\u5730\\u5740\",\n              children: [selectedDevice.ip || '未配置', selectedDevice.ip && selectedDevice.responseTime && /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  marginLeft: 8,\n                  fontSize: 11,\n                  color: '#666'\n                },\n                children: [\"(\", selectedDevice.responseTime, \"ms)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 898,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 17\n            }, this), selectedDevice.type === 'rsu' && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"MAC\\u5730\\u5740\",\n              children: selectedDevice.mac\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 903,\n              columnNumber: 52\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8FDE\\u63A5\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Badge, {\n                  status: selectedDevice.pingStatus === 'online' ? 'success' : selectedDevice.pingStatus === 'offline' ? 'error' : 'default',\n                  text: selectedDevice.pingStatus === 'online' ? '在线' : selectedDevice.pingStatus === 'offline' ? '离线' : selectedDevice.ip ? '未检测' : '无IP地址'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 906,\n                  columnNumber: 21\n                }, this), selectedDevice.lastPingTime && /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    marginLeft: 8,\n                    fontSize: 11,\n                    color: '#666'\n                  },\n                  children: new Date(selectedDevice.lastPingTime).toLocaleString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 915,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 905,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6700\\u540E\\u6D3B\\u52A8\\u65F6\\u95F4\",\n              children: selectedDevice.lastActive\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 17\n            }, this), selectedDevice.status === 'online' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                label: \"CPU\\u4F7F\\u7528\\u7387\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '100px',\n                      height: '8px',\n                      background: '#f0f0f0',\n                      borderRadius: '4px',\n                      overflow: 'hidden',\n                      marginRight: '8px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: `${selectedDevice.cpu}%`,\n                        height: '100%',\n                        background: getHealthColor(selectedDevice.cpu),\n                        borderRadius: '4px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 934,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 926,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [selectedDevice.cpu, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 925,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 924,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                label: \"\\u5185\\u5B58\\u4F7F\\u7528\\u7387\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '100px',\n                      height: '8px',\n                      background: '#f0f0f0',\n                      borderRadius: '4px',\n                      overflow: 'hidden',\n                      marginRight: '8px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: `${selectedDevice.memory}%`,\n                        height: '100%',\n                        background: getHealthColor(selectedDevice.memory),\n                        borderRadius: '4px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 954,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 946,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [selectedDevice.memory, \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 961,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 945,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 944,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 875,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '13px'\n            },\n            children: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u67E5\\u770B\\u8BE6\\u7EC6\\u4FE1\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 988,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 873,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 773,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 726,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 725,\n    columnNumber: 5\n  }, this);\n};\n\n// 移除了未使用的styles变量\n\n// 添加全局样式\n_s(DeviceStatus, \"uqz0rJFm/1u239jutexpWgSpveM=\");\n_c4 = DeviceStatus;\nconst globalStyles = `\n  .device-status-container {\n    display: flex;\n    height: 100%;\n  }\n\n  .device-sidebar {\n    width: 320px;\n    padding: 16px;\n    background-color: #f5f5f5;\n    overflow-y: auto;\n    border-right: 1px solid #e8e8e8;\n  }\n\n  .device-content {\n    flex: 1;\n    padding: 16px;\n    overflow-y: auto;\n  }\n\n  .ant-card-head-title {\n    font-weight: bold;\n    font-size: 16px;\n  }\n`;\nexport default DeviceStatus;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"InfoCard\");\n$RefreshReg$(_c4, \"DeviceStatus\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Row", "Col", "Card", "Statistic", "List", "Table", "Descriptions", "Spin", "Badge", "Tag", "message", "Divider", "Select", "<PERSON><PERSON>", "Space", "echarts", "<PERSON><PERSON><PERSON>", "GridComponent", "TooltipComponent", "LegendComponent", "TitleComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "CollapsibleSidebar", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BASE_URL", "process", "env", "REACT_APP_API_URL", "use", "<PERSON><PERSON><PERSON><PERSON>", "div", "_c", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "_c2", "InfoCard", "height", "_c3", "CompactStatistic", "DeviceStatus", "_s", "loading", "setLoading", "devices", "setDevices", "selected<PERSON><PERSON><PERSON>", "setSelectedDevice", "stats", "setStats", "totalDevices", "onlineDevices", "offlineDevices", "normalDevices", "warningDevices", "errorDevices", "pingLoading", "setPingLoading", "lastPingTime", "setLastPingTime", "pingResults", "setPingResults", "deviceTypeChartRef", "deviceStatusChartRef", "setLeftCollapsed", "setRightCollapsed", "filterType", "setFilterType", "filterValue", "setFilterValue", "secondOptions", "setSecondOptions", "filteredDevices", "setFilteredDevices", "performPingCheck", "showLoading", "apiUrl", "console", "log", "response", "post", "timeout", "data", "success", "results", "pingStats", "duration", "总设备数", "total", "在线", "online", "离线", "offline", "无IP", "noIp", "耗时", "Date", "updateDevicesWithPingResults", "error", "Object", "keys", "length", "prevDevices", "总数", "filter", "d", "status", "updatedDevices", "map", "device", "pingResult", "id", "newStatus", "newHealth", "health", "reason", "name", "isOnline", "timestamp", "responseTime", "pingStatus", "updateCharts", "updateStats", "devicesData", "newStats", "fetchDevices", "get", "processedDevices", "ip", "ip<PERSON><PERSON><PERSON>", "mac", "cpu", "Math", "floor", "random", "memory", "disk", "lastActive", "now", "toLocaleString", "setTimeout", "typeChartRef", "statusChartRef", "设备总数", "在线设备", "离线设备", "typeData", "for<PERSON>ach", "type", "deviceTypeMap", "statusData", "deviceStatusMap", "colorPalette", "current", "typeChart", "init", "setOption", "color", "tooltip", "trigger", "formatter", "backgroundColor", "borderRadius", "textStyle", "legend", "orient", "bottom", "left", "itemWidth", "itemHeight", "fontSize", "padding", "series", "radius", "center", "avoidLabelOverlap", "itemStyle", "borderColor", "borderWidth", "label", "show", "position", "fontWeight", "textBorderColor", "textBorder<PERSON>idth", "textShadow", "emphasis", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "labelLine", "entries", "value", "statusChart", "camera", "mmwave_radar", "lidar", "rsu", "edge_computing", "obu", "handleFirstSelectChange", "uniqueValues", "Set", "Boolean", "sort", "handleSecondSelectChange", "handleFilterSearch", "filtered", "handleResetFilter", "getSecondSelectOptions", "option", "intervalId", "setInterval", "clearInterval", "handleDeviceSelect", "deviceColumns", "title", "dataIndex", "key", "width", "render", "text", "record", "children", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "getHealthColor", "handleResize", "_typeChartRef$current", "_statusChartRef$curre", "resize", "window", "addEventListener", "removeEventListener", "dispose", "styleElement", "document", "createElement", "innerHTML", "globalStyles", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "spinning", "tip", "collapsed", "onCollapse", "bordered", "display", "alignItems", "justifyContent", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "objectFit", "boxShadow", "ref", "flexDirection", "marginBottom", "borderBottom", "textAlign", "toLocaleTimeString", "flex", "overflow", "dataSource", "columns", "<PERSON><PERSON><PERSON>", "pagination", "size", "scroll", "y", "onRow", "onClick", "cursor", "background", "borderTop", "flexWrap", "gap", "placeholder", "onChange", "allowClear", "options", "disabled", "column", "styles", "content", "<PERSON><PERSON>", "location", "entrance", "marginLeft", "marginRight", "_c4", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/DeviceStatus.jsx"], "sourcesContent": ["// src/pages/DeviceStatus.jsx\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge, Tag, message, Divider, Select, Button, Space } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { Pie<PERSON>hart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\n\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n// 注册必要的echarts组件\necharts.use([PieChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\n\n// 页面布局容器   // height: calc(100vh - 96px - env(safe-area-inset-bottom)); // 减去顶部导航栏高度和内边距\n// const PageContainer = styled.div`\n//   display: flex;\n//   height: calc(100vh - 96px ); // 减去顶部导航栏高度和内边距\n//   overflow: hidden;\n// `;\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 65px );\n  overflow: hidden;\n`;\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n  background-color: #fafafa;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' :\n    props.leftCollapsed ? '0 8px 0 24px' :\n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' :\n    props.leftCollapsed ? '0 8px 0 0' :\n    props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  box-shadow: 0 1px 3px rgba(0,0,0,0.05);\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: 0 2px 6px rgba(0,0,0,0.1);\n  }\n\n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n    background-color: #f7f7f7;\n    border-bottom: 1px solid #eee;\n  }\n\n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n    font-weight: 600;\n  }\n\n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n\n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n\nconst DeviceStatus = () => {\n  const [loading, setLoading] = useState(true);\n  const [devices, setDevices] = useState([]);\n  const [selectedDevice, setSelectedDevice] = useState(null);\n  const [stats, setStats] = useState({\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0,\n    normalDevices: 0,\n    warningDevices: 0,\n    errorDevices: 0\n  });\n\n  // 新增：ping检测相关状态\n  const [pingLoading, setPingLoading] = useState(false);\n  const [lastPingTime, setLastPingTime] = useState(null);\n  const [pingResults, setPingResults] = useState({});\n\n  const deviceTypeChartRef = useRef(null);\n  const deviceStatusChartRef = useRef(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  // 添加筛选相关状态\n  const [filterType, setFilterType] = useState(null);\n  const [filterValue, setFilterValue] = useState(null);\n  const [secondOptions, setSecondOptions] = useState([]);\n  const [filteredDevices, setFilteredDevices] = useState([]);\n\n  // ========== Ping检测相关函数 ==========\n\n  /**\n   * 执行ping检测\n   */\n  const performPingCheck = async (showLoading = true) => {\n    try {\n      if (showLoading) {\n        setPingLoading(true);\n      }\n\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      console.log('开始ping检测所有设备...');\n\n      const response = await axios.post(`${apiUrl}/api/devices/ping`, {}, {\n        timeout: 60000 // 60秒超时\n      });\n\n      if (response.data && response.data.success) {\n        const { results, stats: pingStats, duration } = response.data.data;\n\n        console.log('Ping检测完成:', {\n          总设备数: pingStats.total,\n          在线: pingStats.online,\n          离线: pingStats.offline,\n          无IP: pingStats.noIp,\n          耗时: `${duration}ms`\n        });\n\n        // 更新ping结果\n        setPingResults(results);\n        setLastPingTime(new Date());\n\n        // 根据ping结果更新设备状态\n        updateDevicesWithPingResults(results);\n\n        message.success(`Ping检测完成：${pingStats.online}个在线，${pingStats.offline}个离线 (耗时${duration}ms)`);\n      } else {\n        console.error('Ping检测API返回错误:', response.data);\n        message.error('Ping检测失败');\n      }\n    } catch (error) {\n      console.error('Ping检测失败:', error);\n      message.error(`Ping检测失败: ${error.message}`);\n    } finally {\n      if (showLoading) {\n        setPingLoading(false);\n      }\n    }\n  };\n\n  /**\n   * 根据ping结果更新设备状态\n   */\n  const updateDevicesWithPingResults = (pingResults) => {\n    console.log('🔄 开始根据ping结果更新设备状态...');\n    console.log('Ping结果数量:', Object.keys(pingResults).length);\n\n    setDevices(prevDevices => {\n      console.log('更新前设备状态统计:', {\n        总数: prevDevices.length,\n        在线: prevDevices.filter(d => d.status === 'online').length,\n        离线: prevDevices.filter(d => d.status === 'offline').length\n      });\n\n      const updatedDevices = prevDevices.map(device => {\n        const pingResult = pingResults[device.id];\n        if (pingResult) {\n          // 根据ping结果更新设备状态\n          let newStatus = device.status;\n          let newHealth = device.health;\n\n          if (pingResult.reason === 'no_ip') {\n            // 没有IP地址的设备标记为离线状态，健康度为警告\n            newStatus = 'offline';\n            newHealth = 'warning';\n            console.log(`设备 ${device.name}: 无IP地址 → 离线(警告)`);\n          } else if (pingResult.isOnline) {\n            newStatus = 'online';\n            newHealth = 'normal';\n            console.log(`设备 ${device.name}: Ping通 → 在线`);\n          } else {\n            newStatus = 'offline';\n            newHealth = 'error';\n            console.log(`设备 ${device.name}: Ping不通 → 离线(错误)`);\n          }\n\n          return {\n            ...device,\n            status: newStatus,\n            health: newHealth,\n            lastPingTime: pingResult.timestamp,\n            responseTime: pingResult.responseTime,\n            pingStatus: pingResult.isOnline ? 'online' : 'offline'\n          };\n        } else {\n          // 没有ping结果的设备，标记为未知状态\n          console.log(`设备 ${device.name}: 无ping结果 → 保持原状态`);\n          return device;\n        }\n      });\n\n      console.log('更新后设备状态统计:', {\n        总数: updatedDevices.length,\n        在线: updatedDevices.filter(d => d.status === 'online').length,\n        离线: updatedDevices.filter(d => d.status === 'offline').length\n      });\n\n      // 更新筛选后的设备列表\n      setFilteredDevices(updatedDevices);\n\n      // 重新计算统计数据和更新图表\n      console.log('🔄 调用updateCharts更新图表...');\n      updateCharts(updatedDevices);\n      updateStats(updatedDevices);\n\n      return updatedDevices;\n    });\n  };\n\n  /**\n   * 更新统计数据\n   */\n  const updateStats = (devicesData) => {\n    const newStats = {\n      totalDevices: devicesData.length,\n      onlineDevices: devicesData.filter(d => d.status === 'online').length,\n      offlineDevices: devicesData.filter(d => d.status !== 'online').length,\n      normalDevices: devicesData.filter(d => d.health === 'normal').length,\n      warningDevices: devicesData.filter(d => d.health === 'warning').length,\n      errorDevices: devicesData.filter(d => d.health === 'error').length\n    };\n    setStats(newStats);\n  };\n\n  // 修改设备数据获取函数\n  const fetchDevices = async () => {\n    try {\n      setLoading(true);\n\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n\n      if (response.data && response.data.success && response.data.data) {\n        const devicesData = response.data.data;\n\n        // 为每个设备添加健康状态、CPU、内存和磁盘使用率\n        const processedDevices = devicesData.map(device => ({\n          ...device,\n          ip: device.ipAddress, // 确保IP地址的兼容性\n          mac: device.mac,\n          health: 'normal', // 初始健康状态，将通过ping检测更新\n          cpu: Math.floor(Math.random() * 100),\n          memory: Math.floor(Math.random() * 100),\n          disk: Math.floor(Math.random() * 100),\n          lastActive: new Date(Date.now() - Math.floor(Math.random() * 3600000)).toLocaleString(),\n          lastPingTime: null,\n          responseTime: null,\n          pingStatus: null\n        }));\n\n        setDevices(processedDevices);\n        setFilteredDevices(processedDevices); // 初始设置筛选后的设备为全部设备\n        updateCharts(processedDevices);\n        updateStats(processedDevices);\n\n        // 获取设备数据后立即执行一次ping检测\n        setTimeout(() => {\n          performPingCheck(false); // 不显示loading，因为页面已经在loading中\n        }, 1000);\n      } else {\n        console.error('API返回错误:', response.data);\n      }\n    } catch (error) {\n      console.error('获取设备数据失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 添加图表实例引用\n  const typeChartRef = useRef(null);\n  const statusChartRef = useRef(null);\n\n  // 更新图表数据的函数\n  const updateCharts = (devicesData) => {\n    console.log('更新图表数据:', {\n      设备总数: devicesData.length,\n      在线设备: devicesData.filter(d => d.status === 'online').length,\n      离线设备: devicesData.filter(d => d.status === 'offline').length\n    });\n\n    // 设备类型分布数据\n    const typeData = {};\n    devicesData.forEach(device => {\n      const type = deviceTypeMap[device.type] || device.type;\n      typeData[type] = (typeData[type] || 0) + 1;\n    });\n\n    // 设备状态分布数据\n    const statusData = {};\n    devicesData.forEach(device => {\n      const status = deviceStatusMap[device.status] || device.status;\n      statusData[status] = (statusData[status] || 0) + 1;\n    });\n\n    console.log('状态分布数据:', statusData);\n\n    // 自定义颜色方案，匹配截图样式\n    const colorPalette = [\n      '#52C41A', // 绿色\n      '#FF4D4F', // 红色\n      '#4B96FF', // 蓝色\n      '#FAAD14', // 橙黄色\n      '#FA8C16', // 橙色\n      '#722ED1', // 紫色\n      '#13C2C2', // 青色\n      '#F5222D', // 亮红色\n      '#FADB14',  // 黄色\n      '#747474' // 深灰\n    ];\n\n    // 更新类型分布图表\n    if (deviceTypeChartRef.current) {\n      // 获取或创建图表实例\n      let typeChart = typeChartRef.current;\n      if (!typeChart) {\n        typeChart = echarts.init(deviceTypeChartRef.current);\n        typeChartRef.current = typeChart;\n      }\n\n      typeChart.setOption({\n        color: colorPalette,\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(50,50,50,0.9)',\n          borderRadius: 4,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: 'horizontal',\n          bottom: 0,\n          left: 'center',\n          itemWidth: 12,\n          itemHeight: 12,\n          textStyle: {\n            fontSize: 12\n          },\n          padding: [30, 10, 5, 10]\n        },\n        series: [{\n          name: '设备类型',\n          type: 'pie',\n          radius: ['40%', '70%'],\n          center: ['50%', '40%'],\n          avoidLabelOverlap: true,\n          itemStyle: {\n            borderRadius: 4,\n            borderColor: '#fff',\n            borderWidth: 2\n          },\n          label: {\n            show: true,\n            formatter: '{d}%',\n            position: 'inside',\n            fontSize: 12,\n            fontWeight: 'bold',\n            color: 'auto',\n            textBorderColor: 'rgba(255, 255, 255, 0.8)',\n            textBorderWidth: 2,\n            textShadow: '0 0 4px rgba(0, 0, 0, 0.5)'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: 14,\n              fontWeight: 'bold'\n            },\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: Object.entries(typeData).map(([name, value]) => ({ name, value }))\n        }]\n      });\n\n      // 图表实例已保存在ref中，resize事件在useEffect中统一处理\n    }\n\n    // 更新状态分布图表\n    if (deviceStatusChartRef.current) {\n      // 获取或创建图表实例\n      let statusChart = statusChartRef.current;\n      if (!statusChart) {\n        statusChart = echarts.init(deviceStatusChartRef.current);\n        statusChartRef.current = statusChart;\n      }\n\n      statusChart.setOption({\n        color: colorPalette,\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(50,50,50,0.9)',\n          borderRadius: 4,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          orient: 'horizontal',\n          bottom: 0,\n          left: 'center',\n          itemWidth: 12,\n          itemHeight: 12,\n          textStyle: {\n            fontSize: 12\n          },\n          padding: [30, 10, 5, 10]\n        },\n        series: [{\n          name: '设备状态',\n          type: 'pie',\n          radius: ['40%', '70%'],\n          center: ['50%', '40%'],\n          avoidLabelOverlap: true,\n          itemStyle: {\n            borderRadius: 4,\n            borderColor: '#fff',\n            borderWidth: 2\n          },\n          label: {\n            show: true,\n            formatter: '{d}%',\n            position: 'inside',\n            fontSize: 12,\n            fontWeight: 'bold',\n            color: 'auto',\n            textBorderColor: 'rgba(255, 255, 255, 0.8)',\n            textBorderWidth: 2,\n            textShadow: '0 0 4px rgba(0, 0, 0, 0.5)'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: 14,\n              fontWeight: 'bold'\n            },\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: Object.entries(statusData).map(([name, value]) => ({ name, value }))\n        }]\n      });\n\n      // 图表实例已保存在ref中，resize事件在useEffect中统一处理\n    }\n  };\n\n  // 设备类型映射\n  const deviceTypeMap = {\n    camera: '摄像头',\n    mmwave_radar: '毫米波雷达',\n    lidar: '激光雷达',\n    rsu: 'RSU',\n    edge_computing: '边缘计算单元',\n    obu: 'OBU'\n  };\n\n  // 设备状态映射\n  const deviceStatusMap = {\n    'online': '在线',\n    'offline': '离线',\n    'warning': '警告',\n    'error': '错误',\n    'maintenance': '维护中'\n  };\n\n  // 添加筛选相关函数\n  const handleFirstSelectChange = (value) => {\n    setFilterType(value);\n    setFilterValue(null); // 重置第二个选择框的值\n\n    // 根据第一个选择框的值动态更新第二个选择框的选项\n    if (value) {\n      const uniqueValues = [...new Set(devices.map(device => device[value]))];\n      setSecondOptions(uniqueValues.filter(Boolean).sort()); // 过滤掉空值并排序\n    } else {\n      setSecondOptions([]);\n    }\n  };\n\n  const handleSecondSelectChange = (value) => {\n    setFilterValue(value);\n  };\n\n  const handleFilterSearch = () => {\n    if (!filterType || !filterValue) {\n      // 如果未选择筛选条件，显示全部设备\n      setFilteredDevices(devices);\n    } else {\n      // 根据筛选条件过滤设备\n      const filtered = devices.filter(device => device[filterType] === filterValue);\n      setFilteredDevices(filtered);\n    }\n  };\n\n  const handleResetFilter = () => {\n    setFilterType(null);\n    setFilterValue(null);\n    setSecondOptions([]);\n    setFilteredDevices(devices);\n  };\n\n  // 获取第二个选择框的选项标签\n  const getSecondSelectOptions = () => {\n    if (!filterType) return [];\n\n    return secondOptions.map(option => {\n      // 如果筛选条件是设备类型，使用deviceTypeMap进行映射显示\n      if (filterType === 'type') {\n        return {\n          value: option,\n          label: deviceTypeMap[option] || option\n        };\n      }\n      return {\n        value: option,\n        label: option\n      };\n    });\n  };\n\n  // ========== 定时ping检测 ==========\n\n  // 自动ping检测定时器 - 每300秒（5分钟）自动检测\n  useEffect(() => {\n    let intervalId;\n\n    // 自动开启ping检测，固定间隔300秒\n    intervalId = setInterval(() => {\n      console.log('执行定时ping检测 (间隔: 300秒)');\n      performPingCheck(false); // 定时检测不显示loading\n    }, 300 * 1000); // 300秒 = 5分钟\n\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n      }\n    };\n  }, [performPingCheck]);\n\n  // 在组件挂载时获取设备数据\n  useEffect(() => {\n    fetchDevices();\n  }, []);\n\n  // 添加自动选择第一个设备的逻辑，使用filteredDevices替代devices\n  useEffect(() => {\n    if (filteredDevices.length > 0 && !selectedDevice) {\n      setSelectedDevice(filteredDevices[0]);\n      console.log('已自动选择第一个设备:', filteredDevices[0].name);\n    }\n  }, [filteredDevices, selectedDevice]);\n\n  // 处理设备选择\n  const handleDeviceSelect = (device) => {\n    setSelectedDevice(device);\n  };\n\n  // 设备列表列定义\n  const deviceColumns = [\n    {\n      title: '设备名称',\n      dataIndex: 'name',\n      key: 'name',\n      width: '40%',\n      render: (text, record) => (\n        <div>\n          <div style={{ fontSize: 13, fontWeight: 500 }}>{text}</div>\n        </div>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: '30%',\n      render: (status, record) => (\n        <div>\n          {record.pingStatus && (\n            <div style={{ fontSize: 11, marginTop: 2 }}>\n              <Badge\n                status={record.pingStatus === 'online' ? 'success' : 'error'}\n                text={record.pingStatus === 'online' ? '在线' : '离线'}\n              />\n            </div>\n          )}\n        </div>\n      ),\n    },\n    {\n      title: '健康度',\n      dataIndex: 'health',\n      key: 'health',\n      width: '30%',\n      render: health => {\n        let color = 'green';\n        let text = '正常';\n\n        if (health === 'warning') {\n          color = 'orange';\n          text = '警告';\n        } else if (health === 'error') {\n          color = 'red';\n          text = '错误';\n        }\n\n        return <Tag color={color}>{text}</Tag>;\n      },\n    }\n  ];\n\n  // 获取健康状态颜色\n  const getHealthColor = (value) => {\n    if (value >= 80) return '#f5222d'; // 红色\n    if (value >= 60) return '#faad14'; // 黄色\n    return '#52c41a'; // 绿色\n  };\n\n  // 添加对echarts实例的清理和resize处理\n  useEffect(() => {\n    const handleResize = () => {\n      typeChartRef.current?.resize();\n      statusChartRef.current?.resize();\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      // 清理图表实例\n      if (typeChartRef.current) {\n        typeChartRef.current.dispose();\n        typeChartRef.current = null;\n      }\n      if (statusChartRef.current) {\n        statusChartRef.current.dispose();\n        statusChartRef.current = null;\n      }\n    };\n  }, []);\n\n  // 添加全局样式到文档头\n  useEffect(() => {\n    const styleElement = document.createElement('style');\n    styleElement.innerHTML = globalStyles;\n    document.head.appendChild(styleElement);\n\n    return () => {\n      document.head.removeChild(styleElement);\n    };\n  }, []);\n\n  return (\n    <Spin spinning={loading} tip=\"加载中...\">\n      <PageContainer>\n        {/* 左侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"left\"\n          collapsed={leftCollapsed}\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\n        >\n          {/* 路侧设备位置总图 `${BASE_URL}/images/${device.type}.png`;*/}\n          <InfoCard title=\"路侧设备位置总图\" bordered={false} height=\"240px\">\n            <div style={{ width: '100%',  display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n              <img\n                src={`${BASE_URL}/images/overview_map.png`}\n                alt=\"路侧设备位置总图\"\n                style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain', borderRadius: 8, boxShadow: '0 2px 8px rgba(0,0,0,0.08)' }}\n              />\n            </div>\n          </InfoCard>\n\n          {/* 设备类型分布 */}\n          <InfoCard title=\"设备类型分布\" bordered={false} height=\"calc(50% - 135px)\">\n            <div\n              ref={deviceTypeChartRef}\n              style={{\n                height: '100%',\n                width: '100%'\n              }}\n            ></div>\n          </InfoCard>\n\n          {/* 设备状态分布 */}\n          <InfoCard title=\"设备状态分布\" bordered={false} height=\"calc(50% - 135px)\">\n            <div\n              ref={deviceStatusChartRef}\n              style={{\n                height: '100%',\n                width: '100%'\n              }}\n            ></div>\n          </InfoCard>\n        </CollapsibleSidebar>\n\n        {/* 主内容区域 */}\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\n          {/* 保持空白，不显示任何内容 */}\n        </MainContent>\n\n        {/* 右侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"right\"\n          collapsed={rightCollapsed}\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\n        >\n          {/* 设备列表 */}\n          <InfoCard title=\"设备列表\" bordered={false} height=\"50%\">\n            <div style={{\n              display: 'flex',\n              flexDirection: 'column',\n              height: '100%'\n            }}>\n              {/* 自动ping检测状态显示 */}\n              <div style={{\n                marginBottom: 8,\n                padding: '6px 8px',\n                borderBottom: '1px solid #f0f0f0',\n                backgroundColor: '#f9f9f9',\n                borderRadius: '4px'\n              }}>\n                <div style={{ fontSize: 11, color: '#666', textAlign: 'center' }}>\n                  {lastPingTime ? (\n                    <span>\n                      🔄 自动检测中 (5分钟间隔) | 最后检测: {lastPingTime.toLocaleTimeString()}\n                    </span>\n                  ) : (\n                    <span>🔄 自动检测中 (5分钟间隔) | 等待首次检测...</span>\n                  )}\n                </div>\n              </div>\n\n              <div style={{ flex: 1, overflow: 'auto' }}>\n                <Table\n                  dataSource={filteredDevices}\n                  columns={deviceColumns}\n                  rowKey=\"id\"\n                  pagination={false}\n                  size=\"small\"\n                  scroll={{ y: true }}\n                  onRow={(record) => ({\n                    onClick: () => handleDeviceSelect(record),\n                    style: {\n                      cursor: 'pointer',\n                      background: selectedDevice?.id === record.id ? '#e6f7ff' : 'transparent',\n                      fontSize: '13px',\n                      padding: '4px 8px'\n                    }\n                  })}\n                />\n              </div>\n\n              {/* 底部筛选区域 */}\n              <div style={{\n                marginTop: '8px',\n                padding: '8px 0',\n                borderTop: '1px solid #f0f0f0',\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                flexWrap: 'wrap',\n                gap: '4px'\n              }}>\n                <Space size=\"small\" style={{ flexWrap: 'wrap' }}>\n                  <Select\n                    placeholder=\"筛选条件\"\n                    style={{ width: '100px' }}\n                    size=\"small\"\n                    value={filterType}\n                    onChange={handleFirstSelectChange}\n                    allowClear\n                    options={[\n                      { value: 'type', label: '设备类型' },\n                      { value: 'location', label: '位置' }\n                    ]}\n                  />\n                  <Select\n                    placeholder=\"筛选值\"\n                    style={{ width: '120px' }}\n                    size=\"small\"\n                    value={filterValue}\n                    onChange={handleSecondSelectChange}\n                    disabled={!filterType || secondOptions.length === 0}\n                    allowClear\n                    options={getSecondSelectOptions()}\n                  />\n                  <Button type=\"primary\" size=\"small\" onClick={handleFilterSearch}>\n                    查询\n                  </Button>\n                  <Button size=\"small\" onClick={handleResetFilter}>\n                    重置\n                  </Button>\n                </Space>\n                <div style={{ fontSize: '12px', color: '#999' }}>\n                  {filteredDevices.length} / {devices.length} 个设备\n                </div>\n              </div>\n            </div>\n          </InfoCard>\n\n          {/* 设备详细信息 */}\n          <InfoCard title=\"设备详细信息\" bordered={false} height=\"50%\">\n            {selectedDevice ? (\n              <Descriptions\n                bordered\n                column={1}\n                size=\"small\"\n                styles={{\n                  label: { fontSize: '13px', padding: '4px 8px' },\n                  content: { fontSize: '13px', padding: '4px 8px' }\n                }}\n              >\n                <Descriptions.Item label=\"设备名称\">{selectedDevice.name}</Descriptions.Item>\n                <Descriptions.Item label=\"设备类型\">\n                  {deviceTypeMap[selectedDevice.type] || selectedDevice.type}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"状态\">\n                  <Badge\n                    status={selectedDevice.status === 'online' ? 'success' : 'error'}\n                    text={selectedDevice.status === 'online' ? '在线' : '离线'}\n                  />\n                </Descriptions.Item>\n                <Descriptions.Item label=\"位置\">{selectedDevice.location} {selectedDevice.entrance ? `(${selectedDevice.entrance})` : ''}</Descriptions.Item>\n                <Descriptions.Item label=\"IP地址\">\n                  {selectedDevice.ip || '未配置'}\n                  {selectedDevice.ip && selectedDevice.responseTime && (\n                    <span style={{ marginLeft: 8, fontSize: 11, color: '#666' }}>\n                      ({selectedDevice.responseTime}ms)\n                    </span>\n                  )}\n                </Descriptions.Item>\n                {selectedDevice.type === 'rsu' && (<Descriptions.Item label=\"MAC地址\">{selectedDevice.mac}</Descriptions.Item>)}\n                <Descriptions.Item label=\"连接状态\">\n                  <div style={{ display: 'flex', alignItems: 'center' }}>\n                    <Badge\n                      status={selectedDevice.pingStatus === 'online' ? 'success' : selectedDevice.pingStatus === 'offline' ? 'error' : 'default'}\n                      text={\n                        selectedDevice.pingStatus === 'online' ? '在线' :\n                        selectedDevice.pingStatus === 'offline' ? '离线' :\n                        selectedDevice.ip ? '未检测' : '无IP地址'\n                      }\n                    />\n                    {selectedDevice.lastPingTime && (\n                      <span style={{ marginLeft: 8, fontSize: 11, color: '#666' }}>\n                        {new Date(selectedDevice.lastPingTime).toLocaleString()}\n                      </span>\n                    )}\n                  </div>\n                </Descriptions.Item>\n                <Descriptions.Item label=\"最后活动时间\">{selectedDevice.lastActive}</Descriptions.Item>\n                {selectedDevice.status === 'online' && (\n                  <>\n                    <Descriptions.Item label=\"CPU使用率\">\n                      <div style={{ display: 'flex', alignItems: 'center' }}>\n                        <div style={{\n                          width: '100px',\n                          height: '8px',\n                          background: '#f0f0f0',\n                          borderRadius: '4px',\n                          overflow: 'hidden',\n                          marginRight: '8px'\n                        }}>\n                          <div style={{\n                            width: `${selectedDevice.cpu}%`,\n                            height: '100%',\n                            background: getHealthColor(selectedDevice.cpu),\n                            borderRadius: '4px'\n                          }} />\n                        </div>\n                        <span>{selectedDevice.cpu}%</span>\n                      </div>\n                    </Descriptions.Item>\n                    <Descriptions.Item label=\"内存使用率\">\n                      <div style={{ display: 'flex', alignItems: 'center' }}>\n                        <div style={{\n                          width: '100px',\n                          height: '8px',\n                          background: '#f0f0f0',\n                          borderRadius: '4px',\n                          overflow: 'hidden',\n                          marginRight: '8px'\n                        }}>\n                          <div style={{\n                            width: `${selectedDevice.memory}%`,\n                            height: '100%',\n                            background: getHealthColor(selectedDevice.memory),\n                            borderRadius: '4px'\n                          }} />\n                        </div>\n                        <span>{selectedDevice.memory}%</span>\n                      </div>\n                    </Descriptions.Item>\n                    {/* <Descriptions.Item label=\"磁盘使用率\">\n                      <div style={{ display: 'flex', alignItems: 'center' }}>\n                        <div style={{\n                          width: '100px',\n                          height: '8px',\n                          background: '#f0f0f0',\n                          borderRadius: '4px',\n                          overflow: 'hidden',\n                          marginRight: '8px'\n                        }}>\n                          <div style={{\n                            width: `${selectedDevice.disk}%`,\n                            height: '100%',\n                            background: getHealthColor(selectedDevice.disk),\n                            borderRadius: '4px'\n                          }} />\n                        </div>\n                        <span>{selectedDevice.disk}%</span>\n                      </div>\n                    </Descriptions.Item> */}\n                  </>\n                )}\n              </Descriptions>\n            ) : (\n              <p style={{ fontSize: '13px' }}>请选择设备查看详细信息</p>\n            )}\n          </InfoCard>\n        </CollapsibleSidebar>\n      </PageContainer>\n    </Spin>\n  );\n};\n\n// 移除了未使用的styles变量\n\n// 添加全局样式\nconst globalStyles = `\n  .device-status-container {\n    display: flex;\n    height: 100%;\n  }\n\n  .device-sidebar {\n    width: 320px;\n    padding: 16px;\n    background-color: #f5f5f5;\n    overflow-y: auto;\n    border-right: 1px solid #e8e8e8;\n  }\n\n  .device-content {\n    flex: 1;\n    padding: 16px;\n    overflow-y: auto;\n  }\n\n  .ant-card-head-title {\n    font-weight: bold;\n    font-size: 16px;\n  }\n`;\n\nexport default DeviceStatus;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AACtI,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,QAAQ,oBAAoB;AACrG,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,yCAAyC;AACxE,OAAOC,KAAK,MAAM,OAAO;;AAGzB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAEzE;AACAjB,OAAO,CAACkB,GAAG,CAAC,CAACjB,QAAQ,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,cAAc,CAAC,CAAC;;AAEzG;AACA;AACA;AACA;AACA;AACA;AACA,MAAMa,aAAa,GAAGZ,MAAM,CAACa,GAAG;AAChC;AACA;AACA;AACA,CAAC;AACD;AAAAC,EAAA,GALMF,aAAa;AAMnB,MAAMG,WAAW,GAAGf,MAAM,CAACa,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMG,YAAY,GAAGhB,MAAM,CAACa,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMI,WAAW,GAAGjB,MAAM,CAACa,GAAG;AAC9B;AACA;AACA;AACA,aAAaK,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACxEF,KAAK,CAACC,aAAa,GAAG,cAAc,GACpCD,KAAK,CAACE,cAAc,GAAG,cAAc,GAAG,OAAO;AACnD;AACA,YAAYF,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACvEF,KAAK,CAACC,aAAa,GAAG,WAAW,GACjCD,KAAK,CAACE,cAAc,GAAG,WAAW,GAAG,GAAG;AAC5C;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfMJ,WAAW;AAgBjB,MAAMK,QAAQ,GAAGtB,MAAM,CAACpB,IAAI,CAAC;AAC7B;AACA,YAAYsC,KAAK,IAAIA,KAAK,CAACK,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAnCMF,QAAQ;AAoCd,MAAMG,gBAAgB,GAAGzB,MAAM,CAACnB,SAAS,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAM6C,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2D,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,QAAQ,CAAC;IACjC6D,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqE,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACuE,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAElD,MAAMyE,kBAAkB,GAAGvE,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMwE,oBAAoB,GAAGxE,MAAM,CAAC,IAAI,CAAC;;EAEzC;EACA,MAAM,CAAC0C,aAAa,EAAE+B,gBAAgB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6C,cAAc,EAAE+B,iBAAiB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC6E,UAAU,EAAEC,aAAa,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+E,WAAW,EAAEC,cAAc,CAAC,GAAGhF,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACiF,aAAa,EAAEC,gBAAgB,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmF,eAAe,EAAEC,kBAAkB,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;;EAE1D;;EAEA;AACF;AACA;EACE,MAAMqF,gBAAgB,GAAG,MAAAA,CAAOC,WAAW,GAAG,IAAI,KAAK;IACrD,IAAI;MACF,IAAIA,WAAW,EAAE;QACflB,cAAc,CAAC,IAAI,CAAC;MACtB;MAEA,MAAMmB,MAAM,GAAGtD,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvEqD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAE9B,MAAMC,QAAQ,GAAG,MAAM/D,KAAK,CAACgE,IAAI,CAAC,GAAGJ,MAAM,mBAAmB,EAAE,CAAC,CAAC,EAAE;QAClEK,OAAO,EAAE,KAAK,CAAC;MACjB,CAAC,CAAC;MAEF,IAAIF,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;QAC1C,MAAM;UAAEC,OAAO;UAAEpC,KAAK,EAAEqC,SAAS;UAAEC;QAAS,CAAC,GAAGP,QAAQ,CAACG,IAAI,CAACA,IAAI;QAElEL,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;UACvBS,IAAI,EAAEF,SAAS,CAACG,KAAK;UACrBC,EAAE,EAAEJ,SAAS,CAACK,MAAM;UACpBC,EAAE,EAAEN,SAAS,CAACO,OAAO;UACrBC,GAAG,EAAER,SAAS,CAACS,IAAI;UACnBC,EAAE,EAAE,GAAGT,QAAQ;QACjB,CAAC,CAAC;;QAEF;QACAzB,cAAc,CAACuB,OAAO,CAAC;QACvBzB,eAAe,CAAC,IAAIqC,IAAI,CAAC,CAAC,CAAC;;QAE3B;QACAC,4BAA4B,CAACb,OAAO,CAAC;QAErClF,OAAO,CAACiF,OAAO,CAAC,YAAYE,SAAS,CAACK,MAAM,OAAOL,SAAS,CAACO,OAAO,UAAUN,QAAQ,KAAK,CAAC;MAC9F,CAAC,MAAM;QACLT,OAAO,CAACqB,KAAK,CAAC,gBAAgB,EAAEnB,QAAQ,CAACG,IAAI,CAAC;QAC9ChF,OAAO,CAACgG,KAAK,CAAC,UAAU,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjChG,OAAO,CAACgG,KAAK,CAAC,aAAaA,KAAK,CAAChG,OAAO,EAAE,CAAC;IAC7C,CAAC,SAAS;MACR,IAAIyE,WAAW,EAAE;QACflB,cAAc,CAAC,KAAK,CAAC;MACvB;IACF;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMwC,4BAA4B,GAAIrC,WAAW,IAAK;IACpDiB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEqB,MAAM,CAACC,IAAI,CAACxC,WAAW,CAAC,CAACyC,MAAM,CAAC;IAEzDxD,UAAU,CAACyD,WAAW,IAAI;MACxBzB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxByB,EAAE,EAAED,WAAW,CAACD,MAAM;QACtBZ,EAAE,EAAEa,WAAW,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACL,MAAM;QACzDV,EAAE,EAAEW,WAAW,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC,CAACL;MACtD,CAAC,CAAC;MAEF,MAAMM,cAAc,GAAGL,WAAW,CAACM,GAAG,CAACC,MAAM,IAAI;QAC/C,MAAMC,UAAU,GAAGlD,WAAW,CAACiD,MAAM,CAACE,EAAE,CAAC;QACzC,IAAID,UAAU,EAAE;UACd;UACA,IAAIE,SAAS,GAAGH,MAAM,CAACH,MAAM;UAC7B,IAAIO,SAAS,GAAGJ,MAAM,CAACK,MAAM;UAE7B,IAAIJ,UAAU,CAACK,MAAM,KAAK,OAAO,EAAE;YACjC;YACAH,SAAS,GAAG,SAAS;YACrBC,SAAS,GAAG,SAAS;YACrBpC,OAAO,CAACC,GAAG,CAAC,MAAM+B,MAAM,CAACO,IAAI,kBAAkB,CAAC;UAClD,CAAC,MAAM,IAAIN,UAAU,CAACO,QAAQ,EAAE;YAC9BL,SAAS,GAAG,QAAQ;YACpBC,SAAS,GAAG,QAAQ;YACpBpC,OAAO,CAACC,GAAG,CAAC,MAAM+B,MAAM,CAACO,IAAI,cAAc,CAAC;UAC9C,CAAC,MAAM;YACLJ,SAAS,GAAG,SAAS;YACrBC,SAAS,GAAG,OAAO;YACnBpC,OAAO,CAACC,GAAG,CAAC,MAAM+B,MAAM,CAACO,IAAI,mBAAmB,CAAC;UACnD;UAEA,OAAO;YACL,GAAGP,MAAM;YACTH,MAAM,EAAEM,SAAS;YACjBE,MAAM,EAAED,SAAS;YACjBvD,YAAY,EAAEoD,UAAU,CAACQ,SAAS;YAClCC,YAAY,EAAET,UAAU,CAACS,YAAY;YACrCC,UAAU,EAAEV,UAAU,CAACO,QAAQ,GAAG,QAAQ,GAAG;UAC/C,CAAC;QACH,CAAC,MAAM;UACL;UACAxC,OAAO,CAACC,GAAG,CAAC,MAAM+B,MAAM,CAACO,IAAI,mBAAmB,CAAC;UACjD,OAAOP,MAAM;QACf;MACF,CAAC,CAAC;MAEFhC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxByB,EAAE,EAAEI,cAAc,CAACN,MAAM;QACzBZ,EAAE,EAAEkB,cAAc,CAACH,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACL,MAAM;QAC5DV,EAAE,EAAEgB,cAAc,CAACH,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC,CAACL;MACzD,CAAC,CAAC;;MAEF;MACA5B,kBAAkB,CAACkC,cAAc,CAAC;;MAElC;MACA9B,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC2C,YAAY,CAACd,cAAc,CAAC;MAC5Be,WAAW,CAACf,cAAc,CAAC;MAE3B,OAAOA,cAAc;IACvB,CAAC,CAAC;EACJ,CAAC;;EAED;AACF;AACA;EACE,MAAMe,WAAW,GAAIC,WAAW,IAAK;IACnC,MAAMC,QAAQ,GAAG;MACf1E,YAAY,EAAEyE,WAAW,CAACtB,MAAM;MAChClD,aAAa,EAAEwE,WAAW,CAACnB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACL,MAAM;MACpEjD,cAAc,EAAEuE,WAAW,CAACnB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACL,MAAM;MACrEhD,aAAa,EAAEsE,WAAW,CAACnB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACS,MAAM,KAAK,QAAQ,CAAC,CAACb,MAAM;MACpE/C,cAAc,EAAEqE,WAAW,CAACnB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACS,MAAM,KAAK,SAAS,CAAC,CAACb,MAAM;MACtE9C,YAAY,EAAEoE,WAAW,CAACnB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACS,MAAM,KAAK,OAAO,CAAC,CAACb;IAC9D,CAAC;IACDpD,QAAQ,CAAC2E,QAAQ,CAAC;EACpB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFlF,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMiC,MAAM,GAAGtD,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMuD,QAAQ,GAAG,MAAM/D,KAAK,CAAC8G,GAAG,CAAC,GAAGlD,MAAM,cAAc,CAAC;MAEzD,IAAIG,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,IAAIJ,QAAQ,CAACG,IAAI,CAACA,IAAI,EAAE;QAChE,MAAMyC,WAAW,GAAG5C,QAAQ,CAACG,IAAI,CAACA,IAAI;;QAEtC;QACA,MAAM6C,gBAAgB,GAAGJ,WAAW,CAACf,GAAG,CAACC,MAAM,KAAK;UAClD,GAAGA,MAAM;UACTmB,EAAE,EAAEnB,MAAM,CAACoB,SAAS;UAAE;UACtBC,GAAG,EAAErB,MAAM,CAACqB,GAAG;UACfhB,MAAM,EAAE,QAAQ;UAAE;UAClBiB,GAAG,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;UACpCC,MAAM,EAAEH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;UACvCE,IAAI,EAAEJ,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC;UACrCG,UAAU,EAAE,IAAIzC,IAAI,CAACA,IAAI,CAAC0C,GAAG,CAAC,CAAC,GAAGN,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAACK,cAAc,CAAC,CAAC;UACvFjF,YAAY,EAAE,IAAI;UAClB6D,YAAY,EAAE,IAAI;UAClBC,UAAU,EAAE;QACd,CAAC,CAAC,CAAC;QAEH3E,UAAU,CAACkF,gBAAgB,CAAC;QAC5BtD,kBAAkB,CAACsD,gBAAgB,CAAC,CAAC,CAAC;QACtCN,YAAY,CAACM,gBAAgB,CAAC;QAC9BL,WAAW,CAACK,gBAAgB,CAAC;;QAE7B;QACAa,UAAU,CAAC,MAAM;UACflE,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;QAC3B,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLG,OAAO,CAACqB,KAAK,CAAC,UAAU,EAAEnB,QAAQ,CAACG,IAAI,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC,CAAC,SAAS;MACRvD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkG,YAAY,GAAGtJ,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMuJ,cAAc,GAAGvJ,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMkI,YAAY,GAAIE,WAAW,IAAK;IACpC9C,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBiE,IAAI,EAAEpB,WAAW,CAACtB,MAAM;MACxB2C,IAAI,EAAErB,WAAW,CAACnB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACL,MAAM;MAC3D4C,IAAI,EAAEtB,WAAW,CAACnB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC,CAACL;IACxD,CAAC,CAAC;;IAEF;IACA,MAAM6C,QAAQ,GAAG,CAAC,CAAC;IACnBvB,WAAW,CAACwB,OAAO,CAACtC,MAAM,IAAI;MAC5B,MAAMuC,IAAI,GAAGC,aAAa,CAACxC,MAAM,CAACuC,IAAI,CAAC,IAAIvC,MAAM,CAACuC,IAAI;MACtDF,QAAQ,CAACE,IAAI,CAAC,GAAG,CAACF,QAAQ,CAACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5C,CAAC,CAAC;;IAEF;IACA,MAAME,UAAU,GAAG,CAAC,CAAC;IACrB3B,WAAW,CAACwB,OAAO,CAACtC,MAAM,IAAI;MAC5B,MAAMH,MAAM,GAAG6C,eAAe,CAAC1C,MAAM,CAACH,MAAM,CAAC,IAAIG,MAAM,CAACH,MAAM;MAC9D4C,UAAU,CAAC5C,MAAM,CAAC,GAAG,CAAC4C,UAAU,CAAC5C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;IACpD,CAAC,CAAC;IAEF7B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEwE,UAAU,CAAC;;IAElC;IACA,MAAME,YAAY,GAAG,CACnB,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS;IAAE;IACX,SAAS;IAAG;IACZ,SAAS,CAAC;IAAA,CACX;;IAED;IACA,IAAI1F,kBAAkB,CAAC2F,OAAO,EAAE;MAC9B;MACA,IAAIC,SAAS,GAAGb,YAAY,CAACY,OAAO;MACpC,IAAI,CAACC,SAAS,EAAE;QACdA,SAAS,GAAGnJ,OAAO,CAACoJ,IAAI,CAAC7F,kBAAkB,CAAC2F,OAAO,CAAC;QACpDZ,YAAY,CAACY,OAAO,GAAGC,SAAS;MAClC;MAEAA,SAAS,CAACE,SAAS,CAAC;QAClBC,KAAK,EAAEL,YAAY;QACnBM,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,0BAA0B;UACrCC,eAAe,EAAE,oBAAoB;UACrCC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE;YACTN,KAAK,EAAE;UACT;QACF,CAAC;QACDO,MAAM,EAAE;UACNC,MAAM,EAAE,YAAY;UACpBC,MAAM,EAAE,CAAC;UACTC,IAAI,EAAE,QAAQ;UACdC,SAAS,EAAE,EAAE;UACbC,UAAU,EAAE,EAAE;UACdN,SAAS,EAAE;YACTO,QAAQ,EAAE;UACZ,CAAC;UACDC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;QACzB,CAAC;QACDC,MAAM,EAAE,CAAC;UACPxD,IAAI,EAAE,MAAM;UACZgC,IAAI,EAAE,KAAK;UACXyB,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,iBAAiB,EAAE,IAAI;UACvBC,SAAS,EAAE;YACTd,YAAY,EAAE,CAAC;YACfe,WAAW,EAAE,MAAM;YACnBC,WAAW,EAAE;UACf,CAAC;UACDC,KAAK,EAAE;YACLC,IAAI,EAAE,IAAI;YACVpB,SAAS,EAAE,MAAM;YACjBqB,QAAQ,EAAE,QAAQ;YAClBX,QAAQ,EAAE,EAAE;YACZY,UAAU,EAAE,MAAM;YAClBzB,KAAK,EAAE,MAAM;YACb0B,eAAe,EAAE,0BAA0B;YAC3CC,eAAe,EAAE,CAAC;YAClBC,UAAU,EAAE;UACd,CAAC;UACDC,QAAQ,EAAE;YACRP,KAAK,EAAE;cACLC,IAAI,EAAE,IAAI;cACVV,QAAQ,EAAE,EAAE;cACZY,UAAU,EAAE;YACd,CAAC;YACDN,SAAS,EAAE;cACTW,UAAU,EAAE,EAAE;cACdC,aAAa,EAAE,CAAC;cAChBC,WAAW,EAAE;YACf;UACF,CAAC;UACDC,SAAS,EAAE;YACTV,IAAI,EAAE;UACR,CAAC;UACDlG,IAAI,EAAEiB,MAAM,CAAC4F,OAAO,CAAC7C,QAAQ,CAAC,CAACtC,GAAG,CAAC,CAAC,CAACQ,IAAI,EAAE4E,KAAK,CAAC,MAAM;YAAE5E,IAAI;YAAE4E;UAAM,CAAC,CAAC;QACzE,CAAC;MACH,CAAC,CAAC;;MAEF;IACF;;IAEA;IACA,IAAIjI,oBAAoB,CAAC0F,OAAO,EAAE;MAChC;MACA,IAAIwC,WAAW,GAAGnD,cAAc,CAACW,OAAO;MACxC,IAAI,CAACwC,WAAW,EAAE;QAChBA,WAAW,GAAG1L,OAAO,CAACoJ,IAAI,CAAC5F,oBAAoB,CAAC0F,OAAO,CAAC;QACxDX,cAAc,CAACW,OAAO,GAAGwC,WAAW;MACtC;MAEAA,WAAW,CAACrC,SAAS,CAAC;QACpBC,KAAK,EAAEL,YAAY;QACnBM,OAAO,EAAE;UACPC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,0BAA0B;UACrCC,eAAe,EAAE,oBAAoB;UACrCC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE;YACTN,KAAK,EAAE;UACT;QACF,CAAC;QACDO,MAAM,EAAE;UACNC,MAAM,EAAE,YAAY;UACpBC,MAAM,EAAE,CAAC;UACTC,IAAI,EAAE,QAAQ;UACdC,SAAS,EAAE,EAAE;UACbC,UAAU,EAAE,EAAE;UACdN,SAAS,EAAE;YACTO,QAAQ,EAAE;UACZ,CAAC;UACDC,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;QACzB,CAAC;QACDC,MAAM,EAAE,CAAC;UACPxD,IAAI,EAAE,MAAM;UACZgC,IAAI,EAAE,KAAK;UACXyB,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;UACtBC,iBAAiB,EAAE,IAAI;UACvBC,SAAS,EAAE;YACTd,YAAY,EAAE,CAAC;YACfe,WAAW,EAAE,MAAM;YACnBC,WAAW,EAAE;UACf,CAAC;UACDC,KAAK,EAAE;YACLC,IAAI,EAAE,IAAI;YACVpB,SAAS,EAAE,MAAM;YACjBqB,QAAQ,EAAE,QAAQ;YAClBX,QAAQ,EAAE,EAAE;YACZY,UAAU,EAAE,MAAM;YAClBzB,KAAK,EAAE,MAAM;YACb0B,eAAe,EAAE,0BAA0B;YAC3CC,eAAe,EAAE,CAAC;YAClBC,UAAU,EAAE;UACd,CAAC;UACDC,QAAQ,EAAE;YACRP,KAAK,EAAE;cACLC,IAAI,EAAE,IAAI;cACVV,QAAQ,EAAE,EAAE;cACZY,UAAU,EAAE;YACd,CAAC;YACDN,SAAS,EAAE;cACTW,UAAU,EAAE,EAAE;cACdC,aAAa,EAAE,CAAC;cAChBC,WAAW,EAAE;YACf;UACF,CAAC;UACDC,SAAS,EAAE;YACTV,IAAI,EAAE;UACR,CAAC;UACDlG,IAAI,EAAEiB,MAAM,CAAC4F,OAAO,CAACzC,UAAU,CAAC,CAAC1C,GAAG,CAAC,CAAC,CAACQ,IAAI,EAAE4E,KAAK,CAAC,MAAM;YAAE5E,IAAI;YAAE4E;UAAM,CAAC,CAAC;QAC3E,CAAC;MACH,CAAC,CAAC;;MAEF;IACF;EACF,CAAC;;EAED;EACA,MAAM3C,aAAa,GAAG;IACpB6C,MAAM,EAAE,KAAK;IACbC,YAAY,EAAE,OAAO;IACrBC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,KAAK;IACVC,cAAc,EAAE,QAAQ;IACxBC,GAAG,EAAE;EACP,CAAC;;EAED;EACA,MAAMhD,eAAe,GAAG;IACtB,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;IACf,SAAS,EAAE,IAAI;IACf,OAAO,EAAE,IAAI;IACb,aAAa,EAAE;EACjB,CAAC;;EAED;EACA,MAAMiD,uBAAuB,GAAIR,KAAK,IAAK;IACzC7H,aAAa,CAAC6H,KAAK,CAAC;IACpB3H,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;;IAEtB;IACA,IAAI2H,KAAK,EAAE;MACT,MAAMS,YAAY,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC9J,OAAO,CAACgE,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACmF,KAAK,CAAC,CAAC,CAAC,CAAC;MACvEzH,gBAAgB,CAACkI,YAAY,CAACjG,MAAM,CAACmG,OAAO,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC,MAAM;MACLrI,gBAAgB,CAAC,EAAE,CAAC;IACtB;EACF,CAAC;EAED,MAAMsI,wBAAwB,GAAIb,KAAK,IAAK;IAC1C3H,cAAc,CAAC2H,KAAK,CAAC;EACvB,CAAC;EAED,MAAMc,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAAC5I,UAAU,IAAI,CAACE,WAAW,EAAE;MAC/B;MACAK,kBAAkB,CAAC7B,OAAO,CAAC;IAC7B,CAAC,MAAM;MACL;MACA,MAAMmK,QAAQ,GAAGnK,OAAO,CAAC4D,MAAM,CAACK,MAAM,IAAIA,MAAM,CAAC3C,UAAU,CAAC,KAAKE,WAAW,CAAC;MAC7EK,kBAAkB,CAACsI,QAAQ,CAAC;IAC9B;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B7I,aAAa,CAAC,IAAI,CAAC;IACnBE,cAAc,CAAC,IAAI,CAAC;IACpBE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,kBAAkB,CAAC7B,OAAO,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMqK,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAAC/I,UAAU,EAAE,OAAO,EAAE;IAE1B,OAAOI,aAAa,CAACsC,GAAG,CAACsG,MAAM,IAAI;MACjC;MACA,IAAIhJ,UAAU,KAAK,MAAM,EAAE;QACzB,OAAO;UACL8H,KAAK,EAAEkB,MAAM;UACb/B,KAAK,EAAE9B,aAAa,CAAC6D,MAAM,CAAC,IAAIA;QAClC,CAAC;MACH;MACA,OAAO;QACLlB,KAAK,EAAEkB,MAAM;QACb/B,KAAK,EAAE+B;MACT,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;;EAED;;EAEA;EACA5N,SAAS,CAAC,MAAM;IACd,IAAI6N,UAAU;;IAEd;IACAA,UAAU,GAAGC,WAAW,CAAC,MAAM;MAC7BvI,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACpCJ,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC3B,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;;IAEhB,OAAO,MAAM;MACX,IAAIyI,UAAU,EAAE;QACdE,aAAa,CAACF,UAAU,CAAC;MAC3B;IACF,CAAC;EACH,CAAC,EAAE,CAACzI,gBAAgB,CAAC,CAAC;;EAEtB;EACApF,SAAS,CAAC,MAAM;IACduI,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvI,SAAS,CAAC,MAAM;IACd,IAAIkF,eAAe,CAAC6B,MAAM,GAAG,CAAC,IAAI,CAACvD,cAAc,EAAE;MACjDC,iBAAiB,CAACyB,eAAe,CAAC,CAAC,CAAC,CAAC;MACrCK,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEN,eAAe,CAAC,CAAC,CAAC,CAAC4C,IAAI,CAAC;IACrD;EACF,CAAC,EAAE,CAAC5C,eAAe,EAAE1B,cAAc,CAAC,CAAC;;EAErC;EACA,MAAMwK,kBAAkB,GAAIzG,MAAM,IAAK;IACrC9D,iBAAiB,CAAC8D,MAAM,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM0G,aAAa,GAAG,CACpB;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnB5M,OAAA;MAAA6M,QAAA,eACE7M,OAAA;QAAK8M,KAAK,EAAE;UAAEtD,QAAQ,EAAE,EAAE;UAAEY,UAAU,EAAE;QAAI,CAAE;QAAAyC,QAAA,EAAEF;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAET,CAAC,EACD;IACEZ,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEA,CAAClH,MAAM,EAAEoH,MAAM,kBACrB5M,OAAA;MAAA6M,QAAA,EACGD,MAAM,CAACtG,UAAU,iBAChBtG,OAAA;QAAK8M,KAAK,EAAE;UAAEtD,QAAQ,EAAE,EAAE;UAAE2D,SAAS,EAAE;QAAE,CAAE;QAAAN,QAAA,eACzC7M,OAAA,CAAClB,KAAK;UACJ0G,MAAM,EAAEoH,MAAM,CAACtG,UAAU,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;UAC7DqG,IAAI,EAAEC,MAAM,CAACtG,UAAU,KAAK,QAAQ,GAAG,IAAI,GAAG;QAAK;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAET,CAAC,EACD;IACEZ,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE1G,MAAM,IAAI;MAChB,IAAI2C,KAAK,GAAG,OAAO;MACnB,IAAIgE,IAAI,GAAG,IAAI;MAEf,IAAI3G,MAAM,KAAK,SAAS,EAAE;QACxB2C,KAAK,GAAG,QAAQ;QAChBgE,IAAI,GAAG,IAAI;MACb,CAAC,MAAM,IAAI3G,MAAM,KAAK,OAAO,EAAE;QAC7B2C,KAAK,GAAG,KAAK;QACbgE,IAAI,GAAG,IAAI;MACb;MAEA,oBAAO3M,OAAA,CAACjB,GAAG;QAAC4J,KAAK,EAAEA,KAAM;QAAAkE,QAAA,EAAEF;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACxC;EACF,CAAC,CACF;;EAED;EACA,MAAME,cAAc,GAAItC,KAAK,IAAK;IAChC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACnC,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA1M,SAAS,CAAC,MAAM;IACd,MAAMiP,YAAY,GAAGA,CAAA,KAAM;MAAA,IAAAC,qBAAA,EAAAC,qBAAA;MACzB,CAAAD,qBAAA,GAAA3F,YAAY,CAACY,OAAO,cAAA+E,qBAAA,uBAApBA,qBAAA,CAAsBE,MAAM,CAAC,CAAC;MAC9B,CAAAD,qBAAA,GAAA3F,cAAc,CAACW,OAAO,cAAAgF,qBAAA,uBAAtBA,qBAAA,CAAwBC,MAAM,CAAC,CAAC;IAClC,CAAC;IAEDC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEL,YAAY,CAAC;IAE/C,OAAO,MAAM;MACXI,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;MAClD;MACA,IAAI1F,YAAY,CAACY,OAAO,EAAE;QACxBZ,YAAY,CAACY,OAAO,CAACqF,OAAO,CAAC,CAAC;QAC9BjG,YAAY,CAACY,OAAO,GAAG,IAAI;MAC7B;MACA,IAAIX,cAAc,CAACW,OAAO,EAAE;QAC1BX,cAAc,CAACW,OAAO,CAACqF,OAAO,CAAC,CAAC;QAChChG,cAAc,CAACW,OAAO,GAAG,IAAI;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnK,SAAS,CAAC,MAAM;IACd,MAAMyP,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IACpDF,YAAY,CAACG,SAAS,GAAGC,YAAY;IACrCH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,YAAY,CAAC;IAEvC,OAAO,MAAM;MACXC,QAAQ,CAACI,IAAI,CAACE,WAAW,CAACP,YAAY,CAAC;IACzC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE7N,OAAA,CAACnB,IAAI;IAACwP,QAAQ,EAAE7M,OAAQ;IAAC8M,GAAG,EAAC,uBAAQ;IAAAzB,QAAA,eACnC7M,OAAA,CAACQ,aAAa;MAAAqM,QAAA,gBAEZ7M,OAAA,CAACH,kBAAkB;QACjBsK,QAAQ,EAAC,MAAM;QACfoE,SAAS,EAAExN,aAAc;QACzByN,UAAU,EAAEA,CAAA,KAAM1L,gBAAgB,CAAC,CAAC/B,aAAa,CAAE;QAAA8L,QAAA,gBAGnD7M,OAAA,CAACkB,QAAQ;UAACoL,KAAK,EAAC,kDAAU;UAACmC,QAAQ,EAAE,KAAM;UAACtN,MAAM,EAAC,OAAO;UAAA0L,QAAA,eACxD7M,OAAA;YAAK8M,KAAK,EAAE;cAAEL,KAAK,EAAE,MAAM;cAAGiC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,cAAc,EAAE;YAAS,CAAE;YAAA/B,QAAA,eAC9F7M,OAAA;cACE6O,GAAG,EAAE,GAAG1O,QAAQ,0BAA2B;cAC3C2O,GAAG,EAAC,kDAAU;cACdhC,KAAK,EAAE;gBAAEiC,QAAQ,EAAE,MAAM;gBAAEC,SAAS,EAAE,MAAM;gBAAEC,SAAS,EAAE,SAAS;gBAAEjG,YAAY,EAAE,CAAC;gBAAEkG,SAAS,EAAE;cAA6B;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGXlN,OAAA,CAACkB,QAAQ;UAACoL,KAAK,EAAC,sCAAQ;UAACmC,QAAQ,EAAE,KAAM;UAACtN,MAAM,EAAC,mBAAmB;UAAA0L,QAAA,eAClE7M,OAAA;YACEmP,GAAG,EAAEvM,kBAAmB;YACxBkK,KAAK,EAAE;cACL3L,MAAM,EAAE,MAAM;cACdsL,KAAK,EAAE;YACT;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGXlN,OAAA,CAACkB,QAAQ;UAACoL,KAAK,EAAC,sCAAQ;UAACmC,QAAQ,EAAE,KAAM;UAACtN,MAAM,EAAC,mBAAmB;UAAA0L,QAAA,eAClE7M,OAAA;YACEmP,GAAG,EAAEtM,oBAAqB;YAC1BiK,KAAK,EAAE;cACL3L,MAAM,EAAE,MAAM;cACdsL,KAAK,EAAE;YACT;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGrBlN,OAAA,CAACa,WAAW;QAACE,aAAa,EAAEA,aAAc;QAACC,cAAc,EAAEA;MAAe;QAAA+L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC,eAGdlN,OAAA,CAACH,kBAAkB;QACjBsK,QAAQ,EAAC,OAAO;QAChBoE,SAAS,EAAEvN,cAAe;QAC1BwN,UAAU,EAAEA,CAAA,KAAMzL,iBAAiB,CAAC,CAAC/B,cAAc,CAAE;QAAA6L,QAAA,gBAGrD7M,OAAA,CAACkB,QAAQ;UAACoL,KAAK,EAAC,0BAAM;UAACmC,QAAQ,EAAE,KAAM;UAACtN,MAAM,EAAC,KAAK;UAAA0L,QAAA,eAClD7M,OAAA;YAAK8M,KAAK,EAAE;cACV4B,OAAO,EAAE,MAAM;cACfU,aAAa,EAAE,QAAQ;cACvBjO,MAAM,EAAE;YACV,CAAE;YAAA0L,QAAA,gBAEA7M,OAAA;cAAK8M,KAAK,EAAE;gBACVuC,YAAY,EAAE,CAAC;gBACf5F,OAAO,EAAE,SAAS;gBAClB6F,YAAY,EAAE,mBAAmB;gBACjCvG,eAAe,EAAE,SAAS;gBAC1BC,YAAY,EAAE;cAChB,CAAE;cAAA6D,QAAA,eACA7M,OAAA;gBAAK8M,KAAK,EAAE;kBAAEtD,QAAQ,EAAE,EAAE;kBAAEb,KAAK,EAAE,MAAM;kBAAE4G,SAAS,EAAE;gBAAS,CAAE;gBAAA1C,QAAA,EAC9DrK,YAAY,gBACXxC,OAAA;kBAAA6M,QAAA,GAAM,sGACqB,EAACrK,YAAY,CAACgN,kBAAkB,CAAC,CAAC;gBAAA;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD,CAAC,gBAEPlN,OAAA;kBAAA6M,QAAA,EAAM;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACzC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlN,OAAA;cAAK8M,KAAK,EAAE;gBAAE2C,IAAI,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAA7C,QAAA,eACxC7M,OAAA,CAACrB,KAAK;gBACJgR,UAAU,EAAErM,eAAgB;gBAC5BsM,OAAO,EAAEvD,aAAc;gBACvBwD,MAAM,EAAC,IAAI;gBACXC,UAAU,EAAE,KAAM;gBAClBC,IAAI,EAAC,OAAO;gBACZC,MAAM,EAAE;kBAAEC,CAAC,EAAE;gBAAK,CAAE;gBACpBC,KAAK,EAAGtD,MAAM,KAAM;kBAClBuD,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAACQ,MAAM,CAAC;kBACzCE,KAAK,EAAE;oBACLsD,MAAM,EAAE,SAAS;oBACjBC,UAAU,EAAE,CAAAzO,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEiE,EAAE,MAAK+G,MAAM,CAAC/G,EAAE,GAAG,SAAS,GAAG,aAAa;oBACxE2D,QAAQ,EAAE,MAAM;oBAChBC,OAAO,EAAE;kBACX;gBACF,CAAC;cAAE;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNlN,OAAA;cAAK8M,KAAK,EAAE;gBACVK,SAAS,EAAE,KAAK;gBAChB1D,OAAO,EAAE,OAAO;gBAChB6G,SAAS,EAAE,mBAAmB;gBAC9B5B,OAAO,EAAE,MAAM;gBACfE,cAAc,EAAE,eAAe;gBAC/BD,UAAU,EAAE,QAAQ;gBACpB4B,QAAQ,EAAE,MAAM;gBAChBC,GAAG,EAAE;cACP,CAAE;cAAA3D,QAAA,gBACA7M,OAAA,CAACZ,KAAK;gBAAC2Q,IAAI,EAAC,OAAO;gBAACjD,KAAK,EAAE;kBAAEyD,QAAQ,EAAE;gBAAO,CAAE;gBAAA1D,QAAA,gBAC9C7M,OAAA,CAACd,MAAM;kBACLuR,WAAW,EAAC,0BAAM;kBAClB3D,KAAK,EAAE;oBAAEL,KAAK,EAAE;kBAAQ,CAAE;kBAC1BsD,IAAI,EAAC,OAAO;kBACZjF,KAAK,EAAE9H,UAAW;kBAClB0N,QAAQ,EAAEpF,uBAAwB;kBAClCqF,UAAU;kBACVC,OAAO,EAAE,CACP;oBAAE9F,KAAK,EAAE,MAAM;oBAAEb,KAAK,EAAE;kBAAO,CAAC,EAChC;oBAAEa,KAAK,EAAE,UAAU;oBAAEb,KAAK,EAAE;kBAAK,CAAC;gBAClC;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFlN,OAAA,CAACd,MAAM;kBACLuR,WAAW,EAAC,oBAAK;kBACjB3D,KAAK,EAAE;oBAAEL,KAAK,EAAE;kBAAQ,CAAE;kBAC1BsD,IAAI,EAAC,OAAO;kBACZjF,KAAK,EAAE5H,WAAY;kBACnBwN,QAAQ,EAAE/E,wBAAyB;kBACnCkF,QAAQ,EAAE,CAAC7N,UAAU,IAAII,aAAa,CAAC+B,MAAM,KAAK,CAAE;kBACpDwL,UAAU;kBACVC,OAAO,EAAE7E,sBAAsB,CAAC;gBAAE;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACFlN,OAAA,CAACb,MAAM;kBAAC+I,IAAI,EAAC,SAAS;kBAAC6H,IAAI,EAAC,OAAO;kBAACI,OAAO,EAAEvE,kBAAmB;kBAAAiB,QAAA,EAAC;gBAEjE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlN,OAAA,CAACb,MAAM;kBAAC4Q,IAAI,EAAC,OAAO;kBAACI,OAAO,EAAErE,iBAAkB;kBAAAe,QAAA,EAAC;gBAEjD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACRlN,OAAA;gBAAK8M,KAAK,EAAE;kBAAEtD,QAAQ,EAAE,MAAM;kBAAEb,KAAK,EAAE;gBAAO,CAAE;gBAAAkE,QAAA,GAC7CvJ,eAAe,CAAC6B,MAAM,EAAC,KAAG,EAACzD,OAAO,CAACyD,MAAM,EAAC,qBAC7C;cAAA;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGXlN,OAAA,CAACkB,QAAQ;UAACoL,KAAK,EAAC,sCAAQ;UAACmC,QAAQ,EAAE,KAAM;UAACtN,MAAM,EAAC,KAAK;UAAA0L,QAAA,EACnDjL,cAAc,gBACb5B,OAAA,CAACpB,YAAY;YACX6P,QAAQ;YACRqC,MAAM,EAAE,CAAE;YACVf,IAAI,EAAC,OAAO;YACZgB,MAAM,EAAE;cACN9G,KAAK,EAAE;gBAAET,QAAQ,EAAE,MAAM;gBAAEC,OAAO,EAAE;cAAU,CAAC;cAC/CuH,OAAO,EAAE;gBAAExH,QAAQ,EAAE,MAAM;gBAAEC,OAAO,EAAE;cAAU;YAClD,CAAE;YAAAoD,QAAA,gBAEF7M,OAAA,CAACpB,YAAY,CAACqS,IAAI;cAAChH,KAAK,EAAC,0BAAM;cAAA4C,QAAA,EAAEjL,cAAc,CAACsE;YAAI;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eACzElN,OAAA,CAACpB,YAAY,CAACqS,IAAI;cAAChH,KAAK,EAAC,0BAAM;cAAA4C,QAAA,EAC5B1E,aAAa,CAACvG,cAAc,CAACsG,IAAI,CAAC,IAAItG,cAAc,CAACsG;YAAI;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACpBlN,OAAA,CAACpB,YAAY,CAACqS,IAAI;cAAChH,KAAK,EAAC,cAAI;cAAA4C,QAAA,eAC3B7M,OAAA,CAAClB,KAAK;gBACJ0G,MAAM,EAAE5D,cAAc,CAAC4D,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;gBACjEmH,IAAI,EAAE/K,cAAc,CAAC4D,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;cAAK;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,eACpBlN,OAAA,CAACpB,YAAY,CAACqS,IAAI;cAAChH,KAAK,EAAC,cAAI;cAAA4C,QAAA,GAAEjL,cAAc,CAACsP,QAAQ,EAAC,GAAC,EAACtP,cAAc,CAACuP,QAAQ,GAAG,IAAIvP,cAAc,CAACuP,QAAQ,GAAG,GAAG,EAAE;YAAA;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC3IlN,OAAA,CAACpB,YAAY,CAACqS,IAAI;cAAChH,KAAK,EAAC,gBAAM;cAAA4C,QAAA,GAC5BjL,cAAc,CAACkF,EAAE,IAAI,KAAK,EAC1BlF,cAAc,CAACkF,EAAE,IAAIlF,cAAc,CAACyE,YAAY,iBAC/CrG,OAAA;gBAAM8M,KAAK,EAAE;kBAAEsE,UAAU,EAAE,CAAC;kBAAE5H,QAAQ,EAAE,EAAE;kBAAEb,KAAK,EAAE;gBAAO,CAAE;gBAAAkE,QAAA,GAAC,GAC1D,EAACjL,cAAc,CAACyE,YAAY,EAAC,KAChC;cAAA;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACgB,CAAC,EACnBtL,cAAc,CAACsG,IAAI,KAAK,KAAK,iBAAKlI,OAAA,CAACpB,YAAY,CAACqS,IAAI;cAAChH,KAAK,EAAC,iBAAO;cAAA4C,QAAA,EAAEjL,cAAc,CAACoF;YAAG;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAE,eAC7GlN,OAAA,CAACpB,YAAY,CAACqS,IAAI;cAAChH,KAAK,EAAC,0BAAM;cAAA4C,QAAA,eAC7B7M,OAAA;gBAAK8M,KAAK,EAAE;kBAAE4B,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAA9B,QAAA,gBACpD7M,OAAA,CAAClB,KAAK;kBACJ0G,MAAM,EAAE5D,cAAc,CAAC0E,UAAU,KAAK,QAAQ,GAAG,SAAS,GAAG1E,cAAc,CAAC0E,UAAU,KAAK,SAAS,GAAG,OAAO,GAAG,SAAU;kBAC3HqG,IAAI,EACF/K,cAAc,CAAC0E,UAAU,KAAK,QAAQ,GAAG,IAAI,GAC7C1E,cAAc,CAAC0E,UAAU,KAAK,SAAS,GAAG,IAAI,GAC9C1E,cAAc,CAACkF,EAAE,GAAG,KAAK,GAAG;gBAC7B;kBAAAiG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,EACDtL,cAAc,CAACY,YAAY,iBAC1BxC,OAAA;kBAAM8M,KAAK,EAAE;oBAAEsE,UAAU,EAAE,CAAC;oBAAE5H,QAAQ,EAAE,EAAE;oBAAEb,KAAK,EAAE;kBAAO,CAAE;kBAAAkE,QAAA,EACzD,IAAI/H,IAAI,CAAClD,cAAc,CAACY,YAAY,CAAC,CAACiF,cAAc,CAAC;gBAAC;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC,eACpBlN,OAAA,CAACpB,YAAY,CAACqS,IAAI;cAAChH,KAAK,EAAC,sCAAQ;cAAA4C,QAAA,EAAEjL,cAAc,CAAC2F;YAAU;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,EAChFtL,cAAc,CAAC4D,MAAM,KAAK,QAAQ,iBACjCxF,OAAA,CAAAE,SAAA;cAAA2M,QAAA,gBACE7M,OAAA,CAACpB,YAAY,CAACqS,IAAI;gBAAChH,KAAK,EAAC,uBAAQ;gBAAA4C,QAAA,eAC/B7M,OAAA;kBAAK8M,KAAK,EAAE;oBAAE4B,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAA9B,QAAA,gBACpD7M,OAAA;oBAAK8M,KAAK,EAAE;sBACVL,KAAK,EAAE,OAAO;sBACdtL,MAAM,EAAE,KAAK;sBACbkP,UAAU,EAAE,SAAS;sBACrBrH,YAAY,EAAE,KAAK;sBACnB0G,QAAQ,EAAE,QAAQ;sBAClB2B,WAAW,EAAE;oBACf,CAAE;oBAAAxE,QAAA,eACA7M,OAAA;sBAAK8M,KAAK,EAAE;wBACVL,KAAK,EAAE,GAAG7K,cAAc,CAACqF,GAAG,GAAG;wBAC/B9F,MAAM,EAAE,MAAM;wBACdkP,UAAU,EAAEjD,cAAc,CAACxL,cAAc,CAACqF,GAAG,CAAC;wBAC9C+B,YAAY,EAAE;sBAChB;oBAAE;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNlN,OAAA;oBAAA6M,QAAA,GAAOjL,cAAc,CAACqF,GAAG,EAAC,GAAC;kBAAA;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACW,CAAC,eACpBlN,OAAA,CAACpB,YAAY,CAACqS,IAAI;gBAAChH,KAAK,EAAC,gCAAO;gBAAA4C,QAAA,eAC9B7M,OAAA;kBAAK8M,KAAK,EAAE;oBAAE4B,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAA9B,QAAA,gBACpD7M,OAAA;oBAAK8M,KAAK,EAAE;sBACVL,KAAK,EAAE,OAAO;sBACdtL,MAAM,EAAE,KAAK;sBACbkP,UAAU,EAAE,SAAS;sBACrBrH,YAAY,EAAE,KAAK;sBACnB0G,QAAQ,EAAE,QAAQ;sBAClB2B,WAAW,EAAE;oBACf,CAAE;oBAAAxE,QAAA,eACA7M,OAAA;sBAAK8M,KAAK,EAAE;wBACVL,KAAK,EAAE,GAAG7K,cAAc,CAACyF,MAAM,GAAG;wBAClClG,MAAM,EAAE,MAAM;wBACdkP,UAAU,EAAEjD,cAAc,CAACxL,cAAc,CAACyF,MAAM,CAAC;wBACjD2B,YAAY,EAAE;sBAChB;oBAAE;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACNlN,OAAA;oBAAA6M,QAAA,GAAOjL,cAAc,CAACyF,MAAM,EAAC,GAAC;kBAAA;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACW,CAAC;YAAA,eAqBpB,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACW,CAAC,gBAEflN,OAAA;YAAG8M,KAAK,EAAE;cAAEtD,QAAQ,EAAE;YAAO,CAAE;YAAAqD,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC/C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEX,CAAC;;AAED;;AAEA;AAAA3L,EAAA,CAp3BMD,YAAY;AAAAgQ,GAAA,GAAZhQ,YAAY;AAq3BlB,MAAM2M,YAAY,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,eAAe3M,YAAY;AAAC,IAAAZ,EAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAkQ,GAAA;AAAAC,YAAA,CAAA7Q,EAAA;AAAA6Q,YAAA,CAAAtQ,GAAA;AAAAsQ,YAAA,CAAAnQ,GAAA;AAAAmQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}