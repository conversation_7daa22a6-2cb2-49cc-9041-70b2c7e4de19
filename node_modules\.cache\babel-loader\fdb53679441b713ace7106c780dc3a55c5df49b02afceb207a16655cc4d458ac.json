{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\RoadMonitoring.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n// src/pages/RoadMonitoring.jsx\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Card, List, Descriptions, Spin, Badge, Row, Col } from 'antd';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport devicesData from '../data/devices.json';\nimport { FullscreenOutlined } from '@ant-design/icons';\n\n// 添加立即打印，检查导入是否成功\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconsole.log('DevicesData import check:', devicesData);\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\n_c = PageContainer;\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 24px' : props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 0' : props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\n_c2 = MainContent;\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 视频容器\n_c3 = InfoCard;\nconst VideoContainer = styled.div`\n  background: #000;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  margin-bottom: 10px;\n  \n  &:before {\n    content: \"\";\n    display: block;\n    padding-top: 56.25%; // 16:9 宽高比\n  }\n`;\n\n// 添加全屏按钮样式\n_c4 = VideoContainer;\nconst FullscreenButton = styled.div`\n  position: absolute;\n  right: 8px;\n  bottom: 8px;\n  color: white;\n  background-color: rgba(0, 0, 0, 0.5);\n  padding: 4px;\n  border-radius: 4px;\n  cursor: pointer;\n  z-index: 10;\n  transition: all 0.3s;\n\n  &:hover {\n    background-color: rgba(0, 0, 0, 0.7);\n  }\n`;\n\n// 视频占位符\n_c5 = FullscreenButton;\nconst VideoPlaceholder = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 13px;\n`;\n\n// 添加视频播放器组件\n_c6 = VideoPlaceholder;\nconst VideoPlayer = ({\n  camera\n}) => {\n  _s();\n  const videoRef = useRef(null);\n  useEffect(() => {\n    if (camera.rtspUrl && videoRef.current) {\n      // 使用 flv.js 或其他库来播放 RTSP 流\n      // 这里仅作为示例，实际项目中需要使用适当的视频流播放库\n      try {\n        if (camera.rtspUrl) {\n          videoRef.current.src = camera.rtspUrl;\n          videoRef.current.play().catch(e => {\n            console.error('Video playback failed:', e);\n          });\n        }\n      } catch (error) {\n        console.error('Error setting up video stream:', error);\n      }\n    }\n    return () => {\n      if (videoRef.current) {\n        videoRef.current.pause();\n        videoRef.current.src = '';\n      }\n    };\n  }, [camera.rtspUrl]);\n  return /*#__PURE__*/_jsxDEV(\"video\", {\n    ref: videoRef,\n    style: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover'\n    },\n    autoPlay: true,\n    playsInline: true,\n    muted: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 158,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoPlayer, \"PdMsmLAy5JKU3vCrhAlqGYQfKuA=\");\n_c7 = VideoPlayer;\nconst RoadMonitoring = () => {\n  _s2();\n  const [loading, setLoading] = useState(true);\n  const [intersections, setIntersections] = useState([]);\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  // 修改 useEffect 部分\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        // 使用导入的 devicesData\n        console.log('Imported devicesData:', devicesData);\n\n        // 确保我们访问的是 devices 数组\n        const devicesArray = devicesData.devices || [];\n        if (devicesArray.length === 0) {\n          throw new Error('No devices found in the data');\n        }\n        console.log('Processing devices array:', devicesArray);\n\n        // 根据 location 对设备进行分组\n        const groupedDevices = devicesArray.reduce((acc, device) => {\n          const location = device.location;\n          if (!location) {\n            console.warn('Device missing location:', device);\n            return acc;\n          }\n          if (!acc[location]) {\n            acc[location] = {\n              id: location,\n              name: location,\n              description: `${location}监控点`,\n              status: '正常',\n              lastUpdate: device.createdAt || new Date().toISOString(),\n              devices: []\n            };\n          }\n\n          // 使用设备原始名称而不是生成新的名称\n          acc[location].devices.push({\n            type: device.type,\n            id: device.id,\n            name: device.name,\n            status: device.status\n          });\n          return acc;\n        }, {});\n        const intersectionsData = Object.values(groupedDevices);\n        console.log('Final processed intersections:', intersectionsData);\n        if (intersectionsData.length === 0) {\n          throw new Error('No intersections processed from the data');\n        } else {\n          setIntersections(intersectionsData);\n          setSelectedIntersection(intersectionsData[0]);\n        }\n      } catch (error) {\n        console.error('Error loading devices data:', error);\n        setIntersections([{\n          id: 'error',\n          name: '数据加载错误',\n          description: `无法加载设备数据: ${error.message}`,\n          status: '错误',\n          lastUpdate: new Date().toLocaleString(),\n          devices: []\n        }]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadData();\n  }, []);\n\n  // 处理路口选择\n  const handleIntersectionSelect = intersection => {\n    setSelectedIntersection(intersection);\n  };\n\n  // 修改 getDeviceSummary 函数以支持更多设备类型\n  const getDeviceSummary = devices => {\n    const summary = {};\n    devices.forEach(device => {\n      const type = device.type;\n      summary[type] = (summary[type] || 0) + 1;\n    });\n    return Object.entries(summary).map(([type, count]) => {\n      const typeNames = {\n        'camera': '摄像头',\n        'rsu': 'RSU',\n        'mmwave_radar': '毫米波雷达',\n        'edge_computing': '边缘计算单元',\n        'lidar': '激光雷达'\n      };\n      return `${typeNames[type] || type}: ${count}`;\n    }).join(', ');\n  };\n\n  // 修改 getCameras 函数以匹配实际的设备类型\n  const getCameras = devices => {\n    return devices.filter(device => device.type === 'camera');\n  };\n\n  // 添加全屏处理函数\n  const handleFullscreen = videoContainer => {\n    if (!videoContainer) return;\n    if (document.fullscreenElement) {\n      document.exitFullscreen();\n    } else {\n      try {\n        videoContainer.requestFullscreen();\n      } catch (error) {\n        console.error('Error attempting to enable fullscreen:', error);\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    spinning: loading,\n    tip: \"\\u52A0\\u8F7D\\u4E2D...\",\n    children: /*#__PURE__*/_jsxDEV(PageContainer, {\n      children: [/*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"left\",\n        collapsed: leftCollapsed,\n        onCollapse: () => setLeftCollapsed(!leftCollapsed),\n        children: /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8DEF\\u53E3\\u4FE1\\u606F\\u5217\\u8868\",\n          bordered: false,\n          height: \"100%\",\n          children: /*#__PURE__*/_jsxDEV(List, {\n            itemLayout: \"vertical\",\n            dataSource: intersections,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              onClick: () => handleIntersectionSelect(item),\n              style: {\n                cursor: 'pointer',\n                background: (selectedIntersection === null || selectedIntersection === void 0 ? void 0 : selectedIntersection.id) === item.id ? '#e6f7ff' : 'transparent',\n                padding: '8px',\n                borderRadius: '4px',\n                marginBottom: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '14px',\n                    fontWeight: 'bold'\n                  },\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 28\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px'\n                  },\n                  children: item.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 34\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '12px',\n                  marginTop: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u8BBE\\u5907\\u914D\\u7F6E\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 26\n                  }, this), \" \", getDeviceSummary(item.devices)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '4px'\n                  },\n                  children: item.devices.map(device => /*#__PURE__*/_jsxDEV(Badge, {\n                    status: device.status === 'online' ? 'success' : 'error',\n                    text: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '12px',\n                        marginRight: '8px'\n                      },\n                      children: device.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 29\n                    }, this),\n                    style: {\n                      display: 'inline-block',\n                      marginRight: '8px'\n                    }\n                  }, device.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        leftCollapsed: leftCollapsed,\n        rightCollapsed: rightCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"right\",\n        collapsed: rightCollapsed,\n        onCollapse: () => setRightCollapsed(!rightCollapsed),\n        children: /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: `视频监控 - ${(selectedIntersection === null || selectedIntersection === void 0 ? void 0 : selectedIntersection.name) || ''}`,\n          bordered: false,\n          height: \"100%\",\n          children: selectedIntersection ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '12px',\n                fontSize: '13px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Descriptions, {\n                size: \"small\",\n                column: 1,\n                styles: {\n                  label: {\n                    fontSize: '13px'\n                  },\n                  content: {\n                    fontSize: '13px'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                  label: \"\\u8DEF\\u53E3\\u540D\\u79F0\",\n                  children: selectedIntersection.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                  label: \"\\u8DEF\\u53E3\\u63CF\\u8FF0\",\n                  children: selectedIntersection.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                  label: \"\\u6444\\u50CF\\u5934\\u6570\\u91CF\",\n                  children: getCameras(selectedIntersection.devices).length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 17\n            }, this), getCameras(selectedIntersection.devices).slice(0, 4).map(camera => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '10px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '13px',\n                  marginBottom: '4px',\n                  fontWeight: 'bold'\n                },\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  status: camera.status === 'online' ? 'success' : 'error',\n                  text: camera.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(VideoContainer, {\n                children: camera.status === 'online' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [camera.rtspUrl ? /*#__PURE__*/_jsxDEV(VideoPlayer, {\n                    camera: camera\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(VideoPlaceholder, {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        textAlign: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: \"\\u672A\\u914D\\u7F6E\\u89C6\\u9891\\u6D41\\u5730\\u5740\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 404,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '11px',\n                          marginTop: '4px'\n                        },\n                        children: \"\\u8BF7\\u5728\\u8BBE\\u5907\\u7BA1\\u7406\\u4E2D\\u914D\\u7F6ERTSP\\u5730\\u5740\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 405,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 403,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 402,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(FullscreenButton, {\n                    onClick: e => {\n                      e.stopPropagation();\n                      handleFullscreen(e.currentTarget.parentElement);\n                    },\n                    children: /*#__PURE__*/_jsxDEV(FullscreenOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(VideoPlaceholder, {\n                  children: \"\\u6444\\u50CF\\u5934\\u79BB\\u7EBF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 21\n              }, this)]\n            }, camera.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 19\n            }, this)), getCameras(selectedIntersection.devices).length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: '20px 0'\n              },\n              children: \"\\u8BE5\\u8DEF\\u53E3\\u6CA1\\u6709\\u914D\\u7F6E\\u6444\\u50CF\\u5934\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '20px 0'\n            },\n            children: \"\\u8BF7\\u9009\\u62E9\\u4E00\\u4E2A\\u8DEF\\u53E3\\u67E5\\u770B\\u89C6\\u9891\\u76D1\\u63A7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 304,\n    columnNumber: 5\n  }, this);\n};\n_s2(RoadMonitoring, \"mRMMpGGs4EM8Z7alHD7AKBWx1lg=\");\n_c8 = RoadMonitoring;\nexport default RoadMonitoring;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"InfoCard\");\n$RefreshReg$(_c4, \"VideoContainer\");\n$RefreshReg$(_c5, \"FullscreenButton\");\n$RefreshReg$(_c6, \"VideoPlaceholder\");\n$RefreshReg$(_c7, \"VideoPlayer\");\n$RefreshReg$(_c8, \"RoadMonitoring\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Card", "List", "Descriptions", "Spin", "Badge", "Row", "Col", "styled", "CollapsibleSidebar", "devicesData", "FullscreenOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "div", "_c", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "_c2", "InfoCard", "height", "_c3", "VideoContainer", "_c4", "FullscreenButton", "_c5", "VideoPlaceholder", "_c6", "VideoPlayer", "camera", "_s", "videoRef", "rtspUrl", "current", "src", "play", "catch", "e", "error", "pause", "ref", "style", "position", "top", "left", "width", "objectFit", "autoPlay", "playsInline", "muted", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c7", "RoadMonitoring", "_s2", "loading", "setLoading", "intersections", "setIntersections", "selectedIntersection", "setSelectedIntersection", "setLeftCollapsed", "setRightCollapsed", "loadData", "devicesArray", "devices", "length", "Error", "groupedDevices", "reduce", "acc", "device", "location", "warn", "id", "name", "description", "status", "lastUpdate", "createdAt", "Date", "toISOString", "push", "type", "intersectionsData", "Object", "values", "message", "toLocaleString", "handleIntersectionSelect", "intersection", "getDeviceSummary", "summary", "for<PERSON>ach", "entries", "map", "count", "typeNames", "join", "getCameras", "filter", "handleFullscreen", "videoContainer", "document", "fullscreenElement", "exitFullscreen", "requestFullscreen", "spinning", "tip", "children", "collapsed", "onCollapse", "title", "bordered", "itemLayout", "dataSource", "renderItem", "item", "<PERSON><PERSON>", "onClick", "cursor", "background", "padding", "borderRadius", "marginBottom", "Meta", "fontSize", "fontWeight", "marginTop", "text", "marginRight", "display", "size", "column", "styles", "label", "content", "slice", "textAlign", "stopPropagation", "currentTarget", "parentElement", "_c8", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RoadMonitoring.jsx"], "sourcesContent": ["// src/pages/RoadMonitoring.jsx\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Card, List, Descriptions, Spin, Badge, Row, Col } from 'antd';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport devicesData from '../data/devices.json';\nimport { FullscreenOutlined } from '@ant-design/icons';\n\n// 添加立即打印，检查导入是否成功\nconsole.log('DevicesData import check:', devicesData);\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 24px' : \n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 0' : \n    props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 视频容器\nconst VideoContainer = styled.div`\n  background: #000;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  margin-bottom: 10px;\n  \n  &:before {\n    content: \"\";\n    display: block;\n    padding-top: 56.25%; // 16:9 宽高比\n  }\n`;\n\n// 添加全屏按钮样式\nconst FullscreenButton = styled.div`\n  position: absolute;\n  right: 8px;\n  bottom: 8px;\n  color: white;\n  background-color: rgba(0, 0, 0, 0.5);\n  padding: 4px;\n  border-radius: 4px;\n  cursor: pointer;\n  z-index: 10;\n  transition: all 0.3s;\n\n  &:hover {\n    background-color: rgba(0, 0, 0, 0.7);\n  }\n`;\n\n// 视频占位符\nconst VideoPlaceholder = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 13px;\n`;\n\n// 添加视频播放器组件\nconst VideoPlayer = ({ camera }) => {\n  const videoRef = useRef(null);\n\n  useEffect(() => {\n    if (camera.rtspUrl && videoRef.current) {\n      // 使用 flv.js 或其他库来播放 RTSP 流\n      // 这里仅作为示例，实际项目中需要使用适当的视频流播放库\n      try {\n        if (camera.rtspUrl) {\n          videoRef.current.src = camera.rtspUrl;\n          videoRef.current.play().catch(e => {\n            console.error('Video playback failed:', e);\n          });\n        }\n      } catch (error) {\n        console.error('Error setting up video stream:', error);\n      }\n    }\n\n    return () => {\n      if (videoRef.current) {\n        videoRef.current.pause();\n        videoRef.current.src = '';\n      }\n    };\n  }, [camera.rtspUrl]);\n\n  return (\n    <video\n      ref={videoRef}\n      style={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        objectFit: 'cover'\n      }}\n      autoPlay\n      playsInline\n      muted\n    />\n  );\n};\n\nconst RoadMonitoring = () => {\n  const [loading, setLoading] = useState(true);\n  const [intersections, setIntersections] = useState([]);\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n  \n  // 修改 useEffect 部分\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        // 使用导入的 devicesData\n        console.log('Imported devicesData:', devicesData);\n        \n        // 确保我们访问的是 devices 数组\n        const devicesArray = devicesData.devices || [];\n        \n        if (devicesArray.length === 0) {\n          throw new Error('No devices found in the data');\n        }\n        \n        console.log('Processing devices array:', devicesArray);\n        \n        // 根据 location 对设备进行分组\n        const groupedDevices = devicesArray.reduce((acc, device) => {\n          const location = device.location;\n          if (!location) {\n            console.warn('Device missing location:', device);\n            return acc;\n          }\n\n          if (!acc[location]) {\n            acc[location] = {\n              id: location,\n              name: location,\n              description: `${location}监控点`,\n              status: '正常',\n              lastUpdate: device.createdAt || new Date().toISOString(),\n              devices: []\n            };\n          }\n\n          // 使用设备原始名称而不是生成新的名称\n          acc[location].devices.push({\n            type: device.type,\n            id: device.id,\n            name: device.name,\n            status: device.status\n          });\n\n          return acc;\n        }, {});\n\n        const intersectionsData = Object.values(groupedDevices);\n        console.log('Final processed intersections:', intersectionsData);\n\n        if (intersectionsData.length === 0) {\n          throw new Error('No intersections processed from the data');\n        } else {\n          setIntersections(intersectionsData);\n          setSelectedIntersection(intersectionsData[0]);\n        }\n      } catch (error) {\n        console.error('Error loading devices data:', error);\n        setIntersections([{\n          id: 'error',\n          name: '数据加载错误',\n          description: `无法加载设备数据: ${error.message}`,\n          status: '错误',\n          lastUpdate: new Date().toLocaleString(),\n          devices: []\n        }]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n  \n  // 处理路口选择\n  const handleIntersectionSelect = (intersection) => {\n    setSelectedIntersection(intersection);\n  };\n  \n  // 修改 getDeviceSummary 函数以支持更多设备类型\n  const getDeviceSummary = (devices) => {\n    const summary = {};\n    devices.forEach(device => {\n      const type = device.type;\n      summary[type] = (summary[type] || 0) + 1;\n    });\n    \n    return Object.entries(summary).map(([type, count]) => {\n      const typeNames = {\n        'camera': '摄像头',\n        'rsu': 'RSU',\n        'mmwave_radar': '毫米波雷达',\n        'edge_computing': '边缘计算单元',\n        'lidar': '激光雷达'\n      };\n      \n      return `${typeNames[type] || type}: ${count}`;\n    }).join(', ');\n  };\n  \n  // 修改 getCameras 函数以匹配实际的设备类型\n  const getCameras = (devices) => {\n    return devices.filter(device => device.type === 'camera');\n  };\n  \n  // 添加全屏处理函数\n  const handleFullscreen = (videoContainer) => {\n    if (!videoContainer) return;\n\n    if (document.fullscreenElement) {\n      document.exitFullscreen();\n    } else {\n      try {\n        videoContainer.requestFullscreen();\n      } catch (error) {\n        console.error('Error attempting to enable fullscreen:', error);\n      }\n    }\n  };\n  \n  return (\n    <Spin spinning={loading} tip=\"加载中...\">\n      <PageContainer>\n        {/* 左侧信息栏 */}\n        <CollapsibleSidebar \n          position=\"left\"\n          collapsed={leftCollapsed}\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\n        >\n          <InfoCard title=\"路口信息列表\" bordered={false} height=\"100%\">\n            <List\n              itemLayout=\"vertical\"\n              dataSource={intersections}\n              renderItem={item => (\n                <List.Item\n                  onClick={() => handleIntersectionSelect(item)}\n                  style={{ \n                    cursor: 'pointer',\n                    background: selectedIntersection?.id === item.id ? '#e6f7ff' : 'transparent',\n                    padding: '8px',\n                    borderRadius: '4px',\n                    marginBottom: '8px'\n                  }}\n                >\n                  <List.Item.Meta\n                    title={<span style={{ fontSize: '14px', fontWeight: 'bold' }}>{item.name}</span>}\n                    description={<span style={{ fontSize: '12px' }}>{item.description}</span>}\n                  />\n                  <div style={{ fontSize: '12px', marginTop: '4px' }}>\n                    <div><strong>设备配置：</strong> {getDeviceSummary(item.devices)}</div>\n                    <div style={{ marginTop: '4px' }}>\n                      {item.devices.map(device => (\n                        <Badge \n                          key={device.id}\n                          status={device.status === 'online' ? 'success' : 'error'} \n                          text={\n                            <span style={{ fontSize: '12px', marginRight: '8px' }}>\n                              {device.name}\n                            </span>\n                          }\n                          style={{ display: 'inline-block', marginRight: '8px' }}\n                        />\n                      ))}\n                    </div>\n                  </div>\n                </List.Item>\n              )}\n            />\n          </InfoCard>\n        </CollapsibleSidebar>\n        \n        {/* 主内容区域 */}\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\n          {/* 主要内容 */}\n        </MainContent>\n        \n        {/* 右侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"right\"\n          collapsed={rightCollapsed}\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\n        >\n          <InfoCard \n            title={`视频监控 - ${selectedIntersection?.name || ''}`} \n            bordered={false} \n            height=\"100%\"\n          >\n            {selectedIntersection ? (\n              <>\n                <div style={{ marginBottom: '12px', fontSize: '13px' }}>\n                  <Descriptions \n                    size=\"small\" \n                    column={1}\n                    styles={{\n                      label: { fontSize: '13px' },\n                      content: { fontSize: '13px' }\n                    }}\n                  >\n                    <Descriptions.Item label=\"路口名称\">{selectedIntersection.name}</Descriptions.Item>\n                    <Descriptions.Item label=\"路口描述\">{selectedIntersection.description}</Descriptions.Item>\n                    <Descriptions.Item label=\"摄像头数量\">{getCameras(selectedIntersection.devices).length}</Descriptions.Item>\n                  </Descriptions>\n                </div>\n                \n                {/* 修改视频显示部分 */}\n                {getCameras(selectedIntersection.devices).slice(0, 4).map(camera => (\n                  <div key={camera.id} style={{ marginBottom: '10px' }}>\n                    <div style={{ fontSize: '13px', marginBottom: '4px', fontWeight: 'bold' }}>\n                      <Badge \n                        status={camera.status === 'online' ? 'success' : 'error'} \n                        text={camera.name}\n                      />\n                    </div>\n                    <VideoContainer>\n                      {camera.status === 'online' ? (\n                        <>\n                          {camera.rtspUrl ? (\n                            <VideoPlayer camera={camera} />\n                          ) : (\n                            <VideoPlaceholder>\n                              <div style={{ textAlign: 'center' }}>\n                                <div>未配置视频流地址</div>\n                                <div style={{ fontSize: '11px', marginTop: '4px' }}>\n                                  请在设备管理中配置RTSP地址\n                                </div>\n                              </div>\n                            </VideoPlaceholder>\n                          )}\n                          <FullscreenButton\n                            onClick={(e) => {\n                              e.stopPropagation();\n                              handleFullscreen(e.currentTarget.parentElement);\n                            }}\n                          >\n                            <FullscreenOutlined />\n                          </FullscreenButton>\n                        </>\n                      ) : (\n                        <VideoPlaceholder>摄像头离线</VideoPlaceholder>\n                      )}\n                    </VideoContainer>\n                  </div>\n                ))}\n                \n                {getCameras(selectedIntersection.devices).length === 0 && (\n                  <div style={{ textAlign: 'center', padding: '20px 0' }}>\n                    该路口没有配置摄像头\n                  </div>\n                )}\n              </>\n            ) : (\n              <div style={{ textAlign: 'center', padding: '20px 0' }}>\n                请选择一个路口查看视频监控\n              </div>\n            )}\n          </InfoCard>\n        </CollapsibleSidebar>\n      </PageContainer>\n    </Spin>\n  );\n};\n\nexport default RoadMonitoring;"], "mappings": ";;;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,IAAI,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,QAAQ,MAAM;AACtE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,yCAAyC;AACxE,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,SAASC,kBAAkB,QAAQ,mBAAmB;;AAEtD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACAC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEP,WAAW,CAAC;;AAErD;AACA,MAAMQ,aAAa,GAAGV,MAAM,CAACW,GAAG;AAChC;AACA;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GANMF,aAAa;AAOnB,MAAMG,WAAW,GAAGb,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMG,YAAY,GAAGd,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMI,WAAW,GAAGf,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA,aAAaK,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACxEF,KAAK,CAACC,aAAa,GAAG,cAAc,GACpCD,KAAK,CAACE,cAAc,GAAG,cAAc,GAAG,OAAO;AACnD;AACA,YAAYF,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACvEF,KAAK,CAACC,aAAa,GAAG,WAAW,GACjCD,KAAK,CAACE,cAAc,GAAG,WAAW,GAAG,GAAG;AAC5C;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfMJ,WAAW;AAgBjB,MAAMK,QAAQ,GAAGpB,MAAM,CAACP,IAAI,CAAC;AAC7B;AACA,YAAYuB,KAAK,IAAIA,KAAK,CAACK,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GA1BMF,QAAQ;AA2Bd,MAAMG,cAAc,GAAGvB,MAAM,CAACW,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAa,GAAA,GAdMD,cAAc;AAepB,MAAME,gBAAgB,GAAGzB,MAAM,CAACW,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAe,GAAA,GAjBMD,gBAAgB;AAkBtB,MAAME,gBAAgB,GAAG3B,MAAM,CAACW,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAiB,GAAA,GAbMD,gBAAgB;AActB,MAAME,WAAW,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGxC,MAAM,CAAC,IAAI,CAAC;EAE7BD,SAAS,CAAC,MAAM;IACd,IAAIuC,MAAM,CAACG,OAAO,IAAID,QAAQ,CAACE,OAAO,EAAE;MACtC;MACA;MACA,IAAI;QACF,IAAIJ,MAAM,CAACG,OAAO,EAAE;UAClBD,QAAQ,CAACE,OAAO,CAACC,GAAG,GAAGL,MAAM,CAACG,OAAO;UACrCD,QAAQ,CAACE,OAAO,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAACC,CAAC,IAAI;YACjC9B,OAAO,CAAC+B,KAAK,CAAC,wBAAwB,EAAED,CAAC,CAAC;UAC5C,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd/B,OAAO,CAAC+B,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;IACF;IAEA,OAAO,MAAM;MACX,IAAIP,QAAQ,CAACE,OAAO,EAAE;QACpBF,QAAQ,CAACE,OAAO,CAACM,KAAK,CAAC,CAAC;QACxBR,QAAQ,CAACE,OAAO,CAACC,GAAG,GAAG,EAAE;MAC3B;IACF,CAAC;EACH,CAAC,EAAE,CAACL,MAAM,CAACG,OAAO,CAAC,CAAC;EAEpB,oBACE5B,OAAA;IACEoC,GAAG,EAAET,QAAS;IACdU,KAAK,EAAE;MACLC,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,MAAM;MACbzB,MAAM,EAAE,MAAM;MACd0B,SAAS,EAAE;IACb,CAAE;IACFC,QAAQ;IACRC,WAAW;IACXC,KAAK;EAAA;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEN,CAAC;AAACvB,EAAA,CA3CIF,WAAW;AAAA0B,GAAA,GAAX1B,WAAW;AA6CjB,MAAM2B,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsE,aAAa,EAAEC,gBAAgB,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAAC2B,aAAa,EAAE+C,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,cAAc,EAAE+C,iBAAiB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACAC,SAAS,CAAC,MAAM;IACd,MAAM2E,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACF;QACA1D,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEP,WAAW,CAAC;;QAEjD;QACA,MAAMiE,YAAY,GAAGjE,WAAW,CAACkE,OAAO,IAAI,EAAE;QAE9C,IAAID,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;UAC7B,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;QACjD;QAEA9D,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE0D,YAAY,CAAC;;QAEtD;QACA,MAAMI,cAAc,GAAGJ,YAAY,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAK;UAC1D,MAAMC,QAAQ,GAAGD,MAAM,CAACC,QAAQ;UAChC,IAAI,CAACA,QAAQ,EAAE;YACbnE,OAAO,CAACoE,IAAI,CAAC,0BAA0B,EAAEF,MAAM,CAAC;YAChD,OAAOD,GAAG;UACZ;UAEA,IAAI,CAACA,GAAG,CAACE,QAAQ,CAAC,EAAE;YAClBF,GAAG,CAACE,QAAQ,CAAC,GAAG;cACdE,EAAE,EAAEF,QAAQ;cACZG,IAAI,EAAEH,QAAQ;cACdI,WAAW,EAAE,GAAGJ,QAAQ,KAAK;cAC7BK,MAAM,EAAE,IAAI;cACZC,UAAU,EAAEP,MAAM,CAACQ,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;cACxDhB,OAAO,EAAE;YACX,CAAC;UACH;;UAEA;UACAK,GAAG,CAACE,QAAQ,CAAC,CAACP,OAAO,CAACiB,IAAI,CAAC;YACzBC,IAAI,EAAEZ,MAAM,CAACY,IAAI;YACjBT,EAAE,EAAEH,MAAM,CAACG,EAAE;YACbC,IAAI,EAAEJ,MAAM,CAACI,IAAI;YACjBE,MAAM,EAAEN,MAAM,CAACM;UACjB,CAAC,CAAC;UAEF,OAAOP,GAAG;QACZ,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,MAAMc,iBAAiB,GAAGC,MAAM,CAACC,MAAM,CAAClB,cAAc,CAAC;QACvD/D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE8E,iBAAiB,CAAC;QAEhE,IAAIA,iBAAiB,CAAClB,MAAM,KAAK,CAAC,EAAE;UAClC,MAAM,IAAIC,KAAK,CAAC,0CAA0C,CAAC;QAC7D,CAAC,MAAM;UACLT,gBAAgB,CAAC0B,iBAAiB,CAAC;UACnCxB,uBAAuB,CAACwB,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC/C;MACF,CAAC,CAAC,OAAOhD,KAAK,EAAE;QACd/B,OAAO,CAAC+B,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnDsB,gBAAgB,CAAC,CAAC;UAChBgB,EAAE,EAAE,OAAO;UACXC,IAAI,EAAE,QAAQ;UACdC,WAAW,EAAE,aAAaxC,KAAK,CAACmD,OAAO,EAAE;UACzCV,MAAM,EAAE,IAAI;UACZC,UAAU,EAAE,IAAIE,IAAI,CAAC,CAAC,CAACQ,cAAc,CAAC,CAAC;UACvCvB,OAAO,EAAE;QACX,CAAC,CAAC,CAAC;MACL,CAAC,SAAS;QACRT,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDO,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM0B,wBAAwB,GAAIC,YAAY,IAAK;IACjD9B,uBAAuB,CAAC8B,YAAY,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAI1B,OAAO,IAAK;IACpC,MAAM2B,OAAO,GAAG,CAAC,CAAC;IAClB3B,OAAO,CAAC4B,OAAO,CAACtB,MAAM,IAAI;MACxB,MAAMY,IAAI,GAAGZ,MAAM,CAACY,IAAI;MACxBS,OAAO,CAACT,IAAI,CAAC,GAAG,CAACS,OAAO,CAACT,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1C,CAAC,CAAC;IAEF,OAAOE,MAAM,CAACS,OAAO,CAACF,OAAO,CAAC,CAACG,GAAG,CAAC,CAAC,CAACZ,IAAI,EAAEa,KAAK,CAAC,KAAK;MACpD,MAAMC,SAAS,GAAG;QAChB,QAAQ,EAAE,KAAK;QACf,KAAK,EAAE,KAAK;QACZ,cAAc,EAAE,OAAO;QACvB,gBAAgB,EAAE,QAAQ;QAC1B,OAAO,EAAE;MACX,CAAC;MAED,OAAO,GAAGA,SAAS,CAACd,IAAI,CAAC,IAAIA,IAAI,KAAKa,KAAK,EAAE;IAC/C,CAAC,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;EACf,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIlC,OAAO,IAAK;IAC9B,OAAOA,OAAO,CAACmC,MAAM,CAAC7B,MAAM,IAAIA,MAAM,CAACY,IAAI,KAAK,QAAQ,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMkB,gBAAgB,GAAIC,cAAc,IAAK;IAC3C,IAAI,CAACA,cAAc,EAAE;IAErB,IAAIC,QAAQ,CAACC,iBAAiB,EAAE;MAC9BD,QAAQ,CAACE,cAAc,CAAC,CAAC;IAC3B,CAAC,MAAM;MACL,IAAI;QACFH,cAAc,CAACI,iBAAiB,CAAC,CAAC;MACpC,CAAC,CAAC,OAAOtE,KAAK,EAAE;QACd/B,OAAO,CAAC+B,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;IACF;EACF,CAAC;EAED,oBACElC,OAAA,CAACT,IAAI;IAACkH,QAAQ,EAAEpD,OAAQ;IAACqD,GAAG,EAAC,uBAAQ;IAAAC,QAAA,eACnC3G,OAAA,CAACK,aAAa;MAAAsG,QAAA,gBAEZ3G,OAAA,CAACJ,kBAAkB;QACjB0C,QAAQ,EAAC,MAAM;QACfsE,SAAS,EAAEhG,aAAc;QACzBiG,UAAU,EAAEA,CAAA,KAAMlD,gBAAgB,CAAC,CAAC/C,aAAa,CAAE;QAAA+F,QAAA,eAEnD3G,OAAA,CAACe,QAAQ;UAAC+F,KAAK,EAAC,sCAAQ;UAACC,QAAQ,EAAE,KAAM;UAAC/F,MAAM,EAAC,MAAM;UAAA2F,QAAA,eACrD3G,OAAA,CAACX,IAAI;YACH2H,UAAU,EAAC,UAAU;YACrBC,UAAU,EAAE1D,aAAc;YAC1B2D,UAAU,EAAEC,IAAI,iBACdnH,OAAA,CAACX,IAAI,CAAC+H,IAAI;cACRC,OAAO,EAAEA,CAAA,KAAM9B,wBAAwB,CAAC4B,IAAI,CAAE;cAC9C9E,KAAK,EAAE;gBACLiF,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,CAAA9D,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEe,EAAE,MAAK2C,IAAI,CAAC3C,EAAE,GAAG,SAAS,GAAG,aAAa;gBAC5EgD,OAAO,EAAE,KAAK;gBACdC,YAAY,EAAE,KAAK;gBACnBC,YAAY,EAAE;cAChB,CAAE;cAAAf,QAAA,gBAEF3G,OAAA,CAACX,IAAI,CAAC+H,IAAI,CAACO,IAAI;gBACbb,KAAK,eAAE9G,OAAA;kBAAMqC,KAAK,EAAE;oBAAEuF,QAAQ,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAO,CAAE;kBAAAlB,QAAA,EAAEQ,IAAI,CAAC1C;gBAAI;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAE;gBACjFyB,WAAW,eAAE1E,OAAA;kBAAMqC,KAAK,EAAE;oBAAEuF,QAAQ,EAAE;kBAAO,CAAE;kBAAAjB,QAAA,EAAEQ,IAAI,CAACzC;gBAAW;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC,eACFjD,OAAA;gBAAKqC,KAAK,EAAE;kBAAEuF,QAAQ,EAAE,MAAM;kBAAEE,SAAS,EAAE;gBAAM,CAAE;gBAAAnB,QAAA,gBACjD3G,OAAA;kBAAA2G,QAAA,gBAAK3G,OAAA;oBAAA2G,QAAA,EAAQ;kBAAK;oBAAA7D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACwC,gBAAgB,CAAC0B,IAAI,CAACpD,OAAO,CAAC;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClEjD,OAAA;kBAAKqC,KAAK,EAAE;oBAAEyF,SAAS,EAAE;kBAAM,CAAE;kBAAAnB,QAAA,EAC9BQ,IAAI,CAACpD,OAAO,CAAC8B,GAAG,CAACxB,MAAM,iBACtBrE,OAAA,CAACR,KAAK;oBAEJmF,MAAM,EAAEN,MAAM,CAACM,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;oBACzDoD,IAAI,eACF/H,OAAA;sBAAMqC,KAAK,EAAE;wBAAEuF,QAAQ,EAAE,MAAM;wBAAEI,WAAW,EAAE;sBAAM,CAAE;sBAAArB,QAAA,EACnDtC,MAAM,CAACI;oBAAI;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CACP;oBACDZ,KAAK,EAAE;sBAAE4F,OAAO,EAAE,cAAc;sBAAED,WAAW,EAAE;oBAAM;kBAAE,GAPlD3D,MAAM,CAACG,EAAE;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQf,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGrBjD,OAAA,CAACU,WAAW;QAACE,aAAa,EAAEA,aAAc;QAACC,cAAc,EAAEA;MAAe;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC,eAGdjD,OAAA,CAACJ,kBAAkB;QACjB0C,QAAQ,EAAC,OAAO;QAChBsE,SAAS,EAAE/F,cAAe;QAC1BgG,UAAU,EAAEA,CAAA,KAAMjD,iBAAiB,CAAC,CAAC/C,cAAc,CAAE;QAAA8F,QAAA,eAErD3G,OAAA,CAACe,QAAQ;UACP+F,KAAK,EAAE,UAAU,CAAArD,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEgB,IAAI,KAAI,EAAE,EAAG;UACpDsC,QAAQ,EAAE,KAAM;UAChB/F,MAAM,EAAC,MAAM;UAAA2F,QAAA,EAEZlD,oBAAoB,gBACnBzD,OAAA,CAAAE,SAAA;YAAAyG,QAAA,gBACE3G,OAAA;cAAKqC,KAAK,EAAE;gBAAEqF,YAAY,EAAE,MAAM;gBAAEE,QAAQ,EAAE;cAAO,CAAE;cAAAjB,QAAA,eACrD3G,OAAA,CAACV,YAAY;gBACX4I,IAAI,EAAC,OAAO;gBACZC,MAAM,EAAE,CAAE;gBACVC,MAAM,EAAE;kBACNC,KAAK,EAAE;oBAAET,QAAQ,EAAE;kBAAO,CAAC;kBAC3BU,OAAO,EAAE;oBAAEV,QAAQ,EAAE;kBAAO;gBAC9B,CAAE;gBAAAjB,QAAA,gBAEF3G,OAAA,CAACV,YAAY,CAAC8H,IAAI;kBAACiB,KAAK,EAAC,0BAAM;kBAAA1B,QAAA,EAAElD,oBAAoB,CAACgB;gBAAI;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eAC/EjD,OAAA,CAACV,YAAY,CAAC8H,IAAI;kBAACiB,KAAK,EAAC,0BAAM;kBAAA1B,QAAA,EAAElD,oBAAoB,CAACiB;gBAAW;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eACtFjD,OAAA,CAACV,YAAY,CAAC8H,IAAI;kBAACiB,KAAK,EAAC,gCAAO;kBAAA1B,QAAA,EAAEV,UAAU,CAACxC,oBAAoB,CAACM,OAAO,CAAC,CAACC;gBAAM;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,EAGLgD,UAAU,CAACxC,oBAAoB,CAACM,OAAO,CAAC,CAACwE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC1C,GAAG,CAACpE,MAAM,iBAC9DzB,OAAA;cAAqBqC,KAAK,EAAE;gBAAEqF,YAAY,EAAE;cAAO,CAAE;cAAAf,QAAA,gBACnD3G,OAAA;gBAAKqC,KAAK,EAAE;kBAAEuF,QAAQ,EAAE,MAAM;kBAAEF,YAAY,EAAE,KAAK;kBAAEG,UAAU,EAAE;gBAAO,CAAE;gBAAAlB,QAAA,eACxE3G,OAAA,CAACR,KAAK;kBACJmF,MAAM,EAAElD,MAAM,CAACkD,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;kBACzDoD,IAAI,EAAEtG,MAAM,CAACgD;gBAAK;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNjD,OAAA,CAACkB,cAAc;gBAAAyF,QAAA,EACZlF,MAAM,CAACkD,MAAM,KAAK,QAAQ,gBACzB3E,OAAA,CAAAE,SAAA;kBAAAyG,QAAA,GACGlF,MAAM,CAACG,OAAO,gBACb5B,OAAA,CAACwB,WAAW;oBAACC,MAAM,EAAEA;kBAAO;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAE/BjD,OAAA,CAACsB,gBAAgB;oBAAAqF,QAAA,eACf3G,OAAA;sBAAKqC,KAAK,EAAE;wBAAEmG,SAAS,EAAE;sBAAS,CAAE;sBAAA7B,QAAA,gBAClC3G,OAAA;wBAAA2G,QAAA,EAAK;sBAAQ;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnBjD,OAAA;wBAAKqC,KAAK,EAAE;0BAAEuF,QAAQ,EAAE,MAAM;0BAAEE,SAAS,EAAE;wBAAM,CAAE;wBAAAnB,QAAA,EAAC;sBAEpD;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACU,CACnB,eACDjD,OAAA,CAACoB,gBAAgB;oBACfiG,OAAO,EAAGpF,CAAC,IAAK;sBACdA,CAAC,CAACwG,eAAe,CAAC,CAAC;sBACnBtC,gBAAgB,CAAClE,CAAC,CAACyG,aAAa,CAACC,aAAa,CAAC;oBACjD,CAAE;oBAAAhC,QAAA,eAEF3G,OAAA,CAACF,kBAAkB;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,eACnB,CAAC,gBAEHjD,OAAA,CAACsB,gBAAgB;kBAAAqF,QAAA,EAAC;gBAAK;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAkB;cAC1C;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACa,CAAC;YAAA,GAlCTxB,MAAM,CAAC+C,EAAE;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmCd,CACN,CAAC,EAEDgD,UAAU,CAACxC,oBAAoB,CAACM,OAAO,CAAC,CAACC,MAAM,KAAK,CAAC,iBACpDhE,OAAA;cAAKqC,KAAK,EAAE;gBAAEmG,SAAS,EAAE,QAAQ;gBAAEhB,OAAO,EAAE;cAAS,CAAE;cAAAb,QAAA,EAAC;YAExD;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA,eACD,CAAC,gBAEHjD,OAAA;YAAKqC,KAAK,EAAE;cAAEmG,SAAS,EAAE,QAAQ;cAAEhB,OAAO,EAAE;YAAS,CAAE;YAAAb,QAAA,EAAC;UAExD;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEX,CAAC;AAACG,GAAA,CA5QID,cAAc;AAAAyF,GAAA,GAAdzF,cAAc;AA8QpB,eAAeA,cAAc;AAAC,IAAA5C,EAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAA2B,GAAA,EAAA0F,GAAA;AAAAC,YAAA,CAAAtI,EAAA;AAAAsI,YAAA,CAAA/H,GAAA;AAAA+H,YAAA,CAAA5H,GAAA;AAAA4H,YAAA,CAAA1H,GAAA;AAAA0H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAAtH,GAAA;AAAAsH,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}