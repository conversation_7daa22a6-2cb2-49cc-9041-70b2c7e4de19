{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nimport { toPathKey, toPathKeys } from \"../utils/commonUtil\";\nimport { formatStrategyValues } from \"../utils/treeUtil\";\nexport default function useSelect(multiple, triggerChange, checkedValues, halfCheckedValues, missingCheckedValues, getPathKeyEntities, getValueByKeyPath, showCheckedStrategy) {\n  return function (valuePath) {\n    if (!multiple) {\n      triggerChange(valuePath);\n    } else {\n      // Prepare conduct required info\n      var pathKey = toPathKey(valuePath);\n      var checkedPathKeys = toPathKeys(checkedValues);\n      var halfCheckedPathKeys = toPathKeys(halfCheckedValues);\n      var existInChecked = checkedPathKeys.includes(pathKey);\n      var existInMissing = missingCheckedValues.some(function (valueCells) {\n        return toPathKey(valueCells) === pathKey;\n      });\n\n      // Do update\n      var nextCheckedValues = checkedValues;\n      var nextMissingValues = missingCheckedValues;\n      if (existInMissing && !existInChecked) {\n        // Missing value only do filter\n        nextMissingValues = missingCheckedValues.filter(function (valueCells) {\n          return toPathKey(valueCells) !== pathKey;\n        });\n      } else {\n        // Update checked key first\n        var nextRawCheckedKeys = existInChecked ? checkedPathKeys.filter(function (key) {\n          return key !== pathKey;\n        }) : [].concat(_toConsumableArray(checkedPathKeys), [pathKey]);\n        var pathKeyEntities = getPathKeyEntities();\n\n        // Conduction by selected or not\n        var checkedKeys;\n        if (existInChecked) {\n          var _conductCheck = conductCheck(nextRawCheckedKeys, {\n            checked: false,\n            halfCheckedKeys: halfCheckedPathKeys\n          }, pathKeyEntities);\n          checkedKeys = _conductCheck.checkedKeys;\n        } else {\n          var _conductCheck2 = conductCheck(nextRawCheckedKeys, true, pathKeyEntities);\n          checkedKeys = _conductCheck2.checkedKeys;\n        }\n\n        // Roll up to parent level keys\n        var deDuplicatedKeys = formatStrategyValues(checkedKeys, getPathKeyEntities, showCheckedStrategy);\n        nextCheckedValues = getValueByKeyPath(deDuplicatedKeys);\n      }\n      triggerChange([].concat(_toConsumableArray(nextMissingValues), _toConsumableArray(nextCheckedValues)));\n    }\n  };\n}", "map": {"version": 3, "names": ["_toConsumableArray", "conduct<PERSON>heck", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formatStrategyValues", "useSelect", "multiple", "trigger<PERSON>hange", "checkedValues", "halfCheckedValues", "missing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getPathKeyEntities", "getValueByKeyPath", "showCheckedStrategy", "valuePath", "path<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "half<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "existInChecked", "includes", "existInMissing", "some", "valueCells", "nextCheckedValues", "nextMissing<PERSON><PERSON>ues", "filter", "nextRawCheckedKeys", "key", "concat", "pathKeyEntities", "checked<PERSON>eys", "_conductCheck", "checked", "halfC<PERSON>cked<PERSON>eys", "_conductCheck2", "deDuplicatedKeys"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-cascader/es/hooks/useSelect.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nimport { toPathKey, toPathKeys } from \"../utils/commonUtil\";\nimport { formatStrategyValues } from \"../utils/treeUtil\";\nexport default function useSelect(multiple, triggerChange, checkedValues, halfCheckedValues, missingCheckedValues, getPathKeyEntities, getValueByKeyPath, showCheckedStrategy) {\n  return function (valuePath) {\n    if (!multiple) {\n      triggerChange(valuePath);\n    } else {\n      // Prepare conduct required info\n      var pathKey = toPathKey(valuePath);\n      var checkedPathKeys = toPathKeys(checkedValues);\n      var halfCheckedPathKeys = toPathKeys(halfCheckedValues);\n      var existInChecked = checkedPathKeys.includes(pathKey);\n      var existInMissing = missingCheckedValues.some(function (valueCells) {\n        return toPathKey(valueCells) === pathKey;\n      });\n\n      // Do update\n      var nextCheckedValues = checkedValues;\n      var nextMissingValues = missingCheckedValues;\n      if (existInMissing && !existInChecked) {\n        // Missing value only do filter\n        nextMissingValues = missingCheckedValues.filter(function (valueCells) {\n          return toPathKey(valueCells) !== pathKey;\n        });\n      } else {\n        // Update checked key first\n        var nextRawCheckedKeys = existInChecked ? checkedPathKeys.filter(function (key) {\n          return key !== pathKey;\n        }) : [].concat(_toConsumableArray(checkedPathKeys), [pathKey]);\n        var pathKeyEntities = getPathKeyEntities();\n\n        // Conduction by selected or not\n        var checkedKeys;\n        if (existInChecked) {\n          var _conductCheck = conductCheck(nextRawCheckedKeys, {\n            checked: false,\n            halfCheckedKeys: halfCheckedPathKeys\n          }, pathKeyEntities);\n          checkedKeys = _conductCheck.checkedKeys;\n        } else {\n          var _conductCheck2 = conductCheck(nextRawCheckedKeys, true, pathKeyEntities);\n          checkedKeys = _conductCheck2.checkedKeys;\n        }\n\n        // Roll up to parent level keys\n        var deDuplicatedKeys = formatStrategyValues(checkedKeys, getPathKeyEntities, showCheckedStrategy);\n        nextCheckedValues = getValueByKeyPath(deDuplicatedKeys);\n      }\n      triggerChange([].concat(_toConsumableArray(nextMissingValues), _toConsumableArray(nextCheckedValues)));\n    }\n  };\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,SAAS,EAAEC,UAAU,QAAQ,qBAAqB;AAC3D,SAASC,oBAAoB,QAAQ,mBAAmB;AACxD,eAAe,SAASC,SAASA,CAACC,QAAQ,EAAEC,aAAa,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAE;EAC7K,OAAO,UAAUC,SAAS,EAAE;IAC1B,IAAI,CAACR,QAAQ,EAAE;MACbC,aAAa,CAACO,SAAS,CAAC;IAC1B,CAAC,MAAM;MACL;MACA,IAAIC,OAAO,GAAGb,SAAS,CAACY,SAAS,CAAC;MAClC,IAAIE,eAAe,GAAGb,UAAU,CAACK,aAAa,CAAC;MAC/C,IAAIS,mBAAmB,GAAGd,UAAU,CAACM,iBAAiB,CAAC;MACvD,IAAIS,cAAc,GAAGF,eAAe,CAACG,QAAQ,CAACJ,OAAO,CAAC;MACtD,IAAIK,cAAc,GAAGV,oBAAoB,CAACW,IAAI,CAAC,UAAUC,UAAU,EAAE;QACnE,OAAOpB,SAAS,CAACoB,UAAU,CAAC,KAAKP,OAAO;MAC1C,CAAC,CAAC;;MAEF;MACA,IAAIQ,iBAAiB,GAAGf,aAAa;MACrC,IAAIgB,iBAAiB,GAAGd,oBAAoB;MAC5C,IAAIU,cAAc,IAAI,CAACF,cAAc,EAAE;QACrC;QACAM,iBAAiB,GAAGd,oBAAoB,CAACe,MAAM,CAAC,UAAUH,UAAU,EAAE;UACpE,OAAOpB,SAAS,CAACoB,UAAU,CAAC,KAAKP,OAAO;QAC1C,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,IAAIW,kBAAkB,GAAGR,cAAc,GAAGF,eAAe,CAACS,MAAM,CAAC,UAAUE,GAAG,EAAE;UAC9E,OAAOA,GAAG,KAAKZ,OAAO;QACxB,CAAC,CAAC,GAAG,EAAE,CAACa,MAAM,CAAC5B,kBAAkB,CAACgB,eAAe,CAAC,EAAE,CAACD,OAAO,CAAC,CAAC;QAC9D,IAAIc,eAAe,GAAGlB,kBAAkB,CAAC,CAAC;;QAE1C;QACA,IAAImB,WAAW;QACf,IAAIZ,cAAc,EAAE;UAClB,IAAIa,aAAa,GAAG9B,YAAY,CAACyB,kBAAkB,EAAE;YACnDM,OAAO,EAAE,KAAK;YACdC,eAAe,EAAEhB;UACnB,CAAC,EAAEY,eAAe,CAAC;UACnBC,WAAW,GAAGC,aAAa,CAACD,WAAW;QACzC,CAAC,MAAM;UACL,IAAII,cAAc,GAAGjC,YAAY,CAACyB,kBAAkB,EAAE,IAAI,EAAEG,eAAe,CAAC;UAC5EC,WAAW,GAAGI,cAAc,CAACJ,WAAW;QAC1C;;QAEA;QACA,IAAIK,gBAAgB,GAAG/B,oBAAoB,CAAC0B,WAAW,EAAEnB,kBAAkB,EAAEE,mBAAmB,CAAC;QACjGU,iBAAiB,GAAGX,iBAAiB,CAACuB,gBAAgB,CAAC;MACzD;MACA5B,aAAa,CAAC,EAAE,CAACqB,MAAM,CAAC5B,kBAAkB,CAACwB,iBAAiB,CAAC,EAAExB,kBAAkB,CAACuB,iBAAiB,CAAC,CAAC,CAAC;IACxG;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}