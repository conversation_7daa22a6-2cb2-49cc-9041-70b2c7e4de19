{"ast": null, "code": "import React,{useState,useEffect,useRef,useCallback}from'react';import{Row,Col,Card,Statistic,List,Table,Descriptions,Spin,Badge}from'antd';import*as echarts from'echarts/core';import{Bar<PERSON>hart}from'echarts/charts';import{GridComponent,TooltipComponent,LegendComponent,TitleComponent}from'echarts/components';import{CanvasRenderer}from'echarts/renderers';import styled from'styled-components';import CollapsibleSidebar from'../components/layout/CollapsibleSidebar';import axios from'axios';import vehiclesData from'../data/vehicles.json';// 注册必要的echarts组件\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";echarts.use([BarChart,GridComponent,TooltipComponent,LegendComponent,TitleComponent,CanvasRenderer]);const StyledCanvas=styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;// 页面布局容器\nconst PageContainer=styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;// 左侧信息栏容器\nconst LeftSidebar=styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;// 右侧信息栏容器\nconst RightSidebar=styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;// 主内容区域\nconst MainContent=styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props=>props.leftCollapsed&&props.rightCollapsed?'0 24px':props.leftCollapsed?'0 8px 0 24px':props.rightCollapsed?'0 24px 0 8px':'0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props=>props.leftCollapsed&&props.rightCollapsed?'0 24px':props.leftCollapsed?'0 8px 0 0':props.rightCollapsed?'0 0 0 8px':'0'};\n  position: relative;\n  z-index: 1;\n`;// 信息卡片\nconst InfoCard=styled(Card)`\n  margin-bottom: 12px;\n  height: ${props=>props.height||'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;// 自定义统计数字组件\nconst CompactStatistic=styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n  \n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;const RealTimeTraffic=()=>{const[loading,setLoading]=useState(true);const[vehicles,setVehicles]=useState([]);const[events,setEvents]=useState(()=>{try{const savedEvents=localStorage.getItem('realTimeTrafficEvents');return savedEvents?JSON.parse(savedEvents):[];}catch(error){console.error('读取事件列表失败:',error);return[];}});const[selectedVehicle,setSelectedVehicle]=useState(null);const[stats,setStats]=useState({totalVehicles:0,onlineVehicles:0,offlineVehicles:0,totalDevices:0,onlineDevices:0,offlineDevices:0});// 存储在线车辆的 BSM ID，初始时为空集合，表示所有车辆离线\nconst[onlineBsmIds,setOnlineBsmIds]=useState(new Set());// 存储最后一次收到 BSM 消息的时间\nconst lastBsmTime=useRef({});const eventChartRef=useRef(null);// 添加侧边栏折叠状态\nconst[leftCollapsed,setLeftCollapsed]=useState(false);const[rightCollapsed,setRightCollapsed]=useState(false);const[eventStats,setEventStats]=useState(()=>{try{const savedStats=localStorage.getItem('realTimeTrafficEventStats');return savedStats?JSON.parse(savedStats):{'401':0,// 道路抛洒物\n'404':0,// 道路障碍物\n'405':0,// 行人通过马路\n'904':0,// 逆行车辆\n'910':0,// 违停车辆\n'1002':0,// 道路施工\n'901':0// 车辆超速\n};}catch(error){console.error('读取事件统计数据失败:',error);return{'401':0,'404':0,'405':0,'904':0,'910':0,'1002':0,'901':0};}});// 添加手动更新车辆状态和位置信息的函数\nconst updateVehicleStatus=useCallback(function(bsmId,status){let speed=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;let lat=arguments.length>3&&arguments[3]!==undefined?arguments[3]:0;let lng=arguments.length>4&&arguments[4]!==undefined?arguments[4]:0;let heading=arguments.length>5&&arguments[5]!==undefined?arguments[5]:0;// 确保速度和航向角是格式化的数值，保留两位小数\nconst formattedSpeed=parseFloat(speed).toFixed(2);const formattedHeading=parseFloat(heading).toFixed(2);console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${formattedSpeed}, 位置=(${lat}, ${lng})`);// 更新在线状态\nif(status==='online'){setOnlineBsmIds(prev=>new Set([...prev,bsmId]));lastBsmTime.current[bsmId]=Date.now();}else{setOnlineBsmIds(prev=>{const newSet=new Set(prev);newSet.delete(bsmId);return newSet;});}// 更新车辆信息\nsetVehicles(prevVehicles=>prevVehicles.map(vehicle=>vehicle.bsmId===bsmId?{...vehicle,status:status,speed:parseFloat(formattedSpeed),// 确保是数值类型\nlat:parseFloat(lat.toFixed(7)),lng:parseFloat(lng.toFixed(7)),heading:parseFloat(formattedHeading)// 确保是数值类型\n}:vehicle));},[]);// 在组件挂载时，暴露updateVehicleStatus函数到window对象\nuseEffect(()=>{window.updateVehicleStatus=updateVehicleStatus;// 用于监听CampusModel是否接收到BSM消息的事件\nconst handleRealBsmReceived=event=>{if(event.data&&event.data.type==='realBsmReceived'){console.log('收到CampusModel发送的真实BSM消息通知');}};window.addEventListener('message',handleRealBsmReceived);// 确保页面刷新时清空在线车辆状态\nconsole.log('组件初始化，重置所有车辆为离线状态');setOnlineBsmIds(new Set());lastBsmTime.current={};return()=>{window.removeEventListener('message',handleRealBsmReceived);delete window.updateVehicleStatus;};},[updateVehicleStatus]);// 使用防抖保存事件列表到 localStorage\nuseEffect(()=>{const saveTimer=setTimeout(()=>{try{localStorage.setItem('realTimeTrafficEvents',JSON.stringify(events));}catch(error){console.error('保存事件列表失败:',error);}},1000);// 延迟1秒保存，避免频繁写入\nreturn()=>clearTimeout(saveTimer);},[events]);// 使用防抖保存事件统计数据到 localStorage\nuseEffect(()=>{const saveTimer=setTimeout(()=>{try{localStorage.setItem('realTimeTrafficEventStats',JSON.stringify(eventStats));}catch(error){console.error('保存事件统计数据失败:',error);}},1000);// 延迟1秒保存，避免频繁写入\nreturn()=>clearTimeout(saveTimer);},[eventStats]);// 获取车辆数据\nconst fetchVehicles=()=>{try{// 从本地 JSON 文件获取车辆数据\nconst vehiclesList=vehiclesData.vehicles||[];// 更新车辆数据，并根据 BSM ID 是否在线来设置状态\nconst updatedVehicles=vehiclesList.map(vehicle=>({...vehicle,plate:vehicle.plateNumber,// 适配表格显示\nstatus:onlineBsmIds.has(vehicle.bsmId)?'online':'offline',// 确保只有在onlineBsmIds中的车辆才显示为在线\nspeed:0,// 初始速度设为 0，后续通过 BSM 消息更新\nlat:0,// 初始位置设为 0，后续通过 BSM 消息更新\nlng:0,// 初始位置设为 0，后续通过 BSM 消息更新\nheading:0// 初始航向角设为 0，后续通过 BSM 消息更新\n}));setVehicles(updatedVehicles);console.log('车辆列表已更新，在线车辆ID:',Array.from(onlineBsmIds));// 更新车辆统计数据\nsetStats(prevStats=>({...prevStats,totalVehicles:updatedVehicles.length,onlineVehicles:updatedVehicles.filter(v=>v.status==='online').length,offlineVehicles:updatedVehicles.filter(v=>v.status==='offline').length}));}catch(error){console.error('获取车辆列表失败:',error);}};// 获取设备统计数据\nconst fetchDeviceStats=async()=>{try{const apiUrl=process.env.REACT_APP_API_URL||'http://localhost:5000';const response=await axios.get(`${apiUrl}/api/devices`);if(response.data&&response.data.success){const devicesData=response.data.data;// 更新设备统计数据\nsetStats(prevStats=>({...prevStats,totalDevices:devicesData.length,onlineDevices:devicesData.filter(d=>d.status==='online').length,offlineDevices:devicesData.filter(d=>d.status==='offline').length}));}}catch(error){console.error('获取设备统计数据失败:',error);}};// 监听 BSM 消息\nuseEffect(()=>{const handleBsmMessage=event=>{if(event.data&&event.data.type==='bsm'){// 获取bsmId，确保它正确地从消息中提取\nconst bsmData=event.data.data||{};const bsmId=bsmData.bsmId||event.data.bsmId;if(!bsmId){console.error('BSM消息缺少bsmId:',event.data);return;}console.log('收到BSM消息，ID:',bsmId);const now=Date.now();// 更新最后接收时间\nlastBsmTime.current[bsmId]=now;// 添加到在线bsmId集合\nsetOnlineBsmIds(prev=>new Set([...prev,bsmId]));// 提取正确的BSM数据并格式化为两位小数\nconst speed=parseFloat((parseFloat(bsmData.partSpeed||event.data.speed||0)*3.6).toFixed(2));// 转换为km/h，保留两位小数\nconst lat=parseFloat(parseFloat(bsmData.partLat||event.data.lat||0).toFixed(7));const lng=parseFloat(parseFloat(bsmData.partLong||event.data.lng||0).toFixed(7));const heading=parseFloat(parseFloat(bsmData.partHeading||event.data.heading||0).toFixed(2));// 保留两位小数\nconsole.log(`车辆${bsmId}状态: 速度=${speed.toFixed(2)}km/h, 位置=(${lat.toFixed(7)}, ${lng.toFixed(7)}), 航向=${heading.toFixed(2)}°`);// 更新车辆状态和位置信息\nsetVehicles(prevVehicles=>{// 检查是否找到对应车辆\nconst foundVehicle=prevVehicles.find(v=>v.bsmId===bsmId);if(!foundVehicle){console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);return prevVehicles;}return prevVehicles.map(vehicle=>vehicle.bsmId===bsmId?{...vehicle,status:'online',speed:speed,lat:lat,lng:lng,heading:heading}:vehicle);});}};// 添加消息监听器\nwindow.addEventListener('message',handleBsmMessage);// 清理函数\nreturn()=>{window.removeEventListener('message',handleBsmMessage);};},[]);// 定期检查在线状态\nuseEffect(()=>{const checkOnlineStatus=()=>{const now=Date.now();console.log('检查车辆在线状态...');// 如果超过30秒没有收到BSM消息，则认为车辆离线\nsetOnlineBsmIds(prev=>{const newOnlineBsmIds=new Set(prev);let hasChanges=false;// 记录当前所有在线的车辆ID\nif(newOnlineBsmIds.size>0){console.log('当前在线车辆ID:',Array.from(newOnlineBsmIds));}newOnlineBsmIds.forEach(bsmId=>{const lastTime=lastBsmTime.current[bsmId]||0;const timeSinceLastUpdate=now-lastTime;if(timeSinceLastUpdate>30000){console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);newOnlineBsmIds.delete(bsmId);hasChanges=true;}});if(hasChanges){// 更新车辆状态\nsetVehicles(prevVehicles=>prevVehicles.map(vehicle=>{const isOnline=newOnlineBsmIds.has(vehicle.bsmId);return{...vehicle,status:isOnline?'online':'offline'};}));}return newOnlineBsmIds;});};const interval=setInterval(checkOnlineStatus,5000);return()=>clearInterval(interval);},[]);// 重置所有车辆的初始状态\nuseEffect(()=>{// 将所有车辆状态重置为离线\nconst resetAllVehicles=()=>{// 只有在没有任何BSM消息的情况下才执行重置\nif(onlineBsmIds.size===0){setVehicles(prevVehicles=>prevVehicles.map(vehicle=>({...vehicle,status:'offline',speed:0,lat:0,lng:0,heading:0})));console.log('已重置所有车辆为离线状态');}};// 初始执行一次\nresetAllVehicles();// 然后每30秒检查一次\nconst interval=setInterval(resetAllVehicles,30000);return()=>clearInterval(interval);},[onlineBsmIds]);// 在组件挂载时获取数据\nuseEffect(()=>{const loadData=async()=>{setLoading(true);fetchVehicles();await fetchDeviceStats();setLoading(false);};loadData();// 降低更新频率，避免频繁覆盖状态\nconst interval=setInterval(loadData,60000);// 每60秒更新一次\nreturn()=>clearInterval(interval);},[]);// 添加对车辆数据变更的监听\nuseEffect(()=>{console.log('设置车辆数据变更监听');// 监听自定义事件\nconst handleVehiclesDataChanged=()=>{console.log('检测到车辆数据变更事件，重新获取车辆列表');fetchVehicles();};// 监听localStorage变化\nconst handleStorageChange=event=>{if(event.key==='vehiclesLastUpdated'||event.key==='vehiclesData'){console.log('检测到localStorage变化，重新获取车辆列表');fetchVehicles();}};// 添加事件监听器\nwindow.addEventListener('vehiclesDataChanged',handleVehiclesDataChanged);window.addEventListener('storage',handleStorageChange);// 初始检查是否有更新\nconst lastUpdated=localStorage.getItem('vehiclesLastUpdated');if(lastUpdated){console.log('初始检查到vehiclesLastUpdated:',lastUpdated);fetchVehicles();}// 设置更频繁的强制轮询，确保在部署环境中也能获取最新数据\nconst forcedPollingInterval=setInterval(()=>{console.log('强制轮询：重新获取车辆列表');fetchVehicles();},10000);// 每10秒强制刷新一次\n// 清理函数\nreturn()=>{window.removeEventListener('vehiclesDataChanged',handleVehiclesDataChanged);window.removeEventListener('storage',handleStorageChange);clearInterval(forcedPollingInterval);};},[]);// 添加检查本地缓存与API数据是否一致的机制\n// 定义一个单独的API调用函数，用于获取最新的车辆列表数据\nconst fetchLatestVehiclesData=async()=>{try{const apiUrl='http://localhost:5000';const response=await axios.get(`${apiUrl}/api/vehicles/list`);if(response.data&&response.data.vehicles){console.log('API返回的车辆数量:',response.data.vehicles.length);// 与当前列表比较\nif(vehicles.length!==response.data.vehicles.length){console.log('检测到车辆数量变化，从',vehicles.length,'到',response.data.vehicles.length);fetchVehicles();// 重新加载\nreturn;}// 检查是否有新车辆ID\nconst currentIds=new Set(vehicles.map(v=>v.id));const hasNewVehicle=response.data.vehicles.some(v=>!currentIds.has(v.id));if(hasNewVehicle){console.log('检测到新增车辆');fetchVehicles();// 重新加载\n}}}catch(error){console.error('直接获取车辆列表失败:',error);}};// 添加强制定期从API获取最新数据的机制\nuseEffect(()=>{const apiPollingInterval=setInterval(()=>{console.log('直接从API检查车辆数据更新');fetchLatestVehiclesData();},15000);// 每15秒检查一次API\nreturn()=>clearInterval(apiPollingInterval);},[vehicles]);// 添加自动选择第一个车辆的逻辑\nuseEffect(()=>{// 当车辆列表加载完成且有车辆数据时\nif(vehicles.length>0&&!selectedVehicle){// 自动选择第一个车辆\nsetSelectedVehicle(vehicles[0]);console.log('已自动选择第一个车辆:',vehicles[0].plateNumber);}},[vehicles,selectedVehicle]);// 修改图表初始化代码\nuseEffect(()=>{let chart=null;let handleResize=null;let lastUpdateTime=new Date();// 确保 DOM 元素存在\nif(!eventChartRef.current){console.error('图表容器未找到');return;}// 检查并清理已存在的图表实例\nlet existingChart=echarts.getInstanceByDom(eventChartRef.current);if(existingChart){existingChart.dispose();}try{// 初始化新的图表实例\nchart=echarts.init(eventChartRef.current);// 设置图表配置\nconst updateChart=()=>{const currentTime=new Date();lastUpdateTime=currentTime;// 事件类型配置\nconst eventTypes=[{type:'401',name:'道路抛洒物',color:'#ff4d4f'},{type:'404',name:'道路障碍物',color:'#faad14'},{type:'405',name:'行人通过马路',color:'#1890ff'},{type:'904',name:'逆行车辆',color:'#f5222d'},{type:'910',name:'违停车辆',color:'#722ed1'},{type:'1002',name:'道路施工',color:'#fa8c16'},{type:'901',name:'车辆超速',color:'#eb2f96'}];// 处理数据\nconst data=eventTypes.map(event=>({value:eventStats[event.type]||0,name:event.name,itemStyle:{color:event.color}})).filter(item=>item.value>0).sort((a,b)=>b.value-a.value);const option={title:{text:`最后更新: ${currentTime.toLocaleTimeString()}`,left:'center',top:-5,textStyle:{fontSize:12,color:'#999'}},grid:{top:30,bottom:0,left:0,right:50,containLabel:true},animation:true,animationDuration:0,animationDurationUpdate:1000,animationEasingUpdate:'quinticInOut',tooltip:{trigger:'axis',axisPointer:{type:'shadow'}},xAxis:{type:'value',show:false,splitLine:{show:false}},yAxis:{type:'category',data:data.map(item=>item.name),axisLabel:{fontSize:12,color:'#666',margin:8},axisTick:{show:false},axisLine:{show:false}},series:[{type:'bar',data:data,barWidth:'50%',label:{show:true,position:'right',formatter:'{c}次',fontSize:12,color:'#666'},itemStyle:{borderRadius:[0,4,4,0]},realtimeSort:false,animationDelay:function(idx){return idx*100;}}]};// 使用 notMerge: false 来保持增量更新\nchart.setOption(option,{notMerge:false,replaceMerge:['series']});};// 初始更新\nupdateChart();// 监听事件统计变化，每分钟更新一次\nconst statsInterval=setInterval(updateChart,60000);// 60000ms = 1分钟\n// 监听窗口大小变化\nhandleResize=()=>{var _chart;(_chart=chart)===null||_chart===void 0?void 0:_chart.resize();};window.addEventListener('resize',handleResize);// 清理函数\nreturn()=>{clearInterval(statsInterval);if(handleResize){window.removeEventListener('resize',handleResize);}if(chart){chart.dispose();}};}catch(error){console.error('初始化图表失败:',error);}},[eventStats]);// 修改 RSI 消息处理逻辑\nuseEffect(()=>{const handleRsiMessage=event=>{try{if(event.data&&event.data.type==='RSI'){console.log('RealTimeTraffic 收到 RSI 消息:',event.data);const rsiData=event.data.data;if(!rsiData||!rsiData.rtes){console.warn('RSI 消息数据格式不正确:',rsiData);return;}const events=rsiData.rtes;const latitude=parseFloat(rsiData.posLat);const longitude=parseFloat(rsiData.posLong);events.forEach(event=>{const eventType=event.eventType;const description=event.description;const time=new Date().toLocaleTimeString();// 获取事件类型的中文描述\nlet eventTypeText='';let eventColor='';switch(eventType){case'401':eventTypeText='道路抛洒物';eventColor='#ff4d4f';break;case'404':eventTypeText='道路障碍物';eventColor='#faad14';break;case'405':eventTypeText='行人通过马路';eventColor='#1890ff';break;case'904':eventTypeText='逆行车辆';eventColor='#f5222d';break;case'910':eventTypeText='违停车辆';eventColor='#722ed1';break;case'1002':eventTypeText='道路施工';eventColor='#fa8c16';break;case'901':eventTypeText='车辆超速';eventColor='#eb2f96';break;default:eventTypeText=description||'未知事件';eventColor='#8c8c8c';}// 更新事件列表\nconst newEvent={key:Date.now()+Math.random(),type:eventTypeText,time:time,vehicle:rsiData.rsuId||'未知设备',color:eventColor,eventType:eventType,location:{latitude:latitude,longitude:longitude}};setEvents(prev=>{const newEvents=[newEvent,...prev].slice(0,10);// 只保留最近10条记录\nconsole.log('更新后的事件列表:',newEvents);return newEvents;});// 更新事件统计\nif(eventType){setEventStats(prev=>{const newStats={...prev,[eventType]:(prev[eventType]||0)+1};console.log('更新后的事件统计:',newStats);return newStats;});}});}}catch(error){console.error('处理 RSI 消息失败:',error);}};// 添加消息监听器\nwindow.addEventListener('message',handleRsiMessage);return()=>{window.removeEventListener('message',handleRsiMessage);};},[]);// 处理车辆选择\nconst handleVehicleSelect=vehicle=>{console.log('选择车辆:',vehicle.plateNumber,'状态:',vehicle.status);setSelectedVehicle(vehicle);};// 修改车辆状态更新逻辑，确保同步更新selectedVehicle\nuseEffect(()=>{// 当车辆列表更新后，如果当前有选中的车辆，则更新选中的车辆信息\nif(selectedVehicle){const updatedSelectedVehicle=vehicles.find(v=>v.id===selectedVehicle.id);if(updatedSelectedVehicle&&(updatedSelectedVehicle.status!==selectedVehicle.status||updatedSelectedVehicle.speed!==selectedVehicle.speed||updatedSelectedVehicle.lat!==selectedVehicle.lat||updatedSelectedVehicle.lng!==selectedVehicle.lng||updatedSelectedVehicle.heading!==selectedVehicle.heading)){console.log(`更新选中车辆 ${selectedVehicle.plateNumber} 的信息:`,`状态从 ${selectedVehicle.status} 变为 ${updatedSelectedVehicle.status}`);setSelectedVehicle(updatedSelectedVehicle);}}},[vehicles,selectedVehicle]);// 车辆列表列定义\nconst vehicleColumns=[{title:'车牌号',dataIndex:'plate',key:'plate',width:'40%'},{title:'状态',dataIndex:'status',key:'status',width:'30%',render:status=>/*#__PURE__*/_jsx(Badge,{status:status==='online'?'success':'error',text:status==='online'?'在线':'离线'})},{title:'速度',dataIndex:'speed',key:'speed',width:'30%',render:speed=>`${typeof speed==='number'?speed.toFixed(2):speed} km/h`}];// 修改实时事件列表的渲染\nconst renderEventList=()=>/*#__PURE__*/_jsx(List,{size:\"small\",dataSource:events,renderItem:item=>/*#__PURE__*/_jsx(List.Item,{style:{padding:'8px 0'},children:/*#__PURE__*/_jsx(List.Item.Meta,{title:/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'13px',marginBottom:'4px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:item.color,marginRight:'8px'},children:item.type}),/*#__PURE__*/_jsx(\"span\",{style:{color:'#666',fontSize:'12px'},children:item.time})]}),description:/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#666'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u8BBE\\u5907: \",item.vehicle]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u4F4D\\u7F6E: \",item.location?`${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}`:'未知位置']})]})})}),style:{maxHeight:'calc(100% - 24px)',overflowY:'auto'}});return/*#__PURE__*/_jsx(Spin,{spinning:loading,tip:\"\\u52A0\\u8F7D\\u4E2D...\",children:/*#__PURE__*/_jsxs(PageContainer,{children:[/*#__PURE__*/_jsxs(CollapsibleSidebar,{position:\"left\",collapsed:leftCollapsed,onCollapse:()=>setLeftCollapsed(!leftCollapsed),children:[/*#__PURE__*/_jsx(InfoCard,{title:\"\\u8F66\\u8F86\\u548C\\u8BBE\\u5907\\u603B\\u6570\\u4FE1\\u606F\",bordered:false,height:\"150px\",children:/*#__PURE__*/_jsxs(Row,{gutter:[8,8],children:[/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u8F66\\u8F86\\u603B\\u6570\",value:stats.totalVehicles,valueStyle:{color:'#3f8600'}})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u5728\\u7EBF\\u8F66\\u8F86\",value:stats.onlineVehicles,suffix:`/ ${stats.totalVehicles}`,valueStyle:{color:'#3f8600'}})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u79BB\\u7EBF\\u8F66\\u8F86\",value:stats.offlineVehicles,suffix:`/ ${stats.totalVehicles}`,valueStyle:{color:'#cf1322'}})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u8BBE\\u5907\\u603B\\u6570\",value:stats.totalDevices,valueStyle:{color:'#3f8600'}})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u5728\\u7EBF\\u8BBE\\u5907\",value:stats.onlineDevices,suffix:`/ ${stats.totalDevices}`,valueStyle:{color:'#3f8600'}})}),/*#__PURE__*/_jsx(Col,{span:8,children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u79BB\\u7EBF\\u8BBE\\u5907\",value:stats.offlineDevices,suffix:`/ ${stats.totalDevices}`,valueStyle:{color:'#cf1322'}})})]})}),/*#__PURE__*/_jsx(InfoCard,{title:\"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u5217\\u8868\",bordered:false,height:\"calc(50% - 81px)\",children:renderEventList()}),/*#__PURE__*/_jsx(InfoCard,{title:\"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u7EDF\\u8BA1\",bordered:false,height:\"calc(50% - 81px)\",children:/*#__PURE__*/_jsx(\"div\",{ref:eventChartRef,style:{height:'100%',width:'100%'}})})]}),/*#__PURE__*/_jsx(MainContent,{leftCollapsed:leftCollapsed,rightCollapsed:rightCollapsed}),/*#__PURE__*/_jsxs(CollapsibleSidebar,{position:\"right\",collapsed:rightCollapsed,onCollapse:()=>setRightCollapsed(!rightCollapsed),children:[/*#__PURE__*/_jsx(InfoCard,{title:\"\\u8F66\\u8F86\\u5217\\u8868\",bordered:false,height:\"50%\",children:/*#__PURE__*/_jsx(Table,{dataSource:vehicles,columns:vehicleColumns,rowKey:\"id\",pagination:false,size:\"small\",scroll:{y:180},onRow:record=>({onClick:()=>handleVehicleSelect(record),style:{cursor:'pointer',background:(selectedVehicle===null||selectedVehicle===void 0?void 0:selectedVehicle.id)===record.id?'#e6f7ff':'transparent',fontSize:'13px',padding:'4px 8px'}})})}),/*#__PURE__*/_jsx(InfoCard,{title:\"\\u8F66\\u8F86\\u8BE6\\u7EC6\\u4FE1\\u606F\",bordered:false,height:\"50%\",children:selectedVehicle?/*#__PURE__*/_jsxs(Descriptions,{bordered:true,column:1,size:\"small\",styles:{label:{fontSize:'13px',padding:'4px 8px'},content:{fontSize:'13px',padding:'4px 8px'}},children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8F66\\u724C\\u53F7\",children:selectedVehicle.plateNumber}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u72B6\\u6001\",children:/*#__PURE__*/_jsx(Badge,{status:selectedVehicle.status==='online'?'success':'error',text:selectedVehicle.status==='online'?'在线':'离线'})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7ECF\\u5EA6\",children:selectedVehicle.status==='online'?selectedVehicle.lng.toFixed(7):'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7EAC\\u5EA6\",children:selectedVehicle.status==='online'?selectedVehicle.lat.toFixed(7):'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u901F\\u5EA6\",children:selectedVehicle.status==='online'?`${typeof selectedVehicle.speed==='number'?selectedVehicle.speed.toFixed(2):selectedVehicle.speed} km/h`:'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u822A\\u5411\\u89D2\",children:selectedVehicle.status==='online'?`${typeof selectedVehicle.heading==='number'?selectedVehicle.heading.toFixed(2):selectedVehicle.heading}°`:'N/A'})]}):/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'13px'},children:\"\\u8BF7\\u9009\\u62E9\\u8F66\\u8F86\\u67E5\\u770B\\u8BE6\\u7EC6\\u4FE1\\u606F\"})})]})]})});};export default RealTimeTraffic;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "Row", "Col", "Card", "Statistic", "List", "Table", "Descriptions", "Spin", "Badge", "echarts", "<PERSON><PERSON><PERSON>", "GridComponent", "TooltipComponent", "LegendComponent", "TitleComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "CollapsibleSidebar", "axios", "vehiclesData", "jsx", "_jsx", "jsxs", "_jsxs", "use", "StyledCanvas", "div", "<PERSON><PERSON><PERSON><PERSON>", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "InfoCard", "height", "CompactStatistic", "RealTimeTraffic", "loading", "setLoading", "vehicles", "setVehicles", "events", "setEvents", "savedEvents", "localStorage", "getItem", "JSON", "parse", "error", "console", "selectedVehicle", "setSelectedVehicle", "stats", "setStats", "totalVehicles", "onlineVehicles", "offlineVehicles", "totalDevices", "onlineDevices", "offlineDevices", "onlineBsmIds", "setOnlineBsmIds", "Set", "lastBsmTime", "eventChartRef", "setLeftCollapsed", "setRightCollapsed", "eventStats", "setEventStats", "savedStats", "updateVehicleStatus", "bsmId", "status", "speed", "arguments", "length", "undefined", "lat", "lng", "heading", "formattedSpeed", "parseFloat", "toFixed", "formattedHeading", "log", "prev", "current", "Date", "now", "newSet", "delete", "prevVehicles", "map", "vehicle", "window", "handleRealBsmReceived", "event", "data", "type", "addEventListener", "removeEventListener", "saveTimer", "setTimeout", "setItem", "stringify", "clearTimeout", "fetchVehicles", "vehiclesList", "updatedVehicles", "plate", "plateNumber", "has", "Array", "from", "prevStats", "filter", "v", "fetchDeviceStats", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "success", "devicesData", "d", "handleBsmMessage", "bsmData", "partSpeed", "partLat", "partLong", "partHeading", "foundVehicle", "find", "checkOnlineStatus", "newOnlineBsmIds", "has<PERSON><PERSON><PERSON>", "size", "for<PERSON>ach", "lastTime", "timeSinceLastUpdate", "isOnline", "interval", "setInterval", "clearInterval", "resetAllVehicles", "loadData", "handleVehiclesDataChanged", "handleStorageChange", "key", "lastUpdated", "forcedPollingInterval", "fetchLatestVehiclesData", "currentIds", "id", "hasNewVehicle", "some", "apiPollingInterval", "chart", "handleResize", "lastUpdateTime", "existingChart", "getInstanceByDom", "dispose", "init", "updateChart", "currentTime", "eventTypes", "name", "color", "value", "itemStyle", "item", "sort", "a", "b", "option", "title", "text", "toLocaleTimeString", "left", "top", "textStyle", "fontSize", "grid", "bottom", "right", "containLabel", "animation", "animationDuration", "animationDurationUpdate", "animationEasingUpdate", "tooltip", "trigger", "axisPointer", "xAxis", "show", "splitLine", "yAxis", "axisLabel", "margin", "axisTick", "axisLine", "series", "<PERSON><PERSON><PERSON><PERSON>", "label", "position", "formatter", "borderRadius", "realtimeSort", "animationDelay", "idx", "setOption", "notMerge", "replaceMerge", "statsInterval", "_chart", "resize", "handleRsiMessage", "rsiData", "rtes", "warn", "latitude", "posLat", "longitude", "posLong", "eventType", "description", "time", "eventTypeText", "eventColor", "newEvent", "Math", "random", "rsuId", "location", "newEvents", "slice", "newStats", "handleVehicleSelect", "updatedSelectedVehicle", "vehicleColumns", "dataIndex", "width", "render", "renderEventList", "dataSource", "renderItem", "<PERSON><PERSON>", "style", "padding", "children", "Meta", "marginBottom", "marginRight", "maxHeight", "overflowY", "spinning", "tip", "collapsed", "onCollapse", "bordered", "gutter", "span", "valueStyle", "suffix", "ref", "columns", "<PERSON><PERSON><PERSON>", "pagination", "scroll", "y", "onRow", "record", "onClick", "cursor", "background", "column", "styles", "content"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RealTimeTraffic.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { Bar<PERSON>hart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\nimport vehiclesData from '../data/vehicles.json';\n\n// 注册必要的echarts组件\necharts.use([BarChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\n\nconst StyledCanvas = styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 24px' : \n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 0' : \n    props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n  \n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n\nconst RealTimeTraffic = () => {\n  const [loading, setLoading] = useState(true);\n  const [vehicles, setVehicles] = useState([]);\n  const [events, setEvents] = useState(() => {\n    try {\n      const savedEvents = localStorage.getItem('realTimeTrafficEvents');\n      return savedEvents ? JSON.parse(savedEvents) : [];\n    } catch (error) {\n      console.error('读取事件列表失败:', error);\n      return [];\n    }\n  });\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\n  const [stats, setStats] = useState({\n    totalVehicles: 0,\n    onlineVehicles: 0,\n    offlineVehicles: 0,\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0\n  });\n  \n  // 存储在线车辆的 BSM ID，初始时为空集合，表示所有车辆离线\n  const [onlineBsmIds, setOnlineBsmIds] = useState(new Set());\n  // 存储最后一次收到 BSM 消息的时间\n  const lastBsmTime = useRef({});\n  \n  const eventChartRef = useRef(null);\n  \n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n  \n  const [eventStats, setEventStats] = useState(() => {\n    try {\n      const savedStats = localStorage.getItem('realTimeTrafficEventStats');\n      return savedStats ? JSON.parse(savedStats) : {\n        '401': 0,  // 道路抛洒物\n        '404': 0,  // 道路障碍物\n        '405': 0,  // 行人通过马路\n        '904': 0,  // 逆行车辆\n        '910': 0,  // 违停车辆\n        '1002': 0, // 道路施工\n        '901': 0   // 车辆超速\n      };\n    } catch (error) {\n      console.error('读取事件统计数据失败:', error);\n      return {\n        '401': 0, '404': 0, '405': 0, '904': 0,\n        '910': 0, '1002': 0, '901': 0\n      };\n    }\n  });\n\n  // 添加手动更新车辆状态和位置信息的函数\n  const updateVehicleStatus = useCallback((bsmId, status, speed = 0, lat = 0, lng = 0, heading = 0) => {\n    // 确保速度和航向角是格式化的数值，保留两位小数\n    const formattedSpeed = parseFloat(speed).toFixed(2);\n    const formattedHeading = parseFloat(heading).toFixed(2);\n    \n    console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${formattedSpeed}, 位置=(${lat}, ${lng})`);\n    \n    // 更新在线状态\n    if (status === 'online') {\n      setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n      lastBsmTime.current[bsmId] = Date.now();\n    } else {\n      setOnlineBsmIds(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(bsmId);\n        return newSet;\n      });\n    }\n    \n    // 更新车辆信息\n    setVehicles(prevVehicles => \n      prevVehicles.map(vehicle => \n        vehicle.bsmId === bsmId \n          ? { \n              ...vehicle, \n              status: status,\n              speed: parseFloat(formattedSpeed), // 确保是数值类型\n              lat: parseFloat(lat.toFixed(7)),\n              lng: parseFloat(lng.toFixed(7)),\n              heading: parseFloat(formattedHeading) // 确保是数值类型\n            } \n          : vehicle\n      )\n    );\n  }, []);\n  \n  // 在组件挂载时，暴露updateVehicleStatus函数到window对象\n  useEffect(() => {\n    window.updateVehicleStatus = updateVehicleStatus;\n    \n    // 用于监听CampusModel是否接收到BSM消息的事件\n    const handleRealBsmReceived = (event) => {\n      if (event.data && event.data.type === 'realBsmReceived') {\n        console.log('收到CampusModel发送的真实BSM消息通知');\n      }\n    };\n\n    window.addEventListener('message', handleRealBsmReceived);\n    \n    // 确保页面刷新时清空在线车辆状态\n    console.log('组件初始化，重置所有车辆为离线状态');\n    setOnlineBsmIds(new Set());\n    lastBsmTime.current = {};\n    \n    return () => {\n      window.removeEventListener('message', handleRealBsmReceived);\n      delete window.updateVehicleStatus;\n    };\n  }, [updateVehicleStatus]);\n\n  // 使用防抖保存事件列表到 localStorage\n  useEffect(() => {\n    const saveTimer = setTimeout(() => {\n      try {\n        localStorage.setItem('realTimeTrafficEvents', JSON.stringify(events));\n      } catch (error) {\n        console.error('保存事件列表失败:', error);\n      }\n    }, 1000); // 延迟1秒保存，避免频繁写入\n    \n    return () => clearTimeout(saveTimer);\n  }, [events]);\n\n  // 使用防抖保存事件统计数据到 localStorage\n  useEffect(() => {\n    const saveTimer = setTimeout(() => {\n      try {\n        localStorage.setItem('realTimeTrafficEventStats', JSON.stringify(eventStats));\n      } catch (error) {\n        console.error('保存事件统计数据失败:', error);\n      }\n    }, 1000); // 延迟1秒保存，避免频繁写入\n    \n    return () => clearTimeout(saveTimer);\n  }, [eventStats]);\n\n  // 获取车辆数据\n  const fetchVehicles = () => {\n    try {\n      // 从本地 JSON 文件获取车辆数据\n      const vehiclesList = vehiclesData.vehicles || [];\n      \n      // 更新车辆数据，并根据 BSM ID 是否在线来设置状态\n      const updatedVehicles = vehiclesList.map(vehicle => ({\n        ...vehicle,\n        plate: vehicle.plateNumber, // 适配表格显示\n        status: onlineBsmIds.has(vehicle.bsmId) ? 'online' : 'offline', // 确保只有在onlineBsmIds中的车辆才显示为在线\n        speed: 0, // 初始速度设为 0，后续通过 BSM 消息更新\n        lat: 0,   // 初始位置设为 0，后续通过 BSM 消息更新\n        lng: 0,   // 初始位置设为 0，后续通过 BSM 消息更新\n        heading: 0 // 初始航向角设为 0，后续通过 BSM 消息更新\n      }));\n      \n      setVehicles(updatedVehicles);\n      \n      console.log('车辆列表已更新，在线车辆ID:', Array.from(onlineBsmIds));\n      \n      // 更新车辆统计数据\n      setStats(prevStats => ({\n        ...prevStats,\n        totalVehicles: updatedVehicles.length,\n        onlineVehicles: updatedVehicles.filter(v => v.status === 'online').length,\n        offlineVehicles: updatedVehicles.filter(v => v.status === 'offline').length\n      }));\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n    }\n  };\n\n  // 获取设备统计数据\n  const fetchDeviceStats = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      \n      if (response.data && response.data.success) {\n        const devicesData = response.data.data;\n        \n        // 更新设备统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalDevices: devicesData.length,\n          onlineDevices: devicesData.filter(d => d.status === 'online').length,\n          offlineDevices: devicesData.filter(d => d.status === 'offline').length\n        }));\n      }\n    } catch (error) {\n      console.error('获取设备统计数据失败:', error);\n    }\n  };\n\n  // 监听 BSM 消息\n  useEffect(() => {\n    const handleBsmMessage = (event) => {\n      if (event.data && event.data.type === 'bsm') {\n        // 获取bsmId，确保它正确地从消息中提取\n        const bsmData = event.data.data || {};\n        const bsmId = bsmData.bsmId || event.data.bsmId;\n        \n        if (!bsmId) {\n          console.error('BSM消息缺少bsmId:', event.data);\n          return;\n        }\n        \n        console.log('收到BSM消息，ID:', bsmId);\n        const now = Date.now();\n        \n        // 更新最后接收时间\n        lastBsmTime.current[bsmId] = now;\n        \n        // 添加到在线bsmId集合\n        setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n        \n        // 提取正确的BSM数据并格式化为两位小数\n        const speed = parseFloat((parseFloat(bsmData.partSpeed || event.data.speed || 0) * 3.6).toFixed(2)); // 转换为km/h，保留两位小数\n        const lat = parseFloat(parseFloat(bsmData.partLat || event.data.lat || 0).toFixed(7));\n        const lng = parseFloat(parseFloat(bsmData.partLong || event.data.lng || 0).toFixed(7));\n        const heading = parseFloat(parseFloat(bsmData.partHeading || event.data.heading || 0).toFixed(2)); // 保留两位小数\n        \n        console.log(`车辆${bsmId}状态: 速度=${speed.toFixed(2)}km/h, 位置=(${lat.toFixed(7)}, ${lng.toFixed(7)}), 航向=${heading.toFixed(2)}°`);\n        \n        // 更新车辆状态和位置信息\n        setVehicles(prevVehicles => {\n          // 检查是否找到对应车辆\n          const foundVehicle = prevVehicles.find(v => v.bsmId === bsmId);\n          if (!foundVehicle) {\n            console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);\n            return prevVehicles;\n          }\n          \n          return prevVehicles.map(vehicle => \n            vehicle.bsmId === bsmId \n              ? { \n                  ...vehicle, \n                  status: 'online',\n                  speed: speed,\n                  lat: lat,\n                  lng: lng,\n                  heading: heading\n                } \n              : vehicle\n          );\n        });\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleBsmMessage);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleBsmMessage);\n    };\n  }, []);\n\n  // 定期检查在线状态\n  useEffect(() => {\n    const checkOnlineStatus = () => {\n      const now = Date.now();\n      console.log('检查车辆在线状态...');\n      // 如果超过30秒没有收到BSM消息，则认为车辆离线\n      setOnlineBsmIds(prev => {\n        const newOnlineBsmIds = new Set(prev);\n        let hasChanges = false;\n        \n        // 记录当前所有在线的车辆ID\n        if (newOnlineBsmIds.size > 0) {\n          console.log('当前在线车辆ID:', Array.from(newOnlineBsmIds));\n        }\n        \n        newOnlineBsmIds.forEach(bsmId => {\n          const lastTime = lastBsmTime.current[bsmId] || 0;\n          const timeSinceLastUpdate = now - lastTime;\n          \n          if (timeSinceLastUpdate > 30000) {\n            console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);\n            newOnlineBsmIds.delete(bsmId);\n            hasChanges = true;\n          }\n        });\n        \n        if (hasChanges) {\n          // 更新车辆状态\n          setVehicles(prevVehicles => \n            prevVehicles.map(vehicle => {\n              const isOnline = newOnlineBsmIds.has(vehicle.bsmId);\n              return {\n                ...vehicle,\n                status: isOnline ? 'online' : 'offline'\n              };\n            })\n          );\n        }\n        \n        return newOnlineBsmIds;\n      });\n    };\n\n    const interval = setInterval(checkOnlineStatus, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // 重置所有车辆的初始状态\n  useEffect(() => {\n    // 将所有车辆状态重置为离线\n    const resetAllVehicles = () => {\n      // 只有在没有任何BSM消息的情况下才执行重置\n      if (onlineBsmIds.size === 0) {\n        setVehicles(prevVehicles => \n          prevVehicles.map(vehicle => ({\n            ...vehicle, \n            status: 'offline',\n            speed: 0,\n            lat: 0,\n            lng: 0,\n            heading: 0\n          }))\n        );\n        \n        console.log('已重置所有车辆为离线状态');\n      }\n    };\n    \n    // 初始执行一次\n    resetAllVehicles();\n    \n    // 然后每30秒检查一次\n    const interval = setInterval(resetAllVehicles, 30000);\n    \n    return () => clearInterval(interval);\n  }, [onlineBsmIds]);\n\n  // 在组件挂载时获取数据\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      fetchVehicles();\n      await fetchDeviceStats();\n      setLoading(false);\n    };\n    \n    loadData();\n    // 降低更新频率，避免频繁覆盖状态\n    const interval = setInterval(loadData, 60000); // 每60秒更新一次\n    return () => clearInterval(interval);\n  }, []);\n\n  // 添加对车辆数据变更的监听\n  useEffect(() => {\n    console.log('设置车辆数据变更监听');\n    \n    // 监听自定义事件\n    const handleVehiclesDataChanged = () => {\n      console.log('检测到车辆数据变更事件，重新获取车辆列表');\n      fetchVehicles();\n    };\n    \n    // 监听localStorage变化\n    const handleStorageChange = (event) => {\n      if (event.key === 'vehiclesLastUpdated' || event.key === 'vehiclesData') {\n        console.log('检测到localStorage变化，重新获取车辆列表');\n        fetchVehicles();\n      }\n    };\n    \n    // 添加事件监听器\n    window.addEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n    window.addEventListener('storage', handleStorageChange);\n    \n    // 初始检查是否有更新\n    const lastUpdated = localStorage.getItem('vehiclesLastUpdated');\n    if (lastUpdated) {\n      console.log('初始检查到vehiclesLastUpdated:', lastUpdated);\n      fetchVehicles();\n    }\n    \n    // 设置更频繁的强制轮询，确保在部署环境中也能获取最新数据\n    const forcedPollingInterval = setInterval(() => {\n      console.log('强制轮询：重新获取车辆列表');\n      fetchVehicles();\n    }, 10000); // 每10秒强制刷新一次\n    \n    // 清理函数\n    return () => {\n      window.removeEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n      window.removeEventListener('storage', handleStorageChange);\n      clearInterval(forcedPollingInterval);\n    };\n  }, []);\n\n  // 添加检查本地缓存与API数据是否一致的机制\n  // 定义一个单独的API调用函数，用于获取最新的车辆列表数据\n  const fetchLatestVehiclesData = async () => {\n    try {\n      const apiUrl = 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/vehicles/list`);\n      \n      if (response.data && response.data.vehicles) {\n        console.log('API返回的车辆数量:', response.data.vehicles.length);\n        \n        // 与当前列表比较\n        if (vehicles.length !== response.data.vehicles.length) {\n          console.log('检测到车辆数量变化，从', vehicles.length, '到', response.data.vehicles.length);\n          fetchVehicles(); // 重新加载\n          return;\n        }\n        \n        // 检查是否有新车辆ID\n        const currentIds = new Set(vehicles.map(v => v.id));\n        const hasNewVehicle = response.data.vehicles.some(v => !currentIds.has(v.id));\n        \n        if (hasNewVehicle) {\n          console.log('检测到新增车辆');\n          fetchVehicles(); // 重新加载\n        }\n      }\n    } catch (error) {\n      console.error('直接获取车辆列表失败:', error);\n    }\n  };\n\n  // 添加强制定期从API获取最新数据的机制\n  useEffect(() => {\n    const apiPollingInterval = setInterval(() => {\n      console.log('直接从API检查车辆数据更新');\n      fetchLatestVehiclesData();\n    }, 15000); // 每15秒检查一次API\n    \n    return () => clearInterval(apiPollingInterval);\n  }, [vehicles]);\n\n  // 添加自动选择第一个车辆的逻辑\n  useEffect(() => {\n    // 当车辆列表加载完成且有车辆数据时\n    if (vehicles.length > 0 && !selectedVehicle) {\n      // 自动选择第一个车辆\n      setSelectedVehicle(vehicles[0]);\n      console.log('已自动选择第一个车辆:', vehicles[0].plateNumber);\n    }\n  }, [vehicles, selectedVehicle]);\n  \n  // 修改图表初始化代码\n  useEffect(() => {\n    let chart = null;\n    let handleResize = null;\n    let lastUpdateTime = new Date();\n\n    // 确保 DOM 元素存在\n    if (!eventChartRef.current) {\n      console.error('图表容器未找到');\n      return;\n    }\n\n    // 检查并清理已存在的图表实例\n    let existingChart = echarts.getInstanceByDom(eventChartRef.current);\n    if (existingChart) {\n      existingChart.dispose();\n    }\n\n    try {\n      // 初始化新的图表实例\n      chart = echarts.init(eventChartRef.current);\n      \n      // 设置图表配置\n      const updateChart = () => {\n        const currentTime = new Date();\n        lastUpdateTime = currentTime;\n\n        // 事件类型配置\n        const eventTypes = [\n          { type: '401', name: '道路抛洒物', color: '#ff4d4f' },\n          { type: '404', name: '道路障碍物', color: '#faad14' },\n          { type: '405', name: '行人通过马路', color: '#1890ff' },\n          { type: '904', name: '逆行车辆', color: '#f5222d' },\n          { type: '910', name: '违停车辆', color: '#722ed1' },\n          { type: '1002', name: '道路施工', color: '#fa8c16' },\n          { type: '901', name: '车辆超速', color: '#eb2f96' }\n        ];\n\n        // 处理数据\n        const data = eventTypes\n          .map(event => ({\n            value: eventStats[event.type] || 0,\n            name: event.name,\n            itemStyle: { color: event.color }\n          }))\n          .filter(item => item.value > 0)\n          .sort((a, b) => b.value - a.value);\n\n        const option = {\n          title: {\n            text: `最后更新: ${currentTime.toLocaleTimeString()}`,\n            left: 'center',\n            top: -5,\n            textStyle: {\n              fontSize: 12,\n              color: '#999'\n            }\n          },\n          grid: {\n            top: 30,\n            bottom: 0,\n            left: 0,\n            right: 50,\n            containLabel: true\n          },\n          animation: true,\n          animationDuration: 0,\n          animationDurationUpdate: 1000,\n          animationEasingUpdate: 'quinticInOut',\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'shadow'\n            }\n          },\n          xAxis: {\n            type: 'value',\n            show: false,\n            splitLine: { show: false }\n          },\n          yAxis: {\n            type: 'category',\n            data: data.map(item => item.name),\n            axisLabel: {\n              fontSize: 12,\n              color: '#666',\n              margin: 8\n            },\n            axisTick: { show: false },\n            axisLine: { show: false }\n          },\n          series: [{\n            type: 'bar',\n            data: data,\n            barWidth: '50%',\n            label: {\n              show: true,\n              position: 'right',\n              formatter: '{c}次',\n              fontSize: 12,\n              color: '#666'\n            },\n            itemStyle: {\n              borderRadius: [0, 4, 4, 0]\n            },\n            realtimeSort: false,\n            animationDelay: function (idx) {\n              return idx * 100;\n            }\n          }]\n        };\n\n        // 使用 notMerge: false 来保持增量更新\n        chart.setOption(option, {\n          notMerge: false,\n          replaceMerge: ['series']\n        });\n      };\n\n      // 初始更新\n      updateChart();\n      \n      // 监听事件统计变化，每分钟更新一次\n      const statsInterval = setInterval(updateChart, 60000); // 60000ms = 1分钟\n      \n      // 监听窗口大小变化\n      handleResize = () => {\n        chart?.resize();\n      };\n      window.addEventListener('resize', handleResize);\n\n      // 清理函数\n      return () => {\n        clearInterval(statsInterval);\n        if (handleResize) {\n          window.removeEventListener('resize', handleResize);\n        }\n        if (chart) {\n          chart.dispose();\n        }\n      };\n\n    } catch (error) {\n      console.error('初始化图表失败:', error);\n    }\n  }, [eventStats]);\n  \n  // 修改 RSI 消息处理逻辑\n  useEffect(() => {\n    const handleRsiMessage = (event) => {\n      try {\n        if (event.data && event.data.type === 'RSI') {\n          console.log('RealTimeTraffic 收到 RSI 消息:', event.data);\n          \n          const rsiData = event.data.data;\n          if (!rsiData || !rsiData.rtes) {\n            console.warn('RSI 消息数据格式不正确:', rsiData);\n            return;\n          }\n\n          const events = rsiData.rtes;\n          const latitude = parseFloat(rsiData.posLat);\n          const longitude = parseFloat(rsiData.posLong);\n          \n          events.forEach(event => {\n            const eventType = event.eventType;\n            const description = event.description;\n            const time = new Date().toLocaleTimeString();\n            \n            // 获取事件类型的中文描述\n            let eventTypeText = '';\n            let eventColor = '';\n            switch(eventType) {\n              case '401': \n                eventTypeText = '道路抛洒物';\n                eventColor = '#ff4d4f';\n                break;\n              case '404': \n                eventTypeText = '道路障碍物';\n                eventColor = '#faad14';\n                break;\n              case '405': \n                eventTypeText = '行人通过马路';\n                eventColor = '#1890ff';\n                break;\n              case '904': \n                eventTypeText = '逆行车辆';\n                eventColor = '#f5222d';\n                break;\n              case '910': \n                eventTypeText = '违停车辆';\n                eventColor = '#722ed1';\n                break;\n              case '1002': \n                eventTypeText = '道路施工';\n                eventColor = '#fa8c16';\n                break;\n              case '901': \n                eventTypeText = '车辆超速';\n                eventColor = '#eb2f96';\n                break;\n              default: \n                eventTypeText = description || '未知事件';\n                eventColor = '#8c8c8c';\n            }\n            \n            // 更新事件列表\n            const newEvent = {\n              key: Date.now() + Math.random(),\n              type: eventTypeText,\n              time: time,\n              vehicle: rsiData.rsuId || '未知设备',\n              color: eventColor,\n              eventType: eventType,\n              location: {\n                latitude: latitude,\n                longitude: longitude\n              }\n            };\n            \n            setEvents(prev => {\n              const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n              console.log('更新后的事件列表:', newEvents);\n              return newEvents;\n            });\n            \n            // 更新事件统计\n            if (eventType) {\n              setEventStats(prev => {\n                const newStats = {\n                  ...prev,\n                  [eventType]: (prev[eventType] || 0) + 1\n                };\n                console.log('更新后的事件统计:', newStats);\n                return newStats;\n              });\n            }\n          });\n        }\n      } catch (error) {\n        console.error('处理 RSI 消息失败:', error);\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleRsiMessage);\n\n    return () => {\n      window.removeEventListener('message', handleRsiMessage);\n    };\n  }, []);\n  \n  // 处理车辆选择\n  const handleVehicleSelect = (vehicle) => {\n    console.log('选择车辆:', vehicle.plateNumber, '状态:', vehicle.status);\n    setSelectedVehicle(vehicle);\n  };\n  \n  // 修改车辆状态更新逻辑，确保同步更新selectedVehicle\n  useEffect(() => {\n    // 当车辆列表更新后，如果当前有选中的车辆，则更新选中的车辆信息\n    if (selectedVehicle) {\n      const updatedSelectedVehicle = vehicles.find(v => v.id === selectedVehicle.id);\n      if (updatedSelectedVehicle && \n          (updatedSelectedVehicle.status !== selectedVehicle.status || \n           updatedSelectedVehicle.speed !== selectedVehicle.speed ||\n           updatedSelectedVehicle.lat !== selectedVehicle.lat ||\n           updatedSelectedVehicle.lng !== selectedVehicle.lng ||\n           updatedSelectedVehicle.heading !== selectedVehicle.heading)) {\n        console.log(`更新选中车辆 ${selectedVehicle.plateNumber} 的信息:`, \n                   `状态从 ${selectedVehicle.status} 变为 ${updatedSelectedVehicle.status}`);\n        setSelectedVehicle(updatedSelectedVehicle);\n      }\n    }\n  }, [vehicles, selectedVehicle]);\n  \n  // 车辆列表列定义\n  const vehicleColumns = [\n    {\n      title: '车牌号',\n      dataIndex: 'plate',\n      key: 'plate',\n      width: '40%',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: '30%',\n      render: status => (\n        <Badge \n          status={status === 'online' ? 'success' : 'error'} \n          text={status === 'online' ? '在线' : '离线'} \n        />\n      ),\n    },\n    {\n      title: '速度',\n      dataIndex: 'speed',\n      key: 'speed',\n      width: '30%',\n      render: speed => `${typeof speed === 'number' ? speed.toFixed(2) : speed} km/h`,\n    }\n  ];\n  \n  // 修改实时事件列表的渲染\n  const renderEventList = () => (\n    <List\n      size=\"small\"\n      dataSource={events}\n      renderItem={item => (\n        <List.Item style={{ padding: '8px 0' }}>\n          <List.Item.Meta\n            title={\n              <div style={{ fontSize: '13px', marginBottom: '4px' }}>\n                <span style={{ color: item.color, marginRight: '8px' }}>\n                  {item.type}\n                </span>\n                <span style={{ color: '#666', fontSize: '12px' }}>\n                  {item.time}\n                </span>\n              </div>\n            }\n            description={\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                <div>设备: {item.vehicle}</div>\n                <div>位置: {item.location ? \n                  `${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}` : \n                  '未知位置'}\n                </div>\n              </div>\n            }\n          />\n        </List.Item>\n      )}\n      style={{\n        maxHeight: 'calc(100% - 24px)',\n        overflowY: 'auto'\n      }}\n    />\n  );\n  \n  return (\n    <Spin spinning={loading} tip=\"加载中...\">\n      <PageContainer>\n        {/* 左侧信息栏 */}\n        <CollapsibleSidebar \n          position=\"left\"\n          collapsed={leftCollapsed}\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\n        >\n          {/* 车辆和设备总数信息栏 */}\n          <InfoCard title=\"车辆和设备总数信息\" bordered={false} height=\"150px\">\n            <Row gutter={[8, 8]}>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"车辆总数\" \n                  value={stats.totalVehicles} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"在线车辆\" \n                  value={stats.onlineVehicles} \n                  suffix={`/ ${stats.totalVehicles}`} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"离线车辆\" \n                  value={stats.offlineVehicles} \n                  suffix={`/ ${stats.totalVehicles}`} \n                  valueStyle={{ color: '#cf1322' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"设备总数\" \n                  value={stats.totalDevices} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"在线设备\" \n                  value={stats.onlineDevices} \n                  suffix={`/ ${stats.totalDevices}`} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"离线设备\" \n                  value={stats.offlineDevices} \n                  suffix={`/ ${stats.totalDevices}`} \n                  valueStyle={{ color: '#cf1322' }} \n                />\n              </Col>\n            </Row>\n          </InfoCard>\n          \n          {/* 实时事件列表栏 */}\n          <InfoCard title=\"实时事件列表\" bordered={false} height=\"calc(50% - 81px)\">\n            {renderEventList()}\n          </InfoCard>\n          \n          {/* 实时事件统计栏 */}\n          <InfoCard title=\"实时事件统计\" bordered={false} height=\"calc(50% - 81px)\">\n            <div ref={eventChartRef} style={{ height: '100%', width: '100%' }}></div>\n          </InfoCard>\n        </CollapsibleSidebar>\n        \n        {/* 主内容区域 */}\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\n          {/* 主要内容 */}\n        </MainContent>\n        \n        {/* 右侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"right\"\n          collapsed={rightCollapsed}\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\n        >\n          {/* 车辆列表栏 */}\n          <InfoCard title=\"车辆列表\" bordered={false} height=\"50%\">\n            <Table \n              dataSource={vehicles} \n              columns={vehicleColumns} \n              rowKey=\"id\"\n              pagination={false}\n              size=\"small\"\n              scroll={{ y: 180 }}\n              onRow={(record) => ({\n                onClick: () => handleVehicleSelect(record),\n                style: { \n                  cursor: 'pointer',\n                  background: selectedVehicle?.id === record.id ? '#e6f7ff' : 'transparent',\n                  fontSize: '13px',\n                  padding: '4px 8px'\n                }\n              })}\n            />\n          </InfoCard>\n          \n          {/* 车辆详细信息栏 */}\n          <InfoCard title=\"车辆详细信息\" bordered={false} height=\"50%\">\n            {selectedVehicle ? (\n              <Descriptions \n                bordered \n                column={1} \n                size=\"small\"\n                styles={{\n                  label: { fontSize: '13px', padding: '4px 8px' },\n                  content: { fontSize: '13px', padding: '4px 8px' }\n                }}\n              >\n                <Descriptions.Item label=\"车牌号\">{selectedVehicle.plateNumber}</Descriptions.Item>\n                <Descriptions.Item label=\"状态\">\n                  <Badge \n                    status={selectedVehicle.status === 'online' ? 'success' : 'error'} \n                    text={selectedVehicle.status === 'online' ? '在线' : '离线'} \n                  />\n                </Descriptions.Item>\n                <Descriptions.Item label=\"经度\">\n                  {selectedVehicle.status === 'online' ? selectedVehicle.lng.toFixed(7) : 'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"纬度\">\n                  {selectedVehicle.status === 'online' ? selectedVehicle.lat.toFixed(7) : 'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"速度\">\n                  {selectedVehicle.status === 'online' ? \n                    `${typeof selectedVehicle.speed === 'number' ? selectedVehicle.speed.toFixed(2) : selectedVehicle.speed} km/h` : \n                    'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"航向角\">\n                  {selectedVehicle.status === 'online' ? \n                    `${typeof selectedVehicle.heading === 'number' ? selectedVehicle.heading.toFixed(2) : selectedVehicle.heading}°` : \n                    'N/A'}\n                </Descriptions.Item>\n              </Descriptions>\n            ) : (\n              <p style={{ fontSize: '13px' }}>请选择车辆查看详细信息</p>\n            )}\n          </InfoCard>\n        </CollapsibleSidebar>\n      </PageContainer>\n    </Spin>\n  );\n};\n\nexport default RealTimeTraffic;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,OAASC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,SAAS,CAAEC,IAAI,CAAEC,KAAK,CAAEC,YAAY,CAAEC,IAAI,CAAEC,KAAK,KAAQ,MAAM,CACxF,MAAO,GAAK,CAAAC,OAAO,KAAM,cAAc,CACvC,OAASC,QAAQ,KAAQ,gBAAgB,CACzC,OAASC,aAAa,CAAEC,gBAAgB,CAAEC,eAAe,CAAEC,cAAc,KAAQ,oBAAoB,CACrG,OAASC,cAAc,KAAQ,mBAAmB,CAClD,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,kBAAkB,KAAM,yCAAyC,CACxE,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,YAAY,KAAM,uBAAuB,CAEhD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACAd,OAAO,CAACe,GAAG,CAAC,CAACd,QAAQ,CAAEC,aAAa,CAAEC,gBAAgB,CAAEC,eAAe,CAAEC,cAAc,CAAEC,cAAc,CAAC,CAAC,CAEzG,KAAM,CAAAU,YAAY,CAAGT,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAGX,MAAM,CAACU,GAAG;AAChC;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAE,WAAW,CAAGZ,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAG,YAAY,CAAGb,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAI,WAAW,CAAGd,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA,aAAaK,KAAK,EAAIA,KAAK,CAACC,aAAa,EAAID,KAAK,CAACE,cAAc,CAAG,QAAQ,CACxEF,KAAK,CAACC,aAAa,CAAG,cAAc,CACpCD,KAAK,CAACE,cAAc,CAAG,cAAc,CAAG,OAAO;AACnD;AACA,YAAYF,KAAK,EAAIA,KAAK,CAACC,aAAa,EAAID,KAAK,CAACE,cAAc,CAAG,QAAQ,CACvEF,KAAK,CAACC,aAAa,CAAG,WAAW,CACjCD,KAAK,CAACE,cAAc,CAAG,WAAW,CAAG,GAAG;AAC5C;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,QAAQ,CAAGlB,MAAM,CAACd,IAAI,CAAC;AAC7B;AACA,YAAY6B,KAAK,EAAIA,KAAK,CAACI,MAAM,EAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAGpB,MAAM,CAACb,SAAS,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAkC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG3C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC4C,QAAQ,CAAEC,WAAW,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC8C,MAAM,CAAEC,SAAS,CAAC,CAAG/C,QAAQ,CAAC,IAAM,CACzC,GAAI,CACF,KAAM,CAAAgD,WAAW,CAAGC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC,CACjE,MAAO,CAAAF,WAAW,CAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,CAAG,EAAE,CACnD,CAAE,MAAOK,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,MAAO,EAAE,CACX,CACF,CAAC,CAAC,CACF,KAAM,CAACE,eAAe,CAAEC,kBAAkB,CAAC,CAAGxD,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAACyD,KAAK,CAAEC,QAAQ,CAAC,CAAG1D,QAAQ,CAAC,CACjC2D,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAAC,CACjBC,eAAe,CAAE,CAAC,CAClBC,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAClB,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGlE,QAAQ,CAAC,GAAI,CAAAmE,GAAG,CAAC,CAAC,CAAC,CAC3D;AACA,KAAM,CAAAC,WAAW,CAAGlE,MAAM,CAAC,CAAC,CAAC,CAAC,CAE9B,KAAM,CAAAmE,aAAa,CAAGnE,MAAM,CAAC,IAAI,CAAC,CAElC;AACA,KAAM,CAACkC,aAAa,CAAEkC,gBAAgB,CAAC,CAAGtE,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACqC,cAAc,CAAEkC,iBAAiB,CAAC,CAAGvE,QAAQ,CAAC,KAAK,CAAC,CAE3D,KAAM,CAACwE,UAAU,CAAEC,aAAa,CAAC,CAAGzE,QAAQ,CAAC,IAAM,CACjD,GAAI,CACF,KAAM,CAAA0E,UAAU,CAAGzB,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC,CACpE,MAAO,CAAAwB,UAAU,CAAGvB,IAAI,CAACC,KAAK,CAACsB,UAAU,CAAC,CAAG,CAC3C,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,MAAM,CAAE,CAAC,CAAE;AACX,KAAK,CAAE,CAAI;AACb,CAAC,CACH,CAAE,MAAOrB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnC,MAAO,CACL,KAAK,CAAE,CAAC,CAAE,KAAK,CAAE,CAAC,CAAE,KAAK,CAAE,CAAC,CAAE,KAAK,CAAE,CAAC,CACtC,KAAK,CAAE,CAAC,CAAE,MAAM,CAAE,CAAC,CAAE,KAAK,CAAE,CAC9B,CAAC,CACH,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAsB,mBAAmB,CAAGxE,WAAW,CAAC,SAACyE,KAAK,CAAEC,MAAM,CAA+C,IAA7C,CAAAC,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAG,GAAG,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAI,GAAG,CAAAJ,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAK,OAAO,CAAAL,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAC9F;AACA,KAAM,CAAAM,cAAc,CAAGC,UAAU,CAACR,KAAK,CAAC,CAACS,OAAO,CAAC,CAAC,CAAC,CACnD,KAAM,CAAAC,gBAAgB,CAAGF,UAAU,CAACF,OAAO,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAEvDjC,OAAO,CAACmC,GAAG,CAAC,gBAAgBb,KAAK,QAAQC,MAAM,QAAQQ,cAAc,SAASH,GAAG,KAAKC,GAAG,GAAG,CAAC,CAE7F;AACA,GAAIN,MAAM,GAAK,QAAQ,CAAE,CACvBX,eAAe,CAACwB,IAAI,EAAI,GAAI,CAAAvB,GAAG,CAAC,CAAC,GAAGuB,IAAI,CAAEd,KAAK,CAAC,CAAC,CAAC,CAClDR,WAAW,CAACuB,OAAO,CAACf,KAAK,CAAC,CAAGgB,IAAI,CAACC,GAAG,CAAC,CAAC,CACzC,CAAC,IAAM,CACL3B,eAAe,CAACwB,IAAI,EAAI,CACtB,KAAM,CAAAI,MAAM,CAAG,GAAI,CAAA3B,GAAG,CAACuB,IAAI,CAAC,CAC5BI,MAAM,CAACC,MAAM,CAACnB,KAAK,CAAC,CACpB,MAAO,CAAAkB,MAAM,CACf,CAAC,CAAC,CACJ,CAEA;AACAjD,WAAW,CAACmD,YAAY,EACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,EACtBA,OAAO,CAACtB,KAAK,GAAKA,KAAK,CACnB,CACE,GAAGsB,OAAO,CACVrB,MAAM,CAAEA,MAAM,CACdC,KAAK,CAAEQ,UAAU,CAACD,cAAc,CAAC,CAAE;AACnCH,GAAG,CAAEI,UAAU,CAACJ,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC,CAC/BJ,GAAG,CAAEG,UAAU,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CAC/BH,OAAO,CAAEE,UAAU,CAACE,gBAAgB,CAAE;AACxC,CAAC,CACDU,OACN,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACAjG,SAAS,CAAC,IAAM,CACdkG,MAAM,CAACxB,mBAAmB,CAAGA,mBAAmB,CAEhD;AACA,KAAM,CAAAyB,qBAAqB,CAAIC,KAAK,EAAK,CACvC,GAAIA,KAAK,CAACC,IAAI,EAAID,KAAK,CAACC,IAAI,CAACC,IAAI,GAAK,iBAAiB,CAAE,CACvDjD,OAAO,CAACmC,GAAG,CAAC,2BAA2B,CAAC,CAC1C,CACF,CAAC,CAEDU,MAAM,CAACK,gBAAgB,CAAC,SAAS,CAAEJ,qBAAqB,CAAC,CAEzD;AACA9C,OAAO,CAACmC,GAAG,CAAC,mBAAmB,CAAC,CAChCvB,eAAe,CAAC,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAC,CAC1BC,WAAW,CAACuB,OAAO,CAAG,CAAC,CAAC,CAExB,MAAO,IAAM,CACXQ,MAAM,CAACM,mBAAmB,CAAC,SAAS,CAAEL,qBAAqB,CAAC,CAC5D,MAAO,CAAAD,MAAM,CAACxB,mBAAmB,CACnC,CAAC,CACH,CAAC,CAAE,CAACA,mBAAmB,CAAC,CAAC,CAEzB;AACA1E,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyG,SAAS,CAAGC,UAAU,CAAC,IAAM,CACjC,GAAI,CACF1D,YAAY,CAAC2D,OAAO,CAAC,uBAAuB,CAAEzD,IAAI,CAAC0D,SAAS,CAAC/D,MAAM,CAAC,CAAC,CACvE,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACnC,CACF,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAMyD,YAAY,CAACJ,SAAS,CAAC,CACtC,CAAC,CAAE,CAAC5D,MAAM,CAAC,CAAC,CAEZ;AACA7C,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyG,SAAS,CAAGC,UAAU,CAAC,IAAM,CACjC,GAAI,CACF1D,YAAY,CAAC2D,OAAO,CAAC,2BAA2B,CAAEzD,IAAI,CAAC0D,SAAS,CAACrC,UAAU,CAAC,CAAC,CAC/E,CAAE,MAAOnB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACrC,CACF,CAAC,CAAE,IAAI,CAAC,CAAE;AAEV,MAAO,IAAMyD,YAAY,CAACJ,SAAS,CAAC,CACtC,CAAC,CAAE,CAAClC,UAAU,CAAC,CAAC,CAEhB;AACA,KAAM,CAAAuC,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAI,CACF;AACA,KAAM,CAAAC,YAAY,CAAGzF,YAAY,CAACqB,QAAQ,EAAI,EAAE,CAEhD;AACA,KAAM,CAAAqE,eAAe,CAAGD,YAAY,CAACf,GAAG,CAACC,OAAO,GAAK,CACnD,GAAGA,OAAO,CACVgB,KAAK,CAAEhB,OAAO,CAACiB,WAAW,CAAE;AAC5BtC,MAAM,CAAEZ,YAAY,CAACmD,GAAG,CAAClB,OAAO,CAACtB,KAAK,CAAC,CAAG,QAAQ,CAAG,SAAS,CAAE;AAChEE,KAAK,CAAE,CAAC,CAAE;AACVI,GAAG,CAAE,CAAC,CAAI;AACVC,GAAG,CAAE,CAAC,CAAI;AACVC,OAAO,CAAE,CAAE;AACb,CAAC,CAAC,CAAC,CAEHvC,WAAW,CAACoE,eAAe,CAAC,CAE5B3D,OAAO,CAACmC,GAAG,CAAC,iBAAiB,CAAE4B,KAAK,CAACC,IAAI,CAACrD,YAAY,CAAC,CAAC,CAExD;AACAP,QAAQ,CAAC6D,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZ5D,aAAa,CAAEsD,eAAe,CAACjC,MAAM,CACrCpB,cAAc,CAAEqD,eAAe,CAACO,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC5C,MAAM,GAAK,QAAQ,CAAC,CAACG,MAAM,CACzEnB,eAAe,CAAEoD,eAAe,CAACO,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC5C,MAAM,GAAK,SAAS,CAAC,CAACG,MACvE,CAAC,CAAC,CAAC,CACL,CAAE,MAAO3B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACnC,CACF,CAAC,CAED;AACA,KAAM,CAAAqE,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAAC,MAAM,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACvE,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAzG,KAAK,CAAC0G,GAAG,CAAC,GAAGL,MAAM,cAAc,CAAC,CAEzD,GAAII,QAAQ,CAACzB,IAAI,EAAIyB,QAAQ,CAACzB,IAAI,CAAC2B,OAAO,CAAE,CAC1C,KAAM,CAAAC,WAAW,CAAGH,QAAQ,CAACzB,IAAI,CAACA,IAAI,CAEtC;AACA5C,QAAQ,CAAC6D,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZzD,YAAY,CAAEoE,WAAW,CAAClD,MAAM,CAChCjB,aAAa,CAAEmE,WAAW,CAACV,MAAM,CAACW,CAAC,EAAIA,CAAC,CAACtD,MAAM,GAAK,QAAQ,CAAC,CAACG,MAAM,CACpEhB,cAAc,CAAEkE,WAAW,CAACV,MAAM,CAACW,CAAC,EAAIA,CAAC,CAACtD,MAAM,GAAK,SAAS,CAAC,CAACG,MAClE,CAAC,CAAC,CAAC,CACL,CACF,CAAE,MAAO3B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACrC,CACF,CAAC,CAED;AACApD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmI,gBAAgB,CAAI/B,KAAK,EAAK,CAClC,GAAIA,KAAK,CAACC,IAAI,EAAID,KAAK,CAACC,IAAI,CAACC,IAAI,GAAK,KAAK,CAAE,CAC3C;AACA,KAAM,CAAA8B,OAAO,CAAGhC,KAAK,CAACC,IAAI,CAACA,IAAI,EAAI,CAAC,CAAC,CACrC,KAAM,CAAA1B,KAAK,CAAGyD,OAAO,CAACzD,KAAK,EAAIyB,KAAK,CAACC,IAAI,CAAC1B,KAAK,CAE/C,GAAI,CAACA,KAAK,CAAE,CACVtB,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEgD,KAAK,CAACC,IAAI,CAAC,CAC1C,OACF,CAEAhD,OAAO,CAACmC,GAAG,CAAC,aAAa,CAAEb,KAAK,CAAC,CACjC,KAAM,CAAAiB,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CAEtB;AACAzB,WAAW,CAACuB,OAAO,CAACf,KAAK,CAAC,CAAGiB,GAAG,CAEhC;AACA3B,eAAe,CAACwB,IAAI,EAAI,GAAI,CAAAvB,GAAG,CAAC,CAAC,GAAGuB,IAAI,CAAEd,KAAK,CAAC,CAAC,CAAC,CAElD;AACA,KAAM,CAAAE,KAAK,CAAGQ,UAAU,CAAC,CAACA,UAAU,CAAC+C,OAAO,CAACC,SAAS,EAAIjC,KAAK,CAACC,IAAI,CAACxB,KAAK,EAAI,CAAC,CAAC,CAAG,GAAG,EAAES,OAAO,CAAC,CAAC,CAAC,CAAC,CAAE;AACrG,KAAM,CAAAL,GAAG,CAAGI,UAAU,CAACA,UAAU,CAAC+C,OAAO,CAACE,OAAO,EAAIlC,KAAK,CAACC,IAAI,CAACpB,GAAG,EAAI,CAAC,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC,CACrF,KAAM,CAAAJ,GAAG,CAAGG,UAAU,CAACA,UAAU,CAAC+C,OAAO,CAACG,QAAQ,EAAInC,KAAK,CAACC,IAAI,CAACnB,GAAG,EAAI,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CACtF,KAAM,CAAAH,OAAO,CAAGE,UAAU,CAACA,UAAU,CAAC+C,OAAO,CAACI,WAAW,EAAIpC,KAAK,CAACC,IAAI,CAAClB,OAAO,EAAI,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAE;AAEnGjC,OAAO,CAACmC,GAAG,CAAC,KAAKb,KAAK,UAAUE,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,aAAaL,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,KAAKJ,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,SAASH,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAE7H;AACA1C,WAAW,CAACmD,YAAY,EAAI,CAC1B;AACA,KAAM,CAAA0C,YAAY,CAAG1C,YAAY,CAAC2C,IAAI,CAAClB,CAAC,EAAIA,CAAC,CAAC7C,KAAK,GAAKA,KAAK,CAAC,CAC9D,GAAI,CAAC8D,YAAY,CAAE,CACjBpF,OAAO,CAACmC,GAAG,CAAC,eAAeb,KAAK,WAAW,CAAC,CAC5C,MAAO,CAAAoB,YAAY,CACrB,CAEA,MAAO,CAAAA,YAAY,CAACC,GAAG,CAACC,OAAO,EAC7BA,OAAO,CAACtB,KAAK,GAAKA,KAAK,CACnB,CACE,GAAGsB,OAAO,CACVrB,MAAM,CAAE,QAAQ,CAChBC,KAAK,CAAEA,KAAK,CACZI,GAAG,CAAEA,GAAG,CACRC,GAAG,CAAEA,GAAG,CACRC,OAAO,CAAEA,OACX,CAAC,CACDc,OACN,CAAC,CACH,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACAC,MAAM,CAACK,gBAAgB,CAAC,SAAS,CAAE4B,gBAAgB,CAAC,CAEpD;AACA,MAAO,IAAM,CACXjC,MAAM,CAACM,mBAAmB,CAAC,SAAS,CAAE2B,gBAAgB,CAAC,CACzD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACAnI,SAAS,CAAC,IAAM,CACd,KAAM,CAAA2I,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAA/C,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CACtBvC,OAAO,CAACmC,GAAG,CAAC,aAAa,CAAC,CAC1B;AACAvB,eAAe,CAACwB,IAAI,EAAI,CACtB,KAAM,CAAAmD,eAAe,CAAG,GAAI,CAAA1E,GAAG,CAACuB,IAAI,CAAC,CACrC,GAAI,CAAAoD,UAAU,CAAG,KAAK,CAEtB;AACA,GAAID,eAAe,CAACE,IAAI,CAAG,CAAC,CAAE,CAC5BzF,OAAO,CAACmC,GAAG,CAAC,WAAW,CAAE4B,KAAK,CAACC,IAAI,CAACuB,eAAe,CAAC,CAAC,CACvD,CAEAA,eAAe,CAACG,OAAO,CAACpE,KAAK,EAAI,CAC/B,KAAM,CAAAqE,QAAQ,CAAG7E,WAAW,CAACuB,OAAO,CAACf,KAAK,CAAC,EAAI,CAAC,CAChD,KAAM,CAAAsE,mBAAmB,CAAGrD,GAAG,CAAGoD,QAAQ,CAE1C,GAAIC,mBAAmB,CAAG,KAAK,CAAE,CAC/B5F,OAAO,CAACmC,GAAG,CAAC,KAAKb,KAAK,kBAAkB,CAAC,CACzCiE,eAAe,CAAC9C,MAAM,CAACnB,KAAK,CAAC,CAC7BkE,UAAU,CAAG,IAAI,CACnB,CACF,CAAC,CAAC,CAEF,GAAIA,UAAU,CAAE,CACd;AACAjG,WAAW,CAACmD,YAAY,EACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,EAAI,CAC1B,KAAM,CAAAiD,QAAQ,CAAGN,eAAe,CAACzB,GAAG,CAAClB,OAAO,CAACtB,KAAK,CAAC,CACnD,MAAO,CACL,GAAGsB,OAAO,CACVrB,MAAM,CAAEsE,QAAQ,CAAG,QAAQ,CAAG,SAChC,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAEA,MAAO,CAAAN,eAAe,CACxB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAO,QAAQ,CAAGC,WAAW,CAACT,iBAAiB,CAAE,IAAI,CAAC,CACrD,MAAO,IAAMU,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN;AACAnJ,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAsJ,gBAAgB,CAAGA,CAAA,GAAM,CAC7B;AACA,GAAItF,YAAY,CAAC8E,IAAI,GAAK,CAAC,CAAE,CAC3BlG,WAAW,CAACmD,YAAY,EACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,GAAK,CAC3B,GAAGA,OAAO,CACVrB,MAAM,CAAE,SAAS,CACjBC,KAAK,CAAE,CAAC,CACRI,GAAG,CAAE,CAAC,CACNC,GAAG,CAAE,CAAC,CACNC,OAAO,CAAE,CACX,CAAC,CAAC,CACJ,CAAC,CAED9B,OAAO,CAACmC,GAAG,CAAC,cAAc,CAAC,CAC7B,CACF,CAAC,CAED;AACA8D,gBAAgB,CAAC,CAAC,CAElB;AACA,KAAM,CAAAH,QAAQ,CAAGC,WAAW,CAACE,gBAAgB,CAAE,KAAK,CAAC,CAErD,MAAO,IAAMD,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,CAACnF,YAAY,CAAC,CAAC,CAElB;AACAhE,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuJ,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B7G,UAAU,CAAC,IAAI,CAAC,CAChBoE,aAAa,CAAC,CAAC,CACf,KAAM,CAAAW,gBAAgB,CAAC,CAAC,CACxB/E,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED6G,QAAQ,CAAC,CAAC,CACV;AACA,KAAM,CAAAJ,QAAQ,CAAGC,WAAW,CAACG,QAAQ,CAAE,KAAK,CAAC,CAAE;AAC/C,MAAO,IAAMF,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN;AACAnJ,SAAS,CAAC,IAAM,CACdqD,OAAO,CAACmC,GAAG,CAAC,YAAY,CAAC,CAEzB;AACA,KAAM,CAAAgE,yBAAyB,CAAGA,CAAA,GAAM,CACtCnG,OAAO,CAACmC,GAAG,CAAC,sBAAsB,CAAC,CACnCsB,aAAa,CAAC,CAAC,CACjB,CAAC,CAED;AACA,KAAM,CAAA2C,mBAAmB,CAAIrD,KAAK,EAAK,CACrC,GAAIA,KAAK,CAACsD,GAAG,GAAK,qBAAqB,EAAItD,KAAK,CAACsD,GAAG,GAAK,cAAc,CAAE,CACvErG,OAAO,CAACmC,GAAG,CAAC,4BAA4B,CAAC,CACzCsB,aAAa,CAAC,CAAC,CACjB,CACF,CAAC,CAED;AACAZ,MAAM,CAACK,gBAAgB,CAAC,qBAAqB,CAAEiD,yBAAyB,CAAC,CACzEtD,MAAM,CAACK,gBAAgB,CAAC,SAAS,CAAEkD,mBAAmB,CAAC,CAEvD;AACA,KAAM,CAAAE,WAAW,CAAG3G,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAC/D,GAAI0G,WAAW,CAAE,CACftG,OAAO,CAACmC,GAAG,CAAC,2BAA2B,CAAEmE,WAAW,CAAC,CACrD7C,aAAa,CAAC,CAAC,CACjB,CAEA;AACA,KAAM,CAAA8C,qBAAqB,CAAGR,WAAW,CAAC,IAAM,CAC9C/F,OAAO,CAACmC,GAAG,CAAC,eAAe,CAAC,CAC5BsB,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX;AACA,MAAO,IAAM,CACXZ,MAAM,CAACM,mBAAmB,CAAC,qBAAqB,CAAEgD,yBAAyB,CAAC,CAC5EtD,MAAM,CAACM,mBAAmB,CAAC,SAAS,CAAEiD,mBAAmB,CAAC,CAC1DJ,aAAa,CAACO,qBAAqB,CAAC,CACtC,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA;AACA,KAAM,CAAAC,uBAAuB,CAAG,KAAAA,CAAA,GAAY,CAC1C,GAAI,CACF,KAAM,CAAAnC,MAAM,CAAG,uBAAuB,CACtC,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAAzG,KAAK,CAAC0G,GAAG,CAAC,GAAGL,MAAM,oBAAoB,CAAC,CAE/D,GAAII,QAAQ,CAACzB,IAAI,EAAIyB,QAAQ,CAACzB,IAAI,CAAC1D,QAAQ,CAAE,CAC3CU,OAAO,CAACmC,GAAG,CAAC,aAAa,CAAEsC,QAAQ,CAACzB,IAAI,CAAC1D,QAAQ,CAACoC,MAAM,CAAC,CAEzD;AACA,GAAIpC,QAAQ,CAACoC,MAAM,GAAK+C,QAAQ,CAACzB,IAAI,CAAC1D,QAAQ,CAACoC,MAAM,CAAE,CACrD1B,OAAO,CAACmC,GAAG,CAAC,aAAa,CAAE7C,QAAQ,CAACoC,MAAM,CAAE,GAAG,CAAE+C,QAAQ,CAACzB,IAAI,CAAC1D,QAAQ,CAACoC,MAAM,CAAC,CAC/E+B,aAAa,CAAC,CAAC,CAAE;AACjB,OACF,CAEA;AACA,KAAM,CAAAgD,UAAU,CAAG,GAAI,CAAA5F,GAAG,CAACvB,QAAQ,CAACqD,GAAG,CAACwB,CAAC,EAAIA,CAAC,CAACuC,EAAE,CAAC,CAAC,CACnD,KAAM,CAAAC,aAAa,CAAGlC,QAAQ,CAACzB,IAAI,CAAC1D,QAAQ,CAACsH,IAAI,CAACzC,CAAC,EAAI,CAACsC,UAAU,CAAC3C,GAAG,CAACK,CAAC,CAACuC,EAAE,CAAC,CAAC,CAE7E,GAAIC,aAAa,CAAE,CACjB3G,OAAO,CAACmC,GAAG,CAAC,SAAS,CAAC,CACtBsB,aAAa,CAAC,CAAC,CAAE;AACnB,CACF,CACF,CAAE,MAAO1D,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACrC,CACF,CAAC,CAED;AACApD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAkK,kBAAkB,CAAGd,WAAW,CAAC,IAAM,CAC3C/F,OAAO,CAACmC,GAAG,CAAC,gBAAgB,CAAC,CAC7BqE,uBAAuB,CAAC,CAAC,CAC3B,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX,MAAO,IAAMR,aAAa,CAACa,kBAAkB,CAAC,CAChD,CAAC,CAAE,CAACvH,QAAQ,CAAC,CAAC,CAEd;AACA3C,SAAS,CAAC,IAAM,CACd;AACA,GAAI2C,QAAQ,CAACoC,MAAM,CAAG,CAAC,EAAI,CAACzB,eAAe,CAAE,CAC3C;AACAC,kBAAkB,CAACZ,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC/BU,OAAO,CAACmC,GAAG,CAAC,aAAa,CAAE7C,QAAQ,CAAC,CAAC,CAAC,CAACuE,WAAW,CAAC,CACrD,CACF,CAAC,CAAE,CAACvE,QAAQ,CAAEW,eAAe,CAAC,CAAC,CAE/B;AACAtD,SAAS,CAAC,IAAM,CACd,GAAI,CAAAmK,KAAK,CAAG,IAAI,CAChB,GAAI,CAAAC,YAAY,CAAG,IAAI,CACvB,GAAI,CAAAC,cAAc,CAAG,GAAI,CAAA1E,IAAI,CAAC,CAAC,CAE/B;AACA,GAAI,CAACvB,aAAa,CAACsB,OAAO,CAAE,CAC1BrC,OAAO,CAACD,KAAK,CAAC,SAAS,CAAC,CACxB,OACF,CAEA;AACA,GAAI,CAAAkH,aAAa,CAAG1J,OAAO,CAAC2J,gBAAgB,CAACnG,aAAa,CAACsB,OAAO,CAAC,CACnE,GAAI4E,aAAa,CAAE,CACjBA,aAAa,CAACE,OAAO,CAAC,CAAC,CACzB,CAEA,GAAI,CACF;AACAL,KAAK,CAAGvJ,OAAO,CAAC6J,IAAI,CAACrG,aAAa,CAACsB,OAAO,CAAC,CAE3C;AACA,KAAM,CAAAgF,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAhF,IAAI,CAAC,CAAC,CAC9B0E,cAAc,CAAGM,WAAW,CAE5B;AACA,KAAM,CAAAC,UAAU,CAAG,CACjB,CAAEtE,IAAI,CAAE,KAAK,CAAEuE,IAAI,CAAE,OAAO,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAAExE,IAAI,CAAE,KAAK,CAAEuE,IAAI,CAAE,OAAO,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAAExE,IAAI,CAAE,KAAK,CAAEuE,IAAI,CAAE,QAAQ,CAAEC,KAAK,CAAE,SAAU,CAAC,CACjD,CAAExE,IAAI,CAAE,KAAK,CAAEuE,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC/C,CAAExE,IAAI,CAAE,KAAK,CAAEuE,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC/C,CAAExE,IAAI,CAAE,MAAM,CAAEuE,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAAExE,IAAI,CAAE,KAAK,CAAEuE,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAED;AACA,KAAM,CAAAzE,IAAI,CAAGuE,UAAU,CACpB5E,GAAG,CAACI,KAAK,GAAK,CACb2E,KAAK,CAAExG,UAAU,CAAC6B,KAAK,CAACE,IAAI,CAAC,EAAI,CAAC,CAClCuE,IAAI,CAAEzE,KAAK,CAACyE,IAAI,CAChBG,SAAS,CAAE,CAAEF,KAAK,CAAE1E,KAAK,CAAC0E,KAAM,CAClC,CAAC,CAAC,CAAC,CACFvD,MAAM,CAAC0D,IAAI,EAAIA,IAAI,CAACF,KAAK,CAAG,CAAC,CAAC,CAC9BG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAACL,KAAK,CAAGI,CAAC,CAACJ,KAAK,CAAC,CAEpC,KAAM,CAAAM,MAAM,CAAG,CACbC,KAAK,CAAE,CACLC,IAAI,CAAE,SAASZ,WAAW,CAACa,kBAAkB,CAAC,CAAC,EAAE,CACjDC,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,CAAC,CAAC,CACPC,SAAS,CAAE,CACTC,QAAQ,CAAE,EAAE,CACZd,KAAK,CAAE,MACT,CACF,CAAC,CACDe,IAAI,CAAE,CACJH,GAAG,CAAE,EAAE,CACPI,MAAM,CAAE,CAAC,CACTL,IAAI,CAAE,CAAC,CACPM,KAAK,CAAE,EAAE,CACTC,YAAY,CAAE,IAChB,CAAC,CACDC,SAAS,CAAE,IAAI,CACfC,iBAAiB,CAAE,CAAC,CACpBC,uBAAuB,CAAE,IAAI,CAC7BC,qBAAqB,CAAE,cAAc,CACrCC,OAAO,CAAE,CACPC,OAAO,CAAE,MAAM,CACfC,WAAW,CAAE,CACXjG,IAAI,CAAE,QACR,CACF,CAAC,CACDkG,KAAK,CAAE,CACLlG,IAAI,CAAE,OAAO,CACbmG,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,CAAED,IAAI,CAAE,KAAM,CAC3B,CAAC,CACDE,KAAK,CAAE,CACLrG,IAAI,CAAE,UAAU,CAChBD,IAAI,CAAEA,IAAI,CAACL,GAAG,CAACiF,IAAI,EAAIA,IAAI,CAACJ,IAAI,CAAC,CACjC+B,SAAS,CAAE,CACThB,QAAQ,CAAE,EAAE,CACZd,KAAK,CAAE,MAAM,CACb+B,MAAM,CAAE,CACV,CAAC,CACDC,QAAQ,CAAE,CAAEL,IAAI,CAAE,KAAM,CAAC,CACzBM,QAAQ,CAAE,CAAEN,IAAI,CAAE,KAAM,CAC1B,CAAC,CACDO,MAAM,CAAE,CAAC,CACP1G,IAAI,CAAE,KAAK,CACXD,IAAI,CAAEA,IAAI,CACV4G,QAAQ,CAAE,KAAK,CACfC,KAAK,CAAE,CACLT,IAAI,CAAE,IAAI,CACVU,QAAQ,CAAE,OAAO,CACjBC,SAAS,CAAE,MAAM,CACjBxB,QAAQ,CAAE,EAAE,CACZd,KAAK,CAAE,MACT,CAAC,CACDE,SAAS,CAAE,CACTqC,YAAY,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAC3B,CAAC,CACDC,YAAY,CAAE,KAAK,CACnBC,cAAc,CAAE,QAAAA,CAAUC,GAAG,CAAE,CAC7B,MAAO,CAAAA,GAAG,CAAG,GAAG,CAClB,CACF,CAAC,CACH,CAAC,CAED;AACArD,KAAK,CAACsD,SAAS,CAACpC,MAAM,CAAE,CACtBqC,QAAQ,CAAE,KAAK,CACfC,YAAY,CAAE,CAAC,QAAQ,CACzB,CAAC,CAAC,CACJ,CAAC,CAED;AACAjD,WAAW,CAAC,CAAC,CAEb;AACA,KAAM,CAAAkD,aAAa,CAAGxE,WAAW,CAACsB,WAAW,CAAE,KAAK,CAAC,CAAE;AAEvD;AACAN,YAAY,CAAGA,CAAA,GAAM,KAAAyD,MAAA,CACnB,CAAAA,MAAA,CAAA1D,KAAK,UAAA0D,MAAA,iBAALA,MAAA,CAAOC,MAAM,CAAC,CAAC,CACjB,CAAC,CACD5H,MAAM,CAACK,gBAAgB,CAAC,QAAQ,CAAE6D,YAAY,CAAC,CAE/C;AACA,MAAO,IAAM,CACXf,aAAa,CAACuE,aAAa,CAAC,CAC5B,GAAIxD,YAAY,CAAE,CAChBlE,MAAM,CAACM,mBAAmB,CAAC,QAAQ,CAAE4D,YAAY,CAAC,CACpD,CACA,GAAID,KAAK,CAAE,CACTA,KAAK,CAACK,OAAO,CAAC,CAAC,CACjB,CACF,CAAC,CAEH,CAAE,MAAOpH,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CAAE,CAACmB,UAAU,CAAC,CAAC,CAEhB;AACAvE,SAAS,CAAC,IAAM,CACd,KAAM,CAAA+N,gBAAgB,CAAI3H,KAAK,EAAK,CAClC,GAAI,CACF,GAAIA,KAAK,CAACC,IAAI,EAAID,KAAK,CAACC,IAAI,CAACC,IAAI,GAAK,KAAK,CAAE,CAC3CjD,OAAO,CAACmC,GAAG,CAAC,4BAA4B,CAAEY,KAAK,CAACC,IAAI,CAAC,CAErD,KAAM,CAAA2H,OAAO,CAAG5H,KAAK,CAACC,IAAI,CAACA,IAAI,CAC/B,GAAI,CAAC2H,OAAO,EAAI,CAACA,OAAO,CAACC,IAAI,CAAE,CAC7B5K,OAAO,CAAC6K,IAAI,CAAC,gBAAgB,CAAEF,OAAO,CAAC,CACvC,OACF,CAEA,KAAM,CAAAnL,MAAM,CAAGmL,OAAO,CAACC,IAAI,CAC3B,KAAM,CAAAE,QAAQ,CAAG9I,UAAU,CAAC2I,OAAO,CAACI,MAAM,CAAC,CAC3C,KAAM,CAAAC,SAAS,CAAGhJ,UAAU,CAAC2I,OAAO,CAACM,OAAO,CAAC,CAE7CzL,MAAM,CAACkG,OAAO,CAAC3C,KAAK,EAAI,CACtB,KAAM,CAAAmI,SAAS,CAAGnI,KAAK,CAACmI,SAAS,CACjC,KAAM,CAAAC,WAAW,CAAGpI,KAAK,CAACoI,WAAW,CACrC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAA9I,IAAI,CAAC,CAAC,CAAC6F,kBAAkB,CAAC,CAAC,CAE5C;AACA,GAAI,CAAAkD,aAAa,CAAG,EAAE,CACtB,GAAI,CAAAC,UAAU,CAAG,EAAE,CACnB,OAAOJ,SAAS,EACd,IAAK,KAAK,CACRG,aAAa,CAAG,OAAO,CACvBC,UAAU,CAAG,SAAS,CACtB,MACF,IAAK,KAAK,CACRD,aAAa,CAAG,OAAO,CACvBC,UAAU,CAAG,SAAS,CACtB,MACF,IAAK,KAAK,CACRD,aAAa,CAAG,QAAQ,CACxBC,UAAU,CAAG,SAAS,CACtB,MACF,IAAK,KAAK,CACRD,aAAa,CAAG,MAAM,CACtBC,UAAU,CAAG,SAAS,CACtB,MACF,IAAK,KAAK,CACRD,aAAa,CAAG,MAAM,CACtBC,UAAU,CAAG,SAAS,CACtB,MACF,IAAK,MAAM,CACTD,aAAa,CAAG,MAAM,CACtBC,UAAU,CAAG,SAAS,CACtB,MACF,IAAK,KAAK,CACRD,aAAa,CAAG,MAAM,CACtBC,UAAU,CAAG,SAAS,CACtB,MACF,QACED,aAAa,CAAGF,WAAW,EAAI,MAAM,CACrCG,UAAU,CAAG,SAAS,CAC1B,CAEA;AACA,KAAM,CAAAC,QAAQ,CAAG,CACflF,GAAG,CAAE/D,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGiJ,IAAI,CAACC,MAAM,CAAC,CAAC,CAC/BxI,IAAI,CAAEoI,aAAa,CACnBD,IAAI,CAAEA,IAAI,CACVxI,OAAO,CAAE+H,OAAO,CAACe,KAAK,EAAI,MAAM,CAChCjE,KAAK,CAAE6D,UAAU,CACjBJ,SAAS,CAAEA,SAAS,CACpBS,QAAQ,CAAE,CACRb,QAAQ,CAAEA,QAAQ,CAClBE,SAAS,CAAEA,SACb,CACF,CAAC,CAEDvL,SAAS,CAAC2C,IAAI,EAAI,CAChB,KAAM,CAAAwJ,SAAS,CAAG,CAACL,QAAQ,CAAE,GAAGnJ,IAAI,CAAC,CAACyJ,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAE;AACpD7L,OAAO,CAACmC,GAAG,CAAC,WAAW,CAAEyJ,SAAS,CAAC,CACnC,MAAO,CAAAA,SAAS,CAClB,CAAC,CAAC,CAEF;AACA,GAAIV,SAAS,CAAE,CACb/J,aAAa,CAACiB,IAAI,EAAI,CACpB,KAAM,CAAA0J,QAAQ,CAAG,CACf,GAAG1J,IAAI,CACP,CAAC8I,SAAS,EAAG,CAAC9I,IAAI,CAAC8I,SAAS,CAAC,EAAI,CAAC,EAAI,CACxC,CAAC,CACDlL,OAAO,CAACmC,GAAG,CAAC,WAAW,CAAE2J,QAAQ,CAAC,CAClC,MAAO,CAAAA,QAAQ,CACjB,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CACJ,CACF,CAAE,MAAO/L,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACtC,CACF,CAAC,CAED;AACA8C,MAAM,CAACK,gBAAgB,CAAC,SAAS,CAAEwH,gBAAgB,CAAC,CAEpD,MAAO,IAAM,CACX7H,MAAM,CAACM,mBAAmB,CAAC,SAAS,CAAEuH,gBAAgB,CAAC,CACzD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAqB,mBAAmB,CAAInJ,OAAO,EAAK,CACvC5C,OAAO,CAACmC,GAAG,CAAC,OAAO,CAAES,OAAO,CAACiB,WAAW,CAAE,KAAK,CAAEjB,OAAO,CAACrB,MAAM,CAAC,CAChErB,kBAAkB,CAAC0C,OAAO,CAAC,CAC7B,CAAC,CAED;AACAjG,SAAS,CAAC,IAAM,CACd;AACA,GAAIsD,eAAe,CAAE,CACnB,KAAM,CAAA+L,sBAAsB,CAAG1M,QAAQ,CAAC+F,IAAI,CAAClB,CAAC,EAAIA,CAAC,CAACuC,EAAE,GAAKzG,eAAe,CAACyG,EAAE,CAAC,CAC9E,GAAIsF,sBAAsB,GACrBA,sBAAsB,CAACzK,MAAM,GAAKtB,eAAe,CAACsB,MAAM,EACxDyK,sBAAsB,CAACxK,KAAK,GAAKvB,eAAe,CAACuB,KAAK,EACtDwK,sBAAsB,CAACpK,GAAG,GAAK3B,eAAe,CAAC2B,GAAG,EAClDoK,sBAAsB,CAACnK,GAAG,GAAK5B,eAAe,CAAC4B,GAAG,EAClDmK,sBAAsB,CAAClK,OAAO,GAAK7B,eAAe,CAAC6B,OAAO,CAAC,CAAE,CAChE9B,OAAO,CAACmC,GAAG,CAAC,UAAUlC,eAAe,CAAC4D,WAAW,OAAO,CAC7C,OAAO5D,eAAe,CAACsB,MAAM,OAAOyK,sBAAsB,CAACzK,MAAM,EAAE,CAAC,CAC/ErB,kBAAkB,CAAC8L,sBAAsB,CAAC,CAC5C,CACF,CACF,CAAC,CAAE,CAAC1M,QAAQ,CAAEW,eAAe,CAAC,CAAC,CAE/B;AACA,KAAM,CAAAgM,cAAc,CAAG,CACrB,CACEhE,KAAK,CAAE,KAAK,CACZiE,SAAS,CAAE,OAAO,CAClB7F,GAAG,CAAE,OAAO,CACZ8F,KAAK,CAAE,KACT,CAAC,CACD,CACElE,KAAK,CAAE,IAAI,CACXiE,SAAS,CAAE,QAAQ,CACnB7F,GAAG,CAAE,QAAQ,CACb8F,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE7K,MAAM,eACZpD,IAAA,CAACb,KAAK,EACJiE,MAAM,CAAEA,MAAM,GAAK,QAAQ,CAAG,SAAS,CAAG,OAAQ,CAClD2G,IAAI,CAAE3G,MAAM,GAAK,QAAQ,CAAG,IAAI,CAAG,IAAK,CACzC,CAEL,CAAC,CACD,CACE0G,KAAK,CAAE,IAAI,CACXiE,SAAS,CAAE,OAAO,CAClB7F,GAAG,CAAE,OAAO,CACZ8F,KAAK,CAAE,KAAK,CACZC,MAAM,CAAE5K,KAAK,EAAI,GAAG,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAGA,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAAGT,KAAK,OAC1E,CAAC,CACF,CAED;AACA,KAAM,CAAA6K,eAAe,CAAGA,CAAA,gBACtBlO,IAAA,CAACjB,IAAI,EACHuI,IAAI,CAAC,OAAO,CACZ6G,UAAU,CAAE9M,MAAO,CACnB+M,UAAU,CAAE3E,IAAI,eACdzJ,IAAA,CAACjB,IAAI,CAACsP,IAAI,EAACC,KAAK,CAAE,CAAEC,OAAO,CAAE,OAAQ,CAAE,CAAAC,QAAA,cACrCxO,IAAA,CAACjB,IAAI,CAACsP,IAAI,CAACI,IAAI,EACb3E,KAAK,cACH5J,KAAA,QAAKoO,KAAK,CAAE,CAAElE,QAAQ,CAAE,MAAM,CAAEsE,YAAY,CAAE,KAAM,CAAE,CAAAF,QAAA,eACpDxO,IAAA,SAAMsO,KAAK,CAAE,CAAEhF,KAAK,CAAEG,IAAI,CAACH,KAAK,CAAEqF,WAAW,CAAE,KAAM,CAAE,CAAAH,QAAA,CACpD/E,IAAI,CAAC3E,IAAI,CACN,CAAC,cACP9E,IAAA,SAAMsO,KAAK,CAAE,CAAEhF,KAAK,CAAE,MAAM,CAAEc,QAAQ,CAAE,MAAO,CAAE,CAAAoE,QAAA,CAC9C/E,IAAI,CAACwD,IAAI,CACN,CAAC,EACJ,CACN,CACDD,WAAW,cACT9M,KAAA,QAAKoO,KAAK,CAAE,CAAElE,QAAQ,CAAE,MAAM,CAAEd,KAAK,CAAE,MAAO,CAAE,CAAAkF,QAAA,eAC9CtO,KAAA,QAAAsO,QAAA,EAAK,gBAAI,CAAC/E,IAAI,CAAChF,OAAO,EAAM,CAAC,cAC7BvE,KAAA,QAAAsO,QAAA,EAAK,gBAAI,CAAC/E,IAAI,CAAC+D,QAAQ,CACrB,GAAG/D,IAAI,CAAC+D,QAAQ,CAACb,QAAQ,CAAC7I,OAAO,CAAC,CAAC,CAAC,KAAK2F,IAAI,CAAC+D,QAAQ,CAACX,SAAS,CAAC/I,OAAO,CAAC,CAAC,CAAC,EAAE,CAC7E,MAAM,EACH,CAAC,EACH,CACN,CACF,CAAC,CACO,CACX,CACFwK,KAAK,CAAE,CACLM,SAAS,CAAE,mBAAmB,CAC9BC,SAAS,CAAE,MACb,CAAE,CACH,CACF,CAED,mBACE7O,IAAA,CAACd,IAAI,EAAC4P,QAAQ,CAAE7N,OAAQ,CAAC8N,GAAG,CAAC,uBAAQ,CAAAP,QAAA,cACnCtO,KAAA,CAACI,aAAa,EAAAkO,QAAA,eAEZtO,KAAA,CAACN,kBAAkB,EACjB+L,QAAQ,CAAC,MAAM,CACfqD,SAAS,CAAErO,aAAc,CACzBsO,UAAU,CAAEA,CAAA,GAAMpM,gBAAgB,CAAC,CAAClC,aAAa,CAAE,CAAA6N,QAAA,eAGnDxO,IAAA,CAACa,QAAQ,EAACiJ,KAAK,CAAC,wDAAW,CAACoF,QAAQ,CAAE,KAAM,CAACpO,MAAM,CAAC,OAAO,CAAA0N,QAAA,cACzDtO,KAAA,CAACvB,GAAG,EAACwQ,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAAX,QAAA,eAClBxO,IAAA,CAACpB,GAAG,EAACwQ,IAAI,CAAE,CAAE,CAAAZ,QAAA,cACXxO,IAAA,CAACe,gBAAgB,EACf+I,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEvH,KAAK,CAACE,aAAc,CAC3BmN,UAAU,CAAE,CAAE/F,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNtJ,IAAA,CAACpB,GAAG,EAACwQ,IAAI,CAAE,CAAE,CAAAZ,QAAA,cACXxO,IAAA,CAACe,gBAAgB,EACf+I,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEvH,KAAK,CAACG,cAAe,CAC5BmN,MAAM,CAAE,KAAKtN,KAAK,CAACE,aAAa,EAAG,CACnCmN,UAAU,CAAE,CAAE/F,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNtJ,IAAA,CAACpB,GAAG,EAACwQ,IAAI,CAAE,CAAE,CAAAZ,QAAA,cACXxO,IAAA,CAACe,gBAAgB,EACf+I,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEvH,KAAK,CAACI,eAAgB,CAC7BkN,MAAM,CAAE,KAAKtN,KAAK,CAACE,aAAa,EAAG,CACnCmN,UAAU,CAAE,CAAE/F,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNtJ,IAAA,CAACpB,GAAG,EAACwQ,IAAI,CAAE,CAAE,CAAAZ,QAAA,cACXxO,IAAA,CAACe,gBAAgB,EACf+I,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEvH,KAAK,CAACK,YAAa,CAC1BgN,UAAU,CAAE,CAAE/F,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNtJ,IAAA,CAACpB,GAAG,EAACwQ,IAAI,CAAE,CAAE,CAAAZ,QAAA,cACXxO,IAAA,CAACe,gBAAgB,EACf+I,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEvH,KAAK,CAACM,aAAc,CAC3BgN,MAAM,CAAE,KAAKtN,KAAK,CAACK,YAAY,EAAG,CAClCgN,UAAU,CAAE,CAAE/F,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,cACNtJ,IAAA,CAACpB,GAAG,EAACwQ,IAAI,CAAE,CAAE,CAAAZ,QAAA,cACXxO,IAAA,CAACe,gBAAgB,EACf+I,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEvH,KAAK,CAACO,cAAe,CAC5B+M,MAAM,CAAE,KAAKtN,KAAK,CAACK,YAAY,EAAG,CAClCgN,UAAU,CAAE,CAAE/F,KAAK,CAAE,SAAU,CAAE,CAClC,CAAC,CACC,CAAC,EACH,CAAC,CACE,CAAC,cAGXtJ,IAAA,CAACa,QAAQ,EAACiJ,KAAK,CAAC,sCAAQ,CAACoF,QAAQ,CAAE,KAAM,CAACpO,MAAM,CAAC,kBAAkB,CAAA0N,QAAA,CAChEN,eAAe,CAAC,CAAC,CACV,CAAC,cAGXlO,IAAA,CAACa,QAAQ,EAACiJ,KAAK,CAAC,sCAAQ,CAACoF,QAAQ,CAAE,KAAM,CAACpO,MAAM,CAAC,kBAAkB,CAAA0N,QAAA,cACjExO,IAAA,QAAKuP,GAAG,CAAE3M,aAAc,CAAC0L,KAAK,CAAE,CAAExN,MAAM,CAAE,MAAM,CAAEkN,KAAK,CAAE,MAAO,CAAE,CAAM,CAAC,CACjE,CAAC,EACO,CAAC,cAGrBhO,IAAA,CAACS,WAAW,EAACE,aAAa,CAAEA,aAAc,CAACC,cAAc,CAAEA,cAAe,CAE7D,CAAC,cAGdV,KAAA,CAACN,kBAAkB,EACjB+L,QAAQ,CAAC,OAAO,CAChBqD,SAAS,CAAEpO,cAAe,CAC1BqO,UAAU,CAAEA,CAAA,GAAMnM,iBAAiB,CAAC,CAAClC,cAAc,CAAE,CAAA4N,QAAA,eAGrDxO,IAAA,CAACa,QAAQ,EAACiJ,KAAK,CAAC,0BAAM,CAACoF,QAAQ,CAAE,KAAM,CAACpO,MAAM,CAAC,KAAK,CAAA0N,QAAA,cAClDxO,IAAA,CAAChB,KAAK,EACJmP,UAAU,CAAEhN,QAAS,CACrBqO,OAAO,CAAE1B,cAAe,CACxB2B,MAAM,CAAC,IAAI,CACXC,UAAU,CAAE,KAAM,CAClBpI,IAAI,CAAC,OAAO,CACZqI,MAAM,CAAE,CAAEC,CAAC,CAAE,GAAI,CAAE,CACnBC,KAAK,CAAGC,MAAM,GAAM,CAClBC,OAAO,CAAEA,CAAA,GAAMnC,mBAAmB,CAACkC,MAAM,CAAC,CAC1CxB,KAAK,CAAE,CACL0B,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,CAAAnO,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEyG,EAAE,IAAKuH,MAAM,CAACvH,EAAE,CAAG,SAAS,CAAG,aAAa,CACzE6B,QAAQ,CAAE,MAAM,CAChBmE,OAAO,CAAE,SACX,CACF,CAAC,CAAE,CACJ,CAAC,CACM,CAAC,cAGXvO,IAAA,CAACa,QAAQ,EAACiJ,KAAK,CAAC,sCAAQ,CAACoF,QAAQ,CAAE,KAAM,CAACpO,MAAM,CAAC,KAAK,CAAA0N,QAAA,CACnD1M,eAAe,cACd5B,KAAA,CAACjB,YAAY,EACXiQ,QAAQ,MACRgB,MAAM,CAAE,CAAE,CACV5I,IAAI,CAAC,OAAO,CACZ6I,MAAM,CAAE,CACNzE,KAAK,CAAE,CAAEtB,QAAQ,CAAE,MAAM,CAAEmE,OAAO,CAAE,SAAU,CAAC,CAC/C6B,OAAO,CAAE,CAAEhG,QAAQ,CAAE,MAAM,CAAEmE,OAAO,CAAE,SAAU,CAClD,CAAE,CAAAC,QAAA,eAEFxO,IAAA,CAACf,YAAY,CAACoP,IAAI,EAAC3C,KAAK,CAAC,oBAAK,CAAA8C,QAAA,CAAE1M,eAAe,CAAC4D,WAAW,CAAoB,CAAC,cAChF1F,IAAA,CAACf,YAAY,CAACoP,IAAI,EAAC3C,KAAK,CAAC,cAAI,CAAA8C,QAAA,cAC3BxO,IAAA,CAACb,KAAK,EACJiE,MAAM,CAAEtB,eAAe,CAACsB,MAAM,GAAK,QAAQ,CAAG,SAAS,CAAG,OAAQ,CAClE2G,IAAI,CAAEjI,eAAe,CAACsB,MAAM,GAAK,QAAQ,CAAG,IAAI,CAAG,IAAK,CACzD,CAAC,CACe,CAAC,cACpBpD,IAAA,CAACf,YAAY,CAACoP,IAAI,EAAC3C,KAAK,CAAC,cAAI,CAAA8C,QAAA,CAC1B1M,eAAe,CAACsB,MAAM,GAAK,QAAQ,CAAGtB,eAAe,CAAC4B,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAC5D,CAAC,cACpB9D,IAAA,CAACf,YAAY,CAACoP,IAAI,EAAC3C,KAAK,CAAC,cAAI,CAAA8C,QAAA,CAC1B1M,eAAe,CAACsB,MAAM,GAAK,QAAQ,CAAGtB,eAAe,CAAC2B,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAC5D,CAAC,cACpB9D,IAAA,CAACf,YAAY,CAACoP,IAAI,EAAC3C,KAAK,CAAC,cAAI,CAAA8C,QAAA,CAC1B1M,eAAe,CAACsB,MAAM,GAAK,QAAQ,CAClC,GAAG,MAAO,CAAAtB,eAAe,CAACuB,KAAK,GAAK,QAAQ,CAAGvB,eAAe,CAACuB,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAAGhC,eAAe,CAACuB,KAAK,OAAO,CAC9G,KAAK,CACU,CAAC,cACpBrD,IAAA,CAACf,YAAY,CAACoP,IAAI,EAAC3C,KAAK,CAAC,oBAAK,CAAA8C,QAAA,CAC3B1M,eAAe,CAACsB,MAAM,GAAK,QAAQ,CAClC,GAAG,MAAO,CAAAtB,eAAe,CAAC6B,OAAO,GAAK,QAAQ,CAAG7B,eAAe,CAAC6B,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,CAAGhC,eAAe,CAAC6B,OAAO,GAAG,CAChH,KAAK,CACU,CAAC,EACR,CAAC,cAEf3D,IAAA,MAAGsO,KAAK,CAAE,CAAElE,QAAQ,CAAE,MAAO,CAAE,CAAAoE,QAAA,CAAC,oEAAW,CAAG,CAC/C,CACO,CAAC,EACO,CAAC,EACR,CAAC,CACZ,CAAC,CAEX,CAAC,CAED,cAAe,CAAAxN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}