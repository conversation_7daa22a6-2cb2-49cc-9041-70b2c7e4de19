{"ast": null, "code": "import React,{useEffect,useRef,useState,useCallback}from'react';import*as THREE from'three';import{GLTFLoader}from'three/examples/jsm/loaders/GLTFLoader';import{OrbitControls}from'three/examples/jsm/controls/OrbitControls';import{CoordinateConverter}from'../utils/CoordinateConverter';import*as TWEEN from'@tweenjs/tween.js';import mqtt from'mqtt';import{Select,Popover}from'antd';import intersectionsData from'../data/intersections.json';// 全局变量\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";let globalVehicleRef=null;let globalTrajectory=[];let currentPointIndex=0;let globalUpdateInterval=null;let targetPosition=null;// 新增：目标位置\nlet currentPosition=null;// 新增：当前位置\nlet isMoving=false;// 新增：移动状态标志\nlet cameraMode='global';// 'global' 或 'follow'\nlet controls=null;// 保存 controls 的引用\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel=null;let preloadedCyclistModel=null;// 新增：非机动车模型\nlet preloadedPeopleModel=null;// 新增：行人模型\nlet preloadedTrafficLightModel=null;// 新增：红绿灯模型\nlet scene=null;// 添加scene全局变量\n// 添加滤波相关的变量\nlet lastPosition=null;let lastRotation=null;const ALPHA=0.15;// 降低滤波系数，使平滑效果更强（从0.3降到0.15）\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions=new Map();// 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations=new Map();// 使用Map存储每个车辆ID的上一次旋转角度\n// MQTT配置\nconst MQTT_CONFIG={broker:window.location.hostname,port:8083,// 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\nbsm:'changli/cloud/v2x/obu/bsm',rsm:'changli/cloud/v2x/rsu/rsm',scene:'changli/cloud/v2x/obu/scene',rsi:'changli/cloud/v2x/rsu/rsi',// 添加 RSI 主题\nspat:'changli/cloud/v2x/rsu/spat'// 添加 SPAT 主题\n};// 修改所有资源的基础URL\nconst BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:5000';console.log('API_URLxxxx:',process.env.REACT_APP_API_URL);// 添加全局变量来存储所有车辆\nconst vehicleModels=new Map();// 使用Map存储车辆ID和对应的3D模型\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps=new Map();// 用于存储每个设备的最新时间戳\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId=null;// 添加红绿灯相关的全局变量\nlet trafficLightsMap=new Map();// 存储路口ID与红绿灯模型的映射\nlet trafficLightStates=new Map();// 存储路口ID与红绿灯状态的映射\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId=async()=>{try{const response=await fetch(`${BASE_URL}/api/vehicles/list`);const data=await response.json();if(data&&data.vehicles&&Array.isArray(data.vehicles)){const mainVehicle=data.vehicles.find(v=>v.isMainVehicle===true);if(mainVehicle&&mainVehicle.bsmId){mainVehicleBsmId=mainVehicle.bsmId;console.log('获取主车bsmId成功:',mainVehicleBsmId);return mainVehicleBsmId;}}console.log('未找到主车，使用默认值 BSM01');mainVehicleBsmId='BSM01';// 默认值\nreturn mainVehicleBsmId;}catch(error){console.error('获取主车信息失败:',error);mainVehicleBsmId='BSM01';// 出错时使用默认值\nreturn mainVehicleBsmId;}};// 添加滤波器函数\nconst lowPassFilter=(newValue,lastValue,alpha)=>{if(lastValue===null)return newValue;return alpha*newValue+(1-alpha)*lastValue;};// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition=(newPos,vehicleId)=>{// 如果没有提供车辆ID，使用全局变量（用于主车）\nif(!vehicleId){if(!lastPosition){lastPosition=newPos.clone();return newPos;}const filteredX=lowPassFilter(newPos.x,lastPosition.x,ALPHA);const filteredY=lowPassFilter(newPos.y,lastPosition.y,ALPHA);const filteredZ=lowPassFilter(newPos.z,lastPosition.z,ALPHA);lastPosition.set(filteredX,filteredY,filteredZ);return lastPosition.clone();}// 针对特定车辆ID的滤波\nif(!vehicleLastPositions.has(vehicleId)){vehicleLastPositions.set(vehicleId,newPos.clone());return newPos;}const lastPos=vehicleLastPositions.get(vehicleId);// 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\nconst distance=lastPos.distanceTo(newPos);const MAX_DISTANCE_THRESHOLD=50;// 最大距离阈值，超过此距离认为是位置跳变\nif(distance>MAX_DISTANCE_THRESHOLD){console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);vehicleLastPositions.set(vehicleId,newPos.clone());return newPos;}// 正常滤波处理\nconst filteredX=lowPassFilter(newPos.x,lastPos.x,ALPHA);const filteredY=lowPassFilter(newPos.y,lastPos.y,ALPHA);const filteredZ=lowPassFilter(newPos.z,lastPos.z,ALPHA);const filteredPos=new THREE.Vector3(filteredX,filteredY,filteredZ);vehicleLastPositions.set(vehicleId,filteredPos.clone());return filteredPos;};// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation=(newRotation,vehicleId)=>{// 如果没有提供车辆ID，使用全局变量（用于主车）\nif(!vehicleId){if(lastRotation===null){lastRotation=newRotation;return newRotation;}// 处理角度跳变（从360度到0度或反之）\nlet diff=newRotation-lastRotation;if(diff>Math.PI)diff-=2*Math.PI;if(diff<-Math.PI)diff+=2*Math.PI;const filteredRotation=lowPassFilter(lastRotation+diff,lastRotation,ALPHA);lastRotation=filteredRotation;return filteredRotation;}// 针对特定车辆ID的滤波\nif(!vehicleLastRotations.has(vehicleId)){vehicleLastRotations.set(vehicleId,newRotation);return newRotation;}const lastRot=vehicleLastRotations.get(vehicleId);// 处理角度跳变（从360度到0度或反之）\nlet diff=newRotation-lastRot;if(diff>Math.PI)diff-=2*Math.PI;if(diff<-Math.PI)diff+=2*Math.PI;// 检查是否是大角度变化，如果是则不进行过滤\nconst MAX_ANGLE_THRESHOLD=Math.PI/2;// 90度\nif(Math.abs(diff)>MAX_ANGLE_THRESHOLD){console.log(`车辆${vehicleId}朝向变化过大(${(diff*180/Math.PI).toFixed(2)}度)，不进行滤波`);vehicleLastRotations.set(vehicleId,newRotation);return newRotation;}const filteredRotation=lowPassFilter(lastRot+diff,lastRot,ALPHA);vehicleLastRotations.set(vehicleId,filteredRotation);return filteredRotation;};// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL=1;// 每秒更新一次\n// ... existing code ...\nconst CampusModel=_ref=>{let{className,onCurrentRSUChange,selectedRSUs}=_ref;const containerRef=useRef(null);const vehicleRef=useRef(null);const converter=useRef(new CoordinateConverter());const trajectoryRef=useRef([]);const currentPointRef=useRef(0);const mqttClientRef=useRef(null);const animationFrameRef=useRef(null);// 添加动画帧引用\nconst[isVehicleLoaded,setIsVehicleLoaded]=useState(false);// 添加状态跟踪车辆是否加载\n// 添加车辆状态\nconst[vehicleState,setVehicleState]=useState({longitude:0,latitude:0,speed:0,heading:0});// 在 CampusModel 组件中添加状态\nconst[viewMode,setViewMode]=useState('global');// 添加视角切换按钮的样式\nconst buttonContainerStyle={position:'fixed',bottom:'20px',left:'50%',transform:'translateX(-50%)',zIndex:1000,// 改为1000，避免遮挡点击\ndisplay:'flex',gap:'10px'};const buttonStyle={padding:'8px 16px',backgroundColor:'rgba(255, 255, 255, 0.9)',border:'1px solid #ddd',borderRadius:'4px',cursor:'pointer',fontSize:'14px',boxShadow:'0 2px 4px rgba(0,0,0,0.1)',transition:'all 0.3s ease'};// 添加相机引用\nconst cameraRef=useRef(null);// 添加路口选择相关代码\nconst[selectedIntersection,setSelectedIntersection]=useState(null);// 添加红绿灯状态弹出窗口相关状态\nconst[trafficLightPopover,setTrafficLightPopover]=useState({visible:false,interId:null,position:{x:0,y:0},content:null,phases:[]});// 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\nconst currentPopoverIdRef=useRef(null);// 添加红绿灯状态自动更新定时器引用\nconst trafficLightUpdateTimerRef=useRef(null);// 全局存储setTrafficLightPopover函数引用\nwindow._setTrafficLightPopover=setTrafficLightPopover;// 将Ref暴露给全局以便弹窗函数使用\nwindow.currentPopoverIdRef=currentPopoverIdRef;window.trafficLightUpdateTimerRef=trafficLightUpdateTimerRef;// 修改路口选择器的样式\nconst intersectionSelectStyle={position:'fixed',top:'60px',left:'50%',transform:'translateX(-50%)',width:'200px',// 从 300px 改为 200px\nzIndex:1001,backgroundColor:'white',borderRadius:'4px',boxShadow:'0 2px 8px rgba(0,0,0,0.1)'};// 添加文字标签样式\nconst labelStyle={position:'fixed',top:'60px',left:'calc(50% - 90px)',// 从 140px 改为 110px，让文字更靠近选择框\ntransform:'translateX(-100%)',padding:'0 5px',// 从 10px 改为 5px，减少内边距\nlineHeight:'32px',color:'#fff',fontSize:'14px',fontWeight:'bold',textShadow:'0 1px 2px rgba(0,0,0,0.3)',zIndex:1001};// 添加交通灯映射状态\nconst[trafficLightsMap]=useState(new Map());// 添加当前RSU状态\nconst[currentRSU,setCurrentRSU]=useState(null);// 添加设备时间戳状态\nconst[deviceTimestamps,setDeviceTimestamps]=useState(new Map());// 添加最后一条消息状态\nconst[lastMessage,setLastMessage]=useState({topic:'',content:''});// 修改视角切换函数\nconst switchToFollowView=()=>{setViewMode('follow');cameraMode='follow';if(controls){controls.enabled=false;}};const switchToGlobalView=()=>{setViewMode('global');cameraMode='global';if(cameraRef.current&&controls){// 获取当前相机位置和朝向\n// const currentPos = cameraRef.current.position.clone();\ncameraRef.current.position.set(0,500,0);const currentPos=cameraRef.current.position.clone();const currentUp=cameraRef.current.up.clone();// 创建相机位置的补间动画\nnew TWEEN.Tween(currentPos).to({x:0,y:300,z:0},1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(()=>{cameraRef.current.position.copy(currentPos);}).start();// 创建相机上方向的补间动画\nnew TWEEN.Tween(currentUp).to({x:0,y:1,z:0},1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(()=>{cameraRef.current.up.copy(currentUp);}).start();// 获取当前控制器目标点\ncontrols.target.set(0,0,0);const currentTarget=controls.target.clone();// 创建目标点的补间动画\nnew TWEEN.Tween(currentTarget).to({x:0,y:0,z:0},1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(()=>{controls.target.copy(currentTarget);// 确保相机始终朝向目标点\ncameraRef.current.lookAt(controls.target);controls.update();}).start();// 启用控制器\ncontrols.enabled=true;// 重置控制器的一些属性\ncontrols.minDistance=50;controls.maxDistance=500;controls.maxPolarAngle=Math.PI/2.1;controls.minPolarAngle=0;controls.update();// 强制更新相机矩阵\ncameraRef.current.updateMatrix();cameraRef.current.updateMatrixWorld(true);console.log('切换到全局视角',{目标相机位置:[0,300,0],目标控制点:[0,0,0],动画已启动:true});}};// 修改处理路口选择的函数\nconst handleIntersectionChange=value=>{const intersection=intersectionsData.intersections.find(i=>i.name===value);if(intersection&&cameraRef.current&&controls){setSelectedIntersection(intersection);// 使用 wgs84ToModel 方法转换经纬度到模型坐标\nconst modelCoords=converter.current.wgs84ToModel(parseFloat(intersection.longitude),parseFloat(intersection.latitude));console.log('路口坐标转换结果:',{路口名称:intersection.name,经纬度:{longitude:intersection.longitude,latitude:intersection.latitude},模型坐标:modelCoords});// 设置为路口视角模式\ncameraMode='intersection';setViewMode('intersection');// 直接设置相机位置\ncameraRef.current.position.set(modelCoords.x+50,70,-modelCoords.y+50);// 直接设置控制器目标点\ncontrols.target.set(modelCoords.x,0,-modelCoords.y);// 确保相机朝向目标点\ncameraRef.current.lookAt(controls.target);// 更新控制器\ncontrols.enabled=true;controls.update();// 强制更新相机矩阵\ncameraRef.current.updateMatrix();cameraRef.current.updateMatrixWorld(true);console.log('相机已直接移动到路口:',{路口名称:intersection.name,相机位置:cameraRef.current.position.toArray(),目标点:controls.target.toArray(),模型坐标:modelCoords});}};// 修改处理MQTT消息的函数\nconst handleMqttMessage=(topic,message)=>{try{const payload=JSON.parse(message);// 处理RSM消息\nif(topic===MQTT_CONFIG.rsm){var _payload$data;console.log('收到RSM消息:',payload);// 检查设备时间戳\nconst deviceMac=payload.mac;const messageTimestamp=payload.tm;// 获取该设备的最新时间戳\nconst lastTimestamp=deviceTimestamps.get(deviceMac);// 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\nif(lastTimestamp&&messageTimestamp<lastTimestamp){console.log('忽略过期的RSM消息:',{设备MAC:deviceMac,消息时间戳:messageTimestamp,最新时间戳:lastTimestamp});return;}// 更新设备的最新时间戳\ndeviceTimestamps.set(deviceMac,messageTimestamp);console.log('RSM消息时间戳更新:',{设备MAC:deviceMac,时间戳:messageTimestamp,是否为最新:!lastTimestamp||messageTimestamp>=lastTimestamp});const participants=((_payload$data=payload.data)===null||_payload$data===void 0?void 0:_payload$data.participants)||[];const rsuid=payload.data.rsuid;// 分类处理不同类型的参与者\nconst now=Date.now();// 处理所有参与者\nparticipants.forEach(participant=>{// const id = participant.partPtcId;\nconst id=rsuid+participant.partPtcId;const type=participant.partPtcType;if(type==='3'||type==='2'||type==='1'){// if(type === '3'){\n// if(type === '3'||type === '1'){\n// 解析位置和状态信息\nconst state={longitude:parseFloat(participant.partPosLong),latitude:parseFloat(participant.partPosLat),speed:parseFloat(participant.partSpeed),heading:parseFloat(participant.partHeading)};const modelPos=converter.current.wgs84ToModel(state.longitude,state.latitude);// 根据类型选择对应的预加载模型\nlet preloadedModel;switch(type){case'1':// 机动车\npreloadedModel=preloadedVehicleModel;break;case'2':// 非机动车\npreloadedModel=preloadedCyclistModel;break;case'3':// 行人\npreloadedModel=preloadedPeopleModel;break;default:return;// 跳过未知类型\n}// 获取或创建模型\nlet model=vehicleModels.get(id);if(!model&&preloadedModel){// 创建新模型实例\nconst newModel=preloadedModel.clone();// 根据类型调整高度\nconst height=type==='3'?0.5:1.0;// 行人模型贴近地面\nnewModel.position.set(modelPos.x,height,-modelPos.y);newModel.rotation.y=Math.PI-state.heading*Math.PI/180;scene.add(newModel);vehicleModels.set(id,{model:newModel,lastUpdate:now,type:type});}else if(model){// 更新现有模型\nmodel.model.position.set(modelPos.x,model.type==='3'?0.5:1.0,-modelPos.y);model.model.rotation.y=Math.PI-state.heading*Math.PI/180;model.lastUpdate=now;model.model.updateMatrix();model.model.updateMatrixWorld(true);}}});// 清理长时间未更新的模型\nconst CLEANUP_THRESHOLD=2000;const currentIds=new Set(participants.map(p=>p.partPtcId));vehicleModels.forEach((modelData,id)=>{if(now-modelData.lastUpdate>CLEANUP_THRESHOLD&&!currentIds.has(id)){scene.remove(modelData.model);vehicleModels.delete(id);console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);}});return;}// 处理BSM消息\nif(topic===MQTT_CONFIG.bsm){console.log('收到BSM消息:',payload);const bsmData=payload.data;const bsmid=bsmData.bsmId;const newState={longitude:parseFloat(bsmData.partLong),latitude:parseFloat(bsmData.partLat),speed:parseFloat(bsmData.partSpeed),heading:parseFloat(bsmData.partHeading)};console.log('解析后的车辆状态:',newState);console.log('车辆ID:',bsmid);// 通知RealTimeTraffic组件已收到真实BSM消息\nwindow.postMessage({type:'realBsmReceived',source:'CampusModel'},'*');// 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\nwindow.postMessage({type:'bsm',bsmId:bsmid,// 直接传递车辆ID\ndata:{// 同时提供完整的BSM数据\nbsmId:bsmid,partSpeed:bsmData.partSpeed,partLat:bsmData.partLat,partLong:bsmData.partLong,partHeading:bsmData.partHeading}},'*');// 获取模型位置坐标\nconst modelPos=converter.current.wgs84ToModel(newState.longitude,newState.latitude);const newPosition=new THREE.Vector3(modelPos.x,0.5,-modelPos.y);const newRotation=Math.PI-newState.heading*Math.PI/180;// 检查该车辆是否已存在于场景中\nlet vehicleObj=vehicleModels.get(bsmid);// 检查是否是主车\nconst isMainVehicle=bsmid===mainVehicleBsmId;if(!vehicleObj&&preloadedVehicleModel){// 创建一个新的车辆模型实例\nconst newVehicleModel=preloadedVehicleModel.clone();// 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n// 这样可以避免车辆突然出现的视觉冲击\nnewVehicleModel.position.set(newPosition.x,-5,newPosition.z);newVehicleModel.rotation.y=newRotation;// 设置BSM车辆为突出的颜色\nnewVehicleModel.traverse(child=>{if(child.isMesh&&child.material){// // 保存原始材质颜色\n// if (!child.userData.originalColor && child.material.color) {\n//   child.userData.originalColor = child.material.color.clone();\n// }\n// // 设置为更鲜艳的颜色\n// if (isMainVehicle) {\n//   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n// } else {\n//   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n// }\n// // 增加材质亮度\n// child.material.emissive = new THREE.Color(0x222222);\n// // 初始设置为半透明\n// child.material.transparent = true;\n// child.material.opacity = 0.6;\n// child.material.needsUpdate = true;\nconst newMaterial=child.material.clone();child.material=newMaterial;// 修改颜色逻辑（与原模型解耦）\nif(isMainVehicle){newMaterial.color.set(0x00BFFF);}else{newMaterial.color.set(0xFF6347);}newMaterial.emissive=new THREE.Color(0x222222);newMaterial.transparent=true;newMaterial.opacity=0.6;newMaterial.needsUpdate=true;}});// 创建速度显示标签\nconst speedLabel=createTextSprite(`${Math.round(newState.speed)} km/h`,{backgroundColor:isMainVehicle?{r:0,g:191,b:255,a:0.8}:// 主车使用蓝色背景\n{r:255,g:99,b:71,a:0.8},// 其他车辆使用红色背景\ntextColor:{r:255,g:255,b:255,a:1.0},// 白色文本\nfontSize:20,padding:8});speedLabel.position.set(0,7,0);// 位于车辆顶部上方\nspeedLabel.renderOrder=1000;// 确保在最上层渲染\nspeedLabel.material.opacity=0.6;// 初始半透明\nnewVehicleModel.add(speedLabel);scene.add(newVehicleModel);// 保存车辆引用到车辆模型集合中\nvehicleModels.set(bsmid,{model:newVehicleModel,lastUpdate:Date.now(),type:'1',// 设置为机动车类型\nisMain:isMainVehicle,speedLabel:speedLabel// 保存速度标签引用\n});console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);// 使用补间动画使车辆从地下逐渐显示出来\nnew TWEEN.Tween(newVehicleModel.position).to({y:0.5},500).easing(TWEEN.Easing.Quadratic.Out).start();// 使用补间动画使车辆从半透明变为完全不透明\nnewVehicleModel.traverse(child=>{if(child.isMesh&&child.material&&child.material.transparent){new TWEEN.Tween({opacity:0.6}).to({opacity:1.0},500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function(){child.material.opacity=this.opacity;child.material.needsUpdate=true;}).start();}});// 为速度标签也添加透明度动画\nnew TWEEN.Tween({opacity:0.6}).to({opacity:1.0},500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function(){speedLabel.material.opacity=this.opacity;speedLabel.material.needsUpdate=true;}).start();// 如果是主车，设置全局引用\nif(isMainVehicle){globalVehicleRef=newVehicleModel;setVehicleState(newState);console.log('设置全局主车引用:',bsmid);}}else if(vehicleObj){// 应用滤波\nconst filteredPosition=filterPosition(newPosition,bsmid);const filteredRotation=filterRotation(newRotation,bsmid);// 更新现有车辆位置和朝向\nvehicleObj.model.position.copy(filteredPosition);vehicleObj.model.rotation.y=filteredRotation;vehicleObj.model.updateMatrix();vehicleObj.model.updateMatrixWorld(true);vehicleObj.lastUpdate=Date.now();vehicleObj.isMain=isMainVehicle;// 更新主车状态\n// 更新速度标签文本\nif(vehicleObj.speedLabel){vehicleObj.speedLabel.material.map.dispose();vehicleObj.model.remove(vehicleObj.speedLabel);}// 创建新的速度标签\nconst speedLabel=createTextSprite(`${Math.round(newState.speed*3.6)} km/h`,{backgroundColor:isMainVehicle?{r:0,g:191,b:255,a:0.8}:// 主车使用蓝色背景\n{r:255,g:99,b:71,a:0.8},// 其他车辆使用红色背景\ntextColor:{r:255,g:255,b:255,a:1.0},// 白色文本\nfontSize:20,padding:8});speedLabel.position.set(0,15,0);// 位于车辆顶部上方\nspeedLabel.renderOrder=1000;// 确保在最上层渲染\nvehicleObj.model.add(speedLabel);vehicleObj.speedLabel=speedLabel;console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);// 如果是主车，同时更新全局引用和状态（用于相机跟随）\nif(isMainVehicle){globalVehicleRef=vehicleObj.model;setVehicleState(newState);}}// 清理长时间未更新的车辆\nconst now=Date.now();const CLEANUP_THRESHOLD=2000;// 增加到10秒，避免频繁清理造成闪烁\nvehicleModels.forEach((modelData,id)=>{const timeSinceLastUpdate=now-modelData.lastUpdate;// 对于即将超时的车辆，先降低透明度，而不是直接移除\nif(timeSinceLastUpdate>CLEANUP_THRESHOLD*0.7&&timeSinceLastUpdate<=CLEANUP_THRESHOLD){const opacity=1-(timeSinceLastUpdate-CLEANUP_THRESHOLD*0.7)/(CLEANUP_THRESHOLD*0.3);modelData.model.traverse(child=>{if(child.isMesh&&child.material){// 如果材质还没有透明度设置，先保存初始状态\nif(child.material.transparent===undefined){child.material.originalTransparent=child.material.transparent||false;child.material.originalOpacity=child.material.opacity||1.0;}// 设置透明度\nchild.material.transparent=true;child.material.opacity=opacity;child.material.needsUpdate=true;}});// 如果有速度标签，也调整其透明度\nif(modelData.speedLabel){modelData.speedLabel.material.opacity=opacity;modelData.speedLabel.material.needsUpdate=true;}}// 完全超时，移除车辆\nelse if(timeSinceLastUpdate>CLEANUP_THRESHOLD){// 清理资源\nif(modelData.speedLabel){modelData.speedLabel.material.map.dispose();modelData.speedLabel.material.dispose();modelData.model.remove(modelData.speedLabel);}modelData.model.traverse(child=>{if(child.isMesh){if(child.material){if(Array.isArray(child.material)){child.material.forEach(m=>m.dispose());}else{child.material.dispose();}}if(child.geometry)child.geometry.dispose();}});// 从场景中移除\nscene.remove(modelData.model);vehicleModels.delete(id);// 同时清除该车辆的滤波缓存\nvehicleLastPositions.delete(id);vehicleLastRotations.delete(id);console.log(`移除长时间未更新的车辆: ID ${id}`);}});return;}// SPAT消息处理\nif(topic===MQTT_CONFIG.spat){console.log('收到SPAT消息:',message);try{const payload=JSON.parse(message);// 检查设备时间戳\nconst deviceMac=payload.mac;const messageTimestamp=payload.tm;// 获取该设备的最新时间戳\nconst lastTimestamp=deviceTimestamps.get(deviceMac);// 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\nif(lastTimestamp&&messageTimestamp<lastTimestamp){console.log('忽略过期的SPAT消息:',{设备MAC:deviceMac,消息时间戳:messageTimestamp,最新时间戳:lastTimestamp});return;}// 更新设备时间戳\ndeviceTimestamps.set(deviceMac,messageTimestamp);// 修改：访问data.intersections而不是直接访问intersections\nif(payload.data&&payload.data.intersections&&Array.isArray(payload.data.intersections)){payload.data.intersections.forEach(intersection=>{const interId=intersection.interId;if(!interId){console.error('SPAT消息缺少interId:',intersection);return;}console.log(`处理路口ID: ${interId} 的SPAT消息`);// 创建所有相位的数组 - 存储到trafficLightStates\nif(intersection.phases&&Array.isArray(intersection.phases)){// 构建存储相位信息的数组\nconst phasesInfo=[];intersection.phases.forEach(phase=>{// 修改：使用phaseId而不是id\nif(!phase.phaseId){console.error('相位信息缺少phaseId:',phase);return;}const phaseId=phase.phaseId.toString();// 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\nconst direction=phase.trafficDirec?getDirectionFromCode(phase.trafficDirec):getPhaseDirection(phaseId);// 修改：直接从phase中获取信号灯状态和剩余时间\nconst lightState=phase.trafficLight||'R';// 默认为红灯\nconst remainTime=parseInt(phase.remainTime)||0;console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);// 构建相位信息对象\nconst phaseInfo={phaseId,direction,trafficLight:lightState,remainTime};// 添加到相位信息数组\nphasesInfo.push(phaseInfo);// 查找红绿灯模型并更新视觉效果\n// 尝试使用字符串ID和数字ID查找\nlet trafficLightKey=String(interId);let trafficLightModel=trafficLightsMap.get(trafficLightKey);if(!trafficLightModel){// 尝试使用数字ID\ntrafficLightKey=parseInt(interId);trafficLightModel=trafficLightsMap.get(trafficLightKey);}if(trafficLightModel){// 更新交通灯视觉效果\nupdateTrafficLightVisual(trafficLightModel,phaseInfo);// 更新弹出窗信息\nif(selectedIntersection&&selectedIntersection.interId===interId){setTrafficLightPopover(prev=>({...prev,visible:true,phaseId,direction,state:lightState,remainTime}));}}else{console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);}});// 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\nlet modelKey=null;// 尝试字符串ID\nconst strId=String(interId);if(trafficLightsMap.has(strId)){modelKey=strId;}else{// 尝试数字ID\nconst numId=parseInt(interId);if(trafficLightsMap.has(numId)){modelKey=numId;}}if(modelKey!==null){// 使用正确的ID类型存储状态信息\ntrafficLightStates.set(modelKey,{updateTime:Date.now(),phases:phasesInfo});console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);// 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\nif(window.currentPopoverIdRef&&(window.currentPopoverIdRef.current===modelKey||window.currentPopoverIdRef.current===String(modelKey)||window.currentPopoverIdRef.current===parseInt(modelKey))){console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);// 强制更新弹窗ID为当前数据的正确ID类型\nwindow.currentPopoverIdRef.current=modelKey;// 如果没有更新定时器则创建一个\nif(window.trafficLightUpdateTimerRef&&!window.trafficLightUpdateTimerRef.current){console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);setTimeout(()=>{window.showTrafficLightPopup(modelKey);},100);}}}else{// 如果找不到模型，仍使用原始ID存储\ntrafficLightStates.set(interId,{updateTime:Date.now(),phases:phasesInfo});console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);}}else{console.error('SPAT消息缺少相位信息:',intersection);}});}else{console.error('SPAT消息格式错误，缺少data.intersections数组:',payload);}}catch(error){console.error('解析SPAT消息出错:',error,message);}}// 处理 RSI 消息\nif(topic===MQTT_CONFIG.rsi&&payload.type==='RSI'){console.log('收到RSI消息:',payload);// 发送 RSI 消息到 RealTimeTraffic 组件\nwindow.postMessage({type:'RSI',data:payload.data},'*');const rsiData=payload.data;const rsuId=rsiData.rsuId;const events=rsiData.rtes||[];events.forEach(event=>{const eventId=event.rteId;const eventType=event.eventType;const description=event.description;const startTime=event.startTime;const endTime=event.endTime;// 将基准点经纬度转换为模型坐标\nconst modelPos=converter.current.wgs84ToModel(parseFloat(rsiData.posLong),parseFloat(rsiData.posLat));// 根据事件类型显示不同的提示或标记\nlet warningText='';let warningColor='';switch(eventType){case'401':// 道路抛洒物\nwarningText='道路抛洒物';warningColor='#ff4d4f';break;case'404':// 道路障碍物\nwarningText='道路障碍物';warningColor='#faad14';break;case'405':// 行人通过马路\nwarningText='行人通过马路';warningColor='#1890ff';break;case'904':// 逆行车辆\nwarningText='逆行车辆';warningColor='#f5222d';break;case'910':// 违停车辆\nwarningText='违停车辆';warningColor='#722ed1';break;case'1002':// 道路施工\nwarningText='道路施工';warningColor='#fa8c16';break;case'901':// 车辆超速\nwarningText='车辆超速';warningColor='#eb2f96';break;default:warningText=description||'未知事件';warningColor='#8c8c8c';}// 显示警告标记\nshowWarningMarker(modelPos,warningText,warningColor);console.log('RSI事件处理:',{事件ID:eventId,事件类型:eventType,事件说明:description,开始时间:startTime,结束时间:endTime,位置:modelPos});});return;}// 处理场景事件消息\nif(topic===MQTT_CONFIG.scene&&payload.type==='SCENE'){console.log('收到场景事件消息:',payload);const sceneData=payload.data;const sceneId=sceneData.sceneId;const sceneType=sceneData.sceneType;const sceneDesc=sceneData.sceneDesc;const position={latitude:parseFloat(sceneData.partLat),longitude:parseFloat(sceneData.partLong)};// 将经纬度转换为模型坐标\nconst modelPos=converter.current.wgs84ToModel(position.longitude,position.latitude);// 根据场景类型显示不同的提示或标记\nswitch(sceneType){case'2':// 交叉路口碰撞预警\nshowWarningMarker(modelPos,'交叉路口碰撞预警','#ff4d4f');break;case'9-5':// 道路危险状况预警（施工）\nshowWarningMarker(modelPos,'道路施工','#faad14');break;case'9-6':// 前方有障碍物\nshowWarningMarker(modelPos,'前方障碍物','#ff7a45');break;case'10':// 限速提醒\nconst speedLimit=sceneData.eventData1;// 限速值\nshowWarningMarker(modelPos,`限速${speedLimit}km/h`,'#1890ff');break;case'12':// 交通参与者碰撞预警\nshowWarningMarker(modelPos,'碰撞预警','#f5222d');break;case'13':// 绿波车速引导\nshowWarningMarker(modelPos,'绿波引导','#52c41a');break;case'21-8':// 禁止鸣笛\nshowWarningMarker(modelPos,'禁止鸣笛','#722ed1');break;case'34':// 逆行车辆提醒\nshowWarningMarker(modelPos,'逆行警告','#eb2f96');break;case'33':// 违章占道车辆预警\nshowWarningMarker(modelPos,'违章占道','#fa8c16');break;case'999':// 信号灯优先\nconst priorityType=sceneData.eventData1;// 优先类型\nconst duration=sceneData.eventData2;// 优先时长\nshowWarningMarker(modelPos,`信号优先-${getPriorityTypeText(priorityType)}${duration}秒`,'#13c2c2');break;}return;}// 如果不是RSM或BSM消息，则记录为其他类型\nconsole.log('未知类型消息:',{topic,type:payload.type,data:payload});}catch(error){console.error('处理MQTT消息失败:',error);console.error('原始消息内容:',message);}};// 修改初始化MQTT连接函数\nconst initMqttClient=()=>{console.log('正在连接MQTT服务器...');const wsUrl=`ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;console.log('尝试连接WebSocket:',wsUrl);// 创建WebSocket连接，不指定协议\nconst ws=new WebSocket(wsUrl);ws.onopen=()=>{console.log('WebSocket连接成功');};ws.onmessage=event=>{try{const message=JSON.parse(event.data);// 处理连接确认消息\nif(message.type==='connect'){console.log('收到连接确认:',message);return;}// 处理心跳消息\nif(message.type==='ping'){return;}// 处理MQTT消息\nif(message.type==='message'&&message.topic&&message.payload){// 直接将消息传递给handleMqttMessage处理\nhandleMqttMessage(message.topic,JSON.stringify(message.payload));}}catch(error){console.error('处理WebSocket消息失败:',error);}};ws.onerror=error=>{console.error('WebSocket错误:',error);};ws.onclose=()=>{console.log('WebSocket连接关闭');// 5秒后尝试重新连接\nsetTimeout(initMqttClient,5000);};// 保存WebSocket引用\nmqttClientRef.current=ws;};useEffect(()=>{if(!containerRef.current)return;// 预加载所有模型\npreloadModels();// 创建场景\nscene=new THREE.Scene();// 使用全局scene变量\n// 创建相机\nconst camera=new THREE.PerspectiveCamera(60,window.innerWidth/window.innerHeight,0.1,2000);// camera.position.set(0, 300, 0); // 初始为全局视角\ncamera.position.set(0,100,0);// 初始为全局视角\ncamera.lookAt(0,0,0);cameraRef.current=camera;// 创建渲染器\nconst renderer=new THREE.WebGLRenderer({antialias:true});renderer.setSize(window.innerWidth,window.innerHeight);renderer.setClearColor(0x000000);renderer.setPixelRatio(window.devicePixelRatio);containerRef.current.appendChild(renderer.domElement);// 修改光照设置\n// 添加环境光和平行光\nconst ambientLight=new THREE.AmbientLight(0xffffff,0.8);// 增加环境光强度从0.5到0.8\nscene.add(ambientLight);// 添加多个平行光源，从不同角度照亮车辆\nconst directionalLight1=new THREE.DirectionalLight(0xffffff,1.0);// 增加强度从0.8到1.0\ndirectionalLight1.position.set(10,10,10);scene.add(directionalLight1);// 添加第二个平行光源，从另一个角度照亮\nconst directionalLight2=new THREE.DirectionalLight(0xffffff,0.8);directionalLight2.position.set(-10,8,-10);scene.add(directionalLight2);// 添加一个聚光灯，专门照亮车辆\nconst spotLight=new THREE.SpotLight(0xffffff,1.0);spotLight.position.set(0,50,0);spotLight.angle=Math.PI/4;spotLight.penumbra=0.1;spotLight.decay=2;spotLight.distance=200;scene.add(spotLight);// 创建控制器\ncontrols=new OrbitControls(camera,renderer.domElement);controls.enableDamping=true;controls.dampingFactor=0.05;controls.screenSpacePanning=false;controls.minDistance=50;controls.maxDistance=500;controls.maxPolarAngle=Math.PI/2.1;controls.minPolarAngle=0;controls.target.set(0,0,0);controls.update();// 打印相机和控制器引用\nconsole.log('初始化完成',{camera:!!camera,controls:!!controls,cameraRef:!!cameraRef.current});// 修改加载车辆模型的函数\nconst loadVehicleModel=()=>{return new Promise((resolve,reject)=>{const vehicleLoader=new GLTFLoader();vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`,gltf=>{const vehicleModel=gltf.scene;// 创建一个新的Group作为根容器\nconst vehicleContainer=new THREE.Group();// 调整模型材质\nvehicleModel.traverse(child=>{if(child.isMesh){// 检查并调整材质\nif(child.material){// 创建新的标准材质\nconst newMaterial=new THREE.MeshStandardMaterial({color:0xffffff,// 白色\nmetalness:0.2,// 降低金属感\nroughness:0.1,// 降低粗糙度\nenvMapIntensity:1.0// 环境贴图强度\n});// 保留原始贴图\nif(child.material.map){newMaterial.map=child.material.map;}// 应用新材质\nchild.material=newMaterial;console.log('已调整车辆材质:',child.name);}}});// 遍历模型的所有子对象，确保它们都被正确添加到容器中\nwhile(vehicleModel.children.length>0){const child=vehicleModel.children[0];vehicleContainer.add(child);}// 确保容器直接添加到场景根节点\nscene.add(vehicleContainer);// 保存容器的引用\nglobalVehicleRef=vehicleContainer;console.log('车辆模型加载成功，使用容器包装');setIsVehicleLoaded(true);resolve(vehicleContainer);},xhr=>{console.log(`车辆模型加载进度: ${(xhr.loaded/xhr.total*100).toFixed(2)}%`);},reject);});};// 修改初始化流程\nconst initializeScene=async()=>{try{// 1. 加载车辆模型\n// const vehicleContainer = await loadVehicleModel();\n// 2. 初始化MQTT客户端\ninitMqttClient();// 3. 设置初始位置\n// if (vehicleContainer) {\n//   const initialState = {\n//     longitude: 113.0022348,\n//     latitude: 28.0698301,\n//     heading: 0\n//   };\n//   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n//   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n//   vehicleContainer.position.set(0, 1.0, 0);\n//   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n//   vehicleContainer.updateMatrix();\n//   vehicleContainer.updateMatrixWorld(true);\n//   currentPosition = vehicleContainer.position.clone();\n// }\n}catch(error){console.error('初始化场景失败:',error);}};// 添加重试逻辑的加载函数\nconst loadModelWithRetry=function(url){let maxRetries=arguments.length>1&&arguments[1]!==undefined?arguments[1]:3;return new Promise((resolve,reject)=>{const attemptLoad=retriesLeft=>{console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);const loader=new GLTFLoader();loader.load(url,gltf=>{console.log(`模型加载成功: ${url}`);resolve(gltf);},xhr=>{console.log(`加载进度: ${(xhr.loaded/xhr.total*100).toFixed(2)}%`);},error=>{console.error(`加载失败: ${url}`,error);if(retriesLeft>0){console.log(`将在 1 秒后重试...`);setTimeout(()=>attemptLoad(retriesLeft-1),1000);}else{reject(error);}});};attemptLoad(maxRetries);});};// 使用重试逻辑加载模型\nconst loader=new GLTFLoader();loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`,async gltf=>{try{const model=gltf.scene;model.scale.set(1,1,1);model.position.set(0,0,0);// 检查scene是否初始化\nif(scene){scene.add(model);// 在校园模型加载完成后初始化场景\nawait initializeScene();}else{console.error('无法添加模型：场景未初始化');}}catch(error){console.error('处理模型时出错:',error);}},xhr=>{console.log(`加载进度: ${(xhr.loaded/xhr.total*100).toFixed(2)}%`);},error=>{console.error('模型加载错误:',error);console.error('错误详情:',{错误类型:error.type,错误消息:error.message,加载URL:`${BASE_URL}/changli2/CLG_ALL_1025.glb`,完整URL:`${BASE_URL}/changli2/CLG_ALL_1025.glb`});});// 修改动画循环\nconst animate=()=>{animationFrameRef.current=requestAnimationFrame(animate);// 更新 TWEEN 动画\nTWEEN.update();if(cameraMode==='follow'&&globalVehicleRef){// 在跟随模式下禁用控制器\ncontrols.enabled=false;// 获取车辆当前位置\nconst vehiclePos=globalVehicleRef.position.clone();// 获取车辆旋转角度\nconst vehicleRotation=globalVehicleRef.rotation.y;// 计算相机偏移量\n// 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\nconst adjustedRotation=-(vehicleRotation-Math.PI)+Math.PI/2*3;// const adjustedRotation = Math.PI/2;\n// 增加距离到 50 单位\nconst cameraOffset=new THREE.Vector3(-50*Math.cos(adjustedRotation),100,-50*Math.sin(adjustedRotation));// 设置相机偏移（更接近车辆）\n// const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n// 设置相机位置\ncamera.position.copy(vehiclePos).add(cameraOffset);// 重置相机方向\ncamera.up.set(0,1,0);// 直接设置相机的目标\nconst lookAtTarget=vehiclePos.clone();camera.lookAt(lookAtTarget);// 强制更新相机矩阵\ncamera.updateProjectionMatrix();camera.updateMatrix();camera.updateMatrixWorld(true);// 禁用控制器\ncontrols.enabled=false;// 确保控制器不会覆盖相机设置\ncontrols.target.copy(vehiclePos);controls.update();console.log('相机设置:',{车辆位置:vehiclePos.toArray(),相机位置:camera.position.toArray(),相机目标:lookAtTarget.toArray(),相机朝向:camera.getWorldDirection(new THREE.Vector3()).toArray()});}else if(cameraMode==='global'){// 在全局模式下启用控制器\ncontrols.enabled=true;// 确保相机的up向量保持正确\ncamera.up.set(0,1,0);// 如果相机位置偏离太多，重置到默认位置\nif(Math.abs(camera.position.y)<50){camera.position.set(0,300,0);controls.target.set(0,0,0);camera.lookAt(controls.target);controls.update();}//         // 强制更新相机矩阵\n// camera.updateProjectionMatrix();\ncamera.updateMatrix();camera.updateMatrixWorld(true);}else if(cameraMode==='intersection'){// 路口视角模式\ncontrols.update();}if(controls)controls.update();if(scene&&camera){renderer.render(scene,camera);}};animate();// 处理窗口大小变化\nconst handleResize=()=>{camera.aspect=window.innerWidth/window.innerHeight;camera.updateProjectionMatrix();renderer.setSize(window.innerWidth,window.innerHeight);};window.addEventListener('resize',handleResize);// 添加全局函数用于手动切换视角\nwindow.setGlobalView=()=>{if(cameraRef.current){cameraRef.current.position.set(0,300,0);cameraRef.current.lookAt(0,0,0);cameraRef.current.updateMatrix();cameraRef.current.updateMatrixWorld(true);if(controls){controls.target.set(0,0,0);controls.enabled=true;controls.update();}cameraMode='global';console.log('手动切换到全局视角');return true;}return false;};// 修改清理函数\nreturn()=>{console.log('开始清理组件...');// 1. 首先停止动画循环\nif(animationFrameRef.current){cancelAnimationFrame(animationFrameRef.current);animationFrameRef.current=null;}// 2. 清理定时器\nif(globalUpdateInterval){clearInterval(globalUpdateInterval);globalUpdateInterval=null;}// 3. 关闭 WebSocket 连接\nif(mqttClientRef.current){mqttClientRef.current.close();mqttClientRef.current=null;}// 4. 移除事件监听器\nwindow.removeEventListener('resize',handleResize);// 5. 清理渲染器\nif(renderer&&containerRef.current){containerRef.current.removeChild(renderer.domElement);renderer.dispose();}// 6. 清理场景中的所有车辆模型\nif(vehicleModels){vehicleModels.forEach((modelData,id)=>{if(modelData.model&&scene){scene.remove(modelData.model);}});vehicleModels.clear();}// 7. 清理红绿灯模型\ntrafficLightsMap.forEach(lightObj=>{if(scene&&lightObj.model){scene.remove(lightObj.model);}});trafficLightsMap.clear();trafficLightStates.clear();// 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n// 9. 重置全局变量\nscene=null;controls=null;preloadedVehicleModel=null;preloadedCyclistModel=null;preloadedPeopleModel=null;preloadedTrafficLightModel=null;globalVehicleRef=null;console.log('组件清理完成');};},[]);// 在组件挂载时获取主车信息和添加事件监听\nuseEffect(()=>{// 初始获取主车信息\nfetchMainVehicleBsmId();// 添加自定义事件监听，用于接收主车变更通知\nconst handleMainVehicleChange=()=>{console.log('接收到主车变更通知，重新获取主车信息');fetchMainVehicleBsmId();};// 监听主车变更事件\nwindow.addEventListener('mainVehicleChanged',handleMainVehicleChange);// 定时刷新主车信息（每分钟一次）\nconst intervalId=setInterval(()=>{fetchMainVehicleBsmId();},60000);// 组件卸载时清理事件监听和定时器\nreturn()=>{window.removeEventListener('mainVehicleChanged',handleMainVehicleChange);clearInterval(intervalId);};},[]);// 添加一个useEffect钩子在场景初始化完成后创建红绿灯\nuseEffect(()=>{// 在场景加载后初始化红绿灯\nif(scene&&converter.current){console.log('准备创建红绿灯模型');// 延迟一秒创建红绿灯，确保模型已加载\nconst timer=setTimeout(()=>{if(scene&&converter.current){// 再次检查，以防延迟期间组件卸载\ncreateTrafficLights(converter.current);}},1000);return()=>clearTimeout(timer);}else{console.log('场景或坐标转换器未准备好，暂不创建红绿灯');}},[scene]);// 添加点击事件处理\nuseEffect(()=>{if(containerRef.current){// 定义点击处理函数\nconst handleClick=event=>{if(scene&&cameraRef.current){console.log('检测到点击事件',event.clientX,event.clientY);handleMouseClick(event,containerRef.current,scene,cameraRef.current,setTrafficLightPopover);}else{console.warn('点击事件处理失败: scene或camera未初始化');}};// 添加点击事件监听\ncontainerRef.current.addEventListener('click',handleClick);// 记录到控制台\nconsole.log('已添加点击事件监听器到容器',!!containerRef.current);// 清理函数\nreturn()=>{if(containerRef.current){containerRef.current.removeEventListener('click',handleClick);console.log('已移除点击事件监听器');}};}},[scene,cameraRef.current]);// 初始化场景 - 简化为空函数，避免引用错误\nconst initScene=useCallback(()=>{console.log('initScene函数已禁用');// 原始实现已移除，避免canvasRef未定义的错误\n},[containerRef,setCurrentRSU,trafficLightsMap]);// 创建简单交通灯模型\nconst createSimpleTrafficLight=()=>{const geometry=new THREE.BoxGeometry(4,15,4);const material=new THREE.MeshBasicMaterial({color:0x333333});const trafficLightModel=new THREE.Mesh(geometry,material);// 添加基座\nconst baseGeometry=new THREE.CylinderGeometry(2,2,2,32);const baseMaterial=new THREE.MeshBasicMaterial({color:0x666666});const baseModel=new THREE.Mesh(baseGeometry,baseMaterial);baseModel.position.set(0,-8.5,0);trafficLightModel.add(baseModel);return trafficLightModel;};// 添加额外的点击检测辅助对象\nconst addClickHelpers=()=>{if(!scene)return;// 为每个红绿灯添加一个透明的大型碰撞体，便于点击\ntrafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){// 创建一个较大的碰撞检测几何体\nconst helperGeometry=new THREE.SphereGeometry(3,3,3);const helperMaterial=new THREE.MeshBasicMaterial({color:0xff00ff,//\ntransparent:false,opacity:0.1,// 几乎透明\ndepthWrite:false});const helperMesh=new THREE.Mesh(helperGeometry,helperMaterial);helperMesh.position.set(0,0,0);// 放在红绿灯位置\n// 标记为click helper\nhelperMesh.userData={type:'trafficLight',interId:interId,name:lightObj.intersection.name,isClickHelper:true};// 添加到红绿灯模型\nlightObj.model.add(helperMesh);console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);}});};// 在创建红绿灯之后调用\nuseEffect(()=>{// 等待红绿灯创建完成后添加点击辅助对象\nconst timer=setTimeout(()=>{if(trafficLightsMap.size>0){console.log('添加红绿灯点击辅助对象');// addClickHelpers();\n}},5500);// 延迟略长于debugTrafficLights\nreturn()=>clearTimeout(timer);},[]);// // 在组件加载完毕后调用调试函数\n// useEffect(() => {\n//   // 延迟5秒调用调试函数，确保所有模型都已加载\n//   const timer = setTimeout(() => {\n//     console.log('调用场景调试函数');\n//     if (window.debugScene) {\n//       window.debugScene(); // 使用全局函数\n//     } else {\n//       console.error('debugScene函数未定义');\n//     }\n//   }, 5000);\n//   return () => clearTimeout(timer);\n// }, []);\n// 在useEffect中添加定时器清理逻辑\nuseEffect(()=>{return()=>{// 组件卸载时清理定时器\nif(trafficLightUpdateTimerRef.current){clearInterval(trafficLightUpdateTimerRef.current);trafficLightUpdateTimerRef.current=null;console.log('已清理红绿灯状态更新定时器');}};},[]);// 空依赖数组确保只在组件挂载和卸载时运行\n// 添加关闭弹窗时清理定时器的逻辑\nuseEffect(()=>{if(!trafficLightPopover.visible&&trafficLightUpdateTimerRef.current){clearInterval(trafficLightUpdateTimerRef.current);trafficLightUpdateTimerRef.current=null;currentPopoverIdRef.current=null;console.log('弹窗关闭，已清理红绿灯状态更新定时器');}},[trafficLightPopover.visible]);// 添加自动选择第一个路口的逻辑\nuseEffect(()=>{// 确保路口数据已加载\nif(intersectionsData&&intersectionsData.intersections&&intersectionsData.intersections.length>0){// 确保只在组件初次渲染并且未选择路口时执行\nif(!selectedIntersection){const firstIntersection=intersectionsData.intersections[0];console.log('自动选择第一个路口:',firstIntersection.name);// 延迟执行，确保场景和相机已初始化\nconst timer=setTimeout(()=>{handleIntersectionChange(firstIntersection.name);},2000);return()=>clearTimeout(timer);}}},[intersectionsData,selectedIntersection]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{style:labelStyle,children:\"\\u533A\\u57DF\\u9009\\u62E9\\uFF1A\"}),/*#__PURE__*/_jsx(Select,{style:intersectionSelectStyle,placeholder:\"\\u8BF7\\u9009\\u62E9\\u533A\\u57DF\\u4F4D\\u7F6E\",onChange:handleIntersectionChange,options:intersectionsData.intersections.map(intersection=>({value:intersection.name,label:intersection.name})),size:\"large\",bordered:true,dropdownStyle:{zIndex:1002,maxHeight:'300px'},value:selectedIntersection?selectedIntersection.name:undefined}),/*#__PURE__*/_jsx(\"div\",{ref:containerRef,style:{width:'100%',height:'100%'}}),trafficLightPopover.visible&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',left:`${trafficLightPopover.position.x}px`,top:`${trafficLightPopover.position.y}px`,transform:'translate(-50%, -100%)',zIndex:1003,backgroundColor:'rgba(0, 0, 0, 0.85)',color:'white',borderRadius:'4px',boxShadow:'0 2px 8px rgba(0, 0, 0, 0.3)',padding:'0',maxWidth:'240px',// 缩小最大宽度\nfontSize:'12px'// 缩小字体\n},children:[trafficLightPopover.content,/*#__PURE__*/_jsx(\"button\",{style:{position:'absolute',top:'0px',right:'0px',background:'none',border:'none',color:'white',fontSize:'12px',cursor:'pointer',padding:'2px 6px'},onClick:()=>handleClosePopover(setTrafficLightPopover),children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:buttonContainerStyle,children:[/*#__PURE__*/_jsx(\"button\",{style:{...buttonStyle,backgroundColor:viewMode==='follow'?'#1890ff':'rgba(255, 255, 255, 0.9)',color:viewMode==='follow'?'white':'black'},onClick:switchToFollowView,children:\"\\u8DDF\\u968F\\u89C6\\u89D2\"}),/*#__PURE__*/_jsx(\"button\",{style:{...buttonStyle,backgroundColor:viewMode==='global'?'#1890ff':'rgba(255, 255, 255, 0.9)',color:viewMode==='global'?'white':'black'},onClick:switchToGlobalView,children:\"\\u5168\\u5C40\\u89C6\\u89D2\"})]})]});};// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text){let parameters=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const params={fontFace:parameters.fontFace||'Arial',fontSize:parameters.fontSize||24,fontWeight:parameters.fontWeight||'bold',borderThickness:parameters.borderThickness||4,borderColor:parameters.borderColor||{r:0,g:0,b:0,a:1.0},backgroundColor:parameters.backgroundColor||{r:255,g:255,b:255,a:0.8},textColor:parameters.textColor||{r:0,g:0,b:0,a:1.0},padding:parameters.padding||5};// 创建画布\nconst canvas=document.createElement('canvas');const context=canvas.getContext('2d');// 设置字体\ncontext.font=`${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;// 测量文本宽度\nconst textWidth=context.measureText(text).width;// 设置画布尺寸，考虑边框和填充\nconst width=textWidth+2*params.padding+2*params.borderThickness;const height=params.fontSize+2*params.padding+2*params.borderThickness;canvas.width=width;canvas.height=height;// 重新设置字体，因为改变画布尺寸会重置上下文\ncontext.font=`${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;context.textBaseline='middle';// 绘制背景和边框（圆角矩形）\nconst radius=8;context.beginPath();context.moveTo(params.borderThickness+radius,params.borderThickness);context.lineTo(width-params.borderThickness-radius,params.borderThickness);context.arcTo(width-params.borderThickness,params.borderThickness,width-params.borderThickness,params.borderThickness+radius,radius);context.lineTo(width-params.borderThickness,height-params.borderThickness-radius);context.arcTo(width-params.borderThickness,height-params.borderThickness,width-params.borderThickness-radius,height-params.borderThickness,radius);context.lineTo(params.borderThickness+radius,height-params.borderThickness);context.arcTo(params.borderThickness,height-params.borderThickness,params.borderThickness,height-params.borderThickness-radius,radius);context.lineTo(params.borderThickness,params.borderThickness+radius);context.arcTo(params.borderThickness,params.borderThickness,params.borderThickness+radius,params.borderThickness,radius);context.closePath();// 设置边框颜色\ncontext.strokeStyle=`rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;context.lineWidth=params.borderThickness;context.stroke();// 设置背景填充\ncontext.fillStyle=`rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;context.fill();// 设置文字颜色\ncontext.fillStyle=`rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;context.textAlign='center';// 绘制文本\ncontext.fillText(text,width/2,height/2);// 创建纹理\nconst texture=new THREE.CanvasTexture(canvas);texture.minFilter=THREE.LinearFilter;texture.needsUpdate=true;// 创建精灵材质\nconst spriteMaterial=new THREE.SpriteMaterial({map:texture,transparent:true});// 创建精灵\nconst sprite=new THREE.Sprite(spriteMaterial);sprite.scale.set(10,5,1);sprite.material.depthTest=false;// 确保始终可见\n// 保存文本信息到userData，便于后续更新\nsprite.userData={text:text,params:params};return sprite;}// 在文件底部添加这个全局函数\nwindow.forceGlobalView=()=>{try{// 获取当前场景中的相机\nconst camera=document.querySelector('canvas').parentElement.__THREE_camera;if(camera){// 保存旧位置\nconst oldPos=camera.position.clone();// 设置新位置\ncamera.position.set(0,300,0);camera.up.set(0,1,0);camera.lookAt(0,0,0);// 更新矩阵\ncamera.updateMatrix();camera.updateMatrixWorld(true);// 更新控制器\nconst controls=document.querySelector('canvas').parentElement.__THREE_controls;if(controls){controls.target.set(0,0,0);controls.update();}console.log('强制设置全局视角成功',{旧位置:oldPos.toArray(),新位置:camera.position.toArray()});return true;}return false;}catch(e){console.error('强制设置全局视角失败',e);return false;}};// 修改车辆模型预加载函数\nconst preloadModels=async()=>{try{console.log('开始预加载所有模型...');const loader=new GLTFLoader();// 并行加载所有模型\ntry{const[vehicleGltf,cyclistGltf,peopleGltf,trafficLightGltf]=await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),loader.loadAsync(`${BASE_URL}/changli2/people.glb`),loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)]);// 处理机动车模型\npreloadedVehicleModel=vehicleGltf.scene;preloadedVehicleModel.traverse(child=>{if(child.isMesh){const newMaterial=new THREE.MeshStandardMaterial({color:0xff0000,// 0xff0000, //红色 0xffffff,白色\nmetalness:0.2,roughness:0.1,envMapIntensity:1.0});// 保留原始贴图\nif(child.material.map){newMaterial.map=child.material.map;}child.materia=newMaterial;}});// 处理非机动车模型\npreloadedCyclistModel=cyclistGltf.scene;// 设置非机动车模型的缩放\npreloadedCyclistModel.scale.set(2,2,2);// 保持原始材质\npreloadedCyclistModel.traverse(child=>{if(child.isMesh&&child.material){// 只调整材质属性，保持原始颜色\nchild.material.metalness=0.1;child.material.roughness=0.8;child.material.envMapIntensity=1.0;}});// 处理行人模型\npreloadedPeopleModel=peopleGltf.scene;// 设置行人模型的缩放\npreloadedPeopleModel.scale.set(2,2,2);// 保持原始材质\npreloadedPeopleModel.traverse(child=>{if(child.isMesh&&child.material){// 只调整材质属性，保持原始颜色\nchild.material.metalness=0.1;child.material.roughness=0.8;child.material.envMapIntensity=1.0;}});// 处理红绿灯模型\npreloadedTrafficLightModel=trafficLightGltf.scene;// 设置红绿灯模型的缩放\npreloadedTrafficLightModel.scale.set(6,6,6);// 保持原始材质\npreloadedTrafficLightModel.traverse(child=>{if(child.isMesh&&child.material){// 设置材质属性\nchild.material.metalness=0.2;child.material.roughness=0.3;child.material.envMapIntensity=1.2;}});console.log('所有模型预加载成功');}catch(error){console.error('加载特定模型失败，尝试单独加载:',error);// 如果整个Promise.all失败，尝试单独加载关键模型\ntry{if(!preloadedVehicleModel){const vehicleGltf=await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);preloadedVehicleModel=vehicleGltf.scene;}// 尝试单独加载红绿灯模型\nif(!preloadedTrafficLightModel){console.log('正在单独加载红绿灯模型...');const trafficLightGltf=await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);preloadedTrafficLightModel=trafficLightGltf.scene;preloadedTrafficLightModel.scale.set(3,3,3);console.log('红绿灯模型加载成功');}}catch(err){console.error('单独加载模型也失败:',err);}}}catch(error){console.error('模型预加载失败:',error);}};// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText=type=>{const types={'1':'信号灯保持','2':'绿灯延长','3':'红灯截断','4':'相位插入','5':'相位插入','6':'优先未处理'};return types[type]||'未知类型';};// 添加辅助函数：显示警告标记\nconst showWarningMarker=(position,text,color)=>{// 添加安全检查 - 如果场景不存在，则直接返回\nif(!scene){console.warn('无法显示警告标记：场景不存在或已卸载');return;}try{// 创建一个新的警告标记\nconst sprite=createTextSprite(text);sprite.position.set(position.x,10,-position.y);// 设置位置，稍微升高以便可见\n// 为标记添加一个定时器，在1秒后自动移除\nsetTimeout(()=>{// 再次检查场景是否存在\nif(scene&&sprite.parent){scene.remove(sprite);}},100);// 将标记添加到场景中\nscene.add(sprite);console.log('添加场景事件标记:',{位置:position,文本:text,颜色:color});}catch(error){console.error('显示警告标记时出错:',error);}};// 添加创建红绿灯模型的函数\nconst createTrafficLights=converterInstance=>{if(!scene){console.error('无法创建红绿灯：场景未初始化');return;}if(!converterInstance){console.error('无法创建红绿灯：坐标转换器未初始化');return;}if(!preloadedTrafficLightModel){console.error('红绿灯模型未加载');// 如果没有加载红绿灯模型，使用简单的替代物体\ncreateFallbackTrafficLights(converterInstance);return;}// 先清除现有的红绿灯\ntrafficLightsMap.forEach(lightObj=>{if(scene&&lightObj.model){scene.remove(lightObj.model);}});trafficLightsMap.clear();// 为每个路口创建红绿灯模型 - 只为hasTrafficLight为true的路口创建红绿灯\nintersectionsData.intersections.forEach(intersection=>{// 检查路口是否有红绿灯属性，如果没有或为false，则跳过\nif(intersection.hasTrafficLight===false){console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);return;}// 确认路口有经纬度和ID\nif(intersection.latitude&&intersection.longitude&&intersection.interId){// 转换经纬度到模型坐标\nconst modelPos=converterInstance.wgs84ToModel(parseFloat(intersection.longitude),parseFloat(intersection.latitude));console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);try{// 创建红绿灯模型\nconst trafficLightModel=preloadedTrafficLightModel.clone();// 给模型一个名称便于调试\ntrafficLightModel.name=`交通灯-${intersection.name}`;// 设置位置，离地面高度为15米，提高可见性\ntrafficLightModel.position.set(modelPos.x,15,-modelPos.y);// 放大红绿灯模型尺寸，使其更容易被点击\ntrafficLightModel.scale.set(10,10,10);// 确保渲染顺序高，避免被其他对象遮挡\ntrafficLightModel.renderOrder=100;// *** 重要：设置为可被交互 ***\ntrafficLightModel.traverse(child=>{// 设置所有子对象可被点击\nif(child.isMesh){// 确保材质能够被射线检测到\nchild.material.transparent=false;child.material.opacity=1.0;child.material.side=THREE.DoubleSide;child.material.depthWrite=true;child.material.depthTest=true;child.material.needsUpdate=true;// 设置较高的渲染顺序\nchild.renderOrder=100;}});// 添加交互所需的信息\ntrafficLightModel.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name};// // 添加一个专门用于点击的大型碰撞体\n// const colliderGeometry = new THREE.SphereGeometry(5, 12, 12);\n// const colliderMaterial = new THREE.MeshBasicMaterial({\n//   color: 0xff00ff,\n//   transparent: true,\n//   opacity: 0.0,  // 完全透明\n//   depthWrite: false\n// });\n// const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n// collider.name = `交通灯碰撞体-${intersection.name}`;\n// collider.userData = {\n//   type: 'trafficLight',\n//   interId: intersection.interId,\n//   name: intersection.name,\n//   isCollider: true\n// };\n// trafficLightModel.add(collider);\n// 将红绿灯添加到场景中\nscene.add(trafficLightModel);// 存储红绿灯引用\ntrafficLightsMap.set(intersection.interId,{model:trafficLightModel,intersection:intersection,position:modelPos});console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);}catch(error){console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`,error);// 如果克隆失败，创建一个简单的替代物体\ncreateSimpleTrafficLight(intersection,modelPos,converterInstance);}}});// 在控制台输出所有红绿灯的信息\nconsole.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);trafficLightsMap.forEach((lightObj,interId)=>{console.log(`- ID ${interId}: ${lightObj.intersection.name}`);});};// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights=converterInstance=>{intersectionsData.intersections.forEach(intersection=>{// 跳过没有红绿灯的路口\nif(intersection.hasTrafficLight===false){return;}if(intersection.latitude&&intersection.longitude&&intersection.interId){// 转换经纬度到模型坐标\nconst modelPos=converterInstance.wgs84ToModel(parseFloat(intersection.longitude),parseFloat(intersection.latitude));createSimpleTrafficLight(intersection,modelPos,converterInstance);}});};// 创建简单的替代红绿灯\nconst createSimpleTrafficLight=(intersection,modelPos,converterInstance)=>{// 创建一个简单的几何体作为红绿灯 - 增大尺寸\nconst geometry=new THREE.BoxGeometry(10,30,10);const material=new THREE.MeshBasicMaterial({color:0x333333,transparent:false,opacity:1.0});const trafficLightModel=new THREE.Mesh(geometry,material);// 给模型一个名称便于调试\ntrafficLightModel.name=`简易交通灯-${intersection.name}`;// 设置位置 - 提高高度以增加可见性\ntrafficLightModel.position.set(modelPos.x,15,-modelPos.y);// 设置渲染顺序\ntrafficLightModel.renderOrder=100;// 添加交互所需的信息\ntrafficLightModel.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name};// 添加一个专门用于点击的大型碰撞体\nconst colliderGeometry=new THREE.SphereGeometry(3,12,12);const colliderMaterial=new THREE.MeshBasicMaterial({color:0xff00ff,transparent:true,opacity:0.0,// 完全透明\ndepthWrite:false});const collider=new THREE.Mesh(colliderGeometry,colliderMaterial);collider.name=`简易交通灯碰撞体-${intersection.name}`;collider.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name,isCollider:true};trafficLightModel.add(collider);// 将红绿灯添加到场景中\nscene.add(trafficLightModel);// 存储红绿灯引用\ntrafficLightsMap.set(intersection.interId,{model:trafficLightModel,intersection:intersection,position:modelPos});// 添加一个顶部灯光标识，使其更容易被看到\nconst lightGeometry=new THREE.SphereGeometry(5,16,16);const lightMaterial=new THREE.MeshBasicMaterial({color:0xFF0000});const lightMesh=new THREE.Mesh(lightGeometry,lightMaterial);lightMesh.position.set(0,15,0);// 给灯光相同的userData\nlightMesh.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name};trafficLightModel.add(lightMesh);console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);};// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection=phaseId=>{switch(phaseId){case'1':return'北进口左转';case'2':return'北进口直行';case'3':return'北进口右转';case'5':return'东进口左转';case'6':return'东进口直行';case'7':return'东进口右转';case'9':return'南进口左转';case'10':return'南进口直行';case'11':return'南进口右转';case'13':return'西进口左转';case'14':return'西进口直行';case'15':return'西进口右转';default:return`相位${phaseId}`;}};// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode=dirCode=>{switch(dirCode){case'N':return'北向南';case'S':return'南向北';case'E':return'东向西';case'W':return'西向东';case'NE':return'东北向西南';case'NW':return'西北向东南';case'SE':return'东南向西北';case'SW':return'西南向东北';default:return`方向${dirCode}`;}};// 修改点击处理函数\nconst handleMouseClick=(event,container,sceneInstance,cameraInstance,setPopoverState)=>{if(!container||!sceneInstance||!cameraInstance)return;console.log('触发点击事件处理函数',event.clientX,event.clientY);// 计算鼠标在容器中的相对位置\nconst rect=container.getBoundingClientRect();const mouseX=(event.clientX-rect.left)/container.clientWidth*2-1;const mouseY=-((event.clientY-rect.top)/container.clientHeight)*2+1;// 创建射线\nconst raycaster=new THREE.Raycaster();// 增加检测阈值，使小物体也能被点击到\nraycaster.params.Points.threshold=1;raycaster.params.Line.threshold=1;const mouseVector=new THREE.Vector2(mouseX,mouseY);raycaster.setFromCamera(mouseVector,cameraInstance);console.log('鼠标点击位置:',mouseX,mouseY);// 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\nconst trafficLightObjects=[];trafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){// 将模型添加到数组中\ntrafficLightObjects.push(lightObj.model);// 确保模型是可见的，并且不会被其他对象遮挡\nlightObj.model.visible=true;lightObj.model.renderOrder=1000;// 设置高渲染优先级\n// 确保模型的所有子对象都是可见的\nlightObj.model.traverse(child=>{child.visible=true;child.renderOrder=1000;});}});console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);// 首先：尝试直接检测红绿灯模型集合\nconst trafficLightIntersects=raycaster.intersectObjects(trafficLightObjects,true);if(trafficLightIntersects.length>0){console.log('直接命中红绿灯模型:',trafficLightIntersects.length);trafficLightIntersects.forEach((intersect,index)=>{console.log(`命中对象 ${index}:`,intersect.object.name||'无名称','距离:',intersect.distance,'userData:',intersect.object.userData);});// 获取第一个交点的对象\nconst obj=getTrafficLightFromObject(trafficLightIntersects[0].object);if(obj&&obj.userData&&obj.userData.type==='trafficLight'){// 获取红绿灯ID\nconst interId=obj.userData.interId;console.log('成功检测到红绿灯点击, 路口ID:',interId,'类型:',typeof interId);// 检查trafficLightsMap中可能的ID类型\nlet correctId=interId;if(typeof interId==='string'&&!trafficLightsMap.has(interId)&&trafficLightsMap.has(parseInt(interId))){correctId=parseInt(interId);console.log(`转换ID类型为数字: ${correctId}`);}else if(typeof interId==='number'&&!trafficLightsMap.has(interId)&&trafficLightsMap.has(String(interId))){correctId=String(interId);console.log(`转换ID类型为字符串: ${correctId}`);}// 显示弹窗\nwindow.showTrafficLightPopup(correctId);return;}}// 第二步：检测所有场景对象\nconst intersects=raycaster.intersectObjects(sceneInstance.children,true);console.log('射线检测到的对象数量:',intersects.length);if(intersects.length>0){// 输出所有检测到的对象信息用于调试\nintersects.forEach((intersect,index)=>{const obj=intersect.object;console.log(`检测到的对象 ${index}:`,obj.name||'无名称','userData:',obj.userData,'距离:',intersect.distance);});// 检查是否点击了红绿灯\nfor(let i=0;i<intersects.length;i++){const obj=getTrafficLightFromObject(intersects[i].object);if(obj&&obj.userData&&obj.userData.type==='trafficLight'){const interId=obj.userData.interId;console.log('检测到红绿灯点击, 路口ID:',interId);// 显示弹窗\nwindow.showTrafficLightPopup(interId);return;}}}// 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\nconsole.log('尝试查找最接近点击位置的红绿灯');// 将红绿灯模型投影到屏幕坐标中\nlet closestLight=null;let minDistance=0.1;// 设置一个阈值，只有距离小于此值的才会被选中\ntrafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){const worldPos=new THREE.Vector3();// 获取模型的世界坐标\nlightObj.model.getWorldPosition(worldPos);// 将世界坐标投影到屏幕坐标\nconst screenPos=worldPos.clone();screenPos.project(cameraInstance);// 计算鼠标与红绿灯在屏幕上的距离\nconst dx=screenPos.x-mouseX;const dy=screenPos.y-mouseY;const distance=Math.sqrt(dx*dx+dy*dy);console.log(`路口 ${interId} 的距离:`,distance);if(distance<minDistance){minDistance=distance;closestLight={interId,distance};}}});if(closestLight){console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);// 显示弹窗\nwindow.showTrafficLightPopup(closestLight.interId);return;}console.log('未检测到任何红绿灯点击');// 如果用户点击了其他任何地方，关闭现有的弹窗\nif(setPopoverState){setPopoverState(prev=>{if(prev.visible){return{...prev,visible:false};}return prev;});}};// 关闭弹出窗口的处理函数\nconst handleClosePopover=setPopoverState=>{// 清理定时器\nif(window.trafficLightUpdateTimerRef&&window.trafficLightUpdateTimerRef.current){clearInterval(window.trafficLightUpdateTimerRef.current);window.trafficLightUpdateTimerRef.current=null;console.log('已清理红绿灯状态更新定时器');}// 清理当前弹窗ID\nif(window.currentPopoverIdRef){window.currentPopoverIdRef.current=null;}// 更新弹窗状态为不可见\nsetPopoverState(prev=>({...prev,visible:false}));console.log('弹窗已关闭');};// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick=interId=>{try{var _document$querySelect,_document$querySelect2;// 检查是否有该ID的红绿灯\nconst trafficLight=trafficLightsMap.get(interId||'1');if(!trafficLight){console.error('未找到指定ID的红绿灯:',interId);// 输出所有可用ID\nconsole.log('可用的红绿灯ID:');trafficLightsMap.forEach((light,id)=>{console.log(`- ${id}: ${light.intersection.name}`);});return false;}// 获取红绿灯模型\nconst lightModel=trafficLight.model;// 模拟点击事件\nconst stateInfo=trafficLightStates.get(interId);const intersection=trafficLight.intersection;// 创建弹出窗口内容\nlet content;if(stateInfo&&stateInfo.phases){content=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',width:'220px',maxHeight:'300px',overflowY:'auto'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'bold',marginBottom:'6px',fontSize:'14px',borderBottom:'1px solid #eee',paddingBottom:'4px'},children:[intersection.name,\" (ID: \",interId,\")\"]}),/*#__PURE__*/_jsx(\"div\",{children:stateInfo.phases.map((phase,index)=>{let lightColor;switch(phase.trafficLight){case'G':lightColor='#00ff00';break;case'Y':lightColor='#ffff00';break;case'R':default:lightColor='#ff0000';break;}return/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'6px',backgroundColor:'rgba(255,255,255,0.1)',padding:'4px',borderRadius:'4px',fontSize:'12px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'bold'},children:getPhaseDirection(phase.phaseId)}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u706F\\u8272: \"}),/*#__PURE__*/_jsx(\"span\",{style:{color:lightColor,fontWeight:'bold',backgroundColor:'rgba(0,0,0,0.3)',padding:'0 3px',borderRadius:'2px'},children:phase.trafficLight==='R'?'红灯':phase.trafficLight==='Y'?'黄灯':'绿灯'})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u5012\\u8BA1\\u65F6: \"}),/*#__PURE__*/_jsxs(\"span\",{style:{fontWeight:'bold'},children:[phase.remainTime,\" \\u79D2\"]})]})]},index);})}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'6px',fontSize:'10px',color:'#888'},children:[\"\\u66F4\\u65B0\\u65F6\\u95F4: \",new Date().toLocaleTimeString(),\" (\\u81EA\\u52A8\\u5237\\u65B0)\"]})]});}else{content=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',maxWidth:'200px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'bold',marginBottom:'8px'},children:intersection.name}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u8DEF\\u53E3ID: \",interId]}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"})]});}// 获取弹窗位置 - 使用中心位置\nconst centerX=window.innerWidth/2;const centerY=window.innerHeight/2;// 获取全局的setTrafficLightPopover函数\nconst setPopoverState=(_document$querySelect=document.querySelector('#root'))===null||_document$querySelect===void 0?void 0:(_document$querySelect2=_document$querySelect.__REACT_INSTANCE)===null||_document$querySelect2===void 0?void 0:_document$querySelect2.setTrafficLightPopover;if(setPopoverState){// 直接调用React组件的状态更新函数\nsetPopoverState({visible:true,interId:interId,position:{x:centerX,y:centerY},content:content,phases:(stateInfo===null||stateInfo===void 0?void 0:stateInfo.phases)||[]});console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);return true;}else{// 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\nconst popover=document.createElement('div');popover.style.position='absolute';popover.style.left=`${centerX}px`;popover.style.top=`${centerY}px`;popover.style.transform='translate(-50%, -100%)';popover.style.zIndex='9999';popover.style.backgroundColor='rgba(0, 0, 0, 0.85)';popover.style.color='white';popover.style.borderRadius='4px';popover.style.boxShadow='0 2px 8px rgba(0, 0, 0, 0.3)';popover.style.padding='8px';popover.style.maxWidth='240px';popover.style.fontSize='12px';popover.innerHTML=`\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo?'红绿灯状态已加载':'当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;document.body.appendChild(popover);// 添加关闭按钮点击事件\nconst closeButton=popover.querySelector('button');if(closeButton){closeButton.addEventListener('click',()=>{document.body.removeChild(popover);});}console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);return true;}}catch(error){console.error('测试红绿灯点击失败:',error);return false;}};// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights=()=>{console.log('红绿灯列表:');if(!trafficLightsMap||trafficLightsMap.size===0){console.log('当前没有红绿灯对象');return[];}const list=[];trafficLightsMap.forEach((light,id)=>{console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);list.push({id,name:light.intersection.name,position:light.position});});return list;};// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n//   console.log('开始调试红绿灯模型...');\n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n//       // 添加到场景\n//       scene.add(highlightMesh);\n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup=interId=>{try{// 确保interId为字符串类型\ninterId=String(interId);console.log('调用showTrafficLightPopup函数, 参数ID:',interId,'类型:',typeof interId);console.log('当前trafficLightsMap大小:',trafficLightsMap.size);console.log('当前trafficLightStates大小:',trafficLightStates.size);// 如果没有指定ID，则使用第一个红绿灯\nif(!interId&&trafficLightsMap.size>0){interId=String(Array.from(trafficLightsMap.keys())[0]);console.log('未指定ID，使用第一个红绿灯ID:',interId);}// 输出所有可用的红绿灯ID用于调试\nconsole.log('所有可用的红绿灯ID:');trafficLightsMap.forEach((light,id)=>{var _light$intersection;console.log(`- ${id} (${typeof id}): ${((_light$intersection=light.intersection)===null||_light$intersection===void 0?void 0:_light$intersection.name)||'未知'}`);});// 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\nlet trafficLight=trafficLightsMap.get(interId);if(!trafficLight){// 尝试转换为数字查找\nconst numericId=parseInt(interId);trafficLight=trafficLightsMap.get(numericId);if(trafficLight){console.log(`使用数字ID ${numericId} 找到了红绿灯`);interId=numericId;// 更新interId为找到的正确类型\n}}if(!trafficLight){console.error('未找到指定ID的红绿灯:',interId);return false;}// 更新当前显示的红绿灯ID引用\nif(window.currentPopoverIdRef){window.currentPopoverIdRef.current=interId;}// 取消之前的更新定时器（如果存在）\nif(window.trafficLightUpdateTimerRef&&window.trafficLightUpdateTimerRef.current){clearInterval(window.trafficLightUpdateTimerRef.current);window.trafficLightUpdateTimerRef.current=null;}// 创建更新函数\nconst updateTrafficLightPopover=()=>{var _window$currentPopove;// 确保当前弹窗仍然可见\nif(!window._setTrafficLightPopover)return;const currentId=(_window$currentPopove=window.currentPopoverIdRef)===null||_window$currentPopove===void 0?void 0:_window$currentPopove.current;if(!currentId)return;// 重新获取最新的状态信息\nlet stateInfo=trafficLightStates.get(currentId);if(!stateInfo&&typeof currentId==='string'){// 尝试使用数字ID\nstateInfo=trafficLightStates.get(parseInt(currentId));}else if(!stateInfo&&typeof currentId==='number'){// 尝试使用字符串ID\nstateInfo=trafficLightStates.get(String(currentId));}if(!stateInfo||!stateInfo.phases||stateInfo.phases.length===0){console.log(`路口ID ${currentId} 没有状态信息，跳过更新`);return;}// 获取交通灯信息\nconst intersectionLight=trafficLightsMap.get(currentId)||(typeof currentId==='string'?trafficLightsMap.get(parseInt(currentId)):trafficLightsMap.get(String(currentId)));if(!intersectionLight){console.error(`找不到路口ID ${currentId} 的红绿灯信息，无法更新弹窗`);return;}const intersection=intersectionLight.intersection;// 创建更新的弹窗内容\nconst content=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',width:'220px',maxHeight:'300px',overflowY:'auto'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'bold',marginBottom:'6px',fontSize:'14px',borderBottom:'1px solid #eee',paddingBottom:'4px'},children:[(intersection===null||intersection===void 0?void 0:intersection.name)||'未知路口',\" (ID: \",currentId,\")\"]}),/*#__PURE__*/_jsx(\"div\",{children:stateInfo.phases.map((phase,index)=>{let lightColor;let lightText;switch(phase.trafficLight){case'G':lightColor='#00ff00';lightText='绿灯';break;case'Y':lightColor='#ffff00';lightText='黄灯';break;case'R':default:lightColor='#ff0000';lightText='红灯';break;}const direction=getPhaseDirection(phase.phaseId);return/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'6px',backgroundColor:'rgba(255,255,255,0.1)',padding:'4px',borderRadius:'4px',fontSize:'12px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'bold'},children:[direction,\" (\\u76F8\\u4F4DID: \",phase.phaseId,\")\"]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u706F\\u8272: \"}),/*#__PURE__*/_jsx(\"span\",{style:{color:lightColor,fontWeight:'bold',backgroundColor:'rgba(0,0,0,0.3)',padding:'0 3px',borderRadius:'2px'},children:lightText})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u5012\\u8BA1\\u65F6: \"}),/*#__PURE__*/_jsxs(\"span\",{style:{fontWeight:'bold'},children:[phase.remainTime,\" \\u79D2\"]})]})]},index);})}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'6px',fontSize:'10px',color:'#888'},children:[\"\\u66F4\\u65B0\\u65F6\\u95F4: \",new Date(stateInfo.updateTime).toLocaleTimeString(),\" (\\u81EA\\u52A8\\u5237\\u65B0)\"]})]});// 更新弹窗内容\nwindow._setTrafficLightPopover(prev=>({...prev,content:content,phases:stateInfo.phases}));console.log(`已更新路口 ${(intersection===null||intersection===void 0?void 0:intersection.name)||currentId} 的红绿灯状态弹窗`);};// 先执行一次初始更新\nconst updateInitialTrafficLightPopover=()=>{// 同样，查找红绿灯状态信息时也尝试字符串和数字两种类型\nlet stateInfo=trafficLightStates.get(interId);if(!stateInfo){// 尝试使用数字ID\nconst numericId=parseInt(interId);stateInfo=trafficLightStates.get(numericId);if(stateInfo){console.log(`使用数字ID ${numericId} 找到了红绿灯状态信息`);}}console.log(`路口ID ${interId} 的状态信息:`,stateInfo);const intersection=trafficLight.intersection;console.log('路口信息:',intersection);// 创建弹窗内容\nlet content;if(stateInfo&&stateInfo.phases&&stateInfo.phases.length>0){// 添加一个检查相位数据的打印\nconsole.log('相位数据详情:');stateInfo.phases.forEach((phase,index)=>{console.log(`相位 ${index+1}: ID=${phase.phaseId}, 状态=${phase.trafficLight}, 剩余时间=${phase.remainTime}`);});content=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',width:'220px',maxHeight:'300px',overflowY:'auto'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'bold',marginBottom:'6px',fontSize:'14px',borderBottom:'1px solid #eee',paddingBottom:'4px'},children:[(intersection===null||intersection===void 0?void 0:intersection.name)||'未知路口',\" (ID: \",interId,\")\"]}),/*#__PURE__*/_jsx(\"div\",{children:stateInfo.phases.map((phase,index)=>{let lightColor;let lightText;switch(phase.trafficLight){case'G':lightColor='#00ff00';lightText='绿灯';break;case'Y':lightColor='#ffff00';lightText='黄灯';break;case'R':default:lightColor='#ff0000';lightText='红灯';break;}const direction=getPhaseDirection(phase.phaseId);return/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'6px',backgroundColor:'rgba(255,255,255,0.1)',padding:'4px',borderRadius:'4px',fontSize:'12px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'bold'},children:[direction,\" (\\u76F8\\u4F4DID: \",phase.phaseId,\")\"]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u706F\\u8272: \"}),/*#__PURE__*/_jsx(\"span\",{style:{color:lightColor,fontWeight:'bold',backgroundColor:'rgba(0,0,0,0.3)',padding:'0 3px',borderRadius:'2px'},children:lightText})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u5012\\u8BA1\\u65F6: \"}),/*#__PURE__*/_jsxs(\"span\",{style:{fontWeight:'bold'},children:[phase.remainTime,\" \\u79D2\"]})]})]},index);})}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'6px',fontSize:'10px',color:'#888'},children:[\"\\u66F4\\u65B0\\u65F6\\u95F4: \",new Date(stateInfo.updateTime).toLocaleTimeString(),\" (\\u81EA\\u52A8\\u5237\\u65B0)\"]})]});}else{// 如果没有状态信息，创建一个示例内容\ncontent=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',width:'220px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'bold',marginBottom:'6px',fontSize:'14px',borderBottom:'1px solid #eee',paddingBottom:'4px'},children:[(intersection===null||intersection===void 0?void 0:intersection.name)||'未知路口',\" (ID: \",interId,\")\"]}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#ff4d4f',fontSize:'12px'},children:\"\\u8BE5\\u8DEF\\u53E3\\u6682\\u65E0\\u7EA2\\u7EFF\\u706F\\u72B6\\u6001\\u4FE1\\u606F\\uFF0C\\u8BF7\\u7B49\\u5F85SPAT\\u6D88\\u606F\\u66F4\\u65B0\\u3002\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'6px',fontSize:'10px',color:'#888'},children:[\"\\u5F53\\u524D\\u65F6\\u95F4: \",new Date().toLocaleTimeString()]})]});}// 设置弹窗位置在屏幕中央\nconst x=window.innerWidth/2;const y=window.innerHeight/2;// 更新弹窗状态\nif(window._setTrafficLightPopover){var _stateInfo;console.log('调用_setTrafficLightPopover更新弹窗状态',{visible:true,interId,position:{x,y}});window._setTrafficLightPopover({visible:true,interId:interId,position:{x,y},content:content,phases:((_stateInfo=stateInfo)===null||_stateInfo===void 0?void 0:_stateInfo.phases)||[]});console.log(`已显示路口 ${(intersection===null||intersection===void 0?void 0:intersection.name)||interId} 的红绿灯状态弹窗`);// 设置定时更新\nif(window.trafficLightUpdateTimerRef){window.trafficLightUpdateTimerRef.current=setInterval(updateTrafficLightPopover,TRAFFIC_LIGHT_UPDATE_INTERVAL*1000);console.log(`已设置红绿灯状态弹窗定时更新，间隔 ${TRAFFIC_LIGHT_UPDATE_INTERVAL} 秒`);}return true;}else{console.error('无法找到setTrafficLightPopover函数');return false;}};return updateInitialTrafficLightPopover();}catch(error){console.error('显示红绿灯弹窗失败:',error);return false;}};// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n//   console.log('开始生成模拟SPAT数据...');\n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject=object=>{let current=object;// 如果对象本身就有红绿灯数据，直接返回\nif(current&&current.userData&&current.userData.type==='trafficLight'){console.log('直接找到红绿灯对象:',current.name||'无名称');return current;}// 向上查找父对象，直到找到红绿灯或到达顶层\nwhile(current&&current.parent){current=current.parent;if(current.userData&&current.userData.type==='trafficLight'){console.log('从父对象找到红绿灯:',current.name||'无名称');return current;}}return null;};// 添加调试工具：强制进行点击测试\nwindow.testClickDetection=(x,y)=>{try{console.log('执行强制点击测试 @ 位置:',x,y);// 找到渲染器的DOM元素\nconst canvas=document.querySelector('canvas');if(!canvas){console.error('找不到THREE.js的canvas元素');return false;}// 确保scene和camera已定义\nif(!scene||!cameraRef.current){console.error('scene或camera未定义');return false;}// 如果没有传入坐标，使用屏幕中心点\nif(x===undefined||y===undefined){x=window.innerWidth/2;y=window.innerHeight/2;}// 计算归一化设备坐标 (-1 到 +1)\nconst rect=canvas.getBoundingClientRect();const mouseX=(x-rect.left)/canvas.clientWidth*2-1;const mouseY=-((y-rect.top)/canvas.clientHeight)*2+1;console.log('归一化坐标:',mouseX,mouseY);// 创建一个射线\nconst raycaster=new THREE.Raycaster();raycaster.params.Points.threshold=5;raycaster.params.Line.threshold=5;const mouseVector=new THREE.Vector2(mouseX,mouseY);raycaster.setFromCamera(mouseVector,cameraRef.current);// 收集所有红绿灯对象\nconst trafficLightObjects=[];trafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){trafficLightObjects.push(lightObj.model);console.log(`添加红绿灯 ${interId} 到检测列表`);}});// 直接对红绿灯对象进行碰撞检测\nconsole.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);const tlIntersects=raycaster.intersectObjects(trafficLightObjects,true);if(tlIntersects.length>0){console.log('成功点击到红绿灯对象!');tlIntersects.forEach((intersect,i)=>{console.log(`结果 ${i}:`,intersect.object.name||'无名称','距离:',intersect.distance,'position:',intersect.object.position.toArray(),'userData:',intersect.object.userData);// 尝试获取红绿灯ID\nconst obj=getTrafficLightFromObject(intersect.object);if(obj&&obj.userData&&obj.userData.type==='trafficLight'){console.log('找到红绿灯ID:',obj.userData.interId);}});return true;}// 对整个场景进行碰撞检测\nconsole.log('对整个场景进行碰撞检测...');const sceneIntersects=raycaster.intersectObjects(scene.children,true);console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);sceneIntersects.forEach((intersect,i)=>{const obj=intersect.object;console.log(`场景物体 ${i}:`,obj.name||'无名称','类型:',obj.type,'位置:',obj.position.toArray(),'距离:',intersect.distance,'userData:',obj.userData);});// 测试红绿灯的可见性\nconsole.log('检查红绿灯的可见性...');let visibleCount=0;trafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){var _lightObj$intersectio;// 检查红绿灯模型是否可见\nlet isVisible=lightObj.model.visible;let frustumVisible=true;// 获取世界位置\nconst worldPos=new THREE.Vector3();lightObj.model.getWorldPosition(worldPos);// 计算到摄像机的距离\nconst distanceToCamera=worldPos.distanceTo(cameraRef.current.position);// 检查是否在视锥体内\nconst screenPos=worldPos.clone().project(cameraRef.current);if(Math.abs(screenPos.x)>1||Math.abs(screenPos.y)>1||screenPos.z<-1||screenPos.z>1){frustumVisible=false;}if(isVisible){visibleCount++;}console.log(`红绿灯 ${interId}:`,{名称:((_lightObj$intersectio=lightObj.intersection)===null||_lightObj$intersectio===void 0?void 0:_lightObj$intersectio.name)||'未知',可见性:isVisible,在视锥体内:frustumVisible,世界位置:worldPos.toArray(),屏幕位置:[screenPos.x,screenPos.y,screenPos.z],与摄像机距离:distanceToCamera});}});console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);// 如果未检测到任何交叉点\nreturn sceneIntersects.length>0;}catch(error){console.error('点击测试失败:',error);return false;}};// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual=(trafficLight,phaseInfo)=>{var _trafficLight$interse,_trafficLight$interse2,_trafficLight$interse3;if(!trafficLight||!trafficLight.model||!phaseInfo){return;}// 移除旧的灯光模型(如果存在)\nconst lightsToRemove=[];trafficLight.model.traverse(child=>{if(child.userData&&child.userData.isLight){lightsToRemove.push(child);}});lightsToRemove.forEach(light=>{trafficLight.model.remove(light);});// 根据状态获取颜色\nlet lightColor;switch(phaseInfo.trafficLight){case'G':lightColor=0x00FF00;// 绿色\nbreak;case'Y':lightColor=0xFFFF00;// 黄色\nbreak;case'R':default:lightColor=0xFF0000;// 红色\nbreak;}// 创建一个球体作为灯光\nconst lightGeometry=new THREE.SphereGeometry(3,16,16);const lightMaterial=new THREE.MeshBasicMaterial({color:lightColor,emissive:lightColor,emissiveIntensity:1});const lightMesh=new THREE.Mesh(lightGeometry,lightMaterial);lightMesh.position.set(0,12,0);// 放在交通灯顶部\nlightMesh.userData={isLight:true,type:'trafficLight',interId:(_trafficLight$interse=trafficLight.intersection)===null||_trafficLight$interse===void 0?void 0:_trafficLight$interse.interId,phaseId:phaseInfo.phaseId,direction:phaseInfo.direction,remainTime:phaseInfo.remainTime};// 添加光源使灯光更明显\nconst light=new THREE.PointLight(lightColor,1,50);light.position.set(0,12,0);light.userData={isLight:true};// 将灯光添加到交通灯模型\ntrafficLight.model.add(lightMesh);trafficLight.model.add(light);console.log(`更新路口 ${((_trafficLight$interse2=trafficLight.intersection)===null||_trafficLight$interse2===void 0?void 0:_trafficLight$interse2.name)||((_trafficLight$interse3=trafficLight.intersection)===null||_trafficLight$interse3===void 0?void 0:_trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);};export default CampusModel;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "Select", "Popover", "intersectionsData", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "lastPosition", "lastRotation", "ALPHA", "vehicleLastPositions", "Map", "vehicleLastRotations", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "process", "env", "REACT_APP_API_URL", "console", "log", "vehicleModels", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "vehicleId", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "has", "lastPos", "get", "distance", "distanceTo", "MAX_DISTANCE_THRESHOLD", "toFixed", "filteredPos", "Vector3", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "lastRot", "MAX_ANGLE_THRESHOLD", "abs", "TRAFFIC_LIGHT_UPDATE_INTERVAL", "CampusModel", "_ref", "className", "onCurrentRSUChange", "selectedRSUs", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "currentPopoverIdRef", "trafficLightUpdateTimerRef", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "target", "currentTarget", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "updateMatrix", "updateMatrixWorld", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "相机位置", "toArray", "目标点", "handleMqttMessage", "message", "payload", "JSON", "parse", "_payload$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "设备MAC", "消息时间戳", "最新时间戳", "时间戳", "是否为最新", "participants", "rsuid", "now", "Date", "for<PERSON>ach", "participant", "id", "partPtcId", "type", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "model", "newModel", "height", "rotation", "add", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "Set", "map", "p", "modelData", "remove", "delete", "bsmData", "bsmid", "newState", "partLong", "partLat", "postMessage", "source", "newPosition", "vehicleObj", "newVehicleModel", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "emissive", "Color", "transparent", "opacity", "needsUpdate", "speedLabel", "createTextSprite", "round", "r", "g", "b", "a", "textColor", "renderOrder", "is<PERSON><PERSON>", "Out", "filteredPosition", "dispose", "timeSinceLastUpdate", "undefined", "originalTran<PERSON>arent", "originalOpacity", "m", "geometry", "phasesInfo", "phase", "phaseId", "toString", "direction", "trafficDirec", "getDirectionFromCode", "getPhaseDirection", "lightState", "trafficLight", "remainTime", "parseInt", "phaseInfo", "push", "trafficLightKey", "String", "trafficLightModel", "updateTrafficLightVisual", "prev", "warn", "<PERSON><PERSON><PERSON>", "strId", "numId", "updateTime", "setTimeout", "showTrafficLightPopup", "rsiData", "rsuId", "events", "rtes", "event", "eventId", "rteId", "eventType", "description", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "事件ID", "事件类型", "事件说明", "开始时间", "结束时间", "位置", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "stringify", "onerror", "onclose", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "length", "xhr", "loaded", "total", "initializeScene", "loadModelWithRetry", "url", "maxRetries", "arguments", "attemptLoad", "retriesLeft", "loader", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "clearInterval", "close", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "clear", "lightObj", "handleMainVehicleChange", "intervalId", "setInterval", "timer", "createTrafficLights", "clearTimeout", "handleClick", "clientX", "clientY", "handleMouseClick", "initScene", "createSimpleTrafficLight", "BoxGeometry", "MeshBasicMaterial", "<PERSON><PERSON>", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "addClickHelpers", "helperGeometry", "SphereGeometry", "helperMaterial", "depthWrite", "helper<PERSON><PERSON>", "userData", "isClickHelper", "size", "firstIntersection", "style", "placeholder", "onChange", "options", "label", "bordered", "dropdownStyle", "maxHeight", "ref", "max<PERSON><PERSON><PERSON>", "right", "background", "onClick", "handleClosePopover", "text", "parameters", "params", "fontFace", "borderThickness", "borderColor", "canvas", "document", "createElement", "context", "getContext", "font", "textWidth", "measureText", "textBaseline", "radius", "beginPath", "moveTo", "lineTo", "arcTo", "closePath", "strokeStyle", "lineWidth", "stroke", "fillStyle", "fill", "textAlign", "fillText", "texture", "CanvasTexture", "minFilter", "LinearFilter", "spriteMaterial", "SpriteMaterial", "sprite", "Sprite", "depthTest", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "vehicleGltf", "cyclistGltf", "peopleGltf", "trafficLightGltf", "all", "loadAsync", "materia", "err", "types", "parent", "文本", "颜色", "converterInstance", "createFallbackTrafficLights", "hasTrafficLight", "side", "DoubleSide", "colliderGeometry", "colliderMaterial", "collider", "isCollider", "lightGeometry", "lightMaterial", "<PERSON><PERSON><PERSON>", "dirCode", "container", "sceneInstance", "cameraInstance", "setPopoverState", "rect", "getBoundingClientRect", "mouseX", "clientWidth", "mouseY", "clientHeight", "raycaster", "Raycaster", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "trafficLightObjects", "trafficLightIntersects", "intersectObjects", "intersect", "index", "object", "obj", "getTrafficLightFromObject", "correctId", "intersects", "closestLight", "worldPos", "getWorldPosition", "screenPos", "project", "dx", "dy", "sqrt", "testTrafficLightClick", "_document$querySelect", "_document$querySelect2", "light", "lightModel", "stateInfo", "overflowY", "marginBottom", "borderBottom", "paddingBottom", "lightColor", "justifyContent", "marginTop", "toLocaleTimeString", "centerX", "centerY", "__REACT_INSTANCE", "popover", "innerHTML", "body", "closeButton", "listTrafficLights", "list", "from", "keys", "_light$intersection", "numericId", "updateTrafficLightPopover", "_window$currentPopove", "currentId", "intersectionLight", "lightText", "updateInitialTrafficLightPopover", "_stateInfo", "testClickDetection", "tlIntersects", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "distanceToCamera", "名称", "可见性", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "isLight", "emissiveIntensity", "PointLight"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.15; // 降低滤波系数，使平滑效果更强（从0.3降到0.15）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  const lastPos = vehicleLastPositions.get(vehicleId);\n  \n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n  \n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  \n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  \n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const lastRot = vehicleLastRotations.get(vehicleId);\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  \n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);  // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n  \n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n  \n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n  \n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n  \n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      // const currentPos = cameraRef.current.position.clone();\n      cameraRef.current.position.set(0, 500, 0);\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      controls.target.set(0, 0, 0);\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x+50, 70, -modelCoords.y+50);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        console.log('收到RSM消息:', payload);\n        \n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n        \n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n        \n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          console.log('忽略过期的RSM消息:', {\n            设备MAC: deviceMac,\n            消息时间戳: messageTimestamp,\n            最新时间戳: lastTimestamp\n          });\n      return;\n    }\n    \n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        \n        console.log('RSM消息时间戳更新:', {\n          设备MAC: deviceMac,\n          时间戳: messageTimestamp,\n          是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        });\n        \n        const participants = payload.data?.participants || [];\n        const rsuid = payload.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id =  rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n          // 解析位置和状态信息\n          const state = {\n            longitude: parseFloat(participant.partPosLong),\n            latitude: parseFloat(participant.partPosLat),\n            speed: parseFloat(participant.partSpeed),\n            heading: parseFloat(participant.partHeading)\n          };\n          \n          const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n          \n          // 根据类型选择对应的预加载模型\n          let preloadedModel;\n          switch (type) {\n            case '1': // 机动车\n              preloadedModel = preloadedVehicleModel;\n              break;\n            case '2': // 非机动车\n              preloadedModel = preloadedCyclistModel;\n              break;\n            case '3': // 行人\n              preloadedModel = preloadedPeopleModel;\n              break;\n            default:\n              return; // 跳过未知类型\n          }\n          \n          // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n            // 创建新模型实例\n            const newModel = preloadedModel.clone();\n            // 根据类型调整高度\n            const height = type === '3' ? 0.5 : 1.0; // 行人模型贴近地面\n            newModel.position.set(modelPos.x, height, -modelPos.y);\n            newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            scene.add(newModel);\n            \n            vehicleModels.set(id, {\n              model: newModel,\n              lastUpdate: now,\n              type: type\n            });\n          } else if (model) {\n            // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 0.5 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n          }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        console.log('收到BSM消息:', payload);\n        \n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n        \n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n        \n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid, // 直接传递车辆ID\n          data: {       // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n        \n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        const newRotation = Math.PI - newState.heading * Math.PI / 180;\n        \n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n        \n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        \n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          \n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n          \n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n              \n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n              \n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n          \n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n          \n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          \n          scene.add(newVehicleModel);\n          \n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n          \n          console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position)\n            .to({ y: 0.5 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .start();\n          \n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({ opacity: 0.6 })\n                .to({ opacity: 1.0 }, 500)\n                .easing(TWEEN.Easing.Quadratic.Out)\n                .onUpdate(function() {\n                  child.material.opacity = this.opacity;\n                  child.material.needsUpdate = true;\n                })\n                .start();\n            }\n          });\n          \n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({ opacity: 0.6 })\n            .to({ opacity: 1.0 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .onUpdate(function() {\n              speedLabel.material.opacity = this.opacity;\n              speedLabel.material.needsUpdate = true;\n            })\n            .start();\n          \n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n          \n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n          \n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n          \n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n          \n          console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n        \n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 2000; // 增加到10秒，避免频繁清理造成闪烁\n        \n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n          \n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n                \n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n            \n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n            \n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            \n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        \n        return;\n      }\n      \n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        console.log('收到SPAT消息:', message);\n        \n        try {\n          const payload = JSON.parse(message);\n          \n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n          \n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n          \n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            console.log('忽略过期的SPAT消息:', {\n              设备MAC: deviceMac,\n              消息时间戳: messageTimestamp,\n              最新时间戳: lastTimestamp\n            });\n            return;\n          }\n          \n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n          \n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              \n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              \n              console.log(`处理路口ID: ${interId} 的SPAT消息`);\n              \n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                \n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  \n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? \n                    getDirectionFromCode(phase.trafficDirec) : \n                    getPhaseDirection(phaseId);\n                  \n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  \n                  console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n                  \n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n                  \n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n                  \n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  \n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  \n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n                    \n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n                \n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                \n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n                  \n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && \n                     (window.currentPopoverIdRef.current === modelKey || \n                      window.currentPopoverIdRef.current === String(modelKey) || \n                      window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    \n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n                    \n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n      }\n      \n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        console.log('收到RSI消息:', payload);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        \n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        \n        return;\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        console.log('收到场景事件消息:', payload);\n        \n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: payload.type,\n        data: payload\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n        \n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          \n          // 检查scene是否初始化\n          if (scene) {\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          100,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n      \n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n      \n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach((lightObj) => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n      \n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n      \n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n    \n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n    \n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n    \n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n    \n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      \n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n  \n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          console.log('检测到点击事件', event.clientX, event.clientY);\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        } else {\n          console.warn('点击事件处理失败: scene或camera未初始化');\n        }\n      };\n      \n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n      \n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n      \n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n    \n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    \n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n    \n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,//\n          transparent: false,\n          opacity: 0.1,  // 几乎透明\n          depthWrite: false\n        });\n        \n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置\n        \n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n        \n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        \n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500);  // 延迟略长于debugTrafficLights\n    \n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n    \n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n  \n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        const firstIntersection = intersectionsData.intersections[0];\n        console.log('自动选择第一个路口:', firstIntersection.name);\n        \n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(firstIntersection.name);\n        }, 2000);\n        \n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n\n  return (\n    <>\n      <span style={labelStyle}>区域选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择区域位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n        value={selectedIntersection ? selectedIntersection.name : undefined}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      \n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '4px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n            padding: '0',\n            maxWidth: '240px', // 缩小最大宽度\n            fontSize: '12px' // 缩小字体\n          }}\n        >\n          {trafficLightPopover.content}\n          <button \n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '12px',\n              cursor: 'pointer',\n              padding: '2px 6px'\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n      \n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 24,\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    backgroundColor: parameters.backgroundColor || { r: 255, g: 255, b: 255, a: 0.8 },\n    textColor: parameters.textColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  \n  // 设置字体\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  \n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n  \n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  \n  canvas.width = width;\n  canvas.height = height;\n  \n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n  \n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n  \n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n  \n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n  \n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n  \n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n  \n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 5, 1);\n  sprite.material.depthTest = false; // 确保始终可见\n  \n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = { \n    text: text,\n    params: params\n  };\n  \n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    try {\n      const [vehicleGltf, cyclistGltf, peopleGltf, trafficLightGltf] = await Promise.all([\n      loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)\n    ]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n          color: 0xff0000,  // 0xff0000, //红色 0xffffff,白色\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n      \n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse((child) => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n      }\n    });\n\n    console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n      \n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n        \n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  \n  try {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在1秒后自动移除\n  setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n    scene.remove(sprite);\n      }\n  }, 100);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  console.log('添加场景事件标记:', {\n    位置: position,\n    文本: text,\n    颜色: color\n  });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  \n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  \n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n  \n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型 - 只为hasTrafficLight为true的路口创建红绿灯\n  intersectionsData.intersections.forEach(intersection => {\n    // 检查路口是否有红绿灯属性，如果没有或为false，则跳过\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n    \n    // 确认路口有经纬度和ID\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      \n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n        \n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n        \n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n        \n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n        \n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n        \n        // *** 重要：设置为可被交互 ***\n        trafficLightModel.traverse(child => {\n          // 设置所有子对象可被点击\n          if (child.isMesh) {\n            // 确保材质能够被射线检测到\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            \n            // 设置较高的渲染顺序\n            child.renderOrder = 100;\n          }\n        });\n        \n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        \n        // // 添加一个专门用于点击的大型碰撞体\n        // const colliderGeometry = new THREE.SphereGeometry(5, 12, 12);\n        // const colliderMaterial = new THREE.MeshBasicMaterial({\n        //   color: 0xff00ff,\n        //   transparent: true,\n        //   opacity: 0.0,  // 完全透明\n        //   depthWrite: false\n        // });\n        \n        // const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n        // collider.name = `交通灯碰撞体-${intersection.name}`;\n        // collider.userData = {\n        //   type: 'trafficLight',\n        //   interId: intersection.interId,\n        //   name: intersection.name,\n        //   isCollider: true\n        // };\n        \n        // trafficLightModel.add(collider);\n        \n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n        \n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        \n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n  \n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance) => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({ \n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n  \n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n  \n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n  \n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n  \n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,  // 完全透明\n    depthWrite: false\n  });\n  \n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  \n  trafficLightModel.add(collider);\n  \n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n  \n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n  \n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n  \n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  trafficLightModel.add(lightMesh);\n  \n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  switch(phaseId) {\n    case '1': return '北进口左转';\n    case '2': return '北进口直行';\n    case '3': return '北进口右转';\n    case '5': return '东进口左转';\n    case '6': return '东进口直行';\n    case '7': return '东进口右转';\n    case '9': return '南进口左转';\n    case '10': return '南进口直行';\n    case '11': return '南进口右转';\n    case '13': return '西进口左转';\n    case '14': return '西进口直行';\n    case '15': return '西进口右转';\n    default: return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = (dirCode) => {\n  switch(dirCode) {\n    case 'N': return '北向南';\n    case 'S': return '南向北';\n    case 'E': return '东向西';\n    case 'W': return '西向东';\n    case 'NE': return '东北向西南';\n    case 'NW': return '西北向东南';\n    case 'SE': return '东南向西北';\n    case 'SW': return '西南向东北';\n    default: return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  \n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n  \n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n  \n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Line.threshold = 1;\n  \n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  \n  console.log('鼠标点击位置:', mouseX, mouseY);\n  \n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  \n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n  \n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  \n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, \n                 intersect.object.name || '无名称', \n                 '距离:', intersect.distance,\n                 'userData:', intersect.object.userData);\n    });\n    \n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    \n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n      \n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n      \n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n  \n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  \n  console.log('射线检测到的对象数量:', intersects.length);\n  \n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', \n                  'userData:', obj.userData, \n                  '距离:', intersect.distance);\n    });\n    \n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        console.log('检测到红绿灯点击, 路口ID:', interId);\n        \n        // 显示弹窗\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n  \n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n  \n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.1; // 设置一个阈值，只有距离小于此值的才会被选中\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n      \n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n      \n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      \n      console.log(`路口 ${interId} 的距离:`, distance);\n      \n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = { interId, distance };\n      }\n    }\n  });\n  \n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n    \n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  \n  console.log('未检测到任何红绿灯点击');\n  \n  // 如果用户点击了其他任何地方，关闭现有的弹窗\n  if (setPopoverState) {\n    setPopoverState(prev => {\n      if (prev.visible) {\n        return { ...prev, visible: false };\n      }\n      return prev;\n    });\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n  \n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n  \n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({ ...prev, visible: false }));\n  console.log('弹窗已关闭');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = (interId) => {\n  try {\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      \n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      \n      return false;\n    }\n    \n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n    \n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 创建弹出窗口内容\n    let content;\n    \n    if (stateInfo && stateInfo.phases) {\n      content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection.name} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              switch (phase.trafficLight) {\n                case 'G': lightColor = '#00ff00'; break;\n                case 'Y': lightColor = '#ffff00'; break;\n                case 'R': default: lightColor = '#ff0000'; break;\n              }\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {getPhaseDirection(phase.phaseId)}\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      content = (\n        <div style={{ padding: '8px', maxWidth: '200px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>\n          <div>路口ID: {interId}</div>\n          <div>当前无信号灯状态信息</div>\n        </div>\n      );\n    }\n    \n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2;\n    const centerY = window.innerHeight / 2;\n    \n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;\n    \n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: { x: centerX, y: centerY },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      \n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      \n      document.body.appendChild(popover);\n      \n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      \n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  \n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  \n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  \n  return list;\n};\n\n// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n\n//   console.log('开始调试红绿灯模型...');\n  \n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n  \n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n  \n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n      \n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n  \n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n  \n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n      \n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n      \n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n      \n//       // 添加到场景\n//       scene.add(highlightMesh);\n      \n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n      \n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n      \n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n  \n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = (interId) => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    \n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    console.log('当前trafficLightStates大小:', trafficLightStates.size);\n    \n    // 如果没有指定ID，则使用第一个红绿灯\n    if (!interId && trafficLightsMap.size > 0) {\n      interId = String(Array.from(trafficLightsMap.keys())[0]);\n      console.log('未指定ID，使用第一个红绿灯ID:', interId);\n    }\n    \n    // 输出所有可用的红绿灯ID用于调试\n    console.log('所有可用的红绿灯ID:');\n    trafficLightsMap.forEach((light, id) => {\n      console.log(`- ${id} (${typeof id}): ${light.intersection?.name || '未知'}`);\n    });\n    \n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      \n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    \n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    \n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n    \n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n    \n    // 创建更新函数\n    const updateTrafficLightPopover = () => {\n      // 确保当前弹窗仍然可见\n      if (!window._setTrafficLightPopover) return;\n      \n      const currentId = window.currentPopoverIdRef?.current;\n      if (!currentId) return;\n      \n      // 重新获取最新的状态信息\n      let stateInfo = trafficLightStates.get(currentId);\n      if (!stateInfo && typeof currentId === 'string') {\n        // 尝试使用数字ID\n        stateInfo = trafficLightStates.get(parseInt(currentId));\n      } else if (!stateInfo && typeof currentId === 'number') {\n        // 尝试使用字符串ID\n        stateInfo = trafficLightStates.get(String(currentId));\n      }\n      \n      if (!stateInfo || !stateInfo.phases || stateInfo.phases.length === 0) {\n        console.log(`路口ID ${currentId} 没有状态信息，跳过更新`);\n        return;\n      }\n      \n      // 获取交通灯信息\n      const intersectionLight = trafficLightsMap.get(currentId) || \n                               (typeof currentId === 'string' ? trafficLightsMap.get(parseInt(currentId)) : \n                                trafficLightsMap.get(String(currentId)));\n                                \n      if (!intersectionLight) {\n        console.error(`找不到路口ID ${currentId} 的红绿灯信息，无法更新弹窗`);\n        return;\n      }\n      \n      const intersection = intersectionLight.intersection;\n      \n      // 创建更新的弹窗内容\n      const content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection?.name || '未知路口'} (ID: {currentId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              let lightText;\n              \n              switch (phase.trafficLight) {\n                case 'G':\n                  lightColor = '#00ff00';\n                  lightText = '绿灯';\n                  break;\n                case 'Y':\n                  lightColor = '#ffff00';\n                  lightText = '黄灯';\n                  break;\n                case 'R':\n                default:\n                  lightColor = '#ff0000';\n                  lightText = '红灯';\n                  break;\n              }\n              \n              const direction = getPhaseDirection(phase.phaseId);\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {direction} (相位ID: {phase.phaseId})\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {lightText}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date(stateInfo.updateTime).toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n      \n      // 更新弹窗内容\n      window._setTrafficLightPopover(prev => ({\n        ...prev,\n        content: content,\n        phases: stateInfo.phases\n      }));\n      \n      console.log(`已更新路口 ${intersection?.name || currentId} 的红绿灯状态弹窗`);\n    };\n    \n    // 先执行一次初始更新\n    const updateInitialTrafficLightPopover = () => {\n      // 同样，查找红绿灯状态信息时也尝试字符串和数字两种类型\n      let stateInfo = trafficLightStates.get(interId);\n      if (!stateInfo) {\n        // 尝试使用数字ID\n        const numericId = parseInt(interId);\n        stateInfo = trafficLightStates.get(numericId);\n        \n        if (stateInfo) {\n          console.log(`使用数字ID ${numericId} 找到了红绿灯状态信息`);\n        }\n      }\n      \n      console.log(`路口ID ${interId} 的状态信息:`, stateInfo);\n      \n      const intersection = trafficLight.intersection;\n      console.log('路口信息:', intersection);\n      \n      // 创建弹窗内容\n      let content;\n      \n      if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n        // 添加一个检查相位数据的打印\n        console.log('相位数据详情:');\n        stateInfo.phases.forEach((phase, index) => {\n          console.log(`相位 ${index+1}: ID=${phase.phaseId}, 状态=${phase.trafficLight}, 剩余时间=${phase.remainTime}`);\n        });\n        \n        content = (\n          <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n            <div style={{ \n              fontWeight: 'bold', \n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            }}>\n              {intersection?.name || '未知路口'} (ID: {interId})\n            </div>\n            <div>\n              {stateInfo.phases.map((phase, index) => {\n                let lightColor;\n                let lightText;\n                \n                switch (phase.trafficLight) {\n                  case 'G':\n                    lightColor = '#00ff00';\n                    lightText = '绿灯';\n                    break;\n                  case 'Y':\n                    lightColor = '#ffff00';\n                    lightText = '黄灯';\n                    break;\n                  case 'R':\n                  default:\n                    lightColor = '#ff0000';\n                    lightText = '红灯';\n                    break;\n                }\n                \n                const direction = getPhaseDirection(phase.phaseId);\n                \n                return (\n                  <div key={index} style={{ \n                    marginBottom: '6px', \n                    backgroundColor: 'rgba(255,255,255,0.1)',\n                    padding: '4px',\n                    borderRadius: '4px',\n                    fontSize: '12px'\n                  }}>\n                    <div style={{ fontWeight: 'bold' }}>\n                      {direction} (相位ID: {phase.phaseId})\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <span>灯色: </span>\n                      <span style={{ \n                        color: lightColor, \n                        fontWeight: 'bold',\n                        backgroundColor: 'rgba(0,0,0,0.3)',\n                        padding: '0 3px',\n                        borderRadius: '2px'\n                      }}>\n                        {lightText}\n                      </span>\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <span>倒计时: </span>\n                      <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n            <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n              更新时间: {new Date(stateInfo.updateTime).toLocaleTimeString()} (自动刷新)\n            </div>\n          </div>\n        );\n      } else {\n        // 如果没有状态信息，创建一个示例内容\n        content = (\n          <div style={{ padding: '8px', width: '220px' }}>\n            <div style={{ \n              fontWeight: 'bold', \n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            }}>\n              {intersection?.name || '未知路口'} (ID: {interId})\n            </div>\n            <div style={{ color: '#ff4d4f', fontSize: '12px' }}>\n              该路口暂无红绿灯状态信息，请等待SPAT消息更新。\n            </div>\n            <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n              当前时间: {new Date().toLocaleTimeString()}\n            </div>\n          </div>\n        );\n      }\n      \n      // 设置弹窗位置在屏幕中央\n      const x = window.innerWidth / 2;\n      const y = window.innerHeight / 2;\n      \n      // 更新弹窗状态\n      if (window._setTrafficLightPopover) {\n        console.log('调用_setTrafficLightPopover更新弹窗状态', {\n          visible: true,\n          interId,\n          position: { x, y }\n        });\n        \n        window._setTrafficLightPopover({\n          visible: true,\n          interId: interId,\n          position: { x, y },\n          content: content,\n          phases: stateInfo?.phases || []\n        });\n        \n        console.log(`已显示路口 ${intersection?.name || interId} 的红绿灯状态弹窗`);\n        \n        // 设置定时更新\n        if (window.trafficLightUpdateTimerRef) {\n          window.trafficLightUpdateTimerRef.current = setInterval(updateTrafficLightPopover, TRAFFIC_LIGHT_UPDATE_INTERVAL * 1000);\n          console.log(`已设置红绿灯状态弹窗定时更新，间隔 ${TRAFFIC_LIGHT_UPDATE_INTERVAL} 秒`);\n        }\n        \n        return true;\n      } else {\n        console.error('无法找到setTrafficLightPopover函数');\n        return false;\n      }\n    };\n    \n    return updateInitialTrafficLightPopover();\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n  \n//   console.log('开始生成模拟SPAT数据...');\n  \n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n  \n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n  \n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n    \n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n      \n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n    \n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n      \n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n      \n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n    \n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n    \n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n    \n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n    \n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n  \n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n  \n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n  \n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n  \n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n  \n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n  \n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  \n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n    \n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n    \n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n    \n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n    \n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    \n    console.log('归一化坐标:', mouseX, mouseY);\n    \n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    \n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n    \n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n    \n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    \n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', \n                    '距离:', intersect.distance,\n                    'position:', intersect.object.position.toArray(),\n                    'userData:', intersect.object.userData);\n        \n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      \n      return true;\n    }\n    \n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    \n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', \n                  '类型:', obj.type,\n                  '位置:', obj.position.toArray(),\n                  '距离:', intersect.distance,\n                  'userData:', obj.userData);\n    });\n    \n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    \n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n        \n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n        \n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n        \n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        \n        if (isVisible) {\n          visibleCount++;\n        }\n        \n        console.log(`红绿灯 ${interId}:`, {\n          名称: lightObj.intersection?.name || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    \n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n    \n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  \n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch(phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n  \n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ \n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: trafficLight.intersection?.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n  \n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = { isLight: true };\n  \n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  \n  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\n\nexport default CampusModel; \n\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,WAAW,KAAQ,OAAO,CACvE,MAAO,GAAK,CAAAC,KAAK,KAAM,OAAO,CAC9B,OAASC,UAAU,KAAQ,uCAAuC,CAClE,OAASC,aAAa,KAAQ,2CAA2C,CACzE,OAASC,mBAAmB,KAAQ,8BAA8B,CAClE,MAAO,GAAK,CAAAC,KAAK,KAAM,mBAAmB,CAC1C,MAAO,CAAAC,IAAI,KAAM,MAAM,CACvB,OAASC,MAAM,CAAEC,OAAO,KAAQ,MAAM,CACtC,MAAO,CAAAC,iBAAiB,KAAM,4BAA4B,CAE1D;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,GAAI,CAAAC,gBAAgB,CAAG,IAAI,CAC3B,GAAI,CAAAC,gBAAgB,CAAG,EAAE,CACzB,GAAI,CAAAC,iBAAiB,CAAG,CAAC,CACzB,GAAI,CAAAC,oBAAoB,CAAG,IAAI,CAC/B,GAAI,CAAAC,cAAc,CAAG,IAAI,CAAG;AAC5B,GAAI,CAAAC,eAAe,CAAG,IAAI,CAAE;AAC5B,GAAI,CAAAC,QAAQ,CAAG,KAAK,CAAO;AAC3B,GAAI,CAAAC,UAAU,CAAG,QAAQ,CAAE;AAC3B,GAAI,CAAAC,QAAQ,CAAG,IAAI,CAAE;AAErB;AACA,GAAI,CAAAC,qBAAqB,CAAG,IAAI,CAChC,GAAI,CAAAC,qBAAqB,CAAG,IAAI,CAAG;AACnC,GAAI,CAAAC,oBAAoB,CAAG,IAAI,CAAI;AACnC,GAAI,CAAAC,0BAA0B,CAAG,IAAI,CAAE;AACvC,GAAI,CAAAC,KAAK,CAAG,IAAI,CAAE;AAElB;AACA,GAAI,CAAAC,YAAY,CAAG,IAAI,CACvB,GAAI,CAAAC,YAAY,CAAG,IAAI,CACvB,KAAM,CAAAC,KAAK,CAAG,IAAI,CAAE;AAEpB;AACA,KAAM,CAAAC,oBAAoB,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAE;AACxC,KAAM,CAAAC,oBAAoB,CAAG,GAAI,CAAAD,GAAG,CAAC,CAAC,CAAE;AAExC;AACA,KAAM,CAAAE,WAAW,CAAG,CAClBC,MAAM,CAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAChCC,IAAI,CAAE,IAAI,CACV;AACEC,GAAG,CAAE,2BAA2B,CAChCC,GAAG,CAAE,2BAA2B,CAChCd,KAAK,CAAE,6BAA6B,CACtCe,GAAG,CAAE,2BAA2B,CAAG;AACnCC,IAAI,CAAE,4BAA8B;AACtC,CAAC,CAED;AACA,KAAM,CAAAC,QAAQ,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACzEC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC,CAE1D;AACA,KAAM,CAAAG,aAAa,CAAG,GAAI,CAAAlB,GAAG,CAAC,CAAC,CAAE;AAEjC;AACA,GAAI,CAAAmB,gBAAgB,CAAG,GAAI,CAAAnB,GAAG,CAAC,CAAC,CAAE;AAElC;AACA,GAAI,CAAAoB,gBAAgB,CAAG,IAAI,CAE3B;AACA,GAAI,CAAAC,gBAAgB,CAAG,GAAI,CAAArB,GAAG,CAAC,CAAC,CAAE;AAClC,GAAI,CAAAsB,kBAAkB,CAAG,GAAI,CAAAtB,GAAG,CAAC,CAAC,CAAE;AAEpC;AACA,KAAM,CAAAuB,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAGb,QAAQ,oBAAoB,CAAC,CAC7D,KAAM,CAAAc,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,EAAIA,IAAI,CAACE,QAAQ,EAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,CAAE,CACzD,KAAM,CAAAG,WAAW,CAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,aAAa,GAAK,IAAI,CAAC,CACrE,GAAIH,WAAW,EAAIA,WAAW,CAACI,KAAK,CAAE,CACpCf,gBAAgB,CAAGW,WAAW,CAACI,KAAK,CACpCnB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEG,gBAAgB,CAAC,CAC7C,MAAO,CAAAA,gBAAgB,CACzB,CACF,CACAJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC,CAChCG,gBAAgB,CAAG,OAAO,CAAE;AAC5B,MAAO,CAAAA,gBAAgB,CACzB,CAAE,MAAOgB,KAAK,CAAE,CACdpB,OAAO,CAACoB,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjChB,gBAAgB,CAAG,OAAO,CAAE;AAC5B,MAAO,CAAAA,gBAAgB,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAAiB,aAAa,CAAGA,CAACC,QAAQ,CAAEC,SAAS,CAAEC,KAAK,GAAK,CACpD,GAAID,SAAS,GAAK,IAAI,CAAE,MAAO,CAAAD,QAAQ,CACvC,MAAO,CAAAE,KAAK,CAAGF,QAAQ,CAAG,CAAC,CAAC,CAAGE,KAAK,EAAID,SAAS,CACnD,CAAC,CAED;AACA,KAAM,CAAAE,cAAc,CAAGA,CAACC,MAAM,CAAEC,SAAS,GAAK,CAC5C;AACA,GAAI,CAACA,SAAS,CAAE,CAChB,GAAI,CAAC/C,YAAY,CAAE,CACjBA,YAAY,CAAG8C,MAAM,CAACE,KAAK,CAAC,CAAC,CAC7B,MAAO,CAAAF,MAAM,CACf,CAEA,KAAM,CAAAG,SAAS,CAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,CAAElD,YAAY,CAACkD,CAAC,CAAEhD,KAAK,CAAC,CAChE,KAAM,CAAAiD,SAAS,CAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,CAAEpD,YAAY,CAACoD,CAAC,CAAElD,KAAK,CAAC,CAChE,KAAM,CAAAmD,SAAS,CAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,CAAEtD,YAAY,CAACsD,CAAC,CAAEpD,KAAK,CAAC,CAEhEF,YAAY,CAACuD,GAAG,CAACN,SAAS,CAAEE,SAAS,CAAEE,SAAS,CAAC,CACjD,MAAO,CAAArD,YAAY,CAACgD,KAAK,CAAC,CAAC,CAC3B,CAEA;AACA,GAAI,CAAC7C,oBAAoB,CAACqD,GAAG,CAACT,SAAS,CAAC,CAAE,CACxC5C,oBAAoB,CAACoD,GAAG,CAACR,SAAS,CAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CACnD,MAAO,CAAAF,MAAM,CACf,CAEA,KAAM,CAAAW,OAAO,CAAGtD,oBAAoB,CAACuD,GAAG,CAACX,SAAS,CAAC,CAEnD;AACA,KAAM,CAAAY,QAAQ,CAAGF,OAAO,CAACG,UAAU,CAACd,MAAM,CAAC,CAC3C,KAAM,CAAAe,sBAAsB,CAAG,EAAE,CAAE;AAEnC,GAAIF,QAAQ,CAAGE,sBAAsB,CAAE,CACrCzC,OAAO,CAACC,GAAG,CAAC,KAAK0B,SAAS,UAAUY,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAClE3D,oBAAoB,CAACoD,GAAG,CAACR,SAAS,CAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CACnD,MAAO,CAAAF,MAAM,CACf,CAEA;AACA,KAAM,CAAAG,SAAS,CAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,CAAEO,OAAO,CAACP,CAAC,CAAEhD,KAAK,CAAC,CAC3D,KAAM,CAAAiD,SAAS,CAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,CAAEK,OAAO,CAACL,CAAC,CAAElD,KAAK,CAAC,CAC3D,KAAM,CAAAmD,SAAS,CAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,CAAEG,OAAO,CAACH,CAAC,CAAEpD,KAAK,CAAC,CAE3D,KAAM,CAAA6D,WAAW,CAAG,GAAI,CAAA5F,KAAK,CAAC6F,OAAO,CAACf,SAAS,CAAEE,SAAS,CAAEE,SAAS,CAAC,CACtElD,oBAAoB,CAACoD,GAAG,CAACR,SAAS,CAAEgB,WAAW,CAACf,KAAK,CAAC,CAAC,CAAC,CAExD,MAAO,CAAAe,WAAW,CACpB,CAAC,CAED;AACA,KAAM,CAAAE,cAAc,CAAGA,CAACC,WAAW,CAAEnB,SAAS,GAAK,CACjD;AACA,GAAI,CAACA,SAAS,CAAE,CAChB,GAAI9C,YAAY,GAAK,IAAI,CAAE,CACzBA,YAAY,CAAGiE,WAAW,CAC1B,MAAO,CAAAA,WAAW,CACpB,CAEA;AACA,GAAI,CAAAC,IAAI,CAAGD,WAAW,CAAGjE,YAAY,CACrC,GAAIkE,IAAI,CAAGC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CACvC,GAAIF,IAAI,CAAG,CAACC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CAExC,KAAM,CAAAC,gBAAgB,CAAG7B,aAAa,CAACxC,YAAY,CAAGkE,IAAI,CAAElE,YAAY,CAAEC,KAAK,CAAC,CAChFD,YAAY,CAAGqE,gBAAgB,CAC7B,MAAO,CAAAA,gBAAgB,CACzB,CAEA;AACA,GAAI,CAACjE,oBAAoB,CAACmD,GAAG,CAACT,SAAS,CAAC,CAAE,CACxC1C,oBAAoB,CAACkD,GAAG,CAACR,SAAS,CAAEmB,WAAW,CAAC,CAChD,MAAO,CAAAA,WAAW,CACpB,CAEA,KAAM,CAAAK,OAAO,CAAGlE,oBAAoB,CAACqD,GAAG,CAACX,SAAS,CAAC,CAEnD;AACA,GAAI,CAAAoB,IAAI,CAAGD,WAAW,CAAGK,OAAO,CAChC,GAAIJ,IAAI,CAAGC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CACvC,GAAIF,IAAI,CAAG,CAACC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CAExC;AACA,KAAM,CAAAG,mBAAmB,CAAGJ,IAAI,CAACC,EAAE,CAAG,CAAC,CAAE;AACzC,GAAID,IAAI,CAACK,GAAG,CAACN,IAAI,CAAC,CAAGK,mBAAmB,CAAE,CACxCpD,OAAO,CAACC,GAAG,CAAC,KAAK0B,SAAS,UAAU,CAACoB,IAAI,CAAG,GAAG,CAAGC,IAAI,CAACC,EAAE,EAAEP,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAChFzD,oBAAoB,CAACkD,GAAG,CAACR,SAAS,CAAEmB,WAAW,CAAC,CAChD,MAAO,CAAAA,WAAW,CACpB,CAEA,KAAM,CAAAI,gBAAgB,CAAG7B,aAAa,CAAC8B,OAAO,CAAGJ,IAAI,CAAEI,OAAO,CAAErE,KAAK,CAAC,CACtEG,oBAAoB,CAACkD,GAAG,CAACR,SAAS,CAAEuB,gBAAgB,CAAC,CAErD,MAAO,CAAAA,gBAAgB,CACzB,CAAC,CAED;AACA;AACA;AACA,KAAM,CAAAI,6BAA6B,CAAG,CAAC,CAAE;AACzC;AAEA,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAqD,IAApD,CAAEC,SAAS,CAAEC,kBAAkB,CAAEC,YAAa,CAAC,CAAAH,IAAA,CAClE,KAAM,CAAAI,YAAY,CAAGhH,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAAiH,UAAU,CAAGjH,MAAM,CAAC,IAAI,CAAC,CAC/B,KAAM,CAAAkH,SAAS,CAAGlH,MAAM,CAAC,GAAI,CAAAM,mBAAmB,CAAC,CAAC,CAAC,CACnD,KAAM,CAAA6G,aAAa,CAAGnH,MAAM,CAAC,EAAE,CAAC,CAChC,KAAM,CAAAoH,eAAe,CAAGpH,MAAM,CAAC,CAAC,CAAC,CACjC,KAAM,CAAAqH,aAAa,CAAGrH,MAAM,CAAC,IAAI,CAAC,CAClC,KAAM,CAAAsH,iBAAiB,CAAGtH,MAAM,CAAC,IAAI,CAAC,CAAG;AACzC,KAAM,CAACuH,eAAe,CAAEC,kBAAkB,CAAC,CAAGvH,QAAQ,CAAC,KAAK,CAAC,CAAG;AAEhE;AACA,KAAM,CAACwH,YAAY,CAAEC,eAAe,CAAC,CAAGzH,QAAQ,CAAC,CAC/C0H,SAAS,CAAE,CAAC,CACZC,QAAQ,CAAE,CAAC,CACXC,KAAK,CAAE,CAAC,CACRC,OAAO,CAAE,CACX,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAG/H,QAAQ,CAAC,QAAQ,CAAC,CAElD;AACA,KAAM,CAAAgI,oBAAoB,CAAG,CAC3BC,QAAQ,CAAE,OAAO,CACjBC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,kBAAkB,CAC7BC,MAAM,CAAE,IAAI,CAAG;AACfC,OAAO,CAAE,MAAM,CACfC,GAAG,CAAE,MACP,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG,CAClBC,OAAO,CAAE,UAAU,CACnBC,eAAe,CAAE,0BAA0B,CAC3CC,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,2BAA2B,CACtCC,UAAU,CAAE,eACd,CAAC,CAED;AACA,KAAM,CAAAC,SAAS,CAAGlJ,MAAM,CAAC,IAAI,CAAC,CAE9B;AACA,KAAM,CAACmJ,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGnJ,QAAQ,CAAC,IAAI,CAAC,CAEtE;AACA,KAAM,CAACoJ,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGrJ,QAAQ,CAAC,CAC7DsJ,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,IAAI,CACbtB,QAAQ,CAAE,CAAEhD,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CACxBqE,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,EACV,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,mBAAmB,CAAG3J,MAAM,CAAC,IAAI,CAAC,CAExC;AACA,KAAM,CAAA4J,0BAA0B,CAAG5J,MAAM,CAAC,IAAI,CAAC,CAE/C;AACAwC,MAAM,CAACqH,uBAAuB,CAAGP,sBAAsB,CAEvD;AACA9G,MAAM,CAACmH,mBAAmB,CAAGA,mBAAmB,CAChDnH,MAAM,CAACoH,0BAA0B,CAAGA,0BAA0B,CAE9D;AACA,KAAM,CAAAE,uBAAuB,CAAG,CAC9B5B,QAAQ,CAAE,OAAO,CACjB6B,GAAG,CAAE,MAAM,CACX3B,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,kBAAkB,CAC7B2B,KAAK,CAAE,OAAO,CAAG;AACjB1B,MAAM,CAAE,IAAI,CACZK,eAAe,CAAE,OAAO,CACxBE,YAAY,CAAE,KAAK,CACnBG,SAAS,CAAE,2BACb,CAAC,CAED;AACA,KAAM,CAAAiB,UAAU,CAAG,CACjB/B,QAAQ,CAAE,OAAO,CACjB6B,GAAG,CAAE,MAAM,CACX3B,IAAI,CAAE,kBAAkB,CAAG;AAC3BC,SAAS,CAAE,mBAAmB,CAC9BK,OAAO,CAAE,OAAO,CAAG;AACnBwB,UAAU,CAAE,MAAM,CAClBC,KAAK,CAAE,MAAM,CACbpB,QAAQ,CAAE,MAAM,CAChBqB,UAAU,CAAE,MAAM,CAClBC,UAAU,CAAE,2BAA2B,CACvC/B,MAAM,CAAE,IACV,CAAC,CAED;AACA,KAAM,CAAC7E,gBAAgB,CAAC,CAAGxD,QAAQ,CAAC,GAAI,CAAAmC,GAAG,CAAC,CAAC,CAAC,CAE9C;AACA,KAAM,CAACkI,UAAU,CAAEC,aAAa,CAAC,CAAGtK,QAAQ,CAAC,IAAI,CAAC,CAElD;AACA,KAAM,CAACsD,gBAAgB,CAAEiH,mBAAmB,CAAC,CAAGvK,QAAQ,CAAC,GAAI,CAAAmC,GAAG,CAAC,CAAC,CAAC,CAEnE;AACA,KAAM,CAACqI,WAAW,CAAEC,cAAc,CAAC,CAAGzK,QAAQ,CAAC,CAAE0K,KAAK,CAAE,EAAE,CAAElB,OAAO,CAAE,EAAG,CAAC,CAAC,CAE1E;AACA,KAAM,CAAAmB,kBAAkB,CAAGA,CAAA,GAAM,CAC/B5C,WAAW,CAAC,QAAQ,CAAC,CACrBvG,UAAU,CAAG,QAAQ,CAErB,GAAIC,QAAQ,CAAE,CACZA,QAAQ,CAACmJ,OAAO,CAAG,KAAK,CAC1B,CACF,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B9C,WAAW,CAAC,QAAQ,CAAC,CACrBvG,UAAU,CAAG,QAAQ,CAErB,GAAIyH,SAAS,CAAC6B,OAAO,EAAIrJ,QAAQ,CAAE,CACjC;AACA;AACAwH,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAC3C,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CACzC,KAAM,CAAAyF,UAAU,CAAG9B,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAClD,KAAK,CAAC,CAAC,CACrD,KAAM,CAAAiG,SAAS,CAAG/B,SAAS,CAAC6B,OAAO,CAACG,EAAE,CAAClG,KAAK,CAAC,CAAC,CAE9C;AACA,GAAI,CAAAzE,KAAK,CAAC4K,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC,CAAElG,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,GAAG,CAAEE,CAAC,CAAE,CAAE,CAAC,CAAE,IAAI,CAAC,CAChC+F,MAAM,CAAC9K,KAAK,CAAC+K,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,IAAM,CACdvC,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAACwD,IAAI,CAACV,UAAU,CAAC,CAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC,CAEV;AACA,GAAI,CAAApL,KAAK,CAAC4K,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC,CAAElG,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CAAE,IAAI,CAAC,CAC9B+F,MAAM,CAAC9K,KAAK,CAAC+K,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,IAAM,CACdvC,SAAS,CAAC6B,OAAO,CAACG,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC,CACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC,CAEV;AACAjK,QAAQ,CAACkK,MAAM,CAACrG,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5B,KAAM,CAAAsG,aAAa,CAAGnK,QAAQ,CAACkK,MAAM,CAAC5G,KAAK,CAAC,CAAC,CAE7C;AACA,GAAI,CAAAzE,KAAK,CAAC4K,KAAK,CAACU,aAAa,CAAC,CAC3BT,EAAE,CAAC,CAAElG,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CAAE,IAAI,CAAC,CAC9B+F,MAAM,CAAC9K,KAAK,CAAC+K,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,IAAM,CACd/J,QAAQ,CAACkK,MAAM,CAACF,IAAI,CAACG,aAAa,CAAC,CACnC;AACA3C,SAAS,CAAC6B,OAAO,CAACe,MAAM,CAACpK,QAAQ,CAACkK,MAAM,CAAC,CACzClK,QAAQ,CAACqK,MAAM,CAAC,CAAC,CACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC,CAEV;AACAjK,QAAQ,CAACmJ,OAAO,CAAG,IAAI,CAEvB;AACAnJ,QAAQ,CAACsK,WAAW,CAAG,EAAE,CACzBtK,QAAQ,CAACuK,WAAW,CAAG,GAAG,CAC1BvK,QAAQ,CAACwK,aAAa,CAAG9F,IAAI,CAACC,EAAE,CAAG,GAAG,CACtC3E,QAAQ,CAACyK,aAAa,CAAG,CAAC,CAC1BzK,QAAQ,CAACqK,MAAM,CAAC,CAAC,CACjB;AACA7C,SAAS,CAAC6B,OAAO,CAACqB,YAAY,CAAC,CAAC,CAChClD,SAAS,CAAC6B,OAAO,CAACsB,iBAAiB,CAAC,IAAI,CAAC,CAEzCjJ,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE,CACrBiJ,MAAM,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CACnBC,KAAK,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAChBC,KAAK,CAAE,IACT,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAC,wBAAwB,CAAIC,KAAK,EAAK,CAC1C,KAAM,CAAAC,YAAY,CAAGhM,iBAAiB,CAACiM,aAAa,CAACxI,IAAI,CAACyI,CAAC,EAAIA,CAAC,CAACC,IAAI,GAAKJ,KAAK,CAAC,CAChF,GAAIC,YAAY,EAAIzD,SAAS,CAAC6B,OAAO,EAAIrJ,QAAQ,CAAE,CACjD0H,uBAAuB,CAACuD,YAAY,CAAC,CAErC;AACA,KAAM,CAAAI,WAAW,CAAG7F,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAChDC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,CAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC,CAEDxE,OAAO,CAACC,GAAG,CAAC,WAAW,CAAE,CACvB6J,IAAI,CAAEP,YAAY,CAACG,IAAI,CACvBK,GAAG,CAAE,CACHxF,SAAS,CAAEgF,YAAY,CAAChF,SAAS,CACjCC,QAAQ,CAAE+E,YAAY,CAAC/E,QACzB,CAAC,CACDwF,IAAI,CAAEL,WACR,CAAC,CAAC,CAEF;AACAtL,UAAU,CAAG,cAAc,CAC3BuG,WAAW,CAAC,cAAc,CAAC,CAE3B;AACAkB,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAC3C,GAAG,CAACwH,WAAW,CAAC7H,CAAC,CAAC,EAAE,CAAE,EAAE,CAAE,CAAC6H,WAAW,CAAC3H,CAAC,CAAC,EAAE,CAAC,CAEvE;AACA1D,QAAQ,CAACkK,MAAM,CAACrG,GAAG,CAACwH,WAAW,CAAC7H,CAAC,CAAE,CAAC,CAAE,CAAC6H,WAAW,CAAC3H,CAAC,CAAC,CAErD;AACA8D,SAAS,CAAC6B,OAAO,CAACe,MAAM,CAACpK,QAAQ,CAACkK,MAAM,CAAC,CAEzC;AACAlK,QAAQ,CAACmJ,OAAO,CAAG,IAAI,CACvBnJ,QAAQ,CAACqK,MAAM,CAAC,CAAC,CAEjB;AACA7C,SAAS,CAAC6B,OAAO,CAACqB,YAAY,CAAC,CAAC,CAChClD,SAAS,CAAC6B,OAAO,CAACsB,iBAAiB,CAAC,IAAI,CAAC,CAEzCjJ,OAAO,CAACC,GAAG,CAAC,aAAa,CAAE,CACzB6J,IAAI,CAAEP,YAAY,CAACG,IAAI,CACvBO,IAAI,CAAEnE,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAACoF,OAAO,CAAC,CAAC,CAC1CC,GAAG,CAAE7L,QAAQ,CAACkK,MAAM,CAAC0B,OAAO,CAAC,CAAC,CAC9BF,IAAI,CAAEL,WACR,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAS,iBAAiB,CAAGA,CAAC7C,KAAK,CAAE8C,OAAO,GAAK,CAC5C,GAAI,CACF,KAAM,CAAAC,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC,CAEnC;AACA,GAAI9C,KAAK,GAAKrI,WAAW,CAACO,GAAG,CAAE,KAAAgL,aAAA,CAC7BzK,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEqK,OAAO,CAAC,CAEhC;AACA,KAAM,CAAAI,SAAS,CAAGJ,OAAO,CAACK,GAAG,CAC7B,KAAM,CAAAC,gBAAgB,CAAGN,OAAO,CAACO,EAAE,CAEnC;AACA,KAAM,CAAAC,aAAa,CAAG3K,gBAAgB,CAACmC,GAAG,CAACoI,SAAS,CAAC,CAErD;AACA,GAAII,aAAa,EAAIF,gBAAgB,CAAGE,aAAa,CAAE,CACrD9K,OAAO,CAACC,GAAG,CAAC,aAAa,CAAE,CACzB8K,KAAK,CAAEL,SAAS,CAChBM,KAAK,CAAEJ,gBAAgB,CACvBK,KAAK,CAAEH,aACT,CAAC,CAAC,CACN,OACF,CAEI;AACA3K,gBAAgB,CAACgC,GAAG,CAACuI,SAAS,CAAEE,gBAAgB,CAAC,CAEjD5K,OAAO,CAACC,GAAG,CAAC,aAAa,CAAE,CACzB8K,KAAK,CAAEL,SAAS,CAChBQ,GAAG,CAAEN,gBAAgB,CACrBO,KAAK,CAAE,CAACL,aAAa,EAAIF,gBAAgB,EAAIE,aAC/C,CAAC,CAAC,CAEF,KAAM,CAAAM,YAAY,CAAG,EAAAX,aAAA,CAAAH,OAAO,CAAC5J,IAAI,UAAA+J,aAAA,iBAAZA,aAAA,CAAcW,YAAY,GAAI,EAAE,CACrD,KAAM,CAAAC,KAAK,CAAGf,OAAO,CAAC5J,IAAI,CAAC2K,KAAK,CAEhC;AACA,KAAM,CAAAC,GAAG,CAAGC,IAAI,CAACD,GAAG,CAAC,CAAC,CAEtB;AACAF,YAAY,CAACI,OAAO,CAACC,WAAW,EAAI,CAClC;AACA,KAAM,CAAAC,EAAE,CAAIL,KAAK,CAAGI,WAAW,CAACE,SAAS,CACzC,KAAM,CAAAC,IAAI,CAAGH,WAAW,CAACI,WAAW,CAEpC,GAAGD,IAAI,GAAK,GAAG,EAAEA,IAAI,GAAK,GAAG,EAAEA,IAAI,GAAK,GAAG,CAAC,CAC5C;AACA;AACA;AACA,KAAM,CAAAE,KAAK,CAAG,CACZvH,SAAS,CAAEsF,UAAU,CAAC4B,WAAW,CAACM,WAAW,CAAC,CAC9CvH,QAAQ,CAAEqF,UAAU,CAAC4B,WAAW,CAACO,UAAU,CAAC,CAC5CvH,KAAK,CAAEoF,UAAU,CAAC4B,WAAW,CAACQ,SAAS,CAAC,CACxCvH,OAAO,CAAEmF,UAAU,CAAC4B,WAAW,CAACS,WAAW,CAC7C,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGrI,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAACkC,KAAK,CAACvH,SAAS,CAAEuH,KAAK,CAACtH,QAAQ,CAAC,CAEhF;AACA,GAAI,CAAA4H,cAAc,CAClB,OAAQR,IAAI,EACV,IAAK,GAAG,CAAE;AACRQ,cAAc,CAAG7N,qBAAqB,CACtC,MACF,IAAK,GAAG,CAAE;AACR6N,cAAc,CAAG5N,qBAAqB,CACtC,MACF,IAAK,GAAG,CAAE;AACR4N,cAAc,CAAG3N,oBAAoB,CACrC,MACF,QACE,OAAQ;AACZ,CAEA;AACA,GAAI,CAAA4N,KAAK,CAAGnM,aAAa,CAACoC,GAAG,CAACoJ,EAAE,CAAC,CAEjC,GAAI,CAACW,KAAK,EAAID,cAAc,CAAE,CAC5B;AACA,KAAM,CAAAE,QAAQ,CAAGF,cAAc,CAACxK,KAAK,CAAC,CAAC,CACvC;AACA,KAAM,CAAA2K,MAAM,CAAGX,IAAI,GAAK,GAAG,CAAG,GAAG,CAAG,GAAG,CAAE;AACzCU,QAAQ,CAACxH,QAAQ,CAAC3C,GAAG,CAACgK,QAAQ,CAACrK,CAAC,CAAEyK,MAAM,CAAE,CAACJ,QAAQ,CAACnK,CAAC,CAAC,CACtDsK,QAAQ,CAACE,QAAQ,CAACxK,CAAC,CAAGgB,IAAI,CAACC,EAAE,CAAG6I,KAAK,CAACpH,OAAO,CAAG1B,IAAI,CAACC,EAAE,CAAG,GAAG,CAC7DtE,KAAK,CAAC8N,GAAG,CAACH,QAAQ,CAAC,CAEnBpM,aAAa,CAACiC,GAAG,CAACuJ,EAAE,CAAE,CACpBW,KAAK,CAAEC,QAAQ,CACfI,UAAU,CAAEpB,GAAG,CACfM,IAAI,CAAEA,IACR,CAAC,CAAC,CACJ,CAAC,IAAM,IAAIS,KAAK,CAAE,CAChB;AACAA,KAAK,CAACA,KAAK,CAACvH,QAAQ,CAAC3C,GAAG,CAACgK,QAAQ,CAACrK,CAAC,CAAEuK,KAAK,CAACT,IAAI,GAAK,GAAG,CAAG,GAAG,CAAG,GAAG,CAAE,CAACO,QAAQ,CAACnK,CAAC,CAAC,CACjFqK,KAAK,CAACA,KAAK,CAACG,QAAQ,CAACxK,CAAC,CAAGgB,IAAI,CAACC,EAAE,CAAG6I,KAAK,CAACpH,OAAO,CAAG1B,IAAI,CAACC,EAAE,CAAG,GAAG,CAChEoJ,KAAK,CAACK,UAAU,CAAGpB,GAAG,CACtBe,KAAK,CAACA,KAAK,CAACrD,YAAY,CAAC,CAAC,CAC1BqD,KAAK,CAACA,KAAK,CAACpD,iBAAiB,CAAC,IAAI,CAAC,CACrC,CACA,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAA0D,iBAAiB,CAAG,IAAI,CAC9B,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAC,GAAG,CAACzB,YAAY,CAAC0B,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACpB,SAAS,CAAC,CAAC,CAE9DzL,aAAa,CAACsL,OAAO,CAAC,CAACwB,SAAS,CAAEtB,EAAE,GAAK,CACvC,GAAIJ,GAAG,CAAG0B,SAAS,CAACN,UAAU,CAAGC,iBAAiB,EAAI,CAACC,UAAU,CAACxK,GAAG,CAACsJ,EAAE,CAAC,CAAE,CACzE/M,KAAK,CAACsO,MAAM,CAACD,SAAS,CAACX,KAAK,CAAC,CAC7BnM,aAAa,CAACgN,MAAM,CAACxB,EAAE,CAAC,CACxB1L,OAAO,CAACC,GAAG,CAAC,oBAAoByL,EAAE,QAAQsB,SAAS,CAACpB,IAAI,EAAE,CAAC,CAC7D,CACF,CAAC,CAAC,CACF,OACF,CAEA;AACA,GAAIrE,KAAK,GAAKrI,WAAW,CAACM,GAAG,CAAE,CAC7BQ,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEqK,OAAO,CAAC,CAEhC,KAAM,CAAA6C,OAAO,CAAG7C,OAAO,CAAC5J,IAAI,CAC5B,KAAM,CAAA0M,KAAK,CAAGD,OAAO,CAAChM,KAAK,CAC3B,KAAM,CAAAkM,QAAQ,CAAG,CACf9I,SAAS,CAAEsF,UAAU,CAACsD,OAAO,CAACG,QAAQ,CAAC,CACvC9I,QAAQ,CAAEqF,UAAU,CAACsD,OAAO,CAACI,OAAO,CAAC,CACrC9I,KAAK,CAAEoF,UAAU,CAACsD,OAAO,CAAClB,SAAS,CAAC,CACpCvH,OAAO,CAAEmF,UAAU,CAACsD,OAAO,CAACjB,WAAW,CACzC,CAAC,CAEDlM,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEoN,QAAQ,CAAC,CAClCrN,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEmN,KAAK,CAAC,CAE3B;AACAhO,MAAM,CAACoO,WAAW,CAAC,CACjB5B,IAAI,CAAE,iBAAiB,CACvB6B,MAAM,CAAE,aACV,CAAC,CAAE,GAAG,CAAC,CAEP;AACArO,MAAM,CAACoO,WAAW,CAAC,CACjB5B,IAAI,CAAE,KAAK,CACXzK,KAAK,CAAEiM,KAAK,CAAE;AACd1M,IAAI,CAAE,CAAQ;AACZS,KAAK,CAAEiM,KAAK,CACZnB,SAAS,CAAEkB,OAAO,CAAClB,SAAS,CAC5BsB,OAAO,CAAEJ,OAAO,CAACI,OAAO,CACxBD,QAAQ,CAAEH,OAAO,CAACG,QAAQ,CAC1BpB,WAAW,CAAEiB,OAAO,CAACjB,WACvB,CACF,CAAC,CAAE,GAAG,CAAC,CAEP;AACA,KAAM,CAAAC,QAAQ,CAAGrI,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAACyD,QAAQ,CAAC9I,SAAS,CAAE8I,QAAQ,CAAC7I,QAAQ,CAAC,CACtF,KAAM,CAAAkJ,WAAW,CAAG,GAAI,CAAA3Q,KAAK,CAAC6F,OAAO,CAACuJ,QAAQ,CAACrK,CAAC,CAAE,GAAG,CAAE,CAACqK,QAAQ,CAACnK,CAAC,CAAC,CACnE,KAAM,CAAAc,WAAW,CAAGE,IAAI,CAACC,EAAE,CAAGoK,QAAQ,CAAC3I,OAAO,CAAG1B,IAAI,CAACC,EAAE,CAAG,GAAG,CAE9D;AACA,GAAI,CAAA0K,UAAU,CAAGzN,aAAa,CAACoC,GAAG,CAAC8K,KAAK,CAAC,CAEzC;AACA,KAAM,CAAAlM,aAAa,CAAGkM,KAAK,GAAKhN,gBAAgB,CAEhD,GAAI,CAACuN,UAAU,EAAIpP,qBAAqB,CAAE,CACxC;AACA,KAAM,CAAAqP,eAAe,CAAGrP,qBAAqB,CAACqD,KAAK,CAAC,CAAC,CAErD;AACA;AACAgM,eAAe,CAAC9I,QAAQ,CAAC3C,GAAG,CAACuL,WAAW,CAAC5L,CAAC,CAAE,CAAC,CAAC,CAAE4L,WAAW,CAACxL,CAAC,CAAC,CAC9D0L,eAAe,CAACpB,QAAQ,CAACxK,CAAC,CAAGc,WAAW,CAExC;AACA8K,eAAe,CAACC,QAAQ,CAAEC,KAAK,EAAK,CAClC,GAAIA,KAAK,CAACC,MAAM,EAAID,KAAK,CAACE,QAAQ,CAAE,CAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA,KAAM,CAAAC,WAAW,CAAGH,KAAK,CAACE,QAAQ,CAACpM,KAAK,CAAC,CAAC,CAC1CkM,KAAK,CAACE,QAAQ,CAAGC,WAAW,CAE5B;AACA,GAAI/M,aAAa,CAAE,CACjB+M,WAAW,CAAClH,KAAK,CAAC5E,GAAG,CAAC,QAAQ,CAAC,CACjC,CAAC,IAAM,CACL8L,WAAW,CAAClH,KAAK,CAAC5E,GAAG,CAAC,QAAQ,CAAC,CACjC,CACA8L,WAAW,CAACC,QAAQ,CAAG,GAAI,CAAAnR,KAAK,CAACoR,KAAK,CAAC,QAAQ,CAAC,CAChDF,WAAW,CAACG,WAAW,CAAG,IAAI,CAC9BH,WAAW,CAACI,OAAO,CAAG,GAAG,CACzBJ,WAAW,CAACK,WAAW,CAAG,IAAI,CAChC,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,UAAU,CAAGC,gBAAgB,CAAC,GAAGxL,IAAI,CAACyL,KAAK,CAACpB,QAAQ,CAAC5I,KAAK,CAAC,OAAO,CAAE,CACxEc,eAAe,CAAErE,aAAa,CAC5B,CAAEwN,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAI,CAAC,CAAG;AACnC,CAAEH,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,GAAI,CAAC,CAAG;AACrCC,SAAS,CAAE,CAAEJ,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAI,CAAC,CAAE;AAC/ClJ,QAAQ,CAAE,EAAE,CACZL,OAAO,CAAE,CACX,CAAC,CAAC,CACFiJ,UAAU,CAACzJ,QAAQ,CAAC3C,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAE;AAClCoM,UAAU,CAACQ,WAAW,CAAG,IAAI,CAAE;AAC/BR,UAAU,CAACP,QAAQ,CAACK,OAAO,CAAG,GAAG,CAAE;AACnCT,eAAe,CAACnB,GAAG,CAAC8B,UAAU,CAAC,CAE/B5P,KAAK,CAAC8N,GAAG,CAACmB,eAAe,CAAC,CAE1B;AACA1N,aAAa,CAACiC,GAAG,CAACiL,KAAK,CAAE,CACvBf,KAAK,CAAEuB,eAAe,CACtBlB,UAAU,CAAEnB,IAAI,CAACD,GAAG,CAAC,CAAC,CACtBM,IAAI,CAAE,GAAG,CAAE;AACXoD,MAAM,CAAE9N,aAAa,CACrBqN,UAAU,CAAEA,UAAW;AACzB,CAAC,CAAC,CAEFvO,OAAO,CAACC,GAAG,CAAC,aAAamN,KAAK,SAASM,WAAW,CAAC5L,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC,KAAKgL,WAAW,CAAC1L,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,KAAKgL,WAAW,CAACxL,CAAC,CAACQ,OAAO,CAAC,CAAC,CAAC,YAAYxB,aAAa,EAAE,CAAC,CAErJ;AACA,GAAI,CAAA/D,KAAK,CAAC4K,KAAK,CAAC6F,eAAe,CAAC9I,QAAQ,CAAC,CACtCkD,EAAE,CAAC,CAAEhG,CAAC,CAAE,GAAI,CAAC,CAAE,GAAG,CAAC,CACnBiG,MAAM,CAAC9K,KAAK,CAAC+K,MAAM,CAACC,SAAS,CAAC8G,GAAG,CAAC,CAClC1G,KAAK,CAAC,CAAC,CAEV;AACAqF,eAAe,CAACC,QAAQ,CAAEC,KAAK,EAAK,CAClC,GAAIA,KAAK,CAACC,MAAM,EAAID,KAAK,CAACE,QAAQ,EAAIF,KAAK,CAACE,QAAQ,CAACI,WAAW,CAAE,CAChE,GAAI,CAAAjR,KAAK,CAAC4K,KAAK,CAAC,CAAEsG,OAAO,CAAE,GAAI,CAAC,CAAC,CAC9BrG,EAAE,CAAC,CAAEqG,OAAO,CAAE,GAAI,CAAC,CAAE,GAAG,CAAC,CACzBpG,MAAM,CAAC9K,KAAK,CAAC+K,MAAM,CAACC,SAAS,CAAC8G,GAAG,CAAC,CAClC5G,QAAQ,CAAC,UAAW,CACnByF,KAAK,CAACE,QAAQ,CAACK,OAAO,CAAG,IAAI,CAACA,OAAO,CACrCP,KAAK,CAACE,QAAQ,CAACM,WAAW,CAAG,IAAI,CACnC,CAAC,CAAC,CACD/F,KAAK,CAAC,CAAC,CACZ,CACF,CAAC,CAAC,CAEF;AACA,GAAI,CAAApL,KAAK,CAAC4K,KAAK,CAAC,CAAEsG,OAAO,CAAE,GAAI,CAAC,CAAC,CAC9BrG,EAAE,CAAC,CAAEqG,OAAO,CAAE,GAAI,CAAC,CAAE,GAAG,CAAC,CACzBpG,MAAM,CAAC9K,KAAK,CAAC+K,MAAM,CAACC,SAAS,CAAC8G,GAAG,CAAC,CAClC5G,QAAQ,CAAC,UAAW,CACnBkG,UAAU,CAACP,QAAQ,CAACK,OAAO,CAAG,IAAI,CAACA,OAAO,CAC1CE,UAAU,CAACP,QAAQ,CAACM,WAAW,CAAG,IAAI,CACxC,CAAC,CAAC,CACD/F,KAAK,CAAC,CAAC,CAEV;AACA,GAAIrH,aAAa,CAAE,CACjBpD,gBAAgB,CAAG8P,eAAe,CAClCtJ,eAAe,CAAC+I,QAAQ,CAAC,CACzBrN,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEmN,KAAK,CAAC,CACjC,CACF,CAAC,IAAM,IAAIO,UAAU,CAAE,CACrB;AACA,KAAM,CAAAuB,gBAAgB,CAAGzN,cAAc,CAACiM,WAAW,CAAEN,KAAK,CAAC,CAC3D,KAAM,CAAAlK,gBAAgB,CAAGL,cAAc,CAACC,WAAW,CAAEsK,KAAK,CAAC,CAE3D;AACAO,UAAU,CAACtB,KAAK,CAACvH,QAAQ,CAACwD,IAAI,CAAC4G,gBAAgB,CAAC,CAChDvB,UAAU,CAACtB,KAAK,CAACG,QAAQ,CAACxK,CAAC,CAAGkB,gBAAgB,CAC9CyK,UAAU,CAACtB,KAAK,CAACrD,YAAY,CAAC,CAAC,CAC/B2E,UAAU,CAACtB,KAAK,CAACpD,iBAAiB,CAAC,IAAI,CAAC,CACxC0E,UAAU,CAACjB,UAAU,CAAGnB,IAAI,CAACD,GAAG,CAAC,CAAC,CAClCqC,UAAU,CAACqB,MAAM,CAAG9N,aAAa,CAAE;AAEnC;AACA,GAAIyM,UAAU,CAACY,UAAU,CAAE,CACzBZ,UAAU,CAACY,UAAU,CAACP,QAAQ,CAAClB,GAAG,CAACqC,OAAO,CAAC,CAAC,CAC5CxB,UAAU,CAACtB,KAAK,CAACY,MAAM,CAACU,UAAU,CAACY,UAAU,CAAC,CAChD,CAEA;AACA,KAAM,CAAAA,UAAU,CAAGC,gBAAgB,CAAC,GAAGxL,IAAI,CAACyL,KAAK,CAACpB,QAAQ,CAAC5I,KAAK,CAAG,GAAG,CAAC,OAAO,CAAE,CAC9Ec,eAAe,CAAErE,aAAa,CAC5B,CAAEwN,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAI,CAAC,CAAG;AACnC,CAAEH,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,GAAI,CAAC,CAAG;AACrCC,SAAS,CAAE,CAAEJ,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAI,CAAC,CAAE;AAC/ClJ,QAAQ,CAAE,EAAE,CACZL,OAAO,CAAE,CACX,CAAC,CAAC,CACFiJ,UAAU,CAACzJ,QAAQ,CAAC3C,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAAE;AACnCoM,UAAU,CAACQ,WAAW,CAAG,IAAI,CAAE;AAC/BpB,UAAU,CAACtB,KAAK,CAACI,GAAG,CAAC8B,UAAU,CAAC,CAChCZ,UAAU,CAACY,UAAU,CAAGA,UAAU,CAElCvO,OAAO,CAACC,GAAG,CAAC,cAAcmN,KAAK,SAAS8B,gBAAgB,CAACpN,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC,KAAKwM,gBAAgB,CAAClN,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,KAAKwM,gBAAgB,CAAChN,CAAC,CAACQ,OAAO,CAAC,CAAC,CAAC,YAAYxB,aAAa,EAAE,CAAC,CAErK;AACA,GAAIA,aAAa,CAAE,CACjBpD,gBAAgB,CAAG6P,UAAU,CAACtB,KAAK,CACnC/H,eAAe,CAAC+I,QAAQ,CAAC,CAC3B,CACF,CAEA;AACA,KAAM,CAAA/B,GAAG,CAAGC,IAAI,CAACD,GAAG,CAAC,CAAC,CACtB,KAAM,CAAAqB,iBAAiB,CAAG,IAAI,CAAE;AAEhCzM,aAAa,CAACsL,OAAO,CAAC,CAACwB,SAAS,CAAEtB,EAAE,GAAK,CACvC,KAAM,CAAA0D,mBAAmB,CAAG9D,GAAG,CAAG0B,SAAS,CAACN,UAAU,CAEtD;AACA,GAAI0C,mBAAmB,CAAGzC,iBAAiB,CAAG,GAAG,EAAIyC,mBAAmB,EAAIzC,iBAAiB,CAAE,CAC7F,KAAM,CAAA0B,OAAO,CAAG,CAAC,CAAI,CAACe,mBAAmB,CAAGzC,iBAAiB,CAAG,GAAG,GAAKA,iBAAiB,CAAG,GAAG,CAAE,CAEjGK,SAAS,CAACX,KAAK,CAACwB,QAAQ,CAAEC,KAAK,EAAK,CAClC,GAAIA,KAAK,CAACC,MAAM,EAAID,KAAK,CAACE,QAAQ,CAAE,CAClC;AACA,GAAIF,KAAK,CAACE,QAAQ,CAACI,WAAW,GAAKiB,SAAS,CAAE,CAC5CvB,KAAK,CAACE,QAAQ,CAACsB,mBAAmB,CAAGxB,KAAK,CAACE,QAAQ,CAACI,WAAW,EAAI,KAAK,CACxEN,KAAK,CAACE,QAAQ,CAACuB,eAAe,CAAGzB,KAAK,CAACE,QAAQ,CAACK,OAAO,EAAI,GAAG,CAChE,CAEA;AACAP,KAAK,CAACE,QAAQ,CAACI,WAAW,CAAG,IAAI,CACjCN,KAAK,CAACE,QAAQ,CAACK,OAAO,CAAGA,OAAO,CAChCP,KAAK,CAACE,QAAQ,CAACM,WAAW,CAAG,IAAI,CACnC,CACF,CAAC,CAAC,CAEF;AACA,GAAItB,SAAS,CAACuB,UAAU,CAAE,CACxBvB,SAAS,CAACuB,UAAU,CAACP,QAAQ,CAACK,OAAO,CAAGA,OAAO,CAC/CrB,SAAS,CAACuB,UAAU,CAACP,QAAQ,CAACM,WAAW,CAAG,IAAI,CAClD,CACF,CACA;AAAA,IACK,IAAIc,mBAAmB,CAAGzC,iBAAiB,CAAE,CAChD;AACA,GAAIK,SAAS,CAACuB,UAAU,CAAE,CACxBvB,SAAS,CAACuB,UAAU,CAACP,QAAQ,CAAClB,GAAG,CAACqC,OAAO,CAAC,CAAC,CAC3CnC,SAAS,CAACuB,UAAU,CAACP,QAAQ,CAACmB,OAAO,CAAC,CAAC,CACvCnC,SAAS,CAACX,KAAK,CAACY,MAAM,CAACD,SAAS,CAACuB,UAAU,CAAC,CAC9C,CAEAvB,SAAS,CAACX,KAAK,CAACwB,QAAQ,CAAEC,KAAK,EAAK,CAClC,GAAIA,KAAK,CAACC,MAAM,CAAE,CAChB,GAAID,KAAK,CAACE,QAAQ,CAAE,CAClB,GAAInN,KAAK,CAACC,OAAO,CAACgN,KAAK,CAACE,QAAQ,CAAC,CAAE,CACjCF,KAAK,CAACE,QAAQ,CAACxC,OAAO,CAACgE,CAAC,EAAIA,CAAC,CAACL,OAAO,CAAC,CAAC,CAAC,CAC1C,CAAC,IAAM,CACLrB,KAAK,CAACE,QAAQ,CAACmB,OAAO,CAAC,CAAC,CAC1B,CACF,CACA,GAAIrB,KAAK,CAAC2B,QAAQ,CAAE3B,KAAK,CAAC2B,QAAQ,CAACN,OAAO,CAAC,CAAC,CAC9C,CACF,CAAC,CAAC,CAEF;AACAxQ,KAAK,CAACsO,MAAM,CAACD,SAAS,CAACX,KAAK,CAAC,CAC7BnM,aAAa,CAACgN,MAAM,CAACxB,EAAE,CAAC,CACxB;AACA3M,oBAAoB,CAACmO,MAAM,CAACxB,EAAE,CAAC,CAC/BzM,oBAAoB,CAACiO,MAAM,CAACxB,EAAE,CAAC,CAE/B1L,OAAO,CAACC,GAAG,CAAC,mBAAmByL,EAAE,EAAE,CAAC,CACtC,CACF,CAAC,CAAC,CAEF,OACF,CAEA;AACA,GAAInE,KAAK,GAAKrI,WAAW,CAACS,IAAI,CAAE,CAC9BK,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEoK,OAAO,CAAC,CAEjC,GAAI,CACF,KAAM,CAAAC,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC,CAEnC;AACA,KAAM,CAAAK,SAAS,CAAGJ,OAAO,CAACK,GAAG,CAC7B,KAAM,CAAAC,gBAAgB,CAAGN,OAAO,CAACO,EAAE,CAEnC;AACA,KAAM,CAAAC,aAAa,CAAG3K,gBAAgB,CAACmC,GAAG,CAACoI,SAAS,CAAC,CAErD;AACA,GAAII,aAAa,EAAIF,gBAAgB,CAAGE,aAAa,CAAE,CACrD9K,OAAO,CAACC,GAAG,CAAC,cAAc,CAAE,CAC1B8K,KAAK,CAAEL,SAAS,CAChBM,KAAK,CAAEJ,gBAAgB,CACvBK,KAAK,CAAEH,aACT,CAAC,CAAC,CACF,OACF,CAEA;AACA3K,gBAAgB,CAACgC,GAAG,CAACuI,SAAS,CAAEE,gBAAgB,CAAC,CAEjD;AACA,GAAIN,OAAO,CAAC5J,IAAI,EAAI4J,OAAO,CAAC5J,IAAI,CAAC8I,aAAa,EAAI3I,KAAK,CAACC,OAAO,CAACwJ,OAAO,CAAC5J,IAAI,CAAC8I,aAAa,CAAC,CAAE,CAC3Fc,OAAO,CAAC5J,IAAI,CAAC8I,aAAa,CAACgC,OAAO,CAACjC,YAAY,EAAI,CACjD,KAAM,CAAAnD,OAAO,CAAGmD,YAAY,CAACnD,OAAO,CAEpC,GAAI,CAACA,OAAO,CAAE,CACZpG,OAAO,CAACoB,KAAK,CAAC,kBAAkB,CAAEmI,YAAY,CAAC,CAC/C,OACF,CAEAvJ,OAAO,CAACC,GAAG,CAAC,WAAWmG,OAAO,UAAU,CAAC,CAEzC;AACA,GAAImD,YAAY,CAACjD,MAAM,EAAIzF,KAAK,CAACC,OAAO,CAACyI,YAAY,CAACjD,MAAM,CAAC,CAAE,CAC7D;AACA,KAAM,CAAAoJ,UAAU,CAAG,EAAE,CAErBnG,YAAY,CAACjD,MAAM,CAACkF,OAAO,CAACmE,KAAK,EAAI,CACnC;AACA,GAAI,CAACA,KAAK,CAACC,OAAO,CAAE,CAClB5P,OAAO,CAACoB,KAAK,CAAC,gBAAgB,CAAEuO,KAAK,CAAC,CACtC,OACF,CAEA,KAAM,CAAAC,OAAO,CAAGD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC,CACxC;AACA,KAAM,CAAAC,SAAS,CAAGH,KAAK,CAACI,YAAY,CAClCC,oBAAoB,CAACL,KAAK,CAACI,YAAY,CAAC,CACxCE,iBAAiB,CAACL,OAAO,CAAC,CAE5B;AACA,KAAM,CAAAM,UAAU,CAAGP,KAAK,CAACQ,YAAY,EAAI,GAAG,CAAE;AAC9C,KAAM,CAAAC,UAAU,CAAGC,QAAQ,CAACV,KAAK,CAACS,UAAU,CAAC,EAAI,CAAC,CAElDpQ,OAAO,CAACC,GAAG,CAAC,SAASmG,OAAO,WAAWwJ,OAAO,SAASE,SAAS,YAAYI,UAAU,WAAWE,UAAU,GAAG,CAAC,CAE/G;AACA,KAAM,CAAAE,SAAS,CAAG,CAChBV,OAAO,CACPE,SAAS,CACTK,YAAY,CAAED,UAAU,CACxBE,UACF,CAAC,CAED;AACAV,UAAU,CAACa,IAAI,CAACD,SAAS,CAAC,CAE1B;AACA;AACA,GAAI,CAAAE,eAAe,CAAGC,MAAM,CAACrK,OAAO,CAAC,CACrC,GAAI,CAAAsK,iBAAiB,CAAGrQ,gBAAgB,CAACiC,GAAG,CAACkO,eAAe,CAAC,CAE7D,GAAI,CAACE,iBAAiB,CAAE,CACtB;AACAF,eAAe,CAAGH,QAAQ,CAACjK,OAAO,CAAC,CACnCsK,iBAAiB,CAAGrQ,gBAAgB,CAACiC,GAAG,CAACkO,eAAe,CAAC,CAC3D,CAEA,GAAIE,iBAAiB,CAAE,CACrB;AACAC,wBAAwB,CAACD,iBAAiB,CAAEJ,SAAS,CAAC,CAEtD;AACA,GAAIvK,oBAAoB,EAAIA,oBAAoB,CAACK,OAAO,GAAKA,OAAO,CAAE,CACpEF,sBAAsB,CAAC0K,IAAI,GAAK,CAC9B,GAAGA,IAAI,CACPzK,OAAO,CAAE,IAAI,CACbyJ,OAAO,CACPE,SAAS,CACThE,KAAK,CAAEoE,UAAU,CACjBE,UACF,CAAC,CAAC,CAAC,CACL,CACF,CAAC,IAAM,CACLpQ,OAAO,CAAC6Q,IAAI,CAAC,WAAWzK,OAAO,SAASwJ,OAAO,SAAS,CAAC,CAC3D,CACF,CAAC,CAAC,CAEF;AACA,GAAI,CAAAkB,QAAQ,CAAG,IAAI,CACnB;AACA,KAAM,CAAAC,KAAK,CAAGN,MAAM,CAACrK,OAAO,CAAC,CAC7B,GAAI/F,gBAAgB,CAAC+B,GAAG,CAAC2O,KAAK,CAAC,CAAE,CAC/BD,QAAQ,CAAGC,KAAK,CAClB,CAAC,IAAM,CACL;AACA,KAAM,CAAAC,KAAK,CAAGX,QAAQ,CAACjK,OAAO,CAAC,CAC/B,GAAI/F,gBAAgB,CAAC+B,GAAG,CAAC4O,KAAK,CAAC,CAAE,CAC/BF,QAAQ,CAAGE,KAAK,CAClB,CACF,CAEA,GAAIF,QAAQ,GAAK,IAAI,CAAE,CACrB;AACAxQ,kBAAkB,CAAC6B,GAAG,CAAC2O,QAAQ,CAAE,CAC/BG,UAAU,CAAE1F,IAAI,CAACD,GAAG,CAAC,CAAC,CACtBhF,MAAM,CAAEoJ,UACV,CAAC,CAAC,CACF1P,OAAO,CAACC,GAAG,CAAC,aAAa6Q,QAAQ,KAAK,MAAO,CAAAA,QAAQ,aAAa,CAAC,CAEnE;AACA,GAAI1R,MAAM,CAACmH,mBAAmB,GAC1BnH,MAAM,CAACmH,mBAAmB,CAACoB,OAAO,GAAKmJ,QAAQ,EAC/C1R,MAAM,CAACmH,mBAAmB,CAACoB,OAAO,GAAK8I,MAAM,CAACK,QAAQ,CAAC,EACvD1R,MAAM,CAACmH,mBAAmB,CAACoB,OAAO,GAAK0I,QAAQ,CAACS,QAAQ,CAAC,CAAC,CAAE,CAE9D9Q,OAAO,CAACC,GAAG,CAAC,eAAe6Q,QAAQ,aAAa,CAAC,CACjD;AACA1R,MAAM,CAACmH,mBAAmB,CAACoB,OAAO,CAAGmJ,QAAQ,CAE7C;AACA,GAAI1R,MAAM,CAACoH,0BAA0B,EAAI,CAACpH,MAAM,CAACoH,0BAA0B,CAACmB,OAAO,CAAE,CACnF3H,OAAO,CAACC,GAAG,CAAC,SAAS6Q,QAAQ,aAAa,CAAC,CAC3CI,UAAU,CAAC,IAAM,CACf9R,MAAM,CAAC+R,qBAAqB,CAACL,QAAQ,CAAC,CACxC,CAAC,CAAE,GAAG,CAAC,CACT,CACF,CACF,CAAC,IAAM,CACL;AACAxQ,kBAAkB,CAAC6B,GAAG,CAACiE,OAAO,CAAE,CAC9B6K,UAAU,CAAE1F,IAAI,CAACD,GAAG,CAAC,CAAC,CACtBhF,MAAM,CAAEoJ,UACV,CAAC,CAAC,CACF1P,OAAO,CAACC,GAAG,CAAC,sBAAsBmG,OAAO,YAAY,CAAC,CACxD,CACF,CAAC,IAAM,CACLpG,OAAO,CAACoB,KAAK,CAAC,eAAe,CAAEmI,YAAY,CAAC,CAC9C,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACLvJ,OAAO,CAACoB,KAAK,CAAC,oCAAoC,CAAEkJ,OAAO,CAAC,CAC9D,CACF,CAAE,MAAOlJ,KAAK,CAAE,CACdpB,OAAO,CAACoB,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAEiJ,OAAO,CAAC,CAC9C,CACF,CAEA;AACA,GAAI9C,KAAK,GAAKrI,WAAW,CAACQ,GAAG,EAAI4K,OAAO,CAACsB,IAAI,GAAK,KAAK,CAAE,CACvD5L,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEqK,OAAO,CAAC,CAEhC;AACAlL,MAAM,CAACoO,WAAW,CAAC,CACjB5B,IAAI,CAAE,KAAK,CACXlL,IAAI,CAAE4J,OAAO,CAAC5J,IAChB,CAAC,CAAE,GAAG,CAAC,CAEP,KAAM,CAAA0Q,OAAO,CAAG9G,OAAO,CAAC5J,IAAI,CAC5B,KAAM,CAAA2Q,KAAK,CAAGD,OAAO,CAACC,KAAK,CAC3B,KAAM,CAAAC,MAAM,CAAGF,OAAO,CAACG,IAAI,EAAI,EAAE,CAEjCD,MAAM,CAAC9F,OAAO,CAACgG,KAAK,EAAI,CACtB,KAAM,CAAAC,OAAO,CAAGD,KAAK,CAACE,KAAK,CAC3B,KAAM,CAAAC,SAAS,CAAGH,KAAK,CAACG,SAAS,CACjC,KAAM,CAAAC,WAAW,CAAGJ,KAAK,CAACI,WAAW,CACrC,KAAM,CAAAC,SAAS,CAAGL,KAAK,CAACK,SAAS,CACjC,KAAM,CAAAC,OAAO,CAAGN,KAAK,CAACM,OAAO,CAE7B;AACA,KAAM,CAAA3F,QAAQ,CAAGrI,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAC7CC,UAAU,CAACuH,OAAO,CAACW,OAAO,CAAC,CAC3BlI,UAAU,CAACuH,OAAO,CAACY,MAAM,CAC3B,CAAC,CAED;AACA,GAAI,CAAAC,WAAW,CAAG,EAAE,CACpB,GAAI,CAAAC,YAAY,CAAG,EAAE,CAErB,OAAOP,SAAS,EACd,IAAK,KAAK,CAAG;AACXM,WAAW,CAAG,OAAO,CACrBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,OAAO,CACrBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,QAAQ,CACtBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,MAAM,CAAE;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,QACED,WAAW,CAAGL,WAAW,EAAI,MAAM,CACnCM,YAAY,CAAG,SAAS,CAC5B,CAEA;AACAC,iBAAiB,CAAChG,QAAQ,CAAE8F,WAAW,CAAEC,YAAY,CAAC,CAEtDlS,OAAO,CAACC,GAAG,CAAC,UAAU,CAAE,CACtBmS,IAAI,CAAEX,OAAO,CACbY,IAAI,CAAEV,SAAS,CACfW,IAAI,CAAEV,WAAW,CACjBW,IAAI,CAAEV,SAAS,CACfW,IAAI,CAAEV,OAAO,CACbW,EAAE,CAAEtG,QACN,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,OACF,CAEA;AACA,GAAI5E,KAAK,GAAKrI,WAAW,CAACP,KAAK,EAAI2L,OAAO,CAACsB,IAAI,GAAK,OAAO,CAAE,CAC3D5L,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEqK,OAAO,CAAC,CAEjC,KAAM,CAAAoI,SAAS,CAAGpI,OAAO,CAAC5J,IAAI,CAC9B,KAAM,CAAAiS,OAAO,CAAGD,SAAS,CAACC,OAAO,CACjC,KAAM,CAAAC,SAAS,CAAGF,SAAS,CAACE,SAAS,CACrC,KAAM,CAAAC,SAAS,CAAGH,SAAS,CAACG,SAAS,CACrC,KAAM,CAAA/N,QAAQ,CAAG,CACfN,QAAQ,CAAEqF,UAAU,CAAC6I,SAAS,CAACnF,OAAO,CAAC,CACvChJ,SAAS,CAAEsF,UAAU,CAAC6I,SAAS,CAACpF,QAAQ,CAC1C,CAAC,CAED;AACA,KAAM,CAAAnB,QAAQ,CAAGrI,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAAC9E,QAAQ,CAACP,SAAS,CAAEO,QAAQ,CAACN,QAAQ,CAAC,CAEtF;AACA,OAAOoO,SAAS,EACd,IAAK,GAAG,CAAG;AACTT,iBAAiB,CAAChG,QAAQ,CAAE,UAAU,CAAE,SAAS,CAAC,CAClD,MACF,IAAK,KAAK,CAAG;AACXgG,iBAAiB,CAAChG,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,KAAK,CAAG;AACXgG,iBAAiB,CAAChG,QAAQ,CAAE,OAAO,CAAE,SAAS,CAAC,CAC/C,MACF,IAAK,IAAI,CAAG;AACV,KAAM,CAAA2G,UAAU,CAAGJ,SAAS,CAACK,UAAU,CAAG;AAC1CZ,iBAAiB,CAAChG,QAAQ,CAAE,KAAK2G,UAAU,MAAM,CAAE,SAAS,CAAC,CAC7D,MACF,IAAK,IAAI,CAAG;AACVX,iBAAiB,CAAChG,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,IAAI,CAAG;AACVgG,iBAAiB,CAAChG,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,MAAM,CAAG;AACZgG,iBAAiB,CAAChG,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,IAAI,CAAG;AACVgG,iBAAiB,CAAChG,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,IAAI,CAAG;AACVgG,iBAAiB,CAAChG,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,KAAK,CAAG;AACX,KAAM,CAAA6G,YAAY,CAAGN,SAAS,CAACK,UAAU,CAAG;AAC5C,KAAM,CAAAE,QAAQ,CAAGP,SAAS,CAACQ,UAAU,CAAO;AAC5Cf,iBAAiB,CAAChG,QAAQ,CAAE,QAAQgH,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,CAAE,SAAS,CAAC,CAC/F,MACJ,CAEA,OACF,CACA;AACAjT,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE,CACrBsH,KAAK,CACLqE,IAAI,CAAEtB,OAAO,CAACsB,IAAI,CAClBlL,IAAI,CAAE4J,OACR,CAAC,CAAC,CAEJ,CAAE,MAAOlJ,KAAK,CAAE,CACdpB,OAAO,CAACoB,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnCpB,OAAO,CAACoB,KAAK,CAAC,SAAS,CAAEiJ,OAAO,CAAC,CACnC,CACF,CAAC,CAED;AACA,KAAM,CAAA+I,cAAc,CAAGA,CAAA,GAAM,CAC3BpT,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAE7B,KAAM,CAAAoT,KAAK,CAAG,QAAQnU,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO,CACnES,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEoT,KAAK,CAAC,CAEpC;AACA,KAAM,CAAAC,EAAE,CAAG,GAAI,CAAAC,SAAS,CAACF,KAAK,CAAC,CAE/BC,EAAE,CAACE,MAAM,CAAG,IAAM,CAChBxT,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC9B,CAAC,CAEDqT,EAAE,CAACG,SAAS,CAAIjC,KAAK,EAAK,CACxB,GAAI,CACF,KAAM,CAAAnH,OAAO,CAAGE,IAAI,CAACC,KAAK,CAACgH,KAAK,CAAC9Q,IAAI,CAAC,CAEtC;AACA,GAAI2J,OAAO,CAACuB,IAAI,GAAK,SAAS,CAAE,CAC9B5L,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEoK,OAAO,CAAC,CAC/B,OACF,CAEA;AACA,GAAIA,OAAO,CAACuB,IAAI,GAAK,MAAM,CAAE,CAC3B,OACF,CAEA;AACA,GAAIvB,OAAO,CAACuB,IAAI,GAAK,SAAS,EAAIvB,OAAO,CAAC9C,KAAK,EAAI8C,OAAO,CAACC,OAAO,CAAE,CAClE;AACAF,iBAAiB,CAACC,OAAO,CAAC9C,KAAK,CAAEgD,IAAI,CAACmJ,SAAS,CAACrJ,OAAO,CAACC,OAAO,CAAC,CAAC,CACnE,CACF,CAAE,MAAOlJ,KAAK,CAAE,CACdpB,OAAO,CAACoB,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CAC1C,CACF,CAAC,CAEDkS,EAAE,CAACK,OAAO,CAAIvS,KAAK,EAAK,CACtBpB,OAAO,CAACoB,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACtC,CAAC,CAEDkS,EAAE,CAACM,OAAO,CAAG,IAAM,CACjB5T,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC5B;AACAiR,UAAU,CAACkC,cAAc,CAAE,IAAI,CAAC,CAClC,CAAC,CAED;AACAnP,aAAa,CAAC0D,OAAO,CAAG2L,EAAE,CAC5B,CAAC,CAED3W,SAAS,CAAC,IAAM,CACd,GAAI,CAACiH,YAAY,CAAC+D,OAAO,CAAE,OAE3B;AACAkM,aAAa,CAAC,CAAC,CAEf;AACAlV,KAAK,CAAG,GAAI,CAAA5B,KAAK,CAAC+W,KAAK,CAAC,CAAC,CAAE;AAE3B;AACA,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAhX,KAAK,CAACiX,iBAAiB,CACxC,EAAE,CACF5U,MAAM,CAAC6U,UAAU,CAAG7U,MAAM,CAAC8U,WAAW,CACtC,GAAG,CACH,IACF,CAAC,CACD;AACAH,MAAM,CAACjP,QAAQ,CAAC3C,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAAE;AAChC4R,MAAM,CAACrL,MAAM,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACtB5C,SAAS,CAAC6B,OAAO,CAAGoM,MAAM,CAE1B;AACA,KAAM,CAAAI,QAAQ,CAAG,GAAI,CAAApX,KAAK,CAACqX,aAAa,CAAC,CAAEC,SAAS,CAAE,IAAK,CAAC,CAAC,CAC7DF,QAAQ,CAACG,OAAO,CAAClV,MAAM,CAAC6U,UAAU,CAAE7U,MAAM,CAAC8U,WAAW,CAAC,CACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC,CAChCJ,QAAQ,CAACK,aAAa,CAACpV,MAAM,CAACqV,gBAAgB,CAAC,CAC/C7Q,YAAY,CAAC+D,OAAO,CAAC+M,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC,CAErD;AACA;AACA,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAA7X,KAAK,CAAC8X,YAAY,CAAC,QAAQ,CAAE,GAAG,CAAC,CAAE;AAC5DlW,KAAK,CAAC8N,GAAG,CAACmI,YAAY,CAAC,CAEvB;AACA,KAAM,CAAAE,iBAAiB,CAAG,GAAI,CAAA/X,KAAK,CAACgY,gBAAgB,CAAC,QAAQ,CAAE,GAAG,CAAC,CAAE;AACrED,iBAAiB,CAAChQ,QAAQ,CAAC3C,GAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAC1CxD,KAAK,CAAC8N,GAAG,CAACqI,iBAAiB,CAAC,CAE5B;AACA,KAAM,CAAAE,iBAAiB,CAAG,GAAI,CAAAjY,KAAK,CAACgY,gBAAgB,CAAC,QAAQ,CAAE,GAAG,CAAC,CACnEC,iBAAiB,CAAClQ,QAAQ,CAAC3C,GAAG,CAAC,CAAC,EAAE,CAAE,CAAC,CAAE,CAAC,EAAE,CAAC,CAC3CxD,KAAK,CAAC8N,GAAG,CAACuI,iBAAiB,CAAC,CAE5B;AACA,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAlY,KAAK,CAACmY,SAAS,CAAC,QAAQ,CAAE,GAAG,CAAC,CACpDD,SAAS,CAACnQ,QAAQ,CAAC3C,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAChC8S,SAAS,CAACE,KAAK,CAAGnS,IAAI,CAACC,EAAE,CAAG,CAAC,CAC7BgS,SAAS,CAACG,QAAQ,CAAG,GAAG,CACxBH,SAAS,CAACI,KAAK,CAAG,CAAC,CACnBJ,SAAS,CAAC1S,QAAQ,CAAG,GAAG,CACxB5D,KAAK,CAAC8N,GAAG,CAACwI,SAAS,CAAC,CAEpB;AACA3W,QAAQ,CAAG,GAAI,CAAArB,aAAa,CAAC8W,MAAM,CAAEI,QAAQ,CAACQ,UAAU,CAAC,CACzDrW,QAAQ,CAACgX,aAAa,CAAG,IAAI,CAC7BhX,QAAQ,CAACiX,aAAa,CAAG,IAAI,CAC7BjX,QAAQ,CAACkX,kBAAkB,CAAG,KAAK,CACnClX,QAAQ,CAACsK,WAAW,CAAG,EAAE,CACzBtK,QAAQ,CAACuK,WAAW,CAAG,GAAG,CAC1BvK,QAAQ,CAACwK,aAAa,CAAG9F,IAAI,CAACC,EAAE,CAAG,GAAG,CACtC3E,QAAQ,CAACyK,aAAa,CAAG,CAAC,CAC1BzK,QAAQ,CAACkK,MAAM,CAACrG,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5B7D,QAAQ,CAACqK,MAAM,CAAC,CAAC,CAEjB;AACA3I,OAAO,CAACC,GAAG,CAAC,OAAO,CAAE,CACnB8T,MAAM,CAAE,CAAC,CAACA,MAAM,CAChBzV,QAAQ,CAAE,CAAC,CAACA,QAAQ,CACpBwH,SAAS,CAAE,CAAC,CAACA,SAAS,CAAC6B,OACzB,CAAC,CAAC,CAEF;AACA,KAAM,CAAA8N,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,MAAO,IAAI,CAAAC,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACtC,KAAM,CAAAC,aAAa,CAAG,GAAI,CAAA7Y,UAAU,CAAC,CAAC,CACtC6Y,aAAa,CAACC,IAAI,CAChB,GAAGlW,QAAQ,uBAAuB,CACjCmW,IAAI,EAAK,CACR,KAAM,CAAAC,YAAY,CAAGD,IAAI,CAACpX,KAAK,CAE/B;AACA,KAAM,CAAAsX,gBAAgB,CAAG,GAAI,CAAAlZ,KAAK,CAACmZ,KAAK,CAAC,CAAC,CAE1C;AACAF,YAAY,CAACnI,QAAQ,CAAEC,KAAK,EAAK,CAC/B,GAAIA,KAAK,CAACC,MAAM,CAAE,CAChB;AACA,GAAID,KAAK,CAACE,QAAQ,CAAE,CAClB;AACA,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAAlR,KAAK,CAACoZ,oBAAoB,CAAC,CACjDpP,KAAK,CAAE,QAAQ,CAAO;AACtBqP,SAAS,CAAE,GAAG,CAAQ;AACtBC,SAAS,CAAE,GAAG,CAAQ;AACtBC,eAAe,CAAE,GAAK;AACxB,CAAC,CAAC,CAEF;AACA,GAAIxI,KAAK,CAACE,QAAQ,CAAClB,GAAG,CAAE,CACtBmB,WAAW,CAACnB,GAAG,CAAGgB,KAAK,CAACE,QAAQ,CAAClB,GAAG,CACtC,CAEA;AACAgB,KAAK,CAACE,QAAQ,CAAGC,WAAW,CAE5BjO,OAAO,CAACC,GAAG,CAAC,UAAU,CAAE6N,KAAK,CAACpE,IAAI,CAAC,CACrC,CACF,CACF,CAAC,CAAC,CAEF;AACA,MAAMsM,YAAY,CAACO,QAAQ,CAACC,MAAM,CAAG,CAAC,CAAE,CACtC,KAAM,CAAA1I,KAAK,CAAGkI,YAAY,CAACO,QAAQ,CAAC,CAAC,CAAC,CACtCN,gBAAgB,CAACxJ,GAAG,CAACqB,KAAK,CAAC,CAC7B,CAEA;AACAnP,KAAK,CAAC8N,GAAG,CAACwJ,gBAAgB,CAAC,CAE3B;AACAnY,gBAAgB,CAAGmY,gBAAgB,CAEnCjW,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAC9BmE,kBAAkB,CAAC,IAAI,CAAC,CACxBuR,OAAO,CAACM,gBAAgB,CAAC,CAC3B,CAAC,CACAQ,GAAG,EAAK,CACPzW,OAAO,CAACC,GAAG,CAAC,aAAa,CAACwW,GAAG,CAACC,MAAM,CAAGD,GAAG,CAACE,KAAK,CAAG,GAAG,EAAEjU,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CACxE,CAAC,CACDkT,MACF,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAgB,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACF;AACA;AAEA;AACAxD,cAAc,CAAC,CAAC,CAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEF,CAAE,MAAOhS,KAAK,CAAE,CACdpB,OAAO,CAACoB,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CAED;AACA,KAAM,CAAAyV,kBAAkB,CAAG,QAAAA,CAACC,GAAG,CAAqB,IAAnB,CAAAC,UAAU,CAAAC,SAAA,CAAAR,MAAA,IAAAQ,SAAA,MAAA3H,SAAA,CAAA2H,SAAA,IAAG,CAAC,CAC7C,MAAO,IAAI,CAAAtB,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACtC,KAAM,CAAAqB,WAAW,CAAIC,WAAW,EAAK,CACnClX,OAAO,CAACC,GAAG,CAAC,WAAW6W,GAAG,aAAaI,WAAW,EAAE,CAAC,CAErD,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAna,UAAU,CAAC,CAAC,CAC/Bma,MAAM,CAACrB,IAAI,CACTgB,GAAG,CACFf,IAAI,EAAK,CACR/V,OAAO,CAACC,GAAG,CAAC,WAAW6W,GAAG,EAAE,CAAC,CAC7BnB,OAAO,CAACI,IAAI,CAAC,CACf,CAAC,CACAU,GAAG,EAAK,CACPzW,OAAO,CAACC,GAAG,CAAC,SAAS,CAACwW,GAAG,CAACC,MAAM,CAAGD,GAAG,CAACE,KAAK,CAAG,GAAG,EAAEjU,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CACpE,CAAC,CACAtB,KAAK,EAAK,CACTpB,OAAO,CAACoB,KAAK,CAAC,SAAS0V,GAAG,EAAE,CAAE1V,KAAK,CAAC,CACpC,GAAI8V,WAAW,CAAG,CAAC,CAAE,CACnBlX,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3BiR,UAAU,CAAC,IAAM+F,WAAW,CAACC,WAAW,CAAG,CAAC,CAAC,CAAE,IAAI,CAAC,CACtD,CAAC,IAAM,CACLtB,MAAM,CAACxU,KAAK,CAAC,CACf,CACF,CACF,CAAC,CACH,CAAC,CAED6V,WAAW,CAACF,UAAU,CAAC,CACzB,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAI,MAAM,CAAG,GAAI,CAAAna,UAAU,CAAC,CAAC,CAC/Bma,MAAM,CAACrB,IAAI,CACT,GAAGlW,QAAQ,4BAA4B,CACvC,KAAO,CAAAmW,IAAI,EAAK,CACd,GAAI,CACF,KAAM,CAAA1J,KAAK,CAAG0J,IAAI,CAACpX,KAAK,CACxB0N,KAAK,CAAC+K,KAAK,CAACjV,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACxBkK,KAAK,CAACvH,QAAQ,CAAC3C,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAE3B;AACA,GAAIxD,KAAK,CAAE,CACXA,KAAK,CAAC8N,GAAG,CAACJ,KAAK,CAAC,CAEhB;AACA,KAAM,CAAAuK,eAAe,CAAC,CAAC,CACvB,CAAC,IAAM,CACL5W,OAAO,CAACoB,KAAK,CAAC,eAAe,CAAC,CAChC,CACF,CAAE,MAAOA,KAAK,CAAE,CACdpB,OAAO,CAACoB,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CACAqV,GAAG,EAAK,CACPzW,OAAO,CAACC,GAAG,CAAC,SAAS,CAACwW,GAAG,CAACC,MAAM,CAAGD,GAAG,CAACE,KAAK,CAAG,GAAG,EAAEjU,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CACpE,CAAC,CACAtB,KAAK,EAAK,CACTpB,OAAO,CAACoB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BpB,OAAO,CAACoB,KAAK,CAAC,OAAO,CAAE,CACrBiW,IAAI,CAAEjW,KAAK,CAACwK,IAAI,CAChB0L,IAAI,CAAElW,KAAK,CAACiJ,OAAO,CACnBkN,KAAK,CAAE,GAAG3X,QAAQ,4BAA4B,CAC9C4X,KAAK,CAAE,GAAG5X,QAAQ,4BACpB,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAA6X,OAAO,CAAGA,CAAA,GAAM,CACpBvT,iBAAiB,CAACyD,OAAO,CAAG+P,qBAAqB,CAACD,OAAO,CAAC,CAE1D;AACAta,KAAK,CAACwL,MAAM,CAAC,CAAC,CAEd,GAAItK,UAAU,GAAK,QAAQ,EAAIP,gBAAgB,CAAE,CAC/C;AACAQ,QAAQ,CAACmJ,OAAO,CAAG,KAAK,CAExB;AACA,KAAM,CAAAkQ,UAAU,CAAG7Z,gBAAgB,CAACgH,QAAQ,CAAClD,KAAK,CAAC,CAAC,CAEpD;AACA,KAAM,CAAAgW,eAAe,CAAG9Z,gBAAgB,CAAC0O,QAAQ,CAACxK,CAAC,CAEnD;AACA;AACA,KAAM,CAAA6V,gBAAgB,CAAG,EAAED,eAAe,CAAG5U,IAAI,CAACC,EAAE,CAAC,CAAGD,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC,CACnE;AAEA;AACA,KAAM,CAAA6U,YAAY,CAAG,GAAI,CAAA/a,KAAK,CAAC6F,OAAO,CACpC,CAAC,EAAE,CAAGI,IAAI,CAAC+U,GAAG,CAACF,gBAAgB,CAAC,CAChC,GAAG,CACH,CAAC,EAAE,CAAG7U,IAAI,CAACgV,GAAG,CAACH,gBAAgB,CACjC,CAAC,CAED;AACA;AAEA;AACA9D,MAAM,CAACjP,QAAQ,CAACwD,IAAI,CAACqP,UAAU,CAAC,CAAClL,GAAG,CAACqL,YAAY,CAAC,CAElD;AACA/D,MAAM,CAACjM,EAAE,CAAC3F,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAEtB;AACA,KAAM,CAAA8V,YAAY,CAAGN,UAAU,CAAC/V,KAAK,CAAC,CAAC,CACvCmS,MAAM,CAACrL,MAAM,CAACuP,YAAY,CAAC,CAE3B;AACAlE,MAAM,CAACmE,sBAAsB,CAAC,CAAC,CAC/BnE,MAAM,CAAC/K,YAAY,CAAC,CAAC,CACrB+K,MAAM,CAAC9K,iBAAiB,CAAC,IAAI,CAAC,CAE9B;AACA3K,QAAQ,CAACmJ,OAAO,CAAG,KAAK,CAExB;AACAnJ,QAAQ,CAACkK,MAAM,CAACF,IAAI,CAACqP,UAAU,CAAC,CAChCrZ,QAAQ,CAACqK,MAAM,CAAC,CAAC,CAEjB3I,OAAO,CAACC,GAAG,CAAC,OAAO,CAAE,CACnBkY,IAAI,CAAER,UAAU,CAACzN,OAAO,CAAC,CAAC,CAC1BD,IAAI,CAAE8J,MAAM,CAACjP,QAAQ,CAACoF,OAAO,CAAC,CAAC,CAC/BkO,IAAI,CAAEH,YAAY,CAAC/N,OAAO,CAAC,CAAC,CAC5BmO,IAAI,CAAEtE,MAAM,CAACuE,iBAAiB,CAAC,GAAI,CAAAvb,KAAK,CAAC6F,OAAO,CAAC,CAAC,CAAC,CAACsH,OAAO,CAAC,CAC9D,CAAC,CAAC,CACJ,CAAC,IAAM,IAAI7L,UAAU,GAAK,QAAQ,CAAE,CAClC;AACAC,QAAQ,CAACmJ,OAAO,CAAG,IAAI,CAEvB;AACAsM,MAAM,CAACjM,EAAE,CAAC3F,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAEtB;AACA,GAAIa,IAAI,CAACK,GAAG,CAAC0Q,MAAM,CAACjP,QAAQ,CAAC9C,CAAC,CAAC,CAAG,EAAE,CAAE,CACpC+R,MAAM,CAACjP,QAAQ,CAAC3C,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAC9B7D,QAAQ,CAACkK,MAAM,CAACrG,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5B4R,MAAM,CAACrL,MAAM,CAACpK,QAAQ,CAACkK,MAAM,CAAC,CAC9BlK,QAAQ,CAACqK,MAAM,CAAC,CAAC,CACnB,CAEA;AACA;AACAoL,MAAM,CAAC/K,YAAY,CAAC,CAAC,CACrB+K,MAAM,CAAC9K,iBAAiB,CAAC,IAAI,CAAC,CAEhC,CAAC,IAAM,IAAI5K,UAAU,GAAK,cAAc,CAAE,CACxC;AACAC,QAAQ,CAACqK,MAAM,CAAC,CAAC,CACnB,CAEA,GAAIrK,QAAQ,CAAEA,QAAQ,CAACqK,MAAM,CAAC,CAAC,CAC/B,GAAIhK,KAAK,EAAIoV,MAAM,CAAE,CACnBI,QAAQ,CAACoE,MAAM,CAAC5Z,KAAK,CAAEoV,MAAM,CAAC,CAChC,CACF,CAAC,CAED0D,OAAO,CAAC,CAAC,CAET;AACA,KAAM,CAAAe,YAAY,CAAGA,CAAA,GAAM,CACzBzE,MAAM,CAAC0E,MAAM,CAAGrZ,MAAM,CAAC6U,UAAU,CAAG7U,MAAM,CAAC8U,WAAW,CACtDH,MAAM,CAACmE,sBAAsB,CAAC,CAAC,CAC/B/D,QAAQ,CAACG,OAAO,CAAClV,MAAM,CAAC6U,UAAU,CAAE7U,MAAM,CAAC8U,WAAW,CAAC,CACzD,CAAC,CACD9U,MAAM,CAACsZ,gBAAgB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CAE/C;AACApZ,MAAM,CAACuZ,aAAa,CAAG,IAAM,CAC3B,GAAI7S,SAAS,CAAC6B,OAAO,CAAE,CACrB7B,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAC3C,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CACzC2D,SAAS,CAAC6B,OAAO,CAACe,MAAM,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACjC5C,SAAS,CAAC6B,OAAO,CAACqB,YAAY,CAAC,CAAC,CAChClD,SAAS,CAAC6B,OAAO,CAACsB,iBAAiB,CAAC,IAAI,CAAC,CAEzC,GAAI3K,QAAQ,CAAE,CACZA,QAAQ,CAACkK,MAAM,CAACrG,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5B7D,QAAQ,CAACmJ,OAAO,CAAG,IAAI,CACvBnJ,QAAQ,CAACqK,MAAM,CAAC,CAAC,CACnB,CAEAtK,UAAU,CAAG,QAAQ,CACrB2B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CAAC,CAED;AACA,MAAO,IAAM,CACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CAExB;AACA,GAAIiE,iBAAiB,CAACyD,OAAO,CAAE,CAC7BiR,oBAAoB,CAAC1U,iBAAiB,CAACyD,OAAO,CAAC,CAC/CzD,iBAAiB,CAACyD,OAAO,CAAG,IAAI,CAClC,CAEA;AACA,GAAI1J,oBAAoB,CAAE,CACxB4a,aAAa,CAAC5a,oBAAoB,CAAC,CACnCA,oBAAoB,CAAG,IAAI,CAC7B,CAEA;AACA,GAAIgG,aAAa,CAAC0D,OAAO,CAAE,CACzB1D,aAAa,CAAC0D,OAAO,CAACmR,KAAK,CAAC,CAAC,CAC7B7U,aAAa,CAAC0D,OAAO,CAAG,IAAI,CAC9B,CAEA;AACAvI,MAAM,CAAC2Z,mBAAmB,CAAC,QAAQ,CAAEP,YAAY,CAAC,CAElD;AACA,GAAIrE,QAAQ,EAAIvQ,YAAY,CAAC+D,OAAO,CAAE,CACpC/D,YAAY,CAAC+D,OAAO,CAACqR,WAAW,CAAC7E,QAAQ,CAACQ,UAAU,CAAC,CACrDR,QAAQ,CAAChF,OAAO,CAAC,CAAC,CACpB,CAEA;AACA,GAAIjP,aAAa,CAAE,CACjBA,aAAa,CAACsL,OAAO,CAAC,CAACwB,SAAS,CAAEtB,EAAE,GAAK,CACvC,GAAIsB,SAAS,CAACX,KAAK,EAAI1N,KAAK,CAAE,CAC5BA,KAAK,CAACsO,MAAM,CAACD,SAAS,CAACX,KAAK,CAAC,CAC/B,CACF,CAAC,CAAC,CACFnM,aAAa,CAAC+Y,KAAK,CAAC,CAAC,CACvB,CAEA;AACA5Y,gBAAgB,CAACmL,OAAO,CAAE0N,QAAQ,EAAK,CACrC,GAAIva,KAAK,EAAIua,QAAQ,CAAC7M,KAAK,CAAE,CAC3B1N,KAAK,CAACsO,MAAM,CAACiM,QAAQ,CAAC7M,KAAK,CAAC,CAC9B,CACF,CAAC,CAAC,CACFhM,gBAAgB,CAAC4Y,KAAK,CAAC,CAAC,CACxB3Y,kBAAkB,CAAC2Y,KAAK,CAAC,CAAC,CAE1B;AAEA;AACAta,KAAK,CAAG,IAAI,CACZL,QAAQ,CAAG,IAAI,CACfC,qBAAqB,CAAG,IAAI,CAC5BC,qBAAqB,CAAG,IAAI,CAC5BC,oBAAoB,CAAG,IAAI,CAC3BC,0BAA0B,CAAG,IAAI,CACjCZ,gBAAgB,CAAG,IAAI,CAEvBkC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACvB,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACAtD,SAAS,CAAC,IAAM,CACd;AACA4D,qBAAqB,CAAC,CAAC,CAEvB;AACA,KAAM,CAAA4Y,uBAAuB,CAAGA,CAAA,GAAM,CACpCnZ,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjCM,qBAAqB,CAAC,CAAC,CACzB,CAAC,CAED;AACAnB,MAAM,CAACsZ,gBAAgB,CAAC,oBAAoB,CAAES,uBAAuB,CAAC,CAEtE;AACA,KAAM,CAAAC,UAAU,CAAGC,WAAW,CAAC,IAAM,CACnC9Y,qBAAqB,CAAC,CAAC,CACzB,CAAC,CAAE,KAAK,CAAC,CAET;AACA,MAAO,IAAM,CACXnB,MAAM,CAAC2Z,mBAAmB,CAAC,oBAAoB,CAAEI,uBAAuB,CAAC,CACzEN,aAAa,CAACO,UAAU,CAAC,CAC3B,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACAzc,SAAS,CAAC,IAAM,CACd;AACA,GAAIgC,KAAK,EAAImF,SAAS,CAAC6D,OAAO,CAAE,CAC9B3H,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB;AACA,KAAM,CAAAqZ,KAAK,CAAGpI,UAAU,CAAC,IAAM,CAC7B,GAAIvS,KAAK,EAAImF,SAAS,CAAC6D,OAAO,CAAE,CAAG;AACjC4R,mBAAmB,CAACzV,SAAS,CAAC6D,OAAO,CAAC,CACxC,CACF,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAM6R,YAAY,CAACF,KAAK,CAAC,CAClC,CAAC,IAAM,CACLtZ,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CACrC,CACF,CAAC,CAAE,CAACtB,KAAK,CAAC,CAAC,CAEX;AACAhC,SAAS,CAAC,IAAM,CACd,GAAIiH,YAAY,CAAC+D,OAAO,CAAE,CACxB;AACA,KAAM,CAAA8R,WAAW,CAAIjI,KAAK,EAAK,CAC7B,GAAI7S,KAAK,EAAImH,SAAS,CAAC6B,OAAO,CAAE,CAC9B3H,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEuR,KAAK,CAACkI,OAAO,CAAElI,KAAK,CAACmI,OAAO,CAAC,CACpDC,gBAAgB,CAACpI,KAAK,CAAE5N,YAAY,CAAC+D,OAAO,CAAEhJ,KAAK,CAAEmH,SAAS,CAAC6B,OAAO,CAAEzB,sBAAsB,CAAC,CACjG,CAAC,IAAM,CACLlG,OAAO,CAAC6Q,IAAI,CAAC,4BAA4B,CAAC,CAC5C,CACF,CAAC,CAED;AACAjN,YAAY,CAAC+D,OAAO,CAAC+Q,gBAAgB,CAAC,OAAO,CAAEe,WAAW,CAAC,CAE3D;AACAzZ,OAAO,CAACC,GAAG,CAAC,eAAe,CAAE,CAAC,CAAC2D,YAAY,CAAC+D,OAAO,CAAC,CAEpD;AACA,MAAO,IAAM,CACX,GAAI/D,YAAY,CAAC+D,OAAO,CAAE,CACxB/D,YAAY,CAAC+D,OAAO,CAACoR,mBAAmB,CAAC,OAAO,CAAEU,WAAW,CAAC,CAC9DzZ,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC,CAC3B,CACF,CAAC,CACH,CACF,CAAC,CAAE,CAACtB,KAAK,CAAEmH,SAAS,CAAC6B,OAAO,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAkS,SAAS,CAAG/c,WAAW,CAAC,IAAM,CAClCkD,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAC7B;AACF,CAAC,CAAE,CAAC2D,YAAY,CAAEuD,aAAa,CAAE9G,gBAAgB,CAAC,CAAC,CAEnD;AACA,KAAM,CAAAyZ,wBAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAArK,QAAQ,CAAG,GAAI,CAAA1S,KAAK,CAACgd,WAAW,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAChD,KAAM,CAAA/L,QAAQ,CAAG,GAAI,CAAAjR,KAAK,CAACid,iBAAiB,CAAC,CAAEjT,KAAK,CAAE,QAAS,CAAC,CAAC,CACjE,KAAM,CAAA2J,iBAAiB,CAAG,GAAI,CAAA3T,KAAK,CAACkd,IAAI,CAACxK,QAAQ,CAAEzB,QAAQ,CAAC,CAE5D;AACA,KAAM,CAAAkM,YAAY,CAAG,GAAI,CAAAnd,KAAK,CAACod,gBAAgB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,EAAE,CAAC,CAC5D,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAArd,KAAK,CAACid,iBAAiB,CAAC,CAAEjT,KAAK,CAAE,QAAS,CAAC,CAAC,CACrE,KAAM,CAAAsT,SAAS,CAAG,GAAI,CAAAtd,KAAK,CAACkd,IAAI,CAACC,YAAY,CAAEE,YAAY,CAAC,CAC5DC,SAAS,CAACvV,QAAQ,CAAC3C,GAAG,CAAC,CAAC,CAAE,CAAC,GAAG,CAAE,CAAC,CAAC,CAClCuO,iBAAiB,CAACjE,GAAG,CAAC4N,SAAS,CAAC,CAEhC,MAAO,CAAA3J,iBAAiB,CAC1B,CAAC,CAED;AACA,KAAM,CAAA4J,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAI,CAAC3b,KAAK,CAAE,OAEZ;AACA0B,gBAAgB,CAACmL,OAAO,CAAC,CAAC0N,QAAQ,CAAE9S,OAAO,GAAK,CAC9C,GAAI8S,QAAQ,CAAC7M,KAAK,CAAE,CAClB;AACA,KAAM,CAAAkO,cAAc,CAAG,GAAI,CAAAxd,KAAK,CAACyd,cAAc,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACxD,KAAM,CAAAC,cAAc,CAAG,GAAI,CAAA1d,KAAK,CAACid,iBAAiB,CAAC,CACjDjT,KAAK,CAAE,QAAQ,CAAC;AAChBqH,WAAW,CAAE,KAAK,CAClBC,OAAO,CAAE,GAAG,CAAG;AACfqM,UAAU,CAAE,KACd,CAAC,CAAC,CAEF,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAA5d,KAAK,CAACkd,IAAI,CAACM,cAAc,CAAEE,cAAc,CAAC,CACjEE,UAAU,CAAC7V,QAAQ,CAAC3C,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAG;AAEnC;AACAwY,UAAU,CAACC,QAAQ,CAAG,CACpBhP,IAAI,CAAE,cAAc,CACpBxF,OAAO,CAAEA,OAAO,CAChBsD,IAAI,CAAEwP,QAAQ,CAAC3P,YAAY,CAACG,IAAI,CAChCmR,aAAa,CAAE,IACjB,CAAC,CAED;AACA3B,QAAQ,CAAC7M,KAAK,CAACI,GAAG,CAACkO,UAAU,CAAC,CAE9B3a,OAAO,CAACC,GAAG,CAAC,OAAOiZ,QAAQ,CAAC3P,YAAY,CAACG,IAAI,KAAKtD,OAAO,aAAa,CAAC,CACzE,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACAzJ,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA2c,KAAK,CAAGpI,UAAU,CAAC,IAAM,CAC7B,GAAI7Q,gBAAgB,CAACya,IAAI,CAAG,CAAC,CAAE,CAC7B9a,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1B;AACF,CACF,CAAC,CAAE,IAAI,CAAC,CAAG;AAEX,MAAO,IAAMuZ,YAAY,CAACF,KAAK,CAAC,CAClC,CAAC,CAAE,EAAE,CAAC,CAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA3c,SAAS,CAAC,IAAM,CACd,MAAO,IAAM,CACX;AACA,GAAI6J,0BAA0B,CAACmB,OAAO,CAAE,CACtCkR,aAAa,CAACrS,0BAA0B,CAACmB,OAAO,CAAC,CACjDnB,0BAA0B,CAACmB,OAAO,CAAG,IAAI,CACzC3H,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC9B,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAAE;AAER;AACAtD,SAAS,CAAC,IAAM,CACd,GAAI,CAACsJ,mBAAmB,CAACE,OAAO,EAAIK,0BAA0B,CAACmB,OAAO,CAAE,CACtEkR,aAAa,CAACrS,0BAA0B,CAACmB,OAAO,CAAC,CACjDnB,0BAA0B,CAACmB,OAAO,CAAG,IAAI,CACzCpB,mBAAmB,CAACoB,OAAO,CAAG,IAAI,CAClC3H,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACnC,CACF,CAAC,CAAE,CAACgG,mBAAmB,CAACE,OAAO,CAAC,CAAC,CAEjC;AACAxJ,SAAS,CAAC,IAAM,CACd;AACA,GAAIY,iBAAiB,EAAIA,iBAAiB,CAACiM,aAAa,EAAIjM,iBAAiB,CAACiM,aAAa,CAACgN,MAAM,CAAG,CAAC,CAAE,CACtG;AACA,GAAI,CAACzQ,oBAAoB,CAAE,CACzB,KAAM,CAAAgV,iBAAiB,CAAGxd,iBAAiB,CAACiM,aAAa,CAAC,CAAC,CAAC,CAC5DxJ,OAAO,CAACC,GAAG,CAAC,YAAY,CAAE8a,iBAAiB,CAACrR,IAAI,CAAC,CAEjD;AACA,KAAM,CAAA4P,KAAK,CAAGpI,UAAU,CAAC,IAAM,CAC7B7H,wBAAwB,CAAC0R,iBAAiB,CAACrR,IAAI,CAAC,CAClD,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAM8P,YAAY,CAACF,KAAK,CAAC,CAClC,CACF,CACF,CAAC,CAAE,CAAC/b,iBAAiB,CAAEwI,oBAAoB,CAAC,CAAC,CAE7C,mBACEpI,KAAA,CAAAE,SAAA,EAAA0Y,QAAA,eACE9Y,IAAA,SAAMud,KAAK,CAAEnU,UAAW,CAAA0P,QAAA,CAAC,gCAAK,CAAM,CAAC,cACrC9Y,IAAA,CAACJ,MAAM,EACL2d,KAAK,CAAEtU,uBAAwB,CAC/BuU,WAAW,CAAC,4CAAS,CACrBC,QAAQ,CAAE7R,wBAAyB,CACnC8R,OAAO,CAAE5d,iBAAiB,CAACiM,aAAa,CAACsD,GAAG,CAACvD,YAAY,GAAK,CAC5DD,KAAK,CAAEC,YAAY,CAACG,IAAI,CACxB0R,KAAK,CAAE7R,YAAY,CAACG,IACtB,CAAC,CAAC,CAAE,CACJoR,IAAI,CAAC,OAAO,CACZO,QAAQ,CAAE,IAAK,CACfC,aAAa,CAAE,CACbpW,MAAM,CAAE,IAAI,CACZqW,SAAS,CAAE,OACb,CAAE,CACFjS,KAAK,CAAEvD,oBAAoB,CAAGA,oBAAoB,CAAC2D,IAAI,CAAG2F,SAAU,CACrE,CAAC,cACF5R,IAAA,QAAK+d,GAAG,CAAE5X,YAAa,CAACoX,KAAK,CAAE,CAAEpU,KAAK,CAAE,MAAM,CAAE2F,MAAM,CAAE,MAAO,CAAE,CAAE,CAAC,CAGnEtG,mBAAmB,CAACE,OAAO,eAC1BxI,KAAA,QACEqd,KAAK,CAAE,CACLlW,QAAQ,CAAE,UAAU,CACpBE,IAAI,CAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAAChD,CAAC,IAAI,CAC3C6E,GAAG,CAAE,GAAGV,mBAAmB,CAACnB,QAAQ,CAAC9C,CAAC,IAAI,CAC1CiD,SAAS,CAAE,wBAAwB,CACnCC,MAAM,CAAE,IAAI,CACZK,eAAe,CAAE,qBAAqB,CACtCwB,KAAK,CAAE,OAAO,CACdtB,YAAY,CAAE,KAAK,CACnBG,SAAS,CAAE,8BAA8B,CACzCN,OAAO,CAAE,GAAG,CACZmW,QAAQ,CAAE,OAAO,CAAE;AACnB9V,QAAQ,CAAE,MAAO;AACnB,CAAE,CAAA4Q,QAAA,EAEDtQ,mBAAmB,CAACI,OAAO,cAC5B5I,IAAA,WACEud,KAAK,CAAE,CACLlW,QAAQ,CAAE,UAAU,CACpB6B,GAAG,CAAE,KAAK,CACV+U,KAAK,CAAE,KAAK,CACZC,UAAU,CAAE,MAAM,CAClBnW,MAAM,CAAE,MAAM,CACduB,KAAK,CAAE,OAAO,CACdpB,QAAQ,CAAE,MAAM,CAChBD,MAAM,CAAE,SAAS,CACjBJ,OAAO,CAAE,SACX,CAAE,CACFsW,OAAO,CAAEA,CAAA,GAAMC,kBAAkB,CAAC3V,sBAAsB,CAAE,CAAAqQ,QAAA,CAC3D,MAED,CAAQ,CAAC,EACN,CACN,cAED5Y,KAAA,QAAKqd,KAAK,CAAEnW,oBAAqB,CAAA0R,QAAA,eAC/B9Y,IAAA,WACEud,KAAK,CAAE,CACL,GAAG3V,WAAW,CACdE,eAAe,CAAEZ,QAAQ,GAAK,QAAQ,CAAG,SAAS,CAAG,0BAA0B,CAC/EoC,KAAK,CAAEpC,QAAQ,GAAK,QAAQ,CAAG,OAAO,CAAG,OAC3C,CAAE,CACFiX,OAAO,CAAEpU,kBAAmB,CAAA+O,QAAA,CAC7B,0BAED,CAAQ,CAAC,cACT9Y,IAAA,WACEud,KAAK,CAAE,CACL,GAAG3V,WAAW,CACdE,eAAe,CAAEZ,QAAQ,GAAK,QAAQ,CAAG,SAAS,CAAG,0BAA0B,CAC/EoC,KAAK,CAAEpC,QAAQ,GAAK,QAAQ,CAAG,OAAO,CAAG,OAC3C,CAAE,CACFiX,OAAO,CAAElU,kBAAmB,CAAA6O,QAAA,CAC7B,0BAED,CAAQ,CAAC,EACN,CAAC,EACN,CAAC,CAEP,CAAC,CAED;AACA,QAAS,CAAA/H,gBAAgBA,CAACsN,IAAI,CAAmB,IAAjB,CAAAC,UAAU,CAAA/E,SAAA,CAAAR,MAAA,IAAAQ,SAAA,MAAA3H,SAAA,CAAA2H,SAAA,IAAG,CAAC,CAAC,CAC7C,KAAM,CAAAgF,MAAM,CAAG,CACbC,QAAQ,CAAEF,UAAU,CAACE,QAAQ,EAAI,OAAO,CACxCtW,QAAQ,CAAEoW,UAAU,CAACpW,QAAQ,EAAI,EAAE,CACnCqB,UAAU,CAAE+U,UAAU,CAAC/U,UAAU,EAAI,MAAM,CAC3CkV,eAAe,CAAEH,UAAU,CAACG,eAAe,EAAI,CAAC,CAChDC,WAAW,CAAEJ,UAAU,CAACI,WAAW,EAAI,CAAEzN,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,GAAI,CAAC,CACnEtJ,eAAe,CAAEwW,UAAU,CAACxW,eAAe,EAAI,CAAEmJ,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAI,CAAC,CACjFC,SAAS,CAAEiN,UAAU,CAACjN,SAAS,EAAI,CAAEJ,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,GAAI,CAAC,CAC/DvJ,OAAO,CAAEyW,UAAU,CAACzW,OAAO,EAAI,CACjC,CAAC,CAED;AACA,KAAM,CAAA8W,MAAM,CAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC,CAC/C,KAAM,CAAAC,OAAO,CAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC,CAEvC;AACAD,OAAO,CAACE,IAAI,CAAG,GAAGT,MAAM,CAAChV,UAAU,IAAIgV,MAAM,CAACrW,QAAQ,MAAMqW,MAAM,CAACC,QAAQ,EAAE,CAE7E;AACA,KAAM,CAAAS,SAAS,CAAGH,OAAO,CAACI,WAAW,CAACb,IAAI,CAAC,CAAClV,KAAK,CAEjD;AACA,KAAM,CAAAA,KAAK,CAAG8V,SAAS,CAAG,CAAC,CAAGV,MAAM,CAAC1W,OAAO,CAAG,CAAC,CAAG0W,MAAM,CAACE,eAAe,CACzE,KAAM,CAAA3P,MAAM,CAAGyP,MAAM,CAACrW,QAAQ,CAAG,CAAC,CAAGqW,MAAM,CAAC1W,OAAO,CAAG,CAAC,CAAG0W,MAAM,CAACE,eAAe,CAEhFE,MAAM,CAACxV,KAAK,CAAGA,KAAK,CACpBwV,MAAM,CAAC7P,MAAM,CAAGA,MAAM,CAEtB;AACAgQ,OAAO,CAACE,IAAI,CAAG,GAAGT,MAAM,CAAChV,UAAU,IAAIgV,MAAM,CAACrW,QAAQ,MAAMqW,MAAM,CAACC,QAAQ,EAAE,CAC7EM,OAAO,CAACK,YAAY,CAAG,QAAQ,CAE/B;AACA,KAAM,CAAAC,MAAM,CAAG,CAAC,CAChBN,OAAO,CAACO,SAAS,CAAC,CAAC,CACnBP,OAAO,CAACQ,MAAM,CAACf,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEb,MAAM,CAACE,eAAe,CAAC,CACvEK,OAAO,CAACS,MAAM,CAACpW,KAAK,CAAGoV,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEb,MAAM,CAACE,eAAe,CAAC,CAC/EK,OAAO,CAACU,KAAK,CAACrW,KAAK,CAAGoV,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAEtV,KAAK,CAAGoV,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEA,MAAM,CAAC,CAC9IN,OAAO,CAACS,MAAM,CAACpW,KAAK,CAAGoV,MAAM,CAACE,eAAe,CAAE3P,MAAM,CAAGyP,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAC,CACxFN,OAAO,CAACU,KAAK,CAACrW,KAAK,CAAGoV,MAAM,CAACE,eAAe,CAAE3P,MAAM,CAAGyP,MAAM,CAACE,eAAe,CAAEtV,KAAK,CAAGoV,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEtQ,MAAM,CAAGyP,MAAM,CAACE,eAAe,CAAEW,MAAM,CAAC,CAChKN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEtQ,MAAM,CAAGyP,MAAM,CAACE,eAAe,CAAC,CAChFK,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,CAAE3P,MAAM,CAAGyP,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAE3P,MAAM,CAAGyP,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEA,MAAM,CAAC,CAChJN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAC,CACvEN,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEb,MAAM,CAACE,eAAe,CAAEW,MAAM,CAAC,CAC9HN,OAAO,CAACW,SAAS,CAAC,CAAC,CAEnB;AACAX,OAAO,CAACY,WAAW,CAAG,QAAQnB,MAAM,CAACG,WAAW,CAACzN,CAAC,KAAKsN,MAAM,CAACG,WAAW,CAACxN,CAAC,KAAKqN,MAAM,CAACG,WAAW,CAACvN,CAAC,KAAKoN,MAAM,CAACG,WAAW,CAACtN,CAAC,GAAG,CAChI0N,OAAO,CAACa,SAAS,CAAGpB,MAAM,CAACE,eAAe,CAC1CK,OAAO,CAACc,MAAM,CAAC,CAAC,CAEhB;AACAd,OAAO,CAACe,SAAS,CAAG,QAAQtB,MAAM,CAACzW,eAAe,CAACmJ,CAAC,KAAKsN,MAAM,CAACzW,eAAe,CAACoJ,CAAC,KAAKqN,MAAM,CAACzW,eAAe,CAACqJ,CAAC,KAAKoN,MAAM,CAACzW,eAAe,CAACsJ,CAAC,GAAG,CAC9I0N,OAAO,CAACgB,IAAI,CAAC,CAAC,CAEd;AACAhB,OAAO,CAACe,SAAS,CAAG,QAAQtB,MAAM,CAAClN,SAAS,CAACJ,CAAC,KAAKsN,MAAM,CAAClN,SAAS,CAACH,CAAC,KAAKqN,MAAM,CAAClN,SAAS,CAACF,CAAC,KAAKoN,MAAM,CAAClN,SAAS,CAACD,CAAC,GAAG,CACtH0N,OAAO,CAACiB,SAAS,CAAG,QAAQ,CAE5B;AACAjB,OAAO,CAACkB,QAAQ,CAAC3B,IAAI,CAAElV,KAAK,CAAG,CAAC,CAAE2F,MAAM,CAAG,CAAC,CAAC,CAE7C;AACA,KAAM,CAAAmR,OAAO,CAAG,GAAI,CAAA3gB,KAAK,CAAC4gB,aAAa,CAACvB,MAAM,CAAC,CAC/CsB,OAAO,CAACE,SAAS,CAAG7gB,KAAK,CAAC8gB,YAAY,CACtCH,OAAO,CAACpP,WAAW,CAAG,IAAI,CAE1B;AACA,KAAM,CAAAwP,cAAc,CAAG,GAAI,CAAA/gB,KAAK,CAACghB,cAAc,CAAC,CAC9CjR,GAAG,CAAE4Q,OAAO,CACZtP,WAAW,CAAE,IACf,CAAC,CAAC,CAEF;AACA,KAAM,CAAA4P,MAAM,CAAG,GAAI,CAAAjhB,KAAK,CAACkhB,MAAM,CAACH,cAAc,CAAC,CAC/CE,MAAM,CAAC5G,KAAK,CAACjV,GAAG,CAAC,EAAE,CAAE,CAAC,CAAE,CAAC,CAAC,CAC1B6b,MAAM,CAAChQ,QAAQ,CAACkQ,SAAS,CAAG,KAAK,CAAE;AAEnC;AACAF,MAAM,CAACpD,QAAQ,CAAG,CAChBkB,IAAI,CAAEA,IAAI,CACVE,MAAM,CAAEA,MACV,CAAC,CAED,MAAO,CAAAgC,MAAM,CACf,CAIA;AACA5e,MAAM,CAAC+e,eAAe,CAAG,IAAM,CAC7B,GAAI,CACF;AACA,KAAM,CAAApK,MAAM,CAAGsI,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc,CAC5E,GAAIvK,MAAM,CAAE,CACV;AACA,KAAM,CAAAwK,MAAM,CAAGxK,MAAM,CAACjP,QAAQ,CAAClD,KAAK,CAAC,CAAC,CAEtC;AACAmS,MAAM,CAACjP,QAAQ,CAAC3C,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAC9B4R,MAAM,CAACjM,EAAE,CAAC3F,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACtB4R,MAAM,CAACrL,MAAM,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAEtB;AACAqL,MAAM,CAAC/K,YAAY,CAAC,CAAC,CACrB+K,MAAM,CAAC9K,iBAAiB,CAAC,IAAI,CAAC,CAE9B;AACA,KAAM,CAAA3K,QAAQ,CAAG+d,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB,CAChF,GAAIlgB,QAAQ,CAAE,CACZA,QAAQ,CAACkK,MAAM,CAACrG,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5B7D,QAAQ,CAACqK,MAAM,CAAC,CAAC,CACnB,CAEA3I,OAAO,CAACC,GAAG,CAAC,YAAY,CAAE,CACxBwe,GAAG,CAAEF,MAAM,CAACrU,OAAO,CAAC,CAAC,CACrBwU,GAAG,CAAE3K,MAAM,CAACjP,QAAQ,CAACoF,OAAO,CAAC,CAC/B,CAAC,CAAC,CAEF,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CAAE,MAAOyU,CAAC,CAAE,CACV3e,OAAO,CAACoB,KAAK,CAAC,YAAY,CAAEud,CAAC,CAAC,CAC9B,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACA,KAAM,CAAA9K,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF7T,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3B,KAAM,CAAAkX,MAAM,CAAG,GAAI,CAAAna,UAAU,CAAC,CAAC,CAE/B;AACA,GAAI,CACF,KAAM,CAAC4hB,WAAW,CAAEC,WAAW,CAAEC,UAAU,CAAEC,gBAAgB,CAAC,CAAG,KAAM,CAAArJ,OAAO,CAACsJ,GAAG,CAAC,CACnF7H,MAAM,CAAC8H,SAAS,CAAC,GAAGrf,QAAQ,uBAAuB,CAAC,CACpDuX,MAAM,CAAC8H,SAAS,CAAC,GAAGrf,QAAQ,uBAAuB,CAAC,CAClDuX,MAAM,CAAC8H,SAAS,CAAC,GAAGrf,QAAQ,sBAAsB,CAAC,CACnDuX,MAAM,CAAC8H,SAAS,CAAC,GAAGrf,QAAQ,4BAA4B,CAAC,CAC5D,CAAC,CAEF;AACArB,qBAAqB,CAAGqgB,WAAW,CAACjgB,KAAK,CACzCJ,qBAAqB,CAACsP,QAAQ,CAAEC,KAAK,EAAK,CACxC,GAAIA,KAAK,CAACC,MAAM,CAAE,CACd,KAAM,CAAAE,WAAW,CAAG,GAAI,CAAAlR,KAAK,CAACoZ,oBAAoB,CAAC,CACnDpP,KAAK,CAAE,QAAQ,CAAG;AAClBqP,SAAS,CAAE,GAAG,CACdC,SAAS,CAAE,GAAG,CACdC,eAAe,CAAE,GACnB,CAAC,CAAC,CAEA;AACA,GAAIxI,KAAK,CAACE,QAAQ,CAAClB,GAAG,CAAE,CACtBmB,WAAW,CAACnB,GAAG,CAAGgB,KAAK,CAACE,QAAQ,CAAClB,GAAG,CACtC,CACAgB,KAAK,CAACoR,OAAO,CAAGjR,WAAW,CAC/B,CACF,CAAC,CAAC,CAEF;AACAzP,qBAAqB,CAAGqgB,WAAW,CAAClgB,KAAK,CACzC;AACAH,qBAAqB,CAAC4Y,KAAK,CAACjV,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACxC;AACA3D,qBAAqB,CAACqP,QAAQ,CAAEC,KAAK,EAAK,CACxC,GAAIA,KAAK,CAACC,MAAM,EAAID,KAAK,CAACE,QAAQ,CAAE,CAClC;AACAF,KAAK,CAACE,QAAQ,CAACoI,SAAS,CAAG,GAAG,CAC9BtI,KAAK,CAACE,QAAQ,CAACqI,SAAS,CAAG,GAAG,CAC9BvI,KAAK,CAACE,QAAQ,CAACsI,eAAe,CAAG,GAAG,CACtC,CACF,CAAC,CAAC,CAEF;AACA7X,oBAAoB,CAAGqgB,UAAU,CAACngB,KAAK,CACvC;AACAF,oBAAoB,CAAC2Y,KAAK,CAACjV,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACvC;AACA1D,oBAAoB,CAACoP,QAAQ,CAAEC,KAAK,EAAK,CACvC,GAAIA,KAAK,CAACC,MAAM,EAAID,KAAK,CAACE,QAAQ,CAAE,CAClC;AACAF,KAAK,CAACE,QAAQ,CAACoI,SAAS,CAAG,GAAG,CAC9BtI,KAAK,CAACE,QAAQ,CAACqI,SAAS,CAAG,GAAG,CAC9BvI,KAAK,CAACE,QAAQ,CAACsI,eAAe,CAAG,GAAG,CACtC,CACF,CAAC,CAAC,CAEA;AACA5X,0BAA0B,CAAGqgB,gBAAgB,CAACpgB,KAAK,CACnD;AACAD,0BAA0B,CAAC0Y,KAAK,CAACjV,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC7C;AACAzD,0BAA0B,CAACmP,QAAQ,CAAEC,KAAK,EAAK,CAC7C,GAAIA,KAAK,CAACC,MAAM,EAAID,KAAK,CAACE,QAAQ,CAAE,CAClC;AACAF,KAAK,CAACE,QAAQ,CAACoI,SAAS,CAAG,GAAG,CAC9BtI,KAAK,CAACE,QAAQ,CAACqI,SAAS,CAAG,GAAG,CAC9BvI,KAAK,CAACE,QAAQ,CAACsI,eAAe,CAAG,GAAG,CACxC,CACF,CAAC,CAAC,CAEFtW,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB,CAAE,MAAOmB,KAAK,CAAE,CACdpB,OAAO,CAACoB,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CAExC;AACA,GAAI,CACF,GAAI,CAAC7C,qBAAqB,CAAE,CAC1B,KAAM,CAAAqgB,WAAW,CAAG,KAAM,CAAAzH,MAAM,CAAC8H,SAAS,CAAC,GAAGrf,QAAQ,uBAAuB,CAAC,CAC9ErB,qBAAqB,CAAGqgB,WAAW,CAACjgB,KAAK,CAC3C,CAEA;AACA,GAAI,CAACD,0BAA0B,CAAE,CAC/BsB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAC7B,KAAM,CAAA8e,gBAAgB,CAAG,KAAM,CAAA5H,MAAM,CAAC8H,SAAS,CAAC,GAAGrf,QAAQ,4BAA4B,CAAC,CACxFlB,0BAA0B,CAAGqgB,gBAAgB,CAACpgB,KAAK,CACnDD,0BAA0B,CAAC0Y,KAAK,CAACjV,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC7CnC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CAC1B,CACF,CAAE,MAAOkf,GAAG,CAAE,CACZnf,OAAO,CAACoB,KAAK,CAAC,YAAY,CAAE+d,GAAG,CAAC,CAClC,CACF,CACF,CAAE,MAAO/d,KAAK,CAAE,CACdpB,OAAO,CAACoB,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CAED;AACA,KAAM,CAAA+R,mBAAmB,CAAIvH,IAAI,EAAK,CACpC,KAAM,CAAAwT,KAAK,CAAG,CACZ,GAAG,CAAE,OAAO,CACZ,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,OACP,CAAC,CACD,MAAO,CAAAA,KAAK,CAACxT,IAAI,CAAC,EAAI,MAAM,CAC9B,CAAC,CAED;AACA,KAAM,CAAAuG,iBAAiB,CAAGA,CAACrN,QAAQ,CAAEgX,IAAI,CAAE/U,KAAK,GAAK,CACnD;AACA,GAAI,CAACpI,KAAK,CAAE,CACVqB,OAAO,CAAC6Q,IAAI,CAAC,oBAAoB,CAAC,CAClC,OACF,CAEA,GAAI,CACJ;AACA,KAAM,CAAAmN,MAAM,CAAGxP,gBAAgB,CAACsN,IAAI,CAAC,CACrCkC,MAAM,CAAClZ,QAAQ,CAAC3C,GAAG,CAAC2C,QAAQ,CAAChD,CAAC,CAAE,EAAE,CAAE,CAACgD,QAAQ,CAAC9C,CAAC,CAAC,CAAG;AAEnD;AACAkP,UAAU,CAAC,IAAM,CACb;AACA,GAAIvS,KAAK,EAAIqf,MAAM,CAACqB,MAAM,CAAE,CAC9B1gB,KAAK,CAACsO,MAAM,CAAC+Q,MAAM,CAAC,CAClB,CACJ,CAAC,CAAE,GAAG,CAAC,CAEP;AACArf,KAAK,CAAC8N,GAAG,CAACuR,MAAM,CAAC,CAEjBhe,OAAO,CAACC,GAAG,CAAC,WAAW,CAAE,CACvBwS,EAAE,CAAE3N,QAAQ,CACZwa,EAAE,CAAExD,IAAI,CACRyD,EAAE,CAAExY,KACN,CAAC,CAAC,CACF,CAAE,MAAO3F,KAAK,CAAE,CACdpB,OAAO,CAACoB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CACpC,CACF,CAAC,CAED;AACA,KAAM,CAAAmY,mBAAmB,CAAIiG,iBAAiB,EAAK,CACjD,GAAI,CAAC7gB,KAAK,CAAE,CACVqB,OAAO,CAACoB,KAAK,CAAC,gBAAgB,CAAC,CAC/B,OACF,CAEA,GAAI,CAACoe,iBAAiB,CAAE,CACtBxf,OAAO,CAACoB,KAAK,CAAC,mBAAmB,CAAC,CAClC,OACF,CAEA,GAAI,CAAC1C,0BAA0B,CAAE,CAC/BsB,OAAO,CAACoB,KAAK,CAAC,UAAU,CAAC,CACzB;AACAqe,2BAA2B,CAACD,iBAAiB,CAAC,CAC9C,OACF,CAEA;AACAnf,gBAAgB,CAACmL,OAAO,CAAE0N,QAAQ,EAAK,CACrC,GAAIva,KAAK,EAAIua,QAAQ,CAAC7M,KAAK,CAAE,CAC3B1N,KAAK,CAACsO,MAAM,CAACiM,QAAQ,CAAC7M,KAAK,CAAC,CAC9B,CACF,CAAC,CAAC,CACFhM,gBAAgB,CAAC4Y,KAAK,CAAC,CAAC,CAExB;AACA1b,iBAAiB,CAACiM,aAAa,CAACgC,OAAO,CAACjC,YAAY,EAAI,CACtD;AACA,GAAIA,YAAY,CAACmW,eAAe,GAAK,KAAK,CAAE,CAC1C1f,OAAO,CAACC,GAAG,CAAC,UAAUsJ,YAAY,CAACG,IAAI,kBAAkB,CAAC,CAC1D,OACF,CAEA;AACA,GAAIH,YAAY,CAAC/E,QAAQ,EAAI+E,YAAY,CAAChF,SAAS,EAAIgF,YAAY,CAACnD,OAAO,CAAE,CAC3E;AACA,KAAM,CAAA+F,QAAQ,CAAGqT,iBAAiB,CAAC5V,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,CAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC,CAEDxE,OAAO,CAACC,GAAG,CAAC,SAASsJ,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,iBAAiB+F,QAAQ,CAACrK,CAAC,KAAKqK,QAAQ,CAACnK,CAAC,GAAG,CAAC,CAE7G,GAAI,CACF;AACA,KAAM,CAAA0O,iBAAiB,CAAGhS,0BAA0B,CAACkD,KAAK,CAAC,CAAC,CAE5D;AACA8O,iBAAiB,CAAChH,IAAI,CAAG,OAAOH,YAAY,CAACG,IAAI,EAAE,CAEnD;AACAgH,iBAAiB,CAAC5L,QAAQ,CAAC3C,GAAG,CAACgK,QAAQ,CAACrK,CAAC,CAAE,EAAE,CAAE,CAACqK,QAAQ,CAACnK,CAAC,CAAC,CAE3D;AACA0O,iBAAiB,CAAC0G,KAAK,CAACjV,GAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAEvC;AACAuO,iBAAiB,CAAC3B,WAAW,CAAG,GAAG,CAEnC;AACA2B,iBAAiB,CAAC7C,QAAQ,CAACC,KAAK,EAAI,CAClC;AACA,GAAIA,KAAK,CAACC,MAAM,CAAE,CAChB;AACAD,KAAK,CAACE,QAAQ,CAACI,WAAW,CAAG,KAAK,CAClCN,KAAK,CAACE,QAAQ,CAACK,OAAO,CAAG,GAAG,CAC5BP,KAAK,CAACE,QAAQ,CAAC2R,IAAI,CAAG5iB,KAAK,CAAC6iB,UAAU,CACtC9R,KAAK,CAACE,QAAQ,CAAC0M,UAAU,CAAG,IAAI,CAChC5M,KAAK,CAACE,QAAQ,CAACkQ,SAAS,CAAG,IAAI,CAC/BpQ,KAAK,CAACE,QAAQ,CAACM,WAAW,CAAG,IAAI,CAEjC;AACAR,KAAK,CAACiB,WAAW,CAAG,GAAG,CACzB,CACF,CAAC,CAAC,CAEF;AACA2B,iBAAiB,CAACkK,QAAQ,CAAG,CAC3BhP,IAAI,CAAE,cAAc,CACpBxF,OAAO,CAAEmD,YAAY,CAACnD,OAAO,CAC7BsD,IAAI,CAAEH,YAAY,CAACG,IACrB,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA/K,KAAK,CAAC8N,GAAG,CAACiE,iBAAiB,CAAC,CAE5B;AACArQ,gBAAgB,CAAC8B,GAAG,CAACoH,YAAY,CAACnD,OAAO,CAAE,CACzCiG,KAAK,CAAEqE,iBAAiB,CACxBnH,YAAY,CAAEA,YAAY,CAC1BzE,QAAQ,CAAEqH,QACZ,CAAC,CAAC,CAEFnM,OAAO,CAACC,GAAG,CAAC,SAASsJ,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkB+F,QAAQ,CAACrK,CAAC,KAAK,CAACqK,QAAQ,CAACnK,CAAC,GAAG,CAAC,CACjH,CAAE,MAAOZ,KAAK,CAAE,CACdpB,OAAO,CAACoB,KAAK,CAAC,QAAQmI,YAAY,CAACG,IAAI,YAAY,CAAEtI,KAAK,CAAC,CAC3D;AACA0Y,wBAAwB,CAACvQ,YAAY,CAAE4C,QAAQ,CAAEqT,iBAAiB,CAAC,CACrE,CACF,CACF,CAAC,CAAC,CAEF;AACAxf,OAAO,CAACC,GAAG,CAAC,OAAOI,gBAAgB,CAACya,IAAI,SAAS,CAAC,CAClDza,gBAAgB,CAACmL,OAAO,CAAC,CAAC0N,QAAQ,CAAE9S,OAAO,GAAK,CAC9CpG,OAAO,CAACC,GAAG,CAAC,QAAQmG,OAAO,KAAK8S,QAAQ,CAAC3P,YAAY,CAACG,IAAI,EAAE,CAAC,CAC/D,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA+V,2BAA2B,CAAID,iBAAiB,EAAK,CACzDjiB,iBAAiB,CAACiM,aAAa,CAACgC,OAAO,CAACjC,YAAY,EAAI,CACtD;AACA,GAAIA,YAAY,CAACmW,eAAe,GAAK,KAAK,CAAE,CAC1C,OACF,CAEA,GAAInW,YAAY,CAAC/E,QAAQ,EAAI+E,YAAY,CAAChF,SAAS,EAAIgF,YAAY,CAACnD,OAAO,CAAE,CAC3E;AACA,KAAM,CAAA+F,QAAQ,CAAGqT,iBAAiB,CAAC5V,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,CAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC,CAEDsV,wBAAwB,CAACvQ,YAAY,CAAE4C,QAAQ,CAAEqT,iBAAiB,CAAC,CACrE,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA1F,wBAAwB,CAAGA,CAACvQ,YAAY,CAAE4C,QAAQ,CAAEqT,iBAAiB,GAAK,CAC9E;AACA,KAAM,CAAA/P,QAAQ,CAAG,GAAI,CAAA1S,KAAK,CAACgd,WAAW,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAClD,KAAM,CAAA/L,QAAQ,CAAG,GAAI,CAAAjR,KAAK,CAACid,iBAAiB,CAAC,CAC3CjT,KAAK,CAAE,QAAQ,CACfqH,WAAW,CAAE,KAAK,CAClBC,OAAO,CAAE,GACX,CAAC,CAAC,CACF,KAAM,CAAAqC,iBAAiB,CAAG,GAAI,CAAA3T,KAAK,CAACkd,IAAI,CAACxK,QAAQ,CAAEzB,QAAQ,CAAC,CAE5D;AACA0C,iBAAiB,CAAChH,IAAI,CAAG,SAASH,YAAY,CAACG,IAAI,EAAE,CAErD;AACAgH,iBAAiB,CAAC5L,QAAQ,CAAC3C,GAAG,CAACgK,QAAQ,CAACrK,CAAC,CAAE,EAAE,CAAE,CAACqK,QAAQ,CAACnK,CAAC,CAAC,CAE3D;AACA0O,iBAAiB,CAAC3B,WAAW,CAAG,GAAG,CAEnC;AACA2B,iBAAiB,CAACkK,QAAQ,CAAG,CAC3BhP,IAAI,CAAE,cAAc,CACpBxF,OAAO,CAAEmD,YAAY,CAACnD,OAAO,CAC7BsD,IAAI,CAAEH,YAAY,CAACG,IACrB,CAAC,CAED;AACA,KAAM,CAAAmW,gBAAgB,CAAG,GAAI,CAAA9iB,KAAK,CAACyd,cAAc,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CAC5D,KAAM,CAAAsF,gBAAgB,CAAG,GAAI,CAAA/iB,KAAK,CAACid,iBAAiB,CAAC,CACnDjT,KAAK,CAAE,QAAQ,CACfqH,WAAW,CAAE,IAAI,CACjBC,OAAO,CAAE,GAAG,CAAG;AACfqM,UAAU,CAAE,KACd,CAAC,CAAC,CAEF,KAAM,CAAAqF,QAAQ,CAAG,GAAI,CAAAhjB,KAAK,CAACkd,IAAI,CAAC4F,gBAAgB,CAAEC,gBAAgB,CAAC,CACnEC,QAAQ,CAACrW,IAAI,CAAG,YAAYH,YAAY,CAACG,IAAI,EAAE,CAC/CqW,QAAQ,CAACnF,QAAQ,CAAG,CAClBhP,IAAI,CAAE,cAAc,CACpBxF,OAAO,CAAEmD,YAAY,CAACnD,OAAO,CAC7BsD,IAAI,CAAEH,YAAY,CAACG,IAAI,CACvBsW,UAAU,CAAE,IACd,CAAC,CAEDtP,iBAAiB,CAACjE,GAAG,CAACsT,QAAQ,CAAC,CAE/B;AACAphB,KAAK,CAAC8N,GAAG,CAACiE,iBAAiB,CAAC,CAE5B;AACArQ,gBAAgB,CAAC8B,GAAG,CAACoH,YAAY,CAACnD,OAAO,CAAE,CACzCiG,KAAK,CAAEqE,iBAAiB,CACxBnH,YAAY,CAAEA,YAAY,CAC1BzE,QAAQ,CAAEqH,QACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAAA8T,aAAa,CAAG,GAAI,CAAAljB,KAAK,CAACyd,cAAc,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CACzD,KAAM,CAAA0F,aAAa,CAAG,GAAI,CAAAnjB,KAAK,CAACid,iBAAiB,CAAC,CAAEjT,KAAK,CAAE,QAAS,CAAC,CAAC,CACtE,KAAM,CAAAoZ,SAAS,CAAG,GAAI,CAAApjB,KAAK,CAACkd,IAAI,CAACgG,aAAa,CAAEC,aAAa,CAAC,CAC9DC,SAAS,CAACrb,QAAQ,CAAC3C,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAEhC;AACAge,SAAS,CAACvF,QAAQ,CAAG,CACnBhP,IAAI,CAAE,cAAc,CACpBxF,OAAO,CAAEmD,YAAY,CAACnD,OAAO,CAC7BsD,IAAI,CAAEH,YAAY,CAACG,IACrB,CAAC,CAEDgH,iBAAiB,CAACjE,GAAG,CAAC0T,SAAS,CAAC,CAEhCngB,OAAO,CAACC,GAAG,CAAC,SAASsJ,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkB+F,QAAQ,CAACrK,CAAC,KAAK,CAACqK,QAAQ,CAACnK,CAAC,GAAG,CAAC,CACjH,CAAC,CAED;AACA,KAAM,CAAAiO,iBAAiB,CAAIL,OAAO,EAAK,CACrC,OAAOA,OAAO,EACZ,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,QAAS,MAAO,KAAKA,OAAO,EAAE,CAChC,CACF,CAAC,CAED;AACA,KAAM,CAAAI,oBAAoB,CAAIoQ,OAAO,EAAK,CACxC,OAAOA,OAAO,EACZ,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,QAAS,MAAO,KAAKA,OAAO,EAAE,CAChC,CACF,CAAC,CAED;AACA,KAAM,CAAAxG,gBAAgB,CAAGA,CAACpI,KAAK,CAAE6O,SAAS,CAAEC,aAAa,CAAEC,cAAc,CAAEC,eAAe,GAAK,CAC7F,GAAI,CAACH,SAAS,EAAI,CAACC,aAAa,EAAI,CAACC,cAAc,CAAE,OAErDvgB,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEuR,KAAK,CAACkI,OAAO,CAAElI,KAAK,CAACmI,OAAO,CAAC,CAEvD;AACA,KAAM,CAAA8G,IAAI,CAAGJ,SAAS,CAACK,qBAAqB,CAAC,CAAC,CAC9C,KAAM,CAAAC,MAAM,CAAI,CAACnP,KAAK,CAACkI,OAAO,CAAG+G,IAAI,CAACzb,IAAI,EAAIqb,SAAS,CAACO,WAAW,CAAI,CAAC,CAAG,CAAC,CAC5E,KAAM,CAAAC,MAAM,CAAG,EAAE,CAACrP,KAAK,CAACmI,OAAO,CAAG8G,IAAI,CAAC9Z,GAAG,EAAI0Z,SAAS,CAACS,YAAY,CAAC,CAAG,CAAC,CAAG,CAAC,CAE7E;AACA,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAhkB,KAAK,CAACikB,SAAS,CAAC,CAAC,CACvC;AACAD,SAAS,CAAC/E,MAAM,CAACiF,MAAM,CAACC,SAAS,CAAG,CAAC,CACrCH,SAAS,CAAC/E,MAAM,CAACmF,IAAI,CAACD,SAAS,CAAG,CAAC,CAEnC,KAAM,CAAAE,WAAW,CAAG,GAAI,CAAArkB,KAAK,CAACskB,OAAO,CAACV,MAAM,CAAEE,MAAM,CAAC,CACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,CAAEb,cAAc,CAAC,CAEpDvgB,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE0gB,MAAM,CAAEE,MAAM,CAAC,CAEtC;AACA,KAAM,CAAAU,mBAAmB,CAAG,EAAE,CAE9BlhB,gBAAgB,CAACmL,OAAO,CAAC,CAAC0N,QAAQ,CAAE9S,OAAO,GAAK,CAC9C,GAAI8S,QAAQ,CAAC7M,KAAK,CAAE,CAClB;AACAkV,mBAAmB,CAAChR,IAAI,CAAC2I,QAAQ,CAAC7M,KAAK,CAAC,CACxC;AACA6M,QAAQ,CAAC7M,KAAK,CAAClG,OAAO,CAAG,IAAI,CAC7B+S,QAAQ,CAAC7M,KAAK,CAAC0C,WAAW,CAAG,IAAI,CAAE;AACnC;AACAmK,QAAQ,CAAC7M,KAAK,CAACwB,QAAQ,CAACC,KAAK,EAAI,CAC/BA,KAAK,CAAC3H,OAAO,CAAG,IAAI,CACpB2H,KAAK,CAACiB,WAAW,CAAG,IAAI,CAC1B,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF/O,OAAO,CAACC,GAAG,CAAC,QAAQshB,mBAAmB,CAAC/K,MAAM,gBAAgB,CAAC,CAE/D;AACA,KAAM,CAAAgL,sBAAsB,CAAGT,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,CAAE,IAAI,CAAC,CAEpF,GAAIC,sBAAsB,CAAChL,MAAM,CAAG,CAAC,CAAE,CACrCxW,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEuhB,sBAAsB,CAAChL,MAAM,CAAC,CACxDgL,sBAAsB,CAAChW,OAAO,CAAC,CAACkW,SAAS,CAAEC,KAAK,GAAK,CACnD3hB,OAAO,CAACC,GAAG,CAAC,QAAQ0hB,KAAK,GAAG,CACjBD,SAAS,CAACE,MAAM,CAAClY,IAAI,EAAI,KAAK,CAC9B,KAAK,CAAEgY,SAAS,CAACnf,QAAQ,CACzB,WAAW,CAAEmf,SAAS,CAACE,MAAM,CAAChH,QAAQ,CAAC,CACpD,CAAC,CAAC,CAEF;AACA,KAAM,CAAAiH,GAAG,CAAGC,yBAAyB,CAACN,sBAAsB,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC,CAEvE,GAAIC,GAAG,EAAIA,GAAG,CAACjH,QAAQ,EAAIiH,GAAG,CAACjH,QAAQ,CAAChP,IAAI,GAAK,cAAc,CAAE,CAC/D;AACA,KAAM,CAAAxF,OAAO,CAAGyb,GAAG,CAACjH,QAAQ,CAACxU,OAAO,CACpCpG,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAEmG,OAAO,CAAE,KAAK,CAAE,MAAO,CAAAA,OAAO,CAAC,CAEhE;AACA,GAAI,CAAA2b,SAAS,CAAG3b,OAAO,CACvB,GAAI,MAAO,CAAAA,OAAO,GAAK,QAAQ,EAAI,CAAC/F,gBAAgB,CAAC+B,GAAG,CAACgE,OAAO,CAAC,EAAI/F,gBAAgB,CAAC+B,GAAG,CAACiO,QAAQ,CAACjK,OAAO,CAAC,CAAC,CAAE,CAC5G2b,SAAS,CAAG1R,QAAQ,CAACjK,OAAO,CAAC,CAC7BpG,OAAO,CAACC,GAAG,CAAC,cAAc8hB,SAAS,EAAE,CAAC,CACxC,CAAC,IAAM,IAAI,MAAO,CAAA3b,OAAO,GAAK,QAAQ,EAAI,CAAC/F,gBAAgB,CAAC+B,GAAG,CAACgE,OAAO,CAAC,EAAI/F,gBAAgB,CAAC+B,GAAG,CAACqO,MAAM,CAACrK,OAAO,CAAC,CAAC,CAAE,CACjH2b,SAAS,CAAGtR,MAAM,CAACrK,OAAO,CAAC,CAC3BpG,OAAO,CAACC,GAAG,CAAC,eAAe8hB,SAAS,EAAE,CAAC,CACzC,CAEA;AACA3iB,MAAM,CAAC+R,qBAAqB,CAAC4Q,SAAS,CAAC,CACvC,OACF,CACF,CAEA;AACA,KAAM,CAAAC,UAAU,CAAGjB,SAAS,CAACU,gBAAgB,CAACnB,aAAa,CAAC/J,QAAQ,CAAE,IAAI,CAAC,CAE3EvW,OAAO,CAACC,GAAG,CAAC,aAAa,CAAE+hB,UAAU,CAACxL,MAAM,CAAC,CAE7C,GAAIwL,UAAU,CAACxL,MAAM,CAAG,CAAC,CAAE,CACzB;AACAwL,UAAU,CAACxW,OAAO,CAAC,CAACkW,SAAS,CAAEC,KAAK,GAAK,CACvC,KAAM,CAAAE,GAAG,CAAGH,SAAS,CAACE,MAAM,CAC5B5hB,OAAO,CAACC,GAAG,CAAC,UAAU0hB,KAAK,GAAG,CAAEE,GAAG,CAACnY,IAAI,EAAI,KAAK,CACrC,WAAW,CAAEmY,GAAG,CAACjH,QAAQ,CACzB,KAAK,CAAE8G,SAAS,CAACnf,QAAQ,CAAC,CACxC,CAAC,CAAC,CAEF;AACA,IAAK,GAAI,CAAAkH,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGuY,UAAU,CAACxL,MAAM,CAAE/M,CAAC,EAAE,CAAE,CAC1C,KAAM,CAAAoY,GAAG,CAAGC,yBAAyB,CAACE,UAAU,CAACvY,CAAC,CAAC,CAACmY,MAAM,CAAC,CAC3D,GAAIC,GAAG,EAAIA,GAAG,CAACjH,QAAQ,EAAIiH,GAAG,CAACjH,QAAQ,CAAChP,IAAI,GAAK,cAAc,CAAE,CAC/D,KAAM,CAAAxF,OAAO,CAAGyb,GAAG,CAACjH,QAAQ,CAACxU,OAAO,CACpCpG,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAEmG,OAAO,CAAC,CAEvC;AACAhH,MAAM,CAAC+R,qBAAqB,CAAC/K,OAAO,CAAC,CACrC,OACF,CACF,CACF,CAEA;AACApG,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAE9B;AACA,GAAI,CAAAgiB,YAAY,CAAG,IAAI,CACvB,GAAI,CAAArZ,WAAW,CAAG,GAAG,CAAE;AAEvBvI,gBAAgB,CAACmL,OAAO,CAAC,CAAC0N,QAAQ,CAAE9S,OAAO,GAAK,CAC9C,GAAI8S,QAAQ,CAAC7M,KAAK,CAAE,CAClB,KAAM,CAAA6V,QAAQ,CAAG,GAAI,CAAAnlB,KAAK,CAAC6F,OAAO,CAAC,CAAC,CACpC;AACAsW,QAAQ,CAAC7M,KAAK,CAAC8V,gBAAgB,CAACD,QAAQ,CAAC,CAEzC;AACA,KAAM,CAAAE,SAAS,CAAGF,QAAQ,CAACtgB,KAAK,CAAC,CAAC,CAClCwgB,SAAS,CAACC,OAAO,CAAC9B,cAAc,CAAC,CAEjC;AACA,KAAM,CAAA+B,EAAE,CAAGF,SAAS,CAACtgB,CAAC,CAAG6e,MAAM,CAC/B,KAAM,CAAA4B,EAAE,CAAGH,SAAS,CAACpgB,CAAC,CAAG6e,MAAM,CAC/B,KAAM,CAAAte,QAAQ,CAAGS,IAAI,CAACwf,IAAI,CAACF,EAAE,CAAGA,EAAE,CAAGC,EAAE,CAAGA,EAAE,CAAC,CAE7CviB,OAAO,CAACC,GAAG,CAAC,MAAMmG,OAAO,OAAO,CAAE7D,QAAQ,CAAC,CAE3C,GAAIA,QAAQ,CAAGqG,WAAW,CAAE,CAC1BA,WAAW,CAAGrG,QAAQ,CACtB0f,YAAY,CAAG,CAAE7b,OAAO,CAAE7D,QAAS,CAAC,CACtC,CACF,CACF,CAAC,CAAC,CAEF,GAAI0f,YAAY,CAAE,CAChBjiB,OAAO,CAACC,GAAG,CAAC,oBAAoBgiB,YAAY,CAAC7b,OAAO,SAAS6b,YAAY,CAAC1f,QAAQ,EAAE,CAAC,CAErF;AACAnD,MAAM,CAAC+R,qBAAqB,CAAC8Q,YAAY,CAAC7b,OAAO,CAAC,CAClD,OACF,CAEApG,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAE1B;AACA,GAAIugB,eAAe,CAAE,CACnBA,eAAe,CAAC5P,IAAI,EAAI,CACtB,GAAIA,IAAI,CAACzK,OAAO,CAAE,CAChB,MAAO,CAAE,GAAGyK,IAAI,CAAEzK,OAAO,CAAE,KAAM,CAAC,CACpC,CACA,MAAO,CAAAyK,IAAI,CACb,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAiL,kBAAkB,CAAI2E,eAAe,EAAK,CAC9C;AACA,GAAIphB,MAAM,CAACoH,0BAA0B,EAAIpH,MAAM,CAACoH,0BAA0B,CAACmB,OAAO,CAAE,CAClFkR,aAAa,CAACzZ,MAAM,CAACoH,0BAA0B,CAACmB,OAAO,CAAC,CACxDvI,MAAM,CAACoH,0BAA0B,CAACmB,OAAO,CAAG,IAAI,CAChD3H,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC9B,CAEA;AACA,GAAIb,MAAM,CAACmH,mBAAmB,CAAE,CAC9BnH,MAAM,CAACmH,mBAAmB,CAACoB,OAAO,CAAG,IAAI,CAC3C,CAEA;AACA6Y,eAAe,CAAC5P,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEzK,OAAO,CAAE,KAAM,CAAC,CAAC,CAAC,CACtDnG,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC,CACtB,CAAC,CAED;AACAb,MAAM,CAACqjB,qBAAqB,CAAIrc,OAAO,EAAK,CAC1C,GAAI,KAAAsc,qBAAA,CAAAC,sBAAA,CACF;AACA,KAAM,CAAAxS,YAAY,CAAG9P,gBAAgB,CAACiC,GAAG,CAAC8D,OAAO,EAAI,GAAG,CAAC,CACzD,GAAI,CAAC+J,YAAY,CAAE,CACjBnQ,OAAO,CAACoB,KAAK,CAAC,cAAc,CAAEgF,OAAO,CAAC,CAEtC;AACApG,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxBI,gBAAgB,CAACmL,OAAO,CAAC,CAACoX,KAAK,CAAElX,EAAE,GAAK,CACtC1L,OAAO,CAACC,GAAG,CAAC,KAAKyL,EAAE,KAAKkX,KAAK,CAACrZ,YAAY,CAACG,IAAI,EAAE,CAAC,CACpD,CAAC,CAAC,CAEF,MAAO,MAAK,CACd,CAEA;AACA,KAAM,CAAAmZ,UAAU,CAAG1S,YAAY,CAAC9D,KAAK,CAErC;AACA,KAAM,CAAAyW,SAAS,CAAGxiB,kBAAkB,CAACgC,GAAG,CAAC8D,OAAO,CAAC,CACjD,KAAM,CAAAmD,YAAY,CAAG4G,YAAY,CAAC5G,YAAY,CAE9C;AACA,GAAI,CAAAlD,OAAO,CAEX,GAAIyc,SAAS,EAAIA,SAAS,CAACxc,MAAM,CAAE,CACjCD,OAAO,cACL1I,KAAA,QAAKqd,KAAK,CAAE,CAAE1V,OAAO,CAAE,KAAK,CAAEsB,KAAK,CAAE,OAAO,CAAE2U,SAAS,CAAE,OAAO,CAAEwH,SAAS,CAAE,MAAO,CAAE,CAAAxM,QAAA,eACpF5Y,KAAA,QAAKqd,KAAK,CAAE,CACVhU,UAAU,CAAE,MAAM,CAClBgc,YAAY,CAAE,KAAK,CACnBrd,QAAQ,CAAE,MAAM,CAChBsd,YAAY,CAAE,gBAAgB,CAC9BC,aAAa,CAAE,KACjB,CAAE,CAAA3M,QAAA,EACChN,YAAY,CAACG,IAAI,CAAC,QAAM,CAACtD,OAAO,CAAC,GACpC,EAAK,CAAC,cACN3I,IAAA,QAAA8Y,QAAA,CACGuM,SAAS,CAACxc,MAAM,CAACwG,GAAG,CAAC,CAAC6C,KAAK,CAAEgS,KAAK,GAAK,CACtC,GAAI,CAAAwB,UAAU,CACd,OAAQxT,KAAK,CAACQ,YAAY,EACxB,IAAK,GAAG,CAAEgT,UAAU,CAAG,SAAS,CAAE,MAClC,IAAK,GAAG,CAAEA,UAAU,CAAG,SAAS,CAAE,MAClC,IAAK,GAAG,CAAE,QAASA,UAAU,CAAG,SAAS,CAAE,MAC7C,CAEA,mBACExlB,KAAA,QAAiBqd,KAAK,CAAE,CACtBgI,YAAY,CAAE,KAAK,CACnBzd,eAAe,CAAE,uBAAuB,CACxCD,OAAO,CAAE,KAAK,CACdG,YAAY,CAAE,KAAK,CACnBE,QAAQ,CAAE,MACZ,CAAE,CAAA4Q,QAAA,eACA9Y,IAAA,QAAKud,KAAK,CAAE,CAAEhU,UAAU,CAAE,MAAO,CAAE,CAAAuP,QAAA,CAChCtG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC,CAC9B,CAAC,cACNjS,KAAA,QAAKqd,KAAK,CAAE,CAAE7V,OAAO,CAAE,MAAM,CAAEie,cAAc,CAAE,eAAgB,CAAE,CAAA7M,QAAA,eAC/D9Y,IAAA,SAAA8Y,QAAA,CAAM,gBAAI,CAAM,CAAC,cACjB9Y,IAAA,SAAMud,KAAK,CAAE,CACXjU,KAAK,CAAEoc,UAAU,CACjBnc,UAAU,CAAE,MAAM,CAClBzB,eAAe,CAAE,iBAAiB,CAClCD,OAAO,CAAE,OAAO,CAChBG,YAAY,CAAE,KAChB,CAAE,CAAA8Q,QAAA,CACC5G,KAAK,CAACQ,YAAY,GAAK,GAAG,CAAG,IAAI,CAAGR,KAAK,CAACQ,YAAY,GAAK,GAAG,CAAG,IAAI,CAAG,IAAI,CACzE,CAAC,EACJ,CAAC,cACNxS,KAAA,QAAKqd,KAAK,CAAE,CAAE7V,OAAO,CAAE,MAAM,CAAEie,cAAc,CAAE,eAAgB,CAAE,CAAA7M,QAAA,eAC/D9Y,IAAA,SAAA8Y,QAAA,CAAM,sBAAK,CAAM,CAAC,cAClB5Y,KAAA,SAAMqd,KAAK,CAAE,CAAEhU,UAAU,CAAE,MAAO,CAAE,CAAAuP,QAAA,EAAE5G,KAAK,CAACS,UAAU,CAAC,SAAE,EAAM,CAAC,EAC7D,CAAC,GAzBEuR,KA0BL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,cACNhkB,KAAA,QAAKqd,KAAK,CAAE,CAAEqI,SAAS,CAAE,KAAK,CAAE1d,QAAQ,CAAE,MAAM,CAAEoB,KAAK,CAAE,MAAO,CAAE,CAAAwP,QAAA,EAAC,4BAC3D,CAAC,GAAI,CAAAhL,IAAI,CAAC,CAAC,CAAC+X,kBAAkB,CAAC,CAAC,CAAC,6BACzC,EAAK,CAAC,EACH,CACN,CACH,CAAC,IAAM,CACLjd,OAAO,cACL1I,KAAA,QAAKqd,KAAK,CAAE,CAAE1V,OAAO,CAAE,KAAK,CAAEmW,QAAQ,CAAE,OAAQ,CAAE,CAAAlF,QAAA,eAChD9Y,IAAA,QAAKud,KAAK,CAAE,CAAEhU,UAAU,CAAE,MAAM,CAAEgc,YAAY,CAAE,KAAM,CAAE,CAAAzM,QAAA,CAAEhN,YAAY,CAACG,IAAI,CAAM,CAAC,cAClF/L,KAAA,QAAA4Y,QAAA,EAAK,kBAAM,CAACnQ,OAAO,EAAM,CAAC,cAC1B3I,IAAA,QAAA8Y,QAAA,CAAK,8DAAU,CAAK,CAAC,EAClB,CACN,CACH,CAEA;AACA,KAAM,CAAAgN,OAAO,CAAGnkB,MAAM,CAAC6U,UAAU,CAAG,CAAC,CACrC,KAAM,CAAAuP,OAAO,CAAGpkB,MAAM,CAAC8U,WAAW,CAAG,CAAC,CAEtC;AACA,KAAM,CAAAsM,eAAe,EAAAkC,qBAAA,CAAGrG,QAAQ,CAAC+B,aAAa,CAAC,OAAO,CAAC,UAAAsE,qBAAA,kBAAAC,sBAAA,CAA/BD,qBAAA,CAAiCe,gBAAgB,UAAAd,sBAAA,iBAAjDA,sBAAA,CAAmDzc,sBAAsB,CAEjG,GAAIsa,eAAe,CAAE,CACnB;AACAA,eAAe,CAAC,CACdra,OAAO,CAAE,IAAI,CACbC,OAAO,CAAEA,OAAO,CAChBtB,QAAQ,CAAE,CAAEhD,CAAC,CAAEyhB,OAAO,CAAEvhB,CAAC,CAAEwhB,OAAQ,CAAC,CACpCnd,OAAO,CAAEA,OAAO,CAChBC,MAAM,CAAE,CAAAwc,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAExc,MAAM,GAAI,EAC/B,CAAC,CAAC,CAEFtG,OAAO,CAACC,GAAG,CAAC,SAASsJ,YAAY,CAACG,IAAI,KAAKtD,OAAO,UAAU,CAAC,CAC7D,MAAO,KAAI,CACb,CAAC,IAAM,CACL;AACA,KAAM,CAAAsd,OAAO,CAAGrH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAC7CoH,OAAO,CAAC1I,KAAK,CAAClW,QAAQ,CAAG,UAAU,CACnC4e,OAAO,CAAC1I,KAAK,CAAChW,IAAI,CAAG,GAAGue,OAAO,IAAI,CACnCG,OAAO,CAAC1I,KAAK,CAACrU,GAAG,CAAG,GAAG6c,OAAO,IAAI,CAClCE,OAAO,CAAC1I,KAAK,CAAC/V,SAAS,CAAG,wBAAwB,CAClDye,OAAO,CAAC1I,KAAK,CAAC9V,MAAM,CAAG,MAAM,CAC7Bwe,OAAO,CAAC1I,KAAK,CAACzV,eAAe,CAAG,qBAAqB,CACrDme,OAAO,CAAC1I,KAAK,CAACjU,KAAK,CAAG,OAAO,CAC7B2c,OAAO,CAAC1I,KAAK,CAACvV,YAAY,CAAG,KAAK,CAClCie,OAAO,CAAC1I,KAAK,CAACpV,SAAS,CAAG,8BAA8B,CACxD8d,OAAO,CAAC1I,KAAK,CAAC1V,OAAO,CAAG,KAAK,CAC7Boe,OAAO,CAAC1I,KAAK,CAACS,QAAQ,CAAG,OAAO,CAChCiI,OAAO,CAAC1I,KAAK,CAACrV,QAAQ,CAAG,MAAM,CAE/B+d,OAAO,CAACC,SAAS,CAAG;AAC1B;AACA,YAAYpa,YAAY,CAACG,IAAI,SAAStD,OAAO;AAC7C;AACA;AACA,qBAAqBA,OAAO;AAC5B,eAAe0c,SAAS,CAAG,UAAU,CAAG,YAAY;AACpD;AACA;AACA,OAAO,CAEDzG,QAAQ,CAACuH,IAAI,CAAClP,WAAW,CAACgP,OAAO,CAAC,CAElC;AACA,KAAM,CAAAG,WAAW,CAAGH,OAAO,CAACtF,aAAa,CAAC,QAAQ,CAAC,CACnD,GAAIyF,WAAW,CAAE,CACfA,WAAW,CAACnL,gBAAgB,CAAC,OAAO,CAAE,IAAM,CAC1C2D,QAAQ,CAACuH,IAAI,CAAC5K,WAAW,CAAC0K,OAAO,CAAC,CACpC,CAAC,CAAC,CACJ,CAEA1jB,OAAO,CAACC,GAAG,CAAC,gBAAgBsJ,YAAY,CAACG,IAAI,KAAKtD,OAAO,OAAO,CAAC,CACjE,MAAO,KAAI,CACb,CACF,CAAE,MAAOhF,KAAK,CAAE,CACdpB,OAAO,CAACoB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACAhC,MAAM,CAAC0kB,iBAAiB,CAAG,IAAM,CAC/B9jB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CAErB,GAAI,CAACI,gBAAgB,EAAIA,gBAAgB,CAACya,IAAI,GAAK,CAAC,CAAE,CACpD9a,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB,MAAO,EAAE,CACX,CAEA,KAAM,CAAA8jB,IAAI,CAAG,EAAE,CACf1jB,gBAAgB,CAACmL,OAAO,CAAC,CAACoX,KAAK,CAAElX,EAAE,GAAK,CACtC1L,OAAO,CAACC,GAAG,CAAC,SAASyL,EAAE,SAASkX,KAAK,CAACrZ,YAAY,CAACG,IAAI,EAAE,CAAC,CAC1Dqa,IAAI,CAACxT,IAAI,CAAC,CACR7E,EAAE,CACFhC,IAAI,CAAEkZ,KAAK,CAACrZ,YAAY,CAACG,IAAI,CAC7B5E,QAAQ,CAAE8d,KAAK,CAAC9d,QAClB,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,MAAO,CAAAif,IAAI,CACb,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA3kB,MAAM,CAAC+R,qBAAqB,CAAI/K,OAAO,EAAK,CAC1C,GAAI,CACF;AACAA,OAAO,CAAGqK,MAAM,CAACrK,OAAO,CAAC,CAEzBpG,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAEmG,OAAO,CAAE,KAAK,CAAE,MAAO,CAAAA,OAAO,CAAC,CAC/EpG,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEI,gBAAgB,CAACya,IAAI,CAAC,CAC3D9a,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAEK,kBAAkB,CAACwa,IAAI,CAAC,CAE/D;AACA,GAAI,CAAC1U,OAAO,EAAI/F,gBAAgB,CAACya,IAAI,CAAG,CAAC,CAAE,CACzC1U,OAAO,CAAGqK,MAAM,CAAC5P,KAAK,CAACmjB,IAAI,CAAC3jB,gBAAgB,CAAC4jB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACxDjkB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAEmG,OAAO,CAAC,CAC3C,CAEA;AACApG,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1BI,gBAAgB,CAACmL,OAAO,CAAC,CAACoX,KAAK,CAAElX,EAAE,GAAK,KAAAwY,mBAAA,CACtClkB,OAAO,CAACC,GAAG,CAAC,KAAKyL,EAAE,KAAK,MAAO,CAAAA,EAAE,MAAM,EAAAwY,mBAAA,CAAAtB,KAAK,CAACrZ,YAAY,UAAA2a,mBAAA,iBAAlBA,mBAAA,CAAoBxa,IAAI,GAAI,IAAI,EAAE,CAAC,CAC5E,CAAC,CAAC,CAEF;AACA,GAAI,CAAAyG,YAAY,CAAG9P,gBAAgB,CAACiC,GAAG,CAAC8D,OAAO,CAAC,CAChD,GAAI,CAAC+J,YAAY,CAAE,CACjB;AACA,KAAM,CAAAgU,SAAS,CAAG9T,QAAQ,CAACjK,OAAO,CAAC,CACnC+J,YAAY,CAAG9P,gBAAgB,CAACiC,GAAG,CAAC6hB,SAAS,CAAC,CAE9C,GAAIhU,YAAY,CAAE,CAChBnQ,OAAO,CAACC,GAAG,CAAC,UAAUkkB,SAAS,SAAS,CAAC,CACzC/d,OAAO,CAAG+d,SAAS,CAAE;AACvB,CACF,CAEA,GAAI,CAAChU,YAAY,CAAE,CACjBnQ,OAAO,CAACoB,KAAK,CAAC,cAAc,CAAEgF,OAAO,CAAC,CACtC,MAAO,MAAK,CACd,CAEA;AACA,GAAIhH,MAAM,CAACmH,mBAAmB,CAAE,CAC9BnH,MAAM,CAACmH,mBAAmB,CAACoB,OAAO,CAAGvB,OAAO,CAC9C,CAEA;AACA,GAAIhH,MAAM,CAACoH,0BAA0B,EAAIpH,MAAM,CAACoH,0BAA0B,CAACmB,OAAO,CAAE,CAClFkR,aAAa,CAACzZ,MAAM,CAACoH,0BAA0B,CAACmB,OAAO,CAAC,CACxDvI,MAAM,CAACoH,0BAA0B,CAACmB,OAAO,CAAG,IAAI,CAClD,CAEA;AACA,KAAM,CAAAyc,yBAAyB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CACtC;AACA,GAAI,CAACjlB,MAAM,CAACqH,uBAAuB,CAAE,OAErC,KAAM,CAAA6d,SAAS,EAAAD,qBAAA,CAAGjlB,MAAM,CAACmH,mBAAmB,UAAA8d,qBAAA,iBAA1BA,qBAAA,CAA4B1c,OAAO,CACrD,GAAI,CAAC2c,SAAS,CAAE,OAEhB;AACA,GAAI,CAAAxB,SAAS,CAAGxiB,kBAAkB,CAACgC,GAAG,CAACgiB,SAAS,CAAC,CACjD,GAAI,CAACxB,SAAS,EAAI,MAAO,CAAAwB,SAAS,GAAK,QAAQ,CAAE,CAC/C;AACAxB,SAAS,CAAGxiB,kBAAkB,CAACgC,GAAG,CAAC+N,QAAQ,CAACiU,SAAS,CAAC,CAAC,CACzD,CAAC,IAAM,IAAI,CAACxB,SAAS,EAAI,MAAO,CAAAwB,SAAS,GAAK,QAAQ,CAAE,CACtD;AACAxB,SAAS,CAAGxiB,kBAAkB,CAACgC,GAAG,CAACmO,MAAM,CAAC6T,SAAS,CAAC,CAAC,CACvD,CAEA,GAAI,CAACxB,SAAS,EAAI,CAACA,SAAS,CAACxc,MAAM,EAAIwc,SAAS,CAACxc,MAAM,CAACkQ,MAAM,GAAK,CAAC,CAAE,CACpExW,OAAO,CAACC,GAAG,CAAC,QAAQqkB,SAAS,cAAc,CAAC,CAC5C,OACF,CAEA;AACA,KAAM,CAAAC,iBAAiB,CAAGlkB,gBAAgB,CAACiC,GAAG,CAACgiB,SAAS,CAAC,GAC/B,MAAO,CAAAA,SAAS,GAAK,QAAQ,CAAGjkB,gBAAgB,CAACiC,GAAG,CAAC+N,QAAQ,CAACiU,SAAS,CAAC,CAAC,CACzEjkB,gBAAgB,CAACiC,GAAG,CAACmO,MAAM,CAAC6T,SAAS,CAAC,CAAC,CAAC,CAElE,GAAI,CAACC,iBAAiB,CAAE,CACtBvkB,OAAO,CAACoB,KAAK,CAAC,WAAWkjB,SAAS,gBAAgB,CAAC,CACnD,OACF,CAEA,KAAM,CAAA/a,YAAY,CAAGgb,iBAAiB,CAAChb,YAAY,CAEnD;AACA,KAAM,CAAAlD,OAAO,cACX1I,KAAA,QAAKqd,KAAK,CAAE,CAAE1V,OAAO,CAAE,KAAK,CAAEsB,KAAK,CAAE,OAAO,CAAE2U,SAAS,CAAE,OAAO,CAAEwH,SAAS,CAAE,MAAO,CAAE,CAAAxM,QAAA,eACpF5Y,KAAA,QAAKqd,KAAK,CAAE,CACVhU,UAAU,CAAE,MAAM,CAClBgc,YAAY,CAAE,KAAK,CACnBrd,QAAQ,CAAE,MAAM,CAChBsd,YAAY,CAAE,gBAAgB,CAC9BC,aAAa,CAAE,KACjB,CAAE,CAAA3M,QAAA,EACC,CAAAhN,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEG,IAAI,GAAI,MAAM,CAAC,QAAM,CAAC4a,SAAS,CAAC,GACjD,EAAK,CAAC,cACN7mB,IAAA,QAAA8Y,QAAA,CACGuM,SAAS,CAACxc,MAAM,CAACwG,GAAG,CAAC,CAAC6C,KAAK,CAAEgS,KAAK,GAAK,CACtC,GAAI,CAAAwB,UAAU,CACd,GAAI,CAAAqB,SAAS,CAEb,OAAQ7U,KAAK,CAACQ,YAAY,EACxB,IAAK,GAAG,CACNgT,UAAU,CAAG,SAAS,CACtBqB,SAAS,CAAG,IAAI,CAChB,MACF,IAAK,GAAG,CACNrB,UAAU,CAAG,SAAS,CACtBqB,SAAS,CAAG,IAAI,CAChB,MACF,IAAK,GAAG,CACR,QACErB,UAAU,CAAG,SAAS,CACtBqB,SAAS,CAAG,IAAI,CAChB,MACJ,CAEA,KAAM,CAAA1U,SAAS,CAAGG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC,CAElD,mBACEjS,KAAA,QAAiBqd,KAAK,CAAE,CACtBgI,YAAY,CAAE,KAAK,CACnBzd,eAAe,CAAE,uBAAuB,CACxCD,OAAO,CAAE,KAAK,CACdG,YAAY,CAAE,KAAK,CACnBE,QAAQ,CAAE,MACZ,CAAE,CAAA4Q,QAAA,eACA5Y,KAAA,QAAKqd,KAAK,CAAE,CAAEhU,UAAU,CAAE,MAAO,CAAE,CAAAuP,QAAA,EAChCzG,SAAS,CAAC,oBAAQ,CAACH,KAAK,CAACC,OAAO,CAAC,GACpC,EAAK,CAAC,cACNjS,KAAA,QAAKqd,KAAK,CAAE,CAAE7V,OAAO,CAAE,MAAM,CAAEie,cAAc,CAAE,eAAgB,CAAE,CAAA7M,QAAA,eAC/D9Y,IAAA,SAAA8Y,QAAA,CAAM,gBAAI,CAAM,CAAC,cACjB9Y,IAAA,SAAMud,KAAK,CAAE,CACXjU,KAAK,CAAEoc,UAAU,CACjBnc,UAAU,CAAE,MAAM,CAClBzB,eAAe,CAAE,iBAAiB,CAClCD,OAAO,CAAE,OAAO,CAChBG,YAAY,CAAE,KAChB,CAAE,CAAA8Q,QAAA,CACCiO,SAAS,CACN,CAAC,EACJ,CAAC,cACN7mB,KAAA,QAAKqd,KAAK,CAAE,CAAE7V,OAAO,CAAE,MAAM,CAAEie,cAAc,CAAE,eAAgB,CAAE,CAAA7M,QAAA,eAC/D9Y,IAAA,SAAA8Y,QAAA,CAAM,sBAAK,CAAM,CAAC,cAClB5Y,KAAA,SAAMqd,KAAK,CAAE,CAAEhU,UAAU,CAAE,MAAO,CAAE,CAAAuP,QAAA,EAAE5G,KAAK,CAACS,UAAU,CAAC,SAAE,EAAM,CAAC,EAC7D,CAAC,GAzBEuR,KA0BL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,cACNhkB,KAAA,QAAKqd,KAAK,CAAE,CAAEqI,SAAS,CAAE,KAAK,CAAE1d,QAAQ,CAAE,MAAM,CAAEoB,KAAK,CAAE,MAAO,CAAE,CAAAwP,QAAA,EAAC,4BAC3D,CAAC,GAAI,CAAAhL,IAAI,CAACuX,SAAS,CAAC7R,UAAU,CAAC,CAACqS,kBAAkB,CAAC,CAAC,CAAC,6BAC7D,EAAK,CAAC,EACH,CACN,CAED;AACAlkB,MAAM,CAACqH,uBAAuB,CAACmK,IAAI,GAAK,CACtC,GAAGA,IAAI,CACPvK,OAAO,CAAEA,OAAO,CAChBC,MAAM,CAAEwc,SAAS,CAACxc,MACpB,CAAC,CAAC,CAAC,CAEHtG,OAAO,CAACC,GAAG,CAAC,SAAS,CAAAsJ,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEG,IAAI,GAAI4a,SAAS,WAAW,CAAC,CAClE,CAAC,CAED;AACA,KAAM,CAAAG,gCAAgC,CAAGA,CAAA,GAAM,CAC7C;AACA,GAAI,CAAA3B,SAAS,CAAGxiB,kBAAkB,CAACgC,GAAG,CAAC8D,OAAO,CAAC,CAC/C,GAAI,CAAC0c,SAAS,CAAE,CACd;AACA,KAAM,CAAAqB,SAAS,CAAG9T,QAAQ,CAACjK,OAAO,CAAC,CACnC0c,SAAS,CAAGxiB,kBAAkB,CAACgC,GAAG,CAAC6hB,SAAS,CAAC,CAE7C,GAAIrB,SAAS,CAAE,CACb9iB,OAAO,CAACC,GAAG,CAAC,UAAUkkB,SAAS,aAAa,CAAC,CAC/C,CACF,CAEAnkB,OAAO,CAACC,GAAG,CAAC,QAAQmG,OAAO,SAAS,CAAE0c,SAAS,CAAC,CAEhD,KAAM,CAAAvZ,YAAY,CAAG4G,YAAY,CAAC5G,YAAY,CAC9CvJ,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEsJ,YAAY,CAAC,CAElC;AACA,GAAI,CAAAlD,OAAO,CAEX,GAAIyc,SAAS,EAAIA,SAAS,CAACxc,MAAM,EAAIwc,SAAS,CAACxc,MAAM,CAACkQ,MAAM,CAAG,CAAC,CAAE,CAChE;AACAxW,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC,CACtB6iB,SAAS,CAACxc,MAAM,CAACkF,OAAO,CAAC,CAACmE,KAAK,CAAEgS,KAAK,GAAK,CACzC3hB,OAAO,CAACC,GAAG,CAAC,MAAM0hB,KAAK,CAAC,CAAC,QAAQhS,KAAK,CAACC,OAAO,QAAQD,KAAK,CAACQ,YAAY,UAAUR,KAAK,CAACS,UAAU,EAAE,CAAC,CACvG,CAAC,CAAC,CAEF/J,OAAO,cACL1I,KAAA,QAAKqd,KAAK,CAAE,CAAE1V,OAAO,CAAE,KAAK,CAAEsB,KAAK,CAAE,OAAO,CAAE2U,SAAS,CAAE,OAAO,CAAEwH,SAAS,CAAE,MAAO,CAAE,CAAAxM,QAAA,eACpF5Y,KAAA,QAAKqd,KAAK,CAAE,CACVhU,UAAU,CAAE,MAAM,CAClBgc,YAAY,CAAE,KAAK,CACnBrd,QAAQ,CAAE,MAAM,CAChBsd,YAAY,CAAE,gBAAgB,CAC9BC,aAAa,CAAE,KACjB,CAAE,CAAA3M,QAAA,EACC,CAAAhN,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEG,IAAI,GAAI,MAAM,CAAC,QAAM,CAACtD,OAAO,CAAC,GAC/C,EAAK,CAAC,cACN3I,IAAA,QAAA8Y,QAAA,CACGuM,SAAS,CAACxc,MAAM,CAACwG,GAAG,CAAC,CAAC6C,KAAK,CAAEgS,KAAK,GAAK,CACtC,GAAI,CAAAwB,UAAU,CACd,GAAI,CAAAqB,SAAS,CAEb,OAAQ7U,KAAK,CAACQ,YAAY,EACxB,IAAK,GAAG,CACNgT,UAAU,CAAG,SAAS,CACtBqB,SAAS,CAAG,IAAI,CAChB,MACF,IAAK,GAAG,CACNrB,UAAU,CAAG,SAAS,CACtBqB,SAAS,CAAG,IAAI,CAChB,MACF,IAAK,GAAG,CACR,QACErB,UAAU,CAAG,SAAS,CACtBqB,SAAS,CAAG,IAAI,CAChB,MACJ,CAEA,KAAM,CAAA1U,SAAS,CAAGG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC,CAElD,mBACEjS,KAAA,QAAiBqd,KAAK,CAAE,CACtBgI,YAAY,CAAE,KAAK,CACnBzd,eAAe,CAAE,uBAAuB,CACxCD,OAAO,CAAE,KAAK,CACdG,YAAY,CAAE,KAAK,CACnBE,QAAQ,CAAE,MACZ,CAAE,CAAA4Q,QAAA,eACA5Y,KAAA,QAAKqd,KAAK,CAAE,CAAEhU,UAAU,CAAE,MAAO,CAAE,CAAAuP,QAAA,EAChCzG,SAAS,CAAC,oBAAQ,CAACH,KAAK,CAACC,OAAO,CAAC,GACpC,EAAK,CAAC,cACNjS,KAAA,QAAKqd,KAAK,CAAE,CAAE7V,OAAO,CAAE,MAAM,CAAEie,cAAc,CAAE,eAAgB,CAAE,CAAA7M,QAAA,eAC/D9Y,IAAA,SAAA8Y,QAAA,CAAM,gBAAI,CAAM,CAAC,cACjB9Y,IAAA,SAAMud,KAAK,CAAE,CACXjU,KAAK,CAAEoc,UAAU,CACjBnc,UAAU,CAAE,MAAM,CAClBzB,eAAe,CAAE,iBAAiB,CAClCD,OAAO,CAAE,OAAO,CAChBG,YAAY,CAAE,KAChB,CAAE,CAAA8Q,QAAA,CACCiO,SAAS,CACN,CAAC,EACJ,CAAC,cACN7mB,KAAA,QAAKqd,KAAK,CAAE,CAAE7V,OAAO,CAAE,MAAM,CAAEie,cAAc,CAAE,eAAgB,CAAE,CAAA7M,QAAA,eAC/D9Y,IAAA,SAAA8Y,QAAA,CAAM,sBAAK,CAAM,CAAC,cAClB5Y,KAAA,SAAMqd,KAAK,CAAE,CAAEhU,UAAU,CAAE,MAAO,CAAE,CAAAuP,QAAA,EAAE5G,KAAK,CAACS,UAAU,CAAC,SAAE,EAAM,CAAC,EAC7D,CAAC,GAzBEuR,KA0BL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,cACNhkB,KAAA,QAAKqd,KAAK,CAAE,CAAEqI,SAAS,CAAE,KAAK,CAAE1d,QAAQ,CAAE,MAAM,CAAEoB,KAAK,CAAE,MAAO,CAAE,CAAAwP,QAAA,EAAC,4BAC3D,CAAC,GAAI,CAAAhL,IAAI,CAACuX,SAAS,CAAC7R,UAAU,CAAC,CAACqS,kBAAkB,CAAC,CAAC,CAAC,6BAC7D,EAAK,CAAC,EACH,CACN,CACH,CAAC,IAAM,CACL;AACAjd,OAAO,cACL1I,KAAA,QAAKqd,KAAK,CAAE,CAAE1V,OAAO,CAAE,KAAK,CAAEsB,KAAK,CAAE,OAAQ,CAAE,CAAA2P,QAAA,eAC7C5Y,KAAA,QAAKqd,KAAK,CAAE,CACVhU,UAAU,CAAE,MAAM,CAClBgc,YAAY,CAAE,KAAK,CACnBrd,QAAQ,CAAE,MAAM,CAChBsd,YAAY,CAAE,gBAAgB,CAC9BC,aAAa,CAAE,KACjB,CAAE,CAAA3M,QAAA,EACC,CAAAhN,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEG,IAAI,GAAI,MAAM,CAAC,QAAM,CAACtD,OAAO,CAAC,GAC/C,EAAK,CAAC,cACN3I,IAAA,QAAKud,KAAK,CAAE,CAAEjU,KAAK,CAAE,SAAS,CAAEpB,QAAQ,CAAE,MAAO,CAAE,CAAA4Q,QAAA,CAAC,oIAEpD,CAAK,CAAC,cACN5Y,KAAA,QAAKqd,KAAK,CAAE,CAAEqI,SAAS,CAAE,KAAK,CAAE1d,QAAQ,CAAE,MAAM,CAAEoB,KAAK,CAAE,MAAO,CAAE,CAAAwP,QAAA,EAAC,4BAC3D,CAAC,GAAI,CAAAhL,IAAI,CAAC,CAAC,CAAC+X,kBAAkB,CAAC,CAAC,EACnC,CAAC,EACH,CACN,CACH,CAEA;AACA,KAAM,CAAAxhB,CAAC,CAAG1C,MAAM,CAAC6U,UAAU,CAAG,CAAC,CAC/B,KAAM,CAAAjS,CAAC,CAAG5C,MAAM,CAAC8U,WAAW,CAAG,CAAC,CAEhC;AACA,GAAI9U,MAAM,CAACqH,uBAAuB,CAAE,KAAAie,UAAA,CAClC1kB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAE,CAC7CkG,OAAO,CAAE,IAAI,CACbC,OAAO,CACPtB,QAAQ,CAAE,CAAEhD,CAAC,CAAEE,CAAE,CACnB,CAAC,CAAC,CAEF5C,MAAM,CAACqH,uBAAuB,CAAC,CAC7BN,OAAO,CAAE,IAAI,CACbC,OAAO,CAAEA,OAAO,CAChBtB,QAAQ,CAAE,CAAEhD,CAAC,CAAEE,CAAE,CAAC,CAClBqE,OAAO,CAAEA,OAAO,CAChBC,MAAM,CAAE,EAAAoe,UAAA,CAAA5B,SAAS,UAAA4B,UAAA,iBAATA,UAAA,CAAWpe,MAAM,GAAI,EAC/B,CAAC,CAAC,CAEFtG,OAAO,CAACC,GAAG,CAAC,SAAS,CAAAsJ,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEG,IAAI,GAAItD,OAAO,WAAW,CAAC,CAE9D;AACA,GAAIhH,MAAM,CAACoH,0BAA0B,CAAE,CACrCpH,MAAM,CAACoH,0BAA0B,CAACmB,OAAO,CAAG0R,WAAW,CAAC+K,yBAAyB,CAAE9gB,6BAA6B,CAAG,IAAI,CAAC,CACxHtD,OAAO,CAACC,GAAG,CAAC,qBAAqBqD,6BAA6B,IAAI,CAAC,CACrE,CAEA,MAAO,KAAI,CACb,CAAC,IAAM,CACLtD,OAAO,CAACoB,KAAK,CAAC,8BAA8B,CAAC,CAC7C,MAAO,MAAK,CACd,CACF,CAAC,CAED,MAAO,CAAAqjB,gCAAgC,CAAC,CAAC,CAC3C,CAAE,MAAOrjB,KAAK,CAAE,CACdpB,OAAO,CAACoB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA,KAAM,CAAA0gB,yBAAyB,CAAIF,MAAM,EAAK,CAC5C,GAAI,CAAAja,OAAO,CAAGia,MAAM,CAEpB;AACA,GAAIja,OAAO,EAAIA,OAAO,CAACiT,QAAQ,EAAIjT,OAAO,CAACiT,QAAQ,CAAChP,IAAI,GAAK,cAAc,CAAE,CAC3E5L,OAAO,CAACC,GAAG,CAAC,YAAY,CAAE0H,OAAO,CAAC+B,IAAI,EAAI,KAAK,CAAC,CAChD,MAAO,CAAA/B,OAAO,CAChB,CAEA;AACA,MAAOA,OAAO,EAAIA,OAAO,CAAC0X,MAAM,CAAE,CAChC1X,OAAO,CAAGA,OAAO,CAAC0X,MAAM,CACxB,GAAI1X,OAAO,CAACiT,QAAQ,EAAIjT,OAAO,CAACiT,QAAQ,CAAChP,IAAI,GAAK,cAAc,CAAE,CAChE5L,OAAO,CAACC,GAAG,CAAC,YAAY,CAAE0H,OAAO,CAAC+B,IAAI,EAAI,KAAK,CAAC,CAChD,MAAO,CAAA/B,OAAO,CAChB,CACF,CAEA,MAAO,KAAI,CACb,CAAC,CAED;AACAvI,MAAM,CAACulB,kBAAkB,CAAG,CAAC7iB,CAAC,CAAEE,CAAC,GAAK,CACpC,GAAI,CACFhC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAE6B,CAAC,CAAEE,CAAC,CAAC,CAEnC;AACA,KAAM,CAAAoa,MAAM,CAAGC,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAC/C,GAAI,CAAChC,MAAM,CAAE,CACXpc,OAAO,CAACoB,KAAK,CAAC,sBAAsB,CAAC,CACrC,MAAO,MAAK,CACd,CAEA;AACA,GAAI,CAACzC,KAAK,EAAI,CAACmH,SAAS,CAAC6B,OAAO,CAAE,CAChC3H,OAAO,CAACoB,KAAK,CAAC,iBAAiB,CAAC,CAChC,MAAO,MAAK,CACd,CAEA;AACA,GAAIU,CAAC,GAAKuN,SAAS,EAAIrN,CAAC,GAAKqN,SAAS,CAAE,CACtCvN,CAAC,CAAG1C,MAAM,CAAC6U,UAAU,CAAG,CAAC,CACzBjS,CAAC,CAAG5C,MAAM,CAAC8U,WAAW,CAAG,CAAC,CAC5B,CAEA;AACA,KAAM,CAAAuM,IAAI,CAAGrE,MAAM,CAACsE,qBAAqB,CAAC,CAAC,CAC3C,KAAM,CAAAC,MAAM,CAAI,CAAC7e,CAAC,CAAG2e,IAAI,CAACzb,IAAI,EAAIoX,MAAM,CAACwE,WAAW,CAAI,CAAC,CAAG,CAAC,CAC7D,KAAM,CAAAC,MAAM,CAAG,EAAE,CAAC7e,CAAC,CAAGye,IAAI,CAAC9Z,GAAG,EAAIyV,MAAM,CAAC0E,YAAY,CAAC,CAAG,CAAC,CAAG,CAAC,CAE9D9gB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAE0gB,MAAM,CAAEE,MAAM,CAAC,CAErC;AACA,KAAM,CAAAE,SAAS,CAAG,GAAI,CAAAhkB,KAAK,CAACikB,SAAS,CAAC,CAAC,CACvCD,SAAS,CAAC/E,MAAM,CAACiF,MAAM,CAACC,SAAS,CAAG,CAAC,CACrCH,SAAS,CAAC/E,MAAM,CAACmF,IAAI,CAACD,SAAS,CAAG,CAAC,CAEnC,KAAM,CAAAE,WAAW,CAAG,GAAI,CAAArkB,KAAK,CAACskB,OAAO,CAACV,MAAM,CAAEE,MAAM,CAAC,CACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,CAAEtb,SAAS,CAAC6B,OAAO,CAAC,CAEvD;AACA,KAAM,CAAA4Z,mBAAmB,CAAG,EAAE,CAC9BlhB,gBAAgB,CAACmL,OAAO,CAAC,CAAC0N,QAAQ,CAAE9S,OAAO,GAAK,CAC9C,GAAI8S,QAAQ,CAAC7M,KAAK,CAAE,CAClBkV,mBAAmB,CAAChR,IAAI,CAAC2I,QAAQ,CAAC7M,KAAK,CAAC,CACxCrM,OAAO,CAACC,GAAG,CAAC,SAASmG,OAAO,QAAQ,CAAC,CACvC,CACF,CAAC,CAAC,CAEF;AACApG,OAAO,CAACC,GAAG,CAAC,QAAQshB,mBAAmB,CAAC/K,MAAM,YAAY,CAAC,CAC3D,KAAM,CAAAoO,YAAY,CAAG7D,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,CAAE,IAAI,CAAC,CAE1E,GAAIqD,YAAY,CAACpO,MAAM,CAAG,CAAC,CAAE,CAC3BxW,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1B2kB,YAAY,CAACpZ,OAAO,CAAC,CAACkW,SAAS,CAAEjY,CAAC,GAAK,CACrCzJ,OAAO,CAACC,GAAG,CAAC,MAAMwJ,CAAC,GAAG,CAAEiY,SAAS,CAACE,MAAM,CAAClY,IAAI,EAAI,KAAK,CAC1C,KAAK,CAAEgY,SAAS,CAACnf,QAAQ,CACzB,WAAW,CAAEmf,SAAS,CAACE,MAAM,CAAC9c,QAAQ,CAACoF,OAAO,CAAC,CAAC,CAChD,WAAW,CAAEwX,SAAS,CAACE,MAAM,CAAChH,QAAQ,CAAC,CAEnD;AACA,KAAM,CAAAiH,GAAG,CAAGC,yBAAyB,CAACJ,SAAS,CAACE,MAAM,CAAC,CACvD,GAAIC,GAAG,EAAIA,GAAG,CAACjH,QAAQ,EAAIiH,GAAG,CAACjH,QAAQ,CAAChP,IAAI,GAAK,cAAc,CAAE,CAC/D5L,OAAO,CAACC,GAAG,CAAC,UAAU,CAAE4hB,GAAG,CAACjH,QAAQ,CAACxU,OAAO,CAAC,CAC/C,CACF,CAAC,CAAC,CAEF,MAAO,KAAI,CACb,CAEA;AACApG,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAC7B,KAAM,CAAA4kB,eAAe,CAAG9D,SAAS,CAACU,gBAAgB,CAAC9iB,KAAK,CAAC4X,QAAQ,CAAE,IAAI,CAAC,CAExEvW,OAAO,CAACC,GAAG,CAAC,WAAW4kB,eAAe,CAACrO,MAAM,MAAM,CAAC,CACpDqO,eAAe,CAACrZ,OAAO,CAAC,CAACkW,SAAS,CAAEjY,CAAC,GAAK,CACxC,KAAM,CAAAoY,GAAG,CAAGH,SAAS,CAACE,MAAM,CAC5B5hB,OAAO,CAACC,GAAG,CAAC,QAAQwJ,CAAC,GAAG,CAAEoY,GAAG,CAACnY,IAAI,EAAI,KAAK,CAC/B,KAAK,CAAEmY,GAAG,CAACjW,IAAI,CACf,KAAK,CAAEiW,GAAG,CAAC/c,QAAQ,CAACoF,OAAO,CAAC,CAAC,CAC7B,KAAK,CAAEwX,SAAS,CAACnf,QAAQ,CACzB,WAAW,CAAEsf,GAAG,CAACjH,QAAQ,CAAC,CACxC,CAAC,CAAC,CAEF;AACA5a,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3B,GAAI,CAAA6kB,YAAY,CAAG,CAAC,CAEpBzkB,gBAAgB,CAACmL,OAAO,CAAC,CAAC0N,QAAQ,CAAE9S,OAAO,GAAK,CAC9C,GAAI8S,QAAQ,CAAC7M,KAAK,CAAE,KAAA0Y,qBAAA,CAClB;AACA,GAAI,CAAAC,SAAS,CAAG9L,QAAQ,CAAC7M,KAAK,CAAClG,OAAO,CACtC,GAAI,CAAA8e,cAAc,CAAG,IAAI,CAEzB;AACA,KAAM,CAAA/C,QAAQ,CAAG,GAAI,CAAAnlB,KAAK,CAAC6F,OAAO,CAAC,CAAC,CACpCsW,QAAQ,CAAC7M,KAAK,CAAC8V,gBAAgB,CAACD,QAAQ,CAAC,CAEzC;AACA,KAAM,CAAAgD,gBAAgB,CAAGhD,QAAQ,CAAC1f,UAAU,CAACsD,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAC,CAExE;AACA,KAAM,CAAAsd,SAAS,CAAGF,QAAQ,CAACtgB,KAAK,CAAC,CAAC,CAACygB,OAAO,CAACvc,SAAS,CAAC6B,OAAO,CAAC,CAC7D,GAAI3E,IAAI,CAACK,GAAG,CAAC+e,SAAS,CAACtgB,CAAC,CAAC,CAAG,CAAC,EAAIkB,IAAI,CAACK,GAAG,CAAC+e,SAAS,CAACpgB,CAAC,CAAC,CAAG,CAAC,EAAIogB,SAAS,CAAClgB,CAAC,CAAG,CAAC,CAAC,EAAIkgB,SAAS,CAAClgB,CAAC,CAAG,CAAC,CAAE,CACjG+iB,cAAc,CAAG,KAAK,CACxB,CAEA,GAAID,SAAS,CAAE,CACbF,YAAY,EAAE,CAChB,CAEA9kB,OAAO,CAACC,GAAG,CAAC,OAAOmG,OAAO,GAAG,CAAE,CAC7B+e,EAAE,CAAE,EAAAJ,qBAAA,CAAA7L,QAAQ,CAAC3P,YAAY,UAAAwb,qBAAA,iBAArBA,qBAAA,CAAuBrb,IAAI,GAAI,IAAI,CACvC0b,GAAG,CAAEJ,SAAS,CACdK,KAAK,CAAEJ,cAAc,CACrBK,IAAI,CAAEpD,QAAQ,CAAChY,OAAO,CAAC,CAAC,CACxBqb,IAAI,CAAE,CAACnD,SAAS,CAACtgB,CAAC,CAAEsgB,SAAS,CAACpgB,CAAC,CAAEogB,SAAS,CAAClgB,CAAC,CAAC,CAC7CsjB,MAAM,CAAEN,gBACV,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEFllB,OAAO,CAACC,GAAG,CAAC,MAAM6kB,YAAY,IAAIzkB,gBAAgB,CAACya,IAAI,WAAW,CAAC,CAEnE;AACA,MAAO,CAAA+J,eAAe,CAACrO,MAAM,CAAG,CAAC,CACnC,CAAE,MAAOpV,KAAK,CAAE,CACdpB,OAAO,CAACoB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B,MAAO,MAAK,CACd,CACF,CAAC,CAID;AACA,KAAM,CAAAuP,wBAAwB,CAAGA,CAACR,YAAY,CAAEG,SAAS,GAAK,KAAAmV,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAC5D,GAAI,CAACxV,YAAY,EAAI,CAACA,YAAY,CAAC9D,KAAK,EAAI,CAACiE,SAAS,CAAE,CACtD,OACF,CAEA;AACA,KAAM,CAAAsV,cAAc,CAAG,EAAE,CACzBzV,YAAY,CAAC9D,KAAK,CAACwB,QAAQ,CAACC,KAAK,EAAI,CACnC,GAAIA,KAAK,CAAC8M,QAAQ,EAAI9M,KAAK,CAAC8M,QAAQ,CAACiL,OAAO,CAAE,CAC5CD,cAAc,CAACrV,IAAI,CAACzC,KAAK,CAAC,CAC5B,CACF,CAAC,CAAC,CAEF8X,cAAc,CAACpa,OAAO,CAACoX,KAAK,EAAI,CAC9BzS,YAAY,CAAC9D,KAAK,CAACY,MAAM,CAAC2V,KAAK,CAAC,CAClC,CAAC,CAAC,CAEF;AACA,GAAI,CAAAO,UAAU,CACd,OAAO7S,SAAS,CAACH,YAAY,EAC3B,IAAK,GAAG,CACNgT,UAAU,CAAG,QAAQ,CAAE;AACvB,MACF,IAAK,GAAG,CACNA,UAAU,CAAG,QAAQ,CAAE;AACvB,MACF,IAAK,GAAG,CACR,QACEA,UAAU,CAAG,QAAQ,CAAE;AACvB,MACJ,CAEA;AACA,KAAM,CAAAlD,aAAa,CAAG,GAAI,CAAAljB,KAAK,CAACyd,cAAc,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CACzD,KAAM,CAAA0F,aAAa,CAAG,GAAI,CAAAnjB,KAAK,CAACid,iBAAiB,CAAC,CAChDjT,KAAK,CAAEoc,UAAU,CACjBjV,QAAQ,CAAEiV,UAAU,CACpB2C,iBAAiB,CAAE,CACrB,CAAC,CAAC,CACF,KAAM,CAAA3F,SAAS,CAAG,GAAI,CAAApjB,KAAK,CAACkd,IAAI,CAACgG,aAAa,CAAEC,aAAa,CAAC,CAC9DC,SAAS,CAACrb,QAAQ,CAAC3C,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAAE;AAClCge,SAAS,CAACvF,QAAQ,CAAG,CACnBiL,OAAO,CAAE,IAAI,CACbja,IAAI,CAAE,cAAc,CACpBxF,OAAO,EAAAqf,qBAAA,CAAEtV,YAAY,CAAC5G,YAAY,UAAAkc,qBAAA,iBAAzBA,qBAAA,CAA2Brf,OAAO,CAC3CwJ,OAAO,CAAEU,SAAS,CAACV,OAAO,CAC1BE,SAAS,CAAEQ,SAAS,CAACR,SAAS,CAC9BM,UAAU,CAAEE,SAAS,CAACF,UACxB,CAAC,CAED;AACA,KAAM,CAAAwS,KAAK,CAAG,GAAI,CAAA7lB,KAAK,CAACgpB,UAAU,CAAC5C,UAAU,CAAE,CAAC,CAAE,EAAE,CAAC,CACrDP,KAAK,CAAC9d,QAAQ,CAAC3C,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAC5BygB,KAAK,CAAChI,QAAQ,CAAG,CAAEiL,OAAO,CAAE,IAAK,CAAC,CAElC;AACA1V,YAAY,CAAC9D,KAAK,CAACI,GAAG,CAAC0T,SAAS,CAAC,CACjChQ,YAAY,CAAC9D,KAAK,CAACI,GAAG,CAACmW,KAAK,CAAC,CAE7B5iB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAAylB,sBAAA,CAAAvV,YAAY,CAAC5G,YAAY,UAAAmc,sBAAA,iBAAzBA,sBAAA,CAA2Bhc,IAAI,KAAAic,sBAAA,CAAIxV,YAAY,CAAC5G,YAAY,UAAAoc,sBAAA,iBAAzBA,sBAAA,CAA2Bvf,OAAO,cAAakK,SAAS,CAACH,YAAY,WAAWG,SAAS,CAACF,UAAU,GAAG,CAAC,CACjK,CAAC,CAED,cAAe,CAAA7M,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}