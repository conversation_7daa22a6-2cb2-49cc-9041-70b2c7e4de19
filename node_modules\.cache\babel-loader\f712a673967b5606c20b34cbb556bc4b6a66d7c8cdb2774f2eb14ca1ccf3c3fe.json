{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { retrieveRawValue } from '../../data/helper/dataProvider.js';\nimport { formatTpl } from '../../util/format.js';\nimport { error, makePrintable } from '../../util/log.js';\nvar DIMENSION_LABEL_REG = /\\{@(.+?)\\}/g;\nvar DataFormatMixin = /** @class */function () {\n  function DataFormatMixin() {}\n  /**\r\n   * Get params for formatter\r\n   */\n  DataFormatMixin.prototype.getDataParams = function (dataIndex, dataType) {\n    var data = this.getData(dataType);\n    var rawValue = this.getRawValue(dataIndex, dataType);\n    var rawDataIndex = data.getRawIndex(dataIndex);\n    var name = data.getName(dataIndex);\n    var itemOpt = data.getRawDataItem(dataIndex);\n    var style = data.getItemVisual(dataIndex, 'style');\n    var color = style && style[data.getItemVisual(dataIndex, 'drawType') || 'fill'];\n    var borderColor = style && style.stroke;\n    var mainType = this.mainType;\n    var isSeries = mainType === 'series';\n    var userOutput = data.userOutput && data.userOutput.get();\n    return {\n      componentType: mainType,\n      componentSubType: this.subType,\n      componentIndex: this.componentIndex,\n      seriesType: isSeries ? this.subType : null,\n      seriesIndex: this.seriesIndex,\n      seriesId: isSeries ? this.id : null,\n      seriesName: isSeries ? this.name : null,\n      name: name,\n      dataIndex: rawDataIndex,\n      data: itemOpt,\n      dataType: dataType,\n      value: rawValue,\n      color: color,\n      borderColor: borderColor,\n      dimensionNames: userOutput ? userOutput.fullDimensions : null,\n      encode: userOutput ? userOutput.encode : null,\n      // Param name list for mapping `a`, `b`, `c`, `d`, `e`\n      $vars: ['seriesName', 'name', 'value']\n    };\n  };\n  /**\r\n   * Format label\r\n   * @param dataIndex\r\n   * @param status 'normal' by default\r\n   * @param dataType\r\n   * @param labelDimIndex Only used in some chart that\r\n   *        use formatter in different dimensions, like radar.\r\n   * @param formatter Formatter given outside.\r\n   * @return return null/undefined if no formatter\r\n   */\n  DataFormatMixin.prototype.getFormattedLabel = function (dataIndex, status, dataType, labelDimIndex, formatter, extendParams) {\n    status = status || 'normal';\n    var data = this.getData(dataType);\n    var params = this.getDataParams(dataIndex, dataType);\n    if (extendParams) {\n      params.value = extendParams.interpolatedValue;\n    }\n    if (labelDimIndex != null && zrUtil.isArray(params.value)) {\n      params.value = params.value[labelDimIndex];\n    }\n    if (!formatter) {\n      var itemModel = data.getItemModel(dataIndex);\n      // @ts-ignore\n      formatter = itemModel.get(status === 'normal' ? ['label', 'formatter'] : [status, 'label', 'formatter']);\n    }\n    if (zrUtil.isFunction(formatter)) {\n      params.status = status;\n      params.dimensionIndex = labelDimIndex;\n      return formatter(params);\n    } else if (zrUtil.isString(formatter)) {\n      var str = formatTpl(formatter, params);\n      // Support 'aaa{@[3]}bbb{@product}ccc'.\n      // Do not support '}' in dim name util have to.\n      return str.replace(DIMENSION_LABEL_REG, function (origin, dimStr) {\n        var len = dimStr.length;\n        var dimLoose = dimStr;\n        if (dimLoose.charAt(0) === '[' && dimLoose.charAt(len - 1) === ']') {\n          dimLoose = +dimLoose.slice(1, len - 1); // Also support: '[]' => 0\n          if (process.env.NODE_ENV !== 'production') {\n            if (isNaN(dimLoose)) {\n              error(\"Invalide label formatter: @\" + dimStr + \", only support @[0], @[1], @[2], ...\");\n            }\n          }\n        }\n        var val = retrieveRawValue(data, dataIndex, dimLoose);\n        if (extendParams && zrUtil.isArray(extendParams.interpolatedValue)) {\n          var dimIndex = data.getDimensionIndex(dimLoose);\n          if (dimIndex >= 0) {\n            val = extendParams.interpolatedValue[dimIndex];\n          }\n        }\n        return val != null ? val + '' : '';\n      });\n    }\n  };\n  /**\r\n   * Get raw value in option\r\n   */\n  DataFormatMixin.prototype.getRawValue = function (idx, dataType) {\n    return retrieveRawValue(this.getData(dataType), idx);\n  };\n  /**\r\n   * Should be implemented.\r\n   * @param {number} dataIndex\r\n   * @param {boolean} [multipleSeries=false]\r\n   * @param {string} [dataType]\r\n   */\n  DataFormatMixin.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    // Empty function\n    return;\n  };\n  return DataFormatMixin;\n}();\nexport { DataFormatMixin };\n;\n// PENDING: previously we accept this type when calling `formatTooltip`,\n// but guess little chance has been used outside. Do we need to backward\n// compat it?\n// type TooltipFormatResultLegacyObject = {\n//     // `html` means the markup language text, either in 'html' or 'richText'.\n//     // The name `html` is not appropriate because in 'richText' it is not a HTML\n//     // string. But still support it for backward compatibility.\n//     html: string;\n//     markers: Dictionary<ColorString>;\n// };\n/**\r\n * For backward compat, normalize the return from `formatTooltip`.\r\n */\nexport function normalizeTooltipFormatResult(result) {\n  var markupText;\n  // let markers: Dictionary<ColorString>;\n  var markupFragment;\n  if (zrUtil.isObject(result)) {\n    if (result.type) {\n      markupFragment = result;\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn('The return type of `formatTooltip` is not supported: ' + makePrintable(result));\n      }\n    }\n    // else {\n    //     markupText = (result as TooltipFormatResultLegacyObject).html;\n    //     markers = (result as TooltipFormatResultLegacyObject).markers;\n    //     if (markersExisting) {\n    //         markers = zrUtil.merge(markersExisting, markers);\n    //     }\n    // }\n  } else {\n    markupText = result;\n  }\n  return {\n    text: markupText,\n    // markers: markers || markersExisting,\n    frag: markupFragment\n  };\n}", "map": {"version": 3, "names": ["zrUtil", "retrieveRawValue", "formatTpl", "error", "makePrintable", "DIMENSION_LABEL_REG", "DataFormatMixin", "prototype", "getDataParams", "dataIndex", "dataType", "data", "getData", "rawValue", "getRawValue", "rawDataIndex", "getRawIndex", "name", "getName", "itemOpt", "getRawDataItem", "style", "getItemVisual", "color", "borderColor", "stroke", "mainType", "isSeries", "userOutput", "get", "componentType", "componentSubType", "subType", "componentIndex", "seriesType", "seriesIndex", "seriesId", "id", "seriesName", "value", "dimensionNames", "fullDimensions", "encode", "$vars", "getFormattedLabel", "status", "labelDimIndex", "formatter", "extendParams", "params", "interpolatedV<PERSON>ue", "isArray", "itemModel", "getItemModel", "isFunction", "dimensionIndex", "isString", "str", "replace", "origin", "dimStr", "len", "length", "dimLoose", "char<PERSON>t", "slice", "process", "env", "NODE_ENV", "isNaN", "val", "dimIndex", "getDimensionIndex", "idx", "formatTooltip", "multipleSeries", "normalizeTooltipFormatResult", "result", "markupText", "markupFragment", "isObject", "type", "console", "warn", "text", "frag"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/echarts/lib/model/mixin/dataFormat.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { retrieveRawValue } from '../../data/helper/dataProvider.js';\nimport { formatTpl } from '../../util/format.js';\nimport { error, makePrintable } from '../../util/log.js';\nvar DIMENSION_LABEL_REG = /\\{@(.+?)\\}/g;\nvar DataFormatMixin = /** @class */function () {\n  function DataFormatMixin() {}\n  /**\r\n   * Get params for formatter\r\n   */\n  DataFormatMixin.prototype.getDataParams = function (dataIndex, dataType) {\n    var data = this.getData(dataType);\n    var rawValue = this.getRawValue(dataIndex, dataType);\n    var rawDataIndex = data.getRawIndex(dataIndex);\n    var name = data.getName(dataIndex);\n    var itemOpt = data.getRawDataItem(dataIndex);\n    var style = data.getItemVisual(dataIndex, 'style');\n    var color = style && style[data.getItemVisual(dataIndex, 'drawType') || 'fill'];\n    var borderColor = style && style.stroke;\n    var mainType = this.mainType;\n    var isSeries = mainType === 'series';\n    var userOutput = data.userOutput && data.userOutput.get();\n    return {\n      componentType: mainType,\n      componentSubType: this.subType,\n      componentIndex: this.componentIndex,\n      seriesType: isSeries ? this.subType : null,\n      seriesIndex: this.seriesIndex,\n      seriesId: isSeries ? this.id : null,\n      seriesName: isSeries ? this.name : null,\n      name: name,\n      dataIndex: rawDataIndex,\n      data: itemOpt,\n      dataType: dataType,\n      value: rawValue,\n      color: color,\n      borderColor: borderColor,\n      dimensionNames: userOutput ? userOutput.fullDimensions : null,\n      encode: userOutput ? userOutput.encode : null,\n      // Param name list for mapping `a`, `b`, `c`, `d`, `e`\n      $vars: ['seriesName', 'name', 'value']\n    };\n  };\n  /**\r\n   * Format label\r\n   * @param dataIndex\r\n   * @param status 'normal' by default\r\n   * @param dataType\r\n   * @param labelDimIndex Only used in some chart that\r\n   *        use formatter in different dimensions, like radar.\r\n   * @param formatter Formatter given outside.\r\n   * @return return null/undefined if no formatter\r\n   */\n  DataFormatMixin.prototype.getFormattedLabel = function (dataIndex, status, dataType, labelDimIndex, formatter, extendParams) {\n    status = status || 'normal';\n    var data = this.getData(dataType);\n    var params = this.getDataParams(dataIndex, dataType);\n    if (extendParams) {\n      params.value = extendParams.interpolatedValue;\n    }\n    if (labelDimIndex != null && zrUtil.isArray(params.value)) {\n      params.value = params.value[labelDimIndex];\n    }\n    if (!formatter) {\n      var itemModel = data.getItemModel(dataIndex);\n      // @ts-ignore\n      formatter = itemModel.get(status === 'normal' ? ['label', 'formatter'] : [status, 'label', 'formatter']);\n    }\n    if (zrUtil.isFunction(formatter)) {\n      params.status = status;\n      params.dimensionIndex = labelDimIndex;\n      return formatter(params);\n    } else if (zrUtil.isString(formatter)) {\n      var str = formatTpl(formatter, params);\n      // Support 'aaa{@[3]}bbb{@product}ccc'.\n      // Do not support '}' in dim name util have to.\n      return str.replace(DIMENSION_LABEL_REG, function (origin, dimStr) {\n        var len = dimStr.length;\n        var dimLoose = dimStr;\n        if (dimLoose.charAt(0) === '[' && dimLoose.charAt(len - 1) === ']') {\n          dimLoose = +dimLoose.slice(1, len - 1); // Also support: '[]' => 0\n          if (process.env.NODE_ENV !== 'production') {\n            if (isNaN(dimLoose)) {\n              error(\"Invalide label formatter: @\" + dimStr + \", only support @[0], @[1], @[2], ...\");\n            }\n          }\n        }\n        var val = retrieveRawValue(data, dataIndex, dimLoose);\n        if (extendParams && zrUtil.isArray(extendParams.interpolatedValue)) {\n          var dimIndex = data.getDimensionIndex(dimLoose);\n          if (dimIndex >= 0) {\n            val = extendParams.interpolatedValue[dimIndex];\n          }\n        }\n        return val != null ? val + '' : '';\n      });\n    }\n  };\n  /**\r\n   * Get raw value in option\r\n   */\n  DataFormatMixin.prototype.getRawValue = function (idx, dataType) {\n    return retrieveRawValue(this.getData(dataType), idx);\n  };\n  /**\r\n   * Should be implemented.\r\n   * @param {number} dataIndex\r\n   * @param {boolean} [multipleSeries=false]\r\n   * @param {string} [dataType]\r\n   */\n  DataFormatMixin.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    // Empty function\n    return;\n  };\n  return DataFormatMixin;\n}();\nexport { DataFormatMixin };\n;\n// PENDING: previously we accept this type when calling `formatTooltip`,\n// but guess little chance has been used outside. Do we need to backward\n// compat it?\n// type TooltipFormatResultLegacyObject = {\n//     // `html` means the markup language text, either in 'html' or 'richText'.\n//     // The name `html` is not appropriate because in 'richText' it is not a HTML\n//     // string. But still support it for backward compatibility.\n//     html: string;\n//     markers: Dictionary<ColorString>;\n// };\n/**\r\n * For backward compat, normalize the return from `formatTooltip`.\r\n */\nexport function normalizeTooltipFormatResult(result) {\n  var markupText;\n  // let markers: Dictionary<ColorString>;\n  var markupFragment;\n  if (zrUtil.isObject(result)) {\n    if (result.type) {\n      markupFragment = result;\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn('The return type of `formatTooltip` is not supported: ' + makePrintable(result));\n      }\n    }\n    // else {\n    //     markupText = (result as TooltipFormatResultLegacyObject).html;\n    //     markers = (result as TooltipFormatResultLegacyObject).markers;\n    //     if (markersExisting) {\n    //         markers = zrUtil.merge(markersExisting, markers);\n    //     }\n    // }\n  } else {\n    markupText = result;\n  }\n  return {\n    text: markupText,\n    // markers: markers || markersExisting,\n    frag: markupFragment\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,SAASC,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,KAAK,EAAEC,aAAa,QAAQ,mBAAmB;AACxD,IAAIC,mBAAmB,GAAG,aAAa;AACvC,IAAIC,eAAe,GAAG,aAAa,YAAY;EAC7C,SAASA,eAAeA,CAAA,EAAG,CAAC;EAC5B;AACF;AACA;EACEA,eAAe,CAACC,SAAS,CAACC,aAAa,GAAG,UAAUC,SAAS,EAAEC,QAAQ,EAAE;IACvE,IAAIC,IAAI,GAAG,IAAI,CAACC,OAAO,CAACF,QAAQ,CAAC;IACjC,IAAIG,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACL,SAAS,EAAEC,QAAQ,CAAC;IACpD,IAAIK,YAAY,GAAGJ,IAAI,CAACK,WAAW,CAACP,SAAS,CAAC;IAC9C,IAAIQ,IAAI,GAAGN,IAAI,CAACO,OAAO,CAACT,SAAS,CAAC;IAClC,IAAIU,OAAO,GAAGR,IAAI,CAACS,cAAc,CAACX,SAAS,CAAC;IAC5C,IAAIY,KAAK,GAAGV,IAAI,CAACW,aAAa,CAACb,SAAS,EAAE,OAAO,CAAC;IAClD,IAAIc,KAAK,GAAGF,KAAK,IAAIA,KAAK,CAACV,IAAI,CAACW,aAAa,CAACb,SAAS,EAAE,UAAU,CAAC,IAAI,MAAM,CAAC;IAC/E,IAAIe,WAAW,GAAGH,KAAK,IAAIA,KAAK,CAACI,MAAM;IACvC,IAAIC,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,IAAIC,QAAQ,GAAGD,QAAQ,KAAK,QAAQ;IACpC,IAAIE,UAAU,GAAGjB,IAAI,CAACiB,UAAU,IAAIjB,IAAI,CAACiB,UAAU,CAACC,GAAG,CAAC,CAAC;IACzD,OAAO;MACLC,aAAa,EAAEJ,QAAQ;MACvBK,gBAAgB,EAAE,IAAI,CAACC,OAAO;MAC9BC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCC,UAAU,EAAEP,QAAQ,GAAG,IAAI,CAACK,OAAO,GAAG,IAAI;MAC1CG,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,QAAQ,EAAET,QAAQ,GAAG,IAAI,CAACU,EAAE,GAAG,IAAI;MACnCC,UAAU,EAAEX,QAAQ,GAAG,IAAI,CAACV,IAAI,GAAG,IAAI;MACvCA,IAAI,EAAEA,IAAI;MACVR,SAAS,EAAEM,YAAY;MACvBJ,IAAI,EAAEQ,OAAO;MACbT,QAAQ,EAAEA,QAAQ;MAClB6B,KAAK,EAAE1B,QAAQ;MACfU,KAAK,EAAEA,KAAK;MACZC,WAAW,EAAEA,WAAW;MACxBgB,cAAc,EAAEZ,UAAU,GAAGA,UAAU,CAACa,cAAc,GAAG,IAAI;MAC7DC,MAAM,EAAEd,UAAU,GAAGA,UAAU,CAACc,MAAM,GAAG,IAAI;MAC7C;MACAC,KAAK,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,OAAO;IACvC,CAAC;EACH,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACErC,eAAe,CAACC,SAAS,CAACqC,iBAAiB,GAAG,UAAUnC,SAAS,EAAEoC,MAAM,EAAEnC,QAAQ,EAAEoC,aAAa,EAAEC,SAAS,EAAEC,YAAY,EAAE;IAC3HH,MAAM,GAAGA,MAAM,IAAI,QAAQ;IAC3B,IAAIlC,IAAI,GAAG,IAAI,CAACC,OAAO,CAACF,QAAQ,CAAC;IACjC,IAAIuC,MAAM,GAAG,IAAI,CAACzC,aAAa,CAACC,SAAS,EAAEC,QAAQ,CAAC;IACpD,IAAIsC,YAAY,EAAE;MAChBC,MAAM,CAACV,KAAK,GAAGS,YAAY,CAACE,iBAAiB;IAC/C;IACA,IAAIJ,aAAa,IAAI,IAAI,IAAI9C,MAAM,CAACmD,OAAO,CAACF,MAAM,CAACV,KAAK,CAAC,EAAE;MACzDU,MAAM,CAACV,KAAK,GAAGU,MAAM,CAACV,KAAK,CAACO,aAAa,CAAC;IAC5C;IACA,IAAI,CAACC,SAAS,EAAE;MACd,IAAIK,SAAS,GAAGzC,IAAI,CAAC0C,YAAY,CAAC5C,SAAS,CAAC;MAC5C;MACAsC,SAAS,GAAGK,SAAS,CAACvB,GAAG,CAACgB,MAAM,KAAK,QAAQ,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,GAAG,CAACA,MAAM,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;IAC1G;IACA,IAAI7C,MAAM,CAACsD,UAAU,CAACP,SAAS,CAAC,EAAE;MAChCE,MAAM,CAACJ,MAAM,GAAGA,MAAM;MACtBI,MAAM,CAACM,cAAc,GAAGT,aAAa;MACrC,OAAOC,SAAS,CAACE,MAAM,CAAC;IAC1B,CAAC,MAAM,IAAIjD,MAAM,CAACwD,QAAQ,CAACT,SAAS,CAAC,EAAE;MACrC,IAAIU,GAAG,GAAGvD,SAAS,CAAC6C,SAAS,EAAEE,MAAM,CAAC;MACtC;MACA;MACA,OAAOQ,GAAG,CAACC,OAAO,CAACrD,mBAAmB,EAAE,UAAUsD,MAAM,EAAEC,MAAM,EAAE;QAChE,IAAIC,GAAG,GAAGD,MAAM,CAACE,MAAM;QACvB,IAAIC,QAAQ,GAAGH,MAAM;QACrB,IAAIG,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAID,QAAQ,CAACC,MAAM,CAACH,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;UAClEE,QAAQ,GAAG,CAACA,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAEJ,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;UACxC,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzC,IAAIC,KAAK,CAACN,QAAQ,CAAC,EAAE;cACnB5D,KAAK,CAAC,6BAA6B,GAAGyD,MAAM,GAAG,sCAAsC,CAAC;YACxF;UACF;QACF;QACA,IAAIU,GAAG,GAAGrE,gBAAgB,CAACU,IAAI,EAAEF,SAAS,EAAEsD,QAAQ,CAAC;QACrD,IAAIf,YAAY,IAAIhD,MAAM,CAACmD,OAAO,CAACH,YAAY,CAACE,iBAAiB,CAAC,EAAE;UAClE,IAAIqB,QAAQ,GAAG5D,IAAI,CAAC6D,iBAAiB,CAACT,QAAQ,CAAC;UAC/C,IAAIQ,QAAQ,IAAI,CAAC,EAAE;YACjBD,GAAG,GAAGtB,YAAY,CAACE,iBAAiB,CAACqB,QAAQ,CAAC;UAChD;QACF;QACA,OAAOD,GAAG,IAAI,IAAI,GAAGA,GAAG,GAAG,EAAE,GAAG,EAAE;MACpC,CAAC,CAAC;IACJ;EACF,CAAC;EACD;AACF;AACA;EACEhE,eAAe,CAACC,SAAS,CAACO,WAAW,GAAG,UAAU2D,GAAG,EAAE/D,QAAQ,EAAE;IAC/D,OAAOT,gBAAgB,CAAC,IAAI,CAACW,OAAO,CAACF,QAAQ,CAAC,EAAE+D,GAAG,CAAC;EACtD,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEnE,eAAe,CAACC,SAAS,CAACmE,aAAa,GAAG,UAAUjE,SAAS,EAAEkE,cAAc,EAAEjE,QAAQ,EAAE;IACvF;IACA;EACF,CAAC;EACD,OAAOJ,eAAe;AACxB,CAAC,CAAC,CAAC;AACH,SAASA,eAAe;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsE,4BAA4BA,CAACC,MAAM,EAAE;EACnD,IAAIC,UAAU;EACd;EACA,IAAIC,cAAc;EAClB,IAAI/E,MAAM,CAACgF,QAAQ,CAACH,MAAM,CAAC,EAAE;IAC3B,IAAIA,MAAM,CAACI,IAAI,EAAE;MACfF,cAAc,GAAGF,MAAM;IACzB,CAAC,MAAM;MACL,IAAIX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCc,OAAO,CAACC,IAAI,CAAC,uDAAuD,GAAG/E,aAAa,CAACyE,MAAM,CAAC,CAAC;MAC/F;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,CAAC,MAAM;IACLC,UAAU,GAAGD,MAAM;EACrB;EACA,OAAO;IACLO,IAAI,EAAEN,UAAU;IAChB;IACAO,IAAI,EAAEN;EACR,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}