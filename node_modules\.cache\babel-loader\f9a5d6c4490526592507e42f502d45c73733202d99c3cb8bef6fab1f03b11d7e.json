{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\Login.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Form, Input, Button, Card, Typography, Checkbox, message, Spin, Alert } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport axios from 'axios';\nimport { login } from '../services/auth';\nimport './Login.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\n\n// 登录页面容器\nconst LoginContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n  position: relative;\n  overflow: hidden;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\n    opacity: 0.15;\n    z-index: 0;\n  }\n`;\n\n// 登录卡片样式\n_c = LoginContainer;\nconst LoginCard = styled(Card)`\n  width: 400px;\n  border-radius: 8px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\n  z-index: 1;\n  \n  .ant-card-body {\n    padding: 40px;\n  }\n`;\n\n// 标题容器\n_c2 = LoginCard;\nconst TitleContainer = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n\n// Logo样式\n_c3 = TitleContainer;\nconst Logo = styled.div`\n  width: 80px;\n  height: 80px;\n  margin: 0 auto 20px;\n  background: #1890ff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 40px;\n  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);\n`;\n\n// 表单样式\n_c4 = Logo;\nconst StyledForm = styled(Form)`\n  .ant-form-item-label {\n    font-weight: 500;\n  }\n  \n  .ant-input-affix-wrapper {\n    padding: 12px;\n    border-radius: 6px;\n  }\n  \n  .ant-btn {\n    height: 45px;\n    border-radius: 6px;\n    font-weight: 500;\n  }\n`;\n\n// 底部文本样式\n_c5 = StyledForm;\nconst FooterText = styled(Text)`\n  display: block;\n  text-align: center;\n  margin-top: 20px;\n  color: #8c8c8c;\n`;\n_c6 = FooterText;\nconst Login = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [loginError, setLoginError] = useState('');\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const onFinish = async values => {\n    setLoading(true);\n    setLoginError('');\n    try {\n      // 发送POST请求到登录API\n      const response = await axios.post('/api/login', {\n        username: values.username,\n        password: values.password\n      });\n\n      // 检查登录是否成功\n      if (response.data.success) {\n        // 保存token和用户信息\n        localStorage.setItem('token', response.data.data.token);\n        localStorage.setItem('user', JSON.stringify(response.data.data.user));\n        localStorage.setItem('isLoggedIn', 'true');\n        message.success('登录成功！');\n        // 跳转到主页面\n        navigate('/real-time-traffic');\n      } else {\n        // 服务器返回失败信息\n        setLoginError(response.data.message || '登录失败');\n        message.error(response.data.message || '登录失败');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('登录错误:', err);\n      const errorMsg = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || '登录失败，请重试！';\n      setLoginError(errorMsg);\n      message.error(errorMsg);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleTestLogin = () => {\n    form.setFieldsValue({\n      username: 'admin',\n      password: 'admin123'\n    });\n    form.submit();\n  };\n  const testLogin = async () => {\n    setLoading(true);\n    try {\n      // 模拟登录成功\n      localStorage.setItem('token', 'test-token-123');\n      localStorage.setItem('user', JSON.stringify({\n        id: 1,\n        username: 'test',\n        name: '测试用户',\n        role: 'admin'\n      }));\n      localStorage.setItem('isLoggedIn', 'true');\n      message.success('测试登录成功！');\n      // 跳转到主页面\n      navigate('/real-time-traffic');\n    } catch (err) {\n      console.error('测试登录错误:', err);\n      message.error('测试登录失败，请重试！');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(LoginContainer, {\n    children: /*#__PURE__*/_jsxDEV(Spin, {\n      spinning: loading,\n      tip: \"\\u767B\\u5F55\\u4E2D...\",\n      children: /*#__PURE__*/_jsxDEV(LoginCard, {\n        children: [/*#__PURE__*/_jsxDEV(TitleContainer, {\n          children: [/*#__PURE__*/_jsxDEV(Logo, {\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-car-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            style: {\n              margin: 0,\n              color: '#1890ff'\n            },\n            children: \"\\u667A\\u80FD\\u7F51\\u8054\\u4EA7\\u5B66\\u7814\\u4E91\\u5E73\\u53F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u767B\\u5F55\\u60A8\\u7684\\u8D26\\u6237\\u4EE5\\u7EE7\\u7EED\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), loginError && /*#__PURE__*/_jsxDEV(Alert, {\n          message: loginError,\n          type: \"error\",\n          showIcon: true,\n          style: {\n            marginBottom: 24\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StyledForm, {\n          form: form,\n          name: \"login\",\n          initialValues: {\n            remember: true\n          },\n          onFinish: onFinish,\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"username\",\n            rules: [{\n              required: true,\n              message: '请输入用户名!'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {\n                style: {\n                  color: '#bfbfbf'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 25\n              }, this),\n              placeholder: \"\\u7528\\u6237\\u540D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            rules: [{\n              required: true,\n              message: '请输入密码!'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input.Password, {\n              prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {\n                style: {\n                  color: '#bfbfbf'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 25\n              }, this),\n              placeholder: \"\\u5BC6\\u7801\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"remember\",\n              valuePropName: \"checked\",\n              noStyle: true,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                children: \"\\u8BB0\\u4F4F\\u6211\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              style: {\n                float: 'right'\n              },\n              href: \"#\",\n              children: \"\\u5FD8\\u8BB0\\u5BC6\\u7801?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              block: true,\n              loading: loading,\n              children: \"\\u767B\\u5F55\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              onClick: handleTestLogin,\n              block: true,\n              children: \"\\u6D4B\\u8BD5\\u767B\\u5F55 (admin/admin123)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FooterText, {\n          children: [\"\\xA9 \", new Date().getFullYear(), \" \\u667A\\u80FD\\u7F51\\u8054\\u4EA7\\u5B66\\u7814\\u4E91\\u5E73\\u53F0 \\u7248\\u6743\\u6240\\u6709\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"VjMgo1tmSMB+Phs5Cr6cFtVJuvE=\", false, function () {\n  return [useNavigate, Form.useForm];\n});\n_c7 = Login;\nexport default Login;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"LoginContainer\");\n$RefreshReg$(_c2, \"LoginCard\");\n$RefreshReg$(_c3, \"TitleContainer\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"StyledForm\");\n$RefreshReg$(_c6, \"FooterText\");\n$RefreshReg$(_c7, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Form", "Input", "<PERSON><PERSON>", "Card", "Typography", "Checkbox", "message", "Spin", "<PERSON><PERSON>", "UserOutlined", "LockOutlined", "useNavigate", "styled", "axios", "login", "jsxDEV", "_jsxDEV", "Title", "Text", "LoginContainer", "div", "_c", "LoginCard", "_c2", "TitleC<PERSON>r", "_c3", "Logo", "_c4", "StyledForm", "_c5", "FooterText", "_c6", "<PERSON><PERSON>", "_s", "loading", "setLoading", "loginError", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "navigate", "form", "useForm", "onFinish", "values", "response", "post", "username", "password", "data", "success", "localStorage", "setItem", "token", "JSON", "stringify", "user", "error", "err", "_err$response", "_err$response$data", "console", "errorMsg", "handleTestLogin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submit", "testLogin", "id", "name", "role", "children", "spinning", "tip", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "level", "style", "margin", "color", "type", "showIcon", "marginBottom", "initialValues", "remember", "size", "<PERSON><PERSON>", "rules", "required", "prefix", "placeholder", "Password", "valuePropName", "noStyle", "float", "href", "htmlType", "block", "onClick", "Date", "getFullYear", "_c7", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/Login.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Form, Input, But<PERSON>, Card, Typography, Checkbox, message, Spin, Alert } from 'antd';\r\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport styled from 'styled-components';\r\nimport axios from 'axios';\r\nimport { login } from '../services/auth';\r\nimport './Login.css';\r\n\r\nconst { Title, Text } = Typography;\r\n\r\n// 登录页面容器\r\nconst LoginContainer = styled.div`\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100vh;\r\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\r\n  position: relative;\r\n  overflow: hidden;\r\n  \r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\r\n    opacity: 0.15;\r\n    z-index: 0;\r\n  }\r\n`;\r\n\r\n// 登录卡片样式\r\nconst LoginCard = styled(Card)`\r\n  width: 400px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\r\n  z-index: 1;\r\n  \r\n  .ant-card-body {\r\n    padding: 40px;\r\n  }\r\n`;\r\n\r\n// 标题容器\r\nconst TitleContainer = styled.div`\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n`;\r\n\r\n// Logo样式\r\nconst Logo = styled.div`\r\n  width: 80px;\r\n  height: 80px;\r\n  margin: 0 auto 20px;\r\n  background: #1890ff;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 40px;\r\n  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);\r\n`;\r\n\r\n// 表单样式\r\nconst StyledForm = styled(Form)`\r\n  .ant-form-item-label {\r\n    font-weight: 500;\r\n  }\r\n  \r\n  .ant-input-affix-wrapper {\r\n    padding: 12px;\r\n    border-radius: 6px;\r\n  }\r\n  \r\n  .ant-btn {\r\n    height: 45px;\r\n    border-radius: 6px;\r\n    font-weight: 500;\r\n  }\r\n`;\r\n\r\n// 底部文本样式\r\nconst FooterText = styled(Text)`\r\n  display: block;\r\n  text-align: center;\r\n  margin-top: 20px;\r\n  color: #8c8c8c;\r\n`;\r\n\r\nconst Login = () => {\r\n  const [loading, setLoading] = useState(false);\r\n  const [loginError, setLoginError] = useState('');\r\n  const navigate = useNavigate();\r\n  const [form] = Form.useForm();\r\n\r\n  const onFinish = async (values) => {\r\n    setLoading(true);\r\n    setLoginError('');\r\n    \r\n    try {\r\n      // 发送POST请求到登录API\r\n      const response = await axios.post('/api/login', {\r\n        username: values.username,\r\n        password: values.password\r\n      });\r\n      \r\n      // 检查登录是否成功\r\n      if (response.data.success) {\r\n        // 保存token和用户信息\r\n        localStorage.setItem('token', response.data.data.token);\r\n        localStorage.setItem('user', JSON.stringify(response.data.data.user));\r\n        localStorage.setItem('isLoggedIn', 'true');\r\n        \r\n        message.success('登录成功！');\r\n        // 跳转到主页面\r\n        navigate('/real-time-traffic');\r\n      } else {\r\n        // 服务器返回失败信息\r\n        setLoginError(response.data.message || '登录失败');\r\n        message.error(response.data.message || '登录失败');\r\n      }\r\n    } catch (err) {\r\n      console.error('登录错误:', err);\r\n      const errorMsg = err.response?.data?.message || '登录失败，请重试！';\r\n      setLoginError(errorMsg);\r\n      message.error(errorMsg);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleTestLogin = () => {\r\n    form.setFieldsValue({\r\n      username: 'admin',\r\n      password: 'admin123'\r\n    });\r\n    form.submit();\r\n  };\r\n\r\n  const testLogin = async () => {\r\n    setLoading(true);\r\n    try {\r\n      // 模拟登录成功\r\n      localStorage.setItem('token', 'test-token-123');\r\n      localStorage.setItem('user', JSON.stringify({ \r\n        id: 1, \r\n        username: 'test', \r\n        name: '测试用户',\r\n        role: 'admin'\r\n      }));\r\n      localStorage.setItem('isLoggedIn', 'true');\r\n      \r\n      message.success('测试登录成功！');\r\n      // 跳转到主页面\r\n      navigate('/real-time-traffic');\r\n    } catch (err) {\r\n      console.error('测试登录错误:', err);\r\n      message.error('测试登录失败，请重试！');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <LoginContainer>\r\n      <Spin spinning={loading} tip=\"登录中...\">\r\n        <LoginCard>\r\n          <TitleContainer>\r\n            <Logo>\r\n              <i className=\"fas fa-car-alt\" />\r\n            </Logo>\r\n            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>智能网联产学研云平台</Title>\r\n            <Text type=\"secondary\">登录您的账户以继续</Text>\r\n          </TitleContainer>\r\n          \r\n          {loginError && (\r\n            <Alert\r\n              message={loginError}\r\n              type=\"error\"\r\n              showIcon\r\n              style={{ marginBottom: 24 }}\r\n            />\r\n          )}\r\n          \r\n          <StyledForm\r\n            form={form}\r\n            name=\"login\"\r\n            initialValues={{ remember: true }}\r\n            onFinish={onFinish}\r\n            size=\"large\"\r\n          >\r\n            <Form.Item\r\n              name=\"username\"\r\n              rules={[{ required: true, message: '请输入用户名!' }]}\r\n            >\r\n              <Input \r\n                prefix={<UserOutlined style={{ color: '#bfbfbf' }} />} \r\n                placeholder=\"用户名\" \r\n              />\r\n            </Form.Item>\r\n\r\n            <Form.Item\r\n              name=\"password\"\r\n              rules={[{ required: true, message: '请输入密码!' }]}\r\n            >\r\n              <Input.Password \r\n                prefix={<LockOutlined style={{ color: '#bfbfbf' }} />} \r\n                placeholder=\"密码\" \r\n              />\r\n            </Form.Item>\r\n\r\n            <Form.Item>\r\n              <Form.Item name=\"remember\" valuePropName=\"checked\" noStyle>\r\n                <Checkbox>记住我</Checkbox>\r\n              </Form.Item>\r\n\r\n              <a style={{ float: 'right' }} href=\"#\">\r\n                忘记密码?\r\n              </a>\r\n            </Form.Item>\r\n\r\n            <Form.Item>\r\n              <Button type=\"primary\" htmlType=\"submit\" block loading={loading}>\r\n                登录\r\n              </Button>\r\n            </Form.Item>\r\n            \r\n            <Form.Item>\r\n              <Button type=\"link\" onClick={handleTestLogin} block>\r\n                测试登录 (admin/admin123)\r\n              </Button>\r\n            </Form.Item>\r\n          </StyledForm>\r\n          \r\n          <FooterText>\r\n            © {new Date().getFullYear()} 智能网联产学研云平台 版权所有\r\n          </FooterText>\r\n        </LoginCard>\r\n      </Spin>\r\n    </LoginContainer>\r\n  );\r\n};\r\n\r\nexport default Login; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAC5F,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGd,UAAU;;AAElC;AACA,MAAMe,cAAc,GAAGP,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GAtBMF,cAAc;AAuBpB,MAAMG,SAAS,GAAGV,MAAM,CAACT,IAAI,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAoB,GAAA,GAXMD,SAAS;AAYf,MAAME,cAAc,GAAGZ,MAAM,CAACQ,GAAG;AACjC;AACA;AACA,CAAC;;AAED;AAAAK,GAAA,GALMD,cAAc;AAMpB,MAAME,IAAI,GAAGd,MAAM,CAACQ,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAO,GAAA,GAdMD,IAAI;AAeV,MAAME,UAAU,GAAGhB,MAAM,CAACZ,IAAI,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAA6B,GAAA,GAjBMD,UAAU;AAkBhB,MAAME,UAAU,GAAGlB,MAAM,CAACM,IAAI,CAAC;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GALID,UAAU;AAOhB,MAAME,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAMuC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC4B,IAAI,CAAC,GAAGvC,IAAI,CAACwC,OAAO,CAAC,CAAC;EAE7B,MAAMC,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjCP,UAAU,CAAC,IAAI,CAAC;IAChBE,aAAa,CAAC,EAAE,CAAC;IAEjB,IAAI;MACF;MACA,MAAMM,QAAQ,GAAG,MAAM9B,KAAK,CAAC+B,IAAI,CAAC,YAAY,EAAE;QAC9CC,QAAQ,EAAEH,MAAM,CAACG,QAAQ;QACzBC,QAAQ,EAAEJ,MAAM,CAACI;MACnB,CAAC,CAAC;;MAEF;MACA,IAAIH,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzB;QACAC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEP,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACI,KAAK,CAAC;QACvDF,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEE,IAAI,CAACC,SAAS,CAACV,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACO,IAAI,CAAC,CAAC;QACrEL,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC;QAE1C5C,OAAO,CAAC0C,OAAO,CAAC,OAAO,CAAC;QACxB;QACAV,QAAQ,CAAC,oBAAoB,CAAC;MAChC,CAAC,MAAM;QACL;QACAD,aAAa,CAACM,QAAQ,CAACI,IAAI,CAACzC,OAAO,IAAI,MAAM,CAAC;QAC9CA,OAAO,CAACiD,KAAK,CAACZ,QAAQ,CAACI,IAAI,CAACzC,OAAO,IAAI,MAAM,CAAC;MAChD;IACF,CAAC,CAAC,OAAOkD,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZC,OAAO,CAACJ,KAAK,CAAC,OAAO,EAAEC,GAAG,CAAC;MAC3B,MAAMI,QAAQ,GAAG,EAAAH,aAAA,GAAAD,GAAG,CAACb,QAAQ,cAAAc,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcV,IAAI,cAAAW,kBAAA,uBAAlBA,kBAAA,CAAoBpD,OAAO,KAAI,WAAW;MAC3D+B,aAAa,CAACuB,QAAQ,CAAC;MACvBtD,OAAO,CAACiD,KAAK,CAACK,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0B,eAAe,GAAGA,CAAA,KAAM;IAC5BtB,IAAI,CAACuB,cAAc,CAAC;MAClBjB,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFP,IAAI,CAACwB,MAAM,CAAC,CAAC;EACf,CAAC;EAED,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B7B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACAc,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC;MAC/CD,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEE,IAAI,CAACC,SAAS,CAAC;QAC1CY,EAAE,EAAE,CAAC;QACLpB,QAAQ,EAAE,MAAM;QAChBqB,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE;MACR,CAAC,CAAC,CAAC;MACHlB,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC;MAE1C5C,OAAO,CAAC0C,OAAO,CAAC,SAAS,CAAC;MAC1B;MACAV,QAAQ,CAAC,oBAAoB,CAAC;IAChC,CAAC,CAAC,OAAOkB,GAAG,EAAE;MACZG,OAAO,CAACJ,KAAK,CAAC,SAAS,EAAEC,GAAG,CAAC;MAC7BlD,OAAO,CAACiD,KAAK,CAAC,aAAa,CAAC;IAC9B,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEnB,OAAA,CAACG,cAAc;IAAAiD,QAAA,eACbpD,OAAA,CAACT,IAAI;MAAC8D,QAAQ,EAAEnC,OAAQ;MAACoC,GAAG,EAAC,uBAAQ;MAAAF,QAAA,eACnCpD,OAAA,CAACM,SAAS;QAAA8C,QAAA,gBACRpD,OAAA,CAACQ,cAAc;UAAA4C,QAAA,gBACbpD,OAAA,CAACU,IAAI;YAAA0C,QAAA,eACHpD,OAAA;cAAGuD,SAAS,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACP3D,OAAA,CAACC,KAAK;YAAC2D,KAAK,EAAE,CAAE;YAACC,KAAK,EAAE;cAAEC,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAX,QAAA,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3E3D,OAAA,CAACE,IAAI;YAAC8D,IAAI,EAAC,WAAW;YAAAZ,QAAA,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,EAEhBvC,UAAU,iBACTpB,OAAA,CAACR,KAAK;UACJF,OAAO,EAAE8B,UAAW;UACpB4C,IAAI,EAAC,OAAO;UACZC,QAAQ;UACRJ,KAAK,EAAE;YAAEK,YAAY,EAAE;UAAG;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACF,eAED3D,OAAA,CAACY,UAAU;UACTW,IAAI,EAAEA,IAAK;UACX2B,IAAI,EAAC,OAAO;UACZiB,aAAa,EAAE;YAAEC,QAAQ,EAAE;UAAK,CAAE;UAClC3C,QAAQ,EAAEA,QAAS;UACnB4C,IAAI,EAAC,OAAO;UAAAjB,QAAA,gBAEZpD,OAAA,CAAChB,IAAI,CAACsF,IAAI;YACRpB,IAAI,EAAC,UAAU;YACfqB,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAElF,OAAO,EAAE;YAAU,CAAC,CAAE;YAAA8D,QAAA,eAEhDpD,OAAA,CAACf,KAAK;cACJwF,MAAM,eAAEzE,OAAA,CAACP,YAAY;gBAACoE,KAAK,EAAE;kBAAEE,KAAK,EAAE;gBAAU;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACtDe,WAAW,EAAC;YAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZ3D,OAAA,CAAChB,IAAI,CAACsF,IAAI;YACRpB,IAAI,EAAC,UAAU;YACfqB,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAElF,OAAO,EAAE;YAAS,CAAC,CAAE;YAAA8D,QAAA,eAE/CpD,OAAA,CAACf,KAAK,CAAC0F,QAAQ;cACbF,MAAM,eAAEzE,OAAA,CAACN,YAAY;gBAACmE,KAAK,EAAE;kBAAEE,KAAK,EAAE;gBAAU;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACtDe,WAAW,EAAC;YAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZ3D,OAAA,CAAChB,IAAI,CAACsF,IAAI;YAAAlB,QAAA,gBACRpD,OAAA,CAAChB,IAAI,CAACsF,IAAI;cAACpB,IAAI,EAAC,UAAU;cAAC0B,aAAa,EAAC,SAAS;cAACC,OAAO;cAAAzB,QAAA,eACxDpD,OAAA,CAACX,QAAQ;gBAAA+D,QAAA,EAAC;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEZ3D,OAAA;cAAG6D,KAAK,EAAE;gBAAEiB,KAAK,EAAE;cAAQ,CAAE;cAACC,IAAI,EAAC,GAAG;cAAA3B,QAAA,EAAC;YAEvC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAEZ3D,OAAA,CAAChB,IAAI,CAACsF,IAAI;YAAAlB,QAAA,eACRpD,OAAA,CAACd,MAAM;cAAC8E,IAAI,EAAC,SAAS;cAACgB,QAAQ,EAAC,QAAQ;cAACC,KAAK;cAAC/D,OAAO,EAAEA,OAAQ;cAAAkC,QAAA,EAAC;YAEjE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZ3D,OAAA,CAAChB,IAAI,CAACsF,IAAI;YAAAlB,QAAA,eACRpD,OAAA,CAACd,MAAM;cAAC8E,IAAI,EAAC,MAAM;cAACkB,OAAO,EAAErC,eAAgB;cAACoC,KAAK;cAAA7B,QAAA,EAAC;YAEpD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEb3D,OAAA,CAACc,UAAU;UAAAsC,QAAA,GAAC,OACR,EAAC,IAAI+B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAC,wFAC9B;QAAA;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAErB,CAAC;AAAC1C,EAAA,CAxJID,KAAK;EAAA,QAGQrB,WAAW,EACbX,IAAI,CAACwC,OAAO;AAAA;AAAA6D,GAAA,GAJvBrE,KAAK;AA0JX,eAAeA,KAAK;AAAC,IAAAX,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAsE,GAAA;AAAAC,YAAA,CAAAjF,EAAA;AAAAiF,YAAA,CAAA/E,GAAA;AAAA+E,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAAzE,GAAA;AAAAyE,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}