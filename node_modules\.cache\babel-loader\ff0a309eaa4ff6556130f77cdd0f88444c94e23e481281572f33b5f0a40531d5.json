{"ast": null, "code": "var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all) __defProp(target, name, {\n    get: all[name],\n    enumerable: true\n  });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from)) if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {\n      get: () => from[key],\n      enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable\n    });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n// If the importer is in node compatibility mode or this is not an ESM\n// file that has been converted to a CommonJS file using a Babel-\n// compatible transform (i.e. \"__esModule\" has not been set), then set\n// \"default\" to the CommonJS \"module.exports\" for node compatibility.\nisNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", {\n  value: mod,\n  enumerable: true\n}) : target, mod));\nvar __toCommonJS = mod => __copyProps(__defProp({}, \"__esModule\", {\n  value: true\n}), mod);\nvar players_exports = {};\n__export(players_exports, {\n  default: () => players_default\n});\nmodule.exports = __toCommonJS(players_exports);\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nvar players_default = [{\n  key: \"youtube\",\n  name: \"YouTube\",\n  canPlay: import_patterns.canPlay.youtube,\n  lazyPlayer: (0, import_utils.lazy)(() => import(/* webpackChunkName: 'reactPlayerYouTube' */\n  \"./YouTube\"))\n}, {\n  key: \"soundcloud\",\n  name: \"SoundCloud\",\n  canPlay: import_patterns.canPlay.soundcloud,\n  lazyPlayer: (0, import_utils.lazy)(() => import(/* webpackChunkName: 'reactPlayerSoundCloud' */\n  \"./SoundCloud\"))\n}, {\n  key: \"vimeo\",\n  name: \"Vimeo\",\n  canPlay: import_patterns.canPlay.vimeo,\n  lazyPlayer: (0, import_utils.lazy)(() => import(/* webpackChunkName: 'reactPlayerVimeo' */\n  \"./Vimeo\"))\n}, {\n  key: \"mux\",\n  name: \"Mux\",\n  canPlay: import_patterns.canPlay.mux,\n  lazyPlayer: (0, import_utils.lazy)(() => import(/* webpackChunkName: 'reactPlayerMux' */\n  \"./Mux\"))\n}, {\n  key: \"facebook\",\n  name: \"Facebook\",\n  canPlay: import_patterns.canPlay.facebook,\n  lazyPlayer: (0, import_utils.lazy)(() => import(/* webpackChunkName: 'reactPlayerFacebook' */\n  \"./Facebook\"))\n}, {\n  key: \"streamable\",\n  name: \"Streamable\",\n  canPlay: import_patterns.canPlay.streamable,\n  lazyPlayer: (0, import_utils.lazy)(() => import(/* webpackChunkName: 'reactPlayerStreamable' */\n  \"./Streamable\"))\n}, {\n  key: \"wistia\",\n  name: \"Wistia\",\n  canPlay: import_patterns.canPlay.wistia,\n  lazyPlayer: (0, import_utils.lazy)(() => import(/* webpackChunkName: 'reactPlayerWistia' */\n  \"./Wistia\"))\n}, {\n  key: \"twitch\",\n  name: \"Twitch\",\n  canPlay: import_patterns.canPlay.twitch,\n  lazyPlayer: (0, import_utils.lazy)(() => import(/* webpackChunkName: 'reactPlayerTwitch' */\n  \"./Twitch\"))\n}, {\n  key: \"dailymotion\",\n  name: \"DailyMotion\",\n  canPlay: import_patterns.canPlay.dailymotion,\n  lazyPlayer: (0, import_utils.lazy)(() => import(/* webpackChunkName: 'reactPlayerDailyMotion' */\n  \"./DailyMotion\"))\n}, {\n  key: \"mixcloud\",\n  name: \"Mixcloud\",\n  canPlay: import_patterns.canPlay.mixcloud,\n  lazyPlayer: (0, import_utils.lazy)(() => import(/* webpackChunkName: 'reactPlayerMixcloud' */\n  \"./Mixcloud\"))\n}, {\n  key: \"vidyard\",\n  name: \"Vidyard\",\n  canPlay: import_patterns.canPlay.vidyard,\n  lazyPlayer: (0, import_utils.lazy)(() => import(/* webpackChunkName: 'reactPlayerVidyard' */\n  \"./Vidyard\"))\n}, {\n  key: \"kaltura\",\n  name: \"Kaltura\",\n  canPlay: import_patterns.canPlay.kaltura,\n  lazyPlayer: (0, import_utils.lazy)(() => import(/* webpackChunkName: 'reactPlayerKaltura' */\n  \"./Kaltura\"))\n}, {\n  key: \"file\",\n  name: \"FilePlayer\",\n  canPlay: import_patterns.canPlay.file,\n  canEnablePIP: url => {\n    return import_patterns.canPlay.file(url) && (document.pictureInPictureEnabled || (0, import_utils.supportsWebKitPresentationMode)()) && !import_patterns.AUDIO_EXTENSIONS.test(url);\n  },\n  lazyPlayer: (0, import_utils.lazy)(() => import(/* webpackChunkName: 'reactPlayerFilePlayer' */\n  \"./FilePlayer\"))\n}];", "map": {"version": 3, "names": ["__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__export", "target", "all", "name", "get", "enumerable", "__copyProps", "to", "from", "except", "desc", "key", "call", "__toESM", "mod", "isNodeMode", "__esModule", "value", "__toCommonJS", "players_exports", "default", "players_default", "module", "exports", "import_utils", "require", "import_patterns", "canPlay", "youtube", "lazyPlayer", "lazy", "soundcloud", "vimeo", "mux", "facebook", "streamable", "wistia", "twitch", "dailymotion", "mixcloud", "vidyard", "kaltura", "file", "canEnablePIP", "url", "document", "pictureInPictureEnabled", "supportsWebKitPresentationMode", "AUDIO_EXTENSIONS", "test"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/react-player/lib/players/index.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar players_exports = {};\n__export(players_exports, {\n  default: () => players_default\n});\nmodule.exports = __toCommonJS(players_exports);\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nvar players_default = [\n  {\n    key: \"youtube\",\n    name: \"YouTube\",\n    canPlay: import_patterns.canPlay.youtube,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerYouTube' */\n      \"./YouTube\"\n    ))\n  },\n  {\n    key: \"soundcloud\",\n    name: \"SoundCloud\",\n    canPlay: import_patterns.canPlay.soundcloud,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerSoundCloud' */\n      \"./SoundCloud\"\n    ))\n  },\n  {\n    key: \"vimeo\",\n    name: \"Vimeo\",\n    canPlay: import_patterns.canPlay.vimeo,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerVimeo' */\n      \"./Vimeo\"\n    ))\n  },\n  {\n    key: \"mux\",\n    name: \"Mux\",\n    canPlay: import_patterns.canPlay.mux,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerMux' */\n      \"./Mux\"\n    ))\n  },\n  {\n    key: \"facebook\",\n    name: \"Facebook\",\n    canPlay: import_patterns.canPlay.facebook,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerFacebook' */\n      \"./Facebook\"\n    ))\n  },\n  {\n    key: \"streamable\",\n    name: \"Streamable\",\n    canPlay: import_patterns.canPlay.streamable,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerStreamable' */\n      \"./Streamable\"\n    ))\n  },\n  {\n    key: \"wistia\",\n    name: \"Wistia\",\n    canPlay: import_patterns.canPlay.wistia,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerWistia' */\n      \"./Wistia\"\n    ))\n  },\n  {\n    key: \"twitch\",\n    name: \"Twitch\",\n    canPlay: import_patterns.canPlay.twitch,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerTwitch' */\n      \"./Twitch\"\n    ))\n  },\n  {\n    key: \"dailymotion\",\n    name: \"DailyMotion\",\n    canPlay: import_patterns.canPlay.dailymotion,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerDailyMotion' */\n      \"./DailyMotion\"\n    ))\n  },\n  {\n    key: \"mixcloud\",\n    name: \"Mixcloud\",\n    canPlay: import_patterns.canPlay.mixcloud,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerMixcloud' */\n      \"./Mixcloud\"\n    ))\n  },\n  {\n    key: \"vidyard\",\n    name: \"Vidyard\",\n    canPlay: import_patterns.canPlay.vidyard,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerVidyard' */\n      \"./Vidyard\"\n    ))\n  },\n  {\n    key: \"kaltura\",\n    name: \"Kaltura\",\n    canPlay: import_patterns.canPlay.kaltura,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerKaltura' */\n      \"./Kaltura\"\n    ))\n  },\n  {\n    key: \"file\",\n    name: \"FilePlayer\",\n    canPlay: import_patterns.canPlay.file,\n    canEnablePIP: (url) => {\n      return import_patterns.canPlay.file(url) && (document.pictureInPictureEnabled || (0, import_utils.supportsWebKitPresentationMode)()) && !import_patterns.AUDIO_EXTENSIONS.test(url);\n    },\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerFilePlayer' */\n      \"./FilePlayer\"\n    ))\n  }\n];\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,MAAM,CAACC,MAAM;AAC5B,IAAIC,SAAS,GAAGF,MAAM,CAACG,cAAc;AACrC,IAAIC,gBAAgB,GAAGJ,MAAM,CAACK,wBAAwB;AACtD,IAAIC,iBAAiB,GAAGN,MAAM,CAACO,mBAAmB;AAClD,IAAIC,YAAY,GAAGR,MAAM,CAACS,cAAc;AACxC,IAAIC,YAAY,GAAGV,MAAM,CAACW,SAAS,CAACC,cAAc;AAClD,IAAIC,QAAQ,GAAGA,CAACC,MAAM,EAAEC,GAAG,KAAK;EAC9B,KAAK,IAAIC,IAAI,IAAID,GAAG,EAClBb,SAAS,CAACY,MAAM,EAAEE,IAAI,EAAE;IAAEC,GAAG,EAAEF,GAAG,CAACC,IAAI,CAAC;IAAEE,UAAU,EAAE;EAAK,CAAC,CAAC;AACjE,CAAC;AACD,IAAIC,WAAW,GAAGA,CAACC,EAAE,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,KAAK;EAC5C,IAAIF,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IAClE,KAAK,IAAIG,GAAG,IAAIlB,iBAAiB,CAACe,IAAI,CAAC,EACrC,IAAI,CAACX,YAAY,CAACe,IAAI,CAACL,EAAE,EAAEI,GAAG,CAAC,IAAIA,GAAG,KAAKF,MAAM,EAC/CpB,SAAS,CAACkB,EAAE,EAAEI,GAAG,EAAE;MAAEP,GAAG,EAAEA,CAAA,KAAMI,IAAI,CAACG,GAAG,CAAC;MAAEN,UAAU,EAAE,EAAEK,IAAI,GAAGnB,gBAAgB,CAACiB,IAAI,EAAEG,GAAG,CAAC,CAAC,IAAID,IAAI,CAACL;IAAW,CAAC,CAAC;EACxH;EACA,OAAOE,EAAE;AACX,CAAC;AACD,IAAIM,OAAO,GAAGA,CAACC,GAAG,EAAEC,UAAU,EAAEd,MAAM,MAAMA,MAAM,GAAGa,GAAG,IAAI,IAAI,GAAG5B,QAAQ,CAACS,YAAY,CAACmB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAER,WAAW;AAC9G;AACA;AACA;AACA;AACAS,UAAU,IAAI,CAACD,GAAG,IAAI,CAACA,GAAG,CAACE,UAAU,GAAG3B,SAAS,CAACY,MAAM,EAAE,SAAS,EAAE;EAAEgB,KAAK,EAAEH,GAAG;EAAET,UAAU,EAAE;AAAK,CAAC,CAAC,GAAGJ,MAAM,EAC/Ga,GACF,CAAC,CAAC;AACF,IAAII,YAAY,GAAIJ,GAAG,IAAKR,WAAW,CAACjB,SAAS,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE;EAAE4B,KAAK,EAAE;AAAK,CAAC,CAAC,EAAEH,GAAG,CAAC;AAC1F,IAAIK,eAAe,GAAG,CAAC,CAAC;AACxBnB,QAAQ,CAACmB,eAAe,EAAE;EACxBC,OAAO,EAAEA,CAAA,KAAMC;AACjB,CAAC,CAAC;AACFC,MAAM,CAACC,OAAO,GAAGL,YAAY,CAACC,eAAe,CAAC;AAC9C,IAAIK,YAAY,GAAGC,OAAO,CAAC,UAAU,CAAC;AACtC,IAAIC,eAAe,GAAGD,OAAO,CAAC,aAAa,CAAC;AAC5C,IAAIJ,eAAe,GAAG,CACpB;EACEV,GAAG,EAAE,SAAS;EACdR,IAAI,EAAE,SAAS;EACfwB,OAAO,EAAED,eAAe,CAACC,OAAO,CAACC,OAAO;EACxCC,UAAU,EAAE,CAAC,CAAC,EAAEL,YAAY,CAACM,IAAI,EAAE,MAAM,MAAM,CAC7C;EACA,WACF,CAAC;AACH,CAAC,EACD;EACEnB,GAAG,EAAE,YAAY;EACjBR,IAAI,EAAE,YAAY;EAClBwB,OAAO,EAAED,eAAe,CAACC,OAAO,CAACI,UAAU;EAC3CF,UAAU,EAAE,CAAC,CAAC,EAAEL,YAAY,CAACM,IAAI,EAAE,MAAM,MAAM,CAC7C;EACA,cACF,CAAC;AACH,CAAC,EACD;EACEnB,GAAG,EAAE,OAAO;EACZR,IAAI,EAAE,OAAO;EACbwB,OAAO,EAAED,eAAe,CAACC,OAAO,CAACK,KAAK;EACtCH,UAAU,EAAE,CAAC,CAAC,EAAEL,YAAY,CAACM,IAAI,EAAE,MAAM,MAAM,CAC7C;EACA,SACF,CAAC;AACH,CAAC,EACD;EACEnB,GAAG,EAAE,KAAK;EACVR,IAAI,EAAE,KAAK;EACXwB,OAAO,EAAED,eAAe,CAACC,OAAO,CAACM,GAAG;EACpCJ,UAAU,EAAE,CAAC,CAAC,EAAEL,YAAY,CAACM,IAAI,EAAE,MAAM,MAAM,CAC7C;EACA,OACF,CAAC;AACH,CAAC,EACD;EACEnB,GAAG,EAAE,UAAU;EACfR,IAAI,EAAE,UAAU;EAChBwB,OAAO,EAAED,eAAe,CAACC,OAAO,CAACO,QAAQ;EACzCL,UAAU,EAAE,CAAC,CAAC,EAAEL,YAAY,CAACM,IAAI,EAAE,MAAM,MAAM,CAC7C;EACA,YACF,CAAC;AACH,CAAC,EACD;EACEnB,GAAG,EAAE,YAAY;EACjBR,IAAI,EAAE,YAAY;EAClBwB,OAAO,EAAED,eAAe,CAACC,OAAO,CAACQ,UAAU;EAC3CN,UAAU,EAAE,CAAC,CAAC,EAAEL,YAAY,CAACM,IAAI,EAAE,MAAM,MAAM,CAC7C;EACA,cACF,CAAC;AACH,CAAC,EACD;EACEnB,GAAG,EAAE,QAAQ;EACbR,IAAI,EAAE,QAAQ;EACdwB,OAAO,EAAED,eAAe,CAACC,OAAO,CAACS,MAAM;EACvCP,UAAU,EAAE,CAAC,CAAC,EAAEL,YAAY,CAACM,IAAI,EAAE,MAAM,MAAM,CAC7C;EACA,UACF,CAAC;AACH,CAAC,EACD;EACEnB,GAAG,EAAE,QAAQ;EACbR,IAAI,EAAE,QAAQ;EACdwB,OAAO,EAAED,eAAe,CAACC,OAAO,CAACU,MAAM;EACvCR,UAAU,EAAE,CAAC,CAAC,EAAEL,YAAY,CAACM,IAAI,EAAE,MAAM,MAAM,CAC7C;EACA,UACF,CAAC;AACH,CAAC,EACD;EACEnB,GAAG,EAAE,aAAa;EAClBR,IAAI,EAAE,aAAa;EACnBwB,OAAO,EAAED,eAAe,CAACC,OAAO,CAACW,WAAW;EAC5CT,UAAU,EAAE,CAAC,CAAC,EAAEL,YAAY,CAACM,IAAI,EAAE,MAAM,MAAM,CAC7C;EACA,eACF,CAAC;AACH,CAAC,EACD;EACEnB,GAAG,EAAE,UAAU;EACfR,IAAI,EAAE,UAAU;EAChBwB,OAAO,EAAED,eAAe,CAACC,OAAO,CAACY,QAAQ;EACzCV,UAAU,EAAE,CAAC,CAAC,EAAEL,YAAY,CAACM,IAAI,EAAE,MAAM,MAAM,CAC7C;EACA,YACF,CAAC;AACH,CAAC,EACD;EACEnB,GAAG,EAAE,SAAS;EACdR,IAAI,EAAE,SAAS;EACfwB,OAAO,EAAED,eAAe,CAACC,OAAO,CAACa,OAAO;EACxCX,UAAU,EAAE,CAAC,CAAC,EAAEL,YAAY,CAACM,IAAI,EAAE,MAAM,MAAM,CAC7C;EACA,WACF,CAAC;AACH,CAAC,EACD;EACEnB,GAAG,EAAE,SAAS;EACdR,IAAI,EAAE,SAAS;EACfwB,OAAO,EAAED,eAAe,CAACC,OAAO,CAACc,OAAO;EACxCZ,UAAU,EAAE,CAAC,CAAC,EAAEL,YAAY,CAACM,IAAI,EAAE,MAAM,MAAM,CAC7C;EACA,WACF,CAAC;AACH,CAAC,EACD;EACEnB,GAAG,EAAE,MAAM;EACXR,IAAI,EAAE,YAAY;EAClBwB,OAAO,EAAED,eAAe,CAACC,OAAO,CAACe,IAAI;EACrCC,YAAY,EAAGC,GAAG,IAAK;IACrB,OAAOlB,eAAe,CAACC,OAAO,CAACe,IAAI,CAACE,GAAG,CAAC,KAAKC,QAAQ,CAACC,uBAAuB,IAAI,CAAC,CAAC,EAAEtB,YAAY,CAACuB,8BAA8B,EAAE,CAAC,CAAC,IAAI,CAACrB,eAAe,CAACsB,gBAAgB,CAACC,IAAI,CAACL,GAAG,CAAC;EACrL,CAAC;EACDf,UAAU,EAAE,CAAC,CAAC,EAAEL,YAAY,CAACM,IAAI,EAAE,MAAM,MAAM,CAC7C;EACA,cACF,CAAC;AACH,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}