{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport * as THREE from 'three';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';\nimport mqtt from 'mqtt';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CampusModel = () => {\n  _s();\n  // 声明组件级别的变量\n  const containerRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mqttClientRef = useRef(null);\n  const vehicleModels = useRef(new Map());\n  const trafficLightsMap = useRef(new Map());\n  const trafficLightStates = useRef(new Map());\n  const sceneRef = useRef(null);\n  const cameraRef = useRef(null);\n  const rendererRef = useRef(null);\n  const controlsRef = useRef(null);\n  const isSceneInitializedRef = useRef(false);\n\n  // 初始化场景\n  const initScene = () => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    sceneRef.current = new THREE.Scene();\n    sceneRef.current.background = new THREE.Color(0x87CEEB);\n\n    // 创建相机\n    cameraRef.current = new THREE.PerspectiveCamera(75, containerRef.current.clientWidth / containerRef.current.clientHeight, 0.1, 1000);\n    cameraRef.current.position.set(0, 50, 100);\n    cameraRef.current.lookAt(0, 0, 0);\n\n    // 创建渲染器\n    rendererRef.current = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    rendererRef.current.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);\n    rendererRef.current.shadowMap.enabled = true;\n    containerRef.current.appendChild(rendererRef.current.domElement);\n\n    // 创建控制器\n    controlsRef.current = new OrbitControls(cameraRef.current, rendererRef.current.domElement);\n    controlsRef.current.enableDamping = true;\n    controlsRef.current.dampingFactor = 0.05;\n    controlsRef.current.screenSpacePanning = false;\n    controlsRef.current.minDistance = 10;\n    controlsRef.current.maxDistance = 500;\n    controlsRef.current.maxPolarAngle = Math.PI / 2;\n\n    // 添加环境光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);\n    sceneRef.current.add(ambientLight);\n\n    // 添加平行光\n    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight.position.set(100, 100, 50);\n    directionalLight.castShadow = true;\n    directionalLight.shadow.mapSize.width = 2048;\n    directionalLight.shadow.mapSize.height = 2048;\n    sceneRef.current.add(directionalLight);\n\n    // 初始化完成标志\n    isSceneInitializedRef.current = true;\n\n    // 开始动画循环\n    animate();\n  };\n\n  // 添加模型到场景\n  const addModelToScene = (model, position) => {\n    if (!isSceneInitializedRef.current || !sceneRef.current) {\n      console.warn('场景未初始化，无法添加模型');\n      return;\n    }\n    sceneRef.current.add(model);\n    model.position.copy(position);\n  };\n\n  // 动画循环\n  const animate = () => {\n    if (!isSceneInitializedRef.current) return;\n    animationFrameRef.current = requestAnimationFrame(animate);\n    if (controlsRef.current) {\n      controlsRef.current.update();\n    }\n    if (rendererRef.current && sceneRef.current && cameraRef.current) {\n      rendererRef.current.render(sceneRef.current, cameraRef.current);\n    }\n  };\n\n  // 处理窗口大小变化\n  const handleResize = () => {\n    if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;\n    cameraRef.current.aspect = containerRef.current.clientWidth / containerRef.current.clientHeight;\n    cameraRef.current.updateProjectionMatrix();\n    rendererRef.current.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);\n  };\n\n  // 在组件挂载时初始化场景\n  useEffect(() => {\n    initScene();\n    window.addEventListener('resize', handleResize);\n    return () => {\n      // 清理动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n      }\n\n      // 清理事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 清理渲染器\n      if (rendererRef.current && containerRef.current) {\n        containerRef.current.removeChild(rendererRef.current.domElement);\n        rendererRef.current.dispose();\n        rendererRef.current.forceContextLoss();\n        rendererRef.current.domElement = null;\n      }\n\n      // 清理场景\n      if (sceneRef.current) {\n        sceneRef.current.traverse(object => {\n          if (object.geometry) {\n            object.geometry.dispose();\n          }\n          if (object.material) {\n            if (Array.isArray(object.material)) {\n              object.material.forEach(material => material.dispose());\n            } else {\n              object.material.dispose();\n            }\n          }\n        });\n        sceneRef.current.clear();\n      }\n\n      // 清理控制器\n      if (controlsRef.current) {\n        controlsRef.current.dispose();\n      }\n\n      // 清理所有引用\n      sceneRef.current = null;\n      cameraRef.current = null;\n      rendererRef.current = null;\n      controlsRef.current = null;\n      isSceneInitializedRef.current = false;\n      vehicleModels.current.clear();\n      trafficLightsMap.current.clear();\n      trafficLightStates.current.clear();\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: containerRef,\n    style: {\n      width: '100%',\n      height: '100%'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 5\n  }, this);\n};\n_s(CampusModel, \"aWt4bbmxRX+VxeKBXx6Trhj+8iM=\");\n_c = CampusModel;\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "THREE", "OrbitControls", "GLTFLoader", "DRACOLoader", "mqtt", "jsxDEV", "_jsxDEV", "CampusModel", "_s", "containerRef", "animationFrameRef", "mqttClientRef", "vehicleModels", "Map", "trafficLightsMap", "trafficLightStates", "sceneRef", "cameraRef", "rendererRef", "controlsRef", "isSceneInitializedRef", "initScene", "current", "Scene", "background", "Color", "PerspectiveCamera", "clientWidth", "clientHeight", "position", "set", "lookAt", "WebGLRenderer", "antialias", "setSize", "shadowMap", "enabled", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "enableDamping", "dampingFactor", "screenSpacePanning", "minDistance", "maxDistance", "maxPolarAngle", "Math", "PI", "ambientLight", "AmbientLight", "add", "directionalLight", "DirectionalLight", "<PERSON><PERSON><PERSON><PERSON>", "shadow", "mapSize", "width", "height", "animate", "addModelToScene", "model", "console", "warn", "copy", "requestAnimationFrame", "update", "render", "handleResize", "aspect", "updateProjectionMatrix", "window", "addEventListener", "cancelAnimationFrame", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "forceContextLoss", "traverse", "object", "geometry", "material", "Array", "isArray", "for<PERSON>ach", "clear", "ref", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport * as THREE from 'three';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';\nimport mqtt from 'mqtt';\n\nconst CampusModel = () => {\n  // 声明组件级别的变量\n  const containerRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mqttClientRef = useRef(null);\n  const vehicleModels = useRef(new Map());\n  const trafficLightsMap = useRef(new Map());\n  const trafficLightStates = useRef(new Map());\n  const sceneRef = useRef(null);\n  const cameraRef = useRef(null);\n  const rendererRef = useRef(null);\n  const controlsRef = useRef(null);\n  const isSceneInitializedRef = useRef(false);\n\n  // 初始化场景\n  const initScene = () => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    sceneRef.current = new THREE.Scene();\n    sceneRef.current.background = new THREE.Color(0x87CEEB);\n\n    // 创建相机\n    cameraRef.current = new THREE.PerspectiveCamera(\n      75,\n      containerRef.current.clientWidth / containerRef.current.clientHeight,\n      0.1,\n      1000\n    );\n    cameraRef.current.position.set(0, 50, 100);\n    cameraRef.current.lookAt(0, 0, 0);\n\n    // 创建渲染器\n    rendererRef.current = new THREE.WebGLRenderer({ antialias: true });\n    rendererRef.current.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);\n    rendererRef.current.shadowMap.enabled = true;\n    containerRef.current.appendChild(rendererRef.current.domElement);\n\n    // 创建控制器\n    controlsRef.current = new OrbitControls(cameraRef.current, rendererRef.current.domElement);\n    controlsRef.current.enableDamping = true;\n    controlsRef.current.dampingFactor = 0.05;\n    controlsRef.current.screenSpacePanning = false;\n    controlsRef.current.minDistance = 10;\n    controlsRef.current.maxDistance = 500;\n    controlsRef.current.maxPolarAngle = Math.PI / 2;\n\n    // 添加环境光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);\n    sceneRef.current.add(ambientLight);\n\n    // 添加平行光\n    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight.position.set(100, 100, 50);\n    directionalLight.castShadow = true;\n    directionalLight.shadow.mapSize.width = 2048;\n    directionalLight.shadow.mapSize.height = 2048;\n    sceneRef.current.add(directionalLight);\n\n    // 初始化完成标志\n    isSceneInitializedRef.current = true;\n\n    // 开始动画循环\n    animate();\n  };\n\n  // 添加模型到场景\n  const addModelToScene = (model, position) => {\n    if (!isSceneInitializedRef.current || !sceneRef.current) {\n      console.warn('场景未初始化，无法添加模型');\n      return;\n    }\n    sceneRef.current.add(model);\n    model.position.copy(position);\n  };\n\n  // 动画循环\n  const animate = () => {\n    if (!isSceneInitializedRef.current) return;\n\n    animationFrameRef.current = requestAnimationFrame(animate);\n\n    if (controlsRef.current) {\n      controlsRef.current.update();\n    }\n\n    if (rendererRef.current && sceneRef.current && cameraRef.current) {\n      rendererRef.current.render(sceneRef.current, cameraRef.current);\n    }\n  };\n\n  // 处理窗口大小变化\n  const handleResize = () => {\n    if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;\n\n    cameraRef.current.aspect = containerRef.current.clientWidth / containerRef.current.clientHeight;\n    cameraRef.current.updateProjectionMatrix();\n    rendererRef.current.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);\n  };\n\n  // 在组件挂载时初始化场景\n  useEffect(() => {\n    initScene();\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      // 清理动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n      }\n\n      // 清理事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 清理渲染器\n      if (rendererRef.current && containerRef.current) {\n        containerRef.current.removeChild(rendererRef.current.domElement);\n        rendererRef.current.dispose();\n        rendererRef.current.forceContextLoss();\n        rendererRef.current.domElement = null;\n      }\n\n      // 清理场景\n      if (sceneRef.current) {\n        sceneRef.current.traverse((object) => {\n          if (object.geometry) {\n            object.geometry.dispose();\n          }\n          if (object.material) {\n            if (Array.isArray(object.material)) {\n              object.material.forEach(material => material.dispose());\n            } else {\n              object.material.dispose();\n            }\n          }\n        });\n        sceneRef.current.clear();\n      }\n\n      // 清理控制器\n      if (controlsRef.current) {\n        controlsRef.current.dispose();\n      }\n\n      // 清理所有引用\n      sceneRef.current = null;\n      cameraRef.current = null;\n      rendererRef.current = null;\n      controlsRef.current = null;\n      isSceneInitializedRef.current = false;\n      vehicleModels.current.clear();\n      trafficLightsMap.current.clear();\n      trafficLightStates.current.clear();\n    };\n  }, []);\n\n  return (\n    <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n  );\n};\n\nexport default CampusModel; \n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,WAAW,QAAQ,wCAAwC;AACpE,OAAOC,IAAI,MAAM,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB;EACA,MAAMC,YAAY,GAAGV,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMW,iBAAiB,GAAGX,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMY,aAAa,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMa,aAAa,GAAGb,MAAM,CAAC,IAAIc,GAAG,CAAC,CAAC,CAAC;EACvC,MAAMC,gBAAgB,GAAGf,MAAM,CAAC,IAAIc,GAAG,CAAC,CAAC,CAAC;EAC1C,MAAME,kBAAkB,GAAGhB,MAAM,CAAC,IAAIc,GAAG,CAAC,CAAC,CAAC;EAC5C,MAAMG,QAAQ,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMkB,SAAS,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMmB,WAAW,GAAGnB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMoB,WAAW,GAAGpB,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMqB,qBAAqB,GAAGrB,MAAM,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAMsB,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI,CAACZ,YAAY,CAACa,OAAO,EAAE;;IAE3B;IACAN,QAAQ,CAACM,OAAO,GAAG,IAAItB,KAAK,CAACuB,KAAK,CAAC,CAAC;IACpCP,QAAQ,CAACM,OAAO,CAACE,UAAU,GAAG,IAAIxB,KAAK,CAACyB,KAAK,CAAC,QAAQ,CAAC;;IAEvD;IACAR,SAAS,CAACK,OAAO,GAAG,IAAItB,KAAK,CAAC0B,iBAAiB,CAC7C,EAAE,EACFjB,YAAY,CAACa,OAAO,CAACK,WAAW,GAAGlB,YAAY,CAACa,OAAO,CAACM,YAAY,EACpE,GAAG,EACH,IACF,CAAC;IACDX,SAAS,CAACK,OAAO,CAACO,QAAQ,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC;IAC1Cb,SAAS,CAACK,OAAO,CAACS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;IAEjC;IACAb,WAAW,CAACI,OAAO,GAAG,IAAItB,KAAK,CAACgC,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAClEf,WAAW,CAACI,OAAO,CAACY,OAAO,CAACzB,YAAY,CAACa,OAAO,CAACK,WAAW,EAAElB,YAAY,CAACa,OAAO,CAACM,YAAY,CAAC;IAChGV,WAAW,CAACI,OAAO,CAACa,SAAS,CAACC,OAAO,GAAG,IAAI;IAC5C3B,YAAY,CAACa,OAAO,CAACe,WAAW,CAACnB,WAAW,CAACI,OAAO,CAACgB,UAAU,CAAC;;IAEhE;IACAnB,WAAW,CAACG,OAAO,GAAG,IAAIrB,aAAa,CAACgB,SAAS,CAACK,OAAO,EAAEJ,WAAW,CAACI,OAAO,CAACgB,UAAU,CAAC;IAC1FnB,WAAW,CAACG,OAAO,CAACiB,aAAa,GAAG,IAAI;IACxCpB,WAAW,CAACG,OAAO,CAACkB,aAAa,GAAG,IAAI;IACxCrB,WAAW,CAACG,OAAO,CAACmB,kBAAkB,GAAG,KAAK;IAC9CtB,WAAW,CAACG,OAAO,CAACoB,WAAW,GAAG,EAAE;IACpCvB,WAAW,CAACG,OAAO,CAACqB,WAAW,GAAG,GAAG;IACrCxB,WAAW,CAACG,OAAO,CAACsB,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;;IAE/C;IACA,MAAMC,YAAY,GAAG,IAAI/C,KAAK,CAACgD,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC1DhC,QAAQ,CAACM,OAAO,CAAC2B,GAAG,CAACF,YAAY,CAAC;;IAElC;IACA,MAAMG,gBAAgB,GAAG,IAAIlD,KAAK,CAACmD,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IAClED,gBAAgB,CAACrB,QAAQ,CAACC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC;IAC3CoB,gBAAgB,CAACE,UAAU,GAAG,IAAI;IAClCF,gBAAgB,CAACG,MAAM,CAACC,OAAO,CAACC,KAAK,GAAG,IAAI;IAC5CL,gBAAgB,CAACG,MAAM,CAACC,OAAO,CAACE,MAAM,GAAG,IAAI;IAC7CxC,QAAQ,CAACM,OAAO,CAAC2B,GAAG,CAACC,gBAAgB,CAAC;;IAEtC;IACA9B,qBAAqB,CAACE,OAAO,GAAG,IAAI;;IAEpC;IACAmC,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACC,KAAK,EAAE9B,QAAQ,KAAK;IAC3C,IAAI,CAACT,qBAAqB,CAACE,OAAO,IAAI,CAACN,QAAQ,CAACM,OAAO,EAAE;MACvDsC,OAAO,CAACC,IAAI,CAAC,eAAe,CAAC;MAC7B;IACF;IACA7C,QAAQ,CAACM,OAAO,CAAC2B,GAAG,CAACU,KAAK,CAAC;IAC3BA,KAAK,CAAC9B,QAAQ,CAACiC,IAAI,CAACjC,QAAQ,CAAC;EAC/B,CAAC;;EAED;EACA,MAAM4B,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAI,CAACrC,qBAAqB,CAACE,OAAO,EAAE;IAEpCZ,iBAAiB,CAACY,OAAO,GAAGyC,qBAAqB,CAACN,OAAO,CAAC;IAE1D,IAAItC,WAAW,CAACG,OAAO,EAAE;MACvBH,WAAW,CAACG,OAAO,CAAC0C,MAAM,CAAC,CAAC;IAC9B;IAEA,IAAI9C,WAAW,CAACI,OAAO,IAAIN,QAAQ,CAACM,OAAO,IAAIL,SAAS,CAACK,OAAO,EAAE;MAChEJ,WAAW,CAACI,OAAO,CAAC2C,MAAM,CAACjD,QAAQ,CAACM,OAAO,EAAEL,SAAS,CAACK,OAAO,CAAC;IACjE;EACF,CAAC;;EAED;EACA,MAAM4C,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACzD,YAAY,CAACa,OAAO,IAAI,CAACL,SAAS,CAACK,OAAO,IAAI,CAACJ,WAAW,CAACI,OAAO,EAAE;IAEzEL,SAAS,CAACK,OAAO,CAAC6C,MAAM,GAAG1D,YAAY,CAACa,OAAO,CAACK,WAAW,GAAGlB,YAAY,CAACa,OAAO,CAACM,YAAY;IAC/FX,SAAS,CAACK,OAAO,CAAC8C,sBAAsB,CAAC,CAAC;IAC1ClD,WAAW,CAACI,OAAO,CAACY,OAAO,CAACzB,YAAY,CAACa,OAAO,CAACK,WAAW,EAAElB,YAAY,CAACa,OAAO,CAACM,YAAY,CAAC;EAClG,CAAC;;EAED;EACA9B,SAAS,CAAC,MAAM;IACduB,SAAS,CAAC,CAAC;IACXgD,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IAE/C,OAAO,MAAM;MACX;MACA,IAAIxD,iBAAiB,CAACY,OAAO,EAAE;QAC7BiD,oBAAoB,CAAC7D,iBAAiB,CAACY,OAAO,CAAC;MACjD;;MAEA;MACA+C,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;;MAElD;MACA,IAAIhD,WAAW,CAACI,OAAO,IAAIb,YAAY,CAACa,OAAO,EAAE;QAC/Cb,YAAY,CAACa,OAAO,CAACmD,WAAW,CAACvD,WAAW,CAACI,OAAO,CAACgB,UAAU,CAAC;QAChEpB,WAAW,CAACI,OAAO,CAACoD,OAAO,CAAC,CAAC;QAC7BxD,WAAW,CAACI,OAAO,CAACqD,gBAAgB,CAAC,CAAC;QACtCzD,WAAW,CAACI,OAAO,CAACgB,UAAU,GAAG,IAAI;MACvC;;MAEA;MACA,IAAItB,QAAQ,CAACM,OAAO,EAAE;QACpBN,QAAQ,CAACM,OAAO,CAACsD,QAAQ,CAAEC,MAAM,IAAK;UACpC,IAAIA,MAAM,CAACC,QAAQ,EAAE;YACnBD,MAAM,CAACC,QAAQ,CAACJ,OAAO,CAAC,CAAC;UAC3B;UACA,IAAIG,MAAM,CAACE,QAAQ,EAAE;YACnB,IAAIC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAACE,QAAQ,CAAC,EAAE;cAClCF,MAAM,CAACE,QAAQ,CAACG,OAAO,CAACH,QAAQ,IAAIA,QAAQ,CAACL,OAAO,CAAC,CAAC,CAAC;YACzD,CAAC,MAAM;cACLG,MAAM,CAACE,QAAQ,CAACL,OAAO,CAAC,CAAC;YAC3B;UACF;QACF,CAAC,CAAC;QACF1D,QAAQ,CAACM,OAAO,CAAC6D,KAAK,CAAC,CAAC;MAC1B;;MAEA;MACA,IAAIhE,WAAW,CAACG,OAAO,EAAE;QACvBH,WAAW,CAACG,OAAO,CAACoD,OAAO,CAAC,CAAC;MAC/B;;MAEA;MACA1D,QAAQ,CAACM,OAAO,GAAG,IAAI;MACvBL,SAAS,CAACK,OAAO,GAAG,IAAI;MACxBJ,WAAW,CAACI,OAAO,GAAG,IAAI;MAC1BH,WAAW,CAACG,OAAO,GAAG,IAAI;MAC1BF,qBAAqB,CAACE,OAAO,GAAG,KAAK;MACrCV,aAAa,CAACU,OAAO,CAAC6D,KAAK,CAAC,CAAC;MAC7BrE,gBAAgB,CAACQ,OAAO,CAAC6D,KAAK,CAAC,CAAC;MAChCpE,kBAAkB,CAACO,OAAO,CAAC6D,KAAK,CAAC,CAAC;IACpC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE7E,OAAA;IAAK8E,GAAG,EAAE3E,YAAa;IAAC4E,KAAK,EAAE;MAAE9B,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO;EAAE;IAAA8B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAExE,CAAC;AAACjF,EAAA,CA/JID,WAAW;AAAAmF,EAAA,GAAXnF,WAAW;AAiKjB,eAAeA,WAAW;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}