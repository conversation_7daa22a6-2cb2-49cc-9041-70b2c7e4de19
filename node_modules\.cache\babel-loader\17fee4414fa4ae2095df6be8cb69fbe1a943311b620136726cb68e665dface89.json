{"ast": null, "code": "import React from 'react';\nexport function isFragment(child) {\n  return child && /*#__PURE__*/React.isValidElement(child) && child.type === React.Fragment;\n}\nexport const replaceElement = (element, replacement, props) => {\n  if (! /*#__PURE__*/React.isValidElement(element)) {\n    return replacement;\n  }\n  return /*#__PURE__*/React.cloneElement(element, typeof props === 'function' ? props(element.props || {}) : props);\n};\nexport function cloneElement(element, props) {\n  return replaceElement(element, element, props);\n}", "map": {"version": 3, "names": ["React", "isFragment", "child", "isValidElement", "type", "Fragment", "replaceElement", "element", "replacement", "props", "cloneElement"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/_util/reactNode.js"], "sourcesContent": ["import React from 'react';\nexport function isFragment(child) {\n  return child && /*#__PURE__*/React.isValidElement(child) && child.type === React.Fragment;\n}\nexport const replaceElement = (element, replacement, props) => {\n  if (! /*#__PURE__*/React.isValidElement(element)) {\n    return replacement;\n  }\n  return /*#__PURE__*/React.cloneElement(element, typeof props === 'function' ? props(element.props || {}) : props);\n};\nexport function cloneElement(element, props) {\n  return replaceElement(element, element, props);\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAOA,KAAK,IAAI,aAAaF,KAAK,CAACG,cAAc,CAACD,KAAK,CAAC,IAAIA,KAAK,CAACE,IAAI,KAAKJ,KAAK,CAACK,QAAQ;AAC3F;AACA,OAAO,MAAMC,cAAc,GAAGA,CAACC,OAAO,EAAEC,WAAW,EAAEC,KAAK,KAAK;EAC7D,IAAI,EAAE,aAAaT,KAAK,CAACG,cAAc,CAACI,OAAO,CAAC,EAAE;IAChD,OAAOC,WAAW;EACpB;EACA,OAAO,aAAaR,KAAK,CAACU,YAAY,CAACH,OAAO,EAAE,OAAOE,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACF,OAAO,CAACE,KAAK,IAAI,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC;AACnH,CAAC;AACD,OAAO,SAASC,YAAYA,CAACH,OAAO,EAAEE,KAAK,EAAE;EAC3C,OAAOH,cAAc,CAACC,OAAO,EAAEA,OAAO,EAAEE,KAAK,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}