{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\RealTimeTraffic.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge, Tag } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { PieChart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\nimport { message } from 'antd';\n\n// 注册必要的echarts组件\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\necharts.use([<PERSON><PERSON><PERSON>, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\nconst StyledCanvas = styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\n_c = PageContainer;\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 24px' : props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 0' : props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\n_c2 = MainContent;\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\n_c3 = InfoCard;\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n  \n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n_c4 = CompactStatistic;\nconst RealTimeTraffic = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [vehicles, setVehicles] = useState([]);\n  const [events, setEvents] = useState([]);\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\n  const [stats, setStats] = useState({\n    totalVehicles: 0,\n    onlineVehicles: 0,\n    offlineVehicles: 0,\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0\n  });\n  const eventChartRef = useRef(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  // 在组件中添加路口列表状态\n  const [intersections, setIntersections] = useState([]);\n\n  // 模拟数据加载\n  useEffect(() => {\n    let chart = null;\n    let handleResize = null;\n\n    // 模拟加载延迟\n    setTimeout(() => {\n      // 确保 DOM 元素存在\n      if (!eventChartRef.current) {\n        console.error('图表容器未找到');\n        return;\n      }\n\n      // 检查并清理已存在的图表实例\n      let existingChart = echarts.getInstanceByDom(eventChartRef.current);\n      if (existingChart) {\n        existingChart.dispose();\n      }\n      try {\n        // 初始化新的图表实例\n        chart = echarts.init(eventChartRef.current);\n\n        // 设置图表配置\n        chart.setOption({\n          title: {\n            text: '事件类型分布',\n            left: 'center',\n            textStyle: {\n              fontSize: 14\n            }\n          },\n          tooltip: {\n            trigger: 'item',\n            formatter: '{a} <br/>{b}: {c} ({d}%)'\n          },\n          legend: {\n            orient: 'vertical',\n            left: 10,\n            top: 30,\n            itemWidth: 10,\n            itemHeight: 10,\n            textStyle: {\n              fontSize: 12\n            }\n          },\n          series: [{\n            name: '事件类型',\n            type: 'pie',\n            radius: ['40%', '70%'],\n            avoidLabelOverlap: false,\n            itemStyle: {\n              borderRadius: 4\n            },\n            label: {\n              show: false\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: 12\n              }\n            },\n            labelLine: {\n              show: false\n            },\n            data: [{\n              value: 2,\n              name: '超速'\n            }, {\n              value: 1,\n              name: '急刹车'\n            }, {\n              value: 1,\n              name: '变道'\n            }, {\n              value: 1,\n              name: '启动'\n            }, {\n              value: 1,\n              name: '停止'\n            }]\n          }]\n        });\n\n        // 监听窗口大小变化，调整图表大小\n        handleResize = () => {\n          var _chart;\n          (_chart = chart) === null || _chart === void 0 ? void 0 : _chart.resize();\n        };\n        window.addEventListener('resize', handleResize);\n      } catch (error) {\n        console.error('初始化图表失败:', error);\n      }\n      setLoading(false);\n    }, 1500);\n\n    // 组件卸载时清理\n    return () => {\n      if (handleResize) {\n        window.removeEventListener('resize', handleResize);\n      }\n      if (chart) {\n        chart.dispose();\n      }\n    };\n  }, []);\n\n  // 处理车辆选择\n  const handleVehicleSelect = vehicle => {\n    setSelectedVehicle(vehicle);\n  };\n\n  // 车辆列表列定义\n  const vehicleColumns = [{\n    title: '车牌号',\n    dataIndex: 'plate',\n    key: 'plate',\n    width: '40%'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: '30%',\n    render: status => /*#__PURE__*/_jsxDEV(Badge, {\n      status: status === 'online' ? 'success' : 'error',\n      text: status === 'online' ? '在线' : '离线'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '速度',\n    dataIndex: 'speed',\n    key: 'speed',\n    width: '30%',\n    render: speed => `${speed} km/h`\n  }];\n\n  // 修改 calculateDeviceStats 函数\n  const calculateDeviceStats = devices => {\n    // 按位置分组设备，只包含实际存在的路口\n    const devicesByLocation = devices.reduce((acc, device) => {\n      if (!acc[device.location]) {\n        acc[device.location] = {\n          name: device.location,\n          description: getLocationDescription(device.location),\n          devices: []\n        };\n      }\n      acc[device.location].devices.push(device);\n      return acc;\n    }, {});\n\n    // 获取路口描述的辅助函数\n    function getLocationDescription(location) {\n      switch (location) {\n        case '东门路口':\n          return '校园东门主要出入口';\n        case '北门路口':\n          return '校园北门主要出入口';\n        case '图书馆路口':\n          return '图书馆前十字路口';\n        default:\n          return `${location}主要路口`;\n      }\n    }\n\n    // 计算每个路口的设备统计\n    return Object.entries(devicesByLocation).map(([location, data]) => {\n      // 按设备类型统计数量\n      const deviceCounts = data.devices.reduce((acc, device) => {\n        acc[device.type] = (acc[device.type] || 0) + 1;\n        return acc;\n      }, {});\n\n      // 生成设备配置描述，只包含实际存在的设备类型\n      const deviceConfigParts = [];\n      if (deviceCounts.camera) deviceConfigParts.push(`摄像头: ${deviceCounts.camera}`);\n      if (deviceCounts.rsu) deviceConfigParts.push(`RSU: ${deviceCounts.rsu}`);\n      if (deviceCounts.mmwave_radar) deviceConfigParts.push(`毫米波雷达: ${deviceCounts.mmwave_radar}`);\n      if (deviceCounts.lidar) deviceConfigParts.push(`激光雷达: ${deviceCounts.lidar}`);\n      if (deviceCounts.edge_computing) deviceConfigParts.push(`边缘计算单元: ${deviceCounts.edge_computing}`);\n      return {\n        name: data.name,\n        description: data.description,\n        deviceConfig: deviceConfigParts.join(', '),\n        devices: data.devices.map(device => ({\n          ...device,\n          displayName: device.name // 用于显示的设备名称\n        })),\n        onlineDevices: data.devices.filter(d => d.status === 'online').length,\n        totalDevices: data.devices.length\n      };\n    });\n  };\n\n  // 修改 fetchDevices 函数\n  const fetchDevices = async () => {\n    try {\n      setLoading(true);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      if (response.data && response.data.success) {\n        const devicesData = response.data.data;\n        const locationStats = calculateDeviceStats(devicesData);\n\n        // 更新路口列表\n        setIntersections(locationStats);\n\n        // 更新设备统计\n        const stats = {\n          totalDevices: devicesData.length,\n          onlineDevices: devicesData.filter(d => d.status === 'online').length,\n          offlineDevices: devicesData.filter(d => d.status === 'offline').length\n        };\n        setStats(stats);\n      }\n    } catch (error) {\n      console.error('获取设备列表失败:', error);\n      message.error('获取设备列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    spinning: loading,\n    tip: \"\\u52A0\\u8F7D\\u4E2D...\",\n    children: /*#__PURE__*/_jsxDEV(PageContainer, {\n      children: [/*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"left\",\n        collapsed: leftCollapsed,\n        onCollapse: () => setLeftCollapsed(!leftCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u548C\\u8BBE\\u5907\\u603B\\u6570\\u4FE1\\u606F\",\n          bordered: false,\n          height: \"150px\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [8, 8],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u8F66\\u8F86\\u603B\\u6570\",\n                value: stats.totalVehicles,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u5728\\u7EBF\\u8F66\\u8F86\",\n                value: stats.onlineVehicles,\n                suffix: `/ ${stats.totalVehicles}`,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u79BB\\u7EBF\\u8F66\\u8F86\",\n                value: stats.offlineVehicles,\n                suffix: `/ ${stats.totalVehicles}`,\n                valueStyle: {\n                  color: '#cf1322'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u8BBE\\u5907\\u603B\\u6570\",\n                value: stats.totalDevices,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u5728\\u7EBF\\u8BBE\\u5907\",\n                value: stats.onlineDevices,\n                suffix: `/ ${stats.totalDevices}`,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u79BB\\u7EBF\\u8BBE\\u5907\",\n                value: stats.offlineDevices,\n                suffix: `/ ${stats.totalDevices}`,\n                valueStyle: {\n                  color: '#cf1322'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u5217\\u8868\",\n          bordered: false,\n          height: \"calc(50% - 81px)\",\n          children: /*#__PURE__*/_jsxDEV(List, {\n            size: \"small\",\n            dataSource: events,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              style: {\n                padding: '4px 0'\n              },\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '13px'\n                  },\n                  children: item.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 28\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px'\n                  },\n                  children: `${item.time} - ${item.vehicle}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 34\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u7EDF\\u8BA1\",\n          bordered: false,\n          height: \"calc(50% - 81px)\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: eventChartRef,\n            style: {\n              height: '100%',\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        leftCollapsed: leftCollapsed,\n        rightCollapsed: rightCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"right\",\n        collapsed: rightCollapsed,\n        onCollapse: () => setRightCollapsed(!rightCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u5217\\u8868\",\n          bordered: false,\n          height: \"50%\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            dataSource: vehicles,\n            columns: vehicleColumns,\n            rowKey: \"id\",\n            pagination: false,\n            size: \"small\",\n            scroll: {\n              y: 180\n            },\n            onRow: record => ({\n              onClick: () => handleVehicleSelect(record),\n              style: {\n                cursor: 'pointer',\n                background: (selectedVehicle === null || selectedVehicle === void 0 ? void 0 : selectedVehicle.id) === record.id ? '#e6f7ff' : 'transparent',\n                fontSize: '13px',\n                padding: '4px 8px'\n              }\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u8BE6\\u7EC6\\u4FE1\\u606F\",\n          bordered: false,\n          height: \"50%\",\n          children: selectedVehicle ? /*#__PURE__*/_jsxDEV(Descriptions, {\n            bordered: true,\n            column: 1,\n            size: \"small\",\n            styles: {\n              label: {\n                fontSize: '13px',\n                padding: '4px 8px'\n              },\n              content: {\n                fontSize: '13px',\n                padding: '4px 8px'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8F66\\u724C\\u53F7\",\n              children: selectedVehicle.plate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                status: selectedVehicle.status === 'online' ? 'success' : 'error',\n                text: selectedVehicle.status === 'online' ? '在线' : '离线'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7ECF\\u5EA6\",\n              children: selectedVehicle.lng.toFixed(7)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7EAC\\u5EA6\",\n              children: selectedVehicle.lat.toFixed(7)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u901F\\u5EA6\",\n              children: [selectedVehicle.speed, \" km/h\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u822A\\u5411\\u89D2\",\n              children: [selectedVehicle.heading, \"\\xB0\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '13px'\n            },\n            children: \"\\u8BF7\\u9009\\u62E9\\u8F66\\u8F86\\u67E5\\u770B\\u8BE6\\u7EC6\\u4FE1\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8DEF\\u53E3\\u4FE1\\u606F\\u5217\\u8868\",\n          bordered: false,\n          height: \"calc(100% - 162px)\",\n          children: /*#__PURE__*/_jsxDEV(List, {\n            size: \"small\",\n            dataSource: intersections,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              style: {\n                padding: '8px 0'\n              },\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    fontWeight: 'bold',\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: item.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                    color: item.onlineDevices > 0 ? 'green' : 'gray',\n                    children: [item.onlineDevices, \"/\", item.totalDevices]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '12px',\n                      color: '#666',\n                      marginBottom: '4px'\n                    },\n                    children: item.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '12px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u8BBE\\u5907\\u914D\\u7F6E\\uFF1A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 521,\n                      columnNumber: 27\n                    }, this), item.deviceConfig]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: '4px'\n                    },\n                    children: item.devices.map(device => /*#__PURE__*/_jsxDEV(Tag, {\n                      color: device.status === 'online' ? 'green' : 'red',\n                      style: {\n                        marginBottom: '4px',\n                        marginRight: '4px'\n                      },\n                      children: device.displayName\n                    }, device.id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 525,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 356,\n    columnNumber: 5\n  }, this);\n};\n_s(RealTimeTraffic, \"ZwVA06bdHfGL/DehLLrXmwZEqw8=\");\n_c5 = RealTimeTraffic;\nexport default RealTimeTraffic;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"InfoCard\");\n$RefreshReg$(_c4, \"CompactStatistic\");\n$RefreshReg$(_c5, \"RealTimeTraffic\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Row", "Col", "Card", "Statistic", "List", "Table", "Descriptions", "Spin", "Badge", "Tag", "echarts", "<PERSON><PERSON><PERSON>", "GridComponent", "TooltipComponent", "LegendComponent", "TitleComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "CollapsibleSidebar", "axios", "message", "jsxDEV", "_jsxDEV", "use", "StyledCanvas", "div", "<PERSON><PERSON><PERSON><PERSON>", "_c", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "_c2", "InfoCard", "height", "_c3", "CompactStatistic", "_c4", "RealTimeTraffic", "_s", "loading", "setLoading", "vehicles", "setVehicles", "events", "setEvents", "selectedVehicle", "setSelectedVehicle", "stats", "setStats", "totalVehicles", "onlineVehicles", "offlineVehicles", "totalDevices", "onlineDevices", "offlineDevices", "eventChartRef", "setLeftCollapsed", "setRightCollapsed", "intersections", "setIntersections", "chart", "handleResize", "setTimeout", "current", "console", "error", "existingChart", "getInstanceByDom", "dispose", "init", "setOption", "title", "text", "left", "textStyle", "fontSize", "tooltip", "trigger", "formatter", "legend", "orient", "top", "itemWidth", "itemHeight", "series", "name", "type", "radius", "avoidLabelOverlap", "itemStyle", "borderRadius", "label", "show", "emphasis", "labelLine", "data", "value", "_chart", "resize", "window", "addEventListener", "removeEventListener", "handleVehicleSelect", "vehicle", "vehicleColumns", "dataIndex", "key", "width", "render", "status", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "speed", "calculateDeviceStats", "devices", "devicesByLocation", "reduce", "acc", "device", "location", "description", "getLocationDescription", "push", "Object", "entries", "map", "deviceCounts", "deviceConfigParts", "camera", "rsu", "mmwave_radar", "lidar", "edge_computing", "deviceConfig", "join", "displayName", "filter", "d", "length", "fetchDevices", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "success", "devicesData", "locationStats", "spinning", "tip", "children", "position", "collapsed", "onCollapse", "bordered", "gutter", "span", "valueStyle", "color", "suffix", "size", "dataSource", "renderItem", "item", "<PERSON><PERSON>", "style", "padding", "Meta", "time", "ref", "columns", "<PERSON><PERSON><PERSON>", "pagination", "scroll", "y", "onRow", "record", "onClick", "cursor", "background", "id", "column", "styles", "content", "plate", "lng", "toFixed", "lat", "heading", "fontWeight", "display", "justifyContent", "alignItems", "marginBottom", "marginTop", "marginRight", "_c5", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RealTimeTraffic.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge, Tag } from 'antd';\r\nimport * as echarts from 'echarts/core';\r\nimport { Pie<PERSON>hart } from 'echarts/charts';\r\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\r\nimport { CanvasRenderer } from 'echarts/renderers';\r\nimport styled from 'styled-components';\r\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\r\nimport axios from 'axios';\r\nimport { message } from 'antd';\r\n\r\n// 注册必要的echarts组件\r\necharts.use([PieChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\r\n\r\nconst StyledCanvas = styled.div`\r\n  width: 100%;\r\n  height: 600px;\r\n  background: #f0f2f5;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  z-index: 1;\r\n`;\r\n\r\n// 页面布局容器\r\nconst PageContainer = styled.div`\r\n  display: flex;\r\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\r\n  overflow: hidden;\r\n`;\r\n\r\n// 左侧信息栏容器\r\nconst LeftSidebar = styled.div`\r\n  width: 320px;\r\n  height: 100%;\r\n  padding: 0 8px 0 0;\r\n  border-right: 1px solid #f0f0f0;\r\n  display: flex;\r\n  flex-direction: column;\r\n`;\r\n\r\n// 右侧信息栏容器\r\nconst RightSidebar = styled.div`\r\n  width: 320px;\r\n  height: 100%;\r\n  padding: 0 0 0 8px;\r\n  border-left: 1px solid #f0f0f0;\r\n  display: flex;\r\n  flex-direction: column;\r\n`;\r\n\r\n// 主内容区域\r\nconst MainContent = styled.div`\r\n  flex: 1;\r\n  height: 100%;\r\n  overflow: hidden;\r\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \r\n    props.leftCollapsed ? '0 8px 0 24px' : \r\n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\r\n  transition: all 0.3s ease;\r\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \r\n    props.leftCollapsed ? '0 8px 0 0' : \r\n    props.rightCollapsed ? '0 0 0 8px' : '0'};\r\n  position: relative;\r\n  z-index: 1;\r\n`;\r\n\r\n// 信息卡片\r\nconst InfoCard = styled(Card)`\r\n  margin-bottom: 12px;\r\n  height: ${props => props.height || 'auto'};\r\n  \r\n  .ant-card-head {\r\n    min-height: 40px;\r\n    padding: 0 12px;\r\n  }\r\n  \r\n  .ant-card-head-title {\r\n    padding: 8px 0;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .ant-card-body {\r\n    padding: 12px;\r\n    font-size: 13px;\r\n    height: calc(100% - 40px); // 减去卡片头部高度\r\n    overflow-y: auto;\r\n  }\r\n  \r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n`;\r\n\r\n// 自定义统计数字组件\r\nconst CompactStatistic = styled(Statistic)`\r\n  .ant-statistic-title {\r\n    font-size: 12px;\r\n    margin-bottom: 2px;\r\n  }\r\n  \r\n  .ant-statistic-content {\r\n    font-size: 16px;\r\n  }\r\n`;\r\n\r\nconst RealTimeTraffic = () => {\r\n  const [loading, setLoading] = useState(true);\r\n  const [vehicles, setVehicles] = useState([]);\r\n  const [events, setEvents] = useState([]);\r\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\r\n  const [stats, setStats] = useState({\r\n    totalVehicles: 0,\r\n    onlineVehicles: 0,\r\n    offlineVehicles: 0,\r\n    totalDevices: 0,\r\n    onlineDevices: 0,\r\n    offlineDevices: 0\r\n  });\r\n  \r\n  const eventChartRef = useRef(null);\r\n  \r\n  // 添加侧边栏折叠状态\r\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\r\n  const [rightCollapsed, setRightCollapsed] = useState(false);\r\n  \r\n  // 在组件中添加路口列表状态\r\n  const [intersections, setIntersections] = useState([]);\r\n  \r\n  // 模拟数据加载\r\n  useEffect(() => {\r\n    let chart = null;\r\n    let handleResize = null;\r\n\r\n    // 模拟加载延迟\r\n    setTimeout(() => {\r\n      // 确保 DOM 元素存在\r\n      if (!eventChartRef.current) {\r\n        console.error('图表容器未找到');\r\n        return;\r\n      }\r\n\r\n      // 检查并清理已存在的图表实例\r\n      let existingChart = echarts.getInstanceByDom(eventChartRef.current);\r\n      if (existingChart) {\r\n        existingChart.dispose();\r\n      }\r\n\r\n      try {\r\n        // 初始化新的图表实例\r\n        chart = echarts.init(eventChartRef.current);\r\n        \r\n        // 设置图表配置\r\n        chart.setOption({\r\n          title: {\r\n            text: '事件类型分布',\r\n            left: 'center',\r\n            textStyle: {\r\n              fontSize: 14\r\n            }\r\n          },\r\n          tooltip: {\r\n            trigger: 'item',\r\n            formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n          },\r\n          legend: {\r\n            orient: 'vertical',\r\n            left: 10,\r\n            top: 30,\r\n            itemWidth: 10,\r\n            itemHeight: 10,\r\n            textStyle: {\r\n              fontSize: 12\r\n            }\r\n          },\r\n          series: [\r\n            {\r\n              name: '事件类型',\r\n              type: 'pie',\r\n              radius: ['40%', '70%'],\r\n              avoidLabelOverlap: false,\r\n              itemStyle: {\r\n                borderRadius: 4\r\n              },\r\n              label: {\r\n                show: false\r\n              },\r\n              emphasis: {\r\n                label: {\r\n                  show: true,\r\n                  fontSize: 12\r\n                }\r\n              },\r\n              labelLine: {\r\n                show: false\r\n              },\r\n              data: [\r\n                { value: 2, name: '超速' },\r\n                { value: 1, name: '急刹车' },\r\n                { value: 1, name: '变道' },\r\n                { value: 1, name: '启动' },\r\n                { value: 1, name: '停止' }\r\n              ]\r\n            }\r\n          ]\r\n        });\r\n        \r\n        // 监听窗口大小变化，调整图表大小\r\n        handleResize = () => {\r\n          chart?.resize();\r\n        };\r\n        window.addEventListener('resize', handleResize);\r\n\r\n      } catch (error) {\r\n        console.error('初始化图表失败:', error);\r\n      }\r\n      \r\n      setLoading(false);\r\n    }, 1500);\r\n    \r\n    // 组件卸载时清理\r\n    return () => {\r\n      if (handleResize) {\r\n        window.removeEventListener('resize', handleResize);\r\n      }\r\n      if (chart) {\r\n        chart.dispose();\r\n      }\r\n    };\r\n  }, []);\r\n  \r\n  // 处理车辆选择\r\n  const handleVehicleSelect = (vehicle) => {\r\n    setSelectedVehicle(vehicle);\r\n  };\r\n  \r\n  // 车辆列表列定义\r\n  const vehicleColumns = [\r\n    {\r\n      title: '车牌号',\r\n      dataIndex: 'plate',\r\n      key: 'plate',\r\n      width: '40%',\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'status',\r\n      key: 'status',\r\n      width: '30%',\r\n      render: status => (\r\n        <Badge \r\n          status={status === 'online' ? 'success' : 'error'} \r\n          text={status === 'online' ? '在线' : '离线'} \r\n        />\r\n      ),\r\n    },\r\n    {\r\n      title: '速度',\r\n      dataIndex: 'speed',\r\n      key: 'speed',\r\n      width: '30%',\r\n      render: speed => `${speed} km/h`,\r\n    }\r\n  ];\r\n  \r\n  // 修改 calculateDeviceStats 函数\r\n  const calculateDeviceStats = (devices) => {\r\n    // 按位置分组设备，只包含实际存在的路口\r\n    const devicesByLocation = devices.reduce((acc, device) => {\r\n      if (!acc[device.location]) {\r\n        acc[device.location] = {\r\n          name: device.location,\r\n          description: getLocationDescription(device.location),\r\n          devices: []\r\n        };\r\n      }\r\n      acc[device.location].devices.push(device);\r\n      return acc;\r\n    }, {});\r\n\r\n    // 获取路口描述的辅助函数\r\n    function getLocationDescription(location) {\r\n      switch (location) {\r\n        case '东门路口':\r\n          return '校园东门主要出入口';\r\n        case '北门路口':\r\n          return '校园北门主要出入口';\r\n        case '图书馆路口':\r\n          return '图书馆前十字路口';\r\n        default:\r\n          return `${location}主要路口`;\r\n      }\r\n    }\r\n\r\n    // 计算每个路口的设备统计\r\n    return Object.entries(devicesByLocation).map(([location, data]) => {\r\n      // 按设备类型统计数量\r\n      const deviceCounts = data.devices.reduce((acc, device) => {\r\n        acc[device.type] = (acc[device.type] || 0) + 1;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 生成设备配置描述，只包含实际存在的设备类型\r\n      const deviceConfigParts = [];\r\n      if (deviceCounts.camera) deviceConfigParts.push(`摄像头: ${deviceCounts.camera}`);\r\n      if (deviceCounts.rsu) deviceConfigParts.push(`RSU: ${deviceCounts.rsu}`);\r\n      if (deviceCounts.mmwave_radar) deviceConfigParts.push(`毫米波雷达: ${deviceCounts.mmwave_radar}`);\r\n      if (deviceCounts.lidar) deviceConfigParts.push(`激光雷达: ${deviceCounts.lidar}`);\r\n      if (deviceCounts.edge_computing) deviceConfigParts.push(`边缘计算单元: ${deviceCounts.edge_computing}`);\r\n\r\n      return {\r\n        name: data.name,\r\n        description: data.description,\r\n        deviceConfig: deviceConfigParts.join(', '),\r\n        devices: data.devices.map(device => ({\r\n          ...device,\r\n          displayName: device.name // 用于显示的设备名称\r\n        })),\r\n        onlineDevices: data.devices.filter(d => d.status === 'online').length,\r\n        totalDevices: data.devices.length\r\n      };\r\n    });\r\n  };\r\n\r\n  // 修改 fetchDevices 函数\r\n  const fetchDevices = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n      const response = await axios.get(`${apiUrl}/api/devices`);\r\n      \r\n      if (response.data && response.data.success) {\r\n        const devicesData = response.data.data;\r\n        const locationStats = calculateDeviceStats(devicesData);\r\n        \r\n        // 更新路口列表\r\n        setIntersections(locationStats);\r\n        \r\n        // 更新设备统计\r\n        const stats = {\r\n          totalDevices: devicesData.length,\r\n          onlineDevices: devicesData.filter(d => d.status === 'online').length,\r\n          offlineDevices: devicesData.filter(d => d.status === 'offline').length\r\n        };\r\n        setStats(stats);\r\n      }\r\n    } catch (error) {\r\n      console.error('获取设备列表失败:', error);\r\n      message.error('获取设备列表失败');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  return (\r\n    <Spin spinning={loading} tip=\"加载中...\">\r\n      <PageContainer>\r\n        {/* 左侧信息栏 */}\r\n        <CollapsibleSidebar \r\n          position=\"left\"\r\n          collapsed={leftCollapsed}\r\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\r\n        >\r\n          {/* 车辆和设备总数信息栏 */}\r\n          <InfoCard title=\"车辆和设备总数信息\" bordered={false} height=\"150px\">\r\n            <Row gutter={[8, 8]}>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"车辆总数\" \r\n                  value={stats.totalVehicles} \r\n                  valueStyle={{ color: '#3f8600' }} \r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"在线车辆\" \r\n                  value={stats.onlineVehicles} \r\n                  suffix={`/ ${stats.totalVehicles}`} \r\n                  valueStyle={{ color: '#3f8600' }} \r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"离线车辆\" \r\n                  value={stats.offlineVehicles} \r\n                  suffix={`/ ${stats.totalVehicles}`} \r\n                  valueStyle={{ color: '#cf1322' }} \r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"设备总数\" \r\n                  value={stats.totalDevices} \r\n                  valueStyle={{ color: '#3f8600' }} \r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"在线设备\" \r\n                  value={stats.onlineDevices} \r\n                  suffix={`/ ${stats.totalDevices}`} \r\n                  valueStyle={{ color: '#3f8600' }} \r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"离线设备\" \r\n                  value={stats.offlineDevices} \r\n                  suffix={`/ ${stats.totalDevices}`} \r\n                  valueStyle={{ color: '#cf1322' }} \r\n                />\r\n              </Col>\r\n            </Row>\r\n          </InfoCard>\r\n          \r\n          {/* 实时事件列表栏 */}\r\n          <InfoCard title=\"实时事件列表\" bordered={false} height=\"calc(50% - 81px)\">\r\n            <List\r\n              size=\"small\"\r\n              dataSource={events}\r\n              renderItem={item => (\r\n                <List.Item style={{ padding: '4px 0' }}>\r\n                  <List.Item.Meta\r\n                    title={<span style={{ fontSize: '13px' }}>{item.type}</span>}\r\n                    description={<span style={{ fontSize: '12px' }}>{`${item.time} - ${item.vehicle}`}</span>}\r\n                  />\r\n                </List.Item>\r\n              )}\r\n            />\r\n          </InfoCard>\r\n          \r\n          {/* 实时事件统计栏 */}\r\n          <InfoCard title=\"实时事件统计\" bordered={false} height=\"calc(50% - 81px)\">\r\n            <div ref={eventChartRef} style={{ height: '100%', width: '100%' }}></div>\r\n          </InfoCard>\r\n        </CollapsibleSidebar>\r\n        \r\n        {/* 主内容区域 */}\r\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\r\n          {/* 主要内容 */}\r\n        </MainContent>\r\n        \r\n        {/* 右侧信息栏 */}\r\n        <CollapsibleSidebar\r\n          position=\"right\"\r\n          collapsed={rightCollapsed}\r\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\r\n        >\r\n          {/* 车辆列表栏 */}\r\n          <InfoCard title=\"车辆列表\" bordered={false} height=\"50%\">\r\n            <Table \r\n              dataSource={vehicles} \r\n              columns={vehicleColumns} \r\n              rowKey=\"id\"\r\n              pagination={false}\r\n              size=\"small\"\r\n              scroll={{ y: 180 }}\r\n              onRow={(record) => ({\r\n                onClick: () => handleVehicleSelect(record),\r\n                style: { \r\n                  cursor: 'pointer',\r\n                  background: selectedVehicle?.id === record.id ? '#e6f7ff' : 'transparent',\r\n                  fontSize: '13px',\r\n                  padding: '4px 8px'\r\n                }\r\n              })}\r\n            />\r\n          </InfoCard>\r\n          \r\n          {/* 车辆详细信息栏 */}\r\n          <InfoCard title=\"车辆详细信息\" bordered={false} height=\"50%\">\r\n            {selectedVehicle ? (\r\n              <Descriptions \r\n                bordered \r\n                column={1} \r\n                size=\"small\"\r\n                styles={{\r\n                  label: { fontSize: '13px', padding: '4px 8px' },\r\n                  content: { fontSize: '13px', padding: '4px 8px' }\r\n                }}\r\n              >\r\n                <Descriptions.Item label=\"车牌号\">{selectedVehicle.plate}</Descriptions.Item>\r\n                <Descriptions.Item label=\"状态\">\r\n                  <Badge \r\n                    status={selectedVehicle.status === 'online' ? 'success' : 'error'} \r\n                    text={selectedVehicle.status === 'online' ? '在线' : '离线'} \r\n                  />\r\n                </Descriptions.Item>\r\n                <Descriptions.Item label=\"经度\">{selectedVehicle.lng.toFixed(7)}</Descriptions.Item>\r\n                <Descriptions.Item label=\"纬度\">{selectedVehicle.lat.toFixed(7)}</Descriptions.Item>\r\n                <Descriptions.Item label=\"速度\">{selectedVehicle.speed} km/h</Descriptions.Item>\r\n                <Descriptions.Item label=\"航向角\">{selectedVehicle.heading}°</Descriptions.Item>\r\n              </Descriptions>\r\n            ) : (\r\n              <p style={{ fontSize: '13px' }}>请选择车辆查看详细信息</p>\r\n            )}\r\n          </InfoCard>\r\n          \r\n          {/* 路口信息列表栏 */}\r\n          <InfoCard title=\"路口信息列表\" bordered={false} height=\"calc(100% - 162px)\">\r\n            <List\r\n              size=\"small\"\r\n              dataSource={intersections}\r\n              renderItem={item => (\r\n                <List.Item style={{ padding: '8px 0' }}>\r\n                  <List.Item.Meta\r\n                    title={\r\n                      <div style={{ fontSize: '14px', fontWeight: 'bold', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                        <span>{item.name}</span>\r\n                        <Tag color={item.onlineDevices > 0 ? 'green' : 'gray'}>\r\n                          {item.onlineDevices}/{item.totalDevices}\r\n                        </Tag>\r\n                      </div>\r\n                    }\r\n                    description={\r\n                      <div>\r\n                        <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>\r\n                          {item.description}\r\n                        </div>\r\n                        <div style={{ fontSize: '12px' }}>\r\n                          <strong>设备配置：</strong>{item.deviceConfig}\r\n                        </div>\r\n                        <div style={{ marginTop: '4px' }}>\r\n                          {item.devices.map(device => (\r\n                            <Tag \r\n                              key={device.id}\r\n                              color={device.status === 'online' ? 'green' : 'red'}\r\n                              style={{ marginBottom: '4px', marginRight: '4px' }}\r\n                            >\r\n                              {device.displayName}\r\n                            </Tag>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    }\r\n                  />\r\n                </List.Item>\r\n              )}\r\n            />\r\n          </InfoCard>\r\n        </CollapsibleSidebar>\r\n      </PageContainer>\r\n    </Spin>\r\n  );\r\n};\r\n\r\nexport default RealTimeTraffic;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,QAAQ,MAAM;AAC7F,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,QAAQ,oBAAoB;AACrG,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,yCAAyC;AACxE,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,MAAM;;AAE9B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAZ,OAAO,CAACa,GAAG,CAAC,CAACZ,QAAQ,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,cAAc,CAAC,CAAC;AAEzG,MAAMQ,YAAY,GAAGP,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMC,aAAa,GAAGT,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA,CAAC;;AAED;AAAAE,EAAA,GANMD,aAAa;AAOnB,MAAME,WAAW,GAAGX,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMI,YAAY,GAAGZ,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMK,WAAW,GAAGb,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA,aAAaM,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACxEF,KAAK,CAACC,aAAa,GAAG,cAAc,GACpCD,KAAK,CAACE,cAAc,GAAG,cAAc,GAAG,OAAO;AACnD;AACA,YAAYF,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACvEF,KAAK,CAACC,aAAa,GAAG,WAAW,GACjCD,KAAK,CAACE,cAAc,GAAG,WAAW,GAAG,GAAG;AAC5C;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfMJ,WAAW;AAgBjB,MAAMK,QAAQ,GAAGlB,MAAM,CAACf,IAAI,CAAC;AAC7B;AACA,YAAY6B,KAAK,IAAIA,KAAK,CAACK,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GA1BMF,QAAQ;AA2Bd,MAAMG,gBAAgB,GAAGrB,MAAM,CAACd,SAAS,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoC,GAAA,GATID,gBAAgB;AAWtB,MAAME,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiD,MAAM,EAAEC,SAAS,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC;IACjCuD,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAG3D,MAAM,CAAC,IAAI,CAAC;;EAElC;EACA,MAAM,CAACiC,aAAa,EAAE2B,gBAAgB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoC,cAAc,EAAE2B,iBAAiB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIiE,KAAK,GAAG,IAAI;IAChB,IAAIC,YAAY,GAAG,IAAI;;IAEvB;IACAC,UAAU,CAAC,MAAM;MACf;MACA,IAAI,CAACP,aAAa,CAACQ,OAAO,EAAE;QAC1BC,OAAO,CAACC,KAAK,CAAC,SAAS,CAAC;QACxB;MACF;;MAEA;MACA,IAAIC,aAAa,GAAG3D,OAAO,CAAC4D,gBAAgB,CAACZ,aAAa,CAACQ,OAAO,CAAC;MACnE,IAAIG,aAAa,EAAE;QACjBA,aAAa,CAACE,OAAO,CAAC,CAAC;MACzB;MAEA,IAAI;QACF;QACAR,KAAK,GAAGrD,OAAO,CAAC8D,IAAI,CAACd,aAAa,CAACQ,OAAO,CAAC;;QAE3C;QACAH,KAAK,CAACU,SAAS,CAAC;UACdC,KAAK,EAAE;YACLC,IAAI,EAAE,QAAQ;YACdC,IAAI,EAAE,QAAQ;YACdC,SAAS,EAAE;cACTC,QAAQ,EAAE;YACZ;UACF,CAAC;UACDC,OAAO,EAAE;YACPC,OAAO,EAAE,MAAM;YACfC,SAAS,EAAE;UACb,CAAC;UACDC,MAAM,EAAE;YACNC,MAAM,EAAE,UAAU;YAClBP,IAAI,EAAE,EAAE;YACRQ,GAAG,EAAE,EAAE;YACPC,SAAS,EAAE,EAAE;YACbC,UAAU,EAAE,EAAE;YACdT,SAAS,EAAE;cACTC,QAAQ,EAAE;YACZ;UACF,CAAC;UACDS,MAAM,EAAE,CACN;YACEC,IAAI,EAAE,MAAM;YACZC,IAAI,EAAE,KAAK;YACXC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;YACtBC,iBAAiB,EAAE,KAAK;YACxBC,SAAS,EAAE;cACTC,YAAY,EAAE;YAChB,CAAC;YACDC,KAAK,EAAE;cACLC,IAAI,EAAE;YACR,CAAC;YACDC,QAAQ,EAAE;cACRF,KAAK,EAAE;gBACLC,IAAI,EAAE,IAAI;gBACVjB,QAAQ,EAAE;cACZ;YACF,CAAC;YACDmB,SAAS,EAAE;cACTF,IAAI,EAAE;YACR,CAAC;YACDG,IAAI,EAAE,CACJ;cAAEC,KAAK,EAAE,CAAC;cAAEX,IAAI,EAAE;YAAK,CAAC,EACxB;cAAEW,KAAK,EAAE,CAAC;cAAEX,IAAI,EAAE;YAAM,CAAC,EACzB;cAAEW,KAAK,EAAE,CAAC;cAAEX,IAAI,EAAE;YAAK,CAAC,EACxB;cAAEW,KAAK,EAAE,CAAC;cAAEX,IAAI,EAAE;YAAK,CAAC,EACxB;cAAEW,KAAK,EAAE,CAAC;cAAEX,IAAI,EAAE;YAAK,CAAC;UAE5B,CAAC;QAEL,CAAC,CAAC;;QAEF;QACAxB,YAAY,GAAGA,CAAA,KAAM;UAAA,IAAAoC,MAAA;UACnB,CAAAA,MAAA,GAAArC,KAAK,cAAAqC,MAAA,uBAALA,MAAA,CAAOC,MAAM,CAAC,CAAC;QACjB,CAAC;QACDC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEvC,YAAY,CAAC;MAEjD,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;MAEAzB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;;IAER;IACA,OAAO,MAAM;MACX,IAAIqB,YAAY,EAAE;QAChBsC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAExC,YAAY,CAAC;MACpD;MACA,IAAID,KAAK,EAAE;QACTA,KAAK,CAACQ,OAAO,CAAC,CAAC;MACjB;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkC,mBAAmB,GAAIC,OAAO,IAAK;IACvCzD,kBAAkB,CAACyD,OAAO,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMC,cAAc,GAAG,CACrB;IACEjC,KAAK,EAAE,KAAK;IACZkC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC,EACD;IACEpC,KAAK,EAAE,IAAI;IACXkC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEC,MAAM,iBACZ1F,OAAA,CAACd,KAAK;MACJwG,MAAM,EAAEA,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;MAClDrC,IAAI,EAAEqC,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC;EAEL,CAAC,EACD;IACE1C,KAAK,EAAE,IAAI;IACXkC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEM,KAAK,IAAI,GAAGA,KAAK;EAC3B,CAAC,CACF;;EAED;EACA,MAAMC,oBAAoB,GAAIC,OAAO,IAAK;IACxC;IACA,MAAMC,iBAAiB,GAAGD,OAAO,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAK;MACxD,IAAI,CAACD,GAAG,CAACC,MAAM,CAACC,QAAQ,CAAC,EAAE;QACzBF,GAAG,CAACC,MAAM,CAACC,QAAQ,CAAC,GAAG;UACrBpC,IAAI,EAAEmC,MAAM,CAACC,QAAQ;UACrBC,WAAW,EAAEC,sBAAsB,CAACH,MAAM,CAACC,QAAQ,CAAC;UACpDL,OAAO,EAAE;QACX,CAAC;MACH;MACAG,GAAG,CAACC,MAAM,CAACC,QAAQ,CAAC,CAACL,OAAO,CAACQ,IAAI,CAACJ,MAAM,CAAC;MACzC,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEN;IACA,SAASI,sBAAsBA,CAACF,QAAQ,EAAE;MACxC,QAAQA,QAAQ;QACd,KAAK,MAAM;UACT,OAAO,WAAW;QACpB,KAAK,MAAM;UACT,OAAO,WAAW;QACpB,KAAK,OAAO;UACV,OAAO,UAAU;QACnB;UACE,OAAO,GAAGA,QAAQ,MAAM;MAC5B;IACF;;IAEA;IACA,OAAOI,MAAM,CAACC,OAAO,CAACT,iBAAiB,CAAC,CAACU,GAAG,CAAC,CAAC,CAACN,QAAQ,EAAE1B,IAAI,CAAC,KAAK;MACjE;MACA,MAAMiC,YAAY,GAAGjC,IAAI,CAACqB,OAAO,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAK;QACxDD,GAAG,CAACC,MAAM,CAAClC,IAAI,CAAC,GAAG,CAACiC,GAAG,CAACC,MAAM,CAAClC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAC9C,OAAOiC,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEN;MACA,MAAMU,iBAAiB,GAAG,EAAE;MAC5B,IAAID,YAAY,CAACE,MAAM,EAAED,iBAAiB,CAACL,IAAI,CAAC,QAAQI,YAAY,CAACE,MAAM,EAAE,CAAC;MAC9E,IAAIF,YAAY,CAACG,GAAG,EAAEF,iBAAiB,CAACL,IAAI,CAAC,QAAQI,YAAY,CAACG,GAAG,EAAE,CAAC;MACxE,IAAIH,YAAY,CAACI,YAAY,EAAEH,iBAAiB,CAACL,IAAI,CAAC,UAAUI,YAAY,CAACI,YAAY,EAAE,CAAC;MAC5F,IAAIJ,YAAY,CAACK,KAAK,EAAEJ,iBAAiB,CAACL,IAAI,CAAC,SAASI,YAAY,CAACK,KAAK,EAAE,CAAC;MAC7E,IAAIL,YAAY,CAACM,cAAc,EAAEL,iBAAiB,CAACL,IAAI,CAAC,WAAWI,YAAY,CAACM,cAAc,EAAE,CAAC;MAEjG,OAAO;QACLjD,IAAI,EAAEU,IAAI,CAACV,IAAI;QACfqC,WAAW,EAAE3B,IAAI,CAAC2B,WAAW;QAC7Ba,YAAY,EAAEN,iBAAiB,CAACO,IAAI,CAAC,IAAI,CAAC;QAC1CpB,OAAO,EAAErB,IAAI,CAACqB,OAAO,CAACW,GAAG,CAACP,MAAM,KAAK;UACnC,GAAGA,MAAM;UACTiB,WAAW,EAAEjB,MAAM,CAACnC,IAAI,CAAC;QAC3B,CAAC,CAAC,CAAC;QACHhC,aAAa,EAAE0C,IAAI,CAACqB,OAAO,CAACsB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,MAAM,KAAK,QAAQ,CAAC,CAAC+B,MAAM;QACrExF,YAAY,EAAE2C,IAAI,CAACqB,OAAO,CAACwB;MAC7B,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFrG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMsG,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMlI,KAAK,CAACmI,GAAG,CAAC,GAAGL,MAAM,cAAc,CAAC;MAEzD,IAAII,QAAQ,CAACnD,IAAI,IAAImD,QAAQ,CAACnD,IAAI,CAACqD,OAAO,EAAE;QAC1C,MAAMC,WAAW,GAAGH,QAAQ,CAACnD,IAAI,CAACA,IAAI;QACtC,MAAMuD,aAAa,GAAGnC,oBAAoB,CAACkC,WAAW,CAAC;;QAEvD;QACA1F,gBAAgB,CAAC2F,aAAa,CAAC;;QAE/B;QACA,MAAMvG,KAAK,GAAG;UACZK,YAAY,EAAEiG,WAAW,CAACT,MAAM;UAChCvF,aAAa,EAAEgG,WAAW,CAACX,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,MAAM,KAAK,QAAQ,CAAC,CAAC+B,MAAM;UACpEtF,cAAc,EAAE+F,WAAW,CAACX,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,MAAM,KAAK,SAAS,CAAC,CAAC+B;QAClE,CAAC;QACD5F,QAAQ,CAACD,KAAK,CAAC;MACjB;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjChD,OAAO,CAACgD,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACErB,OAAA,CAACf,IAAI;IAACmJ,QAAQ,EAAEhH,OAAQ;IAACiH,GAAG,EAAC,uBAAQ;IAAAC,QAAA,eACnCtI,OAAA,CAACI,aAAa;MAAAkI,QAAA,gBAEZtI,OAAA,CAACJ,kBAAkB;QACjB2I,QAAQ,EAAC,MAAM;QACfC,SAAS,EAAE9H,aAAc;QACzB+H,UAAU,EAAEA,CAAA,KAAMpG,gBAAgB,CAAC,CAAC3B,aAAa,CAAE;QAAA4H,QAAA,gBAGnDtI,OAAA,CAACa,QAAQ;UAACuC,KAAK,EAAC,wDAAW;UAACsF,QAAQ,EAAE,KAAM;UAAC5H,MAAM,EAAC,OAAO;UAAAwH,QAAA,eACzDtI,OAAA,CAACtB,GAAG;YAACiK,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;YAAAL,QAAA,gBAClBtI,OAAA,CAACrB,GAAG;cAACiK,IAAI,EAAE,CAAE;cAAAN,QAAA,eACXtI,OAAA,CAACgB,gBAAgB;gBACfoC,KAAK,EAAC,0BAAM;gBACZyB,KAAK,EAAEjD,KAAK,CAACE,aAAc;gBAC3B+G,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9F,OAAA,CAACrB,GAAG;cAACiK,IAAI,EAAE,CAAE;cAAAN,QAAA,eACXtI,OAAA,CAACgB,gBAAgB;gBACfoC,KAAK,EAAC,0BAAM;gBACZyB,KAAK,EAAEjD,KAAK,CAACG,cAAe;gBAC5BgH,MAAM,EAAE,KAAKnH,KAAK,CAACE,aAAa,EAAG;gBACnC+G,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9F,OAAA,CAACrB,GAAG;cAACiK,IAAI,EAAE,CAAE;cAAAN,QAAA,eACXtI,OAAA,CAACgB,gBAAgB;gBACfoC,KAAK,EAAC,0BAAM;gBACZyB,KAAK,EAAEjD,KAAK,CAACI,eAAgB;gBAC7B+G,MAAM,EAAE,KAAKnH,KAAK,CAACE,aAAa,EAAG;gBACnC+G,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9F,OAAA,CAACrB,GAAG;cAACiK,IAAI,EAAE,CAAE;cAAAN,QAAA,eACXtI,OAAA,CAACgB,gBAAgB;gBACfoC,KAAK,EAAC,0BAAM;gBACZyB,KAAK,EAAEjD,KAAK,CAACK,YAAa;gBAC1B4G,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9F,OAAA,CAACrB,GAAG;cAACiK,IAAI,EAAE,CAAE;cAAAN,QAAA,eACXtI,OAAA,CAACgB,gBAAgB;gBACfoC,KAAK,EAAC,0BAAM;gBACZyB,KAAK,EAAEjD,KAAK,CAACM,aAAc;gBAC3B6G,MAAM,EAAE,KAAKnH,KAAK,CAACK,YAAY,EAAG;gBAClC4G,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9F,OAAA,CAACrB,GAAG;cAACiK,IAAI,EAAE,CAAE;cAAAN,QAAA,eACXtI,OAAA,CAACgB,gBAAgB;gBACfoC,KAAK,EAAC,0BAAM;gBACZyB,KAAK,EAAEjD,KAAK,CAACO,cAAe;gBAC5B4G,MAAM,EAAE,KAAKnH,KAAK,CAACK,YAAY,EAAG;gBAClC4G,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGX9F,OAAA,CAACa,QAAQ;UAACuC,KAAK,EAAC,sCAAQ;UAACsF,QAAQ,EAAE,KAAM;UAAC5H,MAAM,EAAC,kBAAkB;UAAAwH,QAAA,eACjEtI,OAAA,CAAClB,IAAI;YACHkK,IAAI,EAAC,OAAO;YACZC,UAAU,EAAEzH,MAAO;YACnB0H,UAAU,EAAEC,IAAI,iBACdnJ,OAAA,CAAClB,IAAI,CAACsK,IAAI;cAACC,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAQ,CAAE;cAAAhB,QAAA,eACrCtI,OAAA,CAAClB,IAAI,CAACsK,IAAI,CAACG,IAAI;gBACbnG,KAAK,eAAEpD,OAAA;kBAAMqJ,KAAK,EAAE;oBAAE7F,QAAQ,EAAE;kBAAO,CAAE;kBAAA8E,QAAA,EAAEa,IAAI,CAAChF;gBAAI;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAE;gBAC7DS,WAAW,eAAEvG,OAAA;kBAAMqJ,KAAK,EAAE;oBAAE7F,QAAQ,EAAE;kBAAO,CAAE;kBAAA8E,QAAA,EAAE,GAAGa,IAAI,CAACK,IAAI,MAAML,IAAI,CAAC/D,OAAO;gBAAE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGX9F,OAAA,CAACa,QAAQ;UAACuC,KAAK,EAAC,sCAAQ;UAACsF,QAAQ,EAAE,KAAM;UAAC5H,MAAM,EAAC,kBAAkB;UAAAwH,QAAA,eACjEtI,OAAA;YAAKyJ,GAAG,EAAErH,aAAc;YAACiH,KAAK,EAAE;cAAEvI,MAAM,EAAE,MAAM;cAAE0E,KAAK,EAAE;YAAO;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGrB9F,OAAA,CAACQ,WAAW;QAACE,aAAa,EAAEA,aAAc;QAACC,cAAc,EAAEA;MAAe;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC,eAGd9F,OAAA,CAACJ,kBAAkB;QACjB2I,QAAQ,EAAC,OAAO;QAChBC,SAAS,EAAE7H,cAAe;QAC1B8H,UAAU,EAAEA,CAAA,KAAMnG,iBAAiB,CAAC,CAAC3B,cAAc,CAAE;QAAA2H,QAAA,gBAGrDtI,OAAA,CAACa,QAAQ;UAACuC,KAAK,EAAC,0BAAM;UAACsF,QAAQ,EAAE,KAAM;UAAC5H,MAAM,EAAC,KAAK;UAAAwH,QAAA,eAClDtI,OAAA,CAACjB,KAAK;YACJkK,UAAU,EAAE3H,QAAS;YACrBoI,OAAO,EAAErE,cAAe;YACxBsE,MAAM,EAAC,IAAI;YACXC,UAAU,EAAE,KAAM;YAClBZ,IAAI,EAAC,OAAO;YACZa,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAE;YACnBC,KAAK,EAAGC,MAAM,KAAM;cAClBC,OAAO,EAAEA,CAAA,KAAM9E,mBAAmB,CAAC6E,MAAM,CAAC;cAC1CX,KAAK,EAAE;gBACLa,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,CAAAzI,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0I,EAAE,MAAKJ,MAAM,CAACI,EAAE,GAAG,SAAS,GAAG,aAAa;gBACzE5G,QAAQ,EAAE,MAAM;gBAChB8F,OAAO,EAAE;cACX;YACF,CAAC;UAAE;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGX9F,OAAA,CAACa,QAAQ;UAACuC,KAAK,EAAC,sCAAQ;UAACsF,QAAQ,EAAE,KAAM;UAAC5H,MAAM,EAAC,KAAK;UAAAwH,QAAA,EACnD5G,eAAe,gBACd1B,OAAA,CAAChB,YAAY;YACX0J,QAAQ;YACR2B,MAAM,EAAE,CAAE;YACVrB,IAAI,EAAC,OAAO;YACZsB,MAAM,EAAE;cACN9F,KAAK,EAAE;gBAAEhB,QAAQ,EAAE,MAAM;gBAAE8F,OAAO,EAAE;cAAU,CAAC;cAC/CiB,OAAO,EAAE;gBAAE/G,QAAQ,EAAE,MAAM;gBAAE8F,OAAO,EAAE;cAAU;YAClD,CAAE;YAAAhB,QAAA,gBAEFtI,OAAA,CAAChB,YAAY,CAACoK,IAAI;cAAC5E,KAAK,EAAC,oBAAK;cAAA8D,QAAA,EAAE5G,eAAe,CAAC8I;YAAK;cAAA7E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC1E9F,OAAA,CAAChB,YAAY,CAACoK,IAAI;cAAC5E,KAAK,EAAC,cAAI;cAAA8D,QAAA,eAC3BtI,OAAA,CAACd,KAAK;gBACJwG,MAAM,EAAEhE,eAAe,CAACgE,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;gBAClErC,IAAI,EAAE3B,eAAe,CAACgE,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,eACpB9F,OAAA,CAAChB,YAAY,CAACoK,IAAI;cAAC5E,KAAK,EAAC,cAAI;cAAA8D,QAAA,EAAE5G,eAAe,CAAC+I,GAAG,CAACC,OAAO,CAAC,CAAC;YAAC;cAAA/E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAClF9F,OAAA,CAAChB,YAAY,CAACoK,IAAI;cAAC5E,KAAK,EAAC,cAAI;cAAA8D,QAAA,EAAE5G,eAAe,CAACiJ,GAAG,CAACD,OAAO,CAAC,CAAC;YAAC;cAAA/E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAClF9F,OAAA,CAAChB,YAAY,CAACoK,IAAI;cAAC5E,KAAK,EAAC,cAAI;cAAA8D,QAAA,GAAE5G,eAAe,CAACqE,KAAK,EAAC,OAAK;YAAA;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAC9E9F,OAAA,CAAChB,YAAY,CAACoK,IAAI;cAAC5E,KAAK,EAAC,oBAAK;cAAA8D,QAAA,GAAE5G,eAAe,CAACkJ,OAAO,EAAC,MAAC;YAAA;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,gBAEf9F,OAAA;YAAGqJ,KAAK,EAAE;cAAE7F,QAAQ,EAAE;YAAO,CAAE;YAAA8E,QAAA,EAAC;UAAW;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC/C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAGX9F,OAAA,CAACa,QAAQ;UAACuC,KAAK,EAAC,sCAAQ;UAACsF,QAAQ,EAAE,KAAM;UAAC5H,MAAM,EAAC,oBAAoB;UAAAwH,QAAA,eACnEtI,OAAA,CAAClB,IAAI;YACHkK,IAAI,EAAC,OAAO;YACZC,UAAU,EAAE1G,aAAc;YAC1B2G,UAAU,EAAEC,IAAI,iBACdnJ,OAAA,CAAClB,IAAI,CAACsK,IAAI;cAACC,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAQ,CAAE;cAAAhB,QAAA,eACrCtI,OAAA,CAAClB,IAAI,CAACsK,IAAI,CAACG,IAAI;gBACbnG,KAAK,eACHpD,OAAA;kBAAKqJ,KAAK,EAAE;oBAAE7F,QAAQ,EAAE,MAAM;oBAAEqH,UAAU,EAAE,MAAM;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE,eAAe;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAA1C,QAAA,gBAC3HtI,OAAA;oBAAAsI,QAAA,EAAOa,IAAI,CAACjF;kBAAI;oBAAAyB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACxB9F,OAAA,CAACb,GAAG;oBAAC2J,KAAK,EAAEK,IAAI,CAACjH,aAAa,GAAG,CAAC,GAAG,OAAO,GAAG,MAAO;oBAAAoG,QAAA,GACnDa,IAAI,CAACjH,aAAa,EAAC,GAAC,EAACiH,IAAI,CAAClH,YAAY;kBAAA;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;gBACDS,WAAW,eACTvG,OAAA;kBAAAsI,QAAA,gBACEtI,OAAA;oBAAKqJ,KAAK,EAAE;sBAAE7F,QAAQ,EAAE,MAAM;sBAAEsF,KAAK,EAAE,MAAM;sBAAEmC,YAAY,EAAE;oBAAM,CAAE;oBAAA3C,QAAA,EAClEa,IAAI,CAAC5C;kBAAW;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC,eACN9F,OAAA;oBAAKqJ,KAAK,EAAE;sBAAE7F,QAAQ,EAAE;oBAAO,CAAE;oBAAA8E,QAAA,gBAC/BtI,OAAA;sBAAAsI,QAAA,EAAQ;oBAAK;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAACqD,IAAI,CAAC/B,YAAY;kBAAA;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACN9F,OAAA;oBAAKqJ,KAAK,EAAE;sBAAE6B,SAAS,EAAE;oBAAM,CAAE;oBAAA5C,QAAA,EAC9Ba,IAAI,CAAClD,OAAO,CAACW,GAAG,CAACP,MAAM,iBACtBrG,OAAA,CAACb,GAAG;sBAEF2J,KAAK,EAAEzC,MAAM,CAACX,MAAM,KAAK,QAAQ,GAAG,OAAO,GAAG,KAAM;sBACpD2D,KAAK,EAAE;wBAAE4B,YAAY,EAAE,KAAK;wBAAEE,WAAW,EAAE;sBAAM,CAAE;sBAAA7C,QAAA,EAElDjC,MAAM,CAACiB;oBAAW,GAJdjB,MAAM,CAAC+D,EAAE;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAKX,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEX,CAAC;AAAC3E,EAAA,CAtbID,eAAe;AAAAkK,GAAA,GAAflK,eAAe;AAwbrB,eAAeA,eAAe;AAAC,IAAAb,EAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAmK,GAAA;AAAAC,YAAA,CAAAhL,EAAA;AAAAgL,YAAA,CAAAzK,GAAA;AAAAyK,YAAA,CAAAtK,GAAA;AAAAsK,YAAA,CAAApK,GAAA;AAAAoK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}