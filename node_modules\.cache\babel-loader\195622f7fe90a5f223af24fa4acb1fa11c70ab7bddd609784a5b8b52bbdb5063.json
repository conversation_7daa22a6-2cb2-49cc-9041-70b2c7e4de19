{"ast": null, "code": "// form item name black list.  in form ,you can use form.id get the form item element.\n// use object hasOwnProperty will get better performance if black list is longer.\nconst formItemNameBlackList = ['parentNode'];\n// default form item id prefix.\nconst defaultItemNamePrefixCls = 'form_item';\nexport function toArray(candidate) {\n  if (candidate === undefined || candidate === false) return [];\n  return Array.isArray(candidate) ? candidate : [candidate];\n}\nexport function getFieldId(namePath, formName) {\n  if (!namePath.length) {\n    return undefined;\n  }\n  const mergedId = namePath.join('_');\n  if (formName) {\n    return `${formName}_${mergedId}`;\n  }\n  const isIllegalName = formItemNameBlackList.includes(mergedId);\n  return isIllegalName ? `${defaultItemNamePrefixCls}_${mergedId}` : mergedId;\n}\n/**\n * Get merged status by meta or passed `validateStatus`.\n */\nexport function getStatus(errors, warnings, meta, defaultValidateStatus, hasFeedback, validateStatus) {\n  let status = defaultValidateStatus;\n  if (validateStatus !== undefined) {\n    status = validateStatus;\n  } else if (meta.validating) {\n    status = 'validating';\n  } else if (errors.length) {\n    status = 'error';\n  } else if (warnings.length) {\n    status = 'warning';\n  } else if (meta.touched || hasFeedback && meta.validated) {\n    // success feedback should display when pass hasFeedback prop and current value is valid value\n    status = 'success';\n  }\n  return status;\n}", "map": {"version": 3, "names": ["formItemNameBlackList", "defaultItemNamePrefixCls", "toArray", "candidate", "undefined", "Array", "isArray", "getFieldId", "namePath", "formName", "length", "mergedId", "join", "isIllegalName", "includes", "getStatus", "errors", "warnings", "meta", "defaultValidateStatus", "hasFeedback", "validateStatus", "status", "validating", "touched", "validated"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/form/util.js"], "sourcesContent": ["// form item name black list.  in form ,you can use form.id get the form item element.\n// use object hasOwnProperty will get better performance if black list is longer.\nconst formItemNameBlackList = ['parentNode'];\n// default form item id prefix.\nconst defaultItemNamePrefixCls = 'form_item';\nexport function toArray(candidate) {\n  if (candidate === undefined || candidate === false) return [];\n  return Array.isArray(candidate) ? candidate : [candidate];\n}\nexport function getFieldId(namePath, formName) {\n  if (!namePath.length) {\n    return undefined;\n  }\n  const mergedId = namePath.join('_');\n  if (formName) {\n    return `${formName}_${mergedId}`;\n  }\n  const isIllegalName = formItemNameBlackList.includes(mergedId);\n  return isIllegalName ? `${defaultItemNamePrefixCls}_${mergedId}` : mergedId;\n}\n/**\n * Get merged status by meta or passed `validateStatus`.\n */\nexport function getStatus(errors, warnings, meta, defaultValidateStatus, hasFeedback, validateStatus) {\n  let status = defaultValidateStatus;\n  if (validateStatus !== undefined) {\n    status = validateStatus;\n  } else if (meta.validating) {\n    status = 'validating';\n  } else if (errors.length) {\n    status = 'error';\n  } else if (warnings.length) {\n    status = 'warning';\n  } else if (meta.touched || hasFeedback && meta.validated) {\n    // success feedback should display when pass hasFeedback prop and current value is valid value\n    status = 'success';\n  }\n  return status;\n}"], "mappings": "AAAA;AACA;AACA,MAAMA,qBAAqB,GAAG,CAAC,YAAY,CAAC;AAC5C;AACA,MAAMC,wBAAwB,GAAG,WAAW;AAC5C,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjC,IAAIA,SAAS,KAAKC,SAAS,IAAID,SAAS,KAAK,KAAK,EAAE,OAAO,EAAE;EAC7D,OAAOE,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;AAC3D;AACA,OAAO,SAASI,UAAUA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EAC7C,IAAI,CAACD,QAAQ,CAACE,MAAM,EAAE;IACpB,OAAON,SAAS;EAClB;EACA,MAAMO,QAAQ,GAAGH,QAAQ,CAACI,IAAI,CAAC,GAAG,CAAC;EACnC,IAAIH,QAAQ,EAAE;IACZ,OAAO,GAAGA,QAAQ,IAAIE,QAAQ,EAAE;EAClC;EACA,MAAME,aAAa,GAAGb,qBAAqB,CAACc,QAAQ,CAACH,QAAQ,CAAC;EAC9D,OAAOE,aAAa,GAAG,GAAGZ,wBAAwB,IAAIU,QAAQ,EAAE,GAAGA,QAAQ;AAC7E;AACA;AACA;AACA;AACA,OAAO,SAASI,SAASA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,qBAAqB,EAAEC,WAAW,EAAEC,cAAc,EAAE;EACpG,IAAIC,MAAM,GAAGH,qBAAqB;EAClC,IAAIE,cAAc,KAAKjB,SAAS,EAAE;IAChCkB,MAAM,GAAGD,cAAc;EACzB,CAAC,MAAM,IAAIH,IAAI,CAACK,UAAU,EAAE;IAC1BD,MAAM,GAAG,YAAY;EACvB,CAAC,MAAM,IAAIN,MAAM,CAACN,MAAM,EAAE;IACxBY,MAAM,GAAG,OAAO;EAClB,CAAC,MAAM,IAAIL,QAAQ,CAACP,MAAM,EAAE;IAC1BY,MAAM,GAAG,SAAS;EACpB,CAAC,MAAM,IAAIJ,IAAI,CAACM,OAAO,IAAIJ,WAAW,IAAIF,IAAI,CAACO,SAAS,EAAE;IACxD;IACAH,MAAM,GAAG,SAAS;EACpB;EACA,OAAOA,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}