{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CopyOutlined from \"@ant-design/icons/es/icons/CopyOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport classNames from 'classnames';\nimport Tooltip from '../../tooltip';\nimport { getNode, toList } from './util';\nconst CopyBtn = _ref => {\n  let {\n    prefixCls,\n    copied,\n    locale,\n    iconOnly,\n    tooltips,\n    icon,\n    tabIndex,\n    onCopy,\n    loading: btnLoading\n  } = _ref;\n  const tooltipNodes = toList(tooltips);\n  const iconNodes = toList(icon);\n  const {\n    copied: copiedText,\n    copy: copyText\n  } = locale !== null && locale !== void 0 ? locale : {};\n  const systemStr = copied ? copiedText : copyText;\n  const copyTitle = getNode(tooltipNodes[copied ? 1 : 0], systemStr);\n  const ariaLabel = typeof copyTitle === 'string' ? copyTitle : systemStr;\n  return /*#__PURE__*/React.createElement(Tooltip, {\n    title: copyTitle\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: classNames(`${prefixCls}-copy`, {\n      [`${prefixCls}-copy-success`]: copied,\n      [`${prefixCls}-copy-icon-only`]: iconOnly\n    }),\n    onClick: onCopy,\n    \"aria-label\": ariaLabel,\n    tabIndex: tabIndex\n  }, copied ? getNode(iconNodes[1], /*#__PURE__*/React.createElement(CheckOutlined, null), true) : getNode(iconNodes[0], btnLoading ? /*#__PURE__*/React.createElement(LoadingOutlined, null) : /*#__PURE__*/React.createElement(CopyOutlined, null), true)));\n};\nexport default CopyBtn;", "map": {"version": 3, "names": ["React", "CheckOutlined", "CopyOutlined", "LoadingOutlined", "classNames", "<PERSON><PERSON><PERSON>", "getNode", "toList", "CopyBtn", "_ref", "prefixCls", "copied", "locale", "iconOnly", "tooltips", "icon", "tabIndex", "onCopy", "loading", "btnLoading", "tooltipNodes", "iconNodes", "copiedText", "copy", "copyText", "systemStr", "copyTitle", "aria<PERSON><PERSON><PERSON>", "createElement", "title", "type", "className", "onClick"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/typography/Base/CopyBtn.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport CheckOutlined from \"@ant-design/icons/es/icons/CheckOutlined\";\nimport CopyOutlined from \"@ant-design/icons/es/icons/CopyOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport classNames from 'classnames';\nimport Tooltip from '../../tooltip';\nimport { getNode, toList } from './util';\nconst CopyBtn = _ref => {\n  let {\n    prefixCls,\n    copied,\n    locale,\n    iconOnly,\n    tooltips,\n    icon,\n    tabIndex,\n    onCopy,\n    loading: btnLoading\n  } = _ref;\n  const tooltipNodes = toList(tooltips);\n  const iconNodes = toList(icon);\n  const {\n    copied: copiedText,\n    copy: copyText\n  } = locale !== null && locale !== void 0 ? locale : {};\n  const systemStr = copied ? copiedText : copyText;\n  const copyTitle = getNode(tooltipNodes[copied ? 1 : 0], systemStr);\n  const ariaLabel = typeof copyTitle === 'string' ? copyTitle : systemStr;\n  return /*#__PURE__*/React.createElement(Tooltip, {\n    title: copyTitle\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: classNames(`${prefixCls}-copy`, {\n      [`${prefixCls}-copy-success`]: copied,\n      [`${prefixCls}-copy-icon-only`]: iconOnly\n    }),\n    onClick: onCopy,\n    \"aria-label\": ariaLabel,\n    tabIndex: tabIndex\n  }, copied ? getNode(iconNodes[1], /*#__PURE__*/React.createElement(CheckOutlined, null), true) : getNode(iconNodes[0], btnLoading ? /*#__PURE__*/React.createElement(LoadingOutlined, null) : /*#__PURE__*/React.createElement(CopyOutlined, null), true)));\n};\nexport default CopyBtn;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,OAAO,MAAM,eAAe;AACnC,SAASC,OAAO,EAAEC,MAAM,QAAQ,QAAQ;AACxC,MAAMC,OAAO,GAAGC,IAAI,IAAI;EACtB,IAAI;IACFC,SAAS;IACTC,MAAM;IACNC,MAAM;IACNC,QAAQ;IACRC,QAAQ;IACRC,IAAI;IACJC,QAAQ;IACRC,MAAM;IACNC,OAAO,EAAEC;EACX,CAAC,GAAGV,IAAI;EACR,MAAMW,YAAY,GAAGb,MAAM,CAACO,QAAQ,CAAC;EACrC,MAAMO,SAAS,GAAGd,MAAM,CAACQ,IAAI,CAAC;EAC9B,MAAM;IACJJ,MAAM,EAAEW,UAAU;IAClBC,IAAI,EAAEC;EACR,CAAC,GAAGZ,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,CAAC,CAAC;EACtD,MAAMa,SAAS,GAAGd,MAAM,GAAGW,UAAU,GAAGE,QAAQ;EAChD,MAAME,SAAS,GAAGpB,OAAO,CAACc,YAAY,CAACT,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEc,SAAS,CAAC;EAClE,MAAME,SAAS,GAAG,OAAOD,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGD,SAAS;EACvE,OAAO,aAAazB,KAAK,CAAC4B,aAAa,CAACvB,OAAO,EAAE;IAC/CwB,KAAK,EAAEH;EACT,CAAC,EAAE,aAAa1B,KAAK,CAAC4B,aAAa,CAAC,QAAQ,EAAE;IAC5CE,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE3B,UAAU,CAAC,GAAGM,SAAS,OAAO,EAAE;MACzC,CAAC,GAAGA,SAAS,eAAe,GAAGC,MAAM;MACrC,CAAC,GAAGD,SAAS,iBAAiB,GAAGG;IACnC,CAAC,CAAC;IACFmB,OAAO,EAAEf,MAAM;IACf,YAAY,EAAEU,SAAS;IACvBX,QAAQ,EAAEA;EACZ,CAAC,EAAEL,MAAM,GAAGL,OAAO,CAACe,SAAS,CAAC,CAAC,CAAC,EAAE,aAAarB,KAAK,CAAC4B,aAAa,CAAC3B,aAAa,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,GAAGK,OAAO,CAACe,SAAS,CAAC,CAAC,CAAC,EAAEF,UAAU,GAAG,aAAanB,KAAK,CAAC4B,aAAa,CAACzB,eAAe,EAAE,IAAI,CAAC,GAAG,aAAaH,KAAK,CAAC4B,aAAa,CAAC1B,YAAY,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAC7P,CAAC;AACD,eAAeM,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}