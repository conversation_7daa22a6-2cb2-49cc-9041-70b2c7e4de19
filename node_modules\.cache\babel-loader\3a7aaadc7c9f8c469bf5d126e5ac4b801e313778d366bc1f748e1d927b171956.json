{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: 'localhost',\n  port: 8083,\n  // WebSocket 端口\n  topic: 'changli/cloud/v2x/obu/bsm'\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 在组件顶部添加防抖引用\nconst rsmDataRef = useRef(null);\nconst [displayRsmData, setDisplayRsmData] = useState(null);\n\n// 添加防抖处理的 useEffect\nuseEffect(() => {\n  // 每500ms更新一次显示数据\n  const updateInterval = setInterval(() => {\n    if (rsmDataRef.current !== null && JSON.stringify(rsmDataRef.current) !== JSON.stringify(displayRsmData)) {\n      setDisplayRsmData(rsmDataRef.current);\n    }\n  }, 500);\n  return () => {\n    clearInterval(updateInterval);\n  };\n}, [displayRsmData]);\nconst CampusModel = () => {\n  _s();\n  var _displayRsmData$data$, _displayRsmData$data$2, _displayRsmData$data$3, _displayRsmData$data$4, _displayRsmData$data$5, _displayRsmData$data$6;\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 添加 RSM 数据状态\n  const [rsmData, setRsmData] = useState(null);\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加显示控制状态\n  const [showRsmPanel, setShowRsmPanel] = useState(false);\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 处理 MQTT 消息\n  const handleMqttMessage = (topic, message) => {\n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    try {\n      const data = JSON.parse(message.toString());\n      console.log('收到MQTT消息:', data);\n\n      // 处理 RSM 消息\n      if (topic === 'changli/cloud/v2x/rsu/rsm') {\n        console.log('收到 RSM 数据:', data);\n        setRsmData(data);\n        parseRsmData(data);\n      }\n      // 处理 BSM 消息\n      else if (data.type === 'BSM' && data.data) {\n        const bsmData = data.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n\n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n\n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n\n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);\n        }\n      } else {\n        console.log('消息格式不符合预期:', data);\n      }\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 简化 parseRsmData 函数\n  const parseRsmData = data => {\n    try {\n      // 只在需要时解析数据\n      if (!data || !data.data || !data.data.participants) {\n        return;\n      }\n\n      // 只输出简要信息\n      const vehicleCount = data.data.participants.filter(p => p.partPtcType === '1').length;\n      const totalCount = data.data.participants.length;\n      console.log(`RSM 数据: ${vehicleCount}/${totalCount} 辆机动车`);\n    } catch (error) {\n      console.error('解析 RSM 数据时出错:', error);\n    }\n  };\n\n  // 修改 MQTT 连接方法\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n\n    // 修改连接 URL 格式\n    const client = mqtt.connect(`ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`, {\n      clientId: `webclient_${Math.random().toString(16).substr(2, 8)}`,\n      clean: true,\n      connectTimeout: 8000,\n      // 增加超时时间\n      reconnectPeriod: 3000,\n      // 增加重连间隔\n      keepalive: 60,\n      // 添加 keepalive\n      protocolVersion: 4,\n      // 明确指定协议版本\n      rejectUnauthorized: false // 如果使用自签名证书\n    });\n    client.on('connect', () => {\n      console.log('MQTT连接成功', {\n        broker: MQTT_CONFIG.broker,\n        port: MQTT_CONFIG.port,\n        clientId: client.options.clientId\n      });\n\n      // 订阅 BSM 主题\n      client.subscribe(MQTT_CONFIG.topic, err => {\n        if (err) {\n          console.error('MQTT订阅失败:', err);\n        } else {\n          console.log('MQTT订阅成功:', MQTT_CONFIG.topic);\n        }\n      });\n\n      // 订阅 RSM 主题\n      client.subscribe('changli/cloud/v2x/rsu/rsm', err => {\n        if (err) {\n          console.error('MQTT订阅RSM主题失败:', err);\n        } else {\n          console.log('MQTT订阅RSM主题成功: changli/cloud/v2x/rsu/rsm');\n        }\n      });\n    });\n    client.on('message', handleMqttMessage);\n\n    // 添加更多调试事件\n    client.on('packetsend', packet => {\n      console.log('MQTT发送数据包:', {\n        type: packet.cmd,\n        length: JSON.stringify(packet).length\n      });\n    });\n    client.on('packetreceive', packet => {\n      console.log('MQTT接收数据包:', {\n        type: packet.cmd,\n        length: JSON.stringify(packet).length\n      });\n    });\n    client.on('error', err => {\n      console.error('MQTT错误:', err);\n    });\n    client.on('close', () => {\n      console.log('MQTT连接已关闭');\n    });\n    client.on('offline', () => {\n      console.log('MQTT连接已离线');\n    });\n    client.on('reconnect', () => {\n      console.log('MQTT正在重新连接...');\n    });\n    mqttClientRef.current = client;\n  };\n\n  // 移除 MQTT 连接，只使用 WebSocket\n  useEffect(() => {\n    // 创建 WebSocket 连接\n    const socket = new WebSocket(`ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`);\n    socket.onopen = () => {\n      console.log('WebSocket 连接已建立');\n      // 订阅 MQTT 主题\n      const subscribeMsg = {\n        type: 'subscribe',\n        topics: ['changli/cloud/v2x/rsu/rsm', 'changli/cloud/v2x/obu/bsm']\n      };\n      socket.send(JSON.stringify(subscribeMsg));\n    };\n    socket.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理 RSM 消息\n        if (message.topic === 'changli/cloud/v2x/rsu/rsm') {\n          rsmDataRef.current = message.payload;\n          // 减少日志输出\n        }\n        // 处理 BSM 消息\n        else if (message.topic === 'changli/cloud/v2x/obu/bsm') {\n          handleBsmMessage(message.payload);\n        }\n      } catch (error) {\n        console.error('解析消息时出错:', error);\n      }\n    };\n    socket.onclose = () => {\n      console.log('WebSocket 连接已关闭');\n    };\n    socket.onerror = error => {\n      console.error('WebSocket 错误:', error);\n    };\n    return () => {\n      socket.close();\n    };\n  }, []);\n\n  // 添加 BSM 消息处理函数\n  const handleBsmMessage = data => {\n    try {\n      if (data.type === 'BSM' && data.data) {\n        const bsmData = data.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n\n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n\n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n\n          // 更新状态\n          setVehicleState(newState);\n        }\n      }\n    } catch (error) {\n      console.error('处理 BSM 消息失败:', error);\n    }\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    const scene = new THREE.Scene();\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n\n      // 限制帧率，减少 CPU 使用\n      if (performance.now() - lastRenderTime < 16.67) {\n        // 约60fps\n        return;\n      }\n      lastRenderTime = performance.now();\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 15, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      controls.update();\n\n      // 只在需要时渲染\n      if (needsRender.current) {\n        renderer.render(scene, camera);\n        needsRender.current = false;\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 清理函数\n    return () => {\n      var _containerRef$current;\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      window.removeEventListener('resize', handleResize);\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.removeChild(renderer.domElement);\n      renderer.dispose();\n    };\n  }, []);\n\n  // 在状态更新时设置需要渲染标志\n  const needsRender = useRef(true);\n  useEffect(() => {\n    needsRender.current = true;\n  }, [displayRsmData, vehicleState, viewMode]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 692,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 694,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 704,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: showRsmPanel ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: showRsmPanel ? 'white' : 'black'\n        },\n        onClick: () => setShowRsmPanel(!showRsmPanel),\n        children: showRsmPanel ? '隐藏数据' : '显示数据'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 714,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 693,\n      columnNumber: 7\n    }, this), showRsmPanel && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '20px',\n        right: '20px',\n        width: '300px',\n        maxHeight: '400px',\n        overflowY: 'auto',\n        backgroundColor: 'rgba(0, 0, 0, 0.7)',\n        color: 'white',\n        padding: '10px',\n        borderRadius: '5px',\n        fontSize: '12px',\n        zIndex: 1000\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          margin: '0 0 10px 0',\n          fontSize: '14px'\n        },\n        children: \"RSM \\u6570\\u636E\\u76D1\\u63A7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 742,\n        columnNumber: 11\n      }, this), displayRsmData ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\u8BBE\\u5907MAC: \", displayRsmData.mac || '未知']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\u6D88\\u606F\\u7C7B\\u578B: \", displayRsmData.type || '未知']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\u6D88\\u606F\\u6765\\u6E90: \", displayRsmData.source === '1' ? '仿真平台' : displayRsmData.source === '2' ? '设备' : '未知']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 747,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\u65F6\\u95F4\\u6233: \", new Date(displayRsmData.tm).toLocaleString() || '未知']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 748,\n          columnNumber: 15\n        }, this), displayRsmData.data && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"RSU ID: \", displayRsmData.data.rsuId || '未知']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\u57FA\\u51C6\\u70B9: \", ((_displayRsmData$data$ = displayRsmData.data.posLat) === null || _displayRsmData$data$ === void 0 ? void 0 : _displayRsmData$data$.substring(0, 8)) || '未知', \", \", ((_displayRsmData$data$2 = displayRsmData.data.posLong) === null || _displayRsmData$data$2 === void 0 ? void 0 : _displayRsmData$data$2.substring(0, 8)) || '未知']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\u673A\\u52A8\\u8F66\\u6570\\u91CF: \", ((_displayRsmData$data$3 = displayRsmData.data.participants) === null || _displayRsmData$data$3 === void 0 ? void 0 : _displayRsmData$data$3.filter(p => p.partPtcType === '1').length) || 0, \" / \", ((_displayRsmData$data$4 = displayRsmData.data.participants) === null || _displayRsmData$data$4 === void 0 ? void 0 : _displayRsmData$data$4.length) || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 19\n          }, this), (_displayRsmData$data$5 = displayRsmData.data.participants) === null || _displayRsmData$data$5 === void 0 ? void 0 : _displayRsmData$data$5.filter(p => p.partPtcType === '1').slice(0, 5).map((vehicle, index) => {\n            var _vehicle$partPosLat, _vehicle$partPosLong;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '8px',\n                padding: '5px',\n                backgroundColor: 'rgba(255, 255, 255, 0.1)',\n                borderRadius: '3px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '2px 0',\n                  fontWeight: 'bold'\n                },\n                children: [\"\\u8F66\\u8F86 \", index + 1, \": \", vehicle.vehicleNumber || vehicle.partPtcId || '未知ID']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '2px 0'\n                },\n                children: [\"\\u4F4D\\u7F6E: \", ((_vehicle$partPosLat = vehicle.partPosLat) === null || _vehicle$partPosLat === void 0 ? void 0 : _vehicle$partPosLat.substring(0, 8)) || '未知', \", \", ((_vehicle$partPosLong = vehicle.partPosLong) === null || _vehicle$partPosLong === void 0 ? void 0 : _vehicle$partPosLong.substring(0, 8)) || '未知']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '2px 0'\n                },\n                children: [\"\\u901F\\u5EA6: \", vehicle.partSpeed || '0', \" m/s, \\u822A\\u5411: \", vehicle.partHeading || '0', \"\\xB0\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: '2px 0'\n                },\n                children: [\"\\u7C7B\\u578B: \", vehicle.partSizeType === '1' ? '小车' : vehicle.partSizeType === '2' ? '中型车' : vehicle.partSizeType === '3' ? '大型车' : '未知']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 777,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 21\n            }, this);\n          }), ((_displayRsmData$data$6 = displayRsmData.data.participants) === null || _displayRsmData$data$6 === void 0 ? void 0 : _displayRsmData$data$6.filter(p => p.partPtcType === '1').length) > 5 && /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              textAlign: 'center',\n              marginTop: '5px',\n              fontSize: '11px'\n            },\n            children: [\"\\u8FD8\\u6709 \", displayRsmData.data.participants.filter(p => p.partPtcType === '1').length - 5, \" \\u8F86\\u8F66\\u672A\\u663E\\u793A...\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\u7B49\\u5F85 RSM \\u6570\\u636E...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 797,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 728,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"LkCnoxEL8kC+piWRfNlHVBED/Mc=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "MQTT_CONFIG", "broker", "port", "topic", "BASE_URL", "rsmDataRef", "displayRsmData", "setDisplayRsmData", "updateInterval", "setInterval", "current", "JSON", "stringify", "clearInterval", "CampusModel", "_s", "_displayRsmData$data$", "_displayRsmData$data$2", "_displayRsmData$data$3", "_displayRsmData$data$4", "_displayRsmData$data$5", "_displayRsmData$data$6", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "rsmData", "setRsmData", "viewMode", "setViewMode", "showRsmPanel", "setShowRsmPanel", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "switchToFollowView", "enabled", "switchToGlobalView", "currentPos", "clone", "currentUp", "up", "Tween", "to", "x", "y", "z", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "Math", "PI", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "handleMqttMessage", "message", "toString", "data", "parse", "parseRsmData", "type", "bsmData", "newState", "parseFloat", "partLong", "partLat", "partSpeed", "partHeading", "modelPos", "wgs84ToModel", "newPosition", "Vector3", "rotation", "updateMatrix", "updateMatrixWorld", "error", "participants", "vehicleCount", "filter", "p", "partPtcType", "length", "totalCount", "initMqttClient", "client", "connect", "clientId", "random", "substr", "clean", "connectTimeout", "reconnectPeriod", "keepalive", "protocolVersion", "rejectUnauthorized", "on", "options", "subscribe", "err", "packet", "cmd", "socket", "WebSocket", "onopen", "subscribeMsg", "topics", "send", "onmessage", "event", "payload", "handleBsmMessage", "onclose", "onerror", "close", "scene", "Scene", "camera", "PerspectiveCamera", "window", "innerWidth", "innerHeight", "set", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "add", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "MeshStandardMaterial", "color", "metalness", "roughness", "envMapIntensity", "map", "name", "children", "xhr", "loaded", "total", "toFixed", "initializeScene", "initialState", "initialPos", "animate", "requestAnimationFrame", "performance", "now", "lastRenderTime", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "toArray", "相机位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "<PERSON><PERSON><PERSON>", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "_containerRef$current", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "ref", "style", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "top", "right", "maxHeight", "overflowY", "margin", "mac", "source", "Date", "tm", "toLocaleString", "rsuId", "posLat", "substring", "posLong", "slice", "vehicle", "index", "_vehicle$partPosLat", "_vehicle$partPosLong", "marginTop", "fontWeight", "vehicleNumber", "partPtcId", "partPosLat", "partPosLong", "partSizeType", "textAlign", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "scale", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: 'localhost',\n  port: 8083,  // WebSocket 端口\n  topic: 'changli/cloud/v2x/obu/bsm'\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 在组件顶部添加防抖引用\nconst rsmDataRef = useRef(null);\nconst [displayRsmData, setDisplayRsmData] = useState(null);\n\n// 添加防抖处理的 useEffect\nuseEffect(() => {\n  // 每500ms更新一次显示数据\n  const updateInterval = setInterval(() => {\n    if (rsmDataRef.current !== null && \n        JSON.stringify(rsmDataRef.current) !== JSON.stringify(displayRsmData)) {\n      setDisplayRsmData(rsmDataRef.current);\n    }\n  }, 500);\n  \n  return () => {\n    clearInterval(updateInterval);\n  };\n}, [displayRsmData]);\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 添加 RSM 数据状态\n  const [rsmData, setRsmData] = useState(null);\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加显示控制状态\n  const [showRsmPanel, setShowRsmPanel] = useState(false);\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 处理 MQTT 消息\n  const handleMqttMessage = (topic, message) => {\n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    \n    try {\n      const data = JSON.parse(message.toString());\n      console.log('收到MQTT消息:', data);\n      \n      // 处理 RSM 消息\n      if (topic === 'changli/cloud/v2x/rsu/rsm') {\n        console.log('收到 RSM 数据:', data);\n        setRsmData(data);\n        parseRsmData(data);\n      }\n      // 处理 BSM 消息\n      else if (data.type === 'BSM' && data.data) {\n        const bsmData = data.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        \n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n          \n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n          \n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);\n        }\n      } else {\n        console.log('消息格式不符合预期:', data);\n      }\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 简化 parseRsmData 函数\n  const parseRsmData = (data) => {\n    try {\n      // 只在需要时解析数据\n      if (!data || !data.data || !data.data.participants) {\n        return;\n      }\n      \n      // 只输出简要信息\n      const vehicleCount = data.data.participants.filter(p => p.partPtcType === '1').length;\n      const totalCount = data.data.participants.length;\n      \n      console.log(`RSM 数据: ${vehicleCount}/${totalCount} 辆机动车`);\n    } catch (error) {\n      console.error('解析 RSM 数据时出错:', error);\n    }\n  };\n\n  // 修改 MQTT 连接方法\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    // 修改连接 URL 格式\n    const client = mqtt.connect(`ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`, {\n      clientId: `webclient_${Math.random().toString(16).substr(2, 8)}`,\n      clean: true,\n      connectTimeout: 8000,  // 增加超时时间\n      reconnectPeriod: 3000, // 增加重连间隔\n      keepalive: 60,         // 添加 keepalive\n      protocolVersion: 4,    // 明确指定协议版本\n      rejectUnauthorized: false // 如果使用自签名证书\n    });\n    \n    client.on('connect', () => {\n      console.log('MQTT连接成功', {\n        broker: MQTT_CONFIG.broker,\n        port: MQTT_CONFIG.port,\n        clientId: client.options.clientId\n      });\n      \n      // 订阅 BSM 主题\n      client.subscribe(MQTT_CONFIG.topic, (err) => {\n        if (err) {\n          console.error('MQTT订阅失败:', err);\n        } else {\n          console.log('MQTT订阅成功:', MQTT_CONFIG.topic);\n        }\n      });\n      \n      // 订阅 RSM 主题\n      client.subscribe('changli/cloud/v2x/rsu/rsm', (err) => {\n        if (err) {\n          console.error('MQTT订阅RSM主题失败:', err);\n        } else {\n          console.log('MQTT订阅RSM主题成功: changli/cloud/v2x/rsu/rsm');\n        }\n      });\n    });\n    \n    client.on('message', handleMqttMessage);\n    \n    // 添加更多调试事件\n    client.on('packetsend', (packet) => {\n      console.log('MQTT发送数据包:', {\n        type: packet.cmd,\n        length: JSON.stringify(packet).length\n      });\n    });\n\n    client.on('packetreceive', (packet) => {\n      console.log('MQTT接收数据包:', {\n        type: packet.cmd,\n        length: JSON.stringify(packet).length\n      });\n    });\n    \n    client.on('error', (err) => {\n      console.error('MQTT错误:', err);\n    });\n    \n    client.on('close', () => {\n      console.log('MQTT连接已关闭');\n    });\n    \n    client.on('offline', () => {\n      console.log('MQTT连接已离线');\n    });\n    \n    client.on('reconnect', () => {\n      console.log('MQTT正在重新连接...');\n    });\n    \n    mqttClientRef.current = client;\n  };\n\n  // 移除 MQTT 连接，只使用 WebSocket\n  useEffect(() => {\n    // 创建 WebSocket 连接\n    const socket = new WebSocket(`ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`);\n    \n    socket.onopen = () => {\n      console.log('WebSocket 连接已建立');\n      // 订阅 MQTT 主题\n      const subscribeMsg = {\n        type: 'subscribe',\n        topics: ['changli/cloud/v2x/rsu/rsm', 'changli/cloud/v2x/obu/bsm']\n      };\n      socket.send(JSON.stringify(subscribeMsg));\n    };\n    \n    socket.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理 RSM 消息\n        if (message.topic === 'changli/cloud/v2x/rsu/rsm') {\n          rsmDataRef.current = message.payload;\n          // 减少日志输出\n        }\n        // 处理 BSM 消息\n        else if (message.topic === 'changli/cloud/v2x/obu/bsm') {\n          handleBsmMessage(message.payload);\n        }\n      } catch (error) {\n        console.error('解析消息时出错:', error);\n      }\n    };\n    \n    socket.onclose = () => {\n      console.log('WebSocket 连接已关闭');\n    };\n    \n    socket.onerror = (error) => {\n      console.error('WebSocket 错误:', error);\n    };\n    \n    return () => {\n      socket.close();\n    };\n  }, []);\n\n  // 添加 BSM 消息处理函数\n  const handleBsmMessage = (data) => {\n    try {\n      if (data.type === 'BSM' && data.data) {\n        const bsmData = data.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n          \n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n          \n          // 更新状态\n          setVehicleState(newState);\n        }\n      }\n    } catch (error) {\n      console.error('处理 BSM 消息失败:', error);\n    }\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    const scene = new THREE.Scene();\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n      \n      // 限制帧率，减少 CPU 使用\n      if (performance.now() - lastRenderTime < 16.67) { // 约60fps\n        return;\n      }\n      lastRenderTime = performance.now();\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          15,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      \n      controls.update();\n      \n      // 只在需要时渲染\n      if (needsRender.current) {\n        renderer.render(scene, camera);\n        needsRender.current = false;\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 清理函数\n    return () => {\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      window.removeEventListener('resize', handleResize);\n      containerRef.current?.removeChild(renderer.domElement);\n      renderer.dispose();\n    };\n  }, []);\n\n  // 在状态更新时设置需要渲染标志\n  const needsRender = useRef(true);\n  useEffect(() => {\n    needsRender.current = true;\n  }, [displayRsmData, vehicleState, viewMode]);\n\n  return (\n    <>\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: showRsmPanel ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: showRsmPanel ? 'white' : 'black'\n          }}\n          onClick={() => setShowRsmPanel(!showRsmPanel)}\n        >\n          {showRsmPanel ? '隐藏数据' : '显示数据'}\n        </button>\n      </div>\n      \n      {/* 条件渲染 RSM 数据面板 */}\n      {showRsmPanel && (\n        <div style={{\n          position: 'fixed',\n          top: '20px',\n          right: '20px',\n          width: '300px',\n          maxHeight: '400px',\n          overflowY: 'auto',\n          backgroundColor: 'rgba(0, 0, 0, 0.7)',\n          color: 'white',\n          padding: '10px',\n          borderRadius: '5px',\n          fontSize: '12px',\n          zIndex: 1000\n        }}>\n          <h3 style={{ margin: '0 0 10px 0', fontSize: '14px' }}>RSM 数据监控</h3>\n          {displayRsmData ? (\n            <div>\n              <p>设备MAC: {displayRsmData.mac || '未知'}</p>\n              <p>消息类型: {displayRsmData.type || '未知'}</p>\n              <p>消息来源: {displayRsmData.source === '1' ? '仿真平台' : displayRsmData.source === '2' ? '设备' : '未知'}</p>\n              <p>时间戳: {new Date(displayRsmData.tm).toLocaleString() || '未知'}</p>\n              {displayRsmData.data && (\n                <div>\n                  <p>RSU ID: {displayRsmData.data.rsuId || '未知'}</p>\n                  <p>基准点: {displayRsmData.data.posLat?.substring(0, 8) || '未知'}, {displayRsmData.data.posLong?.substring(0, 8) || '未知'}</p>\n                  \n                  {/* 显示机动车参与者数量 */}\n                  <p>机动车数量: {\n                    displayRsmData.data.participants?.filter(p => p.partPtcType === '1').length || 0\n                  } / {displayRsmData.data.participants?.length || 0}</p>\n                  \n                  {/* 只显示机动车参与者 */}\n                  {displayRsmData.data.participants?.filter(p => p.partPtcType === '1').slice(0, 5).map((vehicle, index) => (\n                    <div key={index} style={{\n                      marginTop: '8px',\n                      padding: '5px',\n                      backgroundColor: 'rgba(255, 255, 255, 0.1)',\n                      borderRadius: '3px'\n                    }}>\n                      <p style={{ margin: '2px 0', fontWeight: 'bold' }}>\n                        车辆 {index + 1}: {vehicle.vehicleNumber || vehicle.partPtcId || '未知ID'}\n                      </p>\n                      <p style={{ margin: '2px 0' }}>\n                        位置: {vehicle.partPosLat?.substring(0, 8) || '未知'}, {vehicle.partPosLong?.substring(0, 8) || '未知'}\n                      </p>\n                      <p style={{ margin: '2px 0' }}>\n                        速度: {vehicle.partSpeed || '0'} m/s, \n                        航向: {vehicle.partHeading || '0'}°\n                      </p>\n                      <p style={{ margin: '2px 0' }}>\n                        类型: {\n                          vehicle.partSizeType === '1' ? '小车' : \n                          vehicle.partSizeType === '2' ? '中型车' : \n                          vehicle.partSizeType === '3' ? '大型车' : '未知'\n                        }\n                      </p>\n                    </div>\n                  ))}\n                  \n                  {/* 如果机动车数量超过5辆，显示\"更多\"提示 */}\n                  {displayRsmData.data.participants?.filter(p => p.partPtcType === '1').length > 5 && (\n                    <p style={{ textAlign: 'center', marginTop: '5px', fontSize: '11px' }}>\n                      还有 {displayRsmData.data.participants.filter(p => p.partPtcType === '1').length - 5} 辆车未显示...\n                    </p>\n                  )}\n                </div>\n              )}\n            </div>\n          ) : (\n            <p>等待 RSM 数据...</p>\n          )}\n        </div>\n      )}\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAE,WAAW;EACnBC,IAAI,EAAE,IAAI;EAAG;EACbC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,UAAU,GAAG1B,MAAM,CAAC,IAAI,CAAC;AAC/B,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;;AAE1D;AACAF,SAAS,CAAC,MAAM;EACd;EACA,MAAM8B,cAAc,GAAGC,WAAW,CAAC,MAAM;IACvC,IAAIJ,UAAU,CAACK,OAAO,KAAK,IAAI,IAC3BC,IAAI,CAACC,SAAS,CAACP,UAAU,CAACK,OAAO,CAAC,KAAKC,IAAI,CAACC,SAAS,CAACN,cAAc,CAAC,EAAE;MACzEC,iBAAiB,CAACF,UAAU,CAACK,OAAO,CAAC;IACvC;EACF,CAAC,EAAE,GAAG,CAAC;EAEP,OAAO,MAAM;IACXG,aAAa,CAACL,cAAc,CAAC;EAC/B,CAAC;AACH,CAAC,EAAE,CAACF,cAAc,CAAC,CAAC;AAEpB,MAAMQ,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACxB,MAAMC,YAAY,GAAG3C,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM4C,UAAU,GAAG5C,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM6C,SAAS,GAAG7C,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMyC,aAAa,GAAG9C,MAAM,CAAC,EAAE,CAAC;EAChC,MAAM+C,eAAe,GAAG/C,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMgD,aAAa,GAAGhD,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC;IAC/CoD,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM,CAAC0D,QAAQ,EAAEC,WAAW,CAAC,GAAG3D,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM8D,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGhF,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMiF,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrB,WAAW,CAAC,QAAQ,CAAC;IACrBzC,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAAC8D,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BvB,WAAW,CAAC,QAAQ,CAAC;IACrBzC,UAAU,GAAG,QAAQ;IAErB,IAAI6D,SAAS,CAACjD,OAAO,IAAIX,QAAQ,EAAE;MACjC;MACA,MAAMgE,UAAU,GAAGJ,SAAS,CAACjD,OAAO,CAACiC,QAAQ,CAACqB,KAAK,CAAC,CAAC;MACrD,MAAMC,SAAS,GAAGN,SAAS,CAACjD,OAAO,CAACwD,EAAE,CAACF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAI/E,KAAK,CAACkF,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCC,MAAM,CAACvF,KAAK,CAACwF,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdjB,SAAS,CAACjD,OAAO,CAACiC,QAAQ,CAACkC,IAAI,CAACd,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDe,KAAK,CAAC,CAAC;;MAEV;MACA,IAAI7F,KAAK,CAACkF,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAACvF,KAAK,CAACwF,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdjB,SAAS,CAACjD,OAAO,CAACwD,EAAE,CAACW,IAAI,CAACZ,SAAS,CAAC;MACtC,CAAC,CAAC,CACDa,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAGhF,QAAQ,CAACiF,MAAM,CAAChB,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAI/E,KAAK,CAACkF,KAAK,CAACY,aAAa,CAAC,CAC3BX,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAACvF,KAAK,CAACwF,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACd7E,QAAQ,CAACiF,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACApB,SAAS,CAACjD,OAAO,CAACuE,MAAM,CAAClF,QAAQ,CAACiF,MAAM,CAAC;QACzCjF,QAAQ,CAACmF,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACA/E,QAAQ,CAAC8D,OAAO,GAAG,IAAI;;MAEvB;MACA9D,QAAQ,CAACoF,WAAW,GAAG,EAAE;MACzBpF,QAAQ,CAACqF,WAAW,GAAG,GAAG;MAC1BrF,QAAQ,CAACsF,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;MACtCxF,QAAQ,CAACyF,aAAa,GAAG,CAAC;MAC1BzF,QAAQ,CAACmF,MAAM,CAAC,CAAC;MAEjBO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAC3F,KAAK,EAAE4F,OAAO,KAAK;IAC5CN,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBvF,KAAK;MACL4F,OAAO,EAAEA,OAAO,CAACC,QAAQ,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAMC,IAAI,GAAGtF,IAAI,CAACuF,KAAK,CAACH,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC;MAC3CP,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEO,IAAI,CAAC;;MAE9B;MACA,IAAI9F,KAAK,KAAK,2BAA2B,EAAE;QACzCsF,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEO,IAAI,CAAC;QAC/B5D,UAAU,CAAC4D,IAAI,CAAC;QAChBE,YAAY,CAACF,IAAI,CAAC;MACpB;MACA;MAAA,KACK,IAAIA,IAAI,CAACG,IAAI,KAAK,KAAK,IAAIH,IAAI,CAACA,IAAI,EAAE;QACzC,MAAMI,OAAO,GAAGJ,IAAI,CAACA,IAAI;QACzB,MAAMK,QAAQ,GAAG;UACftE,SAAS,EAAEuE,UAAU,CAACF,OAAO,CAACG,QAAQ,CAAC;UACvCvE,QAAQ,EAAEsE,UAAU,CAACF,OAAO,CAACI,OAAO,CAAC;UACrCvE,KAAK,EAAEqE,UAAU,CAACF,OAAO,CAACK,SAAS,CAAC;UACpCvE,OAAO,EAAEoE,UAAU,CAACF,OAAO,CAACM,WAAW;QACzC,CAAC;QAEDlB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEY,QAAQ,CAAC;;QAElC;QACA,IAAI/G,gBAAgB,EAAE;UACpB,MAAMqH,QAAQ,GAAGpF,SAAS,CAACd,OAAO,CAACmG,YAAY,CAACP,QAAQ,CAACtE,SAAS,EAAEsE,QAAQ,CAACrE,QAAQ,CAAC;UACtF,MAAM6E,WAAW,GAAG,IAAIjI,KAAK,CAACkI,OAAO,CAACH,QAAQ,CAACvC,CAAC,EAAE,GAAG,EAAE,CAACuC,QAAQ,CAACtC,CAAC,CAAC;;UAEnE;UACA/E,gBAAgB,CAACoD,QAAQ,CAACkC,IAAI,CAACiC,WAAW,CAAC;UAC3CvH,gBAAgB,CAACyH,QAAQ,CAAC1C,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGe,QAAQ,CAACnE,OAAO,GAAGmD,IAAI,CAACC,EAAE,GAAG,GAAG;UACxEhG,gBAAgB,CAAC0H,YAAY,CAAC,CAAC;UAC/B1H,gBAAgB,CAAC2H,iBAAiB,CAAC,IAAI,CAAC;;UAExC;UACAnF,eAAe,CAACuE,QAAQ,CAAC;UACzBb,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEoB,WAAW,CAAC;QACtC;MACF,CAAC,MAAM;QACLrB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEO,IAAI,CAAC;MACjC;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC1B,OAAO,CAAC0B,KAAK,CAAC,SAAS,EAAEpB,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMG,YAAY,GAAIF,IAAI,IAAK;IAC7B,IAAI;MACF;MACA,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACA,IAAI,CAACmB,YAAY,EAAE;QAClD;MACF;;MAEA;MACA,MAAMC,YAAY,GAAGpB,IAAI,CAACA,IAAI,CAACmB,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAK,GAAG,CAAC,CAACC,MAAM;MACrF,MAAMC,UAAU,GAAGzB,IAAI,CAACA,IAAI,CAACmB,YAAY,CAACK,MAAM;MAEhDhC,OAAO,CAACC,GAAG,CAAC,WAAW2B,YAAY,IAAIK,UAAU,OAAO,CAAC;IAC3D,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC;EACF,CAAC;;EAED;EACA,MAAMQ,cAAc,GAAGA,CAAA,KAAM;IAC3BlC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;;IAE7B;IACA,MAAMkC,MAAM,GAAG1I,IAAI,CAAC2I,OAAO,CAAC,QAAQ7H,WAAW,CAACC,MAAM,IAAID,WAAW,CAACE,IAAI,OAAO,EAAE;MACjF4H,QAAQ,EAAE,aAAaxC,IAAI,CAACyC,MAAM,CAAC,CAAC,CAAC/B,QAAQ,CAAC,EAAE,CAAC,CAACgC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MAChEC,KAAK,EAAE,IAAI;MACXC,cAAc,EAAE,IAAI;MAAG;MACvBC,eAAe,EAAE,IAAI;MAAE;MACvBC,SAAS,EAAE,EAAE;MAAU;MACvBC,eAAe,EAAE,CAAC;MAAK;MACvBC,kBAAkB,EAAE,KAAK,CAAC;IAC5B,CAAC,CAAC;IAEFV,MAAM,CAACW,EAAE,CAAC,SAAS,EAAE,MAAM;MACzB9C,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;QACtBzF,MAAM,EAAED,WAAW,CAACC,MAAM;QAC1BC,IAAI,EAAEF,WAAW,CAACE,IAAI;QACtB4H,QAAQ,EAAEF,MAAM,CAACY,OAAO,CAACV;MAC3B,CAAC,CAAC;;MAEF;MACAF,MAAM,CAACa,SAAS,CAACzI,WAAW,CAACG,KAAK,EAAGuI,GAAG,IAAK;QAC3C,IAAIA,GAAG,EAAE;UACPjD,OAAO,CAAC0B,KAAK,CAAC,WAAW,EAAEuB,GAAG,CAAC;QACjC,CAAC,MAAM;UACLjD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE1F,WAAW,CAACG,KAAK,CAAC;QAC7C;MACF,CAAC,CAAC;;MAEF;MACAyH,MAAM,CAACa,SAAS,CAAC,2BAA2B,EAAGC,GAAG,IAAK;QACrD,IAAIA,GAAG,EAAE;UACPjD,OAAO,CAAC0B,KAAK,CAAC,gBAAgB,EAAEuB,GAAG,CAAC;QACtC,CAAC,MAAM;UACLjD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACzD;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFkC,MAAM,CAACW,EAAE,CAAC,SAAS,EAAEzC,iBAAiB,CAAC;;IAEvC;IACA8B,MAAM,CAACW,EAAE,CAAC,YAAY,EAAGI,MAAM,IAAK;MAClClD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBU,IAAI,EAAEuC,MAAM,CAACC,GAAG;QAChBnB,MAAM,EAAE9G,IAAI,CAACC,SAAS,CAAC+H,MAAM,CAAC,CAAClB;MACjC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFG,MAAM,CAACW,EAAE,CAAC,eAAe,EAAGI,MAAM,IAAK;MACrClD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBU,IAAI,EAAEuC,MAAM,CAACC,GAAG;QAChBnB,MAAM,EAAE9G,IAAI,CAACC,SAAS,CAAC+H,MAAM,CAAC,CAAClB;MACjC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFG,MAAM,CAACW,EAAE,CAAC,OAAO,EAAGG,GAAG,IAAK;MAC1BjD,OAAO,CAAC0B,KAAK,CAAC,SAAS,EAAEuB,GAAG,CAAC;IAC/B,CAAC,CAAC;IAEFd,MAAM,CAACW,EAAE,CAAC,OAAO,EAAE,MAAM;MACvB9C,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IAC1B,CAAC,CAAC;IAEFkC,MAAM,CAACW,EAAE,CAAC,SAAS,EAAE,MAAM;MACzB9C,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IAC1B,CAAC,CAAC;IAEFkC,MAAM,CAACW,EAAE,CAAC,WAAW,EAAE,MAAM;MAC3B9C,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC,CAAC;IAEF/D,aAAa,CAACjB,OAAO,GAAGkH,MAAM;EAChC,CAAC;;EAED;EACAlJ,SAAS,CAAC,MAAM;IACd;IACA,MAAMmK,MAAM,GAAG,IAAIC,SAAS,CAAC,QAAQ9I,WAAW,CAACC,MAAM,IAAID,WAAW,CAACE,IAAI,OAAO,CAAC;IAEnF2I,MAAM,CAACE,MAAM,GAAG,MAAM;MACpBtD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9B;MACA,MAAMsD,YAAY,GAAG;QACnB5C,IAAI,EAAE,WAAW;QACjB6C,MAAM,EAAE,CAAC,2BAA2B,EAAE,2BAA2B;MACnE,CAAC;MACDJ,MAAM,CAACK,IAAI,CAACvI,IAAI,CAACC,SAAS,CAACoI,YAAY,CAAC,CAAC;IAC3C,CAAC;IAEDH,MAAM,CAACM,SAAS,GAAIC,KAAK,IAAK;MAC5B,IAAI;QACF,MAAMrD,OAAO,GAAGpF,IAAI,CAACuF,KAAK,CAACkD,KAAK,CAACnD,IAAI,CAAC;;QAEtC;QACA,IAAIF,OAAO,CAAC5F,KAAK,KAAK,2BAA2B,EAAE;UACjDE,UAAU,CAACK,OAAO,GAAGqF,OAAO,CAACsD,OAAO;UACpC;QACF;QACA;QAAA,KACK,IAAItD,OAAO,CAAC5F,KAAK,KAAK,2BAA2B,EAAE;UACtDmJ,gBAAgB,CAACvD,OAAO,CAACsD,OAAO,CAAC;QACnC;MACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;QACd1B,OAAO,CAAC0B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;IAED0B,MAAM,CAACU,OAAO,GAAG,MAAM;MACrB9D,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC;IAEDmD,MAAM,CAACW,OAAO,GAAIrC,KAAK,IAAK;MAC1B1B,OAAO,CAAC0B,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC;IAED,OAAO,MAAM;MACX0B,MAAM,CAACY,KAAK,CAAC,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMH,gBAAgB,GAAIrD,IAAI,IAAK;IACjC,IAAI;MACF,IAAIA,IAAI,CAACG,IAAI,KAAK,KAAK,IAAIH,IAAI,CAACA,IAAI,EAAE;QACpC,MAAMI,OAAO,GAAGJ,IAAI,CAACA,IAAI;QACzB,MAAMK,QAAQ,GAAG;UACftE,SAAS,EAAEuE,UAAU,CAACF,OAAO,CAACG,QAAQ,CAAC;UACvCvE,QAAQ,EAAEsE,UAAU,CAACF,OAAO,CAACI,OAAO,CAAC;UACrCvE,KAAK,EAAEqE,UAAU,CAACF,OAAO,CAACK,SAAS,CAAC;UACpCvE,OAAO,EAAEoE,UAAU,CAACF,OAAO,CAACM,WAAW;QACzC,CAAC;;QAED;QACA,IAAIpH,gBAAgB,EAAE;UACpB,MAAMqH,QAAQ,GAAGpF,SAAS,CAACd,OAAO,CAACmG,YAAY,CAACP,QAAQ,CAACtE,SAAS,EAAEsE,QAAQ,CAACrE,QAAQ,CAAC;UACtF,MAAM6E,WAAW,GAAG,IAAIjI,KAAK,CAACkI,OAAO,CAACH,QAAQ,CAACvC,CAAC,EAAE,GAAG,EAAE,CAACuC,QAAQ,CAACtC,CAAC,CAAC;;UAEnE;UACA/E,gBAAgB,CAACoD,QAAQ,CAACkC,IAAI,CAACiC,WAAW,CAAC;UAC3CvH,gBAAgB,CAACyH,QAAQ,CAAC1C,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGe,QAAQ,CAACnE,OAAO,GAAGmD,IAAI,CAACC,EAAE,GAAG,GAAG;UACxEhG,gBAAgB,CAAC0H,YAAY,CAAC,CAAC;UAC/B1H,gBAAgB,CAAC2H,iBAAiB,CAAC,IAAI,CAAC;;UAExC;UACAnF,eAAe,CAACuE,QAAQ,CAAC;QAC3B;MACF;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC;EACF,CAAC;EAEDzI,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4C,YAAY,CAACZ,OAAO,EAAE;;IAE3B;IACA,MAAMgJ,KAAK,GAAG,IAAI7K,KAAK,CAAC8K,KAAK,CAAC,CAAC;;IAE/B;IACA,MAAMC,MAAM,GAAG,IAAI/K,KAAK,CAACgL,iBAAiB,CACxC,EAAE,EACFC,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACDJ,MAAM,CAACjH,QAAQ,CAACsH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCL,MAAM,CAAC3E,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBtB,SAAS,CAACjD,OAAO,GAAGkJ,MAAM;;IAE1B;IACA,MAAMM,QAAQ,GAAG,IAAIrL,KAAK,CAACsL,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAACP,MAAM,CAACC,UAAU,EAAED,MAAM,CAACE,WAAW,CAAC;IACvDE,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAACT,MAAM,CAACU,gBAAgB,CAAC;IAC/ClJ,YAAY,CAACZ,OAAO,CAAC+J,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAI9L,KAAK,CAAC+L,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5DlB,KAAK,CAACmB,GAAG,CAACF,YAAY,CAAC;;IAEvB;IACA,MAAMG,iBAAiB,GAAG,IAAIjM,KAAK,CAACkM,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAACnI,QAAQ,CAACsH,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1CP,KAAK,CAACmB,GAAG,CAACC,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAInM,KAAK,CAACkM,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAACrI,QAAQ,CAACsH,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3CP,KAAK,CAACmB,GAAG,CAACG,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAIpM,KAAK,CAACqM,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAACtI,QAAQ,CAACsH,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCgB,SAAS,CAACE,KAAK,GAAG7F,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7B0F,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxB5B,KAAK,CAACmB,GAAG,CAACI,SAAS,CAAC;;IAEpB;IACAlL,QAAQ,GAAG,IAAIhB,aAAa,CAAC6K,MAAM,EAAEM,QAAQ,CAACQ,UAAU,CAAC;IACzD3K,QAAQ,CAACwL,aAAa,GAAG,IAAI;IAC7BxL,QAAQ,CAACyL,aAAa,GAAG,IAAI;IAC7BzL,QAAQ,CAAC0L,kBAAkB,GAAG,KAAK;IACnC1L,QAAQ,CAACoF,WAAW,GAAG,EAAE;IACzBpF,QAAQ,CAACqF,WAAW,GAAG,GAAG;IAC1BrF,QAAQ,CAACsF,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IACtCxF,QAAQ,CAACyF,aAAa,GAAG,CAAC;IAC1BzF,QAAQ,CAACiF,MAAM,CAACiF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BlK,QAAQ,CAACmF,MAAM,CAAC,CAAC;;IAEjB;IACAO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBkE,MAAM,EAAE,CAAC,CAACA,MAAM;MAChB7J,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpB4D,SAAS,EAAE,CAAC,CAACA,SAAS,CAACjD;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMgL,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAIhN,UAAU,CAAC,CAAC;QACtCgN,aAAa,CAACC,IAAI,CAChB,GAAG3L,QAAQ,uBAAuB,EACjC4L,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAACtC,KAAK;;UAE/B;UACA,MAAMwC,gBAAgB,GAAG,IAAIrN,KAAK,CAACsN,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACG,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAI3N,KAAK,CAAC4N,oBAAoB,CAAC;kBACjDC,KAAK,EAAE,QAAQ;kBAAO;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIR,KAAK,CAACE,QAAQ,CAACO,GAAG,EAAE;kBACtBN,WAAW,CAACM,GAAG,GAAGT,KAAK,CAACE,QAAQ,CAACO,GAAG;gBACtC;;gBAEA;gBACAT,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5B/G,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE2G,KAAK,CAACU,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMd,YAAY,CAACe,QAAQ,CAACvF,MAAM,GAAG,CAAC,EAAE;YACtC,MAAM4E,KAAK,GAAGJ,YAAY,CAACe,QAAQ,CAAC,CAAC,CAAC;YACtCd,gBAAgB,CAACrB,GAAG,CAACwB,KAAK,CAAC;UAC7B;;UAEA;UACA3C,KAAK,CAACmB,GAAG,CAACqB,gBAAgB,CAAC;;UAE3B;UACA3M,gBAAgB,GAAG2M,gBAAgB;UAEnCzG,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9B7D,kBAAkB,CAAC,IAAI,CAAC;UACxB+J,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAe,GAAG,IAAK;UACPxH,OAAO,CAACC,GAAG,CAAC,aAAa,CAACuH,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDvB,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMwB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMnB,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACA,IAAIQ,gBAAgB,EAAE;UACpB,MAAMoB,YAAY,GAAG;YACnBtL,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UACD,MAAMoL,UAAU,GAAG/L,SAAS,CAACd,OAAO,CAACmG,YAAY,CAACyG,YAAY,CAACtL,SAAS,EAAEsL,YAAY,CAACrL,QAAQ,CAAC;UAChGiK,gBAAgB,CAACvJ,QAAQ,CAACsH,GAAG,CAACsD,UAAU,CAAClJ,CAAC,EAAE,GAAG,EAAE,CAACkJ,UAAU,CAACjJ,CAAC,CAAC;UAC/D4H,gBAAgB,CAAClF,QAAQ,CAAC1C,CAAC,GAAIgB,IAAI,CAACC,EAAE,GAAG+H,YAAY,CAACnL,OAAO,GAAGmD,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9E2G,gBAAgB,CAACjF,YAAY,CAAC,CAAC;UAC/BiF,gBAAgB,CAAChF,iBAAiB,CAAC,IAAI,CAAC;UACxCtH,eAAe,GAAGsM,gBAAgB,CAACvJ,QAAQ,CAACqB,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAOmD,KAAK,EAAE;QACd1B,OAAO,CAAC0B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAMqG,OAAO,GAAGA,CAAA,KAAM;MACpBC,qBAAqB,CAACD,OAAO,CAAC;;MAE9B;MACA,IAAIE,WAAW,CAACC,GAAG,CAAC,CAAC,GAAGC,cAAc,GAAG,KAAK,EAAE;QAAE;QAChD;MACF;MACAA,cAAc,GAAGF,WAAW,CAACC,GAAG,CAAC,CAAC;;MAElC;MACA1O,KAAK,CAACiG,MAAM,CAAC,CAAC;MAEd,IAAIpF,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAAC8D,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMgK,UAAU,GAAGtO,gBAAgB,CAACoD,QAAQ,CAACqB,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAM8J,eAAe,GAAGvO,gBAAgB,CAACyH,QAAQ,CAAC1C,CAAC;;QAEnD;QACA;QACA,MAAMyJ,gBAAgB,GAAG,EAAED,eAAe,GAAGxI,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAMyI,YAAY,GAAG,IAAInP,KAAK,CAACkI,OAAO,CACpC,CAAC,EAAE,GAAGzB,IAAI,CAAC2I,GAAG,CAACF,gBAAgB,CAAC,EAChC,EAAE,EACF,CAAC,EAAE,GAAGzI,IAAI,CAAC4I,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACAnE,MAAM,CAACjH,QAAQ,CAACkC,IAAI,CAACgJ,UAAU,CAAC,CAAChD,GAAG,CAACmD,YAAY,CAAC;;QAElD;QACApE,MAAM,CAAC1F,EAAE,CAAC+F,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAMkE,YAAY,GAAGN,UAAU,CAAC7J,KAAK,CAAC,CAAC;QACvC4F,MAAM,CAAC3E,MAAM,CAACkJ,YAAY,CAAC;;QAE3B;QACAvE,MAAM,CAACwE,sBAAsB,CAAC,CAAC;QAC/BxE,MAAM,CAAC3C,YAAY,CAAC,CAAC;QACrB2C,MAAM,CAAC1C,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACAnH,QAAQ,CAAC8D,OAAO,GAAG,KAAK;;QAExB;QACA9D,QAAQ,CAACiF,MAAM,CAACH,IAAI,CAACgJ,UAAU,CAAC;QAChC9N,QAAQ,CAACmF,MAAM,CAAC,CAAC;QAEjBO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnB2I,IAAI,EAAER,UAAU,CAACS,OAAO,CAAC,CAAC;UAC1BC,IAAI,EAAE3E,MAAM,CAACjH,QAAQ,CAAC2L,OAAO,CAAC,CAAC;UAC/BE,IAAI,EAAEL,YAAY,CAACG,OAAO,CAAC,CAAC;UAC5BG,IAAI,EAAE7E,MAAM,CAAC8E,iBAAiB,CAAC,IAAI7P,KAAK,CAACkI,OAAO,CAAC,CAAC,CAAC,CAACuH,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIxO,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAAC8D,OAAO,GAAG,IAAI;;QAEvB;QACA+F,MAAM,CAAC1F,EAAE,CAAC+F,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAI3E,IAAI,CAACqJ,GAAG,CAAC/E,MAAM,CAACjH,QAAQ,CAAC2B,CAAC,CAAC,GAAG,EAAE,EAAE;UACpCsF,MAAM,CAACjH,QAAQ,CAACsH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BlK,QAAQ,CAACiF,MAAM,CAACiF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BL,MAAM,CAAC3E,MAAM,CAAClF,QAAQ,CAACiF,MAAM,CAAC;UAC9BjF,QAAQ,CAACmF,MAAM,CAAC,CAAC;QACnB;MACF;MAEAnF,QAAQ,CAACmF,MAAM,CAAC,CAAC;;MAEjB;MACA,IAAI0J,WAAW,CAAClO,OAAO,EAAE;QACvBwJ,QAAQ,CAAC2E,MAAM,CAACnF,KAAK,EAAEE,MAAM,CAAC;QAC9BgF,WAAW,CAAClO,OAAO,GAAG,KAAK;MAC7B;IACF,CAAC;IAED8M,OAAO,CAAC,CAAC;;IAET;IACA,MAAMsB,YAAY,GAAGA,CAAA,KAAM;MACzBlF,MAAM,CAACmF,MAAM,GAAGjF,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,WAAW;MACtDJ,MAAM,CAACwE,sBAAsB,CAAC,CAAC;MAC/BlE,QAAQ,CAACG,OAAO,CAACP,MAAM,CAACC,UAAU,EAAED,MAAM,CAACE,WAAW,CAAC;IACzD,CAAC;IACDF,MAAM,CAACkF,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACAhF,MAAM,CAACmF,aAAa,GAAG,MAAM;MAC3B,IAAItL,SAAS,CAACjD,OAAO,EAAE;QACrBiD,SAAS,CAACjD,OAAO,CAACiC,QAAQ,CAACsH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzCtG,SAAS,CAACjD,OAAO,CAACuE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCtB,SAAS,CAACjD,OAAO,CAACuG,YAAY,CAAC,CAAC;QAChCtD,SAAS,CAACjD,OAAO,CAACwG,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAInH,QAAQ,EAAE;UACZA,QAAQ,CAACiF,MAAM,CAACiF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BlK,QAAQ,CAAC8D,OAAO,GAAG,IAAI;UACvB9D,QAAQ,CAACmF,MAAM,CAAC,CAAC;QACnB;QAEApF,UAAU,GAAG,QAAQ;QACrB2F,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MAAA,IAAAwJ,qBAAA;MACX,IAAIxP,oBAAoB,EAAE;QACxBmB,aAAa,CAACnB,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;MACAoK,MAAM,CAACqF,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;MAClD,CAAAI,qBAAA,GAAA5N,YAAY,CAACZ,OAAO,cAAAwO,qBAAA,uBAApBA,qBAAA,CAAsBE,WAAW,CAAClF,QAAQ,CAACQ,UAAU,CAAC;MACtDR,QAAQ,CAACmF,OAAO,CAAC,CAAC;IACpB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMT,WAAW,GAAGjQ,MAAM,CAAC,IAAI,CAAC;EAChCD,SAAS,CAAC,MAAM;IACdkQ,WAAW,CAAClO,OAAO,GAAG,IAAI;EAC5B,CAAC,EAAE,CAACJ,cAAc,EAAEwB,YAAY,EAAEQ,QAAQ,CAAC,CAAC;EAE5C,oBACElD,OAAA,CAAAE,SAAA;IAAA0N,QAAA,gBACE5N,OAAA;MAAKkQ,GAAG,EAAEhO,YAAa;MAACiO,KAAK,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpEzQ,OAAA;MAAKmQ,KAAK,EAAE7M,oBAAqB;MAAAsK,QAAA,gBAC/B5N,OAAA;QACEmQ,KAAK,EAAE;UACL,GAAGrM,WAAW;UACdE,eAAe,EAAEd,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoK,KAAK,EAAEpK,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFwN,OAAO,EAAElM,kBAAmB;QAAAoJ,QAAA,EAC7B;MAED;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzQ,OAAA;QACEmQ,KAAK,EAAE;UACL,GAAGrM,WAAW;UACdE,eAAe,EAAEd,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoK,KAAK,EAAEpK,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFwN,OAAO,EAAEhM,kBAAmB;QAAAkJ,QAAA,EAC7B;MAED;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzQ,OAAA;QACEmQ,KAAK,EAAE;UACL,GAAGrM,WAAW;UACdE,eAAe,EAAEZ,YAAY,GAAG,SAAS,GAAG,0BAA0B;UACtEkK,KAAK,EAAElK,YAAY,GAAG,OAAO,GAAG;QAClC,CAAE;QACFsN,OAAO,EAAEA,CAAA,KAAMrN,eAAe,CAAC,CAACD,YAAY,CAAE;QAAAwK,QAAA,EAE7CxK,YAAY,GAAG,MAAM,GAAG;MAAM;QAAAkN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGLrN,YAAY,iBACXpD,OAAA;MAAKmQ,KAAK,EAAE;QACV5M,QAAQ,EAAE,OAAO;QACjBoN,GAAG,EAAE,MAAM;QACXC,KAAK,EAAE,MAAM;QACbR,KAAK,EAAE,OAAO;QACdS,SAAS,EAAE,OAAO;QAClBC,SAAS,EAAE,MAAM;QACjB9M,eAAe,EAAE,oBAAoB;QACrCsJ,KAAK,EAAE,OAAO;QACdvJ,OAAO,EAAE,MAAM;QACfG,YAAY,EAAE,KAAK;QACnBE,QAAQ,EAAE,MAAM;QAChBT,MAAM,EAAE;MACV,CAAE;MAAAiK,QAAA,gBACA5N,OAAA;QAAImQ,KAAK,EAAE;UAAEY,MAAM,EAAE,YAAY;UAAE3M,QAAQ,EAAE;QAAO,CAAE;QAAAwJ,QAAA,EAAC;MAAQ;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACnEvP,cAAc,gBACblB,OAAA;QAAA4N,QAAA,gBACE5N,OAAA;UAAA4N,QAAA,GAAG,mBAAO,EAAC1M,cAAc,CAAC8P,GAAG,IAAI,IAAI;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1CzQ,OAAA;UAAA4N,QAAA,GAAG,4BAAM,EAAC1M,cAAc,CAAC8F,IAAI,IAAI,IAAI;QAAA;UAAAsJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1CzQ,OAAA;UAAA4N,QAAA,GAAG,4BAAM,EAAC1M,cAAc,CAAC+P,MAAM,KAAK,GAAG,GAAG,MAAM,GAAG/P,cAAc,CAAC+P,MAAM,KAAK,GAAG,GAAG,IAAI,GAAG,IAAI;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnGzQ,OAAA;UAAA4N,QAAA,GAAG,sBAAK,EAAC,IAAIsD,IAAI,CAAChQ,cAAc,CAACiQ,EAAE,CAAC,CAACC,cAAc,CAAC,CAAC,IAAI,IAAI;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACjEvP,cAAc,CAAC2F,IAAI,iBAClB7G,OAAA;UAAA4N,QAAA,gBACE5N,OAAA;YAAA4N,QAAA,GAAG,UAAQ,EAAC1M,cAAc,CAAC2F,IAAI,CAACwK,KAAK,IAAI,IAAI;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClDzQ,OAAA;YAAA4N,QAAA,GAAG,sBAAK,EAAC,EAAAhM,qBAAA,GAAAV,cAAc,CAAC2F,IAAI,CAACyK,MAAM,cAAA1P,qBAAA,uBAA1BA,qBAAA,CAA4B2P,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,IAAI,EAAC,IAAE,EAAC,EAAA1P,sBAAA,GAAAX,cAAc,CAAC2F,IAAI,CAAC2K,OAAO,cAAA3P,sBAAA,uBAA3BA,sBAAA,CAA6B0P,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,IAAI;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAGzHzQ,OAAA;YAAA4N,QAAA,GAAG,kCAAO,EACR,EAAA9L,sBAAA,GAAAZ,cAAc,CAAC2F,IAAI,CAACmB,YAAY,cAAAlG,sBAAA,uBAAhCA,sBAAA,CAAkCoG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAK,GAAG,CAAC,CAACC,MAAM,KAAI,CAAC,EACjF,KAAG,EAAC,EAAAtG,sBAAA,GAAAb,cAAc,CAAC2F,IAAI,CAACmB,YAAY,cAAAjG,sBAAA,uBAAhCA,sBAAA,CAAkCsG,MAAM,KAAI,CAAC;UAAA;YAAAiI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,GAAAzO,sBAAA,GAGtDd,cAAc,CAAC2F,IAAI,CAACmB,YAAY,cAAAhG,sBAAA,uBAAhCA,sBAAA,CAAkCkG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAK,GAAG,CAAC,CAACqJ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC/D,GAAG,CAAC,CAACgE,OAAO,EAAEC,KAAK;YAAA,IAAAC,mBAAA,EAAAC,oBAAA;YAAA,oBACnG7R,OAAA;cAAiBmQ,KAAK,EAAE;gBACtB2B,SAAS,EAAE,KAAK;gBAChB/N,OAAO,EAAE,KAAK;gBACdC,eAAe,EAAE,0BAA0B;gBAC3CE,YAAY,EAAE;cAChB,CAAE;cAAA0J,QAAA,gBACA5N,OAAA;gBAAGmQ,KAAK,EAAE;kBAAEY,MAAM,EAAE,OAAO;kBAAEgB,UAAU,EAAE;gBAAO,CAAE;gBAAAnE,QAAA,GAAC,eAC9C,EAAC+D,KAAK,GAAG,CAAC,EAAC,IAAE,EAACD,OAAO,CAACM,aAAa,IAAIN,OAAO,CAACO,SAAS,IAAI,MAAM;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACJzQ,OAAA;gBAAGmQ,KAAK,EAAE;kBAAEY,MAAM,EAAE;gBAAQ,CAAE;gBAAAnD,QAAA,GAAC,gBACzB,EAAC,EAAAgE,mBAAA,GAAAF,OAAO,CAACQ,UAAU,cAAAN,mBAAA,uBAAlBA,mBAAA,CAAoBL,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,IAAI,EAAC,IAAE,EAAC,EAAAM,oBAAA,GAAAH,OAAO,CAACS,WAAW,cAAAN,oBAAA,uBAAnBA,oBAAA,CAAqBN,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAI,IAAI;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC,eACJzQ,OAAA;gBAAGmQ,KAAK,EAAE;kBAAEY,MAAM,EAAE;gBAAQ,CAAE;gBAAAnD,QAAA,GAAC,gBACzB,EAAC8D,OAAO,CAACpK,SAAS,IAAI,GAAG,EAAC,sBAC1B,EAACoK,OAAO,CAACnK,WAAW,IAAI,GAAG,EAAC,MAClC;cAAA;gBAAA+I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJzQ,OAAA;gBAAGmQ,KAAK,EAAE;kBAAEY,MAAM,EAAE;gBAAQ,CAAE;gBAAAnD,QAAA,GAAC,gBACzB,EACF8D,OAAO,CAACU,YAAY,KAAK,GAAG,GAAG,IAAI,GACnCV,OAAO,CAACU,YAAY,KAAK,GAAG,GAAG,KAAK,GACpCV,OAAO,CAACU,YAAY,KAAK,GAAG,GAAG,KAAK,GAAG,IAAI;cAAA;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE5C,CAAC;YAAA,GAtBIkB,KAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBV,CAAC;UAAA,CACP,CAAC,EAGD,EAAAxO,sBAAA,GAAAf,cAAc,CAAC2F,IAAI,CAACmB,YAAY,cAAA/F,sBAAA,uBAAhCA,sBAAA,CAAkCiG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAK,GAAG,CAAC,CAACC,MAAM,IAAG,CAAC,iBAC9ErI,OAAA;YAAGmQ,KAAK,EAAE;cAAEkC,SAAS,EAAE,QAAQ;cAAEP,SAAS,EAAE,KAAK;cAAE1N,QAAQ,EAAE;YAAO,CAAE;YAAAwJ,QAAA,GAAC,eAClE,EAAC1M,cAAc,CAAC2F,IAAI,CAACmB,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAK,GAAG,CAAC,CAACC,MAAM,GAAG,CAAC,EAAC,oCACrF;UAAA;YAAAiI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAENzQ,OAAA;QAAA4N,QAAA,EAAG;MAAY;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CACnB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA,eACD,CAAC;AAEP,CAAC;;AAED;AAAA9O,EAAA,CApvBMD,WAAW;AAAA4Q,EAAA,GAAX5Q,WAAW;AAqvBjB,SAAS6Q,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAACrC,KAAK,GAAG,GAAG;EAClBqC,MAAM,CAACpC,MAAM,GAAG,EAAE;;EAElB;EACAuC,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACP,SAAS,GAAG,QAAQ;;EAE5B;EACAO,OAAO,CAACI,QAAQ,CAACR,IAAI,EAAEC,MAAM,CAACrC,KAAK,GAAC,CAAC,EAAEqC,MAAM,CAACpC,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAM4C,OAAO,GAAG,IAAIxT,KAAK,CAACyT,aAAa,CAACT,MAAM,CAAC;EAC/C,MAAMU,cAAc,GAAG,IAAI1T,KAAK,CAAC2T,cAAc,CAAC;IAC9C1F,GAAG,EAAEuF,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAI7T,KAAK,CAAC8T,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAACE,KAAK,CAAC3I,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAOyI,MAAM;AACf;;AAEA;AACA5I,MAAM,CAAC+I,WAAW,GAAG,CAACxO,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EAChC,IAAIhF,gBAAgB,EAAE;IACpBA,gBAAgB,CAACoD,QAAQ,CAACsH,GAAG,CAAC5F,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACtChF,gBAAgB,CAAC0H,YAAY,CAAC,CAAC;IAC/B1H,gBAAgB,CAAC2H,iBAAiB,CAAC,IAAI,CAAC;IACxCzB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAACrB,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACAuF,MAAM,CAACgJ,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMlJ,MAAM,GAAGkI,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAIrJ,MAAM,EAAE;MACV;MACA,MAAMsJ,MAAM,GAAGtJ,MAAM,CAACjH,QAAQ,CAACqB,KAAK,CAAC,CAAC;;MAEtC;MACA4F,MAAM,CAACjH,QAAQ,CAACsH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BL,MAAM,CAAC1F,EAAE,CAAC+F,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBL,MAAM,CAAC3E,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACA2E,MAAM,CAAC3C,YAAY,CAAC,CAAC;MACrB2C,MAAM,CAAC1C,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMnH,QAAQ,GAAG+R,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIpT,QAAQ,EAAE;QACZA,QAAQ,CAACiF,MAAM,CAACiF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BlK,QAAQ,CAACmF,MAAM,CAAC,CAAC;MACnB;MAEAO,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxB0N,GAAG,EAAEF,MAAM,CAAC5E,OAAO,CAAC,CAAC;QACrB+E,GAAG,EAAEzJ,MAAM,CAACjH,QAAQ,CAAC2L,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOgF,CAAC,EAAE;IACV7N,OAAO,CAAC0B,KAAK,CAAC,YAAY,EAAEmM,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;AAED,eAAexS,WAAW;AAAC,IAAA4Q,EAAA;AAAA6B,YAAA,CAAA7B,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}