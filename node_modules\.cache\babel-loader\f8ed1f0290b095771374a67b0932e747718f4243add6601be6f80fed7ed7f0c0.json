{"ast": null, "code": "import toArray from \"rc-util/es/Children/toArray\";\nimport Sider from '../Sider';\nexport default function useHasSider(siders, children, hasSider) {\n  if (typeof hasSider === 'boolean') {\n    return hasSider;\n  }\n  if (siders.length) {\n    return true;\n  }\n  const childNodes = toArray(children);\n  return childNodes.some(node => node.type === Sider);\n}", "map": {"version": 3, "names": ["toArray", "<PERSON><PERSON>", "useHasSider", "siders", "children", "hasSider", "length", "childNodes", "some", "node", "type"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/layout/hooks/useHasSider.js"], "sourcesContent": ["import toArray from \"rc-util/es/Children/toArray\";\nimport Sider from '../Sider';\nexport default function useHasSider(siders, children, hasSider) {\n  if (typeof hasSider === 'boolean') {\n    return hasSider;\n  }\n  if (siders.length) {\n    return true;\n  }\n  const childNodes = toArray(children);\n  return childNodes.some(node => node.type === Sider);\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,6BAA6B;AACjD,OAAOC,KAAK,MAAM,UAAU;AAC5B,eAAe,SAASC,WAAWA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EAC9D,IAAI,OAAOA,QAAQ,KAAK,SAAS,EAAE;IACjC,OAAOA,QAAQ;EACjB;EACA,IAAIF,MAAM,CAACG,MAAM,EAAE;IACjB,OAAO,IAAI;EACb;EACA,MAAMC,UAAU,GAAGP,OAAO,CAACI,QAAQ,CAAC;EACpC,OAAOG,UAAU,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAKT,KAAK,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}