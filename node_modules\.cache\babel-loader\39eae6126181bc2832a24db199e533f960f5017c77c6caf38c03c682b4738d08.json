{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\SystemManagement.jsx\",\n  _s = $RefreshSig$();\n// src/pages/SystemManagement.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, message, Card, Result } from 'antd';\nimport { UserOutlined, LockOutlined, MailOutlined, StopOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport DeviceManagement from './DeviceManagement';\nimport styled from 'styled-components';\nimport VehicleManagement from './VehicleManagement';\nimport { useNavigate } from 'react-router-dom';\nimport devicesData from '../data/devices.json';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\n\n// 修改容器样式，确保整个组件在3D模型上层\nconst SystemContainer = styled.div`\n  position: relative;\n  z-index: 1002;\n  background: white;\n  pointer-events: auto;\n`;\n_c = SystemContainer;\nconst TabsContainer = styled.div`\n  display: flex;\n  margin-bottom: 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: white;\n  position: relative;\n  z-index: 1002;\n  pointer-events: auto;\n`;\n_c2 = TabsContainer;\nconst TabButton = styled.button`\n  padding: 10px 20px;\n  background: ${props => props.active ? '#e6f7ff' : 'white'};\n  border: none;\n  border-bottom: 2px solid ${props => props.active ? '#1890ff' : 'transparent'};\n  color: ${props => props.active ? '#1890ff' : 'rgba(0, 0, 0, 0.65)'};\n  cursor: pointer;\n  font-size: 14px;\n  margin-right: 20px;\n  transition: all 0.3s;\n  pointer-events: auto;\n  \n  &:hover {\n    color: #1890ff;\n  }\n  \n  &:focus {\n    outline: none;\n  }\n`;\n_c3 = TabButton;\nconst ContentArea = styled.div`\n  // padding: 24px;\n  background: white;\n  position: relative;\n  z-index: 1002;\n  // pointer-events: auto;\n`;\n_c4 = ContentArea;\nconst SystemManagement = () => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [users, setUsers] = useState([]);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingUser, setEditingUser] = useState(null);\n  const [activeTab, setActiveTab] = useState('users');\n  const [intersections, setIntersections] = useState([]);\n  const [intersectionModalVisible, setIntersectionModalVisible] = useState(false);\n  const [editingIntersection, setEditingIntersection] = useState(null);\n  const [intersectionForm] = Form.useForm();\n  const [hasPermission, setHasPermission] = useState(false);\n\n  // 检查用户权限\n  useEffect(() => {\n    const checkPermission = () => {\n      try {\n        var _userData$user;\n        const userInfo = localStorage.getItem('user');\n        if (!userInfo) {\n          setHasPermission(false);\n          return;\n        }\n        const userData = JSON.parse(userInfo);\n        const userRole = userData.role || ((_userData$user = userData.user) === null || _userData$user === void 0 ? void 0 : _userData$user.role) || 'user';\n        if (userRole === 'admin') {\n          setHasPermission(true);\n        } else {\n          setHasPermission(false);\n          message.error('您没有权限访问系统管理页面');\n        }\n      } catch (error) {\n        console.error('检查权限失败:', error);\n        setHasPermission(false);\n      }\n    };\n    checkPermission();\n  }, []);\n\n  // 获取用户列表\n  const fetchUsers = async () => {\n    if (!hasPermission) return; // 没有权限时不请求数据\n\n    try {\n      setLoading(true);\n      console.log('开始获取用户列表...');\n      const token = localStorage.getItem('token');\n      console.log('使用令牌获取用户列表:', token);\n\n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/users`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      console.log('API响应:', response.data);\n      if (response.data && response.data.success) {\n        setUsers(response.data.data || []);\n      } else {\n        var _response$data;\n        message.warning('获取用户列表失败: ' + (((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || '未知错误'));\n      }\n    } catch (error) {\n      console.error('API获取用户列表失败:', error);\n      message.error('获取用户列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 提前合并两个useEffect以避免条件渲染导致的钩子数量不一致\n  useEffect(() => {\n    if (hasPermission) {\n      fetchUsers();\n      fetchIntersections();\n\n      // 从 devices.json 中获取唯一的路口名称，排除 OBU 类型设备的位置\n      const uniqueLocations = [...new Set(devicesData.devices.filter(device => device.type !== 'obu') // 排除 OBU 类型设备\n      .map(device => device.location))];\n\n      // 从 intersections.json 中读取现有的路口数据\n      // const existingIntersections = intersectionsData.intersections || [];\n      const existingIntersections = intersections || [];\n      // 将路口名称与已有的路口数据合并\n      const mergedIntersections = uniqueLocations.map(location => {\n        const existingIntersection = intersections.find(i => i.name === location);\n        if (existingIntersection) {\n          return existingIntersection;\n        }\n        // 如果是新路口，先保存到 intersections.json\n        const newIntersection = {\n          id: `intersection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          name: location,\n          latitude: '',\n          // 必须有\n          longitude: '',\n          // 必须有\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n          interId: '',\n          hasTrafficLight: false,\n          entrances: []\n        };\n        // 调用 API 保存新路口\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        axios.post(`${apiUrl}/api/intersections`, newIntersection).catch(error => {\n          console.error('保存新路口失败:', error);\n        });\n        return newIntersection;\n      });\n      setIntersections(mergedIntersections);\n    }\n  }, [activeTab, hasPermission]);\n\n  // 渲染无权限页面或主要内容\n  if (!hasPermission) {\n    return /*#__PURE__*/_jsxDEV(SystemContainer, {\n      children: /*#__PURE__*/_jsxDEV(Result, {\n        status: \"403\",\n        title: \"403\",\n        subTitle: \"\\u62B1\\u6B49\\uFF0C\\u60A8\\u6CA1\\u6709\\u6743\\u9650\\u8BBF\\u95EE\\u6B64\\u9875\\u9762\",\n        icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 17\n        }, this),\n        extra: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          onClick: () => navigate('/'),\n          children: \"\\u8FD4\\u56DE\\u9996\\u9875\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 以下代码只有在hasPermission为true时才会执行\n\n  // 修改标签切换处理函数\n  const handleTabChange = tab => {\n    console.log('切换到标签:', tab);\n    setActiveTab(tab);\n\n    // 切换到设备管理标签时重新获取设备列表\n    if (tab === 'devices') {\n      const deviceManagementRef = document.querySelector('#device-management');\n      if (deviceManagementRef) {\n        deviceManagementRef.fetchDevices();\n      }\n    }\n  };\n  const handleAddUser = () => {\n    setEditingUser(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEditUser = user => {\n    setEditingUser(user);\n    form.setFieldsValue({\n      username: user.username,\n      role: user.role,\n      email: user.email\n    });\n    setModalVisible(true);\n  };\n  const handleDeleteUser = async userId => {\n    try {\n      const token = localStorage.getItem('token');\n\n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n      // 尝试通过API删除\n      try {\n        await axios.delete(`${apiUrl}/api/users/${userId}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        message.success('用户已删除');\n        fetchUsers(); // 重新获取用户列表\n      } catch (error) {\n        console.error('API删除用户失败:', error);\n\n        // 如果API删除失败，从本地存储中删除\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        const updatedUsers = localUsers.filter(user => user.id !== userId);\n        localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        message.success('用户已删除（本地存储）');\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('删除用户失败:', error);\n      message.error('删除用户失败: ' + (error.message || '未知错误'));\n    }\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      console.log('表单数据:', values);\n      const token = localStorage.getItem('token');\n\n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n      // 尝试通过API保存用户\n      try {\n        let response;\n        if (editingUser) {\n          // 更新用户\n          response = await axios.put(`${apiUrl}/api/users/${editingUser.id}`, values, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        } else {\n          // 添加用户\n          response = await axios.post(`${apiUrl}/api/users`, values, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        }\n        console.log('API响应:', response.data);\n        if (response.data && response.data.success) {\n          message.success(editingUser ? '用户已更新' : '用户已添加');\n          setModalVisible(false);\n          form.resetFields();\n          fetchUsers(); // 重新获取用户列表\n        } else {\n          var _response$data2;\n          message.error(((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || '操作失败');\n        }\n      } catch (error) {\n        console.error('API保存用户失败:', error);\n\n        // 如果API保存失败，保存到本地存储\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        if (editingUser) {\n          // 更新用户\n          const updatedUsers = localUsers.map(user => user.id === editingUser.id ? {\n            ...user,\n            ...values\n          } : user);\n          localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        } else {\n          // 添加用户\n          const newUser = {\n            ...values,\n            id: Date.now().toString(),\n            createdAt: new Date().toISOString()\n          };\n          localUsers.push(newUser);\n          localStorage.setItem('localUsers', JSON.stringify(localUsers));\n        }\n        message.success(editingUser ? '用户已更新（本地存储）' : '用户已添加（本地存储）');\n        setModalVisible(false);\n        form.resetFields();\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('保存用户失败:', error);\n      message.error('表单验证失败');\n    }\n  };\n  const handleModalCancel = () => {\n    setModalVisible(false);\n  };\n  const columns = [{\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username'\n  }, {\n    title: '角色',\n    dataIndex: 'role',\n    key: 'role',\n    render: role => {\n      const roleMap = {\n        'admin': '管理员',\n        'monitor': '监控人员',\n        'user': '普通用户',\n        'maintenance': '设备维护人员'\n      };\n      return roleMap[role] || role;\n    }\n  }, {\n    title: '邮箱',\n    dataIndex: 'email',\n    key: 'email'\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    render: date => date ? new Date(date).toLocaleString() : '-'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleEditUser(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        danger: true,\n        onClick: () => handleDeleteUser(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 在组件内添加路口管理相关函数\n  const handleAddIntersection = () => {\n    setEditingIntersection(null);\n    intersectionForm.resetFields();\n    setIntersectionModalVisible(true);\n  };\n  const handleEditIntersection = intersection => {\n    setEditingIntersection(intersection);\n    intersectionForm.setFieldsValue({\n      ...intersection,\n      entrances: intersection.entrances || []\n    });\n    setIntersectionModalVisible(true);\n  };\n  const handleDeleteIntersection = async id => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      await axios.delete(`${apiUrl}/api/intersections/${id}`);\n      message.success('路口删除成功');\n      // 更新路口列表\n      const updatedIntersections = intersections.filter(item => item.id !== id);\n      setIntersections(updatedIntersections);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('删除路口失败:', error);\n      message.error('删除路口失败: ' + (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || '未知错误'));\n    }\n  };\n\n  // 获取路口列表\n  const fetchIntersections = async () => {\n    try {\n      setLoading(true);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/intersections`);\n      if (response.data && response.data.data) {\n        setIntersections(response.data.data);\n      }\n    } catch (error) {\n      console.error('获取路口列表失败:', error);\n      message.error('获取路口列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 更新handleIntersectionModalOk函数\n  const handleIntersectionModalOk = async () => {\n    try {\n      const values = await intersectionForm.validateFields();\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n      // 确保hasTrafficLight字段被正确处理为布尔值\n      if (values.hasTrafficLight !== undefined) {\n        values.hasTrafficLight = Boolean(values.hasTrafficLight);\n      }\n      if (editingIntersection) {\n        // 更新路口\n        const response = await axios.put(`${apiUrl}/api/intersections/${editingIntersection.id}`, values);\n        if (response.data && response.data.success) {\n          message.success('路口更新成功');\n          // 更新路口列表\n          const updatedIntersections = intersections.map(item => item.id === editingIntersection.id ? {\n            ...item,\n            ...values\n          } : item);\n          setIntersections(updatedIntersections);\n\n          // 更新本地JSON文件\n          try {\n            // 读取当前的intersections.json内容\n            const existingIntersections = intersections || [];\n            // 查找并更新对应路口\n            const updatedIntersectionsData = existingIntersections.map(item => item.id === editingIntersection.id ? {\n              ...item,\n              ...values\n            } : item);\n\n            // 尝试通过API更新JSON文件\n            await axios.post(`${apiUrl}/api/config/update`, {\n              file: 'intersections.json',\n              data: {\n                intersections: updatedIntersectionsData\n              }\n            });\n            console.log('路口数据已同步到JSON文件');\n          } catch (jsonError) {\n            console.error('更新JSON文件失败:', jsonError);\n            message.warning('路口数据已更新，但未能同步到配置文件');\n          }\n        }\n      } else {\n        // 添加新路口\n        const newIntersection = {\n          ...values,\n          id: `intersection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n          hasTrafficLight: values.hasTrafficLight === undefined ? false : Boolean(values.hasTrafficLight)\n        };\n\n        // 如果没有提供interId，则自动生成一个\n        if (!newIntersection.interId) {\n          newIntersection.interId = `I${newIntersection.id.substr(-3)}`;\n        }\n        const response = await axios.post(`${apiUrl}/api/intersections`, newIntersection);\n        if (response.data && response.data.success) {\n          message.success('路口添加成功');\n          setIntersections([...intersections, newIntersection]);\n\n          // 更新本地JSON文件\n          try {\n            // 读取当前的intersections.json内容\n            const existingIntersections = intersections || [];\n            // 添加新路口\n            const updatedIntersectionsData = [...existingIntersections, newIntersection];\n\n            // 尝试通过API更新JSON文件\n            await axios.post(`${apiUrl}/api/config/update`, {\n              file: 'intersections.json',\n              data: {\n                intersections: updatedIntersectionsData\n              }\n            });\n            console.log('新路口数据已添加到JSON文件');\n          } catch (jsonError) {\n            console.error('更新JSON文件失败:', jsonError);\n            message.warning('路口已添加，但未能同步到配置文件');\n          }\n        }\n      }\n      setIntersectionModalVisible(false);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('保存路口失败:', error);\n      message.error('保存路口失败: ' + (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || '未知错误'));\n    }\n  };\n\n  // 修改 renderContent 函数\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'users':\n        return /*#__PURE__*/_jsxDEV(ContentArea, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'flex-end',\n              marginBottom: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              onClick: handleAddUser,\n              children: \"\\u6DFB\\u52A0\\u7528\\u6237\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Table, {\n            loading: loading,\n            dataSource: users,\n            columns: columns,\n            rowKey: \"id\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 11\n        }, this);\n      case 'devices':\n        return /*#__PURE__*/_jsxDEV(DeviceManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 16\n        }, this);\n      case 'intersections':\n        return /*#__PURE__*/_jsxDEV(ContentArea, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'flex-end',\n              marginBottom: 60\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 12\n          }, this), /*#__PURE__*/_jsxDEV(Table, {\n            loading: loading,\n            dataSource: intersections,\n            columns: [{\n              title: '路口名称',\n              dataIndex: 'name',\n              key: 'name'\n            }, {\n              title: '路口ID',\n              dataIndex: 'interId',\n              key: 'interId'\n            }, {\n              title: '纬度',\n              dataIndex: 'latitude',\n              key: 'latitude'\n            }, {\n              title: '经度',\n              dataIndex: 'longitude',\n              key: 'longitude'\n            }, {\n              title: '是否有红绿灯',\n              dataIndex: 'hasTrafficLight',\n              key: 'hasTrafficLight',\n              render: hasLight => hasLight ? '有' : '无'\n            }, {\n              title: '操作',\n              key: 'action',\n              render: (_, record) => /*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  onClick: () => handleEditIntersection(record),\n                  children: \"\\u7F16\\u8F91\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 21\n              }, this)\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 11\n        }, this);\n      case 'vehicles':\n        return /*#__PURE__*/_jsxDEV(VehicleManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(SystemContainer, {\n    children: [/*#__PURE__*/_jsxDEV(TabsContainer, {\n      children: [/*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'users',\n        onClick: () => handleTabChange('users'),\n        children: \"\\u7528\\u6237\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 617,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'devices',\n        onClick: () => handleTabChange('devices'),\n        children: \"\\u8BBE\\u5907\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'intersections',\n        onClick: () => handleTabChange('intersections'),\n        children: \"\\u8DEF\\u53E3\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'vehicles',\n        onClick: () => handleTabChange('vehicles'),\n        children: \"\\u8F66\\u8F86\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 616,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContentArea, {\n      children: renderContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 643,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingUser ? \"编辑用户\" : \"添加用户\",\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: handleModalCancel,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          role: 'user'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"username\",\n          label: \"\\u7528\\u6237\\u540D\",\n          rules: [{\n            required: true,\n            message: '请输入用户名'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 28\n            }, this),\n            placeholder: \"\\u7528\\u6237\\u540D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 11\n        }, this), !editingUser && /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          label: \"\\u5BC6\\u7801\",\n          rules: [{\n            required: true,\n            message: '请输入密码'\n          }, {\n            min: 6,\n            message: '密码长度至少为6个字符'\n          }],\n          extra: \"\\u5BC6\\u7801\\u957F\\u5EA6\\u81F3\\u5C11\\u4E3A6\\u4E2A\\u5B57\\u7B26\",\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 39\n            }, this),\n            placeholder: \"\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 679,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"role\",\n          label: \"\\u89D2\\u8272\",\n          rules: [{\n            required: true,\n            message: '请选择角色'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u89D2\\u8272\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"admin\",\n              children: \"\\u7BA1\\u7406\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"monitor\",\n              children: \"\\u76D1\\u63A7\\u4EBA\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"user\",\n              children: \"\\u666E\\u901A\\u7528\\u6237\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 691,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"maintenance\",\n              children: \"\\u8BBE\\u5907\\u7EF4\\u62A4\\u4EBA\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"email\",\n          label: \"\\u90AE\\u7BB1\",\n          rules: [{\n            type: 'email',\n            message: '请输入有效的邮箱地址'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 28\n            }, this),\n            placeholder: \"\\u90AE\\u7BB1\\uFF08\\u9009\\u586B\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 654,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 647,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingIntersection ? '编辑路口' : '添加路口',\n      open: intersectionModalVisible,\n      onOk: handleIntersectionModalOk,\n      onCancel: () => setIntersectionModalVisible(false),\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: intersectionForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8DEF\\u53E3\\u540D\\u79F0\"\n          // rules={[{ required: true, message: '请输入路口名称' }]}\n          ,\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            disabled: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"interId\",\n          label: \"\\u8DEF\\u53E3ID\",\n          rules: [{\n            required: true,\n            message: '请输入路口ID'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8DEF\\u53E3ID\\uFF08\\u59821\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 732,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"latitude\",\n          label: \"\\u7EAC\\u5EA6\",\n          rules: [{\n            required: true,\n            message: '请输入纬度'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u7EAC\\u5EA6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"longitude\",\n          label: \"\\u7ECF\\u5EA6\",\n          rules: [{\n            required: true,\n            message: '请输入经度'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u7ECF\\u5EA6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"hasTrafficLight\",\n          label: \"\\u662F\\u5426\\u6709\\u7EA2\\u7EFF\\u706F\",\n          valuePropName: \"checked\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u662F\\u5426\\u6709\\u7EA2\\u7EFF\\u706F\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: true,\n              children: \"\\u6709\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: false,\n              children: \"\\u65E0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 748,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.List, {\n          name: \"entrances\",\n          children: fields => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: 'bold',\n                margin: '8px 0 4px 0'\n              },\n              children: \"\\u8FDB\\u53E3\\u4FE1\\u606F\\uFF1A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 17\n            }, this), fields.map(({\n              key,\n              name,\n              ...restField\n            }) => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: 8,\n                marginBottom: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                ...restField,\n                name: [name, 'name'],\n                label: \"\\u8FDB\\u53E3\\u540D\\u79F0\",\n                style: {\n                  flex: 1,\n                  marginBottom: 0\n                },\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  disabled: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                ...restField,\n                name: [name, 'latitude'],\n                label: \"\\u7EAC\\u5EA6\",\n                style: {\n                  flex: 1,\n                  marginBottom: 0\n                },\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u7EAC\\u5EA6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 778,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                ...restField,\n                name: [name, 'longitude'],\n                label: \"\\u7ECF\\u5EA6\",\n                style: {\n                  flex: 1,\n                  marginBottom: 0\n                },\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u7ECF\\u5EA6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 21\n              }, this)]\n            }, key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 758,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 715,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 709,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 615,\n    columnNumber: 5\n  }, this);\n};\n_s(SystemManagement, \"GzMHodrygKCeoqlqfsdpYbV2cEA=\", false, function () {\n  return [useNavigate, Form.useForm, Form.useForm];\n});\n_c5 = SystemManagement;\nexport default SystemManagement;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"SystemContainer\");\n$RefreshReg$(_c2, \"TabsContainer\");\n$RefreshReg$(_c3, \"TabButton\");\n$RefreshReg$(_c4, \"ContentArea\");\n$RefreshReg$(_c5, \"SystemManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "message", "Card", "Result", "UserOutlined", "LockOutlined", "MailOutlined", "StopOutlined", "axios", "DeviceManagement", "styled", "VehicleManagement", "useNavigate", "devicesData", "jsxDEV", "_jsxDEV", "Option", "SystemContainer", "div", "_c", "TabsContainer", "_c2", "TabButton", "button", "props", "active", "_c3", "ContentArea", "_c4", "SystemManagement", "_s", "navigate", "loading", "setLoading", "users", "setUsers", "modalVisible", "setModalVisible", "form", "useForm", "editingUser", "setEditingUser", "activeTab", "setActiveTab", "intersections", "setIntersections", "intersectionModalVisible", "setIntersectionModalVisible", "editingIntersection", "setEditingIntersection", "intersectionForm", "hasPermission", "setHasPermission", "checkPermission", "_userData$user", "userInfo", "localStorage", "getItem", "userData", "JSON", "parse", "userRole", "role", "user", "error", "console", "fetchUsers", "log", "token", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "headers", "data", "success", "_response$data", "warning", "fetchIntersections", "uniqueLocations", "Set", "devices", "filter", "device", "type", "map", "location", "existingIntersections", "mergedIntersections", "existingIntersection", "find", "i", "name", "newIntersection", "id", "Date", "now", "Math", "random", "toString", "substr", "latitude", "longitude", "createdAt", "toISOString", "updatedAt", "interId", "hasTrafficLight", "entrances", "post", "catch", "children", "status", "title", "subTitle", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "extra", "onClick", "handleTabChange", "tab", "deviceManagementRef", "document", "querySelector", "fetchDevices", "handleAddUser", "resetFields", "handleEditUser", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "username", "email", "handleDeleteUser", "userId", "delete", "localUsers", "updatedUsers", "setItem", "stringify", "handleModalOk", "values", "validateFields", "put", "_response$data2", "newUser", "push", "handleModalCancel", "columns", "dataIndex", "key", "render", "roleMap", "date", "toLocaleString", "_", "record", "danger", "handleAddIntersection", "handleEditIntersection", "intersection", "handleDeleteIntersection", "updatedIntersections", "item", "_error$response", "_error$response$data", "handleIntersectionModalOk", "undefined", "Boolean", "updatedIntersectionsData", "file", "jsonError", "_error$response2", "_error$response2$data", "renderContent", "style", "display", "justifyContent", "marginBottom", "dataSource", "<PERSON><PERSON><PERSON>", "hasLight", "open", "onOk", "onCancel", "destroyOnClose", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "prefix", "placeholder", "min", "Password", "value", "disabled", "valuePropName", "List", "fields", "fontWeight", "margin", "restField", "gap", "flex", "_c5", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/SystemManagement.jsx"], "sourcesContent": ["// src/pages/SystemManagement.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, message, Card, Result } from 'antd';\nimport { UserOutlined, LockOutlined, MailOutlined, StopOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport DeviceManagement from './DeviceManagement';\nimport styled from 'styled-components';\nimport VehicleManagement from './VehicleManagement';\nimport { useNavigate } from 'react-router-dom';\nimport devicesData from '../data/devices.json';\nconst { Option } = Select;\n\n// 修改容器样式，确保整个组件在3D模型上层\nconst SystemContainer = styled.div`\n  position: relative;\n  z-index: 1002;\n  background: white;\n  pointer-events: auto;\n`;\n\nconst TabsContainer = styled.div`\n  display: flex;\n  margin-bottom: 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: white;\n  position: relative;\n  z-index: 1002;\n  pointer-events: auto;\n`;\n\nconst TabButton = styled.button`\n  padding: 10px 20px;\n  background: ${props => props.active ? '#e6f7ff' : 'white'};\n  border: none;\n  border-bottom: 2px solid ${props => props.active ? '#1890ff' : 'transparent'};\n  color: ${props => props.active ? '#1890ff' : 'rgba(0, 0, 0, 0.65)'};\n  cursor: pointer;\n  font-size: 14px;\n  margin-right: 20px;\n  transition: all 0.3s;\n  pointer-events: auto;\n  \n  &:hover {\n    color: #1890ff;\n  }\n  \n  &:focus {\n    outline: none;\n  }\n`;\n\nconst ContentArea = styled.div`\n  // padding: 24px;\n  background: white;\n  position: relative;\n  z-index: 1002;\n  // pointer-events: auto;\n`;\n\nconst SystemManagement = () => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [users, setUsers] = useState([]);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingUser, setEditingUser] = useState(null);\n  const [activeTab, setActiveTab] = useState('users');\n  const [intersections, setIntersections] = useState([]);\n  const [intersectionModalVisible, setIntersectionModalVisible] = useState(false);\n  const [editingIntersection, setEditingIntersection] = useState(null);\n  const [intersectionForm] = Form.useForm();\n  const [hasPermission, setHasPermission] = useState(false);\n\n  // 检查用户权限\n  useEffect(() => {\n    const checkPermission = () => {\n      try {\n        const userInfo = localStorage.getItem('user');\n        if (!userInfo) {\n          setHasPermission(false);\n          return;\n        }\n\n        const userData = JSON.parse(userInfo);\n        const userRole = userData.role || userData.user?.role || 'user';\n        \n        if (userRole === 'admin') {\n          setHasPermission(true);\n        } else {\n          setHasPermission(false);\n          message.error('您没有权限访问系统管理页面');\n        }\n      } catch (error) {\n        console.error('检查权限失败:', error);\n        setHasPermission(false);\n      }\n    };\n\n    checkPermission();\n  }, []);\n\n  // 获取用户列表\n  const fetchUsers = async () => {\n    if (!hasPermission) return; // 没有权限时不请求数据\n    \n    try {\n      setLoading(true);\n      console.log('开始获取用户列表...');\n      \n      const token = localStorage.getItem('token');\n      console.log('使用令牌获取用户列表:', token);\n      \n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/users`, {\n        headers: { \n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      \n      console.log('API响应:', response.data);\n      \n      if (response.data && response.data.success) {\n        setUsers(response.data.data || []);\n      } else {\n        message.warning('获取用户列表失败: ' + (response.data?.message || '未知错误'));\n      }\n    } catch (error) {\n      console.error('API获取用户列表失败:', error);\n      message.error('获取用户列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 提前合并两个useEffect以避免条件渲染导致的钩子数量不一致\n  useEffect(() => {\n    if (hasPermission) {\n      fetchUsers();\n      fetchIntersections();\n      \n      // 从 devices.json 中获取唯一的路口名称，排除 OBU 类型设备的位置\n      const uniqueLocations = [...new Set(\n        devicesData.devices\n          .filter(device => device.type !== 'obu') // 排除 OBU 类型设备\n          .map(device => device.location)\n      )];\n      \n      // 从 intersections.json 中读取现有的路口数据\n      // const existingIntersections = intersectionsData.intersections || [];\n      const existingIntersections = intersections || [];\n      // 将路口名称与已有的路口数据合并\n      const mergedIntersections = uniqueLocations.map(location => {\n        const existingIntersection = intersections.find(i => i.name === location);\n        if (existingIntersection) {\n          return existingIntersection;\n        }\n        // 如果是新路口，先保存到 intersections.json\n        const newIntersection = {\n          id: `intersection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          name: location,\n          latitude: '', // 必须有\n          longitude: '', // 必须有\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n          interId: '',\n          hasTrafficLight: false,\n          entrances: []\n        };\n        // 调用 API 保存新路口\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        axios.post(`${apiUrl}/api/intersections`, newIntersection)\n          .catch(error => {\n            console.error('保存新路口失败:', error);\n          });\n        return newIntersection;\n      });\n      \n      setIntersections(mergedIntersections);\n    }\n  }, [activeTab, hasPermission]);\n\n  // 渲染无权限页面或主要内容\n  if (!hasPermission) {\n    return (\n      <SystemContainer>\n        <Result\n          status=\"403\"\n          title=\"403\"\n          subTitle=\"抱歉，您没有权限访问此页面\"\n          icon={<StopOutlined />}\n          extra={\n            <Button type=\"primary\" onClick={() => navigate('/')}>\n              返回首页\n            </Button>\n          }\n        />\n      </SystemContainer>\n    );\n  }\n\n  // 以下代码只有在hasPermission为true时才会执行\n\n  // 修改标签切换处理函数\n  const handleTabChange = (tab) => {\n    console.log('切换到标签:', tab);\n    setActiveTab(tab);\n    \n    // 切换到设备管理标签时重新获取设备列表\n    if (tab === 'devices') {\n      const deviceManagementRef = document.querySelector('#device-management');\n      if (deviceManagementRef) {\n        deviceManagementRef.fetchDevices();\n      }\n    }\n  };\n\n  const handleAddUser = () => {\n    setEditingUser(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEditUser = (user) => {\n    setEditingUser(user);\n    form.setFieldsValue({\n      username: user.username,\n      role: user.role,\n      email: user.email,\n    });\n    setModalVisible(true);\n  };\n\n  const handleDeleteUser = async (userId) => {\n    try {\n      const token = localStorage.getItem('token');\n      \n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      \n      // 尝试通过API删除\n      try {\n        await axios.delete(`${apiUrl}/api/users/${userId}`, {\n          headers: { \n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        message.success('用户已删除');\n        fetchUsers(); // 重新获取用户列表\n      } catch (error) {\n        console.error('API删除用户失败:', error);\n        \n        // 如果API删除失败，从本地存储中删除\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        const updatedUsers = localUsers.filter(user => user.id !== userId);\n        localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        \n        message.success('用户已删除（本地存储）');\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('删除用户失败:', error);\n      message.error('删除用户失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      console.log('表单数据:', values);\n      \n      const token = localStorage.getItem('token');\n      \n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      \n      // 尝试通过API保存用户\n      try {\n        let response;\n        \n        if (editingUser) {\n          // 更新用户\n          response = await axios.put(`${apiUrl}/api/users/${editingUser.id}`, values, {\n            headers: { \n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        } else {\n          // 添加用户\n          response = await axios.post(`${apiUrl}/api/users`, values, {\n            headers: { \n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        }\n        \n        console.log('API响应:', response.data);\n        \n        if (response.data && response.data.success) {\n          message.success(editingUser ? '用户已更新' : '用户已添加');\n          setModalVisible(false);\n          form.resetFields();\n          fetchUsers(); // 重新获取用户列表\n        } else {\n          message.error(response.data?.message || '操作失败');\n        }\n      } catch (error) {\n        console.error('API保存用户失败:', error);\n        \n        // 如果API保存失败，保存到本地存储\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        \n        if (editingUser) {\n          // 更新用户\n          const updatedUsers = localUsers.map(user => \n            user.id === editingUser.id ? { ...user, ...values } : user\n          );\n          localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        } else {\n          // 添加用户\n          const newUser = {\n            ...values,\n            id: Date.now().toString(),\n            createdAt: new Date().toISOString()\n          };\n          localUsers.push(newUser);\n          localStorage.setItem('localUsers', JSON.stringify(localUsers));\n        }\n        \n        message.success(editingUser ? '用户已更新（本地存储）' : '用户已添加（本地存储）');\n        setModalVisible(false);\n        form.resetFields();\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('保存用户失败:', error);\n      message.error('表单验证失败');\n    }\n  };\n\n  const handleModalCancel = () => {\n    setModalVisible(false);\n  };\n\n  const columns = [\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n    },\n    {\n      title: '角色',\n      dataIndex: 'role',\n      key: 'role',\n      render: (role) => {\n        const roleMap = {\n          'admin': '管理员',\n          'monitor': '监控人员',\n          'user': '普通用户',\n          'maintenance': '设备维护人员'\n        };\n        return roleMap[role] || role;\n      }\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      render: date => date ? new Date(date).toLocaleString() : '-'\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <div>\n          <Button type=\"link\" onClick={() => handleEditUser(record)}>编辑</Button>\n          <Button type=\"link\" danger onClick={() => handleDeleteUser(record.id)}>删除</Button>\n        </div>\n      ),\n    },\n  ];\n\n  // 在组件内添加路口管理相关函数\n  const handleAddIntersection = () => {\n    setEditingIntersection(null);\n    intersectionForm.resetFields();\n    setIntersectionModalVisible(true);\n  };\n\n  const handleEditIntersection = (intersection) => {\n    setEditingIntersection(intersection);\n    intersectionForm.setFieldsValue({\n      ...intersection,\n      entrances: intersection.entrances || []\n    });\n    setIntersectionModalVisible(true);\n  };\n\n  const handleDeleteIntersection = async (id) => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      await axios.delete(`${apiUrl}/api/intersections/${id}`);\n      message.success('路口删除成功');\n      // 更新路口列表\n      const updatedIntersections = intersections.filter(item => item.id !== id);\n      setIntersections(updatedIntersections);\n    } catch (error) {\n      console.error('删除路口失败:', error);\n      message.error('删除路口失败: ' + (error.response?.data?.message || '未知错误'));\n    }\n  };\n\n  // 获取路口列表\n  const fetchIntersections = async () => {\n    try {\n      setLoading(true);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/intersections`);\n      if (response.data && response.data.data) {\n        setIntersections(response.data.data);\n      }\n    } catch (error) {\n      console.error('获取路口列表失败:', error);\n      message.error('获取路口列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 更新handleIntersectionModalOk函数\n  const handleIntersectionModalOk = async () => {\n    try {\n      const values = await intersectionForm.validateFields();\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n      // 确保hasTrafficLight字段被正确处理为布尔值\n      if (values.hasTrafficLight !== undefined) {\n        values.hasTrafficLight = Boolean(values.hasTrafficLight);\n      }\n\n      if (editingIntersection) {\n        // 更新路口\n        const response = await axios.put(\n          `${apiUrl}/api/intersections/${editingIntersection.id}`,\n          values\n        );\n        if (response.data && response.data.success) {\n          message.success('路口更新成功');\n          // 更新路口列表\n          const updatedIntersections = intersections.map(item =>\n            item.id === editingIntersection.id ? { ...item, ...values } : item\n          );\n          setIntersections(updatedIntersections);\n          \n          // 更新本地JSON文件\n          try {\n            // 读取当前的intersections.json内容\n            const existingIntersections = intersections || [];\n            // 查找并更新对应路口\n            const updatedIntersectionsData = existingIntersections.map(item => \n              item.id === editingIntersection.id ? { ...item, ...values } : item\n            );\n            \n            // 尝试通过API更新JSON文件\n            await axios.post(`${apiUrl}/api/config/update`, {\n              file: 'intersections.json',\n              data: { intersections: updatedIntersectionsData }\n            });\n            \n            console.log('路口数据已同步到JSON文件');\n          } catch (jsonError) {\n            console.error('更新JSON文件失败:', jsonError);\n            message.warning('路口数据已更新，但未能同步到配置文件');\n          }\n        }\n      } else {\n        // 添加新路口\n        const newIntersection = {\n          ...values,\n          id: `intersection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n          hasTrafficLight: values.hasTrafficLight === undefined ? false : Boolean(values.hasTrafficLight)\n        };\n        \n        // 如果没有提供interId，则自动生成一个\n        if (!newIntersection.interId) {\n          newIntersection.interId = `I${newIntersection.id.substr(-3)}`;\n        }\n        \n        const response = await axios.post(`${apiUrl}/api/intersections`, newIntersection);\n        if (response.data && response.data.success) {\n          message.success('路口添加成功');\n          setIntersections([...intersections, newIntersection]);\n          \n          // 更新本地JSON文件\n          try {\n            // 读取当前的intersections.json内容\n            const existingIntersections = intersections || [];\n            // 添加新路口\n            const updatedIntersectionsData = [...existingIntersections, newIntersection];\n            \n            // 尝试通过API更新JSON文件\n            await axios.post(`${apiUrl}/api/config/update`, {\n              file: 'intersections.json',\n              data: { intersections: updatedIntersectionsData }\n            });\n            \n            console.log('新路口数据已添加到JSON文件');\n          } catch (jsonError) {\n            console.error('更新JSON文件失败:', jsonError);\n            message.warning('路口已添加，但未能同步到配置文件');\n          }\n        }\n      }\n      setIntersectionModalVisible(false);\n    } catch (error) {\n      console.error('保存路口失败:', error);\n      message.error('保存路口失败: ' + (error.response?.data?.message || '未知错误'));\n    }\n  };\n\n  // 修改 renderContent 函数\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'users':\n        return (\n          <ContentArea>\n            <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 16 }}>\n              <Button type=\"primary\" onClick={handleAddUser}>\n                添加用户\n              </Button>\n            </div>\n            <Table\n              loading={loading}\n              dataSource={users}\n              columns={columns}\n              rowKey=\"id\"\n            />\n          </ContentArea>\n        );\n      case 'devices':\n        return <DeviceManagement />;\n      case 'intersections':\n        return (\n          <ContentArea>\n           <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 60 }}>\n            {/* <Button type=\"primary\" onClick={handleAddIntersection}> */}\n              {/* <Button type=\"primary\" >\n                添加路口\n              </Button> */}\n              </div>\n            <Table\n              loading={loading}\n              dataSource={intersections}\n              columns={[\n                {\n                  title: '路口名称',\n                  dataIndex: 'name',\n                  key: 'name',\n                },\n                {\n                  title: '路口ID',\n                  dataIndex: 'interId',\n                  key: 'interId',\n                },\n                {\n                  title: '纬度',\n                  dataIndex: 'latitude',\n                  key: 'latitude',\n                },\n                {\n                  title: '经度',\n                  dataIndex: 'longitude',\n                  key: 'longitude',\n                },\n                {\n                  title: '是否有红绿灯',\n                  dataIndex: 'hasTrafficLight',\n                  key: 'hasTrafficLight',\n                  render: (hasLight) => hasLight ? '有' : '无'\n                },\n                {\n                  title: '操作',\n                  key: 'action',\n                  render: (_, record) => (\n                    <span>\n                      <Button type=\"link\" onClick={() => handleEditIntersection(record)}>\n                        编辑\n                      </Button>\n                    </span>\n                  ),\n                },\n              ]}\n            />\n          </ContentArea>\n        );\n      case 'vehicles':\n        return <VehicleManagement />;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <SystemContainer>\n      <TabsContainer>\n        <TabButton\n          active={activeTab === 'users'}\n          onClick={() => handleTabChange('users')}\n        >\n          用户管理\n        </TabButton>\n        <TabButton\n          active={activeTab === 'devices'}\n          onClick={() => handleTabChange('devices')}\n        >\n          设备管理\n        </TabButton>\n        <TabButton\n          active={activeTab === 'intersections'}\n          onClick={() => handleTabChange('intersections')}\n        >\n          路口管理\n        </TabButton>\n        <TabButton\n          active={activeTab === 'vehicles'}\n          onClick={() => handleTabChange('vehicles')}\n        >\n          车辆管理\n        </TabButton>\n      </TabsContainer>\n      \n      <ContentArea>\n        {renderContent()}\n      </ContentArea>\n      \n      <Modal\n        title={editingUser ? \"编辑用户\" : \"添加用户\"}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            role: 'user'\n          }}\n        >\n          <Form.Item\n            name=\"username\"\n            label=\"用户名\"\n            rules={[{ required: true, message: '请输入用户名' }]}\n          >\n            <Input prefix={<UserOutlined />} placeholder=\"用户名\" />\n          </Form.Item>\n          \n          {!editingUser && (\n            <Form.Item\n              name=\"password\"\n              label=\"密码\"\n              rules={[\n                { required: true, message: '请输入密码' },\n                { min: 6, message: '密码长度至少为6个字符' }\n              ]}\n              extra=\"密码长度至少为6个字符\"\n            >\n              <Input.Password prefix={<LockOutlined />} placeholder=\"密码\" />\n            </Form.Item>\n          )}\n          \n          <Form.Item\n            name=\"role\"\n            label=\"角色\"\n            rules={[{ required: true, message: '请选择角色' }]}\n          >\n            <Select placeholder=\"选择角色\">\n              <Option value=\"admin\">管理员</Option>\n              <Option value=\"monitor\">监控人员</Option>\n              <Option value=\"user\">普通用户</Option>\n              <Option value=\"maintenance\">设备维护人员</Option>\n            </Select>\n          </Form.Item>\n          \n          <Form.Item\n            name=\"email\"\n            label=\"邮箱\"\n            rules={[\n              { type: 'email', message: '请输入有效的邮箱地址' }\n            ]}\n          >\n            <Input prefix={<MailOutlined />} placeholder=\"邮箱（选填）\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n      \n      {/* 添加路口管理 Modal */}\n      <Modal\n        title={editingIntersection ? '编辑路口' : '添加路口'}\n        open={intersectionModalVisible}\n        onOk={handleIntersectionModalOk}\n        onCancel={() => setIntersectionModalVisible(false)}\n      >\n        <Form\n          form={intersectionForm}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"路口名称\"\n            // rules={[{ required: true, message: '请输入路口名称' }]}\n          >\n            {/* <Input placeholder=\"请输入路口名称\" /> */}\n            <Input disabled />\n          </Form.Item>\n          <Form.Item\n            name=\"interId\"\n            label=\"路口ID\"\n            rules={[{ required: true, message: '请输入路口ID' }]}\n          >\n            <Input placeholder=\"请输入路口ID（如1）\" />\n          </Form.Item>\n          <Form.Item\n            name=\"latitude\"\n            label=\"纬度\"\n            rules={[{ required: true, message: '请输入纬度' }]}\n          >\n            <Input placeholder=\"请输入纬度\" />\n          </Form.Item>\n          <Form.Item\n            name=\"longitude\"\n            label=\"经度\"\n            rules={[{ required: true, message: '请输入经度' }]}\n          >\n            <Input placeholder=\"请输入经度\" />\n          </Form.Item>\n          <Form.Item\n            name=\"hasTrafficLight\"\n            label=\"是否有红绿灯\"\n            valuePropName=\"checked\"\n          >\n            <Select placeholder=\"请选择是否有红绿灯\">\n              <Option value={true}>有</Option>\n              <Option value={false}>无</Option>\n            </Select>\n          </Form.Item>\n          <Form.List name=\"entrances\">\n            {(fields) => (\n              <div>\n                <div style={{ fontWeight: 'bold', margin: '8px 0 4px 0' }}>进口信息：</div>\n                {fields.map(({ key, name, ...restField }) => (\n                  <div key={key} style={{ display: 'flex', gap: 8, marginBottom: 8 }}>\n                    <Form.Item\n                      {...restField}\n                      name={[name, 'name']}\n                      label=\"进口名称\"\n                      style={{ flex: 1, marginBottom: 0 }}\n                    >\n                      <Input disabled />\n                    </Form.Item>\n                    <Form.Item\n                      {...restField}\n                      name={[name, 'latitude']}\n                      label=\"纬度\"\n                      style={{ flex: 1, marginBottom: 0 }}\n                    >\n                      <Input placeholder=\"请输入纬度\" />\n                    </Form.Item>\n                    <Form.Item\n                      {...restField}\n                      name={[name, 'longitude']}\n                      label=\"经度\"\n                      style={{ flex: 1, marginBottom: 0 }}\n                    >\n                      <Input placeholder=\"请输入经度\" />\n                    </Form.Item>\n                  </div>\n                ))}\n              </div>\n            )}\n          </Form.List>\n        </Form>\n      </Modal>\n    </SystemContainer>\n  );\n};\n\nexport default SystemManagement;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,MAAM,QAAQ,MAAM;AACvF,SAASC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC1F,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC/C,MAAM;EAAEC;AAAO,CAAC,GAAGhB,MAAM;;AAEzB;AACA,MAAMiB,eAAe,GAAGP,MAAM,CAACQ,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,eAAe;AAOrB,MAAMG,aAAa,GAAGV,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GARID,aAAa;AAUnB,MAAME,SAAS,GAAGZ,MAAM,CAACa,MAAM;AAC/B;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,OAAO;AAC3D;AACA,6BAA6BD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,aAAa;AAC9E,WAAWD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,qBAAqB;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnBIJ,SAAS;AAqBf,MAAMK,WAAW,GAAGjB,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GANID,WAAW;AAQjB,MAAME,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6C,IAAI,CAAC,GAAGxC,IAAI,CAACyC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqD,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACuD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACyD,gBAAgB,CAAC,GAAGpD,IAAI,CAACyC,OAAO,CAAC,CAAC;EACzC,MAAM,CAACY,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM2D,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAI;QAAA,IAAAC,cAAA;QACF,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC7C,IAAI,CAACF,QAAQ,EAAE;UACbH,gBAAgB,CAAC,KAAK,CAAC;UACvB;QACF;QAEA,MAAMM,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAAC;QACrC,MAAMM,QAAQ,GAAGH,QAAQ,CAACI,IAAI,MAAAR,cAAA,GAAII,QAAQ,CAACK,IAAI,cAAAT,cAAA,uBAAbA,cAAA,CAAeQ,IAAI,KAAI,MAAM;QAE/D,IAAID,QAAQ,KAAK,OAAO,EAAE;UACxBT,gBAAgB,CAAC,IAAI,CAAC;QACxB,CAAC,MAAM;UACLA,gBAAgB,CAAC,KAAK,CAAC;UACvBnD,OAAO,CAAC+D,KAAK,CAAC,eAAe,CAAC;QAChC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/BZ,gBAAgB,CAAC,KAAK,CAAC;MACzB;IACF,CAAC;IAEDC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMa,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACf,aAAa,EAAE,OAAO,CAAC;;IAE5B,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAChBgC,OAAO,CAACE,GAAG,CAAC,aAAa,CAAC;MAE1B,MAAMC,KAAK,GAAGZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3CQ,OAAO,CAACE,GAAG,CAAC,aAAa,EAAEC,KAAK,CAAC;;MAEjC;MACA,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMjE,KAAK,CAACkE,GAAG,CAAC,GAAGL,MAAM,YAAY,EAAE;QACtDM,OAAO,EAAE;UACP,eAAe,EAAE,UAAUP,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEFH,OAAO,CAACE,GAAG,CAAC,QAAQ,EAAEM,QAAQ,CAACG,IAAI,CAAC;MAEpC,IAAIH,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;QAC1C1C,QAAQ,CAACsC,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACpC,CAAC,MAAM;QAAA,IAAAE,cAAA;QACL7E,OAAO,CAAC8E,OAAO,CAAC,YAAY,IAAI,EAAAD,cAAA,GAAAL,QAAQ,CAACG,IAAI,cAAAE,cAAA,uBAAbA,cAAA,CAAe7E,OAAO,KAAI,MAAM,CAAC,CAAC;MACpE;IACF,CAAC,CAAC,OAAO+D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC/D,OAAO,CAAC+D,KAAK,CAAC,YAAY,IAAIA,KAAK,CAAC/D,OAAO,IAAI,MAAM,CAAC,CAAC;IACzD,CAAC,SAAS;MACRgC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAvC,SAAS,CAAC,MAAM;IACd,IAAIyD,aAAa,EAAE;MACjBe,UAAU,CAAC,CAAC;MACZc,kBAAkB,CAAC,CAAC;;MAEpB;MACA,MAAMC,eAAe,GAAG,CAAC,GAAG,IAAIC,GAAG,CACjCrE,WAAW,CAACsE,OAAO,CAChBC,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,KAAK,CAAC,CAAC;MAAA,CACxCC,GAAG,CAACF,MAAM,IAAIA,MAAM,CAACG,QAAQ,CAClC,CAAC,CAAC;;MAEF;MACA;MACA,MAAMC,qBAAqB,GAAG7C,aAAa,IAAI,EAAE;MACjD;MACA,MAAM8C,mBAAmB,GAAGT,eAAe,CAACM,GAAG,CAACC,QAAQ,IAAI;QAC1D,MAAMG,oBAAoB,GAAG/C,aAAa,CAACgD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKN,QAAQ,CAAC;QACzE,IAAIG,oBAAoB,EAAE;UACxB,OAAOA,oBAAoB;QAC7B;QACA;QACA,MAAMI,eAAe,GAAG;UACtBC,EAAE,EAAE,gBAAgBC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAC3ER,IAAI,EAAEN,QAAQ;UACde,QAAQ,EAAE,EAAE;UAAE;UACdC,SAAS,EAAE,EAAE;UAAE;UACfC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;UACnCC,SAAS,EAAE,IAAIV,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;UACnCE,OAAO,EAAE,EAAE;UACXC,eAAe,EAAE,KAAK;UACtBC,SAAS,EAAE;QACb,CAAC;QACD;QACA,MAAMzC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;QACvEhE,KAAK,CAACuG,IAAI,CAAC,GAAG1C,MAAM,oBAAoB,EAAE0B,eAAe,CAAC,CACvDiB,KAAK,CAAChD,KAAK,IAAI;UACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;QAClC,CAAC,CAAC;QACJ,OAAO+B,eAAe;MACxB,CAAC,CAAC;MAEFlD,gBAAgB,CAAC6C,mBAAmB,CAAC;IACvC;EACF,CAAC,EAAE,CAAChD,SAAS,EAAES,aAAa,CAAC,CAAC;;EAE9B;EACA,IAAI,CAACA,aAAa,EAAE;IAClB,oBACEpC,OAAA,CAACE,eAAe;MAAAgG,QAAA,eACdlG,OAAA,CAACZ,MAAM;QACL+G,MAAM,EAAC,KAAK;QACZC,KAAK,EAAC,KAAK;QACXC,QAAQ,EAAC,gFAAe;QACxBC,IAAI,eAAEtG,OAAA,CAACR,YAAY;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBC,KAAK,eACH3G,OAAA,CAACnB,MAAM;UAAC0F,IAAI,EAAC,SAAS;UAACqC,OAAO,EAAEA,CAAA,KAAM5F,QAAQ,CAAC,GAAG,CAAE;UAAAkF,QAAA,EAAC;QAErD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACa,CAAC;EAEtB;;EAEA;;EAEA;EACA,MAAMG,eAAe,GAAIC,GAAG,IAAK;IAC/B5D,OAAO,CAACE,GAAG,CAAC,QAAQ,EAAE0D,GAAG,CAAC;IAC1BlF,YAAY,CAACkF,GAAG,CAAC;;IAEjB;IACA,IAAIA,GAAG,KAAK,SAAS,EAAE;MACrB,MAAMC,mBAAmB,GAAGC,QAAQ,CAACC,aAAa,CAAC,oBAAoB,CAAC;MACxE,IAAIF,mBAAmB,EAAE;QACvBA,mBAAmB,CAACG,YAAY,CAAC,CAAC;MACpC;IACF;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1BzF,cAAc,CAAC,IAAI,CAAC;IACpBH,IAAI,CAAC6F,WAAW,CAAC,CAAC;IAClB9F,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM+F,cAAc,GAAIrE,IAAI,IAAK;IAC/BtB,cAAc,CAACsB,IAAI,CAAC;IACpBzB,IAAI,CAAC+F,cAAc,CAAC;MAClBC,QAAQ,EAAEvE,IAAI,CAACuE,QAAQ;MACvBxE,IAAI,EAAEC,IAAI,CAACD,IAAI;MACfyE,KAAK,EAAExE,IAAI,CAACwE;IACd,CAAC,CAAC;IACFlG,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMmG,gBAAgB,GAAG,MAAOC,MAAM,IAAK;IACzC,IAAI;MACF,MAAMrE,KAAK,GAAGZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAMY,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;MAEvE;MACA,IAAI;QACF,MAAMhE,KAAK,CAACkI,MAAM,CAAC,GAAGrE,MAAM,cAAcoE,MAAM,EAAE,EAAE;UAClD9D,OAAO,EAAE;YACP,eAAe,EAAE,UAAUP,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QACFnE,OAAO,CAAC4E,OAAO,CAAC,OAAO,CAAC;QACxBX,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,OAAOF,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;;QAElC;QACA,MAAM2E,UAAU,GAAGhF,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;QACzE,MAAMmF,YAAY,GAAGD,UAAU,CAACvD,MAAM,CAACrB,IAAI,IAAIA,IAAI,CAACiC,EAAE,KAAKyC,MAAM,CAAC;QAClEjF,YAAY,CAACqF,OAAO,CAAC,YAAY,EAAElF,IAAI,CAACmF,SAAS,CAACF,YAAY,CAAC,CAAC;QAEhE3I,OAAO,CAAC4E,OAAO,CAAC,aAAa,CAAC;QAC9BX,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/D,OAAO,CAAC+D,KAAK,CAAC,UAAU,IAAIA,KAAK,CAAC/D,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;EAED,MAAM8I,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM1G,IAAI,CAAC2G,cAAc,CAAC,CAAC;MAC1ChF,OAAO,CAACE,GAAG,CAAC,OAAO,EAAE6E,MAAM,CAAC;MAE5B,MAAM5E,KAAK,GAAGZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAMY,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;MAEvE;MACA,IAAI;QACF,IAAIC,QAAQ;QAEZ,IAAIjC,WAAW,EAAE;UACf;UACAiC,QAAQ,GAAG,MAAMjE,KAAK,CAAC0I,GAAG,CAAC,GAAG7E,MAAM,cAAc7B,WAAW,CAACwD,EAAE,EAAE,EAAEgD,MAAM,EAAE;YAC1ErE,OAAO,EAAE;cACP,eAAe,EAAE,UAAUP,KAAK,EAAE;cAClC,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAK,QAAQ,GAAG,MAAMjE,KAAK,CAACuG,IAAI,CAAC,GAAG1C,MAAM,YAAY,EAAE2E,MAAM,EAAE;YACzDrE,OAAO,EAAE;cACP,eAAe,EAAE,UAAUP,KAAK,EAAE;cAClC,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;QACJ;QAEAH,OAAO,CAACE,GAAG,CAAC,QAAQ,EAAEM,QAAQ,CAACG,IAAI,CAAC;QAEpC,IAAIH,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;UAC1C5E,OAAO,CAAC4E,OAAO,CAACrC,WAAW,GAAG,OAAO,GAAG,OAAO,CAAC;UAChDH,eAAe,CAAC,KAAK,CAAC;UACtBC,IAAI,CAAC6F,WAAW,CAAC,CAAC;UAClBjE,UAAU,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,MAAM;UAAA,IAAAiF,eAAA;UACLlJ,OAAO,CAAC+D,KAAK,CAAC,EAAAmF,eAAA,GAAA1E,QAAQ,CAACG,IAAI,cAAAuE,eAAA,uBAAbA,eAAA,CAAelJ,OAAO,KAAI,MAAM,CAAC;QACjD;MACF,CAAC,CAAC,OAAO+D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;;QAElC;QACA,MAAM2E,UAAU,GAAGhF,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;QAEzE,IAAIjB,WAAW,EAAE;UACf;UACA,MAAMoG,YAAY,GAAGD,UAAU,CAACpD,GAAG,CAACxB,IAAI,IACtCA,IAAI,CAACiC,EAAE,KAAKxD,WAAW,CAACwD,EAAE,GAAG;YAAE,GAAGjC,IAAI;YAAE,GAAGiF;UAAO,CAAC,GAAGjF,IACxD,CAAC;UACDP,YAAY,CAACqF,OAAO,CAAC,YAAY,EAAElF,IAAI,CAACmF,SAAS,CAACF,YAAY,CAAC,CAAC;QAClE,CAAC,MAAM;UACL;UACA,MAAMQ,OAAO,GAAG;YACd,GAAGJ,MAAM;YACThD,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACG,QAAQ,CAAC,CAAC;YACzBI,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;UACpC,CAAC;UACDiC,UAAU,CAACU,IAAI,CAACD,OAAO,CAAC;UACxB5F,YAAY,CAACqF,OAAO,CAAC,YAAY,EAAElF,IAAI,CAACmF,SAAS,CAACH,UAAU,CAAC,CAAC;QAChE;QAEA1I,OAAO,CAAC4E,OAAO,CAACrC,WAAW,GAAG,aAAa,GAAG,aAAa,CAAC;QAC5DH,eAAe,CAAC,KAAK,CAAC;QACtBC,IAAI,CAAC6F,WAAW,CAAC,CAAC;QAClBjE,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/D,OAAO,CAAC+D,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,MAAMsF,iBAAiB,GAAGA,CAAA,KAAM;IAC9BjH,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMkH,OAAO,GAAG,CACd;IACEpC,KAAK,EAAE,KAAK;IACZqC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEtC,KAAK,EAAE,IAAI;IACXqC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAG5F,IAAI,IAAK;MAChB,MAAM6F,OAAO,GAAG;QACd,OAAO,EAAE,KAAK;QACd,SAAS,EAAE,MAAM;QACjB,MAAM,EAAE,MAAM;QACd,aAAa,EAAE;MACjB,CAAC;MACD,OAAOA,OAAO,CAAC7F,IAAI,CAAC,IAAIA,IAAI;IAC9B;EACF,CAAC,EACD;IACEqD,KAAK,EAAE,IAAI;IACXqC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE;EACP,CAAC,EACD;IACEtC,KAAK,EAAE,MAAM;IACbqC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAEE,IAAI,IAAIA,IAAI,GAAG,IAAI3D,IAAI,CAAC2D,IAAI,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG;EAC3D,CAAC,EACD;IACE1C,KAAK,EAAE,IAAI;IACXsC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACI,CAAC,EAAEC,MAAM,kBAChBhJ,OAAA;MAAAkG,QAAA,gBACElG,OAAA,CAACnB,MAAM;QAAC0F,IAAI,EAAC,MAAM;QAACqC,OAAO,EAAEA,CAAA,KAAMS,cAAc,CAAC2B,MAAM,CAAE;QAAA9C,QAAA,EAAC;MAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACtE1G,OAAA,CAACnB,MAAM;QAAC0F,IAAI,EAAC,MAAM;QAAC0E,MAAM;QAACrC,OAAO,EAAEA,CAAA,KAAMa,gBAAgB,CAACuB,MAAM,CAAC/D,EAAE,CAAE;QAAAiB,QAAA,EAAC;MAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E;EAET,CAAC,CACF;;EAED;EACA,MAAMwC,qBAAqB,GAAGA,CAAA,KAAM;IAClChH,sBAAsB,CAAC,IAAI,CAAC;IAC5BC,gBAAgB,CAACiF,WAAW,CAAC,CAAC;IAC9BpF,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAMmH,sBAAsB,GAAIC,YAAY,IAAK;IAC/ClH,sBAAsB,CAACkH,YAAY,CAAC;IACpCjH,gBAAgB,CAACmF,cAAc,CAAC;MAC9B,GAAG8B,YAAY;MACfrD,SAAS,EAAEqD,YAAY,CAACrD,SAAS,IAAI;IACvC,CAAC,CAAC;IACF/D,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAMqH,wBAAwB,GAAG,MAAOpE,EAAE,IAAK;IAC7C,IAAI;MACF,MAAM3B,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMhE,KAAK,CAACkI,MAAM,CAAC,GAAGrE,MAAM,sBAAsB2B,EAAE,EAAE,CAAC;MACvD/F,OAAO,CAAC4E,OAAO,CAAC,QAAQ,CAAC;MACzB;MACA,MAAMwF,oBAAoB,GAAGzH,aAAa,CAACwC,MAAM,CAACkF,IAAI,IAAIA,IAAI,CAACtE,EAAE,KAAKA,EAAE,CAAC;MACzEnD,gBAAgB,CAACwH,oBAAoB,CAAC;IACxC,CAAC,CAAC,OAAOrG,KAAK,EAAE;MAAA,IAAAuG,eAAA,EAAAC,oBAAA;MACdvG,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/D,OAAO,CAAC+D,KAAK,CAAC,UAAU,IAAI,EAAAuG,eAAA,GAAAvG,KAAK,CAACS,QAAQ,cAAA8F,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB3F,IAAI,cAAA4F,oBAAA,uBAApBA,oBAAA,CAAsBvK,OAAO,KAAI,MAAM,CAAC,CAAC;IACvE;EACF,CAAC;;EAED;EACA,MAAM+E,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF/C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMoC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMjE,KAAK,CAACkE,GAAG,CAAC,GAAGL,MAAM,oBAAoB,CAAC;MAC/D,IAAII,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACA,IAAI,EAAE;QACvC/B,gBAAgB,CAAC4B,QAAQ,CAACG,IAAI,CAACA,IAAI,CAAC;MACtC;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC/D,OAAO,CAAC+D,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwI,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI;MACF,MAAMzB,MAAM,GAAG,MAAM9F,gBAAgB,CAAC+F,cAAc,CAAC,CAAC;MACtD,MAAM5E,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;MAEvE;MACA,IAAIwE,MAAM,CAACnC,eAAe,KAAK6D,SAAS,EAAE;QACxC1B,MAAM,CAACnC,eAAe,GAAG8D,OAAO,CAAC3B,MAAM,CAACnC,eAAe,CAAC;MAC1D;MAEA,IAAI7D,mBAAmB,EAAE;QACvB;QACA,MAAMyB,QAAQ,GAAG,MAAMjE,KAAK,CAAC0I,GAAG,CAC9B,GAAG7E,MAAM,sBAAsBrB,mBAAmB,CAACgD,EAAE,EAAE,EACvDgD,MACF,CAAC;QACD,IAAIvE,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;UAC1C5E,OAAO,CAAC4E,OAAO,CAAC,QAAQ,CAAC;UACzB;UACA,MAAMwF,oBAAoB,GAAGzH,aAAa,CAAC2C,GAAG,CAAC+E,IAAI,IACjDA,IAAI,CAACtE,EAAE,KAAKhD,mBAAmB,CAACgD,EAAE,GAAG;YAAE,GAAGsE,IAAI;YAAE,GAAGtB;UAAO,CAAC,GAAGsB,IAChE,CAAC;UACDzH,gBAAgB,CAACwH,oBAAoB,CAAC;;UAEtC;UACA,IAAI;YACF;YACA,MAAM5E,qBAAqB,GAAG7C,aAAa,IAAI,EAAE;YACjD;YACA,MAAMgI,wBAAwB,GAAGnF,qBAAqB,CAACF,GAAG,CAAC+E,IAAI,IAC7DA,IAAI,CAACtE,EAAE,KAAKhD,mBAAmB,CAACgD,EAAE,GAAG;cAAE,GAAGsE,IAAI;cAAE,GAAGtB;YAAO,CAAC,GAAGsB,IAChE,CAAC;;YAED;YACA,MAAM9J,KAAK,CAACuG,IAAI,CAAC,GAAG1C,MAAM,oBAAoB,EAAE;cAC9CwG,IAAI,EAAE,oBAAoB;cAC1BjG,IAAI,EAAE;gBAAEhC,aAAa,EAAEgI;cAAyB;YAClD,CAAC,CAAC;YAEF3G,OAAO,CAACE,GAAG,CAAC,gBAAgB,CAAC;UAC/B,CAAC,CAAC,OAAO2G,SAAS,EAAE;YAClB7G,OAAO,CAACD,KAAK,CAAC,aAAa,EAAE8G,SAAS,CAAC;YACvC7K,OAAO,CAAC8E,OAAO,CAAC,oBAAoB,CAAC;UACvC;QACF;MACF,CAAC,MAAM;QACL;QACA,MAAMgB,eAAe,GAAG;UACtB,GAAGiD,MAAM;UACThD,EAAE,EAAE,gBAAgBC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAC3EG,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;UACnCC,SAAS,EAAE,IAAIV,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;UACnCG,eAAe,EAAEmC,MAAM,CAACnC,eAAe,KAAK6D,SAAS,GAAG,KAAK,GAAGC,OAAO,CAAC3B,MAAM,CAACnC,eAAe;QAChG,CAAC;;QAED;QACA,IAAI,CAACd,eAAe,CAACa,OAAO,EAAE;UAC5Bb,eAAe,CAACa,OAAO,GAAG,IAAIb,eAAe,CAACC,EAAE,CAACM,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/D;QAEA,MAAM7B,QAAQ,GAAG,MAAMjE,KAAK,CAACuG,IAAI,CAAC,GAAG1C,MAAM,oBAAoB,EAAE0B,eAAe,CAAC;QACjF,IAAItB,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;UAC1C5E,OAAO,CAAC4E,OAAO,CAAC,QAAQ,CAAC;UACzBhC,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAEmD,eAAe,CAAC,CAAC;;UAErD;UACA,IAAI;YACF;YACA,MAAMN,qBAAqB,GAAG7C,aAAa,IAAI,EAAE;YACjD;YACA,MAAMgI,wBAAwB,GAAG,CAAC,GAAGnF,qBAAqB,EAAEM,eAAe,CAAC;;YAE5E;YACA,MAAMvF,KAAK,CAACuG,IAAI,CAAC,GAAG1C,MAAM,oBAAoB,EAAE;cAC9CwG,IAAI,EAAE,oBAAoB;cAC1BjG,IAAI,EAAE;gBAAEhC,aAAa,EAAEgI;cAAyB;YAClD,CAAC,CAAC;YAEF3G,OAAO,CAACE,GAAG,CAAC,iBAAiB,CAAC;UAChC,CAAC,CAAC,OAAO2G,SAAS,EAAE;YAClB7G,OAAO,CAACD,KAAK,CAAC,aAAa,EAAE8G,SAAS,CAAC;YACvC7K,OAAO,CAAC8E,OAAO,CAAC,kBAAkB,CAAC;UACrC;QACF;MACF;MACAhC,2BAA2B,CAAC,KAAK,CAAC;IACpC,CAAC,CAAC,OAAOiB,KAAK,EAAE;MAAA,IAAA+G,gBAAA,EAAAC,qBAAA;MACd/G,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/D,OAAO,CAAC+D,KAAK,CAAC,UAAU,IAAI,EAAA+G,gBAAA,GAAA/G,KAAK,CAACS,QAAQ,cAAAsG,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnG,IAAI,cAAAoG,qBAAA,uBAApBA,qBAAA,CAAsB/K,OAAO,KAAI,MAAM,CAAC,CAAC;IACvE;EACF,CAAC;;EAED;EACA,MAAMgL,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQvI,SAAS;MACf,KAAK,OAAO;QACV,oBACE3B,OAAA,CAACY,WAAW;UAAAsF,QAAA,gBACVlG,OAAA;YAAKmK,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,UAAU;cAAEC,YAAY,EAAE;YAAG,CAAE;YAAApE,QAAA,eAC5ElG,OAAA,CAACnB,MAAM;cAAC0F,IAAI,EAAC,SAAS;cAACqC,OAAO,EAAEO,aAAc;cAAAjB,QAAA,EAAC;YAE/C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN1G,OAAA,CAACpB,KAAK;YACJqC,OAAO,EAAEA,OAAQ;YACjBsJ,UAAU,EAAEpJ,KAAM;YAClBqH,OAAO,EAAEA,OAAQ;YACjBgC,MAAM,EAAC;UAAI;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAElB,KAAK,SAAS;QACZ,oBAAO1G,OAAA,CAACN,gBAAgB;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7B,KAAK,eAAe;QAClB,oBACE1G,OAAA,CAACY,WAAW;UAAAsF,QAAA,gBACXlG,OAAA;YAAKmK,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,UAAU;cAAEC,YAAY,EAAE;YAAG;UAAE;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKtE,CAAC,eACR1G,OAAA,CAACpB,KAAK;YACJqC,OAAO,EAAEA,OAAQ;YACjBsJ,UAAU,EAAE1I,aAAc;YAC1B2G,OAAO,EAAE,CACP;cACEpC,KAAK,EAAE,MAAM;cACbqC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE;YACP,CAAC,EACD;cACEtC,KAAK,EAAE,MAAM;cACbqC,SAAS,EAAE,SAAS;cACpBC,GAAG,EAAE;YACP,CAAC,EACD;cACEtC,KAAK,EAAE,IAAI;cACXqC,SAAS,EAAE,UAAU;cACrBC,GAAG,EAAE;YACP,CAAC,EACD;cACEtC,KAAK,EAAE,IAAI;cACXqC,SAAS,EAAE,WAAW;cACtBC,GAAG,EAAE;YACP,CAAC,EACD;cACEtC,KAAK,EAAE,QAAQ;cACfqC,SAAS,EAAE,iBAAiB;cAC5BC,GAAG,EAAE,iBAAiB;cACtBC,MAAM,EAAG8B,QAAQ,IAAKA,QAAQ,GAAG,GAAG,GAAG;YACzC,CAAC,EACD;cACErE,KAAK,EAAE,IAAI;cACXsC,GAAG,EAAE,QAAQ;cACbC,MAAM,EAAEA,CAACI,CAAC,EAAEC,MAAM,kBAChBhJ,OAAA;gBAAAkG,QAAA,eACElG,OAAA,CAACnB,MAAM;kBAAC0F,IAAI,EAAC,MAAM;kBAACqC,OAAO,EAAEA,CAAA,KAAMuC,sBAAsB,CAACH,MAAM,CAAE;kBAAA9C,QAAA,EAAC;gBAEnE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAEV,CAAC;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAElB,KAAK,UAAU;QACb,oBAAO1G,OAAA,CAACJ,iBAAiB;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9B;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACE1G,OAAA,CAACE,eAAe;IAAAgG,QAAA,gBACdlG,OAAA,CAACK,aAAa;MAAA6F,QAAA,gBACZlG,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEiB,SAAS,KAAK,OAAQ;QAC9BiF,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAAC,OAAO,CAAE;QAAAX,QAAA,EACzC;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZ1G,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEiB,SAAS,KAAK,SAAU;QAChCiF,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAAC,SAAS,CAAE;QAAAX,QAAA,EAC3C;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZ1G,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEiB,SAAS,KAAK,eAAgB;QACtCiF,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAAC,eAAe,CAAE;QAAAX,QAAA,EACjD;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZ1G,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEiB,SAAS,KAAK,UAAW;QACjCiF,OAAO,EAAEA,CAAA,KAAMC,eAAe,CAAC,UAAU,CAAE;QAAAX,QAAA,EAC5C;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEhB1G,OAAA,CAACY,WAAW;MAAAsF,QAAA,EACTgE,aAAa,CAAC;IAAC;MAAA3D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEd1G,OAAA,CAAClB,KAAK;MACJsH,KAAK,EAAE3E,WAAW,GAAG,MAAM,GAAG,MAAO;MACrCiJ,IAAI,EAAErJ,YAAa;MACnBsJ,IAAI,EAAE3C,aAAc;MACpB4C,QAAQ,EAAErC,iBAAkB;MAC5BsC,cAAc;MAAA3E,QAAA,eAEdlG,OAAA,CAACjB,IAAI;QACHwC,IAAI,EAAEA,IAAK;QACXuJ,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACbhI,IAAI,EAAE;QACR,CAAE;QAAAmD,QAAA,gBAEFlG,OAAA,CAACjB,IAAI,CAACiM,IAAI;UACRjG,IAAI,EAAC,UAAU;UACfkG,KAAK,EAAC,oBAAK;UACXC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEjM,OAAO,EAAE;UAAS,CAAC,CAAE;UAAAgH,QAAA,eAE/ClG,OAAA,CAAChB,KAAK;YAACoM,MAAM,eAAEpL,OAAA,CAACX,YAAY;cAAAkH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAC2E,WAAW,EAAC;UAAK;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,EAEX,CAACjF,WAAW,iBACXzB,OAAA,CAACjB,IAAI,CAACiM,IAAI;UACRjG,IAAI,EAAC,UAAU;UACfkG,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAEjM,OAAO,EAAE;UAAQ,CAAC,EACpC;YAAEoM,GAAG,EAAE,CAAC;YAAEpM,OAAO,EAAE;UAAc,CAAC,CAClC;UACFyH,KAAK,EAAC,+DAAa;UAAAT,QAAA,eAEnBlG,OAAA,CAAChB,KAAK,CAACuM,QAAQ;YAACH,MAAM,eAAEpL,OAAA,CAACV,YAAY;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAC2E,WAAW,EAAC;UAAI;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CACZ,eAED1G,OAAA,CAACjB,IAAI,CAACiM,IAAI;UACRjG,IAAI,EAAC,MAAM;UACXkG,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEjM,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAgH,QAAA,eAE9ClG,OAAA,CAACf,MAAM;YAACoM,WAAW,EAAC,0BAAM;YAAAnF,QAAA,gBACxBlG,OAAA,CAACC,MAAM;cAACuL,KAAK,EAAC,OAAO;cAAAtF,QAAA,EAAC;YAAG;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC1G,OAAA,CAACC,MAAM;cAACuL,KAAK,EAAC,SAAS;cAAAtF,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrC1G,OAAA,CAACC,MAAM;cAACuL,KAAK,EAAC,MAAM;cAAAtF,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC1G,OAAA,CAACC,MAAM;cAACuL,KAAK,EAAC,aAAa;cAAAtF,QAAA,EAAC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ1G,OAAA,CAACjB,IAAI,CAACiM,IAAI;UACRjG,IAAI,EAAC,OAAO;UACZkG,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CACL;YAAE3G,IAAI,EAAE,OAAO;YAAErF,OAAO,EAAE;UAAa,CAAC,CACxC;UAAAgH,QAAA,eAEFlG,OAAA,CAAChB,KAAK;YAACoM,MAAM,eAAEpL,OAAA,CAACT,YAAY;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAC2E,WAAW,EAAC;UAAQ;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR1G,OAAA,CAAClB,KAAK;MACJsH,KAAK,EAAEnE,mBAAmB,GAAG,MAAM,GAAG,MAAO;MAC7CyI,IAAI,EAAE3I,wBAAyB;MAC/B4I,IAAI,EAAEjB,yBAA0B;MAChCkB,QAAQ,EAAEA,CAAA,KAAM5I,2BAA2B,CAAC,KAAK,CAAE;MAAAkE,QAAA,eAEnDlG,OAAA,CAACjB,IAAI;QACHwC,IAAI,EAAEY,gBAAiB;QACvB2I,MAAM,EAAC,UAAU;QAAA5E,QAAA,gBAEjBlG,OAAA,CAACjB,IAAI,CAACiM,IAAI;UACRjG,IAAI,EAAC,MAAM;UACXkG,KAAK,EAAC;UACN;UAAA;UAAA/E,QAAA,eAGAlG,OAAA,CAAChB,KAAK;YAACyM,QAAQ;UAAA;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACZ1G,OAAA,CAACjB,IAAI,CAACiM,IAAI;UACRjG,IAAI,EAAC,SAAS;UACdkG,KAAK,EAAC,gBAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEjM,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAgH,QAAA,eAEhDlG,OAAA,CAAChB,KAAK;YAACqM,WAAW,EAAC;UAAa;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACZ1G,OAAA,CAACjB,IAAI,CAACiM,IAAI;UACRjG,IAAI,EAAC,UAAU;UACfkG,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEjM,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAgH,QAAA,eAE9ClG,OAAA,CAAChB,KAAK;YAACqM,WAAW,EAAC;UAAO;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACZ1G,OAAA,CAACjB,IAAI,CAACiM,IAAI;UACRjG,IAAI,EAAC,WAAW;UAChBkG,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEjM,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAgH,QAAA,eAE9ClG,OAAA,CAAChB,KAAK;YAACqM,WAAW,EAAC;UAAO;YAAA9E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACZ1G,OAAA,CAACjB,IAAI,CAACiM,IAAI;UACRjG,IAAI,EAAC,iBAAiB;UACtBkG,KAAK,EAAC,sCAAQ;UACdS,aAAa,EAAC,SAAS;UAAAxF,QAAA,eAEvBlG,OAAA,CAACf,MAAM;YAACoM,WAAW,EAAC,wDAAW;YAAAnF,QAAA,gBAC7BlG,OAAA,CAACC,MAAM;cAACuL,KAAK,EAAE,IAAK;cAAAtF,QAAA,EAAC;YAAC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/B1G,OAAA,CAACC,MAAM;cAACuL,KAAK,EAAE,KAAM;cAAAtF,QAAA,EAAC;YAAC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACZ1G,OAAA,CAACjB,IAAI,CAAC4M,IAAI;UAAC5G,IAAI,EAAC,WAAW;UAAAmB,QAAA,EACvB0F,MAAM,iBACN5L,OAAA;YAAAkG,QAAA,gBACElG,OAAA;cAAKmK,KAAK,EAAE;gBAAE0B,UAAU,EAAE,MAAM;gBAAEC,MAAM,EAAE;cAAc,CAAE;cAAA5F,QAAA,EAAC;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACrEkF,MAAM,CAACpH,GAAG,CAAC,CAAC;cAAEkE,GAAG;cAAE3D,IAAI;cAAE,GAAGgH;YAAU,CAAC,kBACtC/L,OAAA;cAAemK,KAAK,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAE4B,GAAG,EAAE,CAAC;gBAAE1B,YAAY,EAAE;cAAE,CAAE;cAAApE,QAAA,gBACjElG,OAAA,CAACjB,IAAI,CAACiM,IAAI;gBAAA,GACJe,SAAS;gBACbhH,IAAI,EAAE,CAACA,IAAI,EAAE,MAAM,CAAE;gBACrBkG,KAAK,EAAC,0BAAM;gBACZd,KAAK,EAAE;kBAAE8B,IAAI,EAAE,CAAC;kBAAE3B,YAAY,EAAE;gBAAE,CAAE;gBAAApE,QAAA,eAEpClG,OAAA,CAAChB,KAAK;kBAACyM,QAAQ;gBAAA;kBAAAlF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACZ1G,OAAA,CAACjB,IAAI,CAACiM,IAAI;gBAAA,GACJe,SAAS;gBACbhH,IAAI,EAAE,CAACA,IAAI,EAAE,UAAU,CAAE;gBACzBkG,KAAK,EAAC,cAAI;gBACVd,KAAK,EAAE;kBAAE8B,IAAI,EAAE,CAAC;kBAAE3B,YAAY,EAAE;gBAAE,CAAE;gBAAApE,QAAA,eAEpClG,OAAA,CAAChB,KAAK;kBAACqM,WAAW,EAAC;gBAAO;kBAAA9E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACZ1G,OAAA,CAACjB,IAAI,CAACiM,IAAI;gBAAA,GACJe,SAAS;gBACbhH,IAAI,EAAE,CAACA,IAAI,EAAE,WAAW,CAAE;gBAC1BkG,KAAK,EAAC,cAAI;gBACVd,KAAK,EAAE;kBAAE8B,IAAI,EAAE,CAAC;kBAAE3B,YAAY,EAAE;gBAAE,CAAE;gBAAApE,QAAA,eAEpClG,OAAA,CAAChB,KAAK;kBAACqM,WAAW,EAAC;gBAAO;kBAAA9E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA,GAxBJgC,GAAG;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBR,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEtB,CAAC;AAAC3F,EAAA,CAjuBID,gBAAgB;EAAA,QACHjB,WAAW,EAIbd,IAAI,CAACyC,OAAO,EAMAzC,IAAI,CAACyC,OAAO;AAAA;AAAA0K,GAAA,GAXnCpL,gBAAgB;AAmuBtB,eAAeA,gBAAgB;AAAC,IAAAV,EAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAqL,GAAA;AAAAC,YAAA,CAAA/L,EAAA;AAAA+L,YAAA,CAAA7L,GAAA;AAAA6L,YAAA,CAAAxL,GAAA;AAAAwL,YAAA,CAAAtL,GAAA;AAAAsL,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}