{"ast": null, "code": "\"use client\";\n\nimport React, { useMemo } from 'react';\nimport { ColorBlock, Color as RcColor } from '@rc-component/color-picker';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Collapse from '../../collapse';\nimport { useLocale } from '../../locale';\nimport { useToken } from '../../theme/internal';\nimport { generateColor } from '../util';\nconst genPresetColor = list => list.map(value => {\n  value.colors = value.colors.map(generateColor);\n  return value;\n});\nexport const isBright = (value, bgColorToken) => {\n  const {\n    r,\n    g,\n    b,\n    a\n  } = value.toRgb();\n  const hsv = new RcColor(value.toRgbString()).onBackground(bgColorToken).toHsv();\n  if (a <= 0.5) {\n    // Adapted to dark mode\n    return hsv.v > 0.5;\n  }\n  return r * 0.299 + g * 0.587 + b * 0.114 > 192;\n};\nconst genCollapsePanelKey = (preset, index) => {\n  var _a;\n  const mergedKey = (_a = preset.key) !== null && _a !== void 0 ? _a : index;\n  return `panel-${mergedKey}`;\n};\nconst ColorPresets = _ref => {\n  let {\n    prefixCls,\n    presets,\n    value: color,\n    onChange\n  } = _ref;\n  const [locale] = useLocale('ColorPicker');\n  const [, token] = useToken();\n  const [presetsValue] = useMergedState(genPresetColor(presets), {\n    value: genPresetColor(presets),\n    postState: genPresetColor\n  });\n  const colorPresetsPrefixCls = `${prefixCls}-presets`;\n  const activeKeys = useMemo(() => presetsValue.reduce((acc, preset, index) => {\n    const {\n      defaultOpen = true\n    } = preset;\n    if (defaultOpen) {\n      acc.push(genCollapsePanelKey(preset, index));\n    }\n    return acc;\n  }, []), [presetsValue]);\n  const handleClick = colorValue => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(colorValue);\n  };\n  const items = presetsValue.map((preset, index) => {\n    var _a;\n    return {\n      key: genCollapsePanelKey(preset, index),\n      label: /*#__PURE__*/React.createElement(\"div\", {\n        className: `${colorPresetsPrefixCls}-label`\n      }, preset === null || preset === void 0 ? void 0 : preset.label),\n      children: (/*#__PURE__*/React.createElement(\"div\", {\n        className: `${colorPresetsPrefixCls}-items`\n      }, Array.isArray(preset === null || preset === void 0 ? void 0 : preset.colors) && ((_a = preset.colors) === null || _a === void 0 ? void 0 : _a.length) > 0 ? preset.colors.map((presetColor, index) => (/*#__PURE__*/React.createElement(ColorBlock\n      // eslint-disable-next-line react/no-array-index-key\n      , {\n        // eslint-disable-next-line react/no-array-index-key\n        key: `preset-${index}-${presetColor.toHexString()}`,\n        color: generateColor(presetColor).toRgbString(),\n        prefixCls: prefixCls,\n        className: classNames(`${colorPresetsPrefixCls}-color`, {\n          [`${colorPresetsPrefixCls}-color-checked`]: presetColor.toHexString() === (color === null || color === void 0 ? void 0 : color.toHexString()),\n          [`${colorPresetsPrefixCls}-color-bright`]: isBright(presetColor, token.colorBgElevated)\n        }),\n        onClick: () => handleClick(presetColor)\n      }))) : (/*#__PURE__*/React.createElement(\"span\", {\n        className: `${colorPresetsPrefixCls}-empty`\n      }, locale.presetEmpty))))\n    };\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorPresetsPrefixCls\n  }, /*#__PURE__*/React.createElement(Collapse, {\n    defaultActiveKey: activeKeys,\n    ghost: true,\n    items: items\n  }));\n};\nexport default ColorPresets;", "map": {"version": 3, "names": ["React", "useMemo", "ColorBlock", "Color", "RcColor", "classNames", "useMergedState", "Collapse", "useLocale", "useToken", "generateColor", "genPresetColor", "list", "map", "value", "colors", "isBright", "bgColorToken", "r", "g", "b", "a", "toRgb", "hsv", "toRgbString", "onBackground", "toHsv", "v", "genCollapsePanelKey", "preset", "index", "_a", "mergedKey", "key", "ColorPresets", "_ref", "prefixCls", "presets", "color", "onChange", "locale", "token", "presetsValue", "postState", "colorPresetsPrefixCls", "activeKeys", "reduce", "acc", "defaultOpen", "push", "handleClick", "colorValue", "items", "label", "createElement", "className", "children", "Array", "isArray", "length", "presetColor", "toHexString", "colorBgElevated", "onClick", "presetEmpty", "defaultActiveKey", "ghost"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/color-picker/components/ColorPresets.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useMemo } from 'react';\nimport { ColorBlock, Color as RcColor } from '@rc-component/color-picker';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Collapse from '../../collapse';\nimport { useLocale } from '../../locale';\nimport { useToken } from '../../theme/internal';\nimport { generateColor } from '../util';\nconst genPresetColor = list => list.map(value => {\n  value.colors = value.colors.map(generateColor);\n  return value;\n});\nexport const isBright = (value, bgColorToken) => {\n  const {\n    r,\n    g,\n    b,\n    a\n  } = value.toRgb();\n  const hsv = new RcColor(value.toRgbString()).onBackground(bgColorToken).toHsv();\n  if (a <= 0.5) {\n    // Adapted to dark mode\n    return hsv.v > 0.5;\n  }\n  return r * 0.299 + g * 0.587 + b * 0.114 > 192;\n};\nconst genCollapsePanelKey = (preset, index) => {\n  var _a;\n  const mergedKey = (_a = preset.key) !== null && _a !== void 0 ? _a : index;\n  return `panel-${mergedKey}`;\n};\nconst ColorPresets = _ref => {\n  let {\n    prefixCls,\n    presets,\n    value: color,\n    onChange\n  } = _ref;\n  const [locale] = useLocale('ColorPicker');\n  const [, token] = useToken();\n  const [presetsValue] = useMergedState(genPresetColor(presets), {\n    value: genPresetColor(presets),\n    postState: genPresetColor\n  });\n  const colorPresetsPrefixCls = `${prefixCls}-presets`;\n  const activeKeys = useMemo(() => presetsValue.reduce((acc, preset, index) => {\n    const {\n      defaultOpen = true\n    } = preset;\n    if (defaultOpen) {\n      acc.push(genCollapsePanelKey(preset, index));\n    }\n    return acc;\n  }, []), [presetsValue]);\n  const handleClick = colorValue => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(colorValue);\n  };\n  const items = presetsValue.map((preset, index) => {\n    var _a;\n    return {\n      key: genCollapsePanelKey(preset, index),\n      label: /*#__PURE__*/React.createElement(\"div\", {\n        className: `${colorPresetsPrefixCls}-label`\n      }, preset === null || preset === void 0 ? void 0 : preset.label),\n      children: (/*#__PURE__*/React.createElement(\"div\", {\n        className: `${colorPresetsPrefixCls}-items`\n      }, Array.isArray(preset === null || preset === void 0 ? void 0 : preset.colors) && ((_a = preset.colors) === null || _a === void 0 ? void 0 : _a.length) > 0 ? preset.colors.map((presetColor, index) => (/*#__PURE__*/React.createElement(ColorBlock\n      // eslint-disable-next-line react/no-array-index-key\n      , {\n        // eslint-disable-next-line react/no-array-index-key\n        key: `preset-${index}-${presetColor.toHexString()}`,\n        color: generateColor(presetColor).toRgbString(),\n        prefixCls: prefixCls,\n        className: classNames(`${colorPresetsPrefixCls}-color`, {\n          [`${colorPresetsPrefixCls}-color-checked`]: presetColor.toHexString() === (color === null || color === void 0 ? void 0 : color.toHexString()),\n          [`${colorPresetsPrefixCls}-color-bright`]: isBright(presetColor, token.colorBgElevated)\n        }),\n        onClick: () => handleClick(presetColor)\n      }))) : (/*#__PURE__*/React.createElement(\"span\", {\n        className: `${colorPresetsPrefixCls}-empty`\n      }, locale.presetEmpty))))\n    };\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorPresetsPrefixCls\n  }, /*#__PURE__*/React.createElement(Collapse, {\n    defaultActiveKey: activeKeys,\n    ghost: true,\n    items: items\n  }));\n};\nexport default ColorPresets;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,SAASC,UAAU,EAAEC,KAAK,IAAIC,OAAO,QAAQ,4BAA4B;AACzE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,aAAa,QAAQ,SAAS;AACvC,MAAMC,cAAc,GAAGC,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,KAAK,IAAI;EAC/CA,KAAK,CAACC,MAAM,GAAGD,KAAK,CAACC,MAAM,CAACF,GAAG,CAACH,aAAa,CAAC;EAC9C,OAAOI,KAAK;AACd,CAAC,CAAC;AACF,OAAO,MAAME,QAAQ,GAAGA,CAACF,KAAK,EAAEG,YAAY,KAAK;EAC/C,MAAM;IACJC,CAAC;IACDC,CAAC;IACDC,CAAC;IACDC;EACF,CAAC,GAAGP,KAAK,CAACQ,KAAK,CAAC,CAAC;EACjB,MAAMC,GAAG,GAAG,IAAInB,OAAO,CAACU,KAAK,CAACU,WAAW,CAAC,CAAC,CAAC,CAACC,YAAY,CAACR,YAAY,CAAC,CAACS,KAAK,CAAC,CAAC;EAC/E,IAAIL,CAAC,IAAI,GAAG,EAAE;IACZ;IACA,OAAOE,GAAG,CAACI,CAAC,GAAG,GAAG;EACpB;EACA,OAAOT,CAAC,GAAG,KAAK,GAAGC,CAAC,GAAG,KAAK,GAAGC,CAAC,GAAG,KAAK,GAAG,GAAG;AAChD,CAAC;AACD,MAAMQ,mBAAmB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAC7C,IAAIC,EAAE;EACN,MAAMC,SAAS,GAAG,CAACD,EAAE,GAAGF,MAAM,CAACI,GAAG,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGD,KAAK;EAC1E,OAAO,SAASE,SAAS,EAAE;AAC7B,CAAC;AACD,MAAME,YAAY,GAAGC,IAAI,IAAI;EAC3B,IAAI;IACFC,SAAS;IACTC,OAAO;IACPvB,KAAK,EAAEwB,KAAK;IACZC;EACF,CAAC,GAAGJ,IAAI;EACR,MAAM,CAACK,MAAM,CAAC,GAAGhC,SAAS,CAAC,aAAa,CAAC;EACzC,MAAM,GAAGiC,KAAK,CAAC,GAAGhC,QAAQ,CAAC,CAAC;EAC5B,MAAM,CAACiC,YAAY,CAAC,GAAGpC,cAAc,CAACK,cAAc,CAAC0B,OAAO,CAAC,EAAE;IAC7DvB,KAAK,EAAEH,cAAc,CAAC0B,OAAO,CAAC;IAC9BM,SAAS,EAAEhC;EACb,CAAC,CAAC;EACF,MAAMiC,qBAAqB,GAAG,GAAGR,SAAS,UAAU;EACpD,MAAMS,UAAU,GAAG5C,OAAO,CAAC,MAAMyC,YAAY,CAACI,MAAM,CAAC,CAACC,GAAG,EAAElB,MAAM,EAAEC,KAAK,KAAK;IAC3E,MAAM;MACJkB,WAAW,GAAG;IAChB,CAAC,GAAGnB,MAAM;IACV,IAAImB,WAAW,EAAE;MACfD,GAAG,CAACE,IAAI,CAACrB,mBAAmB,CAACC,MAAM,EAAEC,KAAK,CAAC,CAAC;IAC9C;IACA,OAAOiB,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC,EAAE,CAACL,YAAY,CAAC,CAAC;EACvB,MAAMQ,WAAW,GAAGC,UAAU,IAAI;IAChCZ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACY,UAAU,CAAC;EAC1E,CAAC;EACD,MAAMC,KAAK,GAAGV,YAAY,CAAC7B,GAAG,CAAC,CAACgB,MAAM,EAAEC,KAAK,KAAK;IAChD,IAAIC,EAAE;IACN,OAAO;MACLE,GAAG,EAAEL,mBAAmB,CAACC,MAAM,EAAEC,KAAK,CAAC;MACvCuB,KAAK,EAAE,aAAarD,KAAK,CAACsD,aAAa,CAAC,KAAK,EAAE;QAC7CC,SAAS,EAAE,GAAGX,qBAAqB;MACrC,CAAC,EAAEf,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACwB,KAAK,CAAC;MAChEG,QAAQ,GAAG,aAAaxD,KAAK,CAACsD,aAAa,CAAC,KAAK,EAAE;QACjDC,SAAS,EAAE,GAAGX,qBAAqB;MACrC,CAAC,EAAEa,KAAK,CAACC,OAAO,CAAC7B,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACd,MAAM,CAAC,IAAI,CAAC,CAACgB,EAAE,GAAGF,MAAM,CAACd,MAAM,MAAM,IAAI,IAAIgB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4B,MAAM,IAAI,CAAC,GAAG9B,MAAM,CAACd,MAAM,CAACF,GAAG,CAAC,CAAC+C,WAAW,EAAE9B,KAAK,MAAM,aAAa9B,KAAK,CAACsD,aAAa,CAACpD;MAC3O;MAAA,EACE;QACA;QACA+B,GAAG,EAAE,UAAUH,KAAK,IAAI8B,WAAW,CAACC,WAAW,CAAC,CAAC,EAAE;QACnDvB,KAAK,EAAE5B,aAAa,CAACkD,WAAW,CAAC,CAACpC,WAAW,CAAC,CAAC;QAC/CY,SAAS,EAAEA,SAAS;QACpBmB,SAAS,EAAElD,UAAU,CAAC,GAAGuC,qBAAqB,QAAQ,EAAE;UACtD,CAAC,GAAGA,qBAAqB,gBAAgB,GAAGgB,WAAW,CAACC,WAAW,CAAC,CAAC,MAAMvB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACuB,WAAW,CAAC,CAAC,CAAC;UAC7I,CAAC,GAAGjB,qBAAqB,eAAe,GAAG5B,QAAQ,CAAC4C,WAAW,EAAEnB,KAAK,CAACqB,eAAe;QACxF,CAAC,CAAC;QACFC,OAAO,EAAEA,CAAA,KAAMb,WAAW,CAACU,WAAW;MACxC,CAAC,CAAC,CAAC,CAAC,IAAI,aAAa5D,KAAK,CAACsD,aAAa,CAAC,MAAM,EAAE;QAC/CC,SAAS,EAAE,GAAGX,qBAAqB;MACrC,CAAC,EAAEJ,MAAM,CAACwB,WAAW,CAAC,CAAC,CAAC;IAC1B,CAAC;EACH,CAAC,CAAC;EACF,OAAO,aAAahE,KAAK,CAACsD,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAEX;EACb,CAAC,EAAE,aAAa5C,KAAK,CAACsD,aAAa,CAAC/C,QAAQ,EAAE;IAC5C0D,gBAAgB,EAAEpB,UAAU;IAC5BqB,KAAK,EAAE,IAAI;IACXd,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAelB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}