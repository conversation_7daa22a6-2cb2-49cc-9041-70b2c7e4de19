{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport RightSquareFilledSvg from \"@ant-design/icons-svg/es/asn/RightSquareFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar RightSquareFilled = function RightSquareFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: RightSquareFilledSvg\n  }));\n};\n\n/**![right-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjU4LjcgNTE4LjVsLTI0NiAxNzhjLTUuMyAzLjgtMTIuNyAwLTEyLjctNi41di00Ni45YzAtMTAuMiA0LjktMTkuOSAxMy4yLTI1LjlMNTU4LjYgNTEyIDQxMy4yIDQwNi44Yy04LjMtNi0xMy4yLTE1LjYtMTMuMi0yNS45VjMzNGMwLTYuNSA3LjQtMTAuMyAxMi43LTYuNWwyNDYgMTc4YzQuNCAzLjIgNC40IDkuOCAwIDEzeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(RightSquareFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'RightSquareFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "RightSquareFilledSvg", "AntdIcon", "RightSquareFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@ant-design/icons/es/icons/RightSquareFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport RightSquareFilledSvg from \"@ant-design/icons-svg/es/asn/RightSquareFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar RightSquareFilled = function RightSquareFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: RightSquareFilledSvg\n  }));\n};\n\n/**![right-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjU4LjcgNTE4LjVsLTI0NiAxNzhjLTUuMyAzLjgtMTIuNyAwLTEyLjctNi41di00Ni45YzAtMTAuMiA0LjktMTkuOSAxMy4yLTI1LjlMNTU4LjYgNTEyIDQxMy4yIDQwNi44Yy04LjMtNi0xMy4yLTE1LjYtMTMuMi0yNS45VjMzNGMwLTYuNSA3LjQtMTAuMyAxMi43LTYuNWwyNDYgMTc4YzQuNCAzLjIgNC40IDkuOCAwIDEzeiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(RightSquareFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'RightSquareFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,iBAAiB,CAAC;AAC9D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,mBAAmB;AAC3C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}