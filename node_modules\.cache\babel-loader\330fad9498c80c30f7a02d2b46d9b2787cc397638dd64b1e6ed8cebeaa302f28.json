{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.3; // 滤波系数，值越小滤波效果越强（0-1之间）\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n    rsi: 'changli/cloud/v2x/rsu/rsi' // 添加 RSI 主题\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 添加位置滤波函数\nconst filterPosition = newPos => {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n};\n\n// 添加朝向滤波函数\nconst filterRotation = newRotation => {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n  return filteredRotation;\n};\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null); // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 添加事件统计状态\n  const [eventStats, setEventStats] = useState(() => {\n    const savedStats = localStorage.getItem('campusModelEventStats');\n    return savedStats ? JSON.parse(savedStats) : {\n      '401': 0,\n      // 道路抛洒物\n      '404': 0,\n      // 道路障碍物\n      '405': 0,\n      // 行人通过马路\n      '904': 0,\n      // 逆行车辆\n      '910': 0,\n      // 违停车辆\n      '1002': 0,\n      // 道路施工\n      '901': 0 // 车辆超速\n    };\n  });\n\n  // 使用防抖保存事件统计到 localStorage\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      localStorage.setItem('campusModelEventStats', JSON.stringify(eventStats));\n    }, 1000); // 延迟1秒保存，避免频繁写入\n\n    return () => clearTimeout(timer);\n  }, [eventStats]);\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    try {\n      const messageData = JSON.parse(message.toString());\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.topics.rsi && messageData.type === 'RSI') {\n        console.log('收到RSI消息:', messageData);\n        const rsiData = messageData.data;\n        const events = rsiData.rtes || [];\n\n        // 批量更新事件统计\n        const statsUpdates = {};\n        events.forEach(event => {\n          const eventType = event.eventType;\n          if (eventType && eventStats.hasOwnProperty(eventType)) {\n            statsUpdates[eventType] = (statsUpdates[eventType] || 0) + 1;\n          }\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(parseFloat(rsiData.posLong), parseFloat(rsiData.posLat));\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          switch (eventType) {\n            case '401':\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002':\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = event.description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n        });\n\n        // 一次性更新所有统计数据\n        if (Object.keys(statsUpdates).length > 0) {\n          setEventStats(prev => {\n            const newStats = {\n              ...prev\n            };\n            Object.entries(statsUpdates).forEach(([type, count]) => {\n              newStats[type] = (newStats[type] || 0) + count;\n            });\n            return newStats;\n          });\n        }\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: messageData.data\n        }, '*');\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.topics.scene && messageData.type === 'SCENE') {\n        console.log('收到场景事件消息:', messageData);\n        const sceneData = messageData.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch (sceneType) {\n          case '2':\n            // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':\n            // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':\n            // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':\n            // 限速提醒\n            const speedLimit = sceneData.eventData1; // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':\n            // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':\n            // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':\n            // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':\n            // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':\n            // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':\n            // 信号灯优先\n            const priorityType = sceneData.eventData1; // 优先类型\n            const duration = sceneData.eventData2; // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        return;\n      }\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        var _messageData$data;\n        console.log('收到RSM消息:', messageData);\n        const participants = ((_messageData$data = messageData.data) === null || _messageData$data === void 0 ? void 0 : _messageData$data.participants) || [];\n        const rsuid = messageData.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id = rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = preloadedModel.clone();\n              // 根据类型调整高度\n              const height = type === '3' ? 0.1 : 0.8; // 行人模型贴近地面\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 0.1 : 0.8, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        const bsmData = messageData.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n          const newRotation = Math.PI - newState.heading * Math.PI / 180;\n\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition);\n          const filteredRotation = filterRotation(newRotation);\n\n          // 更新位置和朝向\n          globalVehicleRef.position.copy(filteredPosition);\n          globalVehicleRef.rotation.y = filteredRotation;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n\n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', filteredPosition);\n        }\n        return;\n      }\n\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n        scene.add(model);\n\n        // 在校园模型加载完成后初始化场景\n        await initializeScene();\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 100, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n\n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n\n      // 7. 清理场景\n      if (scene) {\n        while (scene.children.length > 0) {\n          scene.remove(scene.children[0]);\n        }\n      }\n\n      // 8. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      globalVehicleRef = null;\n      console.log('组件清理完成');\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u8DEF\\u53E3\\u9009\\u62E9\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1042,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u8DEF\\u53E3\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      options: intersectionsData.intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1043,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1058,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1060,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1070,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1059,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"6TlTNaxSZG19Z3fgZrNnsMjRqno=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    const [vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`)]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse(child => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n        // // 保留原始贴图\n        // if (child.material.map) {\n        //   newMaterial.map = child.material.map;\n        // }\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse(child => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse(child => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n    console.log('所有模型预加载成功');\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = type => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y); // 设置位置，稍微升高以便可见\n\n  // 为标记添加一个定时器，在5秒后自动移除\n  setTimeout(() => {\n    scene.remove(sprite);\n  }, 500);\n\n  // 将标记添加到场景中\n  scene.add(sprite);\n  console.log('添加场景事件标记:', {\n    位置: position,\n    文本: text,\n    颜色: color\n  });\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "Select", "intersectionsData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "scene", "lastPosition", "lastRotation", "ALPHA", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "topics", "bsm", "rsm", "rsi", "BASE_URL", "vehicleModels", "Map", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "CampusModel", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "eventStats", "setEventStats", "savedStats", "localStorage", "getItem", "JSON", "parse", "timer", "setTimeout", "setItem", "stringify", "clearTimeout", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "find", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "updateMatrix", "updateMatrixWorld", "相机位置", "toArray", "目标点", "handleMqttMessage", "topic", "message", "error", "toString", "messageData", "type", "rsiData", "data", "events", "rtes", "statsUpdates", "for<PERSON>ach", "event", "eventType", "hasOwnProperty", "modelPos", "posLong", "posLat", "warningText", "warningColor", "description", "showWarningMarker", "Object", "keys", "length", "prev", "newStats", "entries", "count", "postMessage", "sceneData", "sceneId", "sceneType", "sceneDesc", "partLat", "partLong", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "_messageData$data", "participants", "rsuid", "now", "Date", "participant", "id", "partPtcId", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "preloadedModel", "model", "get", "newModel", "height", "rotation", "add", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "Set", "map", "p", "modelData", "has", "remove", "delete", "bsmData", "bsmid", "bsmId", "newState", "newPosition", "Vector3", "filteredPosition", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "payload", "onerror", "onclose", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "xhr", "loaded", "total", "toFixed", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "clearInterval", "close", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "clear", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "onChange", "options", "label", "size", "bordered", "dropdownStyle", "maxHeight", "ref", "onClick", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "vehicleGltf", "cyclistGltf", "peopleGltf", "all", "loadAsync", "types", "位置", "文本", "颜色", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.3; // 滤波系数，值越小滤波效果越强（0-1之间）\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n    rsi: 'changli/cloud/v2x/rsu/rsi'  // 添加 RSI 主题\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 添加位置滤波函数\nconst filterPosition = (newPos) => {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n};\n\n// 添加朝向滤波函数\nconst filterRotation = (newRotation) => {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n  return filteredRotation;\n};\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);  // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加事件统计状态\n  const [eventStats, setEventStats] = useState(() => {\n    const savedStats = localStorage.getItem('campusModelEventStats');\n    return savedStats ? JSON.parse(savedStats) : {\n      '401': 0,  // 道路抛洒物\n      '404': 0,  // 道路障碍物\n      '405': 0,  // 行人通过马路\n      '904': 0,  // 逆行车辆\n      '910': 0,  // 违停车辆\n      '1002': 0, // 道路施工\n      '901': 0   // 车辆超速\n    };\n  });\n\n  // 使用防抖保存事件统计到 localStorage\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      localStorage.setItem('campusModelEventStats', JSON.stringify(eventStats));\n    }, 1000); // 延迟1秒保存，避免频繁写入\n\n    return () => clearTimeout(timer);\n  }, [eventStats]);\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    \n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    \n    try {\n      const messageData = JSON.parse(message.toString());\n      \n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.topics.rsi && messageData.type === 'RSI') {\n        console.log('收到RSI消息:', messageData);\n        \n        const rsiData = messageData.data;\n        const events = rsiData.rtes || [];\n        \n        // 批量更新事件统计\n        const statsUpdates = {};\n        events.forEach(event => {\n          const eventType = event.eventType;\n          if (eventType && eventStats.hasOwnProperty(eventType)) {\n            statsUpdates[eventType] = (statsUpdates[eventType] || 0) + 1;\n          }\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401': warningText = '道路抛洒物'; warningColor = '#ff4d4f'; break;\n            case '404': warningText = '道路障碍物'; warningColor = '#faad14'; break;\n            case '405': warningText = '行人通过马路'; warningColor = '#1890ff'; break;\n            case '904': warningText = '逆行车辆'; warningColor = '#f5222d'; break;\n            case '910': warningText = '违停车辆'; warningColor = '#722ed1'; break;\n            case '1002': warningText = '道路施工'; warningColor = '#fa8c16'; break;\n            case '901': warningText = '车辆超速'; warningColor = '#eb2f96'; break;\n            default: warningText = event.description || '未知事件'; warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n        });\n\n        // 一次性更新所有统计数据\n        if (Object.keys(statsUpdates).length > 0) {\n          setEventStats(prev => {\n            const newStats = { ...prev };\n            Object.entries(statsUpdates).forEach(([type, count]) => {\n              newStats[type] = (newStats[type] || 0) + count;\n            });\n            return newStats;\n          });\n        }\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: messageData.data\n        }, '*');\n        \n        return;\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.topics.scene && messageData.type === 'SCENE') {\n        console.log('收到场景事件消息:', messageData);\n        \n        const sceneData = messageData.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      }\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        console.log('收到RSM消息:', messageData);\n        \n        const participants = messageData.data?.participants || [];\n        const rsuid = messageData.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id =  rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n          // 解析位置和状态信息\n          const state = {\n            longitude: parseFloat(participant.partPosLong),\n            latitude: parseFloat(participant.partPosLat),\n            speed: parseFloat(participant.partSpeed),\n            heading: parseFloat(participant.partHeading)\n          };\n          \n          const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n          \n          // 根据类型选择对应的预加载模型\n          let preloadedModel;\n          switch (type) {\n            case '1': // 机动车\n              preloadedModel = preloadedVehicleModel;\n              break;\n            case '2': // 非机动车\n              preloadedModel = preloadedCyclistModel;\n              break;\n            case '3': // 行人\n              preloadedModel = preloadedPeopleModel;\n              break;\n            default:\n              return; // 跳过未知类型\n          }\n          \n          // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n            // 创建新模型实例\n            const newModel = preloadedModel.clone();\n            // 根据类型调整高度\n            const height = type === '3' ? 0.1 : 0.8; // 行人模型贴近地面\n            newModel.position.set(modelPos.x, height, -modelPos.y);\n            newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            scene.add(newModel);\n            \n            vehicleModels.set(id, {\n              model: newModel,\n              lastUpdate: now,\n              type: type\n            });\n          } else if (model) {\n            // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 0.1 : 0.8, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n          }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        \n        const bsmData = messageData.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n          const newRotation = Math.PI - newState.heading * Math.PI / 180;\n          \n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition);\n          const filteredRotation = filterRotation(newRotation);\n          \n          // 更新位置和朝向\n          globalVehicleRef.position.copy(filteredPosition);\n          globalVehicleRef.rotation.y = filteredRotation;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n          \n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', filteredPosition);\n        }\n        return;\n      }\n      \n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          100,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n      \n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n      \n      // 7. 清理场景\n      if (scene) {\n        while(scene.children.length > 0) { \n          scene.remove(scene.children[0]); \n        }\n      }\n      \n      // 8. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      globalVehicleRef = null;\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  return (\n    <>\n      <span style={labelStyle}>路口选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择路口位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    const [vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([\n      loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/people.glb`)\n    ]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n        // // 保留原始贴图\n        // if (child.material.map) {\n        //   newMaterial.map = child.material.map;\n        // }\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    console.log('所有模型预加载成功');\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在5秒后自动移除\n  setTimeout(() => {\n    scene.remove(sprite);\n  }, 500);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  console.log('添加场景事件标记:', {\n    位置: position,\n    文本: text,\n    颜色: color\n  });\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,MAAM;AAC7B,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,GAAG,CAAC,CAAC;;AAEnB;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE;IACNC,GAAG,EAAE,2BAA2B;IAChCC,GAAG,EAAE,2BAA2B;IAChCZ,KAAK,EAAE,6BAA6B;IACpCa,GAAG,EAAE,2BAA2B,CAAE;EACpC;AACF,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,MAAMC,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAIC,MAAM,IAAK;EACjC,IAAI,CAACrB,YAAY,EAAE;IACjBA,YAAY,GAAGqB,MAAM,CAACC,KAAK,CAAC,CAAC;IAC7B,OAAOD,MAAM;EACf;EAEA,MAAME,SAAS,GAAGP,aAAa,CAACK,MAAM,CAACG,CAAC,EAAExB,YAAY,CAACwB,CAAC,EAAEtB,KAAK,CAAC;EAChE,MAAMuB,SAAS,GAAGT,aAAa,CAACK,MAAM,CAACK,CAAC,EAAE1B,YAAY,CAAC0B,CAAC,EAAExB,KAAK,CAAC;EAChE,MAAMyB,SAAS,GAAGX,aAAa,CAACK,MAAM,CAACO,CAAC,EAAE5B,YAAY,CAAC4B,CAAC,EAAE1B,KAAK,CAAC;EAEhEF,YAAY,CAAC6B,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACjD,OAAO3B,YAAY,CAACsB,KAAK,CAAC,CAAC;AAC7B,CAAC;;AAED;AACA,MAAMQ,cAAc,GAAIC,WAAW,IAAK;EACtC,IAAI9B,YAAY,KAAK,IAAI,EAAE;IACzBA,YAAY,GAAG8B,WAAW;IAC1B,OAAOA,WAAW;EACpB;;EAEA;EACA,IAAIC,IAAI,GAAGD,WAAW,GAAG9B,YAAY;EACrC,IAAI+B,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EAExC,MAAMC,gBAAgB,GAAGnB,aAAa,CAACf,YAAY,GAAG+B,IAAI,EAAE/B,YAAY,EAAEC,KAAK,CAAC;EAChFD,YAAY,GAAGkC,gBAAgB;EAC/B,OAAOA,gBAAgB;AACzB,CAAC;AAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAGjE,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMkE,UAAU,GAAGlE,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMmE,SAAS,GAAGnE,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAM+D,aAAa,GAAGpE,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMqE,eAAe,GAAGrE,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMsE,aAAa,GAAGtE,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMuE,iBAAiB,GAAGvE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAE;EACzC,MAAM,CAACwE,eAAe,EAAEC,kBAAkB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC;IAC/C2E,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMiF,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGnG,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAACoG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAMqG,uBAAuB,GAAG;IAC9BnB,QAAQ,EAAE,OAAO;IACjBoB,GAAG,EAAE,MAAM;IACXlB,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BkB,KAAK,EAAE,OAAO;IAAG;IACjBjB,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMQ,UAAU,GAAG;IACjBtB,QAAQ,EAAE,OAAO;IACjBoB,GAAG,EAAE,MAAM;IACXlB,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBe,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbX,QAAQ,EAAE,MAAM;IAChBY,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvCtB,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAG9G,QAAQ,CAAC,MAAM;IACjD,MAAM+G,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;IAChE,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG;MAC3C,KAAK,EAAE,CAAC;MAAG;MACX,KAAK,EAAE,CAAC;MAAG;MACX,KAAK,EAAE,CAAC;MAAG;MACX,KAAK,EAAE,CAAC;MAAG;MACX,KAAK,EAAE,CAAC;MAAG;MACX,MAAM,EAAE,CAAC;MAAE;MACX,KAAK,EAAE,CAAC,CAAG;IACb,CAAC;EACH,CAAC,CAAC;;EAEF;EACAjH,SAAS,CAAC,MAAM;IACd,MAAMsH,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BL,YAAY,CAACM,OAAO,CAAC,uBAAuB,EAAEJ,IAAI,CAACK,SAAS,CAACV,UAAU,CAAC,CAAC;IAC3E,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMW,YAAY,CAACJ,KAAK,CAAC;EAClC,CAAC,EAAE,CAACP,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMY,kBAAkB,GAAGA,CAAA,KAAM;IAC/BzC,WAAW,CAAC,QAAQ,CAAC;IACrB5D,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACqG,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B3C,WAAW,CAAC,QAAQ,CAAC;IACrB5D,UAAU,GAAG,QAAQ;IAErB,IAAI8E,SAAS,CAAC0B,OAAO,IAAIvG,QAAQ,EAAE;MACjC;MACA,MAAMwG,UAAU,GAAG3B,SAAS,CAAC0B,OAAO,CAAC1C,QAAQ,CAAClC,KAAK,CAAC,CAAC;MACrD,MAAM8E,SAAS,GAAG5B,SAAS,CAAC0B,OAAO,CAACG,EAAE,CAAC/E,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAI3C,KAAK,CAAC2H,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC;QAAE/E,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,GAAG;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChC4E,MAAM,CAAC7H,KAAK,CAAC8H,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdpC,SAAS,CAAC0B,OAAO,CAAC1C,QAAQ,CAACqD,IAAI,CAACV,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;MAEV;MACA,IAAInI,KAAK,CAAC2H,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAE/E,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9B4E,MAAM,CAAC7H,KAAK,CAAC8H,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdpC,SAAS,CAAC0B,OAAO,CAACG,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC;MACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAGpH,QAAQ,CAACqH,MAAM,CAAC1F,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAI3C,KAAK,CAAC2H,KAAK,CAACS,aAAa,CAAC,CAC3BR,EAAE,CAAC;QAAE/E,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9B4E,MAAM,CAAC7H,KAAK,CAAC8H,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdjH,QAAQ,CAACqH,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACAvC,SAAS,CAAC0B,OAAO,CAACe,MAAM,CAACtH,QAAQ,CAACqH,MAAM,CAAC;QACzCrH,QAAQ,CAACuH,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACAnH,QAAQ,CAACqG,OAAO,GAAG,IAAI;;MAEvB;MACArG,QAAQ,CAACwH,WAAW,GAAG,EAAE;MACzBxH,QAAQ,CAACyH,WAAW,GAAG,GAAG;MAC1BzH,QAAQ,CAAC0H,aAAa,GAAGpF,IAAI,CAACC,EAAE,GAAG,GAAG;MACtCvC,QAAQ,CAAC2H,aAAa,GAAG,CAAC;MAC1B3H,QAAQ,CAACuH,MAAM,CAAC,CAAC;MAEjBK,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAGhJ,iBAAiB,CAACiJ,aAAa,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKL,KAAK,CAAC;IAChF,IAAIC,YAAY,IAAItD,SAAS,CAAC0B,OAAO,IAAIvG,QAAQ,EAAE;MACjD+E,uBAAuB,CAACoD,YAAY,CAAC;;MAErC;MACA,MAAMK,WAAW,GAAG3F,SAAS,CAAC0D,OAAO,CAACkC,YAAY,CAChDC,UAAU,CAACP,YAAY,CAAC7E,SAAS,CAAC,EAClCoF,UAAU,CAACP,YAAY,CAAC5E,QAAQ,CAClC,CAAC;MAEDqE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvBc,IAAI,EAAER,YAAY,CAACI,IAAI;QACvBK,GAAG,EAAE;UACHtF,SAAS,EAAE6E,YAAY,CAAC7E,SAAS;UACjCC,QAAQ,EAAE4E,YAAY,CAAC5E;QACzB,CAAC;QACDsF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACAzI,UAAU,GAAG,cAAc;MAC3B4D,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAkB,SAAS,CAAC0B,OAAO,CAAC1C,QAAQ,CAAC3B,GAAG,CAACsG,WAAW,CAAC3G,CAAC,EAAE,GAAG,EAAE,CAAC2G,WAAW,CAACzG,CAAC,CAAC;;MAElE;MACA/B,QAAQ,CAACqH,MAAM,CAACnF,GAAG,CAACsG,WAAW,CAAC3G,CAAC,EAAE,CAAC,EAAE,CAAC2G,WAAW,CAACzG,CAAC,CAAC;;MAErD;MACA8C,SAAS,CAAC0B,OAAO,CAACe,MAAM,CAACtH,QAAQ,CAACqH,MAAM,CAAC;;MAEzC;MACArH,QAAQ,CAACqG,OAAO,GAAG,IAAI;MACvBrG,QAAQ,CAACuH,MAAM,CAAC,CAAC;;MAEjB;MACA1C,SAAS,CAAC0B,OAAO,CAACuC,YAAY,CAAC,CAAC;MAChCjE,SAAS,CAAC0B,OAAO,CAACwC,iBAAiB,CAAC,IAAI,CAAC;MAEzCnB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBc,IAAI,EAAER,YAAY,CAACI,IAAI;QACvBS,IAAI,EAAEnE,SAAS,CAAC0B,OAAO,CAAC1C,QAAQ,CAACoF,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAElJ,QAAQ,CAACqH,MAAM,CAAC4B,OAAO,CAAC,CAAC;QAC9BJ,IAAI,EAAEL;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMW,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC5C,IAAI,CAACjJ,KAAK,EAAE;MACVwH,OAAO,CAAC0B,KAAK,CAAC,QAAQ,CAAC;MACvB;IACF;IAEA1B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBuB,KAAK;MACLC,OAAO,EAAEA,OAAO,CAACE,QAAQ,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAMC,WAAW,GAAG3D,IAAI,CAACC,KAAK,CAACuD,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;;MAElD;MACA,IAAIH,KAAK,KAAK5I,WAAW,CAACM,MAAM,CAACG,GAAG,IAAIuI,WAAW,CAACC,IAAI,KAAK,KAAK,EAAE;QAClE7B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE2B,WAAW,CAAC;QAEpC,MAAME,OAAO,GAAGF,WAAW,CAACG,IAAI;QAChC,MAAMC,MAAM,GAAGF,OAAO,CAACG,IAAI,IAAI,EAAE;;QAEjC;QACA,MAAMC,YAAY,GAAG,CAAC,CAAC;QACvBF,MAAM,CAACG,OAAO,CAACC,KAAK,IAAI;UACtB,MAAMC,SAAS,GAAGD,KAAK,CAACC,SAAS;UACjC,IAAIA,SAAS,IAAIzE,UAAU,CAAC0E,cAAc,CAACD,SAAS,CAAC,EAAE;YACrDH,YAAY,CAACG,SAAS,CAAC,GAAG,CAACH,YAAY,CAACG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;UAC9D;;UAEA;UACA,MAAME,QAAQ,GAAGtH,SAAS,CAAC0D,OAAO,CAACkC,YAAY,CAC7CC,UAAU,CAACgB,OAAO,CAACU,OAAO,CAAC,EAC3B1B,UAAU,CAACgB,OAAO,CAACW,MAAM,CAC3B,CAAC;;UAED;UACA,IAAIC,WAAW,GAAG,EAAE;UACpB,IAAIC,YAAY,GAAG,EAAE;UAErB,QAAON,SAAS;YACd,KAAK,KAAK;cAAEK,WAAW,GAAG,OAAO;cAAEC,YAAY,GAAG,SAAS;cAAE;YAC7D,KAAK,KAAK;cAAED,WAAW,GAAG,OAAO;cAAEC,YAAY,GAAG,SAAS;cAAE;YAC7D,KAAK,KAAK;cAAED,WAAW,GAAG,QAAQ;cAAEC,YAAY,GAAG,SAAS;cAAE;YAC9D,KAAK,KAAK;cAAED,WAAW,GAAG,MAAM;cAAEC,YAAY,GAAG,SAAS;cAAE;YAC5D,KAAK,KAAK;cAAED,WAAW,GAAG,MAAM;cAAEC,YAAY,GAAG,SAAS;cAAE;YAC5D,KAAK,MAAM;cAAED,WAAW,GAAG,MAAM;cAAEC,YAAY,GAAG,SAAS;cAAE;YAC7D,KAAK,KAAK;cAAED,WAAW,GAAG,MAAM;cAAEC,YAAY,GAAG,SAAS;cAAE;YAC5D;cAASD,WAAW,GAAGN,KAAK,CAACQ,WAAW,IAAI,MAAM;cAAED,YAAY,GAAG,SAAS;UAC9E;;UAEA;UACAE,iBAAiB,CAACN,QAAQ,EAAEG,WAAW,EAAEC,YAAY,CAAC;QACxD,CAAC,CAAC;;QAEF;QACA,IAAIG,MAAM,CAACC,IAAI,CAACb,YAAY,CAAC,CAACc,MAAM,GAAG,CAAC,EAAE;UACxCnF,aAAa,CAACoF,IAAI,IAAI;YACpB,MAAMC,QAAQ,GAAG;cAAE,GAAGD;YAAK,CAAC;YAC5BH,MAAM,CAACK,OAAO,CAACjB,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,CAACN,IAAI,EAAEuB,KAAK,CAAC,KAAK;cACtDF,QAAQ,CAACrB,IAAI,CAAC,GAAG,CAACqB,QAAQ,CAACrB,IAAI,CAAC,IAAI,CAAC,IAAIuB,KAAK;YAChD,CAAC,CAAC;YACF,OAAOF,QAAQ;UACjB,CAAC,CAAC;QACJ;;QAEA;QACApK,MAAM,CAACuK,WAAW,CAAC;UACjBxB,IAAI,EAAE,KAAK;UACXE,IAAI,EAAEH,WAAW,CAACG;QACpB,CAAC,EAAE,GAAG,CAAC;QAEP;MACF;;MAEA;MACA,IAAIP,KAAK,KAAK5I,WAAW,CAACM,MAAM,CAACV,KAAK,IAAIoJ,WAAW,CAACC,IAAI,KAAK,OAAO,EAAE;QACtE7B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE2B,WAAW,CAAC;QAErC,MAAM0B,SAAS,GAAG1B,WAAW,CAACG,IAAI;QAClC,MAAMwB,OAAO,GAAGD,SAAS,CAACC,OAAO;QACjC,MAAMC,SAAS,GAAGF,SAAS,CAACE,SAAS;QACrC,MAAMC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAMxH,QAAQ,GAAG;UACfN,QAAQ,EAAEmF,UAAU,CAACwC,SAAS,CAACI,OAAO,CAAC;UACvChI,SAAS,EAAEoF,UAAU,CAACwC,SAAS,CAACK,QAAQ;QAC1C,CAAC;;QAED;QACA,MAAMpB,QAAQ,GAAGtH,SAAS,CAAC0D,OAAO,CAACkC,YAAY,CAAC5E,QAAQ,CAACP,SAAS,EAAEO,QAAQ,CAACN,QAAQ,CAAC;;QAEtF;QACA,QAAO6H,SAAS;UACd,KAAK,GAAG;YAAG;YACTX,iBAAiB,CAACN,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;YAClD;UACF,KAAK,KAAK;YAAG;YACXM,iBAAiB,CAACN,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACXM,iBAAiB,CAACN,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;YAC/C;UACF,KAAK,IAAI;YAAG;YACV,MAAMqB,UAAU,GAAGN,SAAS,CAACO,UAAU,CAAC,CAAE;YAC1ChB,iBAAiB,CAACN,QAAQ,EAAE,KAAKqB,UAAU,MAAM,EAAE,SAAS,CAAC;YAC7D;UACF,KAAK,IAAI;YAAG;YACVf,iBAAiB,CAACN,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVM,iBAAiB,CAACN,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YAAG;YACZM,iBAAiB,CAACN,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVM,iBAAiB,CAACN,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVM,iBAAiB,CAACN,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX,MAAMuB,YAAY,GAAGR,SAAS,CAACO,UAAU,CAAC,CAAE;YAC5C,MAAME,QAAQ,GAAGT,SAAS,CAACU,UAAU,CAAC,CAAM;YAC5CnB,iBAAiB,CAACN,QAAQ,EAAE,QAAQ0B,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,EAAE,SAAS,CAAC;YAC/F;QACJ;QAEA;MACF;;MAEA;MACA,IAAIvC,KAAK,KAAK5I,WAAW,CAACM,MAAM,CAACE,GAAG,IAAIwI,WAAW,CAACC,IAAI,KAAK,KAAK,EAAE;QAAA,IAAAqC,iBAAA;QAClElE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE2B,WAAW,CAAC;QAEpC,MAAMuC,YAAY,GAAG,EAAAD,iBAAA,GAAAtC,WAAW,CAACG,IAAI,cAAAmC,iBAAA,uBAAhBA,iBAAA,CAAkBC,YAAY,KAAI,EAAE;QACzD,MAAMC,KAAK,GAAGxC,WAAW,CAACG,IAAI,CAACqC,KAAK;;QAEpC;QACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;QAEtB;QACAF,YAAY,CAAChC,OAAO,CAACoC,WAAW,IAAI;UAClC;UACA,MAAMC,EAAE,GAAIJ,KAAK,GAAGG,WAAW,CAACE,SAAS;UACzC,MAAM5C,IAAI,GAAG0C,WAAW,CAACG,WAAW;UAEpC,IAAG7C,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACA;YACA,MAAM8C,KAAK,GAAG;cACZjJ,SAAS,EAAEoF,UAAU,CAACyD,WAAW,CAACK,WAAW,CAAC;cAC9CjJ,QAAQ,EAAEmF,UAAU,CAACyD,WAAW,CAACM,UAAU,CAAC;cAC5CjJ,KAAK,EAAEkF,UAAU,CAACyD,WAAW,CAACO,SAAS,CAAC;cACxCjJ,OAAO,EAAEiF,UAAU,CAACyD,WAAW,CAACQ,WAAW;YAC7C,CAAC;YAED,MAAMxC,QAAQ,GAAGtH,SAAS,CAAC0D,OAAO,CAACkC,YAAY,CAAC8D,KAAK,CAACjJ,SAAS,EAAEiJ,KAAK,CAAChJ,QAAQ,CAAC;;YAEhF;YACA,IAAIqJ,cAAc;YAClB,QAAQnD,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRmD,cAAc,GAAG3M,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACR2M,cAAc,GAAG1M,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACR0M,cAAc,GAAGzM,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACA,IAAI0M,KAAK,GAAG1L,aAAa,CAAC2L,GAAG,CAACV,EAAE,CAAC;YAEjC,IAAI,CAACS,KAAK,IAAID,cAAc,EAAE;cAC5B;cACA,MAAMG,QAAQ,GAAGH,cAAc,CAACjL,KAAK,CAAC,CAAC;cACvC;cACA,MAAMqL,MAAM,GAAGvD,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;cACzCsD,QAAQ,CAAClJ,QAAQ,CAAC3B,GAAG,CAACiI,QAAQ,CAACtI,CAAC,EAAEmL,MAAM,EAAE,CAAC7C,QAAQ,CAACpI,CAAC,CAAC;cACtDgL,QAAQ,CAACE,QAAQ,CAAClL,CAAC,GAAGO,IAAI,CAACC,EAAE,GAAGgK,KAAK,CAAC9I,OAAO,GAAGnB,IAAI,CAACC,EAAE,GAAG,GAAG;cAC7DnC,KAAK,CAAC8M,GAAG,CAACH,QAAQ,CAAC;cAEnB5L,aAAa,CAACe,GAAG,CAACkK,EAAE,EAAE;gBACpBS,KAAK,EAAEE,QAAQ;gBACfI,UAAU,EAAElB,GAAG;gBACfxC,IAAI,EAAEA;cACR,CAAC,CAAC;YACJ,CAAC,MAAM,IAAIoD,KAAK,EAAE;cAChB;cACAA,KAAK,CAACA,KAAK,CAAChJ,QAAQ,CAAC3B,GAAG,CAACiI,QAAQ,CAACtI,CAAC,EAAEgL,KAAK,CAACpD,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAACU,QAAQ,CAACpI,CAAC,CAAC;cACjF8K,KAAK,CAACA,KAAK,CAACI,QAAQ,CAAClL,CAAC,GAAGO,IAAI,CAACC,EAAE,GAAGgK,KAAK,CAAC9I,OAAO,GAAGnB,IAAI,CAACC,EAAE,GAAG,GAAG;cAChEsK,KAAK,CAACM,UAAU,GAAGlB,GAAG;cACtBY,KAAK,CAACA,KAAK,CAAC/D,YAAY,CAAC,CAAC;cAC1B+D,KAAK,CAACA,KAAK,CAAC9D,iBAAiB,CAAC,IAAI,CAAC;YACrC;UACA;QACF,CAAC,CAAC;;QAEF;QACA,MAAMqE,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAACvB,YAAY,CAACwB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,SAAS,CAAC,CAAC;QAE9DlL,aAAa,CAAC4I,OAAO,CAAC,CAAC0D,SAAS,EAAErB,EAAE,KAAK;UACvC,IAAIH,GAAG,GAAGwB,SAAS,CAACN,UAAU,GAAGC,iBAAiB,IAAI,CAACC,UAAU,CAACK,GAAG,CAACtB,EAAE,CAAC,EAAE;YACzEhM,KAAK,CAACuN,MAAM,CAACF,SAAS,CAACZ,KAAK,CAAC;YAC7B1L,aAAa,CAACyM,MAAM,CAACxB,EAAE,CAAC;YACxBxE,OAAO,CAACC,GAAG,CAAC,oBAAoBuE,EAAE,QAAQqB,SAAS,CAAChE,IAAI,EAAE,CAAC;UAC7D;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAIL,KAAK,KAAK5I,WAAW,CAACM,MAAM,CAACC,GAAG,IAAIyI,WAAW,CAACC,IAAI,KAAK,KAAK,EAAE;QAClE7B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE2B,WAAW,CAAC;QAEpC,MAAMqE,OAAO,GAAGrE,WAAW,CAACG,IAAI;QAChC,MAAMmE,KAAK,GAAGD,OAAO,CAACE,KAAK;QAC3B,MAAMC,QAAQ,GAAG;UACf1K,SAAS,EAAEoF,UAAU,CAACmF,OAAO,CAACtC,QAAQ,CAAC;UACvChI,QAAQ,EAAEmF,UAAU,CAACmF,OAAO,CAACvC,OAAO,CAAC;UACrC9H,KAAK,EAAEkF,UAAU,CAACmF,OAAO,CAACnB,SAAS,CAAC;UACpCjJ,OAAO,EAAEiF,UAAU,CAACmF,OAAO,CAAClB,WAAW;QACzC,CAAC;QAED/E,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEmG,QAAQ,CAAC;QAClCpG,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEiG,KAAK,CAAC;QAC3B;QACA,IAAItO,gBAAgB,EAAE;UACpB,MAAM2K,QAAQ,GAAGtH,SAAS,CAAC0D,OAAO,CAACkC,YAAY,CAACuF,QAAQ,CAAC1K,SAAS,EAAE0K,QAAQ,CAACzK,QAAQ,CAAC;UACtF,MAAM0K,WAAW,GAAG,IAAIrP,KAAK,CAACsP,OAAO,CAAC/D,QAAQ,CAACtI,CAAC,EAAE,GAAG,EAAE,CAACsI,QAAQ,CAACpI,CAAC,CAAC;UACnE,MAAMK,WAAW,GAAGE,IAAI,CAACC,EAAE,GAAGyL,QAAQ,CAACvK,OAAO,GAAGnB,IAAI,CAACC,EAAE,GAAG,GAAG;;UAE9D;UACA,MAAM4L,gBAAgB,GAAG1M,cAAc,CAACwM,WAAW,CAAC;UACpD,MAAMzL,gBAAgB,GAAGL,cAAc,CAACC,WAAW,CAAC;;UAEpD;UACA5C,gBAAgB,CAACqE,QAAQ,CAACqD,IAAI,CAACiH,gBAAgB,CAAC;UAChD3O,gBAAgB,CAACyN,QAAQ,CAAClL,CAAC,GAAGS,gBAAgB;UAC9ChD,gBAAgB,CAACsJ,YAAY,CAAC,CAAC;UAC/BtJ,gBAAgB,CAACuJ,iBAAiB,CAAC,IAAI,CAAC;;UAExC;UACA1F,eAAe,CAAC2K,QAAQ,CAAC;UACzBpG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsG,gBAAgB,CAAC;QAC3C;QACA;MACF;;MAEA;MACAvG,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBuB,KAAK;QACLK,IAAI,EAAED,WAAW,CAACC,IAAI;QACtBE,IAAI,EAAEH;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC1B,OAAO,CAAC0B,KAAK,CAAC,SAAS,EAAED,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAM6E,cAAc,GAAGA,CAAA,KAAM;IAC3BxG,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAMwG,KAAK,GAAG,QAAQ7N,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnE+G,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEwG,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChB5G,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAEDyG,EAAE,CAACG,SAAS,GAAIzE,KAAK,IAAK;MACxB,IAAI;QACF,MAAMX,OAAO,GAAGxD,IAAI,CAACC,KAAK,CAACkE,KAAK,CAACL,IAAI,CAAC;;QAEtC;QACA,IAAIN,OAAO,CAACI,IAAI,KAAK,SAAS,EAAE;UAC9B7B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEwB,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACI,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIJ,OAAO,CAACI,IAAI,KAAK,SAAS,IAAIJ,OAAO,CAACD,KAAK,IAAIC,OAAO,CAACqF,OAAO,EAAE;UAClE;UACAvF,iBAAiB,CAACE,OAAO,CAACD,KAAK,EAAEvD,IAAI,CAACK,SAAS,CAACmD,OAAO,CAACqF,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOpF,KAAK,EAAE;QACd1B,OAAO,CAAC0B,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDgF,EAAE,CAACK,OAAO,GAAIrF,KAAK,IAAK;MACtB1B,OAAO,CAAC0B,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDgF,EAAE,CAACM,OAAO,GAAG,MAAM;MACjBhH,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACA7B,UAAU,CAACoI,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACApL,aAAa,CAACuD,OAAO,GAAG+H,EAAE;EAC5B,CAAC;EAED7P,SAAS,CAAC,MAAM;IACd,IAAI,CAACkE,YAAY,CAAC4D,OAAO,EAAE;;IAE3B;IACAsI,aAAa,CAAC,CAAC;;IAEf;IACAzO,KAAK,GAAG,IAAIxB,KAAK,CAACkQ,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAInQ,KAAK,CAACoQ,iBAAiB,CACxC,EAAE,EACFtO,MAAM,CAACuO,UAAU,GAAGvO,MAAM,CAACwO,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAAClL,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChC6M,MAAM,CAACzH,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBzC,SAAS,CAAC0B,OAAO,GAAGwI,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAIvQ,KAAK,CAACwQ,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAAC5O,MAAM,CAACuO,UAAU,EAAEvO,MAAM,CAACwO,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAAC9O,MAAM,CAAC+O,gBAAgB,CAAC;IAC/C9M,YAAY,CAAC4D,OAAO,CAACmJ,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAIhR,KAAK,CAACiR,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5DzP,KAAK,CAAC8M,GAAG,CAAC0C,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAIlR,KAAK,CAACmR,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAACjM,QAAQ,CAAC3B,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1C9B,KAAK,CAAC8M,GAAG,CAAC4C,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAIpR,KAAK,CAACmR,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAACnM,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3C9B,KAAK,CAAC8M,GAAG,CAAC8C,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAIrR,KAAK,CAACsR,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAACpM,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChC+N,SAAS,CAACE,KAAK,GAAG7N,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7B0N,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxBlQ,KAAK,CAAC8M,GAAG,CAAC+C,SAAS,CAAC;;IAEpB;IACAjQ,QAAQ,GAAG,IAAIlB,aAAa,CAACiQ,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzD3P,QAAQ,CAACuQ,aAAa,GAAG,IAAI;IAC7BvQ,QAAQ,CAACwQ,aAAa,GAAG,IAAI;IAC7BxQ,QAAQ,CAACyQ,kBAAkB,GAAG,KAAK;IACnCzQ,QAAQ,CAACwH,WAAW,GAAG,EAAE;IACzBxH,QAAQ,CAACyH,WAAW,GAAG,GAAG;IAC1BzH,QAAQ,CAAC0H,aAAa,GAAGpF,IAAI,CAACC,EAAE,GAAG,GAAG;IACtCvC,QAAQ,CAAC2H,aAAa,GAAG,CAAC;IAC1B3H,QAAQ,CAACqH,MAAM,CAACnF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BlC,QAAQ,CAACuH,MAAM,CAAC,CAAC;;IAEjB;IACAK,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBkH,MAAM,EAAE,CAAC,CAACA,MAAM;MAChB/O,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpB6E,SAAS,EAAE,CAAC,CAACA,SAAS,CAAC0B;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMmK,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAIjS,UAAU,CAAC,CAAC;QACtCiS,aAAa,CAACC,IAAI,CAChB,GAAG7P,QAAQ,uBAAuB,EACjC8P,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAAC5Q,KAAK;;UAE/B;UACA,MAAM8Q,gBAAgB,GAAG,IAAItS,KAAK,CAACuS,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACG,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAI5S,KAAK,CAAC6S,oBAAoB,CAAC;kBACjDpM,KAAK,EAAE,QAAQ;kBAAO;kBACtBqM,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIP,KAAK,CAACE,QAAQ,CAAChE,GAAG,EAAE;kBACtBiE,WAAW,CAACjE,GAAG,GAAG8D,KAAK,CAACE,QAAQ,CAAChE,GAAG;gBACtC;;gBAEA;gBACA8D,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5B5J,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEwJ,KAAK,CAAC9I,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAM0I,YAAY,CAACY,QAAQ,CAACjH,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMyG,KAAK,GAAGJ,YAAY,CAACY,QAAQ,CAAC,CAAC,CAAC;YACtCX,gBAAgB,CAAChE,GAAG,CAACmE,KAAK,CAAC;UAC7B;;UAEA;UACAjR,KAAK,CAAC8M,GAAG,CAACgE,gBAAgB,CAAC;;UAE3B;UACA1R,gBAAgB,GAAG0R,gBAAgB;UAEnCtJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9B1E,kBAAkB,CAAC,IAAI,CAAC;UACxByN,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAY,GAAG,IAAK;UACPlK,OAAO,CAACC,GAAG,CAAC,aAAa,CAACiK,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDpB,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMqB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMhB,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACAtC,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAI8C,gBAAgB,EAAE;UACpB,MAAMiB,YAAY,GAAG;YACnB7O,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UAED,MAAM2O,UAAU,GAAGvP,SAAS,CAAC0D,OAAO,CAACkC,YAAY,CAAC0J,YAAY,CAAC7O,SAAS,EAAE6O,YAAY,CAAC5O,QAAQ,CAAC;UAChG;UACA2N,gBAAgB,CAACrN,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACxCgP,gBAAgB,CAACjE,QAAQ,CAAClL,CAAC,GAAIO,IAAI,CAACC,EAAE,GAAG4P,YAAY,CAAC1O,OAAO,GAAGnB,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9E2O,gBAAgB,CAACpI,YAAY,CAAC,CAAC;UAC/BoI,gBAAgB,CAACnI,iBAAiB,CAAC,IAAI,CAAC;UACxClJ,eAAe,GAAGqR,gBAAgB,CAACrN,QAAQ,CAAClC,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAO2H,KAAK,EAAE;QACd1B,OAAO,CAAC0B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAM+I,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAI5B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAM2B,WAAW,GAAIC,WAAW,IAAK;UACnC7K,OAAO,CAACC,GAAG,CAAC,WAAWyK,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAI7T,UAAU,CAAC,CAAC;UAC/B6T,MAAM,CAAC3B,IAAI,CACTuB,GAAG,EACFtB,IAAI,IAAK;YACRpJ,OAAO,CAACC,GAAG,CAAC,WAAWyK,GAAG,EAAE,CAAC;YAC7B1B,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAc,GAAG,IAAK;YACPlK,OAAO,CAACC,GAAG,CAAC,SAAS,CAACiK,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACA3I,KAAK,IAAK;YACT1B,OAAO,CAAC0B,KAAK,CAAC,SAASgJ,GAAG,EAAE,EAAEhJ,KAAK,CAAC;YACpC,IAAImJ,WAAW,GAAG,CAAC,EAAE;cACnB7K,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3B7B,UAAU,CAAC,MAAMwM,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACL5B,MAAM,CAACvH,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAEDkJ,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAI7T,UAAU,CAAC,CAAC;IAC/B6T,MAAM,CAAC3B,IAAI,CACT,GAAG7P,QAAQ,4BAA4B,EACvC,MAAO8P,IAAI,IAAK;MACd,IAAI;QACF,MAAMnE,KAAK,GAAGmE,IAAI,CAAC5Q,KAAK;QACxByM,KAAK,CAAC8F,KAAK,CAACzQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxB2K,KAAK,CAAChJ,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3B9B,KAAK,CAAC8M,GAAG,CAACL,KAAK,CAAC;;QAEhB;QACA,MAAMqF,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC,OAAO5I,KAAK,EAAE;QACd1B,OAAO,CAAC0B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACAwI,GAAG,IAAK;MACPlK,OAAO,CAACC,GAAG,CAAC,SAAS,CAACiK,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACA3I,KAAK,IAAK;MACT1B,OAAO,CAAC0B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B1B,OAAO,CAAC0B,KAAK,CAAC,OAAO,EAAE;QACrBsJ,IAAI,EAAEtJ,KAAK,CAACG,IAAI;QAChBoJ,IAAI,EAAEvJ,KAAK,CAACD,OAAO;QACnByJ,KAAK,EAAE,GAAG5R,QAAQ,4BAA4B;QAC9C6R,KAAK,EAAE,GAAG7R,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAM8R,OAAO,GAAGA,CAAA,KAAM;MACpB/P,iBAAiB,CAACsD,OAAO,GAAG0M,qBAAqB,CAACD,OAAO,CAAC;;MAE1D;MACAhU,KAAK,CAACuI,MAAM,CAAC,CAAC;MAEd,IAAIxH,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACqG,OAAO,GAAG,KAAK;;QAExB;QACA,MAAM6M,UAAU,GAAG1T,gBAAgB,CAACqE,QAAQ,CAAClC,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMwR,eAAe,GAAG3T,gBAAgB,CAACyN,QAAQ,CAAClL,CAAC;;QAEnD;QACA;QACA,MAAMqR,gBAAgB,GAAG,EAAED,eAAe,GAAG7Q,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAM8Q,YAAY,GAAG,IAAIzU,KAAK,CAACsP,OAAO,CACpC,CAAC,EAAE,GAAG5L,IAAI,CAACgR,GAAG,CAACF,gBAAgB,CAAC,EAChC,GAAG,EACH,CAAC,EAAE,GAAG9Q,IAAI,CAACiR,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACArE,MAAM,CAAClL,QAAQ,CAACqD,IAAI,CAACgM,UAAU,CAAC,CAAChG,GAAG,CAACmG,YAAY,CAAC;;QAElD;QACAtE,MAAM,CAACrI,EAAE,CAACxE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAMsR,YAAY,GAAGN,UAAU,CAACvR,KAAK,CAAC,CAAC;QACvCoN,MAAM,CAACzH,MAAM,CAACkM,YAAY,CAAC;;QAE3B;QACAzE,MAAM,CAAC0E,sBAAsB,CAAC,CAAC;QAC/B1E,MAAM,CAACjG,YAAY,CAAC,CAAC;QACrBiG,MAAM,CAAChG,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACA/I,QAAQ,CAACqG,OAAO,GAAG,KAAK;;QAExB;QACArG,QAAQ,CAACqH,MAAM,CAACH,IAAI,CAACgM,UAAU,CAAC;QAChClT,QAAQ,CAACuH,MAAM,CAAC,CAAC;QAEjBK,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnB6L,IAAI,EAAER,UAAU,CAACjK,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAE+F,MAAM,CAAClL,QAAQ,CAACoF,OAAO,CAAC,CAAC;UAC/B0K,IAAI,EAAEH,YAAY,CAACvK,OAAO,CAAC,CAAC;UAC5B2K,IAAI,EAAE7E,MAAM,CAAC8E,iBAAiB,CAAC,IAAIjV,KAAK,CAACsP,OAAO,CAAC,CAAC,CAAC,CAACjF,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIlJ,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAACqG,OAAO,GAAG,IAAI;;QAEvB;QACA0I,MAAM,CAACrI,EAAE,CAACxE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAII,IAAI,CAACwR,GAAG,CAAC/E,MAAM,CAAClL,QAAQ,CAAC9B,CAAC,CAAC,GAAG,EAAE,EAAE;UACpCgN,MAAM,CAAClL,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BlC,QAAQ,CAACqH,MAAM,CAACnF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B6M,MAAM,CAACzH,MAAM,CAACtH,QAAQ,CAACqH,MAAM,CAAC;UAC9BrH,QAAQ,CAACuH,MAAM,CAAC,CAAC;QACnB;;QAEA;QACA;QACAwH,MAAM,CAACjG,YAAY,CAAC,CAAC;QACrBiG,MAAM,CAAChG,iBAAiB,CAAC,IAAI,CAAC;MAEhC,CAAC,MAAM,IAAIhJ,UAAU,KAAK,cAAc,EAAE;QACxC;QACAC,QAAQ,CAACuH,MAAM,CAAC,CAAC;MACnB;MAEA,IAAIvH,QAAQ,EAAEA,QAAQ,CAACuH,MAAM,CAAC,CAAC;MAC/B,IAAInH,KAAK,IAAI2O,MAAM,EAAE;QACnBI,QAAQ,CAAC4E,MAAM,CAAC3T,KAAK,EAAE2O,MAAM,CAAC;MAChC;IACF,CAAC;IAEDiE,OAAO,CAAC,CAAC;;IAET;IACA,MAAMgB,YAAY,GAAGA,CAAA,KAAM;MACzBjF,MAAM,CAACkF,MAAM,GAAGvT,MAAM,CAACuO,UAAU,GAAGvO,MAAM,CAACwO,WAAW;MACtDH,MAAM,CAAC0E,sBAAsB,CAAC,CAAC;MAC/BtE,QAAQ,CAACG,OAAO,CAAC5O,MAAM,CAACuO,UAAU,EAAEvO,MAAM,CAACwO,WAAW,CAAC;IACzD,CAAC;IACDxO,MAAM,CAACwT,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACAtT,MAAM,CAACyT,aAAa,GAAG,MAAM;MAC3B,IAAItP,SAAS,CAAC0B,OAAO,EAAE;QACrB1B,SAAS,CAAC0B,OAAO,CAAC1C,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC2C,SAAS,CAAC0B,OAAO,CAACe,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCzC,SAAS,CAAC0B,OAAO,CAACuC,YAAY,CAAC,CAAC;QAChCjE,SAAS,CAAC0B,OAAO,CAACwC,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAI/I,QAAQ,EAAE;UACZA,QAAQ,CAACqH,MAAM,CAACnF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BlC,QAAQ,CAACqG,OAAO,GAAG,IAAI;UACvBrG,QAAQ,CAACuH,MAAM,CAAC,CAAC;QACnB;QAEAxH,UAAU,GAAG,QAAQ;QACrB6H,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAI5E,iBAAiB,CAACsD,OAAO,EAAE;QAC7B6N,oBAAoB,CAACnR,iBAAiB,CAACsD,OAAO,CAAC;QAC/CtD,iBAAiB,CAACsD,OAAO,GAAG,IAAI;MAClC;;MAEA;MACA,IAAI5G,oBAAoB,EAAE;QACxB0U,aAAa,CAAC1U,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;;MAEA;MACA,IAAIqD,aAAa,CAACuD,OAAO,EAAE;QACzBvD,aAAa,CAACuD,OAAO,CAAC+N,KAAK,CAAC,CAAC;QAC7BtR,aAAa,CAACuD,OAAO,GAAG,IAAI;MAC9B;;MAEA;MACA7F,MAAM,CAAC6T,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;;MAElD;MACA,IAAI7E,QAAQ,IAAIxM,YAAY,CAAC4D,OAAO,EAAE;QACpC5D,YAAY,CAAC4D,OAAO,CAACiO,WAAW,CAACrF,QAAQ,CAACQ,UAAU,CAAC;QACrDR,QAAQ,CAACsF,OAAO,CAAC,CAAC;MACpB;;MAEA;MACA,IAAItT,aAAa,EAAE;QACjBA,aAAa,CAAC4I,OAAO,CAAC,CAAC0D,SAAS,EAAErB,EAAE,KAAK;UACvC,IAAIqB,SAAS,CAACZ,KAAK,IAAIzM,KAAK,EAAE;YAC5BA,KAAK,CAACuN,MAAM,CAACF,SAAS,CAACZ,KAAK,CAAC;UAC/B;QACF,CAAC,CAAC;QACF1L,aAAa,CAACuT,KAAK,CAAC,CAAC;MACvB;;MAEA;MACA,IAAItU,KAAK,EAAE;QACT,OAAMA,KAAK,CAACyR,QAAQ,CAACjH,MAAM,GAAG,CAAC,EAAE;UAC/BxK,KAAK,CAACuN,MAAM,CAACvN,KAAK,CAACyR,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC;MACF;;MAEA;MACAzR,KAAK,GAAG,IAAI;MACZJ,QAAQ,GAAG,IAAI;MACfC,qBAAqB,GAAG,IAAI;MAC5BC,qBAAqB,GAAG,IAAI;MAC5BC,oBAAoB,GAAG,IAAI;MAC3BX,gBAAgB,GAAG,IAAI;MAEvBoI,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACExI,OAAA,CAAAE,SAAA;IAAAsS,QAAA,gBACExS,OAAA;MAAMsV,KAAK,EAAExP,UAAW;MAAA0M,QAAA,EAAC;IAAK;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrC1V,OAAA,CAACH,MAAM;MACLyV,KAAK,EAAE3P,uBAAwB;MAC/BgQ,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAEhN,wBAAyB;MACnCiN,OAAO,EAAE/V,iBAAiB,CAACiJ,aAAa,CAACmF,GAAG,CAACpF,YAAY,KAAK;QAC5DD,KAAK,EAAEC,YAAY,CAACI,IAAI;QACxB4M,KAAK,EAAEhN,YAAY,CAACI;MACtB,CAAC,CAAC,CAAE;MACJ6M,IAAI,EAAC,OAAO;MACZC,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACbrR,MAAM,EAAE,IAAI;QACZsR,SAAS,EAAE;MACb;IAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACF1V,OAAA;MAAKmW,GAAG,EAAE7S,YAAa;MAACgS,KAAK,EAAE;QAAEzP,KAAK,EAAE,MAAM;QAAE8H,MAAM,EAAE;MAAO;IAAE;MAAA4H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpE1V,OAAA;MAAKsV,KAAK,EAAE/Q,oBAAqB;MAAAiO,QAAA,gBAC/BxS,OAAA;QACEsV,KAAK,EAAE;UACL,GAAGvQ,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/E2B,KAAK,EAAE3B,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF+R,OAAO,EAAErP,kBAAmB;QAAAyL,QAAA,EAC7B;MAED;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1V,OAAA;QACEsV,KAAK,EAAE;UACL,GAAGvQ,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/E2B,KAAK,EAAE3B,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF+R,OAAO,EAAEnP,kBAAmB;QAAAuL,QAAA,EAC7B;MAED;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAArS,EAAA,CAp+BMD,WAAW;AAAAiT,EAAA,GAAXjT,WAAW;AAq+BjB,SAASkT,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAAC3Q,KAAK,GAAG,GAAG;EAClB2Q,MAAM,CAAC7I,MAAM,GAAG,EAAE;;EAElB;EACAgJ,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAAC3Q,KAAK,GAAC,CAAC,EAAE2Q,MAAM,CAAC7I,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAMsJ,OAAO,GAAG,IAAI1X,KAAK,CAAC2X,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAI5X,KAAK,CAAC6X,cAAc,CAAC;IAC9ClJ,GAAG,EAAE+I,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAI/X,KAAK,CAACgY,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAAChE,KAAK,CAACzQ,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAOyU,MAAM;AACf;;AAEA;AACAjW,MAAM,CAACmW,WAAW,GAAG,CAAChV,CAAC,EAAEE,CAAC,EAAEE,CAAC,KAAK;EAChC,IAAIzC,gBAAgB,EAAE;IACpBA,gBAAgB,CAACqE,QAAQ,CAAC3B,GAAG,CAACL,CAAC,EAAEE,CAAC,EAAEE,CAAC,CAAC;IACtCzC,gBAAgB,CAACsJ,YAAY,CAAC,CAAC;IAC/BtJ,gBAAgB,CAACuJ,iBAAiB,CAAC,IAAI,CAAC;IACxCnB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAAChG,CAAC;MAAEE,CAAC;MAAEE;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACAvB,MAAM,CAACoW,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAM/H,MAAM,GAAG+G,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAIlI,MAAM,EAAE;MACV;MACA,MAAMmI,MAAM,GAAGnI,MAAM,CAAClL,QAAQ,CAAClC,KAAK,CAAC,CAAC;;MAEtC;MACAoN,MAAM,CAAClL,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9B6M,MAAM,CAACrI,EAAE,CAACxE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtB6M,MAAM,CAACzH,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAyH,MAAM,CAACjG,YAAY,CAAC,CAAC;MACrBiG,MAAM,CAAChG,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAM/I,QAAQ,GAAG8V,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAInX,QAAQ,EAAE;QACZA,QAAQ,CAACqH,MAAM,CAACnF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BlC,QAAQ,CAACuH,MAAM,CAAC,CAAC;MACnB;MAEAK,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBuP,GAAG,EAAEF,MAAM,CAACjO,OAAO,CAAC,CAAC;QACrBoO,GAAG,EAAEtI,MAAM,CAAClL,QAAQ,CAACoF,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOqO,CAAC,EAAE;IACV1P,OAAO,CAAC0B,KAAK,CAAC,YAAY,EAAEgO,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAMzI,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACFjH,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAM6K,MAAM,GAAG,IAAI7T,UAAU,CAAC,CAAC;;IAE/B;IACA,MAAM,CAAC0Y,WAAW,EAAEC,WAAW,EAAEC,UAAU,CAAC,GAAG,MAAM9G,OAAO,CAAC+G,GAAG,CAAC,CAC/DhF,MAAM,CAACiF,SAAS,CAAC,GAAGzW,QAAQ,uBAAuB,CAAC,EACpDwR,MAAM,CAACiF,SAAS,CAAC,GAAGzW,QAAQ,uBAAuB,CAAC,EACpDwR,MAAM,CAACiF,SAAS,CAAC,GAAGzW,QAAQ,sBAAsB,CAAC,CACpD,CAAC;;IAEF;IACAjB,qBAAqB,GAAGsX,WAAW,CAACnX,KAAK;IACzCH,qBAAqB,CAACmR,QAAQ,CAAEC,KAAK,IAAK;MACxC,IAAIA,KAAK,CAACC,MAAM,EAAE;QAChBD,KAAK,CAACE,QAAQ,GAAG,IAAI3S,KAAK,CAAC6S,oBAAoB,CAAC;UAC9CpM,KAAK,EAAE,QAAQ;UACfqM,SAAS,EAAE,GAAG;UACdC,SAAS,EAAE,GAAG;UACdC,eAAe,EAAE;QACnB,CAAC,CAAC;;QAEF;QACA;QACA;QACA;MACF;IACF,CAAC,CAAC;;IAEF;IACA1R,qBAAqB,GAAGsX,WAAW,CAACpX,KAAK;IACzC;IACAF,qBAAqB,CAACyS,KAAK,CAACzQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxC;IACAhC,qBAAqB,CAACkR,QAAQ,CAAEC,KAAK,IAAK;MACxC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClC;QACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;QAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;QAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;MACtC;IACF,CAAC,CAAC;;IAEF;IACAzR,oBAAoB,GAAGsX,UAAU,CAACrX,KAAK;IACvC;IACAD,oBAAoB,CAACwS,KAAK,CAACzQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvC;IACA/B,oBAAoB,CAACiR,QAAQ,CAAEC,KAAK,IAAK;MACvC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClC;QACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;QAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;QAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;MACtC;IACF,CAAC,CAAC;IAEFhK,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,CAAC,OAAOyB,KAAK,EAAE;IACd1B,OAAO,CAAC0B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;;AAED;AACA,MAAMuC,mBAAmB,GAAIpC,IAAI,IAAK;EACpC,MAAMmO,KAAK,GAAG;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,KAAK,CAACnO,IAAI,CAAC,IAAI,MAAM;AAC9B,CAAC;;AAED;AACA,MAAMgB,iBAAiB,GAAGA,CAAC5G,QAAQ,EAAE+R,IAAI,EAAEvQ,KAAK,KAAK;EACnD;EACA,MAAMsR,MAAM,GAAGhB,gBAAgB,CAACC,IAAI,CAAC;EACrCe,MAAM,CAAC9S,QAAQ,CAAC3B,GAAG,CAAC2B,QAAQ,CAAChC,CAAC,EAAE,EAAE,EAAE,CAACgC,QAAQ,CAAC9B,CAAC,CAAC,CAAC,CAAE;;EAEnD;EACAiE,UAAU,CAAC,MAAM;IACf5F,KAAK,CAACuN,MAAM,CAACgJ,MAAM,CAAC;EACtB,CAAC,EAAE,GAAG,CAAC;;EAEP;EACAvW,KAAK,CAAC8M,GAAG,CAACyJ,MAAM,CAAC;EAEjB/O,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;IACvBgQ,EAAE,EAAEhU,QAAQ;IACZiU,EAAE,EAAElC,IAAI;IACRmC,EAAE,EAAE1S;EACN,CAAC,CAAC;AACJ,CAAC;AAED,eAAe5C,WAAW;AAAC,IAAAiT,EAAA;AAAAsC,YAAA,CAAAtC,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}