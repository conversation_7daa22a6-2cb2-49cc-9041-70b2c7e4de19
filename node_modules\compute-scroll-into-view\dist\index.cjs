"use strict";Object.defineProperty(exports,"__esModule",{value:!0});const t=t=>"object"==typeof t&&null!=t&&1===t.nodeType,e=(t,e)=>(!e||"hidden"!==t)&&("visible"!==t&&"clip"!==t),o=(t,o)=>{if(t.clientHeight<t.scrollHeight||t.clientWidth<t.scrollWidth){const n=getComputedStyle(t,null);return e(n.overflowY,o)||e(n.overflowX,o)||(t=>{const e=(t=>{if(!t.ownerDocument||!t.ownerDocument.defaultView)return null;try{return t.ownerDocument.defaultView.frameElement}catch(t){return null}})(t);return!!e&&(e.clientHeight<t.scrollHeight||e.clientWidth<t.scrollWidth)})(t)}return!1},n=(t,e,o,n,l,r,i,s)=>r<t&&i>e||r>t&&i<e?0:r<=t&&s<=o||i>=e&&s>=o?r-t-n:i>e&&s<o||r<t&&s>o?i-e+l:0,l=t=>{const e=t.parentElement;return null==e?t.getRootNode().host||null:e};exports.compute=(e,r)=>{var i,s,d,c;if("undefined"==typeof document)return[];const{scrollMode:h,block:u,inline:f,boundary:a,skipOverflowHiddenElements:g}=r,p="function"==typeof a?a:t=>t!==a;if(!t(e))throw new TypeError("Invalid target");const m=document.scrollingElement||document.documentElement,w=[];let W=e;for(;t(W)&&p(W);){if(W=l(W),W===m){w.push(W);break}null!=W&&W===document.body&&o(W)&&!o(document.documentElement)||null!=W&&o(W,g)&&w.push(W)}const b=null!=(s=null==(i=window.visualViewport)?void 0:i.width)?s:innerWidth,H=null!=(c=null==(d=window.visualViewport)?void 0:d.height)?c:innerHeight,{scrollX:y,scrollY:M}=window,{height:v,width:E,top:x,right:C,bottom:I,left:R}=e.getBoundingClientRect(),{top:T,right:B,bottom:F,left:V}=(t=>{const e=window.getComputedStyle(t);return{top:parseFloat(e.scrollMarginTop)||0,right:parseFloat(e.scrollMarginRight)||0,bottom:parseFloat(e.scrollMarginBottom)||0,left:parseFloat(e.scrollMarginLeft)||0}})(e);let k="start"===u||"nearest"===u?x-T:"end"===u?I+F:x+v/2-T+F,D="center"===f?R+E/2-V+B:"end"===f?C+B:R-V;const L=[];for(let t=0;t<w.length;t++){const e=w[t],{height:l,width:r,top:i,right:s,bottom:d,left:c}=e.getBoundingClientRect();if("if-needed"===h&&x>=0&&R>=0&&I<=H&&C<=b&&(e===m&&!o(e)||x>=i&&I<=d&&R>=c&&C<=s))return L;const a=getComputedStyle(e),g=parseInt(a.borderLeftWidth,10),p=parseInt(a.borderTopWidth,10),W=parseInt(a.borderRightWidth,10),T=parseInt(a.borderBottomWidth,10);let B=0,F=0;const V="offsetWidth"in e?e.offsetWidth-e.clientWidth-g-W:0,S="offsetHeight"in e?e.offsetHeight-e.clientHeight-p-T:0,j="offsetWidth"in e?0===e.offsetWidth?0:r/e.offsetWidth:0,O="offsetHeight"in e?0===e.offsetHeight?0:l/e.offsetHeight:0;if(m===e)B="start"===u?k:"end"===u?k-H:"nearest"===u?n(M,M+H,H,p,T,M+k,M+k+v,v):k-H/2,F="start"===f?D:"center"===f?D-b/2:"end"===f?D-b:n(y,y+b,b,g,W,y+D,y+D+E,E),B=Math.max(0,B+M),F=Math.max(0,F+y);else{B="start"===u?k-i-p:"end"===u?k-d+T+S:"nearest"===u?n(i,d,l,p,T+S,k,k+v,v):k-(i+l/2)+S/2,F="start"===f?D-c-g:"center"===f?D-(c+r/2)+V/2:"end"===f?D-s+W+V:n(c,s,r,g,W+V,D,D+E,E);const{scrollLeft:t,scrollTop:o}=e;B=0===O?0:Math.max(0,Math.min(o+B/O,e.scrollHeight-l/O+S)),F=0===j?0:Math.max(0,Math.min(t+F/j,e.scrollWidth-r/j+V)),k+=o-B,D+=t-F}L.push({el:e,top:B,left:F})}return L};//# sourceMappingURL=index.cjs.map
