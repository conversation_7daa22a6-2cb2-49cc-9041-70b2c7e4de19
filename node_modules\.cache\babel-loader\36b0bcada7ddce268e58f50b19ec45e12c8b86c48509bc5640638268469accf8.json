{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/addons/utils/SkeletonUtils.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel = null; // 存储原始模型数据\nlet skeleton = null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n  bsm: 'changli/cloud/v2x/obu/bsm',\n  rsm: 'changli/cloud/v2x/rsu/rsm',\n  scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',\n  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat' // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (!lastPosition) {\n      lastPosition = newPos.clone();\n      return newPos;\n    }\n    const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n    const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n    const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n    lastPosition.set(filteredX, filteredY, filteredZ);\n    return lastPosition.clone();\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  const lastPos = vehicleLastPositions.get(vehicleId);\n\n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n\n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n\n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (lastRotation === null) {\n      lastRotation = newRotation;\n      return newRotation;\n    }\n\n    // 处理角度跳变（从360度到0度或反之）\n    let diff = newRotation - lastRotation;\n    if (diff > Math.PI) diff -= 2 * Math.PI;\n    if (diff < -Math.PI) diff += 2 * Math.PI;\n    const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n    lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const lastRot = vehicleLastRotations.get(vehicleId);\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n\n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\nconst CampusModel = ({\n  className,\n  onCurrentRSUChange,\n  selectedRSUs\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  const sceneRef = useRef(null);\n  const cameraRef = useRef(null);\n  const controlsRef = useRef(null);\n  const rendererRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const clock = useRef(new THREE.Clock()); // 添加时钟引用\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const vehicleRef = useRef(null);\n\n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n\n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = useRef(new Map()); // 存储所有行人的动画混合器\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,\n    // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null,\n    phases: []\n  });\n\n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n\n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n\n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n\n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({\n    topic: '',\n    content: ''\n  });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n\n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n\n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n\n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos).to({\n          x: 0,\n          y: 300,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        }).start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp).to({\n          x: 0,\n          y: 1,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        }).start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n\n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget).to({\n          x: 0,\n          y: 0,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        }).start();\n\n        // 启用控制器\n        controls.enabled = true;\n\n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x + 50, 70, -modelCoords.y + 50);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        var _payload$data;\n        // console.log('收到RSM消息:', payload);\n\n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n\n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n          return;\n        }\n\n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n\n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n\n        const participants = ((_payload$data = payload.data) === null || _payload$data === void 0 ? void 0 : _payload$data.participants) || [];\n        const rsuid = payload.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id = deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = preloadedModel.clone();\n              // 根据类型调整高度和缩放\n              const height = type === '3' ? 2.0 : 1.0;\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n\n              // 如果是行人类型，设置缩放和创建动画\n              if (type === '3') {\n                // 使用预加载的行人模型\n                const peopleModel = preloadedPeopleModel.clone();\n                peopleModel.position.set(modelPos.x, 2.0, -modelPos.y);\n                peopleModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n                peopleModel.scale.set(0.01, 0.01, 0.01); // 设置正确的缩放\n                scene.add(peopleModel);\n\n                // 创建动画混合器\n                const mixer = new THREE.AnimationMixer(peopleModel);\n                // 播放行走动画\n                if (peopleBaseModel && peopleBaseModel.animations && peopleBaseModel.animations.length > 0) {\n                  const walkAnimation = peopleBaseModel.animations.find(anim => anim.name === 'Walk');\n                  if (walkAnimation) {\n                    const action = mixer.clipAction(walkAnimation);\n                    action.play();\n                  }\n                }\n                // 保存动画混合器\n                peopleAnimationMixers.current.set(id, mixer);\n              }\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n\n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n\n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n\n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n\n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid,\n          // 直接传递车辆ID\n          data: {\n            // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n\n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n\n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n\n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n\n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n\n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n\n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n\n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n\n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n\n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n\n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          scene.add(newVehicleModel);\n\n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1',\n            // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n\n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position).to({\n            y: 0.5\n          }, 500).easing(TWEEN.Easing.Quadratic.Out).start();\n\n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({\n                opacity: 0.6\n              }).to({\n                opacity: 1.0\n              }, 500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function () {\n                child.material.opacity = this.opacity;\n                child.material.needsUpdate = true;\n              }).start();\n            }\n          });\n\n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({\n            opacity: 0.6\n          }).to({\n            opacity: 1.0\n          }, 500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function () {\n            speedLabel.material.opacity = this.opacity;\n            speedLabel.material.needsUpdate = true;\n          }).start();\n\n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n\n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n\n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n\n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n\n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n\n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 2000; // 增加到10秒，避免频繁清理造成闪烁\n\n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n\n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1;\n            modelData.model.traverse(child => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n\n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n\n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            modelData.model.traverse(child => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n\n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n\n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n\n        try {\n          const payload = JSON.parse(message);\n\n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n\n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n\n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n\n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n\n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n\n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? getDirectionFromCode(phase.trafficDirec) : getPhaseDirection(phaseId);\n\n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n\n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n\n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n\n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n\n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n\n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n\n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n\n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && (window.currentPopoverIdRef.current === modelKey || window.currentPopoverIdRef.current === String(modelKey) || window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n\n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n      }\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(parseFloat(rsiData.posLong), parseFloat(rsiData.posLat));\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          switch (eventType) {\n            case '401':\n              // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':\n              // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':\n              // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':\n              // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':\n              // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002':\n              // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':\n              // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n\n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n\n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch (sceneType) {\n          case '2':\n            // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':\n            // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':\n            // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':\n            // 限速提醒\n            const speedLimit = sceneData.eventData1; // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':\n            // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':\n            // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':\n            // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':\n            // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':\n            // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':\n            // 信号灯优先\n            const priorityType = sceneData.eventData1; // 优先类型\n            const duration = sceneData.eventData2; // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n\n        // 检查scene是否初始化\n        if (scene) {\n          scene.add(model);\n\n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } else {\n          console.error('无法添加模型：场景未初始化');\n        }\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画\n      const deltaTime = clock.current.getDelta(); // 使用clock获取时间增量\n\n      peopleAnimationMixers.current.forEach(mixer => {\n        mixer.update(deltaTime);\n      });\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 200, -50 * Math.sin(adjustedRotation));\n\n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n\n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n\n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n\n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 路口视角模式\n        controls.update();\n      }\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n\n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n\n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach(lightObj => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n\n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n\n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n\n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n\n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n\n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n\n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {\n          // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n\n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = event => {\n        if (scene && cameraRef.current) {\n          console.log('检测到点击事件', event.clientX, event.clientY);\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        } else {\n          console.warn('点击事件处理失败: scene或camera未初始化');\n        }\n      };\n\n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n\n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n\n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({\n      color: 0x333333\n    });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n\n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({\n      color: 0x666666\n    });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n\n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          //\n          transparent: false,\n          opacity: 0.1,\n          // 几乎透明\n          depthWrite: false\n        });\n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0); // 放在红绿灯位置\n\n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n\n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500); // 延迟略长于debugTrafficLights\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n\n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n\n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        const firstIntersection = intersectionsData.intersections[0];\n        console.log('自动选择第一个路口:', firstIntersection.name);\n\n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(firstIntersection.name);\n        }, 2000);\n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u533A\\u57DF\\u9009\\u62E9\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1894,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u533A\\u57DF\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      options: intersectionsData.intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      },\n      value: selectedIntersection ? selectedIntersection.name : undefined\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1895,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1911,\n      columnNumber: 7\n    }, this), trafficLightPopover.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${trafficLightPopover.position.x}px`,\n        top: `${trafficLightPopover.position.y}px`,\n        transform: 'translate(-50%, -100%)',\n        zIndex: 1003,\n        backgroundColor: 'rgba(0, 0, 0, 0.85)',\n        color: 'white',\n        borderRadius: '4px',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n        padding: '0',\n        maxWidth: '240px',\n        // 缩小最大宽度\n        fontSize: '12px' // 缩小字体\n      },\n      children: [trafficLightPopover.content, /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          position: 'absolute',\n          top: '0px',\n          right: '0px',\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          fontSize: '12px',\n          cursor: 'pointer',\n          padding: '2px 6px'\n        },\n        onClick: () => handleClosePopover(setTrafficLightPopover),\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1932,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1915,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1952,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1962,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1951,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"yb8MNvbtB8dVi35Iyt1gqFaR5wY=\");\n_c = CampusModel;\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 16,\n    // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    backgroundColor: parameters.backgroundColor || {\n      r: 255,\n      g: 255,\n      b: 255,\n      a: 0.8\n    },\n    textColor: parameters.textColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n\n  // 设置字体\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n\n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n\n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  canvas.width = width;\n  canvas.height = height;\n\n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n\n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n\n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n\n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n\n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n\n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n\n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n\n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n\n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = {\n    text: text,\n    params: params\n  };\n  return sprite;\n}\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    try {\n      const [trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`), loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`)]);\n\n      // 处理机动车模型\n      console.log('加载车辆模型...');\n      preloadedVehicleModel = vehicleGltf.scene;\n      preloadedVehicleModel.traverse(child => {\n        if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n            color: 0xff0000,\n            // 0xff0000, //红色 0xffffff,白色\n            metalness: 0.2,\n            roughness: 0.1,\n            envMapIntensity: 1.0\n          });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n        }\n      });\n      console.log('加载非机动车模型...');\n      // 处理非机动车模型\n      preloadedCyclistModel = cyclistGltf.scene;\n      // 设置非机动车模型的缩放\n      preloadedCyclistModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedCyclistModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n      console.log('加载行人模型...');\n      // 处理行人模型\n      preloadedPeopleModel = peopleGltf.scene;\n      // 设置行人模型的缩放\n      // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n      // 保持原始材质\n      preloadedPeopleModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n        if (child.isMesh) {\n          child.castShadow = true;\n        }\n      });\n\n      // 保存行人动画数据\n      console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n      if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n        console.log('找到行人动画:', peopleGltf.animations.length, '个');\n        peopleBaseModel = peopleGltf;\n      } else {\n        console.warn('行人模型没有包含动画数据');\n      }\n      console.log('加载红绿灯模型...');\n\n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      console.log('红绿灯模型：', preloadedTrafficLightModel);\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n\n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n\n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = type => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  try {\n    // 创建一个新的警告标记\n    const sprite = createTextSprite(text);\n    sprite.position.set(position.x, 10, -position.y); // 设置位置，稍微升高以便可见\n\n    // 为标记添加一个定时器，在1秒后自动移除\n    setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n        scene.remove(sprite);\n      }\n    }, 100);\n\n    // 将标记添加到场景中\n    scene.add(sprite);\n\n    // console.log('添加场景事件标记:', {\n    //   位置: position,\n    //   文本: text,\n    //   颜色: color\n    // });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = converterInstance => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n\n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach(lightObj => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型 - 只为hasTrafficLight为true的路口创建红绿灯\n  intersectionsData.intersections.forEach(intersection => {\n    // 检查路口是否有红绿灯属性，如果没有或为false，则跳过\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n\n    // 确认路口有经纬度和ID\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n\n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n\n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n\n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n\n        // *** 重要：设置为可被交互 ***\n        trafficLightModel.traverse(child => {\n          // 设置所有子对象可被点击\n          if (child.isMesh) {\n            // 确保材质能够被射线检测到\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n\n            // 设置较高的渲染顺序\n            child.renderOrder = 100;\n          }\n        });\n\n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n\n        // // 添加一个专门用于点击的大型碰撞体\n        // const colliderGeometry = new THREE.SphereGeometry(5, 12, 12);\n        // const colliderMaterial = new THREE.MeshBasicMaterial({\n        //   color: 0xff00ff,\n        //   transparent: true,\n        //   opacity: 0.0,  // 完全透明\n        //   depthWrite: false\n        // });\n\n        // const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n        // collider.name = `交通灯碰撞体-${intersection.name}`;\n        // collider.userData = {\n        //   type: 'trafficLight',\n        //   interId: intersection.interId,\n        //   name: intersection.name,\n        //   isCollider: true\n        // };\n\n        // trafficLightModel.add(collider);\n\n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n\n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = converterInstance => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({\n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n\n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n\n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n\n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,\n    // 完全透明\n    depthWrite: false\n  });\n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  trafficLightModel.add(collider);\n\n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n\n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n\n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: 0xFF0000\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n\n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  trafficLightModel.add(lightMesh);\n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = phaseId => {\n  switch (phaseId) {\n    case '1':\n      return '北进口左转';\n    case '2':\n      return '北进口直行';\n    case '3':\n      return '北进口右转';\n    case '5':\n      return '东进口左转';\n    case '6':\n      return '东进口直行';\n    case '7':\n      return '东进口右转';\n    case '9':\n      return '南进口左转';\n    case '10':\n      return '南进口直行';\n    case '11':\n      return '南进口右转';\n    case '13':\n      return '西进口左转';\n    case '14':\n      return '西进口直行';\n    case '15':\n      return '西进口右转';\n    default:\n      return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = dirCode => {\n  switch (dirCode) {\n    case 'N':\n      return '北向南';\n    case 'S':\n      return '南向北';\n    case 'E':\n      return '东向西';\n    case 'W':\n      return '西向东';\n    case 'NE':\n      return '东北向西南';\n    case 'NW':\n      return '西北向东南';\n    case 'SE':\n      return '东南向西北';\n    case 'SW':\n      return '西南向东北';\n    default:\n      return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n\n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = (event.clientX - rect.left) / container.clientWidth * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n\n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Line.threshold = 1;\n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  console.log('鼠标点击位置:', mouseX, mouseY);\n\n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n\n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'userData:', intersect.object.userData);\n    });\n\n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n\n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n\n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n\n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  console.log('射线检测到的对象数量:', intersects.length);\n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', 'userData:', obj.userData, '距离:', intersect.distance);\n    });\n\n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        console.log('检测到红绿灯点击, 路口ID:', interId);\n\n        // 显示弹窗\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n\n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n\n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.1; // 设置一个阈值，只有距离小于此值的才会被选中\n\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n\n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n\n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      console.log(`路口 ${interId} 的距离:`, distance);\n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = {\n          interId,\n          distance\n        };\n      }\n    }\n  });\n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n\n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  console.log('未检测到任何红绿灯点击');\n\n  // 如果用户点击了其他任何地方，关闭现有的弹窗\n  if (setPopoverState) {\n    setPopoverState(prev => {\n      if (prev.visible) {\n        return {\n          ...prev,\n          visible: false\n        };\n      }\n      return prev;\n    });\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = setPopoverState => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n\n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n\n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({\n    ...prev,\n    visible: false\n  }));\n  console.log('弹窗已关闭');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = interId => {\n  try {\n    var _document$querySelect, _document$querySelect2;\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n\n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      return false;\n    }\n\n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n\n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 创建弹出窗口内容\n    let content;\n    if (stateInfo && stateInfo.phases) {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '220px',\n          maxHeight: '300px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          },\n          children: [intersection.name, \" (ID: \", interId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2747,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                break;\n            }\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '6px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '4px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: getPhaseDirection(phase.phaseId)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2773,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2777,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 3px',\n                    borderRadius: '2px'\n                  },\n                  children: phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2778,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2776,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2789,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2790,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2788,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2766,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2756,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '6px',\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2796,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2746,\n        columnNumber: 9\n      }, this);\n    } else {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          maxWidth: '200px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '8px'\n          },\n          children: intersection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2804,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u8DEF\\u53E3ID: \", interId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2805,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2806,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2803,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2;\n    const centerY = window.innerHeight / 2;\n\n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = (_document$querySelect = document.querySelector('#root')) === null || _document$querySelect === void 0 ? void 0 : (_document$querySelect2 = _document$querySelect.__REACT_INSTANCE) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.setTrafficLightPopover;\n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: {\n          x: centerX,\n          y: centerY\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      document.body.appendChild(popover);\n\n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  return list;\n};\n\n// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n\n//   console.log('开始调试红绿灯模型...');\n\n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n\n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n\n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n\n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n\n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n\n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n\n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n\n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n\n//       // 添加到场景\n//       scene.add(highlightMesh);\n\n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n\n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n\n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n\n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = interId => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    console.log('当前trafficLightStates大小:', trafficLightStates.size);\n\n    // 如果没有指定ID，则使用第一个红绿灯\n    if (!interId && trafficLightsMap.size > 0) {\n      interId = String(Array.from(trafficLightsMap.keys())[0]);\n      console.log('未指定ID，使用第一个红绿灯ID:', interId);\n    }\n\n    // 输出所有可用的红绿灯ID用于调试\n    console.log('所有可用的红绿灯ID:');\n    trafficLightsMap.forEach((light, id) => {\n      var _light$intersection;\n      console.log(`- ${id} (${typeof id}): ${((_light$intersection = light.intersection) === null || _light$intersection === void 0 ? void 0 : _light$intersection.name) || '未知'}`);\n    });\n\n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n\n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n\n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n\n    // 创建更新函数\n    const updateTrafficLightPopover = () => {\n      var _window$currentPopove;\n      // 确保当前弹窗仍然可见\n      if (!window._setTrafficLightPopover) return;\n      const currentId = (_window$currentPopove = window.currentPopoverIdRef) === null || _window$currentPopove === void 0 ? void 0 : _window$currentPopove.current;\n      if (!currentId) return;\n\n      // 重新获取最新的状态信息\n      let stateInfo = trafficLightStates.get(currentId);\n      if (!stateInfo && typeof currentId === 'string') {\n        // 尝试使用数字ID\n        stateInfo = trafficLightStates.get(parseInt(currentId));\n      } else if (!stateInfo && typeof currentId === 'number') {\n        // 尝试使用字符串ID\n        stateInfo = trafficLightStates.get(String(currentId));\n      }\n      if (!stateInfo || !stateInfo.phases || stateInfo.phases.length === 0) {\n        console.log(`路口ID ${currentId} 没有状态信息，跳过更新`);\n        return;\n      }\n\n      // 获取交通灯信息\n      const intersectionLight = trafficLightsMap.get(currentId) || (typeof currentId === 'string' ? trafficLightsMap.get(parseInt(currentId)) : trafficLightsMap.get(String(currentId)));\n      if (!intersectionLight) {\n        console.error(`找不到路口ID ${currentId} 的红绿灯信息，无法更新弹窗`);\n        return;\n      }\n      const intersection = intersectionLight.intersection;\n\n      // 创建更新的弹窗内容\n      const content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '220px',\n          maxHeight: '300px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          },\n          children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", currentId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3075,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            let lightText;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                lightText = '绿灯';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                lightText = '黄灯';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                lightText = '红灯';\n                break;\n            }\n            const direction = getPhaseDirection(phase.phaseId);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '6px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '4px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: [direction, \" (\\u76F8\\u4F4DID: \", phase.phaseId, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3115,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3119,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 3px',\n                    borderRadius: '2px'\n                  },\n                  children: lightText\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3120,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3118,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3131,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3132,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3130,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3108,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3084,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '6px',\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date(stateInfo.updateTime).toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3138,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3074,\n        columnNumber: 9\n      }, this);\n\n      // 更新弹窗内容\n      window._setTrafficLightPopover(prev => ({\n        ...prev,\n        content: content,\n        phases: stateInfo.phases\n      }));\n      console.log(`已更新路口 ${(intersection === null || intersection === void 0 ? void 0 : intersection.name) || currentId} 的红绿灯状态弹窗`);\n    };\n\n    // 先执行一次初始更新\n    const updateInitialTrafficLightPopover = () => {\n      // 同样，查找红绿灯状态信息时也尝试字符串和数字两种类型\n      let stateInfo = trafficLightStates.get(interId);\n      if (!stateInfo) {\n        // 尝试使用数字ID\n        const numericId = parseInt(interId);\n        stateInfo = trafficLightStates.get(numericId);\n        if (stateInfo) {\n          console.log(`使用数字ID ${numericId} 找到了红绿灯状态信息`);\n        }\n      }\n      console.log(`路口ID ${interId} 的状态信息:`, stateInfo);\n      const intersection = trafficLight.intersection;\n      console.log('路口信息:', intersection);\n\n      // 创建弹窗内容\n      let content;\n      if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n        // 添加一个检查相位数据的打印\n        console.log('相位数据详情:');\n        stateInfo.phases.forEach((phase, index) => {\n          console.log(`相位 ${index + 1}: ID=${phase.phaseId}, 状态=${phase.trafficLight}, 剩余时间=${phase.remainTime}`);\n        });\n        content = /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '8px',\n            width: '220px',\n            maxHeight: '300px',\n            overflowY: 'auto'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            },\n            children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", interId, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              let lightText;\n              switch (phase.trafficLight) {\n                case 'G':\n                  lightColor = '#00ff00';\n                  lightText = '绿灯';\n                  break;\n                case 'Y':\n                  lightColor = '#ffff00';\n                  lightText = '黄灯';\n                  break;\n                case 'R':\n                default:\n                  lightColor = '#ff0000';\n                  lightText = '红灯';\n                  break;\n              }\n              const direction = getPhaseDirection(phase.phaseId);\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '6px',\n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [direction, \" (\\u76F8\\u4F4DID: \", phase.phaseId, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3225,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u706F\\u8272: \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3229,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: lightColor,\n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    },\n                    children: lightText\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3230,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3228,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u5012\\u8BA1\\u65F6: \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3241,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontWeight: 'bold'\n                    },\n                    children: [phase.remainTime, \" \\u79D2\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3242,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3240,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3218,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '6px',\n              fontSize: '10px',\n              color: '#888'\n            },\n            children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date(stateInfo.updateTime).toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3184,\n          columnNumber: 11\n        }, this);\n      } else {\n        // 如果没有状态信息，创建一个示例内容\n        content = /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '8px',\n            width: '220px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            },\n            children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", interId, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#ff4d4f',\n              fontSize: '12px'\n            },\n            children: \"\\u8BE5\\u8DEF\\u53E3\\u6682\\u65E0\\u7EA2\\u7EFF\\u706F\\u72B6\\u6001\\u4FE1\\u606F\\uFF0C\\u8BF7\\u7B49\\u5F85SPAT\\u6D88\\u606F\\u66F4\\u65B0\\u3002\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3266,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '6px',\n              fontSize: '10px',\n              color: '#888'\n            },\n            children: [\"\\u5F53\\u524D\\u65F6\\u95F4: \", new Date().toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3269,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3256,\n          columnNumber: 11\n        }, this);\n      }\n\n      // 设置弹窗位置在屏幕中央\n      const x = window.innerWidth / 2;\n      const y = window.innerHeight / 2;\n\n      // 更新弹窗状态\n      if (window._setTrafficLightPopover) {\n        var _stateInfo;\n        console.log('调用_setTrafficLightPopover更新弹窗状态', {\n          visible: true,\n          interId,\n          position: {\n            x,\n            y\n          }\n        });\n        window._setTrafficLightPopover({\n          visible: true,\n          interId: interId,\n          position: {\n            x,\n            y\n          },\n          content: content,\n          phases: ((_stateInfo = stateInfo) === null || _stateInfo === void 0 ? void 0 : _stateInfo.phases) || []\n        });\n        console.log(`已显示路口 ${(intersection === null || intersection === void 0 ? void 0 : intersection.name) || interId} 的红绿灯状态弹窗`);\n\n        // 设置定时更新\n        if (window.trafficLightUpdateTimerRef) {\n          window.trafficLightUpdateTimerRef.current = setInterval(updateTrafficLightPopover, TRAFFIC_LIGHT_UPDATE_INTERVAL * 1000);\n          console.log(`已设置红绿灯状态弹窗定时更新，间隔 ${TRAFFIC_LIGHT_UPDATE_INTERVAL} 秒`);\n        }\n        return true;\n      } else {\n        console.error('无法找到setTrafficLightPopover函数');\n        return false;\n      }\n    };\n    return updateInitialTrafficLightPopover();\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n\n//   console.log('开始生成模拟SPAT数据...');\n\n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n\n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n\n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n\n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n\n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n\n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n\n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n\n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n\n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n\n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n\n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n\n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n\n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n\n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n\n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n\n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = object => {\n  let current = object;\n\n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n\n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n\n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n\n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n\n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n\n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = (x - rect.left) / canvas.clientWidth * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    console.log('归一化坐标:', mouseX, mouseY);\n\n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n\n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n\n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'position:', intersect.object.position.toArray(), 'userData:', intersect.object.userData);\n\n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      return true;\n    }\n\n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', '类型:', obj.type, '位置:', obj.position.toArray(), '距离:', intersect.distance, 'userData:', obj.userData);\n    });\n\n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        var _lightObj$intersectio;\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n\n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n\n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n\n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        if (isVisible) {\n          visibleCount++;\n        }\n        console.log(`红绿灯 ${interId}:`, {\n          名称: ((_lightObj$intersectio = lightObj.intersection) === null || _lightObj$intersectio === void 0 ? void 0 : _lightObj$intersectio.name) || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n\n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  var _trafficLight$interse, _trafficLight$interse2, _trafficLight$interse3;\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch (phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n\n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: (_trafficLight$interse = trafficLight.intersection) === null || _trafficLight$interse === void 0 ? void 0 : _trafficLight$interse.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n\n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = {\n    isLight: true\n  };\n\n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  console.log(`更新路口 ${((_trafficLight$interse2 = trafficLight.intersection) === null || _trafficLight$interse2 === void 0 ? void 0 : _trafficLight$interse2.name) || ((_trafficLight$interse3 = trafficLight.intersection) === null || _trafficLight$interse3 === void 0 ? void 0 : _trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "SkeletonUtils", "mqtt", "Select", "Popover", "intersectionsData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "peopleBaseModel", "skeleton", "lastPosition", "lastRotation", "ALPHA", "vehicleLastPositions", "Map", "vehicleLastRotations", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "process", "env", "REACT_APP_API_URL", "console", "log", "vehicleModels", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "clock", "Clock", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "vehicleId", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "has", "lastPos", "get", "distance", "distanceTo", "MAX_DISTANCE_THRESHOLD", "toFixed", "filteredPos", "Vector3", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "lastRot", "MAX_ANGLE_THRESHOLD", "abs", "TRAFFIC_LIGHT_UPDATE_INTERVAL", "CampusModel", "className", "onCurrentRSUChange", "selectedRSUs", "_s", "containerRef", "sceneRef", "cameraRef", "controlsRef", "rendererRef", "animationFrameRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "vehicleRef", "lastCameraPosition", "lastCameraTarget", "cameraSmoothing", "prevAnimationTimeRef", "Date", "now", "peopleAnimationMixers", "peopleAnimations", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "currentPopoverIdRef", "trafficLightUpdateTimerRef", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "switchToFollowView", "current", "enabled", "switchToGlobalView", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "target", "currentTarget", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "updateMatrix", "updateMatrixWorld", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "相机位置", "toArray", "目标点", "handleMqttMessage", "message", "payload", "JSON", "parse", "_payload$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "participants", "rsuid", "for<PERSON>ach", "participant", "id", "partPtcId", "type", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "model", "newModel", "height", "rotation", "peopleModel", "scale", "add", "mixer", "AnimationMixer", "animations", "length", "walkAnimation", "anim", "action", "clipAction", "play", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "Set", "map", "p", "modelData", "remove", "delete", "bsmData", "bsmid", "newState", "partLong", "partLat", "postMessage", "source", "initialPosition", "initialRotation", "newPosition", "vehicleObj", "newVehicleModel", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "emissive", "Color", "transparent", "needsUpdate", "speedLabel", "createTextSprite", "round", "r", "g", "b", "a", "textColor", "renderOrder", "opacity", "is<PERSON><PERSON>", "Out", "filteredPosition", "dispose", "timeSinceLastUpdate", "undefined", "originalTran<PERSON>arent", "originalOpacity", "m", "geometry", "phasesInfo", "phase", "phaseId", "toString", "direction", "trafficDirec", "getDirectionFromCode", "getPhaseDirection", "lightState", "trafficLight", "remainTime", "parseInt", "phaseInfo", "push", "trafficLightKey", "String", "trafficLightModel", "updateTrafficLightVisual", "prev", "<PERSON><PERSON><PERSON>", "strId", "numId", "updateTime", "setTimeout", "showTrafficLightPopup", "rsiData", "rsuId", "events", "rtes", "event", "eventId", "rteId", "eventType", "description", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "stringify", "onerror", "onclose", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "setIsVehicleLoaded", "xhr", "loaded", "total", "initializeScene", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "deltaTime", "<PERSON><PERSON><PERSON><PERSON>", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "targetCameraPosition", "targetLookAt", "lerp", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "clearInterval", "close", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "clear", "lightObj", "handleMainVehicleChange", "intervalId", "setInterval", "timer", "createTrafficLights", "clearTimeout", "handleClick", "clientX", "clientY", "handleMouseClick", "warn", "initScene", "createSimpleTrafficLight", "BoxGeometry", "MeshBasicMaterial", "<PERSON><PERSON>", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "addClickHelpers", "helperGeometry", "SphereGeometry", "helperMaterial", "depthWrite", "helper<PERSON><PERSON>", "userData", "isClickHelper", "size", "firstIntersection", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "onChange", "options", "label", "bordered", "dropdownStyle", "maxHeight", "ref", "max<PERSON><PERSON><PERSON>", "right", "background", "onClick", "handleClosePopover", "_c", "text", "parameters", "params", "fontFace", "borderThickness", "borderColor", "canvas", "document", "createElement", "context", "getContext", "font", "textWidth", "measureText", "textBaseline", "radius", "beginPath", "moveTo", "lineTo", "arcTo", "closePath", "strokeStyle", "lineWidth", "stroke", "fillStyle", "fill", "textAlign", "fillText", "texture", "CanvasTexture", "minFilter", "LinearFilter", "spriteMaterial", "SpriteMaterial", "sprite", "Sprite", "depthTest", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "trafficLightGltf", "vehicleGltf", "cyclistGltf", "peopleGltf", "all", "loadAsync", "materia", "<PERSON><PERSON><PERSON><PERSON>", "err", "types", "parent", "converterInstance", "createFallbackTrafficLights", "hasTrafficLight", "side", "DoubleSide", "colliderGeometry", "colliderMaterial", "collider", "isCollider", "lightGeometry", "lightMaterial", "<PERSON><PERSON><PERSON>", "dirCode", "container", "sceneInstance", "cameraInstance", "setPopoverState", "rect", "getBoundingClientRect", "mouseX", "clientWidth", "mouseY", "clientHeight", "raycaster", "Raycaster", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "trafficLightObjects", "trafficLightIntersects", "intersectObjects", "intersect", "index", "object", "obj", "getTrafficLightFromObject", "correctId", "intersects", "closestLight", "worldPos", "getWorldPosition", "screenPos", "project", "dx", "dy", "sqrt", "testTrafficLightClick", "_document$querySelect", "_document$querySelect2", "light", "lightModel", "stateInfo", "overflowY", "marginBottom", "borderBottom", "paddingBottom", "lightColor", "justifyContent", "marginTop", "toLocaleTimeString", "centerX", "centerY", "__REACT_INSTANCE", "popover", "innerHTML", "body", "closeButton", "listTrafficLights", "list", "from", "keys", "_light$intersection", "numericId", "updateTrafficLightPopover", "_window$currentPopove", "currentId", "intersectionLight", "lightText", "updateInitialTrafficLightPopover", "_stateInfo", "testClickDetection", "tlIntersects", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "distanceToCamera", "名称", "可见性", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "isLight", "emissiveIntensity", "PointLight", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/addons/utils/SkeletonUtils.js' \nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel= null; // 存储原始模型数据\nlet skeleton =null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  const lastPos = vehicleLastPositions.get(vehicleId);\n  \n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n  \n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  \n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  \n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const lastRot = vehicleLastRotations.get(vehicleId);\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  \n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const sceneRef = useRef(null);\n  const cameraRef = useRef(null);\n  const controlsRef = useRef(null);\n  const rendererRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const clock = useRef(new THREE.Clock()); // 添加时钟引用\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const vehicleRef = useRef(null);\n  \n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n  \n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = useRef(new Map()); // 存储所有行人的动画混合器\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n  \n  \n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n  \n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n  \n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n  \n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n  \n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n      \n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n      \n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n        \n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos)\n          .to({ x: 0, y: 300, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.position.copy(currentPos);\n          })\n          .start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp)\n          .to({ x: 0, y: 1, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.up.copy(currentUp);\n          })\n          .start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n        \n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget)\n          .to({ x: 0, y: 0, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            controls.target.copy(currentTarget);\n            // 确保相机始终朝向目标点\n            cameraRef.current.lookAt(controls.target);\n            controls.update();\n          })\n          .start();\n\n        // 启用控制器\n        controls.enabled = true;\n        \n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x+50, 70, -modelCoords.y+50);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        // console.log('收到RSM消息:', payload);\n        \n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n        \n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n        \n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n      return;\n    }\n    \n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        \n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n        \n        const participants = payload.data?.participants || [];\n        const rsuid = payload.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id =  deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n          // 解析位置和状态信息\n          const state = {\n            longitude: parseFloat(participant.partPosLong),\n            latitude: parseFloat(participant.partPosLat),\n            speed: parseFloat(participant.partSpeed),\n            heading: parseFloat(participant.partHeading)\n          };\n          \n          const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n          \n          // 根据类型选择对应的预加载模型\n          let preloadedModel;\n          switch (type) {\n            case '1': // 机动车\n              preloadedModel = preloadedVehicleModel;\n              break;\n            case '2': // 非机动车\n              preloadedModel = preloadedCyclistModel;\n              break;\n            case '3': // 行人\n              preloadedModel = preloadedPeopleModel;\n              break;\n            default:\n              return; // 跳过未知类型\n          }\n          \n          // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n            // 创建新模型实例\n            const newModel = preloadedModel.clone();\n            // 根据类型调整高度和缩放\n            const height = type === '3' ? 2.0 : 1.0;\n            newModel.position.set(modelPos.x, height, -modelPos.y);\n            newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n\n            // 如果是行人类型，设置缩放和创建动画\n            if (type === '3') {\n              // 使用预加载的行人模型\n              const peopleModel = preloadedPeopleModel.clone();\n              peopleModel.position.set(modelPos.x, 2.0, -modelPos.y);\n              peopleModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              peopleModel.scale.set(0.01, 0.01, 0.01); // 设置正确的缩放\n              scene.add(peopleModel);\n\n              // 创建动画混合器\n              const mixer = new THREE.AnimationMixer(peopleModel);\n              // 播放行走动画\n              if (peopleBaseModel && peopleBaseModel.animations && peopleBaseModel.animations.length > 0) {\n                const walkAnimation = peopleBaseModel.animations.find(anim => anim.name === 'Walk');\n                if (walkAnimation) {\n                  const action = mixer.clipAction(walkAnimation);\n                  action.play();\n                }\n              }\n              // 保存动画混合器\n              peopleAnimationMixers.current.set(id, mixer);\n            }\n\n            scene.add(newModel);\n            \n            vehicleModels.set(id, {\n              model: newModel,\n              lastUpdate: now,\n              type: type\n            });\n          } else if (model) {\n            // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n          }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n        \n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n        \n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n        \n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid, // 直接传递车辆ID\n          data: {       // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n        \n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n        \n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n        \n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n        \n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        \n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          \n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n          \n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n              \n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n              \n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n          \n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n          \n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          \n          scene.add(newVehicleModel);\n          \n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n          \n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position)\n            .to({ y: 0.5 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .start();\n          \n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({ opacity: 0.6 })\n                .to({ opacity: 1.0 }, 500)\n                .easing(TWEEN.Easing.Quadratic.Out)\n                .onUpdate(function() {\n                  child.material.opacity = this.opacity;\n                  child.material.needsUpdate = true;\n                })\n                .start();\n            }\n          });\n          \n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({ opacity: 0.6 })\n            .to({ opacity: 1.0 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .onUpdate(function() {\n              speedLabel.material.opacity = this.opacity;\n              speedLabel.material.needsUpdate = true;\n            })\n            .start();\n          \n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n          \n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n          \n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n          \n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n          \n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n        \n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 2000; // 增加到10秒，避免频繁清理造成闪烁\n        \n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n          \n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1 ;\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n                \n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n            \n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n            \n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            \n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        \n        return;\n      }\n      \n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n        \n        try {\n          const payload = JSON.parse(message);\n          \n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n          \n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n          \n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n          \n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n          \n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              \n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              \n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n              \n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                \n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  \n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? \n                    getDirectionFromCode(phase.trafficDirec) : \n                    getPhaseDirection(phaseId);\n                  \n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  \n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n                  \n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n                  \n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n                  \n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  \n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  \n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n                    \n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n                \n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                \n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n                  \n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && \n                     (window.currentPopoverIdRef.current === modelKey || \n                      window.currentPopoverIdRef.current === String(modelKey) || \n                      window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    \n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n                    \n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n      }\n      \n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        \n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n        \n        return;\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n        \n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n        \n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          \n          // 检查scene是否初始化\n          if (scene) {\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画\n      const deltaTime = clock.current.getDelta(); // 使用clock获取时间增量\n\n      peopleAnimationMixers.current.forEach((mixer) => {\n        mixer.update(deltaTime);\n      });\n\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          200,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n        \n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        \n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n        \n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n        \n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n      \n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n      \n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach((lightObj) => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n      \n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n      \n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n    \n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n    \n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n    \n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n    \n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      \n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n  \n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          console.log('检测到点击事件', event.clientX, event.clientY);\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        } else {\n          console.warn('点击事件处理失败: scene或camera未初始化');\n        }\n      };\n      \n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n      \n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n      \n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n    \n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    \n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n    \n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,//\n          transparent: false,\n          opacity: 0.1,  // 几乎透明\n          depthWrite: false\n        });\n        \n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置\n        \n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n        \n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        \n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500);  // 延迟略长于debugTrafficLights\n    \n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n    \n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n  \n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        const firstIntersection = intersectionsData.intersections[0];\n        console.log('自动选择第一个路口:', firstIntersection.name);\n        \n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(firstIntersection.name);\n        }, 2000);\n        \n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n\n  return (\n    <>\n      <span style={labelStyle}>区域选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择区域位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n        value={selectedIntersection ? selectedIntersection.name : undefined}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      \n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '4px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n            padding: '0',\n            maxWidth: '240px', // 缩小最大宽度\n            fontSize: '12px' // 缩小字体\n          }}\n        >\n          {trafficLightPopover.content}\n          <button \n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '12px',\n              cursor: 'pointer',\n              padding: '2px 6px'\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n      \n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 16, // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    backgroundColor: parameters.backgroundColor || { r: 255, g: 255, b: 255, a: 0.8 },\n    textColor: parameters.textColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  \n  // 设置字体\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  \n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n  \n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  \n  canvas.width = width;\n  canvas.height = height;\n  \n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n  \n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n  \n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n  \n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n  \n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n  \n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n  \n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n  \n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = { \n    text: text,\n    params: params\n  };\n  \n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    try {\n      const [ trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([\n        loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`)\n        \n    ]);\n\n    // 处理机动车模型\n    console.log('加载车辆模型...');\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n          color: 0xff0000,  // 0xff0000, //红色 0xffffff,白色\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n      }\n    });\n\n    console.log('加载非机动车模型...');\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    console.log('加载行人模型...');\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n        \n      }\n      if (child.isMesh){\n        child.castShadow = true;\n      }\n    });\n\n\n\n    // 保存行人动画数据\n    console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n    if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n      console.log('找到行人动画:', peopleGltf.animations.length, '个');\n      peopleBaseModel = peopleGltf;\n    } else {\n      console.warn('行人模型没有包含动画数据');\n    }\n\n    console.log('加载红绿灯模型...');\n      \n    // 处理红绿灯模型\n    preloadedTrafficLightModel = trafficLightGltf.scene;\n    console.log('红绿灯模型：', preloadedTrafficLightModel);\n    // 设置红绿灯模型的缩放\n    preloadedTrafficLightModel.scale.set(6, 6, 6);\n    // 保持原始材质\n    preloadedTrafficLightModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 设置材质属性\n        child.material.metalness = 0.2;\n        child.material.roughness = 0.3;\n        child.material.envMapIntensity = 1.2;\n    }\n  });\n\n    console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n      \n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n        \n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  \n  try {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在1秒后自动移除\n  setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n    scene.remove(sprite);\n      }\n  }, 100);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  // console.log('添加场景事件标记:', {\n  //   位置: position,\n  //   文本: text,\n  //   颜色: color\n  // });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  \n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  \n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n  \n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型 - 只为hasTrafficLight为true的路口创建红绿灯\n  intersectionsData.intersections.forEach(intersection => {\n    // 检查路口是否有红绿灯属性，如果没有或为false，则跳过\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n    \n    // 确认路口有经纬度和ID\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      \n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n        \n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n        \n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n        \n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n        \n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n        \n        // *** 重要：设置为可被交互 ***\n        trafficLightModel.traverse(child => {\n          // 设置所有子对象可被点击\n          if (child.isMesh) {\n            // 确保材质能够被射线检测到\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            \n            // 设置较高的渲染顺序\n            child.renderOrder = 100;\n          }\n        });\n        \n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        \n        // // 添加一个专门用于点击的大型碰撞体\n        // const colliderGeometry = new THREE.SphereGeometry(5, 12, 12);\n        // const colliderMaterial = new THREE.MeshBasicMaterial({\n        //   color: 0xff00ff,\n        //   transparent: true,\n        //   opacity: 0.0,  // 完全透明\n        //   depthWrite: false\n        // });\n        \n        // const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n        // collider.name = `交通灯碰撞体-${intersection.name}`;\n        // collider.userData = {\n        //   type: 'trafficLight',\n        //   interId: intersection.interId,\n        //   name: intersection.name,\n        //   isCollider: true\n        // };\n        \n        // trafficLightModel.add(collider);\n        \n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n        \n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        \n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n  \n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance) => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({ \n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n  \n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n  \n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n  \n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n  \n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,  // 完全透明\n    depthWrite: false\n  });\n  \n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  \n  trafficLightModel.add(collider);\n  \n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n  \n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n  \n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n  \n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  trafficLightModel.add(lightMesh);\n  \n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  switch(phaseId) {\n    case '1': return '北进口左转';\n    case '2': return '北进口直行';\n    case '3': return '北进口右转';\n    case '5': return '东进口左转';\n    case '6': return '东进口直行';\n    case '7': return '东进口右转';\n    case '9': return '南进口左转';\n    case '10': return '南进口直行';\n    case '11': return '南进口右转';\n    case '13': return '西进口左转';\n    case '14': return '西进口直行';\n    case '15': return '西进口右转';\n    default: return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = (dirCode) => {\n  switch(dirCode) {\n    case 'N': return '北向南';\n    case 'S': return '南向北';\n    case 'E': return '东向西';\n    case 'W': return '西向东';\n    case 'NE': return '东北向西南';\n    case 'NW': return '西北向东南';\n    case 'SE': return '东南向西北';\n    case 'SW': return '西南向东北';\n    default: return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  \n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n  \n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n  \n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Line.threshold = 1;\n  \n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  \n  console.log('鼠标点击位置:', mouseX, mouseY);\n  \n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  \n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n  \n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  \n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, \n                 intersect.object.name || '无名称', \n                 '距离:', intersect.distance,\n                 'userData:', intersect.object.userData);\n    });\n    \n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    \n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n      \n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n      \n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n  \n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  \n  console.log('射线检测到的对象数量:', intersects.length);\n  \n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', \n                  'userData:', obj.userData, \n                  '距离:', intersect.distance);\n    });\n    \n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        console.log('检测到红绿灯点击, 路口ID:', interId);\n        \n        // 显示弹窗\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n  \n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n  \n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.1; // 设置一个阈值，只有距离小于此值的才会被选中\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n      \n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n      \n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      \n      console.log(`路口 ${interId} 的距离:`, distance);\n      \n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = { interId, distance };\n      }\n    }\n  });\n  \n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n    \n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  \n  console.log('未检测到任何红绿灯点击');\n  \n  // 如果用户点击了其他任何地方，关闭现有的弹窗\n  if (setPopoverState) {\n    setPopoverState(prev => {\n      if (prev.visible) {\n        return { ...prev, visible: false };\n      }\n      return prev;\n    });\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n  \n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n  \n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({ ...prev, visible: false }));\n  console.log('弹窗已关闭');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = (interId) => {\n  try {\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      \n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      \n      return false;\n    }\n    \n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n    \n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 创建弹出窗口内容\n    let content;\n    \n    if (stateInfo && stateInfo.phases) {\n      content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection.name} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              switch (phase.trafficLight) {\n                case 'G': lightColor = '#00ff00'; break;\n                case 'Y': lightColor = '#ffff00'; break;\n                case 'R': default: lightColor = '#ff0000'; break;\n              }\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {getPhaseDirection(phase.phaseId)}\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      content = (\n        <div style={{ padding: '8px', maxWidth: '200px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>\n          <div>路口ID: {interId}</div>\n          <div>当前无信号灯状态信息</div>\n        </div>\n      );\n    }\n    \n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2;\n    const centerY = window.innerHeight / 2;\n    \n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;\n    \n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: { x: centerX, y: centerY },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      \n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      \n      document.body.appendChild(popover);\n      \n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      \n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  \n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  \n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  \n  return list;\n};\n\n// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n\n//   console.log('开始调试红绿灯模型...');\n  \n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n  \n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n  \n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n      \n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n  \n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n  \n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n      \n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n      \n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n      \n//       // 添加到场景\n//       scene.add(highlightMesh);\n      \n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n      \n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n      \n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n  \n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = (interId) => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    \n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    console.log('当前trafficLightStates大小:', trafficLightStates.size);\n    \n    // 如果没有指定ID，则使用第一个红绿灯\n    if (!interId && trafficLightsMap.size > 0) {\n      interId = String(Array.from(trafficLightsMap.keys())[0]);\n      console.log('未指定ID，使用第一个红绿灯ID:', interId);\n    }\n    \n    // 输出所有可用的红绿灯ID用于调试\n    console.log('所有可用的红绿灯ID:');\n    trafficLightsMap.forEach((light, id) => {\n      console.log(`- ${id} (${typeof id}): ${light.intersection?.name || '未知'}`);\n    });\n    \n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      \n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    \n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    \n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n    \n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n    \n    // 创建更新函数\n    const updateTrafficLightPopover = () => {\n      // 确保当前弹窗仍然可见\n      if (!window._setTrafficLightPopover) return;\n      \n      const currentId = window.currentPopoverIdRef?.current;\n      if (!currentId) return;\n      \n      // 重新获取最新的状态信息\n      let stateInfo = trafficLightStates.get(currentId);\n      if (!stateInfo && typeof currentId === 'string') {\n        // 尝试使用数字ID\n        stateInfo = trafficLightStates.get(parseInt(currentId));\n      } else if (!stateInfo && typeof currentId === 'number') {\n        // 尝试使用字符串ID\n        stateInfo = trafficLightStates.get(String(currentId));\n      }\n      \n      if (!stateInfo || !stateInfo.phases || stateInfo.phases.length === 0) {\n        console.log(`路口ID ${currentId} 没有状态信息，跳过更新`);\n        return;\n      }\n      \n      // 获取交通灯信息\n      const intersectionLight = trafficLightsMap.get(currentId) || \n                               (typeof currentId === 'string' ? trafficLightsMap.get(parseInt(currentId)) : \n                                trafficLightsMap.get(String(currentId)));\n                                \n      if (!intersectionLight) {\n        console.error(`找不到路口ID ${currentId} 的红绿灯信息，无法更新弹窗`);\n        return;\n      }\n      \n      const intersection = intersectionLight.intersection;\n      \n      // 创建更新的弹窗内容\n      const content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection?.name || '未知路口'} (ID: {currentId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              let lightText;\n              \n              switch (phase.trafficLight) {\n                case 'G':\n                  lightColor = '#00ff00';\n                  lightText = '绿灯';\n                  break;\n                case 'Y':\n                  lightColor = '#ffff00';\n                  lightText = '黄灯';\n                  break;\n                case 'R':\n                default:\n                  lightColor = '#ff0000';\n                  lightText = '红灯';\n                  break;\n              }\n              \n              const direction = getPhaseDirection(phase.phaseId);\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {direction} (相位ID: {phase.phaseId})\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {lightText}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date(stateInfo.updateTime).toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n      \n      // 更新弹窗内容\n      window._setTrafficLightPopover(prev => ({\n        ...prev,\n        content: content,\n        phases: stateInfo.phases\n      }));\n      \n      console.log(`已更新路口 ${intersection?.name || currentId} 的红绿灯状态弹窗`);\n    };\n    \n    // 先执行一次初始更新\n    const updateInitialTrafficLightPopover = () => {\n      // 同样，查找红绿灯状态信息时也尝试字符串和数字两种类型\n      let stateInfo = trafficLightStates.get(interId);\n      if (!stateInfo) {\n        // 尝试使用数字ID\n        const numericId = parseInt(interId);\n        stateInfo = trafficLightStates.get(numericId);\n        \n        if (stateInfo) {\n          console.log(`使用数字ID ${numericId} 找到了红绿灯状态信息`);\n        }\n      }\n      \n      console.log(`路口ID ${interId} 的状态信息:`, stateInfo);\n      \n      const intersection = trafficLight.intersection;\n      console.log('路口信息:', intersection);\n      \n      // 创建弹窗内容\n      let content;\n      \n      if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n        // 添加一个检查相位数据的打印\n        console.log('相位数据详情:');\n        stateInfo.phases.forEach((phase, index) => {\n          console.log(`相位 ${index+1}: ID=${phase.phaseId}, 状态=${phase.trafficLight}, 剩余时间=${phase.remainTime}`);\n        });\n        \n        content = (\n          <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n            <div style={{ \n              fontWeight: 'bold', \n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            }}>\n              {intersection?.name || '未知路口'} (ID: {interId})\n            </div>\n            <div>\n              {stateInfo.phases.map((phase, index) => {\n                let lightColor;\n                let lightText;\n                \n                switch (phase.trafficLight) {\n                  case 'G':\n                    lightColor = '#00ff00';\n                    lightText = '绿灯';\n                    break;\n                  case 'Y':\n                    lightColor = '#ffff00';\n                    lightText = '黄灯';\n                    break;\n                  case 'R':\n                  default:\n                    lightColor = '#ff0000';\n                    lightText = '红灯';\n                    break;\n                }\n                \n                const direction = getPhaseDirection(phase.phaseId);\n                \n                return (\n                  <div key={index} style={{ \n                    marginBottom: '6px', \n                    backgroundColor: 'rgba(255,255,255,0.1)',\n                    padding: '4px',\n                    borderRadius: '4px',\n                    fontSize: '12px'\n                  }}>\n                    <div style={{ fontWeight: 'bold' }}>\n                      {direction} (相位ID: {phase.phaseId})\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <span>灯色: </span>\n                      <span style={{ \n                        color: lightColor, \n                        fontWeight: 'bold',\n                        backgroundColor: 'rgba(0,0,0,0.3)',\n                        padding: '0 3px',\n                        borderRadius: '2px'\n                      }}>\n                        {lightText}\n                      </span>\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <span>倒计时: </span>\n                      <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n            <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n              更新时间: {new Date(stateInfo.updateTime).toLocaleTimeString()} (自动刷新)\n            </div>\n          </div>\n        );\n      } else {\n        // 如果没有状态信息，创建一个示例内容\n        content = (\n          <div style={{ padding: '8px', width: '220px' }}>\n            <div style={{ \n              fontWeight: 'bold', \n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            }}>\n              {intersection?.name || '未知路口'} (ID: {interId})\n            </div>\n            <div style={{ color: '#ff4d4f', fontSize: '12px' }}>\n              该路口暂无红绿灯状态信息，请等待SPAT消息更新。\n            </div>\n            <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n              当前时间: {new Date().toLocaleTimeString()}\n            </div>\n          </div>\n        );\n      }\n      \n      // 设置弹窗位置在屏幕中央\n      const x = window.innerWidth / 2;\n      const y = window.innerHeight / 2;\n      \n      // 更新弹窗状态\n      if (window._setTrafficLightPopover) {\n        console.log('调用_setTrafficLightPopover更新弹窗状态', {\n          visible: true,\n          interId,\n          position: { x, y }\n        });\n        \n        window._setTrafficLightPopover({\n          visible: true,\n          interId: interId,\n          position: { x, y },\n          content: content,\n          phases: stateInfo?.phases || []\n        });\n        \n        console.log(`已显示路口 ${intersection?.name || interId} 的红绿灯状态弹窗`);\n        \n        // 设置定时更新\n        if (window.trafficLightUpdateTimerRef) {\n          window.trafficLightUpdateTimerRef.current = setInterval(updateTrafficLightPopover, TRAFFIC_LIGHT_UPDATE_INTERVAL * 1000);\n          console.log(`已设置红绿灯状态弹窗定时更新，间隔 ${TRAFFIC_LIGHT_UPDATE_INTERVAL} 秒`);\n        }\n        \n        return true;\n      } else {\n        console.error('无法找到setTrafficLightPopover函数');\n        return false;\n      }\n    };\n    \n    return updateInitialTrafficLightPopover();\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n  \n//   console.log('开始生成模拟SPAT数据...');\n  \n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n  \n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n  \n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n    \n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n      \n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n    \n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n      \n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n      \n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n    \n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n    \n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n    \n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n    \n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n  \n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n  \n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n  \n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n  \n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n  \n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n  \n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  \n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n    \n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n    \n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n    \n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n    \n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    \n    console.log('归一化坐标:', mouseX, mouseY);\n    \n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    \n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n    \n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n    \n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    \n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', \n                    '距离:', intersect.distance,\n                    'position:', intersect.object.position.toArray(),\n                    'userData:', intersect.object.userData);\n        \n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      \n      return true;\n    }\n    \n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    \n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', \n                  '类型:', obj.type,\n                  '位置:', obj.position.toArray(),\n                  '距离:', intersect.distance,\n                  'userData:', obj.userData);\n    });\n    \n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    \n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n        \n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n        \n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n        \n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        \n        if (isVisible) {\n          visibleCount++;\n        }\n        \n        console.log(`红绿灯 ${interId}:`, {\n          名称: lightObj.intersection?.name || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    \n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n    \n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  \n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch(phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n  \n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ \n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: trafficLight.intersection?.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n  \n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = { isLight: true };\n  \n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  \n  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\n\nexport default CampusModel; \n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAO,KAAKC,aAAa,MAAM,qCAAqC;AACpE,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACtC,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,0BAA0B,GAAG,IAAI,CAAC,CAAC;AACvC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB,IAAIC,eAAe,GAAE,IAAI,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAE,IAAI;;AAElB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAEpB;AACA,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,MAAMC,oBAAoB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;;AAExC;AACA,MAAME,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACV;EACEC,GAAG,EAAE,2BAA2B;EAChCC,GAAG,EAAE,2BAA2B;EAChChB,KAAK,EAAE,6BAA6B;EACtCiB,GAAG,EAAE,2BAA2B;EAAG;EACnCC,IAAI,EAAE,4BAA4B,CAAE;AACtC,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AACzEC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;;AAE1D;AACA,MAAMG,aAAa,GAAG,IAAIlB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,IAAImB,gBAAgB,GAAG,IAAInB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAElC;AACA,IAAIoB,gBAAgB,GAAG,IAAI;;AAE3B;AACA,IAAIC,gBAAgB,GAAG,IAAIrB,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,IAAIsB,kBAAkB,GAAG,IAAItB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEpC,MAAMuB,KAAK,GAAG,IAAIzD,KAAK,CAAC0D,KAAK,CAAC,CAAC;;AAE/B;AACA,MAAMC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGf,QAAQ,oBAAoB,CAAC;IAC7D,MAAMgB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,IAAIA,IAAI,CAACE,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;MACzD,MAAMG,WAAW,GAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,IAAI,CAAC;MACrE,IAAIH,WAAW,IAAIA,WAAW,CAACI,KAAK,EAAE;QACpCjB,gBAAgB,GAAGa,WAAW,CAACI,KAAK;QACpCrB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEG,gBAAgB,CAAC;QAC7C,OAAOA,gBAAgB;MACzB;IACF;IACAJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChCG,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjClB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB;AACF,CAAC;;AAED;AACA,MAAMmB,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;EAC5C;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAI,CAACjD,YAAY,EAAE;MACjBA,YAAY,GAAGgD,MAAM,CAACE,KAAK,CAAC,CAAC;MAC7B,OAAOF,MAAM;IACf;IAEA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEpD,YAAY,CAACoD,CAAC,EAAElD,KAAK,CAAC;IAChE,MAAMmD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEtD,YAAY,CAACsD,CAAC,EAAEpD,KAAK,CAAC;IAChE,MAAMqD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAExD,YAAY,CAACwD,CAAC,EAAEtD,KAAK,CAAC;IAEhEF,YAAY,CAACyD,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;IACjD,OAAOvD,YAAY,CAACkD,KAAK,CAAC,CAAC;EAC3B;;EAEA;EACA,IAAI,CAAC/C,oBAAoB,CAACuD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC9C,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;EAEA,MAAMW,OAAO,GAAGxD,oBAAoB,CAACyD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,MAAMY,QAAQ,GAAGF,OAAO,CAACG,UAAU,CAACd,MAAM,CAAC;EAC3C,MAAMe,sBAAsB,GAAG,EAAE,CAAC,CAAC;;EAEnC,IAAIF,QAAQ,GAAGE,sBAAsB,EAAE;IACrC3C,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAUY,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAClE7D,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;;EAEA;EACA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEO,OAAO,CAACP,CAAC,EAAElD,KAAK,CAAC;EAC3D,MAAMmD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEK,OAAO,CAACL,CAAC,EAAEpD,KAAK,CAAC;EAC3D,MAAMqD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAEG,OAAO,CAACH,CAAC,EAAEtD,KAAK,CAAC;EAE3D,MAAM+D,WAAW,GAAG,IAAI/F,KAAK,CAACgG,OAAO,CAACf,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACtEpD,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAEgB,WAAW,CAACf,KAAK,CAAC,CAAC,CAAC;EAExD,OAAOe,WAAW;AACpB,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,WAAW,EAAEnB,SAAS,KAAK;EACjD;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAIhD,YAAY,KAAK,IAAI,EAAE;MACzBA,YAAY,GAAGmE,WAAW;MAC1B,OAAOA,WAAW;IACpB;;IAEA;IACA,IAAIC,IAAI,GAAGD,WAAW,GAAGnE,YAAY;IACrC,IAAIoE,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IAExC,MAAMC,gBAAgB,GAAG7B,aAAa,CAAC1C,YAAY,GAAGoE,IAAI,EAAEpE,YAAY,EAAEC,KAAK,CAAC;IAChFD,YAAY,GAAGuE,gBAAgB;IAC7B,OAAOA,gBAAgB;EACzB;;EAEA;EACA,IAAI,CAACnE,oBAAoB,CAACqD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC5C,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMK,OAAO,GAAGpE,oBAAoB,CAACuD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,IAAIoB,IAAI,GAAGD,WAAW,GAAGK,OAAO;EAChC,IAAIJ,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;;EAExC;EACA,MAAMG,mBAAmB,GAAGJ,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,CAAC;EACzC,IAAID,IAAI,CAACK,GAAG,CAACN,IAAI,CAAC,GAAGK,mBAAmB,EAAE;IACxCtD,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAU,CAACoB,IAAI,GAAG,GAAG,GAAGC,IAAI,CAACC,EAAE,EAAEP,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAChF3D,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMI,gBAAgB,GAAG7B,aAAa,CAAC8B,OAAO,GAAGJ,IAAI,EAAEI,OAAO,EAAEvE,KAAK,CAAC;EACtEG,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEuB,gBAAgB,CAAC;EAErD,OAAOA,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA,MAAMI,6BAA6B,GAAG,CAAC,CAAC,CAAC;AACzC;;AAEA,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC,kBAAkB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAMC,YAAY,GAAGnH,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMoH,QAAQ,GAAGpH,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMqH,SAAS,GAAGrH,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMsH,WAAW,GAAGtH,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMuH,WAAW,GAAGvH,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMwH,iBAAiB,GAAGxH,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM4D,KAAK,GAAG5D,MAAM,CAAC,IAAIG,KAAK,CAAC0D,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EACzC,MAAM4D,SAAS,GAAGzH,MAAM,CAAC,IAAIM,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMoH,aAAa,GAAG1H,MAAM,CAAC,EAAE,CAAC;EAChC,MAAM2H,eAAe,GAAG3H,MAAM,CAAC,CAAC,CAAC;EACjC,MAAM4H,aAAa,GAAG5H,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM6H,UAAU,GAAG7H,MAAM,CAAC,IAAI,CAAC;;EAE/B;EACA,MAAM8H,kBAAkB,GAAG9H,MAAM,CAAC,IAAI,CAAC;EACvC,MAAM+H,gBAAgB,GAAG/H,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMgI,eAAe,GAAG,IAAI,CAAC,CAAC;;EAE9B;EACA,MAAMC,oBAAoB,GAAGjI,MAAM,CAACkI,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC/C,MAAMC,qBAAqB,GAAGpI,MAAM,CAAC,IAAIqC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,MAAMgG,gBAAgB,GAAGrI,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;;EAKrC;EACA,MAAM,CAACsI,YAAY,EAAEC,eAAe,CAAC,GAAGtI,QAAQ,CAAC;IAC/CuI,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5I,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAM6I,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,IAAI;IAAG;IACfC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/J,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAACgK,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjK,QAAQ,CAAC;IAC7DkK,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,IAAI;IACbrB,QAAQ,EAAE;MAAE1D,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxB8E,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAGvK,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMwK,0BAA0B,GAAGxK,MAAM,CAAC,IAAI,CAAC;;EAE/C;EACAyC,MAAM,CAACgI,uBAAuB,GAAGP,sBAAsB;;EAEvD;EACAzH,MAAM,CAAC8H,mBAAmB,GAAGA,mBAAmB;EAChD9H,MAAM,CAAC+H,0BAA0B,GAAGA,0BAA0B;;EAE9D;EACA,MAAME,uBAAuB,GAAG;IAC9B3B,QAAQ,EAAE,OAAO;IACjB4B,GAAG,EAAE,MAAM;IACX1B,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7B0B,KAAK,EAAE,OAAO;IAAG;IACjBzB,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMgB,UAAU,GAAG;IACjB9B,QAAQ,EAAE,OAAO;IACjB4B,GAAG,EAAE,MAAM;IACX1B,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBuB,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbnB,QAAQ,EAAE,MAAM;IAChBoB,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvC9B,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM,CAACzF,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,IAAIoC,GAAG,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAAC6I,UAAU,EAAEC,aAAa,CAAC,GAAGlL,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACuD,gBAAgB,EAAE4H,mBAAmB,CAAC,GAAGnL,QAAQ,CAAC,IAAIoC,GAAG,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACgJ,WAAW,EAAEC,cAAc,CAAC,GAAGrL,QAAQ,CAAC;IAAEsL,KAAK,EAAE,EAAE;IAAElB,OAAO,EAAE;EAAG,CAAC,CAAC;;EAE1E;EACA,MAAMmB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIhK,UAAU,KAAK,QAAQ,EAAE;MAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB9B,UAAU,GAAG,QAAQ;;MAErB;MACAsG,kBAAkB,CAAC2D,OAAO,GAAG,IAAI;MACjC1D,gBAAgB,CAAC0D,OAAO,GAAG,IAAI;MAE/B,IAAIhK,QAAQ,EAAE;QACZA,QAAQ,CAACiK,OAAO,GAAG,KAAK;MAC1B;IACF;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAInK,UAAU,KAAK,QAAQ,EAAE;MAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB9B,UAAU,GAAG,QAAQ;;MAErB;MACAsG,kBAAkB,CAAC2D,OAAO,GAAG,IAAI;MACjC1D,gBAAgB,CAAC0D,OAAO,GAAG,IAAI;MAE/B,IAAIpE,SAAS,CAACoE,OAAO,IAAIhK,QAAQ,EAAE;QACjC;QACA;QACA4F,SAAS,CAACoE,OAAO,CAAC1C,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC,MAAMkG,UAAU,GAAGvE,SAAS,CAACoE,OAAO,CAAC1C,QAAQ,CAAC5D,KAAK,CAAC,CAAC;QACrD,MAAM0G,SAAS,GAAGxE,SAAS,CAACoE,OAAO,CAACK,EAAE,CAAC3G,KAAK,CAAC,CAAC;;QAE9C;QACA,IAAI5E,KAAK,CAACwL,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC;UAAE3G,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,GAAG;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAChCwG,MAAM,CAAC1L,KAAK,CAAC2L,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdhF,SAAS,CAACoE,OAAO,CAAC1C,QAAQ,CAACuD,IAAI,CAACV,UAAU,CAAC;QAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;QAEV;QACA,IAAIhM,KAAK,CAACwL,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;UAAE3G,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAC9BwG,MAAM,CAAC1L,KAAK,CAAC2L,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdhF,SAAS,CAACoE,OAAO,CAACK,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC;QACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC;;QAEV;QACA9K,QAAQ,CAAC+K,MAAM,CAAC9G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B,MAAM+G,aAAa,GAAGhL,QAAQ,CAAC+K,MAAM,CAACrH,KAAK,CAAC,CAAC;;QAE7C;QACA,IAAI5E,KAAK,CAACwL,KAAK,CAACU,aAAa,CAAC,CAC3BT,EAAE,CAAC;UAAE3G,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAC9BwG,MAAM,CAAC1L,KAAK,CAAC2L,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACd5K,QAAQ,CAAC+K,MAAM,CAACF,IAAI,CAACG,aAAa,CAAC;UACnC;UACApF,SAAS,CAACoE,OAAO,CAACiB,MAAM,CAACjL,QAAQ,CAAC+K,MAAM,CAAC;UACzC/K,QAAQ,CAACkL,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;QAEV;QACA9K,QAAQ,CAACiK,OAAO,GAAG,IAAI;;QAEvB;QACAjK,QAAQ,CAACmL,WAAW,GAAG,EAAE;QACzBnL,QAAQ,CAACoL,WAAW,GAAG,GAAG;QAC1BpL,QAAQ,CAACqL,aAAa,GAAGvG,IAAI,CAACC,EAAE,GAAG,GAAG;QACtC/E,QAAQ,CAACsL,aAAa,GAAG,CAAC;QAC1BtL,QAAQ,CAACkL,MAAM,CAAC,CAAC;QACjB;QACAtF,SAAS,CAACoE,OAAO,CAACuB,YAAY,CAAC,CAAC;QAChC3F,SAAS,CAACoE,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;QAEzC5J,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;UACrB4J,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAG3M,iBAAiB,CAAC4M,aAAa,CAACjJ,IAAI,CAACkJ,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKJ,KAAK,CAAC;IAChF,IAAIC,YAAY,IAAIlG,SAAS,CAACoE,OAAO,IAAIhK,QAAQ,EAAE;MACjDuI,uBAAuB,CAACuD,YAAY,CAAC;;MAErC;MACA,MAAMI,WAAW,GAAGlG,SAAS,CAACgE,OAAO,CAACmC,YAAY,CAChDC,UAAU,CAACN,YAAY,CAAC/E,SAAS,CAAC,EAClCqF,UAAU,CAACN,YAAY,CAAC9E,QAAQ,CAClC,CAAC;MAEDpF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvBwK,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBK,GAAG,EAAE;UACHvF,SAAS,EAAE+E,YAAY,CAAC/E,SAAS;UACjCC,QAAQ,EAAE8E,YAAY,CAAC9E;QACzB,CAAC;QACDuF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACAnM,UAAU,GAAG,cAAc;MAC3BqH,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAxB,SAAS,CAACoE,OAAO,CAAC1C,QAAQ,CAACrD,GAAG,CAACiI,WAAW,CAACtI,CAAC,GAAC,EAAE,EAAE,EAAE,EAAE,CAACsI,WAAW,CAACpI,CAAC,GAAC,EAAE,CAAC;;MAEvE;MACA9D,QAAQ,CAAC+K,MAAM,CAAC9G,GAAG,CAACiI,WAAW,CAACtI,CAAC,EAAE,CAAC,EAAE,CAACsI,WAAW,CAACpI,CAAC,CAAC;;MAErD;MACA8B,SAAS,CAACoE,OAAO,CAACiB,MAAM,CAACjL,QAAQ,CAAC+K,MAAM,CAAC;;MAEzC;MACA/K,QAAQ,CAACiK,OAAO,GAAG,IAAI;MACvBjK,QAAQ,CAACkL,MAAM,CAAC,CAAC;;MAEjB;MACAtF,SAAS,CAACoE,OAAO,CAACuB,YAAY,CAAC,CAAC;MAChC3F,SAAS,CAACoE,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;MAEzC5J,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBwK,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBO,IAAI,EAAE5G,SAAS,CAACoE,OAAO,CAAC1C,QAAQ,CAACmF,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAE1M,QAAQ,CAAC+K,MAAM,CAAC0B,OAAO,CAAC,CAAC;QAC9BF,IAAI,EAAEL;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAGA,CAAC7C,KAAK,EAAE8C,OAAO,KAAK;IAC5C,IAAI;MACF,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;MAEnC;MACA,IAAI9C,KAAK,KAAKhJ,WAAW,CAACO,GAAG,EAAE;QAAA,IAAA2L,aAAA;QAC7B;;QAEA;QACA,MAAMC,SAAS,GAAGJ,OAAO,CAACK,GAAG;QAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;QAEnC;QACA,MAAMC,aAAa,GAAGtL,gBAAgB,CAACqC,GAAG,CAAC6I,SAAS,CAAC;;QAErD;QACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;UACrD;UACA;UACA;UACA;UACA;UACJ;QACF;;QAEI;QACAtL,gBAAgB,CAACkC,GAAG,CAACgJ,SAAS,EAAEE,gBAAgB,CAAC;;QAEjD;QACA;QACA;QACA;QACA;;QAEA,MAAMG,YAAY,GAAG,EAAAN,aAAA,GAAAH,OAAO,CAACrK,IAAI,cAAAwK,aAAA,uBAAZA,aAAA,CAAcM,YAAY,KAAI,EAAE;QACrD,MAAMC,KAAK,GAAGV,OAAO,CAACrK,IAAI,CAAC+K,KAAK;;QAEhC;QACA,MAAM7G,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;;QAEtB;QACA4G,YAAY,CAACE,OAAO,CAACC,WAAW,IAAI;UAClC;UACA;UACA,MAAMC,EAAE,GAAIT,SAAS,GAAGQ,WAAW,CAACE,SAAS;UAC7C,MAAMC,IAAI,GAAGH,WAAW,CAACI,WAAW;UAEpC,IAAGD,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACA;YACA,MAAME,KAAK,GAAG;cACZ/G,SAAS,EAAEqF,UAAU,CAACqB,WAAW,CAACM,WAAW,CAAC;cAC9C/G,QAAQ,EAAEoF,UAAU,CAACqB,WAAW,CAACO,UAAU,CAAC;cAC5C/G,KAAK,EAAEmF,UAAU,CAACqB,WAAW,CAACQ,SAAS,CAAC;cACxC/G,OAAO,EAAEkF,UAAU,CAACqB,WAAW,CAACS,WAAW;YAC7C,CAAC;YAED,MAAMC,QAAQ,GAAGnI,SAAS,CAACgE,OAAO,CAACmC,YAAY,CAAC2B,KAAK,CAAC/G,SAAS,EAAE+G,KAAK,CAAC9G,QAAQ,CAAC;;YAEhF;YACA,IAAIoH,cAAc;YAClB,QAAQR,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRQ,cAAc,GAAGnO,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRmO,cAAc,GAAGlO,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRkO,cAAc,GAAGjO,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACA,IAAIkO,KAAK,GAAGvM,aAAa,CAACsC,GAAG,CAACsJ,EAAE,CAAC;YAEjC,IAAI,CAACW,KAAK,IAAID,cAAc,EAAE;cAC5B;cACA,MAAME,QAAQ,GAAGF,cAAc,CAAC1K,KAAK,CAAC,CAAC;cACvC;cACA,MAAM6K,MAAM,GAAGX,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;cACvCU,QAAQ,CAAChH,QAAQ,CAACrD,GAAG,CAACkK,QAAQ,CAACvK,CAAC,EAAE2K,MAAM,EAAE,CAACJ,QAAQ,CAACrK,CAAC,CAAC;cACtDwK,QAAQ,CAACE,QAAQ,CAAC1K,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAG+I,KAAK,CAAC5G,OAAO,GAAGpC,IAAI,CAACC,EAAE,GAAG,GAAG;;cAE7D;cACA,IAAI6I,IAAI,KAAK,GAAG,EAAE;gBAChB;gBACA,MAAMa,WAAW,GAAGtO,oBAAoB,CAACuD,KAAK,CAAC,CAAC;gBAChD+K,WAAW,CAACnH,QAAQ,CAACrD,GAAG,CAACkK,QAAQ,CAACvK,CAAC,EAAE,GAAG,EAAE,CAACuK,QAAQ,CAACrK,CAAC,CAAC;gBACtD2K,WAAW,CAACD,QAAQ,CAAC1K,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAG+I,KAAK,CAAC5G,OAAO,GAAGpC,IAAI,CAACC,EAAE,GAAG,GAAG;gBAChE0J,WAAW,CAACC,KAAK,CAACzK,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBACzC5D,KAAK,CAACsO,GAAG,CAACF,WAAW,CAAC;;gBAEtB;gBACA,MAAMG,KAAK,GAAG,IAAIlQ,KAAK,CAACmQ,cAAc,CAACJ,WAAW,CAAC;gBACnD;gBACA,IAAInO,eAAe,IAAIA,eAAe,CAACwO,UAAU,IAAIxO,eAAe,CAACwO,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;kBAC1F,MAAMC,aAAa,GAAG1O,eAAe,CAACwO,UAAU,CAAChM,IAAI,CAACmM,IAAI,IAAIA,IAAI,CAAChD,IAAI,KAAK,MAAM,CAAC;kBACnF,IAAI+C,aAAa,EAAE;oBACjB,MAAME,MAAM,GAAGN,KAAK,CAACO,UAAU,CAACH,aAAa,CAAC;oBAC9CE,MAAM,CAACE,IAAI,CAAC,CAAC;kBACf;gBACF;gBACA;gBACAzI,qBAAqB,CAACqD,OAAO,CAAC/F,GAAG,CAACyJ,EAAE,EAAEkB,KAAK,CAAC;cAC9C;cAEAvO,KAAK,CAACsO,GAAG,CAACL,QAAQ,CAAC;cAEnBxM,aAAa,CAACmC,GAAG,CAACyJ,EAAE,EAAE;gBACpBW,KAAK,EAAEC,QAAQ;gBACfe,UAAU,EAAE3I,GAAG;gBACfkH,IAAI,EAAEA;cACR,CAAC,CAAC;YACJ,CAAC,MAAM,IAAIS,KAAK,EAAE;cAChB;cACAA,KAAK,CAACA,KAAK,CAAC/G,QAAQ,CAACrD,GAAG,CAACkK,QAAQ,CAACvK,CAAC,EAAEyK,KAAK,CAACT,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAACO,QAAQ,CAACrK,CAAC,CAAC;cACjFuK,KAAK,CAACA,KAAK,CAACG,QAAQ,CAAC1K,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAG+I,KAAK,CAAC5G,OAAO,GAAGpC,IAAI,CAACC,EAAE,GAAG,GAAG;cAChEsJ,KAAK,CAACgB,UAAU,GAAG3I,GAAG;cACtB2H,KAAK,CAACA,KAAK,CAAC9C,YAAY,CAAC,CAAC;cAC1B8C,KAAK,CAACA,KAAK,CAAC7C,iBAAiB,CAAC,IAAI,CAAC;YACrC;UACA;QACF,CAAC,CAAC;;QAEF;QACA,MAAM8D,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAAClC,YAAY,CAACmC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC/B,SAAS,CAAC,CAAC;QAE9D7L,aAAa,CAAC0L,OAAO,CAAC,CAACmC,SAAS,EAAEjC,EAAE,KAAK;UACvC,IAAIhH,GAAG,GAAGiJ,SAAS,CAACN,UAAU,GAAGC,iBAAiB,IAAI,CAACC,UAAU,CAACrL,GAAG,CAACwJ,EAAE,CAAC,EAAE;YACzErN,KAAK,CAACuP,MAAM,CAACD,SAAS,CAACtB,KAAK,CAAC;YAC7BvM,aAAa,CAAC+N,MAAM,CAACnC,EAAE,CAAC;YACxB;UACF;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAI5D,KAAK,KAAKhJ,WAAW,CAACM,GAAG,EAAE;QAC7B;;QAEA,MAAM0O,OAAO,GAAGjD,OAAO,CAACrK,IAAI;QAC5B,MAAMuN,KAAK,GAAGD,OAAO,CAAC7M,KAAK;QAC3B,MAAM+M,QAAQ,GAAG;UACfjJ,SAAS,EAAEqF,UAAU,CAAC0D,OAAO,CAACG,QAAQ,CAAC;UACvCjJ,QAAQ,EAAEoF,UAAU,CAAC0D,OAAO,CAACI,OAAO,CAAC;UACrCjJ,KAAK,EAAEmF,UAAU,CAAC0D,OAAO,CAAC7B,SAAS,CAAC;UACpC/G,OAAO,EAAEkF,UAAU,CAAC0D,OAAO,CAAC5B,WAAW;QACzC,CAAC;;QAED;QACA;;QAEA;QACAlN,MAAM,CAACmP,WAAW,CAAC;UACjBvC,IAAI,EAAE,iBAAiB;UACvBwC,MAAM,EAAE;QACV,CAAC,EAAE,GAAG,CAAC;;QAEP;QACApP,MAAM,CAACmP,WAAW,CAAC;UACjBvC,IAAI,EAAE,KAAK;UACX3K,KAAK,EAAE8M,KAAK;UAAE;UACdvN,IAAI,EAAE;YAAQ;YACZS,KAAK,EAAE8M,KAAK;YACZ9B,SAAS,EAAE6B,OAAO,CAAC7B,SAAS;YAC5BiC,OAAO,EAAEJ,OAAO,CAACI,OAAO;YACxBD,QAAQ,EAAEH,OAAO,CAACG,QAAQ;YAC1B/B,WAAW,EAAE4B,OAAO,CAAC5B;UACvB;QACF,CAAC,EAAE,GAAG,CAAC;;QAEP;QACA,MAAMC,QAAQ,GAAGnI,SAAS,CAACgE,OAAO,CAACmC,YAAY,CAAC6D,QAAQ,CAACjJ,SAAS,EAAEiJ,QAAQ,CAAChJ,QAAQ,CAAC;QACtF,MAAMqJ,eAAe,GAAG,IAAI3R,KAAK,CAACgG,OAAO,CAACyJ,QAAQ,CAACvK,CAAC,EAAE,GAAG,EAAE,CAACuK,QAAQ,CAACrK,CAAC,CAAC;QACvE,MAAMwM,eAAe,GAAGxL,IAAI,CAACC,EAAE,GAAGiL,QAAQ,CAAC9I,OAAO,GAAGpC,IAAI,CAACC,EAAE,GAAG,GAAG;;QAElE;QACA,MAAMwL,WAAW,GAAGhN,cAAc,CAAC8M,eAAe,EAAEN,KAAK,CAAC;QAC1D,MAAMnL,WAAW,GAAGD,cAAc,CAAC2L,eAAe,EAAEP,KAAK,CAAC;;QAE1D;QACA,IAAIS,UAAU,GAAG1O,aAAa,CAACsC,GAAG,CAAC2L,KAAK,CAAC;;QAEzC;QACA,MAAM/M,aAAa,GAAG+M,KAAK,KAAK/N,gBAAgB;QAEhD,IAAI,CAACwO,UAAU,IAAIvQ,qBAAqB,EAAE;UACxC;UACA,MAAMwQ,eAAe,GAAGxQ,qBAAqB,CAACyD,KAAK,CAAC,CAAC;;UAErD;UACA;UACA+M,eAAe,CAACnJ,QAAQ,CAACrD,GAAG,CAACsM,WAAW,CAAC3M,CAAC,EAAE,CAAC,CAAC,EAAE2M,WAAW,CAACvM,CAAC,CAAC;UAC9DyM,eAAe,CAACjC,QAAQ,CAAC1K,CAAC,GAAGc,WAAW;;UAExC;UACA6L,eAAe,CAACC,QAAQ,CAAEC,KAAK,IAAK;YAClC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;cAClC;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;;cAEA;cACA;cACA;;cAEA;;cAEA,MAAMC,WAAW,GAAGH,KAAK,CAACE,QAAQ,CAACnN,KAAK,CAAC,CAAC;cAC1CiN,KAAK,CAACE,QAAQ,GAAGC,WAAW;;cAE5B;cACA,IAAI9N,aAAa,EAAE;gBACjB8N,WAAW,CAACxH,KAAK,CAACrF,GAAG,CAAC,QAAQ,CAAC;cACjC,CAAC,MAAM;gBACL6M,WAAW,CAACxH,KAAK,CAACrF,GAAG,CAAC,QAAQ,CAAC;cACjC;cACA6M,WAAW,CAACC,QAAQ,GAAG,IAAIrS,KAAK,CAACsS,KAAK,CAAC,QAAQ,CAAC;cAChDF,WAAW,CAACG,WAAW,GAAG,IAAI;cAC9B;cACAH,WAAW,CAACI,WAAW,GAAG,IAAI;YAChC;UACF,CAAC,CAAC;;UAEF;UACA,MAAMC,UAAU,GAAGC,gBAAgB,CAAC,GAAGtM,IAAI,CAACuM,KAAK,CAACrB,QAAQ,CAAC/I,KAAK,CAAC,OAAO,EAAE;YACxEc,eAAe,EAAE/E,aAAa,GAC5B;cAAEsO,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCC,SAAS,EAAE;cAAEJ,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/CtJ,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFqJ,UAAU,CAAC7J,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClCkN,UAAU,CAACQ,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/BR,UAAU,CAACN,QAAQ,CAACe,OAAO,GAAG,GAAG,CAAC,CAAC;UACnCnB,eAAe,CAAC9B,GAAG,CAACwC,UAAU,CAAC;UAE/B9Q,KAAK,CAACsO,GAAG,CAAC8B,eAAe,CAAC;;UAE1B;UACA3O,aAAa,CAACmC,GAAG,CAAC8L,KAAK,EAAE;YACvB1B,KAAK,EAAEoC,eAAe;YACtBpB,UAAU,EAAE5I,IAAI,CAACC,GAAG,CAAC,CAAC;YACtBkH,IAAI,EAAE,GAAG;YAAE;YACXiE,MAAM,EAAE7O,aAAa;YACrBmO,UAAU,EAAEA,UAAU,CAAC;UACzB,CAAC,CAAC;;UAEF;;UAEA;UACA,IAAIrS,KAAK,CAACwL,KAAK,CAACmG,eAAe,CAACnJ,QAAQ,CAAC,CACtCiD,EAAE,CAAC;YAAEzG,CAAC,EAAE;UAAI,CAAC,EAAE,GAAG,CAAC,CACnB0G,MAAM,CAAC1L,KAAK,CAAC2L,MAAM,CAACC,SAAS,CAACoH,GAAG,CAAC,CAClChH,KAAK,CAAC,CAAC;;UAEV;UACA2F,eAAe,CAACC,QAAQ,CAAEC,KAAK,IAAK;YAClC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACI,WAAW,EAAE;cAChE,IAAInS,KAAK,CAACwL,KAAK,CAAC;gBAAEsH,OAAO,EAAE;cAAI,CAAC,CAAC,CAC9BrH,EAAE,CAAC;gBAAEqH,OAAO,EAAE;cAAI,CAAC,EAAE,GAAG,CAAC,CACzBpH,MAAM,CAAC1L,KAAK,CAAC2L,MAAM,CAACC,SAAS,CAACoH,GAAG,CAAC,CAClClH,QAAQ,CAAC,YAAW;gBACnB+F,KAAK,CAACE,QAAQ,CAACe,OAAO,GAAG,IAAI,CAACA,OAAO;gBACrCjB,KAAK,CAACE,QAAQ,CAACK,WAAW,GAAG,IAAI;cACnC,CAAC,CAAC,CACDpG,KAAK,CAAC,CAAC;YACZ;UACF,CAAC,CAAC;;UAEF;UACA,IAAIhM,KAAK,CAACwL,KAAK,CAAC;YAAEsH,OAAO,EAAE;UAAI,CAAC,CAAC,CAC9BrH,EAAE,CAAC;YAAEqH,OAAO,EAAE;UAAI,CAAC,EAAE,GAAG,CAAC,CACzBpH,MAAM,CAAC1L,KAAK,CAAC2L,MAAM,CAACC,SAAS,CAACoH,GAAG,CAAC,CAClClH,QAAQ,CAAC,YAAW;YACnBuG,UAAU,CAACN,QAAQ,CAACe,OAAO,GAAG,IAAI,CAACA,OAAO;YAC1CT,UAAU,CAACN,QAAQ,CAACK,WAAW,GAAG,IAAI;UACxC,CAAC,CAAC,CACDpG,KAAK,CAAC,CAAC;;UAEV;UACA,IAAI9H,aAAa,EAAE;YACjBxD,gBAAgB,GAAGiR,eAAe;YAClC3J,eAAe,CAACkJ,QAAQ,CAAC;YACzBpO,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEkO,KAAK,CAAC;UACjC;QACF,CAAC,MAAM,IAAIS,UAAU,EAAE;UACrB;UACA,MAAMuB,gBAAgB,GAAGxO,cAAc,CAACgN,WAAW,EAAER,KAAK,CAAC;UAC3D,MAAM/K,gBAAgB,GAAGL,cAAc,CAACC,WAAW,EAAEmL,KAAK,CAAC;;UAE3D;UACAS,UAAU,CAACnC,KAAK,CAAC/G,QAAQ,CAACuD,IAAI,CAACkH,gBAAgB,CAAC;UAChDvB,UAAU,CAACnC,KAAK,CAACG,QAAQ,CAAC1K,CAAC,GAAGkB,gBAAgB;UAC9CwL,UAAU,CAACnC,KAAK,CAAC9C,YAAY,CAAC,CAAC;UAC/BiF,UAAU,CAACnC,KAAK,CAAC7C,iBAAiB,CAAC,IAAI,CAAC;UACxCgF,UAAU,CAACnB,UAAU,GAAG5I,IAAI,CAACC,GAAG,CAAC,CAAC;UAClC8J,UAAU,CAACqB,MAAM,GAAG7O,aAAa,CAAC,CAAC;;UAEnC;UACA,IAAIwN,UAAU,CAACW,UAAU,EAAE;YACzBX,UAAU,CAACW,UAAU,CAACN,QAAQ,CAACpB,GAAG,CAACuC,OAAO,CAAC,CAAC;YAC5CxB,UAAU,CAACnC,KAAK,CAACuB,MAAM,CAACY,UAAU,CAACW,UAAU,CAAC;UAChD;;UAEA;UACA,MAAMA,UAAU,GAAGC,gBAAgB,CAAC,GAAGtM,IAAI,CAACuM,KAAK,CAACrB,QAAQ,CAAC/I,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE;YAC9Ec,eAAe,EAAE/E,aAAa,GAC5B;cAAEsO,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCC,SAAS,EAAE;cAAEJ,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/CtJ,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFqJ,UAAU,CAAC7J,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACnCkN,UAAU,CAACQ,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/BnB,UAAU,CAACnC,KAAK,CAACM,GAAG,CAACwC,UAAU,CAAC;UAChCX,UAAU,CAACW,UAAU,GAAGA,UAAU;;UAElC;;UAEA;UACA,IAAInO,aAAa,EAAE;YACjBxD,gBAAgB,GAAGgR,UAAU,CAACnC,KAAK;YACnCvH,eAAe,CAACkJ,QAAQ,CAAC;UAC3B;QACF;;QAEA;QACA,MAAMtJ,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;QACtB,MAAM4I,iBAAiB,GAAG,IAAI,CAAC,CAAC;;QAEhCxN,aAAa,CAAC0L,OAAO,CAAC,CAACmC,SAAS,EAAEjC,EAAE,KAAK;UACvC,MAAMuE,mBAAmB,GAAGvL,GAAG,GAAGiJ,SAAS,CAACN,UAAU;;UAEtD;UACA,IAAI4C,mBAAmB,GAAG3C,iBAAiB,GAAG,GAAG,IAAI2C,mBAAmB,IAAI3C,iBAAiB,EAAE;YAC7F;YACA,MAAMsC,OAAO,GAAG,CAAC;YAEjBjC,SAAS,CAACtB,KAAK,CAACqC,QAAQ,CAAEC,KAAK,IAAK;cAClC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClC;gBACA,IAAIF,KAAK,CAACE,QAAQ,CAACI,WAAW,KAAKiB,SAAS,EAAE;kBAC5CvB,KAAK,CAACE,QAAQ,CAACsB,mBAAmB,GAAGxB,KAAK,CAACE,QAAQ,CAACI,WAAW,IAAI,KAAK;kBACxEN,KAAK,CAACE,QAAQ,CAACuB,eAAe,GAAGzB,KAAK,CAACE,QAAQ,CAACe,OAAO,IAAI,GAAG;gBAChE;;gBAEA;gBACAjB,KAAK,CAACE,QAAQ,CAACI,WAAW,GAAG,IAAI;gBACjCN,KAAK,CAACE,QAAQ,CAACe,OAAO,GAAGA,OAAO;gBAChCjB,KAAK,CAACE,QAAQ,CAACK,WAAW,GAAG,IAAI;cACnC;YACF,CAAC,CAAC;;YAEF;YACA,IAAIvB,SAAS,CAACwB,UAAU,EAAE;cACxBxB,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACe,OAAO,GAAGA,OAAO;cAC/CjC,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACK,WAAW,GAAG,IAAI;YAClD;UACF;UACA;UAAA,KACK,IAAIe,mBAAmB,GAAG3C,iBAAiB,EAAE;YAChD;YACA,IAAIK,SAAS,CAACwB,UAAU,EAAE;cACxBxB,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACpB,GAAG,CAACuC,OAAO,CAAC,CAAC;cAC3CrC,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACmB,OAAO,CAAC,CAAC;cACvCrC,SAAS,CAACtB,KAAK,CAACuB,MAAM,CAACD,SAAS,CAACwB,UAAU,CAAC;YAC9C;YAEAxB,SAAS,CAACtB,KAAK,CAACqC,QAAQ,CAAEC,KAAK,IAAK;cAClC,IAAIA,KAAK,CAACC,MAAM,EAAE;gBAChB,IAAID,KAAK,CAACE,QAAQ,EAAE;kBAClB,IAAIlO,KAAK,CAACC,OAAO,CAAC+N,KAAK,CAACE,QAAQ,CAAC,EAAE;oBACjCF,KAAK,CAACE,QAAQ,CAACrD,OAAO,CAAC6E,CAAC,IAAIA,CAAC,CAACL,OAAO,CAAC,CAAC,CAAC;kBAC1C,CAAC,MAAM;oBACLrB,KAAK,CAACE,QAAQ,CAACmB,OAAO,CAAC,CAAC;kBAC1B;gBACF;gBACA,IAAIrB,KAAK,CAAC2B,QAAQ,EAAE3B,KAAK,CAAC2B,QAAQ,CAACN,OAAO,CAAC,CAAC;cAC9C;YACF,CAAC,CAAC;;YAEF;YACA3R,KAAK,CAACuP,MAAM,CAACD,SAAS,CAACtB,KAAK,CAAC;YAC7BvM,aAAa,CAAC+N,MAAM,CAACnC,EAAE,CAAC;YACxB;YACA/M,oBAAoB,CAACkP,MAAM,CAACnC,EAAE,CAAC;YAC/B7M,oBAAoB,CAACgP,MAAM,CAACnC,EAAE,CAAC;YAE/B9L,OAAO,CAACC,GAAG,CAAC,mBAAmB6L,EAAE,EAAE,CAAC;UACtC;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAI5D,KAAK,KAAKhJ,WAAW,CAACS,IAAI,EAAE;QAC9B;;QAEA,IAAI;UACF,MAAMsL,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;UAEnC;UACA,MAAMK,SAAS,GAAGJ,OAAO,CAACK,GAAG;UAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;UAEnC;UACA,MAAMC,aAAa,GAAGtL,gBAAgB,CAACqC,GAAG,CAAC6I,SAAS,CAAC;;UAErD;UACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;YACrD;YACA;YACA;YACA;YACA;YACA;UACF;;UAEA;UACAtL,gBAAgB,CAACkC,GAAG,CAACgJ,SAAS,EAAEE,gBAAgB,CAAC;;UAEjD;UACA,IAAIN,OAAO,CAACrK,IAAI,IAAIqK,OAAO,CAACrK,IAAI,CAACuJ,aAAa,IAAIpJ,KAAK,CAACC,OAAO,CAACiK,OAAO,CAACrK,IAAI,CAACuJ,aAAa,CAAC,EAAE;YAC3Fc,OAAO,CAACrK,IAAI,CAACuJ,aAAa,CAACyB,OAAO,CAAC1B,YAAY,IAAI;cACjD,MAAMnD,OAAO,GAAGmD,YAAY,CAACnD,OAAO;cAEpC,IAAI,CAACA,OAAO,EAAE;gBACZ/G,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAE4I,YAAY,CAAC;gBAC/C;cACF;;cAEA;;cAEA;cACA,IAAIA,YAAY,CAACjD,MAAM,IAAIlG,KAAK,CAACC,OAAO,CAACkJ,YAAY,CAACjD,MAAM,CAAC,EAAE;gBAC7D;gBACA,MAAM0J,UAAU,GAAG,EAAE;gBAErBzG,YAAY,CAACjD,MAAM,CAAC2E,OAAO,CAACgF,KAAK,IAAI;kBACnC;kBACA,IAAI,CAACA,KAAK,CAACC,OAAO,EAAE;oBAClB7Q,OAAO,CAACsB,KAAK,CAAC,gBAAgB,EAAEsP,KAAK,CAAC;oBACtC;kBACF;kBAEA,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC;kBACxC;kBACA,MAAMC,SAAS,GAAGH,KAAK,CAACI,YAAY,GAClCC,oBAAoB,CAACL,KAAK,CAACI,YAAY,CAAC,GACxCE,iBAAiB,CAACL,OAAO,CAAC;;kBAE5B;kBACA,MAAMM,UAAU,GAAGP,KAAK,CAACQ,YAAY,IAAI,GAAG,CAAC,CAAC;kBAC9C,MAAMC,UAAU,GAAGC,QAAQ,CAACV,KAAK,CAACS,UAAU,CAAC,IAAI,CAAC;;kBAElD;;kBAEA;kBACA,MAAME,SAAS,GAAG;oBAChBV,OAAO;oBACPE,SAAS;oBACTK,YAAY,EAAED,UAAU;oBACxBE;kBACF,CAAC;;kBAED;kBACAV,UAAU,CAACa,IAAI,CAACD,SAAS,CAAC;;kBAE1B;kBACA;kBACA,IAAIE,eAAe,GAAGC,MAAM,CAAC3K,OAAO,CAAC;kBACrC,IAAI4K,iBAAiB,GAAGtR,gBAAgB,CAACmC,GAAG,CAACiP,eAAe,CAAC;kBAE7D,IAAI,CAACE,iBAAiB,EAAE;oBACtB;oBACAF,eAAe,GAAGH,QAAQ,CAACvK,OAAO,CAAC;oBACnC4K,iBAAiB,GAAGtR,gBAAgB,CAACmC,GAAG,CAACiP,eAAe,CAAC;kBAC3D;kBAEA,IAAIE,iBAAiB,EAAE;oBACrB;oBACAC,wBAAwB,CAACD,iBAAiB,EAAEJ,SAAS,CAAC;;oBAEtD;oBACA,IAAI7K,oBAAoB,IAAIA,oBAAoB,CAACK,OAAO,KAAKA,OAAO,EAAE;sBACpEF,sBAAsB,CAACgL,IAAI,KAAK;wBAC9B,GAAGA,IAAI;wBACP/K,OAAO,EAAE,IAAI;wBACb+J,OAAO;wBACPE,SAAS;wBACT7E,KAAK,EAAEiF,UAAU;wBACjBE;sBACF,CAAC,CAAC,CAAC;oBACL;kBACF,CAAC,MAAM;oBACL;kBAAA;gBAEJ,CAAC,CAAC;;gBAEF;gBACA,IAAIS,QAAQ,GAAG,IAAI;gBACnB;gBACA,MAAMC,KAAK,GAAGL,MAAM,CAAC3K,OAAO,CAAC;gBAC7B,IAAI1G,gBAAgB,CAACiC,GAAG,CAACyP,KAAK,CAAC,EAAE;kBAC/BD,QAAQ,GAAGC,KAAK;gBAClB,CAAC,MAAM;kBACL;kBACA,MAAMC,KAAK,GAAGV,QAAQ,CAACvK,OAAO,CAAC;kBAC/B,IAAI1G,gBAAgB,CAACiC,GAAG,CAAC0P,KAAK,CAAC,EAAE;oBAC/BF,QAAQ,GAAGE,KAAK;kBAClB;gBACF;gBAEA,IAAIF,QAAQ,KAAK,IAAI,EAAE;kBACrB;kBACAxR,kBAAkB,CAAC+B,GAAG,CAACyP,QAAQ,EAAE;oBAC/BG,UAAU,EAAEpN,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtBmC,MAAM,EAAE0J;kBACV,CAAC,CAAC;kBACF3Q,OAAO,CAACC,GAAG,CAAC,aAAa6R,QAAQ,KAAK,OAAOA,QAAQ,aAAa,CAAC;;kBAEnE;kBACA,IAAI1S,MAAM,CAAC8H,mBAAmB,KAC1B9H,MAAM,CAAC8H,mBAAmB,CAACkB,OAAO,KAAK0J,QAAQ,IAC/C1S,MAAM,CAAC8H,mBAAmB,CAACkB,OAAO,KAAKsJ,MAAM,CAACI,QAAQ,CAAC,IACvD1S,MAAM,CAAC8H,mBAAmB,CAACkB,OAAO,KAAKkJ,QAAQ,CAACQ,QAAQ,CAAC,CAAC,EAAE;oBAE9D9R,OAAO,CAACC,GAAG,CAAC,eAAe6R,QAAQ,aAAa,CAAC;oBACjD;oBACA1S,MAAM,CAAC8H,mBAAmB,CAACkB,OAAO,GAAG0J,QAAQ;;oBAE7C;oBACA,IAAI1S,MAAM,CAAC+H,0BAA0B,IAAI,CAAC/H,MAAM,CAAC+H,0BAA0B,CAACiB,OAAO,EAAE;sBACnFpI,OAAO,CAACC,GAAG,CAAC,SAAS6R,QAAQ,aAAa,CAAC;sBAC3CI,UAAU,CAAC,MAAM;wBACf9S,MAAM,CAAC+S,qBAAqB,CAACL,QAAQ,CAAC;sBACxC,CAAC,EAAE,GAAG,CAAC;oBACT;kBACF;gBACF,CAAC,MAAM;kBACL;kBACAxR,kBAAkB,CAAC+B,GAAG,CAAC0E,OAAO,EAAE;oBAC9BkL,UAAU,EAAEpN,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtBmC,MAAM,EAAE0J;kBACV,CAAC,CAAC;kBACF;gBACF;cACF,CAAC,MAAM;gBACL3Q,OAAO,CAACsB,KAAK,CAAC,eAAe,EAAE4I,YAAY,CAAC;cAC9C;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACLlK,OAAO,CAACsB,KAAK,CAAC,oCAAoC,EAAE2J,OAAO,CAAC;UAC9D;QACF,CAAC,CAAC,OAAO3J,KAAK,EAAE;UACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEA,KAAK,EAAE0J,OAAO,CAAC;QAC9C;MACF;;MAEA;MACA,IAAI9C,KAAK,KAAKhJ,WAAW,CAACQ,GAAG,IAAIuL,OAAO,CAACe,IAAI,KAAK,KAAK,EAAE;QACvD;;QAEA;QACA5M,MAAM,CAACmP,WAAW,CAAC;UACjBvC,IAAI,EAAE,KAAK;UACXpL,IAAI,EAAEqK,OAAO,CAACrK;QAChB,CAAC,EAAE,GAAG,CAAC;QAEP,MAAMwR,OAAO,GAAGnH,OAAO,CAACrK,IAAI;QAC5B,MAAMyR,KAAK,GAAGD,OAAO,CAACC,KAAK;QAC3B,MAAMC,MAAM,GAAGF,OAAO,CAACG,IAAI,IAAI,EAAE;QAEjCD,MAAM,CAAC1G,OAAO,CAAC4G,KAAK,IAAI;UACtB,MAAMC,OAAO,GAAGD,KAAK,CAACE,KAAK;UAC3B,MAAMC,SAAS,GAAGH,KAAK,CAACG,SAAS;UACjC,MAAMC,WAAW,GAAGJ,KAAK,CAACI,WAAW;UACrC,MAAMC,SAAS,GAAGL,KAAK,CAACK,SAAS;UACjC,MAAMC,OAAO,GAAGN,KAAK,CAACM,OAAO;;UAE7B;UACA,MAAMvG,QAAQ,GAAGnI,SAAS,CAACgE,OAAO,CAACmC,YAAY,CAC7CC,UAAU,CAAC4H,OAAO,CAACW,OAAO,CAAC,EAC3BvI,UAAU,CAAC4H,OAAO,CAACY,MAAM,CAC3B,CAAC;;UAED;UACA,IAAIC,WAAW,GAAG,EAAE;UACpB,IAAIC,YAAY,GAAG,EAAE;UAErB,QAAOP,SAAS;YACd,KAAK,KAAK;cAAG;cACXM,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,QAAQ;cACtBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,MAAM;cAAE;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF;cACED,WAAW,GAAGL,WAAW,IAAI,MAAM;cACnCM,YAAY,GAAG,SAAS;UAC5B;;UAEA;UACAC,iBAAiB,CAAC5G,QAAQ,EAAE0G,WAAW,EAAEC,YAAY,CAAC;;UAEtD;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAIhL,KAAK,KAAKhJ,WAAW,CAACT,KAAK,IAAIwM,OAAO,CAACe,IAAI,KAAK,OAAO,EAAE;QAC3D;;QAEA,MAAMoH,SAAS,GAAGnI,OAAO,CAACrK,IAAI;QAC9B,MAAMyS,OAAO,GAAGD,SAAS,CAACC,OAAO;QACjC,MAAMC,SAAS,GAAGF,SAAS,CAACE,SAAS;QACrC,MAAMC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAM7N,QAAQ,GAAG;UACfN,QAAQ,EAAEoF,UAAU,CAAC4I,SAAS,CAAC9E,OAAO,CAAC;UACvCnJ,SAAS,EAAEqF,UAAU,CAAC4I,SAAS,CAAC/E,QAAQ;QAC1C,CAAC;;QAED;QACA,MAAM9B,QAAQ,GAAGnI,SAAS,CAACgE,OAAO,CAACmC,YAAY,CAAC7E,QAAQ,CAACP,SAAS,EAAEO,QAAQ,CAACN,QAAQ,CAAC;;QAEtF;QACA,QAAOkO,SAAS;UACd,KAAK,GAAG;YAAG;YACTH,iBAAiB,CAAC5G,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;YAClD;UACF,KAAK,KAAK;YAAG;YACX4G,iBAAiB,CAAC5G,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX4G,iBAAiB,CAAC5G,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;YAC/C;UACF,KAAK,IAAI;YAAG;YACV,MAAMiH,UAAU,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAE;YAC1CN,iBAAiB,CAAC5G,QAAQ,EAAE,KAAKiH,UAAU,MAAM,EAAE,SAAS,CAAC;YAC7D;UACF,KAAK,IAAI;YAAG;YACVL,iBAAiB,CAAC5G,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV4G,iBAAiB,CAAC5G,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YAAG;YACZ4G,iBAAiB,CAAC5G,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV4G,iBAAiB,CAAC5G,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV4G,iBAAiB,CAAC5G,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX,MAAMmH,YAAY,GAAGN,SAAS,CAACK,UAAU,CAAC,CAAE;YAC5C,MAAME,QAAQ,GAAGP,SAAS,CAACQ,UAAU,CAAC,CAAM;YAC5CT,iBAAiB,CAAC5G,QAAQ,EAAE,QAAQsH,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,EAAE,SAAS,CAAC;YAC/F;QACJ;QAEA;MACF;MACA;MACA;MACA;MACA;MACA;MACA;IAEF,CAAC,CAAC,OAAOrS,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAE0J,OAAO,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAM8I,cAAc,GAAGA,CAAA,KAAM;IAC3B9T,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAM8T,KAAK,GAAG,QAAQ7U,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnES,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE8T,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBlU,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED+T,EAAE,CAACG,SAAS,GAAI3B,KAAK,IAAK;MACxB,IAAI;QACF,MAAMxH,OAAO,GAAGE,IAAI,CAACC,KAAK,CAACqH,KAAK,CAAC5R,IAAI,CAAC;;QAEtC;QACA,IAAIoK,OAAO,CAACgB,IAAI,KAAK,SAAS,EAAE;UAC9BhM,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE+K,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACgB,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIhB,OAAO,CAACgB,IAAI,KAAK,SAAS,IAAIhB,OAAO,CAAC9C,KAAK,IAAI8C,OAAO,CAACC,OAAO,EAAE;UAClE;UACAF,iBAAiB,CAACC,OAAO,CAAC9C,KAAK,EAAEgD,IAAI,CAACkJ,SAAS,CAACpJ,OAAO,CAACC,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAO3J,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAED0S,EAAE,CAACK,OAAO,GAAI/S,KAAK,IAAK;MACtBtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAED0S,EAAE,CAACM,OAAO,GAAG,MAAM;MACjBtU,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACAiS,UAAU,CAAC4B,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACAvP,aAAa,CAAC6D,OAAO,GAAG4L,EAAE;EAC5B,CAAC;EAEDtX,SAAS,CAAC,MAAM;IACd,IAAI,CAACoH,YAAY,CAACsE,OAAO,EAAE;;IAE3B;IACAmM,aAAa,CAAC,CAAC;;IAEf;IACA9V,KAAK,GAAG,IAAI3B,KAAK,CAAC0X,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAI3X,KAAK,CAAC4X,iBAAiB,CACxC,EAAE,EACFtV,MAAM,CAACuV,UAAU,GAAGvV,MAAM,CAACwV,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAAC/O,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCoS,MAAM,CAACpL,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBrF,SAAS,CAACoE,OAAO,GAAGqM,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAI/X,KAAK,CAACgY,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAAC5V,MAAM,CAACuV,UAAU,EAAEvV,MAAM,CAACwV,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAAC9V,MAAM,CAAC+V,gBAAgB,CAAC;IAC/CrR,YAAY,CAACsE,OAAO,CAACgN,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAIxY,KAAK,CAACyY,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5D9W,KAAK,CAACsO,GAAG,CAACuI,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAI1Y,KAAK,CAAC2Y,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAAC9P,QAAQ,CAACrD,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1C5D,KAAK,CAACsO,GAAG,CAACyI,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAI5Y,KAAK,CAAC2Y,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAAChQ,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3C5D,KAAK,CAACsO,GAAG,CAAC2I,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAI7Y,KAAK,CAAC8Y,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAACjQ,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCsT,SAAS,CAACE,KAAK,GAAG3S,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7BwS,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAAClT,QAAQ,GAAG,GAAG;IACxBhE,KAAK,CAACsO,GAAG,CAAC4I,SAAS,CAAC;;IAEpB;IACAvX,QAAQ,GAAG,IAAIpB,aAAa,CAACyX,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzDjX,QAAQ,CAAC4X,aAAa,GAAG,IAAI;IAC7B5X,QAAQ,CAAC6X,aAAa,GAAG,IAAI;IAC7B7X,QAAQ,CAAC8X,kBAAkB,GAAG,KAAK;IACnC9X,QAAQ,CAACmL,WAAW,GAAG,EAAE;IACzBnL,QAAQ,CAACoL,WAAW,GAAG,GAAG;IAC1BpL,QAAQ,CAACqL,aAAa,GAAGvG,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC/E,QAAQ,CAACsL,aAAa,GAAG,CAAC;IAC1BtL,QAAQ,CAAC+K,MAAM,CAAC9G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BjE,QAAQ,CAACkL,MAAM,CAAC,CAAC;;IAEjB;IACAtJ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBwU,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBrW,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpB4F,SAAS,EAAE,CAAC,CAACA,SAAS,CAACoE;IACzB,CAAC,CAAC;;IAEF;IACA,MAAM+N,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAIxZ,UAAU,CAAC,CAAC;QACtCwZ,aAAa,CAACC,IAAI,CAChB,GAAG5W,QAAQ,uBAAuB,EACjC6W,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAAChY,KAAK;;UAE/B;UACA,MAAMkY,gBAAgB,GAAG,IAAI7Z,KAAK,CAAC8Z,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAAC5H,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAIpS,KAAK,CAAC+Z,oBAAoB,CAAC;kBACjDnP,KAAK,EAAE,QAAQ;kBAAO;kBACtBoP,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIjI,KAAK,CAACE,QAAQ,CAACpB,GAAG,EAAE;kBACtBqB,WAAW,CAACrB,GAAG,GAAGkB,KAAK,CAACE,QAAQ,CAACpB,GAAG;gBACtC;;gBAEA;gBACAkB,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5BlP,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE8O,KAAK,CAAC1E,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMqM,YAAY,CAACO,QAAQ,CAAC9J,MAAM,GAAG,CAAC,EAAE;YACtC,MAAM4B,KAAK,GAAG2H,YAAY,CAACO,QAAQ,CAAC,CAAC,CAAC;YACtCN,gBAAgB,CAAC5J,GAAG,CAACgC,KAAK,CAAC;UAC7B;;UAEA;UACAtQ,KAAK,CAACsO,GAAG,CAAC4J,gBAAgB,CAAC;;UAE3B;UACA/Y,gBAAgB,GAAG+Y,gBAAgB;UAEnC3W,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9BiX,kBAAkB,CAAC,IAAI,CAAC;UACxBb,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAQ,GAAG,IAAK;UACPnX,OAAO,CAACC,GAAG,CAAC,aAAa,CAACkX,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEzU,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACD0T,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMgB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA;;QAEA;QACAxD,cAAc,CAAC,CAAC;;QAEhB;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAEF,CAAC,CAAC,OAAOxS,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAMiW,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAIrB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMoB,WAAW,GAAIC,WAAW,IAAK;UACnC3X,OAAO,CAACC,GAAG,CAAC,WAAWuX,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAI7a,UAAU,CAAC,CAAC;UAC/B6a,MAAM,CAACpB,IAAI,CACTgB,GAAG,EACFf,IAAI,IAAK;YACRzW,OAAO,CAACC,GAAG,CAAC,WAAWuX,GAAG,EAAE,CAAC;YAC7BnB,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAU,GAAG,IAAK;YACPnX,OAAO,CAACC,GAAG,CAAC,SAAS,CAACkX,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEzU,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAtB,KAAK,IAAK;YACTtB,OAAO,CAACsB,KAAK,CAAC,SAASkW,GAAG,EAAE,EAAElW,KAAK,CAAC;YACpC,IAAIqW,WAAW,GAAG,CAAC,EAAE;cACnB3X,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3BiS,UAAU,CAAC,MAAMwF,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACLrB,MAAM,CAAChV,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAEDoW,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAI7a,UAAU,CAAC,CAAC;IAC/B6a,MAAM,CAACpB,IAAI,CACT,GAAG5W,QAAQ,4BAA4B,EACvC,MAAO6W,IAAI,IAAK;MACd,IAAI;QACF,MAAMhK,KAAK,GAAGgK,IAAI,CAAChY,KAAK;QACxBgO,KAAK,CAACK,KAAK,CAACzK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBoK,KAAK,CAAC/G,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAE3B;QACA,IAAI5D,KAAK,EAAE;UACXA,KAAK,CAACsO,GAAG,CAACN,KAAK,CAAC;;UAEhB;UACA,MAAM6K,eAAe,CAAC,CAAC;QACvB,CAAC,MAAM;UACLtX,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC;QAChC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACA6V,GAAG,IAAK;MACPnX,OAAO,CAACC,GAAG,CAAC,SAAS,CAACkX,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEzU,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAtB,KAAK,IAAK;MACTtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtB,OAAO,CAACsB,KAAK,CAAC,OAAO,EAAE;QACrBuW,IAAI,EAAEvW,KAAK,CAAC0K,IAAI;QAChB8L,IAAI,EAAExW,KAAK,CAAC0J,OAAO;QACnB+M,KAAK,EAAE,GAAGnY,QAAQ,4BAA4B;QAC9CoY,KAAK,EAAE,GAAGpY,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMqY,OAAO,GAAGA,CAAA,KAAM;MACpB9T,iBAAiB,CAACiE,OAAO,GAAG8P,qBAAqB,CAACD,OAAO,CAAC;;MAE1D;MACA/a,KAAK,CAACoM,MAAM,CAAC,CAAC;;MAEd;MACA,MAAM6O,SAAS,GAAG5X,KAAK,CAAC6H,OAAO,CAACgQ,QAAQ,CAAC,CAAC,CAAC,CAAC;;MAE5CrT,qBAAqB,CAACqD,OAAO,CAACwD,OAAO,CAAEoB,KAAK,IAAK;QAC/CA,KAAK,CAAC1D,MAAM,CAAC6O,SAAS,CAAC;MACzB,CAAC,CAAC;MAEF,IAAIha,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACiK,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMgQ,UAAU,GAAGza,gBAAgB,CAAC8H,QAAQ,CAAC5D,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMwW,eAAe,GAAG1a,gBAAgB,CAACgP,QAAQ,CAAC1K,CAAC;;QAEnD;QACA;QACA,MAAMqW,gBAAgB,GAAG,EAAED,eAAe,GAAGpV,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;;QAEnE;QACA,MAAMqV,YAAY,GAAG,IAAI1b,KAAK,CAACgG,OAAO,CACpC,CAAC,EAAE,GAAGI,IAAI,CAACuV,GAAG,CAACF,gBAAgB,CAAC,EAChC,GAAG,EACH,CAAC,EAAE,GAAGrV,IAAI,CAACwV,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA,MAAMI,oBAAoB,GAAGN,UAAU,CAACvW,KAAK,CAAC,CAAC,CAACiL,GAAG,CAACyL,YAAY,CAAC;QACjE,MAAMI,YAAY,GAAGP,UAAU,CAACvW,KAAK,CAAC,CAAC;;QAEvC;QACA,IAAI,CAAC2C,kBAAkB,CAAC2D,OAAO,EAAE;UAC/B3D,kBAAkB,CAAC2D,OAAO,GAAGuQ,oBAAoB,CAAC7W,KAAK,CAAC,CAAC;QAC3D;QAEA,IAAI,CAAC4C,gBAAgB,CAAC0D,OAAO,EAAE;UAC7B1D,gBAAgB,CAAC0D,OAAO,GAAGwQ,YAAY,CAAC9W,KAAK,CAAC,CAAC;QACjD;;QAEA;QACA2C,kBAAkB,CAAC2D,OAAO,CAACyQ,IAAI,CAACF,oBAAoB,EAAE,CAAC,GAAGhU,eAAe,CAAC;QAC1ED,gBAAgB,CAAC0D,OAAO,CAACyQ,IAAI,CAACD,YAAY,EAAE,CAAC,GAAGjU,eAAe,CAAC;;QAEhE;QACA8P,MAAM,CAAC/O,QAAQ,CAACuD,IAAI,CAACxE,kBAAkB,CAAC2D,OAAO,CAAC;;QAEhD;QACAqM,MAAM,CAAChM,EAAE,CAACpG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACAoS,MAAM,CAACpL,MAAM,CAAC3E,gBAAgB,CAAC0D,OAAO,CAAC;;QAEvC;QACAqM,MAAM,CAACqE,sBAAsB,CAAC,CAAC;QAC/BrE,MAAM,CAAC9K,YAAY,CAAC,CAAC;QACrB8K,MAAM,CAAC7K,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACAxL,QAAQ,CAACiK,OAAO,GAAG,KAAK;;QAExB;QACAjK,QAAQ,CAAC+K,MAAM,CAACF,IAAI,CAACvE,gBAAgB,CAAC0D,OAAO,CAAC;QAC9ChK,QAAQ,CAACkL,MAAM,CAAC,CAAC;QAEjBtJ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnB8Y,IAAI,EAAEV,UAAU,CAACxN,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAE6J,MAAM,CAAC/O,QAAQ,CAACmF,OAAO,CAAC,CAAC;UAC/BmO,IAAI,EAAEtU,gBAAgB,CAAC0D,OAAO,CAACyC,OAAO,CAAC,CAAC;UACxCoO,IAAI,EAAExE,MAAM,CAACyE,iBAAiB,CAAC,IAAIpc,KAAK,CAACgG,OAAO,CAAC,CAAC,CAAC,CAAC+H,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI1M,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAsG,kBAAkB,CAAC2D,OAAO,GAAG,IAAI;QACjC1D,gBAAgB,CAAC0D,OAAO,GAAG,IAAI;;QAE/B;QACAhK,QAAQ,CAACiK,OAAO,GAAG,IAAI;;QAEvB;QACAoM,MAAM,CAAChM,EAAE,CAACpG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAIa,IAAI,CAACK,GAAG,CAACkR,MAAM,CAAC/O,QAAQ,CAACxD,CAAC,CAAC,GAAG,EAAE,EAAE;UACpCuS,MAAM,CAAC/O,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BjE,QAAQ,CAAC+K,MAAM,CAAC9G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BoS,MAAM,CAACpL,MAAM,CAACjL,QAAQ,CAAC+K,MAAM,CAAC;UAC9B/K,QAAQ,CAACkL,MAAM,CAAC,CAAC;QACnB;;QAEA;QACA;QACAmL,MAAM,CAAC9K,YAAY,CAAC,CAAC;QACrB8K,MAAM,CAAC7K,iBAAiB,CAAC,IAAI,CAAC;MAEhC,CAAC,MAAM,IAAIzL,UAAU,KAAK,cAAc,EAAE;QACxC;QACAsG,kBAAkB,CAAC2D,OAAO,GAAG,IAAI;QACjC1D,gBAAgB,CAAC0D,OAAO,GAAG,IAAI;;QAE/B;QACAhK,QAAQ,CAACkL,MAAM,CAAC,CAAC;MACnB;MAEA,IAAIlL,QAAQ,EAAEA,QAAQ,CAACkL,MAAM,CAAC,CAAC;MAC/B,IAAI7K,KAAK,IAAIgW,MAAM,EAAE;QACnBI,QAAQ,CAACsE,MAAM,CAAC1a,KAAK,EAAEgW,MAAM,CAAC;MAChC;IACF,CAAC;IAEDwD,OAAO,CAAC,CAAC;;IAET;IACA,MAAMmB,YAAY,GAAGA,CAAA,KAAM;MACzB3E,MAAM,CAAC4E,MAAM,GAAGja,MAAM,CAACuV,UAAU,GAAGvV,MAAM,CAACwV,WAAW;MACtDH,MAAM,CAACqE,sBAAsB,CAAC,CAAC;MAC/BjE,QAAQ,CAACG,OAAO,CAAC5V,MAAM,CAACuV,UAAU,EAAEvV,MAAM,CAACwV,WAAW,CAAC;IACzD,CAAC;IACDxV,MAAM,CAACka,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACAha,MAAM,CAACma,aAAa,GAAG,MAAM;MAC3B,IAAIvV,SAAS,CAACoE,OAAO,EAAE;QACrBpE,SAAS,CAACoE,OAAO,CAAC1C,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC2B,SAAS,CAACoE,OAAO,CAACiB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCrF,SAAS,CAACoE,OAAO,CAACuB,YAAY,CAAC,CAAC;QAChC3F,SAAS,CAACoE,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAIxL,QAAQ,EAAE;UACZA,QAAQ,CAAC+K,MAAM,CAAC9G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BjE,QAAQ,CAACiK,OAAO,GAAG,IAAI;UACvBjK,QAAQ,CAACkL,MAAM,CAAC,CAAC;QACnB;QAEAnL,UAAU,GAAG,QAAQ;QACrB6B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAIkE,iBAAiB,CAACiE,OAAO,EAAE;QAC7BoR,oBAAoB,CAACrV,iBAAiB,CAACiE,OAAO,CAAC;QAC/CjE,iBAAiB,CAACiE,OAAO,GAAG,IAAI;MAClC;;MAEA;MACA,IAAIrK,oBAAoB,EAAE;QACxB0b,aAAa,CAAC1b,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;;MAEA;MACA,IAAIwG,aAAa,CAAC6D,OAAO,EAAE;QACzB7D,aAAa,CAAC6D,OAAO,CAACsR,KAAK,CAAC,CAAC;QAC7BnV,aAAa,CAAC6D,OAAO,GAAG,IAAI;MAC9B;;MAEA;MACAhJ,MAAM,CAACua,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;;MAElD;MACA,IAAIvE,QAAQ,IAAI/Q,YAAY,CAACsE,OAAO,EAAE;QACpCtE,YAAY,CAACsE,OAAO,CAACwR,WAAW,CAAC/E,QAAQ,CAACQ,UAAU,CAAC;QACrDR,QAAQ,CAACzE,OAAO,CAAC,CAAC;MACpB;;MAEA;MACA,IAAIlQ,aAAa,EAAE;QACjBA,aAAa,CAAC0L,OAAO,CAAC,CAACmC,SAAS,EAAEjC,EAAE,KAAK;UACvC,IAAIiC,SAAS,CAACtB,KAAK,IAAIhO,KAAK,EAAE;YAC5BA,KAAK,CAACuP,MAAM,CAACD,SAAS,CAACtB,KAAK,CAAC;UAC/B;QACF,CAAC,CAAC;QACFvM,aAAa,CAAC2Z,KAAK,CAAC,CAAC;MACvB;;MAEA;MACAxZ,gBAAgB,CAACuL,OAAO,CAAEkO,QAAQ,IAAK;QACrC,IAAIrb,KAAK,IAAIqb,QAAQ,CAACrN,KAAK,EAAE;UAC3BhO,KAAK,CAACuP,MAAM,CAAC8L,QAAQ,CAACrN,KAAK,CAAC;QAC9B;MACF,CAAC,CAAC;MACFpM,gBAAgB,CAACwZ,KAAK,CAAC,CAAC;MACxBvZ,kBAAkB,CAACuZ,KAAK,CAAC,CAAC;;MAE1B;;MAEA;MACApb,KAAK,GAAG,IAAI;MACZL,QAAQ,GAAG,IAAI;MACfC,qBAAqB,GAAG,IAAI;MAC5BC,qBAAqB,GAAG,IAAI;MAC5BC,oBAAoB,GAAG,IAAI;MAC3BC,0BAA0B,GAAG,IAAI;MACjCZ,gBAAgB,GAAG,IAAI;MAEvBoC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAvD,SAAS,CAAC,MAAM;IACd;IACA+D,qBAAqB,CAAC,CAAC;;IAEvB;IACA,MAAMsZ,uBAAuB,GAAGA,CAAA,KAAM;MACpC/Z,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCQ,qBAAqB,CAAC,CAAC;IACzB,CAAC;;IAED;IACArB,MAAM,CAACka,gBAAgB,CAAC,oBAAoB,EAAES,uBAAuB,CAAC;;IAEtE;IACA,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnCxZ,qBAAqB,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC;;IAET;IACA,OAAO,MAAM;MACXrB,MAAM,CAACua,mBAAmB,CAAC,oBAAoB,EAAEI,uBAAuB,CAAC;MACzEN,aAAa,CAACO,UAAU,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAtd,SAAS,CAAC,MAAM;IACd;IACA,IAAI+B,KAAK,IAAI2F,SAAS,CAACgE,OAAO,EAAE;MAC9BpI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA,MAAMia,KAAK,GAAGhI,UAAU,CAAC,MAAM;QAC7B,IAAIzT,KAAK,IAAI2F,SAAS,CAACgE,OAAO,EAAE;UAAG;UACjC+R,mBAAmB,CAAC/V,SAAS,CAACgE,OAAO,CAAC;QACxC;MACF,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAMgS,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM;MACLla,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC,EAAE,CAACxB,KAAK,CAAC,CAAC;;EAEX;EACA/B,SAAS,CAAC,MAAM;IACd,IAAIoH,YAAY,CAACsE,OAAO,EAAE;MACxB;MACA,MAAMiS,WAAW,GAAI7H,KAAK,IAAK;QAC7B,IAAI/T,KAAK,IAAIuF,SAAS,CAACoE,OAAO,EAAE;UAC9BpI,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEuS,KAAK,CAAC8H,OAAO,EAAE9H,KAAK,CAAC+H,OAAO,CAAC;UACpDC,gBAAgB,CAAChI,KAAK,EAAE1O,YAAY,CAACsE,OAAO,EAAE3J,KAAK,EAAEuF,SAAS,CAACoE,OAAO,EAAEvB,sBAAsB,CAAC;QACjG,CAAC,MAAM;UACL7G,OAAO,CAACya,IAAI,CAAC,4BAA4B,CAAC;QAC5C;MACF,CAAC;;MAED;MACA3W,YAAY,CAACsE,OAAO,CAACkR,gBAAgB,CAAC,OAAO,EAAEe,WAAW,CAAC;;MAE3D;MACAra,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC6D,YAAY,CAACsE,OAAO,CAAC;;MAEpD;MACA,OAAO,MAAM;QACX,IAAItE,YAAY,CAACsE,OAAO,EAAE;UACxBtE,YAAY,CAACsE,OAAO,CAACuR,mBAAmB,CAAC,OAAO,EAAEU,WAAW,CAAC;UAC9Dra,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACxB,KAAK,EAAEuF,SAAS,CAACoE,OAAO,CAAC,CAAC;;EAE9B;EACA,MAAMsS,SAAS,GAAG7d,WAAW,CAAC,MAAM;IAClCmD,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B;EACF,CAAC,EAAE,CAAC6D,YAAY,EAAEgE,aAAa,EAAEzH,gBAAgB,CAAC,CAAC;;EAEnD;EACA,MAAMsa,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMjK,QAAQ,GAAG,IAAI5T,KAAK,CAAC8d,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChD,MAAM3L,QAAQ,GAAG,IAAInS,KAAK,CAAC+d,iBAAiB,CAAC;MAAEnT,KAAK,EAAE;IAAS,CAAC,CAAC;IACjE,MAAMiK,iBAAiB,GAAG,IAAI7U,KAAK,CAACge,IAAI,CAACpK,QAAQ,EAAEzB,QAAQ,CAAC;;IAE5D;IACA,MAAM8L,YAAY,GAAG,IAAIje,KAAK,CAACke,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC5D,MAAMC,YAAY,GAAG,IAAIne,KAAK,CAAC+d,iBAAiB,CAAC;MAAEnT,KAAK,EAAE;IAAS,CAAC,CAAC;IACrE,MAAMwT,SAAS,GAAG,IAAIpe,KAAK,CAACge,IAAI,CAACC,YAAY,EAAEE,YAAY,CAAC;IAC5DC,SAAS,CAACxV,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAClCsP,iBAAiB,CAAC5E,GAAG,CAACmO,SAAS,CAAC;IAEhC,OAAOvJ,iBAAiB;EAC1B,CAAC;;EAED;EACA,MAAMwJ,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAC1c,KAAK,EAAE;;IAEZ;IACA4B,gBAAgB,CAACuL,OAAO,CAAC,CAACkO,QAAQ,EAAE/S,OAAO,KAAK;MAC9C,IAAI+S,QAAQ,CAACrN,KAAK,EAAE;QAClB;QACA,MAAM2O,cAAc,GAAG,IAAIte,KAAK,CAACue,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxD,MAAMC,cAAc,GAAG,IAAIxe,KAAK,CAAC+d,iBAAiB,CAAC;UACjDnT,KAAK,EAAE,QAAQ;UAAC;UAChB2H,WAAW,EAAE,KAAK;UAClBW,OAAO,EAAE,GAAG;UAAG;UACfuL,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,MAAMC,UAAU,GAAG,IAAI1e,KAAK,CAACge,IAAI,CAACM,cAAc,EAAEE,cAAc,CAAC;QACjEE,UAAU,CAAC9V,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE;;QAEnC;QACAmZ,UAAU,CAACC,QAAQ,GAAG;UACpBzP,IAAI,EAAE,cAAc;UACpBjF,OAAO,EAAEA,OAAO;UAChBsD,IAAI,EAAEyP,QAAQ,CAAC5P,YAAY,CAACG,IAAI;UAChCqR,aAAa,EAAE;QACjB,CAAC;;QAED;QACA5B,QAAQ,CAACrN,KAAK,CAACM,GAAG,CAACyO,UAAU,CAAC;QAE9Bxb,OAAO,CAACC,GAAG,CAAC,OAAO6Z,QAAQ,CAAC5P,YAAY,CAACG,IAAI,KAAKtD,OAAO,aAAa,CAAC;MACzE;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACArK,SAAS,CAAC,MAAM;IACd;IACA,MAAMwd,KAAK,GAAGhI,UAAU,CAAC,MAAM;MAC7B,IAAI7R,gBAAgB,CAACsb,IAAI,GAAG,CAAC,EAAE;QAC7B3b,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1B;MACF;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAE;;IAEX,OAAO,MAAMma,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACAxd,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACA,IAAIyK,0BAA0B,CAACiB,OAAO,EAAE;QACtCqR,aAAa,CAACtS,0BAA0B,CAACiB,OAAO,CAAC;QACjDjB,0BAA0B,CAACiB,OAAO,GAAG,IAAI;QACzCpI,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC9B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAvD,SAAS,CAAC,MAAM;IACd,IAAI,CAACkK,mBAAmB,CAACE,OAAO,IAAIK,0BAA0B,CAACiB,OAAO,EAAE;MACtEqR,aAAa,CAACtS,0BAA0B,CAACiB,OAAO,CAAC;MACjDjB,0BAA0B,CAACiB,OAAO,GAAG,IAAI;MACzClB,mBAAmB,CAACkB,OAAO,GAAG,IAAI;MAClCpI,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACnC;EACF,CAAC,EAAE,CAAC2G,mBAAmB,CAACE,OAAO,CAAC,CAAC;;EAEjC;EACApK,SAAS,CAAC,MAAM;IACd;IACA,IAAIa,iBAAiB,IAAIA,iBAAiB,CAAC4M,aAAa,IAAI5M,iBAAiB,CAAC4M,aAAa,CAACgD,MAAM,GAAG,CAAC,EAAE;MACtG;MACA,IAAI,CAACzG,oBAAoB,EAAE;QACzB,MAAMkV,iBAAiB,GAAGre,iBAAiB,CAAC4M,aAAa,CAAC,CAAC,CAAC;QAC5DnK,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE2b,iBAAiB,CAACvR,IAAI,CAAC;;QAEjD;QACA,MAAM6P,KAAK,GAAGhI,UAAU,CAAC,MAAM;UAC7BlI,wBAAwB,CAAC4R,iBAAiB,CAACvR,IAAI,CAAC;QAClD,CAAC,EAAE,IAAI,CAAC;QAER,OAAO,MAAM+P,YAAY,CAACF,KAAK,CAAC;MAClC;IACF;EACF,CAAC,EAAE,CAAC3c,iBAAiB,EAAEmJ,oBAAoB,CAAC,CAAC;EAE7C,oBACEjJ,OAAA,CAAAE,SAAA;IAAAsZ,QAAA,gBACExZ,OAAA;MAAMoe,KAAK,EAAErU,UAAW;MAAAyP,QAAA,EAAC;IAAK;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrCxe,OAAA,CAACJ,MAAM;MACLwe,KAAK,EAAExU,uBAAwB;MAC/B6U,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAEnS,wBAAyB;MACnCoS,OAAO,EAAE7e,iBAAiB,CAAC4M,aAAa,CAAC0D,GAAG,CAAC3D,YAAY,KAAK;QAC5DD,KAAK,EAAEC,YAAY,CAACG,IAAI;QACxBgS,KAAK,EAAEnS,YAAY,CAACG;MACtB,CAAC,CAAC,CAAE;MACJsR,IAAI,EAAC,OAAO;MACZW,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACbzW,MAAM,EAAE,IAAI;QACZ0W,SAAS,EAAE;MACb,CAAE;MACFvS,KAAK,EAAEvD,oBAAoB,GAAGA,oBAAoB,CAAC2D,IAAI,GAAGiG;IAAU;MAAAwL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eACFxe,OAAA;MAAKgf,GAAG,EAAE3Y,YAAa;MAAC+X,KAAK,EAAE;QAAEtU,KAAK,EAAE,MAAM;QAAEoF,MAAM,EAAE;MAAO;IAAE;MAAAmP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGnErV,mBAAmB,CAACE,OAAO,iBAC1BrJ,OAAA;MACEoe,KAAK,EAAE;QACLnW,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGgB,mBAAmB,CAAClB,QAAQ,CAAC1D,CAAC,IAAI;QAC3CsF,GAAG,EAAE,GAAGV,mBAAmB,CAAClB,QAAQ,CAACxD,CAAC,IAAI;QAC1C2D,SAAS,EAAE,wBAAwB;QACnCC,MAAM,EAAE,IAAI;QACZK,eAAe,EAAE,qBAAqB;QACtCuB,KAAK,EAAE,OAAO;QACdrB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,8BAA8B;QACzCN,OAAO,EAAE,GAAG;QACZwW,QAAQ,EAAE,OAAO;QAAE;QACnBnW,QAAQ,EAAE,MAAM,CAAC;MACnB,CAAE;MAAA0Q,QAAA,GAEDrQ,mBAAmB,CAACI,OAAO,eAC5BvJ,OAAA;QACEoe,KAAK,EAAE;UACLnW,QAAQ,EAAE,UAAU;UACpB4B,GAAG,EAAE,KAAK;UACVqV,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,MAAM;UAClBxW,MAAM,EAAE,MAAM;UACdsB,KAAK,EAAE,OAAO;UACdnB,QAAQ,EAAE,MAAM;UAChBD,MAAM,EAAE,SAAS;UACjBJ,OAAO,EAAE;QACX,CAAE;QACF2W,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAACjW,sBAAsB,CAAE;QAAAoQ,QAAA,EAC3D;MAED;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAEDxe,OAAA;MAAKoe,KAAK,EAAEpW,oBAAqB;MAAAwR,QAAA,gBAC/BxZ,OAAA;QACEoe,KAAK,EAAE;UACL,GAAG5V,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EmC,KAAK,EAAEnC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFsX,OAAO,EAAE1U,kBAAmB;QAAA8O,QAAA,EAC7B;MAED;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxe,OAAA;QACEoe,KAAK,EAAE;UACL,GAAG5V,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EmC,KAAK,EAAEnC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFsX,OAAO,EAAEvU,kBAAmB;QAAA2O,QAAA,EAC7B;MAED;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAApY,EAAA,CAhvDMJ,WAAW;AAAAsZ,EAAA,GAAXtZ,WAAW;AAivDjB,SAAS+L,gBAAgBA,CAACwN,IAAI,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAMC,MAAM,GAAG;IACbC,QAAQ,EAAEF,UAAU,CAACE,QAAQ,IAAI,OAAO;IACxC5W,QAAQ,EAAE0W,UAAU,CAAC1W,QAAQ,IAAI,EAAE;IAAE;IACrCoB,UAAU,EAAEsV,UAAU,CAACtV,UAAU,IAAI,MAAM;IAC3CyV,eAAe,EAAEH,UAAU,CAACG,eAAe,IAAI,CAAC;IAChDC,WAAW,EAAEJ,UAAU,CAACI,WAAW,IAAI;MAAE3N,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAI,CAAC;IACnE1J,eAAe,EAAE8W,UAAU,CAAC9W,eAAe,IAAI;MAAEuJ,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAI,CAAC;IACjFC,SAAS,EAAEmN,UAAU,CAACnN,SAAS,IAAI;MAAEJ,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAI,CAAC;IAC/D3J,OAAO,EAAE+W,UAAU,CAAC/W,OAAO,IAAI;EACjC,CAAC;;EAED;EACA,MAAMoX,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;EAEvC;EACAD,OAAO,CAACE,IAAI,GAAG,GAAGT,MAAM,CAACvV,UAAU,IAAIuV,MAAM,CAAC3W,QAAQ,MAAM2W,MAAM,CAACC,QAAQ,EAAE;;EAE7E;EACA,MAAMS,SAAS,GAAGH,OAAO,CAACI,WAAW,CAACb,IAAI,CAAC,CAACzV,KAAK;;EAEjD;EACA,MAAMA,KAAK,GAAGqW,SAAS,GAAG,CAAC,GAAGV,MAAM,CAAChX,OAAO,GAAG,CAAC,GAAGgX,MAAM,CAACE,eAAe;EACzE,MAAMzQ,MAAM,GAAGuQ,MAAM,CAAC3W,QAAQ,GAAG,CAAC,GAAG2W,MAAM,CAAChX,OAAO,GAAG,CAAC,GAAGgX,MAAM,CAACE,eAAe;EAEhFE,MAAM,CAAC/V,KAAK,GAAGA,KAAK;EACpB+V,MAAM,CAAC3Q,MAAM,GAAGA,MAAM;;EAEtB;EACA8Q,OAAO,CAACE,IAAI,GAAG,GAAGT,MAAM,CAACvV,UAAU,IAAIuV,MAAM,CAAC3W,QAAQ,MAAM2W,MAAM,CAACC,QAAQ,EAAE;EAC7EM,OAAO,CAACK,YAAY,GAAG,QAAQ;;EAE/B;EACA,MAAMC,MAAM,GAAG,CAAC;EAChBN,OAAO,CAACO,SAAS,CAAC,CAAC;EACnBP,OAAO,CAACQ,MAAM,CAACf,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,CAAC;EACvEK,OAAO,CAACS,MAAM,CAAC3W,KAAK,GAAG2V,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,CAAC;EAC/EK,OAAO,CAACU,KAAK,CAAC5W,KAAK,GAAG2V,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAE7V,KAAK,GAAG2V,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEA,MAAM,CAAC;EAC9IN,OAAO,CAACS,MAAM,CAAC3W,KAAK,GAAG2V,MAAM,CAACE,eAAe,EAAEzQ,MAAM,GAAGuQ,MAAM,CAACE,eAAe,GAAGW,MAAM,CAAC;EACxFN,OAAO,CAACU,KAAK,CAAC5W,KAAK,GAAG2V,MAAM,CAACE,eAAe,EAAEzQ,MAAM,GAAGuQ,MAAM,CAACE,eAAe,EAAE7V,KAAK,GAAG2V,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEpR,MAAM,GAAGuQ,MAAM,CAACE,eAAe,EAAEW,MAAM,CAAC;EAChKN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEpR,MAAM,GAAGuQ,MAAM,CAACE,eAAe,CAAC;EAChFK,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,EAAEzQ,MAAM,GAAGuQ,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAEzQ,MAAM,GAAGuQ,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEA,MAAM,CAAC;EAChJN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,CAAC;EACvEN,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,EAAEW,MAAM,CAAC;EAC9HN,OAAO,CAACW,SAAS,CAAC,CAAC;;EAEnB;EACAX,OAAO,CAACY,WAAW,GAAG,QAAQnB,MAAM,CAACG,WAAW,CAAC3N,CAAC,KAAKwN,MAAM,CAACG,WAAW,CAAC1N,CAAC,KAAKuN,MAAM,CAACG,WAAW,CAACzN,CAAC,KAAKsN,MAAM,CAACG,WAAW,CAACxN,CAAC,GAAG;EAChI4N,OAAO,CAACa,SAAS,GAAGpB,MAAM,CAACE,eAAe;EAC1CK,OAAO,CAACc,MAAM,CAAC,CAAC;;EAEhB;EACAd,OAAO,CAACe,SAAS,GAAG,QAAQtB,MAAM,CAAC/W,eAAe,CAACuJ,CAAC,KAAKwN,MAAM,CAAC/W,eAAe,CAACwJ,CAAC,KAAKuN,MAAM,CAAC/W,eAAe,CAACyJ,CAAC,KAAKsN,MAAM,CAAC/W,eAAe,CAAC0J,CAAC,GAAG;EAC9I4N,OAAO,CAACgB,IAAI,CAAC,CAAC;;EAEd;EACAhB,OAAO,CAACe,SAAS,GAAG,QAAQtB,MAAM,CAACpN,SAAS,CAACJ,CAAC,KAAKwN,MAAM,CAACpN,SAAS,CAACH,CAAC,KAAKuN,MAAM,CAACpN,SAAS,CAACF,CAAC,KAAKsN,MAAM,CAACpN,SAAS,CAACD,CAAC,GAAG;EACtH4N,OAAO,CAACiB,SAAS,GAAG,QAAQ;;EAE5B;EACAjB,OAAO,CAACkB,QAAQ,CAAC3B,IAAI,EAAEzV,KAAK,GAAG,CAAC,EAAEoF,MAAM,GAAG,CAAC,CAAC;;EAE7C;EACA,MAAMiS,OAAO,GAAG,IAAI9hB,KAAK,CAAC+hB,aAAa,CAACvB,MAAM,CAAC;EAC/CsB,OAAO,CAACE,SAAS,GAAGhiB,KAAK,CAACiiB,YAAY;EACtCH,OAAO,CAACtP,WAAW,GAAG,IAAI;;EAE1B;EACA,MAAM0P,cAAc,GAAG,IAAIliB,KAAK,CAACmiB,cAAc,CAAC;IAC9CpR,GAAG,EAAE+Q,OAAO;IACZvP,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM6P,MAAM,GAAG,IAAIpiB,KAAK,CAACqiB,MAAM,CAACH,cAAc,CAAC;EAC/CE,MAAM,CAACpS,KAAK,CAACzK,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EAC7B6c,MAAM,CAACjQ,QAAQ,CAACmQ,SAAS,GAAG,KAAK,CAAC,CAAC;;EAEnC;EACAF,MAAM,CAACzD,QAAQ,GAAG;IAChBuB,IAAI,EAAEA,IAAI;IACVE,MAAM,EAAEA;EACV,CAAC;EAED,OAAOgC,MAAM;AACf;;AAIA;AACA9f,MAAM,CAACigB,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAM5K,MAAM,GAAG8I,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAI/K,MAAM,EAAE;MACV;MACA,MAAMgL,MAAM,GAAGhL,MAAM,CAAC/O,QAAQ,CAAC5D,KAAK,CAAC,CAAC;;MAEtC;MACA2S,MAAM,CAAC/O,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BoS,MAAM,CAAChM,EAAE,CAACpG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBoS,MAAM,CAACpL,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAoL,MAAM,CAAC9K,YAAY,CAAC,CAAC;MACrB8K,MAAM,CAAC7K,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMxL,QAAQ,GAAGmf,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIthB,QAAQ,EAAE;QACZA,QAAQ,CAAC+K,MAAM,CAAC9G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BjE,QAAQ,CAACkL,MAAM,CAAC,CAAC;MACnB;MAEAtJ,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxB0f,GAAG,EAAEF,MAAM,CAAC5U,OAAO,CAAC,CAAC;QACrB+U,GAAG,EAAEnL,MAAM,CAAC/O,QAAQ,CAACmF,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOgV,CAAC,EAAE;IACV7f,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEue,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAMtL,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACFvU,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAM2X,MAAM,GAAG,IAAI7a,UAAU,CAAC,CAAC;;IAE/B;IACA,IAAI;MACF,MAAM,CAAE+iB,gBAAgB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,UAAU,CAAC,GAAG,MAAM7J,OAAO,CAAC8J,GAAG,CAAC,CAClFtI,MAAM,CAACuI,SAAS,CAAC,GAAGvgB,QAAQ,4BAA4B,CAAC,EAC3DgY,MAAM,CAACuI,SAAS,CAAC,GAAGvgB,QAAQ,uBAAuB,CAAC,EACpDgY,MAAM,CAACuI,SAAS,CAAC,GAAGvgB,QAAQ,uBAAuB,CAAC,EAClDgY,MAAM,CAACuI,SAAS,CAAC,GAAGvgB,QAAQ,sBAAsB,CAAC,CAEtD,CAAC;;MAEF;MACAI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB5B,qBAAqB,GAAG0hB,WAAW,CAACthB,KAAK;MACzCJ,qBAAqB,CAACyQ,QAAQ,CAAEC,KAAK,IAAK;QACxC,IAAIA,KAAK,CAACC,MAAM,EAAE;UACd,MAAME,WAAW,GAAG,IAAIpS,KAAK,CAAC+Z,oBAAoB,CAAC;YACnDnP,KAAK,EAAE,QAAQ;YAAG;YAClBoP,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,GAAG;YACdC,eAAe,EAAE;UACnB,CAAC,CAAC;;UAEA;UACA,IAAIjI,KAAK,CAACE,QAAQ,CAACpB,GAAG,EAAE;YACtBqB,WAAW,CAACrB,GAAG,GAAGkB,KAAK,CAACE,QAAQ,CAACpB,GAAG;UACtC;UACAkB,KAAK,CAACqR,OAAO,GAAGlR,WAAW;QAC/B;MACF,CAAC,CAAC;MAEFlP,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B;MACA3B,qBAAqB,GAAG0hB,WAAW,CAACvhB,KAAK;MACzC;MACAH,qBAAqB,CAACwO,KAAK,CAACzK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxC;MACA/D,qBAAqB,CAACwQ,QAAQ,CAAEC,KAAK,IAAK;QACxC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAAC6H,SAAS,GAAG,GAAG;UAC9B/H,KAAK,CAACE,QAAQ,CAAC8H,SAAS,GAAG,GAAG;UAC9BhI,KAAK,CAACE,QAAQ,CAAC+H,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;MAEFhX,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA1B,oBAAoB,GAAG0hB,UAAU,CAACxhB,KAAK;MACvC;MACA;MACA;MACAF,oBAAoB,CAACuQ,QAAQ,CAAEC,KAAK,IAAK;QACvC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAAC6H,SAAS,GAAG,GAAG;UAC9B/H,KAAK,CAACE,QAAQ,CAAC8H,SAAS,GAAG,GAAG;UAC9BhI,KAAK,CAACE,QAAQ,CAAC+H,eAAe,GAAG,GAAG;QAEtC;QACA,IAAIjI,KAAK,CAACC,MAAM,EAAC;UACfD,KAAK,CAACsR,UAAU,GAAG,IAAI;QACzB;MACF,CAAC,CAAC;;MAIF;MACArgB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEggB,UAAU,CAAC/S,UAAU,CAACC,MAAM,EAAE,GAAG,CAAC;MAC5D,IAAI8S,UAAU,CAAC/S,UAAU,IAAI+S,UAAU,CAAC/S,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7DnN,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEggB,UAAU,CAAC/S,UAAU,CAACC,MAAM,EAAE,GAAG,CAAC;QACzDzO,eAAe,GAAGuhB,UAAU;MAC9B,CAAC,MAAM;QACLjgB,OAAO,CAACya,IAAI,CAAC,cAAc,CAAC;MAC9B;MAEAza,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;;MAEzB;MACAzB,0BAA0B,GAAGshB,gBAAgB,CAACrhB,KAAK;MACnDuB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEzB,0BAA0B,CAAC;MACjD;MACAA,0BAA0B,CAACsO,KAAK,CAACzK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C;MACA7D,0BAA0B,CAACsQ,QAAQ,CAAEC,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAAC6H,SAAS,GAAG,GAAG;UAC9B/H,KAAK,CAACE,QAAQ,CAAC8H,SAAS,GAAG,GAAG;UAC9BhI,KAAK,CAACE,QAAQ,CAAC+H,eAAe,GAAG,GAAG;QACxC;MACF,CAAC,CAAC;MAEAhX,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACA,IAAI;QACF,IAAI,CAACjD,qBAAqB,EAAE;UAC1B,MAAM0hB,WAAW,GAAG,MAAMnI,MAAM,CAACuI,SAAS,CAAC,GAAGvgB,QAAQ,uBAAuB,CAAC;UAC9EvB,qBAAqB,GAAG0hB,WAAW,CAACthB,KAAK;QAC3C;;QAEA;QACA,IAAI,CAACD,0BAA0B,EAAE;UAC/BwB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC7B,MAAM6f,gBAAgB,GAAG,MAAMlI,MAAM,CAACuI,SAAS,CAAC,GAAGvgB,QAAQ,4BAA4B,CAAC;UACxFpB,0BAA0B,GAAGshB,gBAAgB,CAACrhB,KAAK;UACnDD,0BAA0B,CAACsO,KAAK,CAACzK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC7CrC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOqgB,GAAG,EAAE;QACZtgB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEgf,GAAG,CAAC;MAClC;IACF;EACF,CAAC,CAAC,OAAOhf,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;;AAED;AACA,MAAMuS,mBAAmB,GAAI7H,IAAI,IAAK;EACpC,MAAMuU,KAAK,GAAG;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,KAAK,CAACvU,IAAI,CAAC,IAAI,MAAM;AAC9B,CAAC;;AAED;AACA,MAAMmH,iBAAiB,GAAGA,CAACzN,QAAQ,EAAEsX,IAAI,EAAEtV,KAAK,KAAK;EACnD;EACA,IAAI,CAACjJ,KAAK,EAAE;IACVuB,OAAO,CAACya,IAAI,CAAC,oBAAoB,CAAC;IAClC;EACF;EAEA,IAAI;IACJ;IACA,MAAMyE,MAAM,GAAG1P,gBAAgB,CAACwN,IAAI,CAAC;IACrCkC,MAAM,CAACxZ,QAAQ,CAACrD,GAAG,CAACqD,QAAQ,CAAC1D,CAAC,EAAE,EAAE,EAAE,CAAC0D,QAAQ,CAACxD,CAAC,CAAC,CAAC,CAAE;;IAEnD;IACAgQ,UAAU,CAAC,MAAM;MACb;MACA,IAAIzT,KAAK,IAAIygB,MAAM,CAACsB,MAAM,EAAE;QAC9B/hB,KAAK,CAACuP,MAAM,CAACkR,MAAM,CAAC;MAClB;IACJ,CAAC,EAAE,GAAG,CAAC;;IAEP;IACAzgB,KAAK,CAACsO,GAAG,CAACmS,MAAM,CAAC;;IAEjB;IACA;IACA;IACA;IACA;EACA,CAAC,CAAC,OAAO5d,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;AACF,CAAC;;AAED;AACA,MAAM6Y,mBAAmB,GAAIsG,iBAAiB,IAAK;EACjD,IAAI,CAAChiB,KAAK,EAAE;IACVuB,OAAO,CAACsB,KAAK,CAAC,gBAAgB,CAAC;IAC/B;EACF;EAEA,IAAI,CAACmf,iBAAiB,EAAE;IACtBzgB,OAAO,CAACsB,KAAK,CAAC,mBAAmB,CAAC;IAClC;EACF;EAEA,IAAI,CAAC9C,0BAA0B,EAAE;IAC/BwB,OAAO,CAACsB,KAAK,CAAC,UAAU,CAAC;IACzB;IACAof,2BAA2B,CAACD,iBAAiB,CAAC;IAC9C;EACF;;EAEA;EACApgB,gBAAgB,CAACuL,OAAO,CAAEkO,QAAQ,IAAK;IACrC,IAAIrb,KAAK,IAAIqb,QAAQ,CAACrN,KAAK,EAAE;MAC3BhO,KAAK,CAACuP,MAAM,CAAC8L,QAAQ,CAACrN,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACFpM,gBAAgB,CAACwZ,KAAK,CAAC,CAAC;;EAExB;EACAtc,iBAAiB,CAAC4M,aAAa,CAACyB,OAAO,CAAC1B,YAAY,IAAI;IACtD;IACA,IAAIA,YAAY,CAACyW,eAAe,KAAK,KAAK,EAAE;MAC1C3gB,OAAO,CAACC,GAAG,CAAC,UAAUiK,YAAY,CAACG,IAAI,kBAAkB,CAAC;MAC1D;IACF;;IAEA;IACA,IAAIH,YAAY,CAAC9E,QAAQ,IAAI8E,YAAY,CAAC/E,SAAS,IAAI+E,YAAY,CAACnD,OAAO,EAAE;MAC3E;MACA,MAAMwF,QAAQ,GAAGkU,iBAAiB,CAAClW,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAC/E,SAAS,CAAC,EAClCqF,UAAU,CAACN,YAAY,CAAC9E,QAAQ,CAClC,CAAC;MAEDpF,OAAO,CAACC,GAAG,CAAC,SAASiK,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,iBAAiBwF,QAAQ,CAACvK,CAAC,KAAKuK,QAAQ,CAACrK,CAAC,GAAG,CAAC;MAE7G,IAAI;QACF;QACA,MAAMyP,iBAAiB,GAAGnT,0BAA0B,CAACsD,KAAK,CAAC,CAAC;;QAE5D;QACA6P,iBAAiB,CAACtH,IAAI,GAAG,OAAOH,YAAY,CAACG,IAAI,EAAE;;QAEnD;QACAsH,iBAAiB,CAACjM,QAAQ,CAACrD,GAAG,CAACkK,QAAQ,CAACvK,CAAC,EAAE,EAAE,EAAE,CAACuK,QAAQ,CAACrK,CAAC,CAAC;;QAE3D;QACAyP,iBAAiB,CAAC7E,KAAK,CAACzK,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;QAEvC;QACAsP,iBAAiB,CAAC5B,WAAW,GAAG,GAAG;;QAEnC;QACA4B,iBAAiB,CAAC7C,QAAQ,CAACC,KAAK,IAAI;UAClC;UACA,IAAIA,KAAK,CAACC,MAAM,EAAE;YAChB;YACAD,KAAK,CAACE,QAAQ,CAACI,WAAW,GAAG,KAAK;YAClCN,KAAK,CAACE,QAAQ,CAACe,OAAO,GAAG,GAAG;YAC5BjB,KAAK,CAACE,QAAQ,CAAC2R,IAAI,GAAG9jB,KAAK,CAAC+jB,UAAU;YACtC9R,KAAK,CAACE,QAAQ,CAACsM,UAAU,GAAG,IAAI;YAChCxM,KAAK,CAACE,QAAQ,CAACmQ,SAAS,GAAG,IAAI;YAC/BrQ,KAAK,CAACE,QAAQ,CAACK,WAAW,GAAG,IAAI;;YAEjC;YACAP,KAAK,CAACgB,WAAW,GAAG,GAAG;UACzB;QACF,CAAC,CAAC;;QAEF;QACA4B,iBAAiB,CAAC8J,QAAQ,GAAG;UAC3BzP,IAAI,EAAE,cAAc;UACpBjF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;UAC7BsD,IAAI,EAAEH,YAAY,CAACG;QACrB,CAAC;;QAED;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;;QAEA;QACA5L,KAAK,CAACsO,GAAG,CAAC4E,iBAAiB,CAAC;;QAE5B;QACAtR,gBAAgB,CAACgC,GAAG,CAAC6H,YAAY,CAACnD,OAAO,EAAE;UACzC0F,KAAK,EAAEkF,iBAAiB;UACxBzH,YAAY,EAAEA,YAAY;UAC1BxE,QAAQ,EAAE6G;QACZ,CAAC,CAAC;QAEFvM,OAAO,CAACC,GAAG,CAAC,SAASiK,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkBwF,QAAQ,CAACvK,CAAC,KAAK,CAACuK,QAAQ,CAACrK,CAAC,GAAG,CAAC;MACjH,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,QAAQ4I,YAAY,CAACG,IAAI,YAAY,EAAE/I,KAAK,CAAC;QAC3D;QACAqZ,wBAAwB,CAACzQ,YAAY,EAAEqC,QAAQ,EAAEkU,iBAAiB,CAAC;MACrE;IACF;EACF,CAAC,CAAC;;EAEF;EACAzgB,OAAO,CAACC,GAAG,CAAC,OAAOI,gBAAgB,CAACsb,IAAI,SAAS,CAAC;EAClDtb,gBAAgB,CAACuL,OAAO,CAAC,CAACkO,QAAQ,EAAE/S,OAAO,KAAK;IAC9C/G,OAAO,CAACC,GAAG,CAAC,QAAQ8G,OAAO,KAAK+S,QAAQ,CAAC5P,YAAY,CAACG,IAAI,EAAE,CAAC;EAC/D,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMqW,2BAA2B,GAAID,iBAAiB,IAAK;EACzDljB,iBAAiB,CAAC4M,aAAa,CAACyB,OAAO,CAAC1B,YAAY,IAAI;IACtD;IACA,IAAIA,YAAY,CAACyW,eAAe,KAAK,KAAK,EAAE;MAC1C;IACF;IAEA,IAAIzW,YAAY,CAAC9E,QAAQ,IAAI8E,YAAY,CAAC/E,SAAS,IAAI+E,YAAY,CAACnD,OAAO,EAAE;MAC3E;MACA,MAAMwF,QAAQ,GAAGkU,iBAAiB,CAAClW,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAC/E,SAAS,CAAC,EAClCqF,UAAU,CAACN,YAAY,CAAC9E,QAAQ,CAClC,CAAC;MAEDuV,wBAAwB,CAACzQ,YAAY,EAAEqC,QAAQ,EAAEkU,iBAAiB,CAAC;IACrE;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAM9F,wBAAwB,GAAGA,CAACzQ,YAAY,EAAEqC,QAAQ,EAAEkU,iBAAiB,KAAK;EAC9E;EACA,MAAM/P,QAAQ,GAAG,IAAI5T,KAAK,CAAC8d,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAClD,MAAM3L,QAAQ,GAAG,IAAInS,KAAK,CAAC+d,iBAAiB,CAAC;IAC3CnT,KAAK,EAAE,QAAQ;IACf2H,WAAW,EAAE,KAAK;IAClBW,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM2B,iBAAiB,GAAG,IAAI7U,KAAK,CAACge,IAAI,CAACpK,QAAQ,EAAEzB,QAAQ,CAAC;;EAE5D;EACA0C,iBAAiB,CAACtH,IAAI,GAAG,SAASH,YAAY,CAACG,IAAI,EAAE;;EAErD;EACAsH,iBAAiB,CAACjM,QAAQ,CAACrD,GAAG,CAACkK,QAAQ,CAACvK,CAAC,EAAE,EAAE,EAAE,CAACuK,QAAQ,CAACrK,CAAC,CAAC;;EAE3D;EACAyP,iBAAiB,CAAC5B,WAAW,GAAG,GAAG;;EAEnC;EACA4B,iBAAiB,CAAC8J,QAAQ,GAAG;IAC3BzP,IAAI,EAAE,cAAc;IACpBjF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG;EACrB,CAAC;;EAED;EACA,MAAMyW,gBAAgB,GAAG,IAAIhkB,KAAK,CAACue,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5D,MAAM0F,gBAAgB,GAAG,IAAIjkB,KAAK,CAAC+d,iBAAiB,CAAC;IACnDnT,KAAK,EAAE,QAAQ;IACf2H,WAAW,EAAE,IAAI;IACjBW,OAAO,EAAE,GAAG;IAAG;IACfuL,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMyF,QAAQ,GAAG,IAAIlkB,KAAK,CAACge,IAAI,CAACgG,gBAAgB,EAAEC,gBAAgB,CAAC;EACnEC,QAAQ,CAAC3W,IAAI,GAAG,YAAYH,YAAY,CAACG,IAAI,EAAE;EAC/C2W,QAAQ,CAACvF,QAAQ,GAAG;IAClBzP,IAAI,EAAE,cAAc;IACpBjF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG,IAAI;IACvB4W,UAAU,EAAE;EACd,CAAC;EAEDtP,iBAAiB,CAAC5E,GAAG,CAACiU,QAAQ,CAAC;;EAE/B;EACAviB,KAAK,CAACsO,GAAG,CAAC4E,iBAAiB,CAAC;;EAE5B;EACAtR,gBAAgB,CAACgC,GAAG,CAAC6H,YAAY,CAACnD,OAAO,EAAE;IACzC0F,KAAK,EAAEkF,iBAAiB;IACxBzH,YAAY,EAAEA,YAAY;IAC1BxE,QAAQ,EAAE6G;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM2U,aAAa,GAAG,IAAIpkB,KAAK,CAACue,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAM8F,aAAa,GAAG,IAAIrkB,KAAK,CAAC+d,iBAAiB,CAAC;IAAEnT,KAAK,EAAE;EAAS,CAAC,CAAC;EACtE,MAAM0Z,SAAS,GAAG,IAAItkB,KAAK,CAACge,IAAI,CAACoG,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAAC1b,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;EAEhC;EACA+e,SAAS,CAAC3F,QAAQ,GAAG;IACnBzP,IAAI,EAAE,cAAc;IACpBjF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG;EACrB,CAAC;EAEDsH,iBAAiB,CAAC5E,GAAG,CAACqU,SAAS,CAAC;EAEhCphB,OAAO,CAACC,GAAG,CAAC,SAASiK,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkBwF,QAAQ,CAACvK,CAAC,KAAK,CAACuK,QAAQ,CAACrK,CAAC,GAAG,CAAC;AACjH,CAAC;;AAED;AACA,MAAMgP,iBAAiB,GAAIL,OAAO,IAAK;EACrC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMI,oBAAoB,GAAIoQ,OAAO,IAAK;EACxC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAM7G,gBAAgB,GAAGA,CAAChI,KAAK,EAAE8O,SAAS,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,KAAK;EAC7F,IAAI,CAACH,SAAS,IAAI,CAACC,aAAa,IAAI,CAACC,cAAc,EAAE;EAErDxhB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEuS,KAAK,CAAC8H,OAAO,EAAE9H,KAAK,CAAC+H,OAAO,CAAC;;EAEvD;EACA,MAAMmH,IAAI,GAAGJ,SAAS,CAACK,qBAAqB,CAAC,CAAC;EAC9C,MAAMC,MAAM,GAAI,CAACpP,KAAK,CAAC8H,OAAO,GAAGoH,IAAI,CAAC9b,IAAI,IAAI0b,SAAS,CAACO,WAAW,GAAI,CAAC,GAAG,CAAC;EAC5E,MAAMC,MAAM,GAAG,EAAE,CAACtP,KAAK,CAAC+H,OAAO,GAAGmH,IAAI,CAACpa,GAAG,IAAIga,SAAS,CAACS,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;;EAE7E;EACA,MAAMC,SAAS,GAAG,IAAIllB,KAAK,CAACmlB,SAAS,CAAC,CAAC;EACvC;EACAD,SAAS,CAAC9E,MAAM,CAACgF,MAAM,CAACC,SAAS,GAAG,CAAC;EACrCH,SAAS,CAAC9E,MAAM,CAACkF,IAAI,CAACD,SAAS,GAAG,CAAC;EAEnC,MAAME,WAAW,GAAG,IAAIvlB,KAAK,CAACwlB,OAAO,CAACV,MAAM,EAAEE,MAAM,CAAC;EACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,EAAEb,cAAc,CAAC;EAEpDxhB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE2hB,MAAM,EAAEE,MAAM,CAAC;;EAEtC;EACA,MAAMU,mBAAmB,GAAG,EAAE;EAE9BniB,gBAAgB,CAACuL,OAAO,CAAC,CAACkO,QAAQ,EAAE/S,OAAO,KAAK;IAC9C,IAAI+S,QAAQ,CAACrN,KAAK,EAAE;MAClB;MACA+V,mBAAmB,CAAChR,IAAI,CAACsI,QAAQ,CAACrN,KAAK,CAAC;MACxC;MACAqN,QAAQ,CAACrN,KAAK,CAAC3F,OAAO,GAAG,IAAI;MAC7BgT,QAAQ,CAACrN,KAAK,CAACsD,WAAW,GAAG,IAAI,CAAC,CAAC;MACnC;MACA+J,QAAQ,CAACrN,KAAK,CAACqC,QAAQ,CAACC,KAAK,IAAI;QAC/BA,KAAK,CAACjI,OAAO,GAAG,IAAI;QACpBiI,KAAK,CAACgB,WAAW,GAAG,IAAI;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF/P,OAAO,CAACC,GAAG,CAAC,QAAQuiB,mBAAmB,CAACrV,MAAM,gBAAgB,CAAC;;EAE/D;EACA,MAAMsV,sBAAsB,GAAGT,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,EAAE,IAAI,CAAC;EAEpF,IAAIC,sBAAsB,CAACtV,MAAM,GAAG,CAAC,EAAE;IACrCnN,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEwiB,sBAAsB,CAACtV,MAAM,CAAC;IACxDsV,sBAAsB,CAAC7W,OAAO,CAAC,CAAC+W,SAAS,EAAEC,KAAK,KAAK;MACnD5iB,OAAO,CAACC,GAAG,CAAC,QAAQ2iB,KAAK,GAAG,EACjBD,SAAS,CAACE,MAAM,CAACxY,IAAI,IAAI,KAAK,EAC9B,KAAK,EAAEsY,SAAS,CAAClgB,QAAQ,EACzB,WAAW,EAAEkgB,SAAS,CAACE,MAAM,CAACpH,QAAQ,CAAC;IACpD,CAAC,CAAC;;IAEF;IACA,MAAMqH,GAAG,GAAGC,yBAAyB,CAACN,sBAAsB,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC;IAEvE,IAAIC,GAAG,IAAIA,GAAG,CAACrH,QAAQ,IAAIqH,GAAG,CAACrH,QAAQ,CAACzP,IAAI,KAAK,cAAc,EAAE;MAC/D;MACA,MAAMjF,OAAO,GAAG+b,GAAG,CAACrH,QAAQ,CAAC1U,OAAO;MACpC/G,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE8G,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;;MAEhE;MACA,IAAIic,SAAS,GAAGjc,OAAO;MACvB,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,CAAC1G,gBAAgB,CAACiC,GAAG,CAACyE,OAAO,CAAC,IAAI1G,gBAAgB,CAACiC,GAAG,CAACgP,QAAQ,CAACvK,OAAO,CAAC,CAAC,EAAE;QAC5Gic,SAAS,GAAG1R,QAAQ,CAACvK,OAAO,CAAC;QAC7B/G,OAAO,CAACC,GAAG,CAAC,cAAc+iB,SAAS,EAAE,CAAC;MACxC,CAAC,MAAM,IAAI,OAAOjc,OAAO,KAAK,QAAQ,IAAI,CAAC1G,gBAAgB,CAACiC,GAAG,CAACyE,OAAO,CAAC,IAAI1G,gBAAgB,CAACiC,GAAG,CAACoP,MAAM,CAAC3K,OAAO,CAAC,CAAC,EAAE;QACjHic,SAAS,GAAGtR,MAAM,CAAC3K,OAAO,CAAC;QAC3B/G,OAAO,CAACC,GAAG,CAAC,eAAe+iB,SAAS,EAAE,CAAC;MACzC;;MAEA;MACA5jB,MAAM,CAAC+S,qBAAqB,CAAC6Q,SAAS,CAAC;MACvC;IACF;EACF;;EAEA;EACA,MAAMC,UAAU,GAAGjB,SAAS,CAACU,gBAAgB,CAACnB,aAAa,CAACtK,QAAQ,EAAE,IAAI,CAAC;EAE3EjX,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgjB,UAAU,CAAC9V,MAAM,CAAC;EAE7C,IAAI8V,UAAU,CAAC9V,MAAM,GAAG,CAAC,EAAE;IACzB;IACA8V,UAAU,CAACrX,OAAO,CAAC,CAAC+W,SAAS,EAAEC,KAAK,KAAK;MACvC,MAAME,GAAG,GAAGH,SAAS,CAACE,MAAM;MAC5B7iB,OAAO,CAACC,GAAG,CAAC,UAAU2iB,KAAK,GAAG,EAAEE,GAAG,CAACzY,IAAI,IAAI,KAAK,EACrC,WAAW,EAAEyY,GAAG,CAACrH,QAAQ,EACzB,KAAK,EAAEkH,SAAS,CAAClgB,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACA,KAAK,IAAI2H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6Y,UAAU,CAAC9V,MAAM,EAAE/C,CAAC,EAAE,EAAE;MAC1C,MAAM0Y,GAAG,GAAGC,yBAAyB,CAACE,UAAU,CAAC7Y,CAAC,CAAC,CAACyY,MAAM,CAAC;MAC3D,IAAIC,GAAG,IAAIA,GAAG,CAACrH,QAAQ,IAAIqH,GAAG,CAACrH,QAAQ,CAACzP,IAAI,KAAK,cAAc,EAAE;QAC/D,MAAMjF,OAAO,GAAG+b,GAAG,CAACrH,QAAQ,CAAC1U,OAAO;QACpC/G,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE8G,OAAO,CAAC;;QAEvC;QACA3H,MAAM,CAAC+S,qBAAqB,CAACpL,OAAO,CAAC;QACrC;MACF;IACF;EACF;;EAEA;EACA/G,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;;EAE9B;EACA,IAAIijB,YAAY,GAAG,IAAI;EACvB,IAAI3Z,WAAW,GAAG,GAAG,CAAC,CAAC;;EAEvBlJ,gBAAgB,CAACuL,OAAO,CAAC,CAACkO,QAAQ,EAAE/S,OAAO,KAAK;IAC9C,IAAI+S,QAAQ,CAACrN,KAAK,EAAE;MAClB,MAAM0W,QAAQ,GAAG,IAAIrmB,KAAK,CAACgG,OAAO,CAAC,CAAC;MACpC;MACAgX,QAAQ,CAACrN,KAAK,CAAC2W,gBAAgB,CAACD,QAAQ,CAAC;;MAEzC;MACA,MAAME,SAAS,GAAGF,QAAQ,CAACrhB,KAAK,CAAC,CAAC;MAClCuhB,SAAS,CAACC,OAAO,CAAC9B,cAAc,CAAC;;MAEjC;MACA,MAAM+B,EAAE,GAAGF,SAAS,CAACrhB,CAAC,GAAG4f,MAAM;MAC/B,MAAM4B,EAAE,GAAGH,SAAS,CAACnhB,CAAC,GAAG4f,MAAM;MAC/B,MAAMrf,QAAQ,GAAGS,IAAI,CAACugB,IAAI,CAACF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;MAE7CxjB,OAAO,CAACC,GAAG,CAAC,MAAM8G,OAAO,OAAO,EAAEtE,QAAQ,CAAC;MAE3C,IAAIA,QAAQ,GAAG8G,WAAW,EAAE;QAC1BA,WAAW,GAAG9G,QAAQ;QACtBygB,YAAY,GAAG;UAAEnc,OAAO;UAAEtE;QAAS,CAAC;MACtC;IACF;EACF,CAAC,CAAC;EAEF,IAAIygB,YAAY,EAAE;IAChBljB,OAAO,CAACC,GAAG,CAAC,oBAAoBijB,YAAY,CAACnc,OAAO,SAASmc,YAAY,CAACzgB,QAAQ,EAAE,CAAC;;IAErF;IACArD,MAAM,CAAC+S,qBAAqB,CAAC+Q,YAAY,CAACnc,OAAO,CAAC;IAClD;EACF;EAEA/G,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;;EAE1B;EACA,IAAIwhB,eAAe,EAAE;IACnBA,eAAe,CAAC5P,IAAI,IAAI;MACtB,IAAIA,IAAI,CAAC/K,OAAO,EAAE;QAChB,OAAO;UAAE,GAAG+K,IAAI;UAAE/K,OAAO,EAAE;QAAM,CAAC;MACpC;MACA,OAAO+K,IAAI;IACb,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAMiL,kBAAkB,GAAI2E,eAAe,IAAK;EAC9C;EACA,IAAIriB,MAAM,CAAC+H,0BAA0B,IAAI/H,MAAM,CAAC+H,0BAA0B,CAACiB,OAAO,EAAE;IAClFqR,aAAa,CAACra,MAAM,CAAC+H,0BAA0B,CAACiB,OAAO,CAAC;IACxDhJ,MAAM,CAAC+H,0BAA0B,CAACiB,OAAO,GAAG,IAAI;IAChDpI,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B;;EAEA;EACA,IAAIb,MAAM,CAAC8H,mBAAmB,EAAE;IAC9B9H,MAAM,CAAC8H,mBAAmB,CAACkB,OAAO,GAAG,IAAI;EAC3C;;EAEA;EACAqZ,eAAe,CAAC5P,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAE/K,OAAO,EAAE;EAAM,CAAC,CAAC,CAAC;EACtD9G,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;AACtB,CAAC;;AAED;AACAb,MAAM,CAACskB,qBAAqB,GAAI3c,OAAO,IAAK;EAC1C,IAAI;IAAA,IAAA4c,qBAAA,EAAAC,sBAAA;IACF;IACA,MAAMxS,YAAY,GAAG/Q,gBAAgB,CAACmC,GAAG,CAACuE,OAAO,IAAI,GAAG,CAAC;IACzD,IAAI,CAACqK,YAAY,EAAE;MACjBpR,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEyF,OAAO,CAAC;;MAEtC;MACA/G,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxBI,gBAAgB,CAACuL,OAAO,CAAC,CAACiY,KAAK,EAAE/X,EAAE,KAAK;QACtC9L,OAAO,CAACC,GAAG,CAAC,KAAK6L,EAAE,KAAK+X,KAAK,CAAC3Z,YAAY,CAACG,IAAI,EAAE,CAAC;MACpD,CAAC,CAAC;MAEF,OAAO,KAAK;IACd;;IAEA;IACA,MAAMyZ,UAAU,GAAG1S,YAAY,CAAC3E,KAAK;;IAErC;IACA,MAAMsX,SAAS,GAAGzjB,kBAAkB,CAACkC,GAAG,CAACuE,OAAO,CAAC;IACjD,MAAMmD,YAAY,GAAGkH,YAAY,CAAClH,YAAY;;IAE9C;IACA,IAAIlD,OAAO;IAEX,IAAI+c,SAAS,IAAIA,SAAS,CAAC9c,MAAM,EAAE;MACjCD,OAAO,gBACLvJ,OAAA;QAAKoe,KAAK,EAAE;UAAE3V,OAAO,EAAE,KAAK;UAAEqB,KAAK,EAAE,OAAO;UAAEiV,SAAS,EAAE,OAAO;UAAEwH,SAAS,EAAE;QAAO,CAAE;QAAA/M,QAAA,gBACpFxZ,OAAA;UAAKoe,KAAK,EAAE;YACVlU,UAAU,EAAE,MAAM;YAClBsc,YAAY,EAAE,KAAK;YACnB1d,QAAQ,EAAE,MAAM;YAChB2d,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAAlN,QAAA,GACC/M,YAAY,CAACG,IAAI,EAAC,QAAM,EAACtD,OAAO,EAAC,GACpC;QAAA;UAAA+U,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxe,OAAA;UAAAwZ,QAAA,EACG8M,SAAS,CAAC9c,MAAM,CAAC4G,GAAG,CAAC,CAAC+C,KAAK,EAAEgS,KAAK,KAAK;YACtC,IAAIwB,UAAU;YACd,QAAQxT,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBAAEgT,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;gBAAEA,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;cAAE;gBAASA,UAAU,GAAG,SAAS;gBAAE;YAC7C;YAEA,oBACE3mB,OAAA;cAAiBoe,KAAK,EAAE;gBACtBoI,YAAY,EAAE,KAAK;gBACnB9d,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE,KAAK;gBACnBE,QAAQ,EAAE;cACZ,CAAE;cAAA0Q,QAAA,gBACAxZ,OAAA;gBAAKoe,KAAK,EAAE;kBAAElU,UAAU,EAAE;gBAAO,CAAE;gBAAAsP,QAAA,EAChC/F,iBAAiB,CAACN,KAAK,CAACC,OAAO;cAAC;gBAAAiL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNxe,OAAA;gBAAKoe,KAAK,EAAE;kBAAE9V,OAAO,EAAE,MAAM;kBAAEse,cAAc,EAAE;gBAAgB,CAAE;gBAAApN,QAAA,gBAC/DxZ,OAAA;kBAAAwZ,QAAA,EAAM;gBAAI;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBxe,OAAA;kBAAMoe,KAAK,EAAE;oBACXnU,KAAK,EAAE0c,UAAU;oBACjBzc,UAAU,EAAE,MAAM;oBAClBxB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAA4Q,QAAA,EACCrG,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGR,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAG;gBAAI;kBAAA0K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNxe,OAAA;gBAAKoe,KAAK,EAAE;kBAAE9V,OAAO,EAAE,MAAM;kBAAEse,cAAc,EAAE;gBAAgB,CAAE;gBAAApN,QAAA,gBAC/DxZ,OAAA;kBAAAwZ,QAAA,EAAM;gBAAK;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBxe,OAAA;kBAAMoe,KAAK,EAAE;oBAAElU,UAAU,EAAE;kBAAO,CAAE;kBAAAsP,QAAA,GAAErG,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAAyK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAzBE2G,KAAK;cAAA9G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxe,OAAA;UAAKoe,KAAK,EAAE;YAAEyI,SAAS,EAAE,KAAK;YAAE/d,QAAQ,EAAE,MAAM;YAAEmB,KAAK,EAAE;UAAO,CAAE;UAAAuP,QAAA,GAAC,4BAC3D,EAAC,IAAIpS,IAAI,CAAC,CAAC,CAAC0f,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAAzI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACLjV,OAAO,gBACLvJ,OAAA;QAAKoe,KAAK,EAAE;UAAE3V,OAAO,EAAE,KAAK;UAAEwW,QAAQ,EAAE;QAAQ,CAAE;QAAAzF,QAAA,gBAChDxZ,OAAA;UAAKoe,KAAK,EAAE;YAAElU,UAAU,EAAE,MAAM;YAAEsc,YAAY,EAAE;UAAM,CAAE;UAAAhN,QAAA,EAAE/M,YAAY,CAACG;QAAI;UAAAyR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClFxe,OAAA;UAAAwZ,QAAA,GAAK,kBAAM,EAAClQ,OAAO;QAAA;UAAA+U,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1Bxe,OAAA;UAAAwZ,QAAA,EAAK;QAAU;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN;IACH;;IAEA;IACA,MAAMuI,OAAO,GAAGplB,MAAM,CAACuV,UAAU,GAAG,CAAC;IACrC,MAAM8P,OAAO,GAAGrlB,MAAM,CAACwV,WAAW,GAAG,CAAC;;IAEtC;IACA,MAAM6M,eAAe,IAAAkC,qBAAA,GAAGpG,QAAQ,CAAC+B,aAAa,CAAC,OAAO,CAAC,cAAAqE,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCe,gBAAgB,cAAAd,sBAAA,uBAAjDA,sBAAA,CAAmD/c,sBAAsB;IAEjG,IAAI4a,eAAe,EAAE;MACnB;MACAA,eAAe,CAAC;QACd3a,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBrB,QAAQ,EAAE;UAAE1D,CAAC,EAAEwiB,OAAO;UAAEtiB,CAAC,EAAEuiB;QAAQ,CAAC;QACpCzd,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAA8c,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE9c,MAAM,KAAI;MAC/B,CAAC,CAAC;MAEFjH,OAAO,CAACC,GAAG,CAAC,SAASiK,YAAY,CAACG,IAAI,KAAKtD,OAAO,UAAU,CAAC;MAC7D,OAAO,IAAI;IACb,CAAC,MAAM;MACL;MACA,MAAM4d,OAAO,GAAGpH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CmH,OAAO,CAAC9I,KAAK,CAACnW,QAAQ,GAAG,UAAU;MACnCif,OAAO,CAAC9I,KAAK,CAACjW,IAAI,GAAG,GAAG4e,OAAO,IAAI;MACnCG,OAAO,CAAC9I,KAAK,CAACvU,GAAG,GAAG,GAAGmd,OAAO,IAAI;MAClCE,OAAO,CAAC9I,KAAK,CAAChW,SAAS,GAAG,wBAAwB;MAClD8e,OAAO,CAAC9I,KAAK,CAAC/V,MAAM,GAAG,MAAM;MAC7B6e,OAAO,CAAC9I,KAAK,CAAC1V,eAAe,GAAG,qBAAqB;MACrDwe,OAAO,CAAC9I,KAAK,CAACnU,KAAK,GAAG,OAAO;MAC7Bid,OAAO,CAAC9I,KAAK,CAACxV,YAAY,GAAG,KAAK;MAClCse,OAAO,CAAC9I,KAAK,CAACrV,SAAS,GAAG,8BAA8B;MACxDme,OAAO,CAAC9I,KAAK,CAAC3V,OAAO,GAAG,KAAK;MAC7Bye,OAAO,CAAC9I,KAAK,CAACa,QAAQ,GAAG,OAAO;MAChCiI,OAAO,CAAC9I,KAAK,CAACtV,QAAQ,GAAG,MAAM;MAE/Boe,OAAO,CAACC,SAAS,GAAG;AAC1B;AACA,YAAY1a,YAAY,CAACG,IAAI,SAAStD,OAAO;AAC7C;AACA;AACA,qBAAqBA,OAAO;AAC5B,eAAegd,SAAS,GAAG,UAAU,GAAG,YAAY;AACpD;AACA;AACA,OAAO;MAEDxG,QAAQ,CAACsH,IAAI,CAACzP,WAAW,CAACuP,OAAO,CAAC;;MAElC;MACA,MAAMG,WAAW,GAAGH,OAAO,CAACrF,aAAa,CAAC,QAAQ,CAAC;MACnD,IAAIwF,WAAW,EAAE;QACfA,WAAW,CAACxL,gBAAgB,CAAC,OAAO,EAAE,MAAM;UAC1CiE,QAAQ,CAACsH,IAAI,CAACjL,WAAW,CAAC+K,OAAO,CAAC;QACpC,CAAC,CAAC;MACJ;MAEA3kB,OAAO,CAACC,GAAG,CAAC,gBAAgBiK,YAAY,CAACG,IAAI,KAAKtD,OAAO,OAAO,CAAC;MACjE,OAAO,IAAI;IACb;EACF,CAAC,CAAC,OAAOzF,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACAlC,MAAM,CAAC2lB,iBAAiB,GAAG,MAAM;EAC/B/kB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;EAErB,IAAI,CAACI,gBAAgB,IAAIA,gBAAgB,CAACsb,IAAI,KAAK,CAAC,EAAE;IACpD3b,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,OAAO,EAAE;EACX;EAEA,MAAM+kB,IAAI,GAAG,EAAE;EACf3kB,gBAAgB,CAACuL,OAAO,CAAC,CAACiY,KAAK,EAAE/X,EAAE,KAAK;IACtC9L,OAAO,CAACC,GAAG,CAAC,SAAS6L,EAAE,SAAS+X,KAAK,CAAC3Z,YAAY,CAACG,IAAI,EAAE,CAAC;IAC1D2a,IAAI,CAACxT,IAAI,CAAC;MACR1F,EAAE;MACFzB,IAAI,EAAEwZ,KAAK,CAAC3Z,YAAY,CAACG,IAAI;MAC7B3E,QAAQ,EAAEme,KAAK,CAACne;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOsf,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA5lB,MAAM,CAAC+S,qBAAqB,GAAIpL,OAAO,IAAK;EAC1C,IAAI;IACF;IACAA,OAAO,GAAG2K,MAAM,CAAC3K,OAAO,CAAC;IAEzB/G,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE8G,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;IAC/E/G,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,gBAAgB,CAACsb,IAAI,CAAC;IAC3D3b,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEK,kBAAkB,CAACqb,IAAI,CAAC;;IAE/D;IACA,IAAI,CAAC5U,OAAO,IAAI1G,gBAAgB,CAACsb,IAAI,GAAG,CAAC,EAAE;MACzC5U,OAAO,GAAG2K,MAAM,CAAC3Q,KAAK,CAACkkB,IAAI,CAAC5kB,gBAAgB,CAAC6kB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxDllB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE8G,OAAO,CAAC;IAC3C;;IAEA;IACA/G,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1BI,gBAAgB,CAACuL,OAAO,CAAC,CAACiY,KAAK,EAAE/X,EAAE,KAAK;MAAA,IAAAqZ,mBAAA;MACtCnlB,OAAO,CAACC,GAAG,CAAC,KAAK6L,EAAE,KAAK,OAAOA,EAAE,MAAM,EAAAqZ,mBAAA,GAAAtB,KAAK,CAAC3Z,YAAY,cAAAib,mBAAA,uBAAlBA,mBAAA,CAAoB9a,IAAI,KAAI,IAAI,EAAE,CAAC;IAC5E,CAAC,CAAC;;IAEF;IACA,IAAI+G,YAAY,GAAG/Q,gBAAgB,CAACmC,GAAG,CAACuE,OAAO,CAAC;IAChD,IAAI,CAACqK,YAAY,EAAE;MACjB;MACA,MAAMgU,SAAS,GAAG9T,QAAQ,CAACvK,OAAO,CAAC;MACnCqK,YAAY,GAAG/Q,gBAAgB,CAACmC,GAAG,CAAC4iB,SAAS,CAAC;MAE9C,IAAIhU,YAAY,EAAE;QAChBpR,OAAO,CAACC,GAAG,CAAC,UAAUmlB,SAAS,SAAS,CAAC;QACzCre,OAAO,GAAGqe,SAAS,CAAC,CAAC;MACvB;IACF;IAEA,IAAI,CAAChU,YAAY,EAAE;MACjBpR,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEyF,OAAO,CAAC;MACtC,OAAO,KAAK;IACd;;IAEA;IACA,IAAI3H,MAAM,CAAC8H,mBAAmB,EAAE;MAC9B9H,MAAM,CAAC8H,mBAAmB,CAACkB,OAAO,GAAGrB,OAAO;IAC9C;;IAEA;IACA,IAAI3H,MAAM,CAAC+H,0BAA0B,IAAI/H,MAAM,CAAC+H,0BAA0B,CAACiB,OAAO,EAAE;MAClFqR,aAAa,CAACra,MAAM,CAAC+H,0BAA0B,CAACiB,OAAO,CAAC;MACxDhJ,MAAM,CAAC+H,0BAA0B,CAACiB,OAAO,GAAG,IAAI;IAClD;;IAEA;IACA,MAAMid,yBAAyB,GAAGA,CAAA,KAAM;MAAA,IAAAC,qBAAA;MACtC;MACA,IAAI,CAAClmB,MAAM,CAACgI,uBAAuB,EAAE;MAErC,MAAMme,SAAS,IAAAD,qBAAA,GAAGlmB,MAAM,CAAC8H,mBAAmB,cAAAoe,qBAAA,uBAA1BA,qBAAA,CAA4Bld,OAAO;MACrD,IAAI,CAACmd,SAAS,EAAE;;MAEhB;MACA,IAAIxB,SAAS,GAAGzjB,kBAAkB,CAACkC,GAAG,CAAC+iB,SAAS,CAAC;MACjD,IAAI,CAACxB,SAAS,IAAI,OAAOwB,SAAS,KAAK,QAAQ,EAAE;QAC/C;QACAxB,SAAS,GAAGzjB,kBAAkB,CAACkC,GAAG,CAAC8O,QAAQ,CAACiU,SAAS,CAAC,CAAC;MACzD,CAAC,MAAM,IAAI,CAACxB,SAAS,IAAI,OAAOwB,SAAS,KAAK,QAAQ,EAAE;QACtD;QACAxB,SAAS,GAAGzjB,kBAAkB,CAACkC,GAAG,CAACkP,MAAM,CAAC6T,SAAS,CAAC,CAAC;MACvD;MAEA,IAAI,CAACxB,SAAS,IAAI,CAACA,SAAS,CAAC9c,MAAM,IAAI8c,SAAS,CAAC9c,MAAM,CAACkG,MAAM,KAAK,CAAC,EAAE;QACpEnN,OAAO,CAACC,GAAG,CAAC,QAAQslB,SAAS,cAAc,CAAC;QAC5C;MACF;;MAEA;MACA,MAAMC,iBAAiB,GAAGnlB,gBAAgB,CAACmC,GAAG,CAAC+iB,SAAS,CAAC,KAC/B,OAAOA,SAAS,KAAK,QAAQ,GAAGllB,gBAAgB,CAACmC,GAAG,CAAC8O,QAAQ,CAACiU,SAAS,CAAC,CAAC,GACzEllB,gBAAgB,CAACmC,GAAG,CAACkP,MAAM,CAAC6T,SAAS,CAAC,CAAC,CAAC;MAElE,IAAI,CAACC,iBAAiB,EAAE;QACtBxlB,OAAO,CAACsB,KAAK,CAAC,WAAWikB,SAAS,gBAAgB,CAAC;QACnD;MACF;MAEA,MAAMrb,YAAY,GAAGsb,iBAAiB,CAACtb,YAAY;;MAEnD;MACA,MAAMlD,OAAO,gBACXvJ,OAAA;QAAKoe,KAAK,EAAE;UAAE3V,OAAO,EAAE,KAAK;UAAEqB,KAAK,EAAE,OAAO;UAAEiV,SAAS,EAAE,OAAO;UAAEwH,SAAS,EAAE;QAAO,CAAE;QAAA/M,QAAA,gBACpFxZ,OAAA;UAAKoe,KAAK,EAAE;YACVlU,UAAU,EAAE,MAAM;YAClBsc,YAAY,EAAE,KAAK;YACnB1d,QAAQ,EAAE,MAAM;YAChB2d,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAAlN,QAAA,GACC,CAAA/M,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAACkb,SAAS,EAAC,GACjD;QAAA;UAAAzJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxe,OAAA;UAAAwZ,QAAA,EACG8M,SAAS,CAAC9c,MAAM,CAAC4G,GAAG,CAAC,CAAC+C,KAAK,EAAEgS,KAAK,KAAK;YACtC,IAAIwB,UAAU;YACd,IAAIqB,SAAS;YAEb,QAAQ7U,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBACNgT,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;cACF,KAAK,GAAG;gBACNrB,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;cACF,KAAK,GAAG;cACR;gBACErB,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;YACJ;YAEA,MAAM1U,SAAS,GAAGG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC;YAElD,oBACEpT,OAAA;cAAiBoe,KAAK,EAAE;gBACtBoI,YAAY,EAAE,KAAK;gBACnB9d,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE,KAAK;gBACnBE,QAAQ,EAAE;cACZ,CAAE;cAAA0Q,QAAA,gBACAxZ,OAAA;gBAAKoe,KAAK,EAAE;kBAAElU,UAAU,EAAE;gBAAO,CAAE;gBAAAsP,QAAA,GAChClG,SAAS,EAAC,oBAAQ,EAACH,KAAK,CAACC,OAAO,EAAC,GACpC;cAAA;gBAAAiL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNxe,OAAA;gBAAKoe,KAAK,EAAE;kBAAE9V,OAAO,EAAE,MAAM;kBAAEse,cAAc,EAAE;gBAAgB,CAAE;gBAAApN,QAAA,gBAC/DxZ,OAAA;kBAAAwZ,QAAA,EAAM;gBAAI;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBxe,OAAA;kBAAMoe,KAAK,EAAE;oBACXnU,KAAK,EAAE0c,UAAU;oBACjBzc,UAAU,EAAE,MAAM;oBAClBxB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAA4Q,QAAA,EACCwO;gBAAS;kBAAA3J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNxe,OAAA;gBAAKoe,KAAK,EAAE;kBAAE9V,OAAO,EAAE,MAAM;kBAAEse,cAAc,EAAE;gBAAgB,CAAE;gBAAApN,QAAA,gBAC/DxZ,OAAA;kBAAAwZ,QAAA,EAAM;gBAAK;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBxe,OAAA;kBAAMoe,KAAK,EAAE;oBAAElU,UAAU,EAAE;kBAAO,CAAE;kBAAAsP,QAAA,GAAErG,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAAyK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAzBE2G,KAAK;cAAA9G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxe,OAAA;UAAKoe,KAAK,EAAE;YAAEyI,SAAS,EAAE,KAAK;YAAE/d,QAAQ,EAAE,MAAM;YAAEmB,KAAK,EAAE;UAAO,CAAE;UAAAuP,QAAA,GAAC,4BAC3D,EAAC,IAAIpS,IAAI,CAACkf,SAAS,CAAC9R,UAAU,CAAC,CAACsS,kBAAkB,CAAC,CAAC,EAAC,6BAC7D;QAAA;UAAAzI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;;MAED;MACA7c,MAAM,CAACgI,uBAAuB,CAACyK,IAAI,KAAK;QACtC,GAAGA,IAAI;QACP7K,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE8c,SAAS,CAAC9c;MACpB,CAAC,CAAC,CAAC;MAEHjH,OAAO,CAACC,GAAG,CAAC,SAAS,CAAAiK,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAIkb,SAAS,WAAW,CAAC;IAClE,CAAC;;IAED;IACA,MAAMG,gCAAgC,GAAGA,CAAA,KAAM;MAC7C;MACA,IAAI3B,SAAS,GAAGzjB,kBAAkB,CAACkC,GAAG,CAACuE,OAAO,CAAC;MAC/C,IAAI,CAACgd,SAAS,EAAE;QACd;QACA,MAAMqB,SAAS,GAAG9T,QAAQ,CAACvK,OAAO,CAAC;QACnCgd,SAAS,GAAGzjB,kBAAkB,CAACkC,GAAG,CAAC4iB,SAAS,CAAC;QAE7C,IAAIrB,SAAS,EAAE;UACb/jB,OAAO,CAACC,GAAG,CAAC,UAAUmlB,SAAS,aAAa,CAAC;QAC/C;MACF;MAEAplB,OAAO,CAACC,GAAG,CAAC,QAAQ8G,OAAO,SAAS,EAAEgd,SAAS,CAAC;MAEhD,MAAM7Z,YAAY,GAAGkH,YAAY,CAAClH,YAAY;MAC9ClK,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEiK,YAAY,CAAC;;MAElC;MACA,IAAIlD,OAAO;MAEX,IAAI+c,SAAS,IAAIA,SAAS,CAAC9c,MAAM,IAAI8c,SAAS,CAAC9c,MAAM,CAACkG,MAAM,GAAG,CAAC,EAAE;QAChE;QACAnN,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;QACtB8jB,SAAS,CAAC9c,MAAM,CAAC2E,OAAO,CAAC,CAACgF,KAAK,EAAEgS,KAAK,KAAK;UACzC5iB,OAAO,CAACC,GAAG,CAAC,MAAM2iB,KAAK,GAAC,CAAC,QAAQhS,KAAK,CAACC,OAAO,QAAQD,KAAK,CAACQ,YAAY,UAAUR,KAAK,CAACS,UAAU,EAAE,CAAC;QACvG,CAAC,CAAC;QAEFrK,OAAO,gBACLvJ,OAAA;UAAKoe,KAAK,EAAE;YAAE3V,OAAO,EAAE,KAAK;YAAEqB,KAAK,EAAE,OAAO;YAAEiV,SAAS,EAAE,OAAO;YAAEwH,SAAS,EAAE;UAAO,CAAE;UAAA/M,QAAA,gBACpFxZ,OAAA;YAAKoe,KAAK,EAAE;cACVlU,UAAU,EAAE,MAAM;cAClBsc,YAAY,EAAE,KAAK;cACnB1d,QAAQ,EAAE,MAAM;cAChB2d,YAAY,EAAE,gBAAgB;cAC9BC,aAAa,EAAE;YACjB,CAAE;YAAAlN,QAAA,GACC,CAAA/M,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAACtD,OAAO,EAAC,GAC/C;UAAA;YAAA+U,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxe,OAAA;YAAAwZ,QAAA,EACG8M,SAAS,CAAC9c,MAAM,CAAC4G,GAAG,CAAC,CAAC+C,KAAK,EAAEgS,KAAK,KAAK;cACtC,IAAIwB,UAAU;cACd,IAAIqB,SAAS;cAEb,QAAQ7U,KAAK,CAACQ,YAAY;gBACxB,KAAK,GAAG;kBACNgT,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;gBACF,KAAK,GAAG;kBACNrB,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;gBACF,KAAK,GAAG;gBACR;kBACErB,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;cACJ;cAEA,MAAM1U,SAAS,GAAGG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC;cAElD,oBACEpT,OAAA;gBAAiBoe,KAAK,EAAE;kBACtBoI,YAAY,EAAE,KAAK;kBACnB9d,eAAe,EAAE,uBAAuB;kBACxCD,OAAO,EAAE,KAAK;kBACdG,YAAY,EAAE,KAAK;kBACnBE,QAAQ,EAAE;gBACZ,CAAE;gBAAA0Q,QAAA,gBACAxZ,OAAA;kBAAKoe,KAAK,EAAE;oBAAElU,UAAU,EAAE;kBAAO,CAAE;kBAAAsP,QAAA,GAChClG,SAAS,EAAC,oBAAQ,EAACH,KAAK,CAACC,OAAO,EAAC,GACpC;gBAAA;kBAAAiL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNxe,OAAA;kBAAKoe,KAAK,EAAE;oBAAE9V,OAAO,EAAE,MAAM;oBAAEse,cAAc,EAAE;kBAAgB,CAAE;kBAAApN,QAAA,gBAC/DxZ,OAAA;oBAAAwZ,QAAA,EAAM;kBAAI;oBAAA6E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjBxe,OAAA;oBAAMoe,KAAK,EAAE;sBACXnU,KAAK,EAAE0c,UAAU;sBACjBzc,UAAU,EAAE,MAAM;sBAClBxB,eAAe,EAAE,iBAAiB;sBAClCD,OAAO,EAAE,OAAO;sBAChBG,YAAY,EAAE;oBAChB,CAAE;oBAAA4Q,QAAA,EACCwO;kBAAS;oBAAA3J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNxe,OAAA;kBAAKoe,KAAK,EAAE;oBAAE9V,OAAO,EAAE,MAAM;oBAAEse,cAAc,EAAE;kBAAgB,CAAE;kBAAApN,QAAA,gBAC/DxZ,OAAA;oBAAAwZ,QAAA,EAAM;kBAAK;oBAAA6E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClBxe,OAAA;oBAAMoe,KAAK,EAAE;sBAAElU,UAAU,EAAE;oBAAO,CAAE;oBAAAsP,QAAA,GAAErG,KAAK,CAACS,UAAU,EAAC,SAAE;kBAAA;oBAAAyK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA,GAzBE2G,KAAK;gBAAA9G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BV,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNxe,OAAA;YAAKoe,KAAK,EAAE;cAAEyI,SAAS,EAAE,KAAK;cAAE/d,QAAQ,EAAE,MAAM;cAAEmB,KAAK,EAAE;YAAO,CAAE;YAAAuP,QAAA,GAAC,4BAC3D,EAAC,IAAIpS,IAAI,CAACkf,SAAS,CAAC9R,UAAU,CAAC,CAACsS,kBAAkB,CAAC,CAAC,EAAC,6BAC7D;UAAA;YAAAzI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MACH,CAAC,MAAM;QACL;QACAjV,OAAO,gBACLvJ,OAAA;UAAKoe,KAAK,EAAE;YAAE3V,OAAO,EAAE,KAAK;YAAEqB,KAAK,EAAE;UAAQ,CAAE;UAAA0P,QAAA,gBAC7CxZ,OAAA;YAAKoe,KAAK,EAAE;cACVlU,UAAU,EAAE,MAAM;cAClBsc,YAAY,EAAE,KAAK;cACnB1d,QAAQ,EAAE,MAAM;cAChB2d,YAAY,EAAE,gBAAgB;cAC9BC,aAAa,EAAE;YACjB,CAAE;YAAAlN,QAAA,GACC,CAAA/M,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAACtD,OAAO,EAAC,GAC/C;UAAA;YAAA+U,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxe,OAAA;YAAKoe,KAAK,EAAE;cAAEnU,KAAK,EAAE,SAAS;cAAEnB,QAAQ,EAAE;YAAO,CAAE;YAAA0Q,QAAA,EAAC;UAEpD;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxe,OAAA;YAAKoe,KAAK,EAAE;cAAEyI,SAAS,EAAE,KAAK;cAAE/d,QAAQ,EAAE,MAAM;cAAEmB,KAAK,EAAE;YAAO,CAAE;YAAAuP,QAAA,GAAC,4BAC3D,EAAC,IAAIpS,IAAI,CAAC,CAAC,CAAC0f,kBAAkB,CAAC,CAAC;UAAA;YAAAzI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MACH;;MAEA;MACA,MAAMja,CAAC,GAAG5C,MAAM,CAACuV,UAAU,GAAG,CAAC;MAC/B,MAAMzS,CAAC,GAAG9C,MAAM,CAACwV,WAAW,GAAG,CAAC;;MAEhC;MACA,IAAIxV,MAAM,CAACgI,uBAAuB,EAAE;QAAA,IAAAue,UAAA;QAClC3lB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7C6G,OAAO,EAAE,IAAI;UACbC,OAAO;UACPrB,QAAQ,EAAE;YAAE1D,CAAC;YAAEE;UAAE;QACnB,CAAC,CAAC;QAEF9C,MAAM,CAACgI,uBAAuB,CAAC;UAC7BN,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEA,OAAO;UAChBrB,QAAQ,EAAE;YAAE1D,CAAC;YAAEE;UAAE,CAAC;UAClB8E,OAAO,EAAEA,OAAO;UAChBC,MAAM,EAAE,EAAA0e,UAAA,GAAA5B,SAAS,cAAA4B,UAAA,uBAATA,UAAA,CAAW1e,MAAM,KAAI;QAC/B,CAAC,CAAC;QAEFjH,OAAO,CAACC,GAAG,CAAC,SAAS,CAAAiK,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAItD,OAAO,WAAW,CAAC;;QAE9D;QACA,IAAI3H,MAAM,CAAC+H,0BAA0B,EAAE;UACrC/H,MAAM,CAAC+H,0BAA0B,CAACiB,OAAO,GAAG6R,WAAW,CAACoL,yBAAyB,EAAE7hB,6BAA6B,GAAG,IAAI,CAAC;UACxHxD,OAAO,CAACC,GAAG,CAAC,qBAAqBuD,6BAA6B,IAAI,CAAC;QACrE;QAEA,OAAO,IAAI;MACb,CAAC,MAAM;QACLxD,OAAO,CAACsB,KAAK,CAAC,8BAA8B,CAAC;QAC7C,OAAO,KAAK;MACd;IACF,CAAC;IAED,OAAOokB,gCAAgC,CAAC,CAAC;EAC3C,CAAC,CAAC,OAAOpkB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,MAAMyhB,yBAAyB,GAAIF,MAAM,IAAK;EAC5C,IAAIza,OAAO,GAAGya,MAAM;;EAEpB;EACA,IAAIza,OAAO,IAAIA,OAAO,CAACqT,QAAQ,IAAIrT,OAAO,CAACqT,QAAQ,CAACzP,IAAI,KAAK,cAAc,EAAE;IAC3EhM,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEmI,OAAO,CAACiC,IAAI,IAAI,KAAK,CAAC;IAChD,OAAOjC,OAAO;EAChB;;EAEA;EACA,OAAOA,OAAO,IAAIA,OAAO,CAACoY,MAAM,EAAE;IAChCpY,OAAO,GAAGA,OAAO,CAACoY,MAAM;IACxB,IAAIpY,OAAO,CAACqT,QAAQ,IAAIrT,OAAO,CAACqT,QAAQ,CAACzP,IAAI,KAAK,cAAc,EAAE;MAChEhM,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEmI,OAAO,CAACiC,IAAI,IAAI,KAAK,CAAC;MAChD,OAAOjC,OAAO;IAChB;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACAhJ,MAAM,CAACwmB,kBAAkB,GAAG,CAAC5jB,CAAC,EAAEE,CAAC,KAAK;EACpC,IAAI;IACFlC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE+B,CAAC,EAAEE,CAAC,CAAC;;IAEnC;IACA,MAAMob,MAAM,GAAGC,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAI,CAAChC,MAAM,EAAE;MACXtd,OAAO,CAACsB,KAAK,CAAC,sBAAsB,CAAC;MACrC,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAAC7C,KAAK,IAAI,CAACuF,SAAS,CAACoE,OAAO,EAAE;MAChCpI,OAAO,CAACsB,KAAK,CAAC,iBAAiB,CAAC;MAChC,OAAO,KAAK;IACd;;IAEA;IACA,IAAIU,CAAC,KAAKsO,SAAS,IAAIpO,CAAC,KAAKoO,SAAS,EAAE;MACtCtO,CAAC,GAAG5C,MAAM,CAACuV,UAAU,GAAG,CAAC;MACzBzS,CAAC,GAAG9C,MAAM,CAACwV,WAAW,GAAG,CAAC;IAC5B;;IAEA;IACA,MAAM8M,IAAI,GAAGpE,MAAM,CAACqE,qBAAqB,CAAC,CAAC;IAC3C,MAAMC,MAAM,GAAI,CAAC5f,CAAC,GAAG0f,IAAI,CAAC9b,IAAI,IAAI0X,MAAM,CAACuE,WAAW,GAAI,CAAC,GAAG,CAAC;IAC7D,MAAMC,MAAM,GAAG,EAAE,CAAC5f,CAAC,GAAGwf,IAAI,CAACpa,GAAG,IAAIgW,MAAM,CAACyE,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;IAE9D/hB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE2hB,MAAM,EAAEE,MAAM,CAAC;;IAErC;IACA,MAAME,SAAS,GAAG,IAAIllB,KAAK,CAACmlB,SAAS,CAAC,CAAC;IACvCD,SAAS,CAAC9E,MAAM,CAACgF,MAAM,CAACC,SAAS,GAAG,CAAC;IACrCH,SAAS,CAAC9E,MAAM,CAACkF,IAAI,CAACD,SAAS,GAAG,CAAC;IAEnC,MAAME,WAAW,GAAG,IAAIvlB,KAAK,CAACwlB,OAAO,CAACV,MAAM,EAAEE,MAAM,CAAC;IACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,EAAEre,SAAS,CAACoE,OAAO,CAAC;;IAEvD;IACA,MAAMoa,mBAAmB,GAAG,EAAE;IAC9BniB,gBAAgB,CAACuL,OAAO,CAAC,CAACkO,QAAQ,EAAE/S,OAAO,KAAK;MAC9C,IAAI+S,QAAQ,CAACrN,KAAK,EAAE;QAClB+V,mBAAmB,CAAChR,IAAI,CAACsI,QAAQ,CAACrN,KAAK,CAAC;QACxCzM,OAAO,CAACC,GAAG,CAAC,SAAS8G,OAAO,QAAQ,CAAC;MACvC;IACF,CAAC,CAAC;;IAEF;IACA/G,OAAO,CAACC,GAAG,CAAC,QAAQuiB,mBAAmB,CAACrV,MAAM,YAAY,CAAC;IAC3D,MAAM0Y,YAAY,GAAG7D,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,EAAE,IAAI,CAAC;IAE1E,IAAIqD,YAAY,CAAC1Y,MAAM,GAAG,CAAC,EAAE;MAC3BnN,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B4lB,YAAY,CAACja,OAAO,CAAC,CAAC+W,SAAS,EAAEvY,CAAC,KAAK;QACrCpK,OAAO,CAACC,GAAG,CAAC,MAAMmK,CAAC,GAAG,EAAEuY,SAAS,CAACE,MAAM,CAACxY,IAAI,IAAI,KAAK,EAC1C,KAAK,EAAEsY,SAAS,CAAClgB,QAAQ,EACzB,WAAW,EAAEkgB,SAAS,CAACE,MAAM,CAACnd,QAAQ,CAACmF,OAAO,CAAC,CAAC,EAChD,WAAW,EAAE8X,SAAS,CAACE,MAAM,CAACpH,QAAQ,CAAC;;QAEnD;QACA,MAAMqH,GAAG,GAAGC,yBAAyB,CAACJ,SAAS,CAACE,MAAM,CAAC;QACvD,IAAIC,GAAG,IAAIA,GAAG,CAACrH,QAAQ,IAAIqH,GAAG,CAACrH,QAAQ,CAACzP,IAAI,KAAK,cAAc,EAAE;UAC/DhM,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE6iB,GAAG,CAACrH,QAAQ,CAAC1U,OAAO,CAAC;QAC/C;MACF,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;;IAEA;IACA/G,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,MAAM6lB,eAAe,GAAG9D,SAAS,CAACU,gBAAgB,CAACjkB,KAAK,CAACwY,QAAQ,EAAE,IAAI,CAAC;IAExEjX,OAAO,CAACC,GAAG,CAAC,WAAW6lB,eAAe,CAAC3Y,MAAM,MAAM,CAAC;IACpD2Y,eAAe,CAACla,OAAO,CAAC,CAAC+W,SAAS,EAAEvY,CAAC,KAAK;MACxC,MAAM0Y,GAAG,GAAGH,SAAS,CAACE,MAAM;MAC5B7iB,OAAO,CAACC,GAAG,CAAC,QAAQmK,CAAC,GAAG,EAAE0Y,GAAG,CAACzY,IAAI,IAAI,KAAK,EAC/B,KAAK,EAAEyY,GAAG,CAAC9W,IAAI,EACf,KAAK,EAAE8W,GAAG,CAACpd,QAAQ,CAACmF,OAAO,CAAC,CAAC,EAC7B,KAAK,EAAE8X,SAAS,CAAClgB,QAAQ,EACzB,WAAW,EAAEqgB,GAAG,CAACrH,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACAzb,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,IAAI8lB,YAAY,GAAG,CAAC;IAEpB1lB,gBAAgB,CAACuL,OAAO,CAAC,CAACkO,QAAQ,EAAE/S,OAAO,KAAK;MAC9C,IAAI+S,QAAQ,CAACrN,KAAK,EAAE;QAAA,IAAAuZ,qBAAA;QAClB;QACA,IAAIC,SAAS,GAAGnM,QAAQ,CAACrN,KAAK,CAAC3F,OAAO;QACtC,IAAIof,cAAc,GAAG,IAAI;;QAEzB;QACA,MAAM/C,QAAQ,GAAG,IAAIrmB,KAAK,CAACgG,OAAO,CAAC,CAAC;QACpCgX,QAAQ,CAACrN,KAAK,CAAC2W,gBAAgB,CAACD,QAAQ,CAAC;;QAEzC;QACA,MAAMgD,gBAAgB,GAAGhD,QAAQ,CAACzgB,UAAU,CAACsB,SAAS,CAACoE,OAAO,CAAC1C,QAAQ,CAAC;;QAExE;QACA,MAAM2d,SAAS,GAAGF,QAAQ,CAACrhB,KAAK,CAAC,CAAC,CAACwhB,OAAO,CAACtf,SAAS,CAACoE,OAAO,CAAC;QAC7D,IAAIlF,IAAI,CAACK,GAAG,CAAC8f,SAAS,CAACrhB,CAAC,CAAC,GAAG,CAAC,IAAIkB,IAAI,CAACK,GAAG,CAAC8f,SAAS,CAACnhB,CAAC,CAAC,GAAG,CAAC,IAAImhB,SAAS,CAACjhB,CAAC,GAAG,CAAC,CAAC,IAAIihB,SAAS,CAACjhB,CAAC,GAAG,CAAC,EAAE;UACjG8jB,cAAc,GAAG,KAAK;QACxB;QAEA,IAAID,SAAS,EAAE;UACbF,YAAY,EAAE;QAChB;QAEA/lB,OAAO,CAACC,GAAG,CAAC,OAAO8G,OAAO,GAAG,EAAE;UAC7Bqf,EAAE,EAAE,EAAAJ,qBAAA,GAAAlM,QAAQ,CAAC5P,YAAY,cAAA8b,qBAAA,uBAArBA,qBAAA,CAAuB3b,IAAI,KAAI,IAAI;UACvCgc,GAAG,EAAEJ,SAAS;UACdK,KAAK,EAAEJ,cAAc;UACrBK,IAAI,EAAEpD,QAAQ,CAACtY,OAAO,CAAC,CAAC;UACxB2b,IAAI,EAAE,CAACnD,SAAS,CAACrhB,CAAC,EAAEqhB,SAAS,CAACnhB,CAAC,EAAEmhB,SAAS,CAACjhB,CAAC,CAAC;UAC7CqkB,MAAM,EAAEN;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEFnmB,OAAO,CAACC,GAAG,CAAC,MAAM8lB,YAAY,IAAI1lB,gBAAgB,CAACsb,IAAI,WAAW,CAAC;;IAEnE;IACA,OAAOmK,eAAe,CAAC3Y,MAAM,GAAG,CAAC;EACnC,CAAC,CAAC,OAAO7L,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,OAAO,KAAK;EACd;AACF,CAAC;;AAID;AACA,MAAMsQ,wBAAwB,GAAGA,CAACR,YAAY,EAAEG,SAAS,KAAK;EAAA,IAAAmV,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC5D,IAAI,CAACxV,YAAY,IAAI,CAACA,YAAY,CAAC3E,KAAK,IAAI,CAAC8E,SAAS,EAAE;IACtD;EACF;;EAEA;EACA,MAAMsV,cAAc,GAAG,EAAE;EACzBzV,YAAY,CAAC3E,KAAK,CAACqC,QAAQ,CAACC,KAAK,IAAI;IACnC,IAAIA,KAAK,CAAC0M,QAAQ,IAAI1M,KAAK,CAAC0M,QAAQ,CAACqL,OAAO,EAAE;MAC5CD,cAAc,CAACrV,IAAI,CAACzC,KAAK,CAAC;IAC5B;EACF,CAAC,CAAC;EAEF8X,cAAc,CAACjb,OAAO,CAACiY,KAAK,IAAI;IAC9BzS,YAAY,CAAC3E,KAAK,CAACuB,MAAM,CAAC6V,KAAK,CAAC;EAClC,CAAC,CAAC;;EAEF;EACA,IAAIO,UAAU;EACd,QAAO7S,SAAS,CAACH,YAAY;IAC3B,KAAK,GAAG;MACNgT,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;MACNA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;IACR;MACEA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;EACJ;;EAEA;EACA,MAAMlD,aAAa,GAAG,IAAIpkB,KAAK,CAACue,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAM8F,aAAa,GAAG,IAAIrkB,KAAK,CAAC+d,iBAAiB,CAAC;IAChDnT,KAAK,EAAE0c,UAAU;IACjBjV,QAAQ,EAAEiV,UAAU;IACpB2C,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM3F,SAAS,GAAG,IAAItkB,KAAK,CAACge,IAAI,CAACoG,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAAC1b,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAClC+e,SAAS,CAAC3F,QAAQ,GAAG;IACnBqL,OAAO,EAAE,IAAI;IACb9a,IAAI,EAAE,cAAc;IACpBjF,OAAO,GAAA2f,qBAAA,GAAEtV,YAAY,CAAClH,YAAY,cAAAwc,qBAAA,uBAAzBA,qBAAA,CAA2B3f,OAAO;IAC3C8J,OAAO,EAAEU,SAAS,CAACV,OAAO;IAC1BE,SAAS,EAAEQ,SAAS,CAACR,SAAS;IAC9BM,UAAU,EAAEE,SAAS,CAACF;EACxB,CAAC;;EAED;EACA,MAAMwS,KAAK,GAAG,IAAI/mB,KAAK,CAACkqB,UAAU,CAAC5C,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;EACrDP,KAAK,CAACne,QAAQ,CAACrD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAC5BwhB,KAAK,CAACpI,QAAQ,GAAG;IAAEqL,OAAO,EAAE;EAAK,CAAC;;EAElC;EACA1V,YAAY,CAAC3E,KAAK,CAACM,GAAG,CAACqU,SAAS,CAAC;EACjChQ,YAAY,CAAC3E,KAAK,CAACM,GAAG,CAAC8W,KAAK,CAAC;EAE7B7jB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAA0mB,sBAAA,GAAAvV,YAAY,CAAClH,YAAY,cAAAyc,sBAAA,uBAAzBA,sBAAA,CAA2Btc,IAAI,OAAAuc,sBAAA,GAAIxV,YAAY,CAAClH,YAAY,cAAA0c,sBAAA,uBAAzBA,sBAAA,CAA2B7f,OAAO,cAAawK,SAAS,CAACH,YAAY,WAAWG,SAAS,CAACF,UAAU,GAAG,CAAC;AACjK,CAAC;AAED,eAAe5N,WAAW;AAAC,IAAAsZ,EAAA;AAAAkK,YAAA,CAAAlK,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}