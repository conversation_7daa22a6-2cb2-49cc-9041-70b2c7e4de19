{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"overlayClassName\", \"trigger\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"overlayStyle\", \"prefixCls\", \"children\", \"onVisibleChange\", \"afterVisibleChange\", \"transitionName\", \"animation\", \"motion\", \"placement\", \"align\", \"destroyTooltipOnHide\", \"defaultVisible\", \"getTooltipContainer\", \"overlayInnerStyle\", \"arrowContent\", \"overlay\", \"id\", \"showArrow\", \"classNames\", \"styles\"];\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { forwardRef, useImperativeHandle, useRef } from 'react';\nimport { placements } from \"./placements\";\nimport Popup from \"./Popup\";\nimport useId from \"rc-util/es/hooks/useId\";\nvar Tooltip = function Tooltip(props, ref) {\n  var overlayClassName = props.overlayClassName,\n    _props$trigger = props.trigger,\n    trigger = _props$trigger === void 0 ? ['hover'] : _props$trigger,\n    _props$mouseEnterDela = props.mouseEnterDelay,\n    mouseEnterDelay = _props$mouseEnterDela === void 0 ? 0 : _props$mouseEnterDela,\n    _props$mouseLeaveDela = props.mouseLeaveDelay,\n    mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela,\n    overlayStyle = props.overlayStyle,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-tooltip' : _props$prefixCls,\n    children = props.children,\n    onVisibleChange = props.onVisibleChange,\n    afterVisibleChange = props.afterVisibleChange,\n    transitionName = props.transitionName,\n    animation = props.animation,\n    motion = props.motion,\n    _props$placement = props.placement,\n    placement = _props$placement === void 0 ? 'right' : _props$placement,\n    _props$align = props.align,\n    align = _props$align === void 0 ? {} : _props$align,\n    _props$destroyTooltip = props.destroyTooltipOnHide,\n    destroyTooltipOnHide = _props$destroyTooltip === void 0 ? false : _props$destroyTooltip,\n    defaultVisible = props.defaultVisible,\n    getTooltipContainer = props.getTooltipContainer,\n    overlayInnerStyle = props.overlayInnerStyle,\n    arrowContent = props.arrowContent,\n    overlay = props.overlay,\n    id = props.id,\n    _props$showArrow = props.showArrow,\n    showArrow = _props$showArrow === void 0 ? true : _props$showArrow,\n    tooltipClassNames = props.classNames,\n    tooltipStyles = props.styles,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedId = useId(id);\n  var triggerRef = useRef(null);\n  useImperativeHandle(ref, function () {\n    return triggerRef.current;\n  });\n  var extraProps = _objectSpread({}, restProps);\n  if ('visible' in props) {\n    extraProps.popupVisible = props.visible;\n  }\n  var getPopupElement = function getPopupElement() {\n    return /*#__PURE__*/React.createElement(Popup, {\n      key: \"content\",\n      prefixCls: prefixCls,\n      id: mergedId,\n      bodyClassName: tooltipClassNames === null || tooltipClassNames === void 0 ? void 0 : tooltipClassNames.body,\n      overlayInnerStyle: _objectSpread(_objectSpread({}, overlayInnerStyle), tooltipStyles === null || tooltipStyles === void 0 ? void 0 : tooltipStyles.body)\n    }, overlay);\n  };\n  var getChildren = function getChildren() {\n    var child = React.Children.only(children);\n    var originalProps = (child === null || child === void 0 ? void 0 : child.props) || {};\n    var childProps = _objectSpread(_objectSpread({}, originalProps), {}, {\n      'aria-describedby': overlay ? mergedId : null\n    });\n    return /*#__PURE__*/React.cloneElement(children, childProps);\n  };\n  return /*#__PURE__*/React.createElement(Trigger, _extends({\n    popupClassName: classNames(overlayClassName, tooltipClassNames === null || tooltipClassNames === void 0 ? void 0 : tooltipClassNames.root),\n    prefixCls: prefixCls,\n    popup: getPopupElement,\n    action: trigger,\n    builtinPlacements: placements,\n    popupPlacement: placement,\n    ref: triggerRef,\n    popupAlign: align,\n    getPopupContainer: getTooltipContainer,\n    onPopupVisibleChange: onVisibleChange,\n    afterPopupVisibleChange: afterVisibleChange,\n    popupTransitionName: transitionName,\n    popupAnimation: animation,\n    popupMotion: motion,\n    defaultPopupVisible: defaultVisible,\n    autoDestroy: destroyTooltipOnHide,\n    mouseLeaveDelay: mouseLeaveDelay,\n    popupStyle: _objectSpread(_objectSpread({}, overlayStyle), tooltipStyles === null || tooltipStyles === void 0 ? void 0 : tooltipStyles.root),\n    mouseEnterDelay: mouseEnterDelay,\n    arrow: showArrow\n  }, extraProps), getChildren());\n};\nexport default /*#__PURE__*/forwardRef(Tooltip);", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_objectWithoutProperties", "_excluded", "<PERSON><PERSON>", "classNames", "React", "forwardRef", "useImperativeHandle", "useRef", "placements", "Popup", "useId", "<PERSON><PERSON><PERSON>", "props", "ref", "overlayClassName", "_props$trigger", "trigger", "_props$mouseEnterDela", "mouseEnterDelay", "_props$mouseLeaveDela", "mouseLeaveDelay", "overlayStyle", "_props$prefixCls", "prefixCls", "children", "onVisibleChange", "afterVisibleChange", "transitionName", "animation", "motion", "_props$placement", "placement", "_props$align", "align", "_props$destroyTooltip", "destroyTooltipOnHide", "defaultVisible", "getTooltipContainer", "overlayInnerStyle", "arrow<PERSON>ontent", "overlay", "id", "_props$showArrow", "showArrow", "tooltipClassNames", "tooltipStyles", "styles", "restProps", "mergedId", "triggerRef", "current", "extraProps", "popupVisible", "visible", "getPopupElement", "createElement", "key", "bodyClassName", "body", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "Children", "only", "originalProps", "childProps", "cloneElement", "popupClassName", "root", "popup", "action", "builtinPlacements", "popupPlacement", "popupAlign", "getPopupContainer", "onPopupVisibleChange", "afterPopupVisibleChange", "popupTransitionName", "popupAnimation", "popupMotion", "defaultPopupVisible", "autoDestroy", "popupStyle", "arrow"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-tooltip/es/Tooltip.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"overlayClassName\", \"trigger\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"overlayStyle\", \"prefixCls\", \"children\", \"onVisibleChange\", \"afterVisibleChange\", \"transitionName\", \"animation\", \"motion\", \"placement\", \"align\", \"destroyTooltipOnHide\", \"defaultVisible\", \"getTooltipContainer\", \"overlayInnerStyle\", \"arrowContent\", \"overlay\", \"id\", \"showArrow\", \"classNames\", \"styles\"];\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { forwardRef, useImperativeHandle, useRef } from 'react';\nimport { placements } from \"./placements\";\nimport Popup from \"./Popup\";\nimport useId from \"rc-util/es/hooks/useId\";\nvar Tooltip = function Tooltip(props, ref) {\n  var overlayClassName = props.overlayClassName,\n    _props$trigger = props.trigger,\n    trigger = _props$trigger === void 0 ? ['hover'] : _props$trigger,\n    _props$mouseEnterDela = props.mouseEnterDelay,\n    mouseEnterDelay = _props$mouseEnterDela === void 0 ? 0 : _props$mouseEnterDela,\n    _props$mouseLeaveDela = props.mouseLeaveDelay,\n    mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela,\n    overlayStyle = props.overlayStyle,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-tooltip' : _props$prefixCls,\n    children = props.children,\n    onVisibleChange = props.onVisibleChange,\n    afterVisibleChange = props.afterVisibleChange,\n    transitionName = props.transitionName,\n    animation = props.animation,\n    motion = props.motion,\n    _props$placement = props.placement,\n    placement = _props$placement === void 0 ? 'right' : _props$placement,\n    _props$align = props.align,\n    align = _props$align === void 0 ? {} : _props$align,\n    _props$destroyTooltip = props.destroyTooltipOnHide,\n    destroyTooltipOnHide = _props$destroyTooltip === void 0 ? false : _props$destroyTooltip,\n    defaultVisible = props.defaultVisible,\n    getTooltipContainer = props.getTooltipContainer,\n    overlayInnerStyle = props.overlayInnerStyle,\n    arrowContent = props.arrowContent,\n    overlay = props.overlay,\n    id = props.id,\n    _props$showArrow = props.showArrow,\n    showArrow = _props$showArrow === void 0 ? true : _props$showArrow,\n    tooltipClassNames = props.classNames,\n    tooltipStyles = props.styles,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedId = useId(id);\n  var triggerRef = useRef(null);\n  useImperativeHandle(ref, function () {\n    return triggerRef.current;\n  });\n  var extraProps = _objectSpread({}, restProps);\n  if ('visible' in props) {\n    extraProps.popupVisible = props.visible;\n  }\n  var getPopupElement = function getPopupElement() {\n    return /*#__PURE__*/React.createElement(Popup, {\n      key: \"content\",\n      prefixCls: prefixCls,\n      id: mergedId,\n      bodyClassName: tooltipClassNames === null || tooltipClassNames === void 0 ? void 0 : tooltipClassNames.body,\n      overlayInnerStyle: _objectSpread(_objectSpread({}, overlayInnerStyle), tooltipStyles === null || tooltipStyles === void 0 ? void 0 : tooltipStyles.body)\n    }, overlay);\n  };\n  var getChildren = function getChildren() {\n    var child = React.Children.only(children);\n    var originalProps = (child === null || child === void 0 ? void 0 : child.props) || {};\n    var childProps = _objectSpread(_objectSpread({}, originalProps), {}, {\n      'aria-describedby': overlay ? mergedId : null\n    });\n    return /*#__PURE__*/React.cloneElement(children, childProps);\n  };\n  return /*#__PURE__*/React.createElement(Trigger, _extends({\n    popupClassName: classNames(overlayClassName, tooltipClassNames === null || tooltipClassNames === void 0 ? void 0 : tooltipClassNames.root),\n    prefixCls: prefixCls,\n    popup: getPopupElement,\n    action: trigger,\n    builtinPlacements: placements,\n    popupPlacement: placement,\n    ref: triggerRef,\n    popupAlign: align,\n    getPopupContainer: getTooltipContainer,\n    onPopupVisibleChange: onVisibleChange,\n    afterPopupVisibleChange: afterVisibleChange,\n    popupTransitionName: transitionName,\n    popupAnimation: animation,\n    popupMotion: motion,\n    defaultPopupVisible: defaultVisible,\n    autoDestroy: destroyTooltipOnHide,\n    mouseLeaveDelay: mouseLeaveDelay,\n    popupStyle: _objectSpread(_objectSpread({}, overlayStyle), tooltipStyles === null || tooltipStyles === void 0 ? void 0 : tooltipStyles.root),\n    mouseEnterDelay: mouseEnterDelay,\n    arrow: showArrow\n  }, extraProps), getChildren());\n};\nexport default /*#__PURE__*/forwardRef(Tooltip);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,kBAAkB,EAAE,SAAS,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,mBAAmB,EAAE,cAAc,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,CAAC;AAClY,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,mBAAmB,EAAEC,MAAM,QAAQ,OAAO;AAC/D,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzC,IAAIC,gBAAgB,GAAGF,KAAK,CAACE,gBAAgB;IAC3CC,cAAc,GAAGH,KAAK,CAACI,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,GAAGA,cAAc;IAChEE,qBAAqB,GAAGL,KAAK,CAACM,eAAe;IAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;IAC9EE,qBAAqB,GAAGP,KAAK,CAACQ,eAAe;IAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,qBAAqB;IAChFE,YAAY,GAAGT,KAAK,CAACS,YAAY;IACjCC,gBAAgB,GAAGV,KAAK,CAACW,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,YAAY,GAAGA,gBAAgB;IACzEE,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,eAAe,GAAGb,KAAK,CAACa,eAAe;IACvCC,kBAAkB,GAAGd,KAAK,CAACc,kBAAkB;IAC7CC,cAAc,GAAGf,KAAK,CAACe,cAAc;IACrCC,SAAS,GAAGhB,KAAK,CAACgB,SAAS;IAC3BC,MAAM,GAAGjB,KAAK,CAACiB,MAAM;IACrBC,gBAAgB,GAAGlB,KAAK,CAACmB,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,gBAAgB;IACpEE,YAAY,GAAGpB,KAAK,CAACqB,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,YAAY;IACnDE,qBAAqB,GAAGtB,KAAK,CAACuB,oBAAoB;IAClDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IACvFE,cAAc,GAAGxB,KAAK,CAACwB,cAAc;IACrCC,mBAAmB,GAAGzB,KAAK,CAACyB,mBAAmB;IAC/CC,iBAAiB,GAAG1B,KAAK,CAAC0B,iBAAiB;IAC3CC,YAAY,GAAG3B,KAAK,CAAC2B,YAAY;IACjCC,OAAO,GAAG5B,KAAK,CAAC4B,OAAO;IACvBC,EAAE,GAAG7B,KAAK,CAAC6B,EAAE;IACbC,gBAAgB,GAAG9B,KAAK,CAAC+B,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,gBAAgB;IACjEE,iBAAiB,GAAGhC,KAAK,CAACT,UAAU;IACpC0C,aAAa,GAAGjC,KAAK,CAACkC,MAAM;IAC5BC,SAAS,GAAG/C,wBAAwB,CAACY,KAAK,EAAEX,SAAS,CAAC;EACxD,IAAI+C,QAAQ,GAAGtC,KAAK,CAAC+B,EAAE,CAAC;EACxB,IAAIQ,UAAU,GAAG1C,MAAM,CAAC,IAAI,CAAC;EAC7BD,mBAAmB,CAACO,GAAG,EAAE,YAAY;IACnC,OAAOoC,UAAU,CAACC,OAAO;EAC3B,CAAC,CAAC;EACF,IAAIC,UAAU,GAAGpD,aAAa,CAAC,CAAC,CAAC,EAAEgD,SAAS,CAAC;EAC7C,IAAI,SAAS,IAAInC,KAAK,EAAE;IACtBuC,UAAU,CAACC,YAAY,GAAGxC,KAAK,CAACyC,OAAO;EACzC;EACA,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,OAAO,aAAalD,KAAK,CAACmD,aAAa,CAAC9C,KAAK,EAAE;MAC7C+C,GAAG,EAAE,SAAS;MACdjC,SAAS,EAAEA,SAAS;MACpBkB,EAAE,EAAEO,QAAQ;MACZS,aAAa,EAAEb,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACc,IAAI;MAC3GpB,iBAAiB,EAAEvC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuC,iBAAiB,CAAC,EAAEO,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACa,IAAI;IACzJ,CAAC,EAAElB,OAAO,CAAC;EACb,CAAC;EACD,IAAImB,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IACvC,IAAIC,KAAK,GAAGxD,KAAK,CAACyD,QAAQ,CAACC,IAAI,CAACtC,QAAQ,CAAC;IACzC,IAAIuC,aAAa,GAAG,CAACH,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAChD,KAAK,KAAK,CAAC,CAAC;IACrF,IAAIoD,UAAU,GAAGjE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgE,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;MACnE,kBAAkB,EAAEvB,OAAO,GAAGQ,QAAQ,GAAG;IAC3C,CAAC,CAAC;IACF,OAAO,aAAa5C,KAAK,CAAC6D,YAAY,CAACzC,QAAQ,EAAEwC,UAAU,CAAC;EAC9D,CAAC;EACD,OAAO,aAAa5D,KAAK,CAACmD,aAAa,CAACrD,OAAO,EAAEJ,QAAQ,CAAC;IACxDoE,cAAc,EAAE/D,UAAU,CAACW,gBAAgB,EAAE8B,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACuB,IAAI,CAAC;IAC1I5C,SAAS,EAAEA,SAAS;IACpB6C,KAAK,EAAEd,eAAe;IACtBe,MAAM,EAAErD,OAAO;IACfsD,iBAAiB,EAAE9D,UAAU;IAC7B+D,cAAc,EAAExC,SAAS;IACzBlB,GAAG,EAAEoC,UAAU;IACfuB,UAAU,EAAEvC,KAAK;IACjBwC,iBAAiB,EAAEpC,mBAAmB;IACtCqC,oBAAoB,EAAEjD,eAAe;IACrCkD,uBAAuB,EAAEjD,kBAAkB;IAC3CkD,mBAAmB,EAAEjD,cAAc;IACnCkD,cAAc,EAAEjD,SAAS;IACzBkD,WAAW,EAAEjD,MAAM;IACnBkD,mBAAmB,EAAE3C,cAAc;IACnC4C,WAAW,EAAE7C,oBAAoB;IACjCf,eAAe,EAAEA,eAAe;IAChC6D,UAAU,EAAElF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsB,YAAY,CAAC,EAAEwB,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACsB,IAAI,CAAC;IAC5IjD,eAAe,EAAEA,eAAe;IAChCgE,KAAK,EAAEvC;EACT,CAAC,EAAEQ,UAAU,CAAC,EAAEQ,WAAW,CAAC,CAAC,CAAC;AAChC,CAAC;AACD,eAAe,aAAatD,UAAU,CAACM,OAAO,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}