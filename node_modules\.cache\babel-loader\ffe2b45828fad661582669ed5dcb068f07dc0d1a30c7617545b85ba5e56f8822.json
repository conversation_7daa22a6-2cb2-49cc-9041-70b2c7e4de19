{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useState, useCallback, useEffect } from 'react';\n/**\n * Trigger a callback on state change\n */\nexport default function useEffectState() {\n  var _useState = useState({\n      id: 0,\n      callback: null\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    effectId = _useState2[0],\n    setEffectId = _useState2[1];\n  var update = useCallback(function (callback) {\n    setEffectId(function (_ref) {\n      var id = _ref.id;\n      return {\n        id: id + 1,\n        callback: callback\n      };\n    });\n  }, []);\n  useEffect(function () {\n    var _effectId$callback;\n    (_effectId$callback = effectId.callback) === null || _effectId$callback === void 0 || _effectId$callback.call(effectId);\n  }, [effectId]);\n  return update;\n}", "map": {"version": 3, "names": ["_slicedToArray", "useState", "useCallback", "useEffect", "useEffectState", "_useState", "id", "callback", "_useState2", "effectId", "setEffectId", "update", "_ref", "_effectId$callback", "call"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-mentions/es/hooks/useEffectState.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useState, useCallback, useEffect } from 'react';\n/**\n * Trigger a callback on state change\n */\nexport default function useEffectState() {\n  var _useState = useState({\n      id: 0,\n      callback: null\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    effectId = _useState2[0],\n    setEffectId = _useState2[1];\n  var update = useCallback(function (callback) {\n    setEffectId(function (_ref) {\n      var id = _ref.id;\n      return {\n        id: id + 1,\n        callback: callback\n      };\n    });\n  }, []);\n  useEffect(function () {\n    var _effectId$callback;\n    (_effectId$callback = effectId.callback) === null || _effectId$callback === void 0 || _effectId$callback.call(effectId);\n  }, [effectId]);\n  return update;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACxD;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAAA,EAAG;EACvC,IAAIC,SAAS,GAAGJ,QAAQ,CAAC;MACrBK,EAAE,EAAE,CAAC;MACLC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFC,UAAU,GAAGR,cAAc,CAACK,SAAS,EAAE,CAAC,CAAC;IACzCI,QAAQ,GAAGD,UAAU,CAAC,CAAC,CAAC;IACxBE,WAAW,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC7B,IAAIG,MAAM,GAAGT,WAAW,CAAC,UAAUK,QAAQ,EAAE;IAC3CG,WAAW,CAAC,UAAUE,IAAI,EAAE;MAC1B,IAAIN,EAAE,GAAGM,IAAI,CAACN,EAAE;MAChB,OAAO;QACLA,EAAE,EAAEA,EAAE,GAAG,CAAC;QACVC,QAAQ,EAAEA;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACNJ,SAAS,CAAC,YAAY;IACpB,IAAIU,kBAAkB;IACtB,CAACA,kBAAkB,GAAGJ,QAAQ,CAACF,QAAQ,MAAM,IAAI,IAAIM,kBAAkB,KAAK,KAAK,CAAC,IAAIA,kBAAkB,CAACC,IAAI,CAACL,QAAQ,CAAC;EACzH,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACd,OAAOE,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}