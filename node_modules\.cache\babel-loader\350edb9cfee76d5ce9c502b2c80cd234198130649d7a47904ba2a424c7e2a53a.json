{"ast": null, "code": "/**\n * The Ease class provides a collection of easing functions for use with tween.js.\n */\nvar Easing = Object.freeze({\n  Linear: Object.freeze({\n    None: function (amount) {\n      return amount;\n    },\n    In: function (amount) {\n      return amount;\n    },\n    Out: function (amount) {\n      return amount;\n    },\n    InOut: function (amount) {\n      return amount;\n    }\n  }),\n  Quadratic: Object.freeze({\n    In: function (amount) {\n      return amount * amount;\n    },\n    Out: function (amount) {\n      return amount * (2 - amount);\n    },\n    InOut: function (amount) {\n      if ((amount *= 2) < 1) {\n        return 0.5 * amount * amount;\n      }\n      return -0.5 * (--amount * (amount - 2) - 1);\n    }\n  }),\n  Cubic: Object.freeze({\n    In: function (amount) {\n      return amount * amount * amount;\n    },\n    Out: function (amount) {\n      return --amount * amount * amount + 1;\n    },\n    InOut: function (amount) {\n      if ((amount *= 2) < 1) {\n        return 0.5 * amount * amount * amount;\n      }\n      return 0.5 * ((amount -= 2) * amount * amount + 2);\n    }\n  }),\n  Quartic: Object.freeze({\n    In: function (amount) {\n      return amount * amount * amount * amount;\n    },\n    Out: function (amount) {\n      return 1 - --amount * amount * amount * amount;\n    },\n    InOut: function (amount) {\n      if ((amount *= 2) < 1) {\n        return 0.5 * amount * amount * amount * amount;\n      }\n      return -0.5 * ((amount -= 2) * amount * amount * amount - 2);\n    }\n  }),\n  Quintic: Object.freeze({\n    In: function (amount) {\n      return amount * amount * amount * amount * amount;\n    },\n    Out: function (amount) {\n      return --amount * amount * amount * amount * amount + 1;\n    },\n    InOut: function (amount) {\n      if ((amount *= 2) < 1) {\n        return 0.5 * amount * amount * amount * amount * amount;\n      }\n      return 0.5 * ((amount -= 2) * amount * amount * amount * amount + 2);\n    }\n  }),\n  Sinusoidal: Object.freeze({\n    In: function (amount) {\n      return 1 - Math.sin((1.0 - amount) * Math.PI / 2);\n    },\n    Out: function (amount) {\n      return Math.sin(amount * Math.PI / 2);\n    },\n    InOut: function (amount) {\n      return 0.5 * (1 - Math.sin(Math.PI * (0.5 - amount)));\n    }\n  }),\n  Exponential: Object.freeze({\n    In: function (amount) {\n      return amount === 0 ? 0 : Math.pow(1024, amount - 1);\n    },\n    Out: function (amount) {\n      return amount === 1 ? 1 : 1 - Math.pow(2, -10 * amount);\n    },\n    InOut: function (amount) {\n      if (amount === 0) {\n        return 0;\n      }\n      if (amount === 1) {\n        return 1;\n      }\n      if ((amount *= 2) < 1) {\n        return 0.5 * Math.pow(1024, amount - 1);\n      }\n      return 0.5 * (-Math.pow(2, -10 * (amount - 1)) + 2);\n    }\n  }),\n  Circular: Object.freeze({\n    In: function (amount) {\n      return 1 - Math.sqrt(1 - amount * amount);\n    },\n    Out: function (amount) {\n      return Math.sqrt(1 - --amount * amount);\n    },\n    InOut: function (amount) {\n      if ((amount *= 2) < 1) {\n        return -0.5 * (Math.sqrt(1 - amount * amount) - 1);\n      }\n      return 0.5 * (Math.sqrt(1 - (amount -= 2) * amount) + 1);\n    }\n  }),\n  Elastic: Object.freeze({\n    In: function (amount) {\n      if (amount === 0) {\n        return 0;\n      }\n      if (amount === 1) {\n        return 1;\n      }\n      return -Math.pow(2, 10 * (amount - 1)) * Math.sin((amount - 1.1) * 5 * Math.PI);\n    },\n    Out: function (amount) {\n      if (amount === 0) {\n        return 0;\n      }\n      if (amount === 1) {\n        return 1;\n      }\n      return Math.pow(2, -10 * amount) * Math.sin((amount - 0.1) * 5 * Math.PI) + 1;\n    },\n    InOut: function (amount) {\n      if (amount === 0) {\n        return 0;\n      }\n      if (amount === 1) {\n        return 1;\n      }\n      amount *= 2;\n      if (amount < 1) {\n        return -0.5 * Math.pow(2, 10 * (amount - 1)) * Math.sin((amount - 1.1) * 5 * Math.PI);\n      }\n      return 0.5 * Math.pow(2, -10 * (amount - 1)) * Math.sin((amount - 1.1) * 5 * Math.PI) + 1;\n    }\n  }),\n  Back: Object.freeze({\n    In: function (amount) {\n      var s = 1.70158;\n      return amount === 1 ? 1 : amount * amount * ((s + 1) * amount - s);\n    },\n    Out: function (amount) {\n      var s = 1.70158;\n      return amount === 0 ? 0 : --amount * amount * ((s + 1) * amount + s) + 1;\n    },\n    InOut: function (amount) {\n      var s = 1.70158 * 1.525;\n      if ((amount *= 2) < 1) {\n        return 0.5 * (amount * amount * ((s + 1) * amount - s));\n      }\n      return 0.5 * ((amount -= 2) * amount * ((s + 1) * amount + s) + 2);\n    }\n  }),\n  Bounce: Object.freeze({\n    In: function (amount) {\n      return 1 - Easing.Bounce.Out(1 - amount);\n    },\n    Out: function (amount) {\n      if (amount < 1 / 2.75) {\n        return 7.5625 * amount * amount;\n      } else if (amount < 2 / 2.75) {\n        return 7.5625 * (amount -= 1.5 / 2.75) * amount + 0.75;\n      } else if (amount < 2.5 / 2.75) {\n        return 7.5625 * (amount -= 2.25 / 2.75) * amount + 0.9375;\n      } else {\n        return 7.5625 * (amount -= 2.625 / 2.75) * amount + 0.984375;\n      }\n    },\n    InOut: function (amount) {\n      if (amount < 0.5) {\n        return Easing.Bounce.In(amount * 2) * 0.5;\n      }\n      return Easing.Bounce.Out(amount * 2 - 1) * 0.5 + 0.5;\n    }\n  }),\n  generatePow: function (power) {\n    if (power === void 0) {\n      power = 4;\n    }\n    power = power < Number.EPSILON ? Number.EPSILON : power;\n    power = power > 10000 ? 10000 : power;\n    return {\n      In: function (amount) {\n        return Math.pow(amount, power);\n      },\n      Out: function (amount) {\n        return 1 - Math.pow(1 - amount, power);\n      },\n      InOut: function (amount) {\n        if (amount < 0.5) {\n          return Math.pow(amount * 2, power) / 2;\n        }\n        return (1 - Math.pow(2 - amount * 2, power)) / 2 + 0.5;\n      }\n    };\n  }\n});\nvar now = function () {\n  return performance.now();\n};\n\n/**\n * Controlling groups of tweens\n *\n * Using the TWEEN singleton to manage your tweens can cause issues in large apps with many components.\n * In these cases, you may want to create your own smaller groups of tween\n */\nvar Group = /** @class */function () {\n  function Group() {\n    var tweens = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      tweens[_i] = arguments[_i];\n    }\n    this._tweens = {};\n    this._tweensAddedDuringUpdate = {};\n    this.add.apply(this, tweens);\n  }\n  Group.prototype.getAll = function () {\n    var _this = this;\n    return Object.keys(this._tweens).map(function (tweenId) {\n      return _this._tweens[tweenId];\n    });\n  };\n  Group.prototype.removeAll = function () {\n    this._tweens = {};\n  };\n  Group.prototype.add = function () {\n    var _a;\n    var tweens = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      tweens[_i] = arguments[_i];\n    }\n    for (var _b = 0, tweens_1 = tweens; _b < tweens_1.length; _b++) {\n      var tween = tweens_1[_b];\n      // Remove from any other group first, a tween can only be in one group at a time.\n      // @ts-expect-error library internal access\n      (_a = tween._group) === null || _a === void 0 ? void 0 : _a.remove(tween);\n      // @ts-expect-error library internal access\n      tween._group = this;\n      this._tweens[tween.getId()] = tween;\n      this._tweensAddedDuringUpdate[tween.getId()] = tween;\n    }\n  };\n  Group.prototype.remove = function () {\n    var tweens = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      tweens[_i] = arguments[_i];\n    }\n    for (var _a = 0, tweens_2 = tweens; _a < tweens_2.length; _a++) {\n      var tween = tweens_2[_a];\n      // @ts-expect-error library internal access\n      tween._group = undefined;\n      delete this._tweens[tween.getId()];\n      delete this._tweensAddedDuringUpdate[tween.getId()];\n    }\n  };\n  /** Return true if all tweens in the group are not paused or playing. */\n  Group.prototype.allStopped = function () {\n    return this.getAll().every(function (tween) {\n      return !tween.isPlaying();\n    });\n  };\n  Group.prototype.update = function (time, preserve) {\n    if (time === void 0) {\n      time = now();\n    }\n    if (preserve === void 0) {\n      preserve = true;\n    }\n    var tweenIds = Object.keys(this._tweens);\n    if (tweenIds.length === 0) return;\n    // Tweens are updated in \"batches\". If you add a new tween during an\n    // update, then the new tween will be updated in the next batch.\n    // If you remove a tween during an update, it may or may not be updated.\n    // However, if the removed tween was added during the current batch,\n    // then it will not be updated.\n    while (tweenIds.length > 0) {\n      this._tweensAddedDuringUpdate = {};\n      for (var i = 0; i < tweenIds.length; i++) {\n        var tween = this._tweens[tweenIds[i]];\n        var autoStart = !preserve;\n        if (tween && tween.update(time, autoStart) === false && !preserve) this.remove(tween);\n      }\n      tweenIds = Object.keys(this._tweensAddedDuringUpdate);\n    }\n  };\n  return Group;\n}();\n\n/**\n *\n */\nvar Interpolation = {\n  Linear: function (v, k) {\n    var m = v.length - 1;\n    var f = m * k;\n    var i = Math.floor(f);\n    var fn = Interpolation.Utils.Linear;\n    if (k < 0) {\n      return fn(v[0], v[1], f);\n    }\n    if (k > 1) {\n      return fn(v[m], v[m - 1], m - f);\n    }\n    return fn(v[i], v[i + 1 > m ? m : i + 1], f - i);\n  },\n  Bezier: function (v, k) {\n    var b = 0;\n    var n = v.length - 1;\n    var pw = Math.pow;\n    var bn = Interpolation.Utils.Bernstein;\n    for (var i = 0; i <= n; i++) {\n      b += pw(1 - k, n - i) * pw(k, i) * v[i] * bn(n, i);\n    }\n    return b;\n  },\n  CatmullRom: function (v, k) {\n    var m = v.length - 1;\n    var f = m * k;\n    var i = Math.floor(f);\n    var fn = Interpolation.Utils.CatmullRom;\n    if (v[0] === v[m]) {\n      if (k < 0) {\n        i = Math.floor(f = m * (1 + k));\n      }\n      return fn(v[(i - 1 + m) % m], v[i], v[(i + 1) % m], v[(i + 2) % m], f - i);\n    } else {\n      if (k < 0) {\n        return v[0] - (fn(v[0], v[0], v[1], v[1], -f) - v[0]);\n      }\n      if (k > 1) {\n        return v[m] - (fn(v[m], v[m], v[m - 1], v[m - 1], f - m) - v[m]);\n      }\n      return fn(v[i ? i - 1 : 0], v[i], v[m < i + 1 ? m : i + 1], v[m < i + 2 ? m : i + 2], f - i);\n    }\n  },\n  Utils: {\n    Linear: function (p0, p1, t) {\n      return (p1 - p0) * t + p0;\n    },\n    Bernstein: function (n, i) {\n      var fc = Interpolation.Utils.Factorial;\n      return fc(n) / fc(i) / fc(n - i);\n    },\n    Factorial: function () {\n      var a = [1];\n      return function (n) {\n        var s = 1;\n        if (a[n]) {\n          return a[n];\n        }\n        for (var i = n; i > 1; i--) {\n          s *= i;\n        }\n        a[n] = s;\n        return s;\n      };\n    }(),\n    CatmullRom: function (p0, p1, p2, p3, t) {\n      var v0 = (p2 - p0) * 0.5;\n      var v1 = (p3 - p1) * 0.5;\n      var t2 = t * t;\n      var t3 = t * t2;\n      return (2 * p1 - 2 * p2 + v0 + v1) * t3 + (-3 * p1 + 3 * p2 - 2 * v0 - v1) * t2 + v0 * t + p1;\n    }\n  }\n};\n\n/**\n * Utils\n */\nvar Sequence = /** @class */function () {\n  function Sequence() {}\n  Sequence.nextId = function () {\n    return Sequence._nextId++;\n  };\n  Sequence._nextId = 0;\n  return Sequence;\n}();\nvar mainGroup = new Group();\n\n/**\n * Tween.js - Licensed under the MIT license\n * https://github.com/tweenjs/tween.js\n * ----------------------------------------------\n *\n * See https://github.com/tweenjs/tween.js/graphs/contributors for the full list of contributors.\n * Thank you all, you're awesome!\n */\nvar Tween = /** @class */function () {\n  function Tween(object, group) {\n    this._isPaused = false;\n    this._pauseStart = 0;\n    this._valuesStart = {};\n    this._valuesEnd = {};\n    this._valuesStartRepeat = {};\n    this._duration = 1000;\n    this._isDynamic = false;\n    this._initialRepeat = 0;\n    this._repeat = 0;\n    this._yoyo = false;\n    this._isPlaying = false;\n    this._reversed = false;\n    this._delayTime = 0;\n    this._startTime = 0;\n    this._easingFunction = Easing.Linear.None;\n    this._interpolationFunction = Interpolation.Linear;\n    // eslint-disable-next-line\n    this._chainedTweens = [];\n    this._onStartCallbackFired = false;\n    this._onEveryStartCallbackFired = false;\n    this._id = Sequence.nextId();\n    this._isChainStopped = false;\n    this._propertiesAreSetUp = false;\n    this._goToEnd = false;\n    this._object = object;\n    if (typeof group === 'object') {\n      this._group = group;\n      group.add(this);\n    }\n    // Use \"true\" to restore old behavior (will be removed in future release).\n    else if (group === true) {\n      this._group = mainGroup;\n      mainGroup.add(this);\n    }\n  }\n  Tween.prototype.getId = function () {\n    return this._id;\n  };\n  Tween.prototype.isPlaying = function () {\n    return this._isPlaying;\n  };\n  Tween.prototype.isPaused = function () {\n    return this._isPaused;\n  };\n  Tween.prototype.getDuration = function () {\n    return this._duration;\n  };\n  Tween.prototype.to = function (target, duration) {\n    if (duration === void 0) {\n      duration = 1000;\n    }\n    if (this._isPlaying) throw new Error('Can not call Tween.to() while Tween is already started or paused. Stop the Tween first.');\n    this._valuesEnd = target;\n    this._propertiesAreSetUp = false;\n    this._duration = duration < 0 ? 0 : duration;\n    return this;\n  };\n  Tween.prototype.duration = function (duration) {\n    if (duration === void 0) {\n      duration = 1000;\n    }\n    this._duration = duration < 0 ? 0 : duration;\n    return this;\n  };\n  Tween.prototype.dynamic = function (dynamic) {\n    if (dynamic === void 0) {\n      dynamic = false;\n    }\n    this._isDynamic = dynamic;\n    return this;\n  };\n  Tween.prototype.start = function (time, overrideStartingValues) {\n    if (time === void 0) {\n      time = now();\n    }\n    if (overrideStartingValues === void 0) {\n      overrideStartingValues = false;\n    }\n    if (this._isPlaying) {\n      return this;\n    }\n    this._repeat = this._initialRepeat;\n    if (this._reversed) {\n      // If we were reversed (f.e. using the yoyo feature) then we need to\n      // flip the tween direction back to forward.\n      this._reversed = false;\n      for (var property in this._valuesStartRepeat) {\n        this._swapEndStartRepeatValues(property);\n        this._valuesStart[property] = this._valuesStartRepeat[property];\n      }\n    }\n    this._isPlaying = true;\n    this._isPaused = false;\n    this._onStartCallbackFired = false;\n    this._onEveryStartCallbackFired = false;\n    this._isChainStopped = false;\n    this._startTime = time;\n    this._startTime += this._delayTime;\n    if (!this._propertiesAreSetUp || overrideStartingValues) {\n      this._propertiesAreSetUp = true;\n      // If dynamic is not enabled, clone the end values instead of using the passed-in end values.\n      if (!this._isDynamic) {\n        var tmp = {};\n        for (var prop in this._valuesEnd) tmp[prop] = this._valuesEnd[prop];\n        this._valuesEnd = tmp;\n      }\n      this._setupProperties(this._object, this._valuesStart, this._valuesEnd, this._valuesStartRepeat, overrideStartingValues);\n    }\n    return this;\n  };\n  Tween.prototype.startFromCurrentValues = function (time) {\n    return this.start(time, true);\n  };\n  Tween.prototype._setupProperties = function (_object, _valuesStart, _valuesEnd, _valuesStartRepeat, overrideStartingValues) {\n    for (var property in _valuesEnd) {\n      var startValue = _object[property];\n      var startValueIsArray = Array.isArray(startValue);\n      var propType = startValueIsArray ? 'array' : typeof startValue;\n      var isInterpolationList = !startValueIsArray && Array.isArray(_valuesEnd[property]);\n      // If `to()` specifies a property that doesn't exist in the source object,\n      // we should not set that property in the object\n      if (propType === 'undefined' || propType === 'function') {\n        continue;\n      }\n      // Check if an Array was provided as property value\n      if (isInterpolationList) {\n        var endValues = _valuesEnd[property];\n        if (endValues.length === 0) {\n          continue;\n        }\n        // Handle an array of relative values.\n        // Creates a local copy of the Array with the start value at the front\n        var temp = [startValue];\n        for (var i = 0, l = endValues.length; i < l; i += 1) {\n          var value = this._handleRelativeValue(startValue, endValues[i]);\n          if (isNaN(value)) {\n            isInterpolationList = false;\n            console.warn('Found invalid interpolation list. Skipping.');\n            break;\n          }\n          temp.push(value);\n        }\n        if (isInterpolationList) {\n          // if (_valuesStart[property] === undefined) { // handle end values only the first time. NOT NEEDED? setupProperties is now guarded by _propertiesAreSetUp.\n          _valuesEnd[property] = temp;\n          // }\n        }\n      }\n      // handle the deepness of the values\n      if ((propType === 'object' || startValueIsArray) && startValue && !isInterpolationList) {\n        _valuesStart[property] = startValueIsArray ? [] : {};\n        var nestedObject = startValue;\n        for (var prop in nestedObject) {\n          _valuesStart[property][prop] = nestedObject[prop];\n        }\n        // TODO? repeat nested values? And yoyo? And array values?\n        _valuesStartRepeat[property] = startValueIsArray ? [] : {};\n        var endValues = _valuesEnd[property];\n        // If dynamic is not enabled, clone the end values instead of using the passed-in end values.\n        if (!this._isDynamic) {\n          var tmp = {};\n          for (var prop in endValues) tmp[prop] = endValues[prop];\n          _valuesEnd[property] = endValues = tmp;\n        }\n        this._setupProperties(nestedObject, _valuesStart[property], endValues, _valuesStartRepeat[property], overrideStartingValues);\n      } else {\n        // Save the starting value, but only once unless override is requested.\n        if (typeof _valuesStart[property] === 'undefined' || overrideStartingValues) {\n          _valuesStart[property] = startValue;\n        }\n        if (!startValueIsArray) {\n          // eslint-disable-next-line\n          // @ts-ignore FIXME?\n          _valuesStart[property] *= 1.0; // Ensures we're using numbers, not strings\n        }\n        if (isInterpolationList) {\n          // eslint-disable-next-line\n          // @ts-ignore FIXME?\n          _valuesStartRepeat[property] = _valuesEnd[property].slice().reverse();\n        } else {\n          _valuesStartRepeat[property] = _valuesStart[property] || 0;\n        }\n      }\n    }\n  };\n  Tween.prototype.stop = function () {\n    if (!this._isChainStopped) {\n      this._isChainStopped = true;\n      this.stopChainedTweens();\n    }\n    if (!this._isPlaying) {\n      return this;\n    }\n    this._isPlaying = false;\n    this._isPaused = false;\n    if (this._onStopCallback) {\n      this._onStopCallback(this._object);\n    }\n    return this;\n  };\n  Tween.prototype.end = function () {\n    this._goToEnd = true;\n    this.update(this._startTime + this._duration);\n    return this;\n  };\n  Tween.prototype.pause = function (time) {\n    if (time === void 0) {\n      time = now();\n    }\n    if (this._isPaused || !this._isPlaying) {\n      return this;\n    }\n    this._isPaused = true;\n    this._pauseStart = time;\n    return this;\n  };\n  Tween.prototype.resume = function (time) {\n    if (time === void 0) {\n      time = now();\n    }\n    if (!this._isPaused || !this._isPlaying) {\n      return this;\n    }\n    this._isPaused = false;\n    this._startTime += time - this._pauseStart;\n    this._pauseStart = 0;\n    return this;\n  };\n  Tween.prototype.stopChainedTweens = function () {\n    for (var i = 0, numChainedTweens = this._chainedTweens.length; i < numChainedTweens; i++) {\n      this._chainedTweens[i].stop();\n    }\n    return this;\n  };\n  Tween.prototype.group = function (group) {\n    if (!group) {\n      console.warn('tween.group() without args has been removed, use group.add(tween) instead.');\n      return this;\n    }\n    group.add(this);\n    return this;\n  };\n  /**\n   * Removes the tween from whichever group it is in.\n   */\n  Tween.prototype.remove = function () {\n    var _a;\n    (_a = this._group) === null || _a === void 0 ? void 0 : _a.remove(this);\n    return this;\n  };\n  Tween.prototype.delay = function (amount) {\n    if (amount === void 0) {\n      amount = 0;\n    }\n    this._delayTime = amount;\n    return this;\n  };\n  Tween.prototype.repeat = function (times) {\n    if (times === void 0) {\n      times = 0;\n    }\n    this._initialRepeat = times;\n    this._repeat = times;\n    return this;\n  };\n  Tween.prototype.repeatDelay = function (amount) {\n    this._repeatDelayTime = amount;\n    return this;\n  };\n  Tween.prototype.yoyo = function (yoyo) {\n    if (yoyo === void 0) {\n      yoyo = false;\n    }\n    this._yoyo = yoyo;\n    return this;\n  };\n  Tween.prototype.easing = function (easingFunction) {\n    if (easingFunction === void 0) {\n      easingFunction = Easing.Linear.None;\n    }\n    this._easingFunction = easingFunction;\n    return this;\n  };\n  Tween.prototype.interpolation = function (interpolationFunction) {\n    if (interpolationFunction === void 0) {\n      interpolationFunction = Interpolation.Linear;\n    }\n    this._interpolationFunction = interpolationFunction;\n    return this;\n  };\n  // eslint-disable-next-line\n  Tween.prototype.chain = function () {\n    var tweens = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      tweens[_i] = arguments[_i];\n    }\n    this._chainedTweens = tweens;\n    return this;\n  };\n  Tween.prototype.onStart = function (callback) {\n    this._onStartCallback = callback;\n    return this;\n  };\n  Tween.prototype.onEveryStart = function (callback) {\n    this._onEveryStartCallback = callback;\n    return this;\n  };\n  Tween.prototype.onUpdate = function (callback) {\n    this._onUpdateCallback = callback;\n    return this;\n  };\n  Tween.prototype.onRepeat = function (callback) {\n    this._onRepeatCallback = callback;\n    return this;\n  };\n  Tween.prototype.onComplete = function (callback) {\n    this._onCompleteCallback = callback;\n    return this;\n  };\n  Tween.prototype.onStop = function (callback) {\n    this._onStopCallback = callback;\n    return this;\n  };\n  /**\n   * @returns true if the tween is still playing after the update, false\n   * otherwise (calling update on a paused tween still returns true because\n   * it is still playing, just paused).\n   *\n   * @param autoStart - When true, calling update will implicitly call start()\n   * as well. Note, if you stop() or end() the tween, but are still calling\n   * update(), it will start again!\n   */\n  Tween.prototype.update = function (time, autoStart) {\n    var _this = this;\n    var _a;\n    if (time === void 0) {\n      time = now();\n    }\n    if (autoStart === void 0) {\n      autoStart = Tween.autoStartOnUpdate;\n    }\n    if (this._isPaused) return true;\n    var property;\n    if (!this._goToEnd && !this._isPlaying) {\n      if (autoStart) this.start(time, true);else return false;\n    }\n    this._goToEnd = false;\n    if (time < this._startTime) {\n      return true;\n    }\n    if (this._onStartCallbackFired === false) {\n      if (this._onStartCallback) {\n        this._onStartCallback(this._object);\n      }\n      this._onStartCallbackFired = true;\n    }\n    if (this._onEveryStartCallbackFired === false) {\n      if (this._onEveryStartCallback) {\n        this._onEveryStartCallback(this._object);\n      }\n      this._onEveryStartCallbackFired = true;\n    }\n    var elapsedTime = time - this._startTime;\n    var durationAndDelay = this._duration + ((_a = this._repeatDelayTime) !== null && _a !== void 0 ? _a : this._delayTime);\n    var totalTime = this._duration + this._repeat * durationAndDelay;\n    var calculateElapsedPortion = function () {\n      if (_this._duration === 0) return 1;\n      if (elapsedTime > totalTime) {\n        return 1;\n      }\n      var timesRepeated = Math.trunc(elapsedTime / durationAndDelay);\n      var timeIntoCurrentRepeat = elapsedTime - timesRepeated * durationAndDelay;\n      // TODO use %?\n      // const timeIntoCurrentRepeat = elapsedTime % durationAndDelay\n      var portion = Math.min(timeIntoCurrentRepeat / _this._duration, 1);\n      if (portion === 0 && elapsedTime === _this._duration) {\n        return 1;\n      }\n      return portion;\n    };\n    var elapsed = calculateElapsedPortion();\n    var value = this._easingFunction(elapsed);\n    // properties transformations\n    this._updateProperties(this._object, this._valuesStart, this._valuesEnd, value);\n    if (this._onUpdateCallback) {\n      this._onUpdateCallback(this._object, elapsed);\n    }\n    if (this._duration === 0 || elapsedTime >= this._duration) {\n      if (this._repeat > 0) {\n        var completeCount = Math.min(Math.trunc((elapsedTime - this._duration) / durationAndDelay) + 1, this._repeat);\n        if (isFinite(this._repeat)) {\n          this._repeat -= completeCount;\n        }\n        // Reassign starting values, restart by making startTime = now\n        for (property in this._valuesStartRepeat) {\n          if (!this._yoyo && typeof this._valuesEnd[property] === 'string') {\n            this._valuesStartRepeat[property] =\n            // eslint-disable-next-line\n            // @ts-ignore FIXME?\n            this._valuesStartRepeat[property] + parseFloat(this._valuesEnd[property]);\n          }\n          if (this._yoyo) {\n            this._swapEndStartRepeatValues(property);\n          }\n          this._valuesStart[property] = this._valuesStartRepeat[property];\n        }\n        if (this._yoyo) {\n          this._reversed = !this._reversed;\n        }\n        this._startTime += durationAndDelay * completeCount;\n        if (this._onRepeatCallback) {\n          this._onRepeatCallback(this._object);\n        }\n        this._onEveryStartCallbackFired = false;\n        return true;\n      } else {\n        if (this._onCompleteCallback) {\n          this._onCompleteCallback(this._object);\n        }\n        for (var i = 0, numChainedTweens = this._chainedTweens.length; i < numChainedTweens; i++) {\n          // Make the chained tweens start exactly at the time they should,\n          // even if the `update()` method was called way past the duration of the tween\n          this._chainedTweens[i].start(this._startTime + this._duration, false);\n        }\n        this._isPlaying = false;\n        return false;\n      }\n    }\n    return true;\n  };\n  Tween.prototype._updateProperties = function (_object, _valuesStart, _valuesEnd, value) {\n    for (var property in _valuesEnd) {\n      // Don't update properties that do not exist in the source object\n      if (_valuesStart[property] === undefined) {\n        continue;\n      }\n      var start = _valuesStart[property] || 0;\n      var end = _valuesEnd[property];\n      var startIsArray = Array.isArray(_object[property]);\n      var endIsArray = Array.isArray(end);\n      var isInterpolationList = !startIsArray && endIsArray;\n      if (isInterpolationList) {\n        _object[property] = this._interpolationFunction(end, value);\n      } else if (typeof end === 'object' && end) {\n        // eslint-disable-next-line\n        // @ts-ignore FIXME?\n        this._updateProperties(_object[property], start, end, value);\n      } else {\n        // Parses relative end values with start as base (e.g.: +10, -3)\n        end = this._handleRelativeValue(start, end);\n        // Protect against non numeric properties.\n        if (typeof end === 'number') {\n          // eslint-disable-next-line\n          // @ts-ignore FIXME?\n          _object[property] = start + (end - start) * value;\n        }\n      }\n    }\n  };\n  Tween.prototype._handleRelativeValue = function (start, end) {\n    if (typeof end !== 'string') {\n      return end;\n    }\n    if (end.charAt(0) === '+' || end.charAt(0) === '-') {\n      return start + parseFloat(end);\n    }\n    return parseFloat(end);\n  };\n  Tween.prototype._swapEndStartRepeatValues = function (property) {\n    var tmp = this._valuesStartRepeat[property];\n    var endValue = this._valuesEnd[property];\n    if (typeof endValue === 'string') {\n      this._valuesStartRepeat[property] = this._valuesStartRepeat[property] + parseFloat(endValue);\n    } else {\n      this._valuesStartRepeat[property] = this._valuesEnd[property];\n    }\n    this._valuesEnd[property] = tmp;\n  };\n  Tween.autoStartOnUpdate = false;\n  return Tween;\n}();\nvar VERSION = '25.0.0';\n\n/**\n * Tween.js - Licensed under the MIT license\n * https://github.com/tweenjs/tween.js\n * ----------------------------------------------\n *\n * See https://github.com/tweenjs/tween.js/graphs/contributors for the full list of contributors.\n * Thank you all, you're awesome!\n */\nvar nextId = Sequence.nextId;\n/**\n * Controlling groups of tweens\n *\n * Using the TWEEN singleton to manage your tweens can cause issues in large apps with many components.\n * In these cases, you may want to create your own smaller groups of tweens.\n */\nvar TWEEN = mainGroup;\n// This is the best way to export things in a way that's compatible with both ES\n// Modules and CommonJS, without build hacks, and so as not to break the\n// existing API.\n// https://github.com/rollup/rollup/issues/1961#issuecomment-423037881\n/**\n * @deprecated The global TWEEN Group will be removed in a following major\n * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n * group.\n *\n * Old code:\n *\n * ```js\n * import * as TWEEN from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new TWEEN.Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   TWEEN.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n *\n * New code:\n *\n * ```js\n * import {Tween, Group} from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * const group = new Group()\n * group.add(tween)\n * group.add(tween2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   group.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n */\nvar getAll = TWEEN.getAll.bind(TWEEN);\n/**\n * @deprecated The global TWEEN Group will be removed in a following major\n * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n * group.\n *\n * Old code:\n *\n * ```js\n * import * as TWEEN from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new TWEEN.Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   TWEEN.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n *\n * New code:\n *\n * ```js\n * import {Tween, Group} from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * const group = new Group()\n * group.add(tween)\n * group.add(tween2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   group.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n */\nvar removeAll = TWEEN.removeAll.bind(TWEEN);\n/**\n * @deprecated The global TWEEN Group will be removed in a following major\n * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n * group.\n *\n * Old code:\n *\n * ```js\n * import * as TWEEN from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new TWEEN.Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   TWEEN.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n *\n * New code:\n *\n * ```js\n * import {Tween, Group} from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * const group = new Group()\n * group.add(tween)\n * group.add(tween2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   group.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n */\nvar add = TWEEN.add.bind(TWEEN);\n/**\n * @deprecated The global TWEEN Group will be removed in a following major\n * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n * group.\n *\n * Old code:\n *\n * ```js\n * import * as TWEEN from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new TWEEN.Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   TWEEN.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n *\n * New code:\n *\n * ```js\n * import {Tween, Group} from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * const group = new Group()\n * group.add(tween)\n * group.add(tween2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   group.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n */\nvar remove = TWEEN.remove.bind(TWEEN);\n/**\n * @deprecated The global TWEEN Group will be removed in a following major\n * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n * group.\n *\n * Old code:\n *\n * ```js\n * import * as TWEEN from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new TWEEN.Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   TWEEN.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n *\n * New code:\n *\n * ```js\n * import {Tween, Group} from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * const group = new Group()\n * group.add(tween)\n * group.add(tween2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   group.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n */\nvar update = TWEEN.update.bind(TWEEN);\nvar exports = {\n  Easing: Easing,\n  Group: Group,\n  Interpolation: Interpolation,\n  now: now,\n  Sequence: Sequence,\n  nextId: nextId,\n  Tween: Tween,\n  VERSION: VERSION,\n  /**\n   * @deprecated The global TWEEN Group will be removed in a following major\n   * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n   * group.\n   *\n   * Old code:\n   *\n   * ```js\n   * import * as TWEEN from '@tweenjs/tween.js'\n   *\n   * //...\n   *\n   * const tween = new TWEEN.Tween(obj)\n   * const tween2 = new TWEEN.Tween(obj2)\n   *\n   * //...\n   *\n   * requestAnimationFrame(function loop(time) {\n   *   TWEEN.update(time)\n   *   requestAnimationFrame(loop)\n   * })\n   * ```\n   *\n   * New code:\n   *\n   * ```js\n   * import {Tween, Group} from '@tweenjs/tween.js'\n   *\n   * //...\n   *\n   * const tween = new Tween(obj)\n   * const tween2 = new TWEEN.Tween(obj2)\n   *\n   * //...\n   *\n   * const group = new Group()\n   * group.add(tween)\n   * group.add(tween2)\n   *\n   * //...\n   *\n   * requestAnimationFrame(function loop(time) {\n   *   group.update(time)\n   *   requestAnimationFrame(loop)\n   * })\n   * ```\n   */\n  getAll: getAll,\n  /**\n   * @deprecated The global TWEEN Group will be removed in a following major\n   * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n   * group.\n   *\n   * Old code:\n   *\n   * ```js\n   * import * as TWEEN from '@tweenjs/tween.js'\n   *\n   * //...\n   *\n   * const tween = new TWEEN.Tween(obj)\n   * const tween2 = new TWEEN.Tween(obj2)\n   *\n   * //...\n   *\n   * requestAnimationFrame(function loop(time) {\n   *   TWEEN.update(time)\n   *   requestAnimationFrame(loop)\n   * })\n   * ```\n   *\n   * New code:\n   *\n   * ```js\n   * import {Tween, Group} from '@tweenjs/tween.js'\n   *\n   * //...\n   *\n   * const tween = new Tween(obj)\n   * const tween2 = new TWEEN.Tween(obj2)\n   *\n   * //...\n   *\n   * const group = new Group()\n   * group.add(tween)\n   * group.add(tween2)\n   *\n   * //...\n   *\n   * requestAnimationFrame(function loop(time) {\n   *   group.update(time)\n   *   requestAnimationFrame(loop)\n   * })\n   * ```\n   */\n  removeAll: removeAll,\n  /**\n   * @deprecated The global TWEEN Group will be removed in a following major\n   * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n   * group.\n   *\n   * Old code:\n   *\n   * ```js\n   * import * as TWEEN from '@tweenjs/tween.js'\n   *\n   * //...\n   *\n   * const tween = new TWEEN.Tween(obj)\n   * const tween2 = new TWEEN.Tween(obj2)\n   *\n   * //...\n   *\n   * requestAnimationFrame(function loop(time) {\n   *   TWEEN.update(time)\n   *   requestAnimationFrame(loop)\n   * })\n   * ```\n   *\n   * New code:\n   *\n   * ```js\n   * import {Tween, Group} from '@tweenjs/tween.js'\n   *\n   * //...\n   *\n   * const tween = new Tween(obj)\n   * const tween2 = new TWEEN.Tween(obj2)\n   *\n   * //...\n   *\n   * const group = new Group()\n   * group.add(tween)\n   * group.add(tween2)\n   *\n   * //...\n   *\n   * requestAnimationFrame(function loop(time) {\n   *   group.update(time)\n   *   requestAnimationFrame(loop)\n   * })\n   * ```\n   */\n  add: add,\n  /**\n   * @deprecated The global TWEEN Group will be removed in a following major\n   * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n   * group.\n   *\n   * Old code:\n   *\n   * ```js\n   * import * as TWEEN from '@tweenjs/tween.js'\n   *\n   * //...\n   *\n   * const tween = new TWEEN.Tween(obj)\n   * const tween2 = new TWEEN.Tween(obj2)\n   *\n   * //...\n   *\n   * requestAnimationFrame(function loop(time) {\n   *   TWEEN.update(time)\n   *   requestAnimationFrame(loop)\n   * })\n   * ```\n   *\n   * New code:\n   *\n   * ```js\n   * import {Tween, Group} from '@tweenjs/tween.js'\n   *\n   * //...\n   *\n   * const tween = new Tween(obj)\n   * const tween2 = new TWEEN.Tween(obj2)\n   *\n   * //...\n   *\n   * const group = new Group()\n   * group.add(tween)\n   * group.add(tween2)\n   *\n   * //...\n   *\n   * requestAnimationFrame(function loop(time) {\n   *   group.update(time)\n   *   requestAnimationFrame(loop)\n   * })\n   * ```\n   */\n  remove: remove,\n  /**\n   * @deprecated The global TWEEN Group will be removed in a following major\n   * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n   * group.\n   *\n   * Old code:\n   *\n   * ```js\n   * import * as TWEEN from '@tweenjs/tween.js'\n   *\n   * //...\n   *\n   * const tween = new TWEEN.Tween(obj)\n   * const tween2 = new TWEEN.Tween(obj2)\n   *\n   * //...\n   *\n   * requestAnimationFrame(function loop(time) {\n   *   TWEEN.update(time)\n   *   requestAnimationFrame(loop)\n   * })\n   * ```\n   *\n   * New code:\n   *\n   * ```js\n   * import {Tween, Group} from '@tweenjs/tween.js'\n   *\n   * //...\n   *\n   * const tween = new Tween(obj)\n   * const tween2 = new TWEEN.Tween(obj2)\n   *\n   * //...\n   *\n   * const group = new Group()\n   * group.add(tween)\n   * group.add(tween2)\n   *\n   * //...\n   *\n   * requestAnimationFrame(function loop(time) {\n   *   group.update(time)\n   *   requestAnimationFrame(loop)\n   * })\n   * ```\n   */\n  update: update\n};\nexport { Easing, Group, Interpolation, Sequence, Tween, VERSION, add, exports as default, getAll, nextId, now, remove, removeAll, update };", "map": {"version": 3, "names": ["Easing", "Object", "freeze", "Linear", "None", "amount", "In", "Out", "InOut", "Quadratic", "Cubic", "Quartic", "<PERSON><PERSON><PERSON>", "Sinusoidal", "Math", "sin", "PI", "Exponential", "pow", "Circular", "sqrt", "Elastic", "Back", "s", "<PERSON><PERSON><PERSON>", "generatePow", "power", "Number", "EPSILON", "now", "performance", "Group", "tweens", "_i", "arguments", "length", "_tweens", "_tweensAddedDuringUpdate", "add", "apply", "prototype", "getAll", "_this", "keys", "map", "tweenId", "removeAll", "_a", "_b", "tweens_1", "tween", "_group", "remove", "getId", "tweens_2", "undefined", "allStopped", "every", "isPlaying", "update", "time", "preserve", "tweenIds", "i", "autoStart", "Interpolation", "v", "k", "m", "f", "floor", "fn", "Utils", "<PERSON><PERSON>", "b", "n", "pw", "bn", "<PERSON>", "CatmullRom", "p0", "p1", "t", "fc", "Factorial", "a", "p2", "p3", "v0", "v1", "t2", "t3", "Sequence", "nextId", "_nextId", "mainGroup", "Tween", "object", "group", "_isPaused", "_pauseStart", "_valuesStart", "_valuesEnd", "_valuesStartRepeat", "_duration", "_isDynamic", "_initialRepeat", "_repeat", "_yoyo", "_isPlaying", "_reversed", "_delayTime", "_startTime", "_easingFunction", "_interpolationFunction", "_chainedTweens", "_onStartCallbackFired", "_onEveryStartCallbackFired", "_id", "_isChainStopped", "_propertiesAreSetUp", "_goToEnd", "_object", "isPaused", "getDuration", "to", "target", "duration", "Error", "dynamic", "start", "overrideStartingValues", "property", "_swapEndStartRepeatValues", "tmp", "prop", "_setupProperties", "startFromCurrentValues", "startValue", "startValueIsArray", "Array", "isArray", "propType", "isInterpolationList", "endValues", "temp", "l", "value", "_handleRelativeValue", "isNaN", "console", "warn", "push", "nestedObject", "slice", "reverse", "stop", "stopChainedTweens", "_onStopCallback", "end", "pause", "resume", "numChainedTweens", "delay", "repeat", "times", "repeatDelay", "_repeatDelayTime", "yoyo", "easing", "easingFunction", "interpolation", "interpolationFunction", "chain", "onStart", "callback", "_onStartCallback", "onEveryStart", "_onEveryStartCallback", "onUpdate", "_onUpdateCallback", "onRepeat", "_onRepeatCallback", "onComplete", "_onCompleteCallback", "onStop", "autoStartOnUpdate", "elapsedTime", "durationAndDelay", "totalTime", "calculateElapsedPortion", "timesRepeated", "trunc", "timeIntoCurrentRepeat", "portion", "min", "elapsed", "_updateProperties", "completeCount", "isFinite", "parseFloat", "startIsArray", "endIsArray", "char<PERSON>t", "endValue", "VERSION", "TWEEN", "bind", "exports", "default"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@tweenjs/tween.js/dist/tween.esm.js"], "sourcesContent": ["/**\n * The Ease class provides a collection of easing functions for use with tween.js.\n */\nvar Easing = Object.freeze({\n    Linear: Object.freeze({\n        None: function (amount) {\n            return amount;\n        },\n        In: function (amount) {\n            return amount;\n        },\n        Out: function (amount) {\n            return amount;\n        },\n        InOut: function (amount) {\n            return amount;\n        },\n    }),\n    Quadratic: Object.freeze({\n        In: function (amount) {\n            return amount * amount;\n        },\n        Out: function (amount) {\n            return amount * (2 - amount);\n        },\n        InOut: function (amount) {\n            if ((amount *= 2) < 1) {\n                return 0.5 * amount * amount;\n            }\n            return -0.5 * (--amount * (amount - 2) - 1);\n        },\n    }),\n    Cubic: Object.freeze({\n        In: function (amount) {\n            return amount * amount * amount;\n        },\n        Out: function (amount) {\n            return --amount * amount * amount + 1;\n        },\n        InOut: function (amount) {\n            if ((amount *= 2) < 1) {\n                return 0.5 * amount * amount * amount;\n            }\n            return 0.5 * ((amount -= 2) * amount * amount + 2);\n        },\n    }),\n    Quartic: Object.freeze({\n        In: function (amount) {\n            return amount * amount * amount * amount;\n        },\n        Out: function (amount) {\n            return 1 - --amount * amount * amount * amount;\n        },\n        InOut: function (amount) {\n            if ((amount *= 2) < 1) {\n                return 0.5 * amount * amount * amount * amount;\n            }\n            return -0.5 * ((amount -= 2) * amount * amount * amount - 2);\n        },\n    }),\n    Quintic: Object.freeze({\n        In: function (amount) {\n            return amount * amount * amount * amount * amount;\n        },\n        Out: function (amount) {\n            return --amount * amount * amount * amount * amount + 1;\n        },\n        InOut: function (amount) {\n            if ((amount *= 2) < 1) {\n                return 0.5 * amount * amount * amount * amount * amount;\n            }\n            return 0.5 * ((amount -= 2) * amount * amount * amount * amount + 2);\n        },\n    }),\n    Sinusoidal: Object.freeze({\n        In: function (amount) {\n            return 1 - Math.sin(((1.0 - amount) * Math.PI) / 2);\n        },\n        Out: function (amount) {\n            return Math.sin((amount * Math.PI) / 2);\n        },\n        InOut: function (amount) {\n            return 0.5 * (1 - Math.sin(Math.PI * (0.5 - amount)));\n        },\n    }),\n    Exponential: Object.freeze({\n        In: function (amount) {\n            return amount === 0 ? 0 : Math.pow(1024, amount - 1);\n        },\n        Out: function (amount) {\n            return amount === 1 ? 1 : 1 - Math.pow(2, -10 * amount);\n        },\n        InOut: function (amount) {\n            if (amount === 0) {\n                return 0;\n            }\n            if (amount === 1) {\n                return 1;\n            }\n            if ((amount *= 2) < 1) {\n                return 0.5 * Math.pow(1024, amount - 1);\n            }\n            return 0.5 * (-Math.pow(2, -10 * (amount - 1)) + 2);\n        },\n    }),\n    Circular: Object.freeze({\n        In: function (amount) {\n            return 1 - Math.sqrt(1 - amount * amount);\n        },\n        Out: function (amount) {\n            return Math.sqrt(1 - --amount * amount);\n        },\n        InOut: function (amount) {\n            if ((amount *= 2) < 1) {\n                return -0.5 * (Math.sqrt(1 - amount * amount) - 1);\n            }\n            return 0.5 * (Math.sqrt(1 - (amount -= 2) * amount) + 1);\n        },\n    }),\n    Elastic: Object.freeze({\n        In: function (amount) {\n            if (amount === 0) {\n                return 0;\n            }\n            if (amount === 1) {\n                return 1;\n            }\n            return -Math.pow(2, 10 * (amount - 1)) * Math.sin((amount - 1.1) * 5 * Math.PI);\n        },\n        Out: function (amount) {\n            if (amount === 0) {\n                return 0;\n            }\n            if (amount === 1) {\n                return 1;\n            }\n            return Math.pow(2, -10 * amount) * Math.sin((amount - 0.1) * 5 * Math.PI) + 1;\n        },\n        InOut: function (amount) {\n            if (amount === 0) {\n                return 0;\n            }\n            if (amount === 1) {\n                return 1;\n            }\n            amount *= 2;\n            if (amount < 1) {\n                return -0.5 * Math.pow(2, 10 * (amount - 1)) * Math.sin((amount - 1.1) * 5 * Math.PI);\n            }\n            return 0.5 * Math.pow(2, -10 * (amount - 1)) * Math.sin((amount - 1.1) * 5 * Math.PI) + 1;\n        },\n    }),\n    Back: Object.freeze({\n        In: function (amount) {\n            var s = 1.70158;\n            return amount === 1 ? 1 : amount * amount * ((s + 1) * amount - s);\n        },\n        Out: function (amount) {\n            var s = 1.70158;\n            return amount === 0 ? 0 : --amount * amount * ((s + 1) * amount + s) + 1;\n        },\n        InOut: function (amount) {\n            var s = 1.70158 * 1.525;\n            if ((amount *= 2) < 1) {\n                return 0.5 * (amount * amount * ((s + 1) * amount - s));\n            }\n            return 0.5 * ((amount -= 2) * amount * ((s + 1) * amount + s) + 2);\n        },\n    }),\n    Bounce: Object.freeze({\n        In: function (amount) {\n            return 1 - Easing.Bounce.Out(1 - amount);\n        },\n        Out: function (amount) {\n            if (amount < 1 / 2.75) {\n                return 7.5625 * amount * amount;\n            }\n            else if (amount < 2 / 2.75) {\n                return 7.5625 * (amount -= 1.5 / 2.75) * amount + 0.75;\n            }\n            else if (amount < 2.5 / 2.75) {\n                return 7.5625 * (amount -= 2.25 / 2.75) * amount + 0.9375;\n            }\n            else {\n                return 7.5625 * (amount -= 2.625 / 2.75) * amount + 0.984375;\n            }\n        },\n        InOut: function (amount) {\n            if (amount < 0.5) {\n                return Easing.Bounce.In(amount * 2) * 0.5;\n            }\n            return Easing.Bounce.Out(amount * 2 - 1) * 0.5 + 0.5;\n        },\n    }),\n    generatePow: function (power) {\n        if (power === void 0) { power = 4; }\n        power = power < Number.EPSILON ? Number.EPSILON : power;\n        power = power > 10000 ? 10000 : power;\n        return {\n            In: function (amount) {\n                return Math.pow(amount, power);\n            },\n            Out: function (amount) {\n                return 1 - Math.pow((1 - amount), power);\n            },\n            InOut: function (amount) {\n                if (amount < 0.5) {\n                    return Math.pow((amount * 2), power) / 2;\n                }\n                return (1 - Math.pow((2 - amount * 2), power)) / 2 + 0.5;\n            },\n        };\n    },\n});\n\nvar now = function () { return performance.now(); };\n\n/**\n * Controlling groups of tweens\n *\n * Using the TWEEN singleton to manage your tweens can cause issues in large apps with many components.\n * In these cases, you may want to create your own smaller groups of tween\n */\nvar Group = /** @class */ (function () {\n    function Group() {\n        var tweens = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            tweens[_i] = arguments[_i];\n        }\n        this._tweens = {};\n        this._tweensAddedDuringUpdate = {};\n        this.add.apply(this, tweens);\n    }\n    Group.prototype.getAll = function () {\n        var _this = this;\n        return Object.keys(this._tweens).map(function (tweenId) { return _this._tweens[tweenId]; });\n    };\n    Group.prototype.removeAll = function () {\n        this._tweens = {};\n    };\n    Group.prototype.add = function () {\n        var _a;\n        var tweens = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            tweens[_i] = arguments[_i];\n        }\n        for (var _b = 0, tweens_1 = tweens; _b < tweens_1.length; _b++) {\n            var tween = tweens_1[_b];\n            // Remove from any other group first, a tween can only be in one group at a time.\n            // @ts-expect-error library internal access\n            (_a = tween._group) === null || _a === void 0 ? void 0 : _a.remove(tween);\n            // @ts-expect-error library internal access\n            tween._group = this;\n            this._tweens[tween.getId()] = tween;\n            this._tweensAddedDuringUpdate[tween.getId()] = tween;\n        }\n    };\n    Group.prototype.remove = function () {\n        var tweens = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            tweens[_i] = arguments[_i];\n        }\n        for (var _a = 0, tweens_2 = tweens; _a < tweens_2.length; _a++) {\n            var tween = tweens_2[_a];\n            // @ts-expect-error library internal access\n            tween._group = undefined;\n            delete this._tweens[tween.getId()];\n            delete this._tweensAddedDuringUpdate[tween.getId()];\n        }\n    };\n    /** Return true if all tweens in the group are not paused or playing. */\n    Group.prototype.allStopped = function () {\n        return this.getAll().every(function (tween) { return !tween.isPlaying(); });\n    };\n    Group.prototype.update = function (time, preserve) {\n        if (time === void 0) { time = now(); }\n        if (preserve === void 0) { preserve = true; }\n        var tweenIds = Object.keys(this._tweens);\n        if (tweenIds.length === 0)\n            return;\n        // Tweens are updated in \"batches\". If you add a new tween during an\n        // update, then the new tween will be updated in the next batch.\n        // If you remove a tween during an update, it may or may not be updated.\n        // However, if the removed tween was added during the current batch,\n        // then it will not be updated.\n        while (tweenIds.length > 0) {\n            this._tweensAddedDuringUpdate = {};\n            for (var i = 0; i < tweenIds.length; i++) {\n                var tween = this._tweens[tweenIds[i]];\n                var autoStart = !preserve;\n                if (tween && tween.update(time, autoStart) === false && !preserve)\n                    this.remove(tween);\n            }\n            tweenIds = Object.keys(this._tweensAddedDuringUpdate);\n        }\n    };\n    return Group;\n}());\n\n/**\n *\n */\nvar Interpolation = {\n    Linear: function (v, k) {\n        var m = v.length - 1;\n        var f = m * k;\n        var i = Math.floor(f);\n        var fn = Interpolation.Utils.Linear;\n        if (k < 0) {\n            return fn(v[0], v[1], f);\n        }\n        if (k > 1) {\n            return fn(v[m], v[m - 1], m - f);\n        }\n        return fn(v[i], v[i + 1 > m ? m : i + 1], f - i);\n    },\n    Bezier: function (v, k) {\n        var b = 0;\n        var n = v.length - 1;\n        var pw = Math.pow;\n        var bn = Interpolation.Utils.Bernstein;\n        for (var i = 0; i <= n; i++) {\n            b += pw(1 - k, n - i) * pw(k, i) * v[i] * bn(n, i);\n        }\n        return b;\n    },\n    CatmullRom: function (v, k) {\n        var m = v.length - 1;\n        var f = m * k;\n        var i = Math.floor(f);\n        var fn = Interpolation.Utils.CatmullRom;\n        if (v[0] === v[m]) {\n            if (k < 0) {\n                i = Math.floor((f = m * (1 + k)));\n            }\n            return fn(v[(i - 1 + m) % m], v[i], v[(i + 1) % m], v[(i + 2) % m], f - i);\n        }\n        else {\n            if (k < 0) {\n                return v[0] - (fn(v[0], v[0], v[1], v[1], -f) - v[0]);\n            }\n            if (k > 1) {\n                return v[m] - (fn(v[m], v[m], v[m - 1], v[m - 1], f - m) - v[m]);\n            }\n            return fn(v[i ? i - 1 : 0], v[i], v[m < i + 1 ? m : i + 1], v[m < i + 2 ? m : i + 2], f - i);\n        }\n    },\n    Utils: {\n        Linear: function (p0, p1, t) {\n            return (p1 - p0) * t + p0;\n        },\n        Bernstein: function (n, i) {\n            var fc = Interpolation.Utils.Factorial;\n            return fc(n) / fc(i) / fc(n - i);\n        },\n        Factorial: (function () {\n            var a = [1];\n            return function (n) {\n                var s = 1;\n                if (a[n]) {\n                    return a[n];\n                }\n                for (var i = n; i > 1; i--) {\n                    s *= i;\n                }\n                a[n] = s;\n                return s;\n            };\n        })(),\n        CatmullRom: function (p0, p1, p2, p3, t) {\n            var v0 = (p2 - p0) * 0.5;\n            var v1 = (p3 - p1) * 0.5;\n            var t2 = t * t;\n            var t3 = t * t2;\n            return (2 * p1 - 2 * p2 + v0 + v1) * t3 + (-3 * p1 + 3 * p2 - 2 * v0 - v1) * t2 + v0 * t + p1;\n        },\n    },\n};\n\n/**\n * Utils\n */\nvar Sequence = /** @class */ (function () {\n    function Sequence() {\n    }\n    Sequence.nextId = function () {\n        return Sequence._nextId++;\n    };\n    Sequence._nextId = 0;\n    return Sequence;\n}());\n\nvar mainGroup = new Group();\n\n/**\n * Tween.js - Licensed under the MIT license\n * https://github.com/tweenjs/tween.js\n * ----------------------------------------------\n *\n * See https://github.com/tweenjs/tween.js/graphs/contributors for the full list of contributors.\n * Thank you all, you're awesome!\n */\nvar Tween = /** @class */ (function () {\n    function Tween(object, group) {\n        this._isPaused = false;\n        this._pauseStart = 0;\n        this._valuesStart = {};\n        this._valuesEnd = {};\n        this._valuesStartRepeat = {};\n        this._duration = 1000;\n        this._isDynamic = false;\n        this._initialRepeat = 0;\n        this._repeat = 0;\n        this._yoyo = false;\n        this._isPlaying = false;\n        this._reversed = false;\n        this._delayTime = 0;\n        this._startTime = 0;\n        this._easingFunction = Easing.Linear.None;\n        this._interpolationFunction = Interpolation.Linear;\n        // eslint-disable-next-line\n        this._chainedTweens = [];\n        this._onStartCallbackFired = false;\n        this._onEveryStartCallbackFired = false;\n        this._id = Sequence.nextId();\n        this._isChainStopped = false;\n        this._propertiesAreSetUp = false;\n        this._goToEnd = false;\n        this._object = object;\n        if (typeof group === 'object') {\n            this._group = group;\n            group.add(this);\n        }\n        // Use \"true\" to restore old behavior (will be removed in future release).\n        else if (group === true) {\n            this._group = mainGroup;\n            mainGroup.add(this);\n        }\n    }\n    Tween.prototype.getId = function () {\n        return this._id;\n    };\n    Tween.prototype.isPlaying = function () {\n        return this._isPlaying;\n    };\n    Tween.prototype.isPaused = function () {\n        return this._isPaused;\n    };\n    Tween.prototype.getDuration = function () {\n        return this._duration;\n    };\n    Tween.prototype.to = function (target, duration) {\n        if (duration === void 0) { duration = 1000; }\n        if (this._isPlaying)\n            throw new Error('Can not call Tween.to() while Tween is already started or paused. Stop the Tween first.');\n        this._valuesEnd = target;\n        this._propertiesAreSetUp = false;\n        this._duration = duration < 0 ? 0 : duration;\n        return this;\n    };\n    Tween.prototype.duration = function (duration) {\n        if (duration === void 0) { duration = 1000; }\n        this._duration = duration < 0 ? 0 : duration;\n        return this;\n    };\n    Tween.prototype.dynamic = function (dynamic) {\n        if (dynamic === void 0) { dynamic = false; }\n        this._isDynamic = dynamic;\n        return this;\n    };\n    Tween.prototype.start = function (time, overrideStartingValues) {\n        if (time === void 0) { time = now(); }\n        if (overrideStartingValues === void 0) { overrideStartingValues = false; }\n        if (this._isPlaying) {\n            return this;\n        }\n        this._repeat = this._initialRepeat;\n        if (this._reversed) {\n            // If we were reversed (f.e. using the yoyo feature) then we need to\n            // flip the tween direction back to forward.\n            this._reversed = false;\n            for (var property in this._valuesStartRepeat) {\n                this._swapEndStartRepeatValues(property);\n                this._valuesStart[property] = this._valuesStartRepeat[property];\n            }\n        }\n        this._isPlaying = true;\n        this._isPaused = false;\n        this._onStartCallbackFired = false;\n        this._onEveryStartCallbackFired = false;\n        this._isChainStopped = false;\n        this._startTime = time;\n        this._startTime += this._delayTime;\n        if (!this._propertiesAreSetUp || overrideStartingValues) {\n            this._propertiesAreSetUp = true;\n            // If dynamic is not enabled, clone the end values instead of using the passed-in end values.\n            if (!this._isDynamic) {\n                var tmp = {};\n                for (var prop in this._valuesEnd)\n                    tmp[prop] = this._valuesEnd[prop];\n                this._valuesEnd = tmp;\n            }\n            this._setupProperties(this._object, this._valuesStart, this._valuesEnd, this._valuesStartRepeat, overrideStartingValues);\n        }\n        return this;\n    };\n    Tween.prototype.startFromCurrentValues = function (time) {\n        return this.start(time, true);\n    };\n    Tween.prototype._setupProperties = function (_object, _valuesStart, _valuesEnd, _valuesStartRepeat, overrideStartingValues) {\n        for (var property in _valuesEnd) {\n            var startValue = _object[property];\n            var startValueIsArray = Array.isArray(startValue);\n            var propType = startValueIsArray ? 'array' : typeof startValue;\n            var isInterpolationList = !startValueIsArray && Array.isArray(_valuesEnd[property]);\n            // If `to()` specifies a property that doesn't exist in the source object,\n            // we should not set that property in the object\n            if (propType === 'undefined' || propType === 'function') {\n                continue;\n            }\n            // Check if an Array was provided as property value\n            if (isInterpolationList) {\n                var endValues = _valuesEnd[property];\n                if (endValues.length === 0) {\n                    continue;\n                }\n                // Handle an array of relative values.\n                // Creates a local copy of the Array with the start value at the front\n                var temp = [startValue];\n                for (var i = 0, l = endValues.length; i < l; i += 1) {\n                    var value = this._handleRelativeValue(startValue, endValues[i]);\n                    if (isNaN(value)) {\n                        isInterpolationList = false;\n                        console.warn('Found invalid interpolation list. Skipping.');\n                        break;\n                    }\n                    temp.push(value);\n                }\n                if (isInterpolationList) {\n                    // if (_valuesStart[property] === undefined) { // handle end values only the first time. NOT NEEDED? setupProperties is now guarded by _propertiesAreSetUp.\n                    _valuesEnd[property] = temp;\n                    // }\n                }\n            }\n            // handle the deepness of the values\n            if ((propType === 'object' || startValueIsArray) && startValue && !isInterpolationList) {\n                _valuesStart[property] = startValueIsArray ? [] : {};\n                var nestedObject = startValue;\n                for (var prop in nestedObject) {\n                    _valuesStart[property][prop] = nestedObject[prop];\n                }\n                // TODO? repeat nested values? And yoyo? And array values?\n                _valuesStartRepeat[property] = startValueIsArray ? [] : {};\n                var endValues = _valuesEnd[property];\n                // If dynamic is not enabled, clone the end values instead of using the passed-in end values.\n                if (!this._isDynamic) {\n                    var tmp = {};\n                    for (var prop in endValues)\n                        tmp[prop] = endValues[prop];\n                    _valuesEnd[property] = endValues = tmp;\n                }\n                this._setupProperties(nestedObject, _valuesStart[property], endValues, _valuesStartRepeat[property], overrideStartingValues);\n            }\n            else {\n                // Save the starting value, but only once unless override is requested.\n                if (typeof _valuesStart[property] === 'undefined' || overrideStartingValues) {\n                    _valuesStart[property] = startValue;\n                }\n                if (!startValueIsArray) {\n                    // eslint-disable-next-line\n                    // @ts-ignore FIXME?\n                    _valuesStart[property] *= 1.0; // Ensures we're using numbers, not strings\n                }\n                if (isInterpolationList) {\n                    // eslint-disable-next-line\n                    // @ts-ignore FIXME?\n                    _valuesStartRepeat[property] = _valuesEnd[property].slice().reverse();\n                }\n                else {\n                    _valuesStartRepeat[property] = _valuesStart[property] || 0;\n                }\n            }\n        }\n    };\n    Tween.prototype.stop = function () {\n        if (!this._isChainStopped) {\n            this._isChainStopped = true;\n            this.stopChainedTweens();\n        }\n        if (!this._isPlaying) {\n            return this;\n        }\n        this._isPlaying = false;\n        this._isPaused = false;\n        if (this._onStopCallback) {\n            this._onStopCallback(this._object);\n        }\n        return this;\n    };\n    Tween.prototype.end = function () {\n        this._goToEnd = true;\n        this.update(this._startTime + this._duration);\n        return this;\n    };\n    Tween.prototype.pause = function (time) {\n        if (time === void 0) { time = now(); }\n        if (this._isPaused || !this._isPlaying) {\n            return this;\n        }\n        this._isPaused = true;\n        this._pauseStart = time;\n        return this;\n    };\n    Tween.prototype.resume = function (time) {\n        if (time === void 0) { time = now(); }\n        if (!this._isPaused || !this._isPlaying) {\n            return this;\n        }\n        this._isPaused = false;\n        this._startTime += time - this._pauseStart;\n        this._pauseStart = 0;\n        return this;\n    };\n    Tween.prototype.stopChainedTweens = function () {\n        for (var i = 0, numChainedTweens = this._chainedTweens.length; i < numChainedTweens; i++) {\n            this._chainedTweens[i].stop();\n        }\n        return this;\n    };\n    Tween.prototype.group = function (group) {\n        if (!group) {\n            console.warn('tween.group() without args has been removed, use group.add(tween) instead.');\n            return this;\n        }\n        group.add(this);\n        return this;\n    };\n    /**\n     * Removes the tween from whichever group it is in.\n     */\n    Tween.prototype.remove = function () {\n        var _a;\n        (_a = this._group) === null || _a === void 0 ? void 0 : _a.remove(this);\n        return this;\n    };\n    Tween.prototype.delay = function (amount) {\n        if (amount === void 0) { amount = 0; }\n        this._delayTime = amount;\n        return this;\n    };\n    Tween.prototype.repeat = function (times) {\n        if (times === void 0) { times = 0; }\n        this._initialRepeat = times;\n        this._repeat = times;\n        return this;\n    };\n    Tween.prototype.repeatDelay = function (amount) {\n        this._repeatDelayTime = amount;\n        return this;\n    };\n    Tween.prototype.yoyo = function (yoyo) {\n        if (yoyo === void 0) { yoyo = false; }\n        this._yoyo = yoyo;\n        return this;\n    };\n    Tween.prototype.easing = function (easingFunction) {\n        if (easingFunction === void 0) { easingFunction = Easing.Linear.None; }\n        this._easingFunction = easingFunction;\n        return this;\n    };\n    Tween.prototype.interpolation = function (interpolationFunction) {\n        if (interpolationFunction === void 0) { interpolationFunction = Interpolation.Linear; }\n        this._interpolationFunction = interpolationFunction;\n        return this;\n    };\n    // eslint-disable-next-line\n    Tween.prototype.chain = function () {\n        var tweens = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            tweens[_i] = arguments[_i];\n        }\n        this._chainedTweens = tweens;\n        return this;\n    };\n    Tween.prototype.onStart = function (callback) {\n        this._onStartCallback = callback;\n        return this;\n    };\n    Tween.prototype.onEveryStart = function (callback) {\n        this._onEveryStartCallback = callback;\n        return this;\n    };\n    Tween.prototype.onUpdate = function (callback) {\n        this._onUpdateCallback = callback;\n        return this;\n    };\n    Tween.prototype.onRepeat = function (callback) {\n        this._onRepeatCallback = callback;\n        return this;\n    };\n    Tween.prototype.onComplete = function (callback) {\n        this._onCompleteCallback = callback;\n        return this;\n    };\n    Tween.prototype.onStop = function (callback) {\n        this._onStopCallback = callback;\n        return this;\n    };\n    /**\n     * @returns true if the tween is still playing after the update, false\n     * otherwise (calling update on a paused tween still returns true because\n     * it is still playing, just paused).\n     *\n     * @param autoStart - When true, calling update will implicitly call start()\n     * as well. Note, if you stop() or end() the tween, but are still calling\n     * update(), it will start again!\n     */\n    Tween.prototype.update = function (time, autoStart) {\n        var _this = this;\n        var _a;\n        if (time === void 0) { time = now(); }\n        if (autoStart === void 0) { autoStart = Tween.autoStartOnUpdate; }\n        if (this._isPaused)\n            return true;\n        var property;\n        if (!this._goToEnd && !this._isPlaying) {\n            if (autoStart)\n                this.start(time, true);\n            else\n                return false;\n        }\n        this._goToEnd = false;\n        if (time < this._startTime) {\n            return true;\n        }\n        if (this._onStartCallbackFired === false) {\n            if (this._onStartCallback) {\n                this._onStartCallback(this._object);\n            }\n            this._onStartCallbackFired = true;\n        }\n        if (this._onEveryStartCallbackFired === false) {\n            if (this._onEveryStartCallback) {\n                this._onEveryStartCallback(this._object);\n            }\n            this._onEveryStartCallbackFired = true;\n        }\n        var elapsedTime = time - this._startTime;\n        var durationAndDelay = this._duration + ((_a = this._repeatDelayTime) !== null && _a !== void 0 ? _a : this._delayTime);\n        var totalTime = this._duration + this._repeat * durationAndDelay;\n        var calculateElapsedPortion = function () {\n            if (_this._duration === 0)\n                return 1;\n            if (elapsedTime > totalTime) {\n                return 1;\n            }\n            var timesRepeated = Math.trunc(elapsedTime / durationAndDelay);\n            var timeIntoCurrentRepeat = elapsedTime - timesRepeated * durationAndDelay;\n            // TODO use %?\n            // const timeIntoCurrentRepeat = elapsedTime % durationAndDelay\n            var portion = Math.min(timeIntoCurrentRepeat / _this._duration, 1);\n            if (portion === 0 && elapsedTime === _this._duration) {\n                return 1;\n            }\n            return portion;\n        };\n        var elapsed = calculateElapsedPortion();\n        var value = this._easingFunction(elapsed);\n        // properties transformations\n        this._updateProperties(this._object, this._valuesStart, this._valuesEnd, value);\n        if (this._onUpdateCallback) {\n            this._onUpdateCallback(this._object, elapsed);\n        }\n        if (this._duration === 0 || elapsedTime >= this._duration) {\n            if (this._repeat > 0) {\n                var completeCount = Math.min(Math.trunc((elapsedTime - this._duration) / durationAndDelay) + 1, this._repeat);\n                if (isFinite(this._repeat)) {\n                    this._repeat -= completeCount;\n                }\n                // Reassign starting values, restart by making startTime = now\n                for (property in this._valuesStartRepeat) {\n                    if (!this._yoyo && typeof this._valuesEnd[property] === 'string') {\n                        this._valuesStartRepeat[property] =\n                            // eslint-disable-next-line\n                            // @ts-ignore FIXME?\n                            this._valuesStartRepeat[property] + parseFloat(this._valuesEnd[property]);\n                    }\n                    if (this._yoyo) {\n                        this._swapEndStartRepeatValues(property);\n                    }\n                    this._valuesStart[property] = this._valuesStartRepeat[property];\n                }\n                if (this._yoyo) {\n                    this._reversed = !this._reversed;\n                }\n                this._startTime += durationAndDelay * completeCount;\n                if (this._onRepeatCallback) {\n                    this._onRepeatCallback(this._object);\n                }\n                this._onEveryStartCallbackFired = false;\n                return true;\n            }\n            else {\n                if (this._onCompleteCallback) {\n                    this._onCompleteCallback(this._object);\n                }\n                for (var i = 0, numChainedTweens = this._chainedTweens.length; i < numChainedTweens; i++) {\n                    // Make the chained tweens start exactly at the time they should,\n                    // even if the `update()` method was called way past the duration of the tween\n                    this._chainedTweens[i].start(this._startTime + this._duration, false);\n                }\n                this._isPlaying = false;\n                return false;\n            }\n        }\n        return true;\n    };\n    Tween.prototype._updateProperties = function (_object, _valuesStart, _valuesEnd, value) {\n        for (var property in _valuesEnd) {\n            // Don't update properties that do not exist in the source object\n            if (_valuesStart[property] === undefined) {\n                continue;\n            }\n            var start = _valuesStart[property] || 0;\n            var end = _valuesEnd[property];\n            var startIsArray = Array.isArray(_object[property]);\n            var endIsArray = Array.isArray(end);\n            var isInterpolationList = !startIsArray && endIsArray;\n            if (isInterpolationList) {\n                _object[property] = this._interpolationFunction(end, value);\n            }\n            else if (typeof end === 'object' && end) {\n                // eslint-disable-next-line\n                // @ts-ignore FIXME?\n                this._updateProperties(_object[property], start, end, value);\n            }\n            else {\n                // Parses relative end values with start as base (e.g.: +10, -3)\n                end = this._handleRelativeValue(start, end);\n                // Protect against non numeric properties.\n                if (typeof end === 'number') {\n                    // eslint-disable-next-line\n                    // @ts-ignore FIXME?\n                    _object[property] = start + (end - start) * value;\n                }\n            }\n        }\n    };\n    Tween.prototype._handleRelativeValue = function (start, end) {\n        if (typeof end !== 'string') {\n            return end;\n        }\n        if (end.charAt(0) === '+' || end.charAt(0) === '-') {\n            return start + parseFloat(end);\n        }\n        return parseFloat(end);\n    };\n    Tween.prototype._swapEndStartRepeatValues = function (property) {\n        var tmp = this._valuesStartRepeat[property];\n        var endValue = this._valuesEnd[property];\n        if (typeof endValue === 'string') {\n            this._valuesStartRepeat[property] = this._valuesStartRepeat[property] + parseFloat(endValue);\n        }\n        else {\n            this._valuesStartRepeat[property] = this._valuesEnd[property];\n        }\n        this._valuesEnd[property] = tmp;\n    };\n    Tween.autoStartOnUpdate = false;\n    return Tween;\n}());\n\nvar VERSION = '25.0.0';\n\n/**\n * Tween.js - Licensed under the MIT license\n * https://github.com/tweenjs/tween.js\n * ----------------------------------------------\n *\n * See https://github.com/tweenjs/tween.js/graphs/contributors for the full list of contributors.\n * Thank you all, you're awesome!\n */\nvar nextId = Sequence.nextId;\n/**\n * Controlling groups of tweens\n *\n * Using the TWEEN singleton to manage your tweens can cause issues in large apps with many components.\n * In these cases, you may want to create your own smaller groups of tweens.\n */\nvar TWEEN = mainGroup;\n// This is the best way to export things in a way that's compatible with both ES\n// Modules and CommonJS, without build hacks, and so as not to break the\n// existing API.\n// https://github.com/rollup/rollup/issues/1961#issuecomment-423037881\n/**\n * @deprecated The global TWEEN Group will be removed in a following major\n * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n * group.\n *\n * Old code:\n *\n * ```js\n * import * as TWEEN from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new TWEEN.Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   TWEEN.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n *\n * New code:\n *\n * ```js\n * import {Tween, Group} from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * const group = new Group()\n * group.add(tween)\n * group.add(tween2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   group.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n */\nvar getAll = TWEEN.getAll.bind(TWEEN);\n/**\n * @deprecated The global TWEEN Group will be removed in a following major\n * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n * group.\n *\n * Old code:\n *\n * ```js\n * import * as TWEEN from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new TWEEN.Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   TWEEN.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n *\n * New code:\n *\n * ```js\n * import {Tween, Group} from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * const group = new Group()\n * group.add(tween)\n * group.add(tween2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   group.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n */\nvar removeAll = TWEEN.removeAll.bind(TWEEN);\n/**\n * @deprecated The global TWEEN Group will be removed in a following major\n * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n * group.\n *\n * Old code:\n *\n * ```js\n * import * as TWEEN from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new TWEEN.Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   TWEEN.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n *\n * New code:\n *\n * ```js\n * import {Tween, Group} from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * const group = new Group()\n * group.add(tween)\n * group.add(tween2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   group.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n */\nvar add = TWEEN.add.bind(TWEEN);\n/**\n * @deprecated The global TWEEN Group will be removed in a following major\n * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n * group.\n *\n * Old code:\n *\n * ```js\n * import * as TWEEN from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new TWEEN.Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   TWEEN.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n *\n * New code:\n *\n * ```js\n * import {Tween, Group} from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * const group = new Group()\n * group.add(tween)\n * group.add(tween2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   group.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n */\nvar remove = TWEEN.remove.bind(TWEEN);\n/**\n * @deprecated The global TWEEN Group will be removed in a following major\n * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n * group.\n *\n * Old code:\n *\n * ```js\n * import * as TWEEN from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new TWEEN.Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   TWEEN.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n *\n * New code:\n *\n * ```js\n * import {Tween, Group} from '@tweenjs/tween.js'\n *\n * //...\n *\n * const tween = new Tween(obj)\n * const tween2 = new TWEEN.Tween(obj2)\n *\n * //...\n *\n * const group = new Group()\n * group.add(tween)\n * group.add(tween2)\n *\n * //...\n *\n * requestAnimationFrame(function loop(time) {\n *   group.update(time)\n *   requestAnimationFrame(loop)\n * })\n * ```\n */\nvar update = TWEEN.update.bind(TWEEN);\nvar exports = {\n    Easing: Easing,\n    Group: Group,\n    Interpolation: Interpolation,\n    now: now,\n    Sequence: Sequence,\n    nextId: nextId,\n    Tween: Tween,\n    VERSION: VERSION,\n    /**\n     * @deprecated The global TWEEN Group will be removed in a following major\n     * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n     * group.\n     *\n     * Old code:\n     *\n     * ```js\n     * import * as TWEEN from '@tweenjs/tween.js'\n     *\n     * //...\n     *\n     * const tween = new TWEEN.Tween(obj)\n     * const tween2 = new TWEEN.Tween(obj2)\n     *\n     * //...\n     *\n     * requestAnimationFrame(function loop(time) {\n     *   TWEEN.update(time)\n     *   requestAnimationFrame(loop)\n     * })\n     * ```\n     *\n     * New code:\n     *\n     * ```js\n     * import {Tween, Group} from '@tweenjs/tween.js'\n     *\n     * //...\n     *\n     * const tween = new Tween(obj)\n     * const tween2 = new TWEEN.Tween(obj2)\n     *\n     * //...\n     *\n     * const group = new Group()\n     * group.add(tween)\n     * group.add(tween2)\n     *\n     * //...\n     *\n     * requestAnimationFrame(function loop(time) {\n     *   group.update(time)\n     *   requestAnimationFrame(loop)\n     * })\n     * ```\n     */\n    getAll: getAll,\n    /**\n     * @deprecated The global TWEEN Group will be removed in a following major\n     * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n     * group.\n     *\n     * Old code:\n     *\n     * ```js\n     * import * as TWEEN from '@tweenjs/tween.js'\n     *\n     * //...\n     *\n     * const tween = new TWEEN.Tween(obj)\n     * const tween2 = new TWEEN.Tween(obj2)\n     *\n     * //...\n     *\n     * requestAnimationFrame(function loop(time) {\n     *   TWEEN.update(time)\n     *   requestAnimationFrame(loop)\n     * })\n     * ```\n     *\n     * New code:\n     *\n     * ```js\n     * import {Tween, Group} from '@tweenjs/tween.js'\n     *\n     * //...\n     *\n     * const tween = new Tween(obj)\n     * const tween2 = new TWEEN.Tween(obj2)\n     *\n     * //...\n     *\n     * const group = new Group()\n     * group.add(tween)\n     * group.add(tween2)\n     *\n     * //...\n     *\n     * requestAnimationFrame(function loop(time) {\n     *   group.update(time)\n     *   requestAnimationFrame(loop)\n     * })\n     * ```\n     */\n    removeAll: removeAll,\n    /**\n     * @deprecated The global TWEEN Group will be removed in a following major\n     * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n     * group.\n     *\n     * Old code:\n     *\n     * ```js\n     * import * as TWEEN from '@tweenjs/tween.js'\n     *\n     * //...\n     *\n     * const tween = new TWEEN.Tween(obj)\n     * const tween2 = new TWEEN.Tween(obj2)\n     *\n     * //...\n     *\n     * requestAnimationFrame(function loop(time) {\n     *   TWEEN.update(time)\n     *   requestAnimationFrame(loop)\n     * })\n     * ```\n     *\n     * New code:\n     *\n     * ```js\n     * import {Tween, Group} from '@tweenjs/tween.js'\n     *\n     * //...\n     *\n     * const tween = new Tween(obj)\n     * const tween2 = new TWEEN.Tween(obj2)\n     *\n     * //...\n     *\n     * const group = new Group()\n     * group.add(tween)\n     * group.add(tween2)\n     *\n     * //...\n     *\n     * requestAnimationFrame(function loop(time) {\n     *   group.update(time)\n     *   requestAnimationFrame(loop)\n     * })\n     * ```\n     */\n    add: add,\n    /**\n     * @deprecated The global TWEEN Group will be removed in a following major\n     * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n     * group.\n     *\n     * Old code:\n     *\n     * ```js\n     * import * as TWEEN from '@tweenjs/tween.js'\n     *\n     * //...\n     *\n     * const tween = new TWEEN.Tween(obj)\n     * const tween2 = new TWEEN.Tween(obj2)\n     *\n     * //...\n     *\n     * requestAnimationFrame(function loop(time) {\n     *   TWEEN.update(time)\n     *   requestAnimationFrame(loop)\n     * })\n     * ```\n     *\n     * New code:\n     *\n     * ```js\n     * import {Tween, Group} from '@tweenjs/tween.js'\n     *\n     * //...\n     *\n     * const tween = new Tween(obj)\n     * const tween2 = new TWEEN.Tween(obj2)\n     *\n     * //...\n     *\n     * const group = new Group()\n     * group.add(tween)\n     * group.add(tween2)\n     *\n     * //...\n     *\n     * requestAnimationFrame(function loop(time) {\n     *   group.update(time)\n     *   requestAnimationFrame(loop)\n     * })\n     * ```\n     */\n    remove: remove,\n    /**\n     * @deprecated The global TWEEN Group will be removed in a following major\n     * release. To migrate, create a `new Group()` instead of using `TWEEN` as a\n     * group.\n     *\n     * Old code:\n     *\n     * ```js\n     * import * as TWEEN from '@tweenjs/tween.js'\n     *\n     * //...\n     *\n     * const tween = new TWEEN.Tween(obj)\n     * const tween2 = new TWEEN.Tween(obj2)\n     *\n     * //...\n     *\n     * requestAnimationFrame(function loop(time) {\n     *   TWEEN.update(time)\n     *   requestAnimationFrame(loop)\n     * })\n     * ```\n     *\n     * New code:\n     *\n     * ```js\n     * import {Tween, Group} from '@tweenjs/tween.js'\n     *\n     * //...\n     *\n     * const tween = new Tween(obj)\n     * const tween2 = new TWEEN.Tween(obj2)\n     *\n     * //...\n     *\n     * const group = new Group()\n     * group.add(tween)\n     * group.add(tween2)\n     *\n     * //...\n     *\n     * requestAnimationFrame(function loop(time) {\n     *   group.update(time)\n     *   requestAnimationFrame(loop)\n     * })\n     * ```\n     */\n    update: update,\n};\n\nexport { Easing, Group, Interpolation, Sequence, Tween, VERSION, add, exports as default, getAll, nextId, now, remove, removeAll, update };\n"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC;EACvBC,MAAM,EAAEF,MAAM,CAACC,MAAM,CAAC;IAClBE,IAAI,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACpB,OAAOA,MAAM;IACjB,CAAC;IACDC,EAAE,EAAE,SAAAA,CAAUD,MAAM,EAAE;MAClB,OAAOA,MAAM;IACjB,CAAC;IACDE,GAAG,EAAE,SAAAA,CAAUF,MAAM,EAAE;MACnB,OAAOA,MAAM;IACjB,CAAC;IACDG,KAAK,EAAE,SAAAA,CAAUH,MAAM,EAAE;MACrB,OAAOA,MAAM;IACjB;EACJ,CAAC,CAAC;EACFI,SAAS,EAAER,MAAM,CAACC,MAAM,CAAC;IACrBI,EAAE,EAAE,SAAAA,CAAUD,MAAM,EAAE;MAClB,OAAOA,MAAM,GAAGA,MAAM;IAC1B,CAAC;IACDE,GAAG,EAAE,SAAAA,CAAUF,MAAM,EAAE;MACnB,OAAOA,MAAM,IAAI,CAAC,GAAGA,MAAM,CAAC;IAChC,CAAC;IACDG,KAAK,EAAE,SAAAA,CAAUH,MAAM,EAAE;MACrB,IAAI,CAACA,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;QACnB,OAAO,GAAG,GAAGA,MAAM,GAAGA,MAAM;MAChC;MACA,OAAO,CAAC,GAAG,IAAI,EAAEA,MAAM,IAAIA,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/C;EACJ,CAAC,CAAC;EACFK,KAAK,EAAET,MAAM,CAACC,MAAM,CAAC;IACjBI,EAAE,EAAE,SAAAA,CAAUD,MAAM,EAAE;MAClB,OAAOA,MAAM,GAAGA,MAAM,GAAGA,MAAM;IACnC,CAAC;IACDE,GAAG,EAAE,SAAAA,CAAUF,MAAM,EAAE;MACnB,OAAO,EAAEA,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAG,CAAC;IACzC,CAAC;IACDG,KAAK,EAAE,SAAAA,CAAUH,MAAM,EAAE;MACrB,IAAI,CAACA,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;QACnB,OAAO,GAAG,GAAGA,MAAM,GAAGA,MAAM,GAAGA,MAAM;MACzC;MACA,OAAO,GAAG,IAAI,CAACA,MAAM,IAAI,CAAC,IAAIA,MAAM,GAAGA,MAAM,GAAG,CAAC,CAAC;IACtD;EACJ,CAAC,CAAC;EACFM,OAAO,EAAEV,MAAM,CAACC,MAAM,CAAC;IACnBI,EAAE,EAAE,SAAAA,CAAUD,MAAM,EAAE;MAClB,OAAOA,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAGA,MAAM;IAC5C,CAAC;IACDE,GAAG,EAAE,SAAAA,CAAUF,MAAM,EAAE;MACnB,OAAO,CAAC,GAAG,EAAEA,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAGA,MAAM;IAClD,CAAC;IACDG,KAAK,EAAE,SAAAA,CAAUH,MAAM,EAAE;MACrB,IAAI,CAACA,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;QACnB,OAAO,GAAG,GAAGA,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAGA,MAAM;MAClD;MACA,OAAO,CAAC,GAAG,IAAI,CAACA,MAAM,IAAI,CAAC,IAAIA,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAG,CAAC,CAAC;IAChE;EACJ,CAAC,CAAC;EACFO,OAAO,EAAEX,MAAM,CAACC,MAAM,CAAC;IACnBI,EAAE,EAAE,SAAAA,CAAUD,MAAM,EAAE;MAClB,OAAOA,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAGA,MAAM;IACrD,CAAC;IACDE,GAAG,EAAE,SAAAA,CAAUF,MAAM,EAAE;MACnB,OAAO,EAAEA,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAG,CAAC;IAC3D,CAAC;IACDG,KAAK,EAAE,SAAAA,CAAUH,MAAM,EAAE;MACrB,IAAI,CAACA,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;QACnB,OAAO,GAAG,GAAGA,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAGA,MAAM;MAC3D;MACA,OAAO,GAAG,IAAI,CAACA,MAAM,IAAI,CAAC,IAAIA,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAGA,MAAM,GAAG,CAAC,CAAC;IACxE;EACJ,CAAC,CAAC;EACFQ,UAAU,EAAEZ,MAAM,CAACC,MAAM,CAAC;IACtBI,EAAE,EAAE,SAAAA,CAAUD,MAAM,EAAE;MAClB,OAAO,CAAC,GAAGS,IAAI,CAACC,GAAG,CAAE,CAAC,GAAG,GAAGV,MAAM,IAAIS,IAAI,CAACE,EAAE,GAAI,CAAC,CAAC;IACvD,CAAC;IACDT,GAAG,EAAE,SAAAA,CAAUF,MAAM,EAAE;MACnB,OAAOS,IAAI,CAACC,GAAG,CAAEV,MAAM,GAAGS,IAAI,CAACE,EAAE,GAAI,CAAC,CAAC;IAC3C,CAAC;IACDR,KAAK,EAAE,SAAAA,CAAUH,MAAM,EAAE;MACrB,OAAO,GAAG,IAAI,CAAC,GAAGS,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,EAAE,IAAI,GAAG,GAAGX,MAAM,CAAC,CAAC,CAAC;IACzD;EACJ,CAAC,CAAC;EACFY,WAAW,EAAEhB,MAAM,CAACC,MAAM,CAAC;IACvBI,EAAE,EAAE,SAAAA,CAAUD,MAAM,EAAE;MAClB,OAAOA,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGS,IAAI,CAACI,GAAG,CAAC,IAAI,EAAEb,MAAM,GAAG,CAAC,CAAC;IACxD,CAAC;IACDE,GAAG,EAAE,SAAAA,CAAUF,MAAM,EAAE;MACnB,OAAOA,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGS,IAAI,CAACI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAGb,MAAM,CAAC;IAC3D,CAAC;IACDG,KAAK,EAAE,SAAAA,CAAUH,MAAM,EAAE;MACrB,IAAIA,MAAM,KAAK,CAAC,EAAE;QACd,OAAO,CAAC;MACZ;MACA,IAAIA,MAAM,KAAK,CAAC,EAAE;QACd,OAAO,CAAC;MACZ;MACA,IAAI,CAACA,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;QACnB,OAAO,GAAG,GAAGS,IAAI,CAACI,GAAG,CAAC,IAAI,EAAEb,MAAM,GAAG,CAAC,CAAC;MAC3C;MACA,OAAO,GAAG,IAAI,CAACS,IAAI,CAACI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAIb,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACvD;EACJ,CAAC,CAAC;EACFc,QAAQ,EAAElB,MAAM,CAACC,MAAM,CAAC;IACpBI,EAAE,EAAE,SAAAA,CAAUD,MAAM,EAAE;MAClB,OAAO,CAAC,GAAGS,IAAI,CAACM,IAAI,CAAC,CAAC,GAAGf,MAAM,GAAGA,MAAM,CAAC;IAC7C,CAAC;IACDE,GAAG,EAAE,SAAAA,CAAUF,MAAM,EAAE;MACnB,OAAOS,IAAI,CAACM,IAAI,CAAC,CAAC,GAAG,EAAEf,MAAM,GAAGA,MAAM,CAAC;IAC3C,CAAC;IACDG,KAAK,EAAE,SAAAA,CAAUH,MAAM,EAAE;MACrB,IAAI,CAACA,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;QACnB,OAAO,CAAC,GAAG,IAAIS,IAAI,CAACM,IAAI,CAAC,CAAC,GAAGf,MAAM,GAAGA,MAAM,CAAC,GAAG,CAAC,CAAC;MACtD;MACA,OAAO,GAAG,IAAIS,IAAI,CAACM,IAAI,CAAC,CAAC,GAAG,CAACf,MAAM,IAAI,CAAC,IAAIA,MAAM,CAAC,GAAG,CAAC,CAAC;IAC5D;EACJ,CAAC,CAAC;EACFgB,OAAO,EAAEpB,MAAM,CAACC,MAAM,CAAC;IACnBI,EAAE,EAAE,SAAAA,CAAUD,MAAM,EAAE;MAClB,IAAIA,MAAM,KAAK,CAAC,EAAE;QACd,OAAO,CAAC;MACZ;MACA,IAAIA,MAAM,KAAK,CAAC,EAAE;QACd,OAAO,CAAC;MACZ;MACA,OAAO,CAACS,IAAI,CAACI,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIb,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACC,GAAG,CAAC,CAACV,MAAM,GAAG,GAAG,IAAI,CAAC,GAAGS,IAAI,CAACE,EAAE,CAAC;IACnF,CAAC;IACDT,GAAG,EAAE,SAAAA,CAAUF,MAAM,EAAE;MACnB,IAAIA,MAAM,KAAK,CAAC,EAAE;QACd,OAAO,CAAC;MACZ;MACA,IAAIA,MAAM,KAAK,CAAC,EAAE;QACd,OAAO,CAAC;MACZ;MACA,OAAOS,IAAI,CAACI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAGb,MAAM,CAAC,GAAGS,IAAI,CAACC,GAAG,CAAC,CAACV,MAAM,GAAG,GAAG,IAAI,CAAC,GAAGS,IAAI,CAACE,EAAE,CAAC,GAAG,CAAC;IACjF,CAAC;IACDR,KAAK,EAAE,SAAAA,CAAUH,MAAM,EAAE;MACrB,IAAIA,MAAM,KAAK,CAAC,EAAE;QACd,OAAO,CAAC;MACZ;MACA,IAAIA,MAAM,KAAK,CAAC,EAAE;QACd,OAAO,CAAC;MACZ;MACAA,MAAM,IAAI,CAAC;MACX,IAAIA,MAAM,GAAG,CAAC,EAAE;QACZ,OAAO,CAAC,GAAG,GAAGS,IAAI,CAACI,GAAG,CAAC,CAAC,EAAE,EAAE,IAAIb,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACC,GAAG,CAAC,CAACV,MAAM,GAAG,GAAG,IAAI,CAAC,GAAGS,IAAI,CAACE,EAAE,CAAC;MACzF;MACA,OAAO,GAAG,GAAGF,IAAI,CAACI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAIb,MAAM,GAAG,CAAC,CAAC,CAAC,GAAGS,IAAI,CAACC,GAAG,CAAC,CAACV,MAAM,GAAG,GAAG,IAAI,CAAC,GAAGS,IAAI,CAACE,EAAE,CAAC,GAAG,CAAC;IAC7F;EACJ,CAAC,CAAC;EACFM,IAAI,EAAErB,MAAM,CAACC,MAAM,CAAC;IAChBI,EAAE,EAAE,SAAAA,CAAUD,MAAM,EAAE;MAClB,IAAIkB,CAAC,GAAG,OAAO;MACf,OAAOlB,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM,GAAGA,MAAM,IAAI,CAACkB,CAAC,GAAG,CAAC,IAAIlB,MAAM,GAAGkB,CAAC,CAAC;IACtE,CAAC;IACDhB,GAAG,EAAE,SAAAA,CAAUF,MAAM,EAAE;MACnB,IAAIkB,CAAC,GAAG,OAAO;MACf,OAAOlB,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,EAAEA,MAAM,GAAGA,MAAM,IAAI,CAACkB,CAAC,GAAG,CAAC,IAAIlB,MAAM,GAAGkB,CAAC,CAAC,GAAG,CAAC;IAC5E,CAAC;IACDf,KAAK,EAAE,SAAAA,CAAUH,MAAM,EAAE;MACrB,IAAIkB,CAAC,GAAG,OAAO,GAAG,KAAK;MACvB,IAAI,CAAClB,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;QACnB,OAAO,GAAG,IAAIA,MAAM,GAAGA,MAAM,IAAI,CAACkB,CAAC,GAAG,CAAC,IAAIlB,MAAM,GAAGkB,CAAC,CAAC,CAAC;MAC3D;MACA,OAAO,GAAG,IAAI,CAAClB,MAAM,IAAI,CAAC,IAAIA,MAAM,IAAI,CAACkB,CAAC,GAAG,CAAC,IAAIlB,MAAM,GAAGkB,CAAC,CAAC,GAAG,CAAC,CAAC;IACtE;EACJ,CAAC,CAAC;EACFC,MAAM,EAAEvB,MAAM,CAACC,MAAM,CAAC;IAClBI,EAAE,EAAE,SAAAA,CAAUD,MAAM,EAAE;MAClB,OAAO,CAAC,GAAGL,MAAM,CAACwB,MAAM,CAACjB,GAAG,CAAC,CAAC,GAAGF,MAAM,CAAC;IAC5C,CAAC;IACDE,GAAG,EAAE,SAAAA,CAAUF,MAAM,EAAE;MACnB,IAAIA,MAAM,GAAG,CAAC,GAAG,IAAI,EAAE;QACnB,OAAO,MAAM,GAAGA,MAAM,GAAGA,MAAM;MACnC,CAAC,MACI,IAAIA,MAAM,GAAG,CAAC,GAAG,IAAI,EAAE;QACxB,OAAO,MAAM,IAAIA,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,GAAGA,MAAM,GAAG,IAAI;MAC1D,CAAC,MACI,IAAIA,MAAM,GAAG,GAAG,GAAG,IAAI,EAAE;QAC1B,OAAO,MAAM,IAAIA,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,GAAGA,MAAM,GAAG,MAAM;MAC7D,CAAC,MACI;QACD,OAAO,MAAM,IAAIA,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,GAAGA,MAAM,GAAG,QAAQ;MAChE;IACJ,CAAC;IACDG,KAAK,EAAE,SAAAA,CAAUH,MAAM,EAAE;MACrB,IAAIA,MAAM,GAAG,GAAG,EAAE;QACd,OAAOL,MAAM,CAACwB,MAAM,CAAClB,EAAE,CAACD,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;MAC7C;MACA,OAAOL,MAAM,CAACwB,MAAM,CAACjB,GAAG,CAACF,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;IACxD;EACJ,CAAC,CAAC;EACFoB,WAAW,EAAE,SAAAA,CAAUC,KAAK,EAAE;IAC1B,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnCA,KAAK,GAAGA,KAAK,GAAGC,MAAM,CAACC,OAAO,GAAGD,MAAM,CAACC,OAAO,GAAGF,KAAK;IACvDA,KAAK,GAAGA,KAAK,GAAG,KAAK,GAAG,KAAK,GAAGA,KAAK;IACrC,OAAO;MACHpB,EAAE,EAAE,SAAAA,CAAUD,MAAM,EAAE;QAClB,OAAOS,IAAI,CAACI,GAAG,CAACb,MAAM,EAAEqB,KAAK,CAAC;MAClC,CAAC;MACDnB,GAAG,EAAE,SAAAA,CAAUF,MAAM,EAAE;QACnB,OAAO,CAAC,GAAGS,IAAI,CAACI,GAAG,CAAE,CAAC,GAAGb,MAAM,EAAGqB,KAAK,CAAC;MAC5C,CAAC;MACDlB,KAAK,EAAE,SAAAA,CAAUH,MAAM,EAAE;QACrB,IAAIA,MAAM,GAAG,GAAG,EAAE;UACd,OAAOS,IAAI,CAACI,GAAG,CAAEb,MAAM,GAAG,CAAC,EAAGqB,KAAK,CAAC,GAAG,CAAC;QAC5C;QACA,OAAO,CAAC,CAAC,GAAGZ,IAAI,CAACI,GAAG,CAAE,CAAC,GAAGb,MAAM,GAAG,CAAC,EAAGqB,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG;MAC5D;IACJ,CAAC;EACL;AACJ,CAAC,CAAC;AAEF,IAAIG,GAAG,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAOC,WAAW,CAACD,GAAG,CAAC,CAAC;AAAE,CAAC;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,KAAK,GAAG,aAAe,YAAY;EACnC,SAASA,KAAKA,CAAA,EAAG;IACb,IAAIC,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC1CD,MAAM,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC9B;IACA,IAAI,CAACG,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACC,wBAAwB,GAAG,CAAC,CAAC;IAClC,IAAI,CAACC,GAAG,CAACC,KAAK,CAAC,IAAI,EAAEP,MAAM,CAAC;EAChC;EACAD,KAAK,CAACS,SAAS,CAACC,MAAM,GAAG,YAAY;IACjC,IAAIC,KAAK,GAAG,IAAI;IAChB,OAAOzC,MAAM,CAAC0C,IAAI,CAAC,IAAI,CAACP,OAAO,CAAC,CAACQ,GAAG,CAAC,UAAUC,OAAO,EAAE;MAAE,OAAOH,KAAK,CAACN,OAAO,CAACS,OAAO,CAAC;IAAE,CAAC,CAAC;EAC/F,CAAC;EACDd,KAAK,CAACS,SAAS,CAACM,SAAS,GAAG,YAAY;IACpC,IAAI,CAACV,OAAO,GAAG,CAAC,CAAC;EACrB,CAAC;EACDL,KAAK,CAACS,SAAS,CAACF,GAAG,GAAG,YAAY;IAC9B,IAAIS,EAAE;IACN,IAAIf,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC1CD,MAAM,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC9B;IACA,KAAK,IAAIe,EAAE,GAAG,CAAC,EAAEC,QAAQ,GAAGjB,MAAM,EAAEgB,EAAE,GAAGC,QAAQ,CAACd,MAAM,EAAEa,EAAE,EAAE,EAAE;MAC5D,IAAIE,KAAK,GAAGD,QAAQ,CAACD,EAAE,CAAC;MACxB;MACA;MACA,CAACD,EAAE,GAAGG,KAAK,CAACC,MAAM,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,MAAM,CAACF,KAAK,CAAC;MACzE;MACAA,KAAK,CAACC,MAAM,GAAG,IAAI;MACnB,IAAI,CAACf,OAAO,CAACc,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,GAAGH,KAAK;MACnC,IAAI,CAACb,wBAAwB,CAACa,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,GAAGH,KAAK;IACxD;EACJ,CAAC;EACDnB,KAAK,CAACS,SAAS,CAACY,MAAM,GAAG,YAAY;IACjC,IAAIpB,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC1CD,MAAM,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC9B;IACA,KAAK,IAAIc,EAAE,GAAG,CAAC,EAAEO,QAAQ,GAAGtB,MAAM,EAAEe,EAAE,GAAGO,QAAQ,CAACnB,MAAM,EAAEY,EAAE,EAAE,EAAE;MAC5D,IAAIG,KAAK,GAAGI,QAAQ,CAACP,EAAE,CAAC;MACxB;MACAG,KAAK,CAACC,MAAM,GAAGI,SAAS;MACxB,OAAO,IAAI,CAACnB,OAAO,CAACc,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC;MAClC,OAAO,IAAI,CAAChB,wBAAwB,CAACa,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC;IACvD;EACJ,CAAC;EACD;EACAtB,KAAK,CAACS,SAAS,CAACgB,UAAU,GAAG,YAAY;IACrC,OAAO,IAAI,CAACf,MAAM,CAAC,CAAC,CAACgB,KAAK,CAAC,UAAUP,KAAK,EAAE;MAAE,OAAO,CAACA,KAAK,CAACQ,SAAS,CAAC,CAAC;IAAE,CAAC,CAAC;EAC/E,CAAC;EACD3B,KAAK,CAACS,SAAS,CAACmB,MAAM,GAAG,UAAUC,IAAI,EAAEC,QAAQ,EAAE;IAC/C,IAAID,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG/B,GAAG,CAAC,CAAC;IAAE;IACrC,IAAIgC,QAAQ,KAAK,KAAK,CAAC,EAAE;MAAEA,QAAQ,GAAG,IAAI;IAAE;IAC5C,IAAIC,QAAQ,GAAG7D,MAAM,CAAC0C,IAAI,CAAC,IAAI,CAACP,OAAO,CAAC;IACxC,IAAI0B,QAAQ,CAAC3B,MAAM,KAAK,CAAC,EACrB;IACJ;IACA;IACA;IACA;IACA;IACA,OAAO2B,QAAQ,CAAC3B,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI,CAACE,wBAAwB,GAAG,CAAC,CAAC;MAClC,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,CAAC3B,MAAM,EAAE4B,CAAC,EAAE,EAAE;QACtC,IAAIb,KAAK,GAAG,IAAI,CAACd,OAAO,CAAC0B,QAAQ,CAACC,CAAC,CAAC,CAAC;QACrC,IAAIC,SAAS,GAAG,CAACH,QAAQ;QACzB,IAAIX,KAAK,IAAIA,KAAK,CAACS,MAAM,CAACC,IAAI,EAAEI,SAAS,CAAC,KAAK,KAAK,IAAI,CAACH,QAAQ,EAC7D,IAAI,CAACT,MAAM,CAACF,KAAK,CAAC;MAC1B;MACAY,QAAQ,GAAG7D,MAAM,CAAC0C,IAAI,CAAC,IAAI,CAACN,wBAAwB,CAAC;IACzD;EACJ,CAAC;EACD,OAAON,KAAK;AAChB,CAAC,CAAC,CAAE;;AAEJ;AACA;AACA;AACA,IAAIkC,aAAa,GAAG;EAChB9D,MAAM,EAAE,SAAAA,CAAU+D,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAIC,CAAC,GAAGF,CAAC,CAAC/B,MAAM,GAAG,CAAC;IACpB,IAAIkC,CAAC,GAAGD,CAAC,GAAGD,CAAC;IACb,IAAIJ,CAAC,GAAGjD,IAAI,CAACwD,KAAK,CAACD,CAAC,CAAC;IACrB,IAAIE,EAAE,GAAGN,aAAa,CAACO,KAAK,CAACrE,MAAM;IACnC,IAAIgE,CAAC,GAAG,CAAC,EAAE;MACP,OAAOI,EAAE,CAACL,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEG,CAAC,CAAC;IAC5B;IACA,IAAIF,CAAC,GAAG,CAAC,EAAE;MACP,OAAOI,EAAE,CAACL,CAAC,CAACE,CAAC,CAAC,EAAEF,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,GAAGC,CAAC,CAAC;IACpC;IACA,OAAOE,EAAE,CAACL,CAAC,CAACH,CAAC,CAAC,EAAEG,CAAC,CAACH,CAAC,GAAG,CAAC,GAAGK,CAAC,GAAGA,CAAC,GAAGL,CAAC,GAAG,CAAC,CAAC,EAAEM,CAAC,GAAGN,CAAC,CAAC;EACpD,CAAC;EACDU,MAAM,EAAE,SAAAA,CAAUP,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAIO,CAAC,GAAG,CAAC;IACT,IAAIC,CAAC,GAAGT,CAAC,CAAC/B,MAAM,GAAG,CAAC;IACpB,IAAIyC,EAAE,GAAG9D,IAAI,CAACI,GAAG;IACjB,IAAI2D,EAAE,GAAGZ,aAAa,CAACO,KAAK,CAACM,SAAS;IACtC,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIY,CAAC,EAAEZ,CAAC,EAAE,EAAE;MACzBW,CAAC,IAAIE,EAAE,CAAC,CAAC,GAAGT,CAAC,EAAEQ,CAAC,GAAGZ,CAAC,CAAC,GAAGa,EAAE,CAACT,CAAC,EAAEJ,CAAC,CAAC,GAAGG,CAAC,CAACH,CAAC,CAAC,GAAGc,EAAE,CAACF,CAAC,EAAEZ,CAAC,CAAC;IACtD;IACA,OAAOW,CAAC;EACZ,CAAC;EACDK,UAAU,EAAE,SAAAA,CAAUb,CAAC,EAAEC,CAAC,EAAE;IACxB,IAAIC,CAAC,GAAGF,CAAC,CAAC/B,MAAM,GAAG,CAAC;IACpB,IAAIkC,CAAC,GAAGD,CAAC,GAAGD,CAAC;IACb,IAAIJ,CAAC,GAAGjD,IAAI,CAACwD,KAAK,CAACD,CAAC,CAAC;IACrB,IAAIE,EAAE,GAAGN,aAAa,CAACO,KAAK,CAACO,UAAU;IACvC,IAAIb,CAAC,CAAC,CAAC,CAAC,KAAKA,CAAC,CAACE,CAAC,CAAC,EAAE;MACf,IAAID,CAAC,GAAG,CAAC,EAAE;QACPJ,CAAC,GAAGjD,IAAI,CAACwD,KAAK,CAAED,CAAC,GAAGD,CAAC,IAAI,CAAC,GAAGD,CAAC,CAAE,CAAC;MACrC;MACA,OAAOI,EAAE,CAACL,CAAC,CAAC,CAACH,CAAC,GAAG,CAAC,GAAGK,CAAC,IAAIA,CAAC,CAAC,EAAEF,CAAC,CAACH,CAAC,CAAC,EAAEG,CAAC,CAAC,CAACH,CAAC,GAAG,CAAC,IAAIK,CAAC,CAAC,EAAEF,CAAC,CAAC,CAACH,CAAC,GAAG,CAAC,IAAIK,CAAC,CAAC,EAAEC,CAAC,GAAGN,CAAC,CAAC;IAC9E,CAAC,MACI;MACD,IAAII,CAAC,GAAG,CAAC,EAAE;QACP,OAAOD,CAAC,CAAC,CAAC,CAAC,IAAIK,EAAE,CAACL,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAACG,CAAC,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC,CAAC;MACzD;MACA,IAAIC,CAAC,GAAG,CAAC,EAAE;QACP,OAAOD,CAAC,CAACE,CAAC,CAAC,IAAIG,EAAE,CAACL,CAAC,CAACE,CAAC,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,EAAEF,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,EAAEF,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,GAAGD,CAAC,CAAC,GAAGF,CAAC,CAACE,CAAC,CAAC,CAAC;MACpE;MACA,OAAOG,EAAE,CAACL,CAAC,CAACH,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEG,CAAC,CAACH,CAAC,CAAC,EAAEG,CAAC,CAACE,CAAC,GAAGL,CAAC,GAAG,CAAC,GAAGK,CAAC,GAAGL,CAAC,GAAG,CAAC,CAAC,EAAEG,CAAC,CAACE,CAAC,GAAGL,CAAC,GAAG,CAAC,GAAGK,CAAC,GAAGL,CAAC,GAAG,CAAC,CAAC,EAAEM,CAAC,GAAGN,CAAC,CAAC;IAChG;EACJ,CAAC;EACDS,KAAK,EAAE;IACHrE,MAAM,EAAE,SAAAA,CAAU6E,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAE;MACzB,OAAO,CAACD,EAAE,GAAGD,EAAE,IAAIE,CAAC,GAAGF,EAAE;IAC7B,CAAC;IACDF,SAAS,EAAE,SAAAA,CAAUH,CAAC,EAAEZ,CAAC,EAAE;MACvB,IAAIoB,EAAE,GAAGlB,aAAa,CAACO,KAAK,CAACY,SAAS;MACtC,OAAOD,EAAE,CAACR,CAAC,CAAC,GAAGQ,EAAE,CAACpB,CAAC,CAAC,GAAGoB,EAAE,CAACR,CAAC,GAAGZ,CAAC,CAAC;IACpC,CAAC;IACDqB,SAAS,EAAG,YAAY;MACpB,IAAIC,CAAC,GAAG,CAAC,CAAC,CAAC;MACX,OAAO,UAAUV,CAAC,EAAE;QAChB,IAAIpD,CAAC,GAAG,CAAC;QACT,IAAI8D,CAAC,CAACV,CAAC,CAAC,EAAE;UACN,OAAOU,CAAC,CAACV,CAAC,CAAC;QACf;QACA,KAAK,IAAIZ,CAAC,GAAGY,CAAC,EAAEZ,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UACxBxC,CAAC,IAAIwC,CAAC;QACV;QACAsB,CAAC,CAACV,CAAC,CAAC,GAAGpD,CAAC;QACR,OAAOA,CAAC;MACZ,CAAC;IACL,CAAC,CAAE,CAAC;IACJwD,UAAU,EAAE,SAAAA,CAAUC,EAAE,EAAEC,EAAE,EAAEK,EAAE,EAAEC,EAAE,EAAEL,CAAC,EAAE;MACrC,IAAIM,EAAE,GAAG,CAACF,EAAE,GAAGN,EAAE,IAAI,GAAG;MACxB,IAAIS,EAAE,GAAG,CAACF,EAAE,GAAGN,EAAE,IAAI,GAAG;MACxB,IAAIS,EAAE,GAAGR,CAAC,GAAGA,CAAC;MACd,IAAIS,EAAE,GAAGT,CAAC,GAAGQ,EAAE;MACf,OAAO,CAAC,CAAC,GAAGT,EAAE,GAAG,CAAC,GAAGK,EAAE,GAAGE,EAAE,GAAGC,EAAE,IAAIE,EAAE,GAAG,CAAC,CAAC,CAAC,GAAGV,EAAE,GAAG,CAAC,GAAGK,EAAE,GAAG,CAAC,GAAGE,EAAE,GAAGC,EAAE,IAAIC,EAAE,GAAGF,EAAE,GAAGN,CAAC,GAAGD,EAAE;IACjG;EACJ;AACJ,CAAC;;AAED;AACA;AACA;AACA,IAAIW,QAAQ,GAAG,aAAe,YAAY;EACtC,SAASA,QAAQA,CAAA,EAAG,CACpB;EACAA,QAAQ,CAACC,MAAM,GAAG,YAAY;IAC1B,OAAOD,QAAQ,CAACE,OAAO,EAAE;EAC7B,CAAC;EACDF,QAAQ,CAACE,OAAO,GAAG,CAAC;EACpB,OAAOF,QAAQ;AACnB,CAAC,CAAC,CAAE;AAEJ,IAAIG,SAAS,GAAG,IAAIhE,KAAK,CAAC,CAAC;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIiE,KAAK,GAAG,aAAe,YAAY;EACnC,SAASA,KAAKA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAC1B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;IACpB,IAAI,CAACC,kBAAkB,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,eAAe,GAAGjH,MAAM,CAACG,MAAM,CAACC,IAAI;IACzC,IAAI,CAAC8G,sBAAsB,GAAGjD,aAAa,CAAC9D,MAAM;IAClD;IACA,IAAI,CAACgH,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACC,0BAA0B,GAAG,KAAK;IACvC,IAAI,CAACC,GAAG,GAAG1B,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC5B,IAAI,CAAC0B,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,OAAO,GAAGzB,MAAM;IACrB,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;MAC3B,IAAI,CAAC/C,MAAM,GAAG+C,KAAK;MACnBA,KAAK,CAAC5D,GAAG,CAAC,IAAI,CAAC;IACnB;IACA;IAAA,KACK,IAAI4D,KAAK,KAAK,IAAI,EAAE;MACrB,IAAI,CAAC/C,MAAM,GAAG4C,SAAS;MACvBA,SAAS,CAACzD,GAAG,CAAC,IAAI,CAAC;IACvB;EACJ;EACA0D,KAAK,CAACxD,SAAS,CAACa,KAAK,GAAG,YAAY;IAChC,OAAO,IAAI,CAACiE,GAAG;EACnB,CAAC;EACDtB,KAAK,CAACxD,SAAS,CAACkB,SAAS,GAAG,YAAY;IACpC,OAAO,IAAI,CAACmD,UAAU;EAC1B,CAAC;EACDb,KAAK,CAACxD,SAAS,CAACmF,QAAQ,GAAG,YAAY;IACnC,OAAO,IAAI,CAACxB,SAAS;EACzB,CAAC;EACDH,KAAK,CAACxD,SAAS,CAACoF,WAAW,GAAG,YAAY;IACtC,OAAO,IAAI,CAACpB,SAAS;EACzB,CAAC;EACDR,KAAK,CAACxD,SAAS,CAACqF,EAAE,GAAG,UAAUC,MAAM,EAAEC,QAAQ,EAAE;IAC7C,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;MAAEA,QAAQ,GAAG,IAAI;IAAE;IAC5C,IAAI,IAAI,CAAClB,UAAU,EACf,MAAM,IAAImB,KAAK,CAAC,yFAAyF,CAAC;IAC9G,IAAI,CAAC1B,UAAU,GAAGwB,MAAM;IACxB,IAAI,CAACN,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAAChB,SAAS,GAAGuB,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAGA,QAAQ;IAC5C,OAAO,IAAI;EACf,CAAC;EACD/B,KAAK,CAACxD,SAAS,CAACuF,QAAQ,GAAG,UAAUA,QAAQ,EAAE;IAC3C,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;MAAEA,QAAQ,GAAG,IAAI;IAAE;IAC5C,IAAI,CAACvB,SAAS,GAAGuB,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAGA,QAAQ;IAC5C,OAAO,IAAI;EACf,CAAC;EACD/B,KAAK,CAACxD,SAAS,CAACyF,OAAO,GAAG,UAAUA,OAAO,EAAE;IACzC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,KAAK;IAAE;IAC3C,IAAI,CAACxB,UAAU,GAAGwB,OAAO;IACzB,OAAO,IAAI;EACf,CAAC;EACDjC,KAAK,CAACxD,SAAS,CAAC0F,KAAK,GAAG,UAAUtE,IAAI,EAAEuE,sBAAsB,EAAE;IAC5D,IAAIvE,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG/B,GAAG,CAAC,CAAC;IAAE;IACrC,IAAIsG,sBAAsB,KAAK,KAAK,CAAC,EAAE;MAAEA,sBAAsB,GAAG,KAAK;IAAE;IACzE,IAAI,IAAI,CAACtB,UAAU,EAAE;MACjB,OAAO,IAAI;IACf;IACA,IAAI,CAACF,OAAO,GAAG,IAAI,CAACD,cAAc;IAClC,IAAI,IAAI,CAACI,SAAS,EAAE;MAChB;MACA;MACA,IAAI,CAACA,SAAS,GAAG,KAAK;MACtB,KAAK,IAAIsB,QAAQ,IAAI,IAAI,CAAC7B,kBAAkB,EAAE;QAC1C,IAAI,CAAC8B,yBAAyB,CAACD,QAAQ,CAAC;QACxC,IAAI,CAAC/B,YAAY,CAAC+B,QAAQ,CAAC,GAAG,IAAI,CAAC7B,kBAAkB,CAAC6B,QAAQ,CAAC;MACnE;IACJ;IACA,IAAI,CAACvB,UAAU,GAAG,IAAI;IACtB,IAAI,CAACV,SAAS,GAAG,KAAK;IACtB,IAAI,CAACiB,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACC,0BAA0B,GAAG,KAAK;IACvC,IAAI,CAACE,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACP,UAAU,GAAGpD,IAAI;IACtB,IAAI,CAACoD,UAAU,IAAI,IAAI,CAACD,UAAU;IAClC,IAAI,CAAC,IAAI,CAACS,mBAAmB,IAAIW,sBAAsB,EAAE;MACrD,IAAI,CAACX,mBAAmB,GAAG,IAAI;MAC/B;MACA,IAAI,CAAC,IAAI,CAACf,UAAU,EAAE;QAClB,IAAI6B,GAAG,GAAG,CAAC,CAAC;QACZ,KAAK,IAAIC,IAAI,IAAI,IAAI,CAACjC,UAAU,EAC5BgC,GAAG,CAACC,IAAI,CAAC,GAAG,IAAI,CAACjC,UAAU,CAACiC,IAAI,CAAC;QACrC,IAAI,CAACjC,UAAU,GAAGgC,GAAG;MACzB;MACA,IAAI,CAACE,gBAAgB,CAAC,IAAI,CAACd,OAAO,EAAE,IAAI,CAACrB,YAAY,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,kBAAkB,EAAE4B,sBAAsB,CAAC;IAC5H;IACA,OAAO,IAAI;EACf,CAAC;EACDnC,KAAK,CAACxD,SAAS,CAACiG,sBAAsB,GAAG,UAAU7E,IAAI,EAAE;IACrD,OAAO,IAAI,CAACsE,KAAK,CAACtE,IAAI,EAAE,IAAI,CAAC;EACjC,CAAC;EACDoC,KAAK,CAACxD,SAAS,CAACgG,gBAAgB,GAAG,UAAUd,OAAO,EAAErB,YAAY,EAAEC,UAAU,EAAEC,kBAAkB,EAAE4B,sBAAsB,EAAE;IACxH,KAAK,IAAIC,QAAQ,IAAI9B,UAAU,EAAE;MAC7B,IAAIoC,UAAU,GAAGhB,OAAO,CAACU,QAAQ,CAAC;MAClC,IAAIO,iBAAiB,GAAGC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC;MACjD,IAAII,QAAQ,GAAGH,iBAAiB,GAAG,OAAO,GAAG,OAAOD,UAAU;MAC9D,IAAIK,mBAAmB,GAAG,CAACJ,iBAAiB,IAAIC,KAAK,CAACC,OAAO,CAACvC,UAAU,CAAC8B,QAAQ,CAAC,CAAC;MACnF;MACA;MACA,IAAIU,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,UAAU,EAAE;QACrD;MACJ;MACA;MACA,IAAIC,mBAAmB,EAAE;QACrB,IAAIC,SAAS,GAAG1C,UAAU,CAAC8B,QAAQ,CAAC;QACpC,IAAIY,SAAS,CAAC7G,MAAM,KAAK,CAAC,EAAE;UACxB;QACJ;QACA;QACA;QACA,IAAI8G,IAAI,GAAG,CAACP,UAAU,CAAC;QACvB,KAAK,IAAI3E,CAAC,GAAG,CAAC,EAAEmF,CAAC,GAAGF,SAAS,CAAC7G,MAAM,EAAE4B,CAAC,GAAGmF,CAAC,EAAEnF,CAAC,IAAI,CAAC,EAAE;UACjD,IAAIoF,KAAK,GAAG,IAAI,CAACC,oBAAoB,CAACV,UAAU,EAAEM,SAAS,CAACjF,CAAC,CAAC,CAAC;UAC/D,IAAIsF,KAAK,CAACF,KAAK,CAAC,EAAE;YACdJ,mBAAmB,GAAG,KAAK;YAC3BO,OAAO,CAACC,IAAI,CAAC,6CAA6C,CAAC;YAC3D;UACJ;UACAN,IAAI,CAACO,IAAI,CAACL,KAAK,CAAC;QACpB;QACA,IAAIJ,mBAAmB,EAAE;UACrB;UACAzC,UAAU,CAAC8B,QAAQ,CAAC,GAAGa,IAAI;UAC3B;QACJ;MACJ;MACA;MACA,IAAI,CAACH,QAAQ,KAAK,QAAQ,IAAIH,iBAAiB,KAAKD,UAAU,IAAI,CAACK,mBAAmB,EAAE;QACpF1C,YAAY,CAAC+B,QAAQ,CAAC,GAAGO,iBAAiB,GAAG,EAAE,GAAG,CAAC,CAAC;QACpD,IAAIc,YAAY,GAAGf,UAAU;QAC7B,KAAK,IAAIH,IAAI,IAAIkB,YAAY,EAAE;UAC3BpD,YAAY,CAAC+B,QAAQ,CAAC,CAACG,IAAI,CAAC,GAAGkB,YAAY,CAAClB,IAAI,CAAC;QACrD;QACA;QACAhC,kBAAkB,CAAC6B,QAAQ,CAAC,GAAGO,iBAAiB,GAAG,EAAE,GAAG,CAAC,CAAC;QAC1D,IAAIK,SAAS,GAAG1C,UAAU,CAAC8B,QAAQ,CAAC;QACpC;QACA,IAAI,CAAC,IAAI,CAAC3B,UAAU,EAAE;UAClB,IAAI6B,GAAG,GAAG,CAAC,CAAC;UACZ,KAAK,IAAIC,IAAI,IAAIS,SAAS,EACtBV,GAAG,CAACC,IAAI,CAAC,GAAGS,SAAS,CAACT,IAAI,CAAC;UAC/BjC,UAAU,CAAC8B,QAAQ,CAAC,GAAGY,SAAS,GAAGV,GAAG;QAC1C;QACA,IAAI,CAACE,gBAAgB,CAACiB,YAAY,EAAEpD,YAAY,CAAC+B,QAAQ,CAAC,EAAEY,SAAS,EAAEzC,kBAAkB,CAAC6B,QAAQ,CAAC,EAAED,sBAAsB,CAAC;MAChI,CAAC,MACI;QACD;QACA,IAAI,OAAO9B,YAAY,CAAC+B,QAAQ,CAAC,KAAK,WAAW,IAAID,sBAAsB,EAAE;UACzE9B,YAAY,CAAC+B,QAAQ,CAAC,GAAGM,UAAU;QACvC;QACA,IAAI,CAACC,iBAAiB,EAAE;UACpB;UACA;UACAtC,YAAY,CAAC+B,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;QACnC;QACA,IAAIW,mBAAmB,EAAE;UACrB;UACA;UACAxC,kBAAkB,CAAC6B,QAAQ,CAAC,GAAG9B,UAAU,CAAC8B,QAAQ,CAAC,CAACsB,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;QACzE,CAAC,MACI;UACDpD,kBAAkB,CAAC6B,QAAQ,CAAC,GAAG/B,YAAY,CAAC+B,QAAQ,CAAC,IAAI,CAAC;QAC9D;MACJ;IACJ;EACJ,CAAC;EACDpC,KAAK,CAACxD,SAAS,CAACoH,IAAI,GAAG,YAAY;IAC/B,IAAI,CAAC,IAAI,CAACrC,eAAe,EAAE;MACvB,IAAI,CAACA,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACsC,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC,IAAI,CAAChD,UAAU,EAAE;MAClB,OAAO,IAAI;IACf;IACA,IAAI,CAACA,UAAU,GAAG,KAAK;IACvB,IAAI,CAACV,SAAS,GAAG,KAAK;IACtB,IAAI,IAAI,CAAC2D,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC,IAAI,CAACpC,OAAO,CAAC;IACtC;IACA,OAAO,IAAI;EACf,CAAC;EACD1B,KAAK,CAACxD,SAAS,CAACuH,GAAG,GAAG,YAAY;IAC9B,IAAI,CAACtC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC9D,MAAM,CAAC,IAAI,CAACqD,UAAU,GAAG,IAAI,CAACR,SAAS,CAAC;IAC7C,OAAO,IAAI;EACf,CAAC;EACDR,KAAK,CAACxD,SAAS,CAACwH,KAAK,GAAG,UAAUpG,IAAI,EAAE;IACpC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG/B,GAAG,CAAC,CAAC;IAAE;IACrC,IAAI,IAAI,CAACsE,SAAS,IAAI,CAAC,IAAI,CAACU,UAAU,EAAE;MACpC,OAAO,IAAI;IACf;IACA,IAAI,CAACV,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,WAAW,GAAGxC,IAAI;IACvB,OAAO,IAAI;EACf,CAAC;EACDoC,KAAK,CAACxD,SAAS,CAACyH,MAAM,GAAG,UAAUrG,IAAI,EAAE;IACrC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG/B,GAAG,CAAC,CAAC;IAAE;IACrC,IAAI,CAAC,IAAI,CAACsE,SAAS,IAAI,CAAC,IAAI,CAACU,UAAU,EAAE;MACrC,OAAO,IAAI;IACf;IACA,IAAI,CAACV,SAAS,GAAG,KAAK;IACtB,IAAI,CAACa,UAAU,IAAIpD,IAAI,GAAG,IAAI,CAACwC,WAAW;IAC1C,IAAI,CAACA,WAAW,GAAG,CAAC;IACpB,OAAO,IAAI;EACf,CAAC;EACDJ,KAAK,CAACxD,SAAS,CAACqH,iBAAiB,GAAG,YAAY;IAC5C,KAAK,IAAI9F,CAAC,GAAG,CAAC,EAAEmG,gBAAgB,GAAG,IAAI,CAAC/C,cAAc,CAAChF,MAAM,EAAE4B,CAAC,GAAGmG,gBAAgB,EAAEnG,CAAC,EAAE,EAAE;MACtF,IAAI,CAACoD,cAAc,CAACpD,CAAC,CAAC,CAAC6F,IAAI,CAAC,CAAC;IACjC;IACA,OAAO,IAAI;EACf,CAAC;EACD5D,KAAK,CAACxD,SAAS,CAAC0D,KAAK,GAAG,UAAUA,KAAK,EAAE;IACrC,IAAI,CAACA,KAAK,EAAE;MACRoD,OAAO,CAACC,IAAI,CAAC,4EAA4E,CAAC;MAC1F,OAAO,IAAI;IACf;IACArD,KAAK,CAAC5D,GAAG,CAAC,IAAI,CAAC;IACf,OAAO,IAAI;EACf,CAAC;EACD;AACJ;AACA;EACI0D,KAAK,CAACxD,SAAS,CAACY,MAAM,GAAG,YAAY;IACjC,IAAIL,EAAE;IACN,CAACA,EAAE,GAAG,IAAI,CAACI,MAAM,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,MAAM,CAAC,IAAI,CAAC;IACvE,OAAO,IAAI;EACf,CAAC;EACD4C,KAAK,CAACxD,SAAS,CAAC2H,KAAK,GAAG,UAAU9J,MAAM,EAAE;IACtC,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;MAAEA,MAAM,GAAG,CAAC;IAAE;IACrC,IAAI,CAAC0G,UAAU,GAAG1G,MAAM;IACxB,OAAO,IAAI;EACf,CAAC;EACD2F,KAAK,CAACxD,SAAS,CAAC4H,MAAM,GAAG,UAAUC,KAAK,EAAE;IACtC,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,IAAI,CAAC3D,cAAc,GAAG2D,KAAK;IAC3B,IAAI,CAAC1D,OAAO,GAAG0D,KAAK;IACpB,OAAO,IAAI;EACf,CAAC;EACDrE,KAAK,CAACxD,SAAS,CAAC8H,WAAW,GAAG,UAAUjK,MAAM,EAAE;IAC5C,IAAI,CAACkK,gBAAgB,GAAGlK,MAAM;IAC9B,OAAO,IAAI;EACf,CAAC;EACD2F,KAAK,CAACxD,SAAS,CAACgI,IAAI,GAAG,UAAUA,IAAI,EAAE;IACnC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG,KAAK;IAAE;IACrC,IAAI,CAAC5D,KAAK,GAAG4D,IAAI;IACjB,OAAO,IAAI;EACf,CAAC;EACDxE,KAAK,CAACxD,SAAS,CAACiI,MAAM,GAAG,UAAUC,cAAc,EAAE;IAC/C,IAAIA,cAAc,KAAK,KAAK,CAAC,EAAE;MAAEA,cAAc,GAAG1K,MAAM,CAACG,MAAM,CAACC,IAAI;IAAE;IACtE,IAAI,CAAC6G,eAAe,GAAGyD,cAAc;IACrC,OAAO,IAAI;EACf,CAAC;EACD1E,KAAK,CAACxD,SAAS,CAACmI,aAAa,GAAG,UAAUC,qBAAqB,EAAE;IAC7D,IAAIA,qBAAqB,KAAK,KAAK,CAAC,EAAE;MAAEA,qBAAqB,GAAG3G,aAAa,CAAC9D,MAAM;IAAE;IACtF,IAAI,CAAC+G,sBAAsB,GAAG0D,qBAAqB;IACnD,OAAO,IAAI;EACf,CAAC;EACD;EACA5E,KAAK,CAACxD,SAAS,CAACqI,KAAK,GAAG,YAAY;IAChC,IAAI7I,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC1CD,MAAM,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC9B;IACA,IAAI,CAACkF,cAAc,GAAGnF,MAAM;IAC5B,OAAO,IAAI;EACf,CAAC;EACDgE,KAAK,CAACxD,SAAS,CAACsI,OAAO,GAAG,UAAUC,QAAQ,EAAE;IAC1C,IAAI,CAACC,gBAAgB,GAAGD,QAAQ;IAChC,OAAO,IAAI;EACf,CAAC;EACD/E,KAAK,CAACxD,SAAS,CAACyI,YAAY,GAAG,UAAUF,QAAQ,EAAE;IAC/C,IAAI,CAACG,qBAAqB,GAAGH,QAAQ;IACrC,OAAO,IAAI;EACf,CAAC;EACD/E,KAAK,CAACxD,SAAS,CAAC2I,QAAQ,GAAG,UAAUJ,QAAQ,EAAE;IAC3C,IAAI,CAACK,iBAAiB,GAAGL,QAAQ;IACjC,OAAO,IAAI;EACf,CAAC;EACD/E,KAAK,CAACxD,SAAS,CAAC6I,QAAQ,GAAG,UAAUN,QAAQ,EAAE;IAC3C,IAAI,CAACO,iBAAiB,GAAGP,QAAQ;IACjC,OAAO,IAAI;EACf,CAAC;EACD/E,KAAK,CAACxD,SAAS,CAAC+I,UAAU,GAAG,UAAUR,QAAQ,EAAE;IAC7C,IAAI,CAACS,mBAAmB,GAAGT,QAAQ;IACnC,OAAO,IAAI;EACf,CAAC;EACD/E,KAAK,CAACxD,SAAS,CAACiJ,MAAM,GAAG,UAAUV,QAAQ,EAAE;IACzC,IAAI,CAACjB,eAAe,GAAGiB,QAAQ;IAC/B,OAAO,IAAI;EACf,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI/E,KAAK,CAACxD,SAAS,CAACmB,MAAM,GAAG,UAAUC,IAAI,EAAEI,SAAS,EAAE;IAChD,IAAItB,KAAK,GAAG,IAAI;IAChB,IAAIK,EAAE;IACN,IAAIa,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG/B,GAAG,CAAC,CAAC;IAAE;IACrC,IAAImC,SAAS,KAAK,KAAK,CAAC,EAAE;MAAEA,SAAS,GAAGgC,KAAK,CAAC0F,iBAAiB;IAAE;IACjE,IAAI,IAAI,CAACvF,SAAS,EACd,OAAO,IAAI;IACf,IAAIiC,QAAQ;IACZ,IAAI,CAAC,IAAI,CAACX,QAAQ,IAAI,CAAC,IAAI,CAACZ,UAAU,EAAE;MACpC,IAAI7C,SAAS,EACT,IAAI,CAACkE,KAAK,CAACtE,IAAI,EAAE,IAAI,CAAC,CAAC,KAEvB,OAAO,KAAK;IACpB;IACA,IAAI,CAAC6D,QAAQ,GAAG,KAAK;IACrB,IAAI7D,IAAI,GAAG,IAAI,CAACoD,UAAU,EAAE;MACxB,OAAO,IAAI;IACf;IACA,IAAI,IAAI,CAACI,qBAAqB,KAAK,KAAK,EAAE;MACtC,IAAI,IAAI,CAAC4D,gBAAgB,EAAE;QACvB,IAAI,CAACA,gBAAgB,CAAC,IAAI,CAACtD,OAAO,CAAC;MACvC;MACA,IAAI,CAACN,qBAAqB,GAAG,IAAI;IACrC;IACA,IAAI,IAAI,CAACC,0BAA0B,KAAK,KAAK,EAAE;MAC3C,IAAI,IAAI,CAAC6D,qBAAqB,EAAE;QAC5B,IAAI,CAACA,qBAAqB,CAAC,IAAI,CAACxD,OAAO,CAAC;MAC5C;MACA,IAAI,CAACL,0BAA0B,GAAG,IAAI;IAC1C;IACA,IAAIsE,WAAW,GAAG/H,IAAI,GAAG,IAAI,CAACoD,UAAU;IACxC,IAAI4E,gBAAgB,GAAG,IAAI,CAACpF,SAAS,IAAI,CAACzD,EAAE,GAAG,IAAI,CAACwH,gBAAgB,MAAM,IAAI,IAAIxH,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAACgE,UAAU,CAAC;IACvH,IAAI8E,SAAS,GAAG,IAAI,CAACrF,SAAS,GAAG,IAAI,CAACG,OAAO,GAAGiF,gBAAgB;IAChE,IAAIE,uBAAuB,GAAG,SAAAA,CAAA,EAAY;MACtC,IAAIpJ,KAAK,CAAC8D,SAAS,KAAK,CAAC,EACrB,OAAO,CAAC;MACZ,IAAImF,WAAW,GAAGE,SAAS,EAAE;QACzB,OAAO,CAAC;MACZ;MACA,IAAIE,aAAa,GAAGjL,IAAI,CAACkL,KAAK,CAACL,WAAW,GAAGC,gBAAgB,CAAC;MAC9D,IAAIK,qBAAqB,GAAGN,WAAW,GAAGI,aAAa,GAAGH,gBAAgB;MAC1E;MACA;MACA,IAAIM,OAAO,GAAGpL,IAAI,CAACqL,GAAG,CAACF,qBAAqB,GAAGvJ,KAAK,CAAC8D,SAAS,EAAE,CAAC,CAAC;MAClE,IAAI0F,OAAO,KAAK,CAAC,IAAIP,WAAW,KAAKjJ,KAAK,CAAC8D,SAAS,EAAE;QAClD,OAAO,CAAC;MACZ;MACA,OAAO0F,OAAO;IAClB,CAAC;IACD,IAAIE,OAAO,GAAGN,uBAAuB,CAAC,CAAC;IACvC,IAAI3C,KAAK,GAAG,IAAI,CAAClC,eAAe,CAACmF,OAAO,CAAC;IACzC;IACA,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAAC3E,OAAO,EAAE,IAAI,CAACrB,YAAY,EAAE,IAAI,CAACC,UAAU,EAAE6C,KAAK,CAAC;IAC/E,IAAI,IAAI,CAACiC,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,IAAI,CAAC1D,OAAO,EAAE0E,OAAO,CAAC;IACjD;IACA,IAAI,IAAI,CAAC5F,SAAS,KAAK,CAAC,IAAImF,WAAW,IAAI,IAAI,CAACnF,SAAS,EAAE;MACvD,IAAI,IAAI,CAACG,OAAO,GAAG,CAAC,EAAE;QAClB,IAAI2F,aAAa,GAAGxL,IAAI,CAACqL,GAAG,CAACrL,IAAI,CAACkL,KAAK,CAAC,CAACL,WAAW,GAAG,IAAI,CAACnF,SAAS,IAAIoF,gBAAgB,CAAC,GAAG,CAAC,EAAE,IAAI,CAACjF,OAAO,CAAC;QAC7G,IAAI4F,QAAQ,CAAC,IAAI,CAAC5F,OAAO,CAAC,EAAE;UACxB,IAAI,CAACA,OAAO,IAAI2F,aAAa;QACjC;QACA;QACA,KAAKlE,QAAQ,IAAI,IAAI,CAAC7B,kBAAkB,EAAE;UACtC,IAAI,CAAC,IAAI,CAACK,KAAK,IAAI,OAAO,IAAI,CAACN,UAAU,CAAC8B,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC9D,IAAI,CAAC7B,kBAAkB,CAAC6B,QAAQ,CAAC;YAC7B;YACA;YACA,IAAI,CAAC7B,kBAAkB,CAAC6B,QAAQ,CAAC,GAAGoE,UAAU,CAAC,IAAI,CAAClG,UAAU,CAAC8B,QAAQ,CAAC,CAAC;UACjF;UACA,IAAI,IAAI,CAACxB,KAAK,EAAE;YACZ,IAAI,CAACyB,yBAAyB,CAACD,QAAQ,CAAC;UAC5C;UACA,IAAI,CAAC/B,YAAY,CAAC+B,QAAQ,CAAC,GAAG,IAAI,CAAC7B,kBAAkB,CAAC6B,QAAQ,CAAC;QACnE;QACA,IAAI,IAAI,CAACxB,KAAK,EAAE;UACZ,IAAI,CAACE,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;QACpC;QACA,IAAI,CAACE,UAAU,IAAI4E,gBAAgB,GAAGU,aAAa;QACnD,IAAI,IAAI,CAAChB,iBAAiB,EAAE;UACxB,IAAI,CAACA,iBAAiB,CAAC,IAAI,CAAC5D,OAAO,CAAC;QACxC;QACA,IAAI,CAACL,0BAA0B,GAAG,KAAK;QACvC,OAAO,IAAI;MACf,CAAC,MACI;QACD,IAAI,IAAI,CAACmE,mBAAmB,EAAE;UAC1B,IAAI,CAACA,mBAAmB,CAAC,IAAI,CAAC9D,OAAO,CAAC;QAC1C;QACA,KAAK,IAAI3D,CAAC,GAAG,CAAC,EAAEmG,gBAAgB,GAAG,IAAI,CAAC/C,cAAc,CAAChF,MAAM,EAAE4B,CAAC,GAAGmG,gBAAgB,EAAEnG,CAAC,EAAE,EAAE;UACtF;UACA;UACA,IAAI,CAACoD,cAAc,CAACpD,CAAC,CAAC,CAACmE,KAAK,CAAC,IAAI,CAAClB,UAAU,GAAG,IAAI,CAACR,SAAS,EAAE,KAAK,CAAC;QACzE;QACA,IAAI,CAACK,UAAU,GAAG,KAAK;QACvB,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACDb,KAAK,CAACxD,SAAS,CAAC6J,iBAAiB,GAAG,UAAU3E,OAAO,EAAErB,YAAY,EAAEC,UAAU,EAAE6C,KAAK,EAAE;IACpF,KAAK,IAAIf,QAAQ,IAAI9B,UAAU,EAAE;MAC7B;MACA,IAAID,YAAY,CAAC+B,QAAQ,CAAC,KAAK7E,SAAS,EAAE;QACtC;MACJ;MACA,IAAI2E,KAAK,GAAG7B,YAAY,CAAC+B,QAAQ,CAAC,IAAI,CAAC;MACvC,IAAI2B,GAAG,GAAGzD,UAAU,CAAC8B,QAAQ,CAAC;MAC9B,IAAIqE,YAAY,GAAG7D,KAAK,CAACC,OAAO,CAACnB,OAAO,CAACU,QAAQ,CAAC,CAAC;MACnD,IAAIsE,UAAU,GAAG9D,KAAK,CAACC,OAAO,CAACkB,GAAG,CAAC;MACnC,IAAIhB,mBAAmB,GAAG,CAAC0D,YAAY,IAAIC,UAAU;MACrD,IAAI3D,mBAAmB,EAAE;QACrBrB,OAAO,CAACU,QAAQ,CAAC,GAAG,IAAI,CAAClB,sBAAsB,CAAC6C,GAAG,EAAEZ,KAAK,CAAC;MAC/D,CAAC,MACI,IAAI,OAAOY,GAAG,KAAK,QAAQ,IAAIA,GAAG,EAAE;QACrC;QACA;QACA,IAAI,CAACsC,iBAAiB,CAAC3E,OAAO,CAACU,QAAQ,CAAC,EAAEF,KAAK,EAAE6B,GAAG,EAAEZ,KAAK,CAAC;MAChE,CAAC,MACI;QACD;QACAY,GAAG,GAAG,IAAI,CAACX,oBAAoB,CAAClB,KAAK,EAAE6B,GAAG,CAAC;QAC3C;QACA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UACzB;UACA;UACArC,OAAO,CAACU,QAAQ,CAAC,GAAGF,KAAK,GAAG,CAAC6B,GAAG,GAAG7B,KAAK,IAAIiB,KAAK;QACrD;MACJ;IACJ;EACJ,CAAC;EACDnD,KAAK,CAACxD,SAAS,CAAC4G,oBAAoB,GAAG,UAAUlB,KAAK,EAAE6B,GAAG,EAAE;IACzD,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACzB,OAAOA,GAAG;IACd;IACA,IAAIA,GAAG,CAAC4C,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI5C,GAAG,CAAC4C,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAChD,OAAOzE,KAAK,GAAGsE,UAAU,CAACzC,GAAG,CAAC;IAClC;IACA,OAAOyC,UAAU,CAACzC,GAAG,CAAC;EAC1B,CAAC;EACD/D,KAAK,CAACxD,SAAS,CAAC6F,yBAAyB,GAAG,UAAUD,QAAQ,EAAE;IAC5D,IAAIE,GAAG,GAAG,IAAI,CAAC/B,kBAAkB,CAAC6B,QAAQ,CAAC;IAC3C,IAAIwE,QAAQ,GAAG,IAAI,CAACtG,UAAU,CAAC8B,QAAQ,CAAC;IACxC,IAAI,OAAOwE,QAAQ,KAAK,QAAQ,EAAE;MAC9B,IAAI,CAACrG,kBAAkB,CAAC6B,QAAQ,CAAC,GAAG,IAAI,CAAC7B,kBAAkB,CAAC6B,QAAQ,CAAC,GAAGoE,UAAU,CAACI,QAAQ,CAAC;IAChG,CAAC,MACI;MACD,IAAI,CAACrG,kBAAkB,CAAC6B,QAAQ,CAAC,GAAG,IAAI,CAAC9B,UAAU,CAAC8B,QAAQ,CAAC;IACjE;IACA,IAAI,CAAC9B,UAAU,CAAC8B,QAAQ,CAAC,GAAGE,GAAG;EACnC,CAAC;EACDtC,KAAK,CAAC0F,iBAAiB,GAAG,KAAK;EAC/B,OAAO1F,KAAK;AAChB,CAAC,CAAC,CAAE;AAEJ,IAAI6G,OAAO,GAAG,QAAQ;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIhH,MAAM,GAAGD,QAAQ,CAACC,MAAM;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,IAAIiH,KAAK,GAAG/G,SAAS;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAItD,MAAM,GAAGqK,KAAK,CAACrK,MAAM,CAACsK,IAAI,CAACD,KAAK,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIhK,SAAS,GAAGgK,KAAK,CAAChK,SAAS,CAACiK,IAAI,CAACD,KAAK,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIxK,GAAG,GAAGwK,KAAK,CAACxK,GAAG,CAACyK,IAAI,CAACD,KAAK,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI1J,MAAM,GAAG0J,KAAK,CAAC1J,MAAM,CAAC2J,IAAI,CAACD,KAAK,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAInJ,MAAM,GAAGmJ,KAAK,CAACnJ,MAAM,CAACoJ,IAAI,CAACD,KAAK,CAAC;AACrC,IAAIE,OAAO,GAAG;EACVhN,MAAM,EAAEA,MAAM;EACd+B,KAAK,EAAEA,KAAK;EACZkC,aAAa,EAAEA,aAAa;EAC5BpC,GAAG,EAAEA,GAAG;EACR+D,QAAQ,EAAEA,QAAQ;EAClBC,MAAM,EAAEA,MAAM;EACdG,KAAK,EAAEA,KAAK;EACZ6G,OAAO,EAAEA,OAAO;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIpK,MAAM,EAAEA,MAAM;EACd;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIK,SAAS,EAAEA,SAAS;EACpB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,GAAG,EAAEA,GAAG;EACR;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIc,MAAM,EAAEA,MAAM;EACd;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIO,MAAM,EAAEA;AACZ,CAAC;AAED,SAAS3D,MAAM,EAAE+B,KAAK,EAAEkC,aAAa,EAAE2B,QAAQ,EAAEI,KAAK,EAAE6G,OAAO,EAAEvK,GAAG,EAAE0K,OAAO,IAAIC,OAAO,EAAExK,MAAM,EAAEoD,MAAM,EAAEhE,GAAG,EAAEuB,MAAM,EAAEN,SAAS,EAAEa,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}