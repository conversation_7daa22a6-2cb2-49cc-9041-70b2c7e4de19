{"ast": null, "code": "/**\n * react-router v7.3.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { FrameworkContext, RemixErrorBoundary, RouterProvider, createBrowserHistory, createClientRoutes, createClientRoutesWithHMRRevalidationOptOut, createRouter, decodeViaTurboStream, deserializeErrors, getPatchRoutesOnNavigationFunction, getSingleFetchDataStrategy, invariant, mapRouteProperties, matchRoutes, shouldHydrateRouteLoader, useFogOFWarDiscovery } from \"./chunk-K6CSEXPM.mjs\";\n\n// lib/dom-export/dom-router-provider.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nfunction RouterProvider2(props) {\n  return /* @__PURE__ */React.createElement(RouterProvider, {\n    flushSync: ReactDOM.flushSync,\n    ...props\n  });\n}\n\n// lib/dom-export/hydrated-router.tsx\nimport * as React2 from \"react\";\nvar ssrInfo = null;\nvar router = null;\nfunction initSsrInfo() {\n  if (!ssrInfo && window.__reactRouterContext && window.__reactRouterManifest && window.__reactRouterRouteModules) {\n    ssrInfo = {\n      context: window.__reactRouterContext,\n      manifest: window.__reactRouterManifest,\n      routeModules: window.__reactRouterRouteModules,\n      stateDecodingPromise: void 0,\n      router: void 0,\n      routerInitialized: false\n    };\n  }\n}\nfunction createHydratedRouter({\n  unstable_getContext\n}) {\n  initSsrInfo();\n  if (!ssrInfo) {\n    throw new Error(\"You must be using the SSR features of React Router in order to skip passing a `router` prop to `<RouterProvider>`\");\n  }\n  let localSsrInfo = ssrInfo;\n  if (!ssrInfo.stateDecodingPromise) {\n    let stream = ssrInfo.context.stream;\n    invariant(stream, \"No stream found for single fetch decoding\");\n    ssrInfo.context.stream = void 0;\n    ssrInfo.stateDecodingPromise = decodeViaTurboStream(stream, window).then(value => {\n      ssrInfo.context.state = value.value;\n      localSsrInfo.stateDecodingPromise.value = true;\n    }).catch(e => {\n      localSsrInfo.stateDecodingPromise.error = e;\n    });\n  }\n  if (ssrInfo.stateDecodingPromise.error) {\n    throw ssrInfo.stateDecodingPromise.error;\n  }\n  if (!ssrInfo.stateDecodingPromise.value) {\n    throw ssrInfo.stateDecodingPromise;\n  }\n  let routes = createClientRoutes(ssrInfo.manifest.routes, ssrInfo.routeModules, ssrInfo.context.state, ssrInfo.context.ssr, ssrInfo.context.isSpaMode);\n  let hydrationData = void 0;\n  let loaderData = ssrInfo.context.state.loaderData;\n  if (ssrInfo.context.isSpaMode) {\n    hydrationData = {\n      loaderData\n    };\n  } else {\n    hydrationData = {\n      ...ssrInfo.context.state,\n      loaderData: {\n        ...loaderData\n      }\n    };\n    let initialMatches = matchRoutes(routes, window.location, window.__reactRouterContext?.basename);\n    if (initialMatches) {\n      for (let match of initialMatches) {\n        let routeId = match.route.id;\n        let route = ssrInfo.routeModules[routeId];\n        let manifestRoute = ssrInfo.manifest.routes[routeId];\n        if (route && manifestRoute && shouldHydrateRouteLoader(manifestRoute, route, ssrInfo.context.isSpaMode) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n          delete hydrationData.loaderData[routeId];\n        } else if (manifestRoute && !manifestRoute.hasLoader) {\n          hydrationData.loaderData[routeId] = null;\n        }\n      }\n    }\n    if (hydrationData && hydrationData.errors) {\n      hydrationData.errors = deserializeErrors(hydrationData.errors);\n    }\n  }\n  let router2 = createRouter({\n    routes,\n    history: createBrowserHistory(),\n    basename: ssrInfo.context.basename,\n    unstable_getContext,\n    hydrationData,\n    mapRouteProperties,\n    future: {\n      unstable_middleware: ssrInfo.context.future.unstable_middleware\n    },\n    dataStrategy: getSingleFetchDataStrategy(ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, ssrInfo.context.basename, () => router2),\n    patchRoutesOnNavigation: getPatchRoutesOnNavigationFunction(ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, ssrInfo.context.isSpaMode, ssrInfo.context.basename)\n  });\n  ssrInfo.router = router2;\n  if (router2.state.initialized) {\n    ssrInfo.routerInitialized = true;\n    router2.initialize();\n  }\n  router2.createRoutesForHMR = /* spacer so ts-ignore does not affect the right hand of the assignment */\n  createClientRoutesWithHMRRevalidationOptOut;\n  window.__reactRouterDataRouter = router2;\n  return router2;\n}\nfunction HydratedRouter(props) {\n  if (!router) {\n    router = createHydratedRouter({\n      unstable_getContext: props.unstable_getContext\n    });\n  }\n  let [criticalCss, setCriticalCss] = React2.useState(process.env.NODE_ENV === \"development\" ? ssrInfo?.context.criticalCss : void 0);\n  if (process.env.NODE_ENV === \"development\") {\n    if (ssrInfo) {\n      window.__reactRouterClearCriticalCss = () => setCriticalCss(void 0);\n    }\n  }\n  let [location, setLocation] = React2.useState(router.state.location);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router && !ssrInfo.routerInitialized) {\n      ssrInfo.routerInitialized = true;\n      ssrInfo.router.initialize();\n    }\n  }, []);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router) {\n      return ssrInfo.router.subscribe(newState => {\n        if (newState.location !== location) {\n          setLocation(newState.location);\n        }\n      });\n    }\n  }, [location]);\n  invariant(ssrInfo, \"ssrInfo unavailable for HydratedRouter\");\n  useFogOFWarDiscovery(router, ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, ssrInfo.context.isSpaMode);\n  return (\n    // This fragment is important to ensure we match the <ServerRouter> JSX\n    // structure so that useId values hydrate correctly\n    /* @__PURE__ */\n    React2.createElement(React2.Fragment, null, /* @__PURE__ */React2.createElement(FrameworkContext.Provider, {\n      value: {\n        manifest: ssrInfo.manifest,\n        routeModules: ssrInfo.routeModules,\n        future: ssrInfo.context.future,\n        criticalCss,\n        ssr: ssrInfo.context.ssr,\n        isSpaMode: ssrInfo.context.isSpaMode\n      }\n    }, /* @__PURE__ */React2.createElement(RemixErrorBoundary, {\n      location\n    }, /* @__PURE__ */React2.createElement(RouterProvider2, {\n      router\n    }))), /* @__PURE__ */React2.createElement(React2.Fragment, null))\n  );\n}\nexport { HydratedRouter, RouterProvider2 as RouterProvider };", "map": {"version": 3, "names": ["FrameworkContext", "RemixErrorBoundary", "RouterProvider", "createBrowserHistory", "createClientRoutes", "createClientRoutesWithHMRRevalidationOptOut", "createRouter", "decodeViaTurboStream", "deserializeErrors", "getPatchRoutesOnNavigationFunction", "getSingleFetchDataStrategy", "invariant", "mapRouteProperties", "matchRoutes", "shouldHydrateRouteLoader", "useFogOFWarDiscovery", "React", "ReactDOM", "RouterProvider2", "props", "createElement", "flushSync", "React2", "ssrInfo", "router", "initSsrInfo", "window", "__reactRouterContext", "__reactRouterManifest", "__reactRouterRouteModules", "context", "manifest", "routeModules", "stateDecodingPromise", "routerInitialized", "createHydratedRouter", "unstable_getContext", "Error", "localSsrInfo", "stream", "then", "value", "state", "catch", "e", "error", "routes", "ssr", "isSpaMode", "hydrationData", "loaderData", "initialMatches", "location", "basename", "match", "routeId", "route", "id", "manifestRoute", "HydrateFallback", "<PERSON><PERSON><PERSON><PERSON>", "errors", "router2", "history", "future", "unstable_middleware", "dataStrategy", "patchRoutesOnNavigation", "initialized", "initialize", "createRoutesForHMR", "__reactRouterDataRouter", "HydratedRouter", "criticalCss", "setCriticalCss", "useState", "process", "env", "NODE_ENV", "__reactRouterClearCriticalCss", "setLocation", "useLayoutEffect", "subscribe", "newState", "Fragment", "Provider"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/react-router/dist/development/dom-export.mjs"], "sourcesContent": ["/**\n * react-router v7.3.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport {\n  FrameworkContext,\n  RemixErrorBoundary,\n  RouterProvider,\n  createBrowserHistory,\n  createClientRoutes,\n  createClientRoutesWithHMRRevalidationOptOut,\n  createRouter,\n  decodeViaTurboStream,\n  deserializeErrors,\n  getPatchRoutesOnNavigationFunction,\n  getSingleFetchDataStrategy,\n  invariant,\n  mapRouteProperties,\n  matchRoutes,\n  shouldHydrateRouteLoader,\n  useFogOFWarDiscovery\n} from \"./chunk-K6CSEXPM.mjs\";\n\n// lib/dom-export/dom-router-provider.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nfunction RouterProvider2(props) {\n  return /* @__PURE__ */ React.createElement(RouterProvider, { flushSync: ReactDOM.flushSync, ...props });\n}\n\n// lib/dom-export/hydrated-router.tsx\nimport * as React2 from \"react\";\nvar ssrInfo = null;\nvar router = null;\nfunction initSsrInfo() {\n  if (!ssrInfo && window.__reactRouterContext && window.__reactRouterManifest && window.__reactRouterRouteModules) {\n    ssrInfo = {\n      context: window.__reactRouterContext,\n      manifest: window.__reactRouterManifest,\n      routeModules: window.__reactRouterRouteModules,\n      stateDecodingPromise: void 0,\n      router: void 0,\n      routerInitialized: false\n    };\n  }\n}\nfunction createHydratedRouter({\n  unstable_getContext\n}) {\n  initSsrInfo();\n  if (!ssrInfo) {\n    throw new Error(\n      \"You must be using the SSR features of React Router in order to skip passing a `router` prop to `<RouterProvider>`\"\n    );\n  }\n  let localSsrInfo = ssrInfo;\n  if (!ssrInfo.stateDecodingPromise) {\n    let stream = ssrInfo.context.stream;\n    invariant(stream, \"No stream found for single fetch decoding\");\n    ssrInfo.context.stream = void 0;\n    ssrInfo.stateDecodingPromise = decodeViaTurboStream(stream, window).then((value) => {\n      ssrInfo.context.state = value.value;\n      localSsrInfo.stateDecodingPromise.value = true;\n    }).catch((e) => {\n      localSsrInfo.stateDecodingPromise.error = e;\n    });\n  }\n  if (ssrInfo.stateDecodingPromise.error) {\n    throw ssrInfo.stateDecodingPromise.error;\n  }\n  if (!ssrInfo.stateDecodingPromise.value) {\n    throw ssrInfo.stateDecodingPromise;\n  }\n  let routes = createClientRoutes(\n    ssrInfo.manifest.routes,\n    ssrInfo.routeModules,\n    ssrInfo.context.state,\n    ssrInfo.context.ssr,\n    ssrInfo.context.isSpaMode\n  );\n  let hydrationData = void 0;\n  let loaderData = ssrInfo.context.state.loaderData;\n  if (ssrInfo.context.isSpaMode) {\n    hydrationData = { loaderData };\n  } else {\n    hydrationData = {\n      ...ssrInfo.context.state,\n      loaderData: { ...loaderData }\n    };\n    let initialMatches = matchRoutes(\n      routes,\n      window.location,\n      window.__reactRouterContext?.basename\n    );\n    if (initialMatches) {\n      for (let match of initialMatches) {\n        let routeId = match.route.id;\n        let route = ssrInfo.routeModules[routeId];\n        let manifestRoute = ssrInfo.manifest.routes[routeId];\n        if (route && manifestRoute && shouldHydrateRouteLoader(\n          manifestRoute,\n          route,\n          ssrInfo.context.isSpaMode\n        ) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n          delete hydrationData.loaderData[routeId];\n        } else if (manifestRoute && !manifestRoute.hasLoader) {\n          hydrationData.loaderData[routeId] = null;\n        }\n      }\n    }\n    if (hydrationData && hydrationData.errors) {\n      hydrationData.errors = deserializeErrors(hydrationData.errors);\n    }\n  }\n  let router2 = createRouter({\n    routes,\n    history: createBrowserHistory(),\n    basename: ssrInfo.context.basename,\n    unstable_getContext,\n    hydrationData,\n    mapRouteProperties,\n    future: {\n      unstable_middleware: ssrInfo.context.future.unstable_middleware\n    },\n    dataStrategy: getSingleFetchDataStrategy(\n      ssrInfo.manifest,\n      ssrInfo.routeModules,\n      ssrInfo.context.ssr,\n      ssrInfo.context.basename,\n      () => router2\n    ),\n    patchRoutesOnNavigation: getPatchRoutesOnNavigationFunction(\n      ssrInfo.manifest,\n      ssrInfo.routeModules,\n      ssrInfo.context.ssr,\n      ssrInfo.context.isSpaMode,\n      ssrInfo.context.basename\n    )\n  });\n  ssrInfo.router = router2;\n  if (router2.state.initialized) {\n    ssrInfo.routerInitialized = true;\n    router2.initialize();\n  }\n  router2.createRoutesForHMR = /* spacer so ts-ignore does not affect the right hand of the assignment */\n  createClientRoutesWithHMRRevalidationOptOut;\n  window.__reactRouterDataRouter = router2;\n  return router2;\n}\nfunction HydratedRouter(props) {\n  if (!router) {\n    router = createHydratedRouter({\n      unstable_getContext: props.unstable_getContext\n    });\n  }\n  let [criticalCss, setCriticalCss] = React2.useState(\n    process.env.NODE_ENV === \"development\" ? ssrInfo?.context.criticalCss : void 0\n  );\n  if (process.env.NODE_ENV === \"development\") {\n    if (ssrInfo) {\n      window.__reactRouterClearCriticalCss = () => setCriticalCss(void 0);\n    }\n  }\n  let [location, setLocation] = React2.useState(router.state.location);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router && !ssrInfo.routerInitialized) {\n      ssrInfo.routerInitialized = true;\n      ssrInfo.router.initialize();\n    }\n  }, []);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router) {\n      return ssrInfo.router.subscribe((newState) => {\n        if (newState.location !== location) {\n          setLocation(newState.location);\n        }\n      });\n    }\n  }, [location]);\n  invariant(ssrInfo, \"ssrInfo unavailable for HydratedRouter\");\n  useFogOFWarDiscovery(\n    router,\n    ssrInfo.manifest,\n    ssrInfo.routeModules,\n    ssrInfo.context.ssr,\n    ssrInfo.context.isSpaMode\n  );\n  return (\n    // This fragment is important to ensure we match the <ServerRouter> JSX\n    // structure so that useId values hydrate correctly\n    /* @__PURE__ */ React2.createElement(React2.Fragment, null, /* @__PURE__ */ React2.createElement(\n      FrameworkContext.Provider,\n      {\n        value: {\n          manifest: ssrInfo.manifest,\n          routeModules: ssrInfo.routeModules,\n          future: ssrInfo.context.future,\n          criticalCss,\n          ssr: ssrInfo.context.ssr,\n          isSpaMode: ssrInfo.context.isSpaMode\n        }\n      },\n      /* @__PURE__ */ React2.createElement(RemixErrorBoundary, { location }, /* @__PURE__ */ React2.createElement(RouterProvider2, { router }))\n    ), /* @__PURE__ */ React2.createElement(React2.Fragment, null))\n  );\n}\nexport {\n  HydratedRouter,\n  RouterProvider2 as RouterProvider\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SACEA,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,oBAAoB,EACpBC,kBAAkB,EAClBC,2CAA2C,EAC3CC,YAAY,EACZC,oBAAoB,EACpBC,iBAAiB,EACjBC,kCAAkC,EAClCC,0BAA0B,EAC1BC,SAAS,EACTC,kBAAkB,EAClBC,WAAW,EACXC,wBAAwB,EACxBC,oBAAoB,QACf,sBAAsB;;AAE7B;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,eAAgBH,KAAK,CAACI,aAAa,CAAClB,cAAc,EAAE;IAAEmB,SAAS,EAAEJ,QAAQ,CAACI,SAAS;IAAE,GAAGF;EAAM,CAAC,CAAC;AACzG;;AAEA;AACA,OAAO,KAAKG,MAAM,MAAM,OAAO;AAC/B,IAAIC,OAAO,GAAG,IAAI;AAClB,IAAIC,MAAM,GAAG,IAAI;AACjB,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAI,CAACF,OAAO,IAAIG,MAAM,CAACC,oBAAoB,IAAID,MAAM,CAACE,qBAAqB,IAAIF,MAAM,CAACG,yBAAyB,EAAE;IAC/GN,OAAO,GAAG;MACRO,OAAO,EAAEJ,MAAM,CAACC,oBAAoB;MACpCI,QAAQ,EAAEL,MAAM,CAACE,qBAAqB;MACtCI,YAAY,EAAEN,MAAM,CAACG,yBAAyB;MAC9CI,oBAAoB,EAAE,KAAK,CAAC;MAC5BT,MAAM,EAAE,KAAK,CAAC;MACdU,iBAAiB,EAAE;IACrB,CAAC;EACH;AACF;AACA,SAASC,oBAAoBA,CAAC;EAC5BC;AACF,CAAC,EAAE;EACDX,WAAW,CAAC,CAAC;EACb,IAAI,CAACF,OAAO,EAAE;IACZ,MAAM,IAAIc,KAAK,CACb,mHACF,CAAC;EACH;EACA,IAAIC,YAAY,GAAGf,OAAO;EAC1B,IAAI,CAACA,OAAO,CAACU,oBAAoB,EAAE;IACjC,IAAIM,MAAM,GAAGhB,OAAO,CAACO,OAAO,CAACS,MAAM;IACnC5B,SAAS,CAAC4B,MAAM,EAAE,2CAA2C,CAAC;IAC9DhB,OAAO,CAACO,OAAO,CAACS,MAAM,GAAG,KAAK,CAAC;IAC/BhB,OAAO,CAACU,oBAAoB,GAAG1B,oBAAoB,CAACgC,MAAM,EAAEb,MAAM,CAAC,CAACc,IAAI,CAAEC,KAAK,IAAK;MAClFlB,OAAO,CAACO,OAAO,CAACY,KAAK,GAAGD,KAAK,CAACA,KAAK;MACnCH,YAAY,CAACL,oBAAoB,CAACQ,KAAK,GAAG,IAAI;IAChD,CAAC,CAAC,CAACE,KAAK,CAAEC,CAAC,IAAK;MACdN,YAAY,CAACL,oBAAoB,CAACY,KAAK,GAAGD,CAAC;IAC7C,CAAC,CAAC;EACJ;EACA,IAAIrB,OAAO,CAACU,oBAAoB,CAACY,KAAK,EAAE;IACtC,MAAMtB,OAAO,CAACU,oBAAoB,CAACY,KAAK;EAC1C;EACA,IAAI,CAACtB,OAAO,CAACU,oBAAoB,CAACQ,KAAK,EAAE;IACvC,MAAMlB,OAAO,CAACU,oBAAoB;EACpC;EACA,IAAIa,MAAM,GAAG1C,kBAAkB,CAC7BmB,OAAO,CAACQ,QAAQ,CAACe,MAAM,EACvBvB,OAAO,CAACS,YAAY,EACpBT,OAAO,CAACO,OAAO,CAACY,KAAK,EACrBnB,OAAO,CAACO,OAAO,CAACiB,GAAG,EACnBxB,OAAO,CAACO,OAAO,CAACkB,SAClB,CAAC;EACD,IAAIC,aAAa,GAAG,KAAK,CAAC;EAC1B,IAAIC,UAAU,GAAG3B,OAAO,CAACO,OAAO,CAACY,KAAK,CAACQ,UAAU;EACjD,IAAI3B,OAAO,CAACO,OAAO,CAACkB,SAAS,EAAE;IAC7BC,aAAa,GAAG;MAAEC;IAAW,CAAC;EAChC,CAAC,MAAM;IACLD,aAAa,GAAG;MACd,GAAG1B,OAAO,CAACO,OAAO,CAACY,KAAK;MACxBQ,UAAU,EAAE;QAAE,GAAGA;MAAW;IAC9B,CAAC;IACD,IAAIC,cAAc,GAAGtC,WAAW,CAC9BiC,MAAM,EACNpB,MAAM,CAAC0B,QAAQ,EACf1B,MAAM,CAACC,oBAAoB,EAAE0B,QAC/B,CAAC;IACD,IAAIF,cAAc,EAAE;MAClB,KAAK,IAAIG,KAAK,IAAIH,cAAc,EAAE;QAChC,IAAII,OAAO,GAAGD,KAAK,CAACE,KAAK,CAACC,EAAE;QAC5B,IAAID,KAAK,GAAGjC,OAAO,CAACS,YAAY,CAACuB,OAAO,CAAC;QACzC,IAAIG,aAAa,GAAGnC,OAAO,CAACQ,QAAQ,CAACe,MAAM,CAACS,OAAO,CAAC;QACpD,IAAIC,KAAK,IAAIE,aAAa,IAAI5C,wBAAwB,CACpD4C,aAAa,EACbF,KAAK,EACLjC,OAAO,CAACO,OAAO,CAACkB,SAClB,CAAC,KAAKQ,KAAK,CAACG,eAAe,IAAI,CAACD,aAAa,CAACE,SAAS,CAAC,EAAE;UACxD,OAAOX,aAAa,CAACC,UAAU,CAACK,OAAO,CAAC;QAC1C,CAAC,MAAM,IAAIG,aAAa,IAAI,CAACA,aAAa,CAACE,SAAS,EAAE;UACpDX,aAAa,CAACC,UAAU,CAACK,OAAO,CAAC,GAAG,IAAI;QAC1C;MACF;IACF;IACA,IAAIN,aAAa,IAAIA,aAAa,CAACY,MAAM,EAAE;MACzCZ,aAAa,CAACY,MAAM,GAAGrD,iBAAiB,CAACyC,aAAa,CAACY,MAAM,CAAC;IAChE;EACF;EACA,IAAIC,OAAO,GAAGxD,YAAY,CAAC;IACzBwC,MAAM;IACNiB,OAAO,EAAE5D,oBAAoB,CAAC,CAAC;IAC/BkD,QAAQ,EAAE9B,OAAO,CAACO,OAAO,CAACuB,QAAQ;IAClCjB,mBAAmB;IACnBa,aAAa;IACbrC,kBAAkB;IAClBoD,MAAM,EAAE;MACNC,mBAAmB,EAAE1C,OAAO,CAACO,OAAO,CAACkC,MAAM,CAACC;IAC9C,CAAC;IACDC,YAAY,EAAExD,0BAA0B,CACtCa,OAAO,CAACQ,QAAQ,EAChBR,OAAO,CAACS,YAAY,EACpBT,OAAO,CAACO,OAAO,CAACiB,GAAG,EACnBxB,OAAO,CAACO,OAAO,CAACuB,QAAQ,EACxB,MAAMS,OACR,CAAC;IACDK,uBAAuB,EAAE1D,kCAAkC,CACzDc,OAAO,CAACQ,QAAQ,EAChBR,OAAO,CAACS,YAAY,EACpBT,OAAO,CAACO,OAAO,CAACiB,GAAG,EACnBxB,OAAO,CAACO,OAAO,CAACkB,SAAS,EACzBzB,OAAO,CAACO,OAAO,CAACuB,QAClB;EACF,CAAC,CAAC;EACF9B,OAAO,CAACC,MAAM,GAAGsC,OAAO;EACxB,IAAIA,OAAO,CAACpB,KAAK,CAAC0B,WAAW,EAAE;IAC7B7C,OAAO,CAACW,iBAAiB,GAAG,IAAI;IAChC4B,OAAO,CAACO,UAAU,CAAC,CAAC;EACtB;EACAP,OAAO,CAACQ,kBAAkB,GAAG;EAC7BjE,2CAA2C;EAC3CqB,MAAM,CAAC6C,uBAAuB,GAAGT,OAAO;EACxC,OAAOA,OAAO;AAChB;AACA,SAASU,cAAcA,CAACrD,KAAK,EAAE;EAC7B,IAAI,CAACK,MAAM,EAAE;IACXA,MAAM,GAAGW,oBAAoB,CAAC;MAC5BC,mBAAmB,EAAEjB,KAAK,CAACiB;IAC7B,CAAC,CAAC;EACJ;EACA,IAAI,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGpD,MAAM,CAACqD,QAAQ,CACjDC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,GAAGvD,OAAO,EAAEO,OAAO,CAAC2C,WAAW,GAAG,KAAK,CAC/E,CAAC;EACD,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1C,IAAIvD,OAAO,EAAE;MACXG,MAAM,CAACqD,6BAA6B,GAAG,MAAML,cAAc,CAAC,KAAK,CAAC,CAAC;IACrE;EACF;EACA,IAAI,CAACtB,QAAQ,EAAE4B,WAAW,CAAC,GAAG1D,MAAM,CAACqD,QAAQ,CAACnD,MAAM,CAACkB,KAAK,CAACU,QAAQ,CAAC;EACpE9B,MAAM,CAAC2D,eAAe,CAAC,MAAM;IAC3B,IAAI1D,OAAO,IAAIA,OAAO,CAACC,MAAM,IAAI,CAACD,OAAO,CAACW,iBAAiB,EAAE;MAC3DX,OAAO,CAACW,iBAAiB,GAAG,IAAI;MAChCX,OAAO,CAACC,MAAM,CAAC6C,UAAU,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EACN/C,MAAM,CAAC2D,eAAe,CAAC,MAAM;IAC3B,IAAI1D,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAE;MAC7B,OAAOD,OAAO,CAACC,MAAM,CAAC0D,SAAS,CAAEC,QAAQ,IAAK;QAC5C,IAAIA,QAAQ,CAAC/B,QAAQ,KAAKA,QAAQ,EAAE;UAClC4B,WAAW,CAACG,QAAQ,CAAC/B,QAAQ,CAAC;QAChC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACdzC,SAAS,CAACY,OAAO,EAAE,wCAAwC,CAAC;EAC5DR,oBAAoB,CAClBS,MAAM,EACND,OAAO,CAACQ,QAAQ,EAChBR,OAAO,CAACS,YAAY,EACpBT,OAAO,CAACO,OAAO,CAACiB,GAAG,EACnBxB,OAAO,CAACO,OAAO,CAACkB,SAClB,CAAC;EACD;IACE;IACA;IACA;IAAgB1B,MAAM,CAACF,aAAa,CAACE,MAAM,CAAC8D,QAAQ,EAAE,IAAI,EAAE,eAAgB9D,MAAM,CAACF,aAAa,CAC9FpB,gBAAgB,CAACqF,QAAQ,EACzB;MACE5C,KAAK,EAAE;QACLV,QAAQ,EAAER,OAAO,CAACQ,QAAQ;QAC1BC,YAAY,EAAET,OAAO,CAACS,YAAY;QAClCgC,MAAM,EAAEzC,OAAO,CAACO,OAAO,CAACkC,MAAM;QAC9BS,WAAW;QACX1B,GAAG,EAAExB,OAAO,CAACO,OAAO,CAACiB,GAAG;QACxBC,SAAS,EAAEzB,OAAO,CAACO,OAAO,CAACkB;MAC7B;IACF,CAAC,EACD,eAAgB1B,MAAM,CAACF,aAAa,CAACnB,kBAAkB,EAAE;MAAEmD;IAAS,CAAC,EAAE,eAAgB9B,MAAM,CAACF,aAAa,CAACF,eAAe,EAAE;MAAEM;IAAO,CAAC,CAAC,CAC1I,CAAC,EAAE,eAAgBF,MAAM,CAACF,aAAa,CAACE,MAAM,CAAC8D,QAAQ,EAAE,IAAI,CAAC;EAAC;AAEnE;AACA,SACEZ,cAAc,EACdtD,eAAe,IAAIhB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}