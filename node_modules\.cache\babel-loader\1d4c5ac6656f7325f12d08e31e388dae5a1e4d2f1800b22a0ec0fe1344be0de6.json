{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CodeSandboxCircleFilledSvg from \"@ant-design/icons-svg/es/asn/CodeSandboxCircleFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CodeSandboxCircleFilled = function CodeSandboxCircleFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CodeSandboxCircleFilledSvg\n  }));\n};\n\n/**![code-sandbox-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0yNDMuNyA1ODkuMkw1MTIgNzk0IDI2OC4zIDY1My4yVjM3MS44bDExMC02My42LS40LS4yaC4yTDUxMiAyMzFsMTM0IDc3aC0uMmwtLjMuMiAxMTAuMSA2My42djI4MS40ek0zMDcuOSA1MzYuN2w4Ny42IDQ5LjlWNjgxbDk2LjcgNTUuOVY1MjQuOEwzMDcuOSA0MTguNHptMjAzLjktMTUxLjhMNDE4IDMzMWwtOTEuMSA1Mi42IDE4NS4yIDEwNyAxODUuMi0xMDYuOS05MS40LTUyLjh6bTIwIDM1Mmw5Ny4zLTU2LjJ2LTk0LjFsODctNDkuNVY0MTguNUw1MzEuOCA1MjV6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CodeSandboxCircleFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CodeSandboxCircleFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "CodeSandboxCircleFilledSvg", "AntdIcon", "CodeSandboxCircleFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@ant-design/icons/es/icons/CodeSandboxCircleFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CodeSandboxCircleFilledSvg from \"@ant-design/icons-svg/es/asn/CodeSandboxCircleFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CodeSandboxCircleFilled = function CodeSandboxCircleFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CodeSandboxCircleFilledSvg\n  }));\n};\n\n/**![code-sandbox-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0yNDMuNyA1ODkuMkw1MTIgNzk0IDI2OC4zIDY1My4yVjM3MS44bDExMC02My42LS40LS4yaC4yTDUxMiAyMzFsMTM0IDc3aC0uMmwtLjMuMiAxMTAuMSA2My42djI4MS40ek0zMDcuOSA1MzYuN2w4Ny42IDQ5LjlWNjgxbDk2LjcgNTUuOVY1MjQuOEwzMDcuOSA0MTguNHptMjAzLjktMTUxLjhMNDE4IDMzMWwtOTEuMSA1Mi42IDE4NS4yIDEwNyAxODUuMi0xMDYuOS05MS40LTUyLjh6bTIwIDM1Mmw5Ny4zLTU2LjJ2LTk0LjFsODctNDkuNVY0MTguNUw1MzEuOCA1MjV6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CodeSandboxCircleFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CodeSandboxCircleFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,0BAA0B,MAAM,sDAAsD;AAC7F,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,uBAAuB,CAAC;AACpE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,yBAAyB;AACjD;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}