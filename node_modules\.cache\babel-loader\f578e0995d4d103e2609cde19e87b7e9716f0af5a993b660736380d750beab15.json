{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport hash from '@emotion/hash';\nimport { removeCSS, updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport * as React from 'react';\n// @ts-ignore\nimport unitless from '@emotion/unitless';\nimport { compile, serialize, stringify } from 'stylis';\nimport { contentQuotesLinter, hashedAnimationLinter } from \"../linters\";\nimport StyleContext, { ATTR_CACHE_PATH, ATTR_MARK, ATTR_TOKEN, CSS_IN_JS_INSTANCE } from \"../StyleContext\";\nimport { isClientSide, toStyleStr } from \"../util\";\nimport { CSS_FILE_STYLE, existPath, getStyleAndHash } from \"../util/cacheMapUtil\";\nimport useGlobalCache from \"./useGlobalCache\";\nvar SKIP_CHECK = '_skip_check_';\nvar MULTI_VALUE = '_multi_value_';\n// ============================================================================\n// ==                                 Parser                                 ==\n// ============================================================================\n// Preprocessor style content to browser support one\nexport function normalizeStyle(styleStr) {\n  var serialized = serialize(compile(styleStr), stringify);\n  return serialized.replace(/\\{%%%\\:[^;];}/g, ';');\n}\nfunction isCompoundCSSProperty(value) {\n  return _typeof(value) === 'object' && value && (SKIP_CHECK in value || MULTI_VALUE in value);\n}\n\n// 注入 hash 值\nfunction injectSelectorHash(key, hashId, hashPriority) {\n  if (!hashId) {\n    return key;\n  }\n  var hashClassName = \".\".concat(hashId);\n  var hashSelector = hashPriority === 'low' ? \":where(\".concat(hashClassName, \")\") : hashClassName;\n\n  // 注入 hashId\n  var keys = key.split(',').map(function (k) {\n    var _firstPath$match;\n    var fullPath = k.trim().split(/\\s+/);\n\n    // 如果 Selector 第一个是 HTML Element，那我们就插到它的后面。反之，就插到最前面。\n    var firstPath = fullPath[0] || '';\n    var htmlElement = ((_firstPath$match = firstPath.match(/^\\w+/)) === null || _firstPath$match === void 0 ? void 0 : _firstPath$match[0]) || '';\n    firstPath = \"\".concat(htmlElement).concat(hashSelector).concat(firstPath.slice(htmlElement.length));\n    return [firstPath].concat(_toConsumableArray(fullPath.slice(1))).join(' ');\n  });\n  return keys.join(',');\n}\n// Parse CSSObject to style content\nexport var parseStyle = function parseStyle(interpolation) {\n  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n      root: true,\n      parentSelectors: []\n    },\n    root = _ref.root,\n    injectHash = _ref.injectHash,\n    parentSelectors = _ref.parentSelectors;\n  var hashId = config.hashId,\n    layer = config.layer,\n    path = config.path,\n    hashPriority = config.hashPriority,\n    _config$transformers = config.transformers,\n    transformers = _config$transformers === void 0 ? [] : _config$transformers,\n    _config$linters = config.linters,\n    linters = _config$linters === void 0 ? [] : _config$linters;\n  var styleStr = '';\n  var effectStyle = {};\n  function parseKeyframes(keyframes) {\n    var animationName = keyframes.getName(hashId);\n    if (!effectStyle[animationName]) {\n      var _parseStyle = parseStyle(keyframes.style, config, {\n          root: false,\n          parentSelectors: parentSelectors\n        }),\n        _parseStyle2 = _slicedToArray(_parseStyle, 1),\n        _parsedStr = _parseStyle2[0];\n      effectStyle[animationName] = \"@keyframes \".concat(keyframes.getName(hashId)).concat(_parsedStr);\n    }\n  }\n  function flattenList(list) {\n    var fullList = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    list.forEach(function (item) {\n      if (Array.isArray(item)) {\n        flattenList(item, fullList);\n      } else if (item) {\n        fullList.push(item);\n      }\n    });\n    return fullList;\n  }\n  var flattenStyleList = flattenList(Array.isArray(interpolation) ? interpolation : [interpolation]);\n  flattenStyleList.forEach(function (originStyle) {\n    // Only root level can use raw string\n    var style = typeof originStyle === 'string' && !root ? {} : originStyle;\n    if (typeof style === 'string') {\n      styleStr += \"\".concat(style, \"\\n\");\n    } else if (style._keyframe) {\n      // Keyframe\n      parseKeyframes(style);\n    } else {\n      var mergedStyle = transformers.reduce(function (prev, trans) {\n        var _trans$visit;\n        return (trans === null || trans === void 0 || (_trans$visit = trans.visit) === null || _trans$visit === void 0 ? void 0 : _trans$visit.call(trans, prev)) || prev;\n      }, style);\n\n      // Normal CSSObject\n      Object.keys(mergedStyle).forEach(function (key) {\n        var value = mergedStyle[key];\n        if (_typeof(value) === 'object' && value && (key !== 'animationName' || !value._keyframe) && !isCompoundCSSProperty(value)) {\n          var subInjectHash = false;\n\n          // 当成嵌套对象来处理\n          var mergedKey = key.trim();\n          // Whether treat child as root. In most case it is false.\n          var nextRoot = false;\n\n          // 拆分多个选择器\n          if ((root || injectHash) && hashId) {\n            if (mergedKey.startsWith('@')) {\n              // 略过媒体查询，交给子节点继续插入 hashId\n              subInjectHash = true;\n            } else if (mergedKey === '&') {\n              // 抹掉 root selector 上的单个 &\n              mergedKey = injectSelectorHash('', hashId, hashPriority);\n            } else {\n              // 注入 hashId\n              mergedKey = injectSelectorHash(key, hashId, hashPriority);\n            }\n          } else if (root && !hashId && (mergedKey === '&' || mergedKey === '')) {\n            // In case of `{ '&': { a: { color: 'red' } } }` or `{ '': { a: { color: 'red' } } }` without hashId,\n            // we will get `&{a:{color:red;}}` or `{a:{color:red;}}` string for stylis to compile.\n            // But it does not conform to stylis syntax,\n            // and finally we will get `{color:red;}` as css, which is wrong.\n            // So we need to remove key in root, and treat child `{ a: { color: 'red' } }` as root.\n            mergedKey = '';\n            nextRoot = true;\n          }\n          var _parseStyle3 = parseStyle(value, config, {\n              root: nextRoot,\n              injectHash: subInjectHash,\n              parentSelectors: [].concat(_toConsumableArray(parentSelectors), [mergedKey])\n            }),\n            _parseStyle4 = _slicedToArray(_parseStyle3, 2),\n            _parsedStr2 = _parseStyle4[0],\n            childEffectStyle = _parseStyle4[1];\n          effectStyle = _objectSpread(_objectSpread({}, effectStyle), childEffectStyle);\n          styleStr += \"\".concat(mergedKey).concat(_parsedStr2);\n        } else {\n          var _value;\n          function appendStyle(cssKey, cssValue) {\n            if (process.env.NODE_ENV !== 'production' && (_typeof(value) !== 'object' || !(value !== null && value !== void 0 && value[SKIP_CHECK]))) {\n              [contentQuotesLinter, hashedAnimationLinter].concat(_toConsumableArray(linters)).forEach(function (linter) {\n                return linter(cssKey, cssValue, {\n                  path: path,\n                  hashId: hashId,\n                  parentSelectors: parentSelectors\n                });\n              });\n            }\n\n            // 如果是样式则直接插入\n            var styleName = cssKey.replace(/[A-Z]/g, function (match) {\n              return \"-\".concat(match.toLowerCase());\n            });\n\n            // Auto suffix with px\n            var formatValue = cssValue;\n            if (!unitless[cssKey] && typeof formatValue === 'number' && formatValue !== 0) {\n              formatValue = \"\".concat(formatValue, \"px\");\n            }\n\n            // handle animationName & Keyframe value\n            if (cssKey === 'animationName' && cssValue !== null && cssValue !== void 0 && cssValue._keyframe) {\n              parseKeyframes(cssValue);\n              formatValue = cssValue.getName(hashId);\n            }\n            styleStr += \"\".concat(styleName, \":\").concat(formatValue, \";\");\n          }\n          var actualValue = (_value = value === null || value === void 0 ? void 0 : value.value) !== null && _value !== void 0 ? _value : value;\n          if (_typeof(value) === 'object' && value !== null && value !== void 0 && value[MULTI_VALUE] && Array.isArray(actualValue)) {\n            actualValue.forEach(function (item) {\n              appendStyle(key, item);\n            });\n          } else {\n            appendStyle(key, actualValue);\n          }\n        }\n      });\n    }\n  });\n  if (!root) {\n    styleStr = \"{\".concat(styleStr, \"}\");\n  } else if (layer) {\n    // fixme: https://github.com/thysultan/stylis/pull/339\n    if (styleStr) {\n      styleStr = \"@layer \".concat(layer.name, \" {\").concat(styleStr, \"}\");\n    }\n    if (layer.dependencies) {\n      effectStyle[\"@layer \".concat(layer.name)] = layer.dependencies.map(function (deps) {\n        return \"@layer \".concat(deps, \", \").concat(layer.name, \";\");\n      }).join('\\n');\n    }\n  }\n  return [styleStr, effectStyle];\n};\n\n// ============================================================================\n// ==                                Register                                ==\n// ============================================================================\nexport function uniqueHash(path, styleStr) {\n  return hash(\"\".concat(path.join('%')).concat(styleStr));\n}\nfunction Empty() {\n  return null;\n}\nexport var STYLE_PREFIX = 'style';\n/**\n * Register a style to the global style sheet.\n */\nexport default function useStyleRegister(info, styleFn) {\n  var token = info.token,\n    path = info.path,\n    hashId = info.hashId,\n    layer = info.layer,\n    nonce = info.nonce,\n    clientOnly = info.clientOnly,\n    _info$order = info.order,\n    order = _info$order === void 0 ? 0 : _info$order;\n  var _React$useContext = React.useContext(StyleContext),\n    autoClear = _React$useContext.autoClear,\n    mock = _React$useContext.mock,\n    defaultCache = _React$useContext.defaultCache,\n    hashPriority = _React$useContext.hashPriority,\n    container = _React$useContext.container,\n    ssrInline = _React$useContext.ssrInline,\n    transformers = _React$useContext.transformers,\n    linters = _React$useContext.linters,\n    cache = _React$useContext.cache,\n    enableLayer = _React$useContext.layer;\n  var tokenKey = token._tokenKey;\n  var fullPath = [tokenKey];\n  if (enableLayer) {\n    fullPath.push('layer');\n  }\n  fullPath.push.apply(fullPath, _toConsumableArray(path));\n\n  // Check if need insert style\n  var isMergedClientSide = isClientSide;\n  if (process.env.NODE_ENV !== 'production' && mock !== undefined) {\n    isMergedClientSide = mock === 'client';\n  }\n  var _useGlobalCache = useGlobalCache(STYLE_PREFIX, fullPath,\n    // Create cache if needed\n    function () {\n      var cachePath = fullPath.join('|');\n\n      // Get style from SSR inline style directly\n      if (existPath(cachePath)) {\n        var _getStyleAndHash = getStyleAndHash(cachePath),\n          _getStyleAndHash2 = _slicedToArray(_getStyleAndHash, 2),\n          inlineCacheStyleStr = _getStyleAndHash2[0],\n          styleHash = _getStyleAndHash2[1];\n        if (inlineCacheStyleStr) {\n          return [inlineCacheStyleStr, tokenKey, styleHash, {}, clientOnly, order];\n        }\n      }\n\n      // Generate style\n      var styleObj = styleFn();\n      var _parseStyle5 = parseStyle(styleObj, {\n          hashId: hashId,\n          hashPriority: hashPriority,\n          layer: enableLayer ? layer : undefined,\n          path: path.join('-'),\n          transformers: transformers,\n          linters: linters\n        }),\n        _parseStyle6 = _slicedToArray(_parseStyle5, 2),\n        parsedStyle = _parseStyle6[0],\n        effectStyle = _parseStyle6[1];\n      var styleStr = normalizeStyle(parsedStyle);\n      var styleId = uniqueHash(fullPath, styleStr);\n      return [styleStr, tokenKey, styleId, effectStyle, clientOnly, order];\n    },\n    // Remove cache if no need\n    function (_ref2, fromHMR) {\n      var _ref3 = _slicedToArray(_ref2, 3),\n        styleId = _ref3[2];\n      if ((fromHMR || autoClear) && isClientSide) {\n        removeCSS(styleId, {\n          mark: ATTR_MARK\n        });\n      }\n    },\n    // Effect: Inject style here\n    function (_ref4) {\n      var _ref5 = _slicedToArray(_ref4, 4),\n        styleStr = _ref5[0],\n        _ = _ref5[1],\n        styleId = _ref5[2],\n        effectStyle = _ref5[3];\n      if (isMergedClientSide && styleStr !== CSS_FILE_STYLE) {\n        var mergedCSSConfig = {\n          mark: ATTR_MARK,\n          prepend: enableLayer ? false : 'queue',\n          attachTo: container,\n          priority: order\n        };\n        var nonceStr = typeof nonce === 'function' ? nonce() : nonce;\n        if (nonceStr) {\n          mergedCSSConfig.csp = {\n            nonce: nonceStr\n          };\n        }\n\n        // ================= Split Effect Style =================\n        // We will split effectStyle here since @layer should be at the top level\n        var effectLayerKeys = [];\n        var effectRestKeys = [];\n        Object.keys(effectStyle).forEach(function (key) {\n          if (key.startsWith('@layer')) {\n            effectLayerKeys.push(key);\n          } else {\n            effectRestKeys.push(key);\n          }\n        });\n\n        // ================= Inject Layer Style =================\n        // Inject layer style\n        effectLayerKeys.forEach(function (effectKey) {\n          updateCSS(normalizeStyle(effectStyle[effectKey]), \"_layer-\".concat(effectKey), _objectSpread(_objectSpread({}, mergedCSSConfig), {}, {\n            prepend: true\n          }));\n        });\n\n        // ==================== Inject Style ====================\n        // Inject style\n        var style = updateCSS(styleStr, styleId, mergedCSSConfig);\n        style[CSS_IN_JS_INSTANCE] = cache.instanceId;\n\n        // Used for `useCacheToken` to remove on batch when token removed\n        style.setAttribute(ATTR_TOKEN, tokenKey);\n\n        // Debug usage. Dev only\n        if (process.env.NODE_ENV !== 'production') {\n          style.setAttribute(ATTR_CACHE_PATH, fullPath.join('|'));\n        }\n\n        // ================ Inject Effect Style =================\n        // Inject client side effect style\n        effectRestKeys.forEach(function (effectKey) {\n          updateCSS(normalizeStyle(effectStyle[effectKey]), \"_effect-\".concat(effectKey), mergedCSSConfig);\n        });\n      }\n    }),\n    _useGlobalCache2 = _slicedToArray(_useGlobalCache, 3),\n    cachedStyleStr = _useGlobalCache2[0],\n    cachedTokenKey = _useGlobalCache2[1],\n    cachedStyleId = _useGlobalCache2[2];\n  return function (node) {\n    var styleNode;\n    if (!ssrInline || isMergedClientSide || !defaultCache) {\n      styleNode = /*#__PURE__*/React.createElement(Empty, null);\n    } else {\n      var _ref6;\n      styleNode = /*#__PURE__*/React.createElement(\"style\", _extends({}, (_ref6 = {}, _defineProperty(_ref6, ATTR_TOKEN, cachedTokenKey), _defineProperty(_ref6, ATTR_MARK, cachedStyleId), _ref6), {\n        dangerouslySetInnerHTML: {\n          __html: cachedStyleStr\n        }\n      }));\n    }\n    return /*#__PURE__*/React.createElement(React.Fragment, null, styleNode, node);\n  };\n}\nexport var extract = function extract(cache, effectStyles, options) {\n  var _cache = _slicedToArray(cache, 6),\n    styleStr = _cache[0],\n    tokenKey = _cache[1],\n    styleId = _cache[2],\n    effectStyle = _cache[3],\n    clientOnly = _cache[4],\n    order = _cache[5];\n  var _ref7 = options || {},\n    plain = _ref7.plain;\n\n  // Skip client only style\n  if (clientOnly) {\n    return null;\n  }\n  var keyStyleText = styleStr;\n\n  // ====================== Share ======================\n  // Used for rc-util\n  var sharedAttrs = {\n    'data-rc-order': 'prependQueue',\n    'data-rc-priority': \"\".concat(order)\n  };\n\n  // ====================== Style ======================\n  keyStyleText = toStyleStr(styleStr, tokenKey, styleId, sharedAttrs, plain);\n\n  // =============== Create effect style ===============\n  if (effectStyle) {\n    Object.keys(effectStyle).forEach(function (effectKey) {\n      // Effect style can be reused\n      if (!effectStyles[effectKey]) {\n        effectStyles[effectKey] = true;\n        var effectStyleStr = normalizeStyle(effectStyle[effectKey]);\n        var effectStyleHTML = toStyleStr(effectStyleStr, tokenKey, \"_effect-\".concat(effectKey), sharedAttrs, plain);\n        if (effectKey.startsWith('@layer')) {\n          keyStyleText = effectStyleHTML + keyStyleText;\n        } else {\n          keyStyleText += effectStyleHTML;\n        }\n      }\n    });\n  }\n  return [order, styleId, keyStyleText];\n};", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_objectSpread", "_slicedToArray", "_toConsumableArray", "_typeof", "hash", "removeCSS", "updateCSS", "React", "unitless", "compile", "serialize", "stringify", "contentQuotesLinter", "hashedAnimationLinter", "StyleContext", "ATTR_CACHE_PATH", "ATTR_MARK", "ATTR_TOKEN", "CSS_IN_JS_INSTANCE", "isClientSide", "toStyleStr", "CSS_FILE_STYLE", "existPath", "getStyleAndHash", "useGlobalCache", "SKIP_CHECK", "MULTI_VALUE", "normalizeStyle", "styleStr", "serialized", "replace", "isCompoundCSSProperty", "value", "injectSelectorHash", "key", "hashId", "hashPriority", "hashClassName", "concat", "hashSelector", "keys", "split", "map", "k", "_firstPath$match", "fullPath", "trim", "firstPath", "htmlElement", "match", "slice", "length", "join", "parseStyle", "interpolation", "config", "arguments", "undefined", "_ref", "root", "parentSelectors", "injectHash", "layer", "path", "_config$transformers", "transformers", "_config$linters", "linters", "effectStyle", "parseKeyframes", "keyframes", "animationName", "getName", "_parseStyle", "style", "_parseStyle2", "_parsedStr", "flattenList", "list", "fullList", "for<PERSON>ach", "item", "Array", "isArray", "push", "flattenStyleList", "originStyle", "_keyframe", "mergedStyle", "reduce", "prev", "trans", "_trans$visit", "visit", "call", "Object", "subInjectHash", "mergedKey", "nextRoot", "startsWith", "_parseStyle3", "_parseStyle4", "_parsedStr2", "childEffectStyle", "_value", "appendStyle", "cssKey", "cssValue", "process", "env", "NODE_ENV", "linter", "styleName", "toLowerCase", "formatValue", "actualValue", "name", "dependencies", "deps", "uniqueHash", "Empty", "STYLE_PREFIX", "useStyleRegister", "info", "styleFn", "token", "nonce", "clientOnly", "_info$order", "order", "_React$useContext", "useContext", "autoClear", "mock", "defaultCache", "container", "ssrInline", "cache", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "_token<PERSON>ey", "apply", "isMergedClientSide", "_useGlobalCache", "cachePath", "_getStyleAndHash", "_getStyleAndHash2", "inlineCacheStyleStr", "styleHash", "styleObj", "_parseStyle5", "_parseStyle6", "parsedStyle", "styleId", "_ref2", "fromHMR", "_ref3", "mark", "_ref4", "_ref5", "_", "mergedCSSConfig", "prepend", "attachTo", "priority", "nonceStr", "csp", "effect<PERSON><PERSON>er<PERSON><PERSON>s", "effectRestKeys", "<PERSON><PERSON><PERSON>", "instanceId", "setAttribute", "_useGlobalCache2", "cachedStyleStr", "cachedTokenKey", "cachedStyleId", "node", "styleNode", "createElement", "_ref6", "dangerouslySetInnerHTML", "__html", "Fragment", "extract", "effectStyles", "options", "_cache", "_ref7", "plain", "keyStyleText", "sharedAttrs", "effectStyleStr", "effectStyleHTML"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@ant-design/cssinjs/es/hooks/useStyleRegister.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport hash from '@emotion/hash';\nimport { removeCSS, updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport * as React from 'react';\n// @ts-ignore\nimport unitless from '@emotion/unitless';\nimport { compile, serialize, stringify } from 'stylis';\nimport { contentQuotesLinter, hashedAnimationLinter } from \"../linters\";\nimport StyleContext, { ATTR_CACHE_PATH, ATTR_MARK, ATTR_TOKEN, CSS_IN_JS_INSTANCE } from \"../StyleContext\";\nimport { isClientSide, toStyleStr } from \"../util\";\nimport { CSS_FILE_STYLE, existPath, getStyleAndHash } from \"../util/cacheMapUtil\";\nimport useGlobalCache from \"./useGlobalCache\";\nvar SKIP_CHECK = '_skip_check_';\nvar MULTI_VALUE = '_multi_value_';\n// ============================================================================\n// ==                                 Parser                                 ==\n// ============================================================================\n// Preprocessor style content to browser support one\nexport function normalizeStyle(styleStr) {\n  var serialized = serialize(compile(styleStr), stringify);\n  return serialized.replace(/\\{%%%\\:[^;];}/g, ';');\n}\nfunction isCompoundCSSProperty(value) {\n  return _typeof(value) === 'object' && value && (SKIP_CHECK in value || MULTI_VALUE in value);\n}\n\n// 注入 hash 值\nfunction injectSelectorHash(key, hashId, hashPriority) {\n  if (!hashId) {\n    return key;\n  }\n  var hashClassName = \".\".concat(hashId);\n  var hashSelector = hashPriority === 'low' ? \":where(\".concat(hashClassName, \")\") : hashClassName;\n\n  // 注入 hashId\n  var keys = key.split(',').map(function (k) {\n    var _firstPath$match;\n    var fullPath = k.trim().split(/\\s+/);\n\n    // 如果 Selector 第一个是 HTML Element，那我们就插到它的后面。反之，就插到最前面。\n    var firstPath = fullPath[0] || '';\n    var htmlElement = ((_firstPath$match = firstPath.match(/^\\w+/)) === null || _firstPath$match === void 0 ? void 0 : _firstPath$match[0]) || '';\n    firstPath = \"\".concat(htmlElement).concat(hashSelector).concat(firstPath.slice(htmlElement.length));\n    return [firstPath].concat(_toConsumableArray(fullPath.slice(1))).join(' ');\n  });\n  return keys.join(',');\n}\n// Parse CSSObject to style content\nexport var parseStyle = function parseStyle(interpolation) {\n  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n      root: true,\n      parentSelectors: []\n    },\n    root = _ref.root,\n    injectHash = _ref.injectHash,\n    parentSelectors = _ref.parentSelectors;\n  var hashId = config.hashId,\n    layer = config.layer,\n    path = config.path,\n    hashPriority = config.hashPriority,\n    _config$transformers = config.transformers,\n    transformers = _config$transformers === void 0 ? [] : _config$transformers,\n    _config$linters = config.linters,\n    linters = _config$linters === void 0 ? [] : _config$linters;\n  var styleStr = '';\n  var effectStyle = {};\n  function parseKeyframes(keyframes) {\n    var animationName = keyframes.getName(hashId);\n    if (!effectStyle[animationName]) {\n      var _parseStyle = parseStyle(keyframes.style, config, {\n          root: false,\n          parentSelectors: parentSelectors\n        }),\n        _parseStyle2 = _slicedToArray(_parseStyle, 1),\n        _parsedStr = _parseStyle2[0];\n      effectStyle[animationName] = \"@keyframes \".concat(keyframes.getName(hashId)).concat(_parsedStr);\n    }\n  }\n  function flattenList(list) {\n    var fullList = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    list.forEach(function (item) {\n      if (Array.isArray(item)) {\n        flattenList(item, fullList);\n      } else if (item) {\n        fullList.push(item);\n      }\n    });\n    return fullList;\n  }\n  var flattenStyleList = flattenList(Array.isArray(interpolation) ? interpolation : [interpolation]);\n  flattenStyleList.forEach(function (originStyle) {\n    // Only root level can use raw string\n    var style = typeof originStyle === 'string' && !root ? {} : originStyle;\n    if (typeof style === 'string') {\n      styleStr += \"\".concat(style, \"\\n\");\n    } else if (style._keyframe) {\n      // Keyframe\n      parseKeyframes(style);\n    } else {\n      var mergedStyle = transformers.reduce(function (prev, trans) {\n        var _trans$visit;\n        return (trans === null || trans === void 0 || (_trans$visit = trans.visit) === null || _trans$visit === void 0 ? void 0 : _trans$visit.call(trans, prev)) || prev;\n      }, style);\n\n      // Normal CSSObject\n      Object.keys(mergedStyle).forEach(function (key) {\n        var value = mergedStyle[key];\n        if (_typeof(value) === 'object' && value && (key !== 'animationName' || !value._keyframe) && !isCompoundCSSProperty(value)) {\n          var subInjectHash = false;\n\n          // 当成嵌套对象来处理\n          var mergedKey = key.trim();\n          // Whether treat child as root. In most case it is false.\n          var nextRoot = false;\n\n          // 拆分多个选择器\n          if ((root || injectHash) && hashId) {\n            if (mergedKey.startsWith('@')) {\n              // 略过媒体查询，交给子节点继续插入 hashId\n              subInjectHash = true;\n            } else if (mergedKey === '&') {\n              // 抹掉 root selector 上的单个 &\n              mergedKey = injectSelectorHash('', hashId, hashPriority);\n            } else {\n              // 注入 hashId\n              mergedKey = injectSelectorHash(key, hashId, hashPriority);\n            }\n          } else if (root && !hashId && (mergedKey === '&' || mergedKey === '')) {\n            // In case of `{ '&': { a: { color: 'red' } } }` or `{ '': { a: { color: 'red' } } }` without hashId,\n            // we will get `&{a:{color:red;}}` or `{a:{color:red;}}` string for stylis to compile.\n            // But it does not conform to stylis syntax,\n            // and finally we will get `{color:red;}` as css, which is wrong.\n            // So we need to remove key in root, and treat child `{ a: { color: 'red' } }` as root.\n            mergedKey = '';\n            nextRoot = true;\n          }\n          var _parseStyle3 = parseStyle(value, config, {\n              root: nextRoot,\n              injectHash: subInjectHash,\n              parentSelectors: [].concat(_toConsumableArray(parentSelectors), [mergedKey])\n            }),\n            _parseStyle4 = _slicedToArray(_parseStyle3, 2),\n            _parsedStr2 = _parseStyle4[0],\n            childEffectStyle = _parseStyle4[1];\n          effectStyle = _objectSpread(_objectSpread({}, effectStyle), childEffectStyle);\n          styleStr += \"\".concat(mergedKey).concat(_parsedStr2);\n        } else {\n          var _value;\n          function appendStyle(cssKey, cssValue) {\n            if (process.env.NODE_ENV !== 'production' && (_typeof(value) !== 'object' || !(value !== null && value !== void 0 && value[SKIP_CHECK]))) {\n              [contentQuotesLinter, hashedAnimationLinter].concat(_toConsumableArray(linters)).forEach(function (linter) {\n                return linter(cssKey, cssValue, {\n                  path: path,\n                  hashId: hashId,\n                  parentSelectors: parentSelectors\n                });\n              });\n            }\n\n            // 如果是样式则直接插入\n            var styleName = cssKey.replace(/[A-Z]/g, function (match) {\n              return \"-\".concat(match.toLowerCase());\n            });\n\n            // Auto suffix with px\n            var formatValue = cssValue;\n            if (!unitless[cssKey] && typeof formatValue === 'number' && formatValue !== 0) {\n              formatValue = \"\".concat(formatValue, \"px\");\n            }\n\n            // handle animationName & Keyframe value\n            if (cssKey === 'animationName' && cssValue !== null && cssValue !== void 0 && cssValue._keyframe) {\n              parseKeyframes(cssValue);\n              formatValue = cssValue.getName(hashId);\n            }\n            styleStr += \"\".concat(styleName, \":\").concat(formatValue, \";\");\n          }\n          var actualValue = (_value = value === null || value === void 0 ? void 0 : value.value) !== null && _value !== void 0 ? _value : value;\n          if (_typeof(value) === 'object' && value !== null && value !== void 0 && value[MULTI_VALUE] && Array.isArray(actualValue)) {\n            actualValue.forEach(function (item) {\n              appendStyle(key, item);\n            });\n          } else {\n            appendStyle(key, actualValue);\n          }\n        }\n      });\n    }\n  });\n  if (!root) {\n    styleStr = \"{\".concat(styleStr, \"}\");\n  } else if (layer) {\n    // fixme: https://github.com/thysultan/stylis/pull/339\n    if (styleStr) {\n      styleStr = \"@layer \".concat(layer.name, \" {\").concat(styleStr, \"}\");\n    }\n    if (layer.dependencies) {\n      effectStyle[\"@layer \".concat(layer.name)] = layer.dependencies.map(function (deps) {\n        return \"@layer \".concat(deps, \", \").concat(layer.name, \";\");\n      }).join('\\n');\n    }\n  }\n  return [styleStr, effectStyle];\n};\n\n// ============================================================================\n// ==                                Register                                ==\n// ============================================================================\nexport function uniqueHash(path, styleStr) {\n  return hash(\"\".concat(path.join('%')).concat(styleStr));\n}\nfunction Empty() {\n  return null;\n}\nexport var STYLE_PREFIX = 'style';\n/**\n * Register a style to the global style sheet.\n */\nexport default function useStyleRegister(info, styleFn) {\n  var token = info.token,\n    path = info.path,\n    hashId = info.hashId,\n    layer = info.layer,\n    nonce = info.nonce,\n    clientOnly = info.clientOnly,\n    _info$order = info.order,\n    order = _info$order === void 0 ? 0 : _info$order;\n  var _React$useContext = React.useContext(StyleContext),\n    autoClear = _React$useContext.autoClear,\n    mock = _React$useContext.mock,\n    defaultCache = _React$useContext.defaultCache,\n    hashPriority = _React$useContext.hashPriority,\n    container = _React$useContext.container,\n    ssrInline = _React$useContext.ssrInline,\n    transformers = _React$useContext.transformers,\n    linters = _React$useContext.linters,\n    cache = _React$useContext.cache,\n    enableLayer = _React$useContext.layer;\n  var tokenKey = token._tokenKey;\n  var fullPath = [tokenKey];\n  if (enableLayer) {\n    fullPath.push('layer');\n  }\n  fullPath.push.apply(fullPath, _toConsumableArray(path));\n\n  // Check if need insert style\n  var isMergedClientSide = isClientSide;\n  if (process.env.NODE_ENV !== 'production' && mock !== undefined) {\n    isMergedClientSide = mock === 'client';\n  }\n  var _useGlobalCache = useGlobalCache(STYLE_PREFIX, fullPath,\n    // Create cache if needed\n    function () {\n      var cachePath = fullPath.join('|');\n\n      // Get style from SSR inline style directly\n      if (existPath(cachePath)) {\n        var _getStyleAndHash = getStyleAndHash(cachePath),\n          _getStyleAndHash2 = _slicedToArray(_getStyleAndHash, 2),\n          inlineCacheStyleStr = _getStyleAndHash2[0],\n          styleHash = _getStyleAndHash2[1];\n        if (inlineCacheStyleStr) {\n          return [inlineCacheStyleStr, tokenKey, styleHash, {}, clientOnly, order];\n        }\n      }\n\n      // Generate style\n      var styleObj = styleFn();\n      var _parseStyle5 = parseStyle(styleObj, {\n          hashId: hashId,\n          hashPriority: hashPriority,\n          layer: enableLayer ? layer : undefined,\n          path: path.join('-'),\n          transformers: transformers,\n          linters: linters\n        }),\n        _parseStyle6 = _slicedToArray(_parseStyle5, 2),\n        parsedStyle = _parseStyle6[0],\n        effectStyle = _parseStyle6[1];\n      var styleStr = normalizeStyle(parsedStyle);\n      var styleId = uniqueHash(fullPath, styleStr);\n      return [styleStr, tokenKey, styleId, effectStyle, clientOnly, order];\n    },\n    // Remove cache if no need\n    function (_ref2, fromHMR) {\n      var _ref3 = _slicedToArray(_ref2, 3),\n        styleId = _ref3[2];\n      if ((fromHMR || autoClear) && isClientSide) {\n        removeCSS(styleId, {\n          mark: ATTR_MARK\n        });\n      }\n    },\n    // Effect: Inject style here\n    function (_ref4) {\n      var _ref5 = _slicedToArray(_ref4, 4),\n        styleStr = _ref5[0],\n        _ = _ref5[1],\n        styleId = _ref5[2],\n        effectStyle = _ref5[3];\n      if (isMergedClientSide && styleStr !== CSS_FILE_STYLE) {\n        var mergedCSSConfig = {\n          mark: ATTR_MARK,\n          prepend: enableLayer ? false : 'queue',\n          attachTo: container,\n          priority: order\n        };\n        var nonceStr = typeof nonce === 'function' ? nonce() : nonce;\n        if (nonceStr) {\n          mergedCSSConfig.csp = {\n            nonce: nonceStr\n          };\n        }\n\n        // ================= Split Effect Style =================\n        // We will split effectStyle here since @layer should be at the top level\n        var effectLayerKeys = [];\n        var effectRestKeys = [];\n        Object.keys(effectStyle).forEach(function (key) {\n          if (key.startsWith('@layer')) {\n            effectLayerKeys.push(key);\n          } else {\n            effectRestKeys.push(key);\n          }\n        });\n\n        // ================= Inject Layer Style =================\n        // Inject layer style\n        effectLayerKeys.forEach(function (effectKey) {\n          updateCSS(normalizeStyle(effectStyle[effectKey]), \"_layer-\".concat(effectKey), _objectSpread(_objectSpread({}, mergedCSSConfig), {}, {\n            prepend: true\n          }));\n        });\n\n        // ==================== Inject Style ====================\n        // Inject style\n        var style = updateCSS(styleStr, styleId, mergedCSSConfig);\n        style[CSS_IN_JS_INSTANCE] = cache.instanceId;\n\n        // Used for `useCacheToken` to remove on batch when token removed\n        style.setAttribute(ATTR_TOKEN, tokenKey);\n\n        // Debug usage. Dev only\n        if (process.env.NODE_ENV !== 'production') {\n          style.setAttribute(ATTR_CACHE_PATH, fullPath.join('|'));\n        }\n\n        // ================ Inject Effect Style =================\n        // Inject client side effect style\n        effectRestKeys.forEach(function (effectKey) {\n          updateCSS(normalizeStyle(effectStyle[effectKey]), \"_effect-\".concat(effectKey), mergedCSSConfig);\n        });\n      }\n    }),\n    _useGlobalCache2 = _slicedToArray(_useGlobalCache, 3),\n    cachedStyleStr = _useGlobalCache2[0],\n    cachedTokenKey = _useGlobalCache2[1],\n    cachedStyleId = _useGlobalCache2[2];\n  return function (node) {\n    var styleNode;\n    if (!ssrInline || isMergedClientSide || !defaultCache) {\n      styleNode = /*#__PURE__*/React.createElement(Empty, null);\n    } else {\n      var _ref6;\n      styleNode = /*#__PURE__*/React.createElement(\"style\", _extends({}, (_ref6 = {}, _defineProperty(_ref6, ATTR_TOKEN, cachedTokenKey), _defineProperty(_ref6, ATTR_MARK, cachedStyleId), _ref6), {\n        dangerouslySetInnerHTML: {\n          __html: cachedStyleStr\n        }\n      }));\n    }\n    return /*#__PURE__*/React.createElement(React.Fragment, null, styleNode, node);\n  };\n}\nexport var extract = function extract(cache, effectStyles, options) {\n  var _cache = _slicedToArray(cache, 6),\n    styleStr = _cache[0],\n    tokenKey = _cache[1],\n    styleId = _cache[2],\n    effectStyle = _cache[3],\n    clientOnly = _cache[4],\n    order = _cache[5];\n  var _ref7 = options || {},\n    plain = _ref7.plain;\n\n  // Skip client only style\n  if (clientOnly) {\n    return null;\n  }\n  var keyStyleText = styleStr;\n\n  // ====================== Share ======================\n  // Used for rc-util\n  var sharedAttrs = {\n    'data-rc-order': 'prependQueue',\n    'data-rc-priority': \"\".concat(order)\n  };\n\n  // ====================== Style ======================\n  keyStyleText = toStyleStr(styleStr, tokenKey, styleId, sharedAttrs, plain);\n\n  // =============== Create effect style ===============\n  if (effectStyle) {\n    Object.keys(effectStyle).forEach(function (effectKey) {\n      // Effect style can be reused\n      if (!effectStyles[effectKey]) {\n        effectStyles[effectKey] = true;\n        var effectStyleStr = normalizeStyle(effectStyle[effectKey]);\n        var effectStyleHTML = toStyleStr(effectStyleStr, tokenKey, \"_effect-\".concat(effectKey), sharedAttrs, plain);\n        if (effectKey.startsWith('@layer')) {\n          keyStyleText = effectStyleHTML + keyStyleText;\n        } else {\n          keyStyleText += effectStyleHTML;\n        }\n      }\n    });\n  }\n  return [order, styleId, keyStyleText];\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,IAAI,MAAM,eAAe;AAChC,SAASC,SAAS,EAAEC,SAAS,QAAQ,2BAA2B;AAChE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B;AACA,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,SAASC,OAAO,EAAEC,SAAS,EAAEC,SAAS,QAAQ,QAAQ;AACtD,SAASC,mBAAmB,EAAEC,qBAAqB,QAAQ,YAAY;AACvE,OAAOC,YAAY,IAAIC,eAAe,EAAEC,SAAS,EAAEC,UAAU,EAAEC,kBAAkB,QAAQ,iBAAiB;AAC1G,SAASC,YAAY,EAAEC,UAAU,QAAQ,SAAS;AAClD,SAASC,cAAc,EAAEC,SAAS,EAAEC,eAAe,QAAQ,sBAAsB;AACjF,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,IAAIC,UAAU,GAAG,cAAc;AAC/B,IAAIC,WAAW,GAAG,eAAe;AACjC;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,QAAQ,EAAE;EACvC,IAAIC,UAAU,GAAGnB,SAAS,CAACD,OAAO,CAACmB,QAAQ,CAAC,EAAEjB,SAAS,CAAC;EACxD,OAAOkB,UAAU,CAACC,OAAO,CAAC,gBAAgB,EAAE,GAAG,CAAC;AAClD;AACA,SAASC,qBAAqBA,CAACC,KAAK,EAAE;EACpC,OAAO7B,OAAO,CAAC6B,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAKP,UAAU,IAAIO,KAAK,IAAIN,WAAW,IAAIM,KAAK,CAAC;AAC9F;;AAEA;AACA,SAASC,kBAAkBA,CAACC,GAAG,EAAEC,MAAM,EAAEC,YAAY,EAAE;EACrD,IAAI,CAACD,MAAM,EAAE;IACX,OAAOD,GAAG;EACZ;EACA,IAAIG,aAAa,GAAG,GAAG,CAACC,MAAM,CAACH,MAAM,CAAC;EACtC,IAAII,YAAY,GAAGH,YAAY,KAAK,KAAK,GAAG,SAAS,CAACE,MAAM,CAACD,aAAa,EAAE,GAAG,CAAC,GAAGA,aAAa;;EAEhG;EACA,IAAIG,IAAI,GAAGN,GAAG,CAACO,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,UAAUC,CAAC,EAAE;IACzC,IAAIC,gBAAgB;IACpB,IAAIC,QAAQ,GAAGF,CAAC,CAACG,IAAI,CAAC,CAAC,CAACL,KAAK,CAAC,KAAK,CAAC;;IAEpC;IACA,IAAIM,SAAS,GAAGF,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE;IACjC,IAAIG,WAAW,GAAG,CAAC,CAACJ,gBAAgB,GAAGG,SAAS,CAACE,KAAK,CAAC,MAAM,CAAC,MAAM,IAAI,IAAIL,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAAC,CAAC,CAAC,KAAK,EAAE;IAC7IG,SAAS,GAAG,EAAE,CAACT,MAAM,CAACU,WAAW,CAAC,CAACV,MAAM,CAACC,YAAY,CAAC,CAACD,MAAM,CAACS,SAAS,CAACG,KAAK,CAACF,WAAW,CAACG,MAAM,CAAC,CAAC;IACnG,OAAO,CAACJ,SAAS,CAAC,CAACT,MAAM,CAACpC,kBAAkB,CAAC2C,QAAQ,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;EAC5E,CAAC,CAAC;EACF,OAAOZ,IAAI,CAACY,IAAI,CAAC,GAAG,CAAC;AACvB;AACA;AACA,OAAO,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,aAAa,EAAE;EACzD,IAAIC,MAAM,GAAGC,SAAS,CAACL,MAAM,GAAG,CAAC,IAAIK,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAIE,IAAI,GAAGF,SAAS,CAACL,MAAM,GAAG,CAAC,IAAIK,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG;MAC3EG,IAAI,EAAE,IAAI;MACVC,eAAe,EAAE;IACnB,CAAC;IACDD,IAAI,GAAGD,IAAI,CAACC,IAAI;IAChBE,UAAU,GAAGH,IAAI,CAACG,UAAU;IAC5BD,eAAe,GAAGF,IAAI,CAACE,eAAe;EACxC,IAAIzB,MAAM,GAAGoB,MAAM,CAACpB,MAAM;IACxB2B,KAAK,GAAGP,MAAM,CAACO,KAAK;IACpBC,IAAI,GAAGR,MAAM,CAACQ,IAAI;IAClB3B,YAAY,GAAGmB,MAAM,CAACnB,YAAY;IAClC4B,oBAAoB,GAAGT,MAAM,CAACU,YAAY;IAC1CA,YAAY,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,oBAAoB;IAC1EE,eAAe,GAAGX,MAAM,CAACY,OAAO;IAChCA,OAAO,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,eAAe;EAC7D,IAAItC,QAAQ,GAAG,EAAE;EACjB,IAAIwC,WAAW,GAAG,CAAC,CAAC;EACpB,SAASC,cAAcA,CAACC,SAAS,EAAE;IACjC,IAAIC,aAAa,GAAGD,SAAS,CAACE,OAAO,CAACrC,MAAM,CAAC;IAC7C,IAAI,CAACiC,WAAW,CAACG,aAAa,CAAC,EAAE;MAC/B,IAAIE,WAAW,GAAGpB,UAAU,CAACiB,SAAS,CAACI,KAAK,EAAEnB,MAAM,EAAE;UAClDI,IAAI,EAAE,KAAK;UACXC,eAAe,EAAEA;QACnB,CAAC,CAAC;QACFe,YAAY,GAAG1E,cAAc,CAACwE,WAAW,EAAE,CAAC,CAAC;QAC7CG,UAAU,GAAGD,YAAY,CAAC,CAAC,CAAC;MAC9BP,WAAW,CAACG,aAAa,CAAC,GAAG,aAAa,CAACjC,MAAM,CAACgC,SAAS,CAACE,OAAO,CAACrC,MAAM,CAAC,CAAC,CAACG,MAAM,CAACsC,UAAU,CAAC;IACjG;EACF;EACA,SAASC,WAAWA,CAACC,IAAI,EAAE;IACzB,IAAIC,QAAQ,GAAGvB,SAAS,CAACL,MAAM,GAAG,CAAC,IAAIK,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;IACrFsB,IAAI,CAACE,OAAO,CAAC,UAAUC,IAAI,EAAE;MAC3B,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;QACvBJ,WAAW,CAACI,IAAI,EAAEF,QAAQ,CAAC;MAC7B,CAAC,MAAM,IAAIE,IAAI,EAAE;QACfF,QAAQ,CAACK,IAAI,CAACH,IAAI,CAAC;MACrB;IACF,CAAC,CAAC;IACF,OAAOF,QAAQ;EACjB;EACA,IAAIM,gBAAgB,GAAGR,WAAW,CAACK,KAAK,CAACC,OAAO,CAAC7B,aAAa,CAAC,GAAGA,aAAa,GAAG,CAACA,aAAa,CAAC,CAAC;EAClG+B,gBAAgB,CAACL,OAAO,CAAC,UAAUM,WAAW,EAAE;IAC9C;IACA,IAAIZ,KAAK,GAAG,OAAOY,WAAW,KAAK,QAAQ,IAAI,CAAC3B,IAAI,GAAG,CAAC,CAAC,GAAG2B,WAAW;IACvE,IAAI,OAAOZ,KAAK,KAAK,QAAQ,EAAE;MAC7B9C,QAAQ,IAAI,EAAE,CAACU,MAAM,CAACoC,KAAK,EAAE,IAAI,CAAC;IACpC,CAAC,MAAM,IAAIA,KAAK,CAACa,SAAS,EAAE;MAC1B;MACAlB,cAAc,CAACK,KAAK,CAAC;IACvB,CAAC,MAAM;MACL,IAAIc,WAAW,GAAGvB,YAAY,CAACwB,MAAM,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;QAC3D,IAAIC,YAAY;QAChB,OAAO,CAACD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,IAAI,CAACC,YAAY,GAAGD,KAAK,CAACE,KAAK,MAAM,IAAI,IAAID,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACE,IAAI,CAACH,KAAK,EAAED,IAAI,CAAC,KAAKA,IAAI;MACnK,CAAC,EAAEhB,KAAK,CAAC;;MAET;MACAqB,MAAM,CAACvD,IAAI,CAACgD,WAAW,CAAC,CAACR,OAAO,CAAC,UAAU9C,GAAG,EAAE;QAC9C,IAAIF,KAAK,GAAGwD,WAAW,CAACtD,GAAG,CAAC;QAC5B,IAAI/B,OAAO,CAAC6B,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAKE,GAAG,KAAK,eAAe,IAAI,CAACF,KAAK,CAACuD,SAAS,CAAC,IAAI,CAACxD,qBAAqB,CAACC,KAAK,CAAC,EAAE;UAC1H,IAAIgE,aAAa,GAAG,KAAK;;UAEzB;UACA,IAAIC,SAAS,GAAG/D,GAAG,CAACY,IAAI,CAAC,CAAC;UAC1B;UACA,IAAIoD,QAAQ,GAAG,KAAK;;UAEpB;UACA,IAAI,CAACvC,IAAI,IAAIE,UAAU,KAAK1B,MAAM,EAAE;YAClC,IAAI8D,SAAS,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;cAC7B;cACAH,aAAa,GAAG,IAAI;YACtB,CAAC,MAAM,IAAIC,SAAS,KAAK,GAAG,EAAE;cAC5B;cACAA,SAAS,GAAGhE,kBAAkB,CAAC,EAAE,EAAEE,MAAM,EAAEC,YAAY,CAAC;YAC1D,CAAC,MAAM;cACL;cACA6D,SAAS,GAAGhE,kBAAkB,CAACC,GAAG,EAAEC,MAAM,EAAEC,YAAY,CAAC;YAC3D;UACF,CAAC,MAAM,IAAIuB,IAAI,IAAI,CAACxB,MAAM,KAAK8D,SAAS,KAAK,GAAG,IAAIA,SAAS,KAAK,EAAE,CAAC,EAAE;YACrE;YACA;YACA;YACA;YACA;YACAA,SAAS,GAAG,EAAE;YACdC,QAAQ,GAAG,IAAI;UACjB;UACA,IAAIE,YAAY,GAAG/C,UAAU,CAACrB,KAAK,EAAEuB,MAAM,EAAE;cACzCI,IAAI,EAAEuC,QAAQ;cACdrC,UAAU,EAAEmC,aAAa;cACzBpC,eAAe,EAAE,EAAE,CAACtB,MAAM,CAACpC,kBAAkB,CAAC0D,eAAe,CAAC,EAAE,CAACqC,SAAS,CAAC;YAC7E,CAAC,CAAC;YACFI,YAAY,GAAGpG,cAAc,CAACmG,YAAY,EAAE,CAAC,CAAC;YAC9CE,WAAW,GAAGD,YAAY,CAAC,CAAC,CAAC;YAC7BE,gBAAgB,GAAGF,YAAY,CAAC,CAAC,CAAC;UACpCjC,WAAW,GAAGpE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoE,WAAW,CAAC,EAAEmC,gBAAgB,CAAC;UAC7E3E,QAAQ,IAAI,EAAE,CAACU,MAAM,CAAC2D,SAAS,CAAC,CAAC3D,MAAM,CAACgE,WAAW,CAAC;QACtD,CAAC,MAAM;UACL,IAAIE,MAAM;UACV,SAASC,WAAWA,CAACC,MAAM,EAAEC,QAAQ,EAAE;YACrC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,KAAK3G,OAAO,CAAC6B,KAAK,CAAC,KAAK,QAAQ,IAAI,EAAEA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,IAAIA,KAAK,CAACP,UAAU,CAAC,CAAC,CAAC,EAAE;cACxI,CAACb,mBAAmB,EAAEC,qBAAqB,CAAC,CAACyB,MAAM,CAACpC,kBAAkB,CAACiE,OAAO,CAAC,CAAC,CAACa,OAAO,CAAC,UAAU+B,MAAM,EAAE;gBACzG,OAAOA,MAAM,CAACL,MAAM,EAAEC,QAAQ,EAAE;kBAC9B5C,IAAI,EAAEA,IAAI;kBACV5B,MAAM,EAAEA,MAAM;kBACdyB,eAAe,EAAEA;gBACnB,CAAC,CAAC;cACJ,CAAC,CAAC;YACJ;;YAEA;YACA,IAAIoD,SAAS,GAAGN,MAAM,CAAC5E,OAAO,CAAC,QAAQ,EAAE,UAAUmB,KAAK,EAAE;cACxD,OAAO,GAAG,CAACX,MAAM,CAACW,KAAK,CAACgE,WAAW,CAAC,CAAC,CAAC;YACxC,CAAC,CAAC;;YAEF;YACA,IAAIC,WAAW,GAAGP,QAAQ;YAC1B,IAAI,CAACnG,QAAQ,CAACkG,MAAM,CAAC,IAAI,OAAOQ,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,CAAC,EAAE;cAC7EA,WAAW,GAAG,EAAE,CAAC5E,MAAM,CAAC4E,WAAW,EAAE,IAAI,CAAC;YAC5C;;YAEA;YACA,IAAIR,MAAM,KAAK,eAAe,IAAIC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAACpB,SAAS,EAAE;cAChGlB,cAAc,CAACsC,QAAQ,CAAC;cACxBO,WAAW,GAAGP,QAAQ,CAACnC,OAAO,CAACrC,MAAM,CAAC;YACxC;YACAP,QAAQ,IAAI,EAAE,CAACU,MAAM,CAAC0E,SAAS,EAAE,GAAG,CAAC,CAAC1E,MAAM,CAAC4E,WAAW,EAAE,GAAG,CAAC;UAChE;UACA,IAAIC,WAAW,GAAG,CAACX,MAAM,GAAGxE,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACA,KAAK,MAAM,IAAI,IAAIwE,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAGxE,KAAK;UACrI,IAAI7B,OAAO,CAAC6B,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,IAAIA,KAAK,CAACN,WAAW,CAAC,IAAIwD,KAAK,CAACC,OAAO,CAACgC,WAAW,CAAC,EAAE;YACzHA,WAAW,CAACnC,OAAO,CAAC,UAAUC,IAAI,EAAE;cAClCwB,WAAW,CAACvE,GAAG,EAAE+C,IAAI,CAAC;YACxB,CAAC,CAAC;UACJ,CAAC,MAAM;YACLwB,WAAW,CAACvE,GAAG,EAAEiF,WAAW,CAAC;UAC/B;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,IAAI,CAACxD,IAAI,EAAE;IACT/B,QAAQ,GAAG,GAAG,CAACU,MAAM,CAACV,QAAQ,EAAE,GAAG,CAAC;EACtC,CAAC,MAAM,IAAIkC,KAAK,EAAE;IAChB;IACA,IAAIlC,QAAQ,EAAE;MACZA,QAAQ,GAAG,SAAS,CAACU,MAAM,CAACwB,KAAK,CAACsD,IAAI,EAAE,IAAI,CAAC,CAAC9E,MAAM,CAACV,QAAQ,EAAE,GAAG,CAAC;IACrE;IACA,IAAIkC,KAAK,CAACuD,YAAY,EAAE;MACtBjD,WAAW,CAAC,SAAS,CAAC9B,MAAM,CAACwB,KAAK,CAACsD,IAAI,CAAC,CAAC,GAAGtD,KAAK,CAACuD,YAAY,CAAC3E,GAAG,CAAC,UAAU4E,IAAI,EAAE;QACjF,OAAO,SAAS,CAAChF,MAAM,CAACgF,IAAI,EAAE,IAAI,CAAC,CAAChF,MAAM,CAACwB,KAAK,CAACsD,IAAI,EAAE,GAAG,CAAC;MAC7D,CAAC,CAAC,CAAChE,IAAI,CAAC,IAAI,CAAC;IACf;EACF;EACA,OAAO,CAACxB,QAAQ,EAAEwC,WAAW,CAAC;AAChC,CAAC;;AAED;AACA;AACA;AACA,OAAO,SAASmD,UAAUA,CAACxD,IAAI,EAAEnC,QAAQ,EAAE;EACzC,OAAOxB,IAAI,CAAC,EAAE,CAACkC,MAAM,CAACyB,IAAI,CAACX,IAAI,CAAC,GAAG,CAAC,CAAC,CAACd,MAAM,CAACV,QAAQ,CAAC,CAAC;AACzD;AACA,SAAS4F,KAAKA,CAAA,EAAG;EACf,OAAO,IAAI;AACb;AACA,OAAO,IAAIC,YAAY,GAAG,OAAO;AACjC;AACA;AACA;AACA,eAAe,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACtD,IAAIC,KAAK,GAAGF,IAAI,CAACE,KAAK;IACpB9D,IAAI,GAAG4D,IAAI,CAAC5D,IAAI;IAChB5B,MAAM,GAAGwF,IAAI,CAACxF,MAAM;IACpB2B,KAAK,GAAG6D,IAAI,CAAC7D,KAAK;IAClBgE,KAAK,GAAGH,IAAI,CAACG,KAAK;IAClBC,UAAU,GAAGJ,IAAI,CAACI,UAAU;IAC5BC,WAAW,GAAGL,IAAI,CAACM,KAAK;IACxBA,KAAK,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,WAAW;EAClD,IAAIE,iBAAiB,GAAG3H,KAAK,CAAC4H,UAAU,CAACrH,YAAY,CAAC;IACpDsH,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,IAAI,GAAGH,iBAAiB,CAACG,IAAI;IAC7BC,YAAY,GAAGJ,iBAAiB,CAACI,YAAY;IAC7ClG,YAAY,GAAG8F,iBAAiB,CAAC9F,YAAY;IAC7CmG,SAAS,GAAGL,iBAAiB,CAACK,SAAS;IACvCC,SAAS,GAAGN,iBAAiB,CAACM,SAAS;IACvCvE,YAAY,GAAGiE,iBAAiB,CAACjE,YAAY;IAC7CE,OAAO,GAAG+D,iBAAiB,CAAC/D,OAAO;IACnCsE,KAAK,GAAGP,iBAAiB,CAACO,KAAK;IAC/BC,WAAW,GAAGR,iBAAiB,CAACpE,KAAK;EACvC,IAAI6E,QAAQ,GAAGd,KAAK,CAACe,SAAS;EAC9B,IAAI/F,QAAQ,GAAG,CAAC8F,QAAQ,CAAC;EACzB,IAAID,WAAW,EAAE;IACf7F,QAAQ,CAACuC,IAAI,CAAC,OAAO,CAAC;EACxB;EACAvC,QAAQ,CAACuC,IAAI,CAACyD,KAAK,CAAChG,QAAQ,EAAE3C,kBAAkB,CAAC6D,IAAI,CAAC,CAAC;;EAEvD;EACA,IAAI+E,kBAAkB,GAAG3H,YAAY;EACrC,IAAIyF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIuB,IAAI,KAAK5E,SAAS,EAAE;IAC/DqF,kBAAkB,GAAGT,IAAI,KAAK,QAAQ;EACxC;EACA,IAAIU,eAAe,GAAGvH,cAAc,CAACiG,YAAY,EAAE5E,QAAQ;IACzD;IACA,YAAY;MACV,IAAImG,SAAS,GAAGnG,QAAQ,CAACO,IAAI,CAAC,GAAG,CAAC;;MAElC;MACA,IAAI9B,SAAS,CAAC0H,SAAS,CAAC,EAAE;QACxB,IAAIC,gBAAgB,GAAG1H,eAAe,CAACyH,SAAS,CAAC;UAC/CE,iBAAiB,GAAGjJ,cAAc,CAACgJ,gBAAgB,EAAE,CAAC,CAAC;UACvDE,mBAAmB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;UAC1CE,SAAS,GAAGF,iBAAiB,CAAC,CAAC,CAAC;QAClC,IAAIC,mBAAmB,EAAE;UACvB,OAAO,CAACA,mBAAmB,EAAER,QAAQ,EAAES,SAAS,EAAE,CAAC,CAAC,EAAErB,UAAU,EAAEE,KAAK,CAAC;QAC1E;MACF;;MAEA;MACA,IAAIoB,QAAQ,GAAGzB,OAAO,CAAC,CAAC;MACxB,IAAI0B,YAAY,GAAGjG,UAAU,CAACgG,QAAQ,EAAE;UACpClH,MAAM,EAAEA,MAAM;UACdC,YAAY,EAAEA,YAAY;UAC1B0B,KAAK,EAAE4E,WAAW,GAAG5E,KAAK,GAAGL,SAAS;UACtCM,IAAI,EAAEA,IAAI,CAACX,IAAI,CAAC,GAAG,CAAC;UACpBa,YAAY,EAAEA,YAAY;UAC1BE,OAAO,EAAEA;QACX,CAAC,CAAC;QACFoF,YAAY,GAAGtJ,cAAc,CAACqJ,YAAY,EAAE,CAAC,CAAC;QAC9CE,WAAW,GAAGD,YAAY,CAAC,CAAC,CAAC;QAC7BnF,WAAW,GAAGmF,YAAY,CAAC,CAAC,CAAC;MAC/B,IAAI3H,QAAQ,GAAGD,cAAc,CAAC6H,WAAW,CAAC;MAC1C,IAAIC,OAAO,GAAGlC,UAAU,CAAC1E,QAAQ,EAAEjB,QAAQ,CAAC;MAC5C,OAAO,CAACA,QAAQ,EAAE+G,QAAQ,EAAEc,OAAO,EAAErF,WAAW,EAAE2D,UAAU,EAAEE,KAAK,CAAC;IACtE,CAAC;IACD;IACA,UAAUyB,KAAK,EAAEC,OAAO,EAAE;MACxB,IAAIC,KAAK,GAAG3J,cAAc,CAACyJ,KAAK,EAAE,CAAC,CAAC;QAClCD,OAAO,GAAGG,KAAK,CAAC,CAAC,CAAC;MACpB,IAAI,CAACD,OAAO,IAAIvB,SAAS,KAAKjH,YAAY,EAAE;QAC1Cd,SAAS,CAACoJ,OAAO,EAAE;UACjBI,IAAI,EAAE7I;QACR,CAAC,CAAC;MACJ;IACF,CAAC;IACD;IACA,UAAU8I,KAAK,EAAE;MACf,IAAIC,KAAK,GAAG9J,cAAc,CAAC6J,KAAK,EAAE,CAAC,CAAC;QAClClI,QAAQ,GAAGmI,KAAK,CAAC,CAAC,CAAC;QACnBC,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC;QACZN,OAAO,GAAGM,KAAK,CAAC,CAAC,CAAC;QAClB3F,WAAW,GAAG2F,KAAK,CAAC,CAAC,CAAC;MACxB,IAAIjB,kBAAkB,IAAIlH,QAAQ,KAAKP,cAAc,EAAE;QACrD,IAAI4I,eAAe,GAAG;UACpBJ,IAAI,EAAE7I,SAAS;UACfkJ,OAAO,EAAExB,WAAW,GAAG,KAAK,GAAG,OAAO;UACtCyB,QAAQ,EAAE5B,SAAS;UACnB6B,QAAQ,EAAEnC;QACZ,CAAC;QACD,IAAIoC,QAAQ,GAAG,OAAOvC,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAC,CAAC,GAAGA,KAAK;QAC5D,IAAIuC,QAAQ,EAAE;UACZJ,eAAe,CAACK,GAAG,GAAG;YACpBxC,KAAK,EAAEuC;UACT,CAAC;QACH;;QAEA;QACA;QACA,IAAIE,eAAe,GAAG,EAAE;QACxB,IAAIC,cAAc,GAAG,EAAE;QACvBzE,MAAM,CAACvD,IAAI,CAAC4B,WAAW,CAAC,CAACY,OAAO,CAAC,UAAU9C,GAAG,EAAE;UAC9C,IAAIA,GAAG,CAACiE,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC5BoE,eAAe,CAACnF,IAAI,CAAClD,GAAG,CAAC;UAC3B,CAAC,MAAM;YACLsI,cAAc,CAACpF,IAAI,CAAClD,GAAG,CAAC;UAC1B;QACF,CAAC,CAAC;;QAEF;QACA;QACAqI,eAAe,CAACvF,OAAO,CAAC,UAAUyF,SAAS,EAAE;UAC3CnK,SAAS,CAACqB,cAAc,CAACyC,WAAW,CAACqG,SAAS,CAAC,CAAC,EAAE,SAAS,CAACnI,MAAM,CAACmI,SAAS,CAAC,EAAEzK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiK,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;YACnIC,OAAO,EAAE;UACX,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;;QAEF;QACA;QACA,IAAIxF,KAAK,GAAGpE,SAAS,CAACsB,QAAQ,EAAE6H,OAAO,EAAEQ,eAAe,CAAC;QACzDvF,KAAK,CAACxD,kBAAkB,CAAC,GAAGuH,KAAK,CAACiC,UAAU;;QAE5C;QACAhG,KAAK,CAACiG,YAAY,CAAC1J,UAAU,EAAE0H,QAAQ,CAAC;;QAExC;QACA,IAAI/B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCpC,KAAK,CAACiG,YAAY,CAAC5J,eAAe,EAAE8B,QAAQ,CAACO,IAAI,CAAC,GAAG,CAAC,CAAC;QACzD;;QAEA;QACA;QACAoH,cAAc,CAACxF,OAAO,CAAC,UAAUyF,SAAS,EAAE;UAC1CnK,SAAS,CAACqB,cAAc,CAACyC,WAAW,CAACqG,SAAS,CAAC,CAAC,EAAE,UAAU,CAACnI,MAAM,CAACmI,SAAS,CAAC,EAAER,eAAe,CAAC;QAClG,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACFW,gBAAgB,GAAG3K,cAAc,CAAC8I,eAAe,EAAE,CAAC,CAAC;IACrD8B,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IACpCG,aAAa,GAAGH,gBAAgB,CAAC,CAAC,CAAC;EACrC,OAAO,UAAUI,IAAI,EAAE;IACrB,IAAIC,SAAS;IACb,IAAI,CAACzC,SAAS,IAAIM,kBAAkB,IAAI,CAACR,YAAY,EAAE;MACrD2C,SAAS,GAAG,aAAa1K,KAAK,CAAC2K,aAAa,CAAC1D,KAAK,EAAE,IAAI,CAAC;IAC3D,CAAC,MAAM;MACL,IAAI2D,KAAK;MACTF,SAAS,GAAG,aAAa1K,KAAK,CAAC2K,aAAa,CAAC,OAAO,EAAEpL,QAAQ,CAAC,CAAC,CAAC,GAAGqL,KAAK,GAAG,CAAC,CAAC,EAAEpL,eAAe,CAACoL,KAAK,EAAElK,UAAU,EAAE6J,cAAc,CAAC,EAAE/K,eAAe,CAACoL,KAAK,EAAEnK,SAAS,EAAE+J,aAAa,CAAC,EAAEI,KAAK,GAAG;QAC5LC,uBAAuB,EAAE;UACvBC,MAAM,EAAER;QACV;MACF,CAAC,CAAC,CAAC;IACL;IACA,OAAO,aAAatK,KAAK,CAAC2K,aAAa,CAAC3K,KAAK,CAAC+K,QAAQ,EAAE,IAAI,EAAEL,SAAS,EAAED,IAAI,CAAC;EAChF,CAAC;AACH;AACA,OAAO,IAAIO,OAAO,GAAG,SAASA,OAAOA,CAAC9C,KAAK,EAAE+C,YAAY,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM,GAAGzL,cAAc,CAACwI,KAAK,EAAE,CAAC,CAAC;IACnC7G,QAAQ,GAAG8J,MAAM,CAAC,CAAC,CAAC;IACpB/C,QAAQ,GAAG+C,MAAM,CAAC,CAAC,CAAC;IACpBjC,OAAO,GAAGiC,MAAM,CAAC,CAAC,CAAC;IACnBtH,WAAW,GAAGsH,MAAM,CAAC,CAAC,CAAC;IACvB3D,UAAU,GAAG2D,MAAM,CAAC,CAAC,CAAC;IACtBzD,KAAK,GAAGyD,MAAM,CAAC,CAAC,CAAC;EACnB,IAAIC,KAAK,GAAGF,OAAO,IAAI,CAAC,CAAC;IACvBG,KAAK,GAAGD,KAAK,CAACC,KAAK;;EAErB;EACA,IAAI7D,UAAU,EAAE;IACd,OAAO,IAAI;EACb;EACA,IAAI8D,YAAY,GAAGjK,QAAQ;;EAE3B;EACA;EACA,IAAIkK,WAAW,GAAG;IAChB,eAAe,EAAE,cAAc;IAC/B,kBAAkB,EAAE,EAAE,CAACxJ,MAAM,CAAC2F,KAAK;EACrC,CAAC;;EAED;EACA4D,YAAY,GAAGzK,UAAU,CAACQ,QAAQ,EAAE+G,QAAQ,EAAEc,OAAO,EAAEqC,WAAW,EAAEF,KAAK,CAAC;;EAE1E;EACA,IAAIxH,WAAW,EAAE;IACf2B,MAAM,CAACvD,IAAI,CAAC4B,WAAW,CAAC,CAACY,OAAO,CAAC,UAAUyF,SAAS,EAAE;MACpD;MACA,IAAI,CAACe,YAAY,CAACf,SAAS,CAAC,EAAE;QAC5Be,YAAY,CAACf,SAAS,CAAC,GAAG,IAAI;QAC9B,IAAIsB,cAAc,GAAGpK,cAAc,CAACyC,WAAW,CAACqG,SAAS,CAAC,CAAC;QAC3D,IAAIuB,eAAe,GAAG5K,UAAU,CAAC2K,cAAc,EAAEpD,QAAQ,EAAE,UAAU,CAACrG,MAAM,CAACmI,SAAS,CAAC,EAAEqB,WAAW,EAAEF,KAAK,CAAC;QAC5G,IAAInB,SAAS,CAACtE,UAAU,CAAC,QAAQ,CAAC,EAAE;UAClC0F,YAAY,GAAGG,eAAe,GAAGH,YAAY;QAC/C,CAAC,MAAM;UACLA,YAAY,IAAIG,eAAe;QACjC;MACF;IACF,CAAC,CAAC;EACJ;EACA,OAAO,CAAC/D,KAAK,EAAEwB,OAAO,EAAEoC,YAAY,CAAC;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}