{"ast": null, "code": "import * as React from 'react';\nimport isEqual from \"rc-util/es/isEqual\";\n/**\n * Singleton cache will only take latest `cacheParams` as key\n * and return the result for callback matching.\n */\nexport default function useSingletonCache() {\n  const cacheRef = React.useRef([null, null]);\n  const getCache = (cacheKeys, callback) => {\n    const filteredKeys = cacheKeys.map(item => item instanceof HTMLElement || isNaN(item) ? '' : item);\n    if (!isEqual(cacheRef.current[0], filteredKeys)) {\n      cacheRef.current = [filteredKeys, callback()];\n    }\n    return cacheRef.current[1];\n  };\n  return getCache;\n}", "map": {"version": 3, "names": ["React", "isEqual", "useSingletonCache", "cacheRef", "useRef", "getCache", "cacheKeys", "callback", "filtered<PERSON>eys", "map", "item", "HTMLElement", "isNaN", "current"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/watermark/useSingletonCache.js"], "sourcesContent": ["import * as React from 'react';\nimport isEqual from \"rc-util/es/isEqual\";\n/**\n * Singleton cache will only take latest `cacheParams` as key\n * and return the result for callback matching.\n */\nexport default function useSingletonCache() {\n  const cacheRef = React.useRef([null, null]);\n  const getCache = (cacheKeys, callback) => {\n    const filteredKeys = cacheKeys.map(item => item instanceof HTMLElement || isNaN(item) ? '' : item);\n    if (!isEqual(cacheRef.current[0], filteredKeys)) {\n      cacheRef.current = [filteredKeys, callback()];\n    }\n    return cacheRef.current[1];\n  };\n  return getCache;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,oBAAoB;AACxC;AACA;AACA;AACA;AACA,eAAe,SAASC,iBAAiBA,CAAA,EAAG;EAC1C,MAAMC,QAAQ,GAAGH,KAAK,CAACI,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;EAC3C,MAAMC,QAAQ,GAAGA,CAACC,SAAS,EAAEC,QAAQ,KAAK;IACxC,MAAMC,YAAY,GAAGF,SAAS,CAACG,GAAG,CAACC,IAAI,IAAIA,IAAI,YAAYC,WAAW,IAAIC,KAAK,CAACF,IAAI,CAAC,GAAG,EAAE,GAAGA,IAAI,CAAC;IAClG,IAAI,CAACT,OAAO,CAACE,QAAQ,CAACU,OAAO,CAAC,CAAC,CAAC,EAAEL,YAAY,CAAC,EAAE;MAC/CL,QAAQ,CAACU,OAAO,GAAG,CAACL,YAAY,EAAED,QAAQ,CAAC,CAAC,CAAC;IAC/C;IACA,OAAOJ,QAAQ,CAACU,OAAO,CAAC,CAAC,CAAC;EAC5B,CAAC;EACD,OAAOR,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}