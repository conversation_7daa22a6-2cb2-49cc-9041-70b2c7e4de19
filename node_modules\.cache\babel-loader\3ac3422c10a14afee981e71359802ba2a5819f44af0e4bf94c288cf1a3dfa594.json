{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\RealTimeTraffic.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { BarChart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\nimport vehiclesData from '../data/vehicles.json';\n\n// 注册必要的echarts组件\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\necharts.use([Bar<PERSON><PERSON>, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\nconst StyledCanvas = styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\n_c = PageContainer;\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 24px' : props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 0' : props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\n_c2 = MainContent;\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\n_c3 = InfoCard;\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n  \n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n_c4 = CompactStatistic;\nconst RealTimeTraffic = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [vehicles, setVehicles] = useState([]);\n  const [events, setEvents] = useState(() => {\n    try {\n      const savedEvents = localStorage.getItem('realTimeTrafficEvents');\n      return savedEvents ? JSON.parse(savedEvents) : [];\n    } catch (error) {\n      console.error('读取事件列表失败:', error);\n      return [];\n    }\n  });\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\n  const [stats, setStats] = useState({\n    totalVehicles: 0,\n    onlineVehicles: 0,\n    offlineVehicles: 0,\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0\n  });\n\n  // 存储在线车辆的 BSM ID，初始时为空集合，表示所有车辆离线\n  const [onlineBsmIds, setOnlineBsmIds] = useState(() => {\n    try {\n      const savedOnlineIds = localStorage.getItem('realTimeTrafficOnlineIds');\n      // 返回空集合，确保所有车辆初始状态为离线\n      return new Set();\n    } catch (error) {\n      console.error('读取在线ID缓存失败:', error);\n      return new Set();\n    }\n  });\n  // 存储最后一次收到 BSM 消息的时间\n  const lastBsmTime = useRef({});\n  const eventChartRef = useRef(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n  const [eventStats, setEventStats] = useState(() => {\n    try {\n      const savedStats = localStorage.getItem('realTimeTrafficEventStats');\n      return savedStats ? JSON.parse(savedStats) : {\n        '401': 0,\n        // 道路抛洒物\n        '404': 0,\n        // 道路障碍物\n        '405': 0,\n        // 行人通过马路\n        '904': 0,\n        // 逆行车辆\n        '910': 0,\n        // 违停车辆\n        '1002': 0,\n        // 道路施工\n        '901': 0 // 车辆超速\n      };\n    } catch (error) {\n      console.error('读取事件统计数据失败:', error);\n      return {\n        '401': 0,\n        '404': 0,\n        '405': 0,\n        '904': 0,\n        '910': 0,\n        '1002': 0,\n        '901': 0\n      };\n    }\n  });\n\n  // 添加RSI事件缓存，用于检测重复事件\n  const prevRsiEvents = useRef(new Map());\n\n  // 添加手动更新车辆状态和位置信息的函数\n  const updateVehicleStatus = useCallback((bsmId, status, speed = 0, lat = 0, lng = 0, heading = 0) => {\n    // 确保速度和航向角是格式化的数值，保留两位小数\n    const formattedSpeed = parseFloat(speed).toFixed(0);\n    const formattedHeading = parseFloat(heading).toFixed(2);\n    console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${formattedSpeed}, 位置=(${lat}, ${lng})`);\n\n    // 更新在线状态\n    if (status === 'online') {\n      setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n      lastBsmTime.current[bsmId] = Date.now();\n    } else {\n      setOnlineBsmIds(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(bsmId);\n        return newSet;\n      });\n    }\n\n    // 更新车辆信息\n    setVehicles(prevVehicles => prevVehicles.map(vehicle => vehicle.bsmId === bsmId ? {\n      ...vehicle,\n      status: status,\n      speed: parseFloat(formattedSpeed),\n      // 确保是数值类型\n      lat: parseFloat(lat.toFixed(7)),\n      lng: parseFloat(lng.toFixed(7)),\n      heading: parseFloat(formattedHeading) // 确保是数值类型\n    } : vehicle));\n  }, []);\n\n  // 在组件挂载时，暴露updateVehicleStatus函数到window对象\n  useEffect(() => {\n    window.updateVehicleStatus = updateVehicleStatus;\n\n    // 用于监听CampusModel是否接收到BSM消息的事件\n    const handleRealBsmReceived = event => {\n      if (event.data && event.data.type === 'realBsmReceived') {\n        // console.log('收到CampusModel发送的真实BSM消息通知');\n      }\n    };\n    window.addEventListener('message', handleRealBsmReceived);\n\n    // 确保页面刷新时清空在线车辆状态\n    console.log('组件初始化，重置所有车辆为离线状态');\n    setOnlineBsmIds(new Set());\n    lastBsmTime.current = {};\n    return () => {\n      window.removeEventListener('message', handleRealBsmReceived);\n      delete window.updateVehicleStatus;\n    };\n  }, [updateVehicleStatus]);\n\n  // 使用防抖保存事件列表到 localStorage\n  useEffect(() => {\n    const saveTimer = setTimeout(() => {\n      try {\n        localStorage.setItem('realTimeTrafficEvents', JSON.stringify(events));\n      } catch (error) {\n        console.error('保存事件列表失败:', error);\n      }\n    }, 1000); // 延迟1秒保存，避免频繁写入\n\n    return () => clearTimeout(saveTimer);\n  }, [events]);\n\n  // 使用防抖保存事件统计数据到 localStorage\n  useEffect(() => {\n    const saveTimer = setTimeout(() => {\n      try {\n        localStorage.setItem('realTimeTrafficEventStats', JSON.stringify(eventStats));\n      } catch (error) {\n        console.error('保存事件统计数据失败:', error);\n      }\n    }, 1000); // 延迟1秒保存，避免频繁写入\n\n    return () => clearTimeout(saveTimer);\n  }, [eventStats]);\n\n  // 获取车辆数据\n  const fetchVehicles = useCallback(async () => {\n    try {\n      // 先尝试从API获取最新数据\n      console.log('尝试从API获取最新车辆数据');\n      const apiData = await fetchLatestVehiclesData(true);\n\n      // 如果API获取成功，直接使用API数据\n      if (apiData && apiData.length > 0) {\n        console.log('成功从API获取车辆数据，车辆数量:', apiData.length);\n\n        // 获取所有车辆的BSM ID列表\n        const bsmIds = apiData.map(v => v.bsmId).filter(id => id);\n        console.log('所有车辆的BSM ID:', bsmIds);\n\n        // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\n        const updatedVehicles = apiData.map(vehicle => {\n          // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\n          const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);\n          return {\n            ...vehicle,\n            plate: vehicle.plateNumber,\n            // 适配表格显示\n            status: isOnline ? 'online' : 'offline',\n            // 只有收到BSM消息的车辆才显示为在线\n            speed: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0 : 0,\n            lat: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0 : 0,\n            lng: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0 : 0,\n            heading: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0 : 0\n          };\n        });\n        setVehicles(updatedVehicles);\n\n        // 直接检查更新后的车辆在线状态\n        const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n        const totalCount = updatedVehicles.length;\n        console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);\n\n        // 更新车辆统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalVehicles: totalCount,\n          onlineVehicles: onlineCount,\n          offlineVehicles: totalCount - onlineCount\n        }));\n        return;\n      }\n\n      // 如果API获取失败，回退到本地JSON文件\n      console.log('API获取失败，回退到本地vehiclesData');\n      const vehiclesList = vehiclesData.vehicles || [];\n      console.log('从vehiclesData获取车辆数据，车辆数量:', vehiclesList.length);\n\n      // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\n      const updatedVehicles = vehiclesList.map(vehicle => {\n        // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\n        const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);\n        return {\n          ...vehicle,\n          plate: vehicle.plateNumber,\n          // 适配表格显示\n          status: isOnline ? 'online' : 'offline',\n          // 只有收到BSM消息的车辆才显示为在线\n          speed: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0 : 0,\n          lat: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0 : 0,\n          lng: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0 : 0,\n          heading: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0 : 0\n        };\n      });\n      setVehicles(updatedVehicles);\n\n      // 直接检查更新后的车辆在线状态\n      const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n      const totalCount = updatedVehicles.length;\n      console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);\n\n      // 更新车辆统计数据\n      setStats(prevStats => ({\n        ...prevStats,\n        totalVehicles: totalCount,\n        onlineVehicles: onlineCount,\n        offlineVehicles: totalCount - onlineCount\n      }));\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n    }\n  }, [onlineBsmIds]);\n\n  // 获取设备统计数据\n  const fetchDeviceStats = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      if (response.data && response.data.success) {\n        const devicesData = response.data.data;\n\n        // 更新设备统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalDevices: devicesData.length,\n          onlineDevices: devicesData.filter(d => d.status === 'online').length,\n          offlineDevices: devicesData.filter(d => d.status === 'offline').length\n        }));\n      }\n    } catch (error) {\n      console.error('获取设备统计数据失败:', error);\n    }\n  };\n\n  // 监听 BSM 消息\n  useEffect(() => {\n    const handleBsmMessage = event => {\n      if (event.data && event.data.type === 'bsm') {\n        // 获取bsmId，确保它正确地从消息中提取\n        const bsmData = event.data.data || {};\n        const bsmId = bsmData.bsmId || event.data.bsmId;\n        if (!bsmId) {\n          console.error('BSM消息缺少bsmId:', event.data);\n          return;\n        }\n\n        // console.log('收到BSM消息，ID:', bsmId);\n        const now = Date.now();\n\n        // 更新最后接收时间\n        lastBsmTime.current[bsmId] = now;\n\n        // 添加到在线bsmId集合\n        setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n\n        // 提取正确的BSM数据并格式化为两位小数\n        const speed = parseFloat((parseFloat(bsmData.partSpeed || event.data.speed || 0) * 3.6).toFixed(0)); // 转换为km/h，保留两位小数\n        const lat = parseFloat(parseFloat(bsmData.partLat || event.data.lat || 0).toFixed(7));\n        const lng = parseFloat(parseFloat(bsmData.partLong || event.data.lng || 0).toFixed(7));\n        const heading = parseFloat(parseFloat(bsmData.partHeading || event.data.heading || 0).toFixed(2)); // 保留两位小数\n\n        // console.log(`车辆${bsmId}状态: 速度=${speed.toFixed(2)}km/h, 位置=(${lat.toFixed(7)}, ${lng.toFixed(7)}), 航向=${heading.toFixed(2)}°`);\n\n        // 更新车辆状态和位置信息\n        setVehicles(prevVehicles => {\n          // 检查是否找到对应车辆\n          const foundVehicle = prevVehicles.find(v => v.bsmId === bsmId);\n          if (!foundVehicle) {\n            console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);\n            return prevVehicles;\n          }\n          const updatedVehicles = prevVehicles.map(vehicle => vehicle.bsmId === bsmId ? {\n            ...vehicle,\n            status: 'online',\n            speed: speed,\n            lat: lat,\n            lng: lng,\n            heading: heading\n          } : vehicle);\n\n          // 计算更新后的在线车辆数量\n          const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n          const totalCount = updatedVehicles.length;\n\n          // 更新统计数据\n          setStats(prevStats => ({\n            ...prevStats,\n            onlineVehicles: onlineCount,\n            offlineVehicles: totalCount - onlineCount\n          }));\n          return updatedVehicles;\n        });\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleBsmMessage);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleBsmMessage);\n    };\n  }, []);\n\n  // 修改检查在线状态的useEffect，同步更新统计信息\n  useEffect(() => {\n    const checkOnlineStatus = () => {\n      const now = Date.now();\n      console.log('检查车辆在线状态...');\n\n      // 只有在特定条件下才将车辆设为离线\n      // 例如，只有收到过BSM消息的车辆才会被检查是否超时\n      setOnlineBsmIds(prev => {\n        const newOnlineBsmIds = new Set(prev);\n        let hasChanges = false;\n\n        // 仅检查已有最后更新时间的车辆\n        prev.forEach(bsmId => {\n          const lastTime = lastBsmTime.current[bsmId];\n\n          // 只有收到过BSM消息的车辆才会被检查是否超时\n          if (lastTime && now - lastTime > 30000) {\n            console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);\n            newOnlineBsmIds.delete(bsmId);\n            hasChanges = true;\n          }\n        });\n        if (hasChanges) {\n          // 更新车辆状态\n          setVehicles(prevVehicles => {\n            const updatedVehicles = prevVehicles.map(vehicle => {\n              // 只更新有最后更新时间的车辆状态\n              if (lastBsmTime.current[vehicle.bsmId]) {\n                const isOnline = newOnlineBsmIds.has(vehicle.bsmId);\n                return {\n                  ...vehicle,\n                  status: isOnline ? 'online' : 'offline'\n                };\n              }\n              return vehicle;\n            });\n\n            // 计算更新后的在线车辆数量\n            const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n            const totalCount = updatedVehicles.length;\n\n            // 更新统计数据\n            setStats(prevStats => ({\n              ...prevStats,\n              onlineVehicles: onlineCount,\n              offlineVehicles: totalCount - onlineCount\n            }));\n            return updatedVehicles;\n          });\n        }\n        return newOnlineBsmIds;\n      });\n    };\n    const interval = setInterval(checkOnlineStatus, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // 重置所有车辆的初始状态\n  useEffect(() => {\n    // 将所有车辆状态重置为离线\n    const resetAllVehicles = () => {\n      // 只有在没有任何BSM消息的情况下才执行重置\n      if (onlineBsmIds.size === 0) {\n        setVehicles(prevVehicles => prevVehicles.map(vehicle => ({\n          ...vehicle,\n          status: 'offline',\n          speed: 0,\n          lat: 0,\n          lng: 0,\n          heading: 0\n        })));\n        console.log('已重置所有车辆为离线状态');\n      }\n    };\n\n    // 初始执行一次\n    resetAllVehicles();\n\n    // 然后每30秒检查一次\n    const interval = setInterval(resetAllVehicles, 30000);\n    return () => clearInterval(interval);\n  }, [onlineBsmIds]);\n\n  // 在组件挂载时获取数据\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      fetchVehicles();\n      await fetchDeviceStats();\n      setLoading(false);\n    };\n    loadData();\n    // // 降低更新频率，避免频繁覆盖状态\n    // const interval = setInterval(loadData, 300000); // 每60x5秒更新一次\n    // return () => clearInterval(interval);\n  }, []);\n\n  // 添加对车辆数据变更的监听\n  useEffect(() => {\n    console.log('设置车辆数据变更监听');\n\n    // 监听自定义事件\n    const handleVehiclesDataChanged = () => {\n      console.log('检测到车辆数据变更事件，重新获取车辆列表');\n      fetchVehicles();\n    };\n\n    // 监听localStorage变化\n    const handleStorageChange = event => {\n      if (event.key === 'vehiclesLastUpdated' || event.key === 'vehiclesData') {\n        console.log('检测到localStorage变化，重新获取车辆列表');\n        fetchVehicles();\n      }\n    };\n\n    // 添加事件监听器\n    window.addEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n    window.addEventListener('storage', handleStorageChange);\n\n    // 初始检查是否有更新\n    const lastUpdated = localStorage.getItem('vehiclesLastUpdated');\n    if (lastUpdated) {\n      console.log('初始检查到vehiclesLastUpdated:', lastUpdated);\n      fetchVehicles();\n    }\n\n    // 设置更频繁的强制轮询，确保在部署环境中也能获取最新数据\n    const forcedPollingInterval = setInterval(() => {\n      console.log('强制轮询：重新获取车辆列表');\n      fetchVehicles();\n    }, 10000); // 每10秒强制刷新一次\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n      window.removeEventListener('storage', handleStorageChange);\n      clearInterval(forcedPollingInterval);\n    };\n  }, []);\n\n  // 添加检查本地缓存与API数据是否一致的机制\n  // 定义一个单独的API调用函数，用于获取最新的车辆列表数据\n  const fetchLatestVehiclesData = async (returnData = false) => {\n    try {\n      // 尝试多个可能的API地址\n      const possibleApiUrls = [process.env.REACT_APP_API_URL || 'http://localhost:5000'\n      // 'http://localhost:5000',\n      // window.location.origin, // 当前站点的根URL\n      // `${window.location.origin}/api`, // 当前站点下的/api路径\n      // 'http://localhost:5000/api',\n      // 'http://127.0.0.1:5000',\n      // 'http://127.0.0.1:5000/api'\n      ];\n      console.log('尝试从多个API地址获取车辆数据');\n\n      // // 尝试从本地JSON文件直接获取\n      // try {\n      //   console.log('尝试从本地JSON文件获取数据');\n      //   const jsonResponse = await axios.get('/vehicles.json');\n      //   if (jsonResponse.data && jsonResponse.data.vehicles) {\n      //     console.log('成功从本地JSON文件获取数据:', jsonResponse.data.vehicles.length);\n      //     if (returnData) {\n      //       return jsonResponse.data.vehicles;\n      //     }\n      //     processVehiclesData(jsonResponse.data.vehicles);\n      //     return jsonResponse.data.vehicles;\n      //   }\n      // } catch (jsonError) {\n      //   console.log('从本地JSON获取失败:', jsonError.message);\n      // }\n\n      // 逐个尝试API地址\n      let succeeded = false;\n      let vehiclesData = null;\n      for (const apiUrl of possibleApiUrls) {\n        if (succeeded) break;\n        try {\n          console.log(`尝试从 ${apiUrl}/api/vehicles/list 获取数据`);\n          const response = await axios.get(`${apiUrl}/api/vehicles/list`);\n          if (response.data && response.data.vehicles) {\n            console.log(`成功从 ${apiUrl} 获取数据:`, response.data.vehicles.length);\n            vehiclesData = response.data.vehicles;\n            if (returnData) {\n              return vehiclesData;\n            }\n            processVehiclesData(response.data.vehicles);\n            succeeded = true;\n            break;\n          }\n        } catch (error) {\n          console.log(`从 ${apiUrl} 获取失败:`, error.message);\n          // 继续尝试下一个URL\n        }\n      }\n      if (!succeeded && !returnData) {\n        console.log('所有API地址都获取失败，尝试使用vehicles.json');\n        // 尝试从当前页面获取\n        try {\n          const response = await fetch('/vehicles.json');\n          if (response.ok) {\n            const data = await response.json();\n            if (data && data.vehicles) {\n              console.log('从public/vehicles.json获取数据成功:', data.vehicles.length);\n              processVehiclesData(data.vehicles);\n              return data.vehicles;\n            }\n          }\n        } catch (e) {\n          console.error('从public/vehicles.json获取失败:', e);\n        }\n      }\n      return vehiclesData || [];\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n      return [];\n    }\n  };\n\n  // 添加处理车辆数据的辅助函数\n  const processVehiclesData = newVehicles => {\n    // 与当前列表比较\n    if (vehicles.length !== newVehicles.length) {\n      console.log('检测到车辆数量变化，从', vehicles.length, '到', newVehicles.length);\n      fetchVehicles(); // 重新加载\n      return;\n    }\n\n    // 检查是否有新车辆ID\n    const currentIds = new Set(vehicles.map(v => v.id));\n    const hasNewVehicle = newVehicles.some(v => !currentIds.has(v.id));\n    if (hasNewVehicle) {\n      console.log('检测到新增车辆');\n      fetchVehicles(); // 重新加载\n    }\n  };\n\n  // 添加自动选择第一个车辆的逻辑\n  useEffect(() => {\n    // 当车辆列表加载完成且有车辆数据时\n    if (vehicles.length > 0 && !selectedVehicle) {\n      // 自动选择第一个车辆\n      setSelectedVehicle(vehicles[0]);\n      console.log('已自动选择第一个车辆:', vehicles[0].plateNumber);\n    }\n  }, [vehicles, selectedVehicle]);\n\n  // 修改图表初始化代码\n  useEffect(() => {\n    let chart = null;\n    let handleResize = null;\n    let lastUpdateTime = new Date();\n\n    // 确保 DOM 元素存在\n    if (!eventChartRef.current) {\n      console.error('图表容器未找到');\n      return;\n    }\n\n    // 检查并清理已存在的图表实例\n    let existingChart = echarts.getInstanceByDom(eventChartRef.current);\n    if (existingChart) {\n      existingChart.dispose();\n    }\n    try {\n      // 初始化新的图表实例\n      chart = echarts.init(eventChartRef.current);\n\n      // 设置图表配置\n      const updateChart = () => {\n        const currentTime = new Date();\n        lastUpdateTime = currentTime;\n\n        // 事件类型配置\n        const eventTypes = [{\n          type: '401',\n          name: '道路抛洒物',\n          color: '#ff4d4f'\n        }, {\n          type: '404',\n          name: '道路障碍物',\n          color: '#faad14'\n        }, {\n          type: '405',\n          name: '行人通过马路',\n          color: '#1890ff'\n        }, {\n          type: '904',\n          name: '逆行车辆',\n          color: '#f5222d'\n        }, {\n          type: '910',\n          name: '违停车辆',\n          color: '#722ed1'\n        }, {\n          type: '1002',\n          name: '道路施工',\n          color: '#fa8c16'\n        }, {\n          type: '901',\n          name: '车辆超速',\n          color: '#eb2f96'\n        }];\n\n        // 处理数据\n        const data = eventTypes.map(event => ({\n          value: eventStats[event.type] || 0,\n          name: event.name,\n          itemStyle: {\n            color: event.color\n          }\n        })).filter(item => item.value > 0).sort((a, b) => b.value - a.value);\n        const option = {\n          title: {\n            text: `最后更新: ${currentTime.toLocaleTimeString()}`,\n            left: 'center',\n            top: -5,\n            textStyle: {\n              fontSize: 12,\n              color: '#999'\n            }\n          },\n          grid: {\n            top: 30,\n            bottom: 0,\n            left: 0,\n            right: 50,\n            containLabel: true\n          },\n          animation: true,\n          animationDuration: 0,\n          animationDurationUpdate: 1000,\n          animationEasingUpdate: 'quinticInOut',\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'shadow'\n            }\n          },\n          xAxis: {\n            type: 'value',\n            show: false,\n            splitLine: {\n              show: false\n            }\n          },\n          yAxis: {\n            type: 'category',\n            data: data.map(item => item.name),\n            axisLabel: {\n              fontSize: 12,\n              color: '#666',\n              margin: 8\n            },\n            axisTick: {\n              show: false\n            },\n            axisLine: {\n              show: false\n            }\n          },\n          series: [{\n            type: 'bar',\n            data: data,\n            barWidth: '50%',\n            label: {\n              show: true,\n              position: 'right',\n              formatter: '{c}次',\n              fontSize: 12,\n              color: '#666'\n            },\n            itemStyle: {\n              borderRadius: [0, 4, 4, 0]\n            },\n            realtimeSort: false,\n            animationDelay: function (idx) {\n              return idx * 100;\n            }\n          }]\n        };\n\n        // 使用 notMerge: false 来保持增量更新\n        chart.setOption(option, {\n          notMerge: false,\n          replaceMerge: ['series']\n        });\n      };\n\n      // 初始更新\n      updateChart();\n\n      // 监听事件统计变化，每分钟更新一次\n      const statsInterval = setInterval(updateChart, 60000); // 60000ms = 1分钟\n\n      // 监听窗口大小变化\n      handleResize = () => {\n        var _chart;\n        (_chart = chart) === null || _chart === void 0 ? void 0 : _chart.resize();\n      };\n      window.addEventListener('resize', handleResize);\n\n      // 清理函数\n      return () => {\n        clearInterval(statsInterval);\n        if (handleResize) {\n          window.removeEventListener('resize', handleResize);\n        }\n        if (chart) {\n          chart.dispose();\n        }\n      };\n    } catch (error) {\n      console.error('初始化图表失败:', error);\n    }\n  }, [eventStats]);\n\n  // 修改 RSI 消息处理逻辑\n  useEffect(() => {\n    const handleRsiMessage = event => {\n      try {\n        if (event.data && event.data.type === 'RSI') {\n          const rsiData = event.data.data;\n          if (!rsiData || !rsiData.rtes) return;\n          if (rsiData.rtes.length > 0) {\n            console.log('RealTimeTraffic 收到 RSI 消息:', event.data);\n          }\n          const latitude = parseFloat(rsiData.posLat);\n          const longitude = parseFloat(rsiData.posLong);\n          const rsuId = rsiData.rsuId;\n          const mac = event.data.mac || '';\n          const timestamp = event.data.tm || Date.now();\n\n          // 统计本帧所有非重复事件类型\n          const nonDuplicateEventTypes = [];\n          rsiData.rtes.forEach(event => {\n            const eventType = event.eventType;\n            const eventKey = `${rsuId}_${mac}_${eventType}`;\n            const isDuplicate = checkDuplicateEvent(eventKey, timestamp, {\n              lat: latitude,\n              lng: longitude\n            }, prevRsiEvents.current);\n            // 获取事件类型的中文描述\n            let eventTypeText = '';\n            let eventColor = '';\n            switch (eventType) {\n              case '401':\n                eventTypeText = '道路抛洒物';\n                eventColor = '#ff4d4f';\n                break;\n              case '404':\n                eventTypeText = '道路障碍物';\n                eventColor = '#faad14';\n                break;\n              case '405':\n                eventTypeText = '行人通过马路';\n                eventColor = '#1890ff';\n                break;\n              case '904':\n                eventTypeText = '逆行车辆';\n                eventColor = '#f5222d';\n                break;\n              case '910':\n                eventTypeText = '违停车辆';\n                eventColor = '#722ed1';\n                break;\n              case '1002':\n                eventTypeText = '道路施工';\n                eventColor = '#fa8c16';\n                break;\n              case '901':\n                eventTypeText = '车辆超速';\n                eventColor = '#eb2f96';\n                break;\n              default:\n                eventTypeText = event.description || '未知事件';\n                eventColor = '#8c8c8c';\n            }\n            // 更新事件列表\n            // const newEvent = {\n            //   key: Date.now() + Math.random(),\n            //   type: eventTypeText,\n            //   time: new Date().toLocaleTimeString(),\n            //   vehicle: rsiData.rsuId || '未知设备',\n            //   color: eventColor,\n            //   eventType: eventType,\n            //   location: {\n            //     latitude: latitude,\n            //     longitude: longitude\n            //   }\n\n            // };\n            // setEvents(prev => {\n            //   const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n            //   return newEvents;\n            // });\n            if (!isDuplicate) {\n              // 更新事件列表\n              const newEvent = {\n                key: Date.now() + Math.random(),\n                type: eventTypeText,\n                time: new Date().toLocaleTimeString(),\n                vehicle: rsiData.rsuId || '未知设备',\n                color: eventColor,\n                eventType: eventType,\n                location: {\n                  latitude: latitude,\n                  longitude: longitude\n                }\n              };\n              setEvents(prev => {\n                const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n                return newEvents;\n              });\n              nonDuplicateEventTypes.push(eventType);\n            } else {\n              console.log(`检测到重复事件，不累计统计: ${eventTypeText}`);\n            }\n          });\n\n          // 只累加一次\n          if (nonDuplicateEventTypes.length > 0) {\n            setEventStats(prev => {\n              const newStats = {\n                ...prev\n              };\n              nonDuplicateEventTypes.forEach(eventType => {\n                newStats[eventType] = (newStats[eventType] || 0) + 1;\n              });\n              return newStats;\n            });\n          }\n        }\n      } catch (error) {\n        console.error('处理 RSI 消息失败:', error);\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleRsiMessage);\n    return () => {\n      window.removeEventListener('message', handleRsiMessage);\n    };\n  }, []);\n\n  // 在组件中添加检查重复事件的辅助函数\n  const checkDuplicateEvent = (eventKey, currentTime, currentPos, prevEvents) => {\n    // 检查是否有相同事件的历史记录\n    if (prevEvents.has(eventKey)) {\n      const prevEvent = prevEvents.get(eventKey);\n      const timeDiff = currentTime - prevEvent.time;\n\n      // 如果时间差小于200ms\n      if (timeDiff < 3000) {\n        // 计算两个位置之间的距离\n        const distance = calculateDistance(currentPos.lat, currentPos.lng, prevEvent.pos.lat, prevEvent.pos.lng);\n\n        // 如果距离小于5米，认为是同一个事件\n        if (distance < 5) {\n          // 更新时间戳，但保持为重复事件\n          prevEvents.set(eventKey, {\n            time: currentTime,\n            pos: currentPos\n          });\n          return true;\n        }\n      }\n    }\n\n    // 保存当前事件信息用于后续比较\n    prevEvents.set(eventKey, {\n      time: currentTime,\n      pos: currentPos\n    });\n\n    // 不是重复事件\n    return false;\n  };\n\n  // 添加计算两点之间距离的函数\n  const calculateDistance = (lat1, lon1, lat2, lon2) => {\n    // 如果任何坐标为0，表示坐标无效，返回大距离以避免被视为相同事件\n    if (lat1 === 0 || lon1 === 0 || lat2 === 0 || lon2 === 0) {\n      return 999;\n    }\n\n    // 计算两点之间的距离（哈弗辛公式）\n    const R = 6371000; // 地球半径，单位：米\n    const dLat = (lat2 - lat1) * Math.PI / 180;\n    const dLon = (lon2 - lon1) * Math.PI / 180;\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * Math.sin(dLon / 2) * Math.sin(dLon / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    const distance = R * c;\n    return distance;\n  };\n\n  // 处理车辆选择\n  const handleVehicleSelect = vehicle => {\n    console.log('选择车辆:', vehicle.plateNumber, '状态:', vehicle.status);\n    setSelectedVehicle(vehicle);\n  };\n\n  // 修改车辆状态更新逻辑，确保同步更新selectedVehicle\n  useEffect(() => {\n    // 当车辆列表更新后，如果当前有选中的车辆，则更新选中的车辆信息\n    if (selectedVehicle) {\n      const updatedSelectedVehicle = vehicles.find(v => v.id === selectedVehicle.id);\n      if (updatedSelectedVehicle && (updatedSelectedVehicle.status !== selectedVehicle.status || updatedSelectedVehicle.speed !== selectedVehicle.speed || updatedSelectedVehicle.lat !== selectedVehicle.lat || updatedSelectedVehicle.lng !== selectedVehicle.lng || updatedSelectedVehicle.heading !== selectedVehicle.heading)) {\n        console.log(`更新选中车辆 ${selectedVehicle.plateNumber} 的信息:`, `状态从 ${selectedVehicle.status} 变为 ${updatedSelectedVehicle.status}`);\n        setSelectedVehicle(updatedSelectedVehicle);\n      }\n    }\n  }, [vehicles, selectedVehicle]);\n\n  // 车辆列表列定义\n  const vehicleColumns = [{\n    title: '车牌号',\n    dataIndex: 'plate',\n    key: 'plate',\n    width: '40%'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: '30%',\n    render: status => /*#__PURE__*/_jsxDEV(Badge, {\n      status: status === 'online' ? 'success' : 'error',\n      text: status === 'online' ? '在线' : '离线'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1068,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '速度',\n    dataIndex: 'speed',\n    key: 'speed',\n    width: '30%',\n    render: speed => `${typeof speed === 'number' ? speed.toFixed(0) : speed} km/h`\n  }];\n\n  // 修改实时事件列表的渲染\n  const renderEventList = () => /*#__PURE__*/_jsxDEV(List, {\n    size: \"small\",\n    dataSource: events,\n    renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n      style: {\n        padding: '8px 0'\n      },\n      children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n        title: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '13px',\n            marginBottom: '4px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: item.color,\n              marginRight: '8px'\n            },\n            children: item.type\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1093,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#666',\n              fontSize: '12px'\n            },\n            children: item.time\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1096,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1092,\n          columnNumber: 15\n        }, this),\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#666'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u8BBE\\u5907: \", item.vehicle]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1103,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u4F4D\\u7F6E: \", item.location ? `${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}` : '未知位置']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1104,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1102,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1090,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1089,\n      columnNumber: 9\n    }, this),\n    style: {\n      maxHeight: 'calc(100% - 24px)',\n      overflowY: 'auto'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1085,\n    columnNumber: 5\n  }, this);\n\n  // 添加强制定期从API获取最新数据的机制\n  useEffect(() => {\n    const apiPollingInterval = setInterval(() => {\n      console.log('直接从API检查车辆数据更新');\n      fetchLatestVehiclesData();\n    }, 15000); // 每15秒检查一次API\n\n    return () => clearInterval(apiPollingInterval);\n  }, [vehicles]);\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    spinning: loading,\n    tip: \"\\u52A0\\u8F7D\\u4E2D...\",\n    children: /*#__PURE__*/_jsxDEV(PageContainer, {\n      children: [/*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"left\",\n        collapsed: leftCollapsed,\n        onCollapse: () => setLeftCollapsed(!leftCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u548C\\u8BBE\\u5907\\u7EDF\\u8BA1\",\n          bordered: false,\n          height: \"160px\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [8, 1],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              style: {\n                display: 'flex',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u8F66\\u8F86\\u603B\\u6570\",\n                value: stats.totalVehicles,\n                valueStyle: {\n                  color: '#3f8600'\n                },\n                Style: {\n                  display: 'flex',\n                  justifyContent: 'center'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1143,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u5728\\u7EBF\\u8F66\\u8F86\",\n                value: stats.onlineVehicles\n                // suffix={`/ ${stats.totalVehicles}`} \n                ,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1151,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u79BB\\u7EBF\\u8F66\\u8F86\",\n                value: stats.offlineVehicles\n                // suffix={`/ ${stats.totalVehicles}`} \n                ,\n                valueStyle: {\n                  color: '#cf1322'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1159,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u8BBE\\u5907\\u603B\\u6570\",\n                value: stats.totalDevices,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1167,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u5728\\u7EBF\\u8BBE\\u5907\",\n                value: stats.onlineDevices\n                // suffix={`/ ${stats.totalDevices}`} \n                ,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1174,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u79BB\\u7EBF\\u8BBE\\u5907\",\n                value: stats.offlineDevices\n                // suffix={`/ ${stats.totalDevices}`} \n                ,\n                valueStyle: {\n                  color: '#cf1322'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1182,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1181,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1141,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u5217\\u8868\",\n          bordered: false,\n          height: \"calc(50% - 81px)\",\n          children: renderEventList()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u7EDF\\u8BA1\",\n          bordered: false,\n          height: \"calc(50% - 81px)\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: eventChartRef,\n            style: {\n              height: '100%',\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1199,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        leftCollapsed: leftCollapsed,\n        rightCollapsed: rightCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"right\",\n        collapsed: rightCollapsed,\n        onCollapse: () => setRightCollapsed(!rightCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u5217\\u8868\",\n          bordered: false,\n          height: \"50%\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            dataSource: vehicles,\n            columns: vehicleColumns,\n            rowKey: \"id\",\n            pagination: false,\n            size: \"small\",\n            scroll: {\n              y: 180\n            },\n            onRow: record => ({\n              onClick: () => handleVehicleSelect(record),\n              style: {\n                cursor: 'pointer',\n                background: (selectedVehicle === null || selectedVehicle === void 0 ? void 0 : selectedVehicle.id) === record.id ? '#e6f7ff' : 'transparent',\n                fontSize: '13px',\n                padding: '4px 8px'\n              }\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1216,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u8BE6\\u7EC6\\u4FE1\\u606F\",\n          bordered: false,\n          height: \"50%\",\n          children: selectedVehicle ? /*#__PURE__*/_jsxDEV(Descriptions, {\n            bordered: true,\n            column: 1,\n            size: \"small\",\n            styles: {\n              label: {\n                fontSize: '13px',\n                padding: '4px 8px'\n              },\n              content: {\n                fontSize: '13px',\n                padding: '4px 8px'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8F66\\u724C\\u53F7\",\n              children: selectedVehicle.plateNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1247,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                status: selectedVehicle.status === 'online' ? 'success' : 'error',\n                text: selectedVehicle.status === 'online' ? '在线' : '离线'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1249,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1248,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7ECF\\u5EA6\",\n              children: selectedVehicle.status === 'online' ? selectedVehicle.lng.toFixed(7) : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1254,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7EAC\\u5EA6\",\n              children: selectedVehicle.status === 'online' ? selectedVehicle.lat.toFixed(7) : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1257,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u901F\\u5EA6\",\n              children: selectedVehicle.status === 'online' ? `${typeof selectedVehicle.speed === 'number' ? selectedVehicle.speed.toFixed(0) : selectedVehicle.speed} km/h` : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1260,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u822A\\u5411\\u89D2\",\n              children: selectedVehicle.status === 'online' ? `${typeof selectedVehicle.heading === 'number' ? selectedVehicle.heading.toFixed(2) : selectedVehicle.heading}°` : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1265,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1238,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '13px'\n            },\n            children: \"\\u8BF7\\u9009\\u62E9\\u8F66\\u8F86\\u67E5\\u770B\\u8BE6\\u7EC6\\u4FE1\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1272,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1236,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1209,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1132,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1131,\n    columnNumber: 5\n  }, this);\n};\n_s(RealTimeTraffic, \"0BGEaXKIUZ+9tRxG43jf+9PDSek=\");\n_c5 = RealTimeTraffic;\nexport default RealTimeTraffic;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"InfoCard\");\n$RefreshReg$(_c4, \"CompactStatistic\");\n$RefreshReg$(_c5, \"RealTimeTraffic\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "Row", "Col", "Card", "Statistic", "List", "Table", "Descriptions", "Spin", "Badge", "echarts", "<PERSON><PERSON><PERSON>", "GridComponent", "TooltipComponent", "LegendComponent", "TitleComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "CollapsibleSidebar", "axios", "vehiclesData", "jsxDEV", "_jsxDEV", "use", "StyledCanvas", "div", "<PERSON><PERSON><PERSON><PERSON>", "_c", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "_c2", "InfoCard", "height", "_c3", "CompactStatistic", "_c4", "RealTimeTraffic", "_s", "loading", "setLoading", "vehicles", "setVehicles", "events", "setEvents", "savedEvents", "localStorage", "getItem", "JSON", "parse", "error", "console", "selectedVehicle", "setSelectedVehicle", "stats", "setStats", "totalVehicles", "onlineVehicles", "offlineVehicles", "totalDevices", "onlineDevices", "offlineDevices", "onlineBsmIds", "setOnlineBsmIds", "savedOnlineIds", "Set", "lastBsmTime", "eventChartRef", "setLeftCollapsed", "setRightCollapsed", "eventStats", "setEventStats", "savedStats", "prevRsiEvents", "Map", "updateVehicleStatus", "bsmId", "status", "speed", "lat", "lng", "heading", "formattedSpeed", "parseFloat", "toFixed", "formattedHeading", "log", "prev", "current", "Date", "now", "newSet", "delete", "prevVehicles", "map", "vehicle", "window", "handleRealBsmReceived", "event", "data", "type", "addEventListener", "removeEventListener", "saveTimer", "setTimeout", "setItem", "stringify", "clearTimeout", "fetchVehicles", "apiData", "fetchLatestVehiclesData", "length", "bsmIds", "v", "filter", "id", "updatedVehicles", "isOnline", "has", "plate", "plateNumber", "onlineCount", "totalCount", "prevStats", "vehiclesList", "fetchDeviceStats", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "success", "devicesData", "d", "handleBsmMessage", "bsmData", "partSpeed", "partLat", "partLong", "partHeading", "foundVehicle", "find", "checkOnlineStatus", "newOnlineBsmIds", "has<PERSON><PERSON><PERSON>", "for<PERSON>ach", "lastTime", "interval", "setInterval", "clearInterval", "resetAllVehicles", "size", "loadData", "handleVehiclesDataChanged", "handleStorageChange", "key", "lastUpdated", "forcedPollingInterval", "returnData", "possibleApiUrls", "succeeded", "processVehiclesData", "message", "fetch", "ok", "json", "e", "newVehicles", "currentIds", "hasNewVehicle", "some", "chart", "handleResize", "lastUpdateTime", "existingChart", "getInstanceByDom", "dispose", "init", "updateChart", "currentTime", "eventTypes", "name", "color", "value", "itemStyle", "item", "sort", "a", "b", "option", "title", "text", "toLocaleTimeString", "left", "top", "textStyle", "fontSize", "grid", "bottom", "right", "containLabel", "animation", "animationDuration", "animationDurationUpdate", "animationEasingUpdate", "tooltip", "trigger", "axisPointer", "xAxis", "show", "splitLine", "yAxis", "axisLabel", "margin", "axisTick", "axisLine", "series", "<PERSON><PERSON><PERSON><PERSON>", "label", "position", "formatter", "borderRadius", "realtimeSort", "animationDelay", "idx", "setOption", "notMerge", "replaceMerge", "statsInterval", "_chart", "resize", "handleRsiMessage", "rsiData", "rtes", "latitude", "posLat", "longitude", "posLong", "rsuId", "mac", "timestamp", "tm", "nonDuplicateEventTypes", "eventType", "eventKey", "isDuplicate", "checkDuplicateEvent", "eventTypeText", "eventColor", "description", "newEvent", "Math", "random", "time", "location", "newEvents", "slice", "push", "newStats", "currentPos", "prevEvents", "prevEvent", "timeDiff", "distance", "calculateDistance", "pos", "set", "lat1", "lon1", "lat2", "lon2", "R", "dLat", "PI", "dLon", "sin", "cos", "c", "atan2", "sqrt", "handleVehicleSelect", "updatedSelectedVehicle", "vehicleColumns", "dataIndex", "width", "render", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderEventList", "dataSource", "renderItem", "<PERSON><PERSON>", "style", "padding", "children", "Meta", "marginBottom", "marginRight", "maxHeight", "overflowY", "apiPollingInterval", "spinning", "tip", "collapsed", "onCollapse", "bordered", "gutter", "span", "display", "justifyContent", "valueStyle", "Style", "ref", "columns", "<PERSON><PERSON><PERSON>", "pagination", "scroll", "y", "onRow", "record", "onClick", "cursor", "background", "column", "styles", "content", "_c5", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RealTimeTraffic.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { Bar<PERSON>hart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\nimport vehiclesData from '../data/vehicles.json';\n\n// 注册必要的echarts组件\necharts.use([BarChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\n\nconst StyledCanvas = styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 24px' : \n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 0' : \n    props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n  \n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n\nconst RealTimeTraffic = () => {\n  const [loading, setLoading] = useState(true);\n  const [vehicles, setVehicles] = useState([]);\n  const [events, setEvents] = useState(() => {\n    try {\n      const savedEvents = localStorage.getItem('realTimeTrafficEvents');\n      return savedEvents ? JSON.parse(savedEvents) : [];\n    } catch (error) {\n      console.error('读取事件列表失败:', error);\n      return [];\n    }\n  });\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\n  const [stats, setStats] = useState({\n    totalVehicles: 0,\n    onlineVehicles: 0,\n    offlineVehicles: 0,\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0\n  });\n  \n  // 存储在线车辆的 BSM ID，初始时为空集合，表示所有车辆离线\n  const [onlineBsmIds, setOnlineBsmIds] = useState(() => {\n    try {\n      const savedOnlineIds = localStorage.getItem('realTimeTrafficOnlineIds');\n      // 返回空集合，确保所有车辆初始状态为离线\n      return new Set();\n    } catch (error) {\n      console.error('读取在线ID缓存失败:', error);\n      return new Set();\n    }\n  });\n  // 存储最后一次收到 BSM 消息的时间\n  const lastBsmTime = useRef({});\n  \n  const eventChartRef = useRef(null);\n  \n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n  \n  const [eventStats, setEventStats] = useState(() => {\n    try {\n      const savedStats = localStorage.getItem('realTimeTrafficEventStats');\n      return savedStats ? JSON.parse(savedStats) : {\n        '401': 0,  // 道路抛洒物\n        '404': 0,  // 道路障碍物\n        '405': 0,  // 行人通过马路\n        '904': 0,  // 逆行车辆\n        '910': 0,  // 违停车辆\n        '1002': 0, // 道路施工\n        '901': 0   // 车辆超速\n      };\n    } catch (error) {\n      console.error('读取事件统计数据失败:', error);\n      return {\n        '401': 0, '404': 0, '405': 0, '904': 0,\n        '910': 0, '1002': 0, '901': 0\n      };\n    }\n  });\n\n  // 添加RSI事件缓存，用于检测重复事件\n  const prevRsiEvents = useRef(new Map());\n\n  // 添加手动更新车辆状态和位置信息的函数\n  const updateVehicleStatus = useCallback((bsmId, status, speed = 0, lat = 0, lng = 0, heading = 0) => {\n    // 确保速度和航向角是格式化的数值，保留两位小数\n    const formattedSpeed = parseFloat(speed).toFixed(0);\n    const formattedHeading = parseFloat(heading).toFixed(2);\n    \n    console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${formattedSpeed}, 位置=(${lat}, ${lng})`);\n    \n    // 更新在线状态\n    if (status === 'online') {\n      setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n      lastBsmTime.current[bsmId] = Date.now();\n    } else {\n      setOnlineBsmIds(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(bsmId);\n        return newSet;\n      });\n    }\n    \n    // 更新车辆信息\n    setVehicles(prevVehicles => \n      prevVehicles.map(vehicle => \n        vehicle.bsmId === bsmId \n          ? { \n              ...vehicle, \n              status: status,\n              speed: parseFloat(formattedSpeed), // 确保是数值类型\n              lat: parseFloat(lat.toFixed(7)),\n              lng: parseFloat(lng.toFixed(7)),\n              heading: parseFloat(formattedHeading) // 确保是数值类型\n            } \n          : vehicle\n      )\n    );\n  }, []);\n  \n  // 在组件挂载时，暴露updateVehicleStatus函数到window对象\n  useEffect(() => {\n    window.updateVehicleStatus = updateVehicleStatus;\n    \n    // 用于监听CampusModel是否接收到BSM消息的事件\n    const handleRealBsmReceived = (event) => {\n      if (event.data && event.data.type === 'realBsmReceived') {\n        // console.log('收到CampusModel发送的真实BSM消息通知');\n      }\n    };\n\n    window.addEventListener('message', handleRealBsmReceived);\n    \n    // 确保页面刷新时清空在线车辆状态\n    console.log('组件初始化，重置所有车辆为离线状态');\n    setOnlineBsmIds(new Set());\n    lastBsmTime.current = {};\n    \n    return () => {\n      window.removeEventListener('message', handleRealBsmReceived);\n      delete window.updateVehicleStatus;\n    };\n  }, [updateVehicleStatus]);\n\n  // 使用防抖保存事件列表到 localStorage\n  useEffect(() => {\n    const saveTimer = setTimeout(() => {\n      try {\n        localStorage.setItem('realTimeTrafficEvents', JSON.stringify(events));\n      } catch (error) {\n        console.error('保存事件列表失败:', error);\n      }\n    }, 1000); // 延迟1秒保存，避免频繁写入\n    \n    return () => clearTimeout(saveTimer);\n  }, [events]);\n\n  // 使用防抖保存事件统计数据到 localStorage\n  useEffect(() => {\n    const saveTimer = setTimeout(() => {\n      try {\n        localStorage.setItem('realTimeTrafficEventStats', JSON.stringify(eventStats));\n      } catch (error) {\n        console.error('保存事件统计数据失败:', error);\n      }\n    }, 1000); // 延迟1秒保存，避免频繁写入\n    \n    return () => clearTimeout(saveTimer);\n  }, [eventStats]);\n\n  // 获取车辆数据\n  const fetchVehicles = useCallback(async () => {\n    try {\n      // 先尝试从API获取最新数据\n      console.log('尝试从API获取最新车辆数据');\n      const apiData = await fetchLatestVehiclesData(true);\n      \n      // 如果API获取成功，直接使用API数据\n      if (apiData && apiData.length > 0) {\n        console.log('成功从API获取车辆数据，车辆数量:', apiData.length);\n        \n        // 获取所有车辆的BSM ID列表\n        const bsmIds = apiData.map(v => v.bsmId).filter(id => id);\n        console.log('所有车辆的BSM ID:', bsmIds);\n        \n        // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\n        const updatedVehicles = apiData.map(vehicle => {\n          // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\n          const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);\n          \n          return {\n            ...vehicle,\n            plate: vehicle.plateNumber, // 适配表格显示\n            status: isOnline ? 'online' : 'offline', // 只有收到BSM消息的车辆才显示为在线\n            speed: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0) : 0,\n            lat: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0) : 0,\n            lng: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0) : 0,\n            heading: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0) : 0\n          };\n        });\n        \n        setVehicles(updatedVehicles);\n        \n        // 直接检查更新后的车辆在线状态\n        const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n        const totalCount = updatedVehicles.length;\n        \n        console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);\n        \n        // 更新车辆统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalVehicles: totalCount,\n          onlineVehicles: onlineCount,\n          offlineVehicles: totalCount - onlineCount\n        }));\n        \n        return;\n      }\n      \n      // 如果API获取失败，回退到本地JSON文件\n      console.log('API获取失败，回退到本地vehiclesData');\n      const vehiclesList = vehiclesData.vehicles || [];\n      console.log('从vehiclesData获取车辆数据，车辆数量:', vehiclesList.length);\n      \n      // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\n      const updatedVehicles = vehiclesList.map(vehicle => {\n        // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\n        const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);\n        \n        return {\n          ...vehicle,\n          plate: vehicle.plateNumber, // 适配表格显示\n          status: isOnline ? 'online' : 'offline', // 只有收到BSM消息的车辆才显示为在线\n          speed: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0) : 0,\n          lat: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0) : 0,\n          lng: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0) : 0,\n          heading: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0) : 0\n        };\n      });\n      \n      setVehicles(updatedVehicles);\n      \n      // 直接检查更新后的车辆在线状态\n      const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n      const totalCount = updatedVehicles.length;\n      \n      console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);\n      \n      // 更新车辆统计数据\n      setStats(prevStats => ({\n        ...prevStats,\n        totalVehicles: totalCount,\n        onlineVehicles: onlineCount,\n        offlineVehicles: totalCount - onlineCount\n      }));\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n    }\n  }, [onlineBsmIds]);\n\n  // 获取设备统计数据\n  const fetchDeviceStats = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      \n      if (response.data && response.data.success) {\n        const devicesData = response.data.data;\n        \n        // 更新设备统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalDevices: devicesData.length,\n          onlineDevices: devicesData.filter(d => d.status === 'online').length,\n          offlineDevices: devicesData.filter(d => d.status === 'offline').length\n        }));\n      }\n    } catch (error) {\n      console.error('获取设备统计数据失败:', error);\n    }\n  };\n\n  // 监听 BSM 消息\n  useEffect(() => {\n    const handleBsmMessage = (event) => {\n      if (event.data && event.data.type === 'bsm') {\n        // 获取bsmId，确保它正确地从消息中提取\n        const bsmData = event.data.data || {};\n        const bsmId = bsmData.bsmId || event.data.bsmId;\n        \n        if (!bsmId) {\n          console.error('BSM消息缺少bsmId:', event.data);\n          return;\n        }\n        \n        // console.log('收到BSM消息，ID:', bsmId);\n        const now = Date.now();\n        \n        // 更新最后接收时间\n        lastBsmTime.current[bsmId] = now;\n        \n        // 添加到在线bsmId集合\n        setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n        \n        // 提取正确的BSM数据并格式化为两位小数\n        const speed = parseFloat((parseFloat(bsmData.partSpeed || event.data.speed || 0) * 3.6).toFixed(0)); // 转换为km/h，保留两位小数\n        const lat = parseFloat(parseFloat(bsmData.partLat || event.data.lat || 0).toFixed(7));\n        const lng = parseFloat(parseFloat(bsmData.partLong || event.data.lng || 0).toFixed(7));\n        const heading = parseFloat(parseFloat(bsmData.partHeading || event.data.heading || 0).toFixed(2)); // 保留两位小数\n        \n        // console.log(`车辆${bsmId}状态: 速度=${speed.toFixed(2)}km/h, 位置=(${lat.toFixed(7)}, ${lng.toFixed(7)}), 航向=${heading.toFixed(2)}°`);\n        \n        // 更新车辆状态和位置信息\n        setVehicles(prevVehicles => {\n          // 检查是否找到对应车辆\n          const foundVehicle = prevVehicles.find(v => v.bsmId === bsmId);\n          if (!foundVehicle) {\n            console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);\n            return prevVehicles;\n          }\n          \n          const updatedVehicles = prevVehicles.map(vehicle => \n            vehicle.bsmId === bsmId \n              ? { \n                  ...vehicle, \n                  status: 'online',\n                  speed: speed,\n                  lat: lat,\n                  lng: lng,\n                  heading: heading\n                } \n              : vehicle\n          );\n          \n          // 计算更新后的在线车辆数量\n          const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n          const totalCount = updatedVehicles.length;\n          \n          // 更新统计数据\n          setStats(prevStats => ({\n            ...prevStats,\n            onlineVehicles: onlineCount,\n            offlineVehicles: totalCount - onlineCount\n          }));\n          \n          return updatedVehicles;\n        });\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleBsmMessage);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleBsmMessage);\n    };\n  }, []);\n\n  // 修改检查在线状态的useEffect，同步更新统计信息\n  useEffect(() => {\n    const checkOnlineStatus = () => {\n      const now = Date.now();\n      console.log('检查车辆在线状态...');\n      \n      // 只有在特定条件下才将车辆设为离线\n      // 例如，只有收到过BSM消息的车辆才会被检查是否超时\n      setOnlineBsmIds(prev => {\n        const newOnlineBsmIds = new Set(prev);\n        let hasChanges = false;\n        \n        // 仅检查已有最后更新时间的车辆\n        prev.forEach(bsmId => {\n          const lastTime = lastBsmTime.current[bsmId];\n          \n          // 只有收到过BSM消息的车辆才会被检查是否超时\n          if (lastTime && (now - lastTime > 30000)) {\n            console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);\n            newOnlineBsmIds.delete(bsmId);\n            hasChanges = true;\n          }\n        });\n        \n        if (hasChanges) {\n          // 更新车辆状态\n          setVehicles(prevVehicles => {\n            const updatedVehicles = prevVehicles.map(vehicle => {\n              // 只更新有最后更新时间的车辆状态\n              if (lastBsmTime.current[vehicle.bsmId]) {\n                const isOnline = newOnlineBsmIds.has(vehicle.bsmId);\n                return {\n                  ...vehicle,\n                  status: isOnline ? 'online' : 'offline'\n                };\n              }\n              return vehicle;\n            });\n            \n            // 计算更新后的在线车辆数量\n            const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n            const totalCount = updatedVehicles.length;\n            \n            // 更新统计数据\n            setStats(prevStats => ({\n              ...prevStats,\n              onlineVehicles: onlineCount,\n              offlineVehicles: totalCount - onlineCount\n            }));\n            \n            return updatedVehicles;\n          });\n        }\n        \n        return newOnlineBsmIds;\n      });\n    };\n\n    const interval = setInterval(checkOnlineStatus, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // 重置所有车辆的初始状态\n  useEffect(() => {\n    // 将所有车辆状态重置为离线\n    const resetAllVehicles = () => {\n      // 只有在没有任何BSM消息的情况下才执行重置\n      if (onlineBsmIds.size === 0) {\n        setVehicles(prevVehicles => \n          prevVehicles.map(vehicle => ({\n            ...vehicle, \n            status: 'offline',\n            speed: 0,\n            lat: 0,\n            lng: 0,\n            heading: 0\n          }))\n        );\n        \n        console.log('已重置所有车辆为离线状态');\n      }\n    };\n    \n    // 初始执行一次\n    resetAllVehicles();\n    \n    // 然后每30秒检查一次\n    const interval = setInterval(resetAllVehicles, 30000);\n    \n    return () => clearInterval(interval);\n  }, [onlineBsmIds]);\n\n  // 在组件挂载时获取数据\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      fetchVehicles();\n      await fetchDeviceStats();\n      setLoading(false);\n    };\n    \n    loadData();\n    // // 降低更新频率，避免频繁覆盖状态\n    // const interval = setInterval(loadData, 300000); // 每60x5秒更新一次\n    // return () => clearInterval(interval);\n  }, []);\n\n  // 添加对车辆数据变更的监听\n  useEffect(() => {\n    console.log('设置车辆数据变更监听');\n    \n    // 监听自定义事件\n    const handleVehiclesDataChanged = () => {\n      console.log('检测到车辆数据变更事件，重新获取车辆列表');\n      fetchVehicles();\n    };\n    \n    // 监听localStorage变化\n    const handleStorageChange = (event) => {\n      if (event.key === 'vehiclesLastUpdated' || event.key === 'vehiclesData') {\n        console.log('检测到localStorage变化，重新获取车辆列表');\n        fetchVehicles();\n      }\n    };\n    \n    // 添加事件监听器\n    window.addEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n    window.addEventListener('storage', handleStorageChange);\n    \n    // 初始检查是否有更新\n    const lastUpdated = localStorage.getItem('vehiclesLastUpdated');\n    if (lastUpdated) {\n      console.log('初始检查到vehiclesLastUpdated:', lastUpdated);\n      fetchVehicles();\n    }\n    \n    // 设置更频繁的强制轮询，确保在部署环境中也能获取最新数据\n    const forcedPollingInterval = setInterval(() => {\n      console.log('强制轮询：重新获取车辆列表');\n      fetchVehicles();\n    }, 10000); // 每10秒强制刷新一次\n    \n    // 清理函数\n    return () => {\n      window.removeEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n      window.removeEventListener('storage', handleStorageChange);\n      clearInterval(forcedPollingInterval);\n    };\n  }, []);\n\n  // 添加检查本地缓存与API数据是否一致的机制\n  // 定义一个单独的API调用函数，用于获取最新的车辆列表数据\n  const fetchLatestVehiclesData = async (returnData = false) => {\n    try {\n      // 尝试多个可能的API地址\n      const possibleApiUrls = [\n        process.env.REACT_APP_API_URL || 'http://localhost:5000'\n        // 'http://localhost:5000',\n        // window.location.origin, // 当前站点的根URL\n        // `${window.location.origin}/api`, // 当前站点下的/api路径\n        // 'http://localhost:5000/api',\n        // 'http://127.0.0.1:5000',\n        // 'http://127.0.0.1:5000/api'\n      ];\n      \n      console.log('尝试从多个API地址获取车辆数据');\n      \n      // // 尝试从本地JSON文件直接获取\n      // try {\n      //   console.log('尝试从本地JSON文件获取数据');\n      //   const jsonResponse = await axios.get('/vehicles.json');\n      //   if (jsonResponse.data && jsonResponse.data.vehicles) {\n      //     console.log('成功从本地JSON文件获取数据:', jsonResponse.data.vehicles.length);\n      //     if (returnData) {\n      //       return jsonResponse.data.vehicles;\n      //     }\n      //     processVehiclesData(jsonResponse.data.vehicles);\n      //     return jsonResponse.data.vehicles;\n      //   }\n      // } catch (jsonError) {\n      //   console.log('从本地JSON获取失败:', jsonError.message);\n      // }\n      \n      // 逐个尝试API地址\n      let succeeded = false;\n      let vehiclesData = null;\n      \n      for (const apiUrl of possibleApiUrls) {\n        if (succeeded) break;\n        \n        try {\n          console.log(`尝试从 ${apiUrl}/api/vehicles/list 获取数据`);\n          const response = await axios.get(`${apiUrl}/api/vehicles/list`);\n          \n          if (response.data && response.data.vehicles) {\n            console.log(`成功从 ${apiUrl} 获取数据:`, response.data.vehicles.length);\n            vehiclesData = response.data.vehicles;\n            \n            if (returnData) {\n              return vehiclesData;\n            }\n            \n            processVehiclesData(response.data.vehicles);\n            succeeded = true;\n            break;\n          }\n        } catch (error) {\n          console.log(`从 ${apiUrl} 获取失败:`, error.message);\n          // 继续尝试下一个URL\n        }\n      }\n      \n      if (!succeeded && !returnData) {\n        console.log('所有API地址都获取失败，尝试使用vehicles.json');\n        // 尝试从当前页面获取\n        try {\n          const response = await fetch('/vehicles.json');\n          if (response.ok) {\n            const data = await response.json();\n            if (data && data.vehicles) {\n              console.log('从public/vehicles.json获取数据成功:', data.vehicles.length);\n              processVehiclesData(data.vehicles);\n              return data.vehicles;\n            }\n          }\n        } catch (e) {\n          console.error('从public/vehicles.json获取失败:', e);\n        }\n      }\n      \n      return vehiclesData || [];\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n      return [];\n    }\n  };\n\n  // 添加处理车辆数据的辅助函数\n  const processVehiclesData = (newVehicles) => {\n    // 与当前列表比较\n    if (vehicles.length !== newVehicles.length) {\n      console.log('检测到车辆数量变化，从', vehicles.length, '到', newVehicles.length);\n      fetchVehicles(); // 重新加载\n      return;\n    }\n    \n    // 检查是否有新车辆ID\n    const currentIds = new Set(vehicles.map(v => v.id));\n    const hasNewVehicle = newVehicles.some(v => !currentIds.has(v.id));\n    \n    if (hasNewVehicle) {\n      console.log('检测到新增车辆');\n      fetchVehicles(); // 重新加载\n    }\n  };\n\n  // 添加自动选择第一个车辆的逻辑\n  useEffect(() => {\n    // 当车辆列表加载完成且有车辆数据时\n    if (vehicles.length > 0 && !selectedVehicle) {\n      // 自动选择第一个车辆\n      setSelectedVehicle(vehicles[0]);\n      console.log('已自动选择第一个车辆:', vehicles[0].plateNumber);\n    }\n  }, [vehicles, selectedVehicle]);\n  \n  // 修改图表初始化代码\n  useEffect(() => {\n    let chart = null;\n    let handleResize = null;\n    let lastUpdateTime = new Date();\n\n    // 确保 DOM 元素存在\n    if (!eventChartRef.current) {\n      console.error('图表容器未找到');\n      return;\n    }\n\n    // 检查并清理已存在的图表实例\n    let existingChart = echarts.getInstanceByDom(eventChartRef.current);\n    if (existingChart) {\n      existingChart.dispose();\n    }\n\n    try {\n      // 初始化新的图表实例\n      chart = echarts.init(eventChartRef.current);\n      \n      // 设置图表配置\n      const updateChart = () => {\n        const currentTime = new Date();\n        lastUpdateTime = currentTime;\n\n        // 事件类型配置\n        const eventTypes = [\n          { type: '401', name: '道路抛洒物', color: '#ff4d4f' },\n          { type: '404', name: '道路障碍物', color: '#faad14' },\n          { type: '405', name: '行人通过马路', color: '#1890ff' },\n          { type: '904', name: '逆行车辆', color: '#f5222d' },\n          { type: '910', name: '违停车辆', color: '#722ed1' },\n          { type: '1002', name: '道路施工', color: '#fa8c16' },\n          { type: '901', name: '车辆超速', color: '#eb2f96' }\n        ];\n\n        // 处理数据\n        const data = eventTypes\n          .map(event => ({\n            value: eventStats[event.type] || 0,\n            name: event.name,\n            itemStyle: { color: event.color }\n          }))\n          .filter(item => item.value > 0)\n          .sort((a, b) => b.value - a.value);\n\n        const option = {\n          title: {\n            text: `最后更新: ${currentTime.toLocaleTimeString()}`,\n            left: 'center',\n            top: -5,\n            textStyle: {\n              fontSize: 12,\n              color: '#999'\n            }\n          },\n          grid: {\n            top: 30,\n            bottom: 0,\n            left: 0,\n            right: 50,\n            containLabel: true\n          },\n          animation: true,\n          animationDuration: 0,\n          animationDurationUpdate: 1000,\n          animationEasingUpdate: 'quinticInOut',\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'shadow'\n            }\n          },\n          xAxis: {\n            type: 'value',\n            show: false,\n            splitLine: { show: false }\n          },\n          yAxis: {\n            type: 'category',\n            data: data.map(item => item.name),\n            axisLabel: {\n              fontSize: 12,\n              color: '#666',\n              margin: 8\n            },\n            axisTick: { show: false },\n            axisLine: { show: false }\n          },\n          series: [{\n            type: 'bar',\n            data: data,\n            barWidth: '50%',\n            label: {\n              show: true,\n              position: 'right',\n              formatter: '{c}次',\n              fontSize: 12,\n              color: '#666'\n            },\n            itemStyle: {\n              borderRadius: [0, 4, 4, 0]\n            },\n            realtimeSort: false,\n            animationDelay: function (idx) {\n              return idx * 100;\n            }\n          }]\n        };\n\n        // 使用 notMerge: false 来保持增量更新\n        chart.setOption(option, {\n          notMerge: false,\n          replaceMerge: ['series']\n        });\n      };\n\n      // 初始更新\n      updateChart();\n      \n      // 监听事件统计变化，每分钟更新一次\n      const statsInterval = setInterval(updateChart, 60000); // 60000ms = 1分钟\n      \n      // 监听窗口大小变化\n      handleResize = () => {\n        chart?.resize();\n      };\n      window.addEventListener('resize', handleResize);\n\n      // 清理函数\n      return () => {\n        clearInterval(statsInterval);\n        if (handleResize) {\n          window.removeEventListener('resize', handleResize);\n        }\n        if (chart) {\n          chart.dispose();\n        }\n      };\n\n    } catch (error) {\n      console.error('初始化图表失败:', error);\n    }\n  }, [eventStats]);\n  \n  // 修改 RSI 消息处理逻辑\n  useEffect(() => {\n    const handleRsiMessage = (event) => {\n      try {\n        if (event.data && event.data.type === 'RSI') {\n          const rsiData = event.data.data;\n          if (!rsiData || !rsiData.rtes) return;\n          if(rsiData.rtes.length > 0){\n            console.log('RealTimeTraffic 收到 RSI 消息:', event.data);\n          }\n\n          const latitude = parseFloat(rsiData.posLat);\n          const longitude = parseFloat(rsiData.posLong);\n          const rsuId = rsiData.rsuId;\n          const mac = event.data.mac || '';\n          const timestamp = event.data.tm || Date.now();\n\n          // 统计本帧所有非重复事件类型\n          const nonDuplicateEventTypes = [];\n          rsiData.rtes.forEach(event => {\n            const eventType = event.eventType;\n            const eventKey = `${rsuId}_${mac}_${eventType}`;\n            const isDuplicate = checkDuplicateEvent(\n              eventKey,\n              timestamp,\n              { lat: latitude, lng: longitude },\n              prevRsiEvents.current\n            );\n            // 获取事件类型的中文描述\n            let eventTypeText = '';\n            let eventColor = '';\n            switch(eventType) {\n              case '401': eventTypeText = '道路抛洒物'; eventColor = '#ff4d4f'; break;\n              case '404': eventTypeText = '道路障碍物'; eventColor = '#faad14'; break;\n              case '405': eventTypeText = '行人通过马路'; eventColor = '#1890ff'; break;\n              case '904': eventTypeText = '逆行车辆'; eventColor = '#f5222d'; break;\n              case '910': eventTypeText = '违停车辆'; eventColor = '#722ed1'; break;\n              case '1002': eventTypeText = '道路施工'; eventColor = '#fa8c16'; break;\n              case '901': eventTypeText = '车辆超速'; eventColor = '#eb2f96'; break;\n              default: eventTypeText = event.description || '未知事件'; eventColor = '#8c8c8c';\n            }\n            // 更新事件列表\n            // const newEvent = {\n            //   key: Date.now() + Math.random(),\n            //   type: eventTypeText,\n            //   time: new Date().toLocaleTimeString(),\n            //   vehicle: rsiData.rsuId || '未知设备',\n            //   color: eventColor,\n            //   eventType: eventType,\n            //   location: {\n            //     latitude: latitude,\n            //     longitude: longitude\n            //   }\n\n            // };\n            // setEvents(prev => {\n            //   const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n            //   return newEvents;\n            // });\n            if (!isDuplicate) {\n              // 更新事件列表\n              const newEvent = {\n                key: Date.now() + Math.random(),\n                type: eventTypeText,\n                time: new Date().toLocaleTimeString(),\n                vehicle: rsiData.rsuId || '未知设备',\n                color: eventColor,\n                eventType: eventType,\n                location: {\n                  latitude: latitude,\n                  longitude: longitude\n                }\n\n              };\n              setEvents(prev => {\n                const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n                return newEvents;\n              });\n              \n              nonDuplicateEventTypes.push(eventType);\n            } else {\n              console.log(`检测到重复事件，不累计统计: ${eventTypeText}`);\n            }\n          });\n\n          // 只累加一次\n          if (nonDuplicateEventTypes.length > 0) {\n            setEventStats(prev => {\n              const newStats = { ...prev };\n              nonDuplicateEventTypes.forEach(eventType => {\n                newStats[eventType] = (newStats[eventType] || 0) + 1;\n              });\n              return newStats;\n            });\n          }\n        }\n      } catch (error) {\n        console.error('处理 RSI 消息失败:', error);\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleRsiMessage);\n\n    return () => {\n      window.removeEventListener('message', handleRsiMessage);\n    };\n  }, []);\n  \n  // 在组件中添加检查重复事件的辅助函数\n  const checkDuplicateEvent = (eventKey, currentTime, currentPos, prevEvents) => {\n    // 检查是否有相同事件的历史记录\n    if (prevEvents.has(eventKey)) {\n      const prevEvent = prevEvents.get(eventKey);\n      const timeDiff = currentTime - prevEvent.time;\n      \n      // 如果时间差小于200ms\n      if (timeDiff < 3000) {\n        // 计算两个位置之间的距离\n        const distance = calculateDistance(\n          currentPos.lat, currentPos.lng,\n          prevEvent.pos.lat, prevEvent.pos.lng\n        );\n        \n        // 如果距离小于5米，认为是同一个事件\n        if (distance < 5) {\n          // 更新时间戳，但保持为重复事件\n          prevEvents.set(eventKey, {\n            time: currentTime,\n            pos: currentPos\n          });\n          return true;\n        }\n      }\n    }\n    \n    // 保存当前事件信息用于后续比较\n    prevEvents.set(eventKey, {\n      time: currentTime,\n      pos: currentPos\n    });\n    \n    // 不是重复事件\n    return false;\n  };\n\n  // 添加计算两点之间距离的函数\n  const calculateDistance = (lat1, lon1, lat2, lon2) => {\n    // 如果任何坐标为0，表示坐标无效，返回大距离以避免被视为相同事件\n    if (lat1 === 0 || lon1 === 0 || lat2 === 0 || lon2 === 0) {\n      return 999;\n    }\n    \n    // 计算两点之间的距离（哈弗辛公式）\n    const R = 6371000; // 地球半径，单位：米\n    const dLat = (lat2 - lat1) * Math.PI / 180;\n    const dLon = (lon2 - lon1) * Math.PI / 180;\n    const a = \n      Math.sin(dLat/2) * Math.sin(dLat/2) +\n      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * \n      Math.sin(dLon/2) * Math.sin(dLon/2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n    const distance = R * c;\n    \n    return distance;\n  };\n  \n  // 处理车辆选择\n  const handleVehicleSelect = (vehicle) => {\n    console.log('选择车辆:', vehicle.plateNumber, '状态:', vehicle.status);\n    setSelectedVehicle(vehicle);\n  };\n  \n  // 修改车辆状态更新逻辑，确保同步更新selectedVehicle\n  useEffect(() => {\n    // 当车辆列表更新后，如果当前有选中的车辆，则更新选中的车辆信息\n    if (selectedVehicle) {\n      const updatedSelectedVehicle = vehicles.find(v => v.id === selectedVehicle.id);\n      if (updatedSelectedVehicle && \n          (updatedSelectedVehicle.status !== selectedVehicle.status || \n           updatedSelectedVehicle.speed !== selectedVehicle.speed ||\n           updatedSelectedVehicle.lat !== selectedVehicle.lat ||\n           updatedSelectedVehicle.lng !== selectedVehicle.lng ||\n           updatedSelectedVehicle.heading !== selectedVehicle.heading)) {\n        console.log(`更新选中车辆 ${selectedVehicle.plateNumber} 的信息:`, \n                   `状态从 ${selectedVehicle.status} 变为 ${updatedSelectedVehicle.status}`);\n        setSelectedVehicle(updatedSelectedVehicle);\n      }\n    }\n  }, [vehicles, selectedVehicle]);\n  \n  // 车辆列表列定义\n  const vehicleColumns = [\n    {\n      title: '车牌号',\n      dataIndex: 'plate',\n      key: 'plate',\n      width: '40%',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: '30%',\n      render: status => (\n        <Badge \n          status={status === 'online' ? 'success' : 'error'} \n          text={status === 'online' ? '在线' : '离线'} \n        />\n      ),\n    },\n    {\n      title: '速度',\n      dataIndex: 'speed',\n      key: 'speed',\n      width: '30%',\n      render: speed => `${typeof speed === 'number' ? speed.toFixed(0) : speed} km/h`,\n    }\n  ];\n  \n  // 修改实时事件列表的渲染\n  const renderEventList = () => (\n    <List\n      size=\"small\"\n      dataSource={events}\n      renderItem={item => (\n        <List.Item style={{ padding: '8px 0' }}>\n          <List.Item.Meta\n            title={\n              <div style={{ fontSize: '13px', marginBottom: '4px' }}>\n                <span style={{ color: item.color, marginRight: '8px' }}>\n                  {item.type}\n                </span>\n                <span style={{ color: '#666', fontSize: '12px' }}>\n                  {item.time}\n                </span>\n              </div>\n            }\n            description={\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                <div>设备: {item.vehicle}</div>\n                <div>位置: {item.location ? \n                  `${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}` : \n                  '未知位置'}\n                </div>\n              </div>\n            }\n          />\n        </List.Item>\n      )}\n      style={{\n        maxHeight: 'calc(100% - 24px)',\n        overflowY: 'auto'\n      }}\n    />\n  );\n  \n  // 添加强制定期从API获取最新数据的机制\n  useEffect(() => {\n    const apiPollingInterval = setInterval(() => {\n      console.log('直接从API检查车辆数据更新');\n      fetchLatestVehiclesData();\n    }, 15000); // 每15秒检查一次API\n    \n    return () => clearInterval(apiPollingInterval);\n  }, [vehicles]);\n\n  return (\n    <Spin spinning={loading} tip=\"加载中...\">\n      <PageContainer>\n        {/* 左侧信息栏 */}\n        <CollapsibleSidebar \n          position=\"left\"\n          collapsed={leftCollapsed}\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\n        >\n          {/* 车辆和设备总数信息栏 */}\n          <InfoCard title=\"车辆和设备统计\" bordered={false} height=\"160px\">\n            <Row gutter={[8, 1]} >\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic \n                  title=\"车辆总数\" \n                  value={stats.totalVehicles} \n                  valueStyle={{ color: '#3f8600' }} \n                  Style={{display: 'flex',justifyContent: 'center'}}\n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"在线车辆\" \n                  value={stats.onlineVehicles} \n                  // suffix={`/ ${stats.totalVehicles}`} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"离线车辆\" \n                  value={stats.offlineVehicles} \n                  // suffix={`/ ${stats.totalVehicles}`} \n                  valueStyle={{ color: '#cf1322' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"设备总数\" \n                  value={stats.totalDevices} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"在线设备\" \n                  value={stats.onlineDevices} \n                  // suffix={`/ ${stats.totalDevices}`} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"离线设备\" \n                  value={stats.offlineDevices} \n                  // suffix={`/ ${stats.totalDevices}`} \n                  valueStyle={{ color: '#cf1322' }} \n                />\n              </Col>\n            </Row>\n          </InfoCard>\n          \n          {/* 实时事件列表栏 */}\n          <InfoCard title=\"实时事件列表\" bordered={false} height=\"calc(50% - 81px)\">\n            {renderEventList()}\n          </InfoCard>\n          \n          {/* 实时事件统计栏 */}\n          <InfoCard title=\"实时事件统计\" bordered={false} height=\"calc(50% - 81px)\">\n            <div ref={eventChartRef} style={{ height: '100%', width: '100%' }}></div>\n          </InfoCard>\n        </CollapsibleSidebar>\n        \n        {/* 主内容区域 */}\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\n          {/* 主要内容 */}\n        </MainContent>\n        \n        {/* 右侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"right\"\n          collapsed={rightCollapsed}\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\n        >\n          {/* 车辆列表栏 */}\n          <InfoCard title=\"车辆列表\" bordered={false} height=\"50%\">\n            <Table \n              dataSource={vehicles} \n              columns={vehicleColumns} \n              rowKey=\"id\"\n              pagination={false}\n              size=\"small\"\n              scroll={{ y: 180 }}\n              onRow={(record) => ({\n                onClick: () => handleVehicleSelect(record),\n                style: { \n                  cursor: 'pointer',\n                  background: selectedVehicle?.id === record.id ? '#e6f7ff' : 'transparent',\n                  fontSize: '13px',\n                  padding: '4px 8px'\n                }\n              })}\n            />\n          </InfoCard>\n          \n          {/* 车辆详细信息栏 */}\n          <InfoCard title=\"车辆详细信息\" bordered={false} height=\"50%\">\n            {selectedVehicle ? (\n              <Descriptions \n                bordered \n                column={1} \n                size=\"small\"\n                styles={{\n                  label: { fontSize: '13px', padding: '4px 8px' },\n                  content: { fontSize: '13px', padding: '4px 8px' }\n                }}\n              >\n                <Descriptions.Item label=\"车牌号\">{selectedVehicle.plateNumber}</Descriptions.Item>\n                <Descriptions.Item label=\"状态\">\n                  <Badge \n                    status={selectedVehicle.status === 'online' ? 'success' : 'error'} \n                    text={selectedVehicle.status === 'online' ? '在线' : '离线'} \n                  />\n                </Descriptions.Item>\n                <Descriptions.Item label=\"经度\">\n                  {selectedVehicle.status === 'online' ? selectedVehicle.lng.toFixed(7) : 'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"纬度\">\n                  {selectedVehicle.status === 'online' ? selectedVehicle.lat.toFixed(7) : 'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"速度\">\n                  {selectedVehicle.status === 'online' ? \n                    `${typeof selectedVehicle.speed === 'number' ? selectedVehicle.speed.toFixed(0) : selectedVehicle.speed} km/h` : \n                    'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"航向角\">\n                  {selectedVehicle.status === 'online' ? \n                    `${typeof selectedVehicle.heading === 'number' ? selectedVehicle.heading.toFixed(2) : selectedVehicle.heading}°` : \n                    'N/A'}\n                </Descriptions.Item>\n              </Descriptions>\n            ) : (\n              <p style={{ fontSize: '13px' }}>请选择车辆查看详细信息</p>\n            )}\n          </InfoCard>\n        </CollapsibleSidebar>\n      </PageContainer>\n    </Spin>\n  );\n};\n\nexport default RealTimeTraffic;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AACxF,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,QAAQ,oBAAoB;AACrG,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,yCAAyC;AACxE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,uBAAuB;;AAEhD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAZ,OAAO,CAACa,GAAG,CAAC,CAACZ,QAAQ,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,cAAc,CAAC,CAAC;AAEzG,MAAMQ,YAAY,GAAGP,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMC,aAAa,GAAGT,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA,CAAC;;AAED;AAAAE,EAAA,GANMD,aAAa;AAOnB,MAAME,WAAW,GAAGX,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMI,YAAY,GAAGZ,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMK,WAAW,GAAGb,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA,aAAaM,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACxEF,KAAK,CAACC,aAAa,GAAG,cAAc,GACpCD,KAAK,CAACE,cAAc,GAAG,cAAc,GAAG,OAAO;AACnD;AACA,YAAYF,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACvEF,KAAK,CAACC,aAAa,GAAG,WAAW,GACjCD,KAAK,CAACE,cAAc,GAAG,WAAW,GAAG,GAAG;AAC5C;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfMJ,WAAW;AAgBjB,MAAMK,QAAQ,GAAGlB,MAAM,CAACd,IAAI,CAAC;AAC7B;AACA,YAAY4B,KAAK,IAAIA,KAAK,CAACK,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GA1BMF,QAAQ;AA2Bd,MAAMG,gBAAgB,GAAGrB,MAAM,CAACb,SAAS,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,GAAA,GATID,gBAAgB;AAWtB,MAAME,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiD,MAAM,EAAEC,SAAS,CAAC,GAAGlD,QAAQ,CAAC,MAAM;IACzC,IAAI;MACF,MAAMmD,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;MACjE,OAAOF,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,GAAG,EAAE;IACnD,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAM,CAACE,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC4D,KAAK,EAAEC,QAAQ,CAAC,GAAG7D,QAAQ,CAAC;IACjC8D,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAC,MAAM;IACrD,IAAI;MACF,MAAMsE,cAAc,GAAGlB,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC;MACvE;MACA,OAAO,IAAIkB,GAAG,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,OAAO,IAAIe,GAAG,CAAC,CAAC;IAClB;EACF,CAAC,CAAC;EACF;EACA,MAAMC,WAAW,GAAGtE,MAAM,CAAC,CAAC,CAAC,CAAC;EAE9B,MAAMuE,aAAa,GAAGvE,MAAM,CAAC,IAAI,CAAC;;EAElC;EACA,MAAM,CAACiC,aAAa,EAAEuC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoC,cAAc,EAAEuC,iBAAiB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM,CAAC4E,UAAU,EAAEC,aAAa,CAAC,GAAG7E,QAAQ,CAAC,MAAM;IACjD,IAAI;MACF,MAAM8E,UAAU,GAAG1B,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC;MACpE,OAAOyB,UAAU,GAAGxB,IAAI,CAACC,KAAK,CAACuB,UAAU,CAAC,GAAG;QAC3C,KAAK,EAAE,CAAC;QAAG;QACX,KAAK,EAAE,CAAC;QAAG;QACX,KAAK,EAAE,CAAC;QAAG;QACX,KAAK,EAAE,CAAC;QAAG;QACX,KAAK,EAAE,CAAC;QAAG;QACX,MAAM,EAAE,CAAC;QAAE;QACX,KAAK,EAAE,CAAC,CAAG;MACb,CAAC;IACH,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,OAAO;QACL,KAAK,EAAE,CAAC;QAAE,KAAK,EAAE,CAAC;QAAE,KAAK,EAAE,CAAC;QAAE,KAAK,EAAE,CAAC;QACtC,KAAK,EAAE,CAAC;QAAE,MAAM,EAAE,CAAC;QAAE,KAAK,EAAE;MAC9B,CAAC;IACH;EACF,CAAC,CAAC;;EAEF;EACA,MAAMuB,aAAa,GAAG7E,MAAM,CAAC,IAAI8E,GAAG,CAAC,CAAC,CAAC;;EAEvC;EACA,MAAMC,mBAAmB,GAAG9E,WAAW,CAAC,CAAC+E,KAAK,EAAEC,MAAM,EAAEC,KAAK,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,EAAEC,OAAO,GAAG,CAAC,KAAK;IACnG;IACA,MAAMC,cAAc,GAAGC,UAAU,CAACL,KAAK,CAAC,CAACM,OAAO,CAAC,CAAC,CAAC;IACnD,MAAMC,gBAAgB,GAAGF,UAAU,CAACF,OAAO,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC;IAEvDjC,OAAO,CAACmC,GAAG,CAAC,gBAAgBV,KAAK,QAAQC,MAAM,QAAQK,cAAc,SAASH,GAAG,KAAKC,GAAG,GAAG,CAAC;;IAE7F;IACA,IAAIH,MAAM,KAAK,QAAQ,EAAE;MACvBd,eAAe,CAACwB,IAAI,IAAI,IAAItB,GAAG,CAAC,CAAC,GAAGsB,IAAI,EAAEX,KAAK,CAAC,CAAC,CAAC;MAClDV,WAAW,CAACsB,OAAO,CAACZ,KAAK,CAAC,GAAGa,IAAI,CAACC,GAAG,CAAC,CAAC;IACzC,CAAC,MAAM;MACL3B,eAAe,CAACwB,IAAI,IAAI;QACtB,MAAMI,MAAM,GAAG,IAAI1B,GAAG,CAACsB,IAAI,CAAC;QAC5BI,MAAM,CAACC,MAAM,CAAChB,KAAK,CAAC;QACpB,OAAOe,MAAM;MACf,CAAC,CAAC;IACJ;;IAEA;IACAjD,WAAW,CAACmD,YAAY,IACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,IACtBA,OAAO,CAACnB,KAAK,KAAKA,KAAK,GACnB;MACE,GAAGmB,OAAO;MACVlB,MAAM,EAAEA,MAAM;MACdC,KAAK,EAAEK,UAAU,CAACD,cAAc,CAAC;MAAE;MACnCH,GAAG,EAAEI,UAAU,CAACJ,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC;MAC/BJ,GAAG,EAAEG,UAAU,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC;MAC/BH,OAAO,EAAEE,UAAU,CAACE,gBAAgB,CAAC,CAAC;IACxC,CAAC,GACDU,OACN,CACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApG,SAAS,CAAC,MAAM;IACdqG,MAAM,CAACrB,mBAAmB,GAAGA,mBAAmB;;IAEhD;IACA,MAAMsB,qBAAqB,GAAIC,KAAK,IAAK;MACvC,IAAIA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACC,IAAI,KAAK,iBAAiB,EAAE;QACvD;MAAA;IAEJ,CAAC;IAEDJ,MAAM,CAACK,gBAAgB,CAAC,SAAS,EAAEJ,qBAAqB,CAAC;;IAEzD;IACA9C,OAAO,CAACmC,GAAG,CAAC,mBAAmB,CAAC;IAChCvB,eAAe,CAAC,IAAIE,GAAG,CAAC,CAAC,CAAC;IAC1BC,WAAW,CAACsB,OAAO,GAAG,CAAC,CAAC;IAExB,OAAO,MAAM;MACXQ,MAAM,CAACM,mBAAmB,CAAC,SAAS,EAAEL,qBAAqB,CAAC;MAC5D,OAAOD,MAAM,CAACrB,mBAAmB;IACnC,CAAC;EACH,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACAhF,SAAS,CAAC,MAAM;IACd,MAAM4G,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjC,IAAI;QACF1D,YAAY,CAAC2D,OAAO,CAAC,uBAAuB,EAAEzD,IAAI,CAAC0D,SAAS,CAAC/D,MAAM,CAAC,CAAC;MACvE,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMyD,YAAY,CAACJ,SAAS,CAAC;EACtC,CAAC,EAAE,CAAC5D,MAAM,CAAC,CAAC;;EAEZ;EACAhD,SAAS,CAAC,MAAM;IACd,MAAM4G,SAAS,GAAGC,UAAU,CAAC,MAAM;MACjC,IAAI;QACF1D,YAAY,CAAC2D,OAAO,CAAC,2BAA2B,EAAEzD,IAAI,CAAC0D,SAAS,CAACpC,UAAU,CAAC,CAAC;MAC/E,CAAC,CAAC,OAAOpB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACrC;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMyD,YAAY,CAACJ,SAAS,CAAC;EACtC,CAAC,EAAE,CAACjC,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMsC,aAAa,GAAG/G,WAAW,CAAC,YAAY;IAC5C,IAAI;MACF;MACAsD,OAAO,CAACmC,GAAG,CAAC,gBAAgB,CAAC;MAC7B,MAAMuB,OAAO,GAAG,MAAMC,uBAAuB,CAAC,IAAI,CAAC;;MAEnD;MACA,IAAID,OAAO,IAAIA,OAAO,CAACE,MAAM,GAAG,CAAC,EAAE;QACjC5D,OAAO,CAACmC,GAAG,CAAC,oBAAoB,EAAEuB,OAAO,CAACE,MAAM,CAAC;;QAEjD;QACA,MAAMC,MAAM,GAAGH,OAAO,CAACf,GAAG,CAACmB,CAAC,IAAIA,CAAC,CAACrC,KAAK,CAAC,CAACsC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAC;QACzDhE,OAAO,CAACmC,GAAG,CAAC,cAAc,EAAE0B,MAAM,CAAC;;QAEnC;QACA,MAAMI,eAAe,GAAGP,OAAO,CAACf,GAAG,CAACC,OAAO,IAAI;UAC7C;UACA,MAAMsB,QAAQ,GAAGtB,OAAO,CAACnB,KAAK,IAAId,YAAY,CAACwD,GAAG,CAACvB,OAAO,CAACnB,KAAK,CAAC;UAEjE,OAAO;YACL,GAAGmB,OAAO;YACVwB,KAAK,EAAExB,OAAO,CAACyB,WAAW;YAAE;YAC5B3C,MAAM,EAAEwC,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAE;YACzCvC,KAAK,EAAEuC,QAAQ,GAAInD,WAAW,CAACsB,OAAO,CAACO,OAAO,CAACnB,KAAK,CAAC,GAAGmB,OAAO,CAACjB,KAAK,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC;YACnFC,GAAG,EAAEsC,QAAQ,GAAInD,WAAW,CAACsB,OAAO,CAACO,OAAO,CAACnB,KAAK,CAAC,GAAGmB,OAAO,CAAChB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC;YAC/EC,GAAG,EAAEqC,QAAQ,GAAInD,WAAW,CAACsB,OAAO,CAACO,OAAO,CAACnB,KAAK,CAAC,GAAGmB,OAAO,CAACf,GAAG,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC;YAC/EC,OAAO,EAAEoC,QAAQ,GAAInD,WAAW,CAACsB,OAAO,CAACO,OAAO,CAACnB,KAAK,CAAC,GAAGmB,OAAO,CAACd,OAAO,IAAI,CAAC,GAAG,CAAC,GAAI;UACxF,CAAC;QACH,CAAC,CAAC;QAEFvC,WAAW,CAAC0E,eAAe,CAAC;;QAE5B;QACA,MAAMK,WAAW,GAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACpC,MAAM,KAAK,QAAQ,CAAC,CAACkC,MAAM;QAC7E,MAAMW,UAAU,GAAGN,eAAe,CAACL,MAAM;QAEzC5D,OAAO,CAACmC,GAAG,CAAC,cAAcoC,UAAU,QAAQD,WAAW,QAAQC,UAAU,GAAGD,WAAW,EAAE,CAAC;;QAE1F;QACAlE,QAAQ,CAACoE,SAAS,KAAK;UACrB,GAAGA,SAAS;UACZnE,aAAa,EAAEkE,UAAU;UACzBjE,cAAc,EAAEgE,WAAW;UAC3B/D,eAAe,EAAEgE,UAAU,GAAGD;QAChC,CAAC,CAAC,CAAC;QAEH;MACF;;MAEA;MACAtE,OAAO,CAACmC,GAAG,CAAC,2BAA2B,CAAC;MACxC,MAAMsC,YAAY,GAAG3G,YAAY,CAACwB,QAAQ,IAAI,EAAE;MAChDU,OAAO,CAACmC,GAAG,CAAC,2BAA2B,EAAEsC,YAAY,CAACb,MAAM,CAAC;;MAE7D;MACA,MAAMK,eAAe,GAAGQ,YAAY,CAAC9B,GAAG,CAACC,OAAO,IAAI;QAClD;QACA,MAAMsB,QAAQ,GAAGtB,OAAO,CAACnB,KAAK,IAAId,YAAY,CAACwD,GAAG,CAACvB,OAAO,CAACnB,KAAK,CAAC;QAEjE,OAAO;UACL,GAAGmB,OAAO;UACVwB,KAAK,EAAExB,OAAO,CAACyB,WAAW;UAAE;UAC5B3C,MAAM,EAAEwC,QAAQ,GAAG,QAAQ,GAAG,SAAS;UAAE;UACzCvC,KAAK,EAAEuC,QAAQ,GAAInD,WAAW,CAACsB,OAAO,CAACO,OAAO,CAACnB,KAAK,CAAC,GAAGmB,OAAO,CAACjB,KAAK,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC;UACnFC,GAAG,EAAEsC,QAAQ,GAAInD,WAAW,CAACsB,OAAO,CAACO,OAAO,CAACnB,KAAK,CAAC,GAAGmB,OAAO,CAAChB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC;UAC/EC,GAAG,EAAEqC,QAAQ,GAAInD,WAAW,CAACsB,OAAO,CAACO,OAAO,CAACnB,KAAK,CAAC,GAAGmB,OAAO,CAACf,GAAG,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC;UAC/EC,OAAO,EAAEoC,QAAQ,GAAInD,WAAW,CAACsB,OAAO,CAACO,OAAO,CAACnB,KAAK,CAAC,GAAGmB,OAAO,CAACd,OAAO,IAAI,CAAC,GAAG,CAAC,GAAI;QACxF,CAAC;MACH,CAAC,CAAC;MAEFvC,WAAW,CAAC0E,eAAe,CAAC;;MAE5B;MACA,MAAMK,WAAW,GAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACpC,MAAM,KAAK,QAAQ,CAAC,CAACkC,MAAM;MAC7E,MAAMW,UAAU,GAAGN,eAAe,CAACL,MAAM;MAEzC5D,OAAO,CAACmC,GAAG,CAAC,cAAcoC,UAAU,QAAQD,WAAW,QAAQC,UAAU,GAAGD,WAAW,EAAE,CAAC;;MAE1F;MACAlE,QAAQ,CAACoE,SAAS,KAAK;QACrB,GAAGA,SAAS;QACZnE,aAAa,EAAEkE,UAAU;QACzBjE,cAAc,EAAEgE,WAAW;QAC3B/D,eAAe,EAAEgE,UAAU,GAAGD;MAChC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOvE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,CAACY,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM+D,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMlH,KAAK,CAACmH,GAAG,CAAC,GAAGL,MAAM,cAAc,CAAC;MAEzD,IAAII,QAAQ,CAAC/B,IAAI,IAAI+B,QAAQ,CAAC/B,IAAI,CAACiC,OAAO,EAAE;QAC1C,MAAMC,WAAW,GAAGH,QAAQ,CAAC/B,IAAI,CAACA,IAAI;;QAEtC;QACA5C,QAAQ,CAACoE,SAAS,KAAK;UACrB,GAAGA,SAAS;UACZhE,YAAY,EAAE0E,WAAW,CAACtB,MAAM;UAChCnD,aAAa,EAAEyE,WAAW,CAACnB,MAAM,CAACoB,CAAC,IAAIA,CAAC,CAACzD,MAAM,KAAK,QAAQ,CAAC,CAACkC,MAAM;UACpElD,cAAc,EAAEwE,WAAW,CAACnB,MAAM,CAACoB,CAAC,IAAIA,CAAC,CAACzD,MAAM,KAAK,SAAS,CAAC,CAACkC;QAClE,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAO7D,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;;EAED;EACAvD,SAAS,CAAC,MAAM;IACd,MAAM4I,gBAAgB,GAAIrC,KAAK,IAAK;MAClC,IAAIA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;QAC3C;QACA,MAAMoC,OAAO,GAAGtC,KAAK,CAACC,IAAI,CAACA,IAAI,IAAI,CAAC,CAAC;QACrC,MAAMvB,KAAK,GAAG4D,OAAO,CAAC5D,KAAK,IAAIsB,KAAK,CAACC,IAAI,CAACvB,KAAK;QAE/C,IAAI,CAACA,KAAK,EAAE;UACVzB,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEgD,KAAK,CAACC,IAAI,CAAC;UAC1C;QACF;;QAEA;QACA,MAAMT,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;;QAEtB;QACAxB,WAAW,CAACsB,OAAO,CAACZ,KAAK,CAAC,GAAGc,GAAG;;QAEhC;QACA3B,eAAe,CAACwB,IAAI,IAAI,IAAItB,GAAG,CAAC,CAAC,GAAGsB,IAAI,EAAEX,KAAK,CAAC,CAAC,CAAC;;QAElD;QACA,MAAME,KAAK,GAAGK,UAAU,CAAC,CAACA,UAAU,CAACqD,OAAO,CAACC,SAAS,IAAIvC,KAAK,CAACC,IAAI,CAACrB,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrG,MAAML,GAAG,GAAGI,UAAU,CAACA,UAAU,CAACqD,OAAO,CAACE,OAAO,IAAIxC,KAAK,CAACC,IAAI,CAACpB,GAAG,IAAI,CAAC,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC;QACrF,MAAMJ,GAAG,GAAGG,UAAU,CAACA,UAAU,CAACqD,OAAO,CAACG,QAAQ,IAAIzC,KAAK,CAACC,IAAI,CAACnB,GAAG,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC;QACtF,MAAMH,OAAO,GAAGE,UAAU,CAACA,UAAU,CAACqD,OAAO,CAACI,WAAW,IAAI1C,KAAK,CAACC,IAAI,CAAClB,OAAO,IAAI,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnG;;QAEA;QACA1C,WAAW,CAACmD,YAAY,IAAI;UAC1B;UACA,MAAMgD,YAAY,GAAGhD,YAAY,CAACiD,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACrC,KAAK,KAAKA,KAAK,CAAC;UAC9D,IAAI,CAACiE,YAAY,EAAE;YACjB1F,OAAO,CAACmC,GAAG,CAAC,eAAeV,KAAK,WAAW,CAAC;YAC5C,OAAOiB,YAAY;UACrB;UAEA,MAAMuB,eAAe,GAAGvB,YAAY,CAACC,GAAG,CAACC,OAAO,IAC9CA,OAAO,CAACnB,KAAK,KAAKA,KAAK,GACnB;YACE,GAAGmB,OAAO;YACVlB,MAAM,EAAE,QAAQ;YAChBC,KAAK,EAAEA,KAAK;YACZC,GAAG,EAAEA,GAAG;YACRC,GAAG,EAAEA,GAAG;YACRC,OAAO,EAAEA;UACX,CAAC,GACDc,OACN,CAAC;;UAED;UACA,MAAM0B,WAAW,GAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACpC,MAAM,KAAK,QAAQ,CAAC,CAACkC,MAAM;UAC7E,MAAMW,UAAU,GAAGN,eAAe,CAACL,MAAM;;UAEzC;UACAxD,QAAQ,CAACoE,SAAS,KAAK;YACrB,GAAGA,SAAS;YACZlE,cAAc,EAAEgE,WAAW;YAC3B/D,eAAe,EAAEgE,UAAU,GAAGD;UAChC,CAAC,CAAC,CAAC;UAEH,OAAOL,eAAe;QACxB,CAAC,CAAC;MACJ;IACF,CAAC;;IAED;IACApB,MAAM,CAACK,gBAAgB,CAAC,SAAS,EAAEkC,gBAAgB,CAAC;;IAEpD;IACA,OAAO,MAAM;MACXvC,MAAM,CAACM,mBAAmB,CAAC,SAAS,EAAEiC,gBAAgB,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5I,SAAS,CAAC,MAAM;IACd,MAAMoJ,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,MAAMrD,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACtBvC,OAAO,CAACmC,GAAG,CAAC,aAAa,CAAC;;MAE1B;MACA;MACAvB,eAAe,CAACwB,IAAI,IAAI;QACtB,MAAMyD,eAAe,GAAG,IAAI/E,GAAG,CAACsB,IAAI,CAAC;QACrC,IAAI0D,UAAU,GAAG,KAAK;;QAEtB;QACA1D,IAAI,CAAC2D,OAAO,CAACtE,KAAK,IAAI;UACpB,MAAMuE,QAAQ,GAAGjF,WAAW,CAACsB,OAAO,CAACZ,KAAK,CAAC;;UAE3C;UACA,IAAIuE,QAAQ,IAAKzD,GAAG,GAAGyD,QAAQ,GAAG,KAAM,EAAE;YACxChG,OAAO,CAACmC,GAAG,CAAC,KAAKV,KAAK,kBAAkB,CAAC;YACzCoE,eAAe,CAACpD,MAAM,CAAChB,KAAK,CAAC;YAC7BqE,UAAU,GAAG,IAAI;UACnB;QACF,CAAC,CAAC;QAEF,IAAIA,UAAU,EAAE;UACd;UACAvG,WAAW,CAACmD,YAAY,IAAI;YAC1B,MAAMuB,eAAe,GAAGvB,YAAY,CAACC,GAAG,CAACC,OAAO,IAAI;cAClD;cACA,IAAI7B,WAAW,CAACsB,OAAO,CAACO,OAAO,CAACnB,KAAK,CAAC,EAAE;gBACtC,MAAMyC,QAAQ,GAAG2B,eAAe,CAAC1B,GAAG,CAACvB,OAAO,CAACnB,KAAK,CAAC;gBACnD,OAAO;kBACL,GAAGmB,OAAO;kBACVlB,MAAM,EAAEwC,QAAQ,GAAG,QAAQ,GAAG;gBAChC,CAAC;cACH;cACA,OAAOtB,OAAO;YAChB,CAAC,CAAC;;YAEF;YACA,MAAM0B,WAAW,GAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACpC,MAAM,KAAK,QAAQ,CAAC,CAACkC,MAAM;YAC7E,MAAMW,UAAU,GAAGN,eAAe,CAACL,MAAM;;YAEzC;YACAxD,QAAQ,CAACoE,SAAS,KAAK;cACrB,GAAGA,SAAS;cACZlE,cAAc,EAAEgE,WAAW;cAC3B/D,eAAe,EAAEgE,UAAU,GAAGD;YAChC,CAAC,CAAC,CAAC;YAEH,OAAOL,eAAe;UACxB,CAAC,CAAC;QACJ;QAEA,OAAO4B,eAAe;MACxB,CAAC,CAAC;IACJ,CAAC;IAED,MAAMI,QAAQ,GAAGC,WAAW,CAACN,iBAAiB,EAAE,IAAI,CAAC;IACrD,OAAO,MAAMO,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzJ,SAAS,CAAC,MAAM;IACd;IACA,MAAM4J,gBAAgB,GAAGA,CAAA,KAAM;MAC7B;MACA,IAAIzF,YAAY,CAAC0F,IAAI,KAAK,CAAC,EAAE;QAC3B9G,WAAW,CAACmD,YAAY,IACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,KAAK;UAC3B,GAAGA,OAAO;UACVlB,MAAM,EAAE,SAAS;UACjBC,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE,CAAC;UACNC,GAAG,EAAE,CAAC;UACNC,OAAO,EAAE;QACX,CAAC,CAAC,CACJ,CAAC;QAED9B,OAAO,CAACmC,GAAG,CAAC,cAAc,CAAC;MAC7B;IACF,CAAC;;IAED;IACAiE,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMH,QAAQ,GAAGC,WAAW,CAACE,gBAAgB,EAAE,KAAK,CAAC;IAErD,OAAO,MAAMD,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACtF,YAAY,CAAC,CAAC;;EAElB;EACAnE,SAAS,CAAC,MAAM;IACd,MAAM8J,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3BjH,UAAU,CAAC,IAAI,CAAC;MAChBoE,aAAa,CAAC,CAAC;MACf,MAAMiB,gBAAgB,CAAC,CAAC;MACxBrF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDiH,QAAQ,CAAC,CAAC;IACV;IACA;IACA;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9J,SAAS,CAAC,MAAM;IACdwD,OAAO,CAACmC,GAAG,CAAC,YAAY,CAAC;;IAEzB;IACA,MAAMoE,yBAAyB,GAAGA,CAAA,KAAM;MACtCvG,OAAO,CAACmC,GAAG,CAAC,sBAAsB,CAAC;MACnCsB,aAAa,CAAC,CAAC;IACjB,CAAC;;IAED;IACA,MAAM+C,mBAAmB,GAAIzD,KAAK,IAAK;MACrC,IAAIA,KAAK,CAAC0D,GAAG,KAAK,qBAAqB,IAAI1D,KAAK,CAAC0D,GAAG,KAAK,cAAc,EAAE;QACvEzG,OAAO,CAACmC,GAAG,CAAC,4BAA4B,CAAC;QACzCsB,aAAa,CAAC,CAAC;MACjB;IACF,CAAC;;IAED;IACAZ,MAAM,CAACK,gBAAgB,CAAC,qBAAqB,EAAEqD,yBAAyB,CAAC;IACzE1D,MAAM,CAACK,gBAAgB,CAAC,SAAS,EAAEsD,mBAAmB,CAAC;;IAEvD;IACA,MAAME,WAAW,GAAG/G,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC;IAC/D,IAAI8G,WAAW,EAAE;MACf1G,OAAO,CAACmC,GAAG,CAAC,2BAA2B,EAAEuE,WAAW,CAAC;MACrDjD,aAAa,CAAC,CAAC;IACjB;;IAEA;IACA,MAAMkD,qBAAqB,GAAGT,WAAW,CAAC,MAAM;MAC9ClG,OAAO,CAACmC,GAAG,CAAC,eAAe,CAAC;MAC5BsB,aAAa,CAAC,CAAC;IACjB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACXZ,MAAM,CAACM,mBAAmB,CAAC,qBAAqB,EAAEoD,yBAAyB,CAAC;MAC5E1D,MAAM,CAACM,mBAAmB,CAAC,SAAS,EAAEqD,mBAAmB,CAAC;MAC1DL,aAAa,CAACQ,qBAAqB,CAAC;IACtC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA,MAAMhD,uBAAuB,GAAG,MAAAA,CAAOiD,UAAU,GAAG,KAAK,KAAK;IAC5D,IAAI;MACF;MACA,MAAMC,eAAe,GAAG,CACtBjC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI;MACjC;MACA;MACA;MACA;MACA;MACA;MAAA,CACD;MAED9E,OAAO,CAACmC,GAAG,CAAC,kBAAkB,CAAC;;MAE/B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,IAAI2E,SAAS,GAAG,KAAK;MACrB,IAAIhJ,YAAY,GAAG,IAAI;MAEvB,KAAK,MAAM6G,MAAM,IAAIkC,eAAe,EAAE;QACpC,IAAIC,SAAS,EAAE;QAEf,IAAI;UACF9G,OAAO,CAACmC,GAAG,CAAC,OAAOwC,MAAM,yBAAyB,CAAC;UACnD,MAAMI,QAAQ,GAAG,MAAMlH,KAAK,CAACmH,GAAG,CAAC,GAAGL,MAAM,oBAAoB,CAAC;UAE/D,IAAII,QAAQ,CAAC/B,IAAI,IAAI+B,QAAQ,CAAC/B,IAAI,CAAC1D,QAAQ,EAAE;YAC3CU,OAAO,CAACmC,GAAG,CAAC,OAAOwC,MAAM,QAAQ,EAAEI,QAAQ,CAAC/B,IAAI,CAAC1D,QAAQ,CAACsE,MAAM,CAAC;YACjE9F,YAAY,GAAGiH,QAAQ,CAAC/B,IAAI,CAAC1D,QAAQ;YAErC,IAAIsH,UAAU,EAAE;cACd,OAAO9I,YAAY;YACrB;YAEAiJ,mBAAmB,CAAChC,QAAQ,CAAC/B,IAAI,CAAC1D,QAAQ,CAAC;YAC3CwH,SAAS,GAAG,IAAI;YAChB;UACF;QACF,CAAC,CAAC,OAAO/G,KAAK,EAAE;UACdC,OAAO,CAACmC,GAAG,CAAC,KAAKwC,MAAM,QAAQ,EAAE5E,KAAK,CAACiH,OAAO,CAAC;UAC/C;QACF;MACF;MAEA,IAAI,CAACF,SAAS,IAAI,CAACF,UAAU,EAAE;QAC7B5G,OAAO,CAACmC,GAAG,CAAC,gCAAgC,CAAC;QAC7C;QACA,IAAI;UACF,MAAM4C,QAAQ,GAAG,MAAMkC,KAAK,CAAC,gBAAgB,CAAC;UAC9C,IAAIlC,QAAQ,CAACmC,EAAE,EAAE;YACf,MAAMlE,IAAI,GAAG,MAAM+B,QAAQ,CAACoC,IAAI,CAAC,CAAC;YAClC,IAAInE,IAAI,IAAIA,IAAI,CAAC1D,QAAQ,EAAE;cACzBU,OAAO,CAACmC,GAAG,CAAC,8BAA8B,EAAEa,IAAI,CAAC1D,QAAQ,CAACsE,MAAM,CAAC;cACjEmD,mBAAmB,CAAC/D,IAAI,CAAC1D,QAAQ,CAAC;cAClC,OAAO0D,IAAI,CAAC1D,QAAQ;YACtB;UACF;QACF,CAAC,CAAC,OAAO8H,CAAC,EAAE;UACVpH,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEqH,CAAC,CAAC;QAChD;MACF;MAEA,OAAOtJ,YAAY,IAAI,EAAE;IAC3B,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAMgH,mBAAmB,GAAIM,WAAW,IAAK;IAC3C;IACA,IAAI/H,QAAQ,CAACsE,MAAM,KAAKyD,WAAW,CAACzD,MAAM,EAAE;MAC1C5D,OAAO,CAACmC,GAAG,CAAC,aAAa,EAAE7C,QAAQ,CAACsE,MAAM,EAAE,GAAG,EAAEyD,WAAW,CAACzD,MAAM,CAAC;MACpEH,aAAa,CAAC,CAAC,CAAC,CAAC;MACjB;IACF;;IAEA;IACA,MAAM6D,UAAU,GAAG,IAAIxG,GAAG,CAACxB,QAAQ,CAACqD,GAAG,CAACmB,CAAC,IAAIA,CAAC,CAACE,EAAE,CAAC,CAAC;IACnD,MAAMuD,aAAa,GAAGF,WAAW,CAACG,IAAI,CAAC1D,CAAC,IAAI,CAACwD,UAAU,CAACnD,GAAG,CAACL,CAAC,CAACE,EAAE,CAAC,CAAC;IAElE,IAAIuD,aAAa,EAAE;MACjBvH,OAAO,CAACmC,GAAG,CAAC,SAAS,CAAC;MACtBsB,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACAjH,SAAS,CAAC,MAAM;IACd;IACA,IAAI8C,QAAQ,CAACsE,MAAM,GAAG,CAAC,IAAI,CAAC3D,eAAe,EAAE;MAC3C;MACAC,kBAAkB,CAACZ,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC/BU,OAAO,CAACmC,GAAG,CAAC,aAAa,EAAE7C,QAAQ,CAAC,CAAC,CAAC,CAAC+E,WAAW,CAAC;IACrD;EACF,CAAC,EAAE,CAAC/E,QAAQ,EAAEW,eAAe,CAAC,CAAC;;EAE/B;EACAzD,SAAS,CAAC,MAAM;IACd,IAAIiL,KAAK,GAAG,IAAI;IAChB,IAAIC,YAAY,GAAG,IAAI;IACvB,IAAIC,cAAc,GAAG,IAAIrF,IAAI,CAAC,CAAC;;IAE/B;IACA,IAAI,CAACtB,aAAa,CAACqB,OAAO,EAAE;MAC1BrC,OAAO,CAACD,KAAK,CAAC,SAAS,CAAC;MACxB;IACF;;IAEA;IACA,IAAI6H,aAAa,GAAGxK,OAAO,CAACyK,gBAAgB,CAAC7G,aAAa,CAACqB,OAAO,CAAC;IACnE,IAAIuF,aAAa,EAAE;MACjBA,aAAa,CAACE,OAAO,CAAC,CAAC;IACzB;IAEA,IAAI;MACF;MACAL,KAAK,GAAGrK,OAAO,CAAC2K,IAAI,CAAC/G,aAAa,CAACqB,OAAO,CAAC;;MAE3C;MACA,MAAM2F,WAAW,GAAGA,CAAA,KAAM;QACxB,MAAMC,WAAW,GAAG,IAAI3F,IAAI,CAAC,CAAC;QAC9BqF,cAAc,GAAGM,WAAW;;QAE5B;QACA,MAAMC,UAAU,GAAG,CACjB;UAAEjF,IAAI,EAAE,KAAK;UAAEkF,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EAChD;UAAEnF,IAAI,EAAE,KAAK;UAAEkF,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EAChD;UAAEnF,IAAI,EAAE,KAAK;UAAEkF,IAAI,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAU,CAAC,EACjD;UAAEnF,IAAI,EAAE,KAAK;UAAEkF,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC/C;UAAEnF,IAAI,EAAE,KAAK;UAAEkF,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC/C;UAAEnF,IAAI,EAAE,MAAM;UAAEkF,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC,EAChD;UAAEnF,IAAI,EAAE,KAAK;UAAEkF,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC,CAChD;;QAED;QACA,MAAMpF,IAAI,GAAGkF,UAAU,CACpBvF,GAAG,CAACI,KAAK,KAAK;UACbsF,KAAK,EAAElH,UAAU,CAAC4B,KAAK,CAACE,IAAI,CAAC,IAAI,CAAC;UAClCkF,IAAI,EAAEpF,KAAK,CAACoF,IAAI;UAChBG,SAAS,EAAE;YAAEF,KAAK,EAAErF,KAAK,CAACqF;UAAM;QAClC,CAAC,CAAC,CAAC,CACFrE,MAAM,CAACwE,IAAI,IAAIA,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC,CAC9BG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACL,KAAK,GAAGI,CAAC,CAACJ,KAAK,CAAC;QAEpC,MAAMM,MAAM,GAAG;UACbC,KAAK,EAAE;YACLC,IAAI,EAAE,SAASZ,WAAW,CAACa,kBAAkB,CAAC,CAAC,EAAE;YACjDC,IAAI,EAAE,QAAQ;YACdC,GAAG,EAAE,CAAC,CAAC;YACPC,SAAS,EAAE;cACTC,QAAQ,EAAE,EAAE;cACZd,KAAK,EAAE;YACT;UACF,CAAC;UACDe,IAAI,EAAE;YACJH,GAAG,EAAE,EAAE;YACPI,MAAM,EAAE,CAAC;YACTL,IAAI,EAAE,CAAC;YACPM,KAAK,EAAE,EAAE;YACTC,YAAY,EAAE;UAChB,CAAC;UACDC,SAAS,EAAE,IAAI;UACfC,iBAAiB,EAAE,CAAC;UACpBC,uBAAuB,EAAE,IAAI;UAC7BC,qBAAqB,EAAE,cAAc;UACrCC,OAAO,EAAE;YACPC,OAAO,EAAE,MAAM;YACfC,WAAW,EAAE;cACX5G,IAAI,EAAE;YACR;UACF,CAAC;UACD6G,KAAK,EAAE;YACL7G,IAAI,EAAE,OAAO;YACb8G,IAAI,EAAE,KAAK;YACXC,SAAS,EAAE;cAAED,IAAI,EAAE;YAAM;UAC3B,CAAC;UACDE,KAAK,EAAE;YACLhH,IAAI,EAAE,UAAU;YAChBD,IAAI,EAAEA,IAAI,CAACL,GAAG,CAAC4F,IAAI,IAAIA,IAAI,CAACJ,IAAI,CAAC;YACjC+B,SAAS,EAAE;cACThB,QAAQ,EAAE,EAAE;cACZd,KAAK,EAAE,MAAM;cACb+B,MAAM,EAAE;YACV,CAAC;YACDC,QAAQ,EAAE;cAAEL,IAAI,EAAE;YAAM,CAAC;YACzBM,QAAQ,EAAE;cAAEN,IAAI,EAAE;YAAM;UAC1B,CAAC;UACDO,MAAM,EAAE,CAAC;YACPrH,IAAI,EAAE,KAAK;YACXD,IAAI,EAAEA,IAAI;YACVuH,QAAQ,EAAE,KAAK;YACfC,KAAK,EAAE;cACLT,IAAI,EAAE,IAAI;cACVU,QAAQ,EAAE,OAAO;cACjBC,SAAS,EAAE,MAAM;cACjBxB,QAAQ,EAAE,EAAE;cACZd,KAAK,EAAE;YACT,CAAC;YACDE,SAAS,EAAE;cACTqC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YAC3B,CAAC;YACDC,YAAY,EAAE,KAAK;YACnBC,cAAc,EAAE,SAAAA,CAAUC,GAAG,EAAE;cAC7B,OAAOA,GAAG,GAAG,GAAG;YAClB;UACF,CAAC;QACH,CAAC;;QAED;QACArD,KAAK,CAACsD,SAAS,CAACpC,MAAM,EAAE;UACtBqC,QAAQ,EAAE,KAAK;UACfC,YAAY,EAAE,CAAC,QAAQ;QACzB,CAAC,CAAC;MACJ,CAAC;;MAED;MACAjD,WAAW,CAAC,CAAC;;MAEb;MACA,MAAMkD,aAAa,GAAGhF,WAAW,CAAC8B,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;;MAEvD;MACAN,YAAY,GAAGA,CAAA,KAAM;QAAA,IAAAyD,MAAA;QACnB,CAAAA,MAAA,GAAA1D,KAAK,cAAA0D,MAAA,uBAALA,MAAA,CAAOC,MAAM,CAAC,CAAC;MACjB,CAAC;MACDvI,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAEwE,YAAY,CAAC;;MAE/C;MACA,OAAO,MAAM;QACXvB,aAAa,CAAC+E,aAAa,CAAC;QAC5B,IAAIxD,YAAY,EAAE;UAChB7E,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAEuE,YAAY,CAAC;QACpD;QACA,IAAID,KAAK,EAAE;UACTA,KAAK,CAACK,OAAO,CAAC,CAAC;QACjB;MACF,CAAC;IAEH,CAAC,CAAC,OAAO/H,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACoB,UAAU,CAAC,CAAC;;EAEhB;EACA3E,SAAS,CAAC,MAAM;IACd,MAAM6O,gBAAgB,GAAItI,KAAK,IAAK;MAClC,IAAI;QACF,IAAIA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;UAC3C,MAAMqI,OAAO,GAAGvI,KAAK,CAACC,IAAI,CAACA,IAAI;UAC/B,IAAI,CAACsI,OAAO,IAAI,CAACA,OAAO,CAACC,IAAI,EAAE;UAC/B,IAAGD,OAAO,CAACC,IAAI,CAAC3H,MAAM,GAAG,CAAC,EAAC;YACzB5D,OAAO,CAACmC,GAAG,CAAC,4BAA4B,EAAEY,KAAK,CAACC,IAAI,CAAC;UACvD;UAEA,MAAMwI,QAAQ,GAAGxJ,UAAU,CAACsJ,OAAO,CAACG,MAAM,CAAC;UAC3C,MAAMC,SAAS,GAAG1J,UAAU,CAACsJ,OAAO,CAACK,OAAO,CAAC;UAC7C,MAAMC,KAAK,GAAGN,OAAO,CAACM,KAAK;UAC3B,MAAMC,GAAG,GAAG9I,KAAK,CAACC,IAAI,CAAC6I,GAAG,IAAI,EAAE;UAChC,MAAMC,SAAS,GAAG/I,KAAK,CAACC,IAAI,CAAC+I,EAAE,IAAIzJ,IAAI,CAACC,GAAG,CAAC,CAAC;;UAE7C;UACA,MAAMyJ,sBAAsB,GAAG,EAAE;UACjCV,OAAO,CAACC,IAAI,CAACxF,OAAO,CAAChD,KAAK,IAAI;YAC5B,MAAMkJ,SAAS,GAAGlJ,KAAK,CAACkJ,SAAS;YACjC,MAAMC,QAAQ,GAAG,GAAGN,KAAK,IAAIC,GAAG,IAAII,SAAS,EAAE;YAC/C,MAAME,WAAW,GAAGC,mBAAmB,CACrCF,QAAQ,EACRJ,SAAS,EACT;cAAElK,GAAG,EAAE4J,QAAQ;cAAE3J,GAAG,EAAE6J;YAAU,CAAC,EACjCpK,aAAa,CAACe,OAChB,CAAC;YACD;YACA,IAAIgK,aAAa,GAAG,EAAE;YACtB,IAAIC,UAAU,GAAG,EAAE;YACnB,QAAOL,SAAS;cACd,KAAK,KAAK;gBAAEI,aAAa,GAAG,OAAO;gBAAEC,UAAU,GAAG,SAAS;gBAAE;cAC7D,KAAK,KAAK;gBAAED,aAAa,GAAG,OAAO;gBAAEC,UAAU,GAAG,SAAS;gBAAE;cAC7D,KAAK,KAAK;gBAAED,aAAa,GAAG,QAAQ;gBAAEC,UAAU,GAAG,SAAS;gBAAE;cAC9D,KAAK,KAAK;gBAAED,aAAa,GAAG,MAAM;gBAAEC,UAAU,GAAG,SAAS;gBAAE;cAC5D,KAAK,KAAK;gBAAED,aAAa,GAAG,MAAM;gBAAEC,UAAU,GAAG,SAAS;gBAAE;cAC5D,KAAK,MAAM;gBAAED,aAAa,GAAG,MAAM;gBAAEC,UAAU,GAAG,SAAS;gBAAE;cAC7D,KAAK,KAAK;gBAAED,aAAa,GAAG,MAAM;gBAAEC,UAAU,GAAG,SAAS;gBAAE;cAC5D;gBAASD,aAAa,GAAGtJ,KAAK,CAACwJ,WAAW,IAAI,MAAM;gBAAED,UAAU,GAAG,SAAS;YAC9E;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;;YAEA;YACA;YACA;YACA;YACA;YACA,IAAI,CAACH,WAAW,EAAE;cAChB;cACA,MAAMK,QAAQ,GAAG;gBACf/F,GAAG,EAAEnE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGkK,IAAI,CAACC,MAAM,CAAC,CAAC;gBAC/BzJ,IAAI,EAAEoJ,aAAa;gBACnBM,IAAI,EAAE,IAAIrK,IAAI,CAAC,CAAC,CAACwG,kBAAkB,CAAC,CAAC;gBACrClG,OAAO,EAAE0I,OAAO,CAACM,KAAK,IAAI,MAAM;gBAChCxD,KAAK,EAAEkE,UAAU;gBACjBL,SAAS,EAAEA,SAAS;gBACpBW,QAAQ,EAAE;kBACRpB,QAAQ,EAAEA,QAAQ;kBAClBE,SAAS,EAAEA;gBACb;cAEF,CAAC;cACDjM,SAAS,CAAC2C,IAAI,IAAI;gBAChB,MAAMyK,SAAS,GAAG,CAACL,QAAQ,EAAE,GAAGpK,IAAI,CAAC,CAAC0K,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;gBACpD,OAAOD,SAAS;cAClB,CAAC,CAAC;cAEFb,sBAAsB,CAACe,IAAI,CAACd,SAAS,CAAC;YACxC,CAAC,MAAM;cACLjM,OAAO,CAACmC,GAAG,CAAC,kBAAkBkK,aAAa,EAAE,CAAC;YAChD;UACF,CAAC,CAAC;;UAEF;UACA,IAAIL,sBAAsB,CAACpI,MAAM,GAAG,CAAC,EAAE;YACrCxC,aAAa,CAACgB,IAAI,IAAI;cACpB,MAAM4K,QAAQ,GAAG;gBAAE,GAAG5K;cAAK,CAAC;cAC5B4J,sBAAsB,CAACjG,OAAO,CAACkG,SAAS,IAAI;gBAC1Ce,QAAQ,CAACf,SAAS,CAAC,GAAG,CAACe,QAAQ,CAACf,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;cACtD,CAAC,CAAC;cACF,OAAOe,QAAQ;YACjB,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC,OAAOjN,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACtC;IACF,CAAC;;IAED;IACA8C,MAAM,CAACK,gBAAgB,CAAC,SAAS,EAAEmI,gBAAgB,CAAC;IAEpD,OAAO,MAAM;MACXxI,MAAM,CAACM,mBAAmB,CAAC,SAAS,EAAEkI,gBAAgB,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMe,mBAAmB,GAAGA,CAACF,QAAQ,EAAEjE,WAAW,EAAEgF,UAAU,EAAEC,UAAU,KAAK;IAC7E;IACA,IAAIA,UAAU,CAAC/I,GAAG,CAAC+H,QAAQ,CAAC,EAAE;MAC5B,MAAMiB,SAAS,GAAGD,UAAU,CAAClI,GAAG,CAACkH,QAAQ,CAAC;MAC1C,MAAMkB,QAAQ,GAAGnF,WAAW,GAAGkF,SAAS,CAACR,IAAI;;MAE7C;MACA,IAAIS,QAAQ,GAAG,IAAI,EAAE;QACnB;QACA,MAAMC,QAAQ,GAAGC,iBAAiB,CAChCL,UAAU,CAACrL,GAAG,EAAEqL,UAAU,CAACpL,GAAG,EAC9BsL,SAAS,CAACI,GAAG,CAAC3L,GAAG,EAAEuL,SAAS,CAACI,GAAG,CAAC1L,GACnC,CAAC;;QAED;QACA,IAAIwL,QAAQ,GAAG,CAAC,EAAE;UAChB;UACAH,UAAU,CAACM,GAAG,CAACtB,QAAQ,EAAE;YACvBS,IAAI,EAAE1E,WAAW;YACjBsF,GAAG,EAAEN;UACP,CAAC,CAAC;UACF,OAAO,IAAI;QACb;MACF;IACF;;IAEA;IACAC,UAAU,CAACM,GAAG,CAACtB,QAAQ,EAAE;MACvBS,IAAI,EAAE1E,WAAW;MACjBsF,GAAG,EAAEN;IACP,CAAC,CAAC;;IAEF;IACA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,MAAMK,iBAAiB,GAAGA,CAACG,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,KAAK;IACpD;IACA,IAAIH,IAAI,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC,EAAE;MACxD,OAAO,GAAG;IACZ;;IAEA;IACA,MAAMC,CAAC,GAAG,OAAO,CAAC,CAAC;IACnB,MAAMC,IAAI,GAAG,CAACH,IAAI,GAAGF,IAAI,IAAIhB,IAAI,CAACsB,EAAE,GAAG,GAAG;IAC1C,MAAMC,IAAI,GAAG,CAACJ,IAAI,GAAGF,IAAI,IAAIjB,IAAI,CAACsB,EAAE,GAAG,GAAG;IAC1C,MAAMtF,CAAC,GACLgE,IAAI,CAACwB,GAAG,CAACH,IAAI,GAAC,CAAC,CAAC,GAAGrB,IAAI,CAACwB,GAAG,CAACH,IAAI,GAAC,CAAC,CAAC,GACnCrB,IAAI,CAACyB,GAAG,CAACT,IAAI,GAAGhB,IAAI,CAACsB,EAAE,GAAG,GAAG,CAAC,GAAGtB,IAAI,CAACyB,GAAG,CAACP,IAAI,GAAGlB,IAAI,CAACsB,EAAE,GAAG,GAAG,CAAC,GAC/DtB,IAAI,CAACwB,GAAG,CAACD,IAAI,GAAC,CAAC,CAAC,GAAGvB,IAAI,CAACwB,GAAG,CAACD,IAAI,GAAC,CAAC,CAAC;IACrC,MAAMG,CAAC,GAAG,CAAC,GAAG1B,IAAI,CAAC2B,KAAK,CAAC3B,IAAI,CAAC4B,IAAI,CAAC5F,CAAC,CAAC,EAAEgE,IAAI,CAAC4B,IAAI,CAAC,CAAC,GAAC5F,CAAC,CAAC,CAAC;IACtD,MAAM4E,QAAQ,GAAGQ,CAAC,GAAGM,CAAC;IAEtB,OAAOd,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMiB,mBAAmB,GAAI1L,OAAO,IAAK;IACvC5C,OAAO,CAACmC,GAAG,CAAC,OAAO,EAAES,OAAO,CAACyB,WAAW,EAAE,KAAK,EAAEzB,OAAO,CAAClB,MAAM,CAAC;IAChExB,kBAAkB,CAAC0C,OAAO,CAAC;EAC7B,CAAC;;EAED;EACApG,SAAS,CAAC,MAAM;IACd;IACA,IAAIyD,eAAe,EAAE;MACnB,MAAMsO,sBAAsB,GAAGjP,QAAQ,CAACqG,IAAI,CAAC7B,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAK/D,eAAe,CAAC+D,EAAE,CAAC;MAC9E,IAAIuK,sBAAsB,KACrBA,sBAAsB,CAAC7M,MAAM,KAAKzB,eAAe,CAACyB,MAAM,IACxD6M,sBAAsB,CAAC5M,KAAK,KAAK1B,eAAe,CAAC0B,KAAK,IACtD4M,sBAAsB,CAAC3M,GAAG,KAAK3B,eAAe,CAAC2B,GAAG,IAClD2M,sBAAsB,CAAC1M,GAAG,KAAK5B,eAAe,CAAC4B,GAAG,IAClD0M,sBAAsB,CAACzM,OAAO,KAAK7B,eAAe,CAAC6B,OAAO,CAAC,EAAE;QAChE9B,OAAO,CAACmC,GAAG,CAAC,UAAUlC,eAAe,CAACoE,WAAW,OAAO,EAC7C,OAAOpE,eAAe,CAACyB,MAAM,OAAO6M,sBAAsB,CAAC7M,MAAM,EAAE,CAAC;QAC/ExB,kBAAkB,CAACqO,sBAAsB,CAAC;MAC5C;IACF;EACF,CAAC,EAAE,CAACjP,QAAQ,EAAEW,eAAe,CAAC,CAAC;;EAE/B;EACA,MAAMuO,cAAc,GAAG,CACrB;IACE5F,KAAK,EAAE,KAAK;IACZ6F,SAAS,EAAE,OAAO;IAClBhI,GAAG,EAAE,OAAO;IACZiI,KAAK,EAAE;EACT,CAAC,EACD;IACE9F,KAAK,EAAE,IAAI;IACX6F,SAAS,EAAE,QAAQ;IACnBhI,GAAG,EAAE,QAAQ;IACbiI,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEjN,MAAM,iBACZ1D,OAAA,CAACb,KAAK;MACJuE,MAAM,EAAEA,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;MAClDmH,IAAI,EAAEnH,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;IAAK;MAAAkN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC;EAEL,CAAC,EACD;IACEnG,KAAK,EAAE,IAAI;IACX6F,SAAS,EAAE,OAAO;IAClBhI,GAAG,EAAE,OAAO;IACZiI,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEhN,KAAK,IAAI,GAAG,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,GAAGN,KAAK;EAC1E,CAAC,CACF;;EAED;EACA,MAAMqN,eAAe,GAAGA,CAAA,kBACtBhR,OAAA,CAACjB,IAAI;IACHsJ,IAAI,EAAC,OAAO;IACZ4I,UAAU,EAAEzP,MAAO;IACnB0P,UAAU,EAAE3G,IAAI,iBACdvK,OAAA,CAACjB,IAAI,CAACoS,IAAI;MAACC,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAQ,CAAE;MAAAC,QAAA,eACrCtR,OAAA,CAACjB,IAAI,CAACoS,IAAI,CAACI,IAAI;QACb3G,KAAK,eACH5K,OAAA;UAAKoR,KAAK,EAAE;YAAElG,QAAQ,EAAE,MAAM;YAAEsG,YAAY,EAAE;UAAM,CAAE;UAAAF,QAAA,gBACpDtR,OAAA;YAAMoR,KAAK,EAAE;cAAEhH,KAAK,EAAEG,IAAI,CAACH,KAAK;cAAEqH,WAAW,EAAE;YAAM,CAAE;YAAAH,QAAA,EACpD/G,IAAI,CAACtF;UAAI;YAAA2L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACP/Q,OAAA;YAAMoR,KAAK,EAAE;cAAEhH,KAAK,EAAE,MAAM;cAAEc,QAAQ,EAAE;YAAO,CAAE;YAAAoG,QAAA,EAC9C/G,IAAI,CAACoE;UAAI;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;QACDxC,WAAW,eACTvO,OAAA;UAAKoR,KAAK,EAAE;YAAElG,QAAQ,EAAE,MAAM;YAAEd,KAAK,EAAE;UAAO,CAAE;UAAAkH,QAAA,gBAC9CtR,OAAA;YAAAsR,QAAA,GAAK,gBAAI,EAAC/G,IAAI,CAAC3F,OAAO;UAAA;YAAAgM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7B/Q,OAAA;YAAAsR,QAAA,GAAK,gBAAI,EAAC/G,IAAI,CAACqE,QAAQ,GACrB,GAAGrE,IAAI,CAACqE,QAAQ,CAACpB,QAAQ,CAACvJ,OAAO,CAAC,CAAC,CAAC,KAAKsG,IAAI,CAACqE,QAAQ,CAAClB,SAAS,CAACzJ,OAAO,CAAC,CAAC,CAAC,EAAE,GAC7E,MAAM;UAAA;YAAA2M,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CACX;IACFK,KAAK,EAAE;MACLM,SAAS,EAAE,mBAAmB;MAC9BC,SAAS,EAAE;IACb;EAAE;IAAAf,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;;EAED;EACAvS,SAAS,CAAC,MAAM;IACd,MAAMoT,kBAAkB,GAAG1J,WAAW,CAAC,MAAM;MAC3ClG,OAAO,CAACmC,GAAG,CAAC,gBAAgB,CAAC;MAC7BwB,uBAAuB,CAAC,CAAC;IAC3B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMwC,aAAa,CAACyJ,kBAAkB,CAAC;EAChD,CAAC,EAAE,CAACtQ,QAAQ,CAAC,CAAC;EAEd,oBACEtB,OAAA,CAACd,IAAI;IAAC2S,QAAQ,EAAEzQ,OAAQ;IAAC0Q,GAAG,EAAC,uBAAQ;IAAAR,QAAA,eACnCtR,OAAA,CAACI,aAAa;MAAAkR,QAAA,gBAEZtR,OAAA,CAACJ,kBAAkB;QACjB6M,QAAQ,EAAC,MAAM;QACfsF,SAAS,EAAErR,aAAc;QACzBsR,UAAU,EAAEA,CAAA,KAAM/O,gBAAgB,CAAC,CAACvC,aAAa,CAAE;QAAA4Q,QAAA,gBAGnDtR,OAAA,CAACa,QAAQ;UAAC+J,KAAK,EAAC,4CAAS;UAACqH,QAAQ,EAAE,KAAM;UAACnR,MAAM,EAAC,OAAO;UAAAwQ,QAAA,eACvDtR,OAAA,CAACrB,GAAG;YAACuT,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;YAAAZ,QAAA,gBAClBtR,OAAA,CAACpB,GAAG;cAACuT,IAAI,EAAE,CAAE;cAACf,KAAK,EAAE;gBAAEgB,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAS,CAAE;cAAAf,QAAA,eACjEtR,OAAA,CAACgB,gBAAgB;gBACf4J,KAAK,EAAC,0BAAM;gBACZP,KAAK,EAAElI,KAAK,CAACE,aAAc;gBAC3BiQ,UAAU,EAAE;kBAAElI,KAAK,EAAE;gBAAU,CAAE;gBACjCmI,KAAK,EAAE;kBAACH,OAAO,EAAE,MAAM;kBAACC,cAAc,EAAE;gBAAQ;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/Q,OAAA,CAACpB,GAAG;cAACuT,IAAI,EAAE,CAAE;cAAAb,QAAA,eACXtR,OAAA,CAACgB,gBAAgB;gBACf4J,KAAK,EAAC,0BAAM;gBACZP,KAAK,EAAElI,KAAK,CAACG;gBACb;gBAAA;gBACAgQ,UAAU,EAAE;kBAAElI,KAAK,EAAE;gBAAU;cAAE;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/Q,OAAA,CAACpB,GAAG;cAACuT,IAAI,EAAE,CAAE;cAAAb,QAAA,eACXtR,OAAA,CAACgB,gBAAgB;gBACf4J,KAAK,EAAC,0BAAM;gBACZP,KAAK,EAAElI,KAAK,CAACI;gBACb;gBAAA;gBACA+P,UAAU,EAAE;kBAAElI,KAAK,EAAE;gBAAU;cAAE;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/Q,OAAA,CAACpB,GAAG;cAACuT,IAAI,EAAE,CAAE;cAAAb,QAAA,eACXtR,OAAA,CAACgB,gBAAgB;gBACf4J,KAAK,EAAC,0BAAM;gBACZP,KAAK,EAAElI,KAAK,CAACK,YAAa;gBAC1B8P,UAAU,EAAE;kBAAElI,KAAK,EAAE;gBAAU;cAAE;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/Q,OAAA,CAACpB,GAAG;cAACuT,IAAI,EAAE,CAAE;cAAAb,QAAA,eACXtR,OAAA,CAACgB,gBAAgB;gBACf4J,KAAK,EAAC,0BAAM;gBACZP,KAAK,EAAElI,KAAK,CAACM;gBACb;gBAAA;gBACA6P,UAAU,EAAE;kBAAElI,KAAK,EAAE;gBAAU;cAAE;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN/Q,OAAA,CAACpB,GAAG;cAACuT,IAAI,EAAE,CAAE;cAAAb,QAAA,eACXtR,OAAA,CAACgB,gBAAgB;gBACf4J,KAAK,EAAC,0BAAM;gBACZP,KAAK,EAAElI,KAAK,CAACO;gBACb;gBAAA;gBACA4P,UAAU,EAAE;kBAAElI,KAAK,EAAE;gBAAU;cAAE;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGX/Q,OAAA,CAACa,QAAQ;UAAC+J,KAAK,EAAC,sCAAQ;UAACqH,QAAQ,EAAE,KAAM;UAACnR,MAAM,EAAC,kBAAkB;UAAAwQ,QAAA,EAChEN,eAAe,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGX/Q,OAAA,CAACa,QAAQ;UAAC+J,KAAK,EAAC,sCAAQ;UAACqH,QAAQ,EAAE,KAAM;UAACnR,MAAM,EAAC,kBAAkB;UAAAwQ,QAAA,eACjEtR,OAAA;YAAKwS,GAAG,EAAExP,aAAc;YAACoO,KAAK,EAAE;cAAEtQ,MAAM,EAAE,MAAM;cAAE4P,KAAK,EAAE;YAAO;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGrB/Q,OAAA,CAACQ,WAAW;QAACE,aAAa,EAAEA,aAAc;QAACC,cAAc,EAAEA;MAAe;QAAAiQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC,eAGd/Q,OAAA,CAACJ,kBAAkB;QACjB6M,QAAQ,EAAC,OAAO;QAChBsF,SAAS,EAAEpR,cAAe;QAC1BqR,UAAU,EAAEA,CAAA,KAAM9O,iBAAiB,CAAC,CAACvC,cAAc,CAAE;QAAA2Q,QAAA,gBAGrDtR,OAAA,CAACa,QAAQ;UAAC+J,KAAK,EAAC,0BAAM;UAACqH,QAAQ,EAAE,KAAM;UAACnR,MAAM,EAAC,KAAK;UAAAwQ,QAAA,eAClDtR,OAAA,CAAChB,KAAK;YACJiS,UAAU,EAAE3P,QAAS;YACrBmR,OAAO,EAAEjC,cAAe;YACxBkC,MAAM,EAAC,IAAI;YACXC,UAAU,EAAE,KAAM;YAClBtK,IAAI,EAAC,OAAO;YACZuK,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAE;YACnBC,KAAK,EAAGC,MAAM,KAAM;cAClBC,OAAO,EAAEA,CAAA,KAAM1C,mBAAmB,CAACyC,MAAM,CAAC;cAC1C3B,KAAK,EAAE;gBACL6B,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,CAAAjR,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+D,EAAE,MAAK+M,MAAM,CAAC/M,EAAE,GAAG,SAAS,GAAG,aAAa;gBACzEkF,QAAQ,EAAE,MAAM;gBAChBmG,OAAO,EAAE;cACX;YACF,CAAC;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGX/Q,OAAA,CAACa,QAAQ;UAAC+J,KAAK,EAAC,sCAAQ;UAACqH,QAAQ,EAAE,KAAM;UAACnR,MAAM,EAAC,KAAK;UAAAwQ,QAAA,EACnDrP,eAAe,gBACdjC,OAAA,CAACf,YAAY;YACXgT,QAAQ;YACRkB,MAAM,EAAE,CAAE;YACV9K,IAAI,EAAC,OAAO;YACZ+K,MAAM,EAAE;cACN5G,KAAK,EAAE;gBAAEtB,QAAQ,EAAE,MAAM;gBAAEmG,OAAO,EAAE;cAAU,CAAC;cAC/CgC,OAAO,EAAE;gBAAEnI,QAAQ,EAAE,MAAM;gBAAEmG,OAAO,EAAE;cAAU;YAClD,CAAE;YAAAC,QAAA,gBAEFtR,OAAA,CAACf,YAAY,CAACkS,IAAI;cAAC3E,KAAK,EAAC,oBAAK;cAAA8E,QAAA,EAAErP,eAAe,CAACoE;YAAW;cAAAuK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAChF/Q,OAAA,CAACf,YAAY,CAACkS,IAAI;cAAC3E,KAAK,EAAC,cAAI;cAAA8E,QAAA,eAC3BtR,OAAA,CAACb,KAAK;gBACJuE,MAAM,EAAEzB,eAAe,CAACyB,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;gBAClEmH,IAAI,EAAE5I,eAAe,CAACyB,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;cAAK;gBAAAkN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,eACpB/Q,OAAA,CAACf,YAAY,CAACkS,IAAI;cAAC3E,KAAK,EAAC,cAAI;cAAA8E,QAAA,EAC1BrP,eAAe,CAACyB,MAAM,KAAK,QAAQ,GAAGzB,eAAe,CAAC4B,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,GAAG;YAAK;cAAA2M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACpB/Q,OAAA,CAACf,YAAY,CAACkS,IAAI;cAAC3E,KAAK,EAAC,cAAI;cAAA8E,QAAA,EAC1BrP,eAAe,CAACyB,MAAM,KAAK,QAAQ,GAAGzB,eAAe,CAAC2B,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,GAAG;YAAK;cAAA2M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACpB/Q,OAAA,CAACf,YAAY,CAACkS,IAAI;cAAC3E,KAAK,EAAC,cAAI;cAAA8E,QAAA,EAC1BrP,eAAe,CAACyB,MAAM,KAAK,QAAQ,GAClC,GAAG,OAAOzB,eAAe,CAAC0B,KAAK,KAAK,QAAQ,GAAG1B,eAAe,CAAC0B,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,GAAGhC,eAAe,CAAC0B,KAAK,OAAO,GAC9G;YAAK;cAAAiN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC,eACpB/Q,OAAA,CAACf,YAAY,CAACkS,IAAI;cAAC3E,KAAK,EAAC,oBAAK;cAAA8E,QAAA,EAC3BrP,eAAe,CAACyB,MAAM,KAAK,QAAQ,GAClC,GAAG,OAAOzB,eAAe,CAAC6B,OAAO,KAAK,QAAQ,GAAG7B,eAAe,CAAC6B,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,GAAGhC,eAAe,CAAC6B,OAAO,GAAG,GAChH;YAAK;cAAA8M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,gBAEf/Q,OAAA;YAAGoR,KAAK,EAAE;cAAElG,QAAQ,EAAE;YAAO,CAAE;YAAAoG,QAAA,EAAC;UAAW;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC/C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEX,CAAC;AAAC5P,EAAA,CAppCID,eAAe;AAAAoS,GAAA,GAAfpS,eAAe;AAspCrB,eAAeA,eAAe;AAAC,IAAAb,EAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAqS,GAAA;AAAAC,YAAA,CAAAlT,EAAA;AAAAkT,YAAA,CAAA3S,GAAA;AAAA2S,YAAA,CAAAxS,GAAA;AAAAwS,YAAA,CAAAtS,GAAA;AAAAsS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}