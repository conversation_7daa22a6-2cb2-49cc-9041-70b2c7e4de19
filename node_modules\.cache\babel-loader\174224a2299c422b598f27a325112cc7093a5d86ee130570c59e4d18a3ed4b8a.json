{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nconst getWidth = (index, props) => {\n  const {\n    width,\n    rows = 2\n  } = props;\n  if (Array.isArray(width)) {\n    return width[index];\n  }\n  // last paragraph\n  if (rows - 1 === index) {\n    return width;\n  }\n  return undefined;\n};\nconst Paragraph = props => {\n  const {\n    prefixCls,\n    className,\n    style,\n    rows = 0\n  } = props;\n  const rowList = Array.from({\n    length: rows\n  }).map((_, index) => (/*#__PURE__*/\n  // eslint-disable-next-line react/no-array-index-key\n  React.createElement(\"li\", {\n    key: index,\n    style: {\n      width: getWidth(index, props)\n    }\n  })));\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: classNames(prefixCls, className),\n    style: style\n  }, rowList);\n};\nexport default Paragraph;", "map": {"version": 3, "names": ["React", "classNames", "getWidth", "index", "props", "width", "rows", "Array", "isArray", "undefined", "Paragraph", "prefixCls", "className", "style", "rowList", "from", "length", "map", "_", "createElement", "key"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/skeleton/Paragraph.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nconst getWidth = (index, props) => {\n  const {\n    width,\n    rows = 2\n  } = props;\n  if (Array.isArray(width)) {\n    return width[index];\n  }\n  // last paragraph\n  if (rows - 1 === index) {\n    return width;\n  }\n  return undefined;\n};\nconst Paragraph = props => {\n  const {\n    prefixCls,\n    className,\n    style,\n    rows = 0\n  } = props;\n  const rowList = Array.from({\n    length: rows\n  }).map((_, index) => (\n  /*#__PURE__*/\n  // eslint-disable-next-line react/no-array-index-key\n  React.createElement(\"li\", {\n    key: index,\n    style: {\n      width: getWidth(index, props)\n    }\n  })));\n  return /*#__PURE__*/React.createElement(\"ul\", {\n    className: classNames(prefixCls, className),\n    style: style\n  }, rowList);\n};\nexport default Paragraph;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,MAAMC,QAAQ,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EACjC,MAAM;IACJC,KAAK;IACLC,IAAI,GAAG;EACT,CAAC,GAAGF,KAAK;EACT,IAAIG,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK,CAACF,KAAK,CAAC;EACrB;EACA;EACA,IAAIG,IAAI,GAAG,CAAC,KAAKH,KAAK,EAAE;IACtB,OAAOE,KAAK;EACd;EACA,OAAOI,SAAS;AAClB,CAAC;AACD,MAAMC,SAAS,GAAGN,KAAK,IAAI;EACzB,MAAM;IACJO,SAAS;IACTC,SAAS;IACTC,KAAK;IACLP,IAAI,GAAG;EACT,CAAC,GAAGF,KAAK;EACT,MAAMU,OAAO,GAAGP,KAAK,CAACQ,IAAI,CAAC;IACzBC,MAAM,EAAEV;EACV,CAAC,CAAC,CAACW,GAAG,CAAC,CAACC,CAAC,EAAEf,KAAK,MAChB;EACA;EACAH,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAE;IACxBC,GAAG,EAAEjB,KAAK;IACVU,KAAK,EAAE;MACLR,KAAK,EAAEH,QAAQ,CAACC,KAAK,EAAEC,KAAK;IAC9B;EACF,CAAC,CAAC,CAAC,CAAC;EACJ,OAAO,aAAaJ,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAE;IAC5CP,SAAS,EAAEX,UAAU,CAACU,SAAS,EAAEC,SAAS,CAAC;IAC3CC,KAAK,EAAEA;EACT,CAAC,EAAEC,OAAO,CAAC;AACb,CAAC;AACD,eAAeJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}