{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\RealTimeTraffic.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { BarChart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\nimport vehiclesData from '../data/vehicles.json';\n\n// 注册必要的echarts组件\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\necharts.use([Bar<PERSON><PERSON>, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\nconst StyledCanvas = styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\n_c = PageContainer;\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 24px' : props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 0' : props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\n_c2 = MainContent;\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\n_c3 = InfoCard;\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n  \n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n_c4 = CompactStatistic;\nconst RealTimeTraffic = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [vehicles, setVehicles] = useState([]);\n  const [events, setEvents] = useState(() => {\n    try {\n      const savedEvents = localStorage.getItem('realTimeTrafficEvents');\n      return savedEvents ? JSON.parse(savedEvents) : [];\n    } catch (error) {\n      console.error('读取事件列表失败:', error);\n      return [];\n    }\n  });\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\n  const [stats, setStats] = useState({\n    totalVehicles: 0,\n    onlineVehicles: 0,\n    offlineVehicles: 0,\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0\n  });\n\n  // 存储在线车辆的 BSM ID\n  const [onlineBsmIds, setOnlineBsmIds] = useState(new Set());\n  // 存储最后一次收到 BSM 消息的时间\n  const lastBsmTime = useRef({});\n  const eventChartRef = useRef(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n  const [eventStats, setEventStats] = useState(() => {\n    try {\n      const savedStats = localStorage.getItem('realTimeTrafficEventStats');\n      return savedStats ? JSON.parse(savedStats) : {\n        '401': 0,\n        // 道路抛洒物\n        '404': 0,\n        // 道路障碍物\n        '405': 0,\n        // 行人通过马路\n        '904': 0,\n        // 逆行车辆\n        '910': 0,\n        // 违停车辆\n        '1002': 0,\n        // 道路施工\n        '901': 0 // 车辆超速\n      };\n    } catch (error) {\n      console.error('读取事件统计数据失败:', error);\n      return {\n        '401': 0,\n        '404': 0,\n        '405': 0,\n        '904': 0,\n        '910': 0,\n        '1002': 0,\n        '901': 0\n      };\n    }\n  });\n\n  // 添加手动更新车辆状态和位置信息的函数\n  const updateVehicleStatus = useCallback((bsmId, status, speed = 0, lat = 0, lng = 0, heading = 0) => {\n    console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${speed}, 位置=(${lat}, ${lng})`);\n\n    // 更新在线状态\n    if (status === 'online') {\n      setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n      lastBsmTime.current[bsmId] = Date.now();\n    } else {\n      setOnlineBsmIds(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(bsmId);\n        return newSet;\n      });\n    }\n\n    // 更新车辆信息\n    setVehicles(prevVehicles => prevVehicles.map(vehicle => vehicle.bsmId === bsmId ? {\n      ...vehicle,\n      status: status,\n      speed: speed,\n      lat: lat,\n      lng: lng,\n      heading: heading\n    } : vehicle));\n  }, []);\n\n  // 在组件挂载时，暴露updateVehicleStatus函数到window对象\n  useEffect(() => {\n    window.updateVehicleStatus = updateVehicleStatus;\n\n    // 特别处理CSUST302车辆，确保其在线\n    if (vehiclesData && vehiclesData.vehicles) {\n      const targetVehicle = vehiclesData.vehicles.find(v => v.bsmId === 'CSUST302');\n      if (targetVehicle) {\n        console.log('找到目标车辆:', targetVehicle);\n        // 设置初始状态\n        setTimeout(() => {\n          updateVehicleStatus('CSUST302', 'online', 50, 28.196631, 112.972282, 90);\n        }, 2000);\n\n        // 设置定时更新，模拟接收BSM消息\n        const interval = setInterval(() => {\n          const randomSpeed = 45 + Math.random() * 10;\n          const randomLat = 28.196631 + (Math.random() - 0.5) * 0.001;\n          const randomLng = 112.972282 + (Math.random() - 0.5) * 0.001;\n          const randomHeading = 60 + Math.random() * 60;\n          updateVehicleStatus('CSUST302', 'online', randomSpeed, randomLat, randomLng, randomHeading);\n        }, 5000);\n        return () => clearInterval(interval);\n      }\n    }\n    return () => {\n      delete window.updateVehicleStatus;\n    };\n  }, [updateVehicleStatus]);\n\n  // 使用防抖保存事件列表到 localStorage\n  useEffect(() => {\n    const saveTimer = setTimeout(() => {\n      try {\n        localStorage.setItem('realTimeTrafficEvents', JSON.stringify(events));\n      } catch (error) {\n        console.error('保存事件列表失败:', error);\n      }\n    }, 1000); // 延迟1秒保存，避免频繁写入\n\n    return () => clearTimeout(saveTimer);\n  }, [events]);\n\n  // 使用防抖保存事件统计数据到 localStorage\n  useEffect(() => {\n    const saveTimer = setTimeout(() => {\n      try {\n        localStorage.setItem('realTimeTrafficEventStats', JSON.stringify(eventStats));\n      } catch (error) {\n        console.error('保存事件统计数据失败:', error);\n      }\n    }, 1000); // 延迟1秒保存，避免频繁写入\n\n    return () => clearTimeout(saveTimer);\n  }, [eventStats]);\n\n  // 获取车辆数据\n  const fetchVehicles = () => {\n    try {\n      // 从本地 JSON 文件获取车辆数据\n      const vehiclesList = vehiclesData.vehicles || [];\n\n      // 更新车辆数据，并根据 BSM ID 是否在线来设置状态\n      const updatedVehicles = vehiclesList.map(vehicle => ({\n        ...vehicle,\n        plate: vehicle.plateNumber,\n        // 适配表格显示\n        status: onlineBsmIds.has(vehicle.bsmId) ? 'online' : 'offline',\n        speed: 0,\n        // 初始速度设为 0，后续通过 BSM 消息更新\n        lat: 0,\n        // 初始位置设为 0，后续通过 BSM 消息更新\n        lng: 0,\n        // 初始位置设为 0，后续通过 BSM 消息更新\n        heading: 0 // 初始航向角设为 0，后续通过 BSM 消息更新\n      }));\n      setVehicles(updatedVehicles);\n\n      // 更新车辆统计数据\n      setStats(prevStats => ({\n        ...prevStats,\n        totalVehicles: updatedVehicles.length,\n        onlineVehicles: updatedVehicles.filter(v => v.status === 'online').length,\n        offlineVehicles: updatedVehicles.filter(v => v.status === 'offline').length\n      }));\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n    }\n  };\n\n  // 获取设备统计数据\n  const fetchDeviceStats = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      if (response.data && response.data.success) {\n        const devicesData = response.data.data;\n\n        // 更新设备统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalDevices: devicesData.length,\n          onlineDevices: devicesData.filter(d => d.status === 'online').length,\n          offlineDevices: devicesData.filter(d => d.status === 'offline').length\n        }));\n      }\n    } catch (error) {\n      console.error('获取设备统计数据失败:', error);\n    }\n  };\n\n  // 监听 BSM 消息\n  useEffect(() => {\n    const handleBsmMessage = event => {\n      if (event.data && event.data.type === 'bsm') {\n        // 获取bsmId，确保它正确地从消息中提取\n        const bsmData = event.data.data || {};\n        const bsmId = bsmData.bsmId || event.data.bsmId;\n        if (!bsmId) {\n          console.error('BSM消息缺少bsmId:', event.data);\n          return;\n        }\n        console.log('收到BSM消息，ID:', bsmId);\n        const now = Date.now();\n\n        // 更新最后接收时间\n        lastBsmTime.current[bsmId] = now;\n\n        // 添加到在线bsmId集合\n        setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n\n        // 提取正确的BSM数据\n        const speed = parseFloat(bsmData.partSpeed || event.data.speed || 0) * 3.6; // 转换为km/h\n        const lat = parseFloat(bsmData.partLat || event.data.lat || 0);\n        const lng = parseFloat(bsmData.partLong || event.data.lng || 0);\n        const heading = parseFloat(bsmData.partHeading || event.data.heading || 0);\n        console.log(`车辆${bsmId}状态: 速度=${speed.toFixed(1)}km/h, 位置=(${lat.toFixed(6)}, ${lng.toFixed(6)}), 航向=${heading.toFixed(1)}°`);\n\n        // 更新车辆状态和位置信息\n        setVehicles(prevVehicles => {\n          // 检查是否找到对应车辆\n          const foundVehicle = prevVehicles.find(v => v.bsmId === bsmId);\n          if (!foundVehicle) {\n            console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);\n            return prevVehicles;\n          }\n          return prevVehicles.map(vehicle => vehicle.bsmId === bsmId ? {\n            ...vehicle,\n            status: 'online',\n            speed: speed,\n            lat: lat,\n            lng: lng,\n            heading: heading\n          } : vehicle);\n        });\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleBsmMessage);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleBsmMessage);\n    };\n  }, []);\n\n  // 定期检查在线状态\n  useEffect(() => {\n    const checkOnlineStatus = () => {\n      const now = Date.now();\n      console.log('检查车辆在线状态...');\n      // 如果超过30秒没有收到BSM消息，则认为车辆离线\n      setOnlineBsmIds(prev => {\n        const newOnlineBsmIds = new Set(prev);\n        let hasChanges = false;\n\n        // 记录当前所有在线的车辆ID\n        if (newOnlineBsmIds.size > 0) {\n          console.log('当前在线车辆ID:', Array.from(newOnlineBsmIds));\n        }\n        newOnlineBsmIds.forEach(bsmId => {\n          const lastTime = lastBsmTime.current[bsmId] || 0;\n          const timeSinceLastUpdate = now - lastTime;\n          if (timeSinceLastUpdate > 30000) {\n            console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);\n            newOnlineBsmIds.delete(bsmId);\n            hasChanges = true;\n          }\n        });\n        if (hasChanges) {\n          // 更新车辆状态\n          setVehicles(prevVehicles => prevVehicles.map(vehicle => {\n            const isOnline = newOnlineBsmIds.has(vehicle.bsmId);\n            return {\n              ...vehicle,\n              status: isOnline ? 'online' : 'offline'\n            };\n          }));\n        }\n        return newOnlineBsmIds;\n      });\n    };\n    const interval = setInterval(checkOnlineStatus, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // 在组件挂载时获取数据\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      fetchVehicles();\n      await fetchDeviceStats();\n      setLoading(false);\n    };\n    loadData();\n    // 降低更新频率，避免频繁覆盖状态\n    const interval = setInterval(loadData, 60000); // 每60秒更新一次\n    return () => clearInterval(interval);\n  }, []);\n\n  // 添加自动选择第一个车辆的逻辑\n  useEffect(() => {\n    // 当车辆列表加载完成且有车辆数据时\n    if (vehicles.length > 0 && !selectedVehicle) {\n      // 自动选择第一个车辆\n      setSelectedVehicle(vehicles[0]);\n      console.log('已自动选择第一个车辆:', vehicles[0].plateNumber);\n    }\n  }, [vehicles, selectedVehicle]);\n\n  // 修改图表初始化代码\n  useEffect(() => {\n    let chart = null;\n    let handleResize = null;\n    let lastUpdateTime = new Date();\n\n    // 确保 DOM 元素存在\n    if (!eventChartRef.current) {\n      console.error('图表容器未找到');\n      return;\n    }\n\n    // 检查并清理已存在的图表实例\n    let existingChart = echarts.getInstanceByDom(eventChartRef.current);\n    if (existingChart) {\n      existingChart.dispose();\n    }\n    try {\n      // 初始化新的图表实例\n      chart = echarts.init(eventChartRef.current);\n\n      // 设置图表配置\n      const updateChart = () => {\n        const currentTime = new Date();\n        lastUpdateTime = currentTime;\n\n        // 事件类型配置\n        const eventTypes = [{\n          type: '401',\n          name: '道路抛洒物',\n          color: '#ff4d4f'\n        }, {\n          type: '404',\n          name: '道路障碍物',\n          color: '#faad14'\n        }, {\n          type: '405',\n          name: '行人通过马路',\n          color: '#1890ff'\n        }, {\n          type: '904',\n          name: '逆行车辆',\n          color: '#f5222d'\n        }, {\n          type: '910',\n          name: '违停车辆',\n          color: '#722ed1'\n        }, {\n          type: '1002',\n          name: '道路施工',\n          color: '#fa8c16'\n        }, {\n          type: '901',\n          name: '车辆超速',\n          color: '#eb2f96'\n        }];\n\n        // 处理数据\n        const data = eventTypes.map(event => ({\n          value: eventStats[event.type] || 0,\n          name: event.name,\n          itemStyle: {\n            color: event.color\n          }\n        })).filter(item => item.value > 0).sort((a, b) => b.value - a.value);\n        const option = {\n          title: {\n            text: `最后更新: ${currentTime.toLocaleTimeString()}`,\n            left: 'center',\n            top: -5,\n            textStyle: {\n              fontSize: 12,\n              color: '#999'\n            }\n          },\n          grid: {\n            top: 30,\n            bottom: 0,\n            left: 0,\n            right: 50,\n            containLabel: true\n          },\n          animation: true,\n          animationDuration: 0,\n          animationDurationUpdate: 1000,\n          animationEasingUpdate: 'quinticInOut',\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'shadow'\n            }\n          },\n          xAxis: {\n            type: 'value',\n            show: false,\n            splitLine: {\n              show: false\n            }\n          },\n          yAxis: {\n            type: 'category',\n            data: data.map(item => item.name),\n            axisLabel: {\n              fontSize: 12,\n              color: '#666',\n              margin: 8\n            },\n            axisTick: {\n              show: false\n            },\n            axisLine: {\n              show: false\n            }\n          },\n          series: [{\n            type: 'bar',\n            data: data,\n            barWidth: '50%',\n            label: {\n              show: true,\n              position: 'right',\n              formatter: '{c}次',\n              fontSize: 12,\n              color: '#666'\n            },\n            itemStyle: {\n              borderRadius: [0, 4, 4, 0]\n            },\n            realtimeSort: false,\n            animationDelay: function (idx) {\n              return idx * 100;\n            }\n          }]\n        };\n\n        // 使用 notMerge: false 来保持增量更新\n        chart.setOption(option, {\n          notMerge: false,\n          replaceMerge: ['series']\n        });\n      };\n\n      // 初始更新\n      updateChart();\n\n      // 监听事件统计变化，每分钟更新一次\n      const statsInterval = setInterval(updateChart, 60000); // 60000ms = 1分钟\n\n      // 监听窗口大小变化\n      handleResize = () => {\n        var _chart;\n        (_chart = chart) === null || _chart === void 0 ? void 0 : _chart.resize();\n      };\n      window.addEventListener('resize', handleResize);\n\n      // 清理函数\n      return () => {\n        clearInterval(statsInterval);\n        if (handleResize) {\n          window.removeEventListener('resize', handleResize);\n        }\n        if (chart) {\n          chart.dispose();\n        }\n      };\n    } catch (error) {\n      console.error('初始化图表失败:', error);\n    }\n  }, [eventStats]);\n\n  // 修改 RSI 消息处理逻辑\n  useEffect(() => {\n    const handleRsiMessage = event => {\n      try {\n        if (event.data && event.data.type === 'RSI') {\n          console.log('RealTimeTraffic 收到 RSI 消息:', event.data);\n          const rsiData = event.data.data;\n          if (!rsiData || !rsiData.rtes) {\n            console.warn('RSI 消息数据格式不正确:', rsiData);\n            return;\n          }\n          const events = rsiData.rtes;\n          const latitude = parseFloat(rsiData.posLat);\n          const longitude = parseFloat(rsiData.posLong);\n          events.forEach(event => {\n            const eventType = event.eventType;\n            const description = event.description;\n            const time = new Date().toLocaleTimeString();\n\n            // 获取事件类型的中文描述\n            let eventTypeText = '';\n            let eventColor = '';\n            switch (eventType) {\n              case '401':\n                eventTypeText = '道路抛洒物';\n                eventColor = '#ff4d4f';\n                break;\n              case '404':\n                eventTypeText = '道路障碍物';\n                eventColor = '#faad14';\n                break;\n              case '405':\n                eventTypeText = '行人通过马路';\n                eventColor = '#1890ff';\n                break;\n              case '904':\n                eventTypeText = '逆行车辆';\n                eventColor = '#f5222d';\n                break;\n              case '910':\n                eventTypeText = '违停车辆';\n                eventColor = '#722ed1';\n                break;\n              case '1002':\n                eventTypeText = '道路施工';\n                eventColor = '#fa8c16';\n                break;\n              case '901':\n                eventTypeText = '车辆超速';\n                eventColor = '#eb2f96';\n                break;\n              default:\n                eventTypeText = description || '未知事件';\n                eventColor = '#8c8c8c';\n            }\n\n            // 更新事件列表\n            const newEvent = {\n              key: Date.now() + Math.random(),\n              type: eventTypeText,\n              time: time,\n              vehicle: rsiData.rsuId || '未知设备',\n              color: eventColor,\n              eventType: eventType,\n              location: {\n                latitude: latitude,\n                longitude: longitude\n              }\n            };\n            setEvents(prev => {\n              const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n              console.log('更新后的事件列表:', newEvents);\n              return newEvents;\n            });\n\n            // 更新事件统计\n            if (eventType) {\n              setEventStats(prev => {\n                const newStats = {\n                  ...prev,\n                  [eventType]: (prev[eventType] || 0) + 1\n                };\n                console.log('更新后的事件统计:', newStats);\n                return newStats;\n              });\n            }\n          });\n        }\n      } catch (error) {\n        console.error('处理 RSI 消息失败:', error);\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleRsiMessage);\n    return () => {\n      window.removeEventListener('message', handleRsiMessage);\n    };\n  }, []);\n\n  // 处理车辆选择\n  const handleVehicleSelect = vehicle => {\n    setSelectedVehicle(vehicle);\n  };\n\n  // 车辆列表列定义\n  const vehicleColumns = [{\n    title: '车牌号',\n    dataIndex: 'plate',\n    key: 'plate',\n    width: '40%'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: '30%',\n    render: status => /*#__PURE__*/_jsxDEV(Badge, {\n      status: status === 'online' ? 'success' : 'error',\n      text: status === 'online' ? '在线' : '离线'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 713,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '速度',\n    dataIndex: 'speed',\n    key: 'speed',\n    width: '30%',\n    render: speed => `${typeof speed === 'number' ? speed.toFixed(2) : speed} km/h`\n  }];\n\n  // 修改实时事件列表的渲染\n  const renderEventList = () => /*#__PURE__*/_jsxDEV(List, {\n    size: \"small\",\n    dataSource: events,\n    renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n      style: {\n        padding: '8px 0'\n      },\n      children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n        title: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '13px',\n            marginBottom: '4px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: item.color,\n              marginRight: '8px'\n            },\n            children: item.type\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#666',\n              fontSize: '12px'\n            },\n            children: item.time\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 15\n        }, this),\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#666'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u8BBE\\u5907: \", item.vehicle]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u4F4D\\u7F6E: \", item.location ? `${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}` : '未知位置']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 747,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 735,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 734,\n      columnNumber: 9\n    }, this),\n    style: {\n      maxHeight: 'calc(100% - 24px)',\n      overflowY: 'auto'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 730,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    spinning: loading,\n    tip: \"\\u52A0\\u8F7D\\u4E2D...\",\n    children: /*#__PURE__*/_jsxDEV(PageContainer, {\n      children: [/*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"left\",\n        collapsed: leftCollapsed,\n        onCollapse: () => setLeftCollapsed(!leftCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u548C\\u8BBE\\u5907\\u603B\\u6570\\u4FE1\\u606F\",\n          bordered: false,\n          height: \"150px\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [8, 8],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u8F66\\u8F86\\u603B\\u6570\",\n                value: stats.totalVehicles,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 778,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 777,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u5728\\u7EBF\\u8F66\\u8F86\",\n                value: stats.onlineVehicles,\n                suffix: `/ ${stats.totalVehicles}`,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 785,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u79BB\\u7EBF\\u8F66\\u8F86\",\n                value: stats.offlineVehicles,\n                suffix: `/ ${stats.totalVehicles}`,\n                valueStyle: {\n                  color: '#cf1322'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u8BBE\\u5907\\u603B\\u6570\",\n                value: stats.totalDevices,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 800,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u5728\\u7EBF\\u8BBE\\u5907\",\n                value: stats.onlineDevices,\n                suffix: `/ ${stats.totalDevices}`,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 808,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u79BB\\u7EBF\\u8BBE\\u5907\",\n                value: stats.offlineDevices,\n                suffix: `/ ${stats.totalDevices}`,\n                valueStyle: {\n                  color: '#cf1322'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 775,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u5217\\u8868\",\n          bordered: false,\n          height: \"calc(50% - 81px)\",\n          children: renderEventList()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 827,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u7EDF\\u8BA1\",\n          bordered: false,\n          height: \"calc(50% - 81px)\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: eventChartRef,\n            style: {\n              height: '100%',\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 769,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        leftCollapsed: leftCollapsed,\n        rightCollapsed: rightCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 838,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"right\",\n        collapsed: rightCollapsed,\n        onCollapse: () => setRightCollapsed(!rightCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u5217\\u8868\",\n          bordered: false,\n          height: \"50%\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            dataSource: vehicles,\n            columns: vehicleColumns,\n            rowKey: \"id\",\n            pagination: false,\n            size: \"small\",\n            scroll: {\n              y: 180\n            },\n            onRow: record => ({\n              onClick: () => handleVehicleSelect(record),\n              style: {\n                cursor: 'pointer',\n                background: (selectedVehicle === null || selectedVehicle === void 0 ? void 0 : selectedVehicle.id) === record.id ? '#e6f7ff' : 'transparent',\n                fontSize: '13px',\n                padding: '4px 8px'\n              }\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 850,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 849,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u8BE6\\u7EC6\\u4FE1\\u606F\",\n          bordered: false,\n          height: \"50%\",\n          children: selectedVehicle ? /*#__PURE__*/_jsxDEV(Descriptions, {\n            bordered: true,\n            column: 1,\n            size: \"small\",\n            styles: {\n              label: {\n                fontSize: '13px',\n                padding: '4px 8px'\n              },\n              content: {\n                fontSize: '13px',\n                padding: '4px 8px'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8F66\\u724C\\u53F7\",\n              children: selectedVehicle.plateNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 881,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                status: selectedVehicle.status === 'online' ? 'success' : 'error',\n                text: selectedVehicle.status === 'online' ? '在线' : '离线'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 883,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7ECF\\u5EA6\",\n              children: selectedVehicle.status === 'online' ? selectedVehicle.lng.toFixed(7) : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7EAC\\u5EA6\",\n              children: selectedVehicle.status === 'online' ? selectedVehicle.lat.toFixed(7) : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u901F\\u5EA6\",\n              children: selectedVehicle.status === 'online' ? `${typeof selectedVehicle.speed === 'number' ? selectedVehicle.speed.toFixed(2) : selectedVehicle.speed} km/h` : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u822A\\u5411\\u89D2\",\n              children: selectedVehicle.status === 'online' ? `${typeof selectedVehicle.heading === 'number' ? selectedVehicle.heading.toFixed(2) : selectedVehicle.heading}°` : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 899,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '13px'\n            },\n            children: \"\\u8BF7\\u9009\\u62E9\\u8F66\\u8F86\\u67E5\\u770B\\u8BE6\\u7EC6\\u4FE1\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 906,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 870,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 843,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 767,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 766,\n    columnNumber: 5\n  }, this);\n};\n_s(RealTimeTraffic, \"y9zpPuf44jdn7GAgaF+hEQO9yhs=\");\n_c5 = RealTimeTraffic;\nexport default RealTimeTraffic;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"InfoCard\");\n$RefreshReg$(_c4, \"CompactStatistic\");\n$RefreshReg$(_c5, \"RealTimeTraffic\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "Row", "Col", "Card", "Statistic", "List", "Table", "Descriptions", "Spin", "Badge", "echarts", "<PERSON><PERSON><PERSON>", "GridComponent", "TooltipComponent", "LegendComponent", "TitleComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "CollapsibleSidebar", "axios", "vehiclesData", "jsxDEV", "_jsxDEV", "use", "StyledCanvas", "div", "<PERSON><PERSON><PERSON><PERSON>", "_c", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "_c2", "InfoCard", "height", "_c3", "CompactStatistic", "_c4", "RealTimeTraffic", "_s", "loading", "setLoading", "vehicles", "setVehicles", "events", "setEvents", "savedEvents", "localStorage", "getItem", "JSON", "parse", "error", "console", "selectedVehicle", "setSelectedVehicle", "stats", "setStats", "totalVehicles", "onlineVehicles", "offlineVehicles", "totalDevices", "onlineDevices", "offlineDevices", "onlineBsmIds", "setOnlineBsmIds", "Set", "lastBsmTime", "eventChartRef", "setLeftCollapsed", "setRightCollapsed", "eventStats", "setEventStats", "savedStats", "updateVehicleStatus", "bsmId", "status", "speed", "lat", "lng", "heading", "log", "prev", "current", "Date", "now", "newSet", "delete", "prevVehicles", "map", "vehicle", "window", "targetVehicle", "find", "v", "setTimeout", "interval", "setInterval", "randomSpeed", "Math", "random", "randomLat", "randomLng", "randomHeading", "clearInterval", "saveTimer", "setItem", "stringify", "clearTimeout", "fetchVehicles", "vehiclesList", "updatedVehicles", "plate", "plateNumber", "has", "prevStats", "length", "filter", "fetchDeviceStats", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "data", "success", "devicesData", "d", "handleBsmMessage", "event", "type", "bsmData", "parseFloat", "partSpeed", "partLat", "partLong", "partHeading", "toFixed", "foundVehicle", "addEventListener", "removeEventListener", "checkOnlineStatus", "newOnlineBsmIds", "has<PERSON><PERSON><PERSON>", "size", "Array", "from", "for<PERSON>ach", "lastTime", "timeSinceLastUpdate", "isOnline", "loadData", "chart", "handleResize", "lastUpdateTime", "existingChart", "getInstanceByDom", "dispose", "init", "updateChart", "currentTime", "eventTypes", "name", "color", "value", "itemStyle", "item", "sort", "a", "b", "option", "title", "text", "toLocaleTimeString", "left", "top", "textStyle", "fontSize", "grid", "bottom", "right", "containLabel", "animation", "animationDuration", "animationDurationUpdate", "animationEasingUpdate", "tooltip", "trigger", "axisPointer", "xAxis", "show", "splitLine", "yAxis", "axisLabel", "margin", "axisTick", "axisLine", "series", "<PERSON><PERSON><PERSON><PERSON>", "label", "position", "formatter", "borderRadius", "realtimeSort", "animationDelay", "idx", "setOption", "notMerge", "replaceMerge", "statsInterval", "_chart", "resize", "handleRsiMessage", "rsiData", "rtes", "warn", "latitude", "posLat", "longitude", "posLong", "eventType", "description", "time", "eventTypeText", "eventColor", "newEvent", "key", "rsuId", "location", "newEvents", "slice", "newStats", "handleVehicleSelect", "vehicleColumns", "dataIndex", "width", "render", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderEventList", "dataSource", "renderItem", "<PERSON><PERSON>", "style", "padding", "children", "Meta", "marginBottom", "marginRight", "maxHeight", "overflowY", "spinning", "tip", "collapsed", "onCollapse", "bordered", "gutter", "span", "valueStyle", "suffix", "ref", "columns", "<PERSON><PERSON><PERSON>", "pagination", "scroll", "y", "onRow", "record", "onClick", "cursor", "background", "id", "column", "styles", "content", "_c5", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RealTimeTraffic.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { Bar<PERSON>hart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\nimport vehiclesData from '../data/vehicles.json';\n\n// 注册必要的echarts组件\necharts.use([BarChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\n\nconst StyledCanvas = styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 24px' : \n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 0' : \n    props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n  \n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n\nconst RealTimeTraffic = () => {\n  const [loading, setLoading] = useState(true);\n  const [vehicles, setVehicles] = useState([]);\n  const [events, setEvents] = useState(() => {\n    try {\n      const savedEvents = localStorage.getItem('realTimeTrafficEvents');\n      return savedEvents ? JSON.parse(savedEvents) : [];\n    } catch (error) {\n      console.error('读取事件列表失败:', error);\n      return [];\n    }\n  });\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\n  const [stats, setStats] = useState({\n    totalVehicles: 0,\n    onlineVehicles: 0,\n    offlineVehicles: 0,\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0\n  });\n  \n  // 存储在线车辆的 BSM ID\n  const [onlineBsmIds, setOnlineBsmIds] = useState(new Set());\n  // 存储最后一次收到 BSM 消息的时间\n  const lastBsmTime = useRef({});\n  \n  const eventChartRef = useRef(null);\n  \n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n  \n  const [eventStats, setEventStats] = useState(() => {\n    try {\n      const savedStats = localStorage.getItem('realTimeTrafficEventStats');\n      return savedStats ? JSON.parse(savedStats) : {\n        '401': 0,  // 道路抛洒物\n        '404': 0,  // 道路障碍物\n        '405': 0,  // 行人通过马路\n        '904': 0,  // 逆行车辆\n        '910': 0,  // 违停车辆\n        '1002': 0, // 道路施工\n        '901': 0   // 车辆超速\n      };\n    } catch (error) {\n      console.error('读取事件统计数据失败:', error);\n      return {\n        '401': 0, '404': 0, '405': 0, '904': 0,\n        '910': 0, '1002': 0, '901': 0\n      };\n    }\n  });\n\n  // 添加手动更新车辆状态和位置信息的函数\n  const updateVehicleStatus = useCallback((bsmId, status, speed = 0, lat = 0, lng = 0, heading = 0) => {\n    console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${speed}, 位置=(${lat}, ${lng})`);\n    \n    // 更新在线状态\n    if (status === 'online') {\n      setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n      lastBsmTime.current[bsmId] = Date.now();\n    } else {\n      setOnlineBsmIds(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(bsmId);\n        return newSet;\n      });\n    }\n    \n    // 更新车辆信息\n    setVehicles(prevVehicles => \n      prevVehicles.map(vehicle => \n        vehicle.bsmId === bsmId \n          ? { \n              ...vehicle, \n              status: status,\n              speed: speed,\n              lat: lat,\n              lng: lng,\n              heading: heading\n            } \n          : vehicle\n      )\n    );\n  }, []);\n  \n  // 在组件挂载时，暴露updateVehicleStatus函数到window对象\n  useEffect(() => {\n    window.updateVehicleStatus = updateVehicleStatus;\n    \n    // 特别处理CSUST302车辆，确保其在线\n    if (vehiclesData && vehiclesData.vehicles) {\n      const targetVehicle = vehiclesData.vehicles.find(v => v.bsmId === 'CSUST302');\n      if (targetVehicle) {\n        console.log('找到目标车辆:', targetVehicle);\n        // 设置初始状态\n        setTimeout(() => {\n          updateVehicleStatus('CSUST302', 'online', 50, 28.196631, 112.972282, 90);\n        }, 2000);\n        \n        // 设置定时更新，模拟接收BSM消息\n        const interval = setInterval(() => {\n          const randomSpeed = 45 + Math.random() * 10;\n          const randomLat = 28.196631 + (Math.random() - 0.5) * 0.001;\n          const randomLng = 112.972282 + (Math.random() - 0.5) * 0.001;\n          const randomHeading = 60 + Math.random() * 60;\n          \n          updateVehicleStatus('CSUST302', 'online', randomSpeed, randomLat, randomLng, randomHeading);\n        }, 5000);\n        \n        return () => clearInterval(interval);\n      }\n    }\n    \n    return () => {\n      delete window.updateVehicleStatus;\n    };\n  }, [updateVehicleStatus]);\n\n  // 使用防抖保存事件列表到 localStorage\n  useEffect(() => {\n    const saveTimer = setTimeout(() => {\n      try {\n        localStorage.setItem('realTimeTrafficEvents', JSON.stringify(events));\n      } catch (error) {\n        console.error('保存事件列表失败:', error);\n      }\n    }, 1000); // 延迟1秒保存，避免频繁写入\n    \n    return () => clearTimeout(saveTimer);\n  }, [events]);\n\n  // 使用防抖保存事件统计数据到 localStorage\n  useEffect(() => {\n    const saveTimer = setTimeout(() => {\n      try {\n        localStorage.setItem('realTimeTrafficEventStats', JSON.stringify(eventStats));\n      } catch (error) {\n        console.error('保存事件统计数据失败:', error);\n      }\n    }, 1000); // 延迟1秒保存，避免频繁写入\n    \n    return () => clearTimeout(saveTimer);\n  }, [eventStats]);\n\n  // 获取车辆数据\n  const fetchVehicles = () => {\n    try {\n      // 从本地 JSON 文件获取车辆数据\n      const vehiclesList = vehiclesData.vehicles || [];\n      \n      // 更新车辆数据，并根据 BSM ID 是否在线来设置状态\n      const updatedVehicles = vehiclesList.map(vehicle => ({\n        ...vehicle,\n        plate: vehicle.plateNumber, // 适配表格显示\n        status: onlineBsmIds.has(vehicle.bsmId) ? 'online' : 'offline',\n        speed: 0, // 初始速度设为 0，后续通过 BSM 消息更新\n        lat: 0,   // 初始位置设为 0，后续通过 BSM 消息更新\n        lng: 0,   // 初始位置设为 0，后续通过 BSM 消息更新\n        heading: 0 // 初始航向角设为 0，后续通过 BSM 消息更新\n      }));\n      \n      setVehicles(updatedVehicles);\n      \n      // 更新车辆统计数据\n      setStats(prevStats => ({\n        ...prevStats,\n        totalVehicles: updatedVehicles.length,\n        onlineVehicles: updatedVehicles.filter(v => v.status === 'online').length,\n        offlineVehicles: updatedVehicles.filter(v => v.status === 'offline').length\n      }));\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n    }\n  };\n\n  // 获取设备统计数据\n  const fetchDeviceStats = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      \n      if (response.data && response.data.success) {\n        const devicesData = response.data.data;\n        \n        // 更新设备统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalDevices: devicesData.length,\n          onlineDevices: devicesData.filter(d => d.status === 'online').length,\n          offlineDevices: devicesData.filter(d => d.status === 'offline').length\n        }));\n      }\n    } catch (error) {\n      console.error('获取设备统计数据失败:', error);\n    }\n  };\n\n  // 监听 BSM 消息\n  useEffect(() => {\n    const handleBsmMessage = (event) => {\n      if (event.data && event.data.type === 'bsm') {\n        // 获取bsmId，确保它正确地从消息中提取\n        const bsmData = event.data.data || {};\n        const bsmId = bsmData.bsmId || event.data.bsmId;\n        \n        if (!bsmId) {\n          console.error('BSM消息缺少bsmId:', event.data);\n          return;\n        }\n        \n        console.log('收到BSM消息，ID:', bsmId);\n        const now = Date.now();\n        \n        // 更新最后接收时间\n        lastBsmTime.current[bsmId] = now;\n        \n        // 添加到在线bsmId集合\n        setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n        \n        // 提取正确的BSM数据\n        const speed = parseFloat(bsmData.partSpeed || event.data.speed || 0) * 3.6; // 转换为km/h\n        const lat = parseFloat(bsmData.partLat || event.data.lat || 0);\n        const lng = parseFloat(bsmData.partLong || event.data.lng || 0);\n        const heading = parseFloat(bsmData.partHeading || event.data.heading || 0);\n        \n        console.log(`车辆${bsmId}状态: 速度=${speed.toFixed(1)}km/h, 位置=(${lat.toFixed(6)}, ${lng.toFixed(6)}), 航向=${heading.toFixed(1)}°`);\n        \n        // 更新车辆状态和位置信息\n        setVehicles(prevVehicles => {\n          // 检查是否找到对应车辆\n          const foundVehicle = prevVehicles.find(v => v.bsmId === bsmId);\n          if (!foundVehicle) {\n            console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);\n            return prevVehicles;\n          }\n          \n          return prevVehicles.map(vehicle => \n            vehicle.bsmId === bsmId \n              ? { \n                  ...vehicle, \n                  status: 'online',\n                  speed: speed,\n                  lat: lat,\n                  lng: lng,\n                  heading: heading\n                } \n              : vehicle\n          );\n        });\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleBsmMessage);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleBsmMessage);\n    };\n  }, []);\n\n  // 定期检查在线状态\n  useEffect(() => {\n    const checkOnlineStatus = () => {\n      const now = Date.now();\n      console.log('检查车辆在线状态...');\n      // 如果超过30秒没有收到BSM消息，则认为车辆离线\n      setOnlineBsmIds(prev => {\n        const newOnlineBsmIds = new Set(prev);\n        let hasChanges = false;\n        \n        // 记录当前所有在线的车辆ID\n        if (newOnlineBsmIds.size > 0) {\n          console.log('当前在线车辆ID:', Array.from(newOnlineBsmIds));\n        }\n        \n        newOnlineBsmIds.forEach(bsmId => {\n          const lastTime = lastBsmTime.current[bsmId] || 0;\n          const timeSinceLastUpdate = now - lastTime;\n          \n          if (timeSinceLastUpdate > 30000) {\n            console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);\n            newOnlineBsmIds.delete(bsmId);\n            hasChanges = true;\n          }\n        });\n        \n        if (hasChanges) {\n          // 更新车辆状态\n          setVehicles(prevVehicles => \n            prevVehicles.map(vehicle => {\n              const isOnline = newOnlineBsmIds.has(vehicle.bsmId);\n              return {\n                ...vehicle,\n                status: isOnline ? 'online' : 'offline'\n              };\n            })\n          );\n        }\n        \n        return newOnlineBsmIds;\n      });\n    };\n\n    const interval = setInterval(checkOnlineStatus, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // 在组件挂载时获取数据\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      fetchVehicles();\n      await fetchDeviceStats();\n      setLoading(false);\n    };\n    \n    loadData();\n    // 降低更新频率，避免频繁覆盖状态\n    const interval = setInterval(loadData, 60000); // 每60秒更新一次\n    return () => clearInterval(interval);\n  }, []);\n\n  // 添加自动选择第一个车辆的逻辑\n  useEffect(() => {\n    // 当车辆列表加载完成且有车辆数据时\n    if (vehicles.length > 0 && !selectedVehicle) {\n      // 自动选择第一个车辆\n      setSelectedVehicle(vehicles[0]);\n      console.log('已自动选择第一个车辆:', vehicles[0].plateNumber);\n    }\n  }, [vehicles, selectedVehicle]);\n  \n  // 修改图表初始化代码\n  useEffect(() => {\n    let chart = null;\n    let handleResize = null;\n    let lastUpdateTime = new Date();\n\n    // 确保 DOM 元素存在\n    if (!eventChartRef.current) {\n      console.error('图表容器未找到');\n      return;\n    }\n\n    // 检查并清理已存在的图表实例\n    let existingChart = echarts.getInstanceByDom(eventChartRef.current);\n    if (existingChart) {\n      existingChart.dispose();\n    }\n\n    try {\n      // 初始化新的图表实例\n      chart = echarts.init(eventChartRef.current);\n      \n      // 设置图表配置\n      const updateChart = () => {\n        const currentTime = new Date();\n        lastUpdateTime = currentTime;\n\n        // 事件类型配置\n        const eventTypes = [\n          { type: '401', name: '道路抛洒物', color: '#ff4d4f' },\n          { type: '404', name: '道路障碍物', color: '#faad14' },\n          { type: '405', name: '行人通过马路', color: '#1890ff' },\n          { type: '904', name: '逆行车辆', color: '#f5222d' },\n          { type: '910', name: '违停车辆', color: '#722ed1' },\n          { type: '1002', name: '道路施工', color: '#fa8c16' },\n          { type: '901', name: '车辆超速', color: '#eb2f96' }\n        ];\n\n        // 处理数据\n        const data = eventTypes\n          .map(event => ({\n            value: eventStats[event.type] || 0,\n            name: event.name,\n            itemStyle: { color: event.color }\n          }))\n          .filter(item => item.value > 0)\n          .sort((a, b) => b.value - a.value);\n\n        const option = {\n          title: {\n            text: `最后更新: ${currentTime.toLocaleTimeString()}`,\n            left: 'center',\n            top: -5,\n            textStyle: {\n              fontSize: 12,\n              color: '#999'\n            }\n          },\n          grid: {\n            top: 30,\n            bottom: 0,\n            left: 0,\n            right: 50,\n            containLabel: true\n          },\n          animation: true,\n          animationDuration: 0,\n          animationDurationUpdate: 1000,\n          animationEasingUpdate: 'quinticInOut',\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'shadow'\n            }\n          },\n          xAxis: {\n            type: 'value',\n            show: false,\n            splitLine: { show: false }\n          },\n          yAxis: {\n            type: 'category',\n            data: data.map(item => item.name),\n            axisLabel: {\n              fontSize: 12,\n              color: '#666',\n              margin: 8\n            },\n            axisTick: { show: false },\n            axisLine: { show: false }\n          },\n          series: [{\n            type: 'bar',\n            data: data,\n            barWidth: '50%',\n            label: {\n              show: true,\n              position: 'right',\n              formatter: '{c}次',\n              fontSize: 12,\n              color: '#666'\n            },\n            itemStyle: {\n              borderRadius: [0, 4, 4, 0]\n            },\n            realtimeSort: false,\n            animationDelay: function (idx) {\n              return idx * 100;\n            }\n          }]\n        };\n\n        // 使用 notMerge: false 来保持增量更新\n        chart.setOption(option, {\n          notMerge: false,\n          replaceMerge: ['series']\n        });\n      };\n\n      // 初始更新\n      updateChart();\n      \n      // 监听事件统计变化，每分钟更新一次\n      const statsInterval = setInterval(updateChart, 60000); // 60000ms = 1分钟\n      \n      // 监听窗口大小变化\n      handleResize = () => {\n        chart?.resize();\n      };\n      window.addEventListener('resize', handleResize);\n\n      // 清理函数\n      return () => {\n        clearInterval(statsInterval);\n        if (handleResize) {\n          window.removeEventListener('resize', handleResize);\n        }\n        if (chart) {\n          chart.dispose();\n        }\n      };\n\n    } catch (error) {\n      console.error('初始化图表失败:', error);\n    }\n  }, [eventStats]);\n  \n  // 修改 RSI 消息处理逻辑\n  useEffect(() => {\n    const handleRsiMessage = (event) => {\n      try {\n        if (event.data && event.data.type === 'RSI') {\n          console.log('RealTimeTraffic 收到 RSI 消息:', event.data);\n          \n          const rsiData = event.data.data;\n          if (!rsiData || !rsiData.rtes) {\n            console.warn('RSI 消息数据格式不正确:', rsiData);\n            return;\n          }\n\n          const events = rsiData.rtes;\n          const latitude = parseFloat(rsiData.posLat);\n          const longitude = parseFloat(rsiData.posLong);\n          \n          events.forEach(event => {\n            const eventType = event.eventType;\n            const description = event.description;\n            const time = new Date().toLocaleTimeString();\n            \n            // 获取事件类型的中文描述\n            let eventTypeText = '';\n            let eventColor = '';\n            switch(eventType) {\n              case '401': \n                eventTypeText = '道路抛洒物';\n                eventColor = '#ff4d4f';\n                break;\n              case '404': \n                eventTypeText = '道路障碍物';\n                eventColor = '#faad14';\n                break;\n              case '405': \n                eventTypeText = '行人通过马路';\n                eventColor = '#1890ff';\n                break;\n              case '904': \n                eventTypeText = '逆行车辆';\n                eventColor = '#f5222d';\n                break;\n              case '910': \n                eventTypeText = '违停车辆';\n                eventColor = '#722ed1';\n                break;\n              case '1002': \n                eventTypeText = '道路施工';\n                eventColor = '#fa8c16';\n                break;\n              case '901': \n                eventTypeText = '车辆超速';\n                eventColor = '#eb2f96';\n                break;\n              default: \n                eventTypeText = description || '未知事件';\n                eventColor = '#8c8c8c';\n            }\n            \n            // 更新事件列表\n            const newEvent = {\n              key: Date.now() + Math.random(),\n              type: eventTypeText,\n              time: time,\n              vehicle: rsiData.rsuId || '未知设备',\n              color: eventColor,\n              eventType: eventType,\n              location: {\n                latitude: latitude,\n                longitude: longitude\n              }\n            };\n            \n            setEvents(prev => {\n              const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n              console.log('更新后的事件列表:', newEvents);\n              return newEvents;\n            });\n            \n            // 更新事件统计\n            if (eventType) {\n              setEventStats(prev => {\n                const newStats = {\n                  ...prev,\n                  [eventType]: (prev[eventType] || 0) + 1\n                };\n                console.log('更新后的事件统计:', newStats);\n                return newStats;\n              });\n            }\n          });\n        }\n      } catch (error) {\n        console.error('处理 RSI 消息失败:', error);\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleRsiMessage);\n\n    return () => {\n      window.removeEventListener('message', handleRsiMessage);\n    };\n  }, []);\n  \n  // 处理车辆选择\n  const handleVehicleSelect = (vehicle) => {\n    setSelectedVehicle(vehicle);\n  };\n  \n  // 车辆列表列定义\n  const vehicleColumns = [\n    {\n      title: '车牌号',\n      dataIndex: 'plate',\n      key: 'plate',\n      width: '40%',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: '30%',\n      render: status => (\n        <Badge \n          status={status === 'online' ? 'success' : 'error'} \n          text={status === 'online' ? '在线' : '离线'} \n        />\n      ),\n    },\n    {\n      title: '速度',\n      dataIndex: 'speed',\n      key: 'speed',\n      width: '30%',\n      render: speed => `${typeof speed === 'number' ? speed.toFixed(2) : speed} km/h`,\n    }\n  ];\n  \n  // 修改实时事件列表的渲染\n  const renderEventList = () => (\n    <List\n      size=\"small\"\n      dataSource={events}\n      renderItem={item => (\n        <List.Item style={{ padding: '8px 0' }}>\n          <List.Item.Meta\n            title={\n              <div style={{ fontSize: '13px', marginBottom: '4px' }}>\n                <span style={{ color: item.color, marginRight: '8px' }}>\n                  {item.type}\n                </span>\n                <span style={{ color: '#666', fontSize: '12px' }}>\n                  {item.time}\n                </span>\n              </div>\n            }\n            description={\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                <div>设备: {item.vehicle}</div>\n                <div>位置: {item.location ? \n                  `${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}` : \n                  '未知位置'}\n                </div>\n              </div>\n            }\n          />\n        </List.Item>\n      )}\n      style={{\n        maxHeight: 'calc(100% - 24px)',\n        overflowY: 'auto'\n      }}\n    />\n  );\n  \n  return (\n    <Spin spinning={loading} tip=\"加载中...\">\n      <PageContainer>\n        {/* 左侧信息栏 */}\n        <CollapsibleSidebar \n          position=\"left\"\n          collapsed={leftCollapsed}\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\n        >\n          {/* 车辆和设备总数信息栏 */}\n          <InfoCard title=\"车辆和设备总数信息\" bordered={false} height=\"150px\">\n            <Row gutter={[8, 8]}>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"车辆总数\" \n                  value={stats.totalVehicles} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"在线车辆\" \n                  value={stats.onlineVehicles} \n                  suffix={`/ ${stats.totalVehicles}`} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"离线车辆\" \n                  value={stats.offlineVehicles} \n                  suffix={`/ ${stats.totalVehicles}`} \n                  valueStyle={{ color: '#cf1322' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"设备总数\" \n                  value={stats.totalDevices} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"在线设备\" \n                  value={stats.onlineDevices} \n                  suffix={`/ ${stats.totalDevices}`} \n                  valueStyle={{ color: '#3f8600' }} \n                />\n              </Col>\n              <Col span={8}>\n                <CompactStatistic \n                  title=\"离线设备\" \n                  value={stats.offlineDevices} \n                  suffix={`/ ${stats.totalDevices}`} \n                  valueStyle={{ color: '#cf1322' }} \n                />\n              </Col>\n            </Row>\n          </InfoCard>\n          \n          {/* 实时事件列表栏 */}\n          <InfoCard title=\"实时事件列表\" bordered={false} height=\"calc(50% - 81px)\">\n            {renderEventList()}\n          </InfoCard>\n          \n          {/* 实时事件统计栏 */}\n          <InfoCard title=\"实时事件统计\" bordered={false} height=\"calc(50% - 81px)\">\n            <div ref={eventChartRef} style={{ height: '100%', width: '100%' }}></div>\n          </InfoCard>\n        </CollapsibleSidebar>\n        \n        {/* 主内容区域 */}\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\n          {/* 主要内容 */}\n        </MainContent>\n        \n        {/* 右侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"right\"\n          collapsed={rightCollapsed}\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\n        >\n          {/* 车辆列表栏 */}\n          <InfoCard title=\"车辆列表\" bordered={false} height=\"50%\">\n            <Table \n              dataSource={vehicles} \n              columns={vehicleColumns} \n              rowKey=\"id\"\n              pagination={false}\n              size=\"small\"\n              scroll={{ y: 180 }}\n              onRow={(record) => ({\n                onClick: () => handleVehicleSelect(record),\n                style: { \n                  cursor: 'pointer',\n                  background: selectedVehicle?.id === record.id ? '#e6f7ff' : 'transparent',\n                  fontSize: '13px',\n                  padding: '4px 8px'\n                }\n              })}\n            />\n          </InfoCard>\n          \n          {/* 车辆详细信息栏 */}\n          <InfoCard title=\"车辆详细信息\" bordered={false} height=\"50%\">\n            {selectedVehicle ? (\n              <Descriptions \n                bordered \n                column={1} \n                size=\"small\"\n                styles={{\n                  label: { fontSize: '13px', padding: '4px 8px' },\n                  content: { fontSize: '13px', padding: '4px 8px' }\n                }}\n              >\n                <Descriptions.Item label=\"车牌号\">{selectedVehicle.plateNumber}</Descriptions.Item>\n                <Descriptions.Item label=\"状态\">\n                  <Badge \n                    status={selectedVehicle.status === 'online' ? 'success' : 'error'} \n                    text={selectedVehicle.status === 'online' ? '在线' : '离线'} \n                  />\n                </Descriptions.Item>\n                <Descriptions.Item label=\"经度\">\n                  {selectedVehicle.status === 'online' ? selectedVehicle.lng.toFixed(7) : 'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"纬度\">\n                  {selectedVehicle.status === 'online' ? selectedVehicle.lat.toFixed(7) : 'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"速度\">\n                  {selectedVehicle.status === 'online' ? \n                    `${typeof selectedVehicle.speed === 'number' ? selectedVehicle.speed.toFixed(2) : selectedVehicle.speed} km/h` : \n                    'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"航向角\">\n                  {selectedVehicle.status === 'online' ? \n                    `${typeof selectedVehicle.heading === 'number' ? selectedVehicle.heading.toFixed(2) : selectedVehicle.heading}°` : \n                    'N/A'}\n                </Descriptions.Item>\n              </Descriptions>\n            ) : (\n              <p style={{ fontSize: '13px' }}>请选择车辆查看详细信息</p>\n            )}\n          </InfoCard>\n        </CollapsibleSidebar>\n      </PageContainer>\n    </Spin>\n  );\n};\n\nexport default RealTimeTraffic;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AACxF,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,QAAQ,oBAAoB;AACrG,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,yCAAyC;AACxE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,uBAAuB;;AAEhD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAZ,OAAO,CAACa,GAAG,CAAC,CAACZ,QAAQ,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,cAAc,CAAC,CAAC;AAEzG,MAAMQ,YAAY,GAAGP,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMC,aAAa,GAAGT,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA,CAAC;;AAED;AAAAE,EAAA,GANMD,aAAa;AAOnB,MAAME,WAAW,GAAGX,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMI,YAAY,GAAGZ,MAAM,CAACQ,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMK,WAAW,GAAGb,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA,aAAaM,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACxEF,KAAK,CAACC,aAAa,GAAG,cAAc,GACpCD,KAAK,CAACE,cAAc,GAAG,cAAc,GAAG,OAAO;AACnD;AACA,YAAYF,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACvEF,KAAK,CAACC,aAAa,GAAG,WAAW,GACjCD,KAAK,CAACE,cAAc,GAAG,WAAW,GAAG,GAAG;AAC5C;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfMJ,WAAW;AAgBjB,MAAMK,QAAQ,GAAGlB,MAAM,CAACd,IAAI,CAAC;AAC7B;AACA,YAAY4B,KAAK,IAAIA,KAAK,CAACK,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GA1BMF,QAAQ;AA2Bd,MAAMG,gBAAgB,GAAGrB,MAAM,CAACb,SAAS,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmC,GAAA,GATID,gBAAgB;AAWtB,MAAME,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiD,MAAM,EAAEC,SAAS,CAAC,GAAGlD,QAAQ,CAAC,MAAM;IACzC,IAAI;MACF,MAAMmD,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;MACjE,OAAOF,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,GAAG,EAAE;IACnD,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,OAAO,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAM,CAACE,eAAe,EAAEC,kBAAkB,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC4D,KAAK,EAAEC,QAAQ,CAAC,GAAG7D,QAAQ,CAAC;IACjC8D,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAC,IAAIsE,GAAG,CAAC,CAAC,CAAC;EAC3D;EACA,MAAMC,WAAW,GAAGrE,MAAM,CAAC,CAAC,CAAC,CAAC;EAE9B,MAAMsE,aAAa,GAAGtE,MAAM,CAAC,IAAI,CAAC;;EAElC;EACA,MAAM,CAACiC,aAAa,EAAEsC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoC,cAAc,EAAEsC,iBAAiB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM,CAAC2E,UAAU,EAAEC,aAAa,CAAC,GAAG5E,QAAQ,CAAC,MAAM;IACjD,IAAI;MACF,MAAM6E,UAAU,GAAGzB,YAAY,CAACC,OAAO,CAAC,2BAA2B,CAAC;MACpE,OAAOwB,UAAU,GAAGvB,IAAI,CAACC,KAAK,CAACsB,UAAU,CAAC,GAAG;QAC3C,KAAK,EAAE,CAAC;QAAG;QACX,KAAK,EAAE,CAAC;QAAG;QACX,KAAK,EAAE,CAAC;QAAG;QACX,KAAK,EAAE,CAAC;QAAG;QACX,KAAK,EAAE,CAAC;QAAG;QACX,MAAM,EAAE,CAAC;QAAE;QACX,KAAK,EAAE,CAAC,CAAG;MACb,CAAC;IACH,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,OAAO;QACL,KAAK,EAAE,CAAC;QAAE,KAAK,EAAE,CAAC;QAAE,KAAK,EAAE,CAAC;QAAE,KAAK,EAAE,CAAC;QACtC,KAAK,EAAE,CAAC;QAAE,MAAM,EAAE,CAAC;QAAE,KAAK,EAAE;MAC9B,CAAC;IACH;EACF,CAAC,CAAC;;EAEF;EACA,MAAMsB,mBAAmB,GAAG3E,WAAW,CAAC,CAAC4E,KAAK,EAAEC,MAAM,EAAEC,KAAK,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,EAAEC,OAAO,GAAG,CAAC,KAAK;IACnG3B,OAAO,CAAC4B,GAAG,CAAC,gBAAgBN,KAAK,QAAQC,MAAM,QAAQC,KAAK,SAASC,GAAG,KAAKC,GAAG,GAAG,CAAC;;IAEpF;IACA,IAAIH,MAAM,KAAK,QAAQ,EAAE;MACvBX,eAAe,CAACiB,IAAI,IAAI,IAAIhB,GAAG,CAAC,CAAC,GAAGgB,IAAI,EAAEP,KAAK,CAAC,CAAC,CAAC;MAClDR,WAAW,CAACgB,OAAO,CAACR,KAAK,CAAC,GAAGS,IAAI,CAACC,GAAG,CAAC,CAAC;IACzC,CAAC,MAAM;MACLpB,eAAe,CAACiB,IAAI,IAAI;QACtB,MAAMI,MAAM,GAAG,IAAIpB,GAAG,CAACgB,IAAI,CAAC;QAC5BI,MAAM,CAACC,MAAM,CAACZ,KAAK,CAAC;QACpB,OAAOW,MAAM;MACf,CAAC,CAAC;IACJ;;IAEA;IACA1C,WAAW,CAAC4C,YAAY,IACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,IACtBA,OAAO,CAACf,KAAK,KAAKA,KAAK,GACnB;MACE,GAAGe,OAAO;MACVd,MAAM,EAAEA,MAAM;MACdC,KAAK,EAAEA,KAAK;MACZC,GAAG,EAAEA,GAAG;MACRC,GAAG,EAAEA,GAAG;MACRC,OAAO,EAAEA;IACX,CAAC,GACDU,OACN,CACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7F,SAAS,CAAC,MAAM;IACd8F,MAAM,CAACjB,mBAAmB,GAAGA,mBAAmB;;IAEhD;IACA,IAAIvD,YAAY,IAAIA,YAAY,CAACwB,QAAQ,EAAE;MACzC,MAAMiD,aAAa,GAAGzE,YAAY,CAACwB,QAAQ,CAACkD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnB,KAAK,KAAK,UAAU,CAAC;MAC7E,IAAIiB,aAAa,EAAE;QACjBvC,OAAO,CAAC4B,GAAG,CAAC,SAAS,EAAEW,aAAa,CAAC;QACrC;QACAG,UAAU,CAAC,MAAM;UACfrB,mBAAmB,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,EAAE,CAAC;QAC1E,CAAC,EAAE,IAAI,CAAC;;QAER;QACA,MAAMsB,QAAQ,GAAGC,WAAW,CAAC,MAAM;UACjC,MAAMC,WAAW,GAAG,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UAC3C,MAAMC,SAAS,GAAG,SAAS,GAAG,CAACF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK;UAC3D,MAAME,SAAS,GAAG,UAAU,GAAG,CAACH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK;UAC5D,MAAMG,aAAa,GAAG,EAAE,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;UAE7C1B,mBAAmB,CAAC,UAAU,EAAE,QAAQ,EAAEwB,WAAW,EAAEG,SAAS,EAAEC,SAAS,EAAEC,aAAa,CAAC;QAC7F,CAAC,EAAE,IAAI,CAAC;QAER,OAAO,MAAMC,aAAa,CAACR,QAAQ,CAAC;MACtC;IACF;IAEA,OAAO,MAAM;MACX,OAAOL,MAAM,CAACjB,mBAAmB;IACnC,CAAC;EACH,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACA7E,SAAS,CAAC,MAAM;IACd,MAAM4G,SAAS,GAAGV,UAAU,CAAC,MAAM;MACjC,IAAI;QACF/C,YAAY,CAAC0D,OAAO,CAAC,uBAAuB,EAAExD,IAAI,CAACyD,SAAS,CAAC9D,MAAM,CAAC,CAAC;MACvE,CAAC,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMwD,YAAY,CAACH,SAAS,CAAC;EACtC,CAAC,EAAE,CAAC5D,MAAM,CAAC,CAAC;;EAEZ;EACAhD,SAAS,CAAC,MAAM;IACd,MAAM4G,SAAS,GAAGV,UAAU,CAAC,MAAM;MACjC,IAAI;QACF/C,YAAY,CAAC0D,OAAO,CAAC,2BAA2B,EAAExD,IAAI,CAACyD,SAAS,CAACpC,UAAU,CAAC,CAAC;MAC/E,CAAC,CAAC,OAAOnB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACrC;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMwD,YAAY,CAACH,SAAS,CAAC;EACtC,CAAC,EAAE,CAAClC,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMsC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI;MACF;MACA,MAAMC,YAAY,GAAG3F,YAAY,CAACwB,QAAQ,IAAI,EAAE;;MAEhD;MACA,MAAMoE,eAAe,GAAGD,YAAY,CAACrB,GAAG,CAACC,OAAO,KAAK;QACnD,GAAGA,OAAO;QACVsB,KAAK,EAAEtB,OAAO,CAACuB,WAAW;QAAE;QAC5BrC,MAAM,EAAEZ,YAAY,CAACkD,GAAG,CAACxB,OAAO,CAACf,KAAK,CAAC,GAAG,QAAQ,GAAG,SAAS;QAC9DE,KAAK,EAAE,CAAC;QAAE;QACVC,GAAG,EAAE,CAAC;QAAI;QACVC,GAAG,EAAE,CAAC;QAAI;QACVC,OAAO,EAAE,CAAC,CAAC;MACb,CAAC,CAAC,CAAC;MAEHpC,WAAW,CAACmE,eAAe,CAAC;;MAE5B;MACAtD,QAAQ,CAAC0D,SAAS,KAAK;QACrB,GAAGA,SAAS;QACZzD,aAAa,EAAEqD,eAAe,CAACK,MAAM;QACrCzD,cAAc,EAAEoD,eAAe,CAACM,MAAM,CAACvB,CAAC,IAAIA,CAAC,CAAClB,MAAM,KAAK,QAAQ,CAAC,CAACwC,MAAM;QACzExD,eAAe,EAAEmD,eAAe,CAACM,MAAM,CAACvB,CAAC,IAAIA,CAAC,CAAClB,MAAM,KAAK,SAAS,CAAC,CAACwC;MACvE,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOhE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMkE,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMzG,KAAK,CAAC0G,GAAG,CAAC,GAAGL,MAAM,cAAc,CAAC;MAEzD,IAAII,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1C,MAAMC,WAAW,GAAGJ,QAAQ,CAACE,IAAI,CAACA,IAAI;;QAEtC;QACApE,QAAQ,CAAC0D,SAAS,KAAK;UACrB,GAAGA,SAAS;UACZtD,YAAY,EAAEkE,WAAW,CAACX,MAAM;UAChCtD,aAAa,EAAEiE,WAAW,CAACV,MAAM,CAACW,CAAC,IAAIA,CAAC,CAACpD,MAAM,KAAK,QAAQ,CAAC,CAACwC,MAAM;UACpErD,cAAc,EAAEgE,WAAW,CAACV,MAAM,CAACW,CAAC,IAAIA,CAAC,CAACpD,MAAM,KAAK,SAAS,CAAC,CAACwC;QAClE,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOhE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;;EAED;EACAvD,SAAS,CAAC,MAAM;IACd,MAAMoI,gBAAgB,GAAIC,KAAK,IAAK;MAClC,IAAIA,KAAK,CAACL,IAAI,IAAIK,KAAK,CAACL,IAAI,CAACM,IAAI,KAAK,KAAK,EAAE;QAC3C;QACA,MAAMC,OAAO,GAAGF,KAAK,CAACL,IAAI,CAACA,IAAI,IAAI,CAAC,CAAC;QACrC,MAAMlD,KAAK,GAAGyD,OAAO,CAACzD,KAAK,IAAIuD,KAAK,CAACL,IAAI,CAAClD,KAAK;QAE/C,IAAI,CAACA,KAAK,EAAE;UACVtB,OAAO,CAACD,KAAK,CAAC,eAAe,EAAE8E,KAAK,CAACL,IAAI,CAAC;UAC1C;QACF;QAEAxE,OAAO,CAAC4B,GAAG,CAAC,aAAa,EAAEN,KAAK,CAAC;QACjC,MAAMU,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;;QAEtB;QACAlB,WAAW,CAACgB,OAAO,CAACR,KAAK,CAAC,GAAGU,GAAG;;QAEhC;QACApB,eAAe,CAACiB,IAAI,IAAI,IAAIhB,GAAG,CAAC,CAAC,GAAGgB,IAAI,EAAEP,KAAK,CAAC,CAAC,CAAC;;QAElD;QACA,MAAME,KAAK,GAAGwD,UAAU,CAACD,OAAO,CAACE,SAAS,IAAIJ,KAAK,CAACL,IAAI,CAAChD,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QAC5E,MAAMC,GAAG,GAAGuD,UAAU,CAACD,OAAO,CAACG,OAAO,IAAIL,KAAK,CAACL,IAAI,CAAC/C,GAAG,IAAI,CAAC,CAAC;QAC9D,MAAMC,GAAG,GAAGsD,UAAU,CAACD,OAAO,CAACI,QAAQ,IAAIN,KAAK,CAACL,IAAI,CAAC9C,GAAG,IAAI,CAAC,CAAC;QAC/D,MAAMC,OAAO,GAAGqD,UAAU,CAACD,OAAO,CAACK,WAAW,IAAIP,KAAK,CAACL,IAAI,CAAC7C,OAAO,IAAI,CAAC,CAAC;QAE1E3B,OAAO,CAAC4B,GAAG,CAAC,KAAKN,KAAK,UAAUE,KAAK,CAAC6D,OAAO,CAAC,CAAC,CAAC,aAAa5D,GAAG,CAAC4D,OAAO,CAAC,CAAC,CAAC,KAAK3D,GAAG,CAAC2D,OAAO,CAAC,CAAC,CAAC,SAAS1D,OAAO,CAAC0D,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;;QAE7H;QACA9F,WAAW,CAAC4C,YAAY,IAAI;UAC1B;UACA,MAAMmD,YAAY,GAAGnD,YAAY,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnB,KAAK,KAAKA,KAAK,CAAC;UAC9D,IAAI,CAACgE,YAAY,EAAE;YACjBtF,OAAO,CAAC4B,GAAG,CAAC,eAAeN,KAAK,WAAW,CAAC;YAC5C,OAAOa,YAAY;UACrB;UAEA,OAAOA,YAAY,CAACC,GAAG,CAACC,OAAO,IAC7BA,OAAO,CAACf,KAAK,KAAKA,KAAK,GACnB;YACE,GAAGe,OAAO;YACVd,MAAM,EAAE,QAAQ;YAChBC,KAAK,EAAEA,KAAK;YACZC,GAAG,EAAEA,GAAG;YACRC,GAAG,EAAEA,GAAG;YACRC,OAAO,EAAEA;UACX,CAAC,GACDU,OACN,CAAC;QACH,CAAC,CAAC;MACJ;IACF,CAAC;;IAED;IACAC,MAAM,CAACiD,gBAAgB,CAAC,SAAS,EAAEX,gBAAgB,CAAC;;IAEpD;IACA,OAAO,MAAM;MACXtC,MAAM,CAACkD,mBAAmB,CAAC,SAAS,EAAEZ,gBAAgB,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApI,SAAS,CAAC,MAAM;IACd,MAAMiJ,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,MAAMzD,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACtBhC,OAAO,CAAC4B,GAAG,CAAC,aAAa,CAAC;MAC1B;MACAhB,eAAe,CAACiB,IAAI,IAAI;QACtB,MAAM6D,eAAe,GAAG,IAAI7E,GAAG,CAACgB,IAAI,CAAC;QACrC,IAAI8D,UAAU,GAAG,KAAK;;QAEtB;QACA,IAAID,eAAe,CAACE,IAAI,GAAG,CAAC,EAAE;UAC5B5F,OAAO,CAAC4B,GAAG,CAAC,WAAW,EAAEiE,KAAK,CAACC,IAAI,CAACJ,eAAe,CAAC,CAAC;QACvD;QAEAA,eAAe,CAACK,OAAO,CAACzE,KAAK,IAAI;UAC/B,MAAM0E,QAAQ,GAAGlF,WAAW,CAACgB,OAAO,CAACR,KAAK,CAAC,IAAI,CAAC;UAChD,MAAM2E,mBAAmB,GAAGjE,GAAG,GAAGgE,QAAQ;UAE1C,IAAIC,mBAAmB,GAAG,KAAK,EAAE;YAC/BjG,OAAO,CAAC4B,GAAG,CAAC,KAAKN,KAAK,kBAAkB,CAAC;YACzCoE,eAAe,CAACxD,MAAM,CAACZ,KAAK,CAAC;YAC7BqE,UAAU,GAAG,IAAI;UACnB;QACF,CAAC,CAAC;QAEF,IAAIA,UAAU,EAAE;UACd;UACApG,WAAW,CAAC4C,YAAY,IACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,IAAI;YAC1B,MAAM6D,QAAQ,GAAGR,eAAe,CAAC7B,GAAG,CAACxB,OAAO,CAACf,KAAK,CAAC;YACnD,OAAO;cACL,GAAGe,OAAO;cACVd,MAAM,EAAE2E,QAAQ,GAAG,QAAQ,GAAG;YAChC,CAAC;UACH,CAAC,CACH,CAAC;QACH;QAEA,OAAOR,eAAe;MACxB,CAAC,CAAC;IACJ,CAAC;IAED,MAAM/C,QAAQ,GAAGC,WAAW,CAAC6C,iBAAiB,EAAE,IAAI,CAAC;IACrD,OAAO,MAAMtC,aAAa,CAACR,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnG,SAAS,CAAC,MAAM;IACd,MAAM2J,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B9G,UAAU,CAAC,IAAI,CAAC;MAChBmE,aAAa,CAAC,CAAC;MACf,MAAMS,gBAAgB,CAAC,CAAC;MACxB5E,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAED8G,QAAQ,CAAC,CAAC;IACV;IACA,MAAMxD,QAAQ,GAAGC,WAAW,CAACuD,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;IAC/C,OAAO,MAAMhD,aAAa,CAACR,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnG,SAAS,CAAC,MAAM;IACd;IACA,IAAI8C,QAAQ,CAACyE,MAAM,GAAG,CAAC,IAAI,CAAC9D,eAAe,EAAE;MAC3C;MACAC,kBAAkB,CAACZ,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC/BU,OAAO,CAAC4B,GAAG,CAAC,aAAa,EAAEtC,QAAQ,CAAC,CAAC,CAAC,CAACsE,WAAW,CAAC;IACrD;EACF,CAAC,EAAE,CAACtE,QAAQ,EAAEW,eAAe,CAAC,CAAC;;EAE/B;EACAzD,SAAS,CAAC,MAAM;IACd,IAAI4J,KAAK,GAAG,IAAI;IAChB,IAAIC,YAAY,GAAG,IAAI;IACvB,IAAIC,cAAc,GAAG,IAAIvE,IAAI,CAAC,CAAC;;IAE/B;IACA,IAAI,CAAChB,aAAa,CAACe,OAAO,EAAE;MAC1B9B,OAAO,CAACD,KAAK,CAAC,SAAS,CAAC;MACxB;IACF;;IAEA;IACA,IAAIwG,aAAa,GAAGnJ,OAAO,CAACoJ,gBAAgB,CAACzF,aAAa,CAACe,OAAO,CAAC;IACnE,IAAIyE,aAAa,EAAE;MACjBA,aAAa,CAACE,OAAO,CAAC,CAAC;IACzB;IAEA,IAAI;MACF;MACAL,KAAK,GAAGhJ,OAAO,CAACsJ,IAAI,CAAC3F,aAAa,CAACe,OAAO,CAAC;;MAE3C;MACA,MAAM6E,WAAW,GAAGA,CAAA,KAAM;QACxB,MAAMC,WAAW,GAAG,IAAI7E,IAAI,CAAC,CAAC;QAC9BuE,cAAc,GAAGM,WAAW;;QAE5B;QACA,MAAMC,UAAU,GAAG,CACjB;UAAE/B,IAAI,EAAE,KAAK;UAAEgC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EAChD;UAAEjC,IAAI,EAAE,KAAK;UAAEgC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EAChD;UAAEjC,IAAI,EAAE,KAAK;UAAEgC,IAAI,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAU,CAAC,EACjD;UAAEjC,IAAI,EAAE,KAAK;UAAEgC,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC/C;UAAEjC,IAAI,EAAE,KAAK;UAAEgC,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC/C;UAAEjC,IAAI,EAAE,MAAM;UAAEgC,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC,EAChD;UAAEjC,IAAI,EAAE,KAAK;UAAEgC,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC,CAChD;;QAED;QACA,MAAMvC,IAAI,GAAGqC,UAAU,CACpBzE,GAAG,CAACyC,KAAK,KAAK;UACbmC,KAAK,EAAE9F,UAAU,CAAC2D,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;UAClCgC,IAAI,EAAEjC,KAAK,CAACiC,IAAI;UAChBG,SAAS,EAAE;YAAEF,KAAK,EAAElC,KAAK,CAACkC;UAAM;QAClC,CAAC,CAAC,CAAC,CACF/C,MAAM,CAACkD,IAAI,IAAIA,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC,CAC9BG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACL,KAAK,GAAGI,CAAC,CAACJ,KAAK,CAAC;QAEpC,MAAMM,MAAM,GAAG;UACbC,KAAK,EAAE;YACLC,IAAI,EAAE,SAASZ,WAAW,CAACa,kBAAkB,CAAC,CAAC,EAAE;YACjDC,IAAI,EAAE,QAAQ;YACdC,GAAG,EAAE,CAAC,CAAC;YACPC,SAAS,EAAE;cACTC,QAAQ,EAAE,EAAE;cACZd,KAAK,EAAE;YACT;UACF,CAAC;UACDe,IAAI,EAAE;YACJH,GAAG,EAAE,EAAE;YACPI,MAAM,EAAE,CAAC;YACTL,IAAI,EAAE,CAAC;YACPM,KAAK,EAAE,EAAE;YACTC,YAAY,EAAE;UAChB,CAAC;UACDC,SAAS,EAAE,IAAI;UACfC,iBAAiB,EAAE,CAAC;UACpBC,uBAAuB,EAAE,IAAI;UAC7BC,qBAAqB,EAAE,cAAc;UACrCC,OAAO,EAAE;YACPC,OAAO,EAAE,MAAM;YACfC,WAAW,EAAE;cACX1D,IAAI,EAAE;YACR;UACF,CAAC;UACD2D,KAAK,EAAE;YACL3D,IAAI,EAAE,OAAO;YACb4D,IAAI,EAAE,KAAK;YACXC,SAAS,EAAE;cAAED,IAAI,EAAE;YAAM;UAC3B,CAAC;UACDE,KAAK,EAAE;YACL9D,IAAI,EAAE,UAAU;YAChBN,IAAI,EAAEA,IAAI,CAACpC,GAAG,CAAC8E,IAAI,IAAIA,IAAI,CAACJ,IAAI,CAAC;YACjC+B,SAAS,EAAE;cACThB,QAAQ,EAAE,EAAE;cACZd,KAAK,EAAE,MAAM;cACb+B,MAAM,EAAE;YACV,CAAC;YACDC,QAAQ,EAAE;cAAEL,IAAI,EAAE;YAAM,CAAC;YACzBM,QAAQ,EAAE;cAAEN,IAAI,EAAE;YAAM;UAC1B,CAAC;UACDO,MAAM,EAAE,CAAC;YACPnE,IAAI,EAAE,KAAK;YACXN,IAAI,EAAEA,IAAI;YACV0E,QAAQ,EAAE,KAAK;YACfC,KAAK,EAAE;cACLT,IAAI,EAAE,IAAI;cACVU,QAAQ,EAAE,OAAO;cACjBC,SAAS,EAAE,MAAM;cACjBxB,QAAQ,EAAE,EAAE;cACZd,KAAK,EAAE;YACT,CAAC;YACDE,SAAS,EAAE;cACTqC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YAC3B,CAAC;YACDC,YAAY,EAAE,KAAK;YACnBC,cAAc,EAAE,SAAAA,CAAUC,GAAG,EAAE;cAC7B,OAAOA,GAAG,GAAG,GAAG;YAClB;UACF,CAAC;QACH,CAAC;;QAED;QACArD,KAAK,CAACsD,SAAS,CAACpC,MAAM,EAAE;UACtBqC,QAAQ,EAAE,KAAK;UACfC,YAAY,EAAE,CAAC,QAAQ;QACzB,CAAC,CAAC;MACJ,CAAC;;MAED;MACAjD,WAAW,CAAC,CAAC;;MAEb;MACA,MAAMkD,aAAa,GAAGjH,WAAW,CAAC+D,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;;MAEvD;MACAN,YAAY,GAAGA,CAAA,KAAM;QAAA,IAAAyD,MAAA;QACnB,CAAAA,MAAA,GAAA1D,KAAK,cAAA0D,MAAA,uBAALA,MAAA,CAAOC,MAAM,CAAC,CAAC;MACjB,CAAC;MACDzH,MAAM,CAACiD,gBAAgB,CAAC,QAAQ,EAAEc,YAAY,CAAC;;MAE/C;MACA,OAAO,MAAM;QACXlD,aAAa,CAAC0G,aAAa,CAAC;QAC5B,IAAIxD,YAAY,EAAE;UAChB/D,MAAM,CAACkD,mBAAmB,CAAC,QAAQ,EAAEa,YAAY,CAAC;QACpD;QACA,IAAID,KAAK,EAAE;UACTA,KAAK,CAACK,OAAO,CAAC,CAAC;QACjB;MACF,CAAC;IAEH,CAAC,CAAC,OAAO1G,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACmB,UAAU,CAAC,CAAC;;EAEhB;EACA1E,SAAS,CAAC,MAAM;IACd,MAAMwN,gBAAgB,GAAInF,KAAK,IAAK;MAClC,IAAI;QACF,IAAIA,KAAK,CAACL,IAAI,IAAIK,KAAK,CAACL,IAAI,CAACM,IAAI,KAAK,KAAK,EAAE;UAC3C9E,OAAO,CAAC4B,GAAG,CAAC,4BAA4B,EAAEiD,KAAK,CAACL,IAAI,CAAC;UAErD,MAAMyF,OAAO,GAAGpF,KAAK,CAACL,IAAI,CAACA,IAAI;UAC/B,IAAI,CAACyF,OAAO,IAAI,CAACA,OAAO,CAACC,IAAI,EAAE;YAC7BlK,OAAO,CAACmK,IAAI,CAAC,gBAAgB,EAAEF,OAAO,CAAC;YACvC;UACF;UAEA,MAAMzK,MAAM,GAAGyK,OAAO,CAACC,IAAI;UAC3B,MAAME,QAAQ,GAAGpF,UAAU,CAACiF,OAAO,CAACI,MAAM,CAAC;UAC3C,MAAMC,SAAS,GAAGtF,UAAU,CAACiF,OAAO,CAACM,OAAO,CAAC;UAE7C/K,MAAM,CAACuG,OAAO,CAAClB,KAAK,IAAI;YACtB,MAAM2F,SAAS,GAAG3F,KAAK,CAAC2F,SAAS;YACjC,MAAMC,WAAW,GAAG5F,KAAK,CAAC4F,WAAW;YACrC,MAAMC,IAAI,GAAG,IAAI3I,IAAI,CAAC,CAAC,CAAC0F,kBAAkB,CAAC,CAAC;;YAE5C;YACA,IAAIkD,aAAa,GAAG,EAAE;YACtB,IAAIC,UAAU,GAAG,EAAE;YACnB,QAAOJ,SAAS;cACd,KAAK,KAAK;gBACRG,aAAa,GAAG,OAAO;gBACvBC,UAAU,GAAG,SAAS;gBACtB;cACF,KAAK,KAAK;gBACRD,aAAa,GAAG,OAAO;gBACvBC,UAAU,GAAG,SAAS;gBACtB;cACF,KAAK,KAAK;gBACRD,aAAa,GAAG,QAAQ;gBACxBC,UAAU,GAAG,SAAS;gBACtB;cACF,KAAK,KAAK;gBACRD,aAAa,GAAG,MAAM;gBACtBC,UAAU,GAAG,SAAS;gBACtB;cACF,KAAK,KAAK;gBACRD,aAAa,GAAG,MAAM;gBACtBC,UAAU,GAAG,SAAS;gBACtB;cACF,KAAK,MAAM;gBACTD,aAAa,GAAG,MAAM;gBACtBC,UAAU,GAAG,SAAS;gBACtB;cACF,KAAK,KAAK;gBACRD,aAAa,GAAG,MAAM;gBACtBC,UAAU,GAAG,SAAS;gBACtB;cACF;gBACED,aAAa,GAAGF,WAAW,IAAI,MAAM;gBACrCG,UAAU,GAAG,SAAS;YAC1B;;YAEA;YACA,MAAMC,QAAQ,GAAG;cACfC,GAAG,EAAE/I,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGc,IAAI,CAACC,MAAM,CAAC,CAAC;cAC/B+B,IAAI,EAAE6F,aAAa;cACnBD,IAAI,EAAEA,IAAI;cACVrI,OAAO,EAAE4H,OAAO,CAACc,KAAK,IAAI,MAAM;cAChChE,KAAK,EAAE6D,UAAU;cACjBJ,SAAS,EAAEA,SAAS;cACpBQ,QAAQ,EAAE;gBACRZ,QAAQ,EAAEA,QAAQ;gBAClBE,SAAS,EAAEA;cACb;YACF,CAAC;YAED7K,SAAS,CAACoC,IAAI,IAAI;cAChB,MAAMoJ,SAAS,GAAG,CAACJ,QAAQ,EAAE,GAAGhJ,IAAI,CAAC,CAACqJ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;cACpDlL,OAAO,CAAC4B,GAAG,CAAC,WAAW,EAAEqJ,SAAS,CAAC;cACnC,OAAOA,SAAS;YAClB,CAAC,CAAC;;YAEF;YACA,IAAIT,SAAS,EAAE;cACbrJ,aAAa,CAACU,IAAI,IAAI;gBACpB,MAAMsJ,QAAQ,GAAG;kBACf,GAAGtJ,IAAI;kBACP,CAAC2I,SAAS,GAAG,CAAC3I,IAAI,CAAC2I,SAAS,CAAC,IAAI,CAAC,IAAI;gBACxC,CAAC;gBACDxK,OAAO,CAAC4B,GAAG,CAAC,WAAW,EAAEuJ,QAAQ,CAAC;gBAClC,OAAOA,QAAQ;cACjB,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAOpL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACtC;IACF,CAAC;;IAED;IACAuC,MAAM,CAACiD,gBAAgB,CAAC,SAAS,EAAEyE,gBAAgB,CAAC;IAEpD,OAAO,MAAM;MACX1H,MAAM,CAACkD,mBAAmB,CAAC,SAAS,EAAEwE,gBAAgB,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMoB,mBAAmB,GAAI/I,OAAO,IAAK;IACvCnC,kBAAkB,CAACmC,OAAO,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMgJ,cAAc,GAAG,CACrB;IACE9D,KAAK,EAAE,KAAK;IACZ+D,SAAS,EAAE,OAAO;IAClBR,GAAG,EAAE,OAAO;IACZS,KAAK,EAAE;EACT,CAAC,EACD;IACEhE,KAAK,EAAE,IAAI;IACX+D,SAAS,EAAE,QAAQ;IACnBR,GAAG,EAAE,QAAQ;IACbS,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEjK,MAAM,iBACZvD,OAAA,CAACb,KAAK;MACJoE,MAAM,EAAEA,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;MAClDiG,IAAI,EAAEjG,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;IAAK;MAAAkK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC;EAEL,CAAC,EACD;IACErE,KAAK,EAAE,IAAI;IACX+D,SAAS,EAAE,OAAO;IAClBR,GAAG,EAAE,OAAO;IACZS,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEhK,KAAK,IAAI,GAAG,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAAC6D,OAAO,CAAC,CAAC,CAAC,GAAG7D,KAAK;EAC1E,CAAC,CACF;;EAED;EACA,MAAMqK,eAAe,GAAGA,CAAA,kBACtB7N,OAAA,CAACjB,IAAI;IACH6I,IAAI,EAAC,OAAO;IACZkG,UAAU,EAAEtM,MAAO;IACnBuM,UAAU,EAAE7E,IAAI,iBACdlJ,OAAA,CAACjB,IAAI,CAACiP,IAAI;MAACC,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAQ,CAAE;MAAAC,QAAA,eACrCnO,OAAA,CAACjB,IAAI,CAACiP,IAAI,CAACI,IAAI;QACb7E,KAAK,eACHvJ,OAAA;UAAKiO,KAAK,EAAE;YAAEpE,QAAQ,EAAE,MAAM;YAAEwE,YAAY,EAAE;UAAM,CAAE;UAAAF,QAAA,gBACpDnO,OAAA;YAAMiO,KAAK,EAAE;cAAElF,KAAK,EAAEG,IAAI,CAACH,KAAK;cAAEuF,WAAW,EAAE;YAAM,CAAE;YAAAH,QAAA,EACpDjF,IAAI,CAACpC;UAAI;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACP5N,OAAA;YAAMiO,KAAK,EAAE;cAAElF,KAAK,EAAE,MAAM;cAAEc,QAAQ,EAAE;YAAO,CAAE;YAAAsE,QAAA,EAC9CjF,IAAI,CAACwD;UAAI;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;QACDnB,WAAW,eACTzM,OAAA;UAAKiO,KAAK,EAAE;YAAEpE,QAAQ,EAAE,MAAM;YAAEd,KAAK,EAAE;UAAO,CAAE;UAAAoF,QAAA,gBAC9CnO,OAAA;YAAAmO,QAAA,GAAK,gBAAI,EAACjF,IAAI,CAAC7E,OAAO;UAAA;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7B5N,OAAA;YAAAmO,QAAA,GAAK,gBAAI,EAACjF,IAAI,CAAC8D,QAAQ,GACrB,GAAG9D,IAAI,CAAC8D,QAAQ,CAACZ,QAAQ,CAAC/E,OAAO,CAAC,CAAC,CAAC,KAAK6B,IAAI,CAAC8D,QAAQ,CAACV,SAAS,CAACjF,OAAO,CAAC,CAAC,CAAC,EAAE,GAC7E,MAAM;UAAA;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CACX;IACFK,KAAK,EAAE;MACLM,SAAS,EAAE,mBAAmB;MAC9BC,SAAS,EAAE;IACb;EAAE;IAAAf,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;EAED,oBACE5N,OAAA,CAACd,IAAI;IAACuP,QAAQ,EAAErN,OAAQ;IAACsN,GAAG,EAAC,uBAAQ;IAAAP,QAAA,eACnCnO,OAAA,CAACI,aAAa;MAAA+N,QAAA,gBAEZnO,OAAA,CAACJ,kBAAkB;QACjBwL,QAAQ,EAAC,MAAM;QACfuD,SAAS,EAAEjO,aAAc;QACzBkO,UAAU,EAAEA,CAAA,KAAM5L,gBAAgB,CAAC,CAACtC,aAAa,CAAE;QAAAyN,QAAA,gBAGnDnO,OAAA,CAACa,QAAQ;UAAC0I,KAAK,EAAC,wDAAW;UAACsF,QAAQ,EAAE,KAAM;UAAC/N,MAAM,EAAC,OAAO;UAAAqN,QAAA,eACzDnO,OAAA,CAACrB,GAAG;YAACmQ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;YAAAX,QAAA,gBAClBnO,OAAA,CAACpB,GAAG;cAACmQ,IAAI,EAAE,CAAE;cAAAZ,QAAA,eACXnO,OAAA,CAACgB,gBAAgB;gBACfuI,KAAK,EAAC,0BAAM;gBACZP,KAAK,EAAE7G,KAAK,CAACE,aAAc;gBAC3B2M,UAAU,EAAE;kBAAEjG,KAAK,EAAE;gBAAU;cAAE;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5N,OAAA,CAACpB,GAAG;cAACmQ,IAAI,EAAE,CAAE;cAAAZ,QAAA,eACXnO,OAAA,CAACgB,gBAAgB;gBACfuI,KAAK,EAAC,0BAAM;gBACZP,KAAK,EAAE7G,KAAK,CAACG,cAAe;gBAC5B2M,MAAM,EAAE,KAAK9M,KAAK,CAACE,aAAa,EAAG;gBACnC2M,UAAU,EAAE;kBAAEjG,KAAK,EAAE;gBAAU;cAAE;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5N,OAAA,CAACpB,GAAG;cAACmQ,IAAI,EAAE,CAAE;cAAAZ,QAAA,eACXnO,OAAA,CAACgB,gBAAgB;gBACfuI,KAAK,EAAC,0BAAM;gBACZP,KAAK,EAAE7G,KAAK,CAACI,eAAgB;gBAC7B0M,MAAM,EAAE,KAAK9M,KAAK,CAACE,aAAa,EAAG;gBACnC2M,UAAU,EAAE;kBAAEjG,KAAK,EAAE;gBAAU;cAAE;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5N,OAAA,CAACpB,GAAG;cAACmQ,IAAI,EAAE,CAAE;cAAAZ,QAAA,eACXnO,OAAA,CAACgB,gBAAgB;gBACfuI,KAAK,EAAC,0BAAM;gBACZP,KAAK,EAAE7G,KAAK,CAACK,YAAa;gBAC1BwM,UAAU,EAAE;kBAAEjG,KAAK,EAAE;gBAAU;cAAE;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5N,OAAA,CAACpB,GAAG;cAACmQ,IAAI,EAAE,CAAE;cAAAZ,QAAA,eACXnO,OAAA,CAACgB,gBAAgB;gBACfuI,KAAK,EAAC,0BAAM;gBACZP,KAAK,EAAE7G,KAAK,CAACM,aAAc;gBAC3BwM,MAAM,EAAE,KAAK9M,KAAK,CAACK,YAAY,EAAG;gBAClCwM,UAAU,EAAE;kBAAEjG,KAAK,EAAE;gBAAU;cAAE;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5N,OAAA,CAACpB,GAAG;cAACmQ,IAAI,EAAE,CAAE;cAAAZ,QAAA,eACXnO,OAAA,CAACgB,gBAAgB;gBACfuI,KAAK,EAAC,0BAAM;gBACZP,KAAK,EAAE7G,KAAK,CAACO,cAAe;gBAC5BuM,MAAM,EAAE,KAAK9M,KAAK,CAACK,YAAY,EAAG;gBAClCwM,UAAU,EAAE;kBAAEjG,KAAK,EAAE;gBAAU;cAAE;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGX5N,OAAA,CAACa,QAAQ;UAAC0I,KAAK,EAAC,sCAAQ;UAACsF,QAAQ,EAAE,KAAM;UAAC/N,MAAM,EAAC,kBAAkB;UAAAqN,QAAA,EAChEN,eAAe,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGX5N,OAAA,CAACa,QAAQ;UAAC0I,KAAK,EAAC,sCAAQ;UAACsF,QAAQ,EAAE,KAAM;UAAC/N,MAAM,EAAC,kBAAkB;UAAAqN,QAAA,eACjEnO,OAAA;YAAKkP,GAAG,EAAEnM,aAAc;YAACkL,KAAK,EAAE;cAAEnN,MAAM,EAAE,MAAM;cAAEyM,KAAK,EAAE;YAAO;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGrB5N,OAAA,CAACQ,WAAW;QAACE,aAAa,EAAEA,aAAc;QAACC,cAAc,EAAEA;MAAe;QAAA8M,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC,eAGd5N,OAAA,CAACJ,kBAAkB;QACjBwL,QAAQ,EAAC,OAAO;QAChBuD,SAAS,EAAEhO,cAAe;QAC1BiO,UAAU,EAAEA,CAAA,KAAM3L,iBAAiB,CAAC,CAACtC,cAAc,CAAE;QAAAwN,QAAA,gBAGrDnO,OAAA,CAACa,QAAQ;UAAC0I,KAAK,EAAC,0BAAM;UAACsF,QAAQ,EAAE,KAAM;UAAC/N,MAAM,EAAC,KAAK;UAAAqN,QAAA,eAClDnO,OAAA,CAAChB,KAAK;YACJ8O,UAAU,EAAExM,QAAS;YACrB6N,OAAO,EAAE9B,cAAe;YACxB+B,MAAM,EAAC,IAAI;YACXC,UAAU,EAAE,KAAM;YAClBzH,IAAI,EAAC,OAAO;YACZ0H,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAE;YACnBC,KAAK,EAAGC,MAAM,KAAM;cAClBC,OAAO,EAAEA,CAAA,KAAMtC,mBAAmB,CAACqC,MAAM,CAAC;cAC1CxB,KAAK,EAAE;gBACL0B,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,CAAA3N,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4N,EAAE,MAAKJ,MAAM,CAACI,EAAE,GAAG,SAAS,GAAG,aAAa;gBACzEhG,QAAQ,EAAE,MAAM;gBAChBqE,OAAO,EAAE;cACX;YACF,CAAC;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGX5N,OAAA,CAACa,QAAQ;UAAC0I,KAAK,EAAC,sCAAQ;UAACsF,QAAQ,EAAE,KAAM;UAAC/N,MAAM,EAAC,KAAK;UAAAqN,QAAA,EACnDlM,eAAe,gBACdjC,OAAA,CAACf,YAAY;YACX4P,QAAQ;YACRiB,MAAM,EAAE,CAAE;YACVlI,IAAI,EAAC,OAAO;YACZmI,MAAM,EAAE;cACN5E,KAAK,EAAE;gBAAEtB,QAAQ,EAAE,MAAM;gBAAEqE,OAAO,EAAE;cAAU,CAAC;cAC/C8B,OAAO,EAAE;gBAAEnG,QAAQ,EAAE,MAAM;gBAAEqE,OAAO,EAAE;cAAU;YAClD,CAAE;YAAAC,QAAA,gBAEFnO,OAAA,CAACf,YAAY,CAAC+O,IAAI;cAAC7C,KAAK,EAAC,oBAAK;cAAAgD,QAAA,EAAElM,eAAe,CAAC2D;YAAW;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAChF5N,OAAA,CAACf,YAAY,CAAC+O,IAAI;cAAC7C,KAAK,EAAC,cAAI;cAAAgD,QAAA,eAC3BnO,OAAA,CAACb,KAAK;gBACJoE,MAAM,EAAEtB,eAAe,CAACsB,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;gBAClEiG,IAAI,EAAEvH,eAAe,CAACsB,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;cAAK;gBAAAkK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,eACpB5N,OAAA,CAACf,YAAY,CAAC+O,IAAI;cAAC7C,KAAK,EAAC,cAAI;cAAAgD,QAAA,EAC1BlM,eAAe,CAACsB,MAAM,KAAK,QAAQ,GAAGtB,eAAe,CAACyB,GAAG,CAAC2D,OAAO,CAAC,CAAC,CAAC,GAAG;YAAK;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACpB5N,OAAA,CAACf,YAAY,CAAC+O,IAAI;cAAC7C,KAAK,EAAC,cAAI;cAAAgD,QAAA,EAC1BlM,eAAe,CAACsB,MAAM,KAAK,QAAQ,GAAGtB,eAAe,CAACwB,GAAG,CAAC4D,OAAO,CAAC,CAAC,CAAC,GAAG;YAAK;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACpB5N,OAAA,CAACf,YAAY,CAAC+O,IAAI;cAAC7C,KAAK,EAAC,cAAI;cAAAgD,QAAA,EAC1BlM,eAAe,CAACsB,MAAM,KAAK,QAAQ,GAClC,GAAG,OAAOtB,eAAe,CAACuB,KAAK,KAAK,QAAQ,GAAGvB,eAAe,CAACuB,KAAK,CAAC6D,OAAO,CAAC,CAAC,CAAC,GAAGpF,eAAe,CAACuB,KAAK,OAAO,GAC9G;YAAK;cAAAiK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC,eACpB5N,OAAA,CAACf,YAAY,CAAC+O,IAAI;cAAC7C,KAAK,EAAC,oBAAK;cAAAgD,QAAA,EAC3BlM,eAAe,CAACsB,MAAM,KAAK,QAAQ,GAClC,GAAG,OAAOtB,eAAe,CAAC0B,OAAO,KAAK,QAAQ,GAAG1B,eAAe,CAAC0B,OAAO,CAAC0D,OAAO,CAAC,CAAC,CAAC,GAAGpF,eAAe,CAAC0B,OAAO,GAAG,GAChH;YAAK;cAAA8J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,gBAEf5N,OAAA;YAAGiO,KAAK,EAAE;cAAEpE,QAAQ,EAAE;YAAO,CAAE;YAAAsE,QAAA,EAAC;UAAW;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC/C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEX,CAAC;AAACzM,EAAA,CAtyBID,eAAe;AAAA+O,GAAA,GAAf/O,eAAe;AAwyBrB,eAAeA,eAAe;AAAC,IAAAb,EAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAgP,GAAA;AAAAC,YAAA,CAAA7P,EAAA;AAAA6P,YAAA,CAAAtP,GAAA;AAAAsP,YAAA,CAAAnP,GAAA;AAAAmP,YAAA,CAAAjP,GAAA;AAAAiP,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}