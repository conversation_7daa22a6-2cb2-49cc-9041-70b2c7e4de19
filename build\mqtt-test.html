<!DOCTYPE html>
<html>
<head>
  <title>MQTT WebSocket测试</title>
  <script src="https://unpkg.com/mqtt/dist/mqtt.min.js"></script>
</head>
<body>
  <h1>MQTT WebSocket连接测试</h1>
  <div id="status">状态: 未连接</div>
  <div id="messages"></div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const statusEl = document.getElementById('status');
      const messagesEl = document.getElementById('messages');
      
      // 连接MQTT WebSocket
      const client = mqtt.connect('ws://localhost:9001/mqtt', {
        clientId: 'test-client-' + Math.random().toString(16).substr(2, 8),
        clean: true,
        connectTimeout: 30000,
        reconnectPeriod: 5000,
        protocolId: 'MQTT',
        protocolVersion: 4,
        keepalive: 60
      });
      
      // 连接事件
      client.on('connect', function() {
        statusEl.textContent = '状态: 已连接';
        console.log('MQTT连接成功');
        
        // 订阅主题
        client.subscribe('changli/cloud/v2x/rsu/rsm');
        client.subscribe('changli/cloud/v2x/obu/bsm');
        
        // 发送测试消息
        client.publish('test/message', JSON.stringify({
          type: 'test',
          time: Date.now()
        }));
      });
      
      // 消息事件
      client.on('message', function(topic, message) {
        console.log('收到消息:', topic, message.toString());
        const msgEl = document.createElement('div');
        msgEl.textContent = `${topic}: ${message.toString().substring(0, 100)}...`;
        messagesEl.appendChild(msgEl);
      });
      
      // 错误事件
      client.on('error', function(err) {
        statusEl.textContent = `状态: 错误 - ${err.message}`;
        console.error('MQTT错误:', err);
      });
      
      // 重连事件
      client.on('reconnect', function() {
        statusEl.textContent = '状态: 重新连接中...';
        console.log('MQTT重新连接...');
      });
      
      // 断开连接事件
      client.on('close', function() {
        statusEl.textContent = '状态: 已断开连接';
        console.log('MQTT连接已关闭');
      });
    });
  </script>
</body>
</html> 