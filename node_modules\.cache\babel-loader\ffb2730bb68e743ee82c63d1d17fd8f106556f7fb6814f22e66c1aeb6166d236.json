{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.3; // 滤波系数，值越小滤波效果越强（0-1之间）\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n    rsi: 'changli/cloud/v2x/rsu/rsi' // 添加 RSI 主题\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 添加位置滤波函数\nconst filterPosition = newPos => {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n};\n\n// 添加朝向滤波函数\nconst filterRotation = newRotation => {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n  return filteredRotation;\n};\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null); // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 添加事件统计状态\n  const [eventStats, setEventStats] = useState(() => {\n    // 从 localStorage 读取初始状态\n    const savedStats = localStorage.getItem('eventStats');\n    return savedStats ? JSON.parse(savedStats) : {\n      stats: {\n        \"401\": {\n          name: \"道路抛洒物\",\n          count: 0\n        },\n        \"404\": {\n          name: \"道路障碍物\",\n          count: 0\n        },\n        \"405\": {\n          name: \"行人通过马路\",\n          count: 0\n        },\n        \"904\": {\n          name: \"逆行车辆\",\n          count: 0\n        },\n        \"910\": {\n          name: \"违停车辆\",\n          count: 0\n        },\n        \"1002\": {\n          name: \"道路施工\",\n          count: 0\n        },\n        \"901\": {\n          name: \"车辆超速\",\n          count: 0\n        }\n      },\n      lastUpdate: new Date().toISOString()\n    };\n  });\n\n  // 监听事件统计变化，保存到 localStorage\n  useEffect(() => {\n    localStorage.setItem('eventStats', JSON.stringify(eventStats));\n  }, [eventStats]);\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    // const handleMqttMessage = async (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    try {\n      const messageData = JSON.parse(message.toString());\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.topics.rsi && messageData.type === 'RSI') {\n        console.log('收到RSI消息:', messageData);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: messageData.data\n        }, '*');\n        const rsiData = messageData.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n\n        // 更新事件统计\n        try {\n          // 更新统计数据\n          setEventStats(prevStats => {\n            const newStats = {\n              ...prevStats\n            };\n            let hasUpdates = false;\n            events.forEach(event => {\n              const eventType = event.eventType;\n              if (newStats.stats[eventType]) {\n                newStats.stats[eventType].count += 1;\n                hasUpdates = true;\n              }\n            });\n            if (hasUpdates) {\n              newStats.lastUpdate = new Date().toISOString();\n\n              // 广播事件统计更新\n              window.postMessage({\n                type: 'EVENT_STATS_UPDATE',\n                data: newStats\n              }, '*');\n            }\n            return hasUpdates ? newStats : prevStats;\n          });\n        } catch (error) {\n          console.error('更新事件统计失败:', error);\n        }\n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(parseFloat(rsiData.posLong), parseFloat(rsiData.posLat));\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          switch (eventType) {\n            case '401':\n              // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':\n              // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':\n              // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':\n              // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':\n              // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002':\n              // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':\n              // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.topics.scene && messageData.type === 'SCENE') {\n        console.log('收到场景事件消息:', messageData);\n        const sceneData = messageData.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch (sceneType) {\n          case '2':\n            // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':\n            // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':\n            // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':\n            // 限速提醒\n            const speedLimit = sceneData.eventData1; // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':\n            // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':\n            // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':\n            // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':\n            // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':\n            // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':\n            // 信号灯优先\n            const priorityType = sceneData.eventData1; // 优先类型\n            const duration = sceneData.eventData2; // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        return;\n      }\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        var _messageData$data;\n        console.log('收到RSM消息:', messageData);\n        const participants = ((_messageData$data = messageData.data) === null || _messageData$data === void 0 ? void 0 : _messageData$data.participants) || [];\n        const rsuid = messageData.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id = rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = preloadedModel.clone();\n              // 根据类型调整高度\n              const height = type === '3' ? 0.1 : 0.8; // 行人模型贴近地面\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 0.1 : 0.8, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        const bsmData = messageData.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n          const newRotation = Math.PI - newState.heading * Math.PI / 180;\n\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition);\n          const filteredRotation = filterRotation(newRotation);\n\n          // 更新位置和朝向\n          globalVehicleRef.position.copy(filteredPosition);\n          globalVehicleRef.rotation.y = filteredRotation;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n\n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', filteredPosition);\n        }\n        return;\n      }\n\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n        scene.add(model);\n\n        // 在校园模型加载完成后初始化场景\n        await initializeScene();\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 100, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n\n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n\n      // 7. 清理场景\n      if (scene) {\n        while (scene.children.length > 0) {\n          scene.remove(scene.children[0]);\n        }\n      }\n\n      // 8. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      globalVehicleRef = null;\n      console.log('组件清理完成');\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u8DEF\\u53E3\\u9009\\u62E9\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1096,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u8DEF\\u53E3\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      options: intersectionsData.intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1097,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1114,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1113,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"X2t1J6Q4p2oUp1NgmX+oB9NCwjw=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    const [vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`)]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse(child => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n        // // 保留原始贴图\n        // if (child.material.map) {\n        //   newMaterial.map = child.material.map;\n        // }\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse(child => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse(child => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n    console.log('所有模型预加载成功');\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = type => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y); // 设置位置，稍微升高以便可见\n\n  // 为标记添加一个定时器，在5秒后自动移除\n  setTimeout(() => {\n    scene.remove(sprite);\n  }, 500);\n\n  // 将标记添加到场景中\n  scene.add(sprite);\n  console.log('添加场景事件标记:', {\n    位置: position,\n    文本: text,\n    颜色: color\n  });\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "Select", "intersectionsData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "scene", "lastPosition", "lastRotation", "ALPHA", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "topics", "bsm", "rsm", "rsi", "BASE_URL", "vehicleModels", "Map", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "CampusModel", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "eventStats", "setEventStats", "savedStats", "localStorage", "getItem", "JSON", "parse", "stats", "name", "count", "lastUpdate", "Date", "toISOString", "setItem", "stringify", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "find", "i", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "updateMatrix", "updateMatrixWorld", "相机位置", "toArray", "目标点", "handleMqttMessage", "topic", "message", "error", "toString", "messageData", "type", "postMessage", "data", "rsiData", "rsuId", "events", "rtes", "prevStats", "newStats", "hasUpdates", "for<PERSON>ach", "event", "eventType", "eventId", "rteId", "description", "startTime", "endTime", "modelPos", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "事件ID", "事件类型", "事件说明", "开始时间", "结束时间", "位置", "sceneData", "sceneId", "sceneType", "sceneDesc", "partLat", "partLong", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "_messageData$data", "participants", "rsuid", "now", "participant", "id", "partPtcId", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "preloadedModel", "model", "get", "newModel", "height", "rotation", "add", "CLEANUP_THRESHOLD", "currentIds", "Set", "map", "p", "modelData", "has", "remove", "delete", "bsmData", "bsmid", "bsmId", "newState", "newPosition", "Vector3", "filteredPosition", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "payload", "onerror", "onclose", "setTimeout", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "length", "xhr", "loaded", "total", "toFixed", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "clearInterval", "close", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "clear", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "onChange", "options", "label", "size", "bordered", "dropdownStyle", "maxHeight", "ref", "onClick", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "vehicleGltf", "cyclistGltf", "peopleGltf", "all", "loadAsync", "types", "文本", "颜色", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.3; // 滤波系数，值越小滤波效果越强（0-1之间）\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n    rsi: 'changli/cloud/v2x/rsu/rsi'  // 添加 RSI 主题\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 添加位置滤波函数\nconst filterPosition = (newPos) => {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n};\n\n// 添加朝向滤波函数\nconst filterRotation = (newRotation) => {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n  return filteredRotation;\n};\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);  // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加事件统计状态\n  const [eventStats, setEventStats] = useState(() => {\n    // 从 localStorage 读取初始状态\n    const savedStats = localStorage.getItem('eventStats');\n    return savedStats ? JSON.parse(savedStats) : {\n      stats: {\n        \"401\": { name: \"道路抛洒物\", count: 0 },\n        \"404\": { name: \"道路障碍物\", count: 0 },\n        \"405\": { name: \"行人通过马路\", count: 0 },\n        \"904\": { name: \"逆行车辆\", count: 0 },\n        \"910\": { name: \"违停车辆\", count: 0 },\n        \"1002\": { name: \"道路施工\", count: 0 },\n        \"901\": { name: \"车辆超速\", count: 0 }\n      },\n      lastUpdate: new Date().toISOString()\n    };\n  });\n\n  // 监听事件统计变化，保存到 localStorage\n  useEffect(() => {\n    localStorage.setItem('eventStats', JSON.stringify(eventStats));\n  }, [eventStats]);\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n\n  // const handleMqttMessage = async (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    \n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    \n    try {\n      const messageData = JSON.parse(message.toString());\n      \n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.topics.rsi && messageData.type === 'RSI') {\n        console.log('收到RSI消息:', messageData);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: messageData.data\n        }, '*');\n        \n        const rsiData = messageData.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        // 更新事件统计\n        try {\n          // 更新统计数据\n          setEventStats(prevStats => {\n            const newStats = { ...prevStats };\n            let hasUpdates = false;\n            \n            events.forEach(event => {\n              const eventType = event.eventType;\n              if (newStats.stats[eventType]) {\n                newStats.stats[eventType].count += 1;\n                hasUpdates = true;\n              }\n            });\n            \n            if (hasUpdates) {\n              newStats.lastUpdate = new Date().toISOString();\n              \n              // 广播事件统计更新\n              window.postMessage({\n                type: 'EVENT_STATS_UPDATE',\n                data: newStats\n              }, '*');\n            }\n            \n            return hasUpdates ? newStats : prevStats;\n          });\n        } catch (error) {\n          console.error('更新事件统计失败:', error);\n        }\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        \n        return;\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.topics.scene && messageData.type === 'SCENE') {\n        console.log('收到场景事件消息:', messageData);\n        \n        const sceneData = messageData.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      }\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        console.log('收到RSM消息:', messageData);\n        \n        const participants = messageData.data?.participants || [];\n        const rsuid = messageData.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id =  rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n          // 解析位置和状态信息\n          const state = {\n            longitude: parseFloat(participant.partPosLong),\n            latitude: parseFloat(participant.partPosLat),\n            speed: parseFloat(participant.partSpeed),\n            heading: parseFloat(participant.partHeading)\n          };\n          \n          const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n          \n          // 根据类型选择对应的预加载模型\n          let preloadedModel;\n          switch (type) {\n            case '1': // 机动车\n              preloadedModel = preloadedVehicleModel;\n              break;\n            case '2': // 非机动车\n              preloadedModel = preloadedCyclistModel;\n              break;\n            case '3': // 行人\n              preloadedModel = preloadedPeopleModel;\n              break;\n            default:\n              return; // 跳过未知类型\n          }\n          \n          // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n            // 创建新模型实例\n            const newModel = preloadedModel.clone();\n            // 根据类型调整高度\n            const height = type === '3' ? 0.1 : 0.8; // 行人模型贴近地面\n            newModel.position.set(modelPos.x, height, -modelPos.y);\n            newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            scene.add(newModel);\n            \n            vehicleModels.set(id, {\n              model: newModel,\n              lastUpdate: now,\n              type: type\n            });\n          } else if (model) {\n            // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 0.1 : 0.8, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n          }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        \n        const bsmData = messageData.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n          const newRotation = Math.PI - newState.heading * Math.PI / 180;\n          \n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition);\n          const filteredRotation = filterRotation(newRotation);\n          \n          // 更新位置和朝向\n          globalVehicleRef.position.copy(filteredPosition);\n          globalVehicleRef.rotation.y = filteredRotation;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n          \n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', filteredPosition);\n        }\n        return;\n      }\n      \n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          100,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n      \n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n      \n      // 7. 清理场景\n      if (scene) {\n        while(scene.children.length > 0) { \n          scene.remove(scene.children[0]); \n        }\n      }\n      \n      // 8. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      globalVehicleRef = null;\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  return (\n    <>\n      <span style={labelStyle}>路口选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择路口位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    const [vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([\n      loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/people.glb`)\n    ]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n        // // 保留原始贴图\n        // if (child.material.map) {\n        //   newMaterial.map = child.material.map;\n        // }\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    console.log('所有模型预加载成功');\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在5秒后自动移除\n  setTimeout(() => {\n    scene.remove(sprite);\n  }, 500);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  console.log('添加场景事件标记:', {\n    位置: position,\n    文本: text,\n    颜色: color\n  });\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,MAAM;AAC7B,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,GAAG,CAAC,CAAC;;AAEnB;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE;IACNC,GAAG,EAAE,2BAA2B;IAChCC,GAAG,EAAE,2BAA2B;IAChCZ,KAAK,EAAE,6BAA6B;IACpCa,GAAG,EAAE,2BAA2B,CAAE;EACpC;AACF,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,MAAMC,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAIC,MAAM,IAAK;EACjC,IAAI,CAACrB,YAAY,EAAE;IACjBA,YAAY,GAAGqB,MAAM,CAACC,KAAK,CAAC,CAAC;IAC7B,OAAOD,MAAM;EACf;EAEA,MAAME,SAAS,GAAGP,aAAa,CAACK,MAAM,CAACG,CAAC,EAAExB,YAAY,CAACwB,CAAC,EAAEtB,KAAK,CAAC;EAChE,MAAMuB,SAAS,GAAGT,aAAa,CAACK,MAAM,CAACK,CAAC,EAAE1B,YAAY,CAAC0B,CAAC,EAAExB,KAAK,CAAC;EAChE,MAAMyB,SAAS,GAAGX,aAAa,CAACK,MAAM,CAACO,CAAC,EAAE5B,YAAY,CAAC4B,CAAC,EAAE1B,KAAK,CAAC;EAEhEF,YAAY,CAAC6B,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACjD,OAAO3B,YAAY,CAACsB,KAAK,CAAC,CAAC;AAC7B,CAAC;;AAED;AACA,MAAMQ,cAAc,GAAIC,WAAW,IAAK;EACtC,IAAI9B,YAAY,KAAK,IAAI,EAAE;IACzBA,YAAY,GAAG8B,WAAW;IAC1B,OAAOA,WAAW;EACpB;;EAEA;EACA,IAAIC,IAAI,GAAGD,WAAW,GAAG9B,YAAY;EACrC,IAAI+B,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EAExC,MAAMC,gBAAgB,GAAGnB,aAAa,CAACf,YAAY,GAAG+B,IAAI,EAAE/B,YAAY,EAAEC,KAAK,CAAC;EAChFD,YAAY,GAAGkC,gBAAgB;EAC/B,OAAOA,gBAAgB;AACzB,CAAC;AAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAGjE,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMkE,UAAU,GAAGlE,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMmE,SAAS,GAAGnE,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAM+D,aAAa,GAAGpE,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMqE,eAAe,GAAGrE,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMsE,aAAa,GAAGtE,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMuE,iBAAiB,GAAGvE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAE;EACzC,MAAM,CAACwE,eAAe,EAAEC,kBAAkB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC;IAC/C2E,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhF,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMiF,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGnG,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAACoG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAMqG,uBAAuB,GAAG;IAC9BnB,QAAQ,EAAE,OAAO;IACjBoB,GAAG,EAAE,MAAM;IACXlB,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BkB,KAAK,EAAE,OAAO;IAAG;IACjBjB,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMQ,UAAU,GAAG;IACjBtB,QAAQ,EAAE,OAAO;IACjBoB,GAAG,EAAE,MAAM;IACXlB,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBe,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbX,QAAQ,EAAE,MAAM;IAChBY,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvCtB,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAG9G,QAAQ,CAAC,MAAM;IACjD;IACA,MAAM+G,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACrD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG;MAC3CK,KAAK,EAAE;QACL,KAAK,EAAE;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAE,CAAC;QAClC,KAAK,EAAE;UAAED,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAE,CAAC;QAClC,KAAK,EAAE;UAAED,IAAI,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAE,CAAC;QACnC,KAAK,EAAE;UAAED,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAE,CAAC;QACjC,KAAK,EAAE;UAAED,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAE,CAAC;QACjC,MAAM,EAAE;UAAED,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAE,CAAC;QAClC,KAAK,EAAE;UAAED,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAE;MAClC,CAAC;MACDC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACrC,CAAC;EACH,CAAC,CAAC;;EAEF;EACA3H,SAAS,CAAC,MAAM;IACdkH,YAAY,CAACU,OAAO,CAAC,YAAY,EAAER,IAAI,CAACS,SAAS,CAACd,UAAU,CAAC,CAAC;EAChE,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMe,kBAAkB,GAAGA,CAAA,KAAM;IAC/B5C,WAAW,CAAC,QAAQ,CAAC;IACrB5D,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACwG,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B9C,WAAW,CAAC,QAAQ,CAAC;IACrB5D,UAAU,GAAG,QAAQ;IAErB,IAAI8E,SAAS,CAAC6B,OAAO,IAAI1G,QAAQ,EAAE;MACjC;MACA,MAAM2G,UAAU,GAAG9B,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAClC,KAAK,CAAC,CAAC;MACrD,MAAMiF,SAAS,GAAG/B,SAAS,CAAC6B,OAAO,CAACG,EAAE,CAAClF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAI3C,KAAK,CAAC8H,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC;QAAElF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,GAAG;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChC+E,MAAM,CAAChI,KAAK,CAACiI,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvC,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAACwD,IAAI,CAACV,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;MAEV;MACA,IAAItI,KAAK,CAAC8H,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAElF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9B+E,MAAM,CAAChI,KAAK,CAACiI,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvC,SAAS,CAAC6B,OAAO,CAACG,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC;MACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAGvH,QAAQ,CAACwH,MAAM,CAAC7F,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAI3C,KAAK,CAAC8H,KAAK,CAACS,aAAa,CAAC,CAC3BR,EAAE,CAAC;QAAElF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9B+E,MAAM,CAAChI,KAAK,CAACiI,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdpH,QAAQ,CAACwH,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACA1C,SAAS,CAAC6B,OAAO,CAACe,MAAM,CAACzH,QAAQ,CAACwH,MAAM,CAAC;QACzCxH,QAAQ,CAAC0H,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACAtH,QAAQ,CAACwG,OAAO,GAAG,IAAI;;MAEvB;MACAxG,QAAQ,CAAC2H,WAAW,GAAG,EAAE;MACzB3H,QAAQ,CAAC4H,WAAW,GAAG,GAAG;MAC1B5H,QAAQ,CAAC6H,aAAa,GAAGvF,IAAI,CAACC,EAAE,GAAG,GAAG;MACtCvC,QAAQ,CAAC8H,aAAa,GAAG,CAAC;MAC1B9H,QAAQ,CAAC0H,MAAM,CAAC,CAAC;MAEjBK,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAGnJ,iBAAiB,CAACoJ,aAAa,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzC,IAAI,KAAKqC,KAAK,CAAC;IAChF,IAAIC,YAAY,IAAIzD,SAAS,CAAC6B,OAAO,IAAI1G,QAAQ,EAAE;MACjD+E,uBAAuB,CAACuD,YAAY,CAAC;;MAErC;MACA,MAAMI,WAAW,GAAG7F,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAChDC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,EAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC;MAEDwE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvBa,IAAI,EAAEP,YAAY,CAACtC,IAAI;QACvB8C,GAAG,EAAE;UACHxF,SAAS,EAAEgF,YAAY,CAAChF,SAAS;UACjCC,QAAQ,EAAE+E,YAAY,CAAC/E;QACzB,CAAC;QACDwF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACA3I,UAAU,GAAG,cAAc;MAC3B4D,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAkB,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAC3B,GAAG,CAACwG,WAAW,CAAC7G,CAAC,EAAE,GAAG,EAAE,CAAC6G,WAAW,CAAC3G,CAAC,CAAC;;MAElE;MACA/B,QAAQ,CAACwH,MAAM,CAACtF,GAAG,CAACwG,WAAW,CAAC7G,CAAC,EAAE,CAAC,EAAE,CAAC6G,WAAW,CAAC3G,CAAC,CAAC;;MAErD;MACA8C,SAAS,CAAC6B,OAAO,CAACe,MAAM,CAACzH,QAAQ,CAACwH,MAAM,CAAC;;MAEzC;MACAxH,QAAQ,CAACwG,OAAO,GAAG,IAAI;MACvBxG,QAAQ,CAAC0H,MAAM,CAAC,CAAC;;MAEjB;MACA7C,SAAS,CAAC6B,OAAO,CAACsC,YAAY,CAAC,CAAC;MAChCnE,SAAS,CAAC6B,OAAO,CAACuC,iBAAiB,CAAC,IAAI,CAAC;MAEzClB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBa,IAAI,EAAEP,YAAY,CAACtC,IAAI;QACvBkD,IAAI,EAAErE,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAACsF,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAEpJ,QAAQ,CAACwH,MAAM,CAAC2B,OAAO,CAAC,CAAC;QAC9BJ,IAAI,EAAEL;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMW,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAE9C;IACE,IAAI,CAACnJ,KAAK,EAAE;MACV2H,OAAO,CAACyB,KAAK,CAAC,QAAQ,CAAC;MACvB;IACF;IAEAzB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBsB,KAAK;MACLC,OAAO,EAAEA,OAAO,CAACE,QAAQ,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAMC,WAAW,GAAG7D,IAAI,CAACC,KAAK,CAACyD,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;;MAElD;MACA,IAAIH,KAAK,KAAK9I,WAAW,CAACM,MAAM,CAACG,GAAG,IAAIyI,WAAW,CAACC,IAAI,KAAK,KAAK,EAAE;QAClE5B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE0B,WAAW,CAAC;;QAEpC;QACAhJ,MAAM,CAACkJ,WAAW,CAAC;UACjBD,IAAI,EAAE,KAAK;UACXE,IAAI,EAAEH,WAAW,CAACG;QACpB,CAAC,EAAE,GAAG,CAAC;QAEP,MAAMC,OAAO,GAAGJ,WAAW,CAACG,IAAI;QAChC,MAAME,KAAK,GAAGD,OAAO,CAACC,KAAK;QAC3B,MAAMC,MAAM,GAAGF,OAAO,CAACG,IAAI,IAAI,EAAE;;QAEjC;QACA,IAAI;UACF;UACAxE,aAAa,CAACyE,SAAS,IAAI;YACzB,MAAMC,QAAQ,GAAG;cAAE,GAAGD;YAAU,CAAC;YACjC,IAAIE,UAAU,GAAG,KAAK;YAEtBJ,MAAM,CAACK,OAAO,CAACC,KAAK,IAAI;cACtB,MAAMC,SAAS,GAAGD,KAAK,CAACC,SAAS;cACjC,IAAIJ,QAAQ,CAACpE,KAAK,CAACwE,SAAS,CAAC,EAAE;gBAC7BJ,QAAQ,CAACpE,KAAK,CAACwE,SAAS,CAAC,CAACtE,KAAK,IAAI,CAAC;gBACpCmE,UAAU,GAAG,IAAI;cACnB;YACF,CAAC,CAAC;YAEF,IAAIA,UAAU,EAAE;cACdD,QAAQ,CAACjE,UAAU,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;;cAE9C;cACA1F,MAAM,CAACkJ,WAAW,CAAC;gBACjBD,IAAI,EAAE,oBAAoB;gBAC1BE,IAAI,EAAEM;cACR,CAAC,EAAE,GAAG,CAAC;YACT;YAEA,OAAOC,UAAU,GAAGD,QAAQ,GAAGD,SAAS;UAC1C,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOV,KAAK,EAAE;UACdzB,OAAO,CAACyB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACnC;QAEAQ,MAAM,CAACK,OAAO,CAACC,KAAK,IAAI;UACtB,MAAME,OAAO,GAAGF,KAAK,CAACG,KAAK;UAC3B,MAAMF,SAAS,GAAGD,KAAK,CAACC,SAAS;UACjC,MAAMG,WAAW,GAAGJ,KAAK,CAACI,WAAW;UACrC,MAAMC,SAAS,GAAGL,KAAK,CAACK,SAAS;UACjC,MAAMC,OAAO,GAAGN,KAAK,CAACM,OAAO;;UAE7B;UACA,MAAMC,QAAQ,GAAGhI,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAC7CC,UAAU,CAACkB,OAAO,CAACgB,OAAO,CAAC,EAC3BlC,UAAU,CAACkB,OAAO,CAACiB,MAAM,CAC3B,CAAC;;UAED;UACA,IAAIC,WAAW,GAAG,EAAE;UACpB,IAAIC,YAAY,GAAG,EAAE;UAErB,QAAOV,SAAS;YACd,KAAK,KAAK;cAAG;cACXS,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,QAAQ;cACtBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,MAAM;cAAE;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF;cACED,WAAW,GAAGN,WAAW,IAAI,MAAM;cACnCO,YAAY,GAAG,SAAS;UAC5B;;UAEA;UACAC,iBAAiB,CAACL,QAAQ,EAAEG,WAAW,EAAEC,YAAY,CAAC;UAEtDlD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;YACtBmD,IAAI,EAAEX,OAAO;YACbY,IAAI,EAAEb,SAAS;YACfc,IAAI,EAAEX,WAAW;YACjBY,IAAI,EAAEX,SAAS;YACfY,IAAI,EAAEX,OAAO;YACbY,EAAE,EAAEX;UACN,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAIvB,KAAK,KAAK9I,WAAW,CAACM,MAAM,CAACV,KAAK,IAAIsJ,WAAW,CAACC,IAAI,KAAK,OAAO,EAAE;QACtE5B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE0B,WAAW,CAAC;QAErC,MAAM+B,SAAS,GAAG/B,WAAW,CAACG,IAAI;QAClC,MAAM6B,OAAO,GAAGD,SAAS,CAACC,OAAO;QACjC,MAAMC,SAAS,GAAGF,SAAS,CAACE,SAAS;QACrC,MAAMC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAM/H,QAAQ,GAAG;UACfN,QAAQ,EAAEqF,UAAU,CAAC6C,SAAS,CAACI,OAAO,CAAC;UACvCvI,SAAS,EAAEsF,UAAU,CAAC6C,SAAS,CAACK,QAAQ;QAC1C,CAAC;;QAED;QACA,MAAMjB,QAAQ,GAAGhI,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAAC9E,QAAQ,CAACP,SAAS,EAAEO,QAAQ,CAACN,QAAQ,CAAC;;QAEtF;QACA,QAAOoI,SAAS;UACd,KAAK,GAAG;YAAG;YACTT,iBAAiB,CAACL,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;YAClD;UACF,KAAK,KAAK;YAAG;YACXK,iBAAiB,CAACL,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACXK,iBAAiB,CAACL,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;YAC/C;UACF,KAAK,IAAI;YAAG;YACV,MAAMkB,UAAU,GAAGN,SAAS,CAACO,UAAU,CAAC,CAAE;YAC1Cd,iBAAiB,CAACL,QAAQ,EAAE,KAAKkB,UAAU,MAAM,EAAE,SAAS,CAAC;YAC7D;UACF,KAAK,IAAI;YAAG;YACVb,iBAAiB,CAACL,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVK,iBAAiB,CAACL,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YAAG;YACZK,iBAAiB,CAACL,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVK,iBAAiB,CAACL,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVK,iBAAiB,CAACL,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX,MAAMoB,YAAY,GAAGR,SAAS,CAACO,UAAU,CAAC,CAAE;YAC5C,MAAME,QAAQ,GAAGT,SAAS,CAACU,UAAU,CAAC,CAAM;YAC5CjB,iBAAiB,CAACL,QAAQ,EAAE,QAAQuB,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,EAAE,SAAS,CAAC;YAC/F;QACJ;QAEA;MACF;;MAEA;MACA,IAAI5C,KAAK,KAAK9I,WAAW,CAACM,MAAM,CAACE,GAAG,IAAI0I,WAAW,CAACC,IAAI,KAAK,KAAK,EAAE;QAAA,IAAA0C,iBAAA;QAClEtE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE0B,WAAW,CAAC;QAEpC,MAAM4C,YAAY,GAAG,EAAAD,iBAAA,GAAA3C,WAAW,CAACG,IAAI,cAAAwC,iBAAA,uBAAhBA,iBAAA,CAAkBC,YAAY,KAAI,EAAE;QACzD,MAAMC,KAAK,GAAG7C,WAAW,CAACG,IAAI,CAAC0C,KAAK;;QAEpC;QACA,MAAMC,GAAG,GAAGrG,IAAI,CAACqG,GAAG,CAAC,CAAC;;QAEtB;QACAF,YAAY,CAACjC,OAAO,CAACoC,WAAW,IAAI;UAClC;UACA,MAAMC,EAAE,GAAIH,KAAK,GAAGE,WAAW,CAACE,SAAS;UACzC,MAAMhD,IAAI,GAAG8C,WAAW,CAACG,WAAW;UAEpC,IAAGjD,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACA;YACA,MAAMkD,KAAK,GAAG;cACZvJ,SAAS,EAAEsF,UAAU,CAAC6D,WAAW,CAACK,WAAW,CAAC;cAC9CvJ,QAAQ,EAAEqF,UAAU,CAAC6D,WAAW,CAACM,UAAU,CAAC;cAC5CvJ,KAAK,EAAEoF,UAAU,CAAC6D,WAAW,CAACO,SAAS,CAAC;cACxCvJ,OAAO,EAAEmF,UAAU,CAAC6D,WAAW,CAACQ,WAAW;YAC7C,CAAC;YAED,MAAMpC,QAAQ,GAAGhI,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAACkE,KAAK,CAACvJ,SAAS,EAAEuJ,KAAK,CAACtJ,QAAQ,CAAC;;YAEhF;YACA,IAAI2J,cAAc;YAClB,QAAQvD,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRuD,cAAc,GAAGjN,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRiN,cAAc,GAAGhN,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRgN,cAAc,GAAG/M,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACA,IAAIgN,KAAK,GAAGhM,aAAa,CAACiM,GAAG,CAACV,EAAE,CAAC;YAEjC,IAAI,CAACS,KAAK,IAAID,cAAc,EAAE;cAC5B;cACA,MAAMG,QAAQ,GAAGH,cAAc,CAACvL,KAAK,CAAC,CAAC;cACvC;cACA,MAAM2L,MAAM,GAAG3D,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;cACzC0D,QAAQ,CAACxJ,QAAQ,CAAC3B,GAAG,CAAC2I,QAAQ,CAAChJ,CAAC,EAAEyL,MAAM,EAAE,CAACzC,QAAQ,CAAC9I,CAAC,CAAC;cACtDsL,QAAQ,CAACE,QAAQ,CAACxL,CAAC,GAAGO,IAAI,CAACC,EAAE,GAAGsK,KAAK,CAACpJ,OAAO,GAAGnB,IAAI,CAACC,EAAE,GAAG,GAAG;cAC7DnC,KAAK,CAACoN,GAAG,CAACH,QAAQ,CAAC;cAEnBlM,aAAa,CAACe,GAAG,CAACwK,EAAE,EAAE;gBACpBS,KAAK,EAAEE,QAAQ;gBACfnH,UAAU,EAAEsG,GAAG;gBACf7C,IAAI,EAAEA;cACR,CAAC,CAAC;YACJ,CAAC,MAAM,IAAIwD,KAAK,EAAE;cAChB;cACAA,KAAK,CAACA,KAAK,CAACtJ,QAAQ,CAAC3B,GAAG,CAAC2I,QAAQ,CAAChJ,CAAC,EAAEsL,KAAK,CAACxD,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAACkB,QAAQ,CAAC9I,CAAC,CAAC;cACjFoL,KAAK,CAACA,KAAK,CAACI,QAAQ,CAACxL,CAAC,GAAGO,IAAI,CAACC,EAAE,GAAGsK,KAAK,CAACpJ,OAAO,GAAGnB,IAAI,CAACC,EAAE,GAAG,GAAG;cAChE4K,KAAK,CAACjH,UAAU,GAAGsG,GAAG;cACtBW,KAAK,CAACA,KAAK,CAACnE,YAAY,CAAC,CAAC;cAC1BmE,KAAK,CAACA,KAAK,CAAClE,iBAAiB,CAAC,IAAI,CAAC;YACrC;UACA;QACF,CAAC,CAAC;;QAEF;QACA,MAAMwE,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAACrB,YAAY,CAACsB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAClB,SAAS,CAAC,CAAC;QAE9DxL,aAAa,CAACkJ,OAAO,CAAC,CAACyD,SAAS,EAAEpB,EAAE,KAAK;UACvC,IAAIF,GAAG,GAAGsB,SAAS,CAAC5H,UAAU,GAAGuH,iBAAiB,IAAI,CAACC,UAAU,CAACK,GAAG,CAACrB,EAAE,CAAC,EAAE;YACzEtM,KAAK,CAAC4N,MAAM,CAACF,SAAS,CAACX,KAAK,CAAC;YAC7BhM,aAAa,CAAC8M,MAAM,CAACvB,EAAE,CAAC;YACxB3E,OAAO,CAACC,GAAG,CAAC,oBAAoB0E,EAAE,QAAQoB,SAAS,CAACnE,IAAI,EAAE,CAAC;UAC7D;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAIL,KAAK,KAAK9I,WAAW,CAACM,MAAM,CAACC,GAAG,IAAI2I,WAAW,CAACC,IAAI,KAAK,KAAK,EAAE;QAClE5B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE0B,WAAW,CAAC;QAEpC,MAAMwE,OAAO,GAAGxE,WAAW,CAACG,IAAI;QAChC,MAAMsE,KAAK,GAAGD,OAAO,CAACE,KAAK;QAC3B,MAAMC,QAAQ,GAAG;UACf/K,SAAS,EAAEsF,UAAU,CAACsF,OAAO,CAACpC,QAAQ,CAAC;UACvCvI,QAAQ,EAAEqF,UAAU,CAACsF,OAAO,CAACrC,OAAO,CAAC;UACrCrI,KAAK,EAAEoF,UAAU,CAACsF,OAAO,CAAClB,SAAS,CAAC;UACpCvJ,OAAO,EAAEmF,UAAU,CAACsF,OAAO,CAACjB,WAAW;QACzC,CAAC;QAEDlF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEqG,QAAQ,CAAC;QAClCtG,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEmG,KAAK,CAAC;QAC3B;QACA,IAAI3O,gBAAgB,EAAE;UACpB,MAAMqL,QAAQ,GAAGhI,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAAC0F,QAAQ,CAAC/K,SAAS,EAAE+K,QAAQ,CAAC9K,QAAQ,CAAC;UACtF,MAAM+K,WAAW,GAAG,IAAI1P,KAAK,CAAC2P,OAAO,CAAC1D,QAAQ,CAAChJ,CAAC,EAAE,GAAG,EAAE,CAACgJ,QAAQ,CAAC9I,CAAC,CAAC;UACnE,MAAMK,WAAW,GAAGE,IAAI,CAACC,EAAE,GAAG8L,QAAQ,CAAC5K,OAAO,GAAGnB,IAAI,CAACC,EAAE,GAAG,GAAG;;UAE9D;UACA,MAAMiM,gBAAgB,GAAG/M,cAAc,CAAC6M,WAAW,CAAC;UACpD,MAAM9L,gBAAgB,GAAGL,cAAc,CAACC,WAAW,CAAC;;UAEpD;UACA5C,gBAAgB,CAACqE,QAAQ,CAACwD,IAAI,CAACmH,gBAAgB,CAAC;UAChDhP,gBAAgB,CAAC+N,QAAQ,CAACxL,CAAC,GAAGS,gBAAgB;UAC9ChD,gBAAgB,CAACwJ,YAAY,CAAC,CAAC;UAC/BxJ,gBAAgB,CAACyJ,iBAAiB,CAAC,IAAI,CAAC;;UAExC;UACA5F,eAAe,CAACgL,QAAQ,CAAC;UACzBtG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEwG,gBAAgB,CAAC;QAC3C;QACA;MACF;;MAEA;MACAzG,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBsB,KAAK;QACLK,IAAI,EAAED,WAAW,CAACC,IAAI;QACtBE,IAAI,EAAEH;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdzB,OAAO,CAACyB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCzB,OAAO,CAACyB,KAAK,CAAC,SAAS,EAAED,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMgF,cAAc,GAAGA,CAAA,KAAM;IAC3B1G,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAM0G,KAAK,GAAG,QAAQlO,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnEkH,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE0G,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChB9G,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED2G,EAAE,CAACG,SAAS,GAAIxE,KAAK,IAAK;MACxB,IAAI;QACF,MAAMf,OAAO,GAAG1D,IAAI,CAACC,KAAK,CAACwE,KAAK,CAACT,IAAI,CAAC;;QAEtC;QACA,IAAIN,OAAO,CAACI,IAAI,KAAK,SAAS,EAAE;UAC9B5B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEuB,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACI,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIJ,OAAO,CAACI,IAAI,KAAK,SAAS,IAAIJ,OAAO,CAACD,KAAK,IAAIC,OAAO,CAACwF,OAAO,EAAE;UAClE;UACA1F,iBAAiB,CAACE,OAAO,CAACD,KAAK,EAAEzD,IAAI,CAACS,SAAS,CAACiD,OAAO,CAACwF,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOvF,KAAK,EAAE;QACdzB,OAAO,CAACyB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDmF,EAAE,CAACK,OAAO,GAAIxF,KAAK,IAAK;MACtBzB,OAAO,CAACyB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDmF,EAAE,CAACM,OAAO,GAAG,MAAM;MACjBlH,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACAkH,UAAU,CAACT,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACAzL,aAAa,CAAC0D,OAAO,GAAGiI,EAAE;EAC5B,CAAC;EAEDlQ,SAAS,CAAC,MAAM;IACd,IAAI,CAACkE,YAAY,CAAC+D,OAAO,EAAE;;IAE3B;IACAyI,aAAa,CAAC,CAAC;;IAEf;IACA/O,KAAK,GAAG,IAAIxB,KAAK,CAACwQ,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAIzQ,KAAK,CAAC0Q,iBAAiB,CACxC,EAAE,EACF5O,MAAM,CAAC6O,UAAU,GAAG7O,MAAM,CAAC8O,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAACxL,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCmN,MAAM,CAAC5H,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB5C,SAAS,CAAC6B,OAAO,GAAG2I,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAI7Q,KAAK,CAAC8Q,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAAClP,MAAM,CAAC6O,UAAU,EAAE7O,MAAM,CAAC8O,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAACpP,MAAM,CAACqP,gBAAgB,CAAC;IAC/CpN,YAAY,CAAC+D,OAAO,CAACsJ,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAItR,KAAK,CAACuR,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5D/P,KAAK,CAACoN,GAAG,CAAC0C,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAIxR,KAAK,CAACyR,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAACvM,QAAQ,CAAC3B,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1C9B,KAAK,CAACoN,GAAG,CAAC4C,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAI1R,KAAK,CAACyR,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAACzM,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3C9B,KAAK,CAACoN,GAAG,CAAC8C,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAI3R,KAAK,CAAC4R,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAAC1M,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCqO,SAAS,CAACE,KAAK,GAAGnO,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7BgO,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxBxQ,KAAK,CAACoN,GAAG,CAAC+C,SAAS,CAAC;;IAEpB;IACAvQ,QAAQ,GAAG,IAAIlB,aAAa,CAACuQ,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzDjQ,QAAQ,CAAC6Q,aAAa,GAAG,IAAI;IAC7B7Q,QAAQ,CAAC8Q,aAAa,GAAG,IAAI;IAC7B9Q,QAAQ,CAAC+Q,kBAAkB,GAAG,KAAK;IACnC/Q,QAAQ,CAAC2H,WAAW,GAAG,EAAE;IACzB3H,QAAQ,CAAC4H,WAAW,GAAG,GAAG;IAC1B5H,QAAQ,CAAC6H,aAAa,GAAGvF,IAAI,CAACC,EAAE,GAAG,GAAG;IACtCvC,QAAQ,CAAC8H,aAAa,GAAG,CAAC;IAC1B9H,QAAQ,CAACwH,MAAM,CAACtF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BlC,QAAQ,CAAC0H,MAAM,CAAC,CAAC;;IAEjB;IACAK,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBqH,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBrP,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpB6E,SAAS,EAAE,CAAC,CAACA,SAAS,CAAC6B;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMsK,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAIvS,UAAU,CAAC,CAAC;QACtCuS,aAAa,CAACC,IAAI,CAChB,GAAGnQ,QAAQ,uBAAuB,EACjCoQ,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAAClR,KAAK;;UAE/B;UACA,MAAMoR,gBAAgB,GAAG,IAAI5S,KAAK,CAAC6S,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACG,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAIlT,KAAK,CAACmT,oBAAoB,CAAC;kBACjD1M,KAAK,EAAE,QAAQ;kBAAO;kBACtB2M,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIP,KAAK,CAACE,QAAQ,CAACjE,GAAG,EAAE;kBACtBkE,WAAW,CAAClE,GAAG,GAAG+D,KAAK,CAACE,QAAQ,CAACjE,GAAG;gBACtC;;gBAEA;gBACA+D,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5B/J,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE2J,KAAK,CAAC3L,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMuL,YAAY,CAACY,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMT,KAAK,GAAGJ,YAAY,CAACY,QAAQ,CAAC,CAAC,CAAC;YACtCX,gBAAgB,CAAChE,GAAG,CAACmE,KAAK,CAAC;UAC7B;;UAEA;UACAvR,KAAK,CAACoN,GAAG,CAACgE,gBAAgB,CAAC;;UAE3B;UACAhS,gBAAgB,GAAGgS,gBAAgB;UAEnCzJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9B7E,kBAAkB,CAAC,IAAI,CAAC;UACxB+N,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAa,GAAG,IAAK;UACPtK,OAAO,CAACC,GAAG,CAAC,aAAa,CAACqK,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDrB,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMsB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMjB,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACAvC,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAI+C,gBAAgB,EAAE;UACpB,MAAMkB,YAAY,GAAG;YACnBpP,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UAED,MAAMkP,UAAU,GAAG9P,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAAC+J,YAAY,CAACpP,SAAS,EAAEoP,YAAY,CAACnP,QAAQ,CAAC;UAChG;UACAiO,gBAAgB,CAAC3N,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACxCsP,gBAAgB,CAACjE,QAAQ,CAACxL,CAAC,GAAIO,IAAI,CAACC,EAAE,GAAGmQ,YAAY,CAACjP,OAAO,GAAGnB,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9EiP,gBAAgB,CAACxI,YAAY,CAAC,CAAC;UAC/BwI,gBAAgB,CAACvI,iBAAiB,CAAC,IAAI,CAAC;UACxCpJ,eAAe,GAAG2R,gBAAgB,CAAC3N,QAAQ,CAAClC,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAO6H,KAAK,EAAE;QACdzB,OAAO,CAACyB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAMoJ,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAI7B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAM4B,WAAW,GAAIC,WAAW,IAAK;UACnCjL,OAAO,CAACC,GAAG,CAAC,WAAW6K,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAIpU,UAAU,CAAC,CAAC;UAC/BoU,MAAM,CAAC5B,IAAI,CACTwB,GAAG,EACFvB,IAAI,IAAK;YACRvJ,OAAO,CAACC,GAAG,CAAC,WAAW6K,GAAG,EAAE,CAAC;YAC7B3B,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAe,GAAG,IAAK;YACPtK,OAAO,CAACC,GAAG,CAAC,SAAS,CAACqK,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAhJ,KAAK,IAAK;YACTzB,OAAO,CAACyB,KAAK,CAAC,SAASqJ,GAAG,EAAE,EAAErJ,KAAK,CAAC;YACpC,IAAIwJ,WAAW,GAAG,CAAC,EAAE;cACnBjL,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3BkH,UAAU,CAAC,MAAM6D,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACL7B,MAAM,CAAC3H,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAEDuJ,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAIpU,UAAU,CAAC,CAAC;IAC/BoU,MAAM,CAAC5B,IAAI,CACT,GAAGnQ,QAAQ,4BAA4B,EACvC,MAAOoQ,IAAI,IAAK;MACd,IAAI;QACF,MAAMnE,KAAK,GAAGmE,IAAI,CAAClR,KAAK;QACxB+M,KAAK,CAAC+F,KAAK,CAAChR,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBiL,KAAK,CAACtJ,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3B9B,KAAK,CAACoN,GAAG,CAACL,KAAK,CAAC;;QAEhB;QACA,MAAMsF,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC,OAAOjJ,KAAK,EAAE;QACdzB,OAAO,CAACyB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACA6I,GAAG,IAAK;MACPtK,OAAO,CAACC,GAAG,CAAC,SAAS,CAACqK,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAhJ,KAAK,IAAK;MACTzB,OAAO,CAACyB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BzB,OAAO,CAACyB,KAAK,CAAC,OAAO,EAAE;QACrB2J,IAAI,EAAE3J,KAAK,CAACG,IAAI;QAChByJ,IAAI,EAAE5J,KAAK,CAACD,OAAO;QACnB8J,KAAK,EAAE,GAAGnS,QAAQ,4BAA4B;QAC9CoS,KAAK,EAAE,GAAGpS,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMqS,OAAO,GAAGA,CAAA,KAAM;MACpBtQ,iBAAiB,CAACyD,OAAO,GAAG8M,qBAAqB,CAACD,OAAO,CAAC;;MAE1D;MACAvU,KAAK,CAAC0I,MAAM,CAAC,CAAC;MAEd,IAAI3H,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACwG,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMiN,UAAU,GAAGjU,gBAAgB,CAACqE,QAAQ,CAAClC,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAM+R,eAAe,GAAGlU,gBAAgB,CAAC+N,QAAQ,CAACxL,CAAC;;QAEnD;QACA;QACA,MAAM4R,gBAAgB,GAAG,EAAED,eAAe,GAAGpR,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAMqR,YAAY,GAAG,IAAIhV,KAAK,CAAC2P,OAAO,CACpC,CAAC,EAAE,GAAGjM,IAAI,CAACuR,GAAG,CAACF,gBAAgB,CAAC,EAChC,GAAG,EACH,CAAC,EAAE,GAAGrR,IAAI,CAACwR,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACAtE,MAAM,CAACxL,QAAQ,CAACwD,IAAI,CAACoM,UAAU,CAAC,CAACjG,GAAG,CAACoG,YAAY,CAAC;;QAElD;QACAvE,MAAM,CAACxI,EAAE,CAAC3E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAM6R,YAAY,GAAGN,UAAU,CAAC9R,KAAK,CAAC,CAAC;QACvC0N,MAAM,CAAC5H,MAAM,CAACsM,YAAY,CAAC;;QAE3B;QACA1E,MAAM,CAAC2E,sBAAsB,CAAC,CAAC;QAC/B3E,MAAM,CAACrG,YAAY,CAAC,CAAC;QACrBqG,MAAM,CAACpG,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACAjJ,QAAQ,CAACwG,OAAO,GAAG,KAAK;;QAExB;QACAxG,QAAQ,CAACwH,MAAM,CAACH,IAAI,CAACoM,UAAU,CAAC;QAChCzT,QAAQ,CAAC0H,MAAM,CAAC,CAAC;QAEjBK,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnBiM,IAAI,EAAER,UAAU,CAACtK,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAEmG,MAAM,CAACxL,QAAQ,CAACsF,OAAO,CAAC,CAAC;UAC/B+K,IAAI,EAAEH,YAAY,CAAC5K,OAAO,CAAC,CAAC;UAC5BgL,IAAI,EAAE9E,MAAM,CAAC+E,iBAAiB,CAAC,IAAIxV,KAAK,CAAC2P,OAAO,CAAC,CAAC,CAAC,CAACpF,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIpJ,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAACwG,OAAO,GAAG,IAAI;;QAEvB;QACA6I,MAAM,CAACxI,EAAE,CAAC3E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAII,IAAI,CAAC+R,GAAG,CAAChF,MAAM,CAACxL,QAAQ,CAAC9B,CAAC,CAAC,GAAG,EAAE,EAAE;UACpCsN,MAAM,CAACxL,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BlC,QAAQ,CAACwH,MAAM,CAACtF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BmN,MAAM,CAAC5H,MAAM,CAACzH,QAAQ,CAACwH,MAAM,CAAC;UAC9BxH,QAAQ,CAAC0H,MAAM,CAAC,CAAC;QACnB;;QAEA;QACA;QACA2H,MAAM,CAACrG,YAAY,CAAC,CAAC;QACrBqG,MAAM,CAACpG,iBAAiB,CAAC,IAAI,CAAC;MAEhC,CAAC,MAAM,IAAIlJ,UAAU,KAAK,cAAc,EAAE;QACxC;QACAC,QAAQ,CAAC0H,MAAM,CAAC,CAAC;MACnB;MAEA,IAAI1H,QAAQ,EAAEA,QAAQ,CAAC0H,MAAM,CAAC,CAAC;MAC/B,IAAItH,KAAK,IAAIiP,MAAM,EAAE;QACnBI,QAAQ,CAAC6E,MAAM,CAAClU,KAAK,EAAEiP,MAAM,CAAC;MAChC;IACF,CAAC;IAEDkE,OAAO,CAAC,CAAC;;IAET;IACA,MAAMgB,YAAY,GAAGA,CAAA,KAAM;MACzBlF,MAAM,CAACmF,MAAM,GAAG9T,MAAM,CAAC6O,UAAU,GAAG7O,MAAM,CAAC8O,WAAW;MACtDH,MAAM,CAAC2E,sBAAsB,CAAC,CAAC;MAC/BvE,QAAQ,CAACG,OAAO,CAAClP,MAAM,CAAC6O,UAAU,EAAE7O,MAAM,CAAC8O,WAAW,CAAC;IACzD,CAAC;IACD9O,MAAM,CAAC+T,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACA7T,MAAM,CAACgU,aAAa,GAAG,MAAM;MAC3B,IAAI7P,SAAS,CAAC6B,OAAO,EAAE;QACrB7B,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC2C,SAAS,CAAC6B,OAAO,CAACe,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC5C,SAAS,CAAC6B,OAAO,CAACsC,YAAY,CAAC,CAAC;QAChCnE,SAAS,CAAC6B,OAAO,CAACuC,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAIjJ,QAAQ,EAAE;UACZA,QAAQ,CAACwH,MAAM,CAACtF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BlC,QAAQ,CAACwG,OAAO,GAAG,IAAI;UACvBxG,QAAQ,CAAC0H,MAAM,CAAC,CAAC;QACnB;QAEA3H,UAAU,GAAG,QAAQ;QACrBgI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAI/E,iBAAiB,CAACyD,OAAO,EAAE;QAC7BiO,oBAAoB,CAAC1R,iBAAiB,CAACyD,OAAO,CAAC;QAC/CzD,iBAAiB,CAACyD,OAAO,GAAG,IAAI;MAClC;;MAEA;MACA,IAAI/G,oBAAoB,EAAE;QACxBiV,aAAa,CAACjV,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;;MAEA;MACA,IAAIqD,aAAa,CAAC0D,OAAO,EAAE;QACzB1D,aAAa,CAAC0D,OAAO,CAACmO,KAAK,CAAC,CAAC;QAC7B7R,aAAa,CAAC0D,OAAO,GAAG,IAAI;MAC9B;;MAEA;MACAhG,MAAM,CAACoU,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;;MAElD;MACA,IAAI9E,QAAQ,IAAI9M,YAAY,CAAC+D,OAAO,EAAE;QACpC/D,YAAY,CAAC+D,OAAO,CAACqO,WAAW,CAACtF,QAAQ,CAACQ,UAAU,CAAC;QACrDR,QAAQ,CAACuF,OAAO,CAAC,CAAC;MACpB;;MAEA;MACA,IAAI7T,aAAa,EAAE;QACjBA,aAAa,CAACkJ,OAAO,CAAC,CAACyD,SAAS,EAAEpB,EAAE,KAAK;UACvC,IAAIoB,SAAS,CAACX,KAAK,IAAI/M,KAAK,EAAE;YAC5BA,KAAK,CAAC4N,MAAM,CAACF,SAAS,CAACX,KAAK,CAAC;UAC/B;QACF,CAAC,CAAC;QACFhM,aAAa,CAAC8T,KAAK,CAAC,CAAC;MACvB;;MAEA;MACA,IAAI7U,KAAK,EAAE;QACT,OAAMA,KAAK,CAAC+R,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UAC/BhS,KAAK,CAAC4N,MAAM,CAAC5N,KAAK,CAAC+R,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjC;MACF;;MAEA;MACA/R,KAAK,GAAG,IAAI;MACZJ,QAAQ,GAAG,IAAI;MACfC,qBAAqB,GAAG,IAAI;MAC5BC,qBAAqB,GAAG,IAAI;MAC5BC,oBAAoB,GAAG,IAAI;MAC3BX,gBAAgB,GAAG,IAAI;MAEvBuI,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE3I,OAAA,CAAAE,SAAA;IAAA4S,QAAA,gBACE9S,OAAA;MAAM6V,KAAK,EAAE/P,UAAW;MAAAgN,QAAA,EAAC;IAAK;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrCjW,OAAA,CAACH,MAAM;MACLgW,KAAK,EAAElQ,uBAAwB;MAC/BuQ,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAEpN,wBAAyB;MACnCqN,OAAO,EAAEtW,iBAAiB,CAACoJ,aAAa,CAACqF,GAAG,CAACtF,YAAY,KAAK;QAC5DD,KAAK,EAAEC,YAAY,CAACtC,IAAI;QACxB0P,KAAK,EAAEpN,YAAY,CAACtC;MACtB,CAAC,CAAC,CAAE;MACJ2P,IAAI,EAAC,OAAO;MACZC,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACb5R,MAAM,EAAE,IAAI;QACZ6R,SAAS,EAAE;MACb;IAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACFjW,OAAA;MAAK0W,GAAG,EAAEpT,YAAa;MAACuS,KAAK,EAAE;QAAEhQ,KAAK,EAAE,MAAM;QAAEoI,MAAM,EAAE;MAAO;IAAE;MAAA6H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpEjW,OAAA;MAAK6V,KAAK,EAAEtR,oBAAqB;MAAAuO,QAAA,gBAC/B9S,OAAA;QACE6V,KAAK,EAAE;UACL,GAAG9Q,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/E2B,KAAK,EAAE3B,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFsS,OAAO,EAAEzP,kBAAmB;QAAA4L,QAAA,EAC7B;MAED;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjW,OAAA;QACE6V,KAAK,EAAE;UACL,GAAG9Q,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/E2B,KAAK,EAAE3B,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFsS,OAAO,EAAEvP,kBAAmB;QAAA0L,QAAA,EAC7B;MAED;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAA5S,EAAA,CA1hCMD,WAAW;AAAAwT,EAAA,GAAXxT,WAAW;AA2hCjB,SAASyT,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAAClR,KAAK,GAAG,GAAG;EAClBkR,MAAM,CAAC9I,MAAM,GAAG,EAAE;;EAElB;EACAiJ,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAAClR,KAAK,GAAC,CAAC,EAAEkR,MAAM,CAAC9I,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAMuJ,OAAO,GAAG,IAAIjY,KAAK,CAACkY,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAInY,KAAK,CAACoY,cAAc,CAAC;IAC9CpJ,GAAG,EAAEiJ,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAItY,KAAK,CAACuY,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAAChE,KAAK,CAAChR,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAOgV,MAAM;AACf;;AAEA;AACAxW,MAAM,CAAC0W,WAAW,GAAG,CAACvV,CAAC,EAAEE,CAAC,EAAEE,CAAC,KAAK;EAChC,IAAIzC,gBAAgB,EAAE;IACpBA,gBAAgB,CAACqE,QAAQ,CAAC3B,GAAG,CAACL,CAAC,EAAEE,CAAC,EAAEE,CAAC,CAAC;IACtCzC,gBAAgB,CAACwJ,YAAY,CAAC,CAAC;IAC/BxJ,gBAAgB,CAACyJ,iBAAiB,CAAC,IAAI,CAAC;IACxClB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAACnG,CAAC;MAAEE,CAAC;MAAEE;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACAvB,MAAM,CAAC2W,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMhI,MAAM,GAAGgH,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAInI,MAAM,EAAE;MACV;MACA,MAAMoI,MAAM,GAAGpI,MAAM,CAACxL,QAAQ,CAAClC,KAAK,CAAC,CAAC;;MAEtC;MACA0N,MAAM,CAACxL,QAAQ,CAAC3B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BmN,MAAM,CAACxI,EAAE,CAAC3E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBmN,MAAM,CAAC5H,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACA4H,MAAM,CAACrG,YAAY,CAAC,CAAC;MACrBqG,MAAM,CAACpG,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMjJ,QAAQ,GAAGqW,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAI1X,QAAQ,EAAE;QACZA,QAAQ,CAACwH,MAAM,CAACtF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BlC,QAAQ,CAAC0H,MAAM,CAAC,CAAC;MACnB;MAEAK,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxB2P,GAAG,EAAEF,MAAM,CAACtO,OAAO,CAAC,CAAC;QACrByO,GAAG,EAAEvI,MAAM,CAACxL,QAAQ,CAACsF,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAO0O,CAAC,EAAE;IACV9P,OAAO,CAACyB,KAAK,CAAC,YAAY,EAAEqO,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAM1I,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACFpH,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAMiL,MAAM,GAAG,IAAIpU,UAAU,CAAC,CAAC;;IAE/B;IACA,MAAM,CAACiZ,WAAW,EAAEC,WAAW,EAAEC,UAAU,CAAC,GAAG,MAAM/G,OAAO,CAACgH,GAAG,CAAC,CAC/DhF,MAAM,CAACiF,SAAS,CAAC,GAAGhX,QAAQ,uBAAuB,CAAC,EACpD+R,MAAM,CAACiF,SAAS,CAAC,GAAGhX,QAAQ,uBAAuB,CAAC,EACpD+R,MAAM,CAACiF,SAAS,CAAC,GAAGhX,QAAQ,sBAAsB,CAAC,CACpD,CAAC;;IAEF;IACAjB,qBAAqB,GAAG6X,WAAW,CAAC1X,KAAK;IACzCH,qBAAqB,CAACyR,QAAQ,CAAEC,KAAK,IAAK;MACxC,IAAIA,KAAK,CAACC,MAAM,EAAE;QAChBD,KAAK,CAACE,QAAQ,GAAG,IAAIjT,KAAK,CAACmT,oBAAoB,CAAC;UAC9C1M,KAAK,EAAE,QAAQ;UACf2M,SAAS,EAAE,GAAG;UACdC,SAAS,EAAE,GAAG;UACdC,eAAe,EAAE;QACnB,CAAC,CAAC;;QAEF;QACA;QACA;QACA;MACF;IACF,CAAC,CAAC;;IAEF;IACAhS,qBAAqB,GAAG6X,WAAW,CAAC3X,KAAK;IACzC;IACAF,qBAAqB,CAACgT,KAAK,CAAChR,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxC;IACAhC,qBAAqB,CAACwR,QAAQ,CAAEC,KAAK,IAAK;MACxC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClC;QACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;QAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;QAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;MACtC;IACF,CAAC,CAAC;;IAEF;IACA/R,oBAAoB,GAAG6X,UAAU,CAAC5X,KAAK;IACvC;IACAD,oBAAoB,CAAC+S,KAAK,CAAChR,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvC;IACA/B,oBAAoB,CAACuR,QAAQ,CAAEC,KAAK,IAAK;MACvC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClC;QACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;QAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;QAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;MACtC;IACF,CAAC,CAAC;IAEFnK,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,CAAC,OAAOwB,KAAK,EAAE;IACdzB,OAAO,CAACyB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;;AAED;AACA,MAAM4C,mBAAmB,GAAIzC,IAAI,IAAK;EACpC,MAAMwO,KAAK,GAAG;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,KAAK,CAACxO,IAAI,CAAC,IAAI,MAAM;AAC9B,CAAC;;AAED;AACA,MAAMuB,iBAAiB,GAAGA,CAACrH,QAAQ,EAAEsS,IAAI,EAAE9Q,KAAK,KAAK;EACnD;EACA,MAAM6R,MAAM,GAAGhB,gBAAgB,CAACC,IAAI,CAAC;EACrCe,MAAM,CAACrT,QAAQ,CAAC3B,GAAG,CAAC2B,QAAQ,CAAChC,CAAC,EAAE,EAAE,EAAE,CAACgC,QAAQ,CAAC9B,CAAC,CAAC,CAAC,CAAE;;EAEnD;EACAmN,UAAU,CAAC,MAAM;IACf9O,KAAK,CAAC4N,MAAM,CAACkJ,MAAM,CAAC;EACtB,CAAC,EAAE,GAAG,CAAC;;EAEP;EACA9W,KAAK,CAACoN,GAAG,CAAC0J,MAAM,CAAC;EAEjBnP,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;IACvBwD,EAAE,EAAE3H,QAAQ;IACZuU,EAAE,EAAEjC,IAAI;IACRkC,EAAE,EAAEhT;EACN,CAAC,CAAC;AACJ,CAAC;AAED,eAAe5C,WAAW;AAAC,IAAAwT,EAAA;AAAAqC,YAAA,CAAArC,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}