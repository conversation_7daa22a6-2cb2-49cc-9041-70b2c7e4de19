{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\SystemManagement.jsx\",\n  _s = $RefreshSig$();\n// src/pages/SystemManagement.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, message, Card } from 'antd';\nimport { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport DeviceManagement from './DeviceManagement';\nimport styled from 'styled-components';\nimport devicesData from '../data/devices.json';\nimport intersectionsData from '../data/intersections.json';\nimport VehicleManagement from './VehicleManagement';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\n\n// 修改容器样式，确保整个组件在3D模型上层\nconst SystemContainer = styled.div`\n  position: relative;\n  z-index: 1002;\n  background: white;\n  pointer-events: auto;\n`;\n_c = SystemContainer;\nconst TabsContainer = styled.div`\n  display: flex;\n  margin-bottom: 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: white;\n  position: relative;\n  z-index: 1002;\n  pointer-events: auto;\n`;\n_c2 = TabsContainer;\nconst TabButton = styled.button`\n  padding: 10px 20px;\n  background: ${props => props.active ? '#e6f7ff' : 'white'};\n  border: none;\n  border-bottom: 2px solid ${props => props.active ? '#1890ff' : 'transparent'};\n  color: ${props => props.active ? '#1890ff' : 'rgba(0, 0, 0, 0.65)'};\n  cursor: pointer;\n  font-size: 14px;\n  margin-right: 20px;\n  transition: all 0.3s;\n  pointer-events: auto;\n  \n  &:hover {\n    color: #1890ff;\n  }\n  \n  &:focus {\n    outline: none;\n  }\n`;\n_c3 = TabButton;\nconst ContentArea = styled.div`\n  background: white;\n  position: relative;\n  z-index: 1002;\n  pointer-events: auto;\n`;\n_c4 = ContentArea;\nconst SystemManagement = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [users, setUsers] = useState([]);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingUser, setEditingUser] = useState(null);\n  const [activeTab, setActiveTab] = useState('users');\n  const [intersections, setIntersections] = useState([]);\n  const [intersectionModalVisible, setIntersectionModalVisible] = useState(false);\n  const [editingIntersection, setEditingIntersection] = useState(null);\n  const [intersectionForm] = Form.useForm();\n\n  // 获取用户列表\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      console.log('开始获取用户列表...');\n      const token = localStorage.getItem('token');\n      console.log('使用令牌获取用户列表:', token);\n\n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/users`, {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      console.log('API响应:', response.data);\n      if (response.data && response.data.success) {\n        setUsers(response.data.data || []);\n      } else {\n        var _response$data;\n        message.warning('获取用户列表失败: ' + (((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || '未知错误'));\n      }\n    } catch (error) {\n      console.error('API获取用户列表失败:', error);\n      message.error('获取用户列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchUsers();\n\n    // 从 devices.json 中获取唯一的路口名称\n    const uniqueLocations = [...new Set(devicesData.devices.map(device => device.location))];\n\n    // 从 intersections.json 中读取现有的路口数据\n    const existingIntersections = intersectionsData.intersections || [];\n\n    // 将路口名称与已有的路口数据合并\n    const mergedIntersections = uniqueLocations.map(location => {\n      const existingIntersection = existingIntersections.find(i => i.name === location);\n      if (existingIntersection) {\n        return existingIntersection;\n      }\n\n      // 如果是新路口，先保存到 intersections.json\n      const newIntersection = {\n        id: `intersection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        name: location,\n        latitude: '',\n        longitude: '',\n        createdAt: new Date().toISOString()\n      };\n\n      // 调用 API 保存新路口\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      axios.post(`${apiUrl}/api/intersections`, newIntersection).catch(error => {\n        console.error('保存新路口失败:', error);\n      });\n      return newIntersection;\n    });\n    setIntersections(mergedIntersections);\n  }, []);\n\n  // 修改标签切换处理函数\n  const handleTabChange = tab => {\n    console.log('切换到标签:', tab);\n    setActiveTab(tab);\n\n    // 切换到设备管理标签时重新获取设备列表\n    if (tab === 'devices') {\n      const deviceManagementRef = document.querySelector('#device-management');\n      if (deviceManagementRef) {\n        deviceManagementRef.fetchDevices();\n      }\n    }\n  };\n  const handleAddUser = () => {\n    setEditingUser(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEditUser = user => {\n    setEditingUser(user);\n    form.setFieldsValue({\n      username: user.username,\n      role: user.role,\n      email: user.email\n    });\n    setModalVisible(true);\n  };\n  const handleDeleteUser = async userId => {\n    try {\n      const token = localStorage.getItem('token');\n\n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n      // 尝试通过API删除\n      try {\n        await axios.delete(`${apiUrl}/api/users/${userId}`, {\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        message.success('用户已删除');\n        fetchUsers(); // 重新获取用户列表\n      } catch (error) {\n        console.error('API删除用户失败:', error);\n\n        // 如果API删除失败，从本地存储中删除\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        const updatedUsers = localUsers.filter(user => user.id !== userId);\n        localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        message.success('用户已删除（本地存储）');\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('删除用户失败:', error);\n      message.error('删除用户失败: ' + (error.message || '未知错误'));\n    }\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      console.log('表单数据:', values);\n      const token = localStorage.getItem('token');\n\n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n      // 尝试通过API保存用户\n      try {\n        let response;\n        if (editingUser) {\n          // 更新用户\n          response = await axios.put(`${apiUrl}/api/users/${editingUser.id}`, values, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        } else {\n          // 添加用户\n          response = await axios.post(`${apiUrl}/api/users`, values, {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        }\n        console.log('API响应:', response.data);\n        if (response.data && response.data.success) {\n          message.success(editingUser ? '用户已更新' : '用户已添加');\n          setModalVisible(false);\n          form.resetFields();\n          fetchUsers(); // 重新获取用户列表\n        } else {\n          var _response$data2;\n          message.error(((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || '操作失败');\n        }\n      } catch (error) {\n        console.error('API保存用户失败:', error);\n\n        // 如果API保存失败，保存到本地存储\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        if (editingUser) {\n          // 更新用户\n          const updatedUsers = localUsers.map(user => user.id === editingUser.id ? {\n            ...user,\n            ...values\n          } : user);\n          localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        } else {\n          // 添加用户\n          const newUser = {\n            ...values,\n            id: Date.now().toString(),\n            createdAt: new Date().toISOString()\n          };\n          localUsers.push(newUser);\n          localStorage.setItem('localUsers', JSON.stringify(localUsers));\n        }\n        message.success(editingUser ? '用户已更新（本地存储）' : '用户已添加（本地存储）');\n        setModalVisible(false);\n        form.resetFields();\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('保存用户失败:', error);\n      message.error('表单验证失败');\n    }\n  };\n  const handleModalCancel = () => {\n    setModalVisible(false);\n  };\n  const columns = [{\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username'\n  }, {\n    title: '角色',\n    dataIndex: 'role',\n    key: 'role',\n    render: role => {\n      const roleMap = {\n        'admin': '管理员',\n        'monitor': '监控人员',\n        'user': '普通用户',\n        'maintenance': '设备维护人员'\n      };\n      return roleMap[role] || role;\n    }\n  }, {\n    title: '邮箱',\n    dataIndex: 'email',\n    key: 'email'\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    render: date => date ? new Date(date).toLocaleString() : '-'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleEditUser(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        danger: true,\n        onClick: () => handleDeleteUser(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 在组件内添加路口管理相关函数\n  const handleAddIntersection = () => {\n    setEditingIntersection(null);\n    intersectionForm.resetFields();\n    setIntersectionModalVisible(true);\n  };\n  const handleEditIntersection = intersection => {\n    setEditingIntersection(intersection);\n    intersectionForm.setFieldsValue(intersection);\n    setIntersectionModalVisible(true);\n  };\n  const handleDeleteIntersection = async id => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      await axios.delete(`${apiUrl}/api/intersections/${id}`);\n      message.success('路口删除成功');\n      // 更新路口列表\n      const updatedIntersections = intersections.filter(item => item.id !== id);\n      setIntersections(updatedIntersections);\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('删除路口失败:', error);\n      message.error('删除路口失败: ' + (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || '未知错误'));\n    }\n  };\n  const handleIntersectionModalOk = async () => {\n    try {\n      const values = await intersectionForm.validateFields();\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      if (editingIntersection) {\n        // 更新路口\n        const response = await axios.put(`${apiUrl}/api/intersections/${editingIntersection.id}`, values);\n        if (response.data && response.data.success) {\n          message.success('路口更新成功');\n          // 更新路口列表\n          const updatedIntersections = intersections.map(item => item.id === editingIntersection.id ? {\n            ...item,\n            ...values\n          } : item);\n          setIntersections(updatedIntersections);\n        }\n      } else {\n        // 添加新路口\n        const newIntersection = {\n          ...values,\n          id: `intersection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          createdAt: new Date().toISOString()\n        };\n        const response = await axios.post(`${apiUrl}/api/intersections`, newIntersection);\n        if (response.data && response.data.success) {\n          message.success('路口添加成功');\n          setIntersections([...intersections, newIntersection]);\n        }\n      }\n      setIntersectionModalVisible(false);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('保存路口失败:', error);\n      message.error('保存路口失败: ' + (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || '未知错误'));\n    }\n  };\n\n  // 修改 renderContent 函数\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'users':\n        return /*#__PURE__*/_jsxDEV(ContentArea, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: handleAddUser,\n            style: {\n              marginBottom: 16\n            },\n            children: \"\\u6DFB\\u52A0\\u7528\\u6237\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Table, {\n            loading: loading,\n            dataSource: users,\n            columns: columns,\n            rowKey: \"id\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this);\n      case 'devices':\n        return /*#__PURE__*/_jsxDEV(DeviceManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 16\n        }, this);\n      case 'intersections':\n        return /*#__PURE__*/_jsxDEV(ContentArea, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: handleAddIntersection,\n            style: {\n              marginBottom: 16\n            },\n            children: \"\\u6DFB\\u52A0\\u8DEF\\u53E3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Table, {\n            loading: loading,\n            dataSource: intersections,\n            columns: [{\n              title: '路口名称',\n              dataIndex: 'name',\n              key: 'name'\n            }, {\n              title: '纬度',\n              dataIndex: 'latitude',\n              key: 'latitude'\n            }, {\n              title: '经度',\n              dataIndex: 'longitude',\n              key: 'longitude'\n            }, {\n              title: '操作',\n              key: 'action',\n              render: (_, record) => /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  onClick: () => handleEditIntersection(record),\n                  children: \"\\u7F16\\u8F91\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  danger: true,\n                  onClick: () => handleDeleteIntersection(record.id),\n                  children: \"\\u5220\\u9664\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 21\n              }, this)\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this);\n      case 'vehicles':\n        return /*#__PURE__*/_jsxDEV(VehicleManagement, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(SystemContainer, {\n    children: [/*#__PURE__*/_jsxDEV(TabsContainer, {\n      children: [/*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'users',\n        onClick: () => handleTabChange('users'),\n        children: \"\\u7528\\u6237\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'devices',\n        onClick: () => handleTabChange('devices'),\n        children: \"\\u8BBE\\u5907\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'intersections',\n        onClick: () => handleTabChange('intersections'),\n        children: \"\\u8DEF\\u53E3\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TabButton, {\n        active: activeTab === 'vehicles',\n        onClick: () => handleTabChange('vehicles'),\n        children: \"\\u8F66\\u8F86\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 471,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContentArea, {\n      children: renderContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingUser ? \"编辑用户\" : \"添加用户\",\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: handleModalCancel,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          role: 'user'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"username\",\n          label: \"\\u7528\\u6237\\u540D\",\n          rules: [{\n            required: true,\n            message: '请输入用户名'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 28\n            }, this),\n            placeholder: \"\\u7528\\u6237\\u540D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 11\n        }, this), !editingUser && /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          label: \"\\u5BC6\\u7801\",\n          rules: [{\n            required: true,\n            message: '请输入密码'\n          }, {\n            min: 6,\n            message: '密码长度至少为6个字符'\n          }],\n          extra: \"\\u5BC6\\u7801\\u957F\\u5EA6\\u81F3\\u5C11\\u4E3A6\\u4E2A\\u5B57\\u7B26\",\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 39\n            }, this),\n            placeholder: \"\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"role\",\n          label: \"\\u89D2\\u8272\",\n          rules: [{\n            required: true,\n            message: '请选择角色'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u9009\\u62E9\\u89D2\\u8272\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"admin\",\n              children: \"\\u7BA1\\u7406\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"monitor\",\n              children: \"\\u76D1\\u63A7\\u4EBA\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"user\",\n              children: \"\\u666E\\u901A\\u7528\\u6237\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"maintenance\",\n              children: \"\\u8BBE\\u5907\\u7EF4\\u62A4\\u4EBA\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"email\",\n          label: \"\\u90AE\\u7BB1\",\n          rules: [{\n            type: 'email',\n            message: '请输入有效的邮箱地址'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 28\n            }, this),\n            placeholder: \"\\u90AE\\u7BB1\\uFF08\\u9009\\u586B\\uFF09\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 502,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingIntersection ? '编辑路口' : '添加路口',\n      open: intersectionModalVisible,\n      onOk: handleIntersectionModalOk,\n      onCancel: () => setIntersectionModalVisible(false),\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: intersectionForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8DEF\\u53E3\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入路口名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8DEF\\u53E3\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"latitude\",\n          label: \"\\u7EAC\\u5EA6\",\n          rules: [{\n            required: true,\n            message: '请输入纬度'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u7EAC\\u5EA6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"longitude\",\n          label: \"\\u7ECF\\u5EA6\",\n          rules: [{\n            required: true,\n            message: '请输入经度'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u7ECF\\u5EA6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 470,\n    columnNumber: 5\n  }, this);\n};\n_s(SystemManagement, \"rnQW//0AOPAI+JB4m9eK760YeW4=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c5 = SystemManagement;\nexport default SystemManagement;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"SystemContainer\");\n$RefreshReg$(_c2, \"TabsContainer\");\n$RefreshReg$(_c3, \"TabButton\");\n$RefreshReg$(_c4, \"ContentArea\");\n$RefreshReg$(_c5, \"SystemManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "message", "Card", "UserOutlined", "LockOutlined", "MailOutlined", "axios", "DeviceManagement", "styled", "devicesData", "intersectionsData", "VehicleManagement", "jsxDEV", "_jsxDEV", "Option", "SystemContainer", "div", "_c", "TabsContainer", "_c2", "TabButton", "button", "props", "active", "_c3", "ContentArea", "_c4", "SystemManagement", "_s", "loading", "setLoading", "users", "setUsers", "modalVisible", "setModalVisible", "form", "useForm", "editingUser", "setEditingUser", "activeTab", "setActiveTab", "intersections", "setIntersections", "intersectionModalVisible", "setIntersectionModalVisible", "editingIntersection", "setEditingIntersection", "intersectionForm", "fetchUsers", "console", "log", "token", "localStorage", "getItem", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "headers", "data", "success", "_response$data", "warning", "error", "uniqueLocations", "Set", "devices", "map", "device", "location", "existingIntersections", "mergedIntersections", "existingIntersection", "find", "i", "name", "newIntersection", "id", "Date", "now", "Math", "random", "toString", "substr", "latitude", "longitude", "createdAt", "toISOString", "post", "catch", "handleTabChange", "tab", "deviceManagementRef", "document", "querySelector", "fetchDevices", "handleAddUser", "resetFields", "handleEditUser", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "username", "role", "email", "handleDeleteUser", "userId", "delete", "localUsers", "JSON", "parse", "updatedUsers", "filter", "setItem", "stringify", "handleModalOk", "values", "validateFields", "put", "_response$data2", "newUser", "push", "handleModalCancel", "columns", "title", "dataIndex", "key", "render", "roleMap", "date", "toLocaleString", "_", "record", "children", "type", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "danger", "handleAddIntersection", "handleEditIntersection", "intersection", "handleDeleteIntersection", "updatedIntersections", "item", "_error$response", "_error$response$data", "handleIntersectionModalOk", "_error$response2", "_error$response2$data", "renderContent", "style", "marginBottom", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onOk", "onCancel", "destroyOnClose", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "prefix", "placeholder", "min", "extra", "Password", "value", "_c5", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/SystemManagement.jsx"], "sourcesContent": ["// src/pages/SystemManagement.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, message, Card } from 'antd';\nimport { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport DeviceManagement from './DeviceManagement';\nimport styled from 'styled-components';\nimport devicesData from '../data/devices.json';\nimport intersectionsData from '../data/intersections.json';\nimport VehicleManagement from './VehicleManagement';\n\nconst { Option } = Select;\n\n// 修改容器样式，确保整个组件在3D模型上层\nconst SystemContainer = styled.div`\n  position: relative;\n  z-index: 1002;\n  background: white;\n  pointer-events: auto;\n`;\n\nconst TabsContainer = styled.div`\n  display: flex;\n  margin-bottom: 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: white;\n  position: relative;\n  z-index: 1002;\n  pointer-events: auto;\n`;\n\nconst TabButton = styled.button`\n  padding: 10px 20px;\n  background: ${props => props.active ? '#e6f7ff' : 'white'};\n  border: none;\n  border-bottom: 2px solid ${props => props.active ? '#1890ff' : 'transparent'};\n  color: ${props => props.active ? '#1890ff' : 'rgba(0, 0, 0, 0.65)'};\n  cursor: pointer;\n  font-size: 14px;\n  margin-right: 20px;\n  transition: all 0.3s;\n  pointer-events: auto;\n  \n  &:hover {\n    color: #1890ff;\n  }\n  \n  &:focus {\n    outline: none;\n  }\n`;\n\nconst ContentArea = styled.div`\n  background: white;\n  position: relative;\n  z-index: 1002;\n  pointer-events: auto;\n`;\n\nconst SystemManagement = () => {\n  const [loading, setLoading] = useState(true);\n  const [users, setUsers] = useState([]);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingUser, setEditingUser] = useState(null);\n  const [activeTab, setActiveTab] = useState('users');\n  const [intersections, setIntersections] = useState([]);\n  const [intersectionModalVisible, setIntersectionModalVisible] = useState(false);\n  const [editingIntersection, setEditingIntersection] = useState(null);\n  const [intersectionForm] = Form.useForm();\n\n  // 获取用户列表\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      console.log('开始获取用户列表...');\n      \n      const token = localStorage.getItem('token');\n      console.log('使用令牌获取用户列表:', token);\n      \n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/users`, {\n        headers: { \n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      \n      console.log('API响应:', response.data);\n      \n      if (response.data && response.data.success) {\n        setUsers(response.data.data || []);\n      } else {\n        message.warning('获取用户列表失败: ' + (response.data?.message || '未知错误'));\n      }\n    } catch (error) {\n      console.error('API获取用户列表失败:', error);\n      message.error('获取用户列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchUsers();\n    \n    // 从 devices.json 中获取唯一的路口名称\n    const uniqueLocations = [...new Set(devicesData.devices.map(device => device.location))];\n    \n    // 从 intersections.json 中读取现有的路口数据\n    const existingIntersections = intersectionsData.intersections || [];\n    \n    // 将路口名称与已有的路口数据合并\n    const mergedIntersections = uniqueLocations.map(location => {\n      const existingIntersection = existingIntersections.find(i => i.name === location);\n      if (existingIntersection) {\n        return existingIntersection;\n      }\n      \n      // 如果是新路口，先保存到 intersections.json\n      const newIntersection = {\n        id: `intersection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        name: location,\n        latitude: '',\n        longitude: '',\n        createdAt: new Date().toISOString()\n      };\n      \n      // 调用 API 保存新路口\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      axios.post(`${apiUrl}/api/intersections`, newIntersection)\n        .catch(error => {\n          console.error('保存新路口失败:', error);\n        });\n      \n      return newIntersection;\n    });\n    \n    setIntersections(mergedIntersections);\n  }, []);\n\n  // 修改标签切换处理函数\n  const handleTabChange = (tab) => {\n    console.log('切换到标签:', tab);\n    setActiveTab(tab);\n    \n    // 切换到设备管理标签时重新获取设备列表\n    if (tab === 'devices') {\n      const deviceManagementRef = document.querySelector('#device-management');\n      if (deviceManagementRef) {\n        deviceManagementRef.fetchDevices();\n      }\n    }\n  };\n\n  const handleAddUser = () => {\n    setEditingUser(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEditUser = (user) => {\n    setEditingUser(user);\n    form.setFieldsValue({\n      username: user.username,\n      role: user.role,\n      email: user.email,\n    });\n    setModalVisible(true);\n  };\n\n  const handleDeleteUser = async (userId) => {\n    try {\n      const token = localStorage.getItem('token');\n      \n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      \n      // 尝试通过API删除\n      try {\n        await axios.delete(`${apiUrl}/api/users/${userId}`, {\n          headers: { \n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          }\n        });\n        message.success('用户已删除');\n        fetchUsers(); // 重新获取用户列表\n      } catch (error) {\n        console.error('API删除用户失败:', error);\n        \n        // 如果API删除失败，从本地存储中删除\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        const updatedUsers = localUsers.filter(user => user.id !== userId);\n        localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        \n        message.success('用户已删除（本地存储）');\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('删除用户失败:', error);\n      message.error('删除用户失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      console.log('表单数据:', values);\n      \n      const token = localStorage.getItem('token');\n      \n      // 使用完整的 URL\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      \n      // 尝试通过API保存用户\n      try {\n        let response;\n        \n        if (editingUser) {\n          // 更新用户\n          response = await axios.put(`${apiUrl}/api/users/${editingUser.id}`, values, {\n            headers: { \n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        } else {\n          // 添加用户\n          response = await axios.post(`${apiUrl}/api/users`, values, {\n            headers: { \n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        }\n        \n        console.log('API响应:', response.data);\n        \n        if (response.data && response.data.success) {\n          message.success(editingUser ? '用户已更新' : '用户已添加');\n          setModalVisible(false);\n          form.resetFields();\n          fetchUsers(); // 重新获取用户列表\n        } else {\n          message.error(response.data?.message || '操作失败');\n        }\n      } catch (error) {\n        console.error('API保存用户失败:', error);\n        \n        // 如果API保存失败，保存到本地存储\n        const localUsers = JSON.parse(localStorage.getItem('localUsers') || '[]');\n        \n        if (editingUser) {\n          // 更新用户\n          const updatedUsers = localUsers.map(user => \n            user.id === editingUser.id ? { ...user, ...values } : user\n          );\n          localStorage.setItem('localUsers', JSON.stringify(updatedUsers));\n        } else {\n          // 添加用户\n          const newUser = {\n            ...values,\n            id: Date.now().toString(),\n            createdAt: new Date().toISOString()\n          };\n          localUsers.push(newUser);\n          localStorage.setItem('localUsers', JSON.stringify(localUsers));\n        }\n        \n        message.success(editingUser ? '用户已更新（本地存储）' : '用户已添加（本地存储）');\n        setModalVisible(false);\n        form.resetFields();\n        fetchUsers(); // 重新获取用户列表\n      }\n    } catch (error) {\n      console.error('保存用户失败:', error);\n      message.error('表单验证失败');\n    }\n  };\n\n  const handleModalCancel = () => {\n    setModalVisible(false);\n  };\n\n  const columns = [\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n    },\n    {\n      title: '角色',\n      dataIndex: 'role',\n      key: 'role',\n      render: (role) => {\n        const roleMap = {\n          'admin': '管理员',\n          'monitor': '监控人员',\n          'user': '普通用户',\n          'maintenance': '设备维护人员'\n        };\n        return roleMap[role] || role;\n      }\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      render: date => date ? new Date(date).toLocaleString() : '-'\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <div>\n          <Button type=\"link\" onClick={() => handleEditUser(record)}>编辑</Button>\n          <Button type=\"link\" danger onClick={() => handleDeleteUser(record.id)}>删除</Button>\n        </div>\n      ),\n    },\n  ];\n\n  // 在组件内添加路口管理相关函数\n  const handleAddIntersection = () => {\n    setEditingIntersection(null);\n    intersectionForm.resetFields();\n    setIntersectionModalVisible(true);\n  };\n\n  const handleEditIntersection = (intersection) => {\n    setEditingIntersection(intersection);\n    intersectionForm.setFieldsValue(intersection);\n    setIntersectionModalVisible(true);\n  };\n\n  const handleDeleteIntersection = async (id) => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      await axios.delete(`${apiUrl}/api/intersections/${id}`);\n      message.success('路口删除成功');\n      // 更新路口列表\n      const updatedIntersections = intersections.filter(item => item.id !== id);\n      setIntersections(updatedIntersections);\n    } catch (error) {\n      console.error('删除路口失败:', error);\n      message.error('删除路口失败: ' + (error.response?.data?.message || '未知错误'));\n    }\n  };\n\n  const handleIntersectionModalOk = async () => {\n    try {\n      const values = await intersectionForm.validateFields();\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n      if (editingIntersection) {\n        // 更新路口\n        const response = await axios.put(\n          `${apiUrl}/api/intersections/${editingIntersection.id}`,\n          values\n        );\n        if (response.data && response.data.success) {\n          message.success('路口更新成功');\n          // 更新路口列表\n          const updatedIntersections = intersections.map(item =>\n            item.id === editingIntersection.id ? { ...item, ...values } : item\n          );\n          setIntersections(updatedIntersections);\n        }\n      } else {\n        // 添加新路口\n        const newIntersection = {\n          ...values,\n          id: `intersection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          createdAt: new Date().toISOString()\n        };\n        const response = await axios.post(`${apiUrl}/api/intersections`, newIntersection);\n        if (response.data && response.data.success) {\n          message.success('路口添加成功');\n          setIntersections([...intersections, newIntersection]);\n        }\n      }\n      setIntersectionModalVisible(false);\n    } catch (error) {\n      console.error('保存路口失败:', error);\n      message.error('保存路口失败: ' + (error.response?.data?.message || '未知错误'));\n    }\n  };\n\n  // 修改 renderContent 函数\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'users':\n        return (\n          <ContentArea>\n            <Button type=\"primary\" onClick={handleAddUser} style={{ marginBottom: 16 }}>\n              添加用户\n            </Button>\n            <Table\n              loading={loading}\n              dataSource={users}\n              columns={columns}\n              rowKey=\"id\"\n            />\n          </ContentArea>\n        );\n      case 'devices':\n        return <DeviceManagement />;\n      case 'intersections':\n        return (\n          <ContentArea>\n            <Button type=\"primary\" onClick={handleAddIntersection} style={{ marginBottom: 16 }}>\n              添加路口\n            </Button>\n            <Table\n              loading={loading}\n              dataSource={intersections}\n              columns={[\n                {\n                  title: '路口名称',\n                  dataIndex: 'name',\n                  key: 'name',\n                },\n                {\n                  title: '纬度',\n                  dataIndex: 'latitude',\n                  key: 'latitude',\n                },\n                {\n                  title: '经度',\n                  dataIndex: 'longitude',\n                  key: 'longitude',\n                },\n                {\n                  title: '操作',\n                  key: 'action',\n                  render: (_, record) => (\n                    <span>\n                      <Button type=\"link\" onClick={() => handleEditIntersection(record)}>\n                        编辑\n                      </Button>\n                      <Button\n                        type=\"link\"\n                        danger\n                        onClick={() => handleDeleteIntersection(record.id)}\n                      >\n                        删除\n                      </Button>\n                    </span>\n                  ),\n                },\n              ]}\n            />\n          </ContentArea>\n        );\n      case 'vehicles':\n        return <VehicleManagement />;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <SystemContainer>\n      <TabsContainer>\n        <TabButton\n          active={activeTab === 'users'}\n          onClick={() => handleTabChange('users')}\n        >\n          用户管理\n        </TabButton>\n        <TabButton\n          active={activeTab === 'devices'}\n          onClick={() => handleTabChange('devices')}\n        >\n          设备管理\n        </TabButton>\n        <TabButton\n          active={activeTab === 'intersections'}\n          onClick={() => handleTabChange('intersections')}\n        >\n          路口管理\n        </TabButton>\n        <TabButton\n          active={activeTab === 'vehicles'}\n          onClick={() => handleTabChange('vehicles')}\n        >\n          车辆管理\n        </TabButton>\n      </TabsContainer>\n      \n      <ContentArea>\n        {renderContent()}\n      </ContentArea>\n      \n      <Modal\n        title={editingUser ? \"编辑用户\" : \"添加用户\"}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            role: 'user'\n          }}\n        >\n          <Form.Item\n            name=\"username\"\n            label=\"用户名\"\n            rules={[{ required: true, message: '请输入用户名' }]}\n          >\n            <Input prefix={<UserOutlined />} placeholder=\"用户名\" />\n          </Form.Item>\n          \n          {!editingUser && (\n            <Form.Item\n              name=\"password\"\n              label=\"密码\"\n              rules={[\n                { required: true, message: '请输入密码' },\n                { min: 6, message: '密码长度至少为6个字符' }\n              ]}\n              extra=\"密码长度至少为6个字符\"\n            >\n              <Input.Password prefix={<LockOutlined />} placeholder=\"密码\" />\n            </Form.Item>\n          )}\n          \n          <Form.Item\n            name=\"role\"\n            label=\"角色\"\n            rules={[{ required: true, message: '请选择角色' }]}\n          >\n            <Select placeholder=\"选择角色\">\n              <Option value=\"admin\">管理员</Option>\n              <Option value=\"monitor\">监控人员</Option>\n              <Option value=\"user\">普通用户</Option>\n              <Option value=\"maintenance\">设备维护人员</Option>\n            </Select>\n          </Form.Item>\n          \n          <Form.Item\n            name=\"email\"\n            label=\"邮箱\"\n            rules={[\n              { type: 'email', message: '请输入有效的邮箱地址' }\n            ]}\n          >\n            <Input prefix={<MailOutlined />} placeholder=\"邮箱（选填）\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n      \n      {/* 添加路口管理 Modal */}\n      <Modal\n        title={editingIntersection ? '编辑路口' : '添加路口'}\n        open={intersectionModalVisible}\n        onOk={handleIntersectionModalOk}\n        onCancel={() => setIntersectionModalVisible(false)}\n      >\n        <Form\n          form={intersectionForm}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"路口名称\"\n            rules={[{ required: true, message: '请输入路口名称' }]}\n          >\n            <Input placeholder=\"请输入路口名称\" />\n          </Form.Item>\n          <Form.Item\n            name=\"latitude\"\n            label=\"纬度\"\n            rules={[{ required: true, message: '请输入纬度' }]}\n          >\n            <Input placeholder=\"请输入纬度\" />\n          </Form.Item>\n          <Form.Item\n            name=\"longitude\"\n            label=\"经度\"\n            rules={[{ required: true, message: '请输入经度' }]}\n          >\n            <Input placeholder=\"请输入经度\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </SystemContainer>\n  );\n};\n\nexport default SystemManagement;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,QAAQ,MAAM;AAC/E,SAASC,YAAY,EAAEC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC5E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAM;EAAEC;AAAO,CAAC,GAAGd,MAAM;;AAEzB;AACA,MAAMe,eAAe,GAAGP,MAAM,CAACQ,GAAG;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,eAAe;AAOrB,MAAMG,aAAa,GAAGV,MAAM,CAACQ,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GARID,aAAa;AAUnB,MAAME,SAAS,GAAGZ,MAAM,CAACa,MAAM;AAC/B;AACA,gBAAgBC,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,OAAO;AAC3D;AACA,6BAA6BD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,aAAa;AAC9E,WAAWD,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,SAAS,GAAG,qBAAqB;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAnBIJ,SAAS;AAqBf,MAAMK,WAAW,GAAGjB,MAAM,CAACQ,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GALID,WAAW;AAOjB,MAAME,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0C,IAAI,CAAC,GAAGrC,IAAI,CAACsC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC8C,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,OAAO,CAAC;EACnD,MAAM,CAACgD,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkD,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACoD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACsD,gBAAgB,CAAC,GAAGjD,IAAI,CAACsC,OAAO,CAAC,CAAC;;EAEzC;EACA,MAAMY,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFlB,UAAU,CAAC,IAAI,CAAC;MAChBmB,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAE1B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3CJ,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEC,KAAK,CAAC;;MAEjC;MACA,MAAMG,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMpD,KAAK,CAACqD,GAAG,CAAC,GAAGL,MAAM,YAAY,EAAE;QACtDM,OAAO,EAAE;UACP,eAAe,EAAE,UAAUT,KAAK,EAAE;UAClC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEFF,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEQ,QAAQ,CAACG,IAAI,CAAC;MAEpC,IAAIH,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;QAC1C9B,QAAQ,CAAC0B,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACpC,CAAC,MAAM;QAAA,IAAAE,cAAA;QACL9D,OAAO,CAAC+D,OAAO,CAAC,YAAY,IAAI,EAAAD,cAAA,GAAAL,QAAQ,CAACG,IAAI,cAAAE,cAAA,uBAAbA,cAAA,CAAe9D,OAAO,KAAI,MAAM,CAAC,CAAC;MACpE;IACF,CAAC,CAAC,OAAOgE,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpChE,OAAO,CAACgE,KAAK,CAAC,YAAY,IAAIA,KAAK,CAAChE,OAAO,IAAI,MAAM,CAAC,CAAC;IACzD,CAAC,SAAS;MACR6B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDpC,SAAS,CAAC,MAAM;IACdsD,UAAU,CAAC,CAAC;;IAEZ;IACA,MAAMkB,eAAe,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC1D,WAAW,CAAC2D,OAAO,CAACC,GAAG,CAACC,MAAM,IAAIA,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;;IAExF;IACA,MAAMC,qBAAqB,GAAG9D,iBAAiB,CAAC+B,aAAa,IAAI,EAAE;;IAEnE;IACA,MAAMgC,mBAAmB,GAAGP,eAAe,CAACG,GAAG,CAACE,QAAQ,IAAI;MAC1D,MAAMG,oBAAoB,GAAGF,qBAAqB,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKN,QAAQ,CAAC;MACjF,IAAIG,oBAAoB,EAAE;QACxB,OAAOA,oBAAoB;MAC7B;;MAEA;MACA,MAAMI,eAAe,GAAG;QACtBC,EAAE,EAAE,gBAAgBC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3ER,IAAI,EAAEN,QAAQ;QACde,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;MACpC,CAAC;;MAED;MACA,MAAMnC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvEnD,KAAK,CAACoF,IAAI,CAAC,GAAGpC,MAAM,oBAAoB,EAAEwB,eAAe,CAAC,CACvDa,KAAK,CAAC1B,KAAK,IAAI;QACdhB,OAAO,CAACgB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC,CAAC,CAAC;MAEJ,OAAOa,eAAe;IACxB,CAAC,CAAC;IAEFpC,gBAAgB,CAAC+B,mBAAmB,CAAC;EACvC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMmB,eAAe,GAAIC,GAAG,IAAK;IAC/B5C,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE2C,GAAG,CAAC;IAC1BrD,YAAY,CAACqD,GAAG,CAAC;;IAEjB;IACA,IAAIA,GAAG,KAAK,SAAS,EAAE;MACrB,MAAMC,mBAAmB,GAAGC,QAAQ,CAACC,aAAa,CAAC,oBAAoB,CAAC;MACxE,IAAIF,mBAAmB,EAAE;QACvBA,mBAAmB,CAACG,YAAY,CAAC,CAAC;MACpC;IACF;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B5D,cAAc,CAAC,IAAI,CAAC;IACpBH,IAAI,CAACgE,WAAW,CAAC,CAAC;IAClBjE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMkE,cAAc,GAAIC,IAAI,IAAK;IAC/B/D,cAAc,CAAC+D,IAAI,CAAC;IACpBlE,IAAI,CAACmE,cAAc,CAAC;MAClBC,QAAQ,EAAEF,IAAI,CAACE,QAAQ;MACvBC,IAAI,EAAEH,IAAI,CAACG,IAAI;MACfC,KAAK,EAAEJ,IAAI,CAACI;IACd,CAAC,CAAC;IACFvE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMwE,gBAAgB,GAAG,MAAOC,MAAM,IAAK;IACzC,IAAI;MACF,MAAMxD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;MAEvE;MACA,IAAI;QACF,MAAMnD,KAAK,CAACsG,MAAM,CAAC,GAAGtD,MAAM,cAAcqD,MAAM,EAAE,EAAE;UAClD/C,OAAO,EAAE;YACP,eAAe,EAAE,UAAUT,KAAK,EAAE;YAClC,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QACFlD,OAAO,CAAC6D,OAAO,CAAC,OAAO,CAAC;QACxBd,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,OAAOiB,KAAK,EAAE;QACdhB,OAAO,CAACgB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;;QAElC;QACA,MAAM4C,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAC3D,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;QACzE,MAAM2D,YAAY,GAAGH,UAAU,CAACI,MAAM,CAACZ,IAAI,IAAIA,IAAI,CAACtB,EAAE,KAAK4B,MAAM,CAAC;QAClEvD,YAAY,CAAC8D,OAAO,CAAC,YAAY,EAAEJ,IAAI,CAACK,SAAS,CAACH,YAAY,CAAC,CAAC;QAEhE/G,OAAO,CAAC6D,OAAO,CAAC,aAAa,CAAC;QAC9Bd,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BhE,OAAO,CAACgE,KAAK,CAAC,UAAU,IAAIA,KAAK,CAAChE,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;EAED,MAAMmH,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMlF,IAAI,CAACmF,cAAc,CAAC,CAAC;MAC1CrE,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEmE,MAAM,CAAC;MAE5B,MAAMlE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;MAEvE;MACA,IAAI;QACF,IAAIC,QAAQ;QAEZ,IAAIrB,WAAW,EAAE;UACf;UACAqB,QAAQ,GAAG,MAAMpD,KAAK,CAACiH,GAAG,CAAC,GAAGjE,MAAM,cAAcjB,WAAW,CAAC0C,EAAE,EAAE,EAAEsC,MAAM,EAAE;YAC1EzD,OAAO,EAAE;cACP,eAAe,EAAE,UAAUT,KAAK,EAAE;cAClC,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAO,QAAQ,GAAG,MAAMpD,KAAK,CAACoF,IAAI,CAAC,GAAGpC,MAAM,YAAY,EAAE+D,MAAM,EAAE;YACzDzD,OAAO,EAAE;cACP,eAAe,EAAE,UAAUT,KAAK,EAAE;cAClC,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;QACJ;QAEAF,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEQ,QAAQ,CAACG,IAAI,CAAC;QAEpC,IAAIH,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;UAC1C7D,OAAO,CAAC6D,OAAO,CAACzB,WAAW,GAAG,OAAO,GAAG,OAAO,CAAC;UAChDH,eAAe,CAAC,KAAK,CAAC;UACtBC,IAAI,CAACgE,WAAW,CAAC,CAAC;UAClBnD,UAAU,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,MAAM;UAAA,IAAAwE,eAAA;UACLvH,OAAO,CAACgE,KAAK,CAAC,EAAAuD,eAAA,GAAA9D,QAAQ,CAACG,IAAI,cAAA2D,eAAA,uBAAbA,eAAA,CAAevH,OAAO,KAAI,MAAM,CAAC;QACjD;MACF,CAAC,CAAC,OAAOgE,KAAK,EAAE;QACdhB,OAAO,CAACgB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;;QAElC;QACA,MAAM4C,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAC3D,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;QAEzE,IAAIhB,WAAW,EAAE;UACf;UACA,MAAM2E,YAAY,GAAGH,UAAU,CAACxC,GAAG,CAACgC,IAAI,IACtCA,IAAI,CAACtB,EAAE,KAAK1C,WAAW,CAAC0C,EAAE,GAAG;YAAE,GAAGsB,IAAI;YAAE,GAAGgB;UAAO,CAAC,GAAGhB,IACxD,CAAC;UACDjD,YAAY,CAAC8D,OAAO,CAAC,YAAY,EAAEJ,IAAI,CAACK,SAAS,CAACH,YAAY,CAAC,CAAC;QAClE,CAAC,MAAM;UACL;UACA,MAAMS,OAAO,GAAG;YACd,GAAGJ,MAAM;YACTtC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACG,QAAQ,CAAC,CAAC;YACzBI,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;UACpC,CAAC;UACDoB,UAAU,CAACa,IAAI,CAACD,OAAO,CAAC;UACxBrE,YAAY,CAAC8D,OAAO,CAAC,YAAY,EAAEJ,IAAI,CAACK,SAAS,CAACN,UAAU,CAAC,CAAC;QAChE;QAEA5G,OAAO,CAAC6D,OAAO,CAACzB,WAAW,GAAG,aAAa,GAAG,aAAa,CAAC;QAC5DH,eAAe,CAAC,KAAK,CAAC;QACtBC,IAAI,CAACgE,WAAW,CAAC,CAAC;QAClBnD,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BhE,OAAO,CAACgE,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,MAAM0D,iBAAiB,GAAGA,CAAA,KAAM;IAC9BzF,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAM0F,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGxB,IAAI,IAAK;MAChB,MAAMyB,OAAO,GAAG;QACd,OAAO,EAAE,KAAK;QACd,SAAS,EAAE,MAAM;QACjB,MAAM,EAAE,MAAM;QACd,aAAa,EAAE;MACjB,CAAC;MACD,OAAOA,OAAO,CAACzB,IAAI,CAAC,IAAIA,IAAI;IAC9B;EACF,CAAC,EACD;IACEqB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAEE,IAAI,IAAIA,IAAI,GAAG,IAAIlD,IAAI,CAACkD,IAAI,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG;EAC3D,CAAC,EACD;IACEN,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACI,CAAC,EAAEC,MAAM,kBAChBxH,OAAA;MAAAyH,QAAA,gBACEzH,OAAA,CAACjB,MAAM;QAAC2I,IAAI,EAAC,MAAM;QAACC,OAAO,EAAEA,CAAA,KAAMpC,cAAc,CAACiC,MAAM,CAAE;QAAAC,QAAA,EAAC;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACtE/H,OAAA,CAACjB,MAAM;QAAC2I,IAAI,EAAC,MAAM;QAACM,MAAM;QAACL,OAAO,EAAEA,CAAA,KAAM9B,gBAAgB,CAAC2B,MAAM,CAACtD,EAAE,CAAE;QAAAuD,QAAA,EAAC;MAAE;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E;EAET,CAAC,CACF;;EAED;EACA,MAAME,qBAAqB,GAAGA,CAAA,KAAM;IAClChG,sBAAsB,CAAC,IAAI,CAAC;IAC5BC,gBAAgB,CAACoD,WAAW,CAAC,CAAC;IAC9BvD,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAMmG,sBAAsB,GAAIC,YAAY,IAAK;IAC/ClG,sBAAsB,CAACkG,YAAY,CAAC;IACpCjG,gBAAgB,CAACuD,cAAc,CAAC0C,YAAY,CAAC;IAC7CpG,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAMqG,wBAAwB,GAAG,MAAOlE,EAAE,IAAK;IAC7C,IAAI;MACF,MAAMzB,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMnD,KAAK,CAACsG,MAAM,CAAC,GAAGtD,MAAM,sBAAsByB,EAAE,EAAE,CAAC;MACvD9E,OAAO,CAAC6D,OAAO,CAAC,QAAQ,CAAC;MACzB;MACA,MAAMoF,oBAAoB,GAAGzG,aAAa,CAACwE,MAAM,CAACkC,IAAI,IAAIA,IAAI,CAACpE,EAAE,KAAKA,EAAE,CAAC;MACzErC,gBAAgB,CAACwG,oBAAoB,CAAC;IACxC,CAAC,CAAC,OAAOjF,KAAK,EAAE;MAAA,IAAAmF,eAAA,EAAAC,oBAAA;MACdpG,OAAO,CAACgB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BhE,OAAO,CAACgE,KAAK,CAAC,UAAU,IAAI,EAAAmF,eAAA,GAAAnF,KAAK,CAACP,QAAQ,cAAA0F,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBvF,IAAI,cAAAwF,oBAAA,uBAApBA,oBAAA,CAAsBpJ,OAAO,KAAI,MAAM,CAAC,CAAC;IACvE;EACF,CAAC;EAED,MAAMqJ,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI;MACF,MAAMjC,MAAM,GAAG,MAAMtE,gBAAgB,CAACuE,cAAc,CAAC,CAAC;MACtD,MAAMhE,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MAEvE,IAAIZ,mBAAmB,EAAE;QACvB;QACA,MAAMa,QAAQ,GAAG,MAAMpD,KAAK,CAACiH,GAAG,CAC9B,GAAGjE,MAAM,sBAAsBT,mBAAmB,CAACkC,EAAE,EAAE,EACvDsC,MACF,CAAC;QACD,IAAI3D,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;UAC1C7D,OAAO,CAAC6D,OAAO,CAAC,QAAQ,CAAC;UACzB;UACA,MAAMoF,oBAAoB,GAAGzG,aAAa,CAAC4B,GAAG,CAAC8E,IAAI,IACjDA,IAAI,CAACpE,EAAE,KAAKlC,mBAAmB,CAACkC,EAAE,GAAG;YAAE,GAAGoE,IAAI;YAAE,GAAG9B;UAAO,CAAC,GAAG8B,IAChE,CAAC;UACDzG,gBAAgB,CAACwG,oBAAoB,CAAC;QACxC;MACF,CAAC,MAAM;QACL;QACA,MAAMpE,eAAe,GAAG;UACtB,GAAGuC,MAAM;UACTtC,EAAE,EAAE,gBAAgBC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAC3EG,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;QACpC,CAAC;QACD,MAAM/B,QAAQ,GAAG,MAAMpD,KAAK,CAACoF,IAAI,CAAC,GAAGpC,MAAM,oBAAoB,EAAEwB,eAAe,CAAC;QACjF,IAAIpB,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;UAC1C7D,OAAO,CAAC6D,OAAO,CAAC,QAAQ,CAAC;UACzBpB,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAEqC,eAAe,CAAC,CAAC;QACvD;MACF;MACAlC,2BAA2B,CAAC,KAAK,CAAC;IACpC,CAAC,CAAC,OAAOqB,KAAK,EAAE;MAAA,IAAAsF,gBAAA,EAAAC,qBAAA;MACdvG,OAAO,CAACgB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BhE,OAAO,CAACgE,KAAK,CAAC,UAAU,IAAI,EAAAsF,gBAAA,GAAAtF,KAAK,CAACP,QAAQ,cAAA6F,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1F,IAAI,cAAA2F,qBAAA,uBAApBA,qBAAA,CAAsBvJ,OAAO,KAAI,MAAM,CAAC,CAAC;IACvE;EACF,CAAC;;EAED;EACA,MAAMwJ,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQlH,SAAS;MACf,KAAK,OAAO;QACV,oBACE1B,OAAA,CAACY,WAAW;UAAA6G,QAAA,gBACVzH,OAAA,CAACjB,MAAM;YAAC2I,IAAI,EAAC,SAAS;YAACC,OAAO,EAAEtC,aAAc;YAACwD,KAAK,EAAE;cAAEC,YAAY,EAAE;YAAG,CAAE;YAAArB,QAAA,EAAC;UAE5E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/H,OAAA,CAAClB,KAAK;YACJkC,OAAO,EAAEA,OAAQ;YACjB+H,UAAU,EAAE7H,KAAM;YAClB6F,OAAO,EAAEA,OAAQ;YACjBiC,MAAM,EAAC;UAAI;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAElB,KAAK,SAAS;QACZ,oBAAO/H,OAAA,CAACN,gBAAgB;UAAAkI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7B,KAAK,eAAe;QAClB,oBACE/H,OAAA,CAACY,WAAW;UAAA6G,QAAA,gBACVzH,OAAA,CAACjB,MAAM;YAAC2I,IAAI,EAAC,SAAS;YAACC,OAAO,EAAEM,qBAAsB;YAACY,KAAK,EAAE;cAAEC,YAAY,EAAE;YAAG,CAAE;YAAArB,QAAA,EAAC;UAEpF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/H,OAAA,CAAClB,KAAK;YACJkC,OAAO,EAAEA,OAAQ;YACjB+H,UAAU,EAAEnH,aAAc;YAC1BmF,OAAO,EAAE,CACP;cACEC,KAAK,EAAE,MAAM;cACbC,SAAS,EAAE,MAAM;cACjBC,GAAG,EAAE;YACP,CAAC,EACD;cACEF,KAAK,EAAE,IAAI;cACXC,SAAS,EAAE,UAAU;cACrBC,GAAG,EAAE;YACP,CAAC,EACD;cACEF,KAAK,EAAE,IAAI;cACXC,SAAS,EAAE,WAAW;cACtBC,GAAG,EAAE;YACP,CAAC,EACD;cACEF,KAAK,EAAE,IAAI;cACXE,GAAG,EAAE,QAAQ;cACbC,MAAM,EAAEA,CAACI,CAAC,EAAEC,MAAM,kBAChBxH,OAAA;gBAAAyH,QAAA,gBACEzH,OAAA,CAACjB,MAAM;kBAAC2I,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAEA,CAAA,KAAMO,sBAAsB,CAACV,MAAM,CAAE;kBAAAC,QAAA,EAAC;gBAEnE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/H,OAAA,CAACjB,MAAM;kBACL2I,IAAI,EAAC,MAAM;kBACXM,MAAM;kBACNL,OAAO,EAAEA,CAAA,KAAMS,wBAAwB,CAACZ,MAAM,CAACtD,EAAE,CAAE;kBAAAuD,QAAA,EACpD;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAEV,CAAC;UACD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAElB,KAAK,UAAU;QACb,oBAAO/H,OAAA,CAACF,iBAAiB;UAAA8H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9B;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACE/H,OAAA,CAACE,eAAe;IAAAuH,QAAA,gBACdzH,OAAA,CAACK,aAAa;MAAAoH,QAAA,gBACZzH,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEgB,SAAS,KAAK,OAAQ;QAC9BiG,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,OAAO,CAAE;QAAA0C,QAAA,EACzC;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZ/H,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEgB,SAAS,KAAK,SAAU;QAChCiG,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,SAAS,CAAE;QAAA0C,QAAA,EAC3C;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZ/H,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEgB,SAAS,KAAK,eAAgB;QACtCiG,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,eAAe,CAAE;QAAA0C,QAAA,EACjD;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACZ/H,OAAA,CAACO,SAAS;QACRG,MAAM,EAAEgB,SAAS,KAAK,UAAW;QACjCiG,OAAO,EAAEA,CAAA,KAAM5C,eAAe,CAAC,UAAU,CAAE;QAAA0C,QAAA,EAC5C;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEhB/H,OAAA,CAACY,WAAW;MAAA6G,QAAA,EACTmB,aAAa,CAAC;IAAC;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEd/H,OAAA,CAAChB,KAAK;MACJgI,KAAK,EAAExF,WAAW,GAAG,MAAM,GAAG,MAAO;MACrCyH,IAAI,EAAE7H,YAAa;MACnB8H,IAAI,EAAE3C,aAAc;MACpB4C,QAAQ,EAAErC,iBAAkB;MAC5BsC,cAAc;MAAA3B,QAAA,eAEdzH,OAAA,CAACf,IAAI;QACHqC,IAAI,EAAEA,IAAK;QACX+H,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACb3D,IAAI,EAAE;QACR,CAAE;QAAA8B,QAAA,gBAEFzH,OAAA,CAACf,IAAI,CAACsK,IAAI;UACRvF,IAAI,EAAC,UAAU;UACfwF,KAAK,EAAC,oBAAK;UACXC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtK,OAAO,EAAE;UAAS,CAAC,CAAE;UAAAqI,QAAA,eAE/CzH,OAAA,CAACd,KAAK;YAACyK,MAAM,eAAE3J,OAAA,CAACV,YAAY;cAAAsI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAC6B,WAAW,EAAC;UAAK;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,EAEX,CAACvG,WAAW,iBACXxB,OAAA,CAACf,IAAI,CAACsK,IAAI;UACRvF,IAAI,EAAC,UAAU;UACfwF,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAEtK,OAAO,EAAE;UAAQ,CAAC,EACpC;YAAEyK,GAAG,EAAE,CAAC;YAAEzK,OAAO,EAAE;UAAc,CAAC,CAClC;UACF0K,KAAK,EAAC,+DAAa;UAAArC,QAAA,eAEnBzH,OAAA,CAACd,KAAK,CAAC6K,QAAQ;YAACJ,MAAM,eAAE3J,OAAA,CAACT,YAAY;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAC6B,WAAW,EAAC;UAAI;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CACZ,eAED/H,OAAA,CAACf,IAAI,CAACsK,IAAI;UACRvF,IAAI,EAAC,MAAM;UACXwF,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtK,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAqI,QAAA,eAE9CzH,OAAA,CAACb,MAAM;YAACyK,WAAW,EAAC,0BAAM;YAAAnC,QAAA,gBACxBzH,OAAA,CAACC,MAAM;cAAC+J,KAAK,EAAC,OAAO;cAAAvC,QAAA,EAAC;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC/H,OAAA,CAACC,MAAM;cAAC+J,KAAK,EAAC,SAAS;cAAAvC,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrC/H,OAAA,CAACC,MAAM;cAAC+J,KAAK,EAAC,MAAM;cAAAvC,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC/H,OAAA,CAACC,MAAM;cAAC+J,KAAK,EAAC,aAAa;cAAAvC,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ/H,OAAA,CAACf,IAAI,CAACsK,IAAI;UACRvF,IAAI,EAAC,OAAO;UACZwF,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CACL;YAAE/B,IAAI,EAAE,OAAO;YAAEtI,OAAO,EAAE;UAAa,CAAC,CACxC;UAAAqI,QAAA,eAEFzH,OAAA,CAACd,KAAK;YAACyK,MAAM,eAAE3J,OAAA,CAACR,YAAY;cAAAoI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAC6B,WAAW,EAAC;UAAQ;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR/H,OAAA,CAAChB,KAAK;MACJgI,KAAK,EAAEhF,mBAAmB,GAAG,MAAM,GAAG,MAAO;MAC7CiH,IAAI,EAAEnH,wBAAyB;MAC/BoH,IAAI,EAAET,yBAA0B;MAChCU,QAAQ,EAAEA,CAAA,KAAMpH,2BAA2B,CAAC,KAAK,CAAE;MAAA0F,QAAA,eAEnDzH,OAAA,CAACf,IAAI;QACHqC,IAAI,EAAEY,gBAAiB;QACvBmH,MAAM,EAAC,UAAU;QAAA5B,QAAA,gBAEjBzH,OAAA,CAACf,IAAI,CAACsK,IAAI;UACRvF,IAAI,EAAC,MAAM;UACXwF,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtK,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqI,QAAA,eAEhDzH,OAAA,CAACd,KAAK;YAAC0K,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACZ/H,OAAA,CAACf,IAAI,CAACsK,IAAI;UACRvF,IAAI,EAAC,UAAU;UACfwF,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtK,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAqI,QAAA,eAE9CzH,OAAA,CAACd,KAAK;YAAC0K,WAAW,EAAC;UAAO;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACZ/H,OAAA,CAACf,IAAI,CAACsK,IAAI;UACRvF,IAAI,EAAC,WAAW;UAChBwF,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtK,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAAqI,QAAA,eAE9CzH,OAAA,CAACd,KAAK;YAAC0K,WAAW,EAAC;UAAO;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEtB,CAAC;AAAChH,EAAA,CA3hBID,gBAAgB;EAAA,QAIL7B,IAAI,CAACsC,OAAO,EAMAtC,IAAI,CAACsC,OAAO;AAAA;AAAA0I,GAAA,GAVnCnJ,gBAAgB;AA6hBtB,eAAeA,gBAAgB;AAAC,IAAAV,EAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAoJ,GAAA;AAAAC,YAAA,CAAA9J,EAAA;AAAA8J,YAAA,CAAA5J,GAAA;AAAA4J,YAAA,CAAAvJ,GAAA;AAAAuJ,YAAA,CAAArJ,GAAA;AAAAqJ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}