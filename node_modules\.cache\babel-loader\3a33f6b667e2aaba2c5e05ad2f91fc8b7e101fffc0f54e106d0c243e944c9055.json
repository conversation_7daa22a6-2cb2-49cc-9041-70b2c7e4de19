{"ast": null, "code": "\"use client\";\n\nimport BackTop from './BackTop';\nimport FloatButton from './FloatButton';\nimport FloatButtonGroup from './FloatButtonGroup';\nimport PurePanel from './PurePanel';\nFloatButton.BackTop = BackTop;\nFloatButton.Group = FloatButtonGroup;\nFloatButton._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nexport default FloatButton;", "map": {"version": 3, "names": ["BackTop", "FloatButton", "FloatButtonGroup", "PurePanel", "Group", "_InternalPanelDoNotUseOrYouWillBeFired"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/float-button/index.js"], "sourcesContent": ["\"use client\";\n\nimport BackTop from './BackTop';\nimport FloatButton from './FloatButton';\nimport FloatButtonGroup from './FloatButtonGroup';\nimport PurePanel from './PurePanel';\nFloatButton.BackTop = BackTop;\nFloatButton.Group = FloatButtonGroup;\nFloatButton._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nexport default FloatButton;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,OAAO,MAAM,WAAW;AAC/B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,SAAS,MAAM,aAAa;AACnCF,WAAW,CAACD,OAAO,GAAGA,OAAO;AAC7BC,WAAW,CAACG,KAAK,GAAGF,gBAAgB;AACpCD,WAAW,CAACI,sCAAsC,GAAGF,SAAS;AAC9D,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}