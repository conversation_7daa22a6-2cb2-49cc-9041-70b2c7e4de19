{"ast": null, "code": "import React,{useState,useEffect,useRef,useCallback}from'react';import{Row,Col,Card,Statistic,List,Table,Descriptions,Spin,Badge,Button}from'antd';import*as echarts from'echarts/core';import{Bar<PERSON>hart}from'echarts/charts';import{GridComponent,TooltipComponent,LegendComponent,TitleComponent}from'echarts/components';import{CanvasRenderer}from'echarts/renderers';import styled from'styled-components';import CollapsibleSidebar from'../components/layout/CollapsibleSidebar';import axios from'axios';import vehiclesData from'../data/vehicles.json';import intersectionsData from'../data/intersections.json';// 导入路口数据\n// 注册必要的echarts组件\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";echarts.use([Bar<PERSON>hart,GridComponent,TooltipComponent,LegendComponent,TitleComponent,Canvas<PERSON>enderer]);const StyledCanvas=styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;// 页面布局容器\nconst PageContainer=styled.div`\n  display: flex;\n  height: calc(100vh - 65px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;// 左侧信息栏容器\nconst LeftSidebar=styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;// 右侧信息栏容器\nconst RightSidebar=styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;// 主内容区域\nconst MainContent=styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props=>props.leftCollapsed&&props.rightCollapsed?'0 24px':props.leftCollapsed?'0 8px 0 24px':props.rightCollapsed?'0 24px 0 8px':'0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props=>props.leftCollapsed&&props.rightCollapsed?'0 24px':props.leftCollapsed?'0 8px 0 0':props.rightCollapsed?'0 0 0 8px':'0'};\n  position: relative;\n  z-index: 1;\n`;// 信息卡片\nconst InfoCard=styled(Card)`\n  margin-bottom: 12px;\n  height: ${props=>props.height||'auto'};\n\n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n\n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n\n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n`;// 自定义统计数字组件\nconst CompactStatistic=styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n\n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;const RealTimeTraffic=()=>{const[loading,setLoading]=useState(true);const[vehicles,setVehicles]=useState([]);const[events,setEvents]=useState([]);const[selectedVehicle,setSelectedVehicle]=useState(null);const[stats,setStats]=useState({totalVehicles:0,onlineVehicles:0,offlineVehicles:0,totalDevices:0,onlineDevices:0,offlineDevices:0});// 存储在线车辆的 BSM ID，初始时为空集合，表示所有车辆离线\nconst[onlineBsmIds,setOnlineBsmIds]=useState(()=>{try{const savedOnlineIds=localStorage.getItem('realTimeTrafficOnlineIds');// 返回空集合，确保所有车辆初始状态为离线\nreturn new Set();}catch(error){console.error('读取在线ID缓存失败:',error);return new Set();}});// 存储最后一次收到 BSM 消息的时间\nconst lastBsmTime=useRef({});const eventChartRef=useRef(null);// 添加侧边栏折叠状态\nconst[leftCollapsed,setLeftCollapsed]=useState(false);const[rightCollapsed,setRightCollapsed]=useState(false);const[eventStats,setEventStats]=useState({'401':0,// 道路抛洒物\n'404':0,// 道路障碍物\n'405':0,// 行人通过马路\n'904':0,// 逆行车辆\n'910':0,// 违停车辆\n'1002':0,// 道路施工\n'901':0// 车辆超速\n});// 添加RSI事件缓存，用于检测重复事件\nconst prevRsiEvents=useRef(new Map());// 事件列表缓存，存储所有事件的完整信息\nconst eventListCache=useRef([]);// 事件ID计数器\nconst eventIdCounter=useRef(1);// ========== 数据库API调用函数 ==========\n/**\n   * 存储实时事件到数据库\n   */const storeEventToDatabase=async eventData=>{try{const apiUrl=process.env.REACT_APP_API_URL||'http://localhost:5000';const response=await axios.post(`${apiUrl}/api/events/store`,eventData);if(response.data&&response.data.success){console.log(`✅ 事件已存储到${response.data.storage}:`,eventData.eventTypeText);return true;}else{console.error('❌ 存储事件失败:',response.data);return false;}}catch(error){console.error('❌ 存储事件到数据库失败:',error);return false;}};/**\n   * 从数据库获取事件统计\n   */const fetchEventStatsFromDatabase=async()=>{try{const apiUrl=process.env.REACT_APP_API_URL||'http://localhost:5000';const response=await axios.get(`${apiUrl}/api/events/stats?timeRange=24h`);if(response.data&&response.data.success){console.log(`📊 从${response.data.storage}获取事件统计:`,response.data.data);return response.data.data;}else{console.error('❌ 获取事件统计失败:',response.data);return null;}}catch(error){console.error('❌ 从数据库获取事件统计失败:',error);return null;}};/**\n   * 从数据库获取最近事件\n   */const fetchRecentEventsFromDatabase=async function(){let limit=arguments.length>0&&arguments[0]!==undefined?arguments[0]:10;try{const apiUrl=process.env.REACT_APP_API_URL||'http://localhost:5000';const response=await axios.get(`${apiUrl}/api/events/recent?limit=${limit}`);if(response.data&&response.data.success){console.log(`📋 从${response.data.storage}获取最近事件:`,response.data.data.length,'条');return response.data.data;}else{console.error('❌ 获取最近事件失败:',response.data);return[];}}catch(error){console.error('❌ 从数据库获取最近事件失败:',error);return[];}};// 添加手动更新车辆状态和位置信息的函数\nconst updateVehicleStatus=useCallback(function(bsmId,status){let speed=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;let lat=arguments.length>3&&arguments[3]!==undefined?arguments[3]:0;let lng=arguments.length>4&&arguments[4]!==undefined?arguments[4]:0;let heading=arguments.length>5&&arguments[5]!==undefined?arguments[5]:0;// 确保速度和航向角是格式化的数值，保留两位小数\nconst formattedSpeed=parseFloat(speed).toFixed(0);const formattedHeading=parseFloat(heading).toFixed(2);console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${formattedSpeed}, 位置=(${lat}, ${lng})`);// 更新在线状态\nif(status==='online'){setOnlineBsmIds(prev=>new Set([...prev,bsmId]));lastBsmTime.current[bsmId]=Date.now();}else{setOnlineBsmIds(prev=>{const newSet=new Set(prev);newSet.delete(bsmId);return newSet;});}// 更新车辆信息\nsetVehicles(prevVehicles=>prevVehicles.map(vehicle=>vehicle.bsmId===bsmId?{...vehicle,status:status,speed:parseFloat(formattedSpeed),// 确保是数值类型\nlat:parseFloat(lat.toFixed(7)),lng:parseFloat(lng.toFixed(7)),heading:parseFloat(formattedHeading)// 确保是数值类型\n}:vehicle));},[]);// 在组件挂载时，暴露updateVehicleStatus函数到window对象\nuseEffect(()=>{window.updateVehicleStatus=updateVehicleStatus;// 用于监听CampusModel是否接收到BSM消息的事件\nconst handleRealBsmReceived=event=>{if(event.data&&event.data.type==='realBsmReceived'){// console.log('收到CampusModel发送的真实BSM消息通知');\n}};window.addEventListener('message',handleRealBsmReceived);// 确保页面刷新时清空在线车辆状态\nconsole.log('组件初始化，重置所有车辆为离线状态');setOnlineBsmIds(new Set());lastBsmTime.current={};return()=>{window.removeEventListener('message',handleRealBsmReceived);delete window.updateVehicleStatus;};},[updateVehicleStatus]);// 从数据库加载事件数据\nconst loadEventsFromDatabase=async()=>{try{// 加载最近事件\nconst recentEvents=await fetchRecentEventsFromDatabase(10);if(recentEvents&&recentEvents.length>0){setEvents(recentEvents);console.log('✅ 从数据库加载了',recentEvents.length,'条最近事件');}// 加载事件统计\nconst stats=await fetchEventStatsFromDatabase();if(stats){setEventStats(prevStats=>({...prevStats,...stats}));console.log('✅ 从数据库加载了事件统计数据');}}catch(error){console.error('❌ 从数据库加载事件数据失败:',error);}};// 组件挂载时从数据库加载数据\nuseEffect(()=>{loadEventsFromDatabase();},[]);// 定期从数据库刷新数据（每30秒）\nuseEffect(()=>{const refreshInterval=setInterval(()=>{console.log('🔄 定期刷新事件数据...');loadEventsFromDatabase();},30000);// 30秒刷新一次\nreturn()=>clearInterval(refreshInterval);},[]);// 定期清理过期事件（每5分钟）\nuseEffect(()=>{const cleanupInterval=setInterval(()=>{cleanupExpiredEvents();},300000);// 5分钟清理一次\nreturn()=>clearInterval(cleanupInterval);},[]);// 定期删除1分钟内没有更新的事件（每30秒检查一次）\nuseEffect(()=>{const removeInactiveInterval=setInterval(()=>{removeInactiveEvents();},30000);// 30秒检查一次\nreturn()=>clearInterval(removeInactiveInterval);},[]);// 获取车辆数据\nconst fetchVehicles=useCallback(async()=>{try{// 先尝试从API获取最新数据\nconsole.log('尝试从API获取最新车辆数据');const apiData=await fetchLatestVehiclesData(true);// 如果API获取成功，直接使用API数据\nif(apiData&&apiData.length>0){console.log('成功从API获取车辆数据，车辆数量:',apiData.length);// 获取所有车辆的BSM ID列表\nconst bsmIds=apiData.map(v=>v.bsmId).filter(id=>id);console.log('所有车辆的BSM ID:',bsmIds);// 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\nconst updatedVehicles=apiData.map(vehicle=>{// 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\nconst isOnline=vehicle.bsmId&&onlineBsmIds.has(vehicle.bsmId);return{...vehicle,plate:vehicle.plateNumber,// 适配表格显示\nstatus:isOnline?'online':'offline',// 只有收到BSM消息的车辆才显示为在线\nspeed:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.speed||0:0:0,lat:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.lat||0:0:0,lng:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.lng||0:0:0,heading:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.heading||0:0:0};});setVehicles(updatedVehicles);// 直接检查更新后的车辆在线状态\nconst onlineCount=updatedVehicles.filter(v=>v.status==='online').length;const totalCount=updatedVehicles.length;console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount-onlineCount}`);// 更新车辆统计数据\nsetStats(prevStats=>({...prevStats,totalVehicles:totalCount,onlineVehicles:onlineCount,offlineVehicles:totalCount-onlineCount}));return;}// 如果API获取失败，回退到本地JSON文件\nconsole.log('API获取失败，回退到本地vehiclesData');const vehiclesList=vehiclesData.vehicles||[];console.log('从vehiclesData获取车辆数据，车辆数量:',vehiclesList.length);// 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\nconst updatedVehicles=vehiclesList.map(vehicle=>{// 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\nconst isOnline=vehicle.bsmId&&onlineBsmIds.has(vehicle.bsmId);return{...vehicle,plate:vehicle.plateNumber,// 适配表格显示\nstatus:isOnline?'online':'offline',// 只有收到BSM消息的车辆才显示为在线\nspeed:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.speed||0:0:0,lat:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.lat||0:0:0,lng:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.lng||0:0:0,heading:isOnline?lastBsmTime.current[vehicle.bsmId]?vehicle.heading||0:0:0};});setVehicles(updatedVehicles);// 直接检查更新后的车辆在线状态\nconst onlineCount=updatedVehicles.filter(v=>v.status==='online').length;const totalCount=updatedVehicles.length;console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount-onlineCount}`);// 更新车辆统计数据\nsetStats(prevStats=>({...prevStats,totalVehicles:totalCount,onlineVehicles:onlineCount,offlineVehicles:totalCount-onlineCount}));}catch(error){console.error('获取车辆列表失败:',error);}},[onlineBsmIds]);// 获取设备统计数据\nconst fetchDeviceStats=async()=>{try{const apiUrl=process.env.REACT_APP_API_URL||'http://localhost:5000';const response=await axios.get(`${apiUrl}/api/devices`);if(response.data&&response.data.success){const devicesData=response.data.data;// 更新设备统计数据\nsetStats(prevStats=>({...prevStats,totalDevices:devicesData.length,onlineDevices:devicesData.filter(d=>d.status==='online').length,offlineDevices:devicesData.filter(d=>d.status==='offline').length}));}}catch(error){console.error('获取设备统计数据失败:',error);}};// 监听 BSM 消息\nuseEffect(()=>{const handleBsmMessage=event=>{if(event.data&&event.data.type==='bsm'){// 获取bsmId，确保它正确地从消息中提取\nconst bsmData=event.data.data||{};const bsmId=bsmData.bsmId||event.data.bsmId;if(!bsmId){console.error('BSM消息缺少bsmId:',event.data);return;}// console.log('收到BSM消息，ID:', bsmId);\nconst now=Date.now();// 更新最后接收时间\nlastBsmTime.current[bsmId]=now;// 添加到在线bsmId集合\nsetOnlineBsmIds(prev=>new Set([...prev,bsmId]));// 提取正确的BSM数据并格式化为两位小数\nconst speed=parseFloat((parseFloat(bsmData.partSpeed||event.data.speed||0)*3.6).toFixed(0));// 转换为km/h，保留两位小数\nconst lat=parseFloat(parseFloat(bsmData.partLat||event.data.lat||0).toFixed(7));const lng=parseFloat(parseFloat(bsmData.partLong||event.data.lng||0).toFixed(7));const heading=parseFloat(parseFloat(bsmData.partHeading||event.data.heading||0).toFixed(2));// 保留两位小数\n// console.log(`车辆${bsmId}状态: 速度=${speed.toFixed(2)}km/h, 位置=(${lat.toFixed(7)}, ${lng.toFixed(7)}), 航向=${heading.toFixed(2)}°`);\n// 更新车辆状态和位置信息\nsetVehicles(prevVehicles=>{// 检查是否找到对应车辆\nconst foundVehicle=prevVehicles.find(v=>v.bsmId===bsmId);if(!foundVehicle){console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);return prevVehicles;}const updatedVehicles=prevVehicles.map(vehicle=>vehicle.bsmId===bsmId?{...vehicle,status:'online',speed:speed,lat:lat,lng:lng,heading:heading}:vehicle);// 计算更新后的在线车辆数量\nconst onlineCount=updatedVehicles.filter(v=>v.status==='online').length;const totalCount=updatedVehicles.length;// 更新统计数据\nsetStats(prevStats=>({...prevStats,onlineVehicles:onlineCount,offlineVehicles:totalCount-onlineCount}));return updatedVehicles;});}};// 添加消息监听器\nwindow.addEventListener('message',handleBsmMessage);// 清理函数\nreturn()=>{window.removeEventListener('message',handleBsmMessage);};},[]);// 修改检查在线状态的useEffect，同步更新统计信息\nuseEffect(()=>{const checkOnlineStatus=()=>{const now=Date.now();console.log('检查车辆在线状态...');// 只有在特定条件下才将车辆设为离线\n// 例如，只有收到过BSM消息的车辆才会被检查是否超时\nsetOnlineBsmIds(prev=>{const newOnlineBsmIds=new Set(prev);let hasChanges=false;// 仅检查已有最后更新时间的车辆\nprev.forEach(bsmId=>{const lastTime=lastBsmTime.current[bsmId];// 只有收到过BSM消息的车辆才会被检查是否超时\nif(lastTime&&now-lastTime>30000){console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);newOnlineBsmIds.delete(bsmId);hasChanges=true;}});if(hasChanges){// 更新车辆状态\nsetVehicles(prevVehicles=>{const updatedVehicles=prevVehicles.map(vehicle=>{// 只更新有最后更新时间的车辆状态\nif(lastBsmTime.current[vehicle.bsmId]){const isOnline=newOnlineBsmIds.has(vehicle.bsmId);return{...vehicle,status:isOnline?'online':'offline'};}return vehicle;});// 计算更新后的在线车辆数量\nconst onlineCount=updatedVehicles.filter(v=>v.status==='online').length;const totalCount=updatedVehicles.length;// 更新统计数据\nsetStats(prevStats=>({...prevStats,onlineVehicles:onlineCount,offlineVehicles:totalCount-onlineCount}));return updatedVehicles;});}return newOnlineBsmIds;});};const interval=setInterval(checkOnlineStatus,5000);return()=>clearInterval(interval);},[]);// 重置所有车辆的初始状态\nuseEffect(()=>{// 将所有车辆状态重置为离线\nconst resetAllVehicles=()=>{// 只有在没有任何BSM消息的情况下才执行重置\nif(onlineBsmIds.size===0){setVehicles(prevVehicles=>prevVehicles.map(vehicle=>({...vehicle,status:'offline',speed:0,lat:0,lng:0,heading:0})));console.log('已重置所有车辆为离线状态');}};// 初始执行一次\nresetAllVehicles();// 然后每30秒检查一次\nconst interval=setInterval(resetAllVehicles,30000);return()=>clearInterval(interval);},[onlineBsmIds]);// 在组件挂载时获取数据\nuseEffect(()=>{const loadData=async()=>{setLoading(true);fetchVehicles();await fetchDeviceStats();setLoading(false);};loadData();// // 降低更新频率，避免频繁覆盖状态\n// const interval = setInterval(loadData, 300000); // 每60x5秒更新一次\n// return () => clearInterval(interval);\n},[]);// 添加对车辆数据变更的监听\nuseEffect(()=>{console.log('设置车辆数据变更监听');// 监听自定义事件\nconst handleVehiclesDataChanged=()=>{console.log('检测到车辆数据变更事件，重新获取车辆列表');fetchVehicles();};// 监听localStorage变化\nconst handleStorageChange=event=>{if(event.key==='vehiclesLastUpdated'||event.key==='vehiclesData'){console.log('检测到localStorage变化，重新获取车辆列表');fetchVehicles();}};// 添加事件监听器\nwindow.addEventListener('vehiclesDataChanged',handleVehiclesDataChanged);window.addEventListener('storage',handleStorageChange);// 初始检查是否有更新\nconst lastUpdated=localStorage.getItem('vehiclesLastUpdated');if(lastUpdated){console.log('初始检查到vehiclesLastUpdated:',lastUpdated);fetchVehicles();}// 设置更频繁的强制轮询，确保在部署环境中也能获取最新数据\nconst forcedPollingInterval=setInterval(()=>{console.log('强制轮询：重新获取车辆列表');fetchVehicles();},10000);// 每10秒强制刷新一次\n// 清理函数\nreturn()=>{window.removeEventListener('vehiclesDataChanged',handleVehiclesDataChanged);window.removeEventListener('storage',handleStorageChange);clearInterval(forcedPollingInterval);};},[]);// 添加检查本地缓存与API数据是否一致的机制\n// 定义一个单独的API调用函数，用于获取最新的车辆列表数据\nconst fetchLatestVehiclesData=async function(){let returnData=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;try{// 尝试多个可能的API地址\nconst possibleApiUrls=[process.env.REACT_APP_API_URL||'http://localhost:5000'// 'http://localhost:5000',\n// window.location.origin, // 当前站点的根URL\n// `${window.location.origin}/api`, // 当前站点下的/api路径\n// 'http://localhost:5000/api',\n// 'http://127.0.0.1:5000',\n// 'http://127.0.0.1:5000/api'\n];console.log('尝试从多个API地址获取车辆数据');// // 尝试从本地JSON文件直接获取\n// try {\n//   console.log('尝试从本地JSON文件获取数据');\n//   const jsonResponse = await axios.get('/vehicles.json');\n//   if (jsonResponse.data && jsonResponse.data.vehicles) {\n//     console.log('成功从本地JSON文件获取数据:', jsonResponse.data.vehicles.length);\n//     if (returnData) {\n//       return jsonResponse.data.vehicles;\n//     }\n//     processVehiclesData(jsonResponse.data.vehicles);\n//     return jsonResponse.data.vehicles;\n//   }\n// } catch (jsonError) {\n//   console.log('从本地JSON获取失败:', jsonError.message);\n// }\n// 逐个尝试API地址\nlet succeeded=false;let vehiclesData=null;for(const apiUrl of possibleApiUrls){if(succeeded)break;try{console.log(`尝试从 ${apiUrl}/api/vehicles/list 获取数据`);const response=await axios.get(`${apiUrl}/api/vehicles/list`);if(response.data&&response.data.vehicles){console.log(`成功从 ${apiUrl} 获取数据:`,response.data.vehicles.length);vehiclesData=response.data.vehicles;if(returnData){return vehiclesData;}processVehiclesData(response.data.vehicles);succeeded=true;break;}}catch(error){console.log(`从 ${apiUrl} 获取失败:`,error.message);// 继续尝试下一个URL\n}}if(!succeeded&&!returnData){console.log('所有API地址都获取失败，尝试使用vehicles.json');// 尝试从当前页面获取\ntry{const response=await fetch('/vehicles.json');if(response.ok){const data=await response.json();if(data&&data.vehicles){console.log('从public/vehicles.json获取数据成功:',data.vehicles.length);processVehiclesData(data.vehicles);return data.vehicles;}}}catch(e){console.error('从public/vehicles.json获取失败:',e);}}return vehiclesData||[];}catch(error){console.error('获取车辆列表失败:',error);return[];}};// 添加处理车辆数据的辅助函数\nconst processVehiclesData=newVehicles=>{// 与当前列表比较\nif(vehicles.length!==newVehicles.length){console.log('检测到车辆数量变化，从',vehicles.length,'到',newVehicles.length);fetchVehicles();// 重新加载\nreturn;}// 检查是否有新车辆ID\nconst currentIds=new Set(vehicles.map(v=>v.id));const hasNewVehicle=newVehicles.some(v=>!currentIds.has(v.id));if(hasNewVehicle){console.log('检测到新增车辆');fetchVehicles();// 重新加载\n}};// 添加自动选择第一个车辆的逻辑\nuseEffect(()=>{// 当车辆列表加载完成且有车辆数据时\nif(vehicles.length>0&&!selectedVehicle){// 自动选择第一个车辆\nsetSelectedVehicle(vehicles[0]);console.log('已自动选择第一个车辆:',vehicles[0].plateNumber);}},[vehicles,selectedVehicle]);// 修改图表初始化代码\nuseEffect(()=>{let chart=null;let handleResize=null;let lastUpdateTime=new Date();// 确保 DOM 元素存在\nif(!eventChartRef.current){console.error('图表容器未找到');return;}// 检查并清理已存在的图表实例\nlet existingChart=echarts.getInstanceByDom(eventChartRef.current);if(existingChart){existingChart.dispose();}try{// 初始化新的图表实例\nchart=echarts.init(eventChartRef.current);// 设置图表配置\nconst updateChart=()=>{const currentTime=new Date();lastUpdateTime=currentTime;// 事件类型配置\nconst eventTypes=[{type:'401',name:'道路抛洒物',color:'#ff4d4f'},{type:'404',name:'道路障碍物',color:'#faad14'},{type:'405',name:'行人通过马路',color:'#1890ff'},{type:'904',name:'逆行车辆',color:'#f5222d'},{type:'910',name:'违停车辆',color:'#722ed1'},{type:'1002',name:'道路施工',color:'#fa8c16'},{type:'901',name:'车辆超速',color:'#eb2f96'}];// 处理数据\nconst data=eventTypes.map(event=>({value:eventStats[event.type]||0,name:event.name,itemStyle:{color:event.color}})).filter(item=>item.value>0).sort((a,b)=>b.value-a.value);const option={title:{text:`最后更新: ${currentTime.toLocaleTimeString()}`,left:'center',top:-3,textStyle:{fontSize:12,color:'#999'}},grid:{top:30,bottom:0,left:0,right:50,containLabel:true},animation:true,animationDuration:0,animationDurationUpdate:1000,animationEasingUpdate:'quinticInOut',tooltip:{trigger:'axis',axisPointer:{type:'shadow'}},xAxis:{type:'value',show:false,splitLine:{show:false}},yAxis:{type:'category',data:data.map(item=>item.name),axisLabel:{fontSize:12,color:'#666',margin:8},axisTick:{show:false},axisLine:{show:false}},series:[{type:'bar',data:data,barWidth:'50%',label:{show:true,position:'right',formatter:'{c}次',fontSize:12,color:'#666'},itemStyle:{borderRadius:[0,4,4,0]},realtimeSort:false,animationDelay:function(idx){return idx*100;}}]};// 使用 notMerge: false 来保持增量更新\nchart.setOption(option,{notMerge:false,replaceMerge:['series']});};// 初始更新\nupdateChart();// 监听事件统计变化，每分钟更新一次\nconst statsInterval=setInterval(updateChart,60000);// 60000ms = 1分钟\n// 监听窗口大小变化\nhandleResize=()=>{var _chart;(_chart=chart)===null||_chart===void 0?void 0:_chart.resize();};window.addEventListener('resize',handleResize);// 清理函数\nreturn()=>{clearInterval(statsInterval);if(handleResize){window.removeEventListener('resize',handleResize);}if(chart){chart.dispose();}};}catch(error){console.error('初始化图表失败:',error);}},[eventStats]);// 修改 RSI 消息处理逻辑\nuseEffect(()=>{const handleRsiMessage=event=>{try{if(event.data&&event.data.type==='RSI'){const rsiData=event.data.data;if(!rsiData||!rsiData.rtes)return;// if(rsiData.rtes.length > 0){\n//   // console.log('RealTimeTraffic 收到 RSI 消息:', event.data);\n//   if(parseFloat(rsiData.posLong) < 113.0){\n//     console.log('位置不在测试范围的 RSI 消息:', event.data);\n//     console.log('收到RSI消息，但位置不在测试范围内，忽略');\n//     return;\n//   }\n// }\nconst latitude=parseFloat(rsiData.posLat);const longitude=parseFloat(rsiData.posLong);const rsuId=rsiData.rsuId;const mac=event.data.mac||'';const timestamp=event.data.tm||Date.now();// 统计本帧所有非重复事件类型\nconst nonDuplicateEventTypes=[];rsiData.rtes.forEach(event=>{const eventType=event.eventType;// 根据事件类型设置不同的位置精度和去重策略\nlet latFixed='';let lngFixed='';let eventKey='';if(eventType==='904'||eventType==='901'){// 逆行和超速：使用3位小数精度（约111米范围）\nlatFixed=Math.floor(latitude*Math.pow(10,3))/Math.pow(10,3);lngFixed=Math.floor(longitude*Math.pow(10,3))/Math.pow(10,3);eventKey=`${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;}else if(eventType==='910'){// 违停车辆：使用2位小数精度（约1.1公里范围），忽略MAC地址\n// 这样可以将相近位置的违停事件归为同一类\nlatFixed=Math.floor(latitude*Math.pow(10,4))/Math.pow(10,4);lngFixed=Math.floor(longitude*Math.pow(10,4))/Math.pow(10,4);eventKey=`${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;}else{// 其他事件：使用4位小数精度（约11米范围）\nlatFixed=Math.floor(latitude*Math.pow(10,4))/Math.pow(10,4);lngFixed=Math.floor(longitude*Math.pow(10,4))/Math.pow(10,4);eventKey=`${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;}const duplicateResult=checkDuplicateEvent(eventType,eventKey,timestamp,{lat:latitude,lng:longitude});const isDuplicate=duplicateResult.isDuplicate;// 获取事件类型的中文描述\nlet eventTypeText='';let eventColor='';switch(eventType){case'401':eventTypeText='道路抛洒物';eventColor='#ff4d4f';break;case'404':eventTypeText='道路障碍物';eventColor='#faad14';break;case'405':eventTypeText='行人通过马路';eventColor='#1890ff';break;case'904':eventTypeText='逆行车辆';eventColor='#f5222d';break;case'910':eventTypeText='违停车辆';eventColor='#722ed1';break;case'1002':eventTypeText='道路施工';eventColor='#fa8c16';break;case'901':eventTypeText='车辆超速';eventColor='#eb2f96';break;default:eventTypeText=event.description||'未知事件';eventColor='#8c8c8c';}// 更新事件列表\n// const newEvent = {\n//   key: Date.now() + Math.random(),\n//   type: eventTypeText,\n//   time: new Date().toLocaleTimeString(),\n//   vehicle: rsiData.rsuId || '未知设备',\n//   color: eventColor,\n//   eventType: eventType,\n//   location: {\n//     latitude: latitude,\n//     longitude: longitude\n//   }\n// };\n// setEvents(prev => {\n//   const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n//   return newEvents;\n// });\nif(!isDuplicate){// 创建事件数据，使用新的事件ID\nconst eventData={eventId:duplicateResult.eventId,eventType:eventType,eventTypeText:eventTypeText,rsuId:rsiData.rsuId||'未知设备',mac:mac,latitude:latitude,longitude:longitude,site:getNearestIntersectionName(latitude,longitude),eventKey:eventKey,color:eventColor,timestamp:new Date().toISOString()};// 存储到数据库\nstoreEventToDatabase(eventData).then(success=>{if(success){console.log(`✅ 事件已存储到数据库: ${eventTypeText}`);// 更新本地事件列表（用于实时显示）\nconst newEvent={key:Date.now()+Math.random(),type:eventTypeText,time:new Date().toLocaleTimeString(),rsuId:rsiData.rsuId||'未知设备',color:eventColor,eventType:eventType,location:{latitude:latitude,longitude:longitude},site:eventData.site};setEvents(prev=>{const newEvents=[newEvent,...prev].slice(0,10);// 只保留最近10条记录\nreturn newEvents;});}else{console.error(`❌ 事件存储失败: ${eventTypeText}`);}});nonDuplicateEventTypes.push(eventType);}else{console.log(`检测到重复事件，不累计统计: ${eventTypeText}`);}});// 如果有新事件，从数据库刷新统计数据\nif(nonDuplicateEventTypes.length>0){console.log(`📊 检测到 ${nonDuplicateEventTypes.length} 个新事件，刷新统计数据`);// 延迟1秒后从数据库获取最新统计，确保数据已存储\nsetTimeout(async()=>{const stats=await fetchEventStatsFromDatabase();if(stats){setEventStats(prevStats=>({...prevStats,...stats}));console.log('✅ 事件统计数据已从数据库更新');}},1000);}}}catch(error){console.error('处理 RSI 消息失败:',error);}};// 添加消息监听器\nwindow.addEventListener('message',handleRsiMessage);return()=>{window.removeEventListener('message',handleRsiMessage);};},[]);// 获取事件类型的阈值配置\nconst getEventThresholds=eventType=>{switch(eventType){case'910':// 违停车辆\nreturn{timeThreshold:300000,distanceThreshold:20};// 5分钟, 20米\ncase'904':// 逆行车辆\nreturn{timeThreshold:10000,distanceThreshold:20};// 10秒, 20米\ncase'901':// 车辆超速\nreturn{timeThreshold:30000,distanceThreshold:50};// 30秒, 50米\ncase'401':// 道路抛洒物\ncase'404':// 道路障碍物\ncase'1002':// 道路施工\nreturn{timeThreshold:600000,distanceThreshold:30};// 10分钟, 30米\ncase'405':// 行人通过马路\nreturn{timeThreshold:10000,distanceThreshold:10};// 10秒, 10米\ndefault:return{timeThreshold:5000,distanceThreshold:5};// 5秒, 5米\n}};// 优化后的事件重复检查逻辑\nconst checkDuplicateEvent=(eventType,eventKey,currentTime,currentPos)=>{const{timeThreshold,distanceThreshold}=getEventThresholds(eventType);// 遍历事件列表缓存中的所有事件\nfor(let i=0;i<eventListCache.current.length;i++){const cachedEvent=eventListCache.current[i];// 检查事件类型是否相同\nif(cachedEvent.eventType!==eventType){continue;}// 计算时间差\nconst timeDiff=currentTime-cachedEvent.lastUpdateTime;// 检查时间差是否在阈值内\nif(timeDiff>timeThreshold){continue;}// 计算距离\nconst distance=calculateDistance(currentPos.lat,currentPos.lng,cachedEvent.position.lat,cachedEvent.position.lng);// 检查距离是否在阈值内\nif(distance<=distanceThreshold){// 找到匹配的事件，更新信息\ncachedEvent.eventKey=eventKey;cachedEvent.lastUpdateTime=currentTime;cachedEvent.position={...currentPos};cachedEvent.updateCount=(cachedEvent.updateCount||1)+1;console.log(`🔄 检测到重复事件 ${eventType} (ID: ${cachedEvent.eventId})，时间差: ${(timeDiff/1000).toFixed(1)}s，距离: ${distance.toFixed(1)}m，更新次数: ${cachedEvent.updateCount}`);return{isDuplicate:true,eventId:cachedEvent.eventId,matchedEvent:cachedEvent};}}// 没有找到匹配的事件，创建新事件\nconst newEventId=`EVT_${eventIdCounter.current.toString().padStart(6,'0')}`;eventIdCounter.current++;const newEvent={eventId:newEventId,eventType:eventType,eventKey:eventKey,firstDetectedTime:currentTime,lastUpdateTime:currentTime,position:{...currentPos},updateCount:1};// 添加到事件列表缓存\neventListCache.current.push(newEvent);console.log(`✨ 创建新事件 ${eventType} (ID: ${newEventId})，位置: (${currentPos.lat.toFixed(6)}, ${currentPos.lng.toFixed(6)})`);return{isDuplicate:false,eventId:newEventId,newEvent:newEvent};};// 清理过期事件的函数\nconst cleanupExpiredEvents=()=>{const currentTime=Date.now();const maxAge=3600000;// 1小时\nconst initialCount=eventListCache.current.length;eventListCache.current=eventListCache.current.filter(event=>{const age=currentTime-event.lastUpdateTime;return age<=maxAge;});const removedCount=initialCount-eventListCache.current.length;if(removedCount>0){console.log(`🧹 清理了 ${removedCount} 个过期事件，当前缓存事件数: ${eventListCache.current.length}`);}};// 删除1分钟内没有更新的事件\nconst removeInactiveEvents=()=>{const currentTime=Date.now();const inactiveThreshold=60000;// 1分钟\nconst initialCount=eventListCache.current.length;const removedEvents=[];eventListCache.current=eventListCache.current.filter(event=>{const timeSinceLastUpdate=currentTime-event.lastUpdateTime;if(timeSinceLastUpdate>inactiveThreshold){removedEvents.push({id:event.eventId,type:event.eventType,inactiveTime:(timeSinceLastUpdate/1000).toFixed(1)});return false;// 删除该事件\n}return true;// 保留该事件\n});const removedCount=initialCount-eventListCache.current.length;if(removedCount>0){console.log(`🗑️ 删除了 ${removedCount} 个1分钟内未更新的事件:`);removedEvents.forEach(event=>{console.log(`   - 事件 ${event.id} (类型: ${event.type})，未更新时间: ${event.inactiveTime}秒`);});console.log(`📊 当前缓存事件数: ${eventListCache.current.length}`);}};// 添加计算两点之间距离的函数\nconst calculateDistance=(lat1,lon1,lat2,lon2)=>{if(lat1===0||lon1===0||lat2===0||lon2===0){return 999;}const R=6371000;const dLat=(lat2-lat1)*Math.PI/180;const dLon=(lon2-lon1)*Math.PI/180;const a=Math.sin(dLat/2)*Math.sin(dLat/2)+Math.cos(lat1*Math.PI/180)*Math.cos(lat2*Math.PI/180)*Math.sin(dLon/2)*Math.sin(dLon/2);const c=2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a));return R*c;};// 根据坐标查找最近的路口名称\nconst getNearestIntersectionName=(lat,lng)=>{// 从 intersections.json 获取所有路口\nconst intersections=intersectionsData.intersections||[];let minDistance=Infinity;let nearestName='未知地点';intersections.forEach(inter=>{const interLat=parseFloat(inter.latitude);const interLng=parseFloat(inter.longitude);const dist=calculateDistance(lat,lng,interLat,interLng);if(dist<minDistance){minDistance=dist;nearestName=inter.name;}});return nearestName;};// 处理车辆选择\nconst handleVehicleSelect=vehicle=>{console.log('选择车辆:',vehicle.plateNumber,'状态:',vehicle.status);setSelectedVehicle(vehicle);};// 修改车辆状态更新逻辑，确保同步更新selectedVehicle\nuseEffect(()=>{// 当车辆列表更新后，如果当前有选中的车辆，则更新选中的车辆信息\nif(selectedVehicle){const updatedSelectedVehicle=vehicles.find(v=>v.id===selectedVehicle.id);if(updatedSelectedVehicle&&(updatedSelectedVehicle.status!==selectedVehicle.status||updatedSelectedVehicle.speed!==selectedVehicle.speed||updatedSelectedVehicle.lat!==selectedVehicle.lat||updatedSelectedVehicle.lng!==selectedVehicle.lng||updatedSelectedVehicle.heading!==selectedVehicle.heading)){console.log(`更新选中车辆 ${selectedVehicle.plateNumber} 的信息:`,`状态从 ${selectedVehicle.status} 变为 ${updatedSelectedVehicle.status}`);setSelectedVehicle(updatedSelectedVehicle);}}},[vehicles,selectedVehicle]);// 车辆列表列定义\nconst vehicleColumns=[{title:'车牌号',dataIndex:'plate',key:'plate',width:'40%'},{title:'状态',dataIndex:'status',key:'status',width:'30%',render:status=>/*#__PURE__*/_jsx(Badge,{status:status==='online'?'success':'error',text:status==='online'?'在线':'离线'})},{title:'速度',dataIndex:'speed',key:'speed',width:'30%',render:speed=>`${typeof speed==='number'?speed.toFixed(0):speed} km/h`}];// 修改实时事件列表的渲染\nconst renderEventList=()=>/*#__PURE__*/_jsx(List,{size:\"small\",dataSource:events,renderItem:item=>/*#__PURE__*/_jsx(List.Item,{style:{padding:'8px 0'},children:/*#__PURE__*/_jsx(List.Item.Meta,{title:/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'13px',marginBottom:'4px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:item.color,marginRight:'8px'},children:item.type}),/*#__PURE__*/_jsx(\"span\",{style:{color:'#666',fontSize:'12px'},children:item.time})]}),description:/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#666'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u5730\\u70B9: \",item.site||'未知地点']}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u4F4D\\u7F6E: \",item.location?`${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}`:'未知位置']})]})})}),style:{maxHeight:'calc(100% - 24px)',overflowY:'auto'}});// 添加强制定期从API获取最新数据的机制\nuseEffect(()=>{const apiPollingInterval=setInterval(()=>{console.log('直接从API检查车辆数据更新');fetchLatestVehiclesData();},15000);// 每15秒检查一次API\nreturn()=>clearInterval(apiPollingInterval);},[vehicles]);return/*#__PURE__*/_jsx(Spin,{spinning:loading,tip:\"\\u52A0\\u8F7D\\u4E2D...\",children:/*#__PURE__*/_jsxs(PageContainer,{children:[/*#__PURE__*/_jsxs(CollapsibleSidebar,{position:\"left\",collapsed:leftCollapsed,onCollapse:()=>setLeftCollapsed(!leftCollapsed),children:[/*#__PURE__*/_jsx(InfoCard,{title:\"\\u8F66\\u8F86\\u548C\\u8BBE\\u5907\\u7EDF\\u8BA1\",bordered:false,height:\"160px\",children:/*#__PURE__*/_jsxs(Row,{gutter:[8,1],children:[/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u8F66\\u8F86\\u603B\\u6570\",value:stats.totalVehicles,valueStyle:{color:'#3f8600',display:'flex',justifyContent:'center'}// Style={{}}\n})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u5728\\u7EBF\\u8F66\\u8F86\",value:stats.onlineVehicles// suffix={`/ ${stats.totalVehicles}`}\n,valueStyle:{color:'#3f8600',display:'flex',justifyContent:'center'}})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u79BB\\u7EBF\\u8F66\\u8F86\",value:stats.offlineVehicles// suffix={`/ ${stats.totalVehicles}`}\n,valueStyle:{color:'#cf1322',display:'flex',justifyContent:'center'}})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u8BBE\\u5907\\u603B\\u6570\",value:stats.totalDevices,valueStyle:{color:'#3f8600',display:'flex',justifyContent:'center'}})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u5728\\u7EBF\\u8BBE\\u5907\",value:stats.onlineDevices// suffix={`/ ${stats.totalDevices}`}\n,valueStyle:{color:'#3f8600',display:'flex',justifyContent:'center'}})}),/*#__PURE__*/_jsx(Col,{span:8,style:{display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(CompactStatistic,{title:\"\\u79BB\\u7EBF\\u8BBE\\u5907\",value:stats.offlineDevices// suffix={`/ ${stats.totalDevices}`}\n,valueStyle:{color:'#cf1322',display:'flex',justifyContent:'center'}})})]})}),/*#__PURE__*/_jsx(InfoCard,{title:\"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u5217\\u8868\",bordered:false,height:\"calc(50% - 95px)\"// extra={\n//   <div>\n//     <Button\n//       size=\"small\"\n//       type=\"link\"\n//       onClick={() => {\n//         console.log('🧪 查看事件缓存状态');\n//         console.log('📊 当前事件缓存状态:', {\n//           缓存事件数: eventListCache.current.length,\n//           事件ID计数器: eventIdCounter.current,\n//           事件列表: eventListCache.current.map(e => {\n//             const timeSinceUpdate = (Date.now() - e.lastUpdateTime) / 1000;\n//             return {\n//               ID: e.eventId,\n//               类型: e.eventType,\n//               更新次数: e.updateCount,\n//               位置: `${e.position.lat.toFixed(6)}, ${e.position.lng.toFixed(6)}`,\n//               未更新时间: `${timeSinceUpdate.toFixed(1)}秒`\n//             };\n//           })\n//         });\n//       }}\n//     >\n//       查看缓存\n//     </Button>\n//     <Button\n//       size=\"small\"\n//       type=\"link\"\n//       onClick={() => {\n//         console.log('🗑️ 手动删除非活跃事件');\n//         removeInactiveEvents();\n//       }}\n//     >\n//       删除非活跃\n//     </Button>\n//     <Button\n//       size=\"small\"\n//       type=\"link\"\n//       onClick={() => {\n//         console.log('🧹 手动清理过期事件');\n//         cleanupExpiredEvents();\n//       }}\n//     >\n//       清理过期\n//     </Button>\n//   </div>\n// }\n,children:renderEventList()}),/*#__PURE__*/_jsx(InfoCard,{title:\"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u7EDF\\u8BA1\",bordered:false,height:\"calc(50% - 95px)\",children:/*#__PURE__*/_jsx(\"div\",{ref:eventChartRef,style:{height:'100%',width:'100%'}})})]}),/*#__PURE__*/_jsx(MainContent,{leftCollapsed:leftCollapsed,rightCollapsed:rightCollapsed}),/*#__PURE__*/_jsxs(CollapsibleSidebar,{position:\"right\",collapsed:rightCollapsed,onCollapse:()=>setRightCollapsed(!rightCollapsed),children:[/*#__PURE__*/_jsx(InfoCard,{title:\"\\u8F66\\u8F86\\u5217\\u8868\",bordered:false,height:\"50%\",children:/*#__PURE__*/_jsx(Table,{dataSource:vehicles,columns:vehicleColumns,rowKey:\"id\",pagination:false,size:\"small\",scroll:{y:180},onRow:record=>({onClick:()=>handleVehicleSelect(record),style:{cursor:'pointer',background:(selectedVehicle===null||selectedVehicle===void 0?void 0:selectedVehicle.id)===record.id?'#e6f7ff':'transparent',fontSize:'13px',padding:'4px 8px'}})})}),/*#__PURE__*/_jsx(InfoCard,{title:\"\\u8F66\\u8F86\\u8BE6\\u7EC6\\u4FE1\\u606F\",bordered:false,height:\"50%\",children:selectedVehicle?/*#__PURE__*/_jsxs(Descriptions,{bordered:true,column:1,size:\"small\",styles:{label:{fontSize:'13px',padding:'4px 8px'},content:{fontSize:'13px',padding:'4px 8px'}},children:[/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u8F66\\u724C\\u53F7\",children:selectedVehicle.plateNumber}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u72B6\\u6001\",children:/*#__PURE__*/_jsx(Badge,{status:selectedVehicle.status==='online'?'success':'error',text:selectedVehicle.status==='online'?'在线':'离线'})}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7ECF\\u5EA6\",children:selectedVehicle.status==='online'?selectedVehicle.lng.toFixed(7):'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u7EAC\\u5EA6\",children:selectedVehicle.status==='online'?selectedVehicle.lat.toFixed(7):'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u901F\\u5EA6\",children:selectedVehicle.status==='online'?`${typeof selectedVehicle.speed==='number'?selectedVehicle.speed.toFixed(0):selectedVehicle.speed} km/h`:'N/A'}),/*#__PURE__*/_jsx(Descriptions.Item,{label:\"\\u822A\\u5411\\u89D2\",children:selectedVehicle.status==='online'?`${typeof selectedVehicle.heading==='number'?selectedVehicle.heading.toFixed(2):selectedVehicle.heading}°`:'N/A'})]}):/*#__PURE__*/_jsx(\"p\",{style:{fontSize:'13px'},children:\"\\u8BF7\\u9009\\u62E9\\u8F66\\u8F86\\u67E5\\u770B\\u8BE6\\u7EC6\\u4FE1\\u606F\"})})]})]})});};export default RealTimeTraffic;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "Row", "Col", "Card", "Statistic", "List", "Table", "Descriptions", "Spin", "Badge", "<PERSON><PERSON>", "echarts", "<PERSON><PERSON><PERSON>", "GridComponent", "TooltipComponent", "LegendComponent", "TitleComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "CollapsibleSidebar", "axios", "vehiclesData", "intersectionsData", "jsx", "_jsx", "jsxs", "_jsxs", "use", "StyledCanvas", "div", "<PERSON><PERSON><PERSON><PERSON>", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "InfoCard", "height", "CompactStatistic", "RealTimeTraffic", "loading", "setLoading", "vehicles", "setVehicles", "events", "setEvents", "selectedVehicle", "setSelectedVehicle", "stats", "setStats", "totalVehicles", "onlineVehicles", "offlineVehicles", "totalDevices", "onlineDevices", "offlineDevices", "onlineBsmIds", "setOnlineBsmIds", "savedOnlineIds", "localStorage", "getItem", "Set", "error", "console", "lastBsmTime", "eventChartRef", "setLeftCollapsed", "setRightCollapsed", "eventStats", "setEventStats", "prevRsiEvents", "Map", "eventListCache", "eventIdCounter", "storeEventToDatabase", "eventData", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "post", "data", "success", "log", "storage", "eventTypeText", "fetchEventStatsFromDatabase", "get", "fetchRecentEventsFromDatabase", "limit", "arguments", "length", "undefined", "updateVehicleStatus", "bsmId", "status", "speed", "lat", "lng", "heading", "formattedSpeed", "parseFloat", "toFixed", "formattedHeading", "prev", "current", "Date", "now", "newSet", "delete", "prevVehicles", "map", "vehicle", "window", "handleRealBsmReceived", "event", "type", "addEventListener", "removeEventListener", "loadEventsFromDatabase", "recentEvents", "prevStats", "refreshInterval", "setInterval", "clearInterval", "cleanupInterval", "cleanupExpiredEvents", "removeInactiveInterval", "removeInactiveEvents", "fetchVehicles", "apiData", "fetchLatestVehiclesData", "bsmIds", "v", "filter", "id", "updatedVehicles", "isOnline", "has", "plate", "plateNumber", "onlineCount", "totalCount", "vehiclesList", "fetchDeviceStats", "devicesData", "d", "handleBsmMessage", "bsmData", "partSpeed", "partLat", "partLong", "partHeading", "foundVehicle", "find", "checkOnlineStatus", "newOnlineBsmIds", "has<PERSON><PERSON><PERSON>", "for<PERSON>ach", "lastTime", "interval", "resetAllVehicles", "size", "loadData", "handleVehiclesDataChanged", "handleStorageChange", "key", "lastUpdated", "forcedPollingInterval", "returnData", "possibleApiUrls", "succeeded", "processVehiclesData", "message", "fetch", "ok", "json", "e", "newVehicles", "currentIds", "hasNewVehicle", "some", "chart", "handleResize", "lastUpdateTime", "existingChart", "getInstanceByDom", "dispose", "init", "updateChart", "currentTime", "eventTypes", "name", "color", "value", "itemStyle", "item", "sort", "a", "b", "option", "title", "text", "toLocaleTimeString", "left", "top", "textStyle", "fontSize", "grid", "bottom", "right", "containLabel", "animation", "animationDuration", "animationDurationUpdate", "animationEasingUpdate", "tooltip", "trigger", "axisPointer", "xAxis", "show", "splitLine", "yAxis", "axisLabel", "margin", "axisTick", "axisLine", "series", "<PERSON><PERSON><PERSON><PERSON>", "label", "position", "formatter", "borderRadius", "realtimeSort", "animationDelay", "idx", "setOption", "notMerge", "replaceMerge", "statsInterval", "_chart", "resize", "handleRsiMessage", "rsiData", "rtes", "latitude", "posLat", "longitude", "posLong", "rsuId", "mac", "timestamp", "tm", "nonDuplicateEventTypes", "eventType", "latFixed", "lngFixed", "eventKey", "Math", "floor", "pow", "duplicateResult", "checkDuplicateEvent", "isDuplicate", "eventColor", "description", "eventId", "site", "getNearestIntersectionName", "toISOString", "then", "newEvent", "random", "time", "location", "newEvents", "slice", "push", "setTimeout", "getEventThresholds", "timeT<PERSON><PERSON>old", "distanceThreshold", "currentPos", "i", "cachedEvent", "timeDiff", "distance", "calculateDistance", "updateCount", "matchedEvent", "newEventId", "toString", "padStart", "firstDetectedTime", "maxAge", "initialCount", "age", "removedCount", "inactiveThreshold", "removedEvents", "timeSinceLastUpdate", "inactiveTime", "lat1", "lon1", "lat2", "lon2", "R", "dLat", "PI", "dLon", "sin", "cos", "c", "atan2", "sqrt", "intersections", "minDistance", "Infinity", "nearestName", "inter", "interLat", "interLng", "dist", "handleVehicleSelect", "updatedSelectedVehicle", "vehicleColumns", "dataIndex", "width", "render", "renderEventList", "dataSource", "renderItem", "<PERSON><PERSON>", "style", "padding", "children", "Meta", "marginBottom", "marginRight", "maxHeight", "overflowY", "apiPollingInterval", "spinning", "tip", "collapsed", "onCollapse", "bordered", "gutter", "span", "display", "justifyContent", "valueStyle", "ref", "columns", "<PERSON><PERSON><PERSON>", "pagination", "scroll", "y", "onRow", "record", "onClick", "cursor", "background", "column", "styles", "content"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RealTimeTraffic.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge, Button } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { Bar<PERSON>hart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\nimport vehiclesData from '../data/vehicles.json';\nimport intersectionsData from '../data/intersections.json'; // 导入路口数据\n\n// 注册必要的echarts组件\necharts.use([BarChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\n\nconst StyledCanvas = styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 65px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' :\n    props.leftCollapsed ? '0 8px 0 24px' :\n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' :\n    props.leftCollapsed ? '0 8px 0 0' :\n    props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n\n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n\n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n\n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n\n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n\nconst RealTimeTraffic = () => {\n  const [loading, setLoading] = useState(true);\n  const [vehicles, setVehicles] = useState([]);\n  const [events, setEvents] = useState([]);\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\n  const [stats, setStats] = useState({\n    totalVehicles: 0,\n    onlineVehicles: 0,\n    offlineVehicles: 0,\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0\n  });\n\n  // 存储在线车辆的 BSM ID，初始时为空集合，表示所有车辆离线\n  const [onlineBsmIds, setOnlineBsmIds] = useState(() => {\n    try {\n      const savedOnlineIds = localStorage.getItem('realTimeTrafficOnlineIds');\n      // 返回空集合，确保所有车辆初始状态为离线\n      return new Set();\n    } catch (error) {\n      console.error('读取在线ID缓存失败:', error);\n      return new Set();\n    }\n  });\n  // 存储最后一次收到 BSM 消息的时间\n  const lastBsmTime = useRef({});\n\n  const eventChartRef = useRef(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  const [eventStats, setEventStats] = useState({\n    '401': 0,  // 道路抛洒物\n    '404': 0,  // 道路障碍物\n    '405': 0,  // 行人通过马路\n    '904': 0,  // 逆行车辆\n    '910': 0,  // 违停车辆\n    '1002': 0, // 道路施工\n    '901': 0   // 车辆超速\n  });\n\n  // 添加RSI事件缓存，用于检测重复事件\n  const prevRsiEvents = useRef(new Map());\n\n  // 事件列表缓存，存储所有事件的完整信息\n  const eventListCache = useRef([]);\n\n  // 事件ID计数器\n  const eventIdCounter = useRef(1);\n\n  // ========== 数据库API调用函数 ==========\n\n  /**\n   * 存储实时事件到数据库\n   */\n  const storeEventToDatabase = async (eventData) => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.post(`${apiUrl}/api/events/store`, eventData);\n\n      if (response.data && response.data.success) {\n        console.log(`✅ 事件已存储到${response.data.storage}:`, eventData.eventTypeText);\n        return true;\n      } else {\n        console.error('❌ 存储事件失败:', response.data);\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ 存储事件到数据库失败:', error);\n      return false;\n    }\n  };\n\n  /**\n   * 从数据库获取事件统计\n   */\n  const fetchEventStatsFromDatabase = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/events/stats?timeRange=24h`);\n\n      if (response.data && response.data.success) {\n        console.log(`📊 从${response.data.storage}获取事件统计:`, response.data.data);\n        return response.data.data;\n      } else {\n        console.error('❌ 获取事件统计失败:', response.data);\n        return null;\n      }\n    } catch (error) {\n      console.error('❌ 从数据库获取事件统计失败:', error);\n      return null;\n    }\n  };\n\n  /**\n   * 从数据库获取最近事件\n   */\n  const fetchRecentEventsFromDatabase = async (limit = 10) => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/events/recent?limit=${limit}`);\n\n      if (response.data && response.data.success) {\n        console.log(`📋 从${response.data.storage}获取最近事件:`, response.data.data.length, '条');\n        return response.data.data;\n      } else {\n        console.error('❌ 获取最近事件失败:', response.data);\n        return [];\n      }\n    } catch (error) {\n      console.error('❌ 从数据库获取最近事件失败:', error);\n      return [];\n    }\n  };\n\n  // 添加手动更新车辆状态和位置信息的函数\n  const updateVehicleStatus = useCallback((bsmId, status, speed = 0, lat = 0, lng = 0, heading = 0) => {\n    // 确保速度和航向角是格式化的数值，保留两位小数\n    const formattedSpeed = parseFloat(speed).toFixed(0);\n    const formattedHeading = parseFloat(heading).toFixed(2);\n\n    console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${formattedSpeed}, 位置=(${lat}, ${lng})`);\n\n    // 更新在线状态\n    if (status === 'online') {\n      setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n      lastBsmTime.current[bsmId] = Date.now();\n    } else {\n      setOnlineBsmIds(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(bsmId);\n        return newSet;\n      });\n    }\n\n    // 更新车辆信息\n    setVehicles(prevVehicles =>\n      prevVehicles.map(vehicle =>\n        vehicle.bsmId === bsmId\n          ? {\n              ...vehicle,\n              status: status,\n              speed: parseFloat(formattedSpeed), // 确保是数值类型\n              lat: parseFloat(lat.toFixed(7)),\n              lng: parseFloat(lng.toFixed(7)),\n              heading: parseFloat(formattedHeading) // 确保是数值类型\n            }\n          : vehicle\n      )\n    );\n  }, []);\n\n  // 在组件挂载时，暴露updateVehicleStatus函数到window对象\n  useEffect(() => {\n    window.updateVehicleStatus = updateVehicleStatus;\n\n    // 用于监听CampusModel是否接收到BSM消息的事件\n    const handleRealBsmReceived = (event) => {\n      if (event.data && event.data.type === 'realBsmReceived') {\n        // console.log('收到CampusModel发送的真实BSM消息通知');\n      }\n    };\n\n    window.addEventListener('message', handleRealBsmReceived);\n\n    // 确保页面刷新时清空在线车辆状态\n    console.log('组件初始化，重置所有车辆为离线状态');\n    setOnlineBsmIds(new Set());\n    lastBsmTime.current = {};\n\n    return () => {\n      window.removeEventListener('message', handleRealBsmReceived);\n      delete window.updateVehicleStatus;\n    };\n  }, [updateVehicleStatus]);\n\n  // 从数据库加载事件数据\n  const loadEventsFromDatabase = async () => {\n    try {\n      // 加载最近事件\n      const recentEvents = await fetchRecentEventsFromDatabase(10);\n      if (recentEvents && recentEvents.length > 0) {\n        setEvents(recentEvents);\n        console.log('✅ 从数据库加载了', recentEvents.length, '条最近事件');\n      }\n\n      // 加载事件统计\n      const stats = await fetchEventStatsFromDatabase();\n      if (stats) {\n        setEventStats(prevStats => ({\n          ...prevStats,\n          ...stats\n        }));\n        console.log('✅ 从数据库加载了事件统计数据');\n      }\n    } catch (error) {\n      console.error('❌ 从数据库加载事件数据失败:', error);\n    }\n  };\n\n  // 组件挂载时从数据库加载数据\n  useEffect(() => {\n    loadEventsFromDatabase();\n  }, []);\n\n  // 定期从数据库刷新数据（每30秒）\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      console.log('🔄 定期刷新事件数据...');\n      loadEventsFromDatabase();\n    }, 30000); // 30秒刷新一次\n\n    return () => clearInterval(refreshInterval);\n  }, []);\n\n  // 定期清理过期事件（每5分钟）\n  useEffect(() => {\n    const cleanupInterval = setInterval(() => {\n      cleanupExpiredEvents();\n    }, 300000); // 5分钟清理一次\n\n    return () => clearInterval(cleanupInterval);\n  }, []);\n\n  // 定期删除1分钟内没有更新的事件（每30秒检查一次）\n  useEffect(() => {\n    const removeInactiveInterval = setInterval(() => {\n      removeInactiveEvents();\n    }, 30000); // 30秒检查一次\n\n    return () => clearInterval(removeInactiveInterval);\n  }, []);\n\n  // 获取车辆数据\n  const fetchVehicles = useCallback(async () => {\n    try {\n      // 先尝试从API获取最新数据\n      console.log('尝试从API获取最新车辆数据');\n      const apiData = await fetchLatestVehiclesData(true);\n\n      // 如果API获取成功，直接使用API数据\n      if (apiData && apiData.length > 0) {\n        console.log('成功从API获取车辆数据，车辆数量:', apiData.length);\n\n        // 获取所有车辆的BSM ID列表\n        const bsmIds = apiData.map(v => v.bsmId).filter(id => id);\n        console.log('所有车辆的BSM ID:', bsmIds);\n\n        // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\n        const updatedVehicles = apiData.map(vehicle => {\n          // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\n          const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);\n\n          return {\n            ...vehicle,\n            plate: vehicle.plateNumber, // 适配表格显示\n            status: isOnline ? 'online' : 'offline', // 只有收到BSM消息的车辆才显示为在线\n            speed: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0) : 0,\n            lat: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0) : 0,\n            lng: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0) : 0,\n            heading: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0) : 0\n          };\n        });\n\n        setVehicles(updatedVehicles);\n\n        // 直接检查更新后的车辆在线状态\n        const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n        const totalCount = updatedVehicles.length;\n\n        console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);\n\n        // 更新车辆统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalVehicles: totalCount,\n          onlineVehicles: onlineCount,\n          offlineVehicles: totalCount - onlineCount\n        }));\n\n        return;\n      }\n\n      // 如果API获取失败，回退到本地JSON文件\n      console.log('API获取失败，回退到本地vehiclesData');\n      const vehiclesList = vehiclesData.vehicles || [];\n      console.log('从vehiclesData获取车辆数据，车辆数量:', vehiclesList.length);\n\n      // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\n      const updatedVehicles = vehiclesList.map(vehicle => {\n        // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\n        const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);\n\n        return {\n          ...vehicle,\n          plate: vehicle.plateNumber, // 适配表格显示\n          status: isOnline ? 'online' : 'offline', // 只有收到BSM消息的车辆才显示为在线\n          speed: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0) : 0,\n          lat: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0) : 0,\n          lng: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0) : 0,\n          heading: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0) : 0\n        };\n      });\n\n      setVehicles(updatedVehicles);\n\n      // 直接检查更新后的车辆在线状态\n      const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n      const totalCount = updatedVehicles.length;\n\n      console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);\n\n      // 更新车辆统计数据\n      setStats(prevStats => ({\n        ...prevStats,\n        totalVehicles: totalCount,\n        onlineVehicles: onlineCount,\n        offlineVehicles: totalCount - onlineCount\n      }));\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n    }\n  }, [onlineBsmIds]);\n\n  // 获取设备统计数据\n  const fetchDeviceStats = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n\n      if (response.data && response.data.success) {\n        const devicesData = response.data.data;\n\n        // 更新设备统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalDevices: devicesData.length,\n          onlineDevices: devicesData.filter(d => d.status === 'online').length,\n          offlineDevices: devicesData.filter(d => d.status === 'offline').length\n        }));\n      }\n    } catch (error) {\n      console.error('获取设备统计数据失败:', error);\n    }\n  };\n\n  // 监听 BSM 消息\n  useEffect(() => {\n    const handleBsmMessage = (event) => {\n      if (event.data && event.data.type === 'bsm') {\n        // 获取bsmId，确保它正确地从消息中提取\n        const bsmData = event.data.data || {};\n        const bsmId = bsmData.bsmId || event.data.bsmId;\n\n        if (!bsmId) {\n          console.error('BSM消息缺少bsmId:', event.data);\n          return;\n        }\n\n        // console.log('收到BSM消息，ID:', bsmId);\n        const now = Date.now();\n\n        // 更新最后接收时间\n        lastBsmTime.current[bsmId] = now;\n\n        // 添加到在线bsmId集合\n        setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n\n        // 提取正确的BSM数据并格式化为两位小数\n        const speed = parseFloat((parseFloat(bsmData.partSpeed || event.data.speed || 0) * 3.6).toFixed(0)); // 转换为km/h，保留两位小数\n        const lat = parseFloat(parseFloat(bsmData.partLat || event.data.lat || 0).toFixed(7));\n        const lng = parseFloat(parseFloat(bsmData.partLong || event.data.lng || 0).toFixed(7));\n        const heading = parseFloat(parseFloat(bsmData.partHeading || event.data.heading || 0).toFixed(2)); // 保留两位小数\n\n        // console.log(`车辆${bsmId}状态: 速度=${speed.toFixed(2)}km/h, 位置=(${lat.toFixed(7)}, ${lng.toFixed(7)}), 航向=${heading.toFixed(2)}°`);\n\n        // 更新车辆状态和位置信息\n        setVehicles(prevVehicles => {\n          // 检查是否找到对应车辆\n          const foundVehicle = prevVehicles.find(v => v.bsmId === bsmId);\n          if (!foundVehicle) {\n            console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);\n            return prevVehicles;\n          }\n\n          const updatedVehicles = prevVehicles.map(vehicle =>\n            vehicle.bsmId === bsmId\n              ? {\n                  ...vehicle,\n                  status: 'online',\n                  speed: speed,\n                  lat: lat,\n                  lng: lng,\n                  heading: heading\n                }\n              : vehicle\n          );\n\n          // 计算更新后的在线车辆数量\n          const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n          const totalCount = updatedVehicles.length;\n\n          // 更新统计数据\n          setStats(prevStats => ({\n            ...prevStats,\n            onlineVehicles: onlineCount,\n            offlineVehicles: totalCount - onlineCount\n          }));\n\n          return updatedVehicles;\n        });\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleBsmMessage);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleBsmMessage);\n    };\n  }, []);\n\n  // 修改检查在线状态的useEffect，同步更新统计信息\n  useEffect(() => {\n    const checkOnlineStatus = () => {\n      const now = Date.now();\n      console.log('检查车辆在线状态...');\n\n      // 只有在特定条件下才将车辆设为离线\n      // 例如，只有收到过BSM消息的车辆才会被检查是否超时\n      setOnlineBsmIds(prev => {\n        const newOnlineBsmIds = new Set(prev);\n        let hasChanges = false;\n\n        // 仅检查已有最后更新时间的车辆\n        prev.forEach(bsmId => {\n          const lastTime = lastBsmTime.current[bsmId];\n\n          // 只有收到过BSM消息的车辆才会被检查是否超时\n          if (lastTime && (now - lastTime > 30000)) {\n            console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);\n            newOnlineBsmIds.delete(bsmId);\n            hasChanges = true;\n          }\n        });\n\n        if (hasChanges) {\n          // 更新车辆状态\n          setVehicles(prevVehicles => {\n            const updatedVehicles = prevVehicles.map(vehicle => {\n              // 只更新有最后更新时间的车辆状态\n              if (lastBsmTime.current[vehicle.bsmId]) {\n                const isOnline = newOnlineBsmIds.has(vehicle.bsmId);\n                return {\n                  ...vehicle,\n                  status: isOnline ? 'online' : 'offline'\n                };\n              }\n              return vehicle;\n            });\n\n            // 计算更新后的在线车辆数量\n            const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n            const totalCount = updatedVehicles.length;\n\n            // 更新统计数据\n            setStats(prevStats => ({\n              ...prevStats,\n              onlineVehicles: onlineCount,\n              offlineVehicles: totalCount - onlineCount\n            }));\n\n            return updatedVehicles;\n          });\n        }\n\n        return newOnlineBsmIds;\n      });\n    };\n\n    const interval = setInterval(checkOnlineStatus, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // 重置所有车辆的初始状态\n  useEffect(() => {\n    // 将所有车辆状态重置为离线\n    const resetAllVehicles = () => {\n      // 只有在没有任何BSM消息的情况下才执行重置\n      if (onlineBsmIds.size === 0) {\n        setVehicles(prevVehicles =>\n          prevVehicles.map(vehicle => ({\n            ...vehicle,\n            status: 'offline',\n            speed: 0,\n            lat: 0,\n            lng: 0,\n            heading: 0\n          }))\n        );\n\n        console.log('已重置所有车辆为离线状态');\n      }\n    };\n\n    // 初始执行一次\n    resetAllVehicles();\n\n    // 然后每30秒检查一次\n    const interval = setInterval(resetAllVehicles, 30000);\n\n    return () => clearInterval(interval);\n  }, [onlineBsmIds]);\n\n  // 在组件挂载时获取数据\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      fetchVehicles();\n      await fetchDeviceStats();\n      setLoading(false);\n    };\n\n    loadData();\n    // // 降低更新频率，避免频繁覆盖状态\n    // const interval = setInterval(loadData, 300000); // 每60x5秒更新一次\n    // return () => clearInterval(interval);\n  }, []);\n\n  // 添加对车辆数据变更的监听\n  useEffect(() => {\n    console.log('设置车辆数据变更监听');\n\n    // 监听自定义事件\n    const handleVehiclesDataChanged = () => {\n      console.log('检测到车辆数据变更事件，重新获取车辆列表');\n      fetchVehicles();\n    };\n\n    // 监听localStorage变化\n    const handleStorageChange = (event) => {\n      if (event.key === 'vehiclesLastUpdated' || event.key === 'vehiclesData') {\n        console.log('检测到localStorage变化，重新获取车辆列表');\n        fetchVehicles();\n      }\n    };\n\n    // 添加事件监听器\n    window.addEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n    window.addEventListener('storage', handleStorageChange);\n\n    // 初始检查是否有更新\n    const lastUpdated = localStorage.getItem('vehiclesLastUpdated');\n    if (lastUpdated) {\n      console.log('初始检查到vehiclesLastUpdated:', lastUpdated);\n      fetchVehicles();\n    }\n\n    // 设置更频繁的强制轮询，确保在部署环境中也能获取最新数据\n    const forcedPollingInterval = setInterval(() => {\n      console.log('强制轮询：重新获取车辆列表');\n      fetchVehicles();\n    }, 10000); // 每10秒强制刷新一次\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n      window.removeEventListener('storage', handleStorageChange);\n      clearInterval(forcedPollingInterval);\n    };\n  }, []);\n\n  // 添加检查本地缓存与API数据是否一致的机制\n  // 定义一个单独的API调用函数，用于获取最新的车辆列表数据\n  const fetchLatestVehiclesData = async (returnData = false) => {\n    try {\n      // 尝试多个可能的API地址\n      const possibleApiUrls = [\n        process.env.REACT_APP_API_URL || 'http://localhost:5000'\n        // 'http://localhost:5000',\n        // window.location.origin, // 当前站点的根URL\n        // `${window.location.origin}/api`, // 当前站点下的/api路径\n        // 'http://localhost:5000/api',\n        // 'http://127.0.0.1:5000',\n        // 'http://127.0.0.1:5000/api'\n      ];\n\n      console.log('尝试从多个API地址获取车辆数据');\n\n      // // 尝试从本地JSON文件直接获取\n      // try {\n      //   console.log('尝试从本地JSON文件获取数据');\n      //   const jsonResponse = await axios.get('/vehicles.json');\n      //   if (jsonResponse.data && jsonResponse.data.vehicles) {\n      //     console.log('成功从本地JSON文件获取数据:', jsonResponse.data.vehicles.length);\n      //     if (returnData) {\n      //       return jsonResponse.data.vehicles;\n      //     }\n      //     processVehiclesData(jsonResponse.data.vehicles);\n      //     return jsonResponse.data.vehicles;\n      //   }\n      // } catch (jsonError) {\n      //   console.log('从本地JSON获取失败:', jsonError.message);\n      // }\n\n      // 逐个尝试API地址\n      let succeeded = false;\n      let vehiclesData = null;\n\n      for (const apiUrl of possibleApiUrls) {\n        if (succeeded) break;\n\n        try {\n          console.log(`尝试从 ${apiUrl}/api/vehicles/list 获取数据`);\n          const response = await axios.get(`${apiUrl}/api/vehicles/list`);\n\n          if (response.data && response.data.vehicles) {\n            console.log(`成功从 ${apiUrl} 获取数据:`, response.data.vehicles.length);\n            vehiclesData = response.data.vehicles;\n\n            if (returnData) {\n              return vehiclesData;\n            }\n\n            processVehiclesData(response.data.vehicles);\n            succeeded = true;\n            break;\n          }\n        } catch (error) {\n          console.log(`从 ${apiUrl} 获取失败:`, error.message);\n          // 继续尝试下一个URL\n        }\n      }\n\n      if (!succeeded && !returnData) {\n        console.log('所有API地址都获取失败，尝试使用vehicles.json');\n        // 尝试从当前页面获取\n        try {\n          const response = await fetch('/vehicles.json');\n          if (response.ok) {\n            const data = await response.json();\n            if (data && data.vehicles) {\n              console.log('从public/vehicles.json获取数据成功:', data.vehicles.length);\n              processVehiclesData(data.vehicles);\n              return data.vehicles;\n            }\n          }\n        } catch (e) {\n          console.error('从public/vehicles.json获取失败:', e);\n        }\n      }\n\n      return vehiclesData || [];\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n      return [];\n    }\n  };\n\n  // 添加处理车辆数据的辅助函数\n  const processVehiclesData = (newVehicles) => {\n    // 与当前列表比较\n    if (vehicles.length !== newVehicles.length) {\n      console.log('检测到车辆数量变化，从', vehicles.length, '到', newVehicles.length);\n      fetchVehicles(); // 重新加载\n      return;\n    }\n\n    // 检查是否有新车辆ID\n    const currentIds = new Set(vehicles.map(v => v.id));\n    const hasNewVehicle = newVehicles.some(v => !currentIds.has(v.id));\n\n    if (hasNewVehicle) {\n      console.log('检测到新增车辆');\n      fetchVehicles(); // 重新加载\n    }\n  };\n\n  // 添加自动选择第一个车辆的逻辑\n  useEffect(() => {\n    // 当车辆列表加载完成且有车辆数据时\n    if (vehicles.length > 0 && !selectedVehicle) {\n      // 自动选择第一个车辆\n      setSelectedVehicle(vehicles[0]);\n      console.log('已自动选择第一个车辆:', vehicles[0].plateNumber);\n    }\n  }, [vehicles, selectedVehicle]);\n\n  // 修改图表初始化代码\n  useEffect(() => {\n    let chart = null;\n    let handleResize = null;\n    let lastUpdateTime = new Date();\n\n    // 确保 DOM 元素存在\n    if (!eventChartRef.current) {\n      console.error('图表容器未找到');\n      return;\n    }\n\n    // 检查并清理已存在的图表实例\n    let existingChart = echarts.getInstanceByDom(eventChartRef.current);\n    if (existingChart) {\n      existingChart.dispose();\n    }\n\n    try {\n      // 初始化新的图表实例\n      chart = echarts.init(eventChartRef.current);\n\n      // 设置图表配置\n      const updateChart = () => {\n        const currentTime = new Date();\n        lastUpdateTime = currentTime;\n\n        // 事件类型配置\n        const eventTypes = [\n          { type: '401', name: '道路抛洒物', color: '#ff4d4f' },\n          { type: '404', name: '道路障碍物', color: '#faad14' },\n          { type: '405', name: '行人通过马路', color: '#1890ff' },\n          { type: '904', name: '逆行车辆', color: '#f5222d' },\n          { type: '910', name: '违停车辆', color: '#722ed1' },\n          { type: '1002', name: '道路施工', color: '#fa8c16' },\n          { type: '901', name: '车辆超速', color: '#eb2f96' }\n        ];\n\n        // 处理数据\n        const data = eventTypes\n          .map(event => ({\n            value: eventStats[event.type] || 0,\n            name: event.name,\n            itemStyle: { color: event.color }\n          }))\n          .filter(item => item.value > 0)\n          .sort((a, b) => b.value - a.value);\n\n        const option = {\n          title: {\n            text: `最后更新: ${currentTime.toLocaleTimeString()}`,\n            left: 'center',\n            top: -3,\n            textStyle: {\n              fontSize: 12,\n              color: '#999'\n            }\n          },\n          grid: {\n            top: 30,\n            bottom: 0,\n            left: 0,\n            right: 50,\n            containLabel: true\n          },\n          animation: true,\n          animationDuration: 0,\n          animationDurationUpdate: 1000,\n          animationEasingUpdate: 'quinticInOut',\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'shadow'\n            }\n          },\n          xAxis: {\n            type: 'value',\n            show: false,\n            splitLine: { show: false }\n          },\n          yAxis: {\n            type: 'category',\n            data: data.map(item => item.name),\n            axisLabel: {\n              fontSize: 12,\n              color: '#666',\n              margin: 8\n            },\n            axisTick: { show: false },\n            axisLine: { show: false }\n          },\n          series: [{\n            type: 'bar',\n            data: data,\n            barWidth: '50%',\n            label: {\n              show: true,\n              position: 'right',\n              formatter: '{c}次',\n              fontSize: 12,\n              color: '#666'\n            },\n            itemStyle: {\n              borderRadius: [0, 4, 4, 0]\n            },\n            realtimeSort: false,\n            animationDelay: function (idx) {\n              return idx * 100;\n            }\n          }]\n        };\n\n        // 使用 notMerge: false 来保持增量更新\n        chart.setOption(option, {\n          notMerge: false,\n          replaceMerge: ['series']\n        });\n      };\n\n      // 初始更新\n      updateChart();\n\n      // 监听事件统计变化，每分钟更新一次\n      const statsInterval = setInterval(updateChart, 60000); // 60000ms = 1分钟\n\n      // 监听窗口大小变化\n      handleResize = () => {\n        chart?.resize();\n      };\n      window.addEventListener('resize', handleResize);\n\n      // 清理函数\n      return () => {\n        clearInterval(statsInterval);\n        if (handleResize) {\n          window.removeEventListener('resize', handleResize);\n        }\n        if (chart) {\n          chart.dispose();\n        }\n      };\n\n    } catch (error) {\n      console.error('初始化图表失败:', error);\n    }\n  }, [eventStats]);\n\n  // 修改 RSI 消息处理逻辑\n  useEffect(() => {\n    const handleRsiMessage = (event) => {\n      try {\n        if (event.data && event.data.type === 'RSI') {\n          const rsiData = event.data.data;\n          if (!rsiData || !rsiData.rtes) return;\n          // if(rsiData.rtes.length > 0){\n          //   // console.log('RealTimeTraffic 收到 RSI 消息:', event.data);\n          //   if(parseFloat(rsiData.posLong) < 113.0){\n          //     console.log('位置不在测试范围的 RSI 消息:', event.data);\n          //     console.log('收到RSI消息，但位置不在测试范围内，忽略');\n          //     return;\n          //   }\n          // }\n\n          const latitude = parseFloat(rsiData.posLat);\n          const longitude = parseFloat(rsiData.posLong);\n          const rsuId = rsiData.rsuId;\n          const mac = event.data.mac || '';\n          const timestamp = event.data.tm || Date.now();\n\n          // 统计本帧所有非重复事件类型\n          const nonDuplicateEventTypes = [];\n          rsiData.rtes.forEach(event => {\n            const eventType = event.eventType;\n\n            // 根据事件类型设置不同的位置精度和去重策略\n            let latFixed = '';\n            let lngFixed = '';\n            let eventKey = '';\n\n            if(eventType === '904' || eventType === '901'){\n              // 逆行和超速：使用3位小数精度（约111米范围）\n              latFixed = Math.floor(latitude * Math.pow(10,3))/Math.pow(10,3);\n              lngFixed = Math.floor(longitude* Math.pow(10,3))/Math.pow(10,3);\n              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;\n            }\n            else if(eventType === '910'){\n              // 违停车辆：使用2位小数精度（约1.1公里范围），忽略MAC地址\n              // 这样可以将相近位置的违停事件归为同一类\n              latFixed = Math.floor(latitude * Math.pow(10,4))/Math.pow(10,4);\n              lngFixed = Math.floor(longitude* Math.pow(10,4))/Math.pow(10,4);\n              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;\n            }\n            else{\n              // 其他事件：使用4位小数精度（约11米范围）\n              latFixed = Math.floor(latitude * Math.pow(10,4))/Math.pow(10,4);\n              lngFixed = Math.floor(longitude* Math.pow(10,4))/Math.pow(10,4);\n              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;\n            }\n\n            const duplicateResult = checkDuplicateEvent(\n              eventType,\n              eventKey,\n              timestamp,\n              { lat: latitude, lng: longitude }\n            );\n\n            const isDuplicate = duplicateResult.isDuplicate;\n            // 获取事件类型的中文描述\n            let eventTypeText = '';\n            let eventColor = '';\n            switch(eventType) {\n              case '401': eventTypeText = '道路抛洒物'; eventColor = '#ff4d4f'; break;\n              case '404': eventTypeText = '道路障碍物'; eventColor = '#faad14'; break;\n              case '405': eventTypeText = '行人通过马路'; eventColor = '#1890ff'; break;\n              case '904': eventTypeText = '逆行车辆'; eventColor = '#f5222d'; break;\n              case '910': eventTypeText = '违停车辆'; eventColor = '#722ed1'; break;\n              case '1002': eventTypeText = '道路施工'; eventColor = '#fa8c16'; break;\n              case '901': eventTypeText = '车辆超速'; eventColor = '#eb2f96'; break;\n              default: eventTypeText = event.description || '未知事件'; eventColor = '#8c8c8c';\n            }\n            // 更新事件列表\n            // const newEvent = {\n            //   key: Date.now() + Math.random(),\n            //   type: eventTypeText,\n            //   time: new Date().toLocaleTimeString(),\n            //   vehicle: rsiData.rsuId || '未知设备',\n            //   color: eventColor,\n            //   eventType: eventType,\n            //   location: {\n            //     latitude: latitude,\n            //     longitude: longitude\n            //   }\n\n            // };\n            // setEvents(prev => {\n            //   const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n            //   return newEvents;\n            // });\n            if (!isDuplicate) {\n              // 创建事件数据，使用新的事件ID\n              const eventData = {\n                eventId: duplicateResult.eventId,\n                eventType: eventType,\n                eventTypeText: eventTypeText,\n                rsuId: rsiData.rsuId || '未知设备',\n                mac: mac,\n                latitude: latitude,\n                longitude: longitude,\n                site: getNearestIntersectionName(latitude, longitude),\n                eventKey: eventKey,\n                color: eventColor,\n                timestamp: new Date().toISOString()\n              };\n\n              // 存储到数据库\n              storeEventToDatabase(eventData).then(success => {\n                if (success) {\n                  console.log(`✅ 事件已存储到数据库: ${eventTypeText}`);\n\n                  // 更新本地事件列表（用于实时显示）\n                  const newEvent = {\n                    key: Date.now() + Math.random(),\n                    type: eventTypeText,\n                    time: new Date().toLocaleTimeString(),\n                    rsuId: rsiData.rsuId || '未知设备',\n                    color: eventColor,\n                    eventType: eventType,\n                    location: {\n                      latitude: latitude,\n                      longitude: longitude\n                    },\n                    site: eventData.site\n                  };\n\n                  setEvents(prev => {\n                    const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n                    return newEvents;\n                  });\n                } else {\n                  console.error(`❌ 事件存储失败: ${eventTypeText}`);\n                }\n              });\n\n              nonDuplicateEventTypes.push(eventType);\n            } else {\n              console.log(`检测到重复事件，不累计统计: ${eventTypeText}`);\n            }\n          });\n\n          // 如果有新事件，从数据库刷新统计数据\n          if (nonDuplicateEventTypes.length > 0) {\n            console.log(`📊 检测到 ${nonDuplicateEventTypes.length} 个新事件，刷新统计数据`);\n\n            // 延迟1秒后从数据库获取最新统计，确保数据已存储\n            setTimeout(async () => {\n              const stats = await fetchEventStatsFromDatabase();\n              if (stats) {\n                setEventStats(prevStats => ({\n                  ...prevStats,\n                  ...stats\n                }));\n                console.log('✅ 事件统计数据已从数据库更新');\n              }\n            }, 1000);\n          }\n        }\n      } catch (error) {\n        console.error('处理 RSI 消息失败:', error);\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleRsiMessage);\n\n    return () => {\n      window.removeEventListener('message', handleRsiMessage);\n    };\n  }, []);\n\n  // 获取事件类型的阈值配置\n  const getEventThresholds = (eventType) => {\n    switch(eventType) {\n      case '910': // 违停车辆\n        return { timeThreshold: 300000, distanceThreshold: 20 }; // 5分钟, 20米\n      case '904': // 逆行车辆\n        return { timeThreshold: 10000, distanceThreshold: 20 }; // 10秒, 20米\n      case '901': // 车辆超速\n        return { timeThreshold: 30000, distanceThreshold: 50 }; // 30秒, 50米\n      case '401': // 道路抛洒物\n      case '404': // 道路障碍物\n      case '1002': // 道路施工\n        return { timeThreshold: 600000, distanceThreshold: 30 }; // 10分钟, 30米\n      case '405': // 行人通过马路\n        return { timeThreshold: 10000, distanceThreshold: 10 }; // 10秒, 10米\n      default:\n        return { timeThreshold: 5000, distanceThreshold: 5 }; // 5秒, 5米\n    }\n  };\n\n  // 优化后的事件重复检查逻辑\n  const checkDuplicateEvent = (eventType, eventKey, currentTime, currentPos) => {\n    const { timeThreshold, distanceThreshold } = getEventThresholds(eventType);\n\n    // 遍历事件列表缓存中的所有事件\n    for (let i = 0; i < eventListCache.current.length; i++) {\n      const cachedEvent = eventListCache.current[i];\n\n      // 检查事件类型是否相同\n      if (cachedEvent.eventType !== eventType) {\n        continue;\n      }\n\n      // 计算时间差\n      const timeDiff = currentTime - cachedEvent.lastUpdateTime;\n\n      // 检查时间差是否在阈值内\n      if (timeDiff > timeThreshold) {\n        continue;\n      }\n\n      // 计算距离\n      const distance = calculateDistance(\n        currentPos.lat, currentPos.lng,\n        cachedEvent.position.lat, cachedEvent.position.lng\n      );\n\n      // 检查距离是否在阈值内\n      if (distance <= distanceThreshold) {\n        // 找到匹配的事件，更新信息\n        cachedEvent.eventKey = eventKey;\n        cachedEvent.lastUpdateTime = currentTime;\n        cachedEvent.position = { ...currentPos };\n        cachedEvent.updateCount = (cachedEvent.updateCount || 1) + 1;\n\n        console.log(`🔄 检测到重复事件 ${eventType} (ID: ${cachedEvent.eventId})，时间差: ${(timeDiff/1000).toFixed(1)}s，距离: ${distance.toFixed(1)}m，更新次数: ${cachedEvent.updateCount}`);\n\n        return {\n          isDuplicate: true,\n          eventId: cachedEvent.eventId,\n          matchedEvent: cachedEvent\n        };\n      }\n    }\n\n    // 没有找到匹配的事件，创建新事件\n    const newEventId = `EVT_${eventIdCounter.current.toString().padStart(6, '0')}`;\n    eventIdCounter.current++;\n\n    const newEvent = {\n      eventId: newEventId,\n      eventType: eventType,\n      eventKey: eventKey,\n      firstDetectedTime: currentTime,\n      lastUpdateTime: currentTime,\n      position: { ...currentPos },\n      updateCount: 1\n    };\n\n    // 添加到事件列表缓存\n    eventListCache.current.push(newEvent);\n\n    console.log(`✨ 创建新事件 ${eventType} (ID: ${newEventId})，位置: (${currentPos.lat.toFixed(6)}, ${currentPos.lng.toFixed(6)})`);\n\n    return {\n      isDuplicate: false,\n      eventId: newEventId,\n      newEvent: newEvent\n    };\n  };\n\n  // 清理过期事件的函数\n  const cleanupExpiredEvents = () => {\n    const currentTime = Date.now();\n    const maxAge = 3600000; // 1小时\n\n    const initialCount = eventListCache.current.length;\n    eventListCache.current = eventListCache.current.filter(event => {\n      const age = currentTime - event.lastUpdateTime;\n      return age <= maxAge;\n    });\n\n    const removedCount = initialCount - eventListCache.current.length;\n    if (removedCount > 0) {\n      console.log(`🧹 清理了 ${removedCount} 个过期事件，当前缓存事件数: ${eventListCache.current.length}`);\n    }\n  };\n\n  // 删除1分钟内没有更新的事件\n  const removeInactiveEvents = () => {\n    const currentTime = Date.now();\n    const inactiveThreshold = 60000; // 1分钟\n\n    const initialCount = eventListCache.current.length;\n    const removedEvents = [];\n\n    eventListCache.current = eventListCache.current.filter(event => {\n      const timeSinceLastUpdate = currentTime - event.lastUpdateTime;\n      if (timeSinceLastUpdate > inactiveThreshold) {\n        removedEvents.push({\n          id: event.eventId,\n          type: event.eventType,\n          inactiveTime: (timeSinceLastUpdate / 1000).toFixed(1)\n        });\n        return false; // 删除该事件\n      }\n      return true; // 保留该事件\n    });\n\n    const removedCount = initialCount - eventListCache.current.length;\n    if (removedCount > 0) {\n      console.log(`🗑️ 删除了 ${removedCount} 个1分钟内未更新的事件:`);\n      removedEvents.forEach(event => {\n        console.log(`   - 事件 ${event.id} (类型: ${event.type})，未更新时间: ${event.inactiveTime}秒`);\n      });\n      console.log(`📊 当前缓存事件数: ${eventListCache.current.length}`);\n    }\n  };\n\n  // 添加计算两点之间距离的函数\n  const calculateDistance = (lat1, lon1, lat2, lon2) => {\n    if (lat1 === 0 || lon1 === 0 || lat2 === 0 || lon2 === 0) {\n      return 999;\n    }\n    const R = 6371000;\n    const dLat = (lat2 - lat1) * Math.PI / 180;\n    const dLon = (lon2 - lon1) * Math.PI / 180;\n    const a =\n      Math.sin(dLat/2) * Math.sin(dLat/2) +\n      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *\n      Math.sin(dLon/2) * Math.sin(dLon/2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n    return R * c;\n  };\n\n  // 根据坐标查找最近的路口名称\n  const getNearestIntersectionName = (lat, lng) => {\n    // 从 intersections.json 获取所有路口\n    const intersections = intersectionsData.intersections || [];\n    let minDistance = Infinity;\n    let nearestName = '未知地点';\n    intersections.forEach(inter => {\n      const interLat = parseFloat(inter.latitude);\n      const interLng = parseFloat(inter.longitude);\n      const dist = calculateDistance(lat, lng, interLat, interLng);\n      if (dist < minDistance) {\n        minDistance = dist;\n        nearestName = inter.name;\n      }\n    });\n    return nearestName;\n  };\n\n  // 处理车辆选择\n  const handleVehicleSelect = (vehicle) => {\n    console.log('选择车辆:', vehicle.plateNumber, '状态:', vehicle.status);\n    setSelectedVehicle(vehicle);\n  };\n\n  // 修改车辆状态更新逻辑，确保同步更新selectedVehicle\n  useEffect(() => {\n    // 当车辆列表更新后，如果当前有选中的车辆，则更新选中的车辆信息\n    if (selectedVehicle) {\n      const updatedSelectedVehicle = vehicles.find(v => v.id === selectedVehicle.id);\n      if (updatedSelectedVehicle &&\n          (updatedSelectedVehicle.status !== selectedVehicle.status ||\n           updatedSelectedVehicle.speed !== selectedVehicle.speed ||\n           updatedSelectedVehicle.lat !== selectedVehicle.lat ||\n           updatedSelectedVehicle.lng !== selectedVehicle.lng ||\n           updatedSelectedVehicle.heading !== selectedVehicle.heading)) {\n        console.log(`更新选中车辆 ${selectedVehicle.plateNumber} 的信息:`,\n                   `状态从 ${selectedVehicle.status} 变为 ${updatedSelectedVehicle.status}`);\n        setSelectedVehicle(updatedSelectedVehicle);\n      }\n    }\n  }, [vehicles, selectedVehicle]);\n\n  // 车辆列表列定义\n  const vehicleColumns = [\n    {\n      title: '车牌号',\n      dataIndex: 'plate',\n      key: 'plate',\n      width: '40%',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: '30%',\n      render: status => (\n        <Badge\n          status={status === 'online' ? 'success' : 'error'}\n          text={status === 'online' ? '在线' : '离线'}\n        />\n      ),\n    },\n    {\n      title: '速度',\n      dataIndex: 'speed',\n      key: 'speed',\n      width: '30%',\n      render: speed => `${typeof speed === 'number' ? speed.toFixed(0) : speed} km/h`,\n    }\n  ];\n\n  // 修改实时事件列表的渲染\n  const renderEventList = () => (\n    <List\n      size=\"small\"\n      dataSource={events}\n      renderItem={item => (\n        <List.Item style={{ padding: '8px 0' }}>\n          <List.Item.Meta\n            title={\n              <div style={{ fontSize: '13px', marginBottom: '4px' }}>\n                <span style={{ color: item.color, marginRight: '8px' }}>\n                  {item.type}\n                </span>\n                <span style={{ color: '#666', fontSize: '12px' }}>\n                  {item.time}\n                </span>\n              </div>\n            }\n            description={\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                {/* 显示地点（site） */}\n                <div>地点: {item.site || '未知地点'}</div>\n                <div>位置: {item.location ?\n                  `${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}` :\n                  '未知位置'}\n                </div>\n              </div>\n            }\n          />\n        </List.Item>\n      )}\n      style={{\n        maxHeight: 'calc(100% - 24px)',\n        overflowY: 'auto'\n      }}\n    />\n  );\n\n  // 添加强制定期从API获取最新数据的机制\n  useEffect(() => {\n    const apiPollingInterval = setInterval(() => {\n      console.log('直接从API检查车辆数据更新');\n      fetchLatestVehiclesData();\n    }, 15000); // 每15秒检查一次API\n\n    return () => clearInterval(apiPollingInterval);\n  }, [vehicles]);\n\n  return (\n    <Spin spinning={loading} tip=\"加载中...\">\n      <PageContainer>\n        {/* 左侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"left\"\n          collapsed={leftCollapsed}\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\n        >\n          {/* 车辆和设备总数信息栏 */}\n          <InfoCard title=\"车辆和设备统计\" bordered={false} height=\"160px\">\n            <Row gutter={[8, 1]} >\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"车辆总数\"\n                  value={stats.totalVehicles}\n                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }}\n                  // Style={{}}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"在线车辆\"\n                  value={stats.onlineVehicles}\n                  // suffix={`/ ${stats.totalVehicles}`}\n                  valueStyle={{ color: '#3f8600', display: 'flex',justifyContent: 'center'}}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"离线车辆\"\n                  value={stats.offlineVehicles}\n                  // suffix={`/ ${stats.totalVehicles}`}\n                  valueStyle={{ color: '#cf1322' ,display: 'flex',justifyContent: 'center' }}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"设备总数\"\n                  value={stats.totalDevices}\n                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"在线设备\"\n                  value={stats.onlineDevices}\n                  // suffix={`/ ${stats.totalDevices}`}\n                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"离线设备\"\n                  value={stats.offlineDevices}\n                  // suffix={`/ ${stats.totalDevices}`}\n                  valueStyle={{ color: '#cf1322',display: 'flex',justifyContent: 'center' }}\n                />\n              </Col>\n            </Row>\n          </InfoCard>\n\n          {/* 实时事件列表栏 */}\n          <InfoCard\n            title=\"实时事件列表\"\n            bordered={false}\n            height=\"calc(50% - 95px)\"\n            // extra={\n            //   <div>\n            //     <Button\n            //       size=\"small\"\n            //       type=\"link\"\n            //       onClick={() => {\n            //         console.log('🧪 查看事件缓存状态');\n            //         console.log('📊 当前事件缓存状态:', {\n            //           缓存事件数: eventListCache.current.length,\n            //           事件ID计数器: eventIdCounter.current,\n            //           事件列表: eventListCache.current.map(e => {\n            //             const timeSinceUpdate = (Date.now() - e.lastUpdateTime) / 1000;\n            //             return {\n            //               ID: e.eventId,\n            //               类型: e.eventType,\n            //               更新次数: e.updateCount,\n            //               位置: `${e.position.lat.toFixed(6)}, ${e.position.lng.toFixed(6)}`,\n            //               未更新时间: `${timeSinceUpdate.toFixed(1)}秒`\n            //             };\n            //           })\n            //         });\n            //       }}\n            //     >\n            //       查看缓存\n            //     </Button>\n            //     <Button\n            //       size=\"small\"\n            //       type=\"link\"\n            //       onClick={() => {\n            //         console.log('🗑️ 手动删除非活跃事件');\n            //         removeInactiveEvents();\n            //       }}\n            //     >\n            //       删除非活跃\n            //     </Button>\n            //     <Button\n            //       size=\"small\"\n            //       type=\"link\"\n            //       onClick={() => {\n            //         console.log('🧹 手动清理过期事件');\n            //         cleanupExpiredEvents();\n            //       }}\n            //     >\n            //       清理过期\n            //     </Button>\n            //   </div>\n            // }\n          >\n            {renderEventList()}\n          </InfoCard>\n\n          {/* 实时事件统计栏 */}\n          <InfoCard title=\"实时事件统计\" bordered={false} height=\"calc(50% - 95px)\">\n            <div ref={eventChartRef} style={{ height: '100%', width: '100%' }}></div>\n          </InfoCard>\n        </CollapsibleSidebar>\n\n        {/* 主内容区域 */}\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\n          {/* 主要内容 */}\n        </MainContent>\n\n        {/* 右侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"right\"\n          collapsed={rightCollapsed}\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\n        >\n          {/* 车辆列表栏 */}\n          <InfoCard title=\"车辆列表\" bordered={false} height=\"50%\">\n            <Table\n              dataSource={vehicles}\n              columns={vehicleColumns}\n              rowKey=\"id\"\n              pagination={false}\n              size=\"small\"\n              scroll={{ y: 180 }}\n              onRow={(record) => ({\n                onClick: () => handleVehicleSelect(record),\n                style: {\n                  cursor: 'pointer',\n                  background: selectedVehicle?.id === record.id ? '#e6f7ff' : 'transparent',\n                  fontSize: '13px',\n                  padding: '4px 8px'\n                }\n              })}\n            />\n          </InfoCard>\n\n          {/* 车辆详细信息栏 */}\n          <InfoCard title=\"车辆详细信息\" bordered={false} height=\"50%\">\n            {selectedVehicle ? (\n              <Descriptions\n                bordered\n                column={1}\n                size=\"small\"\n                styles={{\n                  label: { fontSize: '13px', padding: '4px 8px' },\n                  content: { fontSize: '13px', padding: '4px 8px' }\n                }}\n              >\n                <Descriptions.Item label=\"车牌号\">{selectedVehicle.plateNumber}</Descriptions.Item>\n                <Descriptions.Item label=\"状态\">\n                  <Badge\n                    status={selectedVehicle.status === 'online' ? 'success' : 'error'}\n                    text={selectedVehicle.status === 'online' ? '在线' : '离线'}\n                  />\n                </Descriptions.Item>\n                <Descriptions.Item label=\"经度\">\n                  {selectedVehicle.status === 'online' ? selectedVehicle.lng.toFixed(7) : 'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"纬度\">\n                  {selectedVehicle.status === 'online' ? selectedVehicle.lat.toFixed(7) : 'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"速度\">\n                  {selectedVehicle.status === 'online' ?\n                    `${typeof selectedVehicle.speed === 'number' ? selectedVehicle.speed.toFixed(0) : selectedVehicle.speed} km/h` :\n                    'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"航向角\">\n                  {selectedVehicle.status === 'online' ?\n                    `${typeof selectedVehicle.heading === 'number' ? selectedVehicle.heading.toFixed(2) : selectedVehicle.heading}°` :\n                    'N/A'}\n                </Descriptions.Item>\n              </Descriptions>\n            ) : (\n              <p style={{ fontSize: '13px' }}>请选择车辆查看详细信息</p>\n            )}\n          </InfoCard>\n        </CollapsibleSidebar>\n      </PageContainer>\n    </Spin>\n  );\n};\n\nexport default RealTimeTraffic;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,OAASC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,SAAS,CAAEC,IAAI,CAAEC,KAAK,CAAEC,YAAY,CAAEC,IAAI,CAAEC,KAAK,CAAEC,MAAM,KAAQ,MAAM,CAChG,MAAO,GAAK,CAAAC,OAAO,KAAM,cAAc,CACvC,OAASC,QAAQ,KAAQ,gBAAgB,CACzC,OAASC,aAAa,CAAEC,gBAAgB,CAAEC,eAAe,CAAEC,cAAc,KAAQ,oBAAoB,CACrG,OAASC,cAAc,KAAQ,mBAAmB,CAClD,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,kBAAkB,KAAM,yCAAyC,CACxE,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,YAAY,KAAM,uBAAuB,CAChD,MAAO,CAAAC,iBAAiB,KAAM,4BAA4B,CAAE;AAE5D;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACAf,OAAO,CAACgB,GAAG,CAAC,CAACf,QAAQ,CAAEC,aAAa,CAAEC,gBAAgB,CAAEC,eAAe,CAAEC,cAAc,CAAEC,cAAc,CAAC,CAAC,CAEzG,KAAM,CAAAW,YAAY,CAAGV,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAGZ,MAAM,CAACW,GAAG;AAChC;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAE,WAAW,CAAGb,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAG,YAAY,CAAGd,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAI,WAAW,CAAGf,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA,aAAaK,KAAK,EAAIA,KAAK,CAACC,aAAa,EAAID,KAAK,CAACE,cAAc,CAAG,QAAQ,CACxEF,KAAK,CAACC,aAAa,CAAG,cAAc,CACpCD,KAAK,CAACE,cAAc,CAAG,cAAc,CAAG,OAAO;AACnD;AACA,YAAYF,KAAK,EAAIA,KAAK,CAACC,aAAa,EAAID,KAAK,CAACE,cAAc,CAAG,QAAQ,CACvEF,KAAK,CAACC,aAAa,CAAG,WAAW,CACjCD,KAAK,CAACE,cAAc,CAAG,WAAW,CAAG,GAAG;AAC5C;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,QAAQ,CAAGnB,MAAM,CAACf,IAAI,CAAC;AAC7B;AACA,YAAY+B,KAAK,EAAIA,KAAK,CAACI,MAAM,EAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAGrB,MAAM,CAACd,SAAS,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAED,KAAM,CAAAoC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG7C,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC8C,QAAQ,CAAEC,WAAW,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACgD,MAAM,CAAEC,SAAS,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACkD,eAAe,CAAEC,kBAAkB,CAAC,CAAGnD,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAACoD,KAAK,CAAEC,QAAQ,CAAC,CAAGrD,QAAQ,CAAC,CACjCsD,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAAC,CACjBC,eAAe,CAAE,CAAC,CAClBC,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,CAAC,CAChBC,cAAc,CAAE,CAClB,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG7D,QAAQ,CAAC,IAAM,CACrD,GAAI,CACF,KAAM,CAAA8D,cAAc,CAAGC,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC,CACvE;AACA,MAAO,IAAI,CAAAC,GAAG,CAAC,CAAC,CAClB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnC,MAAO,IAAI,CAAAD,GAAG,CAAC,CAAC,CAClB,CACF,CAAC,CAAC,CACF;AACA,KAAM,CAAAG,WAAW,CAAGlE,MAAM,CAAC,CAAC,CAAC,CAAC,CAE9B,KAAM,CAAAmE,aAAa,CAAGnE,MAAM,CAAC,IAAI,CAAC,CAElC;AACA,KAAM,CAACoC,aAAa,CAAEgC,gBAAgB,CAAC,CAAGtE,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACuC,cAAc,CAAEgC,iBAAiB,CAAC,CAAGvE,QAAQ,CAAC,KAAK,CAAC,CAE3D,KAAM,CAACwE,UAAU,CAAEC,aAAa,CAAC,CAAGzE,QAAQ,CAAC,CAC3C,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,KAAK,CAAE,CAAC,CAAG;AACX,MAAM,CAAE,CAAC,CAAE;AACX,KAAK,CAAE,CAAI;AACb,CAAC,CAAC,CAEF;AACA,KAAM,CAAA0E,aAAa,CAAGxE,MAAM,CAAC,GAAI,CAAAyE,GAAG,CAAC,CAAC,CAAC,CAEvC;AACA,KAAM,CAAAC,cAAc,CAAG1E,MAAM,CAAC,EAAE,CAAC,CAEjC;AACA,KAAM,CAAA2E,cAAc,CAAG3E,MAAM,CAAC,CAAC,CAAC,CAEhC;AAEA;AACF;AACA,KACE,KAAM,CAAA4E,oBAAoB,CAAG,KAAO,CAAAC,SAAS,EAAK,CAChD,GAAI,CACF,KAAM,CAAAC,MAAM,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACvE,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA7D,KAAK,CAAC8D,IAAI,CAAC,GAAGL,MAAM,mBAAmB,CAAED,SAAS,CAAC,CAE1E,GAAIK,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CAC1CpB,OAAO,CAACqB,GAAG,CAAC,WAAWJ,QAAQ,CAACE,IAAI,CAACG,OAAO,GAAG,CAAEV,SAAS,CAACW,aAAa,CAAC,CACzE,MAAO,KAAI,CACb,CAAC,IAAM,CACLvB,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEkB,QAAQ,CAACE,IAAI,CAAC,CACzC,MAAO,MAAK,CACd,CACF,CAAE,MAAOpB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrC,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACF;AACA,KACE,KAAM,CAAAyB,2BAA2B,CAAG,KAAAA,CAAA,GAAY,CAC9C,GAAI,CACF,KAAM,CAAAX,MAAM,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACvE,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA7D,KAAK,CAACqE,GAAG,CAAC,GAAGZ,MAAM,iCAAiC,CAAC,CAE5E,GAAII,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CAC1CpB,OAAO,CAACqB,GAAG,CAAC,OAAOJ,QAAQ,CAACE,IAAI,CAACG,OAAO,SAAS,CAAEL,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC,CACtE,MAAO,CAAAF,QAAQ,CAACE,IAAI,CAACA,IAAI,CAC3B,CAAC,IAAM,CACLnB,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEkB,QAAQ,CAACE,IAAI,CAAC,CAC3C,MAAO,KAAI,CACb,CACF,CAAE,MAAOpB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvC,MAAO,KAAI,CACb,CACF,CAAC,CAED;AACF;AACA,KACE,KAAM,CAAA2B,6BAA6B,CAAG,cAAAA,CAAA,CAAsB,IAAf,CAAAC,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACrD,GAAI,CACF,KAAM,CAAAf,MAAM,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACvE,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA7D,KAAK,CAACqE,GAAG,CAAC,GAAGZ,MAAM,4BAA4Bc,KAAK,EAAE,CAAC,CAE9E,GAAIV,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CAC1CpB,OAAO,CAACqB,GAAG,CAAC,OAAOJ,QAAQ,CAACE,IAAI,CAACG,OAAO,SAAS,CAAEL,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACU,MAAM,CAAE,GAAG,CAAC,CAClF,MAAO,CAAAZ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAC3B,CAAC,IAAM,CACLnB,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEkB,QAAQ,CAACE,IAAI,CAAC,CAC3C,MAAO,EAAE,CACX,CACF,CAAE,MAAOpB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvC,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA,KAAM,CAAAgC,mBAAmB,CAAG/F,WAAW,CAAC,SAACgG,KAAK,CAAEC,MAAM,CAA+C,IAA7C,CAAAC,KAAK,CAAAN,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAO,GAAG,CAAAP,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAQ,GAAG,CAAAR,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IAAE,CAAAS,OAAO,CAAAT,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAC9F;AACA,KAAM,CAAAU,cAAc,CAAGC,UAAU,CAACL,KAAK,CAAC,CAACM,OAAO,CAAC,CAAC,CAAC,CACnD,KAAM,CAAAC,gBAAgB,CAAGF,UAAU,CAACF,OAAO,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAEvDxC,OAAO,CAACqB,GAAG,CAAC,gBAAgBW,KAAK,QAAQC,MAAM,QAAQK,cAAc,SAASH,GAAG,KAAKC,GAAG,GAAG,CAAC,CAE7F;AACA,GAAIH,MAAM,GAAK,QAAQ,CAAE,CACvBvC,eAAe,CAACgD,IAAI,EAAI,GAAI,CAAA5C,GAAG,CAAC,CAAC,GAAG4C,IAAI,CAAEV,KAAK,CAAC,CAAC,CAAC,CAClD/B,WAAW,CAAC0C,OAAO,CAACX,KAAK,CAAC,CAAGY,IAAI,CAACC,GAAG,CAAC,CAAC,CACzC,CAAC,IAAM,CACLnD,eAAe,CAACgD,IAAI,EAAI,CACtB,KAAM,CAAAI,MAAM,CAAG,GAAI,CAAAhD,GAAG,CAAC4C,IAAI,CAAC,CAC5BI,MAAM,CAACC,MAAM,CAACf,KAAK,CAAC,CACpB,MAAO,CAAAc,MAAM,CACf,CAAC,CAAC,CACJ,CAEA;AACAlE,WAAW,CAACoE,YAAY,EACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,EACtBA,OAAO,CAAClB,KAAK,GAAKA,KAAK,CACnB,CACE,GAAGkB,OAAO,CACVjB,MAAM,CAAEA,MAAM,CACdC,KAAK,CAAEK,UAAU,CAACD,cAAc,CAAC,CAAE;AACnCH,GAAG,CAAEI,UAAU,CAACJ,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC,CAC/BJ,GAAG,CAAEG,UAAU,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CAC/BH,OAAO,CAAEE,UAAU,CAACE,gBAAgB,CAAE;AACxC,CAAC,CACDS,OACN,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACApH,SAAS,CAAC,IAAM,CACdqH,MAAM,CAACpB,mBAAmB,CAAGA,mBAAmB,CAEhD;AACA,KAAM,CAAAqB,qBAAqB,CAAIC,KAAK,EAAK,CACvC,GAAIA,KAAK,CAAClC,IAAI,EAAIkC,KAAK,CAAClC,IAAI,CAACmC,IAAI,GAAK,iBAAiB,CAAE,CACvD;AAAA,CAEJ,CAAC,CAEDH,MAAM,CAACI,gBAAgB,CAAC,SAAS,CAAEH,qBAAqB,CAAC,CAEzD;AACApD,OAAO,CAACqB,GAAG,CAAC,mBAAmB,CAAC,CAChC3B,eAAe,CAAC,GAAI,CAAAI,GAAG,CAAC,CAAC,CAAC,CAC1BG,WAAW,CAAC0C,OAAO,CAAG,CAAC,CAAC,CAExB,MAAO,IAAM,CACXQ,MAAM,CAACK,mBAAmB,CAAC,SAAS,CAAEJ,qBAAqB,CAAC,CAC5D,MAAO,CAAAD,MAAM,CAACpB,mBAAmB,CACnC,CAAC,CACH,CAAC,CAAE,CAACA,mBAAmB,CAAC,CAAC,CAEzB;AACA,KAAM,CAAA0B,sBAAsB,CAAG,KAAAA,CAAA,GAAY,CACzC,GAAI,CACF;AACA,KAAM,CAAAC,YAAY,CAAG,KAAM,CAAAhC,6BAA6B,CAAC,EAAE,CAAC,CAC5D,GAAIgC,YAAY,EAAIA,YAAY,CAAC7B,MAAM,CAAG,CAAC,CAAE,CAC3C/C,SAAS,CAAC4E,YAAY,CAAC,CACvB1D,OAAO,CAACqB,GAAG,CAAC,WAAW,CAAEqC,YAAY,CAAC7B,MAAM,CAAE,OAAO,CAAC,CACxD,CAEA;AACA,KAAM,CAAA5C,KAAK,CAAG,KAAM,CAAAuC,2BAA2B,CAAC,CAAC,CACjD,GAAIvC,KAAK,CAAE,CACTqB,aAAa,CAACqD,SAAS,GAAK,CAC1B,GAAGA,SAAS,CACZ,GAAG1E,KACL,CAAC,CAAC,CAAC,CACHe,OAAO,CAACqB,GAAG,CAAC,iBAAiB,CAAC,CAChC,CACF,CAAE,MAAOtB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACzC,CACF,CAAC,CAED;AACAjE,SAAS,CAAC,IAAM,CACd2H,sBAAsB,CAAC,CAAC,CAC1B,CAAC,CAAE,EAAE,CAAC,CAEN;AACA3H,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8H,eAAe,CAAGC,WAAW,CAAC,IAAM,CACxC7D,OAAO,CAACqB,GAAG,CAAC,gBAAgB,CAAC,CAC7BoC,sBAAsB,CAAC,CAAC,CAC1B,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX,MAAO,IAAMK,aAAa,CAACF,eAAe,CAAC,CAC7C,CAAC,CAAE,EAAE,CAAC,CAEN;AACA9H,SAAS,CAAC,IAAM,CACd,KAAM,CAAAiI,eAAe,CAAGF,WAAW,CAAC,IAAM,CACxCG,oBAAoB,CAAC,CAAC,CACxB,CAAC,CAAE,MAAM,CAAC,CAAE;AAEZ,MAAO,IAAMF,aAAa,CAACC,eAAe,CAAC,CAC7C,CAAC,CAAE,EAAE,CAAC,CAEN;AACAjI,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmI,sBAAsB,CAAGJ,WAAW,CAAC,IAAM,CAC/CK,oBAAoB,CAAC,CAAC,CACxB,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX,MAAO,IAAMJ,aAAa,CAACG,sBAAsB,CAAC,CACpD,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAE,aAAa,CAAGnI,WAAW,CAAC,SAAY,CAC5C,GAAI,CACF;AACAgE,OAAO,CAACqB,GAAG,CAAC,gBAAgB,CAAC,CAC7B,KAAM,CAAA+C,OAAO,CAAG,KAAM,CAAAC,uBAAuB,CAAC,IAAI,CAAC,CAEnD;AACA,GAAID,OAAO,EAAIA,OAAO,CAACvC,MAAM,CAAG,CAAC,CAAE,CACjC7B,OAAO,CAACqB,GAAG,CAAC,oBAAoB,CAAE+C,OAAO,CAACvC,MAAM,CAAC,CAEjD;AACA,KAAM,CAAAyC,MAAM,CAAGF,OAAO,CAACnB,GAAG,CAACsB,CAAC,EAAIA,CAAC,CAACvC,KAAK,CAAC,CAACwC,MAAM,CAACC,EAAE,EAAIA,EAAE,CAAC,CACzDzE,OAAO,CAACqB,GAAG,CAAC,cAAc,CAAEiD,MAAM,CAAC,CAEnC;AACA,KAAM,CAAAI,eAAe,CAAGN,OAAO,CAACnB,GAAG,CAACC,OAAO,EAAI,CAC7C;AACA,KAAM,CAAAyB,QAAQ,CAAGzB,OAAO,CAAClB,KAAK,EAAIvC,YAAY,CAACmF,GAAG,CAAC1B,OAAO,CAAClB,KAAK,CAAC,CAEjE,MAAO,CACL,GAAGkB,OAAO,CACV2B,KAAK,CAAE3B,OAAO,CAAC4B,WAAW,CAAE;AAC5B7C,MAAM,CAAE0C,QAAQ,CAAG,QAAQ,CAAG,SAAS,CAAE;AACzCzC,KAAK,CAAEyC,QAAQ,CAAI1E,WAAW,CAAC0C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAAChB,KAAK,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CACnFC,GAAG,CAAEwC,QAAQ,CAAI1E,WAAW,CAAC0C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAACf,GAAG,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CAC/EC,GAAG,CAAEuC,QAAQ,CAAI1E,WAAW,CAAC0C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAACd,GAAG,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CAC/EC,OAAO,CAAEsC,QAAQ,CAAI1E,WAAW,CAAC0C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAACb,OAAO,EAAI,CAAC,CAAG,CAAC,CAAI,CACxF,CAAC,CACH,CAAC,CAAC,CAEFzD,WAAW,CAAC8F,eAAe,CAAC,CAE5B;AACA,KAAM,CAAAK,WAAW,CAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,EAAIA,CAAC,CAACtC,MAAM,GAAK,QAAQ,CAAC,CAACJ,MAAM,CAC7E,KAAM,CAAAmD,UAAU,CAAGN,eAAe,CAAC7C,MAAM,CAEzC7B,OAAO,CAACqB,GAAG,CAAC,cAAc2D,UAAU,QAAQD,WAAW,QAAQC,UAAU,CAAGD,WAAW,EAAE,CAAC,CAE1F;AACA7F,QAAQ,CAACyE,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZxE,aAAa,CAAE6F,UAAU,CACzB5F,cAAc,CAAE2F,WAAW,CAC3B1F,eAAe,CAAE2F,UAAU,CAAGD,WAChC,CAAC,CAAC,CAAC,CAEH,OACF,CAEA;AACA/E,OAAO,CAACqB,GAAG,CAAC,2BAA2B,CAAC,CACxC,KAAM,CAAA4D,YAAY,CAAG5H,YAAY,CAACsB,QAAQ,EAAI,EAAE,CAChDqB,OAAO,CAACqB,GAAG,CAAC,2BAA2B,CAAE4D,YAAY,CAACpD,MAAM,CAAC,CAE7D;AACA,KAAM,CAAA6C,eAAe,CAAGO,YAAY,CAAChC,GAAG,CAACC,OAAO,EAAI,CAClD;AACA,KAAM,CAAAyB,QAAQ,CAAGzB,OAAO,CAAClB,KAAK,EAAIvC,YAAY,CAACmF,GAAG,CAAC1B,OAAO,CAAClB,KAAK,CAAC,CAEjE,MAAO,CACL,GAAGkB,OAAO,CACV2B,KAAK,CAAE3B,OAAO,CAAC4B,WAAW,CAAE;AAC5B7C,MAAM,CAAE0C,QAAQ,CAAG,QAAQ,CAAG,SAAS,CAAE;AACzCzC,KAAK,CAAEyC,QAAQ,CAAI1E,WAAW,CAAC0C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAAChB,KAAK,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CACnFC,GAAG,CAAEwC,QAAQ,CAAI1E,WAAW,CAAC0C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAACf,GAAG,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CAC/EC,GAAG,CAAEuC,QAAQ,CAAI1E,WAAW,CAAC0C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAACd,GAAG,EAAI,CAAC,CAAG,CAAC,CAAI,CAAC,CAC/EC,OAAO,CAAEsC,QAAQ,CAAI1E,WAAW,CAAC0C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAGkB,OAAO,CAACb,OAAO,EAAI,CAAC,CAAG,CAAC,CAAI,CACxF,CAAC,CACH,CAAC,CAAC,CAEFzD,WAAW,CAAC8F,eAAe,CAAC,CAE5B;AACA,KAAM,CAAAK,WAAW,CAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,EAAIA,CAAC,CAACtC,MAAM,GAAK,QAAQ,CAAC,CAACJ,MAAM,CAC7E,KAAM,CAAAmD,UAAU,CAAGN,eAAe,CAAC7C,MAAM,CAEzC7B,OAAO,CAACqB,GAAG,CAAC,cAAc2D,UAAU,QAAQD,WAAW,QAAQC,UAAU,CAAGD,WAAW,EAAE,CAAC,CAE1F;AACA7F,QAAQ,CAACyE,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZxE,aAAa,CAAE6F,UAAU,CACzB5F,cAAc,CAAE2F,WAAW,CAC3B1F,eAAe,CAAE2F,UAAU,CAAGD,WAChC,CAAC,CAAC,CAAC,CACL,CAAE,MAAOhF,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACnC,CACF,CAAC,CAAE,CAACN,YAAY,CAAC,CAAC,CAElB;AACA,KAAM,CAAAyF,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACF,KAAM,CAAArE,MAAM,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACvE,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA7D,KAAK,CAACqE,GAAG,CAAC,GAAGZ,MAAM,cAAc,CAAC,CAEzD,GAAII,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAE,CAC1C,KAAM,CAAA+D,WAAW,CAAGlE,QAAQ,CAACE,IAAI,CAACA,IAAI,CAEtC;AACAjC,QAAQ,CAACyE,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZrE,YAAY,CAAE6F,WAAW,CAACtD,MAAM,CAChCtC,aAAa,CAAE4F,WAAW,CAACX,MAAM,CAACY,CAAC,EAAIA,CAAC,CAACnD,MAAM,GAAK,QAAQ,CAAC,CAACJ,MAAM,CACpErC,cAAc,CAAE2F,WAAW,CAACX,MAAM,CAACY,CAAC,EAAIA,CAAC,CAACnD,MAAM,GAAK,SAAS,CAAC,CAACJ,MAClE,CAAC,CAAC,CAAC,CACL,CACF,CAAE,MAAO9B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACrC,CACF,CAAC,CAED;AACAjE,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuJ,gBAAgB,CAAIhC,KAAK,EAAK,CAClC,GAAIA,KAAK,CAAClC,IAAI,EAAIkC,KAAK,CAAClC,IAAI,CAACmC,IAAI,GAAK,KAAK,CAAE,CAC3C;AACA,KAAM,CAAAgC,OAAO,CAAGjC,KAAK,CAAClC,IAAI,CAACA,IAAI,EAAI,CAAC,CAAC,CACrC,KAAM,CAAAa,KAAK,CAAGsD,OAAO,CAACtD,KAAK,EAAIqB,KAAK,CAAClC,IAAI,CAACa,KAAK,CAE/C,GAAI,CAACA,KAAK,CAAE,CACVhC,OAAO,CAACD,KAAK,CAAC,eAAe,CAAEsD,KAAK,CAAClC,IAAI,CAAC,CAC1C,OACF,CAEA;AACA,KAAM,CAAA0B,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CAEtB;AACA5C,WAAW,CAAC0C,OAAO,CAACX,KAAK,CAAC,CAAGa,GAAG,CAEhC;AACAnD,eAAe,CAACgD,IAAI,EAAI,GAAI,CAAA5C,GAAG,CAAC,CAAC,GAAG4C,IAAI,CAAEV,KAAK,CAAC,CAAC,CAAC,CAElD;AACA,KAAM,CAAAE,KAAK,CAAGK,UAAU,CAAC,CAACA,UAAU,CAAC+C,OAAO,CAACC,SAAS,EAAIlC,KAAK,CAAClC,IAAI,CAACe,KAAK,EAAI,CAAC,CAAC,CAAG,GAAG,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,CAAE;AACrG,KAAM,CAAAL,GAAG,CAAGI,UAAU,CAACA,UAAU,CAAC+C,OAAO,CAACE,OAAO,EAAInC,KAAK,CAAClC,IAAI,CAACgB,GAAG,EAAI,CAAC,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC,CACrF,KAAM,CAAAJ,GAAG,CAAGG,UAAU,CAACA,UAAU,CAAC+C,OAAO,CAACG,QAAQ,EAAIpC,KAAK,CAAClC,IAAI,CAACiB,GAAG,EAAI,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CACtF,KAAM,CAAAH,OAAO,CAAGE,UAAU,CAACA,UAAU,CAAC+C,OAAO,CAACI,WAAW,EAAIrC,KAAK,CAAClC,IAAI,CAACkB,OAAO,EAAI,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAE;AAEnG;AAEA;AACA5D,WAAW,CAACoE,YAAY,EAAI,CAC1B;AACA,KAAM,CAAA2C,YAAY,CAAG3C,YAAY,CAAC4C,IAAI,CAACrB,CAAC,EAAIA,CAAC,CAACvC,KAAK,GAAKA,KAAK,CAAC,CAC9D,GAAI,CAAC2D,YAAY,CAAE,CACjB3F,OAAO,CAACqB,GAAG,CAAC,eAAeW,KAAK,WAAW,CAAC,CAC5C,MAAO,CAAAgB,YAAY,CACrB,CAEA,KAAM,CAAA0B,eAAe,CAAG1B,YAAY,CAACC,GAAG,CAACC,OAAO,EAC9CA,OAAO,CAAClB,KAAK,GAAKA,KAAK,CACnB,CACE,GAAGkB,OAAO,CACVjB,MAAM,CAAE,QAAQ,CAChBC,KAAK,CAAEA,KAAK,CACZC,GAAG,CAAEA,GAAG,CACRC,GAAG,CAAEA,GAAG,CACRC,OAAO,CAAEA,OACX,CAAC,CACDa,OACN,CAAC,CAED;AACA,KAAM,CAAA6B,WAAW,CAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,EAAIA,CAAC,CAACtC,MAAM,GAAK,QAAQ,CAAC,CAACJ,MAAM,CAC7E,KAAM,CAAAmD,UAAU,CAAGN,eAAe,CAAC7C,MAAM,CAEzC;AACA3C,QAAQ,CAACyE,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZvE,cAAc,CAAE2F,WAAW,CAC3B1F,eAAe,CAAE2F,UAAU,CAAGD,WAChC,CAAC,CAAC,CAAC,CAEH,MAAO,CAAAL,eAAe,CACxB,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACAvB,MAAM,CAACI,gBAAgB,CAAC,SAAS,CAAE8B,gBAAgB,CAAC,CAEpD;AACA,MAAO,IAAM,CACXlC,MAAM,CAACK,mBAAmB,CAAC,SAAS,CAAE6B,gBAAgB,CAAC,CACzD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACAvJ,SAAS,CAAC,IAAM,CACd,KAAM,CAAA+J,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAhD,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CACtB7C,OAAO,CAACqB,GAAG,CAAC,aAAa,CAAC,CAE1B;AACA;AACA3B,eAAe,CAACgD,IAAI,EAAI,CACtB,KAAM,CAAAoD,eAAe,CAAG,GAAI,CAAAhG,GAAG,CAAC4C,IAAI,CAAC,CACrC,GAAI,CAAAqD,UAAU,CAAG,KAAK,CAEtB;AACArD,IAAI,CAACsD,OAAO,CAAChE,KAAK,EAAI,CACpB,KAAM,CAAAiE,QAAQ,CAAGhG,WAAW,CAAC0C,OAAO,CAACX,KAAK,CAAC,CAE3C;AACA,GAAIiE,QAAQ,EAAKpD,GAAG,CAAGoD,QAAQ,CAAG,KAAM,CAAE,CACxCjG,OAAO,CAACqB,GAAG,CAAC,KAAKW,KAAK,kBAAkB,CAAC,CACzC8D,eAAe,CAAC/C,MAAM,CAACf,KAAK,CAAC,CAC7B+D,UAAU,CAAG,IAAI,CACnB,CACF,CAAC,CAAC,CAEF,GAAIA,UAAU,CAAE,CACd;AACAnH,WAAW,CAACoE,YAAY,EAAI,CAC1B,KAAM,CAAA0B,eAAe,CAAG1B,YAAY,CAACC,GAAG,CAACC,OAAO,EAAI,CAClD;AACA,GAAIjD,WAAW,CAAC0C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,CAAE,CACtC,KAAM,CAAA2C,QAAQ,CAAGmB,eAAe,CAAClB,GAAG,CAAC1B,OAAO,CAAClB,KAAK,CAAC,CACnD,MAAO,CACL,GAAGkB,OAAO,CACVjB,MAAM,CAAE0C,QAAQ,CAAG,QAAQ,CAAG,SAChC,CAAC,CACH,CACA,MAAO,CAAAzB,OAAO,CAChB,CAAC,CAAC,CAEF;AACA,KAAM,CAAA6B,WAAW,CAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,EAAIA,CAAC,CAACtC,MAAM,GAAK,QAAQ,CAAC,CAACJ,MAAM,CAC7E,KAAM,CAAAmD,UAAU,CAAGN,eAAe,CAAC7C,MAAM,CAEzC;AACA3C,QAAQ,CAACyE,SAAS,GAAK,CACrB,GAAGA,SAAS,CACZvE,cAAc,CAAE2F,WAAW,CAC3B1F,eAAe,CAAE2F,UAAU,CAAGD,WAChC,CAAC,CAAC,CAAC,CAEH,MAAO,CAAAL,eAAe,CACxB,CAAC,CAAC,CACJ,CAEA,MAAO,CAAAoB,eAAe,CACxB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAI,QAAQ,CAAGrC,WAAW,CAACgC,iBAAiB,CAAE,IAAI,CAAC,CACrD,MAAO,IAAM/B,aAAa,CAACoC,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN;AACApK,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAqK,gBAAgB,CAAGA,CAAA,GAAM,CAC7B;AACA,GAAI1G,YAAY,CAAC2G,IAAI,GAAK,CAAC,CAAE,CAC3BxH,WAAW,CAACoE,YAAY,EACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,GAAK,CAC3B,GAAGA,OAAO,CACVjB,MAAM,CAAE,SAAS,CACjBC,KAAK,CAAE,CAAC,CACRC,GAAG,CAAE,CAAC,CACNC,GAAG,CAAE,CAAC,CACNC,OAAO,CAAE,CACX,CAAC,CAAC,CACJ,CAAC,CAEDrC,OAAO,CAACqB,GAAG,CAAC,cAAc,CAAC,CAC7B,CACF,CAAC,CAED;AACA8E,gBAAgB,CAAC,CAAC,CAElB;AACA,KAAM,CAAAD,QAAQ,CAAGrC,WAAW,CAACsC,gBAAgB,CAAE,KAAK,CAAC,CAErD,MAAO,IAAMrC,aAAa,CAACoC,QAAQ,CAAC,CACtC,CAAC,CAAE,CAACzG,YAAY,CAAC,CAAC,CAElB;AACA3D,SAAS,CAAC,IAAM,CACd,KAAM,CAAAuK,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3B3H,UAAU,CAAC,IAAI,CAAC,CAChByF,aAAa,CAAC,CAAC,CACf,KAAM,CAAAe,gBAAgB,CAAC,CAAC,CACxBxG,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED2H,QAAQ,CAAC,CAAC,CACV;AACA;AACA;AACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACAvK,SAAS,CAAC,IAAM,CACdkE,OAAO,CAACqB,GAAG,CAAC,YAAY,CAAC,CAEzB;AACA,KAAM,CAAAiF,yBAAyB,CAAGA,CAAA,GAAM,CACtCtG,OAAO,CAACqB,GAAG,CAAC,sBAAsB,CAAC,CACnC8C,aAAa,CAAC,CAAC,CACjB,CAAC,CAED;AACA,KAAM,CAAAoC,mBAAmB,CAAIlD,KAAK,EAAK,CACrC,GAAIA,KAAK,CAACmD,GAAG,GAAK,qBAAqB,EAAInD,KAAK,CAACmD,GAAG,GAAK,cAAc,CAAE,CACvExG,OAAO,CAACqB,GAAG,CAAC,4BAA4B,CAAC,CACzC8C,aAAa,CAAC,CAAC,CACjB,CACF,CAAC,CAED;AACAhB,MAAM,CAACI,gBAAgB,CAAC,qBAAqB,CAAE+C,yBAAyB,CAAC,CACzEnD,MAAM,CAACI,gBAAgB,CAAC,SAAS,CAAEgD,mBAAmB,CAAC,CAEvD;AACA,KAAM,CAAAE,WAAW,CAAG7G,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAC/D,GAAI4G,WAAW,CAAE,CACfzG,OAAO,CAACqB,GAAG,CAAC,2BAA2B,CAAEoF,WAAW,CAAC,CACrDtC,aAAa,CAAC,CAAC,CACjB,CAEA;AACA,KAAM,CAAAuC,qBAAqB,CAAG7C,WAAW,CAAC,IAAM,CAC9C7D,OAAO,CAACqB,GAAG,CAAC,eAAe,CAAC,CAC5B8C,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX;AACA,MAAO,IAAM,CACXhB,MAAM,CAACK,mBAAmB,CAAC,qBAAqB,CAAE8C,yBAAyB,CAAC,CAC5EnD,MAAM,CAACK,mBAAmB,CAAC,SAAS,CAAE+C,mBAAmB,CAAC,CAC1DzC,aAAa,CAAC4C,qBAAqB,CAAC,CACtC,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA;AACA,KAAM,CAAArC,uBAAuB,CAAG,cAAAA,CAAA,CAA8B,IAAvB,CAAAsC,UAAU,CAAA/E,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CACvD,GAAI,CACF;AACA,KAAM,CAAAgF,eAAe,CAAG,CACtB9F,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBACjC;AACA;AACA;AACA;AACA;AACA;AAAA,CACD,CAEDhB,OAAO,CAACqB,GAAG,CAAC,kBAAkB,CAAC,CAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA,GAAI,CAAAwF,SAAS,CAAG,KAAK,CACrB,GAAI,CAAAxJ,YAAY,CAAG,IAAI,CAEvB,IAAK,KAAM,CAAAwD,MAAM,GAAI,CAAA+F,eAAe,CAAE,CACpC,GAAIC,SAAS,CAAE,MAEf,GAAI,CACF7G,OAAO,CAACqB,GAAG,CAAC,OAAOR,MAAM,yBAAyB,CAAC,CACnD,KAAM,CAAAI,QAAQ,CAAG,KAAM,CAAA7D,KAAK,CAACqE,GAAG,CAAC,GAAGZ,MAAM,oBAAoB,CAAC,CAE/D,GAAII,QAAQ,CAACE,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACxC,QAAQ,CAAE,CAC3CqB,OAAO,CAACqB,GAAG,CAAC,OAAOR,MAAM,QAAQ,CAAEI,QAAQ,CAACE,IAAI,CAACxC,QAAQ,CAACkD,MAAM,CAAC,CACjExE,YAAY,CAAG4D,QAAQ,CAACE,IAAI,CAACxC,QAAQ,CAErC,GAAIgI,UAAU,CAAE,CACd,MAAO,CAAAtJ,YAAY,CACrB,CAEAyJ,mBAAmB,CAAC7F,QAAQ,CAACE,IAAI,CAACxC,QAAQ,CAAC,CAC3CkI,SAAS,CAAG,IAAI,CAChB,MACF,CACF,CAAE,MAAO9G,KAAK,CAAE,CACdC,OAAO,CAACqB,GAAG,CAAC,KAAKR,MAAM,QAAQ,CAAEd,KAAK,CAACgH,OAAO,CAAC,CAC/C;AACF,CACF,CAEA,GAAI,CAACF,SAAS,EAAI,CAACF,UAAU,CAAE,CAC7B3G,OAAO,CAACqB,GAAG,CAAC,gCAAgC,CAAC,CAC7C;AACA,GAAI,CACF,KAAM,CAAAJ,QAAQ,CAAG,KAAM,CAAA+F,KAAK,CAAC,gBAAgB,CAAC,CAC9C,GAAI/F,QAAQ,CAACgG,EAAE,CAAE,CACf,KAAM,CAAA9F,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACiG,IAAI,CAAC,CAAC,CAClC,GAAI/F,IAAI,EAAIA,IAAI,CAACxC,QAAQ,CAAE,CACzBqB,OAAO,CAACqB,GAAG,CAAC,8BAA8B,CAAEF,IAAI,CAACxC,QAAQ,CAACkD,MAAM,CAAC,CACjEiF,mBAAmB,CAAC3F,IAAI,CAACxC,QAAQ,CAAC,CAClC,MAAO,CAAAwC,IAAI,CAACxC,QAAQ,CACtB,CACF,CACF,CAAE,MAAOwI,CAAC,CAAE,CACVnH,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEoH,CAAC,CAAC,CAChD,CACF,CAEA,MAAO,CAAA9J,YAAY,EAAI,EAAE,CAC3B,CAAE,MAAO0C,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA,KAAM,CAAA+G,mBAAmB,CAAIM,WAAW,EAAK,CAC3C;AACA,GAAIzI,QAAQ,CAACkD,MAAM,GAAKuF,WAAW,CAACvF,MAAM,CAAE,CAC1C7B,OAAO,CAACqB,GAAG,CAAC,aAAa,CAAE1C,QAAQ,CAACkD,MAAM,CAAE,GAAG,CAAEuF,WAAW,CAACvF,MAAM,CAAC,CACpEsC,aAAa,CAAC,CAAC,CAAE;AACjB,OACF,CAEA;AACA,KAAM,CAAAkD,UAAU,CAAG,GAAI,CAAAvH,GAAG,CAACnB,QAAQ,CAACsE,GAAG,CAACsB,CAAC,EAAIA,CAAC,CAACE,EAAE,CAAC,CAAC,CACnD,KAAM,CAAA6C,aAAa,CAAGF,WAAW,CAACG,IAAI,CAAChD,CAAC,EAAI,CAAC8C,UAAU,CAACzC,GAAG,CAACL,CAAC,CAACE,EAAE,CAAC,CAAC,CAElE,GAAI6C,aAAa,CAAE,CACjBtH,OAAO,CAACqB,GAAG,CAAC,SAAS,CAAC,CACtB8C,aAAa,CAAC,CAAC,CAAE;AACnB,CACF,CAAC,CAED;AACArI,SAAS,CAAC,IAAM,CACd;AACA,GAAI6C,QAAQ,CAACkD,MAAM,CAAG,CAAC,EAAI,CAAC9C,eAAe,CAAE,CAC3C;AACAC,kBAAkB,CAACL,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC/BqB,OAAO,CAACqB,GAAG,CAAC,aAAa,CAAE1C,QAAQ,CAAC,CAAC,CAAC,CAACmG,WAAW,CAAC,CACrD,CACF,CAAC,CAAE,CAACnG,QAAQ,CAAEI,eAAe,CAAC,CAAC,CAE/B;AACAjD,SAAS,CAAC,IAAM,CACd,GAAI,CAAA0L,KAAK,CAAG,IAAI,CAChB,GAAI,CAAAC,YAAY,CAAG,IAAI,CACvB,GAAI,CAAAC,cAAc,CAAG,GAAI,CAAA9E,IAAI,CAAC,CAAC,CAE/B;AACA,GAAI,CAAC1C,aAAa,CAACyC,OAAO,CAAE,CAC1B3C,OAAO,CAACD,KAAK,CAAC,SAAS,CAAC,CACxB,OACF,CAEA;AACA,GAAI,CAAA4H,aAAa,CAAGhL,OAAO,CAACiL,gBAAgB,CAAC1H,aAAa,CAACyC,OAAO,CAAC,CACnE,GAAIgF,aAAa,CAAE,CACjBA,aAAa,CAACE,OAAO,CAAC,CAAC,CACzB,CAEA,GAAI,CACF;AACAL,KAAK,CAAG7K,OAAO,CAACmL,IAAI,CAAC5H,aAAa,CAACyC,OAAO,CAAC,CAE3C;AACA,KAAM,CAAAoF,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAApF,IAAI,CAAC,CAAC,CAC9B8E,cAAc,CAAGM,WAAW,CAE5B;AACA,KAAM,CAAAC,UAAU,CAAG,CACjB,CAAE3E,IAAI,CAAE,KAAK,CAAE4E,IAAI,CAAE,OAAO,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAAE7E,IAAI,CAAE,KAAK,CAAE4E,IAAI,CAAE,OAAO,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAAE7E,IAAI,CAAE,KAAK,CAAE4E,IAAI,CAAE,QAAQ,CAAEC,KAAK,CAAE,SAAU,CAAC,CACjD,CAAE7E,IAAI,CAAE,KAAK,CAAE4E,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC/C,CAAE7E,IAAI,CAAE,KAAK,CAAE4E,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAC/C,CAAE7E,IAAI,CAAE,MAAM,CAAE4E,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAAE7E,IAAI,CAAE,KAAK,CAAE4E,IAAI,CAAE,MAAM,CAAEC,KAAK,CAAE,SAAU,CAAC,CAChD,CAED;AACA,KAAM,CAAAhH,IAAI,CAAG8G,UAAU,CACpBhF,GAAG,CAACI,KAAK,GAAK,CACb+E,KAAK,CAAE/H,UAAU,CAACgD,KAAK,CAACC,IAAI,CAAC,EAAI,CAAC,CAClC4E,IAAI,CAAE7E,KAAK,CAAC6E,IAAI,CAChBG,SAAS,CAAE,CAAEF,KAAK,CAAE9E,KAAK,CAAC8E,KAAM,CAClC,CAAC,CAAC,CAAC,CACF3D,MAAM,CAAC8D,IAAI,EAAIA,IAAI,CAACF,KAAK,CAAG,CAAC,CAAC,CAC9BG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAACL,KAAK,CAAGI,CAAC,CAACJ,KAAK,CAAC,CAEpC,KAAM,CAAAM,MAAM,CAAG,CACbC,KAAK,CAAE,CACLC,IAAI,CAAE,SAASZ,WAAW,CAACa,kBAAkB,CAAC,CAAC,EAAE,CACjDC,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,CAAC,CAAC,CACPC,SAAS,CAAE,CACTC,QAAQ,CAAE,EAAE,CACZd,KAAK,CAAE,MACT,CACF,CAAC,CACDe,IAAI,CAAE,CACJH,GAAG,CAAE,EAAE,CACPI,MAAM,CAAE,CAAC,CACTL,IAAI,CAAE,CAAC,CACPM,KAAK,CAAE,EAAE,CACTC,YAAY,CAAE,IAChB,CAAC,CACDC,SAAS,CAAE,IAAI,CACfC,iBAAiB,CAAE,CAAC,CACpBC,uBAAuB,CAAE,IAAI,CAC7BC,qBAAqB,CAAE,cAAc,CACrCC,OAAO,CAAE,CACPC,OAAO,CAAE,MAAM,CACfC,WAAW,CAAE,CACXtG,IAAI,CAAE,QACR,CACF,CAAC,CACDuG,KAAK,CAAE,CACLvG,IAAI,CAAE,OAAO,CACbwG,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,CAAED,IAAI,CAAE,KAAM,CAC3B,CAAC,CACDE,KAAK,CAAE,CACL1G,IAAI,CAAE,UAAU,CAChBnC,IAAI,CAAEA,IAAI,CAAC8B,GAAG,CAACqF,IAAI,EAAIA,IAAI,CAACJ,IAAI,CAAC,CACjC+B,SAAS,CAAE,CACThB,QAAQ,CAAE,EAAE,CACZd,KAAK,CAAE,MAAM,CACb+B,MAAM,CAAE,CACV,CAAC,CACDC,QAAQ,CAAE,CAAEL,IAAI,CAAE,KAAM,CAAC,CACzBM,QAAQ,CAAE,CAAEN,IAAI,CAAE,KAAM,CAC1B,CAAC,CACDO,MAAM,CAAE,CAAC,CACP/G,IAAI,CAAE,KAAK,CACXnC,IAAI,CAAEA,IAAI,CACVmJ,QAAQ,CAAE,KAAK,CACfC,KAAK,CAAE,CACLT,IAAI,CAAE,IAAI,CACVU,QAAQ,CAAE,OAAO,CACjBC,SAAS,CAAE,MAAM,CACjBxB,QAAQ,CAAE,EAAE,CACZd,KAAK,CAAE,MACT,CAAC,CACDE,SAAS,CAAE,CACTqC,YAAY,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAC3B,CAAC,CACDC,YAAY,CAAE,KAAK,CACnBC,cAAc,CAAE,QAAAA,CAAUC,GAAG,CAAE,CAC7B,MAAO,CAAAA,GAAG,CAAG,GAAG,CAClB,CACF,CAAC,CACH,CAAC,CAED;AACArD,KAAK,CAACsD,SAAS,CAACpC,MAAM,CAAE,CACtBqC,QAAQ,CAAE,KAAK,CACfC,YAAY,CAAE,CAAC,QAAQ,CACzB,CAAC,CAAC,CACJ,CAAC,CAED;AACAjD,WAAW,CAAC,CAAC,CAEb;AACA,KAAM,CAAAkD,aAAa,CAAGpH,WAAW,CAACkE,WAAW,CAAE,KAAK,CAAC,CAAE;AAEvD;AACAN,YAAY,CAAGA,CAAA,GAAM,KAAAyD,MAAA,CACnB,CAAAA,MAAA,CAAA1D,KAAK,UAAA0D,MAAA,iBAALA,MAAA,CAAOC,MAAM,CAAC,CAAC,CACjB,CAAC,CACDhI,MAAM,CAACI,gBAAgB,CAAC,QAAQ,CAAEkE,YAAY,CAAC,CAE/C;AACA,MAAO,IAAM,CACX3D,aAAa,CAACmH,aAAa,CAAC,CAC5B,GAAIxD,YAAY,CAAE,CAChBtE,MAAM,CAACK,mBAAmB,CAAC,QAAQ,CAAEiE,YAAY,CAAC,CACpD,CACA,GAAID,KAAK,CAAE,CACTA,KAAK,CAACK,OAAO,CAAC,CAAC,CACjB,CACF,CAAC,CAEH,CAAE,MAAO9H,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CAAE,CAACM,UAAU,CAAC,CAAC,CAEhB;AACAvE,SAAS,CAAC,IAAM,CACd,KAAM,CAAAsP,gBAAgB,CAAI/H,KAAK,EAAK,CAClC,GAAI,CACF,GAAIA,KAAK,CAAClC,IAAI,EAAIkC,KAAK,CAAClC,IAAI,CAACmC,IAAI,GAAK,KAAK,CAAE,CAC3C,KAAM,CAAA+H,OAAO,CAAGhI,KAAK,CAAClC,IAAI,CAACA,IAAI,CAC/B,GAAI,CAACkK,OAAO,EAAI,CAACA,OAAO,CAACC,IAAI,CAAE,OAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,KAAM,CAAAC,QAAQ,CAAGhJ,UAAU,CAAC8I,OAAO,CAACG,MAAM,CAAC,CAC3C,KAAM,CAAAC,SAAS,CAAGlJ,UAAU,CAAC8I,OAAO,CAACK,OAAO,CAAC,CAC7C,KAAM,CAAAC,KAAK,CAAGN,OAAO,CAACM,KAAK,CAC3B,KAAM,CAAAC,GAAG,CAAGvI,KAAK,CAAClC,IAAI,CAACyK,GAAG,EAAI,EAAE,CAChC,KAAM,CAAAC,SAAS,CAAGxI,KAAK,CAAClC,IAAI,CAAC2K,EAAE,EAAIlJ,IAAI,CAACC,GAAG,CAAC,CAAC,CAE7C;AACA,KAAM,CAAAkJ,sBAAsB,CAAG,EAAE,CACjCV,OAAO,CAACC,IAAI,CAACtF,OAAO,CAAC3C,KAAK,EAAI,CAC5B,KAAM,CAAA2I,SAAS,CAAG3I,KAAK,CAAC2I,SAAS,CAEjC;AACA,GAAI,CAAAC,QAAQ,CAAG,EAAE,CACjB,GAAI,CAAAC,QAAQ,CAAG,EAAE,CACjB,GAAI,CAAAC,QAAQ,CAAG,EAAE,CAEjB,GAAGH,SAAS,GAAK,KAAK,EAAIA,SAAS,GAAK,KAAK,CAAC,CAC5C;AACAC,QAAQ,CAAGG,IAAI,CAACC,KAAK,CAACd,QAAQ,CAAGa,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DJ,QAAQ,CAAGE,IAAI,CAACC,KAAK,CAACZ,SAAS,CAAEW,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DH,QAAQ,CAAG,GAAGR,KAAK,IAAIC,GAAG,IAAII,SAAS,IAAIC,QAAQ,IAAIC,QAAQ,EAAE,CACnE,CAAC,IACI,IAAGF,SAAS,GAAK,KAAK,CAAC,CAC1B;AACA;AACAC,QAAQ,CAAGG,IAAI,CAACC,KAAK,CAACd,QAAQ,CAAGa,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DJ,QAAQ,CAAGE,IAAI,CAACC,KAAK,CAACZ,SAAS,CAAEW,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DH,QAAQ,CAAG,GAAGR,KAAK,IAAIC,GAAG,IAAII,SAAS,IAAIC,QAAQ,IAAIC,QAAQ,EAAE,CACnE,CAAC,IACG,CACF;AACAD,QAAQ,CAAGG,IAAI,CAACC,KAAK,CAACd,QAAQ,CAAGa,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DJ,QAAQ,CAAGE,IAAI,CAACC,KAAK,CAACZ,SAAS,CAAEW,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACF,IAAI,CAACE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAC/DH,QAAQ,CAAG,GAAGR,KAAK,IAAIC,GAAG,IAAII,SAAS,IAAIC,QAAQ,IAAIC,QAAQ,EAAE,CACnE,CAEA,KAAM,CAAAK,eAAe,CAAGC,mBAAmB,CACzCR,SAAS,CACTG,QAAQ,CACRN,SAAS,CACT,CAAE1J,GAAG,CAAEoJ,QAAQ,CAAEnJ,GAAG,CAAEqJ,SAAU,CAClC,CAAC,CAED,KAAM,CAAAgB,WAAW,CAAGF,eAAe,CAACE,WAAW,CAC/C;AACA,GAAI,CAAAlL,aAAa,CAAG,EAAE,CACtB,GAAI,CAAAmL,UAAU,CAAG,EAAE,CACnB,OAAOV,SAAS,EACd,IAAK,KAAK,CAAEzK,aAAa,CAAG,OAAO,CAAEmL,UAAU,CAAG,SAAS,CAAE,MAC7D,IAAK,KAAK,CAAEnL,aAAa,CAAG,OAAO,CAAEmL,UAAU,CAAG,SAAS,CAAE,MAC7D,IAAK,KAAK,CAAEnL,aAAa,CAAG,QAAQ,CAAEmL,UAAU,CAAG,SAAS,CAAE,MAC9D,IAAK,KAAK,CAAEnL,aAAa,CAAG,MAAM,CAAEmL,UAAU,CAAG,SAAS,CAAE,MAC5D,IAAK,KAAK,CAAEnL,aAAa,CAAG,MAAM,CAAEmL,UAAU,CAAG,SAAS,CAAE,MAC5D,IAAK,MAAM,CAAEnL,aAAa,CAAG,MAAM,CAAEmL,UAAU,CAAG,SAAS,CAAE,MAC7D,IAAK,KAAK,CAAEnL,aAAa,CAAG,MAAM,CAAEmL,UAAU,CAAG,SAAS,CAAE,MAC5D,QAASnL,aAAa,CAAG8B,KAAK,CAACsJ,WAAW,EAAI,MAAM,CAAED,UAAU,CAAG,SAAS,CAC9E,CACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA,GAAI,CAACD,WAAW,CAAE,CAChB;AACA,KAAM,CAAA7L,SAAS,CAAG,CAChBgM,OAAO,CAAEL,eAAe,CAACK,OAAO,CAChCZ,SAAS,CAAEA,SAAS,CACpBzK,aAAa,CAAEA,aAAa,CAC5BoK,KAAK,CAAEN,OAAO,CAACM,KAAK,EAAI,MAAM,CAC9BC,GAAG,CAAEA,GAAG,CACRL,QAAQ,CAAEA,QAAQ,CAClBE,SAAS,CAAEA,SAAS,CACpBoB,IAAI,CAAEC,0BAA0B,CAACvB,QAAQ,CAAEE,SAAS,CAAC,CACrDU,QAAQ,CAAEA,QAAQ,CAClBhE,KAAK,CAAEuE,UAAU,CACjBb,SAAS,CAAE,GAAI,CAAAjJ,IAAI,CAAC,CAAC,CAACmK,WAAW,CAAC,CACpC,CAAC,CAED;AACApM,oBAAoB,CAACC,SAAS,CAAC,CAACoM,IAAI,CAAC5L,OAAO,EAAI,CAC9C,GAAIA,OAAO,CAAE,CACXpB,OAAO,CAACqB,GAAG,CAAC,gBAAgBE,aAAa,EAAE,CAAC,CAE5C;AACA,KAAM,CAAA0L,QAAQ,CAAG,CACfzG,GAAG,CAAE5D,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGuJ,IAAI,CAACc,MAAM,CAAC,CAAC,CAC/B5J,IAAI,CAAE/B,aAAa,CACnB4L,IAAI,CAAE,GAAI,CAAAvK,IAAI,CAAC,CAAC,CAACiG,kBAAkB,CAAC,CAAC,CACrC8C,KAAK,CAAEN,OAAO,CAACM,KAAK,EAAI,MAAM,CAC9BxD,KAAK,CAAEuE,UAAU,CACjBV,SAAS,CAAEA,SAAS,CACpBoB,QAAQ,CAAE,CACR7B,QAAQ,CAAEA,QAAQ,CAClBE,SAAS,CAAEA,SACb,CAAC,CACDoB,IAAI,CAAEjM,SAAS,CAACiM,IAClB,CAAC,CAED/N,SAAS,CAAC4D,IAAI,EAAI,CAChB,KAAM,CAAA2K,SAAS,CAAG,CAACJ,QAAQ,CAAE,GAAGvK,IAAI,CAAC,CAAC4K,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAE;AACpD,MAAO,CAAAD,SAAS,CAClB,CAAC,CAAC,CACJ,CAAC,IAAM,CACLrN,OAAO,CAACD,KAAK,CAAC,aAAawB,aAAa,EAAE,CAAC,CAC7C,CACF,CAAC,CAAC,CAEFwK,sBAAsB,CAACwB,IAAI,CAACvB,SAAS,CAAC,CACxC,CAAC,IAAM,CACLhM,OAAO,CAACqB,GAAG,CAAC,kBAAkBE,aAAa,EAAE,CAAC,CAChD,CACF,CAAC,CAAC,CAEF;AACA,GAAIwK,sBAAsB,CAAClK,MAAM,CAAG,CAAC,CAAE,CACrC7B,OAAO,CAACqB,GAAG,CAAC,UAAU0K,sBAAsB,CAAClK,MAAM,cAAc,CAAC,CAElE;AACA2L,UAAU,CAAC,SAAY,CACrB,KAAM,CAAAvO,KAAK,CAAG,KAAM,CAAAuC,2BAA2B,CAAC,CAAC,CACjD,GAAIvC,KAAK,CAAE,CACTqB,aAAa,CAACqD,SAAS,GAAK,CAC1B,GAAGA,SAAS,CACZ,GAAG1E,KACL,CAAC,CAAC,CAAC,CACHe,OAAO,CAACqB,GAAG,CAAC,iBAAiB,CAAC,CAChC,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CACF,CACF,CAAE,MAAOtB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACtC,CACF,CAAC,CAED;AACAoD,MAAM,CAACI,gBAAgB,CAAC,SAAS,CAAE6H,gBAAgB,CAAC,CAEpD,MAAO,IAAM,CACXjI,MAAM,CAACK,mBAAmB,CAAC,SAAS,CAAE4H,gBAAgB,CAAC,CACzD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAqC,kBAAkB,CAAIzB,SAAS,EAAK,CACxC,OAAOA,SAAS,EACd,IAAK,KAAK,CAAE;AACV,MAAO,CAAE0B,aAAa,CAAE,MAAM,CAAEC,iBAAiB,CAAE,EAAG,CAAC,CAAE;AAC3D,IAAK,KAAK,CAAE;AACV,MAAO,CAAED,aAAa,CAAE,KAAK,CAAEC,iBAAiB,CAAE,EAAG,CAAC,CAAE;AAC1D,IAAK,KAAK,CAAE;AACV,MAAO,CAAED,aAAa,CAAE,KAAK,CAAEC,iBAAiB,CAAE,EAAG,CAAC,CAAE;AAC1D,IAAK,KAAK,CAAE;AACZ,IAAK,KAAK,CAAE;AACZ,IAAK,MAAM,CAAE;AACX,MAAO,CAAED,aAAa,CAAE,MAAM,CAAEC,iBAAiB,CAAE,EAAG,CAAC,CAAE;AAC3D,IAAK,KAAK,CAAE;AACV,MAAO,CAAED,aAAa,CAAE,KAAK,CAAEC,iBAAiB,CAAE,EAAG,CAAC,CAAE;AAC1D,QACE,MAAO,CAAED,aAAa,CAAE,IAAI,CAAEC,iBAAiB,CAAE,CAAE,CAAC,CAAE;AAC1D,CACF,CAAC,CAED;AACA,KAAM,CAAAnB,mBAAmB,CAAGA,CAACR,SAAS,CAAEG,QAAQ,CAAEnE,WAAW,CAAE4F,UAAU,GAAK,CAC5E,KAAM,CAAEF,aAAa,CAAEC,iBAAkB,CAAC,CAAGF,kBAAkB,CAACzB,SAAS,CAAC,CAE1E;AACA,IAAK,GAAI,CAAA6B,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGpN,cAAc,CAACkC,OAAO,CAACd,MAAM,CAAEgM,CAAC,EAAE,CAAE,CACtD,KAAM,CAAAC,WAAW,CAAGrN,cAAc,CAACkC,OAAO,CAACkL,CAAC,CAAC,CAE7C;AACA,GAAIC,WAAW,CAAC9B,SAAS,GAAKA,SAAS,CAAE,CACvC,SACF,CAEA;AACA,KAAM,CAAA+B,QAAQ,CAAG/F,WAAW,CAAG8F,WAAW,CAACpG,cAAc,CAEzD;AACA,GAAIqG,QAAQ,CAAGL,aAAa,CAAE,CAC5B,SACF,CAEA;AACA,KAAM,CAAAM,QAAQ,CAAGC,iBAAiB,CAChCL,UAAU,CAACzL,GAAG,CAAEyL,UAAU,CAACxL,GAAG,CAC9B0L,WAAW,CAACtD,QAAQ,CAACrI,GAAG,CAAE2L,WAAW,CAACtD,QAAQ,CAACpI,GACjD,CAAC,CAED;AACA,GAAI4L,QAAQ,EAAIL,iBAAiB,CAAE,CACjC;AACAG,WAAW,CAAC3B,QAAQ,CAAGA,QAAQ,CAC/B2B,WAAW,CAACpG,cAAc,CAAGM,WAAW,CACxC8F,WAAW,CAACtD,QAAQ,CAAG,CAAE,GAAGoD,UAAW,CAAC,CACxCE,WAAW,CAACI,WAAW,CAAG,CAACJ,WAAW,CAACI,WAAW,EAAI,CAAC,EAAI,CAAC,CAE5DlO,OAAO,CAACqB,GAAG,CAAC,cAAc2K,SAAS,SAAS8B,WAAW,CAAClB,OAAO,UAAU,CAACmB,QAAQ,CAAC,IAAI,EAAEvL,OAAO,CAAC,CAAC,CAAC,SAASwL,QAAQ,CAACxL,OAAO,CAAC,CAAC,CAAC,WAAWsL,WAAW,CAACI,WAAW,EAAE,CAAC,CAEpK,MAAO,CACLzB,WAAW,CAAE,IAAI,CACjBG,OAAO,CAAEkB,WAAW,CAAClB,OAAO,CAC5BuB,YAAY,CAAEL,WAChB,CAAC,CACH,CACF,CAEA;AACA,KAAM,CAAAM,UAAU,CAAG,OAAO1N,cAAc,CAACiC,OAAO,CAAC0L,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CAC9E5N,cAAc,CAACiC,OAAO,EAAE,CAExB,KAAM,CAAAsK,QAAQ,CAAG,CACfL,OAAO,CAAEwB,UAAU,CACnBpC,SAAS,CAAEA,SAAS,CACpBG,QAAQ,CAAEA,QAAQ,CAClBoC,iBAAiB,CAAEvG,WAAW,CAC9BN,cAAc,CAAEM,WAAW,CAC3BwC,QAAQ,CAAE,CAAE,GAAGoD,UAAW,CAAC,CAC3BM,WAAW,CAAE,CACf,CAAC,CAED;AACAzN,cAAc,CAACkC,OAAO,CAAC4K,IAAI,CAACN,QAAQ,CAAC,CAErCjN,OAAO,CAACqB,GAAG,CAAC,WAAW2K,SAAS,SAASoC,UAAU,UAAUR,UAAU,CAACzL,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,KAAKoL,UAAU,CAACxL,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAExH,MAAO,CACLiK,WAAW,CAAE,KAAK,CAClBG,OAAO,CAAEwB,UAAU,CACnBnB,QAAQ,CAAEA,QACZ,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAAjJ,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAgE,WAAW,CAAGpF,IAAI,CAACC,GAAG,CAAC,CAAC,CAC9B,KAAM,CAAA2L,MAAM,CAAG,OAAO,CAAE;AAExB,KAAM,CAAAC,YAAY,CAAGhO,cAAc,CAACkC,OAAO,CAACd,MAAM,CAClDpB,cAAc,CAACkC,OAAO,CAAGlC,cAAc,CAACkC,OAAO,CAAC6B,MAAM,CAACnB,KAAK,EAAI,CAC9D,KAAM,CAAAqL,GAAG,CAAG1G,WAAW,CAAG3E,KAAK,CAACqE,cAAc,CAC9C,MAAO,CAAAgH,GAAG,EAAIF,MAAM,CACtB,CAAC,CAAC,CAEF,KAAM,CAAAG,YAAY,CAAGF,YAAY,CAAGhO,cAAc,CAACkC,OAAO,CAACd,MAAM,CACjE,GAAI8M,YAAY,CAAG,CAAC,CAAE,CACpB3O,OAAO,CAACqB,GAAG,CAAC,UAAUsN,YAAY,mBAAmBlO,cAAc,CAACkC,OAAO,CAACd,MAAM,EAAE,CAAC,CACvF,CACF,CAAC,CAED;AACA,KAAM,CAAAqC,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAA8D,WAAW,CAAGpF,IAAI,CAACC,GAAG,CAAC,CAAC,CAC9B,KAAM,CAAA+L,iBAAiB,CAAG,KAAK,CAAE;AAEjC,KAAM,CAAAH,YAAY,CAAGhO,cAAc,CAACkC,OAAO,CAACd,MAAM,CAClD,KAAM,CAAAgN,aAAa,CAAG,EAAE,CAExBpO,cAAc,CAACkC,OAAO,CAAGlC,cAAc,CAACkC,OAAO,CAAC6B,MAAM,CAACnB,KAAK,EAAI,CAC9D,KAAM,CAAAyL,mBAAmB,CAAG9G,WAAW,CAAG3E,KAAK,CAACqE,cAAc,CAC9D,GAAIoH,mBAAmB,CAAGF,iBAAiB,CAAE,CAC3CC,aAAa,CAACtB,IAAI,CAAC,CACjB9I,EAAE,CAAEpB,KAAK,CAACuJ,OAAO,CACjBtJ,IAAI,CAAED,KAAK,CAAC2I,SAAS,CACrB+C,YAAY,CAAE,CAACD,mBAAmB,CAAG,IAAI,EAAEtM,OAAO,CAAC,CAAC,CACtD,CAAC,CAAC,CACF,MAAO,MAAK,CAAE;AAChB,CACA,MAAO,KAAI,CAAE;AACf,CAAC,CAAC,CAEF,KAAM,CAAAmM,YAAY,CAAGF,YAAY,CAAGhO,cAAc,CAACkC,OAAO,CAACd,MAAM,CACjE,GAAI8M,YAAY,CAAG,CAAC,CAAE,CACpB3O,OAAO,CAACqB,GAAG,CAAC,WAAWsN,YAAY,eAAe,CAAC,CACnDE,aAAa,CAAC7I,OAAO,CAAC3C,KAAK,EAAI,CAC7BrD,OAAO,CAACqB,GAAG,CAAC,WAAWgC,KAAK,CAACoB,EAAE,SAASpB,KAAK,CAACC,IAAI,YAAYD,KAAK,CAAC0L,YAAY,GAAG,CAAC,CACtF,CAAC,CAAC,CACF/O,OAAO,CAACqB,GAAG,CAAC,eAAeZ,cAAc,CAACkC,OAAO,CAACd,MAAM,EAAE,CAAC,CAC7D,CACF,CAAC,CAED;AACA,KAAM,CAAAoM,iBAAiB,CAAGA,CAACe,IAAI,CAAEC,IAAI,CAAEC,IAAI,CAAEC,IAAI,GAAK,CACpD,GAAIH,IAAI,GAAK,CAAC,EAAIC,IAAI,GAAK,CAAC,EAAIC,IAAI,GAAK,CAAC,EAAIC,IAAI,GAAK,CAAC,CAAE,CACxD,MAAO,IAAG,CACZ,CACA,KAAM,CAAAC,CAAC,CAAG,OAAO,CACjB,KAAM,CAAAC,IAAI,CAAG,CAACH,IAAI,CAAGF,IAAI,EAAI5C,IAAI,CAACkD,EAAE,CAAG,GAAG,CAC1C,KAAM,CAAAC,IAAI,CAAG,CAACJ,IAAI,CAAGF,IAAI,EAAI7C,IAAI,CAACkD,EAAE,CAAG,GAAG,CAC1C,KAAM,CAAA9G,CAAC,CACL4D,IAAI,CAACoD,GAAG,CAACH,IAAI,CAAC,CAAC,CAAC,CAAGjD,IAAI,CAACoD,GAAG,CAACH,IAAI,CAAC,CAAC,CAAC,CACnCjD,IAAI,CAACqD,GAAG,CAACT,IAAI,CAAG5C,IAAI,CAACkD,EAAE,CAAG,GAAG,CAAC,CAAGlD,IAAI,CAACqD,GAAG,CAACP,IAAI,CAAG9C,IAAI,CAACkD,EAAE,CAAG,GAAG,CAAC,CAC/DlD,IAAI,CAACoD,GAAG,CAACD,IAAI,CAAC,CAAC,CAAC,CAAGnD,IAAI,CAACoD,GAAG,CAACD,IAAI,CAAC,CAAC,CAAC,CACrC,KAAM,CAAAG,CAAC,CAAG,CAAC,CAAGtD,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACwD,IAAI,CAACpH,CAAC,CAAC,CAAE4D,IAAI,CAACwD,IAAI,CAAC,CAAC,CAACpH,CAAC,CAAC,CAAC,CACtD,MAAO,CAAA4G,CAAC,CAAGM,CAAC,CACd,CAAC,CAED;AACA,KAAM,CAAA5C,0BAA0B,CAAGA,CAAC3K,GAAG,CAAEC,GAAG,GAAK,CAC/C;AACA,KAAM,CAAAyN,aAAa,CAAGvS,iBAAiB,CAACuS,aAAa,EAAI,EAAE,CAC3D,GAAI,CAAAC,WAAW,CAAGC,QAAQ,CAC1B,GAAI,CAAAC,WAAW,CAAG,MAAM,CACxBH,aAAa,CAAC7J,OAAO,CAACiK,KAAK,EAAI,CAC7B,KAAM,CAAAC,QAAQ,CAAG3N,UAAU,CAAC0N,KAAK,CAAC1E,QAAQ,CAAC,CAC3C,KAAM,CAAA4E,QAAQ,CAAG5N,UAAU,CAAC0N,KAAK,CAACxE,SAAS,CAAC,CAC5C,KAAM,CAAA2E,IAAI,CAAGnC,iBAAiB,CAAC9L,GAAG,CAAEC,GAAG,CAAE8N,QAAQ,CAAEC,QAAQ,CAAC,CAC5D,GAAIC,IAAI,CAAGN,WAAW,CAAE,CACtBA,WAAW,CAAGM,IAAI,CAClBJ,WAAW,CAAGC,KAAK,CAAC/H,IAAI,CAC1B,CACF,CAAC,CAAC,CACF,MAAO,CAAA8H,WAAW,CACpB,CAAC,CAED;AACA,KAAM,CAAAK,mBAAmB,CAAInN,OAAO,EAAK,CACvClD,OAAO,CAACqB,GAAG,CAAC,OAAO,CAAE6B,OAAO,CAAC4B,WAAW,CAAE,KAAK,CAAE5B,OAAO,CAACjB,MAAM,CAAC,CAChEjD,kBAAkB,CAACkE,OAAO,CAAC,CAC7B,CAAC,CAED;AACApH,SAAS,CAAC,IAAM,CACd;AACA,GAAIiD,eAAe,CAAE,CACnB,KAAM,CAAAuR,sBAAsB,CAAG3R,QAAQ,CAACiH,IAAI,CAACrB,CAAC,EAAIA,CAAC,CAACE,EAAE,GAAK1F,eAAe,CAAC0F,EAAE,CAAC,CAC9E,GAAI6L,sBAAsB,GACrBA,sBAAsB,CAACrO,MAAM,GAAKlD,eAAe,CAACkD,MAAM,EACxDqO,sBAAsB,CAACpO,KAAK,GAAKnD,eAAe,CAACmD,KAAK,EACtDoO,sBAAsB,CAACnO,GAAG,GAAKpD,eAAe,CAACoD,GAAG,EAClDmO,sBAAsB,CAAClO,GAAG,GAAKrD,eAAe,CAACqD,GAAG,EAClDkO,sBAAsB,CAACjO,OAAO,GAAKtD,eAAe,CAACsD,OAAO,CAAC,CAAE,CAChErC,OAAO,CAACqB,GAAG,CAAC,UAAUtC,eAAe,CAAC+F,WAAW,OAAO,CAC7C,OAAO/F,eAAe,CAACkD,MAAM,OAAOqO,sBAAsB,CAACrO,MAAM,EAAE,CAAC,CAC/EjD,kBAAkB,CAACsR,sBAAsB,CAAC,CAC5C,CACF,CACF,CAAC,CAAE,CAAC3R,QAAQ,CAAEI,eAAe,CAAC,CAAC,CAE/B;AACA,KAAM,CAAAwR,cAAc,CAAG,CACrB,CACE5H,KAAK,CAAE,KAAK,CACZ6H,SAAS,CAAE,OAAO,CAClBhK,GAAG,CAAE,OAAO,CACZiK,KAAK,CAAE,KACT,CAAC,CACD,CACE9H,KAAK,CAAE,IAAI,CACX6H,SAAS,CAAE,QAAQ,CACnBhK,GAAG,CAAE,QAAQ,CACbiK,KAAK,CAAE,KAAK,CACZC,MAAM,CAAEzO,MAAM,eACZzE,IAAA,CAACf,KAAK,EACJwF,MAAM,CAAEA,MAAM,GAAK,QAAQ,CAAG,SAAS,CAAG,OAAQ,CAClD2G,IAAI,CAAE3G,MAAM,GAAK,QAAQ,CAAG,IAAI,CAAG,IAAK,CACzC,CAEL,CAAC,CACD,CACE0G,KAAK,CAAE,IAAI,CACX6H,SAAS,CAAE,OAAO,CAClBhK,GAAG,CAAE,OAAO,CACZiK,KAAK,CAAE,KAAK,CACZC,MAAM,CAAExO,KAAK,EAAI,GAAG,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAGA,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAAGN,KAAK,OAC1E,CAAC,CACF,CAED;AACA,KAAM,CAAAyO,eAAe,CAAGA,CAAA,gBACtBnT,IAAA,CAACnB,IAAI,EACH+J,IAAI,CAAC,OAAO,CACZwK,UAAU,CAAE/R,MAAO,CACnBgS,UAAU,CAAEvI,IAAI,eACd9K,IAAA,CAACnB,IAAI,CAACyU,IAAI,EAACC,KAAK,CAAE,CAAEC,OAAO,CAAE,OAAQ,CAAE,CAAAC,QAAA,cACrCzT,IAAA,CAACnB,IAAI,CAACyU,IAAI,CAACI,IAAI,EACbvI,KAAK,cACHjL,KAAA,QAAKqT,KAAK,CAAE,CAAE9H,QAAQ,CAAE,MAAM,CAAEkI,YAAY,CAAE,KAAM,CAAE,CAAAF,QAAA,eACpDzT,IAAA,SAAMuT,KAAK,CAAE,CAAE5I,KAAK,CAAEG,IAAI,CAACH,KAAK,CAAEiJ,WAAW,CAAE,KAAM,CAAE,CAAAH,QAAA,CACpD3I,IAAI,CAAChF,IAAI,CACN,CAAC,cACP9F,IAAA,SAAMuT,KAAK,CAAE,CAAE5I,KAAK,CAAE,MAAM,CAAEc,QAAQ,CAAE,MAAO,CAAE,CAAAgI,QAAA,CAC9C3I,IAAI,CAAC6E,IAAI,CACN,CAAC,EACJ,CACN,CACDR,WAAW,cACTjP,KAAA,QAAKqT,KAAK,CAAE,CAAE9H,QAAQ,CAAE,MAAM,CAAEd,KAAK,CAAE,MAAO,CAAE,CAAA8I,QAAA,eAE9CvT,KAAA,QAAAuT,QAAA,EAAK,gBAAI,CAAC3I,IAAI,CAACuE,IAAI,EAAI,MAAM,EAAM,CAAC,cACpCnP,KAAA,QAAAuT,QAAA,EAAK,gBAAI,CAAC3I,IAAI,CAAC8E,QAAQ,CACrB,GAAG9E,IAAI,CAAC8E,QAAQ,CAAC7B,QAAQ,CAAC/I,OAAO,CAAC,CAAC,CAAC,KAAK8F,IAAI,CAAC8E,QAAQ,CAAC3B,SAAS,CAACjJ,OAAO,CAAC,CAAC,CAAC,EAAE,CAC7E,MAAM,EACH,CAAC,EACH,CACN,CACF,CAAC,CACO,CACX,CACFuO,KAAK,CAAE,CACLM,SAAS,CAAE,mBAAmB,CAC9BC,SAAS,CAAE,MACb,CAAE,CACH,CACF,CAED;AACAxV,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyV,kBAAkB,CAAG1N,WAAW,CAAC,IAAM,CAC3C7D,OAAO,CAACqB,GAAG,CAAC,gBAAgB,CAAC,CAC7BgD,uBAAuB,CAAC,CAAC,CAC3B,CAAC,CAAE,KAAK,CAAC,CAAE;AAEX,MAAO,IAAMP,aAAa,CAACyN,kBAAkB,CAAC,CAChD,CAAC,CAAE,CAAC5S,QAAQ,CAAC,CAAC,CAEd,mBACEnB,IAAA,CAAChB,IAAI,EAACgV,QAAQ,CAAE/S,OAAQ,CAACgT,GAAG,CAAC,uBAAQ,CAAAR,QAAA,cACnCvT,KAAA,CAACI,aAAa,EAAAmT,QAAA,eAEZvT,KAAA,CAACP,kBAAkB,EACjBqN,QAAQ,CAAC,MAAM,CACfkH,SAAS,CAAEvT,aAAc,CACzBwT,UAAU,CAAEA,CAAA,GAAMxR,gBAAgB,CAAC,CAAChC,aAAa,CAAE,CAAA8S,QAAA,eAGnDzT,IAAA,CAACa,QAAQ,EAACsK,KAAK,CAAC,4CAAS,CAACiJ,QAAQ,CAAE,KAAM,CAACtT,MAAM,CAAC,OAAO,CAAA2S,QAAA,cACvDvT,KAAA,CAACzB,GAAG,EAAC4V,MAAM,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAAZ,QAAA,eAClBzT,IAAA,CAACtB,GAAG,EAAC4V,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjEzT,IAAA,CAACe,gBAAgB,EACfoK,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEnJ,KAAK,CAACE,aAAc,CAC3B8S,UAAU,CAAE,CAAE9J,KAAK,CAAE,SAAS,CAAC4J,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CACxE;AAAA,CACD,CAAC,CACC,CAAC,cACNxU,IAAA,CAACtB,GAAG,EAAC4V,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjEzT,IAAA,CAACe,gBAAgB,EACfoK,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEnJ,KAAK,CAACG,cACb;AAAA,CACA6S,UAAU,CAAE,CAAE9J,KAAK,CAAE,SAAS,CAAE4J,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAQ,CAAE,CAC3E,CAAC,CACC,CAAC,cACNxU,IAAA,CAACtB,GAAG,EAAC4V,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjEzT,IAAA,CAACe,gBAAgB,EACfoK,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEnJ,KAAK,CAACI,eACb;AAAA,CACA4S,UAAU,CAAE,CAAE9J,KAAK,CAAE,SAAS,CAAE4J,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CAAE,CAC5E,CAAC,CACC,CAAC,cACNxU,IAAA,CAACtB,GAAG,EAAC4V,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjEzT,IAAA,CAACe,gBAAgB,EACfoK,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEnJ,KAAK,CAACK,YAAa,CAC1B2S,UAAU,CAAE,CAAE9J,KAAK,CAAE,SAAS,CAAC4J,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CAAE,CAC3E,CAAC,CACC,CAAC,cACNxU,IAAA,CAACtB,GAAG,EAAC4V,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjEzT,IAAA,CAACe,gBAAgB,EACfoK,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEnJ,KAAK,CAACM,aACb;AAAA,CACA0S,UAAU,CAAE,CAAE9J,KAAK,CAAE,SAAS,CAAC4J,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CAAE,CAC3E,CAAC,CACC,CAAC,cACNxU,IAAA,CAACtB,GAAG,EAAC4V,IAAI,CAAE,CAAE,CAACf,KAAK,CAAE,CAAEgB,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAf,QAAA,cACjEzT,IAAA,CAACe,gBAAgB,EACfoK,KAAK,CAAC,0BAAM,CACZP,KAAK,CAAEnJ,KAAK,CAACO,cACb;AAAA,CACAyS,UAAU,CAAE,CAAE9J,KAAK,CAAE,SAAS,CAAC4J,OAAO,CAAE,MAAM,CAACC,cAAc,CAAE,QAAS,CAAE,CAC3E,CAAC,CACC,CAAC,EACH,CAAC,CACE,CAAC,cAGXxU,IAAA,CAACa,QAAQ,EACPsK,KAAK,CAAC,sCAAQ,CACdiJ,QAAQ,CAAE,KAAM,CAChBtT,MAAM,CAAC,kBACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CAAA2S,QAAA,CAECN,eAAe,CAAC,CAAC,CACV,CAAC,cAGXnT,IAAA,CAACa,QAAQ,EAACsK,KAAK,CAAC,sCAAQ,CAACiJ,QAAQ,CAAE,KAAM,CAACtT,MAAM,CAAC,kBAAkB,CAAA2S,QAAA,cACjEzT,IAAA,QAAK0U,GAAG,CAAEhS,aAAc,CAAC6Q,KAAK,CAAE,CAAEzS,MAAM,CAAE,MAAM,CAAEmS,KAAK,CAAE,MAAO,CAAE,CAAM,CAAC,CACjE,CAAC,EACO,CAAC,cAGrBjT,IAAA,CAACS,WAAW,EAACE,aAAa,CAAEA,aAAc,CAACC,cAAc,CAAEA,cAAe,CAE7D,CAAC,cAGdV,KAAA,CAACP,kBAAkB,EACjBqN,QAAQ,CAAC,OAAO,CAChBkH,SAAS,CAAEtT,cAAe,CAC1BuT,UAAU,CAAEA,CAAA,GAAMvR,iBAAiB,CAAC,CAAChC,cAAc,CAAE,CAAA6S,QAAA,eAGrDzT,IAAA,CAACa,QAAQ,EAACsK,KAAK,CAAC,0BAAM,CAACiJ,QAAQ,CAAE,KAAM,CAACtT,MAAM,CAAC,KAAK,CAAA2S,QAAA,cAClDzT,IAAA,CAAClB,KAAK,EACJsU,UAAU,CAAEjS,QAAS,CACrBwT,OAAO,CAAE5B,cAAe,CACxB6B,MAAM,CAAC,IAAI,CACXC,UAAU,CAAE,KAAM,CAClBjM,IAAI,CAAC,OAAO,CACZkM,MAAM,CAAE,CAAEC,CAAC,CAAE,GAAI,CAAE,CACnBC,KAAK,CAAGC,MAAM,GAAM,CAClBC,OAAO,CAAEA,CAAA,GAAMrC,mBAAmB,CAACoC,MAAM,CAAC,CAC1C1B,KAAK,CAAE,CACL4B,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,CAAA7T,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE0F,EAAE,IAAKgO,MAAM,CAAChO,EAAE,CAAG,SAAS,CAAG,aAAa,CACzEwE,QAAQ,CAAE,MAAM,CAChB+H,OAAO,CAAE,SACX,CACF,CAAC,CAAE,CACJ,CAAC,CACM,CAAC,cAGXxT,IAAA,CAACa,QAAQ,EAACsK,KAAK,CAAC,sCAAQ,CAACiJ,QAAQ,CAAE,KAAM,CAACtT,MAAM,CAAC,KAAK,CAAA2S,QAAA,CACnDlS,eAAe,cACdrB,KAAA,CAACnB,YAAY,EACXqV,QAAQ,MACRiB,MAAM,CAAE,CAAE,CACVzM,IAAI,CAAC,OAAO,CACZ0M,MAAM,CAAE,CACNvI,KAAK,CAAE,CAAEtB,QAAQ,CAAE,MAAM,CAAE+H,OAAO,CAAE,SAAU,CAAC,CAC/C+B,OAAO,CAAE,CAAE9J,QAAQ,CAAE,MAAM,CAAE+H,OAAO,CAAE,SAAU,CAClD,CAAE,CAAAC,QAAA,eAEFzT,IAAA,CAACjB,YAAY,CAACuU,IAAI,EAACvG,KAAK,CAAC,oBAAK,CAAA0G,QAAA,CAAElS,eAAe,CAAC+F,WAAW,CAAoB,CAAC,cAChFtH,IAAA,CAACjB,YAAY,CAACuU,IAAI,EAACvG,KAAK,CAAC,cAAI,CAAA0G,QAAA,cAC3BzT,IAAA,CAACf,KAAK,EACJwF,MAAM,CAAElD,eAAe,CAACkD,MAAM,GAAK,QAAQ,CAAG,SAAS,CAAG,OAAQ,CAClE2G,IAAI,CAAE7J,eAAe,CAACkD,MAAM,GAAK,QAAQ,CAAG,IAAI,CAAG,IAAK,CACzD,CAAC,CACe,CAAC,cACpBzE,IAAA,CAACjB,YAAY,CAACuU,IAAI,EAACvG,KAAK,CAAC,cAAI,CAAA0G,QAAA,CAC1BlS,eAAe,CAACkD,MAAM,GAAK,QAAQ,CAAGlD,eAAe,CAACqD,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAC5D,CAAC,cACpBhF,IAAA,CAACjB,YAAY,CAACuU,IAAI,EAACvG,KAAK,CAAC,cAAI,CAAA0G,QAAA,CAC1BlS,eAAe,CAACkD,MAAM,GAAK,QAAQ,CAAGlD,eAAe,CAACoD,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,CAAG,KAAK,CAC5D,CAAC,cACpBhF,IAAA,CAACjB,YAAY,CAACuU,IAAI,EAACvG,KAAK,CAAC,cAAI,CAAA0G,QAAA,CAC1BlS,eAAe,CAACkD,MAAM,GAAK,QAAQ,CAClC,GAAG,MAAO,CAAAlD,eAAe,CAACmD,KAAK,GAAK,QAAQ,CAAGnD,eAAe,CAACmD,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,CAAGzD,eAAe,CAACmD,KAAK,OAAO,CAC9G,KAAK,CACU,CAAC,cACpB1E,IAAA,CAACjB,YAAY,CAACuU,IAAI,EAACvG,KAAK,CAAC,oBAAK,CAAA0G,QAAA,CAC3BlS,eAAe,CAACkD,MAAM,GAAK,QAAQ,CAClC,GAAG,MAAO,CAAAlD,eAAe,CAACsD,OAAO,GAAK,QAAQ,CAAGtD,eAAe,CAACsD,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,CAAGzD,eAAe,CAACsD,OAAO,GAAG,CAChH,KAAK,CACU,CAAC,EACR,CAAC,cAEf7E,IAAA,MAAGuT,KAAK,CAAE,CAAE9H,QAAQ,CAAE,MAAO,CAAE,CAAAgI,QAAA,CAAC,oEAAW,CAAG,CAC/C,CACO,CAAC,EACO,CAAC,EACR,CAAC,CACZ,CAAC,CAEX,CAAC,CAED,cAAe,CAAAzS,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}