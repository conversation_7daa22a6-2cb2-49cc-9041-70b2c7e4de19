{"ast": null, "code": "import { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genStatisticStyle = token => {\n  const {\n    componentCls,\n    marginXXS,\n    padding,\n    colorTextDescription,\n    titleFontSize,\n    colorTextHeading,\n    contentFontSize,\n    fontFamily\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      [`${componentCls}-title`]: {\n        marginBottom: marginXXS,\n        color: colorTextDescription,\n        fontSize: titleFontSize\n      },\n      [`${componentCls}-skeleton`]: {\n        paddingTop: padding\n      },\n      [`${componentCls}-content`]: {\n        color: colorTextHeading,\n        fontSize: contentFontSize,\n        fontFamily,\n        [`${componentCls}-content-value`]: {\n          display: 'inline-block',\n          direction: 'ltr'\n        },\n        [`${componentCls}-content-prefix, ${componentCls}-content-suffix`]: {\n          display: 'inline-block'\n        },\n        [`${componentCls}-content-prefix`]: {\n          marginInlineEnd: marginXXS\n        },\n        [`${componentCls}-content-suffix`]: {\n          marginInlineStart: marginXXS\n        }\n      }\n    })\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  const {\n    fontSizeHeading3,\n    fontSize\n  } = token;\n  return {\n    titleFontSize: fontSize,\n    contentFontSize: fontSizeHeading3\n  };\n};\nexport default genStyleHooks('Statistic', token => {\n  const statisticToken = mergeToken(token, {});\n  return [genStatisticStyle(statisticToken)];\n}, prepareComponentToken);", "map": {"version": 3, "names": ["resetComponent", "genStyleHooks", "mergeToken", "genStatisticStyle", "token", "componentCls", "marginXXS", "padding", "colorTextDescription", "titleFontSize", "colorTextHeading", "contentFontSize", "fontFamily", "Object", "assign", "marginBottom", "color", "fontSize", "paddingTop", "display", "direction", "marginInlineEnd", "marginInlineStart", "prepareComponentToken", "fontSizeHeading3", "statisticToken"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/statistic/style/index.js"], "sourcesContent": ["import { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genStatisticStyle = token => {\n  const {\n    componentCls,\n    marginXXS,\n    padding,\n    colorTextDescription,\n    titleFontSize,\n    colorTextHeading,\n    contentFontSize,\n    fontFamily\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      [`${componentCls}-title`]: {\n        marginBottom: marginXXS,\n        color: colorTextDescription,\n        fontSize: titleFontSize\n      },\n      [`${componentCls}-skeleton`]: {\n        paddingTop: padding\n      },\n      [`${componentCls}-content`]: {\n        color: colorTextHeading,\n        fontSize: contentFontSize,\n        fontFamily,\n        [`${componentCls}-content-value`]: {\n          display: 'inline-block',\n          direction: 'ltr'\n        },\n        [`${componentCls}-content-prefix, ${componentCls}-content-suffix`]: {\n          display: 'inline-block'\n        },\n        [`${componentCls}-content-prefix`]: {\n          marginInlineEnd: marginXXS\n        },\n        [`${componentCls}-content-suffix`]: {\n          marginInlineStart: marginXXS\n        }\n      }\n    })\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  const {\n    fontSizeHeading3,\n    fontSize\n  } = token;\n  return {\n    titleFontSize: fontSize,\n    contentFontSize: fontSizeHeading3\n  };\n};\nexport default genStyleHooks('Statistic', token => {\n  const statisticToken = mergeToken(token, {});\n  return [genStatisticStyle(statisticToken)];\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,cAAc,QAAQ,aAAa;AAC5C,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,MAAMC,iBAAiB,GAAGC,KAAK,IAAI;EACjC,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,OAAO;IACPC,oBAAoB;IACpBC,aAAa;IACbC,gBAAgB;IAChBC,eAAe;IACfC;EACF,CAAC,GAAGR,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAGQ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEd,cAAc,CAACI,KAAK,CAAC,CAAC,EAAE;MACtE,CAAC,GAAGC,YAAY,QAAQ,GAAG;QACzBU,YAAY,EAAET,SAAS;QACvBU,KAAK,EAAER,oBAAoB;QAC3BS,QAAQ,EAAER;MACZ,CAAC;MACD,CAAC,GAAGJ,YAAY,WAAW,GAAG;QAC5Ba,UAAU,EAAEX;MACd,CAAC;MACD,CAAC,GAAGF,YAAY,UAAU,GAAG;QAC3BW,KAAK,EAAEN,gBAAgB;QACvBO,QAAQ,EAAEN,eAAe;QACzBC,UAAU;QACV,CAAC,GAAGP,YAAY,gBAAgB,GAAG;UACjCc,OAAO,EAAE,cAAc;UACvBC,SAAS,EAAE;QACb,CAAC;QACD,CAAC,GAAGf,YAAY,oBAAoBA,YAAY,iBAAiB,GAAG;UAClEc,OAAO,EAAE;QACX,CAAC;QACD,CAAC,GAAGd,YAAY,iBAAiB,GAAG;UAClCgB,eAAe,EAAEf;QACnB,CAAC;QACD,CAAC,GAAGD,YAAY,iBAAiB,GAAG;UAClCiB,iBAAiB,EAAEhB;QACrB;MACF;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD;AACA,OAAO,MAAMiB,qBAAqB,GAAGnB,KAAK,IAAI;EAC5C,MAAM;IACJoB,gBAAgB;IAChBP;EACF,CAAC,GAAGb,KAAK;EACT,OAAO;IACLK,aAAa,EAAEQ,QAAQ;IACvBN,eAAe,EAAEa;EACnB,CAAC;AACH,CAAC;AACD,eAAevB,aAAa,CAAC,WAAW,EAAEG,KAAK,IAAI;EACjD,MAAMqB,cAAc,GAAGvB,UAAU,CAACE,KAAK,EAAE,CAAC,CAAC,CAAC;EAC5C,OAAO,CAACD,iBAAiB,CAACsB,cAAc,CAAC,CAAC;AAC5C,CAAC,EAAEF,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}