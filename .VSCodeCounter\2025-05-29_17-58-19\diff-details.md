# Diff Details

Date : 2025-05-29 17:58:19

Directory g:\\AI_tools\\cursor\\projects\\education web

Total : 35 files,  857 codes, -17 comments, -20 blanks, all 820 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.env](/.env) | Properties | -1 | 0 | 0 | -1 |
| [README.md](/README.md) | Markdown | -2 | 0 | 0 | -2 |
| [build/images/camera.svg](/build/images/camera.svg) | XML | 9 | 0 | 1 | 10 |
| [build/images/compass.svg](/build/images/compass.svg) | XML | 1 | 0 | 0 | 1 |
| [build/images/edge\_computing.svg](/build/images/edge_computing.svg) | XML | 4 | 0 | 1 | 5 |
| [build/images/lidar.svg](/build/images/lidar.svg) | XML | 5 | 0 | 1 | 6 |
| [build/images/mmwave\_radar.svg](/build/images/mmwave_radar.svg) | XML | 6 | 0 | 1 | 7 |
| [build/images/obu.svg](/build/images/obu.svg) | XML | 9 | 0 | 1 | 10 |
| [build/images/rsu.svg](/build/images/rsu.svg) | XML | 8 | 0 | 1 | 9 |
| [build/static/js/main.4c7fc37b.js](/build/static/js/main.4c7fc37b.js) | JavaScript | -420 | -65 | -32 | -517 |
| [build/static/js/main.92f83330.js](/build/static/js/main.92f83330.js) | JavaScript | 422 | 69 | 32 | 523 |
| [package-lock.json](/package-lock.json) | JSON | 101 | 0 | 0 | 101 |
| [package.json](/package.json) | JSON | 2 | 0 | 0 | 2 |
| [public/images/camera.svg](/public/images/camera.svg) | XML | 9 | 0 | 1 | 10 |
| [public/images/compass.svg](/public/images/compass.svg) | XML | 1 | 0 | 0 | 1 |
| [public/images/edge\_computing.svg](/public/images/edge_computing.svg) | XML | 4 | 0 | 1 | 5 |
| [public/images/lidar.svg](/public/images/lidar.svg) | XML | 5 | 0 | 1 | 6 |
| [public/images/mmwave\_radar.svg](/public/images/mmwave_radar.svg) | XML | 6 | 0 | 1 | 7 |
| [public/images/obu.svg](/public/images/obu.svg) | XML | 9 | 0 | 1 | 10 |
| [public/images/rsu.svg](/public/images/rsu.svg) | XML | 8 | 0 | 1 | 9 |
| [server/mqtt-ws-bridge.js](/server/mqtt-ws-bridge.js) | JavaScript | 57 | 18 | 16 | 91 |
| [server/stream-server.js](/server/stream-server.js) | JavaScript | 29 | 7 | 1 | 37 |
| [src/components/CampusModel.jsx](/src/components/CampusModel.jsx) | JavaScript JSX | 360 | -127 | -15 | 218 |
| [src/components/DevicePopoverContent.jsx](/src/components/DevicePopoverContent.jsx) | JavaScript JSX | 150 | 14 | 6 | 170 |
| [src/components/RealTimeEvents.jsx](/src/components/RealTimeEvents.jsx) | JavaScript JSX | -228 | -19 | -37 | -284 |
| [src/components/VideoPlayer.jsx](/src/components/VideoPlayer.jsx) | JavaScript JSX | 39 | 0 | 0 | 39 |
| [src/data/devices.json](/src/data/devices.json) | JSON | 47 | 0 | 0 | 47 |
| [src/data/intersections.json](/src/data/intersections.json) | JSON | 43 | 0 | 0 | 43 |
| [src/pages/DeviceManagement.jsx](/src/pages/DeviceManagement.jsx) | JavaScript JSX | 70 | 16 | 7 | 93 |
| [src/pages/DeviceStatus.jsx](/src/pages/DeviceStatus.jsx) | JavaScript JSX | -41 | 6 | 1 | -34 |
| [src/pages/RealTimeTraffic.jsx](/src/pages/RealTimeTraffic.jsx) | JavaScript JSX | 52 | 39 | 6 | 97 |
| [src/pages/RoadMonitoring.jsx](/src/pages/RoadMonitoring.jsx) | JavaScript JSX | 1 | 5 | 0 | 6 |
| [src/pages/SystemManagement.jsx](/src/pages/SystemManagement.jsx) | JavaScript JSX | 3 | 0 | -7 | -4 |
| [src/pages/VehicleManagement.jsx](/src/pages/VehicleManagement.jsx) | JavaScript JSX | -1 | 1 | 0 | 0 |
| [src/server.js](/src/server.js) | JavaScript | 90 | 19 | -10 | 99 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details