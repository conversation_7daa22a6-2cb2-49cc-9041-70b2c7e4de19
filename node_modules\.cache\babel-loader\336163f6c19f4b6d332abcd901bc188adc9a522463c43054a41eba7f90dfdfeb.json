{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport LeftOutlined from \"@ant-design/icons/es/icons/LeftOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nconst useColumnIcons = (prefixCls, rtl, expandIcon) => {\n  let mergedExpandIcon = expandIcon;\n  if (!expandIcon) {\n    mergedExpandIcon = rtl ? /*#__PURE__*/React.createElement(LeftOutlined, null) : /*#__PURE__*/React.createElement(RightOutlined, null);\n  }\n  const loadingIcon = /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-menu-item-loading-icon`\n  }, /*#__PURE__*/React.createElement(LoadingOutlined, {\n    spin: true\n  }));\n  return React.useMemo(() => [mergedExpandIcon, loadingIcon], [mergedExpandIcon]);\n};\nexport default useColumnIcons;", "map": {"version": 3, "names": ["React", "LeftOutlined", "LoadingOutlined", "RightOutlined", "useColumnIcons", "prefixCls", "rtl", "expandIcon", "mergedExpandIcon", "createElement", "loadingIcon", "className", "spin", "useMemo"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/cascader/hooks/useColumnIcons.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport LeftOutlined from \"@ant-design/icons/es/icons/LeftOutlined\";\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport RightOutlined from \"@ant-design/icons/es/icons/RightOutlined\";\nconst useColumnIcons = (prefixCls, rtl, expandIcon) => {\n  let mergedExpandIcon = expandIcon;\n  if (!expandIcon) {\n    mergedExpandIcon = rtl ? /*#__PURE__*/React.createElement(LeftOutlined, null) : /*#__PURE__*/React.createElement(RightOutlined, null);\n  }\n  const loadingIcon = /*#__PURE__*/React.createElement(\"span\", {\n    className: `${prefixCls}-menu-item-loading-icon`\n  }, /*#__PURE__*/React.createElement(LoadingOutlined, {\n    spin: true\n  }));\n  return React.useMemo(() => [mergedExpandIcon, loadingIcon], [mergedExpandIcon]);\n};\nexport default useColumnIcons;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,yCAAyC;AAClE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,MAAMC,cAAc,GAAGA,CAACC,SAAS,EAAEC,GAAG,EAAEC,UAAU,KAAK;EACrD,IAAIC,gBAAgB,GAAGD,UAAU;EACjC,IAAI,CAACA,UAAU,EAAE;IACfC,gBAAgB,GAAGF,GAAG,GAAG,aAAaN,KAAK,CAACS,aAAa,CAACR,YAAY,EAAE,IAAI,CAAC,GAAG,aAAaD,KAAK,CAACS,aAAa,CAACN,aAAa,EAAE,IAAI,CAAC;EACvI;EACA,MAAMO,WAAW,GAAG,aAAaV,KAAK,CAACS,aAAa,CAAC,MAAM,EAAE;IAC3DE,SAAS,EAAE,GAAGN,SAAS;EACzB,CAAC,EAAE,aAAaL,KAAK,CAACS,aAAa,CAACP,eAAe,EAAE;IACnDU,IAAI,EAAE;EACR,CAAC,CAAC,CAAC;EACH,OAAOZ,KAAK,CAACa,OAAO,CAAC,MAAM,CAACL,gBAAgB,EAAEE,WAAW,CAAC,EAAE,CAACF,gBAAgB,CAAC,CAAC;AACjF,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}