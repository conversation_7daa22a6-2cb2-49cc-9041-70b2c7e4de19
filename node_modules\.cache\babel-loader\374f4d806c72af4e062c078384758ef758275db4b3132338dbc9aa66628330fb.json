{"ast": null, "code": "export var DEFAULT_FONT_SIZE = 12;\nexport var DEFAULT_FONT_FAMILY = 'sans-serif';\nexport var DEFAULT_FONT = DEFAULT_FONT_SIZE + \"px \" + DEFAULT_FONT_FAMILY;\nvar OFFSET = 20;\nvar SCALE = 100;\nvar defaultWidthMapStr = \"007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\\\\\WQb\\\\0FWLg\\\\bWb\\\\WQ\\\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\\\FFF5.5N\";\nfunction getTextWidthMap(mapStr) {\n  var map = {};\n  if (typeof JSON === 'undefined') {\n    return map;\n  }\n  for (var i = 0; i < mapStr.length; i++) {\n    var char = String.fromCharCode(i + 32);\n    var size = (mapStr.charCodeAt(i) - OFFSET) / SCALE;\n    map[char] = size;\n  }\n  return map;\n}\nexport var DEFAULT_TEXT_WIDTH_MAP = getTextWidthMap(defaultWidthMapStr);\nexport var platformApi = {\n  createCanvas: function () {\n    return typeof document !== 'undefined' && document.createElement('canvas');\n  },\n  measureText: function () {\n    var _ctx;\n    var _cachedFont;\n    return function (text, font) {\n      if (!_ctx) {\n        var canvas = platformApi.createCanvas();\n        _ctx = canvas && canvas.getContext('2d');\n      }\n      if (_ctx) {\n        if (_cachedFont !== font) {\n          _cachedFont = _ctx.font = font || DEFAULT_FONT;\n        }\n        return _ctx.measureText(text);\n      } else {\n        text = text || '';\n        font = font || DEFAULT_FONT;\n        var res = /((?:\\d+)?\\.?\\d*)px/.exec(font);\n        var fontSize = res && +res[1] || DEFAULT_FONT_SIZE;\n        var width = 0;\n        if (font.indexOf('mono') >= 0) {\n          width = fontSize * text.length;\n        } else {\n          for (var i = 0; i < text.length; i++) {\n            var preCalcWidth = DEFAULT_TEXT_WIDTH_MAP[text[i]];\n            width += preCalcWidth == null ? fontSize : preCalcWidth * fontSize;\n          }\n        }\n        return {\n          width: width\n        };\n      }\n    };\n  }(),\n  loadImage: function (src, onload, onerror) {\n    var image = new Image();\n    image.onload = onload;\n    image.onerror = onerror;\n    image.src = src;\n    return image;\n  }\n};\nexport function setPlatformAPI(newPlatformApis) {\n  for (var key in platformApi) {\n    if (newPlatformApis[key]) {\n      platformApi[key] = newPlatformApis[key];\n    }\n  }\n}", "map": {"version": 3, "names": ["DEFAULT_FONT_SIZE", "DEFAULT_FONT_FAMILY", "DEFAULT_FONT", "OFFSET", "SCALE", "defaultWidthMapStr", "getTextWidthMap", "mapStr", "map", "JSON", "i", "length", "char", "String", "fromCharCode", "size", "charCodeAt", "DEFAULT_TEXT_WIDTH_MAP", "platformApi", "createCanvas", "document", "createElement", "measureText", "_ctx", "_cachedFont", "text", "font", "canvas", "getContext", "res", "exec", "fontSize", "width", "indexOf", "preCal<PERSON><PERSON><PERSON><PERSON>", "loadImage", "src", "onload", "onerror", "image", "Image", "setPlatformAPI", "newPlatformApis", "key"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/zrender/lib/core/platform.js"], "sourcesContent": ["export var DEFAULT_FONT_SIZE = 12;\nexport var DEFAULT_FONT_FAMILY = 'sans-serif';\nexport var DEFAULT_FONT = DEFAULT_FONT_SIZE + \"px \" + DEFAULT_FONT_FAMILY;\nvar OFFSET = 20;\nvar SCALE = 100;\nvar defaultWidthMapStr = \"007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\\\\\WQb\\\\0FWLg\\\\bWb\\\\WQ\\\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\\\FFF5.5N\";\nfunction getTextWidthMap(mapStr) {\n    var map = {};\n    if (typeof JSON === 'undefined') {\n        return map;\n    }\n    for (var i = 0; i < mapStr.length; i++) {\n        var char = String.fromCharCode(i + 32);\n        var size = (mapStr.charCodeAt(i) - OFFSET) / SCALE;\n        map[char] = size;\n    }\n    return map;\n}\nexport var DEFAULT_TEXT_WIDTH_MAP = getTextWidthMap(defaultWidthMapStr);\nexport var platformApi = {\n    createCanvas: function () {\n        return typeof document !== 'undefined'\n            && document.createElement('canvas');\n    },\n    measureText: (function () {\n        var _ctx;\n        var _cachedFont;\n        return function (text, font) {\n            if (!_ctx) {\n                var canvas = platformApi.createCanvas();\n                _ctx = canvas && canvas.getContext('2d');\n            }\n            if (_ctx) {\n                if (_cachedFont !== font) {\n                    _cachedFont = _ctx.font = font || DEFAULT_FONT;\n                }\n                return _ctx.measureText(text);\n            }\n            else {\n                text = text || '';\n                font = font || DEFAULT_FONT;\n                var res = /((?:\\d+)?\\.?\\d*)px/.exec(font);\n                var fontSize = res && +res[1] || DEFAULT_FONT_SIZE;\n                var width = 0;\n                if (font.indexOf('mono') >= 0) {\n                    width = fontSize * text.length;\n                }\n                else {\n                    for (var i = 0; i < text.length; i++) {\n                        var preCalcWidth = DEFAULT_TEXT_WIDTH_MAP[text[i]];\n                        width += preCalcWidth == null ? fontSize : (preCalcWidth * fontSize);\n                    }\n                }\n                return { width: width };\n            }\n        };\n    })(),\n    loadImage: function (src, onload, onerror) {\n        var image = new Image();\n        image.onload = onload;\n        image.onerror = onerror;\n        image.src = src;\n        return image;\n    }\n};\nexport function setPlatformAPI(newPlatformApis) {\n    for (var key in platformApi) {\n        if (newPlatformApis[key]) {\n            platformApi[key] = newPlatformApis[key];\n        }\n    }\n}\n"], "mappings": "AAAA,OAAO,IAAIA,iBAAiB,GAAG,EAAE;AACjC,OAAO,IAAIC,mBAAmB,GAAG,YAAY;AAC7C,OAAO,IAAIC,YAAY,GAAGF,iBAAiB,GAAG,KAAK,GAAGC,mBAAmB;AACzE,IAAIE,MAAM,GAAG,EAAE;AACf,IAAIC,KAAK,GAAG,GAAG;AACf,IAAIC,kBAAkB,GAAG,wGAAwG;AACjI,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC7B,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZ,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAC7B,OAAOD,GAAG;EACd;EACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAIE,IAAI,GAAGC,MAAM,CAACC,YAAY,CAACJ,CAAC,GAAG,EAAE,CAAC;IACtC,IAAIK,IAAI,GAAG,CAACR,MAAM,CAACS,UAAU,CAACN,CAAC,CAAC,GAAGP,MAAM,IAAIC,KAAK;IAClDI,GAAG,CAACI,IAAI,CAAC,GAAGG,IAAI;EACpB;EACA,OAAOP,GAAG;AACd;AACA,OAAO,IAAIS,sBAAsB,GAAGX,eAAe,CAACD,kBAAkB,CAAC;AACvE,OAAO,IAAIa,WAAW,GAAG;EACrBC,YAAY,EAAE,SAAAA,CAAA,EAAY;IACtB,OAAO,OAAOC,QAAQ,KAAK,WAAW,IAC/BA,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC3C,CAAC;EACDC,WAAW,EAAG,YAAY;IACtB,IAAIC,IAAI;IACR,IAAIC,WAAW;IACf,OAAO,UAAUC,IAAI,EAAEC,IAAI,EAAE;MACzB,IAAI,CAACH,IAAI,EAAE;QACP,IAAII,MAAM,GAAGT,WAAW,CAACC,YAAY,CAAC,CAAC;QACvCI,IAAI,GAAGI,MAAM,IAAIA,MAAM,CAACC,UAAU,CAAC,IAAI,CAAC;MAC5C;MACA,IAAIL,IAAI,EAAE;QACN,IAAIC,WAAW,KAAKE,IAAI,EAAE;UACtBF,WAAW,GAAGD,IAAI,CAACG,IAAI,GAAGA,IAAI,IAAIxB,YAAY;QAClD;QACA,OAAOqB,IAAI,CAACD,WAAW,CAACG,IAAI,CAAC;MACjC,CAAC,MACI;QACDA,IAAI,GAAGA,IAAI,IAAI,EAAE;QACjBC,IAAI,GAAGA,IAAI,IAAIxB,YAAY;QAC3B,IAAI2B,GAAG,GAAG,oBAAoB,CAACC,IAAI,CAACJ,IAAI,CAAC;QACzC,IAAIK,QAAQ,GAAGF,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC,CAAC,IAAI7B,iBAAiB;QAClD,IAAIgC,KAAK,GAAG,CAAC;QACb,IAAIN,IAAI,CAACO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;UAC3BD,KAAK,GAAGD,QAAQ,GAAGN,IAAI,CAACd,MAAM;QAClC,CAAC,MACI;UACD,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,IAAI,CAACd,MAAM,EAAED,CAAC,EAAE,EAAE;YAClC,IAAIwB,YAAY,GAAGjB,sBAAsB,CAACQ,IAAI,CAACf,CAAC,CAAC,CAAC;YAClDsB,KAAK,IAAIE,YAAY,IAAI,IAAI,GAAGH,QAAQ,GAAIG,YAAY,GAAGH,QAAS;UACxE;QACJ;QACA,OAAO;UAAEC,KAAK,EAAEA;QAAM,CAAC;MAC3B;IACJ,CAAC;EACL,CAAC,CAAE,CAAC;EACJG,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAEC,MAAM,EAAEC,OAAO,EAAE;IACvC,IAAIC,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvBD,KAAK,CAACF,MAAM,GAAGA,MAAM;IACrBE,KAAK,CAACD,OAAO,GAAGA,OAAO;IACvBC,KAAK,CAACH,GAAG,GAAGA,GAAG;IACf,OAAOG,KAAK;EAChB;AACJ,CAAC;AACD,OAAO,SAASE,cAAcA,CAACC,eAAe,EAAE;EAC5C,KAAK,IAAIC,GAAG,IAAIzB,WAAW,EAAE;IACzB,IAAIwB,eAAe,CAACC,GAAG,CAAC,EAAE;MACtBzB,WAAW,CAACyB,GAAG,CAAC,GAAGD,eAAe,CAACC,GAAG,CAAC;IAC3C;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}