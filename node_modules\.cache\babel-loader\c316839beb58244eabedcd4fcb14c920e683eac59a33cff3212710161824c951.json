{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport axios from 'axios'; // 新增 axios 导入\nimport VideoPlayer from './VideoPlayer'; // 引入摄像头视频播放组件\nimport DevicePopoverContent from './DevicePopoverContent';\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel = null; // 存储原始模型数据\nlet skeleton = null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n  bsm: 'changli/cloud/v2x/obu/bsm',\n  rsm: 'changli/cloud/v2x/rsu/rsm',\n  scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',\n  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat' // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (!lastPosition) {\n      lastPosition = newPos.clone();\n      return newPos;\n    }\n    const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n    const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n    const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n    lastPosition.set(filteredX, filteredY, filteredZ);\n    return lastPosition.clone();\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  const lastPos = vehicleLastPositions.get(vehicleId);\n\n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n\n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n\n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (lastRotation === null) {\n      lastRotation = newRotation;\n      return newRotation;\n    }\n\n    // 处理角度跳变（从360度到0度或反之）\n    let diff = newRotation - lastRotation;\n    if (diff > Math.PI) diff -= 2 * Math.PI;\n    if (diff < -Math.PI) diff += 2 * Math.PI;\n    const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n    lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const lastRot = vehicleLastRotations.get(vehicleId);\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n\n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\n// 在文件顶部添加\nconst mixersToCleanup = new Set();\nconst bonesMap = new Map();\n\n// 在文件顶部添加资源管理器\nconst resourceManager = {\n  mixers: new Set(),\n  bones: new Map(),\n  actions: new Map(),\n  models: new Set(),\n  addMixer(mixer, model) {\n    this.mixers.add(mixer);\n    if (model) {\n      this.models.add(model);\n      // 记录骨骼\n      model.traverse(object => {\n        if (object.isBone) {\n          this.bones.set(object.uuid, object);\n        }\n      });\n    }\n    return mixer;\n  },\n  addAction(action, mixer) {\n    if (!this.actions.has(mixer)) {\n      this.actions.set(mixer, new Set());\n    }\n    this.actions.get(mixer).add(action);\n    return action;\n  },\n  removeMixer(mixer) {\n    if (this.mixers.has(mixer)) {\n      try {\n        // 停止并清理所有动作\n        if (this.actions.has(mixer)) {\n          this.actions.get(mixer).forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          this.actions.delete(mixer);\n        }\n\n        // 停止混合器\n        if (typeof mixer.stopAllAction === 'function') {\n          mixer.stopAllAction();\n        }\n\n        // 清理混合器的根对象\n        const root = mixer.getRoot();\n        if (root) {\n          this.models.delete(root);\n          root.traverse(object => {\n            if (object && object.isBone) {\n              this.bones.delete(object.uuid);\n            }\n            if (object && object.animations) {\n              object.animations.length = 0;\n            }\n          });\n\n          // 安全地清理缓存\n          try {\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n\n            // 这里是发生错误的地方，添加防御性检查\n            if (typeof mixer.uncacheClip === 'function') {\n              // 不直接调用uncacheClip(null)，而是获取混合器中的所有动画片段并逐个清理\n              const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n              clips.forEach(clip => {\n                if (clip && clip.uuid) {\n                  mixer.uncacheClip(clip);\n                }\n              });\n            }\n          } catch (e) {\n            console.log('在清理动画混合器时发生非致命错误:', e);\n            // 继续执行其余清理操作\n          }\n        }\n        this.mixers.delete(mixer);\n      } catch (error) {\n        console.error('清理动画混合器时发生错误:', error);\n        // 确保即使出错也会从集合中移除\n        this.mixers.delete(mixer);\n      }\n    }\n  },\n  cleanup() {\n    try {\n      // 清理动作\n      this.actions.forEach((actions, mixer) => {\n        try {\n          actions.forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          actions.clear();\n        } catch (e) {\n          console.log('清理动作时发生非致命错误:', e);\n        }\n      });\n      this.actions.clear();\n\n      // 清理混合器\n      this.mixers.forEach(mixer => {\n        try {\n          if (typeof mixer.stopAllAction === 'function') {\n            mixer.stopAllAction();\n          }\n          const root = mixer.getRoot();\n          if (root) {\n            root.traverse(object => {\n              if (object && object.animations) {\n                object.animations.length = 0;\n              }\n            });\n\n            // 安全清理\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n\n            // 安全清理动画片段\n            try {\n              if (mixer._actions && Array.isArray(mixer._actions)) {\n                const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n                clips.forEach(clip => {\n                  if (clip && clip.uuid && typeof mixer.uncacheClip === 'function') {\n                    mixer.uncacheClip(clip);\n                  }\n                });\n              }\n            } catch (e) {\n              console.log('清理动画片段时发生非致命错误:', e);\n            }\n          }\n        } catch (e) {\n          console.log('清理混合器时发生非致命错误:', e);\n        }\n      });\n      this.mixers.clear();\n\n      // 清理骨骼\n      this.bones.forEach(bone => {\n        if (bone.parent) {\n          bone.parent.remove(bone);\n        }\n        if (bone.matrix) bone.matrix.identity();\n        if (bone.matrixWorld) bone.matrixWorld.identity();\n      });\n      this.bones.clear();\n\n      // 清理模型\n      this.models.forEach(model => {\n        if (model.parent) {\n          model.parent.remove(model);\n        }\n        model.traverse(object => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n          }\n          if (object.animations) {\n            object.animations.length = 0;\n          }\n        });\n      });\n      this.models.clear();\n    } catch (error) {\n      console.error('全局清理过程中发生错误:', error);\n      // 即使发生错误也尝试清空集合\n      this.actions.clear();\n      this.mixers.clear();\n      this.bones.clear();\n      this.models.clear();\n    }\n  }\n};\n\n// 修改创建动画混合器的函数\nconst createAnimationMixer = model => {\n  const mixer = new THREE.AnimationMixer(model);\n  return resourceManager.addMixer(mixer, model);\n};\n\n// 修改动画动作创建的函数\nconst createAction = (clip, mixer, model) => {\n  const action = mixer.clipAction(clip, model);\n  return resourceManager.addAction(action, mixer);\n};\n\n// 在CampusModel组件顶部添加消息缓存\nconst processedMessageIds = new Set();\n\n// 添加事件去重相关的全局变量\nlet eventListCache = []; // 事件列表缓存，存储所有事件的完整信息\nlet eventIdCounter = 1; // 事件ID计数器\nlet eventMarkers = new Map(); // 存储事件标记的映射，key为eventId，value为THREE对象\n\nconst CampusModel = ({\n  className,\n  onCurrentRSUChange,\n  selectedRSUs\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n\n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n\n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = new Map(); // 使用全局Map存储所有行人的动画混合器（不再使用useRef）\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n  // 设备数据 state\n  const [devicesData, setDevicesData] = useState({\n    devices: []\n  });\n\n  // 动态加载设备数据\n  const loadDevicesData = async () => {\n    let devicesArray = [];\n    try {\n      // const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const apiUrl = BASE_URL;\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      if (response.data && response.data.success) {\n        devicesArray = response.data.data;\n      }\n      console.log('devicesArrayapiUrl', apiUrl);\n      console.log('devicesArray', devicesArray);\n    } catch (error) {\n      try {\n        const response = await fetch('/src/data/devices.json');\n        const json = await response.json();\n        devicesArray = json.devices || [];\n      } catch (e) {\n        console.error('设备数据加载失败', e);\n      }\n    }\n    setDevicesData({\n      devices: devicesArray\n    });\n  };\n  useEffect(() => {\n    loadDevicesData();\n  }, []);\n\n  // 路口数据 state\n  const [intersections, setIntersections] = useState([]);\n  // 动态加载路口数据\n  useEffect(() => {\n    const fetchIntersections = async () => {\n      try {\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const response = await axios.get(`${apiUrl}/api/intersections`);\n        if (response.data && response.data.success) {\n          setIntersections(response.data.data || []);\n        }\n      } catch (error) {\n        console.error('获取路口信息失败:', error);\n      }\n    };\n    fetchIntersections();\n  }, []);\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,\n    // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null,\n    phases: []\n  });\n\n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n\n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n\n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n\n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '65px',\n    // 从 60px 改为 65px\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '65px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({\n    topic: '',\n    content: ''\n  });\n\n  // 设备信息弹框状态\n  const [devicePopover, setDevicePopover] = useState({\n    visible: false,\n    deviceId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null\n  });\n\n  // 设备弹框关闭函数\n  const handleCloseDevicePopover = () => {\n    setDevicePopover({\n      visible: false,\n      deviceId: null,\n      position: {\n        x: 0,\n        y: 0\n      },\n      content: null\n    });\n  };\n\n  // 设备弹框内容渲染函数\n  const renderDevicePopoverContent = device => {\n    if (!device) return null;\n    return /*#__PURE__*/_jsxDEV(DevicePopoverContent, {\n      device: device\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 12\n    }, this);\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n\n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n\n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n\n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos).to({\n          x: 0,\n          y: 300,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        }).start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp).to({\n          x: 0,\n          y: 1,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        }).start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n\n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget).to({\n          x: 0,\n          y: 0,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        }).start();\n\n        // 启用控制器\n        controls.enabled = true;\n\n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x + 50, 70, -modelCoords.y + 50);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n\n      // 如果该路口有红绿灯，自动显示红绿灯弹窗\n      if (intersection.hasTrafficLight !== false && intersection.interId) {\n        console.log(`路口 ${intersection.name} 有红绿灯，准备显示红绿灯状态`);\n\n        // 延迟300ms调用以确保场景已更新\n        setTimeout(() => {\n          // 检查多种可能的ID格式\n          let interId = intersection.interId;\n\n          // 使用全局的showTrafficLightPopup函数显示红绿灯弹窗\n          if (window.showTrafficLightPopup) {\n            window.showTrafficLightPopup(interId);\n            console.log(`已触发路口 ${intersection.name} (ID: ${interId}) 的红绿灯弹窗显示`);\n          } else {\n            console.error('找不到显示红绿灯弹窗的函数');\n          }\n        }, 300);\n      } else {\n        console.log(`路口 ${intersection.name} 没有红绿灯或缺少ID，不显示红绿灯状态`);\n\n        // 如果有弹窗正在显示，则关闭它\n        if (window._setTrafficLightPopover) {\n          window._setTrafficLightPopover({\n            visible: false\n          });\n        }\n      }\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    var _payload$data3, _payload$data4, _payload$data5, _payload$data6, _payload$data7, _payload$data8, _payload$data9, _payload$data10, _payload$data11, _payload$data12;\n    try {\n      const payload = JSON.parse(message);\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        var _payload$data;\n        // console.log('收到RSM消息:', payload);\n\n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n\n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n          return;\n        }\n\n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n\n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n\n        const participants = ((_payload$data = payload.data) === null || _payload$data === void 0 ? void 0 : _payload$data.participants) || [];\n        const rsuid = payload.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id = deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = type === '3' ? SkeletonUtils.clone(preloadedPeopleModel) : preloadedModel.clone();\n              // 根据类型调整高度和缩放\n              const height = type === '3' ? 2.0 : 1.0;\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n\n              // 如果是行人类型，设置缩放和创建动画\n              if (type === '3') {\n                // newModel.scale.set(0.005, 0.005, 0.005);\n                newModel.scale.set(4, 4, 4);\n\n                // 使用resourceManager创建并管理动画混合器\n                const mixer = createAnimationMixer(newModel);\n                if (peopleBaseModel && peopleBaseModel.animations && peopleBaseModel.animations.length > 0) {\n                  // 只创建一个动作并添加到资源管理器\n                  const action = createAction(peopleBaseModel.animations[0], mixer, newModel);\n                  action.play();\n                }\n\n                // 保存到全局Map中(不再使用useRef)\n                peopleAnimationMixers.set(id, mixer);\n              }\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 1000;\n        const currentIds = new Set(participants.map(p => deviceMac + p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            // 如果是行人，清理动画混合器\n            if (modelData.type === '3' && peopleAnimationMixers.has(id)) {\n              const mixer = peopleAnimationMixers.get(id);\n              // 使用resourceManager清理混合器\n              resourceManager.removeMixer(mixer);\n              peopleAnimationMixers.delete(id);\n            }\n\n            // 从场景移除模型\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n\n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n\n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n\n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n\n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid,\n          // 直接传递车辆ID\n          data: {\n            // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n\n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n\n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n\n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n\n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n\n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n\n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n\n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n\n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n\n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n\n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 10,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          scene.add(newVehicleModel);\n\n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1',\n            // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n\n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position).to({\n            y: 0.5\n          }, 500).easing(TWEEN.Easing.Quadratic.Out).start();\n\n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({\n                opacity: 0.6\n              }).to({\n                opacity: 1.0\n              }, 500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function () {\n                child.material.opacity = this.opacity;\n                child.material.needsUpdate = true;\n              }).start();\n            }\n          });\n\n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({\n            opacity: 0.6\n          }).to({\n            opacity: 1.0\n          }, 500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function () {\n            speedLabel.material.opacity = this.opacity;\n            speedLabel.material.needsUpdate = true;\n          }).start();\n\n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n\n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n\n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n\n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n\n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n\n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 增加到10秒，避免频繁清理造成闪烁\n\n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n\n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1;\n            modelData.model.traverse(child => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n\n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n\n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            modelData.model.traverse(child => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n\n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n\n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n\n        try {\n          const payload = JSON.parse(message);\n\n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n\n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n\n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n\n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n\n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n\n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? getDirectionFromCode(phase.trafficDirec) : getPhaseDirection(phaseId);\n\n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n\n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n\n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n\n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n\n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n\n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n\n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n\n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && (window.currentPopoverIdRef.current === modelKey || window.currentPopoverIdRef.current === String(modelKey) || window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n\n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n        return;\n      }\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件，确保包含mac和timestamp\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data,\n          mac: payload.mac,\n          tm: payload.tm\n        }, '*');\n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        events.forEach(event => {\n          var _payload$data2;\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(parseFloat(rsiData.posLong), parseFloat(rsiData.posLat));\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          switch (eventType) {\n            case '401':\n              // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':\n              // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':\n              // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':\n              // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':\n              // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002':\n              // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':\n              // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor, eventType, {\n            rsuId: ((_payload$data2 = payload.data) === null || _payload$data2 === void 0 ? void 0 : _payload$data2.rsuId) || 'UNKNOWN',\n            eventId: eventId,\n            description: description\n          });\n\n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n\n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch (sceneType) {\n          case '2':\n            // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f', '2', {\n              rsuId: (_payload$data3 = payload.data) === null || _payload$data3 === void 0 ? void 0 : _payload$data3.rsuId,\n              sceneData\n            });\n            break;\n          case '9-5':\n            // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14', '1002', {\n              rsuId: (_payload$data4 = payload.data) === null || _payload$data4 === void 0 ? void 0 : _payload$data4.rsuId,\n              sceneData\n            });\n            break;\n          case '9-6':\n            // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45', '404', {\n              rsuId: (_payload$data5 = payload.data) === null || _payload$data5 === void 0 ? void 0 : _payload$data5.rsuId,\n              sceneData\n            });\n            break;\n          case '10':\n            // 限速提醒\n            const speedLimit = sceneData.eventData1; // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff', '10', {\n              rsuId: (_payload$data6 = payload.data) === null || _payload$data6 === void 0 ? void 0 : _payload$data6.rsuId,\n              sceneData\n            });\n            break;\n          case '12':\n            // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d', '12', {\n              rsuId: (_payload$data7 = payload.data) === null || _payload$data7 === void 0 ? void 0 : _payload$data7.rsuId,\n              sceneData\n            });\n            break;\n          case '13':\n            // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a', '13', {\n              rsuId: (_payload$data8 = payload.data) === null || _payload$data8 === void 0 ? void 0 : _payload$data8.rsuId,\n              sceneData\n            });\n            break;\n          case '21-8':\n            // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1', '21-8', {\n              rsuId: (_payload$data9 = payload.data) === null || _payload$data9 === void 0 ? void 0 : _payload$data9.rsuId,\n              sceneData\n            });\n            break;\n          case '34':\n            // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96', '904', {\n              rsuId: (_payload$data10 = payload.data) === null || _payload$data10 === void 0 ? void 0 : _payload$data10.rsuId,\n              sceneData\n            });\n            break;\n          case '33':\n            // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16', '910', {\n              rsuId: (_payload$data11 = payload.data) === null || _payload$data11 === void 0 ? void 0 : _payload$data11.rsuId,\n              sceneData\n            });\n            break;\n          case '999':\n            // 信号灯优先\n            const priorityType = sceneData.eventData1; // 优先类型\n            const duration = sceneData.eventData2; // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2', '999', {\n              rsuId: (_payload$data12 = payload.data) === null || _payload$data12 === void 0 ? void 0 : _payload$data12.rsuId,\n              sceneData\n            });\n            break;\n        }\n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 检查消息ID是否已处理过\n          if (message.messageId) {\n            if (processedMessageIds.has(message.messageId)) {\n              // console.log('跳过重复消息:', message.messageId);\n              return;\n            }\n\n            // 添加到已处理集合\n            processedMessageIds.add(message.messageId);\n\n            // 限制缓存大小，防止内存泄漏\n            if (processedMessageIds.size > 1000) {\n              // 转换为数组，删除最早的100个元素\n              const idsArray = Array.from(processedMessageIds);\n              for (let i = 0; i < 100; i++) {\n                processedMessageIds.delete(idsArray[i]);\n              }\n            }\n          }\n\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/vehicle.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n\n        // 检查scene是否初始化\n        if (scene) {\n          scene.add(model);\n\n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } else {\n          console.error('无法添加模型：场景未初始化');\n        }\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画 - 只更新存在的混合器\n      const deltaTime = clock.getDelta();\n\n      // 使用Map而不是ref.current\n      if (peopleAnimationMixers.size > 0) {\n        peopleAnimationMixers.forEach(mixer => {\n          mixer.update(deltaTime);\n        });\n      }\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 200, -50 * Math.sin(adjustedRotation));\n\n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n\n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n\n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n\n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 路口视角模式\n        controls.update();\n      }\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加事件清理定时器\n    const eventCleanupInterval = setInterval(() => {\n      removeInactiveEvents();\n    }, 30000); // 每30秒清理一次非活跃事件\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // // 添加全局测试函数用于验证事件去重功能\n    // window.test3DEventDeduplication = () => {\n    //   console.log('🧪 开始3D场景事件去重测试');\n\n    //   // 测试位置\n    //   const testPosition = { x: 100, y: 100 };\n\n    //   // 测试1：创建新事件\n    //   console.log('测试1：创建新的违停车辆事件');\n    //   showWarningMarker(testPosition, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });\n\n    //   // 测试2：相同位置的重复事件（应该被去重）\n    //   setTimeout(() => {\n    //     console.log('测试2：相同位置的重复事件（应该被去重）');\n    //     showWarningMarker(testPosition, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });\n    //   }, 1000);\n\n    //   // 测试3：稍微不同位置的事件（距离在阈值内，应该被去重）\n    //   setTimeout(() => {\n    //     console.log('测试3：稍微不同位置的事件（应该被去重）');\n    //     showWarningMarker({ x: 105, y: 105 }, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });\n    //   }, 2000);\n\n    //   // 测试4：不同类型的事件（应该创建新事件）\n    //   setTimeout(() => {\n    //     console.log('测试4：不同类型的事件（应该创建新事件）');\n    //     showWarningMarker(testPosition, '逆行车辆', '#eb2f96', '904', { rsuId: 'TEST_RSU' });\n    //   }, 3000);\n\n    //   // 测试5：查看缓存状态\n    //   setTimeout(() => {\n    //     console.log('📊 3D场景事件缓存状态:', {\n    //       缓存事件数: eventListCache.length,\n    //       事件ID计数器: eventIdCounter,\n    //       场景标记数: eventMarkers.size,\n    //       事件列表: eventListCache.map(e => ({\n    //         ID: e.eventId,\n    //         类型: e.eventType,\n    //         更新次数: e.updateCount,\n    //         位置: `${e.position.lat.toFixed(6)}, ${e.position.lng.toFixed(6)}`\n    //       }))\n    //     });\n    //   }, 4000);\n    // };\n\n    // 添加手动清理3D事件的全局函数\n    window.cleanup3DEvents = () => {\n      console.log('🧹 手动清理3D场景事件');\n      removeInactiveEvents();\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 停止渲染循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理事件清理定时器\n      if (eventCleanupInterval) {\n        clearInterval(eventCleanupInterval);\n      }\n\n      // 3. 清理所有事件标记\n      eventMarkers.forEach(marker => {\n        if (scene && marker) {\n          scene.remove(marker);\n        }\n      });\n      eventMarkers.clear();\n      eventListCache.length = 0;\n\n      // 4. 停止所有 TWEEN 动画\n      TWEEN.removeAll();\n\n      // 3. 清理所有动画混合器\n      peopleAnimationMixers.forEach(mixer => {\n        resourceManager.removeMixer(mixer);\n      });\n      peopleAnimationMixers.clear();\n\n      // 4. 清理所有其他资源\n      resourceManager.cleanup();\n\n      // 5. 清理场景中的所有对象\n      if (scene) {\n        const objectsToRemove = [];\n        scene.traverse(object => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n            if (object !== scene) {\n              objectsToRemove.push(object);\n            }\n          }\n        });\n\n        // 从场景中移除对象\n        objectsToRemove.forEach(obj => {\n          if (obj.parent) {\n            obj.parent.remove(obj);\n          }\n        });\n        scene.clear();\n      }\n\n      // 6. 清理渲染器\n      if (renderer) {\n        renderer.setAnimationLoop(null);\n        if (containerRef.current && renderer.domElement && renderer.domElement.parentNode === containerRef.current) {\n          containerRef.current.removeChild(renderer.domElement);\n        }\n        renderer.dispose();\n        renderer.forceContextLoss();\n      }\n\n      // 7. 清理其他资源\n      if (controls) {\n        controls.dispose();\n      }\n\n      // 8. 清理数据结构\n      vehicleLastPositions.clear();\n      vehicleLastRotations.clear();\n      deviceTimestamps.clear();\n      vehicleModels.clear();\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n\n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n\n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n\n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n\n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current && intersections && intersections.length > 0) {\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current && intersections && intersections.length > 0) {\n          // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current, intersections);\n        }\n      }, 2000);\n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景、坐标转换器或路口数据未准备好，暂不创建红绿灯');\n    }\n  }, [scene, intersections]);\n\n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = event => {\n        if (scene && cameraRef.current) {\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current);\n        }\n      };\n\n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n\n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n\n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({\n      color: 0x333333\n    });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n\n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({\n      color: 0x666666\n    });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n\n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          //\n          transparent: false,\n          opacity: 0.1,\n          // 几乎透明\n          depthWrite: false\n        });\n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0); // 放在红绿灯位置\n\n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n\n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500); // 延迟略长于debugTrafficLights\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n\n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n\n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersections && intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        // 查找第一个带有红绿灯的路口\n        const firstTrafficLightIntersection = intersections.find(intersection => intersection.hasTrafficLight !== false && intersection.interId);\n\n        // 如果找到带红绿灯的路口，优先选择它；否则选择第一个路口\n        const targetIntersection = firstTrafficLightIntersection || intersections[0];\n        console.log('自动选择路口:', targetIntersection.name, '具有红绿灯:', firstTrafficLightIntersection ? '是' : '否');\n\n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(targetIntersection.name);\n        }, 2000);\n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersections, selectedIntersection]);\n\n  // 新增：在场景初始化后渲染所有路口entrance的设备图标\n  const renderEntranceDeviceIcons = converterInstance => {\n    if (!scene || typeof scene.traverse !== 'function' || !converterInstance) {\n      console.warn('设备图标渲染条件不满足:', {\n        scene: !!scene,\n        sceneTraverse: scene && typeof scene.traverse === 'function',\n        converter: !!converterInstance\n      });\n      return;\n    }\n    if (!devicesData.devices || devicesData.devices.length === 0) {\n      console.warn('设备数据未加载，跳过设备图标渲染');\n      return; // 设备数据未加载时不渲染\n    }\n    if (!intersections || intersections.length === 0) {\n      console.warn('路口数据未加载，跳过设备图标渲染');\n      return;\n    }\n    console.log('开始渲染设备图标:', {\n      设备总数: devicesData.devices.length,\n      路口总数: intersections.length\n    });\n    try {\n      // 清理之前的设备图标\n      const existingIcons = scene.children.filter(obj => obj.userData && obj.userData.isEntranceDeviceIcons);\n      existingIcons.forEach(obj => scene.remove(obj));\n      console.log('清理了', existingIcons.length, '个旧的设备图标');\n      let totalRenderedDevices = 0;\n      intersections.forEach(intersection => {\n        if (!intersection.entrances || !Array.isArray(intersection.entrances)) return;\n        intersection.entrances.forEach(entrance => {\n          if (!entrance.longitude || !entrance.latitude || isNaN(parseFloat(entrance.longitude)) || isNaN(parseFloat(entrance.latitude))) {\n            return;\n          }\n          const devices = devicesData.devices.filter(d => d.location === intersection.name && d.entrance === entrance.name);\n          if (devices.length === 0) return;\n\n          // 经纬度转模型坐标\n          console.log('渲染路口', intersection.name, '入口', entrance.name, '设备数量', devices.length);\n          const modelPos = converterInstance.wgs84ToModel(parseFloat(entrance.longitude), parseFloat(entrance.latitude));\n          // 创建一个组用于存放所有图标\n          const group = new THREE.Group();\n          group.position.set(modelPos.x, 10, -modelPos.y); // 上方10米\n          group.userData = {\n            isEntranceDeviceIcons: true\n          };\n          // 图标排成一排，居中\n          const iconSize = 32; // px\n          const iconSpacing = 8; // px\n          const totalWidth = devices.length * iconSize + (devices.length - 1) * iconSpacing;\n          // 以三维单位为准，假设1单位=1米，24px约等于0.6米\n          const size3D = 4.0; // 图标尺寸加大\n          const spacing3D = 0.5; // 图标间距加大\n          const startX = -((devices.length - 1) * (size3D + spacing3D)) / 2;\n          devices.forEach((device, idx) => {\n            // 创建一个平面用于显示SVG图标\n            const textureLoader = new THREE.TextureLoader();\n            const iconPath = `${BASE_URL}/images/${device.type}.svg`;\n            // 图标材质\n            const iconMaterial = new THREE.MeshBasicMaterial({\n              map: textureLoader.load(iconPath),\n              transparent: true,\n              opacity: 1 // 图标完全不透明\n            });\n            // 图标背景板材质\n            const bgMaterial = new THREE.MeshBasicMaterial({\n              color: 0x000000,\n              transparent: true,\n              opacity: 0.7 // 半透明黑色\n            });\n            // 背景板尺寸略大于图标\n            const bgWidth = size3D * 1.25;\n            const bgHeight = size3D * 1.25;\n            // 背景板几何体（圆角矩形）\n            // 由于Three.js没有内置圆角矩形，使用PlaneGeometry近似\n            const bgGeometry = new THREE.PlaneGeometry(bgWidth, bgHeight);\n            const bgMesh = new THREE.Mesh(bgGeometry, bgMaterial);\n            // 图标几何体\n            const iconGeometry = new THREE.PlaneGeometry(size3D, size3D);\n            const iconMesh = new THREE.Mesh(iconGeometry, iconMaterial);\n            // 图标略微前移，避免Z轴重叠闪烁\n            iconMesh.position.set(0, 0, 0.01);\n            // 创建一个组，包含背景和图标\n            const iconGroup = new THREE.Group();\n            iconGroup.add(bgMesh);\n            iconGroup.add(iconMesh);\n            // 整体平移到正确位置\n            iconGroup.position.set(startX + idx * (size3D + spacing3D), 0, 0);\n            // 不再固定旋转角度，后续在动画循环中让其始终面向相机\n            iconGroup.renderOrder = 999; // 提高渲染优先级\n            iconGroup.userData = {\n              deviceId: device.id,\n              deviceType: device.type,\n              entrance: entrance.name,\n              isEntranceDeviceIcon: true\n            };\n            group.add(iconGroup);\n          });\n          // 新增：添加白色半透明光柱，指向设备图标组\n          // 光柱高度等于图标组的y坐标（即10），底部在地面y=0，顶部在图标组中心\n          const pillarHeight = group.position.y; // 10\n          const pillarGeometry = new THREE.CylinderGeometry(0.2, 0.2, pillarHeight, 16);\n          const pillarMaterial = new THREE.MeshBasicMaterial({\n            color: 0xffffff,\n            transparent: true,\n            opacity: 0.7\n          });\n          const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial);\n          // 设置光柱中心在y=0~y=10之间，底部正好在地面\n          pillar.position.set(0, -pillarHeight / 2, 0);\n          // pillar.position.set(0, -pillarHeight, 0);\n          // 可选：添加标记，便于后续查找或清理\n          pillar.userData = {\n            isEntranceDevicePillar: true\n          };\n          group.add(pillar);\n          scene.add(group);\n        });\n      });\n    } catch (e) {\n      console.error('renderEntranceDeviceIcons error', e);\n      return;\n    }\n  };\n\n  // 在场景初始化后调用渲染设备图标\n  useEffect(() => {\n    if (scene && typeof scene.traverse === 'function' && converter.current && devicesData.devices) {\n      var _devicesData$devices;\n      console.log('触发设备图标渲染:', {\n        scene: !!scene,\n        converter: !!converter.current,\n        devicesCount: ((_devicesData$devices = devicesData.devices) === null || _devicesData$devices === void 0 ? void 0 : _devicesData$devices.length) || 0,\n        intersectionsCount: (intersections === null || intersections === void 0 ? void 0 : intersections.length) || 0\n      });\n\n      // 延迟渲染，确保所有资源都已准备好\n      const timer = setTimeout(() => {\n        renderEntranceDeviceIcons(converter.current);\n      }, 500); // 延迟500ms\n\n      return () => clearTimeout(timer);\n    }\n  }, [scene, converter.current, devicesData.devices, intersections]); // 添加intersections依赖\n\n  // 添加额外的重试机制，确保设备图标能够正确渲染\n  useEffect(() => {\n    if (scene && converter.current && devicesData.devices && intersections) {\n      // 延迟3秒后检查是否有设备图标，如果没有则重新渲染\n      const retryTimer = setTimeout(() => {\n        const existingIcons = scene.children.filter(obj => obj.userData && obj.userData.isEntranceDeviceIcons);\n        if (existingIcons.length === 0) {\n          console.log('检测到设备图标缺失，执行重试渲染');\n          renderEntranceDeviceIcons(converter.current);\n        } else {\n          console.log('设备图标渲染正常，共', existingIcons.length, '个图标组');\n        }\n      }, 3000); // 3秒后检查\n\n      return () => clearTimeout(retryTimer);\n    }\n  }, [scene, converter.current, devicesData.devices, intersections]);\n\n  // 新增：每帧让所有设备图标组和事件图标组始终面向相机（billboard效果）\n  useEffect(() => {\n    if (!scene || !cameraRef.current) return;\n    const animateBillboard = () => {\n      // 遍历所有图标组，让其正对相机\n      scene.children.forEach(obj => {\n        // 设备图标组\n        if (obj.userData && obj.userData.isEntranceDeviceIcons) {\n          obj.lookAt(cameraRef.current.position.x, obj.position.y, cameraRef.current.position.z);\n        }\n        // 事件图标组\n        if (obj.userData && obj.userData.type === 'eventMarker') {\n          obj.lookAt(cameraRef.current.position.x, obj.position.y, cameraRef.current.position.z);\n        }\n      });\n      requestAnimationFrame(animateBillboard);\n    };\n    animateBillboard();\n  }, [scene]);\n\n  // 修改点击处理函数\n  const handleMouseClick = (event, container, sceneInstance, cameraInstance) => {\n    if (!container || !sceneInstance || !cameraInstance) return;\n    const rect = container.getBoundingClientRect();\n    const mouseX = (event.clientX - rect.left) / container.clientWidth * 2 - 1;\n    const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 1;\n    raycaster.params.Line.threshold = 1;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraInstance);\n    const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n    if (intersects.length > 0) {\n      for (let i = 0; i < intersects.length; i++) {\n        const obj = intersects[i].object;\n        if (obj.parent && obj.parent.userData && obj.parent.userData.isEntranceDeviceIcon) {\n          const deviceId = obj.parent.userData.deviceId;\n          console.log('devicesDatahandleMouseClick', devicesData);\n          const device = devicesData.devices.find(d => d.id === deviceId);\n          if (device) {\n            const x = event.clientX;\n            const y = event.clientY;\n            setDevicePopover({\n              visible: true,\n              deviceId,\n              position: {\n                x,\n                y\n              },\n              content: renderDevicePopoverContent(device)\n            });\n            return; // 命中设备图标后直接返回\n          }\n        }\n      }\n    }\n    // ...原有红绿灯弹框逻辑保持不变...\n    // ... existing code ...\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u533A\\u57DF\\u9009\\u62E9\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2575,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u533A\\u57DF\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      onSelect: handleIntersectionChange // 添加onSelect事件，确保每次选择都触发\n      ,\n      options: intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      },\n      value: selectedIntersection ? selectedIntersection.name : undefined\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2576,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2593,\n      columnNumber: 7\n    }, this), trafficLightPopover.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${trafficLightPopover.position.x}px`,\n        top: `${trafficLightPopover.position.y}px`,\n        transform: 'translate(-50%, -100%)',\n        zIndex: 1003,\n        backgroundColor: 'rgba(0, 0, 0, 0.85)',\n        color: 'white',\n        borderRadius: '4px',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n        padding: '0',\n        maxWidth: '240px',\n        // 缩小最大宽度\n        fontSize: '12px' // 缩小字体\n      },\n      children: [trafficLightPopover.content, /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          position: 'absolute',\n          top: '0px',\n          right: '0px',\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          fontSize: '12px',\n          cursor: 'pointer',\n          padding: '2px 6px'\n        },\n        onClick: () => handleClosePopover(setTrafficLightPopover),\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2614,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2597,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2634,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2644,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2633,\n      columnNumber: 7\n    }, this), devicePopover.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${devicePopover.position.x}px`,\n        top: `${devicePopover.position.y}px`,\n        transform: 'translate(-50%, -100%)',\n        zIndex: 1100,\n        backgroundColor: 'rgba(0, 0, 0, 0.92)',\n        color: 'white',\n        borderRadius: '6px',\n        boxShadow: '0 2px 12px rgba(0,0,0,0.35)',\n        padding: 0,\n        minWidth: 320,\n        maxWidth: 350,\n        fontSize: 13\n      },\n      children: [devicePopover.content, /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          position: 'absolute',\n          top: '0px',\n          right: '0px',\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          fontSize: '16px',\n          cursor: 'pointer',\n          padding: '2px 10px',\n          zIndex: 1200\n        },\n        onClick: handleCloseDevicePopover,\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2674,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2656,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"8TChJ+ah82ovhlsEEemV1YGmvsc=\");\n_c = CampusModel;\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 12,\n    // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    backgroundColor: parameters.backgroundColor || {\n      r: 255,\n      g: 255,\n      b: 255,\n      a: 0.8\n    },\n    textColor: parameters.textColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n\n  // 设置字体\n  // context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n\n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n\n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 4 * params.padding + 4 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  canvas.width = width;\n  canvas.height = height;\n\n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n\n  // // 只有在有边框或背景时才绘制背景和边框\n  // if (params.borderThickness > 0 || params.backgroundColor.a > 0) {\n  //   // 绘制背景和边框（圆角矩形）\n  //   const radius = 8;\n  //   context.beginPath();\n  //   context.moveTo(params.borderThickness + radius, params.borderThickness);\n  //   context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  //   context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  //   context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  //   context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  //   context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  //   context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  //   context.lineTo(params.borderThickness, params.borderThickness + radius);\n  //   context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  //   context.closePath();\n\n  //   // 设置背景填充（如果有背景）\n  //   if (params.backgroundColor.a > 0) {\n  //     context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  //     context.fill();\n  //   }\n\n  //   // 设置边框颜色（如果有边框）\n  //   if (params.borderThickness > 0) {\n  //     context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  //     context.lineWidth = params.borderThickness;\n  //     context.stroke();\n  //   }\n  // }\n\n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n\n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n\n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n\n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  // sprite.scale.set(10, 5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n\n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = {\n    text: text,\n    params: params\n  };\n  return sprite;\n}\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    try {\n      const [trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`), loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`)]);\n\n      // 处理机动车模型\n      console.log('加载车辆模型...');\n      preloadedVehicleModel = vehicleGltf.scene;\n      preloadedVehicleModel.traverse(child => {\n        if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n            color: 0xff0000,\n            // 0xff0000, //红色 0xffffff,白色\n            metalness: 0.2,\n            roughness: 0.1,\n            envMapIntensity: 1.0\n          });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n        }\n      });\n      console.log('加载非机动车模型...');\n      // 处理非机动车模型\n      preloadedCyclistModel = cyclistGltf.scene;\n      // 设置非机动车模型的缩放\n      preloadedCyclistModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedCyclistModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n      console.log('加载行人模型...');\n      // 处理行人模型\n      preloadedPeopleModel = peopleGltf.scene;\n      // 设置行人模型的缩放\n      // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n      // 保持原始材质\n      preloadedPeopleModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n        if (child.isMesh) {\n          child.castShadow = true;\n        }\n      });\n\n      // 保存行人动画数据\n      console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n      if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n        console.log('找到行人动画:', peopleGltf.animations.length, '个');\n        peopleBaseModel = peopleGltf;\n      } else {\n        console.warn('行人模型没有包含动画数据');\n      }\n      console.log('加载红绿灯模型...');\n\n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      console.log('红绿灯模型：', preloadedTrafficLightModel);\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n\n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n\n        // // 尝试单独加载红绿灯模型\n        // if (!preloadedTrafficLightModel) {\n        //   console.log('正在单独加载红绿灯模型...');\n        //   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n        //   preloadedTrafficLightModel = trafficLightGltf.scene;\n        //   preloadedTrafficLightModel.scale.set(3, 3, 3);\n        //   console.log('红绿灯模型加载成功');\n        // }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = type => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\n// 添加计算两点之间距离的函数\nconst calculateDistance = (x1, y1, x2, y2) => {\n  const c = Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2));\n  return c;\n};\n\n// 获取事件类型的阈值配置\nconst getEventThresholds = eventType => {\n  switch (eventType) {\n    case '910':\n      // 违停车辆\n      return {\n        timeThreshold: 300000,\n        distanceThreshold: 10\n      };\n    // 5分钟, 20米\n    case '904':\n      // 逆行车辆\n      return {\n        timeThreshold: 10000,\n        distanceThreshold: 20\n      };\n    // 10秒, 20米\n    case '901':\n      // 车辆超速\n      return {\n        timeThreshold: 30000,\n        distanceThreshold: 20\n      };\n    // 30秒, 50米\n    case '401': // 道路抛洒物\n    case '404': // 道路障碍物\n    case '1002':\n      // 道路施工\n      return {\n        timeThreshold: 600000,\n        distanceThreshold: 10\n      };\n    // 10分钟, 30米\n    case '405':\n      // 行人通过马路\n      return {\n        timeThreshold: 10000,\n        distanceThreshold: 10\n      };\n    // 10秒, 10米\n    default:\n      return {\n        timeThreshold: 5000,\n        distanceThreshold: 5\n      };\n    // 5秒, 5米\n  }\n};\n\n// 优化后的事件重复检查逻辑\nconst checkDuplicateEvent = (eventType, eventKey, currentTime, currentPos) => {\n  const {\n    timeThreshold,\n    distanceThreshold\n  } = getEventThresholds(eventType);\n\n  // 遍历事件列表缓存中的所有事件\n  for (let i = 0; i < eventListCache.length; i++) {\n    const cachedEvent = eventListCache[i];\n\n    // 检查事件类型是否相同\n    if (cachedEvent.eventType !== eventType) {\n      continue;\n    }\n\n    // 计算时间差\n    const timeDiff = currentTime - cachedEvent.lastUpdateTime;\n\n    // 检查时间差是否在阈值内\n    if (timeDiff > timeThreshold) {\n      continue;\n    }\n\n    // 计算距离\n    const distance = calculateDistance(currentPos.x, currentPos.y, cachedEvent.position.x, cachedEvent.position.y);\n\n    // 检查距离是否在阈值内\n    if (distance <= distanceThreshold) {\n      // 找到匹配的事件，更新信息\n      cachedEvent.eventKey = eventKey;\n      cachedEvent.lastUpdateTime = currentTime;\n      cachedEvent.position = {\n        ...currentPos\n      };\n      cachedEvent.updateCount = (cachedEvent.updateCount || 1) + 1;\n      console.log(`🔄 3D场景检测到重复事件 ${eventType} (ID: ${cachedEvent.eventId})，时间差: ${(timeDiff / 1000).toFixed(1)}s，距离: ${distance.toFixed(1)}m，更新次数: ${cachedEvent.updateCount}`);\n      return {\n        isDuplicate: true,\n        eventId: cachedEvent.eventId,\n        matchedEvent: cachedEvent\n      };\n    }\n  }\n\n  // 没有找到匹配的事件，创建新事件\n  const newEventId = `3D_EVT_${eventIdCounter.toString().padStart(6, '0')}`;\n  eventIdCounter++;\n  const newEvent = {\n    eventId: newEventId,\n    eventType: eventType,\n    eventKey: eventKey,\n    firstDetectedTime: currentTime,\n    lastUpdateTime: currentTime,\n    position: {\n      ...currentPos\n    },\n    updateCount: 1\n  };\n\n  // 添加到事件列表缓存\n  eventListCache.push(newEvent);\n  return {\n    isDuplicate: false,\n    eventId: newEventId,\n    newEvent: newEvent\n  };\n};\n\n// 删除2s内没有更新的事件\nconst removeInactiveEvents = () => {\n  const currentTime = Date.now();\n  const inactiveThreshold = 2000; // 2s\n\n  const initialCount = eventListCache.length;\n  const removedEvents = [];\n  eventListCache = eventListCache.filter(event => {\n    const timeSinceLastUpdate = currentTime - event.lastUpdateTime;\n    if (timeSinceLastUpdate > inactiveThreshold) {\n      removedEvents.push({\n        id: event.eventId,\n        type: event.eventType,\n        inactiveTime: (timeSinceLastUpdate / 1000).toFixed(1)\n      });\n\n      // 从场景中移除对应的标记\n      const marker = eventMarkers.get(event.eventId);\n      if (marker && scene) {\n        scene.remove(marker);\n        eventMarkers.delete(event.eventId);\n      }\n      return false; // 删除该事件\n    }\n    return true; // 保留该事件\n  });\n  const removedCount = initialCount - eventListCache.length;\n  if (removedCount > 0) {\n    console.log(`🗑️ 3D场景删除了 ${removedCount} 个1s内未更新的事件:`);\n    removedEvents.forEach(event => {\n      console.log(`   - 事件 ${event.id} (类型: ${event.type})，未更新时间: ${event.inactiveTime}秒`);\n    });\n    console.log(`📊 3D场景当前缓存事件数: ${eventListCache.length}`);\n  }\n};\n\n// 根据事件类型获取背景颜色\nconst getEventBackgroundColor = eventType => {\n  switch (eventType) {\n    case '910':\n      // 违停车辆\n      return 0xff4d4f;\n    // 红色背景 - 严重违规\n    case '904':\n      // 逆行车辆\n      return 0xf5222d;\n    // 深红色背景 - 危险行为\n    case '901':\n      // 车辆超速\n      return 0xfa8c16;\n    // 橙色背景 - 警告\n    case '401':\n      // 道路抛洒物\n      return 0xfaad14;\n    // 黄色背景 - 注意\n    case '404':\n      // 道路障碍物\n      return 0xff7a45;\n    // 橙红色背景 - 阻碍\n    case '1002':\n      // 道路施工\n      return 0x1890ff;\n    // 蓝色背景 - 信息\n    case '405':\n      // 行人通过马路\n      return 0x52c41a;\n    // 绿色背景 - 正常\n    case '2':\n      // 交叉路口碰撞预警\n      return 0xff0000;\n    // 纯红色背景 - 紧急\n    case '12':\n      // 交通参与者碰撞预警\n      return 0xeb2f96;\n    // 粉红色背景 - 预警\n    case '13':\n      // 绿波车速引导\n      return 0x13c2c2;\n    // 青色背景 - 引导\n    case '999':\n      // 信号灯优先\n      return 0x722ed1;\n    // 紫色背景 - 优先\n    default:\n      return 0xffa500;\n    // 默认橙黄色背景\n  }\n};\n\n// 获取事件类型的中文名称\nconst getEventTypeName = eventType => {\n  switch (eventType) {\n    case '401':\n      return '抛洒物';\n    case '404':\n      return '障碍物';\n    case '405':\n      return '行人';\n    case '904':\n      return '逆行';\n    case '910':\n      return '违停';\n    case '1002':\n      return '施工';\n    case '901':\n      return '超速';\n    case '2':\n      return '碰撞';\n    case '12':\n      return '碰撞';\n    case '13':\n      return '绿波';\n    case '999':\n      return '优先';\n    default:\n      return `事件${eventType}`;\n  }\n};\n\n// 创建事件图标标记\nconst createEventMarker = (eventType, position, eventId) => {\n  if (!scene) {\n    console.warn('无法创建事件标记：场景不存在或已卸载');\n    return null;\n  }\n  try {\n    // 创建一个组来包含背景和图标\n    const markerGroup = new THREE.Group();\n\n    // 1. 创建统一的背景平面（包含图标和文字区域，类似截图中的设计）\n    const backgroundGeometry = new THREE.PlaneGeometry(6, 8); // 适当增加宽度和高度以容纳图标和文字\n    const backgroundColor = getEventBackgroundColor(eventType); // 根据事件类型获取背景色\n    const backgroundMaterial = new THREE.MeshBasicMaterial({\n      color: backgroundColor,\n      // 使用事件类型对应的背景颜色\n      transparent: true,\n      opacity: 0.9,\n      // 提高不透明度，使颜色更加鲜明\n      side: THREE.DoubleSide\n    });\n    const backgroundMesh = new THREE.Mesh(backgroundGeometry, backgroundMaterial);\n    backgroundMesh.position.set(0, -1, -0.01); // 向下偏移以居中整个标记，稍微向后作为背景\n    backgroundMesh.renderOrder = 998; // 背景渲染优先级\n    markerGroup.add(backgroundMesh);\n\n    // 2. 创建图标平面（位于背景上方区域）\n    const textureLoader = new THREE.TextureLoader();\n    const iconPath = `${BASE_URL}/images/${eventType}.svg`; // 事件图标路径\n\n    const iconMaterial = new THREE.MeshBasicMaterial({\n      map: textureLoader.load(iconPath,\n      // 加载成功回调\n      texture => {\n        console.log(`事件图标加载成功: 事件${eventType}.svg`);\n      },\n      // 加载进度回调\n      undefined,\n      // 加载失败回调\n      error => {\n        console.warn(`事件图标加载失败: 事件${eventType}.svg，使用默认图标`);\n        // 可以在这里设置默认图标\n      }),\n      transparent: true,\n      opacity: 1,\n      // 完全不透明\n      side: THREE.DoubleSide\n    });\n\n    // 创建图标几何体（参考截图中的比例）\n    const iconGeometry = new THREE.PlaneGeometry(5, 5); // 宽度5，高度4，类似截图中的车辆图标比例\n    const iconMesh = new THREE.Mesh(iconGeometry, iconMaterial);\n    iconMesh.position.set(0, 1.5, 0.01); // 图标位于背景上半部分\n    iconMesh.renderOrder = 999; // 图标渲染优先级\n    markerGroup.add(iconMesh);\n\n    // 3. 创建文字标签（显示在图标正下方，参考截图布局）\n    const eventTypeName = getEventTypeName(eventType);\n    const textLabel = createTextSprite(eventTypeName, {\n      backgroundColor: {\n        r: 0,\n        g: 0,\n        b: 0,\n        a: 0.0\n      },\n      // 完全透明背景，与图标共用背景\n      textColor: {\n        r: 255,\n        g: 255,\n        b: 255,\n        a: 1.0\n      },\n      // 白色文字\n      fontSize: 14,\n      // 增大字体以提高可读性\n      padding: 0,\n      // 无填充\n      borderThickness: 0,\n      // 无边框\n      fontWeight: 'bold' // 加粗字体，类似截图效果\n    });\n    textLabel.position.set(0, -2.8, 0.02); // 位于图标正下方，紧贴背景下半部分\n    textLabel.renderOrder = 1000; // 确保在最上层渲染\n    textLabel.scale.set(6, 3, 1); // 调整文字标签的缩放以适应背景\n    markerGroup.add(textLabel);\n\n    // 设置组的位置，高度为15米\n    markerGroup.position.set(position.x, 15, -position.y);\n\n    // 让整个组始终面向相机\n    // markerGroup.lookAt(0, 15, 0);\n    // 创建时直接朝向相机，避免角度跳跃\n    // if (cameraRef.current) {\n    //   markerGroup.lookAt(cameraRef.current.position.x, markerGroup.position.y, cameraRef.current.position.z);\n    // } else {\n    //   markerGroup.lookAt(0, markerGroup.position.y, 0);\n    // }\n    // 设置渲染优先级，与设备图标保持一致\n    markerGroup.renderOrder = 999;\n\n    // 添加用户数据到组\n    markerGroup.userData = {\n      type: 'eventMarker',\n      eventId: eventId,\n      eventType: eventType\n    };\n\n    // 添加到场景\n    scene.add(markerGroup);\n\n    // 存储标记引用\n    eventMarkers.set(eventId, markerGroup);\n    console.log(`📍 创建事件标记 ${eventType} (${eventTypeName}) (ID: ${eventId})，位置: (${position.x.toFixed(2)}, ${position.y.toFixed(2)})，背景色: #${backgroundColor.toString(16)}`);\n    return markerGroup;\n  } catch (error) {\n    console.error('创建事件标记时出错:', error);\n    return null;\n  }\n};\n\n// 更新事件标记位置\nconst updateEventMarkerPosition = (eventId, newPosition) => {\n  const markerGroup = eventMarkers.get(eventId);\n  if (markerGroup && scene) {\n    markerGroup.position.set(newPosition.x, 15, -newPosition.y);\n    // 重新设置朝向相机\n    // markerGroup.lookAt(0, 15, 0);\n    console.log(`📍 更新事件标记位置 (ID: ${eventId})，新位置: (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)})`);\n  }\n};\nconst showWarningMarker = (position, text, color, eventType = '999', eventData = {}) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  try {\n    // 生成事件Key用于去重\n    const currentTime = Date.now();\n    const eventKey = `${eventData.rsuId || 'UNKNOWN'}_${eventType}_${position.x.toFixed(0)}_${position.y.toFixed(0)}`;\n\n    // 转换模型坐标为经纬度（用于距离计算）\n    // const converter = new CoordinateConverter();\n    // const wgs84Pos = converter.modelToWgs84(position.x, position.y);\n\n    // 检查事件去重\n    const duplicateResult = checkDuplicateEvent(eventType, eventKey, currentTime, position);\n    const isDuplicate = duplicateResult.isDuplicate;\n    console.log(`🔍 3D场景事件去重检查 - 类型: ${eventType}, EventKey: ${eventKey}, 位置: (${position.x}, ${position.y}), 结果: ${isDuplicate ? '重复' : '新事件'}`);\n    if (isDuplicate) {\n      // 重复事件，更新现有标记位置\n      updateEventMarkerPosition(duplicateResult.eventId, position);\n    } else {\n      // 新事件，创建新的标记\n      createEventMarker(eventType, position, duplicateResult.eventId);\n    }\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance, intersections) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n\n  // 检查红绿灯模型是否已加载\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载，尝试重新加载...');\n    // 尝试重新加载红绿灯模型\n    const loader = new GLTFLoader();\n    loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`).then(trafficLightGltf => {\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('红绿灯模型重新加载成功，开始创建红绿灯...');\n      // 重新调用创建函数\n      createTrafficLights(converterInstance, intersections);\n    }).catch(error => {\n      console.error('红绿灯模型重新加载失败:', error);\n      // 如果加载失败，使用简单的替代物体\n      createFallbackTrafficLights(converterInstance, intersections);\n    });\n    return;\n  }\n\n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach(lightObj => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersections.forEach(intersection => {\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      try {\n        // 确保模型存在且可以克隆\n        if (!preloadedTrafficLightModel || !preloadedTrafficLightModel.clone) {\n          throw new Error('红绿灯模型无效或无法克隆');\n        }\n\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n\n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n\n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n\n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n\n        // 设置材质属性\n        trafficLightModel.traverse(child => {\n          if (child.isMesh) {\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            child.renderOrder = 100;\n          }\n        });\n\n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n\n        // 将红绿灯添加到场景中\n        // scene.add(trafficLightModel);\n\n        // ========== 新增：在红绿灯地面添加指北针图标 =============\n        // 创建一个平面用于显示指北针SVG图标\n        const compassTextureLoader = new THREE.TextureLoader();\n        const compassIconPath = `${BASE_URL}/images/compass.svg`; // public目录下的路径\n        const compassMaterial = new THREE.MeshBasicMaterial({\n          map: compassTextureLoader.load(compassIconPath),\n          transparent: true,\n          opacity: 1\n        });\n        // 平面几何体，5x5米\n        const compassGeometry = new THREE.PlaneGeometry(5, 5);\n        const compassMesh = new THREE.Mesh(compassGeometry, compassMaterial);\n        // 指北针放在红绿灯正下方地面（y=0.1，略高于地面防止Z冲突）\n        compassMesh.position.set(modelPos.x + 20, 1.5, -(modelPos.y + 20));\n        // 指北针朝上，旋转x轴-90度\n        compassMesh.rotation.x = -Math.PI / 2;\n        // 渲染优先级高，避免被地面遮挡\n        compassMesh.renderOrder = 101;\n        // 可选：添加userData标记\n        compassMesh.userData = {\n          type: 'compassIcon',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        // 添加到场景\n        scene.add(compassMesh);\n        // ========== 指北针图标添加结束 =============\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        // createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n\n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance, intersections) => {\n  intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({\n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n\n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n\n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n\n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,\n    // 完全透明\n    depthWrite: false\n  });\n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  trafficLightModel.add(collider);\n\n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n\n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n\n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: 0xFF0000\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n\n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  trafficLightModel.add(lightMesh);\n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = phaseId => {\n  switch (phaseId) {\n    case '1':\n      return '北进口左转';\n    case '2':\n      return '北进口直行';\n    case '3':\n      return '北进口右转';\n    case '5':\n      return '东进口左转';\n    case '6':\n      return '东进口直行';\n    case '7':\n      return '东进口右转';\n    case '9':\n      return '南进口左转';\n    case '10':\n      return '南进口直行';\n    case '11':\n      return '南进口右转';\n    case '13':\n      return '西进口左转';\n    case '14':\n      return '西进口直行';\n    case '15':\n      return '西进口右转';\n    default:\n      return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = dirCode => {\n  switch (dirCode) {\n    case 'N':\n      return '北向南';\n    case 'S':\n      return '南向北';\n    case 'E':\n      return '东向西';\n    case 'W':\n      return '西向东';\n    case 'NE':\n      return '东北向西南';\n    case 'NW':\n      return '西北向东南';\n    case 'SE':\n      return '东南向西北';\n    case 'SW':\n      return '西南向东北';\n    default:\n      return `方向${dirCode}`;\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = setPopoverState => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n\n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n\n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({\n    ...prev,\n    visible: false,\n    content: null,\n    // 清空内容\n    phases: [] // 清空相位信息\n  }));\n  console.log('弹窗已关闭，所有相关资源已清理');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = interId => {\n  try {\n    var _document$querySelect, _document$querySelect2;\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n\n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      return false;\n    }\n\n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n\n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 创建弹出窗口内容\n    let content;\n    if (stateInfo && stateInfo.phases) {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '220px',\n          maxHeight: '300px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          },\n          children: [intersection.name, \" (ID: \", interId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3661,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                break;\n            }\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '6px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '4px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: getPhaseDirection(phase.phaseId)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3687,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3691,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 3px',\n                    borderRadius: '2px'\n                  },\n                  children: phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3692,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3690,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3703,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3704,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3702,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3680,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3670,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '6px',\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3710,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3660,\n        columnNumber: 9\n      }, this);\n    } else {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          maxWidth: '200px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '8px'\n          },\n          children: intersection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3718,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u8DEF\\u53E3ID: \", interId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3719,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3720,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3717,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2 - 500;\n    const centerY = window.innerHeight / 2 - 500;\n\n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = (_document$querySelect = document.querySelector('#root')) === null || _document$querySelect === void 0 ? void 0 : (_document$querySelect2 = _document$querySelect.__REACT_INSTANCE) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.setTrafficLightPopover;\n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: {\n          x: centerX,\n          y: centerY\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      document.body.appendChild(popover);\n\n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  return list;\n};\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = interId => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n\n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 判断是否有相位数据\n    const hasPhaseData = stateInfo && stateInfo.phases && stateInfo.phases.length > 0;\n    let content;\n\n    // 指北针样式\n    const compassStyle = {\n      position: 'absolute',\n      top: '5px',\n      right: '25px',\n      width: '30px',\n      height: '30px',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      borderRadius: '50%',\n      background: 'rgba(0,0,0,0.1)',\n      zIndex: 10\n    };\n\n    // 指北针组件\n    const CompassIcon = () => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: compassStyle,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          transform: 'rotate(0deg)'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#ff5252',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            lineHeight: '14px'\n          },\n          children: \"N\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3872,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            width: 0,\n            height: 0,\n            borderLeft: '6px solid transparent',\n            borderRight: '6px solid transparent',\n            borderBottom: '10px solid #ff5252',\n            marginTop: '-2px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3878,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3866,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 3865,\n      columnNumber: 7\n    }, this);\n    if (hasPhaseData) {\n      // 相位ID与方向/方式的映射\n      const phaseMap = {\n        '1': {\n          dir: 'N',\n          type: 'left'\n        },\n        '2': {\n          dir: 'N',\n          type: 'straight'\n        },\n        '3': {\n          dir: 'N',\n          type: 'right'\n        },\n        '5': {\n          dir: 'E',\n          type: 'left'\n        },\n        '6': {\n          dir: 'E',\n          type: 'straight'\n        },\n        '7': {\n          dir: 'E',\n          type: 'right'\n        },\n        '9': {\n          dir: 'S',\n          type: 'left'\n        },\n        '10': {\n          dir: 'S',\n          type: 'straight'\n        },\n        '11': {\n          dir: 'S',\n          type: 'right'\n        },\n        '13': {\n          dir: 'W',\n          type: 'left'\n        },\n        '14': {\n          dir: 'W',\n          type: 'straight'\n        },\n        '15': {\n          dir: 'W',\n          type: 'right'\n        }\n      };\n      const typeOrder = ['left', 'straight', 'right'];\n      const colorMap = {\n        G: '#00ff00',\n        Y: '#ffff00',\n        R: '#ff0000'\n      };\n      const dirData = {\n        N: {},\n        E: {},\n        S: {},\n        W: {}\n      };\n      stateInfo.phases.forEach(phase => {\n        const map = phaseMap[phase.phaseId];\n        if (map) {\n          dirData[map.dir][map.type] = {\n            color: colorMap[phase.trafficLight] || '#888',\n            remainTime: phase.remainTime\n          };\n        }\n      });\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '260px',\n          background: 'rgba(0,0,0,0.05)',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CompassIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3915,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '8px',\n            fontSize: '15px',\n            textAlign: 'center'\n          },\n          children: [intersection.name, \"\\u706F\\u6001\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3916,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateRows: '60px 60px 60px',\n            gridTemplateColumns: '60px 60px 60px',\n            justifyContent: 'center',\n            alignItems: 'center',\n            background: 'rgba(255,255,255,0.05)',\n            borderRadius: '8px',\n            margin: '0 auto',\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 1,\n              gridColumn: 2,\n              textAlign: 'center',\n              display: 'flex',\n              justifyContent: 'center',\n              width: '100%'\n            },\n            children: typeOrder.map((type, index) => {\n              // 反转左转和右转在数组中的顺序\n              let displayIndex = index;\n              if (index === 0) displayIndex = 2;else if (index === 2) displayIndex = 0;\n              const currentType = typeOrder[displayIndex];\n\n              // 计算与南边对齐的样式\n              const marginStyle = {};\n              if (currentType === 'left') {\n                // 左转箭头 (右侧显示)\n                marginStyle.marginRight = '0px';\n              } else if (currentType === 'straight') {\n                // 直行箭头 (中间显示)\n                marginStyle.marginLeft = '10px';\n                marginStyle.marginRight = '10px';\n              } else if (currentType === 'right') {\n                // 右转箭头 (左侧显示)\n                marginStyle.marginLeft = '0px';\n              }\n              return dirData.N[currentType] &&\n              /*#__PURE__*/\n              // <div key={currentType} style={{\n              //   display: 'flex',\n              //   flexDirection: 'column',\n              //   alignItems: 'center',\n              //   ...marginStyle\n              // }}>\n              _jsxDEV(\"div\", {\n                style: {\n                  marginRight: currentType === 'left' ? 0 : '10px',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    color: dirData.N[currentType].color,\n                    fontWeight: 'bold',\n                    marginBottom: '3px'\n                  },\n                  children: dirData.N[currentType].remainTime\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3958,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: dirData.N[currentType].color,\n                    fontSize: '20px',\n                    lineHeight: '20px'\n                  },\n                  children: currentType === 'left' ? '\\u{1F87A}' : currentType === 'straight' ? '\\u{1F87B}' : '\\u{1F878}'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3959,\n                  columnNumber: 21\n                }, this)]\n              }, currentType, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3957,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3930,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 3,\n              gridColumn: 2,\n              textAlign: 'center',\n              display: 'flex',\n              justifyContent: 'center',\n              width: '100%'\n            },\n            children: typeOrder.map(type => dirData.S[type] && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginRight: type === 'right' ? 0 : '10px',\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: dirData.S[type].color,\n                  fontSize: '20px',\n                  lineHeight: '20px'\n                },\n                children: type === 'left' ? '\\u{1F878}' : type === 'straight' ? '\\u{1F879}' : '\\u{1F87A}'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3971,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: dirData.S[type].color,\n                  fontWeight: 'bold',\n                  marginTop: '3px'\n                },\n                children: dirData.S[type].remainTime\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3974,\n                columnNumber: 19\n              }, this)]\n            }, type, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3970,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3968,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 2,\n              gridColumn: 3,\n              textAlign: 'center'\n            },\n            children: typeOrder.map((type, index) => {\n              // 反转左转和右转在数组中的顺序\n              let displayIndex = index;\n              if (index === 0) displayIndex = 2;else if (index === 2) displayIndex = 0;\n              const currentType = typeOrder[displayIndex];\n              return dirData.E[currentType] && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '8px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'flex-start'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: dirData.E[currentType].color,\n                    fontSize: '20px',\n                    lineHeight: '20px'\n                  },\n                  children: currentType === 'left' ? '\\u{1F87B}' : currentType === 'straight' ? '\\u{1F878}' : '\\u{1F879}'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3992,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '14px',\n                    color: dirData.E[currentType].color,\n                    fontWeight: 'bold',\n                    marginLeft: '5px'\n                  },\n                  children: dirData.E[currentType].remainTime\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3995,\n                  columnNumber: 21\n                }, this)]\n              }, currentType, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3991,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3981,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              gridRow: 2,\n              gridColumn: 1,\n              textAlign: 'center'\n            },\n            children: typeOrder.map(type => dirData.W[type] && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'flex-end'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '14px',\n                  color: dirData.W[type].color,\n                  fontWeight: 'bold',\n                  marginRight: '5px'\n                },\n                children: dirData.W[type].remainTime\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 4010,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: dirData.W[type].color,\n                  fontSize: '20px',\n                  lineHeight: '20px'\n                },\n                children: type === 'left' ? '\\u{1F879}' : type === 'straight' ? '\\u{1F87A}' : '\\u{1F87B}'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 4016,\n                columnNumber: 19\n              }, this)]\n            }, type, true, {\n              fileName: _jsxFileName,\n              lineNumber: 4009,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 4007,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3917,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            fontSize: '11px',\n            color: '#888',\n            textAlign: 'center'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 4023,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3914,\n        columnNumber: 9\n      }, this);\n    } else {\n      // 没有相位数据时显示的内容\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '15px',\n          width: '260px',\n          background: 'rgba(0,0,0,0.05)',\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CompassIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 4032,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '10px',\n            fontSize: '15px',\n            textAlign: 'center'\n          },\n          children: intersection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 4033,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '20px 0',\n            color: '#ff9800',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            background: 'rgba(255,255,255,0.1)',\n            borderRadius: '8px',\n            marginBottom: '10px'\n          },\n          children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 4034,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#888',\n            textAlign: 'center'\n          },\n          children: [\"\\u8DEF\\u53E3ID: \", interId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 4046,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            fontSize: '11px',\n            color: '#888',\n            textAlign: 'center'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 4049,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 4031,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 设置弹窗位置在左上角区域\n    const x = 445;\n    const y = 250;\n\n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n\n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n\n    // 更新弹窗状态\n    if (window._setTrafficLightPopover) {\n      window._setTrafficLightPopover({\n        visible: true,\n        interId: interId,\n        position: {\n          x,\n          y\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n\n      // 设置定时更新\n      if (window.trafficLightUpdateTimerRef) {\n        window.trafficLightUpdateTimerRef.current = setInterval(() => {\n          window.showTrafficLightPopup(interId);\n        }, 1000);\n      }\n      return true;\n    } else {\n      console.error('无法找到setTrafficLightPopover函数');\n      return false;\n    }\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = object => {\n  let current = object;\n\n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n\n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n\n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n\n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n\n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n\n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = (x - rect.left) / canvas.clientWidth * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    console.log('归一化坐标:', mouseX, mouseY);\n\n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n\n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n\n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'position:', intersect.object.position.toArray(), 'userData:', intersect.object.userData);\n\n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      return true;\n    }\n\n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', '类型:', obj.type, '位置:', obj.position.toArray(), '距离:', intersect.distance, 'userData:', obj.userData);\n    });\n\n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        var _lightObj$intersectio;\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n\n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n\n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n\n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        if (isVisible) {\n          visibleCount++;\n        }\n        console.log(`红绿灯 ${interId}:`, {\n          名称: ((_lightObj$intersectio = lightObj.intersection) === null || _lightObj$intersectio === void 0 ? void 0 : _lightObj$intersectio.name) || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n\n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  var _trafficLight$interse, _trafficLight$interse2, _trafficLight$interse3;\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch (phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n\n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: (_trafficLight$interse = trafficLight.intersection) === null || _trafficLight$interse === void 0 ? void 0 : _trafficLight$interse.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n\n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = {\n    isLight: true\n  };\n\n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  console.log(`更新路口 ${((_trafficLight$interse2 = trafficLight.intersection) === null || _trafficLight$interse2 === void 0 ? void 0 : _trafficLight$interse2.name) || ((_trafficLight$interse3 = trafficLight.intersection) === null || _trafficLight$interse3 === void 0 ? void 0 : _trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "SkeletonUtils", "mqtt", "Select", "Popover", "axios", "VideoPlayer", "DevicePopoverContent", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "peopleBaseModel", "skeleton", "lastPosition", "lastRotation", "ALPHA", "vehicleLastPositions", "Map", "vehicleLastRotations", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "process", "env", "REACT_APP_API_URL", "console", "log", "vehicleModels", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "clock", "Clock", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "vehicleId", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "has", "lastPos", "get", "distance", "distanceTo", "MAX_DISTANCE_THRESHOLD", "toFixed", "filteredPos", "Vector3", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "lastRot", "MAX_ANGLE_THRESHOLD", "abs", "TRAFFIC_LIGHT_UPDATE_INTERVAL", "mixersToCleanup", "Set", "bonesMap", "resourceManager", "mixers", "bones", "actions", "models", "addMixer", "mixer", "model", "add", "traverse", "object", "isBone", "uuid", "addAction", "action", "removeMixer", "for<PERSON>ach", "stop", "delete", "stopAllAction", "root", "getRoot", "animations", "length", "uncacheRoot", "uncacheAction", "uncacheClip", "clips", "_actions", "map", "a", "_clip", "filter", "Boolean", "clip", "e", "cleanup", "clear", "bone", "parent", "remove", "matrix", "identity", "matrixWorld", "<PERSON><PERSON><PERSON>", "geometry", "dispose", "material", "createAnimationMixer", "AnimationMixer", "createAction", "clipAction", "processedMessageIds", "eventListCache", "eventIdCounter", "eventMarkers", "CampusModel", "className", "onCurrentRSUChange", "selectedRSUs", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "mapRef", "lastCameraPosition", "lastCameraTarget", "cameraSmoothing", "prevAnimationTimeRef", "Date", "now", "peopleAnimationMixers", "peopleAnimations", "devicesData", "setDevicesData", "devices", "loadDevicesData", "devicesArray", "apiUrl", "success", "intersections", "setIntersections", "fetchIntersections", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "currentPopoverIdRef", "trafficLightUpdateTimerRef", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "devicePopover", "setDevicePopover", "deviceId", "handleCloseDevicePopover", "renderDevicePopoverContent", "device", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "switchToFollowView", "current", "enabled", "switchToGlobalView", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "target", "currentTarget", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "updateMatrix", "updateMatrixWorld", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "相机位置", "toArray", "目标点", "hasTrafficLight", "setTimeout", "showTrafficLightPopup", "handleMqttMessage", "message", "_payload$data3", "_payload$data4", "_payload$data5", "_payload$data6", "_payload$data7", "_payload$data8", "_payload$data9", "_payload$data10", "_payload$data11", "_payload$data12", "payload", "JSON", "parse", "_payload$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "participants", "rsuid", "participant", "id", "partPtcId", "type", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "newModel", "height", "rotation", "scale", "play", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "p", "modelData", "bsmData", "bsmid", "newState", "partLong", "partLat", "postMessage", "source", "initialPosition", "initialRotation", "newPosition", "vehicleObj", "newVehicleModel", "child", "newMaterial", "emissive", "Color", "transparent", "needsUpdate", "speedLabel", "createTextSprite", "round", "r", "g", "b", "textColor", "renderOrder", "opacity", "is<PERSON><PERSON>", "Out", "filteredPosition", "timeSinceLastUpdate", "undefined", "originalTran<PERSON>arent", "originalOpacity", "m", "phasesInfo", "phase", "phaseId", "toString", "direction", "trafficDirec", "getDirectionFromCode", "getPhaseDirection", "lightState", "trafficLight", "remainTime", "parseInt", "phaseInfo", "push", "trafficLightKey", "String", "trafficLightModel", "updateTrafficLightVisual", "prev", "<PERSON><PERSON><PERSON>", "strId", "numId", "updateTime", "rsiData", "rsuId", "events", "rtes", "event", "_payload$data2", "eventId", "rteId", "eventType", "description", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "messageId", "size", "idsArray", "from", "stringify", "onerror", "onclose", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "setIsVehicleLoaded", "xhr", "loaded", "total", "initializeScene", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "deltaTime", "<PERSON><PERSON><PERSON><PERSON>", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "targetCameraPosition", "targetLookAt", "lerp", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "render", "handleResize", "aspect", "addEventListener", "eventCleanupInterval", "setInterval", "removeInactiveEvents", "setGlobalView", "cleanup3DEvents", "cancelAnimationFrame", "clearInterval", "marker", "removeAll", "objectsToRemove", "obj", "setAnimationLoop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "forceContextLoss", "handleMainVehicleChange", "intervalId", "removeEventListener", "timer", "createTrafficLights", "clearTimeout", "handleClick", "handleMouseClick", "initScene", "createSimpleTrafficLight", "BoxGeometry", "MeshBasicMaterial", "<PERSON><PERSON>", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "addClickHelpers", "lightObj", "helperGeometry", "SphereGeometry", "helperMaterial", "depthWrite", "helper<PERSON><PERSON>", "userData", "isClickHelper", "firstTrafficLightIntersection", "targetIntersection", "renderEntranceDeviceIcons", "converterInstance", "warn", "sceneTraverse", "设备总数", "路口总数", "existingIcons", "isEntranceDeviceIcons", "totalRenderedDevices", "entrances", "entrance", "isNaN", "d", "group", "iconSize", "iconSpacing", "totalWidth", "size3D", "spacing3D", "startX", "idx", "textureLoader", "TextureLoader", "iconPath", "iconMaterial", "bgMaterial", "bgWidth", "bgHeight", "bgGeometry", "PlaneGeometry", "bg<PERSON><PERSON>", "iconGeometry", "<PERSON><PERSON><PERSON>", "iconGroup", "deviceType", "isEntranceDeviceIcon", "pillarHeight", "pillarGeometry", "pillarMaterial", "pillar", "isEntranceDevicePillar", "_devicesData$devices", "devicesCount", "intersectionsCount", "retryTimer", "animateBillboard", "container", "sceneInstance", "cameraInstance", "rect", "getBoundingClientRect", "mouseX", "clientX", "clientWidth", "mouseY", "clientY", "clientHeight", "raycaster", "Raycaster", "params", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "intersects", "intersectObjects", "style", "placeholder", "onChange", "onSelect", "options", "label", "bordered", "dropdownStyle", "maxHeight", "ref", "max<PERSON><PERSON><PERSON>", "right", "background", "onClick", "handleClosePopover", "min<PERSON><PERSON><PERSON>", "_c", "text", "parameters", "fontFace", "borderThickness", "borderColor", "canvas", "document", "createElement", "context", "getContext", "textWidth", "measureText", "font", "textBaseline", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "minFilter", "LinearFilter", "spriteMaterial", "SpriteMaterial", "sprite", "Sprite", "depthTest", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "trafficLightGltf", "vehicleGltf", "cyclistGltf", "peopleGltf", "all", "loadAsync", "materia", "<PERSON><PERSON><PERSON><PERSON>", "err", "types", "calculateDistance", "x1", "y1", "x2", "y2", "c", "sqrt", "getEventThresholds", "timeT<PERSON><PERSON>old", "distanceThreshold", "checkDuplicateEvent", "eventKey", "currentTime", "cachedEvent", "timeDiff", "lastUpdateTime", "updateCount", "isDuplicate", "matchedEvent", "newEventId", "padStart", "newEvent", "firstDetectedTime", "inactiveThreshold", "initialCount", "removedEvents", "inactiveTime", "removedCount", "getEventBackgroundColor", "getEventTypeName", "createEventMarker", "markerGroup", "backgroundGeometry", "backgroundMaterial", "side", "DoubleSide", "<PERSON><PERSON><PERSON>", "eventTypeName", "textLabel", "updateEventMarkerPosition", "eventData", "duplicateResult", "then", "catch", "createFallbackTrafficLights", "Error", "compassTextureLoader", "compassIconPath", "compassMaterial", "compassGeometry", "compassMesh", "colliderGeometry", "colliderMaterial", "collider", "isCollider", "lightGeometry", "lightMaterial", "<PERSON><PERSON><PERSON>", "dirCode", "setPopoverState", "testTrafficLightClick", "_document$querySelect", "_document$querySelect2", "light", "lightModel", "stateInfo", "overflowY", "marginBottom", "borderBottom", "paddingBottom", "index", "lightColor", "justifyContent", "marginTop", "toLocaleTimeString", "centerX", "centerY", "__REACT_INSTANCE", "popover", "innerHTML", "body", "closeButton", "listTrafficLights", "list", "numericId", "hasPhaseData", "compassStyle", "alignItems", "CompassIcon", "flexDirection", "borderLeft", "borderRight", "phaseMap", "dir", "typeOrder", "colorMap", "G", "Y", "R", "dirD<PERSON>", "N", "E", "S", "W", "gridTemplateRows", "gridTemplateColumns", "margin", "gridRow", "gridColumn", "displayIndex", "currentType", "marginStyle", "marginRight", "marginLeft", "getTrafficLightFromObject", "testClickDetection", "trafficLightObjects", "tlIntersects", "intersect", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "worldPos", "getWorldPosition", "distanceToCamera", "screenPos", "project", "名称", "可见性", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "isLight", "emissiveIntensity", "PointLight", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js';\n\n\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport axios from 'axios'; // 新增 axios 导入\nimport VideoPlayer from './VideoPlayer'; // 引入摄像头视频播放组件\nimport DevicePopoverContent from './DevicePopoverContent';\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel= null; // 存储原始模型数据\nlet skeleton =null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n\n\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n\n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n\n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n\n  const lastPos = vehicleLastPositions.get(vehicleId);\n\n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n\n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n\n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n\n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n\n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n\n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n\n  const lastRot = vehicleLastRotations.get(vehicleId);\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n\n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n\n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n\n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\n// 在文件顶部添加\nconst mixersToCleanup = new Set();\nconst bonesMap = new Map();\n\n// 在文件顶部添加资源管理器\nconst resourceManager = {\n  mixers: new Set(),\n  bones: new Map(),\n  actions: new Map(),\n  models: new Set(),\n\n  addMixer(mixer, model) {\n    this.mixers.add(mixer);\n    if (model) {\n      this.models.add(model);\n      // 记录骨骼\n      model.traverse(object => {\n        if (object.isBone) {\n          this.bones.set(object.uuid, object);\n        }\n      });\n    }\n    return mixer;\n  },\n\n  addAction(action, mixer) {\n    if (!this.actions.has(mixer)) {\n      this.actions.set(mixer, new Set());\n    }\n    this.actions.get(mixer).add(action);\n    return action;\n  },\n\n  removeMixer(mixer) {\n    if (this.mixers.has(mixer)) {\n      try {\n        // 停止并清理所有动作\n        if (this.actions.has(mixer)) {\n          this.actions.get(mixer).forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          this.actions.delete(mixer);\n        }\n\n        // 停止混合器\n        if (typeof mixer.stopAllAction === 'function') {\n          mixer.stopAllAction();\n        }\n\n        // 清理混合器的根对象\n        const root = mixer.getRoot();\n        if (root) {\n          this.models.delete(root);\n          root.traverse(object => {\n            if (object && object.isBone) {\n              this.bones.delete(object.uuid);\n            }\n            if (object && object.animations) {\n              object.animations.length = 0;\n            }\n          });\n\n          // 安全地清理缓存\n          try {\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n\n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n\n            // 这里是发生错误的地方，添加防御性检查\n            if (typeof mixer.uncacheClip === 'function') {\n              // 不直接调用uncacheClip(null)，而是获取混合器中的所有动画片段并逐个清理\n              const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n              clips.forEach(clip => {\n                if (clip && clip.uuid) {\n                  mixer.uncacheClip(clip);\n                }\n              });\n            }\n          } catch (e) {\n            console.log('在清理动画混合器时发生非致命错误:', e);\n            // 继续执行其余清理操作\n          }\n        }\n\n        this.mixers.delete(mixer);\n      } catch (error) {\n        console.error('清理动画混合器时发生错误:', error);\n        // 确保即使出错也会从集合中移除\n        this.mixers.delete(mixer);\n      }\n    }\n  },\n\n  cleanup() {\n    try {\n      // 清理动作\n      this.actions.forEach((actions, mixer) => {\n        try {\n          actions.forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          actions.clear();\n        } catch (e) {\n          console.log('清理动作时发生非致命错误:', e);\n        }\n      });\n      this.actions.clear();\n\n      // 清理混合器\n      this.mixers.forEach(mixer => {\n        try {\n          if (typeof mixer.stopAllAction === 'function') {\n            mixer.stopAllAction();\n          }\n\n          const root = mixer.getRoot();\n          if (root) {\n            root.traverse(object => {\n              if (object && object.animations) {\n                object.animations.length = 0;\n              }\n            });\n\n            // 安全清理\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n\n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n\n            // 安全清理动画片段\n            try {\n              if (mixer._actions && Array.isArray(mixer._actions)) {\n                const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n                clips.forEach(clip => {\n                  if (clip && clip.uuid && typeof mixer.uncacheClip === 'function') {\n                    mixer.uncacheClip(clip);\n                  }\n                });\n              }\n            } catch (e) {\n              console.log('清理动画片段时发生非致命错误:', e);\n            }\n          }\n        } catch (e) {\n          console.log('清理混合器时发生非致命错误:', e);\n        }\n      });\n      this.mixers.clear();\n\n      // 清理骨骼\n      this.bones.forEach(bone => {\n        if (bone.parent) {\n          bone.parent.remove(bone);\n        }\n        if (bone.matrix) bone.matrix.identity();\n        if (bone.matrixWorld) bone.matrixWorld.identity();\n      });\n      this.bones.clear();\n\n      // 清理模型\n      this.models.forEach(model => {\n        if (model.parent) {\n          model.parent.remove(model);\n        }\n        model.traverse(object => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n          }\n          if (object.animations) {\n            object.animations.length = 0;\n          }\n        });\n      });\n      this.models.clear();\n    } catch (error) {\n      console.error('全局清理过程中发生错误:', error);\n      // 即使发生错误也尝试清空集合\n      this.actions.clear();\n      this.mixers.clear();\n      this.bones.clear();\n      this.models.clear();\n    }\n  }\n};\n\n// 修改创建动画混合器的函数\nconst createAnimationMixer = (model) => {\n  const mixer = new THREE.AnimationMixer(model);\n  return resourceManager.addMixer(mixer, model);\n};\n\n// 修改动画动作创建的函数\nconst createAction = (clip, mixer, model) => {\n  const action = mixer.clipAction(clip, model);\n  return resourceManager.addAction(action, mixer);\n};\n\n// 在CampusModel组件顶部添加消息缓存\nconst processedMessageIds = new Set();\n\n// 添加事件去重相关的全局变量\nlet eventListCache = []; // 事件列表缓存，存储所有事件的完整信息\nlet eventIdCounter = 1; // 事件ID计数器\nlet eventMarkers = new Map(); // 存储事件标记的映射，key为eventId，value为THREE对象\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n\n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n\n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = new Map(); // 使用全局Map存储所有行人的动画混合器（不再使用useRef）\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n\n  // 设备数据 state\n  const [devicesData, setDevicesData] = useState({ devices: [] });\n\n  // 动态加载设备数据\n  const loadDevicesData = async () => {\n    let devicesArray = [];\n    try {\n      // const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const apiUrl = BASE_URL;\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      if (response.data && response.data.success) {\n        devicesArray = response.data.data;\n      }\n\n      console.log('devicesArrayapiUrl', apiUrl);\n      console.log('devicesArray', devicesArray);\n    } catch (error) {\n      try {\n        const response = await fetch('/src/data/devices.json');\n        const json = await response.json();\n        devicesArray = json.devices || [];\n      } catch (e) {\n        console.error('设备数据加载失败', e);\n      }\n    }\n    setDevicesData({ devices: devicesArray });\n  };\n  useEffect(() => { loadDevicesData(); }, []);\n\n // 路口数据 state\n  const [intersections, setIntersections] = useState([]);\n  // 动态加载路口数据\n  useEffect(() => {\n    const fetchIntersections = async () => {\n      try {\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const response = await axios.get(`${apiUrl}/api/intersections`);\n        if (response.data && response.data.success) {\n          setIntersections(response.data.data || []);\n        }\n      } catch (error) {\n        console.error('获取路口信息失败:', error);\n      }\n    };\n    fetchIntersections();\n  }, []);\n\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n\n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n\n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n\n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n\n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '65px',  // 从 60px 改为 65px\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '65px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 设备信息弹框状态\n  const [devicePopover, setDevicePopover] = useState({\n    visible: false,\n    deviceId: null,\n    position: { x: 0, y: 0 },\n    content: null\n  });\n\n  // 设备弹框关闭函数\n  const handleCloseDevicePopover = () => {\n    setDevicePopover({ visible: false, deviceId: null, position: { x: 0, y: 0 }, content: null });\n  };\n\n  // 设备弹框内容渲染函数\n  const renderDevicePopoverContent = (device) => {\n    if (!device) return null;\n    return <DevicePopoverContent device={device} />;\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n\n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n\n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n\n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n\n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n\n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos)\n          .to({ x: 0, y: 300, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.position.copy(currentPos);\n          })\n          .start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp)\n          .to({ x: 0, y: 1, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.up.copy(currentUp);\n          })\n          .start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n\n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget)\n          .to({ x: 0, y: 0, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            controls.target.copy(currentTarget);\n            // 确保相机始终朝向目标点\n            cameraRef.current.lookAt(controls.target);\n            controls.update();\n          })\n          .start();\n\n        // 启用控制器\n        controls.enabled = true;\n\n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n\n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersections.find(i => i.name === value);\n\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x+50, 70, -modelCoords.y+50);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n\n      // 如果该路口有红绿灯，自动显示红绿灯弹窗\n      if (intersection.hasTrafficLight !== false && intersection.interId) {\n        console.log(`路口 ${intersection.name} 有红绿灯，准备显示红绿灯状态`);\n\n        // 延迟300ms调用以确保场景已更新\n        setTimeout(() => {\n          // 检查多种可能的ID格式\n          let interId = intersection.interId;\n\n          // 使用全局的showTrafficLightPopup函数显示红绿灯弹窗\n          if (window.showTrafficLightPopup) {\n            window.showTrafficLightPopup(interId);\n            console.log(`已触发路口 ${intersection.name} (ID: ${interId}) 的红绿灯弹窗显示`);\n          } else {\n            console.error('找不到显示红绿灯弹窗的函数');\n          }\n        }, 300);\n      } else {\n        console.log(`路口 ${intersection.name} 没有红绿灯或缺少ID，不显示红绿灯状态`);\n\n        // 如果有弹窗正在显示，则关闭它\n        if (window._setTrafficLightPopover) {\n          window._setTrafficLightPopover({\n            visible: false\n          });\n        }\n      }\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        // console.log('收到RSM消息:', payload);\n\n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n\n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n          return;\n        }\n\n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n\n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n\n        const participants = payload.data?.participants || [];\n        const rsuid = payload.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id =  deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1': // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2': // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3': // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return; // 跳过未知类型\n            }\n\n            // 获取或创建模型\n          let model = vehicleModels.get(id);\n\n          if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = type === '3' ? SkeletonUtils.clone(preloadedPeopleModel) : preloadedModel.clone();\n              // 根据类型调整高度和缩放\n              const height = type === '3' ? 2.0 : 1.0;\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n\n              // 如果是行人类型，设置缩放和创建动画\n              if (type === '3') {\n              // newModel.scale.set(0.005, 0.005, 0.005);\n                newModel.scale.set(4, 4, 4);\n\n                // 使用resourceManager创建并管理动画混合器\n                const mixer = createAnimationMixer(newModel);\n\n                if (peopleBaseModel && peopleBaseModel.animations && peopleBaseModel.animations.length > 0) {\n                  // 只创建一个动作并添加到资源管理器\n                  const action = createAction(peopleBaseModel.animations[0], mixer, newModel);\n                  action.play();\n                }\n\n                // 保存到全局Map中(不再使用useRef)\n                peopleAnimationMixers.set(id, mixer);\n              }\n\n              scene.add(newModel);\n\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n              type: type\n              });\n          } else if (model) {\n              // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 1000;\n        const currentIds = new Set(participants.map(p => deviceMac + p.partPtcId));\n\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            // 如果是行人，清理动画混合器\n            if (modelData.type === '3' && peopleAnimationMixers.has(id)) {\n              const mixer = peopleAnimationMixers.get(id);\n              // 使用resourceManager清理混合器\n              resourceManager.removeMixer(mixer);\n              peopleAnimationMixers.delete(id);\n            }\n\n            // 从场景移除模型\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n\n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n\n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n\n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n\n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid, // 直接传递车辆ID\n          data: {       // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n\n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n\n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n\n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n\n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n\n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n\n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n\n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n\n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n\n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n\n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n\n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ?\n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 10,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n\n          scene.add(newVehicleModel);\n\n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n\n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position)\n            .to({ y: 0.5 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .start();\n\n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({ opacity: 0.6 })\n                .to({ opacity: 1.0 }, 500)\n                .easing(TWEEN.Easing.Quadratic.Out)\n                .onUpdate(function() {\n                  child.material.opacity = this.opacity;\n                  child.material.needsUpdate = true;\n                })\n                .start();\n            }\n          });\n\n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({ opacity: 0.6 })\n            .to({ opacity: 1.0 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .onUpdate(function() {\n              speedLabel.material.opacity = this.opacity;\n              speedLabel.material.needsUpdate = true;\n            })\n            .start();\n\n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n\n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n\n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n\n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ?\n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n\n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n\n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 增加到10秒，避免频繁清理造成闪烁\n\n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n\n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1 ;\n\n            modelData.model.traverse((child) => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n\n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n\n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n\n            modelData.model.traverse((child) => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n\n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n\n        return;\n      }\n\n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n\n        try {\n          const payload = JSON.parse(message);\n\n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n\n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n\n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n\n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n\n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n\n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n\n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n\n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n\n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ?\n                    getDirectionFromCode(phase.trafficDirec) :\n                    getPhaseDirection(phaseId);\n\n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n\n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n\n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n\n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n\n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n\n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n\n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n\n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n\n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n\n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n\n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef &&\n                     (window.currentPopoverIdRef.current === modelKey ||\n                      window.currentPopoverIdRef.current === String(modelKey) ||\n                      window.currentPopoverIdRef.current === parseInt(modelKey))) {\n\n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n\n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n        return;\n      }\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件，确保包含mac和timestamp\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data,\n          mac: payload.mac,\n          tm: payload.tm\n        }, '*');\n\n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n\n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n\n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor, eventType, {\n            rsuId: payload.data?.rsuId || 'UNKNOWN',\n            eventId: eventId,\n            description: description\n          });\n\n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n\n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f', '2', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14', '1002', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45', '404', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff', '10', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d', '12', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a', '13', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1', '21-8', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96', '904', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16', '910', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2', '999', { rsuId: payload.data?.rsuId, sceneData });\n            break;\n        }\n\n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n\n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 检查消息ID是否已处理过\n          if (message.messageId) {\n            if (processedMessageIds.has(message.messageId)) {\n              // console.log('跳过重复消息:', message.messageId);\n              return;\n            }\n\n            // 添加到已处理集合\n            processedMessageIds.add(message.messageId);\n\n            // 限制缓存大小，防止内存泄漏\n            if (processedMessageIds.size > 1000) {\n              // 转换为数组，删除最早的100个元素\n              const idsArray = Array.from(processedMessageIds);\n              for (let i = 0; i < 100; i++) {\n                processedMessageIds.delete(idsArray[i]);\n              }\n            }\n          }\n\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n\n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/vehicle.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n\n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n\n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n\n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n\n                  // 应用新材质\n                  child.material = newMaterial;\n\n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n\n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n\n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n\n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n\n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n\n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n\n          // 检查scene是否初始化\n          if (scene) {\n          scene.add(model);\n\n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画 - 只更新存在的混合器\n      const deltaTime = clock.getDelta();\n\n      // 使用Map而不是ref.current\n      if (peopleAnimationMixers.size > 0) {\n        peopleAnimationMixers.forEach((mixer) => {\n          mixer.update(deltaTime);\n        });\n      }\n\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          200,\n          -50 * Math.sin(adjustedRotation)\n        );\n\n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n\n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n\n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n\n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n\n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 路口视角模式\n        controls.update();\n      }\n\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加事件清理定时器\n    const eventCleanupInterval = setInterval(() => {\n      removeInactiveEvents();\n    }, 30000); // 每30秒清理一次非活跃事件\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // // 添加全局测试函数用于验证事件去重功能\n    // window.test3DEventDeduplication = () => {\n    //   console.log('🧪 开始3D场景事件去重测试');\n\n    //   // 测试位置\n    //   const testPosition = { x: 100, y: 100 };\n\n    //   // 测试1：创建新事件\n    //   console.log('测试1：创建新的违停车辆事件');\n    //   showWarningMarker(testPosition, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });\n\n    //   // 测试2：相同位置的重复事件（应该被去重）\n    //   setTimeout(() => {\n    //     console.log('测试2：相同位置的重复事件（应该被去重）');\n    //     showWarningMarker(testPosition, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });\n    //   }, 1000);\n\n    //   // 测试3：稍微不同位置的事件（距离在阈值内，应该被去重）\n    //   setTimeout(() => {\n    //     console.log('测试3：稍微不同位置的事件（应该被去重）');\n    //     showWarningMarker({ x: 105, y: 105 }, '违停车辆', '#ff4d4f', '910', { rsuId: 'TEST_RSU' });\n    //   }, 2000);\n\n    //   // 测试4：不同类型的事件（应该创建新事件）\n    //   setTimeout(() => {\n    //     console.log('测试4：不同类型的事件（应该创建新事件）');\n    //     showWarningMarker(testPosition, '逆行车辆', '#eb2f96', '904', { rsuId: 'TEST_RSU' });\n    //   }, 3000);\n\n    //   // 测试5：查看缓存状态\n    //   setTimeout(() => {\n    //     console.log('📊 3D场景事件缓存状态:', {\n    //       缓存事件数: eventListCache.length,\n    //       事件ID计数器: eventIdCounter,\n    //       场景标记数: eventMarkers.size,\n    //       事件列表: eventListCache.map(e => ({\n    //         ID: e.eventId,\n    //         类型: e.eventType,\n    //         更新次数: e.updateCount,\n    //         位置: `${e.position.lat.toFixed(6)}, ${e.position.lng.toFixed(6)}`\n    //       }))\n    //     });\n    //   }, 4000);\n    // };\n\n    // 添加手动清理3D事件的全局函数\n    window.cleanup3DEvents = () => {\n      console.log('🧹 手动清理3D场景事件');\n      removeInactiveEvents();\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 停止渲染循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理事件清理定时器\n      if (eventCleanupInterval) {\n        clearInterval(eventCleanupInterval);\n      }\n\n      // 3. 清理所有事件标记\n      eventMarkers.forEach((marker) => {\n        if (scene && marker) {\n          scene.remove(marker);\n        }\n      });\n      eventMarkers.clear();\n      eventListCache.length = 0;\n\n      // 4. 停止所有 TWEEN 动画\n      TWEEN.removeAll();\n\n      // 3. 清理所有动画混合器\n      peopleAnimationMixers.forEach(mixer => {\n        resourceManager.removeMixer(mixer);\n      });\n      peopleAnimationMixers.clear();\n\n      // 4. 清理所有其他资源\n      resourceManager.cleanup();\n\n      // 5. 清理场景中的所有对象\n      if (scene) {\n        const objectsToRemove = [];\n        scene.traverse((object) => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n            if (object !== scene) {\n              objectsToRemove.push(object);\n            }\n          }\n        });\n\n        // 从场景中移除对象\n        objectsToRemove.forEach(obj => {\n          if (obj.parent) {\n            obj.parent.remove(obj);\n          }\n        });\n\n        scene.clear();\n      }\n\n      // 6. 清理渲染器\n      if (renderer) {\n        renderer.setAnimationLoop(null);\n        if (containerRef.current && renderer.domElement && renderer.domElement.parentNode === containerRef.current) {\n          containerRef.current.removeChild(renderer.domElement);\n        }\n        renderer.dispose();\n        renderer.forceContextLoss();\n      }\n\n      // 7. 清理其他资源\n      if (controls) {\n        controls.dispose();\n      }\n\n      // 8. 清理数据结构\n      vehicleLastPositions.clear();\n      vehicleLastRotations.clear();\n      deviceTimestamps.clear();\n      vehicleModels.clear();\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n\n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n\n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n\n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n\n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current && intersections && intersections.length > 0) {\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current && intersections && intersections.length > 0) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current, intersections);\n        }\n      }, 2000);\n\n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景、坐标转换器或路口数据未准备好，暂不创建红绿灯');\n    }\n  }, [scene, intersections]);\n\n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current);\n        }\n      };\n\n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n\n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n\n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n\n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n\n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n\n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,//\n          transparent: false,\n          opacity: 0.1,  // 几乎透明\n          depthWrite: false\n        });\n\n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置\n\n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n\n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n\n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500);  // 延迟略长于debugTrafficLights\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n\n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n\n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersections && intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        // 查找第一个带有红绿灯的路口\n        const firstTrafficLightIntersection = intersections.find(\n          intersection => intersection.hasTrafficLight !== false && intersection.interId\n        );\n\n        // 如果找到带红绿灯的路口，优先选择它；否则选择第一个路口\n        const targetIntersection = firstTrafficLightIntersection || intersections[0];\n\n        console.log('自动选择路口:', targetIntersection.name,\n                    '具有红绿灯:', firstTrafficLightIntersection ? '是' : '否');\n\n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(targetIntersection.name);\n        }, 2000);\n\n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersections, selectedIntersection]);\n\n  // 新增：在场景初始化后渲染所有路口entrance的设备图标\n  const renderEntranceDeviceIcons = (converterInstance) => {\n    if (!scene || typeof scene.traverse !== 'function' || !converterInstance) {\n      console.warn('设备图标渲染条件不满足:', {\n        scene: !!scene,\n        sceneTraverse: scene && typeof scene.traverse === 'function',\n        converter: !!converterInstance\n      });\n      return;\n    }\n\n    if (!devicesData.devices || devicesData.devices.length === 0) {\n      console.warn('设备数据未加载，跳过设备图标渲染');\n      return; // 设备数据未加载时不渲染\n    }\n\n    if (!intersections || intersections.length === 0) {\n      console.warn('路口数据未加载，跳过设备图标渲染');\n      return;\n    }\n\n    console.log('开始渲染设备图标:', {\n      设备总数: devicesData.devices.length,\n      路口总数: intersections.length\n    });\n\n    try {\n      // 清理之前的设备图标\n      const existingIcons = scene.children.filter(obj => obj.userData && obj.userData.isEntranceDeviceIcons);\n      existingIcons.forEach(obj => scene.remove(obj));\n      console.log('清理了', existingIcons.length, '个旧的设备图标');\n\n      let totalRenderedDevices = 0;\n\n      intersections.forEach(intersection => {\n        if (!intersection.entrances || !Array.isArray(intersection.entrances)) return;\n        intersection.entrances.forEach((entrance) => {\n          if (!entrance.longitude || !entrance.latitude || isNaN(parseFloat(entrance.longitude)) || isNaN(parseFloat(entrance.latitude))) {\n            return;\n          }\n\n          const devices = devicesData.devices.filter(\n            d => d.location === intersection.name && d.entrance === entrance.name\n          );\n          if (devices.length === 0) return;\n\n          // 经纬度转模型坐标\n          console.log('渲染路口', intersection.name, '入口', entrance.name, '设备数量', devices.length);\n          const modelPos = converterInstance.wgs84ToModel(\n            parseFloat(entrance.longitude),\n            parseFloat(entrance.latitude)\n          );\n          // 创建一个组用于存放所有图标\n          const group = new THREE.Group();\n          group.position.set(modelPos.x, 10, -modelPos.y); // 上方10米\n          group.userData = { isEntranceDeviceIcons: true };\n          // 图标排成一排，居中\n          const iconSize = 32; // px\n          const iconSpacing = 8; // px\n          const totalWidth = devices.length * iconSize + (devices.length - 1) * iconSpacing;\n          // 以三维单位为准，假设1单位=1米，24px约等于0.6米\n          const size3D = 4.0; // 图标尺寸加大\n          const spacing3D = 0.5; // 图标间距加大\n          const startX = -((devices.length - 1) * (size3D + spacing3D)) / 2;\n          devices.forEach((device, idx) => {\n            // 创建一个平面用于显示SVG图标\n            const textureLoader = new THREE.TextureLoader();\n            const iconPath = `${BASE_URL}/images/${device.type}.svg`;\n            // 图标材质\n            const iconMaterial = new THREE.MeshBasicMaterial({\n              map: textureLoader.load(iconPath),\n              transparent: true,\n              opacity: 1 // 图标完全不透明\n            });\n            // 图标背景板材质\n            const bgMaterial = new THREE.MeshBasicMaterial({\n              color: 0x000000,\n              transparent: true,\n              opacity: 0.7 // 半透明黑色\n            });\n            // 背景板尺寸略大于图标\n            const bgWidth = size3D * 1.25;\n            const bgHeight = size3D * 1.25;\n            // 背景板几何体（圆角矩形）\n            // 由于Three.js没有内置圆角矩形，使用PlaneGeometry近似\n            const bgGeometry = new THREE.PlaneGeometry(bgWidth, bgHeight);\n            const bgMesh = new THREE.Mesh(bgGeometry, bgMaterial);\n            // 图标几何体\n            const iconGeometry = new THREE.PlaneGeometry(size3D, size3D);\n            const iconMesh = new THREE.Mesh(iconGeometry, iconMaterial);\n            // 图标略微前移，避免Z轴重叠闪烁\n            iconMesh.position.set(0, 0, 0.01);\n            // 创建一个组，包含背景和图标\n            const iconGroup = new THREE.Group();\n            iconGroup.add(bgMesh);\n            iconGroup.add(iconMesh);\n            // 整体平移到正确位置\n            iconGroup.position.set(startX + idx * (size3D + spacing3D), 0, 0);\n            // 不再固定旋转角度，后续在动画循环中让其始终面向相机\n            iconGroup.renderOrder = 999; // 提高渲染优先级\n            iconGroup.userData = {\n              deviceId: device.id,\n              deviceType: device.type,\n              entrance: entrance.name,\n              isEntranceDeviceIcon: true\n            };\n            group.add(iconGroup);\n          });\n          // 新增：添加白色半透明光柱，指向设备图标组\n          // 光柱高度等于图标组的y坐标（即10），底部在地面y=0，顶部在图标组中心\n          const pillarHeight =  group.position.y; // 10\n          const pillarGeometry = new THREE.CylinderGeometry(0.2, 0.2, pillarHeight, 16);\n          const pillarMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff, transparent: true, opacity: 0.7 });\n          const pillar = new THREE.Mesh(pillarGeometry, pillarMaterial);\n          // 设置光柱中心在y=0~y=10之间，底部正好在地面\n          pillar.position.set(0, -pillarHeight / 2, 0);\n          // pillar.position.set(0, -pillarHeight, 0);\n          // 可选：添加标记，便于后续查找或清理\n          pillar.userData = { isEntranceDevicePillar: true };\n          group.add(pillar);\n          scene.add(group);\n        });\n      });\n\n    } catch (e) {\n      console.error('renderEntranceDeviceIcons error', e);\n      return;\n    }\n  };\n\n  // 在场景初始化后调用渲染设备图标\n  useEffect(() => {\n    if (scene && typeof scene.traverse === 'function' && converter.current && devicesData.devices) {\n      console.log('触发设备图标渲染:', {\n        scene: !!scene,\n        converter: !!converter.current,\n        devicesCount: devicesData.devices?.length || 0,\n        intersectionsCount: intersections?.length || 0\n      });\n\n      // 延迟渲染，确保所有资源都已准备好\n      const timer = setTimeout(() => {\n        renderEntranceDeviceIcons(converter.current);\n      }, 500); // 延迟500ms\n\n      return () => clearTimeout(timer);\n    }\n  }, [scene, converter.current, devicesData.devices, intersections]); // 添加intersections依赖\n\n  // 添加额外的重试机制，确保设备图标能够正确渲染\n  useEffect(() => {\n    if (scene && converter.current && devicesData.devices && intersections) {\n      // 延迟3秒后检查是否有设备图标，如果没有则重新渲染\n      const retryTimer = setTimeout(() => {\n        const existingIcons = scene.children.filter(obj => obj.userData && obj.userData.isEntranceDeviceIcons);\n        if (existingIcons.length === 0) {\n          console.log('检测到设备图标缺失，执行重试渲染');\n          renderEntranceDeviceIcons(converter.current);\n        } else {\n          console.log('设备图标渲染正常，共', existingIcons.length, '个图标组');\n        }\n      }, 3000); // 3秒后检查\n\n      return () => clearTimeout(retryTimer);\n    }\n  }, [scene, converter.current, devicesData.devices, intersections]);\n\n  // 新增：每帧让所有设备图标组和事件图标组始终面向相机（billboard效果）\n  useEffect(() => {\n    if (!scene || !cameraRef.current) return;\n    const animateBillboard = () => {\n      // 遍历所有图标组，让其正对相机\n      scene.children.forEach(obj => {\n        // 设备图标组\n        if (obj.userData && obj.userData.isEntranceDeviceIcons) {\n          obj.lookAt(cameraRef.current.position.x, obj.position.y, cameraRef.current.position.z);\n        }\n        // 事件图标组\n        if (obj.userData && obj.userData.type === 'eventMarker') {\n          obj.lookAt(cameraRef.current.position.x, obj.position.y, cameraRef.current.position.z);\n        }\n      });\n      requestAnimationFrame(animateBillboard);\n    };\n    animateBillboard();\n  }, [scene]);\n\n  // 修改点击处理函数\n  const handleMouseClick = (event, container, sceneInstance, cameraInstance) => {\n    if (!container || !sceneInstance || !cameraInstance) return;\n    const rect = container.getBoundingClientRect();\n    const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n    const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 1;\n    raycaster.params.Line.threshold = 1;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraInstance);\n    const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n    if (intersects.length > 0) {\n      for (let i = 0; i < intersects.length; i++) {\n        const obj = intersects[i].object;\n        if (obj.parent && obj.parent.userData && obj.parent.userData.isEntranceDeviceIcon) {\n          const deviceId = obj.parent.userData.deviceId;\n          console.log('devicesDatahandleMouseClick', devicesData);\n          const device = devicesData.devices.find(d => d.id === deviceId);\n          if (device) {\n            const x = event.clientX;\n            const y = event.clientY;\n            setDevicePopover({\n              visible: true,\n              deviceId,\n              position: { x, y },\n              content: renderDevicePopoverContent(device)\n            });\n            return; // 命中设备图标后直接返回\n          }\n        }\n      }\n    }\n    // ...原有红绿灯弹框逻辑保持不变...\n    // ... existing code ...\n  };\n\n\n\n\n  return (\n    <>\n      <span style={labelStyle}>区域选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择区域位置\"\n        onChange={handleIntersectionChange}\n        onSelect={handleIntersectionChange} // 添加onSelect事件，确保每次选择都触发\n        options={intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{\n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n        value={selectedIntersection ? selectedIntersection.name : undefined}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n\n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div\n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '4px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n            padding: '0',\n            maxWidth: '240px', // 缩小最大宽度\n            fontSize: '12px' // 缩小字体\n          }}\n        >\n          {trafficLightPopover.content}\n          <button\n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '12px',\n              cursor: 'pointer',\n              padding: '2px 6px'\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n\n      <div style={buttonContainerStyle}>\n        <button\n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button\n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n      {devicePopover.visible && (\n        <div\n          style={{\n            position: 'absolute',\n            left: `${devicePopover.position.x}px`,\n            top: `${devicePopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1100,\n            backgroundColor: 'rgba(0, 0, 0, 0.92)',\n            color: 'white',\n            borderRadius: '6px',\n            boxShadow: '0 2px 12px rgba(0,0,0,0.35)',\n            padding: 0,\n            minWidth: 320,\n            maxWidth: 350,\n            fontSize: 13\n          }}\n        >\n          {devicePopover.content}\n          <button\n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '16px',\n              cursor: 'pointer',\n              padding: '2px 10px',\n              zIndex: 1200\n            }}\n            onClick={handleCloseDevicePopover}\n          >×</button>\n        </div>\n      )}\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 12, // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    backgroundColor: parameters.backgroundColor || { r: 255, g: 255, b: 255, a: 0.8 },\n    textColor: parameters.textColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n\n  // 设置字体\n  // context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n\n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n\n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 4 * params.padding + 4 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n\n  canvas.width = width;\n  canvas.height = height;\n\n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n\n  // // 只有在有边框或背景时才绘制背景和边框\n  // if (params.borderThickness > 0 || params.backgroundColor.a > 0) {\n  //   // 绘制背景和边框（圆角矩形）\n  //   const radius = 8;\n  //   context.beginPath();\n  //   context.moveTo(params.borderThickness + radius, params.borderThickness);\n  //   context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  //   context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  //   context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  //   context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  //   context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  //   context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  //   context.lineTo(params.borderThickness, params.borderThickness + radius);\n  //   context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  //   context.closePath();\n\n  //   // 设置背景填充（如果有背景）\n  //   if (params.backgroundColor.a > 0) {\n  //     context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  //     context.fill();\n  //   }\n\n  //   // 设置边框颜色（如果有边框）\n  //   if (params.borderThickness > 0) {\n  //     context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  //     context.lineWidth = params.borderThickness;\n  //     context.stroke();\n  //   }\n  // }\n\n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n\n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n\n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n\n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  // sprite.scale.set(10, 5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n\n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = {\n    text: text,\n    params: params\n  };\n\n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    try {\n      const [ trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([\n        loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`)\n\n    ]);\n\n\n\n    // 处理机动车模型\n    console.log('加载车辆模型...');\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n          color: 0xff0000,  // 0xff0000, //红色 0xffffff,白色\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n      }\n    });\n\n    console.log('加载非机动车模型...');\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    console.log('加载行人模型...');\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n\n      }\n      if (child.isMesh){\n        child.castShadow = true;\n      }\n    });\n\n\n\n    // 保存行人动画数据\n    console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n    if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n      console.log('找到行人动画:', peopleGltf.animations.length, '个');\n      peopleBaseModel = peopleGltf;\n    } else {\n      console.warn('行人模型没有包含动画数据');\n    }\n\n    console.log('加载红绿灯模型...');\n\n    // 处理红绿灯模型\n    preloadedTrafficLightModel = trafficLightGltf.scene;\n    console.log('红绿灯模型：', preloadedTrafficLightModel);\n    // 设置红绿灯模型的缩放\n    preloadedTrafficLightModel.scale.set(6, 6, 6);\n    // 保持原始材质\n    preloadedTrafficLightModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 设置材质属性\n        child.material.metalness = 0.2;\n        child.material.roughness = 0.3;\n        child.material.envMapIntensity = 1.2;\n    }\n  });\n\n    console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n\n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n\n        // // 尝试单独加载红绿灯模型\n        // if (!preloadedTrafficLightModel) {\n        //   console.log('正在单独加载红绿灯模型...');\n        //   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n        //   preloadedTrafficLightModel = trafficLightGltf.scene;\n        //   preloadedTrafficLightModel.scale.set(3, 3, 3);\n        //   console.log('红绿灯模型加载成功');\n        // }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\n// 添加计算两点之间距离的函数\nconst calculateDistance = (x1, y1, x2, y2) => {\n\n  const c = Math.sqrt((x1-x2)*(x1-x2)+(y1-y2)*(y1-y2));\n  return  c;\n};\n\n// 获取事件类型的阈值配置\nconst getEventThresholds = (eventType) => {\n  switch(eventType) {\n    case '910': // 违停车辆\n      return { timeThreshold: 300000, distanceThreshold: 10 }; // 5分钟, 20米\n    case '904': // 逆行车辆\n      return { timeThreshold: 10000, distanceThreshold: 20 }; // 10秒, 20米\n    case '901': // 车辆超速\n      return { timeThreshold: 30000, distanceThreshold: 20 }; // 30秒, 50米\n    case '401': // 道路抛洒物\n    case '404': // 道路障碍物\n    case '1002': // 道路施工\n      return { timeThreshold: 600000, distanceThreshold: 10 }; // 10分钟, 30米\n    case '405': // 行人通过马路\n      return { timeThreshold: 10000, distanceThreshold: 10 }; // 10秒, 10米\n    default:\n      return { timeThreshold: 5000, distanceThreshold: 5 }; // 5秒, 5米\n  }\n};\n\n// 优化后的事件重复检查逻辑\nconst checkDuplicateEvent = (eventType, eventKey, currentTime, currentPos) => {\n  const { timeThreshold, distanceThreshold } = getEventThresholds(eventType);\n\n  // 遍历事件列表缓存中的所有事件\n  for (let i = 0; i < eventListCache.length; i++) {\n    const cachedEvent = eventListCache[i];\n\n    // 检查事件类型是否相同\n    if (cachedEvent.eventType !== eventType) {\n      continue;\n    }\n\n    // 计算时间差\n    const timeDiff = currentTime - cachedEvent.lastUpdateTime;\n\n    // 检查时间差是否在阈值内\n    if (timeDiff > timeThreshold) {\n      continue;\n    }\n\n    // 计算距离\n    const distance = calculateDistance(\n      currentPos.x, currentPos.y,\n      cachedEvent.position.x, cachedEvent.position.y\n    );\n\n    // 检查距离是否在阈值内\n    if (distance <= distanceThreshold) {\n      // 找到匹配的事件，更新信息\n      cachedEvent.eventKey = eventKey;\n      cachedEvent.lastUpdateTime = currentTime;\n      cachedEvent.position = { ...currentPos };\n      cachedEvent.updateCount = (cachedEvent.updateCount || 1) + 1;\n\n      console.log(`🔄 3D场景检测到重复事件 ${eventType} (ID: ${cachedEvent.eventId})，时间差: ${(timeDiff/1000).toFixed(1)}s，距离: ${distance.toFixed(1)}m，更新次数: ${cachedEvent.updateCount}`);\n\n      return {\n        isDuplicate: true,\n        eventId: cachedEvent.eventId,\n        matchedEvent: cachedEvent\n      };\n    }\n  }\n\n  // 没有找到匹配的事件，创建新事件\n  const newEventId = `3D_EVT_${eventIdCounter.toString().padStart(6, '0')}`;\n  eventIdCounter++;\n\n  const newEvent = {\n    eventId: newEventId,\n    eventType: eventType,\n    eventKey: eventKey,\n    firstDetectedTime: currentTime,\n    lastUpdateTime: currentTime,\n    position: { ...currentPos },\n    updateCount: 1\n  };\n\n  // 添加到事件列表缓存\n  eventListCache.push(newEvent);\n\n\n\n  return {\n    isDuplicate: false,\n    eventId: newEventId,\n    newEvent: newEvent\n  };\n};\n\n// 删除2s内没有更新的事件\nconst removeInactiveEvents = () => {\n  const currentTime = Date.now();\n  const inactiveThreshold = 2000; // 2s\n\n  const initialCount = eventListCache.length;\n  const removedEvents = [];\n\n  eventListCache = eventListCache.filter(event => {\n    const timeSinceLastUpdate = currentTime - event.lastUpdateTime;\n    if (timeSinceLastUpdate > inactiveThreshold) {\n      removedEvents.push({\n        id: event.eventId,\n        type: event.eventType,\n        inactiveTime: (timeSinceLastUpdate / 1000).toFixed(1)\n      });\n\n      // 从场景中移除对应的标记\n      const marker = eventMarkers.get(event.eventId);\n      if (marker && scene) {\n        scene.remove(marker);\n        eventMarkers.delete(event.eventId);\n      }\n\n      return false; // 删除该事件\n    }\n    return true; // 保留该事件\n  });\n\n  const removedCount = initialCount - eventListCache.length;\n  if (removedCount > 0) {\n    console.log(`🗑️ 3D场景删除了 ${removedCount} 个1s内未更新的事件:`);\n    removedEvents.forEach(event => {\n      console.log(`   - 事件 ${event.id} (类型: ${event.type})，未更新时间: ${event.inactiveTime}秒`);\n    });\n    console.log(`📊 3D场景当前缓存事件数: ${eventListCache.length}`);\n  }\n};\n\n// 根据事件类型获取背景颜色\nconst getEventBackgroundColor = (eventType) => {\n  switch(eventType) {\n    case '910': // 违停车辆\n      return 0xff4d4f; // 红色背景 - 严重违规\n    case '904': // 逆行车辆\n      return 0xf5222d; // 深红色背景 - 危险行为\n    case '901': // 车辆超速\n      return 0xfa8c16; // 橙色背景 - 警告\n    case '401': // 道路抛洒物\n      return 0xfaad14; // 黄色背景 - 注意\n    case '404': // 道路障碍物\n      return 0xff7a45; // 橙红色背景 - 阻碍\n    case '1002': // 道路施工\n      return 0x1890ff; // 蓝色背景 - 信息\n    case '405': // 行人通过马路\n      return 0x52c41a; // 绿色背景 - 正常\n    case '2': // 交叉路口碰撞预警\n      return 0xff0000; // 纯红色背景 - 紧急\n    case '12': // 交通参与者碰撞预警\n      return 0xeb2f96; // 粉红色背景 - 预警\n    case '13': // 绿波车速引导\n      return 0x13c2c2; // 青色背景 - 引导\n    case '999': // 信号灯优先\n      return 0x722ed1; // 紫色背景 - 优先\n    default:\n      return 0xffa500; // 默认橙黄色背景\n  }\n};\n\n// 获取事件类型的中文名称\nconst getEventTypeName = (eventType) => {\n  switch(eventType) {\n    case '401': return '抛洒物';\n    case '404': return '障碍物';\n    case '405': return '行人';\n    case '904': return '逆行';\n    case '910': return '违停';\n    case '1002': return '施工';\n    case '901': return '超速';\n    case '2': return '碰撞';\n    case '12': return '碰撞';\n    case '13': return '绿波';\n    case '999': return '优先';\n    default: return `事件${eventType}`;\n  }\n};\n\n// 创建事件图标标记\nconst createEventMarker = (eventType, position, eventId) => {\n  if (!scene) {\n    console.warn('无法创建事件标记：场景不存在或已卸载');\n    return null;\n  }\n\n  try {\n    // 创建一个组来包含背景和图标\n    const markerGroup = new THREE.Group();\n\n    // 1. 创建统一的背景平面（包含图标和文字区域，类似截图中的设计）\n    const backgroundGeometry = new THREE.PlaneGeometry(6, 8); // 适当增加宽度和高度以容纳图标和文字\n    const backgroundColor = getEventBackgroundColor(eventType); // 根据事件类型获取背景色\n    const backgroundMaterial = new THREE.MeshBasicMaterial({\n      color: backgroundColor, // 使用事件类型对应的背景颜色\n      transparent: true,\n      opacity: 0.9, // 提高不透明度，使颜色更加鲜明\n      side: THREE.DoubleSide\n    });\n    const backgroundMesh = new THREE.Mesh(backgroundGeometry, backgroundMaterial);\n    backgroundMesh.position.set(0, -1, -0.01); // 向下偏移以居中整个标记，稍微向后作为背景\n    backgroundMesh.renderOrder = 998; // 背景渲染优先级\n    markerGroup.add(backgroundMesh);\n\n    // 2. 创建图标平面（位于背景上方区域）\n    const textureLoader = new THREE.TextureLoader();\n    const iconPath = `${BASE_URL}/images/${eventType}.svg`; // 事件图标路径\n\n    const iconMaterial = new THREE.MeshBasicMaterial({\n      map: textureLoader.load(iconPath,\n        // 加载成功回调\n        (texture) => {\n          console.log(`事件图标加载成功: 事件${eventType}.svg`);\n        },\n        // 加载进度回调\n        undefined,\n        // 加载失败回调\n        (error) => {\n          console.warn(`事件图标加载失败: 事件${eventType}.svg，使用默认图标`);\n          // 可以在这里设置默认图标\n        }\n      ),\n      transparent: true,\n      opacity: 1, // 完全不透明\n      side: THREE.DoubleSide\n    });\n\n    // 创建图标几何体（参考截图中的比例）\n    const iconGeometry = new THREE.PlaneGeometry(5, 5); // 宽度5，高度4，类似截图中的车辆图标比例\n    const iconMesh = new THREE.Mesh(iconGeometry, iconMaterial);\n    iconMesh.position.set(0, 1.5, 0.01); // 图标位于背景上半部分\n    iconMesh.renderOrder = 999; // 图标渲染优先级\n    markerGroup.add(iconMesh);\n\n    // 3. 创建文字标签（显示在图标正下方，参考截图布局）\n    const eventTypeName = getEventTypeName(eventType);\n    const textLabel = createTextSprite(eventTypeName, {\n      backgroundColor: { r: 0, g: 0, b: 0, a: 0.0 }, // 完全透明背景，与图标共用背景\n      textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文字\n      fontSize: 14, // 增大字体以提高可读性\n      padding: 0, // 无填充\n      borderThickness: 0, // 无边框\n      fontWeight: 'bold' // 加粗字体，类似截图效果\n    });\n    textLabel.position.set(0, -2.8, 0.02); // 位于图标正下方，紧贴背景下半部分\n    textLabel.renderOrder = 1000; // 确保在最上层渲染\n    textLabel.scale.set(6, 3, 1); // 调整文字标签的缩放以适应背景\n    markerGroup.add(textLabel);\n\n    // 设置组的位置，高度为15米\n    markerGroup.position.set(position.x, 15, -position.y);\n\n    // 让整个组始终面向相机\n    // markerGroup.lookAt(0, 15, 0);\n// 创建时直接朝向相机，避免角度跳跃\n    // if (cameraRef.current) {\n    //   markerGroup.lookAt(cameraRef.current.position.x, markerGroup.position.y, cameraRef.current.position.z);\n    // } else {\n    //   markerGroup.lookAt(0, markerGroup.position.y, 0);\n    // }\n    // 设置渲染优先级，与设备图标保持一致\n    markerGroup.renderOrder = 999;\n\n    // 添加用户数据到组\n    markerGroup.userData = {\n      type: 'eventMarker',\n      eventId: eventId,\n      eventType: eventType\n    };\n\n    // 添加到场景\n    scene.add(markerGroup);\n\n    // 存储标记引用\n    eventMarkers.set(eventId, markerGroup);\n\n    console.log(`📍 创建事件标记 ${eventType} (${eventTypeName}) (ID: ${eventId})，位置: (${position.x.toFixed(2)}, ${position.y.toFixed(2)})，背景色: #${backgroundColor.toString(16)}`);\n\n    return markerGroup;\n  } catch (error) {\n    console.error('创建事件标记时出错:', error);\n    return null;\n  }\n};\n\n// 更新事件标记位置\nconst updateEventMarkerPosition = (eventId, newPosition) => {\n  const markerGroup = eventMarkers.get(eventId);\n  if (markerGroup && scene) {\n    markerGroup.position.set(newPosition.x, 15, -newPosition.y);\n    // 重新设置朝向相机\n    // markerGroup.lookAt(0, 15, 0);\n    console.log(`📍 更新事件标记位置 (ID: ${eventId})，新位置: (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)})`);\n  }\n};\n\nconst showWarningMarker = (position, text, color, eventType = '999', eventData = {}) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n\n  try {\n    // 生成事件Key用于去重\n    const currentTime = Date.now();\n    const eventKey = `${eventData.rsuId || 'UNKNOWN'}_${eventType}_${position.x.toFixed(0)}_${position.y.toFixed(0)}`;\n\n    // 转换模型坐标为经纬度（用于距离计算）\n    // const converter = new CoordinateConverter();\n    // const wgs84Pos = converter.modelToWgs84(position.x, position.y);\n\n    // 检查事件去重\n    const duplicateResult = checkDuplicateEvent(\n      eventType,\n      eventKey,\n      currentTime,\n      position\n    );\n\n    const isDuplicate = duplicateResult.isDuplicate;\n\n    console.log(`🔍 3D场景事件去重检查 - 类型: ${eventType}, EventKey: ${eventKey}, 位置: (${position.x}, ${position.y}), 结果: ${isDuplicate ? '重复' : '新事件'}`);\n\n    if (isDuplicate) {\n      // 重复事件，更新现有标记位置\n      updateEventMarkerPosition(duplicateResult.eventId, position);\n    } else {\n      // 新事件，创建新的标记\n      createEventMarker(eventType, position, duplicateResult.eventId);\n    }\n\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance, intersections) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n\n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n\n  // 检查红绿灯模型是否已加载\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载，尝试重新加载...');\n    // 尝试重新加载红绿灯模型\n    const loader = new GLTFLoader();\n    loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)\n      .then(trafficLightGltf => {\n        preloadedTrafficLightModel = trafficLightGltf.scene;\n        preloadedTrafficLightModel.scale.set(6, 6, 6);\n        preloadedTrafficLightModel.traverse((child) => {\n          if (child.isMesh && child.material) {\n            child.material.metalness = 0.2;\n            child.material.roughness = 0.3;\n            child.material.envMapIntensity = 1.2;\n          }\n        });\n        console.log('红绿灯模型重新加载成功，开始创建红绿灯...');\n        // 重新调用创建函数\n        createTrafficLights(converterInstance, intersections);\n      })\n      .catch(error => {\n        console.error('红绿灯模型重新加载失败:', error);\n        // 如果加载失败，使用简单的替代物体\n        createFallbackTrafficLights(converterInstance, intersections);\n      });\n    return;\n  }\n\n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersections.forEach(intersection => {\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n\n      try {\n        // 确保模型存在且可以克隆\n        if (!preloadedTrafficLightModel || !preloadedTrafficLightModel.clone) {\n          throw new Error('红绿灯模型无效或无法克隆');\n        }\n\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n\n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n\n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n\n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n\n        // 设置材质属性\n        trafficLightModel.traverse(child => {\n          if (child.isMesh) {\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            child.renderOrder = 100;\n          }\n        });\n\n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n\n        // 将红绿灯添加到场景中\n        // scene.add(trafficLightModel);\n\n        // ========== 新增：在红绿灯地面添加指北针图标 =============\n        // 创建一个平面用于显示指北针SVG图标\n        const compassTextureLoader = new THREE.TextureLoader();\n        const compassIconPath = `${BASE_URL}/images/compass.svg`; // public目录下的路径\n        const compassMaterial = new THREE.MeshBasicMaterial({\n          map: compassTextureLoader.load(compassIconPath),\n          transparent: true,\n          opacity: 1\n        });\n        // 平面几何体，5x5米\n        const compassGeometry = new THREE.PlaneGeometry(5, 5);\n        const compassMesh = new THREE.Mesh(compassGeometry, compassMaterial);\n        // 指北针放在红绿灯正下方地面（y=0.1，略高于地面防止Z冲突）\n        compassMesh.position.set(modelPos.x+20, 1.5, -(modelPos.y+20));\n        // 指北针朝上，旋转x轴-90度\n        compassMesh.rotation.x = -Math.PI / 2;\n        // 渲染优先级高，避免被地面遮挡\n        compassMesh.renderOrder = 101;\n        // 可选：添加userData标记\n        compassMesh.userData = {\n          type: 'compassIcon',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        // 添加到场景\n        scene.add(compassMesh);\n        // ========== 指北针图标添加结束 =============\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n\n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        // createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n\n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance, intersections) => {\n  intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({\n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n\n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n\n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n\n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,  // 完全透明\n    depthWrite: false\n  });\n\n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n\n  trafficLightModel.add(collider);\n\n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n\n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n\n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n\n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  trafficLightModel.add(lightMesh);\n\n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  switch(phaseId) {\n    case '1': return '北进口左转';\n    case '2': return '北进口直行';\n    case '3': return '北进口右转';\n    case '5': return '东进口左转';\n    case '6': return '东进口直行';\n    case '7': return '东进口右转';\n    case '9': return '南进口左转';\n    case '10': return '南进口直行';\n    case '11': return '南进口右转';\n    case '13': return '西进口左转';\n    case '14': return '西进口直行';\n    case '15': return '西进口右转';\n    default: return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = (dirCode) => {\n  switch(dirCode) {\n    case 'N': return '北向南';\n    case 'S': return '南向北';\n    case 'E': return '东向西';\n    case 'W': return '西向东';\n    case 'NE': return '东北向西南';\n    case 'NW': return '西北向东南';\n    case 'SE': return '东南向西北';\n    case 'SW': return '西南向东北';\n    default: return `方向${dirCode}`;\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n\n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n\n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({\n    ...prev,\n    visible: false,\n    content: null, // 清空内容\n    phases: []     // 清空相位信息\n  }));\n\n  console.log('弹窗已关闭，所有相关资源已清理');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = (interId) => {\n  try {\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n\n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n\n      return false;\n    }\n\n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n\n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 创建弹出窗口内容\n    let content;\n\n    if (stateInfo && stateInfo.phases) {\n      content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection.name} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              switch (phase.trafficLight) {\n                case 'G': lightColor = '#00ff00'; break;\n                case 'Y': lightColor = '#ffff00'; break;\n                case 'R': default: lightColor = '#ff0000'; break;\n              }\n\n              return (\n                <div key={index} style={{\n                  marginBottom: '6px',\n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {getPhaseDirection(phase.phaseId)}\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{\n                      color: lightColor,\n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      content = (\n        <div style={{ padding: '8px', maxWidth: '200px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>\n          <div>路口ID: {interId}</div>\n          <div>当前无信号灯状态信息</div>\n        </div>\n      );\n    }\n\n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2 -500;\n    const centerY = window.innerHeight / 2 -500;\n\n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;\n\n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: { x: centerX, y: centerY },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n\n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n\n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n\n      document.body.appendChild(popover);\n\n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n\n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n\n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n\n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n\n  return list;\n};\n\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = (interId) => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n\n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n\n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n\n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 判断是否有相位数据\n    const hasPhaseData = stateInfo && stateInfo.phases && stateInfo.phases.length > 0;\n\n    let content;\n\n    // 指北针样式\n    const compassStyle = {\n      position: 'absolute',\n      top: '5px',\n      right: '25px',\n      width: '30px',\n      height: '30px',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      borderRadius: '50%',\n      background: 'rgba(0,0,0,0.1)',\n      zIndex: 10\n    };\n\n    // 指北针组件\n    const CompassIcon = () => (\n      <div style={compassStyle}>\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          transform: 'rotate(0deg)'\n        }}>\n          <span style={{\n            color: '#ff5252',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            lineHeight: '14px'\n          }}>N</span>\n          <span style={{\n            width: 0,\n            height: 0,\n            borderLeft: '6px solid transparent',\n            borderRight: '6px solid transparent',\n            borderBottom: '10px solid #ff5252',\n            marginTop: '-2px'\n          }}></span>\n        </div>\n      </div>\n    );\n\n    if (hasPhaseData) {\n      // 相位ID与方向/方式的映射\n      const phaseMap = {\n        '1': { dir: 'N', type: 'left' }, '2': { dir: 'N', type: 'straight' }, '3': { dir: 'N', type: 'right' },\n        '5': { dir: 'E', type: 'left' }, '6': { dir: 'E', type: 'straight' }, '7': { dir: 'E', type: 'right' },\n        '9': { dir: 'S', type: 'left' }, '10': { dir: 'S', type: 'straight' }, '11': { dir: 'S', type: 'right' },\n        '13': { dir: 'W', type: 'left' }, '14': { dir: 'W', type: 'straight' }, '15': { dir: 'W', type: 'right' }\n      };\n\n      const typeOrder = ['left', 'straight', 'right'];\n      const colorMap = { G: '#00ff00', Y: '#ffff00', R: '#ff0000' };\n      const dirData = { N: {}, E: {}, S: {}, W: {} };\n\n      stateInfo.phases.forEach(phase => {\n        const map = phaseMap[phase.phaseId];\n        if (map) {\n          dirData[map.dir][map.type] = {\n            color: colorMap[phase.trafficLight] || '#888',\n            remainTime: phase.remainTime\n          };\n        }\n      });\n\n      content = (\n        <div style={{ padding: '8px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>\n          <CompassIcon />\n          <div style={{ fontWeight: 'bold', marginBottom: '8px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}灯态</div>\n          <div style={{\n            display: 'grid',\n            gridTemplateRows: '60px 60px 60px',\n            gridTemplateColumns: '60px 60px 60px',\n            justifyContent: 'center',\n            alignItems: 'center',\n            background: 'rgba(255,255,255,0.05)',\n            borderRadius: '8px',\n            margin: '0 auto',\n            position: 'relative'\n          }}>\n            {/* 北 - 左转、直行、右转箭头水平对齐，倒计时在箭头上面 */}\n            {/* <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%', paddingLeft: '5px' }}> */}\n            <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}>\n              {typeOrder.map((type, index) => {\n                // 反转左转和右转在数组中的顺序\n                let displayIndex = index;\n                if (index === 0) displayIndex = 2;\n                else if (index === 2) displayIndex = 0;\n\n                const currentType = typeOrder[displayIndex];\n\n                // 计算与南边对齐的样式\n                const marginStyle = {};\n                if (currentType === 'left') { // 左转箭头 (右侧显示)\n                  marginStyle.marginRight = '0px';\n                } else if (currentType === 'straight') { // 直行箭头 (中间显示)\n                  marginStyle.marginLeft = '10px';\n                  marginStyle.marginRight = '10px';\n                } else if (currentType === 'right') { // 右转箭头 (左侧显示)\n                  marginStyle.marginLeft = '0px';\n                }\n\n                return dirData.N[currentType] && (\n                  // <div key={currentType} style={{\n                  //   display: 'flex',\n                  //   flexDirection: 'column',\n                  //   alignItems: 'center',\n                  //   ...marginStyle\n                  // }}>\n                  <div key={currentType} style={{marginRight: currentType === 'left' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>\n                    <div style={{fontSize:'14px', color: dirData.N[currentType].color, fontWeight:'bold', marginBottom: '3px'}}>{dirData.N[currentType].remainTime}</div>\n                    <span style={{color: dirData.N[currentType].color, fontSize:'20px', lineHeight: '20px'}}>\n                      {currentType === 'left' ? '\\u{1F87A}' : currentType === 'straight' ? '\\u{1F87B}' : '\\u{1F878}'}\n                    </span>\n                  </div>\n                );\n              })}\n            </div>\n\n            {/* 南 - 左转、直行、右转箭头水平对齐，倒计时在箭头下面 */}\n            <div style={{ gridRow: 3, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}>\n              {typeOrder.map(type => dirData.S[type] && (\n                <div key={type} style={{marginRight: type === 'right' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>\n                  <span style={{color: dirData.S[type].color, fontSize:'20px', lineHeight: '20px'}}>\n                    {type === 'left' ? '\\u{1F878}' : type === 'straight' ? '\\u{1F879}' : '\\u{1F87A}'}\n                  </span>\n                  <div style={{fontSize:'14px', color: dirData.S[type].color, fontWeight:'bold', marginTop: '3px'}}>{dirData.S[type].remainTime}</div>\n                </div>\n              ))}\n            </div>\n\n            {/* 东 - 倒计时显示在箭头右边 */}\n\n            <div style={{ gridRow: 2, gridColumn: 3, textAlign: 'center' }}>\n              {typeOrder.map((type, index) => {\n                // 反转左转和右转在数组中的顺序\n                let displayIndex = index;\n                if (index === 0) displayIndex = 2;\n                else if (index === 2) displayIndex = 0;\n\n                const currentType = typeOrder[displayIndex];\n\n                return dirData.E[currentType] && (\n                  <div key={currentType} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-start'}}>\n                    <span style={{color: dirData.E[currentType].color, fontSize:'20px', lineHeight: '20px'}}>\n                      {currentType === 'left' ? '\\u{1F87B}' : currentType === 'straight' ? '\\u{1F878}' : '\\u{1F879}'}\n                    </span>\n                    <div style={{\n                      fontSize:'14px',\n                      color: dirData.E[currentType].color,\n                      fontWeight:'bold',\n                      marginLeft: '5px'\n                    }}>{dirData.E[currentType].remainTime}</div>\n                  </div>\n                );\n              })}\n            </div>\n\n            {/* 西 - 倒计时显示在箭头左边 */}\n            <div style={{ gridRow: 2, gridColumn: 1, textAlign: 'center' }}>\n              {typeOrder.map(type => dirData.W[type] && (\n                <div key={type} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}>\n                  <div style={{\n                    fontSize:'14px',\n                    color: dirData.W[type].color,\n                    fontWeight:'bold',\n                    marginRight: '5px'\n                  }}>{dirData.W[type].remainTime}</div>\n                  <span style={{color: dirData.W[type].color, fontSize:'20px', lineHeight: '20px'}}>\n                    {type === 'left' ? '\\u{1F879}' : type === 'straight' ? '\\u{1F87A}' : '\\u{1F87B}'}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n          <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      // 没有相位数据时显示的内容\n      content = (\n        <div style={{ padding: '15px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>\n          <CompassIcon />\n          <div style={{ fontWeight: 'bold', marginBottom: '10px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}</div>\n          <div style={{\n            textAlign: 'center',\n            padding: '20px 0',\n            color: '#ff9800',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            background: 'rgba(255,255,255,0.1)',\n            borderRadius: '8px',\n            marginBottom: '10px'\n          }}>\n            当前无信号灯状态信息\n          </div>\n          <div style={{ fontSize: '12px', color: '#888', textAlign: 'center' }}>\n            路口ID: {interId}\n          </div>\n          <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    }\n\n    // 设置弹窗位置在左上角区域\n    const x = 445;\n    const y = 250;\n\n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n\n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n\n    // 更新弹窗状态\n    if (window._setTrafficLightPopover) {\n      window._setTrafficLightPopover({\n        visible: true,\n        interId: interId,\n        position: { x, y },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n\n      // 设置定时更新\n      if (window.trafficLightUpdateTimerRef) {\n        window.trafficLightUpdateTimerRef.current = setInterval(() => {\n          window.showTrafficLightPopup(interId);\n        }, 1000);\n      }\n\n      return true;\n    } else {\n      console.error('无法找到setTrafficLightPopover函数');\n      return false;\n    }\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n\n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n\n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n\n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n\n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n\n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n\n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n\n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n\n    console.log('归一化坐标:', mouseX, mouseY);\n\n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n\n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n\n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n\n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称',\n                    '距离:', intersect.distance,\n                    'position:', intersect.object.position.toArray(),\n                    'userData:', intersect.object.userData);\n\n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n\n      return true;\n    }\n\n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n\n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称',\n                  '类型:', obj.type,\n                  '位置:', obj.position.toArray(),\n                  '距离:', intersect.distance,\n                  'userData:', obj.userData);\n    });\n\n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n\n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n\n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n\n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n\n        if (isVisible) {\n          visibleCount++;\n        }\n\n        console.log(`红绿灯 ${interId}:`, {\n          名称: lightObj.intersection?.name || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n\n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n\n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n\n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch(phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n\n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: trafficLight.intersection?.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n\n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = { isLight: true };\n\n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n\n  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\n\nexport default CampusModel;\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAO,KAAKC,aAAa,MAAM,2CAA2C;AAG1E,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACtC,OAAOC,KAAK,MAAM,OAAO,CAAC,CAAC;AAC3B,OAAOC,WAAW,MAAM,eAAe,CAAC,CAAC;AACzC,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,0BAA0B,GAAG,IAAI,CAAC,CAAC;AACvC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB,IAAIC,eAAe,GAAE,IAAI,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAE,IAAI;;AAElB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAEpB;AACA,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,MAAMC,oBAAoB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;;AAKxC;AACA,MAAME,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACV;EACEC,GAAG,EAAE,2BAA2B;EAChCC,GAAG,EAAE,2BAA2B;EAChChB,KAAK,EAAE,6BAA6B;EACtCiB,GAAG,EAAE,2BAA2B;EAAG;EACnCC,IAAI,EAAE,4BAA4B,CAAE;AACtC,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AACzEC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;;AAE1D;AACA,MAAMG,aAAa,GAAG,IAAIlB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,IAAImB,gBAAgB,GAAG,IAAInB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAElC;AACA,IAAIoB,gBAAgB,GAAG,IAAI;;AAE3B;AACA,IAAIC,gBAAgB,GAAG,IAAIrB,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,IAAIsB,kBAAkB,GAAG,IAAItB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEpC,MAAMuB,KAAK,GAAG,IAAI3D,KAAK,CAAC4D,KAAK,CAAC,CAAC;;AAE/B;AACA,MAAMC,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGf,QAAQ,oBAAoB,CAAC;IAC7D,MAAMgB,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,IAAIA,IAAI,CAACE,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;MACzD,MAAMG,WAAW,GAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,IAAI,CAAC;MACrE,IAAIH,WAAW,IAAIA,WAAW,CAACI,KAAK,EAAE;QACpCjB,gBAAgB,GAAGa,WAAW,CAACI,KAAK;QACpCrB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEG,gBAAgB,CAAC;QAC7C,OAAOA,gBAAgB;MACzB;IACF;IACAJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChCG,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjClB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB;AACF,CAAC;;AAED;AACA,MAAMmB,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;EAC5C;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAI,CAACjD,YAAY,EAAE;MACjBA,YAAY,GAAGgD,MAAM,CAACE,KAAK,CAAC,CAAC;MAC7B,OAAOF,MAAM;IACf;IAEA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEpD,YAAY,CAACoD,CAAC,EAAElD,KAAK,CAAC;IAChE,MAAMmD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEtD,YAAY,CAACsD,CAAC,EAAEpD,KAAK,CAAC;IAChE,MAAMqD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAExD,YAAY,CAACwD,CAAC,EAAEtD,KAAK,CAAC;IAEhEF,YAAY,CAACyD,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;IACjD,OAAOvD,YAAY,CAACkD,KAAK,CAAC,CAAC;EAC3B;;EAEA;EACA,IAAI,CAAC/C,oBAAoB,CAACuD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC9C,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;EAEA,MAAMW,OAAO,GAAGxD,oBAAoB,CAACyD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,MAAMY,QAAQ,GAAGF,OAAO,CAACG,UAAU,CAACd,MAAM,CAAC;EAC3C,MAAMe,sBAAsB,GAAG,EAAE,CAAC,CAAC;;EAEnC,IAAIF,QAAQ,GAAGE,sBAAsB,EAAE;IACrC3C,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAUY,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAClE7D,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;;EAEA;EACA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEO,OAAO,CAACP,CAAC,EAAElD,KAAK,CAAC;EAC3D,MAAMmD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEK,OAAO,CAACL,CAAC,EAAEpD,KAAK,CAAC;EAC3D,MAAMqD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAEG,OAAO,CAACH,CAAC,EAAEtD,KAAK,CAAC;EAE3D,MAAM+D,WAAW,GAAG,IAAIjG,KAAK,CAACkG,OAAO,CAACf,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACtEpD,oBAAoB,CAACsD,GAAG,CAACR,SAAS,EAAEgB,WAAW,CAACf,KAAK,CAAC,CAAC,CAAC;EAExD,OAAOe,WAAW;AACpB,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,WAAW,EAAEnB,SAAS,KAAK;EACjD;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAIhD,YAAY,KAAK,IAAI,EAAE;MACzBA,YAAY,GAAGmE,WAAW;MAC1B,OAAOA,WAAW;IACpB;;IAEA;IACA,IAAIC,IAAI,GAAGD,WAAW,GAAGnE,YAAY;IACrC,IAAIoE,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IAExC,MAAMC,gBAAgB,GAAG7B,aAAa,CAAC1C,YAAY,GAAGoE,IAAI,EAAEpE,YAAY,EAAEC,KAAK,CAAC;IAChFD,YAAY,GAAGuE,gBAAgB;IAC7B,OAAOA,gBAAgB;EACzB;;EAEA;EACA,IAAI,CAACnE,oBAAoB,CAACqD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC5C,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMK,OAAO,GAAGpE,oBAAoB,CAACuD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,IAAIoB,IAAI,GAAGD,WAAW,GAAGK,OAAO;EAChC,IAAIJ,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;;EAExC;EACA,MAAMG,mBAAmB,GAAGJ,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,CAAC;EACzC,IAAID,IAAI,CAACK,GAAG,CAACN,IAAI,CAAC,GAAGK,mBAAmB,EAAE;IACxCtD,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAU,CAACoB,IAAI,GAAG,GAAG,GAAGC,IAAI,CAACC,EAAE,EAAEP,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAChF3D,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMI,gBAAgB,GAAG7B,aAAa,CAAC8B,OAAO,GAAGJ,IAAI,EAAEI,OAAO,EAAEvE,KAAK,CAAC;EACtEG,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEuB,gBAAgB,CAAC;EAErD,OAAOA,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA,MAAMI,6BAA6B,GAAG,CAAC,CAAC,CAAC;AACzC;;AAEA;AACA,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;AACjC,MAAMC,QAAQ,GAAG,IAAI3E,GAAG,CAAC,CAAC;;AAE1B;AACA,MAAM4E,eAAe,GAAG;EACtBC,MAAM,EAAE,IAAIH,GAAG,CAAC,CAAC;EACjBI,KAAK,EAAE,IAAI9E,GAAG,CAAC,CAAC;EAChB+E,OAAO,EAAE,IAAI/E,GAAG,CAAC,CAAC;EAClBgF,MAAM,EAAE,IAAIN,GAAG,CAAC,CAAC;EAEjBO,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACrB,IAAI,CAACN,MAAM,CAACO,GAAG,CAACF,KAAK,CAAC;IACtB,IAAIC,KAAK,EAAE;MACT,IAAI,CAACH,MAAM,CAACI,GAAG,CAACD,KAAK,CAAC;MACtB;MACAA,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAI;QACvB,IAAIA,MAAM,CAACC,MAAM,EAAE;UACjB,IAAI,CAACT,KAAK,CAACzB,GAAG,CAACiC,MAAM,CAACE,IAAI,EAAEF,MAAM,CAAC;QACrC;MACF,CAAC,CAAC;IACJ;IACA,OAAOJ,KAAK;EACd,CAAC;EAEDO,SAASA,CAACC,MAAM,EAAER,KAAK,EAAE;IACvB,IAAI,CAAC,IAAI,CAACH,OAAO,CAACzB,GAAG,CAAC4B,KAAK,CAAC,EAAE;MAC5B,IAAI,CAACH,OAAO,CAAC1B,GAAG,CAAC6B,KAAK,EAAE,IAAIR,GAAG,CAAC,CAAC,CAAC;IACpC;IACA,IAAI,CAACK,OAAO,CAACvB,GAAG,CAAC0B,KAAK,CAAC,CAACE,GAAG,CAACM,MAAM,CAAC;IACnC,OAAOA,MAAM;EACf,CAAC;EAEDC,WAAWA,CAACT,KAAK,EAAE;IACjB,IAAI,IAAI,CAACL,MAAM,CAACvB,GAAG,CAAC4B,KAAK,CAAC,EAAE;MAC1B,IAAI;QACF;QACA,IAAI,IAAI,CAACH,OAAO,CAACzB,GAAG,CAAC4B,KAAK,CAAC,EAAE;UAC3B,IAAI,CAACH,OAAO,CAACvB,GAAG,CAAC0B,KAAK,CAAC,CAACU,OAAO,CAACF,MAAM,IAAI;YACxC,IAAIA,MAAM,IAAI,OAAOA,MAAM,CAACG,IAAI,KAAK,UAAU,EAAE;cAC/CH,MAAM,CAACG,IAAI,CAAC,CAAC;YACf;UACF,CAAC,CAAC;UACF,IAAI,CAACd,OAAO,CAACe,MAAM,CAACZ,KAAK,CAAC;QAC5B;;QAEA;QACA,IAAI,OAAOA,KAAK,CAACa,aAAa,KAAK,UAAU,EAAE;UAC7Cb,KAAK,CAACa,aAAa,CAAC,CAAC;QACvB;;QAEA;QACA,MAAMC,IAAI,GAAGd,KAAK,CAACe,OAAO,CAAC,CAAC;QAC5B,IAAID,IAAI,EAAE;UACR,IAAI,CAAChB,MAAM,CAACc,MAAM,CAACE,IAAI,CAAC;UACxBA,IAAI,CAACX,QAAQ,CAACC,MAAM,IAAI;YACtB,IAAIA,MAAM,IAAIA,MAAM,CAACC,MAAM,EAAE;cAC3B,IAAI,CAACT,KAAK,CAACgB,MAAM,CAACR,MAAM,CAACE,IAAI,CAAC;YAChC;YACA,IAAIF,MAAM,IAAIA,MAAM,CAACY,UAAU,EAAE;cAC/BZ,MAAM,CAACY,UAAU,CAACC,MAAM,GAAG,CAAC;YAC9B;UACF,CAAC,CAAC;;UAEF;UACA,IAAI;YACF,IAAI,OAAOjB,KAAK,CAACkB,WAAW,KAAK,UAAU,EAAE;cAC3ClB,KAAK,CAACkB,WAAW,CAACJ,IAAI,CAAC;YACzB;YAEA,IAAI,OAAOd,KAAK,CAACmB,aAAa,KAAK,UAAU,EAAE;cAC7CnB,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAEL,IAAI,CAAC;YACjC;;YAEA;YACA,IAAI,OAAOd,KAAK,CAACoB,WAAW,KAAK,UAAU,EAAE;cAC3C;cACA,MAAMC,KAAK,GAAGrB,KAAK,CAACsB,QAAQ,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;cACnEN,KAAK,CAACX,OAAO,CAACkB,IAAI,IAAI;gBACpB,IAAIA,IAAI,IAAIA,IAAI,CAACtB,IAAI,EAAE;kBACrBN,KAAK,CAACoB,WAAW,CAACQ,IAAI,CAAC;gBACzB;cACF,CAAC,CAAC;YACJ;UACF,CAAC,CAAC,OAAOC,CAAC,EAAE;YACV/F,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE8F,CAAC,CAAC;YACnC;UACF;QACF;QAEA,IAAI,CAAClC,MAAM,CAACiB,MAAM,CAACZ,KAAK,CAAC;MAC3B,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrC;QACA,IAAI,CAACuC,MAAM,CAACiB,MAAM,CAACZ,KAAK,CAAC;MAC3B;IACF;EACF,CAAC;EAED8B,OAAOA,CAAA,EAAG;IACR,IAAI;MACF;MACA,IAAI,CAACjC,OAAO,CAACa,OAAO,CAAC,CAACb,OAAO,EAAEG,KAAK,KAAK;QACvC,IAAI;UACFH,OAAO,CAACa,OAAO,CAACF,MAAM,IAAI;YACxB,IAAIA,MAAM,IAAI,OAAOA,MAAM,CAACG,IAAI,KAAK,UAAU,EAAE;cAC/CH,MAAM,CAACG,IAAI,CAAC,CAAC;YACf;UACF,CAAC,CAAC;UACFd,OAAO,CAACkC,KAAK,CAAC,CAAC;QACjB,CAAC,CAAC,OAAOF,CAAC,EAAE;UACV/F,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE8F,CAAC,CAAC;QACjC;MACF,CAAC,CAAC;MACF,IAAI,CAAChC,OAAO,CAACkC,KAAK,CAAC,CAAC;;MAEpB;MACA,IAAI,CAACpC,MAAM,CAACe,OAAO,CAACV,KAAK,IAAI;QAC3B,IAAI;UACF,IAAI,OAAOA,KAAK,CAACa,aAAa,KAAK,UAAU,EAAE;YAC7Cb,KAAK,CAACa,aAAa,CAAC,CAAC;UACvB;UAEA,MAAMC,IAAI,GAAGd,KAAK,CAACe,OAAO,CAAC,CAAC;UAC5B,IAAID,IAAI,EAAE;YACRA,IAAI,CAACX,QAAQ,CAACC,MAAM,IAAI;cACtB,IAAIA,MAAM,IAAIA,MAAM,CAACY,UAAU,EAAE;gBAC/BZ,MAAM,CAACY,UAAU,CAACC,MAAM,GAAG,CAAC;cAC9B;YACF,CAAC,CAAC;;YAEF;YACA,IAAI,OAAOjB,KAAK,CAACkB,WAAW,KAAK,UAAU,EAAE;cAC3ClB,KAAK,CAACkB,WAAW,CAACJ,IAAI,CAAC;YACzB;YAEA,IAAI,OAAOd,KAAK,CAACmB,aAAa,KAAK,UAAU,EAAE;cAC7CnB,KAAK,CAACmB,aAAa,CAAC,IAAI,EAAEL,IAAI,CAAC;YACjC;;YAEA;YACA,IAAI;cACF,IAAId,KAAK,CAACsB,QAAQ,IAAIzE,KAAK,CAACC,OAAO,CAACkD,KAAK,CAACsB,QAAQ,CAAC,EAAE;gBACnD,MAAMD,KAAK,GAAGrB,KAAK,CAACsB,QAAQ,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;gBACnEN,KAAK,CAACX,OAAO,CAACkB,IAAI,IAAI;kBACpB,IAAIA,IAAI,IAAIA,IAAI,CAACtB,IAAI,IAAI,OAAON,KAAK,CAACoB,WAAW,KAAK,UAAU,EAAE;oBAChEpB,KAAK,CAACoB,WAAW,CAACQ,IAAI,CAAC;kBACzB;gBACF,CAAC,CAAC;cACJ;YACF,CAAC,CAAC,OAAOC,CAAC,EAAE;cACV/F,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE8F,CAAC,CAAC;YACnC;UACF;QACF,CAAC,CAAC,OAAOA,CAAC,EAAE;UACV/F,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE8F,CAAC,CAAC;QAClC;MACF,CAAC,CAAC;MACF,IAAI,CAAClC,MAAM,CAACoC,KAAK,CAAC,CAAC;;MAEnB;MACA,IAAI,CAACnC,KAAK,CAACc,OAAO,CAACsB,IAAI,IAAI;QACzB,IAAIA,IAAI,CAACC,MAAM,EAAE;UACfD,IAAI,CAACC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAC;QAC1B;QACA,IAAIA,IAAI,CAACG,MAAM,EAAEH,IAAI,CAACG,MAAM,CAACC,QAAQ,CAAC,CAAC;QACvC,IAAIJ,IAAI,CAACK,WAAW,EAAEL,IAAI,CAACK,WAAW,CAACD,QAAQ,CAAC,CAAC;MACnD,CAAC,CAAC;MACF,IAAI,CAACxC,KAAK,CAACmC,KAAK,CAAC,CAAC;;MAElB;MACA,IAAI,CAACjC,MAAM,CAACY,OAAO,CAACT,KAAK,IAAI;QAC3B,IAAIA,KAAK,CAACgC,MAAM,EAAE;UAChBhC,KAAK,CAACgC,MAAM,CAACC,MAAM,CAACjC,KAAK,CAAC;QAC5B;QACAA,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAI;UACvB,IAAIA,MAAM,CAACkC,MAAM,EAAE;YACjB,IAAIlC,MAAM,CAACmC,QAAQ,EAAE;cACnBnC,MAAM,CAACmC,QAAQ,CAACC,OAAO,CAAC,CAAC;YAC3B;YACA,IAAIpC,MAAM,CAACqC,QAAQ,EAAE;cACnB,IAAI5F,KAAK,CAACC,OAAO,CAACsD,MAAM,CAACqC,QAAQ,CAAC,EAAE;gBAClCrC,MAAM,CAACqC,QAAQ,CAAC/B,OAAO,CAAC+B,QAAQ,IAAI;kBAClC,IAAIA,QAAQ,CAAClB,GAAG,EAAEkB,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;kBACxCC,QAAQ,CAACD,OAAO,CAAC,CAAC;gBACpB,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL,IAAIpC,MAAM,CAACqC,QAAQ,CAAClB,GAAG,EAAEnB,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;gBACtDpC,MAAM,CAACqC,QAAQ,CAACD,OAAO,CAAC,CAAC;cAC3B;YACF;UACF;UACA,IAAIpC,MAAM,CAACY,UAAU,EAAE;YACrBZ,MAAM,CAACY,UAAU,CAACC,MAAM,GAAG,CAAC;UAC9B;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAACnB,MAAM,CAACiC,KAAK,CAAC,CAAC;IACrB,CAAC,CAAC,OAAO3E,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC;MACA,IAAI,CAACyC,OAAO,CAACkC,KAAK,CAAC,CAAC;MACpB,IAAI,CAACpC,MAAM,CAACoC,KAAK,CAAC,CAAC;MACnB,IAAI,CAACnC,KAAK,CAACmC,KAAK,CAAC,CAAC;MAClB,IAAI,CAACjC,MAAM,CAACiC,KAAK,CAAC,CAAC;IACrB;EACF;AACF,CAAC;;AAED;AACA,MAAMW,oBAAoB,GAAIzC,KAAK,IAAK;EACtC,MAAMD,KAAK,GAAG,IAAItH,KAAK,CAACiK,cAAc,CAAC1C,KAAK,CAAC;EAC7C,OAAOP,eAAe,CAACK,QAAQ,CAACC,KAAK,EAAEC,KAAK,CAAC;AAC/C,CAAC;;AAED;AACA,MAAM2C,YAAY,GAAGA,CAAChB,IAAI,EAAE5B,KAAK,EAAEC,KAAK,KAAK;EAC3C,MAAMO,MAAM,GAAGR,KAAK,CAAC6C,UAAU,CAACjB,IAAI,EAAE3B,KAAK,CAAC;EAC5C,OAAOP,eAAe,CAACa,SAAS,CAACC,MAAM,EAAER,KAAK,CAAC;AACjD,CAAC;;AAED;AACA,MAAM8C,mBAAmB,GAAG,IAAItD,GAAG,CAAC,CAAC;;AAErC;AACA,IAAIuD,cAAc,GAAG,EAAE,CAAC,CAAC;AACzB,IAAIC,cAAc,GAAG,CAAC,CAAC,CAAC;AACxB,IAAIC,YAAY,GAAG,IAAInI,GAAG,CAAC,CAAC,CAAC,CAAC;;AAE9B,MAAMoI,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC,kBAAkB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAMC,YAAY,GAAGhL,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMiL,UAAU,GAAGjL,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMkL,SAAS,GAAGlL,MAAM,CAAC,IAAIM,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAM6K,aAAa,GAAGnL,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMoL,eAAe,GAAGpL,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMqL,aAAa,GAAGrL,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMsL,iBAAiB,GAAGtL,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMuL,MAAM,GAAGvL,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAMwL,kBAAkB,GAAGxL,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMyL,gBAAgB,GAAGzL,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM0L,eAAe,GAAG,IAAI,CAAC,CAAC;;EAE9B;EACA,MAAMC,oBAAoB,GAAG3L,MAAM,CAAC4L,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC/C,MAAMC,qBAAqB,GAAG,IAAIvJ,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC,MAAMwJ,gBAAgB,GAAG/L,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;;EAGrC;EACA,MAAM,CAACgM,WAAW,EAAEC,cAAc,CAAC,GAAGhM,QAAQ,CAAC;IAAEiM,OAAO,EAAE;EAAG,CAAC,CAAC;;EAE/D;EACA,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAI;MACF;MACA,MAAMC,MAAM,GAAGlJ,QAAQ;MACvB,MAAMc,QAAQ,GAAG,MAAMrD,KAAK,CAACmF,GAAG,CAAC,GAAGsG,MAAM,cAAc,CAAC;MACzD,IAAIpI,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACmI,OAAO,EAAE;QAC1CF,YAAY,GAAGnI,QAAQ,CAACE,IAAI,CAACA,IAAI;MACnC;MAEAZ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE6I,MAAM,CAAC;MACzC9I,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE4I,YAAY,CAAC;IAC3C,CAAC,CAAC,OAAOvH,KAAK,EAAE;MACd,IAAI;QACF,MAAMZ,QAAQ,GAAG,MAAMC,KAAK,CAAC,wBAAwB,CAAC;QACtD,MAAME,IAAI,GAAG,MAAMH,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCgI,YAAY,GAAGhI,IAAI,CAAC8H,OAAO,IAAI,EAAE;MACnC,CAAC,CAAC,OAAO5C,CAAC,EAAE;QACV/F,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEyE,CAAC,CAAC;MAC9B;IACF;IACA2C,cAAc,CAAC;MAAEC,OAAO,EAAEE;IAAa,CAAC,CAAC;EAC3C,CAAC;EACDrM,SAAS,CAAC,MAAM;IAAEoM,eAAe,CAAC,CAAC;EAAE,CAAC,EAAE,EAAE,CAAC;;EAE5C;EACC,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGvM,QAAQ,CAAC,EAAE,CAAC;EACtD;EACAF,SAAS,CAAC,MAAM;IACd,MAAM0M,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACF,MAAMJ,MAAM,GAAGjJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;QACvE,MAAMW,QAAQ,GAAG,MAAMrD,KAAK,CAACmF,GAAG,CAAC,GAAGsG,MAAM,oBAAoB,CAAC;QAC/D,IAAIpI,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACmI,OAAO,EAAE;UAC1CE,gBAAgB,CAACvI,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOU,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC;IACD4H,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAGN;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG1M,QAAQ,CAAC;IAC/C2M,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhN,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMiN,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,IAAI;IAAG;IACfC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGnO,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAACoO,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpO,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAACqO,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtO,QAAQ,CAAC;IAC7DuO,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,IAAI;IACbtB,QAAQ,EAAE;MAAE5H,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxBiJ,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAG5O,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM6O,0BAA0B,GAAG7O,MAAM,CAAC,IAAI,CAAC;;EAE/C;EACA2C,MAAM,CAACmM,uBAAuB,GAAGP,sBAAsB;;EAEvD;EACA5L,MAAM,CAACiM,mBAAmB,GAAGA,mBAAmB;EAChDjM,MAAM,CAACkM,0BAA0B,GAAGA,0BAA0B;;EAE9D;EACA,MAAME,uBAAuB,GAAG;IAC9B5B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IAAG;IACd3B,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7B2B,KAAK,EAAE,OAAO;IAAG;IACjB1B,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMiB,UAAU,GAAG;IACjB/B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACX3B,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBwB,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbpB,QAAQ,EAAE,MAAM;IAChBqB,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvC/B,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM,CAAC3J,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,IAAIsC,GAAG,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACgN,UAAU,EAAEC,aAAa,CAAC,GAAGvP,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACyD,gBAAgB,EAAE+L,mBAAmB,CAAC,GAAGxP,QAAQ,CAAC,IAAIsC,GAAG,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACmN,WAAW,EAAEC,cAAc,CAAC,GAAG1P,QAAQ,CAAC;IAAE2P,KAAK,EAAE,EAAE;IAAElB,OAAO,EAAE;EAAG,CAAC,CAAC;;EAE1E;EACA,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAG7P,QAAQ,CAAC;IACjDuO,OAAO,EAAE,KAAK;IACduB,QAAQ,EAAE,IAAI;IACd5C,QAAQ,EAAE;MAAE5H,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxBiJ,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAMsB,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,gBAAgB,CAAC;MAAEtB,OAAO,EAAE,KAAK;MAAEuB,QAAQ,EAAE,IAAI;MAAE5C,QAAQ,EAAE;QAAE5H,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC;MAAEiJ,OAAO,EAAE;IAAK,CAAC,CAAC;EAC/F,CAAC;;EAED;EACA,MAAMuB,0BAA0B,GAAIC,MAAM,IAAK;IAC7C,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;IACxB,oBAAOlP,OAAA,CAACF,oBAAoB;MAACoP,MAAM,EAAEA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACjD,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI7O,UAAU,KAAK,QAAQ,EAAE;MAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB9B,UAAU,GAAG,QAAQ;;MAErB;MACA8J,kBAAkB,CAACgF,OAAO,GAAG,IAAI;MACjC/E,gBAAgB,CAAC+E,OAAO,GAAG,IAAI;MAE/B,IAAI7O,QAAQ,EAAE;QACZA,QAAQ,CAAC8O,OAAO,GAAG,KAAK;MAC1B;IACF;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIhP,UAAU,KAAK,QAAQ,EAAE;MAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB9B,UAAU,GAAG,QAAQ;;MAErB;MACA8J,kBAAkB,CAACgF,OAAO,GAAG,IAAI;MACjC/E,gBAAgB,CAAC+E,OAAO,GAAG,IAAI;MAE/B,IAAIrC,SAAS,CAACqC,OAAO,IAAI7O,QAAQ,EAAE;QACjC;QACA;QACAwM,SAAS,CAACqC,OAAO,CAACrD,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC,MAAM+K,UAAU,GAAGxC,SAAS,CAACqC,OAAO,CAACrD,QAAQ,CAAC9H,KAAK,CAAC,CAAC;QACrD,MAAMuL,SAAS,GAAGzC,SAAS,CAACqC,OAAO,CAACK,EAAE,CAACxL,KAAK,CAAC,CAAC;;QAE9C;QACA,IAAI9E,KAAK,CAACuQ,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC;UAAExL,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,GAAG;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAChCqL,MAAM,CAACzQ,KAAK,CAAC0Q,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdjD,SAAS,CAACqC,OAAO,CAACrD,QAAQ,CAACkE,IAAI,CAACV,UAAU,CAAC;QAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;QAEV;QACA,IAAI/Q,KAAK,CAACuQ,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;UAAExL,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAC9BqL,MAAM,CAACzQ,KAAK,CAAC0Q,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdjD,SAAS,CAACqC,OAAO,CAACK,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC;QACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC;;QAEV;QACA3P,QAAQ,CAAC4P,MAAM,CAAC3L,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B,MAAM4L,aAAa,GAAG7P,QAAQ,CAAC4P,MAAM,CAAClM,KAAK,CAAC,CAAC;;QAE7C;QACA,IAAI9E,KAAK,CAACuQ,KAAK,CAACU,aAAa,CAAC,CAC3BT,EAAE,CAAC;UAAExL,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAC9BqL,MAAM,CAACzQ,KAAK,CAAC0Q,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdzP,QAAQ,CAAC4P,MAAM,CAACF,IAAI,CAACG,aAAa,CAAC;UACnC;UACArD,SAAS,CAACqC,OAAO,CAACiB,MAAM,CAAC9P,QAAQ,CAAC4P,MAAM,CAAC;UACzC5P,QAAQ,CAAC+P,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;QAEV;QACA3P,QAAQ,CAAC8O,OAAO,GAAG,IAAI;;QAEvB;QACA9O,QAAQ,CAACgQ,WAAW,GAAG,EAAE;QACzBhQ,QAAQ,CAACiQ,WAAW,GAAG,GAAG;QAC1BjQ,QAAQ,CAACkQ,aAAa,GAAGpL,IAAI,CAACC,EAAE,GAAG,GAAG;QACtC/E,QAAQ,CAACmQ,aAAa,GAAG,CAAC;QAC1BnQ,QAAQ,CAAC+P,MAAM,CAAC,CAAC;QACjB;QACAvD,SAAS,CAACqC,OAAO,CAACuB,YAAY,CAAC,CAAC;QAChC5D,SAAS,CAACqC,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;QAEzCzO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;UACrByO,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAG/F,aAAa,CAAC9H,IAAI,CAAC8N,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKH,KAAK,CAAC;IAE9D,IAAIC,YAAY,IAAInE,SAAS,CAACqC,OAAO,IAAI7O,QAAQ,EAAE;MACjD0M,uBAAuB,CAACiE,YAAY,CAAC;;MAErC;MACA,MAAMG,WAAW,GAAGvH,SAAS,CAACsF,OAAO,CAACkC,YAAY,CAChDC,UAAU,CAACL,YAAY,CAAC1F,SAAS,CAAC,EAClC+F,UAAU,CAACL,YAAY,CAACzF,QAAQ,CAClC,CAAC;MAEDtJ,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvBoP,IAAI,EAAEN,YAAY,CAACE,IAAI;QACvBK,GAAG,EAAE;UACHjG,SAAS,EAAE0F,YAAY,CAAC1F,SAAS;UACjCC,QAAQ,EAAEyF,YAAY,CAACzF;QACzB,CAAC;QACDiG,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACA/Q,UAAU,GAAG,cAAc;MAC3BuL,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAkB,SAAS,CAACqC,OAAO,CAACrD,QAAQ,CAACvH,GAAG,CAAC6M,WAAW,CAAClN,CAAC,GAAC,EAAE,EAAE,EAAE,EAAE,CAACkN,WAAW,CAAChN,CAAC,GAAC,EAAE,CAAC;;MAEvE;MACA9D,QAAQ,CAAC4P,MAAM,CAAC3L,GAAG,CAAC6M,WAAW,CAAClN,CAAC,EAAE,CAAC,EAAE,CAACkN,WAAW,CAAChN,CAAC,CAAC;;MAErD;MACA0I,SAAS,CAACqC,OAAO,CAACiB,MAAM,CAAC9P,QAAQ,CAAC4P,MAAM,CAAC;;MAEzC;MACA5P,QAAQ,CAAC8O,OAAO,GAAG,IAAI;MACvB9O,QAAQ,CAAC+P,MAAM,CAAC,CAAC;;MAEjB;MACAvD,SAAS,CAACqC,OAAO,CAACuB,YAAY,CAAC,CAAC;MAChC5D,SAAS,CAACqC,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;MAEzCzO,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBoP,IAAI,EAAEN,YAAY,CAACE,IAAI;QACvBO,IAAI,EAAE5E,SAAS,CAACqC,OAAO,CAACrD,QAAQ,CAAC6F,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAEtR,QAAQ,CAAC4P,MAAM,CAACyB,OAAO,CAAC,CAAC;QAC9BF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACA,IAAIH,YAAY,CAACY,eAAe,KAAK,KAAK,IAAIZ,YAAY,CAAC7D,OAAO,EAAE;QAClElL,OAAO,CAACC,GAAG,CAAC,MAAM8O,YAAY,CAACE,IAAI,iBAAiB,CAAC;;QAErD;QACAW,UAAU,CAAC,MAAM;UACf;UACA,IAAI1E,OAAO,GAAG6D,YAAY,CAAC7D,OAAO;;UAElC;UACA,IAAI9L,MAAM,CAACyQ,qBAAqB,EAAE;YAChCzQ,MAAM,CAACyQ,qBAAqB,CAAC3E,OAAO,CAAC;YACrClL,OAAO,CAACC,GAAG,CAAC,SAAS8O,YAAY,CAACE,IAAI,SAAS/D,OAAO,YAAY,CAAC;UACrE,CAAC,MAAM;YACLlL,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC;UAChC;QACF,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLtB,OAAO,CAACC,GAAG,CAAC,MAAM8O,YAAY,CAACE,IAAI,sBAAsB,CAAC;;QAE1D;QACA,IAAI7P,MAAM,CAACmM,uBAAuB,EAAE;UAClCnM,MAAM,CAACmM,uBAAuB,CAAC;YAC7BN,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAM6E,iBAAiB,GAAGA,CAACzD,KAAK,EAAE0D,OAAO,KAAK;IAAA,IAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA;IAC5C,IAAI;MACF,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACb,OAAO,CAAC;;MAEnC;MACA,IAAI1D,KAAK,KAAKnN,WAAW,CAACO,GAAG,EAAE;QAAA,IAAAoR,aAAA;QAC7B;;QAEA;QACA,MAAMC,SAAS,GAAGJ,OAAO,CAACK,GAAG;QAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;QAEnC;QACA,MAAMC,aAAa,GAAG/Q,gBAAgB,CAACqC,GAAG,CAACsO,SAAS,CAAC;;QAErD;QACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;UACrD;UACA;UACA;UACA;UACA;UACA;QACF;;QAEA;QACA/Q,gBAAgB,CAACkC,GAAG,CAACyO,SAAS,EAAEE,gBAAgB,CAAC;;QAEjD;QACA;QACA;QACA;QACA;;QAEA,MAAMG,YAAY,GAAG,EAAAN,aAAA,GAAAH,OAAO,CAAC9P,IAAI,cAAAiQ,aAAA,uBAAZA,aAAA,CAAcM,YAAY,KAAI,EAAE;QACrD,MAAMC,KAAK,GAAGV,OAAO,CAAC9P,IAAI,CAACwQ,KAAK;;QAEhC;QACA,MAAM9I,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;;QAEtB;QACA6I,YAAY,CAACvM,OAAO,CAACyM,WAAW,IAAI;UAClC;UACA;UACA,MAAMC,EAAE,GAAIR,SAAS,GAAGO,WAAW,CAACE,SAAS;UAC7C,MAAMC,IAAI,GAAGH,WAAW,CAACI,WAAW;UAEpC,IAAGD,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACE;YACA,MAAME,KAAK,GAAG;cACZrI,SAAS,EAAE+F,UAAU,CAACiC,WAAW,CAACM,WAAW,CAAC;cAC9CrI,QAAQ,EAAE8F,UAAU,CAACiC,WAAW,CAACO,UAAU,CAAC;cAC5CrI,KAAK,EAAE6F,UAAU,CAACiC,WAAW,CAACQ,SAAS,CAAC;cACxCrI,OAAO,EAAE4F,UAAU,CAACiC,WAAW,CAACS,WAAW;YAC7C,CAAC;YAED,MAAMC,QAAQ,GAAGpK,SAAS,CAACsF,OAAO,CAACkC,YAAY,CAACuC,KAAK,CAACrI,SAAS,EAAEqI,KAAK,CAACpI,QAAQ,CAAC;;YAEhF;YACA,IAAI0I,cAAc;YAClB,QAAQR,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRQ,cAAc,GAAG3T,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACR2T,cAAc,GAAG1T,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACR0T,cAAc,GAAGzT,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACF,IAAI4F,KAAK,GAAGjE,aAAa,CAACsC,GAAG,CAAC8O,EAAE,CAAC;YAEjC,IAAI,CAACnN,KAAK,IAAI6N,cAAc,EAAE;cAC1B;cACA,MAAMC,QAAQ,GAAGT,IAAI,KAAK,GAAG,GAAGvU,aAAa,CAAC6E,KAAK,CAACvD,oBAAoB,CAAC,GAAGyT,cAAc,CAAClQ,KAAK,CAAC,CAAC;cAClG;cACA,MAAMoQ,MAAM,GAAGV,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;cACvCS,QAAQ,CAACrI,QAAQ,CAACvH,GAAG,CAAC0P,QAAQ,CAAC/P,CAAC,EAAEkQ,MAAM,EAAE,CAACH,QAAQ,CAAC7P,CAAC,CAAC;cACtD+P,QAAQ,CAACE,QAAQ,CAACjQ,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGuO,KAAK,CAAClI,OAAO,GAAGtG,IAAI,CAACC,EAAE,GAAG,GAAG;;cAE7D;cACA,IAAIqO,IAAI,KAAK,GAAG,EAAE;gBAClB;gBACES,QAAQ,CAACG,KAAK,CAAC/P,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;gBAE3B;gBACA,MAAM6B,KAAK,GAAG0C,oBAAoB,CAACqL,QAAQ,CAAC;gBAE5C,IAAIvT,eAAe,IAAIA,eAAe,CAACwG,UAAU,IAAIxG,eAAe,CAACwG,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;kBAC1F;kBACA,MAAMT,MAAM,GAAGoC,YAAY,CAACpI,eAAe,CAACwG,UAAU,CAAC,CAAC,CAAC,EAAEhB,KAAK,EAAE+N,QAAQ,CAAC;kBAC3EvN,MAAM,CAAC2N,IAAI,CAAC,CAAC;gBACf;;gBAEA;gBACA9J,qBAAqB,CAAClG,GAAG,CAACiP,EAAE,EAAEpN,KAAK,CAAC;cACtC;cAEAzF,KAAK,CAAC2F,GAAG,CAAC6N,QAAQ,CAAC;cAEnB/R,aAAa,CAACmC,GAAG,CAACiP,EAAE,EAAE;gBACpBnN,KAAK,EAAE8N,QAAQ;gBACfK,UAAU,EAAEhK,GAAG;gBACjBkJ,IAAI,EAAEA;cACN,CAAC,CAAC;YACN,CAAC,MAAM,IAAIrN,KAAK,EAAE;cACd;cACFA,KAAK,CAACA,KAAK,CAACyF,QAAQ,CAACvH,GAAG,CAAC0P,QAAQ,CAAC/P,CAAC,EAAEmC,KAAK,CAACqN,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAACO,QAAQ,CAAC7P,CAAC,CAAC;cACjFiC,KAAK,CAACA,KAAK,CAACgO,QAAQ,CAACjQ,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGuO,KAAK,CAAClI,OAAO,GAAGtG,IAAI,CAACC,EAAE,GAAG,GAAG;cAChEgB,KAAK,CAACmO,UAAU,GAAGhK,GAAG;cACtBnE,KAAK,CAACA,KAAK,CAACqK,YAAY,CAAC,CAAC;cAC1BrK,KAAK,CAACA,KAAK,CAACsK,iBAAiB,CAAC,IAAI,CAAC;YACnC;UACF;QACF,CAAC,CAAC;;QAEF;QACA,MAAM8D,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAI9O,GAAG,CAACyN,YAAY,CAAC1L,GAAG,CAACgN,CAAC,IAAI3B,SAAS,GAAG2B,CAAC,CAAClB,SAAS,CAAC,CAAC;QAE1ErR,aAAa,CAAC0E,OAAO,CAAC,CAAC8N,SAAS,EAAEpB,EAAE,KAAK;UACvC,IAAIhJ,GAAG,GAAGoK,SAAS,CAACJ,UAAU,GAAGC,iBAAiB,IAAI,CAACC,UAAU,CAAClQ,GAAG,CAACgP,EAAE,CAAC,EAAE;YACzE;YACA,IAAIoB,SAAS,CAAClB,IAAI,KAAK,GAAG,IAAIjJ,qBAAqB,CAACjG,GAAG,CAACgP,EAAE,CAAC,EAAE;cAC3D,MAAMpN,KAAK,GAAGqE,qBAAqB,CAAC/F,GAAG,CAAC8O,EAAE,CAAC;cAC3C;cACA1N,eAAe,CAACe,WAAW,CAACT,KAAK,CAAC;cAClCqE,qBAAqB,CAACzD,MAAM,CAACwM,EAAE,CAAC;YAClC;;YAEA;YACA7S,KAAK,CAAC2H,MAAM,CAACsM,SAAS,CAACvO,KAAK,CAAC;YAC7BjE,aAAa,CAAC4E,MAAM,CAACwM,EAAE,CAAC;UAC1B;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAIjF,KAAK,KAAKnN,WAAW,CAACM,GAAG,EAAE;QAC7B;;QAEA,MAAMmT,OAAO,GAAGjC,OAAO,CAAC9P,IAAI;QAC5B,MAAMgS,KAAK,GAAGD,OAAO,CAACtR,KAAK;QAC3B,MAAMwR,QAAQ,GAAG;UACfxJ,SAAS,EAAE+F,UAAU,CAACuD,OAAO,CAACG,QAAQ,CAAC;UACvCxJ,QAAQ,EAAE8F,UAAU,CAACuD,OAAO,CAACI,OAAO,CAAC;UACrCxJ,KAAK,EAAE6F,UAAU,CAACuD,OAAO,CAACd,SAAS,CAAC;UACpCrI,OAAO,EAAE4F,UAAU,CAACuD,OAAO,CAACb,WAAW;QACzC,CAAC;;QAED;QACA;;QAEA;QACA1S,MAAM,CAAC4T,WAAW,CAAC;UACjBxB,IAAI,EAAE,iBAAiB;UACvByB,MAAM,EAAE;QACV,CAAC,EAAE,GAAG,CAAC;;QAEP;QACA7T,MAAM,CAAC4T,WAAW,CAAC;UACjBxB,IAAI,EAAE,KAAK;UACXnQ,KAAK,EAAEuR,KAAK;UAAE;UACdhS,IAAI,EAAE;YAAQ;YACZS,KAAK,EAAEuR,KAAK;YACZf,SAAS,EAAEc,OAAO,CAACd,SAAS;YAC5BkB,OAAO,EAAEJ,OAAO,CAACI,OAAO;YACxBD,QAAQ,EAAEH,OAAO,CAACG,QAAQ;YAC1BhB,WAAW,EAAEa,OAAO,CAACb;UACvB;QACF,CAAC,EAAE,GAAG,CAAC;;QAEP;QACA,MAAMC,QAAQ,GAAGpK,SAAS,CAACsF,OAAO,CAACkC,YAAY,CAAC0D,QAAQ,CAACxJ,SAAS,EAAEwJ,QAAQ,CAACvJ,QAAQ,CAAC;QACtF,MAAM4J,eAAe,GAAG,IAAItW,KAAK,CAACkG,OAAO,CAACiP,QAAQ,CAAC/P,CAAC,EAAE,GAAG,EAAE,CAAC+P,QAAQ,CAAC7P,CAAC,CAAC;QACvE,MAAMiR,eAAe,GAAGjQ,IAAI,CAACC,EAAE,GAAG0P,QAAQ,CAACrJ,OAAO,GAAGtG,IAAI,CAACC,EAAE,GAAG,GAAG;;QAElE;QACA,MAAMiQ,WAAW,GAAGzR,cAAc,CAACuR,eAAe,EAAEN,KAAK,CAAC;QAC1D,MAAM5P,WAAW,GAAGD,cAAc,CAACoQ,eAAe,EAAEP,KAAK,CAAC;;QAE1D;QACA,IAAIS,UAAU,GAAGnT,aAAa,CAACsC,GAAG,CAACoQ,KAAK,CAAC;;QAEzC;QACA,MAAMxR,aAAa,GAAGwR,KAAK,KAAKxS,gBAAgB;QAEhD,IAAI,CAACiT,UAAU,IAAIhV,qBAAqB,EAAE;UACxC;UACA,MAAMiV,eAAe,GAAGjV,qBAAqB,CAACyD,KAAK,CAAC,CAAC;;UAErD;UACA;UACAwR,eAAe,CAAC1J,QAAQ,CAACvH,GAAG,CAAC+Q,WAAW,CAACpR,CAAC,EAAE,CAAC,CAAC,EAAEoR,WAAW,CAAChR,CAAC,CAAC;UAC9DkR,eAAe,CAACnB,QAAQ,CAACjQ,CAAC,GAAGc,WAAW;;UAExC;UACAsQ,eAAe,CAACjP,QAAQ,CAAEkP,KAAK,IAAK;YAClC,IAAIA,KAAK,CAAC/M,MAAM,IAAI+M,KAAK,CAAC5M,QAAQ,EAAE;cAClC;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;;cAEA;cACA;cACA;;cAEA;;cAEA,MAAM6M,WAAW,GAAGD,KAAK,CAAC5M,QAAQ,CAAC7E,KAAK,CAAC,CAAC;cAC1CyR,KAAK,CAAC5M,QAAQ,GAAG6M,WAAW;;cAE5B;cACA,IAAIpS,aAAa,EAAE;gBACjBoS,WAAW,CAAC3H,KAAK,CAACxJ,GAAG,CAAC,QAAQ,CAAC;cACjC,CAAC,MAAM;gBACLmR,WAAW,CAAC3H,KAAK,CAACxJ,GAAG,CAAC,QAAQ,CAAC;cACjC;cACAmR,WAAW,CAACC,QAAQ,GAAG,IAAI7W,KAAK,CAAC8W,KAAK,CAAC,QAAQ,CAAC;cAChDF,WAAW,CAACG,WAAW,GAAG,IAAI;cAC9B;cACAH,WAAW,CAACI,WAAW,GAAG,IAAI;YAChC;UACF,CAAC,CAAC;;UAEF;UACA,MAAMC,UAAU,GAAGC,gBAAgB,CAAC,GAAG5Q,IAAI,CAAC6Q,KAAK,CAAClB,QAAQ,CAACtJ,KAAK,CAAC,OAAO,EAAE;YACxEc,eAAe,EAAEjJ,aAAa,GAC5B;cAAE4S,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAExO,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAEsO,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAExO,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCyO,SAAS,EAAE;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAExO,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/C+E,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFyJ,UAAU,CAACjK,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClCwR,UAAU,CAACO,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/BP,UAAU,CAAClN,QAAQ,CAAC0N,OAAO,GAAG,GAAG,CAAC,CAAC;UACnCf,eAAe,CAAClP,GAAG,CAACyP,UAAU,CAAC;UAE/BpV,KAAK,CAAC2F,GAAG,CAACkP,eAAe,CAAC;;UAE1B;UACApT,aAAa,CAACmC,GAAG,CAACuQ,KAAK,EAAE;YACvBzO,KAAK,EAAEmP,eAAe;YACtBhB,UAAU,EAAEjK,IAAI,CAACC,GAAG,CAAC,CAAC;YACtBkJ,IAAI,EAAE,GAAG;YAAE;YACX8C,MAAM,EAAElT,aAAa;YACrByS,UAAU,EAAEA,UAAU,CAAC;UACzB,CAAC,CAAC;;UAEF;;UAEA;UACA,IAAI7W,KAAK,CAACuQ,KAAK,CAAC+F,eAAe,CAAC1J,QAAQ,CAAC,CACtC4D,EAAE,CAAC;YAAEtL,CAAC,EAAE;UAAI,CAAC,EAAE,GAAG,CAAC,CACnBuL,MAAM,CAACzQ,KAAK,CAAC0Q,MAAM,CAACC,SAAS,CAAC4G,GAAG,CAAC,CAClCxG,KAAK,CAAC,CAAC;;UAEV;UACAuF,eAAe,CAACjP,QAAQ,CAAEkP,KAAK,IAAK;YAClC,IAAIA,KAAK,CAAC/M,MAAM,IAAI+M,KAAK,CAAC5M,QAAQ,IAAI4M,KAAK,CAAC5M,QAAQ,CAACgN,WAAW,EAAE;cAChE,IAAI3W,KAAK,CAACuQ,KAAK,CAAC;gBAAE8G,OAAO,EAAE;cAAI,CAAC,CAAC,CAC9B7G,EAAE,CAAC;gBAAE6G,OAAO,EAAE;cAAI,CAAC,EAAE,GAAG,CAAC,CACzB5G,MAAM,CAACzQ,KAAK,CAAC0Q,MAAM,CAACC,SAAS,CAAC4G,GAAG,CAAC,CAClC1G,QAAQ,CAAC,YAAW;gBACnB0F,KAAK,CAAC5M,QAAQ,CAAC0N,OAAO,GAAG,IAAI,CAACA,OAAO;gBACrCd,KAAK,CAAC5M,QAAQ,CAACiN,WAAW,GAAG,IAAI;cACnC,CAAC,CAAC,CACD7F,KAAK,CAAC,CAAC;YACZ;UACF,CAAC,CAAC;;UAEF;UACA,IAAI/Q,KAAK,CAACuQ,KAAK,CAAC;YAAE8G,OAAO,EAAE;UAAI,CAAC,CAAC,CAC9B7G,EAAE,CAAC;YAAE6G,OAAO,EAAE;UAAI,CAAC,EAAE,GAAG,CAAC,CACzB5G,MAAM,CAACzQ,KAAK,CAAC0Q,MAAM,CAACC,SAAS,CAAC4G,GAAG,CAAC,CAClC1G,QAAQ,CAAC,YAAW;YACnBgG,UAAU,CAAClN,QAAQ,CAAC0N,OAAO,GAAG,IAAI,CAACA,OAAO;YAC1CR,UAAU,CAAClN,QAAQ,CAACiN,WAAW,GAAG,IAAI;UACxC,CAAC,CAAC,CACD7F,KAAK,CAAC,CAAC;;UAEV;UACA,IAAI3M,aAAa,EAAE;YACjBxD,gBAAgB,GAAG0V,eAAe;YAClClK,eAAe,CAACyJ,QAAQ,CAAC;YACzB7S,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE2S,KAAK,CAAC;UACjC;QACF,CAAC,MAAM,IAAIS,UAAU,EAAE;UACrB;UACA,MAAMmB,gBAAgB,GAAG7S,cAAc,CAACyR,WAAW,EAAER,KAAK,CAAC;UAC3D,MAAMxP,gBAAgB,GAAGL,cAAc,CAACC,WAAW,EAAE4P,KAAK,CAAC;;UAE3D;UACAS,UAAU,CAAClP,KAAK,CAACyF,QAAQ,CAACkE,IAAI,CAAC0G,gBAAgB,CAAC;UAChDnB,UAAU,CAAClP,KAAK,CAACgO,QAAQ,CAACjQ,CAAC,GAAGkB,gBAAgB;UAC9CiQ,UAAU,CAAClP,KAAK,CAACqK,YAAY,CAAC,CAAC;UAC/B6E,UAAU,CAAClP,KAAK,CAACsK,iBAAiB,CAAC,IAAI,CAAC;UACxC4E,UAAU,CAACf,UAAU,GAAGjK,IAAI,CAACC,GAAG,CAAC,CAAC;UAClC+K,UAAU,CAACiB,MAAM,GAAGlT,aAAa,CAAC,CAAC;;UAEnC;UACA,IAAIiS,UAAU,CAACQ,UAAU,EAAE;YACzBR,UAAU,CAACQ,UAAU,CAAClN,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;YAC5C2M,UAAU,CAAClP,KAAK,CAACiC,MAAM,CAACiN,UAAU,CAACQ,UAAU,CAAC;UAChD;;UAEA;UACA,MAAMA,UAAU,GAAGC,gBAAgB,CAAC,GAAG5Q,IAAI,CAAC6Q,KAAK,CAAClB,QAAQ,CAACtJ,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE;YAC9Ec,eAAe,EAAEjJ,aAAa,GAC5B;cAAE4S,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAExO,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAEsO,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAExO,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCyO,SAAS,EAAE;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAExO,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/C+E,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFyJ,UAAU,CAACjK,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACnCwR,UAAU,CAACO,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/Bf,UAAU,CAAClP,KAAK,CAACC,GAAG,CAACyP,UAAU,CAAC;UAChCR,UAAU,CAACQ,UAAU,GAAGA,UAAU;;UAElC;;UAEA;UACA,IAAIzS,aAAa,EAAE;YACjBxD,gBAAgB,GAAGyV,UAAU,CAAClP,KAAK;YACnCiF,eAAe,CAACyJ,QAAQ,CAAC;UAC3B;QACF;;QAEA;QACA,MAAMvK,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;QACtB,MAAMiK,iBAAiB,GAAG,IAAI,CAAC,CAAC;;QAEhCrS,aAAa,CAAC0E,OAAO,CAAC,CAAC8N,SAAS,EAAEpB,EAAE,KAAK;UACvC,MAAMmD,mBAAmB,GAAGnM,GAAG,GAAGoK,SAAS,CAACJ,UAAU;;UAEtD;UACA,IAAImC,mBAAmB,GAAGlC,iBAAiB,GAAG,GAAG,IAAIkC,mBAAmB,IAAIlC,iBAAiB,EAAE;YAC7F;YACA,MAAM8B,OAAO,GAAG,CAAC;YAEjB3B,SAAS,CAACvO,KAAK,CAACE,QAAQ,CAAEkP,KAAK,IAAK;cAClC,IAAIA,KAAK,CAAC/M,MAAM,IAAI+M,KAAK,CAAC5M,QAAQ,EAAE;gBAClC;gBACA,IAAI4M,KAAK,CAAC5M,QAAQ,CAACgN,WAAW,KAAKe,SAAS,EAAE;kBAC5CnB,KAAK,CAAC5M,QAAQ,CAACgO,mBAAmB,GAAGpB,KAAK,CAAC5M,QAAQ,CAACgN,WAAW,IAAI,KAAK;kBACxEJ,KAAK,CAAC5M,QAAQ,CAACiO,eAAe,GAAGrB,KAAK,CAAC5M,QAAQ,CAAC0N,OAAO,IAAI,GAAG;gBAChE;;gBAEA;gBACAd,KAAK,CAAC5M,QAAQ,CAACgN,WAAW,GAAG,IAAI;gBACjCJ,KAAK,CAAC5M,QAAQ,CAAC0N,OAAO,GAAGA,OAAO;gBAChCd,KAAK,CAAC5M,QAAQ,CAACiN,WAAW,GAAG,IAAI;cACnC;YACF,CAAC,CAAC;;YAEF;YACA,IAAIlB,SAAS,CAACmB,UAAU,EAAE;cACxBnB,SAAS,CAACmB,UAAU,CAAClN,QAAQ,CAAC0N,OAAO,GAAGA,OAAO;cAC/C3B,SAAS,CAACmB,UAAU,CAAClN,QAAQ,CAACiN,WAAW,GAAG,IAAI;YAClD;UACF;UACA;UAAA,KACK,IAAIa,mBAAmB,GAAGlC,iBAAiB,EAAE;YAChD;YACA,IAAIG,SAAS,CAACmB,UAAU,EAAE;cACxBnB,SAAS,CAACmB,UAAU,CAAClN,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;cAC3CgM,SAAS,CAACmB,UAAU,CAAClN,QAAQ,CAACD,OAAO,CAAC,CAAC;cACvCgM,SAAS,CAACvO,KAAK,CAACiC,MAAM,CAACsM,SAAS,CAACmB,UAAU,CAAC;YAC9C;YAEAnB,SAAS,CAACvO,KAAK,CAACE,QAAQ,CAAEkP,KAAK,IAAK;cAClC,IAAIA,KAAK,CAAC/M,MAAM,EAAE;gBAChB,IAAI+M,KAAK,CAAC5M,QAAQ,EAAE;kBAClB,IAAI5F,KAAK,CAACC,OAAO,CAACuS,KAAK,CAAC5M,QAAQ,CAAC,EAAE;oBACjC4M,KAAK,CAAC5M,QAAQ,CAAC/B,OAAO,CAACiQ,CAAC,IAAIA,CAAC,CAACnO,OAAO,CAAC,CAAC,CAAC;kBAC1C,CAAC,MAAM;oBACL6M,KAAK,CAAC5M,QAAQ,CAACD,OAAO,CAAC,CAAC;kBAC1B;gBACF;gBACA,IAAI6M,KAAK,CAAC9M,QAAQ,EAAE8M,KAAK,CAAC9M,QAAQ,CAACC,OAAO,CAAC,CAAC;cAC9C;YACF,CAAC,CAAC;;YAEF;YACAjI,KAAK,CAAC2H,MAAM,CAACsM,SAAS,CAACvO,KAAK,CAAC;YAC7BjE,aAAa,CAAC4E,MAAM,CAACwM,EAAE,CAAC;YACxB;YACAvS,oBAAoB,CAAC+F,MAAM,CAACwM,EAAE,CAAC;YAC/BrS,oBAAoB,CAAC6F,MAAM,CAACwM,EAAE,CAAC;YAE/BtR,OAAO,CAACC,GAAG,CAAC,mBAAmBqR,EAAE,EAAE,CAAC;UACtC;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAIjF,KAAK,KAAKnN,WAAW,CAACS,IAAI,EAAE;QAC9B;;QAEA,IAAI;UACF,MAAM+Q,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACb,OAAO,CAAC;;UAEnC;UACA,MAAMe,SAAS,GAAGJ,OAAO,CAACK,GAAG;UAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;UAEnC;UACA,MAAMC,aAAa,GAAG/Q,gBAAgB,CAACqC,GAAG,CAACsO,SAAS,CAAC;;UAErD;UACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;YACrD;YACA;YACA;YACA;YACA;YACA;UACF;;UAEA;UACA/Q,gBAAgB,CAACkC,GAAG,CAACyO,SAAS,EAAEE,gBAAgB,CAAC;;UAEjD;UACA,IAAIN,OAAO,CAAC9P,IAAI,IAAI8P,OAAO,CAAC9P,IAAI,CAACoI,aAAa,IAAIjI,KAAK,CAACC,OAAO,CAAC0P,OAAO,CAAC9P,IAAI,CAACoI,aAAa,CAAC,EAAE;YAC3F0H,OAAO,CAAC9P,IAAI,CAACoI,aAAa,CAACpE,OAAO,CAACmK,YAAY,IAAI;cACjD,MAAM7D,OAAO,GAAG6D,YAAY,CAAC7D,OAAO;cAEpC,IAAI,CAACA,OAAO,EAAE;gBACZlL,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEyN,YAAY,CAAC;gBAC/C;cACF;;cAEA;;cAEA;cACA,IAAIA,YAAY,CAAC3D,MAAM,IAAIrK,KAAK,CAACC,OAAO,CAAC+N,YAAY,CAAC3D,MAAM,CAAC,EAAE;gBAC7D;gBACA,MAAM0J,UAAU,GAAG,EAAE;gBAErB/F,YAAY,CAAC3D,MAAM,CAACxG,OAAO,CAACmQ,KAAK,IAAI;kBACnC;kBACA,IAAI,CAACA,KAAK,CAACC,OAAO,EAAE;oBAClBhV,OAAO,CAACsB,KAAK,CAAC,gBAAgB,EAAEyT,KAAK,CAAC;oBACtC;kBACF;kBAEA,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC;kBACxC;kBACA,MAAMC,SAAS,GAAGH,KAAK,CAACI,YAAY,GAClCC,oBAAoB,CAACL,KAAK,CAACI,YAAY,CAAC,GACxCE,iBAAiB,CAACL,OAAO,CAAC;;kBAE5B;kBACA,MAAMM,UAAU,GAAGP,KAAK,CAACQ,YAAY,IAAI,GAAG,CAAC,CAAC;kBAC9C,MAAMC,UAAU,GAAGC,QAAQ,CAACV,KAAK,CAACS,UAAU,CAAC,IAAI,CAAC;;kBAElD;;kBAEA;kBACA,MAAME,SAAS,GAAG;oBAChBV,OAAO;oBACPE,SAAS;oBACTK,YAAY,EAAED,UAAU;oBACxBE;kBACF,CAAC;;kBAED;kBACAV,UAAU,CAACa,IAAI,CAACD,SAAS,CAAC;;kBAE1B;kBACA;kBACA,IAAIE,eAAe,GAAGC,MAAM,CAAC3K,OAAO,CAAC;kBACrC,IAAI4K,iBAAiB,GAAGzV,gBAAgB,CAACmC,GAAG,CAACoT,eAAe,CAAC;kBAE7D,IAAI,CAACE,iBAAiB,EAAE;oBACtB;oBACAF,eAAe,GAAGH,QAAQ,CAACvK,OAAO,CAAC;oBACnC4K,iBAAiB,GAAGzV,gBAAgB,CAACmC,GAAG,CAACoT,eAAe,CAAC;kBAC3D;kBAEA,IAAIE,iBAAiB,EAAE;oBACrB;oBACAC,wBAAwB,CAACD,iBAAiB,EAAEJ,SAAS,CAAC;;oBAEtD;oBACA,IAAI7K,oBAAoB,IAAIA,oBAAoB,CAACK,OAAO,KAAKA,OAAO,EAAE;sBACpEF,sBAAsB,CAACgL,IAAI,KAAK;wBAC9B,GAAGA,IAAI;wBACP/K,OAAO,EAAE,IAAI;wBACb+J,OAAO;wBACPE,SAAS;wBACTxD,KAAK,EAAE4D,UAAU;wBACjBE;sBACF,CAAC,CAAC,CAAC;oBACL;kBACF,CAAC,MAAM;oBACL;kBAAA;gBAEJ,CAAC,CAAC;;gBAEF;gBACA,IAAIS,QAAQ,GAAG,IAAI;gBACnB;gBACA,MAAMC,KAAK,GAAGL,MAAM,CAAC3K,OAAO,CAAC;gBAC7B,IAAI7K,gBAAgB,CAACiC,GAAG,CAAC4T,KAAK,CAAC,EAAE;kBAC/BD,QAAQ,GAAGC,KAAK;gBAClB,CAAC,MAAM;kBACL;kBACA,MAAMC,KAAK,GAAGV,QAAQ,CAACvK,OAAO,CAAC;kBAC/B,IAAI7K,gBAAgB,CAACiC,GAAG,CAAC6T,KAAK,CAAC,EAAE;oBAC/BF,QAAQ,GAAGE,KAAK;kBAClB;gBACF;gBAEA,IAAIF,QAAQ,KAAK,IAAI,EAAE;kBACrB;kBACA3V,kBAAkB,CAAC+B,GAAG,CAAC4T,QAAQ,EAAE;oBAC/BG,UAAU,EAAE/N,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtB8C,MAAM,EAAE0J;kBACV,CAAC,CAAC;kBACF9U,OAAO,CAACC,GAAG,CAAC,aAAagW,QAAQ,KAAK,OAAOA,QAAQ,aAAa,CAAC;;kBAEnE;kBACA,IAAI7W,MAAM,CAACiM,mBAAmB,KAC1BjM,MAAM,CAACiM,mBAAmB,CAAC4B,OAAO,KAAKgJ,QAAQ,IAC/C7W,MAAM,CAACiM,mBAAmB,CAAC4B,OAAO,KAAK4I,MAAM,CAACI,QAAQ,CAAC,IACvD7W,MAAM,CAACiM,mBAAmB,CAAC4B,OAAO,KAAKwI,QAAQ,CAACQ,QAAQ,CAAC,CAAC,EAAE;oBAE9DjW,OAAO,CAACC,GAAG,CAAC,eAAegW,QAAQ,aAAa,CAAC;oBACjD;oBACA7W,MAAM,CAACiM,mBAAmB,CAAC4B,OAAO,GAAGgJ,QAAQ;;oBAE7C;oBACA,IAAI7W,MAAM,CAACkM,0BAA0B,IAAI,CAAClM,MAAM,CAACkM,0BAA0B,CAAC2B,OAAO,EAAE;sBACnFjN,OAAO,CAACC,GAAG,CAAC,SAASgW,QAAQ,aAAa,CAAC;sBAC3CrG,UAAU,CAAC,MAAM;wBACfxQ,MAAM,CAACyQ,qBAAqB,CAACoG,QAAQ,CAAC;sBACxC,CAAC,EAAE,GAAG,CAAC;oBACT;kBACF;gBACF,CAAC,MAAM;kBACL;kBACA3V,kBAAkB,CAAC+B,GAAG,CAAC6I,OAAO,EAAE;oBAC9BkL,UAAU,EAAE/N,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtB8C,MAAM,EAAE0J;kBACV,CAAC,CAAC;kBACF;gBACF;cACF,CAAC,MAAM;gBACL9U,OAAO,CAACsB,KAAK,CAAC,eAAe,EAAEyN,YAAY,CAAC;cAC9C;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL/O,OAAO,CAACsB,KAAK,CAAC,oCAAoC,EAAEoP,OAAO,CAAC;UAC9D;QACF,CAAC,CAAC,OAAOpP,KAAK,EAAE;UACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEA,KAAK,EAAEyO,OAAO,CAAC;QAC9C;QACA;MACF;;MAEA;MACA,IAAI1D,KAAK,KAAKnN,WAAW,CAACQ,GAAG,IAAIgR,OAAO,CAACc,IAAI,KAAK,KAAK,EAAE;QACvD;;QAEA;QACApS,MAAM,CAAC4T,WAAW,CAAC;UACjBxB,IAAI,EAAE,KAAK;UACX5Q,IAAI,EAAE8P,OAAO,CAAC9P,IAAI;UAClBmQ,GAAG,EAAEL,OAAO,CAACK,GAAG;UAChBE,EAAE,EAAEP,OAAO,CAACO;QACd,CAAC,EAAE,GAAG,CAAC;QAEP,MAAMoF,OAAO,GAAG3F,OAAO,CAAC9P,IAAI;QAC5B,MAAM0V,KAAK,GAAGD,OAAO,CAACC,KAAK;QAC3B,MAAMC,MAAM,GAAGF,OAAO,CAACG,IAAI,IAAI,EAAE;QAEjCD,MAAM,CAAC3R,OAAO,CAAC6R,KAAK,IAAI;UAAA,IAAAC,cAAA;UACtB,MAAMC,OAAO,GAAGF,KAAK,CAACG,KAAK;UAC3B,MAAMC,SAAS,GAAGJ,KAAK,CAACI,SAAS;UACjC,MAAMC,WAAW,GAAGL,KAAK,CAACK,WAAW;UACrC,MAAMC,SAAS,GAAGN,KAAK,CAACM,SAAS;UACjC,MAAMC,OAAO,GAAGP,KAAK,CAACO,OAAO;;UAE7B;UACA,MAAMjF,QAAQ,GAAGpK,SAAS,CAACsF,OAAO,CAACkC,YAAY,CAC7CC,UAAU,CAACiH,OAAO,CAACY,OAAO,CAAC,EAC3B7H,UAAU,CAACiH,OAAO,CAACa,MAAM,CAC3B,CAAC;;UAED;UACA,IAAIC,WAAW,GAAG,EAAE;UACpB,IAAIC,YAAY,GAAG,EAAE;UAErB,QAAOP,SAAS;YACd,KAAK,KAAK;cAAG;cACXM,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,QAAQ;cACtBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,MAAM;cAAE;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF;cACED,WAAW,GAAGL,WAAW,IAAI,MAAM;cACnCM,YAAY,GAAG,SAAS;UAC5B;;UAEA;UACAC,iBAAiB,CAACtF,QAAQ,EAAEoF,WAAW,EAAEC,YAAY,EAAEP,SAAS,EAAE;YAChEP,KAAK,EAAE,EAAAI,cAAA,GAAAhG,OAAO,CAAC9P,IAAI,cAAA8V,cAAA,uBAAZA,cAAA,CAAcJ,KAAK,KAAI,SAAS;YACvCK,OAAO,EAAEA,OAAO;YAChBG,WAAW,EAAEA;UACf,CAAC,CAAC;;UAEF;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAIzK,KAAK,KAAKnN,WAAW,CAACT,KAAK,IAAIiS,OAAO,CAACc,IAAI,KAAK,OAAO,EAAE;QAC3D;;QAEA,MAAM8F,SAAS,GAAG5G,OAAO,CAAC9P,IAAI;QAC9B,MAAM2W,OAAO,GAAGD,SAAS,CAACC,OAAO;QACjC,MAAMC,SAAS,GAAGF,SAAS,CAACE,SAAS;QACrC,MAAMC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAM7N,QAAQ,GAAG;UACfN,QAAQ,EAAE8F,UAAU,CAACkI,SAAS,CAACvE,OAAO,CAAC;UACvC1J,SAAS,EAAE+F,UAAU,CAACkI,SAAS,CAACxE,QAAQ;QAC1C,CAAC;;QAED;QACA,MAAMf,QAAQ,GAAGpK,SAAS,CAACsF,OAAO,CAACkC,YAAY,CAACvF,QAAQ,CAACP,SAAS,EAAEO,QAAQ,CAACN,QAAQ,CAAC;;QAEtF;QACA,QAAOkO,SAAS;UACd,KAAK,GAAG;YAAG;YACTH,iBAAiB,CAACtF,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,EAAE;cAAEuE,KAAK,GAAAtG,cAAA,GAAEU,OAAO,CAAC9P,IAAI,cAAAoP,cAAA,uBAAZA,cAAA,CAAcsG,KAAK;cAAEgB;YAAU,CAAC,CAAC;YAClG;UACF,KAAK,KAAK;YAAG;YACXD,iBAAiB,CAACtF,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE;cAAEuE,KAAK,GAAArG,cAAA,GAAES,OAAO,CAAC9P,IAAI,cAAAqP,cAAA,uBAAZA,cAAA,CAAcqG,KAAK;cAAEgB;YAAU,CAAC,CAAC;YACjG;UACF,KAAK,KAAK;YAAG;YACXD,iBAAiB,CAACtF,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;cAAEuE,KAAK,GAAApG,cAAA,GAAEQ,OAAO,CAAC9P,IAAI,cAAAsP,cAAA,uBAAZA,cAAA,CAAcoG,KAAK;cAAEgB;YAAU,CAAC,CAAC;YACjG;UACF,KAAK,IAAI;YAAG;YACV,MAAMI,UAAU,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAE;YAC1CN,iBAAiB,CAACtF,QAAQ,EAAE,KAAK2F,UAAU,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE;cAAEpB,KAAK,GAAAnG,cAAA,GAAEO,OAAO,CAAC9P,IAAI,cAAAuP,cAAA,uBAAZA,cAAA,CAAcmG,KAAK;cAAEgB;YAAU,CAAC,CAAC;YAC9G;UACF,KAAK,IAAI;YAAG;YACVD,iBAAiB,CAACtF,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE;cAAEuE,KAAK,GAAAlG,cAAA,GAAEM,OAAO,CAAC9P,IAAI,cAAAwP,cAAA,uBAAZA,cAAA,CAAckG,KAAK;cAAEgB;YAAU,CAAC,CAAC;YAC/F;UACF,KAAK,IAAI;YAAG;YACVD,iBAAiB,CAACtF,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE;cAAEuE,KAAK,GAAAjG,cAAA,GAAEK,OAAO,CAAC9P,IAAI,cAAAyP,cAAA,uBAAZA,cAAA,CAAciG,KAAK;cAAEgB;YAAU,CAAC,CAAC;YAC/F;UACF,KAAK,MAAM;YAAG;YACZD,iBAAiB,CAACtF,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE;cAAEuE,KAAK,GAAAhG,cAAA,GAAEI,OAAO,CAAC9P,IAAI,cAAA0P,cAAA,uBAAZA,cAAA,CAAcgG,KAAK;cAAEgB;YAAU,CAAC,CAAC;YACjG;UACF,KAAK,IAAI;YAAG;YACVD,iBAAiB,CAACtF,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE;cAAEuE,KAAK,GAAA/F,eAAA,GAAEG,OAAO,CAAC9P,IAAI,cAAA2P,eAAA,uBAAZA,eAAA,CAAc+F,KAAK;cAAEgB;YAAU,CAAC,CAAC;YAChG;UACF,KAAK,IAAI;YAAG;YACVD,iBAAiB,CAACtF,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE;cAAEuE,KAAK,GAAA9F,eAAA,GAAEE,OAAO,CAAC9P,IAAI,cAAA4P,eAAA,uBAAZA,eAAA,CAAc8F,KAAK;cAAEgB;YAAU,CAAC,CAAC;YAChG;UACF,KAAK,KAAK;YAAG;YACX,MAAMM,YAAY,GAAGN,SAAS,CAACK,UAAU,CAAC,CAAE;YAC5C,MAAME,QAAQ,GAAGP,SAAS,CAACQ,UAAU,CAAC,CAAM;YAC5CT,iBAAiB,CAACtF,QAAQ,EAAE,QAAQgG,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE;cAAEvB,KAAK,GAAA7F,eAAA,GAAEC,OAAO,CAAC9P,IAAI,cAAA6P,eAAA,uBAAZA,eAAA,CAAc6F,KAAK;cAAEgB;YAAU,CAAC,CAAC;YACjJ;QACJ;QAEA;MACF;MACA;MACA;MACA;MACA;MACA;MACA;IAEF,CAAC,CAAC,OAAOhW,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEyO,OAAO,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMiI,cAAc,GAAGA,CAAA,KAAM;IAC3BhY,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAMgY,KAAK,GAAG,QAAQ/Y,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnES,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEgY,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBpY,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAEDiY,EAAE,CAACG,SAAS,GAAI5B,KAAK,IAAK;MACxB,IAAI;QACF,MAAM1G,OAAO,GAAGY,IAAI,CAACC,KAAK,CAAC6F,KAAK,CAAC7V,IAAI,CAAC;;QAEtC;QACA,IAAImP,OAAO,CAACyB,IAAI,KAAK,SAAS,EAAE;UAC9BxR,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE8P,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACyB,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIzB,OAAO,CAACyB,IAAI,KAAK,SAAS,IAAIzB,OAAO,CAAC1D,KAAK,IAAI0D,OAAO,CAACW,OAAO,EAAE;UAClE;UACA,IAAIX,OAAO,CAACuI,SAAS,EAAE;YACrB,IAAItR,mBAAmB,CAAC1E,GAAG,CAACyN,OAAO,CAACuI,SAAS,CAAC,EAAE;cAC9C;cACA;YACF;;YAEA;YACAtR,mBAAmB,CAAC5C,GAAG,CAAC2L,OAAO,CAACuI,SAAS,CAAC;;YAE1C;YACA,IAAItR,mBAAmB,CAACuR,IAAI,GAAG,IAAI,EAAE;cACnC;cACA,MAAMC,QAAQ,GAAGzX,KAAK,CAAC0X,IAAI,CAACzR,mBAAmB,CAAC;cAChD,KAAK,IAAIgI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;gBAC5BhI,mBAAmB,CAAClC,MAAM,CAAC0T,QAAQ,CAACxJ,CAAC,CAAC,CAAC;cACzC;YACF;UACF;;UAEA;UACAc,iBAAiB,CAACC,OAAO,CAAC1D,KAAK,EAAEsE,IAAI,CAAC+H,SAAS,CAAC3I,OAAO,CAACW,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOpP,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAED4W,EAAE,CAACS,OAAO,GAAIrX,KAAK,IAAK;MACtBtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAED4W,EAAE,CAACU,OAAO,GAAG,MAAM;MACjB5Y,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACA2P,UAAU,CAACoI,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACAlQ,aAAa,CAACmF,OAAO,GAAGiL,EAAE;EAC5B,CAAC;EAED1b,SAAS,CAAC,MAAM;IACd,IAAI,CAACiL,YAAY,CAACwF,OAAO,EAAE;;IAE3B;IACA4L,aAAa,CAAC,CAAC;;IAEf;IACApa,KAAK,GAAG,IAAI7B,KAAK,CAACkc,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAInc,KAAK,CAACoc,iBAAiB,CACxC,EAAE,EACF5Z,MAAM,CAAC6Z,UAAU,GAAG7Z,MAAM,CAAC8Z,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAACnP,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChC0W,MAAM,CAAC7K,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBtD,SAAS,CAACqC,OAAO,GAAG8L,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAIvc,KAAK,CAACwc,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAACla,MAAM,CAAC6Z,UAAU,EAAE7Z,MAAM,CAAC8Z,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAACpa,MAAM,CAACqa,gBAAgB,CAAC;IAC/ChS,YAAY,CAACwF,OAAO,CAACyM,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAIhd,KAAK,CAACid,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5Dpb,KAAK,CAAC2F,GAAG,CAACwV,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAIld,KAAK,CAACmd,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAAClQ,QAAQ,CAACvH,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1C5D,KAAK,CAAC2F,GAAG,CAAC0V,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAIpd,KAAK,CAACmd,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAACpQ,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3C5D,KAAK,CAAC2F,GAAG,CAAC4V,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAIrd,KAAK,CAACsd,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAACrQ,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChC4X,SAAS,CAACE,KAAK,GAAGjX,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7B8W,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACxX,QAAQ,GAAG,GAAG;IACxBhE,KAAK,CAAC2F,GAAG,CAAC6V,SAAS,CAAC;;IAEpB;IACA7b,QAAQ,GAAG,IAAItB,aAAa,CAACic,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzDvb,QAAQ,CAACkc,aAAa,GAAG,IAAI;IAC7Blc,QAAQ,CAACmc,aAAa,GAAG,IAAI;IAC7Bnc,QAAQ,CAACoc,kBAAkB,GAAG,KAAK;IACnCpc,QAAQ,CAACgQ,WAAW,GAAG,EAAE;IACzBhQ,QAAQ,CAACiQ,WAAW,GAAG,GAAG;IAC1BjQ,QAAQ,CAACkQ,aAAa,GAAGpL,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC/E,QAAQ,CAACmQ,aAAa,GAAG,CAAC;IAC1BnQ,QAAQ,CAAC4P,MAAM,CAAC3L,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BjE,QAAQ,CAAC+P,MAAM,CAAC,CAAC;;IAEjB;IACAnO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnB8Y,MAAM,EAAE,CAAC,CAACA,MAAM;MAChB3a,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBwM,SAAS,EAAE,CAAC,CAACA,SAAS,CAACqC;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMwN,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAIhe,UAAU,CAAC,CAAC;QACtCge,aAAa,CAACC,IAAI,CAChB,GAAGlb,QAAQ,uBAAuB,EACjCmb,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAACtc,KAAK;;UAE/B;UACA,MAAMwc,gBAAgB,GAAG,IAAIre,KAAK,CAACse,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAAC3W,QAAQ,CAAEkP,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAAC/M,MAAM,EAAE;cAChB;cACA,IAAI+M,KAAK,CAAC5M,QAAQ,EAAE;gBAClB;gBACA,MAAM6M,WAAW,GAAG,IAAI5W,KAAK,CAACue,oBAAoB,CAAC;kBACjDtP,KAAK,EAAE,QAAQ;kBAAO;kBACtBuP,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAI/H,KAAK,CAAC5M,QAAQ,CAAClB,GAAG,EAAE;kBACtB+N,WAAW,CAAC/N,GAAG,GAAG8N,KAAK,CAAC5M,QAAQ,CAAClB,GAAG;gBACtC;;gBAEA;gBACA8N,KAAK,CAAC5M,QAAQ,GAAG6M,WAAW;gBAE5BxT,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsT,KAAK,CAACtE,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAM+L,YAAY,CAACO,QAAQ,CAACpW,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMoO,KAAK,GAAGyH,YAAY,CAACO,QAAQ,CAAC,CAAC,CAAC;YACtCN,gBAAgB,CAAC7W,GAAG,CAACmP,KAAK,CAAC;UAC7B;;UAEA;UACA9U,KAAK,CAAC2F,GAAG,CAAC6W,gBAAgB,CAAC;;UAE3B;UACArd,gBAAgB,GAAGqd,gBAAgB;UAEnCjb,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9Bub,kBAAkB,CAAC,IAAI,CAAC;UACxBb,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAQ,GAAG,IAAK;UACPzb,OAAO,CAACC,GAAG,CAAC,aAAa,CAACwb,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAE/Y,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDgY,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMgB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA;;QAEA;QACA5D,cAAc,CAAC,CAAC;;QAEhB;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAEF,CAAC,CAAC,OAAO1W,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAMua,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAIrB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMoB,WAAW,GAAIC,WAAW,IAAK;UACnCjc,OAAO,CAACC,GAAG,CAAC,WAAW6b,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAIrf,UAAU,CAAC,CAAC;UAC/Bqf,MAAM,CAACpB,IAAI,CACTgB,GAAG,EACFf,IAAI,IAAK;YACR/a,OAAO,CAACC,GAAG,CAAC,WAAW6b,GAAG,EAAE,CAAC;YAC7BnB,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAU,GAAG,IAAK;YACPzb,OAAO,CAACC,GAAG,CAAC,SAAS,CAACwb,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAE/Y,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAtB,KAAK,IAAK;YACTtB,OAAO,CAACsB,KAAK,CAAC,SAASwa,GAAG,EAAE,EAAExa,KAAK,CAAC;YACpC,IAAI2a,WAAW,GAAG,CAAC,EAAE;cACnBjc,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3B2P,UAAU,CAAC,MAAMoM,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACLrB,MAAM,CAACtZ,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAED0a,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAIrf,UAAU,CAAC,CAAC;IAC/Bqf,MAAM,CAACpB,IAAI,CACT,GAAGlb,QAAQ,4BAA4B,EACvC,MAAOmb,IAAI,IAAK;MACd,IAAI;QACF,MAAM5W,KAAK,GAAG4W,IAAI,CAACtc,KAAK;QACxB0F,KAAK,CAACiO,KAAK,CAAC/P,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxB8B,KAAK,CAACyF,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAE3B;QACA,IAAI5D,KAAK,EAAE;UACXA,KAAK,CAAC2F,GAAG,CAACD,KAAK,CAAC;;UAEhB;UACA,MAAMyX,eAAe,CAAC,CAAC;QACvB,CAAC,MAAM;UACL5b,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC;QAChC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACAma,GAAG,IAAK;MACPzb,OAAO,CAACC,GAAG,CAAC,SAAS,CAACwb,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAE/Y,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAtB,KAAK,IAAK;MACTtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtB,OAAO,CAACsB,KAAK,CAAC,OAAO,EAAE;QACrB6a,IAAI,EAAE7a,KAAK,CAACkQ,IAAI;QAChB4K,IAAI,EAAE9a,KAAK,CAACyO,OAAO;QACnBsM,KAAK,EAAE,GAAGzc,QAAQ,4BAA4B;QAC9C0c,KAAK,EAAE,GAAG1c,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAM2c,OAAO,GAAGA,CAAA,KAAM;MACpBxU,iBAAiB,CAACkF,OAAO,GAAGuP,qBAAqB,CAACD,OAAO,CAAC;;MAE1D;MACAvf,KAAK,CAACmR,MAAM,CAAC,CAAC;;MAEd;MACA,MAAMsO,SAAS,GAAGlc,KAAK,CAACmc,QAAQ,CAAC,CAAC;;MAElC;MACA,IAAInU,qBAAqB,CAACgQ,IAAI,GAAG,CAAC,EAAE;QAClChQ,qBAAqB,CAAC3D,OAAO,CAAEV,KAAK,IAAK;UACvCA,KAAK,CAACiK,MAAM,CAACsO,SAAS,CAAC;QACzB,CAAC,CAAC;MACJ;MAEA,IAAIte,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAAC8O,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMyP,UAAU,GAAG/e,gBAAgB,CAACgM,QAAQ,CAAC9H,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAM8a,eAAe,GAAGhf,gBAAgB,CAACuU,QAAQ,CAACjQ,CAAC;;QAEnD;QACA;QACA,MAAM2a,gBAAgB,GAAG,EAAED,eAAe,GAAG1Z,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;;QAEnE;QACA,MAAM2Z,YAAY,GAAG,IAAIlgB,KAAK,CAACkG,OAAO,CACpC,CAAC,EAAE,GAAGI,IAAI,CAAC6Z,GAAG,CAACF,gBAAgB,CAAC,EAChC,GAAG,EACH,CAAC,EAAE,GAAG3Z,IAAI,CAAC8Z,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA,MAAMI,oBAAoB,GAAGN,UAAU,CAAC7a,KAAK,CAAC,CAAC,CAACsC,GAAG,CAAC0Y,YAAY,CAAC;QACjE,MAAMI,YAAY,GAAGP,UAAU,CAAC7a,KAAK,CAAC,CAAC;;QAEvC;QACA,IAAI,CAACmG,kBAAkB,CAACgF,OAAO,EAAE;UAC/BhF,kBAAkB,CAACgF,OAAO,GAAGgQ,oBAAoB,CAACnb,KAAK,CAAC,CAAC;QAC3D;QAEA,IAAI,CAACoG,gBAAgB,CAAC+E,OAAO,EAAE;UAC7B/E,gBAAgB,CAAC+E,OAAO,GAAGiQ,YAAY,CAACpb,KAAK,CAAC,CAAC;QACjD;;QAEA;QACAmG,kBAAkB,CAACgF,OAAO,CAACkQ,IAAI,CAACF,oBAAoB,EAAE,CAAC,GAAG9U,eAAe,CAAC;QAC1ED,gBAAgB,CAAC+E,OAAO,CAACkQ,IAAI,CAACD,YAAY,EAAE,CAAC,GAAG/U,eAAe,CAAC;;QAEhE;QACA4Q,MAAM,CAACnP,QAAQ,CAACkE,IAAI,CAAC7F,kBAAkB,CAACgF,OAAO,CAAC;;QAEhD;QACA8L,MAAM,CAACzL,EAAE,CAACjL,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA0W,MAAM,CAAC7K,MAAM,CAAChG,gBAAgB,CAAC+E,OAAO,CAAC;;QAEvC;QACA8L,MAAM,CAACqE,sBAAsB,CAAC,CAAC;QAC/BrE,MAAM,CAACvK,YAAY,CAAC,CAAC;QACrBuK,MAAM,CAACtK,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACArQ,QAAQ,CAAC8O,OAAO,GAAG,KAAK;;QAExB;QACA9O,QAAQ,CAAC4P,MAAM,CAACF,IAAI,CAAC5F,gBAAgB,CAAC+E,OAAO,CAAC;QAC9C7O,QAAQ,CAAC+P,MAAM,CAAC,CAAC;QAEjBnO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnBod,IAAI,EAAEV,UAAU,CAAClN,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAEuJ,MAAM,CAACnP,QAAQ,CAAC6F,OAAO,CAAC,CAAC;UAC/B6N,IAAI,EAAEpV,gBAAgB,CAAC+E,OAAO,CAACwC,OAAO,CAAC,CAAC;UACxC8N,IAAI,EAAExE,MAAM,CAACyE,iBAAiB,CAAC,IAAI5gB,KAAK,CAACkG,OAAO,CAAC,CAAC,CAAC,CAAC2M,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAItR,UAAU,KAAK,QAAQ,EAAE;QAClC;QACA8J,kBAAkB,CAACgF,OAAO,GAAG,IAAI;QACjC/E,gBAAgB,CAAC+E,OAAO,GAAG,IAAI;;QAE/B;QACA7O,QAAQ,CAAC8O,OAAO,GAAG,IAAI;;QAEvB;QACA6L,MAAM,CAACzL,EAAE,CAACjL,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAIa,IAAI,CAACK,GAAG,CAACwV,MAAM,CAACnP,QAAQ,CAAC1H,CAAC,CAAC,GAAG,EAAE,EAAE;UACpC6W,MAAM,CAACnP,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BjE,QAAQ,CAAC4P,MAAM,CAAC3L,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B0W,MAAM,CAAC7K,MAAM,CAAC9P,QAAQ,CAAC4P,MAAM,CAAC;UAC9B5P,QAAQ,CAAC+P,MAAM,CAAC,CAAC;QACnB;;QAEA;QACA;QACA4K,MAAM,CAACvK,YAAY,CAAC,CAAC;QACrBuK,MAAM,CAACtK,iBAAiB,CAAC,IAAI,CAAC;MAEhC,CAAC,MAAM,IAAItQ,UAAU,KAAK,cAAc,EAAE;QACxC;QACA8J,kBAAkB,CAACgF,OAAO,GAAG,IAAI;QACjC/E,gBAAgB,CAAC+E,OAAO,GAAG,IAAI;;QAE/B;QACA7O,QAAQ,CAAC+P,MAAM,CAAC,CAAC;MACnB;MAEA,IAAI/P,QAAQ,EAAEA,QAAQ,CAAC+P,MAAM,CAAC,CAAC;MAC/B,IAAI1P,KAAK,IAAIsa,MAAM,EAAE;QACnBI,QAAQ,CAACsE,MAAM,CAAChf,KAAK,EAAEsa,MAAM,CAAC;MAChC;IACF,CAAC;IAEDwD,OAAO,CAAC,CAAC;;IAET;IACA,MAAMmB,YAAY,GAAGA,CAAA,KAAM;MACzB3E,MAAM,CAAC4E,MAAM,GAAGve,MAAM,CAAC6Z,UAAU,GAAG7Z,MAAM,CAAC8Z,WAAW;MACtDH,MAAM,CAACqE,sBAAsB,CAAC,CAAC;MAC/BjE,QAAQ,CAACG,OAAO,CAACla,MAAM,CAAC6Z,UAAU,EAAE7Z,MAAM,CAAC8Z,WAAW,CAAC;IACzD,CAAC;IACD9Z,MAAM,CAACwe,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACA,MAAMG,oBAAoB,GAAGC,WAAW,CAAC,MAAM;MAC7CC,oBAAoB,CAAC,CAAC;IACxB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX;IACA3e,MAAM,CAAC4e,aAAa,GAAG,MAAM;MAC3B,IAAIpT,SAAS,CAACqC,OAAO,EAAE;QACrBrC,SAAS,CAACqC,OAAO,CAACrD,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzCuI,SAAS,CAACqC,OAAO,CAACiB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCtD,SAAS,CAACqC,OAAO,CAACuB,YAAY,CAAC,CAAC;QAChC5D,SAAS,CAACqC,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAIrQ,QAAQ,EAAE;UACZA,QAAQ,CAAC4P,MAAM,CAAC3L,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BjE,QAAQ,CAAC8O,OAAO,GAAG,IAAI;UACvB9O,QAAQ,CAAC+P,MAAM,CAAC,CAAC;QACnB;QAEAhQ,UAAU,GAAG,QAAQ;QACrB6B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA;IACA;;IAEA;IACA;;IAEA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAb,MAAM,CAAC6e,eAAe,GAAG,MAAM;MAC7Bje,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B8d,oBAAoB,CAAC,CAAC;IACxB,CAAC;;IAED;IACA,OAAO,MAAM;MACX/d,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAI8H,iBAAiB,CAACkF,OAAO,EAAE;QAC7BiR,oBAAoB,CAACnW,iBAAiB,CAACkF,OAAO,CAAC;QAC/ClF,iBAAiB,CAACkF,OAAO,GAAG,IAAI;MAClC;;MAEA;MACA,IAAI4Q,oBAAoB,EAAE;QACxBM,aAAa,CAACN,oBAAoB,CAAC;MACrC;;MAEA;MACA1W,YAAY,CAACvC,OAAO,CAAEwZ,MAAM,IAAK;QAC/B,IAAI3f,KAAK,IAAI2f,MAAM,EAAE;UACnB3f,KAAK,CAAC2H,MAAM,CAACgY,MAAM,CAAC;QACtB;MACF,CAAC,CAAC;MACFjX,YAAY,CAAClB,KAAK,CAAC,CAAC;MACpBgB,cAAc,CAAC9B,MAAM,GAAG,CAAC;;MAEzB;MACAnI,KAAK,CAACqhB,SAAS,CAAC,CAAC;;MAEjB;MACA9V,qBAAqB,CAAC3D,OAAO,CAACV,KAAK,IAAI;QACrCN,eAAe,CAACe,WAAW,CAACT,KAAK,CAAC;MACpC,CAAC,CAAC;MACFqE,qBAAqB,CAACtC,KAAK,CAAC,CAAC;;MAE7B;MACArC,eAAe,CAACoC,OAAO,CAAC,CAAC;;MAEzB;MACA,IAAIvH,KAAK,EAAE;QACT,MAAM6f,eAAe,GAAG,EAAE;QAC1B7f,KAAK,CAAC4F,QAAQ,CAAEC,MAAM,IAAK;UACzB,IAAIA,MAAM,CAACkC,MAAM,EAAE;YACjB,IAAIlC,MAAM,CAACmC,QAAQ,EAAE;cACnBnC,MAAM,CAACmC,QAAQ,CAACC,OAAO,CAAC,CAAC;YAC3B;YACA,IAAIpC,MAAM,CAACqC,QAAQ,EAAE;cACnB,IAAI5F,KAAK,CAACC,OAAO,CAACsD,MAAM,CAACqC,QAAQ,CAAC,EAAE;gBAClCrC,MAAM,CAACqC,QAAQ,CAAC/B,OAAO,CAAC+B,QAAQ,IAAI;kBAClC,IAAIA,QAAQ,CAAClB,GAAG,EAAEkB,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;kBACxCC,QAAQ,CAACD,OAAO,CAAC,CAAC;gBACpB,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL,IAAIpC,MAAM,CAACqC,QAAQ,CAAClB,GAAG,EAAEnB,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC;gBACtDpC,MAAM,CAACqC,QAAQ,CAACD,OAAO,CAAC,CAAC;cAC3B;YACF;YACA,IAAIpC,MAAM,KAAK7F,KAAK,EAAE;cACpB6f,eAAe,CAAC3I,IAAI,CAACrR,MAAM,CAAC;YAC9B;UACF;QACF,CAAC,CAAC;;QAEF;QACAga,eAAe,CAAC1Z,OAAO,CAAC2Z,GAAG,IAAI;UAC7B,IAAIA,GAAG,CAACpY,MAAM,EAAE;YACdoY,GAAG,CAACpY,MAAM,CAACC,MAAM,CAACmY,GAAG,CAAC;UACxB;QACF,CAAC,CAAC;QAEF9f,KAAK,CAACwH,KAAK,CAAC,CAAC;MACf;;MAEA;MACA,IAAIkT,QAAQ,EAAE;QACZA,QAAQ,CAACqF,gBAAgB,CAAC,IAAI,CAAC;QAC/B,IAAI/W,YAAY,CAACwF,OAAO,IAAIkM,QAAQ,CAACQ,UAAU,IAAIR,QAAQ,CAACQ,UAAU,CAAC8E,UAAU,KAAKhX,YAAY,CAACwF,OAAO,EAAE;UAC1GxF,YAAY,CAACwF,OAAO,CAACyR,WAAW,CAACvF,QAAQ,CAACQ,UAAU,CAAC;QACvD;QACAR,QAAQ,CAACzS,OAAO,CAAC,CAAC;QAClByS,QAAQ,CAACwF,gBAAgB,CAAC,CAAC;MAC7B;;MAEA;MACA,IAAIvgB,QAAQ,EAAE;QACZA,QAAQ,CAACsI,OAAO,CAAC,CAAC;MACpB;;MAEA;MACA3H,oBAAoB,CAACkH,KAAK,CAAC,CAAC;MAC5BhH,oBAAoB,CAACgH,KAAK,CAAC,CAAC;MAC5B9F,gBAAgB,CAAC8F,KAAK,CAAC,CAAC;MACxB/F,aAAa,CAAC+F,KAAK,CAAC,CAAC;MACrB5F,gBAAgB,CAAC4F,KAAK,CAAC,CAAC;MACxB3F,kBAAkB,CAAC2F,KAAK,CAAC,CAAC;MAE1BjG,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzD,SAAS,CAAC,MAAM;IACd;IACAiE,qBAAqB,CAAC,CAAC;;IAEvB;IACA,MAAMme,uBAAuB,GAAGA,CAAA,KAAM;MACpC5e,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCQ,qBAAqB,CAAC,CAAC;IACzB,CAAC;;IAED;IACArB,MAAM,CAACwe,gBAAgB,CAAC,oBAAoB,EAAEgB,uBAAuB,CAAC;;IAEtE;IACA,MAAMC,UAAU,GAAGf,WAAW,CAAC,MAAM;MACnCrd,qBAAqB,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC;;IAET;IACA,OAAO,MAAM;MACXrB,MAAM,CAAC0f,mBAAmB,CAAC,oBAAoB,EAAEF,uBAAuB,CAAC;MACzET,aAAa,CAACU,UAAU,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAriB,SAAS,CAAC,MAAM;IACd;IACA,IAAIiC,KAAK,IAAIkJ,SAAS,CAACsF,OAAO,IAAIjE,aAAa,IAAIA,aAAa,CAAC7D,MAAM,GAAG,CAAC,EAAE;MAC3E;MACA,MAAM4Z,KAAK,GAAGnP,UAAU,CAAC,MAAM;QAC7B,IAAInR,KAAK,IAAIkJ,SAAS,CAACsF,OAAO,IAAIjE,aAAa,IAAIA,aAAa,CAAC7D,MAAM,GAAG,CAAC,EAAE;UAAG;UAC9E6Z,mBAAmB,CAACrX,SAAS,CAACsF,OAAO,EAAEjE,aAAa,CAAC;QACvD;MACF,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAMiW,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM;MACL/e,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IAC1C;EACF,CAAC,EAAE,CAACxB,KAAK,EAAEuK,aAAa,CAAC,CAAC;;EAE1B;EACAxM,SAAS,CAAC,MAAM;IACd,IAAIiL,YAAY,CAACwF,OAAO,EAAE;MACxB;MACA,MAAMiS,WAAW,GAAIzI,KAAK,IAAK;QAC7B,IAAIhY,KAAK,IAAImM,SAAS,CAACqC,OAAO,EAAE;UAC9BkS,gBAAgB,CAAC1I,KAAK,EAAEhP,YAAY,CAACwF,OAAO,EAAExO,KAAK,EAAEmM,SAAS,CAACqC,OAAO,CAAC;QACzE;MACF,CAAC;;MAED;MACAxF,YAAY,CAACwF,OAAO,CAAC2Q,gBAAgB,CAAC,OAAO,EAAEsB,WAAW,CAAC;;MAE3D;MACAlf,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,CAAC,CAACwH,YAAY,CAACwF,OAAO,CAAC;;MAEpD;MACA,OAAO,MAAM;QACX,IAAIxF,YAAY,CAACwF,OAAO,EAAE;UACxBxF,YAAY,CAACwF,OAAO,CAAC6R,mBAAmB,CAAC,OAAO,EAAEI,WAAW,CAAC;UAC9Dlf,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACxB,KAAK,EAAEmM,SAAS,CAACqC,OAAO,CAAC,CAAC;;EAE9B;EACA,MAAMmS,SAAS,GAAGziB,WAAW,CAAC,MAAM;IAClCqD,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B;EACF,CAAC,EAAE,CAACwH,YAAY,EAAEwE,aAAa,EAAE5L,gBAAgB,CAAC,CAAC;;EAEnD;EACA,MAAMgf,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAM5Y,QAAQ,GAAG,IAAI7J,KAAK,CAAC0iB,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChD,MAAM3Y,QAAQ,GAAG,IAAI/J,KAAK,CAAC2iB,iBAAiB,CAAC;MAAE1T,KAAK,EAAE;IAAS,CAAC,CAAC;IACjE,MAAMiK,iBAAiB,GAAG,IAAIlZ,KAAK,CAAC4iB,IAAI,CAAC/Y,QAAQ,EAAEE,QAAQ,CAAC;;IAE5D;IACA,MAAM8Y,YAAY,GAAG,IAAI7iB,KAAK,CAAC8iB,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC5D,MAAMC,YAAY,GAAG,IAAI/iB,KAAK,CAAC2iB,iBAAiB,CAAC;MAAE1T,KAAK,EAAE;IAAS,CAAC,CAAC;IACrE,MAAM+T,SAAS,GAAG,IAAIhjB,KAAK,CAAC4iB,IAAI,CAACC,YAAY,EAAEE,YAAY,CAAC;IAC5DC,SAAS,CAAChW,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAClCyT,iBAAiB,CAAC1R,GAAG,CAACwb,SAAS,CAAC;IAEhC,OAAO9J,iBAAiB;EAC1B,CAAC;;EAED;EACA,MAAM+J,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACphB,KAAK,EAAE;;IAEZ;IACA4B,gBAAgB,CAACuE,OAAO,CAAC,CAACkb,QAAQ,EAAE5U,OAAO,KAAK;MAC9C,IAAI4U,QAAQ,CAAC3b,KAAK,EAAE;QAClB;QACA,MAAM4b,cAAc,GAAG,IAAInjB,KAAK,CAACojB,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxD,MAAMC,cAAc,GAAG,IAAIrjB,KAAK,CAAC2iB,iBAAiB,CAAC;UACjD1T,KAAK,EAAE,QAAQ;UAAC;UAChB8H,WAAW,EAAE,KAAK;UAClBU,OAAO,EAAE,GAAG;UAAG;UACf6L,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,MAAMC,UAAU,GAAG,IAAIvjB,KAAK,CAAC4iB,IAAI,CAACO,cAAc,EAAEE,cAAc,CAAC;QACjEE,UAAU,CAACvW,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE;;QAEnC;QACA8d,UAAU,CAACC,QAAQ,GAAG;UACpB5O,IAAI,EAAE,cAAc;UACpBtG,OAAO,EAAEA,OAAO;UAChB+D,IAAI,EAAE6Q,QAAQ,CAAC/Q,YAAY,CAACE,IAAI;UAChCoR,aAAa,EAAE;QACjB,CAAC;;QAED;QACAP,QAAQ,CAAC3b,KAAK,CAACC,GAAG,CAAC+b,UAAU,CAAC;QAE9BngB,OAAO,CAACC,GAAG,CAAC,OAAO6f,QAAQ,CAAC/Q,YAAY,CAACE,IAAI,KAAK/D,OAAO,aAAa,CAAC;MACzE;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA1O,SAAS,CAAC,MAAM;IACd;IACA,MAAMuiB,KAAK,GAAGnP,UAAU,CAAC,MAAM;MAC7B,IAAIvP,gBAAgB,CAACkY,IAAI,GAAG,CAAC,EAAE;QAC7BvY,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1B;MACF;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAE;;IAEX,OAAO,MAAMgf,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACAviB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACA,IAAI8O,0BAA0B,CAAC2B,OAAO,EAAE;QACtCkR,aAAa,CAAC7S,0BAA0B,CAAC2B,OAAO,CAAC;QACjD3B,0BAA0B,CAAC2B,OAAO,GAAG,IAAI;QACzCjN,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC9B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAzD,SAAS,CAAC,MAAM;IACd,IAAI,CAACuO,mBAAmB,CAACE,OAAO,IAAIK,0BAA0B,CAAC2B,OAAO,EAAE;MACtEkR,aAAa,CAAC7S,0BAA0B,CAAC2B,OAAO,CAAC;MACjD3B,0BAA0B,CAAC2B,OAAO,GAAG,IAAI;MACzC5B,mBAAmB,CAAC4B,OAAO,GAAG,IAAI;MAClCjN,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACnC;EACF,CAAC,EAAE,CAAC8K,mBAAmB,CAACE,OAAO,CAAC,CAAC;;EAEjC;EACAzO,SAAS,CAAC,MAAM;IACd;IACA,IAAIwM,aAAa,IAAIA,aAAa,CAAC7D,MAAM,GAAG,CAAC,EAAE;MAC7C;MACA,IAAI,CAAC0F,oBAAoB,EAAE;QACzB;QACA,MAAMyV,6BAA6B,GAAGtX,aAAa,CAAC9H,IAAI,CACtD6N,YAAY,IAAIA,YAAY,CAACY,eAAe,KAAK,KAAK,IAAIZ,YAAY,CAAC7D,OACzE,CAAC;;QAED;QACA,MAAMqV,kBAAkB,GAAGD,6BAA6B,IAAItX,aAAa,CAAC,CAAC,CAAC;QAE5EhJ,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEsgB,kBAAkB,CAACtR,IAAI,EAClC,QAAQ,EAAEqR,6BAA6B,GAAG,GAAG,GAAG,GAAG,CAAC;;QAEhE;QACA,MAAMvB,KAAK,GAAGnP,UAAU,CAAC,MAAM;UAC7Bf,wBAAwB,CAAC0R,kBAAkB,CAACtR,IAAI,CAAC;QACnD,CAAC,EAAE,IAAI,CAAC;QAER,OAAO,MAAMgQ,YAAY,CAACF,KAAK,CAAC;MAClC;IACF;EACF,CAAC,EAAE,CAAC/V,aAAa,EAAE6B,oBAAoB,CAAC,CAAC;;EAEzC;EACA,MAAM2V,yBAAyB,GAAIC,iBAAiB,IAAK;IACvD,IAAI,CAAChiB,KAAK,IAAI,OAAOA,KAAK,CAAC4F,QAAQ,KAAK,UAAU,IAAI,CAACoc,iBAAiB,EAAE;MACxEzgB,OAAO,CAAC0gB,IAAI,CAAC,cAAc,EAAE;QAC3BjiB,KAAK,EAAE,CAAC,CAACA,KAAK;QACdkiB,aAAa,EAAEliB,KAAK,IAAI,OAAOA,KAAK,CAAC4F,QAAQ,KAAK,UAAU;QAC5DsD,SAAS,EAAE,CAAC,CAAC8Y;MACf,CAAC,CAAC;MACF;IACF;IAEA,IAAI,CAAChY,WAAW,CAACE,OAAO,IAAIF,WAAW,CAACE,OAAO,CAACxD,MAAM,KAAK,CAAC,EAAE;MAC5DnF,OAAO,CAAC0gB,IAAI,CAAC,kBAAkB,CAAC;MAChC,OAAO,CAAC;IACV;IAEA,IAAI,CAAC1X,aAAa,IAAIA,aAAa,CAAC7D,MAAM,KAAK,CAAC,EAAE;MAChDnF,OAAO,CAAC0gB,IAAI,CAAC,kBAAkB,CAAC;MAChC;IACF;IAEA1gB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;MACvB2gB,IAAI,EAAEnY,WAAW,CAACE,OAAO,CAACxD,MAAM;MAChC0b,IAAI,EAAE7X,aAAa,CAAC7D;IACtB,CAAC,CAAC;IAEF,IAAI;MACF;MACA,MAAM2b,aAAa,GAAGriB,KAAK,CAAC8c,QAAQ,CAAC3V,MAAM,CAAC2Y,GAAG,IAAIA,GAAG,CAAC6B,QAAQ,IAAI7B,GAAG,CAAC6B,QAAQ,CAACW,qBAAqB,CAAC;MACtGD,aAAa,CAAClc,OAAO,CAAC2Z,GAAG,IAAI9f,KAAK,CAAC2H,MAAM,CAACmY,GAAG,CAAC,CAAC;MAC/Cve,OAAO,CAACC,GAAG,CAAC,KAAK,EAAE6gB,aAAa,CAAC3b,MAAM,EAAE,SAAS,CAAC;MAEnD,IAAI6b,oBAAoB,GAAG,CAAC;MAE5BhY,aAAa,CAACpE,OAAO,CAACmK,YAAY,IAAI;QACpC,IAAI,CAACA,YAAY,CAACkS,SAAS,IAAI,CAAClgB,KAAK,CAACC,OAAO,CAAC+N,YAAY,CAACkS,SAAS,CAAC,EAAE;QACvElS,YAAY,CAACkS,SAAS,CAACrc,OAAO,CAAEsc,QAAQ,IAAK;UAC3C,IAAI,CAACA,QAAQ,CAAC7X,SAAS,IAAI,CAAC6X,QAAQ,CAAC5X,QAAQ,IAAI6X,KAAK,CAAC/R,UAAU,CAAC8R,QAAQ,CAAC7X,SAAS,CAAC,CAAC,IAAI8X,KAAK,CAAC/R,UAAU,CAAC8R,QAAQ,CAAC5X,QAAQ,CAAC,CAAC,EAAE;YAC9H;UACF;UAEA,MAAMX,OAAO,GAAGF,WAAW,CAACE,OAAO,CAAC/C,MAAM,CACxCwb,CAAC,IAAIA,CAAC,CAAC/hB,QAAQ,KAAK0P,YAAY,CAACE,IAAI,IAAImS,CAAC,CAACF,QAAQ,KAAKA,QAAQ,CAACjS,IACnE,CAAC;UACD,IAAItG,OAAO,CAACxD,MAAM,KAAK,CAAC,EAAE;;UAE1B;UACAnF,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE8O,YAAY,CAACE,IAAI,EAAE,IAAI,EAAEiS,QAAQ,CAACjS,IAAI,EAAE,MAAM,EAAEtG,OAAO,CAACxD,MAAM,CAAC;UACnF,MAAM4M,QAAQ,GAAG0O,iBAAiB,CAACtR,YAAY,CAC7CC,UAAU,CAAC8R,QAAQ,CAAC7X,SAAS,CAAC,EAC9B+F,UAAU,CAAC8R,QAAQ,CAAC5X,QAAQ,CAC9B,CAAC;UACD;UACA,MAAM+X,KAAK,GAAG,IAAIzkB,KAAK,CAACse,KAAK,CAAC,CAAC;UAC/BmG,KAAK,CAACzX,QAAQ,CAACvH,GAAG,CAAC0P,QAAQ,CAAC/P,CAAC,EAAE,EAAE,EAAE,CAAC+P,QAAQ,CAAC7P,CAAC,CAAC,CAAC,CAAC;UACjDmf,KAAK,CAACjB,QAAQ,GAAG;YAAEW,qBAAqB,EAAE;UAAK,CAAC;UAChD;UACA,MAAMO,QAAQ,GAAG,EAAE,CAAC,CAAC;UACrB,MAAMC,WAAW,GAAG,CAAC,CAAC,CAAC;UACvB,MAAMC,UAAU,GAAG7Y,OAAO,CAACxD,MAAM,GAAGmc,QAAQ,GAAG,CAAC3Y,OAAO,CAACxD,MAAM,GAAG,CAAC,IAAIoc,WAAW;UACjF;UACA,MAAME,MAAM,GAAG,GAAG,CAAC,CAAC;UACpB,MAAMC,SAAS,GAAG,GAAG,CAAC,CAAC;UACvB,MAAMC,MAAM,GAAG,EAAE,CAAChZ,OAAO,CAACxD,MAAM,GAAG,CAAC,KAAKsc,MAAM,GAAGC,SAAS,CAAC,CAAC,GAAG,CAAC;UACjE/Y,OAAO,CAAC/D,OAAO,CAAC,CAAC+H,MAAM,EAAEiV,GAAG,KAAK;YAC/B;YACA,MAAMC,aAAa,GAAG,IAAIjlB,KAAK,CAACklB,aAAa,CAAC,CAAC;YAC/C,MAAMC,QAAQ,GAAG,GAAGniB,QAAQ,WAAW+M,MAAM,CAAC6E,IAAI,MAAM;YACxD;YACA,MAAMwQ,YAAY,GAAG,IAAIplB,KAAK,CAAC2iB,iBAAiB,CAAC;cAC/C9Z,GAAG,EAAEoc,aAAa,CAAC/G,IAAI,CAACiH,QAAQ,CAAC;cACjCpO,WAAW,EAAE,IAAI;cACjBU,OAAO,EAAE,CAAC,CAAC;YACb,CAAC,CAAC;YACF;YACA,MAAM4N,UAAU,GAAG,IAAIrlB,KAAK,CAAC2iB,iBAAiB,CAAC;cAC7C1T,KAAK,EAAE,QAAQ;cACf8H,WAAW,EAAE,IAAI;cACjBU,OAAO,EAAE,GAAG,CAAC;YACf,CAAC,CAAC;YACF;YACA,MAAM6N,OAAO,GAAGT,MAAM,GAAG,IAAI;YAC7B,MAAMU,QAAQ,GAAGV,MAAM,GAAG,IAAI;YAC9B;YACA;YACA,MAAMW,UAAU,GAAG,IAAIxlB,KAAK,CAACylB,aAAa,CAACH,OAAO,EAAEC,QAAQ,CAAC;YAC7D,MAAMG,MAAM,GAAG,IAAI1lB,KAAK,CAAC4iB,IAAI,CAAC4C,UAAU,EAAEH,UAAU,CAAC;YACrD;YACA,MAAMM,YAAY,GAAG,IAAI3lB,KAAK,CAACylB,aAAa,CAACZ,MAAM,EAAEA,MAAM,CAAC;YAC5D,MAAMe,QAAQ,GAAG,IAAI5lB,KAAK,CAAC4iB,IAAI,CAAC+C,YAAY,EAAEP,YAAY,CAAC;YAC3D;YACAQ,QAAQ,CAAC5Y,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;YACjC;YACA,MAAMogB,SAAS,GAAG,IAAI7lB,KAAK,CAACse,KAAK,CAAC,CAAC;YACnCuH,SAAS,CAACre,GAAG,CAACke,MAAM,CAAC;YACrBG,SAAS,CAACre,GAAG,CAACoe,QAAQ,CAAC;YACvB;YACAC,SAAS,CAAC7Y,QAAQ,CAACvH,GAAG,CAACsf,MAAM,GAAGC,GAAG,IAAIH,MAAM,GAAGC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACjE;YACAe,SAAS,CAACrO,WAAW,GAAG,GAAG,CAAC,CAAC;YAC7BqO,SAAS,CAACrC,QAAQ,GAAG;cACnB5T,QAAQ,EAAEG,MAAM,CAAC2E,EAAE;cACnBoR,UAAU,EAAE/V,MAAM,CAAC6E,IAAI;cACvB0P,QAAQ,EAAEA,QAAQ,CAACjS,IAAI;cACvB0T,oBAAoB,EAAE;YACxB,CAAC;YACDtB,KAAK,CAACjd,GAAG,CAACqe,SAAS,CAAC;UACtB,CAAC,CAAC;UACF;UACA;UACA,MAAMG,YAAY,GAAIvB,KAAK,CAACzX,QAAQ,CAAC1H,CAAC,CAAC,CAAC;UACxC,MAAM2gB,cAAc,GAAG,IAAIjmB,KAAK,CAAC8iB,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAEkD,YAAY,EAAE,EAAE,CAAC;UAC7E,MAAME,cAAc,GAAG,IAAIlmB,KAAK,CAAC2iB,iBAAiB,CAAC;YAAE1T,KAAK,EAAE,QAAQ;YAAE8H,WAAW,EAAE,IAAI;YAAEU,OAAO,EAAE;UAAI,CAAC,CAAC;UACxG,MAAM0O,MAAM,GAAG,IAAInmB,KAAK,CAAC4iB,IAAI,CAACqD,cAAc,EAAEC,cAAc,CAAC;UAC7D;UACAC,MAAM,CAACnZ,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,CAACugB,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC;UAC5C;UACA;UACAG,MAAM,CAAC3C,QAAQ,GAAG;YAAE4C,sBAAsB,EAAE;UAAK,CAAC;UAClD3B,KAAK,CAACjd,GAAG,CAAC2e,MAAM,CAAC;UACjBtkB,KAAK,CAAC2F,GAAG,CAACid,KAAK,CAAC;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOtb,CAAC,EAAE;MACV/F,OAAO,CAACsB,KAAK,CAAC,iCAAiC,EAAEyE,CAAC,CAAC;MACnD;IACF;EACF,CAAC;;EAED;EACAvJ,SAAS,CAAC,MAAM;IACd,IAAIiC,KAAK,IAAI,OAAOA,KAAK,CAAC4F,QAAQ,KAAK,UAAU,IAAIsD,SAAS,CAACsF,OAAO,IAAIxE,WAAW,CAACE,OAAO,EAAE;MAAA,IAAAsa,oBAAA;MAC7FjjB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvBxB,KAAK,EAAE,CAAC,CAACA,KAAK;QACdkJ,SAAS,EAAE,CAAC,CAACA,SAAS,CAACsF,OAAO;QAC9BiW,YAAY,EAAE,EAAAD,oBAAA,GAAAxa,WAAW,CAACE,OAAO,cAAAsa,oBAAA,uBAAnBA,oBAAA,CAAqB9d,MAAM,KAAI,CAAC;QAC9Cge,kBAAkB,EAAE,CAAAna,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE7D,MAAM,KAAI;MAC/C,CAAC,CAAC;;MAEF;MACA,MAAM4Z,KAAK,GAAGnP,UAAU,CAAC,MAAM;QAC7B4Q,yBAAyB,CAAC7Y,SAAS,CAACsF,OAAO,CAAC;MAC9C,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;MAET,OAAO,MAAMgS,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACtgB,KAAK,EAAEkJ,SAAS,CAACsF,OAAO,EAAExE,WAAW,CAACE,OAAO,EAAEK,aAAa,CAAC,CAAC,CAAC,CAAC;;EAEpE;EACAxM,SAAS,CAAC,MAAM;IACd,IAAIiC,KAAK,IAAIkJ,SAAS,CAACsF,OAAO,IAAIxE,WAAW,CAACE,OAAO,IAAIK,aAAa,EAAE;MACtE;MACA,MAAMoa,UAAU,GAAGxT,UAAU,CAAC,MAAM;QAClC,MAAMkR,aAAa,GAAGriB,KAAK,CAAC8c,QAAQ,CAAC3V,MAAM,CAAC2Y,GAAG,IAAIA,GAAG,CAAC6B,QAAQ,IAAI7B,GAAG,CAAC6B,QAAQ,CAACW,qBAAqB,CAAC;QACtG,IAAID,aAAa,CAAC3b,MAAM,KAAK,CAAC,EAAE;UAC9BnF,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;UAC/BugB,yBAAyB,CAAC7Y,SAAS,CAACsF,OAAO,CAAC;QAC9C,CAAC,MAAM;UACLjN,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE6gB,aAAa,CAAC3b,MAAM,EAAE,MAAM,CAAC;QACzD;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,OAAO,MAAM8Z,YAAY,CAACmE,UAAU,CAAC;IACvC;EACF,CAAC,EAAE,CAAC3kB,KAAK,EAAEkJ,SAAS,CAACsF,OAAO,EAAExE,WAAW,CAACE,OAAO,EAAEK,aAAa,CAAC,CAAC;;EAElE;EACAxM,SAAS,CAAC,MAAM;IACd,IAAI,CAACiC,KAAK,IAAI,CAACmM,SAAS,CAACqC,OAAO,EAAE;IAClC,MAAMoW,gBAAgB,GAAGA,CAAA,KAAM;MAC7B;MACA5kB,KAAK,CAAC8c,QAAQ,CAAC3W,OAAO,CAAC2Z,GAAG,IAAI;QAC5B;QACA,IAAIA,GAAG,CAAC6B,QAAQ,IAAI7B,GAAG,CAAC6B,QAAQ,CAACW,qBAAqB,EAAE;UACtDxC,GAAG,CAACrQ,MAAM,CAACtD,SAAS,CAACqC,OAAO,CAACrD,QAAQ,CAAC5H,CAAC,EAAEuc,GAAG,CAAC3U,QAAQ,CAAC1H,CAAC,EAAE0I,SAAS,CAACqC,OAAO,CAACrD,QAAQ,CAACxH,CAAC,CAAC;QACxF;QACA;QACA,IAAImc,GAAG,CAAC6B,QAAQ,IAAI7B,GAAG,CAAC6B,QAAQ,CAAC5O,IAAI,KAAK,aAAa,EAAE;UACvD+M,GAAG,CAACrQ,MAAM,CAACtD,SAAS,CAACqC,OAAO,CAACrD,QAAQ,CAAC5H,CAAC,EAAEuc,GAAG,CAAC3U,QAAQ,CAAC1H,CAAC,EAAE0I,SAAS,CAACqC,OAAO,CAACrD,QAAQ,CAACxH,CAAC,CAAC;QACxF;MACF,CAAC,CAAC;MACFoa,qBAAqB,CAAC6G,gBAAgB,CAAC;IACzC,CAAC;IACDA,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAAC5kB,KAAK,CAAC,CAAC;;EAEX;EACA,MAAM0gB,gBAAgB,GAAGA,CAAC1I,KAAK,EAAE6M,SAAS,EAAEC,aAAa,EAAEC,cAAc,KAAK;IAC5E,IAAI,CAACF,SAAS,IAAI,CAACC,aAAa,IAAI,CAACC,cAAc,EAAE;IACrD,MAAMC,IAAI,GAAGH,SAAS,CAACI,qBAAqB,CAAC,CAAC;IAC9C,MAAMC,MAAM,GAAI,CAAClN,KAAK,CAACmN,OAAO,GAAGH,IAAI,CAAC3Z,IAAI,IAAIwZ,SAAS,CAACO,WAAW,GAAI,CAAC,GAAG,CAAC;IAC5E,MAAMC,MAAM,GAAG,EAAE,CAACrN,KAAK,CAACsN,OAAO,GAAGN,IAAI,CAAChY,GAAG,IAAI6X,SAAS,CAACU,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;IAC7E,MAAMC,SAAS,GAAG,IAAIrnB,KAAK,CAACsnB,SAAS,CAAC,CAAC;IACvCD,SAAS,CAACE,MAAM,CAACC,MAAM,CAACC,SAAS,GAAG,CAAC;IACrCJ,SAAS,CAACE,MAAM,CAACG,IAAI,CAACD,SAAS,GAAG,CAAC;IACnC,MAAME,WAAW,GAAG,IAAI3nB,KAAK,CAAC4nB,OAAO,CAACb,MAAM,EAAEG,MAAM,CAAC;IACrDG,SAAS,CAACQ,aAAa,CAACF,WAAW,EAAEf,cAAc,CAAC;IACpD,MAAMkB,UAAU,GAAGT,SAAS,CAACU,gBAAgB,CAACpB,aAAa,CAAChI,QAAQ,EAAE,IAAI,CAAC;IAC3E,IAAImJ,UAAU,CAACvf,MAAM,GAAG,CAAC,EAAE;MACzB,KAAK,IAAI6J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0V,UAAU,CAACvf,MAAM,EAAE6J,CAAC,EAAE,EAAE;QAC1C,MAAMuP,GAAG,GAAGmG,UAAU,CAAC1V,CAAC,CAAC,CAAC1K,MAAM;QAChC,IAAIia,GAAG,CAACpY,MAAM,IAAIoY,GAAG,CAACpY,MAAM,CAACia,QAAQ,IAAI7B,GAAG,CAACpY,MAAM,CAACia,QAAQ,CAACuC,oBAAoB,EAAE;UACjF,MAAMnW,QAAQ,GAAG+R,GAAG,CAACpY,MAAM,CAACia,QAAQ,CAAC5T,QAAQ;UAC7CxM,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEwI,WAAW,CAAC;UACvD,MAAMkE,MAAM,GAAGlE,WAAW,CAACE,OAAO,CAACzH,IAAI,CAACkgB,CAAC,IAAIA,CAAC,CAAC9P,EAAE,KAAK9E,QAAQ,CAAC;UAC/D,IAAIG,MAAM,EAAE;YACV,MAAM3K,CAAC,GAAGyU,KAAK,CAACmN,OAAO;YACvB,MAAM1hB,CAAC,GAAGuU,KAAK,CAACsN,OAAO;YACvBxX,gBAAgB,CAAC;cACftB,OAAO,EAAE,IAAI;cACbuB,QAAQ;cACR5C,QAAQ,EAAE;gBAAE5H,CAAC;gBAAEE;cAAE,CAAC;cAClBiJ,OAAO,EAAEuB,0BAA0B,CAACC,MAAM;YAC5C,CAAC,CAAC;YACF,OAAO,CAAC;UACV;QACF;MACF;IACF;IACA;IACA;EACF,CAAC;EAKD,oBACElP,OAAA,CAAAE,SAAA;IAAA4d,QAAA,gBACE9d,OAAA;MAAMmnB,KAAK,EAAEjZ,UAAW;MAAA4P,QAAA,EAAC;IAAK;MAAA3O,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrCtP,OAAA,CAACN,MAAM;MACLynB,KAAK,EAAEpZ,uBAAwB;MAC/BqZ,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAEjW,wBAAyB;MACnCkW,QAAQ,EAAElW,wBAAyB,CAAC;MAAA;MACpCmW,OAAO,EAAEhc,aAAa,CAACvD,GAAG,CAACsJ,YAAY,KAAK;QAC1CD,KAAK,EAAEC,YAAY,CAACE,IAAI;QACxBgW,KAAK,EAAElW,YAAY,CAACE;MACtB,CAAC,CAAC,CAAE;MACJsJ,IAAI,EAAC,OAAO;MACZ2M,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACbnb,MAAM,EAAE,IAAI;QACZob,SAAS,EAAE;MACb,CAAE;MACFtW,KAAK,EAAEjE,oBAAoB,GAAGA,oBAAoB,CAACoE,IAAI,GAAGyF;IAAU;MAAA9H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eACFtP,OAAA;MAAK4nB,GAAG,EAAE5d,YAAa;MAACmd,KAAK,EAAE;QAAElZ,KAAK,EAAE,MAAM;QAAEwG,MAAM,EAAE;MAAO;IAAE;MAAAtF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGnEhC,mBAAmB,CAACE,OAAO,iBAC1BxN,OAAA;MACEmnB,KAAK,EAAE;QACLhb,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAAC5H,CAAC,IAAI;QAC3CyJ,GAAG,EAAE,GAAGV,mBAAmB,CAACnB,QAAQ,CAAC1H,CAAC,IAAI;QAC1C6H,SAAS,EAAE,wBAAwB;QACnCC,MAAM,EAAE,IAAI;QACZK,eAAe,EAAE,qBAAqB;QACtCwB,KAAK,EAAE,OAAO;QACdtB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,8BAA8B;QACzCN,OAAO,EAAE,GAAG;QACZkb,QAAQ,EAAE,OAAO;QAAE;QACnB7a,QAAQ,EAAE,MAAM,CAAC;MACnB,CAAE;MAAA8Q,QAAA,GAEDxQ,mBAAmB,CAACI,OAAO,eAC5B1N,OAAA;QACEmnB,KAAK,EAAE;UACLhb,QAAQ,EAAE,UAAU;UACpB6B,GAAG,EAAE,KAAK;UACV8Z,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,MAAM;UAClBlb,MAAM,EAAE,MAAM;UACduB,KAAK,EAAE,OAAO;UACdpB,QAAQ,EAAE,MAAM;UAChBD,MAAM,EAAE,SAAS;UACjBJ,OAAO,EAAE;QACX,CAAE;QACFqb,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAAC1a,sBAAsB,CAAE;QAAAuQ,QAAA,EAC3D;MAED;QAAA3O,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAEDtP,OAAA;MAAKmnB,KAAK,EAAEjb,oBAAqB;MAAA4R,QAAA,gBAC/B9d,OAAA;QACEmnB,KAAK,EAAE;UACL,GAAGza,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFgc,OAAO,EAAEzY,kBAAmB;QAAAuO,QAAA,EAC7B;MAED;QAAA3O,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtP,OAAA;QACEmnB,KAAK,EAAE;UACL,GAAGza,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFgc,OAAO,EAAEtY,kBAAmB;QAAAoO,QAAA,EAC7B;MAED;QAAA3O,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EACLT,aAAa,CAACrB,OAAO,iBACpBxN,OAAA;MACEmnB,KAAK,EAAE;QACLhb,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGwC,aAAa,CAAC1C,QAAQ,CAAC5H,CAAC,IAAI;QACrCyJ,GAAG,EAAE,GAAGa,aAAa,CAAC1C,QAAQ,CAAC1H,CAAC,IAAI;QACpC6H,SAAS,EAAE,wBAAwB;QACnCC,MAAM,EAAE,IAAI;QACZK,eAAe,EAAE,qBAAqB;QACtCwB,KAAK,EAAE,OAAO;QACdtB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,6BAA6B;QACxCN,OAAO,EAAE,CAAC;QACVub,QAAQ,EAAE,GAAG;QACbL,QAAQ,EAAE,GAAG;QACb7a,QAAQ,EAAE;MACZ,CAAE;MAAA8Q,QAAA,GAEDjP,aAAa,CAACnB,OAAO,eACtB1N,OAAA;QACEmnB,KAAK,EAAE;UACLhb,QAAQ,EAAE,UAAU;UACpB6B,GAAG,EAAE,KAAK;UACV8Z,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,MAAM;UAClBlb,MAAM,EAAE,MAAM;UACduB,KAAK,EAAE,OAAO;UACdpB,QAAQ,EAAE,MAAM;UAChBD,MAAM,EAAE,SAAS;UACjBJ,OAAO,EAAE,UAAU;UACnBJ,MAAM,EAAE;QACV,CAAE;QACFyb,OAAO,EAAEhZ,wBAAyB;QAAA8O,QAAA,EACnC;MAAC;QAAA3O,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACN;EAAA,eACD,CAAC;AAEP,CAAC;;AAED;AAAAvF,EAAA,CAntEMJ,WAAW;AAAAwe,EAAA,GAAXxe,WAAW;AAotEjB,SAAS0M,gBAAgBA,CAAC+R,IAAI,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAM3B,MAAM,GAAG;IACb4B,QAAQ,EAAED,UAAU,CAACC,QAAQ,IAAI,OAAO;IACxCtb,QAAQ,EAAEqb,UAAU,CAACrb,QAAQ,IAAI,EAAE;IAAE;IACrCqB,UAAU,EAAEga,UAAU,CAACha,UAAU,IAAI,MAAM;IAC3Cka,eAAe,EAAEF,UAAU,CAACE,eAAe,IAAI,CAAC;IAChDC,WAAW,EAAEH,UAAU,CAACG,WAAW,IAAI;MAAEjS,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAExO,CAAC,EAAE;IAAI,CAAC;IACnE2E,eAAe,EAAEyb,UAAU,CAACzb,eAAe,IAAI;MAAE2J,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAExO,CAAC,EAAE;IAAI,CAAC;IACjFyO,SAAS,EAAE2R,UAAU,CAAC3R,SAAS,IAAI;MAAEH,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAExO,CAAC,EAAE;IAAI,CAAC;IAC/D0E,OAAO,EAAE0b,UAAU,CAAC1b,OAAO,IAAI;EACjC,CAAC;;EAED;EACA,MAAM8b,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;EAEvC;EACA;;EAEA;EACA,MAAMC,SAAS,GAAGF,OAAO,CAACG,WAAW,CAACX,IAAI,CAAC,CAACna,KAAK;;EAEjD;EACA,MAAMA,KAAK,GAAG6a,SAAS,GAAG,CAAC,GAAGpC,MAAM,CAAC/Z,OAAO,GAAG,CAAC,GAAG+Z,MAAM,CAAC6B,eAAe;EACzE,MAAM9T,MAAM,GAAGiS,MAAM,CAAC1Z,QAAQ,GAAG,CAAC,GAAG0Z,MAAM,CAAC/Z,OAAO,GAAG,CAAC,GAAG+Z,MAAM,CAAC6B,eAAe;EAEhFE,MAAM,CAACxa,KAAK,GAAGA,KAAK;EACpBwa,MAAM,CAAChU,MAAM,GAAGA,MAAM;;EAEtB;EACAmU,OAAO,CAACI,IAAI,GAAG,GAAGtC,MAAM,CAACrY,UAAU,IAAIqY,MAAM,CAAC1Z,QAAQ,MAAM0Z,MAAM,CAAC4B,QAAQ,EAAE;EAC7EM,OAAO,CAACK,YAAY,GAAG,QAAQ;;EAE/B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACAL,OAAO,CAACM,SAAS,GAAG,QAAQxC,MAAM,CAAChQ,SAAS,CAACH,CAAC,KAAKmQ,MAAM,CAAChQ,SAAS,CAACF,CAAC,KAAKkQ,MAAM,CAAChQ,SAAS,CAACD,CAAC,KAAKiQ,MAAM,CAAChQ,SAAS,CAACzO,CAAC,GAAG;EACtH2gB,OAAO,CAACO,SAAS,GAAG,QAAQ;;EAE5B;EACAP,OAAO,CAACQ,QAAQ,CAAChB,IAAI,EAAEna,KAAK,GAAG,CAAC,EAAEwG,MAAM,GAAG,CAAC,CAAC;;EAE7C;EACA,MAAM4U,OAAO,GAAG,IAAIlqB,KAAK,CAACmqB,aAAa,CAACb,MAAM,CAAC;EAC/CY,OAAO,CAACE,SAAS,GAAGpqB,KAAK,CAACqqB,YAAY;EACtCH,OAAO,CAAClT,WAAW,GAAG,IAAI;;EAE1B;EACA,MAAMsT,cAAc,GAAG,IAAItqB,KAAK,CAACuqB,cAAc,CAAC;IAC9C1hB,GAAG,EAAEqhB,OAAO;IACZnT,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAMyT,MAAM,GAAG,IAAIxqB,KAAK,CAACyqB,MAAM,CAACH,cAAc,CAAC;EAC/C;EACAE,MAAM,CAAChV,KAAK,CAAC/P,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EAC7B+kB,MAAM,CAACzgB,QAAQ,CAAC2gB,SAAS,GAAG,KAAK,CAAC,CAAC;;EAEnC;EACAF,MAAM,CAAChH,QAAQ,GAAG;IAChByF,IAAI,EAAEA,IAAI;IACV1B,MAAM,EAAEA;EACV,CAAC;EAED,OAAOiD,MAAM;AACf;;AAIA;AACAhoB,MAAM,CAACmoB,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMxO,MAAM,GAAGoN,QAAQ,CAACqB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAI3O,MAAM,EAAE;MACV;MACA,MAAM4O,MAAM,GAAG5O,MAAM,CAACnP,QAAQ,CAAC9H,KAAK,CAAC,CAAC;;MAEtC;MACAiX,MAAM,CAACnP,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9B0W,MAAM,CAACzL,EAAE,CAACjL,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtB0W,MAAM,CAAC7K,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACA6K,MAAM,CAACvK,YAAY,CAAC,CAAC;MACrBuK,MAAM,CAACtK,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMrQ,QAAQ,GAAG+nB,QAAQ,CAACqB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIxpB,QAAQ,EAAE;QACZA,QAAQ,CAAC4P,MAAM,CAAC3L,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BjE,QAAQ,CAAC+P,MAAM,CAAC,CAAC;MACnB;MAEAnO,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxB4nB,GAAG,EAAEF,MAAM,CAAClY,OAAO,CAAC,CAAC;QACrBqY,GAAG,EAAE/O,MAAM,CAACnP,QAAQ,CAAC6F,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAO1J,CAAC,EAAE;IACV/F,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEyE,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAGD;AACA,MAAM8S,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACF7Y,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAMic,MAAM,GAAG,IAAIrf,UAAU,CAAC,CAAC;;IAE/B;IACA,IAAI;MACF,MAAM,CAAEkrB,gBAAgB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,UAAU,CAAC,GAAG,MAAMxN,OAAO,CAACyN,GAAG,CAAC,CAClFjM,MAAM,CAACkM,SAAS,CAAC,GAAGxoB,QAAQ,4BAA4B,CAAC,EAC3Dsc,MAAM,CAACkM,SAAS,CAAC,GAAGxoB,QAAQ,uBAAuB,CAAC,EACpDsc,MAAM,CAACkM,SAAS,CAAC,GAAGxoB,QAAQ,uBAAuB,CAAC,EAClDsc,MAAM,CAACkM,SAAS,CAAC,GAAGxoB,QAAQ,sBAAsB,CAAC,CAEtD,CAAC;;MAIF;MACAI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB5B,qBAAqB,GAAG2pB,WAAW,CAACvpB,KAAK;MACzCJ,qBAAqB,CAACgG,QAAQ,CAAEkP,KAAK,IAAK;QACxC,IAAIA,KAAK,CAAC/M,MAAM,EAAE;UACd,MAAMgN,WAAW,GAAG,IAAI5W,KAAK,CAACue,oBAAoB,CAAC;YACnDtP,KAAK,EAAE,QAAQ;YAAG;YAClBuP,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,GAAG;YACdC,eAAe,EAAE;UACnB,CAAC,CAAC;;UAEA;UACA,IAAI/H,KAAK,CAAC5M,QAAQ,CAAClB,GAAG,EAAE;YACtB+N,WAAW,CAAC/N,GAAG,GAAG8N,KAAK,CAAC5M,QAAQ,CAAClB,GAAG;UACtC;UACA8N,KAAK,CAAC8U,OAAO,GAAG7U,WAAW;QAC/B;MACF,CAAC,CAAC;MAEFxT,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B;MACA3B,qBAAqB,GAAG2pB,WAAW,CAACxpB,KAAK;MACzC;MACAH,qBAAqB,CAAC8T,KAAK,CAAC/P,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxC;MACA/D,qBAAqB,CAAC+F,QAAQ,CAAEkP,KAAK,IAAK;QACxC,IAAIA,KAAK,CAAC/M,MAAM,IAAI+M,KAAK,CAAC5M,QAAQ,EAAE;UAClC;UACA4M,KAAK,CAAC5M,QAAQ,CAACyU,SAAS,GAAG,GAAG;UAC9B7H,KAAK,CAAC5M,QAAQ,CAAC0U,SAAS,GAAG,GAAG;UAC9B9H,KAAK,CAAC5M,QAAQ,CAAC2U,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;MAEFtb,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA1B,oBAAoB,GAAG2pB,UAAU,CAACzpB,KAAK;MACvC;MACA;MACA;MACAF,oBAAoB,CAAC8F,QAAQ,CAAEkP,KAAK,IAAK;QACvC,IAAIA,KAAK,CAAC/M,MAAM,IAAI+M,KAAK,CAAC5M,QAAQ,EAAE;UAClC;UACA4M,KAAK,CAAC5M,QAAQ,CAACyU,SAAS,GAAG,GAAG;UAC9B7H,KAAK,CAAC5M,QAAQ,CAAC0U,SAAS,GAAG,GAAG;UAC9B9H,KAAK,CAAC5M,QAAQ,CAAC2U,eAAe,GAAG,GAAG;QAEtC;QACA,IAAI/H,KAAK,CAAC/M,MAAM,EAAC;UACf+M,KAAK,CAAC+U,UAAU,GAAG,IAAI;QACzB;MACF,CAAC,CAAC;;MAIF;MACAtoB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEioB,UAAU,CAAChjB,UAAU,CAACC,MAAM,EAAE,GAAG,CAAC;MAC5D,IAAI+iB,UAAU,CAAChjB,UAAU,IAAIgjB,UAAU,CAAChjB,UAAU,CAACC,MAAM,GAAG,CAAC,EAAE;QAC7DnF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEioB,UAAU,CAAChjB,UAAU,CAACC,MAAM,EAAE,GAAG,CAAC;QACzDzG,eAAe,GAAGwpB,UAAU;MAC9B,CAAC,MAAM;QACLloB,OAAO,CAAC0gB,IAAI,CAAC,cAAc,CAAC;MAC9B;MAEA1gB,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;;MAEzB;MACAzB,0BAA0B,GAAGupB,gBAAgB,CAACtpB,KAAK;MACnDuB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEzB,0BAA0B,CAAC;MACjD;MACAA,0BAA0B,CAAC4T,KAAK,CAAC/P,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C;MACA7D,0BAA0B,CAAC6F,QAAQ,CAAEkP,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAAC/M,MAAM,IAAI+M,KAAK,CAAC5M,QAAQ,EAAE;UAClC;UACA4M,KAAK,CAAC5M,QAAQ,CAACyU,SAAS,GAAG,GAAG;UAC9B7H,KAAK,CAAC5M,QAAQ,CAAC0U,SAAS,GAAG,GAAG;UAC9B9H,KAAK,CAAC5M,QAAQ,CAAC2U,eAAe,GAAG,GAAG;QACxC;MACF,CAAC,CAAC;MAEAtb,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACA,IAAI;QACF,IAAI,CAACjD,qBAAqB,EAAE;UAC1B,MAAM2pB,WAAW,GAAG,MAAM9L,MAAM,CAACkM,SAAS,CAAC,GAAGxoB,QAAQ,uBAAuB,CAAC;UAC9EvB,qBAAqB,GAAG2pB,WAAW,CAACvpB,KAAK;QAC3C;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACF,CAAC,CAAC,OAAO8pB,GAAG,EAAE;QACZvoB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEinB,GAAG,CAAC;MAClC;IACF;EACF,CAAC,CAAC,OAAOjnB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;;AAED;AACA,MAAMyW,mBAAmB,GAAIvG,IAAI,IAAK;EACpC,MAAMgX,KAAK,GAAG;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,KAAK,CAAChX,IAAI,CAAC,IAAI,MAAM;AAC9B,CAAC;;AAED;AACA;AACA,MAAMiX,iBAAiB,GAAGA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;EAE5C,MAAMC,CAAC,GAAG5lB,IAAI,CAAC6lB,IAAI,CAAC,CAACL,EAAE,GAACE,EAAE,KAAGF,EAAE,GAACE,EAAE,CAAC,GAAC,CAACD,EAAE,GAACE,EAAE,KAAGF,EAAE,GAACE,EAAE,CAAC,CAAC;EACpD,OAAQC,CAAC;AACX,CAAC;;AAED;AACA,MAAME,kBAAkB,GAAInS,SAAS,IAAK;EACxC,QAAOA,SAAS;IACd,KAAK,KAAK;MAAE;MACV,OAAO;QAAEoS,aAAa,EAAE,MAAM;QAAEC,iBAAiB,EAAE;MAAG,CAAC;IAAE;IAC3D,KAAK,KAAK;MAAE;MACV,OAAO;QAAED,aAAa,EAAE,KAAK;QAAEC,iBAAiB,EAAE;MAAG,CAAC;IAAE;IAC1D,KAAK,KAAK;MAAE;MACV,OAAO;QAAED,aAAa,EAAE,KAAK;QAAEC,iBAAiB,EAAE;MAAG,CAAC;IAAE;IAC1D,KAAK,KAAK,CAAC,CAAC;IACZ,KAAK,KAAK,CAAC,CAAC;IACZ,KAAK,MAAM;MAAE;MACX,OAAO;QAAED,aAAa,EAAE,MAAM;QAAEC,iBAAiB,EAAE;MAAG,CAAC;IAAE;IAC3D,KAAK,KAAK;MAAE;MACV,OAAO;QAAED,aAAa,EAAE,KAAK;QAAEC,iBAAiB,EAAE;MAAG,CAAC;IAAE;IAC1D;MACE,OAAO;QAAED,aAAa,EAAE,IAAI;QAAEC,iBAAiB,EAAE;MAAE,CAAC;IAAE;EAC1D;AACF,CAAC;;AAED;AACA,MAAMC,mBAAmB,GAAGA,CAACtS,SAAS,EAAEuS,QAAQ,EAAEC,WAAW,EAAEjc,UAAU,KAAK;EAC5E,MAAM;IAAE6b,aAAa;IAAEC;EAAkB,CAAC,GAAGF,kBAAkB,CAACnS,SAAS,CAAC;;EAE1E;EACA,KAAK,IAAI7H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/H,cAAc,CAAC9B,MAAM,EAAE6J,CAAC,EAAE,EAAE;IAC9C,MAAMsa,WAAW,GAAGriB,cAAc,CAAC+H,CAAC,CAAC;;IAErC;IACA,IAAIsa,WAAW,CAACzS,SAAS,KAAKA,SAAS,EAAE;MACvC;IACF;;IAEA;IACA,MAAM0S,QAAQ,GAAGF,WAAW,GAAGC,WAAW,CAACE,cAAc;;IAEzD;IACA,IAAID,QAAQ,GAAGN,aAAa,EAAE;MAC5B;IACF;;IAEA;IACA,MAAMxmB,QAAQ,GAAGgmB,iBAAiB,CAChCrb,UAAU,CAACpL,CAAC,EAAEoL,UAAU,CAAClL,CAAC,EAC1BonB,WAAW,CAAC1f,QAAQ,CAAC5H,CAAC,EAAEsnB,WAAW,CAAC1f,QAAQ,CAAC1H,CAC/C,CAAC;;IAED;IACA,IAAIO,QAAQ,IAAIymB,iBAAiB,EAAE;MACjC;MACAI,WAAW,CAACF,QAAQ,GAAGA,QAAQ;MAC/BE,WAAW,CAACE,cAAc,GAAGH,WAAW;MACxCC,WAAW,CAAC1f,QAAQ,GAAG;QAAE,GAAGwD;MAAW,CAAC;MACxCkc,WAAW,CAACG,WAAW,GAAG,CAACH,WAAW,CAACG,WAAW,IAAI,CAAC,IAAI,CAAC;MAE5DzpB,OAAO,CAACC,GAAG,CAAC,kBAAkB4W,SAAS,SAASyS,WAAW,CAAC3S,OAAO,UAAU,CAAC4S,QAAQ,GAAC,IAAI,EAAE3mB,OAAO,CAAC,CAAC,CAAC,SAASH,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,WAAW0mB,WAAW,CAACG,WAAW,EAAE,CAAC;MAExK,OAAO;QACLC,WAAW,EAAE,IAAI;QACjB/S,OAAO,EAAE2S,WAAW,CAAC3S,OAAO;QAC5BgT,YAAY,EAAEL;MAChB,CAAC;IACH;EACF;;EAEA;EACA,MAAMM,UAAU,GAAG,UAAU1iB,cAAc,CAAC+N,QAAQ,CAAC,CAAC,CAAC4U,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACzE3iB,cAAc,EAAE;EAEhB,MAAM4iB,QAAQ,GAAG;IACfnT,OAAO,EAAEiT,UAAU;IACnB/S,SAAS,EAAEA,SAAS;IACpBuS,QAAQ,EAAEA,QAAQ;IAClBW,iBAAiB,EAAEV,WAAW;IAC9BG,cAAc,EAAEH,WAAW;IAC3Bzf,QAAQ,EAAE;MAAE,GAAGwD;IAAW,CAAC;IAC3Bqc,WAAW,EAAE;EACf,CAAC;;EAED;EACAxiB,cAAc,CAAC0O,IAAI,CAACmU,QAAQ,CAAC;EAI7B,OAAO;IACLJ,WAAW,EAAE,KAAK;IAClB/S,OAAO,EAAEiT,UAAU;IACnBE,QAAQ,EAAEA;EACZ,CAAC;AACH,CAAC;;AAED;AACA,MAAM/L,oBAAoB,GAAGA,CAAA,KAAM;EACjC,MAAMsL,WAAW,GAAGhhB,IAAI,CAACC,GAAG,CAAC,CAAC;EAC9B,MAAM0hB,iBAAiB,GAAG,IAAI,CAAC,CAAC;;EAEhC,MAAMC,YAAY,GAAGhjB,cAAc,CAAC9B,MAAM;EAC1C,MAAM+kB,aAAa,GAAG,EAAE;EAExBjjB,cAAc,GAAGA,cAAc,CAACrB,MAAM,CAAC6Q,KAAK,IAAI;IAC9C,MAAMhC,mBAAmB,GAAG4U,WAAW,GAAG5S,KAAK,CAAC+S,cAAc;IAC9D,IAAI/U,mBAAmB,GAAGuV,iBAAiB,EAAE;MAC3CE,aAAa,CAACvU,IAAI,CAAC;QACjBrE,EAAE,EAAEmF,KAAK,CAACE,OAAO;QACjBnF,IAAI,EAAEiF,KAAK,CAACI,SAAS;QACrBsT,YAAY,EAAE,CAAC1V,mBAAmB,GAAG,IAAI,EAAE7R,OAAO,CAAC,CAAC;MACtD,CAAC,CAAC;;MAEF;MACA,MAAMwb,MAAM,GAAGjX,YAAY,CAAC3E,GAAG,CAACiU,KAAK,CAACE,OAAO,CAAC;MAC9C,IAAIyH,MAAM,IAAI3f,KAAK,EAAE;QACnBA,KAAK,CAAC2H,MAAM,CAACgY,MAAM,CAAC;QACpBjX,YAAY,CAACrC,MAAM,CAAC2R,KAAK,CAACE,OAAO,CAAC;MACpC;MAEA,OAAO,KAAK,CAAC,CAAC;IAChB;IACA,OAAO,IAAI,CAAC,CAAC;EACf,CAAC,CAAC;EAEF,MAAMyT,YAAY,GAAGH,YAAY,GAAGhjB,cAAc,CAAC9B,MAAM;EACzD,IAAIilB,YAAY,GAAG,CAAC,EAAE;IACpBpqB,OAAO,CAACC,GAAG,CAAC,eAAemqB,YAAY,cAAc,CAAC;IACtDF,aAAa,CAACtlB,OAAO,CAAC6R,KAAK,IAAI;MAC7BzW,OAAO,CAACC,GAAG,CAAC,WAAWwW,KAAK,CAACnF,EAAE,SAASmF,KAAK,CAACjF,IAAI,YAAYiF,KAAK,CAAC0T,YAAY,GAAG,CAAC;IACtF,CAAC,CAAC;IACFnqB,OAAO,CAACC,GAAG,CAAC,mBAAmBgH,cAAc,CAAC9B,MAAM,EAAE,CAAC;EACzD;AACF,CAAC;;AAED;AACA,MAAMklB,uBAAuB,GAAIxT,SAAS,IAAK;EAC7C,QAAOA,SAAS;IACd,KAAK,KAAK;MAAE;MACV,OAAO,QAAQ;IAAE;IACnB,KAAK,KAAK;MAAE;MACV,OAAO,QAAQ;IAAE;IACnB,KAAK,KAAK;MAAE;MACV,OAAO,QAAQ;IAAE;IACnB,KAAK,KAAK;MAAE;MACV,OAAO,QAAQ;IAAE;IACnB,KAAK,KAAK;MAAE;MACV,OAAO,QAAQ;IAAE;IACnB,KAAK,MAAM;MAAE;MACX,OAAO,QAAQ;IAAE;IACnB,KAAK,KAAK;MAAE;MACV,OAAO,QAAQ;IAAE;IACnB,KAAK,GAAG;MAAE;MACR,OAAO,QAAQ;IAAE;IACnB,KAAK,IAAI;MAAE;MACT,OAAO,QAAQ;IAAE;IACnB,KAAK,IAAI;MAAE;MACT,OAAO,QAAQ;IAAE;IACnB,KAAK,KAAK;MAAE;MACV,OAAO,QAAQ;IAAE;IACnB;MACE,OAAO,QAAQ;IAAE;EACrB;AACF,CAAC;;AAED;AACA,MAAMyT,gBAAgB,GAAIzT,SAAS,IAAK;EACtC,QAAOA,SAAS;IACd,KAAK,KAAK;MAAE,OAAO,KAAK;IACxB,KAAK,KAAK;MAAE,OAAO,KAAK;IACxB,KAAK,KAAK;MAAE,OAAO,IAAI;IACvB,KAAK,KAAK;MAAE,OAAO,IAAI;IACvB,KAAK,KAAK;MAAE,OAAO,IAAI;IACvB,KAAK,MAAM;MAAE,OAAO,IAAI;IACxB,KAAK,KAAK;MAAE,OAAO,IAAI;IACvB,KAAK,GAAG;MAAE,OAAO,IAAI;IACrB,KAAK,IAAI;MAAE,OAAO,IAAI;IACtB,KAAK,IAAI;MAAE,OAAO,IAAI;IACtB,KAAK,KAAK;MAAE,OAAO,IAAI;IACvB;MAAS,OAAO,KAAKA,SAAS,EAAE;EAClC;AACF,CAAC;;AAED;AACA,MAAM0T,iBAAiB,GAAGA,CAAC1T,SAAS,EAAEjN,QAAQ,EAAE+M,OAAO,KAAK;EAC1D,IAAI,CAAClY,KAAK,EAAE;IACVuB,OAAO,CAAC0gB,IAAI,CAAC,oBAAoB,CAAC;IAClC,OAAO,IAAI;EACb;EAEA,IAAI;IACF;IACA,MAAM8J,WAAW,GAAG,IAAI5tB,KAAK,CAACse,KAAK,CAAC,CAAC;;IAErC;IACA,MAAMuP,kBAAkB,GAAG,IAAI7tB,KAAK,CAACylB,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1D,MAAMhY,eAAe,GAAGggB,uBAAuB,CAACxT,SAAS,CAAC,CAAC,CAAC;IAC5D,MAAM6T,kBAAkB,GAAG,IAAI9tB,KAAK,CAAC2iB,iBAAiB,CAAC;MACrD1T,KAAK,EAAExB,eAAe;MAAE;MACxBsJ,WAAW,EAAE,IAAI;MACjBU,OAAO,EAAE,GAAG;MAAE;MACdsW,IAAI,EAAE/tB,KAAK,CAACguB;IACd,CAAC,CAAC;IACF,MAAMC,cAAc,GAAG,IAAIjuB,KAAK,CAAC4iB,IAAI,CAACiL,kBAAkB,EAAEC,kBAAkB,CAAC;IAC7EG,cAAc,CAACjhB,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3CwoB,cAAc,CAACzW,WAAW,GAAG,GAAG,CAAC,CAAC;IAClCoW,WAAW,CAACpmB,GAAG,CAACymB,cAAc,CAAC;;IAE/B;IACA,MAAMhJ,aAAa,GAAG,IAAIjlB,KAAK,CAACklB,aAAa,CAAC,CAAC;IAC/C,MAAMC,QAAQ,GAAG,GAAGniB,QAAQ,WAAWiX,SAAS,MAAM,CAAC,CAAC;;IAExD,MAAMmL,YAAY,GAAG,IAAIplB,KAAK,CAAC2iB,iBAAiB,CAAC;MAC/C9Z,GAAG,EAAEoc,aAAa,CAAC/G,IAAI,CAACiH,QAAQ;MAC9B;MACC+E,OAAO,IAAK;QACX9mB,OAAO,CAACC,GAAG,CAAC,eAAe4W,SAAS,MAAM,CAAC;MAC7C,CAAC;MACD;MACAnC,SAAS;MACT;MACCpT,KAAK,IAAK;QACTtB,OAAO,CAAC0gB,IAAI,CAAC,eAAe7J,SAAS,aAAa,CAAC;QACnD;MACF,CACF,CAAC;MACDlD,WAAW,EAAE,IAAI;MACjBU,OAAO,EAAE,CAAC;MAAE;MACZsW,IAAI,EAAE/tB,KAAK,CAACguB;IACd,CAAC,CAAC;;IAEF;IACA,MAAMrI,YAAY,GAAG,IAAI3lB,KAAK,CAACylB,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACpD,MAAMG,QAAQ,GAAG,IAAI5lB,KAAK,CAAC4iB,IAAI,CAAC+C,YAAY,EAAEP,YAAY,CAAC;IAC3DQ,QAAQ,CAAC5Y,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;IACrCmgB,QAAQ,CAACpO,WAAW,GAAG,GAAG,CAAC,CAAC;IAC5BoW,WAAW,CAACpmB,GAAG,CAACoe,QAAQ,CAAC;;IAEzB;IACA,MAAMsI,aAAa,GAAGR,gBAAgB,CAACzT,SAAS,CAAC;IACjD,MAAMkU,SAAS,GAAGjX,gBAAgB,CAACgX,aAAa,EAAE;MAChDzgB,eAAe,EAAE;QAAE2J,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAExO,CAAC,EAAE;MAAI,CAAC;MAAE;MAC/CyO,SAAS,EAAE;QAAEH,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE,GAAG;QAAExO,CAAC,EAAE;MAAI,CAAC;MAAE;MAC/C+E,QAAQ,EAAE,EAAE;MAAE;MACdL,OAAO,EAAE,CAAC;MAAE;MACZ4b,eAAe,EAAE,CAAC;MAAE;MACpBla,UAAU,EAAE,MAAM,CAAC;IACrB,CAAC,CAAC;IACFif,SAAS,CAACnhB,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;IACvC0oB,SAAS,CAAC3W,WAAW,GAAG,IAAI,CAAC,CAAC;IAC9B2W,SAAS,CAAC3Y,KAAK,CAAC/P,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9BmoB,WAAW,CAACpmB,GAAG,CAAC2mB,SAAS,CAAC;;IAE1B;IACAP,WAAW,CAAC5gB,QAAQ,CAACvH,GAAG,CAACuH,QAAQ,CAAC5H,CAAC,EAAE,EAAE,EAAE,CAAC4H,QAAQ,CAAC1H,CAAC,CAAC;;IAErD;IACA;IACJ;IACI;IACA;IACA;IACA;IACA;IACA;IACAsoB,WAAW,CAACpW,WAAW,GAAG,GAAG;;IAE7B;IACAoW,WAAW,CAACpK,QAAQ,GAAG;MACrB5O,IAAI,EAAE,aAAa;MACnBmF,OAAO,EAAEA,OAAO;MAChBE,SAAS,EAAEA;IACb,CAAC;;IAED;IACApY,KAAK,CAAC2F,GAAG,CAAComB,WAAW,CAAC;;IAEtB;IACArjB,YAAY,CAAC9E,GAAG,CAACsU,OAAO,EAAE6T,WAAW,CAAC;IAEtCxqB,OAAO,CAACC,GAAG,CAAC,aAAa4W,SAAS,KAAKiU,aAAa,UAAUnU,OAAO,UAAU/M,QAAQ,CAAC5H,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC,KAAKgH,QAAQ,CAAC1H,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,WAAWyH,eAAe,CAAC4K,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;IAExK,OAAOuV,WAAW;EACpB,CAAC,CAAC,OAAOlpB,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA,MAAM0pB,yBAAyB,GAAGA,CAACrU,OAAO,EAAEvD,WAAW,KAAK;EAC1D,MAAMoX,WAAW,GAAGrjB,YAAY,CAAC3E,GAAG,CAACmU,OAAO,CAAC;EAC7C,IAAI6T,WAAW,IAAI/rB,KAAK,EAAE;IACxB+rB,WAAW,CAAC5gB,QAAQ,CAACvH,GAAG,CAAC+Q,WAAW,CAACpR,CAAC,EAAE,EAAE,EAAE,CAACoR,WAAW,CAAClR,CAAC,CAAC;IAC3D;IACA;IACAlC,OAAO,CAACC,GAAG,CAAC,oBAAoB0W,OAAO,WAAWvD,WAAW,CAACpR,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC,KAAKwQ,WAAW,CAAClR,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;EAC7G;AACF,CAAC;AAED,MAAMyU,iBAAiB,GAAGA,CAACzN,QAAQ,EAAEic,IAAI,EAAEha,KAAK,EAAEgL,SAAS,GAAG,KAAK,EAAEoU,SAAS,GAAG,CAAC,CAAC,KAAK;EACtF;EACA,IAAI,CAACxsB,KAAK,EAAE;IACVuB,OAAO,CAAC0gB,IAAI,CAAC,oBAAoB,CAAC;IAClC;EACF;EAEA,IAAI;IACF;IACA,MAAM2I,WAAW,GAAGhhB,IAAI,CAACC,GAAG,CAAC,CAAC;IAC9B,MAAM8gB,QAAQ,GAAG,GAAG6B,SAAS,CAAC3U,KAAK,IAAI,SAAS,IAAIO,SAAS,IAAIjN,QAAQ,CAAC5H,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC,IAAIgH,QAAQ,CAAC1H,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,EAAE;;IAEjH;IACA;IACA;;IAEA;IACA,MAAMsoB,eAAe,GAAG/B,mBAAmB,CACzCtS,SAAS,EACTuS,QAAQ,EACRC,WAAW,EACXzf,QACF,CAAC;IAED,MAAM8f,WAAW,GAAGwB,eAAe,CAACxB,WAAW;IAE/C1pB,OAAO,CAACC,GAAG,CAAC,uBAAuB4W,SAAS,eAAeuS,QAAQ,UAAUxf,QAAQ,CAAC5H,CAAC,KAAK4H,QAAQ,CAAC1H,CAAC,UAAUwnB,WAAW,GAAG,IAAI,GAAG,KAAK,EAAE,CAAC;IAE7I,IAAIA,WAAW,EAAE;MACf;MACAsB,yBAAyB,CAACE,eAAe,CAACvU,OAAO,EAAE/M,QAAQ,CAAC;IAC9D,CAAC,MAAM;MACL;MACA2gB,iBAAiB,CAAC1T,SAAS,EAAEjN,QAAQ,EAAEshB,eAAe,CAACvU,OAAO,CAAC;IACjE;EAEF,CAAC,CAAC,OAAOrV,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;AACF,CAAC;;AAED;AACA,MAAM0d,mBAAmB,GAAGA,CAACyB,iBAAiB,EAAEzX,aAAa,KAAK;EAChE,IAAI,CAACvK,KAAK,EAAE;IACVuB,OAAO,CAACsB,KAAK,CAAC,gBAAgB,CAAC;IAC/B;EACF;EAEA,IAAI,CAACmf,iBAAiB,EAAE;IACtBzgB,OAAO,CAACsB,KAAK,CAAC,mBAAmB,CAAC;IAClC;EACF;;EAEA;EACA,IAAI,CAAC9C,0BAA0B,EAAE;IAC/BwB,OAAO,CAACsB,KAAK,CAAC,oBAAoB,CAAC;IACnC;IACA,MAAM4a,MAAM,GAAG,IAAIrf,UAAU,CAAC,CAAC;IAC/Bqf,MAAM,CAACkM,SAAS,CAAC,GAAGxoB,QAAQ,4BAA4B,CAAC,CACtDurB,IAAI,CAACpD,gBAAgB,IAAI;MACxBvpB,0BAA0B,GAAGupB,gBAAgB,CAACtpB,KAAK;MACnDD,0BAA0B,CAAC4T,KAAK,CAAC/P,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C7D,0BAA0B,CAAC6F,QAAQ,CAAEkP,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAAC/M,MAAM,IAAI+M,KAAK,CAAC5M,QAAQ,EAAE;UAClC4M,KAAK,CAAC5M,QAAQ,CAACyU,SAAS,GAAG,GAAG;UAC9B7H,KAAK,CAAC5M,QAAQ,CAAC0U,SAAS,GAAG,GAAG;UAC9B9H,KAAK,CAAC5M,QAAQ,CAAC2U,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;MACFtb,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;MACA+e,mBAAmB,CAACyB,iBAAiB,EAAEzX,aAAa,CAAC;IACvD,CAAC,CAAC,CACDoiB,KAAK,CAAC9pB,KAAK,IAAI;MACdtB,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC;MACA+pB,2BAA2B,CAAC5K,iBAAiB,EAAEzX,aAAa,CAAC;IAC/D,CAAC,CAAC;IACJ;EACF;;EAEA;EACA3I,gBAAgB,CAACuE,OAAO,CAAEkb,QAAQ,IAAK;IACrC,IAAIrhB,KAAK,IAAIqhB,QAAQ,CAAC3b,KAAK,EAAE;MAC3B1F,KAAK,CAAC2H,MAAM,CAAC0Z,QAAQ,CAAC3b,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACF9D,gBAAgB,CAAC4F,KAAK,CAAC,CAAC;;EAExB;EACA+C,aAAa,CAACpE,OAAO,CAACmK,YAAY,IAAI;IACpC,IAAIA,YAAY,CAACY,eAAe,KAAK,KAAK,EAAE;MAC1C3P,OAAO,CAACC,GAAG,CAAC,UAAU8O,YAAY,CAACE,IAAI,kBAAkB,CAAC;MAC1D;IACF;IAEA,IAAIF,YAAY,CAACzF,QAAQ,IAAIyF,YAAY,CAAC1F,SAAS,IAAI0F,YAAY,CAAC7D,OAAO,EAAE;MAC3E,MAAM6G,QAAQ,GAAG0O,iBAAiB,CAACtR,YAAY,CAC7CC,UAAU,CAACL,YAAY,CAAC1F,SAAS,CAAC,EAClC+F,UAAU,CAACL,YAAY,CAACzF,QAAQ,CAClC,CAAC;MAEDtJ,OAAO,CAACC,GAAG,CAAC,SAAS8O,YAAY,CAACE,IAAI,KAAKF,YAAY,CAAC7D,OAAO,iBAAiB6G,QAAQ,CAAC/P,CAAC,KAAK+P,QAAQ,CAAC7P,CAAC,GAAG,CAAC;MAE7G,IAAI;QACF;QACA,IAAI,CAAC1D,0BAA0B,IAAI,CAACA,0BAA0B,CAACsD,KAAK,EAAE;UACpE,MAAM,IAAIwpB,KAAK,CAAC,cAAc,CAAC;QACjC;;QAEA;QACA,MAAMxV,iBAAiB,GAAGtX,0BAA0B,CAACsD,KAAK,CAAC,CAAC;;QAE5D;QACAgU,iBAAiB,CAAC7G,IAAI,GAAG,OAAOF,YAAY,CAACE,IAAI,EAAE;;QAEnD;QACA6G,iBAAiB,CAAClM,QAAQ,CAACvH,GAAG,CAAC0P,QAAQ,CAAC/P,CAAC,EAAE,EAAE,EAAE,CAAC+P,QAAQ,CAAC7P,CAAC,CAAC;;QAE3D;QACA4T,iBAAiB,CAAC1D,KAAK,CAAC/P,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;QAEvC;QACAyT,iBAAiB,CAAC1B,WAAW,GAAG,GAAG;;QAEnC;QACA0B,iBAAiB,CAACzR,QAAQ,CAACkP,KAAK,IAAI;UAClC,IAAIA,KAAK,CAAC/M,MAAM,EAAE;YAChB+M,KAAK,CAAC5M,QAAQ,CAACgN,WAAW,GAAG,KAAK;YAClCJ,KAAK,CAAC5M,QAAQ,CAAC0N,OAAO,GAAG,GAAG;YAC5Bd,KAAK,CAAC5M,QAAQ,CAACgkB,IAAI,GAAG/tB,KAAK,CAACguB,UAAU;YACtCrX,KAAK,CAAC5M,QAAQ,CAACuZ,UAAU,GAAG,IAAI;YAChC3M,KAAK,CAAC5M,QAAQ,CAAC2gB,SAAS,GAAG,IAAI;YAC/B/T,KAAK,CAAC5M,QAAQ,CAACiN,WAAW,GAAG,IAAI;YACjCL,KAAK,CAACa,WAAW,GAAG,GAAG;UACzB;QACF,CAAC,CAAC;;QAEF;QACA0B,iBAAiB,CAACsK,QAAQ,GAAG;UAC3B5O,IAAI,EAAE,cAAc;UACpBtG,OAAO,EAAE6D,YAAY,CAAC7D,OAAO;UAC7B+D,IAAI,EAAEF,YAAY,CAACE;QACrB,CAAC;;QAED;QACA;;QAEA;QACA;QACA,MAAMsc,oBAAoB,GAAG,IAAI3uB,KAAK,CAACklB,aAAa,CAAC,CAAC;QACtD,MAAM0J,eAAe,GAAG,GAAG5rB,QAAQ,qBAAqB,CAAC,CAAC;QAC1D,MAAM6rB,eAAe,GAAG,IAAI7uB,KAAK,CAAC2iB,iBAAiB,CAAC;UAClD9Z,GAAG,EAAE8lB,oBAAoB,CAACzQ,IAAI,CAAC0Q,eAAe,CAAC;UAC/C7X,WAAW,EAAE,IAAI;UACjBU,OAAO,EAAE;QACX,CAAC,CAAC;QACF;QACA,MAAMqX,eAAe,GAAG,IAAI9uB,KAAK,CAACylB,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;QACrD,MAAMsJ,WAAW,GAAG,IAAI/uB,KAAK,CAAC4iB,IAAI,CAACkM,eAAe,EAAED,eAAe,CAAC;QACpE;QACAE,WAAW,CAAC/hB,QAAQ,CAACvH,GAAG,CAAC0P,QAAQ,CAAC/P,CAAC,GAAC,EAAE,EAAE,GAAG,EAAE,EAAE+P,QAAQ,CAAC7P,CAAC,GAAC,EAAE,CAAC,CAAC;QAC9D;QACAypB,WAAW,CAACxZ,QAAQ,CAACnQ,CAAC,GAAG,CAACkB,IAAI,CAACC,EAAE,GAAG,CAAC;QACrC;QACAwoB,WAAW,CAACvX,WAAW,GAAG,GAAG;QAC7B;QACAuX,WAAW,CAACvL,QAAQ,GAAG;UACrB5O,IAAI,EAAE,aAAa;UACnBtG,OAAO,EAAE6D,YAAY,CAAC7D,OAAO;UAC7B+D,IAAI,EAAEF,YAAY,CAACE;QACrB,CAAC;QACD;QACAxQ,KAAK,CAAC2F,GAAG,CAACunB,WAAW,CAAC;QACtB;;QAEA;QACAtrB,gBAAgB,CAACgC,GAAG,CAAC0M,YAAY,CAAC7D,OAAO,EAAE;UACzC/G,KAAK,EAAE2R,iBAAiB;UACxB/G,YAAY,EAAEA,YAAY;UAC1BnF,QAAQ,EAAEmI;QACZ,CAAC,CAAC;QAEF/R,OAAO,CAACC,GAAG,CAAC,SAAS8O,YAAY,CAACE,IAAI,KAAKF,YAAY,CAAC7D,OAAO,kBAAkB6G,QAAQ,CAAC/P,CAAC,KAAK,CAAC+P,QAAQ,CAAC7P,CAAC,GAAG,CAAC;MACjH,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,QAAQyN,YAAY,CAACE,IAAI,YAAY,EAAE3N,KAAK,CAAC;QAC3D;QACA;MACF;IACF;EACF,CAAC,CAAC;;EAEF;EACAtB,OAAO,CAACC,GAAG,CAAC,OAAOI,gBAAgB,CAACkY,IAAI,SAAS,CAAC;EAClDlY,gBAAgB,CAACuE,OAAO,CAAC,CAACkb,QAAQ,EAAE5U,OAAO,KAAK;IAC9ClL,OAAO,CAACC,GAAG,CAAC,QAAQiL,OAAO,KAAK4U,QAAQ,CAAC/Q,YAAY,CAACE,IAAI,EAAE,CAAC;EAC/D,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMoc,2BAA2B,GAAGA,CAAC5K,iBAAiB,EAAEzX,aAAa,KAAK;EACxEA,aAAa,CAACpE,OAAO,CAACmK,YAAY,IAAI;IACpC;IACA,IAAIA,YAAY,CAACY,eAAe,KAAK,KAAK,EAAE;MAC1C;IACF;IAEA,IAAIZ,YAAY,CAACzF,QAAQ,IAAIyF,YAAY,CAAC1F,SAAS,IAAI0F,YAAY,CAAC7D,OAAO,EAAE;MAC3E;MACA,MAAM6G,QAAQ,GAAG0O,iBAAiB,CAACtR,YAAY,CAC7CC,UAAU,CAACL,YAAY,CAAC1F,SAAS,CAAC,EAClC+F,UAAU,CAACL,YAAY,CAACzF,QAAQ,CAClC,CAAC;MAED+V,wBAAwB,CAACtQ,YAAY,EAAEgD,QAAQ,EAAE0O,iBAAiB,CAAC;IACrE;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMpB,wBAAwB,GAAGA,CAACtQ,YAAY,EAAEgD,QAAQ,EAAE0O,iBAAiB,KAAK;EAC9E;EACA,MAAMha,QAAQ,GAAG,IAAI7J,KAAK,CAAC0iB,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAClD,MAAM3Y,QAAQ,GAAG,IAAI/J,KAAK,CAAC2iB,iBAAiB,CAAC;IAC3C1T,KAAK,EAAE,QAAQ;IACf8H,WAAW,EAAE,KAAK;IAClBU,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAMyB,iBAAiB,GAAG,IAAIlZ,KAAK,CAAC4iB,IAAI,CAAC/Y,QAAQ,EAAEE,QAAQ,CAAC;;EAE5D;EACAmP,iBAAiB,CAAC7G,IAAI,GAAG,SAASF,YAAY,CAACE,IAAI,EAAE;;EAErD;EACA6G,iBAAiB,CAAClM,QAAQ,CAACvH,GAAG,CAAC0P,QAAQ,CAAC/P,CAAC,EAAE,EAAE,EAAE,CAAC+P,QAAQ,CAAC7P,CAAC,CAAC;;EAE3D;EACA4T,iBAAiB,CAAC1B,WAAW,GAAG,GAAG;;EAEnC;EACA0B,iBAAiB,CAACsK,QAAQ,GAAG;IAC3B5O,IAAI,EAAE,cAAc;IACpBtG,OAAO,EAAE6D,YAAY,CAAC7D,OAAO;IAC7B+D,IAAI,EAAEF,YAAY,CAACE;EACrB,CAAC;;EAED;EACA,MAAM2c,gBAAgB,GAAG,IAAIhvB,KAAK,CAACojB,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5D,MAAM6L,gBAAgB,GAAG,IAAIjvB,KAAK,CAAC2iB,iBAAiB,CAAC;IACnD1T,KAAK,EAAE,QAAQ;IACf8H,WAAW,EAAE,IAAI;IACjBU,OAAO,EAAE,GAAG;IAAG;IACf6L,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAM4L,QAAQ,GAAG,IAAIlvB,KAAK,CAAC4iB,IAAI,CAACoM,gBAAgB,EAAEC,gBAAgB,CAAC;EACnEC,QAAQ,CAAC7c,IAAI,GAAG,YAAYF,YAAY,CAACE,IAAI,EAAE;EAC/C6c,QAAQ,CAAC1L,QAAQ,GAAG;IAClB5O,IAAI,EAAE,cAAc;IACpBtG,OAAO,EAAE6D,YAAY,CAAC7D,OAAO;IAC7B+D,IAAI,EAAEF,YAAY,CAACE,IAAI;IACvB8c,UAAU,EAAE;EACd,CAAC;EAEDjW,iBAAiB,CAAC1R,GAAG,CAAC0nB,QAAQ,CAAC;;EAE/B;EACArtB,KAAK,CAAC2F,GAAG,CAAC0R,iBAAiB,CAAC;;EAE5B;EACAzV,gBAAgB,CAACgC,GAAG,CAAC0M,YAAY,CAAC7D,OAAO,EAAE;IACzC/G,KAAK,EAAE2R,iBAAiB;IACxB/G,YAAY,EAAEA,YAAY;IAC1BnF,QAAQ,EAAEmI;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMia,aAAa,GAAG,IAAIpvB,KAAK,CAACojB,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAMiM,aAAa,GAAG,IAAIrvB,KAAK,CAAC2iB,iBAAiB,CAAC;IAAE1T,KAAK,EAAE;EAAS,CAAC,CAAC;EACtE,MAAMqgB,SAAS,GAAG,IAAItvB,KAAK,CAAC4iB,IAAI,CAACwM,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAACtiB,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;EAEhC;EACA6pB,SAAS,CAAC9L,QAAQ,GAAG;IACnB5O,IAAI,EAAE,cAAc;IACpBtG,OAAO,EAAE6D,YAAY,CAAC7D,OAAO;IAC7B+D,IAAI,EAAEF,YAAY,CAACE;EACrB,CAAC;EAED6G,iBAAiB,CAAC1R,GAAG,CAAC8nB,SAAS,CAAC;EAEhClsB,OAAO,CAACC,GAAG,CAAC,SAAS8O,YAAY,CAACE,IAAI,KAAKF,YAAY,CAAC7D,OAAO,kBAAkB6G,QAAQ,CAAC/P,CAAC,KAAK,CAAC+P,QAAQ,CAAC7P,CAAC,GAAG,CAAC;AACjH,CAAC;;AAED;AACA,MAAMmT,iBAAiB,GAAIL,OAAO,IAAK;EACrC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMI,oBAAoB,GAAI+W,OAAO,IAAK;EACxC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMzG,kBAAkB,GAAI0G,eAAe,IAAK;EAC9C;EACA,IAAIhtB,MAAM,CAACkM,0BAA0B,IAAIlM,MAAM,CAACkM,0BAA0B,CAAC2B,OAAO,EAAE;IAClFkR,aAAa,CAAC/e,MAAM,CAACkM,0BAA0B,CAAC2B,OAAO,CAAC;IACxD7N,MAAM,CAACkM,0BAA0B,CAAC2B,OAAO,GAAG,IAAI;IAChDjN,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B;;EAEA;EACA,IAAIb,MAAM,CAACiM,mBAAmB,EAAE;IAC9BjM,MAAM,CAACiM,mBAAmB,CAAC4B,OAAO,GAAG,IAAI;EAC3C;;EAEA;EACAmf,eAAe,CAACpW,IAAI,KAAK;IACvB,GAAGA,IAAI;IACP/K,OAAO,EAAE,KAAK;IACdE,OAAO,EAAE,IAAI;IAAE;IACfC,MAAM,EAAE,EAAE,CAAK;EACjB,CAAC,CAAC,CAAC;EAEHpL,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;AAChC,CAAC;;AAED;AACAb,MAAM,CAACitB,qBAAqB,GAAInhB,OAAO,IAAK;EAC1C,IAAI;IAAA,IAAAohB,qBAAA,EAAAC,sBAAA;IACF;IACA,MAAMhX,YAAY,GAAGlV,gBAAgB,CAACmC,GAAG,CAAC0I,OAAO,IAAI,GAAG,CAAC;IACzD,IAAI,CAACqK,YAAY,EAAE;MACjBvV,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAE4J,OAAO,CAAC;;MAEtC;MACAlL,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxBI,gBAAgB,CAACuE,OAAO,CAAC,CAAC4nB,KAAK,EAAElb,EAAE,KAAK;QACtCtR,OAAO,CAACC,GAAG,CAAC,KAAKqR,EAAE,KAAKkb,KAAK,CAACzd,YAAY,CAACE,IAAI,EAAE,CAAC;MACpD,CAAC,CAAC;MAEF,OAAO,KAAK;IACd;;IAEA;IACA,MAAMwd,UAAU,GAAGlX,YAAY,CAACpR,KAAK;;IAErC;IACA,MAAMuoB,SAAS,GAAGpsB,kBAAkB,CAACkC,GAAG,CAAC0I,OAAO,CAAC;IACjD,MAAM6D,YAAY,GAAGwG,YAAY,CAACxG,YAAY;;IAE9C;IACA,IAAI5D,OAAO;IAEX,IAAIuhB,SAAS,IAAIA,SAAS,CAACthB,MAAM,EAAE;MACjCD,OAAO,gBACL1N,OAAA;QAAKmnB,KAAK,EAAE;UAAExa,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAE0Z,SAAS,EAAE,OAAO;UAAEuH,SAAS,EAAE;QAAO,CAAE;QAAApR,QAAA,gBACpF9d,OAAA;UAAKmnB,KAAK,EAAE;YACV9Y,UAAU,EAAE,MAAM;YAClB8gB,YAAY,EAAE,KAAK;YACnBniB,QAAQ,EAAE,MAAM;YAChBoiB,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAAvR,QAAA,GACCxM,YAAY,CAACE,IAAI,EAAC,QAAM,EAAC/D,OAAO,EAAC,GACpC;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNtP,OAAA;UAAA8d,QAAA,EACGmR,SAAS,CAACthB,MAAM,CAAC3F,GAAG,CAAC,CAACsP,KAAK,EAAEgY,KAAK,KAAK;YACtC,IAAIC,UAAU;YACd,QAAQjY,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBAAEyX,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;gBAAEA,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;cAAE;gBAASA,UAAU,GAAG,SAAS;gBAAE;YAC7C;YAEA,oBACEvvB,OAAA;cAAiBmnB,KAAK,EAAE;gBACtBgI,YAAY,EAAE,KAAK;gBACnBviB,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE,KAAK;gBACnBE,QAAQ,EAAE;cACZ,CAAE;cAAA8Q,QAAA,gBACA9d,OAAA;gBAAKmnB,KAAK,EAAE;kBAAE9Y,UAAU,EAAE;gBAAO,CAAE;gBAAAyP,QAAA,EAChClG,iBAAiB,CAACN,KAAK,CAACC,OAAO;cAAC;gBAAApI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNtP,OAAA;gBAAKmnB,KAAK,EAAE;kBAAE3a,OAAO,EAAE,MAAM;kBAAEgjB,cAAc,EAAE;gBAAgB,CAAE;gBAAA1R,QAAA,gBAC/D9d,OAAA;kBAAA8d,QAAA,EAAM;gBAAI;kBAAA3O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBtP,OAAA;kBAAMmnB,KAAK,EAAE;oBACX/Y,KAAK,EAAEmhB,UAAU;oBACjBlhB,UAAU,EAAE,MAAM;oBAClBzB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAAgR,QAAA,EACCxG,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGR,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAG;gBAAI;kBAAA3I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNtP,OAAA;gBAAKmnB,KAAK,EAAE;kBAAE3a,OAAO,EAAE,MAAM;kBAAEgjB,cAAc,EAAE;gBAAgB,CAAE;gBAAA1R,QAAA,gBAC/D9d,OAAA;kBAAA8d,QAAA,EAAM;gBAAK;kBAAA3O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBtP,OAAA;kBAAMmnB,KAAK,EAAE;oBAAE9Y,UAAU,EAAE;kBAAO,CAAE;kBAAAyP,QAAA,GAAExG,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAA5I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAzBEggB,KAAK;cAAAngB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNtP,OAAA;UAAKmnB,KAAK,EAAE;YAAEsI,SAAS,EAAE,KAAK;YAAEziB,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE;UAAO,CAAE;UAAA0P,QAAA,GAAC,4BAC3D,EAAC,IAAIlT,IAAI,CAAC,CAAC,CAAC8kB,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAAvgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACL5B,OAAO,gBACL1N,OAAA;QAAKmnB,KAAK,EAAE;UAAExa,OAAO,EAAE,KAAK;UAAEkb,QAAQ,EAAE;QAAQ,CAAE;QAAA/J,QAAA,gBAChD9d,OAAA;UAAKmnB,KAAK,EAAE;YAAE9Y,UAAU,EAAE,MAAM;YAAE8gB,YAAY,EAAE;UAAM,CAAE;UAAArR,QAAA,EAAExM,YAAY,CAACE;QAAI;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClFtP,OAAA;UAAA8d,QAAA,GAAK,kBAAM,EAACrQ,OAAO;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1BtP,OAAA;UAAA8d,QAAA,EAAK;QAAU;UAAA3O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN;IACH;;IAEA;IACA,MAAMqgB,OAAO,GAAGhuB,MAAM,CAAC6Z,UAAU,GAAG,CAAC,GAAE,GAAG;IAC1C,MAAMoU,OAAO,GAAGjuB,MAAM,CAAC8Z,WAAW,GAAG,CAAC,GAAE,GAAG;;IAE3C;IACA,MAAMkT,eAAe,IAAAE,qBAAA,GAAGnG,QAAQ,CAACqB,aAAa,CAAC,OAAO,CAAC,cAAA8E,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCgB,gBAAgB,cAAAf,sBAAA,uBAAjDA,sBAAA,CAAmDvhB,sBAAsB;IAEjG,IAAIohB,eAAe,EAAE;MACnB;MACAA,eAAe,CAAC;QACdnhB,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBtB,QAAQ,EAAE;UAAE5H,CAAC,EAAEorB,OAAO;UAAElrB,CAAC,EAAEmrB;QAAQ,CAAC;QACpCliB,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAAshB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEthB,MAAM,KAAI;MAC/B,CAAC,CAAC;MAEFpL,OAAO,CAACC,GAAG,CAAC,SAAS8O,YAAY,CAACE,IAAI,KAAK/D,OAAO,UAAU,CAAC;MAC7D,OAAO,IAAI;IACb,CAAC,MAAM;MACL;MACA,MAAMqiB,OAAO,GAAGpH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CmH,OAAO,CAAC3I,KAAK,CAAChb,QAAQ,GAAG,UAAU;MACnC2jB,OAAO,CAAC3I,KAAK,CAAC9a,IAAI,GAAG,GAAGsjB,OAAO,IAAI;MACnCG,OAAO,CAAC3I,KAAK,CAACnZ,GAAG,GAAG,GAAG4hB,OAAO,IAAI;MAClCE,OAAO,CAAC3I,KAAK,CAAC7a,SAAS,GAAG,wBAAwB;MAClDwjB,OAAO,CAAC3I,KAAK,CAAC5a,MAAM,GAAG,MAAM;MAC7BujB,OAAO,CAAC3I,KAAK,CAACva,eAAe,GAAG,qBAAqB;MACrDkjB,OAAO,CAAC3I,KAAK,CAAC/Y,KAAK,GAAG,OAAO;MAC7B0hB,OAAO,CAAC3I,KAAK,CAACra,YAAY,GAAG,KAAK;MAClCgjB,OAAO,CAAC3I,KAAK,CAACla,SAAS,GAAG,8BAA8B;MACxD6iB,OAAO,CAAC3I,KAAK,CAACxa,OAAO,GAAG,KAAK;MAC7BmjB,OAAO,CAAC3I,KAAK,CAACU,QAAQ,GAAG,OAAO;MAChCiI,OAAO,CAAC3I,KAAK,CAACna,QAAQ,GAAG,MAAM;MAE/B8iB,OAAO,CAACC,SAAS,GAAG;AAC1B;AACA,YAAYze,YAAY,CAACE,IAAI,SAAS/D,OAAO;AAC7C;AACA;AACA,qBAAqBA,OAAO;AAC5B,eAAewhB,SAAS,GAAG,UAAU,GAAG,YAAY;AACpD;AACA;AACA,OAAO;MAEDvG,QAAQ,CAACsH,IAAI,CAAC/T,WAAW,CAAC6T,OAAO,CAAC;;MAElC;MACA,MAAMG,WAAW,GAAGH,OAAO,CAAC/F,aAAa,CAAC,QAAQ,CAAC;MACnD,IAAIkG,WAAW,EAAE;QACfA,WAAW,CAAC9P,gBAAgB,CAAC,OAAO,EAAE,MAAM;UAC1CuI,QAAQ,CAACsH,IAAI,CAAC/O,WAAW,CAAC6O,OAAO,CAAC;QACpC,CAAC,CAAC;MACJ;MAEAvtB,OAAO,CAACC,GAAG,CAAC,gBAAgB8O,YAAY,CAACE,IAAI,KAAK/D,OAAO,OAAO,CAAC;MACjE,OAAO,IAAI;IACb;EACF,CAAC,CAAC,OAAO5J,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACAlC,MAAM,CAACuuB,iBAAiB,GAAG,MAAM;EAC/B3tB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;EAErB,IAAI,CAACI,gBAAgB,IAAIA,gBAAgB,CAACkY,IAAI,KAAK,CAAC,EAAE;IACpDvY,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,OAAO,EAAE;EACX;EAEA,MAAM2tB,IAAI,GAAG,EAAE;EACfvtB,gBAAgB,CAACuE,OAAO,CAAC,CAAC4nB,KAAK,EAAElb,EAAE,KAAK;IACtCtR,OAAO,CAACC,GAAG,CAAC,SAASqR,EAAE,SAASkb,KAAK,CAACzd,YAAY,CAACE,IAAI,EAAE,CAAC;IAC1D2e,IAAI,CAACjY,IAAI,CAAC;MACRrE,EAAE;MACFrC,IAAI,EAAEud,KAAK,CAACzd,YAAY,CAACE,IAAI;MAC7BrF,QAAQ,EAAE4iB,KAAK,CAAC5iB;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOgkB,IAAI;AACb,CAAC;;AAGD;AACAxuB,MAAM,CAACyQ,qBAAqB,GAAI3E,OAAO,IAAK;EAC1C,IAAI;IACF;IACAA,OAAO,GAAG2K,MAAM,CAAC3K,OAAO,CAAC;IAEzBlL,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEiL,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;IAC/ElL,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,gBAAgB,CAACkY,IAAI,CAAC;;IAE3D;IACA,IAAIhD,YAAY,GAAGlV,gBAAgB,CAACmC,GAAG,CAAC0I,OAAO,CAAC;IAChD,IAAI,CAACqK,YAAY,EAAE;MACjB;MACA,MAAMsY,SAAS,GAAGpY,QAAQ,CAACvK,OAAO,CAAC;MACnCqK,YAAY,GAAGlV,gBAAgB,CAACmC,GAAG,CAACqrB,SAAS,CAAC;MAE9C,IAAItY,YAAY,EAAE;QAChBvV,OAAO,CAACC,GAAG,CAAC,UAAU4tB,SAAS,SAAS,CAAC;QACzC3iB,OAAO,GAAG2iB,SAAS,CAAC,CAAC;MACvB;IACF;IAEA,IAAI,CAACtY,YAAY,EAAE;MACjBvV,OAAO,CAACsB,KAAK,CAAC,cAAc,EAAE4J,OAAO,CAAC;MACtC,OAAO,KAAK;IACd;IAEA,MAAMwhB,SAAS,GAAGpsB,kBAAkB,CAACkC,GAAG,CAAC0I,OAAO,CAAC;IACjD,MAAM6D,YAAY,GAAGwG,YAAY,CAACxG,YAAY;;IAE9C;IACA,MAAM+e,YAAY,GAAGpB,SAAS,IAAIA,SAAS,CAACthB,MAAM,IAAIshB,SAAS,CAACthB,MAAM,CAACjG,MAAM,GAAG,CAAC;IAEjF,IAAIgG,OAAO;;IAEX;IACA,MAAM4iB,YAAY,GAAG;MACnBnkB,QAAQ,EAAE,UAAU;MACpB6B,GAAG,EAAE,KAAK;MACV8Z,KAAK,EAAE,MAAM;MACb7Z,KAAK,EAAE,MAAM;MACbwG,MAAM,EAAE,MAAM;MACdjI,OAAO,EAAE,MAAM;MACfgjB,cAAc,EAAE,QAAQ;MACxBe,UAAU,EAAE,QAAQ;MACpBzjB,YAAY,EAAE,KAAK;MACnBib,UAAU,EAAE,iBAAiB;MAC7Bxb,MAAM,EAAE;IACV,CAAC;;IAED;IACA,MAAMikB,WAAW,GAAGA,CAAA,kBAClBxwB,OAAA;MAAKmnB,KAAK,EAAEmJ,YAAa;MAAAxS,QAAA,eACvB9d,OAAA;QAAKmnB,KAAK,EAAE;UACV3a,OAAO,EAAE,MAAM;UACfikB,aAAa,EAAE,QAAQ;UACvBF,UAAU,EAAE,QAAQ;UACpBjkB,SAAS,EAAE;QACb,CAAE;QAAAwR,QAAA,gBACA9d,OAAA;UAAMmnB,KAAK,EAAE;YACX/Y,KAAK,EAAE,SAAS;YAChBpB,QAAQ,EAAE,MAAM;YAChBqB,UAAU,EAAE,MAAM;YAClBF,UAAU,EAAE;UACd,CAAE;UAAA2P,QAAA,EAAC;QAAC;UAAA3O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACXtP,OAAA;UAAMmnB,KAAK,EAAE;YACXlZ,KAAK,EAAE,CAAC;YACRwG,MAAM,EAAE,CAAC;YACTic,UAAU,EAAE,uBAAuB;YACnCC,WAAW,EAAE,uBAAuB;YACpCvB,YAAY,EAAE,oBAAoB;YAClCK,SAAS,EAAE;UACb;QAAE;UAAAtgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;IAED,IAAI+gB,YAAY,EAAE;MAChB;MACA,MAAMO,QAAQ,GAAG;QACf,GAAG,EAAE;UAAEC,GAAG,EAAE,GAAG;UAAE9c,IAAI,EAAE;QAAO,CAAC;QAAE,GAAG,EAAE;UAAE8c,GAAG,EAAE,GAAG;UAAE9c,IAAI,EAAE;QAAW,CAAC;QAAE,GAAG,EAAE;UAAE8c,GAAG,EAAE,GAAG;UAAE9c,IAAI,EAAE;QAAQ,CAAC;QACtG,GAAG,EAAE;UAAE8c,GAAG,EAAE,GAAG;UAAE9c,IAAI,EAAE;QAAO,CAAC;QAAE,GAAG,EAAE;UAAE8c,GAAG,EAAE,GAAG;UAAE9c,IAAI,EAAE;QAAW,CAAC;QAAE,GAAG,EAAE;UAAE8c,GAAG,EAAE,GAAG;UAAE9c,IAAI,EAAE;QAAQ,CAAC;QACtG,GAAG,EAAE;UAAE8c,GAAG,EAAE,GAAG;UAAE9c,IAAI,EAAE;QAAO,CAAC;QAAE,IAAI,EAAE;UAAE8c,GAAG,EAAE,GAAG;UAAE9c,IAAI,EAAE;QAAW,CAAC;QAAE,IAAI,EAAE;UAAE8c,GAAG,EAAE,GAAG;UAAE9c,IAAI,EAAE;QAAQ,CAAC;QACxG,IAAI,EAAE;UAAE8c,GAAG,EAAE,GAAG;UAAE9c,IAAI,EAAE;QAAO,CAAC;QAAE,IAAI,EAAE;UAAE8c,GAAG,EAAE,GAAG;UAAE9c,IAAI,EAAE;QAAW,CAAC;QAAE,IAAI,EAAE;UAAE8c,GAAG,EAAE,GAAG;UAAE9c,IAAI,EAAE;QAAQ;MAC1G,CAAC;MAED,MAAM+c,SAAS,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC;MAC/C,MAAMC,QAAQ,GAAG;QAAEC,CAAC,EAAE,SAAS;QAAEC,CAAC,EAAE,SAAS;QAAEC,CAAC,EAAE;MAAU,CAAC;MAC7D,MAAMC,OAAO,GAAG;QAAEC,CAAC,EAAE,CAAC,CAAC;QAAEC,CAAC,EAAE,CAAC,CAAC;QAAEC,CAAC,EAAE,CAAC,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAE,CAAC;MAE9CtC,SAAS,CAACthB,MAAM,CAACxG,OAAO,CAACmQ,KAAK,IAAI;QAChC,MAAMtP,GAAG,GAAG4oB,QAAQ,CAACtZ,KAAK,CAACC,OAAO,CAAC;QACnC,IAAIvP,GAAG,EAAE;UACPmpB,OAAO,CAACnpB,GAAG,CAAC6oB,GAAG,CAAC,CAAC7oB,GAAG,CAAC+L,IAAI,CAAC,GAAG;YAC3B3F,KAAK,EAAE2iB,QAAQ,CAACzZ,KAAK,CAACQ,YAAY,CAAC,IAAI,MAAM;YAC7CC,UAAU,EAAET,KAAK,CAACS;UACpB,CAAC;QACH;MACF,CAAC,CAAC;MAEFrK,OAAO,gBACL1N,OAAA;QAAKmnB,KAAK,EAAE;UAAExa,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAE8Z,UAAU,EAAE,kBAAkB;UAAE5b,QAAQ,EAAE;QAAW,CAAE;QAAA2R,QAAA,gBACnG9d,OAAA,CAACwwB,WAAW;UAAArhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACftP,OAAA;UAAKmnB,KAAK,EAAE;YAAE9Y,UAAU,EAAE,MAAM;YAAE8gB,YAAY,EAAE,KAAK;YAAEniB,QAAQ,EAAE,MAAM;YAAEmc,SAAS,EAAE;UAAS,CAAE;UAAArL,QAAA,GAAExM,YAAY,CAACE,IAAI,EAAC,cAAE;QAAA;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3HtP,OAAA;UAAKmnB,KAAK,EAAE;YACV3a,OAAO,EAAE,MAAM;YACfglB,gBAAgB,EAAE,gBAAgB;YAClCC,mBAAmB,EAAE,gBAAgB;YACrCjC,cAAc,EAAE,QAAQ;YACxBe,UAAU,EAAE,QAAQ;YACpBxI,UAAU,EAAE,wBAAwB;YACpCjb,YAAY,EAAE,KAAK;YACnB4kB,MAAM,EAAE,QAAQ;YAChBvlB,QAAQ,EAAE;UACZ,CAAE;UAAA2R,QAAA,gBAGA9d,OAAA;YAAKmnB,KAAK,EAAE;cAAEwK,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEzI,SAAS,EAAE,QAAQ;cAAE3c,OAAO,EAAE,MAAM;cAAEgjB,cAAc,EAAE,QAAQ;cAAEvhB,KAAK,EAAE;YAAO,CAAE;YAAA6P,QAAA,EACtHgT,SAAS,CAAC9oB,GAAG,CAAC,CAAC+L,IAAI,EAAEub,KAAK,KAAK;cAC9B;cACA,IAAIuC,YAAY,GAAGvC,KAAK;cACxB,IAAIA,KAAK,KAAK,CAAC,EAAEuC,YAAY,GAAG,CAAC,CAAC,KAC7B,IAAIvC,KAAK,KAAK,CAAC,EAAEuC,YAAY,GAAG,CAAC;cAEtC,MAAMC,WAAW,GAAGhB,SAAS,CAACe,YAAY,CAAC;;cAE3C;cACA,MAAME,WAAW,GAAG,CAAC,CAAC;cACtB,IAAID,WAAW,KAAK,MAAM,EAAE;gBAAE;gBAC5BC,WAAW,CAACC,WAAW,GAAG,KAAK;cACjC,CAAC,MAAM,IAAIF,WAAW,KAAK,UAAU,EAAE;gBAAE;gBACvCC,WAAW,CAACE,UAAU,GAAG,MAAM;gBAC/BF,WAAW,CAACC,WAAW,GAAG,MAAM;cAClC,CAAC,MAAM,IAAIF,WAAW,KAAK,OAAO,EAAE;gBAAE;gBACpCC,WAAW,CAACE,UAAU,GAAG,KAAK;cAChC;cAEA,OAAOd,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC;cAAA;cAC3B;cACA;cACA;cACA;cACA;cACA;cACA9xB,OAAA;gBAAuBmnB,KAAK,EAAE;kBAAC6K,WAAW,EAAEF,WAAW,KAAK,MAAM,GAAG,CAAC,GAAG,MAAM;kBAAEtlB,OAAO,EAAE,MAAM;kBAAEikB,aAAa,EAAE,QAAQ;kBAAEF,UAAU,EAAE;gBAAQ,CAAE;gBAAAzS,QAAA,gBAC/I9d,OAAA;kBAAKmnB,KAAK,EAAE;oBAACna,QAAQ,EAAC,MAAM;oBAAEoB,KAAK,EAAE+iB,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAAC1jB,KAAK;oBAAEC,UAAU,EAAC,MAAM;oBAAE8gB,YAAY,EAAE;kBAAK,CAAE;kBAAArR,QAAA,EAAEqT,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAAC/Z;gBAAU;kBAAA5I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrJtP,OAAA;kBAAMmnB,KAAK,EAAE;oBAAC/Y,KAAK,EAAE+iB,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAAC1jB,KAAK;oBAAEpB,QAAQ,EAAC,MAAM;oBAAEmB,UAAU,EAAE;kBAAM,CAAE;kBAAA2P,QAAA,EACrFgU,WAAW,KAAK,MAAM,GAAG,WAAW,GAAGA,WAAW,KAAK,UAAU,GAAG,WAAW,GAAG;gBAAW;kBAAA3iB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA,GAJCwiB,WAAW;gBAAA3iB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKhB,CACN;YACH,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNtP,OAAA;YAAKmnB,KAAK,EAAE;cAAEwK,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEzI,SAAS,EAAE,QAAQ;cAAE3c,OAAO,EAAE,MAAM;cAAEgjB,cAAc,EAAE,QAAQ;cAAEvhB,KAAK,EAAE;YAAO,CAAE;YAAA6P,QAAA,EACtHgT,SAAS,CAAC9oB,GAAG,CAAC+L,IAAI,IAAIod,OAAO,CAACG,CAAC,CAACvd,IAAI,CAAC,iBACpC/T,OAAA;cAAgBmnB,KAAK,EAAE;gBAAC6K,WAAW,EAAEje,IAAI,KAAK,OAAO,GAAG,CAAC,GAAG,MAAM;gBAAEvH,OAAO,EAAE,MAAM;gBAAEikB,aAAa,EAAE,QAAQ;gBAAEF,UAAU,EAAE;cAAQ,CAAE;cAAAzS,QAAA,gBAClI9d,OAAA;gBAAMmnB,KAAK,EAAE;kBAAC/Y,KAAK,EAAE+iB,OAAO,CAACG,CAAC,CAACvd,IAAI,CAAC,CAAC3F,KAAK;kBAAEpB,QAAQ,EAAC,MAAM;kBAAEmB,UAAU,EAAE;gBAAM,CAAE;gBAAA2P,QAAA,EAC9E/J,IAAI,KAAK,MAAM,GAAG,WAAW,GAAGA,IAAI,KAAK,UAAU,GAAG,WAAW,GAAG;cAAW;gBAAA5E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACPtP,OAAA;gBAAKmnB,KAAK,EAAE;kBAACna,QAAQ,EAAC,MAAM;kBAAEoB,KAAK,EAAE+iB,OAAO,CAACG,CAAC,CAACvd,IAAI,CAAC,CAAC3F,KAAK;kBAAEC,UAAU,EAAC,MAAM;kBAAEohB,SAAS,EAAE;gBAAK,CAAE;gBAAA3R,QAAA,EAAEqT,OAAO,CAACG,CAAC,CAACvd,IAAI,CAAC,CAACgE;cAAU;gBAAA5I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAJ5HyE,IAAI;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKT,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAINtP,OAAA;YAAKmnB,KAAK,EAAE;cAAEwK,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEzI,SAAS,EAAE;YAAS,CAAE;YAAArL,QAAA,EAC5DgT,SAAS,CAAC9oB,GAAG,CAAC,CAAC+L,IAAI,EAAEub,KAAK,KAAK;cAC9B;cACA,IAAIuC,YAAY,GAAGvC,KAAK;cACxB,IAAIA,KAAK,KAAK,CAAC,EAAEuC,YAAY,GAAG,CAAC,CAAC,KAC7B,IAAIvC,KAAK,KAAK,CAAC,EAAEuC,YAAY,GAAG,CAAC;cAEtC,MAAMC,WAAW,GAAGhB,SAAS,CAACe,YAAY,CAAC;cAE3C,OAAOV,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,iBAC3B9xB,OAAA;gBAAuBmnB,KAAK,EAAE;kBAACgI,YAAY,EAAC,KAAK;kBAAE3iB,OAAO,EAAE,MAAM;kBAAE+jB,UAAU,EAAE,QAAQ;kBAAEf,cAAc,EAAE;gBAAY,CAAE;gBAAA1R,QAAA,gBACtH9d,OAAA;kBAAMmnB,KAAK,EAAE;oBAAC/Y,KAAK,EAAE+iB,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAAC1jB,KAAK;oBAAEpB,QAAQ,EAAC,MAAM;oBAAEmB,UAAU,EAAE;kBAAM,CAAE;kBAAA2P,QAAA,EACrFgU,WAAW,KAAK,MAAM,GAAG,WAAW,GAAGA,WAAW,KAAK,UAAU,GAAG,WAAW,GAAG;gBAAW;kBAAA3iB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC,eACPtP,OAAA;kBAAKmnB,KAAK,EAAE;oBACVna,QAAQ,EAAC,MAAM;oBACfoB,KAAK,EAAE+iB,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAAC1jB,KAAK;oBACnCC,UAAU,EAAC,MAAM;oBACjB4jB,UAAU,EAAE;kBACd,CAAE;kBAAAnU,QAAA,EAAEqT,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAAC/Z;gBAAU;kBAAA5I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GATpCwiB,WAAW;gBAAA3iB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUhB,CACN;YACH,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNtP,OAAA;YAAKmnB,KAAK,EAAE;cAAEwK,OAAO,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEzI,SAAS,EAAE;YAAS,CAAE;YAAArL,QAAA,EAC5DgT,SAAS,CAAC9oB,GAAG,CAAC+L,IAAI,IAAIod,OAAO,CAACI,CAAC,CAACxd,IAAI,CAAC,iBACpC/T,OAAA;cAAgBmnB,KAAK,EAAE;gBAACgI,YAAY,EAAC,KAAK;gBAAE3iB,OAAO,EAAE,MAAM;gBAAE+jB,UAAU,EAAE,QAAQ;gBAAEf,cAAc,EAAE;cAAU,CAAE;cAAA1R,QAAA,gBAC7G9d,OAAA;gBAAKmnB,KAAK,EAAE;kBACVna,QAAQ,EAAC,MAAM;kBACfoB,KAAK,EAAE+iB,OAAO,CAACI,CAAC,CAACxd,IAAI,CAAC,CAAC3F,KAAK;kBAC5BC,UAAU,EAAC,MAAM;kBACjB2jB,WAAW,EAAE;gBACf,CAAE;gBAAAlU,QAAA,EAAEqT,OAAO,CAACI,CAAC,CAACxd,IAAI,CAAC,CAACgE;cAAU;gBAAA5I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrCtP,OAAA;gBAAMmnB,KAAK,EAAE;kBAAC/Y,KAAK,EAAE+iB,OAAO,CAACI,CAAC,CAACxd,IAAI,CAAC,CAAC3F,KAAK;kBAAEpB,QAAQ,EAAC,MAAM;kBAAEmB,UAAU,EAAE;gBAAM,CAAE;gBAAA2P,QAAA,EAC9E/J,IAAI,KAAK,MAAM,GAAG,WAAW,GAAGA,IAAI,KAAK,UAAU,GAAG,WAAW,GAAG;cAAW;gBAAA5E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA,GATCyE,IAAI;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUT,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtP,OAAA;UAAKmnB,KAAK,EAAE;YAAEsI,SAAS,EAAE,KAAK;YAAEziB,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE,MAAM;YAAE+a,SAAS,EAAE;UAAS,CAAE;UAAArL,QAAA,GAAC,4BAChF,EAAC,IAAIlT,IAAI,CAAC,CAAC,CAAC8kB,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAAvgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACL;MACA5B,OAAO,gBACL1N,OAAA;QAAKmnB,KAAK,EAAE;UAAExa,OAAO,EAAE,MAAM;UAAEsB,KAAK,EAAE,OAAO;UAAE8Z,UAAU,EAAE,kBAAkB;UAAE5b,QAAQ,EAAE;QAAW,CAAE;QAAA2R,QAAA,gBACpG9d,OAAA,CAACwwB,WAAW;UAAArhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACftP,OAAA;UAAKmnB,KAAK,EAAE;YAAE9Y,UAAU,EAAE,MAAM;YAAE8gB,YAAY,EAAE,MAAM;YAAEniB,QAAQ,EAAE,MAAM;YAAEmc,SAAS,EAAE;UAAS,CAAE;UAAArL,QAAA,EAAExM,YAAY,CAACE;QAAI;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1HtP,OAAA;UAAKmnB,KAAK,EAAE;YACVgC,SAAS,EAAE,QAAQ;YACnBxc,OAAO,EAAE,QAAQ;YACjByB,KAAK,EAAE,SAAS;YAChBpB,QAAQ,EAAE,MAAM;YAChBqB,UAAU,EAAE,MAAM;YAClB0Z,UAAU,EAAE,uBAAuB;YACnCjb,YAAY,EAAE,KAAK;YACnBqiB,YAAY,EAAE;UAChB,CAAE;UAAArR,QAAA,EAAC;QAEH;UAAA3O,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNtP,OAAA;UAAKmnB,KAAK,EAAE;YAAEna,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE,MAAM;YAAE+a,SAAS,EAAE;UAAS,CAAE;UAAArL,QAAA,GAAC,kBAC9D,EAACrQ,OAAO;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACNtP,OAAA;UAAKmnB,KAAK,EAAE;YAAEsI,SAAS,EAAE,KAAK;YAAEziB,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE,MAAM;YAAE+a,SAAS,EAAE;UAAS,CAAE;UAAArL,QAAA,GAAC,4BAChF,EAAC,IAAIlT,IAAI,CAAC,CAAC,CAAC8kB,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAAvgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH;;IAEA;IACA,MAAM/K,CAAC,GAAG,GAAG;IACb,MAAME,CAAC,GAAG,GAAG;;IAEb;IACA,IAAI9C,MAAM,CAACiM,mBAAmB,EAAE;MAC9BjM,MAAM,CAACiM,mBAAmB,CAAC4B,OAAO,GAAG/B,OAAO;IAC9C;;IAEA;IACA,IAAI9L,MAAM,CAACkM,0BAA0B,IAAIlM,MAAM,CAACkM,0BAA0B,CAAC2B,OAAO,EAAE;MAClFkR,aAAa,CAAC/e,MAAM,CAACkM,0BAA0B,CAAC2B,OAAO,CAAC;MACxD7N,MAAM,CAACkM,0BAA0B,CAAC2B,OAAO,GAAG,IAAI;IAClD;;IAEA;IACA,IAAI7N,MAAM,CAACmM,uBAAuB,EAAE;MAClCnM,MAAM,CAACmM,uBAAuB,CAAC;QAC7BN,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBtB,QAAQ,EAAE;UAAE5H,CAAC;UAAEE;QAAE,CAAC;QAClBiJ,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAAshB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEthB,MAAM,KAAI;MAC/B,CAAC,CAAC;;MAEF;MACA,IAAIhM,MAAM,CAACkM,0BAA0B,EAAE;QACrClM,MAAM,CAACkM,0BAA0B,CAAC2B,OAAO,GAAG6Q,WAAW,CAAC,MAAM;UAC5D1e,MAAM,CAACyQ,qBAAqB,CAAC3E,OAAO,CAAC;QACvC,CAAC,EAAE,IAAI,CAAC;MACV;MAEA,OAAO,IAAI;IACb,CAAC,MAAM;MACLlL,OAAO,CAACsB,KAAK,CAAC,8BAA8B,CAAC;MAC7C,OAAO,KAAK;IACd;EACF,CAAC,CAAC,OAAOA,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAID;AACA,MAAMquB,yBAAyB,GAAIrrB,MAAM,IAAK;EAC5C,IAAI2I,OAAO,GAAG3I,MAAM;;EAEpB;EACA,IAAI2I,OAAO,IAAIA,OAAO,CAACmT,QAAQ,IAAInT,OAAO,CAACmT,QAAQ,CAAC5O,IAAI,KAAK,cAAc,EAAE;IAC3ExR,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgN,OAAO,CAACgC,IAAI,IAAI,KAAK,CAAC;IAChD,OAAOhC,OAAO;EAChB;;EAEA;EACA,OAAOA,OAAO,IAAIA,OAAO,CAAC9G,MAAM,EAAE;IAChC8G,OAAO,GAAGA,OAAO,CAAC9G,MAAM;IACxB,IAAI8G,OAAO,CAACmT,QAAQ,IAAInT,OAAO,CAACmT,QAAQ,CAAC5O,IAAI,KAAK,cAAc,EAAE;MAChExR,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgN,OAAO,CAACgC,IAAI,IAAI,KAAK,CAAC;MAChD,OAAOhC,OAAO;IAChB;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACA7N,MAAM,CAACwwB,kBAAkB,GAAG,CAAC5tB,CAAC,EAAEE,CAAC,KAAK;EACpC,IAAI;IACFlC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE+B,CAAC,EAAEE,CAAC,CAAC;;IAEnC;IACA,MAAMgkB,MAAM,GAAGC,QAAQ,CAACqB,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAI,CAACtB,MAAM,EAAE;MACXlmB,OAAO,CAACsB,KAAK,CAAC,sBAAsB,CAAC;MACrC,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAAC7C,KAAK,IAAI,CAACmM,SAAS,CAACqC,OAAO,EAAE;MAChCjN,OAAO,CAACsB,KAAK,CAAC,iBAAiB,CAAC;MAChC,OAAO,KAAK;IACd;;IAEA;IACA,IAAIU,CAAC,KAAK0S,SAAS,IAAIxS,CAAC,KAAKwS,SAAS,EAAE;MACtC1S,CAAC,GAAG5C,MAAM,CAAC6Z,UAAU,GAAG,CAAC;MACzB/W,CAAC,GAAG9C,MAAM,CAAC8Z,WAAW,GAAG,CAAC;IAC5B;;IAEA;IACA,MAAMuK,IAAI,GAAGyC,MAAM,CAACxC,qBAAqB,CAAC,CAAC;IAC3C,MAAMC,MAAM,GAAI,CAAC3hB,CAAC,GAAGyhB,IAAI,CAAC3Z,IAAI,IAAIoc,MAAM,CAACrC,WAAW,GAAI,CAAC,GAAG,CAAC;IAC7D,MAAMC,MAAM,GAAG,EAAE,CAAC5hB,CAAC,GAAGuhB,IAAI,CAAChY,GAAG,IAAIya,MAAM,CAAClC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;IAE9DhkB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE0jB,MAAM,EAAEG,MAAM,CAAC;;IAErC;IACA,MAAMG,SAAS,GAAG,IAAIrnB,KAAK,CAACsnB,SAAS,CAAC,CAAC;IACvCD,SAAS,CAACE,MAAM,CAACC,MAAM,CAACC,SAAS,GAAG,CAAC;IACrCJ,SAAS,CAACE,MAAM,CAACG,IAAI,CAACD,SAAS,GAAG,CAAC;IAEnC,MAAME,WAAW,GAAG,IAAI3nB,KAAK,CAAC4nB,OAAO,CAACb,MAAM,EAAEG,MAAM,CAAC;IACrDG,SAAS,CAACQ,aAAa,CAACF,WAAW,EAAE3Z,SAAS,CAACqC,OAAO,CAAC;;IAEvD;IACA,MAAM4iB,mBAAmB,GAAG,EAAE;IAC9BxvB,gBAAgB,CAACuE,OAAO,CAAC,CAACkb,QAAQ,EAAE5U,OAAO,KAAK;MAC9C,IAAI4U,QAAQ,CAAC3b,KAAK,EAAE;QAClB0rB,mBAAmB,CAACla,IAAI,CAACmK,QAAQ,CAAC3b,KAAK,CAAC;QACxCnE,OAAO,CAACC,GAAG,CAAC,SAASiL,OAAO,QAAQ,CAAC;MACvC;IACF,CAAC,CAAC;;IAEF;IACAlL,OAAO,CAACC,GAAG,CAAC,QAAQ4vB,mBAAmB,CAAC1qB,MAAM,YAAY,CAAC;IAC3D,MAAM2qB,YAAY,GAAG7L,SAAS,CAACU,gBAAgB,CAACkL,mBAAmB,EAAE,IAAI,CAAC;IAE1E,IAAIC,YAAY,CAAC3qB,MAAM,GAAG,CAAC,EAAE;MAC3BnF,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1B6vB,YAAY,CAAClrB,OAAO,CAAC,CAACmrB,SAAS,EAAE/gB,CAAC,KAAK;QACrChP,OAAO,CAACC,GAAG,CAAC,MAAM+O,CAAC,GAAG,EAAE+gB,SAAS,CAACzrB,MAAM,CAAC2K,IAAI,IAAI,KAAK,EAC1C,KAAK,EAAE8gB,SAAS,CAACttB,QAAQ,EACzB,WAAW,EAAEstB,SAAS,CAACzrB,MAAM,CAACsF,QAAQ,CAAC6F,OAAO,CAAC,CAAC,EAChD,WAAW,EAAEsgB,SAAS,CAACzrB,MAAM,CAAC8b,QAAQ,CAAC;;QAEnD;QACA,MAAM7B,GAAG,GAAGoR,yBAAyB,CAACI,SAAS,CAACzrB,MAAM,CAAC;QACvD,IAAIia,GAAG,IAAIA,GAAG,CAAC6B,QAAQ,IAAI7B,GAAG,CAAC6B,QAAQ,CAAC5O,IAAI,KAAK,cAAc,EAAE;UAC/DxR,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEse,GAAG,CAAC6B,QAAQ,CAAClV,OAAO,CAAC;QAC/C;MACF,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;;IAEA;IACAlL,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,MAAM+vB,eAAe,GAAG/L,SAAS,CAACU,gBAAgB,CAAClmB,KAAK,CAAC8c,QAAQ,EAAE,IAAI,CAAC;IAExEvb,OAAO,CAACC,GAAG,CAAC,WAAW+vB,eAAe,CAAC7qB,MAAM,MAAM,CAAC;IACpD6qB,eAAe,CAACprB,OAAO,CAAC,CAACmrB,SAAS,EAAE/gB,CAAC,KAAK;MACxC,MAAMuP,GAAG,GAAGwR,SAAS,CAACzrB,MAAM;MAC5BtE,OAAO,CAACC,GAAG,CAAC,QAAQ+O,CAAC,GAAG,EAAEuP,GAAG,CAACtP,IAAI,IAAI,KAAK,EAC/B,KAAK,EAAEsP,GAAG,CAAC/M,IAAI,EACf,KAAK,EAAE+M,GAAG,CAAC3U,QAAQ,CAAC6F,OAAO,CAAC,CAAC,EAC7B,KAAK,EAAEsgB,SAAS,CAACttB,QAAQ,EACzB,WAAW,EAAE8b,GAAG,CAAC6B,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACApgB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,IAAIgwB,YAAY,GAAG,CAAC;IAEpB5vB,gBAAgB,CAACuE,OAAO,CAAC,CAACkb,QAAQ,EAAE5U,OAAO,KAAK;MAC9C,IAAI4U,QAAQ,CAAC3b,KAAK,EAAE;QAAA,IAAA+rB,qBAAA;QAClB;QACA,IAAIC,SAAS,GAAGrQ,QAAQ,CAAC3b,KAAK,CAAC8G,OAAO;QACtC,IAAImlB,cAAc,GAAG,IAAI;;QAEzB;QACA,MAAMC,QAAQ,GAAG,IAAIzzB,KAAK,CAACkG,OAAO,CAAC,CAAC;QACpCgd,QAAQ,CAAC3b,KAAK,CAACmsB,gBAAgB,CAACD,QAAQ,CAAC;;QAEzC;QACA,MAAME,gBAAgB,GAAGF,QAAQ,CAAC3tB,UAAU,CAACkI,SAAS,CAACqC,OAAO,CAACrD,QAAQ,CAAC;;QAExE;QACA,MAAM4mB,SAAS,GAAGH,QAAQ,CAACvuB,KAAK,CAAC,CAAC,CAAC2uB,OAAO,CAAC7lB,SAAS,CAACqC,OAAO,CAAC;QAC7D,IAAI/J,IAAI,CAACK,GAAG,CAACitB,SAAS,CAACxuB,CAAC,CAAC,GAAG,CAAC,IAAIkB,IAAI,CAACK,GAAG,CAACitB,SAAS,CAACtuB,CAAC,CAAC,GAAG,CAAC,IAAIsuB,SAAS,CAACpuB,CAAC,GAAG,CAAC,CAAC,IAAIouB,SAAS,CAACpuB,CAAC,GAAG,CAAC,EAAE;UACjGguB,cAAc,GAAG,KAAK;QACxB;QAEA,IAAID,SAAS,EAAE;UACbF,YAAY,EAAE;QAChB;QAEAjwB,OAAO,CAACC,GAAG,CAAC,OAAOiL,OAAO,GAAG,EAAE;UAC7BwlB,EAAE,EAAE,EAAAR,qBAAA,GAAApQ,QAAQ,CAAC/Q,YAAY,cAAAmhB,qBAAA,uBAArBA,qBAAA,CAAuBjhB,IAAI,KAAI,IAAI;UACvC0hB,GAAG,EAAER,SAAS;UACdS,KAAK,EAAER,cAAc;UACrBS,IAAI,EAAER,QAAQ,CAAC5gB,OAAO,CAAC,CAAC;UACxBqhB,IAAI,EAAE,CAACN,SAAS,CAACxuB,CAAC,EAAEwuB,SAAS,CAACtuB,CAAC,EAAEsuB,SAAS,CAACpuB,CAAC,CAAC;UAC7C2uB,MAAM,EAAER;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEFvwB,OAAO,CAACC,GAAG,CAAC,MAAMgwB,YAAY,IAAI5vB,gBAAgB,CAACkY,IAAI,WAAW,CAAC;;IAEnE;IACA,OAAOyX,eAAe,CAAC7qB,MAAM,GAAG,CAAC;EACnC,CAAC,CAAC,OAAO7D,KAAK,EAAE;IACdtB,OAAO,CAACsB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,OAAO,KAAK;EACd;AACF,CAAC;;AAID;AACA,MAAMyU,wBAAwB,GAAGA,CAACR,YAAY,EAAEG,SAAS,KAAK;EAAA,IAAAsb,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC5D,IAAI,CAAC3b,YAAY,IAAI,CAACA,YAAY,CAACpR,KAAK,IAAI,CAACuR,SAAS,EAAE;IACtD;EACF;;EAEA;EACA,MAAMyb,cAAc,GAAG,EAAE;EACzB5b,YAAY,CAACpR,KAAK,CAACE,QAAQ,CAACkP,KAAK,IAAI;IACnC,IAAIA,KAAK,CAAC6M,QAAQ,IAAI7M,KAAK,CAAC6M,QAAQ,CAACgR,OAAO,EAAE;MAC5CD,cAAc,CAACxb,IAAI,CAACpC,KAAK,CAAC;IAC5B;EACF,CAAC,CAAC;EAEF4d,cAAc,CAACvsB,OAAO,CAAC4nB,KAAK,IAAI;IAC9BjX,YAAY,CAACpR,KAAK,CAACiC,MAAM,CAAComB,KAAK,CAAC;EAClC,CAAC,CAAC;;EAEF;EACA,IAAIQ,UAAU;EACd,QAAOtX,SAAS,CAACH,YAAY;IAC3B,KAAK,GAAG;MACNyX,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;MACNA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;IACR;MACEA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;EACJ;;EAEA;EACA,MAAMhB,aAAa,GAAG,IAAIpvB,KAAK,CAACojB,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAMiM,aAAa,GAAG,IAAIrvB,KAAK,CAAC2iB,iBAAiB,CAAC;IAChD1T,KAAK,EAAEmhB,UAAU;IACjBvZ,QAAQ,EAAEuZ,UAAU;IACpBqE,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAMnF,SAAS,GAAG,IAAItvB,KAAK,CAAC4iB,IAAI,CAACwM,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAACtiB,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAClC6pB,SAAS,CAAC9L,QAAQ,GAAG;IACnBgR,OAAO,EAAE,IAAI;IACb5f,IAAI,EAAE,cAAc;IACpBtG,OAAO,GAAA8lB,qBAAA,GAAEzb,YAAY,CAACxG,YAAY,cAAAiiB,qBAAA,uBAAzBA,qBAAA,CAA2B9lB,OAAO;IAC3C8J,OAAO,EAAEU,SAAS,CAACV,OAAO;IAC1BE,SAAS,EAAEQ,SAAS,CAACR,SAAS;IAC9BM,UAAU,EAAEE,SAAS,CAACF;EACxB,CAAC;;EAED;EACA,MAAMgX,KAAK,GAAG,IAAI5vB,KAAK,CAAC00B,UAAU,CAACtE,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;EACrDR,KAAK,CAAC5iB,QAAQ,CAACvH,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAC5BmqB,KAAK,CAACpM,QAAQ,GAAG;IAAEgR,OAAO,EAAE;EAAK,CAAC;;EAElC;EACA7b,YAAY,CAACpR,KAAK,CAACC,GAAG,CAAC8nB,SAAS,CAAC;EACjC3W,YAAY,CAACpR,KAAK,CAACC,GAAG,CAACooB,KAAK,CAAC;EAE7BxsB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAAgxB,sBAAA,GAAA1b,YAAY,CAACxG,YAAY,cAAAkiB,sBAAA,uBAAzBA,sBAAA,CAA2BhiB,IAAI,OAAAiiB,sBAAA,GAAI3b,YAAY,CAACxG,YAAY,cAAAmiB,sBAAA,uBAAzBA,sBAAA,CAA2BhmB,OAAO,cAAawK,SAAS,CAACH,YAAY,WAAWG,SAAS,CAACF,UAAU,GAAG,CAAC;AACjK,CAAC;AAED,eAAepO,WAAW;AAAC,IAAAwe,EAAA;AAAA2L,YAAA,CAAA3L,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}