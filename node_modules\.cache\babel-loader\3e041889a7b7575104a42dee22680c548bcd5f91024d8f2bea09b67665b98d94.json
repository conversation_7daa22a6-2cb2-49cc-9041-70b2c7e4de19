{"ast": null, "code": "\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport scrollIntoView from 'scroll-into-view-if-needed';\nimport getScroll from '../_util/getScroll';\nimport scrollTo from '../_util/scrollTo';\nimport { devUseWarning } from '../_util/warning';\nimport Affix from '../affix';\nimport { ConfigContext, useComponentConfig } from '../config-provider/context';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport AnchorLink from './AnchorLink';\nimport AnchorContext from './context';\nimport useStyle from './style';\nfunction getDefaultContainer() {\n  return window;\n}\nfunction getOffsetTop(element, container) {\n  if (!element.getClientRects().length) {\n    return 0;\n  }\n  const rect = element.getBoundingClientRect();\n  if (rect.width || rect.height) {\n    if (container === window) {\n      return rect.top - element.ownerDocument.documentElement.clientTop;\n    }\n    return rect.top - container.getBoundingClientRect().top;\n  }\n  return rect.top;\n}\nconst sharpMatcherRegex = /#([\\S ]+)$/;\nconst Anchor = props => {\n  var _a;\n  const {\n    rootClassName,\n    prefixCls: customPrefixCls,\n    className,\n    style,\n    offsetTop,\n    affix = true,\n    showInkInFixed = false,\n    children,\n    items,\n    direction: anchorDirection = 'vertical',\n    bounds,\n    targetOffset,\n    onClick,\n    onChange,\n    getContainer,\n    getCurrentAnchor,\n    replace\n  } = props;\n  // =================== Warning =====================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Anchor');\n    warning.deprecated(!children, 'Anchor children', 'items');\n    process.env.NODE_ENV !== \"production\" ? warning(!(anchorDirection === 'horizontal' && (items === null || items === void 0 ? void 0 : items.some(n => 'children' in n))), 'usage', '`Anchor items#children` is not supported when `Anchor` direction is horizontal.') : void 0;\n  }\n  const [links, setLinks] = React.useState([]);\n  const [activeLink, setActiveLink] = React.useState(null);\n  const activeLinkRef = React.useRef(activeLink);\n  const wrapperRef = React.useRef(null);\n  const spanLinkNode = React.useRef(null);\n  const animating = React.useRef(false);\n  const {\n    direction,\n    getPrefixCls,\n    className: anchorClassName,\n    style: anchorStyle\n  } = useComponentConfig('anchor');\n  const {\n    getTargetContainer\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('anchor', customPrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const getCurrentContainer = (_a = getContainer !== null && getContainer !== void 0 ? getContainer : getTargetContainer) !== null && _a !== void 0 ? _a : getDefaultContainer;\n  const dependencyListItem = JSON.stringify(links);\n  const registerLink = useEvent(link => {\n    if (!links.includes(link)) {\n      setLinks(prev => [].concat(_toConsumableArray(prev), [link]));\n    }\n  });\n  const unregisterLink = useEvent(link => {\n    if (links.includes(link)) {\n      setLinks(prev => prev.filter(i => i !== link));\n    }\n  });\n  const updateInk = () => {\n    var _a;\n    const linkNode = (_a = wrapperRef.current) === null || _a === void 0 ? void 0 : _a.querySelector(`.${prefixCls}-link-title-active`);\n    if (linkNode && spanLinkNode.current) {\n      const {\n        style: inkStyle\n      } = spanLinkNode.current;\n      const horizontalAnchor = anchorDirection === 'horizontal';\n      inkStyle.top = horizontalAnchor ? '' : `${linkNode.offsetTop + linkNode.clientHeight / 2}px`;\n      inkStyle.height = horizontalAnchor ? '' : `${linkNode.clientHeight}px`;\n      inkStyle.left = horizontalAnchor ? `${linkNode.offsetLeft}px` : '';\n      inkStyle.width = horizontalAnchor ? `${linkNode.clientWidth}px` : '';\n      if (horizontalAnchor) {\n        scrollIntoView(linkNode, {\n          scrollMode: 'if-needed',\n          block: 'nearest'\n        });\n      }\n    }\n  };\n  const getInternalCurrentAnchor = function (_links) {\n    let _offsetTop = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    let _bounds = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 5;\n    const linkSections = [];\n    const container = getCurrentContainer();\n    _links.forEach(link => {\n      const sharpLinkMatch = sharpMatcherRegex.exec(link === null || link === void 0 ? void 0 : link.toString());\n      if (!sharpLinkMatch) {\n        return;\n      }\n      const target = document.getElementById(sharpLinkMatch[1]);\n      if (target) {\n        const top = getOffsetTop(target, container);\n        if (top <= _offsetTop + _bounds) {\n          linkSections.push({\n            link,\n            top\n          });\n        }\n      }\n    });\n    if (linkSections.length) {\n      const maxSection = linkSections.reduce((prev, curr) => curr.top > prev.top ? curr : prev);\n      return maxSection.link;\n    }\n    return '';\n  };\n  const setCurrentActiveLink = useEvent(link => {\n    // FIXME: Seems a bug since this compare is not equals\n    // `activeLinkRef` is parsed value which will always trigger `onChange` event.\n    if (activeLinkRef.current === link) {\n      return;\n    }\n    // https://github.com/ant-design/ant-design/issues/30584\n    const newLink = typeof getCurrentAnchor === 'function' ? getCurrentAnchor(link) : link;\n    setActiveLink(newLink);\n    activeLinkRef.current = newLink;\n    // onChange should respect the original link (which may caused by\n    // window scroll or user click), not the new link\n    onChange === null || onChange === void 0 ? void 0 : onChange(link);\n  });\n  const handleScroll = React.useCallback(() => {\n    if (animating.current) {\n      return;\n    }\n    const currentActiveLink = getInternalCurrentAnchor(links, targetOffset !== undefined ? targetOffset : offsetTop || 0, bounds);\n    setCurrentActiveLink(currentActiveLink);\n  }, [dependencyListItem, targetOffset, offsetTop]);\n  const handleScrollTo = React.useCallback(link => {\n    setCurrentActiveLink(link);\n    const sharpLinkMatch = sharpMatcherRegex.exec(link);\n    if (!sharpLinkMatch) {\n      return;\n    }\n    const targetElement = document.getElementById(sharpLinkMatch[1]);\n    if (!targetElement) {\n      return;\n    }\n    const container = getCurrentContainer();\n    const scrollTop = getScroll(container);\n    const eleOffsetTop = getOffsetTop(targetElement, container);\n    let y = scrollTop + eleOffsetTop;\n    y -= targetOffset !== undefined ? targetOffset : offsetTop || 0;\n    animating.current = true;\n    scrollTo(y, {\n      getContainer: getCurrentContainer,\n      callback() {\n        animating.current = false;\n      }\n    });\n  }, [targetOffset, offsetTop]);\n  const wrapperClass = classNames(hashId, cssVarCls, rootCls, rootClassName, `${prefixCls}-wrapper`, {\n    [`${prefixCls}-wrapper-horizontal`]: anchorDirection === 'horizontal',\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, anchorClassName);\n  const anchorClass = classNames(prefixCls, {\n    [`${prefixCls}-fixed`]: !affix && !showInkInFixed\n  });\n  const inkClass = classNames(`${prefixCls}-ink`, {\n    [`${prefixCls}-ink-visible`]: activeLink\n  });\n  const wrapperStyle = Object.assign(Object.assign({\n    maxHeight: offsetTop ? `calc(100vh - ${offsetTop}px)` : '100vh'\n  }, anchorStyle), style);\n  const createNestedLink = options => Array.isArray(options) ? options.map(item => (/*#__PURE__*/React.createElement(AnchorLink, Object.assign({\n    replace: replace\n  }, item, {\n    key: item.key\n  }), anchorDirection === 'vertical' && createNestedLink(item.children)))) : null;\n  const anchorContent = /*#__PURE__*/React.createElement(\"div\", {\n    ref: wrapperRef,\n    className: wrapperClass,\n    style: wrapperStyle\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: anchorClass\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: inkClass,\n    ref: spanLinkNode\n  }), 'items' in props ? createNestedLink(items) : children));\n  React.useEffect(() => {\n    const scrollContainer = getCurrentContainer();\n    handleScroll();\n    scrollContainer === null || scrollContainer === void 0 ? void 0 : scrollContainer.addEventListener('scroll', handleScroll);\n    return () => {\n      scrollContainer === null || scrollContainer === void 0 ? void 0 : scrollContainer.removeEventListener('scroll', handleScroll);\n    };\n  }, [dependencyListItem]);\n  React.useEffect(() => {\n    if (typeof getCurrentAnchor === 'function') {\n      setCurrentActiveLink(getCurrentAnchor(activeLinkRef.current || ''));\n    }\n  }, [getCurrentAnchor]);\n  React.useEffect(() => {\n    updateInk();\n  }, [anchorDirection, getCurrentAnchor, dependencyListItem, activeLink]);\n  const memoizedContextValue = React.useMemo(() => ({\n    registerLink,\n    unregisterLink,\n    scrollTo: handleScrollTo,\n    activeLink,\n    onClick,\n    direction: anchorDirection\n  }), [activeLink, onClick, handleScrollTo, anchorDirection]);\n  const affixProps = affix && typeof affix === 'object' ? affix : undefined;\n  return wrapCSSVar(/*#__PURE__*/React.createElement(AnchorContext.Provider, {\n    value: memoizedContextValue\n  }, affix ? (/*#__PURE__*/React.createElement(Affix, Object.assign({\n    offsetTop: offsetTop,\n    target: getCurrentContainer\n  }, affixProps), anchorContent)) : anchorContent));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Anchor.displayName = 'Anchor';\n}\nexport default Anchor;", "map": {"version": 3, "names": ["_toConsumableArray", "React", "classNames", "useEvent", "scrollIntoView", "getScroll", "scrollTo", "devUseW<PERSON>ning", "Affix", "ConfigContext", "useComponentConfig", "useCSSVarCls", "AnchorLink", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useStyle", "getDefaultContainer", "window", "getOffsetTop", "element", "container", "getClientRects", "length", "rect", "getBoundingClientRect", "width", "height", "top", "ownerDocument", "documentElement", "clientTop", "sharpMatcherRegex", "<PERSON><PERSON>", "props", "_a", "rootClassName", "prefixCls", "customPrefixCls", "className", "style", "offsetTop", "affix", "showInkInFixed", "children", "items", "direction", "anchorDirection", "bounds", "targetOffset", "onClick", "onChange", "getContainer", "getCurrentAnchor", "replace", "process", "env", "NODE_ENV", "warning", "deprecated", "some", "n", "links", "setLinks", "useState", "activeLink", "setActiveLink", "activeLinkRef", "useRef", "wrapperRef", "spanLinkNode", "animating", "getPrefixCls", "anchorClassName", "anchorStyle", "getTargetContainer", "useContext", "rootCls", "wrapCSSVar", "hashId", "cssVarCls", "getCurrentContainer", "dependencyListItem", "JSON", "stringify", "registerLink", "link", "includes", "prev", "concat", "unregisterLink", "filter", "i", "updateInk", "linkNode", "current", "querySelector", "inkStyle", "horizontalAnchor", "clientHeight", "left", "offsetLeft", "clientWidth", "scrollMode", "block", "getInternalCurrentAnchor", "_links", "_offsetTop", "arguments", "undefined", "_bounds", "linkSections", "for<PERSON>ach", "sharpLinkMatch", "exec", "toString", "target", "document", "getElementById", "push", "maxSection", "reduce", "curr", "setCurrentActiveLink", "newLink", "handleScroll", "useCallback", "currentActiveLink", "handleScrollTo", "targetElement", "scrollTop", "eleOffsetTop", "y", "callback", "wrapperClass", "anchorClass", "inkClass", "wrapperStyle", "Object", "assign", "maxHeight", "createNestedLink", "options", "Array", "isArray", "map", "item", "createElement", "key", "anchorContent", "ref", "useEffect", "scrollContainer", "addEventListener", "removeEventListener", "memoizedContextValue", "useMemo", "affixProps", "Provider", "value", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/anchor/Anchor.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport scrollIntoView from 'scroll-into-view-if-needed';\nimport getScroll from '../_util/getScroll';\nimport scrollTo from '../_util/scrollTo';\nimport { devUseWarning } from '../_util/warning';\nimport Affix from '../affix';\nimport { ConfigContext, useComponentConfig } from '../config-provider/context';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport AnchorLink from './AnchorLink';\nimport AnchorContext from './context';\nimport useStyle from './style';\nfunction getDefaultContainer() {\n  return window;\n}\nfunction getOffsetTop(element, container) {\n  if (!element.getClientRects().length) {\n    return 0;\n  }\n  const rect = element.getBoundingClientRect();\n  if (rect.width || rect.height) {\n    if (container === window) {\n      return rect.top - element.ownerDocument.documentElement.clientTop;\n    }\n    return rect.top - container.getBoundingClientRect().top;\n  }\n  return rect.top;\n}\nconst sharpMatcherRegex = /#([\\S ]+)$/;\nconst Anchor = props => {\n  var _a;\n  const {\n    rootClassName,\n    prefixCls: customPrefixCls,\n    className,\n    style,\n    offsetTop,\n    affix = true,\n    showInkInFixed = false,\n    children,\n    items,\n    direction: anchorDirection = 'vertical',\n    bounds,\n    targetOffset,\n    onClick,\n    onChange,\n    getContainer,\n    getCurrentAnchor,\n    replace\n  } = props;\n  // =================== Warning =====================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Anchor');\n    warning.deprecated(!children, 'Anchor children', 'items');\n    process.env.NODE_ENV !== \"production\" ? warning(!(anchorDirection === 'horizontal' && (items === null || items === void 0 ? void 0 : items.some(n => 'children' in n))), 'usage', '`Anchor items#children` is not supported when `Anchor` direction is horizontal.') : void 0;\n  }\n  const [links, setLinks] = React.useState([]);\n  const [activeLink, setActiveLink] = React.useState(null);\n  const activeLinkRef = React.useRef(activeLink);\n  const wrapperRef = React.useRef(null);\n  const spanLinkNode = React.useRef(null);\n  const animating = React.useRef(false);\n  const {\n    direction,\n    getPrefixCls,\n    className: anchorClassName,\n    style: anchorStyle\n  } = useComponentConfig('anchor');\n  const {\n    getTargetContainer\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('anchor', customPrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const getCurrentContainer = (_a = getContainer !== null && getContainer !== void 0 ? getContainer : getTargetContainer) !== null && _a !== void 0 ? _a : getDefaultContainer;\n  const dependencyListItem = JSON.stringify(links);\n  const registerLink = useEvent(link => {\n    if (!links.includes(link)) {\n      setLinks(prev => [].concat(_toConsumableArray(prev), [link]));\n    }\n  });\n  const unregisterLink = useEvent(link => {\n    if (links.includes(link)) {\n      setLinks(prev => prev.filter(i => i !== link));\n    }\n  });\n  const updateInk = () => {\n    var _a;\n    const linkNode = (_a = wrapperRef.current) === null || _a === void 0 ? void 0 : _a.querySelector(`.${prefixCls}-link-title-active`);\n    if (linkNode && spanLinkNode.current) {\n      const {\n        style: inkStyle\n      } = spanLinkNode.current;\n      const horizontalAnchor = anchorDirection === 'horizontal';\n      inkStyle.top = horizontalAnchor ? '' : `${linkNode.offsetTop + linkNode.clientHeight / 2}px`;\n      inkStyle.height = horizontalAnchor ? '' : `${linkNode.clientHeight}px`;\n      inkStyle.left = horizontalAnchor ? `${linkNode.offsetLeft}px` : '';\n      inkStyle.width = horizontalAnchor ? `${linkNode.clientWidth}px` : '';\n      if (horizontalAnchor) {\n        scrollIntoView(linkNode, {\n          scrollMode: 'if-needed',\n          block: 'nearest'\n        });\n      }\n    }\n  };\n  const getInternalCurrentAnchor = function (_links) {\n    let _offsetTop = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    let _bounds = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 5;\n    const linkSections = [];\n    const container = getCurrentContainer();\n    _links.forEach(link => {\n      const sharpLinkMatch = sharpMatcherRegex.exec(link === null || link === void 0 ? void 0 : link.toString());\n      if (!sharpLinkMatch) {\n        return;\n      }\n      const target = document.getElementById(sharpLinkMatch[1]);\n      if (target) {\n        const top = getOffsetTop(target, container);\n        if (top <= _offsetTop + _bounds) {\n          linkSections.push({\n            link,\n            top\n          });\n        }\n      }\n    });\n    if (linkSections.length) {\n      const maxSection = linkSections.reduce((prev, curr) => curr.top > prev.top ? curr : prev);\n      return maxSection.link;\n    }\n    return '';\n  };\n  const setCurrentActiveLink = useEvent(link => {\n    // FIXME: Seems a bug since this compare is not equals\n    // `activeLinkRef` is parsed value which will always trigger `onChange` event.\n    if (activeLinkRef.current === link) {\n      return;\n    }\n    // https://github.com/ant-design/ant-design/issues/30584\n    const newLink = typeof getCurrentAnchor === 'function' ? getCurrentAnchor(link) : link;\n    setActiveLink(newLink);\n    activeLinkRef.current = newLink;\n    // onChange should respect the original link (which may caused by\n    // window scroll or user click), not the new link\n    onChange === null || onChange === void 0 ? void 0 : onChange(link);\n  });\n  const handleScroll = React.useCallback(() => {\n    if (animating.current) {\n      return;\n    }\n    const currentActiveLink = getInternalCurrentAnchor(links, targetOffset !== undefined ? targetOffset : offsetTop || 0, bounds);\n    setCurrentActiveLink(currentActiveLink);\n  }, [dependencyListItem, targetOffset, offsetTop]);\n  const handleScrollTo = React.useCallback(link => {\n    setCurrentActiveLink(link);\n    const sharpLinkMatch = sharpMatcherRegex.exec(link);\n    if (!sharpLinkMatch) {\n      return;\n    }\n    const targetElement = document.getElementById(sharpLinkMatch[1]);\n    if (!targetElement) {\n      return;\n    }\n    const container = getCurrentContainer();\n    const scrollTop = getScroll(container);\n    const eleOffsetTop = getOffsetTop(targetElement, container);\n    let y = scrollTop + eleOffsetTop;\n    y -= targetOffset !== undefined ? targetOffset : offsetTop || 0;\n    animating.current = true;\n    scrollTo(y, {\n      getContainer: getCurrentContainer,\n      callback() {\n        animating.current = false;\n      }\n    });\n  }, [targetOffset, offsetTop]);\n  const wrapperClass = classNames(hashId, cssVarCls, rootCls, rootClassName, `${prefixCls}-wrapper`, {\n    [`${prefixCls}-wrapper-horizontal`]: anchorDirection === 'horizontal',\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, anchorClassName);\n  const anchorClass = classNames(prefixCls, {\n    [`${prefixCls}-fixed`]: !affix && !showInkInFixed\n  });\n  const inkClass = classNames(`${prefixCls}-ink`, {\n    [`${prefixCls}-ink-visible`]: activeLink\n  });\n  const wrapperStyle = Object.assign(Object.assign({\n    maxHeight: offsetTop ? `calc(100vh - ${offsetTop}px)` : '100vh'\n  }, anchorStyle), style);\n  const createNestedLink = options => Array.isArray(options) ? options.map(item => (/*#__PURE__*/React.createElement(AnchorLink, Object.assign({\n    replace: replace\n  }, item, {\n    key: item.key\n  }), anchorDirection === 'vertical' && createNestedLink(item.children)))) : null;\n  const anchorContent = /*#__PURE__*/React.createElement(\"div\", {\n    ref: wrapperRef,\n    className: wrapperClass,\n    style: wrapperStyle\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: anchorClass\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: inkClass,\n    ref: spanLinkNode\n  }), 'items' in props ? createNestedLink(items) : children));\n  React.useEffect(() => {\n    const scrollContainer = getCurrentContainer();\n    handleScroll();\n    scrollContainer === null || scrollContainer === void 0 ? void 0 : scrollContainer.addEventListener('scroll', handleScroll);\n    return () => {\n      scrollContainer === null || scrollContainer === void 0 ? void 0 : scrollContainer.removeEventListener('scroll', handleScroll);\n    };\n  }, [dependencyListItem]);\n  React.useEffect(() => {\n    if (typeof getCurrentAnchor === 'function') {\n      setCurrentActiveLink(getCurrentAnchor(activeLinkRef.current || ''));\n    }\n  }, [getCurrentAnchor]);\n  React.useEffect(() => {\n    updateInk();\n  }, [anchorDirection, getCurrentAnchor, dependencyListItem, activeLink]);\n  const memoizedContextValue = React.useMemo(() => ({\n    registerLink,\n    unregisterLink,\n    scrollTo: handleScrollTo,\n    activeLink,\n    onClick,\n    direction: anchorDirection\n  }), [activeLink, onClick, handleScrollTo, anchorDirection]);\n  const affixProps = affix && typeof affix === 'object' ? affix : undefined;\n  return wrapCSSVar(/*#__PURE__*/React.createElement(AnchorContext.Provider, {\n    value: memoizedContextValue\n  }, affix ? (/*#__PURE__*/React.createElement(Affix, Object.assign({\n    offsetTop: offsetTop,\n    target: getCurrentContainer\n  }, affixProps), anchorContent)) : anchorContent));\n};\nif (process.env.NODE_ENV !== 'production') {\n  Anchor.displayName = 'Anchor';\n}\nexport default Anchor;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,SAASC,aAAa,QAAQ,kBAAkB;AAChD,OAAOC,KAAK,MAAM,UAAU;AAC5B,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,4BAA4B;AAC9E,OAAOC,YAAY,MAAM,uCAAuC;AAChE,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,aAAa,MAAM,WAAW;AACrC,OAAOC,QAAQ,MAAM,SAAS;AAC9B,SAASC,mBAAmBA,CAAA,EAAG;EAC7B,OAAOC,MAAM;AACf;AACA,SAASC,YAAYA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACxC,IAAI,CAACD,OAAO,CAACE,cAAc,CAAC,CAAC,CAACC,MAAM,EAAE;IACpC,OAAO,CAAC;EACV;EACA,MAAMC,IAAI,GAAGJ,OAAO,CAACK,qBAAqB,CAAC,CAAC;EAC5C,IAAID,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACG,MAAM,EAAE;IAC7B,IAAIN,SAAS,KAAKH,MAAM,EAAE;MACxB,OAAOM,IAAI,CAACI,GAAG,GAAGR,OAAO,CAACS,aAAa,CAACC,eAAe,CAACC,SAAS;IACnE;IACA,OAAOP,IAAI,CAACI,GAAG,GAAGP,SAAS,CAACI,qBAAqB,CAAC,CAAC,CAACG,GAAG;EACzD;EACA,OAAOJ,IAAI,CAACI,GAAG;AACjB;AACA,MAAMI,iBAAiB,GAAG,YAAY;AACtC,MAAMC,MAAM,GAAGC,KAAK,IAAI;EACtB,IAAIC,EAAE;EACN,MAAM;IACJC,aAAa;IACbC,SAAS,EAAEC,eAAe;IAC1BC,SAAS;IACTC,KAAK;IACLC,SAAS;IACTC,KAAK,GAAG,IAAI;IACZC,cAAc,GAAG,KAAK;IACtBC,QAAQ;IACRC,KAAK;IACLC,SAAS,EAAEC,eAAe,GAAG,UAAU;IACvCC,MAAM;IACNC,YAAY;IACZC,OAAO;IACPC,QAAQ;IACRC,YAAY;IACZC,gBAAgB;IAChBC;EACF,CAAC,GAAGpB,KAAK;EACT;EACA,IAAIqB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAGjD,aAAa,CAAC,QAAQ,CAAC;IACvCiD,OAAO,CAACC,UAAU,CAAC,CAACf,QAAQ,EAAE,iBAAiB,EAAE,OAAO,CAAC;IACzDW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGC,OAAO,CAAC,EAAEX,eAAe,KAAK,YAAY,KAAKF,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACe,IAAI,CAACC,CAAC,IAAI,UAAU,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,iFAAiF,CAAC,GAAG,KAAK,CAAC;EAC/Q;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,KAAK,CAAC6D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/D,KAAK,CAAC6D,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAMG,aAAa,GAAGhE,KAAK,CAACiE,MAAM,CAACH,UAAU,CAAC;EAC9C,MAAMI,UAAU,GAAGlE,KAAK,CAACiE,MAAM,CAAC,IAAI,CAAC;EACrC,MAAME,YAAY,GAAGnE,KAAK,CAACiE,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMG,SAAS,GAAGpE,KAAK,CAACiE,MAAM,CAAC,KAAK,CAAC;EACrC,MAAM;IACJtB,SAAS;IACT0B,YAAY;IACZjC,SAAS,EAAEkC,eAAe;IAC1BjC,KAAK,EAAEkC;EACT,CAAC,GAAG9D,kBAAkB,CAAC,QAAQ,CAAC;EAChC,MAAM;IACJ+D;EACF,CAAC,GAAGxE,KAAK,CAACyE,UAAU,CAACjE,aAAa,CAAC;EACnC,MAAM0B,SAAS,GAAGmC,YAAY,CAAC,QAAQ,EAAElC,eAAe,CAAC;EACzD,MAAMuC,OAAO,GAAGhE,YAAY,CAACwB,SAAS,CAAC;EACvC,MAAM,CAACyC,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGhE,QAAQ,CAACqB,SAAS,EAAEwC,OAAO,CAAC;EACpE,MAAMI,mBAAmB,GAAG,CAAC9C,EAAE,GAAGiB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGuB,kBAAkB,MAAM,IAAI,IAAIxC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGlB,mBAAmB;EAC5K,MAAMiE,kBAAkB,GAAGC,IAAI,CAACC,SAAS,CAACtB,KAAK,CAAC;EAChD,MAAMuB,YAAY,GAAGhF,QAAQ,CAACiF,IAAI,IAAI;IACpC,IAAI,CAACxB,KAAK,CAACyB,QAAQ,CAACD,IAAI,CAAC,EAAE;MACzBvB,QAAQ,CAACyB,IAAI,IAAI,EAAE,CAACC,MAAM,CAACvF,kBAAkB,CAACsF,IAAI,CAAC,EAAE,CAACF,IAAI,CAAC,CAAC,CAAC;IAC/D;EACF,CAAC,CAAC;EACF,MAAMI,cAAc,GAAGrF,QAAQ,CAACiF,IAAI,IAAI;IACtC,IAAIxB,KAAK,CAACyB,QAAQ,CAACD,IAAI,CAAC,EAAE;MACxBvB,QAAQ,CAACyB,IAAI,IAAIA,IAAI,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKN,IAAI,CAAC,CAAC;IAChD;EACF,CAAC,CAAC;EACF,MAAMO,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAI1D,EAAE;IACN,MAAM2D,QAAQ,GAAG,CAAC3D,EAAE,GAAGkC,UAAU,CAAC0B,OAAO,MAAM,IAAI,IAAI5D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6D,aAAa,CAAC,IAAI3D,SAAS,oBAAoB,CAAC;IACnI,IAAIyD,QAAQ,IAAIxB,YAAY,CAACyB,OAAO,EAAE;MACpC,MAAM;QACJvD,KAAK,EAAEyD;MACT,CAAC,GAAG3B,YAAY,CAACyB,OAAO;MACxB,MAAMG,gBAAgB,GAAGnD,eAAe,KAAK,YAAY;MACzDkD,QAAQ,CAACrE,GAAG,GAAGsE,gBAAgB,GAAG,EAAE,GAAG,GAAGJ,QAAQ,CAACrD,SAAS,GAAGqD,QAAQ,CAACK,YAAY,GAAG,CAAC,IAAI;MAC5FF,QAAQ,CAACtE,MAAM,GAAGuE,gBAAgB,GAAG,EAAE,GAAG,GAAGJ,QAAQ,CAACK,YAAY,IAAI;MACtEF,QAAQ,CAACG,IAAI,GAAGF,gBAAgB,GAAG,GAAGJ,QAAQ,CAACO,UAAU,IAAI,GAAG,EAAE;MAClEJ,QAAQ,CAACvE,KAAK,GAAGwE,gBAAgB,GAAG,GAAGJ,QAAQ,CAACQ,WAAW,IAAI,GAAG,EAAE;MACpE,IAAIJ,gBAAgB,EAAE;QACpB5F,cAAc,CAACwF,QAAQ,EAAE;UACvBS,UAAU,EAAE,WAAW;UACvBC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EACD,MAAMC,wBAAwB,GAAG,SAAAA,CAAUC,MAAM,EAAE;IACjD,IAAIC,UAAU,GAAGC,SAAS,CAACrF,MAAM,GAAG,CAAC,IAAIqF,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IACtF,IAAIE,OAAO,GAAGF,SAAS,CAACrF,MAAM,GAAG,CAAC,IAAIqF,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;IACnF,MAAMG,YAAY,GAAG,EAAE;IACvB,MAAM1F,SAAS,GAAG4D,mBAAmB,CAAC,CAAC;IACvCyB,MAAM,CAACM,OAAO,CAAC1B,IAAI,IAAI;MACrB,MAAM2B,cAAc,GAAGjF,iBAAiB,CAACkF,IAAI,CAAC5B,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC6B,QAAQ,CAAC,CAAC,CAAC;MAC1G,IAAI,CAACF,cAAc,EAAE;QACnB;MACF;MACA,MAAMG,MAAM,GAAGC,QAAQ,CAACC,cAAc,CAACL,cAAc,CAAC,CAAC,CAAC,CAAC;MACzD,IAAIG,MAAM,EAAE;QACV,MAAMxF,GAAG,GAAGT,YAAY,CAACiG,MAAM,EAAE/F,SAAS,CAAC;QAC3C,IAAIO,GAAG,IAAI+E,UAAU,GAAGG,OAAO,EAAE;UAC/BC,YAAY,CAACQ,IAAI,CAAC;YAChBjC,IAAI;YACJ1D;UACF,CAAC,CAAC;QACJ;MACF;IACF,CAAC,CAAC;IACF,IAAImF,YAAY,CAACxF,MAAM,EAAE;MACvB,MAAMiG,UAAU,GAAGT,YAAY,CAACU,MAAM,CAAC,CAACjC,IAAI,EAAEkC,IAAI,KAAKA,IAAI,CAAC9F,GAAG,GAAG4D,IAAI,CAAC5D,GAAG,GAAG8F,IAAI,GAAGlC,IAAI,CAAC;MACzF,OAAOgC,UAAU,CAAClC,IAAI;IACxB;IACA,OAAO,EAAE;EACX,CAAC;EACD,MAAMqC,oBAAoB,GAAGtH,QAAQ,CAACiF,IAAI,IAAI;IAC5C;IACA;IACA,IAAInB,aAAa,CAAC4B,OAAO,KAAKT,IAAI,EAAE;MAClC;IACF;IACA;IACA,MAAMsC,OAAO,GAAG,OAAOvE,gBAAgB,KAAK,UAAU,GAAGA,gBAAgB,CAACiC,IAAI,CAAC,GAAGA,IAAI;IACtFpB,aAAa,CAAC0D,OAAO,CAAC;IACtBzD,aAAa,CAAC4B,OAAO,GAAG6B,OAAO;IAC/B;IACA;IACAzE,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACmC,IAAI,CAAC;EACpE,CAAC,CAAC;EACF,MAAMuC,YAAY,GAAG1H,KAAK,CAAC2H,WAAW,CAAC,MAAM;IAC3C,IAAIvD,SAAS,CAACwB,OAAO,EAAE;MACrB;IACF;IACA,MAAMgC,iBAAiB,GAAGtB,wBAAwB,CAAC3C,KAAK,EAAEb,YAAY,KAAK4D,SAAS,GAAG5D,YAAY,GAAGR,SAAS,IAAI,CAAC,EAAEO,MAAM,CAAC;IAC7H2E,oBAAoB,CAACI,iBAAiB,CAAC;EACzC,CAAC,EAAE,CAAC7C,kBAAkB,EAAEjC,YAAY,EAAER,SAAS,CAAC,CAAC;EACjD,MAAMuF,cAAc,GAAG7H,KAAK,CAAC2H,WAAW,CAACxC,IAAI,IAAI;IAC/CqC,oBAAoB,CAACrC,IAAI,CAAC;IAC1B,MAAM2B,cAAc,GAAGjF,iBAAiB,CAACkF,IAAI,CAAC5B,IAAI,CAAC;IACnD,IAAI,CAAC2B,cAAc,EAAE;MACnB;IACF;IACA,MAAMgB,aAAa,GAAGZ,QAAQ,CAACC,cAAc,CAACL,cAAc,CAAC,CAAC,CAAC,CAAC;IAChE,IAAI,CAACgB,aAAa,EAAE;MAClB;IACF;IACA,MAAM5G,SAAS,GAAG4D,mBAAmB,CAAC,CAAC;IACvC,MAAMiD,SAAS,GAAG3H,SAAS,CAACc,SAAS,CAAC;IACtC,MAAM8G,YAAY,GAAGhH,YAAY,CAAC8G,aAAa,EAAE5G,SAAS,CAAC;IAC3D,IAAI+G,CAAC,GAAGF,SAAS,GAAGC,YAAY;IAChCC,CAAC,IAAInF,YAAY,KAAK4D,SAAS,GAAG5D,YAAY,GAAGR,SAAS,IAAI,CAAC;IAC/D8B,SAAS,CAACwB,OAAO,GAAG,IAAI;IACxBvF,QAAQ,CAAC4H,CAAC,EAAE;MACVhF,YAAY,EAAE6B,mBAAmB;MACjCoD,QAAQA,CAAA,EAAG;QACT9D,SAAS,CAACwB,OAAO,GAAG,KAAK;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC9C,YAAY,EAAER,SAAS,CAAC,CAAC;EAC7B,MAAM6F,YAAY,GAAGlI,UAAU,CAAC2E,MAAM,EAAEC,SAAS,EAAEH,OAAO,EAAEzC,aAAa,EAAE,GAAGC,SAAS,UAAU,EAAE;IACjG,CAAC,GAAGA,SAAS,qBAAqB,GAAGU,eAAe,KAAK,YAAY;IACrE,CAAC,GAAGV,SAAS,MAAM,GAAGS,SAAS,KAAK;EACtC,CAAC,EAAEP,SAAS,EAAEkC,eAAe,CAAC;EAC9B,MAAM8D,WAAW,GAAGnI,UAAU,CAACiC,SAAS,EAAE;IACxC,CAAC,GAAGA,SAAS,QAAQ,GAAG,CAACK,KAAK,IAAI,CAACC;EACrC,CAAC,CAAC;EACF,MAAM6F,QAAQ,GAAGpI,UAAU,CAAC,GAAGiC,SAAS,MAAM,EAAE;IAC9C,CAAC,GAAGA,SAAS,cAAc,GAAG4B;EAChC,CAAC,CAAC;EACF,MAAMwE,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;IAC/CC,SAAS,EAAEnG,SAAS,GAAG,gBAAgBA,SAAS,KAAK,GAAG;EAC1D,CAAC,EAAEiC,WAAW,CAAC,EAAElC,KAAK,CAAC;EACvB,MAAMqG,gBAAgB,GAAGC,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,GAAGA,OAAO,CAACG,GAAG,CAACC,IAAI,KAAK,aAAa/I,KAAK,CAACgJ,aAAa,CAACrI,UAAU,EAAE4H,MAAM,CAACC,MAAM,CAAC;IAC3IrF,OAAO,EAAEA;EACX,CAAC,EAAE4F,IAAI,EAAE;IACPE,GAAG,EAAEF,IAAI,CAACE;EACZ,CAAC,CAAC,EAAErG,eAAe,KAAK,UAAU,IAAI8F,gBAAgB,CAACK,IAAI,CAACtG,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;EAC/E,MAAMyG,aAAa,GAAG,aAAalJ,KAAK,CAACgJ,aAAa,CAAC,KAAK,EAAE;IAC5DG,GAAG,EAAEjF,UAAU;IACf9B,SAAS,EAAE+F,YAAY;IACvB9F,KAAK,EAAEiG;EACT,CAAC,EAAE,aAAatI,KAAK,CAACgJ,aAAa,CAAC,KAAK,EAAE;IACzC5G,SAAS,EAAEgG;EACb,CAAC,EAAE,aAAapI,KAAK,CAACgJ,aAAa,CAAC,MAAM,EAAE;IAC1C5G,SAAS,EAAEiG,QAAQ;IACnBc,GAAG,EAAEhF;EACP,CAAC,CAAC,EAAE,OAAO,IAAIpC,KAAK,GAAG2G,gBAAgB,CAAChG,KAAK,CAAC,GAAGD,QAAQ,CAAC,CAAC;EAC3DzC,KAAK,CAACoJ,SAAS,CAAC,MAAM;IACpB,MAAMC,eAAe,GAAGvE,mBAAmB,CAAC,CAAC;IAC7C4C,YAAY,CAAC,CAAC;IACd2B,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACC,gBAAgB,CAAC,QAAQ,EAAE5B,YAAY,CAAC;IAC1H,OAAO,MAAM;MACX2B,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACE,mBAAmB,CAAC,QAAQ,EAAE7B,YAAY,CAAC;IAC/H,CAAC;EACH,CAAC,EAAE,CAAC3C,kBAAkB,CAAC,CAAC;EACxB/E,KAAK,CAACoJ,SAAS,CAAC,MAAM;IACpB,IAAI,OAAOlG,gBAAgB,KAAK,UAAU,EAAE;MAC1CsE,oBAAoB,CAACtE,gBAAgB,CAACc,aAAa,CAAC4B,OAAO,IAAI,EAAE,CAAC,CAAC;IACrE;EACF,CAAC,EAAE,CAAC1C,gBAAgB,CAAC,CAAC;EACtBlD,KAAK,CAACoJ,SAAS,CAAC,MAAM;IACpB1D,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC9C,eAAe,EAAEM,gBAAgB,EAAE6B,kBAAkB,EAAEjB,UAAU,CAAC,CAAC;EACvE,MAAM0F,oBAAoB,GAAGxJ,KAAK,CAACyJ,OAAO,CAAC,OAAO;IAChDvE,YAAY;IACZK,cAAc;IACdlF,QAAQ,EAAEwH,cAAc;IACxB/D,UAAU;IACVf,OAAO;IACPJ,SAAS,EAAEC;EACb,CAAC,CAAC,EAAE,CAACkB,UAAU,EAAEf,OAAO,EAAE8E,cAAc,EAAEjF,eAAe,CAAC,CAAC;EAC3D,MAAM8G,UAAU,GAAGnH,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGmE,SAAS;EACzE,OAAO/B,UAAU,CAAC,aAAa3E,KAAK,CAACgJ,aAAa,CAACpI,aAAa,CAAC+I,QAAQ,EAAE;IACzEC,KAAK,EAAEJ;EACT,CAAC,EAAEjH,KAAK,IAAI,aAAavC,KAAK,CAACgJ,aAAa,CAACzI,KAAK,EAAEgI,MAAM,CAACC,MAAM,CAAC;IAChElG,SAAS,EAAEA,SAAS;IACpB2E,MAAM,EAAEnC;EACV,CAAC,EAAE4E,UAAU,CAAC,EAAER,aAAa,CAAC,IAAIA,aAAa,CAAC,CAAC;AACnD,CAAC;AACD,IAAI9F,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCxB,MAAM,CAAC+H,WAAW,GAAG,QAAQ;AAC/B;AACA,eAAe/H,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}