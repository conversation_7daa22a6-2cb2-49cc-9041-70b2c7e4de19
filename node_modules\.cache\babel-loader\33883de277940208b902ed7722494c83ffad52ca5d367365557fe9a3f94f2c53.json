{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\VehicleManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, message, Space } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconst Container = styled.div`\n  // padding: 24px;\n  background: white;\n  position: relative;\n  z-index: 1002;\n`;\n_c = Container;\nconst VehicleManagement = () => {\n  _s();\n  const [vehicles, setVehicles] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingVehicle, setEditingVehicle] = useState(null);\n  const [form] = Form.useForm();\n\n  // 获取车辆列表\n  const fetchVehicles = async () => {\n    try {\n      setLoading(true);\n      // 直接从 vehicles.json 文件读取数据\n      const response = await axios.get(`${API_BASE_URL}/api/vehicles/list`);\n      if (response.data && response.data.vehicles) {\n        setVehicles(response.data.vehicles);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('获取车辆列表失败:', error);\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || '获取车辆列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchVehicles();\n  }, []);\n\n  // 表格列定义\n  const columns = [{\n    title: '车牌号',\n    dataIndex: 'plateNumber',\n    key: 'plateNumber'\n  }, {\n    title: '车辆类型',\n    dataIndex: 'type',\n    key: 'type'\n  }, {\n    title: 'BSM ID',\n    dataIndex: 'bsmId',\n    key: 'bsmId'\n  }, {\n    title: '跟随主车',\n    dataIndex: 'isMainVehicle',\n    key: 'isMainVehicle',\n    render: (isMainVehicle, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: isMainVehicle ? \"primary\" : \"default\",\n      onClick: () => handleMainVehicleChange(record),\n      children: isMainVehicle ? '主车' : '设为主车'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        danger: true,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleDelete(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 处理添加/编辑\n  const handleAddEdit = () => {\n    setEditingVehicle(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = vehicle => {\n    setEditingVehicle(vehicle);\n    form.setFieldsValue(vehicle);\n    setModalVisible(true);\n  };\n\n  // 添加数据变更通知函数\n  const notifyDataChange = () => {\n    console.log('通知其他页面车辆数据已更新');\n    // 更新本地存储时间戳，便于其他页面检测到变化\n    localStorage.setItem('vehiclesLastUpdated', Date.now().toString());\n\n    // 触发自定义事件\n    try {\n      window.dispatchEvent(new CustomEvent('vehiclesDataChanged'));\n    } catch (e) {\n      console.error('触发自定义事件失败', e);\n    }\n\n    // 尝试通过localStorage的变化来通知其他页面\n    try {\n      const currentData = localStorage.getItem('vehiclesData');\n      localStorage.setItem('vehiclesData', currentData ? currentData : JSON.stringify({\n        timestamp: Date.now()\n      }));\n    } catch (e) {\n      console.error('更新localStorage失败', e);\n    }\n  };\n\n  // 处理删除\n  const handleDelete = async id => {\n    try {\n      const response = await axios.delete(`${API_BASE_URL}/api/vehicles/${id}`);\n      if (response.data && response.data.success) {\n        message.success(response.data.message);\n        fetchVehicles();\n        // 通知数据变更\n        notifyDataChange();\n      } else {\n        var _response$data;\n        throw new Error(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || '删除失败');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('删除失败:', error);\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || '删除失败');\n    }\n  };\n\n  // 修改处理主车变更的函数\n  const handleMainVehicleChange = async selectedVehicle => {\n    try {\n      // 如果当前车辆已经是主车，不做任何操作\n      if (selectedVehicle.isMainVehicle) {\n        return;\n      }\n\n      // 获取当前所有车辆\n      const {\n        data: vehiclesData\n      } = await axios.get(`${API_BASE_URL}/api/vehicles/list`);\n      const currentVehicles = vehiclesData.vehicles || [];\n\n      // 先将当前主车设置为非主车\n      const currentMainVehicle = currentVehicles.find(v => v.isMainVehicle);\n      if (currentMainVehicle) {\n        await axios.put(`${API_BASE_URL}/api/vehicles/${currentMainVehicle.id}`, {\n          ...currentMainVehicle,\n          isMainVehicle: false\n        });\n      }\n\n      // 将选中的车辆设置为主车\n      // 使用专门的设置主车API\n      const response = await axios.post(`${API_BASE_URL}/api/vehicles/setMainVehicle/${selectedVehicle.id}`);\n      if (response.data && response.data.success) {\n        message.success('主车设置成功');\n\n        // 触发主车更改事件，通知其他组件（如CampusModel）\n        window.dispatchEvent(new CustomEvent('mainVehicleChanged', {\n          detail: {\n            newMainVehicle: selectedVehicle\n          }\n        }));\n        await fetchVehicles();\n        // 通知数据变更\n        notifyDataChange();\n      } else {\n        var _response$data2;\n        throw new Error(((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || '更新失败');\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('设置主车失败:', error);\n      message.error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || '设置主车失败');\n      await fetchVehicles();\n    }\n  };\n\n  // 处理表单提交\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingVehicle) {\n        // 更新车辆\n        const response = await axios.put(`${API_BASE_URL}/api/vehicles/${editingVehicle.id}`, {\n          ...values,\n          isMainVehicle: editingVehicle.isMainVehicle || false\n        });\n        if (response.data && response.data.success) {\n          message.success(response.data.message);\n          setModalVisible(false);\n          fetchVehicles();\n          // 通知数据变更\n          notifyDataChange();\n        } else {\n          var _response$data3;\n          throw new Error(((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.message) || '更新失败');\n        }\n      } else {\n        // 添加车辆\n        const response = await axios.post(`${API_BASE_URL}/api/vehicles`, {\n          ...values,\n          isMainVehicle: false // 新车辆默认非主车\n        });\n        if (response.data && response.data.success) {\n          message.success(response.data.message);\n          setModalVisible(false);\n          fetchVehicles();\n          // 通知数据变更\n          notifyDataChange();\n        } else {\n          var _response$data4;\n          throw new Error(((_response$data4 = response.data) === null || _response$data4 === void 0 ? void 0 : _response$data4.message) || '添加失败');\n        }\n      }\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error('操作失败:', error);\n      message.error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || '操作失败');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\"\n        // icon={<PlusOutlined />}\n        ,\n        onClick: handleAddEdit,\n        children: \"\\u6DFB\\u52A0\\u8F66\\u8F86\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      columns: columns,\n      dataSource: vehicles,\n      loading: loading,\n      rowKey: \"id\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingVehicle ? '编辑车辆' : '添加车辆',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => setModalVisible(false),\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"plateNumber\",\n          label: \"\\u8F66\\u724C\\u53F7\",\n          rules: [{\n            required: true,\n            message: '请输入车牌号'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8F66\\u724C\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"type\",\n          label: \"\\u8F66\\u8F86\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请输入车辆类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8F66\\u8F86\\u7C7B\\u578B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"bsmId\",\n          label: \"BSM ID\",\n          rules: [{\n            required: true,\n            message: '请输入BSM ID'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165BSM ID\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 243,\n    columnNumber: 5\n  }, this);\n};\n_s(VehicleManagement, \"Qu/EOlcDr0+VVyu9rE4A//9mA4A=\", false, function () {\n  return [Form.useForm];\n});\n_c2 = VehicleManagement;\nexport default VehicleManagement;\nvar _c, _c2;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"VehicleManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "message", "Space", "PlusOutlined", "EditOutlined", "DeleteOutlined", "axios", "styled", "jsxDEV", "_jsxDEV", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "Container", "div", "_c", "VehicleManagement", "_s", "vehicles", "setVehicles", "loading", "setLoading", "modalVisible", "setModalVisible", "editingVehicle", "setEditingVehicle", "form", "useForm", "fetchVehicles", "response", "get", "data", "error", "_error$response", "_error$response$data", "console", "columns", "title", "dataIndex", "key", "render", "isMainVehicle", "record", "type", "onClick", "handleMainVehicleChange", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "size", "icon", "handleEdit", "danger", "handleDelete", "id", "handleAddEdit", "resetFields", "vehicle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notifyDataChange", "log", "localStorage", "setItem", "Date", "now", "toString", "window", "dispatchEvent", "CustomEvent", "e", "currentData", "getItem", "JSON", "stringify", "timestamp", "delete", "success", "_response$data", "Error", "_error$response2", "_error$response2$data", "selectedVehicle", "vehiclesData", "currentVehicles", "currentMainVehicle", "find", "v", "put", "post", "detail", "newMainVehicle", "_response$data2", "_error$response3", "_error$response3$data", "handleModalOk", "values", "validateFields", "_response$data3", "_response$data4", "_error$response4", "_error$response4$data", "style", "display", "justifyContent", "marginBottom", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onOk", "onCancel", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "_c2", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/VehicleManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, message, Space } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport styled from 'styled-components';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\nconst Container = styled.div`\n  // padding: 24px;\n  background: white;\n  position: relative;\n  z-index: 1002;\n`;\n\nconst VehicleManagement = () => {\n  const [vehicles, setVehicles] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingVehicle, setEditingVehicle] = useState(null);\n  const [form] = Form.useForm();\n\n  // 获取车辆列表\n  const fetchVehicles = async () => {\n    try {\n      setLoading(true);\n      // 直接从 vehicles.json 文件读取数据\n      const response = await axios.get(`${API_BASE_URL}/api/vehicles/list`);\n      if (response.data && response.data.vehicles) {\n        setVehicles(response.data.vehicles);\n      }\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n      message.error(error.response?.data?.message || '获取车辆列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchVehicles();\n  }, []);\n\n  // 表格列定义\n  const columns = [\n    {\n      title: '车牌号',\n      dataIndex: 'plateNumber',\n      key: 'plateNumber',\n    },\n    {\n      title: '车辆类型',\n      dataIndex: 'type',\n      key: 'type',\n    },\n    {\n      title: 'BSM ID',\n      dataIndex: 'bsmId',\n      key: 'bsmId',\n    },\n    {\n      title: '跟随主车',\n      dataIndex: 'isMainVehicle',\n      key: 'isMainVehicle',\n      render: (isMainVehicle, record) => (\n        <Button\n          type={isMainVehicle ? \"primary\" : \"default\"}\n          onClick={() => handleMainVehicleChange(record)}\n        >\n          {isMainVehicle ? '主车' : '设为主车'}\n        </Button>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space size=\"middle\">\n          <Button\n            type=\"link\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Button\n            type=\"link\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record.id)}\n          >\n            删除\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 处理添加/编辑\n  const handleAddEdit = () => {\n    setEditingVehicle(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (vehicle) => {\n    setEditingVehicle(vehicle);\n    form.setFieldsValue(vehicle);\n    setModalVisible(true);\n  };\n\n  // 添加数据变更通知函数\n  const notifyDataChange = () => {\n    console.log('通知其他页面车辆数据已更新');\n    // 更新本地存储时间戳，便于其他页面检测到变化\n    localStorage.setItem('vehiclesLastUpdated', Date.now().toString());\n    \n    // 触发自定义事件\n    try {\n      window.dispatchEvent(new CustomEvent('vehiclesDataChanged'));\n    } catch (e) {\n      console.error('触发自定义事件失败', e);\n    }\n    \n    // 尝试通过localStorage的变化来通知其他页面\n    try {\n      const currentData = localStorage.getItem('vehiclesData');\n      localStorage.setItem('vehiclesData', currentData ? currentData : JSON.stringify({timestamp: Date.now()}));\n    } catch (e) {\n      console.error('更新localStorage失败', e);\n    }\n  };\n\n  // 处理删除\n  const handleDelete = async (id) => {\n    try {\n      const response = await axios.delete(`${API_BASE_URL}/api/vehicles/${id}`);\n      if (response.data && response.data.success) {\n        message.success(response.data.message);\n        fetchVehicles();\n        // 通知数据变更\n        notifyDataChange();\n      } else {\n        throw new Error(response.data?.message || '删除失败');\n      }\n    } catch (error) {\n      console.error('删除失败:', error);\n      message.error(error.response?.data?.message || '删除失败');\n    }\n  };\n\n  // 修改处理主车变更的函数\n  const handleMainVehicleChange = async (selectedVehicle) => {\n    try {\n      // 如果当前车辆已经是主车，不做任何操作\n      if (selectedVehicle.isMainVehicle) {\n        return;\n      }\n\n      // 获取当前所有车辆\n      const { data: vehiclesData } = await axios.get(`${API_BASE_URL}/api/vehicles/list`);\n      const currentVehicles = vehiclesData.vehicles || [];\n\n      // 先将当前主车设置为非主车\n      const currentMainVehicle = currentVehicles.find(v => v.isMainVehicle);\n      if (currentMainVehicle) {\n        await axios.put(`${API_BASE_URL}/api/vehicles/${currentMainVehicle.id}`, {\n          ...currentMainVehicle,\n          isMainVehicle: false\n        });\n      }\n\n      // 将选中的车辆设置为主车\n      // 使用专门的设置主车API\n      const response = await axios.post(`${API_BASE_URL}/api/vehicles/setMainVehicle/${selectedVehicle.id}`);\n\n      if (response.data && response.data.success) {\n        message.success('主车设置成功');\n        \n        // 触发主车更改事件，通知其他组件（如CampusModel）\n        window.dispatchEvent(new CustomEvent('mainVehicleChanged', {\n          detail: {\n            newMainVehicle: selectedVehicle\n          }\n        }));\n        \n        await fetchVehicles();\n        // 通知数据变更\n        notifyDataChange();\n      } else {\n        throw new Error(response.data?.message || '更新失败');\n      }\n    } catch (error) {\n      console.error('设置主车失败:', error);\n      message.error(error.response?.data?.message || '设置主车失败');\n      await fetchVehicles();\n    }\n  };\n\n  // 处理表单提交\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (editingVehicle) {\n        // 更新车辆\n        const response = await axios.put(`${API_BASE_URL}/api/vehicles/${editingVehicle.id}`, {\n          ...values,\n          isMainVehicle: editingVehicle.isMainVehicle || false\n        });\n        if (response.data && response.data.success) {\n          message.success(response.data.message);\n          setModalVisible(false);\n          fetchVehicles();\n          // 通知数据变更\n          notifyDataChange();\n        } else {\n          throw new Error(response.data?.message || '更新失败');\n        }\n      } else {\n        // 添加车辆\n        const response = await axios.post(`${API_BASE_URL}/api/vehicles`, {\n          ...values,\n          isMainVehicle: false // 新车辆默认非主车\n        });\n        if (response.data && response.data.success) {\n          message.success(response.data.message);\n          setModalVisible(false);\n          fetchVehicles();\n          // 通知数据变更\n          notifyDataChange();\n        } else {\n          throw new Error(response.data?.message || '添加失败');\n        }\n      }\n    } catch (error) {\n      console.error('操作失败:', error);\n      message.error(error.response?.data?.message || '操作失败');\n    }\n  };\n\n  return (\n    <Container>\n      <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 16 }}>\n        <Button\n          type=\"primary\"\n          // icon={<PlusOutlined />}\n          onClick={handleAddEdit}\n        >\n          添加车辆\n        </Button>\n      </div>\n\n      <Table\n        columns={columns}\n        dataSource={vehicles}\n        loading={loading}\n        rowKey=\"id\"\n      />\n\n      <Modal\n        title={editingVehicle ? '编辑车辆' : '添加车辆'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => setModalVisible(false)}\n      >\n        <Form form={form} layout=\"vertical\">\n          <Form.Item\n            name=\"plateNumber\"\n            label=\"车牌号\"\n            rules={[{ required: true, message: '请输入车牌号' }]}\n          >\n            <Input placeholder=\"请输入车牌号\" />\n          </Form.Item>\n          <Form.Item\n            name=\"type\"\n            label=\"车辆类型\"\n            rules={[{ required: true, message: '请输入车辆类型' }]}\n          >\n            <Input placeholder=\"请输入车辆类型\" />\n          </Form.Item>\n          <Form.Item\n            name=\"bsmId\"\n            label=\"BSM ID\"\n            rules={[{ required: true, message: '请输入BSM ID' }]}\n          >\n            <Input placeholder=\"请输入BSM ID\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default VehicleManagement; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACxE,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,QAAQ,mBAAmB;AAC9E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAE7E,MAAMC,SAAS,GAAGP,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,SAAS;AAOf,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiC,IAAI,CAAC,GAAG5B,IAAI,CAAC6B,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMQ,QAAQ,GAAG,MAAMxB,KAAK,CAACyB,GAAG,CAAC,GAAGrB,YAAY,oBAAoB,CAAC;MACrE,IAAIoB,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACb,QAAQ,EAAE;QAC3CC,WAAW,CAACU,QAAQ,CAACE,IAAI,CAACb,QAAQ,CAAC;MACrC;IACF,CAAC,CAAC,OAAOc,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACH,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjChC,OAAO,CAACgC,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsBlC,OAAO,KAAI,UAAU,CAAC;IAC5D,CAAC,SAAS;MACRqB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED3B,SAAS,CAAC,MAAM;IACdkC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMQ,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAEA,CAACC,aAAa,EAAEC,MAAM,kBAC5BlC,OAAA,CAACZ,MAAM;MACL+C,IAAI,EAAEF,aAAa,GAAG,SAAS,GAAG,SAAU;MAC5CG,OAAO,EAAEA,CAAA,KAAMC,uBAAuB,CAACH,MAAM,CAAE;MAAAI,QAAA,EAE9CL,aAAa,GAAG,IAAI,GAAG;IAAM;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAEZ,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACW,CAAC,EAAET,MAAM,kBAChBlC,OAAA,CAACP,KAAK;MAACmD,IAAI,EAAC,QAAQ;MAAAN,QAAA,gBAClBtC,OAAA,CAACZ,MAAM;QACL+C,IAAI,EAAC,MAAM;QACXU,IAAI,eAAE7C,OAAA,CAACL,YAAY;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBN,OAAO,EAAEA,CAAA,KAAMU,UAAU,CAACZ,MAAM,CAAE;QAAAI,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1C,OAAA,CAACZ,MAAM;QACL+C,IAAI,EAAC,MAAM;QACXY,MAAM;QACNF,IAAI,eAAE7C,OAAA,CAACJ,cAAc;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBN,OAAO,EAAEA,CAAA,KAAMY,YAAY,CAACd,MAAM,CAACe,EAAE,CAAE;QAAAX,QAAA,EACxC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;;EAED;EACA,MAAMQ,aAAa,GAAGA,CAAA,KAAM;IAC1BjC,iBAAiB,CAAC,IAAI,CAAC;IACvBC,IAAI,CAACiC,WAAW,CAAC,CAAC;IAClBpC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM+B,UAAU,GAAIM,OAAO,IAAK;IAC9BnC,iBAAiB,CAACmC,OAAO,CAAC;IAC1BlC,IAAI,CAACmC,cAAc,CAACD,OAAO,CAAC;IAC5BrC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B3B,OAAO,CAAC4B,GAAG,CAAC,eAAe,CAAC;IAC5B;IACAC,YAAY,CAACC,OAAO,CAAC,qBAAqB,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;;IAElE;IACA,IAAI;MACFC,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,qBAAqB,CAAC,CAAC;IAC9D,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVrC,OAAO,CAACH,KAAK,CAAC,WAAW,EAAEwC,CAAC,CAAC;IAC/B;;IAEA;IACA,IAAI;MACF,MAAMC,WAAW,GAAGT,YAAY,CAACU,OAAO,CAAC,cAAc,CAAC;MACxDV,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEQ,WAAW,GAAGA,WAAW,GAAGE,IAAI,CAACC,SAAS,CAAC;QAACC,SAAS,EAAEX,IAAI,CAACC,GAAG,CAAC;MAAC,CAAC,CAAC,CAAC;IAC3G,CAAC,CAAC,OAAOK,CAAC,EAAE;MACVrC,OAAO,CAACH,KAAK,CAAC,kBAAkB,EAAEwC,CAAC,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMhB,YAAY,GAAG,MAAOC,EAAE,IAAK;IACjC,IAAI;MACF,MAAM5B,QAAQ,GAAG,MAAMxB,KAAK,CAACyE,MAAM,CAAC,GAAGrE,YAAY,iBAAiBgD,EAAE,EAAE,CAAC;MACzE,IAAI5B,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACgD,OAAO,EAAE;QAC1C/E,OAAO,CAAC+E,OAAO,CAAClD,QAAQ,CAACE,IAAI,CAAC/B,OAAO,CAAC;QACtC4B,aAAa,CAAC,CAAC;QACf;QACAkC,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM;QAAA,IAAAkB,cAAA;QACL,MAAM,IAAIC,KAAK,CAAC,EAAAD,cAAA,GAAAnD,QAAQ,CAACE,IAAI,cAAAiD,cAAA,uBAAbA,cAAA,CAAehF,OAAO,KAAI,MAAM,CAAC;MACnD;IACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MAAA,IAAAkD,gBAAA,EAAAC,qBAAA;MACdhD,OAAO,CAACH,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BhC,OAAO,CAACgC,KAAK,CAAC,EAAAkD,gBAAA,GAAAlD,KAAK,CAACH,QAAQ,cAAAqD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnD,IAAI,cAAAoD,qBAAA,uBAApBA,qBAAA,CAAsBnF,OAAO,KAAI,MAAM,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAM6C,uBAAuB,GAAG,MAAOuC,eAAe,IAAK;IACzD,IAAI;MACF;MACA,IAAIA,eAAe,CAAC3C,aAAa,EAAE;QACjC;MACF;;MAEA;MACA,MAAM;QAAEV,IAAI,EAAEsD;MAAa,CAAC,GAAG,MAAMhF,KAAK,CAACyB,GAAG,CAAC,GAAGrB,YAAY,oBAAoB,CAAC;MACnF,MAAM6E,eAAe,GAAGD,YAAY,CAACnE,QAAQ,IAAI,EAAE;;MAEnD;MACA,MAAMqE,kBAAkB,GAAGD,eAAe,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChD,aAAa,CAAC;MACrE,IAAI8C,kBAAkB,EAAE;QACtB,MAAMlF,KAAK,CAACqF,GAAG,CAAC,GAAGjF,YAAY,iBAAiB8E,kBAAkB,CAAC9B,EAAE,EAAE,EAAE;UACvE,GAAG8B,kBAAkB;UACrB9C,aAAa,EAAE;QACjB,CAAC,CAAC;MACJ;;MAEA;MACA;MACA,MAAMZ,QAAQ,GAAG,MAAMxB,KAAK,CAACsF,IAAI,CAAC,GAAGlF,YAAY,gCAAgC2E,eAAe,CAAC3B,EAAE,EAAE,CAAC;MAEtG,IAAI5B,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACgD,OAAO,EAAE;QAC1C/E,OAAO,CAAC+E,OAAO,CAAC,QAAQ,CAAC;;QAEzB;QACAV,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,oBAAoB,EAAE;UACzDqB,MAAM,EAAE;YACNC,cAAc,EAAET;UAClB;QACF,CAAC,CAAC,CAAC;QAEH,MAAMxD,aAAa,CAAC,CAAC;QACrB;QACAkC,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM;QAAA,IAAAgC,eAAA;QACL,MAAM,IAAIb,KAAK,CAAC,EAAAa,eAAA,GAAAjE,QAAQ,CAACE,IAAI,cAAA+D,eAAA,uBAAbA,eAAA,CAAe9F,OAAO,KAAI,MAAM,CAAC;MACnD;IACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MAAA,IAAA+D,gBAAA,EAAAC,qBAAA;MACd7D,OAAO,CAACH,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BhC,OAAO,CAACgC,KAAK,CAAC,EAAA+D,gBAAA,GAAA/D,KAAK,CAACH,QAAQ,cAAAkE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhE,IAAI,cAAAiE,qBAAA,uBAApBA,qBAAA,CAAsBhG,OAAO,KAAI,QAAQ,CAAC;MACxD,MAAM4B,aAAa,CAAC,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMqE,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMxE,IAAI,CAACyE,cAAc,CAAC,CAAC;MAE1C,IAAI3E,cAAc,EAAE;QAClB;QACA,MAAMK,QAAQ,GAAG,MAAMxB,KAAK,CAACqF,GAAG,CAAC,GAAGjF,YAAY,iBAAiBe,cAAc,CAACiC,EAAE,EAAE,EAAE;UACpF,GAAGyC,MAAM;UACTzD,aAAa,EAAEjB,cAAc,CAACiB,aAAa,IAAI;QACjD,CAAC,CAAC;QACF,IAAIZ,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACgD,OAAO,EAAE;UAC1C/E,OAAO,CAAC+E,OAAO,CAAClD,QAAQ,CAACE,IAAI,CAAC/B,OAAO,CAAC;UACtCuB,eAAe,CAAC,KAAK,CAAC;UACtBK,aAAa,CAAC,CAAC;UACf;UACAkC,gBAAgB,CAAC,CAAC;QACpB,CAAC,MAAM;UAAA,IAAAsC,eAAA;UACL,MAAM,IAAInB,KAAK,CAAC,EAAAmB,eAAA,GAAAvE,QAAQ,CAACE,IAAI,cAAAqE,eAAA,uBAAbA,eAAA,CAAepG,OAAO,KAAI,MAAM,CAAC;QACnD;MACF,CAAC,MAAM;QACL;QACA,MAAM6B,QAAQ,GAAG,MAAMxB,KAAK,CAACsF,IAAI,CAAC,GAAGlF,YAAY,eAAe,EAAE;UAChE,GAAGyF,MAAM;UACTzD,aAAa,EAAE,KAAK,CAAC;QACvB,CAAC,CAAC;QACF,IAAIZ,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACgD,OAAO,EAAE;UAC1C/E,OAAO,CAAC+E,OAAO,CAAClD,QAAQ,CAACE,IAAI,CAAC/B,OAAO,CAAC;UACtCuB,eAAe,CAAC,KAAK,CAAC;UACtBK,aAAa,CAAC,CAAC;UACf;UACAkC,gBAAgB,CAAC,CAAC;QACpB,CAAC,MAAM;UAAA,IAAAuC,eAAA;UACL,MAAM,IAAIpB,KAAK,CAAC,EAAAoB,eAAA,GAAAxE,QAAQ,CAACE,IAAI,cAAAsE,eAAA,uBAAbA,eAAA,CAAerG,OAAO,KAAI,MAAM,CAAC;QACnD;MACF;IACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MAAA,IAAAsE,gBAAA,EAAAC,qBAAA;MACdpE,OAAO,CAACH,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BhC,OAAO,CAACgC,KAAK,CAAC,EAAAsE,gBAAA,GAAAtE,KAAK,CAACH,QAAQ,cAAAyE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvE,IAAI,cAAAwE,qBAAA,uBAApBA,qBAAA,CAAsBvG,OAAO,KAAI,MAAM,CAAC;IACxD;EACF,CAAC;EAED,oBACEQ,OAAA,CAACK,SAAS;IAAAiC,QAAA,gBACRtC,OAAA;MAAKgG,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,UAAU;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAA7D,QAAA,eAC5EtC,OAAA,CAACZ,MAAM;QACL+C,IAAI,EAAC;QACL;QAAA;QACAC,OAAO,EAAEc,aAAc;QAAAZ,QAAA,EACxB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN1C,OAAA,CAACb,KAAK;MACJyC,OAAO,EAAEA,OAAQ;MACjBwE,UAAU,EAAE1F,QAAS;MACrBE,OAAO,EAAEA,OAAQ;MACjByF,MAAM,EAAC;IAAI;MAAA9D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAEF1C,OAAA,CAACX,KAAK;MACJwC,KAAK,EAAEb,cAAc,GAAG,MAAM,GAAG,MAAO;MACxCsF,IAAI,EAAExF,YAAa;MACnByF,IAAI,EAAEd,aAAc;MACpBe,QAAQ,EAAEA,CAAA,KAAMzF,eAAe,CAAC,KAAK,CAAE;MAAAuB,QAAA,eAEvCtC,OAAA,CAACV,IAAI;QAAC4B,IAAI,EAAEA,IAAK;QAACuF,MAAM,EAAC,UAAU;QAAAnE,QAAA,gBACjCtC,OAAA,CAACV,IAAI,CAACoH,IAAI;UACRC,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAC,oBAAK;UACXC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtH,OAAO,EAAE;UAAS,CAAC,CAAE;UAAA8C,QAAA,eAE/CtC,OAAA,CAACT,KAAK;YAACwH,WAAW,EAAC;UAAQ;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACZ1C,OAAA,CAACV,IAAI,CAACoH,IAAI;UACRC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtH,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA8C,QAAA,eAEhDtC,OAAA,CAACT,KAAK;YAACwH,WAAW,EAAC;UAAS;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACZ1C,OAAA,CAACV,IAAI,CAACoH,IAAI;UACRC,IAAI,EAAC,OAAO;UACZC,KAAK,EAAC,QAAQ;UACdC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtH,OAAO,EAAE;UAAY,CAAC,CAAE;UAAA8C,QAAA,eAElDtC,OAAA,CAACT,KAAK;YAACwH,WAAW,EAAC;UAAW;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACjC,EAAA,CArRID,iBAAiB;EAAA,QAKNlB,IAAI,CAAC6B,OAAO;AAAA;AAAA6F,GAAA,GALvBxG,iBAAiB;AAuRvB,eAAeA,iBAAiB;AAAC,IAAAD,EAAA,EAAAyG,GAAA;AAAAC,YAAA,CAAA1G,EAAA;AAAA0G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}