{"ast": null, "code": "\"use client\";\n\nimport React, { forwardRef } from 'react';\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport IconWrapper from './IconWrapper';\nconst InnerLoadingIcon = /*#__PURE__*/forwardRef((props, ref) => {\n  const {\n    prefixCls,\n    className,\n    style,\n    iconClassName\n  } = props;\n  const mergedIconCls = classNames(`${prefixCls}-loading-icon`, className);\n  return /*#__PURE__*/React.createElement(IconWrapper, {\n    prefixCls: prefixCls,\n    className: mergedIconCls,\n    style: style,\n    ref: ref\n  }, /*#__PURE__*/React.createElement(LoadingOutlined, {\n    className: iconClassName\n  }));\n});\nconst getCollapsedWidth = () => ({\n  width: 0,\n  opacity: 0,\n  transform: 'scale(0)'\n});\nconst getRealWidth = node => ({\n  width: node.scrollWidth,\n  opacity: 1,\n  transform: 'scale(1)'\n});\nconst DefaultLoadingIcon = props => {\n  const {\n    prefixCls,\n    loading,\n    existIcon,\n    className,\n    style,\n    mount\n  } = props;\n  const visible = !!loading;\n  if (existIcon) {\n    return /*#__PURE__*/React.createElement(InnerLoadingIcon, {\n      prefixCls: prefixCls,\n      className: className,\n      style: style\n    });\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible,\n    // Used for minus flex gap style only\n    motionName: `${prefixCls}-loading-icon-motion`,\n    motionAppear: !mount,\n    motionEnter: !mount,\n    motionLeave: !mount,\n    removeOnLeave: true,\n    onAppearStart: getCollapsedWidth,\n    onAppearActive: getRealWidth,\n    onEnterStart: getCollapsedWidth,\n    onEnterActive: getRealWidth,\n    onLeaveStart: getRealWidth,\n    onLeaveActive: getCollapsedWidth\n  }, (_ref, ref) => {\n    let {\n      className: motionCls,\n      style: motionStyle\n    } = _ref;\n    const mergedStyle = Object.assign(Object.assign({}, style), motionStyle);\n    return /*#__PURE__*/React.createElement(InnerLoadingIcon, {\n      prefixCls: prefixCls,\n      className: classNames(className, motionCls),\n      style: mergedStyle,\n      ref: ref\n    });\n  });\n};\nexport default DefaultLoadingIcon;", "map": {"version": 3, "names": ["React", "forwardRef", "LoadingOutlined", "classNames", "CSSMotion", "IconWrapper", "InnerLoadingIcon", "props", "ref", "prefixCls", "className", "style", "iconClassName", "mergedIconCls", "createElement", "getCollapsedWidth", "width", "opacity", "transform", "getRealWidth", "node", "scrollWidth", "DefaultLoadingIcon", "loading", "existIcon", "mount", "visible", "motionName", "motionAppear", "motionEnter", "motionLeave", "removeOnLeave", "onAppearStart", "onAppearActive", "onEnterStart", "onEnterActive", "onLeaveStart", "onLeaveActive", "_ref", "motionCls", "motionStyle", "mergedStyle", "Object", "assign"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/button/DefaultLoadingIcon.js"], "sourcesContent": ["\"use client\";\n\nimport React, { forwardRef } from 'react';\nimport LoadingOutlined from \"@ant-design/icons/es/icons/LoadingOutlined\";\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nimport IconWrapper from './IconWrapper';\nconst InnerLoadingIcon = /*#__PURE__*/forwardRef((props, ref) => {\n  const {\n    prefixCls,\n    className,\n    style,\n    iconClassName\n  } = props;\n  const mergedIconCls = classNames(`${prefixCls}-loading-icon`, className);\n  return /*#__PURE__*/React.createElement(IconWrapper, {\n    prefixCls: prefixCls,\n    className: mergedIconCls,\n    style: style,\n    ref: ref\n  }, /*#__PURE__*/React.createElement(LoadingOutlined, {\n    className: iconClassName\n  }));\n});\nconst getCollapsedWidth = () => ({\n  width: 0,\n  opacity: 0,\n  transform: 'scale(0)'\n});\nconst getRealWidth = node => ({\n  width: node.scrollWidth,\n  opacity: 1,\n  transform: 'scale(1)'\n});\nconst DefaultLoadingIcon = props => {\n  const {\n    prefixCls,\n    loading,\n    existIcon,\n    className,\n    style,\n    mount\n  } = props;\n  const visible = !!loading;\n  if (existIcon) {\n    return /*#__PURE__*/React.createElement(InnerLoadingIcon, {\n      prefixCls: prefixCls,\n      className: className,\n      style: style\n    });\n  }\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    visible: visible,\n    // Used for minus flex gap style only\n    motionName: `${prefixCls}-loading-icon-motion`,\n    motionAppear: !mount,\n    motionEnter: !mount,\n    motionLeave: !mount,\n    removeOnLeave: true,\n    onAppearStart: getCollapsedWidth,\n    onAppearActive: getRealWidth,\n    onEnterStart: getCollapsedWidth,\n    onEnterActive: getRealWidth,\n    onLeaveStart: getRealWidth,\n    onLeaveActive: getCollapsedWidth\n  }, (_ref, ref) => {\n    let {\n      className: motionCls,\n      style: motionStyle\n    } = _ref;\n    const mergedStyle = Object.assign(Object.assign({}, style), motionStyle);\n    return /*#__PURE__*/React.createElement(InnerLoadingIcon, {\n      prefixCls: prefixCls,\n      className: classNames(className, motionCls),\n      style: mergedStyle,\n      ref: ref\n    });\n  });\n};\nexport default DefaultLoadingIcon;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,WAAW,MAAM,eAAe;AACvC,MAAMC,gBAAgB,GAAG,aAAaL,UAAU,CAAC,CAACM,KAAK,EAAEC,GAAG,KAAK;EAC/D,MAAM;IACJC,SAAS;IACTC,SAAS;IACTC,KAAK;IACLC;EACF,CAAC,GAAGL,KAAK;EACT,MAAMM,aAAa,GAAGV,UAAU,CAAC,GAAGM,SAAS,eAAe,EAAEC,SAAS,CAAC;EACxE,OAAO,aAAaV,KAAK,CAACc,aAAa,CAACT,WAAW,EAAE;IACnDI,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAEG,aAAa;IACxBF,KAAK,EAAEA,KAAK;IACZH,GAAG,EAAEA;EACP,CAAC,EAAE,aAAaR,KAAK,CAACc,aAAa,CAACZ,eAAe,EAAE;IACnDQ,SAAS,EAAEE;EACb,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAMG,iBAAiB,GAAGA,CAAA,MAAO;EAC/BC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE,CAAC;EACVC,SAAS,EAAE;AACb,CAAC,CAAC;AACF,MAAMC,YAAY,GAAGC,IAAI,KAAK;EAC5BJ,KAAK,EAAEI,IAAI,CAACC,WAAW;EACvBJ,OAAO,EAAE,CAAC;EACVC,SAAS,EAAE;AACb,CAAC,CAAC;AACF,MAAMI,kBAAkB,GAAGf,KAAK,IAAI;EAClC,MAAM;IACJE,SAAS;IACTc,OAAO;IACPC,SAAS;IACTd,SAAS;IACTC,KAAK;IACLc;EACF,CAAC,GAAGlB,KAAK;EACT,MAAMmB,OAAO,GAAG,CAAC,CAACH,OAAO;EACzB,IAAIC,SAAS,EAAE;IACb,OAAO,aAAaxB,KAAK,CAACc,aAAa,CAACR,gBAAgB,EAAE;MACxDG,SAAS,EAAEA,SAAS;MACpBC,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ;EACA,OAAO,aAAaX,KAAK,CAACc,aAAa,CAACV,SAAS,EAAE;IACjDsB,OAAO,EAAEA,OAAO;IAChB;IACAC,UAAU,EAAE,GAAGlB,SAAS,sBAAsB;IAC9CmB,YAAY,EAAE,CAACH,KAAK;IACpBI,WAAW,EAAE,CAACJ,KAAK;IACnBK,WAAW,EAAE,CAACL,KAAK;IACnBM,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAEjB,iBAAiB;IAChCkB,cAAc,EAAEd,YAAY;IAC5Be,YAAY,EAAEnB,iBAAiB;IAC/BoB,aAAa,EAAEhB,YAAY;IAC3BiB,YAAY,EAAEjB,YAAY;IAC1BkB,aAAa,EAAEtB;EACjB,CAAC,EAAE,CAACuB,IAAI,EAAE9B,GAAG,KAAK;IAChB,IAAI;MACFE,SAAS,EAAE6B,SAAS;MACpB5B,KAAK,EAAE6B;IACT,CAAC,GAAGF,IAAI;IACR,MAAMG,WAAW,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhC,KAAK,CAAC,EAAE6B,WAAW,CAAC;IACxE,OAAO,aAAaxC,KAAK,CAACc,aAAa,CAACR,gBAAgB,EAAE;MACxDG,SAAS,EAAEA,SAAS;MACpBC,SAAS,EAAEP,UAAU,CAACO,SAAS,EAAE6B,SAAS,CAAC;MAC3C5B,KAAK,EAAE8B,WAAW;MAClBjC,GAAG,EAAEA;IACP,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,eAAec,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}