{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\VehicleManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, message, Space } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = 'http://localhost:5000/api';\nconst Container = styled.div`\n  padding: 24px;\n  background: white;\n  position: relative;\n  z-index: 1002;\n`;\n_c = Container;\nconst VehicleManagement = () => {\n  _s();\n  const [vehicles, setVehicles] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingVehicle, setEditingVehicle] = useState(null);\n  const [form] = Form.useForm();\n\n  // 获取车辆列表\n  const fetchVehicles = async () => {\n    try {\n      setLoading(true);\n      // 直接从 vehicles.json 文件读取数据\n      const response = await axios.get(`${API_BASE_URL}/vehicles/list`);\n      if (response.data && response.data.vehicles) {\n        setVehicles(response.data.vehicles);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('获取车辆列表失败:', error);\n      message.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || '获取车辆列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchVehicles();\n  }, []);\n\n  // 表格列定义\n  const columns = [{\n    title: '车牌号',\n    dataIndex: 'plateNumber',\n    key: 'plateNumber'\n  }, {\n    title: '车辆类型',\n    dataIndex: 'type',\n    key: 'type'\n  }, {\n    title: 'BSM ID',\n    dataIndex: 'bsmId',\n    key: 'bsmId'\n  }, {\n    title: '跟随主车',\n    dataIndex: 'isMainVehicle',\n    key: 'isMainVehicle',\n    render: (isMainVehicle, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: isMainVehicle ? \"primary\" : \"default\",\n      onClick: () => handleMainVehicleChange(record),\n      children: isMainVehicle ? '主车' : '设为主车'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        danger: true,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleDelete(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 处理添加/编辑\n  const handleAddEdit = () => {\n    setEditingVehicle(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = vehicle => {\n    setEditingVehicle(vehicle);\n    form.setFieldsValue(vehicle);\n    setModalVisible(true);\n  };\n\n  // 处理删除\n  const handleDelete = async id => {\n    try {\n      const response = await axios.delete(`${API_BASE_URL}/vehicles/${id}`);\n      if (response.data && response.data.success) {\n        message.success(response.data.message);\n        fetchVehicles();\n      } else {\n        var _response$data;\n        throw new Error(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || '删除失败');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('删除失败:', error);\n      message.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || '删除失败');\n    }\n  };\n\n  // 修改处理主车变更的函数\n  const handleMainVehicleChange = async selectedVehicle => {\n    try {\n      // 如果当前车辆已经是主车，不做任何操作\n      if (selectedVehicle.isMainVehicle) {\n        return;\n      }\n\n      // 先将选中的车辆设置为主车\n      const response = await axios.put(`${API_BASE_URL}/vehicles/${selectedVehicle.id}`, {\n        ...selectedVehicle,\n        isMainVehicle: true,\n        updatedAt: new Date().toISOString()\n      });\n      if (response.data && response.data.success) {\n        // 获取更新后的所有车辆数据\n        const allVehicles = response.data.allVehicles;\n\n        // 更新其他车辆为非主车\n        await Promise.all(allVehicles.filter(v => v.id !== selectedVehicle.id && v.isMainVehicle).map(vehicle => axios.put(`${API_BASE_URL}/vehicles/${vehicle.id}`, {\n          ...vehicle,\n          isMainVehicle: false,\n          updatedAt: new Date().toISOString()\n        })));\n        message.success('主车设置成功');\n        await fetchVehicles(); // 刷新列表\n      } else {\n        var _response$data2;\n        throw new Error(((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.message) || '更新失败');\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data, _error$response4, _error$response4$data;\n      console.error('设置主车失败:', error);\n      if ((_error$response3 = error.response) !== null && _error$response3 !== void 0 && (_error$response3$data = _error$response3.data) !== null && _error$response3$data !== void 0 && _error$response3$data.errorDetails) {\n        console.error('错误详情:', error.response.data.errorDetails);\n      }\n      message.error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || '设置主车失败');\n      await fetchVehicles(); // 刷新列表以恢复状态\n    }\n  };\n\n  // 处理表单提交\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingVehicle) {\n        // 更新车辆\n        const response = await axios.put(`${API_BASE_URL}/vehicles/${editingVehicle.id}`, {\n          ...values,\n          isMainVehicle: editingVehicle.isMainVehicle || false\n        });\n        if (response.data && response.data.success) {\n          message.success(response.data.message);\n          setModalVisible(false);\n          fetchVehicles();\n        } else {\n          var _response$data3;\n          throw new Error(((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.message) || '更新失败');\n        }\n      } else {\n        // 添加车辆\n        const response = await axios.post(`${API_BASE_URL}/vehicles`, {\n          ...values,\n          isMainVehicle: false // 新车辆默认非主车\n        });\n        if (response.data && response.data.success) {\n          message.success(response.data.message);\n          setModalVisible(false);\n          fetchVehicles();\n        } else {\n          var _response$data4;\n          throw new Error(((_response$data4 = response.data) === null || _response$data4 === void 0 ? void 0 : _response$data4.message) || '添加失败');\n        }\n      }\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      console.error('操作失败:', error);\n      message.error(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || '操作失败');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 17\n        }, this),\n        onClick: handleAddEdit,\n        children: \"\\u6DFB\\u52A0\\u8F66\\u8F86\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      columns: columns,\n      dataSource: vehicles,\n      loading: loading,\n      rowKey: \"id\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingVehicle ? '编辑车辆' : '添加车辆',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => setModalVisible(false),\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"plateNumber\",\n          label: \"\\u8F66\\u724C\\u53F7\",\n          rules: [{\n            required: true,\n            message: '请输入车牌号'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8F66\\u724C\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"type\",\n          label: \"\\u8F66\\u8F86\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请输入车辆类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8F66\\u8F86\\u7C7B\\u578B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"bsmId\",\n          label: \"BSM ID\",\n          rules: [{\n            required: true,\n            message: '请输入BSM ID'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165BSM ID\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 214,\n    columnNumber: 5\n  }, this);\n};\n_s(VehicleManagement, \"Qu/EOlcDr0+VVyu9rE4A//9mA4A=\", false, function () {\n  return [Form.useForm];\n});\n_c2 = VehicleManagement;\nexport default VehicleManagement;\nvar _c, _c2;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"VehicleManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "message", "Space", "PlusOutlined", "EditOutlined", "DeleteOutlined", "axios", "styled", "jsxDEV", "_jsxDEV", "API_BASE_URL", "Container", "div", "_c", "VehicleManagement", "_s", "vehicles", "setVehicles", "loading", "setLoading", "modalVisible", "setModalVisible", "editingVehicle", "setEditingVehicle", "form", "useForm", "fetchVehicles", "response", "get", "data", "error", "_error$response", "_error$response$data", "console", "columns", "title", "dataIndex", "key", "render", "isMainVehicle", "record", "type", "onClick", "handleMainVehicleChange", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "size", "icon", "handleEdit", "danger", "handleDelete", "id", "handleAddEdit", "resetFields", "vehicle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delete", "success", "_response$data", "Error", "_error$response2", "_error$response2$data", "selectedVehicle", "put", "updatedAt", "Date", "toISOString", "allVehicles", "Promise", "all", "filter", "v", "map", "_response$data2", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "errorDetails", "handleModalOk", "values", "validateFields", "_response$data3", "post", "_response$data4", "_error$response5", "_error$response5$data", "style", "display", "justifyContent", "marginBottom", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onOk", "onCancel", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "_c2", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/VehicleManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, message, Space } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport styled from 'styled-components';\n\nconst API_BASE_URL = 'http://localhost:5000/api';\n\nconst Container = styled.div`\n  padding: 24px;\n  background: white;\n  position: relative;\n  z-index: 1002;\n`;\n\nconst VehicleManagement = () => {\n  const [vehicles, setVehicles] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingVehicle, setEditingVehicle] = useState(null);\n  const [form] = Form.useForm();\n\n  // 获取车辆列表\n  const fetchVehicles = async () => {\n    try {\n      setLoading(true);\n      // 直接从 vehicles.json 文件读取数据\n      const response = await axios.get(`${API_BASE_URL}/vehicles/list`);\n      if (response.data && response.data.vehicles) {\n        setVehicles(response.data.vehicles);\n      }\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n      message.error(error.response?.data?.message || '获取车辆列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchVehicles();\n  }, []);\n\n  // 表格列定义\n  const columns = [\n    {\n      title: '车牌号',\n      dataIndex: 'plateNumber',\n      key: 'plateNumber',\n    },\n    {\n      title: '车辆类型',\n      dataIndex: 'type',\n      key: 'type',\n    },\n    {\n      title: 'BSM ID',\n      dataIndex: 'bsmId',\n      key: 'bsmId',\n    },\n    {\n      title: '跟随主车',\n      dataIndex: 'isMainVehicle',\n      key: 'isMainVehicle',\n      render: (isMainVehicle, record) => (\n        <Button\n          type={isMainVehicle ? \"primary\" : \"default\"}\n          onClick={() => handleMainVehicleChange(record)}\n        >\n          {isMainVehicle ? '主车' : '设为主车'}\n        </Button>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space size=\"middle\">\n          <Button\n            type=\"link\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Button\n            type=\"link\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record.id)}\n          >\n            删除\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 处理添加/编辑\n  const handleAddEdit = () => {\n    setEditingVehicle(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (vehicle) => {\n    setEditingVehicle(vehicle);\n    form.setFieldsValue(vehicle);\n    setModalVisible(true);\n  };\n\n  // 处理删除\n  const handleDelete = async (id) => {\n    try {\n      const response = await axios.delete(`${API_BASE_URL}/vehicles/${id}`);\n      if (response.data && response.data.success) {\n        message.success(response.data.message);\n        fetchVehicles();\n      } else {\n        throw new Error(response.data?.message || '删除失败');\n      }\n    } catch (error) {\n      console.error('删除失败:', error);\n      message.error(error.response?.data?.message || '删除失败');\n    }\n  };\n\n  // 修改处理主车变更的函数\n  const handleMainVehicleChange = async (selectedVehicle) => {\n    try {\n      // 如果当前车辆已经是主车，不做任何操作\n      if (selectedVehicle.isMainVehicle) {\n        return;\n      }\n\n      // 先将选中的车辆设置为主车\n      const response = await axios.put(`${API_BASE_URL}/vehicles/${selectedVehicle.id}`, {\n        ...selectedVehicle,\n        isMainVehicle: true,\n        updatedAt: new Date().toISOString()\n      });\n\n      if (response.data && response.data.success) {\n        // 获取更新后的所有车辆数据\n        const allVehicles = response.data.allVehicles;\n        \n        // 更新其他车辆为非主车\n        await Promise.all(\n          allVehicles\n            .filter(v => v.id !== selectedVehicle.id && v.isMainVehicle)\n            .map(vehicle =>\n              axios.put(`${API_BASE_URL}/vehicles/${vehicle.id}`, {\n                ...vehicle,\n                isMainVehicle: false,\n                updatedAt: new Date().toISOString()\n              })\n            )\n        );\n\n        message.success('主车设置成功');\n        await fetchVehicles(); // 刷新列表\n      } else {\n        throw new Error(response.data?.message || '更新失败');\n      }\n    } catch (error) {\n      console.error('设置主车失败:', error);\n      if (error.response?.data?.errorDetails) {\n        console.error('错误详情:', error.response.data.errorDetails);\n      }\n      message.error(error.response?.data?.message || '设置主车失败');\n      await fetchVehicles(); // 刷新列表以恢复状态\n    }\n  };\n\n  // 处理表单提交\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (editingVehicle) {\n        // 更新车辆\n        const response = await axios.put(`${API_BASE_URL}/vehicles/${editingVehicle.id}`, {\n          ...values,\n          isMainVehicle: editingVehicle.isMainVehicle || false\n        });\n        if (response.data && response.data.success) {\n          message.success(response.data.message);\n          setModalVisible(false);\n          fetchVehicles();\n        } else {\n          throw new Error(response.data?.message || '更新失败');\n        }\n      } else {\n        // 添加车辆\n        const response = await axios.post(`${API_BASE_URL}/vehicles`, {\n          ...values,\n          isMainVehicle: false // 新车辆默认非主车\n        });\n        if (response.data && response.data.success) {\n          message.success(response.data.message);\n          setModalVisible(false);\n          fetchVehicles();\n        } else {\n          throw new Error(response.data?.message || '添加失败');\n        }\n      }\n    } catch (error) {\n      console.error('操作失败:', error);\n      message.error(error.response?.data?.message || '操作失败');\n    }\n  };\n\n  return (\n    <Container>\n      <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: 16 }}>\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={handleAddEdit}\n        >\n          添加车辆\n        </Button>\n      </div>\n\n      <Table\n        columns={columns}\n        dataSource={vehicles}\n        loading={loading}\n        rowKey=\"id\"\n      />\n\n      <Modal\n        title={editingVehicle ? '编辑车辆' : '添加车辆'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => setModalVisible(false)}\n      >\n        <Form form={form} layout=\"vertical\">\n          <Form.Item\n            name=\"plateNumber\"\n            label=\"车牌号\"\n            rules={[{ required: true, message: '请输入车牌号' }]}\n          >\n            <Input placeholder=\"请输入车牌号\" />\n          </Form.Item>\n          <Form.Item\n            name=\"type\"\n            label=\"车辆类型\"\n            rules={[{ required: true, message: '请输入车辆类型' }]}\n          >\n            <Input placeholder=\"请输入车辆类型\" />\n          </Form.Item>\n          <Form.Item\n            name=\"bsmId\"\n            label=\"BSM ID\"\n            rules={[{ required: true, message: '请输入BSM ID' }]}\n          >\n            <Input placeholder=\"请输入BSM ID\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default VehicleManagement; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACxE,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,QAAQ,mBAAmB;AAC9E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,YAAY,GAAG,2BAA2B;AAEhD,MAAMC,SAAS,GAAGJ,MAAM,CAACK,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,SAAS;AAOf,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8B,IAAI,CAAC,GAAGzB,IAAI,CAAC0B,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMQ,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,GAAG,CAAC,GAAGlB,YAAY,gBAAgB,CAAC;MACjE,IAAIiB,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACb,QAAQ,EAAE;QAC3CC,WAAW,CAACU,QAAQ,CAACE,IAAI,CAACb,QAAQ,CAAC;MACrC;IACF,CAAC,CAAC,OAAOc,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACdC,OAAO,CAACH,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC7B,OAAO,CAAC6B,KAAK,CAAC,EAAAC,eAAA,GAAAD,KAAK,CAACH,QAAQ,cAAAI,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsB/B,OAAO,KAAI,UAAU,CAAC;IAC5D,CAAC,SAAS;MACRkB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDxB,SAAS,CAAC,MAAM;IACd+B,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMQ,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAEA,CAACC,aAAa,EAAEC,MAAM,kBAC5B/B,OAAA,CAACZ,MAAM;MACL4C,IAAI,EAAEF,aAAa,GAAG,SAAS,GAAG,SAAU;MAC5CG,OAAO,EAAEA,CAAA,KAAMC,uBAAuB,CAACH,MAAM,CAAE;MAAAI,QAAA,EAE9CL,aAAa,GAAG,IAAI,GAAG;IAAM;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAEZ,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACW,CAAC,EAAET,MAAM,kBAChB/B,OAAA,CAACP,KAAK;MAACgD,IAAI,EAAC,QAAQ;MAAAN,QAAA,gBAClBnC,OAAA,CAACZ,MAAM;QACL4C,IAAI,EAAC,MAAM;QACXU,IAAI,eAAE1C,OAAA,CAACL,YAAY;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBN,OAAO,EAAEA,CAAA,KAAMU,UAAU,CAACZ,MAAM,CAAE;QAAAI,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvC,OAAA,CAACZ,MAAM;QACL4C,IAAI,EAAC,MAAM;QACXY,MAAM;QACNF,IAAI,eAAE1C,OAAA,CAACJ,cAAc;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBN,OAAO,EAAEA,CAAA,KAAMY,YAAY,CAACd,MAAM,CAACe,EAAE,CAAE;QAAAX,QAAA,EACxC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;;EAED;EACA,MAAMQ,aAAa,GAAGA,CAAA,KAAM;IAC1BjC,iBAAiB,CAAC,IAAI,CAAC;IACvBC,IAAI,CAACiC,WAAW,CAAC,CAAC;IAClBpC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM+B,UAAU,GAAIM,OAAO,IAAK;IAC9BnC,iBAAiB,CAACmC,OAAO,CAAC;IAC1BlC,IAAI,CAACmC,cAAc,CAACD,OAAO,CAAC;IAC5BrC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMiC,YAAY,GAAG,MAAOC,EAAE,IAAK;IACjC,IAAI;MACF,MAAM5B,QAAQ,GAAG,MAAMrB,KAAK,CAACsD,MAAM,CAAC,GAAGlD,YAAY,aAAa6C,EAAE,EAAE,CAAC;MACrE,IAAI5B,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACgC,OAAO,EAAE;QAC1C5D,OAAO,CAAC4D,OAAO,CAAClC,QAAQ,CAACE,IAAI,CAAC5B,OAAO,CAAC;QACtCyB,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QAAA,IAAAoC,cAAA;QACL,MAAM,IAAIC,KAAK,CAAC,EAAAD,cAAA,GAAAnC,QAAQ,CAACE,IAAI,cAAAiC,cAAA,uBAAbA,cAAA,CAAe7D,OAAO,KAAI,MAAM,CAAC;MACnD;IACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MAAA,IAAAkC,gBAAA,EAAAC,qBAAA;MACdhC,OAAO,CAACH,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B7B,OAAO,CAAC6B,KAAK,CAAC,EAAAkC,gBAAA,GAAAlC,KAAK,CAACH,QAAQ,cAAAqC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnC,IAAI,cAAAoC,qBAAA,uBAApBA,qBAAA,CAAsBhE,OAAO,KAAI,MAAM,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAM0C,uBAAuB,GAAG,MAAOuB,eAAe,IAAK;IACzD,IAAI;MACF;MACA,IAAIA,eAAe,CAAC3B,aAAa,EAAE;QACjC;MACF;;MAEA;MACA,MAAMZ,QAAQ,GAAG,MAAMrB,KAAK,CAAC6D,GAAG,CAAC,GAAGzD,YAAY,aAAawD,eAAe,CAACX,EAAE,EAAE,EAAE;QACjF,GAAGW,eAAe;QAClB3B,aAAa,EAAE,IAAI;QACnB6B,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC,CAAC;MAEF,IAAI3C,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACgC,OAAO,EAAE;QAC1C;QACA,MAAMU,WAAW,GAAG5C,QAAQ,CAACE,IAAI,CAAC0C,WAAW;;QAE7C;QACA,MAAMC,OAAO,CAACC,GAAG,CACfF,WAAW,CACRG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpB,EAAE,KAAKW,eAAe,CAACX,EAAE,IAAIoB,CAAC,CAACpC,aAAa,CAAC,CAC3DqC,GAAG,CAAClB,OAAO,IACVpD,KAAK,CAAC6D,GAAG,CAAC,GAAGzD,YAAY,aAAagD,OAAO,CAACH,EAAE,EAAE,EAAE;UAClD,GAAGG,OAAO;UACVnB,aAAa,EAAE,KAAK;UACpB6B,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC,CACH,CACJ,CAAC;QAEDrE,OAAO,CAAC4D,OAAO,CAAC,QAAQ,CAAC;QACzB,MAAMnC,aAAa,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,MAAM;QAAA,IAAAmD,eAAA;QACL,MAAM,IAAId,KAAK,CAAC,EAAAc,eAAA,GAAAlD,QAAQ,CAACE,IAAI,cAAAgD,eAAA,uBAAbA,eAAA,CAAe5E,OAAO,KAAI,MAAM,CAAC;MACnD;IACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MAAA,IAAAgD,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdhD,OAAO,CAACH,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,KAAAgD,gBAAA,GAAIhD,KAAK,CAACH,QAAQ,cAAAmD,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjD,IAAI,cAAAkD,qBAAA,eAApBA,qBAAA,CAAsBG,YAAY,EAAE;QACtCjD,OAAO,CAACH,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACqD,YAAY,CAAC;MAC1D;MACAjF,OAAO,CAAC6B,KAAK,CAAC,EAAAkD,gBAAA,GAAAlD,KAAK,CAACH,QAAQ,cAAAqD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnD,IAAI,cAAAoD,qBAAA,uBAApBA,qBAAA,CAAsBhF,OAAO,KAAI,QAAQ,CAAC;MACxD,MAAMyB,aAAa,CAAC,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMyD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM5D,IAAI,CAAC6D,cAAc,CAAC,CAAC;MAE1C,IAAI/D,cAAc,EAAE;QAClB;QACA,MAAMK,QAAQ,GAAG,MAAMrB,KAAK,CAAC6D,GAAG,CAAC,GAAGzD,YAAY,aAAaY,cAAc,CAACiC,EAAE,EAAE,EAAE;UAChF,GAAG6B,MAAM;UACT7C,aAAa,EAAEjB,cAAc,CAACiB,aAAa,IAAI;QACjD,CAAC,CAAC;QACF,IAAIZ,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACgC,OAAO,EAAE;UAC1C5D,OAAO,CAAC4D,OAAO,CAAClC,QAAQ,CAACE,IAAI,CAAC5B,OAAO,CAAC;UACtCoB,eAAe,CAAC,KAAK,CAAC;UACtBK,aAAa,CAAC,CAAC;QACjB,CAAC,MAAM;UAAA,IAAA4D,eAAA;UACL,MAAM,IAAIvB,KAAK,CAAC,EAAAuB,eAAA,GAAA3D,QAAQ,CAACE,IAAI,cAAAyD,eAAA,uBAAbA,eAAA,CAAerF,OAAO,KAAI,MAAM,CAAC;QACnD;MACF,CAAC,MAAM;QACL;QACA,MAAM0B,QAAQ,GAAG,MAAMrB,KAAK,CAACiF,IAAI,CAAC,GAAG7E,YAAY,WAAW,EAAE;UAC5D,GAAG0E,MAAM;UACT7C,aAAa,EAAE,KAAK,CAAC;QACvB,CAAC,CAAC;QACF,IAAIZ,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACgC,OAAO,EAAE;UAC1C5D,OAAO,CAAC4D,OAAO,CAAClC,QAAQ,CAACE,IAAI,CAAC5B,OAAO,CAAC;UACtCoB,eAAe,CAAC,KAAK,CAAC;UACtBK,aAAa,CAAC,CAAC;QACjB,CAAC,MAAM;UAAA,IAAA8D,eAAA;UACL,MAAM,IAAIzB,KAAK,CAAC,EAAAyB,eAAA,GAAA7D,QAAQ,CAACE,IAAI,cAAA2D,eAAA,uBAAbA,eAAA,CAAevF,OAAO,KAAI,MAAM,CAAC;QACnD;MACF;IACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MAAA,IAAA2D,gBAAA,EAAAC,qBAAA;MACdzD,OAAO,CAACH,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B7B,OAAO,CAAC6B,KAAK,CAAC,EAAA2D,gBAAA,GAAA3D,KAAK,CAACH,QAAQ,cAAA8D,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5D,IAAI,cAAA6D,qBAAA,uBAApBA,qBAAA,CAAsBzF,OAAO,KAAI,MAAM,CAAC;IACxD;EACF,CAAC;EAED,oBACEQ,OAAA,CAACE,SAAS;IAAAiC,QAAA,gBACRnC,OAAA;MAAKkF,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,UAAU;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAlD,QAAA,eAC5EnC,OAAA,CAACZ,MAAM;QACL4C,IAAI,EAAC,SAAS;QACdU,IAAI,eAAE1C,OAAA,CAACN,YAAY;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBN,OAAO,EAAEc,aAAc;QAAAZ,QAAA,EACxB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENvC,OAAA,CAACb,KAAK;MACJsC,OAAO,EAAEA,OAAQ;MACjB6D,UAAU,EAAE/E,QAAS;MACrBE,OAAO,EAAEA,OAAQ;MACjB8E,MAAM,EAAC;IAAI;MAAAnD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAEFvC,OAAA,CAACX,KAAK;MACJqC,KAAK,EAAEb,cAAc,GAAG,MAAM,GAAG,MAAO;MACxC2E,IAAI,EAAE7E,YAAa;MACnB8E,IAAI,EAAEf,aAAc;MACpBgB,QAAQ,EAAEA,CAAA,KAAM9E,eAAe,CAAC,KAAK,CAAE;MAAAuB,QAAA,eAEvCnC,OAAA,CAACV,IAAI;QAACyB,IAAI,EAAEA,IAAK;QAAC4E,MAAM,EAAC,UAAU;QAAAxD,QAAA,gBACjCnC,OAAA,CAACV,IAAI,CAACsG,IAAI;UACRC,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAC,oBAAK;UACXC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExG,OAAO,EAAE;UAAS,CAAC,CAAE;UAAA2C,QAAA,eAE/CnC,OAAA,CAACT,KAAK;YAAC0G,WAAW,EAAC;UAAQ;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACZvC,OAAA,CAACV,IAAI,CAACsG,IAAI;UACRC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExG,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA2C,QAAA,eAEhDnC,OAAA,CAACT,KAAK;YAAC0G,WAAW,EAAC;UAAS;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACZvC,OAAA,CAACV,IAAI,CAACsG,IAAI;UACRC,IAAI,EAAC,OAAO;UACZC,KAAK,EAAC,QAAQ;UACdC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExG,OAAO,EAAE;UAAY,CAAC,CAAE;UAAA2C,QAAA,eAElDnC,OAAA,CAACT,KAAK;YAAC0G,WAAW,EAAC;UAAW;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACjC,EAAA,CAxPID,iBAAiB;EAAA,QAKNf,IAAI,CAAC0B,OAAO;AAAA;AAAAkF,GAAA,GALvB7F,iBAAiB;AA0PvB,eAAeA,iBAAiB;AAAC,IAAAD,EAAA,EAAA8F,GAAA;AAAAC,YAAA,CAAA/F,EAAA;AAAA+F,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}