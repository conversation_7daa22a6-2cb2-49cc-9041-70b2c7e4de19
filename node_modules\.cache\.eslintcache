[{"G:\\AI_tools\\cursor\\projects\\education web\\src\\index.js": "1", "G:\\AI_tools\\cursor\\projects\\education web\\src\\App.js": "2", "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\Login.jsx": "3", "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\SystemManagement.jsx": "4", "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\RealTimeTraffic.jsx": "5", "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\DeviceStatus.jsx": "6", "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\RoadMonitoring.jsx": "7", "G:\\AI_tools\\cursor\\projects\\education web\\src\\components\\layout\\MainLayout.jsx": "8", "G:\\AI_tools\\cursor\\projects\\education web\\src\\components\\layout\\CollapsibleSidebar.jsx": "9", "G:\\AI_tools\\cursor\\projects\\education web\\src\\services\\auth.js": "10", "G:\\AI_tools\\cursor\\projects\\education web\\src\\components\\CampusModel.jsx": "11", "G:\\AI_tools\\cursor\\projects\\education web\\src\\utils\\CoordinateConverter.js": "12", "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\UserManagement.jsx": "13", "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\DeviceManagement.jsx": "14", "G:\\AI_tools\\cursor\\projects\\education web\\src\\components\\VideoPlayer.jsx": "15", "G:\\AI_tools\\cursor\\projects\\education web\\src\\utils\\axios.js": "16", "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\VehicleManagement.jsx": "17", "G:\\AI_tools\\cursor\\projects\\education web\\src\\components\\DevicePopoverContent.jsx": "18", "G:\\AI_tools\\cursor\\projects\\education web\\src\\utils\\eventDeduplicationTest.js": "19"}, {"size": 264, "mtime": 1745480630472, "results": "20", "hashOfConfig": "21"}, {"size": 2316, "mtime": 1745909907066, "results": "22", "hashOfConfig": "21"}, {"size": 10732, "mtime": 1748511372461, "results": "23", "hashOfConfig": "21"}, {"size": 24282, "mtime": 1748421024251, "results": "24", "hashOfConfig": "21"}, {"size": 57709, "mtime": 1750475656769, "results": "25", "hashOfConfig": "21"}, {"size": 35551, "mtime": 1750153061890, "results": "26", "hashOfConfig": "21"}, {"size": 13947, "mtime": 1748399320473, "results": "27", "hashOfConfig": "21"}, {"size": 8784, "mtime": 1745483941701, "results": "28", "hashOfConfig": "21"}, {"size": 2713, "mtime": 1742458787887, "results": "29", "hashOfConfig": "21"}, {"size": 1435, "mtime": 1742900667547, "results": "30", "hashOfConfig": "21"}, {"size": 154198, "mtime": 1750650200017, "results": "31", "hashOfConfig": "21"}, {"size": 4938, "mtime": 1746588321585, "results": "32", "hashOfConfig": "21"}, {"size": 1554, "mtime": 1742892432117, "results": "33", "hashOfConfig": "21"}, {"size": 17640, "mtime": 1749433251197, "results": "34", "hashOfConfig": "21"}, {"size": 9643, "mtime": 1750151107114, "results": "35", "hashOfConfig": "21"}, {"size": 1221, "mtime": 1744702502156, "results": "36", "hashOfConfig": "21"}, {"size": 8712, "mtime": 1747900955954, "results": "37", "hashOfConfig": "21"}, {"size": 12326, "mtime": 1749195691894, "results": "38", "hashOfConfig": "21"}, {"size": 7963, "mtime": 1750211051561, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "n88kgv", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "G:\\AI_tools\\cursor\\projects\\education web\\src\\index.js", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\App.js", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\Login.jsx", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\SystemManagement.jsx", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\RealTimeTraffic.jsx", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\DeviceStatus.jsx", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\RoadMonitoring.jsx", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\components\\layout\\MainLayout.jsx", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\components\\layout\\CollapsibleSidebar.jsx", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\services\\auth.js", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\components\\CampusModel.jsx", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\utils\\CoordinateConverter.js", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\UserManagement.jsx", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\DeviceManagement.jsx", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\components\\VideoPlayer.jsx", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\utils\\axios.js", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\pages\\VehicleManagement.jsx", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\components\\DevicePopoverContent.jsx", [], [], "G:\\AI_tools\\cursor\\projects\\education web\\src\\utils\\eventDeduplicationTest.js", [], []]