{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nconst path = 'M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z';\nconst SkeletonImage = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    style,\n    active\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, `${prefixCls}-element`, {\n    [`${prefixCls}-active`]: active\n  }, className, rootClassName, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${prefixCls}-image`, className),\n    style: style\n  }, /*#__PURE__*/React.createElement(\"svg\", {\n    viewBox: \"0 0 1098 1024\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    className: `${prefixCls}-image-svg`\n  }, /*#__PURE__*/React.createElement(\"title\", null, \"Image placeholder\"), /*#__PURE__*/React.createElement(\"path\", {\n    d: path,\n    className: `${prefixCls}-image-path`\n  })))));\n};\nexport default SkeletonImage;", "map": {"version": 3, "names": ["React", "classNames", "ConfigContext", "useStyle", "path", "SkeletonImage", "props", "prefixCls", "customizePrefixCls", "className", "rootClassName", "style", "active", "getPrefixCls", "useContext", "wrapCSSVar", "hashId", "cssVarCls", "cls", "createElement", "viewBox", "xmlns", "d"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/skeleton/Image.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nconst path = 'M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z';\nconst SkeletonImage = props => {\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    style,\n    active\n  } = props;\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('skeleton', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, `${prefixCls}-element`, {\n    [`${prefixCls}-active`]: active\n  }, className, rootClassName, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"div\", {\n    className: cls\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(`${prefixCls}-image`, className),\n    style: style\n  }, /*#__PURE__*/React.createElement(\"svg\", {\n    viewBox: \"0 0 1098 1024\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    className: `${prefixCls}-image-svg`\n  }, /*#__PURE__*/React.createElement(\"title\", null, \"Image placeholder\"), /*#__PURE__*/React.createElement(\"path\", {\n    d: path,\n    className: `${prefixCls}-image-path`\n  })))));\n};\nexport default SkeletonImage;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,IAAI,GAAG,i3BAAi3B;AAC93B,MAAMC,aAAa,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC,SAAS,EAAEC,kBAAkB;IAC7BC,SAAS;IACTC,aAAa;IACbC,KAAK;IACLC;EACF,CAAC,GAAGN,KAAK;EACT,MAAM;IACJO;EACF,CAAC,GAAGb,KAAK,CAACc,UAAU,CAACZ,aAAa,CAAC;EACnC,MAAMK,SAAS,GAAGM,YAAY,CAAC,UAAU,EAAEL,kBAAkB,CAAC;EAC9D,MAAM,CAACO,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAACI,SAAS,CAAC;EAC3D,MAAMW,GAAG,GAAGjB,UAAU,CAACM,SAAS,EAAE,GAAGA,SAAS,UAAU,EAAE;IACxD,CAAC,GAAGA,SAAS,SAAS,GAAGK;EAC3B,CAAC,EAAEH,SAAS,EAAEC,aAAa,EAAEM,MAAM,EAAEC,SAAS,CAAC;EAC/C,OAAOF,UAAU,CAAC,aAAaf,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACxDV,SAAS,EAAES;EACb,CAAC,EAAE,aAAalB,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACzCV,SAAS,EAAER,UAAU,CAAC,GAAGM,SAAS,QAAQ,EAAEE,SAAS,CAAC;IACtDE,KAAK,EAAEA;EACT,CAAC,EAAE,aAAaX,KAAK,CAACmB,aAAa,CAAC,KAAK,EAAE;IACzCC,OAAO,EAAE,eAAe;IACxBC,KAAK,EAAE,4BAA4B;IACnCZ,SAAS,EAAE,GAAGF,SAAS;EACzB,CAAC,EAAE,aAAaP,KAAK,CAACmB,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,mBAAmB,CAAC,EAAE,aAAanB,KAAK,CAACmB,aAAa,CAAC,MAAM,EAAE;IAChHG,CAAC,EAAElB,IAAI;IACPK,SAAS,EAAE,GAAGF,SAAS;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACR,CAAC;AACD,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}