{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\DeviceManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, Space, Card, Tag, message } from 'antd';\nimport axios from 'axios';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconst ContentArea = styled.div`\n  // padding: 24px;\n  background: white;\n  position: relative;\n  z-index: 1002;\n`;\n_c = ContentArea;\nconst DeviceManagement = ({\n  id\n}, ref) => {\n  _s();\n  const [devices, setDevices] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingDevice, setEditingDevice] = useState(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentDevice, setCurrentDevice] = useState(null);\n  const [currentDeviceType, setCurrentDeviceType] = useState((editingDevice === null || editingDevice === void 0 ? void 0 : editingDevice.type) || null);\n  const [filterType, setFilterType] = useState('');\n  const [filterLocation, setFilterLocation] = useState('');\n  const [filteredDevices, setFilteredDevices] = useState([]);\n\n  // 获取设备列表\n  const fetchDevices = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/api/devices`);\n      if (response.data.success) {\n        setDevices(response.data.data);\n        setFilteredDevices(response.data.data);\n      } else {\n        throw new Error(response.data.message || '获取设备列表失败');\n      }\n    } catch (error) {\n      console.error('获取设备列表失败:', error);\n      message.error('获取设备列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchDevices();\n  }, []);\n\n  // 新增：筛选功能\n  const handleFilter = () => {\n    let result = [...devices];\n    if (filterType) {\n      result = result.filter(item => item.type === filterType);\n    }\n    if (filterLocation) {\n      result = result.filter(item => item.location === filterLocation);\n    }\n    setFilteredDevices(result);\n  };\n\n  // 新增：重置筛选\n  const handleResetFilter = () => {\n    setFilterType('');\n    setFilterLocation('');\n    setFilteredDevices(devices);\n  };\n\n  // 设备类型和区域选项自动生成\n  const typeOptions = Array.from(new Set(devices.map(d => d.type)));\n  const locationOptions = Array.from(new Set(devices.map(d => d.location)));\n\n  // 处理添加设备\n  const handleAddDevice = () => {\n    setEditingDevice(null);\n    setCurrentDeviceType(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // 处理编辑设备\n  const handleEditDevice = device => {\n    setEditingDevice(device);\n    setCurrentDeviceType(device.type);\n    form.setFieldsValue({\n      name: device.name,\n      type: device.type,\n      status: device.status,\n      location: device.location,\n      ipAddress: device.ipAddress,\n      manufacturer: device.manufacturer,\n      model: device.model,\n      description: device.description,\n      rtspUrl: device.rtspUrl,\n      entrance: device.entrance,\n      mac: device.mac // 添加MAC地址字段\n    });\n    setModalVisible(true);\n  };\n\n  // 处理查看设备详情\n  const handleViewDevice = device => {\n    setCurrentDevice(device);\n    setDetailModalVisible(true);\n  };\n\n  // 处理删除设备\n  const handleDeleteDevice = async deviceId => {\n    try {\n      const response = await axios.delete(`${API_BASE_URL}/api/devices/${deviceId}`);\n      if (response.data.success) {\n        message.success('设备删除成功');\n        fetchDevices(); // 重新获取设备列表\n      } else {\n        throw new Error(response.data.message || '删除设备失败');\n      }\n    } catch (error) {\n      console.error('删除设备失败:', error);\n      message.error('删除设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 处理表单提交\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n\n      // 为调试添加日志\n      console.log('表单验证通过，原始数据:', values);\n      console.log('当前设备类型:', currentDeviceType);\n\n      // 确保摄像头设备有 rtspUrl 字段\n      if (values.type === 'camera' || currentDeviceType === 'camera') {\n        // 直接从表单获取 rtspUrl 值，确保它不会丢失\n        const rtspInput = document.querySelector('textarea[placeholder*=\"RTSP\"]');\n        let rtspUrl = '';\n        if (rtspInput) {\n          rtspUrl = rtspInput.value || '';\n          console.log('从DOM元素获取的 RTSP 地址:', rtspUrl);\n        } else {\n          rtspUrl = form.getFieldValue('rtspUrl') || values.rtspUrl || '';\n          console.log('从表单获取的 RTSP 地址:', rtspUrl);\n        }\n\n        // 无论表单中是否有该字段，都强制设置 rtspUrl\n        values.rtspUrl = rtspUrl;\n        console.log('最终设置的 RTSP 地址:', rtspUrl);\n      } else {\n        // 对于非摄像头设备，确保移除 rtspUrl 字段\n        delete values.rtspUrl;\n        console.log('非摄像头设备，移除 rtspUrl 字段');\n      }\n      console.log('提交表单最终数据:', JSON.stringify(values, null, 2));\n      if (editingDevice) {\n        // 编辑设备\n        const response = await axios.put(`${API_BASE_URL}/api/devices/${editingDevice.id}`, values);\n        if (response.data.success) {\n          message.success('设备更新成功');\n          setModalVisible(false);\n          fetchDevices(); // 重新获取设备列表\n        } else {\n          throw new Error(response.data.message || '更新设备失败');\n        }\n      } else {\n        // 添加设备\n        const response = await axios.post(`${API_BASE_URL}/api/devices`, values);\n        if (response.data.success) {\n          message.success('设备添加成功');\n          setModalVisible(false);\n          fetchDevices(); // 重新获取设备列表\n        } else {\n          throw new Error(response.data.message || '添加设备失败');\n        }\n      }\n    } catch (error) {\n      console.error('保存设备失败:', error);\n      message.error('保存设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 修改设备类型选择的处理函数\n  const handleDeviceTypeChange = value => {\n    console.log('设备类型变更为:', value);\n    setCurrentDeviceType(value);\n    if (value === 'camera') {\n      // 如果是摄像头，确保表单中有 rtspUrl 字段\n      const currentRtspUrl = form.getFieldValue('rtspUrl') || '';\n      form.setFieldsValue({\n        type: value,\n        rtspUrl: currentRtspUrl\n      });\n      console.log('设置摄像头 RTSP 初始值:', currentRtspUrl);\n    } else {\n      // 如果不是摄像头，移除 rtspUrl 字段\n      form.setFieldsValue({\n        type: value\n      });\n      form.resetFields(['rtspUrl']);\n      console.log('重置 RTSP 字段');\n    }\n  };\n\n  // 渲染状态标签\n  const renderStatusTag = status => {\n    const statusMap = {\n      online: {\n        color: 'green',\n        text: '在线'\n      },\n      offline: {\n        color: 'gray',\n        text: '离线'\n      },\n      warning: {\n        color: 'orange',\n        text: '警告'\n      },\n      error: {\n        color: 'red',\n        text: '错误'\n      },\n      maintenance: {\n        color: 'blue',\n        text: '维护中'\n      }\n    };\n    const statusInfo = statusMap[status] || {\n      color: 'default',\n      text: status\n    };\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: statusInfo.color,\n      children: statusInfo.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 12\n    }, this);\n  };\n\n  // 设备类型映射\n  const deviceTypeMap = {\n    camera: '摄像头',\n    mmwave_radar: '毫米波雷达',\n    lidar: '激光雷达',\n    rsu: 'RSU',\n    edge_computing: '边缘计算单元',\n    obu: 'OBU'\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '设备名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '设备类型',\n    dataIndex: 'type',\n    key: 'type',\n    render: type => deviceTypeMap[type] || type\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: renderStatusTag\n  }, {\n    title: '所在区域',\n    dataIndex: 'location',\n    key: 'location'\n  }, {\n    title: 'IP地址',\n    dataIndex: 'ipAddress',\n    key: 'ipAddress'\n  }, {\n    title: 'MAC地址',\n    dataIndex: 'mac',\n    key: 'mac',\n    render: (mac, record) => {\n      // 只有RSU设备才显示MAC地址\n      return record.type === 'rsu' ? mac || '-' : '-';\n    }\n  }, {\n    title: '安装位置',\n    dataIndex: 'entrance',\n    key: 'entrance'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleViewDevice(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleEditDevice(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        danger: true,\n        onClick: () => handleDeleteDevice(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 暴露 fetchDevices 方法\n  useImperativeHandle(ref, () => ({\n    fetchDevices\n  }));\n  return (\n    /*#__PURE__*/\n    // <div id={id}>\n    _jsxDEV(ContentArea, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          marginBottom: 16,\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: 8\n          },\n          children: [/*#__PURE__*/_jsxDEV(Select, {\n            allowClear: true,\n            style: {\n              width: 120\n            },\n            placeholder: \"\\u7B5B\\u9009\\u7C7B\\u578B\",\n            value: filterType || undefined,\n            onChange: value => setFilterType(value),\n            children: typeOptions.map(type => /*#__PURE__*/_jsxDEV(Option, {\n              value: type,\n              children: deviceTypeMap[type] || type\n            }, type, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            allowClear: true,\n            style: {\n              width: 120\n            },\n            placeholder: \"\\u7B5B\\u9009\\u533A\\u57DF\",\n            value: filterLocation || undefined,\n            onChange: value => setFilterLocation(value),\n            children: locationOptions.map(loc => /*#__PURE__*/_jsxDEV(Option, {\n              value: loc,\n              children: loc\n            }, loc, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            onClick: handleFilter,\n            children: \"\\u67E5\\u8BE2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleResetFilter,\n            children: \"\\u91CD\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          onClick: handleAddDevice,\n          children: \"\\u6DFB\\u52A0\\u8BBE\\u5907\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        loading: loading,\n        dataSource: filteredDevices,\n        columns: columns,\n        rowKey: \"id\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        title: editingDevice ? '编辑设备' : '添加设备',\n        open: modalVisible,\n        onOk: handleModalOk,\n        onCancel: () => setModalVisible(false),\n        width: 600,\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          form: form,\n          layout: \"vertical\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"name\",\n            label: \"\\u8BBE\\u5907\\u540D\\u79F0\",\n            rules: [{\n              required: true,\n              message: '请输入设备名称'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u540D\\u79F0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"type\",\n            label: \"\\u8BBE\\u5907\\u7C7B\\u578B\",\n            rules: [{\n              required: true,\n              message: '请选择设备类型'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u7C7B\\u578B\",\n              onChange: handleDeviceTypeChange,\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"camera\",\n                children: \"\\u6444\\u50CF\\u5934\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"mmwave_radar\",\n                children: \"\\u6BEB\\u7C73\\u6CE2\\u96F7\\u8FBE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"lidar\",\n                children: \"\\u6FC0\\u5149\\u96F7\\u8FBE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"rsu\",\n                children: \"RSU\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"edge_computing\",\n                children: \"\\u8FB9\\u7F18\\u8BA1\\u7B97\\u5355\\u5143\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"obu\",\n                children: \"OBU\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 11\n          }, this), currentDeviceType === 'camera' && /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"rtspUrl\",\n            label: \"RTSP\\u5730\\u5740\",\n            initialValue: \"\",\n            children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n              id: \"rtspUrlInput\",\n              placeholder: \"\\u8BF7\\u8F93\\u5165RTSP\\u5730\\u5740\\uFF0C\\u4F8B\\u5982\\uFF1Artsp://admin:password@192.168.1.100:554/stream1\",\n              autoSize: {\n                minRows: 1,\n                maxRows: 3\n              },\n              defaultValue: \"\",\n              onChange: e => {\n                // 将输入的 RTSP 地址保存到表单\n                form.setFieldsValue({\n                  rtspUrl: e.target.value\n                });\n                console.log('RTSP 地址输入变化:', e.target.value);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this), currentDeviceType === 'rsu' && /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"mac\",\n            label: \"MAC\\u5730\\u5740\",\n            rules: [{\n              pattern: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,\n              message: '请输入正确的MAC地址格式，例如：AA:BB:CC:DD:EE:FF'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165MAC\\u5730\\u5740\\uFF0C\\u4F8B\\u5982\\uFF1AAA:BB:CC:DD:EE:FF\",\n              onChange: e => {\n                // 将输入的 MAC 地址保存到表单\n                form.setFieldsValue({\n                  mac: e.target.value\n                });\n                console.log('MAC 地址输入变化:', e.target.value);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"status\",\n            label: \"\\u8BBE\\u5907\\u72B6\\u6001\",\n            rules: [{\n              required: true,\n              message: '请选择设备状态'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u72B6\\u6001\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"online\",\n                children: \"\\u5728\\u7EBF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"offline\",\n                children: \"\\u79BB\\u7EBF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"warning\",\n                children: \"\\u8B66\\u544A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"error\",\n                children: \"\\u9519\\u8BEF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"maintenance\",\n                children: \"\\u7EF4\\u62A4\\u4E2D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"location\",\n            label: \"\\u8BBE\\u5907\\u533A\\u57DF\",\n            rules: [{\n              required: true,\n              message: '请输入设备所在区域'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u6240\\u5728\\u533A\\u57DF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"entrance\",\n            label: \"\\u5B89\\u88C5\\u4F4D\\u7F6E\",\n            rules: [{\n              required: true,\n              message: '请选择安装位置'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u6240\\u5728\\u5B89\\u88C5\\u4F4D\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"ipAddress\",\n            label: \"IP\\u5730\\u5740\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165IP\\u5730\\u5740\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"manufacturer\",\n            label: \"\\u5236\\u9020\\u5546\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u5236\\u9020\\u5546\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"model\",\n            label: \"\\u578B\\u53F7\",\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u578B\\u53F7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"description\",\n            label: \"\\u8BBE\\u5907\\u63CF\\u8FF0\",\n            children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n              rows: 4,\n              placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u63CF\\u8FF0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Modal, {\n        title: \"\\u8BBE\\u5907\\u8BE6\\u60C5\",\n        open: detailModalVisible,\n        onCancel: () => setDetailModalVisible(false),\n        footer: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setDetailModalVisible(false),\n          children: \"\\u5173\\u95ED\"\n        }, \"close\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this)],\n        width: 600,\n        children: currentDevice && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BBE\\u5907ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 16\n            }, this), \" \", currentDevice.id]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BBE\\u5907\\u540D\\u79F0:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 16\n            }, this), \" \", currentDevice.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u8BBE\\u5907\\u7C7B\\u578B:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 16\n            }, this), \" \", deviceTypeMap[currentDevice.type] || currentDevice.type]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u72B6\\u6001:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 16\n            }, this), \" \", renderStatusTag(currentDevice.status)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u4F4D\\u7F6E:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 16\n            }, this), \" \", currentDevice.location]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"IP\\u5730\\u5740:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 16\n            }, this), \" \", currentDevice.ipAddress]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this), currentDevice.type === 'rsu' && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"MAC\\u5730\\u5740:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 18\n            }, this), \" \", currentDevice.mac || '未设置']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u6700\\u540E\\u7EF4\\u62A4\\u65F6\\u95F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 16\n            }, this), \" \", currentDevice.lastMaintenance ? new Date(currentDevice.lastMaintenance).toLocaleString() : '无记录']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5B89\\u88C5\\u65E5\\u671F:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 16\n            }, this), \" \", currentDevice.installationDate ? new Date(currentDevice.installationDate).toLocaleString() : '无记录']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u5236\\u9020\\u5546:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 16\n            }, this), \" \", currentDevice.manufacturer || '未知']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u578B\\u53F7:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 16\n            }, this), \" \", currentDevice.model || '未知']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\u63CF\\u8FF0:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 16\n            }, this), \" \", currentDevice.description || '无']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this), currentDevice.type === 'camera' && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"RTSP\\u5730\\u5740:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 18\n            }, this), \" \", currentDevice.rtspUrl || '未设置']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this)\n    // </div>\n  );\n};\n_s(DeviceManagement, \"PwbqkXC9LvByoMWAJcVxxDVyieU=\", false, function () {\n  return [Form.useForm];\n});\n_c2 = DeviceManagement;\nexport default _c3 = /*#__PURE__*/forwardRef(DeviceManagement);\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ContentArea\");\n$RefreshReg$(_c2, \"DeviceManagement\");\n$RefreshReg$(_c3, \"%default%\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "Space", "Card", "Tag", "message", "axios", "styled", "jsxDEV", "_jsxDEV", "Option", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ContentArea", "div", "_c", "DeviceManagement", "id", "ref", "_s", "devices", "setDevices", "loading", "setLoading", "modalVisible", "setModalVisible", "form", "useForm", "editingDevice", "setEditingDevice", "detailModalVisible", "setDetailModalVisible", "currentDevice", "setCurrentDevice", "currentDeviceType", "setCurrentDeviceType", "type", "filterType", "setFilterType", "filterLocation", "setFilterLocation", "filteredDevices", "setFilteredDevices", "fetchDevices", "response", "get", "data", "success", "Error", "error", "console", "handleFilter", "result", "filter", "item", "location", "handleResetFilter", "typeOptions", "Array", "from", "Set", "map", "d", "locationOptions", "handleAddDevice", "resetFields", "handleEditDevice", "device", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "status", "ip<PERSON><PERSON><PERSON>", "manufacturer", "model", "description", "rtspUrl", "entrance", "mac", "handleViewDevice", "handleDeleteDevice", "deviceId", "delete", "handleModalOk", "values", "validateFields", "log", "rtspInput", "document", "querySelector", "value", "getFieldValue", "JSON", "stringify", "put", "post", "handleDeviceTypeChange", "currentRtspUrl", "renderStatusTag", "statusMap", "online", "color", "text", "offline", "warning", "maintenance", "statusInfo", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "deviceTypeMap", "camera", "mmwave_radar", "lidar", "rsu", "edge_computing", "obu", "columns", "title", "dataIndex", "key", "render", "record", "_", "size", "onClick", "danger", "style", "display", "justifyContent", "marginBottom", "alignItems", "gap", "allowClear", "width", "placeholder", "undefined", "onChange", "loc", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onOk", "onCancel", "layout", "<PERSON><PERSON>", "label", "rules", "required", "initialValue", "TextArea", "autoSize", "minRows", "maxRows", "defaultValue", "e", "target", "pattern", "rows", "footer", "lastMaintenance", "Date", "toLocaleString", "installationDate", "_c2", "_c3", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/DeviceManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, Space, Card, Tag, message } from 'antd';\nimport axios from 'axios';\nimport styled from 'styled-components';\n\nconst { Option } = Select;\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconst ContentArea = styled.div`\n  // padding: 24px;\n  background: white;\n  position: relative;\n  z-index: 1002;\n`;\nconst DeviceManagement = ({ id }, ref) => {\n  const [devices, setDevices] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingDevice, setEditingDevice] = useState(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentDevice, setCurrentDevice] = useState(null);\n  const [currentDeviceType, setCurrentDeviceType] = useState(editingDevice?.type || null);\n  const [filterType, setFilterType] = useState('');\n  const [filterLocation, setFilterLocation] = useState('');\n  const [filteredDevices, setFilteredDevices] = useState([]);\n\n  // 获取设备列表\n  const fetchDevices = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/api/devices`);\n      if (response.data.success) {\n        setDevices(response.data.data);\n        setFilteredDevices(response.data.data);\n      } else {\n        throw new Error(response.data.message || '获取设备列表失败');\n      }\n    } catch (error) {\n      console.error('获取设备列表失败:', error);\n      message.error('获取设备列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchDevices();\n  }, []);\n\n  // 新增：筛选功能\n  const handleFilter = () => {\n    let result = [...devices];\n    if (filterType) {\n      result = result.filter(item => item.type === filterType);\n    }\n    if (filterLocation) {\n      result = result.filter(item => item.location === filterLocation);\n    }\n    setFilteredDevices(result);\n  };\n\n  // 新增：重置筛选\n  const handleResetFilter = () => {\n    setFilterType('');\n    setFilterLocation('');\n    setFilteredDevices(devices);\n  };\n\n  // 设备类型和区域选项自动生成\n  const typeOptions = Array.from(new Set(devices.map(d => d.type)));\n  const locationOptions = Array.from(new Set(devices.map(d => d.location)));\n\n  // 处理添加设备\n  const handleAddDevice = () => {\n    setEditingDevice(null);\n    setCurrentDeviceType(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // 处理编辑设备\n  const handleEditDevice = (device) => {\n    setEditingDevice(device);\n    setCurrentDeviceType(device.type);\n    form.setFieldsValue({\n      name: device.name,\n      type: device.type,\n      status: device.status,\n      location: device.location,\n      ipAddress: device.ipAddress,\n      manufacturer: device.manufacturer,\n      model: device.model,\n      description: device.description,\n      rtspUrl: device.rtspUrl,\n      entrance: device.entrance,\n      mac: device.mac // 添加MAC地址字段\n    });\n    setModalVisible(true);\n  };\n\n  // 处理查看设备详情\n  const handleViewDevice = (device) => {\n    setCurrentDevice(device);\n    setDetailModalVisible(true);\n  };\n\n  // 处理删除设备\n  const handleDeleteDevice = async (deviceId) => {\n    try {\n      const response = await axios.delete(`${API_BASE_URL}/api/devices/${deviceId}`);\n      if (response.data.success) {\n        message.success('设备删除成功');\n        fetchDevices(); // 重新获取设备列表\n      } else {\n        throw new Error(response.data.message || '删除设备失败');\n      }\n    } catch (error) {\n      console.error('删除设备失败:', error);\n      message.error('删除设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 处理表单提交\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n\n      // 为调试添加日志\n      console.log('表单验证通过，原始数据:', values);\n      console.log('当前设备类型:', currentDeviceType);\n\n      // 确保摄像头设备有 rtspUrl 字段\n      if (values.type === 'camera' || currentDeviceType === 'camera') {\n        // 直接从表单获取 rtspUrl 值，确保它不会丢失\n        const rtspInput = document.querySelector('textarea[placeholder*=\"RTSP\"]');\n        let rtspUrl = '';\n\n        if (rtspInput) {\n          rtspUrl = rtspInput.value || '';\n          console.log('从DOM元素获取的 RTSP 地址:', rtspUrl);\n        } else {\n          rtspUrl = form.getFieldValue('rtspUrl') || values.rtspUrl || '';\n          console.log('从表单获取的 RTSP 地址:', rtspUrl);\n        }\n\n        // 无论表单中是否有该字段，都强制设置 rtspUrl\n        values.rtspUrl = rtspUrl;\n        console.log('最终设置的 RTSP 地址:', rtspUrl);\n      } else {\n        // 对于非摄像头设备，确保移除 rtspUrl 字段\n        delete values.rtspUrl;\n        console.log('非摄像头设备，移除 rtspUrl 字段');\n      }\n\n      console.log('提交表单最终数据:', JSON.stringify(values, null, 2));\n\n      if (editingDevice) {\n        // 编辑设备\n        const response = await axios.put(`${API_BASE_URL}/api/devices/${editingDevice.id}`, values);\n        if (response.data.success) {\n          message.success('设备更新成功');\n          setModalVisible(false);\n          fetchDevices(); // 重新获取设备列表\n        } else {\n          throw new Error(response.data.message || '更新设备失败');\n        }\n      } else {\n        // 添加设备\n        const response = await axios.post(`${API_BASE_URL}/api/devices`, values);\n        if (response.data.success) {\n          message.success('设备添加成功');\n          setModalVisible(false);\n          fetchDevices(); // 重新获取设备列表\n        } else {\n          throw new Error(response.data.message || '添加设备失败');\n        }\n      }\n    } catch (error) {\n      console.error('保存设备失败:', error);\n      message.error('保存设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 修改设备类型选择的处理函数\n  const handleDeviceTypeChange = (value) => {\n    console.log('设备类型变更为:', value);\n    setCurrentDeviceType(value);\n\n    if (value === 'camera') {\n      // 如果是摄像头，确保表单中有 rtspUrl 字段\n      const currentRtspUrl = form.getFieldValue('rtspUrl') || '';\n      form.setFieldsValue({\n        type: value,\n        rtspUrl: currentRtspUrl\n      });\n      console.log('设置摄像头 RTSP 初始值:', currentRtspUrl);\n    } else {\n      // 如果不是摄像头，移除 rtspUrl 字段\n      form.setFieldsValue({ type: value });\n      form.resetFields(['rtspUrl']);\n      console.log('重置 RTSP 字段');\n    }\n  };\n\n  // 渲染状态标签\n  const renderStatusTag = (status) => {\n    const statusMap = {\n      online: { color: 'green', text: '在线' },\n      offline: { color: 'gray', text: '离线' },\n      warning: { color: 'orange', text: '警告' },\n      error: { color: 'red', text: '错误' },\n      maintenance: { color: 'blue', text: '维护中' }\n    };\n\n    const statusInfo = statusMap[status] || { color: 'default', text: status };\n    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;\n  };\n\n  // 设备类型映射\n  const deviceTypeMap = {\n    camera: '摄像头',\n    mmwave_radar: '毫米波雷达',\n    lidar: '激光雷达',\n    rsu: 'RSU',\n    edge_computing: '边缘计算单元',\n    obu: 'OBU'\n  };\n\n  // 表格列定义\n  const columns = [\n    {\n      title: '设备名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '设备类型',\n      dataIndex: 'type',\n      key: 'type',\n      render: (type) => deviceTypeMap[type] || type\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: renderStatusTag\n    },\n    {\n      title: '所在区域',\n      dataIndex: 'location',\n      key: 'location',\n    },\n    {\n      title: 'IP地址',\n      dataIndex: 'ipAddress',\n      key: 'ipAddress',\n    },\n    {\n      title: 'MAC地址',\n      dataIndex: 'mac',\n      key: 'mac',\n      render: (mac, record) => {\n        // 只有RSU设备才显示MAC地址\n        return record.type === 'rsu' ? (mac || '-') : '-';\n      }\n    },\n    {\n      title: '安装位置',\n      dataIndex: 'entrance',\n      key: 'entrance',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button type=\"link\" onClick={() => handleViewDevice(record)}>查看</Button>\n          <Button type=\"link\" onClick={() => handleEditDevice(record)}>编辑</Button>\n          <Button type=\"link\" danger onClick={() => handleDeleteDevice(record.id)}>删除</Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 暴露 fetchDevices 方法\n  useImperativeHandle(ref, () => ({\n    fetchDevices\n  }));\n\n  return (\n    // <div id={id}>\n      <ContentArea>\n        {/* 新增：筛选区域 */}\n        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16, alignItems: 'center' }}>\n          <div style={{ display: 'flex', gap: 8 }}>\n            {/* 设备类型筛选下拉框 */}\n            <Select\n              allowClear\n              style={{ width: 120 }}\n              placeholder=\"筛选类型\"\n              value={filterType || undefined}\n              onChange={value => setFilterType(value)}\n            >\n              {typeOptions.map(type => (\n                <Option key={type} value={type}>{deviceTypeMap[type] || type}</Option>\n              ))}\n            </Select>\n            {/* 设备区域筛选下拉框 */}\n            <Select\n              allowClear\n              style={{ width: 120 }}\n              placeholder=\"筛选区域\"\n              value={filterLocation || undefined}\n              onChange={value => setFilterLocation(value)}\n            >\n              {locationOptions.map(loc => (\n                <Option key={loc} value={loc}>{loc}</Option>\n              ))}\n            </Select>\n            {/* 查询按钮 */}\n            <Button type=\"primary\" onClick={handleFilter}>查询</Button>\n            {/* 重置按钮 */}\n            <Button onClick={handleResetFilter}>重置</Button>\n          </div>\n          <Button\n            type=\"primary\"\n            onClick={handleAddDevice}\n          >\n            添加设备\n          </Button>\n        </div>\n        <Table\n          loading={loading}\n          dataSource={filteredDevices}\n          columns={columns}\n          rowKey=\"id\"\n        />\n\n\n\n      {/* 添加/编辑设备表单 */}\n      <Modal\n        title={editingDevice ? '编辑设备' : '添加设备'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => setModalVisible(false)}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"设备名称\"\n            rules={[{ required: true, message: '请输入设备名称' }]}\n          >\n            <Input placeholder=\"请输入设备名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"type\"\n            label=\"设备类型\"\n            rules={[{ required: true, message: '请选择设备类型' }]}\n          >\n            <Select\n              placeholder=\"请选择设备类型\"\n              onChange={handleDeviceTypeChange}\n            >\n              <Option value=\"camera\">摄像头</Option>\n              <Option value=\"mmwave_radar\">毫米波雷达</Option>\n              <Option value=\"lidar\">激光雷达</Option>\n              <Option value=\"rsu\">RSU</Option>\n              <Option value=\"edge_computing\">边缘计算单元</Option>\n              <Option value=\"obu\">OBU</Option>\n            </Select>\n          </Form.Item>\n\n          {/* 当设备类型为摄像头时显示 RTSP 地址输入 */}\n          {currentDeviceType === 'camera' && (\n            <Form.Item\n              name=\"rtspUrl\"\n              label=\"RTSP地址\"\n              initialValue=\"\"\n            >\n              <Input.TextArea\n                id=\"rtspUrlInput\"\n                placeholder=\"请输入RTSP地址，例如：rtsp://admin:password@192.168.1.100:554/stream1\"\n                autoSize={{ minRows: 1, maxRows: 3 }}\n                defaultValue=\"\"\n                onChange={(e) => {\n                  // 将输入的 RTSP 地址保存到表单\n                  form.setFieldsValue({ rtspUrl: e.target.value });\n                  console.log('RTSP 地址输入变化:', e.target.value);\n                }}\n              />\n            </Form.Item>\n          )}\n\n          {/* 当设备类型为RSU时显示 MAC 地址输入 */}\n          {currentDeviceType === 'rsu' && (\n            <Form.Item\n              name=\"mac\"\n              label=\"MAC地址\"\n              rules={[\n                {\n                  pattern: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,\n                  message: '请输入正确的MAC地址格式，例如：AA:BB:CC:DD:EE:FF'\n                }\n              ]}\n            >\n              <Input\n                placeholder=\"请输入MAC地址，例如：AA:BB:CC:DD:EE:FF\"\n                onChange={(e) => {\n                  // 将输入的 MAC 地址保存到表单\n                  form.setFieldsValue({ mac: e.target.value });\n                  console.log('MAC 地址输入变化:', e.target.value);\n                }}\n              />\n            </Form.Item>\n          )}\n\n          <Form.Item\n            name=\"status\"\n            label=\"设备状态\"\n            rules={[{ required: true, message: '请选择设备状态' }]}\n          >\n            <Select placeholder=\"请选择设备状态\">\n              <Option value=\"online\">在线</Option>\n              <Option value=\"offline\">离线</Option>\n              <Option value=\"warning\">警告</Option>\n              <Option value=\"error\">错误</Option>\n              <Option value=\"maintenance\">维护中</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"location\"\n            label=\"设备区域\"\n            rules={[{ required: true, message: '请输入设备所在区域' }]}\n          >\n            <Input placeholder=\"请输入设备所在区域\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"entrance\"\n            label=\"安装位置\"\n            rules={[{ required: true, message: '请选择安装位置' }]}\n          >\n            <Input placeholder=\"请输入设备所在安装位置\" />\n            {/* <Select placeholder=\"请选择安装进口\">\n              <Option value=\"东进口\">东进口</Option>\n              <Option value=\"西进口\">西进口</Option>\n              <Option value=\"北进口\">北进口</Option>\n              <Option value=\"南进口\">南进口</Option>\n            </Select> */}\n          </Form.Item>\n\n          <Form.Item\n            name=\"ipAddress\"\n            label=\"IP地址\"\n          >\n            <Input placeholder=\"请输入IP地址\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"manufacturer\"\n            label=\"制造商\"\n          >\n            <Input placeholder=\"请输入制造商\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"model\"\n            label=\"型号\"\n          >\n            <Input placeholder=\"请输入型号\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"设备描述\"\n          >\n            <Input.TextArea rows={4} placeholder=\"请输入设备描述\" />\n          </Form.Item>\n\n\n        </Form>\n      </Modal>\n\n      {/* 设备详情模态框 */}\n      <Modal\n        title=\"设备详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={600}\n      >\n        {currentDevice && (\n          <div>\n            <p><strong>设备ID:</strong> {currentDevice.id}</p>\n            <p><strong>设备名称:</strong> {currentDevice.name}</p>\n            <p><strong>设备类型:</strong> {deviceTypeMap[currentDevice.type] || currentDevice.type}</p>\n            <p><strong>状态:</strong> {renderStatusTag(currentDevice.status)}</p>\n            <p><strong>位置:</strong> {currentDevice.location}</p>\n            <p><strong>IP地址:</strong> {currentDevice.ipAddress}</p>\n            {currentDevice.type === 'rsu' && (\n              <p><strong>MAC地址:</strong> {currentDevice.mac || '未设置'}</p>\n            )}\n            <p><strong>最后维护时间:</strong> {currentDevice.lastMaintenance ? new Date(currentDevice.lastMaintenance).toLocaleString() : '无记录'}</p>\n            <p><strong>安装日期:</strong> {currentDevice.installationDate ? new Date(currentDevice.installationDate).toLocaleString() : '无记录'}</p>\n            <p><strong>制造商:</strong> {currentDevice.manufacturer || '未知'}</p>\n            <p><strong>型号:</strong> {currentDevice.model || '未知'}</p>\n            <p><strong>描述:</strong> {currentDevice.description || '无'}</p>\n            {currentDevice.type === 'camera' && (\n              <p><strong>RTSP地址:</strong> {currentDevice.rtspUrl || '未设置'}</p>\n            )}\n          </div>\n        )}\n      </Modal>\n      </ContentArea>\n    // </div>\n  );\n};\n\nexport default forwardRef(DeviceManagement);"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACnF,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,OAAO,QAAQ,MAAM;AAC3F,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAM;EAAEC;AAAO,CAAC,GAAGT,MAAM;AACzB,MAAMU,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAC7E,MAAMC,WAAW,GAAGR,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,WAAW;AAMjB,MAAMG,gBAAgB,GAAGA,CAAC;EAAEC;AAAG,CAAC,EAAEC,GAAG,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoC,IAAI,CAAC,GAAG7B,IAAI,CAAC8B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACwC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC4C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7C,QAAQ,CAAC,CAAAsC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEQ,IAAI,KAAI,IAAI,CAAC;EACvF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAMqD,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFpB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqB,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,GAAG,CAAC,GAAGpC,YAAY,cAAc,CAAC;MAC/D,IAAImC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1B,UAAU,CAACuB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;QAC9BJ,kBAAkB,CAACE,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MACxC,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAAC3C,OAAO,IAAI,UAAU,CAAC;MACtD;IACF,CAAC,CAAC,OAAO8C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC9C,OAAO,CAAC8C,KAAK,CAAC,YAAY,IAAIA,KAAK,CAAC9C,OAAO,IAAI,MAAM,CAAC,CAAC;IACzD,CAAC,SAAS;MACRoB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDhC,SAAS,CAAC,MAAM;IACdoD,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,MAAM,GAAG,CAAC,GAAGhC,OAAO,CAAC;IACzB,IAAIiB,UAAU,EAAE;MACde,MAAM,GAAGA,MAAM,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAClB,IAAI,KAAKC,UAAU,CAAC;IAC1D;IACA,IAAIE,cAAc,EAAE;MAClBa,MAAM,GAAGA,MAAM,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,KAAKhB,cAAc,CAAC;IAClE;IACAG,kBAAkB,CAACU,MAAM,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMI,iBAAiB,GAAGA,CAAA,KAAM;IAC9BlB,aAAa,CAAC,EAAE,CAAC;IACjBE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,kBAAkB,CAACtB,OAAO,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMqC,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACxC,OAAO,CAACyC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC1B,IAAI,CAAC,CAAC,CAAC;EACjE,MAAM2B,eAAe,GAAGL,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACxC,OAAO,CAACyC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACP,QAAQ,CAAC,CAAC,CAAC;;EAEzE;EACA,MAAMS,eAAe,GAAGA,CAAA,KAAM;IAC5BnC,gBAAgB,CAAC,IAAI,CAAC;IACtBM,oBAAoB,CAAC,IAAI,CAAC;IAC1BT,IAAI,CAACuC,WAAW,CAAC,CAAC;IAClBxC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMyC,gBAAgB,GAAIC,MAAM,IAAK;IACnCtC,gBAAgB,CAACsC,MAAM,CAAC;IACxBhC,oBAAoB,CAACgC,MAAM,CAAC/B,IAAI,CAAC;IACjCV,IAAI,CAAC0C,cAAc,CAAC;MAClBC,IAAI,EAAEF,MAAM,CAACE,IAAI;MACjBjC,IAAI,EAAE+B,MAAM,CAAC/B,IAAI;MACjBkC,MAAM,EAAEH,MAAM,CAACG,MAAM;MACrBf,QAAQ,EAAEY,MAAM,CAACZ,QAAQ;MACzBgB,SAAS,EAAEJ,MAAM,CAACI,SAAS;MAC3BC,YAAY,EAAEL,MAAM,CAACK,YAAY;MACjCC,KAAK,EAAEN,MAAM,CAACM,KAAK;MACnBC,WAAW,EAAEP,MAAM,CAACO,WAAW;MAC/BC,OAAO,EAAER,MAAM,CAACQ,OAAO;MACvBC,QAAQ,EAAET,MAAM,CAACS,QAAQ;MACzBC,GAAG,EAAEV,MAAM,CAACU,GAAG,CAAC;IAClB,CAAC,CAAC;IACFpD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMqD,gBAAgB,GAAIX,MAAM,IAAK;IACnClC,gBAAgB,CAACkC,MAAM,CAAC;IACxBpC,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMgD,kBAAkB,GAAG,MAAOC,QAAQ,IAAK;IAC7C,IAAI;MACF,MAAMpC,QAAQ,GAAG,MAAMxC,KAAK,CAAC6E,MAAM,CAAC,GAAGxE,YAAY,gBAAgBuE,QAAQ,EAAE,CAAC;MAC9E,IAAIpC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB5C,OAAO,CAAC4C,OAAO,CAAC,QAAQ,CAAC;QACzBJ,YAAY,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,MAAM;QACL,MAAM,IAAIK,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAAC3C,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC,OAAO8C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B9C,OAAO,CAAC8C,KAAK,CAAC,UAAU,IAAIA,KAAK,CAAC9C,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAM+E,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMzD,IAAI,CAAC0D,cAAc,CAAC,CAAC;;MAE1C;MACAlC,OAAO,CAACmC,GAAG,CAAC,cAAc,EAAEF,MAAM,CAAC;MACnCjC,OAAO,CAACmC,GAAG,CAAC,SAAS,EAAEnD,iBAAiB,CAAC;;MAEzC;MACA,IAAIiD,MAAM,CAAC/C,IAAI,KAAK,QAAQ,IAAIF,iBAAiB,KAAK,QAAQ,EAAE;QAC9D;QACA,MAAMoD,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,+BAA+B,CAAC;QACzE,IAAIb,OAAO,GAAG,EAAE;QAEhB,IAAIW,SAAS,EAAE;UACbX,OAAO,GAAGW,SAAS,CAACG,KAAK,IAAI,EAAE;UAC/BvC,OAAO,CAACmC,GAAG,CAAC,oBAAoB,EAAEV,OAAO,CAAC;QAC5C,CAAC,MAAM;UACLA,OAAO,GAAGjD,IAAI,CAACgE,aAAa,CAAC,SAAS,CAAC,IAAIP,MAAM,CAACR,OAAO,IAAI,EAAE;UAC/DzB,OAAO,CAACmC,GAAG,CAAC,iBAAiB,EAAEV,OAAO,CAAC;QACzC;;QAEA;QACAQ,MAAM,CAACR,OAAO,GAAGA,OAAO;QACxBzB,OAAO,CAACmC,GAAG,CAAC,gBAAgB,EAAEV,OAAO,CAAC;MACxC,CAAC,MAAM;QACL;QACA,OAAOQ,MAAM,CAACR,OAAO;QACrBzB,OAAO,CAACmC,GAAG,CAAC,sBAAsB,CAAC;MACrC;MAEAnC,OAAO,CAACmC,GAAG,CAAC,WAAW,EAAEM,IAAI,CAACC,SAAS,CAACT,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAEzD,IAAIvD,aAAa,EAAE;QACjB;QACA,MAAMgB,QAAQ,GAAG,MAAMxC,KAAK,CAACyF,GAAG,CAAC,GAAGpF,YAAY,gBAAgBmB,aAAa,CAACX,EAAE,EAAE,EAAEkE,MAAM,CAAC;QAC3F,IAAIvC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzB5C,OAAO,CAAC4C,OAAO,CAAC,QAAQ,CAAC;UACzBtB,eAAe,CAAC,KAAK,CAAC;UACtBkB,YAAY,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,MAAM;UACL,MAAM,IAAIK,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAAC3C,OAAO,IAAI,QAAQ,CAAC;QACpD;MACF,CAAC,MAAM;QACL;QACA,MAAMyC,QAAQ,GAAG,MAAMxC,KAAK,CAAC0F,IAAI,CAAC,GAAGrF,YAAY,cAAc,EAAE0E,MAAM,CAAC;QACxE,IAAIvC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzB5C,OAAO,CAAC4C,OAAO,CAAC,QAAQ,CAAC;UACzBtB,eAAe,CAAC,KAAK,CAAC;UACtBkB,YAAY,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,MAAM;UACL,MAAM,IAAIK,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAAC3C,OAAO,IAAI,QAAQ,CAAC;QACpD;MACF;IACF,CAAC,CAAC,OAAO8C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B9C,OAAO,CAAC8C,KAAK,CAAC,UAAU,IAAIA,KAAK,CAAC9C,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAM4F,sBAAsB,GAAIN,KAAK,IAAK;IACxCvC,OAAO,CAACmC,GAAG,CAAC,UAAU,EAAEI,KAAK,CAAC;IAC9BtD,oBAAoB,CAACsD,KAAK,CAAC;IAE3B,IAAIA,KAAK,KAAK,QAAQ,EAAE;MACtB;MACA,MAAMO,cAAc,GAAGtE,IAAI,CAACgE,aAAa,CAAC,SAAS,CAAC,IAAI,EAAE;MAC1DhE,IAAI,CAAC0C,cAAc,CAAC;QAClBhC,IAAI,EAAEqD,KAAK;QACXd,OAAO,EAAEqB;MACX,CAAC,CAAC;MACF9C,OAAO,CAACmC,GAAG,CAAC,iBAAiB,EAAEW,cAAc,CAAC;IAChD,CAAC,MAAM;MACL;MACAtE,IAAI,CAAC0C,cAAc,CAAC;QAAEhC,IAAI,EAAEqD;MAAM,CAAC,CAAC;MACpC/D,IAAI,CAACuC,WAAW,CAAC,CAAC,SAAS,CAAC,CAAC;MAC7Bf,OAAO,CAACmC,GAAG,CAAC,YAAY,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMY,eAAe,GAAI3B,MAAM,IAAK;IAClC,MAAM4B,SAAS,GAAG;MAChBC,MAAM,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAK,CAAC;MACtCC,OAAO,EAAE;QAAEF,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAK,CAAC;MACtCE,OAAO,EAAE;QAAEH,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAK,CAAC;MACxCpD,KAAK,EAAE;QAAEmD,KAAK,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAK,CAAC;MACnCG,WAAW,EAAE;QAAEJ,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM;IAC5C,CAAC;IAED,MAAMI,UAAU,GAAGP,SAAS,CAAC5B,MAAM,CAAC,IAAI;MAAE8B,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE/B;IAAO,CAAC;IAC1E,oBAAO/D,OAAA,CAACL,GAAG;MAACkG,KAAK,EAAEK,UAAU,CAACL,KAAM;MAAAM,QAAA,EAAED,UAAU,CAACJ;IAAI;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG;IACpBC,MAAM,EAAE,KAAK;IACbC,YAAY,EAAE,OAAO;IACrBC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,KAAK;IACVC,cAAc,EAAE,QAAQ;IACxBC,GAAG,EAAE;EACP,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGtF,IAAI,IAAK2E,aAAa,CAAC3E,IAAI,CAAC,IAAIA;EAC3C,CAAC,EACD;IACEmF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEzB;EACV,CAAC,EACD;IACEsB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,KAAK;IAChBC,GAAG,EAAE,KAAK;IACVC,MAAM,EAAEA,CAAC7C,GAAG,EAAE8C,MAAM,KAAK;MACvB;MACA,OAAOA,MAAM,CAACvF,IAAI,KAAK,KAAK,GAAIyC,GAAG,IAAI,GAAG,GAAI,GAAG;IACnD;EACF,CAAC,EACD;IACE0C,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACE,CAAC,EAAED,MAAM,kBAChBpH,OAAA,CAACP,KAAK;MAAC6H,IAAI,EAAC,OAAO;MAAAnB,QAAA,gBACjBnG,OAAA,CAACZ,MAAM;QAACyC,IAAI,EAAC,MAAM;QAAC0F,OAAO,EAAEA,CAAA,KAAMhD,gBAAgB,CAAC6C,MAAM,CAAE;QAAAjB,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxEvG,OAAA,CAACZ,MAAM;QAACyC,IAAI,EAAC,MAAM;QAAC0F,OAAO,EAAEA,CAAA,KAAM5D,gBAAgB,CAACyD,MAAM,CAAE;QAAAjB,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxEvG,OAAA,CAACZ,MAAM;QAACyC,IAAI,EAAC,MAAM;QAAC2F,MAAM;QAACD,OAAO,EAAEA,CAAA,KAAM/C,kBAAkB,CAAC4C,MAAM,CAAC1G,EAAE,CAAE;QAAAyF,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E;EAEX,CAAC,CACF;;EAED;EACArH,mBAAmB,CAACyB,GAAG,EAAE,OAAO;IAC9ByB;EACF,CAAC,CAAC,CAAC;EAEH;IAAA;IACE;IACEpC,OAAA,CAACM,WAAW;MAAA6F,QAAA,gBAEVnG,OAAA;QAAKyH,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,YAAY,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAA1B,QAAA,gBACvGnG,OAAA;UAAKyH,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEI,GAAG,EAAE;UAAE,CAAE;UAAA3B,QAAA,gBAEtCnG,OAAA,CAACR,MAAM;YACLuI,UAAU;YACVN,KAAK,EAAE;cAAEO,KAAK,EAAE;YAAI,CAAE;YACtBC,WAAW,EAAC,0BAAM;YAClB/C,KAAK,EAAEpD,UAAU,IAAIoG,SAAU;YAC/BC,QAAQ,EAAEjD,KAAK,IAAInD,aAAa,CAACmD,KAAK,CAAE;YAAAiB,QAAA,EAEvCjD,WAAW,CAACI,GAAG,CAACzB,IAAI,iBACnB7B,OAAA,CAACC,MAAM;cAAYiF,KAAK,EAAErD,IAAK;cAAAsE,QAAA,EAAEK,aAAa,CAAC3E,IAAI,CAAC,IAAIA;YAAI,GAA/CA,IAAI;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoD,CACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAETvG,OAAA,CAACR,MAAM;YACLuI,UAAU;YACVN,KAAK,EAAE;cAAEO,KAAK,EAAE;YAAI,CAAE;YACtBC,WAAW,EAAC,0BAAM;YAClB/C,KAAK,EAAElD,cAAc,IAAIkG,SAAU;YACnCC,QAAQ,EAAEjD,KAAK,IAAIjD,iBAAiB,CAACiD,KAAK,CAAE;YAAAiB,QAAA,EAE3C3C,eAAe,CAACF,GAAG,CAAC8E,GAAG,iBACtBpI,OAAA,CAACC,MAAM;cAAWiF,KAAK,EAAEkD,GAAI;cAAAjC,QAAA,EAAEiC;YAAG,GAArBA,GAAG;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA2B,CAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAETvG,OAAA,CAACZ,MAAM;YAACyC,IAAI,EAAC,SAAS;YAAC0F,OAAO,EAAE3E,YAAa;YAAAuD,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAEzDvG,OAAA,CAACZ,MAAM;YAACmI,OAAO,EAAEtE,iBAAkB;YAAAkD,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACNvG,OAAA,CAACZ,MAAM;UACLyC,IAAI,EAAC,SAAS;UACd0F,OAAO,EAAE9D,eAAgB;UAAA0C,QAAA,EAC1B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNvG,OAAA,CAACb,KAAK;QACJ4B,OAAO,EAAEA,OAAQ;QACjBsH,UAAU,EAAEnG,eAAgB;QAC5B6E,OAAO,EAAEA,OAAQ;QACjBuB,MAAM,EAAC;MAAI;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAKJvG,OAAA,CAACX,KAAK;QACJ2H,KAAK,EAAE3F,aAAa,GAAG,MAAM,GAAG,MAAO;QACvCkH,IAAI,EAAEtH,YAAa;QACnBuH,IAAI,EAAE7D,aAAc;QACpB8D,QAAQ,EAAEA,CAAA,KAAMvH,eAAe,CAAC,KAAK,CAAE;QACvC8G,KAAK,EAAE,GAAI;QAAA7B,QAAA,eAEXnG,OAAA,CAACV,IAAI;UACH6B,IAAI,EAAEA,IAAK;UACXuH,MAAM,EAAC,UAAU;UAAAvC,QAAA,gBAEjBnG,OAAA,CAACV,IAAI,CAACqJ,IAAI;YACR7E,IAAI,EAAC,MAAM;YACX8E,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAElJ,OAAO,EAAE;YAAU,CAAC,CAAE;YAAAuG,QAAA,eAEhDnG,OAAA,CAACT,KAAK;cAAC0I,WAAW,EAAC;YAAS;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAEZvG,OAAA,CAACV,IAAI,CAACqJ,IAAI;YACR7E,IAAI,EAAC,MAAM;YACX8E,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAElJ,OAAO,EAAE;YAAU,CAAC,CAAE;YAAAuG,QAAA,eAEhDnG,OAAA,CAACR,MAAM;cACLyI,WAAW,EAAC,4CAAS;cACrBE,QAAQ,EAAE3C,sBAAuB;cAAAW,QAAA,gBAEjCnG,OAAA,CAACC,MAAM;gBAACiF,KAAK,EAAC,QAAQ;gBAAAiB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCvG,OAAA,CAACC,MAAM;gBAACiF,KAAK,EAAC,cAAc;gBAAAiB,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC3CvG,OAAA,CAACC,MAAM;gBAACiF,KAAK,EAAC,OAAO;gBAAAiB,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCvG,OAAA,CAACC,MAAM;gBAACiF,KAAK,EAAC,KAAK;gBAAAiB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChCvG,OAAA,CAACC,MAAM;gBAACiF,KAAK,EAAC,gBAAgB;gBAAAiB,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC9CvG,OAAA,CAACC,MAAM;gBAACiF,KAAK,EAAC,KAAK;gBAAAiB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EAGX5E,iBAAiB,KAAK,QAAQ,iBAC7B3B,OAAA,CAACV,IAAI,CAACqJ,IAAI;YACR7E,IAAI,EAAC,SAAS;YACd8E,KAAK,EAAC,kBAAQ;YACdG,YAAY,EAAC,EAAE;YAAA5C,QAAA,eAEfnG,OAAA,CAACT,KAAK,CAACyJ,QAAQ;cACbtI,EAAE,EAAC,cAAc;cACjBuH,WAAW,EAAC,2GAA8D;cAC1EgB,QAAQ,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,OAAO,EAAE;cAAE,CAAE;cACrCC,YAAY,EAAC,EAAE;cACfjB,QAAQ,EAAGkB,CAAC,IAAK;gBACf;gBACAlI,IAAI,CAAC0C,cAAc,CAAC;kBAAEO,OAAO,EAAEiF,CAAC,CAACC,MAAM,CAACpE;gBAAM,CAAC,CAAC;gBAChDvC,OAAO,CAACmC,GAAG,CAAC,cAAc,EAAEuE,CAAC,CAACC,MAAM,CAACpE,KAAK,CAAC;cAC7C;YAAE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CACZ,EAGA5E,iBAAiB,KAAK,KAAK,iBAC1B3B,OAAA,CAACV,IAAI,CAACqJ,IAAI;YACR7E,IAAI,EAAC,KAAK;YACV8E,KAAK,EAAC,iBAAO;YACbC,KAAK,EAAE,CACL;cACEU,OAAO,EAAE,2CAA2C;cACpD3J,OAAO,EAAE;YACX,CAAC,CACD;YAAAuG,QAAA,eAEFnG,OAAA,CAACT,KAAK;cACJ0I,WAAW,EAAC,4EAA+B;cAC3CE,QAAQ,EAAGkB,CAAC,IAAK;gBACf;gBACAlI,IAAI,CAAC0C,cAAc,CAAC;kBAAES,GAAG,EAAE+E,CAAC,CAACC,MAAM,CAACpE;gBAAM,CAAC,CAAC;gBAC5CvC,OAAO,CAACmC,GAAG,CAAC,aAAa,EAAEuE,CAAC,CAACC,MAAM,CAACpE,KAAK,CAAC;cAC5C;YAAE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CACZ,eAEDvG,OAAA,CAACV,IAAI,CAACqJ,IAAI;YACR7E,IAAI,EAAC,QAAQ;YACb8E,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAElJ,OAAO,EAAE;YAAU,CAAC,CAAE;YAAAuG,QAAA,eAEhDnG,OAAA,CAACR,MAAM;cAACyI,WAAW,EAAC,4CAAS;cAAA9B,QAAA,gBAC3BnG,OAAA,CAACC,MAAM;gBAACiF,KAAK,EAAC,QAAQ;gBAAAiB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCvG,OAAA,CAACC,MAAM;gBAACiF,KAAK,EAAC,SAAS;gBAAAiB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCvG,OAAA,CAACC,MAAM;gBAACiF,KAAK,EAAC,SAAS;gBAAAiB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACnCvG,OAAA,CAACC,MAAM;gBAACiF,KAAK,EAAC,OAAO;gBAAAiB,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACjCvG,OAAA,CAACC,MAAM;gBAACiF,KAAK,EAAC,aAAa;gBAAAiB,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZvG,OAAA,CAACV,IAAI,CAACqJ,IAAI;YACR7E,IAAI,EAAC,UAAU;YACf8E,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAElJ,OAAO,EAAE;YAAY,CAAC,CAAE;YAAAuG,QAAA,eAElDnG,OAAA,CAACT,KAAK;cAAC0I,WAAW,EAAC;YAAW;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eAEZvG,OAAA,CAACV,IAAI,CAACqJ,IAAI;YACR7E,IAAI,EAAC,UAAU;YACf8E,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAElJ,OAAO,EAAE;YAAU,CAAC,CAAE;YAAAuG,QAAA,eAEhDnG,OAAA,CAACT,KAAK;cAAC0I,WAAW,EAAC;YAAa;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAO1B,CAAC,eAEZvG,OAAA,CAACV,IAAI,CAACqJ,IAAI;YACR7E,IAAI,EAAC,WAAW;YAChB8E,KAAK,EAAC,gBAAM;YAAAzC,QAAA,eAEZnG,OAAA,CAACT,KAAK;cAAC0I,WAAW,EAAC;YAAS;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAEZvG,OAAA,CAACV,IAAI,CAACqJ,IAAI;YACR7E,IAAI,EAAC,cAAc;YACnB8E,KAAK,EAAC,oBAAK;YAAAzC,QAAA,eAEXnG,OAAA,CAACT,KAAK;cAAC0I,WAAW,EAAC;YAAQ;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAEZvG,OAAA,CAACV,IAAI,CAACqJ,IAAI;YACR7E,IAAI,EAAC,OAAO;YACZ8E,KAAK,EAAC,cAAI;YAAAzC,QAAA,eAEVnG,OAAA,CAACT,KAAK;cAAC0I,WAAW,EAAC;YAAO;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eAEZvG,OAAA,CAACV,IAAI,CAACqJ,IAAI;YACR7E,IAAI,EAAC,aAAa;YAClB8E,KAAK,EAAC,0BAAM;YAAAzC,QAAA,eAEZnG,OAAA,CAACT,KAAK,CAACyJ,QAAQ;cAACQ,IAAI,EAAE,CAAE;cAACvB,WAAW,EAAC;YAAS;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGRvG,OAAA,CAACX,KAAK;QACJ2H,KAAK,EAAC,0BAAM;QACZuB,IAAI,EAAEhH,kBAAmB;QACzBkH,QAAQ,EAAEA,CAAA,KAAMjH,qBAAqB,CAAC,KAAK,CAAE;QAC7CiI,MAAM,EAAE,cACNzJ,OAAA,CAACZ,MAAM;UAAamI,OAAO,EAAEA,CAAA,KAAM/F,qBAAqB,CAAC,KAAK,CAAE;UAAA2E,QAAA,EAAC;QAEjE,GAFY,OAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEX,CAAC,CACT;QACFyB,KAAK,EAAE,GAAI;QAAA7B,QAAA,EAEV1E,aAAa,iBACZzB,OAAA;UAAAmG,QAAA,gBACEnG,OAAA;YAAAmG,QAAA,gBAAGnG,OAAA;cAAAmG,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9E,aAAa,CAACf,EAAE;UAAA;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDvG,OAAA;YAAAmG,QAAA,gBAAGnG,OAAA;cAAAmG,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9E,aAAa,CAACqC,IAAI;UAAA;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClDvG,OAAA;YAAAmG,QAAA,gBAAGnG,OAAA;cAAAmG,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACC,aAAa,CAAC/E,aAAa,CAACI,IAAI,CAAC,IAAIJ,aAAa,CAACI,IAAI;UAAA;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvFvG,OAAA;YAAAmG,QAAA,gBAAGnG,OAAA;cAAAmG,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACb,eAAe,CAACjE,aAAa,CAACsC,MAAM,CAAC;UAAA;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnEvG,OAAA;YAAAmG,QAAA,gBAAGnG,OAAA;cAAAmG,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9E,aAAa,CAACuB,QAAQ;UAAA;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpDvG,OAAA;YAAAmG,QAAA,gBAAGnG,OAAA;cAAAmG,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9E,aAAa,CAACuC,SAAS;UAAA;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACtD9E,aAAa,CAACI,IAAI,KAAK,KAAK,iBAC3B7B,OAAA;YAAAmG,QAAA,gBAAGnG,OAAA;cAAAmG,QAAA,EAAQ;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9E,aAAa,CAAC6C,GAAG,IAAI,KAAK;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC3D,eACDvG,OAAA;YAAAmG,QAAA,gBAAGnG,OAAA;cAAAmG,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9E,aAAa,CAACiI,eAAe,GAAG,IAAIC,IAAI,CAAClI,aAAa,CAACiI,eAAe,CAAC,CAACE,cAAc,CAAC,CAAC,GAAG,KAAK;UAAA;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClIvG,OAAA;YAAAmG,QAAA,gBAAGnG,OAAA;cAAAmG,QAAA,EAAQ;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9E,aAAa,CAACoI,gBAAgB,GAAG,IAAIF,IAAI,CAAClI,aAAa,CAACoI,gBAAgB,CAAC,CAACD,cAAc,CAAC,CAAC,GAAG,KAAK;UAAA;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClIvG,OAAA;YAAAmG,QAAA,gBAAGnG,OAAA;cAAAmG,QAAA,EAAQ;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9E,aAAa,CAACwC,YAAY,IAAI,IAAI;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjEvG,OAAA;YAAAmG,QAAA,gBAAGnG,OAAA;cAAAmG,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9E,aAAa,CAACyC,KAAK,IAAI,IAAI;UAAA;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzDvG,OAAA;YAAAmG,QAAA,gBAAGnG,OAAA;cAAAmG,QAAA,EAAQ;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9E,aAAa,CAAC0C,WAAW,IAAI,GAAG;UAAA;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC7D9E,aAAa,CAACI,IAAI,KAAK,QAAQ,iBAC9B7B,OAAA;YAAAmG,QAAA,gBAAGnG,OAAA;cAAAmG,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9E,aAAa,CAAC2C,OAAO,IAAI,KAAK;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAChE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK;IACf;EAAA;AAEJ,CAAC;AAAC3F,EAAA,CAlgBIH,gBAAgB;EAAA,QAILnB,IAAI,CAAC8B,OAAO;AAAA;AAAA0I,GAAA,GAJvBrJ,gBAAgB;AAogBtB,eAAAsJ,GAAA,gBAAe9K,UAAU,CAACwB,gBAAgB,CAAC;AAAC,IAAAD,EAAA,EAAAsJ,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAxJ,EAAA;AAAAwJ,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}