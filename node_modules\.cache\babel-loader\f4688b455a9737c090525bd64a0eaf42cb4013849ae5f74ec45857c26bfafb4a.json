{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport BankFilledSvg from \"@ant-design/icons-svg/es/asn/BankFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar BankFilled = function BankFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: BankFilledSvg\n  }));\n};\n\n/**![bank](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg5NCA0NjJjMzAuOSAwIDQzLjgtMzkuNyAxOC43LTU4TDUzMC44IDEyNi4yYTMxLjgxIDMxLjgxIDAgMDAtMzcuNiAwTDExMS4zIDQwNGMtMjUuMSAxOC4yLTEyLjIgNTggMTguOCA1OEgxOTJ2Mzc0aC03MmMtNC40IDAtOCAzLjYtOCA4djUyYzAgNC40IDMuNiA4IDggOGg3ODRjNC40IDAgOC0zLjYgOC04di01MmMwLTQuNC0zLjYtOC04LThoLTcyVjQ2Mmg2MnpNMzgxIDgzNkgyNjRWNDYyaDExN3YzNzR6bTE4OSAwSDQ1M1Y0NjJoMTE3djM3NHptMTkwIDBINjQyVjQ2MmgxMTh2Mzc0eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(BankFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'BankFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "BankFilledSvg", "AntdIcon", "BankFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@ant-design/icons/es/icons/BankFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport BankFilledSvg from \"@ant-design/icons-svg/es/asn/BankFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar BankFilled = function BankFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: BankFilledSvg\n  }));\n};\n\n/**![bank](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg5NCA0NjJjMzAuOSAwIDQzLjgtMzkuNyAxOC43LTU4TDUzMC44IDEyNi4yYTMxLjgxIDMxLjgxIDAgMDAtMzcuNiAwTDExMS4zIDQwNGMtMjUuMSAxOC4yLTEyLjIgNTggMTguOCA1OEgxOTJ2Mzc0aC03MmMtNC40IDAtOCAzLjYtOCA4djUyYzAgNC40IDMuNiA4IDggOGg3ODRjNC40IDAgOC0zLjYgOC04di01MmMwLTQuNC0zLjYtOC04LThoLTcyVjQ2Mmg2MnpNMzgxIDgzNkgyNjRWNDYyaDExN3YzNzR6bTE4OSAwSDQ1M1Y0NjJoMTE3djM3NHptMTkwIDBINjQyVjQ2MmgxMTh2Mzc0eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(BankFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'BankFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,yCAAyC;AACnE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/C,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,UAAU,CAAC;AACvD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,YAAY;AACpC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}