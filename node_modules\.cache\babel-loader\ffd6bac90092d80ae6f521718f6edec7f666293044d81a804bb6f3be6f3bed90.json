{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\RoadMonitoring.jsx\",\n  _s = $RefreshSig$();\n// src/pages/RoadMonitoring.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Card, List, Descriptions, Spin, Badge, Row, Col } from 'antd';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport devicesData from '../data/devices.json';\nimport VideoPlayer from '../components/VideoPlayer';\nimport axios from 'axios'; // 添加axios导入\n\n// 添加立即打印，检查导入是否成功\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconsole.log('DevicesData import check:', devicesData);\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\n_c = PageContainer;\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 24px' : props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 0' : props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\n_c2 = MainContent;\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 视频容器\n_c3 = InfoCard;\nconst VideoContainer = styled.div`\n  background: #000;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  margin-bottom: 10px;\n  \n  &:before {\n    content: \"\";\n    display: block;\n    padding-top: 56.25%; // 16:9 宽高比\n  }\n`;\n\n// 视频占位符\n_c4 = VideoContainer;\nconst VideoPlaceholder = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 13px;\n`;\n_c5 = VideoPlaceholder;\nconst RoadMonitoring = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [intersections, setIntersections] = useState([]);\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  // 声明一个函数来加载设备数据\n  const loadData = async () => {\n    try {\n      setLoading(true);\n\n      // 尝试从API获取设备数据\n      let devicesArray = [];\n      try {\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const response = await axios.get(`${apiUrl}/api/devices`);\n        if (response.data && response.data.success) {\n          devicesArray = response.data.data;\n        }\n      } catch (error) {\n        console.log('从API获取设备失败，使用本地数据', error);\n        // 使用导入的 devicesData\n        console.log('Imported devicesData:', devicesData);\n        // 确保我们访问的是 devices 数组\n        devicesArray = devicesData.devices || [];\n      }\n      if (devicesArray.length === 0) {\n        throw new Error('No devices found in the data');\n      }\n      console.log('Processing devices array:', devicesArray);\n\n      // 过滤掉类型为obu的设备\n      const filteredDevices = devicesArray.filter(device => device.type !== 'obu');\n\n      // 根据 location 对设备进行分组\n      const groupedDevices = filteredDevices.reduce((acc, device) => {\n        const location = device.location;\n        if (!location) {\n          console.warn('Device missing location:', device);\n          return acc;\n        }\n        if (!acc[location]) {\n          acc[location] = {\n            id: location,\n            name: location,\n            description: `${location}监控点`,\n            status: '正常',\n            lastUpdate: device.createdAt || new Date().toISOString(),\n            devices: []\n          };\n        }\n\n        // 添加 rtspUrl 到设备信息中\n        acc[location].devices.push({\n          type: device.type,\n          id: device.id,\n          name: device.name,\n          status: device.status,\n          rtspUrl: device.rtspUrl // 添加 rtspUrl 字段\n        });\n        return acc;\n      }, {});\n      const intersectionsData = Object.values(groupedDevices);\n      console.log('Final processed intersections:', intersectionsData);\n      if (intersectionsData.length === 0) {\n        throw new Error('No intersections processed from the data');\n      } else {\n        setIntersections(intersectionsData);\n        if (!selectedIntersection && intersectionsData.length > 0) {\n          setSelectedIntersection(intersectionsData[0]);\n        } else if (selectedIntersection) {\n          // 如果之前已选择了一个路口，尝试在新数据中找到它\n          const updatedSelected = intersectionsData.find(i => i.id === selectedIntersection.id);\n          if (updatedSelected) {\n            setSelectedIntersection(updatedSelected);\n          } else if (intersectionsData.length > 0) {\n            setSelectedIntersection(intersectionsData[0]);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error loading devices data:', error);\n      setIntersections([{\n        id: 'error',\n        name: '数据加载错误',\n        description: `无法加载设备数据: ${error.message}`,\n        status: '错误',\n        lastUpdate: new Date().toLocaleString(),\n        devices: []\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 修改 useEffect，添加定时器定期检查设备数据更新\n  useEffect(() => {\n    // 首次加载数据\n    loadData();\n\n    // // 设置定时器，每300秒检查一次设备数据更新\n    // const intervalId = setInterval(async () => {\n    //   try {\n    //     const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n    //     const response = await axios.get(`${apiUrl}/api/devices/lastUpdate`);\n\n    //     if (response.data && response.data.lastUpdate) {\n    //       // 检查上次更新时间，如果有更新则重新加载数据\n    //       if (response.data.lastUpdate > localStorage.getItem('lastDevicesUpdate')) {\n    //         console.log('设备数据有更新，重新加载');\n    //         loadData();\n    //         localStorage.setItem('lastDevicesUpdate', response.data.lastUpdate);\n    //       }\n    //     } else {\n    //       // 如果API不可用，使用简单的时间间隔更新\n    //       loadData();\n    //     }\n    //   } catch (error) {\n    //     console.warn('检查设备更新失败，直接重新加载数据', error);\n    //     // 如果API不可用，仍然定期更新数据\n    //     loadData();\n    //   }\n    // }, 300000); // 300秒检查一次\n\n    // // 组件卸载时清除定时器\n    // return () => clearInterval(intervalId);\n  }, []);\n\n  // 处理路口选择\n  const handleIntersectionSelect = intersection => {\n    setSelectedIntersection(intersection);\n  };\n\n  // 修改 getDeviceSummary 函数以支持更多设备类型\n  const getDeviceSummary = devices => {\n    const summary = {};\n    devices.forEach(device => {\n      const type = device.type;\n      summary[type] = (summary[type] || 0) + 1;\n    });\n    return Object.entries(summary).map(([type, count]) => {\n      const typeNames = {\n        'camera': '摄像头',\n        'rsu': 'RSU',\n        'mmwave_radar': '毫米波雷达',\n        'edge_computing': '边缘计算单元',\n        'lidar': '激光雷达'\n      };\n      return `${typeNames[type] || type}: ${count}`;\n    }).join(', ');\n  };\n\n  // 修改 getCameras 函数以匹配实际的设备类型\n  const getCameras = devices => {\n    return devices.filter(device => device.type === 'camera');\n  };\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    spinning: loading,\n    tip: \"\\u52A0\\u8F7D\\u4E2D...\",\n    children: /*#__PURE__*/_jsxDEV(PageContainer, {\n      children: [/*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"left\",\n        collapsed: leftCollapsed,\n        onCollapse: () => setLeftCollapsed(!leftCollapsed),\n        children: /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8DEF\\u53E3\\u4FE1\\u606F\\u5217\\u8868\",\n          bordered: false,\n          height: \"100%\",\n          children: /*#__PURE__*/_jsxDEV(List, {\n            itemLayout: \"vertical\",\n            dataSource: intersections,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              onClick: () => handleIntersectionSelect(item),\n              style: {\n                cursor: 'pointer',\n                background: (selectedIntersection === null || selectedIntersection === void 0 ? void 0 : selectedIntersection.id) === item.id ? '#e6f7ff' : 'transparent',\n                padding: '8px',\n                borderRadius: '4px',\n                marginBottom: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '14px',\n                    fontWeight: 'bold'\n                  },\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 28\n                }, this)\n                // description={<span style={{ fontSize: '12px' }}>{item.description}</span>}\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '12px',\n                  marginTop: '0px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u8BBE\\u5907\\u914D\\u7F6E\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 26\n                  }, this), \" \", getDeviceSummary(item.devices)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '4px'\n                  },\n                  children: item.devices.map(device => /*#__PURE__*/_jsxDEV(Badge, {\n                    status: device.status === 'online' ? 'success' : 'error',\n                    text: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '12px',\n                        marginRight: '8px'\n                      },\n                      children: device.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 29\n                    }, this),\n                    style: {\n                      display: 'inline-block',\n                      marginRight: '8px'\n                    }\n                  }, device.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        leftCollapsed: leftCollapsed,\n        rightCollapsed: rightCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"right\",\n        collapsed: rightCollapsed,\n        onCollapse: () => setRightCollapsed(!rightCollapsed),\n        children: /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: `视频监控 - ${(selectedIntersection === null || selectedIntersection === void 0 ? void 0 : selectedIntersection.name) || ''}`,\n          bordered: false,\n          height: \"100%\",\n          children: selectedIntersection ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '12px',\n                fontSize: '13px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Descriptions, {\n                size: \"small\",\n                column: 1,\n                styles: {\n                  label: {\n                    fontSize: '13px'\n                  },\n                  content: {\n                    fontSize: '13px'\n                  }\n                },\n                children: /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                  label: \"\\u6444\\u50CF\\u5934\\u6570\\u91CF\",\n                  children: getCameras(selectedIntersection.devices).length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this), getCameras(selectedIntersection.devices).slice(0, 4).map(camera => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '10px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '13px',\n                  marginBottom: '4px',\n                  fontWeight: 'bold'\n                },\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  status: camera.status === 'online' ? 'success' : 'error',\n                  text: camera.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(VideoContainer, {\n                children: /*#__PURE__*/_jsxDEV(VideoPlaceholder, {\n                  children: camera.status === 'online' ? /*#__PURE__*/_jsxDEV(VideoPlayer, {\n                    deviceId: camera.id,\n                    rtspUrl: camera.rtspUrl\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: \"\\u6444\\u50CF\\u5934\\u79BB\\u7EBF\"\n                  }, void 0, false)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 21\n              }, this)]\n            }, camera.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 19\n            }, this)), getCameras(selectedIntersection.devices).length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: '20px 0'\n              },\n              children: \"\\u8BE5\\u8DEF\\u53E3\\u6CA1\\u6709\\u914D\\u7F6E\\u6444\\u50CF\\u5934\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '20px 0'\n            },\n            children: \"\\u8BF7\\u9009\\u62E9\\u4E00\\u4E2A\\u8DEF\\u53E3\\u67E5\\u770B\\u89C6\\u9891\\u76D1\\u63A7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 281,\n    columnNumber: 5\n  }, this);\n};\n_s(RoadMonitoring, \"mRMMpGGs4EM8Z7alHD7AKBWx1lg=\");\n_c6 = RoadMonitoring;\nexport default RoadMonitoring;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"InfoCard\");\n$RefreshReg$(_c4, \"VideoContainer\");\n$RefreshReg$(_c5, \"VideoPlaceholder\");\n$RefreshReg$(_c6, \"RoadMonitoring\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "List", "Descriptions", "Spin", "Badge", "Row", "Col", "styled", "CollapsibleSidebar", "devicesData", "VideoPlayer", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "div", "_c", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "_c2", "InfoCard", "height", "_c3", "VideoContainer", "_c4", "VideoPlaceholder", "_c5", "RoadMonitoring", "_s", "loading", "setLoading", "intersections", "setIntersections", "selectedIntersection", "setSelectedIntersection", "setLeftCollapsed", "setRightCollapsed", "loadData", "devicesArray", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "data", "success", "error", "devices", "length", "Error", "filteredDevices", "filter", "device", "type", "groupedDevices", "reduce", "acc", "location", "warn", "id", "name", "description", "status", "lastUpdate", "createdAt", "Date", "toISOString", "push", "rtspUrl", "intersectionsData", "Object", "values", "updatedSelected", "find", "i", "message", "toLocaleString", "handleIntersectionSelect", "intersection", "getDeviceSummary", "summary", "for<PERSON>ach", "entries", "map", "count", "typeNames", "join", "getCameras", "spinning", "tip", "children", "position", "collapsed", "onCollapse", "title", "bordered", "itemLayout", "dataSource", "renderItem", "item", "<PERSON><PERSON>", "onClick", "style", "cursor", "background", "padding", "borderRadius", "marginBottom", "Meta", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "text", "marginRight", "display", "size", "column", "styles", "label", "content", "slice", "camera", "deviceId", "textAlign", "_c6", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RoadMonitoring.jsx"], "sourcesContent": ["// src/pages/RoadMonitoring.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Card, List, Descriptions, Spin, Badge, Row, Col } from 'antd';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport devicesData from '../data/devices.json';\nimport VideoPlayer from '../components/VideoPlayer';\nimport axios from 'axios';  // 添加axios导入\n\n// 添加立即打印，检查导入是否成功\nconsole.log('DevicesData import check:', devicesData);\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 24px' : \n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \n    props.leftCollapsed ? '0 8px 0 0' : \n    props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 视频容器\nconst VideoContainer = styled.div`\n  background: #000;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  margin-bottom: 10px;\n  \n  &:before {\n    content: \"\";\n    display: block;\n    padding-top: 56.25%; // 16:9 宽高比\n  }\n`;\n\n// 视频占位符\nconst VideoPlaceholder = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 13px;\n`;\n\nconst RoadMonitoring = () => {\n  const [loading, setLoading] = useState(true);\n  const [intersections, setIntersections] = useState([]);\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n  \n  // 声明一个函数来加载设备数据\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      \n      // 尝试从API获取设备数据\n      let devicesArray = [];\n      try {\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const response = await axios.get(`${apiUrl}/api/devices`);\n        if (response.data && response.data.success) {\n          devicesArray = response.data.data;\n        }\n      } catch (error) {\n        console.log('从API获取设备失败，使用本地数据', error);\n        // 使用导入的 devicesData\n        console.log('Imported devicesData:', devicesData);\n        // 确保我们访问的是 devices 数组\n        devicesArray = devicesData.devices || [];\n      }\n      \n      if (devicesArray.length === 0) {\n        throw new Error('No devices found in the data');\n      }\n      \n      console.log('Processing devices array:', devicesArray);\n      \n      // 过滤掉类型为obu的设备\n      const filteredDevices = devicesArray.filter(device => device.type !== 'obu');\n      \n      // 根据 location 对设备进行分组\n      const groupedDevices = filteredDevices.reduce((acc, device) => {\n        const location = device.location;\n        if (!location) {\n          console.warn('Device missing location:', device);\n          return acc;\n        }\n\n        if (!acc[location]) {\n          acc[location] = {\n            id: location,\n            name: location,\n            description: `${location}监控点`,\n            status: '正常',\n            lastUpdate: device.createdAt || new Date().toISOString(),\n            devices: []\n          };\n        }\n\n        // 添加 rtspUrl 到设备信息中\n        acc[location].devices.push({\n          type: device.type,\n          id: device.id,\n          name: device.name,\n          status: device.status,\n          rtspUrl: device.rtspUrl  // 添加 rtspUrl 字段\n        });\n\n        return acc;\n      }, {});\n\n      const intersectionsData = Object.values(groupedDevices);\n      console.log('Final processed intersections:', intersectionsData);\n\n      if (intersectionsData.length === 0) {\n        throw new Error('No intersections processed from the data');\n      } else {\n        setIntersections(intersectionsData);\n        if (!selectedIntersection && intersectionsData.length > 0) {\n          setSelectedIntersection(intersectionsData[0]);\n        } else if (selectedIntersection) {\n          // 如果之前已选择了一个路口，尝试在新数据中找到它\n          const updatedSelected = intersectionsData.find(i => i.id === selectedIntersection.id);\n          if (updatedSelected) {\n            setSelectedIntersection(updatedSelected);\n          } else if (intersectionsData.length > 0) {\n            setSelectedIntersection(intersectionsData[0]);\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Error loading devices data:', error);\n      setIntersections([{\n        id: 'error',\n        name: '数据加载错误',\n        description: `无法加载设备数据: ${error.message}`,\n        status: '错误',\n        lastUpdate: new Date().toLocaleString(),\n        devices: []\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  \n  // 修改 useEffect，添加定时器定期检查设备数据更新\n  useEffect(() => {\n    // 首次加载数据\n    loadData();\n    \n    // // 设置定时器，每300秒检查一次设备数据更新\n    // const intervalId = setInterval(async () => {\n    //   try {\n    //     const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n    //     const response = await axios.get(`${apiUrl}/api/devices/lastUpdate`);\n        \n    //     if (response.data && response.data.lastUpdate) {\n    //       // 检查上次更新时间，如果有更新则重新加载数据\n    //       if (response.data.lastUpdate > localStorage.getItem('lastDevicesUpdate')) {\n    //         console.log('设备数据有更新，重新加载');\n    //         loadData();\n    //         localStorage.setItem('lastDevicesUpdate', response.data.lastUpdate);\n    //       }\n    //     } else {\n    //       // 如果API不可用，使用简单的时间间隔更新\n    //       loadData();\n    //     }\n    //   } catch (error) {\n    //     console.warn('检查设备更新失败，直接重新加载数据', error);\n    //     // 如果API不可用，仍然定期更新数据\n    //     loadData();\n    //   }\n    // }, 300000); // 300秒检查一次\n    \n    // // 组件卸载时清除定时器\n    // return () => clearInterval(intervalId);\n  }, []);\n  \n  // 处理路口选择\n  const handleIntersectionSelect = (intersection) => {\n    setSelectedIntersection(intersection);\n  };\n  \n  // 修改 getDeviceSummary 函数以支持更多设备类型\n  const getDeviceSummary = (devices) => {\n    const summary = {};\n    devices.forEach(device => {\n      const type = device.type;\n      summary[type] = (summary[type] || 0) + 1;\n    });\n    \n    return Object.entries(summary).map(([type, count]) => {\n      const typeNames = {\n        'camera': '摄像头',\n        'rsu': 'RSU',\n        'mmwave_radar': '毫米波雷达',\n        'edge_computing': '边缘计算单元',\n        'lidar': '激光雷达'\n      };\n      \n      return `${typeNames[type] || type}: ${count}`;\n    }).join(', ');\n  };\n  \n  // 修改 getCameras 函数以匹配实际的设备类型\n  const getCameras = (devices) => {\n    return devices.filter(device => device.type === 'camera');\n  };\n  \n  return (\n    <Spin spinning={loading} tip=\"加载中...\">\n      <PageContainer>\n        {/* 左侧信息栏 */}\n        <CollapsibleSidebar \n          position=\"left\"\n          collapsed={leftCollapsed}\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\n        >\n          <InfoCard title=\"路口信息列表\" bordered={false} height=\"100%\">\n            <List\n              itemLayout=\"vertical\"\n              dataSource={intersections}\n              renderItem={item => (\n                <List.Item\n                  onClick={() => handleIntersectionSelect(item)}\n                  style={{ \n                    cursor: 'pointer',\n                    background: selectedIntersection?.id === item.id ? '#e6f7ff' : 'transparent',\n                    padding: '8px',\n                    borderRadius: '4px',\n                    marginBottom: '8px'\n                  }}\n                >\n                  <List.Item.Meta\n                    title={<span style={{ fontSize: '14px', fontWeight: 'bold' }}>{item.name}</span>}\n                    // description={<span style={{ fontSize: '12px' }}>{item.description}</span>}\n                  />\n                  <div style={{ fontSize: '12px', marginTop: '0px' }}>\n                    <div><strong>设备配置：</strong> {getDeviceSummary(item.devices)}</div>\n                    <div style={{ marginTop: '4px' }}>\n                      {item.devices.map(device => (\n                        <Badge \n                          key={device.id}\n                          status={device.status === 'online' ? 'success' : 'error'} \n                          text={\n                            <span style={{ fontSize: '12px', marginRight: '8px' }}>\n                              {device.name}\n                            </span>\n                          }\n                          style={{ display: 'inline-block', marginRight: '8px' }}\n                        />\n                      ))}\n                    </div>\n                  </div>\n                </List.Item>\n              )}\n            />\n          </InfoCard>\n        </CollapsibleSidebar>\n        \n        {/* 主内容区域 */}\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\n          {/* 主要内容 */}\n        </MainContent>\n        \n        {/* 右侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"right\"\n          collapsed={rightCollapsed}\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\n        >\n          <InfoCard \n            title={`视频监控 - ${selectedIntersection?.name || ''}`} \n            bordered={false} \n            height=\"100%\"\n          >\n            {selectedIntersection ? (\n              <>\n                <div style={{ marginBottom: '12px', fontSize: '13px' }}>\n                  <Descriptions \n                    size=\"small\" \n                    column={1}\n                    styles={{\n                      label: { fontSize: '13px' },\n                      content: { fontSize: '13px' }\n                    }}\n                  >\n                    {/* <Descriptions.Item label=\"路口名称\">{selectedIntersection.name}</Descriptions.Item> */}\n                    {/* <Descriptions.Item label=\"路口描述\">{selectedIntersection.description}</Descriptions.Item> */}\n                    <Descriptions.Item label=\"摄像头数量\">{getCameras(selectedIntersection.devices).length}</Descriptions.Item>\n                  </Descriptions>\n                </div>\n                \n                {/* 修改为N行1列的布局 */}\n                {getCameras(selectedIntersection.devices).slice(0, 4).map(camera => (\n                  <div key={camera.id} style={{ marginBottom: '10px' }}>\n                    <div style={{ fontSize: '13px', marginBottom: '4px', fontWeight: 'bold' }}>\n                      <Badge \n                        status={camera.status === 'online' ? 'success' : 'error'} \n                        text={camera.name}\n                      />\n                    </div>\n                    <VideoContainer>\n                      <VideoPlaceholder>\n                        {camera.status === 'online' ? (\n                          <VideoPlayer \n                            deviceId={camera.id} \n                            rtspUrl={camera.rtspUrl} \n                          />\n                        ) : (\n                          <>摄像头离线</>\n                        )}\n                      </VideoPlaceholder>\n                    </VideoContainer>\n                  </div>\n                ))}\n                \n                {getCameras(selectedIntersection.devices).length === 0 && (\n                  <div style={{ textAlign: 'center', padding: '20px 0' }}>\n                    该路口没有配置摄像头\n                  </div>\n                )}\n              </>\n            ) : (\n              <div style={{ textAlign: 'center', padding: '20px 0' }}>\n                请选择一个路口查看视频监控\n              </div>\n            )}\n          </InfoCard>\n        </CollapsibleSidebar>\n      </PageContainer>\n    </Spin>\n  );\n};\n\nexport default RoadMonitoring;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,IAAI,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,QAAQ,MAAM;AACtE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,yCAAyC;AACxE,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,KAAK,MAAM,OAAO,CAAC,CAAE;;AAE5B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACAC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAER,WAAW,CAAC;;AAErD;AACA,MAAMS,aAAa,GAAGX,MAAM,CAACY,GAAG;AAChC;AACA;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GANMF,aAAa;AAOnB,MAAMG,WAAW,GAAGd,MAAM,CAACY,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMG,YAAY,GAAGf,MAAM,CAACY,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMI,WAAW,GAAGhB,MAAM,CAACY,GAAG;AAC9B;AACA;AACA;AACA,aAAaK,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACxEF,KAAK,CAACC,aAAa,GAAG,cAAc,GACpCD,KAAK,CAACE,cAAc,GAAG,cAAc,GAAG,OAAO;AACnD;AACA,YAAYF,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACvEF,KAAK,CAACC,aAAa,GAAG,WAAW,GACjCD,KAAK,CAACE,cAAc,GAAG,WAAW,GAAG,GAAG;AAC5C;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfMJ,WAAW;AAgBjB,MAAMK,QAAQ,GAAGrB,MAAM,CAACP,IAAI,CAAC;AAC7B;AACA,YAAYwB,KAAK,IAAIA,KAAK,CAACK,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GA1BMF,QAAQ;AA2Bd,MAAMG,cAAc,GAAGxB,MAAM,CAACY,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAa,GAAA,GAdMD,cAAc;AAepB,MAAME,gBAAgB,GAAG1B,MAAM,CAACY,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAXID,gBAAgB;AAatB,MAAME,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2C,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAAC2B,aAAa,EAAEkB,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC4B,cAAc,EAAEkB,iBAAiB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM+C,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAIQ,YAAY,GAAG,EAAE;MACrB,IAAI;QACF,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;QACvE,MAAMC,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,GAAG,CAAC,GAAGL,MAAM,cAAc,CAAC;QACzD,IAAII,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UAC1CR,YAAY,GAAGK,QAAQ,CAACE,IAAI,CAACA,IAAI;QACnC;MACF,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdvC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEsC,KAAK,CAAC;QACvC;QACAvC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAER,WAAW,CAAC;QACjD;QACAqC,YAAY,GAAGrC,WAAW,CAAC+C,OAAO,IAAI,EAAE;MAC1C;MAEA,IAAIV,YAAY,CAACW,MAAM,KAAK,CAAC,EAAE;QAC7B,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEA1C,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE6B,YAAY,CAAC;;MAEtD;MACA,MAAMa,eAAe,GAAGb,YAAY,CAACc,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,KAAK,CAAC;;MAE5E;MACA,MAAMC,cAAc,GAAGJ,eAAe,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEJ,MAAM,KAAK;QAC7D,MAAMK,QAAQ,GAAGL,MAAM,CAACK,QAAQ;QAChC,IAAI,CAACA,QAAQ,EAAE;UACblD,OAAO,CAACmD,IAAI,CAAC,0BAA0B,EAAEN,MAAM,CAAC;UAChD,OAAOI,GAAG;QACZ;QAEA,IAAI,CAACA,GAAG,CAACC,QAAQ,CAAC,EAAE;UAClBD,GAAG,CAACC,QAAQ,CAAC,GAAG;YACdE,EAAE,EAAEF,QAAQ;YACZG,IAAI,EAAEH,QAAQ;YACdI,WAAW,EAAE,GAAGJ,QAAQ,KAAK;YAC7BK,MAAM,EAAE,IAAI;YACZC,UAAU,EAAEX,MAAM,CAACY,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACxDnB,OAAO,EAAE;UACX,CAAC;QACH;;QAEA;QACAS,GAAG,CAACC,QAAQ,CAAC,CAACV,OAAO,CAACoB,IAAI,CAAC;UACzBd,IAAI,EAAED,MAAM,CAACC,IAAI;UACjBM,EAAE,EAAEP,MAAM,CAACO,EAAE;UACbC,IAAI,EAAER,MAAM,CAACQ,IAAI;UACjBE,MAAM,EAAEV,MAAM,CAACU,MAAM;UACrBM,OAAO,EAAEhB,MAAM,CAACgB,OAAO,CAAE;QAC3B,CAAC,CAAC;QAEF,OAAOZ,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;MAEN,MAAMa,iBAAiB,GAAGC,MAAM,CAACC,MAAM,CAACjB,cAAc,CAAC;MACvD/C,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE6D,iBAAiB,CAAC;MAEhE,IAAIA,iBAAiB,CAACrB,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAIC,KAAK,CAAC,0CAA0C,CAAC;MAC7D,CAAC,MAAM;QACLlB,gBAAgB,CAACsC,iBAAiB,CAAC;QACnC,IAAI,CAACrC,oBAAoB,IAAIqC,iBAAiB,CAACrB,MAAM,GAAG,CAAC,EAAE;UACzDf,uBAAuB,CAACoC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,MAAM,IAAIrC,oBAAoB,EAAE;UAC/B;UACA,MAAMwC,eAAe,GAAGH,iBAAiB,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACf,EAAE,KAAK3B,oBAAoB,CAAC2B,EAAE,CAAC;UACrF,IAAIa,eAAe,EAAE;YACnBvC,uBAAuB,CAACuC,eAAe,CAAC;UAC1C,CAAC,MAAM,IAAIH,iBAAiB,CAACrB,MAAM,GAAG,CAAC,EAAE;YACvCf,uBAAuB,CAACoC,iBAAiB,CAAC,CAAC,CAAC,CAAC;UAC/C;QACF;MACF;IACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdvC,OAAO,CAACuC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDf,gBAAgB,CAAC,CAAC;QAChB4B,EAAE,EAAE,OAAO;QACXC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE,aAAaf,KAAK,CAAC6B,OAAO,EAAE;QACzCb,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE,IAAIE,IAAI,CAAC,CAAC,CAACW,cAAc,CAAC,CAAC;QACvC7B,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAvC,SAAS,CAAC,MAAM;IACd;IACA8C,QAAQ,CAAC,CAAC;;IAEV;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyC,wBAAwB,GAAIC,YAAY,IAAK;IACjD7C,uBAAuB,CAAC6C,YAAY,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIhC,OAAO,IAAK;IACpC,MAAMiC,OAAO,GAAG,CAAC,CAAC;IAClBjC,OAAO,CAACkC,OAAO,CAAC7B,MAAM,IAAI;MACxB,MAAMC,IAAI,GAAGD,MAAM,CAACC,IAAI;MACxB2B,OAAO,CAAC3B,IAAI,CAAC,GAAG,CAAC2B,OAAO,CAAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1C,CAAC,CAAC;IAEF,OAAOiB,MAAM,CAACY,OAAO,CAACF,OAAO,CAAC,CAACG,GAAG,CAAC,CAAC,CAAC9B,IAAI,EAAE+B,KAAK,CAAC,KAAK;MACpD,MAAMC,SAAS,GAAG;QAChB,QAAQ,EAAE,KAAK;QACf,KAAK,EAAE,KAAK;QACZ,cAAc,EAAE,OAAO;QACvB,gBAAgB,EAAE,QAAQ;QAC1B,OAAO,EAAE;MACX,CAAC;MAED,OAAO,GAAGA,SAAS,CAAChC,IAAI,CAAC,IAAIA,IAAI,KAAK+B,KAAK,EAAE;IAC/C,CAAC,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;EACf,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIxC,OAAO,IAAK;IAC9B,OAAOA,OAAO,CAACI,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,QAAQ,CAAC;EAC3D,CAAC;EAED,oBACEjD,OAAA,CAACV,IAAI;IAAC8F,QAAQ,EAAE5D,OAAQ;IAAC6D,GAAG,EAAC,uBAAQ;IAAAC,QAAA,eACnCtF,OAAA,CAACK,aAAa;MAAAiF,QAAA,gBAEZtF,OAAA,CAACL,kBAAkB;QACjB4F,QAAQ,EAAC,MAAM;QACfC,SAAS,EAAE5E,aAAc;QACzB6E,UAAU,EAAEA,CAAA,KAAM3D,gBAAgB,CAAC,CAAClB,aAAa,CAAE;QAAA0E,QAAA,eAEnDtF,OAAA,CAACe,QAAQ;UAAC2E,KAAK,EAAC,sCAAQ;UAACC,QAAQ,EAAE,KAAM;UAAC3E,MAAM,EAAC,MAAM;UAAAsE,QAAA,eACrDtF,OAAA,CAACZ,IAAI;YACHwG,UAAU,EAAC,UAAU;YACrBC,UAAU,EAAEnE,aAAc;YAC1BoE,UAAU,EAAEC,IAAI,iBACd/F,OAAA,CAACZ,IAAI,CAAC4G,IAAI;cACRC,OAAO,EAAEA,CAAA,KAAMxB,wBAAwB,CAACsB,IAAI,CAAE;cAC9CG,KAAK,EAAE;gBACLC,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,CAAAxE,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAE2B,EAAE,MAAKwC,IAAI,CAACxC,EAAE,GAAG,SAAS,GAAG,aAAa;gBAC5E8C,OAAO,EAAE,KAAK;gBACdC,YAAY,EAAE,KAAK;gBACnBC,YAAY,EAAE;cAChB,CAAE;cAAAjB,QAAA,gBAEFtF,OAAA,CAACZ,IAAI,CAAC4G,IAAI,CAACQ,IAAI;gBACbd,KAAK,eAAE1F,OAAA;kBAAMkG,KAAK,EAAE;oBAAEO,QAAQ,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAO,CAAE;kBAAApB,QAAA,EAAES,IAAI,CAACvC;gBAAI;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;gBAC/E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACF9G,OAAA;gBAAKkG,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEM,SAAS,EAAE;gBAAM,CAAE;gBAAAzB,QAAA,gBACjDtF,OAAA;kBAAAsF,QAAA,gBAAKtF,OAAA;oBAAAsF,QAAA,EAAQ;kBAAK;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACnC,gBAAgB,CAACoB,IAAI,CAACpD,OAAO,CAAC;gBAAA;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClE9G,OAAA;kBAAKkG,KAAK,EAAE;oBAAEa,SAAS,EAAE;kBAAM,CAAE;kBAAAzB,QAAA,EAC9BS,IAAI,CAACpD,OAAO,CAACoC,GAAG,CAAC/B,MAAM,iBACtBhD,OAAA,CAACT,KAAK;oBAEJmE,MAAM,EAAEV,MAAM,CAACU,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;oBACzDsD,IAAI,eACFhH,OAAA;sBAAMkG,KAAK,EAAE;wBAAEO,QAAQ,EAAE,MAAM;wBAAEQ,WAAW,EAAE;sBAAM,CAAE;sBAAA3B,QAAA,EACnDtC,MAAM,CAACQ;oBAAI;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CACP;oBACDZ,KAAK,EAAE;sBAAEgB,OAAO,EAAE,cAAc;sBAAED,WAAW,EAAE;oBAAM;kBAAE,GAPlDjE,MAAM,CAACO,EAAE;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQf,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGrB9G,OAAA,CAACU,WAAW;QAACE,aAAa,EAAEA,aAAc;QAACC,cAAc,EAAEA;MAAe;QAAA8F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC,eAGd9G,OAAA,CAACL,kBAAkB;QACjB4F,QAAQ,EAAC,OAAO;QAChBC,SAAS,EAAE3E,cAAe;QAC1B4E,UAAU,EAAEA,CAAA,KAAM1D,iBAAiB,CAAC,CAAClB,cAAc,CAAE;QAAAyE,QAAA,eAErDtF,OAAA,CAACe,QAAQ;UACP2E,KAAK,EAAE,UAAU,CAAA9D,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAE4B,IAAI,KAAI,EAAE,EAAG;UACpDmC,QAAQ,EAAE,KAAM;UAChB3E,MAAM,EAAC,MAAM;UAAAsE,QAAA,EAEZ1D,oBAAoB,gBACnB5B,OAAA,CAAAE,SAAA;YAAAoF,QAAA,gBACEtF,OAAA;cAAKkG,KAAK,EAAE;gBAAEK,YAAY,EAAE,MAAM;gBAAEE,QAAQ,EAAE;cAAO,CAAE;cAAAnB,QAAA,eACrDtF,OAAA,CAACX,YAAY;gBACX8H,IAAI,EAAC,OAAO;gBACZC,MAAM,EAAE,CAAE;gBACVC,MAAM,EAAE;kBACNC,KAAK,EAAE;oBAAEb,QAAQ,EAAE;kBAAO,CAAC;kBAC3Bc,OAAO,EAAE;oBAAEd,QAAQ,EAAE;kBAAO;gBAC9B,CAAE;gBAAAnB,QAAA,eAIFtF,OAAA,CAACX,YAAY,CAAC2G,IAAI;kBAACsB,KAAK,EAAC,gCAAO;kBAAAhC,QAAA,EAAEH,UAAU,CAACvD,oBAAoB,CAACe,OAAO,CAAC,CAACC;gBAAM;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,EAGL3B,UAAU,CAACvD,oBAAoB,CAACe,OAAO,CAAC,CAAC6E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACzC,GAAG,CAAC0C,MAAM,iBAC9DzH,OAAA;cAAqBkG,KAAK,EAAE;gBAAEK,YAAY,EAAE;cAAO,CAAE;cAAAjB,QAAA,gBACnDtF,OAAA;gBAAKkG,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEF,YAAY,EAAE,KAAK;kBAAEG,UAAU,EAAE;gBAAO,CAAE;gBAAApB,QAAA,eACxEtF,OAAA,CAACT,KAAK;kBACJmE,MAAM,EAAE+D,MAAM,CAAC/D,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;kBACzDsD,IAAI,EAAES,MAAM,CAACjE;gBAAK;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9G,OAAA,CAACkB,cAAc;gBAAAoE,QAAA,eACbtF,OAAA,CAACoB,gBAAgB;kBAAAkE,QAAA,EACdmC,MAAM,CAAC/D,MAAM,KAAK,QAAQ,gBACzB1D,OAAA,CAACH,WAAW;oBACV6H,QAAQ,EAAED,MAAM,CAAClE,EAAG;oBACpBS,OAAO,EAAEyD,MAAM,CAACzD;kBAAQ;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,gBAEF9G,OAAA,CAAAE,SAAA;oBAAAoF,QAAA,EAAE;kBAAK,gBAAE;gBACV;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA,GAlBTW,MAAM,CAAClE,EAAE;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBd,CACN,CAAC,EAED3B,UAAU,CAACvD,oBAAoB,CAACe,OAAO,CAAC,CAACC,MAAM,KAAK,CAAC,iBACpD5C,OAAA;cAAKkG,KAAK,EAAE;gBAAEyB,SAAS,EAAE,QAAQ;gBAAEtB,OAAO,EAAE;cAAS,CAAE;cAAAf,QAAA,EAAC;YAExD;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA,eACD,CAAC,gBAEH9G,OAAA;YAAKkG,KAAK,EAAE;cAAEyB,SAAS,EAAE,QAAQ;cAAEtB,OAAO,EAAE;YAAS,CAAE;YAAAf,QAAA,EAAC;UAExD;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEX,CAAC;AAACvF,EAAA,CApSID,cAAc;AAAAsG,GAAA,GAAdtG,cAAc;AAsSpB,eAAeA,cAAc;AAAC,IAAAf,EAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAuG,GAAA;AAAAC,YAAA,CAAAtH,EAAA;AAAAsH,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}