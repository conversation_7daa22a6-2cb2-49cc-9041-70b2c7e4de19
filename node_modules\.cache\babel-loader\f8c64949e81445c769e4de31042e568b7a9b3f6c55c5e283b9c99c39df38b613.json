{"ast": null, "code": "\"use strict\";\n\nvar window = require(\"global/window\");\nvar _extends = require(\"@babel/runtime/helpers/extends\");\nvar isFunction = require('is-function');\nvar InterceptorsStorage = require('./interceptors.js');\nvar RetryManager = require(\"./retry.js\");\ncreateXHR.httpHandler = require('./http-handler.js');\ncreateXHR.requestInterceptorsStorage = new InterceptorsStorage();\ncreateXHR.responseInterceptorsStorage = new InterceptorsStorage();\ncreateXHR.retryManager = new RetryManager();\n/**\n * @license\n * slighly modified parse-headers 2.0.2 <https://github.com/kesla/parse-headers/>\n * Copyright (c) 2014 David <PERSON>\n * Available under the MIT license\n * <https://github.com/kesla/parse-headers/blob/master/LICENCE>\n */\n\nvar parseHeaders = function parseHeaders(headers) {\n  var result = {};\n  if (!headers) {\n    return result;\n  }\n  headers.trim().split('\\n').forEach(function (row) {\n    var index = row.indexOf(':');\n    var key = row.slice(0, index).trim().toLowerCase();\n    var value = row.slice(index + 1).trim();\n    if (typeof result[key] === 'undefined') {\n      result[key] = value;\n    } else if (Array.isArray(result[key])) {\n      result[key].push(value);\n    } else {\n      result[key] = [result[key], value];\n    }\n  });\n  return result;\n};\nmodule.exports = createXHR; // Allow use of default import syntax in TypeScript\n\nmodule.exports.default = createXHR;\ncreateXHR.XMLHttpRequest = window.XMLHttpRequest || noop;\ncreateXHR.XDomainRequest = \"withCredentials\" in new createXHR.XMLHttpRequest() ? createXHR.XMLHttpRequest : window.XDomainRequest;\nforEachArray([\"get\", \"put\", \"post\", \"patch\", \"head\", \"delete\"], function (method) {\n  createXHR[method === \"delete\" ? \"del\" : method] = function (uri, options, callback) {\n    options = initParams(uri, options, callback);\n    options.method = method.toUpperCase();\n    return _createXHR(options);\n  };\n});\nfunction forEachArray(array, iterator) {\n  for (var i = 0; i < array.length; i++) {\n    iterator(array[i]);\n  }\n}\nfunction isEmpty(obj) {\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) return false;\n  }\n  return true;\n}\nfunction initParams(uri, options, callback) {\n  var params = uri;\n  if (isFunction(options)) {\n    callback = options;\n    if (typeof uri === \"string\") {\n      params = {\n        uri: uri\n      };\n    }\n  } else {\n    params = _extends({}, options, {\n      uri: uri\n    });\n  }\n  params.callback = callback;\n  return params;\n}\nfunction createXHR(uri, options, callback) {\n  options = initParams(uri, options, callback);\n  return _createXHR(options);\n}\nfunction _createXHR(options) {\n  if (typeof options.callback === \"undefined\") {\n    throw new Error(\"callback argument missing\");\n  } // call all registered request interceptors for a given request type:\n\n  if (options.requestType && createXHR.requestInterceptorsStorage.getIsEnabled()) {\n    var requestInterceptorPayload = {\n      uri: options.uri || options.url,\n      headers: options.headers || {},\n      body: options.body,\n      metadata: options.metadata || {},\n      retry: options.retry,\n      timeout: options.timeout\n    };\n    var updatedPayload = createXHR.requestInterceptorsStorage.execute(options.requestType, requestInterceptorPayload);\n    options.uri = updatedPayload.uri;\n    options.headers = updatedPayload.headers;\n    options.body = updatedPayload.body;\n    options.metadata = updatedPayload.metadata;\n    options.retry = updatedPayload.retry;\n    options.timeout = updatedPayload.timeout;\n  }\n  var called = false;\n  var callback = function cbOnce(err, response, body) {\n    if (!called) {\n      called = true;\n      options.callback(err, response, body);\n    }\n  };\n  function readystatechange() {\n    // do not call load 2 times when response interceptors are enabled\n    // why do we even need this 2nd load?\n    if (xhr.readyState === 4 && !createXHR.responseInterceptorsStorage.getIsEnabled()) {\n      setTimeout(loadFunc, 0);\n    }\n  }\n  function getBody() {\n    // Chrome with requestType=blob throws errors arround when even testing access to responseText\n    var body = undefined;\n    if (xhr.response) {\n      body = xhr.response;\n    } else {\n      body = xhr.responseText || getXml(xhr);\n    }\n    if (isJson) {\n      try {\n        body = JSON.parse(body);\n      } catch (e) {}\n    }\n    return body;\n  }\n  function errorFunc(evt) {\n    clearTimeout(timeoutTimer);\n    clearTimeout(options.retryTimeout);\n    if (!(evt instanceof Error)) {\n      evt = new Error(\"\" + (evt || \"Unknown XMLHttpRequest Error\"));\n    }\n    evt.statusCode = 0; // we would like to retry on error:\n\n    if (!aborted && createXHR.retryManager.getIsEnabled() && options.retry && options.retry.shouldRetry()) {\n      options.retryTimeout = setTimeout(function () {\n        options.retry.moveToNextAttempt(); // we want to re-use the same options and the same xhr object:\n\n        options.xhr = xhr;\n        _createXHR(options);\n      }, options.retry.getCurrentFuzzedDelay());\n      return;\n    } // call all registered response interceptors for a given request type:\n\n    if (options.requestType && createXHR.responseInterceptorsStorage.getIsEnabled()) {\n      var responseInterceptorPayload = {\n        headers: failureResponse.headers || {},\n        body: failureResponse.body,\n        responseUrl: xhr.responseURL,\n        responseType: xhr.responseType\n      };\n      var _updatedPayload = createXHR.responseInterceptorsStorage.execute(options.requestType, responseInterceptorPayload);\n      failureResponse.body = _updatedPayload.body;\n      failureResponse.headers = _updatedPayload.headers;\n    }\n    return callback(evt, failureResponse);\n  } // will load the data & process the response in a special response object\n\n  function loadFunc() {\n    if (aborted) return;\n    var status;\n    clearTimeout(timeoutTimer);\n    clearTimeout(options.retryTimeout);\n    if (options.useXDR && xhr.status === undefined) {\n      //IE8 CORS GET successful response doesn't have a status field, but body is fine\n      status = 200;\n    } else {\n      status = xhr.status === 1223 ? 204 : xhr.status;\n    }\n    var response = failureResponse;\n    var err = null;\n    if (status !== 0) {\n      response = {\n        body: getBody(),\n        statusCode: status,\n        method: method,\n        headers: {},\n        url: uri,\n        rawRequest: xhr\n      };\n      if (xhr.getAllResponseHeaders) {\n        //remember xhr can in fact be XDR for CORS in IE\n        response.headers = parseHeaders(xhr.getAllResponseHeaders());\n      }\n    } else {\n      err = new Error(\"Internal XMLHttpRequest Error\");\n    } // call all registered response interceptors for a given request type:\n\n    if (options.requestType && createXHR.responseInterceptorsStorage.getIsEnabled()) {\n      var responseInterceptorPayload = {\n        headers: response.headers || {},\n        body: response.body,\n        responseUrl: xhr.responseURL,\n        responseType: xhr.responseType\n      };\n      var _updatedPayload2 = createXHR.responseInterceptorsStorage.execute(options.requestType, responseInterceptorPayload);\n      response.body = _updatedPayload2.body;\n      response.headers = _updatedPayload2.headers;\n    }\n    return callback(err, response, response.body);\n  }\n  var xhr = options.xhr || null;\n  if (!xhr) {\n    if (options.cors || options.useXDR) {\n      xhr = new createXHR.XDomainRequest();\n    } else {\n      xhr = new createXHR.XMLHttpRequest();\n    }\n  }\n  var key;\n  var aborted;\n  var uri = xhr.url = options.uri || options.url;\n  var method = xhr.method = options.method || \"GET\";\n  var body = options.body || options.data;\n  var headers = xhr.headers = options.headers || {};\n  var sync = !!options.sync;\n  var isJson = false;\n  var timeoutTimer;\n  var failureResponse = {\n    body: undefined,\n    headers: {},\n    statusCode: 0,\n    method: method,\n    url: uri,\n    rawRequest: xhr\n  };\n  if (\"json\" in options && options.json !== false) {\n    isJson = true;\n    headers[\"accept\"] || headers[\"Accept\"] || (headers[\"Accept\"] = \"application/json\"); //Don't override existing accept header declared by user\n\n    if (method !== \"GET\" && method !== \"HEAD\") {\n      headers[\"content-type\"] || headers[\"Content-Type\"] || (headers[\"Content-Type\"] = \"application/json\"); //Don't override existing accept header declared by user\n\n      body = JSON.stringify(options.json === true ? body : options.json);\n    }\n  }\n  xhr.onreadystatechange = readystatechange;\n  xhr.onload = loadFunc;\n  xhr.onerror = errorFunc; // IE9 must have onprogress be set to a unique function.\n\n  xhr.onprogress = function () {// IE must die\n  };\n  xhr.onabort = function () {\n    aborted = true;\n    clearTimeout(options.retryTimeout);\n  };\n  xhr.ontimeout = errorFunc;\n  xhr.open(method, uri, !sync, options.username, options.password); //has to be after open\n\n  if (!sync) {\n    xhr.withCredentials = !!options.withCredentials;\n  } // Cannot set timeout with sync request\n  // not setting timeout on the xhr object, because of old webkits etc. not handling that correctly\n  // both npm's request and jquery 1.x use this kind of timeout, so this is being consistent\n\n  if (!sync && options.timeout > 0) {\n    timeoutTimer = setTimeout(function () {\n      if (aborted) return;\n      aborted = true; //IE9 may still call readystatechange\n\n      xhr.abort(\"timeout\");\n      var e = new Error(\"XMLHttpRequest timeout\");\n      e.code = \"ETIMEDOUT\";\n      errorFunc(e);\n    }, options.timeout);\n  }\n  if (xhr.setRequestHeader) {\n    for (key in headers) {\n      if (headers.hasOwnProperty(key)) {\n        xhr.setRequestHeader(key, headers[key]);\n      }\n    }\n  } else if (options.headers && !isEmpty(options.headers)) {\n    throw new Error(\"Headers cannot be set on an XDomainRequest object\");\n  }\n  if (\"responseType\" in options) {\n    xhr.responseType = options.responseType;\n  }\n  if (\"beforeSend\" in options && typeof options.beforeSend === \"function\") {\n    options.beforeSend(xhr);\n  } // Microsoft Edge browser sends \"undefined\" when send is called with undefined value.\n  // XMLHttpRequest spec says to pass null as body to indicate no body\n  // See https://github.com/naugtur/xhr/issues/100.\n\n  xhr.send(body || null);\n  return xhr;\n}\nfunction getXml(xhr) {\n  // xhr.responseXML will throw Exception \"InvalidStateError\" or \"DOMException\"\n  // See https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/responseXML.\n  try {\n    if (xhr.responseType === \"document\") {\n      return xhr.responseXML;\n    }\n    var firefoxBugTakenEffect = xhr.responseXML && xhr.responseXML.documentElement.nodeName === \"parsererror\";\n    if (xhr.responseType === \"\" && !firefoxBugTakenEffect) {\n      return xhr.responseXML;\n    }\n  } catch (e) {}\n  return null;\n}\nfunction noop() {}", "map": {"version": 3, "names": ["window", "require", "_extends", "isFunction", "InterceptorsStorage", "RetryManager", "createXHR", "httpHandler", "requestInterceptorsStorage", "responseInterceptorsStorage", "retryManager", "parseHeaders", "headers", "result", "trim", "split", "for<PERSON>ach", "row", "index", "indexOf", "key", "slice", "toLowerCase", "value", "Array", "isArray", "push", "module", "exports", "default", "XMLHttpRequest", "noop", "XDomainRequest", "forEachArray", "method", "uri", "options", "callback", "initParams", "toUpperCase", "_createXHR", "array", "iterator", "i", "length", "isEmpty", "obj", "hasOwnProperty", "params", "Error", "requestType", "getIsEnabled", "requestInterceptorPayload", "url", "body", "metadata", "retry", "timeout", "updatedPayload", "execute", "called", "cbOnce", "err", "response", "readystatechange", "xhr", "readyState", "setTimeout", "loadFunc", "getBody", "undefined", "responseText", "getXml", "isJson", "JSON", "parse", "e", "errorFunc", "evt", "clearTimeout", "timeoutTimer", "retryTimeout", "statusCode", "aborted", "shouldRetry", "moveToNextAttempt", "getCurrentFuzzedDelay", "responseInterceptorPayload", "failureResponse", "responseUrl", "responseURL", "responseType", "_updatedPayload", "status", "useXDR", "rawRequest", "getAllResponseHeaders", "_updatedPayload2", "cors", "data", "sync", "json", "stringify", "onreadystatechange", "onload", "onerror", "onprogress", "<PERSON>ab<PERSON>", "ontimeout", "open", "username", "password", "withCredentials", "abort", "code", "setRequestHeader", "beforeSend", "send", "responseXML", "firefoxBugTakenEffect", "documentElement", "nodeName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@videojs/xhr/lib/index.js"], "sourcesContent": ["\"use strict\";\n\nvar window = require(\"global/window\");\n\nvar _extends = require(\"@babel/runtime/helpers/extends\");\n\nvar isFunction = require('is-function');\n\nvar InterceptorsStorage = require('./interceptors.js');\n\nvar RetryManager = require(\"./retry.js\");\n\ncreateXHR.httpHandler = require('./http-handler.js');\ncreateXHR.requestInterceptorsStorage = new InterceptorsStorage();\ncreateXHR.responseInterceptorsStorage = new InterceptorsStorage();\ncreateXHR.retryManager = new RetryManager();\n/**\n * @license\n * slighly modified parse-headers 2.0.2 <https://github.com/kesla/parse-headers/>\n * Copyright (c) 2014 David <PERSON>\n * Available under the MIT license\n * <https://github.com/kesla/parse-headers/blob/master/LICENCE>\n */\n\nvar parseHeaders = function parseHeaders(headers) {\n  var result = {};\n\n  if (!headers) {\n    return result;\n  }\n\n  headers.trim().split('\\n').forEach(function (row) {\n    var index = row.indexOf(':');\n    var key = row.slice(0, index).trim().toLowerCase();\n    var value = row.slice(index + 1).trim();\n\n    if (typeof result[key] === 'undefined') {\n      result[key] = value;\n    } else if (Array.isArray(result[key])) {\n      result[key].push(value);\n    } else {\n      result[key] = [result[key], value];\n    }\n  });\n  return result;\n};\n\nmodule.exports = createXHR; // Allow use of default import syntax in TypeScript\n\nmodule.exports.default = createXHR;\ncreateXHR.XMLHttpRequest = window.XMLHttpRequest || noop;\ncreateXHR.XDomainRequest = \"withCredentials\" in new createXHR.XMLHttpRequest() ? createXHR.XMLHttpRequest : window.XDomainRequest;\nforEachArray([\"get\", \"put\", \"post\", \"patch\", \"head\", \"delete\"], function (method) {\n  createXHR[method === \"delete\" ? \"del\" : method] = function (uri, options, callback) {\n    options = initParams(uri, options, callback);\n    options.method = method.toUpperCase();\n    return _createXHR(options);\n  };\n});\n\nfunction forEachArray(array, iterator) {\n  for (var i = 0; i < array.length; i++) {\n    iterator(array[i]);\n  }\n}\n\nfunction isEmpty(obj) {\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) return false;\n  }\n\n  return true;\n}\n\nfunction initParams(uri, options, callback) {\n  var params = uri;\n\n  if (isFunction(options)) {\n    callback = options;\n\n    if (typeof uri === \"string\") {\n      params = {\n        uri: uri\n      };\n    }\n  } else {\n    params = _extends({}, options, {\n      uri: uri\n    });\n  }\n\n  params.callback = callback;\n  return params;\n}\n\nfunction createXHR(uri, options, callback) {\n  options = initParams(uri, options, callback);\n  return _createXHR(options);\n}\n\nfunction _createXHR(options) {\n  if (typeof options.callback === \"undefined\") {\n    throw new Error(\"callback argument missing\");\n  } // call all registered request interceptors for a given request type:\n\n\n  if (options.requestType && createXHR.requestInterceptorsStorage.getIsEnabled()) {\n    var requestInterceptorPayload = {\n      uri: options.uri || options.url,\n      headers: options.headers || {},\n      body: options.body,\n      metadata: options.metadata || {},\n      retry: options.retry,\n      timeout: options.timeout\n    };\n    var updatedPayload = createXHR.requestInterceptorsStorage.execute(options.requestType, requestInterceptorPayload);\n    options.uri = updatedPayload.uri;\n    options.headers = updatedPayload.headers;\n    options.body = updatedPayload.body;\n    options.metadata = updatedPayload.metadata;\n    options.retry = updatedPayload.retry;\n    options.timeout = updatedPayload.timeout;\n  }\n\n  var called = false;\n\n  var callback = function cbOnce(err, response, body) {\n    if (!called) {\n      called = true;\n      options.callback(err, response, body);\n    }\n  };\n\n  function readystatechange() {\n    // do not call load 2 times when response interceptors are enabled\n    // why do we even need this 2nd load?\n    if (xhr.readyState === 4 && !createXHR.responseInterceptorsStorage.getIsEnabled()) {\n      setTimeout(loadFunc, 0);\n    }\n  }\n\n  function getBody() {\n    // Chrome with requestType=blob throws errors arround when even testing access to responseText\n    var body = undefined;\n\n    if (xhr.response) {\n      body = xhr.response;\n    } else {\n      body = xhr.responseText || getXml(xhr);\n    }\n\n    if (isJson) {\n      try {\n        body = JSON.parse(body);\n      } catch (e) {}\n    }\n\n    return body;\n  }\n\n  function errorFunc(evt) {\n    clearTimeout(timeoutTimer);\n    clearTimeout(options.retryTimeout);\n\n    if (!(evt instanceof Error)) {\n      evt = new Error(\"\" + (evt || \"Unknown XMLHttpRequest Error\"));\n    }\n\n    evt.statusCode = 0; // we would like to retry on error:\n\n    if (!aborted && createXHR.retryManager.getIsEnabled() && options.retry && options.retry.shouldRetry()) {\n      options.retryTimeout = setTimeout(function () {\n        options.retry.moveToNextAttempt(); // we want to re-use the same options and the same xhr object:\n\n        options.xhr = xhr;\n\n        _createXHR(options);\n      }, options.retry.getCurrentFuzzedDelay());\n      return;\n    } // call all registered response interceptors for a given request type:\n\n\n    if (options.requestType && createXHR.responseInterceptorsStorage.getIsEnabled()) {\n      var responseInterceptorPayload = {\n        headers: failureResponse.headers || {},\n        body: failureResponse.body,\n        responseUrl: xhr.responseURL,\n        responseType: xhr.responseType\n      };\n\n      var _updatedPayload = createXHR.responseInterceptorsStorage.execute(options.requestType, responseInterceptorPayload);\n\n      failureResponse.body = _updatedPayload.body;\n      failureResponse.headers = _updatedPayload.headers;\n    }\n\n    return callback(evt, failureResponse);\n  } // will load the data & process the response in a special response object\n\n\n  function loadFunc() {\n    if (aborted) return;\n    var status;\n    clearTimeout(timeoutTimer);\n    clearTimeout(options.retryTimeout);\n\n    if (options.useXDR && xhr.status === undefined) {\n      //IE8 CORS GET successful response doesn't have a status field, but body is fine\n      status = 200;\n    } else {\n      status = xhr.status === 1223 ? 204 : xhr.status;\n    }\n\n    var response = failureResponse;\n    var err = null;\n\n    if (status !== 0) {\n      response = {\n        body: getBody(),\n        statusCode: status,\n        method: method,\n        headers: {},\n        url: uri,\n        rawRequest: xhr\n      };\n\n      if (xhr.getAllResponseHeaders) {\n        //remember xhr can in fact be XDR for CORS in IE\n        response.headers = parseHeaders(xhr.getAllResponseHeaders());\n      }\n    } else {\n      err = new Error(\"Internal XMLHttpRequest Error\");\n    } // call all registered response interceptors for a given request type:\n\n\n    if (options.requestType && createXHR.responseInterceptorsStorage.getIsEnabled()) {\n      var responseInterceptorPayload = {\n        headers: response.headers || {},\n        body: response.body,\n        responseUrl: xhr.responseURL,\n        responseType: xhr.responseType\n      };\n\n      var _updatedPayload2 = createXHR.responseInterceptorsStorage.execute(options.requestType, responseInterceptorPayload);\n\n      response.body = _updatedPayload2.body;\n      response.headers = _updatedPayload2.headers;\n    }\n\n    return callback(err, response, response.body);\n  }\n\n  var xhr = options.xhr || null;\n\n  if (!xhr) {\n    if (options.cors || options.useXDR) {\n      xhr = new createXHR.XDomainRequest();\n    } else {\n      xhr = new createXHR.XMLHttpRequest();\n    }\n  }\n\n  var key;\n  var aborted;\n  var uri = xhr.url = options.uri || options.url;\n  var method = xhr.method = options.method || \"GET\";\n  var body = options.body || options.data;\n  var headers = xhr.headers = options.headers || {};\n  var sync = !!options.sync;\n  var isJson = false;\n  var timeoutTimer;\n  var failureResponse = {\n    body: undefined,\n    headers: {},\n    statusCode: 0,\n    method: method,\n    url: uri,\n    rawRequest: xhr\n  };\n\n  if (\"json\" in options && options.json !== false) {\n    isJson = true;\n    headers[\"accept\"] || headers[\"Accept\"] || (headers[\"Accept\"] = \"application/json\"); //Don't override existing accept header declared by user\n\n    if (method !== \"GET\" && method !== \"HEAD\") {\n      headers[\"content-type\"] || headers[\"Content-Type\"] || (headers[\"Content-Type\"] = \"application/json\"); //Don't override existing accept header declared by user\n\n      body = JSON.stringify(options.json === true ? body : options.json);\n    }\n  }\n\n  xhr.onreadystatechange = readystatechange;\n  xhr.onload = loadFunc;\n  xhr.onerror = errorFunc; // IE9 must have onprogress be set to a unique function.\n\n  xhr.onprogress = function () {// IE must die\n  };\n\n  xhr.onabort = function () {\n    aborted = true;\n    clearTimeout(options.retryTimeout);\n  };\n\n  xhr.ontimeout = errorFunc;\n  xhr.open(method, uri, !sync, options.username, options.password); //has to be after open\n\n  if (!sync) {\n    xhr.withCredentials = !!options.withCredentials;\n  } // Cannot set timeout with sync request\n  // not setting timeout on the xhr object, because of old webkits etc. not handling that correctly\n  // both npm's request and jquery 1.x use this kind of timeout, so this is being consistent\n\n\n  if (!sync && options.timeout > 0) {\n    timeoutTimer = setTimeout(function () {\n      if (aborted) return;\n      aborted = true; //IE9 may still call readystatechange\n\n      xhr.abort(\"timeout\");\n      var e = new Error(\"XMLHttpRequest timeout\");\n      e.code = \"ETIMEDOUT\";\n      errorFunc(e);\n    }, options.timeout);\n  }\n\n  if (xhr.setRequestHeader) {\n    for (key in headers) {\n      if (headers.hasOwnProperty(key)) {\n        xhr.setRequestHeader(key, headers[key]);\n      }\n    }\n  } else if (options.headers && !isEmpty(options.headers)) {\n    throw new Error(\"Headers cannot be set on an XDomainRequest object\");\n  }\n\n  if (\"responseType\" in options) {\n    xhr.responseType = options.responseType;\n  }\n\n  if (\"beforeSend\" in options && typeof options.beforeSend === \"function\") {\n    options.beforeSend(xhr);\n  } // Microsoft Edge browser sends \"undefined\" when send is called with undefined value.\n  // XMLHttpRequest spec says to pass null as body to indicate no body\n  // See https://github.com/naugtur/xhr/issues/100.\n\n\n  xhr.send(body || null);\n  return xhr;\n}\n\nfunction getXml(xhr) {\n  // xhr.responseXML will throw Exception \"InvalidStateError\" or \"DOMException\"\n  // See https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/responseXML.\n  try {\n    if (xhr.responseType === \"document\") {\n      return xhr.responseXML;\n    }\n\n    var firefoxBugTakenEffect = xhr.responseXML && xhr.responseXML.documentElement.nodeName === \"parsererror\";\n\n    if (xhr.responseType === \"\" && !firefoxBugTakenEffect) {\n      return xhr.responseXML;\n    }\n  } catch (e) {}\n\n  return null;\n}\n\nfunction noop() {}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,eAAe,CAAC;AAErC,IAAIC,QAAQ,GAAGD,OAAO,CAAC,gCAAgC,CAAC;AAExD,IAAIE,UAAU,GAAGF,OAAO,CAAC,aAAa,CAAC;AAEvC,IAAIG,mBAAmB,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AAEtD,IAAII,YAAY,GAAGJ,OAAO,CAAC,YAAY,CAAC;AAExCK,SAAS,CAACC,WAAW,GAAGN,OAAO,CAAC,mBAAmB,CAAC;AACpDK,SAAS,CAACE,0BAA0B,GAAG,IAAIJ,mBAAmB,CAAC,CAAC;AAChEE,SAAS,CAACG,2BAA2B,GAAG,IAAIL,mBAAmB,CAAC,CAAC;AACjEE,SAAS,CAACI,YAAY,GAAG,IAAIL,YAAY,CAAC,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIM,YAAY,GAAG,SAASA,YAAYA,CAACC,OAAO,EAAE;EAChD,IAAIC,MAAM,GAAG,CAAC,CAAC;EAEf,IAAI,CAACD,OAAO,EAAE;IACZ,OAAOC,MAAM;EACf;EAEAD,OAAO,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;IAChD,IAAIC,KAAK,GAAGD,GAAG,CAACE,OAAO,CAAC,GAAG,CAAC;IAC5B,IAAIC,GAAG,GAAGH,GAAG,CAACI,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC,CAACJ,IAAI,CAAC,CAAC,CAACQ,WAAW,CAAC,CAAC;IAClD,IAAIC,KAAK,GAAGN,GAAG,CAACI,KAAK,CAACH,KAAK,GAAG,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC;IAEvC,IAAI,OAAOD,MAAM,CAACO,GAAG,CAAC,KAAK,WAAW,EAAE;MACtCP,MAAM,CAACO,GAAG,CAAC,GAAGG,KAAK;IACrB,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACZ,MAAM,CAACO,GAAG,CAAC,CAAC,EAAE;MACrCP,MAAM,CAACO,GAAG,CAAC,CAACM,IAAI,CAACH,KAAK,CAAC;IACzB,CAAC,MAAM;MACLV,MAAM,CAACO,GAAG,CAAC,GAAG,CAACP,MAAM,CAACO,GAAG,CAAC,EAAEG,KAAK,CAAC;IACpC;EACF,CAAC,CAAC;EACF,OAAOV,MAAM;AACf,CAAC;AAEDc,MAAM,CAACC,OAAO,GAAGtB,SAAS,CAAC,CAAC;;AAE5BqB,MAAM,CAACC,OAAO,CAACC,OAAO,GAAGvB,SAAS;AAClCA,SAAS,CAACwB,cAAc,GAAG9B,MAAM,CAAC8B,cAAc,IAAIC,IAAI;AACxDzB,SAAS,CAAC0B,cAAc,GAAG,iBAAiB,IAAI,IAAI1B,SAAS,CAACwB,cAAc,CAAC,CAAC,GAAGxB,SAAS,CAACwB,cAAc,GAAG9B,MAAM,CAACgC,cAAc;AACjIC,YAAY,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,UAAUC,MAAM,EAAE;EAChF5B,SAAS,CAAC4B,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAGA,MAAM,CAAC,GAAG,UAAUC,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IAClFD,OAAO,GAAGE,UAAU,CAACH,GAAG,EAAEC,OAAO,EAAEC,QAAQ,CAAC;IAC5CD,OAAO,CAACF,MAAM,GAAGA,MAAM,CAACK,WAAW,CAAC,CAAC;IACrC,OAAOC,UAAU,CAACJ,OAAO,CAAC;EAC5B,CAAC;AACH,CAAC,CAAC;AAEF,SAASH,YAAYA,CAACQ,KAAK,EAAEC,QAAQ,EAAE;EACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACrCD,QAAQ,CAACD,KAAK,CAACE,CAAC,CAAC,CAAC;EACpB;AACF;AAEA,SAASE,OAAOA,CAACC,GAAG,EAAE;EACpB,KAAK,IAAIH,CAAC,IAAIG,GAAG,EAAE;IACjB,IAAIA,GAAG,CAACC,cAAc,CAACJ,CAAC,CAAC,EAAE,OAAO,KAAK;EACzC;EAEA,OAAO,IAAI;AACb;AAEA,SAASL,UAAUA,CAACH,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAC1C,IAAIW,MAAM,GAAGb,GAAG;EAEhB,IAAIhC,UAAU,CAACiC,OAAO,CAAC,EAAE;IACvBC,QAAQ,GAAGD,OAAO;IAElB,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;MAC3Ba,MAAM,GAAG;QACPb,GAAG,EAAEA;MACP,CAAC;IACH;EACF,CAAC,MAAM;IACLa,MAAM,GAAG9C,QAAQ,CAAC,CAAC,CAAC,EAAEkC,OAAO,EAAE;MAC7BD,GAAG,EAAEA;IACP,CAAC,CAAC;EACJ;EAEAa,MAAM,CAACX,QAAQ,GAAGA,QAAQ;EAC1B,OAAOW,MAAM;AACf;AAEA,SAAS1C,SAASA,CAAC6B,GAAG,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACzCD,OAAO,GAAGE,UAAU,CAACH,GAAG,EAAEC,OAAO,EAAEC,QAAQ,CAAC;EAC5C,OAAOG,UAAU,CAACJ,OAAO,CAAC;AAC5B;AAEA,SAASI,UAAUA,CAACJ,OAAO,EAAE;EAC3B,IAAI,OAAOA,OAAO,CAACC,QAAQ,KAAK,WAAW,EAAE;IAC3C,MAAM,IAAIY,KAAK,CAAC,2BAA2B,CAAC;EAC9C,CAAC,CAAC;;EAGF,IAAIb,OAAO,CAACc,WAAW,IAAI5C,SAAS,CAACE,0BAA0B,CAAC2C,YAAY,CAAC,CAAC,EAAE;IAC9E,IAAIC,yBAAyB,GAAG;MAC9BjB,GAAG,EAAEC,OAAO,CAACD,GAAG,IAAIC,OAAO,CAACiB,GAAG;MAC/BzC,OAAO,EAAEwB,OAAO,CAACxB,OAAO,IAAI,CAAC,CAAC;MAC9B0C,IAAI,EAAElB,OAAO,CAACkB,IAAI;MAClBC,QAAQ,EAAEnB,OAAO,CAACmB,QAAQ,IAAI,CAAC,CAAC;MAChCC,KAAK,EAAEpB,OAAO,CAACoB,KAAK;MACpBC,OAAO,EAAErB,OAAO,CAACqB;IACnB,CAAC;IACD,IAAIC,cAAc,GAAGpD,SAAS,CAACE,0BAA0B,CAACmD,OAAO,CAACvB,OAAO,CAACc,WAAW,EAAEE,yBAAyB,CAAC;IACjHhB,OAAO,CAACD,GAAG,GAAGuB,cAAc,CAACvB,GAAG;IAChCC,OAAO,CAACxB,OAAO,GAAG8C,cAAc,CAAC9C,OAAO;IACxCwB,OAAO,CAACkB,IAAI,GAAGI,cAAc,CAACJ,IAAI;IAClClB,OAAO,CAACmB,QAAQ,GAAGG,cAAc,CAACH,QAAQ;IAC1CnB,OAAO,CAACoB,KAAK,GAAGE,cAAc,CAACF,KAAK;IACpCpB,OAAO,CAACqB,OAAO,GAAGC,cAAc,CAACD,OAAO;EAC1C;EAEA,IAAIG,MAAM,GAAG,KAAK;EAElB,IAAIvB,QAAQ,GAAG,SAASwB,MAAMA,CAACC,GAAG,EAAEC,QAAQ,EAAET,IAAI,EAAE;IAClD,IAAI,CAACM,MAAM,EAAE;MACXA,MAAM,GAAG,IAAI;MACbxB,OAAO,CAACC,QAAQ,CAACyB,GAAG,EAAEC,QAAQ,EAAET,IAAI,CAAC;IACvC;EACF,CAAC;EAED,SAASU,gBAAgBA,CAAA,EAAG;IAC1B;IACA;IACA,IAAIC,GAAG,CAACC,UAAU,KAAK,CAAC,IAAI,CAAC5D,SAAS,CAACG,2BAA2B,CAAC0C,YAAY,CAAC,CAAC,EAAE;MACjFgB,UAAU,CAACC,QAAQ,EAAE,CAAC,CAAC;IACzB;EACF;EAEA,SAASC,OAAOA,CAAA,EAAG;IACjB;IACA,IAAIf,IAAI,GAAGgB,SAAS;IAEpB,IAAIL,GAAG,CAACF,QAAQ,EAAE;MAChBT,IAAI,GAAGW,GAAG,CAACF,QAAQ;IACrB,CAAC,MAAM;MACLT,IAAI,GAAGW,GAAG,CAACM,YAAY,IAAIC,MAAM,CAACP,GAAG,CAAC;IACxC;IAEA,IAAIQ,MAAM,EAAE;MACV,IAAI;QACFnB,IAAI,GAAGoB,IAAI,CAACC,KAAK,CAACrB,IAAI,CAAC;MACzB,CAAC,CAAC,OAAOsB,CAAC,EAAE,CAAC;IACf;IAEA,OAAOtB,IAAI;EACb;EAEA,SAASuB,SAASA,CAACC,GAAG,EAAE;IACtBC,YAAY,CAACC,YAAY,CAAC;IAC1BD,YAAY,CAAC3C,OAAO,CAAC6C,YAAY,CAAC;IAElC,IAAI,EAAEH,GAAG,YAAY7B,KAAK,CAAC,EAAE;MAC3B6B,GAAG,GAAG,IAAI7B,KAAK,CAAC,EAAE,IAAI6B,GAAG,IAAI,8BAA8B,CAAC,CAAC;IAC/D;IAEAA,GAAG,CAACI,UAAU,GAAG,CAAC,CAAC,CAAC;;IAEpB,IAAI,CAACC,OAAO,IAAI7E,SAAS,CAACI,YAAY,CAACyC,YAAY,CAAC,CAAC,IAAIf,OAAO,CAACoB,KAAK,IAAIpB,OAAO,CAACoB,KAAK,CAAC4B,WAAW,CAAC,CAAC,EAAE;MACrGhD,OAAO,CAAC6C,YAAY,GAAGd,UAAU,CAAC,YAAY;QAC5C/B,OAAO,CAACoB,KAAK,CAAC6B,iBAAiB,CAAC,CAAC,CAAC,CAAC;;QAEnCjD,OAAO,CAAC6B,GAAG,GAAGA,GAAG;QAEjBzB,UAAU,CAACJ,OAAO,CAAC;MACrB,CAAC,EAAEA,OAAO,CAACoB,KAAK,CAAC8B,qBAAqB,CAAC,CAAC,CAAC;MACzC;IACF,CAAC,CAAC;;IAGF,IAAIlD,OAAO,CAACc,WAAW,IAAI5C,SAAS,CAACG,2BAA2B,CAAC0C,YAAY,CAAC,CAAC,EAAE;MAC/E,IAAIoC,0BAA0B,GAAG;QAC/B3E,OAAO,EAAE4E,eAAe,CAAC5E,OAAO,IAAI,CAAC,CAAC;QACtC0C,IAAI,EAAEkC,eAAe,CAAClC,IAAI;QAC1BmC,WAAW,EAAExB,GAAG,CAACyB,WAAW;QAC5BC,YAAY,EAAE1B,GAAG,CAAC0B;MACpB,CAAC;MAED,IAAIC,eAAe,GAAGtF,SAAS,CAACG,2BAA2B,CAACkD,OAAO,CAACvB,OAAO,CAACc,WAAW,EAAEqC,0BAA0B,CAAC;MAEpHC,eAAe,CAAClC,IAAI,GAAGsC,eAAe,CAACtC,IAAI;MAC3CkC,eAAe,CAAC5E,OAAO,GAAGgF,eAAe,CAAChF,OAAO;IACnD;IAEA,OAAOyB,QAAQ,CAACyC,GAAG,EAAEU,eAAe,CAAC;EACvC,CAAC,CAAC;;EAGF,SAASpB,QAAQA,CAAA,EAAG;IAClB,IAAIe,OAAO,EAAE;IACb,IAAIU,MAAM;IACVd,YAAY,CAACC,YAAY,CAAC;IAC1BD,YAAY,CAAC3C,OAAO,CAAC6C,YAAY,CAAC;IAElC,IAAI7C,OAAO,CAAC0D,MAAM,IAAI7B,GAAG,CAAC4B,MAAM,KAAKvB,SAAS,EAAE;MAC9C;MACAuB,MAAM,GAAG,GAAG;IACd,CAAC,MAAM;MACLA,MAAM,GAAG5B,GAAG,CAAC4B,MAAM,KAAK,IAAI,GAAG,GAAG,GAAG5B,GAAG,CAAC4B,MAAM;IACjD;IAEA,IAAI9B,QAAQ,GAAGyB,eAAe;IAC9B,IAAI1B,GAAG,GAAG,IAAI;IAEd,IAAI+B,MAAM,KAAK,CAAC,EAAE;MAChB9B,QAAQ,GAAG;QACTT,IAAI,EAAEe,OAAO,CAAC,CAAC;QACfa,UAAU,EAAEW,MAAM;QAClB3D,MAAM,EAAEA,MAAM;QACdtB,OAAO,EAAE,CAAC,CAAC;QACXyC,GAAG,EAAElB,GAAG;QACR4D,UAAU,EAAE9B;MACd,CAAC;MAED,IAAIA,GAAG,CAAC+B,qBAAqB,EAAE;QAC7B;QACAjC,QAAQ,CAACnD,OAAO,GAAGD,YAAY,CAACsD,GAAG,CAAC+B,qBAAqB,CAAC,CAAC,CAAC;MAC9D;IACF,CAAC,MAAM;MACLlC,GAAG,GAAG,IAAIb,KAAK,CAAC,+BAA+B,CAAC;IAClD,CAAC,CAAC;;IAGF,IAAIb,OAAO,CAACc,WAAW,IAAI5C,SAAS,CAACG,2BAA2B,CAAC0C,YAAY,CAAC,CAAC,EAAE;MAC/E,IAAIoC,0BAA0B,GAAG;QAC/B3E,OAAO,EAAEmD,QAAQ,CAACnD,OAAO,IAAI,CAAC,CAAC;QAC/B0C,IAAI,EAAES,QAAQ,CAACT,IAAI;QACnBmC,WAAW,EAAExB,GAAG,CAACyB,WAAW;QAC5BC,YAAY,EAAE1B,GAAG,CAAC0B;MACpB,CAAC;MAED,IAAIM,gBAAgB,GAAG3F,SAAS,CAACG,2BAA2B,CAACkD,OAAO,CAACvB,OAAO,CAACc,WAAW,EAAEqC,0BAA0B,CAAC;MAErHxB,QAAQ,CAACT,IAAI,GAAG2C,gBAAgB,CAAC3C,IAAI;MACrCS,QAAQ,CAACnD,OAAO,GAAGqF,gBAAgB,CAACrF,OAAO;IAC7C;IAEA,OAAOyB,QAAQ,CAACyB,GAAG,EAAEC,QAAQ,EAAEA,QAAQ,CAACT,IAAI,CAAC;EAC/C;EAEA,IAAIW,GAAG,GAAG7B,OAAO,CAAC6B,GAAG,IAAI,IAAI;EAE7B,IAAI,CAACA,GAAG,EAAE;IACR,IAAI7B,OAAO,CAAC8D,IAAI,IAAI9D,OAAO,CAAC0D,MAAM,EAAE;MAClC7B,GAAG,GAAG,IAAI3D,SAAS,CAAC0B,cAAc,CAAC,CAAC;IACtC,CAAC,MAAM;MACLiC,GAAG,GAAG,IAAI3D,SAAS,CAACwB,cAAc,CAAC,CAAC;IACtC;EACF;EAEA,IAAIV,GAAG;EACP,IAAI+D,OAAO;EACX,IAAIhD,GAAG,GAAG8B,GAAG,CAACZ,GAAG,GAAGjB,OAAO,CAACD,GAAG,IAAIC,OAAO,CAACiB,GAAG;EAC9C,IAAInB,MAAM,GAAG+B,GAAG,CAAC/B,MAAM,GAAGE,OAAO,CAACF,MAAM,IAAI,KAAK;EACjD,IAAIoB,IAAI,GAAGlB,OAAO,CAACkB,IAAI,IAAIlB,OAAO,CAAC+D,IAAI;EACvC,IAAIvF,OAAO,GAAGqD,GAAG,CAACrD,OAAO,GAAGwB,OAAO,CAACxB,OAAO,IAAI,CAAC,CAAC;EACjD,IAAIwF,IAAI,GAAG,CAAC,CAAChE,OAAO,CAACgE,IAAI;EACzB,IAAI3B,MAAM,GAAG,KAAK;EAClB,IAAIO,YAAY;EAChB,IAAIQ,eAAe,GAAG;IACpBlC,IAAI,EAAEgB,SAAS;IACf1D,OAAO,EAAE,CAAC,CAAC;IACXsE,UAAU,EAAE,CAAC;IACbhD,MAAM,EAAEA,MAAM;IACdmB,GAAG,EAAElB,GAAG;IACR4D,UAAU,EAAE9B;EACd,CAAC;EAED,IAAI,MAAM,IAAI7B,OAAO,IAAIA,OAAO,CAACiE,IAAI,KAAK,KAAK,EAAE;IAC/C5B,MAAM,GAAG,IAAI;IACb7D,OAAO,CAAC,QAAQ,CAAC,IAAIA,OAAO,CAAC,QAAQ,CAAC,KAAKA,OAAO,CAAC,QAAQ,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC;;IAEpF,IAAIsB,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,MAAM,EAAE;MACzCtB,OAAO,CAAC,cAAc,CAAC,IAAIA,OAAO,CAAC,cAAc,CAAC,KAAKA,OAAO,CAAC,cAAc,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC;;MAEtG0C,IAAI,GAAGoB,IAAI,CAAC4B,SAAS,CAAClE,OAAO,CAACiE,IAAI,KAAK,IAAI,GAAG/C,IAAI,GAAGlB,OAAO,CAACiE,IAAI,CAAC;IACpE;EACF;EAEApC,GAAG,CAACsC,kBAAkB,GAAGvC,gBAAgB;EACzCC,GAAG,CAACuC,MAAM,GAAGpC,QAAQ;EACrBH,GAAG,CAACwC,OAAO,GAAG5B,SAAS,CAAC,CAAC;;EAEzBZ,GAAG,CAACyC,UAAU,GAAG,YAAY,CAAC;EAAA,CAC7B;EAEDzC,GAAG,CAAC0C,OAAO,GAAG,YAAY;IACxBxB,OAAO,GAAG,IAAI;IACdJ,YAAY,CAAC3C,OAAO,CAAC6C,YAAY,CAAC;EACpC,CAAC;EAEDhB,GAAG,CAAC2C,SAAS,GAAG/B,SAAS;EACzBZ,GAAG,CAAC4C,IAAI,CAAC3E,MAAM,EAAEC,GAAG,EAAE,CAACiE,IAAI,EAAEhE,OAAO,CAAC0E,QAAQ,EAAE1E,OAAO,CAAC2E,QAAQ,CAAC,CAAC,CAAC;;EAElE,IAAI,CAACX,IAAI,EAAE;IACTnC,GAAG,CAAC+C,eAAe,GAAG,CAAC,CAAC5E,OAAO,CAAC4E,eAAe;EACjD,CAAC,CAAC;EACF;EACA;;EAGA,IAAI,CAACZ,IAAI,IAAIhE,OAAO,CAACqB,OAAO,GAAG,CAAC,EAAE;IAChCuB,YAAY,GAAGb,UAAU,CAAC,YAAY;MACpC,IAAIgB,OAAO,EAAE;MACbA,OAAO,GAAG,IAAI,CAAC,CAAC;;MAEhBlB,GAAG,CAACgD,KAAK,CAAC,SAAS,CAAC;MACpB,IAAIrC,CAAC,GAAG,IAAI3B,KAAK,CAAC,wBAAwB,CAAC;MAC3C2B,CAAC,CAACsC,IAAI,GAAG,WAAW;MACpBrC,SAAS,CAACD,CAAC,CAAC;IACd,CAAC,EAAExC,OAAO,CAACqB,OAAO,CAAC;EACrB;EAEA,IAAIQ,GAAG,CAACkD,gBAAgB,EAAE;IACxB,KAAK/F,GAAG,IAAIR,OAAO,EAAE;MACnB,IAAIA,OAAO,CAACmC,cAAc,CAAC3B,GAAG,CAAC,EAAE;QAC/B6C,GAAG,CAACkD,gBAAgB,CAAC/F,GAAG,EAAER,OAAO,CAACQ,GAAG,CAAC,CAAC;MACzC;IACF;EACF,CAAC,MAAM,IAAIgB,OAAO,CAACxB,OAAO,IAAI,CAACiC,OAAO,CAACT,OAAO,CAACxB,OAAO,CAAC,EAAE;IACvD,MAAM,IAAIqC,KAAK,CAAC,mDAAmD,CAAC;EACtE;EAEA,IAAI,cAAc,IAAIb,OAAO,EAAE;IAC7B6B,GAAG,CAAC0B,YAAY,GAAGvD,OAAO,CAACuD,YAAY;EACzC;EAEA,IAAI,YAAY,IAAIvD,OAAO,IAAI,OAAOA,OAAO,CAACgF,UAAU,KAAK,UAAU,EAAE;IACvEhF,OAAO,CAACgF,UAAU,CAACnD,GAAG,CAAC;EACzB,CAAC,CAAC;EACF;EACA;;EAGAA,GAAG,CAACoD,IAAI,CAAC/D,IAAI,IAAI,IAAI,CAAC;EACtB,OAAOW,GAAG;AACZ;AAEA,SAASO,MAAMA,CAACP,GAAG,EAAE;EACnB;EACA;EACA,IAAI;IACF,IAAIA,GAAG,CAAC0B,YAAY,KAAK,UAAU,EAAE;MACnC,OAAO1B,GAAG,CAACqD,WAAW;IACxB;IAEA,IAAIC,qBAAqB,GAAGtD,GAAG,CAACqD,WAAW,IAAIrD,GAAG,CAACqD,WAAW,CAACE,eAAe,CAACC,QAAQ,KAAK,aAAa;IAEzG,IAAIxD,GAAG,CAAC0B,YAAY,KAAK,EAAE,IAAI,CAAC4B,qBAAqB,EAAE;MACrD,OAAOtD,GAAG,CAACqD,WAAW;IACxB;EACF,CAAC,CAAC,OAAO1C,CAAC,EAAE,CAAC;EAEb,OAAO,IAAI;AACb;AAEA,SAAS7C,IAAIA,CAAA,EAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}