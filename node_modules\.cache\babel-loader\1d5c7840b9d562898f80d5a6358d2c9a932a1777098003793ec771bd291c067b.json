{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { getRenderPropValue } from '../_util/getRenderPropValue';\nimport { getTransitionName } from '../_util/motion';\nimport { cloneElement } from '../_util/reactNode';\nimport Tooltip from '../tooltip';\nimport PurePanel, { Overlay } from './PurePanel';\nimport { useComponentConfig } from '../config-provider/context';\n// CSSINJS\nimport useStyle from './style';\nconst InternalPopover = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a, _b;\n  const {\n      prefixCls: customizePrefixCls,\n      title,\n      content,\n      overlayClassName,\n      placement = 'top',\n      trigger = 'hover',\n      children,\n      mouseEnterDelay = 0.1,\n      mouseLeaveDelay = 0.1,\n      onOpenChange,\n      overlayStyle = {},\n      styles,\n      classNames: popoverClassNames\n    } = props,\n    otherProps = __rest(props, [\"prefixCls\", \"title\", \"content\", \"overlayClassName\", \"placement\", \"trigger\", \"children\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"onOpenChange\", \"overlayStyle\", \"styles\", \"classNames\"]);\n  const {\n    getPrefixCls,\n    className: contextClassName,\n    style: contextStyle,\n    classNames: contextClassNames,\n    styles: contextStyles\n  } = useComponentConfig('popover');\n  const prefixCls = getPrefixCls('popover', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const rootPrefixCls = getPrefixCls();\n  const rootClassNames = classNames(overlayClassName, hashId, cssVarCls, contextClassName, contextClassNames.root, popoverClassNames === null || popoverClassNames === void 0 ? void 0 : popoverClassNames.root);\n  const bodyClassNames = classNames(contextClassNames.body, popoverClassNames === null || popoverClassNames === void 0 ? void 0 : popoverClassNames.body);\n  const [open, setOpen] = useMergedState(false, {\n    value: (_a = props.open) !== null && _a !== void 0 ? _a : props.visible,\n    defaultValue: (_b = props.defaultOpen) !== null && _b !== void 0 ? _b : props.defaultVisible\n  });\n  const settingOpen = (value, e) => {\n    setOpen(value, true);\n    onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(value, e);\n  };\n  const onKeyDown = e => {\n    if (e.keyCode === KeyCode.ESC) {\n      settingOpen(false, e);\n    }\n  };\n  const onInternalOpenChange = value => {\n    settingOpen(value);\n  };\n  const titleNode = getRenderPropValue(title);\n  const contentNode = getRenderPropValue(content);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(Tooltip, Object.assign({\n    placement: placement,\n    trigger: trigger,\n    mouseEnterDelay: mouseEnterDelay,\n    mouseLeaveDelay: mouseLeaveDelay\n  }, otherProps, {\n    prefixCls: prefixCls,\n    classNames: {\n      root: rootClassNames,\n      body: bodyClassNames\n    },\n    styles: {\n      root: Object.assign(Object.assign(Object.assign(Object.assign({}, contextStyles.root), contextStyle), overlayStyle), styles === null || styles === void 0 ? void 0 : styles.root),\n      body: Object.assign(Object.assign({}, contextStyles.body), styles === null || styles === void 0 ? void 0 : styles.body)\n    },\n    ref: ref,\n    open: open,\n    onOpenChange: onInternalOpenChange,\n    overlay: titleNode || contentNode ? (/*#__PURE__*/React.createElement(Overlay, {\n      prefixCls: prefixCls,\n      title: titleNode,\n      content: contentNode\n    })) : null,\n    transitionName: getTransitionName(rootPrefixCls, 'zoom-big', otherProps.transitionName),\n    \"data-popover-inject\": true\n  }), cloneElement(children, {\n    onKeyDown: e => {\n      var _a, _b;\n      if (/*#__PURE__*/React.isValidElement(children)) {\n        (_b = children === null || children === void 0 ? void 0 : (_a = children.props).onKeyDown) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n      }\n      onKeyDown(e);\n    }\n  })));\n});\nconst Popover = InternalPopover;\nPopover._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nif (process.env.NODE_ENV !== 'production') {\n  Popover.displayName = 'Popover';\n}\nexport default Popover;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "useMergedState", "KeyCode", "getRenderPropValue", "getTransitionName", "cloneElement", "<PERSON><PERSON><PERSON>", "PurePanel", "Overlay", "useComponentConfig", "useStyle", "InternalPopover", "forwardRef", "props", "ref", "_a", "_b", "prefixCls", "customizePrefixCls", "title", "content", "overlayClassName", "placement", "trigger", "children", "mouseEnterDelay", "mouseLeaveDelay", "onOpenChange", "overlayStyle", "styles", "popoverClassNames", "otherProps", "getPrefixCls", "className", "contextClassName", "style", "contextStyle", "contextClassNames", "contextStyles", "wrapCSSVar", "hashId", "cssVarCls", "rootPrefixCls", "rootClassNames", "root", "bodyClassNames", "body", "open", "<PERSON><PERSON><PERSON>", "value", "visible", "defaultValue", "defaultOpen", "defaultVisible", "<PERSON><PERSON><PERSON>", "onKeyDown", "keyCode", "ESC", "onInternalOpenChange", "titleNode", "contentNode", "createElement", "assign", "overlay", "transitionName", "isValidElement", "Popover", "_InternalPanelDoNotUseOrYouWillBeFired", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/popover/index.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { getRenderPropValue } from '../_util/getRenderPropValue';\nimport { getTransitionName } from '../_util/motion';\nimport { cloneElement } from '../_util/reactNode';\nimport Tooltip from '../tooltip';\nimport PurePanel, { Overlay } from './PurePanel';\nimport { useComponentConfig } from '../config-provider/context';\n// CSSINJS\nimport useStyle from './style';\nconst InternalPopover = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a, _b;\n  const {\n      prefixCls: customizePrefixCls,\n      title,\n      content,\n      overlayClassName,\n      placement = 'top',\n      trigger = 'hover',\n      children,\n      mouseEnterDelay = 0.1,\n      mouseLeaveDelay = 0.1,\n      onOpenChange,\n      overlayStyle = {},\n      styles,\n      classNames: popoverClassNames\n    } = props,\n    otherProps = __rest(props, [\"prefixCls\", \"title\", \"content\", \"overlayClassName\", \"placement\", \"trigger\", \"children\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"onOpenChange\", \"overlayStyle\", \"styles\", \"classNames\"]);\n  const {\n    getPrefixCls,\n    className: contextClassName,\n    style: contextStyle,\n    classNames: contextClassNames,\n    styles: contextStyles\n  } = useComponentConfig('popover');\n  const prefixCls = getPrefixCls('popover', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const rootPrefixCls = getPrefixCls();\n  const rootClassNames = classNames(overlayClassName, hashId, cssVarCls, contextClassName, contextClassNames.root, popoverClassNames === null || popoverClassNames === void 0 ? void 0 : popoverClassNames.root);\n  const bodyClassNames = classNames(contextClassNames.body, popoverClassNames === null || popoverClassNames === void 0 ? void 0 : popoverClassNames.body);\n  const [open, setOpen] = useMergedState(false, {\n    value: (_a = props.open) !== null && _a !== void 0 ? _a : props.visible,\n    defaultValue: (_b = props.defaultOpen) !== null && _b !== void 0 ? _b : props.defaultVisible\n  });\n  const settingOpen = (value, e) => {\n    setOpen(value, true);\n    onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(value, e);\n  };\n  const onKeyDown = e => {\n    if (e.keyCode === KeyCode.ESC) {\n      settingOpen(false, e);\n    }\n  };\n  const onInternalOpenChange = value => {\n    settingOpen(value);\n  };\n  const titleNode = getRenderPropValue(title);\n  const contentNode = getRenderPropValue(content);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(Tooltip, Object.assign({\n    placement: placement,\n    trigger: trigger,\n    mouseEnterDelay: mouseEnterDelay,\n    mouseLeaveDelay: mouseLeaveDelay\n  }, otherProps, {\n    prefixCls: prefixCls,\n    classNames: {\n      root: rootClassNames,\n      body: bodyClassNames\n    },\n    styles: {\n      root: Object.assign(Object.assign(Object.assign(Object.assign({}, contextStyles.root), contextStyle), overlayStyle), styles === null || styles === void 0 ? void 0 : styles.root),\n      body: Object.assign(Object.assign({}, contextStyles.body), styles === null || styles === void 0 ? void 0 : styles.body)\n    },\n    ref: ref,\n    open: open,\n    onOpenChange: onInternalOpenChange,\n    overlay: titleNode || contentNode ? (/*#__PURE__*/React.createElement(Overlay, {\n      prefixCls: prefixCls,\n      title: titleNode,\n      content: contentNode\n    })) : null,\n    transitionName: getTransitionName(rootPrefixCls, 'zoom-big', otherProps.transitionName),\n    \"data-popover-inject\": true\n  }), cloneElement(children, {\n    onKeyDown: e => {\n      var _a, _b;\n      if (/*#__PURE__*/React.isValidElement(children)) {\n        (_b = children === null || children === void 0 ? void 0 : (_a = children.props).onKeyDown) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n      }\n      onKeyDown(e);\n    }\n  })));\n});\nconst Popover = InternalPopover;\nPopover._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nif (process.env.NODE_ENV !== 'production') {\n  Popover.displayName = 'Popover';\n}\nexport default Popover;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,SAAS,IAAIC,OAAO,QAAQ,aAAa;AAChD,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D;AACA,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,eAAe,GAAG,aAAaZ,KAAK,CAACa,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACpE,IAAIC,EAAE,EAAEC,EAAE;EACV,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,KAAK;MACLC,OAAO;MACPC,gBAAgB;MAChBC,SAAS,GAAG,KAAK;MACjBC,OAAO,GAAG,OAAO;MACjBC,QAAQ;MACRC,eAAe,GAAG,GAAG;MACrBC,eAAe,GAAG,GAAG;MACrBC,YAAY;MACZC,YAAY,GAAG,CAAC,CAAC;MACjBC,MAAM;MACN7B,UAAU,EAAE8B;IACd,CAAC,GAAGjB,KAAK;IACTkB,UAAU,GAAG9C,MAAM,CAAC4B,KAAK,EAAE,CAAC,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;EACrN,MAAM;IACJmB,YAAY;IACZC,SAAS,EAAEC,gBAAgB;IAC3BC,KAAK,EAAEC,YAAY;IACnBpC,UAAU,EAAEqC,iBAAiB;IAC7BR,MAAM,EAAES;EACV,CAAC,GAAG7B,kBAAkB,CAAC,SAAS,CAAC;EACjC,MAAMQ,SAAS,GAAGe,YAAY,CAAC,SAAS,EAAEd,kBAAkB,CAAC;EAC7D,MAAM,CAACqB,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAACO,SAAS,CAAC;EAC3D,MAAMyB,aAAa,GAAGV,YAAY,CAAC,CAAC;EACpC,MAAMW,cAAc,GAAG3C,UAAU,CAACqB,gBAAgB,EAAEmB,MAAM,EAAEC,SAAS,EAAEP,gBAAgB,EAAEG,iBAAiB,CAACO,IAAI,EAAEd,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACc,IAAI,CAAC;EAC9M,MAAMC,cAAc,GAAG7C,UAAU,CAACqC,iBAAiB,CAACS,IAAI,EAAEhB,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACgB,IAAI,CAAC;EACvJ,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG/C,cAAc,CAAC,KAAK,EAAE;IAC5CgD,KAAK,EAAE,CAAClC,EAAE,GAAGF,KAAK,CAACkC,IAAI,MAAM,IAAI,IAAIhC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGF,KAAK,CAACqC,OAAO;IACvEC,YAAY,EAAE,CAACnC,EAAE,GAAGH,KAAK,CAACuC,WAAW,MAAM,IAAI,IAAIpC,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGH,KAAK,CAACwC;EAChF,CAAC,CAAC;EACF,MAAMC,WAAW,GAAGA,CAACL,KAAK,EAAE9D,CAAC,KAAK;IAChC6D,OAAO,CAACC,KAAK,EAAE,IAAI,CAAC;IACpBtB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACsB,KAAK,EAAE9D,CAAC,CAAC;EACpF,CAAC;EACD,MAAMoE,SAAS,GAAGpE,CAAC,IAAI;IACrB,IAAIA,CAAC,CAACqE,OAAO,KAAKtD,OAAO,CAACuD,GAAG,EAAE;MAC7BH,WAAW,CAAC,KAAK,EAAEnE,CAAC,CAAC;IACvB;EACF,CAAC;EACD,MAAMuE,oBAAoB,GAAGT,KAAK,IAAI;IACpCK,WAAW,CAACL,KAAK,CAAC;EACpB,CAAC;EACD,MAAMU,SAAS,GAAGxD,kBAAkB,CAACgB,KAAK,CAAC;EAC3C,MAAMyC,WAAW,GAAGzD,kBAAkB,CAACiB,OAAO,CAAC;EAC/C,OAAOmB,UAAU,CAAC,aAAaxC,KAAK,CAAC8D,aAAa,CAACvD,OAAO,EAAEhB,MAAM,CAACwE,MAAM,CAAC;IACxExC,SAAS,EAAEA,SAAS;IACpBC,OAAO,EAAEA,OAAO;IAChBE,eAAe,EAAEA,eAAe;IAChCC,eAAe,EAAEA;EACnB,CAAC,EAAEK,UAAU,EAAE;IACbd,SAAS,EAAEA,SAAS;IACpBjB,UAAU,EAAE;MACV4C,IAAI,EAAED,cAAc;MACpBG,IAAI,EAAED;IACR,CAAC;IACDhB,MAAM,EAAE;MACNe,IAAI,EAAEtD,MAAM,CAACwE,MAAM,CAACxE,MAAM,CAACwE,MAAM,CAACxE,MAAM,CAACwE,MAAM,CAACxE,MAAM,CAACwE,MAAM,CAAC,CAAC,CAAC,EAAExB,aAAa,CAACM,IAAI,CAAC,EAAER,YAAY,CAAC,EAAER,YAAY,CAAC,EAAEC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACe,IAAI,CAAC;MACjLE,IAAI,EAAExD,MAAM,CAACwE,MAAM,CAACxE,MAAM,CAACwE,MAAM,CAAC,CAAC,CAAC,EAAExB,aAAa,CAACQ,IAAI,CAAC,EAAEjB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACiB,IAAI;IACxH,CAAC;IACDhC,GAAG,EAAEA,GAAG;IACRiC,IAAI,EAAEA,IAAI;IACVpB,YAAY,EAAE+B,oBAAoB;IAClCK,OAAO,EAAEJ,SAAS,IAAIC,WAAW,IAAI,aAAa7D,KAAK,CAAC8D,aAAa,CAACrD,OAAO,EAAE;MAC7ES,SAAS,EAAEA,SAAS;MACpBE,KAAK,EAAEwC,SAAS;MAChBvC,OAAO,EAAEwC;IACX,CAAC,CAAC,IAAI,IAAI;IACVI,cAAc,EAAE5D,iBAAiB,CAACsC,aAAa,EAAE,UAAU,EAAEX,UAAU,CAACiC,cAAc,CAAC;IACvF,qBAAqB,EAAE;EACzB,CAAC,CAAC,EAAE3D,YAAY,CAACmB,QAAQ,EAAE;IACzB+B,SAAS,EAAEpE,CAAC,IAAI;MACd,IAAI4B,EAAE,EAAEC,EAAE;MACV,IAAI,aAAajB,KAAK,CAACkE,cAAc,CAACzC,QAAQ,CAAC,EAAE;QAC/C,CAACR,EAAE,GAAGQ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACT,EAAE,GAAGS,QAAQ,CAACX,KAAK,EAAE0C,SAAS,MAAM,IAAI,IAAIvC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACvB,IAAI,CAACsB,EAAE,EAAE5B,CAAC,CAAC;MAChJ;MACAoE,SAAS,CAACpE,CAAC,CAAC;IACd;EACF,CAAC,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AACF,MAAM+E,OAAO,GAAGvD,eAAe;AAC/BuD,OAAO,CAACC,sCAAsC,GAAG5D,SAAS;AAC1D,IAAI6D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,SAAS;AACjC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}