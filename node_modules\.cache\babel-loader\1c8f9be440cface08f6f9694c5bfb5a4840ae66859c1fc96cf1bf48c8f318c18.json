{"ast": null, "code": "export function allPromiseFinish(promiseList) {\n  var hasError = false;\n  var count = promiseList.length;\n  var results = [];\n  if (!promiseList.length) {\n    return Promise.resolve([]);\n  }\n  return new Promise(function (resolve, reject) {\n    promiseList.forEach(function (promise, index) {\n      promise.catch(function (e) {\n        hasError = true;\n        return e;\n      }).then(function (result) {\n        count -= 1;\n        results[index] = result;\n        if (count > 0) {\n          return;\n        }\n        if (hasError) {\n          reject(results);\n        }\n        resolve(results);\n      });\n    });\n  });\n}", "map": {"version": 3, "names": ["allPromiseFinish", "promiseList", "<PERSON><PERSON><PERSON><PERSON>", "count", "length", "results", "Promise", "resolve", "reject", "for<PERSON>ach", "promise", "index", "catch", "e", "then", "result"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-field-form/es/utils/asyncUtil.js"], "sourcesContent": ["export function allPromiseFinish(promiseList) {\n  var hasError = false;\n  var count = promiseList.length;\n  var results = [];\n  if (!promiseList.length) {\n    return Promise.resolve([]);\n  }\n  return new Promise(function (resolve, reject) {\n    promiseList.forEach(function (promise, index) {\n      promise.catch(function (e) {\n        hasError = true;\n        return e;\n      }).then(function (result) {\n        count -= 1;\n        results[index] = result;\n        if (count > 0) {\n          return;\n        }\n        if (hasError) {\n          reject(results);\n        }\n        resolve(results);\n      });\n    });\n  });\n}"], "mappings": "AAAA,OAAO,SAASA,gBAAgBA,CAACC,WAAW,EAAE;EAC5C,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAIC,KAAK,GAAGF,WAAW,CAACG,MAAM;EAC9B,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAI,CAACJ,WAAW,CAACG,MAAM,EAAE;IACvB,OAAOE,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC;EAC5B;EACA,OAAO,IAAID,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;IAC5CP,WAAW,CAACQ,OAAO,CAAC,UAAUC,OAAO,EAAEC,KAAK,EAAE;MAC5CD,OAAO,CAACE,KAAK,CAAC,UAAUC,CAAC,EAAE;QACzBX,QAAQ,GAAG,IAAI;QACf,OAAOW,CAAC;MACV,CAAC,CAAC,CAACC,IAAI,CAAC,UAAUC,MAAM,EAAE;QACxBZ,KAAK,IAAI,CAAC;QACVE,OAAO,CAACM,KAAK,CAAC,GAAGI,MAAM;QACvB,IAAIZ,KAAK,GAAG,CAAC,EAAE;UACb;QACF;QACA,IAAID,QAAQ,EAAE;UACZM,MAAM,CAACH,OAAO,CAAC;QACjB;QACAE,OAAO,CAACF,OAAO,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}