{"ast": null, "code": "import { lintWarning } from \"./utils\";\nvar linter = function linter(key, value, info) {\n  if (key === 'content') {\n    // From emotion: https://github.com/emotion-js/emotion/blob/main/packages/serialize/src/index.js#L63\n    var contentValuePattern = /(attr|counters?|url|(((repeating-)?(linear|radial))|conic)-gradient)\\(|(no-)?(open|close)-quote/;\n    var contentValues = ['normal', 'none', 'initial', 'inherit', 'unset'];\n    if (typeof value !== 'string' || contentValues.indexOf(value) === -1 && !contentValuePattern.test(value) && (value.charAt(0) !== value.charAt(value.length - 1) || value.charAt(0) !== '\"' && value.charAt(0) !== \"'\")) {\n      lintWarning(\"You seem to be using a value for 'content' without quotes, try replacing it with `content: '\\\"\".concat(value, \"\\\"'`.\"), info);\n    }\n  }\n};\nexport default linter;", "map": {"version": 3, "names": ["lintWarning", "linter", "key", "value", "info", "contentValuePattern", "contentValues", "indexOf", "test", "char<PERSON>t", "length", "concat"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@ant-design/cssinjs/es/linters/contentQuotesLinter.js"], "sourcesContent": ["import { lintWarning } from \"./utils\";\nvar linter = function linter(key, value, info) {\n  if (key === 'content') {\n    // From emotion: https://github.com/emotion-js/emotion/blob/main/packages/serialize/src/index.js#L63\n    var contentValuePattern = /(attr|counters?|url|(((repeating-)?(linear|radial))|conic)-gradient)\\(|(no-)?(open|close)-quote/;\n    var contentValues = ['normal', 'none', 'initial', 'inherit', 'unset'];\n    if (typeof value !== 'string' || contentValues.indexOf(value) === -1 && !contentValuePattern.test(value) && (value.charAt(0) !== value.charAt(value.length - 1) || value.charAt(0) !== '\"' && value.charAt(0) !== \"'\")) {\n      lintWarning(\"You seem to be using a value for 'content' without quotes, try replacing it with `content: '\\\"\".concat(value, \"\\\"'`.\"), info);\n    }\n  }\n};\nexport default linter;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,SAAS;AACrC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC7C,IAAIF,GAAG,KAAK,SAAS,EAAE;IACrB;IACA,IAAIG,mBAAmB,GAAG,iGAAiG;IAC3H,IAAIC,aAAa,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,CAAC;IACrE,IAAI,OAAOH,KAAK,KAAK,QAAQ,IAAIG,aAAa,CAACC,OAAO,CAACJ,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAACE,mBAAmB,CAACG,IAAI,CAACL,KAAK,CAAC,KAAKA,KAAK,CAACM,MAAM,CAAC,CAAC,CAAC,KAAKN,KAAK,CAACM,MAAM,CAACN,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC,IAAIP,KAAK,CAACM,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIN,KAAK,CAACM,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;MACtNT,WAAW,CAAC,gGAAgG,CAACW,MAAM,CAACR,KAAK,EAAE,OAAO,CAAC,EAAEC,IAAI,CAAC;IAC5I;EACF;AACF,CAAC;AACD,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}