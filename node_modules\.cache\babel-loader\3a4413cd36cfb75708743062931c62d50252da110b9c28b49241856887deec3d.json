{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\DevicePopoverContent.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport VideoPlayer from './VideoPlayer'; // 引入摄像头视频播放组件\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n/**\r\n * 设备弹窗内容组件\r\n * @param {object} props.device 设备对象\r\n */\nconst DevicePopoverContent = ({\n  device\n}) => {\n  _s();\n  if (!device) return null;\n  const isCamera = device.type === 'camera';\n  const isRSU = device.type === 'rsu';\n  const imgPath = `${BASE_URL}/images/${device.type}.png`;\n  const flvBase = process.env.REACT_APP_FLV_URL || 'http://localhost:8000';\n  const flvUrl = device.rtspUrl ? `${flvBase}/live/${device.id}.flv` : null;\n\n  // ========== RSU MQTT消息下发相关本地状态 ==========\n  const [messageType, setMessageType] = useState('RSI'); // 消息类型，默认RSI\n  const [mqttSending, setMqttSending] = useState(false); // MQTT下发状态\n  const [mqttResult, setMqttResult] = useState(''); // MQTT下发结果\n  const [selectedDataFile, setSelectedDataFile] = useState(null); // 选中的数据文件\n  const [fileContent, setFileContent] = useState(''); // 文件内容（16进制字符串）\n\n  // 处理数据文件选择\n  const handleDataFileChange = e => {\n    const file = e.target.files[0];\n    setSelectedDataFile(file);\n    setMqttResult('');\n    if (file) {\n      // 读取文件内容\n      const reader = new FileReader();\n      reader.onload = event => {\n        try {\n          const content = event.target.result;\n          // 将文件内容转换为16进制字符串\n          const hexString = convertToHexString(content);\n          setFileContent(hexString);\n          setMqttResult(`文件 \"${file.name}\" 加载成功，数据长度: ${hexString.length} 字符`);\n        } catch (error) {\n          setMqttResult(`文件读取失败: ${error.message}`);\n          setFileContent('');\n        }\n      };\n      reader.onerror = () => {\n        setMqttResult('文件读取失败');\n        setFileContent('');\n      };\n      // 以二进制方式读取文件\n      reader.readAsArrayBuffer(file);\n    } else {\n      setFileContent('');\n    }\n  };\n\n  // 将文件内容转换为16进制字符串\n  const convertToHexString = arrayBuffer => {\n    const uint8Array = new Uint8Array(arrayBuffer);\n    return Array.from(uint8Array).map(byte => byte.toString(16).padStart(2, '0').toUpperCase()).join('');\n  };\n\n  // 处理MQTT消息下发\n  const handleMqttSend = async () => {\n    if (!device.mac) {\n      setMqttResult('RSU设备未配置MAC地址，无法下发MQTT消息');\n      return;\n    }\n    setMqttSending(true);\n    setMqttResult('正在下发MQTT消息...');\n    try {\n      const apiUrl = BASE_URL + '/api/rsu/send-mqtt';\n      const resp = await axios.post(apiUrl, {\n        mac: device.mac,\n        messageType: messageType,\n        deviceId: device.id,\n        deviceName: device.name\n      }, {\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        timeout: 10000\n      });\n      if (resp.data && resp.data.success) {\n        setMqttResult(`${messageType}消息下发成功！`);\n      } else {\n        var _resp$data;\n        setMqttResult('MQTT消息下发失败：' + (((_resp$data = resp.data) === null || _resp$data === void 0 ? void 0 : _resp$data.message) || '未知错误'));\n      }\n    } catch (err) {\n      setMqttResult('MQTT消息下发失败：' + ((err === null || err === void 0 ? void 0 : err.message) || '网络错误'));\n    } finally {\n      setMqttSending(false);\n    }\n  };\n\n  // ========== 弹窗内容渲染 ==========\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '10px',\n      width: 320,\n      maxWidth: 350\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 6,\n        textAlign: 'center'\n      },\n      children: isCamera ? device.rtspUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: 300,\n          height: 180,\n          background: '#000',\n          margin: '0 auto',\n          borderRadius: 6,\n          overflow: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(VideoPlayer, {\n          deviceId: device.id,\n          rtspUrl: device.rtspUrl\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: 300,\n          height: 180,\n          background: '#222',\n          color: '#fff',\n          lineHeight: '180px',\n          borderRadius: 6,\n          margin: '0 auto'\n        },\n        children: \"\\u65E0\\u89C6\\u9891\\u6D41\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n        src: imgPath,\n        alt: device.type,\n        style: {\n          width: 120,\n          height: 120,\n          objectFit: 'contain',\n          background: '#fff',\n          borderRadius: 8,\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 15,\n        fontWeight: 'bold',\n        marginBottom: 6\n      },\n      children: device.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 13,\n        color: '#888',\n        marginBottom: 6\n      },\n      children: [\"\\u7C7B\\u578B\\uFF1A\", device.type]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 13,\n        color: '#888',\n        marginBottom: 6\n      },\n      children: [\"\\u4F4D\\u7F6E\\uFF1A\", device.location, \" \", device.entrance ? `(${device.entrance})` : '']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 13,\n        color: '#888',\n        marginBottom: 6\n      },\n      children: [\"\\u72B6\\u6001\\uFF1A\", device.status === 'online' ? /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: '#52c41a'\n        },\n        children: \"\\u5728\\u7EBF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 102\n      }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: '#f5222d'\n        },\n        children: \"\\u79BB\\u7EBF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 149\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), device.ipAddress && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 13,\n        color: '#888',\n        marginBottom: 6\n      },\n      children: [\"IP\\uFF1A\", device.ipAddress]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 28\n    }, this), device.manufacturer && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 13,\n        color: '#888',\n        marginBottom: 6\n      },\n      children: [\"\\u5382\\u5546\\uFF1A\", device.manufacturer]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 31\n    }, this), device.model && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 13,\n        color: '#888',\n        marginBottom: 6\n      },\n      children: [\"\\u578B\\u53F7\\uFF1A\", device.model]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 24\n    }, this), device.description && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: 13,\n        color: '#888',\n        marginBottom: 6\n      },\n      children: [\"\\u63CF\\u8FF0\\uFF1A\", device.description]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 30\n    }, this), isRSU && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: 16,\n        padding: 12,\n        background: '#fff',\n        // 改为纯白背景增强对比度\n        borderRadius: 10,\n        boxShadow: '0 2px 8px rgba(0,0,0,0.07)',\n        border: '1px solid #e6e6e6'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold',\n          fontSize: 14,\n          marginBottom: 8,\n          color: '#1890ff',\n          // 保留品牌色但增加对比度\n          letterSpacing: 1\n        },\n        children: \"\\u6587\\u4EF6\\u4E0B\\u53D1\\u5230RSU\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 10\n        },\n        children: /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          onChange: handleFileChange,\n          style: {\n            width: '100%',\n            color: '#333',\n            // 文件选择文字改为深色\n            fontSize: 13,\n            padding: '4px 6px',\n            borderRadius: 4,\n            border: '1px solid #d9d9d9',\n            // 添加边框增强可视性\n            backgroundColor: '#f9f9f9' // 浅灰背景增加层次\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 10,\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 13,\n            color: '#333',\n            marginRight: 6,\n            minWidth: 36\n          },\n          children: \"\\u7AEF\\u53E3\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          value: targetPort,\n          onChange: handlePortChange,\n          placeholder: \"\\u59828080\",\n          style: {\n            width: 80,\n            padding: '4px 6px',\n            borderRadius: 4,\n            border: '1px solid #d9d9d9',\n            // 统一边框样式\n            fontSize: 13,\n            marginRight: 0,\n            color: '#333' // 输入文字深色\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleSendFile,\n        disabled: sending,\n        style: {\n          width: '100%',\n          padding: 7,\n          background: '#1890ff',\n          color: '#fff',\n          border: 'none',\n          borderRadius: 4,\n          cursor: sending ? 'not-allowed' : 'pointer',\n          fontWeight: 'bold',\n          fontSize: 15,\n          letterSpacing: 1\n        },\n        children: sending ? '正在下发...' : '文件下发'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 11\n      }, this), sendResult && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 10,\n          color: sendResult.includes('成功') ? '#52c41a' : '#f5222d',\n          fontSize: 13\n        },\n        children: sendResult\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 26\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 12,\n          color: '#666',\n          marginTop: 6\n        },\n        children: [\"\\u76EE\\u6807IP\\uFF1A\", device.ipAddress || '未配置']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          margin: '16px 0',\n          height: '1px',\n          background: '#e6e6e6'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontWeight: 'bold',\n          fontSize: 14,\n          marginBottom: 8,\n          color: '#52c41a',\n          letterSpacing: 1\n        },\n        children: \"MQTT\\u6D88\\u606F\\u4E0B\\u53D1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 10\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 13,\n            color: '#333',\n            marginRight: 6\n          },\n          children: \"\\u6D88\\u606F\\u7C7B\\u578B\\uFF1A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: messageType,\n          onChange: e => setMessageType(e.target.value),\n          style: {\n            width: '100%',\n            padding: '4px 6px',\n            borderRadius: 4,\n            border: '1px solid #d9d9d9',\n            fontSize: 13,\n            color: '#333',\n            backgroundColor: '#f9f9f9'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"RSI\",\n            children: \"RSI - \\u8DEF\\u4FA7\\u4E8B\\u4EF6\\u548C\\u6807\\u724C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"MAP\",\n            children: \"MAP - \\u5730\\u56FE\\u6D88\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleMqttSend,\n        disabled: mqttSending || !device.mac,\n        style: {\n          width: '100%',\n          padding: 7,\n          background: device.mac ? '#52c41a' : '#d9d9d9',\n          color: '#fff',\n          border: 'none',\n          borderRadius: 4,\n          cursor: mqttSending || !device.mac ? 'not-allowed' : 'pointer',\n          fontWeight: 'bold',\n          fontSize: 15,\n          letterSpacing: 1\n        },\n        children: mqttSending ? '正在下发...' : `下发${messageType}消息`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this), mqttResult && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 10,\n          color: mqttResult.includes('成功') ? '#52c41a' : '#f5222d',\n          fontSize: 13\n        },\n        children: mqttResult\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 26\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 12,\n          color: '#666',\n          marginTop: 6\n        },\n        children: [\"MAC\\u5730\\u5740\\uFF1A\", device.mac || '未配置']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_s(DevicePopoverContent, \"OCnK3svJsj03JkP4PJxhP7jyhRU=\");\n_c = DevicePopoverContent;\nexport default DevicePopoverContent;\nvar _c;\n$RefreshReg$(_c, \"DevicePopoverContent\");", "map": {"version": 3, "names": ["React", "useState", "axios", "VideoPlayer", "jsxDEV", "_jsxDEV", "BASE_URL", "process", "env", "REACT_APP_API_URL", "DevicePopoverContent", "device", "_s", "isCamera", "type", "isRSU", "imgPath", "flvBase", "REACT_APP_FLV_URL", "flvUrl", "rtspUrl", "id", "messageType", "setMessageType", "mqttSending", "setMqttSending", "mqttResult", "setMqttResult", "selectedDataFile", "setSelectedDataFile", "fileContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDataFileChange", "e", "file", "target", "files", "reader", "FileReader", "onload", "event", "content", "result", "hexString", "convertToHexString", "name", "length", "error", "message", "onerror", "readAsA<PERSON>y<PERSON><PERSON>er", "arrayBuffer", "uint8Array", "Uint8Array", "Array", "from", "map", "byte", "toString", "padStart", "toUpperCase", "join", "handleMqttSend", "mac", "apiUrl", "resp", "post", "deviceId", "deviceName", "headers", "timeout", "data", "success", "_resp$data", "err", "style", "padding", "width", "max<PERSON><PERSON><PERSON>", "children", "marginBottom", "textAlign", "height", "background", "margin", "borderRadius", "overflow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "lineHeight", "src", "alt", "objectFit", "boxShadow", "fontSize", "fontWeight", "location", "entrance", "status", "ip<PERSON><PERSON><PERSON>", "manufacturer", "model", "description", "marginTop", "border", "letterSpacing", "onChange", "handleFileChange", "backgroundColor", "display", "alignItems", "marginRight", "min<PERSON><PERSON><PERSON>", "value", "targetPort", "handlePortChange", "placeholder", "onClick", "handleSendFile", "disabled", "sending", "cursor", "sendResult", "includes", "_c", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/DevicePopoverContent.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport axios from 'axios';\r\nimport VideoPlayer from './VideoPlayer'; // 引入摄像头视频播放组件\r\n\r\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n\r\n/**\r\n * 设备弹窗内容组件\r\n * @param {object} props.device 设备对象\r\n */\r\nconst DevicePopoverContent = ({ device }) => {\r\n  if (!device) return null;\r\n  const isCamera = device.type === 'camera';\r\n  const isRSU = device.type === 'rsu';\r\n  const imgPath = `${BASE_URL}/images/${device.type}.png`;\r\n  const flvBase = process.env.REACT_APP_FLV_URL || 'http://localhost:8000';\r\n  const flvUrl = device.rtspUrl ? `${flvBase}/live/${device.id}.flv` : null;\r\n\r\n  // ========== RSU MQTT消息下发相关本地状态 ==========\r\n  const [messageType, setMessageType] = useState('RSI'); // 消息类型，默认RSI\r\n  const [mqttSending, setMqttSending] = useState(false); // MQTT下发状态\r\n  const [mqttResult, setMqttResult] = useState(''); // MQTT下发结果\r\n  const [selectedDataFile, setSelectedDataFile] = useState(null); // 选中的数据文件\r\n  const [fileContent, setFileContent] = useState(''); // 文件内容（16进制字符串）\r\n\r\n  // 处理数据文件选择\r\n  const handleDataFileChange = (e) => {\r\n    const file = e.target.files[0];\r\n    setSelectedDataFile(file);\r\n    setMqttResult('');\r\n\r\n    if (file) {\r\n      // 读取文件内容\r\n      const reader = new FileReader();\r\n      reader.onload = (event) => {\r\n        try {\r\n          const content = event.target.result;\r\n          // 将文件内容转换为16进制字符串\r\n          const hexString = convertToHexString(content);\r\n          setFileContent(hexString);\r\n          setMqttResult(`文件 \"${file.name}\" 加载成功，数据长度: ${hexString.length} 字符`);\r\n        } catch (error) {\r\n          setMqttResult(`文件读取失败: ${error.message}`);\r\n          setFileContent('');\r\n        }\r\n      };\r\n      reader.onerror = () => {\r\n        setMqttResult('文件读取失败');\r\n        setFileContent('');\r\n      };\r\n      // 以二进制方式读取文件\r\n      reader.readAsArrayBuffer(file);\r\n    } else {\r\n      setFileContent('');\r\n    }\r\n  };\r\n\r\n  // 将文件内容转换为16进制字符串\r\n  const convertToHexString = (arrayBuffer) => {\r\n    const uint8Array = new Uint8Array(arrayBuffer);\r\n    return Array.from(uint8Array)\r\n      .map(byte => byte.toString(16).padStart(2, '0').toUpperCase())\r\n      .join('');\r\n  };\r\n\r\n  // 处理MQTT消息下发\r\n  const handleMqttSend = async () => {\r\n    if (!device.mac) {\r\n      setMqttResult('RSU设备未配置MAC地址，无法下发MQTT消息');\r\n      return;\r\n    }\r\n    setMqttSending(true);\r\n    setMqttResult('正在下发MQTT消息...');\r\n    try {\r\n      const apiUrl = BASE_URL + '/api/rsu/send-mqtt';\r\n      const resp = await axios.post(apiUrl, {\r\n        mac: device.mac,\r\n        messageType: messageType,\r\n        deviceId: device.id,\r\n        deviceName: device.name\r\n      }, {\r\n        headers: { 'Content-Type': 'application/json' },\r\n        timeout: 10000\r\n      });\r\n      if (resp.data && resp.data.success) {\r\n        setMqttResult(`${messageType}消息下发成功！`);\r\n      } else {\r\n        setMqttResult('MQTT消息下发失败：' + (resp.data?.message || '未知错误'));\r\n      }\r\n    } catch (err) {\r\n      setMqttResult('MQTT消息下发失败：' + (err?.message || '网络错误'));\r\n    } finally {\r\n      setMqttSending(false);\r\n    }\r\n  };\r\n\r\n  // ========== 弹窗内容渲染 ==========\r\n  return (\r\n    <div style={{ padding: '10px', width: 320, maxWidth: 350 }}>\r\n      <div style={{ marginBottom: 6, textAlign: 'center' }}>\r\n        {isCamera ? (\r\n          device.rtspUrl ? (\r\n            <div style={{ width: 300, height: 180, background: '#000', margin: '0 auto', borderRadius: 6, overflow: 'hidden' }}>\r\n              {/* 这里可以放摄像头视频组件 */}\r\n              {/* <video src={flvUrl} controls style={{ width: '100%', height: '100%' }} /> */}\r\n              <VideoPlayer deviceId={device.id} rtspUrl={device.rtspUrl} />\r\n            </div>\r\n          ) : (\r\n            <div style={{ width: 300, height: 180, background: '#222', color: '#fff', lineHeight: '180px', borderRadius: 6, margin: '0 auto' }}>无视频流</div>\r\n          )\r\n        ) : (\r\n          <img src={imgPath} alt={device.type} style={{ width: 120, height: 120, objectFit: 'contain', background: '#fff', borderRadius: 8, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }} />\r\n        )}\r\n      </div>\r\n      <div style={{ fontSize: 15, fontWeight: 'bold', marginBottom: 6 }}>{device.name}</div>\r\n      <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>类型：{device.type}</div>\r\n      <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>位置：{device.location} {device.entrance ? `(${device.entrance})` : ''}</div>\r\n      <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>状态：{device.status === 'online' ? <span style={{ color: '#52c41a' }}>在线</span> : <span style={{ color: '#f5222d' }}>离线</span>}</div>\r\n      {device.ipAddress && <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>IP：{device.ipAddress}</div>}\r\n      {device.manufacturer && <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>厂商：{device.manufacturer}</div>}\r\n      {device.model && <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>型号：{device.model}</div>}\r\n      {device.description && <div style={{ fontSize: 13, color: '#888', marginBottom: 6 }}>描述：{device.description}</div>}\r\n      {/* ========== RSU消息下发功能区域 ========== */}\r\n      {isRSU && (\r\n        <div style={{\r\n          marginTop: 16,\r\n          padding: 12,\r\n          background: '#fff', // 改为纯白背景增强对比度\r\n          borderRadius: 10,\r\n          boxShadow: '0 2px 8px rgba(0,0,0,0.07)',\r\n          border: '1px solid #e6e6e6',\r\n        }}>\r\n          {/* 文件下发区域 */}\r\n          <div style={{\r\n            fontWeight: 'bold',\r\n            fontSize: 14,\r\n            marginBottom: 8,\r\n            color: '#1890ff', // 保留品牌色但增加对比度\r\n            letterSpacing: 1\r\n          }}>文件下发到RSU</div>\r\n          <div style={{ marginBottom: 10 }}>\r\n            <input\r\n              type=\"file\"\r\n              onChange={handleFileChange}\r\n              style={{\r\n                width: '100%',\r\n                color: '#333', // 文件选择文字改为深色\r\n                fontSize: 13,\r\n                padding: '4px 6px',\r\n                borderRadius: 4,\r\n                border: '1px solid #d9d9d9', // 添加边框增强可视性\r\n                backgroundColor: '#f9f9f9', // 浅灰背景增加层次\r\n              }}\r\n            />\r\n          </div>\r\n          <div style={{ marginBottom: 10, display: 'flex', alignItems: 'center' }}>\r\n            <span style={{ fontSize: 13, color: '#333', marginRight: 6, minWidth: 36 }}>端口：</span>\r\n            <input\r\n              type=\"text\"\r\n              value={targetPort}\r\n              onChange={handlePortChange}\r\n              placeholder=\"如8080\"\r\n              style={{\r\n                width: 80,\r\n                padding: '4px 6px',\r\n                borderRadius: 4,\r\n                border: '1px solid #d9d9d9', // 统一边框样式\r\n                fontSize: 13,\r\n                marginRight: 0,\r\n                color: '#333' // 输入文字深色\r\n              }}\r\n            />\r\n          </div>\r\n          <button onClick={handleSendFile} disabled={sending} style={{ width: '100%', padding: 7, background: '#1890ff', color: '#fff', border: 'none', borderRadius: 4, cursor: sending ? 'not-allowed' : 'pointer', fontWeight: 'bold', fontSize: 15, letterSpacing: 1 }}>\r\n            {sending ? '正在下发...' : '文件下发'}\r\n          </button>\r\n          {sendResult && <div style={{ marginTop: 10, color: sendResult.includes('成功') ? '#52c41a' : '#f5222d', fontSize: 13 }}>{sendResult}</div>}\r\n          <div style={{ fontSize: 12, color: '#666', marginTop: 6 }}>目标IP：{device.ipAddress || '未配置'}</div>\r\n\r\n          {/* 分隔线 */}\r\n          <div style={{ margin: '16px 0', height: '1px', background: '#e6e6e6' }}></div>\r\n\r\n          {/* MQTT消息下发区域 */}\r\n          <div style={{\r\n            fontWeight: 'bold',\r\n            fontSize: 14,\r\n            marginBottom: 8,\r\n            color: '#52c41a',\r\n            letterSpacing: 1\r\n          }}>MQTT消息下发</div>\r\n          <div style={{ marginBottom: 10 }}>\r\n            <span style={{ fontSize: 13, color: '#333', marginRight: 6 }}>消息类型：</span>\r\n            <select\r\n              value={messageType}\r\n              onChange={(e) => setMessageType(e.target.value)}\r\n              style={{\r\n                width: '100%',\r\n                padding: '4px 6px',\r\n                borderRadius: 4,\r\n                border: '1px solid #d9d9d9',\r\n                fontSize: 13,\r\n                color: '#333',\r\n                backgroundColor: '#f9f9f9'\r\n              }}\r\n            >\r\n              <option value=\"RSI\">RSI - 路侧事件和标牌</option>\r\n              <option value=\"MAP\">MAP - 地图消息</option>\r\n            </select>\r\n          </div>\r\n          <button\r\n            onClick={handleMqttSend}\r\n            disabled={mqttSending || !device.mac}\r\n            style={{\r\n              width: '100%',\r\n              padding: 7,\r\n              background: device.mac ? '#52c41a' : '#d9d9d9',\r\n              color: '#fff',\r\n              border: 'none',\r\n              borderRadius: 4,\r\n              cursor: (mqttSending || !device.mac) ? 'not-allowed' : 'pointer',\r\n              fontWeight: 'bold',\r\n              fontSize: 15,\r\n              letterSpacing: 1\r\n            }}\r\n          >\r\n            {mqttSending ? '正在下发...' : `下发${messageType}消息`}\r\n          </button>\r\n          {mqttResult && <div style={{ marginTop: 10, color: mqttResult.includes('成功') ? '#52c41a' : '#f5222d', fontSize: 13 }}>{mqttResult}</div>}\r\n          <div style={{ fontSize: 12, color: '#666', marginTop: 6 }}>MAC地址：{device.mac || '未配置'}</div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DevicePopoverContent;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,WAAW,MAAM,eAAe,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAEzE;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC3C,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EACxB,MAAME,QAAQ,GAAGF,MAAM,CAACG,IAAI,KAAK,QAAQ;EACzC,MAAMC,KAAK,GAAGJ,MAAM,CAACG,IAAI,KAAK,KAAK;EACnC,MAAME,OAAO,GAAG,GAAGV,QAAQ,WAAWK,MAAM,CAACG,IAAI,MAAM;EACvD,MAAMG,OAAO,GAAGV,OAAO,CAACC,GAAG,CAACU,iBAAiB,IAAI,uBAAuB;EACxE,MAAMC,MAAM,GAAGR,MAAM,CAACS,OAAO,GAAG,GAAGH,OAAO,SAASN,MAAM,CAACU,EAAE,MAAM,GAAG,IAAI;;EAEzE;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACvD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACvD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM+B,oBAAoB,GAAIC,CAAC,IAAK;IAClC,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9BP,mBAAmB,CAACK,IAAI,CAAC;IACzBP,aAAa,CAAC,EAAE,CAAC;IAEjB,IAAIO,IAAI,EAAE;MACR;MACA,MAAMG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIC,KAAK,IAAK;QACzB,IAAI;UACF,MAAMC,OAAO,GAAGD,KAAK,CAACL,MAAM,CAACO,MAAM;UACnC;UACA,MAAMC,SAAS,GAAGC,kBAAkB,CAACH,OAAO,CAAC;UAC7CV,cAAc,CAACY,SAAS,CAAC;UACzBhB,aAAa,CAAC,OAAOO,IAAI,CAACW,IAAI,gBAAgBF,SAAS,CAACG,MAAM,KAAK,CAAC;QACtE,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdpB,aAAa,CAAC,WAAWoB,KAAK,CAACC,OAAO,EAAE,CAAC;UACzCjB,cAAc,CAAC,EAAE,CAAC;QACpB;MACF,CAAC;MACDM,MAAM,CAACY,OAAO,GAAG,MAAM;QACrBtB,aAAa,CAAC,QAAQ,CAAC;QACvBI,cAAc,CAAC,EAAE,CAAC;MACpB,CAAC;MACD;MACAM,MAAM,CAACa,iBAAiB,CAAChB,IAAI,CAAC;IAChC,CAAC,MAAM;MACLH,cAAc,CAAC,EAAE,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAMa,kBAAkB,GAAIO,WAAW,IAAK;IAC1C,MAAMC,UAAU,GAAG,IAAIC,UAAU,CAACF,WAAW,CAAC;IAC9C,OAAOG,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC,CAC1BI,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAC7DC,IAAI,CAAC,EAAE,CAAC;EACb,CAAC;;EAED;EACA,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACnD,MAAM,CAACoD,GAAG,EAAE;MACfpC,aAAa,CAAC,0BAA0B,CAAC;MACzC;IACF;IACAF,cAAc,CAAC,IAAI,CAAC;IACpBE,aAAa,CAAC,eAAe,CAAC;IAC9B,IAAI;MACF,MAAMqC,MAAM,GAAG1D,QAAQ,GAAG,oBAAoB;MAC9C,MAAM2D,IAAI,GAAG,MAAM/D,KAAK,CAACgE,IAAI,CAACF,MAAM,EAAE;QACpCD,GAAG,EAAEpD,MAAM,CAACoD,GAAG;QACfzC,WAAW,EAAEA,WAAW;QACxB6C,QAAQ,EAAExD,MAAM,CAACU,EAAE;QACnB+C,UAAU,EAAEzD,MAAM,CAACkC;MACrB,CAAC,EAAE;QACDwB,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,OAAO,EAAE;MACX,CAAC,CAAC;MACF,IAAIL,IAAI,CAACM,IAAI,IAAIN,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE;QAClC7C,aAAa,CAAC,GAAGL,WAAW,SAAS,CAAC;MACxC,CAAC,MAAM;QAAA,IAAAmD,UAAA;QACL9C,aAAa,CAAC,aAAa,IAAI,EAAA8C,UAAA,GAAAR,IAAI,CAACM,IAAI,cAAAE,UAAA,uBAATA,UAAA,CAAWzB,OAAO,KAAI,MAAM,CAAC,CAAC;MAC/D;IACF,CAAC,CAAC,OAAO0B,GAAG,EAAE;MACZ/C,aAAa,CAAC,aAAa,IAAI,CAAA+C,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE1B,OAAO,KAAI,MAAM,CAAC,CAAC;IACzD,CAAC,SAAS;MACRvB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,oBACEpB,OAAA;IAAKsE,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,KAAK,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBACzD1E,OAAA;MAAKsE,KAAK,EAAE;QAAEK,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAF,QAAA,EAClDlE,QAAQ,GACPF,MAAM,CAACS,OAAO,gBACZf,OAAA;QAAKsE,KAAK,EAAE;UAAEE,KAAK,EAAE,GAAG;UAAEK,MAAM,EAAE,GAAG;UAAEC,UAAU,EAAE,MAAM;UAAEC,MAAM,EAAE,QAAQ;UAAEC,YAAY,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAS,CAAE;QAAAP,QAAA,eAGjH1E,OAAA,CAACF,WAAW;UAACgE,QAAQ,EAAExD,MAAM,CAACU,EAAG;UAACD,OAAO,EAAET,MAAM,CAACS;QAAQ;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,gBAENrF,OAAA;QAAKsE,KAAK,EAAE;UAAEE,KAAK,EAAE,GAAG;UAAEK,MAAM,EAAE,GAAG;UAAEC,UAAU,EAAE,MAAM;UAAEQ,KAAK,EAAE,MAAM;UAAEC,UAAU,EAAE,OAAO;UAAEP,YAAY,EAAE,CAAC;UAAED,MAAM,EAAE;QAAS,CAAE;QAAAL,QAAA,EAAC;MAAI;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAC9I,gBAEDrF,OAAA;QAAKwF,GAAG,EAAE7E,OAAQ;QAAC8E,GAAG,EAAEnF,MAAM,CAACG,IAAK;QAAC6D,KAAK,EAAE;UAAEE,KAAK,EAAE,GAAG;UAAEK,MAAM,EAAE,GAAG;UAAEa,SAAS,EAAE,SAAS;UAAEZ,UAAU,EAAE,MAAM;UAAEE,YAAY,EAAE,CAAC;UAAEW,SAAS,EAAE;QAA4B;MAAE;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC9K;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACNrF,OAAA;MAAKsE,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEC,UAAU,EAAE,MAAM;QAAElB,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,EAAEpE,MAAM,CAACkC;IAAI;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACtFrF,OAAA;MAAKsE,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEN,KAAK,EAAE,MAAM;QAAEX,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,oBAAG,EAACpE,MAAM,CAACG,IAAI;IAAA;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACpFrF,OAAA;MAAKsE,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEN,KAAK,EAAE,MAAM;QAAEX,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,oBAAG,EAACpE,MAAM,CAACwF,QAAQ,EAAC,GAAC,EAACxF,MAAM,CAACyF,QAAQ,GAAG,IAAIzF,MAAM,CAACyF,QAAQ,GAAG,GAAG,EAAE;IAAA;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACxIrF,OAAA;MAAKsE,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEN,KAAK,EAAE,MAAM;QAAEX,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,oBAAG,EAACpE,MAAM,CAAC0F,MAAM,KAAK,QAAQ,gBAAGhG,OAAA;QAAMsE,KAAK,EAAE;UAAEgB,KAAK,EAAE;QAAU,CAAE;QAAAZ,QAAA,EAAC;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBAAGrF,OAAA;QAAMsE,KAAK,EAAE;UAAEgB,KAAK,EAAE;QAAU,CAAE;QAAAZ,QAAA,EAAC;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAChM/E,MAAM,CAAC2F,SAAS,iBAAIjG,OAAA;MAAKsE,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEN,KAAK,EAAE,MAAM;QAAEX,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,UAAG,EAACpE,MAAM,CAAC2F,SAAS;IAAA;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAC7G/E,MAAM,CAAC4F,YAAY,iBAAIlG,OAAA;MAAKsE,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEN,KAAK,EAAE,MAAM;QAAEX,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,oBAAG,EAACpE,MAAM,CAAC4F,YAAY;IAAA;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACnH/E,MAAM,CAAC6F,KAAK,iBAAInG,OAAA;MAAKsE,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEN,KAAK,EAAE,MAAM;QAAEX,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,oBAAG,EAACpE,MAAM,CAAC6F,KAAK;IAAA;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACrG/E,MAAM,CAAC8F,WAAW,iBAAIpG,OAAA;MAAKsE,KAAK,EAAE;QAAEsB,QAAQ,EAAE,EAAE;QAAEN,KAAK,EAAE,MAAM;QAAEX,YAAY,EAAE;MAAE,CAAE;MAAAD,QAAA,GAAC,oBAAG,EAACpE,MAAM,CAAC8F,WAAW;IAAA;MAAAlB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAEjH3E,KAAK,iBACJV,OAAA;MAAKsE,KAAK,EAAE;QACV+B,SAAS,EAAE,EAAE;QACb9B,OAAO,EAAE,EAAE;QACXO,UAAU,EAAE,MAAM;QAAE;QACpBE,YAAY,EAAE,EAAE;QAChBW,SAAS,EAAE,4BAA4B;QACvCW,MAAM,EAAE;MACV,CAAE;MAAA5B,QAAA,gBAEA1E,OAAA;QAAKsE,KAAK,EAAE;UACVuB,UAAU,EAAE,MAAM;UAClBD,QAAQ,EAAE,EAAE;UACZjB,YAAY,EAAE,CAAC;UACfW,KAAK,EAAE,SAAS;UAAE;UAClBiB,aAAa,EAAE;QACjB,CAAE;QAAA7B,QAAA,EAAC;MAAQ;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACjBrF,OAAA;QAAKsE,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAG,CAAE;QAAAD,QAAA,eAC/B1E,OAAA;UACES,IAAI,EAAC,MAAM;UACX+F,QAAQ,EAAEC,gBAAiB;UAC3BnC,KAAK,EAAE;YACLE,KAAK,EAAE,MAAM;YACbc,KAAK,EAAE,MAAM;YAAE;YACfM,QAAQ,EAAE,EAAE;YACZrB,OAAO,EAAE,SAAS;YAClBS,YAAY,EAAE,CAAC;YACfsB,MAAM,EAAE,mBAAmB;YAAE;YAC7BI,eAAe,EAAE,SAAS,CAAE;UAC9B;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNrF,OAAA;QAAKsE,KAAK,EAAE;UAAEK,YAAY,EAAE,EAAE;UAAEgC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAlC,QAAA,gBACtE1E,OAAA;UAAMsE,KAAK,EAAE;YAAEsB,QAAQ,EAAE,EAAE;YAAEN,KAAK,EAAE,MAAM;YAAEuB,WAAW,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAApC,QAAA,EAAC;QAAG;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtFrF,OAAA;UACES,IAAI,EAAC,MAAM;UACXsG,KAAK,EAAEC,UAAW;UAClBR,QAAQ,EAAES,gBAAiB;UAC3BC,WAAW,EAAC,YAAO;UACnB5C,KAAK,EAAE;YACLE,KAAK,EAAE,EAAE;YACTD,OAAO,EAAE,SAAS;YAClBS,YAAY,EAAE,CAAC;YACfsB,MAAM,EAAE,mBAAmB;YAAE;YAC7BV,QAAQ,EAAE,EAAE;YACZiB,WAAW,EAAE,CAAC;YACdvB,KAAK,EAAE,MAAM,CAAC;UAChB;QAAE;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNrF,OAAA;QAAQmH,OAAO,EAAEC,cAAe;QAACC,QAAQ,EAAEC,OAAQ;QAAChD,KAAK,EAAE;UAAEE,KAAK,EAAE,MAAM;UAAED,OAAO,EAAE,CAAC;UAAEO,UAAU,EAAE,SAAS;UAAEQ,KAAK,EAAE,MAAM;UAAEgB,MAAM,EAAE,MAAM;UAAEtB,YAAY,EAAE,CAAC;UAAEuC,MAAM,EAAED,OAAO,GAAG,aAAa,GAAG,SAAS;UAAEzB,UAAU,EAAE,MAAM;UAAED,QAAQ,EAAE,EAAE;UAAEW,aAAa,EAAE;QAAE,CAAE;QAAA7B,QAAA,EAC9P4C,OAAO,GAAG,SAAS,GAAG;MAAM;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,EACRmC,UAAU,iBAAIxH,OAAA;QAAKsE,KAAK,EAAE;UAAE+B,SAAS,EAAE,EAAE;UAAEf,KAAK,EAAEkC,UAAU,CAACC,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;UAAE7B,QAAQ,EAAE;QAAG,CAAE;QAAAlB,QAAA,EAAE8C;MAAU;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACxIrF,OAAA;QAAKsE,KAAK,EAAE;UAAEsB,QAAQ,EAAE,EAAE;UAAEN,KAAK,EAAE,MAAM;UAAEe,SAAS,EAAE;QAAE,CAAE;QAAA3B,QAAA,GAAC,sBAAK,EAACpE,MAAM,CAAC2F,SAAS,IAAI,KAAK;MAAA;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAGjGrF,OAAA;QAAKsE,KAAK,EAAE;UAAES,MAAM,EAAE,QAAQ;UAAEF,MAAM,EAAE,KAAK;UAAEC,UAAU,EAAE;QAAU;MAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAG9ErF,OAAA;QAAKsE,KAAK,EAAE;UACVuB,UAAU,EAAE,MAAM;UAClBD,QAAQ,EAAE,EAAE;UACZjB,YAAY,EAAE,CAAC;UACfW,KAAK,EAAE,SAAS;UAChBiB,aAAa,EAAE;QACjB,CAAE;QAAA7B,QAAA,EAAC;MAAQ;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACjBrF,OAAA;QAAKsE,KAAK,EAAE;UAAEK,YAAY,EAAE;QAAG,CAAE;QAAAD,QAAA,gBAC/B1E,OAAA;UAAMsE,KAAK,EAAE;YAAEsB,QAAQ,EAAE,EAAE;YAAEN,KAAK,EAAE,MAAM;YAAEuB,WAAW,EAAE;UAAE,CAAE;UAAAnC,QAAA,EAAC;QAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1ErF,OAAA;UACE+G,KAAK,EAAE9F,WAAY;UACnBuF,QAAQ,EAAG5E,CAAC,IAAKV,cAAc,CAACU,CAAC,CAACE,MAAM,CAACiF,KAAK,CAAE;UAChDzC,KAAK,EAAE;YACLE,KAAK,EAAE,MAAM;YACbD,OAAO,EAAE,SAAS;YAClBS,YAAY,EAAE,CAAC;YACfsB,MAAM,EAAE,mBAAmB;YAC3BV,QAAQ,EAAE,EAAE;YACZN,KAAK,EAAE,MAAM;YACboB,eAAe,EAAE;UACnB,CAAE;UAAAhC,QAAA,gBAEF1E,OAAA;YAAQ+G,KAAK,EAAC,KAAK;YAAArC,QAAA,EAAC;UAAa;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CrF,OAAA;YAAQ+G,KAAK,EAAC,KAAK;YAAArC,QAAA,EAAC;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNrF,OAAA;QACEmH,OAAO,EAAE1D,cAAe;QACxB4D,QAAQ,EAAElG,WAAW,IAAI,CAACb,MAAM,CAACoD,GAAI;QACrCY,KAAK,EAAE;UACLE,KAAK,EAAE,MAAM;UACbD,OAAO,EAAE,CAAC;UACVO,UAAU,EAAExE,MAAM,CAACoD,GAAG,GAAG,SAAS,GAAG,SAAS;UAC9C4B,KAAK,EAAE,MAAM;UACbgB,MAAM,EAAE,MAAM;UACdtB,YAAY,EAAE,CAAC;UACfuC,MAAM,EAAGpG,WAAW,IAAI,CAACb,MAAM,CAACoD,GAAG,GAAI,aAAa,GAAG,SAAS;UAChEmC,UAAU,EAAE,MAAM;UAClBD,QAAQ,EAAE,EAAE;UACZW,aAAa,EAAE;QACjB,CAAE;QAAA7B,QAAA,EAEDvD,WAAW,GAAG,SAAS,GAAG,KAAKF,WAAW;MAAI;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,EACRhE,UAAU,iBAAIrB,OAAA;QAAKsE,KAAK,EAAE;UAAE+B,SAAS,EAAE,EAAE;UAAEf,KAAK,EAAEjE,UAAU,CAACoG,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;UAAE7B,QAAQ,EAAE;QAAG,CAAE;QAAAlB,QAAA,EAAErD;MAAU;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACxIrF,OAAA;QAAKsE,KAAK,EAAE;UAAEsB,QAAQ,EAAE,EAAE;UAAEN,KAAK,EAAE,MAAM;UAAEe,SAAS,EAAE;QAAE,CAAE;QAAA3B,QAAA,GAAC,uBAAM,EAACpE,MAAM,CAACoD,GAAG,IAAI,KAAK;MAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzF,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9E,EAAA,CA/NIF,oBAAoB;AAAAqH,EAAA,GAApBrH,oBAAoB;AAiO1B,eAAeA,oBAAoB;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}