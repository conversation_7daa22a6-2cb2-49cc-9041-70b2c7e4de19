{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: 'localhost',\n  port: 1883,\n  // 标准 MQTT 端口\n  wsPort: 9001,\n  // WebSocket 端口\n  topic: 'changli/cloud/v2x/obu/bsm'\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 处理 MQTT 消息\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const data = JSON.parse(message.toString());\n      console.log('收到MQTT消息:', data); // 添加日志，检查消息是否正确接收\n      if (data.type === 'BSM' && data.data) {\n        const bsmData = data.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState); // 添加日志，检查数据解析\n\n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n\n          // 立即更新位置，不使用 TWEEN\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n\n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition); // 添加日志，确认位置更新\n        }\n      }\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n    }\n  };\n\n  // 修改初始化 MQTT 连接\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const client = mqtt.connect(`ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.wsPort}`, {\n      protocol: 'ws',\n      clientId: 'webclient_' + Math.random().toString(16).substr(2, 8)\n    });\n    client.on('connect', () => {\n      console.log('MQTT连接成功');\n      client.subscribe(MQTT_CONFIG.topic, err => {\n        if (err) {\n          console.error('MQTT订阅失败:', err);\n        } else {\n          console.log('MQTT订阅成功:', MQTT_CONFIG.topic);\n        }\n      });\n    });\n    client.on('message', handleMqttMessage);\n    client.on('error', err => {\n      console.error('MQTT错误:', err);\n    });\n    client.on('close', () => {\n      console.log('MQTT连接已关闭');\n    });\n    client.on('offline', () => {\n      console.log('MQTT连接已离线');\n    });\n    client.on('reconnect', () => {\n      console.log('MQTT正在重新连接...');\n    });\n    mqttClientRef.current = client;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    const scene = new THREE.Scene();\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n        scene.add(model);\n\n        // 在校园模型加载完成后初始化场景\n        await initializeScene();\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 15, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      controls.update();\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 清理函数\n    return () => {\n      var _containerRef$current;\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.end();\n      }\n      window.removeEventListener('resize', handleResize);\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.removeChild(renderer.domElement);\n      renderer.dispose();\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"pok6xuZ9wkLpUfjRdkdxrFsX29M=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "MQTT_CONFIG", "broker", "port", "wsPort", "topic", "BASE_URL", "CampusModel", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "clone", "currentUp", "up", "Tween", "to", "x", "y", "z", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "Math", "PI", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "handleMqttMessage", "message", "data", "JSON", "parse", "toString", "type", "bsmData", "newState", "parseFloat", "partLong", "partLat", "partSpeed", "partHeading", "modelPos", "wgs84ToModel", "newPosition", "Vector3", "rotation", "updateMatrix", "updateMatrixWorld", "error", "initMqttClient", "client", "connect", "protocol", "clientId", "random", "substr", "on", "subscribe", "err", "scene", "Scene", "camera", "PerspectiveCamera", "window", "innerWidth", "innerHeight", "set", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "add", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "MeshStandardMaterial", "color", "metalness", "roughness", "envMapIntensity", "map", "name", "children", "length", "xhr", "loaded", "total", "toFixed", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "setTimeout", "model", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "toArray", "相机位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "_containerRef$current", "clearInterval", "end", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "ref", "style", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: 'localhost',\n  port: 1883,  // 标准 MQTT 端口\n  wsPort: 9001,  // WebSocket 端口\n  topic: 'changli/cloud/v2x/obu/bsm'\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 处理 MQTT 消息\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const data = JSON.parse(message.toString());\n      console.log('收到MQTT消息:', data);  // 添加日志，检查消息是否正确接收\n      if (data.type === 'BSM' && data.data) {\n        const bsmData = data.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);  // 添加日志，检查数据解析\n        \n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n          \n          // 立即更新位置，不使用 TWEEN\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n          \n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);  // 添加日志，确认位置更新\n        }\n      }\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n    }\n  };\n\n  // 修改初始化 MQTT 连接\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const client = mqtt.connect(`ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.wsPort}`, {\n      protocol: 'ws',\n      clientId: 'webclient_' + Math.random().toString(16).substr(2, 8)\n    });\n    \n    client.on('connect', () => {\n      console.log('MQTT连接成功');\n      client.subscribe(MQTT_CONFIG.topic, (err) => {\n        if (err) {\n          console.error('MQTT订阅失败:', err);\n        } else {\n          console.log('MQTT订阅成功:', MQTT_CONFIG.topic);\n        }\n      });\n    });\n    \n    client.on('message', handleMqttMessage);\n    \n    client.on('error', (err) => {\n      console.error('MQTT错误:', err);\n    });\n    \n    client.on('close', () => {\n      console.log('MQTT连接已关闭');\n    });\n    \n    client.on('offline', () => {\n      console.log('MQTT连接已离线');\n    });\n    \n    client.on('reconnect', () => {\n      console.log('MQTT正在重新连接...');\n    });\n    \n    mqttClientRef.current = client;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    const scene = new THREE.Scene();\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          15,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      \n      controls.update();\n      renderer.render(scene, camera);\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 清理函数\n    return () => {\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.end();\n      }\n      window.removeEventListener('resize', handleResize);\n      containerRef.current?.removeChild(renderer.domElement);\n      renderer.dispose();\n    };\n  }, []);\n\n  return (\n    <>\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAE,WAAW;EACnBC,IAAI,EAAE,IAAI;EAAG;EACbC,MAAM,EAAE,IAAI;EAAG;EACfC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;AAExC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAG7B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM8B,UAAU,GAAG9B,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM+B,SAAS,GAAG/B,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAM2B,aAAa,GAAGhC,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMiC,eAAe,GAAGjC,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMkC,aAAa,GAAGlC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC;IAC/CsC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAM4C,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG9D,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM+D,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnB,WAAW,CAAC,QAAQ,CAAC;IACrBzB,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAAC4C,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrB,WAAW,CAAC,QAAQ,CAAC;IACrBzB,UAAU,GAAG,QAAQ;IAErB,IAAI2C,SAAS,CAACI,OAAO,IAAI9C,QAAQ,EAAE;MACjC;MACA,MAAM+C,UAAU,GAAGL,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACsB,KAAK,CAAC,CAAC;MACrD,MAAMC,SAAS,GAAGP,SAAS,CAACI,OAAO,CAACI,EAAE,CAACF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAI9D,KAAK,CAACiE,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCC,MAAM,CAACtE,KAAK,CAACuE,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdlB,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACmC,IAAI,CAACd,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDe,KAAK,CAAC,CAAC;;MAEV;MACA,IAAI5E,KAAK,CAACiE,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAACtE,KAAK,CAACuE,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdlB,SAAS,CAACI,OAAO,CAACI,EAAE,CAACW,IAAI,CAACZ,SAAS,CAAC;MACtC,CAAC,CAAC,CACDa,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAG/D,QAAQ,CAACgE,MAAM,CAAChB,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAI9D,KAAK,CAACiE,KAAK,CAACY,aAAa,CAAC,CAC3BX,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAACtE,KAAK,CAACuE,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACd5D,QAAQ,CAACgE,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACArB,SAAS,CAACI,OAAO,CAACmB,MAAM,CAACjE,QAAQ,CAACgE,MAAM,CAAC;QACzChE,QAAQ,CAACkE,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACA9D,QAAQ,CAAC4C,OAAO,GAAG,IAAI;;MAEvB;MACA5C,QAAQ,CAACmE,WAAW,GAAG,EAAE;MACzBnE,QAAQ,CAACoE,WAAW,GAAG,GAAG;MAC1BpE,QAAQ,CAACqE,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;MACtCvE,QAAQ,CAACwE,aAAa,GAAG,CAAC;MAC1BxE,QAAQ,CAACkE,MAAM,CAAC,CAAC;MAEjBO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAACzE,KAAK,EAAE0E,OAAO,KAAK;IAC5C,IAAI;MACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAACI,QAAQ,CAAC,CAAC,CAAC;MAC3CV,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEM,IAAI,CAAC,CAAC,CAAE;MACjC,IAAIA,IAAI,CAACI,IAAI,KAAK,KAAK,IAAIJ,IAAI,CAACA,IAAI,EAAE;QACpC,MAAMK,OAAO,GAAGL,IAAI,CAACA,IAAI;QACzB,MAAMM,QAAQ,GAAG;UACfnE,SAAS,EAAEoE,UAAU,CAACF,OAAO,CAACG,QAAQ,CAAC;UACvCpE,QAAQ,EAAEmE,UAAU,CAACF,OAAO,CAACI,OAAO,CAAC;UACrCpE,KAAK,EAAEkE,UAAU,CAACF,OAAO,CAACK,SAAS,CAAC;UACpCpE,OAAO,EAAEiE,UAAU,CAACF,OAAO,CAACM,WAAW;QACzC,CAAC;QAEDlB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEY,QAAQ,CAAC,CAAC,CAAE;;QAErC;QACA,IAAI9F,gBAAgB,EAAE;UACpB,MAAMoG,QAAQ,GAAGjF,SAAS,CAACmC,OAAO,CAAC+C,YAAY,CAACP,QAAQ,CAACnE,SAAS,EAAEmE,QAAQ,CAAClE,QAAQ,CAAC;UACtF,MAAM0E,WAAW,GAAG,IAAIhH,KAAK,CAACiH,OAAO,CAACH,QAAQ,CAACvC,CAAC,EAAE,GAAG,EAAE,CAACuC,QAAQ,CAACtC,CAAC,CAAC;;UAEnE;UACA9D,gBAAgB,CAACkC,QAAQ,CAACmC,IAAI,CAACiC,WAAW,CAAC;UAC3CtG,gBAAgB,CAACwG,QAAQ,CAAC1C,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGe,QAAQ,CAAChE,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAG;UACxE/E,gBAAgB,CAACyG,YAAY,CAAC,CAAC;UAC/BzG,gBAAgB,CAAC0G,iBAAiB,CAAC,IAAI,CAAC;;UAExC;UACAhF,eAAe,CAACoE,QAAQ,CAAC;UACzBb,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEoB,WAAW,CAAC,CAAC,CAAE;QACzC;MACF;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B3B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,MAAM2B,MAAM,GAAGlH,IAAI,CAACmH,OAAO,CAAC,QAAQrG,WAAW,CAACC,MAAM,IAAID,WAAW,CAACG,MAAM,EAAE,EAAE;MAC9EmG,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,YAAY,GAAGlC,IAAI,CAACmC,MAAM,CAAC,CAAC,CAACtB,QAAQ,CAAC,EAAE,CAAC,CAACuB,MAAM,CAAC,CAAC,EAAE,CAAC;IACjE,CAAC,CAAC;IAEFL,MAAM,CAACM,EAAE,CAAC,SAAS,EAAE,MAAM;MACzBlC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACvB2B,MAAM,CAACO,SAAS,CAAC3G,WAAW,CAACI,KAAK,EAAGwG,GAAG,IAAK;QAC3C,IAAIA,GAAG,EAAE;UACPpC,OAAO,CAAC0B,KAAK,CAAC,WAAW,EAAEU,GAAG,CAAC;QACjC,CAAC,MAAM;UACLpC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEzE,WAAW,CAACI,KAAK,CAAC;QAC7C;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFgG,MAAM,CAACM,EAAE,CAAC,SAAS,EAAE7B,iBAAiB,CAAC;IAEvCuB,MAAM,CAACM,EAAE,CAAC,OAAO,EAAGE,GAAG,IAAK;MAC1BpC,OAAO,CAAC0B,KAAK,CAAC,SAAS,EAAEU,GAAG,CAAC;IAC/B,CAAC,CAAC;IAEFR,MAAM,CAACM,EAAE,CAAC,OAAO,EAAE,MAAM;MACvBlC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IAC1B,CAAC,CAAC;IAEF2B,MAAM,CAACM,EAAE,CAAC,SAAS,EAAE,MAAM;MACzBlC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IAC1B,CAAC,CAAC;IAEF2B,MAAM,CAACM,EAAE,CAAC,WAAW,EAAE,MAAM;MAC3BlC,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC,CAAC;IAEF5D,aAAa,CAACgC,OAAO,GAAGuD,MAAM;EAChC,CAAC;EAED1H,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8B,YAAY,CAACqC,OAAO,EAAE;;IAE3B;IACA,MAAMgE,KAAK,GAAG,IAAIhI,KAAK,CAACiI,KAAK,CAAC,CAAC;;IAE/B;IACA,MAAMC,MAAM,GAAG,IAAIlI,KAAK,CAACmI,iBAAiB,CACxC,EAAE,EACFC,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACDJ,MAAM,CAACtF,QAAQ,CAAC2F,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCL,MAAM,CAAC/C,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBvB,SAAS,CAACI,OAAO,GAAGkE,MAAM;;IAE1B;IACA,MAAMM,QAAQ,GAAG,IAAIxI,KAAK,CAACyI,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAACP,MAAM,CAACC,UAAU,EAAED,MAAM,CAACE,WAAW,CAAC;IACvDE,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAACT,MAAM,CAACU,gBAAgB,CAAC;IAC/CnH,YAAY,CAACqC,OAAO,CAAC+E,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAIjJ,KAAK,CAACkJ,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5DlB,KAAK,CAACmB,GAAG,CAACF,YAAY,CAAC;;IAEvB;IACA,MAAMG,iBAAiB,GAAG,IAAIpJ,KAAK,CAACqJ,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAACxG,QAAQ,CAAC2F,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1CP,KAAK,CAACmB,GAAG,CAACC,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAItJ,KAAK,CAACqJ,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAAC1G,QAAQ,CAAC2F,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3CP,KAAK,CAACmB,GAAG,CAACG,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAIvJ,KAAK,CAACwJ,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAAC3G,QAAQ,CAAC2F,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCgB,SAAS,CAACE,KAAK,GAAGjE,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7B8D,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxB5B,KAAK,CAACmB,GAAG,CAACI,SAAS,CAAC;;IAEpB;IACArI,QAAQ,GAAG,IAAIhB,aAAa,CAACgI,MAAM,EAAEM,QAAQ,CAACQ,UAAU,CAAC;IACzD9H,QAAQ,CAAC2I,aAAa,GAAG,IAAI;IAC7B3I,QAAQ,CAAC4I,aAAa,GAAG,IAAI;IAC7B5I,QAAQ,CAAC6I,kBAAkB,GAAG,KAAK;IACnC7I,QAAQ,CAACmE,WAAW,GAAG,EAAE;IACzBnE,QAAQ,CAACoE,WAAW,GAAG,GAAG;IAC1BpE,QAAQ,CAACqE,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IACtCvE,QAAQ,CAACwE,aAAa,GAAG,CAAC;IAC1BxE,QAAQ,CAACgE,MAAM,CAACqD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BrH,QAAQ,CAACkE,MAAM,CAAC,CAAC;;IAEjB;IACAO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBsC,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBhH,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpB0C,SAAS,EAAE,CAAC,CAACA,SAAS,CAACI;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMgG,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAInK,UAAU,CAAC,CAAC;QACtCmK,aAAa,CAACC,IAAI,CAChB,GAAG7I,QAAQ,uBAAuB,EACjC8I,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAACtC,KAAK;;UAE/B;UACA,MAAMwC,gBAAgB,GAAG,IAAIxK,KAAK,CAACyK,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACG,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAI9K,KAAK,CAAC+K,oBAAoB,CAAC;kBACjDC,KAAK,EAAE,QAAQ;kBAAO;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIR,KAAK,CAACE,QAAQ,CAACO,GAAG,EAAE;kBACtBN,WAAW,CAACM,GAAG,GAAGT,KAAK,CAACE,QAAQ,CAACO,GAAG;gBACtC;;gBAEA;gBACAT,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5BnF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE+E,KAAK,CAACU,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMd,YAAY,CAACe,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMZ,KAAK,GAAGJ,YAAY,CAACe,QAAQ,CAAC,CAAC,CAAC;YACtCd,gBAAgB,CAACrB,GAAG,CAACwB,KAAK,CAAC;UAC7B;;UAEA;UACA3C,KAAK,CAACmB,GAAG,CAACqB,gBAAgB,CAAC;;UAE3B;UACA9J,gBAAgB,GAAG8J,gBAAgB;UAEnC7E,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9B1D,kBAAkB,CAAC,IAAI,CAAC;UACxBgI,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAgB,GAAG,IAAK;UACP7F,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC4F,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDxB,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMyB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMpB,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACA1C,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAIkD,gBAAgB,EAAE;UACpB,MAAMqB,YAAY,GAAG;YACnBxJ,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UACD,MAAMsJ,UAAU,GAAGjK,SAAS,CAACmC,OAAO,CAAC+C,YAAY,CAAC8E,YAAY,CAACxJ,SAAS,EAAEwJ,YAAY,CAACvJ,QAAQ,CAAC;UAChGkI,gBAAgB,CAAC5H,QAAQ,CAAC2F,GAAG,CAACuD,UAAU,CAACvH,CAAC,EAAE,GAAG,EAAE,CAACuH,UAAU,CAACtH,CAAC,CAAC;UAC/DgG,gBAAgB,CAACtD,QAAQ,CAAC1C,CAAC,GAAIgB,IAAI,CAACC,EAAE,GAAGoG,YAAY,CAACrJ,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9E+E,gBAAgB,CAACrD,YAAY,CAAC,CAAC;UAC/BqD,gBAAgB,CAACpD,iBAAiB,CAAC,IAAI,CAAC;UACxCrG,eAAe,GAAGyJ,gBAAgB,CAAC5H,QAAQ,CAACsB,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAOmD,KAAK,EAAE;QACd1B,OAAO,CAAC0B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAM0E,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAIhC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAM+B,WAAW,GAAIC,WAAW,IAAK;UACnCxG,OAAO,CAACC,GAAG,CAAC,WAAWoG,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAInM,UAAU,CAAC,CAAC;UAC/BmM,MAAM,CAAC/B,IAAI,CACT2B,GAAG,EACF1B,IAAI,IAAK;YACR3E,OAAO,CAACC,GAAG,CAAC,WAAWoG,GAAG,EAAE,CAAC;YAC7B9B,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAkB,GAAG,IAAK;YACP7F,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC4F,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAtE,KAAK,IAAK;YACT1B,OAAO,CAAC0B,KAAK,CAAC,SAAS2E,GAAG,EAAE,EAAE3E,KAAK,CAAC;YACpC,IAAI8E,WAAW,GAAG,CAAC,EAAE;cACnBxG,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3ByG,UAAU,CAAC,MAAMH,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACLhC,MAAM,CAAC9C,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAED6E,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAInM,UAAU,CAAC,CAAC;IAC/BmM,MAAM,CAAC/B,IAAI,CACT,GAAG7I,QAAQ,4BAA4B,EACvC,MAAO8I,IAAI,IAAK;MACd,IAAI;QACF,MAAMgC,KAAK,GAAGhC,IAAI,CAACtC,KAAK;QACxBsE,KAAK,CAACC,KAAK,CAAChE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxB+D,KAAK,CAAC1J,QAAQ,CAAC2F,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3BP,KAAK,CAACmB,GAAG,CAACmD,KAAK,CAAC;;QAEhB;QACA,MAAMV,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC,OAAOvE,KAAK,EAAE;QACd1B,OAAO,CAAC0B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACAmE,GAAG,IAAK;MACP7F,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC4F,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAtE,KAAK,IAAK;MACT1B,OAAO,CAAC0B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B1B,OAAO,CAAC0B,KAAK,CAAC,OAAO,EAAE;QACrBmF,IAAI,EAAEnF,KAAK,CAACf,IAAI;QAChBmG,IAAI,EAAEpF,KAAK,CAACpB,OAAO;QACnByG,KAAK,EAAE,GAAGlL,QAAQ,4BAA4B;QAC9CmL,KAAK,EAAE,GAAGnL,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMoL,OAAO,GAAGA,CAAA,KAAM;MACpBC,qBAAqB,CAACD,OAAO,CAAC;;MAE9B;MACAxM,KAAK,CAACgF,MAAM,CAAC,CAAC;MAEd,IAAInE,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAAC4C,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMgJ,UAAU,GAAGpM,gBAAgB,CAACkC,QAAQ,CAACsB,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAM6I,eAAe,GAAGrM,gBAAgB,CAACwG,QAAQ,CAAC1C,CAAC;;QAEnD;QACA;QACA,MAAMwI,gBAAgB,GAAG,EAAED,eAAe,GAAGvH,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAMwH,YAAY,GAAG,IAAIjN,KAAK,CAACiH,OAAO,CACpC,CAAC,EAAE,GAAGzB,IAAI,CAAC0H,GAAG,CAACF,gBAAgB,CAAC,EAChC,EAAE,EACF,CAAC,EAAE,GAAGxH,IAAI,CAAC2H,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACA9E,MAAM,CAACtF,QAAQ,CAACmC,IAAI,CAAC+H,UAAU,CAAC,CAAC3D,GAAG,CAAC8D,YAAY,CAAC;;QAElD;QACA/E,MAAM,CAAC9D,EAAE,CAACmE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAM6E,YAAY,GAAGN,UAAU,CAAC5I,KAAK,CAAC,CAAC;QACvCgE,MAAM,CAAC/C,MAAM,CAACiI,YAAY,CAAC;;QAE3B;QACAlF,MAAM,CAACmF,sBAAsB,CAAC,CAAC;QAC/BnF,MAAM,CAACf,YAAY,CAAC,CAAC;QACrBe,MAAM,CAACd,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACAlG,QAAQ,CAAC4C,OAAO,GAAG,KAAK;;QAExB;QACA5C,QAAQ,CAACgE,MAAM,CAACH,IAAI,CAAC+H,UAAU,CAAC;QAChC5L,QAAQ,CAACkE,MAAM,CAAC,CAAC;QAEjBO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnB0H,IAAI,EAAER,UAAU,CAACS,OAAO,CAAC,CAAC;UAC1BC,IAAI,EAAEtF,MAAM,CAACtF,QAAQ,CAAC2K,OAAO,CAAC,CAAC;UAC/BE,IAAI,EAAEL,YAAY,CAACG,OAAO,CAAC,CAAC;UAC5BG,IAAI,EAAExF,MAAM,CAACyF,iBAAiB,CAAC,IAAI3N,KAAK,CAACiH,OAAO,CAAC,CAAC,CAAC,CAACsG,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAItM,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAAC4C,OAAO,GAAG,IAAI;;QAEvB;QACAoE,MAAM,CAAC9D,EAAE,CAACmE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAI/C,IAAI,CAACoI,GAAG,CAAC1F,MAAM,CAACtF,QAAQ,CAAC4B,CAAC,CAAC,GAAG,EAAE,EAAE;UACpC0D,MAAM,CAACtF,QAAQ,CAAC2F,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BrH,QAAQ,CAACgE,MAAM,CAACqD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BL,MAAM,CAAC/C,MAAM,CAACjE,QAAQ,CAACgE,MAAM,CAAC;UAC9BhE,QAAQ,CAACkE,MAAM,CAAC,CAAC;QACnB;MACF;MAEAlE,QAAQ,CAACkE,MAAM,CAAC,CAAC;MACjBoD,QAAQ,CAACqF,MAAM,CAAC7F,KAAK,EAAEE,MAAM,CAAC;IAChC,CAAC;IAED0E,OAAO,CAAC,CAAC;;IAET;IACA,MAAMkB,YAAY,GAAGA,CAAA,KAAM;MACzB5F,MAAM,CAAC6F,MAAM,GAAG3F,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,WAAW;MACtDJ,MAAM,CAACmF,sBAAsB,CAAC,CAAC;MAC/B7E,QAAQ,CAACG,OAAO,CAACP,MAAM,CAACC,UAAU,EAAED,MAAM,CAACE,WAAW,CAAC;IACzD,CAAC;IACDF,MAAM,CAAC4F,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACA1F,MAAM,CAAC6F,aAAa,GAAG,MAAM;MAC3B,IAAIrK,SAAS,CAACI,OAAO,EAAE;QACrBJ,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAAC2F,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC3E,SAAS,CAACI,OAAO,CAACmB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCvB,SAAS,CAACI,OAAO,CAACmD,YAAY,CAAC,CAAC;QAChCvD,SAAS,CAACI,OAAO,CAACoD,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAIlG,QAAQ,EAAE;UACZA,QAAQ,CAACgE,MAAM,CAACqD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BrH,QAAQ,CAAC4C,OAAO,GAAG,IAAI;UACvB5C,QAAQ,CAACkE,MAAM,CAAC,CAAC;QACnB;QAEAnE,UAAU,GAAG,QAAQ;QACrB0E,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MAAA,IAAAsI,qBAAA;MACX,IAAIrN,oBAAoB,EAAE;QACxBsN,aAAa,CAACtN,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;MACA,IAAImB,aAAa,CAACgC,OAAO,EAAE;QACzBhC,aAAa,CAACgC,OAAO,CAACoK,GAAG,CAAC,CAAC;MAC7B;MACAhG,MAAM,CAACiG,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;MAClD,CAAAI,qBAAA,GAAAvM,YAAY,CAACqC,OAAO,cAAAkK,qBAAA,uBAApBA,qBAAA,CAAsBI,WAAW,CAAC9F,QAAQ,CAACQ,UAAU,CAAC;MACtDR,QAAQ,CAAC+F,OAAO,CAAC,CAAC;IACpB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEhO,OAAA,CAAAE,SAAA;IAAA6K,QAAA,gBACE/K,OAAA;MAAKiO,GAAG,EAAE7M,YAAa;MAAC8M,KAAK,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpExO,OAAA;MAAKkO,KAAK,EAAE9L,oBAAqB;MAAA2I,QAAA,gBAC/B/K,OAAA;QACEkO,KAAK,EAAE;UACL,GAAGtL,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EuI,KAAK,EAAEvI,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFuM,OAAO,EAAEnL,kBAAmB;QAAAyH,QAAA,EAC7B;MAED;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxO,OAAA;QACEkO,KAAK,EAAE;UACL,GAAGtL,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EuI,KAAK,EAAEvI,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFuM,OAAO,EAAEjL,kBAAmB;QAAAuH,QAAA,EAC7B;MAED;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAArN,EAAA,CAvjBMD,WAAW;AAAAwN,EAAA,GAAXxN,WAAW;AAwjBjB,SAASyN,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAACV,KAAK,GAAG,GAAG;EAClBU,MAAM,CAACT,MAAM,GAAG,EAAE;;EAElB;EACAY,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAACV,KAAK,GAAC,CAAC,EAAEU,MAAM,CAACT,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAMkB,OAAO,GAAG,IAAI7P,KAAK,CAAC8P,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAI/P,KAAK,CAACgQ,cAAc,CAAC;IAC9C5E,GAAG,EAAEyE,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAIlQ,KAAK,CAACmQ,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAAC3D,KAAK,CAAChE,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAO2H,MAAM;AACf;;AAEA;AACA9H,MAAM,CAACgI,WAAW,GAAG,CAAC7L,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EAChC,IAAI/D,gBAAgB,EAAE;IACpBA,gBAAgB,CAACkC,QAAQ,CAAC2F,GAAG,CAAChE,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACtC/D,gBAAgB,CAACyG,YAAY,CAAC,CAAC;IAC/BzG,gBAAgB,CAAC0G,iBAAiB,CAAC,IAAI,CAAC;IACxCzB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAACrB,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA2D,MAAM,CAACiI,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMnI,MAAM,GAAGmH,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAItI,MAAM,EAAE;MACV;MACA,MAAMuI,MAAM,GAAGvI,MAAM,CAACtF,QAAQ,CAACsB,KAAK,CAAC,CAAC;;MAEtC;MACAgE,MAAM,CAACtF,QAAQ,CAAC2F,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BL,MAAM,CAAC9D,EAAE,CAACmE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBL,MAAM,CAAC/C,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACA+C,MAAM,CAACf,YAAY,CAAC,CAAC;MACrBe,MAAM,CAACd,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMlG,QAAQ,GAAGmO,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIxP,QAAQ,EAAE;QACZA,QAAQ,CAACgE,MAAM,CAACqD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BrH,QAAQ,CAACkE,MAAM,CAAC,CAAC;MACnB;MAEAO,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxB+K,GAAG,EAAEF,MAAM,CAAClD,OAAO,CAAC,CAAC;QACrBqD,GAAG,EAAE1I,MAAM,CAACtF,QAAQ,CAAC2K,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOsD,CAAC,EAAE;IACVlL,OAAO,CAAC0B,KAAK,CAAC,YAAY,EAAEwJ,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;AAED,eAAepP,WAAW;AAAC,IAAAwN,EAAA;AAAA6B,YAAA,CAAA7B,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}