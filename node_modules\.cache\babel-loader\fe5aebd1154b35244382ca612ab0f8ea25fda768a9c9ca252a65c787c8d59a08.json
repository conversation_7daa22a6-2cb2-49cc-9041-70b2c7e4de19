{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.3; // 滤波系数，值越小滤波效果越强（0-1之间）\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n  bsm: 'changli/cloud/v2x/obu/bsm',\n  rsm: 'changli/cloud/v2x/rsu/rsm',\n  scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',\n  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat' // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 添加位置滤波函数\nconst filterPosition = newPos => {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n};\n\n// 添加朝向滤波函数\nconst filterRotation = newRotation => {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n  return filteredRotation;\n};\nconst CampusModel = ({\n  className,\n  onCurrentRSUChange,\n  selectedRSUs\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null); // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,\n    // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null,\n    phases: []\n  });\n\n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({\n    topic: '',\n    content: ''\n  });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        var _payload$data;\n        console.log('收到RSM消息:', payload);\n\n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n\n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          console.log('忽略过期的RSM消息:', {\n            设备MAC: deviceMac,\n            消息时间戳: messageTimestamp,\n            最新时间戳: lastTimestamp\n          });\n          return;\n        }\n\n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        console.log('RSM消息时间戳更新:', {\n          设备MAC: deviceMac,\n          时间戳: messageTimestamp,\n          是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        });\n        const participants = ((_payload$data = payload.data) === null || _payload$data === void 0 ? void 0 : _payload$data.participants) || [];\n        const rsuid = payload.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id = rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = preloadedModel.clone();\n              // 根据类型调整高度\n              const height = type === '3' ? 0.5 : 1.0; // 行人模型贴近地面\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 0.5 : 1.0, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        console.log('收到BSM消息:', payload);\n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n\n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        const newRotation = Math.PI - newState.heading * Math.PI / 180;\n\n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n\n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          newVehicleModel.position.copy(newPosition);\n          newVehicleModel.rotation.y = newRotation;\n          scene.add(newVehicleModel);\n\n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1',\n            // 设置为机动车类型\n            isMain: isMainVehicle\n          });\n          console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition);\n          const filteredRotation = filterRotation(newRotation);\n\n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n\n          console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n\n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 1秒\n\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n\n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        console.log('收到SPAT消息:', message);\n        try {\n          const payload = JSON.parse(message);\n\n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              console.log(`处理路口ID: ${interId} 的SPAT消息`);\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? getDirectionFromCode(phase.trafficDirec) : getPhaseDirection(phaseId);\n\n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n\n                  // 在地图上更新对应的交通灯状态\n                  const trafficLightKey = interId;\n                  const trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  if (trafficLightModel) {\n                    // 创建相位信息对象\n                    const phaseInfo = {\n                      phaseId,\n                      direction,\n                      trafficLight: lightState,\n                      remainTime\n                    };\n\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n\n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n      }\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        console.log('收到RSI消息:', payload);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(parseFloat(rsiData.posLong), parseFloat(rsiData.posLat));\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          switch (eventType) {\n            case '401':\n              // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':\n              // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':\n              // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':\n              // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':\n              // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002':\n              // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':\n              // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        console.log('收到场景事件消息:', payload);\n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch (sceneType) {\n          case '2':\n            // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':\n            // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':\n            // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':\n            // 限速提醒\n            const speedLimit = sceneData.eventData1; // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':\n            // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':\n            // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':\n            // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':\n            // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':\n            // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':\n            // 信号灯优先\n            const priorityType = sceneData.eventData1; // 优先类型\n            const duration = sceneData.eventData2; // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: payload.type,\n        data: payload\n      });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n\n        // 检查scene是否初始化\n        if (scene) {\n          scene.add(model);\n\n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } else {\n          console.error('无法添加模型：场景未初始化');\n        }\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 100, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n\n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n\n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach(lightObj => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n\n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n\n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n\n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n\n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n\n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n\n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {\n          // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n\n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = event => {\n        if (scene && cameraRef.current) {\n          console.log('检测到点击事件', event.clientX, event.clientY);\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        } else {\n          console.warn('点击事件处理失败: scene或camera未初始化');\n        }\n      };\n\n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n\n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n\n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({\n      color: 0x333333\n    });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n\n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({\n      color: 0x666666\n    });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n\n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(10, 8, 8);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          transparent: true,\n          opacity: 0.1,\n          // 几乎透明\n          depthWrite: false\n        });\n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0); // 放在红绿灯位置\n\n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n\n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        addClickHelpers();\n      }\n    }, 5500); // 延迟略长于debugTrafficLights\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // 在组件加载完毕后调用调试函数\n  useEffect(() => {\n    // 延迟5秒调用调试函数，确保所有模型都已加载\n    const timer = setTimeout(() => {\n      console.log('调用场景调试函数');\n      if (window.debugScene) {\n        window.debugScene(); // 使用全局函数\n      } else {\n        console.error('debugScene函数未定义');\n      }\n    }, 5000);\n    return () => clearTimeout(timer);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u8DEF\\u53E3\\u9009\\u62E9\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1414,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u8DEF\\u53E3\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      options: intersectionsData.intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1415,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1430,\n      columnNumber: 7\n    }, this), trafficLightPopover.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${trafficLightPopover.position.x}px`,\n        top: `${trafficLightPopover.position.y}px`,\n        transform: 'translate(-50%, -100%)',\n        zIndex: 1003,\n        backgroundColor: 'rgba(0, 0, 0, 0.85)',\n        color: 'white',\n        borderRadius: '4px',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n        padding: '0'\n      },\n      children: [trafficLightPopover.content, /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          position: 'absolute',\n          top: '2px',\n          right: '2px',\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          fontSize: '14px',\n          cursor: 'pointer',\n          padding: '4px 8px'\n        },\n        onClick: () => handleClosePopover(setTrafficLightPopover),\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1449,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1434,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1469,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1479,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1468,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"c1hAsNK3+1lH67Pan8p9MGtOCxQ=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    try {\n      const [vehicleGltf, cyclistGltf, peopleGltf, trafficLightGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`), loader.loadAsync(`${BASE_URL}/changli2/traffic light.glb`)]);\n\n      // 处理机动车模型\n      preloadedVehicleModel = vehicleGltf.scene;\n      preloadedVehicleModel.traverse(child => {\n        if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n            color: 0xffffff,\n            metalness: 0.2,\n            roughness: 0.1,\n            envMapIntensity: 1.0\n          });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n        }\n      });\n\n      // 处理非机动车模型\n      preloadedCyclistModel = cyclistGltf.scene;\n      // 设置非机动车模型的缩放\n      preloadedCyclistModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedCyclistModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n\n      // 处理行人模型\n      preloadedPeopleModel = peopleGltf.scene;\n      // 设置行人模型的缩放\n      preloadedPeopleModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedPeopleModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n\n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n\n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n\n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/traffic light.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = type => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y); // 设置位置，稍微升高以便可见\n\n  // 为标记添加一个定时器，在5秒后自动移除\n  setTimeout(() => {\n    scene.remove(sprite);\n  }, 500);\n\n  // 将标记添加到场景中\n  scene.add(sprite);\n  console.log('添加场景事件标记:', {\n    位置: position,\n    文本: text,\n    颜色: color\n  });\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = converterInstance => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n\n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach(lightObj => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n\n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n\n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n\n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n\n        // *** 重要：设置为可被交互 ***\n        trafficLightModel.traverse(child => {\n          // 设置所有子对象可被点击\n          if (child.isMesh) {\n            // 确保材质能够被射线检测到\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n\n            // 设置较高的渲染顺序\n            child.renderOrder = 100;\n          }\n        });\n\n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n\n        // 添加一个专门用于点击的大型碰撞体\n        const colliderGeometry = new THREE.SphereGeometry(15, 12, 12);\n        const colliderMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          transparent: true,\n          opacity: 0.0,\n          // 完全透明\n          depthWrite: false\n        });\n        const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n        collider.name = `交通灯碰撞体-${intersection.name}`;\n        collider.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name,\n          isCollider: true\n        };\n        trafficLightModel.add(collider);\n\n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n\n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个透明的碰撞体，代替原来的黑色长方体\n  const colliderGeometry = new THREE.SphereGeometry(15, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xffffff,\n    transparent: true,\n    opacity: 0.0,\n    // 完全透明\n    depthWrite: false\n  });\n  const trafficLightModel = new THREE.Mesh(colliderGeometry, colliderMaterial);\n\n  // 给模型一个名称便于调试\n  trafficLightModel.name = `交通灯碰撞体-${intersection.name}`;\n\n  // 设置位置 - 保持相同高度以便于点击\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n\n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n\n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的交通灯碰撞体, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = converterInstance => {\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = phaseId => {\n  switch (phaseId) {\n    case '1':\n      return '北进口左转';\n    case '2':\n      return '北进口直行';\n    case '3':\n      return '北进口右转';\n    case '5':\n      return '东进口左转';\n    case '6':\n      return '东进口直行';\n    case '7':\n      return '东进口右转';\n    case '9':\n      return '南进口左转';\n    case '10':\n      return '南进口直行';\n    case '11':\n      return '南进口右转';\n    case '13':\n      return '西进口左转';\n    case '14':\n      return '西进口直行';\n    case '15':\n      return '西进口右转';\n    default:\n      return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = dirCode => {\n  switch (dirCode) {\n    case 'N':\n      return '北向南';\n    case 'S':\n      return '南向北';\n    case 'E':\n      return '东向西';\n    case 'W':\n      return '西向东';\n    case 'NE':\n      return '东北向西南';\n    case 'NW':\n      return '西北向东南';\n    case 'SE':\n      return '东南向西北';\n    case 'SW':\n      return '西南向东北';\n    default:\n      return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n\n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = (event.clientX - rect.left) / container.clientWidth * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n\n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 5;\n  raycaster.params.Line.threshold = 5;\n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  console.log('鼠标点击位置:', mouseX, mouseY);\n\n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n\n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'userData:', intersect.object.userData);\n    });\n\n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId);\n\n      // 显示弹窗\n      window.showTrafficLightPopup(interId);\n      return;\n    }\n  }\n\n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  console.log('射线检测到的对象数量:', intersects.length);\n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', 'userData:', obj.userData, '距离:', intersect.distance);\n    });\n\n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        console.log('检测到红绿灯点击, 路口ID:', interId);\n\n        // 显示弹窗\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n\n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n\n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.3; // 设置一个阈值，只有距离小于此值的才会被选中\n\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n\n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n\n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      console.log(`路口 ${interId} 的距离:`, distance);\n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = {\n          interId,\n          distance\n        };\n      }\n    }\n  });\n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n\n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  console.log('未检测到任何红绿灯点击');\n\n  // 如果用户点击了其他任何地方，关闭现有的弹窗\n  if (setPopoverState) {\n    setPopoverState(prev => {\n      if (prev.visible) {\n        return {\n          ...prev,\n          visible: false\n        };\n      }\n      return prev;\n    });\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = setPopoverState => {\n  setPopoverState(prev => ({\n    ...prev,\n    visible: false\n  }));\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = interId => {\n  try {\n    var _document$querySelect, _document$querySelect2;\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n\n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      return false;\n    }\n\n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n\n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 创建弹出窗口内容\n    let content;\n    if (stateInfo && stateInfo.phases) {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '10px',\n          width: '300px',\n          maxHeight: '400px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '10px',\n            fontSize: '16px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '5px'\n          },\n          children: [intersection.name, \" (ID: \", interId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2096,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                break;\n            }\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '5px',\n                borderRadius: '4px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: getPhaseDirection(phase.phaseId)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2121,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2125,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 5px',\n                    borderRadius: '2px'\n                  },\n                  children: phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2126,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2124,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2137,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2138,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2136,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2115,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2105,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2095,\n        columnNumber: 9\n      }, this);\n    } else {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          maxWidth: '200px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '8px'\n          },\n          children: intersection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u8DEF\\u53E3ID: \", interId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2151,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2148,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2;\n    const centerY = window.innerHeight / 2;\n\n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = (_document$querySelect = document.querySelector('#root')) === null || _document$querySelect === void 0 ? void 0 : (_document$querySelect2 = _document$querySelect.__REACT_INSTANCE) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.setTrafficLightPopover;\n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: {\n          x: centerX,\n          y: centerY\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '10px';\n      popover.style.maxWidth = '300px';\n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 10px; font-size: 16px; border-bottom: 1px solid #eee; padding-bottom: 5px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 2px; right: 2px; background: none; border: none; color: white; font-size: 14px; cursor: pointer; padding: 4px 8px;\">×</button>\n      `;\n      document.body.appendChild(popover);\n\n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  return list;\n};\n\n// 添加全局调试方法\nwindow.debugScene = function () {\n  if (!scene) {\n    console.error('场景未初始化，无法调试红绿灯');\n    return;\n  }\n  console.log('开始调试红绿灯模型...');\n\n  // 检查红绿灯映射\n  console.log('红绿灯映射数量:', trafficLightsMap.size);\n\n  // 统计场景中的可互动对象\n  let interactiveObjects = 0;\n  let trafficLightObjects = 0;\n\n  // 遍历场景中的所有对象\n  scene.traverse(object => {\n    // 检查是否有userData\n    if (object.userData && Object.keys(object.userData).length > 0) {\n      interactiveObjects++;\n\n      // 检查是否是红绿灯\n      if (object.userData.type === 'trafficLight') {\n        trafficLightObjects++;\n        console.log('找到红绿灯对象:', {\n          名称: object.name || '无名称',\n          类型: object.userData.type,\n          路口ID: object.userData.interId,\n          位置: object.position.toArray(),\n          可见性: object.visible,\n          是否是网格: object.isMesh,\n          userData: object.userData\n        });\n      }\n    }\n  });\n  console.log('场景中的可互动对象数量:', interactiveObjects);\n  console.log('红绿灯对象数量:', trafficLightObjects);\n\n  // 遍历红绿灯映射，创建高亮标记\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 获取红绿灯位置\n      const position = lightObj.model.position.clone();\n\n      // 创建一个高亮球体\n      const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n      const highlightMaterial = new THREE.MeshBasicMaterial({\n        color: 0xff00ff,\n        transparent: true,\n        opacity: 0.7\n      });\n      const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n\n      // 设置位置，稍微偏移，避免遮挡\n      highlightMesh.position.set(position.x, position.y + 15, position.z);\n\n      // 添加到场景\n      scene.add(highlightMesh);\n\n      // 添加用户数据，方便调试\n      highlightMesh.userData = {\n        type: 'trafficLightHighlight',\n        interId: interId,\n        name: lightObj.intersection.name,\n        originalPosition: position.toArray()\n      };\n\n      // 5秒后自动移除高亮标记\n      setTimeout(() => {\n        scene.remove(highlightMesh);\n      }, 5000);\n      console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n    }\n  });\n\n  // 将调试信息显示在控制台\n  console.log('红绿灯调试信息:', {\n    红绿灯映射数量: trafficLightsMap.size,\n    红绿灯状态数量: trafficLightStates.size,\n    场景中红绿灯对象数量: trafficLightObjects,\n    射线检测启用: true\n  });\n};\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = interId => {\n  try {\n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    console.log('当前trafficLightStates大小:', trafficLightStates.size);\n\n    // 如果没有指定ID，则使用第一个红绿灯\n    if (!interId && trafficLightsMap.size > 0) {\n      interId = Array.from(trafficLightsMap.keys())[0];\n      console.log('未指定ID，使用第一个红绿灯ID:', interId);\n    }\n\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n\n      // 输出所有红绿灯ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        var _light$intersection;\n        console.log(`- ${id}: ${((_light$intersection = light.intersection) === null || _light$intersection === void 0 ? void 0 : _light$intersection.name) || '未知'}`);\n      });\n      return false;\n    }\n\n    // 查找红绿灯状态信息\n    const stateInfo = trafficLightStates.get(interId);\n    console.log(`路口ID ${interId} 的状态信息:`, stateInfo);\n    const intersection = trafficLight.intersection;\n    console.log('路口信息:', intersection);\n\n    // 创建弹窗内容\n    let content;\n    if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n      // 添加一个检查相位数据的打印\n      console.log('相位数据详情:');\n      stateInfo.phases.forEach((phase, index) => {\n        console.log(`相位 ${index + 1}: ID=${phase.phaseId}, 状态=${phase.trafficLight}, 剩余时间=${phase.remainTime}`);\n      });\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '10px',\n          width: '300px',\n          maxHeight: '400px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '10px',\n            fontSize: '16px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '5px'\n          },\n          children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", interId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2376,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            let lightText;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                lightText = '绿灯';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                lightText = '黄灯';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                lightText = '红灯';\n                break;\n            }\n            const direction = getPhaseDirection(phase.phaseId);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '5px',\n                borderRadius: '4px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: [direction, \" (\\u76F8\\u4F4DID: \", phase.phaseId, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2415,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2419,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 5px',\n                    borderRadius: '2px'\n                  },\n                  children: lightText\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2420,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2418,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2431,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2432,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2430,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2409,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '10px',\n            fontSize: '12px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date(stateInfo.updateTime).toLocaleTimeString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2438,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2375,\n        columnNumber: 9\n      }, this);\n    } else {\n      // 如果没有状态信息，创建一个示例内容\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '10px',\n          width: '300px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '10px'\n          },\n          children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", interId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2447,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#ff4d4f'\n          },\n          children: \"\\u8BE5\\u8DEF\\u53E3\\u6682\\u65E0\\u7EA2\\u7EFF\\u706F\\u72B6\\u6001\\u4FE1\\u606F\\uFF0C\\u8BF7\\u7B49\\u5F85SPAT\\u6D88\\u606F\\u66F4\\u65B0\\u3002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2450,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '10px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.testTrafficLightWithMockData(interId),\n            style: {\n              background: '#1890ff',\n              color: 'white',\n              border: 'none',\n              padding: '5px 10px',\n              borderRadius: '2px',\n              cursor: 'pointer'\n            },\n            children: \"\\u751F\\u6210\\u6A21\\u62DF\\u6570\\u636E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2454,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2453,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '10px',\n            fontSize: '12px',\n            color: '#888'\n          },\n          children: [\"\\u5F53\\u524D\\u65F6\\u95F4: \", new Date().toLocaleTimeString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2468,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2446,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 设置弹窗位置在屏幕中央\n    const x = window.innerWidth / 2;\n    const y = window.innerHeight / 2;\n\n    // 更新弹窗状态\n    if (window._setTrafficLightPopover) {\n      console.log('调用_setTrafficLightPopover更新弹窗状态', {\n        visible: true,\n        interId,\n        position: {\n          x,\n          y\n        }\n      });\n      window._setTrafficLightPopover({\n        visible: true,\n        interId: interId,\n        position: {\n          x,\n          y\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n      console.log(`已显示路口 ${(intersection === null || intersection === void 0 ? void 0 : intersection.name) || interId} 的红绿灯状态弹窗`);\n      return true;\n    } else {\n      console.error('无法找到setTrafficLightPopover函数');\n      return false;\n    }\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// 添加全局模拟SPAT数据生成函数\nwindow.generateMockSpatData = () => {\n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.error('红绿灯映射为空，无法生成模拟数据');\n    return false;\n  }\n  console.log('开始生成模拟SPAT数据...');\n\n  // 可能的相位ID和对应方向\n  const phaseIds = ['1', '2', '3',\n  // 北进口\n  '5', '6', '7',\n  // 东进口 \n  '9', '10', '11',\n  // 南进口\n  '13', '14', '15' // 西进口\n  ];\n\n  // 准备SPAT数据结构\n  const spatMessage = {\n    data: {\n      intersections: []\n    },\n    tm: Date.now(),\n    source: 2,\n    type: 'SPAT',\n    mac: 'mock-device-id'\n  };\n\n  // 为每个红绿灯生成模拟数据\n  trafficLightsMap.forEach((light, interId) => {\n    var _light$intersection2;\n    // 为该路口生成3-6个随机相位\n    const phaseCount = Math.floor(Math.random() * 4) + 3;\n    const phases = [];\n\n    // 随机选择相位ID\n    const selectedPhaseIds = [];\n    while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n      const randomIndex = Math.floor(Math.random() * phaseIds.length);\n      const phaseId = phaseIds[randomIndex];\n\n      // 避免重复选择同一个ID\n      if (!selectedPhaseIds.includes(phaseId)) {\n        selectedPhaseIds.push(phaseId);\n      }\n    }\n\n    // 为每个选中的相位生成随机状态\n    selectedPhaseIds.forEach(phaseId => {\n      // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n      const lightIndex = Math.floor(Math.random() * 3);\n      const stateLabels = ['red', 'yellow', 'green'];\n\n      // 随机生成剩余时间 (1-60秒)\n      const remainTime = Math.floor(Math.random() * 60) + 1;\n\n      // 添加相位信息 - 符合实际数据结构\n      phases.push({\n        phaseId: phaseId,\n        state: stateLabels[lightIndex],\n        remainTime: remainTime\n      });\n    });\n\n    // 添加交叉口数据到SPAT消息\n    spatMessage.data.intersections.push({\n      id: interId,\n      phases: phases\n    });\n\n    // 同时更新本地状态方便测试\n    const phaseInfos = phases.map(phase => ({\n      phaseId: phase.phaseId,\n      trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n      remainTime: phase.remainTime,\n      lastUpdate: Date.now()\n    }));\n    trafficLightStates.set(interId, {\n      updateTime: Date.now(),\n      phases: phaseInfos\n    });\n    console.log(`为路口 ${((_light$intersection2 = light.intersection) === null || _light$intersection2 === void 0 ? void 0 : _light$intersection2.name) || interId} 生成了 ${phases.length} 个相位的模拟数据`);\n  });\n\n  // 模拟调用SPAT消息处理函数\n  try {\n    console.log('处理模拟SPAT消息:', spatMessage);\n    const message = JSON.stringify(spatMessage);\n    // 间接通过handleMqttMessage处理模拟数据\n    handleMqttMessage(MQTT_CONFIG.spat, message);\n  } catch (error) {\n    console.error('处理模拟SPAT消息失败:', error);\n  }\n  console.log('模拟SPAT数据生成完成');\n  return true;\n};\n\n// 添加全局函数：生成模拟数据并显示红绿灯弹窗\nwindow.testTrafficLightWithMockData = interId => {\n  // 先生成模拟数据\n  window.generateMockSpatData();\n\n  // 延迟100ms确保数据已生成\n  setTimeout(() => {\n    // 显示红绿灯弹窗\n    window.showTrafficLightPopup(interId);\n  }, 100);\n  return '模拟数据已生成，正在显示红绿灯弹窗...';\n};\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = object => {\n  let current = object;\n\n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n\n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n\n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n\n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n\n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n\n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = (x - rect.left) / canvas.clientWidth * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    console.log('归一化坐标:', mouseX, mouseY);\n\n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n\n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n\n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'position:', intersect.object.position.toArray(), 'userData:', intersect.object.userData);\n\n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      return true;\n    }\n\n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', '类型:', obj.type, '位置:', obj.position.toArray(), '距离:', intersect.distance, 'userData:', obj.userData);\n    });\n\n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        var _lightObj$intersectio;\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n\n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n\n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n\n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        if (isVisible) {\n          visibleCount++;\n        }\n        console.log(`红绿灯 ${interId}:`, {\n          名称: ((_lightObj$intersectio = lightObj.intersection) === null || _lightObj$intersectio === void 0 ? void 0 : _lightObj$intersectio.name) || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n\n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n// 添加全局强制点击红绿灯的函数\nwindow.forceClickTrafficLight = interId => {\n  if (!interId && trafficLightsMap.size > 0) {\n    // 使用第一个红绿灯\n    interId = Array.from(trafficLightsMap.keys())[0];\n  }\n  if (!interId) {\n    console.error('找不到红绿灯ID');\n    return false;\n  }\n  console.log(`强制点击红绿灯 ${interId}`);\n  const lightObj = trafficLightsMap.get(interId);\n  if (!lightObj || !lightObj.model) {\n    console.error(`找不到ID为 ${interId} 的红绿灯模型`);\n    return false;\n  }\n\n  // 确保模型可见\n  lightObj.model.visible = true;\n  lightObj.model.traverse(child => {\n    child.visible = true;\n  });\n\n  // 获取红绿灯的世界位置\n  const worldPos = new THREE.Vector3();\n  lightObj.model.getWorldPosition(worldPos);\n\n  // 将世界位置投影到屏幕\n  const screenPos = worldPos.clone().project(cameraRef.current);\n\n  // 计算屏幕像素坐标\n  const canvas = document.querySelector('canvas');\n  if (canvas) {\n    const rect = canvas.getBoundingClientRect();\n    const pixelX = (screenPos.x + 1) / 2 * canvas.clientWidth + rect.left;\n    const pixelY = (-screenPos.y + 1) / 2 * canvas.clientHeight + rect.top;\n    console.log('红绿灯屏幕坐标:', pixelX, pixelY);\n\n    // 测试在该位置的点击\n    window.testClickDetection(pixelX, pixelY);\n  }\n\n  // 无论如何都显示红绿灯弹窗\n  window.showTrafficLightPopup(interId);\n  return true;\n};\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  var _trafficLight$interse, _trafficLight$interse2, _trafficLight$interse3;\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch (phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n\n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: (_trafficLight$interse = trafficLight.intersection) === null || _trafficLight$interse === void 0 ? void 0 : _trafficLight$interse.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n\n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = {\n    isLight: true\n  };\n\n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  console.log(`更新路口 ${((_trafficLight$interse2 = trafficLight.intersection) === null || _trafficLight$interse2 === void 0 ? void 0 : _trafficLight$interse2.name) || ((_trafficLight$interse3 = trafficLight.intersection) === null || _trafficLight$interse3 === void 0 ? void 0 : _trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "Select", "Popover", "intersectionsData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "lastPosition", "lastRotation", "ALPHA", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "vehicleModels", "Map", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "console", "log", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "CampusModel", "className", "onCurrentRSUChange", "selectedRSUs", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "updateMatrix", "updateMatrixWorld", "相机位置", "toArray", "目标点", "handleMqttMessage", "message", "payload", "JSON", "parse", "_payload$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "get", "设备MAC", "消息时间戳", "最新时间戳", "时间戳", "是否为最新", "participants", "rsuid", "now", "Date", "for<PERSON>ach", "participant", "id", "partPtcId", "type", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "model", "newModel", "height", "rotation", "add", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "Set", "map", "p", "modelData", "has", "remove", "delete", "bsmData", "bsmid", "newState", "partLong", "partLat", "newPosition", "Vector3", "vehicleObj", "newVehicleModel", "is<PERSON><PERSON>", "toFixed", "filteredPosition", "phase", "phaseId", "toString", "direction", "trafficDirec", "getDirectionFromCode", "getPhaseDirection", "lightState", "trafficLight", "remainTime", "parseInt", "trafficLightKey", "trafficLightModel", "phaseInfo", "updateTrafficLightVisual", "prev", "warn", "postMessage", "rsiData", "rsuId", "events", "rtes", "event", "eventId", "rteId", "eventType", "description", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "事件ID", "事件类型", "事件说明", "开始时间", "结束时间", "位置", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "stringify", "onerror", "onclose", "setTimeout", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "length", "xhr", "loaded", "total", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "clearInterval", "close", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "clear", "lightObj", "handleMainVehicleChange", "intervalId", "setInterval", "timer", "createTrafficLights", "clearTimeout", "handleClick", "clientX", "clientY", "handleMouseClick", "initScene", "createSimpleTrafficLight", "geometry", "BoxGeometry", "MeshBasicMaterial", "<PERSON><PERSON>", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "addClickHelpers", "helperGeometry", "SphereGeometry", "helperMaterial", "transparent", "opacity", "depthWrite", "helper<PERSON><PERSON>", "userData", "isClickHelper", "size", "debugScene", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "onChange", "options", "label", "bordered", "dropdownStyle", "maxHeight", "ref", "right", "background", "onClick", "handleClosePopover", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "sprite", "Sprite", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "vehicleGltf", "cyclistGltf", "peopleGltf", "trafficLightGltf", "all", "loadAsync", "materia", "err", "types", "文本", "颜色", "converterInstance", "createFallbackTrafficLights", "renderOrder", "side", "DoubleSide", "depthTest", "needsUpdate", "colliderGeometry", "colliderMaterial", "collider", "isCollider", "dirCode", "container", "sceneInstance", "cameraInstance", "setPopoverState", "rect", "getBoundingClientRect", "mouseX", "clientWidth", "mouseY", "clientHeight", "raycaster", "Raycaster", "params", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "trafficLightObjects", "push", "trafficLightIntersects", "intersectObjects", "intersect", "index", "object", "obj", "getTrafficLightFromObject", "showTrafficLightPopup", "intersects", "closestLight", "worldPos", "getWorldPosition", "screenPos", "project", "dx", "dy", "sqrt", "testTrafficLightClick", "_document$querySelect", "_document$querySelect2", "light", "lightModel", "stateInfo", "overflowY", "marginBottom", "borderBottom", "paddingBottom", "lightColor", "justifyContent", "max<PERSON><PERSON><PERSON>", "centerX", "centerY", "__REACT_INSTANCE", "popover", "innerHTML", "body", "closeButton", "listTrafficLights", "list", "interactiveObjects", "Object", "keys", "名称", "类型", "路口ID", "可见性", "是否是网格", "highlightGeometry", "highlightMaterial", "<PERSON><PERSON><PERSON>", "originalPosition", "红绿灯映射数量", "红绿灯状态数量", "场景中红绿灯对象数量", "射线检测启用", "from", "_light$intersection", "lightText", "marginTop", "updateTime", "toLocaleTimeString", "testTrafficLightWithMockData", "generateMockSpatData", "phaseIds", "spatMessage", "source", "_light$intersection2", "phaseCount", "floor", "random", "selectedPhaseIds", "randomIndex", "includes", "lightIndex", "stateLabels", "phaseInfos", "parent", "testClickDetection", "undefined", "tlIntersects", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "distanceToCamera", "distanceTo", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "forceClickTrafficLight", "pixelX", "pixelY", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "isLight", "lightGeometry", "lightMaterial", "emissive", "emissiveIntensity", "<PERSON><PERSON><PERSON>", "PointLight", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.3; // 滤波系数，值越小滤波效果越强（0-1之间）\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 添加位置滤波函数\nconst filterPosition = (newPos) => {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n};\n\n// 添加朝向滤波函数\nconst filterRotation = (newRotation) => {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n  return filteredRotation;\n};\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);  // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n  \n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        console.log('收到RSM消息:', payload);\n        \n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n        \n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n        \n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          console.log('忽略过期的RSM消息:', {\n            设备MAC: deviceMac,\n            消息时间戳: messageTimestamp,\n            最新时间戳: lastTimestamp\n          });\n      return;\n    }\n    \n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        \n        console.log('RSM消息时间戳更新:', {\n          设备MAC: deviceMac,\n          时间戳: messageTimestamp,\n          是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        });\n        \n        const participants = payload.data?.participants || [];\n        const rsuid = payload.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id =  rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n          // 解析位置和状态信息\n          const state = {\n            longitude: parseFloat(participant.partPosLong),\n            latitude: parseFloat(participant.partPosLat),\n            speed: parseFloat(participant.partSpeed),\n            heading: parseFloat(participant.partHeading)\n          };\n          \n          const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n          \n          // 根据类型选择对应的预加载模型\n          let preloadedModel;\n          switch (type) {\n            case '1': // 机动车\n              preloadedModel = preloadedVehicleModel;\n              break;\n            case '2': // 非机动车\n              preloadedModel = preloadedCyclistModel;\n              break;\n            case '3': // 行人\n              preloadedModel = preloadedPeopleModel;\n              break;\n            default:\n              return; // 跳过未知类型\n          }\n          \n          // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n            // 创建新模型实例\n            const newModel = preloadedModel.clone();\n            // 根据类型调整高度\n            const height = type === '3' ? 0.5 : 1.0; // 行人模型贴近地面\n            newModel.position.set(modelPos.x, height, -modelPos.y);\n            newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            scene.add(newModel);\n            \n            vehicleModels.set(id, {\n              model: newModel,\n              lastUpdate: now,\n              type: type\n            });\n          } else if (model) {\n            // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 0.5 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n          }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        console.log('收到BSM消息:', payload);\n        \n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n        \n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        const newRotation = Math.PI - newState.heading * Math.PI / 180;\n        \n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n        \n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        \n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          newVehicleModel.position.copy(newPosition);\n          newVehicleModel.rotation.y = newRotation;\n          scene.add(newVehicleModel);\n          \n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle\n          });\n          \n          console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition);\n          const filteredRotation = filterRotation(newRotation);\n          \n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n          \n          console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n        \n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 1秒\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        \n        return;\n      }\n      \n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        console.log('收到SPAT消息:', message);\n        \n        try {\n          const payload = JSON.parse(message);\n          \n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              \n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              \n              console.log(`处理路口ID: ${interId} 的SPAT消息`);\n              \n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  \n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? \n                    getDirectionFromCode(phase.trafficDirec) : \n                    getPhaseDirection(phaseId);\n                  \n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  \n                  console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n                  \n                  // 在地图上更新对应的交通灯状态\n                  const trafficLightKey = interId;\n                  const trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  \n                  if (trafficLightModel) {\n                    // 创建相位信息对象\n                    const phaseInfo = {\n                      phaseId,\n                      direction,\n                      trafficLight: lightState,\n                      remainTime\n                    };\n                    \n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n                    \n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n      }\n      \n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        console.log('收到RSI消息:', payload);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        \n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        \n        return;\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        console.log('收到场景事件消息:', payload);\n        \n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: payload.type,\n        data: payload\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          \n          // 检查scene是否初始化\n          if (scene) {\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          100,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n      \n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n      \n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach((lightObj) => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n      \n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n      \n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n    \n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n    \n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n    \n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n    \n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      \n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n  \n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          console.log('检测到点击事件', event.clientX, event.clientY);\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        } else {\n          console.warn('点击事件处理失败: scene或camera未初始化');\n        }\n      };\n      \n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n      \n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n      \n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n    \n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    \n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n    \n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(10, 8, 8);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          transparent: true,\n          opacity: 0.1,  // 几乎透明\n          depthWrite: false\n        });\n        \n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置\n        \n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n        \n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        \n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        addClickHelpers();\n      }\n    }, 5500);  // 延迟略长于debugTrafficLights\n    \n    return () => clearTimeout(timer);\n  }, []);\n\n  // 在组件加载完毕后调用调试函数\n  useEffect(() => {\n    // 延迟5秒调用调试函数，确保所有模型都已加载\n    const timer = setTimeout(() => {\n      console.log('调用场景调试函数');\n      if (window.debugScene) {\n        window.debugScene(); // 使用全局函数\n      } else {\n        console.error('debugScene函数未定义');\n      }\n    }, 5000);\n    \n    return () => clearTimeout(timer);\n  }, []);\n\n  return (\n    <>\n      <span style={labelStyle}>路口选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择路口位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      \n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '4px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n            padding: '0',\n          }}\n        >\n          {trafficLightPopover.content}\n          <button \n            style={{\n              position: 'absolute',\n              top: '2px',\n              right: '2px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '14px',\n              cursor: 'pointer',\n              padding: '4px 8px'\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n      \n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    try {\n      const [vehicleGltf, cyclistGltf, peopleGltf, trafficLightGltf] = await Promise.all([\n      loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/traffic light.glb`)\n    ]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n      \n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse((child) => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n\n    console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n      \n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n        \n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/traffic light.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在5秒后自动移除\n  setTimeout(() => {\n    scene.remove(sprite);\n  }, 500);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  console.log('添加场景事件标记:', {\n    位置: position,\n    文本: text,\n    颜色: color\n  });\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  \n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  \n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n  \n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n        \n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n        \n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n        \n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n        \n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n        \n        // *** 重要：设置为可被交互 ***\n        trafficLightModel.traverse(child => {\n          // 设置所有子对象可被点击\n          if (child.isMesh) {\n            // 确保材质能够被射线检测到\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            \n            // 设置较高的渲染顺序\n            child.renderOrder = 100;\n          }\n        });\n        \n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        \n        // 添加一个专门用于点击的大型碰撞体\n        const colliderGeometry = new THREE.SphereGeometry(15, 12, 12);\n        const colliderMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          transparent: true,\n          opacity: 0.0,  // 完全透明\n          depthWrite: false\n        });\n        \n        const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n        collider.name = `交通灯碰撞体-${intersection.name}`;\n        collider.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name,\n          isCollider: true\n        };\n        \n        trafficLightModel.add(collider);\n        \n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n        \n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        \n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n  \n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个透明的碰撞体，代替原来的黑色长方体\n  const colliderGeometry = new THREE.SphereGeometry(15, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xffffff,\n    transparent: true,\n    opacity: 0.0,  // 完全透明\n    depthWrite: false\n  });\n  \n  const trafficLightModel = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  \n  // 给模型一个名称便于调试\n  trafficLightModel.name = `交通灯碰撞体-${intersection.name}`;\n  \n  // 设置位置 - 保持相同高度以便于点击\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n  \n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n  \n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n  \n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n  \n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的交通灯碰撞体, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance) => {\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  switch(phaseId) {\n    case '1': return '北进口左转';\n    case '2': return '北进口直行';\n    case '3': return '北进口右转';\n    case '5': return '东进口左转';\n    case '6': return '东进口直行';\n    case '7': return '东进口右转';\n    case '9': return '南进口左转';\n    case '10': return '南进口直行';\n    case '11': return '南进口右转';\n    case '13': return '西进口左转';\n    case '14': return '西进口直行';\n    case '15': return '西进口右转';\n    default: return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = (dirCode) => {\n  switch(dirCode) {\n    case 'N': return '北向南';\n    case 'S': return '南向北';\n    case 'E': return '东向西';\n    case 'W': return '西向东';\n    case 'NE': return '东北向西南';\n    case 'NW': return '西北向东南';\n    case 'SE': return '东南向西北';\n    case 'SW': return '西南向东北';\n    default: return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  \n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n  \n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n  \n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 5;\n  raycaster.params.Line.threshold = 5;\n  \n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  \n  console.log('鼠标点击位置:', mouseX, mouseY);\n  \n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  \n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n  \n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  \n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, \n                 intersect.object.name || '无名称', \n                 '距离:', intersect.distance,\n                 'userData:', intersect.object.userData);\n    });\n    \n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    \n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId);\n      \n      // 显示弹窗\n      window.showTrafficLightPopup(interId);\n      return;\n    }\n  }\n  \n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  \n  console.log('射线检测到的对象数量:', intersects.length);\n  \n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', \n                  'userData:', obj.userData, \n                  '距离:', intersect.distance);\n    });\n    \n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        console.log('检测到红绿灯点击, 路口ID:', interId);\n        \n        // 显示弹窗\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n  \n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n  \n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.3; // 设置一个阈值，只有距离小于此值的才会被选中\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n      \n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n      \n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      \n      console.log(`路口 ${interId} 的距离:`, distance);\n      \n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = { interId, distance };\n      }\n    }\n  });\n  \n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n    \n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  \n  console.log('未检测到任何红绿灯点击');\n  \n  // 如果用户点击了其他任何地方，关闭现有的弹窗\n  if (setPopoverState) {\n    setPopoverState(prev => {\n      if (prev.visible) {\n        return { ...prev, visible: false };\n      }\n      return prev;\n    });\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  setPopoverState(prev => ({ ...prev, visible: false }));\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = (interId) => {\n  try {\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      \n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      \n      return false;\n    }\n    \n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n    \n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 创建弹出窗口内容\n    let content;\n    \n    if (stateInfo && stateInfo.phases) {\n      content = (\n        <div style={{ padding: '10px', width: '300px', maxHeight: '400px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '10px',\n            fontSize: '16px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '5px'\n          }}>\n            {intersection.name} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              switch (phase.trafficLight) {\n                case 'G': lightColor = '#00ff00'; break;\n                case 'Y': lightColor = '#ffff00'; break;\n                case 'R': default: lightColor = '#ff0000'; break;\n              }\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '8px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '5px',\n                  borderRadius: '4px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {getPhaseDirection(phase.phaseId)}\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 5px',\n                      borderRadius: '2px'\n                    }}>\n                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      );\n    } else {\n      content = (\n        <div style={{ padding: '8px', maxWidth: '200px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>\n          <div>路口ID: {interId}</div>\n          <div>当前无信号灯状态信息</div>\n        </div>\n      );\n    }\n    \n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2;\n    const centerY = window.innerHeight / 2;\n    \n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;\n    \n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: { x: centerX, y: centerY },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '10px';\n      popover.style.maxWidth = '300px';\n      \n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 10px; font-size: 16px; border-bottom: 1px solid #eee; padding-bottom: 5px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 2px; right: 2px; background: none; border: none; color: white; font-size: 14px; cursor: pointer; padding: 4px 8px;\">×</button>\n      `;\n      \n      document.body.appendChild(popover);\n      \n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      \n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  \n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  \n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  \n  return list;\n};\n\n// 添加全局调试方法\nwindow.debugScene = function() {\n  if (!scene) {\n    console.error('场景未初始化，无法调试红绿灯');\n    return;\n  }\n\n  console.log('开始调试红绿灯模型...');\n  \n  // 检查红绿灯映射\n  console.log('红绿灯映射数量:', trafficLightsMap.size);\n  \n  // 统计场景中的可互动对象\n  let interactiveObjects = 0;\n  let trafficLightObjects = 0;\n  \n  // 遍历场景中的所有对象\n  scene.traverse((object) => {\n    // 检查是否有userData\n    if (object.userData && Object.keys(object.userData).length > 0) {\n      interactiveObjects++;\n      \n      // 检查是否是红绿灯\n      if (object.userData.type === 'trafficLight') {\n        trafficLightObjects++;\n        console.log('找到红绿灯对象:', {\n          名称: object.name || '无名称',\n          类型: object.userData.type,\n          路口ID: object.userData.interId,\n          位置: object.position.toArray(),\n          可见性: object.visible,\n          是否是网格: object.isMesh,\n          userData: object.userData\n        });\n      }\n    }\n  });\n  \n  console.log('场景中的可互动对象数量:', interactiveObjects);\n  console.log('红绿灯对象数量:', trafficLightObjects);\n  \n  // 遍历红绿灯映射，创建高亮标记\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 获取红绿灯位置\n      const position = lightObj.model.position.clone();\n      \n      // 创建一个高亮球体\n      const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n      const highlightMaterial = new THREE.MeshBasicMaterial({ \n        color: 0xff00ff, \n        transparent: true,\n        opacity: 0.7\n      });\n      const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n      \n      // 设置位置，稍微偏移，避免遮挡\n      highlightMesh.position.set(position.x, position.y + 15, position.z);\n      \n      // 添加到场景\n      scene.add(highlightMesh);\n      \n      // 添加用户数据，方便调试\n      highlightMesh.userData = {\n        type: 'trafficLightHighlight',\n        interId: interId,\n        name: lightObj.intersection.name,\n        originalPosition: position.toArray()\n      };\n      \n      // 5秒后自动移除高亮标记\n      setTimeout(() => {\n        scene.remove(highlightMesh);\n      }, 5000);\n      \n      console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n    }\n  });\n  \n  // 将调试信息显示在控制台\n  console.log('红绿灯调试信息:', {\n    红绿灯映射数量: trafficLightsMap.size,\n    红绿灯状态数量: trafficLightStates.size,\n    场景中红绿灯对象数量: trafficLightObjects,\n    射线检测启用: true\n  });\n};\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = (interId) => {\n  try {\n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    console.log('当前trafficLightStates大小:', trafficLightStates.size);\n    \n    // 如果没有指定ID，则使用第一个红绿灯\n    if (!interId && trafficLightsMap.size > 0) {\n      interId = Array.from(trafficLightsMap.keys())[0];\n      console.log('未指定ID，使用第一个红绿灯ID:', interId);\n    }\n    \n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      \n      // 输出所有红绿灯ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection?.name || '未知'}`);\n      });\n      \n      return false;\n    }\n    \n    // 查找红绿灯状态信息\n    const stateInfo = trafficLightStates.get(interId);\n    console.log(`路口ID ${interId} 的状态信息:`, stateInfo);\n    \n    const intersection = trafficLight.intersection;\n    console.log('路口信息:', intersection);\n    \n    // 创建弹窗内容\n    let content;\n    \n    if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n      // 添加一个检查相位数据的打印\n      console.log('相位数据详情:');\n      stateInfo.phases.forEach((phase, index) => {\n        console.log(`相位 ${index+1}: ID=${phase.phaseId}, 状态=${phase.trafficLight}, 剩余时间=${phase.remainTime}`);\n      });\n      \n      content = (\n        <div style={{ padding: '10px', width: '300px', maxHeight: '400px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '10px',\n            fontSize: '16px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '5px'\n          }}>\n            {intersection?.name || '未知路口'} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              let lightText;\n              \n              switch (phase.trafficLight) {\n                case 'G':\n                  lightColor = '#00ff00';\n                  lightText = '绿灯';\n                  break;\n                case 'Y':\n                  lightColor = '#ffff00';\n                  lightText = '黄灯';\n                  break;\n                case 'R':\n                default:\n                  lightColor = '#ff0000';\n                  lightText = '红灯';\n                  break;\n              }\n              \n              const direction = getPhaseDirection(phase.phaseId);\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '8px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '5px',\n                  borderRadius: '4px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {direction} (相位ID: {phase.phaseId})\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 5px',\n                      borderRadius: '2px'\n                    }}>\n                      {lightText}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '10px', fontSize: '12px', color: '#888' }}>\n            更新时间: {new Date(stateInfo.updateTime).toLocaleTimeString()}\n          </div>\n        </div>\n      );\n    } else {\n      // 如果没有状态信息，创建一个示例内容\n      content = (\n        <div style={{ padding: '10px', width: '300px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '10px' }}>\n            {intersection?.name || '未知路口'} (ID: {interId})\n          </div>\n          <div style={{ color: '#ff4d4f' }}>\n            该路口暂无红绿灯状态信息，请等待SPAT消息更新。\n          </div>\n          <div style={{ marginTop: '10px' }}>\n            <button \n              onClick={() => window.testTrafficLightWithMockData(interId)} \n              style={{\n                background: '#1890ff',\n                color: 'white',\n                border: 'none',\n                padding: '5px 10px',\n                borderRadius: '2px',\n                cursor: 'pointer'\n              }}\n            >\n              生成模拟数据\n            </button>\n          </div>\n          <div style={{ marginTop: '10px', fontSize: '12px', color: '#888' }}>\n            当前时间: {new Date().toLocaleTimeString()}\n          </div>\n        </div>\n      );\n    }\n    \n    // 设置弹窗位置在屏幕中央\n    const x = window.innerWidth / 2;\n    const y = window.innerHeight / 2;\n    \n    // 更新弹窗状态\n    if (window._setTrafficLightPopover) {\n      console.log('调用_setTrafficLightPopover更新弹窗状态', {\n        visible: true,\n        interId,\n        position: { x, y }\n      });\n      \n      window._setTrafficLightPopover({\n        visible: true,\n        interId: interId,\n        position: { x, y },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      console.log(`已显示路口 ${intersection?.name || interId} 的红绿灯状态弹窗`);\n      return true;\n    } else {\n      console.error('无法找到setTrafficLightPopover函数');\n      return false;\n    }\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// 添加全局模拟SPAT数据生成函数\nwindow.generateMockSpatData = () => {\n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.error('红绿灯映射为空，无法生成模拟数据');\n    return false;\n  }\n  \n  console.log('开始生成模拟SPAT数据...');\n  \n  // 可能的相位ID和对应方向\n  const phaseIds = [\n    '1', '2', '3',  // 北进口\n    '5', '6', '7',  // 东进口 \n    '9', '10', '11', // 南进口\n    '13', '14', '15' // 西进口\n  ];\n  \n  // 准备SPAT数据结构\n  const spatMessage = {\n    data: {\n      intersections: []\n    },\n    tm: Date.now(),\n    source: 2,\n    type: 'SPAT',\n    mac: 'mock-device-id'\n  };\n  \n  // 为每个红绿灯生成模拟数据\n  trafficLightsMap.forEach((light, interId) => {\n    // 为该路口生成3-6个随机相位\n    const phaseCount = Math.floor(Math.random() * 4) + 3;\n    const phases = [];\n    \n    // 随机选择相位ID\n    const selectedPhaseIds = [];\n    while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n      const randomIndex = Math.floor(Math.random() * phaseIds.length);\n      const phaseId = phaseIds[randomIndex];\n      \n      // 避免重复选择同一个ID\n      if (!selectedPhaseIds.includes(phaseId)) {\n        selectedPhaseIds.push(phaseId);\n      }\n    }\n    \n    // 为每个选中的相位生成随机状态\n    selectedPhaseIds.forEach(phaseId => {\n      // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n      const lightIndex = Math.floor(Math.random() * 3);\n      const stateLabels = ['red', 'yellow', 'green'];\n      \n      // 随机生成剩余时间 (1-60秒)\n      const remainTime = Math.floor(Math.random() * 60) + 1;\n      \n      // 添加相位信息 - 符合实际数据结构\n      phases.push({\n        phaseId: phaseId,\n        state: stateLabels[lightIndex],\n        remainTime: remainTime\n      });\n    });\n    \n    // 添加交叉口数据到SPAT消息\n    spatMessage.data.intersections.push({\n      id: interId,\n      phases: phases\n    });\n    \n    // 同时更新本地状态方便测试\n    const phaseInfos = phases.map(phase => ({\n      phaseId: phase.phaseId,\n      trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n      remainTime: phase.remainTime,\n      lastUpdate: Date.now()\n    }));\n    \n    trafficLightStates.set(interId, {\n      updateTime: Date.now(),\n      phases: phaseInfos\n    });\n    \n    console.log(`为路口 ${light.intersection?.name || interId} 生成了 ${phases.length} 个相位的模拟数据`);\n  });\n  \n  // 模拟调用SPAT消息处理函数\n  try {\n    console.log('处理模拟SPAT消息:', spatMessage);\n    const message = JSON.stringify(spatMessage);\n    // 间接通过handleMqttMessage处理模拟数据\n    handleMqttMessage(MQTT_CONFIG.spat, message);\n  } catch (error) {\n    console.error('处理模拟SPAT消息失败:', error);\n  }\n  \n  console.log('模拟SPAT数据生成完成');\n  return true;\n};\n\n// 添加全局函数：生成模拟数据并显示红绿灯弹窗\nwindow.testTrafficLightWithMockData = (interId) => {\n  // 先生成模拟数据\n  window.generateMockSpatData();\n  \n  // 延迟100ms确保数据已生成\n  setTimeout(() => {\n    // 显示红绿灯弹窗\n    window.showTrafficLightPopup(interId);\n  }, 100);\n  \n  return '模拟数据已生成，正在显示红绿灯弹窗...';\n};\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n  \n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n  \n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  \n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n    \n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n    \n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n    \n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n    \n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    \n    console.log('归一化坐标:', mouseX, mouseY);\n    \n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    \n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n    \n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n    \n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    \n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', \n                    '距离:', intersect.distance,\n                    'position:', intersect.object.position.toArray(),\n                    'userData:', intersect.object.userData);\n        \n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      \n      return true;\n    }\n    \n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    \n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', \n                  '类型:', obj.type,\n                  '位置:', obj.position.toArray(),\n                  '距离:', intersect.distance,\n                  'userData:', obj.userData);\n    });\n    \n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    \n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n        \n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n        \n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n        \n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        \n        if (isVisible) {\n          visibleCount++;\n        }\n        \n        console.log(`红绿灯 ${interId}:`, {\n          名称: lightObj.intersection?.name || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    \n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n    \n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n// 添加全局强制点击红绿灯的函数\nwindow.forceClickTrafficLight = (interId) => {\n  if (!interId && trafficLightsMap.size > 0) {\n    // 使用第一个红绿灯\n    interId = Array.from(trafficLightsMap.keys())[0];\n  }\n  \n  if (!interId) {\n    console.error('找不到红绿灯ID');\n    return false;\n  }\n  \n  console.log(`强制点击红绿灯 ${interId}`);\n  \n  const lightObj = trafficLightsMap.get(interId);\n  if (!lightObj || !lightObj.model) {\n    console.error(`找不到ID为 ${interId} 的红绿灯模型`);\n    return false;\n  }\n  \n  // 确保模型可见\n  lightObj.model.visible = true;\n  lightObj.model.traverse(child => {\n    child.visible = true;\n  });\n  \n  // 获取红绿灯的世界位置\n  const worldPos = new THREE.Vector3();\n  lightObj.model.getWorldPosition(worldPos);\n  \n  // 将世界位置投影到屏幕\n  const screenPos = worldPos.clone().project(cameraRef.current);\n  \n  // 计算屏幕像素坐标\n  const canvas = document.querySelector('canvas');\n  if (canvas) {\n    const rect = canvas.getBoundingClientRect();\n    const pixelX = ((screenPos.x + 1) / 2) * canvas.clientWidth + rect.left;\n    const pixelY = ((-screenPos.y + 1) / 2) * canvas.clientHeight + rect.top;\n    \n    console.log('红绿灯屏幕坐标:', pixelX, pixelY);\n    \n    // 测试在该位置的点击\n    window.testClickDetection(pixelX, pixelY);\n  }\n  \n  // 无论如何都显示红绿灯弹窗\n  window.showTrafficLightPopup(interId);\n  return true;\n};\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  \n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch(phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n  \n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ \n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: trafficLight.intersection?.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n  \n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = { isLight: true };\n  \n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  \n  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACtC,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,0BAA0B,GAAG,IAAI,CAAC,CAAC;AACvC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,GAAG,CAAC,CAAC;;AAEnB;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACV;EACEC,GAAG,EAAE,2BAA2B;EAChCC,GAAG,EAAE,2BAA2B;EAChCX,KAAK,EAAE,6BAA6B;EACtCY,GAAG,EAAE,2BAA2B;EAAG;EACnCC,IAAI,EAAE,4BAA4B,CAAE;AACtC,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,IAAIC,gBAAgB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;;AAElC;AACA,IAAIE,gBAAgB,GAAG,IAAI;;AAE3B;AACA,IAAIC,gBAAgB,GAAG,IAAIH,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,IAAII,kBAAkB,GAAG,IAAIJ,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEpC;AACA,MAAMK,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGT,QAAQ,oBAAoB,CAAC;IAC7D,MAAMU,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,IAAIA,IAAI,CAACE,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;MACzD,MAAMG,WAAW,GAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,IAAI,CAAC;MACrE,IAAIH,WAAW,IAAIA,WAAW,CAACI,KAAK,EAAE;QACpCf,gBAAgB,GAAGW,WAAW,CAACI,KAAK;QACpCC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEjB,gBAAgB,CAAC;QAC7C,OAAOA,gBAAgB;MACzB;IACF;IACAgB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChCjB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjClB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB;AACF,CAAC;;AAED;AACA,MAAMmB,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAIC,MAAM,IAAK;EACjC,IAAI,CAACzC,YAAY,EAAE;IACjBA,YAAY,GAAGyC,MAAM,CAACC,KAAK,CAAC,CAAC;IAC7B,OAAOD,MAAM;EACf;EAEA,MAAME,SAAS,GAAGP,aAAa,CAACK,MAAM,CAACG,CAAC,EAAE5C,YAAY,CAAC4C,CAAC,EAAE1C,KAAK,CAAC;EAChE,MAAM2C,SAAS,GAAGT,aAAa,CAACK,MAAM,CAACK,CAAC,EAAE9C,YAAY,CAAC8C,CAAC,EAAE5C,KAAK,CAAC;EAChE,MAAM6C,SAAS,GAAGX,aAAa,CAACK,MAAM,CAACO,CAAC,EAAEhD,YAAY,CAACgD,CAAC,EAAE9C,KAAK,CAAC;EAEhEF,YAAY,CAACiD,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACjD,OAAO/C,YAAY,CAAC0C,KAAK,CAAC,CAAC;AAC7B,CAAC;;AAED;AACA,MAAMQ,cAAc,GAAIC,WAAW,IAAK;EACtC,IAAIlD,YAAY,KAAK,IAAI,EAAE;IACzBA,YAAY,GAAGkD,WAAW;IAC1B,OAAOA,WAAW;EACpB;;EAEA;EACA,IAAIC,IAAI,GAAGD,WAAW,GAAGlD,YAAY;EACrC,IAAImD,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EAExC,MAAMC,gBAAgB,GAAGnB,aAAa,CAACnC,YAAY,GAAGmD,IAAI,EAAEnD,YAAY,EAAEC,KAAK,CAAC;EAChFD,YAAY,GAAGsD,gBAAgB;EAC/B,OAAOA,gBAAgB;AACzB,CAAC;AAED,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC,kBAAkB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAMC,YAAY,GAAG3F,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM4F,UAAU,GAAG5F,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM6F,SAAS,GAAG7F,MAAM,CAAC,IAAIM,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMwF,aAAa,GAAG9F,MAAM,CAAC,EAAE,CAAC;EAChC,MAAM+F,eAAe,GAAG/F,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMgG,aAAa,GAAGhG,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMiG,iBAAiB,GAAGjG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAE;EACzC,MAAM,CAACkG,eAAe,EAAEC,kBAAkB,CAAC,GAAGlG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAACmG,YAAY,EAAEC,eAAe,CAAC,GAAGpG,QAAQ,CAAC;IAC/CqG,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1G,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAM2G,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,IAAI;IAAG;IACfC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG7H,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAAC8H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9H,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAAC+H,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhI,QAAQ,CAAC;IAC7DiI,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,IAAI;IACbtB,QAAQ,EAAE;MAAEnC,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxBwD,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACAlG,MAAM,CAACmG,uBAAuB,GAAGL,sBAAsB;;EAEvD;EACA,MAAMM,uBAAuB,GAAG;IAC9B1B,QAAQ,EAAE,OAAO;IACjB2B,GAAG,EAAE,MAAM;IACXzB,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7ByB,KAAK,EAAE,OAAO;IAAG;IACjBxB,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMe,UAAU,GAAG;IACjB7B,QAAQ,EAAE,OAAO;IACjB2B,GAAG,EAAE,MAAM;IACXzB,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBsB,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACblB,QAAQ,EAAE,MAAM;IAChBmB,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvC7B,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM,CAACjE,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,IAAI4C,GAAG,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACkG,UAAU,EAAEC,aAAa,CAAC,GAAG/I,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAAC6C,gBAAgB,EAAEmG,mBAAmB,CAAC,GAAGhJ,QAAQ,CAAC,IAAI4C,GAAG,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACqG,WAAW,EAAEC,cAAc,CAAC,GAAGlJ,QAAQ,CAAC;IAAEmJ,KAAK,EAAE,EAAE;IAAEhB,OAAO,EAAE;EAAG,CAAC,CAAC;;EAE1E;EACA,MAAMiB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B1C,WAAW,CAAC,QAAQ,CAAC;IACrBpF,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAAC8H,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B5C,WAAW,CAAC,QAAQ,CAAC;IACrBpF,UAAU,GAAG,QAAQ;IAErB,IAAIsG,SAAS,CAAC2B,OAAO,IAAIhI,QAAQ,EAAE;MACjC;MACA,MAAMiI,UAAU,GAAG5B,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACrC,KAAK,CAAC,CAAC;MACrD,MAAMkF,SAAS,GAAG7B,SAAS,CAAC2B,OAAO,CAACG,EAAE,CAACnF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAIjE,KAAK,CAACqJ,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC;QAAEnF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,GAAG;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCgF,MAAM,CAACvJ,KAAK,CAACwJ,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdrC,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACsD,IAAI,CAACV,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;MAEV;MACA,IAAI7J,KAAK,CAACqJ,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEnF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BgF,MAAM,CAACvJ,KAAK,CAACwJ,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdrC,SAAS,CAAC2B,OAAO,CAACG,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC;MACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAG7I,QAAQ,CAAC8I,MAAM,CAAC9F,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAIjE,KAAK,CAACqJ,KAAK,CAACS,aAAa,CAAC,CAC3BR,EAAE,CAAC;QAAEnF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BgF,MAAM,CAACvJ,KAAK,CAACwJ,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACd1I,QAAQ,CAAC8I,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACAxC,SAAS,CAAC2B,OAAO,CAACe,MAAM,CAAC/I,QAAQ,CAAC8I,MAAM,CAAC;QACzC9I,QAAQ,CAACgJ,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACA5I,QAAQ,CAAC8H,OAAO,GAAG,IAAI;;MAEvB;MACA9H,QAAQ,CAACiJ,WAAW,GAAG,EAAE;MACzBjJ,QAAQ,CAACkJ,WAAW,GAAG,GAAG;MAC1BlJ,QAAQ,CAACmJ,aAAa,GAAGxF,IAAI,CAACC,EAAE,GAAG,GAAG;MACtC5D,QAAQ,CAACoJ,aAAa,GAAG,CAAC;MAC1BpJ,QAAQ,CAACgJ,MAAM,CAAC,CAAC;MAEjBzG,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrB6G,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAGvK,iBAAiB,CAACwK,aAAa,CAACxH,IAAI,CAACyH,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKJ,KAAK,CAAC;IAChF,IAAIC,YAAY,IAAIrD,SAAS,CAAC2B,OAAO,IAAIhI,QAAQ,EAAE;MACjDuG,uBAAuB,CAACmD,YAAY,CAAC;;MAErC;MACA,MAAMI,WAAW,GAAGzF,SAAS,CAAC2D,OAAO,CAAC+B,YAAY,CAChDC,UAAU,CAACN,YAAY,CAAC5E,SAAS,CAAC,EAClCkF,UAAU,CAACN,YAAY,CAAC3E,QAAQ,CAClC,CAAC;MAEDxC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvByH,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBK,GAAG,EAAE;UACHpF,SAAS,EAAE4E,YAAY,CAAC5E,SAAS;UACjCC,QAAQ,EAAE2E,YAAY,CAAC3E;QACzB,CAAC;QACDoF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACA/J,UAAU,GAAG,cAAc;MAC3BoF,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAkB,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAC9B,GAAG,CAACuG,WAAW,CAAC5G,CAAC,EAAE,GAAG,EAAE,CAAC4G,WAAW,CAAC1G,CAAC,CAAC;;MAElE;MACApD,QAAQ,CAAC8I,MAAM,CAACvF,GAAG,CAACuG,WAAW,CAAC5G,CAAC,EAAE,CAAC,EAAE,CAAC4G,WAAW,CAAC1G,CAAC,CAAC;;MAErD;MACAiD,SAAS,CAAC2B,OAAO,CAACe,MAAM,CAAC/I,QAAQ,CAAC8I,MAAM,CAAC;;MAEzC;MACA9I,QAAQ,CAAC8H,OAAO,GAAG,IAAI;MACvB9H,QAAQ,CAACgJ,MAAM,CAAC,CAAC;;MAEjB;MACA3C,SAAS,CAAC2B,OAAO,CAACoC,YAAY,CAAC,CAAC;MAChC/D,SAAS,CAAC2B,OAAO,CAACqC,iBAAiB,CAAC,IAAI,CAAC;MAEzC9H,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzByH,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBS,IAAI,EAAEjE,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACkF,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAExK,QAAQ,CAAC8I,MAAM,CAACyB,OAAO,CAAC,CAAC;QAC9BJ,IAAI,EAAEL;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMW,iBAAiB,GAAGA,CAAC7C,KAAK,EAAE8C,OAAO,KAAK;IAC5C,IAAI;MACF,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;MAEnC;MACA,IAAI9C,KAAK,KAAKnH,WAAW,CAACO,GAAG,EAAE;QAAA,IAAA8J,aAAA;QAC7BvI,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEmI,OAAO,CAAC;;QAEhC;QACA,MAAMI,SAAS,GAAGJ,OAAO,CAACK,GAAG;QAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;QAEnC;QACA,MAAMC,aAAa,GAAG7J,gBAAgB,CAAC8J,GAAG,CAACL,SAAS,CAAC;;QAErD;QACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;UACrD5I,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;YACzB6I,KAAK,EAAEN,SAAS;YAChBO,KAAK,EAAEL,gBAAgB;YACvBM,KAAK,EAAEJ;UACT,CAAC,CAAC;UACN;QACF;;QAEI;QACA7J,gBAAgB,CAACiC,GAAG,CAACwH,SAAS,EAAEE,gBAAgB,CAAC;QAEjD1I,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;UACzB6I,KAAK,EAAEN,SAAS;UAChBS,GAAG,EAAEP,gBAAgB;UACrBQ,KAAK,EAAE,CAACN,aAAa,IAAIF,gBAAgB,IAAIE;QAC/C,CAAC,CAAC;QAEF,MAAMO,YAAY,GAAG,EAAAZ,aAAA,GAAAH,OAAO,CAAC9I,IAAI,cAAAiJ,aAAA,uBAAZA,aAAA,CAAcY,YAAY,KAAI,EAAE;QACrD,MAAMC,KAAK,GAAGhB,OAAO,CAAC9I,IAAI,CAAC8J,KAAK;;QAEhC;QACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;QAEtB;QACAF,YAAY,CAACI,OAAO,CAACC,WAAW,IAAI;UAClC;UACA,MAAMC,EAAE,GAAIL,KAAK,GAAGI,WAAW,CAACE,SAAS;UACzC,MAAMC,IAAI,GAAGH,WAAW,CAACI,WAAW;UAEpC,IAAGD,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACA;YACA,MAAME,KAAK,GAAG;cACZtH,SAAS,EAAEkF,UAAU,CAAC+B,WAAW,CAACM,WAAW,CAAC;cAC9CtH,QAAQ,EAAEiF,UAAU,CAAC+B,WAAW,CAACO,UAAU,CAAC;cAC5CtH,KAAK,EAAEgF,UAAU,CAAC+B,WAAW,CAACQ,SAAS,CAAC;cACxCtH,OAAO,EAAE+E,UAAU,CAAC+B,WAAW,CAACS,WAAW;YAC7C,CAAC;YAED,MAAMC,QAAQ,GAAGpI,SAAS,CAAC2D,OAAO,CAAC+B,YAAY,CAACqC,KAAK,CAACtH,SAAS,EAAEsH,KAAK,CAACrH,QAAQ,CAAC;;YAEhF;YACA,IAAI2H,cAAc;YAClB,QAAQR,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRQ,cAAc,GAAGzM,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRyM,cAAc,GAAGxM,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRwM,cAAc,GAAGvM,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACA,IAAIwM,KAAK,GAAGvL,aAAa,CAACgK,GAAG,CAACY,EAAE,CAAC;YAEjC,IAAI,CAACW,KAAK,IAAID,cAAc,EAAE;cAC5B;cACA,MAAME,QAAQ,GAAGF,cAAc,CAAC1J,KAAK,CAAC,CAAC;cACvC;cACA,MAAM6J,MAAM,GAAGX,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;cACzCU,QAAQ,CAACvH,QAAQ,CAAC9B,GAAG,CAACkJ,QAAQ,CAACvJ,CAAC,EAAE2J,MAAM,EAAE,CAACJ,QAAQ,CAACrJ,CAAC,CAAC;cACtDwJ,QAAQ,CAACE,QAAQ,CAAC1J,CAAC,GAAGO,IAAI,CAACC,EAAE,GAAGwI,KAAK,CAACnH,OAAO,GAAGtB,IAAI,CAACC,EAAE,GAAG,GAAG;cAC7DvD,KAAK,CAAC0M,GAAG,CAACH,QAAQ,CAAC;cAEnBxL,aAAa,CAACmC,GAAG,CAACyI,EAAE,EAAE;gBACpBW,KAAK,EAAEC,QAAQ;gBACfI,UAAU,EAAEpB,GAAG;gBACfM,IAAI,EAAEA;cACR,CAAC,CAAC;YACJ,CAAC,MAAM,IAAIS,KAAK,EAAE;cAChB;cACAA,KAAK,CAACA,KAAK,CAACtH,QAAQ,CAAC9B,GAAG,CAACkJ,QAAQ,CAACvJ,CAAC,EAAEyJ,KAAK,CAACT,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAACO,QAAQ,CAACrJ,CAAC,CAAC;cACjFuJ,KAAK,CAACA,KAAK,CAACG,QAAQ,CAAC1J,CAAC,GAAGO,IAAI,CAACC,EAAE,GAAGwI,KAAK,CAACnH,OAAO,GAAGtB,IAAI,CAACC,EAAE,GAAG,GAAG;cAChE+I,KAAK,CAACK,UAAU,GAAGpB,GAAG;cACtBe,KAAK,CAACA,KAAK,CAACvC,YAAY,CAAC,CAAC;cAC1BuC,KAAK,CAACA,KAAK,CAACtC,iBAAiB,CAAC,IAAI,CAAC;YACrC;UACA;QACF,CAAC,CAAC;;QAEF;QACA,MAAM4C,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAACzB,YAAY,CAAC0B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACpB,SAAS,CAAC,CAAC;QAE9D7K,aAAa,CAAC0K,OAAO,CAAC,CAACwB,SAAS,EAAEtB,EAAE,KAAK;UACvC,IAAIJ,GAAG,GAAG0B,SAAS,CAACN,UAAU,GAAGC,iBAAiB,IAAI,CAACC,UAAU,CAACK,GAAG,CAACvB,EAAE,CAAC,EAAE;YACzE3L,KAAK,CAACmN,MAAM,CAACF,SAAS,CAACX,KAAK,CAAC;YAC7BvL,aAAa,CAACqM,MAAM,CAACzB,EAAE,CAAC;YACxBzJ,OAAO,CAACC,GAAG,CAAC,oBAAoBwJ,EAAE,QAAQsB,SAAS,CAACpB,IAAI,EAAE,CAAC;UAC7D;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAItE,KAAK,KAAKnH,WAAW,CAACM,GAAG,EAAE;QAC7BwB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEmI,OAAO,CAAC;QAEhC,MAAM+C,OAAO,GAAG/C,OAAO,CAAC9I,IAAI;QAC5B,MAAM8L,KAAK,GAAGD,OAAO,CAACpL,KAAK;QAC3B,MAAMsL,QAAQ,GAAG;UACf9I,SAAS,EAAEkF,UAAU,CAAC0D,OAAO,CAACG,QAAQ,CAAC;UACvC9I,QAAQ,EAAEiF,UAAU,CAAC0D,OAAO,CAACI,OAAO,CAAC;UACrC9I,KAAK,EAAEgF,UAAU,CAAC0D,OAAO,CAACnB,SAAS,CAAC;UACpCtH,OAAO,EAAE+E,UAAU,CAAC0D,OAAO,CAAClB,WAAW;QACzC,CAAC;QAEDjK,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEoL,QAAQ,CAAC;QAClCrL,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEmL,KAAK,CAAC;;QAE3B;QACA,MAAMlB,QAAQ,GAAGpI,SAAS,CAAC2D,OAAO,CAAC+B,YAAY,CAAC6D,QAAQ,CAAC9I,SAAS,EAAE8I,QAAQ,CAAC7I,QAAQ,CAAC;QACtF,MAAMgJ,WAAW,GAAG,IAAIpP,KAAK,CAACqP,OAAO,CAACvB,QAAQ,CAACvJ,CAAC,EAAE,GAAG,EAAE,CAACuJ,QAAQ,CAACrJ,CAAC,CAAC;QACnE,MAAMK,WAAW,GAAGE,IAAI,CAACC,EAAE,GAAGgK,QAAQ,CAAC3I,OAAO,GAAGtB,IAAI,CAACC,EAAE,GAAG,GAAG;;QAE9D;QACA,IAAIqK,UAAU,GAAG7M,aAAa,CAACgK,GAAG,CAACuC,KAAK,CAAC;;QAEzC;QACA,MAAMtL,aAAa,GAAGsL,KAAK,KAAKpM,gBAAgB;QAEhD,IAAI,CAAC0M,UAAU,IAAIhO,qBAAqB,EAAE;UACxC;UACA,MAAMiO,eAAe,GAAGjO,qBAAqB,CAAC+C,KAAK,CAAC,CAAC;UACrDkL,eAAe,CAAC7I,QAAQ,CAACsD,IAAI,CAACoF,WAAW,CAAC;UAC1CG,eAAe,CAACpB,QAAQ,CAAC1J,CAAC,GAAGK,WAAW;UACxCpD,KAAK,CAAC0M,GAAG,CAACmB,eAAe,CAAC;;UAE1B;UACA9M,aAAa,CAACmC,GAAG,CAACoK,KAAK,EAAE;YACvBhB,KAAK,EAAEuB,eAAe;YACtBlB,UAAU,EAAEnB,IAAI,CAACD,GAAG,CAAC,CAAC;YACtBM,IAAI,EAAE,GAAG;YAAE;YACXiC,MAAM,EAAE9L;UACV,CAAC,CAAC;UAEFE,OAAO,CAACC,GAAG,CAAC,aAAamL,KAAK,SAASI,WAAW,CAAC7K,CAAC,CAACkL,OAAO,CAAC,CAAC,CAAC,KAAKL,WAAW,CAAC3K,CAAC,CAACgL,OAAO,CAAC,CAAC,CAAC,KAAKL,WAAW,CAACzK,CAAC,CAAC8K,OAAO,CAAC,CAAC,CAAC,YAAY/L,aAAa,EAAE,CAAC;;UAErJ;UACA,IAAIA,aAAa,EAAE;YACjB7C,gBAAgB,GAAG0O,eAAe;YAClCrJ,eAAe,CAAC+I,QAAQ,CAAC;YACzBrL,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEmL,KAAK,CAAC;UACjC;QACF,CAAC,MAAM,IAAIM,UAAU,EAAE;UACrB;UACA,MAAMI,gBAAgB,GAAGvL,cAAc,CAACiL,WAAW,CAAC;UACpD,MAAMlK,gBAAgB,GAAGL,cAAc,CAACC,WAAW,CAAC;;UAEpD;UACAwK,UAAU,CAACtB,KAAK,CAACtH,QAAQ,CAACsD,IAAI,CAAC0F,gBAAgB,CAAC;UAChDJ,UAAU,CAACtB,KAAK,CAACG,QAAQ,CAAC1J,CAAC,GAAGS,gBAAgB;UAC9CoK,UAAU,CAACtB,KAAK,CAACvC,YAAY,CAAC,CAAC;UAC/B6D,UAAU,CAACtB,KAAK,CAACtC,iBAAiB,CAAC,IAAI,CAAC;UACxC4D,UAAU,CAACjB,UAAU,GAAGnB,IAAI,CAACD,GAAG,CAAC,CAAC;UAClCqC,UAAU,CAACE,MAAM,GAAG9L,aAAa,CAAC,CAAC;;UAEnCE,OAAO,CAACC,GAAG,CAAC,cAAcmL,KAAK,SAASU,gBAAgB,CAACnL,CAAC,CAACkL,OAAO,CAAC,CAAC,CAAC,KAAKC,gBAAgB,CAACjL,CAAC,CAACgL,OAAO,CAAC,CAAC,CAAC,KAAKC,gBAAgB,CAAC/K,CAAC,CAAC8K,OAAO,CAAC,CAAC,CAAC,YAAY/L,aAAa,EAAE,CAAC;;UAErK;UACA,IAAIA,aAAa,EAAE;YACjB7C,gBAAgB,GAAGyO,UAAU,CAACtB,KAAK;YACnC9H,eAAe,CAAC+I,QAAQ,CAAC;UAC3B;QACF;;QAEA;QACA,MAAMhC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;QACtB,MAAMqB,iBAAiB,GAAG,IAAI,CAAC,CAAC;;QAEhC7L,aAAa,CAAC0K,OAAO,CAAC,CAACwB,SAAS,EAAEtB,EAAE,KAAK;UACvC,IAAIJ,GAAG,GAAG0B,SAAS,CAACN,UAAU,GAAGC,iBAAiB,EAAE;YAClD5M,KAAK,CAACmN,MAAM,CAACF,SAAS,CAACX,KAAK,CAAC;YAC7BvL,aAAa,CAACqM,MAAM,CAACzB,EAAE,CAAC;YACxBzJ,OAAO,CAACC,GAAG,CAAC,mBAAmBwJ,EAAE,EAAE,CAAC;UACtC;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAIpE,KAAK,KAAKnH,WAAW,CAACS,IAAI,EAAE;QAC9BqB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEkI,OAAO,CAAC;QAEjC,IAAI;UACF,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;UAEnC;UACA,IAAIC,OAAO,CAAC9I,IAAI,IAAI8I,OAAO,CAAC9I,IAAI,CAAC8H,aAAa,IAAI3H,KAAK,CAACC,OAAO,CAAC0I,OAAO,CAAC9I,IAAI,CAAC8H,aAAa,CAAC,EAAE;YAC3FgB,OAAO,CAAC9I,IAAI,CAAC8H,aAAa,CAACmC,OAAO,CAACpC,YAAY,IAAI;cACjD,MAAM/C,OAAO,GAAG+C,YAAY,CAAC/C,OAAO;cAEpC,IAAI,CAACA,OAAO,EAAE;gBACZpE,OAAO,CAACE,KAAK,CAAC,kBAAkB,EAAEiH,YAAY,CAAC;gBAC/C;cACF;cAEAnH,OAAO,CAACC,GAAG,CAAC,WAAWmE,OAAO,UAAU,CAAC;cAEzC,IAAI+C,YAAY,CAAC7C,MAAM,IAAI7E,KAAK,CAACC,OAAO,CAACyH,YAAY,CAAC7C,MAAM,CAAC,EAAE;gBAC7D6C,YAAY,CAAC7C,MAAM,CAACiF,OAAO,CAACwC,KAAK,IAAI;kBACnC;kBACA,IAAI,CAACA,KAAK,CAACC,OAAO,EAAE;oBAClBhM,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAE6L,KAAK,CAAC;oBACtC;kBACF;kBAEA,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC;kBACxC;kBACA,MAAMC,SAAS,GAAGH,KAAK,CAACI,YAAY,GAClCC,oBAAoB,CAACL,KAAK,CAACI,YAAY,CAAC,GACxCE,iBAAiB,CAACL,OAAO,CAAC;;kBAE5B;kBACA,MAAMM,UAAU,GAAGP,KAAK,CAACQ,YAAY,IAAI,GAAG,CAAC,CAAC;kBAC9C,MAAMC,UAAU,GAAGC,QAAQ,CAACV,KAAK,CAACS,UAAU,CAAC,IAAI,CAAC;kBAElDxM,OAAO,CAACC,GAAG,CAAC,SAASmE,OAAO,WAAW4H,OAAO,SAASE,SAAS,YAAYI,UAAU,WAAWE,UAAU,GAAG,CAAC;;kBAE/G;kBACA,MAAME,eAAe,GAAGtI,OAAO;kBAC/B,MAAMuI,iBAAiB,GAAG1N,gBAAgB,CAAC4J,GAAG,CAAC6D,eAAe,CAAC;kBAE/D,IAAIC,iBAAiB,EAAE;oBACrB;oBACA,MAAMC,SAAS,GAAG;sBAChBZ,OAAO;sBACPE,SAAS;sBACTK,YAAY,EAAED,UAAU;sBACxBE;oBACF,CAAC;;oBAED;oBACAK,wBAAwB,CAACF,iBAAiB,EAAEC,SAAS,CAAC;;oBAEtD;oBACA,IAAI7I,oBAAoB,IAAIA,oBAAoB,CAACK,OAAO,KAAKA,OAAO,EAAE;sBACpEF,sBAAsB,CAAC4I,IAAI,KAAK;wBAC9B,GAAGA,IAAI;wBACP3I,OAAO,EAAE,IAAI;wBACb6H,OAAO;wBACPE,SAAS;wBACTrC,KAAK,EAAEyC,UAAU;wBACjBE;sBACF,CAAC,CAAC,CAAC;oBACL;kBACF,CAAC,MAAM;oBACLxM,OAAO,CAAC+M,IAAI,CAAC,WAAW3I,OAAO,SAAS4H,OAAO,SAAS,CAAC;kBAC3D;gBACF,CAAC,CAAC;cACJ,CAAC,MAAM;gBACLhM,OAAO,CAACE,KAAK,CAAC,eAAe,EAAEiH,YAAY,CAAC;cAC9C;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACLnH,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEkI,OAAO,CAAC;UAC9D;QACF,CAAC,CAAC,OAAOlI,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,aAAa,EAAEA,KAAK,EAAEiI,OAAO,CAAC;QAC9C;MACF;;MAEA;MACA,IAAI9C,KAAK,KAAKnH,WAAW,CAACQ,GAAG,IAAI0J,OAAO,CAACuB,IAAI,KAAK,KAAK,EAAE;QACvD3J,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEmI,OAAO,CAAC;;QAEhC;QACAhK,MAAM,CAAC4O,WAAW,CAAC;UACjBrD,IAAI,EAAE,KAAK;UACXrK,IAAI,EAAE8I,OAAO,CAAC9I;QAChB,CAAC,EAAE,GAAG,CAAC;QAEP,MAAM2N,OAAO,GAAG7E,OAAO,CAAC9I,IAAI;QAC5B,MAAM4N,KAAK,GAAGD,OAAO,CAACC,KAAK;QAC3B,MAAMC,MAAM,GAAGF,OAAO,CAACG,IAAI,IAAI,EAAE;QAEjCD,MAAM,CAAC5D,OAAO,CAAC8D,KAAK,IAAI;UACtB,MAAMC,OAAO,GAAGD,KAAK,CAACE,KAAK;UAC3B,MAAMC,SAAS,GAAGH,KAAK,CAACG,SAAS;UACjC,MAAMC,WAAW,GAAGJ,KAAK,CAACI,WAAW;UACrC,MAAMC,SAAS,GAAGL,KAAK,CAACK,SAAS;UACjC,MAAMC,OAAO,GAAGN,KAAK,CAACM,OAAO;;UAE7B;UACA,MAAMzD,QAAQ,GAAGpI,SAAS,CAAC2D,OAAO,CAAC+B,YAAY,CAC7CC,UAAU,CAACwF,OAAO,CAACW,OAAO,CAAC,EAC3BnG,UAAU,CAACwF,OAAO,CAACY,MAAM,CAC3B,CAAC;;UAED;UACA,IAAIC,WAAW,GAAG,EAAE;UACpB,IAAIC,YAAY,GAAG,EAAE;UAErB,QAAOP,SAAS;YACd,KAAK,KAAK;cAAG;cACXM,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,QAAQ;cACtBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,MAAM;cAAE;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF;cACED,WAAW,GAAGL,WAAW,IAAI,MAAM;cACnCM,YAAY,GAAG,SAAS;UAC5B;;UAEA;UACAC,iBAAiB,CAAC9D,QAAQ,EAAE4D,WAAW,EAAEC,YAAY,CAAC;UAEtD/N,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;YACtBgO,IAAI,EAAEX,OAAO;YACbY,IAAI,EAAEV,SAAS;YACfW,IAAI,EAAEV,WAAW;YACjBW,IAAI,EAAEV,SAAS;YACfW,IAAI,EAAEV,OAAO;YACbW,EAAE,EAAEpE;UACN,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAI7E,KAAK,KAAKnH,WAAW,CAACJ,KAAK,IAAIsK,OAAO,CAACuB,IAAI,KAAK,OAAO,EAAE;QAC3D3J,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEmI,OAAO,CAAC;QAEjC,MAAMmG,SAAS,GAAGnG,OAAO,CAAC9I,IAAI;QAC9B,MAAMkP,OAAO,GAAGD,SAAS,CAACC,OAAO;QACjC,MAAMC,SAAS,GAAGF,SAAS,CAACE,SAAS;QACrC,MAAMC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAM5L,QAAQ,GAAG;UACfN,QAAQ,EAAEiF,UAAU,CAAC8G,SAAS,CAAChD,OAAO,CAAC;UACvChJ,SAAS,EAAEkF,UAAU,CAAC8G,SAAS,CAACjD,QAAQ;QAC1C,CAAC;;QAED;QACA,MAAMpB,QAAQ,GAAGpI,SAAS,CAAC2D,OAAO,CAAC+B,YAAY,CAAC1E,QAAQ,CAACP,SAAS,EAAEO,QAAQ,CAACN,QAAQ,CAAC;;QAEtF;QACA,QAAOiM,SAAS;UACd,KAAK,GAAG;YAAG;YACTT,iBAAiB,CAAC9D,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;YAClD;UACF,KAAK,KAAK;YAAG;YACX8D,iBAAiB,CAAC9D,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX8D,iBAAiB,CAAC9D,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;YAC/C;UACF,KAAK,IAAI;YAAG;YACV,MAAMyE,UAAU,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAE;YAC1CZ,iBAAiB,CAAC9D,QAAQ,EAAE,KAAKyE,UAAU,MAAM,EAAE,SAAS,CAAC;YAC7D;UACF,KAAK,IAAI;YAAG;YACVX,iBAAiB,CAAC9D,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV8D,iBAAiB,CAAC9D,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YAAG;YACZ8D,iBAAiB,CAAC9D,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV8D,iBAAiB,CAAC9D,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV8D,iBAAiB,CAAC9D,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX,MAAM2E,YAAY,GAAGN,SAAS,CAACK,UAAU,CAAC,CAAE;YAC5C,MAAME,QAAQ,GAAGP,SAAS,CAACQ,UAAU,CAAC,CAAM;YAC5Cf,iBAAiB,CAAC9D,QAAQ,EAAE,QAAQ8E,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,EAAE,SAAS,CAAC;YAC/F;QACJ;QAEA;MACF;MACA;MACA9O,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBoF,KAAK;QACLsE,IAAI,EAAEvB,OAAO,CAACuB,IAAI;QAClBrK,IAAI,EAAE8I;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOlI,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEiI,OAAO,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAM8G,cAAc,GAAGA,CAAA,KAAM;IAC3BjP,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAMiP,KAAK,GAAG,QAAQhR,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnEyB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEiP,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBrP,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAEDkP,EAAE,CAACG,SAAS,GAAIjC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMlF,OAAO,GAAGE,IAAI,CAACC,KAAK,CAAC+E,KAAK,CAAC/N,IAAI,CAAC;;QAEtC;QACA,IAAI6I,OAAO,CAACwB,IAAI,KAAK,SAAS,EAAE;UAC9B3J,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEkI,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACwB,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIxB,OAAO,CAACwB,IAAI,KAAK,SAAS,IAAIxB,OAAO,CAAC9C,KAAK,IAAI8C,OAAO,CAACC,OAAO,EAAE;UAClE;UACAF,iBAAiB,CAACC,OAAO,CAAC9C,KAAK,EAAEgD,IAAI,CAACkH,SAAS,CAACpH,OAAO,CAACC,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOlI,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDiP,EAAE,CAACK,OAAO,GAAItP,KAAK,IAAK;MACtBF,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDiP,EAAE,CAACM,OAAO,GAAG,MAAM;MACjBzP,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACAyP,UAAU,CAACT,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACAhN,aAAa,CAACwD,OAAO,GAAG0J,EAAE;EAC5B,CAAC;EAEDnT,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4F,YAAY,CAAC6D,OAAO,EAAE;;IAE3B;IACAkK,aAAa,CAAC,CAAC;;IAEf;IACA7R,KAAK,GAAG,IAAI1B,KAAK,CAACwT,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAIzT,KAAK,CAAC0T,iBAAiB,CACxC,EAAE,EACF1R,MAAM,CAAC2R,UAAU,GAAG3R,MAAM,CAAC4R,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAAC/M,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChC6O,MAAM,CAACrJ,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB1C,SAAS,CAAC2B,OAAO,GAAGoK,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAI7T,KAAK,CAAC8T,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAAChS,MAAM,CAAC2R,UAAU,EAAE3R,MAAM,CAAC4R,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAAClS,MAAM,CAACmS,gBAAgB,CAAC;IAC/C3O,YAAY,CAAC6D,OAAO,CAAC+K,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAItU,KAAK,CAACuU,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5D7S,KAAK,CAAC0M,GAAG,CAACkG,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAIxU,KAAK,CAACyU,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAAC9N,QAAQ,CAAC9B,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1ClD,KAAK,CAAC0M,GAAG,CAACoG,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAI1U,KAAK,CAACyU,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAAChO,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3ClD,KAAK,CAAC0M,GAAG,CAACsG,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAI3U,KAAK,CAAC4U,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAACjO,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChC+P,SAAS,CAACE,KAAK,GAAG7P,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7B0P,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxBtT,KAAK,CAAC0M,GAAG,CAACuG,SAAS,CAAC;;IAEpB;IACAtT,QAAQ,GAAG,IAAInB,aAAa,CAACuT,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzDhT,QAAQ,CAAC4T,aAAa,GAAG,IAAI;IAC7B5T,QAAQ,CAAC6T,aAAa,GAAG,IAAI;IAC7B7T,QAAQ,CAAC8T,kBAAkB,GAAG,KAAK;IACnC9T,QAAQ,CAACiJ,WAAW,GAAG,EAAE;IACzBjJ,QAAQ,CAACkJ,WAAW,GAAG,GAAG;IAC1BlJ,QAAQ,CAACmJ,aAAa,GAAGxF,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC5D,QAAQ,CAACoJ,aAAa,GAAG,CAAC;IAC1BpJ,QAAQ,CAAC8I,MAAM,CAACvF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BvD,QAAQ,CAACgJ,MAAM,CAAC,CAAC;;IAEjB;IACAzG,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnB4P,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBpS,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBqG,SAAS,EAAE,CAAC,CAACA,SAAS,CAAC2B;IACzB,CAAC,CAAC;;IAEF;IACA,MAAM+L,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAIvV,UAAU,CAAC,CAAC;QACtCuV,aAAa,CAACC,IAAI,CAChB,GAAGjT,QAAQ,uBAAuB,EACjCkT,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAAChU,KAAK;;UAE/B;UACA,MAAMkU,gBAAgB,GAAG,IAAI5V,KAAK,CAAC6V,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACG,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAIlW,KAAK,CAACmW,oBAAoB,CAAC;kBACjD1N,KAAK,EAAE,QAAQ;kBAAO;kBACtB2N,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIP,KAAK,CAACE,QAAQ,CAACxH,GAAG,EAAE;kBACtByH,WAAW,CAACzH,GAAG,GAAGsH,KAAK,CAACE,QAAQ,CAACxH,GAAG;gBACtC;;gBAEA;gBACAsH,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5BtS,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEkS,KAAK,CAAC7K,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMyK,YAAY,CAACY,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMT,KAAK,GAAGJ,YAAY,CAACY,QAAQ,CAAC,CAAC,CAAC;YACtCX,gBAAgB,CAACxH,GAAG,CAAC2H,KAAK,CAAC;UAC7B;;UAEA;UACArU,KAAK,CAAC0M,GAAG,CAACwH,gBAAgB,CAAC;;UAE3B;UACA/U,gBAAgB,GAAG+U,gBAAgB;UAEnChS,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9BmC,kBAAkB,CAAC,IAAI,CAAC;UACxBsP,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAa,GAAG,IAAK;UACP7S,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC4S,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAElH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACD8F,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMqB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMhB,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACAvC,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAI+C,gBAAgB,EAAE;UACpB,MAAMiB,YAAY,GAAG;YACnB1Q,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UAED,MAAMwQ,UAAU,GAAGpR,SAAS,CAAC2D,OAAO,CAAC+B,YAAY,CAACyL,YAAY,CAAC1Q,SAAS,EAAE0Q,YAAY,CAACzQ,QAAQ,CAAC;UAChG;UACAwP,gBAAgB,CAAClP,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACxCgR,gBAAgB,CAACzH,QAAQ,CAAC1J,CAAC,GAAIO,IAAI,CAACC,EAAE,GAAG4R,YAAY,CAACvQ,OAAO,GAAGtB,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9E2Q,gBAAgB,CAACnK,YAAY,CAAC,CAAC;UAC/BmK,gBAAgB,CAAClK,iBAAiB,CAAC,IAAI,CAAC;UACxCxK,eAAe,GAAG0U,gBAAgB,CAAClP,QAAQ,CAACrC,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAMiT,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAI5B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAM2B,WAAW,GAAIC,WAAW,IAAK;UACnCvT,OAAO,CAACC,GAAG,CAAC,WAAWmT,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAInX,UAAU,CAAC,CAAC;UAC/BmX,MAAM,CAAC3B,IAAI,CACTuB,GAAG,EACFtB,IAAI,IAAK;YACR9R,OAAO,CAACC,GAAG,CAAC,WAAWmT,GAAG,EAAE,CAAC;YAC7B1B,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAe,GAAG,IAAK;YACP7S,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC4S,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAElH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACA3L,KAAK,IAAK;YACTF,OAAO,CAACE,KAAK,CAAC,SAASkT,GAAG,EAAE,EAAElT,KAAK,CAAC;YACpC,IAAIqT,WAAW,GAAG,CAAC,EAAE;cACnBvT,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3ByP,UAAU,CAAC,MAAM4D,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACL5B,MAAM,CAACzR,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAEDoT,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAInX,UAAU,CAAC,CAAC;IAC/BmX,MAAM,CAAC3B,IAAI,CACT,GAAGjT,QAAQ,4BAA4B,EACvC,MAAOkT,IAAI,IAAK;MACd,IAAI;QACF,MAAM1H,KAAK,GAAG0H,IAAI,CAAChU,KAAK;QACxBsM,KAAK,CAACqJ,KAAK,CAACzS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBoJ,KAAK,CAACtH,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAE3B;QACA,IAAIlD,KAAK,EAAE;UACXA,KAAK,CAAC0M,GAAG,CAACJ,KAAK,CAAC;;UAEhB;UACA,MAAM4I,eAAe,CAAC,CAAC;QACvB,CAAC,MAAM;UACLhT,OAAO,CAACE,KAAK,CAAC,eAAe,CAAC;QAChC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACA2S,GAAG,IAAK;MACP7S,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC4S,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAElH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACA3L,KAAK,IAAK;MACTF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BF,OAAO,CAACE,KAAK,CAAC,OAAO,EAAE;QACrBwT,IAAI,EAAExT,KAAK,CAACyJ,IAAI;QAChBgK,IAAI,EAAEzT,KAAK,CAACiI,OAAO;QACnByL,KAAK,EAAE,GAAGhV,QAAQ,4BAA4B;QAC9CiV,KAAK,EAAE,GAAGjV,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMkV,OAAO,GAAGA,CAAA,KAAM;MACpB5R,iBAAiB,CAACuD,OAAO,GAAGsO,qBAAqB,CAACD,OAAO,CAAC;;MAE1D;MACAtX,KAAK,CAACiK,MAAM,CAAC,CAAC;MAEd,IAAIjJ,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAAC8H,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMyO,UAAU,GAAG/W,gBAAgB,CAAC6F,QAAQ,CAACrC,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMwT,eAAe,GAAGhX,gBAAgB,CAACsN,QAAQ,CAAC1J,CAAC;;QAEnD;QACA;QACA,MAAMqT,gBAAgB,GAAG,EAAED,eAAe,GAAG7S,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAM8S,YAAY,GAAG,IAAI/X,KAAK,CAACqP,OAAO,CACpC,CAAC,EAAE,GAAGrK,IAAI,CAACgT,GAAG,CAACF,gBAAgB,CAAC,EAChC,GAAG,EACH,CAAC,EAAE,GAAG9S,IAAI,CAACiT,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACArE,MAAM,CAAC/M,QAAQ,CAACsD,IAAI,CAAC4N,UAAU,CAAC,CAACxJ,GAAG,CAAC2J,YAAY,CAAC;;QAElD;QACAtE,MAAM,CAACjK,EAAE,CAAC5E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAMsT,YAAY,GAAGN,UAAU,CAACvT,KAAK,CAAC,CAAC;QACvCoP,MAAM,CAACrJ,MAAM,CAAC8N,YAAY,CAAC;;QAE3B;QACAzE,MAAM,CAAC0E,sBAAsB,CAAC,CAAC;QAC/B1E,MAAM,CAAChI,YAAY,CAAC,CAAC;QACrBgI,MAAM,CAAC/H,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACArK,QAAQ,CAAC8H,OAAO,GAAG,KAAK;;QAExB;QACA9H,QAAQ,CAAC8I,MAAM,CAACH,IAAI,CAAC4N,UAAU,CAAC;QAChCvW,QAAQ,CAACgJ,MAAM,CAAC,CAAC;QAEjBzG,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnBuU,IAAI,EAAER,UAAU,CAAChM,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAE8H,MAAM,CAAC/M,QAAQ,CAACkF,OAAO,CAAC,CAAC;UAC/ByM,IAAI,EAAEH,YAAY,CAACtM,OAAO,CAAC,CAAC;UAC5B0M,IAAI,EAAE7E,MAAM,CAAC8E,iBAAiB,CAAC,IAAIvY,KAAK,CAACqP,OAAO,CAAC,CAAC,CAAC,CAACzD,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIxK,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAAC8H,OAAO,GAAG,IAAI;;QAEvB;QACAsK,MAAM,CAACjK,EAAE,CAAC5E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAII,IAAI,CAACwT,GAAG,CAAC/E,MAAM,CAAC/M,QAAQ,CAACjC,CAAC,CAAC,GAAG,EAAE,EAAE;UACpCgP,MAAM,CAAC/M,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BvD,QAAQ,CAAC8I,MAAM,CAACvF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B6O,MAAM,CAACrJ,MAAM,CAAC/I,QAAQ,CAAC8I,MAAM,CAAC;UAC9B9I,QAAQ,CAACgJ,MAAM,CAAC,CAAC;QACnB;;QAEA;QACA;QACAoJ,MAAM,CAAChI,YAAY,CAAC,CAAC;QACrBgI,MAAM,CAAC/H,iBAAiB,CAAC,IAAI,CAAC;MAEhC,CAAC,MAAM,IAAItK,UAAU,KAAK,cAAc,EAAE;QACxC;QACAC,QAAQ,CAACgJ,MAAM,CAAC,CAAC;MACnB;MAEA,IAAIhJ,QAAQ,EAAEA,QAAQ,CAACgJ,MAAM,CAAC,CAAC;MAC/B,IAAI3I,KAAK,IAAI+R,MAAM,EAAE;QACnBI,QAAQ,CAAC4E,MAAM,CAAC/W,KAAK,EAAE+R,MAAM,CAAC;MAChC;IACF,CAAC;IAEDiE,OAAO,CAAC,CAAC;;IAET;IACA,MAAMgB,YAAY,GAAGA,CAAA,KAAM;MACzBjF,MAAM,CAACkF,MAAM,GAAG3W,MAAM,CAAC2R,UAAU,GAAG3R,MAAM,CAAC4R,WAAW;MACtDH,MAAM,CAAC0E,sBAAsB,CAAC,CAAC;MAC/BtE,QAAQ,CAACG,OAAO,CAAChS,MAAM,CAAC2R,UAAU,EAAE3R,MAAM,CAAC4R,WAAW,CAAC;IACzD,CAAC;IACD5R,MAAM,CAAC4W,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACA1W,MAAM,CAAC6W,aAAa,GAAG,MAAM;MAC3B,IAAInR,SAAS,CAAC2B,OAAO,EAAE;QACrB3B,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC8C,SAAS,CAAC2B,OAAO,CAACe,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC1C,SAAS,CAAC2B,OAAO,CAACoC,YAAY,CAAC,CAAC;QAChC/D,SAAS,CAAC2B,OAAO,CAACqC,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAIrK,QAAQ,EAAE;UACZA,QAAQ,CAAC8I,MAAM,CAACvF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BvD,QAAQ,CAAC8H,OAAO,GAAG,IAAI;UACvB9H,QAAQ,CAACgJ,MAAM,CAAC,CAAC;QACnB;QAEAjJ,UAAU,GAAG,QAAQ;QACrBwC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAIiC,iBAAiB,CAACuD,OAAO,EAAE;QAC7ByP,oBAAoB,CAAChT,iBAAiB,CAACuD,OAAO,CAAC;QAC/CvD,iBAAiB,CAACuD,OAAO,GAAG,IAAI;MAClC;;MAEA;MACA,IAAIrI,oBAAoB,EAAE;QACxB+X,aAAa,CAAC/X,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;;MAEA;MACA,IAAI6E,aAAa,CAACwD,OAAO,EAAE;QACzBxD,aAAa,CAACwD,OAAO,CAAC2P,KAAK,CAAC,CAAC;QAC7BnT,aAAa,CAACwD,OAAO,GAAG,IAAI;MAC9B;;MAEA;MACArH,MAAM,CAACiX,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;;MAElD;MACA,IAAI7E,QAAQ,IAAIrO,YAAY,CAAC6D,OAAO,EAAE;QACpC7D,YAAY,CAAC6D,OAAO,CAAC6P,WAAW,CAACrF,QAAQ,CAACQ,UAAU,CAAC;QACrDR,QAAQ,CAACsF,OAAO,CAAC,CAAC;MACpB;;MAEA;MACA,IAAI1W,aAAa,EAAE;QACjBA,aAAa,CAAC0K,OAAO,CAAC,CAACwB,SAAS,EAAEtB,EAAE,KAAK;UACvC,IAAIsB,SAAS,CAACX,KAAK,IAAItM,KAAK,EAAE;YAC5BA,KAAK,CAACmN,MAAM,CAACF,SAAS,CAACX,KAAK,CAAC;UAC/B;QACF,CAAC,CAAC;QACFvL,aAAa,CAAC2W,KAAK,CAAC,CAAC;MACvB;;MAEA;MACAvW,gBAAgB,CAACsK,OAAO,CAAEkM,QAAQ,IAAK;QACrC,IAAI3X,KAAK,IAAI2X,QAAQ,CAACrL,KAAK,EAAE;UAC3BtM,KAAK,CAACmN,MAAM,CAACwK,QAAQ,CAACrL,KAAK,CAAC;QAC9B;MACF,CAAC,CAAC;MACFnL,gBAAgB,CAACuW,KAAK,CAAC,CAAC;MACxBtW,kBAAkB,CAACsW,KAAK,CAAC,CAAC;;MAE1B;;MAEA;MACA1X,KAAK,GAAG,IAAI;MACZL,QAAQ,GAAG,IAAI;MACfC,qBAAqB,GAAG,IAAI;MAC5BC,qBAAqB,GAAG,IAAI;MAC5BC,oBAAoB,GAAG,IAAI;MAC3BC,0BAA0B,GAAG,IAAI;MACjCZ,gBAAgB,GAAG,IAAI;MAEvB+C,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjE,SAAS,CAAC,MAAM;IACd;IACAmD,qBAAqB,CAAC,CAAC;;IAEvB;IACA,MAAMuW,uBAAuB,GAAGA,CAAA,KAAM;MACpC1V,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCd,qBAAqB,CAAC,CAAC;IACzB,CAAC;;IAED;IACAf,MAAM,CAAC4W,gBAAgB,CAAC,oBAAoB,EAAEU,uBAAuB,CAAC;;IAEtE;IACA,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnCzW,qBAAqB,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC;;IAET;IACA,OAAO,MAAM;MACXf,MAAM,CAACiX,mBAAmB,CAAC,oBAAoB,EAAEK,uBAAuB,CAAC;MACzEP,aAAa,CAACQ,UAAU,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3Z,SAAS,CAAC,MAAM;IACd;IACA,IAAI8B,KAAK,IAAIgE,SAAS,CAAC2D,OAAO,EAAE;MAC9BzF,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA,MAAM4V,KAAK,GAAGnG,UAAU,CAAC,MAAM;QAC7B,IAAI5R,KAAK,IAAIgE,SAAS,CAAC2D,OAAO,EAAE;UAAG;UACjCqQ,mBAAmB,CAAChU,SAAS,CAAC2D,OAAO,CAAC;QACxC;MACF,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAMsQ,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM;MACL7V,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC,EAAE,CAACnC,KAAK,CAAC,CAAC;;EAEX;EACA9B,SAAS,CAAC,MAAM;IACd,IAAI4F,YAAY,CAAC6D,OAAO,EAAE;MACxB;MACA,MAAMuQ,WAAW,GAAI3I,KAAK,IAAK;QAC7B,IAAIvP,KAAK,IAAIgG,SAAS,CAAC2B,OAAO,EAAE;UAC9BzF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEoN,KAAK,CAAC4I,OAAO,EAAE5I,KAAK,CAAC6I,OAAO,CAAC;UACpDC,gBAAgB,CAAC9I,KAAK,EAAEzL,YAAY,CAAC6D,OAAO,EAAE3H,KAAK,EAAEgG,SAAS,CAAC2B,OAAO,EAAEvB,sBAAsB,CAAC;QACjG,CAAC,MAAM;UACLlE,OAAO,CAAC+M,IAAI,CAAC,4BAA4B,CAAC;QAC5C;MACF,CAAC;;MAED;MACAnL,YAAY,CAAC6D,OAAO,CAACuP,gBAAgB,CAAC,OAAO,EAAEgB,WAAW,CAAC;;MAE3D;MACAhW,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC2B,YAAY,CAAC6D,OAAO,CAAC;;MAEpD;MACA,OAAO,MAAM;QACX,IAAI7D,YAAY,CAAC6D,OAAO,EAAE;UACxB7D,YAAY,CAAC6D,OAAO,CAAC4P,mBAAmB,CAAC,OAAO,EAAEW,WAAW,CAAC;UAC9DhW,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACnC,KAAK,EAAEgG,SAAS,CAAC2B,OAAO,CAAC,CAAC;;EAE9B;EACA,MAAM2Q,SAAS,GAAGja,WAAW,CAAC,MAAM;IAClC6D,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B;EACF,CAAC,EAAE,CAAC2B,YAAY,EAAEqD,aAAa,EAAEhG,gBAAgB,CAAC,CAAC;;EAEnD;EACA,MAAMoX,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMC,QAAQ,GAAG,IAAIla,KAAK,CAACma,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChD,MAAMlE,QAAQ,GAAG,IAAIjW,KAAK,CAACoa,iBAAiB,CAAC;MAAE3R,KAAK,EAAE;IAAS,CAAC,CAAC;IACjE,MAAM8H,iBAAiB,GAAG,IAAIvQ,KAAK,CAACqa,IAAI,CAACH,QAAQ,EAAEjE,QAAQ,CAAC;;IAE5D;IACA,MAAMqE,YAAY,GAAG,IAAIta,KAAK,CAACua,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC5D,MAAMC,YAAY,GAAG,IAAIxa,KAAK,CAACoa,iBAAiB,CAAC;MAAE3R,KAAK,EAAE;IAAS,CAAC,CAAC;IACrE,MAAMgS,SAAS,GAAG,IAAIza,KAAK,CAACqa,IAAI,CAACC,YAAY,EAAEE,YAAY,CAAC;IAC5DC,SAAS,CAAC/T,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAClC2L,iBAAiB,CAACnC,GAAG,CAACqM,SAAS,CAAC;IAEhC,OAAOlK,iBAAiB;EAC1B,CAAC;;EAED;EACA,MAAMmK,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAChZ,KAAK,EAAE;;IAEZ;IACAmB,gBAAgB,CAACsK,OAAO,CAAC,CAACkM,QAAQ,EAAErR,OAAO,KAAK;MAC9C,IAAIqR,QAAQ,CAACrL,KAAK,EAAE;QAClB;QACA,MAAM2M,cAAc,GAAG,IAAI3a,KAAK,CAAC4a,cAAc,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QACzD,MAAMC,cAAc,GAAG,IAAI7a,KAAK,CAACoa,iBAAiB,CAAC;UACjD3R,KAAK,EAAE,QAAQ;UACfqS,WAAW,EAAE,IAAI;UACjBC,OAAO,EAAE,GAAG;UAAG;UACfC,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,MAAMC,UAAU,GAAG,IAAIjb,KAAK,CAACqa,IAAI,CAACM,cAAc,EAAEE,cAAc,CAAC;QACjEI,UAAU,CAACvU,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE;;QAEnC;QACAqW,UAAU,CAACC,QAAQ,GAAG;UACpB3N,IAAI,EAAE,cAAc;UACpBvF,OAAO,EAAEA,OAAO;UAChBkD,IAAI,EAAEmO,QAAQ,CAACtO,YAAY,CAACG,IAAI;UAChCiQ,aAAa,EAAE;QACjB,CAAC;;QAED;QACA9B,QAAQ,CAACrL,KAAK,CAACI,GAAG,CAAC6M,UAAU,CAAC;QAE9BrX,OAAO,CAACC,GAAG,CAAC,OAAOwV,QAAQ,CAACtO,YAAY,CAACG,IAAI,KAAKlD,OAAO,aAAa,CAAC;MACzE;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACApI,SAAS,CAAC,MAAM;IACd;IACA,MAAM6Z,KAAK,GAAGnG,UAAU,CAAC,MAAM;MAC7B,IAAIzQ,gBAAgB,CAACuY,IAAI,GAAG,CAAC,EAAE;QAC7BxX,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1B6W,eAAe,CAAC,CAAC;MACnB;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAE;;IAEX,OAAO,MAAMf,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7Z,SAAS,CAAC,MAAM;IACd;IACA,MAAM6Z,KAAK,GAAGnG,UAAU,CAAC,MAAM;MAC7B1P,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACvB,IAAI7B,MAAM,CAACqZ,UAAU,EAAE;QACrBrZ,MAAM,CAACqZ,UAAU,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,MAAM;QACLzX,OAAO,CAACE,KAAK,CAAC,iBAAiB,CAAC;MAClC;IACF,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAM6V,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE/Y,OAAA,CAAAE,SAAA;IAAA2V,QAAA,gBACE7V,OAAA;MAAM4a,KAAK,EAAE/S,UAAW;MAAAgO,QAAA,EAAC;IAAK;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrChb,OAAA,CAACJ,MAAM;MACLgb,KAAK,EAAElT,uBAAwB;MAC/BuT,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAE/Q,wBAAyB;MACnCgR,OAAO,EAAErb,iBAAiB,CAACwK,aAAa,CAACyD,GAAG,CAAC1D,YAAY,KAAK;QAC5DD,KAAK,EAAEC,YAAY,CAACG,IAAI;QACxB4Q,KAAK,EAAE/Q,YAAY,CAACG;MACtB,CAAC,CAAC,CAAE;MACJkQ,IAAI,EAAC,OAAO;MACZW,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACblV,MAAM,EAAE,IAAI;QACZmV,SAAS,EAAE;MACb;IAAE;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACFhb,OAAA;MAAKwb,GAAG,EAAE1W,YAAa;MAAC8V,KAAK,EAAE;QAAEhT,KAAK,EAAE,MAAM;QAAE4F,MAAM,EAAE;MAAO;IAAE;MAAAqN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGnE7T,mBAAmB,CAACE,OAAO,iBAC1BrH,OAAA;MACE4a,KAAK,EAAE;QACL5U,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAACnC,CAAC,IAAI;QAC3C8D,GAAG,EAAE,GAAGR,mBAAmB,CAACnB,QAAQ,CAACjC,CAAC,IAAI;QAC1CoC,SAAS,EAAE,wBAAwB;QACnCC,MAAM,EAAE,IAAI;QACZK,eAAe,EAAE,qBAAqB;QACtCsB,KAAK,EAAE,OAAO;QACdpB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,8BAA8B;QACzCN,OAAO,EAAE;MACX,CAAE;MAAAqP,QAAA,GAED1O,mBAAmB,CAACI,OAAO,eAC5BvH,OAAA;QACE4a,KAAK,EAAE;UACL5U,QAAQ,EAAE,UAAU;UACpB2B,GAAG,EAAE,KAAK;UACV8T,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,MAAM;UAClBhV,MAAM,EAAE,MAAM;UACdqB,KAAK,EAAE,OAAO;UACdlB,QAAQ,EAAE,MAAM;UAChBD,MAAM,EAAE,SAAS;UACjBJ,OAAO,EAAE;QACX,CAAE;QACFmV,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAACxU,sBAAsB,CAAE;QAAAyO,QAAA,EAC3D;MAED;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAEDhb,OAAA;MAAK4a,KAAK,EAAE7U,oBAAqB;MAAA8P,QAAA,gBAC/B7V,OAAA;QACE4a,KAAK,EAAE;UACL,GAAGrU,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EkC,KAAK,EAAElC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF8V,OAAO,EAAEnT,kBAAmB;QAAAqN,QAAA,EAC7B;MAED;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACThb,OAAA;QACE4a,KAAK,EAAE;UACL,GAAGrU,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EkC,KAAK,EAAElC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF8V,OAAO,EAAEjT,kBAAmB;QAAAmN,QAAA,EAC7B;MAED;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAAnW,EAAA,CA11CMJ,WAAW;AAAAoX,EAAA,GAAXpX,WAAW;AA21CjB,SAASqX,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAACpU,KAAK,GAAG,GAAG;EAClBoU,MAAM,CAACxO,MAAM,GAAG,EAAE;;EAElB;EACA2O,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAACpU,KAAK,GAAC,CAAC,EAAEoU,MAAM,CAACxO,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAMiP,OAAO,GAAG,IAAInd,KAAK,CAACod,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAIrd,KAAK,CAACsd,cAAc,CAAC;IAC9C7O,GAAG,EAAE0O,OAAO;IACZrC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMyC,MAAM,GAAG,IAAIvd,KAAK,CAACwd,MAAM,CAACH,cAAc,CAAC;EAC/CE,MAAM,CAAClG,KAAK,CAACzS,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAO2Y,MAAM;AACf;;AAIA;AACAvb,MAAM,CAACyb,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMhK,MAAM,GAAGkJ,QAAQ,CAACe,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAInK,MAAM,EAAE;MACV;MACA,MAAMoK,MAAM,GAAGpK,MAAM,CAAC/M,QAAQ,CAACrC,KAAK,CAAC,CAAC;;MAEtC;MACAoP,MAAM,CAAC/M,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9B6O,MAAM,CAACjK,EAAE,CAAC5E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtB6O,MAAM,CAACrJ,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAqJ,MAAM,CAAChI,YAAY,CAAC,CAAC;MACrBgI,MAAM,CAAC/H,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMrK,QAAQ,GAAGsb,QAAQ,CAACe,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIzc,QAAQ,EAAE;QACZA,QAAQ,CAAC8I,MAAM,CAACvF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BvD,QAAQ,CAACgJ,MAAM,CAAC,CAAC;MACnB;MAEAzG,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBka,GAAG,EAAEF,MAAM,CAACjS,OAAO,CAAC,CAAC;QACrBoS,GAAG,EAAEvK,MAAM,CAAC/M,QAAQ,CAACkF,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOqS,CAAC,EAAE;IACVra,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEma,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAM1K,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACF3P,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAMuT,MAAM,GAAG,IAAInX,UAAU,CAAC,CAAC;;IAE/B;IACA,IAAI;MACF,MAAM,CAACie,WAAW,EAAEC,WAAW,EAAEC,UAAU,EAAEC,gBAAgB,CAAC,GAAG,MAAMhJ,OAAO,CAACiJ,GAAG,CAAC,CACnFlH,MAAM,CAACmH,SAAS,CAAC,GAAG/b,QAAQ,uBAAuB,CAAC,EACpD4U,MAAM,CAACmH,SAAS,CAAC,GAAG/b,QAAQ,uBAAuB,CAAC,EAClD4U,MAAM,CAACmH,SAAS,CAAC,GAAG/b,QAAQ,sBAAsB,CAAC,EACnD4U,MAAM,CAACmH,SAAS,CAAC,GAAG/b,QAAQ,6BAA6B,CAAC,CAC7D,CAAC;;MAEF;MACAlB,qBAAqB,GAAG4c,WAAW,CAACxc,KAAK;MACzCJ,qBAAqB,CAACwU,QAAQ,CAAEC,KAAK,IAAK;QACxC,IAAIA,KAAK,CAACC,MAAM,EAAE;UACd,MAAME,WAAW,GAAG,IAAIlW,KAAK,CAACmW,oBAAoB,CAAC;YACnD1N,KAAK,EAAE,QAAQ;YACf2N,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,GAAG;YACdC,eAAe,EAAE;UACnB,CAAC,CAAC;;UAEA;UACA,IAAIP,KAAK,CAACE,QAAQ,CAACxH,GAAG,EAAE;YACtByH,WAAW,CAACzH,GAAG,GAAGsH,KAAK,CAACE,QAAQ,CAACxH,GAAG;UACtC;UACAsH,KAAK,CAACyI,OAAO,GAAGtI,WAAW;QAC/B;MACF,CAAC,CAAC;;MAEF;MACA3U,qBAAqB,GAAG4c,WAAW,CAACzc,KAAK;MACzC;MACAH,qBAAqB,CAAC8V,KAAK,CAACzS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxC;MACArD,qBAAqB,CAACuU,QAAQ,CAAEC,KAAK,IAAK;QACxC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;UAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;UAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;;MAEF;MACA9U,oBAAoB,GAAG4c,UAAU,CAAC1c,KAAK;MACvC;MACAF,oBAAoB,CAAC6V,KAAK,CAACzS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACvC;MACApD,oBAAoB,CAACsU,QAAQ,CAAEC,KAAK,IAAK;QACvC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;UAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;UAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;;MAEA;MACA7U,0BAA0B,GAAG4c,gBAAgB,CAAC3c,KAAK;MACnD;MACAD,0BAA0B,CAAC4V,KAAK,CAACzS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C;MACAnD,0BAA0B,CAACqU,QAAQ,CAAEC,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAACG,SAAS,GAAG,GAAG;UAC9BL,KAAK,CAACE,QAAQ,CAACI,SAAS,GAAG,GAAG;UAC9BN,KAAK,CAACE,QAAQ,CAACK,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;MAEJ1S,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACA,IAAI;QACF,IAAI,CAACxC,qBAAqB,EAAE;UAC1B,MAAM4c,WAAW,GAAG,MAAM9G,MAAM,CAACmH,SAAS,CAAC,GAAG/b,QAAQ,uBAAuB,CAAC;UAC9ElB,qBAAqB,GAAG4c,WAAW,CAACxc,KAAK;QAC3C;;QAEA;QACA,IAAI,CAACD,0BAA0B,EAAE;UAC/BmC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC7B,MAAMwa,gBAAgB,GAAG,MAAMjH,MAAM,CAACmH,SAAS,CAAC,GAAG/b,QAAQ,6BAA6B,CAAC;UACzFf,0BAA0B,GAAG4c,gBAAgB,CAAC3c,KAAK;UACnDD,0BAA0B,CAAC4V,KAAK,CAACzS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC7ChB,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QAC1B;MACF,CAAC,CAAC,OAAO4a,GAAG,EAAE;QACZ7a,OAAO,CAACE,KAAK,CAAC,YAAY,EAAE2a,GAAG,CAAC;MAClC;IACF;EACF,CAAC,CAAC,OAAO3a,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;;AAED;AACA,MAAM8O,mBAAmB,GAAIrF,IAAI,IAAK;EACpC,MAAMmR,KAAK,GAAG;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,KAAK,CAACnR,IAAI,CAAC,IAAI,MAAM;AAC9B,CAAC;;AAED;AACA,MAAMqE,iBAAiB,GAAGA,CAAClL,QAAQ,EAAE+V,IAAI,EAAEhU,KAAK,KAAK;EACnD;EACA,MAAM8U,MAAM,GAAGf,gBAAgB,CAACC,IAAI,CAAC;EACrCc,MAAM,CAAC7W,QAAQ,CAAC9B,GAAG,CAAC8B,QAAQ,CAACnC,CAAC,EAAE,EAAE,EAAE,CAACmC,QAAQ,CAACjC,CAAC,CAAC,CAAC,CAAE;;EAEnD;EACA6O,UAAU,CAAC,MAAM;IACf5R,KAAK,CAACmN,MAAM,CAAC0O,MAAM,CAAC;EACtB,CAAC,EAAE,GAAG,CAAC;;EAEP;EACA7b,KAAK,CAAC0M,GAAG,CAACmP,MAAM,CAAC;EAEjB3Z,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;IACvBqO,EAAE,EAAExL,QAAQ;IACZiY,EAAE,EAAElC,IAAI;IACRmC,EAAE,EAAEnW;EACN,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMiR,mBAAmB,GAAImF,iBAAiB,IAAK;EACjD,IAAI,CAACnd,KAAK,EAAE;IACVkC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAC;IAC/B;EACF;EAEA,IAAI,CAAC+a,iBAAiB,EAAE;IACtBjb,OAAO,CAACE,KAAK,CAAC,mBAAmB,CAAC;IAClC;EACF;EAEA,IAAI,CAACrC,0BAA0B,EAAE;IAC/BmC,OAAO,CAACE,KAAK,CAAC,UAAU,CAAC;IACzB;IACAgb,2BAA2B,CAACD,iBAAiB,CAAC;IAC9C;EACF;;EAEA;EACAhc,gBAAgB,CAACsK,OAAO,CAAEkM,QAAQ,IAAK;IACrC,IAAI3X,KAAK,IAAI2X,QAAQ,CAACrL,KAAK,EAAE;MAC3BtM,KAAK,CAACmN,MAAM,CAACwK,QAAQ,CAACrL,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACFnL,gBAAgB,CAACuW,KAAK,CAAC,CAAC;;EAExB;EACA5Y,iBAAiB,CAACwK,aAAa,CAACmC,OAAO,CAACpC,YAAY,IAAI;IACtD,IAAIA,YAAY,CAAC3E,QAAQ,IAAI2E,YAAY,CAAC5E,SAAS,IAAI4E,YAAY,CAAC/C,OAAO,EAAE;MAC3E;MACA,MAAM8F,QAAQ,GAAG+Q,iBAAiB,CAACzT,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAC5E,SAAS,CAAC,EAClCkF,UAAU,CAACN,YAAY,CAAC3E,QAAQ,CAClC,CAAC;MAED,IAAI;QACF;QACA,MAAMmK,iBAAiB,GAAG9O,0BAA0B,CAAC4C,KAAK,CAAC,CAAC;;QAE5D;QACAkM,iBAAiB,CAACrF,IAAI,GAAG,OAAOH,YAAY,CAACG,IAAI,EAAE;;QAEnD;QACAqF,iBAAiB,CAAC7J,QAAQ,CAAC9B,GAAG,CAACkJ,QAAQ,CAACvJ,CAAC,EAAE,EAAE,EAAE,CAACuJ,QAAQ,CAACrJ,CAAC,CAAC;;QAE3D;QACA8L,iBAAiB,CAAC8G,KAAK,CAACzS,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;QAEvC;QACA2L,iBAAiB,CAACwO,WAAW,GAAG,GAAG;;QAEnC;QACAxO,iBAAiB,CAACuF,QAAQ,CAACC,KAAK,IAAI;UAClC;UACA,IAAIA,KAAK,CAACC,MAAM,EAAE;YAChB;YACAD,KAAK,CAACE,QAAQ,CAAC6E,WAAW,GAAG,KAAK;YAClC/E,KAAK,CAACE,QAAQ,CAAC8E,OAAO,GAAG,GAAG;YAC5BhF,KAAK,CAACE,QAAQ,CAAC+I,IAAI,GAAGhf,KAAK,CAACif,UAAU;YACtClJ,KAAK,CAACE,QAAQ,CAAC+E,UAAU,GAAG,IAAI;YAChCjF,KAAK,CAACE,QAAQ,CAACiJ,SAAS,GAAG,IAAI;YAC/BnJ,KAAK,CAACE,QAAQ,CAACkJ,WAAW,GAAG,IAAI;;YAEjC;YACApJ,KAAK,CAACgJ,WAAW,GAAG,GAAG;UACzB;QACF,CAAC,CAAC;;QAEF;QACAxO,iBAAiB,CAAC2K,QAAQ,GAAG;UAC3B3N,IAAI,EAAE,cAAc;UACpBvF,OAAO,EAAE+C,YAAY,CAAC/C,OAAO;UAC7BkD,IAAI,EAAEH,YAAY,CAACG;QACrB,CAAC;;QAED;QACA,MAAMkU,gBAAgB,GAAG,IAAIpf,KAAK,CAAC4a,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAC7D,MAAMyE,gBAAgB,GAAG,IAAIrf,KAAK,CAACoa,iBAAiB,CAAC;UACnD3R,KAAK,EAAE,QAAQ;UACfqS,WAAW,EAAE,IAAI;UACjBC,OAAO,EAAE,GAAG;UAAG;UACfC,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,MAAMsE,QAAQ,GAAG,IAAItf,KAAK,CAACqa,IAAI,CAAC+E,gBAAgB,EAAEC,gBAAgB,CAAC;QACnEC,QAAQ,CAACpU,IAAI,GAAG,UAAUH,YAAY,CAACG,IAAI,EAAE;QAC7CoU,QAAQ,CAACpE,QAAQ,GAAG;UAClB3N,IAAI,EAAE,cAAc;UACpBvF,OAAO,EAAE+C,YAAY,CAAC/C,OAAO;UAC7BkD,IAAI,EAAEH,YAAY,CAACG,IAAI;UACvBqU,UAAU,EAAE;QACd,CAAC;QAEDhP,iBAAiB,CAACnC,GAAG,CAACkR,QAAQ,CAAC;;QAE/B;QACA5d,KAAK,CAAC0M,GAAG,CAACmC,iBAAiB,CAAC;;QAE5B;QACA1N,gBAAgB,CAAC+B,GAAG,CAACmG,YAAY,CAAC/C,OAAO,EAAE;UACzCgG,KAAK,EAAEuC,iBAAiB;UACxBxF,YAAY,EAAEA,YAAY;UAC1BrE,QAAQ,EAAEoH;QACZ,CAAC,CAAC;QAEFlK,OAAO,CAACC,GAAG,CAAC,SAASkH,YAAY,CAACG,IAAI,KAAKH,YAAY,CAAC/C,OAAO,kBAAkB8F,QAAQ,CAACvJ,CAAC,KAAK,CAACuJ,QAAQ,CAACrJ,CAAC,GAAG,CAAC;MACjH,CAAC,CAAC,OAAOX,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,QAAQiH,YAAY,CAACG,IAAI,YAAY,EAAEpH,KAAK,CAAC;QAC3D;QACAmW,wBAAwB,CAAClP,YAAY,EAAE+C,QAAQ,EAAE+Q,iBAAiB,CAAC;MACrE;IACF;EACF,CAAC,CAAC;;EAEF;EACAjb,OAAO,CAACC,GAAG,CAAC,OAAOhB,gBAAgB,CAACuY,IAAI,SAAS,CAAC;EAClDvY,gBAAgB,CAACsK,OAAO,CAAC,CAACkM,QAAQ,EAAErR,OAAO,KAAK;IAC9CpE,OAAO,CAACC,GAAG,CAAC,QAAQmE,OAAO,KAAKqR,QAAQ,CAACtO,YAAY,CAACG,IAAI,EAAE,CAAC;EAC/D,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAM+O,wBAAwB,GAAGA,CAAClP,YAAY,EAAE+C,QAAQ,EAAE+Q,iBAAiB,KAAK;EAC9E;EACA,MAAMO,gBAAgB,GAAG,IAAIpf,KAAK,CAAC4a,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC7D,MAAMyE,gBAAgB,GAAG,IAAIrf,KAAK,CAACoa,iBAAiB,CAAC;IACnD3R,KAAK,EAAE,QAAQ;IACfqS,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE,GAAG;IAAG;IACfC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMzK,iBAAiB,GAAG,IAAIvQ,KAAK,CAACqa,IAAI,CAAC+E,gBAAgB,EAAEC,gBAAgB,CAAC;;EAE5E;EACA9O,iBAAiB,CAACrF,IAAI,GAAG,UAAUH,YAAY,CAACG,IAAI,EAAE;;EAEtD;EACAqF,iBAAiB,CAAC7J,QAAQ,CAAC9B,GAAG,CAACkJ,QAAQ,CAACvJ,CAAC,EAAE,EAAE,EAAE,CAACuJ,QAAQ,CAACrJ,CAAC,CAAC;;EAE3D;EACA8L,iBAAiB,CAACwO,WAAW,GAAG,GAAG;;EAEnC;EACAxO,iBAAiB,CAAC2K,QAAQ,GAAG;IAC3B3N,IAAI,EAAE,cAAc;IACpBvF,OAAO,EAAE+C,YAAY,CAAC/C,OAAO;IAC7BkD,IAAI,EAAEH,YAAY,CAACG;EACrB,CAAC;;EAED;EACAxJ,KAAK,CAAC0M,GAAG,CAACmC,iBAAiB,CAAC;;EAE5B;EACA1N,gBAAgB,CAAC+B,GAAG,CAACmG,YAAY,CAAC/C,OAAO,EAAE;IACzCgG,KAAK,EAAEuC,iBAAiB;IACxBxF,YAAY,EAAEA,YAAY;IAC1BrE,QAAQ,EAAEoH;EACZ,CAAC,CAAC;EAEFlK,OAAO,CAACC,GAAG,CAAC,SAASkH,YAAY,CAACG,IAAI,KAAKH,YAAY,CAAC/C,OAAO,mBAAmB8F,QAAQ,CAACvJ,CAAC,KAAK,CAACuJ,QAAQ,CAACrJ,CAAC,GAAG,CAAC;AAClH,CAAC;;AAED;AACA,MAAMqa,2BAA2B,GAAID,iBAAiB,IAAK;EACzDre,iBAAiB,CAACwK,aAAa,CAACmC,OAAO,CAACpC,YAAY,IAAI;IACtD,IAAIA,YAAY,CAAC3E,QAAQ,IAAI2E,YAAY,CAAC5E,SAAS,IAAI4E,YAAY,CAAC/C,OAAO,EAAE;MAC3E;MACA,MAAM8F,QAAQ,GAAG+Q,iBAAiB,CAACzT,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAC5E,SAAS,CAAC,EAClCkF,UAAU,CAACN,YAAY,CAAC3E,QAAQ,CAClC,CAAC;MAED6T,wBAAwB,CAAClP,YAAY,EAAE+C,QAAQ,EAAE+Q,iBAAiB,CAAC;IACrE;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAM5O,iBAAiB,GAAIL,OAAO,IAAK;EACrC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMI,oBAAoB,GAAIwP,OAAO,IAAK;EACxC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMzF,gBAAgB,GAAGA,CAAC9I,KAAK,EAAEwO,SAAS,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,KAAK;EAC7F,IAAI,CAACH,SAAS,IAAI,CAACC,aAAa,IAAI,CAACC,cAAc,EAAE;EAErD/b,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEoN,KAAK,CAAC4I,OAAO,EAAE5I,KAAK,CAAC6I,OAAO,CAAC;;EAEvD;EACA,MAAM+F,IAAI,GAAGJ,SAAS,CAACK,qBAAqB,CAAC,CAAC;EAC9C,MAAMC,MAAM,GAAI,CAAC9O,KAAK,CAAC4I,OAAO,GAAGgG,IAAI,CAACjZ,IAAI,IAAI6Y,SAAS,CAACO,WAAW,GAAI,CAAC,GAAG,CAAC;EAC5E,MAAMC,MAAM,GAAG,EAAE,CAAChP,KAAK,CAAC6I,OAAO,GAAG+F,IAAI,CAACxX,GAAG,IAAIoX,SAAS,CAACS,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;;EAE7E;EACA,MAAMC,SAAS,GAAG,IAAIngB,KAAK,CAACogB,SAAS,CAAC,CAAC;EACvC;EACAD,SAAS,CAACE,MAAM,CAACC,MAAM,CAACC,SAAS,GAAG,CAAC;EACrCJ,SAAS,CAACE,MAAM,CAACG,IAAI,CAACD,SAAS,GAAG,CAAC;EAEnC,MAAME,WAAW,GAAG,IAAIzgB,KAAK,CAAC0gB,OAAO,CAACX,MAAM,EAAEE,MAAM,CAAC;EACrDE,SAAS,CAACQ,aAAa,CAACF,WAAW,EAAEd,cAAc,CAAC;EAEpD/b,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEkc,MAAM,EAAEE,MAAM,CAAC;;EAEtC;EACA,MAAMW,mBAAmB,GAAG,EAAE;EAE9B/d,gBAAgB,CAACsK,OAAO,CAAC,CAACkM,QAAQ,EAAErR,OAAO,KAAK;IAC9C,IAAIqR,QAAQ,CAACrL,KAAK,EAAE;MAClB;MACA4S,mBAAmB,CAACC,IAAI,CAACxH,QAAQ,CAACrL,KAAK,CAAC;MACxC;MACAqL,QAAQ,CAACrL,KAAK,CAACjG,OAAO,GAAG,IAAI;MAC7BsR,QAAQ,CAACrL,KAAK,CAAC+Q,WAAW,GAAG,IAAI,CAAC,CAAC;MACnC;MACA1F,QAAQ,CAACrL,KAAK,CAAC8H,QAAQ,CAACC,KAAK,IAAI;QAC/BA,KAAK,CAAChO,OAAO,GAAG,IAAI;QACpBgO,KAAK,CAACgJ,WAAW,GAAG,IAAI;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEFnb,OAAO,CAACC,GAAG,CAAC,QAAQ+c,mBAAmB,CAACpK,MAAM,gBAAgB,CAAC;;EAE/D;EACA,MAAMsK,sBAAsB,GAAGX,SAAS,CAACY,gBAAgB,CAACH,mBAAmB,EAAE,IAAI,CAAC;EAEpF,IAAIE,sBAAsB,CAACtK,MAAM,GAAG,CAAC,EAAE;IACrC5S,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEid,sBAAsB,CAACtK,MAAM,CAAC;IACxDsK,sBAAsB,CAAC3T,OAAO,CAAC,CAAC6T,SAAS,EAAEC,KAAK,KAAK;MACnDrd,OAAO,CAACC,GAAG,CAAC,QAAQod,KAAK,GAAG,EACjBD,SAAS,CAACE,MAAM,CAAChW,IAAI,IAAI,KAAK,EAC9B,KAAK,EAAE8V,SAAS,CAAChM,QAAQ,EACzB,WAAW,EAAEgM,SAAS,CAACE,MAAM,CAAChG,QAAQ,CAAC;IACpD,CAAC,CAAC;;IAEF;IACA,MAAMiG,GAAG,GAAGC,yBAAyB,CAACN,sBAAsB,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC;IAEvE,IAAIC,GAAG,IAAIA,GAAG,CAACjG,QAAQ,IAAIiG,GAAG,CAACjG,QAAQ,CAAC3N,IAAI,KAAK,cAAc,EAAE;MAC/D,MAAMvF,OAAO,GAAGmZ,GAAG,CAACjG,QAAQ,CAAClT,OAAO;MACpCpE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEmE,OAAO,CAAC;;MAEzC;MACAhG,MAAM,CAACqf,qBAAqB,CAACrZ,OAAO,CAAC;MACrC;IACF;EACF;;EAEA;EACA,MAAMsZ,UAAU,GAAGnB,SAAS,CAACY,gBAAgB,CAACrB,aAAa,CAACnJ,QAAQ,EAAE,IAAI,CAAC;EAE3E3S,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEyd,UAAU,CAAC9K,MAAM,CAAC;EAE7C,IAAI8K,UAAU,CAAC9K,MAAM,GAAG,CAAC,EAAE;IACzB;IACA8K,UAAU,CAACnU,OAAO,CAAC,CAAC6T,SAAS,EAAEC,KAAK,KAAK;MACvC,MAAME,GAAG,GAAGH,SAAS,CAACE,MAAM;MAC5Btd,OAAO,CAACC,GAAG,CAAC,UAAUod,KAAK,GAAG,EAAEE,GAAG,CAACjW,IAAI,IAAI,KAAK,EACrC,WAAW,EAAEiW,GAAG,CAACjG,QAAQ,EACzB,KAAK,EAAE8F,SAAS,CAAChM,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACA,KAAK,IAAI/J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqW,UAAU,CAAC9K,MAAM,EAAEvL,CAAC,EAAE,EAAE;MAC1C,MAAMkW,GAAG,GAAGC,yBAAyB,CAACE,UAAU,CAACrW,CAAC,CAAC,CAACiW,MAAM,CAAC;MAC3D,IAAIC,GAAG,IAAIA,GAAG,CAACjG,QAAQ,IAAIiG,GAAG,CAACjG,QAAQ,CAAC3N,IAAI,KAAK,cAAc,EAAE;QAC/D,MAAMvF,OAAO,GAAGmZ,GAAG,CAACjG,QAAQ,CAAClT,OAAO;QACpCpE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEmE,OAAO,CAAC;;QAEvC;QACAhG,MAAM,CAACqf,qBAAqB,CAACrZ,OAAO,CAAC;QACrC;MACF;IACF;EACF;;EAEA;EACApE,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;;EAE9B;EACA,IAAI0d,YAAY,GAAG,IAAI;EACvB,IAAIjX,WAAW,GAAG,GAAG,CAAC,CAAC;;EAEvBzH,gBAAgB,CAACsK,OAAO,CAAC,CAACkM,QAAQ,EAAErR,OAAO,KAAK;IAC9C,IAAIqR,QAAQ,CAACrL,KAAK,EAAE;MAClB,MAAMwT,QAAQ,GAAG,IAAIxhB,KAAK,CAACqP,OAAO,CAAC,CAAC;MACpC;MACAgK,QAAQ,CAACrL,KAAK,CAACyT,gBAAgB,CAACD,QAAQ,CAAC;;MAEzC;MACA,MAAME,SAAS,GAAGF,QAAQ,CAACnd,KAAK,CAAC,CAAC;MAClCqd,SAAS,CAACC,OAAO,CAAChC,cAAc,CAAC;;MAEjC;MACA,MAAMiC,EAAE,GAAGF,SAAS,CAACnd,CAAC,GAAGwb,MAAM;MAC/B,MAAM8B,EAAE,GAAGH,SAAS,CAACjd,CAAC,GAAGwb,MAAM;MAC/B,MAAMjL,QAAQ,GAAGhQ,IAAI,CAAC8c,IAAI,CAACF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;MAE7Cje,OAAO,CAACC,GAAG,CAAC,MAAMmE,OAAO,OAAO,EAAEgN,QAAQ,CAAC;MAE3C,IAAIA,QAAQ,GAAG1K,WAAW,EAAE;QAC1BA,WAAW,GAAG0K,QAAQ;QACtBuM,YAAY,GAAG;UAAEvZ,OAAO;UAAEgN;QAAS,CAAC;MACtC;IACF;EACF,CAAC,CAAC;EAEF,IAAIuM,YAAY,EAAE;IAChB3d,OAAO,CAACC,GAAG,CAAC,oBAAoB0d,YAAY,CAACvZ,OAAO,SAASuZ,YAAY,CAACvM,QAAQ,EAAE,CAAC;;IAErF;IACAhT,MAAM,CAACqf,qBAAqB,CAACE,YAAY,CAACvZ,OAAO,CAAC;IAClD;EACF;EAEApE,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;;EAE1B;EACA,IAAI+b,eAAe,EAAE;IACnBA,eAAe,CAAClP,IAAI,IAAI;MACtB,IAAIA,IAAI,CAAC3I,OAAO,EAAE;QAChB,OAAO;UAAE,GAAG2I,IAAI;UAAE3I,OAAO,EAAE;QAAM,CAAC;MACpC;MACA,OAAO2I,IAAI;IACb,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAM4L,kBAAkB,GAAIsD,eAAe,IAAK;EAC9CA,eAAe,CAAClP,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAE3I,OAAO,EAAE;EAAM,CAAC,CAAC,CAAC;AACxD,CAAC;;AAED;AACA/F,MAAM,CAAC+f,qBAAqB,GAAI/Z,OAAO,IAAK;EAC1C,IAAI;IAAA,IAAAga,qBAAA,EAAAC,sBAAA;IACF;IACA,MAAM9R,YAAY,GAAGtN,gBAAgB,CAAC4J,GAAG,CAACzE,OAAO,IAAI,GAAG,CAAC;IACzD,IAAI,CAACmI,YAAY,EAAE;MACjBvM,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEkE,OAAO,CAAC;;MAEtC;MACApE,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxBhB,gBAAgB,CAACsK,OAAO,CAAC,CAAC+U,KAAK,EAAE7U,EAAE,KAAK;QACtCzJ,OAAO,CAACC,GAAG,CAAC,KAAKwJ,EAAE,KAAK6U,KAAK,CAACnX,YAAY,CAACG,IAAI,EAAE,CAAC;MACpD,CAAC,CAAC;MAEF,OAAO,KAAK;IACd;;IAEA;IACA,MAAMiX,UAAU,GAAGhS,YAAY,CAACnC,KAAK;;IAErC;IACA,MAAMoU,SAAS,GAAGtf,kBAAkB,CAAC2J,GAAG,CAACzE,OAAO,CAAC;IACjD,MAAM+C,YAAY,GAAGoF,YAAY,CAACpF,YAAY;;IAE9C;IACA,IAAI9C,OAAO;IAEX,IAAIma,SAAS,IAAIA,SAAS,CAACla,MAAM,EAAE;MACjCD,OAAO,gBACLvH,OAAA;QAAK4a,KAAK,EAAE;UAAEpU,OAAO,EAAE,MAAM;UAAEoB,KAAK,EAAE,OAAO;UAAE2T,SAAS,EAAE,OAAO;UAAEoG,SAAS,EAAE;QAAO,CAAE;QAAA9L,QAAA,gBACrF7V,OAAA;UAAK4a,KAAK,EAAE;YACV5S,UAAU,EAAE,MAAM;YAClB4Z,YAAY,EAAE,MAAM;YACpB/a,QAAQ,EAAE,MAAM;YAChBgb,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAAjM,QAAA,GACCxL,YAAY,CAACG,IAAI,EAAC,QAAM,EAAClD,OAAO,EAAC,GACpC;QAAA;UAAAuT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhb,OAAA;UAAA6V,QAAA,EACG6L,SAAS,CAACla,MAAM,CAACuG,GAAG,CAAC,CAACkB,KAAK,EAAEsR,KAAK,KAAK;YACtC,IAAIwB,UAAU;YACd,QAAQ9S,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBAAEsS,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;gBAAEA,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;cAAE;gBAASA,UAAU,GAAG,SAAS;gBAAE;YAC7C;YAEA,oBACE/hB,OAAA;cAAiB4a,KAAK,EAAE;gBACtBgH,YAAY,EAAE,KAAK;gBACnBnb,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE;cAChB,CAAE;cAAAkP,QAAA,gBACA7V,OAAA;gBAAK4a,KAAK,EAAE;kBAAE5S,UAAU,EAAE;gBAAO,CAAE;gBAAA6N,QAAA,EAChCtG,iBAAiB,CAACN,KAAK,CAACC,OAAO;cAAC;gBAAA2L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNhb,OAAA;gBAAK4a,KAAK,EAAE;kBAAEvU,OAAO,EAAE,MAAM;kBAAE2b,cAAc,EAAE;gBAAgB,CAAE;gBAAAnM,QAAA,gBAC/D7V,OAAA;kBAAA6V,QAAA,EAAM;gBAAI;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBhb,OAAA;kBAAM4a,KAAK,EAAE;oBACX7S,KAAK,EAAEga,UAAU;oBACjB/Z,UAAU,EAAE,MAAM;oBAClBvB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAAkP,QAAA,EACC5G,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGR,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAG;gBAAI;kBAAAoL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNhb,OAAA;gBAAK4a,KAAK,EAAE;kBAAEvU,OAAO,EAAE,MAAM;kBAAE2b,cAAc,EAAE;gBAAgB,CAAE;gBAAAnM,QAAA,gBAC/D7V,OAAA;kBAAA6V,QAAA,EAAM;gBAAK;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBhb,OAAA;kBAAM4a,KAAK,EAAE;oBAAE5S,UAAU,EAAE;kBAAO,CAAE;kBAAA6N,QAAA,GAAE5G,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAAmL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAxBEuF,KAAK;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACLzT,OAAO,gBACLvH,OAAA;QAAK4a,KAAK,EAAE;UAAEpU,OAAO,EAAE,KAAK;UAAEyb,QAAQ,EAAE;QAAQ,CAAE;QAAApM,QAAA,gBAChD7V,OAAA;UAAK4a,KAAK,EAAE;YAAE5S,UAAU,EAAE,MAAM;YAAE4Z,YAAY,EAAE;UAAM,CAAE;UAAA/L,QAAA,EAAExL,YAAY,CAACG;QAAI;UAAAqQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClFhb,OAAA;UAAA6V,QAAA,GAAK,kBAAM,EAACvO,OAAO;QAAA;UAAAuT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1Bhb,OAAA;UAAA6V,QAAA,EAAK;QAAU;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN;IACH;;IAEA;IACA,MAAMkH,OAAO,GAAG5gB,MAAM,CAAC2R,UAAU,GAAG,CAAC;IACrC,MAAMkP,OAAO,GAAG7gB,MAAM,CAAC4R,WAAW,GAAG,CAAC;;IAEtC;IACA,MAAMgM,eAAe,IAAAoC,qBAAA,GAAGrF,QAAQ,CAACe,aAAa,CAAC,OAAO,CAAC,cAAAsE,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCc,gBAAgB,cAAAb,sBAAA,uBAAjDA,sBAAA,CAAmDna,sBAAsB;IAEjG,IAAI8X,eAAe,EAAE;MACnB;MACAA,eAAe,CAAC;QACd7X,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBtB,QAAQ,EAAE;UAAEnC,CAAC,EAAEqe,OAAO;UAAEne,CAAC,EAAEoe;QAAQ,CAAC;QACpC5a,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAAka,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEla,MAAM,KAAI;MAC/B,CAAC,CAAC;MAEFtE,OAAO,CAACC,GAAG,CAAC,SAASkH,YAAY,CAACG,IAAI,KAAKlD,OAAO,UAAU,CAAC;MAC7D,OAAO,IAAI;IACb,CAAC,MAAM;MACL;MACA,MAAM+a,OAAO,GAAGpG,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CmG,OAAO,CAACzH,KAAK,CAAC5U,QAAQ,GAAG,UAAU;MACnCqc,OAAO,CAACzH,KAAK,CAAC1U,IAAI,GAAG,GAAGgc,OAAO,IAAI;MACnCG,OAAO,CAACzH,KAAK,CAACjT,GAAG,GAAG,GAAGwa,OAAO,IAAI;MAClCE,OAAO,CAACzH,KAAK,CAACzU,SAAS,GAAG,wBAAwB;MAClDkc,OAAO,CAACzH,KAAK,CAACxU,MAAM,GAAG,MAAM;MAC7Bic,OAAO,CAACzH,KAAK,CAACnU,eAAe,GAAG,qBAAqB;MACrD4b,OAAO,CAACzH,KAAK,CAAC7S,KAAK,GAAG,OAAO;MAC7Bsa,OAAO,CAACzH,KAAK,CAACjU,YAAY,GAAG,KAAK;MAClC0b,OAAO,CAACzH,KAAK,CAAC9T,SAAS,GAAG,8BAA8B;MACxDub,OAAO,CAACzH,KAAK,CAACpU,OAAO,GAAG,MAAM;MAC9B6b,OAAO,CAACzH,KAAK,CAACqH,QAAQ,GAAG,OAAO;MAEhCI,OAAO,CAACC,SAAS,GAAG;AAC1B;AACA,YAAYjY,YAAY,CAACG,IAAI,SAASlD,OAAO;AAC7C;AACA;AACA,qBAAqBA,OAAO;AAC5B,eAAeoa,SAAS,GAAG,UAAU,GAAG,YAAY;AACpD;AACA;AACA,OAAO;MAEDzF,QAAQ,CAACsG,IAAI,CAAC7O,WAAW,CAAC2O,OAAO,CAAC;;MAElC;MACA,MAAMG,WAAW,GAAGH,OAAO,CAACrF,aAAa,CAAC,QAAQ,CAAC;MACnD,IAAIwF,WAAW,EAAE;QACfA,WAAW,CAACtK,gBAAgB,CAAC,OAAO,EAAE,MAAM;UAC1C+D,QAAQ,CAACsG,IAAI,CAAC/J,WAAW,CAAC6J,OAAO,CAAC;QACpC,CAAC,CAAC;MACJ;MAEAnf,OAAO,CAACC,GAAG,CAAC,gBAAgBkH,YAAY,CAACG,IAAI,KAAKlD,OAAO,OAAO,CAAC;MACjE,OAAO,IAAI;IACb;EACF,CAAC,CAAC,OAAOlE,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA9B,MAAM,CAACmhB,iBAAiB,GAAG,MAAM;EAC/Bvf,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;EAErB,IAAI,CAAChB,gBAAgB,IAAIA,gBAAgB,CAACuY,IAAI,KAAK,CAAC,EAAE;IACpDxX,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,OAAO,EAAE;EACX;EAEA,MAAMuf,IAAI,GAAG,EAAE;EACfvgB,gBAAgB,CAACsK,OAAO,CAAC,CAAC+U,KAAK,EAAE7U,EAAE,KAAK;IACtCzJ,OAAO,CAACC,GAAG,CAAC,SAASwJ,EAAE,SAAS6U,KAAK,CAACnX,YAAY,CAACG,IAAI,EAAE,CAAC;IAC1DkY,IAAI,CAACvC,IAAI,CAAC;MACRxT,EAAE;MACFnC,IAAI,EAAEgX,KAAK,CAACnX,YAAY,CAACG,IAAI;MAC7BxE,QAAQ,EAAEwb,KAAK,CAACxb;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAO0c,IAAI;AACb,CAAC;;AAED;AACAphB,MAAM,CAACqZ,UAAU,GAAG,YAAW;EAC7B,IAAI,CAAC3Z,KAAK,EAAE;IACVkC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAC;IAC/B;EACF;EAEAF,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;;EAE3B;EACAD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEhB,gBAAgB,CAACuY,IAAI,CAAC;;EAE9C;EACA,IAAIiI,kBAAkB,GAAG,CAAC;EAC1B,IAAIzC,mBAAmB,GAAG,CAAC;;EAE3B;EACAlf,KAAK,CAACoU,QAAQ,CAAEoL,MAAM,IAAK;IACzB;IACA,IAAIA,MAAM,CAAChG,QAAQ,IAAIoI,MAAM,CAACC,IAAI,CAACrC,MAAM,CAAChG,QAAQ,CAAC,CAAC1E,MAAM,GAAG,CAAC,EAAE;MAC9D6M,kBAAkB,EAAE;;MAEpB;MACA,IAAInC,MAAM,CAAChG,QAAQ,CAAC3N,IAAI,KAAK,cAAc,EAAE;QAC3CqT,mBAAmB,EAAE;QACrBhd,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;UACtB2f,EAAE,EAAEtC,MAAM,CAAChW,IAAI,IAAI,KAAK;UACxBuY,EAAE,EAAEvC,MAAM,CAAChG,QAAQ,CAAC3N,IAAI;UACxBmW,IAAI,EAAExC,MAAM,CAAChG,QAAQ,CAAClT,OAAO;UAC7BkK,EAAE,EAAEgP,MAAM,CAACxa,QAAQ,CAACkF,OAAO,CAAC,CAAC;UAC7B+X,GAAG,EAAEzC,MAAM,CAACnZ,OAAO;UACnB6b,KAAK,EAAE1C,MAAM,CAAClL,MAAM;UACpBkF,QAAQ,EAAEgG,MAAM,CAAChG;QACnB,CAAC,CAAC;MACJ;IACF;EACF,CAAC,CAAC;EAEFtX,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEwf,kBAAkB,CAAC;EAC/Czf,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE+c,mBAAmB,CAAC;;EAE5C;EACA/d,gBAAgB,CAACsK,OAAO,CAAC,CAACkM,QAAQ,EAAErR,OAAO,KAAK;IAC9C,IAAIqR,QAAQ,CAACrL,KAAK,EAAE;MAClB;MACA,MAAMtH,QAAQ,GAAG2S,QAAQ,CAACrL,KAAK,CAACtH,QAAQ,CAACrC,KAAK,CAAC,CAAC;;MAEhD;MACA,MAAMwf,iBAAiB,GAAG,IAAI7jB,KAAK,CAAC4a,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;MAC7D,MAAMkJ,iBAAiB,GAAG,IAAI9jB,KAAK,CAACoa,iBAAiB,CAAC;QACpD3R,KAAK,EAAE,QAAQ;QACfqS,WAAW,EAAE,IAAI;QACjBC,OAAO,EAAE;MACX,CAAC,CAAC;MACF,MAAMgJ,aAAa,GAAG,IAAI/jB,KAAK,CAACqa,IAAI,CAACwJ,iBAAiB,EAAEC,iBAAiB,CAAC;;MAE1E;MACAC,aAAa,CAACrd,QAAQ,CAAC9B,GAAG,CAAC8B,QAAQ,CAACnC,CAAC,EAAEmC,QAAQ,CAACjC,CAAC,GAAG,EAAE,EAAEiC,QAAQ,CAAC/B,CAAC,CAAC;;MAEnE;MACAjD,KAAK,CAAC0M,GAAG,CAAC2V,aAAa,CAAC;;MAExB;MACAA,aAAa,CAAC7I,QAAQ,GAAG;QACvB3N,IAAI,EAAE,uBAAuB;QAC7BvF,OAAO,EAAEA,OAAO;QAChBkD,IAAI,EAAEmO,QAAQ,CAACtO,YAAY,CAACG,IAAI;QAChC8Y,gBAAgB,EAAEtd,QAAQ,CAACkF,OAAO,CAAC;MACrC,CAAC;;MAED;MACA0H,UAAU,CAAC,MAAM;QACf5R,KAAK,CAACmN,MAAM,CAACkV,aAAa,CAAC;MAC7B,CAAC,EAAE,IAAI,CAAC;MAERngB,OAAO,CAACC,GAAG,CAAC,QAAQwV,QAAQ,CAACtO,YAAY,CAACG,IAAI,KAAKlD,OAAO,cAAc,CAAC;IAC3E;EACF,CAAC,CAAC;;EAEF;EACApE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;IACtBogB,OAAO,EAAEphB,gBAAgB,CAACuY,IAAI;IAC9B8I,OAAO,EAAEphB,kBAAkB,CAACsY,IAAI;IAChC+I,UAAU,EAAEvD,mBAAmB;IAC/BwD,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;;AAED;AACApiB,MAAM,CAACqf,qBAAqB,GAAIrZ,OAAO,IAAK;EAC1C,IAAI;IACFpE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEmE,OAAO,CAAC;IACxDpE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEhB,gBAAgB,CAACuY,IAAI,CAAC;IAC3DxX,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEf,kBAAkB,CAACsY,IAAI,CAAC;;IAE/D;IACA,IAAI,CAACpT,OAAO,IAAInF,gBAAgB,CAACuY,IAAI,GAAG,CAAC,EAAE;MACzCpT,OAAO,GAAG3E,KAAK,CAACghB,IAAI,CAACxhB,gBAAgB,CAAC0gB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD3f,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEmE,OAAO,CAAC;IAC3C;;IAEA;IACA,MAAMmI,YAAY,GAAGtN,gBAAgB,CAAC4J,GAAG,CAACzE,OAAO,CAAC;IAClD,IAAI,CAACmI,YAAY,EAAE;MACjBvM,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEkE,OAAO,CAAC;;MAEtC;MACApE,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxBhB,gBAAgB,CAACsK,OAAO,CAAC,CAAC+U,KAAK,EAAE7U,EAAE,KAAK;QAAA,IAAAiX,mBAAA;QACtC1gB,OAAO,CAACC,GAAG,CAAC,KAAKwJ,EAAE,KAAK,EAAAiX,mBAAA,GAAApC,KAAK,CAACnX,YAAY,cAAAuZ,mBAAA,uBAAlBA,mBAAA,CAAoBpZ,IAAI,KAAI,IAAI,EAAE,CAAC;MAC7D,CAAC,CAAC;MAEF,OAAO,KAAK;IACd;;IAEA;IACA,MAAMkX,SAAS,GAAGtf,kBAAkB,CAAC2J,GAAG,CAACzE,OAAO,CAAC;IACjDpE,OAAO,CAACC,GAAG,CAAC,QAAQmE,OAAO,SAAS,EAAEoa,SAAS,CAAC;IAEhD,MAAMrX,YAAY,GAAGoF,YAAY,CAACpF,YAAY;IAC9CnH,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEkH,YAAY,CAAC;;IAElC;IACA,IAAI9C,OAAO;IAEX,IAAIma,SAAS,IAAIA,SAAS,CAACla,MAAM,IAAIka,SAAS,CAACla,MAAM,CAACsO,MAAM,GAAG,CAAC,EAAE;MAChE;MACA5S,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtBue,SAAS,CAACla,MAAM,CAACiF,OAAO,CAAC,CAACwC,KAAK,EAAEsR,KAAK,KAAK;QACzCrd,OAAO,CAACC,GAAG,CAAC,MAAMod,KAAK,GAAC,CAAC,QAAQtR,KAAK,CAACC,OAAO,QAAQD,KAAK,CAACQ,YAAY,UAAUR,KAAK,CAACS,UAAU,EAAE,CAAC;MACvG,CAAC,CAAC;MAEFnI,OAAO,gBACLvH,OAAA;QAAK4a,KAAK,EAAE;UAAEpU,OAAO,EAAE,MAAM;UAAEoB,KAAK,EAAE,OAAO;UAAE2T,SAAS,EAAE,OAAO;UAAEoG,SAAS,EAAE;QAAO,CAAE;QAAA9L,QAAA,gBACrF7V,OAAA;UAAK4a,KAAK,EAAE;YACV5S,UAAU,EAAE,MAAM;YAClB4Z,YAAY,EAAE,MAAM;YACpB/a,QAAQ,EAAE,MAAM;YAChBgb,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAAjM,QAAA,GACC,CAAAxL,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAAClD,OAAO,EAAC,GAC/C;QAAA;UAAAuT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhb,OAAA;UAAA6V,QAAA,EACG6L,SAAS,CAACla,MAAM,CAACuG,GAAG,CAAC,CAACkB,KAAK,EAAEsR,KAAK,KAAK;YACtC,IAAIwB,UAAU;YACd,IAAI8B,SAAS;YAEb,QAAQ5U,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBACNsS,UAAU,GAAG,SAAS;gBACtB8B,SAAS,GAAG,IAAI;gBAChB;cACF,KAAK,GAAG;gBACN9B,UAAU,GAAG,SAAS;gBACtB8B,SAAS,GAAG,IAAI;gBAChB;cACF,KAAK,GAAG;cACR;gBACE9B,UAAU,GAAG,SAAS;gBACtB8B,SAAS,GAAG,IAAI;gBAChB;YACJ;YAEA,MAAMzU,SAAS,GAAGG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC;YAElD,oBACElP,OAAA;cAAiB4a,KAAK,EAAE;gBACtBgH,YAAY,EAAE,KAAK;gBACnBnb,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE;cAChB,CAAE;cAAAkP,QAAA,gBACA7V,OAAA;gBAAK4a,KAAK,EAAE;kBAAE5S,UAAU,EAAE;gBAAO,CAAE;gBAAA6N,QAAA,GAChCzG,SAAS,EAAC,oBAAQ,EAACH,KAAK,CAACC,OAAO,EAAC,GACpC;cAAA;gBAAA2L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNhb,OAAA;gBAAK4a,KAAK,EAAE;kBAAEvU,OAAO,EAAE,MAAM;kBAAE2b,cAAc,EAAE;gBAAgB,CAAE;gBAAAnM,QAAA,gBAC/D7V,OAAA;kBAAA6V,QAAA,EAAM;gBAAI;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBhb,OAAA;kBAAM4a,KAAK,EAAE;oBACX7S,KAAK,EAAEga,UAAU;oBACjB/Z,UAAU,EAAE,MAAM;oBAClBvB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAAkP,QAAA,EACCgO;gBAAS;kBAAAhJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNhb,OAAA;gBAAK4a,KAAK,EAAE;kBAAEvU,OAAO,EAAE,MAAM;kBAAE2b,cAAc,EAAE;gBAAgB,CAAE;gBAAAnM,QAAA,gBAC/D7V,OAAA;kBAAA6V,QAAA,EAAM;gBAAK;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBhb,OAAA;kBAAM4a,KAAK,EAAE;oBAAE5S,UAAU,EAAE;kBAAO,CAAE;kBAAA6N,QAAA,GAAE5G,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAAmL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAxBEuF,KAAK;cAAA1F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNhb,OAAA;UAAK4a,KAAK,EAAE;YAAEkJ,SAAS,EAAE,MAAM;YAAEjd,QAAQ,EAAE,MAAM;YAAEkB,KAAK,EAAE;UAAO,CAAE;UAAA8N,QAAA,GAAC,4BAC5D,EAAC,IAAIrJ,IAAI,CAACkV,SAAS,CAACqC,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC;QAAA;UAAAnJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACL;MACAzT,OAAO,gBACLvH,OAAA;QAAK4a,KAAK,EAAE;UAAEpU,OAAO,EAAE,MAAM;UAAEoB,KAAK,EAAE;QAAQ,CAAE;QAAAiO,QAAA,gBAC9C7V,OAAA;UAAK4a,KAAK,EAAE;YAAE5S,UAAU,EAAE,MAAM;YAAE4Z,YAAY,EAAE;UAAO,CAAE;UAAA/L,QAAA,GACtD,CAAAxL,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAAClD,OAAO,EAAC,GAC/C;QAAA;UAAAuT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhb,OAAA;UAAK4a,KAAK,EAAE;YAAE7S,KAAK,EAAE;UAAU,CAAE;UAAA8N,QAAA,EAAC;QAElC;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNhb,OAAA;UAAK4a,KAAK,EAAE;YAAEkJ,SAAS,EAAE;UAAO,CAAE;UAAAjO,QAAA,eAChC7V,OAAA;YACE2b,OAAO,EAAEA,CAAA,KAAMra,MAAM,CAAC2iB,4BAA4B,CAAC3c,OAAO,CAAE;YAC5DsT,KAAK,EAAE;cACLc,UAAU,EAAE,SAAS;cACrB3T,KAAK,EAAE,OAAO;cACdrB,MAAM,EAAE,MAAM;cACdF,OAAO,EAAE,UAAU;cACnBG,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE;YACV,CAAE;YAAAiP,QAAA,EACH;UAED;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNhb,OAAA;UAAK4a,KAAK,EAAE;YAAEkJ,SAAS,EAAE,MAAM;YAAEjd,QAAQ,EAAE,MAAM;YAAEkB,KAAK,EAAE;UAAO,CAAE;UAAA8N,QAAA,GAAC,4BAC5D,EAAC,IAAIrJ,IAAI,CAAC,CAAC,CAACwX,kBAAkB,CAAC,CAAC;QAAA;UAAAnJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH;;IAEA;IACA,MAAMnX,CAAC,GAAGvC,MAAM,CAAC2R,UAAU,GAAG,CAAC;IAC/B,MAAMlP,CAAC,GAAGzC,MAAM,CAAC4R,WAAW,GAAG,CAAC;;IAEhC;IACA,IAAI5R,MAAM,CAACmG,uBAAuB,EAAE;MAClCvE,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;QAC7CkE,OAAO,EAAE,IAAI;QACbC,OAAO;QACPtB,QAAQ,EAAE;UAAEnC,CAAC;UAAEE;QAAE;MACnB,CAAC,CAAC;MAEFzC,MAAM,CAACmG,uBAAuB,CAAC;QAC7BJ,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBtB,QAAQ,EAAE;UAAEnC,CAAC;UAAEE;QAAE,CAAC;QAClBwD,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAAka,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEla,MAAM,KAAI;MAC/B,CAAC,CAAC;MAEFtE,OAAO,CAACC,GAAG,CAAC,SAAS,CAAAkH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAIlD,OAAO,WAAW,CAAC;MAC9D,OAAO,IAAI;IACb,CAAC,MAAM;MACLpE,OAAO,CAACE,KAAK,CAAC,8BAA8B,CAAC;MAC7C,OAAO,KAAK;IACd;EACF,CAAC,CAAC,OAAOA,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA9B,MAAM,CAAC4iB,oBAAoB,GAAG,MAAM;EAClC,IAAI,CAAC/hB,gBAAgB,IAAIA,gBAAgB,CAACuY,IAAI,KAAK,CAAC,EAAE;IACpDxX,OAAO,CAACE,KAAK,CAAC,kBAAkB,CAAC;IACjC,OAAO,KAAK;EACd;EAEAF,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;;EAE9B;EACA,MAAMghB,QAAQ,GAAG,CACf,GAAG,EAAE,GAAG,EAAE,GAAG;EAAG;EAChB,GAAG,EAAE,GAAG,EAAE,GAAG;EAAG;EAChB,GAAG,EAAE,IAAI,EAAE,IAAI;EAAE;EACjB,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAAA,CAClB;;EAED;EACA,MAAMC,WAAW,GAAG;IAClB5hB,IAAI,EAAE;MACJ8H,aAAa,EAAE;IACjB,CAAC;IACDuB,EAAE,EAAEW,IAAI,CAACD,GAAG,CAAC,CAAC;IACd8X,MAAM,EAAE,CAAC;IACTxX,IAAI,EAAE,MAAM;IACZlB,GAAG,EAAE;EACP,CAAC;;EAED;EACAxJ,gBAAgB,CAACsK,OAAO,CAAC,CAAC+U,KAAK,EAAEla,OAAO,KAAK;IAAA,IAAAgd,oBAAA;IAC3C;IACA,MAAMC,UAAU,GAAGjgB,IAAI,CAACkgB,KAAK,CAAClgB,IAAI,CAACmgB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IACpD,MAAMjd,MAAM,GAAG,EAAE;;IAEjB;IACA,MAAMkd,gBAAgB,GAAG,EAAE;IAC3B,OAAOA,gBAAgB,CAAC5O,MAAM,GAAGyO,UAAU,IAAIG,gBAAgB,CAAC5O,MAAM,GAAGqO,QAAQ,CAACrO,MAAM,EAAE;MACxF,MAAM6O,WAAW,GAAGrgB,IAAI,CAACkgB,KAAK,CAAClgB,IAAI,CAACmgB,MAAM,CAAC,CAAC,GAAGN,QAAQ,CAACrO,MAAM,CAAC;MAC/D,MAAM5G,OAAO,GAAGiV,QAAQ,CAACQ,WAAW,CAAC;;MAErC;MACA,IAAI,CAACD,gBAAgB,CAACE,QAAQ,CAAC1V,OAAO,CAAC,EAAE;QACvCwV,gBAAgB,CAACvE,IAAI,CAACjR,OAAO,CAAC;MAChC;IACF;;IAEA;IACAwV,gBAAgB,CAACjY,OAAO,CAACyC,OAAO,IAAI;MAClC;MACA,MAAM2V,UAAU,GAAGvgB,IAAI,CAACkgB,KAAK,CAAClgB,IAAI,CAACmgB,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;MAChD,MAAMK,WAAW,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;;MAE9C;MACA,MAAMpV,UAAU,GAAGpL,IAAI,CAACkgB,KAAK,CAAClgB,IAAI,CAACmgB,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;;MAErD;MACAjd,MAAM,CAAC2Y,IAAI,CAAC;QACVjR,OAAO,EAAEA,OAAO;QAChBnC,KAAK,EAAE+X,WAAW,CAACD,UAAU,CAAC;QAC9BnV,UAAU,EAAEA;MACd,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA0U,WAAW,CAAC5hB,IAAI,CAAC8H,aAAa,CAAC6V,IAAI,CAAC;MAClCxT,EAAE,EAAErF,OAAO;MACXE,MAAM,EAAEA;IACV,CAAC,CAAC;;IAEF;IACA,MAAMud,UAAU,GAAGvd,MAAM,CAACuG,GAAG,CAACkB,KAAK,KAAK;MACtCC,OAAO,EAAED,KAAK,CAACC,OAAO;MACtBO,YAAY,EAAER,KAAK,CAAClC,KAAK,KAAK,OAAO,GAAG,GAAG,GAAGkC,KAAK,CAAClC,KAAK,KAAK,QAAQ,GAAG,GAAG,GAAG,GAAG;MAClF2C,UAAU,EAAET,KAAK,CAACS,UAAU;MAC5B/B,UAAU,EAAEnB,IAAI,CAACD,GAAG,CAAC;IACvB,CAAC,CAAC,CAAC;IAEHnK,kBAAkB,CAAC8B,GAAG,CAACoD,OAAO,EAAE;MAC9Byc,UAAU,EAAEvX,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB/E,MAAM,EAAEud;IACV,CAAC,CAAC;IAEF7hB,OAAO,CAACC,GAAG,CAAC,OAAO,EAAAmhB,oBAAA,GAAA9C,KAAK,CAACnX,YAAY,cAAAia,oBAAA,uBAAlBA,oBAAA,CAAoB9Z,IAAI,KAAIlD,OAAO,QAAQE,MAAM,CAACsO,MAAM,WAAW,CAAC;EACzF,CAAC,CAAC;;EAEF;EACA,IAAI;IACF5S,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEihB,WAAW,CAAC;IACvC,MAAM/Y,OAAO,GAAGE,IAAI,CAACkH,SAAS,CAAC2R,WAAW,CAAC;IAC3C;IACAhZ,iBAAiB,CAAChK,WAAW,CAACS,IAAI,EAAEwJ,OAAO,CAAC;EAC9C,CAAC,CAAC,OAAOjI,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;EACvC;EAEAF,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;EAC3B,OAAO,IAAI;AACb,CAAC;;AAED;AACA7B,MAAM,CAAC2iB,4BAA4B,GAAI3c,OAAO,IAAK;EACjD;EACAhG,MAAM,CAAC4iB,oBAAoB,CAAC,CAAC;;EAE7B;EACAtR,UAAU,CAAC,MAAM;IACf;IACAtR,MAAM,CAACqf,qBAAqB,CAACrZ,OAAO,CAAC;EACvC,CAAC,EAAE,GAAG,CAAC;EAEP,OAAO,sBAAsB;AAC/B,CAAC;;AAED;AACA,MAAMoZ,yBAAyB,GAAIF,MAAM,IAAK;EAC5C,IAAI7X,OAAO,GAAG6X,MAAM;;EAEpB;EACA,IAAI7X,OAAO,IAAIA,OAAO,CAAC6R,QAAQ,IAAI7R,OAAO,CAAC6R,QAAQ,CAAC3N,IAAI,KAAK,cAAc,EAAE;IAC3E3J,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEwF,OAAO,CAAC6B,IAAI,IAAI,KAAK,CAAC;IAChD,OAAO7B,OAAO;EAChB;;EAEA;EACA,OAAOA,OAAO,IAAIA,OAAO,CAACqc,MAAM,EAAE;IAChCrc,OAAO,GAAGA,OAAO,CAACqc,MAAM;IACxB,IAAIrc,OAAO,CAAC6R,QAAQ,IAAI7R,OAAO,CAAC6R,QAAQ,CAAC3N,IAAI,KAAK,cAAc,EAAE;MAChE3J,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEwF,OAAO,CAAC6B,IAAI,IAAI,KAAK,CAAC;MAChD,OAAO7B,OAAO;IAChB;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACArH,MAAM,CAAC2jB,kBAAkB,GAAG,CAACphB,CAAC,EAAEE,CAAC,KAAK;EACpC,IAAI;IACFb,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEU,CAAC,EAAEE,CAAC,CAAC;;IAEnC;IACA,MAAMiY,MAAM,GAAGC,QAAQ,CAACe,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAI,CAAChB,MAAM,EAAE;MACX9Y,OAAO,CAACE,KAAK,CAAC,sBAAsB,CAAC;MACrC,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAACpC,KAAK,IAAI,CAACgG,SAAS,CAAC2B,OAAO,EAAE;MAChCzF,OAAO,CAACE,KAAK,CAAC,iBAAiB,CAAC;MAChC,OAAO,KAAK;IACd;;IAEA;IACA,IAAIS,CAAC,KAAKqhB,SAAS,IAAInhB,CAAC,KAAKmhB,SAAS,EAAE;MACtCrhB,CAAC,GAAGvC,MAAM,CAAC2R,UAAU,GAAG,CAAC;MACzBlP,CAAC,GAAGzC,MAAM,CAAC4R,WAAW,GAAG,CAAC;IAC5B;;IAEA;IACA,MAAMiM,IAAI,GAAGnD,MAAM,CAACoD,qBAAqB,CAAC,CAAC;IAC3C,MAAMC,MAAM,GAAI,CAACxb,CAAC,GAAGsb,IAAI,CAACjZ,IAAI,IAAI8V,MAAM,CAACsD,WAAW,GAAI,CAAC,GAAG,CAAC;IAC7D,MAAMC,MAAM,GAAG,EAAE,CAACxb,CAAC,GAAGob,IAAI,CAACxX,GAAG,IAAIqU,MAAM,CAACwD,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;IAE9Dtc,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEkc,MAAM,EAAEE,MAAM,CAAC;;IAErC;IACA,MAAME,SAAS,GAAG,IAAIngB,KAAK,CAACogB,SAAS,CAAC,CAAC;IACvCD,SAAS,CAACE,MAAM,CAACC,MAAM,CAACC,SAAS,GAAG,CAAC;IACrCJ,SAAS,CAACE,MAAM,CAACG,IAAI,CAACD,SAAS,GAAG,CAAC;IAEnC,MAAME,WAAW,GAAG,IAAIzgB,KAAK,CAAC0gB,OAAO,CAACX,MAAM,EAAEE,MAAM,CAAC;IACrDE,SAAS,CAACQ,aAAa,CAACF,WAAW,EAAE/Y,SAAS,CAAC2B,OAAO,CAAC;;IAEvD;IACA,MAAMuX,mBAAmB,GAAG,EAAE;IAC9B/d,gBAAgB,CAACsK,OAAO,CAAC,CAACkM,QAAQ,EAAErR,OAAO,KAAK;MAC9C,IAAIqR,QAAQ,CAACrL,KAAK,EAAE;QAClB4S,mBAAmB,CAACC,IAAI,CAACxH,QAAQ,CAACrL,KAAK,CAAC;QACxCpK,OAAO,CAACC,GAAG,CAAC,SAASmE,OAAO,QAAQ,CAAC;MACvC;IACF,CAAC,CAAC;;IAEF;IACApE,OAAO,CAACC,GAAG,CAAC,QAAQ+c,mBAAmB,CAACpK,MAAM,YAAY,CAAC;IAC3D,MAAMqP,YAAY,GAAG1F,SAAS,CAACY,gBAAgB,CAACH,mBAAmB,EAAE,IAAI,CAAC;IAE1E,IAAIiF,YAAY,CAACrP,MAAM,GAAG,CAAC,EAAE;MAC3B5S,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1BgiB,YAAY,CAAC1Y,OAAO,CAAC,CAAC6T,SAAS,EAAE/V,CAAC,KAAK;QACrCrH,OAAO,CAACC,GAAG,CAAC,MAAMoH,CAAC,GAAG,EAAE+V,SAAS,CAACE,MAAM,CAAChW,IAAI,IAAI,KAAK,EAC1C,KAAK,EAAE8V,SAAS,CAAChM,QAAQ,EACzB,WAAW,EAAEgM,SAAS,CAACE,MAAM,CAACxa,QAAQ,CAACkF,OAAO,CAAC,CAAC,EAChD,WAAW,EAAEoV,SAAS,CAACE,MAAM,CAAChG,QAAQ,CAAC;;QAEnD;QACA,MAAMiG,GAAG,GAAGC,yBAAyB,CAACJ,SAAS,CAACE,MAAM,CAAC;QACvD,IAAIC,GAAG,IAAIA,GAAG,CAACjG,QAAQ,IAAIiG,GAAG,CAACjG,QAAQ,CAAC3N,IAAI,KAAK,cAAc,EAAE;UAC/D3J,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsd,GAAG,CAACjG,QAAQ,CAAClT,OAAO,CAAC;QAC/C;MACF,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;;IAEA;IACApE,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,MAAMiiB,eAAe,GAAG3F,SAAS,CAACY,gBAAgB,CAACrf,KAAK,CAAC6U,QAAQ,EAAE,IAAI,CAAC;IAExE3S,OAAO,CAACC,GAAG,CAAC,WAAWiiB,eAAe,CAACtP,MAAM,MAAM,CAAC;IACpDsP,eAAe,CAAC3Y,OAAO,CAAC,CAAC6T,SAAS,EAAE/V,CAAC,KAAK;MACxC,MAAMkW,GAAG,GAAGH,SAAS,CAACE,MAAM;MAC5Btd,OAAO,CAACC,GAAG,CAAC,QAAQoH,CAAC,GAAG,EAAEkW,GAAG,CAACjW,IAAI,IAAI,KAAK,EAC/B,KAAK,EAAEiW,GAAG,CAAC5T,IAAI,EACf,KAAK,EAAE4T,GAAG,CAACza,QAAQ,CAACkF,OAAO,CAAC,CAAC,EAC7B,KAAK,EAAEoV,SAAS,CAAChM,QAAQ,EACzB,WAAW,EAAEmM,GAAG,CAACjG,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACAtX,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,IAAIkiB,YAAY,GAAG,CAAC;IAEpBljB,gBAAgB,CAACsK,OAAO,CAAC,CAACkM,QAAQ,EAAErR,OAAO,KAAK;MAC9C,IAAIqR,QAAQ,CAACrL,KAAK,EAAE;QAAA,IAAAgY,qBAAA;QAClB;QACA,IAAIC,SAAS,GAAG5M,QAAQ,CAACrL,KAAK,CAACjG,OAAO;QACtC,IAAIme,cAAc,GAAG,IAAI;;QAEzB;QACA,MAAM1E,QAAQ,GAAG,IAAIxhB,KAAK,CAACqP,OAAO,CAAC,CAAC;QACpCgK,QAAQ,CAACrL,KAAK,CAACyT,gBAAgB,CAACD,QAAQ,CAAC;;QAEzC;QACA,MAAM2E,gBAAgB,GAAG3E,QAAQ,CAAC4E,UAAU,CAAC1e,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAC;;QAExE;QACA,MAAMgb,SAAS,GAAGF,QAAQ,CAACnd,KAAK,CAAC,CAAC,CAACsd,OAAO,CAACja,SAAS,CAAC2B,OAAO,CAAC;QAC7D,IAAIrE,IAAI,CAACwT,GAAG,CAACkJ,SAAS,CAACnd,CAAC,CAAC,GAAG,CAAC,IAAIS,IAAI,CAACwT,GAAG,CAACkJ,SAAS,CAACjd,CAAC,CAAC,GAAG,CAAC,IAAIid,SAAS,CAAC/c,CAAC,GAAG,CAAC,CAAC,IAAI+c,SAAS,CAAC/c,CAAC,GAAG,CAAC,EAAE;UACjGuhB,cAAc,GAAG,KAAK;QACxB;QAEA,IAAID,SAAS,EAAE;UACbF,YAAY,EAAE;QAChB;QAEAniB,OAAO,CAACC,GAAG,CAAC,OAAOmE,OAAO,GAAG,EAAE;UAC7Bwb,EAAE,EAAE,EAAAwC,qBAAA,GAAA3M,QAAQ,CAACtO,YAAY,cAAAib,qBAAA,uBAArBA,qBAAA,CAAuB9a,IAAI,KAAI,IAAI;UACvCyY,GAAG,EAAEsC,SAAS;UACdI,KAAK,EAAEH,cAAc;UACrBI,IAAI,EAAE9E,QAAQ,CAAC5V,OAAO,CAAC,CAAC;UACxB2a,IAAI,EAAE,CAAC7E,SAAS,CAACnd,CAAC,EAAEmd,SAAS,CAACjd,CAAC,EAAEid,SAAS,CAAC/c,CAAC,CAAC;UAC7C6hB,MAAM,EAAEL;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEFviB,OAAO,CAACC,GAAG,CAAC,MAAMkiB,YAAY,IAAIljB,gBAAgB,CAACuY,IAAI,WAAW,CAAC;;IAEnE;IACA,OAAO0K,eAAe,CAACtP,MAAM,GAAG,CAAC;EACnC,CAAC,CAAC,OAAO1S,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA9B,MAAM,CAACykB,sBAAsB,GAAIze,OAAO,IAAK;EAC3C,IAAI,CAACA,OAAO,IAAInF,gBAAgB,CAACuY,IAAI,GAAG,CAAC,EAAE;IACzC;IACApT,OAAO,GAAG3E,KAAK,CAACghB,IAAI,CAACxhB,gBAAgB,CAAC0gB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClD;EAEA,IAAI,CAACvb,OAAO,EAAE;IACZpE,OAAO,CAACE,KAAK,CAAC,UAAU,CAAC;IACzB,OAAO,KAAK;EACd;EAEAF,OAAO,CAACC,GAAG,CAAC,WAAWmE,OAAO,EAAE,CAAC;EAEjC,MAAMqR,QAAQ,GAAGxW,gBAAgB,CAAC4J,GAAG,CAACzE,OAAO,CAAC;EAC9C,IAAI,CAACqR,QAAQ,IAAI,CAACA,QAAQ,CAACrL,KAAK,EAAE;IAChCpK,OAAO,CAACE,KAAK,CAAC,UAAUkE,OAAO,SAAS,CAAC;IACzC,OAAO,KAAK;EACd;;EAEA;EACAqR,QAAQ,CAACrL,KAAK,CAACjG,OAAO,GAAG,IAAI;EAC7BsR,QAAQ,CAACrL,KAAK,CAAC8H,QAAQ,CAACC,KAAK,IAAI;IAC/BA,KAAK,CAAChO,OAAO,GAAG,IAAI;EACtB,CAAC,CAAC;;EAEF;EACA,MAAMyZ,QAAQ,GAAG,IAAIxhB,KAAK,CAACqP,OAAO,CAAC,CAAC;EACpCgK,QAAQ,CAACrL,KAAK,CAACyT,gBAAgB,CAACD,QAAQ,CAAC;;EAEzC;EACA,MAAME,SAAS,GAAGF,QAAQ,CAACnd,KAAK,CAAC,CAAC,CAACsd,OAAO,CAACja,SAAS,CAAC2B,OAAO,CAAC;;EAE7D;EACA,MAAMqT,MAAM,GAAGC,QAAQ,CAACe,aAAa,CAAC,QAAQ,CAAC;EAC/C,IAAIhB,MAAM,EAAE;IACV,MAAMmD,IAAI,GAAGnD,MAAM,CAACoD,qBAAqB,CAAC,CAAC;IAC3C,MAAM4G,MAAM,GAAI,CAAChF,SAAS,CAACnd,CAAC,GAAG,CAAC,IAAI,CAAC,GAAImY,MAAM,CAACsD,WAAW,GAAGH,IAAI,CAACjZ,IAAI;IACvE,MAAM+f,MAAM,GAAI,CAAC,CAACjF,SAAS,CAACjd,CAAC,GAAG,CAAC,IAAI,CAAC,GAAIiY,MAAM,CAACwD,YAAY,GAAGL,IAAI,CAACxX,GAAG;IAExEzE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE6iB,MAAM,EAAEC,MAAM,CAAC;;IAEvC;IACA3kB,MAAM,CAAC2jB,kBAAkB,CAACe,MAAM,EAAEC,MAAM,CAAC;EAC3C;;EAEA;EACA3kB,MAAM,CAACqf,qBAAqB,CAACrZ,OAAO,CAAC;EACrC,OAAO,IAAI;AACb,CAAC;;AAED;AACA,MAAMyI,wBAAwB,GAAGA,CAACN,YAAY,EAAEK,SAAS,KAAK;EAAA,IAAAoW,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC5D,IAAI,CAAC3W,YAAY,IAAI,CAACA,YAAY,CAACnC,KAAK,IAAI,CAACwC,SAAS,EAAE;IACtD;EACF;;EAEA;EACA,MAAMuW,cAAc,GAAG,EAAE;EACzB5W,YAAY,CAACnC,KAAK,CAAC8H,QAAQ,CAACC,KAAK,IAAI;IACnC,IAAIA,KAAK,CAACmF,QAAQ,IAAInF,KAAK,CAACmF,QAAQ,CAAC8L,OAAO,EAAE;MAC5CD,cAAc,CAAClG,IAAI,CAAC9K,KAAK,CAAC;IAC5B;EACF,CAAC,CAAC;EAEFgR,cAAc,CAAC5Z,OAAO,CAAC+U,KAAK,IAAI;IAC9B/R,YAAY,CAACnC,KAAK,CAACa,MAAM,CAACqT,KAAK,CAAC;EAClC,CAAC,CAAC;;EAEF;EACA,IAAIO,UAAU;EACd,QAAOjS,SAAS,CAACL,YAAY;IAC3B,KAAK,GAAG;MACNsS,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;MACNA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;IACR;MACEA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;EACJ;;EAEA;EACA,MAAMwE,aAAa,GAAG,IAAIjnB,KAAK,CAAC4a,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAMsM,aAAa,GAAG,IAAIlnB,KAAK,CAACoa,iBAAiB,CAAC;IAChD3R,KAAK,EAAEga,UAAU;IACjB0E,QAAQ,EAAE1E,UAAU;IACpB2E,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAMC,SAAS,GAAG,IAAIrnB,KAAK,CAACqa,IAAI,CAAC4M,aAAa,EAAEC,aAAa,CAAC;EAC9DG,SAAS,CAAC3gB,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAClCyiB,SAAS,CAACnM,QAAQ,GAAG;IACnB8L,OAAO,EAAE,IAAI;IACbzZ,IAAI,EAAE,cAAc;IACpBvF,OAAO,GAAA4e,qBAAA,GAAEzW,YAAY,CAACpF,YAAY,cAAA6b,qBAAA,uBAAzBA,qBAAA,CAA2B5e,OAAO;IAC3C4H,OAAO,EAAEY,SAAS,CAACZ,OAAO;IAC1BE,SAAS,EAAEU,SAAS,CAACV,SAAS;IAC9BM,UAAU,EAAEI,SAAS,CAACJ;EACxB,CAAC;;EAED;EACA,MAAM8R,KAAK,GAAG,IAAIliB,KAAK,CAACsnB,UAAU,CAAC7E,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;EACrDP,KAAK,CAACxb,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAC5Bsd,KAAK,CAAChH,QAAQ,GAAG;IAAE8L,OAAO,EAAE;EAAK,CAAC;;EAElC;EACA7W,YAAY,CAACnC,KAAK,CAACI,GAAG,CAACiZ,SAAS,CAAC;EACjClX,YAAY,CAACnC,KAAK,CAACI,GAAG,CAAC8T,KAAK,CAAC;EAE7Bte,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAAgjB,sBAAA,GAAA1W,YAAY,CAACpF,YAAY,cAAA8b,sBAAA,uBAAzBA,sBAAA,CAA2B3b,IAAI,OAAA4b,sBAAA,GAAI3W,YAAY,CAACpF,YAAY,cAAA+b,sBAAA,uBAAzBA,sBAAA,CAA2B9e,OAAO,cAAawI,SAAS,CAACL,YAAY,WAAWK,SAAS,CAACJ,UAAU,GAAG,CAAC;AACjK,CAAC;AAED,eAAejL,WAAW;AAAC,IAAAoX,EAAA;AAAAgL,YAAA,CAAAhL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}