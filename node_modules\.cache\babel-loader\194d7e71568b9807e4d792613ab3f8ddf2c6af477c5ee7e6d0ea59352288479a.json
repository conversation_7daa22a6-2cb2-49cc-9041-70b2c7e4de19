{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\Login.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Form, Input, Button, Card, Typography, Checkbox, message, Spin, Alert } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport axios from 'axios';\nimport { login } from '../services/auth';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\n\n// 登录页面容器\nconst LoginContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n  position: relative;\n  overflow: hidden;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\n    opacity: 0.15;\n    z-index: 0;\n  }\n`;\n\n// 登录卡片样式\n_c = LoginContainer;\nconst LoginCard = styled(Card)`\n  width: 400px;\n  border-radius: 8px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\n  z-index: 1;\n  \n  .ant-card-body {\n    padding: 40px;\n  }\n`;\n\n// 标题容器\n_c2 = LoginCard;\nconst TitleContainer = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n\n// Logo样式\n_c3 = TitleContainer;\nconst Logo = styled.div`\n  width: 80px;\n  height: 80px;\n  margin: 0 auto 20px;\n  background: #1890ff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 40px;\n  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);\n`;\n\n// 表单样式\n_c4 = Logo;\nconst StyledForm = styled(Form)`\n  .ant-form-item-label {\n    font-weight: 500;\n  }\n  \n  .ant-input-affix-wrapper {\n    padding: 12px;\n    border-radius: 6px;\n  }\n  \n  .ant-btn {\n    height: 45px;\n    border-radius: 6px;\n    font-weight: 500;\n  }\n`;\n\n// 底部文本样式\n_c5 = StyledForm;\nconst FooterText = styled(Text)`\n  display: block;\n  text-align: center;\n  margin-top: 20px;\n  color: #8c8c8c;\n`;\n_c6 = FooterText;\nconst Login = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [loginError, setLoginError] = useState('');\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const onFinish = async values => {\n    setLoading(true);\n    setLoginError('');\n    try {\n      console.log('尝试登录:', values);\n\n      // 使用 auth 服务进行登录\n      const result = await login(values.username, values.password);\n      if (result.success) {\n        localStorage.setItem('user', JSON.stringify(result.data.user));\n        localStorage.setItem('token', result.data.token);\n\n        // 添加调试日志\n        console.log('登录成功，准备导航到:', '/real-time-traffic');\n        message.success('登录成功！');\n\n        // 确保导航在消息显示后执行\n        setTimeout(() => {\n          navigate('/real-time-traffic');\n          console.log('导航已执行');\n        }, 100);\n      } else {\n        setLoginError(result.message || '登录失败');\n        message.error(result.message || '登录失败');\n      }\n    } catch (error) {\n      console.error('登录错误:', error);\n      setLoginError('登录过程中发生错误');\n      message.error('登录过程中发生错误');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleTestLogin = () => {\n    form.setFieldsValue({\n      username: 'admin',\n      password: 'admin123'\n    });\n    form.submit();\n  };\n  const handleLogin = async values => {\n    try {\n      // 假设这是您的登录API调用\n      const response = await loginAPI(values.username, values.password);\n      if (response.success) {\n        // 保存用户信息到localStorage\n        localStorage.setItem('user', JSON.stringify(response.user));\n\n        // 跳转到主页\n        navigate('/');\n        message.success('登录成功');\n      } else {\n        message.error('登录失败: ' + response.message);\n      }\n    } catch (error) {\n      console.error('登录出错:', error);\n      message.error('系统错误，请稍后再试');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(LoginContainer, {\n    children: /*#__PURE__*/_jsxDEV(Spin, {\n      spinning: loading,\n      tip: \"\\u767B\\u5F55\\u4E2D...\",\n      children: /*#__PURE__*/_jsxDEV(LoginCard, {\n        children: [/*#__PURE__*/_jsxDEV(TitleContainer, {\n          children: [/*#__PURE__*/_jsxDEV(Logo, {\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-car-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            style: {\n              margin: 0,\n              color: '#1890ff'\n            },\n            children: \"\\u667A\\u80FD\\u7F51\\u8054\\u4EA7\\u5B66\\u7814\\u4E91\\u5E73\\u53F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u767B\\u5F55\\u60A8\\u7684\\u8D26\\u6237\\u4EE5\\u7EE7\\u7EED\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), loginError && /*#__PURE__*/_jsxDEV(Alert, {\n          message: loginError,\n          type: \"error\",\n          showIcon: true,\n          style: {\n            marginBottom: 24\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StyledForm, {\n          form: form,\n          name: \"login\",\n          initialValues: {\n            remember: true\n          },\n          onFinish: onFinish,\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"username\",\n            rules: [{\n              required: true,\n              message: '请输入用户名!'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {\n                style: {\n                  color: '#bfbfbf'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 25\n              }, this),\n              placeholder: \"\\u7528\\u6237\\u540D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            rules: [{\n              required: true,\n              message: '请输入密码!'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input.Password, {\n              prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {\n                style: {\n                  color: '#bfbfbf'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 25\n              }, this),\n              placeholder: \"\\u5BC6\\u7801\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"remember\",\n              valuePropName: \"checked\",\n              noStyle: true,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                children: \"\\u8BB0\\u4F4F\\u6211\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              style: {\n                float: 'right'\n              },\n              href: \"#\",\n              children: \"\\u5FD8\\u8BB0\\u5BC6\\u7801?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              block: true,\n              loading: loading,\n              children: \"\\u767B\\u5F55\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              onClick: handleTestLogin,\n              block: true,\n              children: \"\\u6D4B\\u8BD5\\u767B\\u5F55 (admin/admin123)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FooterText, {\n          children: [\"\\xA9 \", new Date().getFullYear(), \" \\u667A\\u80FD\\u7F51\\u8054\\u4EA7\\u5B66\\u7814\\u4E91\\u5E73\\u53F0 \\u7248\\u6743\\u6240\\u6709\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"VjMgo1tmSMB+Phs5Cr6cFtVJuvE=\", false, function () {\n  return [useNavigate, Form.useForm];\n});\n_c7 = Login;\nexport default Login;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"LoginContainer\");\n$RefreshReg$(_c2, \"LoginCard\");\n$RefreshReg$(_c3, \"TitleContainer\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"StyledForm\");\n$RefreshReg$(_c6, \"FooterText\");\n$RefreshReg$(_c7, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Form", "Input", "<PERSON><PERSON>", "Card", "Typography", "Checkbox", "message", "Spin", "<PERSON><PERSON>", "UserOutlined", "LockOutlined", "useNavigate", "styled", "axios", "login", "jsxDEV", "_jsxDEV", "Title", "Text", "LoginContainer", "div", "_c", "LoginCard", "_c2", "TitleC<PERSON>r", "_c3", "Logo", "_c4", "StyledForm", "_c5", "FooterText", "_c6", "<PERSON><PERSON>", "_s", "loading", "setLoading", "loginError", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "navigate", "form", "useForm", "onFinish", "values", "console", "log", "result", "username", "password", "success", "localStorage", "setItem", "JSON", "stringify", "data", "user", "token", "setTimeout", "error", "handleTestLogin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submit", "handleLogin", "response", "loginAPI", "children", "spinning", "tip", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "level", "style", "margin", "color", "type", "showIcon", "marginBottom", "name", "initialValues", "remember", "size", "<PERSON><PERSON>", "rules", "required", "prefix", "placeholder", "Password", "valuePropName", "noStyle", "float", "href", "htmlType", "block", "onClick", "Date", "getFullYear", "_c7", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/Login.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Form, Input, But<PERSON>, Card, Typography, Checkbox, message, Spin, Alert } from 'antd';\r\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport styled from 'styled-components';\r\nimport axios from 'axios';\r\nimport { login } from '../services/auth';\r\n\r\nconst { Title, Text } = Typography;\r\n\r\n// 登录页面容器\r\nconst LoginContainer = styled.div`\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100vh;\r\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\r\n  position: relative;\r\n  overflow: hidden;\r\n  \r\n  &::before {\r\n    content: '';\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\r\n    opacity: 0.15;\r\n    z-index: 0;\r\n  }\r\n`;\r\n\r\n// 登录卡片样式\r\nconst LoginCard = styled(Card)`\r\n  width: 400px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\r\n  z-index: 1;\r\n  \r\n  .ant-card-body {\r\n    padding: 40px;\r\n  }\r\n`;\r\n\r\n// 标题容器\r\nconst TitleContainer = styled.div`\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n`;\r\n\r\n// Logo样式\r\nconst Logo = styled.div`\r\n  width: 80px;\r\n  height: 80px;\r\n  margin: 0 auto 20px;\r\n  background: #1890ff;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 40px;\r\n  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);\r\n`;\r\n\r\n// 表单样式\r\nconst StyledForm = styled(Form)`\r\n  .ant-form-item-label {\r\n    font-weight: 500;\r\n  }\r\n  \r\n  .ant-input-affix-wrapper {\r\n    padding: 12px;\r\n    border-radius: 6px;\r\n  }\r\n  \r\n  .ant-btn {\r\n    height: 45px;\r\n    border-radius: 6px;\r\n    font-weight: 500;\r\n  }\r\n`;\r\n\r\n// 底部文本样式\r\nconst FooterText = styled(Text)`\r\n  display: block;\r\n  text-align: center;\r\n  margin-top: 20px;\r\n  color: #8c8c8c;\r\n`;\r\n\r\nconst Login = () => {\r\n  const [loading, setLoading] = useState(false);\r\n  const [loginError, setLoginError] = useState('');\r\n  const navigate = useNavigate();\r\n  const [form] = Form.useForm();\r\n\r\n  const onFinish = async (values) => {\r\n    setLoading(true);\r\n    setLoginError('');\r\n    \r\n    try {\r\n      console.log('尝试登录:', values);\r\n      \r\n      // 使用 auth 服务进行登录\r\n      const result = await login(values.username, values.password);\r\n      \r\n      if (result.success) {\r\n        localStorage.setItem('user', JSON.stringify(result.data.user));\r\n        localStorage.setItem('token', result.data.token);\r\n        \r\n        // 添加调试日志\r\n        console.log('登录成功，准备导航到:', '/real-time-traffic');\r\n        \r\n        message.success('登录成功！');\r\n        \r\n        // 确保导航在消息显示后执行\r\n        setTimeout(() => {\r\n          navigate('/real-time-traffic');\r\n          console.log('导航已执行');\r\n        }, 100);\r\n      } else {\r\n        setLoginError(result.message || '登录失败');\r\n        message.error(result.message || '登录失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('登录错误:', error);\r\n      setLoginError('登录过程中发生错误');\r\n      message.error('登录过程中发生错误');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleTestLogin = () => {\r\n    form.setFieldsValue({\r\n      username: 'admin',\r\n      password: 'admin123'\r\n    });\r\n    form.submit();\r\n  };\r\n\r\n  const handleLogin = async (values) => {\r\n    try {\r\n      // 假设这是您的登录API调用\r\n      const response = await loginAPI(values.username, values.password);\r\n      \r\n      if (response.success) {\r\n        // 保存用户信息到localStorage\r\n        localStorage.setItem('user', JSON.stringify(response.user));\r\n        \r\n        // 跳转到主页\r\n        navigate('/');\r\n        \r\n        message.success('登录成功');\r\n      } else {\r\n        message.error('登录失败: ' + response.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('登录出错:', error);\r\n      message.error('系统错误，请稍后再试');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <LoginContainer>\r\n      <Spin spinning={loading} tip=\"登录中...\">\r\n        <LoginCard>\r\n          <TitleContainer>\r\n            <Logo>\r\n              <i className=\"fas fa-car-alt\" />\r\n            </Logo>\r\n            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>智能网联产学研云平台</Title>\r\n            <Text type=\"secondary\">登录您的账户以继续</Text>\r\n          </TitleContainer>\r\n          \r\n          {loginError && (\r\n            <Alert\r\n              message={loginError}\r\n              type=\"error\"\r\n              showIcon\r\n              style={{ marginBottom: 24 }}\r\n            />\r\n          )}\r\n          \r\n          <StyledForm\r\n            form={form}\r\n            name=\"login\"\r\n            initialValues={{ remember: true }}\r\n            onFinish={onFinish}\r\n            size=\"large\"\r\n          >\r\n            <Form.Item\r\n              name=\"username\"\r\n              rules={[{ required: true, message: '请输入用户名!' }]}\r\n            >\r\n              <Input \r\n                prefix={<UserOutlined style={{ color: '#bfbfbf' }} />} \r\n                placeholder=\"用户名\" \r\n              />\r\n            </Form.Item>\r\n\r\n            <Form.Item\r\n              name=\"password\"\r\n              rules={[{ required: true, message: '请输入密码!' }]}\r\n            >\r\n              <Input.Password \r\n                prefix={<LockOutlined style={{ color: '#bfbfbf' }} />} \r\n                placeholder=\"密码\" \r\n              />\r\n            </Form.Item>\r\n\r\n            <Form.Item>\r\n              <Form.Item name=\"remember\" valuePropName=\"checked\" noStyle>\r\n                <Checkbox>记住我</Checkbox>\r\n              </Form.Item>\r\n\r\n              <a style={{ float: 'right' }} href=\"#\">\r\n                忘记密码?\r\n              </a>\r\n            </Form.Item>\r\n\r\n            <Form.Item>\r\n              <Button type=\"primary\" htmlType=\"submit\" block loading={loading}>\r\n                登录\r\n              </Button>\r\n            </Form.Item>\r\n            \r\n            <Form.Item>\r\n              <Button type=\"link\" onClick={handleTestLogin} block>\r\n                测试登录 (admin/admin123)\r\n              </Button>\r\n            </Form.Item>\r\n          </StyledForm>\r\n          \r\n          <FooterText>\r\n            © {new Date().getFullYear()} 智能网联产学研云平台 版权所有\r\n          </FooterText>\r\n        </LoginCard>\r\n      </Spin>\r\n    </LoginContainer>\r\n  );\r\n};\r\n\r\nexport default Login; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAC5F,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGd,UAAU;;AAElC;AACA,MAAMe,cAAc,GAAGP,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GAtBMF,cAAc;AAuBpB,MAAMG,SAAS,GAAGV,MAAM,CAACT,IAAI,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAoB,GAAA,GAXMD,SAAS;AAYf,MAAME,cAAc,GAAGZ,MAAM,CAACQ,GAAG;AACjC;AACA;AACA,CAAC;;AAED;AAAAK,GAAA,GALMD,cAAc;AAMpB,MAAME,IAAI,GAAGd,MAAM,CAACQ,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAO,GAAA,GAdMD,IAAI;AAeV,MAAME,UAAU,GAAGhB,MAAM,CAACZ,IAAI,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAA6B,GAAA,GAjBMD,UAAU;AAkBhB,MAAME,UAAU,GAAGlB,MAAM,CAACM,IAAI,CAAC;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GALID,UAAU;AAOhB,MAAME,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAMuC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC4B,IAAI,CAAC,GAAGvC,IAAI,CAACwC,OAAO,CAAC,CAAC;EAE7B,MAAMC,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjCP,UAAU,CAAC,IAAI,CAAC;IAChBE,aAAa,CAAC,EAAE,CAAC;IAEjB,IAAI;MACFM,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,MAAM,CAAC;;MAE5B;MACA,MAAMG,MAAM,GAAG,MAAM/B,KAAK,CAAC4B,MAAM,CAACI,QAAQ,EAAEJ,MAAM,CAACK,QAAQ,CAAC;MAE5D,IAAIF,MAAM,CAACG,OAAO,EAAE;QAClBC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACP,MAAM,CAACQ,IAAI,CAACC,IAAI,CAAC,CAAC;QAC9DL,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEL,MAAM,CAACQ,IAAI,CAACE,KAAK,CAAC;;QAEhD;QACAZ,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,oBAAoB,CAAC;QAEhDtC,OAAO,CAAC0C,OAAO,CAAC,OAAO,CAAC;;QAExB;QACAQ,UAAU,CAAC,MAAM;UACflB,QAAQ,CAAC,oBAAoB,CAAC;UAC9BK,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;QACtB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLP,aAAa,CAACQ,MAAM,CAACvC,OAAO,IAAI,MAAM,CAAC;QACvCA,OAAO,CAACmD,KAAK,CAACZ,MAAM,CAACvC,OAAO,IAAI,MAAM,CAAC;MACzC;IACF,CAAC,CAAC,OAAOmD,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BpB,aAAa,CAAC,WAAW,CAAC;MAC1B/B,OAAO,CAACmD,KAAK,CAAC,WAAW,CAAC;IAC5B,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,eAAe,GAAGA,CAAA,KAAM;IAC5BnB,IAAI,CAACoB,cAAc,CAAC;MAClBb,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACFR,IAAI,CAACqB,MAAM,CAAC,CAAC;EACf,CAAC;EAED,MAAMC,WAAW,GAAG,MAAOnB,MAAM,IAAK;IACpC,IAAI;MACF;MACA,MAAMoB,QAAQ,GAAG,MAAMC,QAAQ,CAACrB,MAAM,CAACI,QAAQ,EAAEJ,MAAM,CAACK,QAAQ,CAAC;MAEjE,IAAIe,QAAQ,CAACd,OAAO,EAAE;QACpB;QACAC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACU,QAAQ,CAACR,IAAI,CAAC,CAAC;;QAE3D;QACAhB,QAAQ,CAAC,GAAG,CAAC;QAEbhC,OAAO,CAAC0C,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL1C,OAAO,CAACmD,KAAK,CAAC,QAAQ,GAAGK,QAAQ,CAACxD,OAAO,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOmD,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BnD,OAAO,CAACmD,KAAK,CAAC,YAAY,CAAC;IAC7B;EACF,CAAC;EAED,oBACEzC,OAAA,CAACG,cAAc;IAAA6C,QAAA,eACbhD,OAAA,CAACT,IAAI;MAAC0D,QAAQ,EAAE/B,OAAQ;MAACgC,GAAG,EAAC,uBAAQ;MAAAF,QAAA,eACnChD,OAAA,CAACM,SAAS;QAAA0C,QAAA,gBACRhD,OAAA,CAACQ,cAAc;UAAAwC,QAAA,gBACbhD,OAAA,CAACU,IAAI;YAAAsC,QAAA,eACHhD,OAAA;cAAGmD,SAAS,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACPvD,OAAA,CAACC,KAAK;YAACuD,KAAK,EAAE,CAAE;YAACC,KAAK,EAAE;cAAEC,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAX,QAAA,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3EvD,OAAA,CAACE,IAAI;YAAC0D,IAAI,EAAC,WAAW;YAAAZ,QAAA,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,EAEhBnC,UAAU,iBACTpB,OAAA,CAACR,KAAK;UACJF,OAAO,EAAE8B,UAAW;UACpBwC,IAAI,EAAC,OAAO;UACZC,QAAQ;UACRJ,KAAK,EAAE;YAAEK,YAAY,EAAE;UAAG;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACF,eAEDvD,OAAA,CAACY,UAAU;UACTW,IAAI,EAAEA,IAAK;UACXwC,IAAI,EAAC,OAAO;UACZC,aAAa,EAAE;YAAEC,QAAQ,EAAE;UAAK,CAAE;UAClCxC,QAAQ,EAAEA,QAAS;UACnByC,IAAI,EAAC,OAAO;UAAAlB,QAAA,gBAEZhD,OAAA,CAAChB,IAAI,CAACmF,IAAI;YACRJ,IAAI,EAAC,UAAU;YACfK,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE/E,OAAO,EAAE;YAAU,CAAC,CAAE;YAAA0D,QAAA,eAEhDhD,OAAA,CAACf,KAAK;cACJqF,MAAM,eAAEtE,OAAA,CAACP,YAAY;gBAACgE,KAAK,EAAE;kBAAEE,KAAK,EAAE;gBAAU;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACtDgB,WAAW,EAAC;YAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZvD,OAAA,CAAChB,IAAI,CAACmF,IAAI;YACRJ,IAAI,EAAC,UAAU;YACfK,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE/E,OAAO,EAAE;YAAS,CAAC,CAAE;YAAA0D,QAAA,eAE/ChD,OAAA,CAACf,KAAK,CAACuF,QAAQ;cACbF,MAAM,eAAEtE,OAAA,CAACN,YAAY;gBAAC+D,KAAK,EAAE;kBAAEE,KAAK,EAAE;gBAAU;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACtDgB,WAAW,EAAC;YAAI;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZvD,OAAA,CAAChB,IAAI,CAACmF,IAAI;YAAAnB,QAAA,gBACRhD,OAAA,CAAChB,IAAI,CAACmF,IAAI;cAACJ,IAAI,EAAC,UAAU;cAACU,aAAa,EAAC,SAAS;cAACC,OAAO;cAAA1B,QAAA,eACxDhD,OAAA,CAACX,QAAQ;gBAAA2D,QAAA,EAAC;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEZvD,OAAA;cAAGyD,KAAK,EAAE;gBAAEkB,KAAK,EAAE;cAAQ,CAAE;cAACC,IAAI,EAAC,GAAG;cAAA5B,QAAA,EAAC;YAEvC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAEZvD,OAAA,CAAChB,IAAI,CAACmF,IAAI;YAAAnB,QAAA,eACRhD,OAAA,CAACd,MAAM;cAAC0E,IAAI,EAAC,SAAS;cAACiB,QAAQ,EAAC,QAAQ;cAACC,KAAK;cAAC5D,OAAO,EAAEA,OAAQ;cAAA8B,QAAA,EAAC;YAEjE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZvD,OAAA,CAAChB,IAAI,CAACmF,IAAI;YAAAnB,QAAA,eACRhD,OAAA,CAACd,MAAM;cAAC0E,IAAI,EAAC,MAAM;cAACmB,OAAO,EAAErC,eAAgB;cAACoC,KAAK;cAAA9B,QAAA,EAAC;YAEpD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEbvD,OAAA,CAACc,UAAU;UAAAkC,QAAA,GAAC,OACR,EAAC,IAAIgC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAC,wFAC9B;QAAA;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAErB,CAAC;AAACtC,EAAA,CAvJID,KAAK;EAAA,QAGQrB,WAAW,EACbX,IAAI,CAACwC,OAAO;AAAA;AAAA0D,GAAA,GAJvBlE,KAAK;AAyJX,eAAeA,KAAK;AAAC,IAAAX,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAmE,GAAA;AAAAC,YAAA,CAAA9E,EAAA;AAAA8E,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAxE,GAAA;AAAAwE,YAAA,CAAAtE,GAAA;AAAAsE,YAAA,CAAApE,GAAA;AAAAoE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}