{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n  bsm: 'changli/cloud/v2x/obu/bsm',\n  rsm: 'changli/cloud/v2x/rsu/rsm',\n  scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',\n  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat' // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (!lastPosition) {\n      lastPosition = newPos.clone();\n      return newPos;\n    }\n    const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n    const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n    const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n    lastPosition.set(filteredX, filteredY, filteredZ);\n    return lastPosition.clone();\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  const lastPos = vehicleLastPositions.get(vehicleId);\n\n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n\n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n\n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n    if (lastRotation === null) {\n      lastRotation = newRotation;\n      return newRotation;\n    }\n\n    // 处理角度跳变（从360度到0度或反之）\n    let diff = newRotation - lastRotation;\n    if (diff > Math.PI) diff -= 2 * Math.PI;\n    if (diff < -Math.PI) diff += 2 * Math.PI;\n    const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n    lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n\n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const lastRot = vehicleLastRotations.get(vehicleId);\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n\n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\nconst CampusModel = ({\n  className,\n  onCurrentRSUChange,\n  selectedRSUs\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n\n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n\n  // 添加动画时间引用\n  const prevAnimationTimeRef = useRef(Date.now());\n\n  // 添加行人动画混合器Map\n  const peopleAnimationMixers = new Map();\n\n  // 添加行人动画数组\n  const peopleAnimations = [];\n\n  // 添加测试行人动画混合器\n  const testPeopleAnimationMixer = useRef(null);\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,\n    // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null,\n    phases: []\n  });\n\n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n\n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n\n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n\n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({\n    topic: '',\n    content: ''\n  });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n\n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n\n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n\n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos).to({\n          x: 0,\n          y: 300,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        }).start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp).to({\n          x: 0,\n          y: 1,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        }).start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n\n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget).to({\n          x: 0,\n          y: 0,\n          z: 0\n        }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        }).start();\n\n        // 启用控制器\n        controls.enabled = true;\n\n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x + 50, 70, -modelCoords.y + 50);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        var _payload$data;\n        console.log('收到RSM消息:', payload);\n\n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n\n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          console.log('忽略过期的RSM消息:', {\n            设备MAC: deviceMac,\n            消息时间戳: messageTimestamp,\n            最新时间戳: lastTimestamp\n          });\n          return;\n        }\n\n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        console.log('RSM消息时间戳更新:', {\n          设备MAC: deviceMac,\n          时间戳: messageTimestamp,\n          是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        });\n        const participants = ((_payload$data = payload.data) === null || _payload$data === void 0 ? void 0 : _payload$data.participants) || [];\n        const rsuid = payload.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id = deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = preloadedModel.clone();\n              // 根据类型调整高度\n              const height = type === '3' ? 0.5 : 1.0; // 行人模型贴近地面\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n\n              // 如果是行人模型，设置动画\n              if (type === '3' && peopleAnimations.length > 0) {\n                try {\n                  // 创建动画混合器\n                  const mixer = new THREE.AnimationMixer(newModel);\n\n                  // 获取动画剪辑\n                  const walkAnimation = peopleAnimations[0]; // 选择第一个动画\n                  console.log(`为行人ID ${id} 应用动画:`, walkAnimation.name || '无名称动画');\n\n                  // 创建动作并播放\n                  const action = mixer.clipAction(walkAnimation);\n                  action.setEffectiveTimeScale(1.0); // 确保时间比例为1\n                  action.setEffectiveWeight(1.0); // 确保权重为1\n                  action.setLoop(THREE.LoopRepeat); // 设置循环模式\n                  action.play();\n\n                  // 将混合器保存到Map中\n                  peopleAnimationMixers.set(id, mixer);\n                  console.log(`为行人 ${id} 创建动画混合器成功，动画状态:`, {\n                    动画名称: walkAnimation.name || '无名称',\n                    播放状态: action.isRunning() ? '运行中' : '已停止',\n                    循环模式: action.loop,\n                    混合器ID: mixer.uuid\n                  });\n                } catch (error) {\n                  console.error(`为行人 ${id} 创建动画时出错:`, error);\n                }\n              }\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 0.5 : 1.0, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n\n              // 确保更新矩阵\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n\n              // 如果是行人模型，检查并确保动画正在播放\n              if (model.type === '3') {\n                if (!peopleAnimationMixers.has(id) && peopleAnimations.length > 0) {\n                  // 如果没有动画混合器，创建一个\n                  try {\n                    const mixer = new THREE.AnimationMixer(model.model);\n                    const walkAnimation = peopleAnimations[0];\n                    const action = mixer.clipAction(walkAnimation);\n                    action.setLoop(THREE.LoopRepeat);\n                    action.play();\n                    peopleAnimationMixers.set(id, mixer);\n                    console.log(`为现有行人 ${id} 补充创建动画混合器`);\n                  } catch (error) {\n                    console.error(`为现有行人 ${id} 创建动画混合器失败:`, error);\n                  }\n                }\n              }\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        console.log('收到BSM消息:', payload);\n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n\n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n\n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid,\n          // 直接传递车辆ID\n          data: {\n            // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n\n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n\n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n\n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n\n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n\n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n\n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n\n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n\n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n\n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n\n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          scene.add(newVehicleModel);\n\n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1',\n            // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n          console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position).to({\n            y: 0.5\n          }, 500).easing(TWEEN.Easing.Quadratic.Out).start();\n\n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({\n                opacity: 0.6\n              }).to({\n                opacity: 1.0\n              }, 500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function () {\n                child.material.opacity = this.opacity;\n                child.material.needsUpdate = true;\n              }).start();\n            }\n          });\n\n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({\n            opacity: 0.6\n          }).to({\n            opacity: 1.0\n          }, 500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function () {\n            speedLabel.material.opacity = this.opacity;\n            speedLabel.material.needsUpdate = true;\n          }).start();\n\n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n\n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n\n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n\n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n          console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n\n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 2000; // 增加到10秒，避免频繁清理造成闪烁\n\n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n\n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1;\n            modelData.model.traverse(child => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n\n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n\n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            modelData.model.traverse(child => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n\n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n\n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        console.log('收到SPAT消息:', message);\n        try {\n          const payload = JSON.parse(message);\n\n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n\n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            console.log('忽略过期的SPAT消息:', {\n              设备MAC: deviceMac,\n              消息时间戳: messageTimestamp,\n              最新时间戳: lastTimestamp\n            });\n            return;\n          }\n\n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n\n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              console.log(`处理路口ID: ${interId} 的SPAT消息`);\n\n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? getDirectionFromCode(phase.trafficDirec) : getPhaseDirection(phaseId);\n\n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n\n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n\n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n\n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n\n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n\n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n\n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && (window.currentPopoverIdRef.current === modelKey || window.currentPopoverIdRef.current === String(modelKey) || window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n\n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n      }\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        console.log('收到RSI消息:', payload);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(parseFloat(rsiData.posLong), parseFloat(rsiData.posLat));\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          switch (eventType) {\n            case '401':\n              // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':\n              // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':\n              // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':\n              // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':\n              // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002':\n              // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':\n              // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        console.log('收到场景事件消息:', payload);\n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch (sceneType) {\n          case '2':\n            // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':\n            // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':\n            // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':\n            // 限速提醒\n            const speedLimit = sceneData.eventData1; // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':\n            // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':\n            // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':\n            // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':\n            // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':\n            // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':\n            // 信号灯优先\n            const priorityType = sceneData.eventData1; // 优先类型\n            const duration = sceneData.eventData2; // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: payload.type,\n        data: payload\n      });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n\n        // 检查scene是否初始化\n        if (scene) {\n          scene.add(model);\n\n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } else {\n          console.error('无法添加模型：场景未初始化');\n        }\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画混合器\n      const currentTime = Date.now();\n      const deltaTime = (currentTime - prevAnimationTimeRef.current) * 0.001; // 转换为秒\n      prevAnimationTimeRef.current = currentTime;\n\n      // 更新所有行人动画\n      peopleAnimationMixers.forEach(mixer => {\n        mixer.update(deltaTime);\n      });\n\n      // 更新测试行人模型的动画（如果存在）\n      if (testPeopleAnimationMixer) {\n        testPeopleAnimationMixer.update(deltaTime);\n      }\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 200, -50 * Math.sin(adjustedRotation));\n\n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n\n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n\n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n\n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n\n        // 路口视角模式\n        controls.update();\n      }\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n\n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n\n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach(lightObj => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n\n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n\n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n\n      // 清理动画混合器\n      peopleAnimationMixers.forEach(mixer => {\n        mixer.stopAllAction();\n      });\n      peopleAnimationMixers.clear();\n\n      // 清理测试行人模型\n      if (testPeopleModel && scene) {\n        scene.remove(testPeopleModel);\n        if (testPeopleAnimationMixer) {\n          testPeopleAnimationMixer.stopAllAction();\n          testPeopleAnimationMixer = null;\n        }\n        testPeopleModel = null;\n      }\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n\n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n\n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n\n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n\n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {\n          // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n\n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = event => {\n        if (scene && cameraRef.current) {\n          console.log('检测到点击事件', event.clientX, event.clientY);\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        } else {\n          console.warn('点击事件处理失败: scene或camera未初始化');\n        }\n      };\n\n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n\n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n\n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({\n      color: 0x333333\n    });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n\n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({\n      color: 0x666666\n    });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n\n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          //\n          transparent: false,\n          opacity: 0.1,\n          // 几乎透明\n          depthWrite: false\n        });\n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0); // 放在红绿灯位置\n\n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n\n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500); // 延迟略长于debugTrafficLights\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n\n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n\n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        const firstIntersection = intersectionsData.intersections[0];\n        console.log('自动选择第一个路口:', firstIntersection.name);\n\n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(firstIntersection.name);\n        }, 2000);\n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u533A\\u57DF\\u9009\\u62E9\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1943,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u533A\\u57DF\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      options: intersectionsData.intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      },\n      value: selectedIntersection ? selectedIntersection.name : undefined\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1944,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1960,\n      columnNumber: 7\n    }, this), trafficLightPopover.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${trafficLightPopover.position.x}px`,\n        top: `${trafficLightPopover.position.y}px`,\n        transform: 'translate(-50%, -100%)',\n        zIndex: 1003,\n        backgroundColor: 'rgba(0, 0, 0, 0.85)',\n        color: 'white',\n        borderRadius: '4px',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n        padding: '0',\n        maxWidth: '240px',\n        // 缩小最大宽度\n        fontSize: '12px' // 缩小字体\n      },\n      children: [trafficLightPopover.content, /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          position: 'absolute',\n          top: '0px',\n          right: '0px',\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          fontSize: '12px',\n          cursor: 'pointer',\n          padding: '2px 6px'\n        },\n        onClick: () => handleClosePopover(setTrafficLightPopover),\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1981,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1964,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2001,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2011,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2000,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"NyfxMpJ4JIm9ZXh7Z3bU0qBM17o=\");\n_c = CampusModel;\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 16,\n    // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    backgroundColor: parameters.backgroundColor || {\n      r: 255,\n      g: 255,\n      b: 255,\n      a: 0.8\n    },\n    textColor: parameters.textColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n\n  // 设置字体\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n\n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n\n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  canvas.width = width;\n  canvas.height = height;\n\n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n\n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n\n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n\n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n\n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n\n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n\n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n\n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n\n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = {\n    text: text,\n    params: params\n  };\n  return sprite;\n}\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    try {\n      const [vehicleGltf, cyclistGltf, peopleGltf, trafficLightGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`), loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)]);\n\n      // 处理机动车模型\n      preloadedVehicleModel = vehicleGltf.scene;\n      preloadedVehicleModel.traverse(child => {\n        if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n            color: 0xff0000,\n            // 0xff0000, //红色 0xffffff,白色\n            metalness: 0.2,\n            roughness: 0.1,\n            envMapIntensity: 1.0\n          });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n        }\n      });\n\n      // 处理非机动车模型\n      preloadedCyclistModel = cyclistGltf.scene;\n      // 设置非机动车模型的缩放\n      preloadedCyclistModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedCyclistModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n\n      // 处理行人模型\n      preloadedPeopleModel = peopleGltf.scene;\n      // 设置行人模型的缩放\n      preloadedPeopleModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedPeopleModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n\n      // 检查并存储行人动画\n      if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n        peopleAnimations = peopleGltf.animations;\n        console.log(`行人模型包含 ${peopleAnimations.length} 个动画:`, peopleAnimations.map(anim => ({\n          名称: anim.name || '无名称动画',\n          轨道数: anim.tracks ? anim.tracks.length : 0,\n          时长: anim.duration\n        })));\n\n        // 创建一个测试行人模型以验证动画\n        if (scene) {\n          setTimeout(() => {\n            try {\n              // 克隆行人模型并放置在场景中央\n              testPeopleModel = preloadedPeopleModel.clone();\n              testPeopleModel.position.set(20, 0.5, 20); // 放在场景中可见位置\n\n              // 创建动画混合器\n              testPeopleAnimationMixer = new THREE.AnimationMixer(testPeopleModel);\n\n              // 应用行走动画\n              const walkAction = testPeopleAnimationMixer.clipAction(peopleAnimations[0]);\n              walkAction.setLoop(THREE.LoopRepeat);\n              walkAction.play();\n\n              // 添加到场景\n              scene.add(testPeopleModel);\n              console.log('测试行人模型已创建，并应用行走动画');\n            } catch (error) {\n              console.error('创建测试行人模型失败:', error);\n            }\n          }, 5000); // 延迟5秒后创建测试模型，确保场景已初始化\n        }\n      } else {\n        console.warn('行人模型不包含动画');\n      }\n\n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n\n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n\n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = type => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  try {\n    // 创建一个新的警告标记\n    const sprite = createTextSprite(text);\n    sprite.position.set(position.x, 10, -position.y); // 设置位置，稍微升高以便可见\n\n    // 为标记添加一个定时器，在1秒后自动移除\n    setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n        scene.remove(sprite);\n      }\n    }, 100);\n\n    // 将标记添加到场景中\n    scene.add(sprite);\n    console.log('添加场景事件标记:', {\n      位置: position,\n      文本: text,\n      颜色: color\n    });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = converterInstance => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n\n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach(lightObj => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型 - 只为hasTrafficLight为true的路口创建红绿灯\n  intersectionsData.intersections.forEach(intersection => {\n    // 检查路口是否有红绿灯属性，如果没有或为false，则跳过\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n\n    // 确认路口有经纬度和ID\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n\n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n\n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n\n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n\n        // *** 重要：设置为可被交互 ***\n        trafficLightModel.traverse(child => {\n          // 设置所有子对象可被点击\n          if (child.isMesh) {\n            // 确保材质能够被射线检测到\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n\n            // 设置较高的渲染顺序\n            child.renderOrder = 100;\n          }\n        });\n\n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n\n        // // 添加一个专门用于点击的大型碰撞体\n        // const colliderGeometry = new THREE.SphereGeometry(5, 12, 12);\n        // const colliderMaterial = new THREE.MeshBasicMaterial({\n        //   color: 0xff00ff,\n        //   transparent: true,\n        //   opacity: 0.0,  // 完全透明\n        //   depthWrite: false\n        // });\n\n        // const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n        // collider.name = `交通灯碰撞体-${intersection.name}`;\n        // collider.userData = {\n        //   type: 'trafficLight',\n        //   interId: intersection.interId,\n        //   name: intersection.name,\n        //   isCollider: true\n        // };\n\n        // trafficLightModel.add(collider);\n\n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n\n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = converterInstance => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({\n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n\n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n\n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n\n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,\n    // 完全透明\n    depthWrite: false\n  });\n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  trafficLightModel.add(collider);\n\n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n\n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n\n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: 0xFF0000\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n\n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  trafficLightModel.add(lightMesh);\n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = phaseId => {\n  switch (phaseId) {\n    case '1':\n      return '北进口左转';\n    case '2':\n      return '北进口直行';\n    case '3':\n      return '北进口右转';\n    case '5':\n      return '东进口左转';\n    case '6':\n      return '东进口直行';\n    case '7':\n      return '东进口右转';\n    case '9':\n      return '南进口左转';\n    case '10':\n      return '南进口直行';\n    case '11':\n      return '南进口右转';\n    case '13':\n      return '西进口左转';\n    case '14':\n      return '西进口直行';\n    case '15':\n      return '西进口右转';\n    default:\n      return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = dirCode => {\n  switch (dirCode) {\n    case 'N':\n      return '北向南';\n    case 'S':\n      return '南向北';\n    case 'E':\n      return '东向西';\n    case 'W':\n      return '西向东';\n    case 'NE':\n      return '东北向西南';\n    case 'NW':\n      return '西北向东南';\n    case 'SE':\n      return '东南向西北';\n    case 'SW':\n      return '西南向东北';\n    default:\n      return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n\n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = (event.clientX - rect.left) / container.clientWidth * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n\n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Line.threshold = 1;\n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  console.log('鼠标点击位置:', mouseX, mouseY);\n\n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n\n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'userData:', intersect.object.userData);\n    });\n\n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n\n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n\n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n\n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  console.log('射线检测到的对象数量:', intersects.length);\n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', 'userData:', obj.userData, '距离:', intersect.distance);\n    });\n\n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        console.log('检测到红绿灯点击, 路口ID:', interId);\n\n        // 显示弹窗\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n\n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n\n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.1; // 设置一个阈值，只有距离小于此值的才会被选中\n\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n\n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n\n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      console.log(`路口 ${interId} 的距离:`, distance);\n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = {\n          interId,\n          distance\n        };\n      }\n    }\n  });\n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n\n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  console.log('未检测到任何红绿灯点击');\n\n  // 如果用户点击了其他任何地方，关闭现有的弹窗\n  if (setPopoverState) {\n    setPopoverState(prev => {\n      if (prev.visible) {\n        return {\n          ...prev,\n          visible: false\n        };\n      }\n      return prev;\n    });\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = setPopoverState => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n\n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n\n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({\n    ...prev,\n    visible: false\n  }));\n  console.log('弹窗已关闭');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = interId => {\n  try {\n    var _document$querySelect, _document$querySelect2;\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n\n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      return false;\n    }\n\n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n\n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 创建弹出窗口内容\n    let content;\n    if (stateInfo && stateInfo.phases) {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '220px',\n          maxHeight: '300px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          },\n          children: [intersection.name, \" (ID: \", interId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2813,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                break;\n            }\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '6px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '4px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: getPhaseDirection(phase.phaseId)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2839,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2843,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 3px',\n                    borderRadius: '2px'\n                  },\n                  children: phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2844,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2842,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2855,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2856,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2854,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2832,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2822,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '6px',\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2862,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2812,\n        columnNumber: 9\n      }, this);\n    } else {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          maxWidth: '200px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '8px'\n          },\n          children: intersection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2870,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u8DEF\\u53E3ID: \", interId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2871,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2872,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2869,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2;\n    const centerY = window.innerHeight / 2;\n\n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = (_document$querySelect = document.querySelector('#root')) === null || _document$querySelect === void 0 ? void 0 : (_document$querySelect2 = _document$querySelect.__REACT_INSTANCE) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.setTrafficLightPopover;\n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: {\n          x: centerX,\n          y: centerY\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      document.body.appendChild(popover);\n\n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  return list;\n};\n\n// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n\n//   console.log('开始调试红绿灯模型...');\n\n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n\n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n\n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n\n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n\n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n\n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n\n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n\n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n\n//       // 添加到场景\n//       scene.add(highlightMesh);\n\n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n\n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n\n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n\n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = interId => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    console.log('当前trafficLightStates大小:', trafficLightStates.size);\n\n    // 如果没有指定ID，则使用第一个红绿灯\n    if (!interId && trafficLightsMap.size > 0) {\n      interId = String(Array.from(trafficLightsMap.keys())[0]);\n      console.log('未指定ID，使用第一个红绿灯ID:', interId);\n    }\n\n    // 输出所有可用的红绿灯ID用于调试\n    console.log('所有可用的红绿灯ID:');\n    trafficLightsMap.forEach((light, id) => {\n      var _light$intersection;\n      console.log(`- ${id} (${typeof id}): ${((_light$intersection = light.intersection) === null || _light$intersection === void 0 ? void 0 : _light$intersection.name) || '未知'}`);\n    });\n\n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n\n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n\n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n\n    // 创建更新函数\n    const updateTrafficLightPopover = () => {\n      var _window$currentPopove;\n      // 确保当前弹窗仍然可见\n      if (!window._setTrafficLightPopover) return;\n      const currentId = (_window$currentPopove = window.currentPopoverIdRef) === null || _window$currentPopove === void 0 ? void 0 : _window$currentPopove.current;\n      if (!currentId) return;\n\n      // 重新获取最新的状态信息\n      let stateInfo = trafficLightStates.get(currentId);\n      if (!stateInfo && typeof currentId === 'string') {\n        // 尝试使用数字ID\n        stateInfo = trafficLightStates.get(parseInt(currentId));\n      } else if (!stateInfo && typeof currentId === 'number') {\n        // 尝试使用字符串ID\n        stateInfo = trafficLightStates.get(String(currentId));\n      }\n      if (!stateInfo || !stateInfo.phases || stateInfo.phases.length === 0) {\n        console.log(`路口ID ${currentId} 没有状态信息，跳过更新`);\n        return;\n      }\n\n      // 获取交通灯信息\n      const intersectionLight = trafficLightsMap.get(currentId) || (typeof currentId === 'string' ? trafficLightsMap.get(parseInt(currentId)) : trafficLightsMap.get(String(currentId)));\n      if (!intersectionLight) {\n        console.error(`找不到路口ID ${currentId} 的红绿灯信息，无法更新弹窗`);\n        return;\n      }\n      const intersection = intersectionLight.intersection;\n\n      // 创建更新的弹窗内容\n      const content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '220px',\n          maxHeight: '300px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          },\n          children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", currentId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            let lightText;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                lightText = '绿灯';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                lightText = '黄灯';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                lightText = '红灯';\n                break;\n            }\n            const direction = getPhaseDirection(phase.phaseId);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '6px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '4px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: [direction, \" (\\u76F8\\u4F4DID: \", phase.phaseId, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3181,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3185,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 3px',\n                    borderRadius: '2px'\n                  },\n                  children: lightText\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3186,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3197,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3198,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3196,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3174,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 3150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '6px',\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date(stateInfo.updateTime).toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 3140,\n        columnNumber: 9\n      }, this);\n\n      // 更新弹窗内容\n      window._setTrafficLightPopover(prev => ({\n        ...prev,\n        content: content,\n        phases: stateInfo.phases\n      }));\n      console.log(`已更新路口 ${(intersection === null || intersection === void 0 ? void 0 : intersection.name) || currentId} 的红绿灯状态弹窗`);\n    };\n\n    // 先执行一次初始更新\n    const updateInitialTrafficLightPopover = () => {\n      // 同样，查找红绿灯状态信息时也尝试字符串和数字两种类型\n      let stateInfo = trafficLightStates.get(interId);\n      if (!stateInfo) {\n        // 尝试使用数字ID\n        const numericId = parseInt(interId);\n        stateInfo = trafficLightStates.get(numericId);\n        if (stateInfo) {\n          console.log(`使用数字ID ${numericId} 找到了红绿灯状态信息`);\n        }\n      }\n      console.log(`路口ID ${interId} 的状态信息:`, stateInfo);\n      const intersection = trafficLight.intersection;\n      console.log('路口信息:', intersection);\n\n      // 创建弹窗内容\n      let content;\n      if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n        // 添加一个检查相位数据的打印\n        console.log('相位数据详情:');\n        stateInfo.phases.forEach((phase, index) => {\n          console.log(`相位 ${index + 1}: ID=${phase.phaseId}, 状态=${phase.trafficLight}, 剩余时间=${phase.remainTime}`);\n        });\n        content = /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '8px',\n            width: '220px',\n            maxHeight: '300px',\n            overflowY: 'auto'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            },\n            children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", interId, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              let lightText;\n              switch (phase.trafficLight) {\n                case 'G':\n                  lightColor = '#00ff00';\n                  lightText = '绿灯';\n                  break;\n                case 'Y':\n                  lightColor = '#ffff00';\n                  lightText = '黄灯';\n                  break;\n                case 'R':\n                default:\n                  lightColor = '#ff0000';\n                  lightText = '红灯';\n                  break;\n              }\n              const direction = getPhaseDirection(phase.phaseId);\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '6px',\n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [direction, \" (\\u76F8\\u4F4DID: \", phase.phaseId, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3291,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u706F\\u8272: \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3295,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: lightColor,\n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    },\n                    children: lightText\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3296,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3294,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u5012\\u8BA1\\u65F6: \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3307,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontWeight: 'bold'\n                    },\n                    children: [phase.remainTime, \" \\u79D2\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3308,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3306,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3284,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '6px',\n              fontSize: '10px',\n              color: '#888'\n            },\n            children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date(stateInfo.updateTime).toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3314,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3250,\n          columnNumber: 11\n        }, this);\n      } else {\n        // 如果没有状态信息，创建一个示例内容\n        content = /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '8px',\n            width: '220px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            },\n            children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", interId, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#ff4d4f',\n              fontSize: '12px'\n            },\n            children: \"\\u8BE5\\u8DEF\\u53E3\\u6682\\u65E0\\u7EA2\\u7EFF\\u706F\\u72B6\\u6001\\u4FE1\\u606F\\uFF0C\\u8BF7\\u7B49\\u5F85SPAT\\u6D88\\u606F\\u66F4\\u65B0\\u3002\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3332,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '6px',\n              fontSize: '10px',\n              color: '#888'\n            },\n            children: [\"\\u5F53\\u524D\\u65F6\\u95F4: \", new Date().toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3335,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 3322,\n          columnNumber: 11\n        }, this);\n      }\n\n      // 设置弹窗位置在屏幕中央\n      const x = window.innerWidth / 2;\n      const y = window.innerHeight / 2;\n\n      // 更新弹窗状态\n      if (window._setTrafficLightPopover) {\n        var _stateInfo;\n        console.log('调用_setTrafficLightPopover更新弹窗状态', {\n          visible: true,\n          interId,\n          position: {\n            x,\n            y\n          }\n        });\n        window._setTrafficLightPopover({\n          visible: true,\n          interId: interId,\n          position: {\n            x,\n            y\n          },\n          content: content,\n          phases: ((_stateInfo = stateInfo) === null || _stateInfo === void 0 ? void 0 : _stateInfo.phases) || []\n        });\n        console.log(`已显示路口 ${(intersection === null || intersection === void 0 ? void 0 : intersection.name) || interId} 的红绿灯状态弹窗`);\n\n        // 设置定时更新\n        if (window.trafficLightUpdateTimerRef) {\n          window.trafficLightUpdateTimerRef.current = setInterval(updateTrafficLightPopover, TRAFFIC_LIGHT_UPDATE_INTERVAL * 1000);\n          console.log(`已设置红绿灯状态弹窗定时更新，间隔 ${TRAFFIC_LIGHT_UPDATE_INTERVAL} 秒`);\n        }\n        return true;\n      } else {\n        console.error('无法找到setTrafficLightPopover函数');\n        return false;\n      }\n    };\n    return updateInitialTrafficLightPopover();\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n\n//   console.log('开始生成模拟SPAT数据...');\n\n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n\n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n\n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n\n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n\n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n\n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n\n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n\n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n\n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n\n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n\n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n\n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n\n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n\n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n\n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n\n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = object => {\n  let current = object;\n\n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n\n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n\n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n\n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n\n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n\n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = (x - rect.left) / canvas.clientWidth * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    console.log('归一化坐标:', mouseX, mouseY);\n\n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n\n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n\n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'position:', intersect.object.position.toArray(), 'userData:', intersect.object.userData);\n\n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      return true;\n    }\n\n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', '类型:', obj.type, '位置:', obj.position.toArray(), '距离:', intersect.distance, 'userData:', obj.userData);\n    });\n\n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        var _lightObj$intersectio;\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n\n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n\n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n\n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        if (isVisible) {\n          visibleCount++;\n        }\n        console.log(`红绿灯 ${interId}:`, {\n          名称: ((_lightObj$intersectio = lightObj.intersection) === null || _lightObj$intersectio === void 0 ? void 0 : _lightObj$intersectio.name) || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n\n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  var _trafficLight$interse, _trafficLight$interse2, _trafficLight$interse3;\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch (phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n\n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: (_trafficLight$interse = trafficLight.intersection) === null || _trafficLight$interse === void 0 ? void 0 : _trafficLight$interse.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n\n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = {\n    isLight: true\n  };\n\n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  console.log(`更新路口 ${((_trafficLight$interse2 = trafficLight.intersection) === null || _trafficLight$interse2 === void 0 ? void 0 : _trafficLight$interse2.name) || ((_trafficLight$interse3 = trafficLight.intersection) === null || _trafficLight$interse3 === void 0 ? void 0 : _trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "Select", "Popover", "intersectionsData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "lastPosition", "lastRotation", "ALPHA", "vehicleLastPositions", "Map", "vehicleLastRotations", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "process", "env", "REACT_APP_API_URL", "console", "log", "vehicleModels", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "vehicleId", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "has", "lastPos", "get", "distance", "distanceTo", "MAX_DISTANCE_THRESHOLD", "toFixed", "filteredPos", "Vector3", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "lastRot", "MAX_ANGLE_THRESHOLD", "abs", "TRAFFIC_LIGHT_UPDATE_INTERVAL", "CampusModel", "className", "onCurrentRSUChange", "selectedRSUs", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "mapRef", "lastCameraPosition", "lastCameraTarget", "cameraSmoothing", "prevAnimationTimeRef", "Date", "now", "peopleAnimationMixers", "peopleAnimations", "testPeopleAnimationMixer", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "currentPopoverIdRef", "trafficLightUpdateTimerRef", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "switchToFollowView", "current", "enabled", "switchToGlobalView", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "target", "currentTarget", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "updateMatrix", "updateMatrixWorld", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "相机位置", "toArray", "目标点", "handleMqttMessage", "message", "payload", "JSON", "parse", "_payload$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "设备MAC", "消息时间戳", "最新时间戳", "时间戳", "是否为最新", "participants", "rsuid", "for<PERSON>ach", "participant", "id", "partPtcId", "type", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "model", "newModel", "height", "rotation", "length", "mixer", "AnimationMixer", "walkAnimation", "action", "clipAction", "setEffectiveTimeScale", "setEffectiveWeight", "setLoop", "LoopRepeat", "play", "动画名称", "播放状态", "isRunning", "循环模式", "loop", "混合器ID", "uuid", "add", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "Set", "map", "p", "modelData", "remove", "delete", "bsmData", "bsmid", "newState", "partLong", "partLat", "postMessage", "source", "initialPosition", "initialRotation", "newPosition", "vehicleObj", "newVehicleModel", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "emissive", "Color", "transparent", "needsUpdate", "speedLabel", "createTextSprite", "round", "r", "g", "b", "a", "textColor", "renderOrder", "opacity", "is<PERSON><PERSON>", "Out", "filteredPosition", "dispose", "timeSinceLastUpdate", "undefined", "originalTran<PERSON>arent", "originalOpacity", "m", "geometry", "phasesInfo", "phase", "phaseId", "toString", "direction", "trafficDirec", "getDirectionFromCode", "getPhaseDirection", "lightState", "trafficLight", "remainTime", "parseInt", "phaseInfo", "push", "trafficLightKey", "String", "trafficLightModel", "updateTrafficLightVisual", "prev", "warn", "<PERSON><PERSON><PERSON>", "strId", "numId", "updateTime", "setTimeout", "showTrafficLightPopup", "rsiData", "rsuId", "events", "rtes", "event", "eventId", "rteId", "eventType", "description", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "事件ID", "事件类型", "事件说明", "开始时间", "结束时间", "位置", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "stringify", "onerror", "onclose", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "setIsVehicleLoaded", "xhr", "loaded", "total", "initializeScene", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "currentTime", "deltaTime", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "targetCameraPosition", "targetLookAt", "lerp", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "clearInterval", "close", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "clear", "lightObj", "stopAllAction", "testPeopleModel", "handleMainVehicleChange", "intervalId", "setInterval", "timer", "createTrafficLights", "clearTimeout", "handleClick", "clientX", "clientY", "handleMouseClick", "initScene", "createSimpleTrafficLight", "BoxGeometry", "MeshBasicMaterial", "<PERSON><PERSON>", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "addClickHelpers", "helperGeometry", "SphereGeometry", "helperMaterial", "depthWrite", "helper<PERSON><PERSON>", "userData", "isClickHelper", "size", "firstIntersection", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "onChange", "options", "label", "bordered", "dropdownStyle", "maxHeight", "ref", "max<PERSON><PERSON><PERSON>", "right", "background", "onClick", "handleClosePopover", "_c", "text", "parameters", "params", "fontFace", "borderThickness", "borderColor", "canvas", "document", "createElement", "context", "getContext", "font", "textWidth", "measureText", "textBaseline", "radius", "beginPath", "moveTo", "lineTo", "arcTo", "closePath", "strokeStyle", "lineWidth", "stroke", "fillStyle", "fill", "textAlign", "fillText", "texture", "CanvasTexture", "minFilter", "LinearFilter", "spriteMaterial", "SpriteMaterial", "sprite", "Sprite", "depthTest", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "vehicleGltf", "cyclistGltf", "peopleGltf", "trafficLightGltf", "all", "loadAsync", "materia", "animations", "anim", "名称", "轨道数", "tracks", "时长", "walkAction", "err", "types", "parent", "文本", "颜色", "converterInstance", "createFallbackTrafficLights", "hasTrafficLight", "side", "DoubleSide", "colliderGeometry", "colliderMaterial", "collider", "isCollider", "lightGeometry", "lightMaterial", "<PERSON><PERSON><PERSON>", "dirCode", "container", "sceneInstance", "cameraInstance", "setPopoverState", "rect", "getBoundingClientRect", "mouseX", "clientWidth", "mouseY", "clientHeight", "raycaster", "Raycaster", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "trafficLightObjects", "trafficLightIntersects", "intersectObjects", "intersect", "index", "object", "obj", "getTrafficLightFromObject", "correctId", "intersects", "closestLight", "worldPos", "getWorldPosition", "screenPos", "project", "dx", "dy", "sqrt", "testTrafficLightClick", "_document$querySelect", "_document$querySelect2", "light", "lightModel", "stateInfo", "overflowY", "marginBottom", "borderBottom", "paddingBottom", "lightColor", "justifyContent", "marginTop", "toLocaleTimeString", "centerX", "centerY", "__REACT_INSTANCE", "popover", "innerHTML", "body", "closeButton", "listTrafficLights", "list", "from", "keys", "_light$intersection", "numericId", "updateTrafficLightPopover", "_window$currentPopove", "currentId", "intersectionLight", "lightText", "updateInitialTrafficLightPopover", "_stateInfo", "testClickDetection", "tlIntersects", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "distanceToCamera", "可见性", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "isLight", "emissiveIntensity", "PointLight", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  const lastPos = vehicleLastPositions.get(vehicleId);\n  \n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n  \n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  \n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  \n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const lastRot = vehicleLastRotations.get(vehicleId);\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  \n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n  \n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n  \n  // 添加动画时间引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  \n  // 添加行人动画混合器Map\n  const peopleAnimationMixers = new Map();\n  \n  // 添加行人动画数组\n  const peopleAnimations = [];\n  \n  // 添加测试行人动画混合器\n  const testPeopleAnimationMixer = useRef(null);\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n  \n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n  \n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n  \n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n  \n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n      \n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n      \n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n        \n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos)\n          .to({ x: 0, y: 300, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.position.copy(currentPos);\n          })\n          .start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp)\n          .to({ x: 0, y: 1, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.up.copy(currentUp);\n          })\n          .start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n        \n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget)\n          .to({ x: 0, y: 0, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            controls.target.copy(currentTarget);\n            // 确保相机始终朝向目标点\n            cameraRef.current.lookAt(controls.target);\n            controls.update();\n          })\n          .start();\n\n        // 启用控制器\n        controls.enabled = true;\n        \n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x+50, 70, -modelCoords.y+50);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        console.log('收到RSM消息:', payload);\n        \n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n        \n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n        \n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          console.log('忽略过期的RSM消息:', {\n            设备MAC: deviceMac,\n            消息时间戳: messageTimestamp,\n            最新时间戳: lastTimestamp\n          });\n      return;\n    }\n    \n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        \n        console.log('RSM消息时间戳更新:', {\n          设备MAC: deviceMac,\n          时间戳: messageTimestamp,\n          是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        });\n        \n        const participants = payload.data?.participants || [];\n        const rsuid = payload.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id =  deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n          // 解析位置和状态信息\n          const state = {\n            longitude: parseFloat(participant.partPosLong),\n            latitude: parseFloat(participant.partPosLat),\n            speed: parseFloat(participant.partSpeed),\n            heading: parseFloat(participant.partHeading)\n          };\n          \n          const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n          \n          // 根据类型选择对应的预加载模型\n          let preloadedModel;\n          switch (type) {\n            case '1': // 机动车\n              preloadedModel = preloadedVehicleModel;\n              break;\n            case '2': // 非机动车\n              preloadedModel = preloadedCyclistModel;\n              break;\n            case '3': // 行人\n              preloadedModel = preloadedPeopleModel;\n              break;\n            default:\n              return; // 跳过未知类型\n          }\n          \n          // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n            // 创建新模型实例\n            const newModel = preloadedModel.clone();\n            // 根据类型调整高度\n            const height = type === '3' ? 0.5 : 1.0; // 行人模型贴近地面\n            newModel.position.set(modelPos.x, height, -modelPos.y);\n            newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            \n            // 如果是行人模型，设置动画\n            if (type === '3' && peopleAnimations.length > 0) {\n              try {\n                // 创建动画混合器\n                const mixer = new THREE.AnimationMixer(newModel);\n                \n                // 获取动画剪辑\n                const walkAnimation = peopleAnimations[0]; // 选择第一个动画\n                console.log(`为行人ID ${id} 应用动画:`, walkAnimation.name || '无名称动画');\n                \n                // 创建动作并播放\n                const action = mixer.clipAction(walkAnimation);\n                action.setEffectiveTimeScale(1.0); // 确保时间比例为1\n                action.setEffectiveWeight(1.0);    // 确保权重为1\n                action.setLoop(THREE.LoopRepeat);  // 设置循环模式\n                action.play();\n                \n                // 将混合器保存到Map中\n                peopleAnimationMixers.set(id, mixer);\n                \n                console.log(`为行人 ${id} 创建动画混合器成功，动画状态:`, {\n                  动画名称: walkAnimation.name || '无名称',\n                  播放状态: action.isRunning() ? '运行中' : '已停止',\n                  循环模式: action.loop,\n                  混合器ID: mixer.uuid\n                });\n              } catch (error) {\n                console.error(`为行人 ${id} 创建动画时出错:`, error);\n              }\n            }\n            \n            scene.add(newModel);\n            \n            vehicleModels.set(id, {\n              model: newModel,\n              lastUpdate: now,\n              type: type\n            });\n          } else if (model) {\n            // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 0.5 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            \n            // 确保更新矩阵\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n            \n            // 如果是行人模型，检查并确保动画正在播放\n            if (model.type === '3') {\n              if (!peopleAnimationMixers.has(id) && peopleAnimations.length > 0) {\n                // 如果没有动画混合器，创建一个\n                try {\n                  const mixer = new THREE.AnimationMixer(model.model);\n                  const walkAnimation = peopleAnimations[0];\n                  const action = mixer.clipAction(walkAnimation);\n                  action.setLoop(THREE.LoopRepeat);\n                  action.play();\n                  peopleAnimationMixers.set(id, mixer);\n                  console.log(`为现有行人 ${id} 补充创建动画混合器`);\n                } catch (error) {\n                  console.error(`为现有行人 ${id} 创建动画混合器失败:`, error);\n                }\n              }\n            }\n          }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        console.log('收到BSM消息:', payload);\n        \n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n        \n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n        \n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid, // 直接传递车辆ID\n          data: {       // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n        \n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n        \n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n        \n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n        \n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        \n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          \n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n          \n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n              \n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n              \n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n          \n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n          \n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          \n          scene.add(newVehicleModel);\n          \n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n          \n          console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position)\n            .to({ y: 0.5 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .start();\n          \n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({ opacity: 0.6 })\n                .to({ opacity: 1.0 }, 500)\n                .easing(TWEEN.Easing.Quadratic.Out)\n                .onUpdate(function() {\n                  child.material.opacity = this.opacity;\n                  child.material.needsUpdate = true;\n                })\n                .start();\n            }\n          });\n          \n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({ opacity: 0.6 })\n            .to({ opacity: 1.0 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .onUpdate(function() {\n              speedLabel.material.opacity = this.opacity;\n              speedLabel.material.needsUpdate = true;\n            })\n            .start();\n          \n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n          \n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n          \n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n          \n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n          \n          console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n        \n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 2000; // 增加到10秒，避免频繁清理造成闪烁\n        \n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n          \n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1 ;\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n                \n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n            \n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n            \n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            \n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        \n        return;\n      }\n      \n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        console.log('收到SPAT消息:', message);\n        \n        try {\n          const payload = JSON.parse(message);\n          \n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n          \n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n          \n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            console.log('忽略过期的SPAT消息:', {\n              设备MAC: deviceMac,\n              消息时间戳: messageTimestamp,\n              最新时间戳: lastTimestamp\n            });\n            return;\n          }\n          \n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n          \n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              \n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              \n              console.log(`处理路口ID: ${interId} 的SPAT消息`);\n              \n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                \n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  \n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? \n                    getDirectionFromCode(phase.trafficDirec) : \n                    getPhaseDirection(phaseId);\n                  \n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  \n                  console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n                  \n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n                  \n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n                  \n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  \n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  \n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n                    \n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n                \n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                \n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n                  \n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && \n                     (window.currentPopoverIdRef.current === modelKey || \n                      window.currentPopoverIdRef.current === String(modelKey) || \n                      window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    \n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n                    \n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n      }\n      \n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        console.log('收到RSI消息:', payload);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        \n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        \n        return;\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        console.log('收到场景事件消息:', payload);\n        \n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: payload.type,\n        data: payload\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n        \n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          \n          // 检查scene是否初始化\n          if (scene) {\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      // 更新行人动画混合器\n      const currentTime = Date.now();\n      const deltaTime = (currentTime - prevAnimationTimeRef.current) * 0.001; // 转换为秒\n      prevAnimationTimeRef.current = currentTime;\n      \n      // 更新所有行人动画\n      peopleAnimationMixers.forEach((mixer) => {\n        mixer.update(deltaTime);\n      });\n      \n      // 更新测试行人模型的动画（如果存在）\n      if (testPeopleAnimationMixer) {\n        testPeopleAnimationMixer.update(deltaTime);\n      }\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          200,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n        \n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        \n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n        \n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n        \n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n      \n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n      \n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach((lightObj) => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n      \n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n      \n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n\n      // 清理动画混合器\n      peopleAnimationMixers.forEach((mixer) => {\n        mixer.stopAllAction();\n      });\n      peopleAnimationMixers.clear();\n      \n      // 清理测试行人模型\n      if (testPeopleModel && scene) {\n        scene.remove(testPeopleModel);\n        if (testPeopleAnimationMixer) {\n          testPeopleAnimationMixer.stopAllAction();\n          testPeopleAnimationMixer = null;\n        }\n        testPeopleModel = null;\n      }\n      \n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n    \n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n    \n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n    \n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n    \n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      \n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n  \n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          console.log('检测到点击事件', event.clientX, event.clientY);\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        } else {\n          console.warn('点击事件处理失败: scene或camera未初始化');\n        }\n      };\n      \n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n      \n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n      \n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n    \n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    \n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n    \n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,//\n          transparent: false,\n          opacity: 0.1,  // 几乎透明\n          depthWrite: false\n        });\n        \n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置\n        \n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n        \n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        \n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500);  // 延迟略长于debugTrafficLights\n    \n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n    \n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n  \n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        const firstIntersection = intersectionsData.intersections[0];\n        console.log('自动选择第一个路口:', firstIntersection.name);\n        \n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(firstIntersection.name);\n        }, 2000);\n        \n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n\n  return (\n    <>\n      <span style={labelStyle}>区域选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择区域位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n        value={selectedIntersection ? selectedIntersection.name : undefined}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      \n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '4px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n            padding: '0',\n            maxWidth: '240px', // 缩小最大宽度\n            fontSize: '12px' // 缩小字体\n          }}\n        >\n          {trafficLightPopover.content}\n          <button \n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '12px',\n              cursor: 'pointer',\n              padding: '2px 6px'\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n      \n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 16, // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    backgroundColor: parameters.backgroundColor || { r: 255, g: 255, b: 255, a: 0.8 },\n    textColor: parameters.textColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  \n  // 设置字体\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  \n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n  \n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  \n  canvas.width = width;\n  canvas.height = height;\n  \n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n  \n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n  \n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n  \n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n  \n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n  \n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n  \n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n  \n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = { \n    text: text,\n    params: params\n  };\n  \n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    try {\n      const [vehicleGltf, cyclistGltf, peopleGltf, trafficLightGltf] = await Promise.all([\n        loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)\n      ]);\n\n      // 处理机动车模型\n      preloadedVehicleModel = vehicleGltf.scene;\n      preloadedVehicleModel.traverse((child) => {\n        if (child.isMesh) {\n            const newMaterial = new THREE.MeshStandardMaterial({\n            color: 0xff0000,  // 0xff0000, //红色 0xffffff,白色\n            metalness: 0.2,\n            roughness: 0.1,\n            envMapIntensity: 1.0\n          });\n\n            // 保留原始贴图\n            if (child.material.map) {\n              newMaterial.map = child.material.map;\n            }\n            child.materia = newMaterial;\n        }\n      });\n\n      // 处理非机动车模型\n      preloadedCyclistModel = cyclistGltf.scene;\n      // 设置非机动车模型的缩放\n      preloadedCyclistModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedCyclistModel.traverse((child) => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n\n      // 处理行人模型\n      preloadedPeopleModel = peopleGltf.scene;\n      // 设置行人模型的缩放\n      preloadedPeopleModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedPeopleModel.traverse((child) => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n        \n        // 检查并存储行人动画\n        if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n          peopleAnimations = peopleGltf.animations;\n          console.log(`行人模型包含 ${peopleAnimations.length} 个动画:`, \n                     peopleAnimations.map(anim => ({\n                       名称: anim.name || '无名称动画',\n                       轨道数: anim.tracks ? anim.tracks.length : 0,\n                       时长: anim.duration\n                     })));\n            \n          // 创建一个测试行人模型以验证动画\n          if (scene) {\n            setTimeout(() => {\n              try {\n                // 克隆行人模型并放置在场景中央\n                testPeopleModel = preloadedPeopleModel.clone();\n                testPeopleModel.position.set(20, 0.5, 20); // 放在场景中可见位置\n                \n                // 创建动画混合器\n                testPeopleAnimationMixer = new THREE.AnimationMixer(testPeopleModel);\n                \n                // 应用行走动画\n                const walkAction = testPeopleAnimationMixer.clipAction(peopleAnimations[0]);\n                walkAction.setLoop(THREE.LoopRepeat);\n                walkAction.play();\n                \n                // 添加到场景\n                scene.add(testPeopleModel);\n                \n                console.log('测试行人模型已创建，并应用行走动画');\n              } catch (error) {\n                console.error('创建测试行人模型失败:', error);\n              }\n            }, 5000); // 延迟5秒后创建测试模型，确保场景已初始化\n          }\n        } else {\n          console.warn('行人模型不包含动画');\n        }\n        \n        // 处理红绿灯模型\n        preloadedTrafficLightModel = trafficLightGltf.scene;\n        // 设置红绿灯模型的缩放\n        preloadedTrafficLightModel.scale.set(6, 6, 6);\n        // 保持原始材质\n        preloadedTrafficLightModel.traverse((child) => {\n          if (child.isMesh && child.material) {\n            // 设置材质属性\n            child.material.metalness = 0.2;\n            child.material.roughness = 0.3;\n            child.material.envMapIntensity = 1.2;\n        }\n      });\n\n      console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n      \n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n        \n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  \n  try {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在1秒后自动移除\n  setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n    scene.remove(sprite);\n      }\n  }, 100);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  console.log('添加场景事件标记:', {\n    位置: position,\n    文本: text,\n    颜色: color\n  });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  \n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  \n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n  \n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型 - 只为hasTrafficLight为true的路口创建红绿灯\n  intersectionsData.intersections.forEach(intersection => {\n    // 检查路口是否有红绿灯属性，如果没有或为false，则跳过\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n    \n    // 确认路口有经纬度和ID\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      \n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n        \n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n        \n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n        \n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n        \n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n        \n        // *** 重要：设置为可被交互 ***\n        trafficLightModel.traverse(child => {\n          // 设置所有子对象可被点击\n          if (child.isMesh) {\n            // 确保材质能够被射线检测到\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            \n            // 设置较高的渲染顺序\n            child.renderOrder = 100;\n          }\n        });\n        \n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        \n        // // 添加一个专门用于点击的大型碰撞体\n        // const colliderGeometry = new THREE.SphereGeometry(5, 12, 12);\n        // const colliderMaterial = new THREE.MeshBasicMaterial({\n        //   color: 0xff00ff,\n        //   transparent: true,\n        //   opacity: 0.0,  // 完全透明\n        //   depthWrite: false\n        // });\n        \n        // const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n        // collider.name = `交通灯碰撞体-${intersection.name}`;\n        // collider.userData = {\n        //   type: 'trafficLight',\n        //   interId: intersection.interId,\n        //   name: intersection.name,\n        //   isCollider: true\n        // };\n        \n        // trafficLightModel.add(collider);\n        \n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n        \n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        \n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n  \n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance) => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({ \n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n  \n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n  \n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n  \n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n  \n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,  // 完全透明\n    depthWrite: false\n  });\n  \n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  \n  trafficLightModel.add(collider);\n  \n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n  \n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n  \n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n  \n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  trafficLightModel.add(lightMesh);\n  \n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  switch(phaseId) {\n    case '1': return '北进口左转';\n    case '2': return '北进口直行';\n    case '3': return '北进口右转';\n    case '5': return '东进口左转';\n    case '6': return '东进口直行';\n    case '7': return '东进口右转';\n    case '9': return '南进口左转';\n    case '10': return '南进口直行';\n    case '11': return '南进口右转';\n    case '13': return '西进口左转';\n    case '14': return '西进口直行';\n    case '15': return '西进口右转';\n    default: return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = (dirCode) => {\n  switch(dirCode) {\n    case 'N': return '北向南';\n    case 'S': return '南向北';\n    case 'E': return '东向西';\n    case 'W': return '西向东';\n    case 'NE': return '东北向西南';\n    case 'NW': return '西北向东南';\n    case 'SE': return '东南向西北';\n    case 'SW': return '西南向东北';\n    default: return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  \n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n  \n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n  \n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Line.threshold = 1;\n  \n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  \n  console.log('鼠标点击位置:', mouseX, mouseY);\n  \n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  \n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n  \n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  \n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, \n                 intersect.object.name || '无名称', \n                 '距离:', intersect.distance,\n                 'userData:', intersect.object.userData);\n    });\n    \n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    \n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n      \n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n      \n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n  \n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  \n  console.log('射线检测到的对象数量:', intersects.length);\n  \n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', \n                  'userData:', obj.userData, \n                  '距离:', intersect.distance);\n    });\n    \n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        console.log('检测到红绿灯点击, 路口ID:', interId);\n        \n        // 显示弹窗\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n  \n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n  \n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.1; // 设置一个阈值，只有距离小于此值的才会被选中\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n      \n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n      \n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      \n      console.log(`路口 ${interId} 的距离:`, distance);\n      \n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = { interId, distance };\n      }\n    }\n  });\n  \n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n    \n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  \n  console.log('未检测到任何红绿灯点击');\n  \n  // 如果用户点击了其他任何地方，关闭现有的弹窗\n  if (setPopoverState) {\n    setPopoverState(prev => {\n      if (prev.visible) {\n        return { ...prev, visible: false };\n      }\n      return prev;\n    });\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n  \n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n  \n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({ ...prev, visible: false }));\n  console.log('弹窗已关闭');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = (interId) => {\n  try {\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      \n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      \n      return false;\n    }\n    \n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n    \n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 创建弹出窗口内容\n    let content;\n    \n    if (stateInfo && stateInfo.phases) {\n      content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection.name} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              switch (phase.trafficLight) {\n                case 'G': lightColor = '#00ff00'; break;\n                case 'Y': lightColor = '#ffff00'; break;\n                case 'R': default: lightColor = '#ff0000'; break;\n              }\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {getPhaseDirection(phase.phaseId)}\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      content = (\n        <div style={{ padding: '8px', maxWidth: '200px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>\n          <div>路口ID: {interId}</div>\n          <div>当前无信号灯状态信息</div>\n        </div>\n      );\n    }\n    \n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2;\n    const centerY = window.innerHeight / 2;\n    \n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;\n    \n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: { x: centerX, y: centerY },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      \n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      \n      document.body.appendChild(popover);\n      \n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      \n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  \n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  \n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  \n  return list;\n};\n\n// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n\n//   console.log('开始调试红绿灯模型...');\n  \n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n  \n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n  \n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n      \n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n  \n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n  \n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n      \n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n      \n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n      \n//       // 添加到场景\n//       scene.add(highlightMesh);\n      \n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n      \n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n      \n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n  \n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = (interId) => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    \n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    console.log('当前trafficLightStates大小:', trafficLightStates.size);\n    \n    // 如果没有指定ID，则使用第一个红绿灯\n    if (!interId && trafficLightsMap.size > 0) {\n      interId = String(Array.from(trafficLightsMap.keys())[0]);\n      console.log('未指定ID，使用第一个红绿灯ID:', interId);\n    }\n    \n    // 输出所有可用的红绿灯ID用于调试\n    console.log('所有可用的红绿灯ID:');\n    trafficLightsMap.forEach((light, id) => {\n      console.log(`- ${id} (${typeof id}): ${light.intersection?.name || '未知'}`);\n    });\n    \n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      \n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    \n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    \n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n    \n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n    \n    // 创建更新函数\n    const updateTrafficLightPopover = () => {\n      // 确保当前弹窗仍然可见\n      if (!window._setTrafficLightPopover) return;\n      \n      const currentId = window.currentPopoverIdRef?.current;\n      if (!currentId) return;\n      \n      // 重新获取最新的状态信息\n      let stateInfo = trafficLightStates.get(currentId);\n      if (!stateInfo && typeof currentId === 'string') {\n        // 尝试使用数字ID\n        stateInfo = trafficLightStates.get(parseInt(currentId));\n      } else if (!stateInfo && typeof currentId === 'number') {\n        // 尝试使用字符串ID\n        stateInfo = trafficLightStates.get(String(currentId));\n      }\n      \n      if (!stateInfo || !stateInfo.phases || stateInfo.phases.length === 0) {\n        console.log(`路口ID ${currentId} 没有状态信息，跳过更新`);\n        return;\n      }\n      \n      // 获取交通灯信息\n      const intersectionLight = trafficLightsMap.get(currentId) || \n                               (typeof currentId === 'string' ? trafficLightsMap.get(parseInt(currentId)) : \n                                trafficLightsMap.get(String(currentId)));\n                                \n      if (!intersectionLight) {\n        console.error(`找不到路口ID ${currentId} 的红绿灯信息，无法更新弹窗`);\n        return;\n      }\n      \n      const intersection = intersectionLight.intersection;\n      \n      // 创建更新的弹窗内容\n      const content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection?.name || '未知路口'} (ID: {currentId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              let lightText;\n              \n              switch (phase.trafficLight) {\n                case 'G':\n                  lightColor = '#00ff00';\n                  lightText = '绿灯';\n                  break;\n                case 'Y':\n                  lightColor = '#ffff00';\n                  lightText = '黄灯';\n                  break;\n                case 'R':\n                default:\n                  lightColor = '#ff0000';\n                  lightText = '红灯';\n                  break;\n              }\n              \n              const direction = getPhaseDirection(phase.phaseId);\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {direction} (相位ID: {phase.phaseId})\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {lightText}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date(stateInfo.updateTime).toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n      \n      // 更新弹窗内容\n      window._setTrafficLightPopover(prev => ({\n        ...prev,\n        content: content,\n        phases: stateInfo.phases\n      }));\n      \n      console.log(`已更新路口 ${intersection?.name || currentId} 的红绿灯状态弹窗`);\n    };\n    \n    // 先执行一次初始更新\n    const updateInitialTrafficLightPopover = () => {\n      // 同样，查找红绿灯状态信息时也尝试字符串和数字两种类型\n      let stateInfo = trafficLightStates.get(interId);\n      if (!stateInfo) {\n        // 尝试使用数字ID\n        const numericId = parseInt(interId);\n        stateInfo = trafficLightStates.get(numericId);\n        \n        if (stateInfo) {\n          console.log(`使用数字ID ${numericId} 找到了红绿灯状态信息`);\n        }\n      }\n      \n      console.log(`路口ID ${interId} 的状态信息:`, stateInfo);\n      \n      const intersection = trafficLight.intersection;\n      console.log('路口信息:', intersection);\n      \n      // 创建弹窗内容\n      let content;\n      \n      if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n        // 添加一个检查相位数据的打印\n        console.log('相位数据详情:');\n        stateInfo.phases.forEach((phase, index) => {\n          console.log(`相位 ${index+1}: ID=${phase.phaseId}, 状态=${phase.trafficLight}, 剩余时间=${phase.remainTime}`);\n        });\n        \n        content = (\n          <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n            <div style={{ \n              fontWeight: 'bold', \n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            }}>\n              {intersection?.name || '未知路口'} (ID: {interId})\n            </div>\n            <div>\n              {stateInfo.phases.map((phase, index) => {\n                let lightColor;\n                let lightText;\n                \n                switch (phase.trafficLight) {\n                  case 'G':\n                    lightColor = '#00ff00';\n                    lightText = '绿灯';\n                    break;\n                  case 'Y':\n                    lightColor = '#ffff00';\n                    lightText = '黄灯';\n                    break;\n                  case 'R':\n                  default:\n                    lightColor = '#ff0000';\n                    lightText = '红灯';\n                    break;\n                }\n                \n                const direction = getPhaseDirection(phase.phaseId);\n                \n                return (\n                  <div key={index} style={{ \n                    marginBottom: '6px', \n                    backgroundColor: 'rgba(255,255,255,0.1)',\n                    padding: '4px',\n                    borderRadius: '4px',\n                    fontSize: '12px'\n                  }}>\n                    <div style={{ fontWeight: 'bold' }}>\n                      {direction} (相位ID: {phase.phaseId})\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <span>灯色: </span>\n                      <span style={{ \n                        color: lightColor, \n                        fontWeight: 'bold',\n                        backgroundColor: 'rgba(0,0,0,0.3)',\n                        padding: '0 3px',\n                        borderRadius: '2px'\n                      }}>\n                        {lightText}\n                      </span>\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <span>倒计时: </span>\n                      <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n            <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n              更新时间: {new Date(stateInfo.updateTime).toLocaleTimeString()} (自动刷新)\n            </div>\n          </div>\n        );\n      } else {\n        // 如果没有状态信息，创建一个示例内容\n        content = (\n          <div style={{ padding: '8px', width: '220px' }}>\n            <div style={{ \n              fontWeight: 'bold', \n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            }}>\n              {intersection?.name || '未知路口'} (ID: {interId})\n            </div>\n            <div style={{ color: '#ff4d4f', fontSize: '12px' }}>\n              该路口暂无红绿灯状态信息，请等待SPAT消息更新。\n            </div>\n            <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n              当前时间: {new Date().toLocaleTimeString()}\n            </div>\n          </div>\n        );\n      }\n      \n      // 设置弹窗位置在屏幕中央\n      const x = window.innerWidth / 2;\n      const y = window.innerHeight / 2;\n      \n      // 更新弹窗状态\n      if (window._setTrafficLightPopover) {\n        console.log('调用_setTrafficLightPopover更新弹窗状态', {\n          visible: true,\n          interId,\n          position: { x, y }\n        });\n        \n        window._setTrafficLightPopover({\n          visible: true,\n          interId: interId,\n          position: { x, y },\n          content: content,\n          phases: stateInfo?.phases || []\n        });\n        \n        console.log(`已显示路口 ${intersection?.name || interId} 的红绿灯状态弹窗`);\n        \n        // 设置定时更新\n        if (window.trafficLightUpdateTimerRef) {\n          window.trafficLightUpdateTimerRef.current = setInterval(updateTrafficLightPopover, TRAFFIC_LIGHT_UPDATE_INTERVAL * 1000);\n          console.log(`已设置红绿灯状态弹窗定时更新，间隔 ${TRAFFIC_LIGHT_UPDATE_INTERVAL} 秒`);\n        }\n        \n        return true;\n      } else {\n        console.error('无法找到setTrafficLightPopover函数');\n        return false;\n      }\n    };\n    \n    return updateInitialTrafficLightPopover();\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n  \n//   console.log('开始生成模拟SPAT数据...');\n  \n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n  \n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n  \n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n    \n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n      \n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n    \n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n      \n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n      \n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n    \n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n    \n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n    \n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n    \n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n  \n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n  \n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n  \n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n  \n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n  \n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n  \n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  \n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n    \n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n    \n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n    \n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n    \n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    \n    console.log('归一化坐标:', mouseX, mouseY);\n    \n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    \n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n    \n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n    \n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    \n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', \n                    '距离:', intersect.distance,\n                    'position:', intersect.object.position.toArray(),\n                    'userData:', intersect.object.userData);\n        \n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      \n      return true;\n    }\n    \n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    \n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', \n                  '类型:', obj.type,\n                  '位置:', obj.position.toArray(),\n                  '距离:', intersect.distance,\n                  'userData:', obj.userData);\n    });\n    \n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    \n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n        \n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n        \n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n        \n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        \n        if (isVisible) {\n          visibleCount++;\n        }\n        \n        console.log(`红绿灯 ${interId}:`, {\n          名称: lightObj.intersection?.name || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    \n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n    \n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  \n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch(phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n  \n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ \n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: trafficLight.intersection?.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n  \n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = { isLight: true };\n  \n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  \n  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\n\nexport default CampusModel; \n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACtC,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,0BAA0B,GAAG,IAAI,CAAC,CAAC;AACvC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAEpB;AACA,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxC,MAAMC,oBAAoB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;;AAExC;AACA,MAAME,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACV;EACEC,GAAG,EAAE,2BAA2B;EAChCC,GAAG,EAAE,2BAA2B;EAChCd,KAAK,EAAE,6BAA6B;EACtCe,GAAG,EAAE,2BAA2B;EAAG;EACnCC,IAAI,EAAE,4BAA4B,CAAE;AACtC,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AACzEC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;;AAE1D;AACA,MAAMG,aAAa,GAAG,IAAIlB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,IAAImB,gBAAgB,GAAG,IAAInB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAElC;AACA,IAAIoB,gBAAgB,GAAG,IAAI;;AAE3B;AACA,IAAIC,gBAAgB,GAAG,IAAIrB,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,IAAIsB,kBAAkB,GAAG,IAAItB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEpC;AACA,MAAMuB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGb,QAAQ,oBAAoB,CAAC;IAC7D,MAAMc,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,IAAIA,IAAI,CAACE,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;MACzD,MAAMG,WAAW,GAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,IAAI,CAAC;MACrE,IAAIH,WAAW,IAAIA,WAAW,CAACI,KAAK,EAAE;QACpCf,gBAAgB,GAAGW,WAAW,CAACI,KAAK;QACpCnB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEG,gBAAgB,CAAC;QAC7C,OAAOA,gBAAgB;MACzB;IACF;IACAJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChCG,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB,CAAC,CAAC,OAAOgB,KAAK,EAAE;IACdpB,OAAO,CAACoB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjChB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB;AACF,CAAC;;AAED;AACA,MAAMiB,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;EAC5C;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAI,CAAC/C,YAAY,EAAE;MACjBA,YAAY,GAAG8C,MAAM,CAACE,KAAK,CAAC,CAAC;MAC7B,OAAOF,MAAM;IACf;IAEA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAElD,YAAY,CAACkD,CAAC,EAAEhD,KAAK,CAAC;IAChE,MAAMiD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEpD,YAAY,CAACoD,CAAC,EAAElD,KAAK,CAAC;IAChE,MAAMmD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAEtD,YAAY,CAACsD,CAAC,EAAEpD,KAAK,CAAC;IAEhEF,YAAY,CAACuD,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;IACjD,OAAOrD,YAAY,CAACgD,KAAK,CAAC,CAAC;EAC3B;;EAEA;EACA,IAAI,CAAC7C,oBAAoB,CAACqD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC5C,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;EAEA,MAAMW,OAAO,GAAGtD,oBAAoB,CAACuD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,MAAMY,QAAQ,GAAGF,OAAO,CAACG,UAAU,CAACd,MAAM,CAAC;EAC3C,MAAMe,sBAAsB,GAAG,EAAE,CAAC,CAAC;;EAEnC,IAAIF,QAAQ,GAAGE,sBAAsB,EAAE;IACrCzC,OAAO,CAACC,GAAG,CAAC,KAAK0B,SAAS,UAAUY,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAClE3D,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;IACnD,OAAOF,MAAM;EACf;;EAEA;EACA,MAAMG,SAAS,GAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,EAAEO,OAAO,CAACP,CAAC,EAAEhD,KAAK,CAAC;EAC3D,MAAMiD,SAAS,GAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,EAAEK,OAAO,CAACL,CAAC,EAAElD,KAAK,CAAC;EAC3D,MAAMmD,SAAS,GAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,EAAEG,OAAO,CAACH,CAAC,EAAEpD,KAAK,CAAC;EAE3D,MAAM6D,WAAW,GAAG,IAAI1F,KAAK,CAAC2F,OAAO,CAACf,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACtElD,oBAAoB,CAACoD,GAAG,CAACR,SAAS,EAAEgB,WAAW,CAACf,KAAK,CAAC,CAAC,CAAC;EAExD,OAAOe,WAAW;AACpB,CAAC;;AAED;AACA,MAAME,cAAc,GAAGA,CAACC,WAAW,EAAEnB,SAAS,KAAK;EACjD;EACA,IAAI,CAACA,SAAS,EAAE;IAChB,IAAI9C,YAAY,KAAK,IAAI,EAAE;MACzBA,YAAY,GAAGiE,WAAW;MAC1B,OAAOA,WAAW;IACpB;;IAEA;IACA,IAAIC,IAAI,GAAGD,WAAW,GAAGjE,YAAY;IACrC,IAAIkE,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;IAExC,MAAMC,gBAAgB,GAAG7B,aAAa,CAACxC,YAAY,GAAGkE,IAAI,EAAElE,YAAY,EAAEC,KAAK,CAAC;IAChFD,YAAY,GAAGqE,gBAAgB;IAC7B,OAAOA,gBAAgB;EACzB;;EAEA;EACA,IAAI,CAACjE,oBAAoB,CAACmD,GAAG,CAACT,SAAS,CAAC,EAAE;IACxC1C,oBAAoB,CAACkD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMK,OAAO,GAAGlE,oBAAoB,CAACqD,GAAG,CAACX,SAAS,CAAC;;EAEnD;EACA,IAAIoB,IAAI,GAAGD,WAAW,GAAGK,OAAO;EAChC,IAAIJ,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;;EAExC;EACA,MAAMG,mBAAmB,GAAGJ,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,CAAC;EACzC,IAAID,IAAI,CAACK,GAAG,CAACN,IAAI,CAAC,GAAGK,mBAAmB,EAAE;IACxCpD,OAAO,CAACC,GAAG,CAAC,KAAK0B,SAAS,UAAU,CAACoB,IAAI,GAAG,GAAG,GAAGC,IAAI,CAACC,EAAE,EAAEP,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IAChFzD,oBAAoB,CAACkD,GAAG,CAACR,SAAS,EAAEmB,WAAW,CAAC;IAChD,OAAOA,WAAW;EACpB;EAEA,MAAMI,gBAAgB,GAAG7B,aAAa,CAAC8B,OAAO,GAAGJ,IAAI,EAAEI,OAAO,EAAErE,KAAK,CAAC;EACtEG,oBAAoB,CAACkD,GAAG,CAACR,SAAS,EAAEuB,gBAAgB,CAAC;EAErD,OAAOA,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA,MAAMI,6BAA6B,GAAG,CAAC,CAAC,CAAC;AACzC;;AAEA,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC,kBAAkB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAMC,YAAY,GAAG9G,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM+G,UAAU,GAAG/G,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMgH,SAAS,GAAGhH,MAAM,CAAC,IAAIM,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAM2G,aAAa,GAAGjH,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMkH,eAAe,GAAGlH,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMmH,aAAa,GAAGnH,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMoH,iBAAiB,GAAGpH,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMqH,MAAM,GAAGrH,MAAM,CAAC,IAAI,CAAC;;EAE3B;EACA,MAAMsH,kBAAkB,GAAGtH,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMuH,gBAAgB,GAAGvH,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMwH,eAAe,GAAG,IAAI,CAAC,CAAC;;EAE9B;EACA,MAAMC,oBAAoB,GAAGzH,MAAM,CAAC0H,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAMC,qBAAqB,GAAG,IAAI1F,GAAG,CAAC,CAAC;;EAEvC;EACA,MAAM2F,gBAAgB,GAAG,EAAE;;EAE3B;EACA,MAAMC,wBAAwB,GAAG9H,MAAM,CAAC,IAAI,CAAC;;EAE7C;EACA,MAAM,CAAC+H,YAAY,EAAEC,eAAe,CAAC,GAAG/H,QAAQ,CAAC;IAC/CgI,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrI,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMsI,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,IAAI;IAAG;IACfC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGxJ,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAACyJ,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzJ,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAAC0J,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3J,QAAQ,CAAC;IAC7D4J,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,IAAI;IACbtB,QAAQ,EAAE;MAAExD,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxB6E,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAGjK,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMkK,0BAA0B,GAAGlK,MAAM,CAAC,IAAI,CAAC;;EAE/C;EACAsC,MAAM,CAAC6H,uBAAuB,GAAGP,sBAAsB;;EAEvD;EACAtH,MAAM,CAAC2H,mBAAmB,GAAGA,mBAAmB;EAChD3H,MAAM,CAAC4H,0BAA0B,GAAGA,0BAA0B;;EAE9D;EACA,MAAME,uBAAuB,GAAG;IAC9B5B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACX3B,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7B2B,KAAK,EAAE,OAAO;IAAG;IACjB1B,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMiB,UAAU,GAAG;IACjB/B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACX3B,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBwB,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbpB,QAAQ,EAAE,MAAM;IAChBqB,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvC/B,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM,CAACrF,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC,IAAIiC,GAAG,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAAC0I,UAAU,EAAEC,aAAa,CAAC,GAAG5K,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAACoD,gBAAgB,EAAEyH,mBAAmB,CAAC,GAAG7K,QAAQ,CAAC,IAAIiC,GAAG,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAAC6I,WAAW,EAAEC,cAAc,CAAC,GAAG/K,QAAQ,CAAC;IAAEgL,KAAK,EAAE,EAAE;IAAElB,OAAO,EAAE;EAAG,CAAC,CAAC;;EAE1E;EACA,MAAMmB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI3J,UAAU,KAAK,QAAQ,EAAE;MAC3B2B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB5B,UAAU,GAAG,QAAQ;;MAErB;MACA+F,kBAAkB,CAAC6D,OAAO,GAAG,IAAI;MACjC5D,gBAAgB,CAAC4D,OAAO,GAAG,IAAI;MAE/B,IAAI3J,QAAQ,EAAE;QACZA,QAAQ,CAAC4J,OAAO,GAAG,KAAK;MAC1B;IACF;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI9J,UAAU,KAAK,QAAQ,EAAE;MAC3B2B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACtB5B,UAAU,GAAG,QAAQ;;MAErB;MACA+F,kBAAkB,CAAC6D,OAAO,GAAG,IAAI;MACjC5D,gBAAgB,CAAC4D,OAAO,GAAG,IAAI;MAE/B,IAAI3B,SAAS,CAAC2B,OAAO,IAAI3J,QAAQ,EAAE;QACjC;QACA;QACAgI,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC,MAAMiG,UAAU,GAAG9B,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAC1D,KAAK,CAAC,CAAC;QACrD,MAAMyG,SAAS,GAAG/B,SAAS,CAAC2B,OAAO,CAACK,EAAE,CAAC1G,KAAK,CAAC,CAAC;;QAE9C;QACA,IAAIvE,KAAK,CAACkL,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC;UAAE1G,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,GAAG;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAChCuG,MAAM,CAACpL,KAAK,CAACqL,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdvC,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACwD,IAAI,CAACV,UAAU,CAAC;QAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;QAEV;QACA,IAAI1L,KAAK,CAACkL,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;UAAE1G,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAC9BuG,MAAM,CAACpL,KAAK,CAACqL,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdvC,SAAS,CAAC2B,OAAO,CAACK,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC;QACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC;;QAEV;QACAzK,QAAQ,CAAC0K,MAAM,CAAC7G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B,MAAM8G,aAAa,GAAG3K,QAAQ,CAAC0K,MAAM,CAACpH,KAAK,CAAC,CAAC;;QAE7C;QACA,IAAIvE,KAAK,CAACkL,KAAK,CAACU,aAAa,CAAC,CAC3BT,EAAE,CAAC;UAAE1G,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE,CAAC;UAAEE,CAAC,EAAE;QAAE,CAAC,EAAE,IAAI,CAAC,CAC9BuG,MAAM,CAACpL,KAAK,CAACqL,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;UACdvK,QAAQ,CAAC0K,MAAM,CAACF,IAAI,CAACG,aAAa,CAAC;UACnC;UACA3C,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAAC5K,QAAQ,CAAC0K,MAAM,CAAC;UACzC1K,QAAQ,CAAC6K,MAAM,CAAC,CAAC;QACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;QAEV;QACAzK,QAAQ,CAAC4J,OAAO,GAAG,IAAI;;QAEvB;QACA5J,QAAQ,CAAC8K,WAAW,GAAG,EAAE;QACzB9K,QAAQ,CAAC+K,WAAW,GAAG,GAAG;QAC1B/K,QAAQ,CAACgL,aAAa,GAAGtG,IAAI,CAACC,EAAE,GAAG,GAAG;QACtC3E,QAAQ,CAACiL,aAAa,GAAG,CAAC;QAC1BjL,QAAQ,CAAC6K,MAAM,CAAC,CAAC;QACjB;QACA7C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC;QAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;QAEzCzJ,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;UACrByJ,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAChBC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAGtM,iBAAiB,CAACuM,aAAa,CAAChJ,IAAI,CAACiJ,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKJ,KAAK,CAAC;IAChF,IAAIC,YAAY,IAAIzD,SAAS,CAAC2B,OAAO,IAAI3J,QAAQ,EAAE;MACjDkI,uBAAuB,CAACuD,YAAY,CAAC;;MAErC;MACA,MAAMI,WAAW,GAAGrG,SAAS,CAACmE,OAAO,CAACmC,YAAY,CAChDC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,EAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC;MAEDhF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvBqK,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBK,GAAG,EAAE;UACHxF,SAAS,EAAEgF,YAAY,CAAChF,SAAS;UACjCC,QAAQ,EAAE+E,YAAY,CAAC/E;QACzB,CAAC;QACDwF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACA9L,UAAU,GAAG,cAAc;MAC3B+G,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAkB,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACnD,GAAG,CAACgI,WAAW,CAACrI,CAAC,GAAC,EAAE,EAAE,EAAE,EAAE,CAACqI,WAAW,CAACnI,CAAC,GAAC,EAAE,CAAC;;MAEvE;MACA1D,QAAQ,CAAC0K,MAAM,CAAC7G,GAAG,CAACgI,WAAW,CAACrI,CAAC,EAAE,CAAC,EAAE,CAACqI,WAAW,CAACnI,CAAC,CAAC;;MAErD;MACAsE,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAAC5K,QAAQ,CAAC0K,MAAM,CAAC;;MAEzC;MACA1K,QAAQ,CAAC4J,OAAO,GAAG,IAAI;MACvB5J,QAAQ,CAAC6K,MAAM,CAAC,CAAC;;MAEjB;MACA7C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC;MAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;MAEzCzJ,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBqK,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBO,IAAI,EAAEnE,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACoF,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAErM,QAAQ,CAAC0K,MAAM,CAAC0B,OAAO,CAAC,CAAC;QAC9BF,IAAI,EAAEL;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAGA,CAAC7C,KAAK,EAAE8C,OAAO,KAAK;IAC5C,IAAI;MACF,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;MAEnC;MACA,IAAI9C,KAAK,KAAK7I,WAAW,CAACO,GAAG,EAAE;QAAA,IAAAwL,aAAA;QAC7BjL,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE6K,OAAO,CAAC;;QAEhC;QACA,MAAMI,SAAS,GAAGJ,OAAO,CAACK,GAAG;QAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;QAEnC;QACA,MAAMC,aAAa,GAAGnL,gBAAgB,CAACmC,GAAG,CAAC4I,SAAS,CAAC;;QAErD;QACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;UACrDtL,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;YACzBsL,KAAK,EAAEL,SAAS;YAChBM,KAAK,EAAEJ,gBAAgB;YACvBK,KAAK,EAAEH;UACT,CAAC,CAAC;UACN;QACF;;QAEI;QACAnL,gBAAgB,CAACgC,GAAG,CAAC+I,SAAS,EAAEE,gBAAgB,CAAC;QAEjDpL,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;UACzBsL,KAAK,EAAEL,SAAS;UAChBQ,GAAG,EAAEN,gBAAgB;UACrBO,KAAK,EAAE,CAACL,aAAa,IAAIF,gBAAgB,IAAIE;QAC/C,CAAC,CAAC;QAEF,MAAMM,YAAY,GAAG,EAAAX,aAAA,GAAAH,OAAO,CAACpK,IAAI,cAAAuK,aAAA,uBAAZA,aAAA,CAAcW,YAAY,KAAI,EAAE;QACrD,MAAMC,KAAK,GAAGf,OAAO,CAACpK,IAAI,CAACmL,KAAK;;QAEhC;QACA,MAAMpH,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;;QAEtB;QACAmH,YAAY,CAACE,OAAO,CAACC,WAAW,IAAI;UAClC;UACA;UACA,MAAMC,EAAE,GAAId,SAAS,GAAGa,WAAW,CAACE,SAAS;UAC7C,MAAMC,IAAI,GAAGH,WAAW,CAACI,WAAW;UAEpC,IAAGD,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACA;YACA,MAAME,KAAK,GAAG;cACZrH,SAAS,EAAEsF,UAAU,CAAC0B,WAAW,CAACM,WAAW,CAAC;cAC9CrH,QAAQ,EAAEqF,UAAU,CAAC0B,WAAW,CAACO,UAAU,CAAC;cAC5CrH,KAAK,EAAEoF,UAAU,CAAC0B,WAAW,CAACQ,SAAS,CAAC;cACxCrH,OAAO,EAAEmF,UAAU,CAAC0B,WAAW,CAACS,WAAW;YAC7C,CAAC;YAED,MAAMC,QAAQ,GAAG3I,SAAS,CAACmE,OAAO,CAACmC,YAAY,CAACgC,KAAK,CAACrH,SAAS,EAAEqH,KAAK,CAACpH,QAAQ,CAAC;;YAEhF;YACA,IAAI0H,cAAc;YAClB,QAAQR,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRQ,cAAc,GAAGnO,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRmO,cAAc,GAAGlO,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRkO,cAAc,GAAGjO,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACA,IAAIkO,KAAK,GAAGzM,aAAa,CAACoC,GAAG,CAAC0J,EAAE,CAAC;YAEjC,IAAI,CAACW,KAAK,IAAID,cAAc,EAAE;cAC5B;cACA,MAAME,QAAQ,GAAGF,cAAc,CAAC9K,KAAK,CAAC,CAAC;cACvC;cACA,MAAMiL,MAAM,GAAGX,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;cACzCU,QAAQ,CAACtH,QAAQ,CAACnD,GAAG,CAACsK,QAAQ,CAAC3K,CAAC,EAAE+K,MAAM,EAAE,CAACJ,QAAQ,CAACzK,CAAC,CAAC;cACtD4K,QAAQ,CAACE,QAAQ,CAAC9K,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGmJ,KAAK,CAAClH,OAAO,GAAGlC,IAAI,CAACC,EAAE,GAAG,GAAG;;cAE7D;cACA,IAAIiJ,IAAI,KAAK,GAAG,IAAIvH,gBAAgB,CAACoI,MAAM,GAAG,CAAC,EAAE;gBAC/C,IAAI;kBACF;kBACA,MAAMC,KAAK,GAAG,IAAI/P,KAAK,CAACgQ,cAAc,CAACL,QAAQ,CAAC;;kBAEhD;kBACA,MAAMM,aAAa,GAAGvI,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC3C3E,OAAO,CAACC,GAAG,CAAC,SAAS+L,EAAE,QAAQ,EAAEkB,aAAa,CAAChD,IAAI,IAAI,OAAO,CAAC;;kBAE/D;kBACA,MAAMiD,MAAM,GAAGH,KAAK,CAACI,UAAU,CAACF,aAAa,CAAC;kBAC9CC,MAAM,CAACE,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;kBACnCF,MAAM,CAACG,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAI;kBACnCH,MAAM,CAACI,OAAO,CAACtQ,KAAK,CAACuQ,UAAU,CAAC,CAAC,CAAE;kBACnCL,MAAM,CAACM,IAAI,CAAC,CAAC;;kBAEb;kBACA/I,qBAAqB,CAACvC,GAAG,CAAC6J,EAAE,EAAEgB,KAAK,CAAC;kBAEpChN,OAAO,CAACC,GAAG,CAAC,OAAO+L,EAAE,kBAAkB,EAAE;oBACvC0B,IAAI,EAAER,aAAa,CAAChD,IAAI,IAAI,KAAK;oBACjCyD,IAAI,EAAER,MAAM,CAACS,SAAS,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK;oBACxCC,IAAI,EAAEV,MAAM,CAACW,IAAI;oBACjBC,KAAK,EAAEf,KAAK,CAACgB;kBACf,CAAC,CAAC;gBACJ,CAAC,CAAC,OAAO5M,KAAK,EAAE;kBACdpB,OAAO,CAACoB,KAAK,CAAC,OAAO4K,EAAE,WAAW,EAAE5K,KAAK,CAAC;gBAC5C;cACF;cAEAzC,KAAK,CAACsP,GAAG,CAACrB,QAAQ,CAAC;cAEnB1M,aAAa,CAACiC,GAAG,CAAC6J,EAAE,EAAE;gBACpBW,KAAK,EAAEC,QAAQ;gBACfsB,UAAU,EAAEzJ,GAAG;gBACfyH,IAAI,EAAEA;cACR,CAAC,CAAC;YACJ,CAAC,MAAM,IAAIS,KAAK,EAAE;cAChB;cACAA,KAAK,CAACA,KAAK,CAACrH,QAAQ,CAACnD,GAAG,CAACsK,QAAQ,CAAC3K,CAAC,EAAE6K,KAAK,CAACT,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAACO,QAAQ,CAACzK,CAAC,CAAC;cACjF2K,KAAK,CAACA,KAAK,CAACG,QAAQ,CAAC9K,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGmJ,KAAK,CAAClH,OAAO,GAAGlC,IAAI,CAACC,EAAE,GAAG,GAAG;cAChE0J,KAAK,CAACuB,UAAU,GAAGzJ,GAAG;;cAEtB;cACAkI,KAAK,CAACA,KAAK,CAACnD,YAAY,CAAC,CAAC;cAC1BmD,KAAK,CAACA,KAAK,CAAClD,iBAAiB,CAAC,IAAI,CAAC;;cAEnC;cACA,IAAIkD,KAAK,CAACT,IAAI,KAAK,GAAG,EAAE;gBACtB,IAAI,CAACxH,qBAAqB,CAACtC,GAAG,CAAC4J,EAAE,CAAC,IAAIrH,gBAAgB,CAACoI,MAAM,GAAG,CAAC,EAAE;kBACjE;kBACA,IAAI;oBACF,MAAMC,KAAK,GAAG,IAAI/P,KAAK,CAACgQ,cAAc,CAACN,KAAK,CAACA,KAAK,CAAC;oBACnD,MAAMO,aAAa,GAAGvI,gBAAgB,CAAC,CAAC,CAAC;oBACzC,MAAMwI,MAAM,GAAGH,KAAK,CAACI,UAAU,CAACF,aAAa,CAAC;oBAC9CC,MAAM,CAACI,OAAO,CAACtQ,KAAK,CAACuQ,UAAU,CAAC;oBAChCL,MAAM,CAACM,IAAI,CAAC,CAAC;oBACb/I,qBAAqB,CAACvC,GAAG,CAAC6J,EAAE,EAAEgB,KAAK,CAAC;oBACpChN,OAAO,CAACC,GAAG,CAAC,SAAS+L,EAAE,YAAY,CAAC;kBACtC,CAAC,CAAC,OAAO5K,KAAK,EAAE;oBACdpB,OAAO,CAACoB,KAAK,CAAC,SAAS4K,EAAE,aAAa,EAAE5K,KAAK,CAAC;kBAChD;gBACF;cACF;YACF;UACA;QACF,CAAC,CAAC;;QAEF;QACA,MAAM+M,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAACzC,YAAY,CAAC0C,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACtC,SAAS,CAAC,CAAC;QAE9D/L,aAAa,CAAC4L,OAAO,CAAC,CAAC0C,SAAS,EAAExC,EAAE,KAAK;UACvC,IAAIvH,GAAG,GAAG+J,SAAS,CAACN,UAAU,GAAGC,iBAAiB,IAAI,CAACC,UAAU,CAAChM,GAAG,CAAC4J,EAAE,CAAC,EAAE;YACzErN,KAAK,CAAC8P,MAAM,CAACD,SAAS,CAAC7B,KAAK,CAAC;YAC7BzM,aAAa,CAACwO,MAAM,CAAC1C,EAAE,CAAC;YACxBhM,OAAO,CAACC,GAAG,CAAC,oBAAoB+L,EAAE,QAAQwC,SAAS,CAACtC,IAAI,EAAE,CAAC;UAC7D;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAInE,KAAK,KAAK7I,WAAW,CAACM,GAAG,EAAE;QAC7BQ,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE6K,OAAO,CAAC;QAEhC,MAAM6D,OAAO,GAAG7D,OAAO,CAACpK,IAAI;QAC5B,MAAMkO,KAAK,GAAGD,OAAO,CAACxN,KAAK;QAC3B,MAAM0N,QAAQ,GAAG;UACf9J,SAAS,EAAEsF,UAAU,CAACsE,OAAO,CAACG,QAAQ,CAAC;UACvC9J,QAAQ,EAAEqF,UAAU,CAACsE,OAAO,CAACI,OAAO,CAAC;UACrC9J,KAAK,EAAEoF,UAAU,CAACsE,OAAO,CAACpC,SAAS,CAAC;UACpCrH,OAAO,EAAEmF,UAAU,CAACsE,OAAO,CAACnC,WAAW;QACzC,CAAC;QAEDxM,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE4O,QAAQ,CAAC;QAClC7O,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE2O,KAAK,CAAC;;QAE3B;QACAxP,MAAM,CAAC4P,WAAW,CAAC;UACjB9C,IAAI,EAAE,iBAAiB;UACvB+C,MAAM,EAAE;QACV,CAAC,EAAE,GAAG,CAAC;;QAEP;QACA7P,MAAM,CAAC4P,WAAW,CAAC;UACjB9C,IAAI,EAAE,KAAK;UACX/K,KAAK,EAAEyN,KAAK;UAAE;UACdlO,IAAI,EAAE;YAAQ;YACZS,KAAK,EAAEyN,KAAK;YACZrC,SAAS,EAAEoC,OAAO,CAACpC,SAAS;YAC5BwC,OAAO,EAAEJ,OAAO,CAACI,OAAO;YACxBD,QAAQ,EAAEH,OAAO,CAACG,QAAQ;YAC1BtC,WAAW,EAAEmC,OAAO,CAACnC;UACvB;QACF,CAAC,EAAE,GAAG,CAAC;;QAEP;QACA,MAAMC,QAAQ,GAAG3I,SAAS,CAACmE,OAAO,CAACmC,YAAY,CAACyE,QAAQ,CAAC9J,SAAS,EAAE8J,QAAQ,CAAC7J,QAAQ,CAAC;QACtF,MAAMkK,eAAe,GAAG,IAAIjS,KAAK,CAAC2F,OAAO,CAAC6J,QAAQ,CAAC3K,CAAC,EAAE,GAAG,EAAE,CAAC2K,QAAQ,CAACzK,CAAC,CAAC;QACvE,MAAMmN,eAAe,GAAGnM,IAAI,CAACC,EAAE,GAAG4L,QAAQ,CAAC3J,OAAO,GAAGlC,IAAI,CAACC,EAAE,GAAG,GAAG;;QAElE;QACA,MAAMmM,WAAW,GAAG3N,cAAc,CAACyN,eAAe,EAAEN,KAAK,CAAC;QAC1D,MAAM9L,WAAW,GAAGD,cAAc,CAACsM,eAAe,EAAEP,KAAK,CAAC;;QAE1D;QACA,IAAIS,UAAU,GAAGnP,aAAa,CAACoC,GAAG,CAACsM,KAAK,CAAC;;QAEzC;QACA,MAAM1N,aAAa,GAAG0N,KAAK,KAAKxO,gBAAgB;QAEhD,IAAI,CAACiP,UAAU,IAAI9Q,qBAAqB,EAAE;UACxC;UACA,MAAM+Q,eAAe,GAAG/Q,qBAAqB,CAACqD,KAAK,CAAC,CAAC;;UAErD;UACA;UACA0N,eAAe,CAAChK,QAAQ,CAACnD,GAAG,CAACiN,WAAW,CAACtN,CAAC,EAAE,CAAC,CAAC,EAAEsN,WAAW,CAAClN,CAAC,CAAC;UAC9DoN,eAAe,CAACxC,QAAQ,CAAC9K,CAAC,GAAGc,WAAW;;UAExC;UACAwM,eAAe,CAACC,QAAQ,CAAEC,KAAK,IAAK;YAClC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;cAClC;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;;cAEA;cACA;cACA;;cAEA;;cAEA,MAAMC,WAAW,GAAGH,KAAK,CAACE,QAAQ,CAAC9N,KAAK,CAAC,CAAC;cAC1C4N,KAAK,CAACE,QAAQ,GAAGC,WAAW;;cAE5B;cACA,IAAIzO,aAAa,EAAE;gBACjByO,WAAW,CAACpI,KAAK,CAACpF,GAAG,CAAC,QAAQ,CAAC;cACjC,CAAC,MAAM;gBACLwN,WAAW,CAACpI,KAAK,CAACpF,GAAG,CAAC,QAAQ,CAAC;cACjC;cACAwN,WAAW,CAACC,QAAQ,GAAG,IAAI3S,KAAK,CAAC4S,KAAK,CAAC,QAAQ,CAAC;cAChDF,WAAW,CAACG,WAAW,GAAG,IAAI;cAC9B;cACAH,WAAW,CAACI,WAAW,GAAG,IAAI;YAChC;UACF,CAAC,CAAC;;UAEF;UACA,MAAMC,UAAU,GAAGC,gBAAgB,CAAC,GAAGjN,IAAI,CAACkN,KAAK,CAACrB,QAAQ,CAAC5J,KAAK,CAAC,OAAO,EAAE;YACxEc,eAAe,EAAE7E,aAAa,GAC5B;cAAEiP,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCC,SAAS,EAAE;cAAEJ,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/CnK,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFkK,UAAU,CAAC1K,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClC6N,UAAU,CAACQ,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/BR,UAAU,CAACN,QAAQ,CAACe,OAAO,GAAG,GAAG,CAAC,CAAC;UACnCnB,eAAe,CAACrB,GAAG,CAAC+B,UAAU,CAAC;UAE/BrR,KAAK,CAACsP,GAAG,CAACqB,eAAe,CAAC;;UAE1B;UACApP,aAAa,CAACiC,GAAG,CAACyM,KAAK,EAAE;YACvBjC,KAAK,EAAE2C,eAAe;YACtBpB,UAAU,EAAE1J,IAAI,CAACC,GAAG,CAAC,CAAC;YACtByH,IAAI,EAAE,GAAG;YAAE;YACXwE,MAAM,EAAExP,aAAa;YACrB8O,UAAU,EAAEA,UAAU,CAAC;UACzB,CAAC,CAAC;UAEFhQ,OAAO,CAACC,GAAG,CAAC,aAAa2O,KAAK,SAASQ,WAAW,CAACtN,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC,KAAK0M,WAAW,CAACpN,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,KAAK0M,WAAW,CAAClN,CAAC,CAACQ,OAAO,CAAC,CAAC,CAAC,YAAYxB,aAAa,EAAE,CAAC;;UAErJ;UACA,IAAI7D,KAAK,CAACkL,KAAK,CAAC+G,eAAe,CAAChK,QAAQ,CAAC,CACtCkD,EAAE,CAAC;YAAExG,CAAC,EAAE;UAAI,CAAC,EAAE,GAAG,CAAC,CACnByG,MAAM,CAACpL,KAAK,CAACqL,MAAM,CAACC,SAAS,CAACgI,GAAG,CAAC,CAClC5H,KAAK,CAAC,CAAC;;UAEV;UACAuG,eAAe,CAACC,QAAQ,CAAEC,KAAK,IAAK;YAClC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACI,WAAW,EAAE;cAChE,IAAIzS,KAAK,CAACkL,KAAK,CAAC;gBAAEkI,OAAO,EAAE;cAAI,CAAC,CAAC,CAC9BjI,EAAE,CAAC;gBAAEiI,OAAO,EAAE;cAAI,CAAC,EAAE,GAAG,CAAC,CACzBhI,MAAM,CAACpL,KAAK,CAACqL,MAAM,CAACC,SAAS,CAACgI,GAAG,CAAC,CAClC9H,QAAQ,CAAC,YAAW;gBACnB2G,KAAK,CAACE,QAAQ,CAACe,OAAO,GAAG,IAAI,CAACA,OAAO;gBACrCjB,KAAK,CAACE,QAAQ,CAACK,WAAW,GAAG,IAAI;cACnC,CAAC,CAAC,CACDhH,KAAK,CAAC,CAAC;YACZ;UACF,CAAC,CAAC;;UAEF;UACA,IAAI1L,KAAK,CAACkL,KAAK,CAAC;YAAEkI,OAAO,EAAE;UAAI,CAAC,CAAC,CAC9BjI,EAAE,CAAC;YAAEiI,OAAO,EAAE;UAAI,CAAC,EAAE,GAAG,CAAC,CACzBhI,MAAM,CAACpL,KAAK,CAACqL,MAAM,CAACC,SAAS,CAACgI,GAAG,CAAC,CAClC9H,QAAQ,CAAC,YAAW;YACnBmH,UAAU,CAACN,QAAQ,CAACe,OAAO,GAAG,IAAI,CAACA,OAAO;YAC1CT,UAAU,CAACN,QAAQ,CAACK,WAAW,GAAG,IAAI;UACxC,CAAC,CAAC,CACDhH,KAAK,CAAC,CAAC;;UAEV;UACA,IAAI7H,aAAa,EAAE;YACjBpD,gBAAgB,GAAGwR,eAAe;YAClCxK,eAAe,CAAC+J,QAAQ,CAAC;YACzB7O,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE2O,KAAK,CAAC;UACjC;QACF,CAAC,MAAM,IAAIS,UAAU,EAAE;UACrB;UACA,MAAMuB,gBAAgB,GAAGnP,cAAc,CAAC2N,WAAW,EAAER,KAAK,CAAC;UAC3D,MAAM1L,gBAAgB,GAAGL,cAAc,CAACC,WAAW,EAAE8L,KAAK,CAAC;;UAE3D;UACAS,UAAU,CAAC1C,KAAK,CAACrH,QAAQ,CAACwD,IAAI,CAAC8H,gBAAgB,CAAC;UAChDvB,UAAU,CAAC1C,KAAK,CAACG,QAAQ,CAAC9K,CAAC,GAAGkB,gBAAgB;UAC9CmM,UAAU,CAAC1C,KAAK,CAACnD,YAAY,CAAC,CAAC;UAC/B6F,UAAU,CAAC1C,KAAK,CAAClD,iBAAiB,CAAC,IAAI,CAAC;UACxC4F,UAAU,CAACnB,UAAU,GAAG1J,IAAI,CAACC,GAAG,CAAC,CAAC;UAClC4K,UAAU,CAACqB,MAAM,GAAGxP,aAAa,CAAC,CAAC;;UAEnC;UACA,IAAImO,UAAU,CAACW,UAAU,EAAE;YACzBX,UAAU,CAACW,UAAU,CAACN,QAAQ,CAACpB,GAAG,CAACuC,OAAO,CAAC,CAAC;YAC5CxB,UAAU,CAAC1C,KAAK,CAAC8B,MAAM,CAACY,UAAU,CAACW,UAAU,CAAC;UAChD;;UAEA;UACA,MAAMA,UAAU,GAAGC,gBAAgB,CAAC,GAAGjN,IAAI,CAACkN,KAAK,CAACrB,QAAQ,CAAC5J,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE;YAC9Ec,eAAe,EAAE7E,aAAa,GAC5B;cAAEiP,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCC,SAAS,EAAE;cAAEJ,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/CnK,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFkK,UAAU,CAAC1K,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACnC6N,UAAU,CAACQ,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/BnB,UAAU,CAAC1C,KAAK,CAACsB,GAAG,CAAC+B,UAAU,CAAC;UAChCX,UAAU,CAACW,UAAU,GAAGA,UAAU;UAElChQ,OAAO,CAACC,GAAG,CAAC,cAAc2O,KAAK,SAASgC,gBAAgB,CAAC9O,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC,KAAKkO,gBAAgB,CAAC5O,CAAC,CAACU,OAAO,CAAC,CAAC,CAAC,KAAKkO,gBAAgB,CAAC1O,CAAC,CAACQ,OAAO,CAAC,CAAC,CAAC,YAAYxB,aAAa,EAAE,CAAC;;UAErK;UACA,IAAIA,aAAa,EAAE;YACjBpD,gBAAgB,GAAGuR,UAAU,CAAC1C,KAAK;YACnC7H,eAAe,CAAC+J,QAAQ,CAAC;UAC3B;QACF;;QAEA;QACA,MAAMpK,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;QACtB,MAAM0J,iBAAiB,GAAG,IAAI,CAAC,CAAC;;QAEhCjO,aAAa,CAAC4L,OAAO,CAAC,CAAC0C,SAAS,EAAExC,EAAE,KAAK;UACvC,MAAM8E,mBAAmB,GAAGrM,GAAG,GAAG+J,SAAS,CAACN,UAAU;;UAEtD;UACA,IAAI4C,mBAAmB,GAAG3C,iBAAiB,GAAG,GAAG,IAAI2C,mBAAmB,IAAI3C,iBAAiB,EAAE;YAC7F;YACA,MAAMsC,OAAO,GAAG,CAAC;YAEjBjC,SAAS,CAAC7B,KAAK,CAAC4C,QAAQ,CAAEC,KAAK,IAAK;cAClC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClC;gBACA,IAAIF,KAAK,CAACE,QAAQ,CAACI,WAAW,KAAKiB,SAAS,EAAE;kBAC5CvB,KAAK,CAACE,QAAQ,CAACsB,mBAAmB,GAAGxB,KAAK,CAACE,QAAQ,CAACI,WAAW,IAAI,KAAK;kBACxEN,KAAK,CAACE,QAAQ,CAACuB,eAAe,GAAGzB,KAAK,CAACE,QAAQ,CAACe,OAAO,IAAI,GAAG;gBAChE;;gBAEA;gBACAjB,KAAK,CAACE,QAAQ,CAACI,WAAW,GAAG,IAAI;gBACjCN,KAAK,CAACE,QAAQ,CAACe,OAAO,GAAGA,OAAO;gBAChCjB,KAAK,CAACE,QAAQ,CAACK,WAAW,GAAG,IAAI;cACnC;YACF,CAAC,CAAC;;YAEF;YACA,IAAIvB,SAAS,CAACwB,UAAU,EAAE;cACxBxB,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACe,OAAO,GAAGA,OAAO;cAC/CjC,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACK,WAAW,GAAG,IAAI;YAClD;UACF;UACA;UAAA,KACK,IAAIe,mBAAmB,GAAG3C,iBAAiB,EAAE;YAChD;YACA,IAAIK,SAAS,CAACwB,UAAU,EAAE;cACxBxB,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACpB,GAAG,CAACuC,OAAO,CAAC,CAAC;cAC3CrC,SAAS,CAACwB,UAAU,CAACN,QAAQ,CAACmB,OAAO,CAAC,CAAC;cACvCrC,SAAS,CAAC7B,KAAK,CAAC8B,MAAM,CAACD,SAAS,CAACwB,UAAU,CAAC;YAC9C;YAEAxB,SAAS,CAAC7B,KAAK,CAAC4C,QAAQ,CAAEC,KAAK,IAAK;cAClC,IAAIA,KAAK,CAACC,MAAM,EAAE;gBAChB,IAAID,KAAK,CAACE,QAAQ,EAAE;kBAClB,IAAI7O,KAAK,CAACC,OAAO,CAAC0O,KAAK,CAACE,QAAQ,CAAC,EAAE;oBACjCF,KAAK,CAACE,QAAQ,CAAC5D,OAAO,CAACoF,CAAC,IAAIA,CAAC,CAACL,OAAO,CAAC,CAAC,CAAC;kBAC1C,CAAC,MAAM;oBACLrB,KAAK,CAACE,QAAQ,CAACmB,OAAO,CAAC,CAAC;kBAC1B;gBACF;gBACA,IAAIrB,KAAK,CAAC2B,QAAQ,EAAE3B,KAAK,CAAC2B,QAAQ,CAACN,OAAO,CAAC,CAAC;cAC9C;YACF,CAAC,CAAC;;YAEF;YACAlS,KAAK,CAAC8P,MAAM,CAACD,SAAS,CAAC7B,KAAK,CAAC;YAC7BzM,aAAa,CAACwO,MAAM,CAAC1C,EAAE,CAAC;YACxB;YACAjN,oBAAoB,CAAC2P,MAAM,CAAC1C,EAAE,CAAC;YAC/B/M,oBAAoB,CAACyP,MAAM,CAAC1C,EAAE,CAAC;YAE/BhM,OAAO,CAACC,GAAG,CAAC,mBAAmB+L,EAAE,EAAE,CAAC;UACtC;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAIjE,KAAK,KAAK7I,WAAW,CAACS,IAAI,EAAE;QAC9BK,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE4K,OAAO,CAAC;QAEjC,IAAI;UACF,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;UAEnC;UACA,MAAMK,SAAS,GAAGJ,OAAO,CAACK,GAAG;UAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;UAEnC;UACA,MAAMC,aAAa,GAAGnL,gBAAgB,CAACmC,GAAG,CAAC4I,SAAS,CAAC;;UAErD;UACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;YACrDtL,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;cAC1BsL,KAAK,EAAEL,SAAS;cAChBM,KAAK,EAAEJ,gBAAgB;cACvBK,KAAK,EAAEH;YACT,CAAC,CAAC;YACF;UACF;;UAEA;UACAnL,gBAAgB,CAACgC,GAAG,CAAC+I,SAAS,EAAEE,gBAAgB,CAAC;;UAEjD;UACA,IAAIN,OAAO,CAACpK,IAAI,IAAIoK,OAAO,CAACpK,IAAI,CAACsJ,aAAa,IAAInJ,KAAK,CAACC,OAAO,CAACgK,OAAO,CAACpK,IAAI,CAACsJ,aAAa,CAAC,EAAE;YAC3Fc,OAAO,CAACpK,IAAI,CAACsJ,aAAa,CAAC8B,OAAO,CAAC/B,YAAY,IAAI;cACjD,MAAMnD,OAAO,GAAGmD,YAAY,CAACnD,OAAO;cAEpC,IAAI,CAACA,OAAO,EAAE;gBACZ5G,OAAO,CAACoB,KAAK,CAAC,kBAAkB,EAAE2I,YAAY,CAAC;gBAC/C;cACF;cAEA/J,OAAO,CAACC,GAAG,CAAC,WAAW2G,OAAO,UAAU,CAAC;;cAEzC;cACA,IAAImD,YAAY,CAACjD,MAAM,IAAIjG,KAAK,CAACC,OAAO,CAACiJ,YAAY,CAACjD,MAAM,CAAC,EAAE;gBAC7D;gBACA,MAAMsK,UAAU,GAAG,EAAE;gBAErBrH,YAAY,CAACjD,MAAM,CAACgF,OAAO,CAACuF,KAAK,IAAI;kBACnC;kBACA,IAAI,CAACA,KAAK,CAACC,OAAO,EAAE;oBAClBtR,OAAO,CAACoB,KAAK,CAAC,gBAAgB,EAAEiQ,KAAK,CAAC;oBACtC;kBACF;kBAEA,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC;kBACxC;kBACA,MAAMC,SAAS,GAAGH,KAAK,CAACI,YAAY,GAClCC,oBAAoB,CAACL,KAAK,CAACI,YAAY,CAAC,GACxCE,iBAAiB,CAACL,OAAO,CAAC;;kBAE5B;kBACA,MAAMM,UAAU,GAAGP,KAAK,CAACQ,YAAY,IAAI,GAAG,CAAC,CAAC;kBAC9C,MAAMC,UAAU,GAAGC,QAAQ,CAACV,KAAK,CAACS,UAAU,CAAC,IAAI,CAAC;kBAElD9R,OAAO,CAACC,GAAG,CAAC,SAAS2G,OAAO,WAAW0K,OAAO,SAASE,SAAS,YAAYI,UAAU,WAAWE,UAAU,GAAG,CAAC;;kBAE/G;kBACA,MAAME,SAAS,GAAG;oBAChBV,OAAO;oBACPE,SAAS;oBACTK,YAAY,EAAED,UAAU;oBACxBE;kBACF,CAAC;;kBAED;kBACAV,UAAU,CAACa,IAAI,CAACD,SAAS,CAAC;;kBAE1B;kBACA;kBACA,IAAIE,eAAe,GAAGC,MAAM,CAACvL,OAAO,CAAC;kBACrC,IAAIwL,iBAAiB,GAAG/R,gBAAgB,CAACiC,GAAG,CAAC4P,eAAe,CAAC;kBAE7D,IAAI,CAACE,iBAAiB,EAAE;oBACtB;oBACAF,eAAe,GAAGH,QAAQ,CAACnL,OAAO,CAAC;oBACnCwL,iBAAiB,GAAG/R,gBAAgB,CAACiC,GAAG,CAAC4P,eAAe,CAAC;kBAC3D;kBAEA,IAAIE,iBAAiB,EAAE;oBACrB;oBACAC,wBAAwB,CAACD,iBAAiB,EAAEJ,SAAS,CAAC;;oBAEtD;oBACA,IAAIzL,oBAAoB,IAAIA,oBAAoB,CAACK,OAAO,KAAKA,OAAO,EAAE;sBACpEF,sBAAsB,CAAC4L,IAAI,KAAK;wBAC9B,GAAGA,IAAI;wBACP3L,OAAO,EAAE,IAAI;wBACb2K,OAAO;wBACPE,SAAS;wBACTpF,KAAK,EAAEwF,UAAU;wBACjBE;sBACF,CAAC,CAAC,CAAC;oBACL;kBACF,CAAC,MAAM;oBACL9R,OAAO,CAACuS,IAAI,CAAC,WAAW3L,OAAO,SAAS0K,OAAO,SAAS,CAAC;kBAC3D;gBACF,CAAC,CAAC;;gBAEF;gBACA,IAAIkB,QAAQ,GAAG,IAAI;gBACnB;gBACA,MAAMC,KAAK,GAAGN,MAAM,CAACvL,OAAO,CAAC;gBAC7B,IAAIvG,gBAAgB,CAAC+B,GAAG,CAACqQ,KAAK,CAAC,EAAE;kBAC/BD,QAAQ,GAAGC,KAAK;gBAClB,CAAC,MAAM;kBACL;kBACA,MAAMC,KAAK,GAAGX,QAAQ,CAACnL,OAAO,CAAC;kBAC/B,IAAIvG,gBAAgB,CAAC+B,GAAG,CAACsQ,KAAK,CAAC,EAAE;oBAC/BF,QAAQ,GAAGE,KAAK;kBAClB;gBACF;gBAEA,IAAIF,QAAQ,KAAK,IAAI,EAAE;kBACrB;kBACAlS,kBAAkB,CAAC6B,GAAG,CAACqQ,QAAQ,EAAE;oBAC/BG,UAAU,EAAEnO,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtBqC,MAAM,EAAEsK;kBACV,CAAC,CAAC;kBACFpR,OAAO,CAACC,GAAG,CAAC,aAAauS,QAAQ,KAAK,OAAOA,QAAQ,aAAa,CAAC;;kBAEnE;kBACA,IAAIpT,MAAM,CAAC2H,mBAAmB,KAC1B3H,MAAM,CAAC2H,mBAAmB,CAACkB,OAAO,KAAKuK,QAAQ,IAC/CpT,MAAM,CAAC2H,mBAAmB,CAACkB,OAAO,KAAKkK,MAAM,CAACK,QAAQ,CAAC,IACvDpT,MAAM,CAAC2H,mBAAmB,CAACkB,OAAO,KAAK8J,QAAQ,CAACS,QAAQ,CAAC,CAAC,EAAE;oBAE9DxS,OAAO,CAACC,GAAG,CAAC,eAAeuS,QAAQ,aAAa,CAAC;oBACjD;oBACApT,MAAM,CAAC2H,mBAAmB,CAACkB,OAAO,GAAGuK,QAAQ;;oBAE7C;oBACA,IAAIpT,MAAM,CAAC4H,0BAA0B,IAAI,CAAC5H,MAAM,CAAC4H,0BAA0B,CAACiB,OAAO,EAAE;sBACnFjI,OAAO,CAACC,GAAG,CAAC,SAASuS,QAAQ,aAAa,CAAC;sBAC3CI,UAAU,CAAC,MAAM;wBACfxT,MAAM,CAACyT,qBAAqB,CAACL,QAAQ,CAAC;sBACxC,CAAC,EAAE,GAAG,CAAC;oBACT;kBACF;gBACF,CAAC,MAAM;kBACL;kBACAlS,kBAAkB,CAAC6B,GAAG,CAACyE,OAAO,EAAE;oBAC9B+L,UAAU,EAAEnO,IAAI,CAACC,GAAG,CAAC,CAAC;oBACtBqC,MAAM,EAAEsK;kBACV,CAAC,CAAC;kBACFpR,OAAO,CAACC,GAAG,CAAC,sBAAsB2G,OAAO,YAAY,CAAC;gBACxD;cACF,CAAC,MAAM;gBACL5G,OAAO,CAACoB,KAAK,CAAC,eAAe,EAAE2I,YAAY,CAAC;cAC9C;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL/J,OAAO,CAACoB,KAAK,CAAC,oCAAoC,EAAE0J,OAAO,CAAC;UAC9D;QACF,CAAC,CAAC,OAAO1J,KAAK,EAAE;UACdpB,OAAO,CAACoB,KAAK,CAAC,aAAa,EAAEA,KAAK,EAAEyJ,OAAO,CAAC;QAC9C;MACF;;MAEA;MACA,IAAI9C,KAAK,KAAK7I,WAAW,CAACQ,GAAG,IAAIoL,OAAO,CAACoB,IAAI,KAAK,KAAK,EAAE;QACvDlM,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE6K,OAAO,CAAC;;QAEhC;QACA1L,MAAM,CAAC4P,WAAW,CAAC;UACjB9C,IAAI,EAAE,KAAK;UACXxL,IAAI,EAAEoK,OAAO,CAACpK;QAChB,CAAC,EAAE,GAAG,CAAC;QAEP,MAAMoS,OAAO,GAAGhI,OAAO,CAACpK,IAAI;QAC5B,MAAMqS,KAAK,GAAGD,OAAO,CAACC,KAAK;QAC3B,MAAMC,MAAM,GAAGF,OAAO,CAACG,IAAI,IAAI,EAAE;QAEjCD,MAAM,CAAClH,OAAO,CAACoH,KAAK,IAAI;UACtB,MAAMC,OAAO,GAAGD,KAAK,CAACE,KAAK;UAC3B,MAAMC,SAAS,GAAGH,KAAK,CAACG,SAAS;UACjC,MAAMC,WAAW,GAAGJ,KAAK,CAACI,WAAW;UACrC,MAAMC,SAAS,GAAGL,KAAK,CAACK,SAAS;UACjC,MAAMC,OAAO,GAAGN,KAAK,CAACM,OAAO;;UAE7B;UACA,MAAM/G,QAAQ,GAAG3I,SAAS,CAACmE,OAAO,CAACmC,YAAY,CAC7CC,UAAU,CAACyI,OAAO,CAACW,OAAO,CAAC,EAC3BpJ,UAAU,CAACyI,OAAO,CAACY,MAAM,CAC3B,CAAC;;UAED;UACA,IAAIC,WAAW,GAAG,EAAE;UACpB,IAAIC,YAAY,GAAG,EAAE;UAErB,QAAOP,SAAS;YACd,KAAK,KAAK;cAAG;cACXM,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,QAAQ;cACtBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,MAAM;cAAE;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF;cACED,WAAW,GAAGL,WAAW,IAAI,MAAM;cACnCM,YAAY,GAAG,SAAS;UAC5B;;UAEA;UACAC,iBAAiB,CAACpH,QAAQ,EAAEkH,WAAW,EAAEC,YAAY,CAAC;UAEtD5T,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;YACtB6T,IAAI,EAAEX,OAAO;YACbY,IAAI,EAAEV,SAAS;YACfW,IAAI,EAAEV,WAAW;YACjBW,IAAI,EAAEV,SAAS;YACfW,IAAI,EAAEV,OAAO;YACbW,EAAE,EAAE1H;UACN,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAI1E,KAAK,KAAK7I,WAAW,CAACP,KAAK,IAAImM,OAAO,CAACoB,IAAI,KAAK,OAAO,EAAE;QAC3DlM,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE6K,OAAO,CAAC;QAEjC,MAAMsJ,SAAS,GAAGtJ,OAAO,CAACpK,IAAI;QAC9B,MAAM2T,OAAO,GAAGD,SAAS,CAACC,OAAO;QACjC,MAAMC,SAAS,GAAGF,SAAS,CAACE,SAAS;QACrC,MAAMC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAMjP,QAAQ,GAAG;UACfN,QAAQ,EAAEqF,UAAU,CAAC+J,SAAS,CAACrF,OAAO,CAAC;UACvChK,SAAS,EAAEsF,UAAU,CAAC+J,SAAS,CAACtF,QAAQ;QAC1C,CAAC;;QAED;QACA,MAAMrC,QAAQ,GAAG3I,SAAS,CAACmE,OAAO,CAACmC,YAAY,CAAC9E,QAAQ,CAACP,SAAS,EAAEO,QAAQ,CAACN,QAAQ,CAAC;;QAEtF;QACA,QAAOsP,SAAS;UACd,KAAK,GAAG;YAAG;YACTT,iBAAiB,CAACpH,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;YAClD;UACF,KAAK,KAAK;YAAG;YACXoH,iBAAiB,CAACpH,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACXoH,iBAAiB,CAACpH,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;YAC/C;UACF,KAAK,IAAI;YAAG;YACV,MAAM+H,UAAU,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAE;YAC1CZ,iBAAiB,CAACpH,QAAQ,EAAE,KAAK+H,UAAU,MAAM,EAAE,SAAS,CAAC;YAC7D;UACF,KAAK,IAAI;YAAG;YACVX,iBAAiB,CAACpH,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVoH,iBAAiB,CAACpH,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YAAG;YACZoH,iBAAiB,CAACpH,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVoH,iBAAiB,CAACpH,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVoH,iBAAiB,CAACpH,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX,MAAMiI,YAAY,GAAGN,SAAS,CAACK,UAAU,CAAC,CAAE;YAC5C,MAAME,QAAQ,GAAGP,SAAS,CAACQ,UAAU,CAAC,CAAM;YAC5Cf,iBAAiB,CAACpH,QAAQ,EAAE,QAAQoI,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,EAAE,SAAS,CAAC;YAC/F;QACJ;QAEA;MACF;MACA;MACA3U,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrB8H,KAAK;QACLmE,IAAI,EAAEpB,OAAO,CAACoB,IAAI;QAClBxL,IAAI,EAAEoK;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAO1J,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCpB,OAAO,CAACoB,KAAK,CAAC,SAAS,EAAEyJ,OAAO,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMiK,cAAc,GAAGA,CAAA,KAAM;IAC3B9U,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAM8U,KAAK,GAAG,QAAQ7V,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnES,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE8U,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBlV,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED+U,EAAE,CAACG,SAAS,GAAIjC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMrI,OAAO,GAAGE,IAAI,CAACC,KAAK,CAACkI,KAAK,CAACxS,IAAI,CAAC;;QAEtC;QACA,IAAImK,OAAO,CAACqB,IAAI,KAAK,SAAS,EAAE;UAC9BlM,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE4K,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACqB,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIrB,OAAO,CAACqB,IAAI,KAAK,SAAS,IAAIrB,OAAO,CAAC9C,KAAK,IAAI8C,OAAO,CAACC,OAAO,EAAE;UAClE;UACAF,iBAAiB,CAACC,OAAO,CAAC9C,KAAK,EAAEgD,IAAI,CAACqK,SAAS,CAACvK,OAAO,CAACC,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAO1J,KAAK,EAAE;QACdpB,OAAO,CAACoB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAED4T,EAAE,CAACK,OAAO,GAAIjU,KAAK,IAAK;MACtBpB,OAAO,CAACoB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAED4T,EAAE,CAACM,OAAO,GAAG,MAAM;MACjBtV,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACA2S,UAAU,CAACkC,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACA7Q,aAAa,CAACgE,OAAO,GAAG+M,EAAE;EAC5B,CAAC;EAEDnY,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+G,YAAY,CAACqE,OAAO,EAAE;;IAE3B;IACAsN,aAAa,CAAC,CAAC;;IAEf;IACA5W,KAAK,GAAG,IAAI1B,KAAK,CAACuY,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAIxY,KAAK,CAACyY,iBAAiB,CACxC,EAAE,EACFtW,MAAM,CAACuW,UAAU,GAAGvW,MAAM,CAACwW,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAACnQ,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCsT,MAAM,CAACvM,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB5C,SAAS,CAAC2B,OAAO,GAAGwN,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAI5Y,KAAK,CAAC6Y,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAAC5W,MAAM,CAACuW,UAAU,EAAEvW,MAAM,CAACwW,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAAC9W,MAAM,CAAC+W,gBAAgB,CAAC;IAC/CvS,YAAY,CAACqE,OAAO,CAACmO,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAIrZ,KAAK,CAACsZ,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5D5X,KAAK,CAACsP,GAAG,CAACqI,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAIvZ,KAAK,CAACwZ,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAAClR,QAAQ,CAACnD,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1CxD,KAAK,CAACsP,GAAG,CAACuI,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAIzZ,KAAK,CAACwZ,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAACpR,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3CxD,KAAK,CAACsP,GAAG,CAACyI,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAI1Z,KAAK,CAAC2Z,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAACrR,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCwU,SAAS,CAACE,KAAK,GAAG7T,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7B0T,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACpU,QAAQ,GAAG,GAAG;IACxB5D,KAAK,CAACsP,GAAG,CAAC0I,SAAS,CAAC;;IAEpB;IACArY,QAAQ,GAAG,IAAInB,aAAa,CAACsY,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzD/X,QAAQ,CAAC0Y,aAAa,GAAG,IAAI;IAC7B1Y,QAAQ,CAAC2Y,aAAa,GAAG,IAAI;IAC7B3Y,QAAQ,CAAC4Y,kBAAkB,GAAG,KAAK;IACnC5Y,QAAQ,CAAC8K,WAAW,GAAG,EAAE;IACzB9K,QAAQ,CAAC+K,WAAW,GAAG,GAAG;IAC1B/K,QAAQ,CAACgL,aAAa,GAAGtG,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC3E,QAAQ,CAACiL,aAAa,GAAG,CAAC;IAC1BjL,QAAQ,CAAC0K,MAAM,CAAC7G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5B7D,QAAQ,CAAC6K,MAAM,CAAC,CAAC;;IAEjB;IACAnJ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBwV,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBnX,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBgI,SAAS,EAAE,CAAC,CAACA,SAAS,CAAC2B;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMkP,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAIra,UAAU,CAAC,CAAC;QACtCqa,aAAa,CAACC,IAAI,CAChB,GAAG5X,QAAQ,uBAAuB,EACjC6X,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAAC9Y,KAAK;;UAE/B;UACA,MAAMgZ,gBAAgB,GAAG,IAAI1a,KAAK,CAAC2a,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACnI,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAI1S,KAAK,CAAC4a,oBAAoB,CAAC;kBACjDtQ,KAAK,EAAE,QAAQ;kBAAO;kBACtBuQ,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIxI,KAAK,CAACE,QAAQ,CAACpB,GAAG,EAAE;kBACtBqB,WAAW,CAACrB,GAAG,GAAGkB,KAAK,CAACE,QAAQ,CAACpB,GAAG;gBACtC;;gBAEA;gBACAkB,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5B3P,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEuP,KAAK,CAACtF,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMwN,YAAY,CAACO,QAAQ,CAAClL,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMyC,KAAK,GAAGkI,YAAY,CAACO,QAAQ,CAAC,CAAC,CAAC;YACtCN,gBAAgB,CAAC1J,GAAG,CAACuB,KAAK,CAAC;UAC7B;;UAEA;UACA7Q,KAAK,CAACsP,GAAG,CAAC0J,gBAAgB,CAAC;;UAE3B;UACA7Z,gBAAgB,GAAG6Z,gBAAgB;UAEnC3X,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9BiY,kBAAkB,CAAC,IAAI,CAAC;UACxBb,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAQ,GAAG,IAAK;UACPnY,OAAO,CAACC,GAAG,CAAC,aAAa,CAACkY,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAE3V,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACD4U,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMgB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA;;QAEA;QACAxD,cAAc,CAAC,CAAC;;QAEhB;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAEF,CAAC,CAAC,OAAO1T,KAAK,EAAE;QACdpB,OAAO,CAACoB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAMmX,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAIrB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMoB,WAAW,GAAIC,WAAW,IAAK;UACnC3Y,OAAO,CAACC,GAAG,CAAC,WAAWuY,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAI1b,UAAU,CAAC,CAAC;UAC/B0b,MAAM,CAACpB,IAAI,CACTgB,GAAG,EACFf,IAAI,IAAK;YACRzX,OAAO,CAACC,GAAG,CAAC,WAAWuY,GAAG,EAAE,CAAC;YAC7BnB,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAU,GAAG,IAAK;YACPnY,OAAO,CAACC,GAAG,CAAC,SAAS,CAACkY,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAE3V,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAtB,KAAK,IAAK;YACTpB,OAAO,CAACoB,KAAK,CAAC,SAASoX,GAAG,EAAE,EAAEpX,KAAK,CAAC;YACpC,IAAIuX,WAAW,GAAG,CAAC,EAAE;cACnB3Y,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3B2S,UAAU,CAAC,MAAM8F,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACLrB,MAAM,CAAClW,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAEDsX,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAI1b,UAAU,CAAC,CAAC;IAC/B0b,MAAM,CAACpB,IAAI,CACT,GAAG5X,QAAQ,4BAA4B,EACvC,MAAO6X,IAAI,IAAK;MACd,IAAI;QACF,MAAM9K,KAAK,GAAG8K,IAAI,CAAC9Y,KAAK;QACxBgO,KAAK,CAACkM,KAAK,CAAC1W,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBwK,KAAK,CAACrH,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAE3B;QACA,IAAIxD,KAAK,EAAE;UACXA,KAAK,CAACsP,GAAG,CAACtB,KAAK,CAAC;;UAEhB;UACA,MAAM2L,eAAe,CAAC,CAAC;QACvB,CAAC,MAAM;UACLtY,OAAO,CAACoB,KAAK,CAAC,eAAe,CAAC;QAChC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdpB,OAAO,CAACoB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACA+W,GAAG,IAAK;MACPnY,OAAO,CAACC,GAAG,CAAC,SAAS,CAACkY,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAE3V,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAtB,KAAK,IAAK;MACTpB,OAAO,CAACoB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpB,OAAO,CAACoB,KAAK,CAAC,OAAO,EAAE;QACrB0X,IAAI,EAAE1X,KAAK,CAAC8K,IAAI;QAChB6M,IAAI,EAAE3X,KAAK,CAACyJ,OAAO;QACnBmO,KAAK,EAAE,GAAGpZ,QAAQ,4BAA4B;QAC9CqZ,KAAK,EAAE,GAAGrZ,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMsZ,OAAO,GAAGA,CAAA,KAAM;MACpBhV,iBAAiB,CAAC+D,OAAO,GAAGkR,qBAAqB,CAACD,OAAO,CAAC;;MAE1D;MACA7b,KAAK,CAAC8L,MAAM,CAAC,CAAC;;MAEd;MACA,MAAMiQ,WAAW,GAAG5U,IAAI,CAACC,GAAG,CAAC,CAAC;MAC9B,MAAM4U,SAAS,GAAG,CAACD,WAAW,GAAG7U,oBAAoB,CAAC0D,OAAO,IAAI,KAAK,CAAC,CAAC;MACxE1D,oBAAoB,CAAC0D,OAAO,GAAGmR,WAAW;;MAE1C;MACA1U,qBAAqB,CAACoH,OAAO,CAAEkB,KAAK,IAAK;QACvCA,KAAK,CAAC7D,MAAM,CAACkQ,SAAS,CAAC;MACzB,CAAC,CAAC;;MAEF;MACA,IAAIzU,wBAAwB,EAAE;QAC5BA,wBAAwB,CAACuE,MAAM,CAACkQ,SAAS,CAAC;MAC5C;MAEA,IAAIhb,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAAC4J,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMoR,UAAU,GAAGxb,gBAAgB,CAACwH,QAAQ,CAAC1D,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAM2X,eAAe,GAAGzb,gBAAgB,CAACgP,QAAQ,CAAC9K,CAAC;;QAEnD;QACA;QACA,MAAMwX,gBAAgB,GAAG,EAAED,eAAe,GAAGvW,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;;QAEnE;QACA,MAAMwW,YAAY,GAAG,IAAIxc,KAAK,CAAC2F,OAAO,CACpC,CAAC,EAAE,GAAGI,IAAI,CAAC0W,GAAG,CAACF,gBAAgB,CAAC,EAChC,GAAG,EACH,CAAC,EAAE,GAAGxW,IAAI,CAAC2W,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA,MAAMI,oBAAoB,GAAGN,UAAU,CAAC1X,KAAK,CAAC,CAAC,CAACqM,GAAG,CAACwL,YAAY,CAAC;QACjE,MAAMI,YAAY,GAAGP,UAAU,CAAC1X,KAAK,CAAC,CAAC;;QAEvC;QACA,IAAI,CAACwC,kBAAkB,CAAC6D,OAAO,EAAE;UAC/B7D,kBAAkB,CAAC6D,OAAO,GAAG2R,oBAAoB,CAAChY,KAAK,CAAC,CAAC;QAC3D;QAEA,IAAI,CAACyC,gBAAgB,CAAC4D,OAAO,EAAE;UAC7B5D,gBAAgB,CAAC4D,OAAO,GAAG4R,YAAY,CAACjY,KAAK,CAAC,CAAC;QACjD;;QAEA;QACAwC,kBAAkB,CAAC6D,OAAO,CAAC6R,IAAI,CAACF,oBAAoB,EAAE,CAAC,GAAGtV,eAAe,CAAC;QAC1ED,gBAAgB,CAAC4D,OAAO,CAAC6R,IAAI,CAACD,YAAY,EAAE,CAAC,GAAGvV,eAAe,CAAC;;QAEhE;QACAmR,MAAM,CAACnQ,QAAQ,CAACwD,IAAI,CAAC1E,kBAAkB,CAAC6D,OAAO,CAAC;;QAEhD;QACAwN,MAAM,CAACnN,EAAE,CAACnG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACAsT,MAAM,CAACvM,MAAM,CAAC7E,gBAAgB,CAAC4D,OAAO,CAAC;;QAEvC;QACAwN,MAAM,CAACsE,sBAAsB,CAAC,CAAC;QAC/BtE,MAAM,CAACjM,YAAY,CAAC,CAAC;QACrBiM,MAAM,CAAChM,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACAnL,QAAQ,CAAC4J,OAAO,GAAG,KAAK;;QAExB;QACA5J,QAAQ,CAAC0K,MAAM,CAACF,IAAI,CAACzE,gBAAgB,CAAC4D,OAAO,CAAC;QAC9C3J,QAAQ,CAAC6K,MAAM,CAAC,CAAC;QAEjBnJ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnB+Z,IAAI,EAAEV,UAAU,CAAC5O,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAEgL,MAAM,CAACnQ,QAAQ,CAACoF,OAAO,CAAC,CAAC;UAC/BuP,IAAI,EAAE5V,gBAAgB,CAAC4D,OAAO,CAACyC,OAAO,CAAC,CAAC;UACxCwP,IAAI,EAAEzE,MAAM,CAAC0E,iBAAiB,CAAC,IAAIld,KAAK,CAAC2F,OAAO,CAAC,CAAC,CAAC,CAAC8H,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIrM,UAAU,KAAK,QAAQ,EAAE;QAClC;QACA+F,kBAAkB,CAAC6D,OAAO,GAAG,IAAI;QACjC5D,gBAAgB,CAAC4D,OAAO,GAAG,IAAI;;QAE/B;QACA3J,QAAQ,CAAC4J,OAAO,GAAG,IAAI;;QAEvB;QACAuN,MAAM,CAACnN,EAAE,CAACnG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAIa,IAAI,CAACK,GAAG,CAACoS,MAAM,CAACnQ,QAAQ,CAACtD,CAAC,CAAC,GAAG,EAAE,EAAE;UACpCyT,MAAM,CAACnQ,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9B7D,QAAQ,CAAC0K,MAAM,CAAC7G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BsT,MAAM,CAACvM,MAAM,CAAC5K,QAAQ,CAAC0K,MAAM,CAAC;UAC9B1K,QAAQ,CAAC6K,MAAM,CAAC,CAAC;QACnB;;QAEA;QACA;QACAsM,MAAM,CAACjM,YAAY,CAAC,CAAC;QACrBiM,MAAM,CAAChM,iBAAiB,CAAC,IAAI,CAAC;MAEhC,CAAC,MAAM,IAAIpL,UAAU,KAAK,cAAc,EAAE;QACxC;QACA+F,kBAAkB,CAAC6D,OAAO,GAAG,IAAI;QACjC5D,gBAAgB,CAAC4D,OAAO,GAAG,IAAI;;QAE/B;QACA3J,QAAQ,CAAC6K,MAAM,CAAC,CAAC;MACnB;MAEA,IAAI7K,QAAQ,EAAEA,QAAQ,CAAC6K,MAAM,CAAC,CAAC;MAC/B,IAAIxK,KAAK,IAAI8W,MAAM,EAAE;QACnBI,QAAQ,CAACuE,MAAM,CAACzb,KAAK,EAAE8W,MAAM,CAAC;MAChC;IACF,CAAC;IAEDyD,OAAO,CAAC,CAAC;;IAET;IACA,MAAMmB,YAAY,GAAGA,CAAA,KAAM;MACzB5E,MAAM,CAAC6E,MAAM,GAAGlb,MAAM,CAACuW,UAAU,GAAGvW,MAAM,CAACwW,WAAW;MACtDH,MAAM,CAACsE,sBAAsB,CAAC,CAAC;MAC/BlE,QAAQ,CAACG,OAAO,CAAC5W,MAAM,CAACuW,UAAU,EAAEvW,MAAM,CAACwW,WAAW,CAAC;IACzD,CAAC;IACDxW,MAAM,CAACmb,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACAjb,MAAM,CAACob,aAAa,GAAG,MAAM;MAC3B,IAAIlU,SAAS,CAAC2B,OAAO,EAAE;QACrB3B,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzCmE,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC5C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC;QAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAInL,QAAQ,EAAE;UACZA,QAAQ,CAAC0K,MAAM,CAAC7G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B7D,QAAQ,CAAC4J,OAAO,GAAG,IAAI;UACvB5J,QAAQ,CAAC6K,MAAM,CAAC,CAAC;QACnB;QAEA9K,UAAU,GAAG,QAAQ;QACrB2B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAIiE,iBAAiB,CAAC+D,OAAO,EAAE;QAC7BwS,oBAAoB,CAACvW,iBAAiB,CAAC+D,OAAO,CAAC;QAC/C/D,iBAAiB,CAAC+D,OAAO,GAAG,IAAI;MAClC;;MAEA;MACA,IAAIhK,oBAAoB,EAAE;QACxByc,aAAa,CAACzc,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;;MAEA;MACA,IAAIgG,aAAa,CAACgE,OAAO,EAAE;QACzBhE,aAAa,CAACgE,OAAO,CAAC0S,KAAK,CAAC,CAAC;QAC7B1W,aAAa,CAACgE,OAAO,GAAG,IAAI;MAC9B;;MAEA;MACA7I,MAAM,CAACwb,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;;MAElD;MACA,IAAIxE,QAAQ,IAAIjS,YAAY,CAACqE,OAAO,EAAE;QACpCrE,YAAY,CAACqE,OAAO,CAAC4S,WAAW,CAAChF,QAAQ,CAACQ,UAAU,CAAC;QACrDR,QAAQ,CAAChF,OAAO,CAAC,CAAC;MACpB;;MAEA;MACA,IAAI3Q,aAAa,EAAE;QACjBA,aAAa,CAAC4L,OAAO,CAAC,CAAC0C,SAAS,EAAExC,EAAE,KAAK;UACvC,IAAIwC,SAAS,CAAC7B,KAAK,IAAIhO,KAAK,EAAE;YAC5BA,KAAK,CAAC8P,MAAM,CAACD,SAAS,CAAC7B,KAAK,CAAC;UAC/B;QACF,CAAC,CAAC;QACFzM,aAAa,CAAC4a,KAAK,CAAC,CAAC;MACvB;;MAEA;MACAza,gBAAgB,CAACyL,OAAO,CAAEiP,QAAQ,IAAK;QACrC,IAAIpc,KAAK,IAAIoc,QAAQ,CAACpO,KAAK,EAAE;UAC3BhO,KAAK,CAAC8P,MAAM,CAACsM,QAAQ,CAACpO,KAAK,CAAC;QAC9B;MACF,CAAC,CAAC;MACFtM,gBAAgB,CAACya,KAAK,CAAC,CAAC;MACxBxa,kBAAkB,CAACwa,KAAK,CAAC,CAAC;;MAE1B;;MAEA;MACAnc,KAAK,GAAG,IAAI;MACZL,QAAQ,GAAG,IAAI;MACfC,qBAAqB,GAAG,IAAI;MAC5BC,qBAAqB,GAAG,IAAI;MAC5BC,oBAAoB,GAAG,IAAI;MAC3BC,0BAA0B,GAAG,IAAI;MACjCZ,gBAAgB,GAAG,IAAI;;MAEvB;MACA4G,qBAAqB,CAACoH,OAAO,CAAEkB,KAAK,IAAK;QACvCA,KAAK,CAACgO,aAAa,CAAC,CAAC;MACvB,CAAC,CAAC;MACFtW,qBAAqB,CAACoW,KAAK,CAAC,CAAC;;MAE7B;MACA,IAAIG,eAAe,IAAItc,KAAK,EAAE;QAC5BA,KAAK,CAAC8P,MAAM,CAACwM,eAAe,CAAC;QAC7B,IAAIrW,wBAAwB,EAAE;UAC5BA,wBAAwB,CAACoW,aAAa,CAAC,CAAC;UACxCpW,wBAAwB,GAAG,IAAI;QACjC;QACAqW,eAAe,GAAG,IAAI;MACxB;MAEAjb,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApD,SAAS,CAAC,MAAM;IACd;IACA0D,qBAAqB,CAAC,CAAC;;IAEvB;IACA,MAAM2a,uBAAuB,GAAGA,CAAA,KAAM;MACpClb,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCM,qBAAqB,CAAC,CAAC;IACzB,CAAC;;IAED;IACAnB,MAAM,CAACmb,gBAAgB,CAAC,oBAAoB,EAAEW,uBAAuB,CAAC;;IAEtE;IACA,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnC7a,qBAAqB,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC;;IAET;IACA,OAAO,MAAM;MACXnB,MAAM,CAACwb,mBAAmB,CAAC,oBAAoB,EAAEM,uBAAuB,CAAC;MACzER,aAAa,CAACS,UAAU,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAte,SAAS,CAAC,MAAM;IACd;IACA,IAAI8B,KAAK,IAAImF,SAAS,CAACmE,OAAO,EAAE;MAC9BjI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA,MAAMob,KAAK,GAAGzI,UAAU,CAAC,MAAM;QAC7B,IAAIjU,KAAK,IAAImF,SAAS,CAACmE,OAAO,EAAE;UAAG;UACjCqT,mBAAmB,CAACxX,SAAS,CAACmE,OAAO,CAAC;QACxC;MACF,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAMsT,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM;MACLrb,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC,EAAE,CAACtB,KAAK,CAAC,CAAC;;EAEX;EACA9B,SAAS,CAAC,MAAM;IACd,IAAI+G,YAAY,CAACqE,OAAO,EAAE;MACxB;MACA,MAAMuT,WAAW,GAAItI,KAAK,IAAK;QAC7B,IAAIvU,KAAK,IAAI2H,SAAS,CAAC2B,OAAO,EAAE;UAC9BjI,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEiT,KAAK,CAACuI,OAAO,EAAEvI,KAAK,CAACwI,OAAO,CAAC;UACpDC,gBAAgB,CAACzI,KAAK,EAAEtP,YAAY,CAACqE,OAAO,EAAEtJ,KAAK,EAAE2H,SAAS,CAAC2B,OAAO,EAAEvB,sBAAsB,CAAC;QACjG,CAAC,MAAM;UACL1G,OAAO,CAACuS,IAAI,CAAC,4BAA4B,CAAC;QAC5C;MACF,CAAC;;MAED;MACA3O,YAAY,CAACqE,OAAO,CAACsS,gBAAgB,CAAC,OAAO,EAAEiB,WAAW,CAAC;;MAE3D;MACAxb,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC2D,YAAY,CAACqE,OAAO,CAAC;;MAEpD;MACA,OAAO,MAAM;QACX,IAAIrE,YAAY,CAACqE,OAAO,EAAE;UACxBrE,YAAY,CAACqE,OAAO,CAAC2S,mBAAmB,CAAC,OAAO,EAAEY,WAAW,CAAC;UAC9Dxb,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACtB,KAAK,EAAE2H,SAAS,CAAC2B,OAAO,CAAC,CAAC;;EAE9B;EACA,MAAM2T,SAAS,GAAG5e,WAAW,CAAC,MAAM;IAClCgD,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B;EACF,CAAC,EAAE,CAAC2D,YAAY,EAAE+D,aAAa,EAAEtH,gBAAgB,CAAC,CAAC;;EAEnD;EACA,MAAMwb,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAM1K,QAAQ,GAAG,IAAIlU,KAAK,CAAC6e,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChD,MAAMpM,QAAQ,GAAG,IAAIzS,KAAK,CAAC8e,iBAAiB,CAAC;MAAExU,KAAK,EAAE;IAAS,CAAC,CAAC;IACjE,MAAM6K,iBAAiB,GAAG,IAAInV,KAAK,CAAC+e,IAAI,CAAC7K,QAAQ,EAAEzB,QAAQ,CAAC;;IAE5D;IACA,MAAMuM,YAAY,GAAG,IAAIhf,KAAK,CAACif,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC5D,MAAMC,YAAY,GAAG,IAAIlf,KAAK,CAAC8e,iBAAiB,CAAC;MAAExU,KAAK,EAAE;IAAS,CAAC,CAAC;IACrE,MAAM6U,SAAS,GAAG,IAAInf,KAAK,CAAC+e,IAAI,CAACC,YAAY,EAAEE,YAAY,CAAC;IAC5DC,SAAS,CAAC9W,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAClCiQ,iBAAiB,CAACnE,GAAG,CAACmO,SAAS,CAAC;IAEhC,OAAOhK,iBAAiB;EAC1B,CAAC;;EAED;EACA,MAAMiK,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAAC1d,KAAK,EAAE;;IAEZ;IACA0B,gBAAgB,CAACyL,OAAO,CAAC,CAACiP,QAAQ,EAAEnU,OAAO,KAAK;MAC9C,IAAImU,QAAQ,CAACpO,KAAK,EAAE;QAClB;QACA,MAAM2P,cAAc,GAAG,IAAIrf,KAAK,CAACsf,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxD,MAAMC,cAAc,GAAG,IAAIvf,KAAK,CAAC8e,iBAAiB,CAAC;UACjDxU,KAAK,EAAE,QAAQ;UAAC;UAChBuI,WAAW,EAAE,KAAK;UAClBW,OAAO,EAAE,GAAG;UAAG;UACfgM,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,MAAMC,UAAU,GAAG,IAAIzf,KAAK,CAAC+e,IAAI,CAACM,cAAc,EAAEE,cAAc,CAAC;QACjEE,UAAU,CAACpX,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE;;QAEnC;QACAua,UAAU,CAACC,QAAQ,GAAG;UACpBzQ,IAAI,EAAE,cAAc;UACpBtF,OAAO,EAAEA,OAAO;UAChBsD,IAAI,EAAE6Q,QAAQ,CAAChR,YAAY,CAACG,IAAI;UAChC0S,aAAa,EAAE;QACjB,CAAC;;QAED;QACA7B,QAAQ,CAACpO,KAAK,CAACsB,GAAG,CAACyO,UAAU,CAAC;QAE9B1c,OAAO,CAACC,GAAG,CAAC,OAAO8a,QAAQ,CAAChR,YAAY,CAACG,IAAI,KAAKtD,OAAO,aAAa,CAAC;MACzE;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA/J,SAAS,CAAC,MAAM;IACd;IACA,MAAMwe,KAAK,GAAGzI,UAAU,CAAC,MAAM;MAC7B,IAAIvS,gBAAgB,CAACwc,IAAI,GAAG,CAAC,EAAE;QAC7B7c,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1B;MACF;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAE;;IAEX,OAAO,MAAMsb,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACAxe,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACA,IAAImK,0BAA0B,CAACiB,OAAO,EAAE;QACtCyS,aAAa,CAAC1T,0BAA0B,CAACiB,OAAO,CAAC;QACjDjB,0BAA0B,CAACiB,OAAO,GAAG,IAAI;QACzCjI,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC9B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACApD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4J,mBAAmB,CAACE,OAAO,IAAIK,0BAA0B,CAACiB,OAAO,EAAE;MACtEyS,aAAa,CAAC1T,0BAA0B,CAACiB,OAAO,CAAC;MACjDjB,0BAA0B,CAACiB,OAAO,GAAG,IAAI;MACzClB,mBAAmB,CAACkB,OAAO,GAAG,IAAI;MAClCjI,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACnC;EACF,CAAC,EAAE,CAACwG,mBAAmB,CAACE,OAAO,CAAC,CAAC;;EAEjC;EACA9J,SAAS,CAAC,MAAM;IACd;IACA,IAAIY,iBAAiB,IAAIA,iBAAiB,CAACuM,aAAa,IAAIvM,iBAAiB,CAACuM,aAAa,CAAC+C,MAAM,GAAG,CAAC,EAAE;MACtG;MACA,IAAI,CAACxG,oBAAoB,EAAE;QACzB,MAAMuW,iBAAiB,GAAGrf,iBAAiB,CAACuM,aAAa,CAAC,CAAC,CAAC;QAC5DhK,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE6c,iBAAiB,CAAC5S,IAAI,CAAC;;QAEjD;QACA,MAAMmR,KAAK,GAAGzI,UAAU,CAAC,MAAM;UAC7B/I,wBAAwB,CAACiT,iBAAiB,CAAC5S,IAAI,CAAC;QAClD,CAAC,EAAE,IAAI,CAAC;QAER,OAAO,MAAMqR,YAAY,CAACF,KAAK,CAAC;MAClC;IACF;EACF,CAAC,EAAE,CAAC5d,iBAAiB,EAAE8I,oBAAoB,CAAC,CAAC;EAE7C,oBACE5I,OAAA,CAAAE,SAAA;IAAAoa,QAAA,gBACEta,OAAA;MAAMof,KAAK,EAAE1V,UAAW;MAAA4Q,QAAA,EAAC;IAAK;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrCxf,OAAA,CAACJ,MAAM;MACLwf,KAAK,EAAE7V,uBAAwB;MAC/BkW,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAExT,wBAAyB;MACnCyT,OAAO,EAAE7f,iBAAiB,CAACuM,aAAa,CAACsE,GAAG,CAACvE,YAAY,KAAK;QAC5DD,KAAK,EAAEC,YAAY,CAACG,IAAI;QACxBqT,KAAK,EAAExT,YAAY,CAACG;MACtB,CAAC,CAAC,CAAE;MACJ2S,IAAI,EAAC,OAAO;MACZW,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACb/X,MAAM,EAAE,IAAI;QACZgY,SAAS,EAAE;MACb,CAAE;MACF5T,KAAK,EAAEvD,oBAAoB,GAAGA,oBAAoB,CAAC2D,IAAI,GAAG6G;IAAU;MAAAiM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eACFxf,OAAA;MAAKggB,GAAG,EAAE/Z,YAAa;MAACmZ,KAAK,EAAE;QAAE3V,KAAK,EAAE,MAAM;QAAEyF,MAAM,EAAE;MAAO;IAAE;MAAAmQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGnE1W,mBAAmB,CAACE,OAAO,iBAC1BhJ,OAAA;MACEof,KAAK,EAAE;QACLzX,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAACxD,CAAC,IAAI;QAC3CqF,GAAG,EAAE,GAAGV,mBAAmB,CAACnB,QAAQ,CAACtD,CAAC,IAAI;QAC1CyD,SAAS,EAAE,wBAAwB;QACnCC,MAAM,EAAE,IAAI;QACZK,eAAe,EAAE,qBAAqB;QACtCwB,KAAK,EAAE,OAAO;QACdtB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,8BAA8B;QACzCN,OAAO,EAAE,GAAG;QACZ8X,QAAQ,EAAE,OAAO;QAAE;QACnBzX,QAAQ,EAAE,MAAM,CAAC;MACnB,CAAE;MAAA8R,QAAA,GAEDxR,mBAAmB,CAACI,OAAO,eAC5BlJ,OAAA;QACEof,KAAK,EAAE;UACLzX,QAAQ,EAAE,UAAU;UACpB6B,GAAG,EAAE,KAAK;UACV0W,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,MAAM;UAClB9X,MAAM,EAAE,MAAM;UACduB,KAAK,EAAE,OAAO;UACdpB,QAAQ,EAAE,MAAM;UAChBD,MAAM,EAAE,SAAS;UACjBJ,OAAO,EAAE;QACX,CAAE;QACFiY,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAACtX,sBAAsB,CAAE;QAAAuR,QAAA,EAC3D;MAED;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAEDxf,OAAA;MAAKof,KAAK,EAAE1X,oBAAqB;MAAA4S,QAAA,gBAC/Bta,OAAA;QACEof,KAAK,EAAE;UACL,GAAGlX,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF4Y,OAAO,EAAE/V,kBAAmB;QAAAiQ,QAAA,EAC7B;MAED;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxf,OAAA;QACEof,KAAK,EAAE;UACL,GAAGlX,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF4Y,OAAO,EAAE5V,kBAAmB;QAAA8P,QAAA,EAC7B;MAED;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAAxZ,EAAA,CAvyDMJ,WAAW;AAAA0a,EAAA,GAAX1a,WAAW;AAwyDjB,SAAS0M,gBAAgBA,CAACiO,IAAI,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAMC,MAAM,GAAG;IACbC,QAAQ,EAAEF,UAAU,CAACE,QAAQ,IAAI,OAAO;IACxClY,QAAQ,EAAEgY,UAAU,CAAChY,QAAQ,IAAI,EAAE;IAAE;IACrCqB,UAAU,EAAE2W,UAAU,CAAC3W,UAAU,IAAI,MAAM;IAC3C8W,eAAe,EAAEH,UAAU,CAACG,eAAe,IAAI,CAAC;IAChDC,WAAW,EAAEJ,UAAU,CAACI,WAAW,IAAI;MAAEpO,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAI,CAAC;IACnEvK,eAAe,EAAEoY,UAAU,CAACpY,eAAe,IAAI;MAAEoK,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAI,CAAC;IACjFC,SAAS,EAAE4N,UAAU,CAAC5N,SAAS,IAAI;MAAEJ,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAI,CAAC;IAC/DxK,OAAO,EAAEqY,UAAU,CAACrY,OAAO,IAAI;EACjC,CAAC;;EAED;EACA,MAAM0Y,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;EAEvC;EACAD,OAAO,CAACE,IAAI,GAAG,GAAGT,MAAM,CAAC5W,UAAU,IAAI4W,MAAM,CAACjY,QAAQ,MAAMiY,MAAM,CAACC,QAAQ,EAAE;;EAE7E;EACA,MAAMS,SAAS,GAAGH,OAAO,CAACI,WAAW,CAACb,IAAI,CAAC,CAAC9W,KAAK;;EAEjD;EACA,MAAMA,KAAK,GAAG0X,SAAS,GAAG,CAAC,GAAGV,MAAM,CAACtY,OAAO,GAAG,CAAC,GAAGsY,MAAM,CAACE,eAAe;EACzE,MAAMzR,MAAM,GAAGuR,MAAM,CAACjY,QAAQ,GAAG,CAAC,GAAGiY,MAAM,CAACtY,OAAO,GAAG,CAAC,GAAGsY,MAAM,CAACE,eAAe;EAEhFE,MAAM,CAACpX,KAAK,GAAGA,KAAK;EACpBoX,MAAM,CAAC3R,MAAM,GAAGA,MAAM;;EAEtB;EACA8R,OAAO,CAACE,IAAI,GAAG,GAAGT,MAAM,CAAC5W,UAAU,IAAI4W,MAAM,CAACjY,QAAQ,MAAMiY,MAAM,CAACC,QAAQ,EAAE;EAC7EM,OAAO,CAACK,YAAY,GAAG,QAAQ;;EAE/B;EACA,MAAMC,MAAM,GAAG,CAAC;EAChBN,OAAO,CAACO,SAAS,CAAC,CAAC;EACnBP,OAAO,CAACQ,MAAM,CAACf,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,CAAC;EACvEK,OAAO,CAACS,MAAM,CAAChY,KAAK,GAAGgX,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,CAAC;EAC/EK,OAAO,CAACU,KAAK,CAACjY,KAAK,GAAGgX,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAElX,KAAK,GAAGgX,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEA,MAAM,CAAC;EAC9IN,OAAO,CAACS,MAAM,CAAChY,KAAK,GAAGgX,MAAM,CAACE,eAAe,EAAEzR,MAAM,GAAGuR,MAAM,CAACE,eAAe,GAAGW,MAAM,CAAC;EACxFN,OAAO,CAACU,KAAK,CAACjY,KAAK,GAAGgX,MAAM,CAACE,eAAe,EAAEzR,MAAM,GAAGuR,MAAM,CAACE,eAAe,EAAElX,KAAK,GAAGgX,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEpS,MAAM,GAAGuR,MAAM,CAACE,eAAe,EAAEW,MAAM,CAAC;EAChKN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEpS,MAAM,GAAGuR,MAAM,CAACE,eAAe,CAAC;EAChFK,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,EAAEzR,MAAM,GAAGuR,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAEzR,MAAM,GAAGuR,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEA,MAAM,CAAC;EAChJN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,CAAC;EACvEN,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,EAAEW,MAAM,CAAC;EAC9HN,OAAO,CAACW,SAAS,CAAC,CAAC;;EAEnB;EACAX,OAAO,CAACY,WAAW,GAAG,QAAQnB,MAAM,CAACG,WAAW,CAACpO,CAAC,KAAKiO,MAAM,CAACG,WAAW,CAACnO,CAAC,KAAKgO,MAAM,CAACG,WAAW,CAAClO,CAAC,KAAK+N,MAAM,CAACG,WAAW,CAACjO,CAAC,GAAG;EAChIqO,OAAO,CAACa,SAAS,GAAGpB,MAAM,CAACE,eAAe;EAC1CK,OAAO,CAACc,MAAM,CAAC,CAAC;;EAEhB;EACAd,OAAO,CAACe,SAAS,GAAG,QAAQtB,MAAM,CAACrY,eAAe,CAACoK,CAAC,KAAKiO,MAAM,CAACrY,eAAe,CAACqK,CAAC,KAAKgO,MAAM,CAACrY,eAAe,CAACsK,CAAC,KAAK+N,MAAM,CAACrY,eAAe,CAACuK,CAAC,GAAG;EAC9IqO,OAAO,CAACgB,IAAI,CAAC,CAAC;;EAEd;EACAhB,OAAO,CAACe,SAAS,GAAG,QAAQtB,MAAM,CAAC7N,SAAS,CAACJ,CAAC,KAAKiO,MAAM,CAAC7N,SAAS,CAACH,CAAC,KAAKgO,MAAM,CAAC7N,SAAS,CAACF,CAAC,KAAK+N,MAAM,CAAC7N,SAAS,CAACD,CAAC,GAAG;EACtHqO,OAAO,CAACiB,SAAS,GAAG,QAAQ;;EAE5B;EACAjB,OAAO,CAACkB,QAAQ,CAAC3B,IAAI,EAAE9W,KAAK,GAAG,CAAC,EAAEyF,MAAM,GAAG,CAAC,CAAC;;EAE7C;EACA,MAAMiT,OAAO,GAAG,IAAI7iB,KAAK,CAAC8iB,aAAa,CAACvB,MAAM,CAAC;EAC/CsB,OAAO,CAACE,SAAS,GAAG/iB,KAAK,CAACgjB,YAAY;EACtCH,OAAO,CAAC/P,WAAW,GAAG,IAAI;;EAE1B;EACA,MAAMmQ,cAAc,GAAG,IAAIjjB,KAAK,CAACkjB,cAAc,CAAC;IAC9C7R,GAAG,EAAEwR,OAAO;IACZhQ,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAMsQ,MAAM,GAAG,IAAInjB,KAAK,CAACojB,MAAM,CAACH,cAAc,CAAC;EAC/CE,MAAM,CAACvH,KAAK,CAAC1W,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EAC7Bie,MAAM,CAAC1Q,QAAQ,CAAC4Q,SAAS,GAAG,KAAK,CAAC,CAAC;;EAEnC;EACAF,MAAM,CAACzD,QAAQ,GAAG;IAChBuB,IAAI,EAAEA,IAAI;IACVE,MAAM,EAAEA;EACV,CAAC;EAED,OAAOgC,MAAM;AACf;;AAIA;AACAhhB,MAAM,CAACmhB,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAM9K,MAAM,GAAGgJ,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAIjL,MAAM,EAAE;MACV;MACA,MAAMkL,MAAM,GAAGlL,MAAM,CAACnQ,QAAQ,CAAC1D,KAAK,CAAC,CAAC;;MAEtC;MACA6T,MAAM,CAACnQ,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BsT,MAAM,CAACnN,EAAE,CAACnG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBsT,MAAM,CAACvM,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAuM,MAAM,CAACjM,YAAY,CAAC,CAAC;MACrBiM,MAAM,CAAChM,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMnL,QAAQ,GAAGmgB,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAItiB,QAAQ,EAAE;QACZA,QAAQ,CAAC0K,MAAM,CAAC7G,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B7D,QAAQ,CAAC6K,MAAM,CAAC,CAAC;MACnB;MAEAnJ,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxB4gB,GAAG,EAAEF,MAAM,CAACjW,OAAO,CAAC,CAAC;QACrBoW,GAAG,EAAErL,MAAM,CAACnQ,QAAQ,CAACoF,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOqW,CAAC,EAAE;IACV/gB,OAAO,CAACoB,KAAK,CAAC,YAAY,EAAE2f,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAMxL,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACFvV,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAM2Y,MAAM,GAAG,IAAI1b,UAAU,CAAC,CAAC;;IAE/B;IACA,IAAI;MACF,MAAM,CAAC8jB,WAAW,EAAEC,WAAW,EAAEC,UAAU,EAAEC,gBAAgB,CAAC,GAAG,MAAM/J,OAAO,CAACgK,GAAG,CAAC,CACjFxI,MAAM,CAACyI,SAAS,CAAC,GAAGzhB,QAAQ,uBAAuB,CAAC,EACpDgZ,MAAM,CAACyI,SAAS,CAAC,GAAGzhB,QAAQ,uBAAuB,CAAC,EACpDgZ,MAAM,CAACyI,SAAS,CAAC,GAAGzhB,QAAQ,sBAAsB,CAAC,EACnDgZ,MAAM,CAACyI,SAAS,CAAC,GAAGzhB,QAAQ,4BAA4B,CAAC,CAC1D,CAAC;;MAEF;MACArB,qBAAqB,GAAGyiB,WAAW,CAACriB,KAAK;MACzCJ,qBAAqB,CAACgR,QAAQ,CAAEC,KAAK,IAAK;QACxC,IAAIA,KAAK,CAACC,MAAM,EAAE;UACd,MAAME,WAAW,GAAG,IAAI1S,KAAK,CAAC4a,oBAAoB,CAAC;YACnDtQ,KAAK,EAAE,QAAQ;YAAG;YAClBuQ,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,GAAG;YACdC,eAAe,EAAE;UACnB,CAAC,CAAC;;UAEA;UACA,IAAIxI,KAAK,CAACE,QAAQ,CAACpB,GAAG,EAAE;YACtBqB,WAAW,CAACrB,GAAG,GAAGkB,KAAK,CAACE,QAAQ,CAACpB,GAAG;UACtC;UACAkB,KAAK,CAAC8R,OAAO,GAAG3R,WAAW;QAC/B;MACF,CAAC,CAAC;;MAEF;MACAnR,qBAAqB,GAAGyiB,WAAW,CAACtiB,KAAK;MACzC;MACAH,qBAAqB,CAACqa,KAAK,CAAC1W,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxC;MACA3D,qBAAqB,CAAC+Q,QAAQ,CAAEC,KAAK,IAAK;QACxC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAACoI,SAAS,GAAG,GAAG;UAC9BtI,KAAK,CAACE,QAAQ,CAACqI,SAAS,GAAG,GAAG;UAC9BvI,KAAK,CAACE,QAAQ,CAACsI,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;;MAEF;MACAvZ,oBAAoB,GAAGyiB,UAAU,CAACviB,KAAK;MACvC;MACAF,oBAAoB,CAACoa,KAAK,CAAC1W,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACvC;MACA1D,oBAAoB,CAAC8Q,QAAQ,CAAEC,KAAK,IAAK;QACvC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAACoI,SAAS,GAAG,GAAG;UAC9BtI,KAAK,CAACE,QAAQ,CAACqI,SAAS,GAAG,GAAG;UAC9BvI,KAAK,CAACE,QAAQ,CAACsI,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;;MAEA;MACA,IAAIkJ,UAAU,CAACK,UAAU,IAAIL,UAAU,CAACK,UAAU,CAACxU,MAAM,GAAG,CAAC,EAAE;QAC7DpI,gBAAgB,GAAGuc,UAAU,CAACK,UAAU;QACxCvhB,OAAO,CAACC,GAAG,CAAC,UAAU0E,gBAAgB,CAACoI,MAAM,OAAO,EACzCpI,gBAAgB,CAAC2J,GAAG,CAACkT,IAAI,KAAK;UAC5BC,EAAE,EAAED,IAAI,CAACtX,IAAI,IAAI,OAAO;UACxBwX,GAAG,EAAEF,IAAI,CAACG,MAAM,GAAGH,IAAI,CAACG,MAAM,CAAC5U,MAAM,GAAG,CAAC;UACzC6U,EAAE,EAAEJ,IAAI,CAAC7M;QACX,CAAC,CAAC,CAAC,CAAC;;QAEf;QACA,IAAIhW,KAAK,EAAE;UACTiU,UAAU,CAAC,MAAM;YACf,IAAI;cACF;cACAqI,eAAe,GAAGxc,oBAAoB,CAACmD,KAAK,CAAC,CAAC;cAC9CqZ,eAAe,CAAC3V,QAAQ,CAACnD,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;;cAE3C;cACAyC,wBAAwB,GAAG,IAAI3H,KAAK,CAACgQ,cAAc,CAACgO,eAAe,CAAC;;cAEpE;cACA,MAAM4G,UAAU,GAAGjd,wBAAwB,CAACwI,UAAU,CAACzI,gBAAgB,CAAC,CAAC,CAAC,CAAC;cAC3Ekd,UAAU,CAACtU,OAAO,CAACtQ,KAAK,CAACuQ,UAAU,CAAC;cACpCqU,UAAU,CAACpU,IAAI,CAAC,CAAC;;cAEjB;cACA9O,KAAK,CAACsP,GAAG,CAACgN,eAAe,CAAC;cAE1Bjb,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;YAClC,CAAC,CAAC,OAAOmB,KAAK,EAAE;cACdpB,OAAO,CAACoB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;YACrC;UACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QACZ;MACF,CAAC,MAAM;QACLpB,OAAO,CAACuS,IAAI,CAAC,WAAW,CAAC;MAC3B;;MAEA;MACA7T,0BAA0B,GAAGyiB,gBAAgB,CAACxiB,KAAK;MACnD;MACAD,0BAA0B,CAACma,KAAK,CAAC1W,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C;MACAzD,0BAA0B,CAAC6Q,QAAQ,CAAEC,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAACoI,SAAS,GAAG,GAAG;UAC9BtI,KAAK,CAACE,QAAQ,CAACqI,SAAS,GAAG,GAAG;UAC9BvI,KAAK,CAACE,QAAQ,CAACsI,eAAe,GAAG,GAAG;QACxC;MACF,CAAC,CAAC;MAEFhY,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IAC1B,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACA,IAAI;QACF,IAAI,CAAC7C,qBAAqB,EAAE;UAC1B,MAAMyiB,WAAW,GAAG,MAAMpI,MAAM,CAACyI,SAAS,CAAC,GAAGzhB,QAAQ,uBAAuB,CAAC;UAC9ErB,qBAAqB,GAAGyiB,WAAW,CAACriB,KAAK;QAC3C;;QAEA;QACA,IAAI,CAACD,0BAA0B,EAAE;UAC/BsB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC7B,MAAMkhB,gBAAgB,GAAG,MAAMvI,MAAM,CAACyI,SAAS,CAAC,GAAGzhB,QAAQ,4BAA4B,CAAC;UACxFlB,0BAA0B,GAAGyiB,gBAAgB,CAACxiB,KAAK;UACnDD,0BAA0B,CAACma,KAAK,CAAC1W,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC7CnC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QAC1B;MACF,CAAC,CAAC,OAAO6hB,GAAG,EAAE;QACZ9hB,OAAO,CAACoB,KAAK,CAAC,YAAY,EAAE0gB,GAAG,CAAC;MAClC;IACF;EACF,CAAC,CAAC,OAAO1gB,KAAK,EAAE;IACdpB,OAAO,CAACoB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;;AAED;AACA,MAAMyT,mBAAmB,GAAI3I,IAAI,IAAK;EACpC,MAAM6V,KAAK,GAAG;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,KAAK,CAAC7V,IAAI,CAAC,IAAI,MAAM;AAC9B,CAAC;;AAED;AACA,MAAM2H,iBAAiB,GAAGA,CAACvO,QAAQ,EAAE4Y,IAAI,EAAE3W,KAAK,KAAK;EACnD;EACA,IAAI,CAAC5I,KAAK,EAAE;IACVqB,OAAO,CAACuS,IAAI,CAAC,oBAAoB,CAAC;IAClC;EACF;EAEA,IAAI;IACJ;IACA,MAAM6N,MAAM,GAAGnQ,gBAAgB,CAACiO,IAAI,CAAC;IACrCkC,MAAM,CAAC9a,QAAQ,CAACnD,GAAG,CAACmD,QAAQ,CAACxD,CAAC,EAAE,EAAE,EAAE,CAACwD,QAAQ,CAACtD,CAAC,CAAC,CAAC,CAAE;;IAEnD;IACA4Q,UAAU,CAAC,MAAM;MACb;MACA,IAAIjU,KAAK,IAAIyhB,MAAM,CAAC4B,MAAM,EAAE;QAC9BrjB,KAAK,CAAC8P,MAAM,CAAC2R,MAAM,CAAC;MAClB;IACJ,CAAC,EAAE,GAAG,CAAC;;IAEP;IACAzhB,KAAK,CAACsP,GAAG,CAACmS,MAAM,CAAC;IAEjBpgB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;MACvBkU,EAAE,EAAE7O,QAAQ;MACZ2c,EAAE,EAAE/D,IAAI;MACRgE,EAAE,EAAE3a;IACN,CAAC,CAAC;EACF,CAAC,CAAC,OAAOnG,KAAK,EAAE;IACdpB,OAAO,CAACoB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;AACF,CAAC;;AAED;AACA,MAAMka,mBAAmB,GAAI6G,iBAAiB,IAAK;EACjD,IAAI,CAACxjB,KAAK,EAAE;IACVqB,OAAO,CAACoB,KAAK,CAAC,gBAAgB,CAAC;IAC/B;EACF;EAEA,IAAI,CAAC+gB,iBAAiB,EAAE;IACtBniB,OAAO,CAACoB,KAAK,CAAC,mBAAmB,CAAC;IAClC;EACF;EAEA,IAAI,CAAC1C,0BAA0B,EAAE;IAC/BsB,OAAO,CAACoB,KAAK,CAAC,UAAU,CAAC;IACzB;IACAghB,2BAA2B,CAACD,iBAAiB,CAAC;IAC9C;EACF;;EAEA;EACA9hB,gBAAgB,CAACyL,OAAO,CAAEiP,QAAQ,IAAK;IACrC,IAAIpc,KAAK,IAAIoc,QAAQ,CAACpO,KAAK,EAAE;MAC3BhO,KAAK,CAAC8P,MAAM,CAACsM,QAAQ,CAACpO,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACFtM,gBAAgB,CAACya,KAAK,CAAC,CAAC;;EAExB;EACArd,iBAAiB,CAACuM,aAAa,CAAC8B,OAAO,CAAC/B,YAAY,IAAI;IACtD;IACA,IAAIA,YAAY,CAACsY,eAAe,KAAK,KAAK,EAAE;MAC1CriB,OAAO,CAACC,GAAG,CAAC,UAAU8J,YAAY,CAACG,IAAI,kBAAkB,CAAC;MAC1D;IACF;;IAEA;IACA,IAAIH,YAAY,CAAC/E,QAAQ,IAAI+E,YAAY,CAAChF,SAAS,IAAIgF,YAAY,CAACnD,OAAO,EAAE;MAC3E;MACA,MAAM6F,QAAQ,GAAG0V,iBAAiB,CAAC/X,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,EAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC;MAEDhF,OAAO,CAACC,GAAG,CAAC,SAAS8J,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,iBAAiB6F,QAAQ,CAAC3K,CAAC,KAAK2K,QAAQ,CAACzK,CAAC,GAAG,CAAC;MAE7G,IAAI;QACF;QACA,MAAMoQ,iBAAiB,GAAG1T,0BAA0B,CAACkD,KAAK,CAAC,CAAC;;QAE5D;QACAwQ,iBAAiB,CAAClI,IAAI,GAAG,OAAOH,YAAY,CAACG,IAAI,EAAE;;QAEnD;QACAkI,iBAAiB,CAAC9M,QAAQ,CAACnD,GAAG,CAACsK,QAAQ,CAAC3K,CAAC,EAAE,EAAE,EAAE,CAAC2K,QAAQ,CAACzK,CAAC,CAAC;;QAE3D;QACAoQ,iBAAiB,CAACyG,KAAK,CAAC1W,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;QAEvC;QACAiQ,iBAAiB,CAAC5B,WAAW,GAAG,GAAG;;QAEnC;QACA4B,iBAAiB,CAAC7C,QAAQ,CAACC,KAAK,IAAI;UAClC;UACA,IAAIA,KAAK,CAACC,MAAM,EAAE;YAChB;YACAD,KAAK,CAACE,QAAQ,CAACI,WAAW,GAAG,KAAK;YAClCN,KAAK,CAACE,QAAQ,CAACe,OAAO,GAAG,GAAG;YAC5BjB,KAAK,CAACE,QAAQ,CAAC4S,IAAI,GAAGrlB,KAAK,CAACslB,UAAU;YACtC/S,KAAK,CAACE,QAAQ,CAAC+M,UAAU,GAAG,IAAI;YAChCjN,KAAK,CAACE,QAAQ,CAAC4Q,SAAS,GAAG,IAAI;YAC/B9Q,KAAK,CAACE,QAAQ,CAACK,WAAW,GAAG,IAAI;;YAEjC;YACAP,KAAK,CAACgB,WAAW,GAAG,GAAG;UACzB;QACF,CAAC,CAAC;;QAEF;QACA4B,iBAAiB,CAACuK,QAAQ,GAAG;UAC3BzQ,IAAI,EAAE,cAAc;UACpBtF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;UAC7BsD,IAAI,EAAEH,YAAY,CAACG;QACrB,CAAC;;QAED;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;;QAEA;QACAvL,KAAK,CAACsP,GAAG,CAACmE,iBAAiB,CAAC;;QAE5B;QACA/R,gBAAgB,CAAC8B,GAAG,CAAC4H,YAAY,CAACnD,OAAO,EAAE;UACzC+F,KAAK,EAAEyF,iBAAiB;UACxBrI,YAAY,EAAEA,YAAY;UAC1BzE,QAAQ,EAAEmH;QACZ,CAAC,CAAC;QAEFzM,OAAO,CAACC,GAAG,CAAC,SAAS8J,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkB6F,QAAQ,CAAC3K,CAAC,KAAK,CAAC2K,QAAQ,CAACzK,CAAC,GAAG,CAAC;MACjH,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdpB,OAAO,CAACoB,KAAK,CAAC,QAAQ2I,YAAY,CAACG,IAAI,YAAY,EAAE9I,KAAK,CAAC;QAC3D;QACAya,wBAAwB,CAAC9R,YAAY,EAAE0C,QAAQ,EAAE0V,iBAAiB,CAAC;MACrE;IACF;EACF,CAAC,CAAC;;EAEF;EACAniB,OAAO,CAACC,GAAG,CAAC,OAAOI,gBAAgB,CAACwc,IAAI,SAAS,CAAC;EAClDxc,gBAAgB,CAACyL,OAAO,CAAC,CAACiP,QAAQ,EAAEnU,OAAO,KAAK;IAC9C5G,OAAO,CAACC,GAAG,CAAC,QAAQ2G,OAAO,KAAKmU,QAAQ,CAAChR,YAAY,CAACG,IAAI,EAAE,CAAC;EAC/D,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMkY,2BAA2B,GAAID,iBAAiB,IAAK;EACzD1kB,iBAAiB,CAACuM,aAAa,CAAC8B,OAAO,CAAC/B,YAAY,IAAI;IACtD;IACA,IAAIA,YAAY,CAACsY,eAAe,KAAK,KAAK,EAAE;MAC1C;IACF;IAEA,IAAItY,YAAY,CAAC/E,QAAQ,IAAI+E,YAAY,CAAChF,SAAS,IAAIgF,YAAY,CAACnD,OAAO,EAAE;MAC3E;MACA,MAAM6F,QAAQ,GAAG0V,iBAAiB,CAAC/X,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,EAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC;MAED6W,wBAAwB,CAAC9R,YAAY,EAAE0C,QAAQ,EAAE0V,iBAAiB,CAAC;IACrE;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMtG,wBAAwB,GAAGA,CAAC9R,YAAY,EAAE0C,QAAQ,EAAE0V,iBAAiB,KAAK;EAC9E;EACA,MAAMhR,QAAQ,GAAG,IAAIlU,KAAK,CAAC6e,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAClD,MAAMpM,QAAQ,GAAG,IAAIzS,KAAK,CAAC8e,iBAAiB,CAAC;IAC3CxU,KAAK,EAAE,QAAQ;IACfuI,WAAW,EAAE,KAAK;IAClBW,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM2B,iBAAiB,GAAG,IAAInV,KAAK,CAAC+e,IAAI,CAAC7K,QAAQ,EAAEzB,QAAQ,CAAC;;EAE5D;EACA0C,iBAAiB,CAAClI,IAAI,GAAG,SAASH,YAAY,CAACG,IAAI,EAAE;;EAErD;EACAkI,iBAAiB,CAAC9M,QAAQ,CAACnD,GAAG,CAACsK,QAAQ,CAAC3K,CAAC,EAAE,EAAE,EAAE,CAAC2K,QAAQ,CAACzK,CAAC,CAAC;;EAE3D;EACAoQ,iBAAiB,CAAC5B,WAAW,GAAG,GAAG;;EAEnC;EACA4B,iBAAiB,CAACuK,QAAQ,GAAG;IAC3BzQ,IAAI,EAAE,cAAc;IACpBtF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG;EACrB,CAAC;;EAED;EACA,MAAMsY,gBAAgB,GAAG,IAAIvlB,KAAK,CAACsf,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5D,MAAMkG,gBAAgB,GAAG,IAAIxlB,KAAK,CAAC8e,iBAAiB,CAAC;IACnDxU,KAAK,EAAE,QAAQ;IACfuI,WAAW,EAAE,IAAI;IACjBW,OAAO,EAAE,GAAG;IAAG;IACfgM,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAMiG,QAAQ,GAAG,IAAIzlB,KAAK,CAAC+e,IAAI,CAACwG,gBAAgB,EAAEC,gBAAgB,CAAC;EACnEC,QAAQ,CAACxY,IAAI,GAAG,YAAYH,YAAY,CAACG,IAAI,EAAE;EAC/CwY,QAAQ,CAAC/F,QAAQ,GAAG;IAClBzQ,IAAI,EAAE,cAAc;IACpBtF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG,IAAI;IACvByY,UAAU,EAAE;EACd,CAAC;EAEDvQ,iBAAiB,CAACnE,GAAG,CAACyU,QAAQ,CAAC;;EAE/B;EACA/jB,KAAK,CAACsP,GAAG,CAACmE,iBAAiB,CAAC;;EAE5B;EACA/R,gBAAgB,CAAC8B,GAAG,CAAC4H,YAAY,CAACnD,OAAO,EAAE;IACzC+F,KAAK,EAAEyF,iBAAiB;IACxBrI,YAAY,EAAEA,YAAY;IAC1BzE,QAAQ,EAAEmH;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMmW,aAAa,GAAG,IAAI3lB,KAAK,CAACsf,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAMsG,aAAa,GAAG,IAAI5lB,KAAK,CAAC8e,iBAAiB,CAAC;IAAExU,KAAK,EAAE;EAAS,CAAC,CAAC;EACtE,MAAMub,SAAS,GAAG,IAAI7lB,KAAK,CAAC+e,IAAI,CAAC4G,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAACxd,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;EAEhC;EACA2gB,SAAS,CAACnG,QAAQ,GAAG;IACnBzQ,IAAI,EAAE,cAAc;IACpBtF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG;EACrB,CAAC;EAEDkI,iBAAiB,CAACnE,GAAG,CAAC6U,SAAS,CAAC;EAEhC9iB,OAAO,CAACC,GAAG,CAAC,SAAS8J,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkB6F,QAAQ,CAAC3K,CAAC,KAAK,CAAC2K,QAAQ,CAACzK,CAAC,GAAG,CAAC;AACjH,CAAC;;AAED;AACA,MAAM2P,iBAAiB,GAAIL,OAAO,IAAK;EACrC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMI,oBAAoB,GAAIqR,OAAO,IAAK;EACxC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMpH,gBAAgB,GAAGA,CAACzI,KAAK,EAAE8P,SAAS,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,KAAK;EAC7F,IAAI,CAACH,SAAS,IAAI,CAACC,aAAa,IAAI,CAACC,cAAc,EAAE;EAErDljB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEiT,KAAK,CAACuI,OAAO,EAAEvI,KAAK,CAACwI,OAAO,CAAC;;EAEvD;EACA,MAAM0H,IAAI,GAAGJ,SAAS,CAACK,qBAAqB,CAAC,CAAC;EAC9C,MAAMC,MAAM,GAAI,CAACpQ,KAAK,CAACuI,OAAO,GAAG2H,IAAI,CAAC5d,IAAI,IAAIwd,SAAS,CAACO,WAAW,GAAI,CAAC,GAAG,CAAC;EAC5E,MAAMC,MAAM,GAAG,EAAE,CAACtQ,KAAK,CAACwI,OAAO,GAAG0H,IAAI,CAACjc,GAAG,IAAI6b,SAAS,CAACS,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;;EAE7E;EACA,MAAMC,SAAS,GAAG,IAAIzmB,KAAK,CAAC0mB,SAAS,CAAC,CAAC;EACvC;EACAD,SAAS,CAACtF,MAAM,CAACwF,MAAM,CAACC,SAAS,GAAG,CAAC;EACrCH,SAAS,CAACtF,MAAM,CAAC0F,IAAI,CAACD,SAAS,GAAG,CAAC;EAEnC,MAAME,WAAW,GAAG,IAAI9mB,KAAK,CAAC+mB,OAAO,CAACV,MAAM,EAAEE,MAAM,CAAC;EACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,EAAEb,cAAc,CAAC;EAEpDljB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEqjB,MAAM,EAAEE,MAAM,CAAC;;EAEtC;EACA,MAAMU,mBAAmB,GAAG,EAAE;EAE9B7jB,gBAAgB,CAACyL,OAAO,CAAC,CAACiP,QAAQ,EAAEnU,OAAO,KAAK;IAC9C,IAAImU,QAAQ,CAACpO,KAAK,EAAE;MAClB;MACAuX,mBAAmB,CAACjS,IAAI,CAAC8I,QAAQ,CAACpO,KAAK,CAAC;MACxC;MACAoO,QAAQ,CAACpO,KAAK,CAAChG,OAAO,GAAG,IAAI;MAC7BoU,QAAQ,CAACpO,KAAK,CAAC6D,WAAW,GAAG,IAAI,CAAC,CAAC;MACnC;MACAuK,QAAQ,CAACpO,KAAK,CAAC4C,QAAQ,CAACC,KAAK,IAAI;QAC/BA,KAAK,CAAC7I,OAAO,GAAG,IAAI;QACpB6I,KAAK,CAACgB,WAAW,GAAG,IAAI;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEFxQ,OAAO,CAACC,GAAG,CAAC,QAAQikB,mBAAmB,CAACnX,MAAM,gBAAgB,CAAC;;EAE/D;EACA,MAAMoX,sBAAsB,GAAGT,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,EAAE,IAAI,CAAC;EAEpF,IAAIC,sBAAsB,CAACpX,MAAM,GAAG,CAAC,EAAE;IACrC/M,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkkB,sBAAsB,CAACpX,MAAM,CAAC;IACxDoX,sBAAsB,CAACrY,OAAO,CAAC,CAACuY,SAAS,EAAEC,KAAK,KAAK;MACnDtkB,OAAO,CAACC,GAAG,CAAC,QAAQqkB,KAAK,GAAG,EACjBD,SAAS,CAACE,MAAM,CAACra,IAAI,IAAI,KAAK,EAC9B,KAAK,EAAEma,SAAS,CAAC9hB,QAAQ,EACzB,WAAW,EAAE8hB,SAAS,CAACE,MAAM,CAAC5H,QAAQ,CAAC;IACpD,CAAC,CAAC;;IAEF;IACA,MAAM6H,GAAG,GAAGC,yBAAyB,CAACN,sBAAsB,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC;IAEvE,IAAIC,GAAG,IAAIA,GAAG,CAAC7H,QAAQ,IAAI6H,GAAG,CAAC7H,QAAQ,CAACzQ,IAAI,KAAK,cAAc,EAAE;MAC/D;MACA,MAAMtF,OAAO,GAAG4d,GAAG,CAAC7H,QAAQ,CAAC/V,OAAO;MACpC5G,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE2G,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;;MAEhE;MACA,IAAI8d,SAAS,GAAG9d,OAAO;MACvB,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,CAACvG,gBAAgB,CAAC+B,GAAG,CAACwE,OAAO,CAAC,IAAIvG,gBAAgB,CAAC+B,GAAG,CAAC2P,QAAQ,CAACnL,OAAO,CAAC,CAAC,EAAE;QAC5G8d,SAAS,GAAG3S,QAAQ,CAACnL,OAAO,CAAC;QAC7B5G,OAAO,CAACC,GAAG,CAAC,cAAcykB,SAAS,EAAE,CAAC;MACxC,CAAC,MAAM,IAAI,OAAO9d,OAAO,KAAK,QAAQ,IAAI,CAACvG,gBAAgB,CAAC+B,GAAG,CAACwE,OAAO,CAAC,IAAIvG,gBAAgB,CAAC+B,GAAG,CAAC+P,MAAM,CAACvL,OAAO,CAAC,CAAC,EAAE;QACjH8d,SAAS,GAAGvS,MAAM,CAACvL,OAAO,CAAC;QAC3B5G,OAAO,CAACC,GAAG,CAAC,eAAeykB,SAAS,EAAE,CAAC;MACzC;;MAEA;MACAtlB,MAAM,CAACyT,qBAAqB,CAAC6R,SAAS,CAAC;MACvC;IACF;EACF;;EAEA;EACA,MAAMC,UAAU,GAAGjB,SAAS,CAACU,gBAAgB,CAACnB,aAAa,CAAChL,QAAQ,EAAE,IAAI,CAAC;EAE3EjY,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE0kB,UAAU,CAAC5X,MAAM,CAAC;EAE7C,IAAI4X,UAAU,CAAC5X,MAAM,GAAG,CAAC,EAAE;IACzB;IACA4X,UAAU,CAAC7Y,OAAO,CAAC,CAACuY,SAAS,EAAEC,KAAK,KAAK;MACvC,MAAME,GAAG,GAAGH,SAAS,CAACE,MAAM;MAC5BvkB,OAAO,CAACC,GAAG,CAAC,UAAUqkB,KAAK,GAAG,EAAEE,GAAG,CAACta,IAAI,IAAI,KAAK,EACrC,WAAW,EAAEsa,GAAG,CAAC7H,QAAQ,EACzB,KAAK,EAAE0H,SAAS,CAAC9hB,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACA,KAAK,IAAI0H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0a,UAAU,CAAC5X,MAAM,EAAE9C,CAAC,EAAE,EAAE;MAC1C,MAAMua,GAAG,GAAGC,yBAAyB,CAACE,UAAU,CAAC1a,CAAC,CAAC,CAACsa,MAAM,CAAC;MAC3D,IAAIC,GAAG,IAAIA,GAAG,CAAC7H,QAAQ,IAAI6H,GAAG,CAAC7H,QAAQ,CAACzQ,IAAI,KAAK,cAAc,EAAE;QAC/D,MAAMtF,OAAO,GAAG4d,GAAG,CAAC7H,QAAQ,CAAC/V,OAAO;QACpC5G,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE2G,OAAO,CAAC;;QAEvC;QACAxH,MAAM,CAACyT,qBAAqB,CAACjM,OAAO,CAAC;QACrC;MACF;IACF;EACF;;EAEA;EACA5G,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;;EAE9B;EACA,IAAI2kB,YAAY,GAAG,IAAI;EACvB,IAAIxb,WAAW,GAAG,GAAG,CAAC,CAAC;;EAEvB/I,gBAAgB,CAACyL,OAAO,CAAC,CAACiP,QAAQ,EAAEnU,OAAO,KAAK;IAC9C,IAAImU,QAAQ,CAACpO,KAAK,EAAE;MAClB,MAAMkY,QAAQ,GAAG,IAAI5nB,KAAK,CAAC2F,OAAO,CAAC,CAAC;MACpC;MACAmY,QAAQ,CAACpO,KAAK,CAACmY,gBAAgB,CAACD,QAAQ,CAAC;;MAEzC;MACA,MAAME,SAAS,GAAGF,QAAQ,CAACjjB,KAAK,CAAC,CAAC;MAClCmjB,SAAS,CAACC,OAAO,CAAC9B,cAAc,CAAC;;MAEjC;MACA,MAAM+B,EAAE,GAAGF,SAAS,CAACjjB,CAAC,GAAGwhB,MAAM;MAC/B,MAAM4B,EAAE,GAAGH,SAAS,CAAC/iB,CAAC,GAAGwhB,MAAM;MAC/B,MAAMjhB,QAAQ,GAAGS,IAAI,CAACmiB,IAAI,CAACF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;MAE7CllB,OAAO,CAACC,GAAG,CAAC,MAAM2G,OAAO,OAAO,EAAErE,QAAQ,CAAC;MAE3C,IAAIA,QAAQ,GAAG6G,WAAW,EAAE;QAC1BA,WAAW,GAAG7G,QAAQ;QACtBqiB,YAAY,GAAG;UAAEhe,OAAO;UAAErE;QAAS,CAAC;MACtC;IACF;EACF,CAAC,CAAC;EAEF,IAAIqiB,YAAY,EAAE;IAChB5kB,OAAO,CAACC,GAAG,CAAC,oBAAoB2kB,YAAY,CAAChe,OAAO,SAASge,YAAY,CAACriB,QAAQ,EAAE,CAAC;;IAErF;IACAnD,MAAM,CAACyT,qBAAqB,CAAC+R,YAAY,CAAChe,OAAO,CAAC;IAClD;EACF;EAEA5G,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;;EAE1B;EACA,IAAIkjB,eAAe,EAAE;IACnBA,eAAe,CAAC7Q,IAAI,IAAI;MACtB,IAAIA,IAAI,CAAC3L,OAAO,EAAE;QAChB,OAAO;UAAE,GAAG2L,IAAI;UAAE3L,OAAO,EAAE;QAAM,CAAC;MACpC;MACA,OAAO2L,IAAI;IACb,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAM0L,kBAAkB,GAAImF,eAAe,IAAK;EAC9C;EACA,IAAI/jB,MAAM,CAAC4H,0BAA0B,IAAI5H,MAAM,CAAC4H,0BAA0B,CAACiB,OAAO,EAAE;IAClFyS,aAAa,CAACtb,MAAM,CAAC4H,0BAA0B,CAACiB,OAAO,CAAC;IACxD7I,MAAM,CAAC4H,0BAA0B,CAACiB,OAAO,GAAG,IAAI;IAChDjI,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B;;EAEA;EACA,IAAIb,MAAM,CAAC2H,mBAAmB,EAAE;IAC9B3H,MAAM,CAAC2H,mBAAmB,CAACkB,OAAO,GAAG,IAAI;EAC3C;;EAEA;EACAkb,eAAe,CAAC7Q,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAE3L,OAAO,EAAE;EAAM,CAAC,CAAC,CAAC;EACtD3G,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;AACtB,CAAC;;AAED;AACAb,MAAM,CAACgmB,qBAAqB,GAAIxe,OAAO,IAAK;EAC1C,IAAI;IAAA,IAAAye,qBAAA,EAAAC,sBAAA;IACF;IACA,MAAMzT,YAAY,GAAGxR,gBAAgB,CAACiC,GAAG,CAACsE,OAAO,IAAI,GAAG,CAAC;IACzD,IAAI,CAACiL,YAAY,EAAE;MACjB7R,OAAO,CAACoB,KAAK,CAAC,cAAc,EAAEwF,OAAO,CAAC;;MAEtC;MACA5G,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxBI,gBAAgB,CAACyL,OAAO,CAAC,CAACyZ,KAAK,EAAEvZ,EAAE,KAAK;QACtChM,OAAO,CAACC,GAAG,CAAC,KAAK+L,EAAE,KAAKuZ,KAAK,CAACxb,YAAY,CAACG,IAAI,EAAE,CAAC;MACpD,CAAC,CAAC;MAEF,OAAO,KAAK;IACd;;IAEA;IACA,MAAMsb,UAAU,GAAG3T,YAAY,CAAClF,KAAK;;IAErC;IACA,MAAM8Y,SAAS,GAAGnlB,kBAAkB,CAACgC,GAAG,CAACsE,OAAO,CAAC;IACjD,MAAMmD,YAAY,GAAG8H,YAAY,CAAC9H,YAAY;;IAE9C;IACA,IAAIlD,OAAO;IAEX,IAAI4e,SAAS,IAAIA,SAAS,CAAC3e,MAAM,EAAE;MACjCD,OAAO,gBACLlJ,OAAA;QAAKof,KAAK,EAAE;UAAEjX,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAEsW,SAAS,EAAE,OAAO;UAAEgI,SAAS,EAAE;QAAO,CAAE;QAAAzN,QAAA,gBACpFta,OAAA;UAAKof,KAAK,EAAE;YACVvV,UAAU,EAAE,MAAM;YAClBme,YAAY,EAAE,KAAK;YACnBxf,QAAQ,EAAE,MAAM;YAChByf,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAA5N,QAAA,GACClO,YAAY,CAACG,IAAI,EAAC,QAAM,EAACtD,OAAO,EAAC,GACpC;QAAA;UAAAoW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxf,OAAA;UAAAsa,QAAA,EACGwN,SAAS,CAAC3e,MAAM,CAACwH,GAAG,CAAC,CAAC+C,KAAK,EAAEiT,KAAK,KAAK;YACtC,IAAIwB,UAAU;YACd,QAAQzU,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBAAEiU,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;gBAAEA,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;cAAE;gBAASA,UAAU,GAAG,SAAS;gBAAE;YAC7C;YAEA,oBACEnoB,OAAA;cAAiBof,KAAK,EAAE;gBACtB4I,YAAY,EAAE,KAAK;gBACnB5f,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE,KAAK;gBACnBE,QAAQ,EAAE;cACZ,CAAE;cAAA8R,QAAA,gBACAta,OAAA;gBAAKof,KAAK,EAAE;kBAAEvV,UAAU,EAAE;gBAAO,CAAE;gBAAAyQ,QAAA,EAChCtG,iBAAiB,CAACN,KAAK,CAACC,OAAO;cAAC;gBAAA0L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNxf,OAAA;gBAAKof,KAAK,EAAE;kBAAEpX,OAAO,EAAE,MAAM;kBAAEogB,cAAc,EAAE;gBAAgB,CAAE;gBAAA9N,QAAA,gBAC/Dta,OAAA;kBAAAsa,QAAA,EAAM;gBAAI;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBxf,OAAA;kBAAMof,KAAK,EAAE;oBACXxV,KAAK,EAAEue,UAAU;oBACjBte,UAAU,EAAE,MAAM;oBAClBzB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAAgS,QAAA,EACC5G,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGR,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAG;gBAAI;kBAAAmL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNxf,OAAA;gBAAKof,KAAK,EAAE;kBAAEpX,OAAO,EAAE,MAAM;kBAAEogB,cAAc,EAAE;gBAAgB,CAAE;gBAAA9N,QAAA,gBAC/Dta,OAAA;kBAAAsa,QAAA,EAAM;gBAAK;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBxf,OAAA;kBAAMof,KAAK,EAAE;oBAAEvV,UAAU,EAAE;kBAAO,CAAE;kBAAAyQ,QAAA,GAAE5G,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAAkL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAzBEmH,KAAK;cAAAtH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxf,OAAA;UAAKof,KAAK,EAAE;YAAEiJ,SAAS,EAAE,KAAK;YAAE7f,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE;UAAO,CAAE;UAAA0Q,QAAA,GAAC,4BAC3D,EAAC,IAAIzT,IAAI,CAAC,CAAC,CAACyhB,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAAjJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACLtW,OAAO,gBACLlJ,OAAA;QAAKof,KAAK,EAAE;UAAEjX,OAAO,EAAE,KAAK;UAAE8X,QAAQ,EAAE;QAAQ,CAAE;QAAA3F,QAAA,gBAChDta,OAAA;UAAKof,KAAK,EAAE;YAAEvV,UAAU,EAAE,MAAM;YAAEme,YAAY,EAAE;UAAM,CAAE;UAAA1N,QAAA,EAAElO,YAAY,CAACG;QAAI;UAAA8S,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClFxf,OAAA;UAAAsa,QAAA,GAAK,kBAAM,EAACrR,OAAO;QAAA;UAAAoW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1Bxf,OAAA;UAAAsa,QAAA,EAAK;QAAU;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN;IACH;;IAEA;IACA,MAAM+I,OAAO,GAAG9mB,MAAM,CAACuW,UAAU,GAAG,CAAC;IACrC,MAAMwQ,OAAO,GAAG/mB,MAAM,CAACwW,WAAW,GAAG,CAAC;;IAEtC;IACA,MAAMuN,eAAe,IAAAkC,qBAAA,GAAG5G,QAAQ,CAAC+B,aAAa,CAAC,OAAO,CAAC,cAAA6E,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCe,gBAAgB,cAAAd,sBAAA,uBAAjDA,sBAAA,CAAmD5e,sBAAsB;IAEjG,IAAIyc,eAAe,EAAE;MACnB;MACAA,eAAe,CAAC;QACdxc,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBtB,QAAQ,EAAE;UAAExD,CAAC,EAAEokB,OAAO;UAAElkB,CAAC,EAAEmkB;QAAQ,CAAC;QACpCtf,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAA2e,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE3e,MAAM,KAAI;MAC/B,CAAC,CAAC;MAEF9G,OAAO,CAACC,GAAG,CAAC,SAAS8J,YAAY,CAACG,IAAI,KAAKtD,OAAO,UAAU,CAAC;MAC7D,OAAO,IAAI;IACb,CAAC,MAAM;MACL;MACA,MAAMyf,OAAO,GAAG5H,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7C2H,OAAO,CAACtJ,KAAK,CAACzX,QAAQ,GAAG,UAAU;MACnC+gB,OAAO,CAACtJ,KAAK,CAACvX,IAAI,GAAG,GAAG0gB,OAAO,IAAI;MACnCG,OAAO,CAACtJ,KAAK,CAAC5V,GAAG,GAAG,GAAGgf,OAAO,IAAI;MAClCE,OAAO,CAACtJ,KAAK,CAACtX,SAAS,GAAG,wBAAwB;MAClD4gB,OAAO,CAACtJ,KAAK,CAACrX,MAAM,GAAG,MAAM;MAC7B2gB,OAAO,CAACtJ,KAAK,CAAChX,eAAe,GAAG,qBAAqB;MACrDsgB,OAAO,CAACtJ,KAAK,CAACxV,KAAK,GAAG,OAAO;MAC7B8e,OAAO,CAACtJ,KAAK,CAAC9W,YAAY,GAAG,KAAK;MAClCogB,OAAO,CAACtJ,KAAK,CAAC3W,SAAS,GAAG,8BAA8B;MACxDigB,OAAO,CAACtJ,KAAK,CAACjX,OAAO,GAAG,KAAK;MAC7BugB,OAAO,CAACtJ,KAAK,CAACa,QAAQ,GAAG,OAAO;MAChCyI,OAAO,CAACtJ,KAAK,CAAC5W,QAAQ,GAAG,MAAM;MAE/BkgB,OAAO,CAACC,SAAS,GAAG;AAC1B;AACA,YAAYvc,YAAY,CAACG,IAAI,SAAStD,OAAO;AAC7C;AACA;AACA,qBAAqBA,OAAO;AAC5B,eAAe6e,SAAS,GAAG,UAAU,GAAG,YAAY;AACpD;AACA;AACA,OAAO;MAEDhH,QAAQ,CAAC8H,IAAI,CAACnQ,WAAW,CAACiQ,OAAO,CAAC;;MAElC;MACA,MAAMG,WAAW,GAAGH,OAAO,CAAC7F,aAAa,CAAC,QAAQ,CAAC;MACnD,IAAIgG,WAAW,EAAE;QACfA,WAAW,CAACjM,gBAAgB,CAAC,OAAO,EAAE,MAAM;UAC1CkE,QAAQ,CAAC8H,IAAI,CAAC1L,WAAW,CAACwL,OAAO,CAAC;QACpC,CAAC,CAAC;MACJ;MAEArmB,OAAO,CAACC,GAAG,CAAC,gBAAgB8J,YAAY,CAACG,IAAI,KAAKtD,OAAO,OAAO,CAAC;MACjE,OAAO,IAAI;IACb;EACF,CAAC,CAAC,OAAOxF,KAAK,EAAE;IACdpB,OAAO,CAACoB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACAhC,MAAM,CAACqnB,iBAAiB,GAAG,MAAM;EAC/BzmB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;EAErB,IAAI,CAACI,gBAAgB,IAAIA,gBAAgB,CAACwc,IAAI,KAAK,CAAC,EAAE;IACpD7c,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,OAAO,EAAE;EACX;EAEA,MAAMymB,IAAI,GAAG,EAAE;EACfrmB,gBAAgB,CAACyL,OAAO,CAAC,CAACyZ,KAAK,EAAEvZ,EAAE,KAAK;IACtChM,OAAO,CAACC,GAAG,CAAC,SAAS+L,EAAE,SAASuZ,KAAK,CAACxb,YAAY,CAACG,IAAI,EAAE,CAAC;IAC1Dwc,IAAI,CAACzU,IAAI,CAAC;MACRjG,EAAE;MACF9B,IAAI,EAAEqb,KAAK,CAACxb,YAAY,CAACG,IAAI;MAC7B5E,QAAQ,EAAEigB,KAAK,CAACjgB;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOohB,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACAtnB,MAAM,CAACyT,qBAAqB,GAAIjM,OAAO,IAAK;EAC1C,IAAI;IACF;IACAA,OAAO,GAAGuL,MAAM,CAACvL,OAAO,CAAC;IAEzB5G,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE2G,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;IAC/E5G,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,gBAAgB,CAACwc,IAAI,CAAC;IAC3D7c,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEK,kBAAkB,CAACuc,IAAI,CAAC;;IAE/D;IACA,IAAI,CAACjW,OAAO,IAAIvG,gBAAgB,CAACwc,IAAI,GAAG,CAAC,EAAE;MACzCjW,OAAO,GAAGuL,MAAM,CAACtR,KAAK,CAAC8lB,IAAI,CAACtmB,gBAAgB,CAACumB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD5mB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE2G,OAAO,CAAC;IAC3C;;IAEA;IACA5G,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1BI,gBAAgB,CAACyL,OAAO,CAAC,CAACyZ,KAAK,EAAEvZ,EAAE,KAAK;MAAA,IAAA6a,mBAAA;MACtC7mB,OAAO,CAACC,GAAG,CAAC,KAAK+L,EAAE,KAAK,OAAOA,EAAE,MAAM,EAAA6a,mBAAA,GAAAtB,KAAK,CAACxb,YAAY,cAAA8c,mBAAA,uBAAlBA,mBAAA,CAAoB3c,IAAI,KAAI,IAAI,EAAE,CAAC;IAC5E,CAAC,CAAC;;IAEF;IACA,IAAI2H,YAAY,GAAGxR,gBAAgB,CAACiC,GAAG,CAACsE,OAAO,CAAC;IAChD,IAAI,CAACiL,YAAY,EAAE;MACjB;MACA,MAAMiV,SAAS,GAAG/U,QAAQ,CAACnL,OAAO,CAAC;MACnCiL,YAAY,GAAGxR,gBAAgB,CAACiC,GAAG,CAACwkB,SAAS,CAAC;MAE9C,IAAIjV,YAAY,EAAE;QAChB7R,OAAO,CAACC,GAAG,CAAC,UAAU6mB,SAAS,SAAS,CAAC;QACzClgB,OAAO,GAAGkgB,SAAS,CAAC,CAAC;MACvB;IACF;IAEA,IAAI,CAACjV,YAAY,EAAE;MACjB7R,OAAO,CAACoB,KAAK,CAAC,cAAc,EAAEwF,OAAO,CAAC;MACtC,OAAO,KAAK;IACd;;IAEA;IACA,IAAIxH,MAAM,CAAC2H,mBAAmB,EAAE;MAC9B3H,MAAM,CAAC2H,mBAAmB,CAACkB,OAAO,GAAGrB,OAAO;IAC9C;;IAEA;IACA,IAAIxH,MAAM,CAAC4H,0BAA0B,IAAI5H,MAAM,CAAC4H,0BAA0B,CAACiB,OAAO,EAAE;MAClFyS,aAAa,CAACtb,MAAM,CAAC4H,0BAA0B,CAACiB,OAAO,CAAC;MACxD7I,MAAM,CAAC4H,0BAA0B,CAACiB,OAAO,GAAG,IAAI;IAClD;;IAEA;IACA,MAAM8e,yBAAyB,GAAGA,CAAA,KAAM;MAAA,IAAAC,qBAAA;MACtC;MACA,IAAI,CAAC5nB,MAAM,CAAC6H,uBAAuB,EAAE;MAErC,MAAMggB,SAAS,IAAAD,qBAAA,GAAG5nB,MAAM,CAAC2H,mBAAmB,cAAAigB,qBAAA,uBAA1BA,qBAAA,CAA4B/e,OAAO;MACrD,IAAI,CAACgf,SAAS,EAAE;;MAEhB;MACA,IAAIxB,SAAS,GAAGnlB,kBAAkB,CAACgC,GAAG,CAAC2kB,SAAS,CAAC;MACjD,IAAI,CAACxB,SAAS,IAAI,OAAOwB,SAAS,KAAK,QAAQ,EAAE;QAC/C;QACAxB,SAAS,GAAGnlB,kBAAkB,CAACgC,GAAG,CAACyP,QAAQ,CAACkV,SAAS,CAAC,CAAC;MACzD,CAAC,MAAM,IAAI,CAACxB,SAAS,IAAI,OAAOwB,SAAS,KAAK,QAAQ,EAAE;QACtD;QACAxB,SAAS,GAAGnlB,kBAAkB,CAACgC,GAAG,CAAC6P,MAAM,CAAC8U,SAAS,CAAC,CAAC;MACvD;MAEA,IAAI,CAACxB,SAAS,IAAI,CAACA,SAAS,CAAC3e,MAAM,IAAI2e,SAAS,CAAC3e,MAAM,CAACiG,MAAM,KAAK,CAAC,EAAE;QACpE/M,OAAO,CAACC,GAAG,CAAC,QAAQgnB,SAAS,cAAc,CAAC;QAC5C;MACF;;MAEA;MACA,MAAMC,iBAAiB,GAAG7mB,gBAAgB,CAACiC,GAAG,CAAC2kB,SAAS,CAAC,KAC/B,OAAOA,SAAS,KAAK,QAAQ,GAAG5mB,gBAAgB,CAACiC,GAAG,CAACyP,QAAQ,CAACkV,SAAS,CAAC,CAAC,GACzE5mB,gBAAgB,CAACiC,GAAG,CAAC6P,MAAM,CAAC8U,SAAS,CAAC,CAAC,CAAC;MAElE,IAAI,CAACC,iBAAiB,EAAE;QACtBlnB,OAAO,CAACoB,KAAK,CAAC,WAAW6lB,SAAS,gBAAgB,CAAC;QACnD;MACF;MAEA,MAAMld,YAAY,GAAGmd,iBAAiB,CAACnd,YAAY;;MAEnD;MACA,MAAMlD,OAAO,gBACXlJ,OAAA;QAAKof,KAAK,EAAE;UAAEjX,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAEsW,SAAS,EAAE,OAAO;UAAEgI,SAAS,EAAE;QAAO,CAAE;QAAAzN,QAAA,gBACpFta,OAAA;UAAKof,KAAK,EAAE;YACVvV,UAAU,EAAE,MAAM;YAClBme,YAAY,EAAE,KAAK;YACnBxf,QAAQ,EAAE,MAAM;YAChByf,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAA5N,QAAA,GACC,CAAAlO,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAAC+c,SAAS,EAAC,GACjD;QAAA;UAAAjK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxf,OAAA;UAAAsa,QAAA,EACGwN,SAAS,CAAC3e,MAAM,CAACwH,GAAG,CAAC,CAAC+C,KAAK,EAAEiT,KAAK,KAAK;YACtC,IAAIwB,UAAU;YACd,IAAIqB,SAAS;YAEb,QAAQ9V,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBACNiU,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;cACF,KAAK,GAAG;gBACNrB,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;cACF,KAAK,GAAG;cACR;gBACErB,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;YACJ;YAEA,MAAM3V,SAAS,GAAGG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC;YAElD,oBACE3T,OAAA;cAAiBof,KAAK,EAAE;gBACtB4I,YAAY,EAAE,KAAK;gBACnB5f,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE,KAAK;gBACnBE,QAAQ,EAAE;cACZ,CAAE;cAAA8R,QAAA,gBACAta,OAAA;gBAAKof,KAAK,EAAE;kBAAEvV,UAAU,EAAE;gBAAO,CAAE;gBAAAyQ,QAAA,GAChCzG,SAAS,EAAC,oBAAQ,EAACH,KAAK,CAACC,OAAO,EAAC,GACpC;cAAA;gBAAA0L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNxf,OAAA;gBAAKof,KAAK,EAAE;kBAAEpX,OAAO,EAAE,MAAM;kBAAEogB,cAAc,EAAE;gBAAgB,CAAE;gBAAA9N,QAAA,gBAC/Dta,OAAA;kBAAAsa,QAAA,EAAM;gBAAI;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBxf,OAAA;kBAAMof,KAAK,EAAE;oBACXxV,KAAK,EAAEue,UAAU;oBACjBte,UAAU,EAAE,MAAM;oBAClBzB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAAgS,QAAA,EACCkP;gBAAS;kBAAAnK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNxf,OAAA;gBAAKof,KAAK,EAAE;kBAAEpX,OAAO,EAAE,MAAM;kBAAEogB,cAAc,EAAE;gBAAgB,CAAE;gBAAA9N,QAAA,gBAC/Dta,OAAA;kBAAAsa,QAAA,EAAM;gBAAK;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBxf,OAAA;kBAAMof,KAAK,EAAE;oBAAEvV,UAAU,EAAE;kBAAO,CAAE;kBAAAyQ,QAAA,GAAE5G,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAAkL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAzBEmH,KAAK;cAAAtH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxf,OAAA;UAAKof,KAAK,EAAE;YAAEiJ,SAAS,EAAE,KAAK;YAAE7f,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE;UAAO,CAAE;UAAA0Q,QAAA,GAAC,4BAC3D,EAAC,IAAIzT,IAAI,CAACihB,SAAS,CAAC9S,UAAU,CAAC,CAACsT,kBAAkB,CAAC,CAAC,EAAC,6BAC7D;QAAA;UAAAjJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;;MAED;MACA/d,MAAM,CAAC6H,uBAAuB,CAACqL,IAAI,KAAK;QACtC,GAAGA,IAAI;QACPzL,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE2e,SAAS,CAAC3e;MACpB,CAAC,CAAC,CAAC;MAEH9G,OAAO,CAACC,GAAG,CAAC,SAAS,CAAA8J,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI+c,SAAS,WAAW,CAAC;IAClE,CAAC;;IAED;IACA,MAAMG,gCAAgC,GAAGA,CAAA,KAAM;MAC7C;MACA,IAAI3B,SAAS,GAAGnlB,kBAAkB,CAACgC,GAAG,CAACsE,OAAO,CAAC;MAC/C,IAAI,CAAC6e,SAAS,EAAE;QACd;QACA,MAAMqB,SAAS,GAAG/U,QAAQ,CAACnL,OAAO,CAAC;QACnC6e,SAAS,GAAGnlB,kBAAkB,CAACgC,GAAG,CAACwkB,SAAS,CAAC;QAE7C,IAAIrB,SAAS,EAAE;UACbzlB,OAAO,CAACC,GAAG,CAAC,UAAU6mB,SAAS,aAAa,CAAC;QAC/C;MACF;MAEA9mB,OAAO,CAACC,GAAG,CAAC,QAAQ2G,OAAO,SAAS,EAAE6e,SAAS,CAAC;MAEhD,MAAM1b,YAAY,GAAG8H,YAAY,CAAC9H,YAAY;MAC9C/J,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE8J,YAAY,CAAC;;MAElC;MACA,IAAIlD,OAAO;MAEX,IAAI4e,SAAS,IAAIA,SAAS,CAAC3e,MAAM,IAAI2e,SAAS,CAAC3e,MAAM,CAACiG,MAAM,GAAG,CAAC,EAAE;QAChE;QACA/M,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;QACtBwlB,SAAS,CAAC3e,MAAM,CAACgF,OAAO,CAAC,CAACuF,KAAK,EAAEiT,KAAK,KAAK;UACzCtkB,OAAO,CAACC,GAAG,CAAC,MAAMqkB,KAAK,GAAC,CAAC,QAAQjT,KAAK,CAACC,OAAO,QAAQD,KAAK,CAACQ,YAAY,UAAUR,KAAK,CAACS,UAAU,EAAE,CAAC;QACvG,CAAC,CAAC;QAEFjL,OAAO,gBACLlJ,OAAA;UAAKof,KAAK,EAAE;YAAEjX,OAAO,EAAE,KAAK;YAAEsB,KAAK,EAAE,OAAO;YAAEsW,SAAS,EAAE,OAAO;YAAEgI,SAAS,EAAE;UAAO,CAAE;UAAAzN,QAAA,gBACpFta,OAAA;YAAKof,KAAK,EAAE;cACVvV,UAAU,EAAE,MAAM;cAClBme,YAAY,EAAE,KAAK;cACnBxf,QAAQ,EAAE,MAAM;cAChByf,YAAY,EAAE,gBAAgB;cAC9BC,aAAa,EAAE;YACjB,CAAE;YAAA5N,QAAA,GACC,CAAAlO,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAACtD,OAAO,EAAC,GAC/C;UAAA;YAAAoW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxf,OAAA;YAAAsa,QAAA,EACGwN,SAAS,CAAC3e,MAAM,CAACwH,GAAG,CAAC,CAAC+C,KAAK,EAAEiT,KAAK,KAAK;cACtC,IAAIwB,UAAU;cACd,IAAIqB,SAAS;cAEb,QAAQ9V,KAAK,CAACQ,YAAY;gBACxB,KAAK,GAAG;kBACNiU,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;gBACF,KAAK,GAAG;kBACNrB,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;gBACF,KAAK,GAAG;gBACR;kBACErB,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;cACJ;cAEA,MAAM3V,SAAS,GAAGG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC;cAElD,oBACE3T,OAAA;gBAAiBof,KAAK,EAAE;kBACtB4I,YAAY,EAAE,KAAK;kBACnB5f,eAAe,EAAE,uBAAuB;kBACxCD,OAAO,EAAE,KAAK;kBACdG,YAAY,EAAE,KAAK;kBACnBE,QAAQ,EAAE;gBACZ,CAAE;gBAAA8R,QAAA,gBACAta,OAAA;kBAAKof,KAAK,EAAE;oBAAEvV,UAAU,EAAE;kBAAO,CAAE;kBAAAyQ,QAAA,GAChCzG,SAAS,EAAC,oBAAQ,EAACH,KAAK,CAACC,OAAO,EAAC,GACpC;gBAAA;kBAAA0L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNxf,OAAA;kBAAKof,KAAK,EAAE;oBAAEpX,OAAO,EAAE,MAAM;oBAAEogB,cAAc,EAAE;kBAAgB,CAAE;kBAAA9N,QAAA,gBAC/Dta,OAAA;oBAAAsa,QAAA,EAAM;kBAAI;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjBxf,OAAA;oBAAMof,KAAK,EAAE;sBACXxV,KAAK,EAAEue,UAAU;sBACjBte,UAAU,EAAE,MAAM;sBAClBzB,eAAe,EAAE,iBAAiB;sBAClCD,OAAO,EAAE,OAAO;sBAChBG,YAAY,EAAE;oBAChB,CAAE;oBAAAgS,QAAA,EACCkP;kBAAS;oBAAAnK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNxf,OAAA;kBAAKof,KAAK,EAAE;oBAAEpX,OAAO,EAAE,MAAM;oBAAEogB,cAAc,EAAE;kBAAgB,CAAE;kBAAA9N,QAAA,gBAC/Dta,OAAA;oBAAAsa,QAAA,EAAM;kBAAK;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClBxf,OAAA;oBAAMof,KAAK,EAAE;sBAAEvV,UAAU,EAAE;oBAAO,CAAE;oBAAAyQ,QAAA,GAAE5G,KAAK,CAACS,UAAU,EAAC,SAAE;kBAAA;oBAAAkL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA,GAzBEmH,KAAK;gBAAAtH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BV,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNxf,OAAA;YAAKof,KAAK,EAAE;cAAEiJ,SAAS,EAAE,KAAK;cAAE7f,QAAQ,EAAE,MAAM;cAAEoB,KAAK,EAAE;YAAO,CAAE;YAAA0Q,QAAA,GAAC,4BAC3D,EAAC,IAAIzT,IAAI,CAACihB,SAAS,CAAC9S,UAAU,CAAC,CAACsT,kBAAkB,CAAC,CAAC,EAAC,6BAC7D;UAAA;YAAAjJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MACH,CAAC,MAAM;QACL;QACAtW,OAAO,gBACLlJ,OAAA;UAAKof,KAAK,EAAE;YAAEjX,OAAO,EAAE,KAAK;YAAEsB,KAAK,EAAE;UAAQ,CAAE;UAAA6Q,QAAA,gBAC7Cta,OAAA;YAAKof,KAAK,EAAE;cACVvV,UAAU,EAAE,MAAM;cAClBme,YAAY,EAAE,KAAK;cACnBxf,QAAQ,EAAE,MAAM;cAChByf,YAAY,EAAE,gBAAgB;cAC9BC,aAAa,EAAE;YACjB,CAAE;YAAA5N,QAAA,GACC,CAAAlO,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAACtD,OAAO,EAAC,GAC/C;UAAA;YAAAoW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxf,OAAA;YAAKof,KAAK,EAAE;cAAExV,KAAK,EAAE,SAAS;cAAEpB,QAAQ,EAAE;YAAO,CAAE;YAAA8R,QAAA,EAAC;UAEpD;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxf,OAAA;YAAKof,KAAK,EAAE;cAAEiJ,SAAS,EAAE,KAAK;cAAE7f,QAAQ,EAAE,MAAM;cAAEoB,KAAK,EAAE;YAAO,CAAE;YAAA0Q,QAAA,GAAC,4BAC3D,EAAC,IAAIzT,IAAI,CAAC,CAAC,CAACyhB,kBAAkB,CAAC,CAAC;UAAA;YAAAjJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MACH;;MAEA;MACA,MAAMrb,CAAC,GAAG1C,MAAM,CAACuW,UAAU,GAAG,CAAC;MAC/B,MAAM3T,CAAC,GAAG5C,MAAM,CAACwW,WAAW,GAAG,CAAC;;MAEhC;MACA,IAAIxW,MAAM,CAAC6H,uBAAuB,EAAE;QAAA,IAAAogB,UAAA;QAClCrnB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7C0G,OAAO,EAAE,IAAI;UACbC,OAAO;UACPtB,QAAQ,EAAE;YAAExD,CAAC;YAAEE;UAAE;QACnB,CAAC,CAAC;QAEF5C,MAAM,CAAC6H,uBAAuB,CAAC;UAC7BN,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEA,OAAO;UAChBtB,QAAQ,EAAE;YAAExD,CAAC;YAAEE;UAAE,CAAC;UAClB6E,OAAO,EAAEA,OAAO;UAChBC,MAAM,EAAE,EAAAugB,UAAA,GAAA5B,SAAS,cAAA4B,UAAA,uBAATA,UAAA,CAAWvgB,MAAM,KAAI;QAC/B,CAAC,CAAC;QAEF9G,OAAO,CAACC,GAAG,CAAC,SAAS,CAAA8J,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAItD,OAAO,WAAW,CAAC;;QAE9D;QACA,IAAIxH,MAAM,CAAC4H,0BAA0B,EAAE;UACrC5H,MAAM,CAAC4H,0BAA0B,CAACiB,OAAO,GAAGmT,WAAW,CAAC2L,yBAAyB,EAAEzjB,6BAA6B,GAAG,IAAI,CAAC;UACxHtD,OAAO,CAACC,GAAG,CAAC,qBAAqBqD,6BAA6B,IAAI,CAAC;QACrE;QAEA,OAAO,IAAI;MACb,CAAC,MAAM;QACLtD,OAAO,CAACoB,KAAK,CAAC,8BAA8B,CAAC;QAC7C,OAAO,KAAK;MACd;IACF,CAAC;IAED,OAAOgmB,gCAAgC,CAAC,CAAC;EAC3C,CAAC,CAAC,OAAOhmB,KAAK,EAAE;IACdpB,OAAO,CAACoB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,MAAMqjB,yBAAyB,GAAIF,MAAM,IAAK;EAC5C,IAAItc,OAAO,GAAGsc,MAAM;;EAEpB;EACA,IAAItc,OAAO,IAAIA,OAAO,CAAC0U,QAAQ,IAAI1U,OAAO,CAAC0U,QAAQ,CAACzQ,IAAI,KAAK,cAAc,EAAE;IAC3ElM,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgI,OAAO,CAACiC,IAAI,IAAI,KAAK,CAAC;IAChD,OAAOjC,OAAO;EAChB;;EAEA;EACA,OAAOA,OAAO,IAAIA,OAAO,CAAC+Z,MAAM,EAAE;IAChC/Z,OAAO,GAAGA,OAAO,CAAC+Z,MAAM;IACxB,IAAI/Z,OAAO,CAAC0U,QAAQ,IAAI1U,OAAO,CAAC0U,QAAQ,CAACzQ,IAAI,KAAK,cAAc,EAAE;MAChElM,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgI,OAAO,CAACiC,IAAI,IAAI,KAAK,CAAC;MAChD,OAAOjC,OAAO;IAChB;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACA7I,MAAM,CAACkoB,kBAAkB,GAAG,CAACxlB,CAAC,EAAEE,CAAC,KAAK;EACpC,IAAI;IACFhC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE6B,CAAC,EAAEE,CAAC,CAAC;;IAEnC;IACA,MAAMwc,MAAM,GAAGC,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAI,CAAChC,MAAM,EAAE;MACXxe,OAAO,CAACoB,KAAK,CAAC,sBAAsB,CAAC;MACrC,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAACzC,KAAK,IAAI,CAAC2H,SAAS,CAAC2B,OAAO,EAAE;MAChCjI,OAAO,CAACoB,KAAK,CAAC,iBAAiB,CAAC;MAChC,OAAO,KAAK;IACd;;IAEA;IACA,IAAIU,CAAC,KAAKiP,SAAS,IAAI/O,CAAC,KAAK+O,SAAS,EAAE;MACtCjP,CAAC,GAAG1C,MAAM,CAACuW,UAAU,GAAG,CAAC;MACzB3T,CAAC,GAAG5C,MAAM,CAACwW,WAAW,GAAG,CAAC;IAC5B;;IAEA;IACA,MAAMwN,IAAI,GAAG5E,MAAM,CAAC6E,qBAAqB,CAAC,CAAC;IAC3C,MAAMC,MAAM,GAAI,CAACxhB,CAAC,GAAGshB,IAAI,CAAC5d,IAAI,IAAIgZ,MAAM,CAAC+E,WAAW,GAAI,CAAC,GAAG,CAAC;IAC7D,MAAMC,MAAM,GAAG,EAAE,CAACxhB,CAAC,GAAGohB,IAAI,CAACjc,GAAG,IAAIqX,MAAM,CAACiF,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;IAE9DzjB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEqjB,MAAM,EAAEE,MAAM,CAAC;;IAErC;IACA,MAAME,SAAS,GAAG,IAAIzmB,KAAK,CAAC0mB,SAAS,CAAC,CAAC;IACvCD,SAAS,CAACtF,MAAM,CAACwF,MAAM,CAACC,SAAS,GAAG,CAAC;IACrCH,SAAS,CAACtF,MAAM,CAAC0F,IAAI,CAACD,SAAS,GAAG,CAAC;IAEnC,MAAME,WAAW,GAAG,IAAI9mB,KAAK,CAAC+mB,OAAO,CAACV,MAAM,EAAEE,MAAM,CAAC;IACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,EAAEzd,SAAS,CAAC2B,OAAO,CAAC;;IAEvD;IACA,MAAMic,mBAAmB,GAAG,EAAE;IAC9B7jB,gBAAgB,CAACyL,OAAO,CAAC,CAACiP,QAAQ,EAAEnU,OAAO,KAAK;MAC9C,IAAImU,QAAQ,CAACpO,KAAK,EAAE;QAClBuX,mBAAmB,CAACjS,IAAI,CAAC8I,QAAQ,CAACpO,KAAK,CAAC;QACxC3M,OAAO,CAACC,GAAG,CAAC,SAAS2G,OAAO,QAAQ,CAAC;MACvC;IACF,CAAC,CAAC;;IAEF;IACA5G,OAAO,CAACC,GAAG,CAAC,QAAQikB,mBAAmB,CAACnX,MAAM,YAAY,CAAC;IAC3D,MAAMwa,YAAY,GAAG7D,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,EAAE,IAAI,CAAC;IAE1E,IAAIqD,YAAY,CAACxa,MAAM,GAAG,CAAC,EAAE;MAC3B/M,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1BsnB,YAAY,CAACzb,OAAO,CAAC,CAACuY,SAAS,EAAEpa,CAAC,KAAK;QACrCjK,OAAO,CAACC,GAAG,CAAC,MAAMgK,CAAC,GAAG,EAAEoa,SAAS,CAACE,MAAM,CAACra,IAAI,IAAI,KAAK,EAC1C,KAAK,EAAEma,SAAS,CAAC9hB,QAAQ,EACzB,WAAW,EAAE8hB,SAAS,CAACE,MAAM,CAACjf,QAAQ,CAACoF,OAAO,CAAC,CAAC,EAChD,WAAW,EAAE2Z,SAAS,CAACE,MAAM,CAAC5H,QAAQ,CAAC;;QAEnD;QACA,MAAM6H,GAAG,GAAGC,yBAAyB,CAACJ,SAAS,CAACE,MAAM,CAAC;QACvD,IAAIC,GAAG,IAAIA,GAAG,CAAC7H,QAAQ,IAAI6H,GAAG,CAAC7H,QAAQ,CAACzQ,IAAI,KAAK,cAAc,EAAE;UAC/DlM,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEukB,GAAG,CAAC7H,QAAQ,CAAC/V,OAAO,CAAC;QAC/C;MACF,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;;IAEA;IACA5G,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,MAAMunB,eAAe,GAAG9D,SAAS,CAACU,gBAAgB,CAACzlB,KAAK,CAACsZ,QAAQ,EAAE,IAAI,CAAC;IAExEjY,OAAO,CAACC,GAAG,CAAC,WAAWunB,eAAe,CAACza,MAAM,MAAM,CAAC;IACpDya,eAAe,CAAC1b,OAAO,CAAC,CAACuY,SAAS,EAAEpa,CAAC,KAAK;MACxC,MAAMua,GAAG,GAAGH,SAAS,CAACE,MAAM;MAC5BvkB,OAAO,CAACC,GAAG,CAAC,QAAQgK,CAAC,GAAG,EAAEua,GAAG,CAACta,IAAI,IAAI,KAAK,EAC/B,KAAK,EAAEsa,GAAG,CAACtY,IAAI,EACf,KAAK,EAAEsY,GAAG,CAAClf,QAAQ,CAACoF,OAAO,CAAC,CAAC,EAC7B,KAAK,EAAE2Z,SAAS,CAAC9hB,QAAQ,EACzB,WAAW,EAAEiiB,GAAG,CAAC7H,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACA3c,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,IAAIwnB,YAAY,GAAG,CAAC;IAEpBpnB,gBAAgB,CAACyL,OAAO,CAAC,CAACiP,QAAQ,EAAEnU,OAAO,KAAK;MAC9C,IAAImU,QAAQ,CAACpO,KAAK,EAAE;QAAA,IAAA+a,qBAAA;QAClB;QACA,IAAIC,SAAS,GAAG5M,QAAQ,CAACpO,KAAK,CAAChG,OAAO;QACtC,IAAIihB,cAAc,GAAG,IAAI;;QAEzB;QACA,MAAM/C,QAAQ,GAAG,IAAI5nB,KAAK,CAAC2F,OAAO,CAAC,CAAC;QACpCmY,QAAQ,CAACpO,KAAK,CAACmY,gBAAgB,CAACD,QAAQ,CAAC;;QAEzC;QACA,MAAMgD,gBAAgB,GAAGhD,QAAQ,CAACriB,UAAU,CAAC8D,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAC;;QAExE;QACA,MAAMyf,SAAS,GAAGF,QAAQ,CAACjjB,KAAK,CAAC,CAAC,CAACojB,OAAO,CAAC1e,SAAS,CAAC2B,OAAO,CAAC;QAC7D,IAAIjF,IAAI,CAACK,GAAG,CAAC0hB,SAAS,CAACjjB,CAAC,CAAC,GAAG,CAAC,IAAIkB,IAAI,CAACK,GAAG,CAAC0hB,SAAS,CAAC/iB,CAAC,CAAC,GAAG,CAAC,IAAI+iB,SAAS,CAAC7iB,CAAC,GAAG,CAAC,CAAC,IAAI6iB,SAAS,CAAC7iB,CAAC,GAAG,CAAC,EAAE;UACjG0lB,cAAc,GAAG,KAAK;QACxB;QAEA,IAAID,SAAS,EAAE;UACbF,YAAY,EAAE;QAChB;QAEAznB,OAAO,CAACC,GAAG,CAAC,OAAO2G,OAAO,GAAG,EAAE;UAC7B6a,EAAE,EAAE,EAAAiG,qBAAA,GAAA3M,QAAQ,CAAChR,YAAY,cAAA2d,qBAAA,uBAArBA,qBAAA,CAAuBxd,IAAI,KAAI,IAAI;UACvC4d,GAAG,EAAEH,SAAS;UACdI,KAAK,EAAEH,cAAc;UACrBI,IAAI,EAAEnD,QAAQ,CAACna,OAAO,CAAC,CAAC;UACxBud,IAAI,EAAE,CAAClD,SAAS,CAACjjB,CAAC,EAAEijB,SAAS,CAAC/iB,CAAC,EAAE+iB,SAAS,CAAC7iB,CAAC,CAAC;UAC7CgmB,MAAM,EAAEL;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF7nB,OAAO,CAACC,GAAG,CAAC,MAAMwnB,YAAY,IAAIpnB,gBAAgB,CAACwc,IAAI,WAAW,CAAC;;IAEnE;IACA,OAAO2K,eAAe,CAACza,MAAM,GAAG,CAAC;EACnC,CAAC,CAAC,OAAO3L,KAAK,EAAE;IACdpB,OAAO,CAACoB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,OAAO,KAAK;EACd;AACF,CAAC;;AAID;AACA,MAAMiR,wBAAwB,GAAGA,CAACR,YAAY,EAAEG,SAAS,KAAK;EAAA,IAAAmW,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC5D,IAAI,CAACxW,YAAY,IAAI,CAACA,YAAY,CAAClF,KAAK,IAAI,CAACqF,SAAS,EAAE;IACtD;EACF;;EAEA;EACA,MAAMsW,cAAc,GAAG,EAAE;EACzBzW,YAAY,CAAClF,KAAK,CAAC4C,QAAQ,CAACC,KAAK,IAAI;IACnC,IAAIA,KAAK,CAACmN,QAAQ,IAAInN,KAAK,CAACmN,QAAQ,CAAC4L,OAAO,EAAE;MAC5CD,cAAc,CAACrW,IAAI,CAACzC,KAAK,CAAC;IAC5B;EACF,CAAC,CAAC;EAEF8Y,cAAc,CAACxc,OAAO,CAACyZ,KAAK,IAAI;IAC9B1T,YAAY,CAAClF,KAAK,CAAC8B,MAAM,CAAC8W,KAAK,CAAC;EAClC,CAAC,CAAC;;EAEF;EACA,IAAIO,UAAU;EACd,QAAO9T,SAAS,CAACH,YAAY;IAC3B,KAAK,GAAG;MACNiU,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;MACNA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;IACR;MACEA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;EACJ;;EAEA;EACA,MAAMlD,aAAa,GAAG,IAAI3lB,KAAK,CAACsf,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAMsG,aAAa,GAAG,IAAI5lB,KAAK,CAAC8e,iBAAiB,CAAC;IAChDxU,KAAK,EAAEue,UAAU;IACjBlW,QAAQ,EAAEkW,UAAU;IACpB0C,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM1F,SAAS,GAAG,IAAI7lB,KAAK,CAAC+e,IAAI,CAAC4G,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAACxd,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAClC2gB,SAAS,CAACnG,QAAQ,GAAG;IACnB4L,OAAO,EAAE,IAAI;IACbrc,IAAI,EAAE,cAAc;IACpBtF,OAAO,GAAAuhB,qBAAA,GAAEtW,YAAY,CAAC9H,YAAY,cAAAoe,qBAAA,uBAAzBA,qBAAA,CAA2BvhB,OAAO;IAC3C0K,OAAO,EAAEU,SAAS,CAACV,OAAO;IAC1BE,SAAS,EAAEQ,SAAS,CAACR,SAAS;IAC9BM,UAAU,EAAEE,SAAS,CAACF;EACxB,CAAC;;EAED;EACA,MAAMyT,KAAK,GAAG,IAAItoB,KAAK,CAACwrB,UAAU,CAAC3C,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;EACrDP,KAAK,CAACjgB,QAAQ,CAACnD,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAC5BojB,KAAK,CAAC5I,QAAQ,GAAG;IAAE4L,OAAO,EAAE;EAAK,CAAC;;EAElC;EACA1W,YAAY,CAAClF,KAAK,CAACsB,GAAG,CAAC6U,SAAS,CAAC;EACjCjR,YAAY,CAAClF,KAAK,CAACsB,GAAG,CAACsX,KAAK,CAAC;EAE7BvlB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAAmoB,sBAAA,GAAAvW,YAAY,CAAC9H,YAAY,cAAAqe,sBAAA,uBAAzBA,sBAAA,CAA2Ble,IAAI,OAAAme,sBAAA,GAAIxW,YAAY,CAAC9H,YAAY,cAAAse,sBAAA,uBAAzBA,sBAAA,CAA2BzhB,OAAO,cAAaoL,SAAS,CAACH,YAAY,WAAWG,SAAS,CAACF,UAAU,GAAG,CAAC;AACjK,CAAC;AAED,eAAevO,WAAW;AAAC,IAAA0a,EAAA;AAAAyK,YAAA,CAAAzK,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}