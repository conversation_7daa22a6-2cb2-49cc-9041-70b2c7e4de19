{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\DeviceManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, Space, Card, Tag, message } from 'antd';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst DeviceManagement = ({\n  id\n}, ref) => {\n  _s();\n  const [devices, setDevices] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingDevice, setEditingDevice] = useState(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentDevice, setCurrentDevice] = useState(null);\n  const [currentDeviceType, setCurrentDeviceType] = useState((editingDevice === null || editingDevice === void 0 ? void 0 : editingDevice.type) || null);\n\n  // 获取设备列表\n  const fetchDevices = async () => {\n    try {\n      setLoading(true);\n      console.log('开始获取设备列表...');\n      const token = localStorage.getItem('token');\n      if (!token) {\n        console.log('未找到token，从本地存储获取设备列表');\n        const localDevices = JSON.parse(localStorage.getItem('localDevices') || '[]');\n        setDevices(localDevices);\n        return;\n      }\n\n      // 移除token中的引号\n      const cleanToken = token.replace(/^\"|\"$/g, '');\n      console.log('清理后的token:', cleanToken);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`, {\n        headers: {\n          'Authorization': `Bearer ${cleanToken}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data && response.data.success) {\n        setDevices(response.data.data || []);\n        // 同时更新本地存储\n        localStorage.setItem('localDevices', JSON.stringify(response.data.data || []));\n      } else {\n        var _response$data;\n        throw new Error(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || '获取设备列表失败');\n      }\n    } catch (error) {\n      var _error$response;\n      console.error('API获取设备列表失败:', error);\n\n      // 如果是认证错误，尝试从本地存储获取\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 403) {\n        const localDevices = JSON.parse(localStorage.getItem('localDevices') || '[]');\n        setDevices(localDevices);\n        message.warning('使用本地缓存的设备数据');\n      } else {\n        message.error('获取设备列表失败: ' + (error.message || '未知错误'));\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchDevices();\n  }, []);\n\n  // 处理添加设备\n  const handleAddDevice = () => {\n    setEditingDevice(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // 处理编辑设备\n  const handleEditDevice = device => {\n    setEditingDevice(device);\n    setCurrentDeviceType(device.type);\n    form.setFieldsValue({\n      name: device.name,\n      type: device.type,\n      status: device.status,\n      location: device.location,\n      ipAddress: device.ipAddress,\n      manufacturer: device.manufacturer,\n      model: device.model,\n      description: device.description,\n      rtspUrl: device.rtspUrl\n    });\n    setModalVisible(true);\n  };\n\n  // 处理查看设备详情\n  const handleViewDevice = device => {\n    setCurrentDevice(device);\n    setDetailModalVisible(true);\n  };\n\n  // 处理删除设备\n  const handleDeleteDevice = async deviceId => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      await axios.delete(`${apiUrl}/api/devices/${deviceId}`);\n\n      // 更新本地设备列表\n      const updatedDevices = devices.filter(device => device.id !== deviceId);\n      setDevices(updatedDevices);\n\n      // 更新本地存储\n      localStorage.setItem('localDevices', JSON.stringify(updatedDevices));\n      message.success('设备删除成功');\n    } catch (error) {\n      console.error('删除设备失败:', error);\n      message.error('删除设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 处理表单提交\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      if (values.type === 'camera') {\n        values.rtspUrl = values.rtspUrl || '';\n      } else {\n        values.rtspUrl = undefined;\n      }\n      if (editingDevice) {\n        // 编辑设备\n        await axios.put(`${apiUrl}/api/devices/${editingDevice.id}`, values);\n        message.success('设备更新成功');\n      } else {\n        // 添加设备\n        await axios.post(`${apiUrl}/api/devices`, values);\n        message.success('设备添加成功');\n      }\n      setModalVisible(false);\n      fetchDevices();\n    } catch (error) {\n      console.error('保存设备失败:', error);\n      message.error('保存设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 修改设备类型选择的处理函数\n  const handleDeviceTypeChange = value => {\n    setCurrentDeviceType(value);\n    form.setFieldsValue({\n      type: value\n    });\n  };\n\n  // 渲染状态标签\n  const renderStatusTag = status => {\n    const statusMap = {\n      online: {\n        color: 'green',\n        text: '在线'\n      },\n      offline: {\n        color: 'gray',\n        text: '离线'\n      },\n      warning: {\n        color: 'orange',\n        text: '警告'\n      },\n      error: {\n        color: 'red',\n        text: '错误'\n      },\n      maintenance: {\n        color: 'blue',\n        text: '维护中'\n      }\n    };\n    const statusInfo = statusMap[status] || {\n      color: 'default',\n      text: status\n    };\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: statusInfo.color,\n      children: statusInfo.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 12\n    }, this);\n  };\n\n  // 设备类型映射\n  const deviceTypeMap = {\n    camera: '摄像头',\n    mmwave_radar: '毫米波雷达',\n    lidar: '激光雷达',\n    rsu: 'RSU',\n    edge_computing: '边缘计算单元'\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '设备名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '设备类型',\n    dataIndex: 'type',\n    key: 'type',\n    render: type => deviceTypeMap[type] || type\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: renderStatusTag\n  }, {\n    title: '位置',\n    dataIndex: 'location',\n    key: 'location'\n  }, {\n    title: 'IP地址',\n    dataIndex: 'ipAddress',\n    key: 'ipAddress'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleViewDevice(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleEditDevice(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        danger: true,\n        onClick: () => handleDeleteDevice(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 暴露 fetchDevices 方法\n  useImperativeHandle(ref, () => ({\n    fetchDevices\n  }));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: id,\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u8BBE\\u5907\\u5217\\u8868\",\n      extra: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: handleAddDevice,\n        children: \"\\u6DFB\\u52A0\\u8BBE\\u5907\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 16\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        loading: loading,\n        dataSource: devices,\n        columns: columns,\n        rowKey: \"id\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingDevice ? '编辑设备' : '添加设备',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => setModalVisible(false),\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8BBE\\u5907\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入设备名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"type\",\n          label: \"\\u8BBE\\u5907\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择设备类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u7C7B\\u578B\",\n            onChange: handleDeviceTypeChange,\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"camera\",\n              children: \"\\u6444\\u50CF\\u5934\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"mmwave_radar\",\n              children: \"\\u6BEB\\u7C73\\u6CE2\\u96F7\\u8FBE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"lidar\",\n              children: \"\\u6FC0\\u5149\\u96F7\\u8FBE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"rsu\",\n              children: \"RSU\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"edge_computing\",\n              children: \"\\u8FB9\\u7F18\\u8BA1\\u7B97\\u5355\\u5143\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), currentDeviceType === 'camera' && /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"rtspUrl\",\n          label: \"RTSP\\u5730\\u5740\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165RTSP\\u5730\\u5740\\uFF0C\\u4F8B\\u5982\\uFF1Artsp://admin:password@192.168.1.100:554/stream1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u8BBE\\u5907\\u72B6\\u6001\",\n          rules: [{\n            required: true,\n            message: '请选择设备状态'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u72B6\\u6001\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"online\",\n              children: \"\\u5728\\u7EBF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"offline\",\n              children: \"\\u79BB\\u7EBF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"warning\",\n              children: \"\\u8B66\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"error\",\n              children: \"\\u9519\\u8BEF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"maintenance\",\n              children: \"\\u7EF4\\u62A4\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"location\",\n          label: \"\\u8BBE\\u5907\\u4F4D\\u7F6E\",\n          rules: [{\n            required: true,\n            message: '请输入设备位置'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u4F4D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"ipAddress\",\n          label: \"IP\\u5730\\u5740\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165IP\\u5730\\u5740\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"manufacturer\",\n          label: \"\\u5236\\u9020\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5236\\u9020\\u5546\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"model\",\n          label: \"\\u578B\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u578B\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u8BBE\\u5907\\u63CF\\u8FF0\",\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            rows: 4,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u8BBE\\u5907\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 11\n      }, this)],\n      width: 600,\n      children: currentDevice && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8BBE\\u5907ID:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 16\n          }, this), \" \", currentDevice.id]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8BBE\\u5907\\u540D\\u79F0:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 16\n          }, this), \" \", currentDevice.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8BBE\\u5907\\u7C7B\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 16\n          }, this), \" \", deviceTypeMap[currentDevice.type] || currentDevice.type]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u72B6\\u6001:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 16\n          }, this), \" \", renderStatusTag(currentDevice.status)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u4F4D\\u7F6E:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 16\n          }, this), \" \", currentDevice.location]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"IP\\u5730\\u5740:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 16\n          }, this), \" \", currentDevice.ipAddress]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u6700\\u540E\\u7EF4\\u62A4\\u65F6\\u95F4:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 16\n          }, this), \" \", currentDevice.lastMaintenance ? new Date(currentDevice.lastMaintenance).toLocaleString() : '无记录']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u5B89\\u88C5\\u65E5\\u671F:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 16\n          }, this), \" \", currentDevice.installationDate ? new Date(currentDevice.installationDate).toLocaleString() : '无记录']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u5236\\u9020\\u5546:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 16\n          }, this), \" \", currentDevice.manufacturer || '未知']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u578B\\u53F7:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 16\n          }, this), \" \", currentDevice.model || '未知']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u63CF\\u8FF0:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 16\n          }, this), \" \", currentDevice.description || '无']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this), currentDevice.type === 'camera' && /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"RTSP\\u5730\\u5740:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 18\n          }, this), \" \", currentDevice.rtspUrl || '未设置']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 229,\n    columnNumber: 5\n  }, this);\n};\n_s(DeviceManagement, \"FEvoVtvtZFhj3MG4SXI/8+O84qg=\", false, function () {\n  return [Form.useForm];\n});\n_c = DeviceManagement;\nexport default _c2 = /*#__PURE__*/forwardRef(DeviceManagement);\nvar _c, _c2;\n$RefreshReg$(_c, \"DeviceManagement\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "Space", "Card", "Tag", "message", "axios", "jsxDEV", "_jsxDEV", "Option", "DeviceManagement", "id", "ref", "_s", "devices", "setDevices", "loading", "setLoading", "modalVisible", "setModalVisible", "form", "useForm", "editingDevice", "setEditingDevice", "detailModalVisible", "setDetailModalVisible", "currentDevice", "setCurrentDevice", "currentDeviceType", "setCurrentDeviceType", "type", "fetchDevices", "console", "log", "token", "localStorage", "getItem", "localDevices", "JSON", "parse", "cleanToken", "replace", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "headers", "data", "success", "setItem", "stringify", "_response$data", "Error", "error", "_error$response", "status", "warning", "handleAddDevice", "resetFields", "handleEditDevice", "device", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "location", "ip<PERSON><PERSON><PERSON>", "manufacturer", "model", "description", "rtspUrl", "handleViewDevice", "handleDeleteDevice", "deviceId", "delete", "updatedDevices", "filter", "handleModalOk", "values", "validateFields", "undefined", "put", "post", "handleDeviceTypeChange", "value", "renderStatusTag", "statusMap", "online", "color", "text", "offline", "maintenance", "statusInfo", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "deviceTypeMap", "camera", "mmwave_radar", "lidar", "rsu", "edge_computing", "columns", "title", "dataIndex", "key", "render", "_", "record", "size", "onClick", "danger", "extra", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onOk", "onCancel", "width", "layout", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "onChange", "TextArea", "rows", "footer", "lastMaintenance", "Date", "toLocaleString", "installationDate", "_c", "_c2", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/DeviceManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\r\nimport { Table, Button, Modal, Form, Input, Select, Space, Card, Tag, message } from 'antd';\r\nimport axios from 'axios';\r\n\r\nconst { Option } = Select;\r\n\r\nconst DeviceManagement = ({ id }, ref) => {\r\n  const [devices, setDevices] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [form] = Form.useForm();\r\n  const [editingDevice, setEditingDevice] = useState(null);\r\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\r\n  const [currentDevice, setCurrentDevice] = useState(null);\r\n  const [currentDeviceType, setCurrentDeviceType] = useState(editingDevice?.type || null);\r\n\r\n  // 获取设备列表\r\n  const fetchDevices = async () => {\r\n    try {\r\n      setLoading(true);\r\n      console.log('开始获取设备列表...');\r\n      \r\n      const token = localStorage.getItem('token');\r\n      if (!token) {\r\n        console.log('未找到token，从本地存储获取设备列表');\r\n        const localDevices = JSON.parse(localStorage.getItem('localDevices') || '[]');\r\n        setDevices(localDevices);\r\n        return;\r\n      }\r\n\r\n      // 移除token中的引号\r\n      const cleanToken = token.replace(/^\"|\"$/g, '');\r\n      console.log('清理后的token:', cleanToken);\r\n\r\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n      const response = await axios.get(`${apiUrl}/api/devices`, {\r\n        headers: { \r\n          'Authorization': `Bearer ${cleanToken}`,\r\n          'Content-Type': 'application/json'\r\n        }\r\n      });\r\n      \r\n      if (response.data && response.data.success) {\r\n        setDevices(response.data.data || []);\r\n        // 同时更新本地存储\r\n        localStorage.setItem('localDevices', JSON.stringify(response.data.data || []));\r\n      } else {\r\n        throw new Error(response.data?.message || '获取设备列表失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('API获取设备列表失败:', error);\r\n      \r\n      // 如果是认证错误，尝试从本地存储获取\r\n      if (error.response?.status === 403) {\r\n        const localDevices = JSON.parse(localStorage.getItem('localDevices') || '[]');\r\n        setDevices(localDevices);\r\n        message.warning('使用本地缓存的设备数据');\r\n      } else {\r\n        message.error('获取设备列表失败: ' + (error.message || '未知错误'));\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchDevices();\r\n  }, []);\r\n\r\n  // 处理添加设备\r\n  const handleAddDevice = () => {\r\n    setEditingDevice(null);\r\n    form.resetFields();\r\n    setModalVisible(true);\r\n  };\r\n\r\n  // 处理编辑设备\r\n  const handleEditDevice = (device) => {\r\n    setEditingDevice(device);\r\n    setCurrentDeviceType(device.type);\r\n    form.setFieldsValue({\r\n      name: device.name,\r\n      type: device.type,\r\n      status: device.status,\r\n      location: device.location,\r\n      ipAddress: device.ipAddress,\r\n      manufacturer: device.manufacturer,\r\n      model: device.model,\r\n      description: device.description,\r\n      rtspUrl: device.rtspUrl\r\n    });\r\n    setModalVisible(true);\r\n  };\r\n\r\n  // 处理查看设备详情\r\n  const handleViewDevice = (device) => {\r\n    setCurrentDevice(device);\r\n    setDetailModalVisible(true);\r\n  };\r\n\r\n  // 处理删除设备\r\n  const handleDeleteDevice = async (deviceId) => {\r\n    try {\r\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n      \r\n      await axios.delete(`${apiUrl}/api/devices/${deviceId}`);\r\n      \r\n      // 更新本地设备列表\r\n      const updatedDevices = devices.filter(device => device.id !== deviceId);\r\n      setDevices(updatedDevices);\r\n      \r\n      // 更新本地存储\r\n      localStorage.setItem('localDevices', JSON.stringify(updatedDevices));\r\n      \r\n      message.success('设备删除成功');\r\n    } catch (error) {\r\n      console.error('删除设备失败:', error);\r\n      message.error('删除设备失败: ' + (error.message || '未知错误'));\r\n    }\r\n  };\r\n\r\n  // 处理表单提交\r\n  const handleModalOk = async () => {\r\n    try {\r\n      const values = await form.validateFields();\r\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n      \r\n      if (values.type === 'camera') {\r\n        values.rtspUrl = values.rtspUrl || '';\r\n      } else {\r\n        values.rtspUrl = undefined;\r\n      }\r\n      \r\n      if (editingDevice) {\r\n        // 编辑设备\r\n        await axios.put(`${apiUrl}/api/devices/${editingDevice.id}`, values);\r\n        message.success('设备更新成功');\r\n      } else {\r\n        // 添加设备\r\n        await axios.post(`${apiUrl}/api/devices`, values);\r\n        message.success('设备添加成功');\r\n      }\r\n      \r\n      setModalVisible(false);\r\n      fetchDevices();\r\n    } catch (error) {\r\n      console.error('保存设备失败:', error);\r\n      message.error('保存设备失败: ' + (error.message || '未知错误'));\r\n    }\r\n  };\r\n\r\n  // 修改设备类型选择的处理函数\r\n  const handleDeviceTypeChange = (value) => {\r\n    setCurrentDeviceType(value);\r\n    form.setFieldsValue({ type: value });\r\n  };\r\n\r\n  // 渲染状态标签\r\n  const renderStatusTag = (status) => {\r\n    const statusMap = {\r\n      online: { color: 'green', text: '在线' },\r\n      offline: { color: 'gray', text: '离线' },\r\n      warning: { color: 'orange', text: '警告' },\r\n      error: { color: 'red', text: '错误' },\r\n      maintenance: { color: 'blue', text: '维护中' }\r\n    };\r\n    \r\n    const statusInfo = statusMap[status] || { color: 'default', text: status };\r\n    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;\r\n  };\r\n\r\n  // 设备类型映射\r\n  const deviceTypeMap = {\r\n    camera: '摄像头',\r\n    mmwave_radar: '毫米波雷达',\r\n    lidar: '激光雷达',\r\n    rsu: 'RSU',\r\n    edge_computing: '边缘计算单元'\r\n  };\r\n\r\n  // 表格列定义\r\n  const columns = [\r\n    {\r\n      title: '设备名称',\r\n      dataIndex: 'name',\r\n      key: 'name',\r\n    },\r\n    {\r\n      title: '设备类型',\r\n      dataIndex: 'type',\r\n      key: 'type',\r\n      render: (type) => deviceTypeMap[type] || type\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'status',\r\n      key: 'status',\r\n      render: renderStatusTag\r\n    },\r\n    {\r\n      title: '位置',\r\n      dataIndex: 'location',\r\n      key: 'location',\r\n    },\r\n    {\r\n      title: 'IP地址',\r\n      dataIndex: 'ipAddress',\r\n      key: 'ipAddress',\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      render: (_, record) => (\r\n        <Space size=\"small\">\r\n          <Button type=\"link\" onClick={() => handleViewDevice(record)}>查看</Button>\r\n          <Button type=\"link\" onClick={() => handleEditDevice(record)}>编辑</Button>\r\n          <Button type=\"link\" danger onClick={() => handleDeleteDevice(record.id)}>删除</Button>\r\n        </Space>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 暴露 fetchDevices 方法\r\n  useImperativeHandle(ref, () => ({\r\n    fetchDevices\r\n  }));\r\n\r\n  return (\r\n    <div id={id}>\r\n      <Card \r\n        title=\"设备列表\" \r\n        extra={<Button type=\"primary\" onClick={handleAddDevice}>添加设备</Button>}\r\n      >\r\n        <Table \r\n          loading={loading}\r\n          dataSource={devices} \r\n          columns={columns} \r\n          rowKey=\"id\"\r\n        />\r\n      </Card>\r\n\r\n      {/* 添加/编辑设备表单 */}\r\n      <Modal\r\n        title={editingDevice ? '编辑设备' : '添加设备'}\r\n        open={modalVisible}\r\n        onOk={handleModalOk}\r\n        onCancel={() => setModalVisible(false)}\r\n        width={600}\r\n      >\r\n        <Form\r\n          form={form}\r\n          layout=\"vertical\"\r\n        >\r\n          <Form.Item\r\n            name=\"name\"\r\n            label=\"设备名称\"\r\n            rules={[{ required: true, message: '请输入设备名称' }]}\r\n          >\r\n            <Input placeholder=\"请输入设备名称\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"type\"\r\n            label=\"设备类型\"\r\n            rules={[{ required: true, message: '请选择设备类型' }]}\r\n          >\r\n            <Select \r\n              placeholder=\"请选择设备类型\"\r\n              onChange={handleDeviceTypeChange}\r\n            >\r\n              <Option value=\"camera\">摄像头</Option>\r\n              <Option value=\"mmwave_radar\">毫米波雷达</Option>\r\n              <Option value=\"lidar\">激光雷达</Option>\r\n              <Option value=\"rsu\">RSU</Option>\r\n              <Option value=\"edge_computing\">边缘计算单元</Option>\r\n            </Select>\r\n          </Form.Item>\r\n          \r\n          {/* 当设备类型为摄像头时显示 RTSP 地址输入 */}\r\n          {currentDeviceType === 'camera' && (\r\n            <Form.Item\r\n              name=\"rtspUrl\"\r\n              label=\"RTSP地址\"\r\n            >\r\n              <Input placeholder=\"请输入RTSP地址，例如：rtsp://admin:password@192.168.1.100:554/stream1\" />\r\n            </Form.Item>\r\n          )}\r\n          \r\n          <Form.Item\r\n            name=\"status\"\r\n            label=\"设备状态\"\r\n            rules={[{ required: true, message: '请选择设备状态' }]}\r\n          >\r\n            <Select placeholder=\"请选择设备状态\">\r\n              <Option value=\"online\">在线</Option>\r\n              <Option value=\"offline\">离线</Option>\r\n              <Option value=\"warning\">警告</Option>\r\n              <Option value=\"error\">错误</Option>\r\n              <Option value=\"maintenance\">维护中</Option>\r\n            </Select>\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"location\"\r\n            label=\"设备位置\"\r\n            rules={[{ required: true, message: '请输入设备位置' }]}\r\n          >\r\n            <Input placeholder=\"请输入设备位置\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"ipAddress\"\r\n            label=\"IP地址\"\r\n          >\r\n            <Input placeholder=\"请输入IP地址\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"manufacturer\"\r\n            label=\"制造商\"\r\n          >\r\n            <Input placeholder=\"请输入制造商\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"model\"\r\n            label=\"型号\"\r\n          >\r\n            <Input placeholder=\"请输入型号\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"description\"\r\n            label=\"设备描述\"\r\n          >\r\n            <Input.TextArea rows={4} placeholder=\"请输入设备描述\" />\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n\r\n      {/* 设备详情模态框 */}\r\n      <Modal\r\n        title=\"设备详情\"\r\n        open={detailModalVisible}\r\n        onCancel={() => setDetailModalVisible(false)}\r\n        footer={[\r\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\r\n            关闭\r\n          </Button>\r\n        ]}\r\n        width={600}\r\n      >\r\n        {currentDevice && (\r\n          <div>\r\n            <p><strong>设备ID:</strong> {currentDevice.id}</p>\r\n            <p><strong>设备名称:</strong> {currentDevice.name}</p>\r\n            <p><strong>设备类型:</strong> {deviceTypeMap[currentDevice.type] || currentDevice.type}</p>\r\n            <p><strong>状态:</strong> {renderStatusTag(currentDevice.status)}</p>\r\n            <p><strong>位置:</strong> {currentDevice.location}</p>\r\n            <p><strong>IP地址:</strong> {currentDevice.ipAddress}</p>\r\n            <p><strong>最后维护时间:</strong> {currentDevice.lastMaintenance ? new Date(currentDevice.lastMaintenance).toLocaleString() : '无记录'}</p>\r\n            <p><strong>安装日期:</strong> {currentDevice.installationDate ? new Date(currentDevice.installationDate).toLocaleString() : '无记录'}</p>\r\n            <p><strong>制造商:</strong> {currentDevice.manufacturer || '未知'}</p>\r\n            <p><strong>型号:</strong> {currentDevice.model || '未知'}</p>\r\n            <p><strong>描述:</strong> {currentDevice.description || '无'}</p>\r\n            {currentDevice.type === 'camera' && (\r\n              <p><strong>RTSP地址:</strong> {currentDevice.rtspUrl || '未设置'}</p>\r\n            )}\r\n          </div>\r\n        )}\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default forwardRef(DeviceManagement); "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACnF,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,OAAO,QAAQ,MAAM;AAC3F,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC;AAAO,CAAC,GAAGR,MAAM;AAEzB,MAAMS,gBAAgB,GAAGA,CAAC;EAAEC;AAAG,CAAC,EAAEC,GAAG,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,IAAI,CAAC,GAAGrB,IAAI,CAACsB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACgC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrC,QAAQ,CAAC,CAAA8B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEQ,IAAI,KAAI,IAAI,CAAC;;EAEvF;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFd,UAAU,CAAC,IAAI,CAAC;MAChBe,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAE1B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACVF,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACnC,MAAMI,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;QAC7ErB,UAAU,CAACsB,YAAY,CAAC;QACxB;MACF;;MAEA;MACA,MAAMG,UAAU,GAAGN,KAAK,CAACO,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;MAC9CT,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEO,UAAU,CAAC;MAErC,MAAME,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,GAAG,CAAC,GAAGL,MAAM,cAAc,EAAE;QACxDM,OAAO,EAAE;UACP,eAAe,EAAE,UAAUR,UAAU,EAAE;UACvC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAIM,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;QAC1CnC,UAAU,CAAC+B,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;QACpC;QACAd,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAEb,IAAI,CAACc,SAAS,CAACN,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC,CAAC;MAChF,CAAC,MAAM;QAAA,IAAAI,cAAA;QACL,MAAM,IAAIC,KAAK,CAAC,EAAAD,cAAA,GAAAP,QAAQ,CAACG,IAAI,cAAAI,cAAA,uBAAbA,cAAA,CAAehD,OAAO,KAAI,UAAU,CAAC;MACvD;IACF,CAAC,CAAC,OAAOkD,KAAK,EAAE;MAAA,IAAAC,eAAA;MACdxB,OAAO,CAACuB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;;MAEpC;MACA,IAAI,EAAAC,eAAA,GAAAD,KAAK,CAACT,QAAQ,cAAAU,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAClC,MAAMpB,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;QAC7ErB,UAAU,CAACsB,YAAY,CAAC;QACxBhC,OAAO,CAACqD,OAAO,CAAC,aAAa,CAAC;MAChC,CAAC,MAAM;QACLrD,OAAO,CAACkD,KAAK,CAAC,YAAY,IAAIA,KAAK,CAAClD,OAAO,IAAI,MAAM,CAAC,CAAC;MACzD;IACF,CAAC,SAAS;MACRY,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDxB,SAAS,CAAC,MAAM;IACdsC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4B,eAAe,GAAGA,CAAA,KAAM;IAC5BpC,gBAAgB,CAAC,IAAI,CAAC;IACtBH,IAAI,CAACwC,WAAW,CAAC,CAAC;IAClBzC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAM0C,gBAAgB,GAAIC,MAAM,IAAK;IACnCvC,gBAAgB,CAACuC,MAAM,CAAC;IACxBjC,oBAAoB,CAACiC,MAAM,CAAChC,IAAI,CAAC;IACjCV,IAAI,CAAC2C,cAAc,CAAC;MAClBC,IAAI,EAAEF,MAAM,CAACE,IAAI;MACjBlC,IAAI,EAAEgC,MAAM,CAAChC,IAAI;MACjB2B,MAAM,EAAEK,MAAM,CAACL,MAAM;MACrBQ,QAAQ,EAAEH,MAAM,CAACG,QAAQ;MACzBC,SAAS,EAAEJ,MAAM,CAACI,SAAS;MAC3BC,YAAY,EAAEL,MAAM,CAACK,YAAY;MACjCC,KAAK,EAAEN,MAAM,CAACM,KAAK;MACnBC,WAAW,EAAEP,MAAM,CAACO,WAAW;MAC/BC,OAAO,EAAER,MAAM,CAACQ;IAClB,CAAC,CAAC;IACFnD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMoD,gBAAgB,GAAIT,MAAM,IAAK;IACnCnC,gBAAgB,CAACmC,MAAM,CAAC;IACxBrC,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM+C,kBAAkB,GAAG,MAAOC,QAAQ,IAAK;IAC7C,IAAI;MACF,MAAM/B,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MAEvE,MAAMvC,KAAK,CAACoE,MAAM,CAAC,GAAGhC,MAAM,gBAAgB+B,QAAQ,EAAE,CAAC;;MAEvD;MACA,MAAME,cAAc,GAAG7D,OAAO,CAAC8D,MAAM,CAACd,MAAM,IAAIA,MAAM,CAACnD,EAAE,KAAK8D,QAAQ,CAAC;MACvE1D,UAAU,CAAC4D,cAAc,CAAC;;MAE1B;MACAxC,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAEb,IAAI,CAACc,SAAS,CAACuB,cAAc,CAAC,CAAC;MAEpEtE,OAAO,CAAC6C,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BlD,OAAO,CAACkD,KAAK,CAAC,UAAU,IAAIA,KAAK,CAAClD,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMwE,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM1D,IAAI,CAAC2D,cAAc,CAAC,CAAC;MAC1C,MAAMrC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MAEvE,IAAIiC,MAAM,CAAChD,IAAI,KAAK,QAAQ,EAAE;QAC5BgD,MAAM,CAACR,OAAO,GAAGQ,MAAM,CAACR,OAAO,IAAI,EAAE;MACvC,CAAC,MAAM;QACLQ,MAAM,CAACR,OAAO,GAAGU,SAAS;MAC5B;MAEA,IAAI1D,aAAa,EAAE;QACjB;QACA,MAAMhB,KAAK,CAAC2E,GAAG,CAAC,GAAGvC,MAAM,gBAAgBpB,aAAa,CAACX,EAAE,EAAE,EAAEmE,MAAM,CAAC;QACpEzE,OAAO,CAAC6C,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAM5C,KAAK,CAAC4E,IAAI,CAAC,GAAGxC,MAAM,cAAc,EAAEoC,MAAM,CAAC;QACjDzE,OAAO,CAAC6C,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEA/B,eAAe,CAAC,KAAK,CAAC;MACtBY,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BlD,OAAO,CAACkD,KAAK,CAAC,UAAU,IAAIA,KAAK,CAAClD,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAM8E,sBAAsB,GAAIC,KAAK,IAAK;IACxCvD,oBAAoB,CAACuD,KAAK,CAAC;IAC3BhE,IAAI,CAAC2C,cAAc,CAAC;MAAEjC,IAAI,EAAEsD;IAAM,CAAC,CAAC;EACtC,CAAC;;EAED;EACA,MAAMC,eAAe,GAAI5B,MAAM,IAAK;IAClC,MAAM6B,SAAS,GAAG;MAChBC,MAAM,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAK,CAAC;MACtCC,OAAO,EAAE;QAAEF,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAK,CAAC;MACtC/B,OAAO,EAAE;QAAE8B,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAK,CAAC;MACxClC,KAAK,EAAE;QAAEiC,KAAK,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAK,CAAC;MACnCE,WAAW,EAAE;QAAEH,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM;IAC5C,CAAC;IAED,MAAMG,UAAU,GAAGN,SAAS,CAAC7B,MAAM,CAAC,IAAI;MAAE+B,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAEhC;IAAO,CAAC;IAC1E,oBAAOjD,OAAA,CAACJ,GAAG;MAACoF,KAAK,EAAEI,UAAU,CAACJ,KAAM;MAAAK,QAAA,EAAED,UAAU,CAACH;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG;IACpBC,MAAM,EAAE,KAAK;IACbC,YAAY,EAAE,OAAO;IACrBC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,KAAK;IACVC,cAAc,EAAE;EAClB,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAG9E,IAAI,IAAKoE,aAAa,CAACpE,IAAI,CAAC,IAAIA;EAC3C,CAAC,EACD;IACE2E,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEvB;EACV,CAAC,EACD;IACEoB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAM,kBAChBtG,OAAA,CAACN,KAAK;MAAC6G,IAAI,EAAC,OAAO;MAAAlB,QAAA,gBACjBrF,OAAA,CAACX,MAAM;QAACiC,IAAI,EAAC,MAAM;QAACkF,OAAO,EAAEA,CAAA,KAAMzC,gBAAgB,CAACuC,MAAM,CAAE;QAAAjB,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxEzF,OAAA,CAACX,MAAM;QAACiC,IAAI,EAAC,MAAM;QAACkF,OAAO,EAAEA,CAAA,KAAMnD,gBAAgB,CAACiD,MAAM,CAAE;QAAAjB,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxEzF,OAAA,CAACX,MAAM;QAACiC,IAAI,EAAC,MAAM;QAACmF,MAAM;QAACD,OAAO,EAAEA,CAAA,KAAMxC,kBAAkB,CAACsC,MAAM,CAACnG,EAAE,CAAE;QAAAkF,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E;EAEX,CAAC,CACF;;EAED;EACAtG,mBAAmB,CAACiB,GAAG,EAAE,OAAO;IAC9BmB;EACF,CAAC,CAAC,CAAC;EAEH,oBACEvB,OAAA;IAAKG,EAAE,EAAEA,EAAG;IAAAkF,QAAA,gBACVrF,OAAA,CAACL,IAAI;MACHsG,KAAK,EAAC,0BAAM;MACZS,KAAK,eAAE1G,OAAA,CAACX,MAAM;QAACiC,IAAI,EAAC,SAAS;QAACkF,OAAO,EAAErD,eAAgB;QAAAkC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAE;MAAAJ,QAAA,eAEtErF,OAAA,CAACZ,KAAK;QACJoB,OAAO,EAAEA,OAAQ;QACjBmG,UAAU,EAAErG,OAAQ;QACpB0F,OAAO,EAAEA,OAAQ;QACjBY,MAAM,EAAC;MAAI;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPzF,OAAA,CAACV,KAAK;MACJ2G,KAAK,EAAEnF,aAAa,GAAG,MAAM,GAAG,MAAO;MACvC+F,IAAI,EAAEnG,YAAa;MACnBoG,IAAI,EAAEzC,aAAc;MACpB0C,QAAQ,EAAEA,CAAA,KAAMpG,eAAe,CAAC,KAAK,CAAE;MACvCqG,KAAK,EAAE,GAAI;MAAA3B,QAAA,eAEXrF,OAAA,CAACT,IAAI;QACHqB,IAAI,EAAEA,IAAK;QACXqG,MAAM,EAAC,UAAU;QAAA5B,QAAA,gBAEjBrF,OAAA,CAACT,IAAI,CAAC2H,IAAI;UACR1D,IAAI,EAAC,MAAM;UACX2D,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExH,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAwF,QAAA,eAEhDrF,OAAA,CAACR,KAAK;YAAC8H,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZzF,OAAA,CAACT,IAAI,CAAC2H,IAAI;UACR1D,IAAI,EAAC,MAAM;UACX2D,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExH,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAwF,QAAA,eAEhDrF,OAAA,CAACP,MAAM;YACL6H,WAAW,EAAC,4CAAS;YACrBC,QAAQ,EAAE5C,sBAAuB;YAAAU,QAAA,gBAEjCrF,OAAA,CAACC,MAAM;cAAC2E,KAAK,EAAC,QAAQ;cAAAS,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnCzF,OAAA,CAACC,MAAM;cAAC2E,KAAK,EAAC,cAAc;cAAAS,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3CzF,OAAA,CAACC,MAAM;cAAC2E,KAAK,EAAC,OAAO;cAAAS,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnCzF,OAAA,CAACC,MAAM;cAAC2E,KAAK,EAAC,KAAK;cAAAS,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChCzF,OAAA,CAACC,MAAM;cAAC2E,KAAK,EAAC,gBAAgB;cAAAS,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAGXrE,iBAAiB,KAAK,QAAQ,iBAC7BpB,OAAA,CAACT,IAAI,CAAC2H,IAAI;UACR1D,IAAI,EAAC,SAAS;UACd2D,KAAK,EAAC,kBAAQ;UAAA9B,QAAA,eAEdrF,OAAA,CAACR,KAAK;YAAC8H,WAAW,EAAC;UAA8D;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CACZ,eAEDzF,OAAA,CAACT,IAAI,CAAC2H,IAAI;UACR1D,IAAI,EAAC,QAAQ;UACb2D,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExH,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAwF,QAAA,eAEhDrF,OAAA,CAACP,MAAM;YAAC6H,WAAW,EAAC,4CAAS;YAAAjC,QAAA,gBAC3BrF,OAAA,CAACC,MAAM;cAAC2E,KAAK,EAAC,QAAQ;cAAAS,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClCzF,OAAA,CAACC,MAAM;cAAC2E,KAAK,EAAC,SAAS;cAAAS,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnCzF,OAAA,CAACC,MAAM;cAAC2E,KAAK,EAAC,SAAS;cAAAS,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnCzF,OAAA,CAACC,MAAM;cAAC2E,KAAK,EAAC,OAAO;cAAAS,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjCzF,OAAA,CAACC,MAAM;cAAC2E,KAAK,EAAC,aAAa;cAAAS,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZzF,OAAA,CAACT,IAAI,CAAC2H,IAAI;UACR1D,IAAI,EAAC,UAAU;UACf2D,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExH,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAwF,QAAA,eAEhDrF,OAAA,CAACR,KAAK;YAAC8H,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZzF,OAAA,CAACT,IAAI,CAAC2H,IAAI;UACR1D,IAAI,EAAC,WAAW;UAChB2D,KAAK,EAAC,gBAAM;UAAA9B,QAAA,eAEZrF,OAAA,CAACR,KAAK;YAAC8H,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZzF,OAAA,CAACT,IAAI,CAAC2H,IAAI;UACR1D,IAAI,EAAC,cAAc;UACnB2D,KAAK,EAAC,oBAAK;UAAA9B,QAAA,eAEXrF,OAAA,CAACR,KAAK;YAAC8H,WAAW,EAAC;UAAQ;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEZzF,OAAA,CAACT,IAAI,CAAC2H,IAAI;UACR1D,IAAI,EAAC,OAAO;UACZ2D,KAAK,EAAC,cAAI;UAAA9B,QAAA,eAEVrF,OAAA,CAACR,KAAK;YAAC8H,WAAW,EAAC;UAAO;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEZzF,OAAA,CAACT,IAAI,CAAC2H,IAAI;UACR1D,IAAI,EAAC,aAAa;UAClB2D,KAAK,EAAC,0BAAM;UAAA9B,QAAA,eAEZrF,OAAA,CAACR,KAAK,CAACgI,QAAQ;YAACC,IAAI,EAAE,CAAE;YAACH,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRzF,OAAA,CAACV,KAAK;MACJ2G,KAAK,EAAC,0BAAM;MACZY,IAAI,EAAE7F,kBAAmB;MACzB+F,QAAQ,EAAEA,CAAA,KAAM9F,qBAAqB,CAAC,KAAK,CAAE;MAC7CyG,MAAM,EAAE,cACN1H,OAAA,CAACX,MAAM;QAAamH,OAAO,EAAEA,CAAA,KAAMvF,qBAAqB,CAAC,KAAK,CAAE;QAAAoE,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFuB,KAAK,EAAE,GAAI;MAAA3B,QAAA,EAEVnE,aAAa,iBACZlB,OAAA;QAAAqF,QAAA,gBACErF,OAAA;UAAAqF,QAAA,gBAAGrF,OAAA;YAAAqF,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACvE,aAAa,CAACf,EAAE;QAAA;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChDzF,OAAA;UAAAqF,QAAA,gBAAGrF,OAAA;YAAAqF,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACvE,aAAa,CAACsC,IAAI;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClDzF,OAAA;UAAAqF,QAAA,gBAAGrF,OAAA;YAAAqF,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACC,aAAa,CAACxE,aAAa,CAACI,IAAI,CAAC,IAAIJ,aAAa,CAACI,IAAI;QAAA;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvFzF,OAAA;UAAAqF,QAAA,gBAAGrF,OAAA;YAAAqF,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACZ,eAAe,CAAC3D,aAAa,CAAC+B,MAAM,CAAC;QAAA;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnEzF,OAAA;UAAAqF,QAAA,gBAAGrF,OAAA;YAAAqF,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACvE,aAAa,CAACuC,QAAQ;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpDzF,OAAA;UAAAqF,QAAA,gBAAGrF,OAAA;YAAAqF,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACvE,aAAa,CAACwC,SAAS;QAAA;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvDzF,OAAA;UAAAqF,QAAA,gBAAGrF,OAAA;YAAAqF,QAAA,EAAQ;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACvE,aAAa,CAACyG,eAAe,GAAG,IAAIC,IAAI,CAAC1G,aAAa,CAACyG,eAAe,CAAC,CAACE,cAAc,CAAC,CAAC,GAAG,KAAK;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClIzF,OAAA;UAAAqF,QAAA,gBAAGrF,OAAA;YAAAqF,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACvE,aAAa,CAAC4G,gBAAgB,GAAG,IAAIF,IAAI,CAAC1G,aAAa,CAAC4G,gBAAgB,CAAC,CAACD,cAAc,CAAC,CAAC,GAAG,KAAK;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClIzF,OAAA;UAAAqF,QAAA,gBAAGrF,OAAA;YAAAqF,QAAA,EAAQ;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACvE,aAAa,CAACyC,YAAY,IAAI,IAAI;QAAA;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjEzF,OAAA;UAAAqF,QAAA,gBAAGrF,OAAA;YAAAqF,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACvE,aAAa,CAAC0C,KAAK,IAAI,IAAI;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzDzF,OAAA;UAAAqF,QAAA,gBAAGrF,OAAA;YAAAqF,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACvE,aAAa,CAAC2C,WAAW,IAAI,GAAG;QAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC7DvE,aAAa,CAACI,IAAI,KAAK,QAAQ,iBAC9BtB,OAAA;UAAAqF,QAAA,gBAAGrF,OAAA;YAAAqF,QAAA,EAAQ;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACvE,aAAa,CAAC4C,OAAO,IAAI,KAAK;QAAA;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAChE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACpF,EAAA,CA/WIH,gBAAgB;EAAA,QAILX,IAAI,CAACsB,OAAO;AAAA;AAAAkH,EAAA,GAJvB7H,gBAAgB;AAiXtB,eAAA8H,GAAA,gBAAe9I,UAAU,CAACgB,gBAAgB,CAAC;AAAC,IAAA6H,EAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAF,EAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}