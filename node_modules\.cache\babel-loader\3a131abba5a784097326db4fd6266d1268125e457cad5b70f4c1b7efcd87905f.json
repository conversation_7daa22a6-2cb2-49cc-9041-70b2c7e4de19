{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.3; // 滤波系数，值越小滤波效果越强（0-1之间）\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n  bsm: 'changli/cloud/v2x/obu/bsm',\n  rsm: 'changli/cloud/v2x/rsu/rsm',\n  scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',\n  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat' // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 添加位置滤波函数\nconst filterPosition = newPos => {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n};\n\n// 添加朝向滤波函数\nconst filterRotation = newRotation => {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\nconst CampusModel = ({\n  className,\n  onCurrentRSUChange,\n  selectedRSUs\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null); // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,\n    // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null,\n    phases: []\n  });\n\n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n\n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n\n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n\n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({\n    topic: '',\n    content: ''\n  });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      // const currentPos = cameraRef.current.position.clone();\n      cameraRef.current.position.set(0, 500, 0);\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      controls.target.set(0, 0, 0);\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        var _payload$data;\n        console.log('收到RSM消息:', payload);\n\n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n\n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          console.log('忽略过期的RSM消息:', {\n            设备MAC: deviceMac,\n            消息时间戳: messageTimestamp,\n            最新时间戳: lastTimestamp\n          });\n          return;\n        }\n\n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        console.log('RSM消息时间戳更新:', {\n          设备MAC: deviceMac,\n          时间戳: messageTimestamp,\n          是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        });\n        const participants = ((_payload$data = payload.data) === null || _payload$data === void 0 ? void 0 : _payload$data.participants) || [];\n        const rsuid = payload.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id = rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = preloadedModel.clone();\n              // 根据类型调整高度\n              const height = type === '3' ? 0.5 : 1.0; // 行人模型贴近地面\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 0.5 : 1.0, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        console.log('收到BSM消息:', payload);\n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n\n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        const newRotation = Math.PI - newState.heading * Math.PI / 180;\n\n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n\n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          newVehicleModel.position.copy(newPosition);\n          newVehicleModel.rotation.y = newRotation;\n\n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse(child => {\n            if (child.isMesh && child.material) {\n              // 保存原始材质颜色\n              if (!child.userData.originalColor && child.material.color) {\n                child.userData.originalColor = child.material.color.clone();\n              }\n              // 设置为更鲜艳的颜色\n              if (isMainVehicle) {\n                child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              } else {\n                child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              }\n              // 增加材质亮度\n              child.material.emissive = new THREE.Color(0x222222);\n              child.material.needsUpdate = true;\n            }\n          });\n\n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          newVehicleModel.add(speedLabel);\n          scene.add(newVehicleModel);\n\n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1',\n            // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n          console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition);\n          const filteredRotation = filterRotation(newRotation);\n\n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n\n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n\n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? {\n              r: 0,\n              g: 191,\n              b: 255,\n              a: 0.8\n            } :\n            // 主车使用蓝色背景\n            {\n              r: 255,\n              g: 99,\n              b: 71,\n              a: 0.8\n            },\n            // 其他车辆使用红色背景\n            textColor: {\n              r: 255,\n              g: 255,\n              b: 255,\n              a: 1.0\n            },\n            // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n          console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n\n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 1秒\n\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n\n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        console.log('收到SPAT消息:', message);\n        try {\n          const payload = JSON.parse(message);\n\n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n\n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            console.log('忽略过期的SPAT消息:', {\n              设备MAC: deviceMac,\n              消息时间戳: messageTimestamp,\n              最新时间戳: lastTimestamp\n            });\n            return;\n          }\n\n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n\n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              console.log(`处理路口ID: ${interId} 的SPAT消息`);\n\n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? getDirectionFromCode(phase.trafficDirec) : getPhaseDirection(phaseId);\n\n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n\n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n\n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n\n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n\n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n\n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n\n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && (window.currentPopoverIdRef.current === modelKey || window.currentPopoverIdRef.current === String(modelKey) || window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n\n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n      }\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        console.log('收到RSI消息:', payload);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(parseFloat(rsiData.posLong), parseFloat(rsiData.posLat));\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          switch (eventType) {\n            case '401':\n              // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':\n              // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':\n              // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':\n              // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':\n              // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002':\n              // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':\n              // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        console.log('收到场景事件消息:', payload);\n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch (sceneType) {\n          case '2':\n            // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':\n            // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':\n            // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':\n            // 限速提醒\n            const speedLimit = sceneData.eventData1; // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':\n            // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':\n            // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':\n            // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':\n            // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':\n            // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':\n            // 信号灯优先\n            const priorityType = sceneData.eventData1; // 优先类型\n            const duration = sceneData.eventData2; // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: payload.type,\n        data: payload\n      });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n\n        // 检查scene是否初始化\n        if (scene) {\n          scene.add(model);\n\n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } else {\n          console.error('无法添加模型：场景未初始化');\n        }\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 100, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n\n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n\n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach(lightObj => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n\n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n\n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n\n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n\n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n\n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n\n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {\n          // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n\n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = event => {\n        if (scene && cameraRef.current) {\n          console.log('检测到点击事件', event.clientX, event.clientY);\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        } else {\n          console.warn('点击事件处理失败: scene或camera未初始化');\n        }\n      };\n\n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n\n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n\n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({\n      color: 0x333333\n    });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n\n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({\n      color: 0x666666\n    });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n\n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          //\n          transparent: false,\n          opacity: 0.1,\n          // 几乎透明\n          depthWrite: false\n        });\n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0); // 放在红绿灯位置\n\n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n\n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500); // 延迟略长于debugTrafficLights\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n\n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n\n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        const firstIntersection = intersectionsData.intersections[0];\n        console.log('自动选择第一个路口:', firstIntersection.name);\n\n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(firstIntersection.name);\n        }, 2000);\n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u533A\\u57DF\\u9009\\u62E9\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1614,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u533A\\u57DF\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      options: intersectionsData.intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      },\n      value: selectedIntersection ? selectedIntersection.name : undefined\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1615,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1631,\n      columnNumber: 7\n    }, this), trafficLightPopover.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${trafficLightPopover.position.x}px`,\n        top: `${trafficLightPopover.position.y}px`,\n        transform: 'translate(-50%, -100%)',\n        zIndex: 1003,\n        backgroundColor: 'rgba(0, 0, 0, 0.85)',\n        color: 'white',\n        borderRadius: '4px',\n        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n        padding: '0',\n        maxWidth: '240px',\n        // 缩小最大宽度\n        fontSize: '12px' // 缩小字体\n      },\n      children: [trafficLightPopover.content, /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          position: 'absolute',\n          top: '0px',\n          right: '0px',\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          fontSize: '12px',\n          cursor: 'pointer',\n          padding: '2px 6px'\n        },\n        onClick: () => handleClosePopover(setTrafficLightPopover),\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1652,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1635,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1672,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1682,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1671,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"hIF9vxbJ25JDaBhcSTMJebTgTk8=\");\n_c = CampusModel;\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 24,\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    backgroundColor: parameters.backgroundColor || {\n      r: 255,\n      g: 255,\n      b: 255,\n      a: 0.8\n    },\n    textColor: parameters.textColor || {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1.0\n    },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n\n  // 设置字体\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n\n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n\n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  canvas.width = width;\n  canvas.height = height;\n\n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n\n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n\n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n\n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n\n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n\n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n\n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n\n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 5, 1);\n  sprite.material.depthTest = false; // 确保始终可见\n\n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = {\n    text: text,\n    params: params\n  };\n  return sprite;\n}\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    try {\n      const [vehicleGltf, cyclistGltf, peopleGltf, trafficLightGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`), loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)]);\n\n      // 处理机动车模型\n      preloadedVehicleModel = vehicleGltf.scene;\n      preloadedVehicleModel.traverse(child => {\n        if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n            color: 0xff0000,\n            // 0xff0000, //红色 0xffffff,白色\n            metalness: 0.2,\n            roughness: 0.1,\n            envMapIntensity: 1.0\n          });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n        }\n      });\n\n      // 处理非机动车模型\n      preloadedCyclistModel = cyclistGltf.scene;\n      // 设置非机动车模型的缩放\n      preloadedCyclistModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedCyclistModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n\n      // 处理行人模型\n      preloadedPeopleModel = peopleGltf.scene;\n      // 设置行人模型的缩放\n      preloadedPeopleModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedPeopleModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n\n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n\n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n\n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = type => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  try {\n    // 创建一个新的警告标记\n    const sprite = createTextSprite(text);\n    sprite.position.set(position.x, 10, -position.y); // 设置位置，稍微升高以便可见\n\n    // 为标记添加一个定时器，在5秒后自动移除\n    setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n        scene.remove(sprite);\n      }\n    }, 500);\n\n    // 将标记添加到场景中\n    scene.add(sprite);\n    console.log('添加场景事件标记:', {\n      位置: position,\n      文本: text,\n      颜色: color\n    });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = converterInstance => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n\n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach(lightObj => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型 - 只为hasTrafficLight为true的路口创建红绿灯\n  intersectionsData.intersections.forEach(intersection => {\n    // 检查路口是否有红绿灯属性，如果没有或为false，则跳过\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n\n    // 确认路口有经纬度和ID\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n\n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n\n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n\n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n\n        // *** 重要：设置为可被交互 ***\n        trafficLightModel.traverse(child => {\n          // 设置所有子对象可被点击\n          if (child.isMesh) {\n            // 确保材质能够被射线检测到\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n\n            // 设置较高的渲染顺序\n            child.renderOrder = 100;\n          }\n        });\n\n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n\n        // 添加一个专门用于点击的大型碰撞体\n        const colliderGeometry = new THREE.SphereGeometry(5, 12, 12);\n        const colliderMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          transparent: true,\n          opacity: 0.0,\n          // 完全透明\n          depthWrite: false\n        });\n        const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n        collider.name = `交通灯碰撞体-${intersection.name}`;\n        collider.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name,\n          isCollider: true\n        };\n        trafficLightModel.add(collider);\n\n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n\n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = converterInstance => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({\n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n\n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n\n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n\n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n\n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,\n    // 完全透明\n    depthWrite: false\n  });\n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  trafficLightModel.add(collider);\n\n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n\n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n\n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: 0xFF0000\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n\n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  trafficLightModel.add(lightMesh);\n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = phaseId => {\n  switch (phaseId) {\n    case '1':\n      return '北进口左转';\n    case '2':\n      return '北进口直行';\n    case '3':\n      return '北进口右转';\n    case '5':\n      return '东进口左转';\n    case '6':\n      return '东进口直行';\n    case '7':\n      return '东进口右转';\n    case '9':\n      return '南进口左转';\n    case '10':\n      return '南进口直行';\n    case '11':\n      return '南进口右转';\n    case '13':\n      return '西进口左转';\n    case '14':\n      return '西进口直行';\n    case '15':\n      return '西进口右转';\n    default:\n      return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = dirCode => {\n  switch (dirCode) {\n    case 'N':\n      return '北向南';\n    case 'S':\n      return '南向北';\n    case 'E':\n      return '东向西';\n    case 'W':\n      return '西向东';\n    case 'NE':\n      return '东北向西南';\n    case 'NW':\n      return '西北向东南';\n    case 'SE':\n      return '东南向西北';\n    case 'SW':\n      return '西南向东北';\n    default:\n      return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n\n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = (event.clientX - rect.left) / container.clientWidth * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n\n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 3;\n  raycaster.params.Line.threshold = 3;\n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  console.log('鼠标点击位置:', mouseX, mouseY);\n\n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n\n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'userData:', intersect.object.userData);\n    });\n\n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n\n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n\n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n\n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  console.log('射线检测到的对象数量:', intersects.length);\n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', 'userData:', obj.userData, '距离:', intersect.distance);\n    });\n\n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        console.log('检测到红绿灯点击, 路口ID:', interId);\n\n        // 显示弹窗\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n\n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n\n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.3; // 设置一个阈值，只有距离小于此值的才会被选中\n\n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n\n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n\n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      console.log(`路口 ${interId} 的距离:`, distance);\n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = {\n          interId,\n          distance\n        };\n      }\n    }\n  });\n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n\n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  console.log('未检测到任何红绿灯点击');\n\n  // 如果用户点击了其他任何地方，关闭现有的弹窗\n  if (setPopoverState) {\n    setPopoverState(prev => {\n      if (prev.visible) {\n        return {\n          ...prev,\n          visible: false\n        };\n      }\n      return prev;\n    });\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = setPopoverState => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n\n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n\n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({\n    ...prev,\n    visible: false\n  }));\n  console.log('弹窗已关闭');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = interId => {\n  try {\n    var _document$querySelect, _document$querySelect2;\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n\n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      return false;\n    }\n\n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n\n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n\n    // 创建弹出窗口内容\n    let content;\n    if (stateInfo && stateInfo.phases) {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '220px',\n          maxHeight: '300px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          },\n          children: [intersection.name, \" (ID: \", interId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2445,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                break;\n            }\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '6px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '4px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: getPhaseDirection(phase.phaseId)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2471,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2475,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 3px',\n                    borderRadius: '2px'\n                  },\n                  children: phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2476,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2474,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2487,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2488,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2486,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2464,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2454,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '6px',\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date().toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2494,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2444,\n        columnNumber: 9\n      }, this);\n    } else {\n      content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          maxWidth: '200px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '8px'\n          },\n          children: intersection.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2502,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [\"\\u8DEF\\u53E3ID: \", interId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2503,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2504,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2501,\n        columnNumber: 9\n      }, this);\n    }\n\n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2;\n    const centerY = window.innerHeight / 2;\n\n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = (_document$querySelect = document.querySelector('#root')) === null || _document$querySelect === void 0 ? void 0 : (_document$querySelect2 = _document$querySelect.__REACT_INSTANCE) === null || _document$querySelect2 === void 0 ? void 0 : _document$querySelect2.setTrafficLightPopover;\n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: {\n          x: centerX,\n          y: centerY\n        },\n        content: content,\n        phases: (stateInfo === null || stateInfo === void 0 ? void 0 : stateInfo.phases) || []\n      });\n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      document.body.appendChild(popover);\n\n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  return list;\n};\n\n// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n\n//   console.log('开始调试红绿灯模型...');\n\n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n\n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n\n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n\n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n\n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n\n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n\n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n\n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n\n//       // 添加到场景\n//       scene.add(highlightMesh);\n\n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n\n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n\n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n\n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = interId => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    console.log('当前trafficLightStates大小:', trafficLightStates.size);\n\n    // 如果没有指定ID，则使用第一个红绿灯\n    if (!interId && trafficLightsMap.size > 0) {\n      interId = String(Array.from(trafficLightsMap.keys())[0]);\n      console.log('未指定ID，使用第一个红绿灯ID:', interId);\n    }\n\n    // 输出所有可用的红绿灯ID用于调试\n    console.log('所有可用的红绿灯ID:');\n    trafficLightsMap.forEach((light, id) => {\n      var _light$intersection;\n      console.log(`- ${id} (${typeof id}): ${((_light$intersection = light.intersection) === null || _light$intersection === void 0 ? void 0 : _light$intersection.name) || '未知'}`);\n    });\n\n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n\n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n\n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n\n    // 创建更新函数\n    const updateTrafficLightPopover = () => {\n      var _window$currentPopove;\n      // 确保当前弹窗仍然可见\n      if (!window._setTrafficLightPopover) return;\n      const currentId = (_window$currentPopove = window.currentPopoverIdRef) === null || _window$currentPopove === void 0 ? void 0 : _window$currentPopove.current;\n      if (!currentId) return;\n\n      // 重新获取最新的状态信息\n      let stateInfo = trafficLightStates.get(currentId);\n      if (!stateInfo && typeof currentId === 'string') {\n        // 尝试使用数字ID\n        stateInfo = trafficLightStates.get(parseInt(currentId));\n      } else if (!stateInfo && typeof currentId === 'number') {\n        // 尝试使用字符串ID\n        stateInfo = trafficLightStates.get(String(currentId));\n      }\n      if (!stateInfo || !stateInfo.phases || stateInfo.phases.length === 0) {\n        console.log(`路口ID ${currentId} 没有状态信息，跳过更新`);\n        return;\n      }\n\n      // 获取交通灯信息\n      const intersectionLight = trafficLightsMap.get(currentId) || (typeof currentId === 'string' ? trafficLightsMap.get(parseInt(currentId)) : trafficLightsMap.get(String(currentId)));\n      if (!intersectionLight) {\n        console.error(`找不到路口ID ${currentId} 的红绿灯信息，无法更新弹窗`);\n        return;\n      }\n      const intersection = intersectionLight.intersection;\n\n      // 创建更新的弹窗内容\n      const content = /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '8px',\n          width: '220px',\n          maxHeight: '300px',\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 'bold',\n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          },\n          children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", currentId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2773,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: stateInfo.phases.map((phase, index) => {\n            let lightColor;\n            let lightText;\n            switch (phase.trafficLight) {\n              case 'G':\n                lightColor = '#00ff00';\n                lightText = '绿灯';\n                break;\n              case 'Y':\n                lightColor = '#ffff00';\n                lightText = '黄灯';\n                break;\n              case 'R':\n              default:\n                lightColor = '#ff0000';\n                lightText = '红灯';\n                break;\n            }\n            const direction = getPhaseDirection(phase.phaseId);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '6px',\n                backgroundColor: 'rgba(255,255,255,0.1)',\n                padding: '4px',\n                borderRadius: '4px',\n                fontSize: '12px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 'bold'\n                },\n                children: [direction, \" (\\u76F8\\u4F4DID: \", phase.phaseId, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2813,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u706F\\u8272: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2817,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: lightColor,\n                    fontWeight: 'bold',\n                    backgroundColor: 'rgba(0,0,0,0.3)',\n                    padding: '0 3px',\n                    borderRadius: '2px'\n                  },\n                  children: lightText\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2818,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2816,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u5012\\u8BA1\\u65F6: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2829,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [phase.remainTime, \" \\u79D2\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2830,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2828,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2806,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2782,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '6px',\n            fontSize: '10px',\n            color: '#888'\n          },\n          children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date(stateInfo.updateTime).toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2836,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2772,\n        columnNumber: 9\n      }, this);\n\n      // 更新弹窗内容\n      window._setTrafficLightPopover(prev => ({\n        ...prev,\n        content: content,\n        phases: stateInfo.phases\n      }));\n      console.log(`已更新路口 ${(intersection === null || intersection === void 0 ? void 0 : intersection.name) || currentId} 的红绿灯状态弹窗`);\n    };\n\n    // 先执行一次初始更新\n    const updateInitialTrafficLightPopover = () => {\n      // 同样，查找红绿灯状态信息时也尝试字符串和数字两种类型\n      let stateInfo = trafficLightStates.get(interId);\n      if (!stateInfo) {\n        // 尝试使用数字ID\n        const numericId = parseInt(interId);\n        stateInfo = trafficLightStates.get(numericId);\n        if (stateInfo) {\n          console.log(`使用数字ID ${numericId} 找到了红绿灯状态信息`);\n        }\n      }\n      console.log(`路口ID ${interId} 的状态信息:`, stateInfo);\n      const intersection = trafficLight.intersection;\n      console.log('路口信息:', intersection);\n\n      // 创建弹窗内容\n      let content;\n      if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n        // 添加一个检查相位数据的打印\n        console.log('相位数据详情:');\n        stateInfo.phases.forEach((phase, index) => {\n          console.log(`相位 ${index + 1}: ID=${phase.phaseId}, 状态=${phase.trafficLight}, 剩余时间=${phase.remainTime}`);\n        });\n        content = /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '8px',\n            width: '220px',\n            maxHeight: '300px',\n            overflowY: 'auto'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            },\n            children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", interId, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2883,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              let lightText;\n              switch (phase.trafficLight) {\n                case 'G':\n                  lightColor = '#00ff00';\n                  lightText = '绿灯';\n                  break;\n                case 'Y':\n                  lightColor = '#ffff00';\n                  lightText = '黄灯';\n                  break;\n                case 'R':\n                default:\n                  lightColor = '#ff0000';\n                  lightText = '红灯';\n                  break;\n              }\n              const direction = getPhaseDirection(phase.phaseId);\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '6px',\n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: 'bold'\n                  },\n                  children: [direction, \" (\\u76F8\\u4F4DID: \", phase.phaseId, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2923,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u706F\\u8272: \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2927,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: lightColor,\n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    },\n                    children: lightText\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2928,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2926,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\u5012\\u8BA1\\u65F6: \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2939,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontWeight: 'bold'\n                    },\n                    children: [phase.remainTime, \" \\u79D2\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2940,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2938,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2916,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2892,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '6px',\n              fontSize: '10px',\n              color: '#888'\n            },\n            children: [\"\\u66F4\\u65B0\\u65F6\\u95F4: \", new Date(stateInfo.updateTime).toLocaleTimeString(), \" (\\u81EA\\u52A8\\u5237\\u65B0)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2946,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2882,\n          columnNumber: 11\n        }, this);\n      } else {\n        // 如果没有状态信息，创建一个示例内容\n        content = /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '8px',\n            width: '220px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            },\n            children: [(intersection === null || intersection === void 0 ? void 0 : intersection.name) || '未知路口', \" (ID: \", interId, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2955,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#ff4d4f',\n              fontSize: '12px'\n            },\n            children: \"\\u8BE5\\u8DEF\\u53E3\\u6682\\u65E0\\u7EA2\\u7EFF\\u706F\\u72B6\\u6001\\u4FE1\\u606F\\uFF0C\\u8BF7\\u7B49\\u5F85SPAT\\u6D88\\u606F\\u66F4\\u65B0\\u3002\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2964,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '6px',\n              fontSize: '10px',\n              color: '#888'\n            },\n            children: [\"\\u5F53\\u524D\\u65F6\\u95F4: \", new Date().toLocaleTimeString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2967,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2954,\n          columnNumber: 11\n        }, this);\n      }\n\n      // 设置弹窗位置在屏幕中央\n      const x = window.innerWidth / 2;\n      const y = window.innerHeight / 2;\n\n      // 更新弹窗状态\n      if (window._setTrafficLightPopover) {\n        var _stateInfo;\n        console.log('调用_setTrafficLightPopover更新弹窗状态', {\n          visible: true,\n          interId,\n          position: {\n            x,\n            y\n          }\n        });\n        window._setTrafficLightPopover({\n          visible: true,\n          interId: interId,\n          position: {\n            x,\n            y\n          },\n          content: content,\n          phases: ((_stateInfo = stateInfo) === null || _stateInfo === void 0 ? void 0 : _stateInfo.phases) || []\n        });\n        console.log(`已显示路口 ${(intersection === null || intersection === void 0 ? void 0 : intersection.name) || interId} 的红绿灯状态弹窗`);\n\n        // 设置定时更新\n        if (window.trafficLightUpdateTimerRef) {\n          window.trafficLightUpdateTimerRef.current = setInterval(updateTrafficLightPopover, TRAFFIC_LIGHT_UPDATE_INTERVAL * 1000);\n          console.log(`已设置红绿灯状态弹窗定时更新，间隔 ${TRAFFIC_LIGHT_UPDATE_INTERVAL} 秒`);\n        }\n        return true;\n      } else {\n        console.error('无法找到setTrafficLightPopover函数');\n        return false;\n      }\n    };\n    return updateInitialTrafficLightPopover();\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n\n//   console.log('开始生成模拟SPAT数据...');\n\n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n\n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n\n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n\n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n\n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n\n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n\n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n\n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n\n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n\n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n\n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n\n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n\n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n\n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n\n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n\n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = object => {\n  let current = object;\n\n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n\n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n\n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n\n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n\n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n\n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = (x - rect.left) / canvas.clientWidth * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    console.log('归一化坐标:', mouseX, mouseY);\n\n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n\n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n\n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', '距离:', intersect.distance, 'position:', intersect.object.position.toArray(), 'userData:', intersect.object.userData);\n\n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      return true;\n    }\n\n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', '类型:', obj.type, '位置:', obj.position.toArray(), '距离:', intersect.distance, 'userData:', obj.userData);\n    });\n\n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        var _lightObj$intersectio;\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n\n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n\n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n\n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        if (isVisible) {\n          visibleCount++;\n        }\n        console.log(`红绿灯 ${interId}:`, {\n          名称: ((_lightObj$intersectio = lightObj.intersection) === null || _lightObj$intersectio === void 0 ? void 0 : _lightObj$intersectio.name) || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n\n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  var _trafficLight$interse, _trafficLight$interse2, _trafficLight$interse3;\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch (phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n\n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({\n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: (_trafficLight$interse = trafficLight.intersection) === null || _trafficLight$interse === void 0 ? void 0 : _trafficLight$interse.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n\n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = {\n    isLight: true\n  };\n\n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  console.log(`更新路口 ${((_trafficLight$interse2 = trafficLight.intersection) === null || _trafficLight$interse2 === void 0 ? void 0 : _trafficLight$interse2.name) || ((_trafficLight$interse3 = trafficLight.intersection) === null || _trafficLight$interse3 === void 0 ? void 0 : _trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "Select", "Popover", "intersectionsData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "lastPosition", "lastRotation", "ALPHA", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "vehicleModels", "Map", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "console", "log", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "TRAFFIC_LIGHT_UPDATE_INTERVAL", "CampusModel", "className", "onCurrentRSUChange", "selectedRSUs", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "currentPopoverIdRef", "trafficLightUpdateTimerRef", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "target", "currentTarget", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "updateMatrix", "updateMatrixWorld", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "相机位置", "toArray", "目标点", "handleMqttMessage", "message", "payload", "JSON", "parse", "_payload$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "get", "设备MAC", "消息时间戳", "最新时间戳", "时间戳", "是否为最新", "participants", "rsuid", "now", "Date", "for<PERSON>ach", "participant", "id", "partPtcId", "type", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "model", "newModel", "height", "rotation", "add", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "Set", "map", "p", "modelData", "has", "remove", "delete", "bsmData", "bsmid", "newState", "partLong", "partLat", "newPosition", "Vector3", "vehicleObj", "newVehicleModel", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "userData", "originalColor", "emissive", "Color", "needsUpdate", "speedLabel", "createTextSprite", "round", "r", "g", "b", "a", "textColor", "renderOrder", "is<PERSON><PERSON>", "toFixed", "filteredPosition", "dispose", "phasesInfo", "phase", "phaseId", "toString", "direction", "trafficDirec", "getDirectionFromCode", "getPhaseDirection", "lightState", "trafficLight", "remainTime", "parseInt", "phaseInfo", "push", "trafficLightKey", "String", "trafficLightModel", "updateTrafficLightVisual", "prev", "warn", "<PERSON><PERSON><PERSON>", "strId", "numId", "updateTime", "setTimeout", "showTrafficLightPopup", "postMessage", "rsiData", "rsuId", "events", "rtes", "event", "eventId", "rteId", "eventType", "description", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "事件ID", "事件类型", "事件说明", "开始时间", "结束时间", "位置", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "stringify", "onerror", "onclose", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "newMaterial", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "length", "xhr", "loaded", "total", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "clearInterval", "close", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "clear", "lightObj", "handleMainVehicleChange", "intervalId", "setInterval", "timer", "createTrafficLights", "clearTimeout", "handleClick", "clientX", "clientY", "handleMouseClick", "initScene", "createSimpleTrafficLight", "geometry", "BoxGeometry", "MeshBasicMaterial", "<PERSON><PERSON>", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "addClickHelpers", "helperGeometry", "SphereGeometry", "helperMaterial", "transparent", "opacity", "depthWrite", "helper<PERSON><PERSON>", "isClickHelper", "size", "firstIntersection", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "onChange", "options", "label", "bordered", "dropdownStyle", "maxHeight", "undefined", "ref", "max<PERSON><PERSON><PERSON>", "right", "background", "onClick", "handleClosePopover", "_c", "text", "parameters", "params", "fontFace", "borderThickness", "borderColor", "canvas", "document", "createElement", "context", "getContext", "font", "textWidth", "measureText", "textBaseline", "radius", "beginPath", "moveTo", "lineTo", "arcTo", "closePath", "strokeStyle", "lineWidth", "stroke", "fillStyle", "fill", "textAlign", "fillText", "texture", "CanvasTexture", "minFilter", "LinearFilter", "spriteMaterial", "SpriteMaterial", "sprite", "Sprite", "depthTest", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "vehicleGltf", "cyclistGltf", "peopleGltf", "trafficLightGltf", "all", "loadAsync", "materia", "err", "types", "parent", "文本", "颜色", "converterInstance", "createFallbackTrafficLights", "hasTrafficLight", "side", "DoubleSide", "colliderGeometry", "colliderMaterial", "collider", "isCollider", "lightGeometry", "lightMaterial", "<PERSON><PERSON><PERSON>", "dirCode", "container", "sceneInstance", "cameraInstance", "setPopoverState", "rect", "getBoundingClientRect", "mouseX", "clientWidth", "mouseY", "clientHeight", "raycaster", "Raycaster", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "trafficLightObjects", "trafficLightIntersects", "intersectObjects", "intersect", "index", "object", "obj", "getTrafficLightFromObject", "correctId", "intersects", "closestLight", "worldPos", "getWorldPosition", "screenPos", "project", "dx", "dy", "sqrt", "testTrafficLightClick", "_document$querySelect", "_document$querySelect2", "light", "lightModel", "stateInfo", "overflowY", "marginBottom", "borderBottom", "paddingBottom", "lightColor", "justifyContent", "marginTop", "toLocaleTimeString", "centerX", "centerY", "__REACT_INSTANCE", "popover", "innerHTML", "body", "closeButton", "listTrafficLights", "list", "from", "keys", "_light$intersection", "numericId", "updateTrafficLightPopover", "_window$currentPopove", "currentId", "intersectionLight", "lightText", "updateInitialTrafficLightPopover", "_stateInfo", "testClickDetection", "tlIntersects", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "distanceToCamera", "distanceTo", "名称", "可见性", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "isLight", "emissiveIntensity", "PointLight", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.3; // 滤波系数，值越小滤波效果越强（0-1之间）\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 添加位置滤波函数\nconst filterPosition = (newPos) => {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n};\n\n// 添加朝向滤波函数\nconst filterRotation = (newRotation) => {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);  // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n  \n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n  \n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n  \n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n  \n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      // const currentPos = cameraRef.current.position.clone();\n      cameraRef.current.position.set(0, 500, 0);\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      controls.target.set(0, 0, 0);\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        console.log('收到RSM消息:', payload);\n        \n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n        \n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n        \n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          console.log('忽略过期的RSM消息:', {\n            设备MAC: deviceMac,\n            消息时间戳: messageTimestamp,\n            最新时间戳: lastTimestamp\n          });\n      return;\n    }\n    \n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        \n        console.log('RSM消息时间戳更新:', {\n          设备MAC: deviceMac,\n          时间戳: messageTimestamp,\n          是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        });\n        \n        const participants = payload.data?.participants || [];\n        const rsuid = payload.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id =  rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n          // 解析位置和状态信息\n          const state = {\n            longitude: parseFloat(participant.partPosLong),\n            latitude: parseFloat(participant.partPosLat),\n            speed: parseFloat(participant.partSpeed),\n            heading: parseFloat(participant.partHeading)\n          };\n          \n          const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n          \n          // 根据类型选择对应的预加载模型\n          let preloadedModel;\n          switch (type) {\n            case '1': // 机动车\n              preloadedModel = preloadedVehicleModel;\n              break;\n            case '2': // 非机动车\n              preloadedModel = preloadedCyclistModel;\n              break;\n            case '3': // 行人\n              preloadedModel = preloadedPeopleModel;\n              break;\n            default:\n              return; // 跳过未知类型\n          }\n          \n          // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n            // 创建新模型实例\n            const newModel = preloadedModel.clone();\n            // 根据类型调整高度\n            const height = type === '3' ? 0.5 : 1.0; // 行人模型贴近地面\n            newModel.position.set(modelPos.x, height, -modelPos.y);\n            newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            scene.add(newModel);\n            \n            vehicleModels.set(id, {\n              model: newModel,\n              lastUpdate: now,\n              type: type\n            });\n          } else if (model) {\n            // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 0.5 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n          }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        console.log('收到BSM消息:', payload);\n        \n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n        \n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        const newRotation = Math.PI - newState.heading * Math.PI / 180;\n        \n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n        \n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        \n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          newVehicleModel.position.copy(newPosition);\n          newVehicleModel.rotation.y = newRotation;\n          \n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material) {\n              // 保存原始材质颜色\n              if (!child.userData.originalColor && child.material.color) {\n                child.userData.originalColor = child.material.color.clone();\n              }\n              // 设置为更鲜艳的颜色\n              if (isMainVehicle) {\n                child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              } else {\n                child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              }\n              // 增加材质亮度\n              child.material.emissive = new THREE.Color(0x222222);\n              child.material.needsUpdate = true;\n            }\n          });\n          \n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          newVehicleModel.add(speedLabel);\n          \n          scene.add(newVehicleModel);\n          \n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n          \n          console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition);\n          const filteredRotation = filterRotation(newRotation);\n          \n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n          \n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n          \n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n          \n          console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n        \n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 1秒\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        \n        return;\n      }\n      \n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        console.log('收到SPAT消息:', message);\n        \n        try {\n          const payload = JSON.parse(message);\n          \n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n          \n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n          \n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            console.log('忽略过期的SPAT消息:', {\n              设备MAC: deviceMac,\n              消息时间戳: messageTimestamp,\n              最新时间戳: lastTimestamp\n            });\n            return;\n          }\n          \n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n          \n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              \n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              \n              console.log(`处理路口ID: ${interId} 的SPAT消息`);\n              \n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                \n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  \n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? \n                    getDirectionFromCode(phase.trafficDirec) : \n                    getPhaseDirection(phaseId);\n                  \n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  \n                  console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n                  \n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n                  \n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n                  \n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  \n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  \n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n                    \n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n                \n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                \n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n                  \n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && \n                     (window.currentPopoverIdRef.current === modelKey || \n                      window.currentPopoverIdRef.current === String(modelKey) || \n                      window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    \n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n                    \n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n      }\n      \n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        console.log('收到RSI消息:', payload);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        \n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        \n        return;\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        console.log('收到场景事件消息:', payload);\n        \n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: payload.type,\n        data: payload\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          \n          // 检查scene是否初始化\n          if (scene) {\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          100,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n      \n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n      \n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach((lightObj) => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n      \n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n      \n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n    \n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n    \n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n    \n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n    \n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      \n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n  \n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          console.log('检测到点击事件', event.clientX, event.clientY);\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        } else {\n          console.warn('点击事件处理失败: scene或camera未初始化');\n        }\n      };\n      \n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n      \n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n      \n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n    \n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    \n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n    \n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,//\n          transparent: false,\n          opacity: 0.1,  // 几乎透明\n          depthWrite: false\n        });\n        \n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置\n        \n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n        \n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        \n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500);  // 延迟略长于debugTrafficLights\n    \n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n    \n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n  \n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        const firstIntersection = intersectionsData.intersections[0];\n        console.log('自动选择第一个路口:', firstIntersection.name);\n        \n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(firstIntersection.name);\n        }, 2000);\n        \n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n\n  return (\n    <>\n      <span style={labelStyle}>区域选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择区域位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n        value={selectedIntersection ? selectedIntersection.name : undefined}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      \n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '4px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n            padding: '0',\n            maxWidth: '240px', // 缩小最大宽度\n            fontSize: '12px' // 缩小字体\n          }}\n        >\n          {trafficLightPopover.content}\n          <button \n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '12px',\n              cursor: 'pointer',\n              padding: '2px 6px'\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n      \n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 24,\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    backgroundColor: parameters.backgroundColor || { r: 255, g: 255, b: 255, a: 0.8 },\n    textColor: parameters.textColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  \n  // 设置字体\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  \n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n  \n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  \n  canvas.width = width;\n  canvas.height = height;\n  \n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n  \n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n  \n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n  \n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n  \n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n  \n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n  \n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 5, 1);\n  sprite.material.depthTest = false; // 确保始终可见\n  \n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = { \n    text: text,\n    params: params\n  };\n  \n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    try {\n      const [vehicleGltf, cyclistGltf, peopleGltf, trafficLightGltf] = await Promise.all([\n      loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)\n    ]);\n\n    // 处理机动车模型\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n          color: 0xff0000,  // 0xff0000, //红色 0xffffff,白色\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n      }\n    });\n\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    preloadedPeopleModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n      \n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse((child) => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n      }\n    });\n\n    console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n      \n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n        \n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  \n  try {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在5秒后自动移除\n  setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n    scene.remove(sprite);\n      }\n  }, 500);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  console.log('添加场景事件标记:', {\n    位置: position,\n    文本: text,\n    颜色: color\n  });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  \n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  \n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n  \n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型 - 只为hasTrafficLight为true的路口创建红绿灯\n  intersectionsData.intersections.forEach(intersection => {\n    // 检查路口是否有红绿灯属性，如果没有或为false，则跳过\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n    \n    // 确认路口有经纬度和ID\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      \n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n        \n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n        \n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n        \n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n        \n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n        \n        // *** 重要：设置为可被交互 ***\n        trafficLightModel.traverse(child => {\n          // 设置所有子对象可被点击\n          if (child.isMesh) {\n            // 确保材质能够被射线检测到\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            \n            // 设置较高的渲染顺序\n            child.renderOrder = 100;\n          }\n        });\n        \n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        \n        // 添加一个专门用于点击的大型碰撞体\n        const colliderGeometry = new THREE.SphereGeometry(5, 12, 12);\n        const colliderMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,\n          transparent: true,\n          opacity: 0.0,  // 完全透明\n          depthWrite: false\n        });\n        \n        const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n        collider.name = `交通灯碰撞体-${intersection.name}`;\n        collider.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name,\n          isCollider: true\n        };\n        \n        trafficLightModel.add(collider);\n        \n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n        \n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        \n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n  \n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance) => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({ \n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n  \n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n  \n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n  \n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n  \n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,  // 完全透明\n    depthWrite: false\n  });\n  \n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  \n  trafficLightModel.add(collider);\n  \n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n  \n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n  \n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n  \n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  trafficLightModel.add(lightMesh);\n  \n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  switch(phaseId) {\n    case '1': return '北进口左转';\n    case '2': return '北进口直行';\n    case '3': return '北进口右转';\n    case '5': return '东进口左转';\n    case '6': return '东进口直行';\n    case '7': return '东进口右转';\n    case '9': return '南进口左转';\n    case '10': return '南进口直行';\n    case '11': return '南进口右转';\n    case '13': return '西进口左转';\n    case '14': return '西进口直行';\n    case '15': return '西进口右转';\n    default: return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = (dirCode) => {\n  switch(dirCode) {\n    case 'N': return '北向南';\n    case 'S': return '南向北';\n    case 'E': return '东向西';\n    case 'W': return '西向东';\n    case 'NE': return '东北向西南';\n    case 'NW': return '西北向东南';\n    case 'SE': return '东南向西北';\n    case 'SW': return '西南向东北';\n    default: return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  \n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n  \n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n  \n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 3;\n  raycaster.params.Line.threshold = 3;\n  \n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  \n  console.log('鼠标点击位置:', mouseX, mouseY);\n  \n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  \n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n  \n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  \n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, \n                 intersect.object.name || '无名称', \n                 '距离:', intersect.distance,\n                 'userData:', intersect.object.userData);\n    });\n    \n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    \n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n      \n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n      \n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n  \n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  \n  console.log('射线检测到的对象数量:', intersects.length);\n  \n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', \n                  'userData:', obj.userData, \n                  '距离:', intersect.distance);\n    });\n    \n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        console.log('检测到红绿灯点击, 路口ID:', interId);\n        \n        // 显示弹窗\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n  \n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n  \n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.3; // 设置一个阈值，只有距离小于此值的才会被选中\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n      \n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n      \n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      \n      console.log(`路口 ${interId} 的距离:`, distance);\n      \n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = { interId, distance };\n      }\n    }\n  });\n  \n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n    \n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  \n  console.log('未检测到任何红绿灯点击');\n  \n  // 如果用户点击了其他任何地方，关闭现有的弹窗\n  if (setPopoverState) {\n    setPopoverState(prev => {\n      if (prev.visible) {\n        return { ...prev, visible: false };\n      }\n      return prev;\n    });\n  }\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n  \n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n  \n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({ ...prev, visible: false }));\n  console.log('弹窗已关闭');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = (interId) => {\n  try {\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      \n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      \n      return false;\n    }\n    \n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n    \n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 创建弹出窗口内容\n    let content;\n    \n    if (stateInfo && stateInfo.phases) {\n      content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection.name} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              switch (phase.trafficLight) {\n                case 'G': lightColor = '#00ff00'; break;\n                case 'Y': lightColor = '#ffff00'; break;\n                case 'R': default: lightColor = '#ff0000'; break;\n              }\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {getPhaseDirection(phase.phaseId)}\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      content = (\n        <div style={{ padding: '8px', maxWidth: '200px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>\n          <div>路口ID: {interId}</div>\n          <div>当前无信号灯状态信息</div>\n        </div>\n      );\n    }\n    \n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2;\n    const centerY = window.innerHeight / 2;\n    \n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;\n    \n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: { x: centerX, y: centerY },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      \n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      \n      document.body.appendChild(popover);\n      \n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      \n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  \n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  \n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  \n  return list;\n};\n\n// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n\n//   console.log('开始调试红绿灯模型...');\n  \n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n  \n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n  \n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n      \n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n  \n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n  \n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n      \n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n      \n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n      \n//       // 添加到场景\n//       scene.add(highlightMesh);\n      \n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n      \n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n      \n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n  \n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = (interId) => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    \n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    console.log('当前trafficLightStates大小:', trafficLightStates.size);\n    \n    // 如果没有指定ID，则使用第一个红绿灯\n    if (!interId && trafficLightsMap.size > 0) {\n      interId = String(Array.from(trafficLightsMap.keys())[0]);\n      console.log('未指定ID，使用第一个红绿灯ID:', interId);\n    }\n    \n    // 输出所有可用的红绿灯ID用于调试\n    console.log('所有可用的红绿灯ID:');\n    trafficLightsMap.forEach((light, id) => {\n      console.log(`- ${id} (${typeof id}): ${light.intersection?.name || '未知'}`);\n    });\n    \n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      \n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    \n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    \n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n    \n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n    \n    // 创建更新函数\n    const updateTrafficLightPopover = () => {\n      // 确保当前弹窗仍然可见\n      if (!window._setTrafficLightPopover) return;\n      \n      const currentId = window.currentPopoverIdRef?.current;\n      if (!currentId) return;\n      \n      // 重新获取最新的状态信息\n      let stateInfo = trafficLightStates.get(currentId);\n      if (!stateInfo && typeof currentId === 'string') {\n        // 尝试使用数字ID\n        stateInfo = trafficLightStates.get(parseInt(currentId));\n      } else if (!stateInfo && typeof currentId === 'number') {\n        // 尝试使用字符串ID\n        stateInfo = trafficLightStates.get(String(currentId));\n      }\n      \n      if (!stateInfo || !stateInfo.phases || stateInfo.phases.length === 0) {\n        console.log(`路口ID ${currentId} 没有状态信息，跳过更新`);\n        return;\n      }\n      \n      // 获取交通灯信息\n      const intersectionLight = trafficLightsMap.get(currentId) || \n                               (typeof currentId === 'string' ? trafficLightsMap.get(parseInt(currentId)) : \n                                trafficLightsMap.get(String(currentId)));\n                                \n      if (!intersectionLight) {\n        console.error(`找不到路口ID ${currentId} 的红绿灯信息，无法更新弹窗`);\n        return;\n      }\n      \n      const intersection = intersectionLight.intersection;\n      \n      // 创建更新的弹窗内容\n      const content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection?.name || '未知路口'} (ID: {currentId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              let lightText;\n              \n              switch (phase.trafficLight) {\n                case 'G':\n                  lightColor = '#00ff00';\n                  lightText = '绿灯';\n                  break;\n                case 'Y':\n                  lightColor = '#ffff00';\n                  lightText = '黄灯';\n                  break;\n                case 'R':\n                default:\n                  lightColor = '#ff0000';\n                  lightText = '红灯';\n                  break;\n              }\n              \n              const direction = getPhaseDirection(phase.phaseId);\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {direction} (相位ID: {phase.phaseId})\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {lightText}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date(stateInfo.updateTime).toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n      \n      // 更新弹窗内容\n      window._setTrafficLightPopover(prev => ({\n        ...prev,\n        content: content,\n        phases: stateInfo.phases\n      }));\n      \n      console.log(`已更新路口 ${intersection?.name || currentId} 的红绿灯状态弹窗`);\n    };\n    \n    // 先执行一次初始更新\n    const updateInitialTrafficLightPopover = () => {\n      // 同样，查找红绿灯状态信息时也尝试字符串和数字两种类型\n      let stateInfo = trafficLightStates.get(interId);\n      if (!stateInfo) {\n        // 尝试使用数字ID\n        const numericId = parseInt(interId);\n        stateInfo = trafficLightStates.get(numericId);\n        \n        if (stateInfo) {\n          console.log(`使用数字ID ${numericId} 找到了红绿灯状态信息`);\n        }\n      }\n      \n      console.log(`路口ID ${interId} 的状态信息:`, stateInfo);\n      \n      const intersection = trafficLight.intersection;\n      console.log('路口信息:', intersection);\n      \n      // 创建弹窗内容\n      let content;\n      \n      if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n        // 添加一个检查相位数据的打印\n        console.log('相位数据详情:');\n        stateInfo.phases.forEach((phase, index) => {\n          console.log(`相位 ${index+1}: ID=${phase.phaseId}, 状态=${phase.trafficLight}, 剩余时间=${phase.remainTime}`);\n        });\n        \n        content = (\n          <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n            <div style={{ \n              fontWeight: 'bold', \n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            }}>\n              {intersection?.name || '未知路口'} (ID: {interId})\n            </div>\n            <div>\n              {stateInfo.phases.map((phase, index) => {\n                let lightColor;\n                let lightText;\n                \n                switch (phase.trafficLight) {\n                  case 'G':\n                    lightColor = '#00ff00';\n                    lightText = '绿灯';\n                    break;\n                  case 'Y':\n                    lightColor = '#ffff00';\n                    lightText = '黄灯';\n                    break;\n                  case 'R':\n                  default:\n                    lightColor = '#ff0000';\n                    lightText = '红灯';\n                    break;\n                }\n                \n                const direction = getPhaseDirection(phase.phaseId);\n                \n                return (\n                  <div key={index} style={{ \n                    marginBottom: '6px', \n                    backgroundColor: 'rgba(255,255,255,0.1)',\n                    padding: '4px',\n                    borderRadius: '4px',\n                    fontSize: '12px'\n                  }}>\n                    <div style={{ fontWeight: 'bold' }}>\n                      {direction} (相位ID: {phase.phaseId})\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <span>灯色: </span>\n                      <span style={{ \n                        color: lightColor, \n                        fontWeight: 'bold',\n                        backgroundColor: 'rgba(0,0,0,0.3)',\n                        padding: '0 3px',\n                        borderRadius: '2px'\n                      }}>\n                        {lightText}\n                      </span>\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <span>倒计时: </span>\n                      <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n            <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n              更新时间: {new Date(stateInfo.updateTime).toLocaleTimeString()} (自动刷新)\n            </div>\n          </div>\n        );\n      } else {\n        // 如果没有状态信息，创建一个示例内容\n        content = (\n          <div style={{ padding: '8px', width: '220px' }}>\n            <div style={{ \n              fontWeight: 'bold', \n              marginBottom: '6px',\n              fontSize: '14px',\n              borderBottom: '1px solid #eee',\n              paddingBottom: '4px'\n            }}>\n              {intersection?.name || '未知路口'} (ID: {interId})\n            </div>\n            <div style={{ color: '#ff4d4f', fontSize: '12px' }}>\n              该路口暂无红绿灯状态信息，请等待SPAT消息更新。\n            </div>\n            <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n              当前时间: {new Date().toLocaleTimeString()}\n            </div>\n          </div>\n        );\n      }\n      \n      // 设置弹窗位置在屏幕中央\n      const x = window.innerWidth / 2;\n      const y = window.innerHeight / 2;\n      \n      // 更新弹窗状态\n      if (window._setTrafficLightPopover) {\n        console.log('调用_setTrafficLightPopover更新弹窗状态', {\n          visible: true,\n          interId,\n          position: { x, y }\n        });\n        \n        window._setTrafficLightPopover({\n          visible: true,\n          interId: interId,\n          position: { x, y },\n          content: content,\n          phases: stateInfo?.phases || []\n        });\n        \n        console.log(`已显示路口 ${intersection?.name || interId} 的红绿灯状态弹窗`);\n        \n        // 设置定时更新\n        if (window.trafficLightUpdateTimerRef) {\n          window.trafficLightUpdateTimerRef.current = setInterval(updateTrafficLightPopover, TRAFFIC_LIGHT_UPDATE_INTERVAL * 1000);\n          console.log(`已设置红绿灯状态弹窗定时更新，间隔 ${TRAFFIC_LIGHT_UPDATE_INTERVAL} 秒`);\n        }\n        \n        return true;\n      } else {\n        console.error('无法找到setTrafficLightPopover函数');\n        return false;\n      }\n    };\n    \n    return updateInitialTrafficLightPopover();\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n  \n//   console.log('开始生成模拟SPAT数据...');\n  \n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n  \n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n  \n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n    \n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n      \n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n    \n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n      \n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n      \n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n    \n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n    \n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n    \n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n    \n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n  \n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n  \n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n  \n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n  \n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n  \n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n  \n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  \n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n    \n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n    \n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n    \n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n    \n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    \n    console.log('归一化坐标:', mouseX, mouseY);\n    \n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    \n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n    \n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n    \n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    \n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', \n                    '距离:', intersect.distance,\n                    'position:', intersect.object.position.toArray(),\n                    'userData:', intersect.object.userData);\n        \n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      \n      return true;\n    }\n    \n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    \n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', \n                  '类型:', obj.type,\n                  '位置:', obj.position.toArray(),\n                  '距离:', intersect.distance,\n                  'userData:', obj.userData);\n    });\n    \n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    \n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n        \n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n        \n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n        \n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        \n        if (isVisible) {\n          visibleCount++;\n        }\n        \n        console.log(`红绿灯 ${interId}:`, {\n          名称: lightObj.intersection?.name || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    \n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n    \n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  \n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch(phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n  \n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ \n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: trafficLight.intersection?.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n  \n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = { isLight: true };\n  \n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  \n  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\n\nexport default CampusModel; \n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACtC,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,0BAA0B,GAAG,IAAI,CAAC,CAAC;AACvC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,GAAG,CAAC,CAAC;;AAEnB;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACV;EACEC,GAAG,EAAE,2BAA2B;EAChCC,GAAG,EAAE,2BAA2B;EAChCX,KAAK,EAAE,6BAA6B;EACtCY,GAAG,EAAE,2BAA2B;EAAG;EACnCC,IAAI,EAAE,4BAA4B,CAAE;AACtC,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,IAAIC,gBAAgB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;;AAElC;AACA,IAAIE,gBAAgB,GAAG,IAAI;;AAE3B;AACA,IAAIC,gBAAgB,GAAG,IAAIH,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,IAAII,kBAAkB,GAAG,IAAIJ,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEpC;AACA,MAAMK,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGT,QAAQ,oBAAoB,CAAC;IAC7D,MAAMU,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,IAAIA,IAAI,CAACE,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;MACzD,MAAMG,WAAW,GAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,IAAI,CAAC;MACrE,IAAIH,WAAW,IAAIA,WAAW,CAACI,KAAK,EAAE;QACpCf,gBAAgB,GAAGW,WAAW,CAACI,KAAK;QACpCC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEjB,gBAAgB,CAAC;QAC7C,OAAOA,gBAAgB;MACzB;IACF;IACAgB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChCjB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjClB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB;AACF,CAAC;;AAED;AACA,MAAMmB,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAIC,MAAM,IAAK;EACjC,IAAI,CAACzC,YAAY,EAAE;IACjBA,YAAY,GAAGyC,MAAM,CAACC,KAAK,CAAC,CAAC;IAC7B,OAAOD,MAAM;EACf;EAEA,MAAME,SAAS,GAAGP,aAAa,CAACK,MAAM,CAACG,CAAC,EAAE5C,YAAY,CAAC4C,CAAC,EAAE1C,KAAK,CAAC;EAChE,MAAM2C,SAAS,GAAGT,aAAa,CAACK,MAAM,CAACK,CAAC,EAAE9C,YAAY,CAAC8C,CAAC,EAAE5C,KAAK,CAAC;EAChE,MAAM6C,SAAS,GAAGX,aAAa,CAACK,MAAM,CAACO,CAAC,EAAEhD,YAAY,CAACgD,CAAC,EAAE9C,KAAK,CAAC;EAEhEF,YAAY,CAACiD,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACjD,OAAO/C,YAAY,CAAC0C,KAAK,CAAC,CAAC;AAC7B,CAAC;;AAED;AACA,MAAMQ,cAAc,GAAIC,WAAW,IAAK;EACtC,IAAIlD,YAAY,KAAK,IAAI,EAAE;IACzBA,YAAY,GAAGkD,WAAW;IAC1B,OAAOA,WAAW;EACpB;;EAEA;EACA,IAAIC,IAAI,GAAGD,WAAW,GAAGlD,YAAY;EACrC,IAAImD,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EAExC,MAAMC,gBAAgB,GAAGnB,aAAa,CAACnC,YAAY,GAAGmD,IAAI,EAAEnD,YAAY,EAAEC,KAAK,CAAC;EAChFD,YAAY,GAAGsD,gBAAgB;EAC/B,OAAOA,gBAAgB;AACzB,CAAC;;AAED;AACA;AACA;AACA,MAAMC,6BAA6B,GAAG,CAAC,CAAC,CAAC;AACzC;;AAEA,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC,kBAAkB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAMC,YAAY,GAAG5F,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM6F,UAAU,GAAG7F,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM8F,SAAS,GAAG9F,MAAM,CAAC,IAAIM,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMyF,aAAa,GAAG/F,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMgG,eAAe,GAAGhG,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMiG,aAAa,GAAGjG,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMkG,iBAAiB,GAAGlG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAE;EACzC,MAAM,CAACmG,eAAe,EAAEC,kBAAkB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAACoG,YAAY,EAAEC,eAAe,CAAC,GAAGrG,QAAQ,CAAC;IAC/CsG,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3G,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAM4G,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,IAAI;IAAG;IACfC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG9H,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAAC+H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/H,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAACgI,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjI,QAAQ,CAAC;IAC7DkI,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,IAAI;IACbtB,QAAQ,EAAE;MAAEpC,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxByD,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAGvI,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMwI,0BAA0B,GAAGxI,MAAM,CAAC,IAAI,CAAC;;EAE/C;EACAmC,MAAM,CAACsG,uBAAuB,GAAGP,sBAAsB;;EAEvD;EACA/F,MAAM,CAACoG,mBAAmB,GAAGA,mBAAmB;EAChDpG,MAAM,CAACqG,0BAA0B,GAAGA,0BAA0B;;EAE9D;EACA,MAAME,uBAAuB,GAAG;IAC9B5B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACX3B,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7B2B,KAAK,EAAE,OAAO;IAAG;IACjB1B,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMiB,UAAU,GAAG;IACjB/B,QAAQ,EAAE,OAAO;IACjB6B,GAAG,EAAE,MAAM;IACX3B,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBwB,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbpB,QAAQ,EAAE,MAAM;IAChBqB,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvC/B,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM,CAAClE,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,IAAI4C,GAAG,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACqG,UAAU,EAAEC,aAAa,CAAC,GAAGlJ,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAAC6C,gBAAgB,EAAEsG,mBAAmB,CAAC,GAAGnJ,QAAQ,CAAC,IAAI4C,GAAG,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACwG,WAAW,EAAEC,cAAc,CAAC,GAAGrJ,QAAQ,CAAC;IAAEsJ,KAAK,EAAE,EAAE;IAAElB,OAAO,EAAE;EAAG,CAAC,CAAC;;EAE1E;EACA,MAAMmB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B5C,WAAW,CAAC,QAAQ,CAAC;IACrBrF,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACiI,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B9C,WAAW,CAAC,QAAQ,CAAC;IACrBrF,UAAU,GAAG,QAAQ;IAErB,IAAIuG,SAAS,CAAC6B,OAAO,IAAInI,QAAQ,EAAE;MACjC;MACA;MACAsG,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MACzC,MAAM6E,UAAU,GAAG9B,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAACtC,KAAK,CAAC,CAAC;MACrD,MAAMqF,SAAS,GAAG/B,SAAS,CAAC6B,OAAO,CAACG,EAAE,CAACtF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAIjE,KAAK,CAACwJ,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC;QAAEtF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,GAAG;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCmF,MAAM,CAAC1J,KAAK,CAAC2J,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvC,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAACwD,IAAI,CAACV,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;MAEV;MACA,IAAIhK,KAAK,CAACwJ,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEtF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BmF,MAAM,CAAC1J,KAAK,CAAC2J,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvC,SAAS,CAAC6B,OAAO,CAACG,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC;MACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC;;MAEV;MACA/I,QAAQ,CAACgJ,MAAM,CAACzF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC5B,MAAM0F,aAAa,GAAGjJ,QAAQ,CAACgJ,MAAM,CAAChG,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAIjE,KAAK,CAACwJ,KAAK,CAACU,aAAa,CAAC,CAC3BT,EAAE,CAAC;QAAEtF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BmF,MAAM,CAAC1J,KAAK,CAAC2J,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACd7I,QAAQ,CAACgJ,MAAM,CAACF,IAAI,CAACG,aAAa,CAAC;QACnC;QACA3C,SAAS,CAAC6B,OAAO,CAACe,MAAM,CAAClJ,QAAQ,CAACgJ,MAAM,CAAC;QACzChJ,QAAQ,CAACmJ,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACA/I,QAAQ,CAACiI,OAAO,GAAG,IAAI;;MAEvB;MACAjI,QAAQ,CAACoJ,WAAW,GAAG,EAAE;MACzBpJ,QAAQ,CAACqJ,WAAW,GAAG,GAAG;MAC1BrJ,QAAQ,CAACsJ,aAAa,GAAG3F,IAAI,CAACC,EAAE,GAAG,GAAG;MACtC5D,QAAQ,CAACuJ,aAAa,GAAG,CAAC;MAC1BvJ,QAAQ,CAACmJ,MAAM,CAAC,CAAC;MACjB;MACA7C,SAAS,CAAC6B,OAAO,CAACqB,YAAY,CAAC,CAAC;MAChClD,SAAS,CAAC6B,OAAO,CAACsB,iBAAiB,CAAC,IAAI,CAAC;MAEzClH,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBkH,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAG5K,iBAAiB,CAAC6K,aAAa,CAAC7H,IAAI,CAAC8H,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKJ,KAAK,CAAC;IAChF,IAAIC,YAAY,IAAIzD,SAAS,CAAC6B,OAAO,IAAInI,QAAQ,EAAE;MACjDwG,uBAAuB,CAACuD,YAAY,CAAC;;MAErC;MACA,MAAMI,WAAW,GAAG7F,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAChDC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,EAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC;MAEDzC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvB8H,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBK,GAAG,EAAE;UACHxF,SAAS,EAAEgF,YAAY,CAAChF,SAAS;UACjCC,QAAQ,EAAE+E,YAAY,CAAC/E;QACzB,CAAC;QACDwF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACApK,UAAU,GAAG,cAAc;MAC3BqF,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAkB,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAC/B,GAAG,CAAC4G,WAAW,CAACjH,CAAC,EAAE,GAAG,EAAE,CAACiH,WAAW,CAAC/G,CAAC,CAAC;;MAElE;MACApD,QAAQ,CAACgJ,MAAM,CAACzF,GAAG,CAAC4G,WAAW,CAACjH,CAAC,EAAE,CAAC,EAAE,CAACiH,WAAW,CAAC/G,CAAC,CAAC;;MAErD;MACAkD,SAAS,CAAC6B,OAAO,CAACe,MAAM,CAAClJ,QAAQ,CAACgJ,MAAM,CAAC;;MAEzC;MACAhJ,QAAQ,CAACiI,OAAO,GAAG,IAAI;MACvBjI,QAAQ,CAACmJ,MAAM,CAAC,CAAC;;MAEjB;MACA7C,SAAS,CAAC6B,OAAO,CAACqB,YAAY,CAAC,CAAC;MAChClD,SAAS,CAAC6B,OAAO,CAACsB,iBAAiB,CAAC,IAAI,CAAC;MAEzClH,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzB8H,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBO,IAAI,EAAEnE,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAACoF,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAE3K,QAAQ,CAACgJ,MAAM,CAAC0B,OAAO,CAAC,CAAC;QAC9BF,IAAI,EAAEL;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAGA,CAAC7C,KAAK,EAAE8C,OAAO,KAAK;IAC5C,IAAI;MACF,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;MAEnC;MACA,IAAI9C,KAAK,KAAKtH,WAAW,CAACO,GAAG,EAAE;QAAA,IAAAiK,aAAA;QAC7B1I,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsI,OAAO,CAAC;;QAEhC;QACA,MAAMI,SAAS,GAAGJ,OAAO,CAACK,GAAG;QAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;QAEnC;QACA,MAAMC,aAAa,GAAGhK,gBAAgB,CAACiK,GAAG,CAACL,SAAS,CAAC;;QAErD;QACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;UACrD/I,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;YACzBgJ,KAAK,EAAEN,SAAS;YAChBO,KAAK,EAAEL,gBAAgB;YACvBM,KAAK,EAAEJ;UACT,CAAC,CAAC;UACN;QACF;;QAEI;QACAhK,gBAAgB,CAACiC,GAAG,CAAC2H,SAAS,EAAEE,gBAAgB,CAAC;QAEjD7I,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;UACzBgJ,KAAK,EAAEN,SAAS;UAChBS,GAAG,EAAEP,gBAAgB;UACrBQ,KAAK,EAAE,CAACN,aAAa,IAAIF,gBAAgB,IAAIE;QAC/C,CAAC,CAAC;QAEF,MAAMO,YAAY,GAAG,EAAAZ,aAAA,GAAAH,OAAO,CAACjJ,IAAI,cAAAoJ,aAAA,uBAAZA,aAAA,CAAcY,YAAY,KAAI,EAAE;QACrD,MAAMC,KAAK,GAAGhB,OAAO,CAACjJ,IAAI,CAACiK,KAAK;;QAEhC;QACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;QAEtB;QACAF,YAAY,CAACI,OAAO,CAACC,WAAW,IAAI;UAClC;UACA,MAAMC,EAAE,GAAIL,KAAK,GAAGI,WAAW,CAACE,SAAS;UACzC,MAAMC,IAAI,GAAGH,WAAW,CAACI,WAAW;UAEpC,IAAGD,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACA;YACA,MAAME,KAAK,GAAG;cACZxH,SAAS,EAAEsF,UAAU,CAAC6B,WAAW,CAACM,WAAW,CAAC;cAC9CxH,QAAQ,EAAEqF,UAAU,CAAC6B,WAAW,CAACO,UAAU,CAAC;cAC5CxH,KAAK,EAAEoF,UAAU,CAAC6B,WAAW,CAACQ,SAAS,CAAC;cACxCxH,OAAO,EAAEmF,UAAU,CAAC6B,WAAW,CAACS,WAAW;YAC7C,CAAC;YAED,MAAMC,QAAQ,GAAGtI,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAACmC,KAAK,CAACxH,SAAS,EAAEwH,KAAK,CAACvH,QAAQ,CAAC;;YAEhF;YACA,IAAI6H,cAAc;YAClB,QAAQR,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRQ,cAAc,GAAG5M,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACR4M,cAAc,GAAG3M,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACR2M,cAAc,GAAG1M,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACA,IAAI2M,KAAK,GAAG1L,aAAa,CAACmK,GAAG,CAACY,EAAE,CAAC;YAEjC,IAAI,CAACW,KAAK,IAAID,cAAc,EAAE;cAC5B;cACA,MAAME,QAAQ,GAAGF,cAAc,CAAC7J,KAAK,CAAC,CAAC;cACvC;cACA,MAAMgK,MAAM,GAAGX,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;cACzCU,QAAQ,CAACzH,QAAQ,CAAC/B,GAAG,CAACqJ,QAAQ,CAAC1J,CAAC,EAAE8J,MAAM,EAAE,CAACJ,QAAQ,CAACxJ,CAAC,CAAC;cACtD2J,QAAQ,CAACE,QAAQ,CAAC7J,CAAC,GAAGO,IAAI,CAACC,EAAE,GAAG2I,KAAK,CAACrH,OAAO,GAAGvB,IAAI,CAACC,EAAE,GAAG,GAAG;cAC7DvD,KAAK,CAAC6M,GAAG,CAACH,QAAQ,CAAC;cAEnB3L,aAAa,CAACmC,GAAG,CAAC4I,EAAE,EAAE;gBACpBW,KAAK,EAAEC,QAAQ;gBACfI,UAAU,EAAEpB,GAAG;gBACfM,IAAI,EAAEA;cACR,CAAC,CAAC;YACJ,CAAC,MAAM,IAAIS,KAAK,EAAE;cAChB;cACAA,KAAK,CAACA,KAAK,CAACxH,QAAQ,CAAC/B,GAAG,CAACqJ,QAAQ,CAAC1J,CAAC,EAAE4J,KAAK,CAACT,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAACO,QAAQ,CAACxJ,CAAC,CAAC;cACjF0J,KAAK,CAACA,KAAK,CAACG,QAAQ,CAAC7J,CAAC,GAAGO,IAAI,CAACC,EAAE,GAAG2I,KAAK,CAACrH,OAAO,GAAGvB,IAAI,CAACC,EAAE,GAAG,GAAG;cAChEkJ,KAAK,CAACK,UAAU,GAAGpB,GAAG;cACtBe,KAAK,CAACA,KAAK,CAACtD,YAAY,CAAC,CAAC;cAC1BsD,KAAK,CAACA,KAAK,CAACrD,iBAAiB,CAAC,IAAI,CAAC;YACrC;UACA;QACF,CAAC,CAAC;;QAEF;QACA,MAAM2D,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAACzB,YAAY,CAAC0B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACpB,SAAS,CAAC,CAAC;QAE9DhL,aAAa,CAAC6K,OAAO,CAAC,CAACwB,SAAS,EAAEtB,EAAE,KAAK;UACvC,IAAIJ,GAAG,GAAG0B,SAAS,CAACN,UAAU,GAAGC,iBAAiB,IAAI,CAACC,UAAU,CAACK,GAAG,CAACvB,EAAE,CAAC,EAAE;YACzE9L,KAAK,CAACsN,MAAM,CAACF,SAAS,CAACX,KAAK,CAAC;YAC7B1L,aAAa,CAACwM,MAAM,CAACzB,EAAE,CAAC;YACxB5J,OAAO,CAACC,GAAG,CAAC,oBAAoB2J,EAAE,QAAQsB,SAAS,CAACpB,IAAI,EAAE,CAAC;UAC7D;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAItE,KAAK,KAAKtH,WAAW,CAACM,GAAG,EAAE;QAC7BwB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsI,OAAO,CAAC;QAEhC,MAAM+C,OAAO,GAAG/C,OAAO,CAACjJ,IAAI;QAC5B,MAAMiM,KAAK,GAAGD,OAAO,CAACvL,KAAK;QAC3B,MAAMyL,QAAQ,GAAG;UACfhJ,SAAS,EAAEsF,UAAU,CAACwD,OAAO,CAACG,QAAQ,CAAC;UACvChJ,QAAQ,EAAEqF,UAAU,CAACwD,OAAO,CAACI,OAAO,CAAC;UACrChJ,KAAK,EAAEoF,UAAU,CAACwD,OAAO,CAACnB,SAAS,CAAC;UACpCxH,OAAO,EAAEmF,UAAU,CAACwD,OAAO,CAAClB,WAAW;QACzC,CAAC;QAEDpK,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEuL,QAAQ,CAAC;QAClCxL,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEsL,KAAK,CAAC;;QAE3B;QACA,MAAMlB,QAAQ,GAAGtI,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAAC2D,QAAQ,CAAChJ,SAAS,EAAEgJ,QAAQ,CAAC/I,QAAQ,CAAC;QACtF,MAAMkJ,WAAW,GAAG,IAAIvP,KAAK,CAACwP,OAAO,CAACvB,QAAQ,CAAC1J,CAAC,EAAE,GAAG,EAAE,CAAC0J,QAAQ,CAACxJ,CAAC,CAAC;QACnE,MAAMK,WAAW,GAAGE,IAAI,CAACC,EAAE,GAAGmK,QAAQ,CAAC7I,OAAO,GAAGvB,IAAI,CAACC,EAAE,GAAG,GAAG;;QAE9D;QACA,IAAIwK,UAAU,GAAGhN,aAAa,CAACmK,GAAG,CAACuC,KAAK,CAAC;;QAEzC;QACA,MAAMzL,aAAa,GAAGyL,KAAK,KAAKvM,gBAAgB;QAEhD,IAAI,CAAC6M,UAAU,IAAInO,qBAAqB,EAAE;UACxC;UACA,MAAMoO,eAAe,GAAGpO,qBAAqB,CAAC+C,KAAK,CAAC,CAAC;UACrDqL,eAAe,CAAC/I,QAAQ,CAACwD,IAAI,CAACoF,WAAW,CAAC;UAC1CG,eAAe,CAACpB,QAAQ,CAAC7J,CAAC,GAAGK,WAAW;;UAExC;UACA4K,eAAe,CAACC,QAAQ,CAAEC,KAAK,IAAK;YAClC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;cAClC;cACA,IAAI,CAACF,KAAK,CAACG,QAAQ,CAACC,aAAa,IAAIJ,KAAK,CAACE,QAAQ,CAAClH,KAAK,EAAE;gBACzDgH,KAAK,CAACG,QAAQ,CAACC,aAAa,GAAGJ,KAAK,CAACE,QAAQ,CAAClH,KAAK,CAACvE,KAAK,CAAC,CAAC;cAC7D;cACA;cACA,IAAIX,aAAa,EAAE;gBACjBkM,KAAK,CAACE,QAAQ,CAAClH,KAAK,CAAChE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;cACtC,CAAC,MAAM;gBACLgL,KAAK,CAACE,QAAQ,CAAClH,KAAK,CAAChE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;cACtC;cACA;cACAgL,KAAK,CAACE,QAAQ,CAACG,QAAQ,GAAG,IAAIjQ,KAAK,CAACkQ,KAAK,CAAC,QAAQ,CAAC;cACnDN,KAAK,CAACE,QAAQ,CAACK,WAAW,GAAG,IAAI;YACnC;UACF,CAAC,CAAC;;UAEF;UACA,MAAMC,UAAU,GAAGC,gBAAgB,CAAC,GAAGrL,IAAI,CAACsL,KAAK,CAAClB,QAAQ,CAAC9I,KAAK,CAAC,OAAO,EAAE;YACxEc,eAAe,EAAE1D,aAAa,GAC5B;cAAE6M,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCC,SAAS,EAAE;cAAEJ,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/ClJ,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFiJ,UAAU,CAACzJ,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClCwL,UAAU,CAACQ,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/BlB,eAAe,CAACnB,GAAG,CAAC6B,UAAU,CAAC;UAE/B1O,KAAK,CAAC6M,GAAG,CAACmB,eAAe,CAAC;;UAE1B;UACAjN,aAAa,CAACmC,GAAG,CAACuK,KAAK,EAAE;YACvBhB,KAAK,EAAEuB,eAAe;YACtBlB,UAAU,EAAEnB,IAAI,CAACD,GAAG,CAAC,CAAC;YACtBM,IAAI,EAAE,GAAG;YAAE;YACXmD,MAAM,EAAEnN,aAAa;YACrB0M,UAAU,EAAEA,UAAU,CAAC;UACzB,CAAC,CAAC;UAEFxM,OAAO,CAACC,GAAG,CAAC,aAAasL,KAAK,SAASI,WAAW,CAAChL,CAAC,CAACuM,OAAO,CAAC,CAAC,CAAC,KAAKvB,WAAW,CAAC9K,CAAC,CAACqM,OAAO,CAAC,CAAC,CAAC,KAAKvB,WAAW,CAAC5K,CAAC,CAACmM,OAAO,CAAC,CAAC,CAAC,YAAYpN,aAAa,EAAE,CAAC;;UAErJ;UACA,IAAIA,aAAa,EAAE;YACjB7C,gBAAgB,GAAG6O,eAAe;YAClCvJ,eAAe,CAACiJ,QAAQ,CAAC;YACzBxL,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEsL,KAAK,CAAC;UACjC;QACF,CAAC,MAAM,IAAIM,UAAU,EAAE;UACrB;UACA,MAAMsB,gBAAgB,GAAG5M,cAAc,CAACoL,WAAW,CAAC;UACpD,MAAMrK,gBAAgB,GAAGL,cAAc,CAACC,WAAW,CAAC;;UAEpD;UACA2K,UAAU,CAACtB,KAAK,CAACxH,QAAQ,CAACwD,IAAI,CAAC4G,gBAAgB,CAAC;UAChDtB,UAAU,CAACtB,KAAK,CAACG,QAAQ,CAAC7J,CAAC,GAAGS,gBAAgB;UAC9CuK,UAAU,CAACtB,KAAK,CAACtD,YAAY,CAAC,CAAC;UAC/B4E,UAAU,CAACtB,KAAK,CAACrD,iBAAiB,CAAC,IAAI,CAAC;UACxC2E,UAAU,CAACjB,UAAU,GAAGnB,IAAI,CAACD,GAAG,CAAC,CAAC;UAClCqC,UAAU,CAACoB,MAAM,GAAGnN,aAAa,CAAC,CAAC;;UAEnC;UACA,IAAI+L,UAAU,CAACW,UAAU,EAAE;YACzBX,UAAU,CAACW,UAAU,CAACN,QAAQ,CAAClB,GAAG,CAACoC,OAAO,CAAC,CAAC;YAC5CvB,UAAU,CAACtB,KAAK,CAACa,MAAM,CAACS,UAAU,CAACW,UAAU,CAAC;UAChD;;UAEA;UACA,MAAMA,UAAU,GAAGC,gBAAgB,CAAC,GAAGrL,IAAI,CAACsL,KAAK,CAAClB,QAAQ,CAAC9I,KAAK,GAAG,GAAG,CAAC,OAAO,EAAE;YAC9Ec,eAAe,EAAE1D,aAAa,GAC5B;cAAE6M,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACnC;cAAEH,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAG;YACrCC,SAAS,EAAE;cAAEJ,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE,GAAG;cAAEC,CAAC,EAAE;YAAI,CAAC;YAAE;YAC/ClJ,QAAQ,EAAE,EAAE;YACZL,OAAO,EAAE;UACX,CAAC,CAAC;UACFiJ,UAAU,CAACzJ,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;UACnCwL,UAAU,CAACQ,WAAW,GAAG,IAAI,CAAC,CAAC;UAC/BnB,UAAU,CAACtB,KAAK,CAACI,GAAG,CAAC6B,UAAU,CAAC;UAChCX,UAAU,CAACW,UAAU,GAAGA,UAAU;UAElCxM,OAAO,CAACC,GAAG,CAAC,cAAcsL,KAAK,SAAS4B,gBAAgB,CAACxM,CAAC,CAACuM,OAAO,CAAC,CAAC,CAAC,KAAKC,gBAAgB,CAACtM,CAAC,CAACqM,OAAO,CAAC,CAAC,CAAC,KAAKC,gBAAgB,CAACpM,CAAC,CAACmM,OAAO,CAAC,CAAC,CAAC,YAAYpN,aAAa,EAAE,CAAC;;UAErK;UACA,IAAIA,aAAa,EAAE;YACjB7C,gBAAgB,GAAG4O,UAAU,CAACtB,KAAK;YACnChI,eAAe,CAACiJ,QAAQ,CAAC;UAC3B;QACF;;QAEA;QACA,MAAMhC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;QACtB,MAAMqB,iBAAiB,GAAG,IAAI,CAAC,CAAC;;QAEhChM,aAAa,CAAC6K,OAAO,CAAC,CAACwB,SAAS,EAAEtB,EAAE,KAAK;UACvC,IAAIJ,GAAG,GAAG0B,SAAS,CAACN,UAAU,GAAGC,iBAAiB,EAAE;YAClD/M,KAAK,CAACsN,MAAM,CAACF,SAAS,CAACX,KAAK,CAAC;YAC7B1L,aAAa,CAACwM,MAAM,CAACzB,EAAE,CAAC;YACxB5J,OAAO,CAACC,GAAG,CAAC,mBAAmB2J,EAAE,EAAE,CAAC;UACtC;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAIpE,KAAK,KAAKtH,WAAW,CAACS,IAAI,EAAE;QAC9BqB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEqI,OAAO,CAAC;QAEjC,IAAI;UACF,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;UAEnC;UACA,MAAMK,SAAS,GAAGJ,OAAO,CAACK,GAAG;UAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;UAEnC;UACA,MAAMC,aAAa,GAAGhK,gBAAgB,CAACiK,GAAG,CAACL,SAAS,CAAC;;UAErD;UACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;YACrD/I,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;cAC1BgJ,KAAK,EAAEN,SAAS;cAChBO,KAAK,EAAEL,gBAAgB;cACvBM,KAAK,EAAEJ;YACT,CAAC,CAAC;YACF;UACF;;UAEA;UACAhK,gBAAgB,CAACiC,GAAG,CAAC2H,SAAS,EAAEE,gBAAgB,CAAC;;UAEjD;UACA,IAAIN,OAAO,CAACjJ,IAAI,IAAIiJ,OAAO,CAACjJ,IAAI,CAACmI,aAAa,IAAIhI,KAAK,CAACC,OAAO,CAAC6I,OAAO,CAACjJ,IAAI,CAACmI,aAAa,CAAC,EAAE;YAC3Fc,OAAO,CAACjJ,IAAI,CAACmI,aAAa,CAACiC,OAAO,CAAClC,YAAY,IAAI;cACjD,MAAMnD,OAAO,GAAGmD,YAAY,CAACnD,OAAO;cAEpC,IAAI,CAACA,OAAO,EAAE;gBACZrE,OAAO,CAACE,KAAK,CAAC,kBAAkB,EAAEsH,YAAY,CAAC;gBAC/C;cACF;cAEAxH,OAAO,CAACC,GAAG,CAAC,WAAWoE,OAAO,UAAU,CAAC;;cAEzC;cACA,IAAImD,YAAY,CAACjD,MAAM,IAAI9E,KAAK,CAACC,OAAO,CAAC8H,YAAY,CAACjD,MAAM,CAAC,EAAE;gBAC7D;gBACA,MAAM8I,UAAU,GAAG,EAAE;gBAErB7F,YAAY,CAACjD,MAAM,CAACmF,OAAO,CAAC4D,KAAK,IAAI;kBACnC;kBACA,IAAI,CAACA,KAAK,CAACC,OAAO,EAAE;oBAClBvN,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAEoN,KAAK,CAAC;oBACtC;kBACF;kBAEA,MAAMC,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC;kBACxC;kBACA,MAAMC,SAAS,GAAGH,KAAK,CAACI,YAAY,GAClCC,oBAAoB,CAACL,KAAK,CAACI,YAAY,CAAC,GACxCE,iBAAiB,CAACL,OAAO,CAAC;;kBAE5B;kBACA,MAAMM,UAAU,GAAGP,KAAK,CAACQ,YAAY,IAAI,GAAG,CAAC,CAAC;kBAC9C,MAAMC,UAAU,GAAGC,QAAQ,CAACV,KAAK,CAACS,UAAU,CAAC,IAAI,CAAC;kBAElD/N,OAAO,CAACC,GAAG,CAAC,SAASoE,OAAO,WAAWkJ,OAAO,SAASE,SAAS,YAAYI,UAAU,WAAWE,UAAU,GAAG,CAAC;;kBAE/G;kBACA,MAAME,SAAS,GAAG;oBAChBV,OAAO;oBACPE,SAAS;oBACTK,YAAY,EAAED,UAAU;oBACxBE;kBACF,CAAC;;kBAED;kBACAV,UAAU,CAACa,IAAI,CAACD,SAAS,CAAC;;kBAE1B;kBACA;kBACA,IAAIE,eAAe,GAAGC,MAAM,CAAC/J,OAAO,CAAC;kBACrC,IAAIgK,iBAAiB,GAAGpP,gBAAgB,CAAC+J,GAAG,CAACmF,eAAe,CAAC;kBAE7D,IAAI,CAACE,iBAAiB,EAAE;oBACtB;oBACAF,eAAe,GAAGH,QAAQ,CAAC3J,OAAO,CAAC;oBACnCgK,iBAAiB,GAAGpP,gBAAgB,CAAC+J,GAAG,CAACmF,eAAe,CAAC;kBAC3D;kBAEA,IAAIE,iBAAiB,EAAE;oBACrB;oBACAC,wBAAwB,CAACD,iBAAiB,EAAEJ,SAAS,CAAC;;oBAEtD;oBACA,IAAIjK,oBAAoB,IAAIA,oBAAoB,CAACK,OAAO,KAAKA,OAAO,EAAE;sBACpEF,sBAAsB,CAACoK,IAAI,KAAK;wBAC9B,GAAGA,IAAI;wBACPnK,OAAO,EAAE,IAAI;wBACbmJ,OAAO;wBACPE,SAAS;wBACTzD,KAAK,EAAE6D,UAAU;wBACjBE;sBACF,CAAC,CAAC,CAAC;oBACL;kBACF,CAAC,MAAM;oBACL/N,OAAO,CAACwO,IAAI,CAAC,WAAWnK,OAAO,SAASkJ,OAAO,SAAS,CAAC;kBAC3D;gBACF,CAAC,CAAC;;gBAEF;gBACA,IAAIkB,QAAQ,GAAG,IAAI;gBACnB;gBACA,MAAMC,KAAK,GAAGN,MAAM,CAAC/J,OAAO,CAAC;gBAC7B,IAAIpF,gBAAgB,CAACkM,GAAG,CAACuD,KAAK,CAAC,EAAE;kBAC/BD,QAAQ,GAAGC,KAAK;gBAClB,CAAC,MAAM;kBACL;kBACA,MAAMC,KAAK,GAAGX,QAAQ,CAAC3J,OAAO,CAAC;kBAC/B,IAAIpF,gBAAgB,CAACkM,GAAG,CAACwD,KAAK,CAAC,EAAE;oBAC/BF,QAAQ,GAAGE,KAAK;kBAClB;gBACF;gBAEA,IAAIF,QAAQ,KAAK,IAAI,EAAE;kBACrB;kBACAvP,kBAAkB,CAAC8B,GAAG,CAACyN,QAAQ,EAAE;oBAC/BG,UAAU,EAAEnF,IAAI,CAACD,GAAG,CAAC,CAAC;oBACtBjF,MAAM,EAAE8I;kBACV,CAAC,CAAC;kBACFrN,OAAO,CAACC,GAAG,CAAC,aAAawO,QAAQ,KAAK,OAAOA,QAAQ,aAAa,CAAC;;kBAEnE;kBACA,IAAIrQ,MAAM,CAACoG,mBAAmB,KAC1BpG,MAAM,CAACoG,mBAAmB,CAACoB,OAAO,KAAK6I,QAAQ,IAC/CrQ,MAAM,CAACoG,mBAAmB,CAACoB,OAAO,KAAKwI,MAAM,CAACK,QAAQ,CAAC,IACvDrQ,MAAM,CAACoG,mBAAmB,CAACoB,OAAO,KAAKoI,QAAQ,CAACS,QAAQ,CAAC,CAAC,EAAE;oBAE9DzO,OAAO,CAACC,GAAG,CAAC,eAAewO,QAAQ,aAAa,CAAC;oBACjD;oBACArQ,MAAM,CAACoG,mBAAmB,CAACoB,OAAO,GAAG6I,QAAQ;;oBAE7C;oBACA,IAAIrQ,MAAM,CAACqG,0BAA0B,IAAI,CAACrG,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,EAAE;sBACnF5F,OAAO,CAACC,GAAG,CAAC,SAASwO,QAAQ,aAAa,CAAC;sBAC3CI,UAAU,CAAC,MAAM;wBACfzQ,MAAM,CAAC0Q,qBAAqB,CAACL,QAAQ,CAAC;sBACxC,CAAC,EAAE,GAAG,CAAC;oBACT;kBACF;gBACF,CAAC,MAAM;kBACL;kBACAvP,kBAAkB,CAAC8B,GAAG,CAACqD,OAAO,EAAE;oBAC9BuK,UAAU,EAAEnF,IAAI,CAACD,GAAG,CAAC,CAAC;oBACtBjF,MAAM,EAAE8I;kBACV,CAAC,CAAC;kBACFrN,OAAO,CAACC,GAAG,CAAC,sBAAsBoE,OAAO,YAAY,CAAC;gBACxD;cACF,CAAC,MAAM;gBACLrE,OAAO,CAACE,KAAK,CAAC,eAAe,EAAEsH,YAAY,CAAC;cAC9C;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACLxH,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEqI,OAAO,CAAC;UAC9D;QACF,CAAC,CAAC,OAAOrI,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,aAAa,EAAEA,KAAK,EAAEoI,OAAO,CAAC;QAC9C;MACF;;MAEA;MACA,IAAI9C,KAAK,KAAKtH,WAAW,CAACQ,GAAG,IAAI6J,OAAO,CAACuB,IAAI,KAAK,KAAK,EAAE;QACvD9J,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsI,OAAO,CAAC;;QAEhC;QACAnK,MAAM,CAAC2Q,WAAW,CAAC;UACjBjF,IAAI,EAAE,KAAK;UACXxK,IAAI,EAAEiJ,OAAO,CAACjJ;QAChB,CAAC,EAAE,GAAG,CAAC;QAEP,MAAM0P,OAAO,GAAGzG,OAAO,CAACjJ,IAAI;QAC5B,MAAM2P,KAAK,GAAGD,OAAO,CAACC,KAAK;QAC3B,MAAMC,MAAM,GAAGF,OAAO,CAACG,IAAI,IAAI,EAAE;QAEjCD,MAAM,CAACxF,OAAO,CAAC0F,KAAK,IAAI;UACtB,MAAMC,OAAO,GAAGD,KAAK,CAACE,KAAK;UAC3B,MAAMC,SAAS,GAAGH,KAAK,CAACG,SAAS;UACjC,MAAMC,WAAW,GAAGJ,KAAK,CAACI,WAAW;UACrC,MAAMC,SAAS,GAAGL,KAAK,CAACK,SAAS;UACjC,MAAMC,OAAO,GAAGN,KAAK,CAACM,OAAO;;UAE7B;UACA,MAAMrF,QAAQ,GAAGtI,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAC7CC,UAAU,CAACkH,OAAO,CAACW,OAAO,CAAC,EAC3B7H,UAAU,CAACkH,OAAO,CAACY,MAAM,CAC3B,CAAC;;UAED;UACA,IAAIC,WAAW,GAAG,EAAE;UACpB,IAAIC,YAAY,GAAG,EAAE;UAErB,QAAOP,SAAS;YACd,KAAK,KAAK;cAAG;cACXM,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,QAAQ;cACtBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,MAAM;cAAE;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF;cACED,WAAW,GAAGL,WAAW,IAAI,MAAM;cACnCM,YAAY,GAAG,SAAS;UAC5B;;UAEA;UACAC,iBAAiB,CAAC1F,QAAQ,EAAEwF,WAAW,EAAEC,YAAY,CAAC;UAEtD9P,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;YACtB+P,IAAI,EAAEX,OAAO;YACbY,IAAI,EAAEV,SAAS;YACfW,IAAI,EAAEV,WAAW;YACjBW,IAAI,EAAEV,SAAS;YACfW,IAAI,EAAEV,OAAO;YACbW,EAAE,EAAEhG;UACN,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAI7E,KAAK,KAAKtH,WAAW,CAACJ,KAAK,IAAIyK,OAAO,CAACuB,IAAI,KAAK,OAAO,EAAE;QAC3D9J,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEsI,OAAO,CAAC;QAEjC,MAAM+H,SAAS,GAAG/H,OAAO,CAACjJ,IAAI;QAC9B,MAAMiR,OAAO,GAAGD,SAAS,CAACC,OAAO;QACjC,MAAMC,SAAS,GAAGF,SAAS,CAACE,SAAS;QACrC,MAAMC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAM1N,QAAQ,GAAG;UACfN,QAAQ,EAAEqF,UAAU,CAACwI,SAAS,CAAC5E,OAAO,CAAC;UACvClJ,SAAS,EAAEsF,UAAU,CAACwI,SAAS,CAAC7E,QAAQ;QAC1C,CAAC;;QAED;QACA,MAAMpB,QAAQ,GAAGtI,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAAC9E,QAAQ,CAACP,SAAS,EAAEO,QAAQ,CAACN,QAAQ,CAAC;;QAEtF;QACA,QAAO+N,SAAS;UACd,KAAK,GAAG;YAAG;YACTT,iBAAiB,CAAC1F,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;YAClD;UACF,KAAK,KAAK;YAAG;YACX0F,iBAAiB,CAAC1F,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX0F,iBAAiB,CAAC1F,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;YAC/C;UACF,KAAK,IAAI;YAAG;YACV,MAAMqG,UAAU,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAE;YAC1CZ,iBAAiB,CAAC1F,QAAQ,EAAE,KAAKqG,UAAU,MAAM,EAAE,SAAS,CAAC;YAC7D;UACF,KAAK,IAAI;YAAG;YACVX,iBAAiB,CAAC1F,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV0F,iBAAiB,CAAC1F,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YAAG;YACZ0F,iBAAiB,CAAC1F,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV0F,iBAAiB,CAAC1F,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACV0F,iBAAiB,CAAC1F,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX,MAAMuG,YAAY,GAAGN,SAAS,CAACK,UAAU,CAAC,CAAE;YAC5C,MAAME,QAAQ,GAAGP,SAAS,CAACQ,UAAU,CAAC,CAAM;YAC5Cf,iBAAiB,CAAC1F,QAAQ,EAAE,QAAQ0G,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,EAAE,SAAS,CAAC;YAC/F;QACJ;QAEA;MACF;MACA;MACA7Q,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBuF,KAAK;QACLsE,IAAI,EAAEvB,OAAO,CAACuB,IAAI;QAClBxK,IAAI,EAAEiJ;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOrI,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEoI,OAAO,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAM0I,cAAc,GAAGA,CAAA,KAAM;IAC3BhR,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAMgR,KAAK,GAAG,QAAQ/S,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnEyB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEgR,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBpR,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAEDiR,EAAE,CAACG,SAAS,GAAIjC,KAAK,IAAK;MACxB,IAAI;QACF,MAAM9G,OAAO,GAAGE,IAAI,CAACC,KAAK,CAAC2G,KAAK,CAAC9P,IAAI,CAAC;;QAEtC;QACA,IAAIgJ,OAAO,CAACwB,IAAI,KAAK,SAAS,EAAE;UAC9B9J,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEqI,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACwB,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIxB,OAAO,CAACwB,IAAI,KAAK,SAAS,IAAIxB,OAAO,CAAC9C,KAAK,IAAI8C,OAAO,CAACC,OAAO,EAAE;UAClE;UACAF,iBAAiB,CAACC,OAAO,CAAC9C,KAAK,EAAEgD,IAAI,CAAC8I,SAAS,CAAChJ,OAAO,CAACC,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOrI,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDgR,EAAE,CAACK,OAAO,GAAIrR,KAAK,IAAK;MACtBF,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDgR,EAAE,CAACM,OAAO,GAAG,MAAM;MACjBxR,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACA4O,UAAU,CAACmC,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACA9O,aAAa,CAAC0D,OAAO,GAAGsL,EAAE;EAC5B,CAAC;EAEDlV,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6F,YAAY,CAAC+D,OAAO,EAAE;;IAE3B;IACA6L,aAAa,CAAC,CAAC;;IAEf;IACA3T,KAAK,GAAG,IAAI1B,KAAK,CAACsV,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAIvV,KAAK,CAACwV,iBAAiB,CACxC,EAAE,EACFxT,MAAM,CAACyT,UAAU,GAAGzT,MAAM,CAAC0T,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAAC5O,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChC2Q,MAAM,CAAChL,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtB5C,SAAS,CAAC6B,OAAO,GAAG+L,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAI3V,KAAK,CAAC4V,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAAC9T,MAAM,CAACyT,UAAU,EAAEzT,MAAM,CAAC0T,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAAChU,MAAM,CAACiU,gBAAgB,CAAC;IAC/CxQ,YAAY,CAAC+D,OAAO,CAAC0M,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAIpW,KAAK,CAACqW,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5D3U,KAAK,CAAC6M,GAAG,CAAC6H,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAItW,KAAK,CAACuW,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAAC3P,QAAQ,CAAC/B,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1ClD,KAAK,CAAC6M,GAAG,CAAC+H,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAIxW,KAAK,CAACuW,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAAC7P,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3ClD,KAAK,CAAC6M,GAAG,CAACiI,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAIzW,KAAK,CAAC0W,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAAC9P,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChC6R,SAAS,CAACE,KAAK,GAAG3R,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7BwR,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxBpV,KAAK,CAAC6M,GAAG,CAACkI,SAAS,CAAC;;IAEpB;IACApV,QAAQ,GAAG,IAAInB,aAAa,CAACqV,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzD9U,QAAQ,CAAC0V,aAAa,GAAG,IAAI;IAC7B1V,QAAQ,CAAC2V,aAAa,GAAG,IAAI;IAC7B3V,QAAQ,CAAC4V,kBAAkB,GAAG,KAAK;IACnC5V,QAAQ,CAACoJ,WAAW,GAAG,EAAE;IACzBpJ,QAAQ,CAACqJ,WAAW,GAAG,GAAG;IAC1BrJ,QAAQ,CAACsJ,aAAa,GAAG3F,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC5D,QAAQ,CAACuJ,aAAa,GAAG,CAAC;IAC1BvJ,QAAQ,CAACgJ,MAAM,CAACzF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BvD,QAAQ,CAACmJ,MAAM,CAAC,CAAC;;IAEjB;IACA5G,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnB0R,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBlU,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBsG,SAAS,EAAE,CAAC,CAACA,SAAS,CAAC6B;IACzB,CAAC,CAAC;;IAEF;IACA,MAAM0N,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAIrX,UAAU,CAAC,CAAC;QACtCqX,aAAa,CAACC,IAAI,CAChB,GAAG/U,QAAQ,uBAAuB,EACjCgV,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAAC9V,KAAK;;UAE/B;UACA,MAAMgW,gBAAgB,GAAG,IAAI1X,KAAK,CAAC2X,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAAC9H,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAM8H,WAAW,GAAG,IAAI5X,KAAK,CAAC6X,oBAAoB,CAAC;kBACjDjP,KAAK,EAAE,QAAQ;kBAAO;kBACtBkP,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIpI,KAAK,CAACE,QAAQ,CAAClB,GAAG,EAAE;kBACtBgJ,WAAW,CAAChJ,GAAG,GAAGgB,KAAK,CAACE,QAAQ,CAAClB,GAAG;gBACtC;;gBAEA;gBACAgB,KAAK,CAACE,QAAQ,GAAG8H,WAAW;gBAE5BhU,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE+L,KAAK,CAACrE,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMkM,YAAY,CAACQ,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMtI,KAAK,GAAG6H,YAAY,CAACQ,QAAQ,CAAC,CAAC,CAAC;YACtCP,gBAAgB,CAACnJ,GAAG,CAACqB,KAAK,CAAC;UAC7B;;UAEA;UACAlO,KAAK,CAAC6M,GAAG,CAACmJ,gBAAgB,CAAC;;UAE3B;UACA7W,gBAAgB,GAAG6W,gBAAgB;UAEnC9T,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9BoC,kBAAkB,CAAC,IAAI,CAAC;UACxBmR,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAS,GAAG,IAAK;UACPvU,OAAO,CAACC,GAAG,CAAC,aAAa,CAACsU,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEvH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDuG,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMiB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMZ,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACAtC,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAI8C,gBAAgB,EAAE;UACpB,MAAMa,YAAY,GAAG;YACnBnS,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UAED,MAAMiS,UAAU,GAAG7S,SAAS,CAAC6D,OAAO,CAACiC,YAAY,CAAC8M,YAAY,CAACnS,SAAS,EAAEmS,YAAY,CAAClS,QAAQ,CAAC;UAChG;UACAqR,gBAAgB,CAAC/Q,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACxC8S,gBAAgB,CAACpJ,QAAQ,CAAC7J,CAAC,GAAIO,IAAI,CAACC,EAAE,GAAGsT,YAAY,CAAChS,OAAO,GAAGvB,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9EyS,gBAAgB,CAAC7M,YAAY,CAAC,CAAC;UAC/B6M,gBAAgB,CAAC5M,iBAAiB,CAAC,IAAI,CAAC;UACxC5J,eAAe,GAAGwW,gBAAgB,CAAC/Q,QAAQ,CAACtC,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAM2U,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAIxB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMuB,WAAW,GAAIC,WAAW,IAAK;UACnCjV,OAAO,CAACC,GAAG,CAAC,WAAW6U,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAI7Y,UAAU,CAAC,CAAC;UAC/B6Y,MAAM,CAACvB,IAAI,CACTmB,GAAG,EACFlB,IAAI,IAAK;YACR5T,OAAO,CAACC,GAAG,CAAC,WAAW6U,GAAG,EAAE,CAAC;YAC7BtB,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAW,GAAG,IAAK;YACPvU,OAAO,CAACC,GAAG,CAAC,SAAS,CAACsU,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEvH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAhN,KAAK,IAAK;YACTF,OAAO,CAACE,KAAK,CAAC,SAAS4U,GAAG,EAAE,EAAE5U,KAAK,CAAC;YACpC,IAAI+U,WAAW,GAAG,CAAC,EAAE;cACnBjV,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3B4O,UAAU,CAAC,MAAMmG,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACLxB,MAAM,CAACvT,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAED8U,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAI7Y,UAAU,CAAC,CAAC;IAC/B6Y,MAAM,CAACvB,IAAI,CACT,GAAG/U,QAAQ,4BAA4B,EACvC,MAAOgV,IAAI,IAAK;MACd,IAAI;QACF,MAAMrJ,KAAK,GAAGqJ,IAAI,CAAC9V,KAAK;QACxByM,KAAK,CAAC4K,KAAK,CAACnU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBuJ,KAAK,CAACxH,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAE3B;QACA,IAAIlD,KAAK,EAAE;UACXA,KAAK,CAAC6M,GAAG,CAACJ,KAAK,CAAC;;UAEhB;UACA,MAAMmK,eAAe,CAAC,CAAC;QACvB,CAAC,MAAM;UACL1U,OAAO,CAACE,KAAK,CAAC,eAAe,CAAC;QAChC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACAqU,GAAG,IAAK;MACPvU,OAAO,CAACC,GAAG,CAAC,SAAS,CAACsU,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEvH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAhN,KAAK,IAAK;MACTF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BF,OAAO,CAACE,KAAK,CAAC,OAAO,EAAE;QACrBkV,IAAI,EAAElV,KAAK,CAAC4J,IAAI;QAChBuL,IAAI,EAAEnV,KAAK,CAACoI,OAAO;QACnBgN,KAAK,EAAE,GAAG1W,QAAQ,4BAA4B;QAC9C2W,KAAK,EAAE,GAAG3W,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAM4W,OAAO,GAAGA,CAAA,KAAM;MACpBrT,iBAAiB,CAACyD,OAAO,GAAG6P,qBAAqB,CAACD,OAAO,CAAC;;MAE1D;MACAhZ,KAAK,CAACoK,MAAM,CAAC,CAAC;MAEd,IAAIpJ,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACiI,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMgQ,UAAU,GAAGzY,gBAAgB,CAAC8F,QAAQ,CAACtC,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMkV,eAAe,GAAG1Y,gBAAgB,CAACyN,QAAQ,CAAC7J,CAAC;;QAEnD;QACA;QACA,MAAM+U,gBAAgB,GAAG,EAAED,eAAe,GAAGvU,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAMwU,YAAY,GAAG,IAAIzZ,KAAK,CAACwP,OAAO,CACpC,CAAC,EAAE,GAAGxK,IAAI,CAAC0U,GAAG,CAACF,gBAAgB,CAAC,EAChC,GAAG,EACH,CAAC,EAAE,GAAGxU,IAAI,CAAC2U,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACAjE,MAAM,CAAC5O,QAAQ,CAACwD,IAAI,CAACmP,UAAU,CAAC,CAAC/K,GAAG,CAACkL,YAAY,CAAC;;QAElD;QACAlE,MAAM,CAAC5L,EAAE,CAAC/E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAMgV,YAAY,GAAGN,UAAU,CAACjV,KAAK,CAAC,CAAC;QACvCkR,MAAM,CAAChL,MAAM,CAACqP,YAAY,CAAC;;QAE3B;QACArE,MAAM,CAACsE,sBAAsB,CAAC,CAAC;QAC/BtE,MAAM,CAAC1K,YAAY,CAAC,CAAC;QACrB0K,MAAM,CAACzK,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACAzJ,QAAQ,CAACiI,OAAO,GAAG,KAAK;;QAExB;QACAjI,QAAQ,CAACgJ,MAAM,CAACF,IAAI,CAACmP,UAAU,CAAC;QAChCjY,QAAQ,CAACmJ,MAAM,CAAC,CAAC;QAEjB5G,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnBiW,IAAI,EAAER,UAAU,CAACvN,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAEyJ,MAAM,CAAC5O,QAAQ,CAACoF,OAAO,CAAC,CAAC;UAC/BgO,IAAI,EAAEH,YAAY,CAAC7N,OAAO,CAAC,CAAC;UAC5BiO,IAAI,EAAEzE,MAAM,CAAC0E,iBAAiB,CAAC,IAAIja,KAAK,CAACwP,OAAO,CAAC,CAAC,CAAC,CAACzD,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI3K,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAACiI,OAAO,GAAG,IAAI;;QAEvB;QACAiM,MAAM,CAAC5L,EAAE,CAAC/E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAII,IAAI,CAACkV,GAAG,CAAC3E,MAAM,CAAC5O,QAAQ,CAAClC,CAAC,CAAC,GAAG,EAAE,EAAE;UACpC8Q,MAAM,CAAC5O,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BvD,QAAQ,CAACgJ,MAAM,CAACzF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B2Q,MAAM,CAAChL,MAAM,CAAClJ,QAAQ,CAACgJ,MAAM,CAAC;UAC9BhJ,QAAQ,CAACmJ,MAAM,CAAC,CAAC;QACnB;;QAEA;QACA;QACA+K,MAAM,CAAC1K,YAAY,CAAC,CAAC;QACrB0K,MAAM,CAACzK,iBAAiB,CAAC,IAAI,CAAC;MAEhC,CAAC,MAAM,IAAI1J,UAAU,KAAK,cAAc,EAAE;QACxC;QACAC,QAAQ,CAACmJ,MAAM,CAAC,CAAC;MACnB;MAEA,IAAInJ,QAAQ,EAAEA,QAAQ,CAACmJ,MAAM,CAAC,CAAC;MAC/B,IAAI9I,KAAK,IAAI6T,MAAM,EAAE;QACnBI,QAAQ,CAACwE,MAAM,CAACzY,KAAK,EAAE6T,MAAM,CAAC;MAChC;IACF,CAAC;IAED6D,OAAO,CAAC,CAAC;;IAET;IACA,MAAMgB,YAAY,GAAGA,CAAA,KAAM;MACzB7E,MAAM,CAAC8E,MAAM,GAAGrY,MAAM,CAACyT,UAAU,GAAGzT,MAAM,CAAC0T,WAAW;MACtDH,MAAM,CAACsE,sBAAsB,CAAC,CAAC;MAC/BlE,QAAQ,CAACG,OAAO,CAAC9T,MAAM,CAACyT,UAAU,EAAEzT,MAAM,CAAC0T,WAAW,CAAC;IACzD,CAAC;IACD1T,MAAM,CAACsY,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACApY,MAAM,CAACuY,aAAa,GAAG,MAAM;MAC3B,IAAI5S,SAAS,CAAC6B,OAAO,EAAE;QACrB7B,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC+C,SAAS,CAAC6B,OAAO,CAACe,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjC5C,SAAS,CAAC6B,OAAO,CAACqB,YAAY,CAAC,CAAC;QAChClD,SAAS,CAAC6B,OAAO,CAACsB,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAIzJ,QAAQ,EAAE;UACZA,QAAQ,CAACgJ,MAAM,CAACzF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BvD,QAAQ,CAACiI,OAAO,GAAG,IAAI;UACvBjI,QAAQ,CAACmJ,MAAM,CAAC,CAAC;QACnB;QAEApJ,UAAU,GAAG,QAAQ;QACrBwC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAIkC,iBAAiB,CAACyD,OAAO,EAAE;QAC7BgR,oBAAoB,CAACzU,iBAAiB,CAACyD,OAAO,CAAC;QAC/CzD,iBAAiB,CAACyD,OAAO,GAAG,IAAI;MAClC;;MAEA;MACA,IAAIxI,oBAAoB,EAAE;QACxByZ,aAAa,CAACzZ,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;;MAEA;MACA,IAAI8E,aAAa,CAAC0D,OAAO,EAAE;QACzB1D,aAAa,CAAC0D,OAAO,CAACkR,KAAK,CAAC,CAAC;QAC7B5U,aAAa,CAAC0D,OAAO,GAAG,IAAI;MAC9B;;MAEA;MACAxH,MAAM,CAAC2Y,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;;MAElD;MACA,IAAIzE,QAAQ,IAAIlQ,YAAY,CAAC+D,OAAO,EAAE;QACpC/D,YAAY,CAAC+D,OAAO,CAACoR,WAAW,CAACjF,QAAQ,CAACQ,UAAU,CAAC;QACrDR,QAAQ,CAAC3E,OAAO,CAAC,CAAC;MACpB;;MAEA;MACA,IAAIvO,aAAa,EAAE;QACjBA,aAAa,CAAC6K,OAAO,CAAC,CAACwB,SAAS,EAAEtB,EAAE,KAAK;UACvC,IAAIsB,SAAS,CAACX,KAAK,IAAIzM,KAAK,EAAE;YAC5BA,KAAK,CAACsN,MAAM,CAACF,SAAS,CAACX,KAAK,CAAC;UAC/B;QACF,CAAC,CAAC;QACF1L,aAAa,CAACoY,KAAK,CAAC,CAAC;MACvB;;MAEA;MACAhY,gBAAgB,CAACyK,OAAO,CAAEwN,QAAQ,IAAK;QACrC,IAAIpZ,KAAK,IAAIoZ,QAAQ,CAAC3M,KAAK,EAAE;UAC3BzM,KAAK,CAACsN,MAAM,CAAC8L,QAAQ,CAAC3M,KAAK,CAAC;QAC9B;MACF,CAAC,CAAC;MACFtL,gBAAgB,CAACgY,KAAK,CAAC,CAAC;MACxB/X,kBAAkB,CAAC+X,KAAK,CAAC,CAAC;;MAE1B;;MAEA;MACAnZ,KAAK,GAAG,IAAI;MACZL,QAAQ,GAAG,IAAI;MACfC,qBAAqB,GAAG,IAAI;MAC5BC,qBAAqB,GAAG,IAAI;MAC5BC,oBAAoB,GAAG,IAAI;MAC3BC,0BAA0B,GAAG,IAAI;MACjCZ,gBAAgB,GAAG,IAAI;MAEvB+C,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjE,SAAS,CAAC,MAAM;IACd;IACAmD,qBAAqB,CAAC,CAAC;;IAEvB;IACA,MAAMgY,uBAAuB,GAAGA,CAAA,KAAM;MACpCnX,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCd,qBAAqB,CAAC,CAAC;IACzB,CAAC;;IAED;IACAf,MAAM,CAACsY,gBAAgB,CAAC,oBAAoB,EAAES,uBAAuB,CAAC;;IAEtE;IACA,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnClY,qBAAqB,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC;;IAET;IACA,OAAO,MAAM;MACXf,MAAM,CAAC2Y,mBAAmB,CAAC,oBAAoB,EAAEI,uBAAuB,CAAC;MACzEN,aAAa,CAACO,UAAU,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApb,SAAS,CAAC,MAAM;IACd;IACA,IAAI8B,KAAK,IAAIiE,SAAS,CAAC6D,OAAO,EAAE;MAC9B5F,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA,MAAMqX,KAAK,GAAGzI,UAAU,CAAC,MAAM;QAC7B,IAAI/Q,KAAK,IAAIiE,SAAS,CAAC6D,OAAO,EAAE;UAAG;UACjC2R,mBAAmB,CAACxV,SAAS,CAAC6D,OAAO,CAAC;QACxC;MACF,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAM4R,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM;MACLtX,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC,EAAE,CAACnC,KAAK,CAAC,CAAC;;EAEX;EACA9B,SAAS,CAAC,MAAM;IACd,IAAI6F,YAAY,CAAC+D,OAAO,EAAE;MACxB;MACA,MAAM6R,WAAW,GAAIrI,KAAK,IAAK;QAC7B,IAAItR,KAAK,IAAIiG,SAAS,CAAC6B,OAAO,EAAE;UAC9B5F,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEmP,KAAK,CAACsI,OAAO,EAAEtI,KAAK,CAACuI,OAAO,CAAC;UACpDC,gBAAgB,CAACxI,KAAK,EAAEvN,YAAY,CAAC+D,OAAO,EAAE9H,KAAK,EAAEiG,SAAS,CAAC6B,OAAO,EAAEzB,sBAAsB,CAAC;QACjG,CAAC,MAAM;UACLnE,OAAO,CAACwO,IAAI,CAAC,4BAA4B,CAAC;QAC5C;MACF,CAAC;;MAED;MACA3M,YAAY,CAAC+D,OAAO,CAAC8Q,gBAAgB,CAAC,OAAO,EAAEe,WAAW,CAAC;;MAE3D;MACAzX,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC4B,YAAY,CAAC+D,OAAO,CAAC;;MAEpD;MACA,OAAO,MAAM;QACX,IAAI/D,YAAY,CAAC+D,OAAO,EAAE;UACxB/D,YAAY,CAAC+D,OAAO,CAACmR,mBAAmB,CAAC,OAAO,EAAEU,WAAW,CAAC;UAC9DzX,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACnC,KAAK,EAAEiG,SAAS,CAAC6B,OAAO,CAAC,CAAC;;EAE9B;EACA,MAAMiS,SAAS,GAAG1b,WAAW,CAAC,MAAM;IAClC6D,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B;EACF,CAAC,EAAE,CAAC4B,YAAY,EAAEuD,aAAa,EAAEnG,gBAAgB,CAAC,CAAC;;EAEnD;EACA,MAAM6Y,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMC,QAAQ,GAAG,IAAI3b,KAAK,CAAC4b,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChD,MAAM9L,QAAQ,GAAG,IAAI9P,KAAK,CAAC6b,iBAAiB,CAAC;MAAEjT,KAAK,EAAE;IAAS,CAAC,CAAC;IACjE,MAAMqJ,iBAAiB,GAAG,IAAIjS,KAAK,CAAC8b,IAAI,CAACH,QAAQ,EAAE7L,QAAQ,CAAC;;IAE5D;IACA,MAAMiM,YAAY,GAAG,IAAI/b,KAAK,CAACgc,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC5D,MAAMC,YAAY,GAAG,IAAIjc,KAAK,CAAC6b,iBAAiB,CAAC;MAAEjT,KAAK,EAAE;IAAS,CAAC,CAAC;IACrE,MAAMsT,SAAS,GAAG,IAAIlc,KAAK,CAAC8b,IAAI,CAACC,YAAY,EAAEE,YAAY,CAAC;IAC5DC,SAAS,CAACvV,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAClCqN,iBAAiB,CAAC1D,GAAG,CAAC2N,SAAS,CAAC;IAEhC,OAAOjK,iBAAiB;EAC1B,CAAC;;EAED;EACA,MAAMkK,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACza,KAAK,EAAE;;IAEZ;IACAmB,gBAAgB,CAACyK,OAAO,CAAC,CAACwN,QAAQ,EAAE7S,OAAO,KAAK;MAC9C,IAAI6S,QAAQ,CAAC3M,KAAK,EAAE;QAClB;QACA,MAAMiO,cAAc,GAAG,IAAIpc,KAAK,CAACqc,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxD,MAAMC,cAAc,GAAG,IAAItc,KAAK,CAAC6b,iBAAiB,CAAC;UACjDjT,KAAK,EAAE,QAAQ;UAAC;UAChB2T,WAAW,EAAE,KAAK;UAClBC,OAAO,EAAE,GAAG;UAAG;UACfC,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,MAAMC,UAAU,GAAG,IAAI1c,KAAK,CAAC8b,IAAI,CAACM,cAAc,EAAEE,cAAc,CAAC;QACjEI,UAAU,CAAC/V,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE;;QAEnC;QACA8X,UAAU,CAAC3M,QAAQ,GAAG;UACpBrC,IAAI,EAAE,cAAc;UACpBzF,OAAO,EAAEA,OAAO;UAChBsD,IAAI,EAAEuP,QAAQ,CAAC1P,YAAY,CAACG,IAAI;UAChCoR,aAAa,EAAE;QACjB,CAAC;;QAED;QACA7B,QAAQ,CAAC3M,KAAK,CAACI,GAAG,CAACmO,UAAU,CAAC;QAE9B9Y,OAAO,CAACC,GAAG,CAAC,OAAOiX,QAAQ,CAAC1P,YAAY,CAACG,IAAI,KAAKtD,OAAO,aAAa,CAAC;MACzE;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACArI,SAAS,CAAC,MAAM;IACd;IACA,MAAMsb,KAAK,GAAGzI,UAAU,CAAC,MAAM;MAC7B,IAAI5P,gBAAgB,CAAC+Z,IAAI,GAAG,CAAC,EAAE;QAC7BhZ,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;QAC1B;MACF;IACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAE;;IAEX,OAAO,MAAMuX,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACAtb,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACA,IAAIyI,0BAA0B,CAACmB,OAAO,EAAE;QACtCiR,aAAa,CAACpS,0BAA0B,CAACmB,OAAO,CAAC;QACjDnB,0BAA0B,CAACmB,OAAO,GAAG,IAAI;QACzC5F,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC9B;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAjE,SAAS,CAAC,MAAM;IACd,IAAI,CAACkI,mBAAmB,CAACE,OAAO,IAAIK,0BAA0B,CAACmB,OAAO,EAAE;MACtEiR,aAAa,CAACpS,0BAA0B,CAACmB,OAAO,CAAC;MACjDnB,0BAA0B,CAACmB,OAAO,GAAG,IAAI;MACzCpB,mBAAmB,CAACoB,OAAO,GAAG,IAAI;MAClC5F,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACnC;EACF,CAAC,EAAE,CAACiE,mBAAmB,CAACE,OAAO,CAAC,CAAC;;EAEjC;EACApI,SAAS,CAAC,MAAM;IACd;IACA,IAAIY,iBAAiB,IAAIA,iBAAiB,CAAC6K,aAAa,IAAI7K,iBAAiB,CAAC6K,aAAa,CAAC6M,MAAM,GAAG,CAAC,EAAE;MACtG;MACA,IAAI,CAACtQ,oBAAoB,EAAE;QACzB,MAAMiV,iBAAiB,GAAGrc,iBAAiB,CAAC6K,aAAa,CAAC,CAAC,CAAC;QAC5DzH,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEgZ,iBAAiB,CAACtR,IAAI,CAAC;;QAEjD;QACA,MAAM2P,KAAK,GAAGzI,UAAU,CAAC,MAAM;UAC7BvH,wBAAwB,CAAC2R,iBAAiB,CAACtR,IAAI,CAAC;QAClD,CAAC,EAAE,IAAI,CAAC;QAER,OAAO,MAAM6P,YAAY,CAACF,KAAK,CAAC;MAClC;IACF;EACF,CAAC,EAAE,CAAC1a,iBAAiB,EAAEoH,oBAAoB,CAAC,CAAC;EAE7C,oBACElH,OAAA,CAAAE,SAAA;IAAAqX,QAAA,gBACEvX,OAAA;MAAMoc,KAAK,EAAEpU,UAAW;MAAAuP,QAAA,EAAC;IAAK;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrCxc,OAAA,CAACJ,MAAM;MACLwc,KAAK,EAAEvU,uBAAwB;MAC/B4U,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAElS,wBAAyB;MACnCmS,OAAO,EAAE7c,iBAAiB,CAAC6K,aAAa,CAACuD,GAAG,CAACxD,YAAY,KAAK;QAC5DD,KAAK,EAAEC,YAAY,CAACG,IAAI;QACxB+R,KAAK,EAAElS,YAAY,CAACG;MACtB,CAAC,CAAC,CAAE;MACJqR,IAAI,EAAC,OAAO;MACZW,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACbzW,MAAM,EAAE,IAAI;QACZ0W,SAAS,EAAE;MACb,CAAE;MACFtS,KAAK,EAAEvD,oBAAoB,GAAGA,oBAAoB,CAAC2D,IAAI,GAAGmS;IAAU;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eACFxc,OAAA;MAAKid,GAAG,EAAElY,YAAa;MAACqX,KAAK,EAAE;QAAErU,KAAK,EAAE,MAAM;QAAE4F,MAAM,EAAE;MAAO;IAAE;MAAA0O,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGnEpV,mBAAmB,CAACE,OAAO,iBAC1BtH,OAAA;MACEoc,KAAK,EAAE;QACLnW,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAACpC,CAAC,IAAI;QAC3CiE,GAAG,EAAE,GAAGV,mBAAmB,CAACnB,QAAQ,CAAClC,CAAC,IAAI;QAC1CqC,SAAS,EAAE,wBAAwB;QACnCC,MAAM,EAAE,IAAI;QACZK,eAAe,EAAE,qBAAqB;QACtCwB,KAAK,EAAE,OAAO;QACdtB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,8BAA8B;QACzCN,OAAO,EAAE,GAAG;QACZyW,QAAQ,EAAE,OAAO;QAAE;QACnBpW,QAAQ,EAAE,MAAM,CAAC;MACnB,CAAE;MAAAyQ,QAAA,GAEDnQ,mBAAmB,CAACI,OAAO,eAC5BxH,OAAA;QACEoc,KAAK,EAAE;UACLnW,QAAQ,EAAE,UAAU;UACpB6B,GAAG,EAAE,KAAK;UACVqV,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,MAAM;UAClBzW,MAAM,EAAE,MAAM;UACduB,KAAK,EAAE,OAAO;UACdpB,QAAQ,EAAE,MAAM;UAChBD,MAAM,EAAE,SAAS;UACjBJ,OAAO,EAAE;QACX,CAAE;QACF4W,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAACjW,sBAAsB,CAAE;QAAAkQ,QAAA,EAC3D;MAED;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAEDxc,OAAA;MAAKoc,KAAK,EAAEpW,oBAAqB;MAAAuR,QAAA,gBAC/BvX,OAAA;QACEoc,KAAK,EAAE;UACL,GAAG5V,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFuX,OAAO,EAAE1U,kBAAmB;QAAA4O,QAAA,EAC7B;MAED;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxc,OAAA;QACEoc,KAAK,EAAE;UACL,GAAG5V,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoC,KAAK,EAAEpC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFuX,OAAO,EAAExU,kBAAmB;QAAA0O,QAAA,EAC7B;MAED;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAA1X,EAAA,CA/hDMJ,WAAW;AAAA6Y,EAAA,GAAX7Y,WAAW;AAgiDjB,SAASiL,gBAAgBA,CAAC6N,IAAI,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAMC,MAAM,GAAG;IACbC,QAAQ,EAAEF,UAAU,CAACE,QAAQ,IAAI,OAAO;IACxC7W,QAAQ,EAAE2W,UAAU,CAAC3W,QAAQ,IAAI,EAAE;IACnCqB,UAAU,EAAEsV,UAAU,CAACtV,UAAU,IAAI,MAAM;IAC3CyV,eAAe,EAAEH,UAAU,CAACG,eAAe,IAAI,CAAC;IAChDC,WAAW,EAAEJ,UAAU,CAACI,WAAW,IAAI;MAAEhO,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAI,CAAC;IACnEtJ,eAAe,EAAE+W,UAAU,CAAC/W,eAAe,IAAI;MAAEmJ,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE,GAAG;MAAEC,CAAC,EAAE;IAAI,CAAC;IACjFC,SAAS,EAAEwN,UAAU,CAACxN,SAAS,IAAI;MAAEJ,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAI,CAAC;IAC/DvJ,OAAO,EAAEgX,UAAU,CAAChX,OAAO,IAAI;EACjC,CAAC;;EAED;EACA,MAAMqX,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;;EAEvC;EACAD,OAAO,CAACE,IAAI,GAAG,GAAGT,MAAM,CAACvV,UAAU,IAAIuV,MAAM,CAAC5W,QAAQ,MAAM4W,MAAM,CAACC,QAAQ,EAAE;;EAE7E;EACA,MAAMS,SAAS,GAAGH,OAAO,CAACI,WAAW,CAACb,IAAI,CAAC,CAACzV,KAAK;;EAEjD;EACA,MAAMA,KAAK,GAAGqW,SAAS,GAAG,CAAC,GAAGV,MAAM,CAACjX,OAAO,GAAG,CAAC,GAAGiX,MAAM,CAACE,eAAe;EACzE,MAAMjQ,MAAM,GAAG+P,MAAM,CAAC5W,QAAQ,GAAG,CAAC,GAAG4W,MAAM,CAACjX,OAAO,GAAG,CAAC,GAAGiX,MAAM,CAACE,eAAe;EAEhFE,MAAM,CAAC/V,KAAK,GAAGA,KAAK;EACpB+V,MAAM,CAACnQ,MAAM,GAAGA,MAAM;;EAEtB;EACAsQ,OAAO,CAACE,IAAI,GAAG,GAAGT,MAAM,CAACvV,UAAU,IAAIuV,MAAM,CAAC5W,QAAQ,MAAM4W,MAAM,CAACC,QAAQ,EAAE;EAC7EM,OAAO,CAACK,YAAY,GAAG,QAAQ;;EAE/B;EACA,MAAMC,MAAM,GAAG,CAAC;EAChBN,OAAO,CAACO,SAAS,CAAC,CAAC;EACnBP,OAAO,CAACQ,MAAM,CAACf,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,CAAC;EACvEK,OAAO,CAACS,MAAM,CAAC3W,KAAK,GAAG2V,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,CAAC;EAC/EK,OAAO,CAACU,KAAK,CAAC5W,KAAK,GAAG2V,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAE7V,KAAK,GAAG2V,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEA,MAAM,CAAC;EAC9IN,OAAO,CAACS,MAAM,CAAC3W,KAAK,GAAG2V,MAAM,CAACE,eAAe,EAAEjQ,MAAM,GAAG+P,MAAM,CAACE,eAAe,GAAGW,MAAM,CAAC;EACxFN,OAAO,CAACU,KAAK,CAAC5W,KAAK,GAAG2V,MAAM,CAACE,eAAe,EAAEjQ,MAAM,GAAG+P,MAAM,CAACE,eAAe,EAAE7V,KAAK,GAAG2V,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAE5Q,MAAM,GAAG+P,MAAM,CAACE,eAAe,EAAEW,MAAM,CAAC;EAChKN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAE5Q,MAAM,GAAG+P,MAAM,CAACE,eAAe,CAAC;EAChFK,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,EAAEjQ,MAAM,GAAG+P,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAEjQ,MAAM,GAAG+P,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEA,MAAM,CAAC;EAChJN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,CAAC;EACvEN,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,EAAEF,MAAM,CAACE,eAAe,GAAGW,MAAM,EAAEb,MAAM,CAACE,eAAe,EAAEW,MAAM,CAAC;EAC9HN,OAAO,CAACW,SAAS,CAAC,CAAC;;EAEnB;EACAX,OAAO,CAACY,WAAW,GAAG,QAAQnB,MAAM,CAACG,WAAW,CAAChO,CAAC,KAAK6N,MAAM,CAACG,WAAW,CAAC/N,CAAC,KAAK4N,MAAM,CAACG,WAAW,CAAC9N,CAAC,KAAK2N,MAAM,CAACG,WAAW,CAAC7N,CAAC,GAAG;EAChIiO,OAAO,CAACa,SAAS,GAAGpB,MAAM,CAACE,eAAe;EAC1CK,OAAO,CAACc,MAAM,CAAC,CAAC;;EAEhB;EACAd,OAAO,CAACe,SAAS,GAAG,QAAQtB,MAAM,CAAChX,eAAe,CAACmJ,CAAC,KAAK6N,MAAM,CAAChX,eAAe,CAACoJ,CAAC,KAAK4N,MAAM,CAAChX,eAAe,CAACqJ,CAAC,KAAK2N,MAAM,CAAChX,eAAe,CAACsJ,CAAC,GAAG;EAC9IiO,OAAO,CAACgB,IAAI,CAAC,CAAC;;EAEd;EACAhB,OAAO,CAACe,SAAS,GAAG,QAAQtB,MAAM,CAACzN,SAAS,CAACJ,CAAC,KAAK6N,MAAM,CAACzN,SAAS,CAACH,CAAC,KAAK4N,MAAM,CAACzN,SAAS,CAACF,CAAC,KAAK2N,MAAM,CAACzN,SAAS,CAACD,CAAC,GAAG;EACtHiO,OAAO,CAACiB,SAAS,GAAG,QAAQ;;EAE5B;EACAjB,OAAO,CAACkB,QAAQ,CAAC3B,IAAI,EAAEzV,KAAK,GAAG,CAAC,EAAE4F,MAAM,GAAG,CAAC,CAAC;;EAE7C;EACA,MAAMyR,OAAO,GAAG,IAAI9f,KAAK,CAAC+f,aAAa,CAACvB,MAAM,CAAC;EAC/CsB,OAAO,CAACE,SAAS,GAAGhgB,KAAK,CAACigB,YAAY;EACtCH,OAAO,CAAC3P,WAAW,GAAG,IAAI;;EAE1B;EACA,MAAM+P,cAAc,GAAG,IAAIlgB,KAAK,CAACmgB,cAAc,CAAC;IAC9CvR,GAAG,EAAEkR,OAAO;IACZvD,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM6D,MAAM,GAAG,IAAIpgB,KAAK,CAACqgB,MAAM,CAACH,cAAc,CAAC;EAC/CE,MAAM,CAACrH,KAAK,CAACnU,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1Bwb,MAAM,CAACtQ,QAAQ,CAACwQ,SAAS,GAAG,KAAK,CAAC,CAAC;;EAEnC;EACAF,MAAM,CAACrQ,QAAQ,GAAG;IAChBmO,IAAI,EAAEA,IAAI;IACVE,MAAM,EAAEA;EACV,CAAC;EAED,OAAOgC,MAAM;AACf;;AAIA;AACApe,MAAM,CAACue,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMhL,MAAM,GAAGkJ,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAInL,MAAM,EAAE;MACV;MACA,MAAMoL,MAAM,GAAGpL,MAAM,CAAC5O,QAAQ,CAACtC,KAAK,CAAC,CAAC;;MAEtC;MACAkR,MAAM,CAAC5O,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9B2Q,MAAM,CAAC5L,EAAE,CAAC/E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtB2Q,MAAM,CAAChL,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAgL,MAAM,CAAC1K,YAAY,CAAC,CAAC;MACrB0K,MAAM,CAACzK,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMzJ,QAAQ,GAAGod,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIvf,QAAQ,EAAE;QACZA,QAAQ,CAACgJ,MAAM,CAACzF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BvD,QAAQ,CAACmJ,MAAM,CAAC,CAAC;MACnB;MAEA5G,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBgd,GAAG,EAAEF,MAAM,CAAC5U,OAAO,CAAC,CAAC;QACrB+U,GAAG,EAAEvL,MAAM,CAAC5O,QAAQ,CAACoF,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOgV,CAAC,EAAE;IACVnd,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEid,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAM1L,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACFzR,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAMiV,MAAM,GAAG,IAAI7Y,UAAU,CAAC,CAAC;;IAE/B;IACA,IAAI;MACF,MAAM,CAAC+gB,WAAW,EAAEC,WAAW,EAAEC,UAAU,EAAEC,gBAAgB,CAAC,GAAG,MAAMhK,OAAO,CAACiK,GAAG,CAAC,CACnFtI,MAAM,CAACuI,SAAS,CAAC,GAAG7e,QAAQ,uBAAuB,CAAC,EACpDsW,MAAM,CAACuI,SAAS,CAAC,GAAG7e,QAAQ,uBAAuB,CAAC,EAClDsW,MAAM,CAACuI,SAAS,CAAC,GAAG7e,QAAQ,sBAAsB,CAAC,EACnDsW,MAAM,CAACuI,SAAS,CAAC,GAAG7e,QAAQ,4BAA4B,CAAC,CAC5D,CAAC;;MAEF;MACAlB,qBAAqB,GAAG0f,WAAW,CAACtf,KAAK;MACzCJ,qBAAqB,CAACqO,QAAQ,CAAEC,KAAK,IAAK;QACxC,IAAIA,KAAK,CAACC,MAAM,EAAE;UACd,MAAM+H,WAAW,GAAG,IAAI5X,KAAK,CAAC6X,oBAAoB,CAAC;YACnDjP,KAAK,EAAE,QAAQ;YAAG;YAClBkP,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,GAAG;YACdC,eAAe,EAAE;UACnB,CAAC,CAAC;;UAEA;UACA,IAAIpI,KAAK,CAACE,QAAQ,CAAClB,GAAG,EAAE;YACtBgJ,WAAW,CAAChJ,GAAG,GAAGgB,KAAK,CAACE,QAAQ,CAAClB,GAAG;UACtC;UACAgB,KAAK,CAAC0R,OAAO,GAAG1J,WAAW;QAC/B;MACF,CAAC,CAAC;;MAEF;MACArW,qBAAqB,GAAG0f,WAAW,CAACvf,KAAK;MACzC;MACAH,qBAAqB,CAACwX,KAAK,CAACnU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxC;MACArD,qBAAqB,CAACoO,QAAQ,CAAEC,KAAK,IAAK;QACxC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAACgI,SAAS,GAAG,GAAG;UAC9BlI,KAAK,CAACE,QAAQ,CAACiI,SAAS,GAAG,GAAG;UAC9BnI,KAAK,CAACE,QAAQ,CAACkI,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;;MAEF;MACAxW,oBAAoB,GAAG0f,UAAU,CAACxf,KAAK;MACvC;MACAF,oBAAoB,CAACuX,KAAK,CAACnU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACvC;MACApD,oBAAoB,CAACmO,QAAQ,CAAEC,KAAK,IAAK;QACvC,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAACgI,SAAS,GAAG,GAAG;UAC9BlI,KAAK,CAACE,QAAQ,CAACiI,SAAS,GAAG,GAAG;UAC9BnI,KAAK,CAACE,QAAQ,CAACkI,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;;MAEA;MACAvW,0BAA0B,GAAG0f,gBAAgB,CAACzf,KAAK;MACnD;MACAD,0BAA0B,CAACsX,KAAK,CAACnU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C;MACAnD,0BAA0B,CAACkO,QAAQ,CAAEC,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,EAAE;UAClC;UACAF,KAAK,CAACE,QAAQ,CAACgI,SAAS,GAAG,GAAG;UAC9BlI,KAAK,CAACE,QAAQ,CAACiI,SAAS,GAAG,GAAG;UAC9BnI,KAAK,CAACE,QAAQ,CAACkI,eAAe,GAAG,GAAG;QACxC;MACF,CAAC,CAAC;MAEFpU,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACA,IAAI;QACF,IAAI,CAACxC,qBAAqB,EAAE;UAC1B,MAAM0f,WAAW,GAAG,MAAMlI,MAAM,CAACuI,SAAS,CAAC,GAAG7e,QAAQ,uBAAuB,CAAC;UAC9ElB,qBAAqB,GAAG0f,WAAW,CAACtf,KAAK;QAC3C;;QAEA;QACA,IAAI,CAACD,0BAA0B,EAAE;UAC/BmC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC7B,MAAMsd,gBAAgB,GAAG,MAAMrI,MAAM,CAACuI,SAAS,CAAC,GAAG7e,QAAQ,4BAA4B,CAAC;UACxFf,0BAA0B,GAAG0f,gBAAgB,CAACzf,KAAK;UACnDD,0BAA0B,CAACsX,KAAK,CAACnU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC7ChB,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QAC1B;MACF,CAAC,CAAC,OAAO0d,GAAG,EAAE;QACZ3d,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEyd,GAAG,CAAC;MAClC;IACF;EACF,CAAC,CAAC,OAAOzd,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;;AAED;AACA,MAAM6Q,mBAAmB,GAAIjH,IAAI,IAAK;EACpC,MAAM8T,KAAK,GAAG;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,KAAK,CAAC9T,IAAI,CAAC,IAAI,MAAM;AAC9B,CAAC;;AAED;AACA,MAAMiG,iBAAiB,GAAGA,CAAChN,QAAQ,EAAEuX,IAAI,EAAEtV,KAAK,KAAK;EACnD;EACA,IAAI,CAAClH,KAAK,EAAE;IACVkC,OAAO,CAACwO,IAAI,CAAC,oBAAoB,CAAC;IAClC;EACF;EAEA,IAAI;IACJ;IACA,MAAMgO,MAAM,GAAG/P,gBAAgB,CAAC6N,IAAI,CAAC;IACrCkC,MAAM,CAACzZ,QAAQ,CAAC/B,GAAG,CAAC+B,QAAQ,CAACpC,CAAC,EAAE,EAAE,EAAE,CAACoC,QAAQ,CAAClC,CAAC,CAAC,CAAC,CAAE;;IAEnD;IACAgO,UAAU,CAAC,MAAM;MACb;MACA,IAAI/Q,KAAK,IAAI0e,MAAM,CAACqB,MAAM,EAAE;QAC9B/f,KAAK,CAACsN,MAAM,CAACoR,MAAM,CAAC;MAClB;IACJ,CAAC,EAAE,GAAG,CAAC;;IAEP;IACA1e,KAAK,CAAC6M,GAAG,CAAC6R,MAAM,CAAC;IAEjBxc,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;MACvBoQ,EAAE,EAAEtN,QAAQ;MACZ+a,EAAE,EAAExD,IAAI;MACRyD,EAAE,EAAE/Y;IACN,CAAC,CAAC;EACF,CAAC,CAAC,OAAO9E,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;AACF,CAAC;;AAED;AACA,MAAMqX,mBAAmB,GAAIyG,iBAAiB,IAAK;EACjD,IAAI,CAAClgB,KAAK,EAAE;IACVkC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAC;IAC/B;EACF;EAEA,IAAI,CAAC8d,iBAAiB,EAAE;IACtBhe,OAAO,CAACE,KAAK,CAAC,mBAAmB,CAAC;IAClC;EACF;EAEA,IAAI,CAACrC,0BAA0B,EAAE;IAC/BmC,OAAO,CAACE,KAAK,CAAC,UAAU,CAAC;IACzB;IACA+d,2BAA2B,CAACD,iBAAiB,CAAC;IAC9C;EACF;;EAEA;EACA/e,gBAAgB,CAACyK,OAAO,CAAEwN,QAAQ,IAAK;IACrC,IAAIpZ,KAAK,IAAIoZ,QAAQ,CAAC3M,KAAK,EAAE;MAC3BzM,KAAK,CAACsN,MAAM,CAAC8L,QAAQ,CAAC3M,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACFtL,gBAAgB,CAACgY,KAAK,CAAC,CAAC;;EAExB;EACAra,iBAAiB,CAAC6K,aAAa,CAACiC,OAAO,CAAClC,YAAY,IAAI;IACtD;IACA,IAAIA,YAAY,CAAC0W,eAAe,KAAK,KAAK,EAAE;MAC1Cle,OAAO,CAACC,GAAG,CAAC,UAAUuH,YAAY,CAACG,IAAI,kBAAkB,CAAC;MAC1D;IACF;;IAEA;IACA,IAAIH,YAAY,CAAC/E,QAAQ,IAAI+E,YAAY,CAAChF,SAAS,IAAIgF,YAAY,CAACnD,OAAO,EAAE;MAC3E;MACA,MAAMgG,QAAQ,GAAG2T,iBAAiB,CAACnW,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,EAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC;MAEDzC,OAAO,CAACC,GAAG,CAAC,SAASuH,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,iBAAiBgG,QAAQ,CAAC1J,CAAC,KAAK0J,QAAQ,CAACxJ,CAAC,GAAG,CAAC;MAE7G,IAAI;QACF;QACA,MAAMwN,iBAAiB,GAAGxQ,0BAA0B,CAAC4C,KAAK,CAAC,CAAC;;QAE5D;QACA4N,iBAAiB,CAAC1G,IAAI,GAAG,OAAOH,YAAY,CAACG,IAAI,EAAE;;QAEnD;QACA0G,iBAAiB,CAACtL,QAAQ,CAAC/B,GAAG,CAACqJ,QAAQ,CAAC1J,CAAC,EAAE,EAAE,EAAE,CAAC0J,QAAQ,CAACxJ,CAAC,CAAC;;QAE3D;QACAwN,iBAAiB,CAAC8G,KAAK,CAACnU,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;QAEvC;QACAqN,iBAAiB,CAACrB,WAAW,GAAG,GAAG;;QAEnC;QACAqB,iBAAiB,CAACtC,QAAQ,CAACC,KAAK,IAAI;UAClC;UACA,IAAIA,KAAK,CAACC,MAAM,EAAE;YAChB;YACAD,KAAK,CAACE,QAAQ,CAACyM,WAAW,GAAG,KAAK;YAClC3M,KAAK,CAACE,QAAQ,CAAC0M,OAAO,GAAG,GAAG;YAC5B5M,KAAK,CAACE,QAAQ,CAACiS,IAAI,GAAG/hB,KAAK,CAACgiB,UAAU;YACtCpS,KAAK,CAACE,QAAQ,CAAC2M,UAAU,GAAG,IAAI;YAChC7M,KAAK,CAACE,QAAQ,CAACwQ,SAAS,GAAG,IAAI;YAC/B1Q,KAAK,CAACE,QAAQ,CAACK,WAAW,GAAG,IAAI;;YAEjC;YACAP,KAAK,CAACgB,WAAW,GAAG,GAAG;UACzB;QACF,CAAC,CAAC;;QAEF;QACAqB,iBAAiB,CAAClC,QAAQ,GAAG;UAC3BrC,IAAI,EAAE,cAAc;UACpBzF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;UAC7BsD,IAAI,EAAEH,YAAY,CAACG;QACrB,CAAC;;QAED;QACA,MAAM0W,gBAAgB,GAAG,IAAIjiB,KAAK,CAACqc,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;QAC5D,MAAM6F,gBAAgB,GAAG,IAAIliB,KAAK,CAAC6b,iBAAiB,CAAC;UACnDjT,KAAK,EAAE,QAAQ;UACf2T,WAAW,EAAE,IAAI;UACjBC,OAAO,EAAE,GAAG;UAAG;UACfC,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,MAAM0F,QAAQ,GAAG,IAAIniB,KAAK,CAAC8b,IAAI,CAACmG,gBAAgB,EAAEC,gBAAgB,CAAC;QACnEC,QAAQ,CAAC5W,IAAI,GAAG,UAAUH,YAAY,CAACG,IAAI,EAAE;QAC7C4W,QAAQ,CAACpS,QAAQ,GAAG;UAClBrC,IAAI,EAAE,cAAc;UACpBzF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;UAC7BsD,IAAI,EAAEH,YAAY,CAACG,IAAI;UACvB6W,UAAU,EAAE;QACd,CAAC;QAEDnQ,iBAAiB,CAAC1D,GAAG,CAAC4T,QAAQ,CAAC;;QAE/B;QACAzgB,KAAK,CAAC6M,GAAG,CAAC0D,iBAAiB,CAAC;;QAE5B;QACApP,gBAAgB,CAAC+B,GAAG,CAACwG,YAAY,CAACnD,OAAO,EAAE;UACzCkG,KAAK,EAAE8D,iBAAiB;UACxB7G,YAAY,EAAEA,YAAY;UAC1BzE,QAAQ,EAAEsH;QACZ,CAAC,CAAC;QAEFrK,OAAO,CAACC,GAAG,CAAC,SAASuH,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkBgG,QAAQ,CAAC1J,CAAC,KAAK,CAAC0J,QAAQ,CAACxJ,CAAC,GAAG,CAAC;MACjH,CAAC,CAAC,OAAOX,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,QAAQsH,YAAY,CAACG,IAAI,YAAY,EAAEzH,KAAK,CAAC;QAC3D;QACA4X,wBAAwB,CAACtQ,YAAY,EAAE6C,QAAQ,EAAE2T,iBAAiB,CAAC;MACrE;IACF;EACF,CAAC,CAAC;;EAEF;EACAhe,OAAO,CAACC,GAAG,CAAC,OAAOhB,gBAAgB,CAAC+Z,IAAI,SAAS,CAAC;EAClD/Z,gBAAgB,CAACyK,OAAO,CAAC,CAACwN,QAAQ,EAAE7S,OAAO,KAAK;IAC9CrE,OAAO,CAACC,GAAG,CAAC,QAAQoE,OAAO,KAAK6S,QAAQ,CAAC1P,YAAY,CAACG,IAAI,EAAE,CAAC;EAC/D,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMsW,2BAA2B,GAAID,iBAAiB,IAAK;EACzDphB,iBAAiB,CAAC6K,aAAa,CAACiC,OAAO,CAAClC,YAAY,IAAI;IACtD;IACA,IAAIA,YAAY,CAAC0W,eAAe,KAAK,KAAK,EAAE;MAC1C;IACF;IAEA,IAAI1W,YAAY,CAAC/E,QAAQ,IAAI+E,YAAY,CAAChF,SAAS,IAAIgF,YAAY,CAACnD,OAAO,EAAE;MAC3E;MACA,MAAMgG,QAAQ,GAAG2T,iBAAiB,CAACnW,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,EAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC;MAEDqV,wBAAwB,CAACtQ,YAAY,EAAE6C,QAAQ,EAAE2T,iBAAiB,CAAC;IACrE;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMlG,wBAAwB,GAAGA,CAACtQ,YAAY,EAAE6C,QAAQ,EAAE2T,iBAAiB,KAAK;EAC9E;EACA,MAAMjG,QAAQ,GAAG,IAAI3b,KAAK,CAAC4b,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAClD,MAAM9L,QAAQ,GAAG,IAAI9P,KAAK,CAAC6b,iBAAiB,CAAC;IAC3CjT,KAAK,EAAE,QAAQ;IACf2T,WAAW,EAAE,KAAK;IAClBC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAMvK,iBAAiB,GAAG,IAAIjS,KAAK,CAAC8b,IAAI,CAACH,QAAQ,EAAE7L,QAAQ,CAAC;;EAE5D;EACAmC,iBAAiB,CAAC1G,IAAI,GAAG,SAASH,YAAY,CAACG,IAAI,EAAE;;EAErD;EACA0G,iBAAiB,CAACtL,QAAQ,CAAC/B,GAAG,CAACqJ,QAAQ,CAAC1J,CAAC,EAAE,EAAE,EAAE,CAAC0J,QAAQ,CAACxJ,CAAC,CAAC;;EAE3D;EACAwN,iBAAiB,CAACrB,WAAW,GAAG,GAAG;;EAEnC;EACAqB,iBAAiB,CAAClC,QAAQ,GAAG;IAC3BrC,IAAI,EAAE,cAAc;IACpBzF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG;EACrB,CAAC;;EAED;EACA,MAAM0W,gBAAgB,GAAG,IAAIjiB,KAAK,CAACqc,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EAC5D,MAAM6F,gBAAgB,GAAG,IAAIliB,KAAK,CAAC6b,iBAAiB,CAAC;IACnDjT,KAAK,EAAE,QAAQ;IACf2T,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE,GAAG;IAAG;IACfC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF,MAAM0F,QAAQ,GAAG,IAAIniB,KAAK,CAAC8b,IAAI,CAACmG,gBAAgB,EAAEC,gBAAgB,CAAC;EACnEC,QAAQ,CAAC5W,IAAI,GAAG,YAAYH,YAAY,CAACG,IAAI,EAAE;EAC/C4W,QAAQ,CAACpS,QAAQ,GAAG;IAClBrC,IAAI,EAAE,cAAc;IACpBzF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG,IAAI;IACvB6W,UAAU,EAAE;EACd,CAAC;EAEDnQ,iBAAiB,CAAC1D,GAAG,CAAC4T,QAAQ,CAAC;;EAE/B;EACAzgB,KAAK,CAAC6M,GAAG,CAAC0D,iBAAiB,CAAC;;EAE5B;EACApP,gBAAgB,CAAC+B,GAAG,CAACwG,YAAY,CAACnD,OAAO,EAAE;IACzCkG,KAAK,EAAE8D,iBAAiB;IACxB7G,YAAY,EAAEA,YAAY;IAC1BzE,QAAQ,EAAEsH;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMoU,aAAa,GAAG,IAAIriB,KAAK,CAACqc,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAMiG,aAAa,GAAG,IAAItiB,KAAK,CAAC6b,iBAAiB,CAAC;IAAEjT,KAAK,EAAE;EAAS,CAAC,CAAC;EACtE,MAAM2Z,SAAS,GAAG,IAAIviB,KAAK,CAAC8b,IAAI,CAACuG,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAAC5b,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;EAEhC;EACA2d,SAAS,CAACxS,QAAQ,GAAG;IACnBrC,IAAI,EAAE,cAAc;IACpBzF,OAAO,EAAEmD,YAAY,CAACnD,OAAO;IAC7BsD,IAAI,EAAEH,YAAY,CAACG;EACrB,CAAC;EAED0G,iBAAiB,CAAC1D,GAAG,CAACgU,SAAS,CAAC;EAEhC3e,OAAO,CAACC,GAAG,CAAC,SAASuH,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkBgG,QAAQ,CAAC1J,CAAC,KAAK,CAAC0J,QAAQ,CAACxJ,CAAC,GAAG,CAAC;AACjH,CAAC;;AAED;AACA,MAAM+M,iBAAiB,GAAIL,OAAO,IAAK;EACrC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,GAAG;MAAE,OAAO,OAAO;IACxB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMI,oBAAoB,GAAIiR,OAAO,IAAK;EACxC,QAAOA,OAAO;IACZ,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,GAAG;MAAE,OAAO,KAAK;IACtB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB,KAAK,IAAI;MAAE,OAAO,OAAO;IACzB;MAAS,OAAO,KAAKA,OAAO,EAAE;EAChC;AACF,CAAC;;AAED;AACA,MAAMhH,gBAAgB,GAAGA,CAACxI,KAAK,EAAEyP,SAAS,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,KAAK;EAC7F,IAAI,CAACH,SAAS,IAAI,CAACC,aAAa,IAAI,CAACC,cAAc,EAAE;EAErD/e,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEmP,KAAK,CAACsI,OAAO,EAAEtI,KAAK,CAACuI,OAAO,CAAC;;EAEvD;EACA,MAAMsH,IAAI,GAAGJ,SAAS,CAACK,qBAAqB,CAAC,CAAC;EAC9C,MAAMC,MAAM,GAAI,CAAC/P,KAAK,CAACsI,OAAO,GAAGuH,IAAI,CAAChc,IAAI,IAAI4b,SAAS,CAACO,WAAW,GAAI,CAAC,GAAG,CAAC;EAC5E,MAAMC,MAAM,GAAG,EAAE,CAACjQ,KAAK,CAACuI,OAAO,GAAGsH,IAAI,CAACra,GAAG,IAAIia,SAAS,CAACS,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;;EAE7E;EACA,MAAMC,SAAS,GAAG,IAAInjB,KAAK,CAACojB,SAAS,CAAC,CAAC;EACvC;EACAD,SAAS,CAAC/E,MAAM,CAACiF,MAAM,CAACC,SAAS,GAAG,CAAC;EACrCH,SAAS,CAAC/E,MAAM,CAACmF,IAAI,CAACD,SAAS,GAAG,CAAC;EAEnC,MAAME,WAAW,GAAG,IAAIxjB,KAAK,CAACyjB,OAAO,CAACV,MAAM,EAAEE,MAAM,CAAC;EACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,EAAEb,cAAc,CAAC;EAEpD/e,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEkf,MAAM,EAAEE,MAAM,CAAC;;EAEtC;EACA,MAAMU,mBAAmB,GAAG,EAAE;EAE9B9gB,gBAAgB,CAACyK,OAAO,CAAC,CAACwN,QAAQ,EAAE7S,OAAO,KAAK;IAC9C,IAAI6S,QAAQ,CAAC3M,KAAK,EAAE;MAClB;MACAwV,mBAAmB,CAAC7R,IAAI,CAACgJ,QAAQ,CAAC3M,KAAK,CAAC;MACxC;MACA2M,QAAQ,CAAC3M,KAAK,CAACnG,OAAO,GAAG,IAAI;MAC7B8S,QAAQ,CAAC3M,KAAK,CAACyC,WAAW,GAAG,IAAI,CAAC,CAAC;MACnC;MACAkK,QAAQ,CAAC3M,KAAK,CAACwB,QAAQ,CAACC,KAAK,IAAI;QAC/BA,KAAK,CAAC5H,OAAO,GAAG,IAAI;QACpB4H,KAAK,CAACgB,WAAW,GAAG,IAAI;MAC1B,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEFhN,OAAO,CAACC,GAAG,CAAC,QAAQ8f,mBAAmB,CAACzL,MAAM,gBAAgB,CAAC;;EAE/D;EACA,MAAM0L,sBAAsB,GAAGT,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,EAAE,IAAI,CAAC;EAEpF,IAAIC,sBAAsB,CAAC1L,MAAM,GAAG,CAAC,EAAE;IACrCtU,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE+f,sBAAsB,CAAC1L,MAAM,CAAC;IACxD0L,sBAAsB,CAACtW,OAAO,CAAC,CAACwW,SAAS,EAAEC,KAAK,KAAK;MACnDngB,OAAO,CAACC,GAAG,CAAC,QAAQkgB,KAAK,GAAG,EACjBD,SAAS,CAACE,MAAM,CAACzY,IAAI,IAAI,KAAK,EAC9B,KAAK,EAAEuY,SAAS,CAAChN,QAAQ,EACzB,WAAW,EAAEgN,SAAS,CAACE,MAAM,CAACjU,QAAQ,CAAC;IACpD,CAAC,CAAC;;IAEF;IACA,MAAMkU,GAAG,GAAGC,yBAAyB,CAACN,sBAAsB,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC;IAEvE,IAAIC,GAAG,IAAIA,GAAG,CAAClU,QAAQ,IAAIkU,GAAG,CAAClU,QAAQ,CAACrC,IAAI,KAAK,cAAc,EAAE;MAC/D;MACA,MAAMzF,OAAO,GAAGgc,GAAG,CAAClU,QAAQ,CAAC9H,OAAO;MACpCrE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoE,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;;MAEhE;MACA,IAAIkc,SAAS,GAAGlc,OAAO;MACvB,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,CAACpF,gBAAgB,CAACkM,GAAG,CAAC9G,OAAO,CAAC,IAAIpF,gBAAgB,CAACkM,GAAG,CAAC6C,QAAQ,CAAC3J,OAAO,CAAC,CAAC,EAAE;QAC5Gkc,SAAS,GAAGvS,QAAQ,CAAC3J,OAAO,CAAC;QAC7BrE,OAAO,CAACC,GAAG,CAAC,cAAcsgB,SAAS,EAAE,CAAC;MACxC,CAAC,MAAM,IAAI,OAAOlc,OAAO,KAAK,QAAQ,IAAI,CAACpF,gBAAgB,CAACkM,GAAG,CAAC9G,OAAO,CAAC,IAAIpF,gBAAgB,CAACkM,GAAG,CAACiD,MAAM,CAAC/J,OAAO,CAAC,CAAC,EAAE;QACjHkc,SAAS,GAAGnS,MAAM,CAAC/J,OAAO,CAAC;QAC3BrE,OAAO,CAACC,GAAG,CAAC,eAAesgB,SAAS,EAAE,CAAC;MACzC;;MAEA;MACAniB,MAAM,CAAC0Q,qBAAqB,CAACyR,SAAS,CAAC;MACvC;IACF;EACF;;EAEA;EACA,MAAMC,UAAU,GAAGjB,SAAS,CAACU,gBAAgB,CAACnB,aAAa,CAACzK,QAAQ,EAAE,IAAI,CAAC;EAE3ErU,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEugB,UAAU,CAAClM,MAAM,CAAC;EAE7C,IAAIkM,UAAU,CAAClM,MAAM,GAAG,CAAC,EAAE;IACzB;IACAkM,UAAU,CAAC9W,OAAO,CAAC,CAACwW,SAAS,EAAEC,KAAK,KAAK;MACvC,MAAME,GAAG,GAAGH,SAAS,CAACE,MAAM;MAC5BpgB,OAAO,CAACC,GAAG,CAAC,UAAUkgB,KAAK,GAAG,EAAEE,GAAG,CAAC1Y,IAAI,IAAI,KAAK,EACrC,WAAW,EAAE0Y,GAAG,CAAClU,QAAQ,EACzB,KAAK,EAAE+T,SAAS,CAAChN,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACA,KAAK,IAAIxL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8Y,UAAU,CAAClM,MAAM,EAAE5M,CAAC,EAAE,EAAE;MAC1C,MAAM2Y,GAAG,GAAGC,yBAAyB,CAACE,UAAU,CAAC9Y,CAAC,CAAC,CAAC0Y,MAAM,CAAC;MAC3D,IAAIC,GAAG,IAAIA,GAAG,CAAClU,QAAQ,IAAIkU,GAAG,CAAClU,QAAQ,CAACrC,IAAI,KAAK,cAAc,EAAE;QAC/D,MAAMzF,OAAO,GAAGgc,GAAG,CAAClU,QAAQ,CAAC9H,OAAO;QACpCrE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoE,OAAO,CAAC;;QAEvC;QACAjG,MAAM,CAAC0Q,qBAAqB,CAACzK,OAAO,CAAC;QACrC;MACF;IACF;EACF;;EAEA;EACArE,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;;EAE9B;EACA,IAAIwgB,YAAY,GAAG,IAAI;EACvB,IAAI5Z,WAAW,GAAG,GAAG,CAAC,CAAC;;EAEvB5H,gBAAgB,CAACyK,OAAO,CAAC,CAACwN,QAAQ,EAAE7S,OAAO,KAAK;IAC9C,IAAI6S,QAAQ,CAAC3M,KAAK,EAAE;MAClB,MAAMmW,QAAQ,GAAG,IAAItkB,KAAK,CAACwP,OAAO,CAAC,CAAC;MACpC;MACAsL,QAAQ,CAAC3M,KAAK,CAACoW,gBAAgB,CAACD,QAAQ,CAAC;;MAEzC;MACA,MAAME,SAAS,GAAGF,QAAQ,CAACjgB,KAAK,CAAC,CAAC;MAClCmgB,SAAS,CAACC,OAAO,CAAC9B,cAAc,CAAC;;MAEjC;MACA,MAAM+B,EAAE,GAAGF,SAAS,CAACjgB,CAAC,GAAGwe,MAAM;MAC/B,MAAM4B,EAAE,GAAGH,SAAS,CAAC/f,CAAC,GAAGwe,MAAM;MAC/B,MAAMnM,QAAQ,GAAG9R,IAAI,CAAC4f,IAAI,CAACF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;MAE7C/gB,OAAO,CAACC,GAAG,CAAC,MAAMoE,OAAO,OAAO,EAAE6O,QAAQ,CAAC;MAE3C,IAAIA,QAAQ,GAAGrM,WAAW,EAAE;QAC1BA,WAAW,GAAGqM,QAAQ;QACtBuN,YAAY,GAAG;UAAEpc,OAAO;UAAE6O;QAAS,CAAC;MACtC;IACF;EACF,CAAC,CAAC;EAEF,IAAIuN,YAAY,EAAE;IAChBzgB,OAAO,CAACC,GAAG,CAAC,oBAAoBwgB,YAAY,CAACpc,OAAO,SAASoc,YAAY,CAACvN,QAAQ,EAAE,CAAC;;IAErF;IACA9U,MAAM,CAAC0Q,qBAAqB,CAAC2R,YAAY,CAACpc,OAAO,CAAC;IAClD;EACF;EAEArE,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;;EAE1B;EACA,IAAI+e,eAAe,EAAE;IACnBA,eAAe,CAACzQ,IAAI,IAAI;MACtB,IAAIA,IAAI,CAACnK,OAAO,EAAE;QAChB,OAAO;UAAE,GAAGmK,IAAI;UAAEnK,OAAO,EAAE;QAAM,CAAC;MACpC;MACA,OAAOmK,IAAI;IACb,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,MAAM6L,kBAAkB,GAAI4E,eAAe,IAAK;EAC9C;EACA,IAAI5gB,MAAM,CAACqG,0BAA0B,IAAIrG,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,EAAE;IAClFiR,aAAa,CAACzY,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,CAAC;IACxDxH,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,GAAG,IAAI;IAChD5F,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;EAC9B;;EAEA;EACA,IAAI7B,MAAM,CAACoG,mBAAmB,EAAE;IAC9BpG,MAAM,CAACoG,mBAAmB,CAACoB,OAAO,GAAG,IAAI;EAC3C;;EAEA;EACAoZ,eAAe,CAACzQ,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEnK,OAAO,EAAE;EAAM,CAAC,CAAC,CAAC;EACtDpE,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;AACtB,CAAC;;AAED;AACA7B,MAAM,CAAC6iB,qBAAqB,GAAI5c,OAAO,IAAK;EAC1C,IAAI;IAAA,IAAA6c,qBAAA,EAAAC,sBAAA;IACF;IACA,MAAMrT,YAAY,GAAG7O,gBAAgB,CAAC+J,GAAG,CAAC3E,OAAO,IAAI,GAAG,CAAC;IACzD,IAAI,CAACyJ,YAAY,EAAE;MACjB9N,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEmE,OAAO,CAAC;;MAEtC;MACArE,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxBhB,gBAAgB,CAACyK,OAAO,CAAC,CAAC0X,KAAK,EAAExX,EAAE,KAAK;QACtC5J,OAAO,CAACC,GAAG,CAAC,KAAK2J,EAAE,KAAKwX,KAAK,CAAC5Z,YAAY,CAACG,IAAI,EAAE,CAAC;MACpD,CAAC,CAAC;MAEF,OAAO,KAAK;IACd;;IAEA;IACA,MAAM0Z,UAAU,GAAGvT,YAAY,CAACvD,KAAK;;IAErC;IACA,MAAM+W,SAAS,GAAGpiB,kBAAkB,CAAC8J,GAAG,CAAC3E,OAAO,CAAC;IACjD,MAAMmD,YAAY,GAAGsG,YAAY,CAACtG,YAAY;;IAE9C;IACA,IAAIlD,OAAO;IAEX,IAAIgd,SAAS,IAAIA,SAAS,CAAC/c,MAAM,EAAE;MACjCD,OAAO,gBACLxH,OAAA;QAAKoc,KAAK,EAAE;UAAE3V,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAEgV,SAAS,EAAE,OAAO;UAAE0H,SAAS,EAAE;QAAO,CAAE;QAAAlN,QAAA,gBACpFvX,OAAA;UAAKoc,KAAK,EAAE;YACVjU,UAAU,EAAE,MAAM;YAClBuc,YAAY,EAAE,KAAK;YACnB5d,QAAQ,EAAE,MAAM;YAChB6d,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAArN,QAAA,GACC7M,YAAY,CAACG,IAAI,EAAC,QAAM,EAACtD,OAAO,EAAC,GACpC;QAAA;UAAA8U,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxc,OAAA;UAAAuX,QAAA,EACGiN,SAAS,CAAC/c,MAAM,CAACyG,GAAG,CAAC,CAACsC,KAAK,EAAE6S,KAAK,KAAK;YACtC,IAAIwB,UAAU;YACd,QAAQrU,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBAAE6T,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;gBAAEA,UAAU,GAAG,SAAS;gBAAE;cAClC,KAAK,GAAG;cAAE;gBAASA,UAAU,GAAG,SAAS;gBAAE;YAC7C;YAEA,oBACE7kB,OAAA;cAAiBoc,KAAK,EAAE;gBACtBsI,YAAY,EAAE,KAAK;gBACnBhe,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE,KAAK;gBACnBE,QAAQ,EAAE;cACZ,CAAE;cAAAyQ,QAAA,gBACAvX,OAAA;gBAAKoc,KAAK,EAAE;kBAAEjU,UAAU,EAAE;gBAAO,CAAE;gBAAAoP,QAAA,EAChCzG,iBAAiB,CAACN,KAAK,CAACC,OAAO;cAAC;gBAAA4L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNxc,OAAA;gBAAKoc,KAAK,EAAE;kBAAE9V,OAAO,EAAE,MAAM;kBAAEwe,cAAc,EAAE;gBAAgB,CAAE;gBAAAvN,QAAA,gBAC/DvX,OAAA;kBAAAuX,QAAA,EAAM;gBAAI;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBxc,OAAA;kBAAMoc,KAAK,EAAE;oBACXlU,KAAK,EAAE2c,UAAU;oBACjB1c,UAAU,EAAE,MAAM;oBAClBzB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAA2Q,QAAA,EACC/G,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGR,KAAK,CAACQ,YAAY,KAAK,GAAG,GAAG,IAAI,GAAG;gBAAI;kBAAAqL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNxc,OAAA;gBAAKoc,KAAK,EAAE;kBAAE9V,OAAO,EAAE,MAAM;kBAAEwe,cAAc,EAAE;gBAAgB,CAAE;gBAAAvN,QAAA,gBAC/DvX,OAAA;kBAAAuX,QAAA,EAAM;gBAAK;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBxc,OAAA;kBAAMoc,KAAK,EAAE;oBAAEjU,UAAU,EAAE;kBAAO,CAAE;kBAAAoP,QAAA,GAAE/G,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAAoL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAzBE6G,KAAK;cAAAhH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxc,OAAA;UAAKoc,KAAK,EAAE;YAAE2I,SAAS,EAAE,KAAK;YAAEje,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE;UAAO,CAAE;UAAAqP,QAAA,GAAC,4BAC3D,EAAC,IAAI5K,IAAI,CAAC,CAAC,CAACqY,kBAAkB,CAAC,CAAC,EAAC,6BACzC;QAAA;UAAA3I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IACH,CAAC,MAAM;MACLhV,OAAO,gBACLxH,OAAA;QAAKoc,KAAK,EAAE;UAAE3V,OAAO,EAAE,KAAK;UAAEyW,QAAQ,EAAE;QAAQ,CAAE;QAAA3F,QAAA,gBAChDvX,OAAA;UAAKoc,KAAK,EAAE;YAAEjU,UAAU,EAAE,MAAM;YAAEuc,YAAY,EAAE;UAAM,CAAE;UAAAnN,QAAA,EAAE7M,YAAY,CAACG;QAAI;UAAAwR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClFxc,OAAA;UAAAuX,QAAA,GAAK,kBAAM,EAAChQ,OAAO;QAAA;UAAA8U,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1Bxc,OAAA;UAAAuX,QAAA,EAAK;QAAU;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACN;IACH;;IAEA;IACA,MAAMyI,OAAO,GAAG3jB,MAAM,CAACyT,UAAU,GAAG,CAAC;IACrC,MAAMmQ,OAAO,GAAG5jB,MAAM,CAAC0T,WAAW,GAAG,CAAC;;IAEtC;IACA,MAAMkN,eAAe,IAAAkC,qBAAA,GAAGrG,QAAQ,CAAC+B,aAAa,CAAC,OAAO,CAAC,cAAAsE,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCe,gBAAgB,cAAAd,sBAAA,uBAAjDA,sBAAA,CAAmDhd,sBAAsB;IAEjG,IAAI6a,eAAe,EAAE;MACnB;MACAA,eAAe,CAAC;QACd5a,OAAO,EAAE,IAAI;QACbC,OAAO,EAAEA,OAAO;QAChBtB,QAAQ,EAAE;UAAEpC,CAAC,EAAEohB,OAAO;UAAElhB,CAAC,EAAEmhB;QAAQ,CAAC;QACpC1d,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE,CAAA+c,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE/c,MAAM,KAAI;MAC/B,CAAC,CAAC;MAEFvE,OAAO,CAACC,GAAG,CAAC,SAASuH,YAAY,CAACG,IAAI,KAAKtD,OAAO,UAAU,CAAC;MAC7D,OAAO,IAAI;IACb,CAAC,MAAM;MACL;MACA,MAAM6d,OAAO,GAAGrH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7CoH,OAAO,CAAChJ,KAAK,CAACnW,QAAQ,GAAG,UAAU;MACnCmf,OAAO,CAAChJ,KAAK,CAACjW,IAAI,GAAG,GAAG8e,OAAO,IAAI;MACnCG,OAAO,CAAChJ,KAAK,CAACtU,GAAG,GAAG,GAAGod,OAAO,IAAI;MAClCE,OAAO,CAAChJ,KAAK,CAAChW,SAAS,GAAG,wBAAwB;MAClDgf,OAAO,CAAChJ,KAAK,CAAC/V,MAAM,GAAG,MAAM;MAC7B+e,OAAO,CAAChJ,KAAK,CAAC1V,eAAe,GAAG,qBAAqB;MACrD0e,OAAO,CAAChJ,KAAK,CAAClU,KAAK,GAAG,OAAO;MAC7Bkd,OAAO,CAAChJ,KAAK,CAACxV,YAAY,GAAG,KAAK;MAClCwe,OAAO,CAAChJ,KAAK,CAACrV,SAAS,GAAG,8BAA8B;MACxDqe,OAAO,CAAChJ,KAAK,CAAC3V,OAAO,GAAG,KAAK;MAC7B2e,OAAO,CAAChJ,KAAK,CAACc,QAAQ,GAAG,OAAO;MAChCkI,OAAO,CAAChJ,KAAK,CAACtV,QAAQ,GAAG,MAAM;MAE/Bse,OAAO,CAACC,SAAS,GAAG;AAC1B;AACA,YAAY3a,YAAY,CAACG,IAAI,SAAStD,OAAO;AAC7C;AACA;AACA,qBAAqBA,OAAO;AAC5B,eAAeid,SAAS,GAAG,UAAU,GAAG,YAAY;AACpD;AACA;AACA,OAAO;MAEDzG,QAAQ,CAACuH,IAAI,CAAC9P,WAAW,CAAC4P,OAAO,CAAC;;MAElC;MACA,MAAMG,WAAW,GAAGH,OAAO,CAACtF,aAAa,CAAC,QAAQ,CAAC;MACnD,IAAIyF,WAAW,EAAE;QACfA,WAAW,CAAC3L,gBAAgB,CAAC,OAAO,EAAE,MAAM;UAC1CmE,QAAQ,CAACuH,IAAI,CAACpL,WAAW,CAACkL,OAAO,CAAC;QACpC,CAAC,CAAC;MACJ;MAEAliB,OAAO,CAACC,GAAG,CAAC,gBAAgBuH,YAAY,CAACG,IAAI,KAAKtD,OAAO,OAAO,CAAC;MACjE,OAAO,IAAI;IACb;EACF,CAAC,CAAC,OAAOnE,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA9B,MAAM,CAACkkB,iBAAiB,GAAG,MAAM;EAC/BtiB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;EAErB,IAAI,CAAChB,gBAAgB,IAAIA,gBAAgB,CAAC+Z,IAAI,KAAK,CAAC,EAAE;IACpDhZ,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IACxB,OAAO,EAAE;EACX;EAEA,MAAMsiB,IAAI,GAAG,EAAE;EACftjB,gBAAgB,CAACyK,OAAO,CAAC,CAAC0X,KAAK,EAAExX,EAAE,KAAK;IACtC5J,OAAO,CAACC,GAAG,CAAC,SAAS2J,EAAE,SAASwX,KAAK,CAAC5Z,YAAY,CAACG,IAAI,EAAE,CAAC;IAC1D4a,IAAI,CAACrU,IAAI,CAAC;MACRtE,EAAE;MACFjC,IAAI,EAAEyZ,KAAK,CAAC5Z,YAAY,CAACG,IAAI;MAC7B5E,QAAQ,EAAEqe,KAAK,CAACre;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOwf,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACAnkB,MAAM,CAAC0Q,qBAAqB,GAAIzK,OAAO,IAAK;EAC1C,IAAI;IACF;IACAA,OAAO,GAAG+J,MAAM,CAAC/J,OAAO,CAAC;IAEzBrE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEoE,OAAO,EAAE,KAAK,EAAE,OAAOA,OAAO,CAAC;IAC/ErE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEhB,gBAAgB,CAAC+Z,IAAI,CAAC;IAC3DhZ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEf,kBAAkB,CAAC8Z,IAAI,CAAC;;IAE/D;IACA,IAAI,CAAC3U,OAAO,IAAIpF,gBAAgB,CAAC+Z,IAAI,GAAG,CAAC,EAAE;MACzC3U,OAAO,GAAG+J,MAAM,CAAC3O,KAAK,CAAC+iB,IAAI,CAACvjB,gBAAgB,CAACwjB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxDziB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEoE,OAAO,CAAC;IAC3C;;IAEA;IACArE,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;IAC1BhB,gBAAgB,CAACyK,OAAO,CAAC,CAAC0X,KAAK,EAAExX,EAAE,KAAK;MAAA,IAAA8Y,mBAAA;MACtC1iB,OAAO,CAACC,GAAG,CAAC,KAAK2J,EAAE,KAAK,OAAOA,EAAE,MAAM,EAAA8Y,mBAAA,GAAAtB,KAAK,CAAC5Z,YAAY,cAAAkb,mBAAA,uBAAlBA,mBAAA,CAAoB/a,IAAI,KAAI,IAAI,EAAE,CAAC;IAC5E,CAAC,CAAC;;IAEF;IACA,IAAImG,YAAY,GAAG7O,gBAAgB,CAAC+J,GAAG,CAAC3E,OAAO,CAAC;IAChD,IAAI,CAACyJ,YAAY,EAAE;MACjB;MACA,MAAM6U,SAAS,GAAG3U,QAAQ,CAAC3J,OAAO,CAAC;MACnCyJ,YAAY,GAAG7O,gBAAgB,CAAC+J,GAAG,CAAC2Z,SAAS,CAAC;MAE9C,IAAI7U,YAAY,EAAE;QAChB9N,OAAO,CAACC,GAAG,CAAC,UAAU0iB,SAAS,SAAS,CAAC;QACzCte,OAAO,GAAGse,SAAS,CAAC,CAAC;MACvB;IACF;IAEA,IAAI,CAAC7U,YAAY,EAAE;MACjB9N,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEmE,OAAO,CAAC;MACtC,OAAO,KAAK;IACd;;IAEA;IACA,IAAIjG,MAAM,CAACoG,mBAAmB,EAAE;MAC9BpG,MAAM,CAACoG,mBAAmB,CAACoB,OAAO,GAAGvB,OAAO;IAC9C;;IAEA;IACA,IAAIjG,MAAM,CAACqG,0BAA0B,IAAIrG,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,EAAE;MAClFiR,aAAa,CAACzY,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,CAAC;MACxDxH,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,GAAG,IAAI;IAClD;;IAEA;IACA,MAAMgd,yBAAyB,GAAGA,CAAA,KAAM;MAAA,IAAAC,qBAAA;MACtC;MACA,IAAI,CAACzkB,MAAM,CAACsG,uBAAuB,EAAE;MAErC,MAAMoe,SAAS,IAAAD,qBAAA,GAAGzkB,MAAM,CAACoG,mBAAmB,cAAAqe,qBAAA,uBAA1BA,qBAAA,CAA4Bjd,OAAO;MACrD,IAAI,CAACkd,SAAS,EAAE;;MAEhB;MACA,IAAIxB,SAAS,GAAGpiB,kBAAkB,CAAC8J,GAAG,CAAC8Z,SAAS,CAAC;MACjD,IAAI,CAACxB,SAAS,IAAI,OAAOwB,SAAS,KAAK,QAAQ,EAAE;QAC/C;QACAxB,SAAS,GAAGpiB,kBAAkB,CAAC8J,GAAG,CAACgF,QAAQ,CAAC8U,SAAS,CAAC,CAAC;MACzD,CAAC,MAAM,IAAI,CAACxB,SAAS,IAAI,OAAOwB,SAAS,KAAK,QAAQ,EAAE;QACtD;QACAxB,SAAS,GAAGpiB,kBAAkB,CAAC8J,GAAG,CAACoF,MAAM,CAAC0U,SAAS,CAAC,CAAC;MACvD;MAEA,IAAI,CAACxB,SAAS,IAAI,CAACA,SAAS,CAAC/c,MAAM,IAAI+c,SAAS,CAAC/c,MAAM,CAAC+P,MAAM,KAAK,CAAC,EAAE;QACpEtU,OAAO,CAACC,GAAG,CAAC,QAAQ6iB,SAAS,cAAc,CAAC;QAC5C;MACF;;MAEA;MACA,MAAMC,iBAAiB,GAAG9jB,gBAAgB,CAAC+J,GAAG,CAAC8Z,SAAS,CAAC,KAC/B,OAAOA,SAAS,KAAK,QAAQ,GAAG7jB,gBAAgB,CAAC+J,GAAG,CAACgF,QAAQ,CAAC8U,SAAS,CAAC,CAAC,GACzE7jB,gBAAgB,CAAC+J,GAAG,CAACoF,MAAM,CAAC0U,SAAS,CAAC,CAAC,CAAC;MAElE,IAAI,CAACC,iBAAiB,EAAE;QACtB/iB,OAAO,CAACE,KAAK,CAAC,WAAW4iB,SAAS,gBAAgB,CAAC;QACnD;MACF;MAEA,MAAMtb,YAAY,GAAGub,iBAAiB,CAACvb,YAAY;;MAEnD;MACA,MAAMlD,OAAO,gBACXxH,OAAA;QAAKoc,KAAK,EAAE;UAAE3V,OAAO,EAAE,KAAK;UAAEsB,KAAK,EAAE,OAAO;UAAEgV,SAAS,EAAE,OAAO;UAAE0H,SAAS,EAAE;QAAO,CAAE;QAAAlN,QAAA,gBACpFvX,OAAA;UAAKoc,KAAK,EAAE;YACVjU,UAAU,EAAE,MAAM;YAClBuc,YAAY,EAAE,KAAK;YACnB5d,QAAQ,EAAE,MAAM;YAChB6d,YAAY,EAAE,gBAAgB;YAC9BC,aAAa,EAAE;UACjB,CAAE;UAAArN,QAAA,GACC,CAAA7M,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAACmb,SAAS,EAAC,GACjD;QAAA;UAAA3J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxc,OAAA;UAAAuX,QAAA,EACGiN,SAAS,CAAC/c,MAAM,CAACyG,GAAG,CAAC,CAACsC,KAAK,EAAE6S,KAAK,KAAK;YACtC,IAAIwB,UAAU;YACd,IAAIqB,SAAS;YAEb,QAAQ1V,KAAK,CAACQ,YAAY;cACxB,KAAK,GAAG;gBACN6T,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;cACF,KAAK,GAAG;gBACNrB,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;cACF,KAAK,GAAG;cACR;gBACErB,UAAU,GAAG,SAAS;gBACtBqB,SAAS,GAAG,IAAI;gBAChB;YACJ;YAEA,MAAMvV,SAAS,GAAGG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC;YAElD,oBACEzQ,OAAA;cAAiBoc,KAAK,EAAE;gBACtBsI,YAAY,EAAE,KAAK;gBACnBhe,eAAe,EAAE,uBAAuB;gBACxCD,OAAO,EAAE,KAAK;gBACdG,YAAY,EAAE,KAAK;gBACnBE,QAAQ,EAAE;cACZ,CAAE;cAAAyQ,QAAA,gBACAvX,OAAA;gBAAKoc,KAAK,EAAE;kBAAEjU,UAAU,EAAE;gBAAO,CAAE;gBAAAoP,QAAA,GAChC5G,SAAS,EAAC,oBAAQ,EAACH,KAAK,CAACC,OAAO,EAAC,GACpC;cAAA;gBAAA4L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNxc,OAAA;gBAAKoc,KAAK,EAAE;kBAAE9V,OAAO,EAAE,MAAM;kBAAEwe,cAAc,EAAE;gBAAgB,CAAE;gBAAAvN,QAAA,gBAC/DvX,OAAA;kBAAAuX,QAAA,EAAM;gBAAI;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBxc,OAAA;kBAAMoc,KAAK,EAAE;oBACXlU,KAAK,EAAE2c,UAAU;oBACjB1c,UAAU,EAAE,MAAM;oBAClBzB,eAAe,EAAE,iBAAiB;oBAClCD,OAAO,EAAE,OAAO;oBAChBG,YAAY,EAAE;kBAChB,CAAE;kBAAA2Q,QAAA,EACC2O;gBAAS;kBAAA7J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNxc,OAAA;gBAAKoc,KAAK,EAAE;kBAAE9V,OAAO,EAAE,MAAM;kBAAEwe,cAAc,EAAE;gBAAgB,CAAE;gBAAAvN,QAAA,gBAC/DvX,OAAA;kBAAAuX,QAAA,EAAM;gBAAK;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClBxc,OAAA;kBAAMoc,KAAK,EAAE;oBAAEjU,UAAU,EAAE;kBAAO,CAAE;kBAAAoP,QAAA,GAAE/G,KAAK,CAACS,UAAU,EAAC,SAAE;gBAAA;kBAAAoL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA,GAzBE6G,KAAK;cAAAhH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxc,OAAA;UAAKoc,KAAK,EAAE;YAAE2I,SAAS,EAAE,KAAK;YAAEje,QAAQ,EAAE,MAAM;YAAEoB,KAAK,EAAE;UAAO,CAAE;UAAAqP,QAAA,GAAC,4BAC3D,EAAC,IAAI5K,IAAI,CAAC6X,SAAS,CAAC1S,UAAU,CAAC,CAACkT,kBAAkB,CAAC,CAAC,EAAC,6BAC7D;QAAA;UAAA3I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;;MAED;MACAlb,MAAM,CAACsG,uBAAuB,CAAC6J,IAAI,KAAK;QACtC,GAAGA,IAAI;QACPjK,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAE+c,SAAS,CAAC/c;MACpB,CAAC,CAAC,CAAC;MAEHvE,OAAO,CAACC,GAAG,CAAC,SAAS,CAAAuH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAImb,SAAS,WAAW,CAAC;IAClE,CAAC;;IAED;IACA,MAAMG,gCAAgC,GAAGA,CAAA,KAAM;MAC7C;MACA,IAAI3B,SAAS,GAAGpiB,kBAAkB,CAAC8J,GAAG,CAAC3E,OAAO,CAAC;MAC/C,IAAI,CAACid,SAAS,EAAE;QACd;QACA,MAAMqB,SAAS,GAAG3U,QAAQ,CAAC3J,OAAO,CAAC;QACnCid,SAAS,GAAGpiB,kBAAkB,CAAC8J,GAAG,CAAC2Z,SAAS,CAAC;QAE7C,IAAIrB,SAAS,EAAE;UACbthB,OAAO,CAACC,GAAG,CAAC,UAAU0iB,SAAS,aAAa,CAAC;QAC/C;MACF;MAEA3iB,OAAO,CAACC,GAAG,CAAC,QAAQoE,OAAO,SAAS,EAAEid,SAAS,CAAC;MAEhD,MAAM9Z,YAAY,GAAGsG,YAAY,CAACtG,YAAY;MAC9CxH,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEuH,YAAY,CAAC;;MAElC;MACA,IAAIlD,OAAO;MAEX,IAAIgd,SAAS,IAAIA,SAAS,CAAC/c,MAAM,IAAI+c,SAAS,CAAC/c,MAAM,CAAC+P,MAAM,GAAG,CAAC,EAAE;QAChE;QACAtU,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;QACtBqhB,SAAS,CAAC/c,MAAM,CAACmF,OAAO,CAAC,CAAC4D,KAAK,EAAE6S,KAAK,KAAK;UACzCngB,OAAO,CAACC,GAAG,CAAC,MAAMkgB,KAAK,GAAC,CAAC,QAAQ7S,KAAK,CAACC,OAAO,QAAQD,KAAK,CAACQ,YAAY,UAAUR,KAAK,CAACS,UAAU,EAAE,CAAC;QACvG,CAAC,CAAC;QAEFzJ,OAAO,gBACLxH,OAAA;UAAKoc,KAAK,EAAE;YAAE3V,OAAO,EAAE,KAAK;YAAEsB,KAAK,EAAE,OAAO;YAAEgV,SAAS,EAAE,OAAO;YAAE0H,SAAS,EAAE;UAAO,CAAE;UAAAlN,QAAA,gBACpFvX,OAAA;YAAKoc,KAAK,EAAE;cACVjU,UAAU,EAAE,MAAM;cAClBuc,YAAY,EAAE,KAAK;cACnB5d,QAAQ,EAAE,MAAM;cAChB6d,YAAY,EAAE,gBAAgB;cAC9BC,aAAa,EAAE;YACjB,CAAE;YAAArN,QAAA,GACC,CAAA7M,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAACtD,OAAO,EAAC,GAC/C;UAAA;YAAA8U,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxc,OAAA;YAAAuX,QAAA,EACGiN,SAAS,CAAC/c,MAAM,CAACyG,GAAG,CAAC,CAACsC,KAAK,EAAE6S,KAAK,KAAK;cACtC,IAAIwB,UAAU;cACd,IAAIqB,SAAS;cAEb,QAAQ1V,KAAK,CAACQ,YAAY;gBACxB,KAAK,GAAG;kBACN6T,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;gBACF,KAAK,GAAG;kBACNrB,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;gBACF,KAAK,GAAG;gBACR;kBACErB,UAAU,GAAG,SAAS;kBACtBqB,SAAS,GAAG,IAAI;kBAChB;cACJ;cAEA,MAAMvV,SAAS,GAAGG,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC;cAElD,oBACEzQ,OAAA;gBAAiBoc,KAAK,EAAE;kBACtBsI,YAAY,EAAE,KAAK;kBACnBhe,eAAe,EAAE,uBAAuB;kBACxCD,OAAO,EAAE,KAAK;kBACdG,YAAY,EAAE,KAAK;kBACnBE,QAAQ,EAAE;gBACZ,CAAE;gBAAAyQ,QAAA,gBACAvX,OAAA;kBAAKoc,KAAK,EAAE;oBAAEjU,UAAU,EAAE;kBAAO,CAAE;kBAAAoP,QAAA,GAChC5G,SAAS,EAAC,oBAAQ,EAACH,KAAK,CAACC,OAAO,EAAC,GACpC;gBAAA;kBAAA4L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNxc,OAAA;kBAAKoc,KAAK,EAAE;oBAAE9V,OAAO,EAAE,MAAM;oBAAEwe,cAAc,EAAE;kBAAgB,CAAE;kBAAAvN,QAAA,gBAC/DvX,OAAA;oBAAAuX,QAAA,EAAM;kBAAI;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjBxc,OAAA;oBAAMoc,KAAK,EAAE;sBACXlU,KAAK,EAAE2c,UAAU;sBACjB1c,UAAU,EAAE,MAAM;sBAClBzB,eAAe,EAAE,iBAAiB;sBAClCD,OAAO,EAAE,OAAO;sBAChBG,YAAY,EAAE;oBAChB,CAAE;oBAAA2Q,QAAA,EACC2O;kBAAS;oBAAA7J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNxc,OAAA;kBAAKoc,KAAK,EAAE;oBAAE9V,OAAO,EAAE,MAAM;oBAAEwe,cAAc,EAAE;kBAAgB,CAAE;kBAAAvN,QAAA,gBAC/DvX,OAAA;oBAAAuX,QAAA,EAAM;kBAAK;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClBxc,OAAA;oBAAMoc,KAAK,EAAE;sBAAEjU,UAAU,EAAE;oBAAO,CAAE;oBAAAoP,QAAA,GAAE/G,KAAK,CAACS,UAAU,EAAC,SAAE;kBAAA;oBAAAoL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA,GAzBE6G,KAAK;gBAAAhH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BV,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNxc,OAAA;YAAKoc,KAAK,EAAE;cAAE2I,SAAS,EAAE,KAAK;cAAEje,QAAQ,EAAE,MAAM;cAAEoB,KAAK,EAAE;YAAO,CAAE;YAAAqP,QAAA,GAAC,4BAC3D,EAAC,IAAI5K,IAAI,CAAC6X,SAAS,CAAC1S,UAAU,CAAC,CAACkT,kBAAkB,CAAC,CAAC,EAAC,6BAC7D;UAAA;YAAA3I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MACH,CAAC,MAAM;QACL;QACAhV,OAAO,gBACLxH,OAAA;UAAKoc,KAAK,EAAE;YAAE3V,OAAO,EAAE,KAAK;YAAEsB,KAAK,EAAE;UAAQ,CAAE;UAAAwP,QAAA,gBAC7CvX,OAAA;YAAKoc,KAAK,EAAE;cACVjU,UAAU,EAAE,MAAM;cAClBuc,YAAY,EAAE,KAAK;cACnB5d,QAAQ,EAAE,MAAM;cAChB6d,YAAY,EAAE,gBAAgB;cAC9BC,aAAa,EAAE;YACjB,CAAE;YAAArN,QAAA,GACC,CAAA7M,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAI,MAAM,EAAC,QAAM,EAACtD,OAAO,EAAC,GAC/C;UAAA;YAAA8U,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxc,OAAA;YAAKoc,KAAK,EAAE;cAAElU,KAAK,EAAE,SAAS;cAAEpB,QAAQ,EAAE;YAAO,CAAE;YAAAyQ,QAAA,EAAC;UAEpD;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNxc,OAAA;YAAKoc,KAAK,EAAE;cAAE2I,SAAS,EAAE,KAAK;cAAEje,QAAQ,EAAE,MAAM;cAAEoB,KAAK,EAAE;YAAO,CAAE;YAAAqP,QAAA,GAAC,4BAC3D,EAAC,IAAI5K,IAAI,CAAC,CAAC,CAACqY,kBAAkB,CAAC,CAAC;UAAA;YAAA3I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MACH;;MAEA;MACA,MAAM3Y,CAAC,GAAGvC,MAAM,CAACyT,UAAU,GAAG,CAAC;MAC/B,MAAMhR,CAAC,GAAGzC,MAAM,CAAC0T,WAAW,GAAG,CAAC;;MAEhC;MACA,IAAI1T,MAAM,CAACsG,uBAAuB,EAAE;QAAA,IAAAwe,UAAA;QAClCljB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;UAC7CmE,OAAO,EAAE,IAAI;UACbC,OAAO;UACPtB,QAAQ,EAAE;YAAEpC,CAAC;YAAEE;UAAE;QACnB,CAAC,CAAC;QAEFzC,MAAM,CAACsG,uBAAuB,CAAC;UAC7BN,OAAO,EAAE,IAAI;UACbC,OAAO,EAAEA,OAAO;UAChBtB,QAAQ,EAAE;YAAEpC,CAAC;YAAEE;UAAE,CAAC;UAClByD,OAAO,EAAEA,OAAO;UAChBC,MAAM,EAAE,EAAA2e,UAAA,GAAA5B,SAAS,cAAA4B,UAAA,uBAATA,UAAA,CAAW3e,MAAM,KAAI;QAC/B,CAAC,CAAC;QAEFvE,OAAO,CAACC,GAAG,CAAC,SAAS,CAAAuH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,IAAI,KAAItD,OAAO,WAAW,CAAC;;QAE9D;QACA,IAAIjG,MAAM,CAACqG,0BAA0B,EAAE;UACrCrG,MAAM,CAACqG,0BAA0B,CAACmB,OAAO,GAAGyR,WAAW,CAACuL,yBAAyB,EAAErhB,6BAA6B,GAAG,IAAI,CAAC;UACxHvB,OAAO,CAACC,GAAG,CAAC,qBAAqBsB,6BAA6B,IAAI,CAAC;QACrE;QAEA,OAAO,IAAI;MACb,CAAC,MAAM;QACLvB,OAAO,CAACE,KAAK,CAAC,8BAA8B,CAAC;QAC7C,OAAO,KAAK;MACd;IACF,CAAC;IAED,OAAO+iB,gCAAgC,CAAC,CAAC;EAC3C,CAAC,CAAC,OAAO/iB,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,MAAMogB,yBAAyB,GAAIF,MAAM,IAAK;EAC5C,IAAIxa,OAAO,GAAGwa,MAAM;;EAEpB;EACA,IAAIxa,OAAO,IAAIA,OAAO,CAACuG,QAAQ,IAAIvG,OAAO,CAACuG,QAAQ,CAACrC,IAAI,KAAK,cAAc,EAAE;IAC3E9J,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE2F,OAAO,CAAC+B,IAAI,IAAI,KAAK,CAAC;IAChD,OAAO/B,OAAO;EAChB;;EAEA;EACA,OAAOA,OAAO,IAAIA,OAAO,CAACiY,MAAM,EAAE;IAChCjY,OAAO,GAAGA,OAAO,CAACiY,MAAM;IACxB,IAAIjY,OAAO,CAACuG,QAAQ,IAAIvG,OAAO,CAACuG,QAAQ,CAACrC,IAAI,KAAK,cAAc,EAAE;MAChE9J,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE2F,OAAO,CAAC+B,IAAI,IAAI,KAAK,CAAC;MAChD,OAAO/B,OAAO;IAChB;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACAxH,MAAM,CAAC+kB,kBAAkB,GAAG,CAACxiB,CAAC,EAAEE,CAAC,KAAK;EACpC,IAAI;IACFb,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEU,CAAC,EAAEE,CAAC,CAAC;;IAEnC;IACA,MAAM+Z,MAAM,GAAGC,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAI,CAAChC,MAAM,EAAE;MACX5a,OAAO,CAACE,KAAK,CAAC,sBAAsB,CAAC;MACrC,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAACpC,KAAK,IAAI,CAACiG,SAAS,CAAC6B,OAAO,EAAE;MAChC5F,OAAO,CAACE,KAAK,CAAC,iBAAiB,CAAC;MAChC,OAAO,KAAK;IACd;;IAEA;IACA,IAAIS,CAAC,KAAKmZ,SAAS,IAAIjZ,CAAC,KAAKiZ,SAAS,EAAE;MACtCnZ,CAAC,GAAGvC,MAAM,CAACyT,UAAU,GAAG,CAAC;MACzBhR,CAAC,GAAGzC,MAAM,CAAC0T,WAAW,GAAG,CAAC;IAC5B;;IAEA;IACA,MAAMmN,IAAI,GAAGrE,MAAM,CAACsE,qBAAqB,CAAC,CAAC;IAC3C,MAAMC,MAAM,GAAI,CAACxe,CAAC,GAAGse,IAAI,CAAChc,IAAI,IAAI2X,MAAM,CAACwE,WAAW,GAAI,CAAC,GAAG,CAAC;IAC7D,MAAMC,MAAM,GAAG,EAAE,CAACxe,CAAC,GAAGoe,IAAI,CAACra,GAAG,IAAIgW,MAAM,CAAC0E,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;IAE9Dtf,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEkf,MAAM,EAAEE,MAAM,CAAC;;IAErC;IACA,MAAME,SAAS,GAAG,IAAInjB,KAAK,CAACojB,SAAS,CAAC,CAAC;IACvCD,SAAS,CAAC/E,MAAM,CAACiF,MAAM,CAACC,SAAS,GAAG,CAAC;IACrCH,SAAS,CAAC/E,MAAM,CAACmF,IAAI,CAACD,SAAS,GAAG,CAAC;IAEnC,MAAME,WAAW,GAAG,IAAIxjB,KAAK,CAACyjB,OAAO,CAACV,MAAM,EAAEE,MAAM,CAAC;IACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,EAAE7b,SAAS,CAAC6B,OAAO,CAAC;;IAEvD;IACA,MAAMma,mBAAmB,GAAG,EAAE;IAC9B9gB,gBAAgB,CAACyK,OAAO,CAAC,CAACwN,QAAQ,EAAE7S,OAAO,KAAK;MAC9C,IAAI6S,QAAQ,CAAC3M,KAAK,EAAE;QAClBwV,mBAAmB,CAAC7R,IAAI,CAACgJ,QAAQ,CAAC3M,KAAK,CAAC;QACxCvK,OAAO,CAACC,GAAG,CAAC,SAASoE,OAAO,QAAQ,CAAC;MACvC;IACF,CAAC,CAAC;;IAEF;IACArE,OAAO,CAACC,GAAG,CAAC,QAAQ8f,mBAAmB,CAACzL,MAAM,YAAY,CAAC;IAC3D,MAAM8O,YAAY,GAAG7D,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,EAAE,IAAI,CAAC;IAE1E,IAAIqD,YAAY,CAAC9O,MAAM,GAAG,CAAC,EAAE;MAC3BtU,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAC1BmjB,YAAY,CAAC1Z,OAAO,CAAC,CAACwW,SAAS,EAAExY,CAAC,KAAK;QACrC1H,OAAO,CAACC,GAAG,CAAC,MAAMyH,CAAC,GAAG,EAAEwY,SAAS,CAACE,MAAM,CAACzY,IAAI,IAAI,KAAK,EAC1C,KAAK,EAAEuY,SAAS,CAAChN,QAAQ,EACzB,WAAW,EAAEgN,SAAS,CAACE,MAAM,CAACrd,QAAQ,CAACoF,OAAO,CAAC,CAAC,EAChD,WAAW,EAAE+X,SAAS,CAACE,MAAM,CAACjU,QAAQ,CAAC;;QAEnD;QACA,MAAMkU,GAAG,GAAGC,yBAAyB,CAACJ,SAAS,CAACE,MAAM,CAAC;QACvD,IAAIC,GAAG,IAAIA,GAAG,CAAClU,QAAQ,IAAIkU,GAAG,CAAClU,QAAQ,CAACrC,IAAI,KAAK,cAAc,EAAE;UAC/D9J,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEogB,GAAG,CAAClU,QAAQ,CAAC9H,OAAO,CAAC;QAC/C;MACF,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;;IAEA;IACArE,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,MAAMojB,eAAe,GAAG9D,SAAS,CAACU,gBAAgB,CAACniB,KAAK,CAACuW,QAAQ,EAAE,IAAI,CAAC;IAExErU,OAAO,CAACC,GAAG,CAAC,WAAWojB,eAAe,CAAC/O,MAAM,MAAM,CAAC;IACpD+O,eAAe,CAAC3Z,OAAO,CAAC,CAACwW,SAAS,EAAExY,CAAC,KAAK;MACxC,MAAM2Y,GAAG,GAAGH,SAAS,CAACE,MAAM;MAC5BpgB,OAAO,CAACC,GAAG,CAAC,QAAQyH,CAAC,GAAG,EAAE2Y,GAAG,CAAC1Y,IAAI,IAAI,KAAK,EAC/B,KAAK,EAAE0Y,GAAG,CAACvW,IAAI,EACf,KAAK,EAAEuW,GAAG,CAACtd,QAAQ,CAACoF,OAAO,CAAC,CAAC,EAC7B,KAAK,EAAE+X,SAAS,CAAChN,QAAQ,EACzB,WAAW,EAAEmN,GAAG,CAAClU,QAAQ,CAAC;IACxC,CAAC,CAAC;;IAEF;IACAnM,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,IAAIqjB,YAAY,GAAG,CAAC;IAEpBrkB,gBAAgB,CAACyK,OAAO,CAAC,CAACwN,QAAQ,EAAE7S,OAAO,KAAK;MAC9C,IAAI6S,QAAQ,CAAC3M,KAAK,EAAE;QAAA,IAAAgZ,qBAAA;QAClB;QACA,IAAIC,SAAS,GAAGtM,QAAQ,CAAC3M,KAAK,CAACnG,OAAO;QACtC,IAAIqf,cAAc,GAAG,IAAI;;QAEzB;QACA,MAAM/C,QAAQ,GAAG,IAAItkB,KAAK,CAACwP,OAAO,CAAC,CAAC;QACpCsL,QAAQ,CAAC3M,KAAK,CAACoW,gBAAgB,CAACD,QAAQ,CAAC;;QAEzC;QACA,MAAMgD,gBAAgB,GAAGhD,QAAQ,CAACiD,UAAU,CAAC5f,SAAS,CAAC6B,OAAO,CAAC7C,QAAQ,CAAC;;QAExE;QACA,MAAM6d,SAAS,GAAGF,QAAQ,CAACjgB,KAAK,CAAC,CAAC,CAACogB,OAAO,CAAC9c,SAAS,CAAC6B,OAAO,CAAC;QAC7D,IAAIxE,IAAI,CAACkV,GAAG,CAACsK,SAAS,CAACjgB,CAAC,CAAC,GAAG,CAAC,IAAIS,IAAI,CAACkV,GAAG,CAACsK,SAAS,CAAC/f,CAAC,CAAC,GAAG,CAAC,IAAI+f,SAAS,CAAC7f,CAAC,GAAG,CAAC,CAAC,IAAI6f,SAAS,CAAC7f,CAAC,GAAG,CAAC,EAAE;UACjG0iB,cAAc,GAAG,KAAK;QACxB;QAEA,IAAID,SAAS,EAAE;UACbF,YAAY,EAAE;QAChB;QAEAtjB,OAAO,CAACC,GAAG,CAAC,OAAOoE,OAAO,GAAG,EAAE;UAC7Buf,EAAE,EAAE,EAAAL,qBAAA,GAAArM,QAAQ,CAAC1P,YAAY,cAAA+b,qBAAA,uBAArBA,qBAAA,CAAuB5b,IAAI,KAAI,IAAI;UACvCkc,GAAG,EAAEL,SAAS;UACdM,KAAK,EAAEL,cAAc;UACrBM,IAAI,EAAErD,QAAQ,CAACvY,OAAO,CAAC,CAAC;UACxB6b,IAAI,EAAE,CAACpD,SAAS,CAACjgB,CAAC,EAAEigB,SAAS,CAAC/f,CAAC,EAAE+f,SAAS,CAAC7f,CAAC,CAAC;UAC7CkjB,MAAM,EAAEP;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF1jB,OAAO,CAACC,GAAG,CAAC,MAAMqjB,YAAY,IAAIrkB,gBAAgB,CAAC+Z,IAAI,WAAW,CAAC;;IAEnE;IACA,OAAOqK,eAAe,CAAC/O,MAAM,GAAG,CAAC;EACnC,CAAC,CAAC,OAAOpU,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IAC/B,OAAO,KAAK;EACd;AACF,CAAC;;AAID;AACA,MAAMoO,wBAAwB,GAAGA,CAACR,YAAY,EAAEG,SAAS,KAAK;EAAA,IAAAiW,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC5D,IAAI,CAACtW,YAAY,IAAI,CAACA,YAAY,CAACvD,KAAK,IAAI,CAAC0D,SAAS,EAAE;IACtD;EACF;;EAEA;EACA,MAAMoW,cAAc,GAAG,EAAE;EACzBvW,YAAY,CAACvD,KAAK,CAACwB,QAAQ,CAACC,KAAK,IAAI;IACnC,IAAIA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACmY,OAAO,EAAE;MAC5CD,cAAc,CAACnW,IAAI,CAAClC,KAAK,CAAC;IAC5B;EACF,CAAC,CAAC;EAEFqY,cAAc,CAAC3a,OAAO,CAAC0X,KAAK,IAAI;IAC9BtT,YAAY,CAACvD,KAAK,CAACa,MAAM,CAACgW,KAAK,CAAC;EAClC,CAAC,CAAC;;EAEF;EACA,IAAIO,UAAU;EACd,QAAO1T,SAAS,CAACH,YAAY;IAC3B,KAAK,GAAG;MACN6T,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;MACNA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;IACF,KAAK,GAAG;IACR;MACEA,UAAU,GAAG,QAAQ,CAAC,CAAC;MACvB;EACJ;;EAEA;EACA,MAAMlD,aAAa,GAAG,IAAIriB,KAAK,CAACqc,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;EACzD,MAAMiG,aAAa,GAAG,IAAItiB,KAAK,CAAC6b,iBAAiB,CAAC;IAChDjT,KAAK,EAAE2c,UAAU;IACjBtV,QAAQ,EAAEsV,UAAU;IACpB4C,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM5F,SAAS,GAAG,IAAIviB,KAAK,CAAC8b,IAAI,CAACuG,aAAa,EAAEC,aAAa,CAAC;EAC9DC,SAAS,CAAC5b,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAClC2d,SAAS,CAACxS,QAAQ,GAAG;IACnBmY,OAAO,EAAE,IAAI;IACbxa,IAAI,EAAE,cAAc;IACpBzF,OAAO,GAAA6f,qBAAA,GAAEpW,YAAY,CAACtG,YAAY,cAAA0c,qBAAA,uBAAzBA,qBAAA,CAA2B7f,OAAO;IAC3CkJ,OAAO,EAAEU,SAAS,CAACV,OAAO;IAC1BE,SAAS,EAAEQ,SAAS,CAACR,SAAS;IAC9BM,UAAU,EAAEE,SAAS,CAACF;EACxB,CAAC;;EAED;EACA,MAAMqT,KAAK,GAAG,IAAIhlB,KAAK,CAACooB,UAAU,CAAC7C,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;EACrDP,KAAK,CAACre,QAAQ,CAAC/B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAC5BogB,KAAK,CAACjV,QAAQ,GAAG;IAAEmY,OAAO,EAAE;EAAK,CAAC;;EAElC;EACAxW,YAAY,CAACvD,KAAK,CAACI,GAAG,CAACgU,SAAS,CAAC;EACjC7Q,YAAY,CAACvD,KAAK,CAACI,GAAG,CAACyW,KAAK,CAAC;EAE7BphB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAAkkB,sBAAA,GAAArW,YAAY,CAACtG,YAAY,cAAA2c,sBAAA,uBAAzBA,sBAAA,CAA2Bxc,IAAI,OAAAyc,sBAAA,GAAItW,YAAY,CAACtG,YAAY,cAAA4c,sBAAA,uBAAzBA,sBAAA,CAA2B/f,OAAO,cAAa4J,SAAS,CAACH,YAAY,WAAWG,SAAS,CAACF,UAAU,GAAG,CAAC;AACjK,CAAC;AAED,eAAevM,WAAW;AAAC,IAAA6Y,EAAA;AAAAoK,YAAA,CAAApK,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}