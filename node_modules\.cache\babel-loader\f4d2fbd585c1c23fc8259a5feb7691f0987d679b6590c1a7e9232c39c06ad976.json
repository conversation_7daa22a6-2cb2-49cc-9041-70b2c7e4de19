{"ast": null, "code": "export function init() {\n  //no-op for longlat\n}\nfunction identity(pt) {\n  return pt;\n}\nexport { identity as forward };\nexport { identity as inverse };\nexport var names = [\"longlat\", \"identity\"];\nexport default {\n  init: init,\n  forward: identity,\n  inverse: identity,\n  names: names\n};", "map": {"version": 3, "names": ["init", "identity", "pt", "forward", "inverse", "names"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/proj4/lib/projections/longlat.js"], "sourcesContent": ["export function init() {\n  //no-op for longlat\n}\n\nfunction identity(pt) {\n  return pt;\n}\nexport {identity as forward};\nexport {identity as inverse};\nexport var names = [\"longlat\", \"identity\"];\nexport default {\n  init: init,\n  forward: identity,\n  inverse: identity,\n  names: names\n};\n"], "mappings": "AAAA,OAAO,SAASA,IAAIA,CAAA,EAAG;EACrB;AAAA;AAGF,SAASC,QAAQA,CAACC,EAAE,EAAE;EACpB,OAAOA,EAAE;AACX;AACA,SAAQD,QAAQ,IAAIE,OAAO;AAC3B,SAAQF,QAAQ,IAAIG,OAAO;AAC3B,OAAO,IAAIC,KAAK,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC;AAC1C,eAAe;EACbL,IAAI,EAAEA,IAAI;EACVG,OAAO,EAAEF,QAAQ;EACjBG,OAAO,EAAEH,QAAQ;EACjBI,KAAK,EAAEA;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}