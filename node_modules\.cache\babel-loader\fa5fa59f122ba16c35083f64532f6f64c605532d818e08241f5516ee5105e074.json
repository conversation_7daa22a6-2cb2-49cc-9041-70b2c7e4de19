{"ast": null, "code": "import pj_mlfn from \"./pj_mlfn\";\nimport { EPSLN } from '../constants/values';\nvar MAX_ITER = 20;\nexport default function (arg, es, en) {\n  var k = 1 / (1 - es);\n  var phi = arg;\n  for (var i = MAX_ITER; i; --i) {\n    /* rarely goes over 2 iterations */\n    var s = Math.sin(phi);\n    var t = 1 - es * s * s;\n    //t = this.pj_mlfn(phi, s, Math.cos(phi), en) - arg;\n    //phi -= t * (t * Math.sqrt(t)) * k;\n    t = (pj_mlfn(phi, s, Math.cos(phi), en) - arg) * (t * Math.sqrt(t)) * k;\n    phi -= t;\n    if (Math.abs(t) < EPSLN) {\n      return phi;\n    }\n  }\n  //..reportError(\"cass:pj_inv_mlfn: Convergence error\");\n  return phi;\n}", "map": {"version": 3, "names": ["pj_mlfn", "EPSLN", "MAX_ITER", "arg", "es", "en", "k", "phi", "i", "s", "Math", "sin", "t", "cos", "sqrt", "abs"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/proj4/lib/common/pj_inv_mlfn.js"], "sourcesContent": ["import pj_mlfn from \"./pj_mlfn\";\nimport {EPSLN} from '../constants/values';\n\nvar MAX_ITER = 20;\n\nexport default function(arg, es, en) {\n  var k = 1 / (1 - es);\n  var phi = arg;\n  for (var i = MAX_ITER; i; --i) { /* rarely goes over 2 iterations */\n    var s = Math.sin(phi);\n    var t = 1 - es * s * s;\n    //t = this.pj_mlfn(phi, s, Math.cos(phi), en) - arg;\n    //phi -= t * (t * Math.sqrt(t)) * k;\n    t = (pj_mlfn(phi, s, Math.cos(phi), en) - arg) * (t * Math.sqrt(t)) * k;\n    phi -= t;\n    if (Math.abs(t) < EPSLN) {\n      return phi;\n    }\n  }\n  //..reportError(\"cass:pj_inv_mlfn: Convergence error\");\n  return phi;\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,WAAW;AAC/B,SAAQC,KAAK,QAAO,qBAAqB;AAEzC,IAAIC,QAAQ,GAAG,EAAE;AAEjB,eAAe,UAASC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACnC,IAAIC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAGF,EAAE,CAAC;EACpB,IAAIG,GAAG,GAAGJ,GAAG;EACb,KAAK,IAAIK,CAAC,GAAGN,QAAQ,EAAEM,CAAC,EAAE,EAAEA,CAAC,EAAE;IAAE;IAC/B,IAAIC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACJ,GAAG,CAAC;IACrB,IAAIK,CAAC,GAAG,CAAC,GAAGR,EAAE,GAAGK,CAAC,GAAGA,CAAC;IACtB;IACA;IACAG,CAAC,GAAG,CAACZ,OAAO,CAACO,GAAG,EAAEE,CAAC,EAAEC,IAAI,CAACG,GAAG,CAACN,GAAG,CAAC,EAAEF,EAAE,CAAC,GAAGF,GAAG,KAAKS,CAAC,GAAGF,IAAI,CAACI,IAAI,CAACF,CAAC,CAAC,CAAC,GAAGN,CAAC;IACvEC,GAAG,IAAIK,CAAC;IACR,IAAIF,IAAI,CAACK,GAAG,CAACH,CAAC,CAAC,GAAGX,KAAK,EAAE;MACvB,OAAOM,GAAG;IACZ;EACF;EACA;EACA,OAAOA,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}