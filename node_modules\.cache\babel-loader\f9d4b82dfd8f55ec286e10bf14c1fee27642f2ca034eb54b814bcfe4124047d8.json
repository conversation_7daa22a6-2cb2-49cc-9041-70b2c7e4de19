{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { getPtg } from './useSizes';\n/**\n * Handle user drag resize logic.\n */\nexport default function useResize(items, resizableInfos, percentSizes, containerSize, updateSizes, isRTL) {\n  const limitSizes = items.map(item => [item.min, item.max]);\n  const mergedContainerSize = containerSize || 0;\n  const ptg2px = ptg => ptg * mergedContainerSize;\n  // ======================== Resize ========================\n  function getLimitSize(str, defaultLimit) {\n    if (typeof str === 'string') {\n      return ptg2px(getPtg(str));\n    }\n    return str !== null && str !== void 0 ? str : defaultLimit;\n  }\n  // Real px sizes\n  const [cacheSizes, setCacheSizes] = React.useState([]);\n  const cacheCollapsedSize = React.useRef([]);\n  /**\n   * When start drag, check the direct is `start` or `end`.\n   * This will handle when 2 splitter bar are in the same position.\n   */\n  const [movingIndex, setMovingIndex] = React.useState(null);\n  const getPxSizes = () => percentSizes.map(ptg2px);\n  const onOffsetStart = index => {\n    setCacheSizes(getPxSizes());\n    setMovingIndex({\n      index,\n      confirmed: false\n    });\n  };\n  const onOffsetUpdate = (index, offset) => {\n    var _a;\n    // First time trigger move index update is not sync in the state\n    let confirmedIndex = null;\n    // We need to know what the real index is.\n    if ((!movingIndex || !movingIndex.confirmed) && offset !== 0) {\n      // Search for the real index\n      if (offset > 0) {\n        confirmedIndex = index;\n        setMovingIndex({\n          index,\n          confirmed: true\n        });\n      } else {\n        for (let i = index; i >= 0; i -= 1) {\n          if (cacheSizes[i] > 0 && resizableInfos[i].resizable) {\n            confirmedIndex = i;\n            setMovingIndex({\n              index: i,\n              confirmed: true\n            });\n            break;\n          }\n        }\n      }\n    }\n    const mergedIndex = (_a = confirmedIndex !== null && confirmedIndex !== void 0 ? confirmedIndex : movingIndex === null || movingIndex === void 0 ? void 0 : movingIndex.index) !== null && _a !== void 0 ? _a : index;\n    const numSizes = _toConsumableArray(cacheSizes);\n    const nextIndex = mergedIndex + 1;\n    // Get boundary\n    const startMinSize = getLimitSize(limitSizes[mergedIndex][0], 0);\n    const endMinSize = getLimitSize(limitSizes[nextIndex][0], 0);\n    const startMaxSize = getLimitSize(limitSizes[mergedIndex][1], mergedContainerSize);\n    const endMaxSize = getLimitSize(limitSizes[nextIndex][1], mergedContainerSize);\n    let mergedOffset = offset;\n    // Align with the boundary\n    if (numSizes[mergedIndex] + mergedOffset < startMinSize) {\n      mergedOffset = startMinSize - numSizes[mergedIndex];\n    }\n    if (numSizes[nextIndex] - mergedOffset < endMinSize) {\n      mergedOffset = numSizes[nextIndex] - endMinSize;\n    }\n    if (numSizes[mergedIndex] + mergedOffset > startMaxSize) {\n      mergedOffset = startMaxSize - numSizes[mergedIndex];\n    }\n    if (numSizes[nextIndex] - mergedOffset > endMaxSize) {\n      mergedOffset = numSizes[nextIndex] - endMaxSize;\n    }\n    // Do offset\n    numSizes[mergedIndex] += mergedOffset;\n    numSizes[nextIndex] -= mergedOffset;\n    updateSizes(numSizes);\n    return numSizes;\n  };\n  const onOffsetEnd = () => {\n    setMovingIndex(null);\n  };\n  // ======================= Collapse =======================\n  const onCollapse = (index, type) => {\n    const currentSizes = getPxSizes();\n    const adjustedType = isRTL ? type === 'start' ? 'end' : 'start' : type;\n    const currentIndex = adjustedType === 'start' ? index : index + 1;\n    const targetIndex = adjustedType === 'start' ? index + 1 : index;\n    const currentSize = currentSizes[currentIndex];\n    const targetSize = currentSizes[targetIndex];\n    if (currentSize !== 0 && targetSize !== 0) {\n      // Collapse directly\n      currentSizes[currentIndex] = 0;\n      currentSizes[targetIndex] += currentSize;\n      cacheCollapsedSize.current[index] = currentSize;\n    } else {\n      const totalSize = currentSize + targetSize;\n      const currentSizeMin = getLimitSize(limitSizes[currentIndex][0], 0);\n      const currentSizeMax = getLimitSize(limitSizes[currentIndex][1], mergedContainerSize);\n      const targetSizeMin = getLimitSize(limitSizes[targetIndex][0], 0);\n      const targetSizeMax = getLimitSize(limitSizes[targetIndex][1], mergedContainerSize);\n      const limitStart = Math.max(currentSizeMin, totalSize - targetSizeMax);\n      const limitEnd = Math.min(currentSizeMax, totalSize - targetSizeMin);\n      const halfOffset = (limitEnd - limitStart) / 2;\n      const targetCacheCollapsedSize = cacheCollapsedSize.current[index];\n      const currentCacheCollapsedSize = totalSize - targetCacheCollapsedSize;\n      const shouldUseCache = targetCacheCollapsedSize && targetCacheCollapsedSize <= targetSizeMax && targetCacheCollapsedSize >= targetSizeMin && currentCacheCollapsedSize <= currentSizeMax && currentCacheCollapsedSize >= currentSizeMin;\n      if (shouldUseCache) {\n        currentSizes[targetIndex] = targetCacheCollapsedSize;\n        currentSizes[currentIndex] = currentCacheCollapsedSize;\n      } else {\n        currentSizes[currentIndex] -= halfOffset;\n        currentSizes[targetIndex] += halfOffset;\n      }\n    }\n    updateSizes(currentSizes);\n    return currentSizes;\n  };\n  return [onOffsetStart, onOffsetUpdate, onOffsetEnd, onCollapse, movingIndex === null || movingIndex === void 0 ? void 0 : movingIndex.index];\n}", "map": {"version": 3, "names": ["_toConsumableArray", "React", "getPtg", "useResize", "items", "resizableInfos", "percentSizes", "containerSize", "updateSizes", "isRTL", "limitSizes", "map", "item", "min", "max", "mergedContainerSize", "ptg2px", "ptg", "getLimitSize", "str", "defaultLimit", "cacheSizes", "setCacheSizes", "useState", "cacheCollapsedSize", "useRef", "movingIndex", "setMovingIndex", "getPxSizes", "onOffsetStart", "index", "confirmed", "onOffsetUpdate", "offset", "_a", "confirmedIndex", "i", "resizable", "mergedIndex", "numSizes", "nextIndex", "startMinSize", "endMinSize", "startMaxSize", "endMaxSize", "mergedOffset", "onOffsetEnd", "onCollapse", "type", "currentSizes", "adjustedType", "currentIndex", "targetIndex", "currentSize", "targetSize", "current", "totalSize", "currentSizeMin", "currentSizeMax", "targetSizeMin", "targetSizeMax", "limitStart", "Math", "limitEnd", "halfOffset", "targetCacheCollapsedSize", "currentCacheCollapsedSize", "shouldUseCache"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/splitter/hooks/useResize.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { getPtg } from './useSizes';\n/**\n * Handle user drag resize logic.\n */\nexport default function useResize(items, resizableInfos, percentSizes, containerSize, updateSizes, isRTL) {\n  const limitSizes = items.map(item => [item.min, item.max]);\n  const mergedContainerSize = containerSize || 0;\n  const ptg2px = ptg => ptg * mergedContainerSize;\n  // ======================== Resize ========================\n  function getLimitSize(str, defaultLimit) {\n    if (typeof str === 'string') {\n      return ptg2px(getPtg(str));\n    }\n    return str !== null && str !== void 0 ? str : defaultLimit;\n  }\n  // Real px sizes\n  const [cacheSizes, setCacheSizes] = React.useState([]);\n  const cacheCollapsedSize = React.useRef([]);\n  /**\n   * When start drag, check the direct is `start` or `end`.\n   * This will handle when 2 splitter bar are in the same position.\n   */\n  const [movingIndex, setMovingIndex] = React.useState(null);\n  const getPxSizes = () => percentSizes.map(ptg2px);\n  const onOffsetStart = index => {\n    setCacheSizes(getPxSizes());\n    setMovingIndex({\n      index,\n      confirmed: false\n    });\n  };\n  const onOffsetUpdate = (index, offset) => {\n    var _a;\n    // First time trigger move index update is not sync in the state\n    let confirmedIndex = null;\n    // We need to know what the real index is.\n    if ((!movingIndex || !movingIndex.confirmed) && offset !== 0) {\n      // Search for the real index\n      if (offset > 0) {\n        confirmedIndex = index;\n        setMovingIndex({\n          index,\n          confirmed: true\n        });\n      } else {\n        for (let i = index; i >= 0; i -= 1) {\n          if (cacheSizes[i] > 0 && resizableInfos[i].resizable) {\n            confirmedIndex = i;\n            setMovingIndex({\n              index: i,\n              confirmed: true\n            });\n            break;\n          }\n        }\n      }\n    }\n    const mergedIndex = (_a = confirmedIndex !== null && confirmedIndex !== void 0 ? confirmedIndex : movingIndex === null || movingIndex === void 0 ? void 0 : movingIndex.index) !== null && _a !== void 0 ? _a : index;\n    const numSizes = _toConsumableArray(cacheSizes);\n    const nextIndex = mergedIndex + 1;\n    // Get boundary\n    const startMinSize = getLimitSize(limitSizes[mergedIndex][0], 0);\n    const endMinSize = getLimitSize(limitSizes[nextIndex][0], 0);\n    const startMaxSize = getLimitSize(limitSizes[mergedIndex][1], mergedContainerSize);\n    const endMaxSize = getLimitSize(limitSizes[nextIndex][1], mergedContainerSize);\n    let mergedOffset = offset;\n    // Align with the boundary\n    if (numSizes[mergedIndex] + mergedOffset < startMinSize) {\n      mergedOffset = startMinSize - numSizes[mergedIndex];\n    }\n    if (numSizes[nextIndex] - mergedOffset < endMinSize) {\n      mergedOffset = numSizes[nextIndex] - endMinSize;\n    }\n    if (numSizes[mergedIndex] + mergedOffset > startMaxSize) {\n      mergedOffset = startMaxSize - numSizes[mergedIndex];\n    }\n    if (numSizes[nextIndex] - mergedOffset > endMaxSize) {\n      mergedOffset = numSizes[nextIndex] - endMaxSize;\n    }\n    // Do offset\n    numSizes[mergedIndex] += mergedOffset;\n    numSizes[nextIndex] -= mergedOffset;\n    updateSizes(numSizes);\n    return numSizes;\n  };\n  const onOffsetEnd = () => {\n    setMovingIndex(null);\n  };\n  // ======================= Collapse =======================\n  const onCollapse = (index, type) => {\n    const currentSizes = getPxSizes();\n    const adjustedType = isRTL ? type === 'start' ? 'end' : 'start' : type;\n    const currentIndex = adjustedType === 'start' ? index : index + 1;\n    const targetIndex = adjustedType === 'start' ? index + 1 : index;\n    const currentSize = currentSizes[currentIndex];\n    const targetSize = currentSizes[targetIndex];\n    if (currentSize !== 0 && targetSize !== 0) {\n      // Collapse directly\n      currentSizes[currentIndex] = 0;\n      currentSizes[targetIndex] += currentSize;\n      cacheCollapsedSize.current[index] = currentSize;\n    } else {\n      const totalSize = currentSize + targetSize;\n      const currentSizeMin = getLimitSize(limitSizes[currentIndex][0], 0);\n      const currentSizeMax = getLimitSize(limitSizes[currentIndex][1], mergedContainerSize);\n      const targetSizeMin = getLimitSize(limitSizes[targetIndex][0], 0);\n      const targetSizeMax = getLimitSize(limitSizes[targetIndex][1], mergedContainerSize);\n      const limitStart = Math.max(currentSizeMin, totalSize - targetSizeMax);\n      const limitEnd = Math.min(currentSizeMax, totalSize - targetSizeMin);\n      const halfOffset = (limitEnd - limitStart) / 2;\n      const targetCacheCollapsedSize = cacheCollapsedSize.current[index];\n      const currentCacheCollapsedSize = totalSize - targetCacheCollapsedSize;\n      const shouldUseCache = targetCacheCollapsedSize && targetCacheCollapsedSize <= targetSizeMax && targetCacheCollapsedSize >= targetSizeMin && currentCacheCollapsedSize <= currentSizeMax && currentCacheCollapsedSize >= currentSizeMin;\n      if (shouldUseCache) {\n        currentSizes[targetIndex] = targetCacheCollapsedSize;\n        currentSizes[currentIndex] = currentCacheCollapsedSize;\n      } else {\n        currentSizes[currentIndex] -= halfOffset;\n        currentSizes[targetIndex] += halfOffset;\n      }\n    }\n    updateSizes(currentSizes);\n    return currentSizes;\n  };\n  return [onOffsetStart, onOffsetUpdate, onOffsetEnd, onCollapse, movingIndex === null || movingIndex === void 0 ? void 0 : movingIndex.index];\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,YAAY;AACnC;AACA;AACA;AACA,eAAe,SAASC,SAASA,CAACC,KAAK,EAAEC,cAAc,EAAEC,YAAY,EAAEC,aAAa,EAAEC,WAAW,EAAEC,KAAK,EAAE;EACxG,MAAMC,UAAU,GAAGN,KAAK,CAACO,GAAG,CAACC,IAAI,IAAI,CAACA,IAAI,CAACC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC;EAC1D,MAAMC,mBAAmB,GAAGR,aAAa,IAAI,CAAC;EAC9C,MAAMS,MAAM,GAAGC,GAAG,IAAIA,GAAG,GAAGF,mBAAmB;EAC/C;EACA,SAASG,YAAYA,CAACC,GAAG,EAAEC,YAAY,EAAE;IACvC,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;MAC3B,OAAOH,MAAM,CAACd,MAAM,CAACiB,GAAG,CAAC,CAAC;IAC5B;IACA,OAAOA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAGC,YAAY;EAC5D;EACA;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrB,KAAK,CAACsB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAMC,kBAAkB,GAAGvB,KAAK,CAACwB,MAAM,CAAC,EAAE,CAAC;EAC3C;AACF;AACA;AACA;EACE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,KAAK,CAACsB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAMK,UAAU,GAAGA,CAAA,KAAMtB,YAAY,CAACK,GAAG,CAACK,MAAM,CAAC;EACjD,MAAMa,aAAa,GAAGC,KAAK,IAAI;IAC7BR,aAAa,CAACM,UAAU,CAAC,CAAC,CAAC;IAC3BD,cAAc,CAAC;MACbG,KAAK;MACLC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,cAAc,GAAGA,CAACF,KAAK,EAAEG,MAAM,KAAK;IACxC,IAAIC,EAAE;IACN;IACA,IAAIC,cAAc,GAAG,IAAI;IACzB;IACA,IAAI,CAAC,CAACT,WAAW,IAAI,CAACA,WAAW,CAACK,SAAS,KAAKE,MAAM,KAAK,CAAC,EAAE;MAC5D;MACA,IAAIA,MAAM,GAAG,CAAC,EAAE;QACdE,cAAc,GAAGL,KAAK;QACtBH,cAAc,CAAC;UACbG,KAAK;UACLC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,KAAK,IAAIK,CAAC,GAAGN,KAAK,EAAEM,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;UAClC,IAAIf,UAAU,CAACe,CAAC,CAAC,GAAG,CAAC,IAAI/B,cAAc,CAAC+B,CAAC,CAAC,CAACC,SAAS,EAAE;YACpDF,cAAc,GAAGC,CAAC;YAClBT,cAAc,CAAC;cACbG,KAAK,EAAEM,CAAC;cACRL,SAAS,EAAE;YACb,CAAC,CAAC;YACF;UACF;QACF;MACF;IACF;IACA,MAAMO,WAAW,GAAG,CAACJ,EAAE,GAAGC,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGT,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACI,KAAK,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGJ,KAAK;IACrN,MAAMS,QAAQ,GAAGvC,kBAAkB,CAACqB,UAAU,CAAC;IAC/C,MAAMmB,SAAS,GAAGF,WAAW,GAAG,CAAC;IACjC;IACA,MAAMG,YAAY,GAAGvB,YAAY,CAACR,UAAU,CAAC4B,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChE,MAAMI,UAAU,GAAGxB,YAAY,CAACR,UAAU,CAAC8B,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5D,MAAMG,YAAY,GAAGzB,YAAY,CAACR,UAAU,CAAC4B,WAAW,CAAC,CAAC,CAAC,CAAC,EAAEvB,mBAAmB,CAAC;IAClF,MAAM6B,UAAU,GAAG1B,YAAY,CAACR,UAAU,CAAC8B,SAAS,CAAC,CAAC,CAAC,CAAC,EAAEzB,mBAAmB,CAAC;IAC9E,IAAI8B,YAAY,GAAGZ,MAAM;IACzB;IACA,IAAIM,QAAQ,CAACD,WAAW,CAAC,GAAGO,YAAY,GAAGJ,YAAY,EAAE;MACvDI,YAAY,GAAGJ,YAAY,GAAGF,QAAQ,CAACD,WAAW,CAAC;IACrD;IACA,IAAIC,QAAQ,CAACC,SAAS,CAAC,GAAGK,YAAY,GAAGH,UAAU,EAAE;MACnDG,YAAY,GAAGN,QAAQ,CAACC,SAAS,CAAC,GAAGE,UAAU;IACjD;IACA,IAAIH,QAAQ,CAACD,WAAW,CAAC,GAAGO,YAAY,GAAGF,YAAY,EAAE;MACvDE,YAAY,GAAGF,YAAY,GAAGJ,QAAQ,CAACD,WAAW,CAAC;IACrD;IACA,IAAIC,QAAQ,CAACC,SAAS,CAAC,GAAGK,YAAY,GAAGD,UAAU,EAAE;MACnDC,YAAY,GAAGN,QAAQ,CAACC,SAAS,CAAC,GAAGI,UAAU;IACjD;IACA;IACAL,QAAQ,CAACD,WAAW,CAAC,IAAIO,YAAY;IACrCN,QAAQ,CAACC,SAAS,CAAC,IAAIK,YAAY;IACnCrC,WAAW,CAAC+B,QAAQ,CAAC;IACrB,OAAOA,QAAQ;EACjB,CAAC;EACD,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACxBnB,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EACD;EACA,MAAMoB,UAAU,GAAGA,CAACjB,KAAK,EAAEkB,IAAI,KAAK;IAClC,MAAMC,YAAY,GAAGrB,UAAU,CAAC,CAAC;IACjC,MAAMsB,YAAY,GAAGzC,KAAK,GAAGuC,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,OAAO,GAAGA,IAAI;IACtE,MAAMG,YAAY,GAAGD,YAAY,KAAK,OAAO,GAAGpB,KAAK,GAAGA,KAAK,GAAG,CAAC;IACjE,MAAMsB,WAAW,GAAGF,YAAY,KAAK,OAAO,GAAGpB,KAAK,GAAG,CAAC,GAAGA,KAAK;IAChE,MAAMuB,WAAW,GAAGJ,YAAY,CAACE,YAAY,CAAC;IAC9C,MAAMG,UAAU,GAAGL,YAAY,CAACG,WAAW,CAAC;IAC5C,IAAIC,WAAW,KAAK,CAAC,IAAIC,UAAU,KAAK,CAAC,EAAE;MACzC;MACAL,YAAY,CAACE,YAAY,CAAC,GAAG,CAAC;MAC9BF,YAAY,CAACG,WAAW,CAAC,IAAIC,WAAW;MACxC7B,kBAAkB,CAAC+B,OAAO,CAACzB,KAAK,CAAC,GAAGuB,WAAW;IACjD,CAAC,MAAM;MACL,MAAMG,SAAS,GAAGH,WAAW,GAAGC,UAAU;MAC1C,MAAMG,cAAc,GAAGvC,YAAY,CAACR,UAAU,CAACyC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACnE,MAAMO,cAAc,GAAGxC,YAAY,CAACR,UAAU,CAACyC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAEpC,mBAAmB,CAAC;MACrF,MAAM4C,aAAa,GAAGzC,YAAY,CAACR,UAAU,CAAC0C,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjE,MAAMQ,aAAa,GAAG1C,YAAY,CAACR,UAAU,CAAC0C,WAAW,CAAC,CAAC,CAAC,CAAC,EAAErC,mBAAmB,CAAC;MACnF,MAAM8C,UAAU,GAAGC,IAAI,CAAChD,GAAG,CAAC2C,cAAc,EAAED,SAAS,GAAGI,aAAa,CAAC;MACtE,MAAMG,QAAQ,GAAGD,IAAI,CAACjD,GAAG,CAAC6C,cAAc,EAAEF,SAAS,GAAGG,aAAa,CAAC;MACpE,MAAMK,UAAU,GAAG,CAACD,QAAQ,GAAGF,UAAU,IAAI,CAAC;MAC9C,MAAMI,wBAAwB,GAAGzC,kBAAkB,CAAC+B,OAAO,CAACzB,KAAK,CAAC;MAClE,MAAMoC,yBAAyB,GAAGV,SAAS,GAAGS,wBAAwB;MACtE,MAAME,cAAc,GAAGF,wBAAwB,IAAIA,wBAAwB,IAAIL,aAAa,IAAIK,wBAAwB,IAAIN,aAAa,IAAIO,yBAAyB,IAAIR,cAAc,IAAIQ,yBAAyB,IAAIT,cAAc;MACvO,IAAIU,cAAc,EAAE;QAClBlB,YAAY,CAACG,WAAW,CAAC,GAAGa,wBAAwB;QACpDhB,YAAY,CAACE,YAAY,CAAC,GAAGe,yBAAyB;MACxD,CAAC,MAAM;QACLjB,YAAY,CAACE,YAAY,CAAC,IAAIa,UAAU;QACxCf,YAAY,CAACG,WAAW,CAAC,IAAIY,UAAU;MACzC;IACF;IACAxD,WAAW,CAACyC,YAAY,CAAC;IACzB,OAAOA,YAAY;EACrB,CAAC;EACD,OAAO,CAACpB,aAAa,EAAEG,cAAc,EAAEc,WAAW,EAAEC,UAAU,EAAErB,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACI,KAAK,CAAC;AAC9I", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}