{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { EXPAND_COLUMN, Summary } from 'rc-table';\nimport Column from './Column';\nimport ColumnGroup from './ColumnGroup';\nimport { SELECTION_ALL, SELECTION_COLUMN, SELECTION_INVERT, SELECTION_NONE } from './hooks/useSelection';\nimport InternalTable from './InternalTable';\nconst Table = (props, ref) => {\n  const renderTimesRef = React.useRef(0);\n  renderTimesRef.current += 1;\n  return /*#__PURE__*/React.createElement(InternalTable, Object.assign({}, props, {\n    ref: ref,\n    _renderTimes: renderTimesRef.current\n  }));\n};\nconst ForwardTable = /*#__PURE__*/React.forwardRef(Table);\nForwardTable.SELECTION_COLUMN = SELECTION_COLUMN;\nForwardTable.EXPAND_COLUMN = EXPAND_COLUMN;\nForwardTable.SELECTION_ALL = SELECTION_ALL;\nForwardTable.SELECTION_INVERT = SELECTION_INVERT;\nForwardTable.SELECTION_NONE = SELECTION_NONE;\nForwardTable.Column = Column;\nForwardTable.ColumnGroup = ColumnGroup;\nForwardTable.Summary = Summary;\nif (process.env.NODE_ENV !== 'production') {\n  ForwardTable.displayName = 'Table';\n}\nexport default ForwardTable;", "map": {"version": 3, "names": ["React", "EXPAND_COLUMN", "Summary", "Column", "ColumnGroup", "SELECTION_ALL", "SELECTION_COLUMN", "SELECTION_INVERT", "SELECTION_NONE", "InternalTable", "Table", "props", "ref", "renderTimesRef", "useRef", "current", "createElement", "Object", "assign", "_renderTimes", "ForwardTable", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/table/Table.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { EXPAND_COLUMN, Summary } from 'rc-table';\nimport Column from './Column';\nimport ColumnGroup from './ColumnGroup';\nimport { SELECTION_ALL, SELECTION_COLUMN, SELECTION_INVERT, SELECTION_NONE } from './hooks/useSelection';\nimport InternalTable from './InternalTable';\nconst Table = (props, ref) => {\n  const renderTimesRef = React.useRef(0);\n  renderTimesRef.current += 1;\n  return /*#__PURE__*/React.createElement(InternalTable, Object.assign({}, props, {\n    ref: ref,\n    _renderTimes: renderTimesRef.current\n  }));\n};\nconst ForwardTable = /*#__PURE__*/React.forwardRef(Table);\nForwardTable.SELECTION_COLUMN = SELECTION_COLUMN;\nForwardTable.EXPAND_COLUMN = EXPAND_COLUMN;\nForwardTable.SELECTION_ALL = SELECTION_ALL;\nForwardTable.SELECTION_INVERT = SELECTION_INVERT;\nForwardTable.SELECTION_NONE = SELECTION_NONE;\nForwardTable.Column = Column;\nForwardTable.ColumnGroup = ColumnGroup;\nForwardTable.Summary = Summary;\nif (process.env.NODE_ENV !== 'production') {\n  ForwardTable.displayName = 'Table';\n}\nexport default ForwardTable;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,EAAEC,OAAO,QAAQ,UAAU;AACjD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,sBAAsB;AACxG,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,MAAMC,KAAK,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC5B,MAAMC,cAAc,GAAGb,KAAK,CAACc,MAAM,CAAC,CAAC,CAAC;EACtCD,cAAc,CAACE,OAAO,IAAI,CAAC;EAC3B,OAAO,aAAaf,KAAK,CAACgB,aAAa,CAACP,aAAa,EAAEQ,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,KAAK,EAAE;IAC9EC,GAAG,EAAEA,GAAG;IACRO,YAAY,EAAEN,cAAc,CAACE;EAC/B,CAAC,CAAC,CAAC;AACL,CAAC;AACD,MAAMK,YAAY,GAAG,aAAapB,KAAK,CAACqB,UAAU,CAACX,KAAK,CAAC;AACzDU,YAAY,CAACd,gBAAgB,GAAGA,gBAAgB;AAChDc,YAAY,CAACnB,aAAa,GAAGA,aAAa;AAC1CmB,YAAY,CAACf,aAAa,GAAGA,aAAa;AAC1Ce,YAAY,CAACb,gBAAgB,GAAGA,gBAAgB;AAChDa,YAAY,CAACZ,cAAc,GAAGA,cAAc;AAC5CY,YAAY,CAACjB,MAAM,GAAGA,MAAM;AAC5BiB,YAAY,CAAChB,WAAW,GAAGA,WAAW;AACtCgB,YAAY,CAAClB,OAAO,GAAGA,OAAO;AAC9B,IAAIoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,YAAY,CAACK,WAAW,GAAG,OAAO;AACpC;AACA,eAAeL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}