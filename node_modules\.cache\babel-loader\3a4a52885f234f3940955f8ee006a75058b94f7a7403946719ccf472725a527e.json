{"ast": null, "code": "// This icon file is generated automatically.\nvar CheckSquareTwoTone = {\n  \"icon\": function render(primaryColor, secondaryColor) {\n    return {\n      \"tag\": \"svg\",\n      \"attrs\": {\n        \"viewBox\": \"64 64 896 896\",\n        \"focusable\": \"false\"\n      },\n      \"children\": [{\n        \"tag\": \"path\",\n        \"attrs\": {\n          \"d\": \"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z\",\n          \"fill\": primaryColor\n        }\n      }, {\n        \"tag\": \"path\",\n        \"attrs\": {\n          \"d\": \"M184 840h656V184H184v656zm130-367.8h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H688c6.5 0 10.3 7.4 6.5 12.7l-210.6 292a31.8 31.8 0 01-51.7 0L307.5 484.9c-3.8-5.3 0-12.7 6.5-12.7z\",\n          \"fill\": secondaryColor\n        }\n      }, {\n        \"tag\": \"path\",\n        \"attrs\": {\n          \"d\": \"M432.2 657.7a31.8 31.8 0 0051.7 0l210.6-292c3.8-5.3 0-12.7-6.5-12.7h-46.9c-10.3 0-19.9 5-25.9 13.3L458 584.3l-71.2-98.8c-6-8.4-15.7-13.3-25.9-13.3H314c-6.5 0-10.3 7.4-6.5 12.7l124.7 172.8z\",\n          \"fill\": primaryColor\n        }\n      }]\n    };\n  },\n  \"name\": \"check-square\",\n  \"theme\": \"twotone\"\n};\nexport default CheckSquareTwoTone;", "map": {"version": 3, "names": ["CheckSquareTwoTone", "render", "primaryColor", "secondaryColor"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@ant-design/icons-svg/es/asn/CheckSquareTwoTone.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar CheckSquareTwoTone = { \"icon\": function render(primaryColor, secondaryColor) { return { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z\", \"fill\": primaryColor } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M184 840h656V184H184v656zm130-367.8h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H688c6.5 0 10.3 7.4 6.5 12.7l-210.6 292a31.8 31.8 0 01-51.7 0L307.5 484.9c-3.8-5.3 0-12.7 6.5-12.7z\", \"fill\": secondaryColor } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M432.2 657.7a31.8 31.8 0 0051.7 0l210.6-292c3.8-5.3 0-12.7-6.5-12.7h-46.9c-10.3 0-19.9 5-25.9 13.3L458 584.3l-71.2-98.8c-6-8.4-15.7-13.3-25.9-13.3H314c-6.5 0-10.3 7.4-6.5 12.7l124.7 172.8z\", \"fill\": primaryColor } }] }; }, \"name\": \"check-square\", \"theme\": \"twotone\" };\nexport default CheckSquareTwoTone;\n"], "mappings": "AAAA;AACA,IAAIA,kBAAkB,GAAG;EAAE,MAAM,EAAE,SAASC,MAAMA,CAACC,YAAY,EAAEC,cAAc,EAAE;IAAE,OAAO;MAAE,KAAK,EAAE,KAAK;MAAE,OAAO,EAAE;QAAE,SAAS,EAAE,eAAe;QAAE,WAAW,EAAE;MAAQ,CAAC;MAAE,UAAU,EAAE,CAAC;QAAE,KAAK,EAAE,MAAM;QAAE,OAAO,EAAE;UAAE,GAAG,EAAE,yIAAyI;UAAE,MAAM,EAAED;QAAa;MAAE,CAAC,EAAE;QAAE,KAAK,EAAE,MAAM;QAAE,OAAO,EAAE;UAAE,GAAG,EAAE,qNAAqN;UAAE,MAAM,EAAEC;QAAe;MAAE,CAAC,EAAE;QAAE,KAAK,EAAE,MAAM;QAAE,OAAO,EAAE;UAAE,GAAG,EAAE,8LAA8L;UAAE,MAAM,EAAED;QAAa;MAAE,CAAC;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,cAAc;EAAE,OAAO,EAAE;AAAU,CAAC;AAC77B,eAAeF,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}