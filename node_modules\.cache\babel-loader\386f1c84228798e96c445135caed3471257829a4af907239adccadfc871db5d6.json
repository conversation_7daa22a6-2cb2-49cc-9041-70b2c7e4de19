{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\n// import React from 'react';\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/lib/locale/zh_CN';\n\n// 导入页面组件\nimport Login from './pages/Login';\nimport MainLayout from './components/layout/MainLayout';\nimport RealTimeTraffic from './pages/RealTimeTraffic';\nimport DeviceStatus from './pages/DeviceStatus';\nimport RoadMonitoring from './pages/RoadMonitoring';\nimport SystemManagement from './pages/SystemManagement';\n\n// 导入样式\nimport './App.css';\n\n// 改进的身份验证检查，增加权限检查\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst isAuthenticated = () => {\n  const user = localStorage.getItem('user');\n  console.log(\"user\", JSON.stringify(user));\n  return !!user;\n};\n\n// 检查用户是否有访问系统管理的权限\nconst hasSystemManagementAccess = () => {\n  try {\n    var _user$user;\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\n    // 只有管理员可以访问系统管理页面\n    const userRole = user.role || ((_user$user = user.user) === null || _user$user === void 0 ? void 0 : _user$user.role) || 'user';\n    console.log('系统管理权限检查:', {\n      用户数据: user,\n      角色: userRole,\n      是否有权限: userRole === 'admin'\n    });\n    return userRole === 'admin';\n  } catch (e) {\n    console.error('检查管理权限失败:', e);\n    return false;\n  }\n};\nfunction App() {\n  _s();\n  // 状态：跟踪用户是否已认证\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // 监听认证状态变化\n  useEffect(() => {\n    const checkAuth = () => {\n      const user = localStorage.getItem('user');\n      setIsAuthenticated(!!user); // 关键：触发状态更新\n    };\n\n    // 初始检查\n    checkAuth();\n\n    // 监听跨标签页的 localStorage 变化\n    window.addEventListener('storage', checkAuth);\n    return () => {\n      window.removeEventListener('storage', checkAuth);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    locale: zhCN,\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: isAuthenticated() ? /*#__PURE__*/_jsxDEV(MainLayout, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 42\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 59\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            index: true,\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/real-time-traffic\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 35\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"real-time-traffic\",\n            element: /*#__PURE__*/_jsxDEV(RealTimeTraffic, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 54\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"device-status\",\n            element: /*#__PURE__*/_jsxDEV(DeviceStatus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"road-monitoring\",\n            element: /*#__PURE__*/_jsxDEV(RoadMonitoring, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"system-management\",\n            element: /*#__PURE__*/_jsxDEV(SystemManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"JkS3Meyzlj18m4l86SBr9YDqEkQ=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "useNavigate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "<PERSON><PERSON>", "MainLayout", "RealTimeTraffic", "DeviceStatus", "RoadMonitoring", "SystemManagement", "jsxDEV", "_jsxDEV", "isAuthenticated", "user", "localStorage", "getItem", "console", "log", "JSON", "stringify", "hasSystemManagementAccess", "_user$user", "parse", "userRole", "role", "用户数据", "角色", "是否有权限", "e", "error", "App", "_s", "setIsAuthenticated", "checkAuth", "window", "addEventListener", "removeEventListener", "locale", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "index", "_c", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/App.js"], "sourcesContent": ["// import React from 'react';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\r\nimport { ConfigProvider } from 'antd';\r\nimport zhCN from 'antd/lib/locale/zh_CN';\r\n\r\n// 导入页面组件\r\nimport Login from './pages/Login';\r\nimport MainLayout from './components/layout/MainLayout';\r\nimport RealTimeTraffic from './pages/RealTimeTraffic';\r\nimport DeviceStatus from './pages/DeviceStatus';\r\nimport RoadMonitoring from './pages/RoadMonitoring';\r\nimport SystemManagement from './pages/SystemManagement';\r\n\r\n// 导入样式\r\nimport './App.css';\r\n\r\n// 改进的身份验证检查，增加权限检查\r\nconst isAuthenticated = () => {\r\n  const user = localStorage.getItem('user');\r\n  console.log(\"user\", JSON.stringify(user)); \r\n  return !!user;\r\n};\r\n\r\n// 检查用户是否有访问系统管理的权限\r\nconst hasSystemManagementAccess = () => {\r\n  try {\r\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\r\n    // 只有管理员可以访问系统管理页面\r\n    const userRole = user.role || user.user?.role || 'user';\r\n    console.log('系统管理权限检查:', {\r\n      用户数据: user,\r\n      角色: userRole,\r\n      是否有权限: userRole === 'admin'\r\n    });\r\n    return userRole === 'admin';\r\n  } catch (e) {\r\n    console.error('检查管理权限失败:', e);\r\n    return false;\r\n  }\r\n};\r\n\r\nfunction App() {\r\n  // 状态：跟踪用户是否已认证\r\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\r\n\r\n  // 监听认证状态变化\r\n  useEffect(() => {\r\n    const checkAuth = () => {\r\n      const user = localStorage.getItem('user');\r\n      setIsAuthenticated(!!user); // 关键：触发状态更新\r\n    };\r\n\r\n    // 初始检查\r\n    checkAuth();\r\n\r\n    // 监听跨标签页的 localStorage 变化\r\n    window.addEventListener('storage', checkAuth);\r\n\r\n    return () => {\r\n      window.removeEventListener('storage', checkAuth);\r\n    };\r\n  }, []);\r\n  \r\n  return (\r\n    <ConfigProvider locale={zhCN}>\r\n      <Router>\r\n        <Routes>\r\n          <Route path=\"/login\" element={<Login />} />\r\n          <Route \r\n            path=\"/\" \r\n            element={isAuthenticated() ? <MainLayout /> : <Navigate to=\"/login\" />}\r\n          >\r\n            <Route index element={<Navigate to=\"/real-time-traffic\" />} />\r\n            <Route path=\"real-time-traffic\" element={<RealTimeTraffic />} />\r\n            <Route path=\"device-status\" element={<DeviceStatus />} />\r\n            <Route path=\"road-monitoring\" element={<RoadMonitoring />} />\r\n            <Route \r\n              path=\"system-management\" \r\n              element={<SystemManagement />} \r\n            />\r\n          </Route>\r\n          <Route path=\"*\" element={<Navigate to=\"/\" />} />\r\n        </Routes>\r\n      </Router>\r\n    </ConfigProvider>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AAChG,SAASC,cAAc,QAAQ,MAAM;AACrC,OAAOC,IAAI,MAAM,uBAAuB;;AAExC;AACA,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,gBAAgB,MAAM,0BAA0B;;AAEvD;AACA,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,MAAMC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EACzCC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACN,IAAI,CAAC,CAAC;EACzC,OAAO,CAAC,CAACA,IAAI;AACf,CAAC;;AAED;AACA,MAAMO,yBAAyB,GAAGA,CAAA,KAAM;EACtC,IAAI;IAAA,IAAAC,UAAA;IACF,MAAMR,IAAI,GAAGK,IAAI,CAACI,KAAK,CAACR,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IAC7D;IACA,MAAMQ,QAAQ,GAAGV,IAAI,CAACW,IAAI,MAAAH,UAAA,GAAIR,IAAI,CAACA,IAAI,cAAAQ,UAAA,uBAATA,UAAA,CAAWG,IAAI,KAAI,MAAM;IACvDR,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;MACvBQ,IAAI,EAAEZ,IAAI;MACVa,EAAE,EAAEH,QAAQ;MACZI,KAAK,EAAEJ,QAAQ,KAAK;IACtB,CAAC,CAAC;IACF,OAAOA,QAAQ,KAAK,OAAO;EAC7B,CAAC,CAAC,OAAOK,CAAC,EAAE;IACVZ,OAAO,CAACa,KAAK,CAAC,WAAW,EAAED,CAAC,CAAC;IAC7B,OAAO,KAAK;EACd;AACF,CAAC;AAED,SAASE,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACA,MAAM,CAACnB,eAAe,EAAEoB,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMsC,SAAS,GAAGA,CAAA,KAAM;MACtB,MAAMpB,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MACzCiB,kBAAkB,CAAC,CAAC,CAACnB,IAAI,CAAC,CAAC,CAAC;IAC9B,CAAC;;IAED;IACAoB,SAAS,CAAC,CAAC;;IAEX;IACAC,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEF,SAAS,CAAC;IAE7C,OAAO,MAAM;MACXC,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEH,SAAS,CAAC;IAClD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEtB,OAAA,CAACT,cAAc;IAACmC,MAAM,EAAElC,IAAK;IAAAmC,QAAA,eAC3B3B,OAAA,CAACd,MAAM;MAAAyC,QAAA,eACL3B,OAAA,CAACb,MAAM;QAAAwC,QAAA,gBACL3B,OAAA,CAACZ,KAAK;UAACwC,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAE7B,OAAA,CAACP,KAAK;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CjC,OAAA,CAACZ,KAAK;UACJwC,IAAI,EAAC,GAAG;UACRC,OAAO,EAAE5B,eAAe,CAAC,CAAC,gBAAGD,OAAA,CAACN,UAAU;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGjC,OAAA,CAACX,QAAQ;YAAC6C,EAAE,EAAC;UAAQ;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAN,QAAA,gBAEvE3B,OAAA,CAACZ,KAAK;YAAC+C,KAAK;YAACN,OAAO,eAAE7B,OAAA,CAACX,QAAQ;cAAC6C,EAAE,EAAC;YAAoB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DjC,OAAA,CAACZ,KAAK;YAACwC,IAAI,EAAC,mBAAmB;YAACC,OAAO,eAAE7B,OAAA,CAACL,eAAe;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChEjC,OAAA,CAACZ,KAAK;YAACwC,IAAI,EAAC,eAAe;YAACC,OAAO,eAAE7B,OAAA,CAACJ,YAAY;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzDjC,OAAA,CAACZ,KAAK;YAACwC,IAAI,EAAC,iBAAiB;YAACC,OAAO,eAAE7B,OAAA,CAACH,cAAc;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DjC,OAAA,CAACZ,KAAK;YACJwC,IAAI,EAAC,mBAAmB;YACxBC,OAAO,eAAE7B,OAAA,CAACF,gBAAgB;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACRjC,OAAA,CAACZ,KAAK;UAACwC,IAAI,EAAC,GAAG;UAACC,OAAO,eAAE7B,OAAA,CAACX,QAAQ;YAAC6C,EAAE,EAAC;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAErB;AAACb,EAAA,CA7CQD,GAAG;AAAAiB,EAAA,GAAHjB,GAAG;AA+CZ,eAAeA,GAAG;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}