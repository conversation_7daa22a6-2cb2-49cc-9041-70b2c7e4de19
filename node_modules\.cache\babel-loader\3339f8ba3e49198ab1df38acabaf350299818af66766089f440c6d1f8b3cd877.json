{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet scene = null; // 添加scene全局变量\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm'\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    try {\n      // 解析消息\n      const messageData = JSON.parse(message.toString());\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        var _messageData$data;\n        console.log('收到RSM消息:', messageData);\n\n        // 获取交通参与者列表\n        const participants = ((_messageData$data = messageData.data) === null || _messageData$data === void 0 ? void 0 : _messageData$data.participants) || [];\n\n        // 只处理机动车信息 (partPtcType === '1')\n        const vehicles = participants.filter(p => p.partPtcType === '1');\n        if (vehicles.length > 0) {\n          console.log('RSM消息中的机动车信息:', vehicles);\n\n          // 处理每个机动车\n          vehicles.forEach(async vehicle => {\n            const vehicleId = vehicle.partPtcId;\n\n            // 解析位置和状态信息\n            const vehicleState = {\n              longitude: parseFloat(vehicle.partPosLong),\n              latitude: parseFloat(vehicle.partPosLat),\n              speed: parseFloat(vehicle.partSpeed),\n              heading: parseFloat(vehicle.partHeading)\n            };\n\n            // 转换为模型坐标\n            const modelPos = converter.current.wgs84ToModel(vehicleState.longitude, vehicleState.latitude);\n\n            // 检查这个ID的车辆是否已经存在\n            if (!vehicleModels.has(vehicleId)) {\n              try {\n                if (!preloadedVehicleModel) {\n                  console.error('预加载的车辆模型不存在');\n                  return;\n                }\n\n                // 克隆预加载的模型\n                const vehicleModel = preloadedVehicleModel.clone();\n\n                // 设置位置和方向\n                vehicleModel.position.set(modelPos.x, 0.5, -modelPos.y);\n                vehicleModel.rotation.y = Math.PI - vehicleState.heading * Math.PI / 180;\n\n                // 添加到场景\n                scene.add(vehicleModel);\n\n                // 存储到Map中\n                vehicleModels.set(vehicleId, vehicleModel);\n\n                // 添加标签\n                // const label = createTextSprite(`车辆ID: ${vehicleId}\\n速度: ${vehicleState.speed.toFixed(2)} m/s`);\n                // label.position.set(0, 2, 0);\n                // vehicleModel.add(label);\n\n                console.log(`创建新车辆实例: ID ${vehicleId}`, {\n                  位置: modelPos,\n                  朝向: vehicleState.heading\n                });\n              } catch (error) {\n                console.error(`创建车辆实例失败: ID ${vehicleId}`, error);\n              }\n            } else {\n              // 更新现有车辆的位置和朝向\n              const vehicleModel = vehicleModels.get(vehicleId);\n\n              // 创建位置补间动画\n              new TWEEN.Tween(vehicleModel.position).to({\n                x: modelPos.x,\n                y: 0.5,\n                z: -modelPos.y\n              }, 100).easing(TWEEN.Easing.Linear.None).start();\n\n              // 创建旋转补间动画\n              const targetRotation = Math.PI - vehicleState.heading * Math.PI / 180;\n              new TWEEN.Tween(vehicleModel.rotation).to({\n                y: targetRotation\n              }, 100).easing(TWEEN.Easing.Linear.None).start();\n\n              // 更新标签\n              // vehicleModel.children.forEach(child => {\n              //   if (child.isSprite) {\n              //     vehicleModel.remove(child);\n              //   }\n              // });\n              // const label = createTextSprite(`车辆ID: ${vehicleId}\\n速度: ${vehicleState.speed.toFixed(2)} m/s`);\n              // label.position.set(0, 2, 0);\n              // vehicleModel.add(label);\n            }\n          });\n        }\n\n        // 清理不再存在的车辆\n        const currentVehicleIds = new Set(vehicles.map(v => v.partPtcId));\n        vehicleModels.forEach((model, id) => {\n          if (!currentVehicleIds.has(id)) {\n            scene.remove(model);\n            vehicleModels.delete(id);\n            console.log(`移除离开区域的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        const bsmData = messageData.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n\n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n\n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n\n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);\n        }\n        return;\n      }\n\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    scene = new THREE.Scene();\n    scene.background = new THREE.Color(0xf0f0f0);\n\n    // 添加光源\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);\n    scene.add(ambientLight);\n    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight.position.set(10, 20, 10);\n    directionalLight.castShadow = true;\n    scene.add(directionalLight);\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);\n    camera.position.set(0, 300, 0);\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.shadowMap.enabled = true;\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.target.set(0, 0, 0);\n\n    // 添加地面\n    const groundGeometry = new THREE.PlaneGeometry(1000, 1000);\n    const groundMaterial = new THREE.MeshStandardMaterial({\n      color: 0x999999,\n      roughness: 0.8,\n      metalness: 0.2\n    });\n    const ground = new THREE.Mesh(groundGeometry, groundMaterial);\n    ground.rotation.x = -Math.PI / 2;\n    ground.receiveShadow = true;\n    scene.add(ground);\n\n    // 预加载车辆模型\n    preloadVehicleModel().then(() => {\n      console.log('车辆模型预加载完成，开始渲染循环');\n\n      // 动画循环\n      let animationFrameId;\n      const animate = () => {\n        animationFrameId = requestAnimationFrame(animate);\n        if (scene && controls && renderer) {\n          // 更新TWEEN\n          TWEEN.update();\n\n          // 更新控制器\n          controls.update();\n\n          // 渲染场景\n          renderer.render(scene, camera);\n        }\n      };\n      animate();\n\n      // 处理窗口大小变化\n      const handleResize = () => {\n        if (camera && renderer) {\n          camera.aspect = window.innerWidth / window.innerHeight;\n          camera.updateProjectionMatrix();\n          renderer.setSize(window.innerWidth, window.innerHeight);\n        }\n      };\n      window.addEventListener('resize', handleResize);\n\n      // 返回清理函数\n      return () => {\n        // 停止动画循环\n        if (animationFrameId) {\n          cancelAnimationFrame(animationFrameId);\n        }\n\n        // 清理事件监听\n        window.removeEventListener('resize', handleResize);\n\n        // 清理场景中的所有车辆模型\n        if (scene) {\n          vehicleModels.forEach((model, id) => {\n            scene.remove(model);\n          });\n          vehicleModels.clear();\n\n          // 清理场景中的所有对象\n          while (scene.children.length > 0) {\n            scene.remove(scene.children[0]);\n          }\n        }\n\n        // 清理渲染器\n        if (renderer) {\n          var _containerRef$current;\n          (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.removeChild(renderer.domElement);\n          renderer.dispose();\n        }\n\n        // 清理控制器\n        if (controls) {\n          controls.dispose();\n        }\n\n        // 重置全局变量\n        scene = null;\n        preloadedVehicleModel = null;\n      };\n    });\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"pok6xuZ9wkLpUfjRdkdxrFsX29M=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadVehicleModel = async () => {\n  try {\n    console.log('开始预加载车辆模型...');\n    const vehicleLoader = new GLTFLoader();\n    const gltf = await vehicleLoader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n    const vehicleModel = gltf.scene;\n\n    // 调整模型材质\n    vehicleModel.traverse(child => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n      }\n    });\n\n    // 存储预加载的模型\n    preloadedVehicleModel = vehicleModel;\n    console.log('车辆模型预加载成功');\n  } catch (error) {\n    console.error('车辆模型预加载失败:', error);\n  }\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "scene", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "topics", "bsm", "rsm", "BASE_URL", "vehicleModels", "Map", "CampusModel", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "clone", "currentUp", "up", "Tween", "to", "x", "y", "z", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "Math", "PI", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "handleMqttMessage", "topic", "message", "error", "toString", "messageData", "JSON", "parse", "type", "_messageData$data", "participants", "data", "vehicles", "filter", "p", "partPtcType", "length", "for<PERSON>ach", "vehicle", "vehicleId", "partPtcId", "parseFloat", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "wgs84ToModel", "has", "vehicleModel", "set", "rotation", "add", "位置", "朝向", "get", "Linear", "None", "targetRotation", "currentVehicleIds", "Set", "map", "v", "model", "id", "remove", "delete", "bsmData", "newState", "partLong", "partLat", "newPosition", "Vector3", "updateMatrix", "updateMatrixWorld", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "event", "payload", "stringify", "onerror", "onclose", "setTimeout", "Scene", "background", "Color", "ambientLight", "AmbientLight", "directionalLight", "DirectionalLight", "<PERSON><PERSON><PERSON><PERSON>", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "shadowMap", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "enableDamping", "dampingFactor", "groundGeometry", "PlaneGeometry", "groundMaterial", "MeshStandardMaterial", "color", "roughness", "metalness", "ground", "<PERSON><PERSON>", "receiveShadow", "preloadVehicleModel", "then", "animationFrameId", "animate", "requestAnimationFrame", "render", "handleResize", "aspect", "updateProjectionMatrix", "addEventListener", "cancelAnimationFrame", "removeEventListener", "clear", "children", "_containerRef$current", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "ref", "style", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "scale", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "toArray", "新位置", "e", "vehicle<PERSON>oader", "gltf", "loadAsync", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "envMapIntensity", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet scene = null; // 添加scene全局变量\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm'\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    \n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    \n    try {\n      // 解析消息\n      const messageData = JSON.parse(message.toString());\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        console.log('收到RSM消息:', messageData);\n        \n        // 获取交通参与者列表\n        const participants = messageData.data?.participants || [];\n        \n        // 只处理机动车信息 (partPtcType === '1')\n        const vehicles = participants.filter(p => p.partPtcType === '1');\n        \n        if (vehicles.length > 0) {\n          console.log('RSM消息中的机动车信息:', vehicles);\n          \n          // 处理每个机动车\n          vehicles.forEach(async (vehicle) => {\n            const vehicleId = vehicle.partPtcId;\n            \n            // 解析位置和状态信息\n            const vehicleState = {\n              longitude: parseFloat(vehicle.partPosLong),\n              latitude: parseFloat(vehicle.partPosLat),\n              speed: parseFloat(vehicle.partSpeed),\n              heading: parseFloat(vehicle.partHeading)\n            };\n            \n            // 转换为模型坐标\n            const modelPos = converter.current.wgs84ToModel(\n              vehicleState.longitude, \n              vehicleState.latitude\n            );\n            \n            // 检查这个ID的车辆是否已经存在\n            if (!vehicleModels.has(vehicleId)) {\n              try {\n                if (!preloadedVehicleModel) {\n                  console.error('预加载的车辆模型不存在');\n                  return;\n                }\n                \n                // 克隆预加载的模型\n                const vehicleModel = preloadedVehicleModel.clone();\n                \n                // 设置位置和方向\n                vehicleModel.position.set(modelPos.x, 0.5, -modelPos.y);\n                vehicleModel.rotation.y = Math.PI - vehicleState.heading * Math.PI / 180;\n                \n                // 添加到场景\n                scene.add(vehicleModel);\n                \n                // 存储到Map中\n                vehicleModels.set(vehicleId, vehicleModel);\n                \n                // 添加标签\n                // const label = createTextSprite(`车辆ID: ${vehicleId}\\n速度: ${vehicleState.speed.toFixed(2)} m/s`);\n                // label.position.set(0, 2, 0);\n                // vehicleModel.add(label);\n                \n                console.log(`创建新车辆实例: ID ${vehicleId}`, {\n                  位置: modelPos,\n                  朝向: vehicleState.heading\n                });\n              } catch (error) {\n                console.error(`创建车辆实例失败: ID ${vehicleId}`, error);\n              }\n            } else {\n              // 更新现有车辆的位置和朝向\n              const vehicleModel = vehicleModels.get(vehicleId);\n              \n              // 创建位置补间动画\n              new TWEEN.Tween(vehicleModel.position)\n                .to({\n                  x: modelPos.x,\n                  y: 0.5,\n                  z: -modelPos.y\n                }, 100)\n                .easing(TWEEN.Easing.Linear.None)\n                .start();\n              \n              // 创建旋转补间动画\n              const targetRotation = Math.PI - vehicleState.heading * Math.PI / 180;\n              new TWEEN.Tween(vehicleModel.rotation)\n                .to({\n                  y: targetRotation\n                }, 100)\n                .easing(TWEEN.Easing.Linear.None)\n                .start();\n              \n              // 更新标签\n              // vehicleModel.children.forEach(child => {\n              //   if (child.isSprite) {\n              //     vehicleModel.remove(child);\n              //   }\n              // });\n              // const label = createTextSprite(`车辆ID: ${vehicleId}\\n速度: ${vehicleState.speed.toFixed(2)} m/s`);\n              // label.position.set(0, 2, 0);\n              // vehicleModel.add(label);\n            }\n          });\n        }\n        \n        // 清理不再存在的车辆\n        const currentVehicleIds = new Set(vehicles.map(v => v.partPtcId));\n        vehicleModels.forEach((model, id) => {\n          if (!currentVehicleIds.has(id)) {\n            scene.remove(model);\n            vehicleModels.delete(id);\n            console.log(`移除离开区域的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        \n        const bsmData = messageData.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        \n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n          \n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n          \n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);\n        }\n        return;\n      }\n      \n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    scene = new THREE.Scene();\n    scene.background = new THREE.Color(0xf0f0f0);\n    \n    // 添加光源\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);\n    scene.add(ambientLight);\n    \n    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight.position.set(10, 20, 10);\n    directionalLight.castShadow = true;\n    scene.add(directionalLight);\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      75,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      1000\n    );\n    camera.position.set(0, 300, 0);\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.shadowMap.enabled = true;\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.target.set(0, 0, 0);\n    \n    // 添加地面\n    const groundGeometry = new THREE.PlaneGeometry(1000, 1000);\n    const groundMaterial = new THREE.MeshStandardMaterial({ \n      color: 0x999999,\n      roughness: 0.8,\n      metalness: 0.2\n    });\n    const ground = new THREE.Mesh(groundGeometry, groundMaterial);\n    ground.rotation.x = -Math.PI / 2;\n    ground.receiveShadow = true;\n    scene.add(ground);\n\n    // 预加载车辆模型\n    preloadVehicleModel().then(() => {\n      console.log('车辆模型预加载完成，开始渲染循环');\n      \n      // 动画循环\n      let animationFrameId;\n      const animate = () => {\n        animationFrameId = requestAnimationFrame(animate);\n        \n        if (scene && controls && renderer) {\n          // 更新TWEEN\n          TWEEN.update();\n          \n          // 更新控制器\n          controls.update();\n          \n          // 渲染场景\n          renderer.render(scene, camera);\n        }\n      };\n      \n      animate();\n      \n      // 处理窗口大小变化\n      const handleResize = () => {\n        if (camera && renderer) {\n          camera.aspect = window.innerWidth / window.innerHeight;\n          camera.updateProjectionMatrix();\n          renderer.setSize(window.innerWidth, window.innerHeight);\n        }\n      };\n      window.addEventListener('resize', handleResize);\n      \n      // 返回清理函数\n      return () => {\n        // 停止动画循环\n        if (animationFrameId) {\n          cancelAnimationFrame(animationFrameId);\n        }\n        \n        // 清理事件监听\n        window.removeEventListener('resize', handleResize);\n        \n        // 清理场景中的所有车辆模型\n        if (scene) {\n          vehicleModels.forEach((model, id) => {\n            scene.remove(model);\n          });\n          vehicleModels.clear();\n          \n          // 清理场景中的所有对象\n          while(scene.children.length > 0) { \n            scene.remove(scene.children[0]); \n          }\n        }\n        \n        // 清理渲染器\n        if (renderer) {\n          containerRef.current?.removeChild(renderer.domElement);\n          renderer.dispose();\n        }\n        \n        // 清理控制器\n        if (controls) {\n          controls.dispose();\n        }\n        \n        // 重置全局变量\n        scene = null;\n        preloadedVehicleModel = null;\n      };\n    });\n  }, []);\n\n  return (\n    <>\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadVehicleModel = async () => {\n  try {\n    console.log('开始预加载车辆模型...');\n    const vehicleLoader = new GLTFLoader();\n    const gltf = await vehicleLoader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n    const vehicleModel = gltf.scene;\n    \n    // 调整模型材质\n    vehicleModel.traverse((child) => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n      }\n    });\n    \n    // 存储预加载的模型\n    preloadedVehicleModel = vehicleModel;\n    console.log('车辆模型预加载成功');\n  } catch (error) {\n    console.error('车辆模型预加载失败:', error);\n  }\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE;IACNC,GAAG,EAAE,2BAA2B;IAChCC,GAAG,EAAE;EACP;AACF,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAGrC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMsC,UAAU,GAAGtC,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMuC,SAAS,GAAGvC,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMmC,aAAa,GAAGxC,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMyC,eAAe,GAAGzC,MAAM,CAAC,CAAC,CAAC;EACjC,MAAM0C,aAAa,GAAG1C,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC;IAC/C8C,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMoD,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGtE,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMuE,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnB,WAAW,CAAC,QAAQ,CAAC;IACrBjC,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACoD,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrB,WAAW,CAAC,QAAQ,CAAC;IACrBjC,UAAU,GAAG,QAAQ;IAErB,IAAImD,SAAS,CAACI,OAAO,IAAItD,QAAQ,EAAE;MACjC;MACA,MAAMuD,UAAU,GAAGL,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACsB,KAAK,CAAC,CAAC;MACrD,MAAMC,SAAS,GAAGP,SAAS,CAACI,OAAO,CAACI,EAAE,CAACF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAItE,KAAK,CAACyE,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCC,MAAM,CAAC9E,KAAK,CAAC+E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdlB,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACmC,IAAI,CAACd,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDe,KAAK,CAAC,CAAC;;MAEV;MACA,IAAIpF,KAAK,CAACyE,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC9E,KAAK,CAAC+E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdlB,SAAS,CAACI,OAAO,CAACI,EAAE,CAACW,IAAI,CAACZ,SAAS,CAAC;MACtC,CAAC,CAAC,CACDa,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAGvE,QAAQ,CAACwE,MAAM,CAAChB,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAItE,KAAK,CAACyE,KAAK,CAACY,aAAa,CAAC,CAC3BX,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC9E,KAAK,CAAC+E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdpE,QAAQ,CAACwE,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACArB,SAAS,CAACI,OAAO,CAACmB,MAAM,CAACzE,QAAQ,CAACwE,MAAM,CAAC;QACzCxE,QAAQ,CAAC0E,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACAtE,QAAQ,CAACoD,OAAO,GAAG,IAAI;;MAEvB;MACApD,QAAQ,CAAC2E,WAAW,GAAG,EAAE;MACzB3E,QAAQ,CAAC4E,WAAW,GAAG,GAAG;MAC1B5E,QAAQ,CAAC6E,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;MACtC/E,QAAQ,CAACgF,aAAa,GAAG,CAAC;MAC1BhF,QAAQ,CAAC0E,MAAM,CAAC,CAAC;MAEjBO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC5C,IAAI,CAACtF,KAAK,EAAE;MACV+E,OAAO,CAACQ,KAAK,CAAC,QAAQ,CAAC;MACvB;IACF;IAEAR,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBK,KAAK;MACLC,OAAO,EAAEA,OAAO,CAACE,QAAQ,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF;MACA,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACL,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;;MAElD;MACA,IAAIH,KAAK,KAAKpF,WAAW,CAACM,MAAM,CAACE,GAAG,IAAIgF,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAAA,IAAAC,iBAAA;QAClEd,OAAO,CAACC,GAAG,CAAC,UAAU,EAAES,WAAW,CAAC;;QAEpC;QACA,MAAMK,YAAY,GAAG,EAAAD,iBAAA,GAAAJ,WAAW,CAACM,IAAI,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBC,YAAY,KAAI,EAAE;;QAEzD;QACA,MAAME,QAAQ,GAAGF,YAAY,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAK,GAAG,CAAC;QAEhE,IAAIH,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;UACvBrB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgB,QAAQ,CAAC;;UAEtC;UACAA,QAAQ,CAACK,OAAO,CAAC,MAAOC,OAAO,IAAK;YAClC,MAAMC,SAAS,GAAGD,OAAO,CAACE,SAAS;;YAEnC;YACA,MAAMjF,YAAY,GAAG;cACnBE,SAAS,EAAEgF,UAAU,CAACH,OAAO,CAACI,WAAW,CAAC;cAC1ChF,QAAQ,EAAE+E,UAAU,CAACH,OAAO,CAACK,UAAU,CAAC;cACxChF,KAAK,EAAE8E,UAAU,CAACH,OAAO,CAACM,SAAS,CAAC;cACpChF,OAAO,EAAE6E,UAAU,CAACH,OAAO,CAACO,WAAW;YACzC,CAAC;;YAED;YACA,MAAMC,QAAQ,GAAG7F,SAAS,CAACmC,OAAO,CAAC2D,YAAY,CAC7CxF,YAAY,CAACE,SAAS,EACtBF,YAAY,CAACG,QACf,CAAC;;YAED;YACA,IAAI,CAACf,aAAa,CAACqG,GAAG,CAACT,SAAS,CAAC,EAAE;cACjC,IAAI;gBACF,IAAI,CAACxG,qBAAqB,EAAE;kBAC1BgF,OAAO,CAACQ,KAAK,CAAC,aAAa,CAAC;kBAC5B;gBACF;;gBAEA;gBACA,MAAM0B,YAAY,GAAGlH,qBAAqB,CAACuD,KAAK,CAAC,CAAC;;gBAElD;gBACA2D,YAAY,CAACjF,QAAQ,CAACkF,GAAG,CAACJ,QAAQ,CAACnD,CAAC,EAAE,GAAG,EAAE,CAACmD,QAAQ,CAAClD,CAAC,CAAC;gBACvDqD,YAAY,CAACE,QAAQ,CAACvD,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGtD,YAAY,CAACK,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAG;;gBAExE;gBACA7E,KAAK,CAACoH,GAAG,CAACH,YAAY,CAAC;;gBAEvB;gBACAtG,aAAa,CAACuG,GAAG,CAACX,SAAS,EAAEU,YAAY,CAAC;;gBAE1C;gBACA;gBACA;gBACA;;gBAEAlC,OAAO,CAACC,GAAG,CAAC,eAAeuB,SAAS,EAAE,EAAE;kBACtCc,EAAE,EAAEP,QAAQ;kBACZQ,EAAE,EAAE/F,YAAY,CAACK;gBACnB,CAAC,CAAC;cACJ,CAAC,CAAC,OAAO2D,KAAK,EAAE;gBACdR,OAAO,CAACQ,KAAK,CAAC,gBAAgBgB,SAAS,EAAE,EAAEhB,KAAK,CAAC;cACnD;YACF,CAAC,MAAM;cACL;cACA,MAAM0B,YAAY,GAAGtG,aAAa,CAAC4G,GAAG,CAAChB,SAAS,CAAC;;cAEjD;cACA,IAAIvH,KAAK,CAACyE,KAAK,CAACwD,YAAY,CAACjF,QAAQ,CAAC,CACnC0B,EAAE,CAAC;gBACFC,CAAC,EAAEmD,QAAQ,CAACnD,CAAC;gBACbC,CAAC,EAAE,GAAG;gBACNC,CAAC,EAAE,CAACiD,QAAQ,CAAClD;cACf,CAAC,EAAE,GAAG,CAAC,CACNE,MAAM,CAAC9E,KAAK,CAAC+E,MAAM,CAACyD,MAAM,CAACC,IAAI,CAAC,CAChCrD,KAAK,CAAC,CAAC;;cAEV;cACA,MAAMsD,cAAc,GAAG9C,IAAI,CAACC,EAAE,GAAGtD,YAAY,CAACK,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAG;cACrE,IAAI7F,KAAK,CAACyE,KAAK,CAACwD,YAAY,CAACE,QAAQ,CAAC,CACnCzD,EAAE,CAAC;gBACFE,CAAC,EAAE8D;cACL,CAAC,EAAE,GAAG,CAAC,CACN5D,MAAM,CAAC9E,KAAK,CAAC+E,MAAM,CAACyD,MAAM,CAACC,IAAI,CAAC,CAChCrD,KAAK,CAAC,CAAC;;cAEV;cACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACF;UACF,CAAC,CAAC;QACJ;;QAEA;QACA,MAAMuD,iBAAiB,GAAG,IAAIC,GAAG,CAAC5B,QAAQ,CAAC6B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACtB,SAAS,CAAC,CAAC;QACjE7F,aAAa,CAAC0F,OAAO,CAAC,CAAC0B,KAAK,EAAEC,EAAE,KAAK;UACnC,IAAI,CAACL,iBAAiB,CAACX,GAAG,CAACgB,EAAE,CAAC,EAAE;YAC9BhI,KAAK,CAACiI,MAAM,CAACF,KAAK,CAAC;YACnBpH,aAAa,CAACuH,MAAM,CAACF,EAAE,CAAC;YACxBjD,OAAO,CAACC,GAAG,CAAC,iBAAiBgD,EAAE,EAAE,CAAC;UACpC;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAI3C,KAAK,KAAKpF,WAAW,CAACM,MAAM,CAACC,GAAG,IAAIiF,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAClEb,OAAO,CAACC,GAAG,CAAC,UAAU,EAAES,WAAW,CAAC;QAEpC,MAAM0C,OAAO,GAAG1C,WAAW,CAACM,IAAI;QAChC,MAAMqC,QAAQ,GAAG;UACf3G,SAAS,EAAEgF,UAAU,CAAC0B,OAAO,CAACE,QAAQ,CAAC;UACvC3G,QAAQ,EAAE+E,UAAU,CAAC0B,OAAO,CAACG,OAAO,CAAC;UACrC3G,KAAK,EAAE8E,UAAU,CAAC0B,OAAO,CAACvB,SAAS,CAAC;UACpChF,OAAO,EAAE6E,UAAU,CAAC0B,OAAO,CAACtB,WAAW;QACzC,CAAC;QAED9B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEoD,QAAQ,CAAC;;QAElC;QACA,IAAI9I,gBAAgB,EAAE;UACpB,MAAMwH,QAAQ,GAAG7F,SAAS,CAACmC,OAAO,CAAC2D,YAAY,CAACqB,QAAQ,CAAC3G,SAAS,EAAE2G,QAAQ,CAAC1G,QAAQ,CAAC;UACtF,MAAM6G,WAAW,GAAG,IAAI3J,KAAK,CAAC4J,OAAO,CAAC1B,QAAQ,CAACnD,CAAC,EAAE,GAAG,EAAE,CAACmD,QAAQ,CAAClD,CAAC,CAAC;;UAEnE;UACAtE,gBAAgB,CAAC0C,QAAQ,CAACmC,IAAI,CAACoE,WAAW,CAAC;UAC3CjJ,gBAAgB,CAAC6H,QAAQ,CAACvD,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGuD,QAAQ,CAACxG,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAG;UACxEvF,gBAAgB,CAACmJ,YAAY,CAAC,CAAC;UAC/BnJ,gBAAgB,CAACoJ,iBAAiB,CAAC,IAAI,CAAC;;UAExC;UACAlH,eAAe,CAAC4G,QAAQ,CAAC;UACzBrD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEuD,WAAW,CAAC;QACtC;QACA;MACF;;MAEA;MACAxD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBK,KAAK;QACLO,IAAI,EAAEH,WAAW,CAACG,IAAI;QACtBG,IAAI,EAAEN;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCR,OAAO,CAACQ,KAAK,CAAC,SAAS,EAAED,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMmD,cAAc,GAAGA,CAAA,KAAM;IAC3B5D,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAM4D,KAAK,GAAG,QAAQ3I,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnEyE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE4D,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBhE,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED6D,EAAE,CAACG,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAM3D,OAAO,GAAGI,IAAI,CAACC,KAAK,CAACsD,KAAK,CAAClD,IAAI,CAAC;;QAEtC;QACA,IAAIT,OAAO,CAACM,IAAI,KAAK,SAAS,EAAE;UAC9Bb,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEM,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACM,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIN,OAAO,CAACM,IAAI,KAAK,SAAS,IAAIN,OAAO,CAACD,KAAK,IAAIC,OAAO,CAAC4D,OAAO,EAAE;UAClE;UACA9D,iBAAiB,CAACE,OAAO,CAACD,KAAK,EAAEK,IAAI,CAACyD,SAAS,CAAC7D,OAAO,CAAC4D,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAO3D,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDsD,EAAE,CAACO,OAAO,GAAI7D,KAAK,IAAK;MACtBR,OAAO,CAACQ,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDsD,EAAE,CAACQ,OAAO,GAAG,MAAM;MACjBtE,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACAsE,UAAU,CAACX,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACAvH,aAAa,CAACgC,OAAO,GAAGyF,EAAE;EAC5B,CAAC;EAEDpK,SAAS,CAAC,MAAM;IACd,IAAI,CAACsC,YAAY,CAACqC,OAAO,EAAE;;IAE3B;IACApD,KAAK,GAAG,IAAIpB,KAAK,CAAC2K,KAAK,CAAC,CAAC;IACzBvJ,KAAK,CAACwJ,UAAU,GAAG,IAAI5K,KAAK,CAAC6K,KAAK,CAAC,QAAQ,CAAC;;IAE5C;IACA,MAAMC,YAAY,GAAG,IAAI9K,KAAK,CAAC+K,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC1D3J,KAAK,CAACoH,GAAG,CAACsC,YAAY,CAAC;IAEvB,MAAME,gBAAgB,GAAG,IAAIhL,KAAK,CAACiL,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IAClED,gBAAgB,CAAC5H,QAAQ,CAACkF,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACzC0C,gBAAgB,CAACE,UAAU,GAAG,IAAI;IAClC9J,KAAK,CAACoH,GAAG,CAACwC,gBAAgB,CAAC;;IAE3B;IACA,MAAMG,MAAM,GAAG,IAAInL,KAAK,CAACoL,iBAAiB,CACxC,EAAE,EACF7J,MAAM,CAAC8J,UAAU,GAAG9J,MAAM,CAAC+J,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACDH,MAAM,CAAC/H,QAAQ,CAACkF,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;IAC9B6C,MAAM,CAACxF,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBvB,SAAS,CAACI,OAAO,GAAG2G,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAIvL,KAAK,CAACwL,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAACnK,MAAM,CAAC8J,UAAU,EAAE9J,MAAM,CAAC+J,WAAW,CAAC;IACvDC,QAAQ,CAACI,SAAS,CAACrH,OAAO,GAAG,IAAI;IACjCnC,YAAY,CAACqC,OAAO,CAACoH,WAAW,CAACL,QAAQ,CAACM,UAAU,CAAC;;IAErD;IACA3K,QAAQ,GAAG,IAAIhB,aAAa,CAACiL,MAAM,EAAEI,QAAQ,CAACM,UAAU,CAAC;IACzD3K,QAAQ,CAAC4K,aAAa,GAAG,IAAI;IAC7B5K,QAAQ,CAAC6K,aAAa,GAAG,IAAI;IAC7B7K,QAAQ,CAAC2E,WAAW,GAAG,EAAE;IACzB3E,QAAQ,CAAC4E,WAAW,GAAG,GAAG;IAC1B5E,QAAQ,CAAC6E,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC/E,QAAQ,CAACwE,MAAM,CAAC4C,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;IAE5B;IACA,MAAM0D,cAAc,GAAG,IAAIhM,KAAK,CAACiM,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC;IAC1D,MAAMC,cAAc,GAAG,IAAIlM,KAAK,CAACmM,oBAAoB,CAAC;MACpDC,KAAK,EAAE,QAAQ;MACfC,SAAS,EAAE,GAAG;MACdC,SAAS,EAAE;IACb,CAAC,CAAC;IACF,MAAMC,MAAM,GAAG,IAAIvM,KAAK,CAACwM,IAAI,CAACR,cAAc,EAAEE,cAAc,CAAC;IAC7DK,MAAM,CAAChE,QAAQ,CAACxD,CAAC,GAAG,CAACiB,IAAI,CAACC,EAAE,GAAG,CAAC;IAChCsG,MAAM,CAACE,aAAa,GAAG,IAAI;IAC3BrL,KAAK,CAACoH,GAAG,CAAC+D,MAAM,CAAC;;IAEjB;IACAG,mBAAmB,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MAC/BxG,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;;MAE/B;MACA,IAAIwG,gBAAgB;MACpB,MAAMC,OAAO,GAAGA,CAAA,KAAM;QACpBD,gBAAgB,GAAGE,qBAAqB,CAACD,OAAO,CAAC;QAEjD,IAAIzL,KAAK,IAAIF,QAAQ,IAAIqK,QAAQ,EAAE;UACjC;UACAnL,KAAK,CAACwF,MAAM,CAAC,CAAC;;UAEd;UACA1E,QAAQ,CAAC0E,MAAM,CAAC,CAAC;;UAEjB;UACA2F,QAAQ,CAACwB,MAAM,CAAC3L,KAAK,EAAE+J,MAAM,CAAC;QAChC;MACF,CAAC;MAED0B,OAAO,CAAC,CAAC;;MAET;MACA,MAAMG,YAAY,GAAGA,CAAA,KAAM;QACzB,IAAI7B,MAAM,IAAII,QAAQ,EAAE;UACtBJ,MAAM,CAAC8B,MAAM,GAAG1L,MAAM,CAAC8J,UAAU,GAAG9J,MAAM,CAAC+J,WAAW;UACtDH,MAAM,CAAC+B,sBAAsB,CAAC,CAAC;UAC/B3B,QAAQ,CAACG,OAAO,CAACnK,MAAM,CAAC8J,UAAU,EAAE9J,MAAM,CAAC+J,WAAW,CAAC;QACzD;MACF,CAAC;MACD/J,MAAM,CAAC4L,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;;MAE/C;MACA,OAAO,MAAM;QACX;QACA,IAAIJ,gBAAgB,EAAE;UACpBQ,oBAAoB,CAACR,gBAAgB,CAAC;QACxC;;QAEA;QACArL,MAAM,CAAC8L,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;;QAElD;QACA,IAAI5L,KAAK,EAAE;UACTW,aAAa,CAAC0F,OAAO,CAAC,CAAC0B,KAAK,EAAEC,EAAE,KAAK;YACnChI,KAAK,CAACiI,MAAM,CAACF,KAAK,CAAC;UACrB,CAAC,CAAC;UACFpH,aAAa,CAACuL,KAAK,CAAC,CAAC;;UAErB;UACA,OAAMlM,KAAK,CAACmM,QAAQ,CAAC/F,MAAM,GAAG,CAAC,EAAE;YAC/BpG,KAAK,CAACiI,MAAM,CAACjI,KAAK,CAACmM,QAAQ,CAAC,CAAC,CAAC,CAAC;UACjC;QACF;;QAEA;QACA,IAAIhC,QAAQ,EAAE;UAAA,IAAAiC,qBAAA;UACZ,CAAAA,qBAAA,GAAArL,YAAY,CAACqC,OAAO,cAAAgJ,qBAAA,uBAApBA,qBAAA,CAAsBC,WAAW,CAAClC,QAAQ,CAACM,UAAU,CAAC;UACtDN,QAAQ,CAACmC,OAAO,CAAC,CAAC;QACpB;;QAEA;QACA,IAAIxM,QAAQ,EAAE;UACZA,QAAQ,CAACwM,OAAO,CAAC,CAAC;QACpB;;QAEA;QACAtM,KAAK,GAAG,IAAI;QACZD,qBAAqB,GAAG,IAAI;MAC9B,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEZ,OAAA,CAAAE,SAAA;IAAA8M,QAAA,gBACEhN,OAAA;MAAKoN,GAAG,EAAExL,YAAa;MAACyL,KAAK,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpE3N,OAAA;MAAKqN,KAAK,EAAEzK,oBAAqB;MAAAoK,QAAA,gBAC/BhN,OAAA;QACEqN,KAAK,EAAE;UACL,GAAGjK,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EmJ,KAAK,EAAEnJ,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFkL,OAAO,EAAE9J,kBAAmB;QAAAkJ,QAAA,EAC7B;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3N,OAAA;QACEqN,KAAK,EAAE;UACL,GAAGjK,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EmJ,KAAK,EAAEnJ,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFkL,OAAO,EAAE5J,kBAAmB;QAAAgJ,QAAA,EAC7B;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAAhM,EAAA,CAlfMD,WAAW;AAAAmM,EAAA,GAAXnM,WAAW;AAmfjB,SAASoM,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAACV,KAAK,GAAG,GAAG;EAClBU,MAAM,CAACT,MAAM,GAAG,EAAE;;EAElB;EACAY,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAACV,KAAK,GAAC,CAAC,EAAEU,MAAM,CAACT,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAMkB,OAAO,GAAG,IAAIhP,KAAK,CAACiP,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAIlP,KAAK,CAACmP,cAAc,CAAC;IAC9ClG,GAAG,EAAE+F,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAIrP,KAAK,CAACsP,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAACE,KAAK,CAACjH,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAO+G,MAAM;AACf;;AAEA;AACA9N,MAAM,CAACiO,WAAW,GAAG,CAACzK,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EAChC,IAAIvE,gBAAgB,EAAE;IACpBA,gBAAgB,CAAC0C,QAAQ,CAACkF,GAAG,CAACvD,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACtCvE,gBAAgB,CAACmJ,YAAY,CAAC,CAAC;IAC/BnJ,gBAAgB,CAACoJ,iBAAiB,CAAC,IAAI,CAAC;IACxC3D,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAACrB,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA1D,MAAM,CAACkO,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMtE,MAAM,GAAGqD,QAAQ,CAACkB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAIzE,MAAM,EAAE;MACV;MACA,MAAM0E,MAAM,GAAG1E,MAAM,CAAC/H,QAAQ,CAACsB,KAAK,CAAC,CAAC;;MAEtC;MACAyG,MAAM,CAAC/H,QAAQ,CAACkF,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9B6C,MAAM,CAACvG,EAAE,CAAC0D,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtB6C,MAAM,CAACxF,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAwF,MAAM,CAACtB,YAAY,CAAC,CAAC;MACrBsB,MAAM,CAACrB,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAM5I,QAAQ,GAAGsN,QAAQ,CAACkB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAI5O,QAAQ,EAAE;QACZA,QAAQ,CAACwE,MAAM,CAAC4C,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BpH,QAAQ,CAAC0E,MAAM,CAAC,CAAC;MACnB;MAEAO,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxB2J,GAAG,EAAEF,MAAM,CAACG,OAAO,CAAC,CAAC;QACrBC,GAAG,EAAE9E,MAAM,CAAC/H,QAAQ,CAAC4M,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV/J,OAAO,CAACQ,KAAK,CAAC,YAAY,EAAEuJ,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAMxD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;EACtC,IAAI;IACFvG,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAM+J,aAAa,GAAG,IAAIlQ,UAAU,CAAC,CAAC;IACtC,MAAMmQ,IAAI,GAAG,MAAMD,aAAa,CAACE,SAAS,CAAC,GAAGvO,QAAQ,uBAAuB,CAAC;IAC9E,MAAMuG,YAAY,GAAG+H,IAAI,CAAChP,KAAK;;IAE/B;IACAiH,YAAY,CAACiI,QAAQ,CAAEC,KAAK,IAAK;MAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;QAChBD,KAAK,CAACE,QAAQ,GAAG,IAAIzQ,KAAK,CAACmM,oBAAoB,CAAC;UAC9CC,KAAK,EAAE,QAAQ;UACfE,SAAS,EAAE,GAAG;UACdD,SAAS,EAAE,GAAG;UACdqE,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;;IAEF;IACAvP,qBAAqB,GAAGkH,YAAY;IACpClC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,CAAC,OAAOO,KAAK,EAAE;IACdR,OAAO,CAACQ,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;AACF,CAAC;AAED,eAAe1E,WAAW;AAAC,IAAAmM,EAAA;AAAAuC,YAAA,CAAAvC,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}