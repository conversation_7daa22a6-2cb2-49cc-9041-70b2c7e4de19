{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { initFadeMotion } from '../../style/motion/fade';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport getOffset from '../util';\nimport floatButtonGroupMotion from './keyframes';\n// ============================== Group ==============================\nconst floatButtonGroupStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    floatButtonSize,\n    margin,\n    borderRadiusLG,\n    borderRadiusSM,\n    badgeOffset,\n    floatButtonBodyPadding,\n    zIndexPopupBase,\n    calc\n  } = token;\n  const groupPrefixCls = `${componentCls}-group`;\n  return {\n    [groupPrefixCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      zIndex: zIndexPopupBase,\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      border: 'none',\n      position: 'fixed',\n      height: 'auto',\n      boxShadow: 'none',\n      minWidth: floatButtonSize,\n      minHeight: floatButtonSize,\n      insetInlineEnd: token.floatButtonInsetInlineEnd,\n      bottom: token.floatButtonInsetBlockEnd,\n      borderRadius: borderRadiusLG,\n      [`${groupPrefixCls}-wrap`]: {\n        zIndex: -1,\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        position: 'absolute'\n      },\n      [`&${groupPrefixCls}-rtl`]: {\n        direction: 'rtl'\n      },\n      [componentCls]: {\n        position: 'static'\n      }\n    }),\n    [`${groupPrefixCls}-top > ${groupPrefixCls}-wrap`]: {\n      flexDirection: 'column',\n      top: 'auto',\n      bottom: calc(floatButtonSize).add(margin).equal(),\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        width: '100%',\n        height: margin,\n        bottom: calc(margin).mul(-1).equal()\n      }\n    },\n    [`${groupPrefixCls}-bottom > ${groupPrefixCls}-wrap`]: {\n      flexDirection: 'column',\n      top: calc(floatButtonSize).add(margin).equal(),\n      bottom: 'auto',\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        width: '100%',\n        height: margin,\n        top: calc(margin).mul(-1).equal()\n      }\n    },\n    [`${groupPrefixCls}-right > ${groupPrefixCls}-wrap`]: {\n      flexDirection: 'row',\n      left: {\n        _skip_check_: true,\n        value: calc(floatButtonSize).add(margin).equal()\n      },\n      right: {\n        _skip_check_: true,\n        value: 'auto'\n      },\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        width: margin,\n        height: '100%',\n        left: {\n          _skip_check_: true,\n          value: calc(margin).mul(-1).equal()\n        }\n      }\n    },\n    [`${groupPrefixCls}-left > ${groupPrefixCls}-wrap`]: {\n      flexDirection: 'row',\n      left: {\n        _skip_check_: true,\n        value: 'auto'\n      },\n      right: {\n        _skip_check_: true,\n        value: calc(floatButtonSize).add(margin).equal()\n      },\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        width: margin,\n        height: '100%',\n        right: {\n          _skip_check_: true,\n          value: calc(margin).mul(-1).equal()\n        }\n      }\n    },\n    [`${groupPrefixCls}-circle`]: {\n      gap: margin,\n      [`${groupPrefixCls}-wrap`]: {\n        gap: margin\n      }\n    },\n    [`${groupPrefixCls}-square`]: {\n      [`${componentCls}-square`]: {\n        padding: 0,\n        borderRadius: 0,\n        [`&${groupPrefixCls}-trigger`]: {\n          borderRadius: borderRadiusLG\n        },\n        '&:first-child': {\n          borderStartStartRadius: borderRadiusLG,\n          borderStartEndRadius: borderRadiusLG\n        },\n        '&:last-child': {\n          borderEndStartRadius: borderRadiusLG,\n          borderEndEndRadius: borderRadiusLG\n        },\n        '&:not(:last-child)': {\n          borderBottom: `${unit(token.lineWidth)} ${token.lineType} ${token.colorSplit}`\n        },\n        [`${antCls}-badge`]: {\n          [`${antCls}-badge-count`]: {\n            top: calc(calc(floatButtonBodyPadding).add(badgeOffset)).mul(-1).equal(),\n            insetInlineEnd: calc(calc(floatButtonBodyPadding).add(badgeOffset)).mul(-1).equal()\n          }\n        }\n      },\n      [`${groupPrefixCls}-wrap`]: {\n        borderRadius: borderRadiusLG,\n        boxShadow: token.boxShadowSecondary,\n        [`${componentCls}-square`]: {\n          boxShadow: 'none',\n          borderRadius: 0,\n          padding: floatButtonBodyPadding,\n          [`${componentCls}-body`]: {\n            width: token.floatButtonBodySize,\n            height: token.floatButtonBodySize,\n            borderRadius: borderRadiusSM\n          }\n        }\n      }\n    },\n    [`${groupPrefixCls}-top > ${groupPrefixCls}-wrap, ${groupPrefixCls}-bottom > ${groupPrefixCls}-wrap`]: {\n      [`> ${componentCls}-square`]: {\n        '&:first-child': {\n          borderStartStartRadius: borderRadiusLG,\n          borderStartEndRadius: borderRadiusLG\n        },\n        '&:last-child': {\n          borderEndStartRadius: borderRadiusLG,\n          borderEndEndRadius: borderRadiusLG\n        },\n        '&:not(:last-child)': {\n          borderBottom: `${unit(token.lineWidth)} ${token.lineType} ${token.colorSplit}`\n        }\n      }\n    },\n    [`${groupPrefixCls}-left > ${groupPrefixCls}-wrap, ${groupPrefixCls}-right > ${groupPrefixCls}-wrap`]: {\n      [`> ${componentCls}-square`]: {\n        '&:first-child': {\n          borderStartStartRadius: borderRadiusLG,\n          borderEndStartRadius: borderRadiusLG\n        },\n        '&:last-child': {\n          borderStartEndRadius: borderRadiusLG,\n          borderEndEndRadius: borderRadiusLG\n        },\n        '&:not(:last-child)': {\n          borderInlineEnd: `${unit(token.lineWidth)} ${token.lineType} ${token.colorSplit}`\n        }\n      }\n    },\n    [`${groupPrefixCls}-circle-shadow`]: {\n      boxShadow: 'none'\n    },\n    [`${groupPrefixCls}-square-shadow`]: {\n      boxShadow: token.boxShadowSecondary,\n      [`${componentCls}-square`]: {\n        boxShadow: 'none',\n        padding: floatButtonBodyPadding,\n        [`${componentCls}-body`]: {\n          width: token.floatButtonBodySize,\n          height: token.floatButtonBodySize,\n          borderRadius: borderRadiusSM\n        }\n      }\n    }\n  };\n};\n// ============================== Shared ==============================\nconst sharedFloatButtonStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    floatButtonBodyPadding,\n    floatButtonIconSize,\n    floatButtonSize,\n    borderRadiusLG,\n    badgeOffset,\n    dotOffsetInSquare,\n    dotOffsetInCircle,\n    zIndexPopupBase,\n    calc\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      border: 'none',\n      position: 'fixed',\n      cursor: 'pointer',\n      zIndex: zIndexPopupBase,\n      // Do not remove the 'display: block' here.\n      // Deleting it will cause marginBottom to become ineffective.\n      // Ref: https://github.com/ant-design/ant-design/issues/44700\n      display: 'block',\n      width: floatButtonSize,\n      height: floatButtonSize,\n      insetInlineEnd: token.floatButtonInsetInlineEnd,\n      bottom: token.floatButtonInsetBlockEnd,\n      boxShadow: token.boxShadowSecondary,\n      // Pure Panel\n      '&-pure': {\n        position: 'relative',\n        inset: 'auto'\n      },\n      '&:empty': {\n        display: 'none'\n      },\n      [`${antCls}-badge`]: {\n        width: '100%',\n        height: '100%',\n        [`${antCls}-badge-count`]: {\n          transform: 'translate(0, 0)',\n          transformOrigin: 'center',\n          top: calc(badgeOffset).mul(-1).equal(),\n          insetInlineEnd: calc(badgeOffset).mul(-1).equal()\n        }\n      },\n      [`${componentCls}-body`]: {\n        width: '100%',\n        height: '100%',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        transition: `all ${token.motionDurationMid}`,\n        [`${componentCls}-content`]: {\n          overflow: 'hidden',\n          textAlign: 'center',\n          minHeight: floatButtonSize,\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'center',\n          alignItems: 'center',\n          padding: `${unit(calc(floatButtonBodyPadding).div(2).equal())} ${unit(floatButtonBodyPadding)}`,\n          [`${componentCls}-icon`]: {\n            textAlign: 'center',\n            margin: 'auto',\n            width: floatButtonIconSize,\n            fontSize: floatButtonIconSize,\n            lineHeight: 1\n          }\n        }\n      }\n    }),\n    [`${componentCls}-rtl`]: {\n      direction: 'rtl'\n    },\n    [`${componentCls}-circle`]: {\n      height: floatButtonSize,\n      borderRadius: '50%',\n      [`${antCls}-badge`]: {\n        [`${antCls}-badge-dot`]: {\n          top: dotOffsetInCircle,\n          insetInlineEnd: dotOffsetInCircle\n        }\n      },\n      [`${componentCls}-body`]: {\n        borderRadius: '50%'\n      }\n    },\n    [`${componentCls}-square`]: {\n      height: 'auto',\n      minHeight: floatButtonSize,\n      borderRadius: borderRadiusLG,\n      [`${antCls}-badge`]: {\n        [`${antCls}-badge-dot`]: {\n          top: dotOffsetInSquare,\n          insetInlineEnd: dotOffsetInSquare\n        }\n      },\n      [`${componentCls}-body`]: {\n        height: 'auto',\n        borderRadius: borderRadiusLG\n      }\n    },\n    [`${componentCls}-default`]: {\n      backgroundColor: token.floatButtonBackgroundColor,\n      transition: `background-color ${token.motionDurationMid}`,\n      [`${componentCls}-body`]: {\n        backgroundColor: token.floatButtonBackgroundColor,\n        transition: `background-color ${token.motionDurationMid}`,\n        '&:hover': {\n          backgroundColor: token.colorFillContent\n        },\n        [`${componentCls}-content`]: {\n          [`${componentCls}-icon`]: {\n            color: token.colorText\n          },\n          [`${componentCls}-description`]: {\n            display: 'flex',\n            alignItems: 'center',\n            lineHeight: unit(token.fontSizeLG),\n            color: token.colorText,\n            fontSize: token.fontSizeSM\n          }\n        }\n      }\n    },\n    [`${componentCls}-primary`]: {\n      backgroundColor: token.colorPrimary,\n      [`${componentCls}-body`]: {\n        backgroundColor: token.colorPrimary,\n        transition: `background-color ${token.motionDurationMid}`,\n        '&:hover': {\n          backgroundColor: token.colorPrimaryHover\n        },\n        [`${componentCls}-content`]: {\n          [`${componentCls}-icon`]: {\n            color: token.colorTextLightSolid\n          },\n          [`${componentCls}-description`]: {\n            display: 'flex',\n            alignItems: 'center',\n            lineHeight: unit(token.fontSizeLG),\n            color: token.colorTextLightSolid,\n            fontSize: token.fontSizeSM\n          }\n        }\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => ({\n  dotOffsetInCircle: getOffset(token.controlHeightLG / 2),\n  dotOffsetInSquare: getOffset(token.borderRadiusLG)\n});\nexport default genStyleHooks('FloatButton', token => {\n  const {\n    colorTextLightSolid,\n    colorBgElevated,\n    controlHeightLG,\n    marginXXL,\n    marginLG,\n    fontSize,\n    fontSizeIcon,\n    controlItemBgHover,\n    paddingXXS,\n    calc\n  } = token;\n  const floatButtonToken = mergeToken(token, {\n    floatButtonBackgroundColor: colorBgElevated,\n    floatButtonColor: colorTextLightSolid,\n    floatButtonHoverBackgroundColor: controlItemBgHover,\n    floatButtonFontSize: fontSize,\n    floatButtonIconSize: calc(fontSizeIcon).mul(1.5).equal(),\n    floatButtonSize: controlHeightLG,\n    floatButtonInsetBlockEnd: marginXXL,\n    floatButtonInsetInlineEnd: marginLG,\n    floatButtonBodySize: calc(controlHeightLG).sub(calc(paddingXXS).mul(2)).equal(),\n    // 这里的 paddingXXS 是简写，完整逻辑是 (controlHeightLG - (controlHeightLG - paddingXXS * 2)) / 2,\n    floatButtonBodyPadding: paddingXXS,\n    badgeOffset: calc(paddingXXS).mul(1.5).equal()\n  });\n  return [floatButtonGroupStyle(floatButtonToken), sharedFloatButtonStyle(floatButtonToken), initFadeMotion(token), floatButtonGroupMotion(floatButtonToken)];\n}, prepareComponentToken);", "map": {"version": 3, "names": ["unit", "resetComponent", "initFadeMotion", "genStyleHooks", "mergeToken", "getOffset", "floatButtonGroupMotion", "floatButtonGroupStyle", "token", "antCls", "componentCls", "floatButtonSize", "margin", "borderRadiusLG", "borderRadiusSM", "badgeOffset", "floatButtonBodyPadding", "zIndexPopupBase", "calc", "groupPrefixCls", "Object", "assign", "zIndex", "display", "flexDirection", "alignItems", "justifyContent", "border", "position", "height", "boxShadow", "min<PERSON><PERSON><PERSON>", "minHeight", "insetInlineEnd", "floatButtonInsetInlineEnd", "bottom", "floatButtonInsetBlockEnd", "borderRadius", "direction", "top", "add", "equal", "content", "width", "mul", "left", "_skip_check_", "value", "right", "gap", "padding", "borderStartStartRadius", "borderStartEndRadius", "borderEndStartRadius", "borderEndEndRadius", "borderBottom", "lineWidth", "lineType", "colorSplit", "boxShadowSecondary", "floatButtonBodySize", "borderInlineEnd", "sharedFloatButtonStyle", "floatButtonIconSize", "dotOffsetInSquare", "dotOffsetInCircle", "cursor", "inset", "transform", "transform<PERSON><PERSON>in", "transition", "motionDurationMid", "overflow", "textAlign", "div", "fontSize", "lineHeight", "backgroundColor", "floatButtonBackgroundColor", "colorFillContent", "color", "colorText", "fontSizeLG", "fontSizeSM", "colorPrimary", "colorPrimaryHover", "colorTextLightSolid", "prepareComponentToken", "controlHeightLG", "colorBgElevated", "marginXXL", "marginLG", "fontSizeIcon", "controlItemBgHover", "paddingXXS", "floatButtonToken", "floatButtonColor", "floatButtonHoverBackgroundColor", "floatButtonFontSize", "sub"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/float-button/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { initFadeMotion } from '../../style/motion/fade';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport getOffset from '../util';\nimport floatButtonGroupMotion from './keyframes';\n// ============================== Group ==============================\nconst floatButtonGroupStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    floatButtonSize,\n    margin,\n    borderRadiusLG,\n    borderRadiusSM,\n    badgeOffset,\n    floatButtonBodyPadding,\n    zIndexPopupBase,\n    calc\n  } = token;\n  const groupPrefixCls = `${componentCls}-group`;\n  return {\n    [groupPrefixCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      zIndex: zIndexPopupBase,\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      border: 'none',\n      position: 'fixed',\n      height: 'auto',\n      boxShadow: 'none',\n      minWidth: floatButtonSize,\n      minHeight: floatButtonSize,\n      insetInlineEnd: token.floatButtonInsetInlineEnd,\n      bottom: token.floatButtonInsetBlockEnd,\n      borderRadius: borderRadiusLG,\n      [`${groupPrefixCls}-wrap`]: {\n        zIndex: -1,\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        position: 'absolute'\n      },\n      [`&${groupPrefixCls}-rtl`]: {\n        direction: 'rtl'\n      },\n      [componentCls]: {\n        position: 'static'\n      }\n    }),\n    [`${groupPrefixCls}-top > ${groupPrefixCls}-wrap`]: {\n      flexDirection: 'column',\n      top: 'auto',\n      bottom: calc(floatButtonSize).add(margin).equal(),\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        width: '100%',\n        height: margin,\n        bottom: calc(margin).mul(-1).equal()\n      }\n    },\n    [`${groupPrefixCls}-bottom > ${groupPrefixCls}-wrap`]: {\n      flexDirection: 'column',\n      top: calc(floatButtonSize).add(margin).equal(),\n      bottom: 'auto',\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        width: '100%',\n        height: margin,\n        top: calc(margin).mul(-1).equal()\n      }\n    },\n    [`${groupPrefixCls}-right > ${groupPrefixCls}-wrap`]: {\n      flexDirection: 'row',\n      left: {\n        _skip_check_: true,\n        value: calc(floatButtonSize).add(margin).equal()\n      },\n      right: {\n        _skip_check_: true,\n        value: 'auto'\n      },\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        width: margin,\n        height: '100%',\n        left: {\n          _skip_check_: true,\n          value: calc(margin).mul(-1).equal()\n        }\n      }\n    },\n    [`${groupPrefixCls}-left > ${groupPrefixCls}-wrap`]: {\n      flexDirection: 'row',\n      left: {\n        _skip_check_: true,\n        value: 'auto'\n      },\n      right: {\n        _skip_check_: true,\n        value: calc(floatButtonSize).add(margin).equal()\n      },\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        width: margin,\n        height: '100%',\n        right: {\n          _skip_check_: true,\n          value: calc(margin).mul(-1).equal()\n        }\n      }\n    },\n    [`${groupPrefixCls}-circle`]: {\n      gap: margin,\n      [`${groupPrefixCls}-wrap`]: {\n        gap: margin\n      }\n    },\n    [`${groupPrefixCls}-square`]: {\n      [`${componentCls}-square`]: {\n        padding: 0,\n        borderRadius: 0,\n        [`&${groupPrefixCls}-trigger`]: {\n          borderRadius: borderRadiusLG\n        },\n        '&:first-child': {\n          borderStartStartRadius: borderRadiusLG,\n          borderStartEndRadius: borderRadiusLG\n        },\n        '&:last-child': {\n          borderEndStartRadius: borderRadiusLG,\n          borderEndEndRadius: borderRadiusLG\n        },\n        '&:not(:last-child)': {\n          borderBottom: `${unit(token.lineWidth)} ${token.lineType} ${token.colorSplit}`\n        },\n        [`${antCls}-badge`]: {\n          [`${antCls}-badge-count`]: {\n            top: calc(calc(floatButtonBodyPadding).add(badgeOffset)).mul(-1).equal(),\n            insetInlineEnd: calc(calc(floatButtonBodyPadding).add(badgeOffset)).mul(-1).equal()\n          }\n        }\n      },\n      [`${groupPrefixCls}-wrap`]: {\n        borderRadius: borderRadiusLG,\n        boxShadow: token.boxShadowSecondary,\n        [`${componentCls}-square`]: {\n          boxShadow: 'none',\n          borderRadius: 0,\n          padding: floatButtonBodyPadding,\n          [`${componentCls}-body`]: {\n            width: token.floatButtonBodySize,\n            height: token.floatButtonBodySize,\n            borderRadius: borderRadiusSM\n          }\n        }\n      }\n    },\n    [`${groupPrefixCls}-top > ${groupPrefixCls}-wrap, ${groupPrefixCls}-bottom > ${groupPrefixCls}-wrap`]: {\n      [`> ${componentCls}-square`]: {\n        '&:first-child': {\n          borderStartStartRadius: borderRadiusLG,\n          borderStartEndRadius: borderRadiusLG\n        },\n        '&:last-child': {\n          borderEndStartRadius: borderRadiusLG,\n          borderEndEndRadius: borderRadiusLG\n        },\n        '&:not(:last-child)': {\n          borderBottom: `${unit(token.lineWidth)} ${token.lineType} ${token.colorSplit}`\n        }\n      }\n    },\n    [`${groupPrefixCls}-left > ${groupPrefixCls}-wrap, ${groupPrefixCls}-right > ${groupPrefixCls}-wrap`]: {\n      [`> ${componentCls}-square`]: {\n        '&:first-child': {\n          borderStartStartRadius: borderRadiusLG,\n          borderEndStartRadius: borderRadiusLG\n        },\n        '&:last-child': {\n          borderStartEndRadius: borderRadiusLG,\n          borderEndEndRadius: borderRadiusLG\n        },\n        '&:not(:last-child)': {\n          borderInlineEnd: `${unit(token.lineWidth)} ${token.lineType} ${token.colorSplit}`\n        }\n      }\n    },\n    [`${groupPrefixCls}-circle-shadow`]: {\n      boxShadow: 'none'\n    },\n    [`${groupPrefixCls}-square-shadow`]: {\n      boxShadow: token.boxShadowSecondary,\n      [`${componentCls}-square`]: {\n        boxShadow: 'none',\n        padding: floatButtonBodyPadding,\n        [`${componentCls}-body`]: {\n          width: token.floatButtonBodySize,\n          height: token.floatButtonBodySize,\n          borderRadius: borderRadiusSM\n        }\n      }\n    }\n  };\n};\n// ============================== Shared ==============================\nconst sharedFloatButtonStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    floatButtonBodyPadding,\n    floatButtonIconSize,\n    floatButtonSize,\n    borderRadiusLG,\n    badgeOffset,\n    dotOffsetInSquare,\n    dotOffsetInCircle,\n    zIndexPopupBase,\n    calc\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      border: 'none',\n      position: 'fixed',\n      cursor: 'pointer',\n      zIndex: zIndexPopupBase,\n      // Do not remove the 'display: block' here.\n      // Deleting it will cause marginBottom to become ineffective.\n      // Ref: https://github.com/ant-design/ant-design/issues/44700\n      display: 'block',\n      width: floatButtonSize,\n      height: floatButtonSize,\n      insetInlineEnd: token.floatButtonInsetInlineEnd,\n      bottom: token.floatButtonInsetBlockEnd,\n      boxShadow: token.boxShadowSecondary,\n      // Pure Panel\n      '&-pure': {\n        position: 'relative',\n        inset: 'auto'\n      },\n      '&:empty': {\n        display: 'none'\n      },\n      [`${antCls}-badge`]: {\n        width: '100%',\n        height: '100%',\n        [`${antCls}-badge-count`]: {\n          transform: 'translate(0, 0)',\n          transformOrigin: 'center',\n          top: calc(badgeOffset).mul(-1).equal(),\n          insetInlineEnd: calc(badgeOffset).mul(-1).equal()\n        }\n      },\n      [`${componentCls}-body`]: {\n        width: '100%',\n        height: '100%',\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        transition: `all ${token.motionDurationMid}`,\n        [`${componentCls}-content`]: {\n          overflow: 'hidden',\n          textAlign: 'center',\n          minHeight: floatButtonSize,\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'center',\n          alignItems: 'center',\n          padding: `${unit(calc(floatButtonBodyPadding).div(2).equal())} ${unit(floatButtonBodyPadding)}`,\n          [`${componentCls}-icon`]: {\n            textAlign: 'center',\n            margin: 'auto',\n            width: floatButtonIconSize,\n            fontSize: floatButtonIconSize,\n            lineHeight: 1\n          }\n        }\n      }\n    }),\n    [`${componentCls}-rtl`]: {\n      direction: 'rtl'\n    },\n    [`${componentCls}-circle`]: {\n      height: floatButtonSize,\n      borderRadius: '50%',\n      [`${antCls}-badge`]: {\n        [`${antCls}-badge-dot`]: {\n          top: dotOffsetInCircle,\n          insetInlineEnd: dotOffsetInCircle\n        }\n      },\n      [`${componentCls}-body`]: {\n        borderRadius: '50%'\n      }\n    },\n    [`${componentCls}-square`]: {\n      height: 'auto',\n      minHeight: floatButtonSize,\n      borderRadius: borderRadiusLG,\n      [`${antCls}-badge`]: {\n        [`${antCls}-badge-dot`]: {\n          top: dotOffsetInSquare,\n          insetInlineEnd: dotOffsetInSquare\n        }\n      },\n      [`${componentCls}-body`]: {\n        height: 'auto',\n        borderRadius: borderRadiusLG\n      }\n    },\n    [`${componentCls}-default`]: {\n      backgroundColor: token.floatButtonBackgroundColor,\n      transition: `background-color ${token.motionDurationMid}`,\n      [`${componentCls}-body`]: {\n        backgroundColor: token.floatButtonBackgroundColor,\n        transition: `background-color ${token.motionDurationMid}`,\n        '&:hover': {\n          backgroundColor: token.colorFillContent\n        },\n        [`${componentCls}-content`]: {\n          [`${componentCls}-icon`]: {\n            color: token.colorText\n          },\n          [`${componentCls}-description`]: {\n            display: 'flex',\n            alignItems: 'center',\n            lineHeight: unit(token.fontSizeLG),\n            color: token.colorText,\n            fontSize: token.fontSizeSM\n          }\n        }\n      }\n    },\n    [`${componentCls}-primary`]: {\n      backgroundColor: token.colorPrimary,\n      [`${componentCls}-body`]: {\n        backgroundColor: token.colorPrimary,\n        transition: `background-color ${token.motionDurationMid}`,\n        '&:hover': {\n          backgroundColor: token.colorPrimaryHover\n        },\n        [`${componentCls}-content`]: {\n          [`${componentCls}-icon`]: {\n            color: token.colorTextLightSolid\n          },\n          [`${componentCls}-description`]: {\n            display: 'flex',\n            alignItems: 'center',\n            lineHeight: unit(token.fontSizeLG),\n            color: token.colorTextLightSolid,\n            fontSize: token.fontSizeSM\n          }\n        }\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => ({\n  dotOffsetInCircle: getOffset(token.controlHeightLG / 2),\n  dotOffsetInSquare: getOffset(token.borderRadiusLG)\n});\nexport default genStyleHooks('FloatButton', token => {\n  const {\n    colorTextLightSolid,\n    colorBgElevated,\n    controlHeightLG,\n    marginXXL,\n    marginLG,\n    fontSize,\n    fontSizeIcon,\n    controlItemBgHover,\n    paddingXXS,\n    calc\n  } = token;\n  const floatButtonToken = mergeToken(token, {\n    floatButtonBackgroundColor: colorBgElevated,\n    floatButtonColor: colorTextLightSolid,\n    floatButtonHoverBackgroundColor: controlItemBgHover,\n    floatButtonFontSize: fontSize,\n    floatButtonIconSize: calc(fontSizeIcon).mul(1.5).equal(),\n    floatButtonSize: controlHeightLG,\n    floatButtonInsetBlockEnd: marginXXL,\n    floatButtonInsetInlineEnd: marginLG,\n    floatButtonBodySize: calc(controlHeightLG).sub(calc(paddingXXS).mul(2)).equal(),\n    // 这里的 paddingXXS 是简写，完整逻辑是 (controlHeightLG - (controlHeightLG - paddingXXS * 2)) / 2,\n    floatButtonBodyPadding: paddingXXS,\n    badgeOffset: calc(paddingXXS).mul(1.5).equal()\n  });\n  return [floatButtonGroupStyle(floatButtonToken), sharedFloatButtonStyle(floatButtonToken), initFadeMotion(token), floatButtonGroupMotion(floatButtonToken)];\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,OAAOC,SAAS,MAAM,SAAS;AAC/B,OAAOC,sBAAsB,MAAM,aAAa;AAChD;AACA,MAAMC,qBAAqB,GAAGC,KAAK,IAAI;EACrC,MAAM;IACJC,MAAM;IACNC,YAAY;IACZC,eAAe;IACfC,MAAM;IACNC,cAAc;IACdC,cAAc;IACdC,WAAW;IACXC,sBAAsB;IACtBC,eAAe;IACfC;EACF,CAAC,GAAGV,KAAK;EACT,MAAMW,cAAc,GAAG,GAAGT,YAAY,QAAQ;EAC9C,OAAO;IACL,CAACS,cAAc,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpB,cAAc,CAACO,KAAK,CAAC,CAAC,EAAE;MACxEc,MAAM,EAAEL,eAAe;MACvBM,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,MAAM;MACjBC,QAAQ,EAAEpB,eAAe;MACzBqB,SAAS,EAAErB,eAAe;MAC1BsB,cAAc,EAAEzB,KAAK,CAAC0B,yBAAyB;MAC/CC,MAAM,EAAE3B,KAAK,CAAC4B,wBAAwB;MACtCC,YAAY,EAAExB,cAAc;MAC5B,CAAC,GAAGM,cAAc,OAAO,GAAG;QAC1BG,MAAM,EAAE,CAAC,CAAC;QACVC,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,QAAQ;QACxBD,UAAU,EAAE,QAAQ;QACpBG,QAAQ,EAAE;MACZ,CAAC;MACD,CAAC,IAAIT,cAAc,MAAM,GAAG;QAC1BmB,SAAS,EAAE;MACb,CAAC;MACD,CAAC5B,YAAY,GAAG;QACdkB,QAAQ,EAAE;MACZ;IACF,CAAC,CAAC;IACF,CAAC,GAAGT,cAAc,UAAUA,cAAc,OAAO,GAAG;MAClDK,aAAa,EAAE,QAAQ;MACvBe,GAAG,EAAE,MAAM;MACXJ,MAAM,EAAEjB,IAAI,CAACP,eAAe,CAAC,CAAC6B,GAAG,CAAC5B,MAAM,CAAC,CAAC6B,KAAK,CAAC,CAAC;MACjD,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbd,QAAQ,EAAE,UAAU;QACpBe,KAAK,EAAE,MAAM;QACbd,MAAM,EAAEjB,MAAM;QACduB,MAAM,EAAEjB,IAAI,CAACN,MAAM,CAAC,CAACgC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACH,KAAK,CAAC;MACrC;IACF,CAAC;IACD,CAAC,GAAGtB,cAAc,aAAaA,cAAc,OAAO,GAAG;MACrDK,aAAa,EAAE,QAAQ;MACvBe,GAAG,EAAErB,IAAI,CAACP,eAAe,CAAC,CAAC6B,GAAG,CAAC5B,MAAM,CAAC,CAAC6B,KAAK,CAAC,CAAC;MAC9CN,MAAM,EAAE,MAAM;MACd,UAAU,EAAE;QACVO,OAAO,EAAE,IAAI;QACbd,QAAQ,EAAE,UAAU;QACpBe,KAAK,EAAE,MAAM;QACbd,MAAM,EAAEjB,MAAM;QACd2B,GAAG,EAAErB,IAAI,CAACN,MAAM,CAAC,CAACgC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACH,KAAK,CAAC;MAClC;IACF,CAAC;IACD,CAAC,GAAGtB,cAAc,YAAYA,cAAc,OAAO,GAAG;MACpDK,aAAa,EAAE,KAAK;MACpBqB,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI;QAClBC,KAAK,EAAE7B,IAAI,CAACP,eAAe,CAAC,CAAC6B,GAAG,CAAC5B,MAAM,CAAC,CAAC6B,KAAK,CAAC;MACjD,CAAC;MACDO,KAAK,EAAE;QACLF,YAAY,EAAE,IAAI;QAClBC,KAAK,EAAE;MACT,CAAC;MACD,UAAU,EAAE;QACVL,OAAO,EAAE,IAAI;QACbd,QAAQ,EAAE,UAAU;QACpBe,KAAK,EAAE/B,MAAM;QACbiB,MAAM,EAAE,MAAM;QACdgB,IAAI,EAAE;UACJC,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE7B,IAAI,CAACN,MAAM,CAAC,CAACgC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACH,KAAK,CAAC;QACpC;MACF;IACF,CAAC;IACD,CAAC,GAAGtB,cAAc,WAAWA,cAAc,OAAO,GAAG;MACnDK,aAAa,EAAE,KAAK;MACpBqB,IAAI,EAAE;QACJC,YAAY,EAAE,IAAI;QAClBC,KAAK,EAAE;MACT,CAAC;MACDC,KAAK,EAAE;QACLF,YAAY,EAAE,IAAI;QAClBC,KAAK,EAAE7B,IAAI,CAACP,eAAe,CAAC,CAAC6B,GAAG,CAAC5B,MAAM,CAAC,CAAC6B,KAAK,CAAC;MACjD,CAAC;MACD,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbd,QAAQ,EAAE,UAAU;QACpBe,KAAK,EAAE/B,MAAM;QACbiB,MAAM,EAAE,MAAM;QACdmB,KAAK,EAAE;UACLF,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE7B,IAAI,CAACN,MAAM,CAAC,CAACgC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACH,KAAK,CAAC;QACpC;MACF;IACF,CAAC;IACD,CAAC,GAAGtB,cAAc,SAAS,GAAG;MAC5B8B,GAAG,EAAErC,MAAM;MACX,CAAC,GAAGO,cAAc,OAAO,GAAG;QAC1B8B,GAAG,EAAErC;MACP;IACF,CAAC;IACD,CAAC,GAAGO,cAAc,SAAS,GAAG;MAC5B,CAAC,GAAGT,YAAY,SAAS,GAAG;QAC1BwC,OAAO,EAAE,CAAC;QACVb,YAAY,EAAE,CAAC;QACf,CAAC,IAAIlB,cAAc,UAAU,GAAG;UAC9BkB,YAAY,EAAExB;QAChB,CAAC;QACD,eAAe,EAAE;UACfsC,sBAAsB,EAAEtC,cAAc;UACtCuC,oBAAoB,EAAEvC;QACxB,CAAC;QACD,cAAc,EAAE;UACdwC,oBAAoB,EAAExC,cAAc;UACpCyC,kBAAkB,EAAEzC;QACtB,CAAC;QACD,oBAAoB,EAAE;UACpB0C,YAAY,EAAE,GAAGvD,IAAI,CAACQ,KAAK,CAACgD,SAAS,CAAC,IAAIhD,KAAK,CAACiD,QAAQ,IAAIjD,KAAK,CAACkD,UAAU;QAC9E,CAAC;QACD,CAAC,GAAGjD,MAAM,QAAQ,GAAG;UACnB,CAAC,GAAGA,MAAM,cAAc,GAAG;YACzB8B,GAAG,EAAErB,IAAI,CAACA,IAAI,CAACF,sBAAsB,CAAC,CAACwB,GAAG,CAACzB,WAAW,CAAC,CAAC,CAAC6B,GAAG,CAAC,CAAC,CAAC,CAAC,CAACH,KAAK,CAAC,CAAC;YACxER,cAAc,EAAEf,IAAI,CAACA,IAAI,CAACF,sBAAsB,CAAC,CAACwB,GAAG,CAACzB,WAAW,CAAC,CAAC,CAAC6B,GAAG,CAAC,CAAC,CAAC,CAAC,CAACH,KAAK,CAAC;UACpF;QACF;MACF,CAAC;MACD,CAAC,GAAGtB,cAAc,OAAO,GAAG;QAC1BkB,YAAY,EAAExB,cAAc;QAC5BiB,SAAS,EAAEtB,KAAK,CAACmD,kBAAkB;QACnC,CAAC,GAAGjD,YAAY,SAAS,GAAG;UAC1BoB,SAAS,EAAE,MAAM;UACjBO,YAAY,EAAE,CAAC;UACfa,OAAO,EAAElC,sBAAsB;UAC/B,CAAC,GAAGN,YAAY,OAAO,GAAG;YACxBiC,KAAK,EAAEnC,KAAK,CAACoD,mBAAmB;YAChC/B,MAAM,EAAErB,KAAK,CAACoD,mBAAmB;YACjCvB,YAAY,EAAEvB;UAChB;QACF;MACF;IACF,CAAC;IACD,CAAC,GAAGK,cAAc,UAAUA,cAAc,UAAUA,cAAc,aAAaA,cAAc,OAAO,GAAG;MACrG,CAAC,KAAKT,YAAY,SAAS,GAAG;QAC5B,eAAe,EAAE;UACfyC,sBAAsB,EAAEtC,cAAc;UACtCuC,oBAAoB,EAAEvC;QACxB,CAAC;QACD,cAAc,EAAE;UACdwC,oBAAoB,EAAExC,cAAc;UACpCyC,kBAAkB,EAAEzC;QACtB,CAAC;QACD,oBAAoB,EAAE;UACpB0C,YAAY,EAAE,GAAGvD,IAAI,CAACQ,KAAK,CAACgD,SAAS,CAAC,IAAIhD,KAAK,CAACiD,QAAQ,IAAIjD,KAAK,CAACkD,UAAU;QAC9E;MACF;IACF,CAAC;IACD,CAAC,GAAGvC,cAAc,WAAWA,cAAc,UAAUA,cAAc,YAAYA,cAAc,OAAO,GAAG;MACrG,CAAC,KAAKT,YAAY,SAAS,GAAG;QAC5B,eAAe,EAAE;UACfyC,sBAAsB,EAAEtC,cAAc;UACtCwC,oBAAoB,EAAExC;QACxB,CAAC;QACD,cAAc,EAAE;UACduC,oBAAoB,EAAEvC,cAAc;UACpCyC,kBAAkB,EAAEzC;QACtB,CAAC;QACD,oBAAoB,EAAE;UACpBgD,eAAe,EAAE,GAAG7D,IAAI,CAACQ,KAAK,CAACgD,SAAS,CAAC,IAAIhD,KAAK,CAACiD,QAAQ,IAAIjD,KAAK,CAACkD,UAAU;QACjF;MACF;IACF,CAAC;IACD,CAAC,GAAGvC,cAAc,gBAAgB,GAAG;MACnCW,SAAS,EAAE;IACb,CAAC;IACD,CAAC,GAAGX,cAAc,gBAAgB,GAAG;MACnCW,SAAS,EAAEtB,KAAK,CAACmD,kBAAkB;MACnC,CAAC,GAAGjD,YAAY,SAAS,GAAG;QAC1BoB,SAAS,EAAE,MAAM;QACjBoB,OAAO,EAAElC,sBAAsB;QAC/B,CAAC,GAAGN,YAAY,OAAO,GAAG;UACxBiC,KAAK,EAAEnC,KAAK,CAACoD,mBAAmB;UAChC/B,MAAM,EAAErB,KAAK,CAACoD,mBAAmB;UACjCvB,YAAY,EAAEvB;QAChB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD;AACA,MAAMgD,sBAAsB,GAAGtD,KAAK,IAAI;EACtC,MAAM;IACJC,MAAM;IACNC,YAAY;IACZM,sBAAsB;IACtB+C,mBAAmB;IACnBpD,eAAe;IACfE,cAAc;IACdE,WAAW;IACXiD,iBAAiB;IACjBC,iBAAiB;IACjBhD,eAAe;IACfC;EACF,CAAC,GAAGV,KAAK;EACT,OAAO;IACL,CAACE,YAAY,GAAGU,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEpB,cAAc,CAACO,KAAK,CAAC,CAAC,EAAE;MACtEmB,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE,OAAO;MACjBsC,MAAM,EAAE,SAAS;MACjB5C,MAAM,EAAEL,eAAe;MACvB;MACA;MACA;MACAM,OAAO,EAAE,OAAO;MAChBoB,KAAK,EAAEhC,eAAe;MACtBkB,MAAM,EAAElB,eAAe;MACvBsB,cAAc,EAAEzB,KAAK,CAAC0B,yBAAyB;MAC/CC,MAAM,EAAE3B,KAAK,CAAC4B,wBAAwB;MACtCN,SAAS,EAAEtB,KAAK,CAACmD,kBAAkB;MACnC;MACA,QAAQ,EAAE;QACR/B,QAAQ,EAAE,UAAU;QACpBuC,KAAK,EAAE;MACT,CAAC;MACD,SAAS,EAAE;QACT5C,OAAO,EAAE;MACX,CAAC;MACD,CAAC,GAAGd,MAAM,QAAQ,GAAG;QACnBkC,KAAK,EAAE,MAAM;QACbd,MAAM,EAAE,MAAM;QACd,CAAC,GAAGpB,MAAM,cAAc,GAAG;UACzB2D,SAAS,EAAE,iBAAiB;UAC5BC,eAAe,EAAE,QAAQ;UACzB9B,GAAG,EAAErB,IAAI,CAACH,WAAW,CAAC,CAAC6B,GAAG,CAAC,CAAC,CAAC,CAAC,CAACH,KAAK,CAAC,CAAC;UACtCR,cAAc,EAAEf,IAAI,CAACH,WAAW,CAAC,CAAC6B,GAAG,CAAC,CAAC,CAAC,CAAC,CAACH,KAAK,CAAC;QAClD;MACF,CAAC;MACD,CAAC,GAAG/B,YAAY,OAAO,GAAG;QACxBiC,KAAK,EAAE,MAAM;QACbd,MAAM,EAAE,MAAM;QACdN,OAAO,EAAE,MAAM;QACfG,cAAc,EAAE,QAAQ;QACxBD,UAAU,EAAE,QAAQ;QACpB6C,UAAU,EAAE,OAAO9D,KAAK,CAAC+D,iBAAiB,EAAE;QAC5C,CAAC,GAAG7D,YAAY,UAAU,GAAG;UAC3B8D,QAAQ,EAAE,QAAQ;UAClBC,SAAS,EAAE,QAAQ;UACnBzC,SAAS,EAAErB,eAAe;UAC1BY,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBE,cAAc,EAAE,QAAQ;UACxBD,UAAU,EAAE,QAAQ;UACpByB,OAAO,EAAE,GAAGlD,IAAI,CAACkB,IAAI,CAACF,sBAAsB,CAAC,CAAC0D,GAAG,CAAC,CAAC,CAAC,CAACjC,KAAK,CAAC,CAAC,CAAC,IAAIzC,IAAI,CAACgB,sBAAsB,CAAC,EAAE;UAC/F,CAAC,GAAGN,YAAY,OAAO,GAAG;YACxB+D,SAAS,EAAE,QAAQ;YACnB7D,MAAM,EAAE,MAAM;YACd+B,KAAK,EAAEoB,mBAAmB;YAC1BY,QAAQ,EAAEZ,mBAAmB;YAC7Ba,UAAU,EAAE;UACd;QACF;MACF;IACF,CAAC,CAAC;IACF,CAAC,GAAGlE,YAAY,MAAM,GAAG;MACvB4B,SAAS,EAAE;IACb,CAAC;IACD,CAAC,GAAG5B,YAAY,SAAS,GAAG;MAC1BmB,MAAM,EAAElB,eAAe;MACvB0B,YAAY,EAAE,KAAK;MACnB,CAAC,GAAG5B,MAAM,QAAQ,GAAG;QACnB,CAAC,GAAGA,MAAM,YAAY,GAAG;UACvB8B,GAAG,EAAE0B,iBAAiB;UACtBhC,cAAc,EAAEgC;QAClB;MACF,CAAC;MACD,CAAC,GAAGvD,YAAY,OAAO,GAAG;QACxB2B,YAAY,EAAE;MAChB;IACF,CAAC;IACD,CAAC,GAAG3B,YAAY,SAAS,GAAG;MAC1BmB,MAAM,EAAE,MAAM;MACdG,SAAS,EAAErB,eAAe;MAC1B0B,YAAY,EAAExB,cAAc;MAC5B,CAAC,GAAGJ,MAAM,QAAQ,GAAG;QACnB,CAAC,GAAGA,MAAM,YAAY,GAAG;UACvB8B,GAAG,EAAEyB,iBAAiB;UACtB/B,cAAc,EAAE+B;QAClB;MACF,CAAC;MACD,CAAC,GAAGtD,YAAY,OAAO,GAAG;QACxBmB,MAAM,EAAE,MAAM;QACdQ,YAAY,EAAExB;MAChB;IACF,CAAC;IACD,CAAC,GAAGH,YAAY,UAAU,GAAG;MAC3BmE,eAAe,EAAErE,KAAK,CAACsE,0BAA0B;MACjDR,UAAU,EAAE,oBAAoB9D,KAAK,CAAC+D,iBAAiB,EAAE;MACzD,CAAC,GAAG7D,YAAY,OAAO,GAAG;QACxBmE,eAAe,EAAErE,KAAK,CAACsE,0BAA0B;QACjDR,UAAU,EAAE,oBAAoB9D,KAAK,CAAC+D,iBAAiB,EAAE;QACzD,SAAS,EAAE;UACTM,eAAe,EAAErE,KAAK,CAACuE;QACzB,CAAC;QACD,CAAC,GAAGrE,YAAY,UAAU,GAAG;UAC3B,CAAC,GAAGA,YAAY,OAAO,GAAG;YACxBsE,KAAK,EAAExE,KAAK,CAACyE;UACf,CAAC;UACD,CAAC,GAAGvE,YAAY,cAAc,GAAG;YAC/Ba,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBmD,UAAU,EAAE5E,IAAI,CAACQ,KAAK,CAAC0E,UAAU,CAAC;YAClCF,KAAK,EAAExE,KAAK,CAACyE,SAAS;YACtBN,QAAQ,EAAEnE,KAAK,CAAC2E;UAClB;QACF;MACF;IACF,CAAC;IACD,CAAC,GAAGzE,YAAY,UAAU,GAAG;MAC3BmE,eAAe,EAAErE,KAAK,CAAC4E,YAAY;MACnC,CAAC,GAAG1E,YAAY,OAAO,GAAG;QACxBmE,eAAe,EAAErE,KAAK,CAAC4E,YAAY;QACnCd,UAAU,EAAE,oBAAoB9D,KAAK,CAAC+D,iBAAiB,EAAE;QACzD,SAAS,EAAE;UACTM,eAAe,EAAErE,KAAK,CAAC6E;QACzB,CAAC;QACD,CAAC,GAAG3E,YAAY,UAAU,GAAG;UAC3B,CAAC,GAAGA,YAAY,OAAO,GAAG;YACxBsE,KAAK,EAAExE,KAAK,CAAC8E;UACf,CAAC;UACD,CAAC,GAAG5E,YAAY,cAAc,GAAG;YAC/Ba,OAAO,EAAE,MAAM;YACfE,UAAU,EAAE,QAAQ;YACpBmD,UAAU,EAAE5E,IAAI,CAACQ,KAAK,CAAC0E,UAAU,CAAC;YAClCF,KAAK,EAAExE,KAAK,CAAC8E,mBAAmB;YAChCX,QAAQ,EAAEnE,KAAK,CAAC2E;UAClB;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD;AACA,OAAO,MAAMI,qBAAqB,GAAG/E,KAAK,KAAK;EAC7CyD,iBAAiB,EAAE5D,SAAS,CAACG,KAAK,CAACgF,eAAe,GAAG,CAAC,CAAC;EACvDxB,iBAAiB,EAAE3D,SAAS,CAACG,KAAK,CAACK,cAAc;AACnD,CAAC,CAAC;AACF,eAAeV,aAAa,CAAC,aAAa,EAAEK,KAAK,IAAI;EACnD,MAAM;IACJ8E,mBAAmB;IACnBG,eAAe;IACfD,eAAe;IACfE,SAAS;IACTC,QAAQ;IACRhB,QAAQ;IACRiB,YAAY;IACZC,kBAAkB;IAClBC,UAAU;IACV5E;EACF,CAAC,GAAGV,KAAK;EACT,MAAMuF,gBAAgB,GAAG3F,UAAU,CAACI,KAAK,EAAE;IACzCsE,0BAA0B,EAAEW,eAAe;IAC3CO,gBAAgB,EAAEV,mBAAmB;IACrCW,+BAA+B,EAAEJ,kBAAkB;IACnDK,mBAAmB,EAAEvB,QAAQ;IAC7BZ,mBAAmB,EAAE7C,IAAI,CAAC0E,YAAY,CAAC,CAAChD,GAAG,CAAC,GAAG,CAAC,CAACH,KAAK,CAAC,CAAC;IACxD9B,eAAe,EAAE6E,eAAe;IAChCpD,wBAAwB,EAAEsD,SAAS;IACnCxD,yBAAyB,EAAEyD,QAAQ;IACnC/B,mBAAmB,EAAE1C,IAAI,CAACsE,eAAe,CAAC,CAACW,GAAG,CAACjF,IAAI,CAAC4E,UAAU,CAAC,CAAClD,GAAG,CAAC,CAAC,CAAC,CAAC,CAACH,KAAK,CAAC,CAAC;IAC/E;IACAzB,sBAAsB,EAAE8E,UAAU;IAClC/E,WAAW,EAAEG,IAAI,CAAC4E,UAAU,CAAC,CAAClD,GAAG,CAAC,GAAG,CAAC,CAACH,KAAK,CAAC;EAC/C,CAAC,CAAC;EACF,OAAO,CAAClC,qBAAqB,CAACwF,gBAAgB,CAAC,EAAEjC,sBAAsB,CAACiC,gBAAgB,CAAC,EAAE7F,cAAc,CAACM,KAAK,CAAC,EAAEF,sBAAsB,CAACyF,gBAAgB,CAAC,CAAC;AAC7J,CAAC,EAAER,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}