{"ast": null, "code": "import * as React from 'react';\nexport default function useResizable(items, pxSizes, isRTL) {\n  return React.useMemo(() => {\n    const resizeInfos = [];\n    for (let i = 0; i < items.length - 1; i += 1) {\n      const prevItem = items[i];\n      const nextItem = items[i + 1];\n      const prevSize = pxSizes[i];\n      const nextSize = pxSizes[i + 1];\n      const {\n        resizable: prevResizable = true,\n        min: prevMin,\n        collapsible: prevCollapsible\n      } = prevItem;\n      const {\n        resizable: nextResizable = true,\n        min: nextMin,\n        collapsible: nextCollapsible\n      } = nextItem;\n      const mergedResizable =\n      // Both need to be resizable\n      prevResizable && nextResizable && (\n      // Prev is not collapsed and limit min size\n      prevSize !== 0 || !prevMin) && (\n      // Next is not collapsed and limit min size\n      nextSize !== 0 || !nextMin);\n      const startCollapsible =\n      // Self is collapsible\n      prevCollapsible.end && prevSize > 0 ||\n      // Collapsed and can be collapsed\n      nextCollapsible.start && nextSize === 0 && prevSize > 0;\n      const endCollapsible =\n      // Self is collapsible\n      nextCollapsible.start && nextSize > 0 ||\n      // Collapsed and can be collapsed\n      prevCollapsible.end && prevSize === 0 && nextSize > 0;\n      resizeInfos[i] = {\n        resizable: mergedResizable,\n        startCollapsible: !!(isRTL ? endCollapsible : startCollapsible),\n        endCollapsible: !!(isRTL ? startCollapsible : endCollapsible)\n      };\n    }\n    return resizeInfos;\n  }, [pxSizes, items]);\n}", "map": {"version": 3, "names": ["React", "useResizable", "items", "pxSizes", "isRTL", "useMemo", "resizeInfos", "i", "length", "prevItem", "nextItem", "prevSize", "nextSize", "resizable", "prevResizable", "min", "prevMin", "collapsible", "prevCollapsible", "nextResizable", "nextMin", "nextCollapsible", "mergedResizable", "startCollapsible", "end", "start", "endCollapsible"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/splitter/hooks/useResizable.js"], "sourcesContent": ["import * as React from 'react';\nexport default function useResizable(items, pxSizes, isRTL) {\n  return React.useMemo(() => {\n    const resizeInfos = [];\n    for (let i = 0; i < items.length - 1; i += 1) {\n      const prevItem = items[i];\n      const nextItem = items[i + 1];\n      const prevSize = pxSizes[i];\n      const nextSize = pxSizes[i + 1];\n      const {\n        resizable: prevResizable = true,\n        min: prevMin,\n        collapsible: prevCollapsible\n      } = prevItem;\n      const {\n        resizable: nextResizable = true,\n        min: nextMin,\n        collapsible: nextCollapsible\n      } = nextItem;\n      const mergedResizable =\n      // Both need to be resizable\n      prevResizable && nextResizable && (\n      // Prev is not collapsed and limit min size\n      prevSize !== 0 || !prevMin) && (\n      // Next is not collapsed and limit min size\n      nextSize !== 0 || !nextMin);\n      const startCollapsible =\n      // Self is collapsible\n      prevCollapsible.end && prevSize > 0 ||\n      // Collapsed and can be collapsed\n      nextCollapsible.start && nextSize === 0 && prevSize > 0;\n      const endCollapsible =\n      // Self is collapsible\n      nextCollapsible.start && nextSize > 0 ||\n      // Collapsed and can be collapsed\n      prevCollapsible.end && prevSize === 0 && nextSize > 0;\n      resizeInfos[i] = {\n        resizable: mergedResizable,\n        startCollapsible: !!(isRTL ? endCollapsible : startCollapsible),\n        endCollapsible: !!(isRTL ? startCollapsible : endCollapsible)\n      };\n    }\n    return resizeInfos;\n  }, [pxSizes, items]);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,YAAYA,CAACC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAE;EAC1D,OAAOJ,KAAK,CAACK,OAAO,CAAC,MAAM;IACzB,MAAMC,WAAW,GAAG,EAAE;IACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACM,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE;MAC5C,MAAME,QAAQ,GAAGP,KAAK,CAACK,CAAC,CAAC;MACzB,MAAMG,QAAQ,GAAGR,KAAK,CAACK,CAAC,GAAG,CAAC,CAAC;MAC7B,MAAMI,QAAQ,GAAGR,OAAO,CAACI,CAAC,CAAC;MAC3B,MAAMK,QAAQ,GAAGT,OAAO,CAACI,CAAC,GAAG,CAAC,CAAC;MAC/B,MAAM;QACJM,SAAS,EAAEC,aAAa,GAAG,IAAI;QAC/BC,GAAG,EAAEC,OAAO;QACZC,WAAW,EAAEC;MACf,CAAC,GAAGT,QAAQ;MACZ,MAAM;QACJI,SAAS,EAAEM,aAAa,GAAG,IAAI;QAC/BJ,GAAG,EAAEK,OAAO;QACZH,WAAW,EAAEI;MACf,CAAC,GAAGX,QAAQ;MACZ,MAAMY,eAAe;MACrB;MACAR,aAAa,IAAIK,aAAa;MAC9B;MACAR,QAAQ,KAAK,CAAC,IAAI,CAACK,OAAO,CAAC;MAC3B;MACAJ,QAAQ,KAAK,CAAC,IAAI,CAACQ,OAAO,CAAC;MAC3B,MAAMG,gBAAgB;MACtB;MACAL,eAAe,CAACM,GAAG,IAAIb,QAAQ,GAAG,CAAC;MACnC;MACAU,eAAe,CAACI,KAAK,IAAIb,QAAQ,KAAK,CAAC,IAAID,QAAQ,GAAG,CAAC;MACvD,MAAMe,cAAc;MACpB;MACAL,eAAe,CAACI,KAAK,IAAIb,QAAQ,GAAG,CAAC;MACrC;MACAM,eAAe,CAACM,GAAG,IAAIb,QAAQ,KAAK,CAAC,IAAIC,QAAQ,GAAG,CAAC;MACrDN,WAAW,CAACC,CAAC,CAAC,GAAG;QACfM,SAAS,EAAES,eAAe;QAC1BC,gBAAgB,EAAE,CAAC,EAAEnB,KAAK,GAAGsB,cAAc,GAAGH,gBAAgB,CAAC;QAC/DG,cAAc,EAAE,CAAC,EAAEtB,KAAK,GAAGmB,gBAAgB,GAAGG,cAAc;MAC9D,CAAC;IACH;IACA,OAAOpB,WAAW;EACpB,CAAC,EAAE,CAACH,OAAO,EAAED,KAAK,CAAC,CAAC;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}