{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport AimOutlinedSvg from \"@ant-design/icons-svg/es/asn/AimOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar AimOutlined = function AimOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: AimOutlinedSvg\n  }));\n};\n\n/**![aim](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik05NTIgNDc0SDgyOS44QzgxMi41IDMyNy42IDY5Ni40IDIxMS41IDU1MCAxOTQuMlY3MmMwLTQuNC0zLjYtOC04LThoLTYwYy00LjQgMC04IDMuNi04IDh2MTIyLjJDMzI3LjYgMjExLjUgMjExLjUgMzI3LjYgMTk0LjIgNDc0SDcyYy00LjQgMC04IDMuNi04IDh2NjBjMCA0LjQgMy42IDggOCA4aDEyMi4yQzIxMS41IDY5Ni40IDMyNy42IDgxMi41IDQ3NCA4MjkuOFY5NTJjMCA0LjQgMy42IDggOCA4aDYwYzQuNCAwIDgtMy42IDgtOFY4MjkuOEM2OTYuNCA4MTIuNSA4MTIuNSA2OTYuNCA4MjkuOCA1NTBIOTUyYzQuNCAwIDgtMy42IDgtOHYtNjBjMC00LjQtMy42LTgtOC04ek01MTIgNzU2Yy0xMzQuOCAwLTI0NC0xMDkuMi0yNDQtMjQ0czEwOS4yLTI0NCAyNDQtMjQ0IDI0NCAxMDkuMiAyNDQgMjQ0LTEwOS4yIDI0NC0yNDQgMjQ0eiIgLz48cGF0aCBkPSJNNTEyIDM5MmMtMzIuMSAwLTYyLjEgMTIuNC04NC44IDM1LjItMjIuNyAyMi43LTM1LjIgNTIuNy0zNS4yIDg0LjhzMTIuNSA2Mi4xIDM1LjIgODQuOEM0NDkuOSA2MTkuNCA0ODAgNjMyIDUxMiA2MzJzNjIuMS0xMi41IDg0LjgtMzUuMkM2MTkuNCA1NzQuMSA2MzIgNTQ0IDYzMiA1MTJzLTEyLjUtNjIuMS0zNS4yLTg0LjhBMTE4LjU3IDExOC41NyAwIDAwNTEyIDM5MnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(AimOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'AimOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "AimOutlinedSvg", "AntdIcon", "AimOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@ant-design/icons/es/icons/AimOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport AimOutlinedSvg from \"@ant-design/icons-svg/es/asn/AimOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar AimOutlined = function AimOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: AimOutlinedSvg\n  }));\n};\n\n/**![aim](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik05NTIgNDc0SDgyOS44QzgxMi41IDMyNy42IDY5Ni40IDIxMS41IDU1MCAxOTQuMlY3MmMwLTQuNC0zLjYtOC04LThoLTYwYy00LjQgMC04IDMuNi04IDh2MTIyLjJDMzI3LjYgMjExLjUgMjExLjUgMzI3LjYgMTk0LjIgNDc0SDcyYy00LjQgMC04IDMuNi04IDh2NjBjMCA0LjQgMy42IDggOCA4aDEyMi4yQzIxMS41IDY5Ni40IDMyNy42IDgxMi41IDQ3NCA4MjkuOFY5NTJjMCA0LjQgMy42IDggOCA4aDYwYzQuNCAwIDgtMy42IDgtOFY4MjkuOEM2OTYuNCA4MTIuNSA4MTIuNSA2OTYuNCA4MjkuOCA1NTBIOTUyYzQuNCAwIDgtMy42IDgtOHYtNjBjMC00LjQtMy42LTgtOC04ek01MTIgNzU2Yy0xMzQuOCAwLTI0NC0xMDkuMi0yNDQtMjQ0czEwOS4yLTI0NCAyNDQtMjQ0IDI0NCAxMDkuMiAyNDQgMjQ0LTEwOS4yIDI0NC0yNDQgMjQ0eiIgLz48cGF0aCBkPSJNNTEyIDM5MmMtMzIuMSAwLTYyLjEgMTIuNC04NC44IDM1LjItMjIuNyAyMi43LTM1LjIgNTIuNy0zNS4yIDg0LjhzMTIuNSA2Mi4xIDM1LjIgODQuOEM0NDkuOSA2MTkuNCA0ODAgNjMyIDUxMiA2MzJzNjIuMS0xMi41IDg0LjgtMzUuMkM2MTkuNCA1NzQuMSA2MzIgNTQ0IDYzMiA1MTJzLTEyLjUtNjIuMS0zNS4yLTg0LjhBMTE4LjU3IDExOC41NyAwIDAwNTEyIDM5MnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(AimOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'AimOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}