{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { isSame } from \"../../utils/dateUtil\";\nimport { getFromDate } from \"../../utils/miscUtil\";\n\n/**\n * RangePicker need additional logic to handle the `disabled` case. e.g.\n * [disabled, enabled] should end date not before start date\n */\nexport default function useRangeDisabledDate(values, disabled, activeIndexList, generateConfig, locale, disabledDate) {\n  var activeIndex = activeIndexList[activeIndexList.length - 1];\n  var rangeDisabledDate = function rangeDisabledDate(date, info) {\n    var _values = _slicedToArray(values, 2),\n      start = _values[0],\n      end = _values[1];\n    var mergedInfo = _objectSpread(_objectSpread({}, info), {}, {\n      from: getFromDate(values, activeIndexList)\n    });\n\n    // ============================ Disabled ============================\n    // Should not select days before the start date\n    if (activeIndex === 1 && disabled[0] && start &&\n    // Same date isOK\n    !isSame(generateConfig, locale, start, date, mergedInfo.type) &&\n    // Before start date\n    generateConfig.isAfter(start, date)) {\n      return true;\n    }\n\n    // Should not select days after the end date\n    if (activeIndex === 0 && disabled[1] && end &&\n    // Same date isOK\n    !isSame(generateConfig, locale, end, date, mergedInfo.type) &&\n    // After end date\n    generateConfig.isAfter(date, end)) {\n      return true;\n    }\n\n    // ============================= Origin =============================\n    return disabledDate === null || disabledDate === void 0 ? void 0 : disabledDate(date, mergedInfo);\n  };\n  return rangeDisabledDate;\n}", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "isSame", "getFromDate", "useRangeDisabledDate", "values", "disabled", "activeIndexList", "generateConfig", "locale", "disabledDate", "activeIndex", "length", "rangeDisabledDate", "date", "info", "_values", "start", "end", "mergedInfo", "from", "type", "isAfter"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-picker/es/PickerInput/hooks/useRangeDisabledDate.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { isSame } from \"../../utils/dateUtil\";\nimport { getFromDate } from \"../../utils/miscUtil\";\n\n/**\n * RangePicker need additional logic to handle the `disabled` case. e.g.\n * [disabled, enabled] should end date not before start date\n */\nexport default function useRangeDisabledDate(values, disabled, activeIndexList, generateConfig, locale, disabledDate) {\n  var activeIndex = activeIndexList[activeIndexList.length - 1];\n  var rangeDisabledDate = function rangeDisabledDate(date, info) {\n    var _values = _slicedToArray(values, 2),\n      start = _values[0],\n      end = _values[1];\n    var mergedInfo = _objectSpread(_objectSpread({}, info), {}, {\n      from: getFromDate(values, activeIndexList)\n    });\n\n    // ============================ Disabled ============================\n    // Should not select days before the start date\n    if (activeIndex === 1 && disabled[0] && start &&\n    // Same date isOK\n    !isSame(generateConfig, locale, start, date, mergedInfo.type) &&\n    // Before start date\n    generateConfig.isAfter(start, date)) {\n      return true;\n    }\n\n    // Should not select days after the end date\n    if (activeIndex === 0 && disabled[1] && end &&\n    // Same date isOK\n    !isSame(generateConfig, locale, end, date, mergedInfo.type) &&\n    // After end date\n    generateConfig.isAfter(date, end)) {\n      return true;\n    }\n\n    // ============================= Origin =============================\n    return disabledDate === null || disabledDate === void 0 ? void 0 : disabledDate(date, mergedInfo);\n  };\n  return rangeDisabledDate;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,WAAW,QAAQ,sBAAsB;;AAElD;AACA;AACA;AACA;AACA,eAAe,SAASC,oBAAoBA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,cAAc,EAAEC,MAAM,EAAEC,YAAY,EAAE;EACpH,IAAIC,WAAW,GAAGJ,eAAe,CAACA,eAAe,CAACK,MAAM,GAAG,CAAC,CAAC;EAC7D,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,IAAI,EAAEC,IAAI,EAAE;IAC7D,IAAIC,OAAO,GAAGf,cAAc,CAACI,MAAM,EAAE,CAAC,CAAC;MACrCY,KAAK,GAAGD,OAAO,CAAC,CAAC,CAAC;MAClBE,GAAG,GAAGF,OAAO,CAAC,CAAC,CAAC;IAClB,IAAIG,UAAU,GAAGnB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEe,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAC1DK,IAAI,EAAEjB,WAAW,CAACE,MAAM,EAAEE,eAAe;IAC3C,CAAC,CAAC;;IAEF;IACA;IACA,IAAII,WAAW,KAAK,CAAC,IAAIL,QAAQ,CAAC,CAAC,CAAC,IAAIW,KAAK;IAC7C;IACA,CAACf,MAAM,CAACM,cAAc,EAAEC,MAAM,EAAEQ,KAAK,EAAEH,IAAI,EAAEK,UAAU,CAACE,IAAI,CAAC;IAC7D;IACAb,cAAc,CAACc,OAAO,CAACL,KAAK,EAAEH,IAAI,CAAC,EAAE;MACnC,OAAO,IAAI;IACb;;IAEA;IACA,IAAIH,WAAW,KAAK,CAAC,IAAIL,QAAQ,CAAC,CAAC,CAAC,IAAIY,GAAG;IAC3C;IACA,CAAChB,MAAM,CAACM,cAAc,EAAEC,MAAM,EAAES,GAAG,EAAEJ,IAAI,EAAEK,UAAU,CAACE,IAAI,CAAC;IAC3D;IACAb,cAAc,CAACc,OAAO,CAACR,IAAI,EAAEI,GAAG,CAAC,EAAE;MACjC,OAAO,IAAI;IACb;;IAEA;IACA,OAAOR,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACI,IAAI,EAAEK,UAAU,CAAC;EACnG,CAAC;EACD,OAAON,iBAAiB;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}