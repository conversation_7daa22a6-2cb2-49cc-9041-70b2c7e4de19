# Details

Date : 2025-05-29 17:58:19

Directory g:\\AI_tools\\cursor\\projects\\education web

Total : 121 files,  56141 codes, 1681 comments, 2465 blanks, all 60287 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.env](/.env) | Properties | 4 | 0 | 0 | 4 |
| [.vs/VSWorkspaceState.json](/.vs/VSWorkspaceState.json) | JSON | 6 | 0 | 0 | 6 |
| [HOW\_TO\_RUN.md](/HOW_TO_RUN.md) | Markdown | 50 | 0 | 18 | 68 |
| [README.md](/README.md) | Markdown | 127 | 0 | 23 | 150 |
| [build/asset-manifest.json](/build/asset-manifest.json) | JSON | 13 | 0 | 0 | 13 |
| [build/images/camera.svg](/build/images/camera.svg) | XML | 9 | 0 | 1 | 10 |
| [build/images/compass.svg](/build/images/compass.svg) | XML | 1 | 0 | 0 | 1 |
| [build/images/edge\_computing.svg](/build/images/edge_computing.svg) | XML | 4 | 0 | 1 | 5 |
| [build/images/lidar.svg](/build/images/lidar.svg) | XML | 5 | 0 | 1 | 6 |
| [build/images/mmwave\_radar.svg](/build/images/mmwave_radar.svg) | XML | 6 | 0 | 1 | 7 |
| [build/images/obu.svg](/build/images/obu.svg) | XML | 9 | 0 | 1 | 10 |
| [build/images/rsu.svg](/build/images/rsu.svg) | XML | 8 | 0 | 1 | 9 |
| [build/index.html](/build/index.html) | HTML | 1 | 0 | 0 | 1 |
| [build/manifest.json](/build/manifest.json) | JSON | 15 | 0 | 0 | 15 |
| [build/mqtt-test.html](/build/mqtt-test.html) | HTML | 63 | 0 | 9 | 72 |
| [build/static/css/main.beae109d.css](/build/static/css/main.beae109d.css) | CSS | 1 | 1 | 0 | 2 |
| [build/static/js/main.92f83330.js](/build/static/js/main.92f83330.js) | JavaScript | 422 | 69 | 32 | 523 |
| [docs/requirements.md](/docs/requirements.md) | Markdown | 140 | 0 | 26 | 166 |
| [frontend-server.js](/frontend-server.js) | JavaScript | 11 | 3 | 3 | 17 |
| [intelligent-traffic-platform/README.md](/intelligent-traffic-platform/README.md) | Markdown | 38 | 0 | 33 | 71 |
| [intelligent-traffic-platform/package-lock.json](/intelligent-traffic-platform/package-lock.json) | JSON | 20,332 | 0 | 1 | 20,333 |
| [intelligent-traffic-platform/package.json](/intelligent-traffic-platform/package.json) | JSON | 54 | 0 | 1 | 55 |
| [intelligent-traffic-platform/public/index.html](/intelligent-traffic-platform/public/index.html) | HTML | 20 | 23 | 1 | 44 |
| [intelligent-traffic-platform/public/manifest.json](/intelligent-traffic-platform/public/manifest.json) | JSON | 25 | 0 | 1 | 26 |
| [intelligent-traffic-platform/src/App.css](/intelligent-traffic-platform/src/App.css) | CSS | 33 | 0 | 6 | 39 |
| [intelligent-traffic-platform/src/App.js](/intelligent-traffic-platform/src/App.js) | JavaScript | 23 | 0 | 3 | 26 |
| [intelligent-traffic-platform/src/App.test.js](/intelligent-traffic-platform/src/App.test.js) | JavaScript | 7 | 0 | 2 | 9 |
| [intelligent-traffic-platform/src/index.css](/intelligent-traffic-platform/src/index.css) | CSS | 12 | 0 | 2 | 14 |
| [intelligent-traffic-platform/src/index.js](/intelligent-traffic-platform/src/index.js) | JavaScript | 12 | 3 | 3 | 18 |
| [intelligent-traffic-platform/src/logo.svg](/intelligent-traffic-platform/src/logo.svg) | XML | 1 | 0 | 0 | 1 |
| [intelligent-traffic-platform/src/reportWebVitals.js](/intelligent-traffic-platform/src/reportWebVitals.js) | JavaScript | 12 | 0 | 2 | 14 |
| [intelligent-traffic-platform/src/setupTests.js](/intelligent-traffic-platform/src/setupTests.js) | JavaScript | 1 | 4 | 1 | 6 |
| [jsconfig.json](/jsconfig.json) | JSON with Comments | 8 | 0 | 0 | 8 |
| [package-lock.json](/package-lock.json) | JSON | 21,420 | 0 | 1 | 21,421 |
| [package.json](/package.json) | JSON | 68 | 0 | 1 | 69 |
| [public/images/camera.svg](/public/images/camera.svg) | XML | 9 | 0 | 1 | 10 |
| [public/images/compass.svg](/public/images/compass.svg) | XML | 1 | 0 | 0 | 1 |
| [public/images/edge\_computing.svg](/public/images/edge_computing.svg) | XML | 4 | 0 | 1 | 5 |
| [public/images/lidar.svg](/public/images/lidar.svg) | XML | 5 | 0 | 1 | 6 |
| [public/images/mmwave\_radar.svg](/public/images/mmwave_radar.svg) | XML | 6 | 0 | 1 | 7 |
| [public/images/obu.svg](/public/images/obu.svg) | XML | 9 | 0 | 1 | 10 |
| [public/images/rsu.svg](/public/images/rsu.svg) | XML | 8 | 0 | 1 | 9 |
| [public/index.html](/public/index.html) | HTML | 20 | 0 | 0 | 20 |
| [public/manifest.json](/public/manifest.json) | JSON | 15 | 0 | 0 | 15 |
| [public/mqtt-test.html](/public/mqtt-test.html) | HTML | 63 | 0 | 9 | 72 |
| [server/models/Device.js](/server/models/Device.js) | JavaScript | 33 | 0 | 3 | 36 |
| [server/models/Event.js](/server/models/Event.js) | JavaScript | 25 | 0 | 3 | 28 |
| [server/models/Intersection.js](/server/models/Intersection.js) | JavaScript | 21 | 0 | 3 | 24 |
| [server/models/User.js](/server/models/User.js) | JavaScript | 52 | 3 | 6 | 61 |
| [server/models/Vehicle.js](/server/models/Vehicle.js) | JavaScript | 36 | 0 | 3 | 39 |
| [server/mqtt-ws-bridge.js](/server/mqtt-ws-bridge.js) | JavaScript | 243 | 41 | 44 | 328 |
| [server/routes/auth.js](/server/routes/auth.js) | JavaScript | 67 | 6 | 16 | 89 |
| [server/routes/coordinates.js](/server/routes/coordinates.js) | JavaScript | 63 | 11 | 11 | 85 |
| [server/routes/devices.js](/server/routes/devices.js) | JavaScript | 133 | 9 | 21 | 163 |
| [server/routes/users.js](/server/routes/users.js) | JavaScript | 174 | 26 | 38 | 238 |
| [server/routes/vehicles.js](/server/routes/vehicles.js) | JavaScript | 187 | 24 | 39 | 250 |
| [server/server.js](/server/server.js) | JavaScript | 143 | 14 | 27 | 184 |
| [server/start-stream-server.js](/server/start-stream-server.js) | JavaScript | 26 | 4 | 5 | 35 |
| [server/stream-server.js](/server/stream-server.js) | JavaScript | 230 | 28 | 29 | 287 |
| [server/streamServer.js](/server/streamServer.js) | JavaScript | 126 | 12 | 21 | 159 |
| [src/App.css](/src/App.css) | CSS | 37 | 0 | 7 | 44 |
| [src/App.js](/src/App.js) | JavaScript | 57 | 6 | 7 | 70 |
| [src/assets/css/main.css](/src/assets/css/main.css) | CSS | 0 | 0 | 1 | 1 |
| [src/components/CampusModel.jsx](/src/components/CampusModel.jsx) | JavaScript JSX | 2,718 | 603 | 541 | 3,862 |
| [src/components/CoordinateSettings.jsx](/src/components/CoordinateSettings.jsx) | JavaScript JSX | 124 | 6 | 20 | 150 |
| [src/components/DevicePopoverContent.jsx](/src/components/DevicePopoverContent.jsx) | JavaScript JSX | 150 | 14 | 6 | 170 |
| [src/components/MainLayout.jsx](/src/components/MainLayout.jsx) | JavaScript JSX | 125 | 1 | 11 | 137 |
| [src/components/VideoPlayer.jsx](/src/components/VideoPlayer.jsx) | JavaScript JSX | 325 | 0 | 24 | 349 |
| [src/components/layout/CollapsibleSidebar.jsx](/src/components/layout/CollapsibleSidebar.jsx) | JavaScript JSX | 92 | 3 | 6 | 101 |
| [src/components/layout/MainLayout.jsx](/src/components/layout/MainLayout.jsx) | JavaScript JSX | 302 | 14 | 20 | 336 |
| [src/config/coordinates.json](/src/config/coordinates.json) | JSON | 14 | 0 | 0 | 14 |
| [src/data/devices.json](/src/data/devices.json) | JSON | 258 | 0 | 0 | 258 |
| [src/data/intersections.json](/src/data/intersections.json) | JSON | 87 | 0 | 0 | 87 |
| [src/data/users.json](/src/data/users.json) | JSON | 25 | 0 | 0 | 25 |
| [src/data/vehicles.json](/src/data/vehicles.json) | JSON | 40 | 0 | 0 | 40 |
| [src/index.css](/src/index.css) | CSS | 12 | 0 | 1 | 13 |
| [src/index.js](/src/index.js) | JavaScript | 10 | 0 | 1 | 11 |
| [src/js/road-monitor.js](/src/js/road-monitor.js) | JavaScript | 67 | 7 | 7 | 81 |
| [src/main.js](/src/main.js) | JavaScript | 0 | 0 | 1 | 1 |
| [src/models/User.js](/src/models/User.js) | JavaScript | 46 | 3 | 6 | 55 |
| [src/pages/DeviceManagement.jsx](/src/pages/DeviceManagement.jsx) | JavaScript JSX | 413 | 40 | 41 | 494 |
| [src/pages/DeviceStatus.jsx](/src/pages/DeviceStatus.jsx) | JavaScript JSX | 688 | 55 | 60 | 803 |
| [src/pages/Login.css](/src/pages/Login.css) | CSS | 59 | 1 | 9 | 69 |
| [src/pages/Login.jsx](/src/pages/Login.jsx) | JavaScript JSX | 253 | 65 | 43 | 361 |
| [src/pages/MonitoringPage.css](/src/pages/MonitoringPage.css) | CSS | 123 | 0 | 23 | 146 |
| [src/pages/MonitoringPage.js](/src/pages/MonitoringPage.js) | JavaScript | 144 | 10 | 20 | 174 |
| [src/pages/RealTimeTraffic.jsx](/src/pages/RealTimeTraffic.jsx) | JavaScript JSX | 989 | 181 | 142 | 1,312 |
| [src/pages/RoadMonitoring.jsx](/src/pages/RoadMonitoring.jsx) | JavaScript JSX | 319 | 57 | 36 | 412 |
| [src/pages/SystemManagement.jsx](/src/pages/SystemManagement.jsx) | JavaScript JSX | 651 | 45 | 64 | 760 |
| [src/pages/UserManagement.jsx](/src/pages/UserManagement.jsx) | JavaScript JSX | 52 | 0 | 8 | 60 |
| [src/pages/VehicleManagement.jsx](/src/pages/VehicleManagement.jsx) | JavaScript JSX | 246 | 24 | 25 | 295 |
| [src/routes/auth.js](/src/routes/auth.js) | JavaScript | 83 | 8 | 13 | 104 |
| [src/routes/vehicles.js](/src/routes/vehicles.js) | JavaScript | 138 | 22 | 33 | 193 |
| [src/scripts/migratePasswords.js](/src/scripts/migratePasswords.js) | JavaScript | 20 | 5 | 7 | 32 |
| [src/server.js](/src/server.js) | JavaScript | 789 | 95 | 101 | 985 |
| [src/server/config/db.js](/src/server/config/db.js) | JavaScript | 14 | 0 | 3 | 17 |
| [src/server/index.js](/src/server/index.js) | JavaScript | 18 | 4 | 6 | 28 |
| [src/server/middleware/admin.js](/src/server/middleware/admin.js) | JavaScript | 6 | 1 | 0 | 7 |
| [src/server/middleware/auth.js](/src/server/middleware/auth.js) | JavaScript | 55 | 7 | 13 | 75 |
| [src/server/models/User.js](/src/server/models/User.js) | JavaScript | 86 | 6 | 9 | 101 |
| [src/server/models/Vehicle.js](/src/server/models/Vehicle.js) | JavaScript | 30 | 1 | 3 | 34 |
| [src/server/reset-admin.js](/src/server/reset-admin.js) | JavaScript | 78 | 11 | 16 | 105 |
| [src/server/routes/auth.js](/src/server/routes/auth.js) | JavaScript | 121 | 12 | 19 | 152 |
| [src/server/routes/users.js](/src/server/routes/users.js) | JavaScript | 103 | 15 | 19 | 137 |
| [src/server/routes/vehicles.js](/src/server/routes/vehicles.js) | JavaScript | 108 | 17 | 24 | 149 |
| [src/server/rtspServer.js](/src/server/rtspServer.js) | JavaScript | 0 | 0 | 1 | 1 |
| [src/server/start.js](/src/server/start.js) | JavaScript | 0 | 0 | 1 | 1 |
| [src/server/webrtcServer.js](/src/server/webrtcServer.js) | JavaScript | 0 | 0 | 1 | 1 |
| [src/services/auth.js](/src/services/auth.js) | JavaScript | 35 | 6 | 7 | 48 |
| [src/setupProxy.js](/src/setupProxy.js) | JavaScript | 46 | 2 | 3 | 51 |
| [src/utils/CoordinateConverter.js](/src/utils/CoordinateConverter.js) | JavaScript | 102 | 26 | 23 | 151 |
| [src/utils/axios.js](/src/utils/axios.js) | JavaScript | 41 | 6 | 4 | 51 |
| [start-all.bat](/start-all.bat) | Batch | 20 | 0 | 5 | 25 |
| [start-simple.bat](/start-simple.bat) | Batch | 38 | 1 | 6 | 45 |
| [start-stream-server.js](/start-stream-server.js) | JavaScript | 1 | 0 | 0 | 1 |
| [start.bat](/start.bat) | Batch | 64 | 9 | 9 | 82 |
| [start\_en.bat](/start_en.bat) | Batch | 142 | 11 | 14 | 167 |
| [vue.config.js](/vue.config.js) | JavaScript | 0 | 0 | 1 | 1 |
| [使用说明书.md](/%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E%E4%B9%A6.md) | Markdown | 494 | 0 | 106 | 600 |
| [消息协议文档说明.md](/%E6%B6%88%E6%81%AF%E5%8D%8F%E8%AE%AE%E6%96%87%E6%A1%A3%E8%AF%B4%E6%98%8E.md) | Markdown | 110 | 0 | 53 | 163 |
| [软件著作权/软件著作权的样例.md](/%E8%BD%AF%E4%BB%B6%E8%91%97%E4%BD%9C%E6%9D%83/%E8%BD%AF%E4%BB%B6%E8%91%97%E4%BD%9C%E6%9D%83%E7%9A%84%E6%A0%B7%E4%BE%8B.md) | Markdown | 313 | 0 | 376 | 689 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)