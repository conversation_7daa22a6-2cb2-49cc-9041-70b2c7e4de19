{"ast": null, "code": "import { Keyframes, unit } from '@ant-design/cssinjs';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst skeletonClsLoading = new Keyframes(`ant-skeleton-loading`, {\n  '0%': {\n    backgroundPosition: '100% 50%'\n  },\n  '100%': {\n    backgroundPosition: '0 50%'\n  }\n});\nconst genSkeletonElementCommonSize = size => ({\n  height: size,\n  lineHeight: unit(size)\n});\nconst genSkeletonElementAvatarSize = size => Object.assign({\n  width: size\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonColor = token => ({\n  background: token.skeletonLoadingBackground,\n  backgroundSize: '400% 100%',\n  animationName: skeletonClsLoading,\n  animationDuration: token.skeletonLoadingMotionDuration,\n  animationTimingFunction: 'ease',\n  animationIterationCount: 'infinite'\n});\nconst genSkeletonElementInputSize = (size, calc) => Object.assign({\n  width: calc(size).mul(5).equal(),\n  minWidth: calc(size).mul(5).equal()\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonElementAvatar = token => {\n  const {\n    skeletonAvatarCls,\n    gradientFromColor,\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM\n  } = token;\n  return {\n    [skeletonAvatarCls]: Object.assign({\n      display: 'inline-block',\n      verticalAlign: 'top',\n      background: gradientFromColor\n    }, genSkeletonElementAvatarSize(controlHeight)),\n    [`${skeletonAvatarCls}${skeletonAvatarCls}-circle`]: {\n      borderRadius: '50%'\n    },\n    [`${skeletonAvatarCls}${skeletonAvatarCls}-lg`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightLG)),\n    [`${skeletonAvatarCls}${skeletonAvatarCls}-sm`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightSM))\n  };\n};\nconst genSkeletonElementInput = token => {\n  const {\n    controlHeight,\n    borderRadiusSM,\n    skeletonInputCls,\n    controlHeightLG,\n    controlHeightSM,\n    gradientFromColor,\n    calc\n  } = token;\n  return {\n    [skeletonInputCls]: Object.assign({\n      display: 'inline-block',\n      verticalAlign: 'top',\n      background: gradientFromColor,\n      borderRadius: borderRadiusSM\n    }, genSkeletonElementInputSize(controlHeight, calc)),\n    [`${skeletonInputCls}-lg`]: Object.assign({}, genSkeletonElementInputSize(controlHeightLG, calc)),\n    [`${skeletonInputCls}-sm`]: Object.assign({}, genSkeletonElementInputSize(controlHeightSM, calc))\n  };\n};\nconst genSkeletonElementImageSize = size => Object.assign({\n  width: size\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonElementImage = token => {\n  const {\n    skeletonImageCls,\n    imageSizeBase,\n    gradientFromColor,\n    borderRadiusSM,\n    calc\n  } = token;\n  return {\n    [skeletonImageCls]: Object.assign(Object.assign({\n      display: 'inline-flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      verticalAlign: 'middle',\n      background: gradientFromColor,\n      borderRadius: borderRadiusSM\n    }, genSkeletonElementImageSize(calc(imageSizeBase).mul(2).equal())), {\n      [`${skeletonImageCls}-path`]: {\n        fill: '#bfbfbf'\n      },\n      [`${skeletonImageCls}-svg`]: Object.assign(Object.assign({}, genSkeletonElementImageSize(imageSizeBase)), {\n        maxWidth: calc(imageSizeBase).mul(4).equal(),\n        maxHeight: calc(imageSizeBase).mul(4).equal()\n      }),\n      [`${skeletonImageCls}-svg${skeletonImageCls}-svg-circle`]: {\n        borderRadius: '50%'\n      }\n    }),\n    [`${skeletonImageCls}${skeletonImageCls}-circle`]: {\n      borderRadius: '50%'\n    }\n  };\n};\nconst genSkeletonElementButtonShape = (token, size, buttonCls) => {\n  const {\n    skeletonButtonCls\n  } = token;\n  return {\n    [`${buttonCls}${skeletonButtonCls}-circle`]: {\n      width: size,\n      minWidth: size,\n      borderRadius: '50%'\n    },\n    [`${buttonCls}${skeletonButtonCls}-round`]: {\n      borderRadius: size\n    }\n  };\n};\nconst genSkeletonElementButtonSize = (size, calc) => Object.assign({\n  width: calc(size).mul(2).equal(),\n  minWidth: calc(size).mul(2).equal()\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonElementButton = token => {\n  const {\n    borderRadiusSM,\n    skeletonButtonCls,\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM,\n    gradientFromColor,\n    calc\n  } = token;\n  return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n    [skeletonButtonCls]: Object.assign({\n      display: 'inline-block',\n      verticalAlign: 'top',\n      background: gradientFromColor,\n      borderRadius: borderRadiusSM,\n      width: calc(controlHeight).mul(2).equal(),\n      minWidth: calc(controlHeight).mul(2).equal()\n    }, genSkeletonElementButtonSize(controlHeight, calc))\n  }, genSkeletonElementButtonShape(token, controlHeight, skeletonButtonCls)), {\n    [`${skeletonButtonCls}-lg`]: Object.assign({}, genSkeletonElementButtonSize(controlHeightLG, calc))\n  }), genSkeletonElementButtonShape(token, controlHeightLG, `${skeletonButtonCls}-lg`)), {\n    [`${skeletonButtonCls}-sm`]: Object.assign({}, genSkeletonElementButtonSize(controlHeightSM, calc))\n  }), genSkeletonElementButtonShape(token, controlHeightSM, `${skeletonButtonCls}-sm`));\n};\n// =============================== Base ===============================\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    skeletonAvatarCls,\n    skeletonTitleCls,\n    skeletonParagraphCls,\n    skeletonButtonCls,\n    skeletonInputCls,\n    skeletonImageCls,\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM,\n    gradientFromColor,\n    padding,\n    marginSM,\n    borderRadius,\n    titleHeight,\n    blockRadius,\n    paragraphLiHeight,\n    controlHeightXS,\n    paragraphMarginTop\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'table',\n      width: '100%',\n      [`${componentCls}-header`]: {\n        display: 'table-cell',\n        paddingInlineEnd: padding,\n        verticalAlign: 'top',\n        // Avatar\n        [skeletonAvatarCls]: Object.assign({\n          display: 'inline-block',\n          verticalAlign: 'top',\n          background: gradientFromColor\n        }, genSkeletonElementAvatarSize(controlHeight)),\n        [`${skeletonAvatarCls}-circle`]: {\n          borderRadius: '50%'\n        },\n        [`${skeletonAvatarCls}-lg`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightLG)),\n        [`${skeletonAvatarCls}-sm`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightSM))\n      },\n      [`${componentCls}-content`]: {\n        display: 'table-cell',\n        width: '100%',\n        verticalAlign: 'top',\n        // Title\n        [skeletonTitleCls]: {\n          width: '100%',\n          height: titleHeight,\n          background: gradientFromColor,\n          borderRadius: blockRadius,\n          [`+ ${skeletonParagraphCls}`]: {\n            marginBlockStart: controlHeightSM\n          }\n        },\n        // paragraph\n        [skeletonParagraphCls]: {\n          padding: 0,\n          '> li': {\n            width: '100%',\n            height: paragraphLiHeight,\n            listStyle: 'none',\n            background: gradientFromColor,\n            borderRadius: blockRadius,\n            '+ li': {\n              marginBlockStart: controlHeightXS\n            }\n          }\n        },\n        [`${skeletonParagraphCls}> li:last-child:not(:first-child):not(:nth-child(2))`]: {\n          width: '61%'\n        }\n      },\n      [`&-round ${componentCls}-content`]: {\n        [`${skeletonTitleCls}, ${skeletonParagraphCls} > li`]: {\n          borderRadius\n        }\n      }\n    },\n    [`${componentCls}-with-avatar ${componentCls}-content`]: {\n      // Title\n      [skeletonTitleCls]: {\n        marginBlockStart: marginSM,\n        [`+ ${skeletonParagraphCls}`]: {\n          marginBlockStart: paragraphMarginTop\n        }\n      }\n    },\n    // Skeleton element\n    [`${componentCls}${componentCls}-element`]: Object.assign(Object.assign(Object.assign(Object.assign({\n      display: 'inline-block',\n      width: 'auto'\n    }, genSkeletonElementButton(token)), genSkeletonElementAvatar(token)), genSkeletonElementInput(token)), genSkeletonElementImage(token)),\n    // Skeleton Block Button, Input\n    [`${componentCls}${componentCls}-block`]: {\n      width: '100%',\n      [skeletonButtonCls]: {\n        width: '100%'\n      },\n      [skeletonInputCls]: {\n        width: '100%'\n      }\n    },\n    // With active animation\n    [`${componentCls}${componentCls}-active`]: {\n      [`\n        ${skeletonTitleCls},\n        ${skeletonParagraphCls} > li,\n        ${skeletonAvatarCls},\n        ${skeletonButtonCls},\n        ${skeletonInputCls},\n        ${skeletonImageCls}\n      `]: Object.assign({}, genSkeletonColor(token))\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  const {\n    colorFillContent,\n    colorFill\n  } = token;\n  const gradientFromColor = colorFillContent;\n  const gradientToColor = colorFill;\n  return {\n    color: gradientFromColor,\n    colorGradientEnd: gradientToColor,\n    gradientFromColor,\n    gradientToColor,\n    titleHeight: token.controlHeight / 2,\n    blockRadius: token.borderRadiusSM,\n    paragraphMarginTop: token.marginLG + token.marginXXS,\n    paragraphLiHeight: token.controlHeight / 2\n  };\n};\nexport default genStyleHooks('Skeleton', token => {\n  const {\n    componentCls,\n    calc\n  } = token;\n  const skeletonToken = mergeToken(token, {\n    skeletonAvatarCls: `${componentCls}-avatar`,\n    skeletonTitleCls: `${componentCls}-title`,\n    skeletonParagraphCls: `${componentCls}-paragraph`,\n    skeletonButtonCls: `${componentCls}-button`,\n    skeletonInputCls: `${componentCls}-input`,\n    skeletonImageCls: `${componentCls}-image`,\n    imageSizeBase: calc(token.controlHeight).mul(1.5).equal(),\n    borderRadius: 100,\n    // Large number to make capsule shape\n    skeletonLoadingBackground: `linear-gradient(90deg, ${token.gradientFromColor} 25%, ${token.gradientToColor} 37%, ${token.gradientFromColor} 63%)`,\n    skeletonLoadingMotionDuration: '1.4s'\n  });\n  return [genBaseStyle(skeletonToken)];\n}, prepareComponentToken, {\n  deprecatedTokens: [['color', 'gradientFromColor'], ['colorGradientEnd', 'gradientToColor']]\n});", "map": {"version": 3, "names": ["Keyframes", "unit", "genStyleHooks", "mergeToken", "skeletonClsLoading", "backgroundPosition", "genSkeletonElementCommonSize", "size", "height", "lineHeight", "genSkeletonElementAvatarSize", "Object", "assign", "width", "genSkeletonColor", "token", "background", "skeletonLoadingBackground", "backgroundSize", "animationName", "animationDuration", "skeletonLoadingMotionDuration", "animationTimingFunction", "animationIterationCount", "genSkeletonElementInputSize", "calc", "mul", "equal", "min<PERSON><PERSON><PERSON>", "genSkeletonElementAvatar", "skeletonAvatarCls", "gradientFromColor", "controlHeight", "controlHeightLG", "controlHeightSM", "display", "verticalAlign", "borderRadius", "genSkeletonElementInput", "borderRadiusSM", "skeletonInputCls", "genSkeletonElementImageSize", "genSkeletonElementImage", "skeletonImageCls", "imageSizeBase", "alignItems", "justifyContent", "fill", "max<PERSON><PERSON><PERSON>", "maxHeight", "genSkeletonElementButtonShape", "buttonCls", "skeletonButtonCls", "genSkeletonElementButtonSize", "genSkeletonElementButton", "genBaseStyle", "componentCls", "skeletonTitleCls", "skeletonParagraphCls", "padding", "marginSM", "titleHeight", "blockRadius", "paragraphLiHeight", "controlHeightXS", "paragraphMarginTop", "paddingInlineEnd", "marginBlockStart", "listStyle", "prepareComponentToken", "colorFillContent", "colorFill", "gradientToColor", "color", "colorGradientEnd", "marginLG", "marginXXS", "skeletonToken", "deprecatedTokens"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/skeleton/style/index.js"], "sourcesContent": ["import { Keyframes, unit } from '@ant-design/cssinjs';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst skeletonClsLoading = new Keyframes(`ant-skeleton-loading`, {\n  '0%': {\n    backgroundPosition: '100% 50%'\n  },\n  '100%': {\n    backgroundPosition: '0 50%'\n  }\n});\nconst genSkeletonElementCommonSize = size => ({\n  height: size,\n  lineHeight: unit(size)\n});\nconst genSkeletonElementAvatarSize = size => Object.assign({\n  width: size\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonColor = token => ({\n  background: token.skeletonLoadingBackground,\n  backgroundSize: '400% 100%',\n  animationName: skeletonClsLoading,\n  animationDuration: token.skeletonLoadingMotionDuration,\n  animationTimingFunction: 'ease',\n  animationIterationCount: 'infinite'\n});\nconst genSkeletonElementInputSize = (size, calc) => Object.assign({\n  width: calc(size).mul(5).equal(),\n  minWidth: calc(size).mul(5).equal()\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonElementAvatar = token => {\n  const {\n    skeletonAvatarCls,\n    gradientFromColor,\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM\n  } = token;\n  return {\n    [skeletonAvatarCls]: Object.assign({\n      display: 'inline-block',\n      verticalAlign: 'top',\n      background: gradientFromColor\n    }, genSkeletonElementAvatarSize(controlHeight)),\n    [`${skeletonAvatarCls}${skeletonAvatarCls}-circle`]: {\n      borderRadius: '50%'\n    },\n    [`${skeletonAvatarCls}${skeletonAvatarCls}-lg`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightLG)),\n    [`${skeletonAvatarCls}${skeletonAvatarCls}-sm`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightSM))\n  };\n};\nconst genSkeletonElementInput = token => {\n  const {\n    controlHeight,\n    borderRadiusSM,\n    skeletonInputCls,\n    controlHeightLG,\n    controlHeightSM,\n    gradientFromColor,\n    calc\n  } = token;\n  return {\n    [skeletonInputCls]: Object.assign({\n      display: 'inline-block',\n      verticalAlign: 'top',\n      background: gradientFromColor,\n      borderRadius: borderRadiusSM\n    }, genSkeletonElementInputSize(controlHeight, calc)),\n    [`${skeletonInputCls}-lg`]: Object.assign({}, genSkeletonElementInputSize(controlHeightLG, calc)),\n    [`${skeletonInputCls}-sm`]: Object.assign({}, genSkeletonElementInputSize(controlHeightSM, calc))\n  };\n};\nconst genSkeletonElementImageSize = size => Object.assign({\n  width: size\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonElementImage = token => {\n  const {\n    skeletonImageCls,\n    imageSizeBase,\n    gradientFromColor,\n    borderRadiusSM,\n    calc\n  } = token;\n  return {\n    [skeletonImageCls]: Object.assign(Object.assign({\n      display: 'inline-flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      verticalAlign: 'middle',\n      background: gradientFromColor,\n      borderRadius: borderRadiusSM\n    }, genSkeletonElementImageSize(calc(imageSizeBase).mul(2).equal())), {\n      [`${skeletonImageCls}-path`]: {\n        fill: '#bfbfbf'\n      },\n      [`${skeletonImageCls}-svg`]: Object.assign(Object.assign({}, genSkeletonElementImageSize(imageSizeBase)), {\n        maxWidth: calc(imageSizeBase).mul(4).equal(),\n        maxHeight: calc(imageSizeBase).mul(4).equal()\n      }),\n      [`${skeletonImageCls}-svg${skeletonImageCls}-svg-circle`]: {\n        borderRadius: '50%'\n      }\n    }),\n    [`${skeletonImageCls}${skeletonImageCls}-circle`]: {\n      borderRadius: '50%'\n    }\n  };\n};\nconst genSkeletonElementButtonShape = (token, size, buttonCls) => {\n  const {\n    skeletonButtonCls\n  } = token;\n  return {\n    [`${buttonCls}${skeletonButtonCls}-circle`]: {\n      width: size,\n      minWidth: size,\n      borderRadius: '50%'\n    },\n    [`${buttonCls}${skeletonButtonCls}-round`]: {\n      borderRadius: size\n    }\n  };\n};\nconst genSkeletonElementButtonSize = (size, calc) => Object.assign({\n  width: calc(size).mul(2).equal(),\n  minWidth: calc(size).mul(2).equal()\n}, genSkeletonElementCommonSize(size));\nconst genSkeletonElementButton = token => {\n  const {\n    borderRadiusSM,\n    skeletonButtonCls,\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM,\n    gradientFromColor,\n    calc\n  } = token;\n  return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n    [skeletonButtonCls]: Object.assign({\n      display: 'inline-block',\n      verticalAlign: 'top',\n      background: gradientFromColor,\n      borderRadius: borderRadiusSM,\n      width: calc(controlHeight).mul(2).equal(),\n      minWidth: calc(controlHeight).mul(2).equal()\n    }, genSkeletonElementButtonSize(controlHeight, calc))\n  }, genSkeletonElementButtonShape(token, controlHeight, skeletonButtonCls)), {\n    [`${skeletonButtonCls}-lg`]: Object.assign({}, genSkeletonElementButtonSize(controlHeightLG, calc))\n  }), genSkeletonElementButtonShape(token, controlHeightLG, `${skeletonButtonCls}-lg`)), {\n    [`${skeletonButtonCls}-sm`]: Object.assign({}, genSkeletonElementButtonSize(controlHeightSM, calc))\n  }), genSkeletonElementButtonShape(token, controlHeightSM, `${skeletonButtonCls}-sm`));\n};\n// =============================== Base ===============================\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    skeletonAvatarCls,\n    skeletonTitleCls,\n    skeletonParagraphCls,\n    skeletonButtonCls,\n    skeletonInputCls,\n    skeletonImageCls,\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM,\n    gradientFromColor,\n    padding,\n    marginSM,\n    borderRadius,\n    titleHeight,\n    blockRadius,\n    paragraphLiHeight,\n    controlHeightXS,\n    paragraphMarginTop\n  } = token;\n  return {\n    [componentCls]: {\n      display: 'table',\n      width: '100%',\n      [`${componentCls}-header`]: {\n        display: 'table-cell',\n        paddingInlineEnd: padding,\n        verticalAlign: 'top',\n        // Avatar\n        [skeletonAvatarCls]: Object.assign({\n          display: 'inline-block',\n          verticalAlign: 'top',\n          background: gradientFromColor\n        }, genSkeletonElementAvatarSize(controlHeight)),\n        [`${skeletonAvatarCls}-circle`]: {\n          borderRadius: '50%'\n        },\n        [`${skeletonAvatarCls}-lg`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightLG)),\n        [`${skeletonAvatarCls}-sm`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightSM))\n      },\n      [`${componentCls}-content`]: {\n        display: 'table-cell',\n        width: '100%',\n        verticalAlign: 'top',\n        // Title\n        [skeletonTitleCls]: {\n          width: '100%',\n          height: titleHeight,\n          background: gradientFromColor,\n          borderRadius: blockRadius,\n          [`+ ${skeletonParagraphCls}`]: {\n            marginBlockStart: controlHeightSM\n          }\n        },\n        // paragraph\n        [skeletonParagraphCls]: {\n          padding: 0,\n          '> li': {\n            width: '100%',\n            height: paragraphLiHeight,\n            listStyle: 'none',\n            background: gradientFromColor,\n            borderRadius: blockRadius,\n            '+ li': {\n              marginBlockStart: controlHeightXS\n            }\n          }\n        },\n        [`${skeletonParagraphCls}> li:last-child:not(:first-child):not(:nth-child(2))`]: {\n          width: '61%'\n        }\n      },\n      [`&-round ${componentCls}-content`]: {\n        [`${skeletonTitleCls}, ${skeletonParagraphCls} > li`]: {\n          borderRadius\n        }\n      }\n    },\n    [`${componentCls}-with-avatar ${componentCls}-content`]: {\n      // Title\n      [skeletonTitleCls]: {\n        marginBlockStart: marginSM,\n        [`+ ${skeletonParagraphCls}`]: {\n          marginBlockStart: paragraphMarginTop\n        }\n      }\n    },\n    // Skeleton element\n    [`${componentCls}${componentCls}-element`]: Object.assign(Object.assign(Object.assign(Object.assign({\n      display: 'inline-block',\n      width: 'auto'\n    }, genSkeletonElementButton(token)), genSkeletonElementAvatar(token)), genSkeletonElementInput(token)), genSkeletonElementImage(token)),\n    // Skeleton Block Button, Input\n    [`${componentCls}${componentCls}-block`]: {\n      width: '100%',\n      [skeletonButtonCls]: {\n        width: '100%'\n      },\n      [skeletonInputCls]: {\n        width: '100%'\n      }\n    },\n    // With active animation\n    [`${componentCls}${componentCls}-active`]: {\n      [`\n        ${skeletonTitleCls},\n        ${skeletonParagraphCls} > li,\n        ${skeletonAvatarCls},\n        ${skeletonButtonCls},\n        ${skeletonInputCls},\n        ${skeletonImageCls}\n      `]: Object.assign({}, genSkeletonColor(token))\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareComponentToken = token => {\n  const {\n    colorFillContent,\n    colorFill\n  } = token;\n  const gradientFromColor = colorFillContent;\n  const gradientToColor = colorFill;\n  return {\n    color: gradientFromColor,\n    colorGradientEnd: gradientToColor,\n    gradientFromColor,\n    gradientToColor,\n    titleHeight: token.controlHeight / 2,\n    blockRadius: token.borderRadiusSM,\n    paragraphMarginTop: token.marginLG + token.marginXXS,\n    paragraphLiHeight: token.controlHeight / 2\n  };\n};\nexport default genStyleHooks('Skeleton', token => {\n  const {\n    componentCls,\n    calc\n  } = token;\n  const skeletonToken = mergeToken(token, {\n    skeletonAvatarCls: `${componentCls}-avatar`,\n    skeletonTitleCls: `${componentCls}-title`,\n    skeletonParagraphCls: `${componentCls}-paragraph`,\n    skeletonButtonCls: `${componentCls}-button`,\n    skeletonInputCls: `${componentCls}-input`,\n    skeletonImageCls: `${componentCls}-image`,\n    imageSizeBase: calc(token.controlHeight).mul(1.5).equal(),\n    borderRadius: 100,\n    // Large number to make capsule shape\n    skeletonLoadingBackground: `linear-gradient(90deg, ${token.gradientFromColor} 25%, ${token.gradientToColor} 37%, ${token.gradientFromColor} 63%)`,\n    skeletonLoadingMotionDuration: '1.4s'\n  });\n  return [genBaseStyle(skeletonToken)];\n}, prepareComponentToken, {\n  deprecatedTokens: [['color', 'gradientFromColor'], ['colorGradientEnd', 'gradientToColor']]\n});"], "mappings": "AAAA,SAASA,SAAS,EAAEC,IAAI,QAAQ,qBAAqB;AACrD,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,MAAMC,kBAAkB,GAAG,IAAIJ,SAAS,CAAC,sBAAsB,EAAE;EAC/D,IAAI,EAAE;IACJK,kBAAkB,EAAE;EACtB,CAAC;EACD,MAAM,EAAE;IACNA,kBAAkB,EAAE;EACtB;AACF,CAAC,CAAC;AACF,MAAMC,4BAA4B,GAAGC,IAAI,KAAK;EAC5CC,MAAM,EAAED,IAAI;EACZE,UAAU,EAAER,IAAI,CAACM,IAAI;AACvB,CAAC,CAAC;AACF,MAAMG,4BAA4B,GAAGH,IAAI,IAAII,MAAM,CAACC,MAAM,CAAC;EACzDC,KAAK,EAAEN;AACT,CAAC,EAAED,4BAA4B,CAACC,IAAI,CAAC,CAAC;AACtC,MAAMO,gBAAgB,GAAGC,KAAK,KAAK;EACjCC,UAAU,EAAED,KAAK,CAACE,yBAAyB;EAC3CC,cAAc,EAAE,WAAW;EAC3BC,aAAa,EAAEf,kBAAkB;EACjCgB,iBAAiB,EAAEL,KAAK,CAACM,6BAA6B;EACtDC,uBAAuB,EAAE,MAAM;EAC/BC,uBAAuB,EAAE;AAC3B,CAAC,CAAC;AACF,MAAMC,2BAA2B,GAAGA,CAACjB,IAAI,EAAEkB,IAAI,KAAKd,MAAM,CAACC,MAAM,CAAC;EAChEC,KAAK,EAAEY,IAAI,CAAClB,IAAI,CAAC,CAACmB,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EAChCC,QAAQ,EAAEH,IAAI,CAAClB,IAAI,CAAC,CAACmB,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;AACpC,CAAC,EAAErB,4BAA4B,CAACC,IAAI,CAAC,CAAC;AACtC,MAAMsB,wBAAwB,GAAGd,KAAK,IAAI;EACxC,MAAM;IACJe,iBAAiB;IACjBC,iBAAiB;IACjBC,aAAa;IACbC,eAAe;IACfC;EACF,CAAC,GAAGnB,KAAK;EACT,OAAO;IACL,CAACe,iBAAiB,GAAGnB,MAAM,CAACC,MAAM,CAAC;MACjCuB,OAAO,EAAE,cAAc;MACvBC,aAAa,EAAE,KAAK;MACpBpB,UAAU,EAAEe;IACd,CAAC,EAAErB,4BAA4B,CAACsB,aAAa,CAAC,CAAC;IAC/C,CAAC,GAAGF,iBAAiB,GAAGA,iBAAiB,SAAS,GAAG;MACnDO,YAAY,EAAE;IAChB,CAAC;IACD,CAAC,GAAGP,iBAAiB,GAAGA,iBAAiB,KAAK,GAAGnB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,4BAA4B,CAACuB,eAAe,CAAC,CAAC;IACjH,CAAC,GAAGH,iBAAiB,GAAGA,iBAAiB,KAAK,GAAGnB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,4BAA4B,CAACwB,eAAe,CAAC;EAClH,CAAC;AACH,CAAC;AACD,MAAMI,uBAAuB,GAAGvB,KAAK,IAAI;EACvC,MAAM;IACJiB,aAAa;IACbO,cAAc;IACdC,gBAAgB;IAChBP,eAAe;IACfC,eAAe;IACfH,iBAAiB;IACjBN;EACF,CAAC,GAAGV,KAAK;EACT,OAAO;IACL,CAACyB,gBAAgB,GAAG7B,MAAM,CAACC,MAAM,CAAC;MAChCuB,OAAO,EAAE,cAAc;MACvBC,aAAa,EAAE,KAAK;MACpBpB,UAAU,EAAEe,iBAAiB;MAC7BM,YAAY,EAAEE;IAChB,CAAC,EAAEf,2BAA2B,CAACQ,aAAa,EAAEP,IAAI,CAAC,CAAC;IACpD,CAAC,GAAGe,gBAAgB,KAAK,GAAG7B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEY,2BAA2B,CAACS,eAAe,EAAER,IAAI,CAAC,CAAC;IACjG,CAAC,GAAGe,gBAAgB,KAAK,GAAG7B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEY,2BAA2B,CAACU,eAAe,EAAET,IAAI,CAAC;EAClG,CAAC;AACH,CAAC;AACD,MAAMgB,2BAA2B,GAAGlC,IAAI,IAAII,MAAM,CAACC,MAAM,CAAC;EACxDC,KAAK,EAAEN;AACT,CAAC,EAAED,4BAA4B,CAACC,IAAI,CAAC,CAAC;AACtC,MAAMmC,uBAAuB,GAAG3B,KAAK,IAAI;EACvC,MAAM;IACJ4B,gBAAgB;IAChBC,aAAa;IACbb,iBAAiB;IACjBQ,cAAc;IACdd;EACF,CAAC,GAAGV,KAAK;EACT,OAAO;IACL,CAAC4B,gBAAgB,GAAGhC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;MAC9CuB,OAAO,EAAE,aAAa;MACtBU,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBV,aAAa,EAAE,QAAQ;MACvBpB,UAAU,EAAEe,iBAAiB;MAC7BM,YAAY,EAAEE;IAChB,CAAC,EAAEE,2BAA2B,CAAChB,IAAI,CAACmB,aAAa,CAAC,CAAClB,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MACnE,CAAC,GAAGgB,gBAAgB,OAAO,GAAG;QAC5BI,IAAI,EAAE;MACR,CAAC;MACD,CAAC,GAAGJ,gBAAgB,MAAM,GAAGhC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE6B,2BAA2B,CAACG,aAAa,CAAC,CAAC,EAAE;QACxGI,QAAQ,EAAEvB,IAAI,CAACmB,aAAa,CAAC,CAAClB,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;QAC5CsB,SAAS,EAAExB,IAAI,CAACmB,aAAa,CAAC,CAAClB,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;MAC9C,CAAC,CAAC;MACF,CAAC,GAAGgB,gBAAgB,OAAOA,gBAAgB,aAAa,GAAG;QACzDN,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;IACF,CAAC,GAAGM,gBAAgB,GAAGA,gBAAgB,SAAS,GAAG;MACjDN,YAAY,EAAE;IAChB;EACF,CAAC;AACH,CAAC;AACD,MAAMa,6BAA6B,GAAGA,CAACnC,KAAK,EAAER,IAAI,EAAE4C,SAAS,KAAK;EAChE,MAAM;IACJC;EACF,CAAC,GAAGrC,KAAK;EACT,OAAO;IACL,CAAC,GAAGoC,SAAS,GAAGC,iBAAiB,SAAS,GAAG;MAC3CvC,KAAK,EAAEN,IAAI;MACXqB,QAAQ,EAAErB,IAAI;MACd8B,YAAY,EAAE;IAChB,CAAC;IACD,CAAC,GAAGc,SAAS,GAAGC,iBAAiB,QAAQ,GAAG;MAC1Cf,YAAY,EAAE9B;IAChB;EACF,CAAC;AACH,CAAC;AACD,MAAM8C,4BAA4B,GAAGA,CAAC9C,IAAI,EAAEkB,IAAI,KAAKd,MAAM,CAACC,MAAM,CAAC;EACjEC,KAAK,EAAEY,IAAI,CAAClB,IAAI,CAAC,CAACmB,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EAChCC,QAAQ,EAAEH,IAAI,CAAClB,IAAI,CAAC,CAACmB,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;AACpC,CAAC,EAAErB,4BAA4B,CAACC,IAAI,CAAC,CAAC;AACtC,MAAM+C,wBAAwB,GAAGvC,KAAK,IAAI;EACxC,MAAM;IACJwB,cAAc;IACda,iBAAiB;IACjBpB,aAAa;IACbC,eAAe;IACfC,eAAe;IACfH,iBAAiB;IACjBN;EACF,CAAC,GAAGV,KAAK;EACT,OAAOJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;IAC3E,CAACwC,iBAAiB,GAAGzC,MAAM,CAACC,MAAM,CAAC;MACjCuB,OAAO,EAAE,cAAc;MACvBC,aAAa,EAAE,KAAK;MACpBpB,UAAU,EAAEe,iBAAiB;MAC7BM,YAAY,EAAEE,cAAc;MAC5B1B,KAAK,EAAEY,IAAI,CAACO,aAAa,CAAC,CAACN,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MACzCC,QAAQ,EAAEH,IAAI,CAACO,aAAa,CAAC,CAACN,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;IAC7C,CAAC,EAAE0B,4BAA4B,CAACrB,aAAa,EAAEP,IAAI,CAAC;EACtD,CAAC,EAAEyB,6BAA6B,CAACnC,KAAK,EAAEiB,aAAa,EAAEoB,iBAAiB,CAAC,CAAC,EAAE;IAC1E,CAAC,GAAGA,iBAAiB,KAAK,GAAGzC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEyC,4BAA4B,CAACpB,eAAe,EAAER,IAAI,CAAC;EACpG,CAAC,CAAC,EAAEyB,6BAA6B,CAACnC,KAAK,EAAEkB,eAAe,EAAE,GAAGmB,iBAAiB,KAAK,CAAC,CAAC,EAAE;IACrF,CAAC,GAAGA,iBAAiB,KAAK,GAAGzC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEyC,4BAA4B,CAACnB,eAAe,EAAET,IAAI,CAAC;EACpG,CAAC,CAAC,EAAEyB,6BAA6B,CAACnC,KAAK,EAAEmB,eAAe,EAAE,GAAGkB,iBAAiB,KAAK,CAAC,CAAC;AACvF,CAAC;AACD;AACA,MAAMG,YAAY,GAAGxC,KAAK,IAAI;EAC5B,MAAM;IACJyC,YAAY;IACZ1B,iBAAiB;IACjB2B,gBAAgB;IAChBC,oBAAoB;IACpBN,iBAAiB;IACjBZ,gBAAgB;IAChBG,gBAAgB;IAChBX,aAAa;IACbC,eAAe;IACfC,eAAe;IACfH,iBAAiB;IACjB4B,OAAO;IACPC,QAAQ;IACRvB,YAAY;IACZwB,WAAW;IACXC,WAAW;IACXC,iBAAiB;IACjBC,eAAe;IACfC;EACF,CAAC,GAAGlD,KAAK;EACT,OAAO;IACL,CAACyC,YAAY,GAAG;MACdrB,OAAO,EAAE,OAAO;MAChBtB,KAAK,EAAE,MAAM;MACb,CAAC,GAAG2C,YAAY,SAAS,GAAG;QAC1BrB,OAAO,EAAE,YAAY;QACrB+B,gBAAgB,EAAEP,OAAO;QACzBvB,aAAa,EAAE,KAAK;QACpB;QACA,CAACN,iBAAiB,GAAGnB,MAAM,CAACC,MAAM,CAAC;UACjCuB,OAAO,EAAE,cAAc;UACvBC,aAAa,EAAE,KAAK;UACpBpB,UAAU,EAAEe;QACd,CAAC,EAAErB,4BAA4B,CAACsB,aAAa,CAAC,CAAC;QAC/C,CAAC,GAAGF,iBAAiB,SAAS,GAAG;UAC/BO,YAAY,EAAE;QAChB,CAAC;QACD,CAAC,GAAGP,iBAAiB,KAAK,GAAGnB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,4BAA4B,CAACuB,eAAe,CAAC,CAAC;QAC7F,CAAC,GAAGH,iBAAiB,KAAK,GAAGnB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,4BAA4B,CAACwB,eAAe,CAAC;MAC9F,CAAC;MACD,CAAC,GAAGsB,YAAY,UAAU,GAAG;QAC3BrB,OAAO,EAAE,YAAY;QACrBtB,KAAK,EAAE,MAAM;QACbuB,aAAa,EAAE,KAAK;QACpB;QACA,CAACqB,gBAAgB,GAAG;UAClB5C,KAAK,EAAE,MAAM;UACbL,MAAM,EAAEqD,WAAW;UACnB7C,UAAU,EAAEe,iBAAiB;UAC7BM,YAAY,EAAEyB,WAAW;UACzB,CAAC,KAAKJ,oBAAoB,EAAE,GAAG;YAC7BS,gBAAgB,EAAEjC;UACpB;QACF,CAAC;QACD;QACA,CAACwB,oBAAoB,GAAG;UACtBC,OAAO,EAAE,CAAC;UACV,MAAM,EAAE;YACN9C,KAAK,EAAE,MAAM;YACbL,MAAM,EAAEuD,iBAAiB;YACzBK,SAAS,EAAE,MAAM;YACjBpD,UAAU,EAAEe,iBAAiB;YAC7BM,YAAY,EAAEyB,WAAW;YACzB,MAAM,EAAE;cACNK,gBAAgB,EAAEH;YACpB;UACF;QACF,CAAC;QACD,CAAC,GAAGN,oBAAoB,sDAAsD,GAAG;UAC/E7C,KAAK,EAAE;QACT;MACF,CAAC;MACD,CAAC,WAAW2C,YAAY,UAAU,GAAG;QACnC,CAAC,GAAGC,gBAAgB,KAAKC,oBAAoB,OAAO,GAAG;UACrDrB;QACF;MACF;IACF,CAAC;IACD,CAAC,GAAGmB,YAAY,gBAAgBA,YAAY,UAAU,GAAG;MACvD;MACA,CAACC,gBAAgB,GAAG;QAClBU,gBAAgB,EAAEP,QAAQ;QAC1B,CAAC,KAAKF,oBAAoB,EAAE,GAAG;UAC7BS,gBAAgB,EAAEF;QACpB;MACF;IACF,CAAC;IACD;IACA,CAAC,GAAGT,YAAY,GAAGA,YAAY,UAAU,GAAG7C,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;MAClGuB,OAAO,EAAE,cAAc;MACvBtB,KAAK,EAAE;IACT,CAAC,EAAEyC,wBAAwB,CAACvC,KAAK,CAAC,CAAC,EAAEc,wBAAwB,CAACd,KAAK,CAAC,CAAC,EAAEuB,uBAAuB,CAACvB,KAAK,CAAC,CAAC,EAAE2B,uBAAuB,CAAC3B,KAAK,CAAC,CAAC;IACvI;IACA,CAAC,GAAGyC,YAAY,GAAGA,YAAY,QAAQ,GAAG;MACxC3C,KAAK,EAAE,MAAM;MACb,CAACuC,iBAAiB,GAAG;QACnBvC,KAAK,EAAE;MACT,CAAC;MACD,CAAC2B,gBAAgB,GAAG;QAClB3B,KAAK,EAAE;MACT;IACF,CAAC;IACD;IACA,CAAC,GAAG2C,YAAY,GAAGA,YAAY,SAAS,GAAG;MACzC,CAAC;AACP,UAAUC,gBAAgB;AAC1B,UAAUC,oBAAoB;AAC9B,UAAU5B,iBAAiB;AAC3B,UAAUsB,iBAAiB;AAC3B,UAAUZ,gBAAgB;AAC1B,UAAUG,gBAAgB;AAC1B,OAAO,GAAGhC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEE,gBAAgB,CAACC,KAAK,CAAC;IAC/C;EACF,CAAC;AACH,CAAC;AACD;AACA,OAAO,MAAMsD,qBAAqB,GAAGtD,KAAK,IAAI;EAC5C,MAAM;IACJuD,gBAAgB;IAChBC;EACF,CAAC,GAAGxD,KAAK;EACT,MAAMgB,iBAAiB,GAAGuC,gBAAgB;EAC1C,MAAME,eAAe,GAAGD,SAAS;EACjC,OAAO;IACLE,KAAK,EAAE1C,iBAAiB;IACxB2C,gBAAgB,EAAEF,eAAe;IACjCzC,iBAAiB;IACjByC,eAAe;IACfX,WAAW,EAAE9C,KAAK,CAACiB,aAAa,GAAG,CAAC;IACpC8B,WAAW,EAAE/C,KAAK,CAACwB,cAAc;IACjC0B,kBAAkB,EAAElD,KAAK,CAAC4D,QAAQ,GAAG5D,KAAK,CAAC6D,SAAS;IACpDb,iBAAiB,EAAEhD,KAAK,CAACiB,aAAa,GAAG;EAC3C,CAAC;AACH,CAAC;AACD,eAAe9B,aAAa,CAAC,UAAU,EAAEa,KAAK,IAAI;EAChD,MAAM;IACJyC,YAAY;IACZ/B;EACF,CAAC,GAAGV,KAAK;EACT,MAAM8D,aAAa,GAAG1E,UAAU,CAACY,KAAK,EAAE;IACtCe,iBAAiB,EAAE,GAAG0B,YAAY,SAAS;IAC3CC,gBAAgB,EAAE,GAAGD,YAAY,QAAQ;IACzCE,oBAAoB,EAAE,GAAGF,YAAY,YAAY;IACjDJ,iBAAiB,EAAE,GAAGI,YAAY,SAAS;IAC3ChB,gBAAgB,EAAE,GAAGgB,YAAY,QAAQ;IACzCb,gBAAgB,EAAE,GAAGa,YAAY,QAAQ;IACzCZ,aAAa,EAAEnB,IAAI,CAACV,KAAK,CAACiB,aAAa,CAAC,CAACN,GAAG,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC;IACzDU,YAAY,EAAE,GAAG;IACjB;IACApB,yBAAyB,EAAE,0BAA0BF,KAAK,CAACgB,iBAAiB,SAAShB,KAAK,CAACyD,eAAe,SAASzD,KAAK,CAACgB,iBAAiB,OAAO;IACjJV,6BAA6B,EAAE;EACjC,CAAC,CAAC;EACF,OAAO,CAACkC,YAAY,CAACsB,aAAa,CAAC,CAAC;AACtC,CAAC,EAAER,qBAAqB,EAAE;EACxBS,gBAAgB,EAAE,CAAC,CAAC,OAAO,EAAE,mBAAmB,CAAC,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,CAAC;AAC5F,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}