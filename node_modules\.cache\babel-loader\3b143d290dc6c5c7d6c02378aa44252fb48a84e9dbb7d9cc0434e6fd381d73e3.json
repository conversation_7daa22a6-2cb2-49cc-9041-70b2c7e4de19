{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { genFocusOutline, genFocusStyle, resetComponent, textEllipsis } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genMotionStyle from './motion';\nconst genCardStyle = token => {\n  const {\n    componentCls,\n    tabsCardPadding,\n    cardBg,\n    cardGutter,\n    colorBorderSecondary,\n    itemSelectedColor\n  } = token;\n  return {\n    [`${componentCls}-card`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        [`${componentCls}-tab`]: {\n          margin: 0,\n          padding: tabsCardPadding,\n          background: cardBg,\n          border: `${unit(token.lineWidth)} ${token.lineType} ${colorBorderSecondary}`,\n          transition: `all ${token.motionDurationSlow} ${token.motionEaseInOut}`\n        },\n        [`${componentCls}-tab-active`]: {\n          color: itemSelectedColor,\n          background: token.colorBgContainer\n        },\n        [`${componentCls}-tab-focus`]: Object.assign({}, genFocusOutline(token, -3)),\n        [`${componentCls}-ink-bar`]: {\n          visibility: 'hidden'\n        },\n        [`& ${componentCls}-tab${componentCls}-tab-focus ${componentCls}-tab-btn`]: {\n          outline: 'none'\n        }\n      },\n      // ========================== Top & Bottom ==========================\n      [`&${componentCls}-top, &${componentCls}-bottom`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab + ${componentCls}-tab`]: {\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(cardGutter)\n            }\n          }\n        }\n      },\n      [`&${componentCls}-top`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: `${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)} 0 0`\n          },\n          [`${componentCls}-tab-active`]: {\n            borderBottomColor: token.colorBgContainer\n          }\n        }\n      },\n      [`&${componentCls}-bottom`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: `0 0 ${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)}`\n          },\n          [`${componentCls}-tab-active`]: {\n            borderTopColor: token.colorBgContainer\n          }\n        }\n      },\n      // ========================== Left & Right ==========================\n      [`&${componentCls}-left, &${componentCls}-right`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab + ${componentCls}-tab`]: {\n            marginTop: unit(cardGutter)\n          }\n        }\n      },\n      [`&${componentCls}-left`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `${unit(token.borderRadiusLG)} 0 0 ${unit(token.borderRadiusLG)}`\n            }\n          },\n          [`${componentCls}-tab-active`]: {\n            borderRightColor: {\n              _skip_check_: true,\n              value: token.colorBgContainer\n            }\n          }\n        }\n      },\n      [`&${componentCls}-right`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `0 ${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)} 0`\n            }\n          },\n          [`${componentCls}-tab-active`]: {\n            borderLeftColor: {\n              _skip_check_: true,\n              value: token.colorBgContainer\n            }\n          }\n        }\n      }\n    }\n  };\n};\nconst genDropdownStyle = token => {\n  const {\n    componentCls,\n    itemHoverColor,\n    dropdownEdgeChildVerticalPadding\n  } = token;\n  return {\n    [`${componentCls}-dropdown`]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'absolute',\n      top: -9999,\n      left: {\n        _skip_check_: true,\n        value: -9999\n      },\n      zIndex: token.zIndexPopup,\n      display: 'block',\n      '&-hidden': {\n        display: 'none'\n      },\n      [`${componentCls}-dropdown-menu`]: {\n        maxHeight: token.tabsDropdownHeight,\n        margin: 0,\n        padding: `${unit(dropdownEdgeChildVerticalPadding)} 0`,\n        overflowX: 'hidden',\n        overflowY: 'auto',\n        textAlign: {\n          _skip_check_: true,\n          value: 'left'\n        },\n        listStyleType: 'none',\n        backgroundColor: token.colorBgContainer,\n        backgroundClip: 'padding-box',\n        borderRadius: token.borderRadiusLG,\n        outline: 'none',\n        boxShadow: token.boxShadowSecondary,\n        '&-item': Object.assign(Object.assign({}, textEllipsis), {\n          display: 'flex',\n          alignItems: 'center',\n          minWidth: token.tabsDropdownWidth,\n          margin: 0,\n          padding: `${unit(token.paddingXXS)} ${unit(token.paddingSM)}`,\n          color: token.colorText,\n          fontWeight: 'normal',\n          fontSize: token.fontSize,\n          lineHeight: token.lineHeight,\n          cursor: 'pointer',\n          transition: `all ${token.motionDurationSlow}`,\n          '> span': {\n            flex: 1,\n            whiteSpace: 'nowrap'\n          },\n          '&-remove': {\n            flex: 'none',\n            marginLeft: {\n              _skip_check_: true,\n              value: token.marginSM\n            },\n            color: token.colorTextDescription,\n            fontSize: token.fontSizeSM,\n            background: 'transparent',\n            border: 0,\n            cursor: 'pointer',\n            '&:hover': {\n              color: itemHoverColor\n            }\n          },\n          '&:hover': {\n            background: token.controlItemBgHover\n          },\n          '&-disabled': {\n            '&, &:hover': {\n              color: token.colorTextDisabled,\n              background: 'transparent',\n              cursor: 'not-allowed'\n            }\n          }\n        })\n      }\n    })\n  };\n};\nconst genPositionStyle = token => {\n  const {\n    componentCls,\n    margin,\n    colorBorderSecondary,\n    horizontalMargin,\n    verticalItemPadding,\n    verticalItemMargin,\n    calc\n  } = token;\n  return {\n    // ========================== Top & Bottom ==========================\n    [`${componentCls}-top, ${componentCls}-bottom`]: {\n      flexDirection: 'column',\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        margin: horizontalMargin,\n        '&::before': {\n          position: 'absolute',\n          right: {\n            _skip_check_: true,\n            value: 0\n          },\n          left: {\n            _skip_check_: true,\n            value: 0\n          },\n          borderBottom: `${unit(token.lineWidth)} ${token.lineType} ${colorBorderSecondary}`,\n          content: \"''\"\n        },\n        [`${componentCls}-ink-bar`]: {\n          height: token.lineWidthBold,\n          '&-animated': {\n            transition: `width ${token.motionDurationSlow}, left ${token.motionDurationSlow},\n            right ${token.motionDurationSlow}`\n          }\n        },\n        [`${componentCls}-nav-wrap`]: {\n          '&::before, &::after': {\n            top: 0,\n            bottom: 0,\n            width: token.controlHeight\n          },\n          '&::before': {\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            boxShadow: token.boxShadowTabsOverflowLeft\n          },\n          '&::after': {\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            boxShadow: token.boxShadowTabsOverflowRight\n          },\n          [`&${componentCls}-nav-wrap-ping-left::before`]: {\n            opacity: 1\n          },\n          [`&${componentCls}-nav-wrap-ping-right::after`]: {\n            opacity: 1\n          }\n        }\n      }\n    },\n    [`${componentCls}-top`]: {\n      [`> ${componentCls}-nav,\n        > div > ${componentCls}-nav`]: {\n        '&::before': {\n          bottom: 0\n        },\n        [`${componentCls}-ink-bar`]: {\n          bottom: 0\n        }\n      }\n    },\n    [`${componentCls}-bottom`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        order: 1,\n        marginTop: margin,\n        marginBottom: 0,\n        '&::before': {\n          top: 0\n        },\n        [`${componentCls}-ink-bar`]: {\n          top: 0\n        }\n      },\n      [`> ${componentCls}-content-holder, > div > ${componentCls}-content-holder`]: {\n        order: 0\n      }\n    },\n    // ========================== Left & Right ==========================\n    [`${componentCls}-left, ${componentCls}-right`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        flexDirection: 'column',\n        minWidth: calc(token.controlHeight).mul(1.25).equal(),\n        // >>>>>>>>>>> Tab\n        [`${componentCls}-tab`]: {\n          padding: verticalItemPadding,\n          textAlign: 'center'\n        },\n        [`${componentCls}-tab + ${componentCls}-tab`]: {\n          margin: verticalItemMargin\n        },\n        // >>>>>>>>>>> Nav\n        [`${componentCls}-nav-wrap`]: {\n          flexDirection: 'column',\n          '&::before, &::after': {\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            height: token.controlHeight\n          },\n          '&::before': {\n            top: 0,\n            boxShadow: token.boxShadowTabsOverflowTop\n          },\n          '&::after': {\n            bottom: 0,\n            boxShadow: token.boxShadowTabsOverflowBottom\n          },\n          [`&${componentCls}-nav-wrap-ping-top::before`]: {\n            opacity: 1\n          },\n          [`&${componentCls}-nav-wrap-ping-bottom::after`]: {\n            opacity: 1\n          }\n        },\n        // >>>>>>>>>>> Ink Bar\n        [`${componentCls}-ink-bar`]: {\n          width: token.lineWidthBold,\n          '&-animated': {\n            transition: `height ${token.motionDurationSlow}, top ${token.motionDurationSlow}`\n          }\n        },\n        [`${componentCls}-nav-list, ${componentCls}-nav-operations`]: {\n          flex: '1 0 auto',\n          // fix safari scroll problem\n          flexDirection: 'column'\n        }\n      }\n    },\n    [`${componentCls}-left`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        [`${componentCls}-ink-bar`]: {\n          right: {\n            _skip_check_: true,\n            value: 0\n          }\n        }\n      },\n      [`> ${componentCls}-content-holder, > div > ${componentCls}-content-holder`]: {\n        marginLeft: {\n          _skip_check_: true,\n          value: unit(calc(token.lineWidth).mul(-1).equal())\n        },\n        borderLeft: {\n          _skip_check_: true,\n          value: `${unit(token.lineWidth)} ${token.lineType} ${token.colorBorder}`\n        },\n        [`> ${componentCls}-content > ${componentCls}-tabpane`]: {\n          paddingLeft: {\n            _skip_check_: true,\n            value: token.paddingLG\n          }\n        }\n      }\n    },\n    [`${componentCls}-right`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        order: 1,\n        [`${componentCls}-ink-bar`]: {\n          left: {\n            _skip_check_: true,\n            value: 0\n          }\n        }\n      },\n      [`> ${componentCls}-content-holder, > div > ${componentCls}-content-holder`]: {\n        order: 0,\n        marginRight: {\n          _skip_check_: true,\n          value: calc(token.lineWidth).mul(-1).equal()\n        },\n        borderRight: {\n          _skip_check_: true,\n          value: `${unit(token.lineWidth)} ${token.lineType} ${token.colorBorder}`\n        },\n        [`> ${componentCls}-content > ${componentCls}-tabpane`]: {\n          paddingRight: {\n            _skip_check_: true,\n            value: token.paddingLG\n          }\n        }\n      }\n    }\n  };\n};\nconst genSizeStyle = token => {\n  const {\n    componentCls,\n    cardPaddingSM,\n    cardPaddingLG,\n    horizontalItemPaddingSM,\n    horizontalItemPaddingLG\n  } = token;\n  return {\n    [componentCls]: {\n      '&-small': {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: horizontalItemPaddingSM,\n            fontSize: token.titleFontSizeSM\n          }\n        }\n      },\n      '&-large': {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: horizontalItemPaddingLG,\n            fontSize: token.titleFontSizeLG\n          }\n        }\n      }\n    },\n    [`${componentCls}-card`]: {\n      [`&${componentCls}-small`]: {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: cardPaddingSM\n          }\n        },\n        [`&${componentCls}-bottom`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: `0 0 ${unit(token.borderRadius)} ${unit(token.borderRadius)}`\n          }\n        },\n        [`&${componentCls}-top`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: `${unit(token.borderRadius)} ${unit(token.borderRadius)} 0 0`\n          }\n        },\n        [`&${componentCls}-right`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `0 ${unit(token.borderRadius)} ${unit(token.borderRadius)} 0`\n            }\n          }\n        },\n        [`&${componentCls}-left`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `${unit(token.borderRadius)} 0 0 ${unit(token.borderRadius)}`\n            }\n          }\n        }\n      },\n      [`&${componentCls}-large`]: {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: cardPaddingLG\n          }\n        }\n      }\n    }\n  };\n};\nconst genTabStyle = token => {\n  const {\n    componentCls,\n    itemActiveColor,\n    itemHoverColor,\n    iconCls,\n    tabsHorizontalItemMargin,\n    horizontalItemPadding,\n    itemSelectedColor,\n    itemColor\n  } = token;\n  const tabCls = `${componentCls}-tab`;\n  return {\n    [tabCls]: {\n      position: 'relative',\n      WebkitTouchCallout: 'none',\n      WebkitTapHighlightColor: 'transparent',\n      display: 'inline-flex',\n      alignItems: 'center',\n      padding: horizontalItemPadding,\n      fontSize: token.titleFontSize,\n      background: 'transparent',\n      border: 0,\n      outline: 'none',\n      cursor: 'pointer',\n      color: itemColor,\n      '&-btn, &-remove': {\n        '&:focus:not(:focus-visible), &:active': {\n          color: itemActiveColor\n        }\n      },\n      '&-btn': {\n        outline: 'none',\n        transition: `all ${token.motionDurationSlow}`,\n        [`${tabCls}-icon:not(:last-child)`]: {\n          marginInlineEnd: token.marginSM\n        }\n      },\n      '&-remove': Object.assign({\n        flex: 'none',\n        marginRight: {\n          _skip_check_: true,\n          value: token.calc(token.marginXXS).mul(-1).equal()\n        },\n        marginLeft: {\n          _skip_check_: true,\n          value: token.marginXS\n        },\n        color: token.colorTextDescription,\n        fontSize: token.fontSizeSM,\n        background: 'transparent',\n        border: 'none',\n        outline: 'none',\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationSlow}`,\n        '&:hover': {\n          color: token.colorTextHeading\n        }\n      }, genFocusStyle(token)),\n      '&:hover': {\n        color: itemHoverColor\n      },\n      [`&${tabCls}-active ${tabCls}-btn`]: {\n        color: itemSelectedColor,\n        textShadow: token.tabsActiveTextShadow\n      },\n      [`&${tabCls}-focus ${tabCls}-btn`]: Object.assign({}, genFocusOutline(token)),\n      [`&${tabCls}-disabled`]: {\n        color: token.colorTextDisabled,\n        cursor: 'not-allowed'\n      },\n      [`&${tabCls}-disabled ${tabCls}-btn, &${tabCls}-disabled ${componentCls}-remove`]: {\n        '&:focus, &:active': {\n          color: token.colorTextDisabled\n        }\n      },\n      [`& ${tabCls}-remove ${iconCls}`]: {\n        margin: 0\n      },\n      [`${iconCls}:not(:last-child)`]: {\n        marginRight: {\n          _skip_check_: true,\n          value: token.marginSM\n        }\n      }\n    },\n    [`${tabCls} + ${tabCls}`]: {\n      margin: {\n        _skip_check_: true,\n        value: tabsHorizontalItemMargin\n      }\n    }\n  };\n};\nconst genRtlStyle = token => {\n  const {\n    componentCls,\n    tabsHorizontalItemMarginRTL,\n    iconCls,\n    cardGutter,\n    calc\n  } = token;\n  const rtlCls = `${componentCls}-rtl`;\n  return {\n    [rtlCls]: {\n      direction: 'rtl',\n      [`${componentCls}-nav`]: {\n        [`${componentCls}-tab`]: {\n          margin: {\n            _skip_check_: true,\n            value: tabsHorizontalItemMarginRTL\n          },\n          [`${componentCls}-tab:last-of-type`]: {\n            marginLeft: {\n              _skip_check_: true,\n              value: 0\n            }\n          },\n          [iconCls]: {\n            marginRight: {\n              _skip_check_: true,\n              value: 0\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(token.marginSM)\n            }\n          },\n          [`${componentCls}-tab-remove`]: {\n            marginRight: {\n              _skip_check_: true,\n              value: unit(token.marginXS)\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(calc(token.marginXXS).mul(-1).equal())\n            },\n            [iconCls]: {\n              margin: 0\n            }\n          }\n        }\n      },\n      [`&${componentCls}-left`]: {\n        [`> ${componentCls}-nav`]: {\n          order: 1\n        },\n        [`> ${componentCls}-content-holder`]: {\n          order: 0\n        }\n      },\n      [`&${componentCls}-right`]: {\n        [`> ${componentCls}-nav`]: {\n          order: 0\n        },\n        [`> ${componentCls}-content-holder`]: {\n          order: 1\n        }\n      },\n      // ====================== Card ======================\n      [`&${componentCls}-card${componentCls}-top, &${componentCls}-card${componentCls}-bottom`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab + ${componentCls}-tab`]: {\n            marginRight: {\n              _skip_check_: true,\n              value: cardGutter\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: 0\n            }\n          }\n        }\n      }\n    },\n    [`${componentCls}-dropdown-rtl`]: {\n      direction: 'rtl'\n    },\n    [`${componentCls}-menu-item`]: {\n      [`${componentCls}-dropdown-rtl`]: {\n        textAlign: {\n          _skip_check_: true,\n          value: 'right'\n        }\n      }\n    }\n  };\n};\nconst genTabsStyle = token => {\n  const {\n    componentCls,\n    tabsCardPadding,\n    cardHeight,\n    cardGutter,\n    itemHoverColor,\n    itemActiveColor,\n    colorBorderSecondary\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'flex',\n      // ========================== Navigation ==========================\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        position: 'relative',\n        display: 'flex',\n        flex: 'none',\n        alignItems: 'center',\n        [`${componentCls}-nav-wrap`]: {\n          position: 'relative',\n          display: 'flex',\n          flex: 'auto',\n          alignSelf: 'stretch',\n          overflow: 'hidden',\n          whiteSpace: 'nowrap',\n          transform: 'translate(0)',\n          // Fix chrome render bug\n          // >>>>> Ping shadow\n          '&::before, &::after': {\n            position: 'absolute',\n            zIndex: 1,\n            opacity: 0,\n            transition: `opacity ${token.motionDurationSlow}`,\n            content: \"''\",\n            pointerEvents: 'none'\n          }\n        },\n        [`${componentCls}-nav-list`]: {\n          position: 'relative',\n          display: 'flex',\n          transition: `opacity ${token.motionDurationSlow}`\n        },\n        // >>>>>>>> Operations\n        [`${componentCls}-nav-operations`]: {\n          display: 'flex',\n          alignSelf: 'stretch'\n        },\n        [`${componentCls}-nav-operations-hidden`]: {\n          position: 'absolute',\n          visibility: 'hidden',\n          pointerEvents: 'none'\n        },\n        [`${componentCls}-nav-more`]: {\n          position: 'relative',\n          padding: tabsCardPadding,\n          background: 'transparent',\n          border: 0,\n          color: token.colorText,\n          '&::after': {\n            position: 'absolute',\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            bottom: 0,\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            height: token.calc(token.controlHeightLG).div(8).equal(),\n            transform: 'translateY(100%)',\n            content: \"''\"\n          }\n        },\n        [`${componentCls}-nav-add`]: Object.assign({\n          minWidth: cardHeight,\n          marginLeft: {\n            _skip_check_: true,\n            value: cardGutter\n          },\n          padding: unit(token.paddingXS),\n          background: 'transparent',\n          border: `${unit(token.lineWidth)} ${token.lineType} ${colorBorderSecondary}`,\n          borderRadius: `${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)} 0 0`,\n          outline: 'none',\n          cursor: 'pointer',\n          color: token.colorText,\n          transition: `all ${token.motionDurationSlow} ${token.motionEaseInOut}`,\n          '&:hover': {\n            color: itemHoverColor\n          },\n          '&:active, &:focus:not(:focus-visible)': {\n            color: itemActiveColor\n          }\n        }, genFocusStyle(token, -3))\n      },\n      [`${componentCls}-extra-content`]: {\n        flex: 'none'\n      },\n      // ============================ InkBar ============================\n      [`${componentCls}-ink-bar`]: {\n        position: 'absolute',\n        background: token.inkBarColor,\n        pointerEvents: 'none'\n      }\n    }), genTabStyle(token)), {\n      // =========================== TabPanes ===========================\n      [`${componentCls}-content`]: {\n        position: 'relative',\n        width: '100%'\n      },\n      [`${componentCls}-content-holder`]: {\n        flex: 'auto',\n        minWidth: 0,\n        minHeight: 0\n      },\n      [`${componentCls}-tabpane`]: Object.assign(Object.assign({}, genFocusStyle(token)), {\n        '&-hidden': {\n          display: 'none'\n        }\n      })\n    }),\n    [`${componentCls}-centered`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        [`${componentCls}-nav-wrap`]: {\n          [`&:not([class*='${componentCls}-nav-wrap-ping']) > ${componentCls}-nav-list`]: {\n            margin: 'auto'\n          }\n        }\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const cardHeight = token.controlHeightLG;\n  return {\n    zIndexPopup: token.zIndexPopupBase + 50,\n    cardBg: token.colorFillAlter,\n    cardHeight,\n    // Initialize with empty string, because cardPadding will be calculated with cardHeight by default.\n    cardPadding: `${(cardHeight - Math.round(token.fontSize * token.lineHeight)) / 2 - token.lineWidth}px ${token.padding}px`,\n    cardPaddingSM: `${token.paddingXXS * 1.5}px ${token.padding}px`,\n    cardPaddingLG: `${token.paddingXS}px ${token.padding}px ${token.paddingXXS * 1.5}px`,\n    titleFontSize: token.fontSize,\n    titleFontSizeLG: token.fontSizeLG,\n    titleFontSizeSM: token.fontSize,\n    inkBarColor: token.colorPrimary,\n    horizontalMargin: `0 0 ${token.margin}px 0`,\n    horizontalItemGutter: 32,\n    // Fixed Value\n    // Initialize with empty string, because horizontalItemMargin will be calculated with horizontalItemGutter by default.\n    horizontalItemMargin: ``,\n    horizontalItemMarginRTL: ``,\n    horizontalItemPadding: `${token.paddingSM}px 0`,\n    horizontalItemPaddingSM: `${token.paddingXS}px 0`,\n    horizontalItemPaddingLG: `${token.padding}px 0`,\n    verticalItemPadding: `${token.paddingXS}px ${token.paddingLG}px`,\n    verticalItemMargin: `${token.margin}px 0 0 0`,\n    itemColor: token.colorText,\n    itemSelectedColor: token.colorPrimary,\n    itemHoverColor: token.colorPrimaryHover,\n    itemActiveColor: token.colorPrimaryActive,\n    cardGutter: token.marginXXS / 2\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks('Tabs', token => {\n  const tabsToken = mergeToken(token, {\n    // `cardPadding` is empty by default, so we could calculate with dynamic `cardHeight`\n    tabsCardPadding: token.cardPadding,\n    dropdownEdgeChildVerticalPadding: token.paddingXXS,\n    tabsActiveTextShadow: '0 0 0.25px currentcolor',\n    tabsDropdownHeight: 200,\n    tabsDropdownWidth: 120,\n    tabsHorizontalItemMargin: `0 0 0 ${unit(token.horizontalItemGutter)}`,\n    tabsHorizontalItemMarginRTL: `0 0 0 ${unit(token.horizontalItemGutter)}`\n  });\n  return [genSizeStyle(tabsToken), genRtlStyle(tabsToken), genPositionStyle(tabsToken), genDropdownStyle(tabsToken), genCardStyle(tabsToken), genTabsStyle(tabsToken), genMotionStyle(tabsToken)];\n}, prepareComponentToken);", "map": {"version": 3, "names": ["unit", "genFocusOutline", "genFocusStyle", "resetComponent", "textEllipsis", "genStyleHooks", "mergeToken", "genMotionStyle", "genCardStyle", "token", "componentCls", "tabsCardPadding", "cardBg", "cardGutter", "colorBorderSecondary", "itemSelectedColor", "margin", "padding", "background", "border", "lineWidth", "lineType", "transition", "motionDurationSlow", "motionEaseInOut", "color", "colorBgContainer", "Object", "assign", "visibility", "outline", "marginLeft", "_skip_check_", "value", "borderRadius", "borderRadiusLG", "borderBottomColor", "borderTopColor", "marginTop", "borderRightColor", "borderLeftColor", "genDropdownStyle", "itemHoverColor", "dropdownEdgeChildVerticalPadding", "position", "top", "left", "zIndex", "zIndexPopup", "display", "maxHeight", "tabsDropdownHeight", "overflowX", "overflowY", "textAlign", "listStyleType", "backgroundColor", "backgroundClip", "boxShadow", "boxShadowSecondary", "alignItems", "min<PERSON><PERSON><PERSON>", "tabsDropdownWidth", "paddingXXS", "paddingSM", "colorText", "fontWeight", "fontSize", "lineHeight", "cursor", "flex", "whiteSpace", "marginSM", "colorTextDescription", "fontSizeSM", "controlItemBgHover", "colorTextDisabled", "genPositionStyle", "<PERSON><PERSON><PERSON><PERSON>", "verticalItemPadding", "verticalItemMargin", "calc", "flexDirection", "right", "borderBottom", "content", "height", "lineWidthBold", "bottom", "width", "controlHeight", "boxShadowTabsOverflowLeft", "boxShadowTabsOverflowRight", "opacity", "order", "marginBottom", "mul", "equal", "boxShadowTabsOverflowTop", "boxShadowTabsOverflowBottom", "borderLeft", "colorBorder", "paddingLeft", "paddingLG", "marginRight", "borderRight", "paddingRight", "genSizeStyle", "cardPaddingSM", "cardPaddingLG", "horizontalItemPaddingSM", "horizontalItemPaddingLG", "titleFontSizeSM", "titleFontSizeLG", "genTabStyle", "itemActiveColor", "iconCls", "tabsHorizontalItemMargin", "horizontalItemPadding", "itemColor", "tabCls", "WebkitTouchCallout", "WebkitTapHighlightColor", "titleFontSize", "marginInlineEnd", "marginXXS", "marginXS", "colorTextHeading", "textShadow", "tabsActiveTextShadow", "genRtlStyle", "tabsHorizontalItemMarginRTL", "rtlCls", "direction", "genTabsStyle", "cardHeight", "alignSelf", "overflow", "transform", "pointerEvents", "controlHeightLG", "div", "paddingXS", "inkBarColor", "minHeight", "prepareComponentToken", "zIndexPopupBase", "colorFillAlter", "cardPadding", "Math", "round", "fontSizeLG", "colorPrimary", "horizontalItemGutter", "horizontalItemMargin", "horizontalItemMarginRTL", "colorPrimaryHover", "colorPrimaryActive", "tabsToken"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/tabs/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { genFocusOutline, genFocusStyle, resetComponent, textEllipsis } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genMotionStyle from './motion';\nconst genCardStyle = token => {\n  const {\n    componentCls,\n    tabsCardPadding,\n    cardBg,\n    cardGutter,\n    colorBorderSecondary,\n    itemSelectedColor\n  } = token;\n  return {\n    [`${componentCls}-card`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        [`${componentCls}-tab`]: {\n          margin: 0,\n          padding: tabsCardPadding,\n          background: cardBg,\n          border: `${unit(token.lineWidth)} ${token.lineType} ${colorBorderSecondary}`,\n          transition: `all ${token.motionDurationSlow} ${token.motionEaseInOut}`\n        },\n        [`${componentCls}-tab-active`]: {\n          color: itemSelectedColor,\n          background: token.colorBgContainer\n        },\n        [`${componentCls}-tab-focus`]: Object.assign({}, genFocusOutline(token, -3)),\n        [`${componentCls}-ink-bar`]: {\n          visibility: 'hidden'\n        },\n        [`& ${componentCls}-tab${componentCls}-tab-focus ${componentCls}-tab-btn`]: {\n          outline: 'none'\n        }\n      },\n      // ========================== Top & Bottom ==========================\n      [`&${componentCls}-top, &${componentCls}-bottom`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab + ${componentCls}-tab`]: {\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(cardGutter)\n            }\n          }\n        }\n      },\n      [`&${componentCls}-top`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: `${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)} 0 0`\n          },\n          [`${componentCls}-tab-active`]: {\n            borderBottomColor: token.colorBgContainer\n          }\n        }\n      },\n      [`&${componentCls}-bottom`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: `0 0 ${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)}`\n          },\n          [`${componentCls}-tab-active`]: {\n            borderTopColor: token.colorBgContainer\n          }\n        }\n      },\n      // ========================== Left & Right ==========================\n      [`&${componentCls}-left, &${componentCls}-right`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab + ${componentCls}-tab`]: {\n            marginTop: unit(cardGutter)\n          }\n        }\n      },\n      [`&${componentCls}-left`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `${unit(token.borderRadiusLG)} 0 0 ${unit(token.borderRadiusLG)}`\n            }\n          },\n          [`${componentCls}-tab-active`]: {\n            borderRightColor: {\n              _skip_check_: true,\n              value: token.colorBgContainer\n            }\n          }\n        }\n      },\n      [`&${componentCls}-right`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `0 ${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)} 0`\n            }\n          },\n          [`${componentCls}-tab-active`]: {\n            borderLeftColor: {\n              _skip_check_: true,\n              value: token.colorBgContainer\n            }\n          }\n        }\n      }\n    }\n  };\n};\nconst genDropdownStyle = token => {\n  const {\n    componentCls,\n    itemHoverColor,\n    dropdownEdgeChildVerticalPadding\n  } = token;\n  return {\n    [`${componentCls}-dropdown`]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'absolute',\n      top: -9999,\n      left: {\n        _skip_check_: true,\n        value: -9999\n      },\n      zIndex: token.zIndexPopup,\n      display: 'block',\n      '&-hidden': {\n        display: 'none'\n      },\n      [`${componentCls}-dropdown-menu`]: {\n        maxHeight: token.tabsDropdownHeight,\n        margin: 0,\n        padding: `${unit(dropdownEdgeChildVerticalPadding)} 0`,\n        overflowX: 'hidden',\n        overflowY: 'auto',\n        textAlign: {\n          _skip_check_: true,\n          value: 'left'\n        },\n        listStyleType: 'none',\n        backgroundColor: token.colorBgContainer,\n        backgroundClip: 'padding-box',\n        borderRadius: token.borderRadiusLG,\n        outline: 'none',\n        boxShadow: token.boxShadowSecondary,\n        '&-item': Object.assign(Object.assign({}, textEllipsis), {\n          display: 'flex',\n          alignItems: 'center',\n          minWidth: token.tabsDropdownWidth,\n          margin: 0,\n          padding: `${unit(token.paddingXXS)} ${unit(token.paddingSM)}`,\n          color: token.colorText,\n          fontWeight: 'normal',\n          fontSize: token.fontSize,\n          lineHeight: token.lineHeight,\n          cursor: 'pointer',\n          transition: `all ${token.motionDurationSlow}`,\n          '> span': {\n            flex: 1,\n            whiteSpace: 'nowrap'\n          },\n          '&-remove': {\n            flex: 'none',\n            marginLeft: {\n              _skip_check_: true,\n              value: token.marginSM\n            },\n            color: token.colorTextDescription,\n            fontSize: token.fontSizeSM,\n            background: 'transparent',\n            border: 0,\n            cursor: 'pointer',\n            '&:hover': {\n              color: itemHoverColor\n            }\n          },\n          '&:hover': {\n            background: token.controlItemBgHover\n          },\n          '&-disabled': {\n            '&, &:hover': {\n              color: token.colorTextDisabled,\n              background: 'transparent',\n              cursor: 'not-allowed'\n            }\n          }\n        })\n      }\n    })\n  };\n};\nconst genPositionStyle = token => {\n  const {\n    componentCls,\n    margin,\n    colorBorderSecondary,\n    horizontalMargin,\n    verticalItemPadding,\n    verticalItemMargin,\n    calc\n  } = token;\n  return {\n    // ========================== Top & Bottom ==========================\n    [`${componentCls}-top, ${componentCls}-bottom`]: {\n      flexDirection: 'column',\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        margin: horizontalMargin,\n        '&::before': {\n          position: 'absolute',\n          right: {\n            _skip_check_: true,\n            value: 0\n          },\n          left: {\n            _skip_check_: true,\n            value: 0\n          },\n          borderBottom: `${unit(token.lineWidth)} ${token.lineType} ${colorBorderSecondary}`,\n          content: \"''\"\n        },\n        [`${componentCls}-ink-bar`]: {\n          height: token.lineWidthBold,\n          '&-animated': {\n            transition: `width ${token.motionDurationSlow}, left ${token.motionDurationSlow},\n            right ${token.motionDurationSlow}`\n          }\n        },\n        [`${componentCls}-nav-wrap`]: {\n          '&::before, &::after': {\n            top: 0,\n            bottom: 0,\n            width: token.controlHeight\n          },\n          '&::before': {\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            boxShadow: token.boxShadowTabsOverflowLeft\n          },\n          '&::after': {\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            boxShadow: token.boxShadowTabsOverflowRight\n          },\n          [`&${componentCls}-nav-wrap-ping-left::before`]: {\n            opacity: 1\n          },\n          [`&${componentCls}-nav-wrap-ping-right::after`]: {\n            opacity: 1\n          }\n        }\n      }\n    },\n    [`${componentCls}-top`]: {\n      [`> ${componentCls}-nav,\n        > div > ${componentCls}-nav`]: {\n        '&::before': {\n          bottom: 0\n        },\n        [`${componentCls}-ink-bar`]: {\n          bottom: 0\n        }\n      }\n    },\n    [`${componentCls}-bottom`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        order: 1,\n        marginTop: margin,\n        marginBottom: 0,\n        '&::before': {\n          top: 0\n        },\n        [`${componentCls}-ink-bar`]: {\n          top: 0\n        }\n      },\n      [`> ${componentCls}-content-holder, > div > ${componentCls}-content-holder`]: {\n        order: 0\n      }\n    },\n    // ========================== Left & Right ==========================\n    [`${componentCls}-left, ${componentCls}-right`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        flexDirection: 'column',\n        minWidth: calc(token.controlHeight).mul(1.25).equal(),\n        // >>>>>>>>>>> Tab\n        [`${componentCls}-tab`]: {\n          padding: verticalItemPadding,\n          textAlign: 'center'\n        },\n        [`${componentCls}-tab + ${componentCls}-tab`]: {\n          margin: verticalItemMargin\n        },\n        // >>>>>>>>>>> Nav\n        [`${componentCls}-nav-wrap`]: {\n          flexDirection: 'column',\n          '&::before, &::after': {\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            height: token.controlHeight\n          },\n          '&::before': {\n            top: 0,\n            boxShadow: token.boxShadowTabsOverflowTop\n          },\n          '&::after': {\n            bottom: 0,\n            boxShadow: token.boxShadowTabsOverflowBottom\n          },\n          [`&${componentCls}-nav-wrap-ping-top::before`]: {\n            opacity: 1\n          },\n          [`&${componentCls}-nav-wrap-ping-bottom::after`]: {\n            opacity: 1\n          }\n        },\n        // >>>>>>>>>>> Ink Bar\n        [`${componentCls}-ink-bar`]: {\n          width: token.lineWidthBold,\n          '&-animated': {\n            transition: `height ${token.motionDurationSlow}, top ${token.motionDurationSlow}`\n          }\n        },\n        [`${componentCls}-nav-list, ${componentCls}-nav-operations`]: {\n          flex: '1 0 auto',\n          // fix safari scroll problem\n          flexDirection: 'column'\n        }\n      }\n    },\n    [`${componentCls}-left`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        [`${componentCls}-ink-bar`]: {\n          right: {\n            _skip_check_: true,\n            value: 0\n          }\n        }\n      },\n      [`> ${componentCls}-content-holder, > div > ${componentCls}-content-holder`]: {\n        marginLeft: {\n          _skip_check_: true,\n          value: unit(calc(token.lineWidth).mul(-1).equal())\n        },\n        borderLeft: {\n          _skip_check_: true,\n          value: `${unit(token.lineWidth)} ${token.lineType} ${token.colorBorder}`\n        },\n        [`> ${componentCls}-content > ${componentCls}-tabpane`]: {\n          paddingLeft: {\n            _skip_check_: true,\n            value: token.paddingLG\n          }\n        }\n      }\n    },\n    [`${componentCls}-right`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        order: 1,\n        [`${componentCls}-ink-bar`]: {\n          left: {\n            _skip_check_: true,\n            value: 0\n          }\n        }\n      },\n      [`> ${componentCls}-content-holder, > div > ${componentCls}-content-holder`]: {\n        order: 0,\n        marginRight: {\n          _skip_check_: true,\n          value: calc(token.lineWidth).mul(-1).equal()\n        },\n        borderRight: {\n          _skip_check_: true,\n          value: `${unit(token.lineWidth)} ${token.lineType} ${token.colorBorder}`\n        },\n        [`> ${componentCls}-content > ${componentCls}-tabpane`]: {\n          paddingRight: {\n            _skip_check_: true,\n            value: token.paddingLG\n          }\n        }\n      }\n    }\n  };\n};\nconst genSizeStyle = token => {\n  const {\n    componentCls,\n    cardPaddingSM,\n    cardPaddingLG,\n    horizontalItemPaddingSM,\n    horizontalItemPaddingLG\n  } = token;\n  return {\n    [componentCls]: {\n      '&-small': {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: horizontalItemPaddingSM,\n            fontSize: token.titleFontSizeSM\n          }\n        }\n      },\n      '&-large': {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: horizontalItemPaddingLG,\n            fontSize: token.titleFontSizeLG\n          }\n        }\n      }\n    },\n    [`${componentCls}-card`]: {\n      [`&${componentCls}-small`]: {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: cardPaddingSM\n          }\n        },\n        [`&${componentCls}-bottom`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: `0 0 ${unit(token.borderRadius)} ${unit(token.borderRadius)}`\n          }\n        },\n        [`&${componentCls}-top`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: `${unit(token.borderRadius)} ${unit(token.borderRadius)} 0 0`\n          }\n        },\n        [`&${componentCls}-right`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `0 ${unit(token.borderRadius)} ${unit(token.borderRadius)} 0`\n            }\n          }\n        },\n        [`&${componentCls}-left`]: {\n          [`> ${componentCls}-nav ${componentCls}-tab`]: {\n            borderRadius: {\n              _skip_check_: true,\n              value: `${unit(token.borderRadius)} 0 0 ${unit(token.borderRadius)}`\n            }\n          }\n        }\n      },\n      [`&${componentCls}-large`]: {\n        [`> ${componentCls}-nav`]: {\n          [`${componentCls}-tab`]: {\n            padding: cardPaddingLG\n          }\n        }\n      }\n    }\n  };\n};\nconst genTabStyle = token => {\n  const {\n    componentCls,\n    itemActiveColor,\n    itemHoverColor,\n    iconCls,\n    tabsHorizontalItemMargin,\n    horizontalItemPadding,\n    itemSelectedColor,\n    itemColor\n  } = token;\n  const tabCls = `${componentCls}-tab`;\n  return {\n    [tabCls]: {\n      position: 'relative',\n      WebkitTouchCallout: 'none',\n      WebkitTapHighlightColor: 'transparent',\n      display: 'inline-flex',\n      alignItems: 'center',\n      padding: horizontalItemPadding,\n      fontSize: token.titleFontSize,\n      background: 'transparent',\n      border: 0,\n      outline: 'none',\n      cursor: 'pointer',\n      color: itemColor,\n      '&-btn, &-remove': {\n        '&:focus:not(:focus-visible), &:active': {\n          color: itemActiveColor\n        }\n      },\n      '&-btn': {\n        outline: 'none',\n        transition: `all ${token.motionDurationSlow}`,\n        [`${tabCls}-icon:not(:last-child)`]: {\n          marginInlineEnd: token.marginSM\n        }\n      },\n      '&-remove': Object.assign({\n        flex: 'none',\n        marginRight: {\n          _skip_check_: true,\n          value: token.calc(token.marginXXS).mul(-1).equal()\n        },\n        marginLeft: {\n          _skip_check_: true,\n          value: token.marginXS\n        },\n        color: token.colorTextDescription,\n        fontSize: token.fontSizeSM,\n        background: 'transparent',\n        border: 'none',\n        outline: 'none',\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationSlow}`,\n        '&:hover': {\n          color: token.colorTextHeading\n        }\n      }, genFocusStyle(token)),\n      '&:hover': {\n        color: itemHoverColor\n      },\n      [`&${tabCls}-active ${tabCls}-btn`]: {\n        color: itemSelectedColor,\n        textShadow: token.tabsActiveTextShadow\n      },\n      [`&${tabCls}-focus ${tabCls}-btn`]: Object.assign({}, genFocusOutline(token)),\n      [`&${tabCls}-disabled`]: {\n        color: token.colorTextDisabled,\n        cursor: 'not-allowed'\n      },\n      [`&${tabCls}-disabled ${tabCls}-btn, &${tabCls}-disabled ${componentCls}-remove`]: {\n        '&:focus, &:active': {\n          color: token.colorTextDisabled\n        }\n      },\n      [`& ${tabCls}-remove ${iconCls}`]: {\n        margin: 0\n      },\n      [`${iconCls}:not(:last-child)`]: {\n        marginRight: {\n          _skip_check_: true,\n          value: token.marginSM\n        }\n      }\n    },\n    [`${tabCls} + ${tabCls}`]: {\n      margin: {\n        _skip_check_: true,\n        value: tabsHorizontalItemMargin\n      }\n    }\n  };\n};\nconst genRtlStyle = token => {\n  const {\n    componentCls,\n    tabsHorizontalItemMarginRTL,\n    iconCls,\n    cardGutter,\n    calc\n  } = token;\n  const rtlCls = `${componentCls}-rtl`;\n  return {\n    [rtlCls]: {\n      direction: 'rtl',\n      [`${componentCls}-nav`]: {\n        [`${componentCls}-tab`]: {\n          margin: {\n            _skip_check_: true,\n            value: tabsHorizontalItemMarginRTL\n          },\n          [`${componentCls}-tab:last-of-type`]: {\n            marginLeft: {\n              _skip_check_: true,\n              value: 0\n            }\n          },\n          [iconCls]: {\n            marginRight: {\n              _skip_check_: true,\n              value: 0\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(token.marginSM)\n            }\n          },\n          [`${componentCls}-tab-remove`]: {\n            marginRight: {\n              _skip_check_: true,\n              value: unit(token.marginXS)\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: unit(calc(token.marginXXS).mul(-1).equal())\n            },\n            [iconCls]: {\n              margin: 0\n            }\n          }\n        }\n      },\n      [`&${componentCls}-left`]: {\n        [`> ${componentCls}-nav`]: {\n          order: 1\n        },\n        [`> ${componentCls}-content-holder`]: {\n          order: 0\n        }\n      },\n      [`&${componentCls}-right`]: {\n        [`> ${componentCls}-nav`]: {\n          order: 0\n        },\n        [`> ${componentCls}-content-holder`]: {\n          order: 1\n        }\n      },\n      // ====================== Card ======================\n      [`&${componentCls}-card${componentCls}-top, &${componentCls}-card${componentCls}-bottom`]: {\n        [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n          [`${componentCls}-tab + ${componentCls}-tab`]: {\n            marginRight: {\n              _skip_check_: true,\n              value: cardGutter\n            },\n            marginLeft: {\n              _skip_check_: true,\n              value: 0\n            }\n          }\n        }\n      }\n    },\n    [`${componentCls}-dropdown-rtl`]: {\n      direction: 'rtl'\n    },\n    [`${componentCls}-menu-item`]: {\n      [`${componentCls}-dropdown-rtl`]: {\n        textAlign: {\n          _skip_check_: true,\n          value: 'right'\n        }\n      }\n    }\n  };\n};\nconst genTabsStyle = token => {\n  const {\n    componentCls,\n    tabsCardPadding,\n    cardHeight,\n    cardGutter,\n    itemHoverColor,\n    itemActiveColor,\n    colorBorderSecondary\n  } = token;\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'flex',\n      // ========================== Navigation ==========================\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        position: 'relative',\n        display: 'flex',\n        flex: 'none',\n        alignItems: 'center',\n        [`${componentCls}-nav-wrap`]: {\n          position: 'relative',\n          display: 'flex',\n          flex: 'auto',\n          alignSelf: 'stretch',\n          overflow: 'hidden',\n          whiteSpace: 'nowrap',\n          transform: 'translate(0)',\n          // Fix chrome render bug\n          // >>>>> Ping shadow\n          '&::before, &::after': {\n            position: 'absolute',\n            zIndex: 1,\n            opacity: 0,\n            transition: `opacity ${token.motionDurationSlow}`,\n            content: \"''\",\n            pointerEvents: 'none'\n          }\n        },\n        [`${componentCls}-nav-list`]: {\n          position: 'relative',\n          display: 'flex',\n          transition: `opacity ${token.motionDurationSlow}`\n        },\n        // >>>>>>>> Operations\n        [`${componentCls}-nav-operations`]: {\n          display: 'flex',\n          alignSelf: 'stretch'\n        },\n        [`${componentCls}-nav-operations-hidden`]: {\n          position: 'absolute',\n          visibility: 'hidden',\n          pointerEvents: 'none'\n        },\n        [`${componentCls}-nav-more`]: {\n          position: 'relative',\n          padding: tabsCardPadding,\n          background: 'transparent',\n          border: 0,\n          color: token.colorText,\n          '&::after': {\n            position: 'absolute',\n            right: {\n              _skip_check_: true,\n              value: 0\n            },\n            bottom: 0,\n            left: {\n              _skip_check_: true,\n              value: 0\n            },\n            height: token.calc(token.controlHeightLG).div(8).equal(),\n            transform: 'translateY(100%)',\n            content: \"''\"\n          }\n        },\n        [`${componentCls}-nav-add`]: Object.assign({\n          minWidth: cardHeight,\n          marginLeft: {\n            _skip_check_: true,\n            value: cardGutter\n          },\n          padding: unit(token.paddingXS),\n          background: 'transparent',\n          border: `${unit(token.lineWidth)} ${token.lineType} ${colorBorderSecondary}`,\n          borderRadius: `${unit(token.borderRadiusLG)} ${unit(token.borderRadiusLG)} 0 0`,\n          outline: 'none',\n          cursor: 'pointer',\n          color: token.colorText,\n          transition: `all ${token.motionDurationSlow} ${token.motionEaseInOut}`,\n          '&:hover': {\n            color: itemHoverColor\n          },\n          '&:active, &:focus:not(:focus-visible)': {\n            color: itemActiveColor\n          }\n        }, genFocusStyle(token, -3))\n      },\n      [`${componentCls}-extra-content`]: {\n        flex: 'none'\n      },\n      // ============================ InkBar ============================\n      [`${componentCls}-ink-bar`]: {\n        position: 'absolute',\n        background: token.inkBarColor,\n        pointerEvents: 'none'\n      }\n    }), genTabStyle(token)), {\n      // =========================== TabPanes ===========================\n      [`${componentCls}-content`]: {\n        position: 'relative',\n        width: '100%'\n      },\n      [`${componentCls}-content-holder`]: {\n        flex: 'auto',\n        minWidth: 0,\n        minHeight: 0\n      },\n      [`${componentCls}-tabpane`]: Object.assign(Object.assign({}, genFocusStyle(token)), {\n        '&-hidden': {\n          display: 'none'\n        }\n      })\n    }),\n    [`${componentCls}-centered`]: {\n      [`> ${componentCls}-nav, > div > ${componentCls}-nav`]: {\n        [`${componentCls}-nav-wrap`]: {\n          [`&:not([class*='${componentCls}-nav-wrap-ping']) > ${componentCls}-nav-list`]: {\n            margin: 'auto'\n          }\n        }\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const cardHeight = token.controlHeightLG;\n  return {\n    zIndexPopup: token.zIndexPopupBase + 50,\n    cardBg: token.colorFillAlter,\n    cardHeight,\n    // Initialize with empty string, because cardPadding will be calculated with cardHeight by default.\n    cardPadding: `${(cardHeight - Math.round(token.fontSize * token.lineHeight)) / 2 - token.lineWidth}px ${token.padding}px`,\n    cardPaddingSM: `${token.paddingXXS * 1.5}px ${token.padding}px`,\n    cardPaddingLG: `${token.paddingXS}px ${token.padding}px ${token.paddingXXS * 1.5}px`,\n    titleFontSize: token.fontSize,\n    titleFontSizeLG: token.fontSizeLG,\n    titleFontSizeSM: token.fontSize,\n    inkBarColor: token.colorPrimary,\n    horizontalMargin: `0 0 ${token.margin}px 0`,\n    horizontalItemGutter: 32,\n    // Fixed Value\n    // Initialize with empty string, because horizontalItemMargin will be calculated with horizontalItemGutter by default.\n    horizontalItemMargin: ``,\n    horizontalItemMarginRTL: ``,\n    horizontalItemPadding: `${token.paddingSM}px 0`,\n    horizontalItemPaddingSM: `${token.paddingXS}px 0`,\n    horizontalItemPaddingLG: `${token.padding}px 0`,\n    verticalItemPadding: `${token.paddingXS}px ${token.paddingLG}px`,\n    verticalItemMargin: `${token.margin}px 0 0 0`,\n    itemColor: token.colorText,\n    itemSelectedColor: token.colorPrimary,\n    itemHoverColor: token.colorPrimaryHover,\n    itemActiveColor: token.colorPrimaryActive,\n    cardGutter: token.marginXXS / 2\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks('Tabs', token => {\n  const tabsToken = mergeToken(token, {\n    // `cardPadding` is empty by default, so we could calculate with dynamic `cardHeight`\n    tabsCardPadding: token.cardPadding,\n    dropdownEdgeChildVerticalPadding: token.paddingXXS,\n    tabsActiveTextShadow: '0 0 0.25px currentcolor',\n    tabsDropdownHeight: 200,\n    tabsDropdownWidth: 120,\n    tabsHorizontalItemMargin: `0 0 0 ${unit(token.horizontalItemGutter)}`,\n    tabsHorizontalItemMarginRTL: `0 0 0 ${unit(token.horizontalItemGutter)}`\n  });\n  return [genSizeStyle(tabsToken), genRtlStyle(tabsToken), genPositionStyle(tabsToken), genDropdownStyle(tabsToken), genCardStyle(tabsToken), genTabsStyle(tabsToken), genMotionStyle(tabsToken)];\n}, prepareComponentToken);"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,eAAe,EAAEC,aAAa,EAAEC,cAAc,EAAEC,YAAY,QAAQ,aAAa;AAC1F,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,OAAOC,cAAc,MAAM,UAAU;AACrC,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,eAAe;IACfC,MAAM;IACNC,UAAU;IACVC,oBAAoB;IACpBC;EACF,CAAC,GAAGN,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,OAAO,GAAG;MACxB,CAAC,KAAKA,YAAY,iBAAiBA,YAAY,MAAM,GAAG;QACtD,CAAC,GAAGA,YAAY,MAAM,GAAG;UACvBM,MAAM,EAAE,CAAC;UACTC,OAAO,EAAEN,eAAe;UACxBO,UAAU,EAAEN,MAAM;UAClBO,MAAM,EAAE,GAAGnB,IAAI,CAACS,KAAK,CAACW,SAAS,CAAC,IAAIX,KAAK,CAACY,QAAQ,IAAIP,oBAAoB,EAAE;UAC5EQ,UAAU,EAAE,OAAOb,KAAK,CAACc,kBAAkB,IAAId,KAAK,CAACe,eAAe;QACtE,CAAC;QACD,CAAC,GAAGd,YAAY,aAAa,GAAG;UAC9Be,KAAK,EAAEV,iBAAiB;UACxBG,UAAU,EAAET,KAAK,CAACiB;QACpB,CAAC;QACD,CAAC,GAAGhB,YAAY,YAAY,GAAGiB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3B,eAAe,CAACQ,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5E,CAAC,GAAGC,YAAY,UAAU,GAAG;UAC3BmB,UAAU,EAAE;QACd,CAAC;QACD,CAAC,KAAKnB,YAAY,OAAOA,YAAY,cAAcA,YAAY,UAAU,GAAG;UAC1EoB,OAAO,EAAE;QACX;MACF,CAAC;MACD;MACA,CAAC,IAAIpB,YAAY,UAAUA,YAAY,SAAS,GAAG;QACjD,CAAC,KAAKA,YAAY,iBAAiBA,YAAY,MAAM,GAAG;UACtD,CAAC,GAAGA,YAAY,UAAUA,YAAY,MAAM,GAAG;YAC7CqB,UAAU,EAAE;cACVC,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAEjC,IAAI,CAACa,UAAU;YACxB;UACF;QACF;MACF,CAAC;MACD,CAAC,IAAIH,YAAY,MAAM,GAAG;QACxB,CAAC,KAAKA,YAAY,iBAAiBA,YAAY,MAAM,GAAG;UACtD,CAAC,GAAGA,YAAY,MAAM,GAAG;YACvBwB,YAAY,EAAE,GAAGlC,IAAI,CAACS,KAAK,CAAC0B,cAAc,CAAC,IAAInC,IAAI,CAACS,KAAK,CAAC0B,cAAc,CAAC;UAC3E,CAAC;UACD,CAAC,GAAGzB,YAAY,aAAa,GAAG;YAC9B0B,iBAAiB,EAAE3B,KAAK,CAACiB;UAC3B;QACF;MACF,CAAC;MACD,CAAC,IAAIhB,YAAY,SAAS,GAAG;QAC3B,CAAC,KAAKA,YAAY,iBAAiBA,YAAY,MAAM,GAAG;UACtD,CAAC,GAAGA,YAAY,MAAM,GAAG;YACvBwB,YAAY,EAAE,OAAOlC,IAAI,CAACS,KAAK,CAAC0B,cAAc,CAAC,IAAInC,IAAI,CAACS,KAAK,CAAC0B,cAAc,CAAC;UAC/E,CAAC;UACD,CAAC,GAAGzB,YAAY,aAAa,GAAG;YAC9B2B,cAAc,EAAE5B,KAAK,CAACiB;UACxB;QACF;MACF,CAAC;MACD;MACA,CAAC,IAAIhB,YAAY,WAAWA,YAAY,QAAQ,GAAG;QACjD,CAAC,KAAKA,YAAY,iBAAiBA,YAAY,MAAM,GAAG;UACtD,CAAC,GAAGA,YAAY,UAAUA,YAAY,MAAM,GAAG;YAC7C4B,SAAS,EAAEtC,IAAI,CAACa,UAAU;UAC5B;QACF;MACF,CAAC;MACD,CAAC,IAAIH,YAAY,OAAO,GAAG;QACzB,CAAC,KAAKA,YAAY,iBAAiBA,YAAY,MAAM,GAAG;UACtD,CAAC,GAAGA,YAAY,MAAM,GAAG;YACvBwB,YAAY,EAAE;cACZF,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE,GAAGjC,IAAI,CAACS,KAAK,CAAC0B,cAAc,CAAC,QAAQnC,IAAI,CAACS,KAAK,CAAC0B,cAAc,CAAC;YACxE;UACF,CAAC;UACD,CAAC,GAAGzB,YAAY,aAAa,GAAG;YAC9B6B,gBAAgB,EAAE;cAChBP,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAExB,KAAK,CAACiB;YACf;UACF;QACF;MACF,CAAC;MACD,CAAC,IAAIhB,YAAY,QAAQ,GAAG;QAC1B,CAAC,KAAKA,YAAY,iBAAiBA,YAAY,MAAM,GAAG;UACtD,CAAC,GAAGA,YAAY,MAAM,GAAG;YACvBwB,YAAY,EAAE;cACZF,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE,KAAKjC,IAAI,CAACS,KAAK,CAAC0B,cAAc,CAAC,IAAInC,IAAI,CAACS,KAAK,CAAC0B,cAAc,CAAC;YACtE;UACF,CAAC;UACD,CAAC,GAAGzB,YAAY,aAAa,GAAG;YAC9B8B,eAAe,EAAE;cACfR,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAExB,KAAK,CAACiB;YACf;UACF;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMe,gBAAgB,GAAGhC,KAAK,IAAI;EAChC,MAAM;IACJC,YAAY;IACZgC,cAAc;IACdC;EACF,CAAC,GAAGlC,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,WAAW,GAAGiB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEzB,cAAc,CAACM,KAAK,CAAC,CAAC,EAAE;MACpFmC,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,CAAC,IAAI;MACVC,IAAI,EAAE;QACJd,YAAY,EAAE,IAAI;QAClBC,KAAK,EAAE,CAAC;MACV,CAAC;MACDc,MAAM,EAAEtC,KAAK,CAACuC,WAAW;MACzBC,OAAO,EAAE,OAAO;MAChB,UAAU,EAAE;QACVA,OAAO,EAAE;MACX,CAAC;MACD,CAAC,GAAGvC,YAAY,gBAAgB,GAAG;QACjCwC,SAAS,EAAEzC,KAAK,CAAC0C,kBAAkB;QACnCnC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,GAAGjB,IAAI,CAAC2C,gCAAgC,CAAC,IAAI;QACtDS,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE;UACTtB,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT,CAAC;QACDsB,aAAa,EAAE,MAAM;QACrBC,eAAe,EAAE/C,KAAK,CAACiB,gBAAgB;QACvC+B,cAAc,EAAE,aAAa;QAC7BvB,YAAY,EAAEzB,KAAK,CAAC0B,cAAc;QAClCL,OAAO,EAAE,MAAM;QACf4B,SAAS,EAAEjD,KAAK,CAACkD,kBAAkB;QACnC,QAAQ,EAAEhC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAExB,YAAY,CAAC,EAAE;UACvD6C,OAAO,EAAE,MAAM;UACfW,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAEpD,KAAK,CAACqD,iBAAiB;UACjC9C,MAAM,EAAE,CAAC;UACTC,OAAO,EAAE,GAAGjB,IAAI,CAACS,KAAK,CAACsD,UAAU,CAAC,IAAI/D,IAAI,CAACS,KAAK,CAACuD,SAAS,CAAC,EAAE;UAC7DvC,KAAK,EAAEhB,KAAK,CAACwD,SAAS;UACtBC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE1D,KAAK,CAAC0D,QAAQ;UACxBC,UAAU,EAAE3D,KAAK,CAAC2D,UAAU;UAC5BC,MAAM,EAAE,SAAS;UACjB/C,UAAU,EAAE,OAAOb,KAAK,CAACc,kBAAkB,EAAE;UAC7C,QAAQ,EAAE;YACR+C,IAAI,EAAE,CAAC;YACPC,UAAU,EAAE;UACd,CAAC;UACD,UAAU,EAAE;YACVD,IAAI,EAAE,MAAM;YACZvC,UAAU,EAAE;cACVC,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAExB,KAAK,CAAC+D;YACf,CAAC;YACD/C,KAAK,EAAEhB,KAAK,CAACgE,oBAAoB;YACjCN,QAAQ,EAAE1D,KAAK,CAACiE,UAAU;YAC1BxD,UAAU,EAAE,aAAa;YACzBC,MAAM,EAAE,CAAC;YACTkD,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE;cACT5C,KAAK,EAAEiB;YACT;UACF,CAAC;UACD,SAAS,EAAE;YACTxB,UAAU,EAAET,KAAK,CAACkE;UACpB,CAAC;UACD,YAAY,EAAE;YACZ,YAAY,EAAE;cACZlD,KAAK,EAAEhB,KAAK,CAACmE,iBAAiB;cAC9B1D,UAAU,EAAE,aAAa;cACzBmD,MAAM,EAAE;YACV;UACF;QACF,CAAC;MACH;IACF,CAAC;EACH,CAAC;AACH,CAAC;AACD,MAAMQ,gBAAgB,GAAGpE,KAAK,IAAI;EAChC,MAAM;IACJC,YAAY;IACZM,MAAM;IACNF,oBAAoB;IACpBgE,gBAAgB;IAChBC,mBAAmB;IACnBC,kBAAkB;IAClBC;EACF,CAAC,GAAGxE,KAAK;EACT,OAAO;IACL;IACA,CAAC,GAAGC,YAAY,SAASA,YAAY,SAAS,GAAG;MAC/CwE,aAAa,EAAE,QAAQ;MACvB,CAAC,KAAKxE,YAAY,iBAAiBA,YAAY,MAAM,GAAG;QACtDM,MAAM,EAAE8D,gBAAgB;QACxB,WAAW,EAAE;UACXlC,QAAQ,EAAE,UAAU;UACpBuC,KAAK,EAAE;YACLnD,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAE;UACT,CAAC;UACDa,IAAI,EAAE;YACJd,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAE;UACT,CAAC;UACDmD,YAAY,EAAE,GAAGpF,IAAI,CAACS,KAAK,CAACW,SAAS,CAAC,IAAIX,KAAK,CAACY,QAAQ,IAAIP,oBAAoB,EAAE;UAClFuE,OAAO,EAAE;QACX,CAAC;QACD,CAAC,GAAG3E,YAAY,UAAU,GAAG;UAC3B4E,MAAM,EAAE7E,KAAK,CAAC8E,aAAa;UAC3B,YAAY,EAAE;YACZjE,UAAU,EAAE,SAASb,KAAK,CAACc,kBAAkB,UAAUd,KAAK,CAACc,kBAAkB;AAC3F,oBAAoBd,KAAK,CAACc,kBAAkB;UAClC;QACF,CAAC;QACD,CAAC,GAAGb,YAAY,WAAW,GAAG;UAC5B,qBAAqB,EAAE;YACrBmC,GAAG,EAAE,CAAC;YACN2C,MAAM,EAAE,CAAC;YACTC,KAAK,EAAEhF,KAAK,CAACiF;UACf,CAAC;UACD,WAAW,EAAE;YACX5C,IAAI,EAAE;cACJd,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT,CAAC;YACDyB,SAAS,EAAEjD,KAAK,CAACkF;UACnB,CAAC;UACD,UAAU,EAAE;YACVR,KAAK,EAAE;cACLnD,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT,CAAC;YACDyB,SAAS,EAAEjD,KAAK,CAACmF;UACnB,CAAC;UACD,CAAC,IAAIlF,YAAY,6BAA6B,GAAG;YAC/CmF,OAAO,EAAE;UACX,CAAC;UACD,CAAC,IAAInF,YAAY,6BAA6B,GAAG;YAC/CmF,OAAO,EAAE;UACX;QACF;MACF;IACF,CAAC;IACD,CAAC,GAAGnF,YAAY,MAAM,GAAG;MACvB,CAAC,KAAKA,YAAY;AACxB,kBAAkBA,YAAY,MAAM,GAAG;QAC/B,WAAW,EAAE;UACX8E,MAAM,EAAE;QACV,CAAC;QACD,CAAC,GAAG9E,YAAY,UAAU,GAAG;UAC3B8E,MAAM,EAAE;QACV;MACF;IACF,CAAC;IACD,CAAC,GAAG9E,YAAY,SAAS,GAAG;MAC1B,CAAC,KAAKA,YAAY,iBAAiBA,YAAY,MAAM,GAAG;QACtDoF,KAAK,EAAE,CAAC;QACRxD,SAAS,EAAEtB,MAAM;QACjB+E,YAAY,EAAE,CAAC;QACf,WAAW,EAAE;UACXlD,GAAG,EAAE;QACP,CAAC;QACD,CAAC,GAAGnC,YAAY,UAAU,GAAG;UAC3BmC,GAAG,EAAE;QACP;MACF,CAAC;MACD,CAAC,KAAKnC,YAAY,4BAA4BA,YAAY,iBAAiB,GAAG;QAC5EoF,KAAK,EAAE;MACT;IACF,CAAC;IACD;IACA,CAAC,GAAGpF,YAAY,UAAUA,YAAY,QAAQ,GAAG;MAC/C,CAAC,KAAKA,YAAY,iBAAiBA,YAAY,MAAM,GAAG;QACtDwE,aAAa,EAAE,QAAQ;QACvBrB,QAAQ,EAAEoB,IAAI,CAACxE,KAAK,CAACiF,aAAa,CAAC,CAACM,GAAG,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC;QACrD;QACA,CAAC,GAAGvF,YAAY,MAAM,GAAG;UACvBO,OAAO,EAAE8D,mBAAmB;UAC5BzB,SAAS,EAAE;QACb,CAAC;QACD,CAAC,GAAG5C,YAAY,UAAUA,YAAY,MAAM,GAAG;UAC7CM,MAAM,EAAEgE;QACV,CAAC;QACD;QACA,CAAC,GAAGtE,YAAY,WAAW,GAAG;UAC5BwE,aAAa,EAAE,QAAQ;UACvB,qBAAqB,EAAE;YACrBC,KAAK,EAAE;cACLnD,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT,CAAC;YACDa,IAAI,EAAE;cACJd,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT,CAAC;YACDqD,MAAM,EAAE7E,KAAK,CAACiF;UAChB,CAAC;UACD,WAAW,EAAE;YACX7C,GAAG,EAAE,CAAC;YACNa,SAAS,EAAEjD,KAAK,CAACyF;UACnB,CAAC;UACD,UAAU,EAAE;YACVV,MAAM,EAAE,CAAC;YACT9B,SAAS,EAAEjD,KAAK,CAAC0F;UACnB,CAAC;UACD,CAAC,IAAIzF,YAAY,4BAA4B,GAAG;YAC9CmF,OAAO,EAAE;UACX,CAAC;UACD,CAAC,IAAInF,YAAY,8BAA8B,GAAG;YAChDmF,OAAO,EAAE;UACX;QACF,CAAC;QACD;QACA,CAAC,GAAGnF,YAAY,UAAU,GAAG;UAC3B+E,KAAK,EAAEhF,KAAK,CAAC8E,aAAa;UAC1B,YAAY,EAAE;YACZjE,UAAU,EAAE,UAAUb,KAAK,CAACc,kBAAkB,SAASd,KAAK,CAACc,kBAAkB;UACjF;QACF,CAAC;QACD,CAAC,GAAGb,YAAY,cAAcA,YAAY,iBAAiB,GAAG;UAC5D4D,IAAI,EAAE,UAAU;UAChB;UACAY,aAAa,EAAE;QACjB;MACF;IACF,CAAC;IACD,CAAC,GAAGxE,YAAY,OAAO,GAAG;MACxB,CAAC,KAAKA,YAAY,iBAAiBA,YAAY,MAAM,GAAG;QACtD,CAAC,GAAGA,YAAY,UAAU,GAAG;UAC3ByE,KAAK,EAAE;YACLnD,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACD,CAAC,KAAKvB,YAAY,4BAA4BA,YAAY,iBAAiB,GAAG;QAC5EqB,UAAU,EAAE;UACVC,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAEjC,IAAI,CAACiF,IAAI,CAACxE,KAAK,CAACW,SAAS,CAAC,CAAC4E,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;QACnD,CAAC;QACDG,UAAU,EAAE;UACVpE,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE,GAAGjC,IAAI,CAACS,KAAK,CAACW,SAAS,CAAC,IAAIX,KAAK,CAACY,QAAQ,IAAIZ,KAAK,CAAC4F,WAAW;QACxE,CAAC;QACD,CAAC,KAAK3F,YAAY,cAAcA,YAAY,UAAU,GAAG;UACvD4F,WAAW,EAAE;YACXtE,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAExB,KAAK,CAAC8F;UACf;QACF;MACF;IACF,CAAC;IACD,CAAC,GAAG7F,YAAY,QAAQ,GAAG;MACzB,CAAC,KAAKA,YAAY,iBAAiBA,YAAY,MAAM,GAAG;QACtDoF,KAAK,EAAE,CAAC;QACR,CAAC,GAAGpF,YAAY,UAAU,GAAG;UAC3BoC,IAAI,EAAE;YACJd,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAE;UACT;QACF;MACF,CAAC;MACD,CAAC,KAAKvB,YAAY,4BAA4BA,YAAY,iBAAiB,GAAG;QAC5EoF,KAAK,EAAE,CAAC;QACRU,WAAW,EAAE;UACXxE,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAEgD,IAAI,CAACxE,KAAK,CAACW,SAAS,CAAC,CAAC4E,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;QAC7C,CAAC;QACDQ,WAAW,EAAE;UACXzE,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE,GAAGjC,IAAI,CAACS,KAAK,CAACW,SAAS,CAAC,IAAIX,KAAK,CAACY,QAAQ,IAAIZ,KAAK,CAAC4F,WAAW;QACxE,CAAC;QACD,CAAC,KAAK3F,YAAY,cAAcA,YAAY,UAAU,GAAG;UACvDgG,YAAY,EAAE;YACZ1E,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAExB,KAAK,CAAC8F;UACf;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMI,YAAY,GAAGlG,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZkG,aAAa;IACbC,aAAa;IACbC,uBAAuB;IACvBC;EACF,CAAC,GAAGtG,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACd,SAAS,EAAE;QACT,CAAC,KAAKA,YAAY,MAAM,GAAG;UACzB,CAAC,GAAGA,YAAY,MAAM,GAAG;YACvBO,OAAO,EAAE6F,uBAAuB;YAChC3C,QAAQ,EAAE1D,KAAK,CAACuG;UAClB;QACF;MACF,CAAC;MACD,SAAS,EAAE;QACT,CAAC,KAAKtG,YAAY,MAAM,GAAG;UACzB,CAAC,GAAGA,YAAY,MAAM,GAAG;YACvBO,OAAO,EAAE8F,uBAAuB;YAChC5C,QAAQ,EAAE1D,KAAK,CAACwG;UAClB;QACF;MACF;IACF,CAAC;IACD,CAAC,GAAGvG,YAAY,OAAO,GAAG;MACxB,CAAC,IAAIA,YAAY,QAAQ,GAAG;QAC1B,CAAC,KAAKA,YAAY,MAAM,GAAG;UACzB,CAAC,GAAGA,YAAY,MAAM,GAAG;YACvBO,OAAO,EAAE2F;UACX;QACF,CAAC;QACD,CAAC,IAAIlG,YAAY,SAAS,GAAG;UAC3B,CAAC,KAAKA,YAAY,QAAQA,YAAY,MAAM,GAAG;YAC7CwB,YAAY,EAAE,OAAOlC,IAAI,CAACS,KAAK,CAACyB,YAAY,CAAC,IAAIlC,IAAI,CAACS,KAAK,CAACyB,YAAY,CAAC;UAC3E;QACF,CAAC;QACD,CAAC,IAAIxB,YAAY,MAAM,GAAG;UACxB,CAAC,KAAKA,YAAY,QAAQA,YAAY,MAAM,GAAG;YAC7CwB,YAAY,EAAE,GAAGlC,IAAI,CAACS,KAAK,CAACyB,YAAY,CAAC,IAAIlC,IAAI,CAACS,KAAK,CAACyB,YAAY,CAAC;UACvE;QACF,CAAC;QACD,CAAC,IAAIxB,YAAY,QAAQ,GAAG;UAC1B,CAAC,KAAKA,YAAY,QAAQA,YAAY,MAAM,GAAG;YAC7CwB,YAAY,EAAE;cACZF,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE,KAAKjC,IAAI,CAACS,KAAK,CAACyB,YAAY,CAAC,IAAIlC,IAAI,CAACS,KAAK,CAACyB,YAAY,CAAC;YAClE;UACF;QACF,CAAC;QACD,CAAC,IAAIxB,YAAY,OAAO,GAAG;UACzB,CAAC,KAAKA,YAAY,QAAQA,YAAY,MAAM,GAAG;YAC7CwB,YAAY,EAAE;cACZF,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE,GAAGjC,IAAI,CAACS,KAAK,CAACyB,YAAY,CAAC,QAAQlC,IAAI,CAACS,KAAK,CAACyB,YAAY,CAAC;YACpE;UACF;QACF;MACF,CAAC;MACD,CAAC,IAAIxB,YAAY,QAAQ,GAAG;QAC1B,CAAC,KAAKA,YAAY,MAAM,GAAG;UACzB,CAAC,GAAGA,YAAY,MAAM,GAAG;YACvBO,OAAO,EAAE4F;UACX;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMK,WAAW,GAAGzG,KAAK,IAAI;EAC3B,MAAM;IACJC,YAAY;IACZyG,eAAe;IACfzE,cAAc;IACd0E,OAAO;IACPC,wBAAwB;IACxBC,qBAAqB;IACrBvG,iBAAiB;IACjBwG;EACF,CAAC,GAAG9G,KAAK;EACT,MAAM+G,MAAM,GAAG,GAAG9G,YAAY,MAAM;EACpC,OAAO;IACL,CAAC8G,MAAM,GAAG;MACR5E,QAAQ,EAAE,UAAU;MACpB6E,kBAAkB,EAAE,MAAM;MAC1BC,uBAAuB,EAAE,aAAa;MACtCzE,OAAO,EAAE,aAAa;MACtBW,UAAU,EAAE,QAAQ;MACpB3C,OAAO,EAAEqG,qBAAqB;MAC9BnD,QAAQ,EAAE1D,KAAK,CAACkH,aAAa;MAC7BzG,UAAU,EAAE,aAAa;MACzBC,MAAM,EAAE,CAAC;MACTW,OAAO,EAAE,MAAM;MACfuC,MAAM,EAAE,SAAS;MACjB5C,KAAK,EAAE8F,SAAS;MAChB,iBAAiB,EAAE;QACjB,uCAAuC,EAAE;UACvC9F,KAAK,EAAE0F;QACT;MACF,CAAC;MACD,OAAO,EAAE;QACPrF,OAAO,EAAE,MAAM;QACfR,UAAU,EAAE,OAAOb,KAAK,CAACc,kBAAkB,EAAE;QAC7C,CAAC,GAAGiG,MAAM,wBAAwB,GAAG;UACnCI,eAAe,EAAEnH,KAAK,CAAC+D;QACzB;MACF,CAAC;MACD,UAAU,EAAE7C,MAAM,CAACC,MAAM,CAAC;QACxB0C,IAAI,EAAE,MAAM;QACZkC,WAAW,EAAE;UACXxE,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAExB,KAAK,CAACwE,IAAI,CAACxE,KAAK,CAACoH,SAAS,CAAC,CAAC7B,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;QACnD,CAAC;QACDlE,UAAU,EAAE;UACVC,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAExB,KAAK,CAACqH;QACf,CAAC;QACDrG,KAAK,EAAEhB,KAAK,CAACgE,oBAAoB;QACjCN,QAAQ,EAAE1D,KAAK,CAACiE,UAAU;QAC1BxD,UAAU,EAAE,aAAa;QACzBC,MAAM,EAAE,MAAM;QACdW,OAAO,EAAE,MAAM;QACfuC,MAAM,EAAE,SAAS;QACjB/C,UAAU,EAAE,OAAOb,KAAK,CAACc,kBAAkB,EAAE;QAC7C,SAAS,EAAE;UACTE,KAAK,EAAEhB,KAAK,CAACsH;QACf;MACF,CAAC,EAAE7H,aAAa,CAACO,KAAK,CAAC,CAAC;MACxB,SAAS,EAAE;QACTgB,KAAK,EAAEiB;MACT,CAAC;MACD,CAAC,IAAI8E,MAAM,WAAWA,MAAM,MAAM,GAAG;QACnC/F,KAAK,EAAEV,iBAAiB;QACxBiH,UAAU,EAAEvH,KAAK,CAACwH;MACpB,CAAC;MACD,CAAC,IAAIT,MAAM,UAAUA,MAAM,MAAM,GAAG7F,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE3B,eAAe,CAACQ,KAAK,CAAC,CAAC;MAC7E,CAAC,IAAI+G,MAAM,WAAW,GAAG;QACvB/F,KAAK,EAAEhB,KAAK,CAACmE,iBAAiB;QAC9BP,MAAM,EAAE;MACV,CAAC;MACD,CAAC,IAAImD,MAAM,aAAaA,MAAM,UAAUA,MAAM,aAAa9G,YAAY,SAAS,GAAG;QACjF,mBAAmB,EAAE;UACnBe,KAAK,EAAEhB,KAAK,CAACmE;QACf;MACF,CAAC;MACD,CAAC,KAAK4C,MAAM,WAAWJ,OAAO,EAAE,GAAG;QACjCpG,MAAM,EAAE;MACV,CAAC;MACD,CAAC,GAAGoG,OAAO,mBAAmB,GAAG;QAC/BZ,WAAW,EAAE;UACXxE,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAExB,KAAK,CAAC+D;QACf;MACF;IACF,CAAC;IACD,CAAC,GAAGgD,MAAM,MAAMA,MAAM,EAAE,GAAG;MACzBxG,MAAM,EAAE;QACNgB,YAAY,EAAE,IAAI;QAClBC,KAAK,EAAEoF;MACT;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMa,WAAW,GAAGzH,KAAK,IAAI;EAC3B,MAAM;IACJC,YAAY;IACZyH,2BAA2B;IAC3Bf,OAAO;IACPvG,UAAU;IACVoE;EACF,CAAC,GAAGxE,KAAK;EACT,MAAM2H,MAAM,GAAG,GAAG1H,YAAY,MAAM;EACpC,OAAO;IACL,CAAC0H,MAAM,GAAG;MACRC,SAAS,EAAE,KAAK;MAChB,CAAC,GAAG3H,YAAY,MAAM,GAAG;QACvB,CAAC,GAAGA,YAAY,MAAM,GAAG;UACvBM,MAAM,EAAE;YACNgB,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAEkG;UACT,CAAC;UACD,CAAC,GAAGzH,YAAY,mBAAmB,GAAG;YACpCqB,UAAU,EAAE;cACVC,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT;UACF,CAAC;UACD,CAACmF,OAAO,GAAG;YACTZ,WAAW,EAAE;cACXxE,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT,CAAC;YACDF,UAAU,EAAE;cACVC,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAEjC,IAAI,CAACS,KAAK,CAAC+D,QAAQ;YAC5B;UACF,CAAC;UACD,CAAC,GAAG9D,YAAY,aAAa,GAAG;YAC9B8F,WAAW,EAAE;cACXxE,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAEjC,IAAI,CAACS,KAAK,CAACqH,QAAQ;YAC5B,CAAC;YACD/F,UAAU,EAAE;cACVC,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAEjC,IAAI,CAACiF,IAAI,CAACxE,KAAK,CAACoH,SAAS,CAAC,CAAC7B,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;YACnD,CAAC;YACD,CAACmB,OAAO,GAAG;cACTpG,MAAM,EAAE;YACV;UACF;QACF;MACF,CAAC;MACD,CAAC,IAAIN,YAAY,OAAO,GAAG;QACzB,CAAC,KAAKA,YAAY,MAAM,GAAG;UACzBoF,KAAK,EAAE;QACT,CAAC;QACD,CAAC,KAAKpF,YAAY,iBAAiB,GAAG;UACpCoF,KAAK,EAAE;QACT;MACF,CAAC;MACD,CAAC,IAAIpF,YAAY,QAAQ,GAAG;QAC1B,CAAC,KAAKA,YAAY,MAAM,GAAG;UACzBoF,KAAK,EAAE;QACT,CAAC;QACD,CAAC,KAAKpF,YAAY,iBAAiB,GAAG;UACpCoF,KAAK,EAAE;QACT;MACF,CAAC;MACD;MACA,CAAC,IAAIpF,YAAY,QAAQA,YAAY,UAAUA,YAAY,QAAQA,YAAY,SAAS,GAAG;QACzF,CAAC,KAAKA,YAAY,iBAAiBA,YAAY,MAAM,GAAG;UACtD,CAAC,GAAGA,YAAY,UAAUA,YAAY,MAAM,GAAG;YAC7C8F,WAAW,EAAE;cACXxE,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAEpB;YACT,CAAC;YACDkB,UAAU,EAAE;cACVC,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT;UACF;QACF;MACF;IACF,CAAC;IACD,CAAC,GAAGvB,YAAY,eAAe,GAAG;MAChC2H,SAAS,EAAE;IACb,CAAC;IACD,CAAC,GAAG3H,YAAY,YAAY,GAAG;MAC7B,CAAC,GAAGA,YAAY,eAAe,GAAG;QAChC4C,SAAS,EAAE;UACTtB,YAAY,EAAE,IAAI;UAClBC,KAAK,EAAE;QACT;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMqG,YAAY,GAAG7H,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,eAAe;IACf4H,UAAU;IACV1H,UAAU;IACV6B,cAAc;IACdyE,eAAe;IACfrG;EACF,CAAC,GAAGL,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAGiB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEzB,cAAc,CAACM,KAAK,CAAC,CAAC,EAAE;MAClGwC,OAAO,EAAE,MAAM;MACf;MACA,CAAC,KAAKvC,YAAY,iBAAiBA,YAAY,MAAM,GAAG;QACtDkC,QAAQ,EAAE,UAAU;QACpBK,OAAO,EAAE,MAAM;QACfqB,IAAI,EAAE,MAAM;QACZV,UAAU,EAAE,QAAQ;QACpB,CAAC,GAAGlD,YAAY,WAAW,GAAG;UAC5BkC,QAAQ,EAAE,UAAU;UACpBK,OAAO,EAAE,MAAM;UACfqB,IAAI,EAAE,MAAM;UACZkE,SAAS,EAAE,SAAS;UACpBC,QAAQ,EAAE,QAAQ;UAClBlE,UAAU,EAAE,QAAQ;UACpBmE,SAAS,EAAE,cAAc;UACzB;UACA;UACA,qBAAqB,EAAE;YACrB9F,QAAQ,EAAE,UAAU;YACpBG,MAAM,EAAE,CAAC;YACT8C,OAAO,EAAE,CAAC;YACVvE,UAAU,EAAE,WAAWb,KAAK,CAACc,kBAAkB,EAAE;YACjD8D,OAAO,EAAE,IAAI;YACbsD,aAAa,EAAE;UACjB;QACF,CAAC;QACD,CAAC,GAAGjI,YAAY,WAAW,GAAG;UAC5BkC,QAAQ,EAAE,UAAU;UACpBK,OAAO,EAAE,MAAM;UACf3B,UAAU,EAAE,WAAWb,KAAK,CAACc,kBAAkB;QACjD,CAAC;QACD;QACA,CAAC,GAAGb,YAAY,iBAAiB,GAAG;UAClCuC,OAAO,EAAE,MAAM;UACfuF,SAAS,EAAE;QACb,CAAC;QACD,CAAC,GAAG9H,YAAY,wBAAwB,GAAG;UACzCkC,QAAQ,EAAE,UAAU;UACpBf,UAAU,EAAE,QAAQ;UACpB8G,aAAa,EAAE;QACjB,CAAC;QACD,CAAC,GAAGjI,YAAY,WAAW,GAAG;UAC5BkC,QAAQ,EAAE,UAAU;UACpB3B,OAAO,EAAEN,eAAe;UACxBO,UAAU,EAAE,aAAa;UACzBC,MAAM,EAAE,CAAC;UACTM,KAAK,EAAEhB,KAAK,CAACwD,SAAS;UACtB,UAAU,EAAE;YACVrB,QAAQ,EAAE,UAAU;YACpBuC,KAAK,EAAE;cACLnD,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT,CAAC;YACDuD,MAAM,EAAE,CAAC;YACT1C,IAAI,EAAE;cACJd,YAAY,EAAE,IAAI;cAClBC,KAAK,EAAE;YACT,CAAC;YACDqD,MAAM,EAAE7E,KAAK,CAACwE,IAAI,CAACxE,KAAK,CAACmI,eAAe,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC5C,KAAK,CAAC,CAAC;YACxDyC,SAAS,EAAE,kBAAkB;YAC7BrD,OAAO,EAAE;UACX;QACF,CAAC;QACD,CAAC,GAAG3E,YAAY,UAAU,GAAGiB,MAAM,CAACC,MAAM,CAAC;UACzCiC,QAAQ,EAAE0E,UAAU;UACpBxG,UAAU,EAAE;YACVC,YAAY,EAAE,IAAI;YAClBC,KAAK,EAAEpB;UACT,CAAC;UACDI,OAAO,EAAEjB,IAAI,CAACS,KAAK,CAACqI,SAAS,CAAC;UAC9B5H,UAAU,EAAE,aAAa;UACzBC,MAAM,EAAE,GAAGnB,IAAI,CAACS,KAAK,CAACW,SAAS,CAAC,IAAIX,KAAK,CAACY,QAAQ,IAAIP,oBAAoB,EAAE;UAC5EoB,YAAY,EAAE,GAAGlC,IAAI,CAACS,KAAK,CAAC0B,cAAc,CAAC,IAAInC,IAAI,CAACS,KAAK,CAAC0B,cAAc,CAAC,MAAM;UAC/EL,OAAO,EAAE,MAAM;UACfuC,MAAM,EAAE,SAAS;UACjB5C,KAAK,EAAEhB,KAAK,CAACwD,SAAS;UACtB3C,UAAU,EAAE,OAAOb,KAAK,CAACc,kBAAkB,IAAId,KAAK,CAACe,eAAe,EAAE;UACtE,SAAS,EAAE;YACTC,KAAK,EAAEiB;UACT,CAAC;UACD,uCAAuC,EAAE;YACvCjB,KAAK,EAAE0F;UACT;QACF,CAAC,EAAEjH,aAAa,CAACO,KAAK,EAAE,CAAC,CAAC,CAAC;MAC7B,CAAC;MACD,CAAC,GAAGC,YAAY,gBAAgB,GAAG;QACjC4D,IAAI,EAAE;MACR,CAAC;MACD;MACA,CAAC,GAAG5D,YAAY,UAAU,GAAG;QAC3BkC,QAAQ,EAAE,UAAU;QACpB1B,UAAU,EAAET,KAAK,CAACsI,WAAW;QAC7BJ,aAAa,EAAE;MACjB;IACF,CAAC,CAAC,EAAEzB,WAAW,CAACzG,KAAK,CAAC,CAAC,EAAE;MACvB;MACA,CAAC,GAAGC,YAAY,UAAU,GAAG;QAC3BkC,QAAQ,EAAE,UAAU;QACpB6C,KAAK,EAAE;MACT,CAAC;MACD,CAAC,GAAG/E,YAAY,iBAAiB,GAAG;QAClC4D,IAAI,EAAE,MAAM;QACZT,QAAQ,EAAE,CAAC;QACXmF,SAAS,EAAE;MACb,CAAC;MACD,CAAC,GAAGtI,YAAY,UAAU,GAAGiB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE1B,aAAa,CAACO,KAAK,CAAC,CAAC,EAAE;QAClF,UAAU,EAAE;UACVwC,OAAO,EAAE;QACX;MACF,CAAC;IACH,CAAC,CAAC;IACF,CAAC,GAAGvC,YAAY,WAAW,GAAG;MAC5B,CAAC,KAAKA,YAAY,iBAAiBA,YAAY,MAAM,GAAG;QACtD,CAAC,GAAGA,YAAY,WAAW,GAAG;UAC5B,CAAC,kBAAkBA,YAAY,uBAAuBA,YAAY,WAAW,GAAG;YAC9EM,MAAM,EAAE;UACV;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMiI,qBAAqB,GAAGxI,KAAK,IAAI;EAC5C,MAAM8H,UAAU,GAAG9H,KAAK,CAACmI,eAAe;EACxC,OAAO;IACL5F,WAAW,EAAEvC,KAAK,CAACyI,eAAe,GAAG,EAAE;IACvCtI,MAAM,EAAEH,KAAK,CAAC0I,cAAc;IAC5BZ,UAAU;IACV;IACAa,WAAW,EAAE,GAAG,CAACb,UAAU,GAAGc,IAAI,CAACC,KAAK,CAAC7I,KAAK,CAAC0D,QAAQ,GAAG1D,KAAK,CAAC2D,UAAU,CAAC,IAAI,CAAC,GAAG3D,KAAK,CAACW,SAAS,MAAMX,KAAK,CAACQ,OAAO,IAAI;IACzH2F,aAAa,EAAE,GAAGnG,KAAK,CAACsD,UAAU,GAAG,GAAG,MAAMtD,KAAK,CAACQ,OAAO,IAAI;IAC/D4F,aAAa,EAAE,GAAGpG,KAAK,CAACqI,SAAS,MAAMrI,KAAK,CAACQ,OAAO,MAAMR,KAAK,CAACsD,UAAU,GAAG,GAAG,IAAI;IACpF4D,aAAa,EAAElH,KAAK,CAAC0D,QAAQ;IAC7B8C,eAAe,EAAExG,KAAK,CAAC8I,UAAU;IACjCvC,eAAe,EAAEvG,KAAK,CAAC0D,QAAQ;IAC/B4E,WAAW,EAAEtI,KAAK,CAAC+I,YAAY;IAC/B1E,gBAAgB,EAAE,OAAOrE,KAAK,CAACO,MAAM,MAAM;IAC3CyI,oBAAoB,EAAE,EAAE;IACxB;IACA;IACAC,oBAAoB,EAAE,EAAE;IACxBC,uBAAuB,EAAE,EAAE;IAC3BrC,qBAAqB,EAAE,GAAG7G,KAAK,CAACuD,SAAS,MAAM;IAC/C8C,uBAAuB,EAAE,GAAGrG,KAAK,CAACqI,SAAS,MAAM;IACjD/B,uBAAuB,EAAE,GAAGtG,KAAK,CAACQ,OAAO,MAAM;IAC/C8D,mBAAmB,EAAE,GAAGtE,KAAK,CAACqI,SAAS,MAAMrI,KAAK,CAAC8F,SAAS,IAAI;IAChEvB,kBAAkB,EAAE,GAAGvE,KAAK,CAACO,MAAM,UAAU;IAC7CuG,SAAS,EAAE9G,KAAK,CAACwD,SAAS;IAC1BlD,iBAAiB,EAAEN,KAAK,CAAC+I,YAAY;IACrC9G,cAAc,EAAEjC,KAAK,CAACmJ,iBAAiB;IACvCzC,eAAe,EAAE1G,KAAK,CAACoJ,kBAAkB;IACzChJ,UAAU,EAAEJ,KAAK,CAACoH,SAAS,GAAG;EAChC,CAAC;AACH,CAAC;AACD;AACA,eAAexH,aAAa,CAAC,MAAM,EAAEI,KAAK,IAAI;EAC5C,MAAMqJ,SAAS,GAAGxJ,UAAU,CAACG,KAAK,EAAE;IAClC;IACAE,eAAe,EAAEF,KAAK,CAAC2I,WAAW;IAClCzG,gCAAgC,EAAElC,KAAK,CAACsD,UAAU;IAClDkE,oBAAoB,EAAE,yBAAyB;IAC/C9E,kBAAkB,EAAE,GAAG;IACvBW,iBAAiB,EAAE,GAAG;IACtBuD,wBAAwB,EAAE,SAASrH,IAAI,CAACS,KAAK,CAACgJ,oBAAoB,CAAC,EAAE;IACrEtB,2BAA2B,EAAE,SAASnI,IAAI,CAACS,KAAK,CAACgJ,oBAAoB,CAAC;EACxE,CAAC,CAAC;EACF,OAAO,CAAC9C,YAAY,CAACmD,SAAS,CAAC,EAAE5B,WAAW,CAAC4B,SAAS,CAAC,EAAEjF,gBAAgB,CAACiF,SAAS,CAAC,EAAErH,gBAAgB,CAACqH,SAAS,CAAC,EAAEtJ,YAAY,CAACsJ,SAAS,CAAC,EAAExB,YAAY,CAACwB,SAAS,CAAC,EAAEvJ,cAAc,CAACuJ,SAAS,CAAC,CAAC;AACjM,CAAC,EAAEb,qBAAqB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}