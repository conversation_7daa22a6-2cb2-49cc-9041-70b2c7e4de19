{"ast": null, "code": "export default function (point) {\n  checkCoord(point.x);\n  checkCoord(point.y);\n}\nfunction checkCoord(num) {\n  if (typeof Number.isFinite === 'function') {\n    if (Number.isFinite(num)) {\n      return;\n    }\n    throw new TypeError('coordinates must be finite numbers');\n  }\n  if (typeof num !== 'number' || num !== num || !isFinite(num)) {\n    throw new TypeError('coordinates must be finite numbers');\n  }\n}", "map": {"version": 3, "names": ["point", "checkCoord", "x", "y", "num", "Number", "isFinite", "TypeError"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/proj4/lib/checkSanity.js"], "sourcesContent": ["export default function (point) {\n  checkCoord(point.x);\n  checkCoord(point.y);\n}\nfunction checkCoord(num) {\n  if (typeof Number.isFinite === 'function') {\n    if (Number.isFinite(num)) {\n      return;\n    }\n    throw new TypeError('coordinates must be finite numbers');\n  }\n  if (typeof num !== 'number' || num !== num || !isFinite(num)) {\n    throw new TypeError('coordinates must be finite numbers');\n  }\n}\n"], "mappings": "AAAA,eAAe,UAAUA,KAAK,EAAE;EAC9BC,UAAU,CAACD,KAAK,CAACE,CAAC,CAAC;EACnBD,UAAU,CAACD,KAAK,CAACG,CAAC,CAAC;AACrB;AACA,SAASF,UAAUA,CAACG,GAAG,EAAE;EACvB,IAAI,OAAOC,MAAM,CAACC,QAAQ,KAAK,UAAU,EAAE;IACzC,IAAID,MAAM,CAACC,QAAQ,CAACF,GAAG,CAAC,EAAE;MACxB;IACF;IACA,MAAM,IAAIG,SAAS,CAAC,oCAAoC,CAAC;EAC3D;EACA,IAAI,OAAOH,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAKA,GAAG,IAAI,CAACE,QAAQ,CAACF,GAAG,CAAC,EAAE;IAC5D,MAAM,IAAIG,SAAS,CAAC,oCAAoC,CAAC;EAC3D;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}