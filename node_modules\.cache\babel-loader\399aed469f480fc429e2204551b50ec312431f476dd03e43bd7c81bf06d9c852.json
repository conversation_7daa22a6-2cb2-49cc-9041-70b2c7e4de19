{"ast": null, "code": "/* eslint-disable react-hooks/rules-of-hooks */\nimport * as React from 'react';\nimport { isBrowserClient } from \"../utils/commonUtil\";\n\n/**\n * Wrap `React.useLayoutEffect` which will not throw warning message in test env\n */\nexport default function useLayoutEffect(effect, deps) {\n  // Never happen in test env\n  if (isBrowserClient) {\n    /* istanbul ignore next */\n    React.useLayoutEffect(effect, deps);\n  } else {\n    React.useEffect(effect, deps);\n  }\n}\n/* eslint-enable */", "map": {"version": 3, "names": ["React", "isBrowserClient", "useLayoutEffect", "effect", "deps", "useEffect"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-select/es/hooks/useLayoutEffect.js"], "sourcesContent": ["/* eslint-disable react-hooks/rules-of-hooks */\nimport * as React from 'react';\nimport { isBrowserClient } from \"../utils/commonUtil\";\n\n/**\n * Wrap `React.useLayoutEffect` which will not throw warning message in test env\n */\nexport default function useLayoutEffect(effect, deps) {\n  // Never happen in test env\n  if (isBrowserClient) {\n    /* istanbul ignore next */\n    React.useLayoutEffect(effect, deps);\n  } else {\n    React.useEffect(effect, deps);\n  }\n}\n/* eslint-enable */"], "mappings": "AAAA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,qBAAqB;;AAErD;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACpD;EACA,IAAIH,eAAe,EAAE;IACnB;IACAD,KAAK,CAACE,eAAe,CAACC,MAAM,EAAEC,IAAI,CAAC;EACrC,CAAC,MAAM;IACLJ,KAAK,CAACK,SAAS,CAACF,MAAM,EAAEC,IAAI,CAAC;EAC/B;AACF;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}