{"ast": null, "code": "export default function (array) {\n  var out = {\n    x: array[0],\n    y: array[1]\n  };\n  if (array.length > 2) {\n    out.z = array[2];\n  }\n  if (array.length > 3) {\n    out.m = array[3];\n  }\n  return out;\n}", "map": {"version": 3, "names": ["array", "out", "x", "y", "length", "z", "m"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/proj4/lib/common/toPoint.js"], "sourcesContent": ["export default function (array){\n  var out = {\n    x: array[0],\n    y: array[1]\n  };\n  if (array.length>2) {\n    out.z = array[2];\n  }\n  if (array.length>3) {\n    out.m = array[3];\n  }\n  return out;\n}"], "mappings": "AAAA,eAAe,UAAUA,KAAK,EAAC;EAC7B,IAAIC,GAAG,GAAG;IACRC,CAAC,EAAEF,KAAK,CAAC,CAAC,CAAC;IACXG,CAAC,EAAEH,KAAK,CAAC,CAAC;EACZ,CAAC;EACD,IAAIA,KAAK,CAACI,MAAM,GAAC,CAAC,EAAE;IAClBH,GAAG,CAACI,CAAC,GAAGL,KAAK,CAAC,CAAC,CAAC;EAClB;EACA,IAAIA,KAAK,CAACI,MAAM,GAAC,CAAC,EAAE;IAClBH,GAAG,CAACK,CAAC,GAAGN,KAAK,CAAC,CAAC,CAAC;EAClB;EACA,OAAOC,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}