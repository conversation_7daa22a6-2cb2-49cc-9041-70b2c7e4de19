{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    const scene = new THREE.Scene();\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改轨迹数据加载函数\n    const loadTrajectoryData = async () => {\n      try {\n        console.log('开始加载轨迹数据');\n        const response = await fetch(`${BASE_URL}/changli2/vehicle_trajectory.txt`);\n        const text = await response.text();\n        const lines = text.trim().split('\\n');\n        const trajectory = lines.map(line => {\n          const [lat, lon, heading, speed] = line.split(',').map(Number);\n          return {\n            latitude: lat,\n            longitude: lon,\n            heading: heading,\n            speed: speed\n          };\n        });\n        console.log(`成功加载${trajectory.length}个轨迹点`);\n        console.log('第一个轨迹点:', trajectory[0]);\n\n        // 设置全局轨迹数据\n        globalTrajectory = trajectory;\n        currentPointIndex = 0;\n        return trajectory;\n      } catch (error) {\n        console.error('加载轨迹数据失败:', error);\n        throw error;\n      }\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 加载轨迹数据\n        const trajectory = await loadTrajectoryData();\n\n        // 3. 设置初始位置\n        if (vehicleContainer && trajectory.length > 0) {\n          var _vehicleContainer$par;\n          const initialState = trajectory[0];\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n\n          // 确保位置设置正确应用\n          vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n\n          // 强制更新矩阵\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n\n          // 打印初始化后的位置信息\n          console.log('初始化位置:', {\n            设置的位置: initialPos,\n            当前位置: vehicleContainer.position.toArray(),\n            世界位置: vehicleContainer.getWorldPosition(new THREE.Vector3()).toArray(),\n            父对象: (_vehicleContainer$par = vehicleContainer.parent) === null || _vehicleContainer$par === void 0 ? void 0 : _vehicleContainer$par.type,\n            矩阵: vehicleContainer.matrix.elements\n          });\n\n          // 设置初始当前位置\n          currentPosition = vehicleContainer.position.clone();\n\n          // 创建新的更新间隔\n          console.log('开始轨迹更新');\n          globalUpdateInterval = setInterval(() => {\n            if (!vehicleContainer || trajectory.length === 0) return;\n            currentPointIndex = (currentPointIndex + 1) % trajectory.length;\n            const newState = trajectory[currentPointIndex];\n            if (newState) {\n              const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n              targetPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n\n              // 直接设置位置\n              vehicleContainer.position.copy(targetPosition);\n\n              // 计算并设置正确的旋转角度\n              // 确保每次更新都会改变旋转角度\n              vehicleContainer.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n              // vehicleContainer.rotation.y = newState.heading * Math.PI / 180;\n\n              // 强制更新矩阵\n              vehicleContainer.updateMatrix();\n              vehicleContainer.updateMatrixWorld(true);\n\n              // 打印更详细的信息\n              console.log('位置和旋转更新:', {\n                目标位置: targetPosition.toArray(),\n                当前位置: vehicleContainer.position.toArray(),\n                世界位置: vehicleContainer.getWorldPosition(new THREE.Vector3()).toArray(),\n                旋转角度: vehicleContainer.rotation.y,\n                航向角: newState.heading\n              });\n\n              // 更新状态\n              setVehicleState({\n                longitude: newState.longitude,\n                latitude: newState.latitude,\n                speed: newState.speed,\n                heading: newState.heading\n              });\n            }\n          }, 200);\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n        scene.add(model);\n\n        // 在校园模型加载完成后初始化场景\n        await initializeScene();\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 15, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      controls.update();\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 清理函数\n    return () => {\n      var _containerRef$current;\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      window.removeEventListener('resize', handleResize);\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.removeChild(renderer.domElement);\n      renderer.dispose();\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 568,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"CZLMFEfLxjnT1cTLVwgusHKd+bg=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "BASE_URL", "CampusModel", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "clone", "currentUp", "up", "Tween", "to", "x", "y", "z", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "Math", "PI", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "scene", "Scene", "camera", "PerspectiveCamera", "window", "innerWidth", "innerHeight", "set", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "add", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "MeshStandardMaterial", "color", "metalness", "roughness", "envMapIntensity", "map", "name", "children", "length", "xhr", "loaded", "total", "toFixed", "loadTrajectoryData", "response", "fetch", "text", "lines", "trim", "split", "trajectory", "line", "lat", "lon", "Number", "error", "initializeScene", "_vehicleContainer$par", "initialState", "initialPos", "wgs84ToModel", "rotation", "updateMatrix", "updateMatrixWorld", "设置的位置", "当前位置", "toArray", "世界位置", "getWorldPosition", "Vector3", "父对象", "parent", "type", "矩阵", "matrix", "elements", "setInterval", "newState", "modelPos", "目标位置", "旋转角度", "航向角", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "setTimeout", "model", "scale", "错误类型", "错误消息", "message", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "相机位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "_containerRef$current", "clearInterval", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "ref", "style", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "createTextSprite", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    const scene = new THREE.Scene();\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改轨迹数据加载函数\n    const loadTrajectoryData = async () => {\n      try {\n        console.log('开始加载轨迹数据');\n        const response = await fetch(`${BASE_URL}/changli2/vehicle_trajectory.txt`);\n        const text = await response.text();\n        \n        const lines = text.trim().split('\\n');\n        const trajectory = lines.map(line => {\n          const [lat, lon, heading, speed] = line.split(',').map(Number);\n          return {\n            latitude: lat,\n            longitude: lon,\n            heading: heading,\n            speed: speed\n          };\n        });\n        \n        console.log(`成功加载${trajectory.length}个轨迹点`);\n        console.log('第一个轨迹点:', trajectory[0]);\n        \n        // 设置全局轨迹数据\n        globalTrajectory = trajectory;\n        currentPointIndex = 0;\n        \n        return trajectory;\n      } catch (error) {\n        console.error('加载轨迹数据失败:', error);\n        throw error;\n      }\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 加载轨迹数据\n        const trajectory = await loadTrajectoryData();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer && trajectory.length > 0) {\n          const initialState = trajectory[0];\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          \n          // 确保位置设置正确应用\n          vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          \n          // 强制更新矩阵\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          \n          // 打印初始化后的位置信息\n          console.log('初始化位置:', {\n            设置的位置: initialPos,\n            当前位置: vehicleContainer.position.toArray(),\n            世界位置: vehicleContainer.getWorldPosition(new THREE.Vector3()).toArray(),\n            父对象: vehicleContainer.parent?.type,\n            矩阵: vehicleContainer.matrix.elements\n          });\n          \n          // 设置初始当前位置\n          currentPosition = vehicleContainer.position.clone();\n          \n          // 创建新的更新间隔\n          console.log('开始轨迹更新');\n          globalUpdateInterval = setInterval(() => {\n            if (!vehicleContainer || trajectory.length === 0) return;\n            \n            currentPointIndex = (currentPointIndex + 1) % trajectory.length;\n            const newState = trajectory[currentPointIndex];\n            \n            if (newState) {\n              const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n              targetPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n              \n              // 直接设置位置\n              vehicleContainer.position.copy(targetPosition);\n              \n              // 计算并设置正确的旋转角度\n              // 确保每次更新都会改变旋转角度\n              vehicleContainer.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n              // vehicleContainer.rotation.y = newState.heading * Math.PI / 180;\n              \n              // 强制更新矩阵\n              vehicleContainer.updateMatrix();\n              vehicleContainer.updateMatrixWorld(true);\n              \n              // 打印更详细的信息\n              console.log('位置和旋转更新:', {\n                目标位置: targetPosition.toArray(),\n                当前位置: vehicleContainer.position.toArray(),\n                世界位置: vehicleContainer.getWorldPosition(new THREE.Vector3()).toArray(),\n                旋转角度: vehicleContainer.rotation.y,\n                航向角: newState.heading\n              });\n              \n              // 更新状态\n              setVehicleState({\n                longitude: newState.longitude,\n                latitude: newState.latitude,\n                speed: newState.speed,\n                heading: newState.heading\n              });\n            }\n          }, 200);\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          15,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      \n      controls.update();\n      renderer.render(scene, camera);\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 清理函数\n    return () => {\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      window.removeEventListener('resize', handleResize);\n      containerRef.current?.removeChild(renderer.domElement);\n      renderer.dispose();\n    };\n  }, []);\n\n  return (\n    <>\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;;AAE1C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,MAAMC,QAAQ,GAAG,uBAAuB;AAExC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAGvB,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMwB,UAAU,GAAGxB,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMyB,SAAS,GAAGzB,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMqB,aAAa,GAAG1B,MAAM,CAAC,EAAE,CAAC;EAChC,MAAM2B,eAAe,GAAG3B,MAAM,CAAC,CAAC,CAAC;EACjC,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC;IAC/C+B,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMqC,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGvD,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMwD,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnB,WAAW,CAAC,QAAQ,CAAC;IACrBnB,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACsC,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrB,WAAW,CAAC,QAAQ,CAAC;IACrBnB,UAAU,GAAG,QAAQ;IAErB,IAAIqC,SAAS,CAACI,OAAO,IAAIxC,QAAQ,EAAE;MACjC;MACA,MAAMyC,UAAU,GAAGL,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACsB,KAAK,CAAC,CAAC;MACrD,MAAMC,SAAS,GAAGP,SAAS,CAACI,OAAO,CAACI,EAAE,CAACF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAIvD,KAAK,CAAC0D,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCC,MAAM,CAAC/D,KAAK,CAACgE,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdlB,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACmC,IAAI,CAACd,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDe,KAAK,CAAC,CAAC;;MAEV;MACA,IAAIrE,KAAK,CAAC0D,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC/D,KAAK,CAACgE,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdlB,SAAS,CAACI,OAAO,CAACI,EAAE,CAACW,IAAI,CAACZ,SAAS,CAAC;MACtC,CAAC,CAAC,CACDa,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAGzD,QAAQ,CAAC0D,MAAM,CAAChB,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAIvD,KAAK,CAAC0D,KAAK,CAACY,aAAa,CAAC,CAC3BX,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC/D,KAAK,CAACgE,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdtD,QAAQ,CAAC0D,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACArB,SAAS,CAACI,OAAO,CAACmB,MAAM,CAAC3D,QAAQ,CAAC0D,MAAM,CAAC;QACzC1D,QAAQ,CAAC4D,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACAxD,QAAQ,CAACsC,OAAO,GAAG,IAAI;;MAEvB;MACAtC,QAAQ,CAAC6D,WAAW,GAAG,EAAE;MACzB7D,QAAQ,CAAC8D,WAAW,GAAG,GAAG;MAC1B9D,QAAQ,CAAC+D,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;MACtCjE,QAAQ,CAACkE,aAAa,GAAG,CAAC;MAC1BlE,QAAQ,CAAC4D,MAAM,CAAC,CAAC;MAEjBO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAED3F,SAAS,CAAC,MAAM;IACd,IAAI,CAACwB,YAAY,CAACoC,OAAO,EAAE;;IAE3B;IACA,MAAMgC,KAAK,GAAG,IAAIzF,KAAK,CAAC0F,KAAK,CAAC,CAAC;;IAE/B;IACA,MAAMC,MAAM,GAAG,IAAI3F,KAAK,CAAC4F,iBAAiB,CACxC,EAAE,EACFC,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACDJ,MAAM,CAACtD,QAAQ,CAAC2D,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCL,MAAM,CAACf,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBvB,SAAS,CAACI,OAAO,GAAGkC,MAAM;;IAE1B;IACA,MAAMM,QAAQ,GAAG,IAAIjG,KAAK,CAACkG,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAACP,MAAM,CAACC,UAAU,EAAED,MAAM,CAACE,WAAW,CAAC;IACvDE,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAACT,MAAM,CAACU,gBAAgB,CAAC;IAC/ClF,YAAY,CAACoC,OAAO,CAAC+C,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAI1G,KAAK,CAAC2G,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5DlB,KAAK,CAACmB,GAAG,CAACF,YAAY,CAAC;;IAEvB;IACA,MAAMG,iBAAiB,GAAG,IAAI7G,KAAK,CAAC8G,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAACxE,QAAQ,CAAC2D,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1CP,KAAK,CAACmB,GAAG,CAACC,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAI/G,KAAK,CAAC8G,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAAC1E,QAAQ,CAAC2D,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3CP,KAAK,CAACmB,GAAG,CAACG,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAIhH,KAAK,CAACiH,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAAC3E,QAAQ,CAAC2D,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCgB,SAAS,CAACE,KAAK,GAAGjC,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7B8B,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxB5B,KAAK,CAACmB,GAAG,CAACI,SAAS,CAAC;;IAEpB;IACA/F,QAAQ,GAAG,IAAIf,aAAa,CAACyF,MAAM,EAAEM,QAAQ,CAACQ,UAAU,CAAC;IACzDxF,QAAQ,CAACqG,aAAa,GAAG,IAAI;IAC7BrG,QAAQ,CAACsG,aAAa,GAAG,IAAI;IAC7BtG,QAAQ,CAACuG,kBAAkB,GAAG,KAAK;IACnCvG,QAAQ,CAAC6D,WAAW,GAAG,EAAE;IACzB7D,QAAQ,CAAC8D,WAAW,GAAG,GAAG;IAC1B9D,QAAQ,CAAC+D,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IACtCjE,QAAQ,CAACkE,aAAa,GAAG,CAAC;IAC1BlE,QAAQ,CAAC0D,MAAM,CAACqB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5B/E,QAAQ,CAAC4D,MAAM,CAAC,CAAC;;IAEjB;IACAO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBM,MAAM,EAAE,CAAC,CAACA,MAAM;MAChB1E,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBoC,SAAS,EAAE,CAAC,CAACA,SAAS,CAACI;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMgE,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAI5H,UAAU,CAAC,CAAC;QACtC4H,aAAa,CAACC,IAAI,CAChB,GAAG5G,QAAQ,uBAAuB,EACjC6G,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAACtC,KAAK;;UAE/B;UACA,MAAMwC,gBAAgB,GAAG,IAAIjI,KAAK,CAACkI,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACG,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAIvI,KAAK,CAACwI,oBAAoB,CAAC;kBACjDC,KAAK,EAAE,QAAQ;kBAAO;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIR,KAAK,CAACE,QAAQ,CAACO,GAAG,EAAE;kBACtBN,WAAW,CAACM,GAAG,GAAGT,KAAK,CAACE,QAAQ,CAACO,GAAG;gBACtC;;gBAEA;gBACAT,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5BnD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE+C,KAAK,CAACU,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMd,YAAY,CAACe,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMZ,KAAK,GAAGJ,YAAY,CAACe,QAAQ,CAAC,CAAC,CAAC;YACtCd,gBAAgB,CAACrB,GAAG,CAACwB,KAAK,CAAC;UAC7B;;UAEA;UACA3C,KAAK,CAACmB,GAAG,CAACqB,gBAAgB,CAAC;;UAE3B;UACAxH,gBAAgB,GAAGwH,gBAAgB;UAEnC7C,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9B1D,kBAAkB,CAAC,IAAI,CAAC;UACxBgG,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAgB,GAAG,IAAK;UACP7D,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC4D,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDxB,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMyB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACFjE,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;QACvB,MAAMiE,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGrI,QAAQ,kCAAkC,CAAC;QAC3E,MAAMsI,IAAI,GAAG,MAAMF,QAAQ,CAACE,IAAI,CAAC,CAAC;QAElC,MAAMC,KAAK,GAAGD,IAAI,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC;QACrC,MAAMC,UAAU,GAAGH,KAAK,CAACZ,GAAG,CAACgB,IAAI,IAAI;UACnC,MAAM,CAACC,GAAG,EAAEC,GAAG,EAAE9H,OAAO,EAAED,KAAK,CAAC,GAAG6H,IAAI,CAACF,KAAK,CAAC,GAAG,CAAC,CAACd,GAAG,CAACmB,MAAM,CAAC;UAC9D,OAAO;YACLjI,QAAQ,EAAE+H,GAAG;YACbhI,SAAS,EAAEiI,GAAG;YACd9H,OAAO,EAAEA,OAAO;YAChBD,KAAK,EAAEA;UACT,CAAC;QACH,CAAC,CAAC;QAEFoD,OAAO,CAACC,GAAG,CAAC,OAAOuE,UAAU,CAACZ,MAAM,MAAM,CAAC;QAC3C5D,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEuE,UAAU,CAAC,CAAC,CAAC,CAAC;;QAErC;QACAlJ,gBAAgB,GAAGkJ,UAAU;QAC7BjJ,iBAAiB,GAAG,CAAC;QAErB,OAAOiJ,UAAU;MACnB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACd7E,OAAO,CAAC6E,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,MAAMA,KAAK;MACb;IACF,CAAC;;IAED;IACA,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMjC,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACA,MAAMmC,UAAU,GAAG,MAAMP,kBAAkB,CAAC,CAAC;;QAE7C;QACA,IAAIpB,gBAAgB,IAAI2B,UAAU,CAACZ,MAAM,GAAG,CAAC,EAAE;UAAA,IAAAmB,qBAAA;UAC7C,MAAMC,YAAY,GAAGR,UAAU,CAAC,CAAC,CAAC;UAClC,MAAMS,UAAU,GAAG9I,SAAS,CAACkC,OAAO,CAAC6G,YAAY,CAACF,YAAY,CAACtI,SAAS,EAAEsI,YAAY,CAACrI,QAAQ,CAAC;;UAEhG;UACAkG,gBAAgB,CAAC5F,QAAQ,CAAC2D,GAAG,CAACqE,UAAU,CAACrG,CAAC,EAAE,GAAG,EAAE,CAACqG,UAAU,CAACpG,CAAC,CAAC;UAC/DgE,gBAAgB,CAACsC,QAAQ,CAACtG,CAAC,GAAIgB,IAAI,CAACC,EAAE,GAAGkF,YAAY,CAACnI,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAI;;UAE9E;UACA+C,gBAAgB,CAACuC,YAAY,CAAC,CAAC;UAC/BvC,gBAAgB,CAACwC,iBAAiB,CAAC,IAAI,CAAC;;UAExC;UACArF,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE;YACpBqF,KAAK,EAAEL,UAAU;YACjBM,IAAI,EAAE1C,gBAAgB,CAAC5F,QAAQ,CAACuI,OAAO,CAAC,CAAC;YACzCC,IAAI,EAAE5C,gBAAgB,CAAC6C,gBAAgB,CAAC,IAAI9K,KAAK,CAAC+K,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO,CAAC,CAAC;YACtEI,GAAG,GAAAb,qBAAA,GAAElC,gBAAgB,CAACgD,MAAM,cAAAd,qBAAA,uBAAvBA,qBAAA,CAAyBe,IAAI;YAClCC,EAAE,EAAElD,gBAAgB,CAACmD,MAAM,CAACC;UAC9B,CAAC,CAAC;;UAEF;UACAvK,eAAe,GAAGmH,gBAAgB,CAAC5F,QAAQ,CAACsB,KAAK,CAAC,CAAC;;UAEnD;UACAyB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;UACrBzE,oBAAoB,GAAG0K,WAAW,CAAC,MAAM;YACvC,IAAI,CAACrD,gBAAgB,IAAI2B,UAAU,CAACZ,MAAM,KAAK,CAAC,EAAE;YAElDrI,iBAAiB,GAAG,CAACA,iBAAiB,GAAG,CAAC,IAAIiJ,UAAU,CAACZ,MAAM;YAC/D,MAAMuC,QAAQ,GAAG3B,UAAU,CAACjJ,iBAAiB,CAAC;YAE9C,IAAI4K,QAAQ,EAAE;cACZ,MAAMC,QAAQ,GAAGjK,SAAS,CAACkC,OAAO,CAAC6G,YAAY,CAACiB,QAAQ,CAACzJ,SAAS,EAAEyJ,QAAQ,CAACxJ,QAAQ,CAAC;cACtFlB,cAAc,GAAG,IAAIb,KAAK,CAAC+K,OAAO,CAACS,QAAQ,CAACxH,CAAC,EAAE,GAAG,EAAE,CAACwH,QAAQ,CAACvH,CAAC,CAAC;;cAEhE;cACAgE,gBAAgB,CAAC5F,QAAQ,CAACmC,IAAI,CAAC3D,cAAc,CAAC;;cAE9C;cACA;cACAoH,gBAAgB,CAACsC,QAAQ,CAACtG,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGqG,QAAQ,CAACtJ,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAG;cACxE;;cAEA;cACA+C,gBAAgB,CAACuC,YAAY,CAAC,CAAC;cAC/BvC,gBAAgB,CAACwC,iBAAiB,CAAC,IAAI,CAAC;;cAExC;cACArF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;gBACtBoG,IAAI,EAAE5K,cAAc,CAAC+J,OAAO,CAAC,CAAC;gBAC9BD,IAAI,EAAE1C,gBAAgB,CAAC5F,QAAQ,CAACuI,OAAO,CAAC,CAAC;gBACzCC,IAAI,EAAE5C,gBAAgB,CAAC6C,gBAAgB,CAAC,IAAI9K,KAAK,CAAC+K,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO,CAAC,CAAC;gBACtEc,IAAI,EAAEzD,gBAAgB,CAACsC,QAAQ,CAACtG,CAAC;gBACjC0H,GAAG,EAAEJ,QAAQ,CAACtJ;cAChB,CAAC,CAAC;;cAEF;cACAJ,eAAe,CAAC;gBACdC,SAAS,EAAEyJ,QAAQ,CAACzJ,SAAS;gBAC7BC,QAAQ,EAAEwJ,QAAQ,CAACxJ,QAAQ;gBAC3BC,KAAK,EAAEuJ,QAAQ,CAACvJ,KAAK;gBACrBC,OAAO,EAAEsJ,QAAQ,CAACtJ;cACpB,CAAC,CAAC;YACJ;UACF,CAAC,EAAE,GAAG,CAAC;QACT;MACF,CAAC,CAAC,OAAOgI,KAAK,EAAE;QACd7E,OAAO,CAAC6E,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAM2B,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAIpE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMmE,WAAW,GAAIC,WAAW,IAAK;UACnC5G,OAAO,CAACC,GAAG,CAAC,WAAWwG,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAIhM,UAAU,CAAC,CAAC;UAC/BgM,MAAM,CAACnE,IAAI,CACT+D,GAAG,EACF9D,IAAI,IAAK;YACR3C,OAAO,CAACC,GAAG,CAAC,WAAWwG,GAAG,EAAE,CAAC;YAC7BlE,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAkB,GAAG,IAAK;YACP7D,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC4D,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAa,KAAK,IAAK;YACT7E,OAAO,CAAC6E,KAAK,CAAC,SAAS4B,GAAG,EAAE,EAAE5B,KAAK,CAAC;YACpC,IAAI+B,WAAW,GAAG,CAAC,EAAE;cACnB5G,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3B6G,UAAU,CAAC,MAAMH,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACLpE,MAAM,CAACqC,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAED8B,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAIhM,UAAU,CAAC,CAAC;IAC/BgM,MAAM,CAACnE,IAAI,CACT,GAAG5G,QAAQ,4BAA4B,EACvC,MAAO6G,IAAI,IAAK;MACd,IAAI;QACF,MAAMoE,KAAK,GAAGpE,IAAI,CAACtC,KAAK;QACxB0G,KAAK,CAACC,KAAK,CAACpG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBmG,KAAK,CAAC9J,QAAQ,CAAC2D,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3BP,KAAK,CAACmB,GAAG,CAACuF,KAAK,CAAC;;QAEhB;QACA,MAAMjC,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC,OAAOD,KAAK,EAAE;QACd7E,OAAO,CAAC6E,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACAhB,GAAG,IAAK;MACP7D,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC4D,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAa,KAAK,IAAK;MACT7E,OAAO,CAAC6E,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7E,OAAO,CAAC6E,KAAK,CAAC,OAAO,EAAE;QACrBoC,IAAI,EAAEpC,KAAK,CAACiB,IAAI;QAChBoB,IAAI,EAAErC,KAAK,CAACsC,OAAO;QACnBC,KAAK,EAAE,GAAGtL,QAAQ,4BAA4B;QAC9CuL,KAAK,EAAE,GAAGvL,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMwL,OAAO,GAAGA,CAAA,KAAM;MACpBC,qBAAqB,CAACD,OAAO,CAAC;;MAE9B;MACAtM,KAAK,CAACyE,MAAM,CAAC,CAAC;MAEd,IAAI7D,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACsC,OAAO,GAAG,KAAK;;QAExB;QACA,MAAMqJ,UAAU,GAAGnM,gBAAgB,CAAC4B,QAAQ,CAACsB,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMkJ,eAAe,GAAGpM,gBAAgB,CAAC8J,QAAQ,CAACtG,CAAC;;QAEnD;QACA;QACA,MAAM6I,gBAAgB,GAAG,EAAED,eAAe,GAAG5H,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAM6H,YAAY,GAAG,IAAI/M,KAAK,CAAC+K,OAAO,CACpC,CAAC,EAAE,GAAG9F,IAAI,CAAC+H,GAAG,CAACF,gBAAgB,CAAC,EAChC,EAAE,EACF,CAAC,EAAE,GAAG7H,IAAI,CAACgI,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACAnH,MAAM,CAACtD,QAAQ,CAACmC,IAAI,CAACoI,UAAU,CAAC,CAAChG,GAAG,CAACmG,YAAY,CAAC;;QAElD;QACApH,MAAM,CAAC9B,EAAE,CAACmC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAMkH,YAAY,GAAGN,UAAU,CAACjJ,KAAK,CAAC,CAAC;QACvCgC,MAAM,CAACf,MAAM,CAACsI,YAAY,CAAC;;QAE3B;QACAvH,MAAM,CAACwH,sBAAsB,CAAC,CAAC;QAC/BxH,MAAM,CAAC6E,YAAY,CAAC,CAAC;QACrB7E,MAAM,CAAC8E,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACAxJ,QAAQ,CAACsC,OAAO,GAAG,KAAK;;QAExB;QACAtC,QAAQ,CAAC0D,MAAM,CAACH,IAAI,CAACoI,UAAU,CAAC;QAChC3L,QAAQ,CAAC4D,MAAM,CAAC,CAAC;QAEjBO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnB+H,IAAI,EAAER,UAAU,CAAChC,OAAO,CAAC,CAAC;UAC1ByC,IAAI,EAAE1H,MAAM,CAACtD,QAAQ,CAACuI,OAAO,CAAC,CAAC;UAC/B0C,IAAI,EAAEJ,YAAY,CAACtC,OAAO,CAAC,CAAC;UAC5B2C,IAAI,EAAE5H,MAAM,CAAC6H,iBAAiB,CAAC,IAAIxN,KAAK,CAAC+K,OAAO,CAAC,CAAC,CAAC,CAACH,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI5J,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAACsC,OAAO,GAAG,IAAI;;QAEvB;QACAoC,MAAM,CAAC9B,EAAE,CAACmC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAIf,IAAI,CAACwI,GAAG,CAAC9H,MAAM,CAACtD,QAAQ,CAAC4B,CAAC,CAAC,GAAG,EAAE,EAAE;UACpC0B,MAAM,CAACtD,QAAQ,CAAC2D,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9B/E,QAAQ,CAAC0D,MAAM,CAACqB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BL,MAAM,CAACf,MAAM,CAAC3D,QAAQ,CAAC0D,MAAM,CAAC;UAC9B1D,QAAQ,CAAC4D,MAAM,CAAC,CAAC;QACnB;MACF;MAEA5D,QAAQ,CAAC4D,MAAM,CAAC,CAAC;MACjBoB,QAAQ,CAACyH,MAAM,CAACjI,KAAK,EAAEE,MAAM,CAAC;IAChC,CAAC;IAED+G,OAAO,CAAC,CAAC;;IAET;IACA,MAAMiB,YAAY,GAAGA,CAAA,KAAM;MACzBhI,MAAM,CAACiI,MAAM,GAAG/H,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,WAAW;MACtDJ,MAAM,CAACwH,sBAAsB,CAAC,CAAC;MAC/BlH,QAAQ,CAACG,OAAO,CAACP,MAAM,CAACC,UAAU,EAAED,MAAM,CAACE,WAAW,CAAC;IACzD,CAAC;IACDF,MAAM,CAACgI,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACA9H,MAAM,CAACiI,aAAa,GAAG,MAAM;MAC3B,IAAIzK,SAAS,CAACI,OAAO,EAAE;QACrBJ,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAAC2D,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC3C,SAAS,CAACI,OAAO,CAACmB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCvB,SAAS,CAACI,OAAO,CAAC+G,YAAY,CAAC,CAAC;QAChCnH,SAAS,CAACI,OAAO,CAACgH,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAIxJ,QAAQ,EAAE;UACZA,QAAQ,CAAC0D,MAAM,CAACqB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B/E,QAAQ,CAACsC,OAAO,GAAG,IAAI;UACvBtC,QAAQ,CAAC4D,MAAM,CAAC,CAAC;QACnB;QAEA7D,UAAU,GAAG,QAAQ;QACrBoE,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MAAA,IAAA0I,qBAAA;MACX,IAAInN,oBAAoB,EAAE;QACxBoN,aAAa,CAACpN,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;MACAiF,MAAM,CAACoI,mBAAmB,CAAC,QAAQ,EAAEN,YAAY,CAAC;MAClD,CAAAI,qBAAA,GAAA1M,YAAY,CAACoC,OAAO,cAAAsK,qBAAA,uBAApBA,qBAAA,CAAsBG,WAAW,CAACjI,QAAQ,CAACQ,UAAU,CAAC;MACtDR,QAAQ,CAACkI,OAAO,CAAC,CAAC;IACpB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE7N,OAAA,CAAAE,SAAA;IAAAuI,QAAA,gBACEzI,OAAA;MAAK8N,GAAG,EAAE/M,YAAa;MAACgN,KAAK,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpErO,OAAA;MAAK+N,KAAK,EAAEjM,oBAAqB;MAAA2G,QAAA,gBAC/BzI,OAAA;QACE+N,KAAK,EAAE;UACL,GAAGzL,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EuG,KAAK,EAAEvG,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF0M,OAAO,EAAEtL,kBAAmB;QAAAyF,QAAA,EAC7B;MAED;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrO,OAAA;QACE+N,KAAK,EAAE;UACL,GAAGzL,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EuG,KAAK,EAAEvG,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACF0M,OAAO,EAAEpL,kBAAmB;QAAAuF,QAAA,EAC7B;MAED;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAAvN,EAAA,CA5jBMD,WAAW;AAAA0N,EAAA,GAAX1N,WAAW;AA6jBjB,SAAS2N,gBAAgBA,CAACtF,IAAI,EAAE;EAC9B,MAAMuF,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAACT,KAAK,GAAG,GAAG;EAClBS,MAAM,CAACR,MAAM,GAAG,EAAE;;EAElB;EACAW,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAAC/F,IAAI,EAAEuF,MAAM,CAACT,KAAK,GAAC,CAAC,EAAES,MAAM,CAACR,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAMiB,OAAO,GAAG,IAAIxP,KAAK,CAACyP,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAI1P,KAAK,CAAC2P,cAAc,CAAC;IAC9C9G,GAAG,EAAE2G,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAI7P,KAAK,CAAC8P,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAACzD,KAAK,CAACpG,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAO6J,MAAM;AACf;;AAEA;AACAhK,MAAM,CAACkK,WAAW,GAAG,CAAC/L,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EAChC,IAAIzD,gBAAgB,EAAE;IACpBA,gBAAgB,CAAC4B,QAAQ,CAAC2D,GAAG,CAAChC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACtCzD,gBAAgB,CAAC+J,YAAY,CAAC,CAAC;IAC/B/J,gBAAgB,CAACgK,iBAAiB,CAAC,IAAI,CAAC;IACxCrF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAACrB,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA2B,MAAM,CAACmK,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMrK,MAAM,GAAGqJ,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAIxK,MAAM,EAAE;MACV;MACA,MAAMyK,MAAM,GAAGzK,MAAM,CAACtD,QAAQ,CAACsB,KAAK,CAAC,CAAC;;MAEtC;MACAgC,MAAM,CAACtD,QAAQ,CAAC2D,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BL,MAAM,CAAC9B,EAAE,CAACmC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBL,MAAM,CAACf,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAe,MAAM,CAAC6E,YAAY,CAAC,CAAC;MACrB7E,MAAM,CAAC8E,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMxJ,QAAQ,GAAG+N,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIpP,QAAQ,EAAE;QACZA,QAAQ,CAAC0D,MAAM,CAACqB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B/E,QAAQ,CAAC4D,MAAM,CAAC,CAAC;MACnB;MAEAO,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBiL,GAAG,EAAEF,MAAM,CAACxF,OAAO,CAAC,CAAC;QACrB2F,GAAG,EAAE5K,MAAM,CAACtD,QAAQ,CAACuI,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAO4F,CAAC,EAAE;IACVpL,OAAO,CAAC6E,KAAK,CAAC,YAAY,EAAEuG,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;AAED,eAAerP,WAAW;AAAC,IAAA0N,EAAA;AAAA4B,YAAA,CAAA5B,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}