{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport MoneyCollectOutlinedSvg from \"@ant-design/icons-svg/es/asn/MoneyCollectOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar MoneyCollectOutlined = function MoneyCollectOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: MoneyCollectOutlinedSvg\n  }));\n};\n\n/**![money-collect](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxMS41IDcwMC43YTggOCAwIDAwLTEwLjMtNC44TDg0MCA3MTguMlYxODBjMC0zNy42LTMwLjQtNjgtNjgtNjhIMjUyYy0zNy42IDAtNjggMzAuNC02OCA2OHY1MzguMmwtNjEuMy0yMi4zYy0uOS0uMy0xLjgtLjUtMi43LS41LTQuNCAwLTggMy42LTggOFY3NjNjMCAzLjMgMi4xIDYuMyA1LjMgNy41TDUwMSA5MTAuMWM3LjEgMi42IDE0LjggMi42IDIxLjkgMGwzODMuOC0xMzkuNWMzLjItMS4yIDUuMy00LjIgNS4zLTcuNXYtNTkuNmMwLTEtLjItMS45LS41LTIuOHpNNTEyIDgzNy41bC0yNTYtOTMuMVYxODRoNTEydjU2MC40bC0yNTYgOTMuMXpNNjYwLjYgMzEyaC01NC41Yy0zIDAtNS44IDEuNy03LjEgNC40bC04NC43IDE2OC44SDUxMWwtODQuNy0xNjguOGE4IDggMCAwMC03LjEtNC40aC01NS43Yy0xLjMgMC0yLjYuMy0zLjggMS0zLjkgMi4xLTUuMyA3LTMuMiAxMC44bDEwMy45IDE5MS42aC01N2MtNC40IDAtOCAzLjYtOCA4djI3LjFjMCA0LjQgMy42IDggOCA4aDc2djM5aC03NmMtNC40IDAtOCAzLjYtOCA4djI3LjFjMCA0LjQgMy42IDggOCA4aDc2VjcwNGMwIDQuNCAzLjYgOCA4IDhoNDkuOWM0LjQgMCA4LTMuNiA4LTh2LTYzLjVoNzYuM2M0LjQgMCA4LTMuNiA4LTh2LTI3LjFjMC00LjQtMy42LTgtOC04aC03Ni4zdi0zOWg3Ni4zYzQuNCAwIDgtMy42IDgtOHYtMjcuMWMwLTQuNC0zLjYtOC04LThINTY0bDEwMy43LTE5MS42Yy42LTEuMiAxLTIuNSAxLTMuOC0uMS00LjMtMy43LTcuOS04LjEtNy45eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(MoneyCollectOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'MoneyCollectOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "MoneyCollectOutlinedSvg", "AntdIcon", "MoneyCollectOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@ant-design/icons/es/icons/MoneyCollectOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport MoneyCollectOutlinedSvg from \"@ant-design/icons-svg/es/asn/MoneyCollectOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar MoneyCollectOutlined = function MoneyCollectOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: MoneyCollectOutlinedSvg\n  }));\n};\n\n/**![money-collect](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxMS41IDcwMC43YTggOCAwIDAwLTEwLjMtNC44TDg0MCA3MTguMlYxODBjMC0zNy42LTMwLjQtNjgtNjgtNjhIMjUyYy0zNy42IDAtNjggMzAuNC02OCA2OHY1MzguMmwtNjEuMy0yMi4zYy0uOS0uMy0xLjgtLjUtMi43LS41LTQuNCAwLTggMy42LTggOFY3NjNjMCAzLjMgMi4xIDYuMyA1LjMgNy41TDUwMSA5MTAuMWM3LjEgMi42IDE0LjggMi42IDIxLjkgMGwzODMuOC0xMzkuNWMzLjItMS4yIDUuMy00LjIgNS4zLTcuNXYtNTkuNmMwLTEtLjItMS45LS41LTIuOHpNNTEyIDgzNy41bC0yNTYtOTMuMVYxODRoNTEydjU2MC40bC0yNTYgOTMuMXpNNjYwLjYgMzEyaC01NC41Yy0zIDAtNS44IDEuNy03LjEgNC40bC04NC43IDE2OC44SDUxMWwtODQuNy0xNjguOGE4IDggMCAwMC03LjEtNC40aC01NS43Yy0xLjMgMC0yLjYuMy0zLjggMS0zLjkgMi4xLTUuMyA3LTMuMiAxMC44bDEwMy45IDE5MS42aC01N2MtNC40IDAtOCAzLjYtOCA4djI3LjFjMCA0LjQgMy42IDggOCA4aDc2djM5aC03NmMtNC40IDAtOCAzLjYtOCA4djI3LjFjMCA0LjQgMy42IDggOCA4aDc2VjcwNGMwIDQuNCAzLjYgOCA4IDhoNDkuOWM0LjQgMCA4LTMuNiA4LTh2LTYzLjVoNzYuM2M0LjQgMCA4LTMuNiA4LTh2LTI3LjFjMC00LjQtMy42LTgtOC04aC03Ni4zdi0zOWg3Ni4zYzQuNCAwIDgtMy42IDgtOHYtMjcuMWMwLTQuNC0zLjYtOC04LThINTY0bDEwMy43LTE5MS42Yy42LTEuMiAxLTIuNSAxLTMuOC0uMS00LjMtMy43LTcuOS04LjEtNy45eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(MoneyCollectOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'MoneyCollectOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,uBAAuB,MAAM,mDAAmD;AACvF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACnE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,oBAAoB,CAAC;AACjE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,sBAAsB;AAC9C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}