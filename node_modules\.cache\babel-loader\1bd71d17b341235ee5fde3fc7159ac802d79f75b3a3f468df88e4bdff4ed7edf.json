{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Displayable from './Displayable.js';\nimport { getBoundingRect } from '../contain/text.js';\nimport { DEFAULT_PATH_STYLE } from './Path.js';\nimport { createObject, defaults } from '../core/util.js';\nimport { DEFAULT_FONT } from '../core/platform.js';\nexport var DEFAULT_TSPAN_STYLE = defaults({\n  strokeFirst: true,\n  font: DEFAULT_FONT,\n  x: 0,\n  y: 0,\n  textAlign: 'left',\n  textBaseline: 'top',\n  miterLimit: 2\n}, DEFAULT_PATH_STYLE);\nvar TSpan = function (_super) {\n  __extends(TSpan, _super);\n  function TSpan() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  TSpan.prototype.hasStroke = function () {\n    var style = this.style;\n    var stroke = style.stroke;\n    return stroke != null && stroke !== 'none' && style.lineWidth > 0;\n  };\n  TSpan.prototype.hasFill = function () {\n    var style = this.style;\n    var fill = style.fill;\n    return fill != null && fill !== 'none';\n  };\n  TSpan.prototype.createStyle = function (obj) {\n    return createObject(DEFAULT_TSPAN_STYLE, obj);\n  };\n  TSpan.prototype.setBoundingRect = function (rect) {\n    this._rect = rect;\n  };\n  TSpan.prototype.getBoundingRect = function () {\n    var style = this.style;\n    if (!this._rect) {\n      var text = style.text;\n      text != null ? text += '' : text = '';\n      var rect = getBoundingRect(text, style.font, style.textAlign, style.textBaseline);\n      rect.x += style.x || 0;\n      rect.y += style.y || 0;\n      if (this.hasStroke()) {\n        var w = style.lineWidth;\n        rect.x -= w / 2;\n        rect.y -= w / 2;\n        rect.width += w;\n        rect.height += w;\n      }\n      this._rect = rect;\n    }\n    return this._rect;\n  };\n  TSpan.initDefaultProps = function () {\n    var tspanProto = TSpan.prototype;\n    tspanProto.dirtyRectTolerance = 10;\n  }();\n  return TSpan;\n}(Displayable);\nTSpan.prototype.type = 'tspan';\nexport default TSpan;", "map": {"version": 3, "names": ["__extends", "Displayable", "getBoundingRect", "DEFAULT_PATH_STYLE", "createObject", "defaults", "DEFAULT_FONT", "DEFAULT_TSPAN_STYLE", "<PERSON><PERSON><PERSON><PERSON>", "font", "x", "y", "textAlign", "textBaseline", "miterLimit", "TSpan", "_super", "apply", "arguments", "prototype", "hasStroke", "style", "stroke", "lineWidth", "hasFill", "fill", "createStyle", "obj", "setBoundingRect", "rect", "_rect", "text", "w", "width", "height", "initDefaultProps", "tspanProto", "dirtyRectTolerance", "type"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/zrender/lib/graphic/TSpan.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Displayable from './Displayable.js';\nimport { getBoundingRect } from '../contain/text.js';\nimport { DEFAULT_PATH_STYLE } from './Path.js';\nimport { createObject, defaults } from '../core/util.js';\nimport { DEFAULT_FONT } from '../core/platform.js';\nexport var DEFAULT_TSPAN_STYLE = defaults({\n    strokeFirst: true,\n    font: DEFAULT_FONT,\n    x: 0,\n    y: 0,\n    textAlign: 'left',\n    textBaseline: 'top',\n    miterLimit: 2\n}, DEFAULT_PATH_STYLE);\nvar TSpan = (function (_super) {\n    __extends(TSpan, _super);\n    function TSpan() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    TSpan.prototype.hasStroke = function () {\n        var style = this.style;\n        var stroke = style.stroke;\n        return stroke != null && stroke !== 'none' && style.lineWidth > 0;\n    };\n    TSpan.prototype.hasFill = function () {\n        var style = this.style;\n        var fill = style.fill;\n        return fill != null && fill !== 'none';\n    };\n    TSpan.prototype.createStyle = function (obj) {\n        return createObject(DEFAULT_TSPAN_STYLE, obj);\n    };\n    TSpan.prototype.setBoundingRect = function (rect) {\n        this._rect = rect;\n    };\n    TSpan.prototype.getBoundingRect = function () {\n        var style = this.style;\n        if (!this._rect) {\n            var text = style.text;\n            text != null ? (text += '') : (text = '');\n            var rect = getBoundingRect(text, style.font, style.textAlign, style.textBaseline);\n            rect.x += style.x || 0;\n            rect.y += style.y || 0;\n            if (this.hasStroke()) {\n                var w = style.lineWidth;\n                rect.x -= w / 2;\n                rect.y -= w / 2;\n                rect.width += w;\n                rect.height += w;\n            }\n            this._rect = rect;\n        }\n        return this._rect;\n    };\n    TSpan.initDefaultProps = (function () {\n        var tspanProto = TSpan.prototype;\n        tspanProto.dirtyRectTolerance = 10;\n    })();\n    return TSpan;\n}(Displayable));\nTSpan.prototype.type = 'tspan';\nexport default TSpan;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,kBAAkB,QAAQ,WAAW;AAC9C,SAASC,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AACxD,SAASC,YAAY,QAAQ,qBAAqB;AAClD,OAAO,IAAIC,mBAAmB,GAAGF,QAAQ,CAAC;EACtCG,WAAW,EAAE,IAAI;EACjBC,IAAI,EAAEH,YAAY;EAClBI,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,SAAS,EAAE,MAAM;EACjBC,YAAY,EAAE,KAAK;EACnBC,UAAU,EAAE;AAChB,CAAC,EAAEX,kBAAkB,CAAC;AACtB,IAAIY,KAAK,GAAI,UAAUC,MAAM,EAAE;EAC3BhB,SAAS,CAACe,KAAK,EAAEC,MAAM,CAAC;EACxB,SAASD,KAAKA,CAAA,EAAG;IACb,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACnE;EACAH,KAAK,CAACI,SAAS,CAACC,SAAS,GAAG,YAAY;IACpC,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACzB,OAAOA,MAAM,IAAI,IAAI,IAAIA,MAAM,KAAK,MAAM,IAAID,KAAK,CAACE,SAAS,GAAG,CAAC;EACrE,CAAC;EACDR,KAAK,CAACI,SAAS,CAACK,OAAO,GAAG,YAAY;IAClC,IAAIH,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAII,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACrB,OAAOA,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,MAAM;EAC1C,CAAC;EACDV,KAAK,CAACI,SAAS,CAACO,WAAW,GAAG,UAAUC,GAAG,EAAE;IACzC,OAAOvB,YAAY,CAACG,mBAAmB,EAAEoB,GAAG,CAAC;EACjD,CAAC;EACDZ,KAAK,CAACI,SAAS,CAACS,eAAe,GAAG,UAAUC,IAAI,EAAE;IAC9C,IAAI,CAACC,KAAK,GAAGD,IAAI;EACrB,CAAC;EACDd,KAAK,CAACI,SAAS,CAACjB,eAAe,GAAG,YAAY;IAC1C,IAAImB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI,CAAC,IAAI,CAACS,KAAK,EAAE;MACb,IAAIC,IAAI,GAAGV,KAAK,CAACU,IAAI;MACrBA,IAAI,IAAI,IAAI,GAAIA,IAAI,IAAI,EAAE,GAAKA,IAAI,GAAG,EAAG;MACzC,IAAIF,IAAI,GAAG3B,eAAe,CAAC6B,IAAI,EAAEV,KAAK,CAACZ,IAAI,EAAEY,KAAK,CAACT,SAAS,EAAES,KAAK,CAACR,YAAY,CAAC;MACjFgB,IAAI,CAACnB,CAAC,IAAIW,KAAK,CAACX,CAAC,IAAI,CAAC;MACtBmB,IAAI,CAAClB,CAAC,IAAIU,KAAK,CAACV,CAAC,IAAI,CAAC;MACtB,IAAI,IAAI,CAACS,SAAS,CAAC,CAAC,EAAE;QAClB,IAAIY,CAAC,GAAGX,KAAK,CAACE,SAAS;QACvBM,IAAI,CAACnB,CAAC,IAAIsB,CAAC,GAAG,CAAC;QACfH,IAAI,CAAClB,CAAC,IAAIqB,CAAC,GAAG,CAAC;QACfH,IAAI,CAACI,KAAK,IAAID,CAAC;QACfH,IAAI,CAACK,MAAM,IAAIF,CAAC;MACpB;MACA,IAAI,CAACF,KAAK,GAAGD,IAAI;IACrB;IACA,OAAO,IAAI,CAACC,KAAK;EACrB,CAAC;EACDf,KAAK,CAACoB,gBAAgB,GAAI,YAAY;IAClC,IAAIC,UAAU,GAAGrB,KAAK,CAACI,SAAS;IAChCiB,UAAU,CAACC,kBAAkB,GAAG,EAAE;EACtC,CAAC,CAAE,CAAC;EACJ,OAAOtB,KAAK;AAChB,CAAC,CAACd,WAAW,CAAE;AACfc,KAAK,CAACI,SAAS,CAACmB,IAAI,GAAG,OAAO;AAC9B,eAAevB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}