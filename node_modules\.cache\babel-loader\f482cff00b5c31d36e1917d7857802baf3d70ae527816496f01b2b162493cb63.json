{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport Dropdown from 'rc-dropdown';\nimport Menu, { MenuItem } from 'rc-menu';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { useEffect, useState } from 'react';\nimport { getRemovable } from \"../util\";\nimport AddButton from \"./AddButton\";\nvar OperationNode = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    tabs = props.tabs,\n    locale = props.locale,\n    mobile = props.mobile,\n    _props$more = props.more,\n    moreProps = _props$more === void 0 ? {} : _props$more,\n    style = props.style,\n    className = props.className,\n    editable = props.editable,\n    tabBarGutter = props.tabBarGutter,\n    rtl = props.rtl,\n    removeAriaLabel = props.removeAriaLabel,\n    onTabClick = props.onTabClick,\n    getPopupContainer = props.getPopupContainer,\n    popupClassName = props.popupClassName;\n  // ======================== Dropdown ========================\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    open = _useState2[0],\n    setOpen = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    selectedKey = _useState4[0],\n    setSelectedKey = _useState4[1];\n  var _moreProps$icon = moreProps.icon,\n    moreIcon = _moreProps$icon === void 0 ? 'More' : _moreProps$icon;\n  var popupId = \"\".concat(id, \"-more-popup\");\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var selectedItemId = selectedKey !== null ? \"\".concat(popupId, \"-\").concat(selectedKey) : null;\n  var dropdownAriaLabel = locale === null || locale === void 0 ? void 0 : locale.dropdownAriaLabel;\n  function onRemoveTab(event, key) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var menu = /*#__PURE__*/React.createElement(Menu, {\n    onClick: function onClick(_ref) {\n      var key = _ref.key,\n        domEvent = _ref.domEvent;\n      onTabClick(key, domEvent);\n      setOpen(false);\n    },\n    prefixCls: \"\".concat(dropdownPrefix, \"-menu\"),\n    id: popupId,\n    tabIndex: -1,\n    role: \"listbox\",\n    \"aria-activedescendant\": selectedItemId,\n    selectedKeys: [selectedKey],\n    \"aria-label\": dropdownAriaLabel !== undefined ? dropdownAriaLabel : 'expanded dropdown'\n  }, tabs.map(function (tab) {\n    var closable = tab.closable,\n      disabled = tab.disabled,\n      closeIcon = tab.closeIcon,\n      key = tab.key,\n      label = tab.label;\n    var removable = getRemovable(closable, closeIcon, editable, disabled);\n    return /*#__PURE__*/React.createElement(MenuItem, {\n      key: key,\n      id: \"\".concat(popupId, \"-\").concat(key),\n      role: \"option\",\n      \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n      disabled: disabled\n    }, /*#__PURE__*/React.createElement(\"span\", null, label), removable && /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": removeAriaLabel || 'remove',\n      tabIndex: 0,\n      className: \"\".concat(dropdownPrefix, \"-menu-item-remove\"),\n      onClick: function onClick(e) {\n        e.stopPropagation();\n        onRemoveTab(e, key);\n      }\n    }, closeIcon || editable.removeIcon || '×'));\n  }));\n  function selectOffset(offset) {\n    var enabledTabs = tabs.filter(function (tab) {\n      return !tab.disabled;\n    });\n    var selectedIndex = enabledTabs.findIndex(function (tab) {\n      return tab.key === selectedKey;\n    }) || 0;\n    var len = enabledTabs.length;\n    for (var i = 0; i < len; i += 1) {\n      selectedIndex = (selectedIndex + offset + len) % len;\n      var tab = enabledTabs[selectedIndex];\n      if (!tab.disabled) {\n        setSelectedKey(tab.key);\n        return;\n      }\n    }\n  }\n  function onKeyDown(e) {\n    var which = e.which;\n    if (!open) {\n      if ([KeyCode.DOWN, KeyCode.SPACE, KeyCode.ENTER].includes(which)) {\n        setOpen(true);\n        e.preventDefault();\n      }\n      return;\n    }\n    switch (which) {\n      case KeyCode.UP:\n        selectOffset(-1);\n        e.preventDefault();\n        break;\n      case KeyCode.DOWN:\n        selectOffset(1);\n        e.preventDefault();\n        break;\n      case KeyCode.ESC:\n        setOpen(false);\n        break;\n      case KeyCode.SPACE:\n      case KeyCode.ENTER:\n        if (selectedKey !== null) {\n          onTabClick(selectedKey, e);\n        }\n        break;\n    }\n  }\n\n  // ========================= Effect =========================\n  useEffect(function () {\n    // We use query element here to avoid React strict warning\n    var ele = document.getElementById(selectedItemId);\n    if (ele && ele.scrollIntoView) {\n      ele.scrollIntoView(false);\n    }\n  }, [selectedKey]);\n  useEffect(function () {\n    if (!open) {\n      setSelectedKey(null);\n    }\n  }, [open]);\n\n  // ========================= Render =========================\n  var moreStyle = _defineProperty({}, rtl ? 'marginRight' : 'marginLeft', tabBarGutter);\n  if (!tabs.length) {\n    moreStyle.visibility = 'hidden';\n    moreStyle.order = 1;\n  }\n  var overlayClassName = classNames(_defineProperty({}, \"\".concat(dropdownPrefix, \"-rtl\"), rtl));\n  var moreNode = mobile ? null : /*#__PURE__*/React.createElement(Dropdown, _extends({\n    prefixCls: dropdownPrefix,\n    overlay: menu,\n    visible: tabs.length ? open : false,\n    onVisibleChange: setOpen,\n    overlayClassName: classNames(overlayClassName, popupClassName),\n    mouseEnterDelay: 0.1,\n    mouseLeaveDelay: 0.1,\n    getPopupContainer: getPopupContainer\n  }, moreProps), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-more\"),\n    style: moreStyle,\n    \"aria-haspopup\": \"listbox\",\n    \"aria-controls\": popupId,\n    id: \"\".concat(id, \"-more\"),\n    \"aria-expanded\": open,\n    onKeyDown: onKeyDown\n  }, moreIcon));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-nav-operations\"), className),\n    style: style,\n    ref: ref\n  }, moreNode, /*#__PURE__*/React.createElement(AddButton, {\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable\n  }));\n});\nexport default /*#__PURE__*/React.memo(OperationNode, function (_, next) {\n  return (\n    // https://github.com/ant-design/ant-design/issues/32544\n    // We'd better remove syntactic sugar in `rc-menu` since this has perf issue\n    next.tabMoving\n  );\n});", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "classNames", "Dropdown", "<PERSON><PERSON>", "MenuItem", "KeyCode", "React", "useEffect", "useState", "getRemovable", "AddButton", "OperationNode", "forwardRef", "props", "ref", "prefixCls", "id", "tabs", "locale", "mobile", "_props$more", "more", "moreProps", "style", "className", "editable", "tabBarGutter", "rtl", "removeAriaLabel", "onTabClick", "getPopupContainer", "popupClassName", "_useState", "_useState2", "open", "<PERSON><PERSON><PERSON>", "_useState3", "_useState4", "<PERSON><PERSON><PERSON>", "setSelectedKey", "_moreProps$icon", "icon", "moreIcon", "popupId", "concat", "dropdownPrefix", "selectedItemId", "dropdownAriaLabel", "onRemoveTab", "event", "key", "preventDefault", "stopPropagation", "onEdit", "menu", "createElement", "onClick", "_ref", "domEvent", "tabIndex", "role", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "map", "tab", "closable", "disabled", "closeIcon", "label", "removable", "type", "e", "removeIcon", "selectOffset", "offset", "enabledTabs", "filter", "selectedIndex", "findIndex", "len", "length", "i", "onKeyDown", "which", "DOWN", "SPACE", "ENTER", "includes", "UP", "ESC", "ele", "document", "getElementById", "scrollIntoView", "moreStyle", "visibility", "order", "overlayClassName", "moreNode", "overlay", "visible", "onVisibleChange", "mouseEnterDelay", "mouseLeaveDelay", "memo", "_", "next", "tabMoving"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-tabs/es/TabNavList/OperationNode.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport Dropdown from 'rc-dropdown';\nimport Menu, { MenuItem } from 'rc-menu';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { useEffect, useState } from 'react';\nimport { getRemovable } from \"../util\";\nimport AddButton from \"./AddButton\";\nvar OperationNode = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    tabs = props.tabs,\n    locale = props.locale,\n    mobile = props.mobile,\n    _props$more = props.more,\n    moreProps = _props$more === void 0 ? {} : _props$more,\n    style = props.style,\n    className = props.className,\n    editable = props.editable,\n    tabBarGutter = props.tabBarGutter,\n    rtl = props.rtl,\n    removeAriaLabel = props.removeAriaLabel,\n    onTabClick = props.onTabClick,\n    getPopupContainer = props.getPopupContainer,\n    popupClassName = props.popupClassName;\n  // ======================== Dropdown ========================\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    open = _useState2[0],\n    setOpen = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    selectedKey = _useState4[0],\n    setSelectedKey = _useState4[1];\n  var _moreProps$icon = moreProps.icon,\n    moreIcon = _moreProps$icon === void 0 ? 'More' : _moreProps$icon;\n  var popupId = \"\".concat(id, \"-more-popup\");\n  var dropdownPrefix = \"\".concat(prefixCls, \"-dropdown\");\n  var selectedItemId = selectedKey !== null ? \"\".concat(popupId, \"-\").concat(selectedKey) : null;\n  var dropdownAriaLabel = locale === null || locale === void 0 ? void 0 : locale.dropdownAriaLabel;\n  function onRemoveTab(event, key) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var menu = /*#__PURE__*/React.createElement(Menu, {\n    onClick: function onClick(_ref) {\n      var key = _ref.key,\n        domEvent = _ref.domEvent;\n      onTabClick(key, domEvent);\n      setOpen(false);\n    },\n    prefixCls: \"\".concat(dropdownPrefix, \"-menu\"),\n    id: popupId,\n    tabIndex: -1,\n    role: \"listbox\",\n    \"aria-activedescendant\": selectedItemId,\n    selectedKeys: [selectedKey],\n    \"aria-label\": dropdownAriaLabel !== undefined ? dropdownAriaLabel : 'expanded dropdown'\n  }, tabs.map(function (tab) {\n    var closable = tab.closable,\n      disabled = tab.disabled,\n      closeIcon = tab.closeIcon,\n      key = tab.key,\n      label = tab.label;\n    var removable = getRemovable(closable, closeIcon, editable, disabled);\n    return /*#__PURE__*/React.createElement(MenuItem, {\n      key: key,\n      id: \"\".concat(popupId, \"-\").concat(key),\n      role: \"option\",\n      \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n      disabled: disabled\n    }, /*#__PURE__*/React.createElement(\"span\", null, label), removable && /*#__PURE__*/React.createElement(\"button\", {\n      type: \"button\",\n      \"aria-label\": removeAriaLabel || 'remove',\n      tabIndex: 0,\n      className: \"\".concat(dropdownPrefix, \"-menu-item-remove\"),\n      onClick: function onClick(e) {\n        e.stopPropagation();\n        onRemoveTab(e, key);\n      }\n    }, closeIcon || editable.removeIcon || '×'));\n  }));\n  function selectOffset(offset) {\n    var enabledTabs = tabs.filter(function (tab) {\n      return !tab.disabled;\n    });\n    var selectedIndex = enabledTabs.findIndex(function (tab) {\n      return tab.key === selectedKey;\n    }) || 0;\n    var len = enabledTabs.length;\n    for (var i = 0; i < len; i += 1) {\n      selectedIndex = (selectedIndex + offset + len) % len;\n      var tab = enabledTabs[selectedIndex];\n      if (!tab.disabled) {\n        setSelectedKey(tab.key);\n        return;\n      }\n    }\n  }\n  function onKeyDown(e) {\n    var which = e.which;\n    if (!open) {\n      if ([KeyCode.DOWN, KeyCode.SPACE, KeyCode.ENTER].includes(which)) {\n        setOpen(true);\n        e.preventDefault();\n      }\n      return;\n    }\n    switch (which) {\n      case KeyCode.UP:\n        selectOffset(-1);\n        e.preventDefault();\n        break;\n      case KeyCode.DOWN:\n        selectOffset(1);\n        e.preventDefault();\n        break;\n      case KeyCode.ESC:\n        setOpen(false);\n        break;\n      case KeyCode.SPACE:\n      case KeyCode.ENTER:\n        if (selectedKey !== null) {\n          onTabClick(selectedKey, e);\n        }\n        break;\n    }\n  }\n\n  // ========================= Effect =========================\n  useEffect(function () {\n    // We use query element here to avoid React strict warning\n    var ele = document.getElementById(selectedItemId);\n    if (ele && ele.scrollIntoView) {\n      ele.scrollIntoView(false);\n    }\n  }, [selectedKey]);\n  useEffect(function () {\n    if (!open) {\n      setSelectedKey(null);\n    }\n  }, [open]);\n\n  // ========================= Render =========================\n  var moreStyle = _defineProperty({}, rtl ? 'marginRight' : 'marginLeft', tabBarGutter);\n  if (!tabs.length) {\n    moreStyle.visibility = 'hidden';\n    moreStyle.order = 1;\n  }\n  var overlayClassName = classNames(_defineProperty({}, \"\".concat(dropdownPrefix, \"-rtl\"), rtl));\n  var moreNode = mobile ? null : /*#__PURE__*/React.createElement(Dropdown, _extends({\n    prefixCls: dropdownPrefix,\n    overlay: menu,\n    visible: tabs.length ? open : false,\n    onVisibleChange: setOpen,\n    overlayClassName: classNames(overlayClassName, popupClassName),\n    mouseEnterDelay: 0.1,\n    mouseLeaveDelay: 0.1,\n    getPopupContainer: getPopupContainer\n  }, moreProps), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-more\"),\n    style: moreStyle,\n    \"aria-haspopup\": \"listbox\",\n    \"aria-controls\": popupId,\n    id: \"\".concat(id, \"-more\"),\n    \"aria-expanded\": open,\n    onKeyDown: onKeyDown\n  }, moreIcon));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-nav-operations\"), className),\n    style: style,\n    ref: ref\n  }, moreNode, /*#__PURE__*/React.createElement(AddButton, {\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable\n  }));\n});\nexport default /*#__PURE__*/React.memo(OperationNode, function (_, next) {\n  return (\n    // https://github.com/ant-design/ant-design/issues/32544\n    // We'd better remove syntactic sugar in `rc-menu` since this has perf issue\n    next.tabMoving\n  );\n});"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,IAAI,IAAIC,QAAQ,QAAQ,SAAS;AACxC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,YAAY,QAAQ,SAAS;AACtC,OAAOC,SAAS,MAAM,aAAa;AACnC,IAAIC,aAAa,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACtE,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,EAAE,GAAGH,KAAK,CAACG,EAAE;IACbC,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACjBC,MAAM,GAAGL,KAAK,CAACK,MAAM;IACrBC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,WAAW,GAAGP,KAAK,CAACQ,IAAI;IACxBC,SAAS,GAAGF,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,WAAW;IACrDG,KAAK,GAAGV,KAAK,CAACU,KAAK;IACnBC,SAAS,GAAGX,KAAK,CAACW,SAAS;IAC3BC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,YAAY,GAAGb,KAAK,CAACa,YAAY;IACjCC,GAAG,GAAGd,KAAK,CAACc,GAAG;IACfC,eAAe,GAAGf,KAAK,CAACe,eAAe;IACvCC,UAAU,GAAGhB,KAAK,CAACgB,UAAU;IAC7BC,iBAAiB,GAAGjB,KAAK,CAACiB,iBAAiB;IAC3CC,cAAc,GAAGlB,KAAK,CAACkB,cAAc;EACvC;EACA,IAAIC,SAAS,GAAGxB,QAAQ,CAAC,KAAK,CAAC;IAC7ByB,UAAU,GAAGjC,cAAc,CAACgC,SAAS,EAAE,CAAC,CAAC;IACzCE,IAAI,GAAGD,UAAU,CAAC,CAAC,CAAC;IACpBE,OAAO,GAAGF,UAAU,CAAC,CAAC,CAAC;EACzB,IAAIG,UAAU,GAAG5B,QAAQ,CAAC,IAAI,CAAC;IAC7B6B,UAAU,GAAGrC,cAAc,CAACoC,UAAU,EAAE,CAAC,CAAC;IAC1CE,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC3BE,cAAc,GAAGF,UAAU,CAAC,CAAC,CAAC;EAChC,IAAIG,eAAe,GAAGlB,SAAS,CAACmB,IAAI;IAClCC,QAAQ,GAAGF,eAAe,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,eAAe;EAClE,IAAIG,OAAO,GAAG,EAAE,CAACC,MAAM,CAAC5B,EAAE,EAAE,aAAa,CAAC;EAC1C,IAAI6B,cAAc,GAAG,EAAE,CAACD,MAAM,CAAC7B,SAAS,EAAE,WAAW,CAAC;EACtD,IAAI+B,cAAc,GAAGR,WAAW,KAAK,IAAI,GAAG,EAAE,CAACM,MAAM,CAACD,OAAO,EAAE,GAAG,CAAC,CAACC,MAAM,CAACN,WAAW,CAAC,GAAG,IAAI;EAC9F,IAAIS,iBAAiB,GAAG7B,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC6B,iBAAiB;EAChG,SAASC,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC/BD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBF,KAAK,CAACG,eAAe,CAAC,CAAC;IACvB3B,QAAQ,CAAC4B,MAAM,CAAC,QAAQ,EAAE;MACxBH,GAAG,EAAEA,GAAG;MACRD,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ;EACA,IAAIK,IAAI,GAAG,aAAahD,KAAK,CAACiD,aAAa,CAACpD,IAAI,EAAE;IAChDqD,OAAO,EAAE,SAASA,OAAOA,CAACC,IAAI,EAAE;MAC9B,IAAIP,GAAG,GAAGO,IAAI,CAACP,GAAG;QAChBQ,QAAQ,GAAGD,IAAI,CAACC,QAAQ;MAC1B7B,UAAU,CAACqB,GAAG,EAAEQ,QAAQ,CAAC;MACzBvB,OAAO,CAAC,KAAK,CAAC;IAChB,CAAC;IACDpB,SAAS,EAAE,EAAE,CAAC6B,MAAM,CAACC,cAAc,EAAE,OAAO,CAAC;IAC7C7B,EAAE,EAAE2B,OAAO;IACXgB,QAAQ,EAAE,CAAC,CAAC;IACZC,IAAI,EAAE,SAAS;IACf,uBAAuB,EAAEd,cAAc;IACvCe,YAAY,EAAE,CAACvB,WAAW,CAAC;IAC3B,YAAY,EAAES,iBAAiB,KAAKe,SAAS,GAAGf,iBAAiB,GAAG;EACtE,CAAC,EAAE9B,IAAI,CAAC8C,GAAG,CAAC,UAAUC,GAAG,EAAE;IACzB,IAAIC,QAAQ,GAAGD,GAAG,CAACC,QAAQ;MACzBC,QAAQ,GAAGF,GAAG,CAACE,QAAQ;MACvBC,SAAS,GAAGH,GAAG,CAACG,SAAS;MACzBjB,GAAG,GAAGc,GAAG,CAACd,GAAG;MACbkB,KAAK,GAAGJ,GAAG,CAACI,KAAK;IACnB,IAAIC,SAAS,GAAG5D,YAAY,CAACwD,QAAQ,EAAEE,SAAS,EAAE1C,QAAQ,EAAEyC,QAAQ,CAAC;IACrE,OAAO,aAAa5D,KAAK,CAACiD,aAAa,CAACnD,QAAQ,EAAE;MAChD8C,GAAG,EAAEA,GAAG;MACRlC,EAAE,EAAE,EAAE,CAAC4B,MAAM,CAACD,OAAO,EAAE,GAAG,CAAC,CAACC,MAAM,CAACM,GAAG,CAAC;MACvCU,IAAI,EAAE,QAAQ;MACd,eAAe,EAAE5C,EAAE,IAAI,EAAE,CAAC4B,MAAM,CAAC5B,EAAE,EAAE,SAAS,CAAC,CAAC4B,MAAM,CAACM,GAAG,CAAC;MAC3DgB,QAAQ,EAAEA;IACZ,CAAC,EAAE,aAAa5D,KAAK,CAACiD,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEa,KAAK,CAAC,EAAEC,SAAS,IAAI,aAAa/D,KAAK,CAACiD,aAAa,CAAC,QAAQ,EAAE;MAChHe,IAAI,EAAE,QAAQ;MACd,YAAY,EAAE1C,eAAe,IAAI,QAAQ;MACzC+B,QAAQ,EAAE,CAAC;MACXnC,SAAS,EAAE,EAAE,CAACoB,MAAM,CAACC,cAAc,EAAE,mBAAmB,CAAC;MACzDW,OAAO,EAAE,SAASA,OAAOA,CAACe,CAAC,EAAE;QAC3BA,CAAC,CAACnB,eAAe,CAAC,CAAC;QACnBJ,WAAW,CAACuB,CAAC,EAAErB,GAAG,CAAC;MACrB;IACF,CAAC,EAAEiB,SAAS,IAAI1C,QAAQ,CAAC+C,UAAU,IAAI,GAAG,CAAC,CAAC;EAC9C,CAAC,CAAC,CAAC;EACH,SAASC,YAAYA,CAACC,MAAM,EAAE;IAC5B,IAAIC,WAAW,GAAG1D,IAAI,CAAC2D,MAAM,CAAC,UAAUZ,GAAG,EAAE;MAC3C,OAAO,CAACA,GAAG,CAACE,QAAQ;IACtB,CAAC,CAAC;IACF,IAAIW,aAAa,GAAGF,WAAW,CAACG,SAAS,CAAC,UAAUd,GAAG,EAAE;MACvD,OAAOA,GAAG,CAACd,GAAG,KAAKZ,WAAW;IAChC,CAAC,CAAC,IAAI,CAAC;IACP,IAAIyC,GAAG,GAAGJ,WAAW,CAACK,MAAM;IAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAI,CAAC,EAAE;MAC/BJ,aAAa,GAAG,CAACA,aAAa,GAAGH,MAAM,GAAGK,GAAG,IAAIA,GAAG;MACpD,IAAIf,GAAG,GAAGW,WAAW,CAACE,aAAa,CAAC;MACpC,IAAI,CAACb,GAAG,CAACE,QAAQ,EAAE;QACjB3B,cAAc,CAACyB,GAAG,CAACd,GAAG,CAAC;QACvB;MACF;IACF;EACF;EACA,SAASgC,SAASA,CAACX,CAAC,EAAE;IACpB,IAAIY,KAAK,GAAGZ,CAAC,CAACY,KAAK;IACnB,IAAI,CAACjD,IAAI,EAAE;MACT,IAAI,CAAC7B,OAAO,CAAC+E,IAAI,EAAE/E,OAAO,CAACgF,KAAK,EAAEhF,OAAO,CAACiF,KAAK,CAAC,CAACC,QAAQ,CAACJ,KAAK,CAAC,EAAE;QAChEhD,OAAO,CAAC,IAAI,CAAC;QACboC,CAAC,CAACpB,cAAc,CAAC,CAAC;MACpB;MACA;IACF;IACA,QAAQgC,KAAK;MACX,KAAK9E,OAAO,CAACmF,EAAE;QACbf,YAAY,CAAC,CAAC,CAAC,CAAC;QAChBF,CAAC,CAACpB,cAAc,CAAC,CAAC;QAClB;MACF,KAAK9C,OAAO,CAAC+E,IAAI;QACfX,YAAY,CAAC,CAAC,CAAC;QACfF,CAAC,CAACpB,cAAc,CAAC,CAAC;QAClB;MACF,KAAK9C,OAAO,CAACoF,GAAG;QACdtD,OAAO,CAAC,KAAK,CAAC;QACd;MACF,KAAK9B,OAAO,CAACgF,KAAK;MAClB,KAAKhF,OAAO,CAACiF,KAAK;QAChB,IAAIhD,WAAW,KAAK,IAAI,EAAE;UACxBT,UAAU,CAACS,WAAW,EAAEiC,CAAC,CAAC;QAC5B;QACA;IACJ;EACF;;EAEA;EACAhE,SAAS,CAAC,YAAY;IACpB;IACA,IAAImF,GAAG,GAAGC,QAAQ,CAACC,cAAc,CAAC9C,cAAc,CAAC;IACjD,IAAI4C,GAAG,IAAIA,GAAG,CAACG,cAAc,EAAE;MAC7BH,GAAG,CAACG,cAAc,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC,EAAE,CAACvD,WAAW,CAAC,CAAC;EACjB/B,SAAS,CAAC,YAAY;IACpB,IAAI,CAAC2B,IAAI,EAAE;MACTK,cAAc,CAAC,IAAI,CAAC;IACtB;EACF,CAAC,EAAE,CAACL,IAAI,CAAC,CAAC;;EAEV;EACA,IAAI4D,SAAS,GAAG/F,eAAe,CAAC,CAAC,CAAC,EAAE4B,GAAG,GAAG,aAAa,GAAG,YAAY,EAAED,YAAY,CAAC;EACrF,IAAI,CAACT,IAAI,CAAC+D,MAAM,EAAE;IAChBc,SAAS,CAACC,UAAU,GAAG,QAAQ;IAC/BD,SAAS,CAACE,KAAK,GAAG,CAAC;EACrB;EACA,IAAIC,gBAAgB,GAAGhG,UAAU,CAACF,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC6C,MAAM,CAACC,cAAc,EAAE,MAAM,CAAC,EAAElB,GAAG,CAAC,CAAC;EAC9F,IAAIuE,QAAQ,GAAG/E,MAAM,GAAG,IAAI,GAAG,aAAab,KAAK,CAACiD,aAAa,CAACrD,QAAQ,EAAEJ,QAAQ,CAAC;IACjFiB,SAAS,EAAE8B,cAAc;IACzBsD,OAAO,EAAE7C,IAAI;IACb8C,OAAO,EAAEnF,IAAI,CAAC+D,MAAM,GAAG9C,IAAI,GAAG,KAAK;IACnCmE,eAAe,EAAElE,OAAO;IACxB8D,gBAAgB,EAAEhG,UAAU,CAACgG,gBAAgB,EAAElE,cAAc,CAAC;IAC9DuE,eAAe,EAAE,GAAG;IACpBC,eAAe,EAAE,GAAG;IACpBzE,iBAAiB,EAAEA;EACrB,CAAC,EAAER,SAAS,CAAC,EAAE,aAAahB,KAAK,CAACiD,aAAa,CAAC,QAAQ,EAAE;IACxDe,IAAI,EAAE,QAAQ;IACd9C,SAAS,EAAE,EAAE,CAACoB,MAAM,CAAC7B,SAAS,EAAE,WAAW,CAAC;IAC5CQ,KAAK,EAAEuE,SAAS;IAChB,eAAe,EAAE,SAAS;IAC1B,eAAe,EAAEnD,OAAO;IACxB3B,EAAE,EAAE,EAAE,CAAC4B,MAAM,CAAC5B,EAAE,EAAE,OAAO,CAAC;IAC1B,eAAe,EAAEkB,IAAI;IACrBgD,SAAS,EAAEA;EACb,CAAC,EAAExC,QAAQ,CAAC,CAAC;EACb,OAAO,aAAapC,KAAK,CAACiD,aAAa,CAAC,KAAK,EAAE;IAC7C/B,SAAS,EAAEvB,UAAU,CAAC,EAAE,CAAC2C,MAAM,CAAC7B,SAAS,EAAE,iBAAiB,CAAC,EAAES,SAAS,CAAC;IACzED,KAAK,EAAEA,KAAK;IACZT,GAAG,EAAEA;EACP,CAAC,EAAEoF,QAAQ,EAAE,aAAa5F,KAAK,CAACiD,aAAa,CAAC7C,SAAS,EAAE;IACvDK,SAAS,EAAEA,SAAS;IACpBG,MAAM,EAAEA,MAAM;IACdO,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,eAAe,aAAanB,KAAK,CAACkG,IAAI,CAAC7F,aAAa,EAAE,UAAU8F,CAAC,EAAEC,IAAI,EAAE;EACvE;IACE;IACA;IACAA,IAAI,CAACC;EAAS;AAElB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}