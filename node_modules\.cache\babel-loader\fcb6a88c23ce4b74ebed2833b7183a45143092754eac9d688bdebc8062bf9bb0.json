{"ast": null, "code": "import * as React from 'react';\nvar CascaderContext = /*#__PURE__*/React.createContext({});\nexport default CascaderContext;", "map": {"version": 3, "names": ["React", "CascaderContext", "createContext"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-cascader/es/context.js"], "sourcesContent": ["import * as React from 'react';\nvar CascaderContext = /*#__PURE__*/React.createContext({});\nexport default CascaderContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AAC1D,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}