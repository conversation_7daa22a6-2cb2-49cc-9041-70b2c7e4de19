{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport default function dataFilter(seriesType) {\n  return {\n    seriesType: seriesType,\n    reset: function (seriesModel, ecModel) {\n      var legendModels = ecModel.findComponents({\n        mainType: 'legend'\n      });\n      if (!legendModels || !legendModels.length) {\n        return;\n      }\n      var data = seriesModel.getData();\n      data.filterSelf(function (idx) {\n        var name = data.getName(idx);\n        // If in any legend component the status is not selected.\n        for (var i = 0; i < legendModels.length; i++) {\n          // @ts-ignore FIXME: LegendModel\n          if (!legendModels[i].isSelected(name)) {\n            return false;\n          }\n        }\n        return true;\n      });\n    }\n  };\n}", "map": {"version": 3, "names": ["dataFilter", "seriesType", "reset", "seriesModel", "ecModel", "legend<PERSON><PERSON><PERSON>", "findComponents", "mainType", "length", "data", "getData", "filterSelf", "idx", "name", "getName", "i", "isSelected"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/echarts/lib/processor/dataFilter.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport default function dataFilter(seriesType) {\n  return {\n    seriesType: seriesType,\n    reset: function (seriesModel, ecModel) {\n      var legendModels = ecModel.findComponents({\n        mainType: 'legend'\n      });\n      if (!legendModels || !legendModels.length) {\n        return;\n      }\n      var data = seriesModel.getData();\n      data.filterSelf(function (idx) {\n        var name = data.getName(idx);\n        // If in any legend component the status is not selected.\n        for (var i = 0; i < legendModels.length; i++) {\n          // @ts-ignore FIXME: LegendModel\n          if (!legendModels[i].isSelected(name)) {\n            return false;\n          }\n        }\n        return true;\n      });\n    }\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,UAAUA,CAACC,UAAU,EAAE;EAC7C,OAAO;IACLA,UAAU,EAAEA,UAAU;IACtBC,KAAK,EAAE,SAAAA,CAAUC,WAAW,EAAEC,OAAO,EAAE;MACrC,IAAIC,YAAY,GAAGD,OAAO,CAACE,cAAc,CAAC;QACxCC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,IAAI,CAACF,YAAY,IAAI,CAACA,YAAY,CAACG,MAAM,EAAE;QACzC;MACF;MACA,IAAIC,IAAI,GAAGN,WAAW,CAACO,OAAO,CAAC,CAAC;MAChCD,IAAI,CAACE,UAAU,CAAC,UAAUC,GAAG,EAAE;QAC7B,IAAIC,IAAI,GAAGJ,IAAI,CAACK,OAAO,CAACF,GAAG,CAAC;QAC5B;QACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,YAAY,CAACG,MAAM,EAAEO,CAAC,EAAE,EAAE;UAC5C;UACA,IAAI,CAACV,YAAY,CAACU,CAAC,CAAC,CAACC,UAAU,CAACH,IAAI,CAAC,EAAE;YACrC,OAAO,KAAK;UACd;QACF;QACA,OAAO,IAAI;MACb,CAAC,CAAC;IACJ;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}