{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\nlet trafficParticipants = new Map(); // 使用 Map 存储所有交通参与者\nlet participantMeshes = new Map(); // 存储交通参与者的 3D 模型\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  wsUrl: 'ws://localhost:8083/mqtt',\n  // 添加路径\n  username: 'cloud',\n  password: 'A825FBCBB97D1975',\n  clientId: 'V2X_CENTER_CSLG_opentest',\n  topics: ['changli/cloud/v2x/rsu/rsm', 'changli/cloud/v2x/obu/bsm']\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改初始化 MQTT 连接\n  const initMqttClient = () => {\n    console.log('正在连接WebSocket...');\n    try {\n      const ws = new WebSocket(MQTT_CONFIG.wsUrl);\n\n      // 添加更多连接状态日志\n      ws.onopen = () => {\n        console.log('WebSocket连接成功');\n        // 发送认证信息\n        ws.send(JSON.stringify({\n          type: 'auth',\n          clientId: MQTT_CONFIG.clientId,\n          username: MQTT_CONFIG.username,\n          password: MQTT_CONFIG.password\n        }));\n      };\n      ws.onmessage = event => {\n        console.log('收到WebSocket消息:', event.data);\n        try {\n          const {\n            topic,\n            message\n          } = JSON.parse(event.data);\n          const data = JSON.parse(message);\n\n          // 处理消息\n          switch (topic) {\n            case 'changli/cloud/v2x/rsu/rsm':\n              handleRSMMessage(data);\n              break;\n            case 'changli/cloud/v2x/obu/bsm':\n              handleBSMMessage(data);\n              break;\n          }\n        } catch (error) {\n          console.error('处理消息失败:', error);\n        }\n      };\n      ws.onerror = error => {\n        console.error('WebSocket错误:', error.message);\n      };\n      mqttClientRef.current = ws;\n    } catch (error) {\n      console.error('创建WebSocket连接失败:', error);\n    }\n  };\n\n  // 处理 BSM 消息\n  const handleBSMMessage = data => {\n    if (data.type === 'BSM' && data.data) {\n      const bsmData = data.data;\n      const newState = {\n        longitude: parseFloat(bsmData.partLong),\n        latitude: parseFloat(bsmData.partLat),\n        speed: parseFloat(bsmData.partSpeed),\n        heading: parseFloat(bsmData.partHeading)\n      };\n      console.log('解析后的车辆状态:', newState);\n\n      // 更新车辆位置和状态\n      if (globalVehicleRef) {\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        globalVehicleRef.position.copy(newPosition);\n        globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n        globalVehicleRef.updateMatrix();\n        globalVehicleRef.updateMatrixWorld(true);\n        setVehicleState(newState);\n        console.log('车辆位置已更新:', newPosition);\n      }\n    }\n  };\n\n  // 处理 RSM 消息\n  const handleRSMMessage = data => {\n    if (data.type === 'RSM' && data.data) {\n      var _rsmData$participants;\n      const rsmData = data.data;\n      console.log('收到RSM数据:', {\n        设备ID: rsmData.rsuId,\n        基准点: {\n          经度: rsmData.posLong,\n          纬度: rsmData.posLat\n        },\n        参与者数量: ((_rsmData$participants = rsmData.participants) === null || _rsmData$participants === void 0 ? void 0 : _rsmData$participants.length) || 0\n      });\n\n      // 处理交通参与者\n      if (rsmData.participants && Array.isArray(rsmData.participants)) {\n        rsmData.participants.forEach(participant => {\n          updateTrafficParticipant(participant);\n        });\n      }\n    }\n  };\n\n  // 更新交通参与者\n  const updateTrafficParticipant = participant => {\n    const id = participant.partPtcId;\n    const type = parseInt(participant.partPtcType);\n\n    // 转换坐标\n    const modelPos = converter.current.wgs84ToModel(parseFloat(participant.partPosLong), parseFloat(participant.partPosLat));\n\n    // 创建或更新参与者数据\n    const participantData = {\n      position: new THREE.Vector3(modelPos.x, 0.5, -modelPos.y),\n      heading: parseFloat(participant.partHeading),\n      speed: parseFloat(participant.partSpeed),\n      type: type,\n      size: {\n        length: parseFloat(participant.partLength) / 100,\n        // 转换为米\n        width: parseFloat(participant.partWidth) / 100,\n        height: parseFloat(participant.partHeight) / 100\n      },\n      lastUpdate: Date.now()\n    };\n\n    // 更新或创建 3D 模型\n    if (!participantMeshes.has(id)) {\n      // 创建新的 3D 模型\n      const mesh = createParticipantMesh(participantData);\n      if (mesh) {\n        scene.add(mesh);\n        participantMeshes.set(id, mesh);\n      }\n    } else {\n      // 更新现有模型\n      const mesh = participantMeshes.get(id);\n      updateParticipantMesh(mesh, participantData);\n    }\n\n    // 更新数据存储\n    trafficParticipants.set(id, participantData);\n  };\n\n  // 创建交通参与者的 3D 模型\n  const createParticipantMesh = data => {\n    let geometry, material;\n    switch (data.type) {\n      case 1:\n        // 机动车\n        geometry = new THREE.BoxGeometry(data.size.length, data.size.height, data.size.width);\n        material = new THREE.MeshPhongMaterial({\n          color: 0x4444ff\n        });\n        break;\n      case 2:\n        // 非机动车\n        geometry = new THREE.BoxGeometry(data.size.length, data.size.height, data.size.width);\n        material = new THREE.MeshPhongMaterial({\n          color: 0x44ff44\n        });\n        break;\n      case 3:\n        // 行人\n        geometry = new THREE.CylinderGeometry(0.3, 0.3, data.size.height, 8);\n        material = new THREE.MeshPhongMaterial({\n          color: 0xff4444\n        });\n        break;\n      default:\n        // 未知类型\n        geometry = new THREE.BoxGeometry(1, 1, 1);\n        material = new THREE.MeshPhongMaterial({\n          color: 0x888888\n        });\n    }\n    const mesh = new THREE.Mesh(geometry, material);\n    mesh.position.copy(data.position);\n    mesh.rotation.y = Math.PI - data.heading * Math.PI / 180;\n    return mesh;\n  };\n\n  // 更新交通参与者的 3D 模型\n  const updateParticipantMesh = (mesh, data) => {\n    // 使用 TWEEN 创建平滑过渡\n    new TWEEN.Tween(mesh.position).to(data.position, 100).easing(TWEEN.Easing.Linear.None).start();\n    new TWEEN.Tween(mesh.rotation).to({\n      y: Math.PI - data.heading * Math.PI / 180\n    }, 100).easing(TWEEN.Easing.Linear.None).start();\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    const scene = new THREE.Scene();\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n        scene.add(model);\n\n        // 在校园模型加载完成后初始化场景\n        await initializeScene();\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 15, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      controls.update();\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 清理函数\n    return () => {\n      var _containerRef$current;\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n      }\n      window.removeEventListener('resize', handleResize);\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.removeChild(renderer.domElement);\n      renderer.dispose();\n      // 清理所有参与者模型\n      participantMeshes.forEach(mesh => {\n        scene.remove(mesh);\n        mesh.geometry.dispose();\n        mesh.material.dispose();\n      });\n      participantMeshes.clear();\n      trafficParticipants.clear();\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 693,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 695,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 705,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 694,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"pok6xuZ9wkLpUfjRdkdxrFsX29M=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "trafficParticipants", "Map", "participant<PERSON><PERSON><PERSON>", "MQTT_CONFIG", "wsUrl", "username", "password", "clientId", "topics", "BASE_URL", "CampusModel", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "clone", "currentUp", "up", "Tween", "to", "x", "y", "z", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "Math", "PI", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "initMqttClient", "ws", "WebSocket", "onopen", "send", "JSON", "stringify", "type", "onmessage", "event", "data", "topic", "message", "parse", "handleRSMMessage", "handleBSMMessage", "error", "onerror", "bsmData", "newState", "parseFloat", "partLong", "partLat", "partSpeed", "partHeading", "modelPos", "wgs84ToModel", "newPosition", "Vector3", "rotation", "updateMatrix", "updateMatrixWorld", "_rsmData$participants", "rsmData", "设备ID", "rsuId", "基准点", "经度", "posLong", "纬度", "posLat", "参与者数量", "participants", "length", "Array", "isArray", "for<PERSON>ach", "participant", "updateTrafficParticipant", "id", "partPtcId", "parseInt", "partPtcType", "partPosLong", "partPosLat", "participantData", "size", "partLength", "width", "partWidth", "height", "partHeight", "lastUpdate", "Date", "now", "has", "mesh", "createParticipantMesh", "scene", "add", "set", "get", "updateParticipantMesh", "geometry", "material", "BoxGeometry", "MeshPhongMaterial", "color", "CylinderGeometry", "<PERSON><PERSON>", "Linear", "None", "Scene", "camera", "PerspectiveCamera", "window", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "traverse", "child", "<PERSON><PERSON><PERSON>", "newMaterial", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "map", "name", "children", "xhr", "loaded", "total", "toFixed", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "setTimeout", "model", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "toArray", "相机位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "_containerRef$current", "clearInterval", "close", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "remove", "clear", "ref", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\nlet trafficParticipants = new Map();  // 使用 Map 存储所有交通参与者\nlet participantMeshes = new Map();    // 存储交通参与者的 3D 模型\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  wsUrl: 'ws://localhost:8083/mqtt',  // 添加路径\n  username: 'cloud',\n  password: 'A825FBCBB97D1975',\n  clientId: 'V2X_CENTER_CSLG_opentest',\n  topics: [\n    'changli/cloud/v2x/rsu/rsm',\n    'changli/cloud/v2x/obu/bsm'\n  ]\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改初始化 MQTT 连接\n  const initMqttClient = () => {\n    console.log('正在连接WebSocket...');\n    try {\n      const ws = new WebSocket(MQTT_CONFIG.wsUrl);\n      \n      // 添加更多连接状态日志\n      ws.onopen = () => {\n        console.log('WebSocket连接成功');\n        // 发送认证信息\n        ws.send(JSON.stringify({\n          type: 'auth',\n          clientId: MQTT_CONFIG.clientId,\n          username: MQTT_CONFIG.username,\n          password: MQTT_CONFIG.password\n        }));\n      };\n\n      ws.onmessage = (event) => {\n        console.log('收到WebSocket消息:', event.data);\n        try {\n          const { topic, message } = JSON.parse(event.data);\n          const data = JSON.parse(message);\n          \n          // 处理消息\n          switch(topic) {\n            case 'changli/cloud/v2x/rsu/rsm':\n              handleRSMMessage(data);\n              break;\n            case 'changli/cloud/v2x/obu/bsm':\n              handleBSMMessage(data);\n              break;\n          }\n        } catch (error) {\n          console.error('处理消息失败:', error);\n        }\n      };\n      \n      ws.onerror = (error) => {\n        console.error('WebSocket错误:', error.message);\n      };\n      \n      mqttClientRef.current = ws;\n    } catch (error) {\n      console.error('创建WebSocket连接失败:', error);\n    }\n  };\n\n  // 处理 BSM 消息\n  const handleBSMMessage = (data) => {\n    if (data.type === 'BSM' && data.data) {\n      const bsmData = data.data;\n      const newState = {\n        longitude: parseFloat(bsmData.partLong),\n        latitude: parseFloat(bsmData.partLat),\n        speed: parseFloat(bsmData.partSpeed),\n        heading: parseFloat(bsmData.partHeading)\n      };\n      \n      console.log('解析后的车辆状态:', newState);\n      \n      // 更新车辆位置和状态\n      if (globalVehicleRef) {\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        \n        globalVehicleRef.position.copy(newPosition);\n        globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n        globalVehicleRef.updateMatrix();\n        globalVehicleRef.updateMatrixWorld(true);\n        \n        setVehicleState(newState);\n        console.log('车辆位置已更新:', newPosition);\n      }\n    }\n  };\n\n  // 处理 RSM 消息\n  const handleRSMMessage = (data) => {\n    if (data.type === 'RSM' && data.data) {\n      const rsmData = data.data;\n      console.log('收到RSM数据:', {\n        设备ID: rsmData.rsuId,\n        基准点: {\n          经度: rsmData.posLong,\n          纬度: rsmData.posLat\n        },\n        参与者数量: rsmData.participants?.length || 0\n      });\n\n      // 处理交通参与者\n      if (rsmData.participants && Array.isArray(rsmData.participants)) {\n        rsmData.participants.forEach(participant => {\n          updateTrafficParticipant(participant);\n        });\n      }\n    }\n  };\n\n  // 更新交通参与者\n  const updateTrafficParticipant = (participant) => {\n    const id = participant.partPtcId;\n    const type = parseInt(participant.partPtcType);\n    \n    // 转换坐标\n    const modelPos = converter.current.wgs84ToModel(\n      parseFloat(participant.partPosLong),\n      parseFloat(participant.partPosLat)\n    );\n    \n    // 创建或更新参与者数据\n    const participantData = {\n      position: new THREE.Vector3(modelPos.x, 0.5, -modelPos.y),\n      heading: parseFloat(participant.partHeading),\n      speed: parseFloat(participant.partSpeed),\n      type: type,\n      size: {\n        length: parseFloat(participant.partLength) / 100,  // 转换为米\n        width: parseFloat(participant.partWidth) / 100,\n        height: parseFloat(participant.partHeight) / 100\n      },\n      lastUpdate: Date.now()\n    };\n    \n    // 更新或创建 3D 模型\n    if (!participantMeshes.has(id)) {\n      // 创建新的 3D 模型\n      const mesh = createParticipantMesh(participantData);\n      if (mesh) {\n        scene.add(mesh);\n        participantMeshes.set(id, mesh);\n      }\n    } else {\n      // 更新现有模型\n      const mesh = participantMeshes.get(id);\n      updateParticipantMesh(mesh, participantData);\n    }\n    \n    // 更新数据存储\n    trafficParticipants.set(id, participantData);\n  };\n\n  // 创建交通参与者的 3D 模型\n  const createParticipantMesh = (data) => {\n    let geometry, material;\n    \n    switch (data.type) {\n      case 1:  // 机动车\n        geometry = new THREE.BoxGeometry(data.size.length, data.size.height, data.size.width);\n        material = new THREE.MeshPhongMaterial({ color: 0x4444ff });\n        break;\n      case 2:  // 非机动车\n        geometry = new THREE.BoxGeometry(data.size.length, data.size.height, data.size.width);\n        material = new THREE.MeshPhongMaterial({ color: 0x44ff44 });\n        break;\n      case 3:  // 行人\n        geometry = new THREE.CylinderGeometry(0.3, 0.3, data.size.height, 8);\n        material = new THREE.MeshPhongMaterial({ color: 0xff4444 });\n        break;\n      default:  // 未知类型\n        geometry = new THREE.BoxGeometry(1, 1, 1);\n        material = new THREE.MeshPhongMaterial({ color: 0x888888 });\n    }\n    \n    const mesh = new THREE.Mesh(geometry, material);\n    mesh.position.copy(data.position);\n    mesh.rotation.y = Math.PI - data.heading * Math.PI / 180;\n    \n    return mesh;\n  };\n\n  // 更新交通参与者的 3D 模型\n  const updateParticipantMesh = (mesh, data) => {\n    // 使用 TWEEN 创建平滑过渡\n    new TWEEN.Tween(mesh.position)\n      .to(data.position, 100)\n      .easing(TWEEN.Easing.Linear.None)\n      .start();\n    \n    new TWEEN.Tween(mesh.rotation)\n      .to({ y: Math.PI - data.heading * Math.PI / 180 }, 100)\n      .easing(TWEEN.Easing.Linear.None)\n      .start();\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 创建场景\n    const scene = new THREE.Scene();\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          15,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      \n      controls.update();\n      renderer.render(scene, camera);\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 清理函数\n    return () => {\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n      }\n      window.removeEventListener('resize', handleResize);\n      containerRef.current?.removeChild(renderer.domElement);\n      renderer.dispose();\n      // 清理所有参与者模型\n      participantMeshes.forEach(mesh => {\n        scene.remove(mesh);\n        mesh.geometry.dispose();\n        mesh.material.dispose();\n      });\n      participantMeshes.clear();\n      trafficParticipants.clear();\n    };\n  }, []);\n\n  return (\n    <>\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;AACrB,IAAIC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAE;AACtC,IAAIC,iBAAiB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAI;;AAEtC;AACA,MAAME,WAAW,GAAG;EAClBC,KAAK,EAAE,0BAA0B;EAAG;EACpCC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE,0BAA0B;EACpCC,MAAM,EAAE,CACN,2BAA2B,EAC3B,2BAA2B;AAE/B,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;AAExC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAGjC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMkC,UAAU,GAAGlC,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMmC,SAAS,GAAGnC,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAM+B,aAAa,GAAGpC,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMqC,eAAe,GAAGrC,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMsC,aAAa,GAAGtC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC;IAC/C0C,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMgD,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGlE,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMmE,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnB,WAAW,CAAC,QAAQ,CAAC;IACrB7B,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACgD,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrB,WAAW,CAAC,QAAQ,CAAC;IACrB7B,UAAU,GAAG,QAAQ;IAErB,IAAI+C,SAAS,CAACI,OAAO,IAAIlD,QAAQ,EAAE;MACjC;MACA,MAAMmD,UAAU,GAAGL,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACsB,KAAK,CAAC,CAAC;MACrD,MAAMC,SAAS,GAAGP,SAAS,CAACI,OAAO,CAACI,EAAE,CAACF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAIlE,KAAK,CAACqE,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCC,MAAM,CAAC1E,KAAK,CAAC2E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdlB,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACmC,IAAI,CAACd,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDe,KAAK,CAAC,CAAC;;MAEV;MACA,IAAIhF,KAAK,CAACqE,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC1E,KAAK,CAAC2E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdlB,SAAS,CAACI,OAAO,CAACI,EAAE,CAACW,IAAI,CAACZ,SAAS,CAAC;MACtC,CAAC,CAAC,CACDa,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAGnE,QAAQ,CAACoE,MAAM,CAAChB,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAIlE,KAAK,CAACqE,KAAK,CAACY,aAAa,CAAC,CAC3BX,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC1E,KAAK,CAAC2E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdhE,QAAQ,CAACoE,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACArB,SAAS,CAACI,OAAO,CAACmB,MAAM,CAACrE,QAAQ,CAACoE,MAAM,CAAC;QACzCpE,QAAQ,CAACsE,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACAlE,QAAQ,CAACgD,OAAO,GAAG,IAAI;;MAEvB;MACAhD,QAAQ,CAACuE,WAAW,GAAG,EAAE;MACzBvE,QAAQ,CAACwE,WAAW,GAAG,GAAG;MAC1BxE,QAAQ,CAACyE,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;MACtC3E,QAAQ,CAAC4E,aAAa,GAAG,CAAC;MAC1B5E,QAAQ,CAACsE,MAAM,CAAC,CAAC;MAEjBO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BL,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B,IAAI;MACF,MAAMK,EAAE,GAAG,IAAIC,SAAS,CAAChF,WAAW,CAACC,KAAK,CAAC;;MAE3C;MACA8E,EAAE,CAACE,MAAM,GAAG,MAAM;QAChBR,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;QAC5B;QACAK,EAAE,CAACG,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;UACrBC,IAAI,EAAE,MAAM;UACZjF,QAAQ,EAAEJ,WAAW,CAACI,QAAQ;UAC9BF,QAAQ,EAAEF,WAAW,CAACE,QAAQ;UAC9BC,QAAQ,EAAEH,WAAW,CAACG;QACxB,CAAC,CAAC,CAAC;MACL,CAAC;MAED4E,EAAE,CAACO,SAAS,GAAIC,KAAK,IAAK;QACxBd,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEa,KAAK,CAACC,IAAI,CAAC;QACzC,IAAI;UACF,MAAM;YAAEC,KAAK;YAAEC;UAAQ,CAAC,GAAGP,IAAI,CAACQ,KAAK,CAACJ,KAAK,CAACC,IAAI,CAAC;UACjD,MAAMA,IAAI,GAAGL,IAAI,CAACQ,KAAK,CAACD,OAAO,CAAC;;UAEhC;UACA,QAAOD,KAAK;YACV,KAAK,2BAA2B;cAC9BG,gBAAgB,CAACJ,IAAI,CAAC;cACtB;YACF,KAAK,2BAA2B;cAC9BK,gBAAgB,CAACL,IAAI,CAAC;cACtB;UACJ;QACF,CAAC,CAAC,OAAOM,KAAK,EAAE;UACdrB,OAAO,CAACqB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QACjC;MACF,CAAC;MAEDf,EAAE,CAACgB,OAAO,GAAID,KAAK,IAAK;QACtBrB,OAAO,CAACqB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACJ,OAAO,CAAC;MAC9C,CAAC;MAED5E,aAAa,CAACgC,OAAO,GAAGiC,EAAE;IAC5B,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdrB,OAAO,CAACqB,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAMD,gBAAgB,GAAIL,IAAI,IAAK;IACjC,IAAIA,IAAI,CAACH,IAAI,KAAK,KAAK,IAAIG,IAAI,CAACA,IAAI,EAAE;MACpC,MAAMQ,OAAO,GAAGR,IAAI,CAACA,IAAI;MACzB,MAAMS,QAAQ,GAAG;QACf9E,SAAS,EAAE+E,UAAU,CAACF,OAAO,CAACG,QAAQ,CAAC;QACvC/E,QAAQ,EAAE8E,UAAU,CAACF,OAAO,CAACI,OAAO,CAAC;QACrC/E,KAAK,EAAE6E,UAAU,CAACF,OAAO,CAACK,SAAS,CAAC;QACpC/E,OAAO,EAAE4E,UAAU,CAACF,OAAO,CAACM,WAAW;MACzC,CAAC;MAED7B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEuB,QAAQ,CAAC;;MAElC;MACA,IAAI7G,gBAAgB,EAAE;QACpB,MAAMmH,QAAQ,GAAG5F,SAAS,CAACmC,OAAO,CAAC0D,YAAY,CAACP,QAAQ,CAAC9E,SAAS,EAAE8E,QAAQ,CAAC7E,QAAQ,CAAC;QACtF,MAAMqF,WAAW,GAAG,IAAI/H,KAAK,CAACgI,OAAO,CAACH,QAAQ,CAAClD,CAAC,EAAE,GAAG,EAAE,CAACkD,QAAQ,CAACjD,CAAC,CAAC;QAEnElE,gBAAgB,CAACsC,QAAQ,CAACmC,IAAI,CAAC4C,WAAW,CAAC;QAC3CrH,gBAAgB,CAACuH,QAAQ,CAACrD,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAG0B,QAAQ,CAAC3E,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAG;QACxEnF,gBAAgB,CAACwH,YAAY,CAAC,CAAC;QAC/BxH,gBAAgB,CAACyH,iBAAiB,CAAC,IAAI,CAAC;QAExC3F,eAAe,CAAC+E,QAAQ,CAAC;QACzBxB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE+B,WAAW,CAAC;MACtC;IACF;EACF,CAAC;;EAED;EACA,MAAMb,gBAAgB,GAAIJ,IAAI,IAAK;IACjC,IAAIA,IAAI,CAACH,IAAI,KAAK,KAAK,IAAIG,IAAI,CAACA,IAAI,EAAE;MAAA,IAAAsB,qBAAA;MACpC,MAAMC,OAAO,GAAGvB,IAAI,CAACA,IAAI;MACzBf,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;QACtBsC,IAAI,EAAED,OAAO,CAACE,KAAK;QACnBC,GAAG,EAAE;UACHC,EAAE,EAAEJ,OAAO,CAACK,OAAO;UACnBC,EAAE,EAAEN,OAAO,CAACO;QACd,CAAC;QACDC,KAAK,EAAE,EAAAT,qBAAA,GAAAC,OAAO,CAACS,YAAY,cAAAV,qBAAA,uBAApBA,qBAAA,CAAsBW,MAAM,KAAI;MACzC,CAAC,CAAC;;MAEF;MACA,IAAIV,OAAO,CAACS,YAAY,IAAIE,KAAK,CAACC,OAAO,CAACZ,OAAO,CAACS,YAAY,CAAC,EAAE;QAC/DT,OAAO,CAACS,YAAY,CAACI,OAAO,CAACC,WAAW,IAAI;UAC1CC,wBAAwB,CAACD,WAAW,CAAC;QACvC,CAAC,CAAC;MACJ;IACF;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAID,WAAW,IAAK;IAChD,MAAME,EAAE,GAAGF,WAAW,CAACG,SAAS;IAChC,MAAM3C,IAAI,GAAG4C,QAAQ,CAACJ,WAAW,CAACK,WAAW,CAAC;;IAE9C;IACA,MAAM3B,QAAQ,GAAG5F,SAAS,CAACmC,OAAO,CAAC0D,YAAY,CAC7CN,UAAU,CAAC2B,WAAW,CAACM,WAAW,CAAC,EACnCjC,UAAU,CAAC2B,WAAW,CAACO,UAAU,CACnC,CAAC;;IAED;IACA,MAAMC,eAAe,GAAG;MACtB3G,QAAQ,EAAE,IAAIhD,KAAK,CAACgI,OAAO,CAACH,QAAQ,CAAClD,CAAC,EAAE,GAAG,EAAE,CAACkD,QAAQ,CAACjD,CAAC,CAAC;MACzDhC,OAAO,EAAE4E,UAAU,CAAC2B,WAAW,CAACvB,WAAW,CAAC;MAC5CjF,KAAK,EAAE6E,UAAU,CAAC2B,WAAW,CAACxB,SAAS,CAAC;MACxChB,IAAI,EAAEA,IAAI;MACViD,IAAI,EAAE;QACJb,MAAM,EAAEvB,UAAU,CAAC2B,WAAW,CAACU,UAAU,CAAC,GAAG,GAAG;QAAG;QACnDC,KAAK,EAAEtC,UAAU,CAAC2B,WAAW,CAACY,SAAS,CAAC,GAAG,GAAG;QAC9CC,MAAM,EAAExC,UAAU,CAAC2B,WAAW,CAACc,UAAU,CAAC,GAAG;MAC/C,CAAC;MACDC,UAAU,EAAEC,IAAI,CAACC,GAAG,CAAC;IACvB,CAAC;;IAED;IACA,IAAI,CAAC/I,iBAAiB,CAACgJ,GAAG,CAAChB,EAAE,CAAC,EAAE;MAC9B;MACA,MAAMiB,IAAI,GAAGC,qBAAqB,CAACZ,eAAe,CAAC;MACnD,IAAIW,IAAI,EAAE;QACRE,KAAK,CAACC,GAAG,CAACH,IAAI,CAAC;QACfjJ,iBAAiB,CAACqJ,GAAG,CAACrB,EAAE,EAAEiB,IAAI,CAAC;MACjC;IACF,CAAC,MAAM;MACL;MACA,MAAMA,IAAI,GAAGjJ,iBAAiB,CAACsJ,GAAG,CAACtB,EAAE,CAAC;MACtCuB,qBAAqB,CAACN,IAAI,EAAEX,eAAe,CAAC;IAC9C;;IAEA;IACAxI,mBAAmB,CAACuJ,GAAG,CAACrB,EAAE,EAAEM,eAAe,CAAC;EAC9C,CAAC;;EAED;EACA,MAAMY,qBAAqB,GAAIzD,IAAI,IAAK;IACtC,IAAI+D,QAAQ,EAAEC,QAAQ;IAEtB,QAAQhE,IAAI,CAACH,IAAI;MACf,KAAK,CAAC;QAAG;QACPkE,QAAQ,GAAG,IAAI7K,KAAK,CAAC+K,WAAW,CAACjE,IAAI,CAAC8C,IAAI,CAACb,MAAM,EAAEjC,IAAI,CAAC8C,IAAI,CAACI,MAAM,EAAElD,IAAI,CAAC8C,IAAI,CAACE,KAAK,CAAC;QACrFgB,QAAQ,GAAG,IAAI9K,KAAK,CAACgL,iBAAiB,CAAC;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QAC3D;MACF,KAAK,CAAC;QAAG;QACPJ,QAAQ,GAAG,IAAI7K,KAAK,CAAC+K,WAAW,CAACjE,IAAI,CAAC8C,IAAI,CAACb,MAAM,EAAEjC,IAAI,CAAC8C,IAAI,CAACI,MAAM,EAAElD,IAAI,CAAC8C,IAAI,CAACE,KAAK,CAAC;QACrFgB,QAAQ,GAAG,IAAI9K,KAAK,CAACgL,iBAAiB,CAAC;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QAC3D;MACF,KAAK,CAAC;QAAG;QACPJ,QAAQ,GAAG,IAAI7K,KAAK,CAACkL,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAEpE,IAAI,CAAC8C,IAAI,CAACI,MAAM,EAAE,CAAC,CAAC;QACpEc,QAAQ,GAAG,IAAI9K,KAAK,CAACgL,iBAAiB,CAAC;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;QAC3D;MACF;QAAU;QACRJ,QAAQ,GAAG,IAAI7K,KAAK,CAAC+K,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACzCD,QAAQ,GAAG,IAAI9K,KAAK,CAACgL,iBAAiB,CAAC;UAAEC,KAAK,EAAE;QAAS,CAAC,CAAC;IAC/D;IAEA,MAAMX,IAAI,GAAG,IAAItK,KAAK,CAACmL,IAAI,CAACN,QAAQ,EAAEC,QAAQ,CAAC;IAC/CR,IAAI,CAACtH,QAAQ,CAACmC,IAAI,CAAC2B,IAAI,CAAC9D,QAAQ,CAAC;IACjCsH,IAAI,CAACrC,QAAQ,CAACrD,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGiB,IAAI,CAAClE,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAG;IAExD,OAAOyE,IAAI;EACb,CAAC;;EAED;EACA,MAAMM,qBAAqB,GAAGA,CAACN,IAAI,EAAExD,IAAI,KAAK;IAC5C;IACA,IAAI1G,KAAK,CAACqE,KAAK,CAAC6F,IAAI,CAACtH,QAAQ,CAAC,CAC3B0B,EAAE,CAACoC,IAAI,CAAC9D,QAAQ,EAAE,GAAG,CAAC,CACtB8B,MAAM,CAAC1E,KAAK,CAAC2E,MAAM,CAACqG,MAAM,CAACC,IAAI,CAAC,CAChCjG,KAAK,CAAC,CAAC;IAEV,IAAIhF,KAAK,CAACqE,KAAK,CAAC6F,IAAI,CAACrC,QAAQ,CAAC,CAC3BvD,EAAE,CAAC;MAAEE,CAAC,EAAEgB,IAAI,CAACC,EAAE,GAAGiB,IAAI,CAAClE,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG;IAAI,CAAC,EAAE,GAAG,CAAC,CACtDf,MAAM,CAAC1E,KAAK,CAAC2E,MAAM,CAACqG,MAAM,CAACC,IAAI,CAAC,CAChCjG,KAAK,CAAC,CAAC;EACZ,CAAC;EAEDvF,SAAS,CAAC,MAAM;IACd,IAAI,CAACkC,YAAY,CAACqC,OAAO,EAAE;;IAE3B;IACA,MAAMoG,KAAK,GAAG,IAAIxK,KAAK,CAACsL,KAAK,CAAC,CAAC;;IAE/B;IACA,MAAMC,MAAM,GAAG,IAAIvL,KAAK,CAACwL,iBAAiB,CACxC,EAAE,EACFC,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACDJ,MAAM,CAACvI,QAAQ,CAAC0H,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCa,MAAM,CAAChG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBvB,SAAS,CAACI,OAAO,GAAGmH,MAAM;;IAE1B;IACA,MAAMK,QAAQ,GAAG,IAAI5L,KAAK,CAAC6L,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAACN,MAAM,CAACC,UAAU,EAAED,MAAM,CAACE,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAACR,MAAM,CAACS,gBAAgB,CAAC;IAC/CnK,YAAY,CAACqC,OAAO,CAAC+H,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAIrM,KAAK,CAACsM,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5D9B,KAAK,CAACC,GAAG,CAAC4B,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAIvM,KAAK,CAACwM,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAACvJ,QAAQ,CAAC0H,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1CF,KAAK,CAACC,GAAG,CAAC8B,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAIzM,KAAK,CAACwM,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAACzJ,QAAQ,CAAC0H,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3CF,KAAK,CAACC,GAAG,CAACgC,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAI1M,KAAK,CAAC2M,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAAC1J,QAAQ,CAAC0H,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCgC,SAAS,CAACE,KAAK,GAAGhH,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7B6G,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxBvC,KAAK,CAACC,GAAG,CAACiC,SAAS,CAAC;;IAEpB;IACAxL,QAAQ,GAAG,IAAIhB,aAAa,CAACqL,MAAM,EAAEK,QAAQ,CAACQ,UAAU,CAAC;IACzDlL,QAAQ,CAAC8L,aAAa,GAAG,IAAI;IAC7B9L,QAAQ,CAAC+L,aAAa,GAAG,IAAI;IAC7B/L,QAAQ,CAACgM,kBAAkB,GAAG,KAAK;IACnChM,QAAQ,CAACuE,WAAW,GAAG,EAAE;IACzBvE,QAAQ,CAACwE,WAAW,GAAG,GAAG;IAC1BxE,QAAQ,CAACyE,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC3E,QAAQ,CAAC4E,aAAa,GAAG,CAAC;IAC1B5E,QAAQ,CAACoE,MAAM,CAACoF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BxJ,QAAQ,CAACsE,MAAM,CAAC,CAAC;;IAEjB;IACAO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBuF,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBrK,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpB8C,SAAS,EAAE,CAAC,CAACA,SAAS,CAACI;IACzB,CAAC,CAAC;;IAEF;IACA,MAAM+I,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAItN,UAAU,CAAC,CAAC;QACtCsN,aAAa,CAACC,IAAI,CAChB,GAAG5L,QAAQ,uBAAuB,EACjC6L,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAACjD,KAAK;;UAE/B;UACA,MAAMmD,gBAAgB,GAAG,IAAI3N,KAAK,CAAC4N,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACG,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAAChD,QAAQ,EAAE;gBAClB;gBACA,MAAMkD,WAAW,GAAG,IAAIhO,KAAK,CAACiO,oBAAoB,CAAC;kBACjDhD,KAAK,EAAE,QAAQ;kBAAO;kBACtBiD,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIN,KAAK,CAAChD,QAAQ,CAACuD,GAAG,EAAE;kBACtBL,WAAW,CAACK,GAAG,GAAGP,KAAK,CAAChD,QAAQ,CAACuD,GAAG;gBACtC;;gBAEA;gBACAP,KAAK,CAAChD,QAAQ,GAAGkD,WAAW;gBAE5BjI,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE8H,KAAK,CAACQ,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMZ,YAAY,CAACa,QAAQ,CAACxF,MAAM,GAAG,CAAC,EAAE;YACtC,MAAM+E,KAAK,GAAGJ,YAAY,CAACa,QAAQ,CAAC,CAAC,CAAC;YACtCZ,gBAAgB,CAAClD,GAAG,CAACqD,KAAK,CAAC;UAC7B;;UAEA;UACAtD,KAAK,CAACC,GAAG,CAACkD,gBAAgB,CAAC;;UAE3B;UACAjN,gBAAgB,GAAGiN,gBAAgB;UAEnC5H,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9B1D,kBAAkB,CAAC,IAAI,CAAC;UACxB+K,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAa,GAAG,IAAK;UACPzI,OAAO,CAACC,GAAG,CAAC,aAAa,CAACwI,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDrB,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMsB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMjB,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACA/G,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAIuH,gBAAgB,EAAE;UACpB,MAAMkB,YAAY,GAAG;YACnBpM,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UACD,MAAMkM,UAAU,GAAG7M,SAAS,CAACmC,OAAO,CAAC0D,YAAY,CAAC+G,YAAY,CAACpM,SAAS,EAAEoM,YAAY,CAACnM,QAAQ,CAAC;UAChGiL,gBAAgB,CAAC3K,QAAQ,CAAC0H,GAAG,CAACoE,UAAU,CAACnK,CAAC,EAAE,GAAG,EAAE,CAACmK,UAAU,CAAClK,CAAC,CAAC;UAC/D+I,gBAAgB,CAAC1F,QAAQ,CAACrD,CAAC,GAAIgB,IAAI,CAACC,EAAE,GAAGgJ,YAAY,CAACjM,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9E8H,gBAAgB,CAACzF,YAAY,CAAC,CAAC;UAC/ByF,gBAAgB,CAACxF,iBAAiB,CAAC,IAAI,CAAC;UACxCpH,eAAe,GAAG4M,gBAAgB,CAAC3K,QAAQ,CAACsB,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAO8C,KAAK,EAAE;QACdrB,OAAO,CAACqB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAM2H,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAI7B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAM4B,WAAW,GAAIC,WAAW,IAAK;UACnCpJ,OAAO,CAACC,GAAG,CAAC,WAAWgJ,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAInP,UAAU,CAAC,CAAC;UAC/BmP,MAAM,CAAC5B,IAAI,CACTwB,GAAG,EACFvB,IAAI,IAAK;YACR1H,OAAO,CAACC,GAAG,CAAC,WAAWgJ,GAAG,EAAE,CAAC;YAC7B3B,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAe,GAAG,IAAK;YACPzI,OAAO,CAACC,GAAG,CAAC,SAAS,CAACwI,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAvH,KAAK,IAAK;YACTrB,OAAO,CAACqB,KAAK,CAAC,SAAS4H,GAAG,EAAE,EAAE5H,KAAK,CAAC;YACpC,IAAI+H,WAAW,GAAG,CAAC,EAAE;cACnBpJ,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3BqJ,UAAU,CAAC,MAAMH,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACL7B,MAAM,CAAClG,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAED8H,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAInP,UAAU,CAAC,CAAC;IAC/BmP,MAAM,CAAC5B,IAAI,CACT,GAAG5L,QAAQ,4BAA4B,EACvC,MAAO6L,IAAI,IAAK;MACd,IAAI;QACF,MAAM6B,KAAK,GAAG7B,IAAI,CAACjD,KAAK;QACxB8E,KAAK,CAACC,KAAK,CAAC7E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxB4E,KAAK,CAACtM,QAAQ,CAAC0H,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3BF,KAAK,CAACC,GAAG,CAAC6E,KAAK,CAAC;;QAEhB;QACA,MAAMV,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC,OAAOxH,KAAK,EAAE;QACdrB,OAAO,CAACqB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACAoH,GAAG,IAAK;MACPzI,OAAO,CAACC,GAAG,CAAC,SAAS,CAACwI,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAvH,KAAK,IAAK;MACTrB,OAAO,CAACqB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BrB,OAAO,CAACqB,KAAK,CAAC,OAAO,EAAE;QACrBoI,IAAI,EAAEpI,KAAK,CAACT,IAAI;QAChB8I,IAAI,EAAErI,KAAK,CAACJ,OAAO;QACnB0I,KAAK,EAAE,GAAG9N,QAAQ,4BAA4B;QAC9C+N,KAAK,EAAE,GAAG/N,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMgO,OAAO,GAAGA,CAAA,KAAM;MACpBC,qBAAqB,CAACD,OAAO,CAAC;;MAE9B;MACAxP,KAAK,CAACoF,MAAM,CAAC,CAAC;MAEd,IAAIvE,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACgD,OAAO,GAAG,KAAK;;QAExB;QACA,MAAM4L,UAAU,GAAGpP,gBAAgB,CAACsC,QAAQ,CAACsB,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMyL,eAAe,GAAGrP,gBAAgB,CAACuH,QAAQ,CAACrD,CAAC;;QAEnD;QACA;QACA,MAAMoL,gBAAgB,GAAG,EAAED,eAAe,GAAGnK,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAMoK,YAAY,GAAG,IAAIjQ,KAAK,CAACgI,OAAO,CACpC,CAAC,EAAE,GAAGpC,IAAI,CAACsK,GAAG,CAACF,gBAAgB,CAAC,EAChC,EAAE,EACF,CAAC,EAAE,GAAGpK,IAAI,CAACuK,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACAzE,MAAM,CAACvI,QAAQ,CAACmC,IAAI,CAAC2K,UAAU,CAAC,CAACrF,GAAG,CAACwF,YAAY,CAAC;;QAElD;QACA1E,MAAM,CAAC/G,EAAE,CAACkG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAM0F,YAAY,GAAGN,UAAU,CAACxL,KAAK,CAAC,CAAC;QACvCiH,MAAM,CAAChG,MAAM,CAAC6K,YAAY,CAAC;;QAE3B;QACA7E,MAAM,CAAC8E,sBAAsB,CAAC,CAAC;QAC/B9E,MAAM,CAACrD,YAAY,CAAC,CAAC;QACrBqD,MAAM,CAACpD,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACAjH,QAAQ,CAACgD,OAAO,GAAG,KAAK;;QAExB;QACAhD,QAAQ,CAACoE,MAAM,CAACH,IAAI,CAAC2K,UAAU,CAAC;QAChC5O,QAAQ,CAACsE,MAAM,CAAC,CAAC;QAEjBO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnBsK,IAAI,EAAER,UAAU,CAACS,OAAO,CAAC,CAAC;UAC1BC,IAAI,EAAEjF,MAAM,CAACvI,QAAQ,CAACuN,OAAO,CAAC,CAAC;UAC/BE,IAAI,EAAEL,YAAY,CAACG,OAAO,CAAC,CAAC;UAC5BG,IAAI,EAAEnF,MAAM,CAACoF,iBAAiB,CAAC,IAAI3Q,KAAK,CAACgI,OAAO,CAAC,CAAC,CAAC,CAACuI,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAItP,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAACgD,OAAO,GAAG,IAAI;;QAEvB;QACAqH,MAAM,CAAC/G,EAAE,CAACkG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAI9E,IAAI,CAACgL,GAAG,CAACrF,MAAM,CAACvI,QAAQ,CAAC4B,CAAC,CAAC,GAAG,EAAE,EAAE;UACpC2G,MAAM,CAACvI,QAAQ,CAAC0H,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BxJ,QAAQ,CAACoE,MAAM,CAACoF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5Ba,MAAM,CAAChG,MAAM,CAACrE,QAAQ,CAACoE,MAAM,CAAC;UAC9BpE,QAAQ,CAACsE,MAAM,CAAC,CAAC;QACnB;MACF;MAEAtE,QAAQ,CAACsE,MAAM,CAAC,CAAC;MACjBoG,QAAQ,CAACiF,MAAM,CAACrG,KAAK,EAAEe,MAAM,CAAC;IAChC,CAAC;IAEDqE,OAAO,CAAC,CAAC;;IAET;IACA,MAAMkB,YAAY,GAAGA,CAAA,KAAM;MACzBvF,MAAM,CAACwF,MAAM,GAAGtF,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,WAAW;MACtDJ,MAAM,CAAC8E,sBAAsB,CAAC,CAAC;MAC/BzE,QAAQ,CAACG,OAAO,CAACN,MAAM,CAACC,UAAU,EAAED,MAAM,CAACE,WAAW,CAAC;IACzD,CAAC;IACDF,MAAM,CAACuF,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACArF,MAAM,CAACwF,aAAa,GAAG,MAAM;MAC3B,IAAIjN,SAAS,CAACI,OAAO,EAAE;QACrBJ,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAAC0H,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC1G,SAAS,CAACI,OAAO,CAACmB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCvB,SAAS,CAACI,OAAO,CAAC8D,YAAY,CAAC,CAAC;QAChClE,SAAS,CAACI,OAAO,CAAC+D,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAIjH,QAAQ,EAAE;UACZA,QAAQ,CAACoE,MAAM,CAACoF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BxJ,QAAQ,CAACgD,OAAO,GAAG,IAAI;UACvBhD,QAAQ,CAACsE,MAAM,CAAC,CAAC;QACnB;QAEAvE,UAAU,GAAG,QAAQ;QACrB8E,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MAAA,IAAAkL,qBAAA;MACX,IAAIrQ,oBAAoB,EAAE;QACxBsQ,aAAa,CAACtQ,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;MACA,IAAIuB,aAAa,CAACgC,OAAO,EAAE;QACzBhC,aAAa,CAACgC,OAAO,CAACgN,KAAK,CAAC,CAAC;MAC/B;MACA3F,MAAM,CAAC4F,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;MAClD,CAAAI,qBAAA,GAAAnP,YAAY,CAACqC,OAAO,cAAA8M,qBAAA,uBAApBA,qBAAA,CAAsBI,WAAW,CAAC1F,QAAQ,CAACQ,UAAU,CAAC;MACtDR,QAAQ,CAAC2F,OAAO,CAAC,CAAC;MAClB;MACAlQ,iBAAiB,CAAC6H,OAAO,CAACoB,IAAI,IAAI;QAChCE,KAAK,CAACgH,MAAM,CAAClH,IAAI,CAAC;QAClBA,IAAI,CAACO,QAAQ,CAAC0G,OAAO,CAAC,CAAC;QACvBjH,IAAI,CAACQ,QAAQ,CAACyG,OAAO,CAAC,CAAC;MACzB,CAAC,CAAC;MACFlQ,iBAAiB,CAACoQ,KAAK,CAAC,CAAC;MACzBtQ,mBAAmB,CAACsQ,KAAK,CAAC,CAAC;IAC7B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACElR,OAAA,CAAAE,SAAA;IAAA8N,QAAA,gBACEhO,OAAA;MAAKmR,GAAG,EAAE3P,YAAa;MAAC4P,KAAK,EAAE;QAAE7H,KAAK,EAAE,MAAM;QAAEE,MAAM,EAAE;MAAO;IAAE;MAAA4H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpExR,OAAA;MAAKoR,KAAK,EAAE5O,oBAAqB;MAAAwL,QAAA,gBAC/BhO,OAAA;QACEoR,KAAK,EAAE;UACL,GAAGpO,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoI,KAAK,EAAEpI,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFmP,OAAO,EAAE/N,kBAAmB;QAAAsK,QAAA,EAC7B;MAED;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxR,OAAA;QACEoR,KAAK,EAAE;UACL,GAAGpO,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EoI,KAAK,EAAEpI,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFmP,OAAO,EAAE7N,kBAAmB;QAAAoK,QAAA,EAC7B;MAED;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAAjQ,EAAA,CA3qBMD,WAAW;AAAAoQ,EAAA,GAAXpQ,WAAW;AA4qBjB,SAASqQ,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAACtI,KAAK,GAAG,GAAG;EAClBsI,MAAM,CAACpI,MAAM,GAAG,EAAE;;EAElB;EACAuI,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAACtI,KAAK,GAAC,CAAC,EAAEsI,MAAM,CAACpI,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAM6I,OAAO,GAAG,IAAI7S,KAAK,CAAC8S,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAI/S,KAAK,CAACgT,cAAc,CAAC;IAC9C3E,GAAG,EAAEwE,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAIlT,KAAK,CAACmT,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAAC3D,KAAK,CAAC7E,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAOwI,MAAM;AACf;;AAEA;AACAzH,MAAM,CAAC2H,WAAW,GAAG,CAACzO,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EAChC,IAAInE,gBAAgB,EAAE;IACpBA,gBAAgB,CAACsC,QAAQ,CAAC0H,GAAG,CAAC/F,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACtCnE,gBAAgB,CAACwH,YAAY,CAAC,CAAC;IAC/BxH,gBAAgB,CAACyH,iBAAiB,CAAC,IAAI,CAAC;IACxCpC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAACrB,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA4G,MAAM,CAAC4H,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAM9H,MAAM,GAAG8G,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAIjI,MAAM,EAAE;MACV;MACA,MAAMkI,MAAM,GAAGlI,MAAM,CAACvI,QAAQ,CAACsB,KAAK,CAAC,CAAC;;MAEtC;MACAiH,MAAM,CAACvI,QAAQ,CAAC0H,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9Ba,MAAM,CAAC/G,EAAE,CAACkG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBa,MAAM,CAAChG,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAgG,MAAM,CAACrD,YAAY,CAAC,CAAC;MACrBqD,MAAM,CAACpD,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMjH,QAAQ,GAAGmR,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIxS,QAAQ,EAAE;QACZA,QAAQ,CAACoE,MAAM,CAACoF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BxJ,QAAQ,CAACsE,MAAM,CAAC,CAAC;MACnB;MAEAO,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxB2N,GAAG,EAAEF,MAAM,CAAClD,OAAO,CAAC,CAAC;QACrBqD,GAAG,EAAErI,MAAM,CAACvI,QAAQ,CAACuN,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOsD,CAAC,EAAE;IACV9N,OAAO,CAACqB,KAAK,CAAC,YAAY,EAAEyM,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;AAED,eAAehS,WAAW;AAAC,IAAAoQ,EAAA;AAAA6B,YAAA,CAAA7B,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}