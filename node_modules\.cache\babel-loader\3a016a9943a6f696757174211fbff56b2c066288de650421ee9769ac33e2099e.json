{"ast": null, "code": "import { mergeToken } from '../../theme/internal';\nexport function initInputToken(token) {\n  return mergeToken(token, {\n    inputAffixPadding: token.paddingXXS\n  });\n}\nexport const initComponentToken = token => {\n  const {\n    controlHeight,\n    fontSize,\n    lineHeight,\n    lineWidth,\n    controlHeightSM,\n    controlHeightLG,\n    fontSizeLG,\n    lineHeightLG,\n    paddingSM,\n    controlPaddingHorizontalSM,\n    controlPaddingHorizontal,\n    colorFillAlter,\n    colorPrimaryHover,\n    colorPrimary,\n    controlOutlineWidth,\n    controlOutline,\n    colorErrorOutline,\n    colorWarningOutline,\n    colorBgContainer,\n    inputFontSize,\n    inputFontSizeLG,\n    inputFontSizeSM\n  } = token;\n  const mergedFontSize = inputFontSize || fontSize;\n  const mergedFontSizeSM = inputFontSizeSM || mergedFontSize;\n  const mergedFontSizeLG = inputFontSizeLG || fontSizeLG;\n  const paddingBlock = Math.round((controlHeight - mergedFontSize * lineHeight) / 2 * 10) / 10 - lineWidth;\n  const paddingBlockSM = Math.round((controlHeightSM - mergedFontSizeSM * lineHeight) / 2 * 10) / 10 - lineWidth;\n  const paddingBlockLG = Math.ceil((controlHeightLG - mergedFontSizeLG * lineHeightLG) / 2 * 10) / 10 - lineWidth;\n  return {\n    paddingBlock: Math.max(paddingBlock, 0),\n    paddingBlockSM: Math.max(paddingBlockSM, 0),\n    paddingBlockLG: Math.max(paddingBlockLG, 0),\n    paddingInline: paddingSM - lineWidth,\n    paddingInlineSM: controlPaddingHorizontalSM - lineWidth,\n    paddingInlineLG: controlPaddingHorizontal - lineWidth,\n    addonBg: colorFillAlter,\n    activeBorderColor: colorPrimary,\n    hoverBorderColor: colorPrimaryHover,\n    activeShadow: `0 0 0 ${controlOutlineWidth}px ${controlOutline}`,\n    errorActiveShadow: `0 0 0 ${controlOutlineWidth}px ${colorErrorOutline}`,\n    warningActiveShadow: `0 0 0 ${controlOutlineWidth}px ${colorWarningOutline}`,\n    hoverBg: colorBgContainer,\n    activeBg: colorBgContainer,\n    inputFontSize: mergedFontSize,\n    inputFontSizeLG: mergedFontSizeLG,\n    inputFontSizeSM: mergedFontSizeSM\n  };\n};", "map": {"version": 3, "names": ["mergeToken", "initInputToken", "token", "inputAffixPadding", "paddingXXS", "initComponentToken", "controlHeight", "fontSize", "lineHeight", "lineWidth", "controlHeightSM", "controlHeightLG", "fontSizeLG", "lineHeightLG", "paddingSM", "controlPaddingHorizontalSM", "controlPaddingHorizontal", "colorFillAlter", "colorPrimaryHover", "colorPrimary", "controlOutlineWidth", "controlOutline", "colorErrorOutline", "colorWarningOutline", "colorBgContainer", "inputFontSize", "inputFontSizeLG", "inputFontSizeSM", "mergedFontSize", "mergedFontSizeSM", "mergedFontSizeLG", "paddingBlock", "Math", "round", "paddingBlockSM", "paddingBlockLG", "ceil", "max", "paddingInline", "paddingInlineSM", "paddingInlineLG", "addonBg", "activeBorderColor", "hoverBorderColor", "activeShadow", "errorActiveShadow", "warningActiveShadow", "hoverBg", "activeBg"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/input/style/token.js"], "sourcesContent": ["import { mergeToken } from '../../theme/internal';\nexport function initInputToken(token) {\n  return mergeToken(token, {\n    inputAffixPadding: token.paddingXXS\n  });\n}\nexport const initComponentToken = token => {\n  const {\n    controlHeight,\n    fontSize,\n    lineHeight,\n    lineWidth,\n    controlHeightSM,\n    controlHeightLG,\n    fontSizeLG,\n    lineHeightLG,\n    paddingSM,\n    controlPaddingHorizontalSM,\n    controlPaddingHorizontal,\n    colorFillAlter,\n    colorPrimaryHover,\n    colorPrimary,\n    controlOutlineWidth,\n    controlOutline,\n    colorErrorOutline,\n    colorWarningOutline,\n    colorBgContainer,\n    inputFontSize,\n    inputFontSizeLG,\n    inputFontSizeSM\n  } = token;\n  const mergedFontSize = inputFontSize || fontSize;\n  const mergedFontSizeSM = inputFontSizeSM || mergedFontSize;\n  const mergedFontSizeLG = inputFontSizeLG || fontSizeLG;\n  const paddingBlock = Math.round((controlHeight - mergedFontSize * lineHeight) / 2 * 10) / 10 - lineWidth;\n  const paddingBlockSM = Math.round((controlHeightSM - mergedFontSizeSM * lineHeight) / 2 * 10) / 10 - lineWidth;\n  const paddingBlockLG = Math.ceil((controlHeightLG - mergedFontSizeLG * lineHeightLG) / 2 * 10) / 10 - lineWidth;\n  return {\n    paddingBlock: Math.max(paddingBlock, 0),\n    paddingBlockSM: Math.max(paddingBlockSM, 0),\n    paddingBlockLG: Math.max(paddingBlockLG, 0),\n    paddingInline: paddingSM - lineWidth,\n    paddingInlineSM: controlPaddingHorizontalSM - lineWidth,\n    paddingInlineLG: controlPaddingHorizontal - lineWidth,\n    addonBg: colorFillAlter,\n    activeBorderColor: colorPrimary,\n    hoverBorderColor: colorPrimaryHover,\n    activeShadow: `0 0 0 ${controlOutlineWidth}px ${controlOutline}`,\n    errorActiveShadow: `0 0 0 ${controlOutlineWidth}px ${colorErrorOutline}`,\n    warningActiveShadow: `0 0 0 ${controlOutlineWidth}px ${colorWarningOutline}`,\n    hoverBg: colorBgContainer,\n    activeBg: colorBgContainer,\n    inputFontSize: mergedFontSize,\n    inputFontSizeLG: mergedFontSizeLG,\n    inputFontSizeSM: mergedFontSizeSM\n  };\n};"], "mappings": "AAAA,SAASA,UAAU,QAAQ,sBAAsB;AACjD,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAOF,UAAU,CAACE,KAAK,EAAE;IACvBC,iBAAiB,EAAED,KAAK,CAACE;EAC3B,CAAC,CAAC;AACJ;AACA,OAAO,MAAMC,kBAAkB,GAAGH,KAAK,IAAI;EACzC,MAAM;IACJI,aAAa;IACbC,QAAQ;IACRC,UAAU;IACVC,SAAS;IACTC,eAAe;IACfC,eAAe;IACfC,UAAU;IACVC,YAAY;IACZC,SAAS;IACTC,0BAA0B;IAC1BC,wBAAwB;IACxBC,cAAc;IACdC,iBAAiB;IACjBC,YAAY;IACZC,mBAAmB;IACnBC,cAAc;IACdC,iBAAiB;IACjBC,mBAAmB;IACnBC,gBAAgB;IAChBC,aAAa;IACbC,eAAe;IACfC;EACF,CAAC,GAAGzB,KAAK;EACT,MAAM0B,cAAc,GAAGH,aAAa,IAAIlB,QAAQ;EAChD,MAAMsB,gBAAgB,GAAGF,eAAe,IAAIC,cAAc;EAC1D,MAAME,gBAAgB,GAAGJ,eAAe,IAAId,UAAU;EACtD,MAAMmB,YAAY,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAC3B,aAAa,GAAGsB,cAAc,GAAGpB,UAAU,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAGC,SAAS;EACxG,MAAMyB,cAAc,GAAGF,IAAI,CAACC,KAAK,CAAC,CAACvB,eAAe,GAAGmB,gBAAgB,GAAGrB,UAAU,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAGC,SAAS;EAC9G,MAAM0B,cAAc,GAAGH,IAAI,CAACI,IAAI,CAAC,CAACzB,eAAe,GAAGmB,gBAAgB,GAAGjB,YAAY,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAGJ,SAAS;EAC/G,OAAO;IACLsB,YAAY,EAAEC,IAAI,CAACK,GAAG,CAACN,YAAY,EAAE,CAAC,CAAC;IACvCG,cAAc,EAAEF,IAAI,CAACK,GAAG,CAACH,cAAc,EAAE,CAAC,CAAC;IAC3CC,cAAc,EAAEH,IAAI,CAACK,GAAG,CAACF,cAAc,EAAE,CAAC,CAAC;IAC3CG,aAAa,EAAExB,SAAS,GAAGL,SAAS;IACpC8B,eAAe,EAAExB,0BAA0B,GAAGN,SAAS;IACvD+B,eAAe,EAAExB,wBAAwB,GAAGP,SAAS;IACrDgC,OAAO,EAAExB,cAAc;IACvByB,iBAAiB,EAAEvB,YAAY;IAC/BwB,gBAAgB,EAAEzB,iBAAiB;IACnC0B,YAAY,EAAE,SAASxB,mBAAmB,MAAMC,cAAc,EAAE;IAChEwB,iBAAiB,EAAE,SAASzB,mBAAmB,MAAME,iBAAiB,EAAE;IACxEwB,mBAAmB,EAAE,SAAS1B,mBAAmB,MAAMG,mBAAmB,EAAE;IAC5EwB,OAAO,EAAEvB,gBAAgB;IACzBwB,QAAQ,EAAExB,gBAAgB;IAC1BC,aAAa,EAAEG,cAAc;IAC7BF,eAAe,EAAEI,gBAAgB;IACjCH,eAAe,EAAEE;EACnB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}