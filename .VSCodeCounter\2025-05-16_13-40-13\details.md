# Details

Date : 2025-05-16 13:40:13

Directory g:\\AI_tools\\cursor\\projects\\education web

Total : 107 files,  55284 codes, 1698 comments, 2485 blanks, all 59467 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.env](/.env) | Properties | 5 | 0 | 0 | 5 |
| [.vs/VSWorkspaceState.json](/.vs/VSWorkspaceState.json) | JSON | 6 | 0 | 0 | 6 |
| [HOW\_TO\_RUN.md](/HOW_TO_RUN.md) | Markdown | 50 | 0 | 18 | 68 |
| [README.md](/README.md) | Markdown | 129 | 0 | 23 | 152 |
| [build/asset-manifest.json](/build/asset-manifest.json) | JSON | 13 | 0 | 0 | 13 |
| [build/index.html](/build/index.html) | HTML | 1 | 0 | 0 | 1 |
| [build/manifest.json](/build/manifest.json) | JSON | 15 | 0 | 0 | 15 |
| [build/mqtt-test.html](/build/mqtt-test.html) | HTML | 63 | 0 | 9 | 72 |
| [build/static/css/main.beae109d.css](/build/static/css/main.beae109d.css) | CSS | 1 | 1 | 0 | 2 |
| [build/static/js/main.4c7fc37b.js](/build/static/js/main.4c7fc37b.js) | JavaScript | 420 | 65 | 32 | 517 |
| [docs/requirements.md](/docs/requirements.md) | Markdown | 140 | 0 | 26 | 166 |
| [frontend-server.js](/frontend-server.js) | JavaScript | 11 | 3 | 3 | 17 |
| [intelligent-traffic-platform/README.md](/intelligent-traffic-platform/README.md) | Markdown | 38 | 0 | 33 | 71 |
| [intelligent-traffic-platform/package-lock.json](/intelligent-traffic-platform/package-lock.json) | JSON | 20,332 | 0 | 1 | 20,333 |
| [intelligent-traffic-platform/package.json](/intelligent-traffic-platform/package.json) | JSON | 54 | 0 | 1 | 55 |
| [intelligent-traffic-platform/public/index.html](/intelligent-traffic-platform/public/index.html) | HTML | 20 | 23 | 1 | 44 |
| [intelligent-traffic-platform/public/manifest.json](/intelligent-traffic-platform/public/manifest.json) | JSON | 25 | 0 | 1 | 26 |
| [intelligent-traffic-platform/src/App.css](/intelligent-traffic-platform/src/App.css) | CSS | 33 | 0 | 6 | 39 |
| [intelligent-traffic-platform/src/App.js](/intelligent-traffic-platform/src/App.js) | JavaScript | 23 | 0 | 3 | 26 |
| [intelligent-traffic-platform/src/App.test.js](/intelligent-traffic-platform/src/App.test.js) | JavaScript | 7 | 0 | 2 | 9 |
| [intelligent-traffic-platform/src/index.css](/intelligent-traffic-platform/src/index.css) | CSS | 12 | 0 | 2 | 14 |
| [intelligent-traffic-platform/src/index.js](/intelligent-traffic-platform/src/index.js) | JavaScript | 12 | 3 | 3 | 18 |
| [intelligent-traffic-platform/src/logo.svg](/intelligent-traffic-platform/src/logo.svg) | XML | 1 | 0 | 0 | 1 |
| [intelligent-traffic-platform/src/reportWebVitals.js](/intelligent-traffic-platform/src/reportWebVitals.js) | JavaScript | 12 | 0 | 2 | 14 |
| [intelligent-traffic-platform/src/setupTests.js](/intelligent-traffic-platform/src/setupTests.js) | JavaScript | 1 | 4 | 1 | 6 |
| [jsconfig.json](/jsconfig.json) | JSON with Comments | 8 | 0 | 0 | 8 |
| [package-lock.json](/package-lock.json) | JSON | 21,319 | 0 | 1 | 21,320 |
| [package.json](/package.json) | JSON | 66 | 0 | 1 | 67 |
| [public/index.html](/public/index.html) | HTML | 20 | 0 | 0 | 20 |
| [public/manifest.json](/public/manifest.json) | JSON | 15 | 0 | 0 | 15 |
| [public/mqtt-test.html](/public/mqtt-test.html) | HTML | 63 | 0 | 9 | 72 |
| [server/models/Device.js](/server/models/Device.js) | JavaScript | 33 | 0 | 3 | 36 |
| [server/models/Event.js](/server/models/Event.js) | JavaScript | 25 | 0 | 3 | 28 |
| [server/models/Intersection.js](/server/models/Intersection.js) | JavaScript | 21 | 0 | 3 | 24 |
| [server/models/User.js](/server/models/User.js) | JavaScript | 52 | 3 | 6 | 61 |
| [server/models/Vehicle.js](/server/models/Vehicle.js) | JavaScript | 36 | 0 | 3 | 39 |
| [server/mqtt-ws-bridge.js](/server/mqtt-ws-bridge.js) | JavaScript | 186 | 23 | 28 | 237 |
| [server/routes/auth.js](/server/routes/auth.js) | JavaScript | 67 | 6 | 16 | 89 |
| [server/routes/coordinates.js](/server/routes/coordinates.js) | JavaScript | 63 | 11 | 11 | 85 |
| [server/routes/devices.js](/server/routes/devices.js) | JavaScript | 133 | 9 | 21 | 163 |
| [server/routes/users.js](/server/routes/users.js) | JavaScript | 174 | 26 | 38 | 238 |
| [server/routes/vehicles.js](/server/routes/vehicles.js) | JavaScript | 187 | 24 | 39 | 250 |
| [server/server.js](/server/server.js) | JavaScript | 143 | 14 | 27 | 184 |
| [server/start-stream-server.js](/server/start-stream-server.js) | JavaScript | 26 | 4 | 5 | 35 |
| [server/stream-server.js](/server/stream-server.js) | JavaScript | 201 | 21 | 28 | 250 |
| [server/streamServer.js](/server/streamServer.js) | JavaScript | 126 | 12 | 21 | 159 |
| [src/App.css](/src/App.css) | CSS | 37 | 0 | 7 | 44 |
| [src/App.js](/src/App.js) | JavaScript | 57 | 6 | 7 | 70 |
| [src/assets/css/main.css](/src/assets/css/main.css) | CSS | 0 | 0 | 1 | 1 |
| [src/components/CampusModel.jsx](/src/components/CampusModel.jsx) | JavaScript JSX | 2,358 | 730 | 556 | 3,644 |
| [src/components/CoordinateSettings.jsx](/src/components/CoordinateSettings.jsx) | JavaScript JSX | 124 | 6 | 20 | 150 |
| [src/components/MainLayout.jsx](/src/components/MainLayout.jsx) | JavaScript JSX | 125 | 1 | 11 | 137 |
| [src/components/RealTimeEvents.jsx](/src/components/RealTimeEvents.jsx) | JavaScript JSX | 228 | 19 | 37 | 284 |
| [src/components/VideoPlayer.jsx](/src/components/VideoPlayer.jsx) | JavaScript JSX | 286 | 0 | 24 | 310 |
| [src/components/layout/CollapsibleSidebar.jsx](/src/components/layout/CollapsibleSidebar.jsx) | JavaScript JSX | 92 | 3 | 6 | 101 |
| [src/components/layout/MainLayout.jsx](/src/components/layout/MainLayout.jsx) | JavaScript JSX | 302 | 14 | 20 | 336 |
| [src/config/coordinates.json](/src/config/coordinates.json) | JSON | 14 | 0 | 0 | 14 |
| [src/data/devices.json](/src/data/devices.json) | JSON | 211 | 0 | 0 | 211 |
| [src/data/intersections.json](/src/data/intersections.json) | JSON | 44 | 0 | 0 | 44 |
| [src/data/users.json](/src/data/users.json) | JSON | 25 | 0 | 0 | 25 |
| [src/data/vehicles.json](/src/data/vehicles.json) | JSON | 40 | 0 | 0 | 40 |
| [src/index.css](/src/index.css) | CSS | 12 | 0 | 1 | 13 |
| [src/index.js](/src/index.js) | JavaScript | 10 | 0 | 1 | 11 |
| [src/js/road-monitor.js](/src/js/road-monitor.js) | JavaScript | 67 | 7 | 7 | 81 |
| [src/main.js](/src/main.js) | JavaScript | 0 | 0 | 1 | 1 |
| [src/models/User.js](/src/models/User.js) | JavaScript | 46 | 3 | 6 | 55 |
| [src/pages/DeviceManagement.jsx](/src/pages/DeviceManagement.jsx) | JavaScript JSX | 343 | 24 | 34 | 401 |
| [src/pages/DeviceStatus.jsx](/src/pages/DeviceStatus.jsx) | JavaScript JSX | 729 | 49 | 59 | 837 |
| [src/pages/Login.css](/src/pages/Login.css) | CSS | 59 | 1 | 9 | 69 |
| [src/pages/Login.jsx](/src/pages/Login.jsx) | JavaScript JSX | 253 | 65 | 43 | 361 |
| [src/pages/MonitoringPage.css](/src/pages/MonitoringPage.css) | CSS | 123 | 0 | 23 | 146 |
| [src/pages/MonitoringPage.js](/src/pages/MonitoringPage.js) | JavaScript | 144 | 10 | 20 | 174 |
| [src/pages/RealTimeTraffic.jsx](/src/pages/RealTimeTraffic.jsx) | JavaScript JSX | 937 | 142 | 136 | 1,215 |
| [src/pages/RoadMonitoring.jsx](/src/pages/RoadMonitoring.jsx) | JavaScript JSX | 318 | 52 | 36 | 406 |
| [src/pages/SystemManagement.jsx](/src/pages/SystemManagement.jsx) | JavaScript JSX | 648 | 45 | 71 | 764 |
| [src/pages/UserManagement.jsx](/src/pages/UserManagement.jsx) | JavaScript JSX | 52 | 0 | 8 | 60 |
| [src/pages/VehicleManagement.jsx](/src/pages/VehicleManagement.jsx) | JavaScript JSX | 247 | 23 | 25 | 295 |
| [src/routes/auth.js](/src/routes/auth.js) | JavaScript | 83 | 8 | 13 | 104 |
| [src/routes/vehicles.js](/src/routes/vehicles.js) | JavaScript | 138 | 22 | 33 | 193 |
| [src/scripts/migratePasswords.js](/src/scripts/migratePasswords.js) | JavaScript | 20 | 5 | 7 | 32 |
| [src/server.js](/src/server.js) | JavaScript | 699 | 76 | 111 | 886 |
| [src/server/config/db.js](/src/server/config/db.js) | JavaScript | 14 | 0 | 3 | 17 |
| [src/server/index.js](/src/server/index.js) | JavaScript | 18 | 4 | 6 | 28 |
| [src/server/middleware/admin.js](/src/server/middleware/admin.js) | JavaScript | 6 | 1 | 0 | 7 |
| [src/server/middleware/auth.js](/src/server/middleware/auth.js) | JavaScript | 55 | 7 | 13 | 75 |
| [src/server/models/User.js](/src/server/models/User.js) | JavaScript | 86 | 6 | 9 | 101 |
| [src/server/models/Vehicle.js](/src/server/models/Vehicle.js) | JavaScript | 30 | 1 | 3 | 34 |
| [src/server/reset-admin.js](/src/server/reset-admin.js) | JavaScript | 78 | 11 | 16 | 105 |
| [src/server/routes/auth.js](/src/server/routes/auth.js) | JavaScript | 121 | 12 | 19 | 152 |
| [src/server/routes/users.js](/src/server/routes/users.js) | JavaScript | 103 | 15 | 19 | 137 |
| [src/server/routes/vehicles.js](/src/server/routes/vehicles.js) | JavaScript | 108 | 17 | 24 | 149 |
| [src/server/rtspServer.js](/src/server/rtspServer.js) | JavaScript | 0 | 0 | 1 | 1 |
| [src/server/start.js](/src/server/start.js) | JavaScript | 0 | 0 | 1 | 1 |
| [src/server/webrtcServer.js](/src/server/webrtcServer.js) | JavaScript | 0 | 0 | 1 | 1 |
| [src/services/auth.js](/src/services/auth.js) | JavaScript | 35 | 6 | 7 | 48 |
| [src/setupProxy.js](/src/setupProxy.js) | JavaScript | 46 | 2 | 3 | 51 |
| [src/utils/CoordinateConverter.js](/src/utils/CoordinateConverter.js) | JavaScript | 102 | 26 | 23 | 151 |
| [src/utils/axios.js](/src/utils/axios.js) | JavaScript | 41 | 6 | 4 | 51 |
| [start-all.bat](/start-all.bat) | Batch | 20 | 0 | 5 | 25 |
| [start-simple.bat](/start-simple.bat) | Batch | 38 | 1 | 6 | 45 |
| [start-stream-server.js](/start-stream-server.js) | JavaScript | 1 | 0 | 0 | 1 |
| [start.bat](/start.bat) | Batch | 64 | 9 | 9 | 82 |
| [start\_en.bat](/start_en.bat) | Batch | 142 | 11 | 14 | 167 |
| [vue.config.js](/vue.config.js) | JavaScript | 0 | 0 | 1 | 1 |
| [使用说明书.md](/%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E%E4%B9%A6.md) | Markdown | 494 | 0 | 106 | 600 |
| [消息协议文档说明.md](/%E6%B6%88%E6%81%AF%E5%8D%8F%E8%AE%AE%E6%96%87%E6%A1%A3%E8%AF%B4%E6%98%8E.md) | Markdown | 110 | 0 | 53 | 163 |
| [软件著作权/软件著作权的样例.md](/%E8%BD%AF%E4%BB%B6%E8%91%97%E4%BD%9C%E6%9D%83/%E8%BD%AF%E4%BB%B6%E8%91%97%E4%BD%9C%E6%9D%83%E7%9A%84%E6%A0%B7%E4%BE%8B.md) | Markdown | 313 | 0 | 376 | 689 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)