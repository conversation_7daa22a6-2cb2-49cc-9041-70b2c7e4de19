{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\VehicleManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Modal, Form, Input, Space, message } from 'antd';\nimport { EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VehicleManagement = () => {\n  _s();\n  const [vehicles, setVehicles] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingVehicle, setEditingVehicle] = useState(null);\n\n  // 获取车辆列表\n  const fetchVehicles = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/vehicles');\n      const data = await response.json();\n      setVehicles(data);\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n      message.error('获取车辆列表失败');\n    }\n    setLoading(false);\n  };\n  useEffect(() => {\n    fetchVehicles();\n  }, []);\n\n  // 表格列定义\n  const columns = [{\n    title: '车牌号',\n    dataIndex: 'plateNumber',\n    key: 'plateNumber'\n  }, {\n    title: 'BSM ID',\n    dataIndex: 'bsmId',\n    key: 'bsmId'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        danger: true,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleDelete(record),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 处理添加/编辑表单提交\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      if (editingVehicle) {\n        // 编辑现有车辆\n        await fetch(`/api/vehicles/${editingVehicle.id}`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(values)\n        });\n        message.success('车辆信息更新成功');\n      } else {\n        // 添加新车辆\n        await fetch('/api/vehicles', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(values)\n        });\n        message.success('车辆添加成功');\n      }\n      setModalVisible(false);\n      form.resetFields();\n      setEditingVehicle(null);\n      fetchVehicles();\n    } catch (error) {\n      console.error('提交表单失败:', error);\n      message.error('操作失败，请重试');\n    }\n  };\n\n  // 处理编辑\n  const handleEdit = record => {\n    setEditingVehicle(record);\n    form.setFieldsValue(record);\n    setModalVisible(true);\n  };\n\n  // 处理删除\n  const handleDelete = async record => {\n    Modal.confirm({\n      title: '确认删除',\n      content: `确定要删除车牌号为 ${record.plateNumber} 的车辆吗？`,\n      onOk: async () => {\n        try {\n          await fetch(`/api/vehicles/${record.id}`, {\n            method: 'DELETE'\n          });\n          message.success('删除成功');\n          fetchVehicles();\n        } catch (error) {\n          console.error('删除失败:', error);\n          message.error('删除失败，请重试');\n        }\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 17\n        }, this),\n        onClick: () => {\n          setEditingVehicle(null);\n          form.resetFields();\n          setModalVisible(true);\n        },\n        children: \"\\u6DFB\\u52A0\\u8F66\\u8F86\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      columns: columns,\n      dataSource: vehicles,\n      rowKey: \"id\",\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingVehicle ? '编辑车辆' : '添加车辆',\n      open: modalVisible,\n      onOk: handleSubmit,\n      onCancel: () => {\n        setModalVisible(false);\n        form.resetFields();\n        setEditingVehicle(null);\n      },\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"plateNumber\",\n          label: \"\\u8F66\\u724C\\u53F7\",\n          rules: [{\n            required: true,\n            message: '请输入车牌号'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8F66\\u724C\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"bsmId\",\n          label: \"BSM ID\",\n          rules: [{\n            required: true,\n            message: '请输入BSM ID'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165BSM ID\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this);\n};\n_s(VehicleManagement, \"oA3mhoQwfuaMOGW6PM+zYZ/+ctU=\", false, function () {\n  return [Form.useForm];\n});\n_c = VehicleManagement;\nexport default VehicleManagement;\nvar _c;\n$RefreshReg$(_c, \"VehicleManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Space", "message", "EditOutlined", "DeleteOutlined", "PlusOutlined", "jsxDEV", "_jsxDEV", "VehicleManagement", "_s", "vehicles", "setVehicles", "loading", "setLoading", "modalVisible", "setModalVisible", "form", "useForm", "editingVehicle", "setEditingVehicle", "fetchVehicles", "response", "fetch", "data", "json", "error", "console", "columns", "title", "dataIndex", "key", "render", "_", "record", "size", "children", "type", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "handleEdit", "danger", "handleDelete", "handleSubmit", "values", "validateFields", "id", "method", "headers", "body", "JSON", "stringify", "success", "resetFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confirm", "content", "plateNumber", "onOk", "style", "padding", "marginBottom", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onCancel", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "_c", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/VehicleManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Table, Button, Modal, Form, Input, Space, message } from 'antd';\r\nimport { EditOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';\r\n\r\nconst VehicleManagement = () => {\r\n  const [vehicles, setVehicles] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [form] = Form.useForm();\r\n  const [editingVehicle, setEditingVehicle] = useState(null);\r\n\r\n  // 获取车辆列表\r\n  const fetchVehicles = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await fetch('/api/vehicles');\r\n      const data = await response.json();\r\n      setVehicles(data);\r\n    } catch (error) {\r\n      console.error('获取车辆列表失败:', error);\r\n      message.error('获取车辆列表失败');\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchVehicles();\r\n  }, []);\r\n\r\n  // 表格列定义\r\n  const columns = [\r\n    {\r\n      title: '车牌号',\r\n      dataIndex: 'plateNumber',\r\n      key: 'plateNumber',\r\n    },\r\n    {\r\n      title: 'BSM ID',\r\n      dataIndex: 'bsmId',\r\n      key: 'bsmId',\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      render: (_, record) => (\r\n        <Space size=\"middle\">\r\n          <Button\r\n            type=\"link\"\r\n            icon={<EditOutlined />}\r\n            onClick={() => handleEdit(record)}\r\n          >\r\n            编辑\r\n          </Button>\r\n          <Button\r\n            type=\"link\"\r\n            danger\r\n            icon={<DeleteOutlined />}\r\n            onClick={() => handleDelete(record)}\r\n          >\r\n            删除\r\n          </Button>\r\n        </Space>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 处理添加/编辑表单提交\r\n  const handleSubmit = async () => {\r\n    try {\r\n      const values = await form.validateFields();\r\n      if (editingVehicle) {\r\n        // 编辑现有车辆\r\n        await fetch(`/api/vehicles/${editingVehicle.id}`, {\r\n          method: 'PUT',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n          body: JSON.stringify(values),\r\n        });\r\n        message.success('车辆信息更新成功');\r\n      } else {\r\n        // 添加新车辆\r\n        await fetch('/api/vehicles', {\r\n          method: 'POST',\r\n          headers: {\r\n            'Content-Type': 'application/json',\r\n          },\r\n          body: JSON.stringify(values),\r\n        });\r\n        message.success('车辆添加成功');\r\n      }\r\n      setModalVisible(false);\r\n      form.resetFields();\r\n      setEditingVehicle(null);\r\n      fetchVehicles();\r\n    } catch (error) {\r\n      console.error('提交表单失败:', error);\r\n      message.error('操作失败，请重试');\r\n    }\r\n  };\r\n\r\n  // 处理编辑\r\n  const handleEdit = (record) => {\r\n    setEditingVehicle(record);\r\n    form.setFieldsValue(record);\r\n    setModalVisible(true);\r\n  };\r\n\r\n  // 处理删除\r\n  const handleDelete = async (record) => {\r\n    Modal.confirm({\r\n      title: '确认删除',\r\n      content: `确定要删除车牌号为 ${record.plateNumber} 的车辆吗？`,\r\n      onOk: async () => {\r\n        try {\r\n          await fetch(`/api/vehicles/${record.id}`, {\r\n            method: 'DELETE',\r\n          });\r\n          message.success('删除成功');\r\n          fetchVehicles();\r\n        } catch (error) {\r\n          console.error('删除失败:', error);\r\n          message.error('删除失败，请重试');\r\n        }\r\n      },\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div style={{ padding: '24px' }}>\r\n      <div style={{ marginBottom: '16px' }}>\r\n        <Button\r\n          type=\"primary\"\r\n          icon={<PlusOutlined />}\r\n          onClick={() => {\r\n            setEditingVehicle(null);\r\n            form.resetFields();\r\n            setModalVisible(true);\r\n          }}\r\n        >\r\n          添加车辆\r\n        </Button>\r\n      </div>\r\n\r\n      <Table\r\n        columns={columns}\r\n        dataSource={vehicles}\r\n        rowKey=\"id\"\r\n        loading={loading}\r\n      />\r\n\r\n      <Modal\r\n        title={editingVehicle ? '编辑车辆' : '添加车辆'}\r\n        open={modalVisible}\r\n        onOk={handleSubmit}\r\n        onCancel={() => {\r\n          setModalVisible(false);\r\n          form.resetFields();\r\n          setEditingVehicle(null);\r\n        }}\r\n      >\r\n        <Form\r\n          form={form}\r\n          layout=\"vertical\"\r\n        >\r\n          <Form.Item\r\n            name=\"plateNumber\"\r\n            label=\"车牌号\"\r\n            rules={[{ required: true, message: '请输入车牌号' }]}\r\n          >\r\n            <Input placeholder=\"请输入车牌号\" />\r\n          </Form.Item>\r\n          <Form.Item\r\n            name=\"bsmId\"\r\n            label=\"BSM ID\"\r\n            rules={[{ required: true, message: '请输入BSM ID' }]}\r\n          >\r\n            <Input placeholder=\"请输入BSM ID\" />\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VehicleManagement; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,QAAQ,MAAM;AACxE,SAASC,YAAY,EAAEC,cAAc,EAAEC,YAAY,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/E,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,IAAI,CAAC,GAAGjB,IAAI,CAACkB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM0B,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCP,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,eAAe,CAAC;MAC7C,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCb,WAAW,CAACY,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCvB,OAAO,CAACuB,KAAK,CAAC,UAAU,CAAC;IAC3B;IACAZ,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAEDlB,SAAS,CAAC,MAAM;IACdyB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAM,kBAChB1B,OAAA,CAACN,KAAK;MAACiC,IAAI,EAAC,QAAQ;MAAAC,QAAA,gBAClB5B,OAAA,CAACV,MAAM;QACLuC,IAAI,EAAC,MAAM;QACXC,IAAI,eAAE9B,OAAA,CAACJ,YAAY;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBC,OAAO,EAAEA,CAAA,KAAMC,UAAU,CAACV,MAAM,CAAE;QAAAE,QAAA,EACnC;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlC,OAAA,CAACV,MAAM;QACLuC,IAAI,EAAC,MAAM;QACXQ,MAAM;QACNP,IAAI,eAAE9B,OAAA,CAACH,cAAc;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBC,OAAO,EAAEA,CAAA,KAAMG,YAAY,CAACZ,MAAM,CAAE;QAAAE,QAAA,EACrC;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;;EAED;EACA,MAAMK,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM/B,IAAI,CAACgC,cAAc,CAAC,CAAC;MAC1C,IAAI9B,cAAc,EAAE;QAClB;QACA,MAAMI,KAAK,CAAC,iBAAiBJ,cAAc,CAAC+B,EAAE,EAAE,EAAE;UAChDC,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACP,MAAM;QAC7B,CAAC,CAAC;QACF7C,OAAO,CAACqD,OAAO,CAAC,UAAU,CAAC;MAC7B,CAAC,MAAM;QACL;QACA,MAAMjC,KAAK,CAAC,eAAe,EAAE;UAC3B4B,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACP,MAAM;QAC7B,CAAC,CAAC;QACF7C,OAAO,CAACqD,OAAO,CAAC,QAAQ,CAAC;MAC3B;MACAxC,eAAe,CAAC,KAAK,CAAC;MACtBC,IAAI,CAACwC,WAAW,CAAC,CAAC;MAClBrC,iBAAiB,CAAC,IAAI,CAAC;MACvBC,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BvB,OAAO,CAACuB,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMkB,UAAU,GAAIV,MAAM,IAAK;IAC7Bd,iBAAiB,CAACc,MAAM,CAAC;IACzBjB,IAAI,CAACyC,cAAc,CAACxB,MAAM,CAAC;IAC3BlB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAM8B,YAAY,GAAG,MAAOZ,MAAM,IAAK;IACrCnC,KAAK,CAAC4D,OAAO,CAAC;MACZ9B,KAAK,EAAE,MAAM;MACb+B,OAAO,EAAE,aAAa1B,MAAM,CAAC2B,WAAW,QAAQ;MAChDC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF,MAAMvC,KAAK,CAAC,iBAAiBW,MAAM,CAACgB,EAAE,EAAE,EAAE;YACxCC,MAAM,EAAE;UACV,CAAC,CAAC;UACFhD,OAAO,CAACqD,OAAO,CAAC,MAAM,CAAC;UACvBnC,aAAa,CAAC,CAAC;QACjB,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;UAC7BvB,OAAO,CAACuB,KAAK,CAAC,UAAU,CAAC;QAC3B;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,oBACElB,OAAA;IAAKuD,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAA5B,QAAA,gBAC9B5B,OAAA;MAAKuD,KAAK,EAAE;QAAEE,YAAY,EAAE;MAAO,CAAE;MAAA7B,QAAA,eACnC5B,OAAA,CAACV,MAAM;QACLuC,IAAI,EAAC,SAAS;QACdC,IAAI,eAAE9B,OAAA,CAACF,YAAY;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBC,OAAO,EAAEA,CAAA,KAAM;UACbvB,iBAAiB,CAAC,IAAI,CAAC;UACvBH,IAAI,CAACwC,WAAW,CAAC,CAAC;UAClBzC,eAAe,CAAC,IAAI,CAAC;QACvB,CAAE;QAAAoB,QAAA,EACH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENlC,OAAA,CAACX,KAAK;MACJ+B,OAAO,EAAEA,OAAQ;MACjBsC,UAAU,EAAEvD,QAAS;MACrBwD,MAAM,EAAC,IAAI;MACXtD,OAAO,EAAEA;IAAQ;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAEFlC,OAAA,CAACT,KAAK;MACJ8B,KAAK,EAAEV,cAAc,GAAG,MAAM,GAAG,MAAO;MACxCiD,IAAI,EAAErD,YAAa;MACnB+C,IAAI,EAAEf,YAAa;MACnBsB,QAAQ,EAAEA,CAAA,KAAM;QACdrD,eAAe,CAAC,KAAK,CAAC;QACtBC,IAAI,CAACwC,WAAW,CAAC,CAAC;QAClBrC,iBAAiB,CAAC,IAAI,CAAC;MACzB,CAAE;MAAAgB,QAAA,eAEF5B,OAAA,CAACR,IAAI;QACHiB,IAAI,EAAEA,IAAK;QACXqD,MAAM,EAAC,UAAU;QAAAlC,QAAA,gBAEjB5B,OAAA,CAACR,IAAI,CAACuE,IAAI;UACRC,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAC,oBAAK;UACXC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExE,OAAO,EAAE;UAAS,CAAC,CAAE;UAAAiC,QAAA,eAE/C5B,OAAA,CAACP,KAAK;YAAC2E,WAAW,EAAC;UAAQ;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACZlC,OAAA,CAACR,IAAI,CAACuE,IAAI;UACRC,IAAI,EAAC,OAAO;UACZC,KAAK,EAAC,QAAQ;UACdC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExE,OAAO,EAAE;UAAY,CAAC,CAAE;UAAAiC,QAAA,eAElD5B,OAAA,CAACP,KAAK;YAAC2E,WAAW,EAAC;UAAW;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAChC,EAAA,CAnLID,iBAAiB;EAAA,QAINT,IAAI,CAACkB,OAAO;AAAA;AAAA2D,EAAA,GAJvBpE,iBAAiB;AAqLvB,eAAeA,iBAAiB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}