{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ExclamationCircleTwoToneSvg from \"@ant-design/icons-svg/es/asn/ExclamationCircleTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ExclamationCircleTwoTone = function ExclamationCircleTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ExclamationCircleTwoToneSvg\n  }));\n};\n\n/**![exclamation-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTUxMiAxNDBjLTIwNS40IDAtMzcyIDE2Ni42LTM3MiAzNzJzMTY2LjYgMzcyIDM3MiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzItMTY2LjYtMzcyLTM3Mi0zNzJ6bS0zMiAxNTZjMC00LjQgMy42LTggOC04aDQ4YzQuNCAwIDggMy42IDggOHYyNzJjMCA0LjQtMy42IDgtOCA4aC00OGMtNC40IDAtOC0zLjYtOC04VjI5NnptMzIgNDQwYTQ4LjAxIDQ4LjAxIDAgMDEwLTk2IDQ4LjAxIDQ4LjAxIDAgMDEwIDk2eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNNDg4IDU3Nmg0OGM0LjQgMCA4LTMuNiA4LThWMjk2YzAtNC40LTMuNi04LTgtOGgtNDhjLTQuNCAwLTggMy42LTggOHYyNzJjMCA0LjQgMy42IDggOCA4em0tMjQgMTEyYTQ4IDQ4IDAgMTA5NiAwIDQ4IDQ4IDAgMTAtOTYgMHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ExclamationCircleTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ExclamationCircleTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "ExclamationCircleTwoToneSvg", "AntdIcon", "ExclamationCircleTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/@ant-design/icons/es/icons/ExclamationCircleTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport ExclamationCircleTwoToneSvg from \"@ant-design/icons-svg/es/asn/ExclamationCircleTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar ExclamationCircleTwoTone = function ExclamationCircleTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: ExclamationCircleTwoToneSvg\n  }));\n};\n\n/**![exclamation-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTUxMiAxNDBjLTIwNS40IDAtMzcyIDE2Ni42LTM3MiAzNzJzMTY2LjYgMzcyIDM3MiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzItMTY2LjYtMzcyLTM3Mi0zNzJ6bS0zMiAxNTZjMC00LjQgMy42LTggOC04aDQ4YzQuNCAwIDggMy42IDggOHYyNzJjMCA0LjQtMy42IDgtOCA4aC00OGMtNC40IDAtOC0zLjYtOC04VjI5NnptMzIgNDQwYTQ4LjAxIDQ4LjAxIDAgMDEwLTk2IDQ4LjAxIDQ4LjAxIDAgMDEwIDk2eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNNDg4IDU3Nmg0OGM0LjQgMCA4LTMuNiA4LThWMjk2YzAtNC40LTMuNi04LTgtOGgtNDhjLTQuNCAwLTggMy42LTggOHYyNzJjMCA0LjQgMy42IDggOCA4em0tMjQgMTEyYTQ4IDQ4IDAgMTA5NiAwIDQ4IDQ4IDAgMTAtOTYgMHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(ExclamationCircleTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ExclamationCircleTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,2BAA2B,MAAM,uDAAuD;AAC/F,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC3E,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,wBAAwB,CAAC;AACrE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,0BAA0B;AAClD;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}