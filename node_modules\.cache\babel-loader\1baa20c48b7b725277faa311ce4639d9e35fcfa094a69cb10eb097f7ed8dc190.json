{"ast": null, "code": "// ============================ Directory =============================\nexport const genDirectoryStyle = _ref => {\n  let {\n    treeCls,\n    treeNodeCls,\n    directoryNodeSelectedBg,\n    directoryNodeSelectedColor,\n    motionDurationMid,\n    borderRadius,\n    controlItemBgHover\n  } = _ref;\n  return {\n    [`${treeCls}${treeCls}-directory ${treeNodeCls}`]: {\n      // >>> Title\n      [`${treeCls}-node-content-wrapper`]: {\n        position: 'static',\n        [`> *:not(${treeCls}-drop-indicator)`]: {\n          position: 'relative'\n        },\n        '&:hover': {\n          background: 'transparent'\n        },\n        // Expand interactive area to whole line\n        '&:before': {\n          position: 'absolute',\n          inset: 0,\n          transition: `background-color ${motionDurationMid}`,\n          content: '\"\"',\n          borderRadius\n        },\n        '&:hover:before': {\n          background: controlItemBgHover\n        }\n      },\n      [`${treeCls}-switcher, ${treeCls}-checkbox, ${treeCls}-draggable-icon`]: {\n        zIndex: 1\n      },\n      // ============= Selected =============\n      '&-selected': {\n        [`${treeCls}-switcher, ${treeCls}-draggable-icon`]: {\n          color: directoryNodeSelectedColor\n        },\n        // >>> Title\n        [`${treeCls}-node-content-wrapper`]: {\n          color: directoryNodeSelectedColor,\n          background: 'transparent',\n          '&:before, &:hover:before': {\n            background: directoryNodeSelectedBg\n          }\n        }\n      }\n    }\n  };\n};", "map": {"version": 3, "names": ["genDirectoryStyle", "_ref", "treeCls", "treeNodeCls", "directoryNodeSelectedBg", "directoryNodeSelectedColor", "motionDurationMid", "borderRadius", "controlItemBgHover", "position", "background", "inset", "transition", "content", "zIndex", "color"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/tree/style/directory.js"], "sourcesContent": ["// ============================ Directory =============================\nexport const genDirectoryStyle = _ref => {\n  let {\n    treeCls,\n    treeNodeCls,\n    directoryNodeSelectedBg,\n    directoryNodeSelectedColor,\n    motionDurationMid,\n    borderRadius,\n    controlItemBgHover\n  } = _ref;\n  return {\n    [`${treeCls}${treeCls}-directory ${treeNodeCls}`]: {\n      // >>> Title\n      [`${treeCls}-node-content-wrapper`]: {\n        position: 'static',\n        [`> *:not(${treeCls}-drop-indicator)`]: {\n          position: 'relative'\n        },\n        '&:hover': {\n          background: 'transparent'\n        },\n        // Expand interactive area to whole line\n        '&:before': {\n          position: 'absolute',\n          inset: 0,\n          transition: `background-color ${motionDurationMid}`,\n          content: '\"\"',\n          borderRadius\n        },\n        '&:hover:before': {\n          background: controlItemBgHover\n        }\n      },\n      [`${treeCls}-switcher, ${treeCls}-checkbox, ${treeCls}-draggable-icon`]: {\n        zIndex: 1\n      },\n      // ============= Selected =============\n      '&-selected': {\n        [`${treeCls}-switcher, ${treeCls}-draggable-icon`]: {\n          color: directoryNodeSelectedColor\n        },\n        // >>> Title\n        [`${treeCls}-node-content-wrapper`]: {\n          color: directoryNodeSelectedColor,\n          background: 'transparent',\n          '&:before, &:hover:before': {\n            background: directoryNodeSelectedBg\n          }\n        }\n      }\n    }\n  };\n};"], "mappings": "AAAA;AACA,OAAO,MAAMA,iBAAiB,GAAGC,IAAI,IAAI;EACvC,IAAI;IACFC,OAAO;IACPC,WAAW;IACXC,uBAAuB;IACvBC,0BAA0B;IAC1BC,iBAAiB;IACjBC,YAAY;IACZC;EACF,CAAC,GAAGP,IAAI;EACR,OAAO;IACL,CAAC,GAAGC,OAAO,GAAGA,OAAO,cAAcC,WAAW,EAAE,GAAG;MACjD;MACA,CAAC,GAAGD,OAAO,uBAAuB,GAAG;QACnCO,QAAQ,EAAE,QAAQ;QAClB,CAAC,WAAWP,OAAO,kBAAkB,GAAG;UACtCO,QAAQ,EAAE;QACZ,CAAC;QACD,SAAS,EAAE;UACTC,UAAU,EAAE;QACd,CAAC;QACD;QACA,UAAU,EAAE;UACVD,QAAQ,EAAE,UAAU;UACpBE,KAAK,EAAE,CAAC;UACRC,UAAU,EAAE,oBAAoBN,iBAAiB,EAAE;UACnDO,OAAO,EAAE,IAAI;UACbN;QACF,CAAC;QACD,gBAAgB,EAAE;UAChBG,UAAU,EAAEF;QACd;MACF,CAAC;MACD,CAAC,GAAGN,OAAO,cAAcA,OAAO,cAAcA,OAAO,iBAAiB,GAAG;QACvEY,MAAM,EAAE;MACV,CAAC;MACD;MACA,YAAY,EAAE;QACZ,CAAC,GAAGZ,OAAO,cAAcA,OAAO,iBAAiB,GAAG;UAClDa,KAAK,EAAEV;QACT,CAAC;QACD;QACA,CAAC,GAAGH,OAAO,uBAAuB,GAAG;UACnCa,KAAK,EAAEV,0BAA0B;UACjCK,UAAU,EAAE,aAAa;UACzB,0BAA0B,EAAE;YAC1BA,UAAU,EAAEN;UACd;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}