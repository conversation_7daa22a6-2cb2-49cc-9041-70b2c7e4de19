{"ast": null, "code": "import { FastColor } from '@ant-design/fast-color';\nimport { initComponentToken } from '../../input/style/token';\nexport const prepareComponentToken = token => {\n  var _a;\n  const handleVisible = (_a = token.handleVisible) !== null && _a !== void 0 ? _a : 'auto';\n  const handleWidth = token.controlHeightSM - token.lineWidth * 2;\n  return Object.assign(Object.assign({}, initComponentToken(token)), {\n    controlWidth: 90,\n    handleWidth,\n    handleFontSize: token.fontSize / 2,\n    handleVisible,\n    handleActiveBg: token.colorFillAlter,\n    handleBg: token.colorBgContainer,\n    filledHandleBg: new FastColor(token.colorFillSecondary).onBackground(token.colorBgContainer).toHexString(),\n    handleHoverColor: token.colorPrimary,\n    handleBorderColor: token.colorBorder,\n    handleOpacity: handleVisible === true ? 1 : 0,\n    handleVisibleWidth: handleVisible === true ? handleWidth : 0\n  });\n};", "map": {"version": 3, "names": ["FastColor", "initComponentToken", "prepareComponentToken", "token", "_a", "handleVisible", "handleWidth", "controlHeightSM", "lineWidth", "Object", "assign", "controlWidth", "handleFontSize", "fontSize", "handleActiveBg", "colorFillAlter", "handleBg", "colorBgContainer", "filledHandleBg", "colorFillSecondary", "onBackground", "toHexString", "handleHoverColor", "colorPrimary", "handleBorderColor", "colorBorder", "handleOpacity", "handleVisibleWidth"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/input-number/style/token.js"], "sourcesContent": ["import { FastColor } from '@ant-design/fast-color';\nimport { initComponentToken } from '../../input/style/token';\nexport const prepareComponentToken = token => {\n  var _a;\n  const handleVisible = (_a = token.handleVisible) !== null && _a !== void 0 ? _a : 'auto';\n  const handleWidth = token.controlHeightSM - token.lineWidth * 2;\n  return Object.assign(Object.assign({}, initComponentToken(token)), {\n    controlWidth: 90,\n    handleWidth,\n    handleFontSize: token.fontSize / 2,\n    handleVisible,\n    handleActiveBg: token.colorFillAlter,\n    handleBg: token.colorBgContainer,\n    filledHandleBg: new FastColor(token.colorFillSecondary).onBackground(token.colorBgContainer).toHexString(),\n    handleHoverColor: token.colorPrimary,\n    handleBorderColor: token.colorBorder,\n    handleOpacity: handleVisible === true ? 1 : 0,\n    handleVisibleWidth: handleVisible === true ? handleWidth : 0\n  });\n};"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,OAAO,MAAMC,qBAAqB,GAAGC,KAAK,IAAI;EAC5C,IAAIC,EAAE;EACN,MAAMC,aAAa,GAAG,CAACD,EAAE,GAAGD,KAAK,CAACE,aAAa,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,MAAM;EACxF,MAAME,WAAW,GAAGH,KAAK,CAACI,eAAe,GAAGJ,KAAK,CAACK,SAAS,GAAG,CAAC;EAC/D,OAAOC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAET,kBAAkB,CAACE,KAAK,CAAC,CAAC,EAAE;IACjEQ,YAAY,EAAE,EAAE;IAChBL,WAAW;IACXM,cAAc,EAAET,KAAK,CAACU,QAAQ,GAAG,CAAC;IAClCR,aAAa;IACbS,cAAc,EAAEX,KAAK,CAACY,cAAc;IACpCC,QAAQ,EAAEb,KAAK,CAACc,gBAAgB;IAChCC,cAAc,EAAE,IAAIlB,SAAS,CAACG,KAAK,CAACgB,kBAAkB,CAAC,CAACC,YAAY,CAACjB,KAAK,CAACc,gBAAgB,CAAC,CAACI,WAAW,CAAC,CAAC;IAC1GC,gBAAgB,EAAEnB,KAAK,CAACoB,YAAY;IACpCC,iBAAiB,EAAErB,KAAK,CAACsB,WAAW;IACpCC,aAAa,EAAErB,aAAa,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;IAC7CsB,kBAAkB,EAAEtB,aAAa,KAAK,IAAI,GAAGC,WAAW,GAAG;EAC7D,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}