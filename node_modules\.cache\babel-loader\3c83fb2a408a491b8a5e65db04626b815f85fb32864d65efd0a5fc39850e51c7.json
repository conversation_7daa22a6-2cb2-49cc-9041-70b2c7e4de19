{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\DeviceStatus.jsx\",\n  _s = $RefreshSig$();\n// src/pages/DeviceStatus.jsx\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Row, Col, Card, Statistic, List, Descriptions, Spin, Badge, Tag, Button, Empty } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { PieChart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport { CheckCircleOutlined, CloseCircleOutlined, WarningOutlined, SyncOutlined } from '@ant-design/icons';\nimport axios from 'axios';\nimport { message } from 'antd';\n\n// 注册必要的echarts组件\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\necharts.use([PieChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\n_c = PageContainer;\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 24px' : props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 0' : props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\n_c2 = MainContent;\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\n_c3 = InfoCard;\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n  \n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n_c4 = CompactStatistic;\nconst DeviceStatus = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [devices, setDevices] = useState([]);\n  const [selectedDevice, setSelectedDevice] = useState(null);\n  const [stats, setStats] = useState({\n    total: 0,\n    online: 0,\n    offline: 0,\n    warning: 0\n  });\n  const deviceTypeChartRef = useRef(null);\n  const deviceStatusChartRef = useRef(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  // 获取设备列表\n  const fetchDevices = async () => {\n    try {\n      setLoading(true);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data && response.data.success) {\n        const deviceData = response.data.data || [];\n        setDevices(deviceData);\n\n        // 计算统计数据\n        const stats = {\n          total: deviceData.length,\n          online: deviceData.filter(d => d.status === 'online').length,\n          offline: deviceData.filter(d => d.status === 'offline').length,\n          warning: deviceData.filter(d => d.status === 'warning' || d.status === 'error').length\n        };\n        setStats(stats);\n\n        // 默认选中第一个设备\n        if (deviceData.length > 0 && !selectedDevice) {\n          setSelectedDevice(deviceData[0]);\n        }\n      } else {\n        var _response$data;\n        message.warning('获取设备列表失败: ' + (((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || '未知错误'));\n      }\n    } catch (error) {\n      console.error('获取设备列表失败:', error);\n      message.error('获取设备列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchDevices();\n\n    // 设置定时刷新\n    const interval = setInterval(() => {\n      fetchDevices();\n    }, 30000); // 每30秒刷新一次\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // 渲染状态图标\n  const renderStatusIcon = status => {\n    switch (status) {\n      case 'online':\n        return /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n          style: {\n            color: '#52c41a'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 16\n        }, this);\n      case 'offline':\n        return /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n          style: {\n            color: '#bfbfbf'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 16\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(WarningOutlined, {\n          style: {\n            color: '#faad14'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 16\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n          style: {\n            color: '#f5222d'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 16\n        }, this);\n      case 'maintenance':\n        return /*#__PURE__*/_jsxDEV(SyncOutlined, {\n          style: {\n            color: '#1890ff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 16\n        }, this);\n      default:\n        return null;\n    }\n  };\n\n  // 渲染状态标签\n  const renderStatusTag = status => {\n    const statusMap = {\n      online: {\n        color: 'green',\n        text: '在线'\n      },\n      offline: {\n        color: 'gray',\n        text: '离线'\n      },\n      warning: {\n        color: 'orange',\n        text: '警告'\n      },\n      error: {\n        color: 'red',\n        text: '错误'\n      },\n      maintenance: {\n        color: 'blue',\n        text: '维护中'\n      }\n    };\n    const statusInfo = statusMap[status] || {\n      color: 'default',\n      text: status\n    };\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: statusInfo.color,\n      children: statusInfo.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 12\n    }, this);\n  };\n\n  // 渲染设备类型\n  const renderDeviceType = type => {\n    const typeMap = {\n      camera: '摄像头',\n      access_control: '门禁系统',\n      sensor: '传感器',\n      broadcast: '广播系统',\n      other: '其他设备'\n    };\n    return typeMap[type] || type;\n  };\n\n  // 处理设备选择\n  const handleDeviceSelect = device => {\n    setSelectedDevice(device);\n  };\n\n  // 设备列表列定义\n  const deviceColumns = [{\n    title: '设备名称',\n    dataIndex: 'name',\n    key: 'name',\n    width: '50%'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: '25%',\n    render: status => /*#__PURE__*/_jsxDEV(Badge, {\n      status: status === 'online' ? 'success' : 'error',\n      text: status === 'online' ? '在线' : '离线'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '健康度',\n    dataIndex: 'health',\n    key: 'health',\n    width: '25%',\n    render: health => {\n      let color = 'green';\n      let text = '正常';\n      if (health === 'warning') {\n        color = 'orange';\n        text = '警告';\n      } else if (health === 'error') {\n        color = 'red';\n        text = '错误';\n      }\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: color,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 16\n      }, this);\n    }\n  }];\n\n  // 获取健康状态颜色\n  const getHealthColor = value => {\n    if (value >= 80) return '#f5222d'; // 红色\n    if (value >= 60) return '#faad14'; // 黄色\n    return '#52c41a'; // 绿色\n  };\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    spinning: loading,\n    tip: \"\\u52A0\\u8F7D\\u4E2D...\",\n    children: /*#__PURE__*/_jsxDEV(PageContainer, {\n      children: [/*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"left\",\n        collapsed: leftCollapsed,\n        onCollapse: () => setLeftCollapsed(!leftCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8BBE\\u5907\\u603B\\u6570\\u7EDF\\u8BA1\",\n          bordered: false,\n          height: \"150px\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [8, 8],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u8BBE\\u5907\\u603B\\u6570\",\n                value: stats.total,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u5728\\u7EBF\\u8BBE\\u5907\",\n                value: stats.online,\n                suffix: `/ ${stats.total}`,\n                valueStyle: {\n                  color: '#3f8600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u79BB\\u7EBF\\u8BBE\\u5907\",\n                value: stats.offline,\n                suffix: `/ ${stats.total}`,\n                valueStyle: {\n                  color: '#bfbfbf'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u5F02\\u5E38\\u8BBE\\u5907\",\n                value: stats.warning,\n                suffix: `/ ${stats.total}`,\n                valueStyle: {\n                  color: '#faad14'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8BBE\\u5907\\u7C7B\\u578B\\u5206\\u5E03\",\n          bordered: false,\n          height: \"calc(50% - 81px)\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: deviceTypeChartRef,\n            style: {\n              height: '100%',\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8BBE\\u5907\\u72B6\\u6001\\u5206\\u5E03\",\n          bordered: false,\n          height: \"calc(50% - 81px)\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: deviceStatusChartRef,\n            style: {\n              height: '100%',\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        leftCollapsed: leftCollapsed,\n        rightCollapsed: rightCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"right\",\n        collapsed: rightCollapsed,\n        onCollapse: () => setRightCollapsed(!rightCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8BBE\\u5907\\u5217\\u8868\",\n          bordered: false,\n          height: \"50%\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            onClick: fetchDevices,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: loading,\n            children: devices.length > 0 ? /*#__PURE__*/_jsxDEV(List, {\n              dataSource: devices,\n              renderItem: device => /*#__PURE__*/_jsxDEV(List.Item, {\n                onClick: () => handleDeviceSelect(device),\n                className: (selectedDevice === null || selectedDevice === void 0 ? void 0 : selectedDevice.id) === device.id ? 'selected-device' : '',\n                style: {\n                  cursor: 'pointer',\n                  padding: '8px 12px',\n                  backgroundColor: (selectedDevice === null || selectedDevice === void 0 ? void 0 : selectedDevice.id) === device.id ? '#f0f5ff' : 'transparent',\n                  borderRadius: '4px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    width: '100%'\n                  },\n                  children: [renderStatusIcon(device.status), /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginLeft: 8,\n                      flex: 1\n                    },\n                    children: device.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 356,\n                    columnNumber: 25\n                  }, this), renderStatusTag(device.status)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 23\n                }, this)\n              }, device.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Empty, {\n              description: \"\\u6682\\u65E0\\u8BBE\\u5907\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8BBE\\u5907\\u8BE6\\u7EC6\\u4FE1\\u606F\",\n          bordered: false,\n          height: \"50%\",\n          children: /*#__PURE__*/_jsxDEV(Spin, {\n            spinning: loading,\n            children: selectedDevice ? /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                gutter: [16, 16],\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u8BBE\\u5907ID:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 26\n                    }, this), \" \", selectedDevice.id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u8BBE\\u5907\\u540D\\u79F0:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 26\n                    }, this), \" \", selectedDevice.name]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u8BBE\\u5907\\u7C7B\\u578B:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 26\n                    }, this), \" \", renderDeviceType(selectedDevice.type)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u72B6\\u6001:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 378,\n                      columnNumber: 26\n                    }, this), \" \", renderStatusTag(selectedDevice.status)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u4F4D\\u7F6E:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 379,\n                      columnNumber: 26\n                    }, this), \" \", selectedDevice.location]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"IP\\u5730\\u5740:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 380,\n                      columnNumber: 26\n                    }, this), \" \", selectedDevice.ipAddress]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  span: 12,\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u6700\\u540E\\u7EF4\\u62A4\\u65F6\\u95F4:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 26\n                    }, this), \" \", selectedDevice.lastMaintenance ? new Date(selectedDevice.lastMaintenance).toLocaleString() : '无记录']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u5B89\\u88C5\\u65E5\\u671F:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 384,\n                      columnNumber: 26\n                    }, this), \" \", selectedDevice.installationDate ? new Date(selectedDevice.installationDate).toLocaleString() : '无记录']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u5236\\u9020\\u5546:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 26\n                    }, this), \" \", selectedDevice.manufacturer || '未知']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u578B\\u53F7:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 386,\n                      columnNumber: 26\n                    }, this), \" \", selectedDevice.model || '未知']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"\\u63CF\\u8FF0:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 387,\n                      columnNumber: 26\n                    }, this), \" \", selectedDevice.description || '无']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 382,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 24\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: \"\\u8BBE\\u5907\\u72B6\\u6001\\u76D1\\u63A7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    color: '#999'\n                  },\n                  children: \"\\u8BBE\\u5907\\u5B9E\\u65F6\\u76D1\\u63A7\\u6570\\u636E\\u5C06\\u5728\\u8FD9\\u91CC\\u663E\\u793A...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Empty, {\n              description: \"\\u8BF7\\u9009\\u62E9\\u4E00\\u4E2A\\u8BBE\\u5907\\u67E5\\u770B\\u8BE6\\u60C5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 268,\n    columnNumber: 5\n  }, this);\n};\n_s(DeviceStatus, \"INN33M8xfQmnsYukYAN6pXNxCJQ=\");\n_c5 = DeviceStatus;\nexport default DeviceStatus;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"InfoCard\");\n$RefreshReg$(_c4, \"CompactStatistic\");\n$RefreshReg$(_c5, \"DeviceStatus\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Row", "Col", "Card", "Statistic", "List", "Descriptions", "Spin", "Badge", "Tag", "<PERSON><PERSON>", "Empty", "echarts", "<PERSON><PERSON><PERSON>", "GridComponent", "TooltipComponent", "LegendComponent", "TitleComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "CollapsibleSidebar", "CheckCircleOutlined", "CloseCircleOutlined", "WarningOutlined", "SyncOutlined", "axios", "message", "jsxDEV", "_jsxDEV", "use", "<PERSON><PERSON><PERSON><PERSON>", "div", "_c", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "_c2", "InfoCard", "height", "_c3", "CompactStatistic", "_c4", "DeviceStatus", "_s", "loading", "setLoading", "devices", "setDevices", "selected<PERSON><PERSON><PERSON>", "setSelectedDevice", "stats", "setStats", "total", "online", "offline", "warning", "deviceTypeChartRef", "deviceStatusChartRef", "setLeftCollapsed", "setRightCollapsed", "fetchDevices", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "headers", "localStorage", "getItem", "data", "success", "deviceData", "length", "filter", "d", "status", "_response$data", "error", "console", "interval", "setInterval", "clearInterval", "renderStatusIcon", "style", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderStatusTag", "statusMap", "text", "maintenance", "statusInfo", "children", "renderDeviceType", "type", "typeMap", "camera", "access_control", "sensor", "broadcast", "other", "handleDeviceSelect", "device", "deviceColumns", "title", "dataIndex", "key", "width", "render", "health", "getHealthColor", "value", "spinning", "tip", "position", "collapsed", "onCollapse", "bordered", "gutter", "span", "valueStyle", "suffix", "ref", "onClick", "dataSource", "renderItem", "<PERSON><PERSON>", "className", "id", "cursor", "padding", "backgroundColor", "borderRadius", "display", "alignItems", "marginLeft", "flex", "name", "description", "location", "ip<PERSON><PERSON><PERSON>", "lastMaintenance", "Date", "toLocaleString", "installationDate", "manufacturer", "model", "marginTop", "_c5", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/DeviceStatus.jsx"], "sourcesContent": ["// src/pages/DeviceStatus.jsx\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport { Row, Col, Card, Statistic, List, Descriptions, Spin, Badge, Tag, Button, Empty } from 'antd';\r\nimport * as echarts from 'echarts/core';\r\nimport { Pie<PERSON>hart } from 'echarts/charts';\r\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\r\nimport { CanvasRenderer } from 'echarts/renderers';\r\nimport styled from 'styled-components';\r\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\r\nimport { CheckCircleOutlined, CloseCircleOutlined, WarningOutlined, SyncOutlined } from '@ant-design/icons';\r\nimport axios from 'axios';\r\nimport { message } from 'antd';\r\n\r\n// 注册必要的echarts组件\r\necharts.use([PieChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\r\n\r\n// 页面布局容器\r\nconst PageContainer = styled.div`\r\n  display: flex;\r\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\r\n  overflow: hidden;\r\n`;\r\n\r\n// 左侧信息栏容器\r\nconst LeftSidebar = styled.div`\r\n  width: 320px;\r\n  height: 100%;\r\n  padding: 0 8px 0 0;\r\n  border-right: 1px solid #f0f0f0;\r\n  display: flex;\r\n  flex-direction: column;\r\n`;\r\n\r\n// 右侧信息栏容器\r\nconst RightSidebar = styled.div`\r\n  width: 320px;\r\n  height: 100%;\r\n  padding: 0 0 0 8px;\r\n  border-left: 1px solid #f0f0f0;\r\n  display: flex;\r\n  flex-direction: column;\r\n`;\r\n\r\n// 主内容区域\r\nconst MainContent = styled.div`\r\n  flex: 1;\r\n  height: 100%;\r\n  overflow: hidden;\r\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \r\n    props.leftCollapsed ? '0 8px 0 24px' : \r\n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\r\n  transition: all 0.3s ease;\r\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \r\n    props.leftCollapsed ? '0 8px 0 0' : \r\n    props.rightCollapsed ? '0 0 0 8px' : '0'};\r\n  position: relative;\r\n  z-index: 1;\r\n`;\r\n\r\n// 信息卡片\r\nconst InfoCard = styled(Card)`\r\n  margin-bottom: 12px;\r\n  height: ${props => props.height || 'auto'};\r\n  \r\n  .ant-card-head {\r\n    min-height: 40px;\r\n    padding: 0 12px;\r\n  }\r\n  \r\n  .ant-card-head-title {\r\n    padding: 8px 0;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .ant-card-body {\r\n    padding: 12px;\r\n    font-size: 13px;\r\n    height: calc(100% - 40px); // 减去卡片头部高度\r\n    overflow-y: auto;\r\n  }\r\n  \r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n`;\r\n\r\n// 自定义统计数字组件\r\nconst CompactStatistic = styled(Statistic)`\r\n  .ant-statistic-title {\r\n    font-size: 12px;\r\n    margin-bottom: 2px;\r\n  }\r\n  \r\n  .ant-statistic-content {\r\n    font-size: 16px;\r\n  }\r\n`;\r\n\r\nconst DeviceStatus = () => {\r\n  const [loading, setLoading] = useState(true);\r\n  const [devices, setDevices] = useState([]);\r\n  const [selectedDevice, setSelectedDevice] = useState(null);\r\n  const [stats, setStats] = useState({\r\n    total: 0,\r\n    online: 0,\r\n    offline: 0,\r\n    warning: 0\r\n  });\r\n  \r\n  const deviceTypeChartRef = useRef(null);\r\n  const deviceStatusChartRef = useRef(null);\r\n  \r\n  // 添加侧边栏折叠状态\r\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\r\n  const [rightCollapsed, setRightCollapsed] = useState(false);\r\n  \r\n  // 获取设备列表\r\n  const fetchDevices = async () => {\r\n    try {\r\n      setLoading(true);\r\n      \r\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n      const response = await axios.get(`${apiUrl}/api/devices`, {\r\n        headers: { \r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\r\n          'Content-Type': 'application/json'\r\n        }\r\n      });\r\n      \r\n      if (response.data && response.data.success) {\r\n        const deviceData = response.data.data || [];\r\n        setDevices(deviceData);\r\n        \r\n        // 计算统计数据\r\n        const stats = {\r\n          total: deviceData.length,\r\n          online: deviceData.filter(d => d.status === 'online').length,\r\n          offline: deviceData.filter(d => d.status === 'offline').length,\r\n          warning: deviceData.filter(d => d.status === 'warning' || d.status === 'error').length\r\n        };\r\n        setStats(stats);\r\n        \r\n        // 默认选中第一个设备\r\n        if (deviceData.length > 0 && !selectedDevice) {\r\n          setSelectedDevice(deviceData[0]);\r\n        }\r\n      } else {\r\n        message.warning('获取设备列表失败: ' + (response.data?.message || '未知错误'));\r\n      }\r\n    } catch (error) {\r\n      console.error('获取设备列表失败:', error);\r\n      message.error('获取设备列表失败: ' + (error.message || '未知错误'));\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchDevices();\r\n    \r\n    // 设置定时刷新\r\n    const interval = setInterval(() => {\r\n      fetchDevices();\r\n    }, 30000); // 每30秒刷新一次\r\n    \r\n    return () => clearInterval(interval);\r\n  }, []);\r\n  \r\n  // 渲染状态图标\r\n  const renderStatusIcon = (status) => {\r\n    switch (status) {\r\n      case 'online':\r\n        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\r\n      case 'offline':\r\n        return <CloseCircleOutlined style={{ color: '#bfbfbf' }} />;\r\n      case 'warning':\r\n        return <WarningOutlined style={{ color: '#faad14' }} />;\r\n      case 'error':\r\n        return <CloseCircleOutlined style={{ color: '#f5222d' }} />;\r\n      case 'maintenance':\r\n        return <SyncOutlined style={{ color: '#1890ff' }} />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  // 渲染状态标签\r\n  const renderStatusTag = (status) => {\r\n    const statusMap = {\r\n      online: { color: 'green', text: '在线' },\r\n      offline: { color: 'gray', text: '离线' },\r\n      warning: { color: 'orange', text: '警告' },\r\n      error: { color: 'red', text: '错误' },\r\n      maintenance: { color: 'blue', text: '维护中' }\r\n    };\r\n    \r\n    const statusInfo = statusMap[status] || { color: 'default', text: status };\r\n    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;\r\n  };\r\n\r\n  // 渲染设备类型\r\n  const renderDeviceType = (type) => {\r\n    const typeMap = {\r\n      camera: '摄像头',\r\n      access_control: '门禁系统',\r\n      sensor: '传感器',\r\n      broadcast: '广播系统',\r\n      other: '其他设备'\r\n    };\r\n    return typeMap[type] || type;\r\n  };\r\n  \r\n  // 处理设备选择\r\n  const handleDeviceSelect = (device) => {\r\n    setSelectedDevice(device);\r\n  };\r\n  \r\n  // 设备列表列定义\r\n  const deviceColumns = [\r\n    {\r\n      title: '设备名称',\r\n      dataIndex: 'name',\r\n      key: 'name',\r\n      width: '50%',\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'status',\r\n      key: 'status',\r\n      width: '25%',\r\n      render: status => (\r\n        <Badge \r\n          status={status === 'online' ? 'success' : 'error'} \r\n          text={status === 'online' ? '在线' : '离线'} \r\n        />\r\n      ),\r\n    },\r\n    {\r\n      title: '健康度',\r\n      dataIndex: 'health',\r\n      key: 'health',\r\n      width: '25%',\r\n      render: health => {\r\n        let color = 'green';\r\n        let text = '正常';\r\n        \r\n        if (health === 'warning') {\r\n          color = 'orange';\r\n          text = '警告';\r\n        } else if (health === 'error') {\r\n          color = 'red';\r\n          text = '错误';\r\n        }\r\n        \r\n        return <Tag color={color}>{text}</Tag>;\r\n      },\r\n    }\r\n  ];\r\n  \r\n  // 获取健康状态颜色\r\n  const getHealthColor = (value) => {\r\n    if (value >= 80) return '#f5222d'; // 红色\r\n    if (value >= 60) return '#faad14'; // 黄色\r\n    return '#52c41a'; // 绿色\r\n  };\r\n  \r\n  return (\r\n    <Spin spinning={loading} tip=\"加载中...\">\r\n      <PageContainer>\r\n        {/* 左侧信息栏 */}\r\n        <CollapsibleSidebar \r\n          position=\"left\"\r\n          collapsed={leftCollapsed}\r\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\r\n        >\r\n          {/* 设备总数统计 */}\r\n          <InfoCard title=\"设备总数统计\" bordered={false} height=\"150px\">\r\n            <Row gutter={[8, 8]}>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"设备总数\" \r\n                  value={stats.total} \r\n                  valueStyle={{ color: '#3f8600' }} \r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"在线设备\" \r\n                  value={stats.online} \r\n                  suffix={`/ ${stats.total}`} \r\n                  valueStyle={{ color: '#3f8600' }} \r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"离线设备\" \r\n                  value={stats.offline} \r\n                  suffix={`/ ${stats.total}`} \r\n                  valueStyle={{ color: '#bfbfbf' }} \r\n                />\r\n              </Col>\r\n              <Col span={8}>\r\n                <CompactStatistic \r\n                  title=\"异常设备\" \r\n                  value={stats.warning} \r\n                  suffix={`/ ${stats.total}`} \r\n                  valueStyle={{ color: '#faad14' }} \r\n                />\r\n              </Col>\r\n            </Row>\r\n          </InfoCard>\r\n          \r\n          {/* 设备类型分布 */}\r\n          <InfoCard title=\"设备类型分布\" bordered={false} height=\"calc(50% - 81px)\">\r\n            <div ref={deviceTypeChartRef} style={{ height: '100%', width: '100%' }}></div>\r\n          </InfoCard>\r\n          \r\n          {/* 设备状态分布 */}\r\n          <InfoCard title=\"设备状态分布\" bordered={false} height=\"calc(50% - 81px)\">\r\n            <div ref={deviceStatusChartRef} style={{ height: '100%', width: '100%' }}></div>\r\n          </InfoCard>\r\n        </CollapsibleSidebar>\r\n        \r\n        {/* 主内容区域 */}\r\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\r\n          {/* 保持空白，不显示任何内容 */}\r\n        </MainContent>\r\n        \r\n        {/* 右侧信息栏 */}\r\n        <CollapsibleSidebar\r\n          position=\"right\"\r\n          collapsed={rightCollapsed}\r\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\r\n        >\r\n          {/* 设备列表 */}\r\n          <InfoCard title=\"设备列表\" bordered={false} height=\"50%\">\r\n            <Button type=\"link\" onClick={fetchDevices}>刷新</Button>\r\n            <Spin spinning={loading}>\r\n              {devices.length > 0 ? (\r\n                <List\r\n                  dataSource={devices}\r\n                  renderItem={device => (\r\n                    <List.Item \r\n                      key={device.id}\r\n                      onClick={() => handleDeviceSelect(device)}\r\n                      className={selectedDevice?.id === device.id ? 'selected-device' : ''}\r\n                      style={{ \r\n                        cursor: 'pointer', \r\n                        padding: '8px 12px',\r\n                        backgroundColor: selectedDevice?.id === device.id ? '#f0f5ff' : 'transparent',\r\n                        borderRadius: '4px'\r\n                      }}\r\n                    >\r\n                      <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>\r\n                        {renderStatusIcon(device.status)}\r\n                        <span style={{ marginLeft: 8, flex: 1 }}>{device.name}</span>\r\n                        {renderStatusTag(device.status)}\r\n                      </div>\r\n                    </List.Item>\r\n                  )}\r\n                />\r\n              ) : (\r\n                <Empty description=\"暂无设备数据\" />\r\n              )}\r\n            </Spin>\r\n          </InfoCard>\r\n          \r\n          {/* 设备详细信息 */}\r\n          <InfoCard title=\"设备详细信息\" bordered={false} height=\"50%\">\r\n            <Spin spinning={loading}>\r\n              {selectedDevice ? (\r\n                <div>\r\n                  <Row gutter={[16, 16]}>\r\n                    <Col span={12}>\r\n                      <p><strong>设备ID:</strong> {selectedDevice.id}</p>\r\n                      <p><strong>设备名称:</strong> {selectedDevice.name}</p>\r\n                      <p><strong>设备类型:</strong> {renderDeviceType(selectedDevice.type)}</p>\r\n                      <p><strong>状态:</strong> {renderStatusTag(selectedDevice.status)}</p>\r\n                      <p><strong>位置:</strong> {selectedDevice.location}</p>\r\n                      <p><strong>IP地址:</strong> {selectedDevice.ipAddress}</p>\r\n                    </Col>\r\n                    <Col span={12}>\r\n                      <p><strong>最后维护时间:</strong> {selectedDevice.lastMaintenance ? new Date(selectedDevice.lastMaintenance).toLocaleString() : '无记录'}</p>\r\n                      <p><strong>安装日期:</strong> {selectedDevice.installationDate ? new Date(selectedDevice.installationDate).toLocaleString() : '无记录'}</p>\r\n                      <p><strong>制造商:</strong> {selectedDevice.manufacturer || '未知'}</p>\r\n                      <p><strong>型号:</strong> {selectedDevice.model || '未知'}</p>\r\n                      <p><strong>描述:</strong> {selectedDevice.description || '无'}</p>\r\n                    </Col>\r\n                  </Row>\r\n                  \r\n                  {/* 设备状态图表和实时数据可以在这里添加 */}\r\n                  <div style={{ marginTop: 24 }}>\r\n                    <h3>设备状态监控</h3>\r\n                    <p style={{ color: '#999' }}>设备实时监控数据将在这里显示...</p>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <Empty description=\"请选择一个设备查看详情\" />\r\n              )}\r\n            </Spin>\r\n          </InfoCard>\r\n        </CollapsibleSidebar>\r\n      </PageContainer>\r\n    </Spin>\r\n  );\r\n};\r\n\r\nexport default DeviceStatus;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AACrG,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,QAAQ,oBAAoB;AACrG,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,yCAAyC;AACxE,SAASC,mBAAmB,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,YAAY,QAAQ,mBAAmB;AAC3G,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,MAAM;;AAE9B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAhB,OAAO,CAACiB,GAAG,CAAC,CAAChB,QAAQ,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,cAAc,CAAC,CAAC;;AAEzG;AACA,MAAMY,aAAa,GAAGX,MAAM,CAACY,GAAG;AAChC;AACA;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GANMF,aAAa;AAOnB,MAAMG,WAAW,GAAGd,MAAM,CAACY,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMG,YAAY,GAAGf,MAAM,CAACY,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMI,WAAW,GAAGhB,MAAM,CAACY,GAAG;AAC9B;AACA;AACA;AACA,aAAaK,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACxEF,KAAK,CAACC,aAAa,GAAG,cAAc,GACpCD,KAAK,CAACE,cAAc,GAAG,cAAc,GAAG,OAAO;AACnD;AACA,YAAYF,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACvEF,KAAK,CAACC,aAAa,GAAG,WAAW,GACjCD,KAAK,CAACE,cAAc,GAAG,WAAW,GAAG,GAAG;AAC5C;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfMJ,WAAW;AAgBjB,MAAMK,QAAQ,GAAGrB,MAAM,CAAChB,IAAI,CAAC;AAC7B;AACA,YAAYiC,KAAK,IAAIA,KAAK,CAACK,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GA1BMF,QAAQ;AA2Bd,MAAMG,gBAAgB,GAAGxB,MAAM,CAACf,SAAS,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwC,GAAA,GATID,gBAAgB;AAWtB,MAAME,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqD,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAC;IACjCyD,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAMC,kBAAkB,GAAG3D,MAAM,CAAC,IAAI,CAAC;EACvC,MAAM4D,oBAAoB,GAAG5D,MAAM,CAAC,IAAI,CAAC;;EAEzC;EACA,MAAM,CAACqC,aAAa,EAAEwB,gBAAgB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwC,cAAc,EAAEwB,iBAAiB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAMiE,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMgB,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAM3C,KAAK,CAAC4C,GAAG,CAAC,GAAGL,MAAM,cAAc,EAAE;QACxDM,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;UAC1D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAIJ,QAAQ,CAACK,IAAI,IAAIL,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;QAC1C,MAAMC,UAAU,GAAGP,QAAQ,CAACK,IAAI,CAACA,IAAI,IAAI,EAAE;QAC3CvB,UAAU,CAACyB,UAAU,CAAC;;QAEtB;QACA,MAAMtB,KAAK,GAAG;UACZE,KAAK,EAAEoB,UAAU,CAACC,MAAM;UACxBpB,MAAM,EAAEmB,UAAU,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,QAAQ,CAAC,CAACH,MAAM;UAC5DnB,OAAO,EAAEkB,UAAU,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,CAAC,CAACH,MAAM;UAC9DlB,OAAO,EAAEiB,UAAU,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,SAAS,IAAID,CAAC,CAACC,MAAM,KAAK,OAAO,CAAC,CAACH;QAClF,CAAC;QACDtB,QAAQ,CAACD,KAAK,CAAC;;QAEf;QACA,IAAIsB,UAAU,CAACC,MAAM,GAAG,CAAC,IAAI,CAACzB,cAAc,EAAE;UAC5CC,iBAAiB,CAACuB,UAAU,CAAC,CAAC,CAAC,CAAC;QAClC;MACF,CAAC,MAAM;QAAA,IAAAK,cAAA;QACLtD,OAAO,CAACgC,OAAO,CAAC,YAAY,IAAI,EAAAsB,cAAA,GAAAZ,QAAQ,CAACK,IAAI,cAAAO,cAAA,uBAAbA,cAAA,CAAetD,OAAO,KAAI,MAAM,CAAC,CAAC;MACpE;IACF,CAAC,CAAC,OAAOuD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCvD,OAAO,CAACuD,KAAK,CAAC,YAAY,IAAIA,KAAK,CAACvD,OAAO,IAAI,MAAM,CAAC,CAAC;IACzD,CAAC,SAAS;MACRsB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDjD,SAAS,CAAC,MAAM;IACdgE,YAAY,CAAC,CAAC;;IAEd;IACA,MAAMoB,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCrB,YAAY,CAAC,CAAC;IAChB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMsB,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,gBAAgB,GAAIP,MAAM,IAAK;IACnC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,oBAAOnD,OAAA,CAACP,mBAAmB;UAACkE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK,SAAS;QACZ,oBAAOhE,OAAA,CAACN,mBAAmB;UAACiE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK,SAAS;QACZ,oBAAOhE,OAAA,CAACL,eAAe;UAACgE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzD,KAAK,OAAO;QACV,oBAAOhE,OAAA,CAACN,mBAAmB;UAACiE,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7D,KAAK,aAAa;QAChB,oBAAOhE,OAAA,CAACJ,YAAY;UAAC+D,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtD;QACE,OAAO,IAAI;IACf;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAId,MAAM,IAAK;IAClC,MAAMe,SAAS,GAAG;MAChBtC,MAAM,EAAE;QAAEgC,KAAK,EAAE,OAAO;QAAEO,IAAI,EAAE;MAAK,CAAC;MACtCtC,OAAO,EAAE;QAAE+B,KAAK,EAAE,MAAM;QAAEO,IAAI,EAAE;MAAK,CAAC;MACtCrC,OAAO,EAAE;QAAE8B,KAAK,EAAE,QAAQ;QAAEO,IAAI,EAAE;MAAK,CAAC;MACxCd,KAAK,EAAE;QAAEO,KAAK,EAAE,KAAK;QAAEO,IAAI,EAAE;MAAK,CAAC;MACnCC,WAAW,EAAE;QAAER,KAAK,EAAE,MAAM;QAAEO,IAAI,EAAE;MAAM;IAC5C,CAAC;IAED,MAAME,UAAU,GAAGH,SAAS,CAACf,MAAM,CAAC,IAAI;MAAES,KAAK,EAAE,SAAS;MAAEO,IAAI,EAAEhB;IAAO,CAAC;IAC1E,oBAAOnD,OAAA,CAACnB,GAAG;MAAC+E,KAAK,EAAES,UAAU,CAACT,KAAM;MAAAU,QAAA,EAAED,UAAU,CAACF;IAAI;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMO,gBAAgB,GAAIC,IAAI,IAAK;IACjC,MAAMC,OAAO,GAAG;MACdC,MAAM,EAAE,KAAK;MACbC,cAAc,EAAE,MAAM;MACtBC,MAAM,EAAE,KAAK;MACbC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE;IACT,CAAC;IACD,OAAOL,OAAO,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC9B,CAAC;;EAED;EACA,MAAMO,kBAAkB,GAAIC,MAAM,IAAK;IACrCxD,iBAAiB,CAACwD,MAAM,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,CACpB;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEnC,MAAM,iBACZnD,OAAA,CAACpB,KAAK;MACJuE,MAAM,EAAEA,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;MAClDgB,IAAI,EAAEhB,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;IAAK;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC;EAEL,CAAC,EACD;IACEkB,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEC,MAAM,IAAI;MAChB,IAAI3B,KAAK,GAAG,OAAO;MACnB,IAAIO,IAAI,GAAG,IAAI;MAEf,IAAIoB,MAAM,KAAK,SAAS,EAAE;QACxB3B,KAAK,GAAG,QAAQ;QAChBO,IAAI,GAAG,IAAI;MACb,CAAC,MAAM,IAAIoB,MAAM,KAAK,OAAO,EAAE;QAC7B3B,KAAK,GAAG,KAAK;QACbO,IAAI,GAAG,IAAI;MACb;MAEA,oBAAOnE,OAAA,CAACnB,GAAG;QAAC+E,KAAK,EAAEA,KAAM;QAAAU,QAAA,EAAEH;MAAI;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IACxC;EACF,CAAC,CACF;;EAED;EACA,MAAMwB,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;IACnC,OAAO,SAAS,CAAC,CAAC;EACpB,CAAC;EAED,oBACEzF,OAAA,CAACrB,IAAI;IAAC+G,QAAQ,EAAEvE,OAAQ;IAACwE,GAAG,EAAC,uBAAQ;IAAArB,QAAA,eACnCtE,OAAA,CAACE,aAAa;MAAAoE,QAAA,gBAEZtE,OAAA,CAACR,kBAAkB;QACjBoG,QAAQ,EAAC,MAAM;QACfC,SAAS,EAAEpF,aAAc;QACzBqF,UAAU,EAAEA,CAAA,KAAM7D,gBAAgB,CAAC,CAACxB,aAAa,CAAE;QAAA6D,QAAA,gBAGnDtE,OAAA,CAACY,QAAQ;UAACsE,KAAK,EAAC,sCAAQ;UAACa,QAAQ,EAAE,KAAM;UAAClF,MAAM,EAAC,OAAO;UAAAyD,QAAA,eACtDtE,OAAA,CAAC3B,GAAG;YAAC2H,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;YAAA1B,QAAA,gBAClBtE,OAAA,CAAC1B,GAAG;cAAC2H,IAAI,EAAE,CAAE;cAAA3B,QAAA,eACXtE,OAAA,CAACe,gBAAgB;gBACfmE,KAAK,EAAC,0BAAM;gBACZO,KAAK,EAAEhE,KAAK,CAACE,KAAM;gBACnBuE,UAAU,EAAE;kBAAEtC,KAAK,EAAE;gBAAU;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhE,OAAA,CAAC1B,GAAG;cAAC2H,IAAI,EAAE,CAAE;cAAA3B,QAAA,eACXtE,OAAA,CAACe,gBAAgB;gBACfmE,KAAK,EAAC,0BAAM;gBACZO,KAAK,EAAEhE,KAAK,CAACG,MAAO;gBACpBuE,MAAM,EAAE,KAAK1E,KAAK,CAACE,KAAK,EAAG;gBAC3BuE,UAAU,EAAE;kBAAEtC,KAAK,EAAE;gBAAU;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhE,OAAA,CAAC1B,GAAG;cAAC2H,IAAI,EAAE,CAAE;cAAA3B,QAAA,eACXtE,OAAA,CAACe,gBAAgB;gBACfmE,KAAK,EAAC,0BAAM;gBACZO,KAAK,EAAEhE,KAAK,CAACI,OAAQ;gBACrBsE,MAAM,EAAE,KAAK1E,KAAK,CAACE,KAAK,EAAG;gBAC3BuE,UAAU,EAAE;kBAAEtC,KAAK,EAAE;gBAAU;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhE,OAAA,CAAC1B,GAAG;cAAC2H,IAAI,EAAE,CAAE;cAAA3B,QAAA,eACXtE,OAAA,CAACe,gBAAgB;gBACfmE,KAAK,EAAC,0BAAM;gBACZO,KAAK,EAAEhE,KAAK,CAACK,OAAQ;gBACrBqE,MAAM,EAAE,KAAK1E,KAAK,CAACE,KAAK,EAAG;gBAC3BuE,UAAU,EAAE;kBAAEtC,KAAK,EAAE;gBAAU;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGXhE,OAAA,CAACY,QAAQ;UAACsE,KAAK,EAAC,sCAAQ;UAACa,QAAQ,EAAE,KAAM;UAAClF,MAAM,EAAC,kBAAkB;UAAAyD,QAAA,eACjEtE,OAAA;YAAKoG,GAAG,EAAErE,kBAAmB;YAAC4B,KAAK,EAAE;cAAE9C,MAAM,EAAE,MAAM;cAAEwE,KAAK,EAAE;YAAO;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC,eAGXhE,OAAA,CAACY,QAAQ;UAACsE,KAAK,EAAC,sCAAQ;UAACa,QAAQ,EAAE,KAAM;UAAClF,MAAM,EAAC,kBAAkB;UAAAyD,QAAA,eACjEtE,OAAA;YAAKoG,GAAG,EAAEpE,oBAAqB;YAAC2B,KAAK,EAAE;cAAE9C,MAAM,EAAE,MAAM;cAAEwE,KAAK,EAAE;YAAO;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGrBhE,OAAA,CAACO,WAAW;QAACE,aAAa,EAAEA,aAAc;QAACC,cAAc,EAAEA;MAAe;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC,eAGdhE,OAAA,CAACR,kBAAkB;QACjBoG,QAAQ,EAAC,OAAO;QAChBC,SAAS,EAAEnF,cAAe;QAC1BoF,UAAU,EAAEA,CAAA,KAAM5D,iBAAiB,CAAC,CAACxB,cAAc,CAAE;QAAA4D,QAAA,gBAGrDtE,OAAA,CAACY,QAAQ;UAACsE,KAAK,EAAC,0BAAM;UAACa,QAAQ,EAAE,KAAM;UAAClF,MAAM,EAAC,KAAK;UAAAyD,QAAA,gBAClDtE,OAAA,CAAClB,MAAM;YAAC0F,IAAI,EAAC,MAAM;YAAC6B,OAAO,EAAElE,YAAa;YAAAmC,QAAA,EAAC;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtDhE,OAAA,CAACrB,IAAI;YAAC+G,QAAQ,EAAEvE,OAAQ;YAAAmD,QAAA,EACrBjD,OAAO,CAAC2B,MAAM,GAAG,CAAC,gBACjBhD,OAAA,CAACvB,IAAI;cACH6H,UAAU,EAAEjF,OAAQ;cACpBkF,UAAU,EAAEvB,MAAM,iBAChBhF,OAAA,CAACvB,IAAI,CAAC+H,IAAI;gBAERH,OAAO,EAAEA,CAAA,KAAMtB,kBAAkB,CAACC,MAAM,CAAE;gBAC1CyB,SAAS,EAAE,CAAAlF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEmF,EAAE,MAAK1B,MAAM,CAAC0B,EAAE,GAAG,iBAAiB,GAAG,EAAG;gBACrE/C,KAAK,EAAE;kBACLgD,MAAM,EAAE,SAAS;kBACjBC,OAAO,EAAE,UAAU;kBACnBC,eAAe,EAAE,CAAAtF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEmF,EAAE,MAAK1B,MAAM,CAAC0B,EAAE,GAAG,SAAS,GAAG,aAAa;kBAC7EI,YAAY,EAAE;gBAChB,CAAE;gBAAAxC,QAAA,eAEFtE,OAAA;kBAAK2D,KAAK,EAAE;oBAAEoD,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAE3B,KAAK,EAAE;kBAAO,CAAE;kBAAAf,QAAA,GAClEZ,gBAAgB,CAACsB,MAAM,CAAC7B,MAAM,CAAC,eAChCnD,OAAA;oBAAM2D,KAAK,EAAE;sBAAEsD,UAAU,EAAE,CAAC;sBAAEC,IAAI,EAAE;oBAAE,CAAE;oBAAA5C,QAAA,EAAEU,MAAM,CAACmC;kBAAI;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EAC5DC,eAAe,CAACe,MAAM,CAAC7B,MAAM,CAAC;gBAAA;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B;cAAC,GAdDgB,MAAM,CAAC0B,EAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAeL;YACX;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEFhE,OAAA,CAACjB,KAAK;cAACqI,WAAW,EAAC;YAAQ;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAC9B;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGXhE,OAAA,CAACY,QAAQ;UAACsE,KAAK,EAAC,sCAAQ;UAACa,QAAQ,EAAE,KAAM;UAAClF,MAAM,EAAC,KAAK;UAAAyD,QAAA,eACpDtE,OAAA,CAACrB,IAAI;YAAC+G,QAAQ,EAAEvE,OAAQ;YAAAmD,QAAA,EACrB/C,cAAc,gBACbvB,OAAA;cAAAsE,QAAA,gBACEtE,OAAA,CAAC3B,GAAG;gBAAC2H,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;gBAAA1B,QAAA,gBACpBtE,OAAA,CAAC1B,GAAG;kBAAC2H,IAAI,EAAE,EAAG;kBAAA3B,QAAA,gBACZtE,OAAA;oBAAAsE,QAAA,gBAAGtE,OAAA;sBAAAsE,QAAA,EAAQ;oBAAK;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACzC,cAAc,CAACmF,EAAE;kBAAA;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjDhE,OAAA;oBAAAsE,QAAA,gBAAGtE,OAAA;sBAAAsE,QAAA,EAAQ;oBAAK;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACzC,cAAc,CAAC4F,IAAI;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACnDhE,OAAA;oBAAAsE,QAAA,gBAAGtE,OAAA;sBAAAsE,QAAA,EAAQ;oBAAK;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACO,gBAAgB,CAAChD,cAAc,CAACiD,IAAI,CAAC;kBAAA;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrEhE,OAAA;oBAAAsE,QAAA,gBAAGtE,OAAA;sBAAAsE,QAAA,EAAQ;oBAAG;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACC,eAAe,CAAC1C,cAAc,CAAC4B,MAAM,CAAC;kBAAA;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpEhE,OAAA;oBAAAsE,QAAA,gBAAGtE,OAAA;sBAAAsE,QAAA,EAAQ;oBAAG;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACzC,cAAc,CAAC8F,QAAQ;kBAAA;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrDhE,OAAA;oBAAAsE,QAAA,gBAAGtE,OAAA;sBAAAsE,QAAA,EAAQ;oBAAK;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACzC,cAAc,CAAC+F,SAAS;kBAAA;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNhE,OAAA,CAAC1B,GAAG;kBAAC2H,IAAI,EAAE,EAAG;kBAAA3B,QAAA,gBACZtE,OAAA;oBAAAsE,QAAA,gBAAGtE,OAAA;sBAAAsE,QAAA,EAAQ;oBAAO;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACzC,cAAc,CAACgG,eAAe,GAAG,IAAIC,IAAI,CAACjG,cAAc,CAACgG,eAAe,CAAC,CAACE,cAAc,CAAC,CAAC,GAAG,KAAK;kBAAA;oBAAA5D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpIhE,OAAA;oBAAAsE,QAAA,gBAAGtE,OAAA;sBAAAsE,QAAA,EAAQ;oBAAK;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACzC,cAAc,CAACmG,gBAAgB,GAAG,IAAIF,IAAI,CAACjG,cAAc,CAACmG,gBAAgB,CAAC,CAACD,cAAc,CAAC,CAAC,GAAG,KAAK;kBAAA;oBAAA5D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpIhE,OAAA;oBAAAsE,QAAA,gBAAGtE,OAAA;sBAAAsE,QAAA,EAAQ;oBAAI;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACzC,cAAc,CAACoG,YAAY,IAAI,IAAI;kBAAA;oBAAA9D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClEhE,OAAA;oBAAAsE,QAAA,gBAAGtE,OAAA;sBAAAsE,QAAA,EAAQ;oBAAG;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACzC,cAAc,CAACqG,KAAK,IAAI,IAAI;kBAAA;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC1DhE,OAAA;oBAAAsE,QAAA,gBAAGtE,OAAA;sBAAAsE,QAAA,EAAQ;oBAAG;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACzC,cAAc,CAAC6F,WAAW,IAAI,GAAG;kBAAA;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNhE,OAAA;gBAAK2D,KAAK,EAAE;kBAAEkE,SAAS,EAAE;gBAAG,CAAE;gBAAAvD,QAAA,gBAC5BtE,OAAA;kBAAAsE,QAAA,EAAI;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACfhE,OAAA;kBAAG2D,KAAK,EAAE;oBAAEC,KAAK,EAAE;kBAAO,CAAE;kBAAAU,QAAA,EAAC;gBAAiB;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAENhE,OAAA,CAACjB,KAAK;cAACqI,WAAW,EAAC;YAAa;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACnC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEX,CAAC;AAAC9C,EAAA,CAnTID,YAAY;AAAA6G,GAAA,GAAZ7G,YAAY;AAqTlB,eAAeA,YAAY;AAAC,IAAAb,EAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAA8G,GAAA;AAAAC,YAAA,CAAA3H,EAAA;AAAA2H,YAAA,CAAApH,GAAA;AAAAoH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}