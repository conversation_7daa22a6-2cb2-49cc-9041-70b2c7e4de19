{"ast": null, "code": "import * as React from 'react';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport raf from \"rc-util/es/raf\";\nimport { ConfigContext } from '../../config-provider';\nimport useToken from '../../theme/useToken';\nimport { TARGET_CLS } from './interface';\nimport showWaveEffect from './WaveEffect';\nconst useWave = (nodeRef, className, component) => {\n  const {\n    wave\n  } = React.useContext(ConfigContext);\n  const [, token, hashId] = useToken();\n  const showWave = useEvent(event => {\n    const node = nodeRef.current;\n    if ((wave === null || wave === void 0 ? void 0 : wave.disabled) || !node) {\n      return;\n    }\n    const targetNode = node.querySelector(`.${TARGET_CLS}`) || node;\n    const {\n      showEffect\n    } = wave || {};\n    // Customize wave effect\n    (showEffect || showWaveEffect)(targetNode, {\n      className,\n      token,\n      component,\n      event,\n      hashId\n    });\n  });\n  const rafId = React.useRef(null);\n  // Merge trigger event into one for each frame\n  const showDebounceWave = event => {\n    raf.cancel(rafId.current);\n    rafId.current = raf(() => {\n      showWave(event);\n    });\n  };\n  return showDebounceWave;\n};\nexport default useWave;", "map": {"version": 3, "names": ["React", "useEvent", "raf", "ConfigContext", "useToken", "TARGET_CLS", "showWaveEffect", "useWave", "nodeRef", "className", "component", "wave", "useContext", "token", "hashId", "showWave", "event", "node", "current", "disabled", "targetNode", "querySelector", "showEffect", "rafId", "useRef", "showDebounceWave", "cancel"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/_util/wave/useWave.js"], "sourcesContent": ["import * as React from 'react';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport raf from \"rc-util/es/raf\";\nimport { ConfigContext } from '../../config-provider';\nimport useToken from '../../theme/useToken';\nimport { TARGET_CLS } from './interface';\nimport showWaveEffect from './WaveEffect';\nconst useWave = (nodeRef, className, component) => {\n  const {\n    wave\n  } = React.useContext(ConfigContext);\n  const [, token, hashId] = useToken();\n  const showWave = useEvent(event => {\n    const node = nodeRef.current;\n    if ((wave === null || wave === void 0 ? void 0 : wave.disabled) || !node) {\n      return;\n    }\n    const targetNode = node.querySelector(`.${TARGET_CLS}`) || node;\n    const {\n      showEffect\n    } = wave || {};\n    // Customize wave effect\n    (showEffect || showWaveEffect)(targetNode, {\n      className,\n      token,\n      component,\n      event,\n      hashId\n    });\n  });\n  const rafId = React.useRef(null);\n  // Merge trigger event into one for each frame\n  const showDebounceWave = event => {\n    raf.cancel(rafId.current);\n    rafId.current = raf(() => {\n      showWave(event);\n    });\n  };\n  return showDebounceWave;\n};\nexport default useWave;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,aAAa,QAAQ,uBAAuB;AACrD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,UAAU,QAAQ,aAAa;AACxC,OAAOC,cAAc,MAAM,cAAc;AACzC,MAAMC,OAAO,GAAGA,CAACC,OAAO,EAAEC,SAAS,EAAEC,SAAS,KAAK;EACjD,MAAM;IACJC;EACF,CAAC,GAAGX,KAAK,CAACY,UAAU,CAACT,aAAa,CAAC;EACnC,MAAM,GAAGU,KAAK,EAAEC,MAAM,CAAC,GAAGV,QAAQ,CAAC,CAAC;EACpC,MAAMW,QAAQ,GAAGd,QAAQ,CAACe,KAAK,IAAI;IACjC,MAAMC,IAAI,GAAGT,OAAO,CAACU,OAAO;IAC5B,IAAI,CAACP,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACQ,QAAQ,KAAK,CAACF,IAAI,EAAE;MACxE;IACF;IACA,MAAMG,UAAU,GAAGH,IAAI,CAACI,aAAa,CAAC,IAAIhB,UAAU,EAAE,CAAC,IAAIY,IAAI;IAC/D,MAAM;MACJK;IACF,CAAC,GAAGX,IAAI,IAAI,CAAC,CAAC;IACd;IACA,CAACW,UAAU,IAAIhB,cAAc,EAAEc,UAAU,EAAE;MACzCX,SAAS;MACTI,KAAK;MACLH,SAAS;MACTM,KAAK;MACLF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMS,KAAK,GAAGvB,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;EAChC;EACA,MAAMC,gBAAgB,GAAGT,KAAK,IAAI;IAChCd,GAAG,CAACwB,MAAM,CAACH,KAAK,CAACL,OAAO,CAAC;IACzBK,KAAK,CAACL,OAAO,GAAGhB,GAAG,CAAC,MAAM;MACxBa,QAAQ,CAACC,KAAK,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC;EACD,OAAOS,gBAAgB;AACzB,CAAC;AACD,eAAelB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}