{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\DeviceManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, Space, Card, Tag, message } from 'antd';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst DeviceManagement = ({\n  id\n}, ref) => {\n  _s();\n  const [devices, setDevices] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingDevice, setEditingDevice] = useState(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentDevice, setCurrentDevice] = useState(null);\n\n  // 获取设备列表\n  const fetchDevices = async () => {\n    try {\n      setLoading(true);\n      console.log('开始获取设备列表...');\n      const token = localStorage.getItem('token');\n      if (!token) {\n        console.log('未找到token，从本地存储获取设备列表');\n        const localDevices = JSON.parse(localStorage.getItem('localDevices') || '[]');\n        setDevices(localDevices);\n        return;\n      }\n\n      // 移除token中的引号\n      const cleanToken = token.replace(/^\"|\"$/g, '');\n      console.log('清理后的token:', cleanToken);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`, {\n        headers: {\n          'Authorization': `Bearer ${cleanToken}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data && response.data.success) {\n        setDevices(response.data.data || []);\n        // 同时更新本地存储\n        localStorage.setItem('localDevices', JSON.stringify(response.data.data || []));\n      } else {\n        var _response$data;\n        throw new Error(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || '获取设备列表失败');\n      }\n    } catch (error) {\n      var _error$response;\n      console.error('API获取设备列表失败:', error);\n\n      // 如果是认证错误，尝试从本地存储获取\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 403) {\n        const localDevices = JSON.parse(localStorage.getItem('localDevices') || '[]');\n        setDevices(localDevices);\n        message.warning('使用本地缓存的设备数据');\n      } else {\n        message.error('获取设备列表失败: ' + (error.message || '未知错误'));\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchDevices();\n  }, []);\n\n  // 处理添加设备\n  const handleAddDevice = () => {\n    setEditingDevice(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // 处理编辑设备\n  const handleEditDevice = device => {\n    setEditingDevice(device);\n    form.setFieldsValue({\n      name: device.name,\n      type: device.type,\n      status: device.status,\n      location: device.location,\n      ipAddress: device.ipAddress,\n      manufacturer: device.manufacturer,\n      model: device.model,\n      description: device.description\n    });\n    setModalVisible(true);\n  };\n\n  // 处理查看设备详情\n  const handleViewDevice = device => {\n    setCurrentDevice(device);\n    setDetailModalVisible(true);\n  };\n\n  // 处理删除设备\n  const handleDeleteDevice = async deviceId => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      await axios.delete(`${apiUrl}/api/devices/${deviceId}`);\n\n      // 更新本地设备列表\n      const updatedDevices = devices.filter(device => device.id !== deviceId);\n      setDevices(updatedDevices);\n\n      // 更新本地存储\n      localStorage.setItem('localDevices', JSON.stringify(updatedDevices));\n      message.success('设备删除成功');\n    } catch (error) {\n      console.error('删除设备失败:', error);\n      message.error('删除设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 处理表单提交\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n\n      // 首先尝试通过API保存\n      try {\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n        const deviceData = {\n          ...values,\n          id: editingDevice === null || editingDevice === void 0 ? void 0 : editingDevice.id\n        };\n        if (editingDevice) {\n          // 更新设备\n          await axios.put(`${apiUrl}/api/devices/${editingDevice.id}`, deviceData, {\n            headers: {\n              'Authorization': `Bearer ${localStorage.getItem('token')}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        } else {\n          // 添加设备\n          await axios.post(`${apiUrl}/api/devices`, deviceData, {\n            headers: {\n              'Authorization': `Bearer ${localStorage.getItem('token')}`,\n              'Content-Type': 'application/json'\n            }\n          });\n        }\n        message.success(editingDevice ? '设备更新成功' : '设备添加成功');\n        setModalVisible(false);\n        form.resetFields();\n        fetchDevices();\n        return;\n      } catch (error) {\n        console.error('API保存设备失败:', error);\n      }\n\n      // 如果API保存失败，保存到本地存储\n      const localDevices = JSON.parse(localStorage.getItem('localDevices') || '[]');\n      if (editingDevice) {\n        // 更新设备\n        const updatedDevices = localDevices.map(device => device.id === editingDevice.id ? {\n          ...device,\n          ...values\n        } : device);\n        localStorage.setItem('localDevices', JSON.stringify(updatedDevices));\n        setDevices(updatedDevices);\n      } else {\n        // 添加设备\n        const newDevice = {\n          ...values,\n          id: `dev-${Date.now()}`,\n          createdAt: new Date().toISOString()\n        };\n        localDevices.push(newDevice);\n        localStorage.setItem('localDevices', JSON.stringify(localDevices));\n        setDevices(localDevices);\n      }\n      message.success(editingDevice ? '设备更新成功（本地存储）' : '设备添加成功（本地存储）');\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('保存设备失败:', error);\n      message.error('表单验证失败');\n    }\n  };\n\n  // 渲染状态标签\n  const renderStatusTag = status => {\n    const statusMap = {\n      online: {\n        color: 'green',\n        text: '在线'\n      },\n      offline: {\n        color: 'gray',\n        text: '离线'\n      },\n      warning: {\n        color: 'orange',\n        text: '警告'\n      },\n      error: {\n        color: 'red',\n        text: '错误'\n      },\n      maintenance: {\n        color: 'blue',\n        text: '维护中'\n      }\n    };\n    const statusInfo = statusMap[status] || {\n      color: 'default',\n      text: status\n    };\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: statusInfo.color,\n      children: statusInfo.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 12\n    }, this);\n  };\n\n  // 设备类型映射\n  const deviceTypeMap = {\n    camera: '摄像头',\n    access_control: '门禁系统',\n    sensor: '传感器',\n    broadcast: '广播系统',\n    other: '其他设备'\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '设备名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '设备类型',\n    dataIndex: 'type',\n    key: 'type',\n    render: type => deviceTypeMap[type] || type\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: renderStatusTag\n  }, {\n    title: '位置',\n    dataIndex: 'location',\n    key: 'location'\n  }, {\n    title: 'IP地址',\n    dataIndex: 'ipAddress',\n    key: 'ipAddress'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleViewDevice(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleEditDevice(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        danger: true,\n        onClick: () => handleDeleteDevice(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 暴露 fetchDevices 方法\n  useImperativeHandle(ref, () => ({\n    fetchDevices\n  }));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: id,\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u8BBE\\u5907\\u5217\\u8868\",\n      extra: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: handleAddDevice,\n        children: \"\\u6DFB\\u52A0\\u8BBE\\u5907\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 16\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        loading: loading,\n        dataSource: devices,\n        columns: columns,\n        rowKey: \"id\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingDevice ? '编辑设备' : '添加设备',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => setModalVisible(false),\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8BBE\\u5907\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入设备名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"type\",\n          label: \"\\u8BBE\\u5907\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择设备类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u7C7B\\u578B\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"camera\",\n              children: \"\\u6444\\u50CF\\u5934\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"access_control\",\n              children: \"\\u95E8\\u7981\\u7CFB\\u7EDF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"sensor\",\n              children: \"\\u4F20\\u611F\\u5668\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"broadcast\",\n              children: \"\\u5E7F\\u64AD\\u7CFB\\u7EDF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"other\",\n              children: \"\\u5176\\u4ED6\\u8BBE\\u5907\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u8BBE\\u5907\\u72B6\\u6001\",\n          rules: [{\n            required: true,\n            message: '请选择设备状态'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u72B6\\u6001\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"online\",\n              children: \"\\u5728\\u7EBF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"offline\",\n              children: \"\\u79BB\\u7EBF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"warning\",\n              children: \"\\u8B66\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"error\",\n              children: \"\\u9519\\u8BEF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"maintenance\",\n              children: \"\\u7EF4\\u62A4\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"location\",\n          label: \"\\u8BBE\\u5907\\u4F4D\\u7F6E\",\n          rules: [{\n            required: true,\n            message: '请输入设备位置'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u4F4D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"ipAddress\",\n          label: \"IP\\u5730\\u5740\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165IP\\u5730\\u5740\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"manufacturer\",\n          label: \"\\u5236\\u9020\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5236\\u9020\\u5546\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"model\",\n          label: \"\\u578B\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u578B\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u8BBE\\u5907\\u63CF\\u8FF0\",\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            rows: 4,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u8BBE\\u5907\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 11\n      }, this)],\n      width: 600,\n      children: currentDevice && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8BBE\\u5907ID:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 16\n          }, this), \" \", currentDevice.id]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8BBE\\u5907\\u540D\\u79F0:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 16\n          }, this), \" \", currentDevice.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8BBE\\u5907\\u7C7B\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 16\n          }, this), \" \", deviceTypeMap[currentDevice.type] || currentDevice.type]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u72B6\\u6001:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 16\n          }, this), \" \", renderStatusTag(currentDevice.status)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u4F4D\\u7F6E:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 16\n          }, this), \" \", currentDevice.location]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"IP\\u5730\\u5740:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 16\n          }, this), \" \", currentDevice.ipAddress]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u6700\\u540E\\u7EF4\\u62A4\\u65F6\\u95F4:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 16\n          }, this), \" \", currentDevice.lastMaintenance ? new Date(currentDevice.lastMaintenance).toLocaleString() : '无记录']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u5B89\\u88C5\\u65E5\\u671F:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 16\n          }, this), \" \", currentDevice.installationDate ? new Date(currentDevice.installationDate).toLocaleString() : '无记录']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u5236\\u9020\\u5546:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 16\n          }, this), \" \", currentDevice.manufacturer || '未知']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u578B\\u53F7:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 16\n          }, this), \" \", currentDevice.model || '未知']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u63CF\\u8FF0:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 16\n          }, this), \" \", currentDevice.description || '无']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 260,\n    columnNumber: 5\n  }, this);\n};\n_s(DeviceManagement, \"JBbpSIoIFITvqhUHk6IYSBW3Uuo=\", false, function () {\n  return [Form.useForm];\n});\n_c = DeviceManagement;\nexport default _c2 = /*#__PURE__*/forwardRef(DeviceManagement);\nvar _c, _c2;\n$RefreshReg$(_c, \"DeviceManagement\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "Space", "Card", "Tag", "message", "axios", "jsxDEV", "_jsxDEV", "Option", "DeviceManagement", "id", "ref", "_s", "devices", "setDevices", "loading", "setLoading", "modalVisible", "setModalVisible", "form", "useForm", "editingDevice", "setEditingDevice", "detailModalVisible", "setDetailModalVisible", "currentDevice", "setCurrentDevice", "fetchDevices", "console", "log", "token", "localStorage", "getItem", "localDevices", "JSON", "parse", "cleanToken", "replace", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "get", "headers", "data", "success", "setItem", "stringify", "_response$data", "Error", "error", "_error$response", "status", "warning", "handleAddDevice", "resetFields", "handleEditDevice", "device", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "type", "location", "ip<PERSON><PERSON><PERSON>", "manufacturer", "model", "description", "handleViewDevice", "handleDeleteDevice", "deviceId", "delete", "updatedDevices", "filter", "handleModalOk", "values", "validateFields", "deviceData", "put", "post", "map", "newDevice", "Date", "now", "createdAt", "toISOString", "push", "renderStatusTag", "statusMap", "online", "color", "text", "offline", "maintenance", "statusInfo", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "deviceTypeMap", "camera", "access_control", "sensor", "broadcast", "other", "columns", "title", "dataIndex", "key", "render", "_", "record", "size", "onClick", "danger", "extra", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onOk", "onCancel", "width", "layout", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "value", "TextArea", "rows", "footer", "lastMaintenance", "toLocaleString", "installationDate", "_c", "_c2", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/DeviceManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\r\nimport { Table, Button, Modal, Form, Input, Select, Space, Card, Tag, message } from 'antd';\r\nimport axios from 'axios';\r\n\r\nconst { Option } = Select;\r\n\r\nconst DeviceManagement = ({ id }, ref) => {\r\n  const [devices, setDevices] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [form] = Form.useForm();\r\n  const [editingDevice, setEditingDevice] = useState(null);\r\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\r\n  const [currentDevice, setCurrentDevice] = useState(null);\r\n\r\n  // 获取设备列表\r\n  const fetchDevices = async () => {\r\n    try {\r\n      setLoading(true);\r\n      console.log('开始获取设备列表...');\r\n      \r\n      const token = localStorage.getItem('token');\r\n      if (!token) {\r\n        console.log('未找到token，从本地存储获取设备列表');\r\n        const localDevices = JSON.parse(localStorage.getItem('localDevices') || '[]');\r\n        setDevices(localDevices);\r\n        return;\r\n      }\r\n\r\n      // 移除token中的引号\r\n      const cleanToken = token.replace(/^\"|\"$/g, '');\r\n      console.log('清理后的token:', cleanToken);\r\n\r\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n      const response = await axios.get(`${apiUrl}/api/devices`, {\r\n        headers: { \r\n          'Authorization': `Bearer ${cleanToken}`,\r\n          'Content-Type': 'application/json'\r\n        }\r\n      });\r\n      \r\n      if (response.data && response.data.success) {\r\n        setDevices(response.data.data || []);\r\n        // 同时更新本地存储\r\n        localStorage.setItem('localDevices', JSON.stringify(response.data.data || []));\r\n      } else {\r\n        throw new Error(response.data?.message || '获取设备列表失败');\r\n      }\r\n    } catch (error) {\r\n      console.error('API获取设备列表失败:', error);\r\n      \r\n      // 如果是认证错误，尝试从本地存储获取\r\n      if (error.response?.status === 403) {\r\n        const localDevices = JSON.parse(localStorage.getItem('localDevices') || '[]');\r\n        setDevices(localDevices);\r\n        message.warning('使用本地缓存的设备数据');\r\n      } else {\r\n        message.error('获取设备列表失败: ' + (error.message || '未知错误'));\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchDevices();\r\n  }, []);\r\n\r\n  // 处理添加设备\r\n  const handleAddDevice = () => {\r\n    setEditingDevice(null);\r\n    form.resetFields();\r\n    setModalVisible(true);\r\n  };\r\n\r\n  // 处理编辑设备\r\n  const handleEditDevice = (device) => {\r\n    setEditingDevice(device);\r\n    form.setFieldsValue({\r\n      name: device.name,\r\n      type: device.type,\r\n      status: device.status,\r\n      location: device.location,\r\n      ipAddress: device.ipAddress,\r\n      manufacturer: device.manufacturer,\r\n      model: device.model,\r\n      description: device.description\r\n    });\r\n    setModalVisible(true);\r\n  };\r\n\r\n  // 处理查看设备详情\r\n  const handleViewDevice = (device) => {\r\n    setCurrentDevice(device);\r\n    setDetailModalVisible(true);\r\n  };\r\n\r\n  // 处理删除设备\r\n  const handleDeleteDevice = async (deviceId) => {\r\n    try {\r\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n      await axios.delete(`${apiUrl}/api/devices/${deviceId}`);\r\n      \r\n      // 更新本地设备列表\r\n      const updatedDevices = devices.filter(device => device.id !== deviceId);\r\n      setDevices(updatedDevices);\r\n      \r\n      // 更新本地存储\r\n      localStorage.setItem('localDevices', JSON.stringify(updatedDevices));\r\n      \r\n      message.success('设备删除成功');\r\n    } catch (error) {\r\n      console.error('删除设备失败:', error);\r\n      message.error('删除设备失败: ' + (error.message || '未知错误'));\r\n    }\r\n  };\r\n\r\n  // 处理表单提交\r\n  const handleModalOk = async () => {\r\n    try {\r\n      const values = await form.validateFields();\r\n      \r\n      // 首先尝试通过API保存\r\n      try {\r\n        const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\n        const deviceData = {\r\n          ...values,\r\n          id: editingDevice?.id\r\n        };\r\n        \r\n        if (editingDevice) {\r\n          // 更新设备\r\n          await axios.put(`${apiUrl}/api/devices/${editingDevice.id}`, deviceData, {\r\n            headers: { \r\n              'Authorization': `Bearer ${localStorage.getItem('token')}`,\r\n              'Content-Type': 'application/json'\r\n            }\r\n          });\r\n        } else {\r\n          // 添加设备\r\n          await axios.post(`${apiUrl}/api/devices`, deviceData, {\r\n            headers: { \r\n              'Authorization': `Bearer ${localStorage.getItem('token')}`,\r\n              'Content-Type': 'application/json'\r\n            }\r\n          });\r\n        }\r\n        \r\n        message.success(editingDevice ? '设备更新成功' : '设备添加成功');\r\n        setModalVisible(false);\r\n        form.resetFields();\r\n        fetchDevices();\r\n        return;\r\n      } catch (error) {\r\n        console.error('API保存设备失败:', error);\r\n      }\r\n      \r\n      // 如果API保存失败，保存到本地存储\r\n      const localDevices = JSON.parse(localStorage.getItem('localDevices') || '[]');\r\n      \r\n      if (editingDevice) {\r\n        // 更新设备\r\n        const updatedDevices = localDevices.map(device => \r\n          device.id === editingDevice.id ? { ...device, ...values } : device\r\n        );\r\n        localStorage.setItem('localDevices', JSON.stringify(updatedDevices));\r\n        setDevices(updatedDevices);\r\n      } else {\r\n        // 添加设备\r\n        const newDevice = {\r\n          ...values,\r\n          id: `dev-${Date.now()}`,\r\n          createdAt: new Date().toISOString()\r\n        };\r\n        localDevices.push(newDevice);\r\n        localStorage.setItem('localDevices', JSON.stringify(localDevices));\r\n        setDevices(localDevices);\r\n      }\r\n      \r\n      message.success(editingDevice ? '设备更新成功（本地存储）' : '设备添加成功（本地存储）');\r\n      setModalVisible(false);\r\n      form.resetFields();\r\n    } catch (error) {\r\n      console.error('保存设备失败:', error);\r\n      message.error('表单验证失败');\r\n    }\r\n  };\r\n\r\n  // 渲染状态标签\r\n  const renderStatusTag = (status) => {\r\n    const statusMap = {\r\n      online: { color: 'green', text: '在线' },\r\n      offline: { color: 'gray', text: '离线' },\r\n      warning: { color: 'orange', text: '警告' },\r\n      error: { color: 'red', text: '错误' },\r\n      maintenance: { color: 'blue', text: '维护中' }\r\n    };\r\n    \r\n    const statusInfo = statusMap[status] || { color: 'default', text: status };\r\n    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;\r\n  };\r\n\r\n  // 设备类型映射\r\n  const deviceTypeMap = {\r\n    camera: '摄像头',\r\n    access_control: '门禁系统',\r\n    sensor: '传感器',\r\n    broadcast: '广播系统',\r\n    other: '其他设备'\r\n  };\r\n\r\n  // 表格列定义\r\n  const columns = [\r\n    {\r\n      title: '设备名称',\r\n      dataIndex: 'name',\r\n      key: 'name',\r\n    },\r\n    {\r\n      title: '设备类型',\r\n      dataIndex: 'type',\r\n      key: 'type',\r\n      render: (type) => deviceTypeMap[type] || type\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'status',\r\n      key: 'status',\r\n      render: renderStatusTag\r\n    },\r\n    {\r\n      title: '位置',\r\n      dataIndex: 'location',\r\n      key: 'location',\r\n    },\r\n    {\r\n      title: 'IP地址',\r\n      dataIndex: 'ipAddress',\r\n      key: 'ipAddress',\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      render: (_, record) => (\r\n        <Space size=\"small\">\r\n          <Button type=\"link\" onClick={() => handleViewDevice(record)}>查看</Button>\r\n          <Button type=\"link\" onClick={() => handleEditDevice(record)}>编辑</Button>\r\n          <Button type=\"link\" danger onClick={() => handleDeleteDevice(record.id)}>删除</Button>\r\n        </Space>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 暴露 fetchDevices 方法\r\n  useImperativeHandle(ref, () => ({\r\n    fetchDevices\r\n  }));\r\n\r\n  return (\r\n    <div id={id}>\r\n      <Card \r\n        title=\"设备列表\" \r\n        extra={<Button type=\"primary\" onClick={handleAddDevice}>添加设备</Button>}\r\n      >\r\n        <Table \r\n          loading={loading}\r\n          dataSource={devices} \r\n          columns={columns} \r\n          rowKey=\"id\"\r\n        />\r\n      </Card>\r\n\r\n      {/* 添加/编辑设备表单 */}\r\n      <Modal\r\n        title={editingDevice ? '编辑设备' : '添加设备'}\r\n        open={modalVisible}\r\n        onOk={handleModalOk}\r\n        onCancel={() => setModalVisible(false)}\r\n        width={600}\r\n      >\r\n        <Form\r\n          form={form}\r\n          layout=\"vertical\"\r\n        >\r\n          <Form.Item\r\n            name=\"name\"\r\n            label=\"设备名称\"\r\n            rules={[{ required: true, message: '请输入设备名称' }]}\r\n          >\r\n            <Input placeholder=\"请输入设备名称\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"type\"\r\n            label=\"设备类型\"\r\n            rules={[{ required: true, message: '请选择设备类型' }]}\r\n          >\r\n            <Select placeholder=\"请选择设备类型\">\r\n              <Option value=\"camera\">摄像头</Option>\r\n              <Option value=\"access_control\">门禁系统</Option>\r\n              <Option value=\"sensor\">传感器</Option>\r\n              <Option value=\"broadcast\">广播系统</Option>\r\n              <Option value=\"other\">其他设备</Option>\r\n            </Select>\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"status\"\r\n            label=\"设备状态\"\r\n            rules={[{ required: true, message: '请选择设备状态' }]}\r\n          >\r\n            <Select placeholder=\"请选择设备状态\">\r\n              <Option value=\"online\">在线</Option>\r\n              <Option value=\"offline\">离线</Option>\r\n              <Option value=\"warning\">警告</Option>\r\n              <Option value=\"error\">错误</Option>\r\n              <Option value=\"maintenance\">维护中</Option>\r\n            </Select>\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"location\"\r\n            label=\"设备位置\"\r\n            rules={[{ required: true, message: '请输入设备位置' }]}\r\n          >\r\n            <Input placeholder=\"请输入设备位置\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"ipAddress\"\r\n            label=\"IP地址\"\r\n          >\r\n            <Input placeholder=\"请输入IP地址\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"manufacturer\"\r\n            label=\"制造商\"\r\n          >\r\n            <Input placeholder=\"请输入制造商\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"model\"\r\n            label=\"型号\"\r\n          >\r\n            <Input placeholder=\"请输入型号\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"description\"\r\n            label=\"设备描述\"\r\n          >\r\n            <Input.TextArea rows={4} placeholder=\"请输入设备描述\" />\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n\r\n      {/* 设备详情模态框 */}\r\n      <Modal\r\n        title=\"设备详情\"\r\n        open={detailModalVisible}\r\n        onCancel={() => setDetailModalVisible(false)}\r\n        footer={[\r\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\r\n            关闭\r\n          </Button>\r\n        ]}\r\n        width={600}\r\n      >\r\n        {currentDevice && (\r\n          <div>\r\n            <p><strong>设备ID:</strong> {currentDevice.id}</p>\r\n            <p><strong>设备名称:</strong> {currentDevice.name}</p>\r\n            <p><strong>设备类型:</strong> {deviceTypeMap[currentDevice.type] || currentDevice.type}</p>\r\n            <p><strong>状态:</strong> {renderStatusTag(currentDevice.status)}</p>\r\n            <p><strong>位置:</strong> {currentDevice.location}</p>\r\n            <p><strong>IP地址:</strong> {currentDevice.ipAddress}</p>\r\n            <p><strong>最后维护时间:</strong> {currentDevice.lastMaintenance ? new Date(currentDevice.lastMaintenance).toLocaleString() : '无记录'}</p>\r\n            <p><strong>安装日期:</strong> {currentDevice.installationDate ? new Date(currentDevice.installationDate).toLocaleString() : '无记录'}</p>\r\n            <p><strong>制造商:</strong> {currentDevice.manufacturer || '未知'}</p>\r\n            <p><strong>型号:</strong> {currentDevice.model || '未知'}</p>\r\n            <p><strong>描述:</strong> {currentDevice.description || '无'}</p>\r\n          </div>\r\n        )}\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default forwardRef(DeviceManagement); "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACnF,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,OAAO,QAAQ,MAAM;AAC3F,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC;AAAO,CAAC,GAAGR,MAAM;AAEzB,MAAMS,gBAAgB,GAAGA,CAAC;EAAEC;AAAG,CAAC,EAAEC,GAAG,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,IAAI,CAAC,GAAGrB,IAAI,CAACsB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACgC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAMoC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChBY,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;MAE1B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACVF,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;QACnC,MAAMI,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;QAC7ElB,UAAU,CAACmB,YAAY,CAAC;QACxB;MACF;;MAEA;MACA,MAAMG,UAAU,GAAGN,KAAK,CAACO,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;MAC9CT,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEO,UAAU,CAAC;MAErC,MAAME,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMrC,KAAK,CAACsC,GAAG,CAAC,GAAGL,MAAM,cAAc,EAAE;QACxDM,OAAO,EAAE;UACP,eAAe,EAAE,UAAUR,UAAU,EAAE;UACvC,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAIM,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACG,IAAI,CAACC,OAAO,EAAE;QAC1ChC,UAAU,CAAC4B,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;QACpC;QACAd,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAEb,IAAI,CAACc,SAAS,CAACN,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC,CAAC;MAChF,CAAC,MAAM;QAAA,IAAAI,cAAA;QACL,MAAM,IAAIC,KAAK,CAAC,EAAAD,cAAA,GAAAP,QAAQ,CAACG,IAAI,cAAAI,cAAA,uBAAbA,cAAA,CAAe7C,OAAO,KAAI,UAAU,CAAC;MACvD;IACF,CAAC,CAAC,OAAO+C,KAAK,EAAE;MAAA,IAAAC,eAAA;MACdxB,OAAO,CAACuB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;;MAEpC;MACA,IAAI,EAAAC,eAAA,GAAAD,KAAK,CAACT,QAAQ,cAAAU,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAClC,MAAMpB,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;QAC7ElB,UAAU,CAACmB,YAAY,CAAC;QACxB7B,OAAO,CAACkD,OAAO,CAAC,aAAa,CAAC;MAChC,CAAC,MAAM;QACLlD,OAAO,CAAC+C,KAAK,CAAC,YAAY,IAAIA,KAAK,CAAC/C,OAAO,IAAI,MAAM,CAAC,CAAC;MACzD;IACF,CAAC,SAAS;MACRY,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDxB,SAAS,CAAC,MAAM;IACdmC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM4B,eAAe,GAAGA,CAAA,KAAM;IAC5BjC,gBAAgB,CAAC,IAAI,CAAC;IACtBH,IAAI,CAACqC,WAAW,CAAC,CAAC;IAClBtC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuC,gBAAgB,GAAIC,MAAM,IAAK;IACnCpC,gBAAgB,CAACoC,MAAM,CAAC;IACxBvC,IAAI,CAACwC,cAAc,CAAC;MAClBC,IAAI,EAAEF,MAAM,CAACE,IAAI;MACjBC,IAAI,EAAEH,MAAM,CAACG,IAAI;MACjBR,MAAM,EAAEK,MAAM,CAACL,MAAM;MACrBS,QAAQ,EAAEJ,MAAM,CAACI,QAAQ;MACzBC,SAAS,EAAEL,MAAM,CAACK,SAAS;MAC3BC,YAAY,EAAEN,MAAM,CAACM,YAAY;MACjCC,KAAK,EAAEP,MAAM,CAACO,KAAK;MACnBC,WAAW,EAAER,MAAM,CAACQ;IACtB,CAAC,CAAC;IACFhD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMiD,gBAAgB,GAAIT,MAAM,IAAK;IACnChC,gBAAgB,CAACgC,MAAM,CAAC;IACxBlC,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM4C,kBAAkB,GAAG,MAAOC,QAAQ,IAAK;IAC7C,IAAI;MACF,MAAM/B,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMpC,KAAK,CAACiE,MAAM,CAAC,GAAGhC,MAAM,gBAAgB+B,QAAQ,EAAE,CAAC;;MAEvD;MACA,MAAME,cAAc,GAAG1D,OAAO,CAAC2D,MAAM,CAACd,MAAM,IAAIA,MAAM,CAAChD,EAAE,KAAK2D,QAAQ,CAAC;MACvEvD,UAAU,CAACyD,cAAc,CAAC;;MAE1B;MACAxC,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAEb,IAAI,CAACc,SAAS,CAACuB,cAAc,CAAC,CAAC;MAEpEnE,OAAO,CAAC0C,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/C,OAAO,CAAC+C,KAAK,CAAC,UAAU,IAAIA,KAAK,CAAC/C,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMqE,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMvD,IAAI,CAACwD,cAAc,CAAC,CAAC;;MAE1C;MACA,IAAI;QACF,MAAMrC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;QACvE,MAAMmC,UAAU,GAAG;UACjB,GAAGF,MAAM;UACThE,EAAE,EAAEW,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEX;QACrB,CAAC;QAED,IAAIW,aAAa,EAAE;UACjB;UACA,MAAMhB,KAAK,CAACwE,GAAG,CAAC,GAAGvC,MAAM,gBAAgBjB,aAAa,CAACX,EAAE,EAAE,EAAEkE,UAAU,EAAE;YACvEhC,OAAO,EAAE;cACP,eAAe,EAAE,UAAUb,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;cAC1D,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACA,MAAM3B,KAAK,CAACyE,IAAI,CAAC,GAAGxC,MAAM,cAAc,EAAEsC,UAAU,EAAE;YACpDhC,OAAO,EAAE;cACP,eAAe,EAAE,UAAUb,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;cAC1D,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;QACJ;QAEA5B,OAAO,CAAC0C,OAAO,CAACzB,aAAa,GAAG,QAAQ,GAAG,QAAQ,CAAC;QACpDH,eAAe,CAAC,KAAK,CAAC;QACtBC,IAAI,CAACqC,WAAW,CAAC,CAAC;QAClB7B,YAAY,CAAC,CAAC;QACd;MACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;QACdvB,OAAO,CAACuB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MACpC;;MAEA;MACA,MAAMlB,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;MAE7E,IAAIX,aAAa,EAAE;QACjB;QACA,MAAMkD,cAAc,GAAGtC,YAAY,CAAC8C,GAAG,CAACrB,MAAM,IAC5CA,MAAM,CAAChD,EAAE,KAAKW,aAAa,CAACX,EAAE,GAAG;UAAE,GAAGgD,MAAM;UAAE,GAAGgB;QAAO,CAAC,GAAGhB,MAC9D,CAAC;QACD3B,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAEb,IAAI,CAACc,SAAS,CAACuB,cAAc,CAAC,CAAC;QACpEzD,UAAU,CAACyD,cAAc,CAAC;MAC5B,CAAC,MAAM;QACL;QACA,MAAMS,SAAS,GAAG;UAChB,GAAGN,MAAM;UACThE,EAAE,EAAE,OAAOuE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UACvBC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC;QACpC,CAAC;QACDnD,YAAY,CAACoD,IAAI,CAACL,SAAS,CAAC;QAC5BjD,YAAY,CAACgB,OAAO,CAAC,cAAc,EAAEb,IAAI,CAACc,SAAS,CAACf,YAAY,CAAC,CAAC;QAClEnB,UAAU,CAACmB,YAAY,CAAC;MAC1B;MAEA7B,OAAO,CAAC0C,OAAO,CAACzB,aAAa,GAAG,cAAc,GAAG,cAAc,CAAC;MAChEH,eAAe,CAAC,KAAK,CAAC;MACtBC,IAAI,CAACqC,WAAW,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/C,OAAO,CAAC+C,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMmC,eAAe,GAAIjC,MAAM,IAAK;IAClC,MAAMkC,SAAS,GAAG;MAChBC,MAAM,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAK,CAAC;MACtCC,OAAO,EAAE;QAAEF,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAK,CAAC;MACtCpC,OAAO,EAAE;QAAEmC,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAK,CAAC;MACxCvC,KAAK,EAAE;QAAEsC,KAAK,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAK,CAAC;MACnCE,WAAW,EAAE;QAAEH,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM;IAC5C,CAAC;IAED,MAAMG,UAAU,GAAGN,SAAS,CAAClC,MAAM,CAAC,IAAI;MAAEoC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAErC;IAAO,CAAC;IAC1E,oBAAO9C,OAAA,CAACJ,GAAG;MAACsF,KAAK,EAAEI,UAAU,CAACJ,KAAM;MAAAK,QAAA,EAAED,UAAU,CAACH;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG;IACpBC,MAAM,EAAE,KAAK;IACbC,cAAc,EAAE,MAAM;IACtBC,MAAM,EAAE,KAAK;IACbC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE;EACT,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGhD,IAAI,IAAKsC,aAAa,CAACtC,IAAI,CAAC,IAAIA;EAC3C,CAAC,EACD;IACE6C,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEvB;EACV,CAAC,EACD;IACEoB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAM,kBAChBxG,OAAA,CAACN,KAAK;MAAC+G,IAAI,EAAC,OAAO;MAAAlB,QAAA,gBACjBvF,OAAA,CAACX,MAAM;QAACiE,IAAI,EAAC,MAAM;QAACoD,OAAO,EAAEA,CAAA,KAAM9C,gBAAgB,CAAC4C,MAAM,CAAE;QAAAjB,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxE3F,OAAA,CAACX,MAAM;QAACiE,IAAI,EAAC,MAAM;QAACoD,OAAO,EAAEA,CAAA,KAAMxD,gBAAgB,CAACsD,MAAM,CAAE;QAAAjB,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxE3F,OAAA,CAACX,MAAM;QAACiE,IAAI,EAAC,MAAM;QAACqD,MAAM;QAACD,OAAO,EAAEA,CAAA,KAAM7C,kBAAkB,CAAC2C,MAAM,CAACrG,EAAE,CAAE;QAAAoF,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E;EAEX,CAAC,CACF;;EAED;EACAxG,mBAAmB,CAACiB,GAAG,EAAE,OAAO;IAC9BgB;EACF,CAAC,CAAC,CAAC;EAEH,oBACEpB,OAAA;IAAKG,EAAE,EAAEA,EAAG;IAAAoF,QAAA,gBACVvF,OAAA,CAACL,IAAI;MACHwG,KAAK,EAAC,0BAAM;MACZS,KAAK,eAAE5G,OAAA,CAACX,MAAM;QAACiE,IAAI,EAAC,SAAS;QAACoD,OAAO,EAAE1D,eAAgB;QAAAuC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAE;MAAAJ,QAAA,eAEtEvF,OAAA,CAACZ,KAAK;QACJoB,OAAO,EAAEA,OAAQ;QACjBqG,UAAU,EAAEvG,OAAQ;QACpB4F,OAAO,EAAEA,OAAQ;QACjBY,MAAM,EAAC;MAAI;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP3F,OAAA,CAACV,KAAK;MACJ6G,KAAK,EAAErF,aAAa,GAAG,MAAM,GAAG,MAAO;MACvCiG,IAAI,EAAErG,YAAa;MACnBsG,IAAI,EAAE9C,aAAc;MACpB+C,QAAQ,EAAEA,CAAA,KAAMtG,eAAe,CAAC,KAAK,CAAE;MACvCuG,KAAK,EAAE,GAAI;MAAA3B,QAAA,eAEXvF,OAAA,CAACT,IAAI;QACHqB,IAAI,EAAEA,IAAK;QACXuG,MAAM,EAAC,UAAU;QAAA5B,QAAA,gBAEjBvF,OAAA,CAACT,IAAI,CAAC6H,IAAI;UACR/D,IAAI,EAAC,MAAM;UACXgE,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1H,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA0F,QAAA,eAEhDvF,OAAA,CAACR,KAAK;YAACgI,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZ3F,OAAA,CAACT,IAAI,CAAC6H,IAAI;UACR/D,IAAI,EAAC,MAAM;UACXgE,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1H,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA0F,QAAA,eAEhDvF,OAAA,CAACP,MAAM;YAAC+H,WAAW,EAAC,4CAAS;YAAAjC,QAAA,gBAC3BvF,OAAA,CAACC,MAAM;cAACwH,KAAK,EAAC,QAAQ;cAAAlC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnC3F,OAAA,CAACC,MAAM;cAACwH,KAAK,EAAC,gBAAgB;cAAAlC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C3F,OAAA,CAACC,MAAM;cAACwH,KAAK,EAAC,QAAQ;cAAAlC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnC3F,OAAA,CAACC,MAAM;cAACwH,KAAK,EAAC,WAAW;cAAAlC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvC3F,OAAA,CAACC,MAAM;cAACwH,KAAK,EAAC,OAAO;cAAAlC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ3F,OAAA,CAACT,IAAI,CAAC6H,IAAI;UACR/D,IAAI,EAAC,QAAQ;UACbgE,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1H,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA0F,QAAA,eAEhDvF,OAAA,CAACP,MAAM;YAAC+H,WAAW,EAAC,4CAAS;YAAAjC,QAAA,gBAC3BvF,OAAA,CAACC,MAAM;cAACwH,KAAK,EAAC,QAAQ;cAAAlC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC3F,OAAA,CAACC,MAAM;cAACwH,KAAK,EAAC,SAAS;cAAAlC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnC3F,OAAA,CAACC,MAAM;cAACwH,KAAK,EAAC,SAAS;cAAAlC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnC3F,OAAA,CAACC,MAAM;cAACwH,KAAK,EAAC,OAAO;cAAAlC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjC3F,OAAA,CAACC,MAAM;cAACwH,KAAK,EAAC,aAAa;cAAAlC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ3F,OAAA,CAACT,IAAI,CAAC6H,IAAI;UACR/D,IAAI,EAAC,UAAU;UACfgE,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1H,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA0F,QAAA,eAEhDvF,OAAA,CAACR,KAAK;YAACgI,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZ3F,OAAA,CAACT,IAAI,CAAC6H,IAAI;UACR/D,IAAI,EAAC,WAAW;UAChBgE,KAAK,EAAC,gBAAM;UAAA9B,QAAA,eAEZvF,OAAA,CAACR,KAAK;YAACgI,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZ3F,OAAA,CAACT,IAAI,CAAC6H,IAAI;UACR/D,IAAI,EAAC,cAAc;UACnBgE,KAAK,EAAC,oBAAK;UAAA9B,QAAA,eAEXvF,OAAA,CAACR,KAAK;YAACgI,WAAW,EAAC;UAAQ;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEZ3F,OAAA,CAACT,IAAI,CAAC6H,IAAI;UACR/D,IAAI,EAAC,OAAO;UACZgE,KAAK,EAAC,cAAI;UAAA9B,QAAA,eAEVvF,OAAA,CAACR,KAAK;YAACgI,WAAW,EAAC;UAAO;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEZ3F,OAAA,CAACT,IAAI,CAAC6H,IAAI;UACR/D,IAAI,EAAC,aAAa;UAClBgE,KAAK,EAAC,0BAAM;UAAA9B,QAAA,eAEZvF,OAAA,CAACR,KAAK,CAACkI,QAAQ;YAACC,IAAI,EAAE,CAAE;YAACH,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR3F,OAAA,CAACV,KAAK;MACJ6G,KAAK,EAAC,0BAAM;MACZY,IAAI,EAAE/F,kBAAmB;MACzBiG,QAAQ,EAAEA,CAAA,KAAMhG,qBAAqB,CAAC,KAAK,CAAE;MAC7C2G,MAAM,EAAE,cACN5H,OAAA,CAACX,MAAM;QAAaqH,OAAO,EAAEA,CAAA,KAAMzF,qBAAqB,CAAC,KAAK,CAAE;QAAAsE,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFuB,KAAK,EAAE,GAAI;MAAA3B,QAAA,EAEVrE,aAAa,iBACZlB,OAAA;QAAAuF,QAAA,gBACEvF,OAAA;UAAAuF,QAAA,gBAAGvF,OAAA;YAAAuF,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACzE,aAAa,CAACf,EAAE;QAAA;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChD3F,OAAA;UAAAuF,QAAA,gBAAGvF,OAAA;YAAAuF,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACzE,aAAa,CAACmC,IAAI;QAAA;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClD3F,OAAA;UAAAuF,QAAA,gBAAGvF,OAAA;YAAAuF,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACC,aAAa,CAAC1E,aAAa,CAACoC,IAAI,CAAC,IAAIpC,aAAa,CAACoC,IAAI;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvF3F,OAAA;UAAAuF,QAAA,gBAAGvF,OAAA;YAAAuF,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACZ,eAAe,CAAC7D,aAAa,CAAC4B,MAAM,CAAC;QAAA;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnE3F,OAAA;UAAAuF,QAAA,gBAAGvF,OAAA;YAAAuF,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACzE,aAAa,CAACqC,QAAQ;QAAA;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpD3F,OAAA;UAAAuF,QAAA,gBAAGvF,OAAA;YAAAuF,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACzE,aAAa,CAACsC,SAAS;QAAA;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvD3F,OAAA;UAAAuF,QAAA,gBAAGvF,OAAA;YAAAuF,QAAA,EAAQ;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACzE,aAAa,CAAC2G,eAAe,GAAG,IAAInD,IAAI,CAACxD,aAAa,CAAC2G,eAAe,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG,KAAK;QAAA;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClI3F,OAAA;UAAAuF,QAAA,gBAAGvF,OAAA;YAAAuF,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACzE,aAAa,CAAC6G,gBAAgB,GAAG,IAAIrD,IAAI,CAACxD,aAAa,CAAC6G,gBAAgB,CAAC,CAACD,cAAc,CAAC,CAAC,GAAG,KAAK;QAAA;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClI3F,OAAA;UAAAuF,QAAA,gBAAGvF,OAAA;YAAAuF,QAAA,EAAQ;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACzE,aAAa,CAACuC,YAAY,IAAI,IAAI;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjE3F,OAAA;UAAAuF,QAAA,gBAAGvF,OAAA;YAAAuF,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACzE,aAAa,CAACwC,KAAK,IAAI,IAAI;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzD3F,OAAA;UAAAuF,QAAA,gBAAGvF,OAAA;YAAAuF,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACzE,aAAa,CAACyC,WAAW,IAAI,GAAG;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACtF,EAAA,CA9XIH,gBAAgB;EAAA,QAILX,IAAI,CAACsB,OAAO;AAAA;AAAAmH,EAAA,GAJvB9H,gBAAgB;AAgYtB,eAAA+H,GAAA,gBAAe/I,UAAU,CAACgB,gBAAgB,CAAC;AAAC,IAAA8H,EAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAF,EAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}