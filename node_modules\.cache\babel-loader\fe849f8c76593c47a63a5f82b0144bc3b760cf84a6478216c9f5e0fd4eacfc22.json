{"ast": null, "code": "import React from 'react';\nconst FloatButtonGroupContext = /*#__PURE__*/React.createContext(undefined);\nexport const {\n  Provider: FloatButtonGroupProvider\n} = FloatButtonGroupContext;\nexport default FloatButtonGroupContext;", "map": {"version": 3, "names": ["React", "FloatButtonGroupContext", "createContext", "undefined", "Provider", "FloatButtonGroupProvider"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/float-button/context.js"], "sourcesContent": ["import React from 'react';\nconst FloatButtonGroupContext = /*#__PURE__*/React.createContext(undefined);\nexport const {\n  Provider: FloatButtonGroupProvider\n} = FloatButtonGroupContext;\nexport default FloatButtonGroupContext;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,uBAAuB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AAC3E,OAAO,MAAM;EACXC,QAAQ,EAAEC;AACZ,CAAC,GAAGJ,uBAAuB;AAC3B,eAAeA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}