{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BaseBarSeriesModel from './BaseBarSeries.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar PictorialBarSeriesModel = /** @class */function (_super) {\n  __extends(PictorialBarSeriesModel, _super);\n  function PictorialBarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PictorialBarSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    _this.defaultSymbol = 'roundRect';\n    return _this;\n  }\n  PictorialBarSeriesModel.prototype.getInitialData = function (option) {\n    // Disable stack.\n    option.stack = null;\n    return _super.prototype.getInitialData.apply(this, arguments);\n  };\n  PictorialBarSeriesModel.type = 'series.pictorialBar';\n  PictorialBarSeriesModel.dependencies = ['grid'];\n  PictorialBarSeriesModel.defaultOption = inheritDefaultOption(BaseBarSeriesModel.defaultOption, {\n    symbol: 'circle',\n    symbolSize: null,\n    symbolRotate: null,\n    symbolPosition: null,\n    symbolOffset: null,\n    symbolMargin: null,\n    symbolRepeat: false,\n    symbolRepeatDirection: 'end',\n    symbolClip: false,\n    symbolBoundingData: null,\n    symbolPatternSize: 400,\n    barGap: '-100%',\n    // Pictorial bar do not clip by default because in many cases\n    // xAxis and yAxis are not displayed and it's expected not to clip\n    clip: false,\n    // z can be set in data item, which is z2 actually.\n    // Disable progressive\n    progressive: 0,\n    emphasis: {\n      // By default pictorialBar do not hover scale. Hover scale is not suitable\n      // for the case that both has foreground and background.\n      scale: false\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    }\n  });\n  return PictorialBarSeriesModel;\n}(BaseBarSeriesModel);\nexport default PictorialBarSeriesModel;", "map": {"version": 3, "names": ["__extends", "BaseBarSeriesModel", "inheritDefaultOption", "PictorialBarSeriesModel", "_super", "_this", "apply", "arguments", "type", "hasSymbolVisual", "defaultSymbol", "prototype", "getInitialData", "option", "stack", "dependencies", "defaultOption", "symbol", "symbolSize", "symbolRotate", "symbolPosition", "symbolOffset", "symbol<PERSON><PERSON><PERSON>", "symbolRepeat", "symbolRepeatDirection", "symbolClip", "symbolBoundingData", "symbolPatternSize", "barGap", "clip", "progressive", "emphasis", "scale", "select", "itemStyle", "borderColor"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/echarts/lib/chart/bar/PictorialBarSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BaseBarSeriesModel from './BaseBarSeries.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar PictorialBarSeriesModel = /** @class */function (_super) {\n  __extends(PictorialBarSeriesModel, _super);\n  function PictorialBarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PictorialBarSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    _this.defaultSymbol = 'roundRect';\n    return _this;\n  }\n  PictorialBarSeriesModel.prototype.getInitialData = function (option) {\n    // Disable stack.\n    option.stack = null;\n    return _super.prototype.getInitialData.apply(this, arguments);\n  };\n  PictorialBarSeriesModel.type = 'series.pictorialBar';\n  PictorialBarSeriesModel.dependencies = ['grid'];\n  PictorialBarSeriesModel.defaultOption = inheritDefaultOption(BaseBarSeriesModel.defaultOption, {\n    symbol: 'circle',\n    symbolSize: null,\n    symbolRotate: null,\n    symbolPosition: null,\n    symbolOffset: null,\n    symbolMargin: null,\n    symbolRepeat: false,\n    symbolRepeatDirection: 'end',\n    symbolClip: false,\n    symbolBoundingData: null,\n    symbolPatternSize: 400,\n    barGap: '-100%',\n    // Pictorial bar do not clip by default because in many cases\n    // xAxis and yAxis are not displayed and it's expected not to clip\n    clip: false,\n    // z can be set in data item, which is z2 actually.\n    // Disable progressive\n    progressive: 0,\n    emphasis: {\n      // By default pictorialBar do not hover scale. Hover scale is not suitable\n      // for the case that both has foreground and background.\n      scale: false\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    }\n  });\n  return PictorialBarSeriesModel;\n}(BaseBarSeriesModel);\nexport default PictorialBarSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,kBAAkB,MAAM,oBAAoB;AACnD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,IAAIC,uBAAuB,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC3DJ,SAAS,CAACG,uBAAuB,EAAEC,MAAM,CAAC;EAC1C,SAASD,uBAAuBA,CAAA,EAAG;IACjC,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,uBAAuB,CAACK,IAAI;IACzCH,KAAK,CAACI,eAAe,GAAG,IAAI;IAC5BJ,KAAK,CAACK,aAAa,GAAG,WAAW;IACjC,OAAOL,KAAK;EACd;EACAF,uBAAuB,CAACQ,SAAS,CAACC,cAAc,GAAG,UAAUC,MAAM,EAAE;IACnE;IACAA,MAAM,CAACC,KAAK,GAAG,IAAI;IACnB,OAAOV,MAAM,CAACO,SAAS,CAACC,cAAc,CAACN,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAC/D,CAAC;EACDJ,uBAAuB,CAACK,IAAI,GAAG,qBAAqB;EACpDL,uBAAuB,CAACY,YAAY,GAAG,CAAC,MAAM,CAAC;EAC/CZ,uBAAuB,CAACa,aAAa,GAAGd,oBAAoB,CAACD,kBAAkB,CAACe,aAAa,EAAE;IAC7FC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,IAAI;IAChBC,YAAY,EAAE,IAAI;IAClBC,cAAc,EAAE,IAAI;IACpBC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,KAAK;IACnBC,qBAAqB,EAAE,KAAK;IAC5BC,UAAU,EAAE,KAAK;IACjBC,kBAAkB,EAAE,IAAI;IACxBC,iBAAiB,EAAE,GAAG;IACtBC,MAAM,EAAE,OAAO;IACf;IACA;IACAC,IAAI,EAAE,KAAK;IACX;IACA;IACAC,WAAW,EAAE,CAAC;IACdC,QAAQ,EAAE;MACR;MACA;MACAC,KAAK,EAAE;IACT,CAAC;IACDC,MAAM,EAAE;MACNC,SAAS,EAAE;QACTC,WAAW,EAAE;MACf;IACF;EACF,CAAC,CAAC;EACF,OAAOhC,uBAAuB;AAChC,CAAC,CAACF,kBAAkB,CAAC;AACrB,eAAeE,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}