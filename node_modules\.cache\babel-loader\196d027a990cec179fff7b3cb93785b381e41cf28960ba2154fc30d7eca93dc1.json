{"ast": null, "code": "import { responsiveArray } from '../../_util/responsiveObserver';\nexport default function useGutter(gutter, screens) {\n  const results = [undefined, undefined];\n  const normalizedGutter = Array.isArray(gutter) ? gutter : [gutter, undefined];\n  // By default use as `xs`\n  const mergedScreens = screens || {\n    xs: true,\n    sm: true,\n    md: true,\n    lg: true,\n    xl: true,\n    xxl: true\n  };\n  normalizedGutter.forEach((g, index) => {\n    if (typeof g === 'object' && g !== null) {\n      for (let i = 0; i < responsiveArray.length; i++) {\n        const breakpoint = responsiveArray[i];\n        if (mergedScreens[breakpoint] && g[breakpoint] !== undefined) {\n          results[index] = g[breakpoint];\n          break;\n        }\n      }\n    } else {\n      results[index] = g;\n    }\n  });\n  return results;\n}", "map": {"version": 3, "names": ["responsiveArray", "useGutter", "gutter", "screens", "results", "undefined", "normalizedGutter", "Array", "isArray", "mergedScreens", "xs", "sm", "md", "lg", "xl", "xxl", "for<PERSON>ach", "g", "index", "i", "length", "breakpoint"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/grid/hooks/useGutter.js"], "sourcesContent": ["import { responsiveArray } from '../../_util/responsiveObserver';\nexport default function useGutter(gutter, screens) {\n  const results = [undefined, undefined];\n  const normalizedGutter = Array.isArray(gutter) ? gutter : [gutter, undefined];\n  // By default use as `xs`\n  const mergedScreens = screens || {\n    xs: true,\n    sm: true,\n    md: true,\n    lg: true,\n    xl: true,\n    xxl: true\n  };\n  normalizedGutter.forEach((g, index) => {\n    if (typeof g === 'object' && g !== null) {\n      for (let i = 0; i < responsiveArray.length; i++) {\n        const breakpoint = responsiveArray[i];\n        if (mergedScreens[breakpoint] && g[breakpoint] !== undefined) {\n          results[index] = g[breakpoint];\n          break;\n        }\n      }\n    } else {\n      results[index] = g;\n    }\n  });\n  return results;\n}"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAChE,eAAe,SAASC,SAASA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACjD,MAAMC,OAAO,GAAG,CAACC,SAAS,EAAEA,SAAS,CAAC;EACtC,MAAMC,gBAAgB,GAAGC,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,EAAEG,SAAS,CAAC;EAC7E;EACA,MAAMI,aAAa,GAAGN,OAAO,IAAI;IAC/BO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,GAAG,EAAE;EACP,CAAC;EACDT,gBAAgB,CAACU,OAAO,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;IACrC,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,EAAE;MACvC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,eAAe,CAACoB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/C,MAAME,UAAU,GAAGrB,eAAe,CAACmB,CAAC,CAAC;QACrC,IAAIV,aAAa,CAACY,UAAU,CAAC,IAAIJ,CAAC,CAACI,UAAU,CAAC,KAAKhB,SAAS,EAAE;UAC5DD,OAAO,CAACc,KAAK,CAAC,GAAGD,CAAC,CAACI,UAAU,CAAC;UAC9B;QACF;MACF;IACF,CAAC,MAAM;MACLjB,OAAO,CAACc,KAAK,CAAC,GAAGD,CAAC;IACpB;EACF,CAAC,CAAC;EACF,OAAOb,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}