{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { genFocusStyle, resetIcon } from '../../style';\nimport { PresetColors } from '../../theme/interface';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genGroupStyle from './group';\nimport { prepareComponentToken, prepareToken } from './token';\n// ============================== Shared ==============================\nconst genSharedButtonStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    fontWeight,\n    opacityLoading,\n    motionDurationSlow,\n    motionEaseInOut,\n    marginXS,\n    calc\n  } = token;\n  return {\n    [componentCls]: {\n      outline: 'none',\n      position: 'relative',\n      display: 'inline-flex',\n      gap: token.marginXS,\n      alignItems: 'center',\n      justifyContent: 'center',\n      fontWeight,\n      whiteSpace: 'nowrap',\n      textAlign: 'center',\n      backgroundImage: 'none',\n      background: 'transparent',\n      border: `${unit(token.lineWidth)} ${token.lineType} transparent`,\n      cursor: 'pointer',\n      transition: `all ${token.motionDurationMid} ${token.motionEaseInOut}`,\n      userSelect: 'none',\n      touchAction: 'manipulation',\n      color: token.colorText,\n      '&:disabled > *': {\n        pointerEvents: 'none'\n      },\n      // https://github.com/ant-design/ant-design/issues/51380\n      [`${componentCls}-icon > svg`]: resetIcon(),\n      '> a': {\n        color: 'currentColor'\n      },\n      '&:not(:disabled)': genFocusStyle(token),\n      [`&${componentCls}-two-chinese-chars::first-letter`]: {\n        letterSpacing: '0.34em'\n      },\n      [`&${componentCls}-two-chinese-chars > *:not(${iconCls})`]: {\n        marginInlineEnd: '-0.34em',\n        letterSpacing: '0.34em'\n      },\n      [`&${componentCls}-icon-only`]: {\n        paddingInline: 0,\n        // make `btn-icon-only` not too narrow\n        [`&${componentCls}-compact-item`]: {\n          flex: 'none'\n        },\n        [`&${componentCls}-round`]: {\n          width: 'auto'\n        }\n      },\n      // Loading\n      [`&${componentCls}-loading`]: {\n        opacity: opacityLoading,\n        cursor: 'default'\n      },\n      [`${componentCls}-loading-icon`]: {\n        transition: ['width', 'opacity', 'margin'].map(transition => `${transition} ${motionDurationSlow} ${motionEaseInOut}`).join(',')\n      },\n      // iconPosition\n      [`&:not(${componentCls}-icon-end)`]: {\n        [`${componentCls}-loading-icon-motion`]: {\n          '&-appear-start, &-enter-start': {\n            marginInlineEnd: calc(marginXS).mul(-1).equal()\n          },\n          '&-appear-active, &-enter-active': {\n            marginInlineEnd: 0\n          },\n          '&-leave-start': {\n            marginInlineEnd: 0\n          },\n          '&-leave-active': {\n            marginInlineEnd: calc(marginXS).mul(-1).equal()\n          }\n        }\n      },\n      '&-icon-end': {\n        flexDirection: 'row-reverse',\n        [`${componentCls}-loading-icon-motion`]: {\n          '&-appear-start, &-enter-start': {\n            marginInlineStart: calc(marginXS).mul(-1).equal()\n          },\n          '&-appear-active, &-enter-active': {\n            marginInlineStart: 0\n          },\n          '&-leave-start': {\n            marginInlineStart: 0\n          },\n          '&-leave-active': {\n            marginInlineStart: calc(marginXS).mul(-1).equal()\n          }\n        }\n      }\n    }\n  };\n};\nconst genHoverActiveButtonStyle = (btnCls, hoverStyle, activeStyle) => ({\n  [`&:not(:disabled):not(${btnCls}-disabled)`]: {\n    '&:hover': hoverStyle,\n    '&:active': activeStyle\n  }\n});\n// ============================== Shape ===============================\nconst genCircleButtonStyle = token => ({\n  minWidth: token.controlHeight,\n  paddingInlineStart: 0,\n  paddingInlineEnd: 0,\n  borderRadius: '50%'\n});\nconst genRoundButtonStyle = token => ({\n  borderRadius: token.controlHeight,\n  paddingInlineStart: token.calc(token.controlHeight).div(2).equal(),\n  paddingInlineEnd: token.calc(token.controlHeight).div(2).equal()\n});\nconst genDisabledStyle = token => ({\n  cursor: 'not-allowed',\n  borderColor: token.borderColorDisabled,\n  color: token.colorTextDisabled,\n  background: token.colorBgContainerDisabled,\n  boxShadow: 'none'\n});\nconst genGhostButtonStyle = (btnCls, background, textColor, borderColor, textColorDisabled, borderColorDisabled, hoverStyle, activeStyle) => ({\n  [`&${btnCls}-background-ghost`]: Object.assign(Object.assign({\n    color: textColor || undefined,\n    background,\n    borderColor: borderColor || undefined,\n    boxShadow: 'none'\n  }, genHoverActiveButtonStyle(btnCls, Object.assign({\n    background\n  }, hoverStyle), Object.assign({\n    background\n  }, activeStyle))), {\n    '&:disabled': {\n      cursor: 'not-allowed',\n      color: textColorDisabled || undefined,\n      borderColor: borderColorDisabled || undefined\n    }\n  })\n});\nconst genSolidDisabledButtonStyle = token => ({\n  [`&:disabled, &${token.componentCls}-disabled`]: Object.assign({}, genDisabledStyle(token))\n});\nconst genPureDisabledButtonStyle = token => ({\n  [`&:disabled, &${token.componentCls}-disabled`]: {\n    cursor: 'not-allowed',\n    color: token.colorTextDisabled\n  }\n});\n// ============================== Variant =============================\nconst genVariantButtonStyle = (token, hoverStyle, activeStyle, variant) => {\n  const isPureDisabled = variant && ['link', 'text'].includes(variant);\n  const genDisabledButtonStyle = isPureDisabled ? genPureDisabledButtonStyle : genSolidDisabledButtonStyle;\n  return Object.assign(Object.assign({}, genDisabledButtonStyle(token)), genHoverActiveButtonStyle(token.componentCls, hoverStyle, activeStyle));\n};\nconst genSolidButtonStyle = (token, textColor, background, hoverStyle, activeStyle) => ({\n  [`&${token.componentCls}-variant-solid`]: Object.assign({\n    color: textColor,\n    background\n  }, genVariantButtonStyle(token, hoverStyle, activeStyle))\n});\nconst genOutlinedDashedButtonStyle = (token, borderColor, background, hoverStyle, activeStyle) => ({\n  [`&${token.componentCls}-variant-outlined, &${token.componentCls}-variant-dashed`]: Object.assign({\n    borderColor,\n    background\n  }, genVariantButtonStyle(token, hoverStyle, activeStyle))\n});\nconst genDashedButtonStyle = token => ({\n  [`&${token.componentCls}-variant-dashed`]: {\n    borderStyle: 'dashed'\n  }\n});\nconst genFilledButtonStyle = (token, background, hoverStyle, activeStyle) => ({\n  [`&${token.componentCls}-variant-filled`]: Object.assign({\n    boxShadow: 'none',\n    background\n  }, genVariantButtonStyle(token, hoverStyle, activeStyle))\n});\nconst genTextLinkButtonStyle = (token, textColor, variant, hoverStyle, activeStyle) => ({\n  [`&${token.componentCls}-variant-${variant}`]: Object.assign({\n    color: textColor,\n    boxShadow: 'none'\n  }, genVariantButtonStyle(token, hoverStyle, activeStyle, variant))\n});\n// =============================== Color ==============================\nconst genPresetColorStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return PresetColors.reduce((prev, colorKey) => {\n    const darkColor = token[`${colorKey}6`];\n    const lightColor = token[`${colorKey}1`];\n    const hoverColor = token[`${colorKey}5`];\n    const lightHoverColor = token[`${colorKey}2`];\n    const lightBorderColor = token[`${colorKey}3`];\n    const activeColor = token[`${colorKey}7`];\n    return Object.assign(Object.assign({}, prev), {\n      [`&${componentCls}-color-${colorKey}`]: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n        color: darkColor,\n        boxShadow: token[`${colorKey}ShadowColor`]\n      }, genSolidButtonStyle(token, token.colorTextLightSolid, darkColor, {\n        background: hoverColor\n      }, {\n        background: activeColor\n      })), genOutlinedDashedButtonStyle(token, darkColor, token.colorBgContainer, {\n        color: hoverColor,\n        borderColor: hoverColor,\n        background: token.colorBgContainer\n      }, {\n        color: activeColor,\n        borderColor: activeColor,\n        background: token.colorBgContainer\n      })), genDashedButtonStyle(token)), genFilledButtonStyle(token, lightColor, {\n        background: lightHoverColor\n      }, {\n        background: lightBorderColor\n      })), genTextLinkButtonStyle(token, darkColor, 'link', {\n        color: hoverColor\n      }, {\n        color: activeColor\n      })), genTextLinkButtonStyle(token, darkColor, 'text', {\n        color: hoverColor,\n        background: lightColor\n      }, {\n        color: activeColor,\n        background: lightBorderColor\n      }))\n    });\n  }, {});\n};\nconst genDefaultButtonStyle = token => Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n  color: token.defaultColor,\n  boxShadow: token.defaultShadow\n}, genSolidButtonStyle(token, token.solidTextColor, token.colorBgSolid, {\n  color: token.solidTextColor,\n  background: token.colorBgSolidHover\n}, {\n  color: token.solidTextColor,\n  background: token.colorBgSolidActive\n})), genDashedButtonStyle(token)), genFilledButtonStyle(token, token.colorFillTertiary, {\n  background: token.colorFillSecondary\n}, {\n  background: token.colorFill\n})), genGhostButtonStyle(token.componentCls, token.ghostBg, token.defaultGhostColor, token.defaultGhostBorderColor, token.colorTextDisabled, token.colorBorder)), genTextLinkButtonStyle(token, token.textTextColor, 'link', {\n  color: token.colorLinkHover,\n  background: token.linkHoverBg\n}, {\n  color: token.colorLinkActive\n}));\nconst genPrimaryButtonStyle = token => Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n  color: token.colorPrimary,\n  boxShadow: token.primaryShadow\n}, genOutlinedDashedButtonStyle(token, token.colorPrimary, token.colorBgContainer, {\n  color: token.colorPrimaryTextHover,\n  borderColor: token.colorPrimaryHover,\n  background: token.colorBgContainer\n}, {\n  color: token.colorPrimaryTextActive,\n  borderColor: token.colorPrimaryActive,\n  background: token.colorBgContainer\n})), genDashedButtonStyle(token)), genFilledButtonStyle(token, token.colorPrimaryBg, {\n  background: token.colorPrimaryBgHover\n}, {\n  background: token.colorPrimaryBorder\n})), genTextLinkButtonStyle(token, token.colorPrimaryText, 'text', {\n  color: token.colorPrimaryTextHover,\n  background: token.colorPrimaryBg\n}, {\n  color: token.colorPrimaryTextActive,\n  background: token.colorPrimaryBorder\n})), genTextLinkButtonStyle(token, token.colorPrimaryText, 'link', {\n  color: token.colorPrimaryTextHover,\n  background: token.linkHoverBg\n}, {\n  color: token.colorPrimaryTextActive\n})), genGhostButtonStyle(token.componentCls, token.ghostBg, token.colorPrimary, token.colorPrimary, token.colorTextDisabled, token.colorBorder, {\n  color: token.colorPrimaryHover,\n  borderColor: token.colorPrimaryHover\n}, {\n  color: token.colorPrimaryActive,\n  borderColor: token.colorPrimaryActive\n}));\nconst genDangerousStyle = token => Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n  color: token.colorError,\n  boxShadow: token.dangerShadow\n}, genSolidButtonStyle(token, token.dangerColor, token.colorError, {\n  background: token.colorErrorHover\n}, {\n  background: token.colorErrorActive\n})), genOutlinedDashedButtonStyle(token, token.colorError, token.colorBgContainer, {\n  color: token.colorErrorHover,\n  borderColor: token.colorErrorBorderHover\n}, {\n  color: token.colorErrorActive,\n  borderColor: token.colorErrorActive\n})), genDashedButtonStyle(token)), genFilledButtonStyle(token, token.colorErrorBg, {\n  background: token.colorErrorBgFilledHover\n}, {\n  background: token.colorErrorBgActive\n})), genTextLinkButtonStyle(token, token.colorError, 'text', {\n  color: token.colorErrorHover,\n  background: token.colorErrorBg\n}, {\n  color: token.colorErrorHover,\n  background: token.colorErrorBgActive\n})), genTextLinkButtonStyle(token, token.colorError, 'link', {\n  color: token.colorErrorHover\n}, {\n  color: token.colorErrorActive\n})), genGhostButtonStyle(token.componentCls, token.ghostBg, token.colorError, token.colorError, token.colorTextDisabled, token.colorBorder, {\n  color: token.colorErrorHover,\n  borderColor: token.colorErrorHover\n}, {\n  color: token.colorErrorActive,\n  borderColor: token.colorErrorActive\n}));\nconst genLinkStyle = token => Object.assign(Object.assign({}, genTextLinkButtonStyle(token, token.colorLink, 'link', {\n  color: token.colorLinkHover\n}, {\n  color: token.colorLinkActive\n})), genGhostButtonStyle(token.componentCls, token.ghostBg, token.colorInfo, token.colorInfo, token.colorTextDisabled, token.colorBorder, {\n  color: token.colorInfoHover,\n  borderColor: token.colorInfoHover\n}, {\n  color: token.colorInfoActive,\n  borderColor: token.colorInfoActive\n}));\nconst genColorButtonStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return Object.assign({\n    [`${componentCls}-color-default`]: genDefaultButtonStyle(token),\n    [`${componentCls}-color-primary`]: genPrimaryButtonStyle(token),\n    [`${componentCls}-color-dangerous`]: genDangerousStyle(token),\n    [`${componentCls}-color-link`]: genLinkStyle(token)\n  }, genPresetColorStyle(token));\n};\n// =========== Compatible with versions earlier than 5.21.0 ===========\nconst genCompatibleButtonStyle = token => Object.assign(Object.assign(Object.assign(Object.assign({}, genOutlinedDashedButtonStyle(token, token.defaultBorderColor, token.defaultBg, {\n  color: token.defaultHoverColor,\n  borderColor: token.defaultHoverBorderColor,\n  background: token.defaultHoverBg\n}, {\n  color: token.defaultActiveColor,\n  borderColor: token.defaultActiveBorderColor,\n  background: token.defaultActiveBg\n})), genTextLinkButtonStyle(token, token.textTextColor, 'text', {\n  color: token.textTextHoverColor,\n  background: token.textHoverBg\n}, {\n  color: token.textTextActiveColor,\n  background: token.colorBgTextActive\n})), genSolidButtonStyle(token, token.primaryColor, token.colorPrimary, {\n  background: token.colorPrimaryHover,\n  color: token.primaryColor\n}, {\n  background: token.colorPrimaryActive,\n  color: token.primaryColor\n})), genTextLinkButtonStyle(token, token.colorLink, 'link', {\n  color: token.colorLinkHover,\n  background: token.linkHoverBg\n}, {\n  color: token.colorLinkActive\n}));\n// =============================== Size ===============================\nconst genButtonStyle = function (token) {\n  let prefixCls = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  const {\n    componentCls,\n    controlHeight,\n    fontSize,\n    borderRadius,\n    buttonPaddingHorizontal,\n    iconCls,\n    buttonPaddingVertical,\n    buttonIconOnlyFontSize\n  } = token;\n  return [{\n    [prefixCls]: {\n      fontSize,\n      height: controlHeight,\n      padding: `${unit(buttonPaddingVertical)} ${unit(buttonPaddingHorizontal)}`,\n      borderRadius,\n      [`&${componentCls}-icon-only`]: {\n        width: controlHeight,\n        [iconCls]: {\n          fontSize: buttonIconOnlyFontSize\n        }\n      }\n    }\n  },\n  // Shape - patch prefixCls again to override solid border radius style\n  {\n    [`${componentCls}${componentCls}-circle${prefixCls}`]: genCircleButtonStyle(token)\n  }, {\n    [`${componentCls}${componentCls}-round${prefixCls}`]: genRoundButtonStyle(token)\n  }];\n};\nconst genSizeBaseButtonStyle = token => {\n  const baseToken = mergeToken(token, {\n    fontSize: token.contentFontSize\n  });\n  return genButtonStyle(baseToken, token.componentCls);\n};\nconst genSizeSmallButtonStyle = token => {\n  const smallToken = mergeToken(token, {\n    controlHeight: token.controlHeightSM,\n    fontSize: token.contentFontSizeSM,\n    padding: token.paddingXS,\n    buttonPaddingHorizontal: token.paddingInlineSM,\n    buttonPaddingVertical: 0,\n    borderRadius: token.borderRadiusSM,\n    buttonIconOnlyFontSize: token.onlyIconSizeSM\n  });\n  return genButtonStyle(smallToken, `${token.componentCls}-sm`);\n};\nconst genSizeLargeButtonStyle = token => {\n  const largeToken = mergeToken(token, {\n    controlHeight: token.controlHeightLG,\n    fontSize: token.contentFontSizeLG,\n    buttonPaddingHorizontal: token.paddingInlineLG,\n    buttonPaddingVertical: 0,\n    borderRadius: token.borderRadiusLG,\n    buttonIconOnlyFontSize: token.onlyIconSizeLG\n  });\n  return genButtonStyle(largeToken, `${token.componentCls}-lg`);\n};\nconst genBlockButtonStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      [`&${componentCls}-block`]: {\n        width: '100%'\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks('Button', token => {\n  const buttonToken = prepareToken(token);\n  return [\n  // Shared\n  genSharedButtonStyle(buttonToken),\n  // Size\n  genSizeBaseButtonStyle(buttonToken), genSizeSmallButtonStyle(buttonToken), genSizeLargeButtonStyle(buttonToken),\n  // Block\n  genBlockButtonStyle(buttonToken),\n  // Color\n  genColorButtonStyle(buttonToken),\n  // https://github.com/ant-design/ant-design/issues/50969\n  genCompatibleButtonStyle(buttonToken),\n  // Button Group\n  genGroupStyle(buttonToken)];\n}, prepareComponentToken, {\n  unitless: {\n    fontWeight: true,\n    contentLineHeight: true,\n    contentLineHeightSM: true,\n    contentLineHeightLG: true\n  }\n});", "map": {"version": 3, "names": ["unit", "genFocusStyle", "resetIcon", "PresetColors", "genStyleHooks", "mergeToken", "genGroupStyle", "prepareComponentToken", "prepareToken", "genSharedButtonStyle", "token", "componentCls", "iconCls", "fontWeight", "opacityLoading", "motionDurationSlow", "motionEaseInOut", "marginXS", "calc", "outline", "position", "display", "gap", "alignItems", "justifyContent", "whiteSpace", "textAlign", "backgroundImage", "background", "border", "lineWidth", "lineType", "cursor", "transition", "motionDurationMid", "userSelect", "touchAction", "color", "colorText", "pointerEvents", "letterSpacing", "marginInlineEnd", "paddingInline", "flex", "width", "opacity", "map", "join", "mul", "equal", "flexDirection", "marginInlineStart", "genHoverActiveButtonStyle", "btnCls", "hoverStyle", "activeStyle", "genCircleButtonStyle", "min<PERSON><PERSON><PERSON>", "controlHeight", "paddingInlineStart", "paddingInlineEnd", "borderRadius", "genRoundButtonStyle", "div", "genDisabledStyle", "borderColor", "borderColorDisabled", "colorTextDisabled", "colorBgContainerDisabled", "boxShadow", "genGhostButtonStyle", "textColor", "textColorDisabled", "Object", "assign", "undefined", "genSolidDisabledButtonStyle", "genPureDisabledButtonStyle", "genVariantButtonStyle", "variant", "isPureDisabled", "includes", "genDisabledButtonStyle", "genSolidButtonStyle", "genOutlinedDashedButtonStyle", "genDashedButtonStyle", "borderStyle", "genFilledButtonStyle", "genTextLinkButtonStyle", "genPresetColorStyle", "reduce", "prev", "colorKey", "darkColor", "lightColor", "hoverColor", "lightHoverColor", "lightBorderColor", "activeColor", "colorTextLightSolid", "colorBgContainer", "genDefaultButtonStyle", "defaultColor", "defaultShadow", "solidTextColor", "colorBgSolid", "colorBgSolidHover", "colorBgSolidActive", "colorFillTertiary", "colorFillSecondary", "colorFill", "ghostBg", "defaultGhostColor", "defaultGhostBorderColor", "colorBorder", "textTextColor", "colorLinkHover", "linkHoverBg", "colorLinkActive", "genPrimaryButtonStyle", "colorPrimary", "primaryShadow", "colorPrimaryTextHover", "colorPrimaryHover", "colorPrimaryTextActive", "colorPrimaryActive", "colorPrimaryBg", "colorPrimaryBgHover", "colorPrimaryBorder", "colorPrimaryText", "genDangerousStyle", "colorError", "dangerShadow", "dangerColor", "colorErrorHover", "colorErrorActive", "colorErrorBorderHover", "colorErrorBg", "colorErrorBgFilledHover", "colorErrorBgActive", "genLinkStyle", "colorLink", "colorInfo", "colorInfoHover", "colorInfoActive", "genColorButtonStyle", "genCompatibleButtonStyle", "defaultBorderColor", "defaultBg", "defaultHoverColor", "defaultHoverBorderColor", "defaultHoverBg", "defaultActiveColor", "defaultActiveBorderColor", "defaultActiveBg", "textTextHoverColor", "textHoverBg", "textTextActiveColor", "colorBgTextActive", "primaryColor", "genButtonStyle", "prefixCls", "arguments", "length", "fontSize", "buttonPaddingHorizontal", "buttonPaddingVertical", "buttonIconOnlyFontSize", "height", "padding", "genSizeBaseButtonStyle", "baseToken", "contentFontSize", "genSizeSmallButtonStyle", "smallToken", "controlHeightSM", "contentFontSizeSM", "paddingXS", "paddingInlineSM", "borderRadiusSM", "onlyIconSizeSM", "genSizeLargeButtonStyle", "largeToken", "controlHeightLG", "contentFontSizeLG", "paddingInlineLG", "borderRadiusLG", "onlyIconSizeLG", "genBlockButtonStyle", "buttonToken", "unitless", "contentLineHeight", "contentLineHeightSM", "contentLineHeightLG"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/button/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { genFocusStyle, resetIcon } from '../../style';\nimport { PresetColors } from '../../theme/interface';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genGroupStyle from './group';\nimport { prepareComponentToken, prepareToken } from './token';\n// ============================== Shared ==============================\nconst genSharedButtonStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    fontWeight,\n    opacityLoading,\n    motionDurationSlow,\n    motionEaseInOut,\n    marginXS,\n    calc\n  } = token;\n  return {\n    [componentCls]: {\n      outline: 'none',\n      position: 'relative',\n      display: 'inline-flex',\n      gap: token.marginXS,\n      alignItems: 'center',\n      justifyContent: 'center',\n      fontWeight,\n      whiteSpace: 'nowrap',\n      textAlign: 'center',\n      backgroundImage: 'none',\n      background: 'transparent',\n      border: `${unit(token.lineWidth)} ${token.lineType} transparent`,\n      cursor: 'pointer',\n      transition: `all ${token.motionDurationMid} ${token.motionEaseInOut}`,\n      userSelect: 'none',\n      touchAction: 'manipulation',\n      color: token.colorText,\n      '&:disabled > *': {\n        pointerEvents: 'none'\n      },\n      // https://github.com/ant-design/ant-design/issues/51380\n      [`${componentCls}-icon > svg`]: resetIcon(),\n      '> a': {\n        color: 'currentColor'\n      },\n      '&:not(:disabled)': genFocusStyle(token),\n      [`&${componentCls}-two-chinese-chars::first-letter`]: {\n        letterSpacing: '0.34em'\n      },\n      [`&${componentCls}-two-chinese-chars > *:not(${iconCls})`]: {\n        marginInlineEnd: '-0.34em',\n        letterSpacing: '0.34em'\n      },\n      [`&${componentCls}-icon-only`]: {\n        paddingInline: 0,\n        // make `btn-icon-only` not too narrow\n        [`&${componentCls}-compact-item`]: {\n          flex: 'none'\n        },\n        [`&${componentCls}-round`]: {\n          width: 'auto'\n        }\n      },\n      // Loading\n      [`&${componentCls}-loading`]: {\n        opacity: opacityLoading,\n        cursor: 'default'\n      },\n      [`${componentCls}-loading-icon`]: {\n        transition: ['width', 'opacity', 'margin'].map(transition => `${transition} ${motionDurationSlow} ${motionEaseInOut}`).join(',')\n      },\n      // iconPosition\n      [`&:not(${componentCls}-icon-end)`]: {\n        [`${componentCls}-loading-icon-motion`]: {\n          '&-appear-start, &-enter-start': {\n            marginInlineEnd: calc(marginXS).mul(-1).equal()\n          },\n          '&-appear-active, &-enter-active': {\n            marginInlineEnd: 0\n          },\n          '&-leave-start': {\n            marginInlineEnd: 0\n          },\n          '&-leave-active': {\n            marginInlineEnd: calc(marginXS).mul(-1).equal()\n          }\n        }\n      },\n      '&-icon-end': {\n        flexDirection: 'row-reverse',\n        [`${componentCls}-loading-icon-motion`]: {\n          '&-appear-start, &-enter-start': {\n            marginInlineStart: calc(marginXS).mul(-1).equal()\n          },\n          '&-appear-active, &-enter-active': {\n            marginInlineStart: 0\n          },\n          '&-leave-start': {\n            marginInlineStart: 0\n          },\n          '&-leave-active': {\n            marginInlineStart: calc(marginXS).mul(-1).equal()\n          }\n        }\n      }\n    }\n  };\n};\nconst genHoverActiveButtonStyle = (btnCls, hoverStyle, activeStyle) => ({\n  [`&:not(:disabled):not(${btnCls}-disabled)`]: {\n    '&:hover': hoverStyle,\n    '&:active': activeStyle\n  }\n});\n// ============================== Shape ===============================\nconst genCircleButtonStyle = token => ({\n  minWidth: token.controlHeight,\n  paddingInlineStart: 0,\n  paddingInlineEnd: 0,\n  borderRadius: '50%'\n});\nconst genRoundButtonStyle = token => ({\n  borderRadius: token.controlHeight,\n  paddingInlineStart: token.calc(token.controlHeight).div(2).equal(),\n  paddingInlineEnd: token.calc(token.controlHeight).div(2).equal()\n});\nconst genDisabledStyle = token => ({\n  cursor: 'not-allowed',\n  borderColor: token.borderColorDisabled,\n  color: token.colorTextDisabled,\n  background: token.colorBgContainerDisabled,\n  boxShadow: 'none'\n});\nconst genGhostButtonStyle = (btnCls, background, textColor, borderColor, textColorDisabled, borderColorDisabled, hoverStyle, activeStyle) => ({\n  [`&${btnCls}-background-ghost`]: Object.assign(Object.assign({\n    color: textColor || undefined,\n    background,\n    borderColor: borderColor || undefined,\n    boxShadow: 'none'\n  }, genHoverActiveButtonStyle(btnCls, Object.assign({\n    background\n  }, hoverStyle), Object.assign({\n    background\n  }, activeStyle))), {\n    '&:disabled': {\n      cursor: 'not-allowed',\n      color: textColorDisabled || undefined,\n      borderColor: borderColorDisabled || undefined\n    }\n  })\n});\nconst genSolidDisabledButtonStyle = token => ({\n  [`&:disabled, &${token.componentCls}-disabled`]: Object.assign({}, genDisabledStyle(token))\n});\nconst genPureDisabledButtonStyle = token => ({\n  [`&:disabled, &${token.componentCls}-disabled`]: {\n    cursor: 'not-allowed',\n    color: token.colorTextDisabled\n  }\n});\n// ============================== Variant =============================\nconst genVariantButtonStyle = (token, hoverStyle, activeStyle, variant) => {\n  const isPureDisabled = variant && ['link', 'text'].includes(variant);\n  const genDisabledButtonStyle = isPureDisabled ? genPureDisabledButtonStyle : genSolidDisabledButtonStyle;\n  return Object.assign(Object.assign({}, genDisabledButtonStyle(token)), genHoverActiveButtonStyle(token.componentCls, hoverStyle, activeStyle));\n};\nconst genSolidButtonStyle = (token, textColor, background, hoverStyle, activeStyle) => ({\n  [`&${token.componentCls}-variant-solid`]: Object.assign({\n    color: textColor,\n    background\n  }, genVariantButtonStyle(token, hoverStyle, activeStyle))\n});\nconst genOutlinedDashedButtonStyle = (token, borderColor, background, hoverStyle, activeStyle) => ({\n  [`&${token.componentCls}-variant-outlined, &${token.componentCls}-variant-dashed`]: Object.assign({\n    borderColor,\n    background\n  }, genVariantButtonStyle(token, hoverStyle, activeStyle))\n});\nconst genDashedButtonStyle = token => ({\n  [`&${token.componentCls}-variant-dashed`]: {\n    borderStyle: 'dashed'\n  }\n});\nconst genFilledButtonStyle = (token, background, hoverStyle, activeStyle) => ({\n  [`&${token.componentCls}-variant-filled`]: Object.assign({\n    boxShadow: 'none',\n    background\n  }, genVariantButtonStyle(token, hoverStyle, activeStyle))\n});\nconst genTextLinkButtonStyle = (token, textColor, variant, hoverStyle, activeStyle) => ({\n  [`&${token.componentCls}-variant-${variant}`]: Object.assign({\n    color: textColor,\n    boxShadow: 'none'\n  }, genVariantButtonStyle(token, hoverStyle, activeStyle, variant))\n});\n// =============================== Color ==============================\nconst genPresetColorStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return PresetColors.reduce((prev, colorKey) => {\n    const darkColor = token[`${colorKey}6`];\n    const lightColor = token[`${colorKey}1`];\n    const hoverColor = token[`${colorKey}5`];\n    const lightHoverColor = token[`${colorKey}2`];\n    const lightBorderColor = token[`${colorKey}3`];\n    const activeColor = token[`${colorKey}7`];\n    return Object.assign(Object.assign({}, prev), {\n      [`&${componentCls}-color-${colorKey}`]: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n        color: darkColor,\n        boxShadow: token[`${colorKey}ShadowColor`]\n      }, genSolidButtonStyle(token, token.colorTextLightSolid, darkColor, {\n        background: hoverColor\n      }, {\n        background: activeColor\n      })), genOutlinedDashedButtonStyle(token, darkColor, token.colorBgContainer, {\n        color: hoverColor,\n        borderColor: hoverColor,\n        background: token.colorBgContainer\n      }, {\n        color: activeColor,\n        borderColor: activeColor,\n        background: token.colorBgContainer\n      })), genDashedButtonStyle(token)), genFilledButtonStyle(token, lightColor, {\n        background: lightHoverColor\n      }, {\n        background: lightBorderColor\n      })), genTextLinkButtonStyle(token, darkColor, 'link', {\n        color: hoverColor\n      }, {\n        color: activeColor\n      })), genTextLinkButtonStyle(token, darkColor, 'text', {\n        color: hoverColor,\n        background: lightColor\n      }, {\n        color: activeColor,\n        background: lightBorderColor\n      }))\n    });\n  }, {});\n};\nconst genDefaultButtonStyle = token => Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n  color: token.defaultColor,\n  boxShadow: token.defaultShadow\n}, genSolidButtonStyle(token, token.solidTextColor, token.colorBgSolid, {\n  color: token.solidTextColor,\n  background: token.colorBgSolidHover\n}, {\n  color: token.solidTextColor,\n  background: token.colorBgSolidActive\n})), genDashedButtonStyle(token)), genFilledButtonStyle(token, token.colorFillTertiary, {\n  background: token.colorFillSecondary\n}, {\n  background: token.colorFill\n})), genGhostButtonStyle(token.componentCls, token.ghostBg, token.defaultGhostColor, token.defaultGhostBorderColor, token.colorTextDisabled, token.colorBorder)), genTextLinkButtonStyle(token, token.textTextColor, 'link', {\n  color: token.colorLinkHover,\n  background: token.linkHoverBg\n}, {\n  color: token.colorLinkActive\n}));\nconst genPrimaryButtonStyle = token => Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n  color: token.colorPrimary,\n  boxShadow: token.primaryShadow\n}, genOutlinedDashedButtonStyle(token, token.colorPrimary, token.colorBgContainer, {\n  color: token.colorPrimaryTextHover,\n  borderColor: token.colorPrimaryHover,\n  background: token.colorBgContainer\n}, {\n  color: token.colorPrimaryTextActive,\n  borderColor: token.colorPrimaryActive,\n  background: token.colorBgContainer\n})), genDashedButtonStyle(token)), genFilledButtonStyle(token, token.colorPrimaryBg, {\n  background: token.colorPrimaryBgHover\n}, {\n  background: token.colorPrimaryBorder\n})), genTextLinkButtonStyle(token, token.colorPrimaryText, 'text', {\n  color: token.colorPrimaryTextHover,\n  background: token.colorPrimaryBg\n}, {\n  color: token.colorPrimaryTextActive,\n  background: token.colorPrimaryBorder\n})), genTextLinkButtonStyle(token, token.colorPrimaryText, 'link', {\n  color: token.colorPrimaryTextHover,\n  background: token.linkHoverBg\n}, {\n  color: token.colorPrimaryTextActive\n})), genGhostButtonStyle(token.componentCls, token.ghostBg, token.colorPrimary, token.colorPrimary, token.colorTextDisabled, token.colorBorder, {\n  color: token.colorPrimaryHover,\n  borderColor: token.colorPrimaryHover\n}, {\n  color: token.colorPrimaryActive,\n  borderColor: token.colorPrimaryActive\n}));\nconst genDangerousStyle = token => Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n  color: token.colorError,\n  boxShadow: token.dangerShadow\n}, genSolidButtonStyle(token, token.dangerColor, token.colorError, {\n  background: token.colorErrorHover\n}, {\n  background: token.colorErrorActive\n})), genOutlinedDashedButtonStyle(token, token.colorError, token.colorBgContainer, {\n  color: token.colorErrorHover,\n  borderColor: token.colorErrorBorderHover\n}, {\n  color: token.colorErrorActive,\n  borderColor: token.colorErrorActive\n})), genDashedButtonStyle(token)), genFilledButtonStyle(token, token.colorErrorBg, {\n  background: token.colorErrorBgFilledHover\n}, {\n  background: token.colorErrorBgActive\n})), genTextLinkButtonStyle(token, token.colorError, 'text', {\n  color: token.colorErrorHover,\n  background: token.colorErrorBg\n}, {\n  color: token.colorErrorHover,\n  background: token.colorErrorBgActive\n})), genTextLinkButtonStyle(token, token.colorError, 'link', {\n  color: token.colorErrorHover\n}, {\n  color: token.colorErrorActive\n})), genGhostButtonStyle(token.componentCls, token.ghostBg, token.colorError, token.colorError, token.colorTextDisabled, token.colorBorder, {\n  color: token.colorErrorHover,\n  borderColor: token.colorErrorHover\n}, {\n  color: token.colorErrorActive,\n  borderColor: token.colorErrorActive\n}));\nconst genLinkStyle = token => Object.assign(Object.assign({}, genTextLinkButtonStyle(token, token.colorLink, 'link', {\n  color: token.colorLinkHover\n}, {\n  color: token.colorLinkActive\n})), genGhostButtonStyle(token.componentCls, token.ghostBg, token.colorInfo, token.colorInfo, token.colorTextDisabled, token.colorBorder, {\n  color: token.colorInfoHover,\n  borderColor: token.colorInfoHover\n}, {\n  color: token.colorInfoActive,\n  borderColor: token.colorInfoActive\n}));\nconst genColorButtonStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return Object.assign({\n    [`${componentCls}-color-default`]: genDefaultButtonStyle(token),\n    [`${componentCls}-color-primary`]: genPrimaryButtonStyle(token),\n    [`${componentCls}-color-dangerous`]: genDangerousStyle(token),\n    [`${componentCls}-color-link`]: genLinkStyle(token)\n  }, genPresetColorStyle(token));\n};\n// =========== Compatible with versions earlier than 5.21.0 ===========\nconst genCompatibleButtonStyle = token => Object.assign(Object.assign(Object.assign(Object.assign({}, genOutlinedDashedButtonStyle(token, token.defaultBorderColor, token.defaultBg, {\n  color: token.defaultHoverColor,\n  borderColor: token.defaultHoverBorderColor,\n  background: token.defaultHoverBg\n}, {\n  color: token.defaultActiveColor,\n  borderColor: token.defaultActiveBorderColor,\n  background: token.defaultActiveBg\n})), genTextLinkButtonStyle(token, token.textTextColor, 'text', {\n  color: token.textTextHoverColor,\n  background: token.textHoverBg\n}, {\n  color: token.textTextActiveColor,\n  background: token.colorBgTextActive\n})), genSolidButtonStyle(token, token.primaryColor, token.colorPrimary, {\n  background: token.colorPrimaryHover,\n  color: token.primaryColor\n}, {\n  background: token.colorPrimaryActive,\n  color: token.primaryColor\n})), genTextLinkButtonStyle(token, token.colorLink, 'link', {\n  color: token.colorLinkHover,\n  background: token.linkHoverBg\n}, {\n  color: token.colorLinkActive\n}));\n// =============================== Size ===============================\nconst genButtonStyle = function (token) {\n  let prefixCls = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  const {\n    componentCls,\n    controlHeight,\n    fontSize,\n    borderRadius,\n    buttonPaddingHorizontal,\n    iconCls,\n    buttonPaddingVertical,\n    buttonIconOnlyFontSize\n  } = token;\n  return [{\n    [prefixCls]: {\n      fontSize,\n      height: controlHeight,\n      padding: `${unit(buttonPaddingVertical)} ${unit(buttonPaddingHorizontal)}`,\n      borderRadius,\n      [`&${componentCls}-icon-only`]: {\n        width: controlHeight,\n        [iconCls]: {\n          fontSize: buttonIconOnlyFontSize\n        }\n      }\n    }\n  },\n  // Shape - patch prefixCls again to override solid border radius style\n  {\n    [`${componentCls}${componentCls}-circle${prefixCls}`]: genCircleButtonStyle(token)\n  }, {\n    [`${componentCls}${componentCls}-round${prefixCls}`]: genRoundButtonStyle(token)\n  }];\n};\nconst genSizeBaseButtonStyle = token => {\n  const baseToken = mergeToken(token, {\n    fontSize: token.contentFontSize\n  });\n  return genButtonStyle(baseToken, token.componentCls);\n};\nconst genSizeSmallButtonStyle = token => {\n  const smallToken = mergeToken(token, {\n    controlHeight: token.controlHeightSM,\n    fontSize: token.contentFontSizeSM,\n    padding: token.paddingXS,\n    buttonPaddingHorizontal: token.paddingInlineSM,\n    buttonPaddingVertical: 0,\n    borderRadius: token.borderRadiusSM,\n    buttonIconOnlyFontSize: token.onlyIconSizeSM\n  });\n  return genButtonStyle(smallToken, `${token.componentCls}-sm`);\n};\nconst genSizeLargeButtonStyle = token => {\n  const largeToken = mergeToken(token, {\n    controlHeight: token.controlHeightLG,\n    fontSize: token.contentFontSizeLG,\n    buttonPaddingHorizontal: token.paddingInlineLG,\n    buttonPaddingVertical: 0,\n    borderRadius: token.borderRadiusLG,\n    buttonIconOnlyFontSize: token.onlyIconSizeLG\n  });\n  return genButtonStyle(largeToken, `${token.componentCls}-lg`);\n};\nconst genBlockButtonStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: {\n      [`&${componentCls}-block`]: {\n        width: '100%'\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genStyleHooks('Button', token => {\n  const buttonToken = prepareToken(token);\n  return [\n  // Shared\n  genSharedButtonStyle(buttonToken),\n  // Size\n  genSizeBaseButtonStyle(buttonToken), genSizeSmallButtonStyle(buttonToken), genSizeLargeButtonStyle(buttonToken),\n  // Block\n  genBlockButtonStyle(buttonToken),\n  // Color\n  genColorButtonStyle(buttonToken),\n  // https://github.com/ant-design/ant-design/issues/50969\n  genCompatibleButtonStyle(buttonToken),\n  // Button Group\n  genGroupStyle(buttonToken)];\n}, prepareComponentToken, {\n  unitless: {\n    fontWeight: true,\n    contentLineHeight: true,\n    contentLineHeightSM: true,\n    contentLineHeightLG: true\n  }\n});"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,aAAa,EAAEC,SAAS,QAAQ,aAAa;AACtD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,aAAa,EAAEC,UAAU,QAAQ,sBAAsB;AAChE,OAAOC,aAAa,MAAM,SAAS;AACnC,SAASC,qBAAqB,EAAEC,YAAY,QAAQ,SAAS;AAC7D;AACA,MAAMC,oBAAoB,GAAGC,KAAK,IAAI;EACpC,MAAM;IACJC,YAAY;IACZC,OAAO;IACPC,UAAU;IACVC,cAAc;IACdC,kBAAkB;IAClBC,eAAe;IACfC,QAAQ;IACRC;EACF,CAAC,GAAGR,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACdQ,OAAO,EAAE,MAAM;MACfC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,aAAa;MACtBC,GAAG,EAAEZ,KAAK,CAACO,QAAQ;MACnBM,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBX,UAAU;MACVY,UAAU,EAAE,QAAQ;MACpBC,SAAS,EAAE,QAAQ;MACnBC,eAAe,EAAE,MAAM;MACvBC,UAAU,EAAE,aAAa;MACzBC,MAAM,EAAE,GAAG7B,IAAI,CAACU,KAAK,CAACoB,SAAS,CAAC,IAAIpB,KAAK,CAACqB,QAAQ,cAAc;MAChEC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,OAAOvB,KAAK,CAACwB,iBAAiB,IAAIxB,KAAK,CAACM,eAAe,EAAE;MACrEmB,UAAU,EAAE,MAAM;MAClBC,WAAW,EAAE,cAAc;MAC3BC,KAAK,EAAE3B,KAAK,CAAC4B,SAAS;MACtB,gBAAgB,EAAE;QAChBC,aAAa,EAAE;MACjB,CAAC;MACD;MACA,CAAC,GAAG5B,YAAY,aAAa,GAAGT,SAAS,CAAC,CAAC;MAC3C,KAAK,EAAE;QACLmC,KAAK,EAAE;MACT,CAAC;MACD,kBAAkB,EAAEpC,aAAa,CAACS,KAAK,CAAC;MACxC,CAAC,IAAIC,YAAY,kCAAkC,GAAG;QACpD6B,aAAa,EAAE;MACjB,CAAC;MACD,CAAC,IAAI7B,YAAY,8BAA8BC,OAAO,GAAG,GAAG;QAC1D6B,eAAe,EAAE,SAAS;QAC1BD,aAAa,EAAE;MACjB,CAAC;MACD,CAAC,IAAI7B,YAAY,YAAY,GAAG;QAC9B+B,aAAa,EAAE,CAAC;QAChB;QACA,CAAC,IAAI/B,YAAY,eAAe,GAAG;UACjCgC,IAAI,EAAE;QACR,CAAC;QACD,CAAC,IAAIhC,YAAY,QAAQ,GAAG;UAC1BiC,KAAK,EAAE;QACT;MACF,CAAC;MACD;MACA,CAAC,IAAIjC,YAAY,UAAU,GAAG;QAC5BkC,OAAO,EAAE/B,cAAc;QACvBkB,MAAM,EAAE;MACV,CAAC;MACD,CAAC,GAAGrB,YAAY,eAAe,GAAG;QAChCsB,UAAU,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,CAACa,GAAG,CAACb,UAAU,IAAI,GAAGA,UAAU,IAAIlB,kBAAkB,IAAIC,eAAe,EAAE,CAAC,CAAC+B,IAAI,CAAC,GAAG;MACjI,CAAC;MACD;MACA,CAAC,SAASpC,YAAY,YAAY,GAAG;QACnC,CAAC,GAAGA,YAAY,sBAAsB,GAAG;UACvC,+BAA+B,EAAE;YAC/B8B,eAAe,EAAEvB,IAAI,CAACD,QAAQ,CAAC,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;UAChD,CAAC;UACD,iCAAiC,EAAE;YACjCR,eAAe,EAAE;UACnB,CAAC;UACD,eAAe,EAAE;YACfA,eAAe,EAAE;UACnB,CAAC;UACD,gBAAgB,EAAE;YAChBA,eAAe,EAAEvB,IAAI,CAACD,QAAQ,CAAC,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;UAChD;QACF;MACF,CAAC;MACD,YAAY,EAAE;QACZC,aAAa,EAAE,aAAa;QAC5B,CAAC,GAAGvC,YAAY,sBAAsB,GAAG;UACvC,+BAA+B,EAAE;YAC/BwC,iBAAiB,EAAEjC,IAAI,CAACD,QAAQ,CAAC,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;UAClD,CAAC;UACD,iCAAiC,EAAE;YACjCE,iBAAiB,EAAE;UACrB,CAAC;UACD,eAAe,EAAE;YACfA,iBAAiB,EAAE;UACrB,CAAC;UACD,gBAAgB,EAAE;YAChBA,iBAAiB,EAAEjC,IAAI,CAACD,QAAQ,CAAC,CAAC+B,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;UAClD;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,MAAMG,yBAAyB,GAAGA,CAACC,MAAM,EAAEC,UAAU,EAAEC,WAAW,MAAM;EACtE,CAAC,wBAAwBF,MAAM,YAAY,GAAG;IAC5C,SAAS,EAAEC,UAAU;IACrB,UAAU,EAAEC;EACd;AACF,CAAC,CAAC;AACF;AACA,MAAMC,oBAAoB,GAAG9C,KAAK,KAAK;EACrC+C,QAAQ,EAAE/C,KAAK,CAACgD,aAAa;EAC7BC,kBAAkB,EAAE,CAAC;EACrBC,gBAAgB,EAAE,CAAC;EACnBC,YAAY,EAAE;AAChB,CAAC,CAAC;AACF,MAAMC,mBAAmB,GAAGpD,KAAK,KAAK;EACpCmD,YAAY,EAAEnD,KAAK,CAACgD,aAAa;EACjCC,kBAAkB,EAAEjD,KAAK,CAACQ,IAAI,CAACR,KAAK,CAACgD,aAAa,CAAC,CAACK,GAAG,CAAC,CAAC,CAAC,CAACd,KAAK,CAAC,CAAC;EAClEW,gBAAgB,EAAElD,KAAK,CAACQ,IAAI,CAACR,KAAK,CAACgD,aAAa,CAAC,CAACK,GAAG,CAAC,CAAC,CAAC,CAACd,KAAK,CAAC;AACjE,CAAC,CAAC;AACF,MAAMe,gBAAgB,GAAGtD,KAAK,KAAK;EACjCsB,MAAM,EAAE,aAAa;EACrBiC,WAAW,EAAEvD,KAAK,CAACwD,mBAAmB;EACtC7B,KAAK,EAAE3B,KAAK,CAACyD,iBAAiB;EAC9BvC,UAAU,EAAElB,KAAK,CAAC0D,wBAAwB;EAC1CC,SAAS,EAAE;AACb,CAAC,CAAC;AACF,MAAMC,mBAAmB,GAAGA,CAACjB,MAAM,EAAEzB,UAAU,EAAE2C,SAAS,EAAEN,WAAW,EAAEO,iBAAiB,EAAEN,mBAAmB,EAAEZ,UAAU,EAAEC,WAAW,MAAM;EAC5I,CAAC,IAAIF,MAAM,mBAAmB,GAAGoB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;IAC3DrC,KAAK,EAAEkC,SAAS,IAAII,SAAS;IAC7B/C,UAAU;IACVqC,WAAW,EAAEA,WAAW,IAAIU,SAAS;IACrCN,SAAS,EAAE;EACb,CAAC,EAAEjB,yBAAyB,CAACC,MAAM,EAAEoB,MAAM,CAACC,MAAM,CAAC;IACjD9C;EACF,CAAC,EAAE0B,UAAU,CAAC,EAAEmB,MAAM,CAACC,MAAM,CAAC;IAC5B9C;EACF,CAAC,EAAE2B,WAAW,CAAC,CAAC,CAAC,EAAE;IACjB,YAAY,EAAE;MACZvB,MAAM,EAAE,aAAa;MACrBK,KAAK,EAAEmC,iBAAiB,IAAIG,SAAS;MACrCV,WAAW,EAAEC,mBAAmB,IAAIS;IACtC;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,2BAA2B,GAAGlE,KAAK,KAAK;EAC5C,CAAC,gBAAgBA,KAAK,CAACC,YAAY,WAAW,GAAG8D,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,gBAAgB,CAACtD,KAAK,CAAC;AAC5F,CAAC,CAAC;AACF,MAAMmE,0BAA0B,GAAGnE,KAAK,KAAK;EAC3C,CAAC,gBAAgBA,KAAK,CAACC,YAAY,WAAW,GAAG;IAC/CqB,MAAM,EAAE,aAAa;IACrBK,KAAK,EAAE3B,KAAK,CAACyD;EACf;AACF,CAAC,CAAC;AACF;AACA,MAAMW,qBAAqB,GAAGA,CAACpE,KAAK,EAAE4C,UAAU,EAAEC,WAAW,EAAEwB,OAAO,KAAK;EACzE,MAAMC,cAAc,GAAGD,OAAO,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAACE,QAAQ,CAACF,OAAO,CAAC;EACpE,MAAMG,sBAAsB,GAAGF,cAAc,GAAGH,0BAA0B,GAAGD,2BAA2B;EACxG,OAAOH,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEQ,sBAAsB,CAACxE,KAAK,CAAC,CAAC,EAAE0C,yBAAyB,CAAC1C,KAAK,CAACC,YAAY,EAAE2C,UAAU,EAAEC,WAAW,CAAC,CAAC;AAChJ,CAAC;AACD,MAAM4B,mBAAmB,GAAGA,CAACzE,KAAK,EAAE6D,SAAS,EAAE3C,UAAU,EAAE0B,UAAU,EAAEC,WAAW,MAAM;EACtF,CAAC,IAAI7C,KAAK,CAACC,YAAY,gBAAgB,GAAG8D,MAAM,CAACC,MAAM,CAAC;IACtDrC,KAAK,EAAEkC,SAAS;IAChB3C;EACF,CAAC,EAAEkD,qBAAqB,CAACpE,KAAK,EAAE4C,UAAU,EAAEC,WAAW,CAAC;AAC1D,CAAC,CAAC;AACF,MAAM6B,4BAA4B,GAAGA,CAAC1E,KAAK,EAAEuD,WAAW,EAAErC,UAAU,EAAE0B,UAAU,EAAEC,WAAW,MAAM;EACjG,CAAC,IAAI7C,KAAK,CAACC,YAAY,uBAAuBD,KAAK,CAACC,YAAY,iBAAiB,GAAG8D,MAAM,CAACC,MAAM,CAAC;IAChGT,WAAW;IACXrC;EACF,CAAC,EAAEkD,qBAAqB,CAACpE,KAAK,EAAE4C,UAAU,EAAEC,WAAW,CAAC;AAC1D,CAAC,CAAC;AACF,MAAM8B,oBAAoB,GAAG3E,KAAK,KAAK;EACrC,CAAC,IAAIA,KAAK,CAACC,YAAY,iBAAiB,GAAG;IACzC2E,WAAW,EAAE;EACf;AACF,CAAC,CAAC;AACF,MAAMC,oBAAoB,GAAGA,CAAC7E,KAAK,EAAEkB,UAAU,EAAE0B,UAAU,EAAEC,WAAW,MAAM;EAC5E,CAAC,IAAI7C,KAAK,CAACC,YAAY,iBAAiB,GAAG8D,MAAM,CAACC,MAAM,CAAC;IACvDL,SAAS,EAAE,MAAM;IACjBzC;EACF,CAAC,EAAEkD,qBAAqB,CAACpE,KAAK,EAAE4C,UAAU,EAAEC,WAAW,CAAC;AAC1D,CAAC,CAAC;AACF,MAAMiC,sBAAsB,GAAGA,CAAC9E,KAAK,EAAE6D,SAAS,EAAEQ,OAAO,EAAEzB,UAAU,EAAEC,WAAW,MAAM;EACtF,CAAC,IAAI7C,KAAK,CAACC,YAAY,YAAYoE,OAAO,EAAE,GAAGN,MAAM,CAACC,MAAM,CAAC;IAC3DrC,KAAK,EAAEkC,SAAS;IAChBF,SAAS,EAAE;EACb,CAAC,EAAES,qBAAqB,CAACpE,KAAK,EAAE4C,UAAU,EAAEC,WAAW,EAAEwB,OAAO,CAAC;AACnE,CAAC,CAAC;AACF;AACA,MAAMU,mBAAmB,GAAG/E,KAAK,IAAI;EACnC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAOP,YAAY,CAACuF,MAAM,CAAC,CAACC,IAAI,EAAEC,QAAQ,KAAK;IAC7C,MAAMC,SAAS,GAAGnF,KAAK,CAAC,GAAGkF,QAAQ,GAAG,CAAC;IACvC,MAAME,UAAU,GAAGpF,KAAK,CAAC,GAAGkF,QAAQ,GAAG,CAAC;IACxC,MAAMG,UAAU,GAAGrF,KAAK,CAAC,GAAGkF,QAAQ,GAAG,CAAC;IACxC,MAAMI,eAAe,GAAGtF,KAAK,CAAC,GAAGkF,QAAQ,GAAG,CAAC;IAC7C,MAAMK,gBAAgB,GAAGvF,KAAK,CAAC,GAAGkF,QAAQ,GAAG,CAAC;IAC9C,MAAMM,WAAW,GAAGxF,KAAK,CAAC,GAAGkF,QAAQ,GAAG,CAAC;IACzC,OAAOnB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEiB,IAAI,CAAC,EAAE;MAC5C,CAAC,IAAIhF,YAAY,UAAUiF,QAAQ,EAAE,GAAGnB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QAC1HrC,KAAK,EAAEwD,SAAS;QAChBxB,SAAS,EAAE3D,KAAK,CAAC,GAAGkF,QAAQ,aAAa;MAC3C,CAAC,EAAET,mBAAmB,CAACzE,KAAK,EAAEA,KAAK,CAACyF,mBAAmB,EAAEN,SAAS,EAAE;QAClEjE,UAAU,EAAEmE;MACd,CAAC,EAAE;QACDnE,UAAU,EAAEsE;MACd,CAAC,CAAC,CAAC,EAAEd,4BAA4B,CAAC1E,KAAK,EAAEmF,SAAS,EAAEnF,KAAK,CAAC0F,gBAAgB,EAAE;QAC1E/D,KAAK,EAAE0D,UAAU;QACjB9B,WAAW,EAAE8B,UAAU;QACvBnE,UAAU,EAAElB,KAAK,CAAC0F;MACpB,CAAC,EAAE;QACD/D,KAAK,EAAE6D,WAAW;QAClBjC,WAAW,EAAEiC,WAAW;QACxBtE,UAAU,EAAElB,KAAK,CAAC0F;MACpB,CAAC,CAAC,CAAC,EAAEf,oBAAoB,CAAC3E,KAAK,CAAC,CAAC,EAAE6E,oBAAoB,CAAC7E,KAAK,EAAEoF,UAAU,EAAE;QACzElE,UAAU,EAAEoE;MACd,CAAC,EAAE;QACDpE,UAAU,EAAEqE;MACd,CAAC,CAAC,CAAC,EAAET,sBAAsB,CAAC9E,KAAK,EAAEmF,SAAS,EAAE,MAAM,EAAE;QACpDxD,KAAK,EAAE0D;MACT,CAAC,EAAE;QACD1D,KAAK,EAAE6D;MACT,CAAC,CAAC,CAAC,EAAEV,sBAAsB,CAAC9E,KAAK,EAAEmF,SAAS,EAAE,MAAM,EAAE;QACpDxD,KAAK,EAAE0D,UAAU;QACjBnE,UAAU,EAAEkE;MACd,CAAC,EAAE;QACDzD,KAAK,EAAE6D,WAAW;QAClBtE,UAAU,EAAEqE;MACd,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;AACD,MAAMI,qBAAqB,GAAG3F,KAAK,IAAI+D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;EAC3GrC,KAAK,EAAE3B,KAAK,CAAC4F,YAAY;EACzBjC,SAAS,EAAE3D,KAAK,CAAC6F;AACnB,CAAC,EAAEpB,mBAAmB,CAACzE,KAAK,EAAEA,KAAK,CAAC8F,cAAc,EAAE9F,KAAK,CAAC+F,YAAY,EAAE;EACtEpE,KAAK,EAAE3B,KAAK,CAAC8F,cAAc;EAC3B5E,UAAU,EAAElB,KAAK,CAACgG;AACpB,CAAC,EAAE;EACDrE,KAAK,EAAE3B,KAAK,CAAC8F,cAAc;EAC3B5E,UAAU,EAAElB,KAAK,CAACiG;AACpB,CAAC,CAAC,CAAC,EAAEtB,oBAAoB,CAAC3E,KAAK,CAAC,CAAC,EAAE6E,oBAAoB,CAAC7E,KAAK,EAAEA,KAAK,CAACkG,iBAAiB,EAAE;EACtFhF,UAAU,EAAElB,KAAK,CAACmG;AACpB,CAAC,EAAE;EACDjF,UAAU,EAAElB,KAAK,CAACoG;AACpB,CAAC,CAAC,CAAC,EAAExC,mBAAmB,CAAC5D,KAAK,CAACC,YAAY,EAAED,KAAK,CAACqG,OAAO,EAAErG,KAAK,CAACsG,iBAAiB,EAAEtG,KAAK,CAACuG,uBAAuB,EAAEvG,KAAK,CAACyD,iBAAiB,EAAEzD,KAAK,CAACwG,WAAW,CAAC,CAAC,EAAE1B,sBAAsB,CAAC9E,KAAK,EAAEA,KAAK,CAACyG,aAAa,EAAE,MAAM,EAAE;EAC3N9E,KAAK,EAAE3B,KAAK,CAAC0G,cAAc;EAC3BxF,UAAU,EAAElB,KAAK,CAAC2G;AACpB,CAAC,EAAE;EACDhF,KAAK,EAAE3B,KAAK,CAAC4G;AACf,CAAC,CAAC,CAAC;AACH,MAAMC,qBAAqB,GAAG7G,KAAK,IAAI+D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;EACzHrC,KAAK,EAAE3B,KAAK,CAAC8G,YAAY;EACzBnD,SAAS,EAAE3D,KAAK,CAAC+G;AACnB,CAAC,EAAErC,4BAA4B,CAAC1E,KAAK,EAAEA,KAAK,CAAC8G,YAAY,EAAE9G,KAAK,CAAC0F,gBAAgB,EAAE;EACjF/D,KAAK,EAAE3B,KAAK,CAACgH,qBAAqB;EAClCzD,WAAW,EAAEvD,KAAK,CAACiH,iBAAiB;EACpC/F,UAAU,EAAElB,KAAK,CAAC0F;AACpB,CAAC,EAAE;EACD/D,KAAK,EAAE3B,KAAK,CAACkH,sBAAsB;EACnC3D,WAAW,EAAEvD,KAAK,CAACmH,kBAAkB;EACrCjG,UAAU,EAAElB,KAAK,CAAC0F;AACpB,CAAC,CAAC,CAAC,EAAEf,oBAAoB,CAAC3E,KAAK,CAAC,CAAC,EAAE6E,oBAAoB,CAAC7E,KAAK,EAAEA,KAAK,CAACoH,cAAc,EAAE;EACnFlG,UAAU,EAAElB,KAAK,CAACqH;AACpB,CAAC,EAAE;EACDnG,UAAU,EAAElB,KAAK,CAACsH;AACpB,CAAC,CAAC,CAAC,EAAExC,sBAAsB,CAAC9E,KAAK,EAAEA,KAAK,CAACuH,gBAAgB,EAAE,MAAM,EAAE;EACjE5F,KAAK,EAAE3B,KAAK,CAACgH,qBAAqB;EAClC9F,UAAU,EAAElB,KAAK,CAACoH;AACpB,CAAC,EAAE;EACDzF,KAAK,EAAE3B,KAAK,CAACkH,sBAAsB;EACnChG,UAAU,EAAElB,KAAK,CAACsH;AACpB,CAAC,CAAC,CAAC,EAAExC,sBAAsB,CAAC9E,KAAK,EAAEA,KAAK,CAACuH,gBAAgB,EAAE,MAAM,EAAE;EACjE5F,KAAK,EAAE3B,KAAK,CAACgH,qBAAqB;EAClC9F,UAAU,EAAElB,KAAK,CAAC2G;AACpB,CAAC,EAAE;EACDhF,KAAK,EAAE3B,KAAK,CAACkH;AACf,CAAC,CAAC,CAAC,EAAEtD,mBAAmB,CAAC5D,KAAK,CAACC,YAAY,EAAED,KAAK,CAACqG,OAAO,EAAErG,KAAK,CAAC8G,YAAY,EAAE9G,KAAK,CAAC8G,YAAY,EAAE9G,KAAK,CAACyD,iBAAiB,EAAEzD,KAAK,CAACwG,WAAW,EAAE;EAC9I7E,KAAK,EAAE3B,KAAK,CAACiH,iBAAiB;EAC9B1D,WAAW,EAAEvD,KAAK,CAACiH;AACrB,CAAC,EAAE;EACDtF,KAAK,EAAE3B,KAAK,CAACmH,kBAAkB;EAC/B5D,WAAW,EAAEvD,KAAK,CAACmH;AACrB,CAAC,CAAC,CAAC;AACH,MAAMK,iBAAiB,GAAGxH,KAAK,IAAI+D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;EACnIrC,KAAK,EAAE3B,KAAK,CAACyH,UAAU;EACvB9D,SAAS,EAAE3D,KAAK,CAAC0H;AACnB,CAAC,EAAEjD,mBAAmB,CAACzE,KAAK,EAAEA,KAAK,CAAC2H,WAAW,EAAE3H,KAAK,CAACyH,UAAU,EAAE;EACjEvG,UAAU,EAAElB,KAAK,CAAC4H;AACpB,CAAC,EAAE;EACD1G,UAAU,EAAElB,KAAK,CAAC6H;AACpB,CAAC,CAAC,CAAC,EAAEnD,4BAA4B,CAAC1E,KAAK,EAAEA,KAAK,CAACyH,UAAU,EAAEzH,KAAK,CAAC0F,gBAAgB,EAAE;EACjF/D,KAAK,EAAE3B,KAAK,CAAC4H,eAAe;EAC5BrE,WAAW,EAAEvD,KAAK,CAAC8H;AACrB,CAAC,EAAE;EACDnG,KAAK,EAAE3B,KAAK,CAAC6H,gBAAgB;EAC7BtE,WAAW,EAAEvD,KAAK,CAAC6H;AACrB,CAAC,CAAC,CAAC,EAAElD,oBAAoB,CAAC3E,KAAK,CAAC,CAAC,EAAE6E,oBAAoB,CAAC7E,KAAK,EAAEA,KAAK,CAAC+H,YAAY,EAAE;EACjF7G,UAAU,EAAElB,KAAK,CAACgI;AACpB,CAAC,EAAE;EACD9G,UAAU,EAAElB,KAAK,CAACiI;AACpB,CAAC,CAAC,CAAC,EAAEnD,sBAAsB,CAAC9E,KAAK,EAAEA,KAAK,CAACyH,UAAU,EAAE,MAAM,EAAE;EAC3D9F,KAAK,EAAE3B,KAAK,CAAC4H,eAAe;EAC5B1G,UAAU,EAAElB,KAAK,CAAC+H;AACpB,CAAC,EAAE;EACDpG,KAAK,EAAE3B,KAAK,CAAC4H,eAAe;EAC5B1G,UAAU,EAAElB,KAAK,CAACiI;AACpB,CAAC,CAAC,CAAC,EAAEnD,sBAAsB,CAAC9E,KAAK,EAAEA,KAAK,CAACyH,UAAU,EAAE,MAAM,EAAE;EAC3D9F,KAAK,EAAE3B,KAAK,CAAC4H;AACf,CAAC,EAAE;EACDjG,KAAK,EAAE3B,KAAK,CAAC6H;AACf,CAAC,CAAC,CAAC,EAAEjE,mBAAmB,CAAC5D,KAAK,CAACC,YAAY,EAAED,KAAK,CAACqG,OAAO,EAAErG,KAAK,CAACyH,UAAU,EAAEzH,KAAK,CAACyH,UAAU,EAAEzH,KAAK,CAACyD,iBAAiB,EAAEzD,KAAK,CAACwG,WAAW,EAAE;EAC1I7E,KAAK,EAAE3B,KAAK,CAAC4H,eAAe;EAC5BrE,WAAW,EAAEvD,KAAK,CAAC4H;AACrB,CAAC,EAAE;EACDjG,KAAK,EAAE3B,KAAK,CAAC6H,gBAAgB;EAC7BtE,WAAW,EAAEvD,KAAK,CAAC6H;AACrB,CAAC,CAAC,CAAC;AACH,MAAMK,YAAY,GAAGlI,KAAK,IAAI+D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEc,sBAAsB,CAAC9E,KAAK,EAAEA,KAAK,CAACmI,SAAS,EAAE,MAAM,EAAE;EACnHxG,KAAK,EAAE3B,KAAK,CAAC0G;AACf,CAAC,EAAE;EACD/E,KAAK,EAAE3B,KAAK,CAAC4G;AACf,CAAC,CAAC,CAAC,EAAEhD,mBAAmB,CAAC5D,KAAK,CAACC,YAAY,EAAED,KAAK,CAACqG,OAAO,EAAErG,KAAK,CAACoI,SAAS,EAAEpI,KAAK,CAACoI,SAAS,EAAEpI,KAAK,CAACyD,iBAAiB,EAAEzD,KAAK,CAACwG,WAAW,EAAE;EACxI7E,KAAK,EAAE3B,KAAK,CAACqI,cAAc;EAC3B9E,WAAW,EAAEvD,KAAK,CAACqI;AACrB,CAAC,EAAE;EACD1G,KAAK,EAAE3B,KAAK,CAACsI,eAAe;EAC5B/E,WAAW,EAAEvD,KAAK,CAACsI;AACrB,CAAC,CAAC,CAAC;AACH,MAAMC,mBAAmB,GAAGvI,KAAK,IAAI;EACnC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO+D,MAAM,CAACC,MAAM,CAAC;IACnB,CAAC,GAAG/D,YAAY,gBAAgB,GAAG0F,qBAAqB,CAAC3F,KAAK,CAAC;IAC/D,CAAC,GAAGC,YAAY,gBAAgB,GAAG4G,qBAAqB,CAAC7G,KAAK,CAAC;IAC/D,CAAC,GAAGC,YAAY,kBAAkB,GAAGuH,iBAAiB,CAACxH,KAAK,CAAC;IAC7D,CAAC,GAAGC,YAAY,aAAa,GAAGiI,YAAY,CAAClI,KAAK;EACpD,CAAC,EAAE+E,mBAAmB,CAAC/E,KAAK,CAAC,CAAC;AAChC,CAAC;AACD;AACA,MAAMwI,wBAAwB,GAAGxI,KAAK,IAAI+D,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEU,4BAA4B,CAAC1E,KAAK,EAAEA,KAAK,CAACyI,kBAAkB,EAAEzI,KAAK,CAAC0I,SAAS,EAAE;EACnL/G,KAAK,EAAE3B,KAAK,CAAC2I,iBAAiB;EAC9BpF,WAAW,EAAEvD,KAAK,CAAC4I,uBAAuB;EAC1C1H,UAAU,EAAElB,KAAK,CAAC6I;AACpB,CAAC,EAAE;EACDlH,KAAK,EAAE3B,KAAK,CAAC8I,kBAAkB;EAC/BvF,WAAW,EAAEvD,KAAK,CAAC+I,wBAAwB;EAC3C7H,UAAU,EAAElB,KAAK,CAACgJ;AACpB,CAAC,CAAC,CAAC,EAAElE,sBAAsB,CAAC9E,KAAK,EAAEA,KAAK,CAACyG,aAAa,EAAE,MAAM,EAAE;EAC9D9E,KAAK,EAAE3B,KAAK,CAACiJ,kBAAkB;EAC/B/H,UAAU,EAAElB,KAAK,CAACkJ;AACpB,CAAC,EAAE;EACDvH,KAAK,EAAE3B,KAAK,CAACmJ,mBAAmB;EAChCjI,UAAU,EAAElB,KAAK,CAACoJ;AACpB,CAAC,CAAC,CAAC,EAAE3E,mBAAmB,CAACzE,KAAK,EAAEA,KAAK,CAACqJ,YAAY,EAAErJ,KAAK,CAAC8G,YAAY,EAAE;EACtE5F,UAAU,EAAElB,KAAK,CAACiH,iBAAiB;EACnCtF,KAAK,EAAE3B,KAAK,CAACqJ;AACf,CAAC,EAAE;EACDnI,UAAU,EAAElB,KAAK,CAACmH,kBAAkB;EACpCxF,KAAK,EAAE3B,KAAK,CAACqJ;AACf,CAAC,CAAC,CAAC,EAAEvE,sBAAsB,CAAC9E,KAAK,EAAEA,KAAK,CAACmI,SAAS,EAAE,MAAM,EAAE;EAC1DxG,KAAK,EAAE3B,KAAK,CAAC0G,cAAc;EAC3BxF,UAAU,EAAElB,KAAK,CAAC2G;AACpB,CAAC,EAAE;EACDhF,KAAK,EAAE3B,KAAK,CAAC4G;AACf,CAAC,CAAC,CAAC;AACH;AACA,MAAM0C,cAAc,GAAG,SAAAA,CAAUtJ,KAAK,EAAE;EACtC,IAAIuJ,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKvF,SAAS,GAAGuF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACtF,MAAM;IACJvJ,YAAY;IACZ+C,aAAa;IACb0G,QAAQ;IACRvG,YAAY;IACZwG,uBAAuB;IACvBzJ,OAAO;IACP0J,qBAAqB;IACrBC;EACF,CAAC,GAAG7J,KAAK;EACT,OAAO,CAAC;IACN,CAACuJ,SAAS,GAAG;MACXG,QAAQ;MACRI,MAAM,EAAE9G,aAAa;MACrB+G,OAAO,EAAE,GAAGzK,IAAI,CAACsK,qBAAqB,CAAC,IAAItK,IAAI,CAACqK,uBAAuB,CAAC,EAAE;MAC1ExG,YAAY;MACZ,CAAC,IAAIlD,YAAY,YAAY,GAAG;QAC9BiC,KAAK,EAAEc,aAAa;QACpB,CAAC9C,OAAO,GAAG;UACTwJ,QAAQ,EAAEG;QACZ;MACF;IACF;EACF,CAAC;EACD;EACA;IACE,CAAC,GAAG5J,YAAY,GAAGA,YAAY,UAAUsJ,SAAS,EAAE,GAAGzG,oBAAoB,CAAC9C,KAAK;EACnF,CAAC,EAAE;IACD,CAAC,GAAGC,YAAY,GAAGA,YAAY,SAASsJ,SAAS,EAAE,GAAGnG,mBAAmB,CAACpD,KAAK;EACjF,CAAC,CAAC;AACJ,CAAC;AACD,MAAMgK,sBAAsB,GAAGhK,KAAK,IAAI;EACtC,MAAMiK,SAAS,GAAGtK,UAAU,CAACK,KAAK,EAAE;IAClC0J,QAAQ,EAAE1J,KAAK,CAACkK;EAClB,CAAC,CAAC;EACF,OAAOZ,cAAc,CAACW,SAAS,EAAEjK,KAAK,CAACC,YAAY,CAAC;AACtD,CAAC;AACD,MAAMkK,uBAAuB,GAAGnK,KAAK,IAAI;EACvC,MAAMoK,UAAU,GAAGzK,UAAU,CAACK,KAAK,EAAE;IACnCgD,aAAa,EAAEhD,KAAK,CAACqK,eAAe;IACpCX,QAAQ,EAAE1J,KAAK,CAACsK,iBAAiB;IACjCP,OAAO,EAAE/J,KAAK,CAACuK,SAAS;IACxBZ,uBAAuB,EAAE3J,KAAK,CAACwK,eAAe;IAC9CZ,qBAAqB,EAAE,CAAC;IACxBzG,YAAY,EAAEnD,KAAK,CAACyK,cAAc;IAClCZ,sBAAsB,EAAE7J,KAAK,CAAC0K;EAChC,CAAC,CAAC;EACF,OAAOpB,cAAc,CAACc,UAAU,EAAE,GAAGpK,KAAK,CAACC,YAAY,KAAK,CAAC;AAC/D,CAAC;AACD,MAAM0K,uBAAuB,GAAG3K,KAAK,IAAI;EACvC,MAAM4K,UAAU,GAAGjL,UAAU,CAACK,KAAK,EAAE;IACnCgD,aAAa,EAAEhD,KAAK,CAAC6K,eAAe;IACpCnB,QAAQ,EAAE1J,KAAK,CAAC8K,iBAAiB;IACjCnB,uBAAuB,EAAE3J,KAAK,CAAC+K,eAAe;IAC9CnB,qBAAqB,EAAE,CAAC;IACxBzG,YAAY,EAAEnD,KAAK,CAACgL,cAAc;IAClCnB,sBAAsB,EAAE7J,KAAK,CAACiL;EAChC,CAAC,CAAC;EACF,OAAO3B,cAAc,CAACsB,UAAU,EAAE,GAAG5K,KAAK,CAACC,YAAY,KAAK,CAAC;AAC/D,CAAC;AACD,MAAMiL,mBAAmB,GAAGlL,KAAK,IAAI;EACnC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAACC,YAAY,GAAG;MACd,CAAC,IAAIA,YAAY,QAAQ,GAAG;QAC1BiC,KAAK,EAAE;MACT;IACF;EACF,CAAC;AACH,CAAC;AACD;AACA,eAAexC,aAAa,CAAC,QAAQ,EAAEM,KAAK,IAAI;EAC9C,MAAMmL,WAAW,GAAGrL,YAAY,CAACE,KAAK,CAAC;EACvC,OAAO;EACP;EACAD,oBAAoB,CAACoL,WAAW,CAAC;EACjC;EACAnB,sBAAsB,CAACmB,WAAW,CAAC,EAAEhB,uBAAuB,CAACgB,WAAW,CAAC,EAAER,uBAAuB,CAACQ,WAAW,CAAC;EAC/G;EACAD,mBAAmB,CAACC,WAAW,CAAC;EAChC;EACA5C,mBAAmB,CAAC4C,WAAW,CAAC;EAChC;EACA3C,wBAAwB,CAAC2C,WAAW,CAAC;EACrC;EACAvL,aAAa,CAACuL,WAAW,CAAC,CAAC;AAC7B,CAAC,EAAEtL,qBAAqB,EAAE;EACxBuL,QAAQ,EAAE;IACRjL,UAAU,EAAE,IAAI;IAChBkL,iBAAiB,EAAE,IAAI;IACvBC,mBAAmB,EAAE,IAAI;IACzBC,mBAAmB,EAAE;EACvB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}