{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\DeviceManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, Space, Card, Tag, message } from 'antd';\nimport devicesData from '../data/devices.json';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst DeviceManagement = ({\n  id\n}, ref) => {\n  _s();\n  const [devices, setDevices] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingDevice, setEditingDevice] = useState(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentDevice, setCurrentDevice] = useState(null);\n  const [currentDeviceType, setCurrentDeviceType] = useState((editingDevice === null || editingDevice === void 0 ? void 0 : editingDevice.type) || null);\n\n  // 获取设备列表\n  const fetchDevices = () => {\n    try {\n      setLoading(true);\n      // 尝试从 localStorage 获取数据，如果没有则使用初始数据\n      const savedDevices = localStorage.getItem('devices');\n      if (savedDevices) {\n        setDevices(JSON.parse(savedDevices));\n      } else {\n        setDevices(devicesData.devices);\n        localStorage.setItem('devices', JSON.stringify(devicesData.devices));\n      }\n    } catch (error) {\n      console.error('获取设备列表失败:', error);\n      message.error('获取设备列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchDevices();\n  }, []);\n\n  // 保存设备数据\n  const saveDevices = updatedDevices => {\n    try {\n      localStorage.setItem('devices', JSON.stringify(updatedDevices));\n      setDevices(updatedDevices);\n      return true;\n    } catch (error) {\n      console.error('保存设备数据失败:', error);\n      message.error('保存设备数据失败: ' + (error.message || '未知错误'));\n      return false;\n    }\n  };\n\n  // 处理添加设备\n  const handleAddDevice = () => {\n    setEditingDevice(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // 处理编辑设备\n  const handleEditDevice = device => {\n    setEditingDevice(device);\n    setCurrentDeviceType(device.type);\n    form.setFieldsValue({\n      name: device.name,\n      type: device.type,\n      status: device.status,\n      location: device.location,\n      ipAddress: device.ipAddress,\n      manufacturer: device.manufacturer,\n      model: device.model,\n      description: device.description,\n      rtspUrl: device.rtspUrl\n    });\n    setModalVisible(true);\n  };\n\n  // 处理查看设备详情\n  const handleViewDevice = device => {\n    setCurrentDevice(device);\n    setDetailModalVisible(true);\n  };\n\n  // 处理删除设备\n  const handleDeleteDevice = deviceId => {\n    try {\n      const updatedDevices = devices.filter(device => device.id !== deviceId);\n      if (saveDevices(updatedDevices)) {\n        message.success('设备删除成功');\n      }\n    } catch (error) {\n      console.error('删除设备失败:', error);\n      message.error('删除设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 处理表单提交\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      if (values.type === 'camera') {\n        values.rtspUrl = values.rtspUrl || '';\n      } else {\n        values.rtspUrl = undefined;\n      }\n      let updatedDevices;\n      if (editingDevice) {\n        // 编辑设备\n        updatedDevices = devices.map(device => device.id === editingDevice.id ? {\n          ...device,\n          ...values\n        } : device);\n      } else {\n        // 添加设备\n        const newDevice = {\n          ...values,\n          id: `dev-${Date.now()}`,\n          createdAt: new Date().toISOString()\n        };\n        updatedDevices = [...devices, newDevice];\n      }\n      if (saveDevices(updatedDevices)) {\n        setModalVisible(false);\n        message.success(editingDevice ? '设备更新成功' : '设备添加成功');\n      }\n    } catch (error) {\n      console.error('保存设备失败:', error);\n      message.error('保存设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 修改设备类型选择的处理函数\n  const handleDeviceTypeChange = value => {\n    setCurrentDeviceType(value);\n    form.setFieldsValue({\n      type: value\n    });\n  };\n\n  // 渲染状态标签\n  const renderStatusTag = status => {\n    const statusMap = {\n      online: {\n        color: 'green',\n        text: '在线'\n      },\n      offline: {\n        color: 'gray',\n        text: '离线'\n      },\n      warning: {\n        color: 'orange',\n        text: '警告'\n      },\n      error: {\n        color: 'red',\n        text: '错误'\n      },\n      maintenance: {\n        color: 'blue',\n        text: '维护中'\n      }\n    };\n    const statusInfo = statusMap[status] || {\n      color: 'default',\n      text: status\n    };\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: statusInfo.color,\n      children: statusInfo.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 12\n    }, this);\n  };\n\n  // 设备类型映射\n  const deviceTypeMap = {\n    camera: '摄像头',\n    mmwave_radar: '毫米波雷达',\n    lidar: '激光雷达',\n    rsu: 'RSU',\n    edge_computing: '边缘计算单元'\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '设备名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '设备类型',\n    dataIndex: 'type',\n    key: 'type',\n    render: type => deviceTypeMap[type] || type\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: renderStatusTag\n  }, {\n    title: '位置',\n    dataIndex: 'location',\n    key: 'location'\n  }, {\n    title: 'IP地址',\n    dataIndex: 'ipAddress',\n    key: 'ipAddress'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleViewDevice(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleEditDevice(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        danger: true,\n        onClick: () => handleDeleteDevice(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 暴露 fetchDevices 方法\n  useImperativeHandle(ref, () => ({\n    fetchDevices\n  }));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: id,\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u8BBE\\u5907\\u5217\\u8868\",\n      extra: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: handleAddDevice,\n        children: \"\\u6DFB\\u52A0\\u8BBE\\u5907\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 16\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        loading: loading,\n        dataSource: devices,\n        columns: columns,\n        rowKey: \"id\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingDevice ? '编辑设备' : '添加设备',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => setModalVisible(false),\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8BBE\\u5907\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入设备名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"type\",\n          label: \"\\u8BBE\\u5907\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择设备类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u7C7B\\u578B\",\n            onChange: handleDeviceTypeChange,\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"camera\",\n              children: \"\\u6444\\u50CF\\u5934\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"mmwave_radar\",\n              children: \"\\u6BEB\\u7C73\\u6CE2\\u96F7\\u8FBE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"lidar\",\n              children: \"\\u6FC0\\u5149\\u96F7\\u8FBE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"rsu\",\n              children: \"RSU\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"edge_computing\",\n              children: \"\\u8FB9\\u7F18\\u8BA1\\u7B97\\u5355\\u5143\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), currentDeviceType === 'camera' && /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"rtspUrl\",\n          label: \"RTSP\\u5730\\u5740\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165RTSP\\u5730\\u5740\\uFF0C\\u4F8B\\u5982\\uFF1Artsp://admin:password@192.168.1.100:554/stream1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u8BBE\\u5907\\u72B6\\u6001\",\n          rules: [{\n            required: true,\n            message: '请选择设备状态'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u72B6\\u6001\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"online\",\n              children: \"\\u5728\\u7EBF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"offline\",\n              children: \"\\u79BB\\u7EBF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"warning\",\n              children: \"\\u8B66\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"error\",\n              children: \"\\u9519\\u8BEF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"maintenance\",\n              children: \"\\u7EF4\\u62A4\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"location\",\n          label: \"\\u8BBE\\u5907\\u4F4D\\u7F6E\",\n          rules: [{\n            required: true,\n            message: '请输入设备位置'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u4F4D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"ipAddress\",\n          label: \"IP\\u5730\\u5740\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165IP\\u5730\\u5740\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"manufacturer\",\n          label: \"\\u5236\\u9020\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5236\\u9020\\u5546\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"model\",\n          label: \"\\u578B\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u578B\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u8BBE\\u5907\\u63CF\\u8FF0\",\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            rows: 4,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u8BBE\\u5907\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 11\n      }, this)],\n      width: 600,\n      children: currentDevice && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8BBE\\u5907ID:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 16\n          }, this), \" \", currentDevice.id]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8BBE\\u5907\\u540D\\u79F0:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 16\n          }, this), \" \", currentDevice.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8BBE\\u5907\\u7C7B\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 16\n          }, this), \" \", deviceTypeMap[currentDevice.type] || currentDevice.type]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u72B6\\u6001:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 16\n          }, this), \" \", renderStatusTag(currentDevice.status)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u4F4D\\u7F6E:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 16\n          }, this), \" \", currentDevice.location]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"IP\\u5730\\u5740:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 16\n          }, this), \" \", currentDevice.ipAddress]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u6700\\u540E\\u7EF4\\u62A4\\u65F6\\u95F4:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 16\n          }, this), \" \", currentDevice.lastMaintenance ? new Date(currentDevice.lastMaintenance).toLocaleString() : '无记录']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u5B89\\u88C5\\u65E5\\u671F:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 16\n          }, this), \" \", currentDevice.installationDate ? new Date(currentDevice.installationDate).toLocaleString() : '无记录']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u5236\\u9020\\u5546:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 16\n          }, this), \" \", currentDevice.manufacturer || '未知']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u578B\\u53F7:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 16\n          }, this), \" \", currentDevice.model || '未知']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u63CF\\u8FF0:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 16\n          }, this), \" \", currentDevice.description || '无']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this), currentDevice.type === 'camera' && /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"RTSP\\u5730\\u5740:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 18\n          }, this), \" \", currentDevice.rtspUrl || '未设置']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 5\n  }, this);\n};\n_s(DeviceManagement, \"FEvoVtvtZFhj3MG4SXI/8+O84qg=\", false, function () {\n  return [Form.useForm];\n});\n_c = DeviceManagement;\nexport default _c2 = /*#__PURE__*/forwardRef(DeviceManagement);\nvar _c, _c2;\n$RefreshReg$(_c, \"DeviceManagement\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "Space", "Card", "Tag", "message", "devicesData", "jsxDEV", "_jsxDEV", "Option", "DeviceManagement", "id", "ref", "_s", "devices", "setDevices", "loading", "setLoading", "modalVisible", "setModalVisible", "form", "useForm", "editingDevice", "setEditingDevice", "detailModalVisible", "setDetailModalVisible", "currentDevice", "setCurrentDevice", "currentDeviceType", "setCurrentDeviceType", "type", "fetchDevices", "savedDevices", "localStorage", "getItem", "JSON", "parse", "setItem", "stringify", "error", "console", "saveDevices", "updatedDevices", "handleAddDevice", "resetFields", "handleEditDevice", "device", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "status", "location", "ip<PERSON><PERSON><PERSON>", "manufacturer", "model", "description", "rtspUrl", "handleViewDevice", "handleDeleteDevice", "deviceId", "filter", "success", "handleModalOk", "values", "validateFields", "undefined", "map", "newDevice", "Date", "now", "createdAt", "toISOString", "handleDeviceTypeChange", "value", "renderStatusTag", "statusMap", "online", "color", "text", "offline", "warning", "maintenance", "statusInfo", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "deviceTypeMap", "camera", "mmwave_radar", "lidar", "rsu", "edge_computing", "columns", "title", "dataIndex", "key", "render", "_", "record", "size", "onClick", "danger", "extra", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onOk", "onCancel", "width", "layout", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "onChange", "TextArea", "rows", "footer", "lastMaintenance", "toLocaleString", "installationDate", "_c", "_c2", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/DeviceManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\r\nimport { Table, Button, Modal, Form, Input, Select, Space, Card, Tag, message } from 'antd';\r\nimport devicesData from '../data/devices.json';\r\n\r\nconst { Option } = Select;\r\n\r\nconst DeviceManagement = ({ id }, ref) => {\r\n  const [devices, setDevices] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [form] = Form.useForm();\r\n  const [editingDevice, setEditingDevice] = useState(null);\r\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\r\n  const [currentDevice, setCurrentDevice] = useState(null);\r\n  const [currentDeviceType, setCurrentDeviceType] = useState(editingDevice?.type || null);\r\n\r\n  // 获取设备列表\r\n  const fetchDevices = () => {\r\n    try {\r\n      setLoading(true);\r\n      // 尝试从 localStorage 获取数据，如果没有则使用初始数据\r\n      const savedDevices = localStorage.getItem('devices');\r\n      if (savedDevices) {\r\n        setDevices(JSON.parse(savedDevices));\r\n      } else {\r\n        setDevices(devicesData.devices);\r\n        localStorage.setItem('devices', JSON.stringify(devicesData.devices));\r\n      }\r\n    } catch (error) {\r\n      console.error('获取设备列表失败:', error);\r\n      message.error('获取设备列表失败: ' + (error.message || '未知错误'));\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchDevices();\r\n  }, []);\r\n\r\n  // 保存设备数据\r\n  const saveDevices = (updatedDevices) => {\r\n    try {\r\n      localStorage.setItem('devices', JSON.stringify(updatedDevices));\r\n      setDevices(updatedDevices);\r\n      return true;\r\n    } catch (error) {\r\n      console.error('保存设备数据失败:', error);\r\n      message.error('保存设备数据失败: ' + (error.message || '未知错误'));\r\n      return false;\r\n    }\r\n  };\r\n\r\n  // 处理添加设备\r\n  const handleAddDevice = () => {\r\n    setEditingDevice(null);\r\n    form.resetFields();\r\n    setModalVisible(true);\r\n  };\r\n\r\n  // 处理编辑设备\r\n  const handleEditDevice = (device) => {\r\n    setEditingDevice(device);\r\n    setCurrentDeviceType(device.type);\r\n    form.setFieldsValue({\r\n      name: device.name,\r\n      type: device.type,\r\n      status: device.status,\r\n      location: device.location,\r\n      ipAddress: device.ipAddress,\r\n      manufacturer: device.manufacturer,\r\n      model: device.model,\r\n      description: device.description,\r\n      rtspUrl: device.rtspUrl\r\n    });\r\n    setModalVisible(true);\r\n  };\r\n\r\n  // 处理查看设备详情\r\n  const handleViewDevice = (device) => {\r\n    setCurrentDevice(device);\r\n    setDetailModalVisible(true);\r\n  };\r\n\r\n  // 处理删除设备\r\n  const handleDeleteDevice = (deviceId) => {\r\n    try {\r\n      const updatedDevices = devices.filter(device => device.id !== deviceId);\r\n      if (saveDevices(updatedDevices)) {\r\n        message.success('设备删除成功');\r\n      }\r\n    } catch (error) {\r\n      console.error('删除设备失败:', error);\r\n      message.error('删除设备失败: ' + (error.message || '未知错误'));\r\n    }\r\n  };\r\n\r\n  // 处理表单提交\r\n  const handleModalOk = async () => {\r\n    try {\r\n      const values = await form.validateFields();\r\n      \r\n      if (values.type === 'camera') {\r\n        values.rtspUrl = values.rtspUrl || '';\r\n      } else {\r\n        values.rtspUrl = undefined;\r\n      }\r\n\r\n      let updatedDevices;\r\n      if (editingDevice) {\r\n        // 编辑设备\r\n        updatedDevices = devices.map(device => \r\n          device.id === editingDevice.id ? { ...device, ...values } : device\r\n        );\r\n      } else {\r\n        // 添加设备\r\n        const newDevice = {\r\n          ...values,\r\n          id: `dev-${Date.now()}`,\r\n          createdAt: new Date().toISOString()\r\n        };\r\n        updatedDevices = [...devices, newDevice];\r\n      }\r\n\r\n      if (saveDevices(updatedDevices)) {\r\n        setModalVisible(false);\r\n        message.success(editingDevice ? '设备更新成功' : '设备添加成功');\r\n      }\r\n    } catch (error) {\r\n      console.error('保存设备失败:', error);\r\n      message.error('保存设备失败: ' + (error.message || '未知错误'));\r\n    }\r\n  };\r\n\r\n  // 修改设备类型选择的处理函数\r\n  const handleDeviceTypeChange = (value) => {\r\n    setCurrentDeviceType(value);\r\n    form.setFieldsValue({ type: value });\r\n  };\r\n\r\n  // 渲染状态标签\r\n  const renderStatusTag = (status) => {\r\n    const statusMap = {\r\n      online: { color: 'green', text: '在线' },\r\n      offline: { color: 'gray', text: '离线' },\r\n      warning: { color: 'orange', text: '警告' },\r\n      error: { color: 'red', text: '错误' },\r\n      maintenance: { color: 'blue', text: '维护中' }\r\n    };\r\n    \r\n    const statusInfo = statusMap[status] || { color: 'default', text: status };\r\n    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;\r\n  };\r\n\r\n  // 设备类型映射\r\n  const deviceTypeMap = {\r\n    camera: '摄像头',\r\n    mmwave_radar: '毫米波雷达',\r\n    lidar: '激光雷达',\r\n    rsu: 'RSU',\r\n    edge_computing: '边缘计算单元'\r\n  };\r\n\r\n  // 表格列定义\r\n  const columns = [\r\n    {\r\n      title: '设备名称',\r\n      dataIndex: 'name',\r\n      key: 'name',\r\n    },\r\n    {\r\n      title: '设备类型',\r\n      dataIndex: 'type',\r\n      key: 'type',\r\n      render: (type) => deviceTypeMap[type] || type\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'status',\r\n      key: 'status',\r\n      render: renderStatusTag\r\n    },\r\n    {\r\n      title: '位置',\r\n      dataIndex: 'location',\r\n      key: 'location',\r\n    },\r\n    {\r\n      title: 'IP地址',\r\n      dataIndex: 'ipAddress',\r\n      key: 'ipAddress',\r\n    },\r\n    {\r\n      title: '操作',\r\n      key: 'action',\r\n      render: (_, record) => (\r\n        <Space size=\"small\">\r\n          <Button type=\"link\" onClick={() => handleViewDevice(record)}>查看</Button>\r\n          <Button type=\"link\" onClick={() => handleEditDevice(record)}>编辑</Button>\r\n          <Button type=\"link\" danger onClick={() => handleDeleteDevice(record.id)}>删除</Button>\r\n        </Space>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  // 暴露 fetchDevices 方法\r\n  useImperativeHandle(ref, () => ({\r\n    fetchDevices\r\n  }));\r\n\r\n  return (\r\n    <div id={id}>\r\n      <Card \r\n        title=\"设备列表\" \r\n        extra={<Button type=\"primary\" onClick={handleAddDevice}>添加设备</Button>}\r\n      >\r\n        <Table \r\n          loading={loading}\r\n          dataSource={devices} \r\n          columns={columns} \r\n          rowKey=\"id\"\r\n        />\r\n      </Card>\r\n\r\n      {/* 添加/编辑设备表单 */}\r\n      <Modal\r\n        title={editingDevice ? '编辑设备' : '添加设备'}\r\n        open={modalVisible}\r\n        onOk={handleModalOk}\r\n        onCancel={() => setModalVisible(false)}\r\n        width={600}\r\n      >\r\n        <Form\r\n          form={form}\r\n          layout=\"vertical\"\r\n        >\r\n          <Form.Item\r\n            name=\"name\"\r\n            label=\"设备名称\"\r\n            rules={[{ required: true, message: '请输入设备名称' }]}\r\n          >\r\n            <Input placeholder=\"请输入设备名称\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"type\"\r\n            label=\"设备类型\"\r\n            rules={[{ required: true, message: '请选择设备类型' }]}\r\n          >\r\n            <Select \r\n              placeholder=\"请选择设备类型\"\r\n              onChange={handleDeviceTypeChange}\r\n            >\r\n              <Option value=\"camera\">摄像头</Option>\r\n              <Option value=\"mmwave_radar\">毫米波雷达</Option>\r\n              <Option value=\"lidar\">激光雷达</Option>\r\n              <Option value=\"rsu\">RSU</Option>\r\n              <Option value=\"edge_computing\">边缘计算单元</Option>\r\n            </Select>\r\n          </Form.Item>\r\n          \r\n          {/* 当设备类型为摄像头时显示 RTSP 地址输入 */}\r\n          {currentDeviceType === 'camera' && (\r\n            <Form.Item\r\n              name=\"rtspUrl\"\r\n              label=\"RTSP地址\"\r\n            >\r\n              <Input placeholder=\"请输入RTSP地址，例如：rtsp://admin:password@192.168.1.100:554/stream1\" />\r\n            </Form.Item>\r\n          )}\r\n          \r\n          <Form.Item\r\n            name=\"status\"\r\n            label=\"设备状态\"\r\n            rules={[{ required: true, message: '请选择设备状态' }]}\r\n          >\r\n            <Select placeholder=\"请选择设备状态\">\r\n              <Option value=\"online\">在线</Option>\r\n              <Option value=\"offline\">离线</Option>\r\n              <Option value=\"warning\">警告</Option>\r\n              <Option value=\"error\">错误</Option>\r\n              <Option value=\"maintenance\">维护中</Option>\r\n            </Select>\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"location\"\r\n            label=\"设备位置\"\r\n            rules={[{ required: true, message: '请输入设备位置' }]}\r\n          >\r\n            <Input placeholder=\"请输入设备位置\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"ipAddress\"\r\n            label=\"IP地址\"\r\n          >\r\n            <Input placeholder=\"请输入IP地址\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"manufacturer\"\r\n            label=\"制造商\"\r\n          >\r\n            <Input placeholder=\"请输入制造商\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"model\"\r\n            label=\"型号\"\r\n          >\r\n            <Input placeholder=\"请输入型号\" />\r\n          </Form.Item>\r\n          \r\n          <Form.Item\r\n            name=\"description\"\r\n            label=\"设备描述\"\r\n          >\r\n            <Input.TextArea rows={4} placeholder=\"请输入设备描述\" />\r\n          </Form.Item>\r\n        </Form>\r\n      </Modal>\r\n\r\n      {/* 设备详情模态框 */}\r\n      <Modal\r\n        title=\"设备详情\"\r\n        open={detailModalVisible}\r\n        onCancel={() => setDetailModalVisible(false)}\r\n        footer={[\r\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\r\n            关闭\r\n          </Button>\r\n        ]}\r\n        width={600}\r\n      >\r\n        {currentDevice && (\r\n          <div>\r\n            <p><strong>设备ID:</strong> {currentDevice.id}</p>\r\n            <p><strong>设备名称:</strong> {currentDevice.name}</p>\r\n            <p><strong>设备类型:</strong> {deviceTypeMap[currentDevice.type] || currentDevice.type}</p>\r\n            <p><strong>状态:</strong> {renderStatusTag(currentDevice.status)}</p>\r\n            <p><strong>位置:</strong> {currentDevice.location}</p>\r\n            <p><strong>IP地址:</strong> {currentDevice.ipAddress}</p>\r\n            <p><strong>最后维护时间:</strong> {currentDevice.lastMaintenance ? new Date(currentDevice.lastMaintenance).toLocaleString() : '无记录'}</p>\r\n            <p><strong>安装日期:</strong> {currentDevice.installationDate ? new Date(currentDevice.installationDate).toLocaleString() : '无记录'}</p>\r\n            <p><strong>制造商:</strong> {currentDevice.manufacturer || '未知'}</p>\r\n            <p><strong>型号:</strong> {currentDevice.model || '未知'}</p>\r\n            <p><strong>描述:</strong> {currentDevice.description || '无'}</p>\r\n            {currentDevice.type === 'camera' && (\r\n              <p><strong>RTSP地址:</strong> {currentDevice.rtspUrl || '未设置'}</p>\r\n            )}\r\n          </div>\r\n        )}\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default forwardRef(DeviceManagement); "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACnF,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,OAAO,QAAQ,MAAM;AAC3F,OAAOC,WAAW,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAM;EAAEC;AAAO,CAAC,GAAGR,MAAM;AAEzB,MAAMS,gBAAgB,GAAGA,CAAC;EAAEC;AAAG,CAAC,EAAEC,GAAG,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,IAAI,CAAC,GAAGrB,IAAI,CAACsB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACgC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrC,QAAQ,CAAC,CAAA8B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEQ,IAAI,KAAI,IAAI,CAAC;;EAEvF;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI;MACFd,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAMe,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC;MACpD,IAAIF,YAAY,EAAE;QAChBjB,UAAU,CAACoB,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,CAAC;MACtC,CAAC,MAAM;QACLjB,UAAU,CAACT,WAAW,CAACQ,OAAO,CAAC;QAC/BmB,YAAY,CAACI,OAAO,CAAC,SAAS,EAAEF,IAAI,CAACG,SAAS,CAAChC,WAAW,CAACQ,OAAO,CAAC,CAAC;MACtE;IACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjClC,OAAO,CAACkC,KAAK,CAAC,YAAY,IAAIA,KAAK,CAAClC,OAAO,IAAI,MAAM,CAAC,CAAC;IACzD,CAAC,SAAS;MACRY,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDxB,SAAS,CAAC,MAAM;IACdsC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,WAAW,GAAIC,cAAc,IAAK;IACtC,IAAI;MACFT,YAAY,CAACI,OAAO,CAAC,SAAS,EAAEF,IAAI,CAACG,SAAS,CAACI,cAAc,CAAC,CAAC;MAC/D3B,UAAU,CAAC2B,cAAc,CAAC;MAC1B,OAAO,IAAI;IACb,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjClC,OAAO,CAACkC,KAAK,CAAC,YAAY,IAAIA,KAAK,CAAClC,OAAO,IAAI,MAAM,CAAC,CAAC;MACvD,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAMsC,eAAe,GAAGA,CAAA,KAAM;IAC5BpB,gBAAgB,CAAC,IAAI,CAAC;IACtBH,IAAI,CAACwB,WAAW,CAAC,CAAC;IAClBzB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAM0B,gBAAgB,GAAIC,MAAM,IAAK;IACnCvB,gBAAgB,CAACuB,MAAM,CAAC;IACxBjB,oBAAoB,CAACiB,MAAM,CAAChB,IAAI,CAAC;IACjCV,IAAI,CAAC2B,cAAc,CAAC;MAClBC,IAAI,EAAEF,MAAM,CAACE,IAAI;MACjBlB,IAAI,EAAEgB,MAAM,CAAChB,IAAI;MACjBmB,MAAM,EAAEH,MAAM,CAACG,MAAM;MACrBC,QAAQ,EAAEJ,MAAM,CAACI,QAAQ;MACzBC,SAAS,EAAEL,MAAM,CAACK,SAAS;MAC3BC,YAAY,EAAEN,MAAM,CAACM,YAAY;MACjCC,KAAK,EAAEP,MAAM,CAACO,KAAK;MACnBC,WAAW,EAAER,MAAM,CAACQ,WAAW;MAC/BC,OAAO,EAAET,MAAM,CAACS;IAClB,CAAC,CAAC;IACFpC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMqC,gBAAgB,GAAIV,MAAM,IAAK;IACnCnB,gBAAgB,CAACmB,MAAM,CAAC;IACxBrB,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMgC,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,IAAI;MACF,MAAMhB,cAAc,GAAG5B,OAAO,CAAC6C,MAAM,CAACb,MAAM,IAAIA,MAAM,CAACnC,EAAE,KAAK+C,QAAQ,CAAC;MACvE,IAAIjB,WAAW,CAACC,cAAc,CAAC,EAAE;QAC/BrC,OAAO,CAACuD,OAAO,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BlC,OAAO,CAACkC,KAAK,CAAC,UAAU,IAAIA,KAAK,CAAClC,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMwD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM1C,IAAI,CAAC2C,cAAc,CAAC,CAAC;MAE1C,IAAID,MAAM,CAAChC,IAAI,KAAK,QAAQ,EAAE;QAC5BgC,MAAM,CAACP,OAAO,GAAGO,MAAM,CAACP,OAAO,IAAI,EAAE;MACvC,CAAC,MAAM;QACLO,MAAM,CAACP,OAAO,GAAGS,SAAS;MAC5B;MAEA,IAAItB,cAAc;MAClB,IAAIpB,aAAa,EAAE;QACjB;QACAoB,cAAc,GAAG5B,OAAO,CAACmD,GAAG,CAACnB,MAAM,IACjCA,MAAM,CAACnC,EAAE,KAAKW,aAAa,CAACX,EAAE,GAAG;UAAE,GAAGmC,MAAM;UAAE,GAAGgB;QAAO,CAAC,GAAGhB,MAC9D,CAAC;MACH,CAAC,MAAM;QACL;QACA,MAAMoB,SAAS,GAAG;UAChB,GAAGJ,MAAM;UACTnD,EAAE,EAAE,OAAOwD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UACvBC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC;QACpC,CAAC;QACD5B,cAAc,GAAG,CAAC,GAAG5B,OAAO,EAAEoD,SAAS,CAAC;MAC1C;MAEA,IAAIzB,WAAW,CAACC,cAAc,CAAC,EAAE;QAC/BvB,eAAe,CAAC,KAAK,CAAC;QACtBd,OAAO,CAACuD,OAAO,CAACtC,aAAa,GAAG,QAAQ,GAAG,QAAQ,CAAC;MACtD;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BlC,OAAO,CAACkC,KAAK,CAAC,UAAU,IAAIA,KAAK,CAAClC,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMkE,sBAAsB,GAAIC,KAAK,IAAK;IACxC3C,oBAAoB,CAAC2C,KAAK,CAAC;IAC3BpD,IAAI,CAAC2B,cAAc,CAAC;MAAEjB,IAAI,EAAE0C;IAAM,CAAC,CAAC;EACtC,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIxB,MAAM,IAAK;IAClC,MAAMyB,SAAS,GAAG;MAChBC,MAAM,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAK,CAAC;MACtCC,OAAO,EAAE;QAAEF,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAK,CAAC;MACtCE,OAAO,EAAE;QAAEH,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAK,CAAC;MACxCtC,KAAK,EAAE;QAAEqC,KAAK,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAK,CAAC;MACnCG,WAAW,EAAE;QAAEJ,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM;IAC5C,CAAC;IAED,MAAMI,UAAU,GAAGP,SAAS,CAACzB,MAAM,CAAC,IAAI;MAAE2B,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE5B;IAAO,CAAC;IAC1E,oBAAOzC,OAAA,CAACJ,GAAG;MAACwE,KAAK,EAAEK,UAAU,CAACL,KAAM;MAAAM,QAAA,EAAED,UAAU,CAACJ;IAAI;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG;IACpBC,MAAM,EAAE,KAAK;IACbC,YAAY,EAAE,OAAO;IACrBC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,KAAK;IACVC,cAAc,EAAE;EAClB,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGnE,IAAI,IAAKyD,aAAa,CAACzD,IAAI,CAAC,IAAIA;EAC3C,CAAC,EACD;IACEgE,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAExB;EACV,CAAC,EACD;IACEqB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAM,kBAChB3F,OAAA,CAACN,KAAK;MAACkG,IAAI,EAAC,OAAO;MAAAlB,QAAA,gBACjB1E,OAAA,CAACX,MAAM;QAACiC,IAAI,EAAC,MAAM;QAACuE,OAAO,EAAEA,CAAA,KAAM7C,gBAAgB,CAAC2C,MAAM,CAAE;QAAAjB,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxE9E,OAAA,CAACX,MAAM;QAACiC,IAAI,EAAC,MAAM;QAACuE,OAAO,EAAEA,CAAA,KAAMxD,gBAAgB,CAACsD,MAAM,CAAE;QAAAjB,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxE9E,OAAA,CAACX,MAAM;QAACiC,IAAI,EAAC,MAAM;QAACwE,MAAM;QAACD,OAAO,EAAEA,CAAA,KAAM5C,kBAAkB,CAAC0C,MAAM,CAACxF,EAAE,CAAE;QAAAuE,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E;EAEX,CAAC,CACF;;EAED;EACA3F,mBAAmB,CAACiB,GAAG,EAAE,OAAO;IAC9BmB;EACF,CAAC,CAAC,CAAC;EAEH,oBACEvB,OAAA;IAAKG,EAAE,EAAEA,EAAG;IAAAuE,QAAA,gBACV1E,OAAA,CAACL,IAAI;MACH2F,KAAK,EAAC,0BAAM;MACZS,KAAK,eAAE/F,OAAA,CAACX,MAAM;QAACiC,IAAI,EAAC,SAAS;QAACuE,OAAO,EAAE1D,eAAgB;QAAAuC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAE;MAAAJ,QAAA,eAEtE1E,OAAA,CAACZ,KAAK;QACJoB,OAAO,EAAEA,OAAQ;QACjBwF,UAAU,EAAE1F,OAAQ;QACpB+E,OAAO,EAAEA,OAAQ;QACjBY,MAAM,EAAC;MAAI;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP9E,OAAA,CAACV,KAAK;MACJgG,KAAK,EAAExE,aAAa,GAAG,MAAM,GAAG,MAAO;MACvCoF,IAAI,EAAExF,YAAa;MACnByF,IAAI,EAAE9C,aAAc;MACpB+C,QAAQ,EAAEA,CAAA,KAAMzF,eAAe,CAAC,KAAK,CAAE;MACvC0F,KAAK,EAAE,GAAI;MAAA3B,QAAA,eAEX1E,OAAA,CAACT,IAAI;QACHqB,IAAI,EAAEA,IAAK;QACX0F,MAAM,EAAC,UAAU;QAAA5B,QAAA,gBAEjB1E,OAAA,CAACT,IAAI,CAACgH,IAAI;UACR/D,IAAI,EAAC,MAAM;UACXgE,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7G,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6E,QAAA,eAEhD1E,OAAA,CAACR,KAAK;YAACmH,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZ9E,OAAA,CAACT,IAAI,CAACgH,IAAI;UACR/D,IAAI,EAAC,MAAM;UACXgE,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7G,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6E,QAAA,eAEhD1E,OAAA,CAACP,MAAM;YACLkH,WAAW,EAAC,4CAAS;YACrBC,QAAQ,EAAE7C,sBAAuB;YAAAW,QAAA,gBAEjC1E,OAAA,CAACC,MAAM;cAAC+D,KAAK,EAAC,QAAQ;cAAAU,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnC9E,OAAA,CAACC,MAAM;cAAC+D,KAAK,EAAC,cAAc;cAAAU,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3C9E,OAAA,CAACC,MAAM;cAAC+D,KAAK,EAAC,OAAO;cAAAU,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnC9E,OAAA,CAACC,MAAM;cAAC+D,KAAK,EAAC,KAAK;cAAAU,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChC9E,OAAA,CAACC,MAAM;cAAC+D,KAAK,EAAC,gBAAgB;cAAAU,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAGX1D,iBAAiB,KAAK,QAAQ,iBAC7BpB,OAAA,CAACT,IAAI,CAACgH,IAAI;UACR/D,IAAI,EAAC,SAAS;UACdgE,KAAK,EAAC,kBAAQ;UAAA9B,QAAA,eAEd1E,OAAA,CAACR,KAAK;YAACmH,WAAW,EAAC;UAA8D;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CACZ,eAED9E,OAAA,CAACT,IAAI,CAACgH,IAAI;UACR/D,IAAI,EAAC,QAAQ;UACbgE,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7G,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6E,QAAA,eAEhD1E,OAAA,CAACP,MAAM;YAACkH,WAAW,EAAC,4CAAS;YAAAjC,QAAA,gBAC3B1E,OAAA,CAACC,MAAM;cAAC+D,KAAK,EAAC,QAAQ;cAAAU,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC9E,OAAA,CAACC,MAAM;cAAC+D,KAAK,EAAC,SAAS;cAAAU,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnC9E,OAAA,CAACC,MAAM;cAAC+D,KAAK,EAAC,SAAS;cAAAU,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnC9E,OAAA,CAACC,MAAM;cAAC+D,KAAK,EAAC,OAAO;cAAAU,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjC9E,OAAA,CAACC,MAAM;cAAC+D,KAAK,EAAC,aAAa;cAAAU,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ9E,OAAA,CAACT,IAAI,CAACgH,IAAI;UACR/D,IAAI,EAAC,UAAU;UACfgE,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE7G,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6E,QAAA,eAEhD1E,OAAA,CAACR,KAAK;YAACmH,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZ9E,OAAA,CAACT,IAAI,CAACgH,IAAI;UACR/D,IAAI,EAAC,WAAW;UAChBgE,KAAK,EAAC,gBAAM;UAAA9B,QAAA,eAEZ1E,OAAA,CAACR,KAAK;YAACmH,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZ9E,OAAA,CAACT,IAAI,CAACgH,IAAI;UACR/D,IAAI,EAAC,cAAc;UACnBgE,KAAK,EAAC,oBAAK;UAAA9B,QAAA,eAEX1E,OAAA,CAACR,KAAK;YAACmH,WAAW,EAAC;UAAQ;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEZ9E,OAAA,CAACT,IAAI,CAACgH,IAAI;UACR/D,IAAI,EAAC,OAAO;UACZgE,KAAK,EAAC,cAAI;UAAA9B,QAAA,eAEV1E,OAAA,CAACR,KAAK;YAACmH,WAAW,EAAC;UAAO;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEZ9E,OAAA,CAACT,IAAI,CAACgH,IAAI;UACR/D,IAAI,EAAC,aAAa;UAClBgE,KAAK,EAAC,0BAAM;UAAA9B,QAAA,eAEZ1E,OAAA,CAACR,KAAK,CAACqH,QAAQ;YAACC,IAAI,EAAE,CAAE;YAACH,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR9E,OAAA,CAACV,KAAK;MACJgG,KAAK,EAAC,0BAAM;MACZY,IAAI,EAAElF,kBAAmB;MACzBoF,QAAQ,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,KAAK,CAAE;MAC7C8F,MAAM,EAAE,cACN/G,OAAA,CAACX,MAAM;QAAawG,OAAO,EAAEA,CAAA,KAAM5E,qBAAqB,CAAC,KAAK,CAAE;QAAAyD,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFuB,KAAK,EAAE,GAAI;MAAA3B,QAAA,EAEVxD,aAAa,iBACZlB,OAAA;QAAA0E,QAAA,gBACE1E,OAAA;UAAA0E,QAAA,gBAAG1E,OAAA;YAAA0E,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC5D,aAAa,CAACf,EAAE;QAAA;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChD9E,OAAA;UAAA0E,QAAA,gBAAG1E,OAAA;YAAA0E,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC5D,aAAa,CAACsB,IAAI;QAAA;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClD9E,OAAA;UAAA0E,QAAA,gBAAG1E,OAAA;YAAA0E,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACC,aAAa,CAAC7D,aAAa,CAACI,IAAI,CAAC,IAAIJ,aAAa,CAACI,IAAI;QAAA;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvF9E,OAAA;UAAA0E,QAAA,gBAAG1E,OAAA;YAAA0E,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACb,eAAe,CAAC/C,aAAa,CAACuB,MAAM,CAAC;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnE9E,OAAA;UAAA0E,QAAA,gBAAG1E,OAAA;YAAA0E,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC5D,aAAa,CAACwB,QAAQ;QAAA;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpD9E,OAAA;UAAA0E,QAAA,gBAAG1E,OAAA;YAAA0E,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC5D,aAAa,CAACyB,SAAS;QAAA;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvD9E,OAAA;UAAA0E,QAAA,gBAAG1E,OAAA;YAAA0E,QAAA,EAAQ;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC5D,aAAa,CAAC8F,eAAe,GAAG,IAAIrD,IAAI,CAACzC,aAAa,CAAC8F,eAAe,CAAC,CAACC,cAAc,CAAC,CAAC,GAAG,KAAK;QAAA;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClI9E,OAAA;UAAA0E,QAAA,gBAAG1E,OAAA;YAAA0E,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC5D,aAAa,CAACgG,gBAAgB,GAAG,IAAIvD,IAAI,CAACzC,aAAa,CAACgG,gBAAgB,CAAC,CAACD,cAAc,CAAC,CAAC,GAAG,KAAK;QAAA;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClI9E,OAAA;UAAA0E,QAAA,gBAAG1E,OAAA;YAAA0E,QAAA,EAAQ;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC5D,aAAa,CAAC0B,YAAY,IAAI,IAAI;QAAA;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjE9E,OAAA;UAAA0E,QAAA,gBAAG1E,OAAA;YAAA0E,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC5D,aAAa,CAAC2B,KAAK,IAAI,IAAI;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzD9E,OAAA;UAAA0E,QAAA,gBAAG1E,OAAA;YAAA0E,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC5D,aAAa,CAAC4B,WAAW,IAAI,GAAG;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC7D5D,aAAa,CAACI,IAAI,KAAK,QAAQ,iBAC9BtB,OAAA;UAAA0E,QAAA,gBAAG1E,OAAA;YAAA0E,QAAA,EAAQ;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC5D,aAAa,CAAC6B,OAAO,IAAI,KAAK;QAAA;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAChE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACzE,EAAA,CA9VIH,gBAAgB;EAAA,QAILX,IAAI,CAACsB,OAAO;AAAA;AAAAsG,EAAA,GAJvBjH,gBAAgB;AAgWtB,eAAAkH,GAAA,gBAAelI,UAAU,CAACgB,gBAAgB,CAAC;AAAC,IAAAiH,EAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAF,EAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}