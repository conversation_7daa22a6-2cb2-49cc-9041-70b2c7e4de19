{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\RealTimeTraffic.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { PieChart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport DeviceStatistics from '../components/DeviceStatistics';\n\n// 注册必要的echarts组件\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\necharts.use([<PERSON><PERSON><PERSON>, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\nconst StyledCanvas = styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 24px' : props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 0' : props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\n_c = MainContent;\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n  \n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\nconst Container = styled.div`\n  display: flex;\n  height: 100%;\n`;\n_c2 = Container;\nconst Sidebar = styled.div`\n  width: 300px;\n  background: white;\n  padding: 20px;\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\n`;\n_c3 = Sidebar;\nconst Title = styled.h2`\n  font-size: 16px;\n  color: rgba(0, 0, 0, 0.85);\n  margin-bottom: 16px;\n`;\n_c4 = Title;\nconst RealTimeTraffic = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [vehicles, setVehicles] = useState([]);\n  const [events, setEvents] = useState([]);\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\n  const [stats, setStats] = useState({\n    totalVehicles: 0,\n    onlineVehicles: 0,\n    offlineVehicles: 0,\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0\n  });\n  const eventChartRef = useRef(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  // 模拟数据加载\n  useEffect(() => {\n    let chart = null;\n    let handleResize = null;\n\n    // 模拟加载延迟\n    setTimeout(() => {\n      // 确保 DOM 元素存在\n      if (!eventChartRef.current) {\n        console.error('图表容器未找到');\n        return;\n      }\n\n      // 检查并清理已存在的图表实例\n      let existingChart = echarts.getInstanceByDom(eventChartRef.current);\n      if (existingChart) {\n        existingChart.dispose();\n      }\n      try {\n        // 初始化新的图表实例\n        chart = echarts.init(eventChartRef.current);\n\n        // 设置图表配置\n        chart.setOption({\n          title: {\n            text: '事件类型分布',\n            left: 'center',\n            textStyle: {\n              fontSize: 14\n            }\n          },\n          tooltip: {\n            trigger: 'item',\n            formatter: '{a} <br/>{b}: {c} ({d}%)'\n          },\n          legend: {\n            orient: 'vertical',\n            left: 10,\n            top: 30,\n            itemWidth: 10,\n            itemHeight: 10,\n            textStyle: {\n              fontSize: 12\n            }\n          },\n          series: [{\n            name: '事件类型',\n            type: 'pie',\n            radius: ['40%', '70%'],\n            avoidLabelOverlap: false,\n            itemStyle: {\n              borderRadius: 4\n            },\n            label: {\n              show: false\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: 12\n              }\n            },\n            labelLine: {\n              show: false\n            },\n            data: [{\n              value: 2,\n              name: '超速'\n            }, {\n              value: 1,\n              name: '急刹车'\n            }, {\n              value: 1,\n              name: '变道'\n            }, {\n              value: 1,\n              name: '启动'\n            }, {\n              value: 1,\n              name: '停止'\n            }]\n          }]\n        });\n\n        // 监听窗口大小变化，调整图表大小\n        handleResize = () => {\n          var _chart;\n          (_chart = chart) === null || _chart === void 0 ? void 0 : _chart.resize();\n        };\n        window.addEventListener('resize', handleResize);\n      } catch (error) {\n        console.error('初始化图表失败:', error);\n      }\n      setLoading(false);\n    }, 1500);\n\n    // 组件卸载时清理\n    return () => {\n      if (handleResize) {\n        window.removeEventListener('resize', handleResize);\n      }\n      if (chart) {\n        chart.dispose();\n      }\n    };\n  }, []);\n\n  // 处理车辆选择\n  const handleVehicleSelect = vehicle => {\n    setSelectedVehicle(vehicle);\n  };\n\n  // 车辆列表列定义\n  const vehicleColumns = [{\n    title: '车牌号',\n    dataIndex: 'plate',\n    key: 'plate',\n    width: '40%'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: '30%',\n    render: status => /*#__PURE__*/_jsxDEV(Badge, {\n      status: status === 'online' ? 'success' : 'error',\n      text: status === 'online' ? '在线' : '离线'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '速度',\n    dataIndex: 'speed',\n    key: 'speed',\n    width: '30%',\n    render: speed => `${speed} km/h`\n  }];\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    spinning: loading,\n    tip: \"\\u52A0\\u8F7D\\u4E2D...\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          children: \"\\u8BBE\\u5907\\u548C\\u8F66\\u8F86\\u7EDF\\u8BA1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DeviceStatistics, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        leftCollapsed: leftCollapsed,\n        rightCollapsed: rightCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 281,\n    columnNumber: 5\n  }, this);\n};\n_s(RealTimeTraffic, \"MUqPouPaD6RZBSnJmhkSDTSRCR8=\");\n_c5 = RealTimeTraffic;\nexport default RealTimeTraffic;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"MainContent\");\n$RefreshReg$(_c2, \"Container\");\n$RefreshReg$(_c3, \"Sidebar\");\n$RefreshReg$(_c4, \"Title\");\n$RefreshReg$(_c5, \"RealTimeTraffic\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Row", "Col", "Card", "Statistic", "List", "Table", "Descriptions", "Spin", "Badge", "echarts", "<PERSON><PERSON><PERSON>", "GridComponent", "TooltipComponent", "LegendComponent", "TitleComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "CollapsibleSidebar", "DeviceStatistics", "jsxDEV", "_jsxDEV", "use", "StyledCanvas", "div", "<PERSON><PERSON><PERSON><PERSON>", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "_c", "InfoCard", "height", "CompactStatistic", "Container", "_c2", "Sidebar", "_c3", "Title", "h2", "_c4", "RealTimeTraffic", "_s", "loading", "setLoading", "vehicles", "setVehicles", "events", "setEvents", "selectedVehicle", "setSelectedVehicle", "stats", "setStats", "totalVehicles", "onlineVehicles", "offlineVehicles", "totalDevices", "onlineDevices", "offlineDevices", "eventChartRef", "setLeftCollapsed", "setRightCollapsed", "chart", "handleResize", "setTimeout", "current", "console", "error", "existingChart", "getInstanceByDom", "dispose", "init", "setOption", "title", "text", "left", "textStyle", "fontSize", "tooltip", "trigger", "formatter", "legend", "orient", "top", "itemWidth", "itemHeight", "series", "name", "type", "radius", "avoidLabelOverlap", "itemStyle", "borderRadius", "label", "show", "emphasis", "labelLine", "data", "value", "_chart", "resize", "window", "addEventListener", "removeEventListener", "handleVehicleSelect", "vehicle", "vehicleColumns", "dataIndex", "key", "width", "render", "status", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "speed", "spinning", "tip", "children", "_c5", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RealTimeTraffic.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge } from 'antd';\r\nimport * as echarts from 'echarts/core';\r\nimport { Pie<PERSON>hart } from 'echarts/charts';\r\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\r\nimport { CanvasRenderer } from 'echarts/renderers';\r\nimport styled from 'styled-components';\r\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\r\nimport DeviceStatistics from '../components/DeviceStatistics';\r\n\r\n// 注册必要的echarts组件\r\necharts.use([PieChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\r\n\r\nconst StyledCanvas = styled.div`\r\n  width: 100%;\r\n  height: 600px;\r\n  background: #f0f2f5;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  z-index: 1;\r\n`;\r\n\r\n// 页面布局容器\r\nconst PageContainer = styled.div`\r\n  display: flex;\r\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\r\n  overflow: hidden;\r\n`;\r\n\r\n// 左侧信息栏容器\r\nconst LeftSidebar = styled.div`\r\n  width: 320px;\r\n  height: 100%;\r\n  padding: 0 8px 0 0;\r\n  border-right: 1px solid #f0f0f0;\r\n  display: flex;\r\n  flex-direction: column;\r\n`;\r\n\r\n// 右侧信息栏容器\r\nconst RightSidebar = styled.div`\r\n  width: 320px;\r\n  height: 100%;\r\n  padding: 0 0 0 8px;\r\n  border-left: 1px solid #f0f0f0;\r\n  display: flex;\r\n  flex-direction: column;\r\n`;\r\n\r\n// 主内容区域\r\nconst MainContent = styled.div`\r\n  flex: 1;\r\n  height: 100%;\r\n  overflow: hidden;\r\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \r\n    props.leftCollapsed ? '0 8px 0 24px' : \r\n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\r\n  transition: all 0.3s ease;\r\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \r\n    props.leftCollapsed ? '0 8px 0 0' : \r\n    props.rightCollapsed ? '0 0 0 8px' : '0'};\r\n  position: relative;\r\n  z-index: 1;\r\n`;\r\n\r\n// 信息卡片\r\nconst InfoCard = styled(Card)`\r\n  margin-bottom: 12px;\r\n  height: ${props => props.height || 'auto'};\r\n  \r\n  .ant-card-head {\r\n    min-height: 40px;\r\n    padding: 0 12px;\r\n  }\r\n  \r\n  .ant-card-head-title {\r\n    padding: 8px 0;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .ant-card-body {\r\n    padding: 12px;\r\n    font-size: 13px;\r\n    height: calc(100% - 40px); // 减去卡片头部高度\r\n    overflow-y: auto;\r\n  }\r\n  \r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n`;\r\n\r\n// 自定义统计数字组件\r\nconst CompactStatistic = styled(Statistic)`\r\n  .ant-statistic-title {\r\n    font-size: 12px;\r\n    margin-bottom: 2px;\r\n  }\r\n  \r\n  .ant-statistic-content {\r\n    font-size: 16px;\r\n  }\r\n`;\r\n\r\nconst Container = styled.div`\r\n  display: flex;\r\n  height: 100%;\r\n`;\r\n\r\nconst Sidebar = styled.div`\r\n  width: 300px;\r\n  background: white;\r\n  padding: 20px;\r\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\r\n`;\r\n\r\nconst Title = styled.h2`\r\n  font-size: 16px;\r\n  color: rgba(0, 0, 0, 0.85);\r\n  margin-bottom: 16px;\r\n`;\r\n\r\nconst RealTimeTraffic = () => {\r\n  const [loading, setLoading] = useState(true);\r\n  const [vehicles, setVehicles] = useState([]);\r\n  const [events, setEvents] = useState([]);\r\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\r\n  const [stats, setStats] = useState({\r\n    totalVehicles: 0,\r\n    onlineVehicles: 0,\r\n    offlineVehicles: 0,\r\n    totalDevices: 0,\r\n    onlineDevices: 0,\r\n    offlineDevices: 0\r\n  });\r\n  \r\n  const eventChartRef = useRef(null);\r\n  \r\n  // 添加侧边栏折叠状态\r\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\r\n  const [rightCollapsed, setRightCollapsed] = useState(false);\r\n  \r\n  // 模拟数据加载\r\n  useEffect(() => {\r\n    let chart = null;\r\n    let handleResize = null;\r\n\r\n    // 模拟加载延迟\r\n    setTimeout(() => {\r\n      // 确保 DOM 元素存在\r\n      if (!eventChartRef.current) {\r\n        console.error('图表容器未找到');\r\n        return;\r\n      }\r\n\r\n      // 检查并清理已存在的图表实例\r\n      let existingChart = echarts.getInstanceByDom(eventChartRef.current);\r\n      if (existingChart) {\r\n        existingChart.dispose();\r\n      }\r\n\r\n      try {\r\n        // 初始化新的图表实例\r\n        chart = echarts.init(eventChartRef.current);\r\n        \r\n        // 设置图表配置\r\n        chart.setOption({\r\n          title: {\r\n            text: '事件类型分布',\r\n            left: 'center',\r\n            textStyle: {\r\n              fontSize: 14\r\n            }\r\n          },\r\n          tooltip: {\r\n            trigger: 'item',\r\n            formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n          },\r\n          legend: {\r\n            orient: 'vertical',\r\n            left: 10,\r\n            top: 30,\r\n            itemWidth: 10,\r\n            itemHeight: 10,\r\n            textStyle: {\r\n              fontSize: 12\r\n            }\r\n          },\r\n          series: [\r\n            {\r\n              name: '事件类型',\r\n              type: 'pie',\r\n              radius: ['40%', '70%'],\r\n              avoidLabelOverlap: false,\r\n              itemStyle: {\r\n                borderRadius: 4\r\n              },\r\n              label: {\r\n                show: false\r\n              },\r\n              emphasis: {\r\n                label: {\r\n                  show: true,\r\n                  fontSize: 12\r\n                }\r\n              },\r\n              labelLine: {\r\n                show: false\r\n              },\r\n              data: [\r\n                { value: 2, name: '超速' },\r\n                { value: 1, name: '急刹车' },\r\n                { value: 1, name: '变道' },\r\n                { value: 1, name: '启动' },\r\n                { value: 1, name: '停止' }\r\n              ]\r\n            }\r\n          ]\r\n        });\r\n        \r\n        // 监听窗口大小变化，调整图表大小\r\n        handleResize = () => {\r\n          chart?.resize();\r\n        };\r\n        window.addEventListener('resize', handleResize);\r\n\r\n      } catch (error) {\r\n        console.error('初始化图表失败:', error);\r\n      }\r\n      \r\n      setLoading(false);\r\n    }, 1500);\r\n    \r\n    // 组件卸载时清理\r\n    return () => {\r\n      if (handleResize) {\r\n        window.removeEventListener('resize', handleResize);\r\n      }\r\n      if (chart) {\r\n        chart.dispose();\r\n      }\r\n    };\r\n  }, []);\r\n  \r\n  // 处理车辆选择\r\n  const handleVehicleSelect = (vehicle) => {\r\n    setSelectedVehicle(vehicle);\r\n  };\r\n  \r\n  // 车辆列表列定义\r\n  const vehicleColumns = [\r\n    {\r\n      title: '车牌号',\r\n      dataIndex: 'plate',\r\n      key: 'plate',\r\n      width: '40%',\r\n    },\r\n    {\r\n      title: '状态',\r\n      dataIndex: 'status',\r\n      key: 'status',\r\n      width: '30%',\r\n      render: status => (\r\n        <Badge \r\n          status={status === 'online' ? 'success' : 'error'} \r\n          text={status === 'online' ? '在线' : '离线'} \r\n        />\r\n      ),\r\n    },\r\n    {\r\n      title: '速度',\r\n      dataIndex: 'speed',\r\n      key: 'speed',\r\n      width: '30%',\r\n      render: speed => `${speed} km/h`,\r\n    }\r\n  ];\r\n  \r\n  return (\r\n    <Spin spinning={loading} tip=\"加载中...\">\r\n      <Container>\r\n        <Sidebar>\r\n          <Title>设备和车辆统计</Title>\r\n          <DeviceStatistics />\r\n        </Sidebar>\r\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\r\n          {/* 主要内容区域 */}\r\n        </MainContent>\r\n      </Container>\r\n    </Spin>\r\n  );\r\n};\r\n\r\nexport default RealTimeTraffic;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AACxF,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,QAAQ,oBAAoB;AACrG,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,yCAAyC;AACxE,OAAOC,gBAAgB,MAAM,gCAAgC;;AAE7D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAX,OAAO,CAACY,GAAG,CAAC,CAACX,QAAQ,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,cAAc,CAAC,CAAC;AAEzG,MAAMO,YAAY,GAAGN,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMC,aAAa,GAAGR,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAME,WAAW,GAAGT,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMG,YAAY,GAAGV,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMI,WAAW,GAAGX,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA,aAAaK,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACxEF,KAAK,CAACC,aAAa,GAAG,cAAc,GACpCD,KAAK,CAACE,cAAc,GAAG,cAAc,GAAG,OAAO;AACnD;AACA,YAAYF,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACvEF,KAAK,CAACC,aAAa,GAAG,WAAW,GACjCD,KAAK,CAACE,cAAc,GAAG,WAAW,GAAG,GAAG;AAC5C;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GAfMJ,WAAW;AAgBjB,MAAMK,QAAQ,GAAGhB,MAAM,CAACd,IAAI,CAAC;AAC7B;AACA,YAAY0B,KAAK,IAAIA,KAAK,CAACK,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMC,gBAAgB,GAAGlB,MAAM,CAACb,SAAS,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMgC,SAAS,GAAGnB,MAAM,CAACO,GAAG;AAC5B;AACA;AACA,CAAC;AAACa,GAAA,GAHID,SAAS;AAKf,MAAME,OAAO,GAAGrB,MAAM,CAACO,GAAG;AAC1B;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GALID,OAAO;AAOb,MAAME,KAAK,GAAGvB,MAAM,CAACwB,EAAE;AACvB;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,KAAK;AAMX,MAAMG,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiD,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmD,MAAM,EAAEC,SAAS,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACqD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAC;IACjCyD,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE;EAClB,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAG7D,MAAM,CAAC,IAAI,CAAC;;EAElC;EACA,MAAM,CAAC8B,aAAa,EAAEgC,gBAAgB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiC,cAAc,EAAEgC,iBAAiB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACAC,SAAS,CAAC,MAAM;IACd,IAAIiE,KAAK,GAAG,IAAI;IAChB,IAAIC,YAAY,GAAG,IAAI;;IAEvB;IACAC,UAAU,CAAC,MAAM;MACf;MACA,IAAI,CAACL,aAAa,CAACM,OAAO,EAAE;QAC1BC,OAAO,CAACC,KAAK,CAAC,SAAS,CAAC;QACxB;MACF;;MAEA;MACA,IAAIC,aAAa,GAAG5D,OAAO,CAAC6D,gBAAgB,CAACV,aAAa,CAACM,OAAO,CAAC;MACnE,IAAIG,aAAa,EAAE;QACjBA,aAAa,CAACE,OAAO,CAAC,CAAC;MACzB;MAEA,IAAI;QACF;QACAR,KAAK,GAAGtD,OAAO,CAAC+D,IAAI,CAACZ,aAAa,CAACM,OAAO,CAAC;;QAE3C;QACAH,KAAK,CAACU,SAAS,CAAC;UACdC,KAAK,EAAE;YACLC,IAAI,EAAE,QAAQ;YACdC,IAAI,EAAE,QAAQ;YACdC,SAAS,EAAE;cACTC,QAAQ,EAAE;YACZ;UACF,CAAC;UACDC,OAAO,EAAE;YACPC,OAAO,EAAE,MAAM;YACfC,SAAS,EAAE;UACb,CAAC;UACDC,MAAM,EAAE;YACNC,MAAM,EAAE,UAAU;YAClBP,IAAI,EAAE,EAAE;YACRQ,GAAG,EAAE,EAAE;YACPC,SAAS,EAAE,EAAE;YACbC,UAAU,EAAE,EAAE;YACdT,SAAS,EAAE;cACTC,QAAQ,EAAE;YACZ;UACF,CAAC;UACDS,MAAM,EAAE,CACN;YACEC,IAAI,EAAE,MAAM;YACZC,IAAI,EAAE,KAAK;YACXC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;YACtBC,iBAAiB,EAAE,KAAK;YACxBC,SAAS,EAAE;cACTC,YAAY,EAAE;YAChB,CAAC;YACDC,KAAK,EAAE;cACLC,IAAI,EAAE;YACR,CAAC;YACDC,QAAQ,EAAE;cACRF,KAAK,EAAE;gBACLC,IAAI,EAAE,IAAI;gBACVjB,QAAQ,EAAE;cACZ;YACF,CAAC;YACDmB,SAAS,EAAE;cACTF,IAAI,EAAE;YACR,CAAC;YACDG,IAAI,EAAE,CACJ;cAAEC,KAAK,EAAE,CAAC;cAAEX,IAAI,EAAE;YAAK,CAAC,EACxB;cAAEW,KAAK,EAAE,CAAC;cAAEX,IAAI,EAAE;YAAM,CAAC,EACzB;cAAEW,KAAK,EAAE,CAAC;cAAEX,IAAI,EAAE;YAAK,CAAC,EACxB;cAAEW,KAAK,EAAE,CAAC;cAAEX,IAAI,EAAE;YAAK,CAAC,EACxB;cAAEW,KAAK,EAAE,CAAC;cAAEX,IAAI,EAAE;YAAK,CAAC;UAE5B,CAAC;QAEL,CAAC,CAAC;;QAEF;QACAxB,YAAY,GAAGA,CAAA,KAAM;UAAA,IAAAoC,MAAA;UACnB,CAAAA,MAAA,GAAArC,KAAK,cAAAqC,MAAA,uBAALA,MAAA,CAAOC,MAAM,CAAC,CAAC;QACjB,CAAC;QACDC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEvC,YAAY,CAAC;MAEjD,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;MAEAvB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;;IAER;IACA,OAAO,MAAM;MACX,IAAImB,YAAY,EAAE;QAChBsC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAExC,YAAY,CAAC;MACpD;MACA,IAAID,KAAK,EAAE;QACTA,KAAK,CAACQ,OAAO,CAAC,CAAC;MACjB;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkC,mBAAmB,GAAIC,OAAO,IAAK;IACvCvD,kBAAkB,CAACuD,OAAO,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMC,cAAc,GAAG,CACrB;IACEjC,KAAK,EAAE,KAAK;IACZkC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC,EACD;IACEpC,KAAK,EAAE,IAAI;IACXkC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEC,MAAM,iBACZ5F,OAAA,CAACZ,KAAK;MACJwG,MAAM,EAAEA,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;MAClDrC,IAAI,EAAEqC,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC;EAEL,CAAC,EACD;IACE1C,KAAK,EAAE,IAAI;IACXkC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEM,KAAK,IAAI,GAAGA,KAAK;EAC3B,CAAC,CACF;EAED,oBACEjG,OAAA,CAACb,IAAI;IAAC+G,QAAQ,EAAE1E,OAAQ;IAAC2E,GAAG,EAAC,uBAAQ;IAAAC,QAAA,eACnCpG,OAAA,CAACe,SAAS;MAAAqF,QAAA,gBACRpG,OAAA,CAACiB,OAAO;QAAAmF,QAAA,gBACNpG,OAAA,CAACmB,KAAK;UAAAiF,QAAA,EAAC;QAAO;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtBhG,OAAA,CAACF,gBAAgB;UAAA+F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACVhG,OAAA,CAACO,WAAW;QAACE,aAAa,EAAEA,aAAc;QAACC,cAAc,EAAEA;MAAe;QAAAmF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEX,CAAC;AAACzE,EAAA,CAzKID,eAAe;AAAA+E,GAAA,GAAf/E,eAAe;AA2KrB,eAAeA,eAAe;AAAC,IAAAX,EAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAgF,GAAA;AAAAC,YAAA,CAAA3F,EAAA;AAAA2F,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAApF,GAAA;AAAAoF,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}