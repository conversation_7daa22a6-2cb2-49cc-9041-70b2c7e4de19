{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap, each } from 'zrender/lib/core/util.js';\nimport { addSafe } from '../util/number.js';\n// (1) [Caution]: the logic is correct based on the premises:\n//     data processing stage is blocked in stream.\n//     See <module:echarts/stream/Scheduler#performDataProcessorTasks>\n// (2) Only register once when import repeatedly.\n//     Should be executed after series is filtered and before stack calculation.\nexport default function dataStack(ecModel) {\n  var stackInfoMap = createHashMap();\n  ecModel.eachSeries(function (seriesModel) {\n    var stack = seriesModel.get('stack');\n    // Compatible: when `stack` is set as '', do not stack.\n    if (stack) {\n      var stackInfoList = stackInfoMap.get(stack) || stackInfoMap.set(stack, []);\n      var data = seriesModel.getData();\n      var stackInfo = {\n        // Used for calculate axis extent automatically.\n        // TODO: Type getCalculationInfo return more specific type?\n        stackResultDimension: data.getCalculationInfo('stackResultDimension'),\n        stackedOverDimension: data.getCalculationInfo('stackedOverDimension'),\n        stackedDimension: data.getCalculationInfo('stackedDimension'),\n        stackedByDimension: data.getCalculationInfo('stackedByDimension'),\n        isStackedByIndex: data.getCalculationInfo('isStackedByIndex'),\n        data: data,\n        seriesModel: seriesModel\n      };\n      // If stacked on axis that do not support data stack.\n      if (!stackInfo.stackedDimension || !(stackInfo.isStackedByIndex || stackInfo.stackedByDimension)) {\n        return;\n      }\n      stackInfoList.length && data.setCalculationInfo('stackedOnSeries', stackInfoList[stackInfoList.length - 1].seriesModel);\n      stackInfoList.push(stackInfo);\n    }\n  });\n  stackInfoMap.each(calculateStack);\n}\nfunction calculateStack(stackInfoList) {\n  each(stackInfoList, function (targetStackInfo, idxInStack) {\n    var resultVal = [];\n    var resultNaN = [NaN, NaN];\n    var dims = [targetStackInfo.stackResultDimension, targetStackInfo.stackedOverDimension];\n    var targetData = targetStackInfo.data;\n    var isStackedByIndex = targetStackInfo.isStackedByIndex;\n    var stackStrategy = targetStackInfo.seriesModel.get('stackStrategy') || 'samesign';\n    // Should not write on raw data, because stack series model list changes\n    // depending on legend selection.\n    targetData.modify(dims, function (v0, v1, dataIndex) {\n      var sum = targetData.get(targetStackInfo.stackedDimension, dataIndex);\n      // Consider `connectNulls` of line area, if value is NaN, stackedOver\n      // should also be NaN, to draw a appropriate belt area.\n      if (isNaN(sum)) {\n        return resultNaN;\n      }\n      var byValue;\n      var stackedDataRawIndex;\n      if (isStackedByIndex) {\n        stackedDataRawIndex = targetData.getRawIndex(dataIndex);\n      } else {\n        byValue = targetData.get(targetStackInfo.stackedByDimension, dataIndex);\n      }\n      // If stackOver is NaN, chart view will render point on value start.\n      var stackedOver = NaN;\n      for (var j = idxInStack - 1; j >= 0; j--) {\n        var stackInfo = stackInfoList[j];\n        // Has been optimized by inverted indices on `stackedByDimension`.\n        if (!isStackedByIndex) {\n          stackedDataRawIndex = stackInfo.data.rawIndexOf(stackInfo.stackedByDimension, byValue);\n        }\n        if (stackedDataRawIndex >= 0) {\n          var val = stackInfo.data.getByRawIndex(stackInfo.stackResultDimension, stackedDataRawIndex);\n          // Considering positive stack, negative stack and empty data\n          if (stackStrategy === 'all' // single stack group\n          || stackStrategy === 'positive' && val > 0 || stackStrategy === 'negative' && val < 0 || stackStrategy === 'samesign' && sum >= 0 && val > 0 // All positive stack\n          || stackStrategy === 'samesign' && sum <= 0 && val < 0 // All negative stack\n          ) {\n            // The sum has to be very small to be affected by the\n            // floating arithmetic problem. An incorrect result will probably\n            // cause axis min/max to be filtered incorrectly.\n            sum = addSafe(sum, val);\n            stackedOver = val;\n            break;\n          }\n        }\n      }\n      resultVal[0] = sum;\n      resultVal[1] = stackedOver;\n      return resultVal;\n    });\n  });\n}", "map": {"version": 3, "names": ["createHashMap", "each", "addSafe", "dataStack", "ecModel", "stackInfoMap", "eachSeries", "seriesModel", "stack", "get", "stackInfoList", "set", "data", "getData", "stackInfo", "stackResultDimension", "getCalculationInfo", "stackedOverDimension", "stackedDimension", "stackedByDimension", "isStackedByIndex", "length", "setCalculationInfo", "push", "calculateStack", "targetStackInfo", "idxInStack", "resultVal", "resultNaN", "NaN", "dims", "targetData", "stackStrategy", "modify", "v0", "v1", "dataIndex", "sum", "isNaN", "byV<PERSON>ue", "stackedDataRawIndex", "getRawIndex", "stackedOver", "j", "rawIndexOf", "val", "getByRawIndex"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/echarts/lib/processor/dataStack.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap, each } from 'zrender/lib/core/util.js';\nimport { addSafe } from '../util/number.js';\n// (1) [Caution]: the logic is correct based on the premises:\n//     data processing stage is blocked in stream.\n//     See <module:echarts/stream/Scheduler#performDataProcessorTasks>\n// (2) Only register once when import repeatedly.\n//     Should be executed after series is filtered and before stack calculation.\nexport default function dataStack(ecModel) {\n  var stackInfoMap = createHashMap();\n  ecModel.eachSeries(function (seriesModel) {\n    var stack = seriesModel.get('stack');\n    // Compatible: when `stack` is set as '', do not stack.\n    if (stack) {\n      var stackInfoList = stackInfoMap.get(stack) || stackInfoMap.set(stack, []);\n      var data = seriesModel.getData();\n      var stackInfo = {\n        // Used for calculate axis extent automatically.\n        // TODO: Type getCalculationInfo return more specific type?\n        stackResultDimension: data.getCalculationInfo('stackResultDimension'),\n        stackedOverDimension: data.getCalculationInfo('stackedOverDimension'),\n        stackedDimension: data.getCalculationInfo('stackedDimension'),\n        stackedByDimension: data.getCalculationInfo('stackedByDimension'),\n        isStackedByIndex: data.getCalculationInfo('isStackedByIndex'),\n        data: data,\n        seriesModel: seriesModel\n      };\n      // If stacked on axis that do not support data stack.\n      if (!stackInfo.stackedDimension || !(stackInfo.isStackedByIndex || stackInfo.stackedByDimension)) {\n        return;\n      }\n      stackInfoList.length && data.setCalculationInfo('stackedOnSeries', stackInfoList[stackInfoList.length - 1].seriesModel);\n      stackInfoList.push(stackInfo);\n    }\n  });\n  stackInfoMap.each(calculateStack);\n}\nfunction calculateStack(stackInfoList) {\n  each(stackInfoList, function (targetStackInfo, idxInStack) {\n    var resultVal = [];\n    var resultNaN = [NaN, NaN];\n    var dims = [targetStackInfo.stackResultDimension, targetStackInfo.stackedOverDimension];\n    var targetData = targetStackInfo.data;\n    var isStackedByIndex = targetStackInfo.isStackedByIndex;\n    var stackStrategy = targetStackInfo.seriesModel.get('stackStrategy') || 'samesign';\n    // Should not write on raw data, because stack series model list changes\n    // depending on legend selection.\n    targetData.modify(dims, function (v0, v1, dataIndex) {\n      var sum = targetData.get(targetStackInfo.stackedDimension, dataIndex);\n      // Consider `connectNulls` of line area, if value is NaN, stackedOver\n      // should also be NaN, to draw a appropriate belt area.\n      if (isNaN(sum)) {\n        return resultNaN;\n      }\n      var byValue;\n      var stackedDataRawIndex;\n      if (isStackedByIndex) {\n        stackedDataRawIndex = targetData.getRawIndex(dataIndex);\n      } else {\n        byValue = targetData.get(targetStackInfo.stackedByDimension, dataIndex);\n      }\n      // If stackOver is NaN, chart view will render point on value start.\n      var stackedOver = NaN;\n      for (var j = idxInStack - 1; j >= 0; j--) {\n        var stackInfo = stackInfoList[j];\n        // Has been optimized by inverted indices on `stackedByDimension`.\n        if (!isStackedByIndex) {\n          stackedDataRawIndex = stackInfo.data.rawIndexOf(stackInfo.stackedByDimension, byValue);\n        }\n        if (stackedDataRawIndex >= 0) {\n          var val = stackInfo.data.getByRawIndex(stackInfo.stackResultDimension, stackedDataRawIndex);\n          // Considering positive stack, negative stack and empty data\n          if (stackStrategy === 'all' // single stack group\n          || stackStrategy === 'positive' && val > 0 || stackStrategy === 'negative' && val < 0 || stackStrategy === 'samesign' && sum >= 0 && val > 0 // All positive stack\n          || stackStrategy === 'samesign' && sum <= 0 && val < 0 // All negative stack\n          ) {\n            // The sum has to be very small to be affected by the\n            // floating arithmetic problem. An incorrect result will probably\n            // cause axis min/max to be filtered incorrectly.\n            sum = addSafe(sum, val);\n            stackedOver = val;\n            break;\n          }\n        }\n      }\n      resultVal[0] = sum;\n      resultVal[1] = stackedOver;\n      return resultVal;\n    });\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,EAAEC,IAAI,QAAQ,0BAA0B;AAC9D,SAASC,OAAO,QAAQ,mBAAmB;AAC3C;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,SAASA,CAACC,OAAO,EAAE;EACzC,IAAIC,YAAY,GAAGL,aAAa,CAAC,CAAC;EAClCI,OAAO,CAACE,UAAU,CAAC,UAAUC,WAAW,EAAE;IACxC,IAAIC,KAAK,GAAGD,WAAW,CAACE,GAAG,CAAC,OAAO,CAAC;IACpC;IACA,IAAID,KAAK,EAAE;MACT,IAAIE,aAAa,GAAGL,YAAY,CAACI,GAAG,CAACD,KAAK,CAAC,IAAIH,YAAY,CAACM,GAAG,CAACH,KAAK,EAAE,EAAE,CAAC;MAC1E,IAAII,IAAI,GAAGL,WAAW,CAACM,OAAO,CAAC,CAAC;MAChC,IAAIC,SAAS,GAAG;QACd;QACA;QACAC,oBAAoB,EAAEH,IAAI,CAACI,kBAAkB,CAAC,sBAAsB,CAAC;QACrEC,oBAAoB,EAAEL,IAAI,CAACI,kBAAkB,CAAC,sBAAsB,CAAC;QACrEE,gBAAgB,EAAEN,IAAI,CAACI,kBAAkB,CAAC,kBAAkB,CAAC;QAC7DG,kBAAkB,EAAEP,IAAI,CAACI,kBAAkB,CAAC,oBAAoB,CAAC;QACjEI,gBAAgB,EAAER,IAAI,CAACI,kBAAkB,CAAC,kBAAkB,CAAC;QAC7DJ,IAAI,EAAEA,IAAI;QACVL,WAAW,EAAEA;MACf,CAAC;MACD;MACA,IAAI,CAACO,SAAS,CAACI,gBAAgB,IAAI,EAAEJ,SAAS,CAACM,gBAAgB,IAAIN,SAAS,CAACK,kBAAkB,CAAC,EAAE;QAChG;MACF;MACAT,aAAa,CAACW,MAAM,IAAIT,IAAI,CAACU,kBAAkB,CAAC,iBAAiB,EAAEZ,aAAa,CAACA,aAAa,CAACW,MAAM,GAAG,CAAC,CAAC,CAACd,WAAW,CAAC;MACvHG,aAAa,CAACa,IAAI,CAACT,SAAS,CAAC;IAC/B;EACF,CAAC,CAAC;EACFT,YAAY,CAACJ,IAAI,CAACuB,cAAc,CAAC;AACnC;AACA,SAASA,cAAcA,CAACd,aAAa,EAAE;EACrCT,IAAI,CAACS,aAAa,EAAE,UAAUe,eAAe,EAAEC,UAAU,EAAE;IACzD,IAAIC,SAAS,GAAG,EAAE;IAClB,IAAIC,SAAS,GAAG,CAACC,GAAG,EAAEA,GAAG,CAAC;IAC1B,IAAIC,IAAI,GAAG,CAACL,eAAe,CAACV,oBAAoB,EAAEU,eAAe,CAACR,oBAAoB,CAAC;IACvF,IAAIc,UAAU,GAAGN,eAAe,CAACb,IAAI;IACrC,IAAIQ,gBAAgB,GAAGK,eAAe,CAACL,gBAAgB;IACvD,IAAIY,aAAa,GAAGP,eAAe,CAAClB,WAAW,CAACE,GAAG,CAAC,eAAe,CAAC,IAAI,UAAU;IAClF;IACA;IACAsB,UAAU,CAACE,MAAM,CAACH,IAAI,EAAE,UAAUI,EAAE,EAAEC,EAAE,EAAEC,SAAS,EAAE;MACnD,IAAIC,GAAG,GAAGN,UAAU,CAACtB,GAAG,CAACgB,eAAe,CAACP,gBAAgB,EAAEkB,SAAS,CAAC;MACrE;MACA;MACA,IAAIE,KAAK,CAACD,GAAG,CAAC,EAAE;QACd,OAAOT,SAAS;MAClB;MACA,IAAIW,OAAO;MACX,IAAIC,mBAAmB;MACvB,IAAIpB,gBAAgB,EAAE;QACpBoB,mBAAmB,GAAGT,UAAU,CAACU,WAAW,CAACL,SAAS,CAAC;MACzD,CAAC,MAAM;QACLG,OAAO,GAAGR,UAAU,CAACtB,GAAG,CAACgB,eAAe,CAACN,kBAAkB,EAAEiB,SAAS,CAAC;MACzE;MACA;MACA,IAAIM,WAAW,GAAGb,GAAG;MACrB,KAAK,IAAIc,CAAC,GAAGjB,UAAU,GAAG,CAAC,EAAEiB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxC,IAAI7B,SAAS,GAAGJ,aAAa,CAACiC,CAAC,CAAC;QAChC;QACA,IAAI,CAACvB,gBAAgB,EAAE;UACrBoB,mBAAmB,GAAG1B,SAAS,CAACF,IAAI,CAACgC,UAAU,CAAC9B,SAAS,CAACK,kBAAkB,EAAEoB,OAAO,CAAC;QACxF;QACA,IAAIC,mBAAmB,IAAI,CAAC,EAAE;UAC5B,IAAIK,GAAG,GAAG/B,SAAS,CAACF,IAAI,CAACkC,aAAa,CAAChC,SAAS,CAACC,oBAAoB,EAAEyB,mBAAmB,CAAC;UAC3F;UACA,IAAIR,aAAa,KAAK,KAAK,CAAC;UAAA,GACzBA,aAAa,KAAK,UAAU,IAAIa,GAAG,GAAG,CAAC,IAAIb,aAAa,KAAK,UAAU,IAAIa,GAAG,GAAG,CAAC,IAAIb,aAAa,KAAK,UAAU,IAAIK,GAAG,IAAI,CAAC,IAAIQ,GAAG,GAAG,CAAC,CAAC;UAAA,GAC1Ib,aAAa,KAAK,UAAU,IAAIK,GAAG,IAAI,CAAC,IAAIQ,GAAG,GAAG,CAAC,CAAC;UAAA,EACrD;YACA;YACA;YACA;YACAR,GAAG,GAAGnC,OAAO,CAACmC,GAAG,EAAEQ,GAAG,CAAC;YACvBH,WAAW,GAAGG,GAAG;YACjB;UACF;QACF;MACF;MACAlB,SAAS,CAAC,CAAC,CAAC,GAAGU,GAAG;MAClBV,SAAS,CAAC,CAAC,CAAC,GAAGe,WAAW;MAC1B,OAAOf,SAAS;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}