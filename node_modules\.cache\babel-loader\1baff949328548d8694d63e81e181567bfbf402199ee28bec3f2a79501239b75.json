{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\Login.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Form, Input, Button, Card, Typography, Checkbox, message, Spin, Alert } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport axios from 'axios';\nimport { login } from '../services/auth';\nimport './Login.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\n\n// 设置 axios 默认配置\n// axios.defaults.baseURL = 'http://localhost:5000';\n// axios.defaults.baseURL = process.env.REACT_APP_API_URL;\n\n// 登录页面容器\nconst LoginContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n  position: relative;\n  overflow: hidden;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\n    opacity: 0.15;\n    z-index: 0;\n  }\n`;\n\n// 登录卡片样式\n_c = LoginContainer;\nconst LoginCard = styled(Card)`\n  width: 400px;\n  border-radius: 8px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\n  z-index: 1;\n  \n  .ant-card-body {\n    padding: 40px;\n  }\n`;\n\n// 标题容器\n_c2 = LoginCard;\nconst TitleContainer = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n\n// Logo样式\n_c3 = TitleContainer;\nconst Logo = styled.div`\n  width: 80px;\n  height: 80px;\n  margin: 0 auto 20px;\n  background: #1890ff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 40px;\n  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);\n`;\n\n// 表单样式\n_c4 = Logo;\nconst StyledForm = styled(Form)`\n  .ant-form-item-label {\n    font-weight: 500;\n  }\n  \n  .ant-input-affix-wrapper {\n    padding: 12px;\n    border-radius: 6px;\n  }\n  \n  .ant-btn {\n    height: 45px;\n    border-radius: 6px;\n    font-weight: 500;\n  }\n`;\n\n// 底部文本样式\n_c5 = StyledForm;\nconst FooterText = styled(Text)`\n  display: block;\n  text-align: center;\n  margin-top: 20px;\n  color: #8c8c8c;\n`;\n_c6 = FooterText;\nconst Login = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [loginError, setLoginError] = useState('');\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n\n  // 全局状态初始化（如 Context/Redux）\n  const [authState, setAuthState] = useState({\n    isLoggedIn: localStorage.getItem('isLoggedIn') === 'true',\n    user: JSON.parse(localStorage.getItem('user') || '{}')\n  });\n  const onFinish = async values => {\n    setLoading(true);\n    setLoginError('');\n\n    // 动态获取API基础URL\n    const currentDomain = window.location.hostname;\n    const currentPort = window.location.port === '3000' ? '5000' : window.location.port; // 开发环境使用5000端口，生产环境使用当前端口\n\n    // 尝试从多个来源获取API URL\n    const apiUrl = process.env.REACT_APP_API_URL ||\n    // 首先检查环境变量\n    `http://${currentDomain}:${currentPort}` ||\n    // 然后尝试使用当前域名和推断的端口\n    'http://localhost:5000'; // 最后回退到本地开发地址\n\n    console.log('使用API地址:', apiUrl);\n    console.log('API_URL:', process.env.REACT_APP_API_URL);\n    try {\n      // 尝试多种可能的登录请求方式\n      let loginSuccess = false;\n      let errorMsg = '';\n\n      // 方法1: 尝试标准登录接口 POST /api/login\n      try {\n        console.log('尝试登录方法1: POST到/api/login');\n        const response = await axios.post(`${apiUrl}/api/login`, {\n          username: values.username,\n          password: values.password\n        });\n        if (response.data && response.data.success) {\n          handleLoginSuccess(response.data);\n          loginSuccess = true;\n        }\n      } catch (error) {\n        var _error$response, _error$response$data;\n        console.log('登录方法1失败:', error.message);\n        errorMsg = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || '登录失败，请尝试其他方式';\n      }\n\n      // // 方法2: 如果方法1失败，尝试直接通过GET方式访问登录验证接口\n      // if (!loginSuccess) {\n      //   try {\n      //     console.log('尝试登录方法2: GET请求验证用户');\n      //     const response = await axios.get(`${apiUrl}/api/auth/validate?username=${encodeURIComponent(values.username)}&password=${encodeURIComponent(values.password)}`);\n\n      //     if (response.data && response.data.success) {\n      //       handleLoginSuccess(response.data);\n      //       loginSuccess = true;\n      //     }\n      //   } catch (error) {\n      //     console.log('登录方法2失败:', error.message);\n      //     errorMsg = errorMsg || error.response?.data?.message || '登录失败，请尝试其他方式';\n      //   }\n      // }\n\n      // // 方法3: 尝试使用相对路径\n      // if (!loginSuccess) {\n      //   try {\n      //     console.log('尝试登录方法3: 使用相对路径');\n      //     const response = await axios.post('/api/login', {\n      //       username: values.username,\n      //       password: values.password\n      //     });\n\n      //     if (response.data && response.data.success) {\n      //       handleLoginSuccess(response.data);\n      //       loginSuccess = true;\n      //     }\n      //   } catch (error) {\n      //     console.log('登录方法3失败:', error.message);\n      //     errorMsg = errorMsg || error.response?.data?.message || '登录失败，请尝试其他方式';\n      //   }\n      // }\n\n      // // 方法4: 尝试使用mock登录（最后手段）\n      // if (!loginSuccess) {\n      //   try {\n      //     console.log('尝试登录方法4: 模拟登录');\n      //     directTestLogin(true);\n      //     loginSuccess = true;\n      //   } catch (error) {\n      //     console.log('登录方法4失败:', error.message);\n      //   }\n      // }\n\n      if (!loginSuccess) {\n        setLoginError(errorMsg || '所有登录方式都失败，请联系管理员');\n        message.error(errorMsg || '登录失败，请联系管理员');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('登录过程中发生错误:', err);\n      const errorMsg = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || '登录失败，请重试！';\n      setLoginError(errorMsg);\n      message.error(errorMsg);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 添加登录成功的统一处理函数\n  const handleLoginSuccess = data => {\n    // // 保存token和用户信息\n    // localStorage.setItem('token', data.data?.token || 'mock-token');\n    // localStorage.setItem('user', JSON.stringify(data.data?.user || { \n    //   id: 1, \n    //   username: 'admin', \n    //   name: '管理员',\n    //   role: 'admin'\n    // }));\n    // localStorage.setItem('isLoggedIn', 'true');\n\n    localStorage.setItem('token', data.data.token);\n    localStorage.setItem('user', JSON.stringify(data.data.user));\n    localStorage.setItem('isLoggedIn', 'true');\n    console.log(\"继续执行其他代码\", JSON.stringify(data.data.user));\n    message.success('登录成功！');\n    // 同步全局状态\n    setAuthState({\n      isLoggedIn: true,\n      user: data.data.user\n    });\n    // setTimeout(() => {\n    //   console.log(\"两秒后执行的操作\");\n    //   navigate('/real-time-traffic');\n    // }, 0);\n\n    // 跳转到主页面\n    navigate('/real-time-traffic');\n    console.log(\"继续执行其他代码\");\n  };\n\n  // 修改测试登录函数，添加参数控制是否显示消息\n  const directTestLogin = async (silent = false) => {\n    setLoading(true);\n    if (!silent) {\n      setLoginError('');\n    }\n    try {\n      // 模拟成功响应格式\n      const mockResponse = {\n        success: true,\n        data: {\n          token: 'test-token-123',\n          user: {\n            id: 1,\n            username: 'test',\n            name: '测试用户',\n            role: 'admin'\n          }\n        }\n      };\n\n      // 保存token和用户信息\n      localStorage.setItem('token', mockResponse.data.token);\n      localStorage.setItem('user', JSON.stringify(mockResponse.data.user));\n      localStorage.setItem('isLoggedIn', 'true');\n      if (!silent) {\n        message.success('测试登录成功！');\n      }\n      // 跳转到主页面\n      navigate('/real-time-traffic');\n    } catch (err) {\n      console.error('测试登录错误:', err);\n      if (!silent) {\n        var _err$response2, _err$response2$data;\n        const errorMsg = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.message) || '测试登录失败，请重试！';\n        setLoginError(errorMsg);\n        message.error(errorMsg);\n      }\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleTestLogin = () => {\n    form.setFieldsValue({\n      username: 'admin',\n      password: 'admin123'\n    });\n    form.submit();\n  };\n  return /*#__PURE__*/_jsxDEV(LoginContainer, {\n    children: /*#__PURE__*/_jsxDEV(Spin, {\n      spinning: loading,\n      tip: \"\\u767B\\u5F55\\u4E2D...\",\n      children: /*#__PURE__*/_jsxDEV(LoginCard, {\n        children: [/*#__PURE__*/_jsxDEV(TitleContainer, {\n          children: [/*#__PURE__*/_jsxDEV(Logo, {\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-car-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Title, {\n            level: 2,\n            style: {\n              margin: 0,\n              color: '#1890ff'\n            },\n            children: \"\\u667A\\u80FD\\u7F51\\u8054\\u4EA7\\u5B66\\u7814\\u4E91\\u5E73\\u53F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u767B\\u5F55\\u60A8\\u7684\\u8D26\\u6237\\u4EE5\\u7EE7\\u7EED\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), loginError && /*#__PURE__*/_jsxDEV(Alert, {\n          message: loginError,\n          type: \"error\",\n          showIcon: true,\n          style: {\n            marginBottom: 24\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StyledForm, {\n          form: form,\n          name: \"login\",\n          initialValues: {\n            remember: true\n          },\n          onFinish: onFinish,\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"username\",\n            rules: [{\n              required: true,\n              message: '请输入用户名!'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {\n                style: {\n                  color: '#bfbfbf'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 25\n              }, this),\n              placeholder: \"\\u7528\\u6237\\u540D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"password\",\n            rules: [{\n              required: true,\n              message: '请输入密码!'\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input.Password, {\n              prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {\n                style: {\n                  color: '#bfbfbf'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 25\n              }, this),\n              placeholder: \"\\u5BC6\\u7801\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"remember\",\n              valuePropName: \"checked\",\n              noStyle: true,\n              children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                children: \"\\u8BB0\\u4F4F\\u6211\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              style: {\n                float: 'right'\n              },\n              href: \"#\",\n              children: \"\\u5FD8\\u8BB0\\u5BC6\\u7801?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              block: true,\n              loading: loading,\n              children: \"\\u767B\\u5F55\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              onClick: handleTestLogin,\n              block: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FooterText, {\n          children: [\"\\xA9 \", new Date().getFullYear(), \" \\u5E0C\\u8FEA\\u667A\\u9A7E \\u7248\\u6743\\u6240\\u6709\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 296,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"KFb55SKEPP3CSlFD9DiuUhqdXBY=\", false, function () {\n  return [useNavigate, Form.useForm];\n});\n_c7 = Login;\nexport default Login;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"LoginContainer\");\n$RefreshReg$(_c2, \"LoginCard\");\n$RefreshReg$(_c3, \"TitleContainer\");\n$RefreshReg$(_c4, \"Logo\");\n$RefreshReg$(_c5, \"StyledForm\");\n$RefreshReg$(_c6, \"FooterText\");\n$RefreshReg$(_c7, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Form", "Input", "<PERSON><PERSON>", "Card", "Typography", "Checkbox", "message", "Spin", "<PERSON><PERSON>", "UserOutlined", "LockOutlined", "useNavigate", "styled", "axios", "login", "jsxDEV", "_jsxDEV", "Title", "Text", "LoginContainer", "div", "_c", "LoginCard", "_c2", "TitleC<PERSON>r", "_c3", "Logo", "_c4", "StyledForm", "_c5", "FooterText", "_c6", "<PERSON><PERSON>", "_s", "loading", "setLoading", "loginError", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "navigate", "form", "useForm", "authState", "setAuthState", "isLoggedIn", "localStorage", "getItem", "user", "JSON", "parse", "onFinish", "values", "currentDomain", "window", "location", "hostname", "currentPort", "port", "apiUrl", "process", "env", "REACT_APP_API_URL", "console", "log", "loginSuccess", "errorMsg", "response", "post", "username", "password", "data", "success", "handleLoginSuccess", "error", "_error$response", "_error$response$data", "err", "_err$response", "_err$response$data", "setItem", "token", "stringify", "directTestLogin", "silent", "mockResponse", "id", "name", "role", "_err$response2", "_err$response2$data", "handleTestLogin", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "submit", "children", "spinning", "tip", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "level", "style", "margin", "color", "type", "showIcon", "marginBottom", "initialValues", "remember", "size", "<PERSON><PERSON>", "rules", "required", "prefix", "placeholder", "Password", "valuePropName", "noStyle", "float", "href", "htmlType", "block", "onClick", "Date", "getFullYear", "_c7", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/Login.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Form, Input, But<PERSON>, Card, Typography, Checkbox, message, Spin, Alert } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport axios from 'axios';\nimport { login } from '../services/auth';\nimport './Login.css';\n\nconst { Title, Text } = Typography;\n\n// 设置 axios 默认配置\n// axios.defaults.baseURL = 'http://localhost:5000';\n// axios.defaults.baseURL = process.env.REACT_APP_API_URL;\n\n// 登录页面容器\nconst LoginContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100vh;\n  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n  position: relative;\n  overflow: hidden;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\n    opacity: 0.15;\n    z-index: 0;\n  }\n`;\n\n// 登录卡片样式\nconst LoginCard = styled(Card)`\n  width: 400px;\n  border-radius: 8px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);\n  z-index: 1;\n  \n  .ant-card-body {\n    padding: 40px;\n  }\n`;\n\n// 标题容器\nconst TitleContainer = styled.div`\n  text-align: center;\n  margin-bottom: 30px;\n`;\n\n// Logo样式\nconst Logo = styled.div`\n  width: 80px;\n  height: 80px;\n  margin: 0 auto 20px;\n  background: #1890ff;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 40px;\n  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.3);\n`;\n\n// 表单样式\nconst StyledForm = styled(Form)`\n  .ant-form-item-label {\n    font-weight: 500;\n  }\n  \n  .ant-input-affix-wrapper {\n    padding: 12px;\n    border-radius: 6px;\n  }\n  \n  .ant-btn {\n    height: 45px;\n    border-radius: 6px;\n    font-weight: 500;\n  }\n`;\n\n// 底部文本样式\nconst FooterText = styled(Text)`\n  display: block;\n  text-align: center;\n  margin-top: 20px;\n  color: #8c8c8c;\n`;\n\n\n\n\nconst Login = () => {\n  const [loading, setLoading] = useState(false);\n  const [loginError, setLoginError] = useState('');\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n\n  // 全局状态初始化（如 Context/Redux）\n  const [authState, setAuthState] = useState({\n    isLoggedIn: localStorage.getItem('isLoggedIn') === 'true',\n    user: JSON.parse(localStorage.getItem('user') || '{}')\n  });\n\n  const onFinish = async (values) => {\n    setLoading(true);\n    setLoginError('');\n    \n    // 动态获取API基础URL\n    const currentDomain = window.location.hostname;\n    const currentPort = window.location.port === '3000' ? '5000' : window.location.port; // 开发环境使用5000端口，生产环境使用当前端口\n    \n    // 尝试从多个来源获取API URL\n    const apiUrl = process.env.REACT_APP_API_URL || // 首先检查环境变量\n                  `http://${currentDomain}:${currentPort}` || // 然后尝试使用当前域名和推断的端口\n                  'http://localhost:5000'; // 最后回退到本地开发地址\n    \n    console.log('使用API地址:', apiUrl);\n    console.log('API_URL:', process.env.REACT_APP_API_URL);\n    \n    try {\n      // 尝试多种可能的登录请求方式\n      let loginSuccess = false;\n      let errorMsg = '';\n      \n      // 方法1: 尝试标准登录接口 POST /api/login\n      try {\n        console.log('尝试登录方法1: POST到/api/login');\n        const response = await axios.post(`${apiUrl}/api/login`, {\n          username: values.username,\n          password: values.password\n        });\n        \n        if (response.data && response.data.success) {\n          handleLoginSuccess(response.data);\n          loginSuccess = true;\n        }\n      } catch (error) {\n        console.log('登录方法1失败:', error.message);\n        errorMsg = error.response?.data?.message || '登录失败，请尝试其他方式';\n      }\n      \n      // // 方法2: 如果方法1失败，尝试直接通过GET方式访问登录验证接口\n      // if (!loginSuccess) {\n      //   try {\n      //     console.log('尝试登录方法2: GET请求验证用户');\n      //     const response = await axios.get(`${apiUrl}/api/auth/validate?username=${encodeURIComponent(values.username)}&password=${encodeURIComponent(values.password)}`);\n          \n      //     if (response.data && response.data.success) {\n      //       handleLoginSuccess(response.data);\n      //       loginSuccess = true;\n      //     }\n      //   } catch (error) {\n      //     console.log('登录方法2失败:', error.message);\n      //     errorMsg = errorMsg || error.response?.data?.message || '登录失败，请尝试其他方式';\n      //   }\n      // }\n      \n      // // 方法3: 尝试使用相对路径\n      // if (!loginSuccess) {\n      //   try {\n      //     console.log('尝试登录方法3: 使用相对路径');\n      //     const response = await axios.post('/api/login', {\n      //       username: values.username,\n      //       password: values.password\n      //     });\n          \n      //     if (response.data && response.data.success) {\n      //       handleLoginSuccess(response.data);\n      //       loginSuccess = true;\n      //     }\n      //   } catch (error) {\n      //     console.log('登录方法3失败:', error.message);\n      //     errorMsg = errorMsg || error.response?.data?.message || '登录失败，请尝试其他方式';\n      //   }\n      // }\n      \n      // // 方法4: 尝试使用mock登录（最后手段）\n      // if (!loginSuccess) {\n      //   try {\n      //     console.log('尝试登录方法4: 模拟登录');\n      //     directTestLogin(true);\n      //     loginSuccess = true;\n      //   } catch (error) {\n      //     console.log('登录方法4失败:', error.message);\n      //   }\n      // }\n      \n      if (!loginSuccess) {\n        setLoginError(errorMsg || '所有登录方式都失败，请联系管理员');\n        message.error(errorMsg || '登录失败，请联系管理员');\n      }\n    } catch (err) {\n      console.error('登录过程中发生错误:', err);\n      const errorMsg = err.response?.data?.message || '登录失败，请重试！';\n      setLoginError(errorMsg);\n      message.error(errorMsg);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 添加登录成功的统一处理函数\n  const handleLoginSuccess = (data) => {\n    // // 保存token和用户信息\n    // localStorage.setItem('token', data.data?.token || 'mock-token');\n    // localStorage.setItem('user', JSON.stringify(data.data?.user || { \n    //   id: 1, \n    //   username: 'admin', \n    //   name: '管理员',\n    //   role: 'admin'\n    // }));\n    // localStorage.setItem('isLoggedIn', 'true');\n\n    localStorage.setItem('token', data.data.token);\n    localStorage.setItem('user', JSON.stringify(data.data.user));\n    localStorage.setItem('isLoggedIn', 'true');\n\n    console.log(\"继续执行其他代码\", JSON.stringify(data.data.user)); \n    \n    message.success('登录成功！');\n    // 同步全局状态\n    setAuthState({ isLoggedIn: true, user: data.data.user });\n    // setTimeout(() => {\n    //   console.log(\"两秒后执行的操作\");\n    //   navigate('/real-time-traffic');\n    // }, 0);\n\n    // 跳转到主页面\n    navigate('/real-time-traffic');\n    console.log(\"继续执行其他代码\"); \n  };\n\n  // 修改测试登录函数，添加参数控制是否显示消息\n  const directTestLogin = async (silent = false) => {\n    setLoading(true);\n    if (!silent) {\n      setLoginError('');\n    }\n    \n    try {\n      // 模拟成功响应格式\n      const mockResponse = {\n        success: true,\n        data: {\n          token: 'test-token-123',\n          user: { \n            id: 1, \n            username: 'test', \n            name: '测试用户',\n            role: 'admin'\n          }\n        }\n      };\n      \n      // 保存token和用户信息\n      localStorage.setItem('token', mockResponse.data.token);\n      localStorage.setItem('user', JSON.stringify(mockResponse.data.user));\n      localStorage.setItem('isLoggedIn', 'true');\n      \n      if (!silent) {\n        message.success('测试登录成功！');\n      }\n      // 跳转到主页面\n      navigate('/real-time-traffic');\n    } catch (err) {\n      console.error('测试登录错误:', err);\n      if (!silent) {\n        const errorMsg = err.response?.data?.message || '测试登录失败，请重试！';\n        setLoginError(errorMsg);\n        message.error(errorMsg);\n      }\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleTestLogin = () => {\n    form.setFieldsValue({\n      username: 'admin',\n      password: 'admin123'\n    });\n    form.submit();\n  };\n\n  return (\n    <LoginContainer>\n      <Spin spinning={loading} tip=\"登录中...\">\n        <LoginCard>\n          <TitleContainer>\n            <Logo>\n              <i className=\"fas fa-car-alt\" />\n            </Logo>\n            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>智能网联产学研云平台</Title>\n            <Text type=\"secondary\">登录您的账户以继续</Text>\n          </TitleContainer>\n          \n          {loginError && (\n            <Alert\n              message={loginError}\n              type=\"error\"\n              showIcon\n              style={{ marginBottom: 24 }}\n            />\n          )}\n          \n          <StyledForm\n            form={form}\n            name=\"login\"\n            initialValues={{ remember: true }}\n            onFinish={onFinish}\n            size=\"large\"\n          >\n            <Form.Item\n              name=\"username\"\n              rules={[{ required: true, message: '请输入用户名!' }]}\n            >\n              <Input \n                prefix={<UserOutlined style={{ color: '#bfbfbf' }} />} \n                placeholder=\"用户名\" \n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"password\"\n              rules={[{ required: true, message: '请输入密码!' }]}\n            >\n              <Input.Password \n                prefix={<LockOutlined style={{ color: '#bfbfbf' }} />} \n                placeholder=\"密码\" \n              />\n            </Form.Item>\n\n            <Form.Item>\n              <Form.Item name=\"remember\" valuePropName=\"checked\" noStyle>\n                <Checkbox>记住我</Checkbox>\n              </Form.Item>\n\n              <a style={{ float: 'right' }} href=\"#\">\n                忘记密码?\n              </a>\n            </Form.Item>\n\n            <Form.Item>\n              <Button type=\"primary\" htmlType=\"submit\" block loading={loading}>\n                登录\n              </Button>\n            </Form.Item>\n            \n            <Form.Item>\n              <Button type=\"link\" onClick={handleTestLogin} block>\n                {/* 测试登录 (admin/admin123) */}\n              </Button>\n            </Form.Item>\n          </StyledForm>\n          \n          <FooterText>\n            © {new Date().getFullYear()} 希迪智驾 版权所有\n          </FooterText>\n        </LoginCard>\n      </Spin>\n    </LoginContainer>\n  );\n};\n\nexport default Login; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAC5F,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGd,UAAU;;AAElC;AACA;AACA;;AAEA;AACA,MAAMe,cAAc,GAAGP,MAAM,CAACQ,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GAtBMF,cAAc;AAuBpB,MAAMG,SAAS,GAAGV,MAAM,CAACT,IAAI,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAoB,GAAA,GAXMD,SAAS;AAYf,MAAME,cAAc,GAAGZ,MAAM,CAACQ,GAAG;AACjC;AACA;AACA,CAAC;;AAED;AAAAK,GAAA,GALMD,cAAc;AAMpB,MAAME,IAAI,GAAGd,MAAM,CAACQ,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAO,GAAA,GAdMD,IAAI;AAeV,MAAME,UAAU,GAAGhB,MAAM,CAACZ,IAAI,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAA6B,GAAA,GAjBMD,UAAU;AAkBhB,MAAME,UAAU,GAAGlB,MAAM,CAACM,IAAI,CAAC;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GALID,UAAU;AAUhB,MAAME,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAMuC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC4B,IAAI,CAAC,GAAGvC,IAAI,CAACwC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC;IACzC4C,UAAU,EAAEC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,KAAK,MAAM;IACzDC,IAAI,EAAEC,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI;EACvD,CAAC,CAAC;EAEF,MAAMI,QAAQ,GAAG,MAAOC,MAAM,IAAK;IACjCf,UAAU,CAAC,IAAI,CAAC;IAChBE,aAAa,CAAC,EAAE,CAAC;;IAEjB;IACA,MAAMc,aAAa,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;IAC9C,MAAMC,WAAW,GAAGH,MAAM,CAACC,QAAQ,CAACG,IAAI,KAAK,MAAM,GAAG,MAAM,GAAGJ,MAAM,CAACC,QAAQ,CAACG,IAAI,CAAC,CAAC;;IAErF;IACA,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;IAAI;IAClC,UAAUT,aAAa,IAAII,WAAW,EAAE;IAAI;IAC5C,uBAAuB,CAAC,CAAC;;IAEvCM,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEL,MAAM,CAAC;IAC/BI,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;IAEtD,IAAI;MACF;MACA,IAAIG,YAAY,GAAG,KAAK;MACxB,IAAIC,QAAQ,GAAG,EAAE;;MAEjB;MACA,IAAI;QACFH,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;QACvC,MAAMG,QAAQ,GAAG,MAAMpD,KAAK,CAACqD,IAAI,CAAC,GAAGT,MAAM,YAAY,EAAE;UACvDU,QAAQ,EAAEjB,MAAM,CAACiB,QAAQ;UACzBC,QAAQ,EAAElB,MAAM,CAACkB;QACnB,CAAC,CAAC;QAEF,IAAIH,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;UAC1CC,kBAAkB,CAACN,QAAQ,CAACI,IAAI,CAAC;UACjCN,YAAY,GAAG,IAAI;QACrB;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QAAA,IAAAC,eAAA,EAAAC,oBAAA;QACdb,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEU,KAAK,CAAClE,OAAO,CAAC;QACtC0D,QAAQ,GAAG,EAAAS,eAAA,GAAAD,KAAK,CAACP,QAAQ,cAAAQ,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBJ,IAAI,cAAAK,oBAAA,uBAApBA,oBAAA,CAAsBpE,OAAO,KAAI,cAAc;MAC5D;;MAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAI,CAACyD,YAAY,EAAE;QACjB1B,aAAa,CAAC2B,QAAQ,IAAI,kBAAkB,CAAC;QAC7C1D,OAAO,CAACkE,KAAK,CAACR,QAAQ,IAAI,aAAa,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOW,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZhB,OAAO,CAACW,KAAK,CAAC,YAAY,EAAEG,GAAG,CAAC;MAChC,MAAMX,QAAQ,GAAG,EAAAY,aAAA,GAAAD,GAAG,CAACV,QAAQ,cAAAW,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcP,IAAI,cAAAQ,kBAAA,uBAAlBA,kBAAA,CAAoBvE,OAAO,KAAI,WAAW;MAC3D+B,aAAa,CAAC2B,QAAQ,CAAC;MACvB1D,OAAO,CAACkE,KAAK,CAACR,QAAQ,CAAC;IACzB,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoC,kBAAkB,GAAIF,IAAI,IAAK;IACnC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEAzB,YAAY,CAACkC,OAAO,CAAC,OAAO,EAAET,IAAI,CAACA,IAAI,CAACU,KAAK,CAAC;IAC9CnC,YAAY,CAACkC,OAAO,CAAC,MAAM,EAAE/B,IAAI,CAACiC,SAAS,CAACX,IAAI,CAACA,IAAI,CAACvB,IAAI,CAAC,CAAC;IAC5DF,YAAY,CAACkC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC;IAE1CjB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEf,IAAI,CAACiC,SAAS,CAACX,IAAI,CAACA,IAAI,CAACvB,IAAI,CAAC,CAAC;IAEvDxC,OAAO,CAACgE,OAAO,CAAC,OAAO,CAAC;IACxB;IACA5B,YAAY,CAAC;MAAEC,UAAU,EAAE,IAAI;MAAEG,IAAI,EAAEuB,IAAI,CAACA,IAAI,CAACvB;IAAK,CAAC,CAAC;IACxD;IACA;IACA;IACA;;IAEA;IACAR,QAAQ,CAAC,oBAAoB,CAAC;IAC9BuB,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;EACzB,CAAC;;EAED;EACA,MAAMmB,eAAe,GAAG,MAAAA,CAAOC,MAAM,GAAG,KAAK,KAAK;IAChD/C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI,CAAC+C,MAAM,EAAE;MACX7C,aAAa,CAAC,EAAE,CAAC;IACnB;IAEA,IAAI;MACF;MACA,MAAM8C,YAAY,GAAG;QACnBb,OAAO,EAAE,IAAI;QACbD,IAAI,EAAE;UACJU,KAAK,EAAE,gBAAgB;UACvBjC,IAAI,EAAE;YACJsC,EAAE,EAAE,CAAC;YACLjB,QAAQ,EAAE,MAAM;YAChBkB,IAAI,EAAE,MAAM;YACZC,IAAI,EAAE;UACR;QACF;MACF,CAAC;;MAED;MACA1C,YAAY,CAACkC,OAAO,CAAC,OAAO,EAAEK,YAAY,CAACd,IAAI,CAACU,KAAK,CAAC;MACtDnC,YAAY,CAACkC,OAAO,CAAC,MAAM,EAAE/B,IAAI,CAACiC,SAAS,CAACG,YAAY,CAACd,IAAI,CAACvB,IAAI,CAAC,CAAC;MACpEF,YAAY,CAACkC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC;MAE1C,IAAI,CAACI,MAAM,EAAE;QACX5E,OAAO,CAACgE,OAAO,CAAC,SAAS,CAAC;MAC5B;MACA;MACAhC,QAAQ,CAAC,oBAAoB,CAAC;IAChC,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACZd,OAAO,CAACW,KAAK,CAAC,SAAS,EAAEG,GAAG,CAAC;MAC7B,IAAI,CAACO,MAAM,EAAE;QAAA,IAAAK,cAAA,EAAAC,mBAAA;QACX,MAAMxB,QAAQ,GAAG,EAAAuB,cAAA,GAAAZ,GAAG,CAACV,QAAQ,cAAAsB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAclB,IAAI,cAAAmB,mBAAA,uBAAlBA,mBAAA,CAAoBlF,OAAO,KAAI,aAAa;QAC7D+B,aAAa,CAAC2B,QAAQ,CAAC;QACvB1D,OAAO,CAACkE,KAAK,CAACR,QAAQ,CAAC;MACzB;MACA,MAAMW,GAAG;IACX,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsD,eAAe,GAAGA,CAAA,KAAM;IAC5BlD,IAAI,CAACmD,cAAc,CAAC;MAClBvB,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF7B,IAAI,CAACoD,MAAM,CAAC,CAAC;EACf,CAAC;EAED,oBACE3E,OAAA,CAACG,cAAc;IAAAyE,QAAA,eACb5E,OAAA,CAACT,IAAI;MAACsF,QAAQ,EAAE3D,OAAQ;MAAC4D,GAAG,EAAC,uBAAQ;MAAAF,QAAA,eACnC5E,OAAA,CAACM,SAAS;QAAAsE,QAAA,gBACR5E,OAAA,CAACQ,cAAc;UAAAoE,QAAA,gBACb5E,OAAA,CAACU,IAAI;YAAAkE,QAAA,eACH5E,OAAA;cAAG+E,SAAS,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACPnF,OAAA,CAACC,KAAK;YAACmF,KAAK,EAAE,CAAE;YAACC,KAAK,EAAE;cAAEC,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAU,CAAE;YAAAX,QAAA,EAAC;UAAU;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3EnF,OAAA,CAACE,IAAI;YAACsF,IAAI,EAAC,WAAW;YAAAZ,QAAA,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,EAEhB/D,UAAU,iBACTpB,OAAA,CAACR,KAAK;UACJF,OAAO,EAAE8B,UAAW;UACpBoE,IAAI,EAAC,OAAO;UACZC,QAAQ;UACRJ,KAAK,EAAE;YAAEK,YAAY,EAAE;UAAG;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACF,eAEDnF,OAAA,CAACY,UAAU;UACTW,IAAI,EAAEA,IAAK;UACX8C,IAAI,EAAC,OAAO;UACZsB,aAAa,EAAE;YAAEC,QAAQ,EAAE;UAAK,CAAE;UAClC3D,QAAQ,EAAEA,QAAS;UACnB4D,IAAI,EAAC,OAAO;UAAAjB,QAAA,gBAEZ5E,OAAA,CAAChB,IAAI,CAAC8G,IAAI;YACRzB,IAAI,EAAC,UAAU;YACf0B,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE1G,OAAO,EAAE;YAAU,CAAC,CAAE;YAAAsF,QAAA,eAEhD5E,OAAA,CAACf,KAAK;cACJgH,MAAM,eAAEjG,OAAA,CAACP,YAAY;gBAAC4F,KAAK,EAAE;kBAAEE,KAAK,EAAE;gBAAU;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACtDe,WAAW,EAAC;YAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZnF,OAAA,CAAChB,IAAI,CAAC8G,IAAI;YACRzB,IAAI,EAAC,UAAU;YACf0B,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAE1G,OAAO,EAAE;YAAS,CAAC,CAAE;YAAAsF,QAAA,eAE/C5E,OAAA,CAACf,KAAK,CAACkH,QAAQ;cACbF,MAAM,eAAEjG,OAAA,CAACN,YAAY;gBAAC2F,KAAK,EAAE;kBAAEE,KAAK,EAAE;gBAAU;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACtDe,WAAW,EAAC;YAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC,eAEZnF,OAAA,CAAChB,IAAI,CAAC8G,IAAI;YAAAlB,QAAA,gBACR5E,OAAA,CAAChB,IAAI,CAAC8G,IAAI;cAACzB,IAAI,EAAC,UAAU;cAAC+B,aAAa,EAAC,SAAS;cAACC,OAAO;cAAAzB,QAAA,eACxD5E,OAAA,CAACX,QAAQ;gBAAAuF,QAAA,EAAC;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEZnF,OAAA;cAAGqF,KAAK,EAAE;gBAAEiB,KAAK,EAAE;cAAQ,CAAE;cAACC,IAAI,EAAC,GAAG;cAAA3B,QAAA,EAAC;YAEvC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAEZnF,OAAA,CAAChB,IAAI,CAAC8G,IAAI;YAAAlB,QAAA,eACR5E,OAAA,CAACd,MAAM;cAACsG,IAAI,EAAC,SAAS;cAACgB,QAAQ,EAAC,QAAQ;cAACC,KAAK;cAACvF,OAAO,EAAEA,OAAQ;cAAA0D,QAAA,EAAC;YAEjE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZnF,OAAA,CAAChB,IAAI,CAAC8G,IAAI;YAAAlB,QAAA,eACR5E,OAAA,CAACd,MAAM;cAACsG,IAAI,EAAC,MAAM;cAACkB,OAAO,EAAEjC,eAAgB;cAACgC,KAAK;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEbnF,OAAA,CAACc,UAAU;UAAA8D,QAAA,GAAC,OACR,EAAC,IAAI+B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAC,oDAC9B;QAAA;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAErB,CAAC;AAAClE,EAAA,CAhRID,KAAK;EAAA,QAGQrB,WAAW,EACbX,IAAI,CAACwC,OAAO;AAAA;AAAAqF,GAAA,GAJvB7F,KAAK;AAkRX,eAAeA,KAAK;AAAC,IAAAX,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAA8F,GAAA;AAAAC,YAAA,CAAAzG,EAAA;AAAAyG,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAjG,GAAA;AAAAiG,YAAA,CAAA/F,GAAA;AAAA+F,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}