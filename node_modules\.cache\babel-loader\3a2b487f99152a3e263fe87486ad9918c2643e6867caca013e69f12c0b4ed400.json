{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null; // 新增：非机动车模型\nlet preloadedPeopleModel = null; // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.3; // 滤波系数，值越小滤波效果越强（0-1之间）\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n  bsm: 'changli/cloud/v2x/obu/bsm',\n  rsm: 'changli/cloud/v2x/rsu/rsm',\n  scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',\n  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat' // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 添加位置滤波函数\nconst filterPosition = newPos => {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n};\n\n// 添加朝向滤波函数\nconst filterRotation = newRotation => {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n\n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n  return filteredRotation;\n};\nconst CampusModel = ({\n  className,\n  onCurrentRSUChange,\n  selectedRSUs\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null); // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    content: null,\n    phases: []\n  });\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',\n    // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',\n    // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',\n    // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({\n    topic: '',\n    content: ''\n  });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = value => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n\n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n\n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n\n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n\n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n\n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        var _payload$data;\n        console.log('收到RSM消息:', payload);\n\n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n\n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n\n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          console.log('忽略过期的RSM消息:', {\n            设备MAC: deviceMac,\n            消息时间戳: messageTimestamp,\n            最新时间戳: lastTimestamp\n          });\n          return;\n        }\n\n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        console.log('RSM消息时间戳更新:', {\n          设备MAC: deviceMac,\n          时间戳: messageTimestamp,\n          是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        });\n        const participants = ((_payload$data = payload.data) === null || _payload$data === void 0 ? void 0 : _payload$data.participants) || [];\n        const rsuid = payload.data.rsuid;\n\n        // 分类处理不同类型的参与者\n        const now = Date.now();\n\n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id = rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n          if (type === '3' || type === '2' || type === '1') {\n            // if(type === '3'){\n            // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n\n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1':\n                // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2':\n                // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3':\n                // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return;\n              // 跳过未知类型\n            }\n\n            // 获取或创建模型\n            let model = vehicleModels.get(id);\n            if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = preloadedModel.clone();\n              // 根据类型调整高度\n              const height = type === '3' ? 0.5 : 1.0; // 行人模型贴近地面\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              scene.add(newModel);\n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n                type: type\n              });\n            } else if (model) {\n              // 更新现有模型\n              model.model.position.set(modelPos.x, model.type === '3' ? 0.5 : 1.0, -modelPos.y);\n              model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              model.lastUpdate = now;\n              model.model.updateMatrix();\n              model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        console.log('收到BSM消息:', payload);\n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n\n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        const newRotation = Math.PI - newState.heading * Math.PI / 180;\n\n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n\n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          newVehicleModel.position.copy(newPosition);\n          newVehicleModel.rotation.y = newRotation;\n          scene.add(newVehicleModel);\n\n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1',\n            // 设置为机动车类型\n            isMain: isMainVehicle\n          });\n          console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition);\n          const filteredRotation = filterRotation(newRotation);\n\n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n\n          console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n\n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n\n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 1秒\n\n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        return;\n      }\n\n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        console.log('收到SPAT消息:', payload);\n        // 检查是否有intersection数据\n        if (payload.intersections && payload.intersections.length > 0) {\n          // 遍历所有intersection\n          payload.intersections.forEach(intersection => {\n            const interId = intersection.id;\n            // 查找对应的交通灯\n            const trafficLight = trafficLightsMap.get(interId);\n\n            // 保存交通灯状态信息到全局映射中，以便点击时使用\n            if (intersection.phases && intersection.phases.length > 0) {\n              trafficLightStates.set(interId, {\n                phases: intersection.phases.map(phase => ({\n                  phaseId: phase.phaseId,\n                  trafficLight: phase.state.toLowerCase().startsWith('g') ? 'G' : phase.state.toLowerCase().startsWith('y') ? 'Y' : 'R',\n                  remainTime: phase.remainTime || Math.floor(Math.random() * 30) + 5 // 如果没有倒计时信息，生成一个假的\n                }))\n              });\n              console.log(`更新路口 ${interId} 的交通灯状态信息:`, trafficLightStates.get(interId));\n            }\n            if (trafficLight && intersection.phases) {\n              // 确定交通灯状态 (使用第一个相位的状态简化处理)\n              const phase = intersection.phases[0];\n              if (phase && phase.state) {\n                // 根据相位状态更新交通灯颜色\n                const trafficLightModel = trafficLight.model;\n\n                // 移除旧的灯光模型(如果存在)\n                if (trafficLightModel.children.length > 0) {\n                  trafficLightModel.children.forEach(child => {\n                    trafficLightModel.remove(child);\n                  });\n                }\n\n                // 根据状态创建新的灯光\n                let lightColor;\n                switch (phase.state.toLowerCase()) {\n                  case 'green':\n                    lightColor = 0x00FF00; // 绿色\n                    break;\n                  case 'yellow':\n                    lightColor = 0xFFFF00; // 黄色\n                    break;\n                  case 'red':\n                    lightColor = 0xFF0000; // 红色\n                    break;\n                  default:\n                    lightColor = 0x888888;\n                  // 灰色(未知状态)\n                }\n\n                // 创建一个球体作为灯光\n                const lightGeometry = new THREE.SphereGeometry(2, 16, 16);\n                const lightMaterial = new THREE.MeshBasicMaterial({\n                  color: lightColor,\n                  emissive: lightColor,\n                  emissiveIntensity: 1\n                });\n                const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n                lightMesh.position.set(0, 10, 0); // 放在交通灯顶部\n\n                // 添加光源使灯光更明显\n                const light = new THREE.PointLight(lightColor, 1, 50);\n                light.position.set(0, 10, 0);\n\n                // 将灯光添加到交通灯模型\n                trafficLightModel.add(lightMesh);\n                trafficLightModel.add(light);\n                console.log(`更新路口 ${trafficLight.intersection.name} (${interId}) 的交通灯状态为: ${phase.state}`);\n              }\n            }\n          });\n        }\n\n        // 更新界面中展示的消息\n        setLastMessage({\n          topic: '交通灯信号',\n          content: `收到交通灯信号(SPAT)消息，涉及 ${payload.intersections ? payload.intersections.length : 0} 个路口`\n        });\n\n        // 将SPAT消息发送到其他组件\n        window.postMessage({\n          type: 'SPAT',\n          data: payload\n        }, '*');\n        return;\n      }\n\n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        console.log('收到RSI消息:', payload);\n\n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n\n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(parseFloat(rsiData.posLong), parseFloat(rsiData.posLat));\n\n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          switch (eventType) {\n            case '401':\n              // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':\n              // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':\n              // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':\n              // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':\n              // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002':\n              // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':\n              // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n\n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        return;\n      }\n\n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        console.log('收到场景事件消息:', payload);\n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n\n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n\n        // 根据场景类型显示不同的提示或标记\n        switch (sceneType) {\n          case '2':\n            // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':\n            // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':\n            // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':\n            // 限速提醒\n            const speedLimit = sceneData.eventData1; // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':\n            // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':\n            // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':\n            // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':\n            // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':\n            // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':\n            // 信号灯优先\n            const priorityType = sceneData.eventData1; // 优先类型\n            const duration = sceneData.eventData2; // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: payload.type,\n        data: payload\n      });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n\n        // 检查scene是否初始化\n        if (scene) {\n          scene.add(model);\n\n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } else {\n          console.error('无法添加模型：场景未初始化');\n        }\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 100, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n\n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n\n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n\n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach(lightObj => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n\n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n\n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n\n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n\n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n\n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n\n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {\n          // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n\n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = event => {\n        if (scene && cameraRef.current) {\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        }\n      };\n\n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n\n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({\n      color: 0x333333\n    });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n\n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({\n      color: 0x666666\n    });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    return trafficLightModel;\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: labelStyle,\n      children: \"\\u8DEF\\u53E3\\u9009\\u62E9\\uFF1A\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1353,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      style: intersectionSelectStyle,\n      placeholder: \"\\u8BF7\\u9009\\u62E9\\u8DEF\\u53E3\\u4F4D\\u7F6E\",\n      onChange: handleIntersectionChange,\n      options: intersectionsData.intersections.map(intersection => ({\n        value: intersection.name,\n        label: intersection.name\n      })),\n      size: \"large\",\n      bordered: true,\n      dropdownStyle: {\n        zIndex: 1002,\n        maxHeight: '300px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1354,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1369,\n      columnNumber: 7\n    }, this), trafficLightPopover.visible && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        left: `${trafficLightPopover.position.x}px`,\n        top: `${trafficLightPopover.position.y}px`,\n        transform: 'translate(-50%, -100%)',\n        zIndex: 1003,\n        backgroundColor: 'rgba(0, 0, 0, 0.85)',\n        color: 'white',\n        borderRadius: '8px',\n        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.5)',\n        padding: '0',\n        maxHeight: '80vh',\n        overflowY: 'auto',\n        border: '1px solid rgba(255,255,255,0.2)',\n        animation: 'fadeIn 0.3s ease-out'\n      },\n      children: [trafficLightPopover.content, /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          position: 'absolute',\n          top: '2px',\n          right: '2px',\n          background: 'none',\n          border: 'none',\n          color: 'white',\n          fontSize: '16px',\n          cursor: 'pointer',\n          padding: '4px 8px',\n          fontWeight: 'bold'\n        },\n        onClick: () => handleClosePopover(setTrafficLightPopover),\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1392,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1373,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1413,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1423,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1412,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"6+2ZyTRgY8jWVNJV4Z91w5YqHck=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n\n    // 并行加载所有模型\n    try {\n      const [vehicleGltf, cyclistGltf, peopleGltf, trafficLightGltf] = await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`), loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`), loader.loadAsync(`${BASE_URL}/changli2/people.glb`), loader.loadAsync(`${BASE_URL}/changli2/traffic light.glb`)]);\n\n      // 处理机动车模型\n      preloadedVehicleModel = vehicleGltf.scene;\n      preloadedVehicleModel.traverse(child => {\n        if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n            color: 0xffffff,\n            metalness: 0.2,\n            roughness: 0.1,\n            envMapIntensity: 1.0\n          });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n        }\n      });\n\n      // 处理非机动车模型\n      preloadedCyclistModel = cyclistGltf.scene;\n      // 设置非机动车模型的缩放\n      preloadedCyclistModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedCyclistModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n\n      // 处理行人模型\n      preloadedPeopleModel = peopleGltf.scene;\n      // 设置行人模型的缩放\n      preloadedPeopleModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedPeopleModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n\n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse(child => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n      console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n\n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n\n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/traffic light.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = type => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y); // 设置位置，稍微升高以便可见\n\n  // 为标记添加一个定时器，在5秒后自动移除\n  setTimeout(() => {\n    scene.remove(sprite);\n  }, 500);\n\n  // 将标记添加到场景中\n  scene.add(sprite);\n  console.log('添加场景事件标记:', {\n    位置: position,\n    文本: text,\n    颜色: color\n  });\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = converterInstance => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n\n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach(lightObj => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n\n        // 设置位置，离地面高度为5米\n        trafficLightModel.position.set(modelPos.x, 10, -modelPos.y);\n\n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n\n        // 遍历模型的所有子对象，添加相同的userData\n        trafficLightModel.traverse(child => {\n          if (child.isMesh) {\n            child.userData = {\n              type: 'trafficLight',\n              interId: intersection.interId,\n              name: intersection.name\n            };\n          }\n        });\n\n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n\n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n\n        // 为每个路口生成模拟的交通灯状态数据（如果尚未收到SPAT消息）\n        if (!trafficLightStates.has(intersection.interId)) {\n          // 生成模拟的相位数据\n          const mockPhases = [{\n            phaseId: '1',\n            trafficLight: 'G',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 北进口左转\n          {\n            phaseId: '2',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 北进口直行\n          {\n            phaseId: '3',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 北进口右转\n          {\n            phaseId: '5',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 东进口左转\n          {\n            phaseId: '6',\n            trafficLight: 'G',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 东进口直行\n          {\n            phaseId: '7',\n            trafficLight: 'G',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 东进口右转\n          {\n            phaseId: '9',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 南进口左转\n          {\n            phaseId: '10',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 南进口直行\n          {\n            phaseId: '11',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 南进口右转\n          {\n            phaseId: '13',\n            trafficLight: 'G',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 西进口左转\n          {\n            phaseId: '14',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 西进口直行\n          {\n            phaseId: '15',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          } // 西进口右转\n          ];\n          trafficLightStates.set(intersection.interId, {\n            phases: mockPhases\n          });\n          console.log(`为路口 ${intersection.name} (${intersection.interId}) 生成模拟的交通灯状态信息`);\n        }\n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯\n  const geometry = new THREE.BoxGeometry(4, 15, 4);\n  const material = new THREE.MeshBasicMaterial({\n    color: 0x333333\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n\n  // 设置位置\n  trafficLightModel.position.set(modelPos.x, 10, -modelPos.y);\n\n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n\n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n\n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n\n  // 为路口生成模拟的交通灯状态数据（如果尚未收到SPAT消息）\n  if (!trafficLightStates.has(intersection.interId)) {\n    // 生成模拟的相位数据\n    const mockPhases = [{\n      phaseId: '1',\n      trafficLight: 'G',\n      remainTime: Math.floor(Math.random() * 30) + 5\n    },\n    // 北进口左转\n    {\n      phaseId: '2',\n      trafficLight: 'R',\n      remainTime: Math.floor(Math.random() * 30) + 5\n    },\n    // 北进口直行\n    {\n      phaseId: '3',\n      trafficLight: 'R',\n      remainTime: Math.floor(Math.random() * 30) + 5\n    },\n    // 北进口右转\n    {\n      phaseId: '5',\n      trafficLight: 'R',\n      remainTime: Math.floor(Math.random() * 30) + 5\n    },\n    // 东进口左转\n    {\n      phaseId: '6',\n      trafficLight: 'G',\n      remainTime: Math.floor(Math.random() * 30) + 5\n    },\n    // 东进口直行\n    {\n      phaseId: '7',\n      trafficLight: 'G',\n      remainTime: Math.floor(Math.random() * 30) + 5\n    },\n    // 东进口右转\n    {\n      phaseId: '9',\n      trafficLight: 'R',\n      remainTime: Math.floor(Math.random() * 30) + 5\n    },\n    // 南进口左转\n    {\n      phaseId: '10',\n      trafficLight: 'R',\n      remainTime: Math.floor(Math.random() * 30) + 5\n    },\n    // 南进口直行\n    {\n      phaseId: '11',\n      trafficLight: 'R',\n      remainTime: Math.floor(Math.random() * 30) + 5\n    },\n    // 南进口右转\n    {\n      phaseId: '13',\n      trafficLight: 'G',\n      remainTime: Math.floor(Math.random() * 30) + 5\n    },\n    // 西进口左转\n    {\n      phaseId: '14',\n      trafficLight: 'R',\n      remainTime: Math.floor(Math.random() * 30) + 5\n    },\n    // 西进口直行\n    {\n      phaseId: '15',\n      trafficLight: 'R',\n      remainTime: Math.floor(Math.random() * 30) + 5\n    } // 西进口右转\n    ];\n    trafficLightStates.set(intersection.interId, {\n      phases: mockPhases\n    });\n    console.log(`为路口 ${intersection.name} (${intersection.interId}) 生成模拟的交通灯状态信息`);\n  }\n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = converterInstance => {\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(parseFloat(intersection.longitude), parseFloat(intersection.latitude));\n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = phaseId => {\n  const directions = {\n    '1': '北进口左转',\n    '2': '北进口直行',\n    '3': '北进口右转',\n    '5': '东进口左转',\n    '6': '东进口直行',\n    '7': '东进口右转',\n    '9': '南进口左转',\n    '10': '南进口直行',\n    '11': '南进口右转',\n    '13': '西进口左转',\n    '14': '西进口直行',\n    '15': '西进口右转'\n  };\n  return directions[phaseId] || `相位${phaseId}`;\n};\n\n// 处理鼠标点击事件的函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n\n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = (event.clientX - rect.left) / container.clientWidth * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n\n  // 创建射线，提高检测精度\n  const raycaster = new THREE.Raycaster();\n  raycaster.params.Line.threshold = 1;\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Mesh.threshold = 0.1; // 增加网格检测阈值\n\n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n\n  // 获取相交的对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n\n  // 调试信息\n  console.log('鼠标点击坐标:', {\n    x: event.clientX,\n    y: event.clientY\n  });\n  console.log('检测到的交叉物体数量:', intersects.length);\n  intersects.forEach((obj, index) => {\n    console.log(`物体 ${index}:`, obj.object.type, obj.object.userData);\n  });\n\n  // 先关闭当前可能打开的弹出窗口\n  setPopoverState(prev => {\n    if (prev.visible) {\n      return {\n        ...prev,\n        visible: false\n      };\n    }\n    return prev;\n  });\n\n  // 检查是否点击了红绿灯\n  for (let i = 0; i < intersects.length; i++) {\n    const obj = getTrafficLightFromObject(intersects[i].object);\n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      const interId = obj.userData.interId;\n      const stateInfo = trafficLightStates.get(interId);\n      const lightObj = trafficLightsMap.get(interId);\n      console.log('找到红绿灯对象:', obj.userData);\n      if (lightObj) {\n        // 获取屏幕上的位置\n        const position = new THREE.Vector3();\n        position.copy(obj.position);\n        position.project(cameraInstance);\n        const x = (position.x * 0.5 + 0.5) * container.clientWidth;\n        const y = (-(position.y * 0.5) + 0.5) * container.clientHeight;\n        console.log('红绿灯在屏幕上的位置:', {\n          x,\n          y\n        });\n\n        // 如果有该路口的信号灯状态信息\n        if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n          console.log(`显示路口 ${obj.userData.name} (${interId}) 的交通灯状态信息:`, stateInfo);\n\n          // 生成弹出窗口内容\n          const popoverContent = /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '10px',\n              width: '300px',\n              maxHeight: '400px',\n              overflowY: 'auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontWeight: 'bold',\n                marginBottom: '10px',\n                fontSize: '16px',\n                borderBottom: '1px solid #eee',\n                paddingBottom: '5px'\n              },\n              children: [obj.userData.name, \" (ID: \", interId, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1905,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: stateInfo.phases.map((phase, index) => {\n                // 根据灯色确定显示文本和颜色\n                let lightColor;\n                switch (phase.trafficLight) {\n                  case 'G':\n                    lightColor = '#00ff00';\n                    break;\n                  case 'Y':\n                    lightColor = '#ffff00';\n                    break;\n                  case 'R':\n                  default:\n                    lightColor = '#ff0000';\n                    break;\n                }\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: '8px',\n                    backgroundColor: 'rgba(255,255,255,0.1)',\n                    padding: '5px',\n                    borderRadius: '4px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontWeight: 'bold'\n                    },\n                    children: getPhaseDirection(phase.phaseId)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1938,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u706F\\u8272: \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1942,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: lightColor,\n                        fontWeight: 'bold',\n                        backgroundColor: 'rgba(0,0,0,0.3)',\n                        padding: '0 5px',\n                        borderRadius: '2px'\n                      },\n                      children: phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1943,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1941,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      justifyContent: 'space-between'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"\\u5012\\u8BA1\\u65F6: \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1954,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontWeight: 'bold'\n                      },\n                      children: [phase.remainTime, \" \\u79D2\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1955,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1953,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1932,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1914,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1904,\n            columnNumber: 13\n          }, this);\n\n          // 更新弹出窗口状态\n          setPopoverState({\n            visible: true,\n            interId: interId,\n            position: {\n              x,\n              y: y - 20\n            },\n            // 稍微向上偏移，避免被手指遮挡\n            content: popoverContent,\n            phases: stateInfo.phases\n          });\n          console.log('已设置弹出窗口状态:', {\n            visible: true,\n            interId,\n            x,\n            y: y - 20\n          });\n          return; // 找到并处理后立即返回\n        } else {\n          // 如果没有信号灯状态信息，或者强制创建模拟数据\n          // 生成模拟数据\n          const mockPhases = [{\n            phaseId: '1',\n            trafficLight: 'G',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 北进口左转\n          {\n            phaseId: '2',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 北进口直行\n          {\n            phaseId: '3',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 北进口右转\n          {\n            phaseId: '5',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 东进口左转\n          {\n            phaseId: '6',\n            trafficLight: 'G',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 东进口直行\n          {\n            phaseId: '7',\n            trafficLight: 'G',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 东进口右转\n          {\n            phaseId: '9',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 南进口左转\n          {\n            phaseId: '10',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 南进口直行\n          {\n            phaseId: '11',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 南进口右转\n          {\n            phaseId: '13',\n            trafficLight: 'G',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 西进口左转\n          {\n            phaseId: '14',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          },\n          // 西进口直行\n          {\n            phaseId: '15',\n            trafficLight: 'R',\n            remainTime: Math.floor(Math.random() * 30) + 5\n          } // 西进口右转\n          ];\n          trafficLightStates.set(interId, {\n            phases: mockPhases\n          });\n          console.log(`为路口 ${obj.userData.name} (${interId}) 生成模拟的交通灯状态信息`);\n\n          // 递归调用自身来处理新生成的数据\n          handleMouseClick(event, container, sceneInstance, cameraInstance, setPopoverState);\n          return; // 找到并处理后立即返回\n        }\n      }\n    }\n  }\n\n  // 如果没有点击到红绿灯，记录这个信息\n  console.log('未检测到红绿灯对象');\n};\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = object => {\n  let current = object;\n\n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current) {\n    if (current.userData && current.userData.type === 'trafficLight') {\n      return current;\n    }\n    current = current.parent;\n  }\n  return null;\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = setPopoverState => {\n  setPopoverState(prev => ({\n    ...prev,\n    visible: false\n  }));\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "Select", "Popover", "intersectionsData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "lastPosition", "lastRotation", "ALPHA", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "vehicleModels", "Map", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "console", "log", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "CampusModel", "className", "onCurrentRSUChange", "selectedRSUs", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "updateMatrix", "updateMatrixWorld", "相机位置", "toArray", "目标点", "handleMqttMessage", "message", "payload", "JSON", "parse", "_payload$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "get", "设备MAC", "消息时间戳", "最新时间戳", "时间戳", "是否为最新", "participants", "rsuid", "now", "Date", "for<PERSON>ach", "participant", "id", "partPtcId", "type", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "model", "newModel", "height", "rotation", "add", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "Set", "map", "p", "modelData", "has", "remove", "delete", "bsmData", "bsmid", "newState", "partLong", "partLat", "newPosition", "Vector3", "vehicleObj", "newVehicleModel", "is<PERSON><PERSON>", "toFixed", "filteredPosition", "length", "trafficLight", "phase", "phaseId", "toLowerCase", "startsWith", "remainTime", "floor", "random", "trafficLightModel", "children", "child", "lightColor", "lightGeometry", "SphereGeometry", "lightMaterial", "MeshBasicMaterial", "emissive", "emissiveIntensity", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "light", "PointLight", "postMessage", "rsiData", "rsuId", "events", "rtes", "event", "eventId", "rteId", "eventType", "description", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "事件ID", "事件类型", "事件说明", "开始时间", "结束时间", "位置", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "stringify", "onerror", "onclose", "setTimeout", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "traverse", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "xhr", "loaded", "total", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "clearInterval", "close", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "clear", "lightObj", "handleMainVehicleChange", "intervalId", "setInterval", "timer", "createTrafficLights", "clearTimeout", "handleClick", "handleMouseClick", "initScene", "createSimpleTrafficLight", "geometry", "BoxGeometry", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "onChange", "options", "label", "size", "bordered", "dropdownStyle", "maxHeight", "ref", "overflowY", "animation", "right", "background", "onClick", "handleClosePopover", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "vehicleGltf", "cyclistGltf", "peopleGltf", "trafficLightGltf", "all", "loadAsync", "materia", "err", "types", "文本", "颜色", "converterInstance", "createFallbackTrafficLights", "userData", "mockPhases", "getPhaseDirection", "directions", "container", "sceneInstance", "cameraInstance", "setPopoverState", "rect", "getBoundingClientRect", "mouseX", "clientX", "clientWidth", "mouseY", "clientY", "clientHeight", "raycaster", "Raycaster", "params", "Line", "threshold", "Points", "mouseVector", "Vector2", "setFromCamera", "intersects", "intersectObjects", "obj", "index", "object", "prev", "getTrafficLightFromObject", "stateInfo", "project", "popoverContent", "marginBottom", "borderBottom", "paddingBottom", "justifyContent", "parent", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.3; // 滤波系数，值越小滤波效果越强（0-1之间）\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n  bsm: 'changli/cloud/v2x/obu/bsm',\n  rsm: 'changli/cloud/v2x/rsu/rsm',\n  scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 添加位置滤波函数\nconst filterPosition = (newPos) => {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n};\n\n// 添加朝向滤波函数\nconst filterRotation = (newRotation) => {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n  return filteredRotation;\n};\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);  // 添加动画帧引用\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '60px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x, 100, -modelCoords.y);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        console.log('收到RSM消息:', payload);\n        \n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n        \n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n        \n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          console.log('忽略过期的RSM消息:', {\n            设备MAC: deviceMac,\n            消息时间戳: messageTimestamp,\n            最新时间戳: lastTimestamp\n          });\n          return;\n        }\n        \n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        \n        console.log('RSM消息时间戳更新:', {\n          设备MAC: deviceMac,\n          时间戳: messageTimestamp,\n          是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        });\n        \n        const participants = payload.data?.participants || [];\n        const rsuid = payload.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          const id =  rsuid + participant.partPtcId;\n          const type = participant.partPtcType;\n\n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n          // 解析位置和状态信息\n          const state = {\n            longitude: parseFloat(participant.partPosLong),\n            latitude: parseFloat(participant.partPosLat),\n            speed: parseFloat(participant.partSpeed),\n            heading: parseFloat(participant.partHeading)\n          };\n          \n          const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n          \n          // 根据类型选择对应的预加载模型\n          let preloadedModel;\n          switch (type) {\n            case '1': // 机动车\n              preloadedModel = preloadedVehicleModel;\n              break;\n            case '2': // 非机动车\n              preloadedModel = preloadedCyclistModel;\n              break;\n            case '3': // 行人\n              preloadedModel = preloadedPeopleModel;\n              break;\n            default:\n              return; // 跳过未知类型\n          }\n          \n          // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n            // 创建新模型实例\n            const newModel = preloadedModel.clone();\n            // 根据类型调整高度\n            const height = type === '3' ? 0.5 : 1.0; // 行人模型贴近地面\n            newModel.position.set(modelPos.x, height, -modelPos.y);\n            newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            scene.add(newModel);\n            \n            vehicleModels.set(id, {\n              model: newModel,\n              lastUpdate: now,\n              type: type\n            });\n          } else if (model) {\n            // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 0.5 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n          }\n          }\n        });\n\n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 2000;\n        const currentIds = new Set(participants.map(p => p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的参与者: ID ${id}, 类型 ${modelData.type}`);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        console.log('收到BSM消息:', payload);\n        \n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        console.log('车辆ID:', bsmid);\n        \n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        const newRotation = Math.PI - newState.heading * Math.PI / 180;\n        \n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n        \n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        \n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          newVehicleModel.position.copy(newPosition);\n          newVehicleModel.rotation.y = newRotation;\n          scene.add(newVehicleModel);\n          \n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle\n          });\n          \n          console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition);\n          const filteredRotation = filterRotation(newRotation);\n          \n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n          \n          console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n        \n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 1秒\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD) {\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        \n        return;\n      }\n      \n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        console.log('收到SPAT消息:', payload);\n        // 检查是否有intersection数据\n        if (payload.intersections && payload.intersections.length > 0) {\n          // 遍历所有intersection\n          payload.intersections.forEach(intersection => {\n            const interId = intersection.id;\n            // 查找对应的交通灯\n            const trafficLight = trafficLightsMap.get(interId);\n            \n            // 保存交通灯状态信息到全局映射中，以便点击时使用\n            if (intersection.phases && intersection.phases.length > 0) {\n              trafficLightStates.set(interId, {\n                phases: intersection.phases.map(phase => ({\n                  phaseId: phase.phaseId,\n                  trafficLight: phase.state.toLowerCase().startsWith('g') ? 'G' : \n                                phase.state.toLowerCase().startsWith('y') ? 'Y' : 'R',\n                  remainTime: phase.remainTime || Math.floor(Math.random() * 30) + 5 // 如果没有倒计时信息，生成一个假的\n                }))\n              });\n              \n              console.log(`更新路口 ${interId} 的交通灯状态信息:`, trafficLightStates.get(interId));\n            }\n            \n            if (trafficLight && intersection.phases) {\n              // 确定交通灯状态 (使用第一个相位的状态简化处理)\n              const phase = intersection.phases[0];\n              if (phase && phase.state) {\n                // 根据相位状态更新交通灯颜色\n                const trafficLightModel = trafficLight.model;\n                \n                // 移除旧的灯光模型(如果存在)\n                if (trafficLightModel.children.length > 0) {\n                  trafficLightModel.children.forEach(child => {\n                    trafficLightModel.remove(child);\n                  });\n                }\n                \n                // 根据状态创建新的灯光\n                let lightColor;\n                switch(phase.state.toLowerCase()) {\n                  case 'green':\n                    lightColor = 0x00FF00; // 绿色\n                    break;\n                  case 'yellow':\n                    lightColor = 0xFFFF00; // 黄色\n                    break;\n                  case 'red':\n                    lightColor = 0xFF0000; // 红色\n                    break;\n                  default:\n                    lightColor = 0x888888; // 灰色(未知状态)\n                }\n                \n                // 创建一个球体作为灯光\n                const lightGeometry = new THREE.SphereGeometry(2, 16, 16);\n                const lightMaterial = new THREE.MeshBasicMaterial({ \n                  color: lightColor,\n                  emissive: lightColor,\n                  emissiveIntensity: 1\n                });\n                const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n                lightMesh.position.set(0, 10, 0); // 放在交通灯顶部\n                \n                // 添加光源使灯光更明显\n                const light = new THREE.PointLight(lightColor, 1, 50);\n                light.position.set(0, 10, 0);\n                \n                // 将灯光添加到交通灯模型\n                trafficLightModel.add(lightMesh);\n                trafficLightModel.add(light);\n                \n                console.log(`更新路口 ${trafficLight.intersection.name} (${interId}) 的交通灯状态为: ${phase.state}`);\n              }\n            }\n          });\n        }\n        \n        // 更新界面中展示的消息\n        setLastMessage({\n          topic: '交通灯信号',\n          content: `收到交通灯信号(SPAT)消息，涉及 ${payload.intersections ? payload.intersections.length : 0} 个路口`\n        });\n\n        // 将SPAT消息发送到其他组件\n        window.postMessage({\n          type: 'SPAT',\n          data: payload\n        }, '*');\n\n        return;\n      }\n      \n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        console.log('收到RSI消息:', payload);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        \n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          console.log('RSI事件处理:', {\n            事件ID: eventId,\n            事件类型: eventType,\n            事件说明: description,\n            开始时间: startTime,\n            结束时间: endTime,\n            位置: modelPos\n          });\n        });\n        \n        return;\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        console.log('收到场景事件消息:', payload);\n        \n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      } \n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: payload.type,\n        data: payload\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.position.set(0, 1.0, 0);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          \n          // 检查scene是否初始化\n          if (scene) {\n            scene.add(model);\n            \n            // 在校园模型加载完成后初始化场景\n            await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          100,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 首先停止动画循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 清理定时器\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n\n      // 3. 关闭 WebSocket 连接\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n        mqttClientRef.current = null;\n      }\n\n      // 4. 移除事件监听器\n      window.removeEventListener('resize', handleResize);\n\n      // 5. 清理渲染器\n      if (renderer && containerRef.current) {\n        containerRef.current.removeChild(renderer.domElement);\n        renderer.dispose();\n      }\n      \n      // 6. 清理场景中的所有车辆模型\n      if (vehicleModels) {\n        vehicleModels.forEach((modelData, id) => {\n          if (modelData.model && scene) {\n            scene.remove(modelData.model);\n          }\n        });\n        vehicleModels.clear();\n      }\n      \n      // 7. 清理红绿灯模型\n      trafficLightsMap.forEach((lightObj) => {\n        if (scene && lightObj.model) {\n          scene.remove(lightObj.model);\n        }\n      });\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n      \n      // 8. 移除点击事件监听器 - 此处不需要，在专门的useEffect中已处理\n      \n      // 9. 重置全局变量\n      scene = null;\n      controls = null;\n      preloadedVehicleModel = null;\n      preloadedCyclistModel = null;\n      preloadedPeopleModel = null;\n      preloadedTrafficLightModel = null;\n      globalVehicleRef = null;\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n    \n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n    \n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n    \n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n    \n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 1000);\n      \n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n  \n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current, setTrafficLightPopover);\n        }\n      };\n      \n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n      \n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n    \n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    \n    return trafficLightModel;\n  };\n\n  return (\n    <>\n      <span style={labelStyle}>路口选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择路口位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      \n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '8px',\n            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.5)',\n            padding: '0',\n            maxHeight: '80vh',\n            overflowY: 'auto',\n            border: '1px solid rgba(255,255,255,0.2)',\n            animation: 'fadeIn 0.3s ease-out',\n          }}\n        >\n          {trafficLightPopover.content}\n          <button \n            style={{\n              position: 'absolute',\n              top: '2px',\n              right: '2px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '16px',\n              cursor: 'pointer',\n              padding: '4px 8px',\n              fontWeight: 'bold',\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n      \n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    try {\n      const [vehicleGltf, cyclistGltf, peopleGltf, trafficLightGltf] = await Promise.all([\n        loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/traffic light.glb`)\n      ]);\n\n      // 处理机动车模型\n      preloadedVehicleModel = vehicleGltf.scene;\n      preloadedVehicleModel.traverse((child) => {\n        if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n            color: 0xffffff,\n            metalness: 0.2,\n            roughness: 0.1,\n            envMapIntensity: 1.0\n          });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n        }\n      });\n\n      // 处理非机动车模型\n      preloadedCyclistModel = cyclistGltf.scene;\n      // 设置非机动车模型的缩放\n      preloadedCyclistModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedCyclistModel.traverse((child) => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n\n      // 处理行人模型\n      preloadedPeopleModel = peopleGltf.scene;\n      // 设置行人模型的缩放\n      preloadedPeopleModel.scale.set(2, 2, 2);\n      // 保持原始材质\n      preloadedPeopleModel.traverse((child) => {\n        if (child.isMesh && child.material) {\n          // 只调整材质属性，保持原始颜色\n          child.material.metalness = 0.1;\n          child.material.roughness = 0.8;\n          child.material.envMapIntensity = 1.0;\n        }\n      });\n      \n      // 处理红绿灯模型\n      preloadedTrafficLightModel = trafficLightGltf.scene;\n      // 设置红绿灯模型的缩放\n      preloadedTrafficLightModel.scale.set(6, 6, 6);\n      // 保持原始材质\n      preloadedTrafficLightModel.traverse((child) => {\n        if (child.isMesh && child.material) {\n          // 设置材质属性\n          child.material.metalness = 0.2;\n          child.material.roughness = 0.3;\n          child.material.envMapIntensity = 1.2;\n        }\n      });\n\n      console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n      \n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n        \n        // 尝试单独加载红绿灯模型\n        if (!preloadedTrafficLightModel) {\n          console.log('正在单独加载红绿灯模型...');\n          const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/traffic light.glb`);\n          preloadedTrafficLightModel = trafficLightGltf.scene;\n          preloadedTrafficLightModel.scale.set(3, 3, 3);\n          console.log('红绿灯模型加载成功');\n        }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在5秒后自动移除\n  setTimeout(() => {\n    scene.remove(sprite);\n  }, 500);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  console.log('添加场景事件标记:', {\n    位置: position,\n    文本: text,\n    颜色: color\n  });\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  \n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  \n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载');\n    // 如果没有加载红绿灯模型，使用简单的替代物体\n    createFallbackTrafficLights(converterInstance);\n    return;\n  }\n  \n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      try {\n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n        \n        // 设置位置，离地面高度为5米\n        trafficLightModel.position.set(modelPos.x, 10, -modelPos.y);\n        \n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        \n        // 遍历模型的所有子对象，添加相同的userData\n        trafficLightModel.traverse((child) => {\n          if (child.isMesh) {\n            child.userData = {\n              type: 'trafficLight',\n              interId: intersection.interId,\n              name: intersection.name\n            };\n          }\n        });\n        \n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n        \n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        \n        // 为每个路口生成模拟的交通灯状态数据（如果尚未收到SPAT消息）\n        if (!trafficLightStates.has(intersection.interId)) {\n          // 生成模拟的相位数据\n          const mockPhases = [\n            { phaseId: '1', trafficLight: 'G', remainTime: Math.floor(Math.random() * 30) + 5 }, // 北进口左转\n            { phaseId: '2', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 北进口直行\n            { phaseId: '3', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 北进口右转\n            { phaseId: '5', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 东进口左转\n            { phaseId: '6', trafficLight: 'G', remainTime: Math.floor(Math.random() * 30) + 5 }, // 东进口直行\n            { phaseId: '7', trafficLight: 'G', remainTime: Math.floor(Math.random() * 30) + 5 }, // 东进口右转\n            { phaseId: '9', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 南进口左转\n            { phaseId: '10', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 南进口直行\n            { phaseId: '11', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 南进口右转\n            { phaseId: '13', trafficLight: 'G', remainTime: Math.floor(Math.random() * 30) + 5 }, // 西进口左转\n            { phaseId: '14', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 西进口直行\n            { phaseId: '15', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }  // 西进口右转\n          ];\n          \n          trafficLightStates.set(intersection.interId, {\n            phases: mockPhases\n          });\n          \n          console.log(`为路口 ${intersection.name} (${intersection.interId}) 生成模拟的交通灯状态信息`);\n        }\n        \n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯\n  const geometry = new THREE.BoxGeometry(4, 15, 4);\n  const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n  \n  // 设置位置\n  trafficLightModel.position.set(modelPos.x, 10, -modelPos.y);\n  \n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n  \n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n  \n  // 为路口生成模拟的交通灯状态数据（如果尚未收到SPAT消息）\n  if (!trafficLightStates.has(intersection.interId)) {\n    // 生成模拟的相位数据\n    const mockPhases = [\n      { phaseId: '1', trafficLight: 'G', remainTime: Math.floor(Math.random() * 30) + 5 }, // 北进口左转\n      { phaseId: '2', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 北进口直行\n      { phaseId: '3', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 北进口右转\n      { phaseId: '5', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 东进口左转\n      { phaseId: '6', trafficLight: 'G', remainTime: Math.floor(Math.random() * 30) + 5 }, // 东进口直行\n      { phaseId: '7', trafficLight: 'G', remainTime: Math.floor(Math.random() * 30) + 5 }, // 东进口右转\n      { phaseId: '9', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 南进口左转\n      { phaseId: '10', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 南进口直行\n      { phaseId: '11', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 南进口右转\n      { phaseId: '13', trafficLight: 'G', remainTime: Math.floor(Math.random() * 30) + 5 }, // 西进口左转\n      { phaseId: '14', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 西进口直行\n      { phaseId: '15', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }  // 西进口右转\n    ];\n    \n    trafficLightStates.set(intersection.interId, {\n      phases: mockPhases\n    });\n    \n    console.log(`为路口 ${intersection.name} (${intersection.interId}) 生成模拟的交通灯状态信息`);\n  }\n  \n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance) => {\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  const directions = {\n    '1': '北进口左转',\n    '2': '北进口直行',\n    '3': '北进口右转',\n    '5': '东进口左转',\n    '6': '东进口直行',\n    '7': '东进口右转',\n    '9': '南进口左转',\n    '10': '南进口直行',\n    '11': '南进口右转',\n    '13': '西进口左转',\n    '14': '西进口直行',\n    '15': '西进口右转'\n  };\n  \n  return directions[phaseId] || `相位${phaseId}`;\n};\n\n// 处理鼠标点击事件的函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance, setPopoverState) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  \n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n  \n  // 创建射线，提高检测精度\n  const raycaster = new THREE.Raycaster();\n  raycaster.params.Line.threshold = 1;\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Mesh.threshold = 0.1; // 增加网格检测阈值\n  \n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  \n  // 获取相交的对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  \n  // 调试信息\n  console.log('鼠标点击坐标:', { x: event.clientX, y: event.clientY });\n  console.log('检测到的交叉物体数量:', intersects.length);\n  intersects.forEach((obj, index) => {\n    console.log(`物体 ${index}:`, obj.object.type, obj.object.userData);\n  });\n  \n  // 先关闭当前可能打开的弹出窗口\n  setPopoverState(prev => {\n    if (prev.visible) {\n      return { ...prev, visible: false };\n    }\n    return prev;\n  });\n  \n  // 检查是否点击了红绿灯\n  for (let i = 0; i < intersects.length; i++) {\n    const obj = getTrafficLightFromObject(intersects[i].object);\n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      const interId = obj.userData.interId;\n      const stateInfo = trafficLightStates.get(interId);\n      const lightObj = trafficLightsMap.get(interId);\n      \n      console.log('找到红绿灯对象:', obj.userData);\n      \n      if (lightObj) {\n        // 获取屏幕上的位置\n        const position = new THREE.Vector3();\n        position.copy(obj.position);\n        position.project(cameraInstance);\n        \n        const x = (position.x * 0.5 + 0.5) * container.clientWidth;\n        const y = (-(position.y * 0.5) + 0.5) * container.clientHeight;\n        \n        console.log('红绿灯在屏幕上的位置:', { x, y });\n        \n        // 如果有该路口的信号灯状态信息\n        if (stateInfo && stateInfo.phases && stateInfo.phases.length > 0) {\n          console.log(`显示路口 ${obj.userData.name} (${interId}) 的交通灯状态信息:`, stateInfo);\n          \n          // 生成弹出窗口内容\n          const popoverContent = (\n            <div style={{ padding: '10px', width: '300px', maxHeight: '400px', overflowY: 'auto' }}>\n              <div style={{ \n                fontWeight: 'bold', \n                marginBottom: '10px',\n                fontSize: '16px',\n                borderBottom: '1px solid #eee',\n                paddingBottom: '5px'\n              }}>\n                {obj.userData.name} (ID: {interId})\n              </div>\n              <div>\n                {stateInfo.phases.map((phase, index) => {\n                  // 根据灯色确定显示文本和颜色\n                  let lightColor;\n                  switch (phase.trafficLight) {\n                    case 'G':\n                      lightColor = '#00ff00';\n                      break;\n                    case 'Y':\n                      lightColor = '#ffff00';\n                      break;\n                    case 'R':\n                    default:\n                      lightColor = '#ff0000';\n                      break;\n                  }\n                  \n                  return (\n                    <div key={index} style={{ \n                      marginBottom: '8px', \n                      backgroundColor: 'rgba(255,255,255,0.1)',\n                      padding: '5px',\n                      borderRadius: '4px'\n                    }}>\n                      <div style={{ fontWeight: 'bold' }}>\n                        {getPhaseDirection(phase.phaseId)}\n                      </div>\n                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                        <span>灯色: </span>\n                        <span style={{ \n                          color: lightColor, \n                          fontWeight: 'bold',\n                          backgroundColor: 'rgba(0,0,0,0.3)',\n                          padding: '0 5px',\n                          borderRadius: '2px'\n                        }}>\n                          {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                        </span>\n                      </div>\n                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                        <span>倒计时: </span>\n                        <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          );\n          \n          // 更新弹出窗口状态\n          setPopoverState({\n            visible: true,\n            interId: interId,\n            position: { x, y: y - 20 }, // 稍微向上偏移，避免被手指遮挡\n            content: popoverContent,\n            phases: stateInfo.phases\n          });\n          \n          console.log('已设置弹出窗口状态:', { visible: true, interId, x, y: y - 20 });\n          return; // 找到并处理后立即返回\n        } else {\n          // 如果没有信号灯状态信息，或者强制创建模拟数据\n          // 生成模拟数据\n          const mockPhases = [\n            { phaseId: '1', trafficLight: 'G', remainTime: Math.floor(Math.random() * 30) + 5 }, // 北进口左转\n            { phaseId: '2', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 北进口直行\n            { phaseId: '3', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 北进口右转\n            { phaseId: '5', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 东进口左转\n            { phaseId: '6', trafficLight: 'G', remainTime: Math.floor(Math.random() * 30) + 5 }, // 东进口直行\n            { phaseId: '7', trafficLight: 'G', remainTime: Math.floor(Math.random() * 30) + 5 }, // 东进口右转\n            { phaseId: '9', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 南进口左转\n            { phaseId: '10', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 南进口直行\n            { phaseId: '11', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 南进口右转\n            { phaseId: '13', trafficLight: 'G', remainTime: Math.floor(Math.random() * 30) + 5 }, // 西进口左转\n            { phaseId: '14', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }, // 西进口直行\n            { phaseId: '15', trafficLight: 'R', remainTime: Math.floor(Math.random() * 30) + 5 }  // 西进口右转\n          ];\n          \n          trafficLightStates.set(interId, {\n            phases: mockPhases\n          });\n          \n          console.log(`为路口 ${obj.userData.name} (${interId}) 生成模拟的交通灯状态信息`);\n          \n          // 递归调用自身来处理新生成的数据\n          handleMouseClick(event, container, sceneInstance, cameraInstance, setPopoverState);\n          return; // 找到并处理后立即返回\n        }\n      }\n    }\n  }\n  \n  // 如果没有点击到红绿灯，记录这个信息\n  console.log('未检测到红绿灯对象');\n};\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n  \n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current) {\n    if (current.userData && current.userData.type === 'trafficLight') {\n      return current;\n    }\n    current = current.parent;\n  }\n  \n  return null;\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  setPopoverState(prev => ({ ...prev, visible: false }));\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,EAAEC,OAAO,QAAQ,MAAM;AACtC,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,qBAAqB,GAAG,IAAI,CAAC,CAAE;AACnC,IAAIC,oBAAoB,GAAG,IAAI,CAAC,CAAG;AACnC,IAAIC,0BAA0B,GAAG,IAAI,CAAC,CAAC;AACvC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,YAAY,GAAG,IAAI;AACvB,MAAMC,KAAK,GAAG,GAAG,CAAC,CAAC;;AAEnB;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACV;EACAC,GAAG,EAAE,2BAA2B;EAChCC,GAAG,EAAE,2BAA2B;EAChCX,KAAK,EAAE,6BAA6B;EACpCY,GAAG,EAAE,2BAA2B;EAAG;EACnCC,IAAI,EAAE,4BAA4B,CAAE;AACtC,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC;AACA,IAAIC,gBAAgB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAC;;AAElC;AACA,IAAIE,gBAAgB,GAAG,IAAI;;AAE3B;AACA,IAAIC,gBAAgB,GAAG,IAAIH,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,IAAII,kBAAkB,GAAG,IAAIJ,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEpC;AACA,MAAMK,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EACxC,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGT,QAAQ,oBAAoB,CAAC;IAC7D,MAAMU,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;IAElC,IAAID,IAAI,IAAIA,IAAI,CAACE,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,EAAE;MACzD,MAAMG,WAAW,GAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,IAAI,CAAC;MACrE,IAAIH,WAAW,IAAIA,WAAW,CAACI,KAAK,EAAE;QACpCf,gBAAgB,GAAGW,WAAW,CAACI,KAAK;QACpCC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEjB,gBAAgB,CAAC;QAC7C,OAAOA,gBAAgB;MACzB;IACF;IACAgB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChCjB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACjClB,gBAAgB,GAAG,OAAO,CAAC,CAAC;IAC5B,OAAOA,gBAAgB;EACzB;AACF,CAAC;;AAED;AACA,MAAMmB,aAAa,GAAGA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,KAAK;EACpD,IAAID,SAAS,KAAK,IAAI,EAAE,OAAOD,QAAQ;EACvC,OAAOE,KAAK,GAAGF,QAAQ,GAAG,CAAC,CAAC,GAAGE,KAAK,IAAID,SAAS;AACnD,CAAC;;AAED;AACA,MAAME,cAAc,GAAIC,MAAM,IAAK;EACjC,IAAI,CAACzC,YAAY,EAAE;IACjBA,YAAY,GAAGyC,MAAM,CAACC,KAAK,CAAC,CAAC;IAC7B,OAAOD,MAAM;EACf;EAEA,MAAME,SAAS,GAAGP,aAAa,CAACK,MAAM,CAACG,CAAC,EAAE5C,YAAY,CAAC4C,CAAC,EAAE1C,KAAK,CAAC;EAChE,MAAM2C,SAAS,GAAGT,aAAa,CAACK,MAAM,CAACK,CAAC,EAAE9C,YAAY,CAAC8C,CAAC,EAAE5C,KAAK,CAAC;EAChE,MAAM6C,SAAS,GAAGX,aAAa,CAACK,MAAM,CAACO,CAAC,EAAEhD,YAAY,CAACgD,CAAC,EAAE9C,KAAK,CAAC;EAEhEF,YAAY,CAACiD,GAAG,CAACN,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC;EACjD,OAAO/C,YAAY,CAAC0C,KAAK,CAAC,CAAC;AAC7B,CAAC;;AAED;AACA,MAAMQ,cAAc,GAAIC,WAAW,IAAK;EACtC,IAAIlD,YAAY,KAAK,IAAI,EAAE;IACzBA,YAAY,GAAGkD,WAAW;IAC1B,OAAOA,WAAW;EACpB;;EAEA;EACA,IAAIC,IAAI,GAAGD,WAAW,GAAGlD,YAAY;EACrC,IAAImD,IAAI,GAAGC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EACvC,IAAIF,IAAI,GAAG,CAACC,IAAI,CAACC,EAAE,EAAEF,IAAI,IAAI,CAAC,GAAGC,IAAI,CAACC,EAAE;EAExC,MAAMC,gBAAgB,GAAGnB,aAAa,CAACnC,YAAY,GAAGmD,IAAI,EAAEnD,YAAY,EAAEC,KAAK,CAAC;EAChFD,YAAY,GAAGsD,gBAAgB;EAC/B,OAAOA,gBAAgB;AACzB,CAAC;AAED,MAAMC,WAAW,GAAGA,CAAC;EAAEC,SAAS;EAAEC,kBAAkB;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACvE,MAAMC,YAAY,GAAG3F,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM4F,UAAU,GAAG5F,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM6F,SAAS,GAAG7F,MAAM,CAAC,IAAIM,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMwF,aAAa,GAAG9F,MAAM,CAAC,EAAE,CAAC;EAChC,MAAM+F,eAAe,GAAG/F,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMgG,aAAa,GAAGhG,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMiG,iBAAiB,GAAGjG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAE;EACzC,MAAM,CAACkG,eAAe,EAAEC,kBAAkB,CAAC,GAAGlG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAACmG,YAAY,EAAEC,eAAe,CAAC,GAAGpG,QAAQ,CAAC;IAC/CqG,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG1G,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAM2G,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG7H,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAM,CAAC8H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9H,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAAC+H,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhI,QAAQ,CAAC;IAC7DiI,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,IAAI;IACbtB,QAAQ,EAAE;MAAEnC,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAE,CAAC;IACxBwD,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,uBAAuB,GAAG;IAC9BzB,QAAQ,EAAE,OAAO;IACjB0B,GAAG,EAAE,MAAM;IACXxB,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BwB,KAAK,EAAE,OAAO;IAAG;IACjBvB,MAAM,EAAE,IAAI;IACZK,eAAe,EAAE,OAAO;IACxBE,YAAY,EAAE,KAAK;IACnBG,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMc,UAAU,GAAG;IACjB5B,QAAQ,EAAE,OAAO;IACjB0B,GAAG,EAAE,MAAM;IACXxB,IAAI,EAAE,kBAAkB;IAAG;IAC3BC,SAAS,EAAE,mBAAmB;IAC9BK,OAAO,EAAE,OAAO;IAAG;IACnBqB,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE,MAAM;IACbjB,QAAQ,EAAE,MAAM;IAChBkB,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAE,2BAA2B;IACvC5B,MAAM,EAAE;EACV,CAAC;;EAED;EACA,MAAM,CAACjE,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,IAAI4C,GAAG,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACiG,UAAU,EAAEC,aAAa,CAAC,GAAG9I,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAAC6C,gBAAgB,EAAEkG,mBAAmB,CAAC,GAAG/I,QAAQ,CAAC,IAAI4C,GAAG,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACoG,WAAW,EAAEC,cAAc,CAAC,GAAGjJ,QAAQ,CAAC;IAAEkJ,KAAK,EAAE,EAAE;IAAEf,OAAO,EAAE;EAAG,CAAC,CAAC;;EAE1E;EACA,MAAMgB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BzC,WAAW,CAAC,QAAQ,CAAC;IACrBpF,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAAC6H,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B3C,WAAW,CAAC,QAAQ,CAAC;IACrBpF,UAAU,GAAG,QAAQ;IAErB,IAAIsG,SAAS,CAAC0B,OAAO,IAAI/H,QAAQ,EAAE;MACjC;MACA,MAAMgI,UAAU,GAAG3B,SAAS,CAAC0B,OAAO,CAAC1C,QAAQ,CAACrC,KAAK,CAAC,CAAC;MACrD,MAAMiF,SAAS,GAAG5B,SAAS,CAAC0B,OAAO,CAACG,EAAE,CAAClF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAIjE,KAAK,CAACoJ,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC;QAAElF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,GAAG;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChC+E,MAAM,CAACtJ,KAAK,CAACuJ,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdpC,SAAS,CAAC0B,OAAO,CAAC1C,QAAQ,CAACqD,IAAI,CAACV,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;MAEV;MACA,IAAI5J,KAAK,CAACoJ,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAElF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9B+E,MAAM,CAACtJ,KAAK,CAACuJ,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdpC,SAAS,CAAC0B,OAAO,CAACG,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC;MACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAG5I,QAAQ,CAAC6I,MAAM,CAAC7F,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAIjE,KAAK,CAACoJ,KAAK,CAACS,aAAa,CAAC,CAC3BR,EAAE,CAAC;QAAElF,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE,CAAC;QAAEE,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9B+E,MAAM,CAACtJ,KAAK,CAACuJ,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdzI,QAAQ,CAAC6I,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACAvC,SAAS,CAAC0B,OAAO,CAACe,MAAM,CAAC9I,QAAQ,CAAC6I,MAAM,CAAC;QACzC7I,QAAQ,CAAC+I,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACA3I,QAAQ,CAAC6H,OAAO,GAAG,IAAI;;MAEvB;MACA7H,QAAQ,CAACgJ,WAAW,GAAG,EAAE;MACzBhJ,QAAQ,CAACiJ,WAAW,GAAG,GAAG;MAC1BjJ,QAAQ,CAACkJ,aAAa,GAAGvF,IAAI,CAACC,EAAE,GAAG,GAAG;MACtC5D,QAAQ,CAACmJ,aAAa,GAAG,CAAC;MAC1BnJ,QAAQ,CAAC+I,MAAM,CAAC,CAAC;MAEjBxG,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrB4G,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,YAAY,GAAGtK,iBAAiB,CAACuK,aAAa,CAACvH,IAAI,CAACwH,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAKJ,KAAK,CAAC;IAChF,IAAIC,YAAY,IAAIpD,SAAS,CAAC0B,OAAO,IAAI/H,QAAQ,EAAE;MACjDuG,uBAAuB,CAACkD,YAAY,CAAC;;MAErC;MACA,MAAMI,WAAW,GAAGxF,SAAS,CAAC0D,OAAO,CAAC+B,YAAY,CAChDC,UAAU,CAACN,YAAY,CAAC3E,SAAS,CAAC,EAClCiF,UAAU,CAACN,YAAY,CAAC1E,QAAQ,CAClC,CAAC;MAEDxC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvBwH,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBK,GAAG,EAAE;UACHnF,SAAS,EAAE2E,YAAY,CAAC3E,SAAS;UACjCC,QAAQ,EAAE0E,YAAY,CAAC1E;QACzB,CAAC;QACDmF,IAAI,EAAEL;MACR,CAAC,CAAC;;MAEF;MACA9J,UAAU,GAAG,cAAc;MAC3BoF,WAAW,CAAC,cAAc,CAAC;;MAE3B;MACAkB,SAAS,CAAC0B,OAAO,CAAC1C,QAAQ,CAAC9B,GAAG,CAACsG,WAAW,CAAC3G,CAAC,EAAE,GAAG,EAAE,CAAC2G,WAAW,CAACzG,CAAC,CAAC;;MAElE;MACApD,QAAQ,CAAC6I,MAAM,CAACtF,GAAG,CAACsG,WAAW,CAAC3G,CAAC,EAAE,CAAC,EAAE,CAAC2G,WAAW,CAACzG,CAAC,CAAC;;MAErD;MACAiD,SAAS,CAAC0B,OAAO,CAACe,MAAM,CAAC9I,QAAQ,CAAC6I,MAAM,CAAC;;MAEzC;MACA7I,QAAQ,CAAC6H,OAAO,GAAG,IAAI;MACvB7H,QAAQ,CAAC+I,MAAM,CAAC,CAAC;;MAEjB;MACA1C,SAAS,CAAC0B,OAAO,CAACoC,YAAY,CAAC,CAAC;MAChC9D,SAAS,CAAC0B,OAAO,CAACqC,iBAAiB,CAAC,IAAI,CAAC;MAEzC7H,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBwH,IAAI,EAAEP,YAAY,CAACG,IAAI;QACvBS,IAAI,EAAEhE,SAAS,CAAC0B,OAAO,CAAC1C,QAAQ,CAACiF,OAAO,CAAC,CAAC;QAC1CC,GAAG,EAAEvK,QAAQ,CAAC6I,MAAM,CAACyB,OAAO,CAAC,CAAC;QAC9BJ,IAAI,EAAEL;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMW,iBAAiB,GAAGA,CAAC7C,KAAK,EAAE8C,OAAO,KAAK;IAC5C,IAAI;MACF,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;;MAEnC;MACA,IAAI9C,KAAK,KAAKlH,WAAW,CAACO,GAAG,EAAE;QAAA,IAAA6J,aAAA;QAC7BtI,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEkI,OAAO,CAAC;;QAEhC;QACA,MAAMI,SAAS,GAAGJ,OAAO,CAACK,GAAG;QAC7B,MAAMC,gBAAgB,GAAGN,OAAO,CAACO,EAAE;;QAEnC;QACA,MAAMC,aAAa,GAAG5J,gBAAgB,CAAC6J,GAAG,CAACL,SAAS,CAAC;;QAErD;QACA,IAAII,aAAa,IAAIF,gBAAgB,GAAGE,aAAa,EAAE;UACrD3I,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;YACzB4I,KAAK,EAAEN,SAAS;YAChBO,KAAK,EAAEL,gBAAgB;YACvBM,KAAK,EAAEJ;UACT,CAAC,CAAC;UACF;QACF;;QAEA;QACA5J,gBAAgB,CAACiC,GAAG,CAACuH,SAAS,EAAEE,gBAAgB,CAAC;QAEjDzI,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;UACzB4I,KAAK,EAAEN,SAAS;UAChBS,GAAG,EAAEP,gBAAgB;UACrBQ,KAAK,EAAE,CAACN,aAAa,IAAIF,gBAAgB,IAAIE;QAC/C,CAAC,CAAC;QAEF,MAAMO,YAAY,GAAG,EAAAZ,aAAA,GAAAH,OAAO,CAAC7I,IAAI,cAAAgJ,aAAA,uBAAZA,aAAA,CAAcY,YAAY,KAAI,EAAE;QACrD,MAAMC,KAAK,GAAGhB,OAAO,CAAC7I,IAAI,CAAC6J,KAAK;;QAEhC;QACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;QAEtB;QACAF,YAAY,CAACI,OAAO,CAACC,WAAW,IAAI;UAClC;UACA,MAAMC,EAAE,GAAIL,KAAK,GAAGI,WAAW,CAACE,SAAS;UACzC,MAAMC,IAAI,GAAGH,WAAW,CAACI,WAAW;UAEpC,IAAGD,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,IAAEA,IAAI,KAAK,GAAG,EAAC;YAC5C;YACA;YACA;YACA,MAAME,KAAK,GAAG;cACZrH,SAAS,EAAEiF,UAAU,CAAC+B,WAAW,CAACM,WAAW,CAAC;cAC9CrH,QAAQ,EAAEgF,UAAU,CAAC+B,WAAW,CAACO,UAAU,CAAC;cAC5CrH,KAAK,EAAE+E,UAAU,CAAC+B,WAAW,CAACQ,SAAS,CAAC;cACxCrH,OAAO,EAAE8E,UAAU,CAAC+B,WAAW,CAACS,WAAW;YAC7C,CAAC;YAED,MAAMC,QAAQ,GAAGnI,SAAS,CAAC0D,OAAO,CAAC+B,YAAY,CAACqC,KAAK,CAACrH,SAAS,EAAEqH,KAAK,CAACpH,QAAQ,CAAC;;YAEhF;YACA,IAAI0H,cAAc;YAClB,QAAQR,IAAI;cACV,KAAK,GAAG;gBAAE;gBACRQ,cAAc,GAAGxM,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRwM,cAAc,GAAGvM,qBAAqB;gBACtC;cACF,KAAK,GAAG;gBAAE;gBACRuM,cAAc,GAAGtM,oBAAoB;gBACrC;cACF;gBACE;cAAQ;YACZ;;YAEA;YACA,IAAIuM,KAAK,GAAGtL,aAAa,CAAC+J,GAAG,CAACY,EAAE,CAAC;YAEjC,IAAI,CAACW,KAAK,IAAID,cAAc,EAAE;cAC5B;cACA,MAAME,QAAQ,GAAGF,cAAc,CAACzJ,KAAK,CAAC,CAAC;cACvC;cACA,MAAM4J,MAAM,GAAGX,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;cACzCU,QAAQ,CAACtH,QAAQ,CAAC9B,GAAG,CAACiJ,QAAQ,CAACtJ,CAAC,EAAE0J,MAAM,EAAE,CAACJ,QAAQ,CAACpJ,CAAC,CAAC;cACtDuJ,QAAQ,CAACE,QAAQ,CAACzJ,CAAC,GAAGO,IAAI,CAACC,EAAE,GAAGuI,KAAK,CAAClH,OAAO,GAAGtB,IAAI,CAACC,EAAE,GAAG,GAAG;cAC7DvD,KAAK,CAACyM,GAAG,CAACH,QAAQ,CAAC;cAEnBvL,aAAa,CAACmC,GAAG,CAACwI,EAAE,EAAE;gBACpBW,KAAK,EAAEC,QAAQ;gBACfI,UAAU,EAAEpB,GAAG;gBACfM,IAAI,EAAEA;cACR,CAAC,CAAC;YACJ,CAAC,MAAM,IAAIS,KAAK,EAAE;cAChB;cACAA,KAAK,CAACA,KAAK,CAACrH,QAAQ,CAAC9B,GAAG,CAACiJ,QAAQ,CAACtJ,CAAC,EAAEwJ,KAAK,CAACT,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAACO,QAAQ,CAACpJ,CAAC,CAAC;cACjFsJ,KAAK,CAACA,KAAK,CAACG,QAAQ,CAACzJ,CAAC,GAAGO,IAAI,CAACC,EAAE,GAAGuI,KAAK,CAAClH,OAAO,GAAGtB,IAAI,CAACC,EAAE,GAAG,GAAG;cAChE8I,KAAK,CAACK,UAAU,GAAGpB,GAAG;cACtBe,KAAK,CAACA,KAAK,CAACvC,YAAY,CAAC,CAAC;cAC1BuC,KAAK,CAACA,KAAK,CAACtC,iBAAiB,CAAC,IAAI,CAAC;YACrC;UACA;QACF,CAAC,CAAC;;QAEF;QACA,MAAM4C,iBAAiB,GAAG,IAAI;QAC9B,MAAMC,UAAU,GAAG,IAAIC,GAAG,CAACzB,YAAY,CAAC0B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACpB,SAAS,CAAC,CAAC;QAE9D5K,aAAa,CAACyK,OAAO,CAAC,CAACwB,SAAS,EAAEtB,EAAE,KAAK;UACvC,IAAIJ,GAAG,GAAG0B,SAAS,CAACN,UAAU,GAAGC,iBAAiB,IAAI,CAACC,UAAU,CAACK,GAAG,CAACvB,EAAE,CAAC,EAAE;YACzE1L,KAAK,CAACkN,MAAM,CAACF,SAAS,CAACX,KAAK,CAAC;YAC7BtL,aAAa,CAACoM,MAAM,CAACzB,EAAE,CAAC;YACxBxJ,OAAO,CAACC,GAAG,CAAC,oBAAoBuJ,EAAE,QAAQsB,SAAS,CAACpB,IAAI,EAAE,CAAC;UAC7D;QACF,CAAC,CAAC;QACF;MACF;;MAEA;MACA,IAAItE,KAAK,KAAKlH,WAAW,CAACM,GAAG,EAAE;QAC7BwB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEkI,OAAO,CAAC;QAEhC,MAAM+C,OAAO,GAAG/C,OAAO,CAAC7I,IAAI;QAC5B,MAAM6L,KAAK,GAAGD,OAAO,CAACnL,KAAK;QAC3B,MAAMqL,QAAQ,GAAG;UACf7I,SAAS,EAAEiF,UAAU,CAAC0D,OAAO,CAACG,QAAQ,CAAC;UACvC7I,QAAQ,EAAEgF,UAAU,CAAC0D,OAAO,CAACI,OAAO,CAAC;UACrC7I,KAAK,EAAE+E,UAAU,CAAC0D,OAAO,CAACnB,SAAS,CAAC;UACpCrH,OAAO,EAAE8E,UAAU,CAAC0D,OAAO,CAAClB,WAAW;QACzC,CAAC;QAEDhK,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEmL,QAAQ,CAAC;QAClCpL,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEkL,KAAK,CAAC;;QAE3B;QACA,MAAMlB,QAAQ,GAAGnI,SAAS,CAAC0D,OAAO,CAAC+B,YAAY,CAAC6D,QAAQ,CAAC7I,SAAS,EAAE6I,QAAQ,CAAC5I,QAAQ,CAAC;QACtF,MAAM+I,WAAW,GAAG,IAAInP,KAAK,CAACoP,OAAO,CAACvB,QAAQ,CAACtJ,CAAC,EAAE,GAAG,EAAE,CAACsJ,QAAQ,CAACpJ,CAAC,CAAC;QACnE,MAAMK,WAAW,GAAGE,IAAI,CAACC,EAAE,GAAG+J,QAAQ,CAAC1I,OAAO,GAAGtB,IAAI,CAACC,EAAE,GAAG,GAAG;;QAE9D;QACA,IAAIoK,UAAU,GAAG5M,aAAa,CAAC+J,GAAG,CAACuC,KAAK,CAAC;;QAEzC;QACA,MAAMrL,aAAa,GAAGqL,KAAK,KAAKnM,gBAAgB;QAEhD,IAAI,CAACyM,UAAU,IAAI/N,qBAAqB,EAAE;UACxC;UACA,MAAMgO,eAAe,GAAGhO,qBAAqB,CAAC+C,KAAK,CAAC,CAAC;UACrDiL,eAAe,CAAC5I,QAAQ,CAACqD,IAAI,CAACoF,WAAW,CAAC;UAC1CG,eAAe,CAACpB,QAAQ,CAACzJ,CAAC,GAAGK,WAAW;UACxCpD,KAAK,CAACyM,GAAG,CAACmB,eAAe,CAAC;;UAE1B;UACA7M,aAAa,CAACmC,GAAG,CAACmK,KAAK,EAAE;YACvBhB,KAAK,EAAEuB,eAAe;YACtBlB,UAAU,EAAEnB,IAAI,CAACD,GAAG,CAAC,CAAC;YACtBM,IAAI,EAAE,GAAG;YAAE;YACXiC,MAAM,EAAE7L;UACV,CAAC,CAAC;UAEFE,OAAO,CAACC,GAAG,CAAC,aAAakL,KAAK,SAASI,WAAW,CAAC5K,CAAC,CAACiL,OAAO,CAAC,CAAC,CAAC,KAAKL,WAAW,CAAC1K,CAAC,CAAC+K,OAAO,CAAC,CAAC,CAAC,KAAKL,WAAW,CAACxK,CAAC,CAAC6K,OAAO,CAAC,CAAC,CAAC,YAAY9L,aAAa,EAAE,CAAC;;UAErJ;UACA,IAAIA,aAAa,EAAE;YACjB7C,gBAAgB,GAAGyO,eAAe;YAClCpJ,eAAe,CAAC8I,QAAQ,CAAC;YACzBpL,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEkL,KAAK,CAAC;UACjC;QACF,CAAC,MAAM,IAAIM,UAAU,EAAE;UACrB;UACA,MAAMI,gBAAgB,GAAGtL,cAAc,CAACgL,WAAW,CAAC;UACpD,MAAMjK,gBAAgB,GAAGL,cAAc,CAACC,WAAW,CAAC;;UAEpD;UACAuK,UAAU,CAACtB,KAAK,CAACrH,QAAQ,CAACqD,IAAI,CAAC0F,gBAAgB,CAAC;UAChDJ,UAAU,CAACtB,KAAK,CAACG,QAAQ,CAACzJ,CAAC,GAAGS,gBAAgB;UAC9CmK,UAAU,CAACtB,KAAK,CAACvC,YAAY,CAAC,CAAC;UAC/B6D,UAAU,CAACtB,KAAK,CAACtC,iBAAiB,CAAC,IAAI,CAAC;UACxC4D,UAAU,CAACjB,UAAU,GAAGnB,IAAI,CAACD,GAAG,CAAC,CAAC;UAClCqC,UAAU,CAACE,MAAM,GAAG7L,aAAa,CAAC,CAAC;;UAEnCE,OAAO,CAACC,GAAG,CAAC,cAAckL,KAAK,SAASU,gBAAgB,CAAClL,CAAC,CAACiL,OAAO,CAAC,CAAC,CAAC,KAAKC,gBAAgB,CAAChL,CAAC,CAAC+K,OAAO,CAAC,CAAC,CAAC,KAAKC,gBAAgB,CAAC9K,CAAC,CAAC6K,OAAO,CAAC,CAAC,CAAC,YAAY9L,aAAa,EAAE,CAAC;;UAErK;UACA,IAAIA,aAAa,EAAE;YACjB7C,gBAAgB,GAAGwO,UAAU,CAACtB,KAAK;YACnC7H,eAAe,CAAC8I,QAAQ,CAAC;UAC3B;QACF;;QAEA;QACA,MAAMhC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;QACtB,MAAMqB,iBAAiB,GAAG,IAAI,CAAC,CAAC;;QAEhC5L,aAAa,CAACyK,OAAO,CAAC,CAACwB,SAAS,EAAEtB,EAAE,KAAK;UACvC,IAAIJ,GAAG,GAAG0B,SAAS,CAACN,UAAU,GAAGC,iBAAiB,EAAE;YAClD3M,KAAK,CAACkN,MAAM,CAACF,SAAS,CAACX,KAAK,CAAC;YAC7BtL,aAAa,CAACoM,MAAM,CAACzB,EAAE,CAAC;YACxBxJ,OAAO,CAACC,GAAG,CAAC,mBAAmBuJ,EAAE,EAAE,CAAC;UACtC;QACF,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAIpE,KAAK,KAAKlH,WAAW,CAACS,IAAI,EAAE;QAC9BqB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEkI,OAAO,CAAC;QACjC;QACA,IAAIA,OAAO,CAAChB,aAAa,IAAIgB,OAAO,CAAChB,aAAa,CAAC2E,MAAM,GAAG,CAAC,EAAE;UAC7D;UACA3D,OAAO,CAAChB,aAAa,CAACmC,OAAO,CAACpC,YAAY,IAAI;YAC5C,MAAM9C,OAAO,GAAG8C,YAAY,CAACsC,EAAE;YAC/B;YACA,MAAMuC,YAAY,GAAG9M,gBAAgB,CAAC2J,GAAG,CAACxE,OAAO,CAAC;;YAElD;YACA,IAAI8C,YAAY,CAAC5C,MAAM,IAAI4C,YAAY,CAAC5C,MAAM,CAACwH,MAAM,GAAG,CAAC,EAAE;cACzD5M,kBAAkB,CAAC8B,GAAG,CAACoD,OAAO,EAAE;gBAC9BE,MAAM,EAAE4C,YAAY,CAAC5C,MAAM,CAACsG,GAAG,CAACoB,KAAK,KAAK;kBACxCC,OAAO,EAAED,KAAK,CAACC,OAAO;kBACtBF,YAAY,EAAEC,KAAK,CAACpC,KAAK,CAACsC,WAAW,CAAC,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,GAC/CH,KAAK,CAACpC,KAAK,CAACsC,WAAW,CAAC,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;kBACnEC,UAAU,EAAEJ,KAAK,CAACI,UAAU,IAAIhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;gBACrE,CAAC,CAAC;cACJ,CAAC,CAAC;cAEFtM,OAAO,CAACC,GAAG,CAAC,QAAQmE,OAAO,YAAY,EAAElF,kBAAkB,CAAC0J,GAAG,CAACxE,OAAO,CAAC,CAAC;YAC3E;YAEA,IAAI2H,YAAY,IAAI7E,YAAY,CAAC5C,MAAM,EAAE;cACvC;cACA,MAAM0H,KAAK,GAAG9E,YAAY,CAAC5C,MAAM,CAAC,CAAC,CAAC;cACpC,IAAI0H,KAAK,IAAIA,KAAK,CAACpC,KAAK,EAAE;gBACxB;gBACA,MAAM2C,iBAAiB,GAAGR,YAAY,CAAC5B,KAAK;;gBAE5C;gBACA,IAAIoC,iBAAiB,CAACC,QAAQ,CAACV,MAAM,GAAG,CAAC,EAAE;kBACzCS,iBAAiB,CAACC,QAAQ,CAAClD,OAAO,CAACmD,KAAK,IAAI;oBAC1CF,iBAAiB,CAACvB,MAAM,CAACyB,KAAK,CAAC;kBACjC,CAAC,CAAC;gBACJ;;gBAEA;gBACA,IAAIC,UAAU;gBACd,QAAOV,KAAK,CAACpC,KAAK,CAACsC,WAAW,CAAC,CAAC;kBAC9B,KAAK,OAAO;oBACVQ,UAAU,GAAG,QAAQ,CAAC,CAAC;oBACvB;kBACF,KAAK,QAAQ;oBACXA,UAAU,GAAG,QAAQ,CAAC,CAAC;oBACvB;kBACF,KAAK,KAAK;oBACRA,UAAU,GAAG,QAAQ,CAAC,CAAC;oBACvB;kBACF;oBACEA,UAAU,GAAG,QAAQ;kBAAE;gBAC3B;;gBAEA;gBACA,MAAMC,aAAa,GAAG,IAAIvQ,KAAK,CAACwQ,cAAc,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;gBACzD,MAAMC,aAAa,GAAG,IAAIzQ,KAAK,CAAC0Q,iBAAiB,CAAC;kBAChDlI,KAAK,EAAE8H,UAAU;kBACjBK,QAAQ,EAAEL,UAAU;kBACpBM,iBAAiB,EAAE;gBACrB,CAAC,CAAC;gBACF,MAAMC,SAAS,GAAG,IAAI7Q,KAAK,CAAC8Q,IAAI,CAACP,aAAa,EAAEE,aAAa,CAAC;gBAC9DI,SAAS,CAACnK,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;gBAElC;gBACA,MAAMmM,KAAK,GAAG,IAAI/Q,KAAK,CAACgR,UAAU,CAACV,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;gBACrDS,KAAK,CAACrK,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;gBAE5B;gBACAuL,iBAAiB,CAAChC,GAAG,CAAC0C,SAAS,CAAC;gBAChCV,iBAAiB,CAAChC,GAAG,CAAC4C,KAAK,CAAC;gBAE5BnN,OAAO,CAACC,GAAG,CAAC,QAAQ8L,YAAY,CAAC7E,YAAY,CAACG,IAAI,KAAKjD,OAAO,cAAc4H,KAAK,CAACpC,KAAK,EAAE,CAAC;cAC5F;YACF;UACF,CAAC,CAAC;QACJ;;QAEA;QACAzE,cAAc,CAAC;UACbC,KAAK,EAAE,OAAO;UACdf,OAAO,EAAE,sBAAsB8D,OAAO,CAAChB,aAAa,GAAGgB,OAAO,CAAChB,aAAa,CAAC2E,MAAM,GAAG,CAAC;QACzF,CAAC,CAAC;;QAEF;QACA1N,MAAM,CAACiP,WAAW,CAAC;UACjB3D,IAAI,EAAE,MAAM;UACZpK,IAAI,EAAE6I;QACR,CAAC,EAAE,GAAG,CAAC;QAEP;MACF;;MAEA;MACA,IAAI/C,KAAK,KAAKlH,WAAW,CAACQ,GAAG,IAAIyJ,OAAO,CAACuB,IAAI,KAAK,KAAK,EAAE;QACvD1J,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEkI,OAAO,CAAC;;QAEhC;QACA/J,MAAM,CAACiP,WAAW,CAAC;UACjB3D,IAAI,EAAE,KAAK;UACXpK,IAAI,EAAE6I,OAAO,CAAC7I;QAChB,CAAC,EAAE,GAAG,CAAC;QAEP,MAAMgO,OAAO,GAAGnF,OAAO,CAAC7I,IAAI;QAC5B,MAAMiO,KAAK,GAAGD,OAAO,CAACC,KAAK;QAC3B,MAAMC,MAAM,GAAGF,OAAO,CAACG,IAAI,IAAI,EAAE;QAEjCD,MAAM,CAAClE,OAAO,CAACoE,KAAK,IAAI;UACtB,MAAMC,OAAO,GAAGD,KAAK,CAACE,KAAK;UAC3B,MAAMC,SAAS,GAAGH,KAAK,CAACG,SAAS;UACjC,MAAMC,WAAW,GAAGJ,KAAK,CAACI,WAAW;UACrC,MAAMC,SAAS,GAAGL,KAAK,CAACK,SAAS;UACjC,MAAMC,OAAO,GAAGN,KAAK,CAACM,OAAO;;UAE7B;UACA,MAAM/D,QAAQ,GAAGnI,SAAS,CAAC0D,OAAO,CAAC+B,YAAY,CAC7CC,UAAU,CAAC8F,OAAO,CAACW,OAAO,CAAC,EAC3BzG,UAAU,CAAC8F,OAAO,CAACY,MAAM,CAC3B,CAAC;;UAED;UACA,IAAIC,WAAW,GAAG,EAAE;UACpB,IAAIC,YAAY,GAAG,EAAE;UAErB,QAAOP,SAAS;YACd,KAAK,KAAK;cAAG;cACXM,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,OAAO;cACrBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,QAAQ;cACtBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,MAAM;cAAE;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF,KAAK,KAAK;cAAG;cACXD,WAAW,GAAG,MAAM;cACpBC,YAAY,GAAG,SAAS;cACxB;YACF;cACED,WAAW,GAAGL,WAAW,IAAI,MAAM;cACnCM,YAAY,GAAG,SAAS;UAC5B;;UAEA;UACAC,iBAAiB,CAACpE,QAAQ,EAAEkE,WAAW,EAAEC,YAAY,CAAC;UAEtDpO,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;YACtBqO,IAAI,EAAEX,OAAO;YACbY,IAAI,EAAEV,SAAS;YACfW,IAAI,EAAEV,WAAW;YACjBW,IAAI,EAAEV,SAAS;YACfW,IAAI,EAAEV,OAAO;YACbW,EAAE,EAAE1E;UACN,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF;MACF;;MAEA;MACA,IAAI7E,KAAK,KAAKlH,WAAW,CAACJ,KAAK,IAAIqK,OAAO,CAACuB,IAAI,KAAK,OAAO,EAAE;QAC3D1J,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEkI,OAAO,CAAC;QAEjC,MAAMyG,SAAS,GAAGzG,OAAO,CAAC7I,IAAI;QAC9B,MAAMuP,OAAO,GAAGD,SAAS,CAACC,OAAO;QACjC,MAAMC,SAAS,GAAGF,SAAS,CAACE,SAAS;QACrC,MAAMC,SAAS,GAAGH,SAAS,CAACG,SAAS;QACrC,MAAMjM,QAAQ,GAAG;UACfN,QAAQ,EAAEgF,UAAU,CAACoH,SAAS,CAACtD,OAAO,CAAC;UACvC/I,SAAS,EAAEiF,UAAU,CAACoH,SAAS,CAACvD,QAAQ;QAC1C,CAAC;;QAED;QACA,MAAMpB,QAAQ,GAAGnI,SAAS,CAAC0D,OAAO,CAAC+B,YAAY,CAACzE,QAAQ,CAACP,SAAS,EAAEO,QAAQ,CAACN,QAAQ,CAAC;;QAEtF;QACA,QAAOsM,SAAS;UACd,KAAK,GAAG;YAAG;YACTT,iBAAiB,CAACpE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;YAClD;UACF,KAAK,KAAK;YAAG;YACXoE,iBAAiB,CAACpE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACXoE,iBAAiB,CAACpE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;YAC/C;UACF,KAAK,IAAI;YAAG;YACV,MAAM+E,UAAU,GAAGJ,SAAS,CAACK,UAAU,CAAC,CAAE;YAC1CZ,iBAAiB,CAACpE,QAAQ,EAAE,KAAK+E,UAAU,MAAM,EAAE,SAAS,CAAC;YAC7D;UACF,KAAK,IAAI;YAAG;YACVX,iBAAiB,CAACpE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVoE,iBAAiB,CAACpE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,MAAM;YAAG;YACZoE,iBAAiB,CAACpE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVoE,iBAAiB,CAACpE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,IAAI;YAAG;YACVoE,iBAAiB,CAACpE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;YAC9C;UACF,KAAK,KAAK;YAAG;YACX,MAAMiF,YAAY,GAAGN,SAAS,CAACK,UAAU,CAAC,CAAE;YAC5C,MAAME,QAAQ,GAAGP,SAAS,CAACQ,UAAU,CAAC,CAAM;YAC5Cf,iBAAiB,CAACpE,QAAQ,EAAE,QAAQoF,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,EAAE,SAAS,CAAC;YAC/F;QACJ;QAEA;MACF;MACA;MACAnP,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBmF,KAAK;QACLsE,IAAI,EAAEvB,OAAO,CAACuB,IAAI;QAClBpK,IAAI,EAAE6I;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOjI,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEgI,OAAO,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMoH,cAAc,GAAGA,CAAA,KAAM;IAC3BtP,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAMsP,KAAK,GAAG,QAAQrR,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnEyB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEsP,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChB1P,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAEDuP,EAAE,CAACG,SAAS,GAAIjC,KAAK,IAAK;MACxB,IAAI;QACF,MAAMxF,OAAO,GAAGE,IAAI,CAACC,KAAK,CAACqF,KAAK,CAACpO,IAAI,CAAC;;QAEtC;QACA,IAAI4I,OAAO,CAACwB,IAAI,KAAK,SAAS,EAAE;UAC9B1J,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEiI,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACwB,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIxB,OAAO,CAACwB,IAAI,KAAK,SAAS,IAAIxB,OAAO,CAAC9C,KAAK,IAAI8C,OAAO,CAACC,OAAO,EAAE;UAClE;UACAF,iBAAiB,CAACC,OAAO,CAAC9C,KAAK,EAAEgD,IAAI,CAACwH,SAAS,CAAC1H,OAAO,CAACC,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAOjI,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDsP,EAAE,CAACK,OAAO,GAAI3P,KAAK,IAAK;MACtBF,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDsP,EAAE,CAACM,OAAO,GAAG,MAAM;MACjB9P,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACA8P,UAAU,CAACT,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACArN,aAAa,CAACuD,OAAO,GAAGgK,EAAE;EAC5B,CAAC;EAEDxT,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4F,YAAY,CAAC4D,OAAO,EAAE;;IAE3B;IACAwK,aAAa,CAAC,CAAC;;IAEf;IACAlS,KAAK,GAAG,IAAI1B,KAAK,CAAC6T,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAI9T,KAAK,CAAC+T,iBAAiB,CACxC,EAAE,EACF/R,MAAM,CAACgS,UAAU,GAAGhS,MAAM,CAACiS,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACD;IACAH,MAAM,CAACpN,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCkP,MAAM,CAAC3J,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBzC,SAAS,CAAC0B,OAAO,GAAG0K,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAIlU,KAAK,CAACmU,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAACrS,MAAM,CAACgS,UAAU,EAAEhS,MAAM,CAACiS,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAACvS,MAAM,CAACwS,gBAAgB,CAAC;IAC/ChP,YAAY,CAAC4D,OAAO,CAACqL,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAI3U,KAAK,CAAC4U,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5DlT,KAAK,CAACyM,GAAG,CAACwG,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAI7U,KAAK,CAAC8U,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAACnO,QAAQ,CAAC9B,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1ClD,KAAK,CAACyM,GAAG,CAAC0G,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAI/U,KAAK,CAAC8U,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAACrO,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3ClD,KAAK,CAACyM,GAAG,CAAC4G,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAIhV,KAAK,CAACiV,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAACtO,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCoQ,SAAS,CAACE,KAAK,GAAGlQ,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7B+P,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxB3T,KAAK,CAACyM,GAAG,CAAC6G,SAAS,CAAC;;IAEpB;IACA3T,QAAQ,GAAG,IAAInB,aAAa,CAAC4T,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzDrT,QAAQ,CAACiU,aAAa,GAAG,IAAI;IAC7BjU,QAAQ,CAACkU,aAAa,GAAG,IAAI;IAC7BlU,QAAQ,CAACmU,kBAAkB,GAAG,KAAK;IACnCnU,QAAQ,CAACgJ,WAAW,GAAG,EAAE;IACzBhJ,QAAQ,CAACiJ,WAAW,GAAG,GAAG;IAC1BjJ,QAAQ,CAACkJ,aAAa,GAAGvF,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC5D,QAAQ,CAACmJ,aAAa,GAAG,CAAC;IAC1BnJ,QAAQ,CAAC6I,MAAM,CAACtF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BvD,QAAQ,CAAC+I,MAAM,CAAC,CAAC;;IAEjB;IACAxG,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnBiQ,MAAM,EAAE,CAAC,CAACA,MAAM;MAChBzS,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBqG,SAAS,EAAE,CAAC,CAACA,SAAS,CAAC0B;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMqM,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAI5V,UAAU,CAAC,CAAC;QACtC4V,aAAa,CAACC,IAAI,CAChB,GAAGtT,QAAQ,uBAAuB,EACjCuT,IAAI,IAAK;UACR,MAAMC,YAAY,GAAGD,IAAI,CAACrU,KAAK;;UAE/B;UACA,MAAMuU,gBAAgB,GAAG,IAAIjW,KAAK,CAACkW,KAAK,CAAC,CAAC;;UAE1C;UACAF,YAAY,CAACG,QAAQ,CAAE9F,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAAC+F,MAAM,EAAE;cAChB;cACA,IAAI/F,KAAK,CAACgG,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAItW,KAAK,CAACuW,oBAAoB,CAAC;kBACjD/N,KAAK,EAAE,QAAQ;kBAAO;kBACtBgO,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIrG,KAAK,CAACgG,QAAQ,CAAC7H,GAAG,EAAE;kBACtB8H,WAAW,CAAC9H,GAAG,GAAG6B,KAAK,CAACgG,QAAQ,CAAC7H,GAAG;gBACtC;;gBAEA;gBACA6B,KAAK,CAACgG,QAAQ,GAAGC,WAAW;gBAE5B1S,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEwM,KAAK,CAACpF,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAM+K,YAAY,CAAC5F,QAAQ,CAACV,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMW,KAAK,GAAG2F,YAAY,CAAC5F,QAAQ,CAAC,CAAC,CAAC;YACtC6F,gBAAgB,CAAC9H,GAAG,CAACkC,KAAK,CAAC;UAC7B;;UAEA;UACA3O,KAAK,CAACyM,GAAG,CAAC8H,gBAAgB,CAAC;;UAE3B;UACApV,gBAAgB,GAAGoV,gBAAgB;UAEnCrS,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9BmC,kBAAkB,CAAC,IAAI,CAAC;UACxB2P,OAAO,CAACM,gBAAgB,CAAC;QAC3B,CAAC,EACAU,GAAG,IAAK;UACP/S,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC8S,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAErH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDoG,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMkB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMb,gBAAgB,GAAG,MAAMR,gBAAgB,CAAC,CAAC;;QAEjD;QACAvC,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAI+C,gBAAgB,EAAE;UACpB,MAAMc,YAAY,GAAG;YACnB5Q,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UAED,MAAM0Q,UAAU,GAAGtR,SAAS,CAAC0D,OAAO,CAAC+B,YAAY,CAAC4L,YAAY,CAAC5Q,SAAS,EAAE4Q,YAAY,CAAC3Q,QAAQ,CAAC;UAChG;UACA6P,gBAAgB,CAACvP,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACxCqR,gBAAgB,CAAC/H,QAAQ,CAACzJ,CAAC,GAAIO,IAAI,CAACC,EAAE,GAAG8R,YAAY,CAACzQ,OAAO,GAAGtB,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9EgR,gBAAgB,CAACzK,YAAY,CAAC,CAAC;UAC/ByK,gBAAgB,CAACxK,iBAAiB,CAAC,IAAI,CAAC;UACxCvK,eAAe,GAAG+U,gBAAgB,CAACvP,QAAQ,CAACrC,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAMmT,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAIzB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMwB,WAAW,GAAIC,WAAW,IAAK;UACnCzT,OAAO,CAACC,GAAG,CAAC,WAAWqT,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAIrX,UAAU,CAAC,CAAC;UAC/BqX,MAAM,CAACxB,IAAI,CACToB,GAAG,EACFnB,IAAI,IAAK;YACRnS,OAAO,CAACC,GAAG,CAAC,WAAWqT,GAAG,EAAE,CAAC;YAC7BvB,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAY,GAAG,IAAK;YACP/S,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC8S,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAErH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACA1L,KAAK,IAAK;YACTF,OAAO,CAACE,KAAK,CAAC,SAASoT,GAAG,EAAE,EAAEpT,KAAK,CAAC;YACpC,IAAIuT,WAAW,GAAG,CAAC,EAAE;cACnBzT,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3B8P,UAAU,CAAC,MAAMyD,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACLzB,MAAM,CAAC9R,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAEDsT,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAIrX,UAAU,CAAC,CAAC;IAC/BqX,MAAM,CAACxB,IAAI,CACT,GAAGtT,QAAQ,4BAA4B,EACvC,MAAOuT,IAAI,IAAK;MACd,IAAI;QACF,MAAMhI,KAAK,GAAGgI,IAAI,CAACrU,KAAK;QACxBqM,KAAK,CAACwJ,KAAK,CAAC3S,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBmJ,KAAK,CAACrH,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAE3B;QACA,IAAIlD,KAAK,EAAE;UACTA,KAAK,CAACyM,GAAG,CAACJ,KAAK,CAAC;;UAEhB;UACA,MAAM+I,eAAe,CAAC,CAAC;QACzB,CAAC,MAAM;UACLlT,OAAO,CAACE,KAAK,CAAC,eAAe,CAAC;QAChC;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACA6S,GAAG,IAAK;MACP/S,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC8S,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAErH,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACA1L,KAAK,IAAK;MACTF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BF,OAAO,CAACE,KAAK,CAAC,OAAO,EAAE;QACrB0T,IAAI,EAAE1T,KAAK,CAACwJ,IAAI;QAChBmK,IAAI,EAAE3T,KAAK,CAACgI,OAAO;QACnB4L,KAAK,EAAE,GAAGlV,QAAQ,4BAA4B;QAC9CmV,KAAK,EAAE,GAAGnV,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMoV,OAAO,GAAGA,CAAA,KAAM;MACpB9R,iBAAiB,CAACsD,OAAO,GAAGyO,qBAAqB,CAACD,OAAO,CAAC;;MAE1D;MACAxX,KAAK,CAACgK,MAAM,CAAC,CAAC;MAEd,IAAIhJ,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAAC6H,OAAO,GAAG,KAAK;;QAExB;QACA,MAAM4O,UAAU,GAAGjX,gBAAgB,CAAC6F,QAAQ,CAACrC,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAM0T,eAAe,GAAGlX,gBAAgB,CAACqN,QAAQ,CAACzJ,CAAC;;QAEnD;QACA;QACA,MAAMuT,gBAAgB,GAAG,EAAED,eAAe,GAAG/S,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAMgT,YAAY,GAAG,IAAIjY,KAAK,CAACoP,OAAO,CACpC,CAAC,EAAE,GAAGpK,IAAI,CAACkT,GAAG,CAACF,gBAAgB,CAAC,EAChC,GAAG,EACH,CAAC,EAAE,GAAGhT,IAAI,CAACmT,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACAlE,MAAM,CAACpN,QAAQ,CAACqD,IAAI,CAAC+N,UAAU,CAAC,CAAC3J,GAAG,CAAC8J,YAAY,CAAC;;QAElD;QACAnE,MAAM,CAACvK,EAAE,CAAC3E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAMwT,YAAY,GAAGN,UAAU,CAACzT,KAAK,CAAC,CAAC;QACvCyP,MAAM,CAAC3J,MAAM,CAACiO,YAAY,CAAC;;QAE3B;QACAtE,MAAM,CAACuE,sBAAsB,CAAC,CAAC;QAC/BvE,MAAM,CAACtI,YAAY,CAAC,CAAC;QACrBsI,MAAM,CAACrI,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACApK,QAAQ,CAAC6H,OAAO,GAAG,KAAK;;QAExB;QACA7H,QAAQ,CAAC6I,MAAM,CAACH,IAAI,CAAC+N,UAAU,CAAC;QAChCzW,QAAQ,CAAC+I,MAAM,CAAC,CAAC;QAEjBxG,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnByU,IAAI,EAAER,UAAU,CAACnM,OAAO,CAAC,CAAC;UAC1BD,IAAI,EAAEoI,MAAM,CAACpN,QAAQ,CAACiF,OAAO,CAAC,CAAC;UAC/B4M,IAAI,EAAEH,YAAY,CAACzM,OAAO,CAAC,CAAC;UAC5B6M,IAAI,EAAE1E,MAAM,CAAC2E,iBAAiB,CAAC,IAAIzY,KAAK,CAACoP,OAAO,CAAC,CAAC,CAAC,CAACzD,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIvK,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAAC6H,OAAO,GAAG,IAAI;;QAEvB;QACA4K,MAAM,CAACvK,EAAE,CAAC3E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAII,IAAI,CAAC0T,GAAG,CAAC5E,MAAM,CAACpN,QAAQ,CAACjC,CAAC,CAAC,GAAG,EAAE,EAAE;UACpCqP,MAAM,CAACpN,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9BvD,QAAQ,CAAC6I,MAAM,CAACtF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BkP,MAAM,CAAC3J,MAAM,CAAC9I,QAAQ,CAAC6I,MAAM,CAAC;UAC9B7I,QAAQ,CAAC+I,MAAM,CAAC,CAAC;QACnB;;QAEA;QACA;QACA0J,MAAM,CAACtI,YAAY,CAAC,CAAC;QACrBsI,MAAM,CAACrI,iBAAiB,CAAC,IAAI,CAAC;MAEhC,CAAC,MAAM,IAAIrK,UAAU,KAAK,cAAc,EAAE;QACxC;QACAC,QAAQ,CAAC+I,MAAM,CAAC,CAAC;MACnB;MAEA,IAAI/I,QAAQ,EAAEA,QAAQ,CAAC+I,MAAM,CAAC,CAAC;MAC/B,IAAI1I,KAAK,IAAIoS,MAAM,EAAE;QACnBI,QAAQ,CAACyE,MAAM,CAACjX,KAAK,EAAEoS,MAAM,CAAC;MAChC;IACF,CAAC;IAED8D,OAAO,CAAC,CAAC;;IAET;IACA,MAAMgB,YAAY,GAAGA,CAAA,KAAM;MACzB9E,MAAM,CAAC+E,MAAM,GAAG7W,MAAM,CAACgS,UAAU,GAAGhS,MAAM,CAACiS,WAAW;MACtDH,MAAM,CAACuE,sBAAsB,CAAC,CAAC;MAC/BnE,QAAQ,CAACG,OAAO,CAACrS,MAAM,CAACgS,UAAU,EAAEhS,MAAM,CAACiS,WAAW,CAAC;IACzD,CAAC;IACDjS,MAAM,CAAC8W,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACA5W,MAAM,CAAC+W,aAAa,GAAG,MAAM;MAC3B,IAAIrR,SAAS,CAAC0B,OAAO,EAAE;QACrB1B,SAAS,CAAC0B,OAAO,CAAC1C,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzC8C,SAAS,CAAC0B,OAAO,CAACe,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCzC,SAAS,CAAC0B,OAAO,CAACoC,YAAY,CAAC,CAAC;QAChC9D,SAAS,CAAC0B,OAAO,CAACqC,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAIpK,QAAQ,EAAE;UACZA,QAAQ,CAAC6I,MAAM,CAACtF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BvD,QAAQ,CAAC6H,OAAO,GAAG,IAAI;UACvB7H,QAAQ,CAAC+I,MAAM,CAAC,CAAC;QACnB;QAEAhJ,UAAU,GAAG,QAAQ;QACrBwC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;MAExB;MACA,IAAIiC,iBAAiB,CAACsD,OAAO,EAAE;QAC7B4P,oBAAoB,CAAClT,iBAAiB,CAACsD,OAAO,CAAC;QAC/CtD,iBAAiB,CAACsD,OAAO,GAAG,IAAI;MAClC;;MAEA;MACA,IAAIpI,oBAAoB,EAAE;QACxBiY,aAAa,CAACjY,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;;MAEA;MACA,IAAI6E,aAAa,CAACuD,OAAO,EAAE;QACzBvD,aAAa,CAACuD,OAAO,CAAC8P,KAAK,CAAC,CAAC;QAC7BrT,aAAa,CAACuD,OAAO,GAAG,IAAI;MAC9B;;MAEA;MACApH,MAAM,CAACmX,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;;MAElD;MACA,IAAI1E,QAAQ,IAAI1O,YAAY,CAAC4D,OAAO,EAAE;QACpC5D,YAAY,CAAC4D,OAAO,CAACgQ,WAAW,CAAClF,QAAQ,CAACQ,UAAU,CAAC;QACrDR,QAAQ,CAACmF,OAAO,CAAC,CAAC;MACpB;;MAEA;MACA,IAAI5W,aAAa,EAAE;QACjBA,aAAa,CAACyK,OAAO,CAAC,CAACwB,SAAS,EAAEtB,EAAE,KAAK;UACvC,IAAIsB,SAAS,CAACX,KAAK,IAAIrM,KAAK,EAAE;YAC5BA,KAAK,CAACkN,MAAM,CAACF,SAAS,CAACX,KAAK,CAAC;UAC/B;QACF,CAAC,CAAC;QACFtL,aAAa,CAAC6W,KAAK,CAAC,CAAC;MACvB;;MAEA;MACAzW,gBAAgB,CAACqK,OAAO,CAAEqM,QAAQ,IAAK;QACrC,IAAI7X,KAAK,IAAI6X,QAAQ,CAACxL,KAAK,EAAE;UAC3BrM,KAAK,CAACkN,MAAM,CAAC2K,QAAQ,CAACxL,KAAK,CAAC;QAC9B;MACF,CAAC,CAAC;MACFlL,gBAAgB,CAACyW,KAAK,CAAC,CAAC;MACxBxW,kBAAkB,CAACwW,KAAK,CAAC,CAAC;;MAE1B;;MAEA;MACA5X,KAAK,GAAG,IAAI;MACZL,QAAQ,GAAG,IAAI;MACfC,qBAAqB,GAAG,IAAI;MAC5BC,qBAAqB,GAAG,IAAI;MAC5BC,oBAAoB,GAAG,IAAI;MAC3BC,0BAA0B,GAAG,IAAI;MACjCZ,gBAAgB,GAAG,IAAI;MAEvB+C,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjE,SAAS,CAAC,MAAM;IACd;IACAmD,qBAAqB,CAAC,CAAC;;IAEvB;IACA,MAAMyW,uBAAuB,GAAGA,CAAA,KAAM;MACpC5V,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACjCd,qBAAqB,CAAC,CAAC;IACzB,CAAC;;IAED;IACAf,MAAM,CAAC8W,gBAAgB,CAAC,oBAAoB,EAAEU,uBAAuB,CAAC;;IAEtE;IACA,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnC3W,qBAAqB,CAAC,CAAC;IACzB,CAAC,EAAE,KAAK,CAAC;;IAET;IACA,OAAO,MAAM;MACXf,MAAM,CAACmX,mBAAmB,CAAC,oBAAoB,EAAEK,uBAAuB,CAAC;MACzEP,aAAa,CAACQ,UAAU,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7Z,SAAS,CAAC,MAAM;IACd;IACA,IAAI8B,KAAK,IAAIgE,SAAS,CAAC0D,OAAO,EAAE;MAC9BxF,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxB;MACA,MAAM8V,KAAK,GAAGhG,UAAU,CAAC,MAAM;QAC7B,IAAIjS,KAAK,IAAIgE,SAAS,CAAC0D,OAAO,EAAE;UAAG;UACjCwQ,mBAAmB,CAAClU,SAAS,CAAC0D,OAAO,CAAC;QACxC;MACF,CAAC,EAAE,IAAI,CAAC;MAER,OAAO,MAAMyQ,YAAY,CAACF,KAAK,CAAC;IAClC,CAAC,MAAM;MACL/V,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC,EAAE,CAACnC,KAAK,CAAC,CAAC;;EAEX;EACA9B,SAAS,CAAC,MAAM;IACd,IAAI4F,YAAY,CAAC4D,OAAO,EAAE;MACxB;MACA,MAAM0Q,WAAW,GAAIxI,KAAK,IAAK;QAC7B,IAAI5P,KAAK,IAAIgG,SAAS,CAAC0B,OAAO,EAAE;UAC9B2Q,gBAAgB,CAACzI,KAAK,EAAE9L,YAAY,CAAC4D,OAAO,EAAE1H,KAAK,EAAEgG,SAAS,CAAC0B,OAAO,EAAEtB,sBAAsB,CAAC;QACjG;MACF,CAAC;;MAED;MACAtC,YAAY,CAAC4D,OAAO,CAAC0P,gBAAgB,CAAC,OAAO,EAAEgB,WAAW,CAAC;;MAE3D;MACA,OAAO,MAAM;QACX,IAAItU,YAAY,CAAC4D,OAAO,EAAE;UACxB5D,YAAY,CAAC4D,OAAO,CAAC+P,mBAAmB,CAAC,OAAO,EAAEW,WAAW,CAAC;QAChE;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACpY,KAAK,EAAEgG,SAAS,CAAC0B,OAAO,CAAC,CAAC;;EAE9B;EACA,MAAM4Q,SAAS,GAAGja,WAAW,CAAC,MAAM;IAClC6D,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B;EACF,CAAC,EAAE,CAAC2B,YAAY,EAAEoD,aAAa,EAAE/F,gBAAgB,CAAC,CAAC;;EAEnD;EACA,MAAMoX,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMC,QAAQ,GAAG,IAAIla,KAAK,CAACma,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChD,MAAM9D,QAAQ,GAAG,IAAIrW,KAAK,CAAC0Q,iBAAiB,CAAC;MAAElI,KAAK,EAAE;IAAS,CAAC,CAAC;IACjE,MAAM2H,iBAAiB,GAAG,IAAInQ,KAAK,CAAC8Q,IAAI,CAACoJ,QAAQ,EAAE7D,QAAQ,CAAC;;IAE5D;IACA,MAAM+D,YAAY,GAAG,IAAIpa,KAAK,CAACqa,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC5D,MAAMC,YAAY,GAAG,IAAIta,KAAK,CAAC0Q,iBAAiB,CAAC;MAAElI,KAAK,EAAE;IAAS,CAAC,CAAC;IACrE,MAAM+R,SAAS,GAAG,IAAIva,KAAK,CAAC8Q,IAAI,CAACsJ,YAAY,EAAEE,YAAY,CAAC;IAC5DC,SAAS,CAAC7T,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IAClCuL,iBAAiB,CAAChC,GAAG,CAACoM,SAAS,CAAC;IAEhC,OAAOpK,iBAAiB;EAC1B,CAAC;EAED,oBACEzP,OAAA,CAAAE,SAAA;IAAAwP,QAAA,gBACE1P,OAAA;MAAM8Z,KAAK,EAAElS,UAAW;MAAA8H,QAAA,EAAC;IAAK;MAAAqK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrCla,OAAA,CAACJ,MAAM;MACLka,KAAK,EAAErS,uBAAwB;MAC/B0S,WAAW,EAAC,4CAAS;MACrBC,QAAQ,EAAElQ,wBAAyB;MACnCmQ,OAAO,EAAEva,iBAAiB,CAACuK,aAAa,CAACyD,GAAG,CAAC1D,YAAY,KAAK;QAC5DD,KAAK,EAAEC,YAAY,CAACG,IAAI;QACxB+P,KAAK,EAAElQ,YAAY,CAACG;MACtB,CAAC,CAAC,CAAE;MACJgQ,IAAI,EAAC,OAAO;MACZC,QAAQ,EAAE,IAAK;MACfC,aAAa,EAAE;QACbrU,MAAM,EAAE,IAAI;QACZsU,SAAS,EAAE;MACb;IAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACFla,OAAA;MAAK2a,GAAG,EAAE7V,YAAa;MAACgV,KAAK,EAAE;QAAEnS,KAAK,EAAE,MAAM;QAAE4F,MAAM,EAAE;MAAO;IAAE;MAAAwM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGnE/S,mBAAmB,CAACE,OAAO,iBAC1BrH,OAAA;MACE8Z,KAAK,EAAE;QACL9T,QAAQ,EAAE,UAAU;QACpBE,IAAI,EAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAACnC,CAAC,IAAI;QAC3C6D,GAAG,EAAE,GAAGP,mBAAmB,CAACnB,QAAQ,CAACjC,CAAC,IAAI;QAC1CoC,SAAS,EAAE,wBAAwB;QACnCC,MAAM,EAAE,IAAI;QACZK,eAAe,EAAE,qBAAqB;QACtCqB,KAAK,EAAE,OAAO;QACdnB,YAAY,EAAE,KAAK;QACnBG,SAAS,EAAE,+BAA+B;QAC1CN,OAAO,EAAE,GAAG;QACZkU,SAAS,EAAE,MAAM;QACjBE,SAAS,EAAE,MAAM;QACjBlU,MAAM,EAAE,iCAAiC;QACzCmU,SAAS,EAAE;MACb,CAAE;MAAAnL,QAAA,GAEDvI,mBAAmB,CAACI,OAAO,eAC5BvH,OAAA;QACE8Z,KAAK,EAAE;UACL9T,QAAQ,EAAE,UAAU;UACpB0B,GAAG,EAAE,KAAK;UACVoT,KAAK,EAAE,KAAK;UACZC,UAAU,EAAE,MAAM;UAClBrU,MAAM,EAAE,MAAM;UACdoB,KAAK,EAAE,OAAO;UACdjB,QAAQ,EAAE,MAAM;UAChBD,MAAM,EAAE,SAAS;UACjBJ,OAAO,EAAE,SAAS;UAClBuB,UAAU,EAAE;QACd,CAAE;QACFiT,OAAO,EAAEA,CAAA,KAAMC,kBAAkB,CAAC7T,sBAAsB,CAAE;QAAAsI,QAAA,EAC3D;MAED;QAAAqK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAEDla,OAAA;MAAK8Z,KAAK,EAAE/T,oBAAqB;MAAA2J,QAAA,gBAC/B1P,OAAA;QACE8Z,KAAK,EAAE;UACL,GAAGvT,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EiC,KAAK,EAAEjC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFmV,OAAO,EAAEzS,kBAAmB;QAAAmH,QAAA,EAC7B;MAED;QAAAqK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTla,OAAA;QACE8Z,KAAK,EAAE;UACL,GAAGvT,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EiC,KAAK,EAAEjC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFmV,OAAO,EAAEvS,kBAAmB;QAAAiH,QAAA,EAC7B;MAED;QAAAqK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAArV,EAAA,CAlyCMJ,WAAW;AAAAyW,EAAA,GAAXzW,WAAW;AAmyCjB,SAAS0W,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAAC1T,KAAK,GAAG,GAAG;EAClB0T,MAAM,CAAC9N,MAAM,GAAG,EAAE;;EAElB;EACAiO,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAAC1T,KAAK,GAAC,CAAC,EAAE0T,MAAM,CAAC9N,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAMuO,OAAO,GAAG,IAAIxc,KAAK,CAACyc,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAI1c,KAAK,CAAC2c,cAAc,CAAC;IAC9CnO,GAAG,EAAEgO,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAI7c,KAAK,CAAC8c,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAACtF,KAAK,CAAC3S,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAOiY,MAAM;AACf;;AAIA;AACA7a,MAAM,CAAC+a,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMjJ,MAAM,GAAGkI,QAAQ,CAACgB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAIpJ,MAAM,EAAE;MACV;MACA,MAAMqJ,MAAM,GAAGrJ,MAAM,CAACpN,QAAQ,CAACrC,KAAK,CAAC,CAAC;;MAEtC;MACAyP,MAAM,CAACpN,QAAQ,CAAC9B,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BkP,MAAM,CAACvK,EAAE,CAAC3E,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBkP,MAAM,CAAC3J,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACA2J,MAAM,CAACtI,YAAY,CAAC,CAAC;MACrBsI,MAAM,CAACrI,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAMpK,QAAQ,GAAG2a,QAAQ,CAACgB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAI/b,QAAQ,EAAE;QACZA,QAAQ,CAAC6I,MAAM,CAACtF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5BvD,QAAQ,CAAC+I,MAAM,CAAC,CAAC;MACnB;MAEAxG,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBwZ,GAAG,EAAEF,MAAM,CAACxR,OAAO,CAAC,CAAC;QACrB2R,GAAG,EAAExJ,MAAM,CAACpN,QAAQ,CAACiF,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAO4R,CAAC,EAAE;IACV3Z,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEyZ,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAM3J,aAAa,GAAG,MAAAA,CAAA,KAAY;EAChC,IAAI;IACFhQ,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAMyT,MAAM,GAAG,IAAIrX,UAAU,CAAC,CAAC;;IAE/B;IACA,IAAI;MACF,MAAM,CAACud,WAAW,EAAEC,WAAW,EAAEC,UAAU,EAAEC,gBAAgB,CAAC,GAAG,MAAMjI,OAAO,CAACkI,GAAG,CAAC,CACjFtG,MAAM,CAACuG,SAAS,CAAC,GAAGrb,QAAQ,uBAAuB,CAAC,EACpD8U,MAAM,CAACuG,SAAS,CAAC,GAAGrb,QAAQ,uBAAuB,CAAC,EACpD8U,MAAM,CAACuG,SAAS,CAAC,GAAGrb,QAAQ,sBAAsB,CAAC,EACnD8U,MAAM,CAACuG,SAAS,CAAC,GAAGrb,QAAQ,6BAA6B,CAAC,CAC3D,CAAC;;MAEF;MACAlB,qBAAqB,GAAGkc,WAAW,CAAC9b,KAAK;MACzCJ,qBAAqB,CAAC6U,QAAQ,CAAE9F,KAAK,IAAK;QACxC,IAAIA,KAAK,CAAC+F,MAAM,EAAE;UAChB,MAAME,WAAW,GAAG,IAAItW,KAAK,CAACuW,oBAAoB,CAAC;YACjD/N,KAAK,EAAE,QAAQ;YACfgO,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,GAAG;YACdC,eAAe,EAAE;UACnB,CAAC,CAAC;;UAEF;UACA,IAAIrG,KAAK,CAACgG,QAAQ,CAAC7H,GAAG,EAAE;YACtB8H,WAAW,CAAC9H,GAAG,GAAG6B,KAAK,CAACgG,QAAQ,CAAC7H,GAAG;UACtC;UACA6B,KAAK,CAACyN,OAAO,GAAGxH,WAAW;QAC7B;MACF,CAAC,CAAC;;MAEF;MACA/U,qBAAqB,GAAGkc,WAAW,CAAC/b,KAAK;MACzC;MACAH,qBAAqB,CAACgW,KAAK,CAAC3S,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxC;MACArD,qBAAqB,CAAC4U,QAAQ,CAAE9F,KAAK,IAAK;QACxC,IAAIA,KAAK,CAAC+F,MAAM,IAAI/F,KAAK,CAACgG,QAAQ,EAAE;UAClC;UACAhG,KAAK,CAACgG,QAAQ,CAACG,SAAS,GAAG,GAAG;UAC9BnG,KAAK,CAACgG,QAAQ,CAACI,SAAS,GAAG,GAAG;UAC9BpG,KAAK,CAACgG,QAAQ,CAACK,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;;MAEF;MACAlV,oBAAoB,GAAGkc,UAAU,CAAChc,KAAK;MACvC;MACAF,oBAAoB,CAAC+V,KAAK,CAAC3S,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACvC;MACApD,oBAAoB,CAAC2U,QAAQ,CAAE9F,KAAK,IAAK;QACvC,IAAIA,KAAK,CAAC+F,MAAM,IAAI/F,KAAK,CAACgG,QAAQ,EAAE;UAClC;UACAhG,KAAK,CAACgG,QAAQ,CAACG,SAAS,GAAG,GAAG;UAC9BnG,KAAK,CAACgG,QAAQ,CAACI,SAAS,GAAG,GAAG;UAC9BpG,KAAK,CAACgG,QAAQ,CAACK,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;;MAEF;MACAjV,0BAA0B,GAAGkc,gBAAgB,CAACjc,KAAK;MACnD;MACAD,0BAA0B,CAAC8V,KAAK,CAAC3S,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7C;MACAnD,0BAA0B,CAAC0U,QAAQ,CAAE9F,KAAK,IAAK;QAC7C,IAAIA,KAAK,CAAC+F,MAAM,IAAI/F,KAAK,CAACgG,QAAQ,EAAE;UAClC;UACAhG,KAAK,CAACgG,QAAQ,CAACG,SAAS,GAAG,GAAG;UAC9BnG,KAAK,CAACgG,QAAQ,CAACI,SAAS,GAAG,GAAG;UAC9BpG,KAAK,CAACgG,QAAQ,CAACK,eAAe,GAAG,GAAG;QACtC;MACF,CAAC,CAAC;MAEF9S,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACA,IAAI;QACF,IAAI,CAACxC,qBAAqB,EAAE;UAC1B,MAAMkc,WAAW,GAAG,MAAMlG,MAAM,CAACuG,SAAS,CAAC,GAAGrb,QAAQ,uBAAuB,CAAC;UAC9ElB,qBAAqB,GAAGkc,WAAW,CAAC9b,KAAK;QAC3C;;QAEA;QACA,IAAI,CAACD,0BAA0B,EAAE;UAC/BmC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;UAC7B,MAAM8Z,gBAAgB,GAAG,MAAMrG,MAAM,CAACuG,SAAS,CAAC,GAAGrb,QAAQ,6BAA6B,CAAC;UACzFf,0BAA0B,GAAGkc,gBAAgB,CAACjc,KAAK;UACnDD,0BAA0B,CAAC8V,KAAK,CAAC3S,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC7ChB,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOka,GAAG,EAAE;QACZna,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEia,GAAG,CAAC;MAClC;IACF;EACF,CAAC,CAAC,OAAOja,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;EAClC;AACF,CAAC;;AAED;AACA,MAAMmP,mBAAmB,GAAI3F,IAAI,IAAK;EACpC,MAAM0Q,KAAK,GAAG;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE,MAAM;IACX,GAAG,EAAE;EACP,CAAC;EACD,OAAOA,KAAK,CAAC1Q,IAAI,CAAC,IAAI,MAAM;AAC9B,CAAC;;AAED;AACA,MAAM2E,iBAAiB,GAAGA,CAACvL,QAAQ,EAAEoV,IAAI,EAAEtT,KAAK,KAAK;EACnD;EACA,MAAMqU,MAAM,GAAGhB,gBAAgB,CAACC,IAAI,CAAC;EACrCe,MAAM,CAACnW,QAAQ,CAAC9B,GAAG,CAAC8B,QAAQ,CAACnC,CAAC,EAAE,EAAE,EAAE,CAACmC,QAAQ,CAACjC,CAAC,CAAC,CAAC,CAAE;;EAEnD;EACAkP,UAAU,CAAC,MAAM;IACfjS,KAAK,CAACkN,MAAM,CAACiO,MAAM,CAAC;EACtB,CAAC,EAAE,GAAG,CAAC;;EAEP;EACAnb,KAAK,CAACyM,GAAG,CAAC0O,MAAM,CAAC;EAEjBjZ,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;IACvB0O,EAAE,EAAE7L,QAAQ;IACZuX,EAAE,EAAEnC,IAAI;IACRoC,EAAE,EAAE1V;EACN,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMoR,mBAAmB,GAAIuE,iBAAiB,IAAK;EACjD,IAAI,CAACzc,KAAK,EAAE;IACVkC,OAAO,CAACE,KAAK,CAAC,gBAAgB,CAAC;IAC/B;EACF;EAEA,IAAI,CAACqa,iBAAiB,EAAE;IACtBva,OAAO,CAACE,KAAK,CAAC,mBAAmB,CAAC;IAClC;EACF;EAEA,IAAI,CAACrC,0BAA0B,EAAE;IAC/BmC,OAAO,CAACE,KAAK,CAAC,UAAU,CAAC;IACzB;IACAsa,2BAA2B,CAACD,iBAAiB,CAAC;IAC9C;EACF;;EAEA;EACAtb,gBAAgB,CAACqK,OAAO,CAAEqM,QAAQ,IAAK;IACrC,IAAI7X,KAAK,IAAI6X,QAAQ,CAACxL,KAAK,EAAE;MAC3BrM,KAAK,CAACkN,MAAM,CAAC2K,QAAQ,CAACxL,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACFlL,gBAAgB,CAACyW,KAAK,CAAC,CAAC;;EAExB;EACA9Y,iBAAiB,CAACuK,aAAa,CAACmC,OAAO,CAACpC,YAAY,IAAI;IACtD,IAAIA,YAAY,CAAC1E,QAAQ,IAAI0E,YAAY,CAAC3E,SAAS,IAAI2E,YAAY,CAAC9C,OAAO,EAAE;MAC3E;MACA,MAAM6F,QAAQ,GAAGsQ,iBAAiB,CAAChT,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAC3E,SAAS,CAAC,EAClCiF,UAAU,CAACN,YAAY,CAAC1E,QAAQ,CAClC,CAAC;MAED,IAAI;QACF;QACA,MAAM+J,iBAAiB,GAAG1O,0BAA0B,CAAC4C,KAAK,CAAC,CAAC;;QAE5D;QACA8L,iBAAiB,CAACzJ,QAAQ,CAAC9B,GAAG,CAACiJ,QAAQ,CAACtJ,CAAC,EAAE,EAAE,EAAE,CAACsJ,QAAQ,CAACpJ,CAAC,CAAC;;QAE3D;QACA0L,iBAAiB,CAACkO,QAAQ,GAAG;UAC3B/Q,IAAI,EAAE,cAAc;UACpBtF,OAAO,EAAE8C,YAAY,CAAC9C,OAAO;UAC7BiD,IAAI,EAAEH,YAAY,CAACG;QACrB,CAAC;;QAED;QACAkF,iBAAiB,CAACgG,QAAQ,CAAE9F,KAAK,IAAK;UACpC,IAAIA,KAAK,CAAC+F,MAAM,EAAE;YAChB/F,KAAK,CAACgO,QAAQ,GAAG;cACf/Q,IAAI,EAAE,cAAc;cACpBtF,OAAO,EAAE8C,YAAY,CAAC9C,OAAO;cAC7BiD,IAAI,EAAEH,YAAY,CAACG;YACrB,CAAC;UACH;QACF,CAAC,CAAC;;QAEF;QACAvJ,KAAK,CAACyM,GAAG,CAACgC,iBAAiB,CAAC;;QAE5B;QACAtN,gBAAgB,CAAC+B,GAAG,CAACkG,YAAY,CAAC9C,OAAO,EAAE;UACzC+F,KAAK,EAAEoC,iBAAiB;UACxBrF,YAAY,EAAEA,YAAY;UAC1BpE,QAAQ,EAAEmH;QACZ,CAAC,CAAC;;QAEF;QACA,IAAI,CAAC/K,kBAAkB,CAAC6L,GAAG,CAAC7D,YAAY,CAAC9C,OAAO,CAAC,EAAE;UACjD;UACA,MAAMsW,UAAU,GAAG,CACjB;YAAEzO,OAAO,EAAE,GAAG;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACrF;YAAEL,OAAO,EAAE,GAAG;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACrF;YAAEL,OAAO,EAAE,GAAG;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACrF;YAAEL,OAAO,EAAE,GAAG;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACrF;YAAEL,OAAO,EAAE,GAAG;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACrF;YAAEL,OAAO,EAAE,GAAG;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACrF;YAAEL,OAAO,EAAE,GAAG;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACrF;YAAEL,OAAO,EAAE,IAAI;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACtF;YAAEL,OAAO,EAAE,IAAI;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACtF;YAAEL,OAAO,EAAE,IAAI;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACtF;YAAEL,OAAO,EAAE,IAAI;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACtF;YAAEL,OAAO,EAAE,IAAI;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC,CAAE;UAAA,CACvF;UAEDpN,kBAAkB,CAAC8B,GAAG,CAACkG,YAAY,CAAC9C,OAAO,EAAE;YAC3CE,MAAM,EAAEoW;UACV,CAAC,CAAC;UAEF1a,OAAO,CAACC,GAAG,CAAC,OAAOiH,YAAY,CAACG,IAAI,KAAKH,YAAY,CAAC9C,OAAO,gBAAgB,CAAC;QAChF;QAEApE,OAAO,CAACC,GAAG,CAAC,SAASiH,YAAY,CAACG,IAAI,KAAKH,YAAY,CAAC9C,OAAO,kBAAkB6F,QAAQ,CAACtJ,CAAC,KAAK,CAACsJ,QAAQ,CAACpJ,CAAC,GAAG,CAAC;MACjH,CAAC,CAAC,OAAOX,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,QAAQgH,YAAY,CAACG,IAAI,YAAY,EAAEnH,KAAK,CAAC;QAC3D;QACAmW,wBAAwB,CAACnP,YAAY,EAAE+C,QAAQ,EAAEsQ,iBAAiB,CAAC;MACrE;IACF;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMlE,wBAAwB,GAAGA,CAACnP,YAAY,EAAE+C,QAAQ,EAAEsQ,iBAAiB,KAAK;EAC9E;EACA,MAAMjE,QAAQ,GAAG,IAAIla,KAAK,CAACma,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAChD,MAAM9D,QAAQ,GAAG,IAAIrW,KAAK,CAAC0Q,iBAAiB,CAAC;IAAElI,KAAK,EAAE;EAAS,CAAC,CAAC;EACjE,MAAM2H,iBAAiB,GAAG,IAAInQ,KAAK,CAAC8Q,IAAI,CAACoJ,QAAQ,EAAE7D,QAAQ,CAAC;;EAE5D;EACAlG,iBAAiB,CAACzJ,QAAQ,CAAC9B,GAAG,CAACiJ,QAAQ,CAACtJ,CAAC,EAAE,EAAE,EAAE,CAACsJ,QAAQ,CAACpJ,CAAC,CAAC;;EAE3D;EACA0L,iBAAiB,CAACkO,QAAQ,GAAG;IAC3B/Q,IAAI,EAAE,cAAc;IACpBtF,OAAO,EAAE8C,YAAY,CAAC9C,OAAO;IAC7BiD,IAAI,EAAEH,YAAY,CAACG;EACrB,CAAC;;EAED;EACAvJ,KAAK,CAACyM,GAAG,CAACgC,iBAAiB,CAAC;;EAE5B;EACAtN,gBAAgB,CAAC+B,GAAG,CAACkG,YAAY,CAAC9C,OAAO,EAAE;IACzC+F,KAAK,EAAEoC,iBAAiB;IACxBrF,YAAY,EAAEA,YAAY;IAC1BpE,QAAQ,EAAEmH;EACZ,CAAC,CAAC;;EAEF;EACA,IAAI,CAAC/K,kBAAkB,CAAC6L,GAAG,CAAC7D,YAAY,CAAC9C,OAAO,CAAC,EAAE;IACjD;IACA,MAAMsW,UAAU,GAAG,CACjB;MAAEzO,OAAO,EAAE,GAAG;MAAEF,YAAY,EAAE,GAAG;MAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;IAAE,CAAC;IAAE;IACrF;MAAEL,OAAO,EAAE,GAAG;MAAEF,YAAY,EAAE,GAAG;MAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;IAAE,CAAC;IAAE;IACrF;MAAEL,OAAO,EAAE,GAAG;MAAEF,YAAY,EAAE,GAAG;MAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;IAAE,CAAC;IAAE;IACrF;MAAEL,OAAO,EAAE,GAAG;MAAEF,YAAY,EAAE,GAAG;MAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;IAAE,CAAC;IAAE;IACrF;MAAEL,OAAO,EAAE,GAAG;MAAEF,YAAY,EAAE,GAAG;MAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;IAAE,CAAC;IAAE;IACrF;MAAEL,OAAO,EAAE,GAAG;MAAEF,YAAY,EAAE,GAAG;MAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;IAAE,CAAC;IAAE;IACrF;MAAEL,OAAO,EAAE,GAAG;MAAEF,YAAY,EAAE,GAAG;MAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;IAAE,CAAC;IAAE;IACrF;MAAEL,OAAO,EAAE,IAAI;MAAEF,YAAY,EAAE,GAAG;MAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;IAAE,CAAC;IAAE;IACtF;MAAEL,OAAO,EAAE,IAAI;MAAEF,YAAY,EAAE,GAAG;MAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;IAAE,CAAC;IAAE;IACtF;MAAEL,OAAO,EAAE,IAAI;MAAEF,YAAY,EAAE,GAAG;MAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;IAAE,CAAC;IAAE;IACtF;MAAEL,OAAO,EAAE,IAAI;MAAEF,YAAY,EAAE,GAAG;MAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;IAAE,CAAC;IAAE;IACtF;MAAEL,OAAO,EAAE,IAAI;MAAEF,YAAY,EAAE,GAAG;MAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;IAAE,CAAC,CAAE;IAAA,CACvF;IAEDpN,kBAAkB,CAAC8B,GAAG,CAACkG,YAAY,CAAC9C,OAAO,EAAE;MAC3CE,MAAM,EAAEoW;IACV,CAAC,CAAC;IAEF1a,OAAO,CAACC,GAAG,CAAC,OAAOiH,YAAY,CAACG,IAAI,KAAKH,YAAY,CAAC9C,OAAO,gBAAgB,CAAC;EAChF;EAEApE,OAAO,CAACC,GAAG,CAAC,SAASiH,YAAY,CAACG,IAAI,KAAKH,YAAY,CAAC9C,OAAO,kBAAkB6F,QAAQ,CAACtJ,CAAC,KAAK,CAACsJ,QAAQ,CAACpJ,CAAC,GAAG,CAAC;AACjH,CAAC;;AAED;AACA,MAAM2Z,2BAA2B,GAAID,iBAAiB,IAAK;EACzD3d,iBAAiB,CAACuK,aAAa,CAACmC,OAAO,CAACpC,YAAY,IAAI;IACtD,IAAIA,YAAY,CAAC1E,QAAQ,IAAI0E,YAAY,CAAC3E,SAAS,IAAI2E,YAAY,CAAC9C,OAAO,EAAE;MAC3E;MACA,MAAM6F,QAAQ,GAAGsQ,iBAAiB,CAAChT,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAC3E,SAAS,CAAC,EAClCiF,UAAU,CAACN,YAAY,CAAC1E,QAAQ,CAClC,CAAC;MAED6T,wBAAwB,CAACnP,YAAY,EAAE+C,QAAQ,EAAEsQ,iBAAiB,CAAC;IACrE;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAMI,iBAAiB,GAAI1O,OAAO,IAAK;EACrC,MAAM2O,UAAU,GAAG;IACjB,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,OAAO;IACZ,GAAG,EAAE,OAAO;IACZ,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,OAAO;IACb,IAAI,EAAE;EACR,CAAC;EAED,OAAOA,UAAU,CAAC3O,OAAO,CAAC,IAAI,KAAKA,OAAO,EAAE;AAC9C,CAAC;;AAED;AACA,MAAMkK,gBAAgB,GAAGA,CAACzI,KAAK,EAAEmN,SAAS,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,KAAK;EAC7F,IAAI,CAACH,SAAS,IAAI,CAACC,aAAa,IAAI,CAACC,cAAc,EAAE;;EAErD;EACA,MAAME,IAAI,GAAGJ,SAAS,CAACK,qBAAqB,CAAC,CAAC;EAC9C,MAAMC,MAAM,GAAI,CAACzN,KAAK,CAAC0N,OAAO,GAAGH,IAAI,CAACjY,IAAI,IAAI6X,SAAS,CAACQ,WAAW,GAAI,CAAC,GAAG,CAAC;EAC5E,MAAMC,MAAM,GAAG,EAAE,CAAC5N,KAAK,CAAC6N,OAAO,GAAGN,IAAI,CAACzW,GAAG,IAAIqW,SAAS,CAACW,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC;;EAE7E;EACA,MAAMC,SAAS,GAAG,IAAIrf,KAAK,CAACsf,SAAS,CAAC,CAAC;EACvCD,SAAS,CAACE,MAAM,CAACC,IAAI,CAACC,SAAS,GAAG,CAAC;EACnCJ,SAAS,CAACE,MAAM,CAACG,MAAM,CAACD,SAAS,GAAG,CAAC;EACrCJ,SAAS,CAACE,MAAM,CAACzO,IAAI,CAAC2O,SAAS,GAAG,GAAG,CAAC,CAAC;;EAEvC,MAAME,WAAW,GAAG,IAAI3f,KAAK,CAAC4f,OAAO,CAACb,MAAM,EAAEG,MAAM,CAAC;EACrDG,SAAS,CAACQ,aAAa,CAACF,WAAW,EAAEhB,cAAc,CAAC;;EAEpD;EACA,MAAMmB,UAAU,GAAGT,SAAS,CAACU,gBAAgB,CAACrB,aAAa,CAACtO,QAAQ,EAAE,IAAI,CAAC;;EAE3E;EACAxM,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;IAAEU,CAAC,EAAE+M,KAAK,CAAC0N,OAAO;IAAEva,CAAC,EAAE6M,KAAK,CAAC6N;EAAQ,CAAC,CAAC;EAC9Dvb,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEic,UAAU,CAACpQ,MAAM,CAAC;EAC7CoQ,UAAU,CAAC5S,OAAO,CAAC,CAAC8S,GAAG,EAAEC,KAAK,KAAK;IACjCrc,OAAO,CAACC,GAAG,CAAC,MAAMoc,KAAK,GAAG,EAAED,GAAG,CAACE,MAAM,CAAC5S,IAAI,EAAE0S,GAAG,CAACE,MAAM,CAAC7B,QAAQ,CAAC;EACnE,CAAC,CAAC;;EAEF;EACAO,eAAe,CAACuB,IAAI,IAAI;IACtB,IAAIA,IAAI,CAACpY,OAAO,EAAE;MAChB,OAAO;QAAE,GAAGoY,IAAI;QAAEpY,OAAO,EAAE;MAAM,CAAC;IACpC;IACA,OAAOoY,IAAI;EACb,CAAC,CAAC;;EAEF;EACA,KAAK,IAAInV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8U,UAAU,CAACpQ,MAAM,EAAE1E,CAAC,EAAE,EAAE;IAC1C,MAAMgV,GAAG,GAAGI,yBAAyB,CAACN,UAAU,CAAC9U,CAAC,CAAC,CAACkV,MAAM,CAAC;IAC3D,IAAIF,GAAG,IAAIA,GAAG,CAAC3B,QAAQ,IAAI2B,GAAG,CAAC3B,QAAQ,CAAC/Q,IAAI,KAAK,cAAc,EAAE;MAC/D,MAAMtF,OAAO,GAAGgY,GAAG,CAAC3B,QAAQ,CAACrW,OAAO;MACpC,MAAMqY,SAAS,GAAGvd,kBAAkB,CAAC0J,GAAG,CAACxE,OAAO,CAAC;MACjD,MAAMuR,QAAQ,GAAG1W,gBAAgB,CAAC2J,GAAG,CAACxE,OAAO,CAAC;MAE9CpE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEmc,GAAG,CAAC3B,QAAQ,CAAC;MAErC,IAAI9E,QAAQ,EAAE;QACZ;QACA,MAAM7S,QAAQ,GAAG,IAAI1G,KAAK,CAACoP,OAAO,CAAC,CAAC;QACpC1I,QAAQ,CAACqD,IAAI,CAACiW,GAAG,CAACtZ,QAAQ,CAAC;QAC3BA,QAAQ,CAAC4Z,OAAO,CAAC3B,cAAc,CAAC;QAEhC,MAAMpa,CAAC,GAAG,CAACmC,QAAQ,CAACnC,CAAC,GAAG,GAAG,GAAG,GAAG,IAAIka,SAAS,CAACQ,WAAW;QAC1D,MAAMxa,CAAC,GAAG,CAAC,EAAEiC,QAAQ,CAACjC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,IAAIga,SAAS,CAACW,YAAY;QAE9Dxb,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;UAAEU,CAAC;UAAEE;QAAE,CAAC,CAAC;;QAEpC;QACA,IAAI4b,SAAS,IAAIA,SAAS,CAACnY,MAAM,IAAImY,SAAS,CAACnY,MAAM,CAACwH,MAAM,GAAG,CAAC,EAAE;UAChE9L,OAAO,CAACC,GAAG,CAAC,QAAQmc,GAAG,CAAC3B,QAAQ,CAACpT,IAAI,KAAKjD,OAAO,aAAa,EAAEqY,SAAS,CAAC;;UAE1E;UACA,MAAME,cAAc,gBAClB7f,OAAA;YAAK8Z,KAAK,EAAE;cAAEtT,OAAO,EAAE,MAAM;cAAEmB,KAAK,EAAE,OAAO;cAAE+S,SAAS,EAAE,OAAO;cAAEE,SAAS,EAAE;YAAO,CAAE;YAAAlL,QAAA,gBACrF1P,OAAA;cAAK8Z,KAAK,EAAE;gBACV/R,UAAU,EAAE,MAAM;gBAClB+X,YAAY,EAAE,MAAM;gBACpBjZ,QAAQ,EAAE,MAAM;gBAChBkZ,YAAY,EAAE,gBAAgB;gBAC9BC,aAAa,EAAE;cACjB,CAAE;cAAAtQ,QAAA,GACC4P,GAAG,CAAC3B,QAAQ,CAACpT,IAAI,EAAC,QAAM,EAACjD,OAAO,EAAC,GACpC;YAAA;cAAAyS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNla,OAAA;cAAA0P,QAAA,EACGiQ,SAAS,CAACnY,MAAM,CAACsG,GAAG,CAAC,CAACoB,KAAK,EAAEqQ,KAAK,KAAK;gBACtC;gBACA,IAAI3P,UAAU;gBACd,QAAQV,KAAK,CAACD,YAAY;kBACxB,KAAK,GAAG;oBACNW,UAAU,GAAG,SAAS;oBACtB;kBACF,KAAK,GAAG;oBACNA,UAAU,GAAG,SAAS;oBACtB;kBACF,KAAK,GAAG;kBACR;oBACEA,UAAU,GAAG,SAAS;oBACtB;gBACJ;gBAEA,oBACE5P,OAAA;kBAAiB8Z,KAAK,EAAE;oBACtBgG,YAAY,EAAE,KAAK;oBACnBrZ,eAAe,EAAE,uBAAuB;oBACxCD,OAAO,EAAE,KAAK;oBACdG,YAAY,EAAE;kBAChB,CAAE;kBAAA+I,QAAA,gBACA1P,OAAA;oBAAK8Z,KAAK,EAAE;sBAAE/R,UAAU,EAAE;oBAAO,CAAE;oBAAA2H,QAAA,EAChCmO,iBAAiB,CAAC3O,KAAK,CAACC,OAAO;kBAAC;oBAAA4K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACNla,OAAA;oBAAK8Z,KAAK,EAAE;sBAAEzT,OAAO,EAAE,MAAM;sBAAE4Z,cAAc,EAAE;oBAAgB,CAAE;oBAAAvQ,QAAA,gBAC/D1P,OAAA;sBAAA0P,QAAA,EAAM;oBAAI;sBAAAqK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBla,OAAA;sBAAM8Z,KAAK,EAAE;wBACXhS,KAAK,EAAE8H,UAAU;wBACjB7H,UAAU,EAAE,MAAM;wBAClBtB,eAAe,EAAE,iBAAiB;wBAClCD,OAAO,EAAE,OAAO;wBAChBG,YAAY,EAAE;sBAChB,CAAE;sBAAA+I,QAAA,EACCR,KAAK,CAACD,YAAY,KAAK,GAAG,GAAG,IAAI,GAAGC,KAAK,CAACD,YAAY,KAAK,GAAG,GAAG,IAAI,GAAG;oBAAI;sBAAA8K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNla,OAAA;oBAAK8Z,KAAK,EAAE;sBAAEzT,OAAO,EAAE,MAAM;sBAAE4Z,cAAc,EAAE;oBAAgB,CAAE;oBAAAvQ,QAAA,gBAC/D1P,OAAA;sBAAA0P,QAAA,EAAM;oBAAK;sBAAAqK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClBla,OAAA;sBAAM8Z,KAAK,EAAE;wBAAE/R,UAAU,EAAE;sBAAO,CAAE;sBAAA2H,QAAA,GAAER,KAAK,CAACI,UAAU,EAAC,SAAE;oBAAA;sBAAAyK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC;gBAAA,GAxBEqF,KAAK;kBAAAxF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyBV,CAAC;cAEV,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;;UAED;UACAgE,eAAe,CAAC;YACd7W,OAAO,EAAE,IAAI;YACbC,OAAO,EAAEA,OAAO;YAChBtB,QAAQ,EAAE;cAAEnC,CAAC;cAAEE,CAAC,EAAEA,CAAC,GAAG;YAAG,CAAC;YAAE;YAC5BwD,OAAO,EAAEsY,cAAc;YACvBrY,MAAM,EAAEmY,SAAS,CAACnY;UACpB,CAAC,CAAC;UAEFtE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;YAAEkE,OAAO,EAAE,IAAI;YAAEC,OAAO;YAAEzD,CAAC;YAAEE,CAAC,EAAEA,CAAC,GAAG;UAAG,CAAC,CAAC;UACnE,OAAO,CAAC;QACV,CAAC,MAAM;UACL;UACA;UACA,MAAM6Z,UAAU,GAAG,CACjB;YAAEzO,OAAO,EAAE,GAAG;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACrF;YAAEL,OAAO,EAAE,GAAG;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACrF;YAAEL,OAAO,EAAE,GAAG;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACrF;YAAEL,OAAO,EAAE,GAAG;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACrF;YAAEL,OAAO,EAAE,GAAG;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACrF;YAAEL,OAAO,EAAE,GAAG;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACrF;YAAEL,OAAO,EAAE,GAAG;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACrF;YAAEL,OAAO,EAAE,IAAI;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACtF;YAAEL,OAAO,EAAE,IAAI;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACtF;YAAEL,OAAO,EAAE,IAAI;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACtF;YAAEL,OAAO,EAAE,IAAI;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC;UAAE;UACtF;YAAEL,OAAO,EAAE,IAAI;YAAEF,YAAY,EAAE,GAAG;YAAEK,UAAU,EAAEhL,IAAI,CAACiL,KAAK,CAACjL,IAAI,CAACkL,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG;UAAE,CAAC,CAAE;UAAA,CACvF;UAEDpN,kBAAkB,CAAC8B,GAAG,CAACoD,OAAO,EAAE;YAC9BE,MAAM,EAAEoW;UACV,CAAC,CAAC;UAEF1a,OAAO,CAACC,GAAG,CAAC,OAAOmc,GAAG,CAAC3B,QAAQ,CAACpT,IAAI,KAAKjD,OAAO,gBAAgB,CAAC;;UAEjE;UACA+R,gBAAgB,CAACzI,KAAK,EAAEmN,SAAS,EAAEC,aAAa,EAAEC,cAAc,EAAEC,eAAe,CAAC;UAClF,OAAO,CAAC;QACV;MACF;IACF;EACF;;EAEA;EACAhb,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;AAC1B,CAAC;;AAED;AACA,MAAMuc,yBAAyB,GAAIF,MAAM,IAAK;EAC5C,IAAI9W,OAAO,GAAG8W,MAAM;;EAEpB;EACA,OAAO9W,OAAO,EAAE;IACd,IAAIA,OAAO,CAACiV,QAAQ,IAAIjV,OAAO,CAACiV,QAAQ,CAAC/Q,IAAI,KAAK,cAAc,EAAE;MAChE,OAAOlE,OAAO;IAChB;IACAA,OAAO,GAAGA,OAAO,CAACwX,MAAM;EAC1B;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACA,MAAMjF,kBAAkB,GAAIiD,eAAe,IAAK;EAC9CA,eAAe,CAACuB,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEpY,OAAO,EAAE;EAAM,CAAC,CAAC,CAAC;AACxD,CAAC;AAED,eAAe5C,WAAW;AAAC,IAAAyW,EAAA;AAAAiF,YAAA,CAAAjF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}