{"ast": null, "code": "\"use client\";\n\nimport { createContext } from 'react';\nconst MenuContext = /*#__PURE__*/createContext({\n  prefixCls: '',\n  firstLevel: true,\n  inlineCollapsed: false\n});\nexport default MenuContext;", "map": {"version": 3, "names": ["createContext", "MenuContext", "prefixCls", "firstLevel", "inlineCollapsed"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/menu/MenuContext.js"], "sourcesContent": ["\"use client\";\n\nimport { createContext } from 'react';\nconst MenuContext = /*#__PURE__*/createContext({\n  prefixCls: '',\n  firstLevel: true,\n  inlineCollapsed: false\n});\nexport default MenuContext;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,aAAa,QAAQ,OAAO;AACrC,MAAMC,WAAW,GAAG,aAAaD,aAAa,CAAC;EAC7CE,SAAS,EAAE,EAAE;EACbC,UAAU,EAAE,IAAI;EAChBC,eAAe,EAAE;AACnB,CAAC,CAAC;AACF,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}