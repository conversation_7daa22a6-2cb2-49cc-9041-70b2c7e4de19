{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n/**\n * Feature:\n *  - fixed not need to set width\n *  - support `rowExpandable` to config row expand logic\n *  - add `summary` to support `() => ReactNode`\n *\n * Update:\n *  - `dataIndex` is `array[]` now\n *  - `expandable` wrap all the expand related props\n *\n * Removed:\n *  - expandIconAsCell\n *  - useFixedHeader\n *  - rowRef\n *  - columns[number].onCellClick\n *  - onRowClick\n *  - onRowDoubleClick\n *  - onRowMouseEnter\n *  - onRowMouseLeave\n *  - getBodyWrapper\n *  - bodyStyle\n *\n * Deprecated:\n *  - All expanded props, move into expandable\n */\n\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { isStyleSupport } from \"rc-util/es/Dom/styleChecker\";\nimport { getTargetScrollBarSize } from \"rc-util/es/getScrollBarSize\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport getValue from \"rc-util/es/utils/get\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport Body from \"./Body\";\nimport ColGroup from \"./ColGroup\";\nimport { EXPAND_COLUMN, INTERNAL_HOOKS } from \"./constant\";\nimport TableContext, { makeImmutable } from \"./context/TableContext\";\nimport FixedHolder from \"./FixedHolder\";\nimport Footer, { FooterComponents } from \"./Footer\";\nimport Summary from \"./Footer/Summary\";\nimport Header from \"./Header/Header\";\nimport useColumns from \"./hooks/useColumns\";\nimport useExpand from \"./hooks/useExpand\";\nimport useFixedInfo from \"./hooks/useFixedInfo\";\nimport { useTimeoutLock } from \"./hooks/useFrame\";\nimport useHover from \"./hooks/useHover\";\nimport useSticky from \"./hooks/useSticky\";\nimport useStickyOffsets from \"./hooks/useStickyOffsets\";\nimport Panel from \"./Panel\";\nimport StickyScrollBar from \"./stickyScrollBar\";\nimport Column from \"./sugar/Column\";\nimport ColumnGroup from \"./sugar/ColumnGroup\";\nimport { getColumnsKey, validateValue, validNumberValue } from \"./utils/valueUtil\";\nimport { getDOM } from \"rc-util/es/Dom/findDOMNode\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nexport var DEFAULT_PREFIX = 'rc-table';\n\n// Used for conditions cache\nvar EMPTY_DATA = [];\n\n// Used for customize scroll\nvar EMPTY_SCROLL_TARGET = {};\nfunction defaultEmpty() {\n  return 'No Data';\n}\nfunction Table(tableProps, ref) {\n  var props = _objectSpread({\n    rowKey: 'key',\n    prefixCls: DEFAULT_PREFIX,\n    emptyText: defaultEmpty\n  }, tableProps);\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    rowClassName = props.rowClassName,\n    style = props.style,\n    data = props.data,\n    rowKey = props.rowKey,\n    scroll = props.scroll,\n    tableLayout = props.tableLayout,\n    direction = props.direction,\n    title = props.title,\n    footer = props.footer,\n    summary = props.summary,\n    caption = props.caption,\n    id = props.id,\n    showHeader = props.showHeader,\n    components = props.components,\n    emptyText = props.emptyText,\n    onRow = props.onRow,\n    onHeaderRow = props.onHeaderRow,\n    onScroll = props.onScroll,\n    internalHooks = props.internalHooks,\n    transformColumns = props.transformColumns,\n    internalRefs = props.internalRefs,\n    tailor = props.tailor,\n    getContainerWidth = props.getContainerWidth,\n    sticky = props.sticky,\n    _props$rowHoverable = props.rowHoverable,\n    rowHoverable = _props$rowHoverable === void 0 ? true : _props$rowHoverable;\n  var mergedData = data || EMPTY_DATA;\n  var hasData = !!mergedData.length;\n  var useInternalHooks = internalHooks === INTERNAL_HOOKS;\n\n  // ===================== Warning ======================\n  if (process.env.NODE_ENV !== 'production') {\n    ['onRowClick', 'onRowDoubleClick', 'onRowContextMenu', 'onRowMouseEnter', 'onRowMouseLeave'].forEach(function (name) {\n      warning(props[name] === undefined, \"`\".concat(name, \"` is removed, please use `onRow` instead.\"));\n    });\n    warning(!('getBodyWrapper' in props), '`getBodyWrapper` is deprecated, please use custom `components` instead.');\n  }\n\n  // ==================== Customize =====================\n  var getComponent = React.useCallback(function (path, defaultComponent) {\n    return getValue(components, path) || defaultComponent;\n  }, [components]);\n  var getRowKey = React.useMemo(function () {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n    return function (record) {\n      var key = record && record[rowKey];\n      if (process.env.NODE_ENV !== 'production') {\n        warning(key !== undefined, 'Each record in table should have a unique `key` prop, or set `rowKey` to an unique primary key.');\n      }\n      return key;\n    };\n  }, [rowKey]);\n  var customizeScrollBody = getComponent(['body']);\n\n  // ====================== Hover =======================\n  var _useHover = useHover(),\n    _useHover2 = _slicedToArray(_useHover, 3),\n    startRow = _useHover2[0],\n    endRow = _useHover2[1],\n    onHover = _useHover2[2];\n\n  // ====================== Expand ======================\n  var _useExpand = useExpand(props, mergedData, getRowKey),\n    _useExpand2 = _slicedToArray(_useExpand, 6),\n    expandableConfig = _useExpand2[0],\n    expandableType = _useExpand2[1],\n    mergedExpandedKeys = _useExpand2[2],\n    mergedExpandIcon = _useExpand2[3],\n    mergedChildrenColumnName = _useExpand2[4],\n    onTriggerExpand = _useExpand2[5];\n\n  // ====================== Column ======================\n  var scrollX = scroll === null || scroll === void 0 ? void 0 : scroll.x;\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    componentWidth = _React$useState2[0],\n    setComponentWidth = _React$useState2[1];\n  var _useColumns = useColumns(_objectSpread(_objectSpread(_objectSpread({}, props), expandableConfig), {}, {\n      expandable: !!expandableConfig.expandedRowRender,\n      columnTitle: expandableConfig.columnTitle,\n      expandedKeys: mergedExpandedKeys,\n      getRowKey: getRowKey,\n      // https://github.com/ant-design/ant-design/issues/23894\n      onTriggerExpand: onTriggerExpand,\n      expandIcon: mergedExpandIcon,\n      expandIconColumnIndex: expandableConfig.expandIconColumnIndex,\n      direction: direction,\n      scrollWidth: useInternalHooks && tailor && typeof scrollX === 'number' ? scrollX : null,\n      clientWidth: componentWidth\n    }), useInternalHooks ? transformColumns : null),\n    _useColumns2 = _slicedToArray(_useColumns, 4),\n    columns = _useColumns2[0],\n    flattenColumns = _useColumns2[1],\n    flattenScrollX = _useColumns2[2],\n    hasGapFixed = _useColumns2[3];\n  var mergedScrollX = flattenScrollX !== null && flattenScrollX !== void 0 ? flattenScrollX : scrollX;\n  var columnContext = React.useMemo(function () {\n    return {\n      columns: columns,\n      flattenColumns: flattenColumns\n    };\n  }, [columns, flattenColumns]);\n\n  // ======================= Refs =======================\n  var fullTableRef = React.useRef();\n  var scrollHeaderRef = React.useRef();\n  var scrollBodyRef = React.useRef();\n  var scrollBodyContainerRef = React.useRef();\n  React.useImperativeHandle(ref, function () {\n    return {\n      nativeElement: fullTableRef.current,\n      scrollTo: function scrollTo(config) {\n        var _scrollBodyRef$curren3;\n        if (scrollBodyRef.current instanceof HTMLElement) {\n          // Native scroll\n          var index = config.index,\n            top = config.top,\n            key = config.key;\n          if (validNumberValue(top)) {\n            var _scrollBodyRef$curren;\n            (_scrollBodyRef$curren = scrollBodyRef.current) === null || _scrollBodyRef$curren === void 0 || _scrollBodyRef$curren.scrollTo({\n              top: top\n            });\n          } else {\n            var _scrollBodyRef$curren2;\n            var mergedKey = key !== null && key !== void 0 ? key : getRowKey(mergedData[index]);\n            (_scrollBodyRef$curren2 = scrollBodyRef.current.querySelector(\"[data-row-key=\\\"\".concat(mergedKey, \"\\\"]\"))) === null || _scrollBodyRef$curren2 === void 0 || _scrollBodyRef$curren2.scrollIntoView();\n          }\n        } else if ((_scrollBodyRef$curren3 = scrollBodyRef.current) !== null && _scrollBodyRef$curren3 !== void 0 && _scrollBodyRef$curren3.scrollTo) {\n          // Pass to proxy\n          scrollBodyRef.current.scrollTo(config);\n        }\n      }\n    };\n  });\n\n  // ====================== Scroll ======================\n  var scrollSummaryRef = React.useRef();\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    pingedLeft = _React$useState4[0],\n    setPingedLeft = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    pingedRight = _React$useState6[0],\n    setPingedRight = _React$useState6[1];\n  var _React$useState7 = React.useState(new Map()),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    colsWidths = _React$useState8[0],\n    updateColsWidths = _React$useState8[1];\n\n  // Convert map to number width\n  var colsKeys = getColumnsKey(flattenColumns);\n  var pureColWidths = colsKeys.map(function (columnKey) {\n    return colsWidths.get(columnKey);\n  });\n  var colWidths = React.useMemo(function () {\n    return pureColWidths;\n  }, [pureColWidths.join('_')]);\n  var stickyOffsets = useStickyOffsets(colWidths, flattenColumns, direction);\n  var fixHeader = scroll && validateValue(scroll.y);\n  var horizonScroll = scroll && validateValue(mergedScrollX) || Boolean(expandableConfig.fixed);\n  var fixColumn = horizonScroll && flattenColumns.some(function (_ref) {\n    var fixed = _ref.fixed;\n    return fixed;\n  });\n\n  // Sticky\n  var stickyRef = React.useRef();\n  var _useSticky = useSticky(sticky, prefixCls),\n    isSticky = _useSticky.isSticky,\n    offsetHeader = _useSticky.offsetHeader,\n    offsetSummary = _useSticky.offsetSummary,\n    offsetScroll = _useSticky.offsetScroll,\n    stickyClassName = _useSticky.stickyClassName,\n    container = _useSticky.container;\n\n  // Footer (Fix footer must fixed header)\n  var summaryNode = React.useMemo(function () {\n    return summary === null || summary === void 0 ? void 0 : summary(mergedData);\n  }, [summary, mergedData]);\n  var fixFooter = (fixHeader || isSticky) && /*#__PURE__*/React.isValidElement(summaryNode) && summaryNode.type === Summary && summaryNode.props.fixed;\n\n  // Scroll\n  var scrollXStyle;\n  var scrollYStyle;\n  var scrollTableStyle;\n  if (fixHeader) {\n    scrollYStyle = {\n      overflowY: hasData ? 'scroll' : 'auto',\n      maxHeight: scroll.y\n    };\n  }\n  if (horizonScroll) {\n    scrollXStyle = {\n      overflowX: 'auto'\n    };\n    // When no vertical scrollbar, should hide it\n    // https://github.com/ant-design/ant-design/pull/20705\n    // https://github.com/ant-design/ant-design/issues/21879\n    if (!fixHeader) {\n      scrollYStyle = {\n        overflowY: 'hidden'\n      };\n    }\n    scrollTableStyle = {\n      width: mergedScrollX === true ? 'auto' : mergedScrollX,\n      minWidth: '100%'\n    };\n  }\n  var onColumnResize = React.useCallback(function (columnKey, width) {\n    updateColsWidths(function (widths) {\n      if (widths.get(columnKey) !== width) {\n        var newWidths = new Map(widths);\n        newWidths.set(columnKey, width);\n        return newWidths;\n      }\n      return widths;\n    });\n  }, []);\n  var _useTimeoutLock = useTimeoutLock(null),\n    _useTimeoutLock2 = _slicedToArray(_useTimeoutLock, 2),\n    setScrollTarget = _useTimeoutLock2[0],\n    getScrollTarget = _useTimeoutLock2[1];\n  function forceScroll(scrollLeft, target) {\n    if (!target) {\n      return;\n    }\n    if (typeof target === 'function') {\n      target(scrollLeft);\n    } else if (target.scrollLeft !== scrollLeft) {\n      target.scrollLeft = scrollLeft;\n\n      // Delay to force scroll position if not sync\n      // ref: https://github.com/ant-design/ant-design/issues/37179\n      if (target.scrollLeft !== scrollLeft) {\n        setTimeout(function () {\n          target.scrollLeft = scrollLeft;\n        }, 0);\n      }\n    }\n  }\n  var onInternalScroll = useEvent(function (_ref2) {\n    var currentTarget = _ref2.currentTarget,\n      scrollLeft = _ref2.scrollLeft;\n    var isRTL = direction === 'rtl';\n    var mergedScrollLeft = typeof scrollLeft === 'number' ? scrollLeft : currentTarget.scrollLeft;\n    var compareTarget = currentTarget || EMPTY_SCROLL_TARGET;\n    if (!getScrollTarget() || getScrollTarget() === compareTarget) {\n      var _stickyRef$current;\n      setScrollTarget(compareTarget);\n      forceScroll(mergedScrollLeft, scrollHeaderRef.current);\n      forceScroll(mergedScrollLeft, scrollBodyRef.current);\n      forceScroll(mergedScrollLeft, scrollSummaryRef.current);\n      forceScroll(mergedScrollLeft, (_stickyRef$current = stickyRef.current) === null || _stickyRef$current === void 0 ? void 0 : _stickyRef$current.setScrollLeft);\n    }\n    var measureTarget = currentTarget || scrollHeaderRef.current;\n    if (measureTarget) {\n      var scrollWidth =\n      // Should use mergedScrollX in virtual table(useInternalHooks && tailor === true)\n      useInternalHooks && tailor && typeof mergedScrollX === 'number' ? mergedScrollX : measureTarget.scrollWidth;\n      var clientWidth = measureTarget.clientWidth;\n      // There is no space to scroll\n      if (scrollWidth === clientWidth) {\n        setPingedLeft(false);\n        setPingedRight(false);\n        return;\n      }\n      if (isRTL) {\n        setPingedLeft(-mergedScrollLeft < scrollWidth - clientWidth);\n        setPingedRight(-mergedScrollLeft > 0);\n      } else {\n        setPingedLeft(mergedScrollLeft > 0);\n        setPingedRight(mergedScrollLeft < scrollWidth - clientWidth);\n      }\n    }\n  });\n  var onBodyScroll = useEvent(function (e) {\n    onInternalScroll(e);\n    onScroll === null || onScroll === void 0 || onScroll(e);\n  });\n  var triggerOnScroll = function triggerOnScroll() {\n    if (horizonScroll && scrollBodyRef.current) {\n      var _scrollBodyRef$curren4;\n      onInternalScroll({\n        currentTarget: getDOM(scrollBodyRef.current),\n        scrollLeft: (_scrollBodyRef$curren4 = scrollBodyRef.current) === null || _scrollBodyRef$curren4 === void 0 ? void 0 : _scrollBodyRef$curren4.scrollLeft\n      });\n    } else {\n      setPingedLeft(false);\n      setPingedRight(false);\n    }\n  };\n  var onFullTableResize = function onFullTableResize(_ref3) {\n    var _stickyRef$current2;\n    var width = _ref3.width;\n    (_stickyRef$current2 = stickyRef.current) === null || _stickyRef$current2 === void 0 || _stickyRef$current2.checkScrollBarVisible();\n    var mergedWidth = fullTableRef.current ? fullTableRef.current.offsetWidth : width;\n    if (useInternalHooks && getContainerWidth && fullTableRef.current) {\n      mergedWidth = getContainerWidth(fullTableRef.current, mergedWidth) || mergedWidth;\n    }\n    if (mergedWidth !== componentWidth) {\n      triggerOnScroll();\n      setComponentWidth(mergedWidth);\n    }\n  };\n\n  // Sync scroll bar when init or `horizonScroll`, `data` and `columns.length` changed\n  var mounted = React.useRef(false);\n  React.useEffect(function () {\n    // onFullTableResize will be trigger once when ResizeObserver is mounted\n    // This will reduce one duplicated triggerOnScroll time\n    if (mounted.current) {\n      triggerOnScroll();\n    }\n  }, [horizonScroll, data, columns.length]);\n  React.useEffect(function () {\n    mounted.current = true;\n  }, []);\n\n  // ===================== Effects ======================\n  var _React$useState9 = React.useState(0),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    scrollbarSize = _React$useState10[0],\n    setScrollbarSize = _React$useState10[1];\n  var _React$useState11 = React.useState(true),\n    _React$useState12 = _slicedToArray(_React$useState11, 2),\n    supportSticky = _React$useState12[0],\n    setSupportSticky = _React$useState12[1]; // Only IE not support, we mark as support first\n\n  useLayoutEffect(function () {\n    if (!tailor || !useInternalHooks) {\n      if (scrollBodyRef.current instanceof Element) {\n        setScrollbarSize(getTargetScrollBarSize(scrollBodyRef.current).width);\n      } else {\n        setScrollbarSize(getTargetScrollBarSize(scrollBodyContainerRef.current).width);\n      }\n    }\n    setSupportSticky(isStyleSupport('position', 'sticky'));\n  }, []);\n\n  // ================== INTERNAL HOOKS ==================\n  React.useEffect(function () {\n    if (useInternalHooks && internalRefs) {\n      internalRefs.body.current = scrollBodyRef.current;\n    }\n  });\n\n  // ========================================================================\n  // ==                               Render                               ==\n  // ========================================================================\n  // =================== Render: Func ===================\n  var renderFixedHeaderTable = React.useCallback(function (fixedHolderPassProps) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Header, fixedHolderPassProps), fixFooter === 'top' && /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode));\n  }, [fixFooter, summaryNode]);\n  var renderFixedFooterTable = React.useCallback(function (fixedHolderPassProps) {\n    return /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode);\n  }, [summaryNode]);\n\n  // =================== Render: Node ===================\n  var TableComponent = getComponent(['table'], 'table');\n\n  // Table layout\n  var mergedTableLayout = React.useMemo(function () {\n    if (tableLayout) {\n      return tableLayout;\n    }\n    // https://github.com/ant-design/ant-design/issues/25227\n    // When scroll.x is max-content, no need to fix table layout\n    // it's width should stretch out to fit content\n    if (fixColumn) {\n      return mergedScrollX === 'max-content' ? 'auto' : 'fixed';\n    }\n    if (fixHeader || isSticky || flattenColumns.some(function (_ref4) {\n      var ellipsis = _ref4.ellipsis;\n      return ellipsis;\n    })) {\n      return 'fixed';\n    }\n    return 'auto';\n  }, [fixHeader, fixColumn, flattenColumns, tableLayout, isSticky]);\n  var groupTableNode;\n\n  // Header props\n  var headerProps = {\n    colWidths: colWidths,\n    columCount: flattenColumns.length,\n    stickyOffsets: stickyOffsets,\n    onHeaderRow: onHeaderRow,\n    fixHeader: fixHeader,\n    scroll: scroll\n  };\n\n  // Empty\n  var emptyNode = React.useMemo(function () {\n    if (hasData) {\n      return null;\n    }\n    if (typeof emptyText === 'function') {\n      return emptyText();\n    }\n    return emptyText;\n  }, [hasData, emptyText]);\n\n  // Body\n  var bodyTable = /*#__PURE__*/React.createElement(Body, {\n    data: mergedData,\n    measureColumnWidth: fixHeader || horizonScroll || isSticky\n  });\n  var bodyColGroup = /*#__PURE__*/React.createElement(ColGroup, {\n    colWidths: flattenColumns.map(function (_ref5) {\n      var width = _ref5.width;\n      return width;\n    }),\n    columns: flattenColumns\n  });\n  var captionElement = caption !== null && caption !== undefined ? /*#__PURE__*/React.createElement(\"caption\", {\n    className: \"\".concat(prefixCls, \"-caption\")\n  }, caption) : undefined;\n  var dataProps = pickAttrs(props, {\n    data: true\n  });\n  var ariaProps = pickAttrs(props, {\n    aria: true\n  });\n  if (fixHeader || isSticky) {\n    // >>>>>> Fixed Header\n    var bodyContent;\n    if (typeof customizeScrollBody === 'function') {\n      bodyContent = customizeScrollBody(mergedData, {\n        scrollbarSize: scrollbarSize,\n        ref: scrollBodyRef,\n        onScroll: onInternalScroll\n      });\n      headerProps.colWidths = flattenColumns.map(function (_ref6, index) {\n        var width = _ref6.width;\n        var colWidth = index === flattenColumns.length - 1 ? width - scrollbarSize : width;\n        if (typeof colWidth === 'number' && !Number.isNaN(colWidth)) {\n          return colWidth;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          warning(props.columns.length === 0, 'When use `components.body` with render props. Each column should have a fixed `width` value.');\n        }\n        return 0;\n      });\n    } else {\n      bodyContent = /*#__PURE__*/React.createElement(\"div\", {\n        style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n        onScroll: onBodyScroll,\n        ref: scrollBodyRef,\n        className: classNames(\"\".concat(prefixCls, \"-body\"))\n      }, /*#__PURE__*/React.createElement(TableComponent, _extends({\n        style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n          tableLayout: mergedTableLayout\n        })\n      }, ariaProps), captionElement, bodyColGroup, bodyTable, !fixFooter && summaryNode && /*#__PURE__*/React.createElement(Footer, {\n        stickyOffsets: stickyOffsets,\n        flattenColumns: flattenColumns\n      }, summaryNode)));\n    }\n\n    // Fixed holder share the props\n    var fixedHolderProps = _objectSpread(_objectSpread(_objectSpread({\n      noData: !mergedData.length,\n      maxContentScroll: horizonScroll && mergedScrollX === 'max-content'\n    }, headerProps), columnContext), {}, {\n      direction: direction,\n      stickyClassName: stickyClassName,\n      onScroll: onInternalScroll\n    });\n    groupTableNode = /*#__PURE__*/React.createElement(React.Fragment, null, showHeader !== false && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyTopOffset: offsetHeader,\n      className: \"\".concat(prefixCls, \"-header\"),\n      ref: scrollHeaderRef\n    }), renderFixedHeaderTable), bodyContent, fixFooter && fixFooter !== 'top' && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyBottomOffset: offsetSummary,\n      className: \"\".concat(prefixCls, \"-summary\"),\n      ref: scrollSummaryRef\n    }), renderFixedFooterTable), isSticky && scrollBodyRef.current && scrollBodyRef.current instanceof Element && /*#__PURE__*/React.createElement(StickyScrollBar, {\n      ref: stickyRef,\n      offsetScroll: offsetScroll,\n      scrollBodyRef: scrollBodyRef,\n      onScroll: onInternalScroll,\n      container: container,\n      direction: direction\n    }));\n  } else {\n    // >>>>>> Unique table\n    groupTableNode = /*#__PURE__*/React.createElement(\"div\", {\n      style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n      className: classNames(\"\".concat(prefixCls, \"-content\")),\n      onScroll: onInternalScroll,\n      ref: scrollBodyRef\n    }, /*#__PURE__*/React.createElement(TableComponent, _extends({\n      style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n        tableLayout: mergedTableLayout\n      })\n    }, ariaProps), captionElement, bodyColGroup, showHeader !== false && /*#__PURE__*/React.createElement(Header, _extends({}, headerProps, columnContext)), bodyTable, summaryNode && /*#__PURE__*/React.createElement(Footer, {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns\n    }, summaryNode)));\n  }\n  var fullTable = /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(prefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), \"\".concat(prefixCls, \"-ping-left\"), pingedLeft), \"\".concat(prefixCls, \"-ping-right\"), pingedRight), \"\".concat(prefixCls, \"-layout-fixed\"), tableLayout === 'fixed'), \"\".concat(prefixCls, \"-fixed-header\"), fixHeader), \"\".concat(prefixCls, \"-fixed-column\"), fixColumn), \"\".concat(prefixCls, \"-fixed-column-gapped\"), fixColumn && hasGapFixed), \"\".concat(prefixCls, \"-scroll-horizontal\"), horizonScroll), \"\".concat(prefixCls, \"-has-fix-left\"), flattenColumns[0] && flattenColumns[0].fixed), \"\".concat(prefixCls, \"-has-fix-right\"), flattenColumns[flattenColumns.length - 1] && flattenColumns[flattenColumns.length - 1].fixed === 'right')),\n    style: style,\n    id: id,\n    ref: fullTableRef\n  }, dataProps), title && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title(mergedData)), /*#__PURE__*/React.createElement(\"div\", {\n    ref: scrollBodyContainerRef,\n    className: \"\".concat(prefixCls, \"-container\")\n  }, groupTableNode), footer && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, footer(mergedData)));\n  if (horizonScroll) {\n    fullTable = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: onFullTableResize\n    }, fullTable);\n  }\n  var fixedInfoList = useFixedInfo(flattenColumns, stickyOffsets, direction);\n  var TableContextValue = React.useMemo(function () {\n    return {\n      // Scroll\n      scrollX: mergedScrollX,\n      // Table\n      prefixCls: prefixCls,\n      getComponent: getComponent,\n      scrollbarSize: scrollbarSize,\n      direction: direction,\n      fixedInfoList: fixedInfoList,\n      isSticky: isSticky,\n      supportSticky: supportSticky,\n      componentWidth: componentWidth,\n      fixHeader: fixHeader,\n      fixColumn: fixColumn,\n      horizonScroll: horizonScroll,\n      // Body\n      tableLayout: mergedTableLayout,\n      rowClassName: rowClassName,\n      expandedRowClassName: expandableConfig.expandedRowClassName,\n      expandIcon: mergedExpandIcon,\n      expandableType: expandableType,\n      expandRowByClick: expandableConfig.expandRowByClick,\n      expandedRowRender: expandableConfig.expandedRowRender,\n      onTriggerExpand: onTriggerExpand,\n      expandIconColumnIndex: expandableConfig.expandIconColumnIndex,\n      indentSize: expandableConfig.indentSize,\n      allColumnsFixedLeft: flattenColumns.every(function (col) {\n        return col.fixed === 'left';\n      }),\n      emptyNode: emptyNode,\n      // Column\n      columns: columns,\n      flattenColumns: flattenColumns,\n      onColumnResize: onColumnResize,\n      // Row\n      hoverStartRow: startRow,\n      hoverEndRow: endRow,\n      onHover: onHover,\n      rowExpandable: expandableConfig.rowExpandable,\n      onRow: onRow,\n      getRowKey: getRowKey,\n      expandedKeys: mergedExpandedKeys,\n      childrenColumnName: mergedChildrenColumnName,\n      rowHoverable: rowHoverable\n    };\n  }, [\n  // Scroll\n  mergedScrollX,\n  // Table\n  prefixCls, getComponent, scrollbarSize, direction, fixedInfoList, isSticky, supportSticky, componentWidth, fixHeader, fixColumn, horizonScroll,\n  // Body\n  mergedTableLayout, rowClassName, expandableConfig.expandedRowClassName, mergedExpandIcon, expandableType, expandableConfig.expandRowByClick, expandableConfig.expandedRowRender, onTriggerExpand, expandableConfig.expandIconColumnIndex, expandableConfig.indentSize, emptyNode,\n  // Column\n  columns, flattenColumns, onColumnResize,\n  // Row\n  startRow, endRow, onHover, expandableConfig.rowExpandable, onRow, getRowKey, mergedExpandedKeys, mergedChildrenColumnName, rowHoverable]);\n  return /*#__PURE__*/React.createElement(TableContext.Provider, {\n    value: TableContextValue\n  }, fullTable);\n}\nvar RefTable = /*#__PURE__*/React.forwardRef(Table);\nif (process.env.NODE_ENV !== 'production') {\n  RefTable.displayName = 'Table';\n}\nexport function genTable(shouldTriggerRender) {\n  return makeImmutable(RefTable, shouldTriggerRender);\n}\nvar ImmutableTable = genTable();\nImmutableTable.EXPAND_COLUMN = EXPAND_COLUMN;\nImmutableTable.INTERNAL_HOOKS = INTERNAL_HOOKS;\nImmutableTable.Column = Column;\nImmutableTable.ColumnGroup = ColumnGroup;\nImmutableTable.Summary = FooterComponents;\nexport default ImmutableTable;", "map": {"version": 3, "names": ["_defineProperty", "_extends", "_slicedToArray", "_objectSpread", "classNames", "ResizeObserver", "isStyleSupport", "getTargetScrollBarSize", "useEvent", "pickAttrs", "getValue", "warning", "React", "Body", "ColGroup", "EXPAND_COLUMN", "INTERNAL_HOOKS", "TableContext", "makeImmutable", "FixedHolder", "Footer", "FooterComponents", "Summary", "Header", "useColumns", "useExpand", "useFixedInfo", "useTimeoutLock", "useHover", "useSticky", "useStickyOffsets", "Panel", "StickyScrollBar", "Column", "ColumnGroup", "getColumnsKey", "validate<PERSON><PERSON>ue", "validNumberValue", "getDOM", "useLayoutEffect", "DEFAULT_PREFIX", "EMPTY_DATA", "EMPTY_SCROLL_TARGET", "defaultEmpty", "Table", "tableProps", "ref", "props", "<PERSON><PERSON><PERSON>", "prefixCls", "emptyText", "className", "rowClassName", "style", "data", "scroll", "tableLayout", "direction", "title", "footer", "summary", "caption", "id", "showHeader", "components", "onRow", "onHeaderRow", "onScroll", "internalHooks", "transformColumns", "internalRefs", "tailor", "getContainer<PERSON>idth", "sticky", "_props$rowHoverable", "rowHoverable", "mergedData", "hasData", "length", "useInternalHooks", "process", "env", "NODE_ENV", "for<PERSON>ach", "name", "undefined", "concat", "getComponent", "useCallback", "path", "defaultComponent", "getRowKey", "useMemo", "record", "key", "customizeScrollBody", "_useHover", "_useHover2", "startRow", "endRow", "onHover", "_useExpand", "_useExpand2", "expandableConfig", "expandableType", "mergedExpandedKeys", "mergedExpandIcon", "mergedChildrenColumnName", "onTriggerExpand", "scrollX", "x", "_React$useState", "useState", "_React$useState2", "componentWidth", "setComponentWidth", "_useColumns", "expandable", "expandedRowRender", "columnTitle", "expandedKeys", "expandIcon", "expandIconColumnIndex", "scrollWidth", "clientWidth", "_useColumns2", "columns", "flattenColumns", "flattenScrollX", "hasGapFixed", "mergedScrollX", "columnContext", "fullTableRef", "useRef", "scrollHeaderRef", "scrollBodyRef", "scrollBodyContainerRef", "useImperativeHandle", "nativeElement", "current", "scrollTo", "config", "_scrollBodyRef$curren3", "HTMLElement", "index", "top", "_scrollBodyRef$curren", "_scrollBodyRef$curren2", "mergedKey", "querySelector", "scrollIntoView", "scrollSummaryRef", "_React$useState3", "_React$useState4", "pingedLeft", "setPingedLeft", "_React$useState5", "_React$useState6", "pingedRight", "setPingedRight", "_React$useState7", "Map", "_React$useState8", "colsWidths", "updateColsWidths", "colsKeys", "pureColWidths", "map", "column<PERSON>ey", "get", "col<PERSON><PERSON><PERSON>", "join", "stickyOffsets", "fixHeader", "y", "horizonScroll", "Boolean", "fixed", "fixColumn", "some", "_ref", "stickyRef", "_useSticky", "isSticky", "offsetHeader", "offsetSummary", "offsetScroll", "stickyClassName", "container", "summaryNode", "fixFooter", "isValidElement", "type", "scrollXStyle", "scrollYStyle", "scrollTableStyle", "overflowY", "maxHeight", "overflowX", "width", "min<PERSON><PERSON><PERSON>", "onColumnResize", "widths", "newWidths", "set", "_useTimeoutLock", "_useTimeoutLock2", "setScrollTarget", "getScrollTarget", "forceScroll", "scrollLeft", "target", "setTimeout", "onInternalScroll", "_ref2", "currentTarget", "isRTL", "mergedScrollLeft", "compareTarget", "_stickyRef$current", "setScrollLeft", "measureTarget", "onBodyScroll", "e", "triggerOnScroll", "_scrollBodyRef$curren4", "onFullTableResize", "_ref3", "_stickyRef$current2", "checkScrollBarVisible", "mergedWidth", "offsetWidth", "mounted", "useEffect", "_React$useState9", "_React$useState10", "scrollbarSize", "setScrollbarSize", "_React$useState11", "_React$useState12", "supportSticky", "setSupportSticky", "Element", "body", "renderFixedHeaderTable", "fixedHolderPassProps", "createElement", "Fragment", "renderFixedFooterTable", "TableComponent", "mergedTableLayout", "_ref4", "ellipsis", "groupTableNode", "headerProps", "columCount", "emptyNode", "bodyTable", "measureColumnWidth", "bodyColGroup", "_ref5", "captionElement", "dataProps", "ariaProps", "aria", "bodyContent", "_ref6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Number", "isNaN", "fixedHolderProps", "noData", "maxContentScroll", "stickyTopOffset", "stickyBottomOffset", "fullTable", "onResize", "fixedInfoList", "TableContextValue", "expandedRowClassName", "expandRowByClick", "indentSize", "allColumnsFixedLeft", "every", "col", "hoverStartRow", "hoverEndRow", "rowExpandable", "childrenColumnName", "Provider", "value", "RefTable", "forwardRef", "displayName", "genTable", "should<PERSON>rigger<PERSON>ender", "ImmutableTable"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-table/es/Table.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n/**\n * Feature:\n *  - fixed not need to set width\n *  - support `rowExpandable` to config row expand logic\n *  - add `summary` to support `() => ReactNode`\n *\n * Update:\n *  - `dataIndex` is `array[]` now\n *  - `expandable` wrap all the expand related props\n *\n * Removed:\n *  - expandIconAsCell\n *  - useFixedHeader\n *  - rowRef\n *  - columns[number].onCellClick\n *  - onRowClick\n *  - onRowDoubleClick\n *  - onRowMouseEnter\n *  - onRowMouseLeave\n *  - getBodyWrapper\n *  - bodyStyle\n *\n * Deprecated:\n *  - All expanded props, move into expandable\n */\n\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { isStyleSupport } from \"rc-util/es/Dom/styleChecker\";\nimport { getTargetScrollBarSize } from \"rc-util/es/getScrollBarSize\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport getValue from \"rc-util/es/utils/get\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport Body from \"./Body\";\nimport ColGroup from \"./ColGroup\";\nimport { EXPAND_COLUMN, INTERNAL_HOOKS } from \"./constant\";\nimport TableContext, { makeImmutable } from \"./context/TableContext\";\nimport FixedHolder from \"./FixedHolder\";\nimport Footer, { FooterComponents } from \"./Footer\";\nimport Summary from \"./Footer/Summary\";\nimport Header from \"./Header/Header\";\nimport useColumns from \"./hooks/useColumns\";\nimport useExpand from \"./hooks/useExpand\";\nimport useFixedInfo from \"./hooks/useFixedInfo\";\nimport { useTimeoutLock } from \"./hooks/useFrame\";\nimport useHover from \"./hooks/useHover\";\nimport useSticky from \"./hooks/useSticky\";\nimport useStickyOffsets from \"./hooks/useStickyOffsets\";\nimport Panel from \"./Panel\";\nimport StickyScrollBar from \"./stickyScrollBar\";\nimport Column from \"./sugar/Column\";\nimport ColumnGroup from \"./sugar/ColumnGroup\";\nimport { getColumnsKey, validateValue, validNumberValue } from \"./utils/valueUtil\";\nimport { getDOM } from \"rc-util/es/Dom/findDOMNode\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nexport var DEFAULT_PREFIX = 'rc-table';\n\n// Used for conditions cache\nvar EMPTY_DATA = [];\n\n// Used for customize scroll\nvar EMPTY_SCROLL_TARGET = {};\nfunction defaultEmpty() {\n  return 'No Data';\n}\nfunction Table(tableProps, ref) {\n  var props = _objectSpread({\n    rowKey: 'key',\n    prefixCls: DEFAULT_PREFIX,\n    emptyText: defaultEmpty\n  }, tableProps);\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    rowClassName = props.rowClassName,\n    style = props.style,\n    data = props.data,\n    rowKey = props.rowKey,\n    scroll = props.scroll,\n    tableLayout = props.tableLayout,\n    direction = props.direction,\n    title = props.title,\n    footer = props.footer,\n    summary = props.summary,\n    caption = props.caption,\n    id = props.id,\n    showHeader = props.showHeader,\n    components = props.components,\n    emptyText = props.emptyText,\n    onRow = props.onRow,\n    onHeaderRow = props.onHeaderRow,\n    onScroll = props.onScroll,\n    internalHooks = props.internalHooks,\n    transformColumns = props.transformColumns,\n    internalRefs = props.internalRefs,\n    tailor = props.tailor,\n    getContainerWidth = props.getContainerWidth,\n    sticky = props.sticky,\n    _props$rowHoverable = props.rowHoverable,\n    rowHoverable = _props$rowHoverable === void 0 ? true : _props$rowHoverable;\n  var mergedData = data || EMPTY_DATA;\n  var hasData = !!mergedData.length;\n  var useInternalHooks = internalHooks === INTERNAL_HOOKS;\n\n  // ===================== Warning ======================\n  if (process.env.NODE_ENV !== 'production') {\n    ['onRowClick', 'onRowDoubleClick', 'onRowContextMenu', 'onRowMouseEnter', 'onRowMouseLeave'].forEach(function (name) {\n      warning(props[name] === undefined, \"`\".concat(name, \"` is removed, please use `onRow` instead.\"));\n    });\n    warning(!('getBodyWrapper' in props), '`getBodyWrapper` is deprecated, please use custom `components` instead.');\n  }\n\n  // ==================== Customize =====================\n  var getComponent = React.useCallback(function (path, defaultComponent) {\n    return getValue(components, path) || defaultComponent;\n  }, [components]);\n  var getRowKey = React.useMemo(function () {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n    return function (record) {\n      var key = record && record[rowKey];\n      if (process.env.NODE_ENV !== 'production') {\n        warning(key !== undefined, 'Each record in table should have a unique `key` prop, or set `rowKey` to an unique primary key.');\n      }\n      return key;\n    };\n  }, [rowKey]);\n  var customizeScrollBody = getComponent(['body']);\n\n  // ====================== Hover =======================\n  var _useHover = useHover(),\n    _useHover2 = _slicedToArray(_useHover, 3),\n    startRow = _useHover2[0],\n    endRow = _useHover2[1],\n    onHover = _useHover2[2];\n\n  // ====================== Expand ======================\n  var _useExpand = useExpand(props, mergedData, getRowKey),\n    _useExpand2 = _slicedToArray(_useExpand, 6),\n    expandableConfig = _useExpand2[0],\n    expandableType = _useExpand2[1],\n    mergedExpandedKeys = _useExpand2[2],\n    mergedExpandIcon = _useExpand2[3],\n    mergedChildrenColumnName = _useExpand2[4],\n    onTriggerExpand = _useExpand2[5];\n\n  // ====================== Column ======================\n  var scrollX = scroll === null || scroll === void 0 ? void 0 : scroll.x;\n  var _React$useState = React.useState(0),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    componentWidth = _React$useState2[0],\n    setComponentWidth = _React$useState2[1];\n  var _useColumns = useColumns(_objectSpread(_objectSpread(_objectSpread({}, props), expandableConfig), {}, {\n      expandable: !!expandableConfig.expandedRowRender,\n      columnTitle: expandableConfig.columnTitle,\n      expandedKeys: mergedExpandedKeys,\n      getRowKey: getRowKey,\n      // https://github.com/ant-design/ant-design/issues/23894\n      onTriggerExpand: onTriggerExpand,\n      expandIcon: mergedExpandIcon,\n      expandIconColumnIndex: expandableConfig.expandIconColumnIndex,\n      direction: direction,\n      scrollWidth: useInternalHooks && tailor && typeof scrollX === 'number' ? scrollX : null,\n      clientWidth: componentWidth\n    }), useInternalHooks ? transformColumns : null),\n    _useColumns2 = _slicedToArray(_useColumns, 4),\n    columns = _useColumns2[0],\n    flattenColumns = _useColumns2[1],\n    flattenScrollX = _useColumns2[2],\n    hasGapFixed = _useColumns2[3];\n  var mergedScrollX = flattenScrollX !== null && flattenScrollX !== void 0 ? flattenScrollX : scrollX;\n  var columnContext = React.useMemo(function () {\n    return {\n      columns: columns,\n      flattenColumns: flattenColumns\n    };\n  }, [columns, flattenColumns]);\n\n  // ======================= Refs =======================\n  var fullTableRef = React.useRef();\n  var scrollHeaderRef = React.useRef();\n  var scrollBodyRef = React.useRef();\n  var scrollBodyContainerRef = React.useRef();\n  React.useImperativeHandle(ref, function () {\n    return {\n      nativeElement: fullTableRef.current,\n      scrollTo: function scrollTo(config) {\n        var _scrollBodyRef$curren3;\n        if (scrollBodyRef.current instanceof HTMLElement) {\n          // Native scroll\n          var index = config.index,\n            top = config.top,\n            key = config.key;\n          if (validNumberValue(top)) {\n            var _scrollBodyRef$curren;\n            (_scrollBodyRef$curren = scrollBodyRef.current) === null || _scrollBodyRef$curren === void 0 || _scrollBodyRef$curren.scrollTo({\n              top: top\n            });\n          } else {\n            var _scrollBodyRef$curren2;\n            var mergedKey = key !== null && key !== void 0 ? key : getRowKey(mergedData[index]);\n            (_scrollBodyRef$curren2 = scrollBodyRef.current.querySelector(\"[data-row-key=\\\"\".concat(mergedKey, \"\\\"]\"))) === null || _scrollBodyRef$curren2 === void 0 || _scrollBodyRef$curren2.scrollIntoView();\n          }\n        } else if ((_scrollBodyRef$curren3 = scrollBodyRef.current) !== null && _scrollBodyRef$curren3 !== void 0 && _scrollBodyRef$curren3.scrollTo) {\n          // Pass to proxy\n          scrollBodyRef.current.scrollTo(config);\n        }\n      }\n    };\n  });\n\n  // ====================== Scroll ======================\n  var scrollSummaryRef = React.useRef();\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    pingedLeft = _React$useState4[0],\n    setPingedLeft = _React$useState4[1];\n  var _React$useState5 = React.useState(false),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    pingedRight = _React$useState6[0],\n    setPingedRight = _React$useState6[1];\n  var _React$useState7 = React.useState(new Map()),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    colsWidths = _React$useState8[0],\n    updateColsWidths = _React$useState8[1];\n\n  // Convert map to number width\n  var colsKeys = getColumnsKey(flattenColumns);\n  var pureColWidths = colsKeys.map(function (columnKey) {\n    return colsWidths.get(columnKey);\n  });\n  var colWidths = React.useMemo(function () {\n    return pureColWidths;\n  }, [pureColWidths.join('_')]);\n  var stickyOffsets = useStickyOffsets(colWidths, flattenColumns, direction);\n  var fixHeader = scroll && validateValue(scroll.y);\n  var horizonScroll = scroll && validateValue(mergedScrollX) || Boolean(expandableConfig.fixed);\n  var fixColumn = horizonScroll && flattenColumns.some(function (_ref) {\n    var fixed = _ref.fixed;\n    return fixed;\n  });\n\n  // Sticky\n  var stickyRef = React.useRef();\n  var _useSticky = useSticky(sticky, prefixCls),\n    isSticky = _useSticky.isSticky,\n    offsetHeader = _useSticky.offsetHeader,\n    offsetSummary = _useSticky.offsetSummary,\n    offsetScroll = _useSticky.offsetScroll,\n    stickyClassName = _useSticky.stickyClassName,\n    container = _useSticky.container;\n\n  // Footer (Fix footer must fixed header)\n  var summaryNode = React.useMemo(function () {\n    return summary === null || summary === void 0 ? void 0 : summary(mergedData);\n  }, [summary, mergedData]);\n  var fixFooter = (fixHeader || isSticky) && /*#__PURE__*/React.isValidElement(summaryNode) && summaryNode.type === Summary && summaryNode.props.fixed;\n\n  // Scroll\n  var scrollXStyle;\n  var scrollYStyle;\n  var scrollTableStyle;\n  if (fixHeader) {\n    scrollYStyle = {\n      overflowY: hasData ? 'scroll' : 'auto',\n      maxHeight: scroll.y\n    };\n  }\n  if (horizonScroll) {\n    scrollXStyle = {\n      overflowX: 'auto'\n    };\n    // When no vertical scrollbar, should hide it\n    // https://github.com/ant-design/ant-design/pull/20705\n    // https://github.com/ant-design/ant-design/issues/21879\n    if (!fixHeader) {\n      scrollYStyle = {\n        overflowY: 'hidden'\n      };\n    }\n    scrollTableStyle = {\n      width: mergedScrollX === true ? 'auto' : mergedScrollX,\n      minWidth: '100%'\n    };\n  }\n  var onColumnResize = React.useCallback(function (columnKey, width) {\n    updateColsWidths(function (widths) {\n      if (widths.get(columnKey) !== width) {\n        var newWidths = new Map(widths);\n        newWidths.set(columnKey, width);\n        return newWidths;\n      }\n      return widths;\n    });\n  }, []);\n  var _useTimeoutLock = useTimeoutLock(null),\n    _useTimeoutLock2 = _slicedToArray(_useTimeoutLock, 2),\n    setScrollTarget = _useTimeoutLock2[0],\n    getScrollTarget = _useTimeoutLock2[1];\n  function forceScroll(scrollLeft, target) {\n    if (!target) {\n      return;\n    }\n    if (typeof target === 'function') {\n      target(scrollLeft);\n    } else if (target.scrollLeft !== scrollLeft) {\n      target.scrollLeft = scrollLeft;\n\n      // Delay to force scroll position if not sync\n      // ref: https://github.com/ant-design/ant-design/issues/37179\n      if (target.scrollLeft !== scrollLeft) {\n        setTimeout(function () {\n          target.scrollLeft = scrollLeft;\n        }, 0);\n      }\n    }\n  }\n  var onInternalScroll = useEvent(function (_ref2) {\n    var currentTarget = _ref2.currentTarget,\n      scrollLeft = _ref2.scrollLeft;\n    var isRTL = direction === 'rtl';\n    var mergedScrollLeft = typeof scrollLeft === 'number' ? scrollLeft : currentTarget.scrollLeft;\n    var compareTarget = currentTarget || EMPTY_SCROLL_TARGET;\n    if (!getScrollTarget() || getScrollTarget() === compareTarget) {\n      var _stickyRef$current;\n      setScrollTarget(compareTarget);\n      forceScroll(mergedScrollLeft, scrollHeaderRef.current);\n      forceScroll(mergedScrollLeft, scrollBodyRef.current);\n      forceScroll(mergedScrollLeft, scrollSummaryRef.current);\n      forceScroll(mergedScrollLeft, (_stickyRef$current = stickyRef.current) === null || _stickyRef$current === void 0 ? void 0 : _stickyRef$current.setScrollLeft);\n    }\n    var measureTarget = currentTarget || scrollHeaderRef.current;\n    if (measureTarget) {\n      var scrollWidth =\n      // Should use mergedScrollX in virtual table(useInternalHooks && tailor === true)\n      useInternalHooks && tailor && typeof mergedScrollX === 'number' ? mergedScrollX : measureTarget.scrollWidth;\n      var clientWidth = measureTarget.clientWidth;\n      // There is no space to scroll\n      if (scrollWidth === clientWidth) {\n        setPingedLeft(false);\n        setPingedRight(false);\n        return;\n      }\n      if (isRTL) {\n        setPingedLeft(-mergedScrollLeft < scrollWidth - clientWidth);\n        setPingedRight(-mergedScrollLeft > 0);\n      } else {\n        setPingedLeft(mergedScrollLeft > 0);\n        setPingedRight(mergedScrollLeft < scrollWidth - clientWidth);\n      }\n    }\n  });\n  var onBodyScroll = useEvent(function (e) {\n    onInternalScroll(e);\n    onScroll === null || onScroll === void 0 || onScroll(e);\n  });\n  var triggerOnScroll = function triggerOnScroll() {\n    if (horizonScroll && scrollBodyRef.current) {\n      var _scrollBodyRef$curren4;\n      onInternalScroll({\n        currentTarget: getDOM(scrollBodyRef.current),\n        scrollLeft: (_scrollBodyRef$curren4 = scrollBodyRef.current) === null || _scrollBodyRef$curren4 === void 0 ? void 0 : _scrollBodyRef$curren4.scrollLeft\n      });\n    } else {\n      setPingedLeft(false);\n      setPingedRight(false);\n    }\n  };\n  var onFullTableResize = function onFullTableResize(_ref3) {\n    var _stickyRef$current2;\n    var width = _ref3.width;\n    (_stickyRef$current2 = stickyRef.current) === null || _stickyRef$current2 === void 0 || _stickyRef$current2.checkScrollBarVisible();\n    var mergedWidth = fullTableRef.current ? fullTableRef.current.offsetWidth : width;\n    if (useInternalHooks && getContainerWidth && fullTableRef.current) {\n      mergedWidth = getContainerWidth(fullTableRef.current, mergedWidth) || mergedWidth;\n    }\n    if (mergedWidth !== componentWidth) {\n      triggerOnScroll();\n      setComponentWidth(mergedWidth);\n    }\n  };\n\n  // Sync scroll bar when init or `horizonScroll`, `data` and `columns.length` changed\n  var mounted = React.useRef(false);\n  React.useEffect(function () {\n    // onFullTableResize will be trigger once when ResizeObserver is mounted\n    // This will reduce one duplicated triggerOnScroll time\n    if (mounted.current) {\n      triggerOnScroll();\n    }\n  }, [horizonScroll, data, columns.length]);\n  React.useEffect(function () {\n    mounted.current = true;\n  }, []);\n\n  // ===================== Effects ======================\n  var _React$useState9 = React.useState(0),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    scrollbarSize = _React$useState10[0],\n    setScrollbarSize = _React$useState10[1];\n  var _React$useState11 = React.useState(true),\n    _React$useState12 = _slicedToArray(_React$useState11, 2),\n    supportSticky = _React$useState12[0],\n    setSupportSticky = _React$useState12[1]; // Only IE not support, we mark as support first\n\n  useLayoutEffect(function () {\n    if (!tailor || !useInternalHooks) {\n      if (scrollBodyRef.current instanceof Element) {\n        setScrollbarSize(getTargetScrollBarSize(scrollBodyRef.current).width);\n      } else {\n        setScrollbarSize(getTargetScrollBarSize(scrollBodyContainerRef.current).width);\n      }\n    }\n    setSupportSticky(isStyleSupport('position', 'sticky'));\n  }, []);\n\n  // ================== INTERNAL HOOKS ==================\n  React.useEffect(function () {\n    if (useInternalHooks && internalRefs) {\n      internalRefs.body.current = scrollBodyRef.current;\n    }\n  });\n\n  // ========================================================================\n  // ==                               Render                               ==\n  // ========================================================================\n  // =================== Render: Func ===================\n  var renderFixedHeaderTable = React.useCallback(function (fixedHolderPassProps) {\n    return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Header, fixedHolderPassProps), fixFooter === 'top' && /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode));\n  }, [fixFooter, summaryNode]);\n  var renderFixedFooterTable = React.useCallback(function (fixedHolderPassProps) {\n    return /*#__PURE__*/React.createElement(Footer, fixedHolderPassProps, summaryNode);\n  }, [summaryNode]);\n\n  // =================== Render: Node ===================\n  var TableComponent = getComponent(['table'], 'table');\n\n  // Table layout\n  var mergedTableLayout = React.useMemo(function () {\n    if (tableLayout) {\n      return tableLayout;\n    }\n    // https://github.com/ant-design/ant-design/issues/25227\n    // When scroll.x is max-content, no need to fix table layout\n    // it's width should stretch out to fit content\n    if (fixColumn) {\n      return mergedScrollX === 'max-content' ? 'auto' : 'fixed';\n    }\n    if (fixHeader || isSticky || flattenColumns.some(function (_ref4) {\n      var ellipsis = _ref4.ellipsis;\n      return ellipsis;\n    })) {\n      return 'fixed';\n    }\n    return 'auto';\n  }, [fixHeader, fixColumn, flattenColumns, tableLayout, isSticky]);\n  var groupTableNode;\n\n  // Header props\n  var headerProps = {\n    colWidths: colWidths,\n    columCount: flattenColumns.length,\n    stickyOffsets: stickyOffsets,\n    onHeaderRow: onHeaderRow,\n    fixHeader: fixHeader,\n    scroll: scroll\n  };\n\n  // Empty\n  var emptyNode = React.useMemo(function () {\n    if (hasData) {\n      return null;\n    }\n    if (typeof emptyText === 'function') {\n      return emptyText();\n    }\n    return emptyText;\n  }, [hasData, emptyText]);\n\n  // Body\n  var bodyTable = /*#__PURE__*/React.createElement(Body, {\n    data: mergedData,\n    measureColumnWidth: fixHeader || horizonScroll || isSticky\n  });\n  var bodyColGroup = /*#__PURE__*/React.createElement(ColGroup, {\n    colWidths: flattenColumns.map(function (_ref5) {\n      var width = _ref5.width;\n      return width;\n    }),\n    columns: flattenColumns\n  });\n  var captionElement = caption !== null && caption !== undefined ? /*#__PURE__*/React.createElement(\"caption\", {\n    className: \"\".concat(prefixCls, \"-caption\")\n  }, caption) : undefined;\n  var dataProps = pickAttrs(props, {\n    data: true\n  });\n  var ariaProps = pickAttrs(props, {\n    aria: true\n  });\n  if (fixHeader || isSticky) {\n    // >>>>>> Fixed Header\n    var bodyContent;\n    if (typeof customizeScrollBody === 'function') {\n      bodyContent = customizeScrollBody(mergedData, {\n        scrollbarSize: scrollbarSize,\n        ref: scrollBodyRef,\n        onScroll: onInternalScroll\n      });\n      headerProps.colWidths = flattenColumns.map(function (_ref6, index) {\n        var width = _ref6.width;\n        var colWidth = index === flattenColumns.length - 1 ? width - scrollbarSize : width;\n        if (typeof colWidth === 'number' && !Number.isNaN(colWidth)) {\n          return colWidth;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          warning(props.columns.length === 0, 'When use `components.body` with render props. Each column should have a fixed `width` value.');\n        }\n        return 0;\n      });\n    } else {\n      bodyContent = /*#__PURE__*/React.createElement(\"div\", {\n        style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n        onScroll: onBodyScroll,\n        ref: scrollBodyRef,\n        className: classNames(\"\".concat(prefixCls, \"-body\"))\n      }, /*#__PURE__*/React.createElement(TableComponent, _extends({\n        style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n          tableLayout: mergedTableLayout\n        })\n      }, ariaProps), captionElement, bodyColGroup, bodyTable, !fixFooter && summaryNode && /*#__PURE__*/React.createElement(Footer, {\n        stickyOffsets: stickyOffsets,\n        flattenColumns: flattenColumns\n      }, summaryNode)));\n    }\n\n    // Fixed holder share the props\n    var fixedHolderProps = _objectSpread(_objectSpread(_objectSpread({\n      noData: !mergedData.length,\n      maxContentScroll: horizonScroll && mergedScrollX === 'max-content'\n    }, headerProps), columnContext), {}, {\n      direction: direction,\n      stickyClassName: stickyClassName,\n      onScroll: onInternalScroll\n    });\n    groupTableNode = /*#__PURE__*/React.createElement(React.Fragment, null, showHeader !== false && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyTopOffset: offsetHeader,\n      className: \"\".concat(prefixCls, \"-header\"),\n      ref: scrollHeaderRef\n    }), renderFixedHeaderTable), bodyContent, fixFooter && fixFooter !== 'top' && /*#__PURE__*/React.createElement(FixedHolder, _extends({}, fixedHolderProps, {\n      stickyBottomOffset: offsetSummary,\n      className: \"\".concat(prefixCls, \"-summary\"),\n      ref: scrollSummaryRef\n    }), renderFixedFooterTable), isSticky && scrollBodyRef.current && scrollBodyRef.current instanceof Element && /*#__PURE__*/React.createElement(StickyScrollBar, {\n      ref: stickyRef,\n      offsetScroll: offsetScroll,\n      scrollBodyRef: scrollBodyRef,\n      onScroll: onInternalScroll,\n      container: container,\n      direction: direction\n    }));\n  } else {\n    // >>>>>> Unique table\n    groupTableNode = /*#__PURE__*/React.createElement(\"div\", {\n      style: _objectSpread(_objectSpread({}, scrollXStyle), scrollYStyle),\n      className: classNames(\"\".concat(prefixCls, \"-content\")),\n      onScroll: onInternalScroll,\n      ref: scrollBodyRef\n    }, /*#__PURE__*/React.createElement(TableComponent, _extends({\n      style: _objectSpread(_objectSpread({}, scrollTableStyle), {}, {\n        tableLayout: mergedTableLayout\n      })\n    }, ariaProps), captionElement, bodyColGroup, showHeader !== false && /*#__PURE__*/React.createElement(Header, _extends({}, headerProps, columnContext)), bodyTable, summaryNode && /*#__PURE__*/React.createElement(Footer, {\n      stickyOffsets: stickyOffsets,\n      flattenColumns: flattenColumns\n    }, summaryNode)));\n  }\n  var fullTable = /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(prefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-rtl\"), direction === 'rtl'), \"\".concat(prefixCls, \"-ping-left\"), pingedLeft), \"\".concat(prefixCls, \"-ping-right\"), pingedRight), \"\".concat(prefixCls, \"-layout-fixed\"), tableLayout === 'fixed'), \"\".concat(prefixCls, \"-fixed-header\"), fixHeader), \"\".concat(prefixCls, \"-fixed-column\"), fixColumn), \"\".concat(prefixCls, \"-fixed-column-gapped\"), fixColumn && hasGapFixed), \"\".concat(prefixCls, \"-scroll-horizontal\"), horizonScroll), \"\".concat(prefixCls, \"-has-fix-left\"), flattenColumns[0] && flattenColumns[0].fixed), \"\".concat(prefixCls, \"-has-fix-right\"), flattenColumns[flattenColumns.length - 1] && flattenColumns[flattenColumns.length - 1].fixed === 'right')),\n    style: style,\n    id: id,\n    ref: fullTableRef\n  }, dataProps), title && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-title\")\n  }, title(mergedData)), /*#__PURE__*/React.createElement(\"div\", {\n    ref: scrollBodyContainerRef,\n    className: \"\".concat(prefixCls, \"-container\")\n  }, groupTableNode), footer && /*#__PURE__*/React.createElement(Panel, {\n    className: \"\".concat(prefixCls, \"-footer\")\n  }, footer(mergedData)));\n  if (horizonScroll) {\n    fullTable = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: onFullTableResize\n    }, fullTable);\n  }\n  var fixedInfoList = useFixedInfo(flattenColumns, stickyOffsets, direction);\n  var TableContextValue = React.useMemo(function () {\n    return {\n      // Scroll\n      scrollX: mergedScrollX,\n      // Table\n      prefixCls: prefixCls,\n      getComponent: getComponent,\n      scrollbarSize: scrollbarSize,\n      direction: direction,\n      fixedInfoList: fixedInfoList,\n      isSticky: isSticky,\n      supportSticky: supportSticky,\n      componentWidth: componentWidth,\n      fixHeader: fixHeader,\n      fixColumn: fixColumn,\n      horizonScroll: horizonScroll,\n      // Body\n      tableLayout: mergedTableLayout,\n      rowClassName: rowClassName,\n      expandedRowClassName: expandableConfig.expandedRowClassName,\n      expandIcon: mergedExpandIcon,\n      expandableType: expandableType,\n      expandRowByClick: expandableConfig.expandRowByClick,\n      expandedRowRender: expandableConfig.expandedRowRender,\n      onTriggerExpand: onTriggerExpand,\n      expandIconColumnIndex: expandableConfig.expandIconColumnIndex,\n      indentSize: expandableConfig.indentSize,\n      allColumnsFixedLeft: flattenColumns.every(function (col) {\n        return col.fixed === 'left';\n      }),\n      emptyNode: emptyNode,\n      // Column\n      columns: columns,\n      flattenColumns: flattenColumns,\n      onColumnResize: onColumnResize,\n      // Row\n      hoverStartRow: startRow,\n      hoverEndRow: endRow,\n      onHover: onHover,\n      rowExpandable: expandableConfig.rowExpandable,\n      onRow: onRow,\n      getRowKey: getRowKey,\n      expandedKeys: mergedExpandedKeys,\n      childrenColumnName: mergedChildrenColumnName,\n      rowHoverable: rowHoverable\n    };\n  }, [\n  // Scroll\n  mergedScrollX,\n  // Table\n  prefixCls, getComponent, scrollbarSize, direction, fixedInfoList, isSticky, supportSticky, componentWidth, fixHeader, fixColumn, horizonScroll,\n  // Body\n  mergedTableLayout, rowClassName, expandableConfig.expandedRowClassName, mergedExpandIcon, expandableType, expandableConfig.expandRowByClick, expandableConfig.expandedRowRender, onTriggerExpand, expandableConfig.expandIconColumnIndex, expandableConfig.indentSize, emptyNode,\n  // Column\n  columns, flattenColumns, onColumnResize,\n  // Row\n  startRow, endRow, onHover, expandableConfig.rowExpandable, onRow, getRowKey, mergedExpandedKeys, mergedChildrenColumnName, rowHoverable]);\n  return /*#__PURE__*/React.createElement(TableContext.Provider, {\n    value: TableContextValue\n  }, fullTable);\n}\nvar RefTable = /*#__PURE__*/React.forwardRef(Table);\nif (process.env.NODE_ENV !== 'production') {\n  RefTable.displayName = 'Table';\n}\nexport function genTable(shouldTriggerRender) {\n  return makeImmutable(RefTable, shouldTriggerRender);\n}\nvar ImmutableTable = genTable();\nImmutableTable.EXPAND_COLUMN = EXPAND_COLUMN;\nImmutableTable.INTERNAL_HOOKS = INTERNAL_HOOKS;\nImmutableTable.Column = Column;\nImmutableTable.ColumnGroup = ColumnGroup;\nImmutableTable.Summary = FooterComponents;\nexport default ImmutableTable;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,aAAa,MAAM,0CAA0C;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,aAAa,EAAEC,cAAc,QAAQ,YAAY;AAC1D,OAAOC,YAAY,IAAIC,aAAa,QAAQ,wBAAwB;AACpE,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,MAAM,IAAIC,gBAAgB,QAAQ,UAAU;AACnD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,aAAa,EAAEC,aAAa,EAAEC,gBAAgB,QAAQ,mBAAmB;AAClF,SAASC,MAAM,QAAQ,4BAA4B;AACnD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAO,IAAIC,cAAc,GAAG,UAAU;;AAEtC;AACA,IAAIC,UAAU,GAAG,EAAE;;AAEnB;AACA,IAAIC,mBAAmB,GAAG,CAAC,CAAC;AAC5B,SAASC,YAAYA,CAAA,EAAG;EACtB,OAAO,SAAS;AAClB;AACA,SAASC,KAAKA,CAACC,UAAU,EAAEC,GAAG,EAAE;EAC9B,IAAIC,KAAK,GAAG5C,aAAa,CAAC;IACxB6C,MAAM,EAAE,KAAK;IACbC,SAAS,EAAET,cAAc;IACzBU,SAAS,EAAEP;EACb,CAAC,EAAEE,UAAU,CAAC;EACd,IAAII,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BE,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,YAAY,GAAGL,KAAK,CAACK,YAAY;IACjCC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,IAAI,GAAGP,KAAK,CAACO,IAAI;IACjBN,MAAM,GAAGD,KAAK,CAACC,MAAM;IACrBO,MAAM,GAAGR,KAAK,CAACQ,MAAM;IACrBC,WAAW,GAAGT,KAAK,CAACS,WAAW;IAC/BC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,KAAK,GAAGX,KAAK,CAACW,KAAK;IACnBC,MAAM,GAAGZ,KAAK,CAACY,MAAM;IACrBC,OAAO,GAAGb,KAAK,CAACa,OAAO;IACvBC,OAAO,GAAGd,KAAK,CAACc,OAAO;IACvBC,EAAE,GAAGf,KAAK,CAACe,EAAE;IACbC,UAAU,GAAGhB,KAAK,CAACgB,UAAU;IAC7BC,UAAU,GAAGjB,KAAK,CAACiB,UAAU;IAC7Bd,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3Be,KAAK,GAAGlB,KAAK,CAACkB,KAAK;IACnBC,WAAW,GAAGnB,KAAK,CAACmB,WAAW;IAC/BC,QAAQ,GAAGpB,KAAK,CAACoB,QAAQ;IACzBC,aAAa,GAAGrB,KAAK,CAACqB,aAAa;IACnCC,gBAAgB,GAAGtB,KAAK,CAACsB,gBAAgB;IACzCC,YAAY,GAAGvB,KAAK,CAACuB,YAAY;IACjCC,MAAM,GAAGxB,KAAK,CAACwB,MAAM;IACrBC,iBAAiB,GAAGzB,KAAK,CAACyB,iBAAiB;IAC3CC,MAAM,GAAG1B,KAAK,CAAC0B,MAAM;IACrBC,mBAAmB,GAAG3B,KAAK,CAAC4B,YAAY;IACxCA,YAAY,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,mBAAmB;EAC5E,IAAIE,UAAU,GAAGtB,IAAI,IAAIb,UAAU;EACnC,IAAIoC,OAAO,GAAG,CAAC,CAACD,UAAU,CAACE,MAAM;EACjC,IAAIC,gBAAgB,GAAGX,aAAa,KAAKpD,cAAc;;EAEvD;EACA,IAAIgE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,CAAC,YAAY,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAACC,OAAO,CAAC,UAAUC,IAAI,EAAE;MACnHzE,OAAO,CAACoC,KAAK,CAACqC,IAAI,CAAC,KAAKC,SAAS,EAAE,GAAG,CAACC,MAAM,CAACF,IAAI,EAAE,2CAA2C,CAAC,CAAC;IACnG,CAAC,CAAC;IACFzE,OAAO,CAAC,EAAE,gBAAgB,IAAIoC,KAAK,CAAC,EAAE,yEAAyE,CAAC;EAClH;;EAEA;EACA,IAAIwC,YAAY,GAAG3E,KAAK,CAAC4E,WAAW,CAAC,UAAUC,IAAI,EAAEC,gBAAgB,EAAE;IACrE,OAAOhF,QAAQ,CAACsD,UAAU,EAAEyB,IAAI,CAAC,IAAIC,gBAAgB;EACvD,CAAC,EAAE,CAAC1B,UAAU,CAAC,CAAC;EAChB,IAAI2B,SAAS,GAAG/E,KAAK,CAACgF,OAAO,CAAC,YAAY;IACxC,IAAI,OAAO5C,MAAM,KAAK,UAAU,EAAE;MAChC,OAAOA,MAAM;IACf;IACA,OAAO,UAAU6C,MAAM,EAAE;MACvB,IAAIC,GAAG,GAAGD,MAAM,IAAIA,MAAM,CAAC7C,MAAM,CAAC;MAClC,IAAIgC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCvE,OAAO,CAACmF,GAAG,KAAKT,SAAS,EAAE,iGAAiG,CAAC;MAC/H;MACA,OAAOS,GAAG;IACZ,CAAC;EACH,CAAC,EAAE,CAAC9C,MAAM,CAAC,CAAC;EACZ,IAAI+C,mBAAmB,GAAGR,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC;;EAEhD;EACA,IAAIS,SAAS,GAAGpE,QAAQ,CAAC,CAAC;IACxBqE,UAAU,GAAG/F,cAAc,CAAC8F,SAAS,EAAE,CAAC,CAAC;IACzCE,QAAQ,GAAGD,UAAU,CAAC,CAAC,CAAC;IACxBE,MAAM,GAAGF,UAAU,CAAC,CAAC,CAAC;IACtBG,OAAO,GAAGH,UAAU,CAAC,CAAC,CAAC;;EAEzB;EACA,IAAII,UAAU,GAAG5E,SAAS,CAACsB,KAAK,EAAE6B,UAAU,EAAEe,SAAS,CAAC;IACtDW,WAAW,GAAGpG,cAAc,CAACmG,UAAU,EAAE,CAAC,CAAC;IAC3CE,gBAAgB,GAAGD,WAAW,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,WAAW,CAAC,CAAC,CAAC;IAC/BG,kBAAkB,GAAGH,WAAW,CAAC,CAAC,CAAC;IACnCI,gBAAgB,GAAGJ,WAAW,CAAC,CAAC,CAAC;IACjCK,wBAAwB,GAAGL,WAAW,CAAC,CAAC,CAAC;IACzCM,eAAe,GAAGN,WAAW,CAAC,CAAC,CAAC;;EAElC;EACA,IAAIO,OAAO,GAAGtD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACuD,CAAC;EACtE,IAAIC,eAAe,GAAGnG,KAAK,CAACoG,QAAQ,CAAC,CAAC,CAAC;IACrCC,gBAAgB,GAAG/G,cAAc,CAAC6G,eAAe,EAAE,CAAC,CAAC;IACrDG,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACzC,IAAIG,WAAW,GAAG5F,UAAU,CAACrB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4C,KAAK,CAAC,EAAEwD,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;MACtGc,UAAU,EAAE,CAAC,CAACd,gBAAgB,CAACe,iBAAiB;MAChDC,WAAW,EAAEhB,gBAAgB,CAACgB,WAAW;MACzCC,YAAY,EAAEf,kBAAkB;MAChCd,SAAS,EAAEA,SAAS;MACpB;MACAiB,eAAe,EAAEA,eAAe;MAChCa,UAAU,EAAEf,gBAAgB;MAC5BgB,qBAAqB,EAAEnB,gBAAgB,CAACmB,qBAAqB;MAC7DjE,SAAS,EAAEA,SAAS;MACpBkE,WAAW,EAAE5C,gBAAgB,IAAIR,MAAM,IAAI,OAAOsC,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAG,IAAI;MACvFe,WAAW,EAAEV;IACf,CAAC,CAAC,EAAEnC,gBAAgB,GAAGV,gBAAgB,GAAG,IAAI,CAAC;IAC/CwD,YAAY,GAAG3H,cAAc,CAACkH,WAAW,EAAE,CAAC,CAAC;IAC7CU,OAAO,GAAGD,YAAY,CAAC,CAAC,CAAC;IACzBE,cAAc,GAAGF,YAAY,CAAC,CAAC,CAAC;IAChCG,cAAc,GAAGH,YAAY,CAAC,CAAC,CAAC;IAChCI,WAAW,GAAGJ,YAAY,CAAC,CAAC,CAAC;EAC/B,IAAIK,aAAa,GAAGF,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGnB,OAAO;EACnG,IAAIsB,aAAa,GAAGvH,KAAK,CAACgF,OAAO,CAAC,YAAY;IAC5C,OAAO;MACLkC,OAAO,EAAEA,OAAO;MAChBC,cAAc,EAAEA;IAClB,CAAC;EACH,CAAC,EAAE,CAACD,OAAO,EAAEC,cAAc,CAAC,CAAC;;EAE7B;EACA,IAAIK,YAAY,GAAGxH,KAAK,CAACyH,MAAM,CAAC,CAAC;EACjC,IAAIC,eAAe,GAAG1H,KAAK,CAACyH,MAAM,CAAC,CAAC;EACpC,IAAIE,aAAa,GAAG3H,KAAK,CAACyH,MAAM,CAAC,CAAC;EAClC,IAAIG,sBAAsB,GAAG5H,KAAK,CAACyH,MAAM,CAAC,CAAC;EAC3CzH,KAAK,CAAC6H,mBAAmB,CAAC3F,GAAG,EAAE,YAAY;IACzC,OAAO;MACL4F,aAAa,EAAEN,YAAY,CAACO,OAAO;MACnCC,QAAQ,EAAE,SAASA,QAAQA,CAACC,MAAM,EAAE;QAClC,IAAIC,sBAAsB;QAC1B,IAAIP,aAAa,CAACI,OAAO,YAAYI,WAAW,EAAE;UAChD;UACA,IAAIC,KAAK,GAAGH,MAAM,CAACG,KAAK;YACtBC,GAAG,GAAGJ,MAAM,CAACI,GAAG;YAChBnD,GAAG,GAAG+C,MAAM,CAAC/C,GAAG;UAClB,IAAIzD,gBAAgB,CAAC4G,GAAG,CAAC,EAAE;YACzB,IAAIC,qBAAqB;YACzB,CAACA,qBAAqB,GAAGX,aAAa,CAACI,OAAO,MAAM,IAAI,IAAIO,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACN,QAAQ,CAAC;cAC7HK,GAAG,EAAEA;YACP,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,IAAIE,sBAAsB;YAC1B,IAAIC,SAAS,GAAGtD,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAGH,SAAS,CAACf,UAAU,CAACoE,KAAK,CAAC,CAAC;YACnF,CAACG,sBAAsB,GAAGZ,aAAa,CAACI,OAAO,CAACU,aAAa,CAAC,kBAAkB,CAAC/D,MAAM,CAAC8D,SAAS,EAAE,KAAK,CAAC,CAAC,MAAM,IAAI,IAAID,sBAAsB,KAAK,KAAK,CAAC,IAAIA,sBAAsB,CAACG,cAAc,CAAC,CAAC;UACtM;QACF,CAAC,MAAM,IAAI,CAACR,sBAAsB,GAAGP,aAAa,CAACI,OAAO,MAAM,IAAI,IAAIG,sBAAsB,KAAK,KAAK,CAAC,IAAIA,sBAAsB,CAACF,QAAQ,EAAE;UAC5I;UACAL,aAAa,CAACI,OAAO,CAACC,QAAQ,CAACC,MAAM,CAAC;QACxC;MACF;IACF,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,IAAIU,gBAAgB,GAAG3I,KAAK,CAACyH,MAAM,CAAC,CAAC;EACrC,IAAImB,gBAAgB,GAAG5I,KAAK,CAACoG,QAAQ,CAAC,KAAK,CAAC;IAC1CyC,gBAAgB,GAAGvJ,cAAc,CAACsJ,gBAAgB,EAAE,CAAC,CAAC;IACtDE,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrC,IAAIG,gBAAgB,GAAGhJ,KAAK,CAACoG,QAAQ,CAAC,KAAK,CAAC;IAC1C6C,gBAAgB,GAAG3J,cAAc,CAAC0J,gBAAgB,EAAE,CAAC,CAAC;IACtDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,gBAAgB,GAAGpJ,KAAK,CAACoG,QAAQ,CAAC,IAAIiD,GAAG,CAAC,CAAC,CAAC;IAC9CC,gBAAgB,GAAGhK,cAAc,CAAC8J,gBAAgB,EAAE,CAAC,CAAC;IACtDG,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAExC;EACA,IAAIG,QAAQ,GAAGlI,aAAa,CAAC4F,cAAc,CAAC;EAC5C,IAAIuC,aAAa,GAAGD,QAAQ,CAACE,GAAG,CAAC,UAAUC,SAAS,EAAE;IACpD,OAAOL,UAAU,CAACM,GAAG,CAACD,SAAS,CAAC;EAClC,CAAC,CAAC;EACF,IAAIE,SAAS,GAAG9J,KAAK,CAACgF,OAAO,CAAC,YAAY;IACxC,OAAO0E,aAAa;EACtB,CAAC,EAAE,CAACA,aAAa,CAACK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EAC7B,IAAIC,aAAa,GAAG9I,gBAAgB,CAAC4I,SAAS,EAAE3C,cAAc,EAAEtE,SAAS,CAAC;EAC1E,IAAIoH,SAAS,GAAGtH,MAAM,IAAInB,aAAa,CAACmB,MAAM,CAACuH,CAAC,CAAC;EACjD,IAAIC,aAAa,GAAGxH,MAAM,IAAInB,aAAa,CAAC8F,aAAa,CAAC,IAAI8C,OAAO,CAACzE,gBAAgB,CAAC0E,KAAK,CAAC;EAC7F,IAAIC,SAAS,GAAGH,aAAa,IAAIhD,cAAc,CAACoD,IAAI,CAAC,UAAUC,IAAI,EAAE;IACnE,IAAIH,KAAK,GAAGG,IAAI,CAACH,KAAK;IACtB,OAAOA,KAAK;EACd,CAAC,CAAC;;EAEF;EACA,IAAII,SAAS,GAAGzK,KAAK,CAACyH,MAAM,CAAC,CAAC;EAC9B,IAAIiD,UAAU,GAAGzJ,SAAS,CAAC4C,MAAM,EAAExB,SAAS,CAAC;IAC3CsI,QAAQ,GAAGD,UAAU,CAACC,QAAQ;IAC9BC,YAAY,GAAGF,UAAU,CAACE,YAAY;IACtCC,aAAa,GAAGH,UAAU,CAACG,aAAa;IACxCC,YAAY,GAAGJ,UAAU,CAACI,YAAY;IACtCC,eAAe,GAAGL,UAAU,CAACK,eAAe;IAC5CC,SAAS,GAAGN,UAAU,CAACM,SAAS;;EAElC;EACA,IAAIC,WAAW,GAAGjL,KAAK,CAACgF,OAAO,CAAC,YAAY;IAC1C,OAAOhC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACgB,UAAU,CAAC;EAC9E,CAAC,EAAE,CAAChB,OAAO,EAAEgB,UAAU,CAAC,CAAC;EACzB,IAAIkH,SAAS,GAAG,CAACjB,SAAS,IAAIU,QAAQ,KAAK,aAAa3K,KAAK,CAACmL,cAAc,CAACF,WAAW,CAAC,IAAIA,WAAW,CAACG,IAAI,KAAK1K,OAAO,IAAIuK,WAAW,CAAC9I,KAAK,CAACkI,KAAK;;EAEpJ;EACA,IAAIgB,YAAY;EAChB,IAAIC,YAAY;EAChB,IAAIC,gBAAgB;EACpB,IAAItB,SAAS,EAAE;IACbqB,YAAY,GAAG;MACbE,SAAS,EAAEvH,OAAO,GAAG,QAAQ,GAAG,MAAM;MACtCwH,SAAS,EAAE9I,MAAM,CAACuH;IACpB,CAAC;EACH;EACA,IAAIC,aAAa,EAAE;IACjBkB,YAAY,GAAG;MACbK,SAAS,EAAE;IACb,CAAC;IACD;IACA;IACA;IACA,IAAI,CAACzB,SAAS,EAAE;MACdqB,YAAY,GAAG;QACbE,SAAS,EAAE;MACb,CAAC;IACH;IACAD,gBAAgB,GAAG;MACjBI,KAAK,EAAErE,aAAa,KAAK,IAAI,GAAG,MAAM,GAAGA,aAAa;MACtDsE,QAAQ,EAAE;IACZ,CAAC;EACH;EACA,IAAIC,cAAc,GAAG7L,KAAK,CAAC4E,WAAW,CAAC,UAAUgF,SAAS,EAAE+B,KAAK,EAAE;IACjEnC,gBAAgB,CAAC,UAAUsC,MAAM,EAAE;MACjC,IAAIA,MAAM,CAACjC,GAAG,CAACD,SAAS,CAAC,KAAK+B,KAAK,EAAE;QACnC,IAAII,SAAS,GAAG,IAAI1C,GAAG,CAACyC,MAAM,CAAC;QAC/BC,SAAS,CAACC,GAAG,CAACpC,SAAS,EAAE+B,KAAK,CAAC;QAC/B,OAAOI,SAAS;MAClB;MACA,OAAOD,MAAM;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIG,eAAe,GAAGlL,cAAc,CAAC,IAAI,CAAC;IACxCmL,gBAAgB,GAAG5M,cAAc,CAAC2M,eAAe,EAAE,CAAC,CAAC;IACrDE,eAAe,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACrCE,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACvC,SAASG,WAAWA,CAACC,UAAU,EAAEC,MAAM,EAAE;IACvC,IAAI,CAACA,MAAM,EAAE;MACX;IACF;IACA,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;MAChCA,MAAM,CAACD,UAAU,CAAC;IACpB,CAAC,MAAM,IAAIC,MAAM,CAACD,UAAU,KAAKA,UAAU,EAAE;MAC3CC,MAAM,CAACD,UAAU,GAAGA,UAAU;;MAE9B;MACA;MACA,IAAIC,MAAM,CAACD,UAAU,KAAKA,UAAU,EAAE;QACpCE,UAAU,CAAC,YAAY;UACrBD,MAAM,CAACD,UAAU,GAAGA,UAAU;QAChC,CAAC,EAAE,CAAC,CAAC;MACP;IACF;EACF;EACA,IAAIG,gBAAgB,GAAG7M,QAAQ,CAAC,UAAU8M,KAAK,EAAE;IAC/C,IAAIC,aAAa,GAAGD,KAAK,CAACC,aAAa;MACrCL,UAAU,GAAGI,KAAK,CAACJ,UAAU;IAC/B,IAAIM,KAAK,GAAG/J,SAAS,KAAK,KAAK;IAC/B,IAAIgK,gBAAgB,GAAG,OAAOP,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGK,aAAa,CAACL,UAAU;IAC7F,IAAIQ,aAAa,GAAGH,aAAa,IAAI7K,mBAAmB;IACxD,IAAI,CAACsK,eAAe,CAAC,CAAC,IAAIA,eAAe,CAAC,CAAC,KAAKU,aAAa,EAAE;MAC7D,IAAIC,kBAAkB;MACtBZ,eAAe,CAACW,aAAa,CAAC;MAC9BT,WAAW,CAACQ,gBAAgB,EAAEnF,eAAe,CAACK,OAAO,CAAC;MACtDsE,WAAW,CAACQ,gBAAgB,EAAElF,aAAa,CAACI,OAAO,CAAC;MACpDsE,WAAW,CAACQ,gBAAgB,EAAElE,gBAAgB,CAACZ,OAAO,CAAC;MACvDsE,WAAW,CAACQ,gBAAgB,EAAE,CAACE,kBAAkB,GAAGtC,SAAS,CAAC1C,OAAO,MAAM,IAAI,IAAIgF,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACC,aAAa,CAAC;IAC/J;IACA,IAAIC,aAAa,GAAGN,aAAa,IAAIjF,eAAe,CAACK,OAAO;IAC5D,IAAIkF,aAAa,EAAE;MACjB,IAAIlG,WAAW;MACf;MACA5C,gBAAgB,IAAIR,MAAM,IAAI,OAAO2D,aAAa,KAAK,QAAQ,GAAGA,aAAa,GAAG2F,aAAa,CAAClG,WAAW;MAC3G,IAAIC,WAAW,GAAGiG,aAAa,CAACjG,WAAW;MAC3C;MACA,IAAID,WAAW,KAAKC,WAAW,EAAE;QAC/B+B,aAAa,CAAC,KAAK,CAAC;QACpBI,cAAc,CAAC,KAAK,CAAC;QACrB;MACF;MACA,IAAIyD,KAAK,EAAE;QACT7D,aAAa,CAAC,CAAC8D,gBAAgB,GAAG9F,WAAW,GAAGC,WAAW,CAAC;QAC5DmC,cAAc,CAAC,CAAC0D,gBAAgB,GAAG,CAAC,CAAC;MACvC,CAAC,MAAM;QACL9D,aAAa,CAAC8D,gBAAgB,GAAG,CAAC,CAAC;QACnC1D,cAAc,CAAC0D,gBAAgB,GAAG9F,WAAW,GAAGC,WAAW,CAAC;MAC9D;IACF;EACF,CAAC,CAAC;EACF,IAAIkG,YAAY,GAAGtN,QAAQ,CAAC,UAAUuN,CAAC,EAAE;IACvCV,gBAAgB,CAACU,CAAC,CAAC;IACnB5J,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,IAAIA,QAAQ,CAAC4J,CAAC,CAAC;EACzD,CAAC,CAAC;EACF,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAIjD,aAAa,IAAIxC,aAAa,CAACI,OAAO,EAAE;MAC1C,IAAIsF,sBAAsB;MAC1BZ,gBAAgB,CAAC;QACfE,aAAa,EAAEjL,MAAM,CAACiG,aAAa,CAACI,OAAO,CAAC;QAC5CuE,UAAU,EAAE,CAACe,sBAAsB,GAAG1F,aAAa,CAACI,OAAO,MAAM,IAAI,IAAIsF,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACf;MAC/I,CAAC,CAAC;IACJ,CAAC,MAAM;MACLvD,aAAa,CAAC,KAAK,CAAC;MACpBI,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EACD,IAAImE,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAE;IACxD,IAAIC,mBAAmB;IACvB,IAAI7B,KAAK,GAAG4B,KAAK,CAAC5B,KAAK;IACvB,CAAC6B,mBAAmB,GAAG/C,SAAS,CAAC1C,OAAO,MAAM,IAAI,IAAIyF,mBAAmB,KAAK,KAAK,CAAC,IAAIA,mBAAmB,CAACC,qBAAqB,CAAC,CAAC;IACnI,IAAIC,WAAW,GAAGlG,YAAY,CAACO,OAAO,GAAGP,YAAY,CAACO,OAAO,CAAC4F,WAAW,GAAGhC,KAAK;IACjF,IAAIxH,gBAAgB,IAAIP,iBAAiB,IAAI4D,YAAY,CAACO,OAAO,EAAE;MACjE2F,WAAW,GAAG9J,iBAAiB,CAAC4D,YAAY,CAACO,OAAO,EAAE2F,WAAW,CAAC,IAAIA,WAAW;IACnF;IACA,IAAIA,WAAW,KAAKpH,cAAc,EAAE;MAClC8G,eAAe,CAAC,CAAC;MACjB7G,iBAAiB,CAACmH,WAAW,CAAC;IAChC;EACF,CAAC;;EAED;EACA,IAAIE,OAAO,GAAG5N,KAAK,CAACyH,MAAM,CAAC,KAAK,CAAC;EACjCzH,KAAK,CAAC6N,SAAS,CAAC,YAAY;IAC1B;IACA;IACA,IAAID,OAAO,CAAC7F,OAAO,EAAE;MACnBqF,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACjD,aAAa,EAAEzH,IAAI,EAAEwE,OAAO,CAAChD,MAAM,CAAC,CAAC;EACzClE,KAAK,CAAC6N,SAAS,CAAC,YAAY;IAC1BD,OAAO,CAAC7F,OAAO,GAAG,IAAI;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAI+F,gBAAgB,GAAG9N,KAAK,CAACoG,QAAQ,CAAC,CAAC,CAAC;IACtC2H,iBAAiB,GAAGzO,cAAc,CAACwO,gBAAgB,EAAE,CAAC,CAAC;IACvDE,aAAa,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACpCE,gBAAgB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACzC,IAAIG,iBAAiB,GAAGlO,KAAK,CAACoG,QAAQ,CAAC,IAAI,CAAC;IAC1C+H,iBAAiB,GAAG7O,cAAc,CAAC4O,iBAAiB,EAAE,CAAC,CAAC;IACxDE,aAAa,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACpCE,gBAAgB,GAAGF,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE3CxM,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACgC,MAAM,IAAI,CAACQ,gBAAgB,EAAE;MAChC,IAAIwD,aAAa,CAACI,OAAO,YAAYuG,OAAO,EAAE;QAC5CL,gBAAgB,CAACtO,sBAAsB,CAACgI,aAAa,CAACI,OAAO,CAAC,CAAC4D,KAAK,CAAC;MACvE,CAAC,MAAM;QACLsC,gBAAgB,CAACtO,sBAAsB,CAACiI,sBAAsB,CAACG,OAAO,CAAC,CAAC4D,KAAK,CAAC;MAChF;IACF;IACA0C,gBAAgB,CAAC3O,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;EACxD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAM,KAAK,CAAC6N,SAAS,CAAC,YAAY;IAC1B,IAAI1J,gBAAgB,IAAIT,YAAY,EAAE;MACpCA,YAAY,CAAC6K,IAAI,CAACxG,OAAO,GAAGJ,aAAa,CAACI,OAAO;IACnD;EACF,CAAC,CAAC;;EAEF;EACA;EACA;EACA;EACA,IAAIyG,sBAAsB,GAAGxO,KAAK,CAAC4E,WAAW,CAAC,UAAU6J,oBAAoB,EAAE;IAC7E,OAAO,aAAazO,KAAK,CAAC0O,aAAa,CAAC1O,KAAK,CAAC2O,QAAQ,EAAE,IAAI,EAAE,aAAa3O,KAAK,CAAC0O,aAAa,CAAC/N,MAAM,EAAE8N,oBAAoB,CAAC,EAAEvD,SAAS,KAAK,KAAK,IAAI,aAAalL,KAAK,CAAC0O,aAAa,CAAClO,MAAM,EAAEiO,oBAAoB,EAAExD,WAAW,CAAC,CAAC;EACnO,CAAC,EAAE,CAACC,SAAS,EAAED,WAAW,CAAC,CAAC;EAC5B,IAAI2D,sBAAsB,GAAG5O,KAAK,CAAC4E,WAAW,CAAC,UAAU6J,oBAAoB,EAAE;IAC7E,OAAO,aAAazO,KAAK,CAAC0O,aAAa,CAAClO,MAAM,EAAEiO,oBAAoB,EAAExD,WAAW,CAAC;EACpF,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA,IAAI4D,cAAc,GAAGlK,YAAY,CAAC,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;;EAErD;EACA,IAAImK,iBAAiB,GAAG9O,KAAK,CAACgF,OAAO,CAAC,YAAY;IAChD,IAAIpC,WAAW,EAAE;MACf,OAAOA,WAAW;IACpB;IACA;IACA;IACA;IACA,IAAI0H,SAAS,EAAE;MACb,OAAOhD,aAAa,KAAK,aAAa,GAAG,MAAM,GAAG,OAAO;IAC3D;IACA,IAAI2C,SAAS,IAAIU,QAAQ,IAAIxD,cAAc,CAACoD,IAAI,CAAC,UAAUwE,KAAK,EAAE;MAChE,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;MAC7B,OAAOA,QAAQ;IACjB,CAAC,CAAC,EAAE;MACF,OAAO,OAAO;IAChB;IACA,OAAO,MAAM;EACf,CAAC,EAAE,CAAC/E,SAAS,EAAEK,SAAS,EAAEnD,cAAc,EAAEvE,WAAW,EAAE+H,QAAQ,CAAC,CAAC;EACjE,IAAIsE,cAAc;;EAElB;EACA,IAAIC,WAAW,GAAG;IAChBpF,SAAS,EAAEA,SAAS;IACpBqF,UAAU,EAAEhI,cAAc,CAACjD,MAAM;IACjC8F,aAAa,EAAEA,aAAa;IAC5B1G,WAAW,EAAEA,WAAW;IACxB2G,SAAS,EAAEA,SAAS;IACpBtH,MAAM,EAAEA;EACV,CAAC;;EAED;EACA,IAAIyM,SAAS,GAAGpP,KAAK,CAACgF,OAAO,CAAC,YAAY;IACxC,IAAIf,OAAO,EAAE;MACX,OAAO,IAAI;IACb;IACA,IAAI,OAAO3B,SAAS,KAAK,UAAU,EAAE;MACnC,OAAOA,SAAS,CAAC,CAAC;IACpB;IACA,OAAOA,SAAS;EAClB,CAAC,EAAE,CAAC2B,OAAO,EAAE3B,SAAS,CAAC,CAAC;;EAExB;EACA,IAAI+M,SAAS,GAAG,aAAarP,KAAK,CAAC0O,aAAa,CAACzO,IAAI,EAAE;IACrDyC,IAAI,EAAEsB,UAAU;IAChBsL,kBAAkB,EAAErF,SAAS,IAAIE,aAAa,IAAIQ;EACpD,CAAC,CAAC;EACF,IAAI4E,YAAY,GAAG,aAAavP,KAAK,CAAC0O,aAAa,CAACxO,QAAQ,EAAE;IAC5D4J,SAAS,EAAE3C,cAAc,CAACwC,GAAG,CAAC,UAAU6F,KAAK,EAAE;MAC7C,IAAI7D,KAAK,GAAG6D,KAAK,CAAC7D,KAAK;MACvB,OAAOA,KAAK;IACd,CAAC,CAAC;IACFzE,OAAO,EAAEC;EACX,CAAC,CAAC;EACF,IAAIsI,cAAc,GAAGxM,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKwB,SAAS,GAAG,aAAazE,KAAK,CAAC0O,aAAa,CAAC,SAAS,EAAE;IAC3GnM,SAAS,EAAE,EAAE,CAACmC,MAAM,CAACrC,SAAS,EAAE,UAAU;EAC5C,CAAC,EAAEY,OAAO,CAAC,GAAGwB,SAAS;EACvB,IAAIiL,SAAS,GAAG7P,SAAS,CAACsC,KAAK,EAAE;IAC/BO,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAIiN,SAAS,GAAG9P,SAAS,CAACsC,KAAK,EAAE;IAC/ByN,IAAI,EAAE;EACR,CAAC,CAAC;EACF,IAAI3F,SAAS,IAAIU,QAAQ,EAAE;IACzB;IACA,IAAIkF,WAAW;IACf,IAAI,OAAO1K,mBAAmB,KAAK,UAAU,EAAE;MAC7C0K,WAAW,GAAG1K,mBAAmB,CAACnB,UAAU,EAAE;QAC5CgK,aAAa,EAAEA,aAAa;QAC5B9L,GAAG,EAAEyF,aAAa;QAClBpE,QAAQ,EAAEkJ;MACZ,CAAC,CAAC;MACFyC,WAAW,CAACpF,SAAS,GAAG3C,cAAc,CAACwC,GAAG,CAAC,UAAUmG,KAAK,EAAE1H,KAAK,EAAE;QACjE,IAAIuD,KAAK,GAAGmE,KAAK,CAACnE,KAAK;QACvB,IAAIoE,QAAQ,GAAG3H,KAAK,KAAKjB,cAAc,CAACjD,MAAM,GAAG,CAAC,GAAGyH,KAAK,GAAGqC,aAAa,GAAGrC,KAAK;QAClF,IAAI,OAAOoE,QAAQ,KAAK,QAAQ,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,QAAQ,CAAC,EAAE;UAC3D,OAAOA,QAAQ;QACjB;QACA,IAAI3L,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCvE,OAAO,CAACoC,KAAK,CAAC+E,OAAO,CAAChD,MAAM,KAAK,CAAC,EAAE,8FAA8F,CAAC;QACrI;QACA,OAAO,CAAC;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACL2L,WAAW,GAAG,aAAa7P,KAAK,CAAC0O,aAAa,CAAC,KAAK,EAAE;QACpDjM,KAAK,EAAElD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8L,YAAY,CAAC,EAAEC,YAAY,CAAC;QACnE/H,QAAQ,EAAE2J,YAAY;QACtBhL,GAAG,EAAEyF,aAAa;QAClBpF,SAAS,EAAE/C,UAAU,CAAC,EAAE,CAACkF,MAAM,CAACrC,SAAS,EAAE,OAAO,CAAC;MACrD,CAAC,EAAE,aAAarC,KAAK,CAAC0O,aAAa,CAACG,cAAc,EAAExP,QAAQ,CAAC;QAC3DoD,KAAK,EAAElD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgM,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5D3I,WAAW,EAAEkM;QACf,CAAC;MACH,CAAC,EAAEa,SAAS,CAAC,EAAEF,cAAc,EAAEF,YAAY,EAAEF,SAAS,EAAE,CAACnE,SAAS,IAAID,WAAW,IAAI,aAAajL,KAAK,CAAC0O,aAAa,CAAClO,MAAM,EAAE;QAC5HwJ,aAAa,EAAEA,aAAa;QAC5B7C,cAAc,EAAEA;MAClB,CAAC,EAAE8D,WAAW,CAAC,CAAC,CAAC;IACnB;;IAEA;IACA,IAAIiF,gBAAgB,GAAG3Q,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;MAC/D4Q,MAAM,EAAE,CAACnM,UAAU,CAACE,MAAM;MAC1BkM,gBAAgB,EAAEjG,aAAa,IAAI7C,aAAa,KAAK;IACvD,CAAC,EAAE4H,WAAW,CAAC,EAAE3H,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;MACnC1E,SAAS,EAAEA,SAAS;MACpBkI,eAAe,EAAEA,eAAe;MAChCxH,QAAQ,EAAEkJ;IACZ,CAAC,CAAC;IACFwC,cAAc,GAAG,aAAajP,KAAK,CAAC0O,aAAa,CAAC1O,KAAK,CAAC2O,QAAQ,EAAE,IAAI,EAAExL,UAAU,KAAK,KAAK,IAAI,aAAanD,KAAK,CAAC0O,aAAa,CAACnO,WAAW,EAAElB,QAAQ,CAAC,CAAC,CAAC,EAAE6Q,gBAAgB,EAAE;MAC3KG,eAAe,EAAEzF,YAAY;MAC7BrI,SAAS,EAAE,EAAE,CAACmC,MAAM,CAACrC,SAAS,EAAE,SAAS,CAAC;MAC1CH,GAAG,EAAEwF;IACP,CAAC,CAAC,EAAE8G,sBAAsB,CAAC,EAAEqB,WAAW,EAAE3E,SAAS,IAAIA,SAAS,KAAK,KAAK,IAAI,aAAalL,KAAK,CAAC0O,aAAa,CAACnO,WAAW,EAAElB,QAAQ,CAAC,CAAC,CAAC,EAAE6Q,gBAAgB,EAAE;MACzJI,kBAAkB,EAAEzF,aAAa;MACjCtI,SAAS,EAAE,EAAE,CAACmC,MAAM,CAACrC,SAAS,EAAE,UAAU,CAAC;MAC3CH,GAAG,EAAEyG;IACP,CAAC,CAAC,EAAEiG,sBAAsB,CAAC,EAAEjE,QAAQ,IAAIhD,aAAa,CAACI,OAAO,IAAIJ,aAAa,CAACI,OAAO,YAAYuG,OAAO,IAAI,aAAatO,KAAK,CAAC0O,aAAa,CAACtN,eAAe,EAAE;MAC9Jc,GAAG,EAAEuI,SAAS;MACdK,YAAY,EAAEA,YAAY;MAC1BnD,aAAa,EAAEA,aAAa;MAC5BpE,QAAQ,EAAEkJ,gBAAgB;MAC1BzB,SAAS,EAAEA,SAAS;MACpBnI,SAAS,EAAEA;IACb,CAAC,CAAC,CAAC;EACL,CAAC,MAAM;IACL;IACAoM,cAAc,GAAG,aAAajP,KAAK,CAAC0O,aAAa,CAAC,KAAK,EAAE;MACvDjM,KAAK,EAAElD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8L,YAAY,CAAC,EAAEC,YAAY,CAAC;MACnE/I,SAAS,EAAE/C,UAAU,CAAC,EAAE,CAACkF,MAAM,CAACrC,SAAS,EAAE,UAAU,CAAC,CAAC;MACvDkB,QAAQ,EAAEkJ,gBAAgB;MAC1BvK,GAAG,EAAEyF;IACP,CAAC,EAAE,aAAa3H,KAAK,CAAC0O,aAAa,CAACG,cAAc,EAAExP,QAAQ,CAAC;MAC3DoD,KAAK,EAAElD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgM,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;QAC5D3I,WAAW,EAAEkM;MACf,CAAC;IACH,CAAC,EAAEa,SAAS,CAAC,EAAEF,cAAc,EAAEF,YAAY,EAAEpM,UAAU,KAAK,KAAK,IAAI,aAAanD,KAAK,CAAC0O,aAAa,CAAC/N,MAAM,EAAEtB,QAAQ,CAAC,CAAC,CAAC,EAAE6P,WAAW,EAAE3H,aAAa,CAAC,CAAC,EAAE8H,SAAS,EAAEpE,WAAW,IAAI,aAAajL,KAAK,CAAC0O,aAAa,CAAClO,MAAM,EAAE;MAC1NwJ,aAAa,EAAEA,aAAa;MAC5B7C,cAAc,EAAEA;IAClB,CAAC,EAAE8D,WAAW,CAAC,CAAC,CAAC;EACnB;EACA,IAAIsF,SAAS,GAAG,aAAavQ,KAAK,CAAC0O,aAAa,CAAC,KAAK,EAAErP,QAAQ,CAAC;IAC/DkD,SAAS,EAAE/C,UAAU,CAAC6C,SAAS,EAAEE,SAAS,EAAEnD,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACsF,MAAM,CAACrC,SAAS,EAAE,MAAM,CAAC,EAAEQ,SAAS,KAAK,KAAK,CAAC,EAAE,EAAE,CAAC6B,MAAM,CAACrC,SAAS,EAAE,YAAY,CAAC,EAAEyG,UAAU,CAAC,EAAE,EAAE,CAACpE,MAAM,CAACrC,SAAS,EAAE,aAAa,CAAC,EAAE6G,WAAW,CAAC,EAAE,EAAE,CAACxE,MAAM,CAACrC,SAAS,EAAE,eAAe,CAAC,EAAEO,WAAW,KAAK,OAAO,CAAC,EAAE,EAAE,CAAC8B,MAAM,CAACrC,SAAS,EAAE,eAAe,CAAC,EAAE4H,SAAS,CAAC,EAAE,EAAE,CAACvF,MAAM,CAACrC,SAAS,EAAE,eAAe,CAAC,EAAEiI,SAAS,CAAC,EAAE,EAAE,CAAC5F,MAAM,CAACrC,SAAS,EAAE,sBAAsB,CAAC,EAAEiI,SAAS,IAAIjD,WAAW,CAAC,EAAE,EAAE,CAAC3C,MAAM,CAACrC,SAAS,EAAE,oBAAoB,CAAC,EAAE8H,aAAa,CAAC,EAAE,EAAE,CAACzF,MAAM,CAACrC,SAAS,EAAE,eAAe,CAAC,EAAE8E,cAAc,CAAC,CAAC,CAAC,IAAIA,cAAc,CAAC,CAAC,CAAC,CAACkD,KAAK,CAAC,EAAE,EAAE,CAAC3F,MAAM,CAACrC,SAAS,EAAE,gBAAgB,CAAC,EAAE8E,cAAc,CAACA,cAAc,CAACjD,MAAM,GAAG,CAAC,CAAC,IAAIiD,cAAc,CAACA,cAAc,CAACjD,MAAM,GAAG,CAAC,CAAC,CAACmG,KAAK,KAAK,OAAO,CAAC,CAAC;IAC53B5H,KAAK,EAAEA,KAAK;IACZS,EAAE,EAAEA,EAAE;IACNhB,GAAG,EAAEsF;EACP,CAAC,EAAEkI,SAAS,CAAC,EAAE5M,KAAK,IAAI,aAAa9C,KAAK,CAAC0O,aAAa,CAACvN,KAAK,EAAE;IAC9DoB,SAAS,EAAE,EAAE,CAACmC,MAAM,CAACrC,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAES,KAAK,CAACkB,UAAU,CAAC,CAAC,EAAE,aAAahE,KAAK,CAAC0O,aAAa,CAAC,KAAK,EAAE;IAC7DxM,GAAG,EAAE0F,sBAAsB;IAC3BrF,SAAS,EAAE,EAAE,CAACmC,MAAM,CAACrC,SAAS,EAAE,YAAY;EAC9C,CAAC,EAAE4M,cAAc,CAAC,EAAElM,MAAM,IAAI,aAAa/C,KAAK,CAAC0O,aAAa,CAACvN,KAAK,EAAE;IACpEoB,SAAS,EAAE,EAAE,CAACmC,MAAM,CAACrC,SAAS,EAAE,SAAS;EAC3C,CAAC,EAAEU,MAAM,CAACiB,UAAU,CAAC,CAAC,CAAC;EACvB,IAAImG,aAAa,EAAE;IACjBoG,SAAS,GAAG,aAAavQ,KAAK,CAAC0O,aAAa,CAACjP,cAAc,EAAE;MAC3D+Q,QAAQ,EAAElD;IACZ,CAAC,EAAEiD,SAAS,CAAC;EACf;EACA,IAAIE,aAAa,GAAG3P,YAAY,CAACqG,cAAc,EAAE6C,aAAa,EAAEnH,SAAS,CAAC;EAC1E,IAAI6N,iBAAiB,GAAG1Q,KAAK,CAACgF,OAAO,CAAC,YAAY;IAChD,OAAO;MACL;MACAiB,OAAO,EAAEqB,aAAa;MACtB;MACAjF,SAAS,EAAEA,SAAS;MACpBsC,YAAY,EAAEA,YAAY;MAC1BqJ,aAAa,EAAEA,aAAa;MAC5BnL,SAAS,EAAEA,SAAS;MACpB4N,aAAa,EAAEA,aAAa;MAC5B9F,QAAQ,EAAEA,QAAQ;MAClByD,aAAa,EAAEA,aAAa;MAC5B9H,cAAc,EAAEA,cAAc;MAC9B2D,SAAS,EAAEA,SAAS;MACpBK,SAAS,EAAEA,SAAS;MACpBH,aAAa,EAAEA,aAAa;MAC5B;MACAvH,WAAW,EAAEkM,iBAAiB;MAC9BtM,YAAY,EAAEA,YAAY;MAC1BmO,oBAAoB,EAAEhL,gBAAgB,CAACgL,oBAAoB;MAC3D9J,UAAU,EAAEf,gBAAgB;MAC5BF,cAAc,EAAEA,cAAc;MAC9BgL,gBAAgB,EAAEjL,gBAAgB,CAACiL,gBAAgB;MACnDlK,iBAAiB,EAAEf,gBAAgB,CAACe,iBAAiB;MACrDV,eAAe,EAAEA,eAAe;MAChCc,qBAAqB,EAAEnB,gBAAgB,CAACmB,qBAAqB;MAC7D+J,UAAU,EAAElL,gBAAgB,CAACkL,UAAU;MACvCC,mBAAmB,EAAE3J,cAAc,CAAC4J,KAAK,CAAC,UAAUC,GAAG,EAAE;QACvD,OAAOA,GAAG,CAAC3G,KAAK,KAAK,MAAM;MAC7B,CAAC,CAAC;MACF+E,SAAS,EAAEA,SAAS;MACpB;MACAlI,OAAO,EAAEA,OAAO;MAChBC,cAAc,EAAEA,cAAc;MAC9B0E,cAAc,EAAEA,cAAc;MAC9B;MACAoF,aAAa,EAAE3L,QAAQ;MACvB4L,WAAW,EAAE3L,MAAM;MACnBC,OAAO,EAAEA,OAAO;MAChB2L,aAAa,EAAExL,gBAAgB,CAACwL,aAAa;MAC7C9N,KAAK,EAAEA,KAAK;MACZ0B,SAAS,EAAEA,SAAS;MACpB6B,YAAY,EAAEf,kBAAkB;MAChCuL,kBAAkB,EAAErL,wBAAwB;MAC5ChC,YAAY,EAAEA;IAChB,CAAC;EACH,CAAC,EAAE;EACH;EACAuD,aAAa;EACb;EACAjF,SAAS,EAAEsC,YAAY,EAAEqJ,aAAa,EAAEnL,SAAS,EAAE4N,aAAa,EAAE9F,QAAQ,EAAEyD,aAAa,EAAE9H,cAAc,EAAE2D,SAAS,EAAEK,SAAS,EAAEH,aAAa;EAC9I;EACA2E,iBAAiB,EAAEtM,YAAY,EAAEmD,gBAAgB,CAACgL,oBAAoB,EAAE7K,gBAAgB,EAAEF,cAAc,EAAED,gBAAgB,CAACiL,gBAAgB,EAAEjL,gBAAgB,CAACe,iBAAiB,EAAEV,eAAe,EAAEL,gBAAgB,CAACmB,qBAAqB,EAAEnB,gBAAgB,CAACkL,UAAU,EAAEzB,SAAS;EAChR;EACAlI,OAAO,EAAEC,cAAc,EAAE0E,cAAc;EACvC;EACAvG,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEG,gBAAgB,CAACwL,aAAa,EAAE9N,KAAK,EAAE0B,SAAS,EAAEc,kBAAkB,EAAEE,wBAAwB,EAAEhC,YAAY,CAAC,CAAC;EACzI,OAAO,aAAa/D,KAAK,CAAC0O,aAAa,CAACrO,YAAY,CAACgR,QAAQ,EAAE;IAC7DC,KAAK,EAAEZ;EACT,CAAC,EAAEH,SAAS,CAAC;AACf;AACA,IAAIgB,QAAQ,GAAG,aAAavR,KAAK,CAACwR,UAAU,CAACxP,KAAK,CAAC;AACnD,IAAIoC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCiN,QAAQ,CAACE,WAAW,GAAG,OAAO;AAChC;AACA,OAAO,SAASC,QAAQA,CAACC,mBAAmB,EAAE;EAC5C,OAAOrR,aAAa,CAACiR,QAAQ,EAAEI,mBAAmB,CAAC;AACrD;AACA,IAAIC,cAAc,GAAGF,QAAQ,CAAC,CAAC;AAC/BE,cAAc,CAACzR,aAAa,GAAGA,aAAa;AAC5CyR,cAAc,CAACxR,cAAc,GAAGA,cAAc;AAC9CwR,cAAc,CAACvQ,MAAM,GAAGA,MAAM;AAC9BuQ,cAAc,CAACtQ,WAAW,GAAGA,WAAW;AACxCsQ,cAAc,CAAClR,OAAO,GAAGD,gBAAgB;AACzC,eAAemR,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}