{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\n// import React from 'react';\nimport React, { useEffect, useState } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/lib/locale/zh_CN';\n\n// 导入页面组件\nimport Login from './pages/Login';\nimport MainLayout from './components/layout/MainLayout';\nimport RealTimeTraffic from './pages/RealTimeTraffic';\nimport DeviceStatus from './pages/DeviceStatus';\nimport RoadMonitoring from './pages/RoadMonitoring';\nimport SystemManagement from './pages/SystemManagement';\n\n// 导入样式\nimport './App.css';\n\n// 改进的身份验证检查，增加权限检查\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst isAuthenticated = () => {\n  const user = localStorage.getItem('user');\n  return !!user;\n};\n\n// 检查用户是否有访问系统管理的权限\nconst hasSystemManagementAccess = () => {\n  try {\n    var _user$user;\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\n    // 只有管理员可以访问系统管理页面\n    const userRole = user.role || ((_user$user = user.user) === null || _user$user === void 0 ? void 0 : _user$user.role) || 'user';\n    console.log('系统管理权限检查:', {\n      用户数据: user,\n      角色: userRole,\n      是否有权限: userRole === 'admin'\n    });\n    return userRole === 'admin';\n  } catch (e) {\n    console.error('检查管理权限失败:', e);\n    return false;\n  }\n};\nfunction App() {\n  _s();\n  const [authChecked, setAuthChecked] = useState(false);\n  const navigate = useNavigate();\n\n  // 监听认证状态变化\n  useEffect(() => {\n    if (isAuthenticated()) {\n      navigate('/real-time-traffic'); // 已登录则导航到目标页\n    } else {\n      navigate('/login'); // 未登录则导航到登录页\n    }\n    setAuthChecked(true);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    locale: zhCN,\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: isAuthenticated() ? /*#__PURE__*/_jsxDEV(MainLayout, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 42\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 59\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            index: true,\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/real-time-traffic\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 35\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"real-time-traffic\",\n            element: /*#__PURE__*/_jsxDEV(RealTimeTraffic, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 54\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"device-status\",\n            element: /*#__PURE__*/_jsxDEV(DeviceStatus, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"road-monitoring\",\n            element: /*#__PURE__*/_jsxDEV(RoadMonitoring, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"system-management\",\n            element: /*#__PURE__*/_jsxDEV(SystemManagement, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 24\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"W8y86FSv0haaYWx1BeG0zLpu7BA=\", false, function () {\n  return [useNavigate];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "useNavigate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "<PERSON><PERSON>", "MainLayout", "RealTimeTraffic", "DeviceStatus", "RoadMonitoring", "SystemManagement", "jsxDEV", "_jsxDEV", "isAuthenticated", "user", "localStorage", "getItem", "hasSystemManagementAccess", "_user$user", "JSON", "parse", "userRole", "role", "console", "log", "用户数据", "角色", "是否有权限", "e", "error", "App", "_s", "authChecked", "setAuthChecked", "navigate", "locale", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "index", "_c", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/App.js"], "sourcesContent": ["// import React from 'react';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';\r\nimport { ConfigProvider } from 'antd';\r\nimport zhCN from 'antd/lib/locale/zh_CN';\r\n\r\n// 导入页面组件\r\nimport Login from './pages/Login';\r\nimport MainLayout from './components/layout/MainLayout';\r\nimport RealTimeTraffic from './pages/RealTimeTraffic';\r\nimport DeviceStatus from './pages/DeviceStatus';\r\nimport RoadMonitoring from './pages/RoadMonitoring';\r\nimport SystemManagement from './pages/SystemManagement';\r\n\r\n// 导入样式\r\nimport './App.css';\r\n\r\n// 改进的身份验证检查，增加权限检查\r\nconst isAuthenticated = () => {\r\n  const user = localStorage.getItem('user');\r\n  return !!user;\r\n};\r\n\r\n// 检查用户是否有访问系统管理的权限\r\nconst hasSystemManagementAccess = () => {\r\n  try {\r\n    const user = JSON.parse(localStorage.getItem('user') || '{}');\r\n    // 只有管理员可以访问系统管理页面\r\n    const userRole = user.role || user.user?.role || 'user';\r\n    console.log('系统管理权限检查:', {\r\n      用户数据: user,\r\n      角色: userRole,\r\n      是否有权限: userRole === 'admin'\r\n    });\r\n    return userRole === 'admin';\r\n  } catch (e) {\r\n    console.error('检查管理权限失败:', e);\r\n    return false;\r\n  }\r\n};\r\n\r\nfunction App() {\r\n  const [authChecked, setAuthChecked] = useState(false);\r\n  const navigate = useNavigate();\r\n\r\n  // 监听认证状态变化\r\n  useEffect(() => {\r\n    if (isAuthenticated()) {\r\n      navigate('/real-time-traffic'); // 已登录则导航到目标页\r\n    } else {\r\n      navigate('/login'); // 未登录则导航到登录页\r\n    }\r\n    setAuthChecked(true);\r\n  }, []);\r\n\r\n  return (\r\n    <ConfigProvider locale={zhCN}>\r\n      <Router>\r\n        <Routes>\r\n          <Route path=\"/login\" element={<Login />} />\r\n          <Route \r\n            path=\"/\" \r\n            element={isAuthenticated() ? <MainLayout /> : <Navigate to=\"/login\" />}\r\n          >\r\n            <Route index element={<Navigate to=\"/real-time-traffic\" />} />\r\n            <Route path=\"real-time-traffic\" element={<RealTimeTraffic />} />\r\n            <Route path=\"device-status\" element={<DeviceStatus />} />\r\n            <Route path=\"road-monitoring\" element={<RoadMonitoring />} />\r\n            <Route \r\n              path=\"system-management\" \r\n              element={<SystemManagement />} \r\n            />\r\n          </Route>\r\n          <Route path=\"*\" element={<Navigate to=\"/\" />} />\r\n        </Routes>\r\n      </Router>\r\n    </ConfigProvider>\r\n  );\r\n}\r\n\r\nexport default App; "], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AAChG,SAASC,cAAc,QAAQ,MAAM;AACrC,OAAOC,IAAI,MAAM,uBAAuB;;AAExC;AACA,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,gBAAgB,MAAM,0BAA0B;;AAEvD;AACA,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,MAAMC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EACzC,OAAO,CAAC,CAACF,IAAI;AACf,CAAC;;AAED;AACA,MAAMG,yBAAyB,GAAGA,CAAA,KAAM;EACtC,IAAI;IAAA,IAAAC,UAAA;IACF,MAAMJ,IAAI,GAAGK,IAAI,CAACC,KAAK,CAACL,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IAC7D;IACA,MAAMK,QAAQ,GAAGP,IAAI,CAACQ,IAAI,MAAAJ,UAAA,GAAIJ,IAAI,CAACA,IAAI,cAAAI,UAAA,uBAATA,UAAA,CAAWI,IAAI,KAAI,MAAM;IACvDC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;MACvBC,IAAI,EAAEX,IAAI;MACVY,EAAE,EAAEL,QAAQ;MACZM,KAAK,EAAEN,QAAQ,KAAK;IACtB,CAAC,CAAC;IACF,OAAOA,QAAQ,KAAK,OAAO;EAC7B,CAAC,CAAC,OAAOO,CAAC,EAAE;IACVL,OAAO,CAACM,KAAK,CAAC,WAAW,EAAED,CAAC,CAAC;IAC7B,OAAO,KAAK;EACd;AACF,CAAC;AAED,SAASE,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMsC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;;EAE9B;EACAP,SAAS,CAAC,MAAM;IACd,IAAIkB,eAAe,CAAC,CAAC,EAAE;MACrBqB,QAAQ,CAAC,oBAAoB,CAAC,CAAC,CAAC;IAClC,CAAC,MAAM;MACLA,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;IACtB;IACAD,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACErB,OAAA,CAACT,cAAc;IAACgC,MAAM,EAAE/B,IAAK;IAAAgC,QAAA,eAC3BxB,OAAA,CAACd,MAAM;MAAAsC,QAAA,eACLxB,OAAA,CAACb,MAAM;QAAAqC,QAAA,gBACLxB,OAAA,CAACZ,KAAK;UAACqC,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAE1B,OAAA,CAACP,KAAK;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3C9B,OAAA,CAACZ,KAAK;UACJqC,IAAI,EAAC,GAAG;UACRC,OAAO,EAAEzB,eAAe,CAAC,CAAC,gBAAGD,OAAA,CAACN,UAAU;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG9B,OAAA,CAACX,QAAQ;YAAC0C,EAAE,EAAC;UAAQ;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAN,QAAA,gBAEvExB,OAAA,CAACZ,KAAK;YAAC4C,KAAK;YAACN,OAAO,eAAE1B,OAAA,CAACX,QAAQ;cAAC0C,EAAE,EAAC;YAAoB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9D9B,OAAA,CAACZ,KAAK;YAACqC,IAAI,EAAC,mBAAmB;YAACC,OAAO,eAAE1B,OAAA,CAACL,eAAe;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChE9B,OAAA,CAACZ,KAAK;YAACqC,IAAI,EAAC,eAAe;YAACC,OAAO,eAAE1B,OAAA,CAACJ,YAAY;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzD9B,OAAA,CAACZ,KAAK;YAACqC,IAAI,EAAC,iBAAiB;YAACC,OAAO,eAAE1B,OAAA,CAACH,cAAc;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7D9B,OAAA,CAACZ,KAAK;YACJqC,IAAI,EAAC,mBAAmB;YACxBC,OAAO,eAAE1B,OAAA,CAACF,gBAAgB;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACR9B,OAAA,CAACZ,KAAK;UAACqC,IAAI,EAAC,GAAG;UAACC,OAAO,eAAE1B,OAAA,CAACX,QAAQ;YAAC0C,EAAE,EAAC;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAErB;AAACX,EAAA,CArCQD,GAAG;EAAA,QAEO5B,WAAW;AAAA;AAAA2C,EAAA,GAFrBf,GAAG;AAuCZ,eAAeA,GAAG;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}