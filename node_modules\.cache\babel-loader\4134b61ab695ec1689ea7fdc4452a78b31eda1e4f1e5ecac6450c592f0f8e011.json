{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\RealTimeTraffic.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge, Button, Select } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { BarChart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\nimport vehiclesData from '../data/vehicles.json';\nimport intersectionsData from '../data/intersections.json'; // 导入路口数据\n\n// 注册必要的echarts组件\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\necharts.use([Bar<PERSON><PERSON>, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\nconst StyledCanvas = styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 65px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\n_c = PageContainer;\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 24px' : props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 0' : props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\n_c2 = MainContent;\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n\n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n\n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n\n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\n_c3 = InfoCard;\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n\n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n_c4 = CompactStatistic;\nconst RealTimeTraffic = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [vehicles, setVehicles] = useState([]);\n  const [events, setEvents] = useState([]);\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\n  const [stats, setStats] = useState({\n    totalVehicles: 0,\n    onlineVehicles: 0,\n    offlineVehicles: 0,\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0\n  });\n\n  // 存储在线车辆的 BSM ID，初始时为空集合，表示所有车辆离线\n  const [onlineBsmIds, setOnlineBsmIds] = useState(() => {\n    try {\n      const savedOnlineIds = localStorage.getItem('realTimeTrafficOnlineIds');\n      // 返回空集合，确保所有车辆初始状态为离线\n      return new Set();\n    } catch (error) {\n      console.error('读取在线ID缓存失败:', error);\n      return new Set();\n    }\n  });\n  // 存储最后一次收到 BSM 消息的时间\n  const lastBsmTime = useRef({});\n  const eventChartRef = useRef(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  // 添加时间段选择状态\n  const [selectedTimeRange, setSelectedTimeRange] = useState('1h');\n  const [eventStats, setEventStats] = useState({\n    '401': 0,\n    // 道路抛洒物\n    '404': 0,\n    // 道路障碍物\n    '405': 0,\n    // 行人通过马路\n    '904': 0,\n    // 逆行车辆\n    '910': 0,\n    // 违停车辆\n    '1002': 0,\n    // 道路施工\n    '901': 0 // 车辆超速\n  });\n\n  // 添加RSI事件缓存，用于检测重复事件\n  const prevRsiEvents = useRef(new Map());\n\n  // 事件列表缓存，存储所有事件的完整信息\n  const eventListCache = useRef([]);\n\n  // 事件ID计数器\n  const eventIdCounter = useRef(1);\n\n  // ========== 数据库API调用函数 ==========\n\n  /**\n   * 存储实时事件到数据库\n   */\n  const storeEventToDatabase = async eventData => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.post(`${apiUrl}/api/events/store`, eventData);\n      if (response.data && response.data.success) {\n        console.log(`✅ 事件已存储到${response.data.storage}:`, eventData.eventTypeText);\n        return true;\n      } else {\n        console.error('❌ 存储事件失败:', response.data);\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ 存储事件到数据库失败:', error);\n      return false;\n    }\n  };\n\n  /**\n   * 从数据库获取事件统计\n   */\n  const fetchEventStatsFromDatabase = async (timeRange = '1h') => {\n    try {\n      // console.log(`timeRange);\n      console.log('实际的时间段0', timeRange);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/events/stats?timeRange=${timeRange}`);\n      if (response.data && response.data.success) {\n        console.log(`📊 从${response.data.storage}获取事件统计(${timeRange}):`, response.data.data);\n        return response.data.data;\n      } else {\n        console.error('❌ 获取事件统计失败:', response.data);\n        return null;\n      }\n    } catch (error) {\n      console.error('❌ 从数据库获取事件统计失败:', error);\n      return null;\n    }\n  };\n\n  /**\n   * 从数据库获取最近事件\n   */\n  const fetchRecentEventsFromDatabase = async (limit = 10) => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/events/recent?limit=${limit}`);\n      if (response.data && response.data.success) {\n        console.log(`📋 从${response.data.storage}获取最近事件:`, response.data.data.length, '条');\n        return response.data.data;\n      } else {\n        console.error('❌ 获取最近事件失败:', response.data);\n        return [];\n      }\n    } catch (error) {\n      console.error('❌ 从数据库获取最近事件失败:', error);\n      return [];\n    }\n  };\n\n  // 添加手动更新车辆状态和位置信息的函数\n  const updateVehicleStatus = useCallback((bsmId, status, speed = 0, lat = 0, lng = 0, heading = 0) => {\n    // 确保速度和航向角是格式化的数值，保留两位小数\n    const formattedSpeed = parseFloat(speed).toFixed(0);\n    const formattedHeading = parseFloat(heading).toFixed(2);\n    console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${formattedSpeed}, 位置=(${lat}, ${lng})`);\n\n    // 更新在线状态\n    if (status === 'online') {\n      setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n      lastBsmTime.current[bsmId] = Date.now();\n    } else {\n      setOnlineBsmIds(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(bsmId);\n        return newSet;\n      });\n    }\n\n    // 更新车辆信息\n    setVehicles(prevVehicles => prevVehicles.map(vehicle => vehicle.bsmId === bsmId ? {\n      ...vehicle,\n      status: status,\n      speed: parseFloat(formattedSpeed),\n      // 确保是数值类型\n      lat: parseFloat(lat.toFixed(7)),\n      lng: parseFloat(lng.toFixed(7)),\n      heading: parseFloat(formattedHeading) // 确保是数值类型\n    } : vehicle));\n  }, []);\n\n  // 在组件挂载时，暴露updateVehicleStatus函数到window对象\n  useEffect(() => {\n    window.updateVehicleStatus = updateVehicleStatus;\n\n    // 用于监听CampusModel是否接收到BSM消息的事件\n    const handleRealBsmReceived = event => {\n      if (event.data && event.data.type === 'realBsmReceived') {\n        // console.log('收到CampusModel发送的真实BSM消息通知');\n      }\n    };\n    window.addEventListener('message', handleRealBsmReceived);\n\n    // 确保页面刷新时清空在线车辆状态\n    console.log('组件初始化，重置所有车辆为离线状态');\n    setOnlineBsmIds(new Set());\n    lastBsmTime.current = {};\n    return () => {\n      window.removeEventListener('message', handleRealBsmReceived);\n      delete window.updateVehicleStatus;\n    };\n  }, [updateVehicleStatus]);\n\n  // 从数据库加载事件数据\n  const loadEventsFromDatabase = async (timeRange = selectedTimeRange) => {\n    try {\n      // 加载最近事件\n      const recentEvents = await fetchRecentEventsFromDatabase(10);\n      if (recentEvents && recentEvents.length > 0) {\n        setEvents(recentEvents);\n        console.log('✅ 从数据库加载了', recentEvents.length, '条最近事件');\n      }\n\n      // 加载事件统计（使用选择的时间段）\n      console.log('实际的时间段1', timeRange);\n      const stats = await fetchEventStatsFromDatabase(timeRange);\n      if (stats) {\n        setEventStats(prevStats => ({\n          ...prevStats,\n          ...stats\n        }));\n        console.log(`✅ 从数据库加载了事件统计数据(${timeRange})`);\n      }\n    } catch (error) {\n      console.error('❌ 从数据库加载事件数据失败:', error);\n    }\n  };\n\n  // 处理时间段选择变化\n  const handleTimeRangeChange = value => {\n    console.log('📅 时间段选择变化:', value);\n    setSelectedTimeRange(value);\n    // 立即重新加载事件统计数据\n    loadEventsFromDatabase(value);\n  };\n\n  // 组件挂载时从数据库加载数据\n  useEffect(() => {\n    loadEventsFromDatabase(selectedTimeRange);\n  }, []);\n\n  // 监听时间段变化，重新加载事件统计数据\n  useEffect(() => {\n    loadEventsFromDatabase(selectedTimeRange);\n  }, [selectedTimeRange]);\n\n  // 定期从数据库刷新数据（每30秒）\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      console.log('🔄 定期刷新事件数据...', '当前时间段:', selectedTimeRange);\n      loadEventsFromDatabase(selectedTimeRange);\n    }, 30000); // 30秒刷新一次\n\n    return () => clearInterval(refreshInterval);\n  }, [selectedTimeRange]); // 添加 selectedTimeRange 作为依赖\n\n  // 定期清理过期事件（每5分钟）\n  useEffect(() => {\n    const cleanupInterval = setInterval(() => {\n      cleanupExpiredEvents();\n    }, 300000); // 5分钟清理一次\n\n    return () => clearInterval(cleanupInterval);\n  }, []);\n\n  // 定期删除1分钟内没有更新的事件（每30秒检查一次）\n  useEffect(() => {\n    const removeInactiveInterval = setInterval(() => {\n      removeInactiveEvents();\n    }, 30000); // 30秒检查一次\n\n    return () => clearInterval(removeInactiveInterval);\n  }, []);\n\n  // 获取车辆数据\n  const fetchVehicles = useCallback(async () => {\n    try {\n      // 先尝试从API获取最新数据\n      console.log('尝试从API获取最新车辆数据');\n      const apiData = await fetchLatestVehiclesData(true);\n\n      // 如果API获取成功，直接使用API数据\n      if (apiData && apiData.length > 0) {\n        console.log('成功从API获取车辆数据，车辆数量:', apiData.length);\n\n        // 获取所有车辆的BSM ID列表\n        const bsmIds = apiData.map(v => v.bsmId).filter(id => id);\n        console.log('所有车辆的BSM ID:', bsmIds);\n\n        // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\n        const updatedVehicles = apiData.map(vehicle => {\n          // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\n          const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);\n          return {\n            ...vehicle,\n            plate: vehicle.plateNumber,\n            // 适配表格显示\n            status: isOnline ? 'online' : 'offline',\n            // 只有收到BSM消息的车辆才显示为在线\n            speed: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0 : 0,\n            lat: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0 : 0,\n            lng: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0 : 0,\n            heading: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0 : 0\n          };\n        });\n        setVehicles(updatedVehicles);\n\n        // 直接检查更新后的车辆在线状态\n        const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n        const totalCount = updatedVehicles.length;\n        console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);\n\n        // 更新车辆统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalVehicles: totalCount,\n          onlineVehicles: onlineCount,\n          offlineVehicles: totalCount - onlineCount\n        }));\n        return;\n      }\n\n      // 如果API获取失败，回退到本地JSON文件\n      console.log('API获取失败，回退到本地vehiclesData');\n      const vehiclesList = vehiclesData.vehicles || [];\n      console.log('从vehiclesData获取车辆数据，车辆数量:', vehiclesList.length);\n\n      // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\n      const updatedVehicles = vehiclesList.map(vehicle => {\n        // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\n        const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);\n        return {\n          ...vehicle,\n          plate: vehicle.plateNumber,\n          // 适配表格显示\n          status: isOnline ? 'online' : 'offline',\n          // 只有收到BSM消息的车辆才显示为在线\n          speed: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0 : 0,\n          lat: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0 : 0,\n          lng: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0 : 0,\n          heading: isOnline ? lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0 : 0\n        };\n      });\n      setVehicles(updatedVehicles);\n\n      // 直接检查更新后的车辆在线状态\n      const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n      const totalCount = updatedVehicles.length;\n      console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);\n\n      // 更新车辆统计数据\n      setStats(prevStats => ({\n        ...prevStats,\n        totalVehicles: totalCount,\n        onlineVehicles: onlineCount,\n        offlineVehicles: totalCount - onlineCount\n      }));\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n    }\n  }, [onlineBsmIds]);\n\n  // 获取设备统计数据\n  const fetchDeviceStats = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n      if (response.data && response.data.success) {\n        const devicesData = response.data.data;\n\n        // 更新设备统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalDevices: devicesData.length,\n          onlineDevices: devicesData.filter(d => d.status === 'online').length,\n          offlineDevices: devicesData.filter(d => d.status === 'offline').length\n        }));\n      }\n    } catch (error) {\n      console.error('获取设备统计数据失败:', error);\n    }\n  };\n\n  // 监听 BSM 消息\n  useEffect(() => {\n    const handleBsmMessage = event => {\n      if (event.data && event.data.type === 'bsm') {\n        // 获取bsmId，确保它正确地从消息中提取\n        const bsmData = event.data.data || {};\n        const bsmId = bsmData.bsmId || event.data.bsmId;\n        if (!bsmId) {\n          console.error('BSM消息缺少bsmId:', event.data);\n          return;\n        }\n\n        // console.log('收到BSM消息，ID:', bsmId);\n        const now = Date.now();\n\n        // 更新最后接收时间\n        lastBsmTime.current[bsmId] = now;\n\n        // 添加到在线bsmId集合\n        setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n\n        // 提取正确的BSM数据并格式化为两位小数\n        const speed = parseFloat((parseFloat(bsmData.partSpeed || event.data.speed || 0) * 3.6).toFixed(0)); // 转换为km/h，保留两位小数\n        const lat = parseFloat(parseFloat(bsmData.partLat || event.data.lat || 0).toFixed(7));\n        const lng = parseFloat(parseFloat(bsmData.partLong || event.data.lng || 0).toFixed(7));\n        const heading = parseFloat(parseFloat(bsmData.partHeading || event.data.heading || 0).toFixed(2)); // 保留两位小数\n\n        // console.log(`车辆${bsmId}状态: 速度=${speed.toFixed(2)}km/h, 位置=(${lat.toFixed(7)}, ${lng.toFixed(7)}), 航向=${heading.toFixed(2)}°`);\n\n        // 更新车辆状态和位置信息\n        setVehicles(prevVehicles => {\n          // 检查是否找到对应车辆\n          const foundVehicle = prevVehicles.find(v => v.bsmId === bsmId);\n          if (!foundVehicle) {\n            console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);\n            return prevVehicles;\n          }\n          const updatedVehicles = prevVehicles.map(vehicle => vehicle.bsmId === bsmId ? {\n            ...vehicle,\n            status: 'online',\n            speed: speed,\n            lat: lat,\n            lng: lng,\n            heading: heading\n          } : vehicle);\n\n          // 计算更新后的在线车辆数量\n          const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n          const totalCount = updatedVehicles.length;\n\n          // 更新统计数据\n          setStats(prevStats => ({\n            ...prevStats,\n            onlineVehicles: onlineCount,\n            offlineVehicles: totalCount - onlineCount\n          }));\n          return updatedVehicles;\n        });\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleBsmMessage);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleBsmMessage);\n    };\n  }, []);\n\n  // 修改检查在线状态的useEffect，同步更新统计信息\n  useEffect(() => {\n    const checkOnlineStatus = () => {\n      const now = Date.now();\n      console.log('检查车辆在线状态...');\n\n      // 只有在特定条件下才将车辆设为离线\n      // 例如，只有收到过BSM消息的车辆才会被检查是否超时\n      setOnlineBsmIds(prev => {\n        const newOnlineBsmIds = new Set(prev);\n        let hasChanges = false;\n\n        // 仅检查已有最后更新时间的车辆\n        prev.forEach(bsmId => {\n          const lastTime = lastBsmTime.current[bsmId];\n\n          // 只有收到过BSM消息的车辆才会被检查是否超时\n          if (lastTime && now - lastTime > 30000) {\n            console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);\n            newOnlineBsmIds.delete(bsmId);\n            hasChanges = true;\n          }\n        });\n        if (hasChanges) {\n          // 更新车辆状态\n          setVehicles(prevVehicles => {\n            const updatedVehicles = prevVehicles.map(vehicle => {\n              // 只更新有最后更新时间的车辆状态\n              if (lastBsmTime.current[vehicle.bsmId]) {\n                const isOnline = newOnlineBsmIds.has(vehicle.bsmId);\n                return {\n                  ...vehicle,\n                  status: isOnline ? 'online' : 'offline'\n                };\n              }\n              return vehicle;\n            });\n\n            // 计算更新后的在线车辆数量\n            const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n            const totalCount = updatedVehicles.length;\n\n            // 更新统计数据\n            setStats(prevStats => ({\n              ...prevStats,\n              onlineVehicles: onlineCount,\n              offlineVehicles: totalCount - onlineCount\n            }));\n            return updatedVehicles;\n          });\n        }\n        return newOnlineBsmIds;\n      });\n    };\n    const interval = setInterval(checkOnlineStatus, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // 重置所有车辆的初始状态\n  useEffect(() => {\n    // 将所有车辆状态重置为离线\n    const resetAllVehicles = () => {\n      // 只有在没有任何BSM消息的情况下才执行重置\n      if (onlineBsmIds.size === 0) {\n        setVehicles(prevVehicles => prevVehicles.map(vehicle => ({\n          ...vehicle,\n          status: 'offline',\n          speed: 0,\n          lat: 0,\n          lng: 0,\n          heading: 0\n        })));\n        console.log('已重置所有车辆为离线状态');\n      }\n    };\n\n    // 初始执行一次\n    resetAllVehicles();\n\n    // 然后每30秒检查一次\n    const interval = setInterval(resetAllVehicles, 30000);\n    return () => clearInterval(interval);\n  }, [onlineBsmIds]);\n\n  // 在组件挂载时获取数据\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      fetchVehicles();\n      await fetchDeviceStats();\n      setLoading(false);\n    };\n    loadData();\n    // // 降低更新频率，避免频繁覆盖状态\n    // const interval = setInterval(loadData, 300000); // 每60x5秒更新一次\n    // return () => clearInterval(interval);\n  }, []);\n\n  // 添加对车辆数据变更的监听\n  useEffect(() => {\n    console.log('设置车辆数据变更监听');\n\n    // 监听自定义事件\n    const handleVehiclesDataChanged = () => {\n      console.log('检测到车辆数据变更事件，重新获取车辆列表');\n      fetchVehicles();\n    };\n\n    // 监听localStorage变化\n    const handleStorageChange = event => {\n      if (event.key === 'vehiclesLastUpdated' || event.key === 'vehiclesData') {\n        console.log('检测到localStorage变化，重新获取车辆列表');\n        fetchVehicles();\n      }\n    };\n\n    // 添加事件监听器\n    window.addEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n    window.addEventListener('storage', handleStorageChange);\n\n    // 初始检查是否有更新\n    const lastUpdated = localStorage.getItem('vehiclesLastUpdated');\n    if (lastUpdated) {\n      console.log('初始检查到vehiclesLastUpdated:', lastUpdated);\n      fetchVehicles();\n    }\n\n    // 设置更频繁的强制轮询，确保在部署环境中也能获取最新数据\n    const forcedPollingInterval = setInterval(() => {\n      console.log('强制轮询：重新获取车辆列表');\n      fetchVehicles();\n    }, 10000); // 每10秒强制刷新一次\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n      window.removeEventListener('storage', handleStorageChange);\n      clearInterval(forcedPollingInterval);\n    };\n  }, []);\n\n  // 添加检查本地缓存与API数据是否一致的机制\n  // 定义一个单独的API调用函数，用于获取最新的车辆列表数据\n  const fetchLatestVehiclesData = async (returnData = false) => {\n    try {\n      // 尝试多个可能的API地址\n      const possibleApiUrls = [process.env.REACT_APP_API_URL || 'http://localhost:5000'\n      // 'http://localhost:5000',\n      // window.location.origin, // 当前站点的根URL\n      // `${window.location.origin}/api`, // 当前站点下的/api路径\n      // 'http://localhost:5000/api',\n      // 'http://127.0.0.1:5000',\n      // 'http://127.0.0.1:5000/api'\n      ];\n      console.log('尝试从多个API地址获取车辆数据');\n\n      // // 尝试从本地JSON文件直接获取\n      // try {\n      //   console.log('尝试从本地JSON文件获取数据');\n      //   const jsonResponse = await axios.get('/vehicles.json');\n      //   if (jsonResponse.data && jsonResponse.data.vehicles) {\n      //     console.log('成功从本地JSON文件获取数据:', jsonResponse.data.vehicles.length);\n      //     if (returnData) {\n      //       return jsonResponse.data.vehicles;\n      //     }\n      //     processVehiclesData(jsonResponse.data.vehicles);\n      //     return jsonResponse.data.vehicles;\n      //   }\n      // } catch (jsonError) {\n      //   console.log('从本地JSON获取失败:', jsonError.message);\n      // }\n\n      // 逐个尝试API地址\n      let succeeded = false;\n      let vehiclesData = null;\n      for (const apiUrl of possibleApiUrls) {\n        if (succeeded) break;\n        try {\n          console.log(`尝试从 ${apiUrl}/api/vehicles/list 获取数据`);\n          const response = await axios.get(`${apiUrl}/api/vehicles/list`);\n          if (response.data && response.data.vehicles) {\n            console.log(`成功从 ${apiUrl} 获取数据:`, response.data.vehicles.length);\n            vehiclesData = response.data.vehicles;\n            if (returnData) {\n              return vehiclesData;\n            }\n            processVehiclesData(response.data.vehicles);\n            succeeded = true;\n            break;\n          }\n        } catch (error) {\n          console.log(`从 ${apiUrl} 获取失败:`, error.message);\n          // 继续尝试下一个URL\n        }\n      }\n      if (!succeeded && !returnData) {\n        console.log('所有API地址都获取失败，尝试使用vehicles.json');\n        // 尝试从当前页面获取\n        try {\n          const response = await fetch('/vehicles.json');\n          if (response.ok) {\n            const data = await response.json();\n            if (data && data.vehicles) {\n              console.log('从public/vehicles.json获取数据成功:', data.vehicles.length);\n              processVehiclesData(data.vehicles);\n              return data.vehicles;\n            }\n          }\n        } catch (e) {\n          console.error('从public/vehicles.json获取失败:', e);\n        }\n      }\n      return vehiclesData || [];\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n      return [];\n    }\n  };\n\n  // 添加处理车辆数据的辅助函数\n  const processVehiclesData = newVehicles => {\n    // 与当前列表比较\n    if (vehicles.length !== newVehicles.length) {\n      console.log('检测到车辆数量变化，从', vehicles.length, '到', newVehicles.length);\n      fetchVehicles(); // 重新加载\n      return;\n    }\n\n    // 检查是否有新车辆ID\n    const currentIds = new Set(vehicles.map(v => v.id));\n    const hasNewVehicle = newVehicles.some(v => !currentIds.has(v.id));\n    if (hasNewVehicle) {\n      console.log('检测到新增车辆');\n      fetchVehicles(); // 重新加载\n    }\n  };\n\n  // 添加自动选择第一个车辆的逻辑\n  useEffect(() => {\n    // 当车辆列表加载完成且有车辆数据时\n    if (vehicles.length > 0 && !selectedVehicle) {\n      // 自动选择第一个车辆\n      setSelectedVehicle(vehicles[0]);\n      console.log('已自动选择第一个车辆:', vehicles[0].plateNumber);\n    }\n  }, [vehicles, selectedVehicle]);\n\n  // 修改图表初始化代码\n  useEffect(() => {\n    let chart = null;\n    let handleResize = null;\n    let lastUpdateTime = new Date();\n\n    // 确保 DOM 元素存在\n    if (!eventChartRef.current) {\n      console.error('图表容器未找到');\n      return;\n    }\n\n    // 检查并清理已存在的图表实例\n    let existingChart = echarts.getInstanceByDom(eventChartRef.current);\n    if (existingChart) {\n      existingChart.dispose();\n    }\n    try {\n      // 初始化新的图表实例\n      chart = echarts.init(eventChartRef.current);\n\n      // 设置图表配置\n      const updateChart = () => {\n        const currentTime = new Date();\n        lastUpdateTime = currentTime;\n\n        // 事件类型配置\n        // const eventTypes = [\n        //   { type: '401', name: '道路抛洒物', color: '#ff4d4f' },\n        //   { type: '404', name: '道路障碍物', color: '#faad14' },\n        //   { type: '405', name: '行人通过马路', color: '#1890ff' },\n        //   { type: '904', name: '逆行车辆', color: '#f5222d' },\n        //   { type: '910', name: '违停车辆', color: '#722ed1' },\n        //   { type: '1002', name: '道路施工', color: '#fa8c16' },\n        //   { type: '901', name: '车辆超速', color: '#eb2f96' }\n        // ];\n        const eventTypes = [{\n          type: '401',\n          name: '道路抛洒物',\n          color: '#faad14'\n        }, {\n          type: '404',\n          name: '道路障碍物',\n          color: '#ff7a45'\n        }, {\n          type: '405',\n          name: '行人通过马路',\n          color: '#52c41a'\n        }, {\n          type: '904',\n          name: '逆行车辆',\n          color: '#f5222d'\n        }, {\n          type: '910',\n          name: '违停车辆',\n          color: '#ff4d4f'\n        }, {\n          type: '1002',\n          name: '道路施工',\n          color: '#1890ff'\n        }, {\n          type: '901',\n          name: '车辆超速',\n          color: '#fa8c16'\n        }];\n\n        // 处理数据\n        const data = eventTypes.map(event => ({\n          value: eventStats[event.type] || 0,\n          name: event.name,\n          itemStyle: {\n            color: event.color\n          }\n        })).filter(item => item.value > 0).sort((a, b) => b.value - a.value);\n        const option = {\n          title: {\n            text: `最后更新: ${currentTime.toLocaleTimeString()}`,\n            left: 'center',\n            top: -3,\n            textStyle: {\n              fontSize: 12,\n              color: '#999'\n            }\n          },\n          grid: {\n            top: 30,\n            bottom: 0,\n            left: 0,\n            right: 50,\n            containLabel: true\n          },\n          animation: true,\n          animationDuration: 0,\n          animationDurationUpdate: 1000,\n          animationEasingUpdate: 'quinticInOut',\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'shadow'\n            }\n          },\n          xAxis: {\n            type: 'value',\n            show: false,\n            splitLine: {\n              show: false\n            }\n          },\n          yAxis: {\n            type: 'category',\n            data: data.map(item => item.name),\n            axisLabel: {\n              fontSize: 12,\n              color: '#666',\n              margin: 8\n            },\n            axisTick: {\n              show: false\n            },\n            axisLine: {\n              show: false\n            }\n          },\n          series: [{\n            type: 'bar',\n            data: data,\n            barWidth: '50%',\n            label: {\n              show: true,\n              position: 'right',\n              formatter: '{c}次',\n              fontSize: 12,\n              color: '#666'\n            },\n            itemStyle: {\n              borderRadius: [0, 4, 4, 0]\n            },\n            realtimeSort: false,\n            animationDelay: function (idx) {\n              return idx * 100;\n            }\n          }]\n        };\n\n        // 使用 notMerge: false 来保持增量更新\n        chart.setOption(option, {\n          notMerge: false,\n          replaceMerge: ['series']\n        });\n      };\n\n      // 初始更新\n      updateChart();\n\n      // 监听事件统计变化，每分钟更新一次\n      const statsInterval = setInterval(updateChart, 60000); // 60000ms = 1分钟\n\n      // 监听窗口大小变化\n      handleResize = () => {\n        var _chart;\n        (_chart = chart) === null || _chart === void 0 ? void 0 : _chart.resize();\n      };\n      window.addEventListener('resize', handleResize);\n\n      // 清理函数\n      return () => {\n        clearInterval(statsInterval);\n        if (handleResize) {\n          window.removeEventListener('resize', handleResize);\n        }\n        if (chart) {\n          chart.dispose();\n        }\n      };\n    } catch (error) {\n      console.error('初始化图表失败:', error);\n    }\n  }, [eventStats]);\n\n  // 修改 RSI 消息处理逻辑\n  useEffect(() => {\n    const handleRsiMessage = event => {\n      try {\n        if (event.data && event.data.type === 'RSI') {\n          const rsiData = event.data.data;\n          if (!rsiData || !rsiData.rtes) return;\n          // if(rsiData.rtes.length > 0){\n          //   // console.log('RealTimeTraffic 收到 RSI 消息:', event.data);\n          //   if(parseFloat(rsiData.posLong) < 113.0){\n          //     console.log('位置不在测试范围的 RSI 消息:', event.data);\n          //     console.log('收到RSI消息，但位置不在测试范围内，忽略');\n          //     return;\n          //   }\n          // }\n\n          const latitude = parseFloat(rsiData.posLat);\n          const longitude = parseFloat(rsiData.posLong);\n          const rsuId = rsiData.rsuId;\n          const mac = event.data.mac || '';\n          const timestamp = event.data.tm || Date.now();\n\n          // 统计本帧所有非重复事件类型\n          const nonDuplicateEventTypes = [];\n          rsiData.rtes.forEach(event => {\n            const eventType = event.eventType;\n\n            // 根据事件类型设置不同的位置精度和去重策略\n            let latFixed = '';\n            let lngFixed = '';\n            let eventKey = '';\n            if (eventType === '904' || eventType === '901') {\n              // 逆行和超速：使用3位小数精度（约111米范围）\n              latFixed = Math.floor(latitude * Math.pow(10, 3)) / Math.pow(10, 3);\n              lngFixed = Math.floor(longitude * Math.pow(10, 3)) / Math.pow(10, 3);\n              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;\n            } else if (eventType === '910') {\n              // 违停车辆：使用2位小数精度（约1.1公里范围），忽略MAC地址\n              // 这样可以将相近位置的违停事件归为同一类\n              latFixed = Math.floor(latitude * Math.pow(10, 4)) / Math.pow(10, 4);\n              lngFixed = Math.floor(longitude * Math.pow(10, 4)) / Math.pow(10, 4);\n              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;\n            } else {\n              // 其他事件：使用4位小数精度（约11米范围）\n              latFixed = Math.floor(latitude * Math.pow(10, 4)) / Math.pow(10, 4);\n              lngFixed = Math.floor(longitude * Math.pow(10, 4)) / Math.pow(10, 4);\n              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;\n            }\n            const duplicateResult = checkDuplicateEvent(eventType, eventKey, timestamp, {\n              lat: latitude,\n              lng: longitude\n            });\n            const isDuplicate = duplicateResult.isDuplicate;\n            // 获取事件类型的中文描述\n            let eventTypeText = '';\n            let eventColor = '';\n            switch (eventType) {\n              case '401':\n                eventTypeText = '道路抛洒物';\n                eventColor = '#faad14';\n                break;\n              case '404':\n                eventTypeText = '道路障碍物';\n                eventColor = '#ff7a45';\n                break;\n              case '405':\n                eventTypeText = '行人通过马路';\n                eventColor = '#52c41a';\n                break;\n              case '904':\n                eventTypeText = '逆行车辆';\n                eventColor = '#f5222d';\n                break;\n              case '910':\n                eventTypeText = '违停车辆';\n                eventColor = '#ff4d4f';\n                break;\n              case '1002':\n                eventTypeText = '道路施工';\n                eventColor = '#1890ff';\n                break;\n              case '901':\n                eventTypeText = '车辆超速';\n                eventColor = '#fa8c16';\n                break;\n              default:\n                eventTypeText = event.description || '未知事件';\n                eventColor = '#8c8c8c';\n            }\n            // 更新事件列表\n            // const newEvent = {\n            //   key: Date.now() + Math.random(),\n            //   type: eventTypeText,\n            //   time: new Date().toLocaleTimeString(),\n            //   vehicle: rsiData.rsuId || '未知设备',\n            //   color: eventColor,\n            //   eventType: eventType,\n            //   location: {\n            //     latitude: latitude,\n            //     longitude: longitude\n            //   }\n\n            // };\n            // setEvents(prev => {\n            //   const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n            //   return newEvents;\n            // });\n            if (!isDuplicate) {\n              // 创建事件数据，使用新的事件ID\n              const eventData = {\n                eventId: duplicateResult.eventId,\n                eventType: eventType,\n                eventTypeText: eventTypeText,\n                rsuId: rsiData.rsuId || '未知设备',\n                mac: mac,\n                latitude: latitude,\n                longitude: longitude,\n                site: getNearestIntersectionName(latitude, longitude),\n                eventKey: eventKey,\n                color: eventColor,\n                timestamp: new Date().toISOString()\n              };\n\n              // 存储到数据库\n              storeEventToDatabase(eventData).then(success => {\n                if (success) {\n                  console.log(`✅ 事件已存储到数据库: ${eventTypeText}`);\n\n                  // 更新本地事件列表（用于实时显示）\n                  const newEvent = {\n                    key: Date.now() + Math.random(),\n                    type: eventTypeText,\n                    time: new Date().toLocaleTimeString(),\n                    rsuId: rsiData.rsuId || '未知设备',\n                    color: eventColor,\n                    eventType: eventType,\n                    location: {\n                      latitude: latitude,\n                      longitude: longitude\n                    },\n                    site: eventData.site\n                  };\n                  setEvents(prev => {\n                    const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n                    return newEvents;\n                  });\n                } else {\n                  console.error(`❌ 事件存储失败: ${eventTypeText}`);\n                }\n              });\n              nonDuplicateEventTypes.push(eventType);\n            } else {\n              // console.log(`检测到重复事件，不累计统计: ${eventTypeText}`);\n            }\n          });\n\n          // 如果有新事件，从数据库刷新统计数据\n          if (nonDuplicateEventTypes.length > 0) {\n            console.log(`📊 检测到 ${nonDuplicateEventTypes.length} 个新事件，刷新统计数据`);\n\n            // 延迟1秒后从数据库获取最新统计，确保数据已存储\n            console.log('实际的时间段2', selectedTimeRange);\n            setTimeout(async () => {\n              const stats = await fetchEventStatsFromDatabase(selectedTimeRange);\n              if (stats) {\n                setEventStats(prevStats => ({\n                  ...prevStats,\n                  ...stats\n                }));\n                console.log('✅ 事件统计数据已从数据库更新，时间段:', selectedTimeRange);\n              }\n            }, 1000);\n          }\n        }\n      } catch (error) {\n        console.error('处理 RSI 消息失败:', error);\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleRsiMessage);\n    return () => {\n      window.removeEventListener('message', handleRsiMessage);\n    };\n  }, []);\n\n  // 获取事件类型的阈值配置\n  const getEventThresholds = eventType => {\n    switch (eventType) {\n      case '910':\n        // 违停车辆\n        return {\n          timeThreshold: 300000,\n          distanceThreshold: 10\n        };\n      // 5分钟, 20米\n      case '904':\n        // 逆行车辆\n        return {\n          timeThreshold: 10000,\n          distanceThreshold: 20\n        };\n      // 10秒, 20米\n      case '901':\n        // 车辆超速\n        return {\n          timeThreshold: 30000,\n          distanceThreshold: 20\n        };\n      // 30秒, 50米\n      case '401': // 道路抛洒物\n      case '404': // 道路障碍物\n      case '1002':\n        // 道路施工\n        return {\n          timeThreshold: 600000,\n          distanceThreshold: 5\n        };\n      // 10分钟, 30米\n      case '405':\n        // 行人通过马路\n        return {\n          timeThreshold: 10000,\n          distanceThreshold: 10\n        };\n      // 10秒, 10米\n      default:\n        return {\n          timeThreshold: 5000,\n          distanceThreshold: 5\n        };\n      // 5秒, 5米\n    }\n  };\n\n  // 优化后的事件重复检查逻辑\n  const checkDuplicateEvent = (eventType, eventKey, currentTime, currentPos) => {\n    const {\n      timeThreshold,\n      distanceThreshold\n    } = getEventThresholds(eventType);\n\n    // 遍历事件列表缓存中的所有事件\n    for (let i = 0; i < eventListCache.current.length; i++) {\n      const cachedEvent = eventListCache.current[i];\n\n      // 检查事件类型是否相同\n      if (cachedEvent.eventType !== eventType) {\n        continue;\n      }\n\n      // 计算时间差\n      const timeDiff = currentTime - cachedEvent.lastUpdateTime;\n\n      // 检查时间差是否在阈值内\n      if (timeDiff > timeThreshold) {\n        continue;\n      }\n\n      // 计算距离\n      const distance = calculateDistance(currentPos.lat, currentPos.lng, cachedEvent.position.lat, cachedEvent.position.lng);\n\n      // 检查距离是否在阈值内\n      if (distance <= distanceThreshold) {\n        // 找到匹配的事件，更新信息\n        cachedEvent.eventKey = eventKey;\n        cachedEvent.lastUpdateTime = currentTime;\n        cachedEvent.position = {\n          ...currentPos\n        };\n        cachedEvent.updateCount = (cachedEvent.updateCount || 1) + 1;\n\n        // console.log(`🔄 检测到重复事件 ${eventType} (ID: ${cachedEvent.eventId})，时间差: ${(timeDiff/1000).toFixed(1)}s，距离: ${distance.toFixed(1)}m，更新次数: ${cachedEvent.updateCount}`);\n\n        return {\n          isDuplicate: true,\n          eventId: cachedEvent.eventId,\n          matchedEvent: cachedEvent\n        };\n      }\n    }\n\n    // 没有找到匹配的事件，创建新事件\n    const newEventId = `EVT_${eventIdCounter.current.toString().padStart(6, '0')}`;\n    eventIdCounter.current++;\n    const newEvent = {\n      eventId: newEventId,\n      eventType: eventType,\n      eventKey: eventKey,\n      firstDetectedTime: currentTime,\n      lastUpdateTime: currentTime,\n      position: {\n        ...currentPos\n      },\n      updateCount: 1\n    };\n\n    // 添加到事件列表缓存\n    eventListCache.current.push(newEvent);\n    console.log(`✨ 创建新事件 ${eventType} (ID: ${newEventId})，位置: (${currentPos.lat.toFixed(6)}, ${currentPos.lng.toFixed(6)})`);\n    return {\n      isDuplicate: false,\n      eventId: newEventId,\n      newEvent: newEvent\n    };\n  };\n\n  // 清理过期事件的函数\n  const cleanupExpiredEvents = () => {\n    const currentTime = Date.now();\n    const maxAge = 3600000; // 1小时\n\n    const initialCount = eventListCache.current.length;\n    eventListCache.current = eventListCache.current.filter(event => {\n      const age = currentTime - event.lastUpdateTime;\n      return age <= maxAge;\n    });\n    const removedCount = initialCount - eventListCache.current.length;\n    if (removedCount > 0) {\n      console.log(`🧹 清理了 ${removedCount} 个过期事件，当前缓存事件数: ${eventListCache.current.length}`);\n    }\n  };\n\n  // 删除1分钟内没有更新的事件\n  const removeInactiveEvents = () => {\n    const currentTime = Date.now();\n    const inactiveThreshold = 60000; // 1分钟\n\n    const initialCount = eventListCache.current.length;\n    const removedEvents = [];\n    eventListCache.current = eventListCache.current.filter(event => {\n      const timeSinceLastUpdate = currentTime - event.lastUpdateTime;\n      if (timeSinceLastUpdate > inactiveThreshold) {\n        removedEvents.push({\n          id: event.eventId,\n          type: event.eventType,\n          inactiveTime: (timeSinceLastUpdate / 1000).toFixed(1)\n        });\n        return false; // 删除该事件\n      }\n      return true; // 保留该事件\n    });\n    const removedCount = initialCount - eventListCache.current.length;\n    if (removedCount > 0) {\n      console.log(`🗑️ 删除了 ${removedCount} 个1分钟内未更新的事件:`);\n      removedEvents.forEach(event => {\n        console.log(`   - 事件 ${event.id} (类型: ${event.type})，未更新时间: ${event.inactiveTime}秒`);\n      });\n      console.log(`📊 当前缓存事件数: ${eventListCache.current.length}`);\n    }\n  };\n\n  // 添加计算两点之间距离的函数\n  const calculateDistance = (lat1, lon1, lat2, lon2) => {\n    if (lat1 === 0 || lon1 === 0 || lat2 === 0 || lon2 === 0) {\n      return 999;\n    }\n    const R = 6371000;\n    const dLat = (lat2 - lat1) * Math.PI / 180;\n    const dLon = (lon2 - lon1) * Math.PI / 180;\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * Math.sin(dLon / 2) * Math.sin(dLon / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c;\n  };\n\n  // 根据坐标查找最近的路口名称\n  const getNearestIntersectionName = (lat, lng) => {\n    // 从 intersections.json 获取所有路口\n    const intersections = intersectionsData.intersections || [];\n    let minDistance = Infinity;\n    let nearestName = '未知地点';\n    intersections.forEach(inter => {\n      const interLat = parseFloat(inter.latitude);\n      const interLng = parseFloat(inter.longitude);\n      const dist = calculateDistance(lat, lng, interLat, interLng);\n      if (dist < minDistance) {\n        minDistance = dist;\n        nearestName = inter.name;\n      }\n    });\n    return nearestName;\n  };\n\n  // 处理车辆选择\n  const handleVehicleSelect = vehicle => {\n    console.log('选择车辆:', vehicle.plateNumber, '状态:', vehicle.status);\n    setSelectedVehicle(vehicle);\n  };\n\n  // 修改车辆状态更新逻辑，确保同步更新selectedVehicle\n  useEffect(() => {\n    // 当车辆列表更新后，如果当前有选中的车辆，则更新选中的车辆信息\n    if (selectedVehicle) {\n      const updatedSelectedVehicle = vehicles.find(v => v.id === selectedVehicle.id);\n      if (updatedSelectedVehicle && (updatedSelectedVehicle.status !== selectedVehicle.status || updatedSelectedVehicle.speed !== selectedVehicle.speed || updatedSelectedVehicle.lat !== selectedVehicle.lat || updatedSelectedVehicle.lng !== selectedVehicle.lng || updatedSelectedVehicle.heading !== selectedVehicle.heading)) {\n        console.log(`更新选中车辆 ${selectedVehicle.plateNumber} 的信息:`, `状态从 ${selectedVehicle.status} 变为 ${updatedSelectedVehicle.status}`);\n        setSelectedVehicle(updatedSelectedVehicle);\n      }\n    }\n  }, [vehicles, selectedVehicle]);\n\n  // 车辆列表列定义\n  const vehicleColumns = [{\n    title: '车牌号',\n    dataIndex: 'plate',\n    key: 'plate',\n    width: '40%'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: '30%',\n    render: status => /*#__PURE__*/_jsxDEV(Badge, {\n      status: status === 'online' ? 'success' : 'error',\n      text: status === 'online' ? '在线' : '离线'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1360,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '速度',\n    dataIndex: 'speed',\n    key: 'speed',\n    width: '30%',\n    render: speed => `${typeof speed === 'number' ? speed.toFixed(0) : speed} km/h`\n  }];\n\n  // 修改实时事件列表的渲染\n  const renderEventList = () => /*#__PURE__*/_jsxDEV(List, {\n    size: \"small\",\n    dataSource: events,\n    renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n      style: {\n        padding: '8px 0'\n      },\n      children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n        title: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '13px',\n            marginBottom: '4px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: item.color,\n              marginRight: '8px'\n            },\n            children: item.type\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1385,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#666',\n              fontSize: '12px'\n            },\n            children: item.time\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1388,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1384,\n          columnNumber: 15\n        }, this),\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#666'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u5730\\u70B9: \", item.site || '未知地点']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1396,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u4F4D\\u7F6E: \", item.location ? `${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}` : '未知位置']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1397,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1394,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1382,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1381,\n      columnNumber: 9\n    }, this),\n    style: {\n      maxHeight: 'calc(100% - 24px)',\n      overflowY: 'auto'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1377,\n    columnNumber: 5\n  }, this);\n\n  // 添加强制定期从API获取最新数据的机制\n  useEffect(() => {\n    const apiPollingInterval = setInterval(() => {\n      console.log('直接从API检查车辆数据更新');\n      fetchLatestVehiclesData();\n    }, 15000); // 每15秒检查一次API\n\n    return () => clearInterval(apiPollingInterval);\n  }, [vehicles]);\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    spinning: loading,\n    tip: \"\\u52A0\\u8F7D\\u4E2D...\",\n    children: /*#__PURE__*/_jsxDEV(PageContainer, {\n      children: [/*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"left\",\n        collapsed: leftCollapsed,\n        onCollapse: () => setLeftCollapsed(!leftCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u548C\\u8BBE\\u5907\\u7EDF\\u8BA1\",\n          bordered: false,\n          height: \"160px\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [8, 1],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              style: {\n                display: 'flex',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u8F66\\u8F86\\u603B\\u6570\",\n                value: stats.totalVehicles,\n                valueStyle: {\n                  color: '#3f8600',\n                  display: 'flex',\n                  justifyContent: 'center'\n                }\n                // Style={{}}\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1436,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1435,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              style: {\n                display: 'flex',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u5728\\u7EBF\\u8F66\\u8F86\",\n                value: stats.onlineVehicles\n                // suffix={`/ ${stats.totalVehicles}`}\n                ,\n                valueStyle: {\n                  color: '#3f8600',\n                  display: 'flex',\n                  justifyContent: 'center'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1444,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1443,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              style: {\n                display: 'flex',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u79BB\\u7EBF\\u8F66\\u8F86\",\n                value: stats.offlineVehicles\n                // suffix={`/ ${stats.totalVehicles}`}\n                ,\n                valueStyle: {\n                  color: '#cf1322',\n                  display: 'flex',\n                  justifyContent: 'center'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1452,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1451,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              style: {\n                display: 'flex',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u8BBE\\u5907\\u603B\\u6570\",\n                value: stats.totalDevices,\n                valueStyle: {\n                  color: '#3f8600',\n                  display: 'flex',\n                  justifyContent: 'center'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1460,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1459,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              style: {\n                display: 'flex',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u5728\\u7EBF\\u8BBE\\u5907\",\n                value: stats.onlineDevices\n                // suffix={`/ ${stats.totalDevices}`}\n                ,\n                valueStyle: {\n                  color: '#3f8600',\n                  display: 'flex',\n                  justifyContent: 'center'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1467,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1466,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 8,\n              style: {\n                display: 'flex',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(CompactStatistic, {\n                title: \"\\u79BB\\u7EBF\\u8BBE\\u5907\",\n                value: stats.offlineDevices\n                // suffix={`/ ${stats.totalDevices}`}\n                ,\n                valueStyle: {\n                  color: '#cf1322',\n                  display: 'flex',\n                  justifyContent: 'center'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1475,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1474,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1434,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1433,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u5217\\u8868\",\n          bordered: false,\n          height: \"calc(50% - 95px)\"\n          // extra={\n          //   <div>\n          //     <Button\n          //       size=\"small\"\n          //       type=\"link\"\n          //       onClick={() => {\n          //         console.log('🧪 查看事件缓存状态');\n          //         console.log('📊 当前事件缓存状态:', {\n          //           缓存事件数: eventListCache.current.length,\n          //           事件ID计数器: eventIdCounter.current,\n          //           事件列表: eventListCache.current.map(e => {\n          //             const timeSinceUpdate = (Date.now() - e.lastUpdateTime) / 1000;\n          //             return {\n          //               ID: e.eventId,\n          //               类型: e.eventType,\n          //               更新次数: e.updateCount,\n          //               位置: `${e.position.lat.toFixed(6)}, ${e.position.lng.toFixed(6)}`,\n          //               未更新时间: `${timeSinceUpdate.toFixed(1)}秒`\n          //             };\n          //           })\n          //         });\n          //       }}\n          //     >\n          //       查看缓存\n          //     </Button>\n          //     <Button\n          //       size=\"small\"\n          //       type=\"link\"\n          //       onClick={() => {\n          //         console.log('🗑️ 手动删除非活跃事件');\n          //         removeInactiveEvents();\n          //       }}\n          //     >\n          //       删除非活跃\n          //     </Button>\n          //     <Button\n          //       size=\"small\"\n          //       type=\"link\"\n          //       onClick={() => {\n          //         console.log('🧹 手动清理过期事件');\n          //         cleanupExpiredEvents();\n          //       }}\n          //     >\n          //       清理过期\n          //     </Button>\n          //   </div>\n          // }\n          ,\n          children: renderEventList()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1486,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u5B9E\\u65F6\\u4E8B\\u4EF6\\u7EDF\\u8BA1\",\n          bordered: false,\n          height: \"calc(50% - 95px)\",\n          extra: /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedTimeRange,\n            onChange: handleTimeRangeChange,\n            onSelect: handleTimeRangeChange,\n            size: \"small\",\n            style: {\n              width: 80\n            },\n            options: [{\n              value: '1h',\n              label: '1小时'\n            }, {\n              value: '24h',\n              label: '1天'\n            }, {\n              value: '7d',\n              label: '7天'\n            }, {\n              value: 'all',\n              label: '全部'\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1547,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: eventChartRef,\n            style: {\n              height: '100%',\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1562,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1542,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1427,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        leftCollapsed: leftCollapsed,\n        rightCollapsed: rightCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1567,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"right\",\n        collapsed: rightCollapsed,\n        onCollapse: () => setRightCollapsed(!rightCollapsed),\n        children: [/*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u5217\\u8868\",\n          bordered: false,\n          height: \"50%\",\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            dataSource: vehicles,\n            columns: vehicleColumns,\n            rowKey: \"id\",\n            pagination: false,\n            size: \"small\",\n            scroll: {\n              y: 180\n            },\n            onRow: record => ({\n              onClick: () => handleVehicleSelect(record),\n              style: {\n                cursor: 'pointer',\n                background: (selectedVehicle === null || selectedVehicle === void 0 ? void 0 : selectedVehicle.id) === record.id ? '#e6f7ff' : 'transparent',\n                fontSize: '13px',\n                padding: '4px 8px'\n              }\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1579,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1578,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8F66\\u8F86\\u8BE6\\u7EC6\\u4FE1\\u606F\",\n          bordered: false,\n          height: \"50%\",\n          children: selectedVehicle ? /*#__PURE__*/_jsxDEV(Descriptions, {\n            bordered: true,\n            column: 1,\n            size: \"small\",\n            styles: {\n              label: {\n                fontSize: '13px',\n                padding: '4px 8px'\n              },\n              content: {\n                fontSize: '13px',\n                padding: '4px 8px'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8F66\\u724C\\u53F7\",\n              children: selectedVehicle.plateNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1610,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                status: selectedVehicle.status === 'online' ? 'success' : 'error',\n                text: selectedVehicle.status === 'online' ? '在线' : '离线'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1612,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1611,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7ECF\\u5EA6\",\n              children: selectedVehicle.status === 'online' ? selectedVehicle.lng.toFixed(7) : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1617,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u7EAC\\u5EA6\",\n              children: selectedVehicle.status === 'online' ? selectedVehicle.lat.toFixed(7) : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1620,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u901F\\u5EA6\",\n              children: selectedVehicle.status === 'online' ? `${typeof selectedVehicle.speed === 'number' ? selectedVehicle.speed.toFixed(0) : selectedVehicle.speed} km/h` : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1623,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u822A\\u5411\\u89D2\",\n              children: selectedVehicle.status === 'online' ? `${typeof selectedVehicle.heading === 'number' ? selectedVehicle.heading.toFixed(2) : selectedVehicle.heading}°` : 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1628,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1601,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '13px'\n            },\n            children: \"\\u8BF7\\u9009\\u62E9\\u8F66\\u8F86\\u67E5\\u770B\\u8BE6\\u7EC6\\u4FE1\\u606F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1635,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1599,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1572,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1425,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1424,\n    columnNumber: 5\n  }, this);\n};\n_s(RealTimeTraffic, \"+vct2WXYRiobLz+XJIbPCuIX6pE=\");\n_c5 = RealTimeTraffic;\nexport default RealTimeTraffic;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"InfoCard\");\n$RefreshReg$(_c4, \"CompactStatistic\");\n$RefreshReg$(_c5, \"RealTimeTraffic\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "Row", "Col", "Card", "Statistic", "List", "Table", "Descriptions", "Spin", "Badge", "<PERSON><PERSON>", "Select", "echarts", "<PERSON><PERSON><PERSON>", "GridComponent", "TooltipComponent", "LegendComponent", "TitleComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styled", "CollapsibleSidebar", "axios", "vehiclesData", "intersectionsData", "jsxDEV", "_jsxDEV", "use", "StyledCanvas", "div", "<PERSON><PERSON><PERSON><PERSON>", "_c", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "_c2", "InfoCard", "height", "_c3", "CompactStatistic", "_c4", "RealTimeTraffic", "_s", "loading", "setLoading", "vehicles", "setVehicles", "events", "setEvents", "selectedVehicle", "setSelectedVehicle", "stats", "setStats", "totalVehicles", "onlineVehicles", "offlineVehicles", "totalDevices", "onlineDevices", "offlineDevices", "onlineBsmIds", "setOnlineBsmIds", "savedOnlineIds", "localStorage", "getItem", "Set", "error", "console", "lastBsmTime", "eventChartRef", "setLeftCollapsed", "setRightCollapsed", "selectedTimeRange", "setSelectedTimeRange", "eventStats", "setEventStats", "prevRsiEvents", "Map", "eventListCache", "eventIdCounter", "storeEventToDatabase", "eventData", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "post", "data", "success", "log", "storage", "eventTypeText", "fetchEventStatsFromDatabase", "timeRange", "get", "fetchRecentEventsFromDatabase", "limit", "length", "updateVehicleStatus", "bsmId", "status", "speed", "lat", "lng", "heading", "formattedSpeed", "parseFloat", "toFixed", "formattedHeading", "prev", "current", "Date", "now", "newSet", "delete", "prevVehicles", "map", "vehicle", "window", "handleRealBsmReceived", "event", "type", "addEventListener", "removeEventListener", "loadEventsFromDatabase", "recentEvents", "prevStats", "handleTimeRangeChange", "value", "refreshInterval", "setInterval", "clearInterval", "cleanupInterval", "cleanupExpiredEvents", "removeInactiveInterval", "removeInactiveEvents", "fetchVehicles", "apiData", "fetchLatestVehiclesData", "bsmIds", "v", "filter", "id", "updatedVehicles", "isOnline", "has", "plate", "plateNumber", "onlineCount", "totalCount", "vehiclesList", "fetchDeviceStats", "devicesData", "d", "handleBsmMessage", "bsmData", "partSpeed", "partLat", "partLong", "partHeading", "foundVehicle", "find", "checkOnlineStatus", "newOnlineBsmIds", "has<PERSON><PERSON><PERSON>", "for<PERSON>ach", "lastTime", "interval", "resetAllVehicles", "size", "loadData", "handleVehiclesDataChanged", "handleStorageChange", "key", "lastUpdated", "forcedPollingInterval", "returnData", "possibleApiUrls", "succeeded", "processVehiclesData", "message", "fetch", "ok", "json", "e", "newVehicles", "currentIds", "hasNewVehicle", "some", "chart", "handleResize", "lastUpdateTime", "existingChart", "getInstanceByDom", "dispose", "init", "updateChart", "currentTime", "eventTypes", "name", "color", "itemStyle", "item", "sort", "a", "b", "option", "title", "text", "toLocaleTimeString", "left", "top", "textStyle", "fontSize", "grid", "bottom", "right", "containLabel", "animation", "animationDuration", "animationDurationUpdate", "animationEasingUpdate", "tooltip", "trigger", "axisPointer", "xAxis", "show", "splitLine", "yAxis", "axisLabel", "margin", "axisTick", "axisLine", "series", "<PERSON><PERSON><PERSON><PERSON>", "label", "position", "formatter", "borderRadius", "realtimeSort", "animationDelay", "idx", "setOption", "notMerge", "replaceMerge", "statsInterval", "_chart", "resize", "handleRsiMessage", "rsiData", "rtes", "latitude", "posLat", "longitude", "posLong", "rsuId", "mac", "timestamp", "tm", "nonDuplicateEventTypes", "eventType", "latFixed", "lngFixed", "eventKey", "Math", "floor", "pow", "duplicateResult", "checkDuplicateEvent", "isDuplicate", "eventColor", "description", "eventId", "site", "getNearestIntersectionName", "toISOString", "then", "newEvent", "random", "time", "location", "newEvents", "slice", "push", "setTimeout", "getEventThresholds", "timeT<PERSON><PERSON>old", "distanceThreshold", "currentPos", "i", "cachedEvent", "timeDiff", "distance", "calculateDistance", "updateCount", "matchedEvent", "newEventId", "toString", "padStart", "firstDetectedTime", "maxAge", "initialCount", "age", "removedCount", "inactiveThreshold", "removedEvents", "timeSinceLastUpdate", "inactiveTime", "lat1", "lon1", "lat2", "lon2", "R", "dLat", "PI", "dLon", "sin", "cos", "c", "atan2", "sqrt", "intersections", "minDistance", "Infinity", "nearestName", "inter", "interLat", "interLng", "dist", "handleVehicleSelect", "updatedSelectedVehicle", "vehicleColumns", "dataIndex", "width", "render", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderEventList", "dataSource", "renderItem", "<PERSON><PERSON>", "style", "padding", "children", "Meta", "marginBottom", "marginRight", "maxHeight", "overflowY", "apiPollingInterval", "spinning", "tip", "collapsed", "onCollapse", "bordered", "gutter", "span", "display", "justifyContent", "valueStyle", "extra", "onChange", "onSelect", "options", "ref", "columns", "<PERSON><PERSON><PERSON>", "pagination", "scroll", "y", "onRow", "record", "onClick", "cursor", "background", "column", "styles", "content", "_c5", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RealTimeTraffic.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { Row, Col, Card, Statistic, List, Table, Descriptions, Spin, Badge, Button, Select } from 'antd';\nimport * as echarts from 'echarts/core';\nimport { Bar<PERSON>hart } from 'echarts/charts';\nimport { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components';\nimport { CanvasRenderer } from 'echarts/renderers';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\nimport axios from 'axios';\nimport vehiclesData from '../data/vehicles.json';\nimport intersectionsData from '../data/intersections.json'; // 导入路口数据\n\n// 注册必要的echarts组件\necharts.use([BarChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer]);\n\nconst StyledCanvas = styled.div`\n  width: 100%;\n  height: 600px;\n  background: #f0f2f5;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n`;\n\n// 页面布局容器\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 65px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' :\n    props.leftCollapsed ? '0 8px 0 24px' :\n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' :\n    props.leftCollapsed ? '0 8px 0 0' :\n    props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n\n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n\n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n\n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 自定义统计数字组件\nconst CompactStatistic = styled(Statistic)`\n  .ant-statistic-title {\n    font-size: 12px;\n    margin-bottom: 2px;\n  }\n\n  .ant-statistic-content {\n    font-size: 16px;\n  }\n`;\n\nconst RealTimeTraffic = () => {\n  const [loading, setLoading] = useState(true);\n  const [vehicles, setVehicles] = useState([]);\n  const [events, setEvents] = useState([]);\n  const [selectedVehicle, setSelectedVehicle] = useState(null);\n  const [stats, setStats] = useState({\n    totalVehicles: 0,\n    onlineVehicles: 0,\n    offlineVehicles: 0,\n    totalDevices: 0,\n    onlineDevices: 0,\n    offlineDevices: 0\n  });\n\n  // 存储在线车辆的 BSM ID，初始时为空集合，表示所有车辆离线\n  const [onlineBsmIds, setOnlineBsmIds] = useState(() => {\n    try {\n      const savedOnlineIds = localStorage.getItem('realTimeTrafficOnlineIds');\n      // 返回空集合，确保所有车辆初始状态为离线\n      return new Set();\n    } catch (error) {\n      console.error('读取在线ID缓存失败:', error);\n      return new Set();\n    }\n  });\n  // 存储最后一次收到 BSM 消息的时间\n  const lastBsmTime = useRef({});\n\n  const eventChartRef = useRef(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  // 添加时间段选择状态\n  const [selectedTimeRange, setSelectedTimeRange] = useState('1h');\n\n  const [eventStats, setEventStats] = useState({\n    '401': 0,  // 道路抛洒物\n    '404': 0,  // 道路障碍物\n    '405': 0,  // 行人通过马路\n    '904': 0,  // 逆行车辆\n    '910': 0,  // 违停车辆\n    '1002': 0, // 道路施工\n    '901': 0   // 车辆超速\n  });\n\n  // 添加RSI事件缓存，用于检测重复事件\n  const prevRsiEvents = useRef(new Map());\n\n  // 事件列表缓存，存储所有事件的完整信息\n  const eventListCache = useRef([]);\n\n  // 事件ID计数器\n  const eventIdCounter = useRef(1);\n\n  // ========== 数据库API调用函数 ==========\n\n  /**\n   * 存储实时事件到数据库\n   */\n  const storeEventToDatabase = async (eventData) => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.post(`${apiUrl}/api/events/store`, eventData);\n\n      if (response.data && response.data.success) {\n        console.log(`✅ 事件已存储到${response.data.storage}:`, eventData.eventTypeText);\n        return true;\n      } else {\n        console.error('❌ 存储事件失败:', response.data);\n        return false;\n      }\n    } catch (error) {\n      console.error('❌ 存储事件到数据库失败:', error);\n      return false;\n    }\n  };\n\n  /**\n   * 从数据库获取事件统计\n   */\n  const fetchEventStatsFromDatabase = async (timeRange = '1h') => {\n    try {\n      // console.log(`timeRange);\n      console.log('实际的时间段0', timeRange);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/events/stats?timeRange=${timeRange}`);\n\n      if (response.data && response.data.success) {\n        console.log(`📊 从${response.data.storage}获取事件统计(${timeRange}):`, response.data.data);\n        return response.data.data;\n      } else {\n        console.error('❌ 获取事件统计失败:', response.data);\n        return null;\n      }\n    } catch (error) {\n      console.error('❌ 从数据库获取事件统计失败:', error);\n      return null;\n    }\n  };\n\n  /**\n   * 从数据库获取最近事件\n   */\n  const fetchRecentEventsFromDatabase = async (limit = 10) => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/events/recent?limit=${limit}`);\n\n      if (response.data && response.data.success) {\n        console.log(`📋 从${response.data.storage}获取最近事件:`, response.data.data.length, '条');\n        return response.data.data;\n      } else {\n        console.error('❌ 获取最近事件失败:', response.data);\n        return [];\n      }\n    } catch (error) {\n      console.error('❌ 从数据库获取最近事件失败:', error);\n      return [];\n    }\n  };\n\n  // 添加手动更新车辆状态和位置信息的函数\n  const updateVehicleStatus = useCallback((bsmId, status, speed = 0, lat = 0, lng = 0, heading = 0) => {\n    // 确保速度和航向角是格式化的数值，保留两位小数\n    const formattedSpeed = parseFloat(speed).toFixed(0);\n    const formattedHeading = parseFloat(heading).toFixed(2);\n\n    console.log(`手动更新车辆状态: ID=${bsmId}, 状态=${status}, 速度=${formattedSpeed}, 位置=(${lat}, ${lng})`);\n\n    // 更新在线状态\n    if (status === 'online') {\n      setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n      lastBsmTime.current[bsmId] = Date.now();\n    } else {\n      setOnlineBsmIds(prev => {\n        const newSet = new Set(prev);\n        newSet.delete(bsmId);\n        return newSet;\n      });\n    }\n\n    // 更新车辆信息\n    setVehicles(prevVehicles =>\n      prevVehicles.map(vehicle =>\n        vehicle.bsmId === bsmId\n          ? {\n              ...vehicle,\n              status: status,\n              speed: parseFloat(formattedSpeed), // 确保是数值类型\n              lat: parseFloat(lat.toFixed(7)),\n              lng: parseFloat(lng.toFixed(7)),\n              heading: parseFloat(formattedHeading) // 确保是数值类型\n            }\n          : vehicle\n      )\n    );\n  }, []);\n\n  // 在组件挂载时，暴露updateVehicleStatus函数到window对象\n  useEffect(() => {\n    window.updateVehicleStatus = updateVehicleStatus;\n\n    // 用于监听CampusModel是否接收到BSM消息的事件\n    const handleRealBsmReceived = (event) => {\n      if (event.data && event.data.type === 'realBsmReceived') {\n        // console.log('收到CampusModel发送的真实BSM消息通知');\n      }\n    };\n\n    window.addEventListener('message', handleRealBsmReceived);\n\n    // 确保页面刷新时清空在线车辆状态\n    console.log('组件初始化，重置所有车辆为离线状态');\n    setOnlineBsmIds(new Set());\n    lastBsmTime.current = {};\n\n    return () => {\n      window.removeEventListener('message', handleRealBsmReceived);\n      delete window.updateVehicleStatus;\n    };\n  }, [updateVehicleStatus]);\n\n  // 从数据库加载事件数据\n  const loadEventsFromDatabase = async (timeRange = selectedTimeRange) => {\n    \n    try {\n      // 加载最近事件\n      const recentEvents = await fetchRecentEventsFromDatabase(10);\n      if (recentEvents && recentEvents.length > 0) {\n        setEvents(recentEvents);\n        console.log('✅ 从数据库加载了', recentEvents.length, '条最近事件');\n      }\n\n      // 加载事件统计（使用选择的时间段）\n      console.log('实际的时间段1', timeRange);\n      const stats = await fetchEventStatsFromDatabase(timeRange);\n      if (stats) {\n        setEventStats(prevStats => ({\n          ...prevStats,\n          ...stats\n        }));\n        console.log(`✅ 从数据库加载了事件统计数据(${timeRange})`);\n      }\n    } catch (error) {\n      console.error('❌ 从数据库加载事件数据失败:', error);\n    }\n  };\n\n  // 处理时间段选择变化\n  const handleTimeRangeChange = (value) => {\n    console.log('📅 时间段选择变化:', value);\n    setSelectedTimeRange(value);\n    // 立即重新加载事件统计数据\n    loadEventsFromDatabase(value);\n  };\n\n  // 组件挂载时从数据库加载数据\n  useEffect(() => {\n    loadEventsFromDatabase(selectedTimeRange);\n  }, []);\n\n  // 监听时间段变化，重新加载事件统计数据\n  useEffect(() => {\n    loadEventsFromDatabase(selectedTimeRange);\n  }, [selectedTimeRange]);\n\n  // 定期从数据库刷新数据（每30秒）\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      console.log('🔄 定期刷新事件数据...', '当前时间段:', selectedTimeRange);\n      loadEventsFromDatabase(selectedTimeRange);\n    }, 30000); // 30秒刷新一次\n\n    return () => clearInterval(refreshInterval);\n  }, [selectedTimeRange]); // 添加 selectedTimeRange 作为依赖\n\n  // 定期清理过期事件（每5分钟）\n  useEffect(() => {\n    const cleanupInterval = setInterval(() => {\n      cleanupExpiredEvents();\n    }, 300000); // 5分钟清理一次\n\n    return () => clearInterval(cleanupInterval);\n  }, []);\n\n  // 定期删除1分钟内没有更新的事件（每30秒检查一次）\n  useEffect(() => {\n    const removeInactiveInterval = setInterval(() => {\n      removeInactiveEvents();\n    }, 30000); // 30秒检查一次\n\n    return () => clearInterval(removeInactiveInterval);\n  }, []);\n\n  // 获取车辆数据\n  const fetchVehicles = useCallback(async () => {\n    try {\n      // 先尝试从API获取最新数据\n      console.log('尝试从API获取最新车辆数据');\n      const apiData = await fetchLatestVehiclesData(true);\n\n      // 如果API获取成功，直接使用API数据\n      if (apiData && apiData.length > 0) {\n        console.log('成功从API获取车辆数据，车辆数量:', apiData.length);\n\n        // 获取所有车辆的BSM ID列表\n        const bsmIds = apiData.map(v => v.bsmId).filter(id => id);\n        console.log('所有车辆的BSM ID:', bsmIds);\n\n        // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\n        const updatedVehicles = apiData.map(vehicle => {\n          // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\n          const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);\n\n          return {\n            ...vehicle,\n            plate: vehicle.plateNumber, // 适配表格显示\n            status: isOnline ? 'online' : 'offline', // 只有收到BSM消息的车辆才显示为在线\n            speed: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0) : 0,\n            lat: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0) : 0,\n            lng: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0) : 0,\n            heading: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0) : 0\n          };\n        });\n\n        setVehicles(updatedVehicles);\n\n        // 直接检查更新后的车辆在线状态\n        const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n        const totalCount = updatedVehicles.length;\n\n        console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);\n\n        // 更新车辆统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalVehicles: totalCount,\n          onlineVehicles: onlineCount,\n          offlineVehicles: totalCount - onlineCount\n        }));\n\n        return;\n      }\n\n      // 如果API获取失败，回退到本地JSON文件\n      console.log('API获取失败，回退到本地vehiclesData');\n      const vehiclesList = vehiclesData.vehicles || [];\n      console.log('从vehiclesData获取车辆数据，车辆数量:', vehiclesList.length);\n\n      // 更新车辆数据，仅将onlineBsmIds中的车辆设置为在线\n      const updatedVehicles = vehiclesList.map(vehicle => {\n        // 只有在onlineBsmIds中的车辆才显示为在线，其他全部为离线\n        const isOnline = vehicle.bsmId && onlineBsmIds.has(vehicle.bsmId);\n\n        return {\n          ...vehicle,\n          plate: vehicle.plateNumber, // 适配表格显示\n          status: isOnline ? 'online' : 'offline', // 只有收到BSM消息的车辆才显示为在线\n          speed: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.speed || 0 : 0) : 0,\n          lat: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lat || 0 : 0) : 0,\n          lng: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.lng || 0 : 0) : 0,\n          heading: isOnline ? (lastBsmTime.current[vehicle.bsmId] ? vehicle.heading || 0 : 0) : 0\n        };\n      });\n\n      setVehicles(updatedVehicles);\n\n      // 直接检查更新后的车辆在线状态\n      const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n      const totalCount = updatedVehicles.length;\n\n      console.log(`更新统计数据: 总数=${totalCount}, 在线=${onlineCount}, 离线=${totalCount - onlineCount}`);\n\n      // 更新车辆统计数据\n      setStats(prevStats => ({\n        ...prevStats,\n        totalVehicles: totalCount,\n        onlineVehicles: onlineCount,\n        offlineVehicles: totalCount - onlineCount\n      }));\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n    }\n  }, [onlineBsmIds]);\n\n  // 获取设备统计数据\n  const fetchDeviceStats = async () => {\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await axios.get(`${apiUrl}/api/devices`);\n\n      if (response.data && response.data.success) {\n        const devicesData = response.data.data;\n\n        // 更新设备统计数据\n        setStats(prevStats => ({\n          ...prevStats,\n          totalDevices: devicesData.length,\n          onlineDevices: devicesData.filter(d => d.status === 'online').length,\n          offlineDevices: devicesData.filter(d => d.status === 'offline').length\n        }));\n      }\n    } catch (error) {\n      console.error('获取设备统计数据失败:', error);\n    }\n  };\n\n  // 监听 BSM 消息\n  useEffect(() => {\n    const handleBsmMessage = (event) => {\n      if (event.data && event.data.type === 'bsm') {\n        // 获取bsmId，确保它正确地从消息中提取\n        const bsmData = event.data.data || {};\n        const bsmId = bsmData.bsmId || event.data.bsmId;\n\n        if (!bsmId) {\n          console.error('BSM消息缺少bsmId:', event.data);\n          return;\n        }\n\n        // console.log('收到BSM消息，ID:', bsmId);\n        const now = Date.now();\n\n        // 更新最后接收时间\n        lastBsmTime.current[bsmId] = now;\n\n        // 添加到在线bsmId集合\n        setOnlineBsmIds(prev => new Set([...prev, bsmId]));\n\n        // 提取正确的BSM数据并格式化为两位小数\n        const speed = parseFloat((parseFloat(bsmData.partSpeed || event.data.speed || 0) * 3.6).toFixed(0)); // 转换为km/h，保留两位小数\n        const lat = parseFloat(parseFloat(bsmData.partLat || event.data.lat || 0).toFixed(7));\n        const lng = parseFloat(parseFloat(bsmData.partLong || event.data.lng || 0).toFixed(7));\n        const heading = parseFloat(parseFloat(bsmData.partHeading || event.data.heading || 0).toFixed(2)); // 保留两位小数\n\n        // console.log(`车辆${bsmId}状态: 速度=${speed.toFixed(2)}km/h, 位置=(${lat.toFixed(7)}, ${lng.toFixed(7)}), 航向=${heading.toFixed(2)}°`);\n\n        // 更新车辆状态和位置信息\n        setVehicles(prevVehicles => {\n          // 检查是否找到对应车辆\n          const foundVehicle = prevVehicles.find(v => v.bsmId === bsmId);\n          if (!foundVehicle) {\n            console.log(`未在车辆列表中找到ID为${bsmId}的车辆，不更新状态`);\n            return prevVehicles;\n          }\n\n          const updatedVehicles = prevVehicles.map(vehicle =>\n            vehicle.bsmId === bsmId\n              ? {\n                  ...vehicle,\n                  status: 'online',\n                  speed: speed,\n                  lat: lat,\n                  lng: lng,\n                  heading: heading\n                }\n              : vehicle\n          );\n\n          // 计算更新后的在线车辆数量\n          const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n          const totalCount = updatedVehicles.length;\n\n          // 更新统计数据\n          setStats(prevStats => ({\n            ...prevStats,\n            onlineVehicles: onlineCount,\n            offlineVehicles: totalCount - onlineCount\n          }));\n\n          return updatedVehicles;\n        });\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleBsmMessage);\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('message', handleBsmMessage);\n    };\n  }, []);\n\n  // 修改检查在线状态的useEffect，同步更新统计信息\n  useEffect(() => {\n    const checkOnlineStatus = () => {\n      const now = Date.now();\n      console.log('检查车辆在线状态...');\n\n      // 只有在特定条件下才将车辆设为离线\n      // 例如，只有收到过BSM消息的车辆才会被检查是否超时\n      setOnlineBsmIds(prev => {\n        const newOnlineBsmIds = new Set(prev);\n        let hasChanges = false;\n\n        // 仅检查已有最后更新时间的车辆\n        prev.forEach(bsmId => {\n          const lastTime = lastBsmTime.current[bsmId];\n\n          // 只有收到过BSM消息的车辆才会被检查是否超时\n          if (lastTime && (now - lastTime > 30000)) {\n            console.log(`车辆${bsmId}超过30秒未更新，设置为离线状态`);\n            newOnlineBsmIds.delete(bsmId);\n            hasChanges = true;\n          }\n        });\n\n        if (hasChanges) {\n          // 更新车辆状态\n          setVehicles(prevVehicles => {\n            const updatedVehicles = prevVehicles.map(vehicle => {\n              // 只更新有最后更新时间的车辆状态\n              if (lastBsmTime.current[vehicle.bsmId]) {\n                const isOnline = newOnlineBsmIds.has(vehicle.bsmId);\n                return {\n                  ...vehicle,\n                  status: isOnline ? 'online' : 'offline'\n                };\n              }\n              return vehicle;\n            });\n\n            // 计算更新后的在线车辆数量\n            const onlineCount = updatedVehicles.filter(v => v.status === 'online').length;\n            const totalCount = updatedVehicles.length;\n\n            // 更新统计数据\n            setStats(prevStats => ({\n              ...prevStats,\n              onlineVehicles: onlineCount,\n              offlineVehicles: totalCount - onlineCount\n            }));\n\n            return updatedVehicles;\n          });\n        }\n\n        return newOnlineBsmIds;\n      });\n    };\n\n    const interval = setInterval(checkOnlineStatus, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  // 重置所有车辆的初始状态\n  useEffect(() => {\n    // 将所有车辆状态重置为离线\n    const resetAllVehicles = () => {\n      // 只有在没有任何BSM消息的情况下才执行重置\n      if (onlineBsmIds.size === 0) {\n        setVehicles(prevVehicles =>\n          prevVehicles.map(vehicle => ({\n            ...vehicle,\n            status: 'offline',\n            speed: 0,\n            lat: 0,\n            lng: 0,\n            heading: 0\n          }))\n        );\n\n        console.log('已重置所有车辆为离线状态');\n      }\n    };\n\n    // 初始执行一次\n    resetAllVehicles();\n\n    // 然后每30秒检查一次\n    const interval = setInterval(resetAllVehicles, 30000);\n\n    return () => clearInterval(interval);\n  }, [onlineBsmIds]);\n\n  // 在组件挂载时获取数据\n  useEffect(() => {\n    const loadData = async () => {\n      setLoading(true);\n      fetchVehicles();\n      await fetchDeviceStats();\n      setLoading(false);\n    };\n\n    loadData();\n    // // 降低更新频率，避免频繁覆盖状态\n    // const interval = setInterval(loadData, 300000); // 每60x5秒更新一次\n    // return () => clearInterval(interval);\n  }, []);\n\n  // 添加对车辆数据变更的监听\n  useEffect(() => {\n    console.log('设置车辆数据变更监听');\n\n    // 监听自定义事件\n    const handleVehiclesDataChanged = () => {\n      console.log('检测到车辆数据变更事件，重新获取车辆列表');\n      fetchVehicles();\n    };\n\n    // 监听localStorage变化\n    const handleStorageChange = (event) => {\n      if (event.key === 'vehiclesLastUpdated' || event.key === 'vehiclesData') {\n        console.log('检测到localStorage变化，重新获取车辆列表');\n        fetchVehicles();\n      }\n    };\n\n    // 添加事件监听器\n    window.addEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n    window.addEventListener('storage', handleStorageChange);\n\n    // 初始检查是否有更新\n    const lastUpdated = localStorage.getItem('vehiclesLastUpdated');\n    if (lastUpdated) {\n      console.log('初始检查到vehiclesLastUpdated:', lastUpdated);\n      fetchVehicles();\n    }\n\n    // 设置更频繁的强制轮询，确保在部署环境中也能获取最新数据\n    const forcedPollingInterval = setInterval(() => {\n      console.log('强制轮询：重新获取车辆列表');\n      fetchVehicles();\n    }, 10000); // 每10秒强制刷新一次\n\n    // 清理函数\n    return () => {\n      window.removeEventListener('vehiclesDataChanged', handleVehiclesDataChanged);\n      window.removeEventListener('storage', handleStorageChange);\n      clearInterval(forcedPollingInterval);\n    };\n  }, []);\n\n  // 添加检查本地缓存与API数据是否一致的机制\n  // 定义一个单独的API调用函数，用于获取最新的车辆列表数据\n  const fetchLatestVehiclesData = async (returnData = false) => {\n    try {\n      // 尝试多个可能的API地址\n      const possibleApiUrls = [\n        process.env.REACT_APP_API_URL || 'http://localhost:5000'\n        // 'http://localhost:5000',\n        // window.location.origin, // 当前站点的根URL\n        // `${window.location.origin}/api`, // 当前站点下的/api路径\n        // 'http://localhost:5000/api',\n        // 'http://127.0.0.1:5000',\n        // 'http://127.0.0.1:5000/api'\n      ];\n\n      console.log('尝试从多个API地址获取车辆数据');\n\n      // // 尝试从本地JSON文件直接获取\n      // try {\n      //   console.log('尝试从本地JSON文件获取数据');\n      //   const jsonResponse = await axios.get('/vehicles.json');\n      //   if (jsonResponse.data && jsonResponse.data.vehicles) {\n      //     console.log('成功从本地JSON文件获取数据:', jsonResponse.data.vehicles.length);\n      //     if (returnData) {\n      //       return jsonResponse.data.vehicles;\n      //     }\n      //     processVehiclesData(jsonResponse.data.vehicles);\n      //     return jsonResponse.data.vehicles;\n      //   }\n      // } catch (jsonError) {\n      //   console.log('从本地JSON获取失败:', jsonError.message);\n      // }\n\n      // 逐个尝试API地址\n      let succeeded = false;\n      let vehiclesData = null;\n\n      for (const apiUrl of possibleApiUrls) {\n        if (succeeded) break;\n\n        try {\n          console.log(`尝试从 ${apiUrl}/api/vehicles/list 获取数据`);\n          const response = await axios.get(`${apiUrl}/api/vehicles/list`);\n\n          if (response.data && response.data.vehicles) {\n            console.log(`成功从 ${apiUrl} 获取数据:`, response.data.vehicles.length);\n            vehiclesData = response.data.vehicles;\n\n            if (returnData) {\n              return vehiclesData;\n            }\n\n            processVehiclesData(response.data.vehicles);\n            succeeded = true;\n            break;\n          }\n        } catch (error) {\n          console.log(`从 ${apiUrl} 获取失败:`, error.message);\n          // 继续尝试下一个URL\n        }\n      }\n\n      if (!succeeded && !returnData) {\n        console.log('所有API地址都获取失败，尝试使用vehicles.json');\n        // 尝试从当前页面获取\n        try {\n          const response = await fetch('/vehicles.json');\n          if (response.ok) {\n            const data = await response.json();\n            if (data && data.vehicles) {\n              console.log('从public/vehicles.json获取数据成功:', data.vehicles.length);\n              processVehiclesData(data.vehicles);\n              return data.vehicles;\n            }\n          }\n        } catch (e) {\n          console.error('从public/vehicles.json获取失败:', e);\n        }\n      }\n\n      return vehiclesData || [];\n    } catch (error) {\n      console.error('获取车辆列表失败:', error);\n      return [];\n    }\n  };\n\n  // 添加处理车辆数据的辅助函数\n  const processVehiclesData = (newVehicles) => {\n    // 与当前列表比较\n    if (vehicles.length !== newVehicles.length) {\n      console.log('检测到车辆数量变化，从', vehicles.length, '到', newVehicles.length);\n      fetchVehicles(); // 重新加载\n      return;\n    }\n\n    // 检查是否有新车辆ID\n    const currentIds = new Set(vehicles.map(v => v.id));\n    const hasNewVehicle = newVehicles.some(v => !currentIds.has(v.id));\n\n    if (hasNewVehicle) {\n      console.log('检测到新增车辆');\n      fetchVehicles(); // 重新加载\n    }\n  };\n\n  // 添加自动选择第一个车辆的逻辑\n  useEffect(() => {\n    // 当车辆列表加载完成且有车辆数据时\n    if (vehicles.length > 0 && !selectedVehicle) {\n      // 自动选择第一个车辆\n      setSelectedVehicle(vehicles[0]);\n      console.log('已自动选择第一个车辆:', vehicles[0].plateNumber);\n    }\n  }, [vehicles, selectedVehicle]);\n\n  // 修改图表初始化代码\n  useEffect(() => {\n    let chart = null;\n    let handleResize = null;\n    let lastUpdateTime = new Date();\n\n    // 确保 DOM 元素存在\n    if (!eventChartRef.current) {\n      console.error('图表容器未找到');\n      return;\n    }\n\n    // 检查并清理已存在的图表实例\n    let existingChart = echarts.getInstanceByDom(eventChartRef.current);\n    if (existingChart) {\n      existingChart.dispose();\n    }\n\n    try {\n      // 初始化新的图表实例\n      chart = echarts.init(eventChartRef.current);\n\n      // 设置图表配置\n      const updateChart = () => {\n        const currentTime = new Date();\n        lastUpdateTime = currentTime;\n\n        // 事件类型配置\n        // const eventTypes = [\n        //   { type: '401', name: '道路抛洒物', color: '#ff4d4f' },\n        //   { type: '404', name: '道路障碍物', color: '#faad14' },\n        //   { type: '405', name: '行人通过马路', color: '#1890ff' },\n        //   { type: '904', name: '逆行车辆', color: '#f5222d' },\n        //   { type: '910', name: '违停车辆', color: '#722ed1' },\n        //   { type: '1002', name: '道路施工', color: '#fa8c16' },\n        //   { type: '901', name: '车辆超速', color: '#eb2f96' }\n        // ];\n        const eventTypes = [\n          { type: '401', name: '道路抛洒物', color: '#faad14' },\n          { type: '404', name: '道路障碍物', color: '#ff7a45' },\n          { type: '405', name: '行人通过马路', color: '#52c41a' },\n          { type: '904', name: '逆行车辆', color: '#f5222d' },\n          { type: '910', name: '违停车辆', color: '#ff4d4f' },\n          { type: '1002', name: '道路施工', color: '#1890ff' },\n          { type: '901', name: '车辆超速', color: '#fa8c16' }\n        ];\n\n        // 处理数据\n        const data = eventTypes\n          .map(event => ({\n            value: eventStats[event.type] || 0,\n            name: event.name,\n            itemStyle: { color: event.color }\n          }))\n          .filter(item => item.value > 0)\n          .sort((a, b) => b.value - a.value);\n\n        const option = {\n          title: {\n            text: `最后更新: ${currentTime.toLocaleTimeString()}`,\n            left: 'center',\n            top: -3,\n            textStyle: {\n              fontSize: 12,\n              color: '#999'\n            }\n          },\n          grid: {\n            top: 30,\n            bottom: 0,\n            left: 0,\n            right: 50,\n            containLabel: true\n          },\n          animation: true,\n          animationDuration: 0,\n          animationDurationUpdate: 1000,\n          animationEasingUpdate: 'quinticInOut',\n          tooltip: {\n            trigger: 'axis',\n            axisPointer: {\n              type: 'shadow'\n            }\n          },\n          xAxis: {\n            type: 'value',\n            show: false,\n            splitLine: { show: false }\n          },\n          yAxis: {\n            type: 'category',\n            data: data.map(item => item.name),\n            axisLabel: {\n              fontSize: 12,\n              color: '#666',\n              margin: 8\n            },\n            axisTick: { show: false },\n            axisLine: { show: false }\n          },\n          series: [{\n            type: 'bar',\n            data: data,\n            barWidth: '50%',\n            label: {\n              show: true,\n              position: 'right',\n              formatter: '{c}次',\n              fontSize: 12,\n              color: '#666'\n            },\n            itemStyle: {\n              borderRadius: [0, 4, 4, 0]\n            },\n            realtimeSort: false,\n            animationDelay: function (idx) {\n              return idx * 100;\n            }\n          }]\n        };\n\n        // 使用 notMerge: false 来保持增量更新\n        chart.setOption(option, {\n          notMerge: false,\n          replaceMerge: ['series']\n        });\n      };\n\n      // 初始更新\n      updateChart();\n\n      // 监听事件统计变化，每分钟更新一次\n      const statsInterval = setInterval(updateChart, 60000); // 60000ms = 1分钟\n\n      // 监听窗口大小变化\n      handleResize = () => {\n        chart?.resize();\n      };\n      window.addEventListener('resize', handleResize);\n\n      // 清理函数\n      return () => {\n        clearInterval(statsInterval);\n        if (handleResize) {\n          window.removeEventListener('resize', handleResize);\n        }\n        if (chart) {\n          chart.dispose();\n        }\n      };\n\n    } catch (error) {\n      console.error('初始化图表失败:', error);\n    }\n  }, [eventStats]);\n\n  // 修改 RSI 消息处理逻辑\n  useEffect(() => {\n    const handleRsiMessage = (event) => {\n      try {\n        if (event.data && event.data.type === 'RSI') {\n          const rsiData = event.data.data;\n          if (!rsiData || !rsiData.rtes) return;\n          // if(rsiData.rtes.length > 0){\n          //   // console.log('RealTimeTraffic 收到 RSI 消息:', event.data);\n          //   if(parseFloat(rsiData.posLong) < 113.0){\n          //     console.log('位置不在测试范围的 RSI 消息:', event.data);\n          //     console.log('收到RSI消息，但位置不在测试范围内，忽略');\n          //     return;\n          //   }\n          // }\n\n          const latitude = parseFloat(rsiData.posLat);\n          const longitude = parseFloat(rsiData.posLong);\n          const rsuId = rsiData.rsuId;\n          const mac = event.data.mac || '';\n          const timestamp = event.data.tm || Date.now();\n\n          // 统计本帧所有非重复事件类型\n          const nonDuplicateEventTypes = [];\n          rsiData.rtes.forEach(event => {\n            const eventType = event.eventType;\n\n            // 根据事件类型设置不同的位置精度和去重策略\n            let latFixed = '';\n            let lngFixed = '';\n            let eventKey = '';\n\n            if(eventType === '904' || eventType === '901'){\n              // 逆行和超速：使用3位小数精度（约111米范围）\n              latFixed = Math.floor(latitude * Math.pow(10,3))/Math.pow(10,3);\n              lngFixed = Math.floor(longitude* Math.pow(10,3))/Math.pow(10,3);\n              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;\n            }\n            else if(eventType === '910'){\n              // 违停车辆：使用2位小数精度（约1.1公里范围），忽略MAC地址\n              // 这样可以将相近位置的违停事件归为同一类\n              latFixed = Math.floor(latitude * Math.pow(10,4))/Math.pow(10,4);\n              lngFixed = Math.floor(longitude* Math.pow(10,4))/Math.pow(10,4);\n              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;\n            }\n            else{\n              // 其他事件：使用4位小数精度（约11米范围）\n              latFixed = Math.floor(latitude * Math.pow(10,4))/Math.pow(10,4);\n              lngFixed = Math.floor(longitude* Math.pow(10,4))/Math.pow(10,4);\n              eventKey = `${rsuId}_${mac}_${eventType}_${latFixed}_${lngFixed}`;\n            }\n\n            const duplicateResult = checkDuplicateEvent(\n              eventType,\n              eventKey,\n              timestamp,\n              { lat: latitude, lng: longitude }\n            );\n\n            const isDuplicate = duplicateResult.isDuplicate;\n            // 获取事件类型的中文描述\n            let eventTypeText = '';\n            let eventColor = '';\n            switch(eventType) {\n              case '401': eventTypeText = '道路抛洒物'; eventColor = '#faad14'; break;\n              case '404': eventTypeText = '道路障碍物'; eventColor = '#ff7a45'; break;\n              case '405': eventTypeText = '行人通过马路'; eventColor = '#52c41a'; break;\n              case '904': eventTypeText = '逆行车辆'; eventColor = '#f5222d'; break;\n              case '910': eventTypeText = '违停车辆'; eventColor = '#ff4d4f'; break;\n              case '1002': eventTypeText = '道路施工'; eventColor = '#1890ff'; break;\n              case '901': eventTypeText = '车辆超速'; eventColor = '#fa8c16'; break;\n              default: eventTypeText = event.description || '未知事件'; eventColor = '#8c8c8c';\n            }\n            // 更新事件列表\n            // const newEvent = {\n            //   key: Date.now() + Math.random(),\n            //   type: eventTypeText,\n            //   time: new Date().toLocaleTimeString(),\n            //   vehicle: rsiData.rsuId || '未知设备',\n            //   color: eventColor,\n            //   eventType: eventType,\n            //   location: {\n            //     latitude: latitude,\n            //     longitude: longitude\n            //   }\n\n            // };\n            // setEvents(prev => {\n            //   const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n            //   return newEvents;\n            // });\n            if (!isDuplicate) {\n              // 创建事件数据，使用新的事件ID\n              const eventData = {\n                eventId: duplicateResult.eventId,\n                eventType: eventType,\n                eventTypeText: eventTypeText,\n                rsuId: rsiData.rsuId || '未知设备',\n                mac: mac,\n                latitude: latitude,\n                longitude: longitude,\n                site: getNearestIntersectionName(latitude, longitude),\n                eventKey: eventKey,\n                color: eventColor,\n                timestamp: new Date().toISOString()\n              };\n\n              // 存储到数据库\n              storeEventToDatabase(eventData).then(success => {\n                if (success) {\n                  console.log(`✅ 事件已存储到数据库: ${eventTypeText}`);\n\n                  // 更新本地事件列表（用于实时显示）\n                  const newEvent = {\n                    key: Date.now() + Math.random(),\n                    type: eventTypeText,\n                    time: new Date().toLocaleTimeString(),\n                    rsuId: rsiData.rsuId || '未知设备',\n                    color: eventColor,\n                    eventType: eventType,\n                    location: {\n                      latitude: latitude,\n                      longitude: longitude\n                    },\n                    site: eventData.site\n                  };\n\n                  setEvents(prev => {\n                    const newEvents = [newEvent, ...prev].slice(0, 10); // 只保留最近10条记录\n                    return newEvents;\n                  });\n                } else {\n                  console.error(`❌ 事件存储失败: ${eventTypeText}`);\n                }\n              });\n\n              nonDuplicateEventTypes.push(eventType);\n            } else {\n              // console.log(`检测到重复事件，不累计统计: ${eventTypeText}`);\n            }\n          });\n\n          // 如果有新事件，从数据库刷新统计数据\n          if (nonDuplicateEventTypes.length > 0) {\n            console.log(`📊 检测到 ${nonDuplicateEventTypes.length} 个新事件，刷新统计数据`);\n\n            // 延迟1秒后从数据库获取最新统计，确保数据已存储\n            console.log('实际的时间段2', selectedTimeRange);\n            setTimeout(async () => {\n              const stats = await fetchEventStatsFromDatabase(selectedTimeRange);\n              if (stats) {\n                setEventStats(prevStats => ({\n                  ...prevStats,\n                  ...stats\n                }));\n                console.log('✅ 事件统计数据已从数据库更新，时间段:', selectedTimeRange);\n              }\n            }, 1000);\n          }\n        }\n      } catch (error) {\n        console.error('处理 RSI 消息失败:', error);\n      }\n    };\n\n    // 添加消息监听器\n    window.addEventListener('message', handleRsiMessage);\n\n    return () => {\n      window.removeEventListener('message', handleRsiMessage);\n    };\n  }, []);\n\n  // 获取事件类型的阈值配置\n  const getEventThresholds = (eventType) => {\n    switch(eventType) {\n      case '910': // 违停车辆\n        return { timeThreshold: 300000, distanceThreshold: 10 }; // 5分钟, 20米\n      case '904': // 逆行车辆\n        return { timeThreshold: 10000, distanceThreshold: 20 }; // 10秒, 20米\n      case '901': // 车辆超速\n        return { timeThreshold: 30000, distanceThreshold: 20 }; // 30秒, 50米\n      case '401': // 道路抛洒物\n      case '404': // 道路障碍物\n      case '1002': // 道路施工\n        return { timeThreshold: 600000, distanceThreshold: 5 }; // 10分钟, 30米\n      case '405': // 行人通过马路\n        return { timeThreshold: 10000, distanceThreshold: 10 }; // 10秒, 10米\n      default:\n        return { timeThreshold: 5000, distanceThreshold: 5 }; // 5秒, 5米\n    }\n  };\n\n  // 优化后的事件重复检查逻辑\n  const checkDuplicateEvent = (eventType, eventKey, currentTime, currentPos) => {\n    const { timeThreshold, distanceThreshold } = getEventThresholds(eventType);\n\n    // 遍历事件列表缓存中的所有事件\n    for (let i = 0; i < eventListCache.current.length; i++) {\n      const cachedEvent = eventListCache.current[i];\n\n      // 检查事件类型是否相同\n      if (cachedEvent.eventType !== eventType) {\n        continue;\n      }\n\n      // 计算时间差\n      const timeDiff = currentTime - cachedEvent.lastUpdateTime;\n\n      // 检查时间差是否在阈值内\n      if (timeDiff > timeThreshold) {\n        continue;\n      }\n\n      // 计算距离\n      const distance = calculateDistance(\n        currentPos.lat, currentPos.lng,\n        cachedEvent.position.lat, cachedEvent.position.lng\n      );\n\n      // 检查距离是否在阈值内\n      if (distance <= distanceThreshold) {\n        // 找到匹配的事件，更新信息\n        cachedEvent.eventKey = eventKey;\n        cachedEvent.lastUpdateTime = currentTime;\n        cachedEvent.position = { ...currentPos };\n        cachedEvent.updateCount = (cachedEvent.updateCount || 1) + 1;\n\n        // console.log(`🔄 检测到重复事件 ${eventType} (ID: ${cachedEvent.eventId})，时间差: ${(timeDiff/1000).toFixed(1)}s，距离: ${distance.toFixed(1)}m，更新次数: ${cachedEvent.updateCount}`);\n\n        return {\n          isDuplicate: true,\n          eventId: cachedEvent.eventId,\n          matchedEvent: cachedEvent\n        };\n      }\n    }\n\n    // 没有找到匹配的事件，创建新事件\n    const newEventId = `EVT_${eventIdCounter.current.toString().padStart(6, '0')}`;\n    eventIdCounter.current++;\n\n    const newEvent = {\n      eventId: newEventId,\n      eventType: eventType,\n      eventKey: eventKey,\n      firstDetectedTime: currentTime,\n      lastUpdateTime: currentTime,\n      position: { ...currentPos },\n      updateCount: 1\n    };\n\n    // 添加到事件列表缓存\n    eventListCache.current.push(newEvent);\n\n    console.log(`✨ 创建新事件 ${eventType} (ID: ${newEventId})，位置: (${currentPos.lat.toFixed(6)}, ${currentPos.lng.toFixed(6)})`);\n\n    return {\n      isDuplicate: false,\n      eventId: newEventId,\n      newEvent: newEvent\n    };\n  };\n\n  // 清理过期事件的函数\n  const cleanupExpiredEvents = () => {\n    const currentTime = Date.now();\n    const maxAge = 3600000; // 1小时\n\n    const initialCount = eventListCache.current.length;\n    eventListCache.current = eventListCache.current.filter(event => {\n      const age = currentTime - event.lastUpdateTime;\n      return age <= maxAge;\n    });\n\n    const removedCount = initialCount - eventListCache.current.length;\n    if (removedCount > 0) {\n      console.log(`🧹 清理了 ${removedCount} 个过期事件，当前缓存事件数: ${eventListCache.current.length}`);\n    }\n  };\n\n  // 删除1分钟内没有更新的事件\n  const removeInactiveEvents = () => {\n    const currentTime = Date.now();\n    const inactiveThreshold = 60000; // 1分钟\n\n    const initialCount = eventListCache.current.length;\n    const removedEvents = [];\n\n    eventListCache.current = eventListCache.current.filter(event => {\n      const timeSinceLastUpdate = currentTime - event.lastUpdateTime;\n      if (timeSinceLastUpdate > inactiveThreshold) {\n        removedEvents.push({\n          id: event.eventId,\n          type: event.eventType,\n          inactiveTime: (timeSinceLastUpdate / 1000).toFixed(1)\n        });\n        return false; // 删除该事件\n      }\n      return true; // 保留该事件\n    });\n\n    const removedCount = initialCount - eventListCache.current.length;\n    if (removedCount > 0) {\n      console.log(`🗑️ 删除了 ${removedCount} 个1分钟内未更新的事件:`);\n      removedEvents.forEach(event => {\n        console.log(`   - 事件 ${event.id} (类型: ${event.type})，未更新时间: ${event.inactiveTime}秒`);\n      });\n      console.log(`📊 当前缓存事件数: ${eventListCache.current.length}`);\n    }\n  };\n\n  // 添加计算两点之间距离的函数\n  const calculateDistance = (lat1, lon1, lat2, lon2) => {\n    if (lat1 === 0 || lon1 === 0 || lat2 === 0 || lon2 === 0) {\n      return 999;\n    }\n    const R = 6371000;\n    const dLat = (lat2 - lat1) * Math.PI / 180;\n    const dLon = (lon2 - lon1) * Math.PI / 180;\n    const a =\n      Math.sin(dLat/2) * Math.sin(dLat/2) +\n      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *\n      Math.sin(dLon/2) * Math.sin(dLon/2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n    return R * c;\n  };\n\n  // 根据坐标查找最近的路口名称\n  const getNearestIntersectionName = (lat, lng) => {\n    // 从 intersections.json 获取所有路口\n    const intersections = intersectionsData.intersections || [];\n    let minDistance = Infinity;\n    let nearestName = '未知地点';\n    intersections.forEach(inter => {\n      const interLat = parseFloat(inter.latitude);\n      const interLng = parseFloat(inter.longitude);\n      const dist = calculateDistance(lat, lng, interLat, interLng);\n      if (dist < minDistance) {\n        minDistance = dist;\n        nearestName = inter.name;\n      }\n    });\n    return nearestName;\n  };\n\n  // 处理车辆选择\n  const handleVehicleSelect = (vehicle) => {\n    console.log('选择车辆:', vehicle.plateNumber, '状态:', vehicle.status);\n    setSelectedVehicle(vehicle);\n  };\n\n  // 修改车辆状态更新逻辑，确保同步更新selectedVehicle\n  useEffect(() => {\n    // 当车辆列表更新后，如果当前有选中的车辆，则更新选中的车辆信息\n    if (selectedVehicle) {\n      const updatedSelectedVehicle = vehicles.find(v => v.id === selectedVehicle.id);\n      if (updatedSelectedVehicle &&\n          (updatedSelectedVehicle.status !== selectedVehicle.status ||\n           updatedSelectedVehicle.speed !== selectedVehicle.speed ||\n           updatedSelectedVehicle.lat !== selectedVehicle.lat ||\n           updatedSelectedVehicle.lng !== selectedVehicle.lng ||\n           updatedSelectedVehicle.heading !== selectedVehicle.heading)) {\n        console.log(`更新选中车辆 ${selectedVehicle.plateNumber} 的信息:`,\n                   `状态从 ${selectedVehicle.status} 变为 ${updatedSelectedVehicle.status}`);\n        setSelectedVehicle(updatedSelectedVehicle);\n      }\n    }\n  }, [vehicles, selectedVehicle]);\n\n  // 车辆列表列定义\n  const vehicleColumns = [\n    {\n      title: '车牌号',\n      dataIndex: 'plate',\n      key: 'plate',\n      width: '40%',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: '30%',\n      render: status => (\n        <Badge\n          status={status === 'online' ? 'success' : 'error'}\n          text={status === 'online' ? '在线' : '离线'}\n        />\n      ),\n    },\n    {\n      title: '速度',\n      dataIndex: 'speed',\n      key: 'speed',\n      width: '30%',\n      render: speed => `${typeof speed === 'number' ? speed.toFixed(0) : speed} km/h`,\n    }\n  ];\n\n  // 修改实时事件列表的渲染\n  const renderEventList = () => (\n    <List\n      size=\"small\"\n      dataSource={events}\n      renderItem={item => (\n        <List.Item style={{ padding: '8px 0' }}>\n          <List.Item.Meta\n            title={\n              <div style={{ fontSize: '13px', marginBottom: '4px' }}>\n                <span style={{ color: item.color, marginRight: '8px' }}>\n                  {item.type}\n                </span>\n                <span style={{ color: '#666', fontSize: '12px' }}>\n                  {item.time}\n                </span>\n              </div>\n            }\n            description={\n              <div style={{ fontSize: '12px', color: '#666' }}>\n                {/* 显示地点（site） */}\n                <div>地点: {item.site || '未知地点'}</div>\n                <div>位置: {item.location ?\n                  `${item.location.latitude.toFixed(6)}, ${item.location.longitude.toFixed(6)}` :\n                  '未知位置'}\n                </div>\n              </div>\n            }\n          />\n        </List.Item>\n      )}\n      style={{\n        maxHeight: 'calc(100% - 24px)',\n        overflowY: 'auto'\n      }}\n    />\n  );\n\n  // 添加强制定期从API获取最新数据的机制\n  useEffect(() => {\n    const apiPollingInterval = setInterval(() => {\n      console.log('直接从API检查车辆数据更新');\n      fetchLatestVehiclesData();\n    }, 15000); // 每15秒检查一次API\n\n    return () => clearInterval(apiPollingInterval);\n  }, [vehicles]);\n\n  return (\n    <Spin spinning={loading} tip=\"加载中...\">\n      <PageContainer>\n        {/* 左侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"left\"\n          collapsed={leftCollapsed}\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\n        >\n          {/* 车辆和设备总数信息栏 */}\n          <InfoCard title=\"车辆和设备统计\" bordered={false} height=\"160px\">\n            <Row gutter={[8, 1]} >\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"车辆总数\"\n                  value={stats.totalVehicles}\n                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }}\n                  // Style={{}}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"在线车辆\"\n                  value={stats.onlineVehicles}\n                  // suffix={`/ ${stats.totalVehicles}`}\n                  valueStyle={{ color: '#3f8600', display: 'flex',justifyContent: 'center'}}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"离线车辆\"\n                  value={stats.offlineVehicles}\n                  // suffix={`/ ${stats.totalVehicles}`}\n                  valueStyle={{ color: '#cf1322' ,display: 'flex',justifyContent: 'center' }}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"设备总数\"\n                  value={stats.totalDevices}\n                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"在线设备\"\n                  value={stats.onlineDevices}\n                  // suffix={`/ ${stats.totalDevices}`}\n                  valueStyle={{ color: '#3f8600',display: 'flex',justifyContent: 'center' }}\n                />\n              </Col>\n              <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>\n                <CompactStatistic\n                  title=\"离线设备\"\n                  value={stats.offlineDevices}\n                  // suffix={`/ ${stats.totalDevices}`}\n                  valueStyle={{ color: '#cf1322',display: 'flex',justifyContent: 'center' }}\n                />\n              </Col>\n            </Row>\n          </InfoCard>\n\n          {/* 实时事件列表栏 */}\n          <InfoCard\n            title=\"实时事件列表\"\n            bordered={false}\n            height=\"calc(50% - 95px)\"\n            // extra={\n            //   <div>\n            //     <Button\n            //       size=\"small\"\n            //       type=\"link\"\n            //       onClick={() => {\n            //         console.log('🧪 查看事件缓存状态');\n            //         console.log('📊 当前事件缓存状态:', {\n            //           缓存事件数: eventListCache.current.length,\n            //           事件ID计数器: eventIdCounter.current,\n            //           事件列表: eventListCache.current.map(e => {\n            //             const timeSinceUpdate = (Date.now() - e.lastUpdateTime) / 1000;\n            //             return {\n            //               ID: e.eventId,\n            //               类型: e.eventType,\n            //               更新次数: e.updateCount,\n            //               位置: `${e.position.lat.toFixed(6)}, ${e.position.lng.toFixed(6)}`,\n            //               未更新时间: `${timeSinceUpdate.toFixed(1)}秒`\n            //             };\n            //           })\n            //         });\n            //       }}\n            //     >\n            //       查看缓存\n            //     </Button>\n            //     <Button\n            //       size=\"small\"\n            //       type=\"link\"\n            //       onClick={() => {\n            //         console.log('🗑️ 手动删除非活跃事件');\n            //         removeInactiveEvents();\n            //       }}\n            //     >\n            //       删除非活跃\n            //     </Button>\n            //     <Button\n            //       size=\"small\"\n            //       type=\"link\"\n            //       onClick={() => {\n            //         console.log('🧹 手动清理过期事件');\n            //         cleanupExpiredEvents();\n            //       }}\n            //     >\n            //       清理过期\n            //     </Button>\n            //   </div>\n            // }\n          >\n            {renderEventList()}\n          </InfoCard>\n\n          {/* 实时事件统计栏 */}\n          <InfoCard\n            title=\"实时事件统计\"\n            bordered={false}\n            height=\"calc(50% - 95px)\"\n            extra={\n              <Select\n                value={selectedTimeRange}\n                onChange={handleTimeRangeChange}\n                onSelect={handleTimeRangeChange}\n                size=\"small\"\n                style={{ width: 80 }}\n                options={[\n                  { value: '1h', label: '1小时' },\n                  { value: '24h', label: '1天' },\n                  { value: '7d', label: '7天' },\n                  { value: 'all', label: '全部' }\n                ]}\n              />\n            }\n          >\n            <div ref={eventChartRef} style={{ height: '100%', width: '100%' }}></div>\n          </InfoCard>\n        </CollapsibleSidebar>\n\n        {/* 主内容区域 */}\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\n          {/* 主要内容 */}\n        </MainContent>\n\n        {/* 右侧信息栏 */}\n        <CollapsibleSidebar\n          position=\"right\"\n          collapsed={rightCollapsed}\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\n        >\n          {/* 车辆列表栏 */}\n          <InfoCard title=\"车辆列表\" bordered={false} height=\"50%\">\n            <Table\n              dataSource={vehicles}\n              columns={vehicleColumns}\n              rowKey=\"id\"\n              pagination={false}\n              size=\"small\"\n              scroll={{ y: 180 }}\n              onRow={(record) => ({\n                onClick: () => handleVehicleSelect(record),\n                style: {\n                  cursor: 'pointer',\n                  background: selectedVehicle?.id === record.id ? '#e6f7ff' : 'transparent',\n                  fontSize: '13px',\n                  padding: '4px 8px'\n                }\n              })}\n            />\n          </InfoCard>\n\n          {/* 车辆详细信息栏 */}\n          <InfoCard title=\"车辆详细信息\" bordered={false} height=\"50%\">\n            {selectedVehicle ? (\n              <Descriptions\n                bordered\n                column={1}\n                size=\"small\"\n                styles={{\n                  label: { fontSize: '13px', padding: '4px 8px' },\n                  content: { fontSize: '13px', padding: '4px 8px' }\n                }}\n              >\n                <Descriptions.Item label=\"车牌号\">{selectedVehicle.plateNumber}</Descriptions.Item>\n                <Descriptions.Item label=\"状态\">\n                  <Badge\n                    status={selectedVehicle.status === 'online' ? 'success' : 'error'}\n                    text={selectedVehicle.status === 'online' ? '在线' : '离线'}\n                  />\n                </Descriptions.Item>\n                <Descriptions.Item label=\"经度\">\n                  {selectedVehicle.status === 'online' ? selectedVehicle.lng.toFixed(7) : 'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"纬度\">\n                  {selectedVehicle.status === 'online' ? selectedVehicle.lat.toFixed(7) : 'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"速度\">\n                  {selectedVehicle.status === 'online' ?\n                    `${typeof selectedVehicle.speed === 'number' ? selectedVehicle.speed.toFixed(0) : selectedVehicle.speed} km/h` :\n                    'N/A'}\n                </Descriptions.Item>\n                <Descriptions.Item label=\"航向角\">\n                  {selectedVehicle.status === 'online' ?\n                    `${typeof selectedVehicle.heading === 'number' ? selectedVehicle.heading.toFixed(2) : selectedVehicle.heading}°` :\n                    'N/A'}\n                </Descriptions.Item>\n              </Descriptions>\n            ) : (\n              <p style={{ fontSize: '13px' }}>请选择车辆查看详细信息</p>\n            )}\n          </InfoCard>\n        </CollapsibleSidebar>\n      </PageContainer>\n    </Spin>\n  );\n};\n\nexport default RealTimeTraffic;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,KAAK,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,QAAQ,MAAM;AACxG,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,QAAQ,oBAAoB;AACrG,SAASC,cAAc,QAAQ,mBAAmB;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,yCAAyC;AACxE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,iBAAiB,MAAM,4BAA4B,CAAC,CAAC;;AAE5D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAb,OAAO,CAACc,GAAG,CAAC,CAACb,QAAQ,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,cAAc,CAAC,CAAC;AAEzG,MAAMS,YAAY,GAAGR,MAAM,CAACS,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMC,aAAa,GAAGV,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA,CAAC;;AAED;AAAAE,EAAA,GANMD,aAAa;AAOnB,MAAME,WAAW,GAAGZ,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMI,YAAY,GAAGb,MAAM,CAACS,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMK,WAAW,GAAGd,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA,aAAaM,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACxEF,KAAK,CAACC,aAAa,GAAG,cAAc,GACpCD,KAAK,CAACE,cAAc,GAAG,cAAc,GAAG,OAAO;AACnD;AACA,YAAYF,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACvEF,KAAK,CAACC,aAAa,GAAG,WAAW,GACjCD,KAAK,CAACE,cAAc,GAAG,WAAW,GAAG,GAAG;AAC5C;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfMJ,WAAW;AAgBjB,MAAMK,QAAQ,GAAGnB,MAAM,CAAChB,IAAI,CAAC;AAC7B;AACA,YAAY+B,KAAK,IAAIA,KAAK,CAACK,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GA1BMF,QAAQ;AA2Bd,MAAMG,gBAAgB,GAAGtB,MAAM,CAACf,SAAS,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsC,GAAA,GATID,gBAAgB;AAWtB,MAAME,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoD,MAAM,EAAEC,SAAS,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwD,KAAK,EAAEC,QAAQ,CAAC,GAAGzD,QAAQ,CAAC;IACjC0D,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,eAAe,EAAE,CAAC;IAClBC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAC,MAAM;IACrD,IAAI;MACF,MAAMkE,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC;MACvE;MACA,OAAO,IAAIC,GAAG,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,OAAO,IAAID,GAAG,CAAC,CAAC;IAClB;EACF,CAAC,CAAC;EACF;EACA,MAAMG,WAAW,GAAGtE,MAAM,CAAC,CAAC,CAAC,CAAC;EAE9B,MAAMuE,aAAa,GAAGvE,MAAM,CAAC,IAAI,CAAC;;EAElC;EACA,MAAM,CAACoC,aAAa,EAAEoC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuC,cAAc,EAAEoC,iBAAiB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC4E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EAEhE,MAAM,CAAC8E,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC;IAC3C,KAAK,EAAE,CAAC;IAAG;IACX,KAAK,EAAE,CAAC;IAAG;IACX,KAAK,EAAE,CAAC;IAAG;IACX,KAAK,EAAE,CAAC;IAAG;IACX,KAAK,EAAE,CAAC;IAAG;IACX,MAAM,EAAE,CAAC;IAAE;IACX,KAAK,EAAE,CAAC,CAAG;EACb,CAAC,CAAC;;EAEF;EACA,MAAMgF,aAAa,GAAG9E,MAAM,CAAC,IAAI+E,GAAG,CAAC,CAAC,CAAC;;EAEvC;EACA,MAAMC,cAAc,GAAGhF,MAAM,CAAC,EAAE,CAAC;;EAEjC;EACA,MAAMiF,cAAc,GAAGjF,MAAM,CAAC,CAAC,CAAC;;EAEhC;;EAEA;AACF;AACA;EACE,MAAMkF,oBAAoB,GAAG,MAAOC,SAAS,IAAK;IAChD,IAAI;MACF,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMlE,KAAK,CAACmE,IAAI,CAAC,GAAGL,MAAM,mBAAmB,EAAED,SAAS,CAAC;MAE1E,IAAIK,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1CtB,OAAO,CAACuB,GAAG,CAAC,WAAWJ,QAAQ,CAACE,IAAI,CAACG,OAAO,GAAG,EAAEV,SAAS,CAACW,aAAa,CAAC;QACzE,OAAO,IAAI;MACb,CAAC,MAAM;QACLzB,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEoB,QAAQ,CAACE,IAAI,CAAC;QACzC,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,OAAO,KAAK;IACd;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAM2B,2BAA2B,GAAG,MAAAA,CAAOC,SAAS,GAAG,IAAI,KAAK;IAC9D,IAAI;MACF;MACA3B,OAAO,CAACuB,GAAG,CAAC,SAAS,EAAEI,SAAS,CAAC;MACjC,MAAMZ,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMlE,KAAK,CAAC2E,GAAG,CAAC,GAAGb,MAAM,+BAA+BY,SAAS,EAAE,CAAC;MAErF,IAAIR,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1CtB,OAAO,CAACuB,GAAG,CAAC,OAAOJ,QAAQ,CAACE,IAAI,CAACG,OAAO,UAAUG,SAAS,IAAI,EAAER,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;QACpF,OAAOF,QAAQ,CAACE,IAAI,CAACA,IAAI;MAC3B,CAAC,MAAM;QACLrB,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEoB,QAAQ,CAACE,IAAI,CAAC;QAC3C,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,OAAO,IAAI;IACb;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAM8B,6BAA6B,GAAG,MAAAA,CAAOC,KAAK,GAAG,EAAE,KAAK;IAC1D,IAAI;MACF,MAAMf,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMlE,KAAK,CAAC2E,GAAG,CAAC,GAAGb,MAAM,4BAA4Be,KAAK,EAAE,CAAC;MAE9E,IAAIX,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1CtB,OAAO,CAACuB,GAAG,CAAC,OAAOJ,QAAQ,CAACE,IAAI,CAACG,OAAO,SAAS,EAAEL,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACU,MAAM,EAAE,GAAG,CAAC;QAClF,OAAOZ,QAAQ,CAACE,IAAI,CAACA,IAAI;MAC3B,CAAC,MAAM;QACLrB,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEoB,QAAQ,CAACE,IAAI,CAAC;QAC3C,OAAO,EAAE;MACX;IACF,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAMiC,mBAAmB,GAAGpG,WAAW,CAAC,CAACqG,KAAK,EAAEC,MAAM,EAAEC,KAAK,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,EAAEC,OAAO,GAAG,CAAC,KAAK;IACnG;IACA,MAAMC,cAAc,GAAGC,UAAU,CAACL,KAAK,CAAC,CAACM,OAAO,CAAC,CAAC,CAAC;IACnD,MAAMC,gBAAgB,GAAGF,UAAU,CAACF,OAAO,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC;IAEvDzC,OAAO,CAACuB,GAAG,CAAC,gBAAgBU,KAAK,QAAQC,MAAM,QAAQK,cAAc,SAASH,GAAG,KAAKC,GAAG,GAAG,CAAC;;IAE7F;IACA,IAAIH,MAAM,KAAK,QAAQ,EAAE;MACvBxC,eAAe,CAACiD,IAAI,IAAI,IAAI7C,GAAG,CAAC,CAAC,GAAG6C,IAAI,EAAEV,KAAK,CAAC,CAAC,CAAC;MAClDhC,WAAW,CAAC2C,OAAO,CAACX,KAAK,CAAC,GAAGY,IAAI,CAACC,GAAG,CAAC,CAAC;IACzC,CAAC,MAAM;MACLpD,eAAe,CAACiD,IAAI,IAAI;QACtB,MAAMI,MAAM,GAAG,IAAIjD,GAAG,CAAC6C,IAAI,CAAC;QAC5BI,MAAM,CAACC,MAAM,CAACf,KAAK,CAAC;QACpB,OAAOc,MAAM;MACf,CAAC,CAAC;IACJ;;IAEA;IACAnE,WAAW,CAACqE,YAAY,IACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,IACtBA,OAAO,CAAClB,KAAK,KAAKA,KAAK,GACnB;MACE,GAAGkB,OAAO;MACVjB,MAAM,EAAEA,MAAM;MACdC,KAAK,EAAEK,UAAU,CAACD,cAAc,CAAC;MAAE;MACnCH,GAAG,EAAEI,UAAU,CAACJ,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC;MAC/BJ,GAAG,EAAEG,UAAU,CAACH,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC;MAC/BH,OAAO,EAAEE,UAAU,CAACE,gBAAgB,CAAC,CAAC;IACxC,CAAC,GACDS,OACN,CACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzH,SAAS,CAAC,MAAM;IACd0H,MAAM,CAACpB,mBAAmB,GAAGA,mBAAmB;;IAEhD;IACA,MAAMqB,qBAAqB,GAAIC,KAAK,IAAK;MACvC,IAAIA,KAAK,CAACjC,IAAI,IAAIiC,KAAK,CAACjC,IAAI,CAACkC,IAAI,KAAK,iBAAiB,EAAE;QACvD;MAAA;IAEJ,CAAC;IAEDH,MAAM,CAACI,gBAAgB,CAAC,SAAS,EAAEH,qBAAqB,CAAC;;IAEzD;IACArD,OAAO,CAACuB,GAAG,CAAC,mBAAmB,CAAC;IAChC7B,eAAe,CAAC,IAAII,GAAG,CAAC,CAAC,CAAC;IAC1BG,WAAW,CAAC2C,OAAO,GAAG,CAAC,CAAC;IAExB,OAAO,MAAM;MACXQ,MAAM,CAACK,mBAAmB,CAAC,SAAS,EAAEJ,qBAAqB,CAAC;MAC5D,OAAOD,MAAM,CAACpB,mBAAmB;IACnC,CAAC;EACH,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;;EAEzB;EACA,MAAM0B,sBAAsB,GAAG,MAAAA,CAAO/B,SAAS,GAAGtB,iBAAiB,KAAK;IAEtE,IAAI;MACF;MACA,MAAMsD,YAAY,GAAG,MAAM9B,6BAA6B,CAAC,EAAE,CAAC;MAC5D,IAAI8B,YAAY,IAAIA,YAAY,CAAC5B,MAAM,GAAG,CAAC,EAAE;QAC3CjD,SAAS,CAAC6E,YAAY,CAAC;QACvB3D,OAAO,CAACuB,GAAG,CAAC,WAAW,EAAEoC,YAAY,CAAC5B,MAAM,EAAE,OAAO,CAAC;MACxD;;MAEA;MACA/B,OAAO,CAACuB,GAAG,CAAC,SAAS,EAAEI,SAAS,CAAC;MACjC,MAAM1C,KAAK,GAAG,MAAMyC,2BAA2B,CAACC,SAAS,CAAC;MAC1D,IAAI1C,KAAK,EAAE;QACTuB,aAAa,CAACoD,SAAS,KAAK;UAC1B,GAAGA,SAAS;UACZ,GAAG3E;QACL,CAAC,CAAC,CAAC;QACHe,OAAO,CAACuB,GAAG,CAAC,mBAAmBI,SAAS,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAM8D,qBAAqB,GAAIC,KAAK,IAAK;IACvC9D,OAAO,CAACuB,GAAG,CAAC,aAAa,EAAEuC,KAAK,CAAC;IACjCxD,oBAAoB,CAACwD,KAAK,CAAC;IAC3B;IACAJ,sBAAsB,CAACI,KAAK,CAAC;EAC/B,CAAC;;EAED;EACApI,SAAS,CAAC,MAAM;IACdgI,sBAAsB,CAACrD,iBAAiB,CAAC;EAC3C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3E,SAAS,CAAC,MAAM;IACdgI,sBAAsB,CAACrD,iBAAiB,CAAC;EAC3C,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;;EAEvB;EACA3E,SAAS,CAAC,MAAM;IACd,MAAMqI,eAAe,GAAGC,WAAW,CAAC,MAAM;MACxChE,OAAO,CAACuB,GAAG,CAAC,gBAAgB,EAAE,QAAQ,EAAElB,iBAAiB,CAAC;MAC1DqD,sBAAsB,CAACrD,iBAAiB,CAAC;IAC3C,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM4D,aAAa,CAACF,eAAe,CAAC;EAC7C,CAAC,EAAE,CAAC1D,iBAAiB,CAAC,CAAC,CAAC,CAAC;;EAEzB;EACA3E,SAAS,CAAC,MAAM;IACd,MAAMwI,eAAe,GAAGF,WAAW,CAAC,MAAM;MACxCG,oBAAoB,CAAC,CAAC;IACxB,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;;IAEZ,OAAO,MAAMF,aAAa,CAACC,eAAe,CAAC;EAC7C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxI,SAAS,CAAC,MAAM;IACd,MAAM0I,sBAAsB,GAAGJ,WAAW,CAAC,MAAM;MAC/CK,oBAAoB,CAAC,CAAC;IACxB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMJ,aAAa,CAACG,sBAAsB,CAAC;EACpD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,aAAa,GAAG1I,WAAW,CAAC,YAAY;IAC5C,IAAI;MACF;MACAoE,OAAO,CAACuB,GAAG,CAAC,gBAAgB,CAAC;MAC7B,MAAMgD,OAAO,GAAG,MAAMC,uBAAuB,CAAC,IAAI,CAAC;;MAEnD;MACA,IAAID,OAAO,IAAIA,OAAO,CAACxC,MAAM,GAAG,CAAC,EAAE;QACjC/B,OAAO,CAACuB,GAAG,CAAC,oBAAoB,EAAEgD,OAAO,CAACxC,MAAM,CAAC;;QAEjD;QACA,MAAM0C,MAAM,GAAGF,OAAO,CAACrB,GAAG,CAACwB,CAAC,IAAIA,CAAC,CAACzC,KAAK,CAAC,CAAC0C,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAC;QACzD5E,OAAO,CAACuB,GAAG,CAAC,cAAc,EAAEkD,MAAM,CAAC;;QAEnC;QACA,MAAMI,eAAe,GAAGN,OAAO,CAACrB,GAAG,CAACC,OAAO,IAAI;UAC7C;UACA,MAAM2B,QAAQ,GAAG3B,OAAO,CAAClB,KAAK,IAAIxC,YAAY,CAACsF,GAAG,CAAC5B,OAAO,CAAClB,KAAK,CAAC;UAEjE,OAAO;YACL,GAAGkB,OAAO;YACV6B,KAAK,EAAE7B,OAAO,CAAC8B,WAAW;YAAE;YAC5B/C,MAAM,EAAE4C,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAE;YACzC3C,KAAK,EAAE2C,QAAQ,GAAI7E,WAAW,CAAC2C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,GAAGkB,OAAO,CAAChB,KAAK,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC;YACnFC,GAAG,EAAE0C,QAAQ,GAAI7E,WAAW,CAAC2C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,GAAGkB,OAAO,CAACf,GAAG,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC;YAC/EC,GAAG,EAAEyC,QAAQ,GAAI7E,WAAW,CAAC2C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,GAAGkB,OAAO,CAACd,GAAG,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC;YAC/EC,OAAO,EAAEwC,QAAQ,GAAI7E,WAAW,CAAC2C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,GAAGkB,OAAO,CAACb,OAAO,IAAI,CAAC,GAAG,CAAC,GAAI;UACxF,CAAC;QACH,CAAC,CAAC;QAEF1D,WAAW,CAACiG,eAAe,CAAC;;QAE5B;QACA,MAAMK,WAAW,GAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,QAAQ,CAAC,CAACH,MAAM;QAC7E,MAAMoD,UAAU,GAAGN,eAAe,CAAC9C,MAAM;QAEzC/B,OAAO,CAACuB,GAAG,CAAC,cAAc4D,UAAU,QAAQD,WAAW,QAAQC,UAAU,GAAGD,WAAW,EAAE,CAAC;;QAE1F;QACAhG,QAAQ,CAAC0E,SAAS,KAAK;UACrB,GAAGA,SAAS;UACZzE,aAAa,EAAEgG,UAAU;UACzB/F,cAAc,EAAE8F,WAAW;UAC3B7F,eAAe,EAAE8F,UAAU,GAAGD;QAChC,CAAC,CAAC,CAAC;QAEH;MACF;;MAEA;MACAlF,OAAO,CAACuB,GAAG,CAAC,2BAA2B,CAAC;MACxC,MAAM6D,YAAY,GAAGlI,YAAY,CAACyB,QAAQ,IAAI,EAAE;MAChDqB,OAAO,CAACuB,GAAG,CAAC,2BAA2B,EAAE6D,YAAY,CAACrD,MAAM,CAAC;;MAE7D;MACA,MAAM8C,eAAe,GAAGO,YAAY,CAAClC,GAAG,CAACC,OAAO,IAAI;QAClD;QACA,MAAM2B,QAAQ,GAAG3B,OAAO,CAAClB,KAAK,IAAIxC,YAAY,CAACsF,GAAG,CAAC5B,OAAO,CAAClB,KAAK,CAAC;QAEjE,OAAO;UACL,GAAGkB,OAAO;UACV6B,KAAK,EAAE7B,OAAO,CAAC8B,WAAW;UAAE;UAC5B/C,MAAM,EAAE4C,QAAQ,GAAG,QAAQ,GAAG,SAAS;UAAE;UACzC3C,KAAK,EAAE2C,QAAQ,GAAI7E,WAAW,CAAC2C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,GAAGkB,OAAO,CAAChB,KAAK,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC;UACnFC,GAAG,EAAE0C,QAAQ,GAAI7E,WAAW,CAAC2C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,GAAGkB,OAAO,CAACf,GAAG,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC;UAC/EC,GAAG,EAAEyC,QAAQ,GAAI7E,WAAW,CAAC2C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,GAAGkB,OAAO,CAACd,GAAG,IAAI,CAAC,GAAG,CAAC,GAAI,CAAC;UAC/EC,OAAO,EAAEwC,QAAQ,GAAI7E,WAAW,CAAC2C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,GAAGkB,OAAO,CAACb,OAAO,IAAI,CAAC,GAAG,CAAC,GAAI;QACxF,CAAC;MACH,CAAC,CAAC;MAEF1D,WAAW,CAACiG,eAAe,CAAC;;MAE5B;MACA,MAAMK,WAAW,GAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,QAAQ,CAAC,CAACH,MAAM;MAC7E,MAAMoD,UAAU,GAAGN,eAAe,CAAC9C,MAAM;MAEzC/B,OAAO,CAACuB,GAAG,CAAC,cAAc4D,UAAU,QAAQD,WAAW,QAAQC,UAAU,GAAGD,WAAW,EAAE,CAAC;;MAE1F;MACAhG,QAAQ,CAAC0E,SAAS,KAAK;QACrB,GAAGA,SAAS;QACZzE,aAAa,EAAEgG,UAAU;QACzB/F,cAAc,EAAE8F,WAAW;QAC3B7F,eAAe,EAAE8F,UAAU,GAAGD;MAChC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOnF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC,EAAE,CAACN,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM4F,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMtE,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMlE,KAAK,CAAC2E,GAAG,CAAC,GAAGb,MAAM,cAAc,CAAC;MAEzD,IAAII,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1C,MAAMgE,WAAW,GAAGnE,QAAQ,CAACE,IAAI,CAACA,IAAI;;QAEtC;QACAnC,QAAQ,CAAC0E,SAAS,KAAK;UACrB,GAAGA,SAAS;UACZtE,YAAY,EAAEgG,WAAW,CAACvD,MAAM;UAChCxC,aAAa,EAAE+F,WAAW,CAACX,MAAM,CAACY,CAAC,IAAIA,CAAC,CAACrD,MAAM,KAAK,QAAQ,CAAC,CAACH,MAAM;UACpEvC,cAAc,EAAE8F,WAAW,CAACX,MAAM,CAACY,CAAC,IAAIA,CAAC,CAACrD,MAAM,KAAK,SAAS,CAAC,CAACH;QAClE,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACrC;EACF,CAAC;;EAED;EACArE,SAAS,CAAC,MAAM;IACd,MAAM8J,gBAAgB,GAAIlC,KAAK,IAAK;MAClC,IAAIA,KAAK,CAACjC,IAAI,IAAIiC,KAAK,CAACjC,IAAI,CAACkC,IAAI,KAAK,KAAK,EAAE;QAC3C;QACA,MAAMkC,OAAO,GAAGnC,KAAK,CAACjC,IAAI,CAACA,IAAI,IAAI,CAAC,CAAC;QACrC,MAAMY,KAAK,GAAGwD,OAAO,CAACxD,KAAK,IAAIqB,KAAK,CAACjC,IAAI,CAACY,KAAK;QAE/C,IAAI,CAACA,KAAK,EAAE;UACVjC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEuD,KAAK,CAACjC,IAAI,CAAC;UAC1C;QACF;;QAEA;QACA,MAAMyB,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;;QAEtB;QACA7C,WAAW,CAAC2C,OAAO,CAACX,KAAK,CAAC,GAAGa,GAAG;;QAEhC;QACApD,eAAe,CAACiD,IAAI,IAAI,IAAI7C,GAAG,CAAC,CAAC,GAAG6C,IAAI,EAAEV,KAAK,CAAC,CAAC,CAAC;;QAElD;QACA,MAAME,KAAK,GAAGK,UAAU,CAAC,CAACA,UAAU,CAACiD,OAAO,CAACC,SAAS,IAAIpC,KAAK,CAACjC,IAAI,CAACc,KAAK,IAAI,CAAC,CAAC,GAAG,GAAG,EAAEM,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrG,MAAML,GAAG,GAAGI,UAAU,CAACA,UAAU,CAACiD,OAAO,CAACE,OAAO,IAAIrC,KAAK,CAACjC,IAAI,CAACe,GAAG,IAAI,CAAC,CAAC,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC;QACrF,MAAMJ,GAAG,GAAGG,UAAU,CAACA,UAAU,CAACiD,OAAO,CAACG,QAAQ,IAAItC,KAAK,CAACjC,IAAI,CAACgB,GAAG,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC;QACtF,MAAMH,OAAO,GAAGE,UAAU,CAACA,UAAU,CAACiD,OAAO,CAACI,WAAW,IAAIvC,KAAK,CAACjC,IAAI,CAACiB,OAAO,IAAI,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnG;;QAEA;QACA7D,WAAW,CAACqE,YAAY,IAAI;UAC1B;UACA,MAAM6C,YAAY,GAAG7C,YAAY,CAAC8C,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAACzC,KAAK,KAAKA,KAAK,CAAC;UAC9D,IAAI,CAAC6D,YAAY,EAAE;YACjB9F,OAAO,CAACuB,GAAG,CAAC,eAAeU,KAAK,WAAW,CAAC;YAC5C,OAAOgB,YAAY;UACrB;UAEA,MAAM4B,eAAe,GAAG5B,YAAY,CAACC,GAAG,CAACC,OAAO,IAC9CA,OAAO,CAAClB,KAAK,KAAKA,KAAK,GACnB;YACE,GAAGkB,OAAO;YACVjB,MAAM,EAAE,QAAQ;YAChBC,KAAK,EAAEA,KAAK;YACZC,GAAG,EAAEA,GAAG;YACRC,GAAG,EAAEA,GAAG;YACRC,OAAO,EAAEA;UACX,CAAC,GACDa,OACN,CAAC;;UAED;UACA,MAAM+B,WAAW,GAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,QAAQ,CAAC,CAACH,MAAM;UAC7E,MAAMoD,UAAU,GAAGN,eAAe,CAAC9C,MAAM;;UAEzC;UACA7C,QAAQ,CAAC0E,SAAS,KAAK;YACrB,GAAGA,SAAS;YACZxE,cAAc,EAAE8F,WAAW;YAC3B7F,eAAe,EAAE8F,UAAU,GAAGD;UAChC,CAAC,CAAC,CAAC;UAEH,OAAOL,eAAe;QACxB,CAAC,CAAC;MACJ;IACF,CAAC;;IAED;IACAzB,MAAM,CAACI,gBAAgB,CAAC,SAAS,EAAEgC,gBAAgB,CAAC;;IAEpD;IACA,OAAO,MAAM;MACXpC,MAAM,CAACK,mBAAmB,CAAC,SAAS,EAAE+B,gBAAgB,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9J,SAAS,CAAC,MAAM;IACd,MAAMsK,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,MAAMlD,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACtB9C,OAAO,CAACuB,GAAG,CAAC,aAAa,CAAC;;MAE1B;MACA;MACA7B,eAAe,CAACiD,IAAI,IAAI;QACtB,MAAMsD,eAAe,GAAG,IAAInG,GAAG,CAAC6C,IAAI,CAAC;QACrC,IAAIuD,UAAU,GAAG,KAAK;;QAEtB;QACAvD,IAAI,CAACwD,OAAO,CAAClE,KAAK,IAAI;UACpB,MAAMmE,QAAQ,GAAGnG,WAAW,CAAC2C,OAAO,CAACX,KAAK,CAAC;;UAE3C;UACA,IAAImE,QAAQ,IAAKtD,GAAG,GAAGsD,QAAQ,GAAG,KAAM,EAAE;YACxCpG,OAAO,CAACuB,GAAG,CAAC,KAAKU,KAAK,kBAAkB,CAAC;YACzCgE,eAAe,CAACjD,MAAM,CAACf,KAAK,CAAC;YAC7BiE,UAAU,GAAG,IAAI;UACnB;QACF,CAAC,CAAC;QAEF,IAAIA,UAAU,EAAE;UACd;UACAtH,WAAW,CAACqE,YAAY,IAAI;YAC1B,MAAM4B,eAAe,GAAG5B,YAAY,CAACC,GAAG,CAACC,OAAO,IAAI;cAClD;cACA,IAAIlD,WAAW,CAAC2C,OAAO,CAACO,OAAO,CAAClB,KAAK,CAAC,EAAE;gBACtC,MAAM6C,QAAQ,GAAGmB,eAAe,CAAClB,GAAG,CAAC5B,OAAO,CAAClB,KAAK,CAAC;gBACnD,OAAO;kBACL,GAAGkB,OAAO;kBACVjB,MAAM,EAAE4C,QAAQ,GAAG,QAAQ,GAAG;gBAChC,CAAC;cACH;cACA,OAAO3B,OAAO;YAChB,CAAC,CAAC;;YAEF;YACA,MAAM+B,WAAW,GAAGL,eAAe,CAACF,MAAM,CAACD,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,QAAQ,CAAC,CAACH,MAAM;YAC7E,MAAMoD,UAAU,GAAGN,eAAe,CAAC9C,MAAM;;YAEzC;YACA7C,QAAQ,CAAC0E,SAAS,KAAK;cACrB,GAAGA,SAAS;cACZxE,cAAc,EAAE8F,WAAW;cAC3B7F,eAAe,EAAE8F,UAAU,GAAGD;YAChC,CAAC,CAAC,CAAC;YAEH,OAAOL,eAAe;UACxB,CAAC,CAAC;QACJ;QAEA,OAAOoB,eAAe;MACxB,CAAC,CAAC;IACJ,CAAC;IAED,MAAMI,QAAQ,GAAGrC,WAAW,CAACgC,iBAAiB,EAAE,IAAI,CAAC;IACrD,OAAO,MAAM/B,aAAa,CAACoC,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3K,SAAS,CAAC,MAAM;IACd;IACA,MAAM4K,gBAAgB,GAAGA,CAAA,KAAM;MAC7B;MACA,IAAI7G,YAAY,CAAC8G,IAAI,KAAK,CAAC,EAAE;QAC3B3H,WAAW,CAACqE,YAAY,IACtBA,YAAY,CAACC,GAAG,CAACC,OAAO,KAAK;UAC3B,GAAGA,OAAO;UACVjB,MAAM,EAAE,SAAS;UACjBC,KAAK,EAAE,CAAC;UACRC,GAAG,EAAE,CAAC;UACNC,GAAG,EAAE,CAAC;UACNC,OAAO,EAAE;QACX,CAAC,CAAC,CACJ,CAAC;QAEDtC,OAAO,CAACuB,GAAG,CAAC,cAAc,CAAC;MAC7B;IACF,CAAC;;IAED;IACA+E,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMD,QAAQ,GAAGrC,WAAW,CAACsC,gBAAgB,EAAE,KAAK,CAAC;IAErD,OAAO,MAAMrC,aAAa,CAACoC,QAAQ,CAAC;EACtC,CAAC,EAAE,CAAC5G,YAAY,CAAC,CAAC;;EAElB;EACA/D,SAAS,CAAC,MAAM;IACd,MAAM8K,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B9H,UAAU,CAAC,IAAI,CAAC;MAChB4F,aAAa,CAAC,CAAC;MACf,MAAMe,gBAAgB,CAAC,CAAC;MACxB3G,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAED8H,QAAQ,CAAC,CAAC;IACV;IACA;IACA;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9K,SAAS,CAAC,MAAM;IACdsE,OAAO,CAACuB,GAAG,CAAC,YAAY,CAAC;;IAEzB;IACA,MAAMkF,yBAAyB,GAAGA,CAAA,KAAM;MACtCzG,OAAO,CAACuB,GAAG,CAAC,sBAAsB,CAAC;MACnC+C,aAAa,CAAC,CAAC;IACjB,CAAC;;IAED;IACA,MAAMoC,mBAAmB,GAAIpD,KAAK,IAAK;MACrC,IAAIA,KAAK,CAACqD,GAAG,KAAK,qBAAqB,IAAIrD,KAAK,CAACqD,GAAG,KAAK,cAAc,EAAE;QACvE3G,OAAO,CAACuB,GAAG,CAAC,4BAA4B,CAAC;QACzC+C,aAAa,CAAC,CAAC;MACjB;IACF,CAAC;;IAED;IACAlB,MAAM,CAACI,gBAAgB,CAAC,qBAAqB,EAAEiD,yBAAyB,CAAC;IACzErD,MAAM,CAACI,gBAAgB,CAAC,SAAS,EAAEkD,mBAAmB,CAAC;;IAEvD;IACA,MAAME,WAAW,GAAGhH,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC;IAC/D,IAAI+G,WAAW,EAAE;MACf5G,OAAO,CAACuB,GAAG,CAAC,2BAA2B,EAAEqF,WAAW,CAAC;MACrDtC,aAAa,CAAC,CAAC;IACjB;;IAEA;IACA,MAAMuC,qBAAqB,GAAG7C,WAAW,CAAC,MAAM;MAC9ChE,OAAO,CAACuB,GAAG,CAAC,eAAe,CAAC;MAC5B+C,aAAa,CAAC,CAAC;IACjB,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACXlB,MAAM,CAACK,mBAAmB,CAAC,qBAAqB,EAAEgD,yBAAyB,CAAC;MAC5ErD,MAAM,CAACK,mBAAmB,CAAC,SAAS,EAAEiD,mBAAmB,CAAC;MAC1DzC,aAAa,CAAC4C,qBAAqB,CAAC;IACtC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA,MAAMrC,uBAAuB,GAAG,MAAAA,CAAOsC,UAAU,GAAG,KAAK,KAAK;IAC5D,IAAI;MACF;MACA,MAAMC,eAAe,GAAG,CACtB/F,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI;MACjC;MACA;MACA;MACA;MACA;MACA;MAAA,CACD;MAEDlB,OAAO,CAACuB,GAAG,CAAC,kBAAkB,CAAC;;MAE/B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,IAAIyF,SAAS,GAAG,KAAK;MACrB,IAAI9J,YAAY,GAAG,IAAI;MAEvB,KAAK,MAAM6D,MAAM,IAAIgG,eAAe,EAAE;QACpC,IAAIC,SAAS,EAAE;QAEf,IAAI;UACFhH,OAAO,CAACuB,GAAG,CAAC,OAAOR,MAAM,yBAAyB,CAAC;UACnD,MAAMI,QAAQ,GAAG,MAAMlE,KAAK,CAAC2E,GAAG,CAAC,GAAGb,MAAM,oBAAoB,CAAC;UAE/D,IAAII,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAAC1C,QAAQ,EAAE;YAC3CqB,OAAO,CAACuB,GAAG,CAAC,OAAOR,MAAM,QAAQ,EAAEI,QAAQ,CAACE,IAAI,CAAC1C,QAAQ,CAACoD,MAAM,CAAC;YACjE7E,YAAY,GAAGiE,QAAQ,CAACE,IAAI,CAAC1C,QAAQ;YAErC,IAAImI,UAAU,EAAE;cACd,OAAO5J,YAAY;YACrB;YAEA+J,mBAAmB,CAAC9F,QAAQ,CAACE,IAAI,CAAC1C,QAAQ,CAAC;YAC3CqI,SAAS,GAAG,IAAI;YAChB;UACF;QACF,CAAC,CAAC,OAAOjH,KAAK,EAAE;UACdC,OAAO,CAACuB,GAAG,CAAC,KAAKR,MAAM,QAAQ,EAAEhB,KAAK,CAACmH,OAAO,CAAC;UAC/C;QACF;MACF;MAEA,IAAI,CAACF,SAAS,IAAI,CAACF,UAAU,EAAE;QAC7B9G,OAAO,CAACuB,GAAG,CAAC,gCAAgC,CAAC;QAC7C;QACA,IAAI;UACF,MAAMJ,QAAQ,GAAG,MAAMgG,KAAK,CAAC,gBAAgB,CAAC;UAC9C,IAAIhG,QAAQ,CAACiG,EAAE,EAAE;YACf,MAAM/F,IAAI,GAAG,MAAMF,QAAQ,CAACkG,IAAI,CAAC,CAAC;YAClC,IAAIhG,IAAI,IAAIA,IAAI,CAAC1C,QAAQ,EAAE;cACzBqB,OAAO,CAACuB,GAAG,CAAC,8BAA8B,EAAEF,IAAI,CAAC1C,QAAQ,CAACoD,MAAM,CAAC;cACjEkF,mBAAmB,CAAC5F,IAAI,CAAC1C,QAAQ,CAAC;cAClC,OAAO0C,IAAI,CAAC1C,QAAQ;YACtB;UACF;QACF,CAAC,CAAC,OAAO2I,CAAC,EAAE;UACVtH,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEuH,CAAC,CAAC;QAChD;MACF;MAEA,OAAOpK,YAAY,IAAI,EAAE;IAC3B,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAMkH,mBAAmB,GAAIM,WAAW,IAAK;IAC3C;IACA,IAAI5I,QAAQ,CAACoD,MAAM,KAAKwF,WAAW,CAACxF,MAAM,EAAE;MAC1C/B,OAAO,CAACuB,GAAG,CAAC,aAAa,EAAE5C,QAAQ,CAACoD,MAAM,EAAE,GAAG,EAAEwF,WAAW,CAACxF,MAAM,CAAC;MACpEuC,aAAa,CAAC,CAAC,CAAC,CAAC;MACjB;IACF;;IAEA;IACA,MAAMkD,UAAU,GAAG,IAAI1H,GAAG,CAACnB,QAAQ,CAACuE,GAAG,CAACwB,CAAC,IAAIA,CAAC,CAACE,EAAE,CAAC,CAAC;IACnD,MAAM6C,aAAa,GAAGF,WAAW,CAACG,IAAI,CAAChD,CAAC,IAAI,CAAC8C,UAAU,CAACzC,GAAG,CAACL,CAAC,CAACE,EAAE,CAAC,CAAC;IAElE,IAAI6C,aAAa,EAAE;MACjBzH,OAAO,CAACuB,GAAG,CAAC,SAAS,CAAC;MACtB+C,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA5I,SAAS,CAAC,MAAM;IACd;IACA,IAAIiD,QAAQ,CAACoD,MAAM,GAAG,CAAC,IAAI,CAAChD,eAAe,EAAE;MAC3C;MACAC,kBAAkB,CAACL,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC/BqB,OAAO,CAACuB,GAAG,CAAC,aAAa,EAAE5C,QAAQ,CAAC,CAAC,CAAC,CAACsG,WAAW,CAAC;IACrD;EACF,CAAC,EAAE,CAACtG,QAAQ,EAAEI,eAAe,CAAC,CAAC;;EAE/B;EACArD,SAAS,CAAC,MAAM;IACd,IAAIiM,KAAK,GAAG,IAAI;IAChB,IAAIC,YAAY,GAAG,IAAI;IACvB,IAAIC,cAAc,GAAG,IAAIhF,IAAI,CAAC,CAAC;;IAE/B;IACA,IAAI,CAAC3C,aAAa,CAAC0C,OAAO,EAAE;MAC1B5C,OAAO,CAACD,KAAK,CAAC,SAAS,CAAC;MACxB;IACF;;IAEA;IACA,IAAI+H,aAAa,GAAGtL,OAAO,CAACuL,gBAAgB,CAAC7H,aAAa,CAAC0C,OAAO,CAAC;IACnE,IAAIkF,aAAa,EAAE;MACjBA,aAAa,CAACE,OAAO,CAAC,CAAC;IACzB;IAEA,IAAI;MACF;MACAL,KAAK,GAAGnL,OAAO,CAACyL,IAAI,CAAC/H,aAAa,CAAC0C,OAAO,CAAC;;MAE3C;MACA,MAAMsF,WAAW,GAAGA,CAAA,KAAM;QACxB,MAAMC,WAAW,GAAG,IAAItF,IAAI,CAAC,CAAC;QAC9BgF,cAAc,GAAGM,WAAW;;QAE5B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAMC,UAAU,GAAG,CACjB;UAAE7E,IAAI,EAAE,KAAK;UAAE8E,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EAChD;UAAE/E,IAAI,EAAE,KAAK;UAAE8E,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EAChD;UAAE/E,IAAI,EAAE,KAAK;UAAE8E,IAAI,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAU,CAAC,EACjD;UAAE/E,IAAI,EAAE,KAAK;UAAE8E,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC/C;UAAE/E,IAAI,EAAE,KAAK;UAAE8E,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC/C;UAAE/E,IAAI,EAAE,MAAM;UAAE8E,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC,EAChD;UAAE/E,IAAI,EAAE,KAAK;UAAE8E,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAC,CAChD;;QAED;QACA,MAAMjH,IAAI,GAAG+G,UAAU,CACpBlF,GAAG,CAACI,KAAK,KAAK;UACbQ,KAAK,EAAEvD,UAAU,CAAC+C,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;UAClC8E,IAAI,EAAE/E,KAAK,CAAC+E,IAAI;UAChBE,SAAS,EAAE;YAAED,KAAK,EAAEhF,KAAK,CAACgF;UAAM;QAClC,CAAC,CAAC,CAAC,CACF3D,MAAM,CAAC6D,IAAI,IAAIA,IAAI,CAAC1E,KAAK,GAAG,CAAC,CAAC,CAC9B2E,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC7E,KAAK,GAAG4E,CAAC,CAAC5E,KAAK,CAAC;QAEpC,MAAM8E,MAAM,GAAG;UACbC,KAAK,EAAE;YACLC,IAAI,EAAE,SAASX,WAAW,CAACY,kBAAkB,CAAC,CAAC,EAAE;YACjDC,IAAI,EAAE,QAAQ;YACdC,GAAG,EAAE,CAAC,CAAC;YACPC,SAAS,EAAE;cACTC,QAAQ,EAAE,EAAE;cACZb,KAAK,EAAE;YACT;UACF,CAAC;UACDc,IAAI,EAAE;YACJH,GAAG,EAAE,EAAE;YACPI,MAAM,EAAE,CAAC;YACTL,IAAI,EAAE,CAAC;YACPM,KAAK,EAAE,EAAE;YACTC,YAAY,EAAE;UAChB,CAAC;UACDC,SAAS,EAAE,IAAI;UACfC,iBAAiB,EAAE,CAAC;UACpBC,uBAAuB,EAAE,IAAI;UAC7BC,qBAAqB,EAAE,cAAc;UACrCC,OAAO,EAAE;YACPC,OAAO,EAAE,MAAM;YACfC,WAAW,EAAE;cACXvG,IAAI,EAAE;YACR;UACF,CAAC;UACDwG,KAAK,EAAE;YACLxG,IAAI,EAAE,OAAO;YACbyG,IAAI,EAAE,KAAK;YACXC,SAAS,EAAE;cAAED,IAAI,EAAE;YAAM;UAC3B,CAAC;UACDE,KAAK,EAAE;YACL3G,IAAI,EAAE,UAAU;YAChBlC,IAAI,EAAEA,IAAI,CAAC6B,GAAG,CAACsF,IAAI,IAAIA,IAAI,CAACH,IAAI,CAAC;YACjC8B,SAAS,EAAE;cACThB,QAAQ,EAAE,EAAE;cACZb,KAAK,EAAE,MAAM;cACb8B,MAAM,EAAE;YACV,CAAC;YACDC,QAAQ,EAAE;cAAEL,IAAI,EAAE;YAAM,CAAC;YACzBM,QAAQ,EAAE;cAAEN,IAAI,EAAE;YAAM;UAC1B,CAAC;UACDO,MAAM,EAAE,CAAC;YACPhH,IAAI,EAAE,KAAK;YACXlC,IAAI,EAAEA,IAAI;YACVmJ,QAAQ,EAAE,KAAK;YACfC,KAAK,EAAE;cACLT,IAAI,EAAE,IAAI;cACVU,QAAQ,EAAE,OAAO;cACjBC,SAAS,EAAE,MAAM;cACjBxB,QAAQ,EAAE,EAAE;cACZb,KAAK,EAAE;YACT,CAAC;YACDC,SAAS,EAAE;cACTqC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YAC3B,CAAC;YACDC,YAAY,EAAE,KAAK;YACnBC,cAAc,EAAE,SAAAA,CAAUC,GAAG,EAAE;cAC7B,OAAOA,GAAG,GAAG,GAAG;YAClB;UACF,CAAC;QACH,CAAC;;QAED;QACApD,KAAK,CAACqD,SAAS,CAACpC,MAAM,EAAE;UACtBqC,QAAQ,EAAE,KAAK;UACfC,YAAY,EAAE,CAAC,QAAQ;QACzB,CAAC,CAAC;MACJ,CAAC;;MAED;MACAhD,WAAW,CAAC,CAAC;;MAEb;MACA,MAAMiD,aAAa,GAAGnH,WAAW,CAACkE,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;;MAEvD;MACAN,YAAY,GAAGA,CAAA,KAAM;QAAA,IAAAwD,MAAA;QACnB,CAAAA,MAAA,GAAAzD,KAAK,cAAAyD,MAAA,uBAALA,MAAA,CAAOC,MAAM,CAAC,CAAC;MACjB,CAAC;MACDjI,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEoE,YAAY,CAAC;;MAE/C;MACA,OAAO,MAAM;QACX3D,aAAa,CAACkH,aAAa,CAAC;QAC5B,IAAIvD,YAAY,EAAE;UAChBxE,MAAM,CAACK,mBAAmB,CAAC,QAAQ,EAAEmE,YAAY,CAAC;QACpD;QACA,IAAID,KAAK,EAAE;UACTA,KAAK,CAACK,OAAO,CAAC,CAAC;QACjB;MACF,CAAC;IAEH,CAAC,CAAC,OAAOjI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACQ,UAAU,CAAC,CAAC;;EAEhB;EACA7E,SAAS,CAAC,MAAM;IACd,MAAM4P,gBAAgB,GAAIhI,KAAK,IAAK;MAClC,IAAI;QACF,IAAIA,KAAK,CAACjC,IAAI,IAAIiC,KAAK,CAACjC,IAAI,CAACkC,IAAI,KAAK,KAAK,EAAE;UAC3C,MAAMgI,OAAO,GAAGjI,KAAK,CAACjC,IAAI,CAACA,IAAI;UAC/B,IAAI,CAACkK,OAAO,IAAI,CAACA,OAAO,CAACC,IAAI,EAAE;UAC/B;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA,MAAMC,QAAQ,GAAGjJ,UAAU,CAAC+I,OAAO,CAACG,MAAM,CAAC;UAC3C,MAAMC,SAAS,GAAGnJ,UAAU,CAAC+I,OAAO,CAACK,OAAO,CAAC;UAC7C,MAAMC,KAAK,GAAGN,OAAO,CAACM,KAAK;UAC3B,MAAMC,GAAG,GAAGxI,KAAK,CAACjC,IAAI,CAACyK,GAAG,IAAI,EAAE;UAChC,MAAMC,SAAS,GAAGzI,KAAK,CAACjC,IAAI,CAAC2K,EAAE,IAAInJ,IAAI,CAACC,GAAG,CAAC,CAAC;;UAE7C;UACA,MAAMmJ,sBAAsB,GAAG,EAAE;UACjCV,OAAO,CAACC,IAAI,CAACrF,OAAO,CAAC7C,KAAK,IAAI;YAC5B,MAAM4I,SAAS,GAAG5I,KAAK,CAAC4I,SAAS;;YAEjC;YACA,IAAIC,QAAQ,GAAG,EAAE;YACjB,IAAIC,QAAQ,GAAG,EAAE;YACjB,IAAIC,QAAQ,GAAG,EAAE;YAEjB,IAAGH,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,KAAK,EAAC;cAC5C;cACAC,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACd,QAAQ,GAAGa,IAAI,CAACE,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC,CAAC,GAACF,IAAI,CAACE,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC;cAC/DJ,QAAQ,GAAGE,IAAI,CAACC,KAAK,CAACZ,SAAS,GAAEW,IAAI,CAACE,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC,CAAC,GAACF,IAAI,CAACE,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC;cAC/DH,QAAQ,GAAG,GAAGR,KAAK,IAAIC,GAAG,IAAII,SAAS,IAAIC,QAAQ,IAAIC,QAAQ,EAAE;YACnE,CAAC,MACI,IAAGF,SAAS,KAAK,KAAK,EAAC;cAC1B;cACA;cACAC,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACd,QAAQ,GAAGa,IAAI,CAACE,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC,CAAC,GAACF,IAAI,CAACE,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC;cAC/DJ,QAAQ,GAAGE,IAAI,CAACC,KAAK,CAACZ,SAAS,GAAEW,IAAI,CAACE,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC,CAAC,GAACF,IAAI,CAACE,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC;cAC/DH,QAAQ,GAAG,GAAGR,KAAK,IAAIC,GAAG,IAAII,SAAS,IAAIC,QAAQ,IAAIC,QAAQ,EAAE;YACnE,CAAC,MACG;cACF;cACAD,QAAQ,GAAGG,IAAI,CAACC,KAAK,CAACd,QAAQ,GAAGa,IAAI,CAACE,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC,CAAC,GAACF,IAAI,CAACE,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC;cAC/DJ,QAAQ,GAAGE,IAAI,CAACC,KAAK,CAACZ,SAAS,GAAEW,IAAI,CAACE,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC,CAAC,GAACF,IAAI,CAACE,GAAG,CAAC,EAAE,EAAC,CAAC,CAAC;cAC/DH,QAAQ,GAAG,GAAGR,KAAK,IAAIC,GAAG,IAAII,SAAS,IAAIC,QAAQ,IAAIC,QAAQ,EAAE;YACnE;YAEA,MAAMK,eAAe,GAAGC,mBAAmB,CACzCR,SAAS,EACTG,QAAQ,EACRN,SAAS,EACT;cAAE3J,GAAG,EAAEqJ,QAAQ;cAAEpJ,GAAG,EAAEsJ;YAAU,CAClC,CAAC;YAED,MAAMgB,WAAW,GAAGF,eAAe,CAACE,WAAW;YAC/C;YACA,IAAIlL,aAAa,GAAG,EAAE;YACtB,IAAImL,UAAU,GAAG,EAAE;YACnB,QAAOV,SAAS;cACd,KAAK,KAAK;gBAAEzK,aAAa,GAAG,OAAO;gBAAEmL,UAAU,GAAG,SAAS;gBAAE;cAC7D,KAAK,KAAK;gBAAEnL,aAAa,GAAG,OAAO;gBAAEmL,UAAU,GAAG,SAAS;gBAAE;cAC7D,KAAK,KAAK;gBAAEnL,aAAa,GAAG,QAAQ;gBAAEmL,UAAU,GAAG,SAAS;gBAAE;cAC9D,KAAK,KAAK;gBAAEnL,aAAa,GAAG,MAAM;gBAAEmL,UAAU,GAAG,SAAS;gBAAE;cAC5D,KAAK,KAAK;gBAAEnL,aAAa,GAAG,MAAM;gBAAEmL,UAAU,GAAG,SAAS;gBAAE;cAC5D,KAAK,MAAM;gBAAEnL,aAAa,GAAG,MAAM;gBAAEmL,UAAU,GAAG,SAAS;gBAAE;cAC7D,KAAK,KAAK;gBAAEnL,aAAa,GAAG,MAAM;gBAAEmL,UAAU,GAAG,SAAS;gBAAE;cAC5D;gBAASnL,aAAa,GAAG6B,KAAK,CAACuJ,WAAW,IAAI,MAAM;gBAAED,UAAU,GAAG,SAAS;YAC9E;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;;YAEA;YACA;YACA;YACA;YACA;YACA,IAAI,CAACD,WAAW,EAAE;cAChB;cACA,MAAM7L,SAAS,GAAG;gBAChBgM,OAAO,EAAEL,eAAe,CAACK,OAAO;gBAChCZ,SAAS,EAAEA,SAAS;gBACpBzK,aAAa,EAAEA,aAAa;gBAC5BoK,KAAK,EAAEN,OAAO,CAACM,KAAK,IAAI,MAAM;gBAC9BC,GAAG,EAAEA,GAAG;gBACRL,QAAQ,EAAEA,QAAQ;gBAClBE,SAAS,EAAEA,SAAS;gBACpBoB,IAAI,EAAEC,0BAA0B,CAACvB,QAAQ,EAAEE,SAAS,CAAC;gBACrDU,QAAQ,EAAEA,QAAQ;gBAClB/D,KAAK,EAAEsE,UAAU;gBACjBb,SAAS,EAAE,IAAIlJ,IAAI,CAAC,CAAC,CAACoK,WAAW,CAAC;cACpC,CAAC;;cAED;cACApM,oBAAoB,CAACC,SAAS,CAAC,CAACoM,IAAI,CAAC5L,OAAO,IAAI;gBAC9C,IAAIA,OAAO,EAAE;kBACXtB,OAAO,CAACuB,GAAG,CAAC,gBAAgBE,aAAa,EAAE,CAAC;;kBAE5C;kBACA,MAAM0L,QAAQ,GAAG;oBACfxG,GAAG,EAAE9D,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGwJ,IAAI,CAACc,MAAM,CAAC,CAAC;oBAC/B7J,IAAI,EAAE9B,aAAa;oBACnB4L,IAAI,EAAE,IAAIxK,IAAI,CAAC,CAAC,CAACkG,kBAAkB,CAAC,CAAC;oBACrC8C,KAAK,EAAEN,OAAO,CAACM,KAAK,IAAI,MAAM;oBAC9BvD,KAAK,EAAEsE,UAAU;oBACjBV,SAAS,EAAEA,SAAS;oBACpBoB,QAAQ,EAAE;sBACR7B,QAAQ,EAAEA,QAAQ;sBAClBE,SAAS,EAAEA;oBACb,CAAC;oBACDoB,IAAI,EAAEjM,SAAS,CAACiM;kBAClB,CAAC;kBAEDjO,SAAS,CAAC6D,IAAI,IAAI;oBAChB,MAAM4K,SAAS,GAAG,CAACJ,QAAQ,EAAE,GAAGxK,IAAI,CAAC,CAAC6K,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;oBACpD,OAAOD,SAAS;kBAClB,CAAC,CAAC;gBACJ,CAAC,MAAM;kBACLvN,OAAO,CAACD,KAAK,CAAC,aAAa0B,aAAa,EAAE,CAAC;gBAC7C;cACF,CAAC,CAAC;cAEFwK,sBAAsB,CAACwB,IAAI,CAACvB,SAAS,CAAC;YACxC,CAAC,MAAM;cACL;YAAA;UAEJ,CAAC,CAAC;;UAEF;UACA,IAAID,sBAAsB,CAAClK,MAAM,GAAG,CAAC,EAAE;YACrC/B,OAAO,CAACuB,GAAG,CAAC,UAAU0K,sBAAsB,CAAClK,MAAM,cAAc,CAAC;;YAElE;YACA/B,OAAO,CAACuB,GAAG,CAAC,SAAS,EAAElB,iBAAiB,CAAC;YACzCqN,UAAU,CAAC,YAAY;cACrB,MAAMzO,KAAK,GAAG,MAAMyC,2BAA2B,CAACrB,iBAAiB,CAAC;cAClE,IAAIpB,KAAK,EAAE;gBACTuB,aAAa,CAACoD,SAAS,KAAK;kBAC1B,GAAGA,SAAS;kBACZ,GAAG3E;gBACL,CAAC,CAAC,CAAC;gBACHe,OAAO,CAACuB,GAAG,CAAC,sBAAsB,EAAElB,iBAAiB,CAAC;cACxD;YACF,CAAC,EAAE,IAAI,CAAC;UACV;QACF;MACF,CAAC,CAAC,OAAON,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACtC;IACF,CAAC;;IAED;IACAqD,MAAM,CAACI,gBAAgB,CAAC,SAAS,EAAE8H,gBAAgB,CAAC;IAEpD,OAAO,MAAM;MACXlI,MAAM,CAACK,mBAAmB,CAAC,SAAS,EAAE6H,gBAAgB,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqC,kBAAkB,GAAIzB,SAAS,IAAK;IACxC,QAAOA,SAAS;MACd,KAAK,KAAK;QAAE;QACV,OAAO;UAAE0B,aAAa,EAAE,MAAM;UAAEC,iBAAiB,EAAE;QAAG,CAAC;MAAE;MAC3D,KAAK,KAAK;QAAE;QACV,OAAO;UAAED,aAAa,EAAE,KAAK;UAAEC,iBAAiB,EAAE;QAAG,CAAC;MAAE;MAC1D,KAAK,KAAK;QAAE;QACV,OAAO;UAAED,aAAa,EAAE,KAAK;UAAEC,iBAAiB,EAAE;QAAG,CAAC;MAAE;MAC1D,KAAK,KAAK,CAAC,CAAC;MACZ,KAAK,KAAK,CAAC,CAAC;MACZ,KAAK,MAAM;QAAE;QACX,OAAO;UAAED,aAAa,EAAE,MAAM;UAAEC,iBAAiB,EAAE;QAAE,CAAC;MAAE;MAC1D,KAAK,KAAK;QAAE;QACV,OAAO;UAAED,aAAa,EAAE,KAAK;UAAEC,iBAAiB,EAAE;QAAG,CAAC;MAAE;MAC1D;QACE,OAAO;UAAED,aAAa,EAAE,IAAI;UAAEC,iBAAiB,EAAE;QAAE,CAAC;MAAE;IAC1D;EACF,CAAC;;EAED;EACA,MAAMnB,mBAAmB,GAAGA,CAACR,SAAS,EAAEG,QAAQ,EAAElE,WAAW,EAAE2F,UAAU,KAAK;IAC5E,MAAM;MAAEF,aAAa;MAAEC;IAAkB,CAAC,GAAGF,kBAAkB,CAACzB,SAAS,CAAC;;IAE1E;IACA,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpN,cAAc,CAACiC,OAAO,CAACb,MAAM,EAAEgM,CAAC,EAAE,EAAE;MACtD,MAAMC,WAAW,GAAGrN,cAAc,CAACiC,OAAO,CAACmL,CAAC,CAAC;;MAE7C;MACA,IAAIC,WAAW,CAAC9B,SAAS,KAAKA,SAAS,EAAE;QACvC;MACF;;MAEA;MACA,MAAM+B,QAAQ,GAAG9F,WAAW,GAAG6F,WAAW,CAACnG,cAAc;;MAEzD;MACA,IAAIoG,QAAQ,GAAGL,aAAa,EAAE;QAC5B;MACF;;MAEA;MACA,MAAMM,QAAQ,GAAGC,iBAAiB,CAChCL,UAAU,CAAC1L,GAAG,EAAE0L,UAAU,CAACzL,GAAG,EAC9B2L,WAAW,CAACtD,QAAQ,CAACtI,GAAG,EAAE4L,WAAW,CAACtD,QAAQ,CAACrI,GACjD,CAAC;;MAED;MACA,IAAI6L,QAAQ,IAAIL,iBAAiB,EAAE;QACjC;QACAG,WAAW,CAAC3B,QAAQ,GAAGA,QAAQ;QAC/B2B,WAAW,CAACnG,cAAc,GAAGM,WAAW;QACxC6F,WAAW,CAACtD,QAAQ,GAAG;UAAE,GAAGoD;QAAW,CAAC;QACxCE,WAAW,CAACI,WAAW,GAAG,CAACJ,WAAW,CAACI,WAAW,IAAI,CAAC,IAAI,CAAC;;QAE5D;;QAEA,OAAO;UACLzB,WAAW,EAAE,IAAI;UACjBG,OAAO,EAAEkB,WAAW,CAAClB,OAAO;UAC5BuB,YAAY,EAAEL;QAChB,CAAC;MACH;IACF;;IAEA;IACA,MAAMM,UAAU,GAAG,OAAO1N,cAAc,CAACgC,OAAO,CAAC2L,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAC9E5N,cAAc,CAACgC,OAAO,EAAE;IAExB,MAAMuK,QAAQ,GAAG;MACfL,OAAO,EAAEwB,UAAU;MACnBpC,SAAS,EAAEA,SAAS;MACpBG,QAAQ,EAAEA,QAAQ;MAClBoC,iBAAiB,EAAEtG,WAAW;MAC9BN,cAAc,EAAEM,WAAW;MAC3BuC,QAAQ,EAAE;QAAE,GAAGoD;MAAW,CAAC;MAC3BM,WAAW,EAAE;IACf,CAAC;;IAED;IACAzN,cAAc,CAACiC,OAAO,CAAC6K,IAAI,CAACN,QAAQ,CAAC;IAErCnN,OAAO,CAACuB,GAAG,CAAC,WAAW2K,SAAS,SAASoC,UAAU,UAAUR,UAAU,CAAC1L,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,KAAKqL,UAAU,CAACzL,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAExH,OAAO;MACLkK,WAAW,EAAE,KAAK;MAClBG,OAAO,EAAEwB,UAAU;MACnBnB,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC;;EAED;EACA,MAAMhJ,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMgE,WAAW,GAAGtF,IAAI,CAACC,GAAG,CAAC,CAAC;IAC9B,MAAM4L,MAAM,GAAG,OAAO,CAAC,CAAC;;IAExB,MAAMC,YAAY,GAAGhO,cAAc,CAACiC,OAAO,CAACb,MAAM;IAClDpB,cAAc,CAACiC,OAAO,GAAGjC,cAAc,CAACiC,OAAO,CAAC+B,MAAM,CAACrB,KAAK,IAAI;MAC9D,MAAMsL,GAAG,GAAGzG,WAAW,GAAG7E,KAAK,CAACuE,cAAc;MAC9C,OAAO+G,GAAG,IAAIF,MAAM;IACtB,CAAC,CAAC;IAEF,MAAMG,YAAY,GAAGF,YAAY,GAAGhO,cAAc,CAACiC,OAAO,CAACb,MAAM;IACjE,IAAI8M,YAAY,GAAG,CAAC,EAAE;MACpB7O,OAAO,CAACuB,GAAG,CAAC,UAAUsN,YAAY,mBAAmBlO,cAAc,CAACiC,OAAO,CAACb,MAAM,EAAE,CAAC;IACvF;EACF,CAAC;;EAED;EACA,MAAMsC,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAM8D,WAAW,GAAGtF,IAAI,CAACC,GAAG,CAAC,CAAC;IAC9B,MAAMgM,iBAAiB,GAAG,KAAK,CAAC,CAAC;;IAEjC,MAAMH,YAAY,GAAGhO,cAAc,CAACiC,OAAO,CAACb,MAAM;IAClD,MAAMgN,aAAa,GAAG,EAAE;IAExBpO,cAAc,CAACiC,OAAO,GAAGjC,cAAc,CAACiC,OAAO,CAAC+B,MAAM,CAACrB,KAAK,IAAI;MAC9D,MAAM0L,mBAAmB,GAAG7G,WAAW,GAAG7E,KAAK,CAACuE,cAAc;MAC9D,IAAImH,mBAAmB,GAAGF,iBAAiB,EAAE;QAC3CC,aAAa,CAACtB,IAAI,CAAC;UACjB7I,EAAE,EAAEtB,KAAK,CAACwJ,OAAO;UACjBvJ,IAAI,EAAED,KAAK,CAAC4I,SAAS;UACrB+C,YAAY,EAAE,CAACD,mBAAmB,GAAG,IAAI,EAAEvM,OAAO,CAAC,CAAC;QACtD,CAAC,CAAC;QACF,OAAO,KAAK,CAAC,CAAC;MAChB;MACA,OAAO,IAAI,CAAC,CAAC;IACf,CAAC,CAAC;IAEF,MAAMoM,YAAY,GAAGF,YAAY,GAAGhO,cAAc,CAACiC,OAAO,CAACb,MAAM;IACjE,IAAI8M,YAAY,GAAG,CAAC,EAAE;MACpB7O,OAAO,CAACuB,GAAG,CAAC,WAAWsN,YAAY,eAAe,CAAC;MACnDE,aAAa,CAAC5I,OAAO,CAAC7C,KAAK,IAAI;QAC7BtD,OAAO,CAACuB,GAAG,CAAC,WAAW+B,KAAK,CAACsB,EAAE,SAAStB,KAAK,CAACC,IAAI,YAAYD,KAAK,CAAC2L,YAAY,GAAG,CAAC;MACtF,CAAC,CAAC;MACFjP,OAAO,CAACuB,GAAG,CAAC,eAAeZ,cAAc,CAACiC,OAAO,CAACb,MAAM,EAAE,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMoM,iBAAiB,GAAGA,CAACe,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,KAAK;IACpD,IAAIH,IAAI,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC,IAAIC,IAAI,KAAK,CAAC,EAAE;MACxD,OAAO,GAAG;IACZ;IACA,MAAMC,CAAC,GAAG,OAAO;IACjB,MAAMC,IAAI,GAAG,CAACH,IAAI,GAAGF,IAAI,IAAI5C,IAAI,CAACkD,EAAE,GAAG,GAAG;IAC1C,MAAMC,IAAI,GAAG,CAACJ,IAAI,GAAGF,IAAI,IAAI7C,IAAI,CAACkD,EAAE,GAAG,GAAG;IAC1C,MAAM9G,CAAC,GACL4D,IAAI,CAACoD,GAAG,CAACH,IAAI,GAAC,CAAC,CAAC,GAAGjD,IAAI,CAACoD,GAAG,CAACH,IAAI,GAAC,CAAC,CAAC,GACnCjD,IAAI,CAACqD,GAAG,CAACT,IAAI,GAAG5C,IAAI,CAACkD,EAAE,GAAG,GAAG,CAAC,GAAGlD,IAAI,CAACqD,GAAG,CAACP,IAAI,GAAG9C,IAAI,CAACkD,EAAE,GAAG,GAAG,CAAC,GAC/DlD,IAAI,CAACoD,GAAG,CAACD,IAAI,GAAC,CAAC,CAAC,GAAGnD,IAAI,CAACoD,GAAG,CAACD,IAAI,GAAC,CAAC,CAAC;IACrC,MAAMG,CAAC,GAAG,CAAC,GAAGtD,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACwD,IAAI,CAACpH,CAAC,CAAC,EAAE4D,IAAI,CAACwD,IAAI,CAAC,CAAC,GAACpH,CAAC,CAAC,CAAC;IACtD,OAAO4G,CAAC,GAAGM,CAAC;EACd,CAAC;;EAED;EACA,MAAM5C,0BAA0B,GAAGA,CAAC5K,GAAG,EAAEC,GAAG,KAAK;IAC/C;IACA,MAAM0N,aAAa,GAAG5S,iBAAiB,CAAC4S,aAAa,IAAI,EAAE;IAC3D,IAAIC,WAAW,GAAGC,QAAQ;IAC1B,IAAIC,WAAW,GAAG,MAAM;IACxBH,aAAa,CAAC5J,OAAO,CAACgK,KAAK,IAAI;MAC7B,MAAMC,QAAQ,GAAG5N,UAAU,CAAC2N,KAAK,CAAC1E,QAAQ,CAAC;MAC3C,MAAM4E,QAAQ,GAAG7N,UAAU,CAAC2N,KAAK,CAACxE,SAAS,CAAC;MAC5C,MAAM2E,IAAI,GAAGnC,iBAAiB,CAAC/L,GAAG,EAAEC,GAAG,EAAE+N,QAAQ,EAAEC,QAAQ,CAAC;MAC5D,IAAIC,IAAI,GAAGN,WAAW,EAAE;QACtBA,WAAW,GAAGM,IAAI;QAClBJ,WAAW,GAAGC,KAAK,CAAC9H,IAAI;MAC1B;IACF,CAAC,CAAC;IACF,OAAO6H,WAAW;EACpB,CAAC;;EAED;EACA,MAAMK,mBAAmB,GAAIpN,OAAO,IAAK;IACvCnD,OAAO,CAACuB,GAAG,CAAC,OAAO,EAAE4B,OAAO,CAAC8B,WAAW,EAAE,KAAK,EAAE9B,OAAO,CAACjB,MAAM,CAAC;IAChElD,kBAAkB,CAACmE,OAAO,CAAC;EAC7B,CAAC;;EAED;EACAzH,SAAS,CAAC,MAAM;IACd;IACA,IAAIqD,eAAe,EAAE;MACnB,MAAMyR,sBAAsB,GAAG7R,QAAQ,CAACoH,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAK7F,eAAe,CAAC6F,EAAE,CAAC;MAC9E,IAAI4L,sBAAsB,KACrBA,sBAAsB,CAACtO,MAAM,KAAKnD,eAAe,CAACmD,MAAM,IACxDsO,sBAAsB,CAACrO,KAAK,KAAKpD,eAAe,CAACoD,KAAK,IACtDqO,sBAAsB,CAACpO,GAAG,KAAKrD,eAAe,CAACqD,GAAG,IAClDoO,sBAAsB,CAACnO,GAAG,KAAKtD,eAAe,CAACsD,GAAG,IAClDmO,sBAAsB,CAAClO,OAAO,KAAKvD,eAAe,CAACuD,OAAO,CAAC,EAAE;QAChEtC,OAAO,CAACuB,GAAG,CAAC,UAAUxC,eAAe,CAACkG,WAAW,OAAO,EAC7C,OAAOlG,eAAe,CAACmD,MAAM,OAAOsO,sBAAsB,CAACtO,MAAM,EAAE,CAAC;QAC/ElD,kBAAkB,CAACwR,sBAAsB,CAAC;MAC5C;IACF;EACF,CAAC,EAAE,CAAC7R,QAAQ,EAAEI,eAAe,CAAC,CAAC;;EAE/B;EACA,MAAM0R,cAAc,GAAG,CACrB;IACE5H,KAAK,EAAE,KAAK;IACZ6H,SAAS,EAAE,OAAO;IAClB/J,GAAG,EAAE,OAAO;IACZgK,KAAK,EAAE;EACT,CAAC,EACD;IACE9H,KAAK,EAAE,IAAI;IACX6H,SAAS,EAAE,QAAQ;IACnB/J,GAAG,EAAE,QAAQ;IACbgK,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE1O,MAAM,iBACZ7E,OAAA,CAAChB,KAAK;MACJ6F,MAAM,EAAEA,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;MAClD4G,IAAI,EAAE5G,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;IAAK;MAAA2O,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC;EAEL,CAAC,EACD;IACEnI,KAAK,EAAE,IAAI;IACX6H,SAAS,EAAE,OAAO;IAClB/J,GAAG,EAAE,OAAO;IACZgK,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEzO,KAAK,IAAI,GAAG,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,GAAGN,KAAK;EAC1E,CAAC,CACF;;EAED;EACA,MAAM8O,eAAe,GAAGA,CAAA,kBACtB5T,OAAA,CAACpB,IAAI;IACHsK,IAAI,EAAC,OAAO;IACZ2K,UAAU,EAAErS,MAAO;IACnBsS,UAAU,EAAE3I,IAAI,iBACdnL,OAAA,CAACpB,IAAI,CAACmV,IAAI;MAACC,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAQ,CAAE;MAAAC,QAAA,eACrClU,OAAA,CAACpB,IAAI,CAACmV,IAAI,CAACI,IAAI;QACb3I,KAAK,eACHxL,OAAA;UAAKgU,KAAK,EAAE;YAAElI,QAAQ,EAAE,MAAM;YAAEsI,YAAY,EAAE;UAAM,CAAE;UAAAF,QAAA,gBACpDlU,OAAA;YAAMgU,KAAK,EAAE;cAAE/I,KAAK,EAAEE,IAAI,CAACF,KAAK;cAAEoJ,WAAW,EAAE;YAAM,CAAE;YAAAH,QAAA,EACpD/I,IAAI,CAACjF;UAAI;YAAAsN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACP3T,OAAA;YAAMgU,KAAK,EAAE;cAAE/I,KAAK,EAAE,MAAM;cAAEa,QAAQ,EAAE;YAAO,CAAE;YAAAoI,QAAA,EAC9C/I,IAAI,CAAC6E;UAAI;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;QACDnE,WAAW,eACTxP,OAAA;UAAKgU,KAAK,EAAE;YAAElI,QAAQ,EAAE,MAAM;YAAEb,KAAK,EAAE;UAAO,CAAE;UAAAiJ,QAAA,gBAE9ClU,OAAA;YAAAkU,QAAA,GAAK,gBAAI,EAAC/I,IAAI,CAACuE,IAAI,IAAI,MAAM;UAAA;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpC3T,OAAA;YAAAkU,QAAA,GAAK,gBAAI,EAAC/I,IAAI,CAAC8E,QAAQ,GACrB,GAAG9E,IAAI,CAAC8E,QAAQ,CAAC7B,QAAQ,CAAChJ,OAAO,CAAC,CAAC,CAAC,KAAK+F,IAAI,CAAC8E,QAAQ,CAAC3B,SAAS,CAAClJ,OAAO,CAAC,CAAC,CAAC,EAAE,GAC7E,MAAM;UAAA;YAAAoO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CACX;IACFK,KAAK,EAAE;MACLM,SAAS,EAAE,mBAAmB;MAC9BC,SAAS,EAAE;IACb;EAAE;IAAAf,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACF;;EAED;EACAtV,SAAS,CAAC,MAAM;IACd,MAAMmW,kBAAkB,GAAG7N,WAAW,CAAC,MAAM;MAC3ChE,OAAO,CAACuB,GAAG,CAAC,gBAAgB,CAAC;MAC7BiD,uBAAuB,CAAC,CAAC;IAC3B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMP,aAAa,CAAC4N,kBAAkB,CAAC;EAChD,CAAC,EAAE,CAAClT,QAAQ,CAAC,CAAC;EAEd,oBACEtB,OAAA,CAACjB,IAAI;IAAC0V,QAAQ,EAAErT,OAAQ;IAACsT,GAAG,EAAC,uBAAQ;IAAAR,QAAA,eACnClU,OAAA,CAACI,aAAa;MAAA8T,QAAA,gBAEZlU,OAAA,CAACL,kBAAkB;QACjB0N,QAAQ,EAAC,MAAM;QACfsH,SAAS,EAAEjU,aAAc;QACzBkU,UAAU,EAAEA,CAAA,KAAM9R,gBAAgB,CAAC,CAACpC,aAAa,CAAE;QAAAwT,QAAA,gBAGnDlU,OAAA,CAACa,QAAQ;UAAC2K,KAAK,EAAC,4CAAS;UAACqJ,QAAQ,EAAE,KAAM;UAAC/T,MAAM,EAAC,OAAO;UAAAoT,QAAA,eACvDlU,OAAA,CAACxB,GAAG;YAACsW,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAE;YAAAZ,QAAA,gBAClBlU,OAAA,CAACvB,GAAG;cAACsW,IAAI,EAAE,CAAE;cAACf,KAAK,EAAE;gBAAEgB,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAS,CAAE;cAAAf,QAAA,eACjElU,OAAA,CAACgB,gBAAgB;gBACfwK,KAAK,EAAC,0BAAM;gBACZ/E,KAAK,EAAE7E,KAAK,CAACE,aAAc;gBAC3BoT,UAAU,EAAE;kBAAEjK,KAAK,EAAE,SAAS;kBAAC+J,OAAO,EAAE,MAAM;kBAACC,cAAc,EAAE;gBAAS;gBACxE;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN3T,OAAA,CAACvB,GAAG;cAACsW,IAAI,EAAE,CAAE;cAACf,KAAK,EAAE;gBAAEgB,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAS,CAAE;cAAAf,QAAA,eACjElU,OAAA,CAACgB,gBAAgB;gBACfwK,KAAK,EAAC,0BAAM;gBACZ/E,KAAK,EAAE7E,KAAK,CAACG;gBACb;gBAAA;gBACAmT,UAAU,EAAE;kBAAEjK,KAAK,EAAE,SAAS;kBAAE+J,OAAO,EAAE,MAAM;kBAACC,cAAc,EAAE;gBAAQ;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN3T,OAAA,CAACvB,GAAG;cAACsW,IAAI,EAAE,CAAE;cAACf,KAAK,EAAE;gBAAEgB,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAS,CAAE;cAAAf,QAAA,eACjElU,OAAA,CAACgB,gBAAgB;gBACfwK,KAAK,EAAC,0BAAM;gBACZ/E,KAAK,EAAE7E,KAAK,CAACI;gBACb;gBAAA;gBACAkT,UAAU,EAAE;kBAAEjK,KAAK,EAAE,SAAS;kBAAE+J,OAAO,EAAE,MAAM;kBAACC,cAAc,EAAE;gBAAS;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN3T,OAAA,CAACvB,GAAG;cAACsW,IAAI,EAAE,CAAE;cAACf,KAAK,EAAE;gBAAEgB,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAS,CAAE;cAAAf,QAAA,eACjElU,OAAA,CAACgB,gBAAgB;gBACfwK,KAAK,EAAC,0BAAM;gBACZ/E,KAAK,EAAE7E,KAAK,CAACK,YAAa;gBAC1BiT,UAAU,EAAE;kBAAEjK,KAAK,EAAE,SAAS;kBAAC+J,OAAO,EAAE,MAAM;kBAACC,cAAc,EAAE;gBAAS;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN3T,OAAA,CAACvB,GAAG;cAACsW,IAAI,EAAE,CAAE;cAACf,KAAK,EAAE;gBAAEgB,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAS,CAAE;cAAAf,QAAA,eACjElU,OAAA,CAACgB,gBAAgB;gBACfwK,KAAK,EAAC,0BAAM;gBACZ/E,KAAK,EAAE7E,KAAK,CAACM;gBACb;gBAAA;gBACAgT,UAAU,EAAE;kBAAEjK,KAAK,EAAE,SAAS;kBAAC+J,OAAO,EAAE,MAAM;kBAACC,cAAc,EAAE;gBAAS;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN3T,OAAA,CAACvB,GAAG;cAACsW,IAAI,EAAE,CAAE;cAACf,KAAK,EAAE;gBAAEgB,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAS,CAAE;cAAAf,QAAA,eACjElU,OAAA,CAACgB,gBAAgB;gBACfwK,KAAK,EAAC,0BAAM;gBACZ/E,KAAK,EAAE7E,KAAK,CAACO;gBACb;gBAAA;gBACA+S,UAAU,EAAE;kBAAEjK,KAAK,EAAE,SAAS;kBAAC+J,OAAO,EAAE,MAAM;kBAACC,cAAc,EAAE;gBAAS;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGX3T,OAAA,CAACa,QAAQ;UACP2K,KAAK,EAAC,sCAAQ;UACdqJ,QAAQ,EAAE,KAAM;UAChB/T,MAAM,EAAC;UACP;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UAAA;UAAAoT,QAAA,EAECN,eAAe,CAAC;QAAC;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGX3T,OAAA,CAACa,QAAQ;UACP2K,KAAK,EAAC,sCAAQ;UACdqJ,QAAQ,EAAE,KAAM;UAChB/T,MAAM,EAAC,kBAAkB;UACzBqU,KAAK,eACHnV,OAAA,CAACd,MAAM;YACLuH,KAAK,EAAEzD,iBAAkB;YACzBoS,QAAQ,EAAE5O,qBAAsB;YAChC6O,QAAQ,EAAE7O,qBAAsB;YAChC0C,IAAI,EAAC,OAAO;YACZ8K,KAAK,EAAE;cAAEV,KAAK,EAAE;YAAG,CAAE;YACrBgC,OAAO,EAAE,CACP;cAAE7O,KAAK,EAAE,IAAI;cAAE2G,KAAK,EAAE;YAAM,CAAC,EAC7B;cAAE3G,KAAK,EAAE,KAAK;cAAE2G,KAAK,EAAE;YAAK,CAAC,EAC7B;cAAE3G,KAAK,EAAE,IAAI;cAAE2G,KAAK,EAAE;YAAK,CAAC,EAC5B;cAAE3G,KAAK,EAAE,KAAK;cAAE2G,KAAK,EAAE;YAAK,CAAC;UAC7B;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF;UAAAO,QAAA,eAEDlU,OAAA;YAAKuV,GAAG,EAAE1S,aAAc;YAACmR,KAAK,EAAE;cAAElT,MAAM,EAAE,MAAM;cAAEwS,KAAK,EAAE;YAAO;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGrB3T,OAAA,CAACQ,WAAW;QAACE,aAAa,EAAEA,aAAc;QAACC,cAAc,EAAEA;MAAe;QAAA6S,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC,eAGd3T,OAAA,CAACL,kBAAkB;QACjB0N,QAAQ,EAAC,OAAO;QAChBsH,SAAS,EAAEhU,cAAe;QAC1BiU,UAAU,EAAEA,CAAA,KAAM7R,iBAAiB,CAAC,CAACpC,cAAc,CAAE;QAAAuT,QAAA,gBAGrDlU,OAAA,CAACa,QAAQ;UAAC2K,KAAK,EAAC,0BAAM;UAACqJ,QAAQ,EAAE,KAAM;UAAC/T,MAAM,EAAC,KAAK;UAAAoT,QAAA,eAClDlU,OAAA,CAACnB,KAAK;YACJgV,UAAU,EAAEvS,QAAS;YACrBkU,OAAO,EAAEpC,cAAe;YACxBqC,MAAM,EAAC,IAAI;YACXC,UAAU,EAAE,KAAM;YAClBxM,IAAI,EAAC,OAAO;YACZyM,MAAM,EAAE;cAAEC,CAAC,EAAE;YAAI,CAAE;YACnBC,KAAK,EAAGC,MAAM,KAAM;cAClBC,OAAO,EAAEA,CAAA,KAAM7C,mBAAmB,CAAC4C,MAAM,CAAC;cAC1C9B,KAAK,EAAE;gBACLgC,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,CAAAvU,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE6F,EAAE,MAAKuO,MAAM,CAACvO,EAAE,GAAG,SAAS,GAAG,aAAa;gBACzEuE,QAAQ,EAAE,MAAM;gBAChBmI,OAAO,EAAE;cACX;YACF,CAAC;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAGX3T,OAAA,CAACa,QAAQ;UAAC2K,KAAK,EAAC,sCAAQ;UAACqJ,QAAQ,EAAE,KAAM;UAAC/T,MAAM,EAAC,KAAK;UAAAoT,QAAA,EACnDxS,eAAe,gBACd1B,OAAA,CAAClB,YAAY;YACX+V,QAAQ;YACRqB,MAAM,EAAE,CAAE;YACVhN,IAAI,EAAC,OAAO;YACZiN,MAAM,EAAE;cACN/I,KAAK,EAAE;gBAAEtB,QAAQ,EAAE,MAAM;gBAAEmI,OAAO,EAAE;cAAU,CAAC;cAC/CmC,OAAO,EAAE;gBAAEtK,QAAQ,EAAE,MAAM;gBAAEmI,OAAO,EAAE;cAAU;YAClD,CAAE;YAAAC,QAAA,gBAEFlU,OAAA,CAAClB,YAAY,CAACiV,IAAI;cAAC3G,KAAK,EAAC,oBAAK;cAAA8G,QAAA,EAAExS,eAAe,CAACkG;YAAW;cAAA4L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAChF3T,OAAA,CAAClB,YAAY,CAACiV,IAAI;cAAC3G,KAAK,EAAC,cAAI;cAAA8G,QAAA,eAC3BlU,OAAA,CAAChB,KAAK;gBACJ6F,MAAM,EAAEnD,eAAe,CAACmD,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;gBAClE4G,IAAI,EAAE/J,eAAe,CAACmD,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG;cAAK;gBAAA2O,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,eACpB3T,OAAA,CAAClB,YAAY,CAACiV,IAAI;cAAC3G,KAAK,EAAC,cAAI;cAAA8G,QAAA,EAC1BxS,eAAe,CAACmD,MAAM,KAAK,QAAQ,GAAGnD,eAAe,CAACsD,GAAG,CAACI,OAAO,CAAC,CAAC,CAAC,GAAG;YAAK;cAAAoO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACpB3T,OAAA,CAAClB,YAAY,CAACiV,IAAI;cAAC3G,KAAK,EAAC,cAAI;cAAA8G,QAAA,EAC1BxS,eAAe,CAACmD,MAAM,KAAK,QAAQ,GAAGnD,eAAe,CAACqD,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC,GAAG;YAAK;cAAAoO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACpB3T,OAAA,CAAClB,YAAY,CAACiV,IAAI;cAAC3G,KAAK,EAAC,cAAI;cAAA8G,QAAA,EAC1BxS,eAAe,CAACmD,MAAM,KAAK,QAAQ,GAClC,GAAG,OAAOnD,eAAe,CAACoD,KAAK,KAAK,QAAQ,GAAGpD,eAAe,CAACoD,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,GAAG1D,eAAe,CAACoD,KAAK,OAAO,GAC9G;YAAK;cAAA0O,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC,eACpB3T,OAAA,CAAClB,YAAY,CAACiV,IAAI;cAAC3G,KAAK,EAAC,oBAAK;cAAA8G,QAAA,EAC3BxS,eAAe,CAACmD,MAAM,KAAK,QAAQ,GAClC,GAAG,OAAOnD,eAAe,CAACuD,OAAO,KAAK,QAAQ,GAAGvD,eAAe,CAACuD,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,GAAG1D,eAAe,CAACuD,OAAO,GAAG,GAChH;YAAK;cAAAuO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,gBAEf3T,OAAA;YAAGgU,KAAK,EAAE;cAAElI,QAAQ,EAAE;YAAO,CAAE;YAAAoI,QAAA,EAAC;UAAW;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC/C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEX,CAAC;AAACxS,EAAA,CA9/CID,eAAe;AAAAmV,GAAA,GAAfnV,eAAe;AAggDrB,eAAeA,eAAe;AAAC,IAAAb,EAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAoV,GAAA;AAAAC,YAAA,CAAAjW,EAAA;AAAAiW,YAAA,CAAA1V,GAAA;AAAA0V,YAAA,CAAAvV,GAAA;AAAAuV,YAAA,CAAArV,GAAA;AAAAqV,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}