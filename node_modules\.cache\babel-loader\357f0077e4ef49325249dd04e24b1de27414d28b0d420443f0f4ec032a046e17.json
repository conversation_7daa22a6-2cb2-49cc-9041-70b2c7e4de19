{"ast": null, "code": "import { EXPAND_COLUMN, INTERNAL_HOOKS } from \"./constant\";\nimport { FooterComponents as Summary } from \"./Footer\";\nimport Column from \"./sugar/Column\";\nimport ColumnGroup from \"./sugar/ColumnGroup\";\nimport Table, { genTable } from \"./Table\";\nimport { INTERNAL_COL_DEFINE } from \"./utils/legacyUtil\";\nimport VirtualTable, { genVirtualTable } from \"./VirtualTable\";\nexport { genTable, Summary, Column, ColumnGroup, INTERNAL_COL_DEFINE, EXPAND_COLUMN, INTERNAL_HOOKS, VirtualTable, genVirtualTable };\nexport default Table;", "map": {"version": 3, "names": ["EXPAND_COLUMN", "INTERNAL_HOOKS", "FooterComponents", "Summary", "Column", "ColumnGroup", "Table", "genTable", "INTERNAL_COL_DEFINE", "VirtualTable", "genVirtualTable"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-table/es/index.js"], "sourcesContent": ["import { EXPAND_COLUMN, INTERNAL_HOOKS } from \"./constant\";\nimport { FooterComponents as Summary } from \"./Footer\";\nimport Column from \"./sugar/Column\";\nimport ColumnGroup from \"./sugar/ColumnGroup\";\nimport Table, { genTable } from \"./Table\";\nimport { INTERNAL_COL_DEFINE } from \"./utils/legacyUtil\";\nimport VirtualTable, { genVirtualTable } from \"./VirtualTable\";\nexport { genTable, Summary, Column, ColumnGroup, INTERNAL_COL_DEFINE, EXPAND_COLUMN, INTERNAL_HOOKS, VirtualTable, genVirtualTable };\nexport default Table;"], "mappings": "AAAA,SAASA,aAAa,EAAEC,cAAc,QAAQ,YAAY;AAC1D,SAASC,gBAAgB,IAAIC,OAAO,QAAQ,UAAU;AACtD,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,KAAK,IAAIC,QAAQ,QAAQ,SAAS;AACzC,SAASC,mBAAmB,QAAQ,oBAAoB;AACxD,OAAOC,YAAY,IAAIC,eAAe,QAAQ,gBAAgB;AAC9D,SAASH,QAAQ,EAAEJ,OAAO,EAAEC,MAAM,EAAEC,WAAW,EAAEG,mBAAmB,EAAER,aAAa,EAAEC,cAAc,EAAEQ,YAAY,EAAEC,eAAe;AAClI,eAAeJ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}