{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\RoadMonitoring.jsx\",\n  _s = $RefreshSig$();\n// src/pages/RoadMonitoring.jsx\nimport React, { useState, useEffect } from 'react';\nimport { Card, List, Descriptions, Spin, Badge, Row, Col } from 'antd';\nimport styled from 'styled-components';\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\n\n// 页面布局容器\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PageContainer = styled.div`\n  display: flex;\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\n  overflow: hidden;\n`;\n\n// 左侧信息栏容器\n_c = PageContainer;\nconst LeftSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 8px 0 0;\n  border-right: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 右侧信息栏容器\nconst RightSidebar = styled.div`\n  width: 320px;\n  height: 100%;\n  padding: 0 0 0 8px;\n  border-left: 1px solid #f0f0f0;\n  display: flex;\n  flex-direction: column;\n`;\n\n// 主内容区域\nconst MainContent = styled.div`\n  flex: 1;\n  height: 100%;\n  overflow: hidden;\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 24px' : props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\n  transition: all 0.3s ease;\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : props.leftCollapsed ? '0 8px 0 0' : props.rightCollapsed ? '0 0 0 8px' : '0'};\n  position: relative;\n  z-index: 1;\n`;\n\n// 信息卡片\n_c2 = MainContent;\nconst InfoCard = styled(Card)`\n  margin-bottom: 12px;\n  height: ${props => props.height || 'auto'};\n  \n  .ant-card-head {\n    min-height: 40px;\n    padding: 0 12px;\n  }\n  \n  .ant-card-head-title {\n    padding: 8px 0;\n    font-size: 14px;\n  }\n  \n  .ant-card-body {\n    padding: 12px;\n    font-size: 13px;\n    height: calc(100% - 40px); // 减去卡片头部高度\n    overflow-y: auto;\n  }\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// 视频容器\n_c3 = InfoCard;\nconst VideoContainer = styled.div`\n  background: #000;\n  border-radius: 4px;\n  overflow: hidden;\n  position: relative;\n  margin-bottom: 10px;\n  \n  &:before {\n    content: \"\";\n    display: block;\n    padding-top: 56.25%; // 16:9 宽高比\n  }\n`;\n\n// 视频占位符\n_c4 = VideoContainer;\nconst VideoPlaceholder = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 13px;\n`;\n_c5 = VideoPlaceholder;\nconst RoadMonitoring = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n  const [intersections, setIntersections] = useState([]);\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n\n  // 添加侧边栏折叠状态\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\n  const [rightCollapsed, setRightCollapsed] = useState(false);\n\n  // 模拟数据加载\n  useEffect(() => {\n    // 模拟加载延迟\n    setTimeout(() => {\n      // 模拟路口数据\n      const intersectionsData = [{\n        id: 1,\n        name: '东门路口',\n        description: '校园东门主要出入口',\n        devices: [{\n          type: 'camera',\n          id: 'CAM-E-01',\n          name: '东门摄像头1',\n          status: 'online'\n        }, {\n          type: 'camera',\n          id: 'CAM-E-02',\n          name: '东门摄像头2',\n          status: 'online'\n        }, {\n          type: 'rsu',\n          id: 'RSU-E-01',\n          name: '东门RSU',\n          status: 'online'\n        }, {\n          type: 'radar',\n          id: 'RAD-E-01',\n          name: '东门毫米波雷达',\n          status: 'online'\n        }]\n      }, {\n        id: 2,\n        name: '北门路口',\n        description: '校园北门次要出入口',\n        devices: [{\n          type: 'camera',\n          id: 'CAM-N-01',\n          name: '北门摄像头1',\n          status: 'online'\n        }, {\n          type: 'camera',\n          id: 'CAM-N-02',\n          name: '北门摄像头2',\n          status: 'offline'\n        }, {\n          type: 'rsu',\n          id: 'RSU-N-01',\n          name: '北门RSU',\n          status: 'online'\n        }]\n      }, {\n        id: 3,\n        name: '图书馆路口',\n        description: '图书馆前T字路口',\n        devices: [{\n          type: 'camera',\n          id: 'CAM-L-01',\n          name: '图书馆摄像头1',\n          status: 'online'\n        }, {\n          type: 'camera',\n          id: 'CAM-L-02',\n          name: '图书馆摄像头2',\n          status: 'online'\n        }, {\n          type: 'camera',\n          id: 'CAM-L-03',\n          name: '图书馆摄像头3',\n          status: 'online'\n        }, {\n          type: 'camera',\n          id: 'CAM-L-04',\n          name: '图书馆摄像头4',\n          status: 'online'\n        }, {\n          type: 'rsu',\n          id: 'RSU-L-01',\n          name: '图书馆RSU',\n          status: 'online'\n        }, {\n          type: 'lidar',\n          id: 'LID-L-01',\n          name: '图书馆激光雷达',\n          status: 'online'\n        }]\n      }, {\n        id: 4,\n        name: '食堂路口',\n        description: '学生食堂前十字路口',\n        devices: [{\n          type: 'camera',\n          id: 'CAM-C-01',\n          name: '食堂摄像头1',\n          status: 'online'\n        }, {\n          type: 'camera',\n          id: 'CAM-C-02',\n          name: '食堂摄像头2',\n          status: 'offline'\n        }, {\n          type: 'rsu',\n          id: 'RSU-C-01',\n          name: '食堂RSU',\n          status: 'offline'\n        }]\n      }, {\n        id: 5,\n        name: '教学楼路口',\n        description: '主教学楼前环形路口',\n        devices: [{\n          type: 'camera',\n          id: 'CAM-T-01',\n          name: '教学楼摄像头1',\n          status: 'online'\n        }, {\n          type: 'camera',\n          id: 'CAM-T-02',\n          name: '教学楼摄像头2',\n          status: 'online'\n        }, {\n          type: 'rsu',\n          id: 'RSU-T-01',\n          name: '教学楼RSU',\n          status: 'online'\n        }, {\n          type: 'edge',\n          id: 'EDG-T-01',\n          name: '教学楼边缘计算单元',\n          status: 'online'\n        }]\n      }];\n      setIntersections(intersectionsData);\n      setSelectedIntersection(intersectionsData[0]);\n      setLoading(false);\n    }, 1500);\n  }, []);\n\n  // 处理路口选择\n  const handleIntersectionSelect = intersection => {\n    setSelectedIntersection(intersection);\n  };\n\n  // 获取路口设备摘要\n  const getDeviceSummary = devices => {\n    const summary = {};\n    devices.forEach(device => {\n      const type = device.type;\n      summary[type] = (summary[type] || 0) + 1;\n    });\n    return Object.entries(summary).map(([type, count]) => {\n      const typeNames = {\n        'camera': '摄像头',\n        'rsu': 'RSU',\n        'radar': '毫米波雷达',\n        'lidar': '激光雷达',\n        'edge': '边缘计算单元'\n      };\n      return `${typeNames[type]}: ${count}`;\n    }).join(', ');\n  };\n\n  // 获取摄像头设备\n  const getCameras = devices => {\n    return devices.filter(device => device.type === 'camera');\n  };\n  return /*#__PURE__*/_jsxDEV(Spin, {\n    spinning: loading,\n    tip: \"\\u52A0\\u8F7D\\u4E2D...\",\n    children: /*#__PURE__*/_jsxDEV(PageContainer, {\n      children: [/*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"left\",\n        collapsed: leftCollapsed,\n        onCollapse: () => setLeftCollapsed(!leftCollapsed),\n        children: /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: \"\\u8DEF\\u53E3\\u4FE1\\u606F\\u5217\\u8868\",\n          bordered: false,\n          height: \"100%\",\n          children: /*#__PURE__*/_jsxDEV(List, {\n            itemLayout: \"vertical\",\n            dataSource: intersections,\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              onClick: () => handleIntersectionSelect(item),\n              style: {\n                cursor: 'pointer',\n                background: (selectedIntersection === null || selectedIntersection === void 0 ? void 0 : selectedIntersection.id) === item.id ? '#e6f7ff' : 'transparent',\n                padding: '8px',\n                borderRadius: '4px',\n                marginBottom: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                title: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '14px',\n                    fontWeight: 'bold'\n                  },\n                  children: item.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 28\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '12px'\n                  },\n                  children: item.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 34\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '12px',\n                  marginTop: '4px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"\\u8BBE\\u5907\\u914D\\u7F6E\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 26\n                  }, this), \" \", getDeviceSummary(item.devices)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '4px'\n                  },\n                  children: item.devices.map(device => /*#__PURE__*/_jsxDEV(Badge, {\n                    status: device.status === 'online' ? 'success' : 'error',\n                    text: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: '12px',\n                        marginRight: '8px'\n                      },\n                      children: device.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 29\n                    }, this),\n                    style: {\n                      display: 'inline-block',\n                      marginRight: '8px'\n                    }\n                  }, device.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContent, {\n        leftCollapsed: leftCollapsed,\n        rightCollapsed: rightCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CollapsibleSidebar, {\n        position: \"right\",\n        collapsed: rightCollapsed,\n        onCollapse: () => setRightCollapsed(!rightCollapsed),\n        children: /*#__PURE__*/_jsxDEV(InfoCard, {\n          title: `视频监控 - ${(selectedIntersection === null || selectedIntersection === void 0 ? void 0 : selectedIntersection.name) || ''}`,\n          bordered: false,\n          height: \"100%\",\n          children: selectedIntersection ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '12px',\n                fontSize: '13px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Descriptions, {\n                size: \"small\",\n                column: 1,\n                styles: {\n                  label: {\n                    fontSize: '13px'\n                  },\n                  content: {\n                    fontSize: '13px'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                  label: \"\\u8DEF\\u53E3\\u540D\\u79F0\",\n                  children: selectedIntersection.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                  label: \"\\u8DEF\\u53E3\\u63CF\\u8FF0\",\n                  children: selectedIntersection.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                  label: \"\\u6444\\u50CF\\u5934\\u6570\\u91CF\",\n                  children: getCameras(selectedIntersection.devices).length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this), getCameras(selectedIntersection.devices).slice(0, 4).map(camera => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '10px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '13px',\n                  marginBottom: '4px',\n                  fontWeight: 'bold'\n                },\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  status: camera.status === 'online' ? 'success' : 'error',\n                  text: camera.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(VideoContainer, {\n                children: /*#__PURE__*/_jsxDEV(VideoPlaceholder, {\n                  children: camera.status === 'online' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        textAlign: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        children: \"\\u89C6\\u9891\\u6D41\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 313,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: '11px',\n                          marginTop: '4px'\n                        },\n                        children: \"(\\u5B9E\\u9645\\u9879\\u76EE\\u4E2D\\u8FD9\\u91CC\\u4F1A\\u663E\\u793A\\u5B9E\\u65F6\\u89C6\\u9891)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 314,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 312,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: \"\\u6444\\u50CF\\u5934\\u79BB\\u7EBF\"\n                  }, void 0, false)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 21\n              }, this)]\n            }, camera.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 19\n            }, this)), getCameras(selectedIntersection.devices).length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                padding: '20px 0'\n              },\n              children: \"\\u8BE5\\u8DEF\\u53E3\\u6CA1\\u6709\\u914D\\u7F6E\\u6444\\u50CF\\u5934\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '20px 0'\n            },\n            children: \"\\u8BF7\\u9009\\u62E9\\u4E00\\u4E2A\\u8DEF\\u53E3\\u67E5\\u770B\\u89C6\\u9891\\u76D1\\u63A7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 216,\n    columnNumber: 5\n  }, this);\n};\n_s(RoadMonitoring, \"mRMMpGGs4EM8Z7alHD7AKBWx1lg=\");\n_c6 = RoadMonitoring;\nexport default RoadMonitoring;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"PageContainer\");\n$RefreshReg$(_c2, \"MainContent\");\n$RefreshReg$(_c3, \"InfoCard\");\n$RefreshReg$(_c4, \"VideoContainer\");\n$RefreshReg$(_c5, \"VideoPlaceholder\");\n$RefreshReg$(_c6, \"RoadMonitoring\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "List", "Descriptions", "Spin", "Badge", "Row", "Col", "styled", "CollapsibleSidebar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON>", "div", "_c", "LeftSidebar", "RightSidebar", "MainContent", "props", "leftCollapsed", "rightCollapsed", "_c2", "InfoCard", "height", "_c3", "VideoContainer", "_c4", "VideoPlaceholder", "_c5", "RoadMonitoring", "_s", "loading", "setLoading", "intersections", "setIntersections", "selectedIntersection", "setSelectedIntersection", "setLeftCollapsed", "setRightCollapsed", "setTimeout", "intersectionsData", "id", "name", "description", "devices", "type", "status", "handleIntersectionSelect", "intersection", "getDeviceSummary", "summary", "for<PERSON>ach", "device", "Object", "entries", "map", "count", "typeNames", "join", "getCameras", "filter", "spinning", "tip", "children", "position", "collapsed", "onCollapse", "title", "bordered", "itemLayout", "dataSource", "renderItem", "item", "<PERSON><PERSON>", "onClick", "style", "cursor", "background", "padding", "borderRadius", "marginBottom", "Meta", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "text", "marginRight", "display", "size", "column", "styles", "label", "content", "length", "slice", "camera", "textAlign", "_c6", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/RoadMonitoring.jsx"], "sourcesContent": ["// src/pages/RoadMonitoring.jsx\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Card, List, Descriptions, Spin, Badge, Row, Col } from 'antd';\r\nimport styled from 'styled-components';\r\nimport CollapsibleSidebar from '../components/layout/CollapsibleSidebar';\r\n\r\n// 页面布局容器\r\nconst PageContainer = styled.div`\r\n  display: flex;\r\n  height: calc(100vh - 96px); // 减去顶部导航栏高度和内边距\r\n  overflow: hidden;\r\n`;\r\n\r\n// 左侧信息栏容器\r\nconst LeftSidebar = styled.div`\r\n  width: 320px;\r\n  height: 100%;\r\n  padding: 0 8px 0 0;\r\n  border-right: 1px solid #f0f0f0;\r\n  display: flex;\r\n  flex-direction: column;\r\n`;\r\n\r\n// 右侧信息栏容器\r\nconst RightSidebar = styled.div`\r\n  width: 320px;\r\n  height: 100%;\r\n  padding: 0 0 0 8px;\r\n  border-left: 1px solid #f0f0f0;\r\n  display: flex;\r\n  flex-direction: column;\r\n`;\r\n\r\n// 主内容区域\r\nconst MainContent = styled.div`\r\n  flex: 1;\r\n  height: 100%;\r\n  overflow: hidden;\r\n  padding: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \r\n    props.leftCollapsed ? '0 8px 0 24px' : \r\n    props.rightCollapsed ? '0 24px 0 8px' : '0 8px'};\r\n  transition: all 0.3s ease;\r\n  margin: ${props => props.leftCollapsed && props.rightCollapsed ? '0 24px' : \r\n    props.leftCollapsed ? '0 8px 0 0' : \r\n    props.rightCollapsed ? '0 0 0 8px' : '0'};\r\n  position: relative;\r\n  z-index: 1;\r\n`;\r\n\r\n// 信息卡片\r\nconst InfoCard = styled(Card)`\r\n  margin-bottom: 12px;\r\n  height: ${props => props.height || 'auto'};\r\n  \r\n  .ant-card-head {\r\n    min-height: 40px;\r\n    padding: 0 12px;\r\n  }\r\n  \r\n  .ant-card-head-title {\r\n    padding: 8px 0;\r\n    font-size: 14px;\r\n  }\r\n  \r\n  .ant-card-body {\r\n    padding: 12px;\r\n    font-size: 13px;\r\n    height: calc(100% - 40px); // 减去卡片头部高度\r\n    overflow-y: auto;\r\n  }\r\n  \r\n  &:last-child {\r\n    margin-bottom: 0;\r\n  }\r\n`;\r\n\r\n// 视频容器\r\nconst VideoContainer = styled.div`\r\n  background: #000;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  margin-bottom: 10px;\r\n  \r\n  &:before {\r\n    content: \"\";\r\n    display: block;\r\n    padding-top: 56.25%; // 16:9 宽高比\r\n  }\r\n`;\r\n\r\n// 视频占位符\r\nconst VideoPlaceholder = styled.div`\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #fff;\r\n  font-size: 13px;\r\n`;\r\n\r\nconst RoadMonitoring = () => {\r\n  const [loading, setLoading] = useState(true);\r\n  const [intersections, setIntersections] = useState([]);\r\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\r\n  \r\n  // 添加侧边栏折叠状态\r\n  const [leftCollapsed, setLeftCollapsed] = useState(false);\r\n  const [rightCollapsed, setRightCollapsed] = useState(false);\r\n  \r\n  // 模拟数据加载\r\n  useEffect(() => {\r\n    // 模拟加载延迟\r\n    setTimeout(() => {\r\n      // 模拟路口数据\r\n      const intersectionsData = [\r\n        { \r\n          id: 1, \r\n          name: '东门路口', \r\n          description: '校园东门主要出入口',\r\n          devices: [\r\n            { type: 'camera', id: 'CAM-E-01', name: '东门摄像头1', status: 'online' },\r\n            { type: 'camera', id: 'CAM-E-02', name: '东门摄像头2', status: 'online' },\r\n            { type: 'rsu', id: 'RSU-E-01', name: '东门RSU', status: 'online' },\r\n            { type: 'radar', id: 'RAD-E-01', name: '东门毫米波雷达', status: 'online' }\r\n          ]\r\n        },\r\n        { \r\n          id: 2, \r\n          name: '北门路口', \r\n          description: '校园北门次要出入口',\r\n          devices: [\r\n            { type: 'camera', id: 'CAM-N-01', name: '北门摄像头1', status: 'online' },\r\n            { type: 'camera', id: 'CAM-N-02', name: '北门摄像头2', status: 'offline' },\r\n            { type: 'rsu', id: 'RSU-N-01', name: '北门RSU', status: 'online' }\r\n          ]\r\n        },\r\n        { \r\n          id: 3, \r\n          name: '图书馆路口', \r\n          description: '图书馆前T字路口',\r\n          devices: [\r\n            { type: 'camera', id: 'CAM-L-01', name: '图书馆摄像头1', status: 'online' },\r\n            { type: 'camera', id: 'CAM-L-02', name: '图书馆摄像头2', status: 'online' },\r\n            { type: 'camera', id: 'CAM-L-03', name: '图书馆摄像头3', status: 'online' },\r\n            { type: 'camera', id: 'CAM-L-04', name: '图书馆摄像头4', status: 'online' },\r\n            { type: 'rsu', id: 'RSU-L-01', name: '图书馆RSU', status: 'online' },\r\n            { type: 'lidar', id: 'LID-L-01', name: '图书馆激光雷达', status: 'online' }\r\n          ]\r\n        },\r\n        { \r\n          id: 4, \r\n          name: '食堂路口', \r\n          description: '学生食堂前十字路口',\r\n          devices: [\r\n            { type: 'camera', id: 'CAM-C-01', name: '食堂摄像头1', status: 'online' },\r\n            { type: 'camera', id: 'CAM-C-02', name: '食堂摄像头2', status: 'offline' },\r\n            { type: 'rsu', id: 'RSU-C-01', name: '食堂RSU', status: 'offline' }\r\n          ]\r\n        },\r\n        { \r\n          id: 5, \r\n          name: '教学楼路口', \r\n          description: '主教学楼前环形路口',\r\n          devices: [\r\n            { type: 'camera', id: 'CAM-T-01', name: '教学楼摄像头1', status: 'online' },\r\n            { type: 'camera', id: 'CAM-T-02', name: '教学楼摄像头2', status: 'online' },\r\n            { type: 'rsu', id: 'RSU-T-01', name: '教学楼RSU', status: 'online' },\r\n            { type: 'edge', id: 'EDG-T-01', name: '教学楼边缘计算单元', status: 'online' }\r\n          ]\r\n        },\r\n      ];\r\n      \r\n      setIntersections(intersectionsData);\r\n      setSelectedIntersection(intersectionsData[0]);\r\n      setLoading(false);\r\n    }, 1500);\r\n  }, []);\r\n  \r\n  // 处理路口选择\r\n  const handleIntersectionSelect = (intersection) => {\r\n    setSelectedIntersection(intersection);\r\n  };\r\n  \r\n  // 获取路口设备摘要\r\n  const getDeviceSummary = (devices) => {\r\n    const summary = {};\r\n    devices.forEach(device => {\r\n      const type = device.type;\r\n      summary[type] = (summary[type] || 0) + 1;\r\n    });\r\n    \r\n    return Object.entries(summary).map(([type, count]) => {\r\n      const typeNames = {\r\n        'camera': '摄像头',\r\n        'rsu': 'RSU',\r\n        'radar': '毫米波雷达',\r\n        'lidar': '激光雷达',\r\n        'edge': '边缘计算单元'\r\n      };\r\n      \r\n      return `${typeNames[type]}: ${count}`;\r\n    }).join(', ');\r\n  };\r\n  \r\n  // 获取摄像头设备\r\n  const getCameras = (devices) => {\r\n    return devices.filter(device => device.type === 'camera');\r\n  };\r\n  \r\n  return (\r\n    <Spin spinning={loading} tip=\"加载中...\">\r\n      <PageContainer>\r\n        {/* 左侧信息栏 */}\r\n        <CollapsibleSidebar \r\n          position=\"left\"\r\n          collapsed={leftCollapsed}\r\n          onCollapse={() => setLeftCollapsed(!leftCollapsed)}\r\n        >\r\n          <InfoCard title=\"路口信息列表\" bordered={false} height=\"100%\">\r\n            <List\r\n              itemLayout=\"vertical\"\r\n              dataSource={intersections}\r\n              renderItem={item => (\r\n                <List.Item\r\n                  onClick={() => handleIntersectionSelect(item)}\r\n                  style={{ \r\n                    cursor: 'pointer',\r\n                    background: selectedIntersection?.id === item.id ? '#e6f7ff' : 'transparent',\r\n                    padding: '8px',\r\n                    borderRadius: '4px',\r\n                    marginBottom: '8px'\r\n                  }}\r\n                >\r\n                  <List.Item.Meta\r\n                    title={<span style={{ fontSize: '14px', fontWeight: 'bold' }}>{item.name}</span>}\r\n                    description={<span style={{ fontSize: '12px' }}>{item.description}</span>}\r\n                  />\r\n                  <div style={{ fontSize: '12px', marginTop: '4px' }}>\r\n                    <div><strong>设备配置：</strong> {getDeviceSummary(item.devices)}</div>\r\n                    <div style={{ marginTop: '4px' }}>\r\n                      {item.devices.map(device => (\r\n                        <Badge \r\n                          key={device.id}\r\n                          status={device.status === 'online' ? 'success' : 'error'} \r\n                          text={\r\n                            <span style={{ fontSize: '12px', marginRight: '8px' }}>\r\n                              {device.name}\r\n                            </span>\r\n                          }\r\n                          style={{ display: 'inline-block', marginRight: '8px' }}\r\n                        />\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </List.Item>\r\n              )}\r\n            />\r\n          </InfoCard>\r\n        </CollapsibleSidebar>\r\n        \r\n        {/* 主内容区域 */}\r\n        <MainContent leftCollapsed={leftCollapsed} rightCollapsed={rightCollapsed}>\r\n          {/* 主要内容 */}\r\n        </MainContent>\r\n        \r\n        {/* 右侧信息栏 */}\r\n        <CollapsibleSidebar\r\n          position=\"right\"\r\n          collapsed={rightCollapsed}\r\n          onCollapse={() => setRightCollapsed(!rightCollapsed)}\r\n        >\r\n          <InfoCard \r\n            title={`视频监控 - ${selectedIntersection?.name || ''}`} \r\n            bordered={false} \r\n            height=\"100%\"\r\n          >\r\n            {selectedIntersection ? (\r\n              <>\r\n                <div style={{ marginBottom: '12px', fontSize: '13px' }}>\r\n                  <Descriptions \r\n                    size=\"small\" \r\n                    column={1}\r\n                    styles={{\r\n                      label: { fontSize: '13px' },\r\n                      content: { fontSize: '13px' }\r\n                    }}\r\n                  >\r\n                    <Descriptions.Item label=\"路口名称\">{selectedIntersection.name}</Descriptions.Item>\r\n                    <Descriptions.Item label=\"路口描述\">{selectedIntersection.description}</Descriptions.Item>\r\n                    <Descriptions.Item label=\"摄像头数量\">{getCameras(selectedIntersection.devices).length}</Descriptions.Item>\r\n                  </Descriptions>\r\n                </div>\r\n                \r\n                {/* 修改为N行1列的布局 */}\r\n                {getCameras(selectedIntersection.devices).slice(0, 4).map(camera => (\r\n                  <div key={camera.id} style={{ marginBottom: '10px' }}>\r\n                    <div style={{ fontSize: '13px', marginBottom: '4px', fontWeight: 'bold' }}>\r\n                      <Badge \r\n                        status={camera.status === 'online' ? 'success' : 'error'} \r\n                        text={camera.name}\r\n                      />\r\n                    </div>\r\n                    <VideoContainer>\r\n                      <VideoPlaceholder>\r\n                        {camera.status === 'online' ? (\r\n                          <>\r\n                            <div style={{ textAlign: 'center' }}>\r\n                              <div>视频流</div>\r\n                              <div style={{ fontSize: '11px', marginTop: '4px' }}>\r\n                                (实际项目中这里会显示实时视频)\r\n                              </div>\r\n                            </div>\r\n                          </>\r\n                        ) : (\r\n                          <>摄像头离线</>\r\n                        )}\r\n                      </VideoPlaceholder>\r\n                    </VideoContainer>\r\n                  </div>\r\n                ))}\r\n                \r\n                {getCameras(selectedIntersection.devices).length === 0 && (\r\n                  <div style={{ textAlign: 'center', padding: '20px 0' }}>\r\n                    该路口没有配置摄像头\r\n                  </div>\r\n                )}\r\n              </>\r\n            ) : (\r\n              <div style={{ textAlign: 'center', padding: '20px 0' }}>\r\n                请选择一个路口查看视频监控\r\n              </div>\r\n            )}\r\n          </InfoCard>\r\n        </CollapsibleSidebar>\r\n      </PageContainer>\r\n    </Spin>\r\n  );\r\n};\r\n\r\nexport default RoadMonitoring;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,IAAI,EAAEC,YAAY,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,QAAQ,MAAM;AACtE,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,kBAAkB,MAAM,yCAAyC;;AAExE;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,aAAa,GAAGN,MAAM,CAACO,GAAG;AAChC;AACA;AACA;AACA,CAAC;;AAED;AAAAC,EAAA,GANMF,aAAa;AAOnB,MAAMG,WAAW,GAAGT,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMG,YAAY,GAAGV,MAAM,CAACO,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMI,WAAW,GAAGX,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA,aAAaK,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACxEF,KAAK,CAACC,aAAa,GAAG,cAAc,GACpCD,KAAK,CAACE,cAAc,GAAG,cAAc,GAAG,OAAO;AACnD;AACA,YAAYF,KAAK,IAAIA,KAAK,CAACC,aAAa,IAAID,KAAK,CAACE,cAAc,GAAG,QAAQ,GACvEF,KAAK,CAACC,aAAa,GAAG,WAAW,GACjCD,KAAK,CAACE,cAAc,GAAG,WAAW,GAAG,GAAG;AAC5C;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfMJ,WAAW;AAgBjB,MAAMK,QAAQ,GAAGhB,MAAM,CAACP,IAAI,CAAC;AAC7B;AACA,YAAYmB,KAAK,IAAIA,KAAK,CAACK,MAAM,IAAI,MAAM;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GA1BMF,QAAQ;AA2Bd,MAAMG,cAAc,GAAGnB,MAAM,CAACO,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAa,GAAA,GAdMD,cAAc;AAepB,MAAME,gBAAgB,GAAGrB,MAAM,CAACO,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAXID,gBAAgB;AAatB,MAAME,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoC,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACsC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAACsB,aAAa,EAAEkB,gBAAgB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuB,cAAc,EAAEkB,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACAC,SAAS,CAAC,MAAM;IACd;IACAyC,UAAU,CAAC,MAAM;MACf;MACA,MAAMC,iBAAiB,GAAG,CACxB;QACEC,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,WAAW;QACxBC,OAAO,EAAE,CACP;UAAEC,IAAI,EAAE,QAAQ;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,QAAQ;UAAEI,MAAM,EAAE;QAAS,CAAC,EACpE;UAAED,IAAI,EAAE,QAAQ;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,QAAQ;UAAEI,MAAM,EAAE;QAAS,CAAC,EACpE;UAAED,IAAI,EAAE,KAAK;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,OAAO;UAAEI,MAAM,EAAE;QAAS,CAAC,EAChE;UAAED,IAAI,EAAE,OAAO;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,SAAS;UAAEI,MAAM,EAAE;QAAS,CAAC;MAExE,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,WAAW;QACxBC,OAAO,EAAE,CACP;UAAEC,IAAI,EAAE,QAAQ;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,QAAQ;UAAEI,MAAM,EAAE;QAAS,CAAC,EACpE;UAAED,IAAI,EAAE,QAAQ;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,QAAQ;UAAEI,MAAM,EAAE;QAAU,CAAC,EACrE;UAAED,IAAI,EAAE,KAAK;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,OAAO;UAAEI,MAAM,EAAE;QAAS,CAAC;MAEpE,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,OAAO;QACbC,WAAW,EAAE,UAAU;QACvBC,OAAO,EAAE,CACP;UAAEC,IAAI,EAAE,QAAQ;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,SAAS;UAAEI,MAAM,EAAE;QAAS,CAAC,EACrE;UAAED,IAAI,EAAE,QAAQ;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,SAAS;UAAEI,MAAM,EAAE;QAAS,CAAC,EACrE;UAAED,IAAI,EAAE,QAAQ;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,SAAS;UAAEI,MAAM,EAAE;QAAS,CAAC,EACrE;UAAED,IAAI,EAAE,QAAQ;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,SAAS;UAAEI,MAAM,EAAE;QAAS,CAAC,EACrE;UAAED,IAAI,EAAE,KAAK;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,QAAQ;UAAEI,MAAM,EAAE;QAAS,CAAC,EACjE;UAAED,IAAI,EAAE,OAAO;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,SAAS;UAAEI,MAAM,EAAE;QAAS,CAAC;MAExE,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE,WAAW;QACxBC,OAAO,EAAE,CACP;UAAEC,IAAI,EAAE,QAAQ;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,QAAQ;UAAEI,MAAM,EAAE;QAAS,CAAC,EACpE;UAAED,IAAI,EAAE,QAAQ;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,QAAQ;UAAEI,MAAM,EAAE;QAAU,CAAC,EACrE;UAAED,IAAI,EAAE,KAAK;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,OAAO;UAAEI,MAAM,EAAE;QAAU,CAAC;MAErE,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,OAAO;QACbC,WAAW,EAAE,WAAW;QACxBC,OAAO,EAAE,CACP;UAAEC,IAAI,EAAE,QAAQ;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,SAAS;UAAEI,MAAM,EAAE;QAAS,CAAC,EACrE;UAAED,IAAI,EAAE,QAAQ;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,SAAS;UAAEI,MAAM,EAAE;QAAS,CAAC,EACrE;UAAED,IAAI,EAAE,KAAK;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,QAAQ;UAAEI,MAAM,EAAE;QAAS,CAAC,EACjE;UAAED,IAAI,EAAE,MAAM;UAAEJ,EAAE,EAAE,UAAU;UAAEC,IAAI,EAAE,WAAW;UAAEI,MAAM,EAAE;QAAS,CAAC;MAEzE,CAAC,CACF;MAEDZ,gBAAgB,CAACM,iBAAiB,CAAC;MACnCJ,uBAAuB,CAACI,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC7CR,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMe,wBAAwB,GAAIC,YAAY,IAAK;IACjDZ,uBAAuB,CAACY,YAAY,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIL,OAAO,IAAK;IACpC,MAAMM,OAAO,GAAG,CAAC,CAAC;IAClBN,OAAO,CAACO,OAAO,CAACC,MAAM,IAAI;MACxB,MAAMP,IAAI,GAAGO,MAAM,CAACP,IAAI;MACxBK,OAAO,CAACL,IAAI,CAAC,GAAG,CAACK,OAAO,CAACL,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1C,CAAC,CAAC;IAEF,OAAOQ,MAAM,CAACC,OAAO,CAACJ,OAAO,CAAC,CAACK,GAAG,CAAC,CAAC,CAACV,IAAI,EAAEW,KAAK,CAAC,KAAK;MACpD,MAAMC,SAAS,GAAG;QAChB,QAAQ,EAAE,KAAK;QACf,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE,OAAO;QAChB,OAAO,EAAE,MAAM;QACf,MAAM,EAAE;MACV,CAAC;MAED,OAAO,GAAGA,SAAS,CAACZ,IAAI,CAAC,KAAKW,KAAK,EAAE;IACvC,CAAC,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;EACf,CAAC;;EAED;EACA,MAAMC,UAAU,GAAIf,OAAO,IAAK;IAC9B,OAAOA,OAAO,CAACgB,MAAM,CAACR,MAAM,IAAIA,MAAM,CAACP,IAAI,KAAK,QAAQ,CAAC;EAC3D,CAAC;EAED,oBACEpC,OAAA,CAACP,IAAI;IAAC2D,QAAQ,EAAE9B,OAAQ;IAAC+B,GAAG,EAAC,uBAAQ;IAAAC,QAAA,eACnCtD,OAAA,CAACG,aAAa;MAAAmD,QAAA,gBAEZtD,OAAA,CAACF,kBAAkB;QACjByD,QAAQ,EAAC,MAAM;QACfC,SAAS,EAAE9C,aAAc;QACzB+C,UAAU,EAAEA,CAAA,KAAM7B,gBAAgB,CAAC,CAAClB,aAAa,CAAE;QAAA4C,QAAA,eAEnDtD,OAAA,CAACa,QAAQ;UAAC6C,KAAK,EAAC,sCAAQ;UAACC,QAAQ,EAAE,KAAM;UAAC7C,MAAM,EAAC,MAAM;UAAAwC,QAAA,eACrDtD,OAAA,CAACT,IAAI;YACHqE,UAAU,EAAC,UAAU;YACrBC,UAAU,EAAErC,aAAc;YAC1BsC,UAAU,EAAEC,IAAI,iBACd/D,OAAA,CAACT,IAAI,CAACyE,IAAI;cACRC,OAAO,EAAEA,CAAA,KAAM3B,wBAAwB,CAACyB,IAAI,CAAE;cAC9CG,KAAK,EAAE;gBACLC,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,CAAA1C,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEM,EAAE,MAAK+B,IAAI,CAAC/B,EAAE,GAAG,SAAS,GAAG,aAAa;gBAC5EqC,OAAO,EAAE,KAAK;gBACdC,YAAY,EAAE,KAAK;gBACnBC,YAAY,EAAE;cAChB,CAAE;cAAAjB,QAAA,gBAEFtD,OAAA,CAACT,IAAI,CAACyE,IAAI,CAACQ,IAAI;gBACbd,KAAK,eAAE1D,OAAA;kBAAMkE,KAAK,EAAE;oBAAEO,QAAQ,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAO,CAAE;kBAAApB,QAAA,EAAES,IAAI,CAAC9B;gBAAI;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAE;gBACjF5C,WAAW,eAAElC,OAAA;kBAAMkE,KAAK,EAAE;oBAAEO,QAAQ,EAAE;kBAAO,CAAE;kBAAAnB,QAAA,EAAES,IAAI,CAAC7B;gBAAW;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC,eACF9E,OAAA;gBAAKkE,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEM,SAAS,EAAE;gBAAM,CAAE;gBAAAzB,QAAA,gBACjDtD,OAAA;kBAAAsD,QAAA,gBAAKtD,OAAA;oBAAAsD,QAAA,EAAQ;kBAAK;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACtC,gBAAgB,CAACuB,IAAI,CAAC5B,OAAO,CAAC;gBAAA;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClE9E,OAAA;kBAAKkE,KAAK,EAAE;oBAAEa,SAAS,EAAE;kBAAM,CAAE;kBAAAzB,QAAA,EAC9BS,IAAI,CAAC5B,OAAO,CAACW,GAAG,CAACH,MAAM,iBACtB3C,OAAA,CAACN,KAAK;oBAEJ2C,MAAM,EAAEM,MAAM,CAACN,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;oBACzD2C,IAAI,eACFhF,OAAA;sBAAMkE,KAAK,EAAE;wBAAEO,QAAQ,EAAE,MAAM;wBAAEQ,WAAW,EAAE;sBAAM,CAAE;sBAAA3B,QAAA,EACnDX,MAAM,CAACV;oBAAI;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CACP;oBACDZ,KAAK,EAAE;sBAAEgB,OAAO,EAAE,cAAc;sBAAED,WAAW,EAAE;oBAAM;kBAAE,GAPlDtC,MAAM,CAACX,EAAE;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQf,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGrB9E,OAAA,CAACQ,WAAW;QAACE,aAAa,EAAEA,aAAc;QAACC,cAAc,EAAEA;MAAe;QAAAgE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7D,CAAC,eAGd9E,OAAA,CAACF,kBAAkB;QACjByD,QAAQ,EAAC,OAAO;QAChBC,SAAS,EAAE7C,cAAe;QAC1B8C,UAAU,EAAEA,CAAA,KAAM5B,iBAAiB,CAAC,CAAClB,cAAc,CAAE;QAAA2C,QAAA,eAErDtD,OAAA,CAACa,QAAQ;UACP6C,KAAK,EAAE,UAAU,CAAAhC,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEO,IAAI,KAAI,EAAE,EAAG;UACpD0B,QAAQ,EAAE,KAAM;UAChB7C,MAAM,EAAC,MAAM;UAAAwC,QAAA,EAEZ5B,oBAAoB,gBACnB1B,OAAA,CAAAE,SAAA;YAAAoD,QAAA,gBACEtD,OAAA;cAAKkE,KAAK,EAAE;gBAAEK,YAAY,EAAE,MAAM;gBAAEE,QAAQ,EAAE;cAAO,CAAE;cAAAnB,QAAA,eACrDtD,OAAA,CAACR,YAAY;gBACX2F,IAAI,EAAC,OAAO;gBACZC,MAAM,EAAE,CAAE;gBACVC,MAAM,EAAE;kBACNC,KAAK,EAAE;oBAAEb,QAAQ,EAAE;kBAAO,CAAC;kBAC3Bc,OAAO,EAAE;oBAAEd,QAAQ,EAAE;kBAAO;gBAC9B,CAAE;gBAAAnB,QAAA,gBAEFtD,OAAA,CAACR,YAAY,CAACwE,IAAI;kBAACsB,KAAK,EAAC,0BAAM;kBAAAhC,QAAA,EAAE5B,oBAAoB,CAACO;gBAAI;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eAC/E9E,OAAA,CAACR,YAAY,CAACwE,IAAI;kBAACsB,KAAK,EAAC,0BAAM;kBAAAhC,QAAA,EAAE5B,oBAAoB,CAACQ;gBAAW;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC,eACtF9E,OAAA,CAACR,YAAY,CAACwE,IAAI;kBAACsB,KAAK,EAAC,gCAAO;kBAAAhC,QAAA,EAAEJ,UAAU,CAACxB,oBAAoB,CAACS,OAAO,CAAC,CAACqD;gBAAM;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,EAGL5B,UAAU,CAACxB,oBAAoB,CAACS,OAAO,CAAC,CAACsD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3C,GAAG,CAAC4C,MAAM,iBAC9D1F,OAAA;cAAqBkE,KAAK,EAAE;gBAAEK,YAAY,EAAE;cAAO,CAAE;cAAAjB,QAAA,gBACnDtD,OAAA;gBAAKkE,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEF,YAAY,EAAE,KAAK;kBAAEG,UAAU,EAAE;gBAAO,CAAE;gBAAApB,QAAA,eACxEtD,OAAA,CAACN,KAAK;kBACJ2C,MAAM,EAAEqD,MAAM,CAACrD,MAAM,KAAK,QAAQ,GAAG,SAAS,GAAG,OAAQ;kBACzD2C,IAAI,EAAEU,MAAM,CAACzD;gBAAK;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9E,OAAA,CAACgB,cAAc;gBAAAsC,QAAA,eACbtD,OAAA,CAACkB,gBAAgB;kBAAAoC,QAAA,EACdoC,MAAM,CAACrD,MAAM,KAAK,QAAQ,gBACzBrC,OAAA,CAAAE,SAAA;oBAAAoD,QAAA,eACEtD,OAAA;sBAAKkE,KAAK,EAAE;wBAAEyB,SAAS,EAAE;sBAAS,CAAE;sBAAArC,QAAA,gBAClCtD,OAAA;wBAAAsD,QAAA,EAAK;sBAAG;wBAAAqB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACd9E,OAAA;wBAAKkE,KAAK,EAAE;0BAAEO,QAAQ,EAAE,MAAM;0BAAEM,SAAS,EAAE;wBAAM,CAAE;wBAAAzB,QAAA,EAAC;sBAEpD;wBAAAqB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,gBACN,CAAC,gBAEH9E,OAAA,CAAAE,SAAA;oBAAAoD,QAAA,EAAE;kBAAK,gBAAE;gBACV;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACe;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA,GAtBTY,MAAM,CAAC1D,EAAE;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBd,CACN,CAAC,EAED5B,UAAU,CAACxB,oBAAoB,CAACS,OAAO,CAAC,CAACqD,MAAM,KAAK,CAAC,iBACpDxF,OAAA;cAAKkE,KAAK,EAAE;gBAAEyB,SAAS,EAAE,QAAQ;gBAAEtB,OAAO,EAAE;cAAS,CAAE;cAAAf,QAAA,EAAC;YAExD;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA,eACD,CAAC,gBAEH9E,OAAA;YAAKkE,KAAK,EAAE;cAAEyB,SAAS,EAAE,QAAQ;cAAEtB,OAAO,EAAE;YAAS,CAAE;YAAAf,QAAA,EAAC;UAExD;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEX,CAAC;AAACzD,EAAA,CA7OID,cAAc;AAAAwE,GAAA,GAAdxE,cAAc;AA+OpB,eAAeA,cAAc;AAAC,IAAAf,EAAA,EAAAO,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAyE,GAAA;AAAAC,YAAA,CAAAxF,EAAA;AAAAwF,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}