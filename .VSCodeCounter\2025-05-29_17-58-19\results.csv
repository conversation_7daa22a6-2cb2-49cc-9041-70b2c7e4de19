"filename", "language", "Markdown", "JavaScript", "Batch", "JSON", "CSS", "JavaScript JSX", "HTML", "JSON with Comments", "XML", "Properties", "comment", "blank", "total"
"g:\AI_tools\cursor\projects\education web\.env", "Properties", 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 4
"g:\AI_tools\cursor\projects\education web\.vs\VSWorkspaceState.json", "JSON", 0, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 6
"g:\AI_tools\cursor\projects\education web\HOW_TO_RUN.md", "Markdown", 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 68
"g:\AI_tools\cursor\projects\education web\README.md", "Markdown", 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 150
"g:\AI_tools\cursor\projects\education web\build\asset-manifest.json", "JSON", 0, 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 13
"g:\AI_tools\cursor\projects\education web\build\images\camera.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 1, 10
"g:\AI_tools\cursor\projects\education web\build\images\compass.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1
"g:\AI_tools\cursor\projects\education web\build\images\edge_computing.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 1, 5
"g:\AI_tools\cursor\projects\education web\build\images\lidar.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 1, 6
"g:\AI_tools\cursor\projects\education web\build\images\mmwave_radar.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 1, 7
"g:\AI_tools\cursor\projects\education web\build\images\obu.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 1, 10
"g:\AI_tools\cursor\projects\education web\build\images\rsu.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 1, 9
"g:\AI_tools\cursor\projects\education web\build\index.html", "HTML", 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1
"g:\AI_tools\cursor\projects\education web\build\manifest.json", "JSON", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 15
"g:\AI_tools\cursor\projects\education web\build\mqtt-test.html", "HTML", 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 9, 72
"g:\AI_tools\cursor\projects\education web\build\static\css\main.beae109d.css", "CSS", 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 2
"g:\AI_tools\cursor\projects\education web\build\static\js\main.92f83330.js", "JavaScript", 0, 422, 0, 0, 0, 0, 0, 0, 0, 0, 69, 32, 523
"g:\AI_tools\cursor\projects\education web\docs\requirements.md", "Markdown", 140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 166
"g:\AI_tools\cursor\projects\education web\frontend-server.js", "JavaScript", 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 17
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\README.md", "Markdown", 38, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 71
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\package-lock.json", "JSON", 0, 0, 0, 20332, 0, 0, 0, 0, 0, 0, 0, 1, 20333
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\package.json", "JSON", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 1, 55
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\public\index.html", "HTML", 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 23, 1, 44
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\public\manifest.json", "JSON", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 1, 26
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\App.css", "CSS", 0, 0, 0, 0, 33, 0, 0, 0, 0, 0, 0, 6, 39
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\App.js", "JavaScript", 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 26
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\App.test.js", "JavaScript", 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 9
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\index.css", "CSS", 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 2, 14
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\index.js", "JavaScript", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 18
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\logo.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\reportWebVitals.js", "JavaScript", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 14
"g:\AI_tools\cursor\projects\education web\intelligent-traffic-platform\src\setupTests.js", "JavaScript", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"g:\AI_tools\cursor\projects\education web\jsconfig.json", "JSON with Comments", 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 8
"g:\AI_tools\cursor\projects\education web\package-lock.json", "JSON", 0, 0, 0, 21420, 0, 0, 0, 0, 0, 0, 0, 1, 21421
"g:\AI_tools\cursor\projects\education web\package.json", "JSON", 0, 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 1, 69
"g:\AI_tools\cursor\projects\education web\public\images\camera.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 1, 10
"g:\AI_tools\cursor\projects\education web\public\images\compass.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1
"g:\AI_tools\cursor\projects\education web\public\images\edge_computing.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 1, 5
"g:\AI_tools\cursor\projects\education web\public\images\lidar.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 1, 6
"g:\AI_tools\cursor\projects\education web\public\images\mmwave_radar.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 6, 0, 0, 1, 7
"g:\AI_tools\cursor\projects\education web\public\images\obu.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 0, 1, 10
"g:\AI_tools\cursor\projects\education web\public\images\rsu.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 8, 0, 0, 1, 9
"g:\AI_tools\cursor\projects\education web\public\index.html", "HTML", 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 20
"g:\AI_tools\cursor\projects\education web\public\manifest.json", "JSON", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 15
"g:\AI_tools\cursor\projects\education web\public\mqtt-test.html", "HTML", 0, 0, 0, 0, 0, 0, 63, 0, 0, 0, 0, 9, 72
"g:\AI_tools\cursor\projects\education web\server\models\Device.js", "JavaScript", 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 36
"g:\AI_tools\cursor\projects\education web\server\models\Event.js", "JavaScript", 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 28
"g:\AI_tools\cursor\projects\education web\server\models\Intersection.js", "JavaScript", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 24
"g:\AI_tools\cursor\projects\education web\server\models\User.js", "JavaScript", 0, 52, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6, 61
"g:\AI_tools\cursor\projects\education web\server\models\Vehicle.js", "JavaScript", 0, 36, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 39
"g:\AI_tools\cursor\projects\education web\server\mqtt-ws-bridge.js", "JavaScript", 0, 243, 0, 0, 0, 0, 0, 0, 0, 0, 41, 44, 328
"g:\AI_tools\cursor\projects\education web\server\routes\auth.js", "JavaScript", 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 6, 16, 89
"g:\AI_tools\cursor\projects\education web\server\routes\coordinates.js", "JavaScript", 0, 63, 0, 0, 0, 0, 0, 0, 0, 0, 11, 11, 85
"g:\AI_tools\cursor\projects\education web\server\routes\devices.js", "JavaScript", 0, 133, 0, 0, 0, 0, 0, 0, 0, 0, 9, 21, 163
"g:\AI_tools\cursor\projects\education web\server\routes\users.js", "JavaScript", 0, 174, 0, 0, 0, 0, 0, 0, 0, 0, 26, 38, 238
"g:\AI_tools\cursor\projects\education web\server\routes\vehicles.js", "JavaScript", 0, 187, 0, 0, 0, 0, 0, 0, 0, 0, 24, 39, 250
"g:\AI_tools\cursor\projects\education web\server\server.js", "JavaScript", 0, 143, 0, 0, 0, 0, 0, 0, 0, 0, 14, 27, 184
"g:\AI_tools\cursor\projects\education web\server\start-stream-server.js", "JavaScript", 0, 26, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 35
"g:\AI_tools\cursor\projects\education web\server\stream-server.js", "JavaScript", 0, 230, 0, 0, 0, 0, 0, 0, 0, 0, 28, 29, 287
"g:\AI_tools\cursor\projects\education web\server\streamServer.js", "JavaScript", 0, 126, 0, 0, 0, 0, 0, 0, 0, 0, 12, 21, 159
"g:\AI_tools\cursor\projects\education web\src\App.css", "CSS", 0, 0, 0, 0, 37, 0, 0, 0, 0, 0, 0, 7, 44
"g:\AI_tools\cursor\projects\education web\src\App.js", "JavaScript", 0, 57, 0, 0, 0, 0, 0, 0, 0, 0, 6, 7, 70
"g:\AI_tools\cursor\projects\education web\src\assets\css\main.css", "CSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"g:\AI_tools\cursor\projects\education web\src\components\CampusModel.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 2718, 0, 0, 0, 0, 603, 541, 3862
"g:\AI_tools\cursor\projects\education web\src\components\CoordinateSettings.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 124, 0, 0, 0, 0, 6, 20, 150
"g:\AI_tools\cursor\projects\education web\src\components\DevicePopoverContent.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 150, 0, 0, 0, 0, 14, 6, 170
"g:\AI_tools\cursor\projects\education web\src\components\MainLayout.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 125, 0, 0, 0, 0, 1, 11, 137
"g:\AI_tools\cursor\projects\education web\src\components\VideoPlayer.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 325, 0, 0, 0, 0, 0, 24, 349
"g:\AI_tools\cursor\projects\education web\src\components\layout\CollapsibleSidebar.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 92, 0, 0, 0, 0, 3, 6, 101
"g:\AI_tools\cursor\projects\education web\src\components\layout\MainLayout.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 302, 0, 0, 0, 0, 14, 20, 336
"g:\AI_tools\cursor\projects\education web\src\config\coordinates.json", "JSON", 0, 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 14
"g:\AI_tools\cursor\projects\education web\src\data\devices.json", "JSON", 0, 0, 0, 258, 0, 0, 0, 0, 0, 0, 0, 0, 258
"g:\AI_tools\cursor\projects\education web\src\data\intersections.json", "JSON", 0, 0, 0, 87, 0, 0, 0, 0, 0, 0, 0, 0, 87
"g:\AI_tools\cursor\projects\education web\src\data\users.json", "JSON", 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 25
"g:\AI_tools\cursor\projects\education web\src\data\vehicles.json", "JSON", 0, 0, 0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 40
"g:\AI_tools\cursor\projects\education web\src\index.css", "CSS", 0, 0, 0, 0, 12, 0, 0, 0, 0, 0, 0, 1, 13
"g:\AI_tools\cursor\projects\education web\src\index.js", "JavaScript", 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 11
"g:\AI_tools\cursor\projects\education web\src\js\road-monitor.js", "JavaScript", 0, 67, 0, 0, 0, 0, 0, 0, 0, 0, 7, 7, 81
"g:\AI_tools\cursor\projects\education web\src\main.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"g:\AI_tools\cursor\projects\education web\src\models\User.js", "JavaScript", 0, 46, 0, 0, 0, 0, 0, 0, 0, 0, 3, 6, 55
"g:\AI_tools\cursor\projects\education web\src\pages\DeviceManagement.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 413, 0, 0, 0, 0, 40, 41, 494
"g:\AI_tools\cursor\projects\education web\src\pages\DeviceStatus.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 688, 0, 0, 0, 0, 55, 60, 803
"g:\AI_tools\cursor\projects\education web\src\pages\Login.css", "CSS", 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 1, 9, 69
"g:\AI_tools\cursor\projects\education web\src\pages\Login.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 253, 0, 0, 0, 0, 65, 43, 361
"g:\AI_tools\cursor\projects\education web\src\pages\MonitoringPage.css", "CSS", 0, 0, 0, 0, 123, 0, 0, 0, 0, 0, 0, 23, 146
"g:\AI_tools\cursor\projects\education web\src\pages\MonitoringPage.js", "JavaScript", 0, 144, 0, 0, 0, 0, 0, 0, 0, 0, 10, 20, 174
"g:\AI_tools\cursor\projects\education web\src\pages\RealTimeTraffic.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 989, 0, 0, 0, 0, 181, 142, 1312
"g:\AI_tools\cursor\projects\education web\src\pages\RoadMonitoring.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 319, 0, 0, 0, 0, 57, 36, 412
"g:\AI_tools\cursor\projects\education web\src\pages\SystemManagement.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 651, 0, 0, 0, 0, 45, 64, 760
"g:\AI_tools\cursor\projects\education web\src\pages\UserManagement.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 52, 0, 0, 0, 0, 0, 8, 60
"g:\AI_tools\cursor\projects\education web\src\pages\VehicleManagement.jsx", "JavaScript JSX", 0, 0, 0, 0, 0, 246, 0, 0, 0, 0, 24, 25, 295
"g:\AI_tools\cursor\projects\education web\src\routes\auth.js", "JavaScript", 0, 83, 0, 0, 0, 0, 0, 0, 0, 0, 8, 13, 104
"g:\AI_tools\cursor\projects\education web\src\routes\vehicles.js", "JavaScript", 0, 138, 0, 0, 0, 0, 0, 0, 0, 0, 22, 33, 193
"g:\AI_tools\cursor\projects\education web\src\scripts\migratePasswords.js", "JavaScript", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 5, 7, 32
"g:\AI_tools\cursor\projects\education web\src\server.js", "JavaScript", 0, 789, 0, 0, 0, 0, 0, 0, 0, 0, 95, 101, 985
"g:\AI_tools\cursor\projects\education web\src\server\config\db.js", "JavaScript", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 17
"g:\AI_tools\cursor\projects\education web\src\server\index.js", "JavaScript", 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 4, 6, 28
"g:\AI_tools\cursor\projects\education web\src\server\middleware\admin.js", "JavaScript", 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 7
"g:\AI_tools\cursor\projects\education web\src\server\middleware\auth.js", "JavaScript", 0, 55, 0, 0, 0, 0, 0, 0, 0, 0, 7, 13, 75
"g:\AI_tools\cursor\projects\education web\src\server\models\User.js", "JavaScript", 0, 86, 0, 0, 0, 0, 0, 0, 0, 0, 6, 9, 101
"g:\AI_tools\cursor\projects\education web\src\server\models\Vehicle.js", "JavaScript", 0, 30, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3, 34
"g:\AI_tools\cursor\projects\education web\src\server\reset-admin.js", "JavaScript", 0, 78, 0, 0, 0, 0, 0, 0, 0, 0, 11, 16, 105
"g:\AI_tools\cursor\projects\education web\src\server\routes\auth.js", "JavaScript", 0, 121, 0, 0, 0, 0, 0, 0, 0, 0, 12, 19, 152
"g:\AI_tools\cursor\projects\education web\src\server\routes\users.js", "JavaScript", 0, 103, 0, 0, 0, 0, 0, 0, 0, 0, 15, 19, 137
"g:\AI_tools\cursor\projects\education web\src\server\routes\vehicles.js", "JavaScript", 0, 108, 0, 0, 0, 0, 0, 0, 0, 0, 17, 24, 149
"g:\AI_tools\cursor\projects\education web\src\server\rtspServer.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"g:\AI_tools\cursor\projects\education web\src\server\start.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"g:\AI_tools\cursor\projects\education web\src\server\webrtcServer.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"g:\AI_tools\cursor\projects\education web\src\services\auth.js", "JavaScript", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 6, 7, 48
"g:\AI_tools\cursor\projects\education web\src\setupProxy.js", "JavaScript", 0, 46, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 51
"g:\AI_tools\cursor\projects\education web\src\utils\CoordinateConverter.js", "JavaScript", 0, 102, 0, 0, 0, 0, 0, 0, 0, 0, 26, 23, 151
"g:\AI_tools\cursor\projects\education web\src\utils\axios.js", "JavaScript", 0, 41, 0, 0, 0, 0, 0, 0, 0, 0, 6, 4, 51
"g:\AI_tools\cursor\projects\education web\start-all.bat", "Batch", 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 5, 25
"g:\AI_tools\cursor\projects\education web\start-simple.bat", "Batch", 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 1, 6, 45
"g:\AI_tools\cursor\projects\education web\start-stream-server.js", "JavaScript", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1
"g:\AI_tools\cursor\projects\education web\start.bat", "Batch", 0, 0, 64, 0, 0, 0, 0, 0, 0, 0, 9, 9, 82
"g:\AI_tools\cursor\projects\education web\start_en.bat", "Batch", 0, 0, 142, 0, 0, 0, 0, 0, 0, 0, 11, 14, 167
"g:\AI_tools\cursor\projects\education web\vue.config.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"g:\AI_tools\cursor\projects\education web\使用说明书.md", "Markdown", 494, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 106, 600
"g:\AI_tools\cursor\projects\education web\消息协议文档说明.md", "Markdown", 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 53, 163
"g:\AI_tools\cursor\projects\education web\软件著作权\软件著作权的样例.md", "Markdown", 313, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 376, 689
"Total", "-", 1272, 4245, 264, 42372, 277, 7447, 167, 8, 85, 4, 1681, 2465, 60287