# 智能网联产学研云平台需求文档

## 项目概述
智能网联产学研云平台是一个基于 React + Three.js 的前端应用，旨在提供校园智能交通系统的可视化监控与管理。该平台通过数字孪生技术实现校园交通、设备的实时监控和智能分析，为校园交通管理提供全面的解决方案。

## 技术栈
- **前端**：React + Three.js + Ant Design
- **后端**：Node.js + Express
- **数据库**：MongoDB
- **通信**：WebSocket、RESTful API
- **3D渲染**：Three.js、WebGL

## 功能模块

### 1. 登录页面
- **设计风格**：现代简约，专业科技感
- **登录表单**：
  - 用户名/密码输入框
  - 记住我选项
  - 忘记密码链接
  - 登录按钮
- **错误处理**：
  - 表单验证
  - 登录失败提示
- **默认账号**：
  - 用户名：admin
  - 密码：admin123

### 2. 实时交通
- **中央显示区域**：
  - 通过 Three.js 加载校园的 3D 模型文件
  - 显示校园的数字孪生系统
  - 在对应点位显示路侧设备图标
  - 显示车辆实时位置和运行状态

- **左侧信息栏**：
  - **车辆和设备总数信息栏**：
    - 车辆总数、设备总数
    - 在线车辆数、离线车辆数
    - 在线设备数、离线设备数
  - **实时信息列表栏**：
    - 显示车辆 OBU 上传的各类预警及提示事件信息
    - 列表中显示事件的名称和时间
  - **实时事件统计栏**：
    - 使用圆饼图展示不同事件的统计信息

- **右侧信息栏**：
  - **车辆列表栏**：
    - 显示车辆的车牌和在线状态
  - **车辆详细信息栏**：
    - 显示选中车辆的车牌、经纬度（7位小数）、速度、航向角等信息

- **交互功能**：
  - 点击路侧设备图标显示设备信息
  - 点击摄像头图标显示视频小窗口
  - 点击红绿灯图标显示实时灯色和倒计时信息
  - 点击车辆列表中的车辆显示详细信息

### 3. 设备状态
- **中央显示区域**：
  - 与实时交通页面相同的 3D 校园数字孪生系统

- **左侧信息栏**：
  - **设备总数统计**：
    - 设备总数
    - 在线/离线设备数量
    - 正常/警告/错误设备数量
  - **设备类型分布**：
    - 使用饼图显示不同类型设备的分布情况
  - **设备状态分布**：
    - 使用饼图显示不同状态设备的分布情况

- **右侧信息栏**：
  - **设备列表**：
    - 显示所有设备的名称、状态和健康度
  - **设备详细信息**：
    - 显示选中设备的详细信息
    - 包括名称、类型、状态、健康状态、位置、IP地址等
    - 对于在线设备，显示 CPU、内存、磁盘使用率

### 4. 路网监控
- **中央显示区域**：
  - 与实时交通页面相同的 3D 校园数字孪生系统

- **左侧信息栏**：
  - **路口信息列表**：
    - 显示路口的名称
    - 显示路口描述
    - 显示路口的设备配置

- **右侧信息栏**：
  - **视频监控**：
    - 显示选中路口的所有摄像头的实时视频
    - 最多显示 4 个摄像头的视频
    - 采用 N 行 1 列的方式进行排列
    - 默认显示第一个路口的视频信息

### 5. 系统管理
- **用户管理**：
  - 添加、编辑、删除用户
  - 设置用户角色和权限

- **角色权限管理**：
  - 定义不同角色的权限
  - 分配角色给用户

- **系统参数配置**：
  - 设置系统运行参数
  - 配置数据刷新频率等

- **日志管理**：
  - 查看系统操作日志
  - 查看用户登录日志

- **数据备份与恢复**：
  - 备份系统数据
  - 恢复系统数据

## 用户角色与权限
- **管理员**：拥有全部功能的访问权限，可进行系统配置和用户管理
- **监控人员**：可查看所有监控数据，但无法修改系统配置
- **普通用户**：仅可查看基本信息和公开数据，无操作权限
- **设备维护人员**：专注于设备状态监控和故障处理

## 界面设计要求
- **整体风格**：现代简约，专业科技感
- **配色方案**：主色调蓝色（#1890ff），辅助色绿色（#52c41a）、红色（#f5222d）
- **字体**：系统默认无衬线字体，标题 16-24px，正文 14px
- **布局**：
  - 顶部导航栏
  - 左侧信息栏（宽度 320px）
  - 中央显示区域
  - 右侧信息栏（宽度 320px）
- **响应式设计**：适配不同分辨率的显示设备

## 性能要求
- 页面首次加载时间 < 3 秒（宽带环境）
- 3D 场景渲染帧率 > 30fps
- 数据刷新频率：实时数据 1-5 秒，统计数据 30 秒
- 支持同一局域网内多设备访问

## 部署与访问
- **开发环境**：
  - 使用 `npm run start:lan` 启动支持局域网访问的开发服务器
  - 通过 `http://服务器IP:3000` 访问

- **生产环境**：
  - 使用 `npm run build` 构建生产版本
  - 使用 Express 服务器提供静态文件
  - 通过 `http://服务器IP:3000` 访问

## 后续优化建议
- 引入 AI 分析模块，提供交通预测和异常检测
- 增加移动端 APP 支持
- 开发 API 接口，支持第三方系统集成
- 优化 3D 模型加载性能，支持分级加载
- 增加国际化支持
- 实现更精细的权限控制
- 添加更多数据可视化图表类型

## 安全要求
- 用户认证：支持多因素认证
- 数据传输：全程 HTTPS 加密
- 权限控制：基于 RBAC 模型
- 日志审计：记录所有关键操作
- 防攻击：实现 CSRF 防护、XSS 过滤、SQL 注入防护 