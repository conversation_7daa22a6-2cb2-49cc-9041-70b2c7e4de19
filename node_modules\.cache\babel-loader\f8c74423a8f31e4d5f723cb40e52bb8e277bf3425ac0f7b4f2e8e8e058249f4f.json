{"ast": null, "code": "import * as imageHelper from '../helper/image.js';\nimport { extend, retrieve2, retrieve3, reduce } from '../../core/util.js';\nimport { getLineHeight, getWidth, parsePercent } from '../../contain/text.js';\nvar STYLE_REG = /\\{([a-zA-Z0-9_]+)\\|([^}]*)\\}/g;\nexport function truncateText(text, containerWidth, font, ellipsis, options) {\n  var out = {};\n  truncateText2(out, text, containerWidth, font, ellipsis, options);\n  return out.text;\n}\nfunction truncateText2(out, text, containerWidth, font, ellipsis, options) {\n  if (!containerWidth) {\n    out.text = '';\n    out.isTruncated = false;\n    return;\n  }\n  var textLines = (text + '').split('\\n');\n  options = prepareTruncateOptions(containerWidth, font, ellipsis, options);\n  var isTruncated = false;\n  var truncateOut = {};\n  for (var i = 0, len = textLines.length; i < len; i++) {\n    truncateSingleLine(truncateOut, textLines[i], options);\n    textLines[i] = truncateOut.textLine;\n    isTruncated = isTruncated || truncateOut.isTruncated;\n  }\n  out.text = textLines.join('\\n');\n  out.isTruncated = isTruncated;\n}\nfunction prepareTruncateOptions(containerWidth, font, ellipsis, options) {\n  options = options || {};\n  var preparedOpts = extend({}, options);\n  preparedOpts.font = font;\n  ellipsis = retrieve2(ellipsis, '...');\n  preparedOpts.maxIterations = retrieve2(options.maxIterations, 2);\n  var minChar = preparedOpts.minChar = retrieve2(options.minChar, 0);\n  preparedOpts.cnCharWidth = getWidth('国', font);\n  var ascCharWidth = preparedOpts.ascCharWidth = getWidth('a', font);\n  preparedOpts.placeholder = retrieve2(options.placeholder, '');\n  var contentWidth = containerWidth = Math.max(0, containerWidth - 1);\n  for (var i = 0; i < minChar && contentWidth >= ascCharWidth; i++) {\n    contentWidth -= ascCharWidth;\n  }\n  var ellipsisWidth = getWidth(ellipsis, font);\n  if (ellipsisWidth > contentWidth) {\n    ellipsis = '';\n    ellipsisWidth = 0;\n  }\n  contentWidth = containerWidth - ellipsisWidth;\n  preparedOpts.ellipsis = ellipsis;\n  preparedOpts.ellipsisWidth = ellipsisWidth;\n  preparedOpts.contentWidth = contentWidth;\n  preparedOpts.containerWidth = containerWidth;\n  return preparedOpts;\n}\nfunction truncateSingleLine(out, textLine, options) {\n  var containerWidth = options.containerWidth;\n  var font = options.font;\n  var contentWidth = options.contentWidth;\n  if (!containerWidth) {\n    out.textLine = '';\n    out.isTruncated = false;\n    return;\n  }\n  var lineWidth = getWidth(textLine, font);\n  if (lineWidth <= containerWidth) {\n    out.textLine = textLine;\n    out.isTruncated = false;\n    return;\n  }\n  for (var j = 0;; j++) {\n    if (lineWidth <= contentWidth || j >= options.maxIterations) {\n      textLine += options.ellipsis;\n      break;\n    }\n    var subLength = j === 0 ? estimateLength(textLine, contentWidth, options.ascCharWidth, options.cnCharWidth) : lineWidth > 0 ? Math.floor(textLine.length * contentWidth / lineWidth) : 0;\n    textLine = textLine.substr(0, subLength);\n    lineWidth = getWidth(textLine, font);\n  }\n  if (textLine === '') {\n    textLine = options.placeholder;\n  }\n  out.textLine = textLine;\n  out.isTruncated = true;\n}\nfunction estimateLength(text, contentWidth, ascCharWidth, cnCharWidth) {\n  var width = 0;\n  var i = 0;\n  for (var len = text.length; i < len && width < contentWidth; i++) {\n    var charCode = text.charCodeAt(i);\n    width += 0 <= charCode && charCode <= 127 ? ascCharWidth : cnCharWidth;\n  }\n  return i;\n}\nexport function parsePlainText(text, style) {\n  text != null && (text += '');\n  var overflow = style.overflow;\n  var padding = style.padding;\n  var font = style.font;\n  var truncate = overflow === 'truncate';\n  var calculatedLineHeight = getLineHeight(font);\n  var lineHeight = retrieve2(style.lineHeight, calculatedLineHeight);\n  var bgColorDrawn = !!style.backgroundColor;\n  var truncateLineOverflow = style.lineOverflow === 'truncate';\n  var isTruncated = false;\n  var width = style.width;\n  var lines;\n  if (width != null && (overflow === 'break' || overflow === 'breakAll')) {\n    lines = text ? wrapText(text, style.font, width, overflow === 'breakAll', 0).lines : [];\n  } else {\n    lines = text ? text.split('\\n') : [];\n  }\n  var contentHeight = lines.length * lineHeight;\n  var height = retrieve2(style.height, contentHeight);\n  if (contentHeight > height && truncateLineOverflow) {\n    var lineCount = Math.floor(height / lineHeight);\n    isTruncated = isTruncated || lines.length > lineCount;\n    lines = lines.slice(0, lineCount);\n  }\n  if (text && truncate && width != null) {\n    var options = prepareTruncateOptions(width, font, style.ellipsis, {\n      minChar: style.truncateMinChar,\n      placeholder: style.placeholder\n    });\n    var singleOut = {};\n    for (var i = 0; i < lines.length; i++) {\n      truncateSingleLine(singleOut, lines[i], options);\n      lines[i] = singleOut.textLine;\n      isTruncated = isTruncated || singleOut.isTruncated;\n    }\n  }\n  var outerHeight = height;\n  var contentWidth = 0;\n  for (var i = 0; i < lines.length; i++) {\n    contentWidth = Math.max(getWidth(lines[i], font), contentWidth);\n  }\n  if (width == null) {\n    width = contentWidth;\n  }\n  var outerWidth = contentWidth;\n  if (padding) {\n    outerHeight += padding[0] + padding[2];\n    outerWidth += padding[1] + padding[3];\n    width += padding[1] + padding[3];\n  }\n  if (bgColorDrawn) {\n    outerWidth = width;\n  }\n  return {\n    lines: lines,\n    height: height,\n    outerWidth: outerWidth,\n    outerHeight: outerHeight,\n    lineHeight: lineHeight,\n    calculatedLineHeight: calculatedLineHeight,\n    contentWidth: contentWidth,\n    contentHeight: contentHeight,\n    width: width,\n    isTruncated: isTruncated\n  };\n}\nvar RichTextToken = function () {\n  function RichTextToken() {}\n  return RichTextToken;\n}();\nvar RichTextLine = function () {\n  function RichTextLine(tokens) {\n    this.tokens = [];\n    if (tokens) {\n      this.tokens = tokens;\n    }\n  }\n  return RichTextLine;\n}();\nvar RichTextContentBlock = function () {\n  function RichTextContentBlock() {\n    this.width = 0;\n    this.height = 0;\n    this.contentWidth = 0;\n    this.contentHeight = 0;\n    this.outerWidth = 0;\n    this.outerHeight = 0;\n    this.lines = [];\n    this.isTruncated = false;\n  }\n  return RichTextContentBlock;\n}();\nexport { RichTextContentBlock };\nexport function parseRichText(text, style) {\n  var contentBlock = new RichTextContentBlock();\n  text != null && (text += '');\n  if (!text) {\n    return contentBlock;\n  }\n  var topWidth = style.width;\n  var topHeight = style.height;\n  var overflow = style.overflow;\n  var wrapInfo = (overflow === 'break' || overflow === 'breakAll') && topWidth != null ? {\n    width: topWidth,\n    accumWidth: 0,\n    breakAll: overflow === 'breakAll'\n  } : null;\n  var lastIndex = STYLE_REG.lastIndex = 0;\n  var result;\n  while ((result = STYLE_REG.exec(text)) != null) {\n    var matchedIndex = result.index;\n    if (matchedIndex > lastIndex) {\n      pushTokens(contentBlock, text.substring(lastIndex, matchedIndex), style, wrapInfo);\n    }\n    pushTokens(contentBlock, result[2], style, wrapInfo, result[1]);\n    lastIndex = STYLE_REG.lastIndex;\n  }\n  if (lastIndex < text.length) {\n    pushTokens(contentBlock, text.substring(lastIndex, text.length), style, wrapInfo);\n  }\n  var pendingList = [];\n  var calculatedHeight = 0;\n  var calculatedWidth = 0;\n  var stlPadding = style.padding;\n  var truncate = overflow === 'truncate';\n  var truncateLine = style.lineOverflow === 'truncate';\n  var tmpTruncateOut = {};\n  function finishLine(line, lineWidth, lineHeight) {\n    line.width = lineWidth;\n    line.lineHeight = lineHeight;\n    calculatedHeight += lineHeight;\n    calculatedWidth = Math.max(calculatedWidth, lineWidth);\n  }\n  outer: for (var i = 0; i < contentBlock.lines.length; i++) {\n    var line = contentBlock.lines[i];\n    var lineHeight = 0;\n    var lineWidth = 0;\n    for (var j = 0; j < line.tokens.length; j++) {\n      var token = line.tokens[j];\n      var tokenStyle = token.styleName && style.rich[token.styleName] || {};\n      var textPadding = token.textPadding = tokenStyle.padding;\n      var paddingH = textPadding ? textPadding[1] + textPadding[3] : 0;\n      var font = token.font = tokenStyle.font || style.font;\n      token.contentHeight = getLineHeight(font);\n      var tokenHeight = retrieve2(tokenStyle.height, token.contentHeight);\n      token.innerHeight = tokenHeight;\n      textPadding && (tokenHeight += textPadding[0] + textPadding[2]);\n      token.height = tokenHeight;\n      token.lineHeight = retrieve3(tokenStyle.lineHeight, style.lineHeight, tokenHeight);\n      token.align = tokenStyle && tokenStyle.align || style.align;\n      token.verticalAlign = tokenStyle && tokenStyle.verticalAlign || 'middle';\n      if (truncateLine && topHeight != null && calculatedHeight + token.lineHeight > topHeight) {\n        var originalLength = contentBlock.lines.length;\n        if (j > 0) {\n          line.tokens = line.tokens.slice(0, j);\n          finishLine(line, lineWidth, lineHeight);\n          contentBlock.lines = contentBlock.lines.slice(0, i + 1);\n        } else {\n          contentBlock.lines = contentBlock.lines.slice(0, i);\n        }\n        contentBlock.isTruncated = contentBlock.isTruncated || contentBlock.lines.length < originalLength;\n        break outer;\n      }\n      var styleTokenWidth = tokenStyle.width;\n      var tokenWidthNotSpecified = styleTokenWidth == null || styleTokenWidth === 'auto';\n      if (typeof styleTokenWidth === 'string' && styleTokenWidth.charAt(styleTokenWidth.length - 1) === '%') {\n        token.percentWidth = styleTokenWidth;\n        pendingList.push(token);\n        token.contentWidth = getWidth(token.text, font);\n      } else {\n        if (tokenWidthNotSpecified) {\n          var textBackgroundColor = tokenStyle.backgroundColor;\n          var bgImg = textBackgroundColor && textBackgroundColor.image;\n          if (bgImg) {\n            bgImg = imageHelper.findExistImage(bgImg);\n            if (imageHelper.isImageReady(bgImg)) {\n              token.width = Math.max(token.width, bgImg.width * tokenHeight / bgImg.height);\n            }\n          }\n        }\n        var remainTruncWidth = truncate && topWidth != null ? topWidth - lineWidth : null;\n        if (remainTruncWidth != null && remainTruncWidth < token.width) {\n          if (!tokenWidthNotSpecified || remainTruncWidth < paddingH) {\n            token.text = '';\n            token.width = token.contentWidth = 0;\n          } else {\n            truncateText2(tmpTruncateOut, token.text, remainTruncWidth - paddingH, font, style.ellipsis, {\n              minChar: style.truncateMinChar\n            });\n            token.text = tmpTruncateOut.text;\n            contentBlock.isTruncated = contentBlock.isTruncated || tmpTruncateOut.isTruncated;\n            token.width = token.contentWidth = getWidth(token.text, font);\n          }\n        } else {\n          token.contentWidth = getWidth(token.text, font);\n        }\n      }\n      token.width += paddingH;\n      lineWidth += token.width;\n      tokenStyle && (lineHeight = Math.max(lineHeight, token.lineHeight));\n    }\n    finishLine(line, lineWidth, lineHeight);\n  }\n  contentBlock.outerWidth = contentBlock.width = retrieve2(topWidth, calculatedWidth);\n  contentBlock.outerHeight = contentBlock.height = retrieve2(topHeight, calculatedHeight);\n  contentBlock.contentHeight = calculatedHeight;\n  contentBlock.contentWidth = calculatedWidth;\n  if (stlPadding) {\n    contentBlock.outerWidth += stlPadding[1] + stlPadding[3];\n    contentBlock.outerHeight += stlPadding[0] + stlPadding[2];\n  }\n  for (var i = 0; i < pendingList.length; i++) {\n    var token = pendingList[i];\n    var percentWidth = token.percentWidth;\n    token.width = parseInt(percentWidth, 10) / 100 * contentBlock.width;\n  }\n  return contentBlock;\n}\nfunction pushTokens(block, str, style, wrapInfo, styleName) {\n  var isEmptyStr = str === '';\n  var tokenStyle = styleName && style.rich[styleName] || {};\n  var lines = block.lines;\n  var font = tokenStyle.font || style.font;\n  var newLine = false;\n  var strLines;\n  var linesWidths;\n  if (wrapInfo) {\n    var tokenPadding = tokenStyle.padding;\n    var tokenPaddingH = tokenPadding ? tokenPadding[1] + tokenPadding[3] : 0;\n    if (tokenStyle.width != null && tokenStyle.width !== 'auto') {\n      var outerWidth_1 = parsePercent(tokenStyle.width, wrapInfo.width) + tokenPaddingH;\n      if (lines.length > 0) {\n        if (outerWidth_1 + wrapInfo.accumWidth > wrapInfo.width) {\n          strLines = str.split('\\n');\n          newLine = true;\n        }\n      }\n      wrapInfo.accumWidth = outerWidth_1;\n    } else {\n      var res = wrapText(str, font, wrapInfo.width, wrapInfo.breakAll, wrapInfo.accumWidth);\n      wrapInfo.accumWidth = res.accumWidth + tokenPaddingH;\n      linesWidths = res.linesWidths;\n      strLines = res.lines;\n    }\n  } else {\n    strLines = str.split('\\n');\n  }\n  for (var i = 0; i < strLines.length; i++) {\n    var text = strLines[i];\n    var token = new RichTextToken();\n    token.styleName = styleName;\n    token.text = text;\n    token.isLineHolder = !text && !isEmptyStr;\n    if (typeof tokenStyle.width === 'number') {\n      token.width = tokenStyle.width;\n    } else {\n      token.width = linesWidths ? linesWidths[i] : getWidth(text, font);\n    }\n    if (!i && !newLine) {\n      var tokens = (lines[lines.length - 1] || (lines[0] = new RichTextLine())).tokens;\n      var tokensLen = tokens.length;\n      tokensLen === 1 && tokens[0].isLineHolder ? tokens[0] = token : (text || !tokensLen || isEmptyStr) && tokens.push(token);\n    } else {\n      lines.push(new RichTextLine([token]));\n    }\n  }\n}\nfunction isAlphabeticLetter(ch) {\n  var code = ch.charCodeAt(0);\n  return code >= 0x20 && code <= 0x24F || code >= 0x370 && code <= 0x10FF || code >= 0x1200 && code <= 0x13FF || code >= 0x1E00 && code <= 0x206F;\n}\nvar breakCharMap = reduce(',&?/;] '.split(''), function (obj, ch) {\n  obj[ch] = true;\n  return obj;\n}, {});\nfunction isWordBreakChar(ch) {\n  if (isAlphabeticLetter(ch)) {\n    if (breakCharMap[ch]) {\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction wrapText(text, font, lineWidth, isBreakAll, lastAccumWidth) {\n  var lines = [];\n  var linesWidths = [];\n  var line = '';\n  var currentWord = '';\n  var currentWordWidth = 0;\n  var accumWidth = 0;\n  for (var i = 0; i < text.length; i++) {\n    var ch = text.charAt(i);\n    if (ch === '\\n') {\n      if (currentWord) {\n        line += currentWord;\n        accumWidth += currentWordWidth;\n      }\n      lines.push(line);\n      linesWidths.push(accumWidth);\n      line = '';\n      currentWord = '';\n      currentWordWidth = 0;\n      accumWidth = 0;\n      continue;\n    }\n    var chWidth = getWidth(ch, font);\n    var inWord = isBreakAll ? false : !isWordBreakChar(ch);\n    if (!lines.length ? lastAccumWidth + accumWidth + chWidth > lineWidth : accumWidth + chWidth > lineWidth) {\n      if (!accumWidth) {\n        if (inWord) {\n          lines.push(currentWord);\n          linesWidths.push(currentWordWidth);\n          currentWord = ch;\n          currentWordWidth = chWidth;\n        } else {\n          lines.push(ch);\n          linesWidths.push(chWidth);\n        }\n      } else if (line || currentWord) {\n        if (inWord) {\n          if (!line) {\n            line = currentWord;\n            currentWord = '';\n            currentWordWidth = 0;\n            accumWidth = currentWordWidth;\n          }\n          lines.push(line);\n          linesWidths.push(accumWidth - currentWordWidth);\n          currentWord += ch;\n          currentWordWidth += chWidth;\n          line = '';\n          accumWidth = currentWordWidth;\n        } else {\n          if (currentWord) {\n            line += currentWord;\n            currentWord = '';\n            currentWordWidth = 0;\n          }\n          lines.push(line);\n          linesWidths.push(accumWidth);\n          line = ch;\n          accumWidth = chWidth;\n        }\n      }\n      continue;\n    }\n    accumWidth += chWidth;\n    if (inWord) {\n      currentWord += ch;\n      currentWordWidth += chWidth;\n    } else {\n      if (currentWord) {\n        line += currentWord;\n        currentWord = '';\n        currentWordWidth = 0;\n      }\n      line += ch;\n    }\n  }\n  if (!lines.length && !line) {\n    line = text;\n    currentWord = '';\n    currentWordWidth = 0;\n  }\n  if (currentWord) {\n    line += currentWord;\n  }\n  if (line) {\n    lines.push(line);\n    linesWidths.push(accumWidth);\n  }\n  if (lines.length === 1) {\n    accumWidth += lastAccumWidth;\n  }\n  return {\n    accumWidth: accumWidth,\n    lines: lines,\n    linesWidths: linesWidths\n  };\n}", "map": {"version": 3, "names": ["imageHelper", "extend", "retrieve2", "retrieve3", "reduce", "getLineHeight", "getWidth", "parsePercent", "STYLE_REG", "truncateText", "text", "containerWidth", "font", "ellipsis", "options", "out", "truncateText2", "isTruncated", "textLines", "split", "prepareTruncateOptions", "truncateOut", "i", "len", "length", "truncateSingleLine", "textLine", "join", "preparedOpts", "maxIterations", "minChar", "cnChar<PERSON>idth", "ascCharWidth", "placeholder", "contentWidth", "Math", "max", "ellip<PERSON><PERSON><PERSON><PERSON>", "lineWidth", "j", "subLength", "estimateLength", "floor", "substr", "width", "charCode", "charCodeAt", "parsePlainText", "style", "overflow", "padding", "truncate", "calculatedLineHeight", "lineHeight", "bgColorDrawn", "backgroundColor", "truncateLineOverflow", "lineOverflow", "lines", "wrapText", "contentHeight", "height", "lineCount", "slice", "truncateMinChar", "singleOut", "outerHeight", "outerWidth", "RichTextToken", "RichTextLine", "tokens", "RichTextContentBlock", "parseRichText", "contentBlock", "topWidth", "topHeight", "wrapInfo", "accumWidth", "breakAll", "lastIndex", "result", "exec", "matchedIndex", "index", "pushTokens", "substring", "pendingList", "calculatedHeight", "calculatedWidth", "stlPadding", "truncateLine", "tmpTruncateOut", "finishLine", "line", "outer", "token", "tokenStyle", "styleName", "rich", "textPadding", "paddingH", "tokenHeight", "innerHeight", "align", "verticalAlign", "original<PERSON>ength", "styleTokenWidth", "tokenWidthNotSpecified", "char<PERSON>t", "percentWidth", "push", "textBackgroundColor", "bgImg", "image", "findExistImage", "isImageReady", "remainTrunc<PERSON>idth", "parseInt", "block", "str", "isEmptyStr", "newLine", "strLines", "linesWidths", "tokenPadding", "tokenPaddingH", "outerWidth_1", "res", "isLineHolder", "tokensLen", "isAlphabeticLetter", "ch", "code", "breakCharMap", "obj", "isWordBreakChar", "isBreakAll", "lastAccum<PERSON>idth", "currentWord", "currentWordWidth", "ch<PERSON><PERSON><PERSON>", "inWord"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/zrender/lib/graphic/helper/parseText.js"], "sourcesContent": ["import * as imageHelper from '../helper/image.js';\nimport { extend, retrieve2, retrieve3, reduce } from '../../core/util.js';\nimport { getLineHeight, getWidth, parsePercent } from '../../contain/text.js';\nvar STYLE_REG = /\\{([a-zA-Z0-9_]+)\\|([^}]*)\\}/g;\nexport function truncateText(text, containerWidth, font, ellipsis, options) {\n    var out = {};\n    truncateText2(out, text, containerWidth, font, ellipsis, options);\n    return out.text;\n}\nfunction truncateText2(out, text, containerWidth, font, ellipsis, options) {\n    if (!containerWidth) {\n        out.text = '';\n        out.isTruncated = false;\n        return;\n    }\n    var textLines = (text + '').split('\\n');\n    options = prepareTruncateOptions(containerWidth, font, ellipsis, options);\n    var isTruncated = false;\n    var truncateOut = {};\n    for (var i = 0, len = textLines.length; i < len; i++) {\n        truncateSingleLine(truncateOut, textLines[i], options);\n        textLines[i] = truncateOut.textLine;\n        isTruncated = isTruncated || truncateOut.isTruncated;\n    }\n    out.text = textLines.join('\\n');\n    out.isTruncated = isTruncated;\n}\nfunction prepareTruncateOptions(containerWidth, font, ellipsis, options) {\n    options = options || {};\n    var preparedOpts = extend({}, options);\n    preparedOpts.font = font;\n    ellipsis = retrieve2(ellipsis, '...');\n    preparedOpts.maxIterations = retrieve2(options.maxIterations, 2);\n    var minChar = preparedOpts.minChar = retrieve2(options.minChar, 0);\n    preparedOpts.cnCharWidth = getWidth('国', font);\n    var ascCharWidth = preparedOpts.ascCharWidth = getWidth('a', font);\n    preparedOpts.placeholder = retrieve2(options.placeholder, '');\n    var contentWidth = containerWidth = Math.max(0, containerWidth - 1);\n    for (var i = 0; i < minChar && contentWidth >= ascCharWidth; i++) {\n        contentWidth -= ascCharWidth;\n    }\n    var ellipsisWidth = getWidth(ellipsis, font);\n    if (ellipsisWidth > contentWidth) {\n        ellipsis = '';\n        ellipsisWidth = 0;\n    }\n    contentWidth = containerWidth - ellipsisWidth;\n    preparedOpts.ellipsis = ellipsis;\n    preparedOpts.ellipsisWidth = ellipsisWidth;\n    preparedOpts.contentWidth = contentWidth;\n    preparedOpts.containerWidth = containerWidth;\n    return preparedOpts;\n}\nfunction truncateSingleLine(out, textLine, options) {\n    var containerWidth = options.containerWidth;\n    var font = options.font;\n    var contentWidth = options.contentWidth;\n    if (!containerWidth) {\n        out.textLine = '';\n        out.isTruncated = false;\n        return;\n    }\n    var lineWidth = getWidth(textLine, font);\n    if (lineWidth <= containerWidth) {\n        out.textLine = textLine;\n        out.isTruncated = false;\n        return;\n    }\n    for (var j = 0;; j++) {\n        if (lineWidth <= contentWidth || j >= options.maxIterations) {\n            textLine += options.ellipsis;\n            break;\n        }\n        var subLength = j === 0\n            ? estimateLength(textLine, contentWidth, options.ascCharWidth, options.cnCharWidth)\n            : lineWidth > 0\n                ? Math.floor(textLine.length * contentWidth / lineWidth)\n                : 0;\n        textLine = textLine.substr(0, subLength);\n        lineWidth = getWidth(textLine, font);\n    }\n    if (textLine === '') {\n        textLine = options.placeholder;\n    }\n    out.textLine = textLine;\n    out.isTruncated = true;\n}\nfunction estimateLength(text, contentWidth, ascCharWidth, cnCharWidth) {\n    var width = 0;\n    var i = 0;\n    for (var len = text.length; i < len && width < contentWidth; i++) {\n        var charCode = text.charCodeAt(i);\n        width += (0 <= charCode && charCode <= 127) ? ascCharWidth : cnCharWidth;\n    }\n    return i;\n}\nexport function parsePlainText(text, style) {\n    text != null && (text += '');\n    var overflow = style.overflow;\n    var padding = style.padding;\n    var font = style.font;\n    var truncate = overflow === 'truncate';\n    var calculatedLineHeight = getLineHeight(font);\n    var lineHeight = retrieve2(style.lineHeight, calculatedLineHeight);\n    var bgColorDrawn = !!(style.backgroundColor);\n    var truncateLineOverflow = style.lineOverflow === 'truncate';\n    var isTruncated = false;\n    var width = style.width;\n    var lines;\n    if (width != null && (overflow === 'break' || overflow === 'breakAll')) {\n        lines = text ? wrapText(text, style.font, width, overflow === 'breakAll', 0).lines : [];\n    }\n    else {\n        lines = text ? text.split('\\n') : [];\n    }\n    var contentHeight = lines.length * lineHeight;\n    var height = retrieve2(style.height, contentHeight);\n    if (contentHeight > height && truncateLineOverflow) {\n        var lineCount = Math.floor(height / lineHeight);\n        isTruncated = isTruncated || (lines.length > lineCount);\n        lines = lines.slice(0, lineCount);\n    }\n    if (text && truncate && width != null) {\n        var options = prepareTruncateOptions(width, font, style.ellipsis, {\n            minChar: style.truncateMinChar,\n            placeholder: style.placeholder\n        });\n        var singleOut = {};\n        for (var i = 0; i < lines.length; i++) {\n            truncateSingleLine(singleOut, lines[i], options);\n            lines[i] = singleOut.textLine;\n            isTruncated = isTruncated || singleOut.isTruncated;\n        }\n    }\n    var outerHeight = height;\n    var contentWidth = 0;\n    for (var i = 0; i < lines.length; i++) {\n        contentWidth = Math.max(getWidth(lines[i], font), contentWidth);\n    }\n    if (width == null) {\n        width = contentWidth;\n    }\n    var outerWidth = contentWidth;\n    if (padding) {\n        outerHeight += padding[0] + padding[2];\n        outerWidth += padding[1] + padding[3];\n        width += padding[1] + padding[3];\n    }\n    if (bgColorDrawn) {\n        outerWidth = width;\n    }\n    return {\n        lines: lines,\n        height: height,\n        outerWidth: outerWidth,\n        outerHeight: outerHeight,\n        lineHeight: lineHeight,\n        calculatedLineHeight: calculatedLineHeight,\n        contentWidth: contentWidth,\n        contentHeight: contentHeight,\n        width: width,\n        isTruncated: isTruncated\n    };\n}\nvar RichTextToken = (function () {\n    function RichTextToken() {\n    }\n    return RichTextToken;\n}());\nvar RichTextLine = (function () {\n    function RichTextLine(tokens) {\n        this.tokens = [];\n        if (tokens) {\n            this.tokens = tokens;\n        }\n    }\n    return RichTextLine;\n}());\nvar RichTextContentBlock = (function () {\n    function RichTextContentBlock() {\n        this.width = 0;\n        this.height = 0;\n        this.contentWidth = 0;\n        this.contentHeight = 0;\n        this.outerWidth = 0;\n        this.outerHeight = 0;\n        this.lines = [];\n        this.isTruncated = false;\n    }\n    return RichTextContentBlock;\n}());\nexport { RichTextContentBlock };\nexport function parseRichText(text, style) {\n    var contentBlock = new RichTextContentBlock();\n    text != null && (text += '');\n    if (!text) {\n        return contentBlock;\n    }\n    var topWidth = style.width;\n    var topHeight = style.height;\n    var overflow = style.overflow;\n    var wrapInfo = (overflow === 'break' || overflow === 'breakAll') && topWidth != null\n        ? { width: topWidth, accumWidth: 0, breakAll: overflow === 'breakAll' }\n        : null;\n    var lastIndex = STYLE_REG.lastIndex = 0;\n    var result;\n    while ((result = STYLE_REG.exec(text)) != null) {\n        var matchedIndex = result.index;\n        if (matchedIndex > lastIndex) {\n            pushTokens(contentBlock, text.substring(lastIndex, matchedIndex), style, wrapInfo);\n        }\n        pushTokens(contentBlock, result[2], style, wrapInfo, result[1]);\n        lastIndex = STYLE_REG.lastIndex;\n    }\n    if (lastIndex < text.length) {\n        pushTokens(contentBlock, text.substring(lastIndex, text.length), style, wrapInfo);\n    }\n    var pendingList = [];\n    var calculatedHeight = 0;\n    var calculatedWidth = 0;\n    var stlPadding = style.padding;\n    var truncate = overflow === 'truncate';\n    var truncateLine = style.lineOverflow === 'truncate';\n    var tmpTruncateOut = {};\n    function finishLine(line, lineWidth, lineHeight) {\n        line.width = lineWidth;\n        line.lineHeight = lineHeight;\n        calculatedHeight += lineHeight;\n        calculatedWidth = Math.max(calculatedWidth, lineWidth);\n    }\n    outer: for (var i = 0; i < contentBlock.lines.length; i++) {\n        var line = contentBlock.lines[i];\n        var lineHeight = 0;\n        var lineWidth = 0;\n        for (var j = 0; j < line.tokens.length; j++) {\n            var token = line.tokens[j];\n            var tokenStyle = token.styleName && style.rich[token.styleName] || {};\n            var textPadding = token.textPadding = tokenStyle.padding;\n            var paddingH = textPadding ? textPadding[1] + textPadding[3] : 0;\n            var font = token.font = tokenStyle.font || style.font;\n            token.contentHeight = getLineHeight(font);\n            var tokenHeight = retrieve2(tokenStyle.height, token.contentHeight);\n            token.innerHeight = tokenHeight;\n            textPadding && (tokenHeight += textPadding[0] + textPadding[2]);\n            token.height = tokenHeight;\n            token.lineHeight = retrieve3(tokenStyle.lineHeight, style.lineHeight, tokenHeight);\n            token.align = tokenStyle && tokenStyle.align || style.align;\n            token.verticalAlign = tokenStyle && tokenStyle.verticalAlign || 'middle';\n            if (truncateLine && topHeight != null && calculatedHeight + token.lineHeight > topHeight) {\n                var originalLength = contentBlock.lines.length;\n                if (j > 0) {\n                    line.tokens = line.tokens.slice(0, j);\n                    finishLine(line, lineWidth, lineHeight);\n                    contentBlock.lines = contentBlock.lines.slice(0, i + 1);\n                }\n                else {\n                    contentBlock.lines = contentBlock.lines.slice(0, i);\n                }\n                contentBlock.isTruncated = contentBlock.isTruncated || (contentBlock.lines.length < originalLength);\n                break outer;\n            }\n            var styleTokenWidth = tokenStyle.width;\n            var tokenWidthNotSpecified = styleTokenWidth == null || styleTokenWidth === 'auto';\n            if (typeof styleTokenWidth === 'string' && styleTokenWidth.charAt(styleTokenWidth.length - 1) === '%') {\n                token.percentWidth = styleTokenWidth;\n                pendingList.push(token);\n                token.contentWidth = getWidth(token.text, font);\n            }\n            else {\n                if (tokenWidthNotSpecified) {\n                    var textBackgroundColor = tokenStyle.backgroundColor;\n                    var bgImg = textBackgroundColor && textBackgroundColor.image;\n                    if (bgImg) {\n                        bgImg = imageHelper.findExistImage(bgImg);\n                        if (imageHelper.isImageReady(bgImg)) {\n                            token.width = Math.max(token.width, bgImg.width * tokenHeight / bgImg.height);\n                        }\n                    }\n                }\n                var remainTruncWidth = truncate && topWidth != null\n                    ? topWidth - lineWidth : null;\n                if (remainTruncWidth != null && remainTruncWidth < token.width) {\n                    if (!tokenWidthNotSpecified || remainTruncWidth < paddingH) {\n                        token.text = '';\n                        token.width = token.contentWidth = 0;\n                    }\n                    else {\n                        truncateText2(tmpTruncateOut, token.text, remainTruncWidth - paddingH, font, style.ellipsis, { minChar: style.truncateMinChar });\n                        token.text = tmpTruncateOut.text;\n                        contentBlock.isTruncated = contentBlock.isTruncated || tmpTruncateOut.isTruncated;\n                        token.width = token.contentWidth = getWidth(token.text, font);\n                    }\n                }\n                else {\n                    token.contentWidth = getWidth(token.text, font);\n                }\n            }\n            token.width += paddingH;\n            lineWidth += token.width;\n            tokenStyle && (lineHeight = Math.max(lineHeight, token.lineHeight));\n        }\n        finishLine(line, lineWidth, lineHeight);\n    }\n    contentBlock.outerWidth = contentBlock.width = retrieve2(topWidth, calculatedWidth);\n    contentBlock.outerHeight = contentBlock.height = retrieve2(topHeight, calculatedHeight);\n    contentBlock.contentHeight = calculatedHeight;\n    contentBlock.contentWidth = calculatedWidth;\n    if (stlPadding) {\n        contentBlock.outerWidth += stlPadding[1] + stlPadding[3];\n        contentBlock.outerHeight += stlPadding[0] + stlPadding[2];\n    }\n    for (var i = 0; i < pendingList.length; i++) {\n        var token = pendingList[i];\n        var percentWidth = token.percentWidth;\n        token.width = parseInt(percentWidth, 10) / 100 * contentBlock.width;\n    }\n    return contentBlock;\n}\nfunction pushTokens(block, str, style, wrapInfo, styleName) {\n    var isEmptyStr = str === '';\n    var tokenStyle = styleName && style.rich[styleName] || {};\n    var lines = block.lines;\n    var font = tokenStyle.font || style.font;\n    var newLine = false;\n    var strLines;\n    var linesWidths;\n    if (wrapInfo) {\n        var tokenPadding = tokenStyle.padding;\n        var tokenPaddingH = tokenPadding ? tokenPadding[1] + tokenPadding[3] : 0;\n        if (tokenStyle.width != null && tokenStyle.width !== 'auto') {\n            var outerWidth_1 = parsePercent(tokenStyle.width, wrapInfo.width) + tokenPaddingH;\n            if (lines.length > 0) {\n                if (outerWidth_1 + wrapInfo.accumWidth > wrapInfo.width) {\n                    strLines = str.split('\\n');\n                    newLine = true;\n                }\n            }\n            wrapInfo.accumWidth = outerWidth_1;\n        }\n        else {\n            var res = wrapText(str, font, wrapInfo.width, wrapInfo.breakAll, wrapInfo.accumWidth);\n            wrapInfo.accumWidth = res.accumWidth + tokenPaddingH;\n            linesWidths = res.linesWidths;\n            strLines = res.lines;\n        }\n    }\n    else {\n        strLines = str.split('\\n');\n    }\n    for (var i = 0; i < strLines.length; i++) {\n        var text = strLines[i];\n        var token = new RichTextToken();\n        token.styleName = styleName;\n        token.text = text;\n        token.isLineHolder = !text && !isEmptyStr;\n        if (typeof tokenStyle.width === 'number') {\n            token.width = tokenStyle.width;\n        }\n        else {\n            token.width = linesWidths\n                ? linesWidths[i]\n                : getWidth(text, font);\n        }\n        if (!i && !newLine) {\n            var tokens = (lines[lines.length - 1] || (lines[0] = new RichTextLine())).tokens;\n            var tokensLen = tokens.length;\n            (tokensLen === 1 && tokens[0].isLineHolder)\n                ? (tokens[0] = token)\n                : ((text || !tokensLen || isEmptyStr) && tokens.push(token));\n        }\n        else {\n            lines.push(new RichTextLine([token]));\n        }\n    }\n}\nfunction isAlphabeticLetter(ch) {\n    var code = ch.charCodeAt(0);\n    return code >= 0x20 && code <= 0x24F\n        || code >= 0x370 && code <= 0x10FF\n        || code >= 0x1200 && code <= 0x13FF\n        || code >= 0x1E00 && code <= 0x206F;\n}\nvar breakCharMap = reduce(',&?/;] '.split(''), function (obj, ch) {\n    obj[ch] = true;\n    return obj;\n}, {});\nfunction isWordBreakChar(ch) {\n    if (isAlphabeticLetter(ch)) {\n        if (breakCharMap[ch]) {\n            return true;\n        }\n        return false;\n    }\n    return true;\n}\nfunction wrapText(text, font, lineWidth, isBreakAll, lastAccumWidth) {\n    var lines = [];\n    var linesWidths = [];\n    var line = '';\n    var currentWord = '';\n    var currentWordWidth = 0;\n    var accumWidth = 0;\n    for (var i = 0; i < text.length; i++) {\n        var ch = text.charAt(i);\n        if (ch === '\\n') {\n            if (currentWord) {\n                line += currentWord;\n                accumWidth += currentWordWidth;\n            }\n            lines.push(line);\n            linesWidths.push(accumWidth);\n            line = '';\n            currentWord = '';\n            currentWordWidth = 0;\n            accumWidth = 0;\n            continue;\n        }\n        var chWidth = getWidth(ch, font);\n        var inWord = isBreakAll ? false : !isWordBreakChar(ch);\n        if (!lines.length\n            ? lastAccumWidth + accumWidth + chWidth > lineWidth\n            : accumWidth + chWidth > lineWidth) {\n            if (!accumWidth) {\n                if (inWord) {\n                    lines.push(currentWord);\n                    linesWidths.push(currentWordWidth);\n                    currentWord = ch;\n                    currentWordWidth = chWidth;\n                }\n                else {\n                    lines.push(ch);\n                    linesWidths.push(chWidth);\n                }\n            }\n            else if (line || currentWord) {\n                if (inWord) {\n                    if (!line) {\n                        line = currentWord;\n                        currentWord = '';\n                        currentWordWidth = 0;\n                        accumWidth = currentWordWidth;\n                    }\n                    lines.push(line);\n                    linesWidths.push(accumWidth - currentWordWidth);\n                    currentWord += ch;\n                    currentWordWidth += chWidth;\n                    line = '';\n                    accumWidth = currentWordWidth;\n                }\n                else {\n                    if (currentWord) {\n                        line += currentWord;\n                        currentWord = '';\n                        currentWordWidth = 0;\n                    }\n                    lines.push(line);\n                    linesWidths.push(accumWidth);\n                    line = ch;\n                    accumWidth = chWidth;\n                }\n            }\n            continue;\n        }\n        accumWidth += chWidth;\n        if (inWord) {\n            currentWord += ch;\n            currentWordWidth += chWidth;\n        }\n        else {\n            if (currentWord) {\n                line += currentWord;\n                currentWord = '';\n                currentWordWidth = 0;\n            }\n            line += ch;\n        }\n    }\n    if (!lines.length && !line) {\n        line = text;\n        currentWord = '';\n        currentWordWidth = 0;\n    }\n    if (currentWord) {\n        line += currentWord;\n    }\n    if (line) {\n        lines.push(line);\n        linesWidths.push(accumWidth);\n    }\n    if (lines.length === 1) {\n        accumWidth += lastAccumWidth;\n    }\n    return {\n        accumWidth: accumWidth,\n        lines: lines,\n        linesWidths: linesWidths\n    };\n}\n"], "mappings": "AAAA,OAAO,KAAKA,WAAW,MAAM,oBAAoB;AACjD,SAASC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,MAAM,QAAQ,oBAAoB;AACzE,SAASC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,uBAAuB;AAC7E,IAAIC,SAAS,GAAG,+BAA+B;AAC/C,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,cAAc,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACxE,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZC,aAAa,CAACD,GAAG,EAAEL,IAAI,EAAEC,cAAc,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACjE,OAAOC,GAAG,CAACL,IAAI;AACnB;AACA,SAASM,aAAaA,CAACD,GAAG,EAAEL,IAAI,EAAEC,cAAc,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACvE,IAAI,CAACH,cAAc,EAAE;IACjBI,GAAG,CAACL,IAAI,GAAG,EAAE;IACbK,GAAG,CAACE,WAAW,GAAG,KAAK;IACvB;EACJ;EACA,IAAIC,SAAS,GAAG,CAACR,IAAI,GAAG,EAAE,EAAES,KAAK,CAAC,IAAI,CAAC;EACvCL,OAAO,GAAGM,sBAAsB,CAACT,cAAc,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACzE,IAAIG,WAAW,GAAG,KAAK;EACvB,IAAII,WAAW,GAAG,CAAC,CAAC;EACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGL,SAAS,CAACM,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAClDG,kBAAkB,CAACJ,WAAW,EAAEH,SAAS,CAACI,CAAC,CAAC,EAAER,OAAO,CAAC;IACtDI,SAAS,CAACI,CAAC,CAAC,GAAGD,WAAW,CAACK,QAAQ;IACnCT,WAAW,GAAGA,WAAW,IAAII,WAAW,CAACJ,WAAW;EACxD;EACAF,GAAG,CAACL,IAAI,GAAGQ,SAAS,CAACS,IAAI,CAAC,IAAI,CAAC;EAC/BZ,GAAG,CAACE,WAAW,GAAGA,WAAW;AACjC;AACA,SAASG,sBAAsBA,CAACT,cAAc,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACrEA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,IAAIc,YAAY,GAAG3B,MAAM,CAAC,CAAC,CAAC,EAAEa,OAAO,CAAC;EACtCc,YAAY,CAAChB,IAAI,GAAGA,IAAI;EACxBC,QAAQ,GAAGX,SAAS,CAACW,QAAQ,EAAE,KAAK,CAAC;EACrCe,YAAY,CAACC,aAAa,GAAG3B,SAAS,CAACY,OAAO,CAACe,aAAa,EAAE,CAAC,CAAC;EAChE,IAAIC,OAAO,GAAGF,YAAY,CAACE,OAAO,GAAG5B,SAAS,CAACY,OAAO,CAACgB,OAAO,EAAE,CAAC,CAAC;EAClEF,YAAY,CAACG,WAAW,GAAGzB,QAAQ,CAAC,GAAG,EAAEM,IAAI,CAAC;EAC9C,IAAIoB,YAAY,GAAGJ,YAAY,CAACI,YAAY,GAAG1B,QAAQ,CAAC,GAAG,EAAEM,IAAI,CAAC;EAClEgB,YAAY,CAACK,WAAW,GAAG/B,SAAS,CAACY,OAAO,CAACmB,WAAW,EAAE,EAAE,CAAC;EAC7D,IAAIC,YAAY,GAAGvB,cAAc,GAAGwB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEzB,cAAc,GAAG,CAAC,CAAC;EACnE,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,OAAO,IAAII,YAAY,IAAIF,YAAY,EAAEV,CAAC,EAAE,EAAE;IAC9DY,YAAY,IAAIF,YAAY;EAChC;EACA,IAAIK,aAAa,GAAG/B,QAAQ,CAACO,QAAQ,EAAED,IAAI,CAAC;EAC5C,IAAIyB,aAAa,GAAGH,YAAY,EAAE;IAC9BrB,QAAQ,GAAG,EAAE;IACbwB,aAAa,GAAG,CAAC;EACrB;EACAH,YAAY,GAAGvB,cAAc,GAAG0B,aAAa;EAC7CT,YAAY,CAACf,QAAQ,GAAGA,QAAQ;EAChCe,YAAY,CAACS,aAAa,GAAGA,aAAa;EAC1CT,YAAY,CAACM,YAAY,GAAGA,YAAY;EACxCN,YAAY,CAACjB,cAAc,GAAGA,cAAc;EAC5C,OAAOiB,YAAY;AACvB;AACA,SAASH,kBAAkBA,CAACV,GAAG,EAAEW,QAAQ,EAAEZ,OAAO,EAAE;EAChD,IAAIH,cAAc,GAAGG,OAAO,CAACH,cAAc;EAC3C,IAAIC,IAAI,GAAGE,OAAO,CAACF,IAAI;EACvB,IAAIsB,YAAY,GAAGpB,OAAO,CAACoB,YAAY;EACvC,IAAI,CAACvB,cAAc,EAAE;IACjBI,GAAG,CAACW,QAAQ,GAAG,EAAE;IACjBX,GAAG,CAACE,WAAW,GAAG,KAAK;IACvB;EACJ;EACA,IAAIqB,SAAS,GAAGhC,QAAQ,CAACoB,QAAQ,EAAEd,IAAI,CAAC;EACxC,IAAI0B,SAAS,IAAI3B,cAAc,EAAE;IAC7BI,GAAG,CAACW,QAAQ,GAAGA,QAAQ;IACvBX,GAAG,CAACE,WAAW,GAAG,KAAK;IACvB;EACJ;EACA,KAAK,IAAIsB,CAAC,GAAG,CAAC,GAAGA,CAAC,EAAE,EAAE;IAClB,IAAID,SAAS,IAAIJ,YAAY,IAAIK,CAAC,IAAIzB,OAAO,CAACe,aAAa,EAAE;MACzDH,QAAQ,IAAIZ,OAAO,CAACD,QAAQ;MAC5B;IACJ;IACA,IAAI2B,SAAS,GAAGD,CAAC,KAAK,CAAC,GACjBE,cAAc,CAACf,QAAQ,EAAEQ,YAAY,EAAEpB,OAAO,CAACkB,YAAY,EAAElB,OAAO,CAACiB,WAAW,CAAC,GACjFO,SAAS,GAAG,CAAC,GACTH,IAAI,CAACO,KAAK,CAAChB,QAAQ,CAACF,MAAM,GAAGU,YAAY,GAAGI,SAAS,CAAC,GACtD,CAAC;IACXZ,QAAQ,GAAGA,QAAQ,CAACiB,MAAM,CAAC,CAAC,EAAEH,SAAS,CAAC;IACxCF,SAAS,GAAGhC,QAAQ,CAACoB,QAAQ,EAAEd,IAAI,CAAC;EACxC;EACA,IAAIc,QAAQ,KAAK,EAAE,EAAE;IACjBA,QAAQ,GAAGZ,OAAO,CAACmB,WAAW;EAClC;EACAlB,GAAG,CAACW,QAAQ,GAAGA,QAAQ;EACvBX,GAAG,CAACE,WAAW,GAAG,IAAI;AAC1B;AACA,SAASwB,cAAcA,CAAC/B,IAAI,EAAEwB,YAAY,EAAEF,YAAY,EAAED,WAAW,EAAE;EACnE,IAAIa,KAAK,GAAG,CAAC;EACb,IAAItB,CAAC,GAAG,CAAC;EACT,KAAK,IAAIC,GAAG,GAAGb,IAAI,CAACc,MAAM,EAAEF,CAAC,GAAGC,GAAG,IAAIqB,KAAK,GAAGV,YAAY,EAAEZ,CAAC,EAAE,EAAE;IAC9D,IAAIuB,QAAQ,GAAGnC,IAAI,CAACoC,UAAU,CAACxB,CAAC,CAAC;IACjCsB,KAAK,IAAK,CAAC,IAAIC,QAAQ,IAAIA,QAAQ,IAAI,GAAG,GAAIb,YAAY,GAAGD,WAAW;EAC5E;EACA,OAAOT,CAAC;AACZ;AACA,OAAO,SAASyB,cAAcA,CAACrC,IAAI,EAAEsC,KAAK,EAAE;EACxCtC,IAAI,IAAI,IAAI,KAAKA,IAAI,IAAI,EAAE,CAAC;EAC5B,IAAIuC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;EAC7B,IAAIC,OAAO,GAAGF,KAAK,CAACE,OAAO;EAC3B,IAAItC,IAAI,GAAGoC,KAAK,CAACpC,IAAI;EACrB,IAAIuC,QAAQ,GAAGF,QAAQ,KAAK,UAAU;EACtC,IAAIG,oBAAoB,GAAG/C,aAAa,CAACO,IAAI,CAAC;EAC9C,IAAIyC,UAAU,GAAGnD,SAAS,CAAC8C,KAAK,CAACK,UAAU,EAAED,oBAAoB,CAAC;EAClE,IAAIE,YAAY,GAAG,CAAC,CAAEN,KAAK,CAACO,eAAgB;EAC5C,IAAIC,oBAAoB,GAAGR,KAAK,CAACS,YAAY,KAAK,UAAU;EAC5D,IAAIxC,WAAW,GAAG,KAAK;EACvB,IAAI2B,KAAK,GAAGI,KAAK,CAACJ,KAAK;EACvB,IAAIc,KAAK;EACT,IAAId,KAAK,IAAI,IAAI,KAAKK,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,UAAU,CAAC,EAAE;IACpES,KAAK,GAAGhD,IAAI,GAAGiD,QAAQ,CAACjD,IAAI,EAAEsC,KAAK,CAACpC,IAAI,EAAEgC,KAAK,EAAEK,QAAQ,KAAK,UAAU,EAAE,CAAC,CAAC,CAACS,KAAK,GAAG,EAAE;EAC3F,CAAC,MACI;IACDA,KAAK,GAAGhD,IAAI,GAAGA,IAAI,CAACS,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;EACxC;EACA,IAAIyC,aAAa,GAAGF,KAAK,CAAClC,MAAM,GAAG6B,UAAU;EAC7C,IAAIQ,MAAM,GAAG3D,SAAS,CAAC8C,KAAK,CAACa,MAAM,EAAED,aAAa,CAAC;EACnD,IAAIA,aAAa,GAAGC,MAAM,IAAIL,oBAAoB,EAAE;IAChD,IAAIM,SAAS,GAAG3B,IAAI,CAACO,KAAK,CAACmB,MAAM,GAAGR,UAAU,CAAC;IAC/CpC,WAAW,GAAGA,WAAW,IAAKyC,KAAK,CAAClC,MAAM,GAAGsC,SAAU;IACvDJ,KAAK,GAAGA,KAAK,CAACK,KAAK,CAAC,CAAC,EAAED,SAAS,CAAC;EACrC;EACA,IAAIpD,IAAI,IAAIyC,QAAQ,IAAIP,KAAK,IAAI,IAAI,EAAE;IACnC,IAAI9B,OAAO,GAAGM,sBAAsB,CAACwB,KAAK,EAAEhC,IAAI,EAAEoC,KAAK,CAACnC,QAAQ,EAAE;MAC9DiB,OAAO,EAAEkB,KAAK,CAACgB,eAAe;MAC9B/B,WAAW,EAAEe,KAAK,CAACf;IACvB,CAAC,CAAC;IACF,IAAIgC,SAAS,GAAG,CAAC,CAAC;IAClB,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,KAAK,CAAClC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACnCG,kBAAkB,CAACwC,SAAS,EAAEP,KAAK,CAACpC,CAAC,CAAC,EAAER,OAAO,CAAC;MAChD4C,KAAK,CAACpC,CAAC,CAAC,GAAG2C,SAAS,CAACvC,QAAQ;MAC7BT,WAAW,GAAGA,WAAW,IAAIgD,SAAS,CAAChD,WAAW;IACtD;EACJ;EACA,IAAIiD,WAAW,GAAGL,MAAM;EACxB,IAAI3B,YAAY,GAAG,CAAC;EACpB,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,KAAK,CAAClC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACnCY,YAAY,GAAGC,IAAI,CAACC,GAAG,CAAC9B,QAAQ,CAACoD,KAAK,CAACpC,CAAC,CAAC,EAAEV,IAAI,CAAC,EAAEsB,YAAY,CAAC;EACnE;EACA,IAAIU,KAAK,IAAI,IAAI,EAAE;IACfA,KAAK,GAAGV,YAAY;EACxB;EACA,IAAIiC,UAAU,GAAGjC,YAAY;EAC7B,IAAIgB,OAAO,EAAE;IACTgB,WAAW,IAAIhB,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;IACtCiB,UAAU,IAAIjB,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;IACrCN,KAAK,IAAIM,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;EACpC;EACA,IAAII,YAAY,EAAE;IACda,UAAU,GAAGvB,KAAK;EACtB;EACA,OAAO;IACHc,KAAK,EAAEA,KAAK;IACZG,MAAM,EAAEA,MAAM;IACdM,UAAU,EAAEA,UAAU;IACtBD,WAAW,EAAEA,WAAW;IACxBb,UAAU,EAAEA,UAAU;IACtBD,oBAAoB,EAAEA,oBAAoB;IAC1ClB,YAAY,EAAEA,YAAY;IAC1B0B,aAAa,EAAEA,aAAa;IAC5BhB,KAAK,EAAEA,KAAK;IACZ3B,WAAW,EAAEA;EACjB,CAAC;AACL;AACA,IAAImD,aAAa,GAAI,YAAY;EAC7B,SAASA,aAAaA,CAAA,EAAG,CACzB;EACA,OAAOA,aAAa;AACxB,CAAC,CAAC,CAAE;AACJ,IAAIC,YAAY,GAAI,YAAY;EAC5B,SAASA,YAAYA,CAACC,MAAM,EAAE;IAC1B,IAAI,CAACA,MAAM,GAAG,EAAE;IAChB,IAAIA,MAAM,EAAE;MACR,IAAI,CAACA,MAAM,GAAGA,MAAM;IACxB;EACJ;EACA,OAAOD,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,IAAIE,oBAAoB,GAAI,YAAY;EACpC,SAASA,oBAAoBA,CAAA,EAAG;IAC5B,IAAI,CAAC3B,KAAK,GAAG,CAAC;IACd,IAAI,CAACiB,MAAM,GAAG,CAAC;IACf,IAAI,CAAC3B,YAAY,GAAG,CAAC;IACrB,IAAI,CAAC0B,aAAa,GAAG,CAAC;IACtB,IAAI,CAACO,UAAU,GAAG,CAAC;IACnB,IAAI,CAACD,WAAW,GAAG,CAAC;IACpB,IAAI,CAACR,KAAK,GAAG,EAAE;IACf,IAAI,CAACzC,WAAW,GAAG,KAAK;EAC5B;EACA,OAAOsD,oBAAoB;AAC/B,CAAC,CAAC,CAAE;AACJ,SAASA,oBAAoB;AAC7B,OAAO,SAASC,aAAaA,CAAC9D,IAAI,EAAEsC,KAAK,EAAE;EACvC,IAAIyB,YAAY,GAAG,IAAIF,oBAAoB,CAAC,CAAC;EAC7C7D,IAAI,IAAI,IAAI,KAAKA,IAAI,IAAI,EAAE,CAAC;EAC5B,IAAI,CAACA,IAAI,EAAE;IACP,OAAO+D,YAAY;EACvB;EACA,IAAIC,QAAQ,GAAG1B,KAAK,CAACJ,KAAK;EAC1B,IAAI+B,SAAS,GAAG3B,KAAK,CAACa,MAAM;EAC5B,IAAIZ,QAAQ,GAAGD,KAAK,CAACC,QAAQ;EAC7B,IAAI2B,QAAQ,GAAG,CAAC3B,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,UAAU,KAAKyB,QAAQ,IAAI,IAAI,GAC9E;IAAE9B,KAAK,EAAE8B,QAAQ;IAAEG,UAAU,EAAE,CAAC;IAAEC,QAAQ,EAAE7B,QAAQ,KAAK;EAAW,CAAC,GACrE,IAAI;EACV,IAAI8B,SAAS,GAAGvE,SAAS,CAACuE,SAAS,GAAG,CAAC;EACvC,IAAIC,MAAM;EACV,OAAO,CAACA,MAAM,GAAGxE,SAAS,CAACyE,IAAI,CAACvE,IAAI,CAAC,KAAK,IAAI,EAAE;IAC5C,IAAIwE,YAAY,GAAGF,MAAM,CAACG,KAAK;IAC/B,IAAID,YAAY,GAAGH,SAAS,EAAE;MAC1BK,UAAU,CAACX,YAAY,EAAE/D,IAAI,CAAC2E,SAAS,CAACN,SAAS,EAAEG,YAAY,CAAC,EAAElC,KAAK,EAAE4B,QAAQ,CAAC;IACtF;IACAQ,UAAU,CAACX,YAAY,EAAEO,MAAM,CAAC,CAAC,CAAC,EAAEhC,KAAK,EAAE4B,QAAQ,EAAEI,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/DD,SAAS,GAAGvE,SAAS,CAACuE,SAAS;EACnC;EACA,IAAIA,SAAS,GAAGrE,IAAI,CAACc,MAAM,EAAE;IACzB4D,UAAU,CAACX,YAAY,EAAE/D,IAAI,CAAC2E,SAAS,CAACN,SAAS,EAAErE,IAAI,CAACc,MAAM,CAAC,EAAEwB,KAAK,EAAE4B,QAAQ,CAAC;EACrF;EACA,IAAIU,WAAW,GAAG,EAAE;EACpB,IAAIC,gBAAgB,GAAG,CAAC;EACxB,IAAIC,eAAe,GAAG,CAAC;EACvB,IAAIC,UAAU,GAAGzC,KAAK,CAACE,OAAO;EAC9B,IAAIC,QAAQ,GAAGF,QAAQ,KAAK,UAAU;EACtC,IAAIyC,YAAY,GAAG1C,KAAK,CAACS,YAAY,KAAK,UAAU;EACpD,IAAIkC,cAAc,GAAG,CAAC,CAAC;EACvB,SAASC,UAAUA,CAACC,IAAI,EAAEvD,SAAS,EAAEe,UAAU,EAAE;IAC7CwC,IAAI,CAACjD,KAAK,GAAGN,SAAS;IACtBuD,IAAI,CAACxC,UAAU,GAAGA,UAAU;IAC5BkC,gBAAgB,IAAIlC,UAAU;IAC9BmC,eAAe,GAAGrD,IAAI,CAACC,GAAG,CAACoD,eAAe,EAAElD,SAAS,CAAC;EAC1D;EACAwD,KAAK,EAAE,KAAK,IAAIxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmD,YAAY,CAACf,KAAK,CAAClC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACvD,IAAIuE,IAAI,GAAGpB,YAAY,CAACf,KAAK,CAACpC,CAAC,CAAC;IAChC,IAAI+B,UAAU,GAAG,CAAC;IAClB,IAAIf,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsD,IAAI,CAACvB,MAAM,CAAC9C,MAAM,EAAEe,CAAC,EAAE,EAAE;MACzC,IAAIwD,KAAK,GAAGF,IAAI,CAACvB,MAAM,CAAC/B,CAAC,CAAC;MAC1B,IAAIyD,UAAU,GAAGD,KAAK,CAACE,SAAS,IAAIjD,KAAK,CAACkD,IAAI,CAACH,KAAK,CAACE,SAAS,CAAC,IAAI,CAAC,CAAC;MACrE,IAAIE,WAAW,GAAGJ,KAAK,CAACI,WAAW,GAAGH,UAAU,CAAC9C,OAAO;MACxD,IAAIkD,QAAQ,GAAGD,WAAW,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;MAChE,IAAIvF,IAAI,GAAGmF,KAAK,CAACnF,IAAI,GAAGoF,UAAU,CAACpF,IAAI,IAAIoC,KAAK,CAACpC,IAAI;MACrDmF,KAAK,CAACnC,aAAa,GAAGvD,aAAa,CAACO,IAAI,CAAC;MACzC,IAAIyF,WAAW,GAAGnG,SAAS,CAAC8F,UAAU,CAACnC,MAAM,EAAEkC,KAAK,CAACnC,aAAa,CAAC;MACnEmC,KAAK,CAACO,WAAW,GAAGD,WAAW;MAC/BF,WAAW,KAAKE,WAAW,IAAIF,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,CAAC;MAC/DJ,KAAK,CAAClC,MAAM,GAAGwC,WAAW;MAC1BN,KAAK,CAAC1C,UAAU,GAAGlD,SAAS,CAAC6F,UAAU,CAAC3C,UAAU,EAAEL,KAAK,CAACK,UAAU,EAAEgD,WAAW,CAAC;MAClFN,KAAK,CAACQ,KAAK,GAAGP,UAAU,IAAIA,UAAU,CAACO,KAAK,IAAIvD,KAAK,CAACuD,KAAK;MAC3DR,KAAK,CAACS,aAAa,GAAGR,UAAU,IAAIA,UAAU,CAACQ,aAAa,IAAI,QAAQ;MACxE,IAAId,YAAY,IAAIf,SAAS,IAAI,IAAI,IAAIY,gBAAgB,GAAGQ,KAAK,CAAC1C,UAAU,GAAGsB,SAAS,EAAE;QACtF,IAAI8B,cAAc,GAAGhC,YAAY,CAACf,KAAK,CAAClC,MAAM;QAC9C,IAAIe,CAAC,GAAG,CAAC,EAAE;UACPsD,IAAI,CAACvB,MAAM,GAAGuB,IAAI,CAACvB,MAAM,CAACP,KAAK,CAAC,CAAC,EAAExB,CAAC,CAAC;UACrCqD,UAAU,CAACC,IAAI,EAAEvD,SAAS,EAAEe,UAAU,CAAC;UACvCoB,YAAY,CAACf,KAAK,GAAGe,YAAY,CAACf,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEzC,CAAC,GAAG,CAAC,CAAC;QAC3D,CAAC,MACI;UACDmD,YAAY,CAACf,KAAK,GAAGe,YAAY,CAACf,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEzC,CAAC,CAAC;QACvD;QACAmD,YAAY,CAACxD,WAAW,GAAGwD,YAAY,CAACxD,WAAW,IAAKwD,YAAY,CAACf,KAAK,CAAClC,MAAM,GAAGiF,cAAe;QACnG,MAAMX,KAAK;MACf;MACA,IAAIY,eAAe,GAAGV,UAAU,CAACpD,KAAK;MACtC,IAAI+D,sBAAsB,GAAGD,eAAe,IAAI,IAAI,IAAIA,eAAe,KAAK,MAAM;MAClF,IAAI,OAAOA,eAAe,KAAK,QAAQ,IAAIA,eAAe,CAACE,MAAM,CAACF,eAAe,CAAClF,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QACnGuE,KAAK,CAACc,YAAY,GAAGH,eAAe;QACpCpB,WAAW,CAACwB,IAAI,CAACf,KAAK,CAAC;QACvBA,KAAK,CAAC7D,YAAY,GAAG5B,QAAQ,CAACyF,KAAK,CAACrF,IAAI,EAAEE,IAAI,CAAC;MACnD,CAAC,MACI;QACD,IAAI+F,sBAAsB,EAAE;UACxB,IAAII,mBAAmB,GAAGf,UAAU,CAACzC,eAAe;UACpD,IAAIyD,KAAK,GAAGD,mBAAmB,IAAIA,mBAAmB,CAACE,KAAK;UAC5D,IAAID,KAAK,EAAE;YACPA,KAAK,GAAGhH,WAAW,CAACkH,cAAc,CAACF,KAAK,CAAC;YACzC,IAAIhH,WAAW,CAACmH,YAAY,CAACH,KAAK,CAAC,EAAE;cACjCjB,KAAK,CAACnD,KAAK,GAAGT,IAAI,CAACC,GAAG,CAAC2D,KAAK,CAACnD,KAAK,EAAEoE,KAAK,CAACpE,KAAK,GAAGyD,WAAW,GAAGW,KAAK,CAACnD,MAAM,CAAC;YACjF;UACJ;QACJ;QACA,IAAIuD,gBAAgB,GAAGjE,QAAQ,IAAIuB,QAAQ,IAAI,IAAI,GAC7CA,QAAQ,GAAGpC,SAAS,GAAG,IAAI;QACjC,IAAI8E,gBAAgB,IAAI,IAAI,IAAIA,gBAAgB,GAAGrB,KAAK,CAACnD,KAAK,EAAE;UAC5D,IAAI,CAAC+D,sBAAsB,IAAIS,gBAAgB,GAAGhB,QAAQ,EAAE;YACxDL,KAAK,CAACrF,IAAI,GAAG,EAAE;YACfqF,KAAK,CAACnD,KAAK,GAAGmD,KAAK,CAAC7D,YAAY,GAAG,CAAC;UACxC,CAAC,MACI;YACDlB,aAAa,CAAC2E,cAAc,EAAEI,KAAK,CAACrF,IAAI,EAAE0G,gBAAgB,GAAGhB,QAAQ,EAAExF,IAAI,EAAEoC,KAAK,CAACnC,QAAQ,EAAE;cAAEiB,OAAO,EAAEkB,KAAK,CAACgB;YAAgB,CAAC,CAAC;YAChI+B,KAAK,CAACrF,IAAI,GAAGiF,cAAc,CAACjF,IAAI;YAChC+D,YAAY,CAACxD,WAAW,GAAGwD,YAAY,CAACxD,WAAW,IAAI0E,cAAc,CAAC1E,WAAW;YACjF8E,KAAK,CAACnD,KAAK,GAAGmD,KAAK,CAAC7D,YAAY,GAAG5B,QAAQ,CAACyF,KAAK,CAACrF,IAAI,EAAEE,IAAI,CAAC;UACjE;QACJ,CAAC,MACI;UACDmF,KAAK,CAAC7D,YAAY,GAAG5B,QAAQ,CAACyF,KAAK,CAACrF,IAAI,EAAEE,IAAI,CAAC;QACnD;MACJ;MACAmF,KAAK,CAACnD,KAAK,IAAIwD,QAAQ;MACvB9D,SAAS,IAAIyD,KAAK,CAACnD,KAAK;MACxBoD,UAAU,KAAK3C,UAAU,GAAGlB,IAAI,CAACC,GAAG,CAACiB,UAAU,EAAE0C,KAAK,CAAC1C,UAAU,CAAC,CAAC;IACvE;IACAuC,UAAU,CAACC,IAAI,EAAEvD,SAAS,EAAEe,UAAU,CAAC;EAC3C;EACAoB,YAAY,CAACN,UAAU,GAAGM,YAAY,CAAC7B,KAAK,GAAG1C,SAAS,CAACwE,QAAQ,EAAEc,eAAe,CAAC;EACnFf,YAAY,CAACP,WAAW,GAAGO,YAAY,CAACZ,MAAM,GAAG3D,SAAS,CAACyE,SAAS,EAAEY,gBAAgB,CAAC;EACvFd,YAAY,CAACb,aAAa,GAAG2B,gBAAgB;EAC7Cd,YAAY,CAACvC,YAAY,GAAGsD,eAAe;EAC3C,IAAIC,UAAU,EAAE;IACZhB,YAAY,CAACN,UAAU,IAAIsB,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;IACxDhB,YAAY,CAACP,WAAW,IAAIuB,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;EAC7D;EACA,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,WAAW,CAAC9D,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIyE,KAAK,GAAGT,WAAW,CAAChE,CAAC,CAAC;IAC1B,IAAIuF,YAAY,GAAGd,KAAK,CAACc,YAAY;IACrCd,KAAK,CAACnD,KAAK,GAAGyE,QAAQ,CAACR,YAAY,EAAE,EAAE,CAAC,GAAG,GAAG,GAAGpC,YAAY,CAAC7B,KAAK;EACvE;EACA,OAAO6B,YAAY;AACvB;AACA,SAASW,UAAUA,CAACkC,KAAK,EAAEC,GAAG,EAAEvE,KAAK,EAAE4B,QAAQ,EAAEqB,SAAS,EAAE;EACxD,IAAIuB,UAAU,GAAGD,GAAG,KAAK,EAAE;EAC3B,IAAIvB,UAAU,GAAGC,SAAS,IAAIjD,KAAK,CAACkD,IAAI,CAACD,SAAS,CAAC,IAAI,CAAC,CAAC;EACzD,IAAIvC,KAAK,GAAG4D,KAAK,CAAC5D,KAAK;EACvB,IAAI9C,IAAI,GAAGoF,UAAU,CAACpF,IAAI,IAAIoC,KAAK,CAACpC,IAAI;EACxC,IAAI6G,OAAO,GAAG,KAAK;EACnB,IAAIC,QAAQ;EACZ,IAAIC,WAAW;EACf,IAAI/C,QAAQ,EAAE;IACV,IAAIgD,YAAY,GAAG5B,UAAU,CAAC9C,OAAO;IACrC,IAAI2E,aAAa,GAAGD,YAAY,GAAGA,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC;IACxE,IAAI5B,UAAU,CAACpD,KAAK,IAAI,IAAI,IAAIoD,UAAU,CAACpD,KAAK,KAAK,MAAM,EAAE;MACzD,IAAIkF,YAAY,GAAGvH,YAAY,CAACyF,UAAU,CAACpD,KAAK,EAAEgC,QAAQ,CAAChC,KAAK,CAAC,GAAGiF,aAAa;MACjF,IAAInE,KAAK,CAAClC,MAAM,GAAG,CAAC,EAAE;QAClB,IAAIsG,YAAY,GAAGlD,QAAQ,CAACC,UAAU,GAAGD,QAAQ,CAAChC,KAAK,EAAE;UACrD8E,QAAQ,GAAGH,GAAG,CAACpG,KAAK,CAAC,IAAI,CAAC;UAC1BsG,OAAO,GAAG,IAAI;QAClB;MACJ;MACA7C,QAAQ,CAACC,UAAU,GAAGiD,YAAY;IACtC,CAAC,MACI;MACD,IAAIC,GAAG,GAAGpE,QAAQ,CAAC4D,GAAG,EAAE3G,IAAI,EAAEgE,QAAQ,CAAChC,KAAK,EAAEgC,QAAQ,CAACE,QAAQ,EAAEF,QAAQ,CAACC,UAAU,CAAC;MACrFD,QAAQ,CAACC,UAAU,GAAGkD,GAAG,CAAClD,UAAU,GAAGgD,aAAa;MACpDF,WAAW,GAAGI,GAAG,CAACJ,WAAW;MAC7BD,QAAQ,GAAGK,GAAG,CAACrE,KAAK;IACxB;EACJ,CAAC,MACI;IACDgE,QAAQ,GAAGH,GAAG,CAACpG,KAAK,CAAC,IAAI,CAAC;EAC9B;EACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoG,QAAQ,CAAClG,MAAM,EAAEF,CAAC,EAAE,EAAE;IACtC,IAAIZ,IAAI,GAAGgH,QAAQ,CAACpG,CAAC,CAAC;IACtB,IAAIyE,KAAK,GAAG,IAAI3B,aAAa,CAAC,CAAC;IAC/B2B,KAAK,CAACE,SAAS,GAAGA,SAAS;IAC3BF,KAAK,CAACrF,IAAI,GAAGA,IAAI;IACjBqF,KAAK,CAACiC,YAAY,GAAG,CAACtH,IAAI,IAAI,CAAC8G,UAAU;IACzC,IAAI,OAAOxB,UAAU,CAACpD,KAAK,KAAK,QAAQ,EAAE;MACtCmD,KAAK,CAACnD,KAAK,GAAGoD,UAAU,CAACpD,KAAK;IAClC,CAAC,MACI;MACDmD,KAAK,CAACnD,KAAK,GAAG+E,WAAW,GACnBA,WAAW,CAACrG,CAAC,CAAC,GACdhB,QAAQ,CAACI,IAAI,EAAEE,IAAI,CAAC;IAC9B;IACA,IAAI,CAACU,CAAC,IAAI,CAACmG,OAAO,EAAE;MAChB,IAAInD,MAAM,GAAG,CAACZ,KAAK,CAACA,KAAK,CAAClC,MAAM,GAAG,CAAC,CAAC,KAAKkC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAIW,YAAY,CAAC,CAAC,CAAC,EAAEC,MAAM;MAChF,IAAI2D,SAAS,GAAG3D,MAAM,CAAC9C,MAAM;MAC5ByG,SAAS,KAAK,CAAC,IAAI3D,MAAM,CAAC,CAAC,CAAC,CAAC0D,YAAY,GACnC1D,MAAM,CAAC,CAAC,CAAC,GAAGyB,KAAK,GACjB,CAACrF,IAAI,IAAI,CAACuH,SAAS,IAAIT,UAAU,KAAKlD,MAAM,CAACwC,IAAI,CAACf,KAAK,CAAE;IACpE,CAAC,MACI;MACDrC,KAAK,CAACoD,IAAI,CAAC,IAAIzC,YAAY,CAAC,CAAC0B,KAAK,CAAC,CAAC,CAAC;IACzC;EACJ;AACJ;AACA,SAASmC,kBAAkBA,CAACC,EAAE,EAAE;EAC5B,IAAIC,IAAI,GAAGD,EAAE,CAACrF,UAAU,CAAC,CAAC,CAAC;EAC3B,OAAOsF,IAAI,IAAI,IAAI,IAAIA,IAAI,IAAI,KAAK,IAC7BA,IAAI,IAAI,KAAK,IAAIA,IAAI,IAAI,MAAM,IAC/BA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,IAChCA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM;AAC3C;AACA,IAAIC,YAAY,GAAGjI,MAAM,CAAC,SAAS,CAACe,KAAK,CAAC,EAAE,CAAC,EAAE,UAAUmH,GAAG,EAAEH,EAAE,EAAE;EAC9DG,GAAG,CAACH,EAAE,CAAC,GAAG,IAAI;EACd,OAAOG,GAAG;AACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACN,SAASC,eAAeA,CAACJ,EAAE,EAAE;EACzB,IAAID,kBAAkB,CAACC,EAAE,CAAC,EAAE;IACxB,IAAIE,YAAY,CAACF,EAAE,CAAC,EAAE;MAClB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA,SAASxE,QAAQA,CAACjD,IAAI,EAAEE,IAAI,EAAE0B,SAAS,EAAEkG,UAAU,EAAEC,cAAc,EAAE;EACjE,IAAI/E,KAAK,GAAG,EAAE;EACd,IAAIiE,WAAW,GAAG,EAAE;EACpB,IAAI9B,IAAI,GAAG,EAAE;EACb,IAAI6C,WAAW,GAAG,EAAE;EACpB,IAAIC,gBAAgB,GAAG,CAAC;EACxB,IAAI9D,UAAU,GAAG,CAAC;EAClB,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,IAAI,CAACc,MAAM,EAAEF,CAAC,EAAE,EAAE;IAClC,IAAI6G,EAAE,GAAGzH,IAAI,CAACkG,MAAM,CAACtF,CAAC,CAAC;IACvB,IAAI6G,EAAE,KAAK,IAAI,EAAE;MACb,IAAIO,WAAW,EAAE;QACb7C,IAAI,IAAI6C,WAAW;QACnB7D,UAAU,IAAI8D,gBAAgB;MAClC;MACAjF,KAAK,CAACoD,IAAI,CAACjB,IAAI,CAAC;MAChB8B,WAAW,CAACb,IAAI,CAACjC,UAAU,CAAC;MAC5BgB,IAAI,GAAG,EAAE;MACT6C,WAAW,GAAG,EAAE;MAChBC,gBAAgB,GAAG,CAAC;MACpB9D,UAAU,GAAG,CAAC;MACd;IACJ;IACA,IAAI+D,OAAO,GAAGtI,QAAQ,CAAC6H,EAAE,EAAEvH,IAAI,CAAC;IAChC,IAAIiI,MAAM,GAAGL,UAAU,GAAG,KAAK,GAAG,CAACD,eAAe,CAACJ,EAAE,CAAC;IACtD,IAAI,CAACzE,KAAK,CAAClC,MAAM,GACXiH,cAAc,GAAG5D,UAAU,GAAG+D,OAAO,GAAGtG,SAAS,GACjDuC,UAAU,GAAG+D,OAAO,GAAGtG,SAAS,EAAE;MACpC,IAAI,CAACuC,UAAU,EAAE;QACb,IAAIgE,MAAM,EAAE;UACRnF,KAAK,CAACoD,IAAI,CAAC4B,WAAW,CAAC;UACvBf,WAAW,CAACb,IAAI,CAAC6B,gBAAgB,CAAC;UAClCD,WAAW,GAAGP,EAAE;UAChBQ,gBAAgB,GAAGC,OAAO;QAC9B,CAAC,MACI;UACDlF,KAAK,CAACoD,IAAI,CAACqB,EAAE,CAAC;UACdR,WAAW,CAACb,IAAI,CAAC8B,OAAO,CAAC;QAC7B;MACJ,CAAC,MACI,IAAI/C,IAAI,IAAI6C,WAAW,EAAE;QAC1B,IAAIG,MAAM,EAAE;UACR,IAAI,CAAChD,IAAI,EAAE;YACPA,IAAI,GAAG6C,WAAW;YAClBA,WAAW,GAAG,EAAE;YAChBC,gBAAgB,GAAG,CAAC;YACpB9D,UAAU,GAAG8D,gBAAgB;UACjC;UACAjF,KAAK,CAACoD,IAAI,CAACjB,IAAI,CAAC;UAChB8B,WAAW,CAACb,IAAI,CAACjC,UAAU,GAAG8D,gBAAgB,CAAC;UAC/CD,WAAW,IAAIP,EAAE;UACjBQ,gBAAgB,IAAIC,OAAO;UAC3B/C,IAAI,GAAG,EAAE;UACThB,UAAU,GAAG8D,gBAAgB;QACjC,CAAC,MACI;UACD,IAAID,WAAW,EAAE;YACb7C,IAAI,IAAI6C,WAAW;YACnBA,WAAW,GAAG,EAAE;YAChBC,gBAAgB,GAAG,CAAC;UACxB;UACAjF,KAAK,CAACoD,IAAI,CAACjB,IAAI,CAAC;UAChB8B,WAAW,CAACb,IAAI,CAACjC,UAAU,CAAC;UAC5BgB,IAAI,GAAGsC,EAAE;UACTtD,UAAU,GAAG+D,OAAO;QACxB;MACJ;MACA;IACJ;IACA/D,UAAU,IAAI+D,OAAO;IACrB,IAAIC,MAAM,EAAE;MACRH,WAAW,IAAIP,EAAE;MACjBQ,gBAAgB,IAAIC,OAAO;IAC/B,CAAC,MACI;MACD,IAAIF,WAAW,EAAE;QACb7C,IAAI,IAAI6C,WAAW;QACnBA,WAAW,GAAG,EAAE;QAChBC,gBAAgB,GAAG,CAAC;MACxB;MACA9C,IAAI,IAAIsC,EAAE;IACd;EACJ;EACA,IAAI,CAACzE,KAAK,CAAClC,MAAM,IAAI,CAACqE,IAAI,EAAE;IACxBA,IAAI,GAAGnF,IAAI;IACXgI,WAAW,GAAG,EAAE;IAChBC,gBAAgB,GAAG,CAAC;EACxB;EACA,IAAID,WAAW,EAAE;IACb7C,IAAI,IAAI6C,WAAW;EACvB;EACA,IAAI7C,IAAI,EAAE;IACNnC,KAAK,CAACoD,IAAI,CAACjB,IAAI,CAAC;IAChB8B,WAAW,CAACb,IAAI,CAACjC,UAAU,CAAC;EAChC;EACA,IAAInB,KAAK,CAAClC,MAAM,KAAK,CAAC,EAAE;IACpBqD,UAAU,IAAI4D,cAAc;EAChC;EACA,OAAO;IACH5D,UAAU,EAAEA,UAAU;IACtBnB,KAAK,EAAEA,KAAK;IACZiE,WAAW,EAAEA;EACjB,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}