{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet scene = null; // 添加scene全局变量\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm'\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false); // 添加状态跟踪车辆是否加载\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controls.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controls.target);\n        controls.update();\n      }).start();\n\n      // 启用控制器\n      controls.enabled = true;\n\n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    try {\n      const messageData = JSON.parse(message.toString());\n\n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        var _messageData$data;\n        console.log('收到RSM消息:', messageData);\n        const participants = ((_messageData$data = messageData.data) === null || _messageData$data === void 0 ? void 0 : _messageData$data.participants) || [];\n        const vehicles = participants.filter(p => p.partPtcType === '1');\n        if (vehicles.length > 0) {\n          console.log('RSM消息中的机动车信息:', vehicles);\n\n          // 创建当前消息中的车辆ID集合\n          const currentVehicleIds = new Set(vehicles.map(v => v.partPtcId));\n\n          // 记录上一次更新的时间\n          const now = Date.now();\n\n          // 处理每个车辆\n          vehicles.forEach(async vehicle => {\n            const vehicleId = vehicle.partPtcId;\n\n            // 解析位置和状态信息\n            const vehicleState = {\n              longitude: parseFloat(vehicle.partPosLong),\n              latitude: parseFloat(vehicle.partPosLat),\n              speed: parseFloat(vehicle.partSpeed),\n              heading: parseFloat(vehicle.partHeading)\n            };\n            const modelPos = converter.current.wgs84ToModel(vehicleState.longitude, vehicleState.latitude);\n\n            // 获取或创建车辆模型\n            let vehicleModel = vehicleModels.get(vehicleId);\n            if (!vehicleModel && preloadedVehicleModel) {\n              // 创建新车辆\n              vehicleModel = preloadedVehicleModel.clone();\n              vehicleModel.position.set(modelPos.x, 0.5, -modelPos.y);\n              vehicleModel.rotation.y = Math.PI - vehicleState.heading * Math.PI / 180;\n              scene.add(vehicleModel);\n              vehicleModels.set(vehicleId, {\n                model: vehicleModel,\n                lastUpdate: now\n              });\n            } else if (vehicleModel) {\n              // 更新现有车辆\n              // 使用TWEEN创建平滑过渡\n              new TWEEN.Tween(vehicleModel.model.position).to({\n                x: modelPos.x,\n                y: 0.5,\n                z: -modelPos.y\n              }, 50) // 增加过渡时间到200ms\n              .easing(TWEEN.Easing.Quadratic.Out).start();\n              new TWEEN.Tween(vehicleModel.model.rotation).to({\n                y: Math.PI - vehicleState.heading * Math.PI / 180\n              }, 50).easing(TWEEN.Easing.Quadratic.Out).start();\n\n              // 更新最后更新时间\n              vehicleModel.lastUpdate = now;\n            }\n          });\n\n          // 延迟清理消失的车辆\n          // 只有超过2秒没有更新的车辆才会被移除\n          const CLEANUP_THRESHOLD = 2000; // 2秒\n\n          vehicleModels.forEach((vehicleData, id) => {\n            if (now - vehicleData.lastUpdate > CLEANUP_THRESHOLD && !currentVehicleIds.has(id)) {\n              scene.remove(vehicleData.model);\n              vehicleModels.delete(id);\n              console.log(`移除长时间未更新的车辆: ID ${id}`);\n            }\n          });\n        }\n        return;\n      }\n\n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        const bsmData = messageData.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        console.log('解析后的车辆状态:', newState);\n\n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n\n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n\n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);\n        }\n        return;\n      }\n\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n\n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    ws.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n\n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n\n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n\n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    ws.onerror = error => {\n      console.error('WebSocket错误:', error);\n    };\n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n\n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载车辆模型\n    preloadVehicleModel();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n\n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);\n    camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n\n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({\n      antialias: true\n    });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n\n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n\n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n\n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(`${BASE_URL}/changli2/Audi R8.glb`, gltf => {\n          const vehicleModel = gltf.scene;\n\n          // 创建一个新的Group作为根容器\n          const vehicleContainer = new THREE.Group();\n\n          // 调整模型材质\n          vehicleModel.traverse(child => {\n            if (child.isMesh) {\n              // 检查并调整材质\n              if (child.material) {\n                // 创建新的标准材质\n                const newMaterial = new THREE.MeshStandardMaterial({\n                  color: 0xffffff,\n                  // 白色\n                  metalness: 0.2,\n                  // 降低金属感\n                  roughness: 0.1,\n                  // 降低粗糙度\n                  envMapIntensity: 1.0 // 环境贴图强度\n                });\n\n                // 保留原始贴图\n                if (child.material.map) {\n                  newMaterial.map = child.material.map;\n                }\n\n                // 应用新材质\n                child.material = newMaterial;\n                console.log('已调整车辆材质:', child.name);\n              }\n            }\n          });\n\n          // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n          while (vehicleModel.children.length > 0) {\n            const child = vehicleModel.children[0];\n            vehicleContainer.add(child);\n          }\n\n          // 确保容器直接添加到场景根节点\n          scene.add(vehicleContainer);\n\n          // 保存容器的引用\n          globalVehicleRef = vehicleContainer;\n          console.log('车辆模型加载成功，使用容器包装');\n          setIsVehicleLoaded(true);\n          resolve(vehicleContainer);\n        }, xhr => {\n          console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n        }, reject);\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n\n        // 2. 初始化MQTT客户端\n        initMqttClient();\n\n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.rotation.y = Math.PI - initialState.heading * Math.PI / 180;\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = retriesLeft => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          const loader = new GLTFLoader();\n          loader.load(url, gltf => {\n            console.log(`模型加载成功: ${url}`);\n            resolve(gltf);\n          }, xhr => {\n            console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          }, error => {\n            console.error(`加载失败: ${url}`, error);\n            if (retriesLeft > 0) {\n              console.log(`将在 1 秒后重试...`);\n              setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n            } else {\n              reject(error);\n            }\n          });\n        };\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`, async gltf => {\n      try {\n        const model = gltf.scene;\n        model.scale.set(1, 1, 1);\n        model.position.set(0, 0, 0);\n        scene.add(model);\n\n        // 在校园模型加载完成后初始化场景\n        await initializeScene();\n      } catch (error) {\n        console.error('处理模型时出错:', error);\n      }\n    }, xhr => {\n      console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n    }, error => {\n      console.error('模型加载错误:', error);\n      console.error('错误详情:', {\n        错误类型: error.type,\n        错误消息: error.message,\n        加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n        完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n      });\n    });\n\n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n\n      // 更新 TWEEN 动画\n      TWEEN.update();\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n\n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n\n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI / 2 * 3;\n        // const adjustedRotation = Math.PI/2;\n\n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(-50 * Math.cos(adjustedRotation), 15, -50 * Math.sin(adjustedRotation));\n\n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n\n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n\n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n\n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n\n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n        // 禁用控制器\n        controls.enabled = false;\n\n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n\n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n\n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      controls.update();\n      renderer.render(scene, camera);\n    };\n    animate();\n\n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n\n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n\n    // 清理函数\n    return () => {\n      var _containerRef$current;\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.end();\n      }\n      window.removeEventListener('resize', handleResize);\n      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 ? void 0 : _containerRef$current.removeChild(renderer.domElement);\n      renderer.dispose();\n\n      // 清理场景中的所有车辆模型\n      vehicleModels.forEach((model, id) => {\n        scene.remove(model);\n      });\n      vehicleModels.clear();\n\n      // 清理场景\n      while (scene.children.length > 0) {\n        scene.remove(scene.children[0]);\n      }\n      scene = null; // 重置scene\n      preloadedVehicleModel = null; // 重置预加载的模型\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 718,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 720,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 730,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 719,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"pok6xuZ9wkLpUfjRdkdxrFsX29M=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadVehicleModel = async () => {\n  try {\n    console.log('开始预加载车辆模型...');\n    const vehicleLoader = new GLTFLoader();\n    const gltf = await vehicleLoader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n    const vehicleModel = gltf.scene;\n\n    // 调整模型材质\n    vehicleModel.traverse(child => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n      }\n    });\n\n    // 存储预加载的模型\n    preloadedVehicleModel = vehicleModel;\n    console.log('车辆模型预加载成功');\n  } catch (error) {\n    console.error('车辆模型预加载失败:', error);\n  }\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "scene", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "topics", "bsm", "rsm", "BASE_URL", "vehicleModels", "Map", "CampusModel", "_s", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "isVehicleLoaded", "setIsVehicleLoaded", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "switchToFollowView", "enabled", "switchToGlobalView", "current", "currentPos", "clone", "currentUp", "up", "Tween", "to", "x", "y", "z", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "Math", "PI", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "handleMqttMessage", "topic", "message", "error", "toString", "messageData", "JSON", "parse", "type", "_messageData$data", "participants", "data", "vehicles", "filter", "p", "partPtcType", "length", "currentVehicleIds", "Set", "map", "v", "partPtcId", "now", "Date", "for<PERSON>ach", "vehicle", "vehicleId", "parseFloat", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "wgs84ToModel", "vehicleModel", "get", "set", "rotation", "add", "model", "lastUpdate", "Out", "CLEANUP_THRESHOLD", "vehicleData", "id", "has", "remove", "delete", "bsmData", "newState", "partLong", "partLat", "newPosition", "Vector3", "updateMatrix", "updateMatrixWorld", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "event", "payload", "stringify", "onerror", "onclose", "setTimeout", "preloadVehicleModel", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "distance", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleContainer", "Group", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "newMaterial", "MeshStandardMaterial", "color", "metalness", "roughness", "envMapIntensity", "name", "children", "xhr", "loaded", "total", "toFixed", "initializeScene", "initialState", "initialPos", "loadModelWithRetry", "url", "maxRetries", "attemptLoad", "retriesLeft", "loader", "scale", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "lookAtTarget", "updateProjectionMatrix", "车辆位置", "toArray", "相机位置", "相机目标", "相机朝向", "getWorldDirection", "abs", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "_containerRef$current", "clearInterval", "end", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "clear", "ref", "style", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "createTextSprite", "text", "canvas", "document", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "transparent", "sprite", "Sprite", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "e", "loadAsync", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet scene = null; // 添加scene全局变量\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  topics: {\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm'\n  }\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);  // 添加状态跟踪车辆是否加载\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controls) {\n      controls.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controls) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controls.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controls.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controls.target);\n          controls.update();\n        })\n        .start();\n\n      // 启用控制器\n      controls.enabled = true;\n      \n      // 重置控制器的一些属性\n      controls.minDistance = 50;\n      controls.maxDistance = 500;\n      controls.maxPolarAngle = Math.PI / 2.1;\n      controls.minPolarAngle = 0;\n      controls.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    if (!scene) {\n      console.error('场景未初始化');\n      return;\n    }\n    \n    console.log('收到原始消息:', {\n      topic,\n      message: message.toString()\n    });\n    \n    try {\n      const messageData = JSON.parse(message.toString());\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.topics.rsm && messageData.type === 'RSM') {\n        console.log('收到RSM消息:', messageData);\n        \n        const participants = messageData.data?.participants || [];\n        const vehicles = participants.filter(p => p.partPtcType === '1');\n        \n        if (vehicles.length > 0) {\n          console.log('RSM消息中的机动车信息:', vehicles);\n          \n          // 创建当前消息中的车辆ID集合\n          const currentVehicleIds = new Set(vehicles.map(v => v.partPtcId));\n          \n          // 记录上一次更新的时间\n          const now = Date.now();\n          \n          // 处理每个车辆\n          vehicles.forEach(async (vehicle) => {\n            const vehicleId = vehicle.partPtcId;\n            \n            // 解析位置和状态信息\n            const vehicleState = {\n              longitude: parseFloat(vehicle.partPosLong),\n              latitude: parseFloat(vehicle.partPosLat),\n              speed: parseFloat(vehicle.partSpeed),\n              heading: parseFloat(vehicle.partHeading)\n            };\n            \n            const modelPos = converter.current.wgs84ToModel(\n              vehicleState.longitude, \n              vehicleState.latitude\n            );\n            \n            // 获取或创建车辆模型\n            let vehicleModel = vehicleModels.get(vehicleId);\n            \n            if (!vehicleModel && preloadedVehicleModel) {\n              // 创建新车辆\n              vehicleModel = preloadedVehicleModel.clone();\n              vehicleModel.position.set(modelPos.x, 0.5, -modelPos.y);\n              vehicleModel.rotation.y = Math.PI - vehicleState.heading * Math.PI / 180;\n              scene.add(vehicleModel);\n              vehicleModels.set(vehicleId, {\n                model: vehicleModel,\n                lastUpdate: now\n              });\n            } else if (vehicleModel) {\n              // 更新现有车辆\n              // 使用TWEEN创建平滑过渡\n              new TWEEN.Tween(vehicleModel.model.position)\n                .to({\n                  x: modelPos.x,\n                  y: 0.5,\n                  z: -modelPos.y\n                }, 50) // 增加过渡时间到200ms\n                .easing(TWEEN.Easing.Quadratic.Out)\n                .start();\n              \n              new TWEEN.Tween(vehicleModel.model.rotation)\n                .to({\n                  y: Math.PI - vehicleState.heading * Math.PI / 180\n                }, 50)\n                .easing(TWEEN.Easing.Quadratic.Out)\n                .start();\n              \n              // 更新最后更新时间\n              vehicleModel.lastUpdate = now;\n            }\n          });\n          \n          // 延迟清理消失的车辆\n          // 只有超过2秒没有更新的车辆才会被移除\n          const CLEANUP_THRESHOLD = 2000; // 2秒\n          \n          vehicleModels.forEach((vehicleData, id) => {\n            if (now - vehicleData.lastUpdate > CLEANUP_THRESHOLD && !currentVehicleIds.has(id)) {\n              scene.remove(vehicleData.model);\n              vehicleModels.delete(id);\n              console.log(`移除长时间未更新的车辆: ID ${id}`);\n            }\n          });\n        }\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.topics.bsm && messageData.type === 'BSM') {\n        console.log('收到BSM消息:', messageData);\n        \n        const bsmData = messageData.data;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        console.log('解析后的车辆状态:', newState);\n        \n        // 更新车辆位置和状态\n        if (globalVehicleRef) {\n          const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n          const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n          \n          // 立即更新位置\n          globalVehicleRef.position.copy(newPosition);\n          globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n          globalVehicleRef.updateMatrix();\n          globalVehicleRef.updateMatrixWorld(true);\n          \n          // 更新状态\n          setVehicleState(newState);\n          console.log('车辆位置已更新:', newPosition);\n        }\n        return;\n      }\n      \n      // 如果不是RSM或BSM消息，则记录为其他类型\n      console.log('未知类型消息:', {\n        topic,\n        type: messageData.type,\n        data: messageData\n      });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message.toString());\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载车辆模型\n    preloadVehicleModel();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/Audi R8.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        if (vehicleContainer) {\n          const initialState = {\n            longitude: 113.0022348,\n            latitude: 28.0698301,\n            heading: 0\n          };\n          const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n          vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n          vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n          vehicleContainer.updateMatrix();\n          vehicleContainer.updateMatrixWorld(true);\n          currentPosition = vehicleContainer.position.clone();\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n      \n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y ;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        // const adjustedRotation = Math.PI/2;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          15,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 设置相机偏移（更接近车辆）\n        // const cameraOffset = new THREE.Vector3(0, 10, 20); // 减小距离和高度\n        \n        // 设置相机位置\n        camera.position.copy(vehiclePos).add(cameraOffset);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 直接设置相机的目标\n        const lookAtTarget = vehiclePos.clone();\n        camera.lookAt(lookAtTarget);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(vehiclePos);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lookAtTarget.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n      }\n      \n      controls.update();\n      renderer.render(scene, camera);\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 清理函数\n    return () => {\n      if (globalUpdateInterval) {\n        clearInterval(globalUpdateInterval);\n        globalUpdateInterval = null;\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.end();\n      }\n      window.removeEventListener('resize', handleResize);\n      containerRef.current?.removeChild(renderer.domElement);\n      renderer.dispose();\n      \n      // 清理场景中的所有车辆模型\n      vehicleModels.forEach((model, id) => {\n        scene.remove(model);\n      });\n      vehicleModels.clear();\n      \n      // 清理场景\n      while(scene.children.length > 0) { \n        scene.remove(scene.children[0]); \n      }\n      \n      scene = null; // 重置scene\n      preloadedVehicleModel = null; // 重置预加载的模型\n    };\n  }, []);\n\n  return (\n    <>\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n// 修改车辆模型预加载函数\nconst preloadVehicleModel = async () => {\n  try {\n    console.log('开始预加载车辆模型...');\n    const vehicleLoader = new GLTFLoader();\n    const gltf = await vehicleLoader.loadAsync(`${BASE_URL}/changli2/Audi R8.glb`);\n    const vehicleModel = gltf.scene;\n    \n    // 调整模型材质\n    vehicleModel.traverse((child) => {\n      if (child.isMesh) {\n        child.material = new THREE.MeshStandardMaterial({\n          color: 0xffffff,\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n      }\n    });\n    \n    // 存储预加载的模型\n    preloadedVehicleModel = vehicleModel;\n    console.log('车辆模型预加载成功');\n  } catch (error) {\n    console.error('车辆模型预加载失败:', error);\n  }\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;;AAErB;AACA,IAAIC,qBAAqB,GAAG,IAAI;AAChC,IAAIC,KAAK,GAAG,IAAI,CAAC,CAAC;;AAElB;AACA,MAAMC,WAAW,GAAG;EAClBC,MAAM,EAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ;EAChCC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE;IACNC,GAAG,EAAE,2BAA2B;IAChCC,GAAG,EAAE;EACP;AACF,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEjC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAGrC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMsC,UAAU,GAAGtC,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMuC,SAAS,GAAGvC,MAAM,CAAC,IAAIK,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMmC,aAAa,GAAGxC,MAAM,CAAC,EAAE,CAAC;EAChC,MAAMyC,eAAe,GAAGzC,MAAM,CAAC,CAAC,CAAC;EACjC,MAAM0C,aAAa,GAAG1C,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAE;;EAEhE;EACA,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC;IAC/C8C,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAMoD,oBAAoB,GAAG;IAC3BC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,SAAS,GAAGtE,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMuE,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnB,WAAW,CAAC,QAAQ,CAAC;IACrBjC,UAAU,GAAG,QAAQ;IAErB,IAAIC,QAAQ,EAAE;MACZA,QAAQ,CAACoD,OAAO,GAAG,KAAK;IAC1B;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrB,WAAW,CAAC,QAAQ,CAAC;IACrBjC,UAAU,GAAG,QAAQ;IAErB,IAAImD,SAAS,CAACI,OAAO,IAAItD,QAAQ,EAAE;MACjC;MACA,MAAMuD,UAAU,GAAGL,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACsB,KAAK,CAAC,CAAC;MACrD,MAAMC,SAAS,GAAGP,SAAS,CAACI,OAAO,CAACI,EAAE,CAACF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAItE,KAAK,CAACyE,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCC,MAAM,CAAC9E,KAAK,CAAC+E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdlB,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACmC,IAAI,CAACd,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDe,KAAK,CAAC,CAAC;;MAEV;MACA,IAAIpF,KAAK,CAACyE,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC9E,KAAK,CAAC+E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdlB,SAAS,CAACI,OAAO,CAACI,EAAE,CAACW,IAAI,CAACZ,SAAS,CAAC;MACtC,CAAC,CAAC,CACDa,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAGvE,QAAQ,CAACwE,MAAM,CAAChB,KAAK,CAAC,CAAC;;MAE7C;MACA,IAAItE,KAAK,CAACyE,KAAK,CAACY,aAAa,CAAC,CAC3BX,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAAC9E,KAAK,CAAC+E,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdpE,QAAQ,CAACwE,MAAM,CAACH,IAAI,CAACE,aAAa,CAAC;QACnC;QACArB,SAAS,CAACI,OAAO,CAACmB,MAAM,CAACzE,QAAQ,CAACwE,MAAM,CAAC;QACzCxE,QAAQ,CAAC0E,MAAM,CAAC,CAAC;MACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACAtE,QAAQ,CAACoD,OAAO,GAAG,IAAI;;MAEvB;MACApD,QAAQ,CAAC2E,WAAW,GAAG,EAAE;MACzB3E,QAAQ,CAAC4E,WAAW,GAAG,GAAG;MAC1B5E,QAAQ,CAAC6E,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;MACtC/E,QAAQ,CAACgF,aAAa,GAAG,CAAC;MAC1BhF,QAAQ,CAAC0E,MAAM,CAAC,CAAC;MAEjBO,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC5C,IAAI,CAACtF,KAAK,EAAE;MACV+E,OAAO,CAACQ,KAAK,CAAC,QAAQ,CAAC;MACvB;IACF;IAEAR,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;MACrBK,KAAK;MACLC,OAAO,EAAEA,OAAO,CAACE,QAAQ,CAAC;IAC5B,CAAC,CAAC;IAEF,IAAI;MACF,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACL,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;;MAElD;MACA,IAAIH,KAAK,KAAKpF,WAAW,CAACM,MAAM,CAACE,GAAG,IAAIgF,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAAA,IAAAC,iBAAA;QAClEd,OAAO,CAACC,GAAG,CAAC,UAAU,EAAES,WAAW,CAAC;QAEpC,MAAMK,YAAY,GAAG,EAAAD,iBAAA,GAAAJ,WAAW,CAACM,IAAI,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBC,YAAY,KAAI,EAAE;QACzD,MAAME,QAAQ,GAAGF,YAAY,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAK,GAAG,CAAC;QAEhE,IAAIH,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAE;UACvBrB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgB,QAAQ,CAAC;;UAEtC;UACA,MAAMK,iBAAiB,GAAG,IAAIC,GAAG,CAACN,QAAQ,CAACO,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,CAAC,CAAC;;UAEjE;UACA,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;UAEtB;UACAV,QAAQ,CAACY,OAAO,CAAC,MAAOC,OAAO,IAAK;YAClC,MAAMC,SAAS,GAAGD,OAAO,CAACJ,SAAS;;YAEnC;YACA,MAAMlF,YAAY,GAAG;cACnBE,SAAS,EAAEsF,UAAU,CAACF,OAAO,CAACG,WAAW,CAAC;cAC1CtF,QAAQ,EAAEqF,UAAU,CAACF,OAAO,CAACI,UAAU,CAAC;cACxCtF,KAAK,EAAEoF,UAAU,CAACF,OAAO,CAACK,SAAS,CAAC;cACpCtF,OAAO,EAAEmF,UAAU,CAACF,OAAO,CAACM,WAAW;YACzC,CAAC;YAED,MAAMC,QAAQ,GAAGnG,SAAS,CAACmC,OAAO,CAACiE,YAAY,CAC7C9F,YAAY,CAACE,SAAS,EACtBF,YAAY,CAACG,QACf,CAAC;;YAED;YACA,IAAI4F,YAAY,GAAG3G,aAAa,CAAC4G,GAAG,CAACT,SAAS,CAAC;YAE/C,IAAI,CAACQ,YAAY,IAAIvH,qBAAqB,EAAE;cAC1C;cACAuH,YAAY,GAAGvH,qBAAqB,CAACuD,KAAK,CAAC,CAAC;cAC5CgE,YAAY,CAACtF,QAAQ,CAACwF,GAAG,CAACJ,QAAQ,CAACzD,CAAC,EAAE,GAAG,EAAE,CAACyD,QAAQ,CAACxD,CAAC,CAAC;cACvD0D,YAAY,CAACG,QAAQ,CAAC7D,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGtD,YAAY,CAACK,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAG;cACxE7E,KAAK,CAAC0H,GAAG,CAACJ,YAAY,CAAC;cACvB3G,aAAa,CAAC6G,GAAG,CAACV,SAAS,EAAE;gBAC3Ba,KAAK,EAAEL,YAAY;gBACnBM,UAAU,EAAElB;cACd,CAAC,CAAC;YACJ,CAAC,MAAM,IAAIY,YAAY,EAAE;cACvB;cACA;cACA,IAAItI,KAAK,CAACyE,KAAK,CAAC6D,YAAY,CAACK,KAAK,CAAC3F,QAAQ,CAAC,CACzC0B,EAAE,CAAC;gBACFC,CAAC,EAAEyD,QAAQ,CAACzD,CAAC;gBACbC,CAAC,EAAE,GAAG;gBACNC,CAAC,EAAE,CAACuD,QAAQ,CAACxD;cACf,CAAC,EAAE,EAAE,CAAC,CAAC;cAAA,CACNE,MAAM,CAAC9E,KAAK,CAAC+E,MAAM,CAACC,SAAS,CAAC6D,GAAG,CAAC,CAClCzD,KAAK,CAAC,CAAC;cAEV,IAAIpF,KAAK,CAACyE,KAAK,CAAC6D,YAAY,CAACK,KAAK,CAACF,QAAQ,CAAC,CACzC/D,EAAE,CAAC;gBACFE,CAAC,EAAEgB,IAAI,CAACC,EAAE,GAAGtD,YAAY,CAACK,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG;cAChD,CAAC,EAAE,EAAE,CAAC,CACLf,MAAM,CAAC9E,KAAK,CAAC+E,MAAM,CAACC,SAAS,CAAC6D,GAAG,CAAC,CAClCzD,KAAK,CAAC,CAAC;;cAEV;cACAkD,YAAY,CAACM,UAAU,GAAGlB,GAAG;YAC/B;UACF,CAAC,CAAC;;UAEF;UACA;UACA,MAAMoB,iBAAiB,GAAG,IAAI,CAAC,CAAC;;UAEhCnH,aAAa,CAACiG,OAAO,CAAC,CAACmB,WAAW,EAAEC,EAAE,KAAK;YACzC,IAAItB,GAAG,GAAGqB,WAAW,CAACH,UAAU,GAAGE,iBAAiB,IAAI,CAACzB,iBAAiB,CAAC4B,GAAG,CAACD,EAAE,CAAC,EAAE;cAClFhI,KAAK,CAACkI,MAAM,CAACH,WAAW,CAACJ,KAAK,CAAC;cAC/BhH,aAAa,CAACwH,MAAM,CAACH,EAAE,CAAC;cACxBjD,OAAO,CAACC,GAAG,CAAC,mBAAmBgD,EAAE,EAAE,CAAC;YACtC;UACF,CAAC,CAAC;QACJ;QACA;MACF;;MAEA;MACA,IAAI3C,KAAK,KAAKpF,WAAW,CAACM,MAAM,CAACC,GAAG,IAAIiF,WAAW,CAACG,IAAI,KAAK,KAAK,EAAE;QAClEb,OAAO,CAACC,GAAG,CAAC,UAAU,EAAES,WAAW,CAAC;QAEpC,MAAM2C,OAAO,GAAG3C,WAAW,CAACM,IAAI;QAChC,MAAMsC,QAAQ,GAAG;UACf5G,SAAS,EAAEsF,UAAU,CAACqB,OAAO,CAACE,QAAQ,CAAC;UACvC5G,QAAQ,EAAEqF,UAAU,CAACqB,OAAO,CAACG,OAAO,CAAC;UACrC5G,KAAK,EAAEoF,UAAU,CAACqB,OAAO,CAAClB,SAAS,CAAC;UACpCtF,OAAO,EAAEmF,UAAU,CAACqB,OAAO,CAACjB,WAAW;QACzC,CAAC;QAEDpC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEqD,QAAQ,CAAC;;QAElC;QACA,IAAI/I,gBAAgB,EAAE;UACpB,MAAM8H,QAAQ,GAAGnG,SAAS,CAACmC,OAAO,CAACiE,YAAY,CAACgB,QAAQ,CAAC5G,SAAS,EAAE4G,QAAQ,CAAC3G,QAAQ,CAAC;UACtF,MAAM8G,WAAW,GAAG,IAAI5J,KAAK,CAAC6J,OAAO,CAACrB,QAAQ,CAACzD,CAAC,EAAE,GAAG,EAAE,CAACyD,QAAQ,CAACxD,CAAC,CAAC;;UAEnE;UACAtE,gBAAgB,CAAC0C,QAAQ,CAACmC,IAAI,CAACqE,WAAW,CAAC;UAC3ClJ,gBAAgB,CAACmI,QAAQ,CAAC7D,CAAC,GAAGgB,IAAI,CAACC,EAAE,GAAGwD,QAAQ,CAACzG,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAG;UACxEvF,gBAAgB,CAACoJ,YAAY,CAAC,CAAC;UAC/BpJ,gBAAgB,CAACqJ,iBAAiB,CAAC,IAAI,CAAC;;UAExC;UACAnH,eAAe,CAAC6G,QAAQ,CAAC;UACzBtD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEwD,WAAW,CAAC;QACtC;QACA;MACF;;MAEA;MACAzD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBK,KAAK;QACLO,IAAI,EAAEH,WAAW,CAACG,IAAI;QACtBG,IAAI,EAAEN;MACR,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnCR,OAAO,CAACQ,KAAK,CAAC,SAAS,EAAED,OAAO,CAACE,QAAQ,CAAC,CAAC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMoD,cAAc,GAAGA,CAAA,KAAM;IAC3B7D,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAE7B,MAAM6D,KAAK,GAAG,QAAQ5I,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO;IACnEyE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE6D,KAAK,CAAC;;IAEpC;IACA,MAAMC,EAAE,GAAG,IAAIC,SAAS,CAACF,KAAK,CAAC;IAE/BC,EAAE,CAACE,MAAM,GAAG,MAAM;MAChBjE,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED8D,EAAE,CAACG,SAAS,GAAIC,KAAK,IAAK;MACxB,IAAI;QACF,MAAM5D,OAAO,GAAGI,IAAI,CAACC,KAAK,CAACuD,KAAK,CAACnD,IAAI,CAAC;;QAEtC;QACA,IAAIT,OAAO,CAACM,IAAI,KAAK,SAAS,EAAE;UAC9Bb,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEM,OAAO,CAAC;UAC/B;QACF;;QAEA;QACA,IAAIA,OAAO,CAACM,IAAI,KAAK,MAAM,EAAE;UAC3B;QACF;;QAEA;QACA,IAAIN,OAAO,CAACM,IAAI,KAAK,SAAS,IAAIN,OAAO,CAACD,KAAK,IAAIC,OAAO,CAAC6D,OAAO,EAAE;UAClE;UACA/D,iBAAiB,CAACE,OAAO,CAACD,KAAK,EAAEK,IAAI,CAAC0D,SAAS,CAAC9D,OAAO,CAAC6D,OAAO,CAAC,CAAC;QACnE;MACF,CAAC,CAAC,OAAO5D,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDuD,EAAE,CAACO,OAAO,GAAI9D,KAAK,IAAK;MACtBR,OAAO,CAACQ,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACtC,CAAC;IAEDuD,EAAE,CAACQ,OAAO,GAAG,MAAM;MACjBvE,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACAuE,UAAU,CAACX,cAAc,EAAE,IAAI,CAAC;IAClC,CAAC;;IAED;IACAxH,aAAa,CAACgC,OAAO,GAAG0F,EAAE;EAC5B,CAAC;EAEDrK,SAAS,CAAC,MAAM;IACd,IAAI,CAACsC,YAAY,CAACqC,OAAO,EAAE;;IAE3B;IACAoG,mBAAmB,CAAC,CAAC;;IAErB;IACAxJ,KAAK,GAAG,IAAIpB,KAAK,CAAC6K,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAG,IAAI9K,KAAK,CAAC+K,iBAAiB,CACxC,EAAE,EACFxJ,MAAM,CAACyJ,UAAU,GAAGzJ,MAAM,CAAC0J,WAAW,EACtC,GAAG,EACH,IACF,CAAC;IACDH,MAAM,CAAC1H,QAAQ,CAACwF,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IAChCkC,MAAM,CAACnF,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBvB,SAAS,CAACI,OAAO,GAAGsG,MAAM;;IAE1B;IACA,MAAMI,QAAQ,GAAG,IAAIlL,KAAK,CAACmL,aAAa,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7DF,QAAQ,CAACG,OAAO,CAAC9J,MAAM,CAACyJ,UAAU,EAAEzJ,MAAM,CAAC0J,WAAW,CAAC;IACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAChCJ,QAAQ,CAACK,aAAa,CAAChK,MAAM,CAACiK,gBAAgB,CAAC;IAC/CrJ,YAAY,CAACqC,OAAO,CAACiH,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC;;IAErD;IACA;IACA,MAAMC,YAAY,GAAG,IAAI3L,KAAK,CAAC4L,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5DxK,KAAK,CAAC0H,GAAG,CAAC6C,YAAY,CAAC;;IAEvB;IACA,MAAME,iBAAiB,GAAG,IAAI7L,KAAK,CAAC8L,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;IACrED,iBAAiB,CAACzI,QAAQ,CAACwF,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC1CxH,KAAK,CAAC0H,GAAG,CAAC+C,iBAAiB,CAAC;;IAE5B;IACA,MAAME,iBAAiB,GAAG,IAAI/L,KAAK,CAAC8L,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;IACnEC,iBAAiB,CAAC3I,QAAQ,CAACwF,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3CxH,KAAK,CAAC0H,GAAG,CAACiD,iBAAiB,CAAC;;IAE5B;IACA,MAAMC,SAAS,GAAG,IAAIhM,KAAK,CAACiM,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;IACpDD,SAAS,CAAC5I,QAAQ,CAACwF,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAChCoD,SAAS,CAACE,KAAK,GAAGlG,IAAI,CAACC,EAAE,GAAG,CAAC;IAC7B+F,SAAS,CAACG,QAAQ,GAAG,GAAG;IACxBH,SAAS,CAACI,KAAK,GAAG,CAAC;IACnBJ,SAAS,CAACK,QAAQ,GAAG,GAAG;IACxBjL,KAAK,CAAC0H,GAAG,CAACkD,SAAS,CAAC;;IAEpB;IACA9K,QAAQ,GAAG,IAAIhB,aAAa,CAAC4K,MAAM,EAAEI,QAAQ,CAACQ,UAAU,CAAC;IACzDxK,QAAQ,CAACoL,aAAa,GAAG,IAAI;IAC7BpL,QAAQ,CAACqL,aAAa,GAAG,IAAI;IAC7BrL,QAAQ,CAACsL,kBAAkB,GAAG,KAAK;IACnCtL,QAAQ,CAAC2E,WAAW,GAAG,EAAE;IACzB3E,QAAQ,CAAC4E,WAAW,GAAG,GAAG;IAC1B5E,QAAQ,CAAC6E,aAAa,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IACtC/E,QAAQ,CAACgF,aAAa,GAAG,CAAC;IAC1BhF,QAAQ,CAACwE,MAAM,CAACkD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5B1H,QAAQ,CAAC0E,MAAM,CAAC,CAAC;;IAEjB;IACAO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;MACnB0E,MAAM,EAAE,CAAC,CAACA,MAAM;MAChB5J,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBkD,SAAS,EAAE,CAAC,CAACA,SAAS,CAACI;IACzB,CAAC,CAAC;;IAEF;IACA,MAAMiI,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAMC,aAAa,GAAG,IAAI5M,UAAU,CAAC,CAAC;QACtC4M,aAAa,CAACC,IAAI,CAChB,GAAGhL,QAAQ,uBAAuB,EACjCiL,IAAI,IAAK;UACR,MAAMrE,YAAY,GAAGqE,IAAI,CAAC3L,KAAK;;UAE/B;UACA,MAAM4L,gBAAgB,GAAG,IAAIhN,KAAK,CAACiN,KAAK,CAAC,CAAC;;UAE1C;UACAvE,YAAY,CAACwE,QAAQ,CAAEC,KAAK,IAAK;YAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;cAChB;cACA,IAAID,KAAK,CAACE,QAAQ,EAAE;gBAClB;gBACA,MAAMC,WAAW,GAAG,IAAItN,KAAK,CAACuN,oBAAoB,CAAC;kBACjDC,KAAK,EAAE,QAAQ;kBAAO;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,SAAS,EAAE,GAAG;kBAAQ;kBACtBC,eAAe,EAAE,GAAG,CAAE;gBACxB,CAAC,CAAC;;gBAEF;gBACA,IAAIR,KAAK,CAACE,QAAQ,CAAC1F,GAAG,EAAE;kBACtB2F,WAAW,CAAC3F,GAAG,GAAGwF,KAAK,CAACE,QAAQ,CAAC1F,GAAG;gBACtC;;gBAEA;gBACAwF,KAAK,CAACE,QAAQ,GAAGC,WAAW;gBAE5BnH,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE+G,KAAK,CAACS,IAAI,CAAC;cACrC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,OAAMlF,YAAY,CAACmF,QAAQ,CAACrG,MAAM,GAAG,CAAC,EAAE;YACtC,MAAM2F,KAAK,GAAGzE,YAAY,CAACmF,QAAQ,CAAC,CAAC,CAAC;YACtCb,gBAAgB,CAAClE,GAAG,CAACqE,KAAK,CAAC;UAC7B;;UAEA;UACA/L,KAAK,CAAC0H,GAAG,CAACkE,gBAAgB,CAAC;;UAE3B;UACAtM,gBAAgB,GAAGsM,gBAAgB;UAEnC7G,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9B1D,kBAAkB,CAAC,IAAI,CAAC;UACxBiK,OAAO,CAACK,gBAAgB,CAAC;QAC3B,CAAC,EACAc,GAAG,IAAK;UACP3H,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC0H,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;QACxE,CAAC,EACDrB,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMsB,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF;QACA,MAAMlB,gBAAgB,GAAG,MAAMP,gBAAgB,CAAC,CAAC;;QAEjD;QACAzC,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAIgD,gBAAgB,EAAE;UACpB,MAAMmB,YAAY,GAAG;YACnBtL,SAAS,EAAE,WAAW;YACtBC,QAAQ,EAAE,UAAU;YACpBE,OAAO,EAAE;UACX,CAAC;UACD,MAAMoL,UAAU,GAAG/L,SAAS,CAACmC,OAAO,CAACiE,YAAY,CAAC0F,YAAY,CAACtL,SAAS,EAAEsL,YAAY,CAACrL,QAAQ,CAAC;UAChGkK,gBAAgB,CAAC5J,QAAQ,CAACwF,GAAG,CAACwF,UAAU,CAACrJ,CAAC,EAAE,GAAG,EAAE,CAACqJ,UAAU,CAACpJ,CAAC,CAAC;UAC/DgI,gBAAgB,CAACnE,QAAQ,CAAC7D,CAAC,GAAIgB,IAAI,CAACC,EAAE,GAAGkI,YAAY,CAACnL,OAAO,GAAGgD,IAAI,CAACC,EAAE,GAAG,GAAI;UAC9E+G,gBAAgB,CAAClD,YAAY,CAAC,CAAC;UAC/BkD,gBAAgB,CAACjD,iBAAiB,CAAC,IAAI,CAAC;UACxChJ,eAAe,GAAGiM,gBAAgB,CAAC5J,QAAQ,CAACsB,KAAK,CAAC,CAAC;QACrD;MACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;;IAED;IACA,MAAM0H,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,UAAU,GAAG,CAAC,KAAK;MAClD,OAAO,IAAI7B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACtC,MAAM4B,WAAW,GAAIC,WAAW,IAAK;UACnCtI,OAAO,CAACC,GAAG,CAAC,WAAWkI,GAAG,aAAaG,WAAW,EAAE,CAAC;UAErD,MAAMC,MAAM,GAAG,IAAIzO,UAAU,CAAC,CAAC;UAC/ByO,MAAM,CAAC5B,IAAI,CACTwB,GAAG,EACFvB,IAAI,IAAK;YACR5G,OAAO,CAACC,GAAG,CAAC,WAAWkI,GAAG,EAAE,CAAC;YAC7B3B,OAAO,CAACI,IAAI,CAAC;UACf,CAAC,EACAe,GAAG,IAAK;YACP3H,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC0H,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;UACpE,CAAC,EACAtH,KAAK,IAAK;YACTR,OAAO,CAACQ,KAAK,CAAC,SAAS2H,GAAG,EAAE,EAAE3H,KAAK,CAAC;YACpC,IAAI8H,WAAW,GAAG,CAAC,EAAE;cACnBtI,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;cAC3BuE,UAAU,CAAC,MAAM6D,WAAW,CAACC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;YACtD,CAAC,MAAM;cACL7B,MAAM,CAACjG,KAAK,CAAC;YACf;UACF,CACF,CAAC;QACH,CAAC;QAED6H,WAAW,CAACD,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,IAAIzO,UAAU,CAAC,CAAC;IAC/ByO,MAAM,CAAC5B,IAAI,CACT,GAAGhL,QAAQ,4BAA4B,EACvC,MAAOiL,IAAI,IAAK;MACd,IAAI;QACF,MAAMhE,KAAK,GAAGgE,IAAI,CAAC3L,KAAK;QACxB2H,KAAK,CAAC4F,KAAK,CAAC/F,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACxBG,KAAK,CAAC3F,QAAQ,CAACwF,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3BxH,KAAK,CAAC0H,GAAG,CAACC,KAAK,CAAC;;QAEhB;QACA,MAAMmF,eAAe,CAAC,CAAC;MACzB,CAAC,CAAC,OAAOvH,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC,EACAmH,GAAG,IAAK;MACP3H,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC0H,GAAG,CAACC,MAAM,GAAGD,GAAG,CAACE,KAAK,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IACpE,CAAC,EACAtH,KAAK,IAAK;MACTR,OAAO,CAACQ,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BR,OAAO,CAACQ,KAAK,CAAC,OAAO,EAAE;QACrBiI,IAAI,EAAEjI,KAAK,CAACK,IAAI;QAChB6H,IAAI,EAAElI,KAAK,CAACD,OAAO;QACnBoI,KAAK,EAAE,GAAGhN,QAAQ,4BAA4B;QAC9CiN,KAAK,EAAE,GAAGjN,QAAQ;MACpB,CAAC,CAAC;IACJ,CACF,CAAC;;IAED;IACA,MAAMkN,OAAO,GAAGA,CAAA,KAAM;MACpBC,qBAAqB,CAACD,OAAO,CAAC;;MAE9B;MACA5O,KAAK,CAACwF,MAAM,CAAC,CAAC;MAEd,IAAI3E,UAAU,KAAK,QAAQ,IAAIP,gBAAgB,EAAE;QAC/C;QACAQ,QAAQ,CAACoD,OAAO,GAAG,KAAK;;QAExB;QACA,MAAM4K,UAAU,GAAGxO,gBAAgB,CAAC0C,QAAQ,CAACsB,KAAK,CAAC,CAAC;;QAEpD;QACA,MAAMyK,eAAe,GAAGzO,gBAAgB,CAACmI,QAAQ,CAAC7D,CAAC;;QAEnD;QACA;QACA,MAAMoK,gBAAgB,GAAG,EAAED,eAAe,GAAGnJ,IAAI,CAACC,EAAE,CAAC,GAAGD,IAAI,CAACC,EAAE,GAAC,CAAC,GAAC,CAAC;QACnE;;QAEA;QACA,MAAMoJ,YAAY,GAAG,IAAIrP,KAAK,CAAC6J,OAAO,CACpC,CAAC,EAAE,GAAG7D,IAAI,CAACsJ,GAAG,CAACF,gBAAgB,CAAC,EAChC,EAAE,EACF,CAAC,EAAE,GAAGpJ,IAAI,CAACuJ,GAAG,CAACH,gBAAgB,CACjC,CAAC;;QAED;QACA;;QAEA;QACAtE,MAAM,CAAC1H,QAAQ,CAACmC,IAAI,CAAC2J,UAAU,CAAC,CAACpG,GAAG,CAACuG,YAAY,CAAC;;QAElD;QACAvE,MAAM,CAAClG,EAAE,CAACgE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,MAAM4G,YAAY,GAAGN,UAAU,CAACxK,KAAK,CAAC,CAAC;QACvCoG,MAAM,CAACnF,MAAM,CAAC6J,YAAY,CAAC;;QAE3B;QACA1E,MAAM,CAAC2E,sBAAsB,CAAC,CAAC;QAC/B3E,MAAM,CAAChB,YAAY,CAAC,CAAC;QACrBgB,MAAM,CAACf,iBAAiB,CAAC,IAAI,CAAC;;QAE9B;QACA7I,QAAQ,CAACoD,OAAO,GAAG,KAAK;;QAExB;QACApD,QAAQ,CAACwE,MAAM,CAACH,IAAI,CAAC2J,UAAU,CAAC;QAChChO,QAAQ,CAAC0E,MAAM,CAAC,CAAC;QAEjBO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;UACnBsJ,IAAI,EAAER,UAAU,CAACS,OAAO,CAAC,CAAC;UAC1BC,IAAI,EAAE9E,MAAM,CAAC1H,QAAQ,CAACuM,OAAO,CAAC,CAAC;UAC/BE,IAAI,EAAEL,YAAY,CAACG,OAAO,CAAC,CAAC;UAC5BG,IAAI,EAAEhF,MAAM,CAACiF,iBAAiB,CAAC,IAAI/P,KAAK,CAAC6J,OAAO,CAAC,CAAC,CAAC,CAAC8F,OAAO,CAAC;QAC9D,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI1O,UAAU,KAAK,QAAQ,EAAE;QAClC;QACAC,QAAQ,CAACoD,OAAO,GAAG,IAAI;;QAEvB;QACAwG,MAAM,CAAClG,EAAE,CAACgE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;QAEtB;QACA,IAAI5C,IAAI,CAACgK,GAAG,CAAClF,MAAM,CAAC1H,QAAQ,CAAC4B,CAAC,CAAC,GAAG,EAAE,EAAE;UACpC8F,MAAM,CAAC1H,QAAQ,CAACwF,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAC9B1H,QAAQ,CAACwE,MAAM,CAACkD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5BkC,MAAM,CAACnF,MAAM,CAACzE,QAAQ,CAACwE,MAAM,CAAC;UAC9BxE,QAAQ,CAAC0E,MAAM,CAAC,CAAC;QACnB;MACF;MAEA1E,QAAQ,CAAC0E,MAAM,CAAC,CAAC;MACjBsF,QAAQ,CAAC+E,MAAM,CAAC7O,KAAK,EAAE0J,MAAM,CAAC;IAChC,CAAC;IAEDkE,OAAO,CAAC,CAAC;;IAET;IACA,MAAMkB,YAAY,GAAGA,CAAA,KAAM;MACzBpF,MAAM,CAACqF,MAAM,GAAG5O,MAAM,CAACyJ,UAAU,GAAGzJ,MAAM,CAAC0J,WAAW;MACtDH,MAAM,CAAC2E,sBAAsB,CAAC,CAAC;MAC/BvE,QAAQ,CAACG,OAAO,CAAC9J,MAAM,CAACyJ,UAAU,EAAEzJ,MAAM,CAAC0J,WAAW,CAAC;IACzD,CAAC;IACD1J,MAAM,CAAC6O,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;;IAE/C;IACA3O,MAAM,CAAC8O,aAAa,GAAG,MAAM;MAC3B,IAAIjM,SAAS,CAACI,OAAO,EAAE;QACrBJ,SAAS,CAACI,OAAO,CAACpB,QAAQ,CAACwF,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACzCxE,SAAS,CAACI,OAAO,CAACmB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjCvB,SAAS,CAACI,OAAO,CAACsF,YAAY,CAAC,CAAC;QAChC1F,SAAS,CAACI,OAAO,CAACuF,iBAAiB,CAAC,IAAI,CAAC;QAEzC,IAAI7I,QAAQ,EAAE;UACZA,QAAQ,CAACwE,MAAM,CAACkD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC5B1H,QAAQ,CAACoD,OAAO,GAAG,IAAI;UACvBpD,QAAQ,CAAC0E,MAAM,CAAC,CAAC;QACnB;QAEA3E,UAAU,GAAG,QAAQ;QACrBkF,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;QACxB,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,OAAO,MAAM;MAAA,IAAAkK,qBAAA;MACX,IAAIzP,oBAAoB,EAAE;QACxB0P,aAAa,CAAC1P,oBAAoB,CAAC;QACnCA,oBAAoB,GAAG,IAAI;MAC7B;MACA,IAAI2B,aAAa,CAACgC,OAAO,EAAE;QACzBhC,aAAa,CAACgC,OAAO,CAACgM,GAAG,CAAC,CAAC;MAC7B;MACAjP,MAAM,CAACkP,mBAAmB,CAAC,QAAQ,EAAEP,YAAY,CAAC;MAClD,CAAAI,qBAAA,GAAAnO,YAAY,CAACqC,OAAO,cAAA8L,qBAAA,uBAApBA,qBAAA,CAAsBI,WAAW,CAACxF,QAAQ,CAACQ,UAAU,CAAC;MACtDR,QAAQ,CAACyF,OAAO,CAAC,CAAC;;MAElB;MACA5O,aAAa,CAACiG,OAAO,CAAC,CAACe,KAAK,EAAEK,EAAE,KAAK;QACnChI,KAAK,CAACkI,MAAM,CAACP,KAAK,CAAC;MACrB,CAAC,CAAC;MACFhH,aAAa,CAAC6O,KAAK,CAAC,CAAC;;MAErB;MACA,OAAMxP,KAAK,CAACyM,QAAQ,CAACrG,MAAM,GAAG,CAAC,EAAE;QAC/BpG,KAAK,CAACkI,MAAM,CAAClI,KAAK,CAACyM,QAAQ,CAAC,CAAC,CAAC,CAAC;MACjC;MAEAzM,KAAK,GAAG,IAAI,CAAC,CAAC;MACdD,qBAAqB,GAAG,IAAI,CAAC,CAAC;IAChC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEZ,OAAA,CAAAE,SAAA;IAAAoN,QAAA,gBACEtN,OAAA;MAAKsQ,GAAG,EAAE1O,YAAa;MAAC2O,KAAK,EAAE;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpE7Q,OAAA;MAAKuQ,KAAK,EAAE3N,oBAAqB;MAAA0K,QAAA,gBAC/BtN,OAAA;QACEuQ,KAAK,EAAE;UACL,GAAGnN,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EuK,KAAK,EAAEvK,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFoO,OAAO,EAAEhN,kBAAmB;QAAAwJ,QAAA,EAC7B;MAED;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7Q,OAAA;QACEuQ,KAAK,EAAE;UACL,GAAGnN,WAAW;UACdE,eAAe,EAAEZ,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EuK,KAAK,EAAEvK,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFoO,OAAO,EAAE9M,kBAAmB;QAAAsJ,QAAA,EAC7B;MAED;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAAlP,EAAA,CAjsBMD,WAAW;AAAAqP,EAAA,GAAXrP,WAAW;AAksBjB,SAASsP,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;EACvCJ,MAAM,CAACV,KAAK,GAAG,GAAG;EAClBU,MAAM,CAACT,MAAM,GAAG,EAAE;;EAElB;EACAY,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACT,IAAI,EAAEC,MAAM,CAACV,KAAK,GAAC,CAAC,EAAEU,MAAM,CAACT,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAMkB,OAAO,GAAG,IAAIlS,KAAK,CAACmS,aAAa,CAACV,MAAM,CAAC;EAC/C,MAAMW,cAAc,GAAG,IAAIpS,KAAK,CAACqS,cAAc,CAAC;IAC9C1K,GAAG,EAAEuK,OAAO;IACZI,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMC,MAAM,GAAG,IAAIvS,KAAK,CAACwS,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAAC5D,KAAK,CAAC/F,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAO2J,MAAM;AACf;;AAEA;AACAhR,MAAM,CAACkR,WAAW,GAAG,CAAC1N,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;EAChC,IAAIvE,gBAAgB,EAAE;IACpBA,gBAAgB,CAAC0C,QAAQ,CAACwF,GAAG,CAAC7D,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IACtCvE,gBAAgB,CAACoJ,YAAY,CAAC,CAAC;IAC/BpJ,gBAAgB,CAACqJ,iBAAiB,CAAC,IAAI,CAAC;IACxC5D,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAACrB,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA1D,MAAM,CAACmR,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAM5H,MAAM,GAAG4G,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAI/H,MAAM,EAAE;MACV;MACA,MAAMgI,MAAM,GAAGhI,MAAM,CAAC1H,QAAQ,CAACsB,KAAK,CAAC,CAAC;;MAEtC;MACAoG,MAAM,CAAC1H,QAAQ,CAACwF,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BkC,MAAM,CAAClG,EAAE,CAACgE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBkC,MAAM,CAACnF,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAmF,MAAM,CAAChB,YAAY,CAAC,CAAC;MACrBgB,MAAM,CAACf,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAM7I,QAAQ,GAAGwQ,QAAQ,CAACiB,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAI7R,QAAQ,EAAE;QACZA,QAAQ,CAACwE,MAAM,CAACkD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B1H,QAAQ,CAAC0E,MAAM,CAAC,CAAC;MACnB;MAEAO,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxB4M,GAAG,EAAEF,MAAM,CAACnD,OAAO,CAAC,CAAC;QACrBsD,GAAG,EAAEnI,MAAM,CAAC1H,QAAQ,CAACuM,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOuD,CAAC,EAAE;IACV/M,OAAO,CAACQ,KAAK,CAAC,YAAY,EAAEuM,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA,MAAMtI,mBAAmB,GAAG,MAAAA,CAAA,KAAY;EACtC,IAAI;IACFzE,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B,MAAMyG,aAAa,GAAG,IAAI5M,UAAU,CAAC,CAAC;IACtC,MAAM8M,IAAI,GAAG,MAAMF,aAAa,CAACsG,SAAS,CAAC,GAAGrR,QAAQ,uBAAuB,CAAC;IAC9E,MAAM4G,YAAY,GAAGqE,IAAI,CAAC3L,KAAK;;IAE/B;IACAsH,YAAY,CAACwE,QAAQ,CAAEC,KAAK,IAAK;MAC/B,IAAIA,KAAK,CAACC,MAAM,EAAE;QAChBD,KAAK,CAACE,QAAQ,GAAG,IAAIrN,KAAK,CAACuN,oBAAoB,CAAC;UAC9CC,KAAK,EAAE,QAAQ;UACfC,SAAS,EAAE,GAAG;UACdC,SAAS,EAAE,GAAG;UACdC,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;;IAEF;IACAxM,qBAAqB,GAAGuH,YAAY;IACpCvC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,CAAC,OAAOO,KAAK,EAAE;IACdR,OAAO,CAACQ,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;AACF,CAAC;AAED,eAAe1E,WAAW;AAAC,IAAAqP,EAAA;AAAA8B,YAAA,CAAA9B,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}