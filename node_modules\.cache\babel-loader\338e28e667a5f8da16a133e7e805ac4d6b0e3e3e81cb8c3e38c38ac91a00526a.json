{"ast": null, "code": "import warning from \"rc-util/es/warning\";\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nexport function parseWidthHeight(value) {\n  if (typeof value === 'string' && String(Number(value)) === value) {\n    warning(false, 'Invalid value type of `width` or `height` which should be number type instead.');\n    return Number(value);\n  }\n  return value;\n}\nexport function warnCheck(props) {\n  warning(!('wrapperClassName' in props), \"'wrapperClassName' is removed. Please use 'rootClassName' instead.\");\n  warning(canUseDom() || !props.open, \"Drawer with 'open' in SSR is not work since no place to createPortal. Please move to 'useEffect' instead.\");\n}", "map": {"version": 3, "names": ["warning", "canUseDom", "parseWidthHeight", "value", "String", "Number", "warn<PERSON>heck", "props", "open"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/rc-drawer/es/util.js"], "sourcesContent": ["import warning from \"rc-util/es/warning\";\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nexport function parseWidthHeight(value) {\n  if (typeof value === 'string' && String(Number(value)) === value) {\n    warning(false, 'Invalid value type of `width` or `height` which should be number type instead.');\n    return Number(value);\n  }\n  return value;\n}\nexport function warnCheck(props) {\n  warning(!('wrapperClassName' in props), \"'wrapperClassName' is removed. Please use 'rootClassName' instead.\");\n  warning(canUseDom() || !props.open, \"Drawer with 'open' in SSR is not work since no place to createPortal. Please move to 'useEffect' instead.\");\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,oBAAoB;AACxC,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAO,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EACtC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIC,MAAM,CAACC,MAAM,CAACF,KAAK,CAAC,CAAC,KAAKA,KAAK,EAAE;IAChEH,OAAO,CAAC,KAAK,EAAE,gFAAgF,CAAC;IAChG,OAAOK,MAAM,CAACF,KAAK,CAAC;EACtB;EACA,OAAOA,KAAK;AACd;AACA,OAAO,SAASG,SAASA,CAACC,KAAK,EAAE;EAC/BP,OAAO,CAAC,EAAE,kBAAkB,IAAIO,KAAK,CAAC,EAAE,oEAAoE,CAAC;EAC7GP,OAAO,CAACC,SAAS,CAAC,CAAC,IAAI,CAACM,KAAK,CAACC,IAAI,EAAE,2GAA2G,CAAC;AAClJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}