{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\pages\\\\DeviceManagement.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, Space, Card, Tag, message } from 'antd';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst API_BASE_URL = 'http://localhost:5000/api';\nconst DeviceManagement = ({\n  id\n}, ref) => {\n  _s();\n  const [devices, setDevices] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingDevice, setEditingDevice] = useState(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentDevice, setCurrentDevice] = useState(null);\n  const [currentDeviceType, setCurrentDeviceType] = useState((editingDevice === null || editingDevice === void 0 ? void 0 : editingDevice.type) || null);\n\n  // 获取设备列表\n  const fetchDevices = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/devices`);\n      if (response.data.success) {\n        setDevices(response.data.data);\n      } else {\n        throw new Error(response.data.message || '获取设备列表失败');\n      }\n    } catch (error) {\n      console.error('获取设备列表失败:', error);\n      message.error('获取设备列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchDevices();\n  }, []);\n\n  // 处理添加设备\n  const handleAddDevice = () => {\n    setEditingDevice(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // 处理编辑设备\n  const handleEditDevice = device => {\n    setEditingDevice(device);\n    setCurrentDeviceType(device.type);\n    form.setFieldsValue({\n      name: device.name,\n      type: device.type,\n      status: device.status,\n      location: device.location,\n      ipAddress: device.ipAddress,\n      manufacturer: device.manufacturer,\n      model: device.model,\n      description: device.description,\n      rtspUrl: device.rtspUrl\n    });\n    setModalVisible(true);\n  };\n\n  // 处理查看设备详情\n  const handleViewDevice = device => {\n    setCurrentDevice(device);\n    setDetailModalVisible(true);\n  };\n\n  // 处理删除设备\n  const handleDeleteDevice = async deviceId => {\n    try {\n      const response = await axios.delete(`${API_BASE_URL}/devices/${deviceId}`);\n      if (response.data.success) {\n        message.success('设备删除成功');\n        fetchDevices(); // 重新获取设备列表\n      } else {\n        throw new Error(response.data.message || '删除设备失败');\n      }\n    } catch (error) {\n      console.error('删除设备失败:', error);\n      message.error('删除设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 处理表单提交\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      if (values.type === 'camera') {\n        values.rtspUrl = values.rtspUrl || '';\n      } else {\n        values.rtspUrl = undefined;\n      }\n      if (editingDevice) {\n        // 编辑设备\n        const response = await axios.put(`${API_BASE_URL}/devices/${editingDevice.id}`, values);\n        if (response.data.success) {\n          message.success('设备更新成功');\n          setModalVisible(false);\n          fetchDevices(); // 重新获取设备列表\n        } else {\n          throw new Error(response.data.message || '更新设备失败');\n        }\n      } else {\n        // 添加设备\n        const response = await axios.post(`${API_BASE_URL}/devices`, values);\n        if (response.data.success) {\n          message.success('设备添加成功');\n          setModalVisible(false);\n          fetchDevices(); // 重新获取设备列表\n        } else {\n          throw new Error(response.data.message || '添加设备失败');\n        }\n      }\n    } catch (error) {\n      console.error('保存设备失败:', error);\n      message.error('保存设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 修改设备类型选择的处理函数\n  const handleDeviceTypeChange = value => {\n    setCurrentDeviceType(value);\n    form.setFieldsValue({\n      type: value\n    });\n  };\n\n  // 渲染状态标签\n  const renderStatusTag = status => {\n    const statusMap = {\n      online: {\n        color: 'green',\n        text: '在线'\n      },\n      offline: {\n        color: 'gray',\n        text: '离线'\n      },\n      warning: {\n        color: 'orange',\n        text: '警告'\n      },\n      error: {\n        color: 'red',\n        text: '错误'\n      },\n      maintenance: {\n        color: 'blue',\n        text: '维护中'\n      }\n    };\n    const statusInfo = statusMap[status] || {\n      color: 'default',\n      text: status\n    };\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: statusInfo.color,\n      children: statusInfo.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 12\n    }, this);\n  };\n\n  // 设备类型映射\n  const deviceTypeMap = {\n    camera: '摄像头',\n    mmwave_radar: '毫米波雷达',\n    lidar: '激光雷达',\n    rsu: 'RSU',\n    edge_computing: '边缘计算单元'\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: '设备名称',\n    dataIndex: 'name',\n    key: 'name'\n  }, {\n    title: '设备类型',\n    dataIndex: 'type',\n    key: 'type',\n    render: type => deviceTypeMap[type] || type\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: renderStatusTag\n  }, {\n    title: '位置',\n    dataIndex: 'location',\n    key: 'location'\n  }, {\n    title: 'IP地址',\n    dataIndex: 'ipAddress',\n    key: 'ipAddress'\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleViewDevice(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        onClick: () => handleEditDevice(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        danger: true,\n        onClick: () => handleDeleteDevice(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 暴露 fetchDevices 方法\n  useImperativeHandle(ref, () => ({\n    fetchDevices\n  }));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: id,\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u8BBE\\u5907\\u5217\\u8868\",\n      extra: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: handleAddDevice,\n        children: \"\\u6DFB\\u52A0\\u8BBE\\u5907\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 16\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        loading: loading,\n        dataSource: devices,\n        columns: columns,\n        rowKey: \"id\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingDevice ? '编辑设备' : '添加设备',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => setModalVisible(false),\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u8BBE\\u5907\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入设备名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"type\",\n          label: \"\\u8BBE\\u5907\\u7C7B\\u578B\",\n          rules: [{\n            required: true,\n            message: '请选择设备类型'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u7C7B\\u578B\",\n            onChange: handleDeviceTypeChange,\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"camera\",\n              children: \"\\u6444\\u50CF\\u5934\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"mmwave_radar\",\n              children: \"\\u6BEB\\u7C73\\u6CE2\\u96F7\\u8FBE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"lidar\",\n              children: \"\\u6FC0\\u5149\\u96F7\\u8FBE\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"rsu\",\n              children: \"RSU\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"edge_computing\",\n              children: \"\\u8FB9\\u7F18\\u8BA1\\u7B97\\u5355\\u5143\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), currentDeviceType === 'camera' && /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"rtspUrl\",\n          label: \"RTSP\\u5730\\u5740\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165RTSP\\u5730\\u5740\\uFF0C\\u4F8B\\u5982\\uFF1Artsp://admin:password@192.168.1.100:554/stream1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u8BBE\\u5907\\u72B6\\u6001\",\n          rules: [{\n            required: true,\n            message: '请选择设备状态'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u8BBE\\u5907\\u72B6\\u6001\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"online\",\n              children: \"\\u5728\\u7EBF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"offline\",\n              children: \"\\u79BB\\u7EBF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"warning\",\n              children: \"\\u8B66\\u544A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"error\",\n              children: \"\\u9519\\u8BEF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"maintenance\",\n              children: \"\\u7EF4\\u62A4\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"location\",\n          label: \"\\u8BBE\\u5907\\u4F4D\\u7F6E\",\n          rules: [{\n            required: true,\n            message: '请输入设备位置'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u4F4D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"ipAddress\",\n          label: \"IP\\u5730\\u5740\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165IP\\u5730\\u5740\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"manufacturer\",\n          label: \"\\u5236\\u9020\\u5546\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5236\\u9020\\u5546\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"model\",\n          label: \"\\u578B\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u578B\\u53F7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u8BBE\\u5907\\u63CF\\u8FF0\",\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            rows: 4,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u8BBE\\u5907\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u8BBE\\u5907\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 11\n      }, this)],\n      width: 600,\n      children: currentDevice && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8BBE\\u5907ID:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 16\n          }, this), \" \", currentDevice.id]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8BBE\\u5907\\u540D\\u79F0:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 16\n          }, this), \" \", currentDevice.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u8BBE\\u5907\\u7C7B\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 16\n          }, this), \" \", deviceTypeMap[currentDevice.type] || currentDevice.type]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u72B6\\u6001:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 16\n          }, this), \" \", renderStatusTag(currentDevice.status)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u4F4D\\u7F6E:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 16\n          }, this), \" \", currentDevice.location]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"IP\\u5730\\u5740:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 16\n          }, this), \" \", currentDevice.ipAddress]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u6700\\u540E\\u7EF4\\u62A4\\u65F6\\u95F4:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 16\n          }, this), \" \", currentDevice.lastMaintenance ? new Date(currentDevice.lastMaintenance).toLocaleString() : '无记录']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u5B89\\u88C5\\u65E5\\u671F:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 16\n          }, this), \" \", currentDevice.installationDate ? new Date(currentDevice.installationDate).toLocaleString() : '无记录']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u5236\\u9020\\u5546:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 16\n          }, this), \" \", currentDevice.manufacturer || '未知']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u578B\\u53F7:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 16\n          }, this), \" \", currentDevice.model || '未知']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u63CF\\u8FF0:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 16\n          }, this), \" \", currentDevice.description || '无']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 13\n        }, this), currentDevice.type === 'camera' && /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"RTSP\\u5730\\u5740:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 18\n          }, this), \" \", currentDevice.rtspUrl || '未设置']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 5\n  }, this);\n};\n_s(DeviceManagement, \"FEvoVtvtZFhj3MG4SXI/8+O84qg=\", false, function () {\n  return [Form.useForm];\n});\n_c = DeviceManagement;\nexport default _c2 = /*#__PURE__*/forwardRef(DeviceManagement);\nvar _c, _c2;\n$RefreshReg$(_c, \"DeviceManagement\");\n$RefreshReg$(_c2, \"%default%\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "Space", "Card", "Tag", "message", "axios", "jsxDEV", "_jsxDEV", "Option", "API_BASE_URL", "DeviceManagement", "id", "ref", "_s", "devices", "setDevices", "loading", "setLoading", "modalVisible", "setModalVisible", "form", "useForm", "editingDevice", "setEditingDevice", "detailModalVisible", "setDetailModalVisible", "currentDevice", "setCurrentDevice", "currentDeviceType", "setCurrentDeviceType", "type", "fetchDevices", "response", "get", "data", "success", "Error", "error", "console", "handleAddDevice", "resetFields", "handleEditDevice", "device", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "status", "location", "ip<PERSON><PERSON><PERSON>", "manufacturer", "model", "description", "rtspUrl", "handleViewDevice", "handleDeleteDevice", "deviceId", "delete", "handleModalOk", "values", "validateFields", "undefined", "put", "post", "handleDeviceTypeChange", "value", "renderStatusTag", "statusMap", "online", "color", "text", "offline", "warning", "maintenance", "statusInfo", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "deviceTypeMap", "camera", "mmwave_radar", "lidar", "rsu", "edge_computing", "columns", "title", "dataIndex", "key", "render", "_", "record", "size", "onClick", "danger", "extra", "dataSource", "<PERSON><PERSON><PERSON>", "open", "onOk", "onCancel", "width", "layout", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "onChange", "TextArea", "rows", "footer", "lastMaintenance", "Date", "toLocaleString", "installationDate", "_c", "_c2", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/pages/DeviceManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Table, Button, Modal, Form, Input, Select, Space, Card, Tag, message } from 'antd';\nimport axios from 'axios';\n\nconst { Option } = Select;\nconst API_BASE_URL = 'http://localhost:5000/api';\n\nconst DeviceManagement = ({ id }, ref) => {\n  const [devices, setDevices] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [form] = Form.useForm();\n  const [editingDevice, setEditingDevice] = useState(null);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [currentDevice, setCurrentDevice] = useState(null);\n  const [currentDeviceType, setCurrentDeviceType] = useState(editingDevice?.type || null);\n\n  // 获取设备列表\n  const fetchDevices = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`${API_BASE_URL}/devices`);\n      if (response.data.success) {\n        setDevices(response.data.data);\n      } else {\n        throw new Error(response.data.message || '获取设备列表失败');\n      }\n    } catch (error) {\n      console.error('获取设备列表失败:', error);\n      message.error('获取设备列表失败: ' + (error.message || '未知错误'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchDevices();\n  }, []);\n\n  // 处理添加设备\n  const handleAddDevice = () => {\n    setEditingDevice(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  // 处理编辑设备\n  const handleEditDevice = (device) => {\n    setEditingDevice(device);\n    setCurrentDeviceType(device.type);\n    form.setFieldsValue({\n      name: device.name,\n      type: device.type,\n      status: device.status,\n      location: device.location,\n      ipAddress: device.ipAddress,\n      manufacturer: device.manufacturer,\n      model: device.model,\n      description: device.description,\n      rtspUrl: device.rtspUrl\n    });\n    setModalVisible(true);\n  };\n\n  // 处理查看设备详情\n  const handleViewDevice = (device) => {\n    setCurrentDevice(device);\n    setDetailModalVisible(true);\n  };\n\n  // 处理删除设备\n  const handleDeleteDevice = async (deviceId) => {\n    try {\n      const response = await axios.delete(`${API_BASE_URL}/devices/${deviceId}`);\n      if (response.data.success) {\n        message.success('设备删除成功');\n        fetchDevices(); // 重新获取设备列表\n      } else {\n        throw new Error(response.data.message || '删除设备失败');\n      }\n    } catch (error) {\n      console.error('删除设备失败:', error);\n      message.error('删除设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 处理表单提交\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (values.type === 'camera') {\n        values.rtspUrl = values.rtspUrl || '';\n      } else {\n        values.rtspUrl = undefined;\n      }\n\n      if (editingDevice) {\n        // 编辑设备\n        const response = await axios.put(`${API_BASE_URL}/devices/${editingDevice.id}`, values);\n        if (response.data.success) {\n          message.success('设备更新成功');\n          setModalVisible(false);\n          fetchDevices(); // 重新获取设备列表\n        } else {\n          throw new Error(response.data.message || '更新设备失败');\n        }\n      } else {\n        // 添加设备\n        const response = await axios.post(`${API_BASE_URL}/devices`, values);\n        if (response.data.success) {\n          message.success('设备添加成功');\n          setModalVisible(false);\n          fetchDevices(); // 重新获取设备列表\n        } else {\n          throw new Error(response.data.message || '添加设备失败');\n        }\n      }\n    } catch (error) {\n      console.error('保存设备失败:', error);\n      message.error('保存设备失败: ' + (error.message || '未知错误'));\n    }\n  };\n\n  // 修改设备类型选择的处理函数\n  const handleDeviceTypeChange = (value) => {\n    setCurrentDeviceType(value);\n    form.setFieldsValue({ type: value });\n  };\n\n  // 渲染状态标签\n  const renderStatusTag = (status) => {\n    const statusMap = {\n      online: { color: 'green', text: '在线' },\n      offline: { color: 'gray', text: '离线' },\n      warning: { color: 'orange', text: '警告' },\n      error: { color: 'red', text: '错误' },\n      maintenance: { color: 'blue', text: '维护中' }\n    };\n    \n    const statusInfo = statusMap[status] || { color: 'default', text: status };\n    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;\n  };\n\n  // 设备类型映射\n  const deviceTypeMap = {\n    camera: '摄像头',\n    mmwave_radar: '毫米波雷达',\n    lidar: '激光雷达',\n    rsu: 'RSU',\n    edge_computing: '边缘计算单元'\n  };\n\n  // 表格列定义\n  const columns = [\n    {\n      title: '设备名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '设备类型',\n      dataIndex: 'type',\n      key: 'type',\n      render: (type) => deviceTypeMap[type] || type\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: renderStatusTag\n    },\n    {\n      title: '位置',\n      dataIndex: 'location',\n      key: 'location',\n    },\n    {\n      title: 'IP地址',\n      dataIndex: 'ipAddress',\n      key: 'ipAddress',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space size=\"small\">\n          <Button type=\"link\" onClick={() => handleViewDevice(record)}>查看</Button>\n          <Button type=\"link\" onClick={() => handleEditDevice(record)}>编辑</Button>\n          <Button type=\"link\" danger onClick={() => handleDeleteDevice(record.id)}>删除</Button>\n        </Space>\n      ),\n    },\n  ];\n\n  // 暴露 fetchDevices 方法\n  useImperativeHandle(ref, () => ({\n    fetchDevices\n  }));\n\n  return (\n    <div id={id}>\n      <Card \n        title=\"设备列表\" \n        extra={<Button type=\"primary\" onClick={handleAddDevice}>添加设备</Button>}\n      >\n        <Table \n          loading={loading}\n          dataSource={devices} \n          columns={columns} \n          rowKey=\"id\"\n        />\n      </Card>\n\n      {/* 添加/编辑设备表单 */}\n      <Modal\n        title={editingDevice ? '编辑设备' : '添加设备'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => setModalVisible(false)}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"设备名称\"\n            rules={[{ required: true, message: '请输入设备名称' }]}\n          >\n            <Input placeholder=\"请输入设备名称\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"type\"\n            label=\"设备类型\"\n            rules={[{ required: true, message: '请选择设备类型' }]}\n          >\n            <Select \n              placeholder=\"请选择设备类型\"\n              onChange={handleDeviceTypeChange}\n            >\n              <Option value=\"camera\">摄像头</Option>\n              <Option value=\"mmwave_radar\">毫米波雷达</Option>\n              <Option value=\"lidar\">激光雷达</Option>\n              <Option value=\"rsu\">RSU</Option>\n              <Option value=\"edge_computing\">边缘计算单元</Option>\n            </Select>\n          </Form.Item>\n          \n          {/* 当设备类型为摄像头时显示 RTSP 地址输入 */}\n          {currentDeviceType === 'camera' && (\n            <Form.Item\n              name=\"rtspUrl\"\n              label=\"RTSP地址\"\n            >\n              <Input placeholder=\"请输入RTSP地址，例如：rtsp://admin:password@192.168.1.100:554/stream1\" />\n            </Form.Item>\n          )}\n          \n          <Form.Item\n            name=\"status\"\n            label=\"设备状态\"\n            rules={[{ required: true, message: '请选择设备状态' }]}\n          >\n            <Select placeholder=\"请选择设备状态\">\n              <Option value=\"online\">在线</Option>\n              <Option value=\"offline\">离线</Option>\n              <Option value=\"warning\">警告</Option>\n              <Option value=\"error\">错误</Option>\n              <Option value=\"maintenance\">维护中</Option>\n            </Select>\n          </Form.Item>\n          \n          <Form.Item\n            name=\"location\"\n            label=\"设备位置\"\n            rules={[{ required: true, message: '请输入设备位置' }]}\n          >\n            <Input placeholder=\"请输入设备位置\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"ipAddress\"\n            label=\"IP地址\"\n          >\n            <Input placeholder=\"请输入IP地址\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"manufacturer\"\n            label=\"制造商\"\n          >\n            <Input placeholder=\"请输入制造商\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"model\"\n            label=\"型号\"\n          >\n            <Input placeholder=\"请输入型号\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"description\"\n            label=\"设备描述\"\n          >\n            <Input.TextArea rows={4} placeholder=\"请输入设备描述\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 设备详情模态框 */}\n      <Modal\n        title=\"设备详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={600}\n      >\n        {currentDevice && (\n          <div>\n            <p><strong>设备ID:</strong> {currentDevice.id}</p>\n            <p><strong>设备名称:</strong> {currentDevice.name}</p>\n            <p><strong>设备类型:</strong> {deviceTypeMap[currentDevice.type] || currentDevice.type}</p>\n            <p><strong>状态:</strong> {renderStatusTag(currentDevice.status)}</p>\n            <p><strong>位置:</strong> {currentDevice.location}</p>\n            <p><strong>IP地址:</strong> {currentDevice.ipAddress}</p>\n            <p><strong>最后维护时间:</strong> {currentDevice.lastMaintenance ? new Date(currentDevice.lastMaintenance).toLocaleString() : '无记录'}</p>\n            <p><strong>安装日期:</strong> {currentDevice.installationDate ? new Date(currentDevice.installationDate).toLocaleString() : '无记录'}</p>\n            <p><strong>制造商:</strong> {currentDevice.manufacturer || '未知'}</p>\n            <p><strong>型号:</strong> {currentDevice.model || '未知'}</p>\n            <p><strong>描述:</strong> {currentDevice.description || '无'}</p>\n            {currentDevice.type === 'camera' && (\n              <p><strong>RTSP地址:</strong> {currentDevice.rtspUrl || '未设置'}</p>\n            )}\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default forwardRef(DeviceManagement); "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACnF,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,EAAEC,OAAO,QAAQ,MAAM;AAC3F,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC;AAAO,CAAC,GAAGR,MAAM;AACzB,MAAMS,YAAY,GAAG,2BAA2B;AAEhD,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC;AAAG,CAAC,EAAEC,GAAG,KAAK;EAAAC,EAAA;EACxC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6B,IAAI,CAAC,GAAGtB,IAAI,CAACuB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACiC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACqC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtC,QAAQ,CAAC,CAAA+B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEQ,IAAI,KAAI,IAAI,CAAC;;EAEvF;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFd,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMe,QAAQ,GAAG,MAAM3B,KAAK,CAAC4B,GAAG,CAAC,GAAGxB,YAAY,UAAU,CAAC;MAC3D,IAAIuB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBpB,UAAU,CAACiB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MAChC,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAAC9B,OAAO,IAAI,UAAU,CAAC;MACtD;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCjC,OAAO,CAACiC,KAAK,CAAC,YAAY,IAAIA,KAAK,CAACjC,OAAO,IAAI,MAAM,CAAC,CAAC;IACzD,CAAC,SAAS;MACRa,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDzB,SAAS,CAAC,MAAM;IACduC,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMQ,eAAe,GAAGA,CAAA,KAAM;IAC5BhB,gBAAgB,CAAC,IAAI,CAAC;IACtBH,IAAI,CAACoB,WAAW,CAAC,CAAC;IAClBrB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsB,gBAAgB,GAAIC,MAAM,IAAK;IACnCnB,gBAAgB,CAACmB,MAAM,CAAC;IACxBb,oBAAoB,CAACa,MAAM,CAACZ,IAAI,CAAC;IACjCV,IAAI,CAACuB,cAAc,CAAC;MAClBC,IAAI,EAAEF,MAAM,CAACE,IAAI;MACjBd,IAAI,EAAEY,MAAM,CAACZ,IAAI;MACjBe,MAAM,EAAEH,MAAM,CAACG,MAAM;MACrBC,QAAQ,EAAEJ,MAAM,CAACI,QAAQ;MACzBC,SAAS,EAAEL,MAAM,CAACK,SAAS;MAC3BC,YAAY,EAAEN,MAAM,CAACM,YAAY;MACjCC,KAAK,EAAEP,MAAM,CAACO,KAAK;MACnBC,WAAW,EAAER,MAAM,CAACQ,WAAW;MAC/BC,OAAO,EAAET,MAAM,CAACS;IAClB,CAAC,CAAC;IACFhC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMiC,gBAAgB,GAAIV,MAAM,IAAK;IACnCf,gBAAgB,CAACe,MAAM,CAAC;IACxBjB,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM4B,kBAAkB,GAAG,MAAOC,QAAQ,IAAK;IAC7C,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAM3B,KAAK,CAACkD,MAAM,CAAC,GAAG9C,YAAY,YAAY6C,QAAQ,EAAE,CAAC;MAC1E,IAAItB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB/B,OAAO,CAAC+B,OAAO,CAAC,QAAQ,CAAC;QACzBJ,YAAY,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,MAAM;QACL,MAAM,IAAIK,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAAC9B,OAAO,IAAI,QAAQ,CAAC;MACpD;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BjC,OAAO,CAACiC,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACjC,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAMoD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMrC,IAAI,CAACsC,cAAc,CAAC,CAAC;MAE1C,IAAID,MAAM,CAAC3B,IAAI,KAAK,QAAQ,EAAE;QAC5B2B,MAAM,CAACN,OAAO,GAAGM,MAAM,CAACN,OAAO,IAAI,EAAE;MACvC,CAAC,MAAM;QACLM,MAAM,CAACN,OAAO,GAAGQ,SAAS;MAC5B;MAEA,IAAIrC,aAAa,EAAE;QACjB;QACA,MAAMU,QAAQ,GAAG,MAAM3B,KAAK,CAACuD,GAAG,CAAC,GAAGnD,YAAY,YAAYa,aAAa,CAACX,EAAE,EAAE,EAAE8C,MAAM,CAAC;QACvF,IAAIzB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzB/B,OAAO,CAAC+B,OAAO,CAAC,QAAQ,CAAC;UACzBhB,eAAe,CAAC,KAAK,CAAC;UACtBY,YAAY,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,MAAM;UACL,MAAM,IAAIK,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAAC9B,OAAO,IAAI,QAAQ,CAAC;QACpD;MACF,CAAC,MAAM;QACL;QACA,MAAM4B,QAAQ,GAAG,MAAM3B,KAAK,CAACwD,IAAI,CAAC,GAAGpD,YAAY,UAAU,EAAEgD,MAAM,CAAC;QACpE,IAAIzB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzB/B,OAAO,CAAC+B,OAAO,CAAC,QAAQ,CAAC;UACzBhB,eAAe,CAAC,KAAK,CAAC;UACtBY,YAAY,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,MAAM;UACL,MAAM,IAAIK,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAAC9B,OAAO,IAAI,QAAQ,CAAC;QACpD;MACF;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BjC,OAAO,CAACiC,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACjC,OAAO,IAAI,MAAM,CAAC,CAAC;IACvD;EACF,CAAC;;EAED;EACA,MAAM0D,sBAAsB,GAAIC,KAAK,IAAK;IACxClC,oBAAoB,CAACkC,KAAK,CAAC;IAC3B3C,IAAI,CAACuB,cAAc,CAAC;MAAEb,IAAI,EAAEiC;IAAM,CAAC,CAAC;EACtC,CAAC;;EAED;EACA,MAAMC,eAAe,GAAInB,MAAM,IAAK;IAClC,MAAMoB,SAAS,GAAG;MAChBC,MAAM,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAK,CAAC;MACtCC,OAAO,EAAE;QAAEF,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAK,CAAC;MACtCE,OAAO,EAAE;QAAEH,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAK,CAAC;MACxC/B,KAAK,EAAE;QAAE8B,KAAK,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAK,CAAC;MACnCG,WAAW,EAAE;QAAEJ,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM;IAC5C,CAAC;IAED,MAAMI,UAAU,GAAGP,SAAS,CAACpB,MAAM,CAAC,IAAI;MAAEsB,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAEvB;IAAO,CAAC;IAC1E,oBAAOtC,OAAA,CAACJ,GAAG;MAACgE,KAAK,EAAEK,UAAU,CAACL,KAAM;MAAAM,QAAA,EAAED,UAAU,CAACJ;IAAI;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG;IACpBC,MAAM,EAAE,KAAK;IACbC,YAAY,EAAE,OAAO;IACrBC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,KAAK;IACVC,cAAc,EAAE;EAClB,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAG1D,IAAI,IAAKgD,aAAa,CAAChD,IAAI,CAAC,IAAIA;EAC3C,CAAC,EACD;IACEuD,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAExB;EACV,CAAC,EACD;IACEqB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAM,kBAChBnF,OAAA,CAACN,KAAK;MAAC0F,IAAI,EAAC,OAAO;MAAAlB,QAAA,gBACjBlE,OAAA,CAACX,MAAM;QAACkC,IAAI,EAAC,MAAM;QAAC8D,OAAO,EAAEA,CAAA,KAAMxC,gBAAgB,CAACsC,MAAM,CAAE;QAAAjB,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxEtE,OAAA,CAACX,MAAM;QAACkC,IAAI,EAAC,MAAM;QAAC8D,OAAO,EAAEA,CAAA,KAAMnD,gBAAgB,CAACiD,MAAM,CAAE;QAAAjB,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxEtE,OAAA,CAACX,MAAM;QAACkC,IAAI,EAAC,MAAM;QAAC+D,MAAM;QAACD,OAAO,EAAEA,CAAA,KAAMvC,kBAAkB,CAACqC,MAAM,CAAC/E,EAAE,CAAE;QAAA8D,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E;EAEX,CAAC,CACF;;EAED;EACAnF,mBAAmB,CAACkB,GAAG,EAAE,OAAO;IAC9BmB;EACF,CAAC,CAAC,CAAC;EAEH,oBACExB,OAAA;IAAKI,EAAE,EAAEA,EAAG;IAAA8D,QAAA,gBACVlE,OAAA,CAACL,IAAI;MACHmF,KAAK,EAAC,0BAAM;MACZS,KAAK,eAAEvF,OAAA,CAACX,MAAM;QAACkC,IAAI,EAAC,SAAS;QAAC8D,OAAO,EAAErD,eAAgB;QAAAkC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAE;MAAAJ,QAAA,eAEtElE,OAAA,CAACZ,KAAK;QACJqB,OAAO,EAAEA,OAAQ;QACjB+E,UAAU,EAAEjF,OAAQ;QACpBsE,OAAO,EAAEA,OAAQ;QACjBY,MAAM,EAAC;MAAI;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPtE,OAAA,CAACV,KAAK;MACJwF,KAAK,EAAE/D,aAAa,GAAG,MAAM,GAAG,MAAO;MACvC2E,IAAI,EAAE/E,YAAa;MACnBgF,IAAI,EAAE1C,aAAc;MACpB2C,QAAQ,EAAEA,CAAA,KAAMhF,eAAe,CAAC,KAAK,CAAE;MACvCiF,KAAK,EAAE,GAAI;MAAA3B,QAAA,eAEXlE,OAAA,CAACT,IAAI;QACHsB,IAAI,EAAEA,IAAK;QACXiF,MAAM,EAAC,UAAU;QAAA5B,QAAA,gBAEjBlE,OAAA,CAACT,IAAI,CAACwG,IAAI;UACR1D,IAAI,EAAC,MAAM;UACX2D,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAErG,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqE,QAAA,eAEhDlE,OAAA,CAACR,KAAK;YAAC2G,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZtE,OAAA,CAACT,IAAI,CAACwG,IAAI;UACR1D,IAAI,EAAC,MAAM;UACX2D,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAErG,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqE,QAAA,eAEhDlE,OAAA,CAACP,MAAM;YACL0G,WAAW,EAAC,4CAAS;YACrBC,QAAQ,EAAE7C,sBAAuB;YAAAW,QAAA,gBAEjClE,OAAA,CAACC,MAAM;cAACuD,KAAK,EAAC,QAAQ;cAAAU,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnCtE,OAAA,CAACC,MAAM;cAACuD,KAAK,EAAC,cAAc;cAAAU,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC3CtE,OAAA,CAACC,MAAM;cAACuD,KAAK,EAAC,OAAO;cAAAU,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnCtE,OAAA,CAACC,MAAM;cAACuD,KAAK,EAAC,KAAK;cAAAU,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChCtE,OAAA,CAACC,MAAM;cAACuD,KAAK,EAAC,gBAAgB;cAAAU,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAGXjD,iBAAiB,KAAK,QAAQ,iBAC7BrB,OAAA,CAACT,IAAI,CAACwG,IAAI;UACR1D,IAAI,EAAC,SAAS;UACd2D,KAAK,EAAC,kBAAQ;UAAA9B,QAAA,eAEdlE,OAAA,CAACR,KAAK;YAAC2G,WAAW,EAAC;UAA8D;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CACZ,eAEDtE,OAAA,CAACT,IAAI,CAACwG,IAAI;UACR1D,IAAI,EAAC,QAAQ;UACb2D,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAErG,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqE,QAAA,eAEhDlE,OAAA,CAACP,MAAM;YAAC0G,WAAW,EAAC,4CAAS;YAAAjC,QAAA,gBAC3BlE,OAAA,CAACC,MAAM;cAACuD,KAAK,EAAC,QAAQ;cAAAU,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClCtE,OAAA,CAACC,MAAM;cAACuD,KAAK,EAAC,SAAS;cAAAU,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnCtE,OAAA,CAACC,MAAM;cAACuD,KAAK,EAAC,SAAS;cAAAU,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnCtE,OAAA,CAACC,MAAM;cAACuD,KAAK,EAAC,OAAO;cAAAU,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjCtE,OAAA,CAACC,MAAM;cAACuD,KAAK,EAAC,aAAa;cAAAU,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZtE,OAAA,CAACT,IAAI,CAACwG,IAAI;UACR1D,IAAI,EAAC,UAAU;UACf2D,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAErG,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqE,QAAA,eAEhDlE,OAAA,CAACR,KAAK;YAAC2G,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZtE,OAAA,CAACT,IAAI,CAACwG,IAAI;UACR1D,IAAI,EAAC,WAAW;UAChB2D,KAAK,EAAC,gBAAM;UAAA9B,QAAA,eAEZlE,OAAA,CAACR,KAAK;YAAC2G,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZtE,OAAA,CAACT,IAAI,CAACwG,IAAI;UACR1D,IAAI,EAAC,cAAc;UACnB2D,KAAK,EAAC,oBAAK;UAAA9B,QAAA,eAEXlE,OAAA,CAACR,KAAK;YAAC2G,WAAW,EAAC;UAAQ;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEZtE,OAAA,CAACT,IAAI,CAACwG,IAAI;UACR1D,IAAI,EAAC,OAAO;UACZ2D,KAAK,EAAC,cAAI;UAAA9B,QAAA,eAEVlE,OAAA,CAACR,KAAK;YAAC2G,WAAW,EAAC;UAAO;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEZtE,OAAA,CAACT,IAAI,CAACwG,IAAI;UACR1D,IAAI,EAAC,aAAa;UAClB2D,KAAK,EAAC,0BAAM;UAAA9B,QAAA,eAEZlE,OAAA,CAACR,KAAK,CAAC6G,QAAQ;YAACC,IAAI,EAAE,CAAE;YAACH,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRtE,OAAA,CAACV,KAAK;MACJwF,KAAK,EAAC,0BAAM;MACZY,IAAI,EAAEzE,kBAAmB;MACzB2E,QAAQ,EAAEA,CAAA,KAAM1E,qBAAqB,CAAC,KAAK,CAAE;MAC7CqF,MAAM,EAAE,cACNvG,OAAA,CAACX,MAAM;QAAagG,OAAO,EAAEA,CAAA,KAAMnE,qBAAqB,CAAC,KAAK,CAAE;QAAAgD,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFuB,KAAK,EAAE,GAAI;MAAA3B,QAAA,EAEV/C,aAAa,iBACZnB,OAAA;QAAAkE,QAAA,gBACElE,OAAA;UAAAkE,QAAA,gBAAGlE,OAAA;YAAAkE,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACnD,aAAa,CAACf,EAAE;QAAA;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChDtE,OAAA;UAAAkE,QAAA,gBAAGlE,OAAA;YAAAkE,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACnD,aAAa,CAACkB,IAAI;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClDtE,OAAA;UAAAkE,QAAA,gBAAGlE,OAAA;YAAAkE,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACC,aAAa,CAACpD,aAAa,CAACI,IAAI,CAAC,IAAIJ,aAAa,CAACI,IAAI;QAAA;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvFtE,OAAA;UAAAkE,QAAA,gBAAGlE,OAAA;YAAAkE,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACb,eAAe,CAACtC,aAAa,CAACmB,MAAM,CAAC;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnEtE,OAAA;UAAAkE,QAAA,gBAAGlE,OAAA;YAAAkE,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACnD,aAAa,CAACoB,QAAQ;QAAA;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpDtE,OAAA;UAAAkE,QAAA,gBAAGlE,OAAA;YAAAkE,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACnD,aAAa,CAACqB,SAAS;QAAA;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvDtE,OAAA;UAAAkE,QAAA,gBAAGlE,OAAA;YAAAkE,QAAA,EAAQ;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACnD,aAAa,CAACqF,eAAe,GAAG,IAAIC,IAAI,CAACtF,aAAa,CAACqF,eAAe,CAAC,CAACE,cAAc,CAAC,CAAC,GAAG,KAAK;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClItE,OAAA;UAAAkE,QAAA,gBAAGlE,OAAA;YAAAkE,QAAA,EAAQ;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACnD,aAAa,CAACwF,gBAAgB,GAAG,IAAIF,IAAI,CAACtF,aAAa,CAACwF,gBAAgB,CAAC,CAACD,cAAc,CAAC,CAAC,GAAG,KAAK;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClItE,OAAA;UAAAkE,QAAA,gBAAGlE,OAAA;YAAAkE,QAAA,EAAQ;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACnD,aAAa,CAACsB,YAAY,IAAI,IAAI;QAAA;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjEtE,OAAA;UAAAkE,QAAA,gBAAGlE,OAAA;YAAAkE,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACnD,aAAa,CAACuB,KAAK,IAAI,IAAI;QAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzDtE,OAAA;UAAAkE,QAAA,gBAAGlE,OAAA;YAAAkE,QAAA,EAAQ;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACnD,aAAa,CAACwB,WAAW,IAAI,GAAG;QAAA;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC7DnD,aAAa,CAACI,IAAI,KAAK,QAAQ,iBAC9BvB,OAAA;UAAAkE,QAAA,gBAAGlE,OAAA;YAAAkE,QAAA,EAAQ;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACnD,aAAa,CAACyB,OAAO,IAAI,KAAK;QAAA;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAChE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAChE,EAAA,CAnVIH,gBAAgB;EAAA,QAILZ,IAAI,CAACuB,OAAO;AAAA;AAAA8F,EAAA,GAJvBzG,gBAAgB;AAqVtB,eAAA0G,GAAA,gBAAe3H,UAAU,CAACiB,gBAAgB,CAAC;AAAC,IAAAyG,EAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAF,EAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}