{"ast": null, "code": "import React,{useEffect,useRef,useState,useCallback}from'react';import*as THREE from'three';import{GLTFLoader}from'three/examples/jsm/loaders/GLTFLoader';import{OrbitControls}from'three/examples/jsm/controls/OrbitControls';import{CoordinateConverter}from'../utils/CoordinateConverter';import*as TWEEN from'@tweenjs/tween.js';import*as SkeletonUtils from'three/examples/jsm/utils/SkeletonUtils.js';import mqtt from'mqtt';import{Select,Popover}from'antd';import intersectionsData from'../data/intersections.json';// 全局变量\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";let globalVehicleRef=null;let globalTrajectory=[];let currentPointIndex=0;let globalUpdateInterval=null;let targetPosition=null;// 新增：目标位置\nlet currentPosition=null;// 新增：当前位置\nlet isMoving=false;// 新增：移动状态标志\nlet cameraMode='global';// 'global' 或 'follow'\nlet controls=null;// 保存 controls 的引用\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel=null;let preloadedCyclistModel=null;// 新增：非机动车模型\nlet preloadedPeopleModel=null;// 新增：行人模型\nlet preloadedTrafficLightModel=null;// 新增：红绿灯模型\nlet scene=null;// 添加scene全局变量\nlet peopleBaseModel=null;// 存储原始模型数据\nlet skeleton=null;// 添加滤波相关的变量\nlet lastPosition=null;let lastRotation=null;const ALPHA=0.08;// 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions=new Map();// 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations=new Map();// 使用Map存储每个车辆ID的上一次旋转角度\n// MQTT配置\nconst MQTT_CONFIG={broker:window.location.hostname,port:8083,// 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\nbsm:'changli/cloud/v2x/obu/bsm',rsm:'changli/cloud/v2x/rsu/rsm',scene:'changli/cloud/v2x/obu/scene',rsi:'changli/cloud/v2x/rsu/rsi',// 添加 RSI 主题\nspat:'changli/cloud/v2x/rsu/spat'// 添加 SPAT 主题\n};// 修改所有资源的基础URL\nconst BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:5000';console.log('API_URLxxxx:',process.env.REACT_APP_API_URL);// 添加全局变量来存储所有车辆\nconst vehicleModels=new Map();// 使用Map存储车辆ID和对应的3D模型\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps=new Map();// 用于存储每个设备的最新时间戳\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId=null;// 添加红绿灯相关的全局变量\nlet trafficLightsMap=new Map();// 存储路口ID与红绿灯模型的映射\nlet trafficLightStates=new Map();// 存储路口ID与红绿灯状态的映射\nconst clock=new THREE.Clock();// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId=async()=>{try{const response=await fetch(`${BASE_URL}/api/vehicles/list`);const data=await response.json();if(data&&data.vehicles&&Array.isArray(data.vehicles)){const mainVehicle=data.vehicles.find(v=>v.isMainVehicle===true);if(mainVehicle&&mainVehicle.bsmId){mainVehicleBsmId=mainVehicle.bsmId;console.log('获取主车bsmId成功:',mainVehicleBsmId);return mainVehicleBsmId;}}console.log('未找到主车，使用默认值 BSM01');mainVehicleBsmId='BSM01';// 默认值\nreturn mainVehicleBsmId;}catch(error){console.error('获取主车信息失败:',error);mainVehicleBsmId='BSM01';// 出错时使用默认值\nreturn mainVehicleBsmId;}};// 添加滤波器函数\nconst lowPassFilter=(newValue,lastValue,alpha)=>{if(lastValue===null)return newValue;return alpha*newValue+(1-alpha)*lastValue;};// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition=(newPos,vehicleId)=>{// 如果没有提供车辆ID，使用全局变量（用于主车）\nif(!vehicleId){if(!lastPosition){lastPosition=newPos.clone();return newPos;}const filteredX=lowPassFilter(newPos.x,lastPosition.x,ALPHA);const filteredY=lowPassFilter(newPos.y,lastPosition.y,ALPHA);const filteredZ=lowPassFilter(newPos.z,lastPosition.z,ALPHA);lastPosition.set(filteredX,filteredY,filteredZ);return lastPosition.clone();}// 针对特定车辆ID的滤波\nif(!vehicleLastPositions.has(vehicleId)){vehicleLastPositions.set(vehicleId,newPos.clone());return newPos;}const lastPos=vehicleLastPositions.get(vehicleId);// 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\nconst distance=lastPos.distanceTo(newPos);const MAX_DISTANCE_THRESHOLD=50;// 最大距离阈值，超过此距离认为是位置跳变\nif(distance>MAX_DISTANCE_THRESHOLD){console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);vehicleLastPositions.set(vehicleId,newPos.clone());return newPos;}// 正常滤波处理\nconst filteredX=lowPassFilter(newPos.x,lastPos.x,ALPHA);const filteredY=lowPassFilter(newPos.y,lastPos.y,ALPHA);const filteredZ=lowPassFilter(newPos.z,lastPos.z,ALPHA);const filteredPos=new THREE.Vector3(filteredX,filteredY,filteredZ);vehicleLastPositions.set(vehicleId,filteredPos.clone());return filteredPos;};// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation=(newRotation,vehicleId)=>{// 如果没有提供车辆ID，使用全局变量（用于主车）\nif(!vehicleId){if(lastRotation===null){lastRotation=newRotation;return newRotation;}// 处理角度跳变（从360度到0度或反之）\nlet diff=newRotation-lastRotation;if(diff>Math.PI)diff-=2*Math.PI;if(diff<-Math.PI)diff+=2*Math.PI;const filteredRotation=lowPassFilter(lastRotation+diff,lastRotation,ALPHA);lastRotation=filteredRotation;return filteredRotation;}// 针对特定车辆ID的滤波\nif(!vehicleLastRotations.has(vehicleId)){vehicleLastRotations.set(vehicleId,newRotation);return newRotation;}const lastRot=vehicleLastRotations.get(vehicleId);// 处理角度跳变（从360度到0度或反之）\nlet diff=newRotation-lastRot;if(diff>Math.PI)diff-=2*Math.PI;if(diff<-Math.PI)diff+=2*Math.PI;// 检查是否是大角度变化，如果是则不进行过滤\nconst MAX_ANGLE_THRESHOLD=Math.PI/2;// 90度\nif(Math.abs(diff)>MAX_ANGLE_THRESHOLD){console.log(`车辆${vehicleId}朝向变化过大(${(diff*180/Math.PI).toFixed(2)}度)，不进行滤波`);vehicleLastRotations.set(vehicleId,newRotation);return newRotation;}const filteredRotation=lowPassFilter(lastRot+diff,lastRot,ALPHA);vehicleLastRotations.set(vehicleId,filteredRotation);return filteredRotation;};// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL=1;// 每秒更新一次\n// ... existing code ...\n// 在文件顶部添加\nconst mixersToCleanup=new Set();const bonesMap=new Map();// 在文件顶部添加资源管理器\nconst resourceManager={mixers:new Set(),bones:new Map(),actions:new Map(),models:new Set(),addMixer(mixer,model){this.mixers.add(mixer);if(model){this.models.add(model);// 记录骨骼\nmodel.traverse(object=>{if(object.isBone){this.bones.set(object.uuid,object);}});}return mixer;},addAction(action,mixer){if(!this.actions.has(mixer)){this.actions.set(mixer,new Set());}this.actions.get(mixer).add(action);return action;},removeMixer(mixer){if(this.mixers.has(mixer)){try{// 停止并清理所有动作\nif(this.actions.has(mixer)){this.actions.get(mixer).forEach(action=>{if(action&&typeof action.stop==='function'){action.stop();}});this.actions.delete(mixer);}// 停止混合器\nif(typeof mixer.stopAllAction==='function'){mixer.stopAllAction();}// 清理混合器的根对象\nconst root=mixer.getRoot();if(root){this.models.delete(root);root.traverse(object=>{if(object&&object.isBone){this.bones.delete(object.uuid);}if(object&&object.animations){object.animations.length=0;}});// 安全地清理缓存\ntry{if(typeof mixer.uncacheRoot==='function'){mixer.uncacheRoot(root);}if(typeof mixer.uncacheAction==='function'){mixer.uncacheAction(null,root);}// 这里是发生错误的地方，添加防御性检查\nif(typeof mixer.uncacheClip==='function'){// 不直接调用uncacheClip(null)，而是获取混合器中的所有动画片段并逐个清理\nconst clips=mixer._actions.map(a=>a&&a._clip).filter(Boolean);clips.forEach(clip=>{if(clip&&clip.uuid){mixer.uncacheClip(clip);}});}}catch(e){console.log('在清理动画混合器时发生非致命错误:',e);// 继续执行其余清理操作\n}}this.mixers.delete(mixer);}catch(error){console.error('清理动画混合器时发生错误:',error);// 确保即使出错也会从集合中移除\nthis.mixers.delete(mixer);}}},cleanup(){try{// 清理动作\nthis.actions.forEach((actions,mixer)=>{try{actions.forEach(action=>{if(action&&typeof action.stop==='function'){action.stop();}});actions.clear();}catch(e){console.log('清理动作时发生非致命错误:',e);}});this.actions.clear();// 清理混合器\nthis.mixers.forEach(mixer=>{try{if(typeof mixer.stopAllAction==='function'){mixer.stopAllAction();}const root=mixer.getRoot();if(root){root.traverse(object=>{if(object&&object.animations){object.animations.length=0;}});// 安全清理\nif(typeof mixer.uncacheRoot==='function'){mixer.uncacheRoot(root);}if(typeof mixer.uncacheAction==='function'){mixer.uncacheAction(null,root);}// 安全清理动画片段\ntry{if(mixer._actions&&Array.isArray(mixer._actions)){const clips=mixer._actions.map(a=>a&&a._clip).filter(Boolean);clips.forEach(clip=>{if(clip&&clip.uuid&&typeof mixer.uncacheClip==='function'){mixer.uncacheClip(clip);}});}}catch(e){console.log('清理动画片段时发生非致命错误:',e);}}}catch(e){console.log('清理混合器时发生非致命错误:',e);}});this.mixers.clear();// 清理骨骼\nthis.bones.forEach(bone=>{if(bone.parent){bone.parent.remove(bone);}if(bone.matrix)bone.matrix.identity();if(bone.matrixWorld)bone.matrixWorld.identity();});this.bones.clear();// 清理模型\nthis.models.forEach(model=>{if(model.parent){model.parent.remove(model);}model.traverse(object=>{if(object.isMesh){if(object.geometry){object.geometry.dispose();}if(object.material){if(Array.isArray(object.material)){object.material.forEach(material=>{if(material.map)material.map.dispose();material.dispose();});}else{if(object.material.map)object.material.map.dispose();object.material.dispose();}}}if(object.animations){object.animations.length=0;}});});this.models.clear();}catch(error){console.error('全局清理过程中发生错误:',error);// 即使发生错误也尝试清空集合\nthis.actions.clear();this.mixers.clear();this.bones.clear();this.models.clear();}}};// 修改创建动画混合器的函数\nconst createAnimationMixer=model=>{const mixer=new THREE.AnimationMixer(model);return resourceManager.addMixer(mixer,model);};// 修改动画动作创建的函数\nconst createAction=(clip,mixer,model)=>{const action=mixer.clipAction(clip,model);return resourceManager.addAction(action,mixer);};const CampusModel=_ref=>{let{className,onCurrentRSUChange,selectedRSUs}=_ref;const containerRef=useRef(null);const vehicleRef=useRef(null);const converter=useRef(new CoordinateConverter());const trajectoryRef=useRef([]);const currentPointRef=useRef(0);const mqttClientRef=useRef(null);const animationFrameRef=useRef(null);const mapRef=useRef(null);// 添加相机平滑过渡的变量\nconst lastCameraPosition=useRef(null);const lastCameraTarget=useRef(null);const cameraSmoothing=0.98;// 平滑系数，值越大越平滑 (0-1之间)\n// 添加行人动画相关的引用\nconst prevAnimationTimeRef=useRef(Date.now());const peopleAnimationMixers=new Map();// 使用全局Map存储所有行人的动画混合器（不再使用useRef）\nconst peopleAnimations=useRef([]);// 存储行人动画数据\n// 添加车辆状态\nconst[vehicleState,setVehicleState]=useState({longitude:0,latitude:0,speed:0,heading:0});// 在 CampusModel 组件中添加状态\nconst[viewMode,setViewMode]=useState('global');// 添加视角切换按钮的样式\nconst buttonContainerStyle={position:'fixed',bottom:'20px',left:'50%',transform:'translateX(-50%)',zIndex:1000,// 改为1000，避免遮挡点击\ndisplay:'flex',gap:'10px'};const buttonStyle={padding:'8px 16px',backgroundColor:'rgba(255, 255, 255, 0.9)',border:'1px solid #ddd',borderRadius:'4px',cursor:'pointer',fontSize:'14px',boxShadow:'0 2px 4px rgba(0,0,0,0.1)',transition:'all 0.3s ease'};// 添加相机引用\nconst cameraRef=useRef(null);// 添加路口选择相关代码\nconst[selectedIntersection,setSelectedIntersection]=useState(null);// 添加红绿灯状态弹出窗口相关状态\nconst[trafficLightPopover,setTrafficLightPopover]=useState({visible:false,interId:null,position:{x:0,y:0},content:null,phases:[]});// 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\nconst currentPopoverIdRef=useRef(null);// 添加红绿灯状态自动更新定时器引用\nconst trafficLightUpdateTimerRef=useRef(null);// 全局存储setTrafficLightPopover函数引用\nwindow._setTrafficLightPopover=setTrafficLightPopover;// 将Ref暴露给全局以便弹窗函数使用\nwindow.currentPopoverIdRef=currentPopoverIdRef;window.trafficLightUpdateTimerRef=trafficLightUpdateTimerRef;// 修改路口选择器的样式\nconst intersectionSelectStyle={position:'fixed',top:'65px',// 从 60px 改为 65px\nleft:'50%',transform:'translateX(-50%)',width:'200px',// 从 300px 改为 200px\nzIndex:1001,backgroundColor:'white',borderRadius:'4px',boxShadow:'0 2px 8px rgba(0,0,0,0.1)'};// 添加文字标签样式\nconst labelStyle={position:'fixed',top:'65px',left:'calc(50% - 90px)',// 从 140px 改为 110px，让文字更靠近选择框\ntransform:'translateX(-100%)',padding:'0 5px',// 从 10px 改为 5px，减少内边距\nlineHeight:'32px',color:'#fff',fontSize:'14px',fontWeight:'bold',textShadow:'0 1px 2px rgba(0,0,0,0.3)',zIndex:1001};// 添加交通灯映射状态\nconst[trafficLightsMap]=useState(new Map());// 添加当前RSU状态\nconst[currentRSU,setCurrentRSU]=useState(null);// 添加设备时间戳状态\nconst[deviceTimestamps,setDeviceTimestamps]=useState(new Map());// 添加最后一条消息状态\nconst[lastMessage,setLastMessage]=useState({topic:'',content:''});// 修改视角切换函数\nconst switchToFollowView=()=>{if(cameraMode!=='follow'){console.log('切换到跟随视角');cameraMode='follow';// 重置相机平滑变量，确保切换视角时不会有突兀的过渡\nlastCameraPosition.current=null;lastCameraTarget.current=null;if(controls){controls.enabled=false;}}};const switchToGlobalView=()=>{if(cameraMode!=='global'){console.log('切换到全局视角');cameraMode='global';// 重置相机平滑变量\nlastCameraPosition.current=null;lastCameraTarget.current=null;if(cameraRef.current&&controls){// 获取当前相机位置和朝向\n// const currentPos = cameraRef.current.position.clone();\ncameraRef.current.position.set(0,500,0);const currentPos=cameraRef.current.position.clone();const currentUp=cameraRef.current.up.clone();// 创建相机位置的补间动画\nnew TWEEN.Tween(currentPos).to({x:0,y:300,z:0},1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(()=>{cameraRef.current.position.copy(currentPos);}).start();// 创建相机上方向的补间动画\nnew TWEEN.Tween(currentUp).to({x:0,y:1,z:0},1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(()=>{cameraRef.current.up.copy(currentUp);}).start();// 获取当前控制器目标点\ncontrols.target.set(0,0,0);const currentTarget=controls.target.clone();// 创建目标点的补间动画\nnew TWEEN.Tween(currentTarget).to({x:0,y:0,z:0},1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(()=>{controls.target.copy(currentTarget);// 确保相机始终朝向目标点\ncameraRef.current.lookAt(controls.target);controls.update();}).start();// 启用控制器\ncontrols.enabled=true;// 重置控制器的一些属性\ncontrols.minDistance=50;controls.maxDistance=500;controls.maxPolarAngle=Math.PI/2.1;controls.minPolarAngle=0;controls.update();// 强制更新相机矩阵\ncameraRef.current.updateMatrix();cameraRef.current.updateMatrixWorld(true);console.log('切换到全局视角',{目标相机位置:[0,300,0],目标控制点:[0,0,0],动画已启动:true});}}};// 修改处理路口选择的函数\nconst handleIntersectionChange=value=>{const intersection=intersectionsData.intersections.find(i=>i.name===value);if(intersection&&cameraRef.current&&controls){setSelectedIntersection(intersection);// 使用 wgs84ToModel 方法转换经纬度到模型坐标\nconst modelCoords=converter.current.wgs84ToModel(parseFloat(intersection.longitude),parseFloat(intersection.latitude));console.log('路口坐标转换结果:',{路口名称:intersection.name,经纬度:{longitude:intersection.longitude,latitude:intersection.latitude},模型坐标:modelCoords});// 设置为路口视角模式\ncameraMode='intersection';setViewMode('intersection');// 直接设置相机位置\ncameraRef.current.position.set(modelCoords.x+50,70,-modelCoords.y+50);// 直接设置控制器目标点\ncontrols.target.set(modelCoords.x,0,-modelCoords.y);// 确保相机朝向目标点\ncameraRef.current.lookAt(controls.target);// 更新控制器\ncontrols.enabled=true;controls.update();// 强制更新相机矩阵\ncameraRef.current.updateMatrix();cameraRef.current.updateMatrixWorld(true);console.log('相机已直接移动到路口:',{路口名称:intersection.name,相机位置:cameraRef.current.position.toArray(),目标点:controls.target.toArray(),模型坐标:modelCoords});// 如果该路口有红绿灯，自动显示红绿灯弹窗\nif(intersection.hasTrafficLight!==false&&intersection.interId){console.log(`路口 ${intersection.name} 有红绿灯，准备显示红绿灯状态`);// 延迟300ms调用以确保场景已更新\nsetTimeout(()=>{// 检查多种可能的ID格式\nlet interId=intersection.interId;// 使用全局的showTrafficLightPopup函数显示红绿灯弹窗\nif(window.showTrafficLightPopup){window.showTrafficLightPopup(interId);console.log(`已触发路口 ${intersection.name} (ID: ${interId}) 的红绿灯弹窗显示`);}else{console.error('找不到显示红绿灯弹窗的函数');}},300);}else{console.log(`路口 ${intersection.name} 没有红绿灯或缺少ID，不显示红绿灯状态`);// 如果有弹窗正在显示，则关闭它\nif(window._setTrafficLightPopover){window._setTrafficLightPopover({visible:false});}}}};// 修改处理MQTT消息的函数\nconst handleMqttMessage=(topic,message)=>{try{const payload=JSON.parse(message);// 处理RSM消息\nif(topic===MQTT_CONFIG.rsm){var _payload$data;// console.log('收到RSM消息:', payload);\n// 检查设备时间戳\nconst deviceMac=payload.mac;const messageTimestamp=payload.tm;// 获取该设备的最新时间戳\nconst lastTimestamp=deviceTimestamps.get(deviceMac);// 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\nif(lastTimestamp&&messageTimestamp<lastTimestamp){// console.log('忽略过期的RSM消息:', {\n//   设备MAC: deviceMac,\n//   消息时间戳: messageTimestamp,\n//   最新时间戳: lastTimestamp\n// });\nreturn;}// 更新设备的最新时间戳\ndeviceTimestamps.set(deviceMac,messageTimestamp);// console.log('RSM消息时间戳更新:', {\n//   设备MAC: deviceMac,\n//   时间戳: messageTimestamp,\n//   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n// });\nconst participants=((_payload$data=payload.data)===null||_payload$data===void 0?void 0:_payload$data.participants)||[];const rsuid=payload.data.rsuid;// 分类处理不同类型的参与者\nconst now=Date.now();// 处理所有参与者\nparticipants.forEach(participant=>{// const id = participant.partPtcId;\n// const id =  rsuid + participant.partPtcId;\nconst id=deviceMac+participant.partPtcId;const type=participant.partPtcType;if(type==='3'||type==='2'||type==='1'){// if(type === '3'){\n// if(type === '3'||type === '1'){\n// 解析位置和状态信息\nconst state={longitude:parseFloat(participant.partPosLong),latitude:parseFloat(participant.partPosLat),speed:parseFloat(participant.partSpeed),heading:parseFloat(participant.partHeading)};const modelPos=converter.current.wgs84ToModel(state.longitude,state.latitude);// 根据类型选择对应的预加载模型\nlet preloadedModel;switch(type){case'1':// 机动车\npreloadedModel=preloadedVehicleModel;break;case'2':// 非机动车\npreloadedModel=preloadedCyclistModel;break;case'3':// 行人\npreloadedModel=preloadedPeopleModel;break;default:return;// 跳过未知类型\n}// 获取或创建模型\nlet model=vehicleModels.get(id);if(!model&&preloadedModel){// 创建新模型实例\nconst newModel=type==='3'?SkeletonUtils.clone(preloadedPeopleModel):preloadedModel.clone();// 根据类型调整高度和缩放\nconst height=type==='3'?2.0:1.0;newModel.position.set(modelPos.x,height,-modelPos.y);newModel.rotation.y=Math.PI-state.heading*Math.PI/180;// 如果是行人类型，设置缩放和创建动画\nif(type==='3'){// newModel.scale.set(0.005, 0.005, 0.005);\nnewModel.scale.set(4,4,4);// 使用resourceManager创建并管理动画混合器\nconst mixer=createAnimationMixer(newModel);if(peopleBaseModel&&peopleBaseModel.animations&&peopleBaseModel.animations.length>0){// 只创建一个动作并添加到资源管理器\nconst action=createAction(peopleBaseModel.animations[0],mixer,newModel);action.play();}// 保存到全局Map中(不再使用useRef)\npeopleAnimationMixers.set(id,mixer);}scene.add(newModel);vehicleModels.set(id,{model:newModel,lastUpdate:now,type:type});}else if(model){// 更新现有模型\nmodel.model.position.set(modelPos.x,model.type==='3'?2.0:1.0,-modelPos.y);model.model.rotation.y=Math.PI-state.heading*Math.PI/180;model.lastUpdate=now;model.model.updateMatrix();model.model.updateMatrixWorld(true);}}});// 清理长时间未更新的模型\nconst CLEANUP_THRESHOLD=1000;const currentIds=new Set(participants.map(p=>deviceMac+p.partPtcId));vehicleModels.forEach((modelData,id)=>{if(now-modelData.lastUpdate>CLEANUP_THRESHOLD&&!currentIds.has(id)){// 如果是行人，清理动画混合器\nif(modelData.type==='3'&&peopleAnimationMixers.has(id)){const mixer=peopleAnimationMixers.get(id);// 使用resourceManager清理混合器\nresourceManager.removeMixer(mixer);peopleAnimationMixers.delete(id);}// 从场景移除模型\nscene.remove(modelData.model);vehicleModels.delete(id);}});return;}// 处理BSM消息\nif(topic===MQTT_CONFIG.bsm){// console.log('收到BSM消息:', payload);\nconst bsmData=payload.data;const bsmid=bsmData.bsmId;const newState={longitude:parseFloat(bsmData.partLong),latitude:parseFloat(bsmData.partLat),speed:parseFloat(bsmData.partSpeed),heading:parseFloat(bsmData.partHeading)};// console.log('解析后的车辆状态:', newState);\n// console.log('车辆ID:', bsmid);\n// 通知RealTimeTraffic组件已收到真实BSM消息\nwindow.postMessage({type:'realBsmReceived',source:'CampusModel'},'*');// 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\nwindow.postMessage({type:'bsm',bsmId:bsmid,// 直接传递车辆ID\ndata:{// 同时提供完整的BSM数据\nbsmId:bsmid,partSpeed:bsmData.partSpeed,partLat:bsmData.partLat,partLong:bsmData.partLong,partHeading:bsmData.partHeading}},'*');// 获取模型位置坐标\nconst modelPos=converter.current.wgs84ToModel(newState.longitude,newState.latitude);const initialPosition=new THREE.Vector3(modelPos.x,1.0,-modelPos.y);const initialRotation=Math.PI-newState.heading*Math.PI/180;// 应用平滑滤波 - 使用已有的滤波函数\nconst newPosition=filterPosition(initialPosition,bsmid);const newRotation=filterRotation(initialRotation,bsmid);// 检查该车辆是否已存在于场景中\nlet vehicleObj=vehicleModels.get(bsmid);// 检查是否是主车\nconst isMainVehicle=bsmid===mainVehicleBsmId;if(!vehicleObj&&preloadedVehicleModel){// 创建一个新的车辆模型实例\nconst newVehicleModel=preloadedVehicleModel.clone();// 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n// 这样可以避免车辆突然出现的视觉冲击\nnewVehicleModel.position.set(newPosition.x,-5,newPosition.z);newVehicleModel.rotation.y=newRotation;// 设置BSM车辆为突出的颜色\nnewVehicleModel.traverse(child=>{if(child.isMesh&&child.material){// // 保存原始材质颜色\n// if (!child.userData.originalColor && child.material.color) {\n//   child.userData.originalColor = child.material.color.clone();\n// }\n// // 设置为更鲜艳的颜色\n// if (isMainVehicle) {\n//   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n// } else {\n//   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n// }\n// // 增加材质亮度\n// child.material.emissive = new THREE.Color(0x222222);\n// // 初始设置为半透明\n// child.material.transparent = true;\n// child.material.opacity = 0.6;\n// child.material.needsUpdate = true;\nconst newMaterial=child.material.clone();child.material=newMaterial;// 修改颜色逻辑（与原模型解耦）\nif(isMainVehicle){newMaterial.color.set(0x00BFFF);}else{newMaterial.color.set(0xFF6347);}newMaterial.emissive=new THREE.Color(0x222222);newMaterial.transparent=true;// newMaterial.opacity = 0.6;\nnewMaterial.needsUpdate=true;}});// 创建速度显示标签\nconst speedLabel=createTextSprite(`${Math.round(newState.speed)} km/h`,{backgroundColor:isMainVehicle?{r:0,g:191,b:255,a:0.8}:// 主车使用蓝色背景\n{r:255,g:99,b:71,a:0.8},// 其他车辆使用红色背景\ntextColor:{r:255,g:255,b:255,a:1.0},// 白色文本\nfontSize:20,padding:8});speedLabel.position.set(0,7,0);// 位于车辆顶部上方\nspeedLabel.renderOrder=1000;// 确保在最上层渲染\nspeedLabel.material.opacity=0.6;// 初始半透明\nnewVehicleModel.add(speedLabel);scene.add(newVehicleModel);// 保存车辆引用到车辆模型集合中\nvehicleModels.set(bsmid,{model:newVehicleModel,lastUpdate:Date.now(),type:'1',// 设置为机动车类型\nisMain:isMainVehicle,speedLabel:speedLabel// 保存速度标签引用\n});// console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n// 使用补间动画使车辆从地下逐渐显示出来\nnew TWEEN.Tween(newVehicleModel.position).to({y:0.5},500).easing(TWEEN.Easing.Quadratic.Out).start();// 使用补间动画使车辆从半透明变为完全不透明\nnewVehicleModel.traverse(child=>{if(child.isMesh&&child.material&&child.material.transparent){new TWEEN.Tween({opacity:0.6}).to({opacity:1.0},500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function(){child.material.opacity=this.opacity;child.material.needsUpdate=true;}).start();}});// 为速度标签也添加透明度动画\nnew TWEEN.Tween({opacity:0.6}).to({opacity:1.0},500).easing(TWEEN.Easing.Quadratic.Out).onUpdate(function(){speedLabel.material.opacity=this.opacity;speedLabel.material.needsUpdate=true;}).start();// 如果是主车，设置全局引用\nif(isMainVehicle){globalVehicleRef=newVehicleModel;setVehicleState(newState);console.log('设置全局主车引用:',bsmid);}}else if(vehicleObj){// 应用滤波\nconst filteredPosition=filterPosition(newPosition,bsmid);const filteredRotation=filterRotation(newRotation,bsmid);// 更新现有车辆位置和朝向\nvehicleObj.model.position.copy(filteredPosition);vehicleObj.model.rotation.y=filteredRotation;vehicleObj.model.updateMatrix();vehicleObj.model.updateMatrixWorld(true);vehicleObj.lastUpdate=Date.now();vehicleObj.isMain=isMainVehicle;// 更新主车状态\n// 更新速度标签文本\nif(vehicleObj.speedLabel){vehicleObj.speedLabel.material.map.dispose();vehicleObj.model.remove(vehicleObj.speedLabel);}// 创建新的速度标签\nconst speedLabel=createTextSprite(`${Math.round(newState.speed*3.6)} km/h`,{backgroundColor:isMainVehicle?{r:0,g:191,b:255,a:0.8}:// 主车使用蓝色背景\n{r:255,g:99,b:71,a:0.8},// 其他车辆使用红色背景\ntextColor:{r:255,g:255,b:255,a:1.0},// 白色文本\nfontSize:20,padding:8});speedLabel.position.set(0,15,0);// 位于车辆顶部上方\nspeedLabel.renderOrder=1000;// 确保在最上层渲染\nvehicleObj.model.add(speedLabel);vehicleObj.speedLabel=speedLabel;// console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n// 如果是主车，同时更新全局引用和状态（用于相机跟随）\nif(isMainVehicle){globalVehicleRef=vehicleObj.model;setVehicleState(newState);}}// 清理长时间未更新的车辆\nconst now=Date.now();const CLEANUP_THRESHOLD=1000;// 增加到10秒，避免频繁清理造成闪烁\nvehicleModels.forEach((modelData,id)=>{const timeSinceLastUpdate=now-modelData.lastUpdate;// 对于即将超时的车辆，先降低透明度，而不是直接移除\nif(timeSinceLastUpdate>CLEANUP_THRESHOLD*0.7&&timeSinceLastUpdate<=CLEANUP_THRESHOLD){// const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\nconst opacity=1;modelData.model.traverse(child=>{if(child.isMesh&&child.material){// 如果材质还没有透明度设置，先保存初始状态\nif(child.material.transparent===undefined){child.material.originalTransparent=child.material.transparent||false;child.material.originalOpacity=child.material.opacity||1.0;}// 设置透明度\nchild.material.transparent=true;child.material.opacity=opacity;child.material.needsUpdate=true;}});// 如果有速度标签，也调整其透明度\nif(modelData.speedLabel){modelData.speedLabel.material.opacity=opacity;modelData.speedLabel.material.needsUpdate=true;}}// 完全超时，移除车辆\nelse if(timeSinceLastUpdate>CLEANUP_THRESHOLD){// 清理资源\nif(modelData.speedLabel){modelData.speedLabel.material.map.dispose();modelData.speedLabel.material.dispose();modelData.model.remove(modelData.speedLabel);}modelData.model.traverse(child=>{if(child.isMesh){if(child.material){if(Array.isArray(child.material)){child.material.forEach(m=>m.dispose());}else{child.material.dispose();}}if(child.geometry)child.geometry.dispose();}});// 从场景中移除\nscene.remove(modelData.model);vehicleModels.delete(id);// 同时清除该车辆的滤波缓存\nvehicleLastPositions.delete(id);vehicleLastRotations.delete(id);console.log(`移除长时间未更新的车辆: ID ${id}`);}});return;}// SPAT消息处理\nif(topic===MQTT_CONFIG.spat){// console.log('收到SPAT消息:', message);\ntry{const payload=JSON.parse(message);// 检查设备时间戳\nconst deviceMac=payload.mac;const messageTimestamp=payload.tm;// 获取该设备的最新时间戳\nconst lastTimestamp=deviceTimestamps.get(deviceMac);// 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\nif(lastTimestamp&&messageTimestamp<lastTimestamp){// console.log('忽略过期的SPAT消息:', {\n//   设备MAC: deviceMac,\n//   消息时间戳: messageTimestamp,\n//   最新时间戳: lastTimestamp\n// });\nreturn;}// 更新设备时间戳\ndeviceTimestamps.set(deviceMac,messageTimestamp);// 修改：访问data.intersections而不是直接访问intersections\nif(payload.data&&payload.data.intersections&&Array.isArray(payload.data.intersections)){payload.data.intersections.forEach(intersection=>{const interId=intersection.interId;if(!interId){console.error('SPAT消息缺少interId:',intersection);return;}// console.log(`处理路口ID: ${interId} 的SPAT消息`);\n// 创建所有相位的数组 - 存储到trafficLightStates\nif(intersection.phases&&Array.isArray(intersection.phases)){// 构建存储相位信息的数组\nconst phasesInfo=[];intersection.phases.forEach(phase=>{// 修改：使用phaseId而不是id\nif(!phase.phaseId){console.error('相位信息缺少phaseId:',phase);return;}const phaseId=phase.phaseId.toString();// 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\nconst direction=phase.trafficDirec?getDirectionFromCode(phase.trafficDirec):getPhaseDirection(phaseId);// 修改：直接从phase中获取信号灯状态和剩余时间\nconst lightState=phase.trafficLight||'R';// 默认为红灯\nconst remainTime=parseInt(phase.remainTime)||0;// console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n// 构建相位信息对象\nconst phaseInfo={phaseId,direction,trafficLight:lightState,remainTime};// 添加到相位信息数组\nphasesInfo.push(phaseInfo);// 查找红绿灯模型并更新视觉效果\n// 尝试使用字符串ID和数字ID查找\nlet trafficLightKey=String(interId);let trafficLightModel=trafficLightsMap.get(trafficLightKey);if(!trafficLightModel){// 尝试使用数字ID\ntrafficLightKey=parseInt(interId);trafficLightModel=trafficLightsMap.get(trafficLightKey);}if(trafficLightModel){// 更新交通灯视觉效果\nupdateTrafficLightVisual(trafficLightModel,phaseInfo);// 更新弹出窗信息\nif(selectedIntersection&&selectedIntersection.interId===interId){setTrafficLightPopover(prev=>({...prev,visible:true,phaseId,direction,state:lightState,remainTime}));}}else{// console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n}});// 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\nlet modelKey=null;// 尝试字符串ID\nconst strId=String(interId);if(trafficLightsMap.has(strId)){modelKey=strId;}else{// 尝试数字ID\nconst numId=parseInt(interId);if(trafficLightsMap.has(numId)){modelKey=numId;}}if(modelKey!==null){// 使用正确的ID类型存储状态信息\ntrafficLightStates.set(modelKey,{updateTime:Date.now(),phases:phasesInfo});console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);// 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\nif(window.currentPopoverIdRef&&(window.currentPopoverIdRef.current===modelKey||window.currentPopoverIdRef.current===String(modelKey)||window.currentPopoverIdRef.current===parseInt(modelKey))){console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);// 强制更新弹窗ID为当前数据的正确ID类型\nwindow.currentPopoverIdRef.current=modelKey;// 如果没有更新定时器则创建一个\nif(window.trafficLightUpdateTimerRef&&!window.trafficLightUpdateTimerRef.current){console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);setTimeout(()=>{window.showTrafficLightPopup(modelKey);},100);}}}else{// 如果找不到模型，仍使用原始ID存储\ntrafficLightStates.set(interId,{updateTime:Date.now(),phases:phasesInfo});// console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n}}else{console.error('SPAT消息缺少相位信息:',intersection);}});}else{console.error('SPAT消息格式错误，缺少data.intersections数组:',payload);}}catch(error){console.error('解析SPAT消息出错:',error,message);}return;}// 处理 RSI 消息\nif(topic===MQTT_CONFIG.rsi&&payload.type==='RSI'){// console.log('收到RSI消息:', payload);\n// 发送 RSI 消息到 RealTimeTraffic 组件\nwindow.postMessage({type:'RSI',data:payload.data},'*');const rsiData=payload.data;const rsuId=rsiData.rsuId;const events=rsiData.rtes||[];events.forEach(event=>{const eventId=event.rteId;const eventType=event.eventType;const description=event.description;const startTime=event.startTime;const endTime=event.endTime;// 将基准点经纬度转换为模型坐标\nconst modelPos=converter.current.wgs84ToModel(parseFloat(rsiData.posLong),parseFloat(rsiData.posLat));// 根据事件类型显示不同的提示或标记\nlet warningText='';let warningColor='';switch(eventType){case'401':// 道路抛洒物\nwarningText='道路抛洒物';warningColor='#ff4d4f';break;case'404':// 道路障碍物\nwarningText='道路障碍物';warningColor='#faad14';break;case'405':// 行人通过马路\nwarningText='行人通过马路';warningColor='#1890ff';break;case'904':// 逆行车辆\nwarningText='逆行车辆';warningColor='#f5222d';break;case'910':// 违停车辆\nwarningText='违停车辆';warningColor='#722ed1';break;case'1002':// 道路施工\nwarningText='道路施工';warningColor='#fa8c16';break;case'901':// 车辆超速\nwarningText='车辆超速';warningColor='#eb2f96';break;default:warningText=description||'未知事件';warningColor='#8c8c8c';}// 显示警告标记\nshowWarningMarker(modelPos,warningText,warningColor);// console.log('RSI事件处理:', {\n//   事件ID: eventId,\n//   事件类型: eventType,\n//   事件说明: description,\n//   开始时间: startTime,\n//   结束时间: endTime,\n//   位置: modelPos\n// });\n});return;}// 处理场景事件消息\nif(topic===MQTT_CONFIG.scene&&payload.type==='SCENE'){// console.log('收到场景事件消息:', payload);\nconst sceneData=payload.data;const sceneId=sceneData.sceneId;const sceneType=sceneData.sceneType;const sceneDesc=sceneData.sceneDesc;const position={latitude:parseFloat(sceneData.partLat),longitude:parseFloat(sceneData.partLong)};// 将经纬度转换为模型坐标\nconst modelPos=converter.current.wgs84ToModel(position.longitude,position.latitude);// 根据场景类型显示不同的提示或标记\nswitch(sceneType){case'2':// 交叉路口碰撞预警\nshowWarningMarker(modelPos,'交叉路口碰撞预警','#ff4d4f');break;case'9-5':// 道路危险状况预警（施工）\nshowWarningMarker(modelPos,'道路施工','#faad14');break;case'9-6':// 前方有障碍物\nshowWarningMarker(modelPos,'前方障碍物','#ff7a45');break;case'10':// 限速提醒\nconst speedLimit=sceneData.eventData1;// 限速值\nshowWarningMarker(modelPos,`限速${speedLimit}km/h`,'#1890ff');break;case'12':// 交通参与者碰撞预警\nshowWarningMarker(modelPos,'碰撞预警','#f5222d');break;case'13':// 绿波车速引导\nshowWarningMarker(modelPos,'绿波引导','#52c41a');break;case'21-8':// 禁止鸣笛\nshowWarningMarker(modelPos,'禁止鸣笛','#722ed1');break;case'34':// 逆行车辆提醒\nshowWarningMarker(modelPos,'逆行警告','#eb2f96');break;case'33':// 违章占道车辆预警\nshowWarningMarker(modelPos,'违章占道','#fa8c16');break;case'999':// 信号灯优先\nconst priorityType=sceneData.eventData1;// 优先类型\nconst duration=sceneData.eventData2;// 优先时长\nshowWarningMarker(modelPos,`信号优先-${getPriorityTypeText(priorityType)}${duration}秒`,'#13c2c2');break;}return;}// 如果不是RSM或BSM消息，则记录为其他类型\n// console.log('未知类型消息:', {\n//   topic,\n//   type: payload.type,\n//   data: payload\n// });\n}catch(error){console.error('处理MQTT消息失败:',error);console.error('原始消息内容:',message);}};// 修改初始化MQTT连接函数\nconst initMqttClient=()=>{console.log('正在连接MQTT服务器...');const wsUrl=`ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;console.log('尝试连接WebSocket:',wsUrl);// 创建WebSocket连接，不指定协议\nconst ws=new WebSocket(wsUrl);ws.onopen=()=>{console.log('WebSocket连接成功');};ws.onmessage=event=>{try{const message=JSON.parse(event.data);// 处理连接确认消息\nif(message.type==='connect'){console.log('收到连接确认:',message);return;}// 处理心跳消息\nif(message.type==='ping'){return;}// 处理MQTT消息\nif(message.type==='message'&&message.topic&&message.payload){// 直接将消息传递给handleMqttMessage处理\nhandleMqttMessage(message.topic,JSON.stringify(message.payload));}}catch(error){console.error('处理WebSocket消息失败:',error);}};ws.onerror=error=>{console.error('WebSocket错误:',error);};ws.onclose=()=>{console.log('WebSocket连接关闭');// 5秒后尝试重新连接\nsetTimeout(initMqttClient,5000);};// 保存WebSocket引用\nmqttClientRef.current=ws;};useEffect(()=>{if(!containerRef.current)return;// 预加载所有模型\npreloadModels();// 创建场景\nscene=new THREE.Scene();// 使用全局scene变量\n// 创建相机\nconst camera=new THREE.PerspectiveCamera(60,window.innerWidth/window.innerHeight,0.1,2000);// camera.position.set(0, 300, 0); // 初始为全局视角\ncamera.position.set(0,100,0);// 初始为全局视角\ncamera.lookAt(0,0,0);cameraRef.current=camera;// 创建渲染器\nconst renderer=new THREE.WebGLRenderer({antialias:true});renderer.setSize(window.innerWidth,window.innerHeight);renderer.setClearColor(0x000000);renderer.setPixelRatio(window.devicePixelRatio);containerRef.current.appendChild(renderer.domElement);// 修改光照设置\n// 添加环境光和平行光\nconst ambientLight=new THREE.AmbientLight(0xffffff,0.8);// 增加环境光强度从0.5到0.8\nscene.add(ambientLight);// 添加多个平行光源，从不同角度照亮车辆\nconst directionalLight1=new THREE.DirectionalLight(0xffffff,1.0);// 增加强度从0.8到1.0\ndirectionalLight1.position.set(10,10,10);scene.add(directionalLight1);// 添加第二个平行光源，从另一个角度照亮\nconst directionalLight2=new THREE.DirectionalLight(0xffffff,0.8);directionalLight2.position.set(-10,8,-10);scene.add(directionalLight2);// 添加一个聚光灯，专门照亮车辆\nconst spotLight=new THREE.SpotLight(0xffffff,1.0);spotLight.position.set(0,50,0);spotLight.angle=Math.PI/4;spotLight.penumbra=0.1;spotLight.decay=2;spotLight.distance=200;scene.add(spotLight);// 创建控制器\ncontrols=new OrbitControls(camera,renderer.domElement);controls.enableDamping=true;controls.dampingFactor=0.05;controls.screenSpacePanning=false;controls.minDistance=50;controls.maxDistance=500;controls.maxPolarAngle=Math.PI/2.1;controls.minPolarAngle=0;controls.target.set(0,0,0);controls.update();// 打印相机和控制器引用\nconsole.log('初始化完成',{camera:!!camera,controls:!!controls,cameraRef:!!cameraRef.current});// 修改加载车辆模型的函数\nconst loadVehicleModel=()=>{return new Promise((resolve,reject)=>{const vehicleLoader=new GLTFLoader();vehicleLoader.load(`${BASE_URL}/changli2/vehicle.glb`,gltf=>{const vehicleModel=gltf.scene;// 创建一个新的Group作为根容器\nconst vehicleContainer=new THREE.Group();// 调整模型材质\nvehicleModel.traverse(child=>{if(child.isMesh){// 检查并调整材质\nif(child.material){// 创建新的标准材质\nconst newMaterial=new THREE.MeshStandardMaterial({color:0xffffff,// 白色\nmetalness:0.2,// 降低金属感\nroughness:0.1,// 降低粗糙度\nenvMapIntensity:1.0// 环境贴图强度\n});// 保留原始贴图\nif(child.material.map){newMaterial.map=child.material.map;}// 应用新材质\nchild.material=newMaterial;console.log('已调整车辆材质:',child.name);}}});// 遍历模型的所有子对象，确保它们都被正确添加到容器中\nwhile(vehicleModel.children.length>0){const child=vehicleModel.children[0];vehicleContainer.add(child);}// 确保容器直接添加到场景根节点\nscene.add(vehicleContainer);// 保存容器的引用\nglobalVehicleRef=vehicleContainer;console.log('车辆模型加载成功，使用容器包装');setIsVehicleLoaded(true);resolve(vehicleContainer);},xhr=>{console.log(`车辆模型加载进度: ${(xhr.loaded/xhr.total*100).toFixed(2)}%`);},reject);});};// 修改初始化流程\nconst initializeScene=async()=>{try{// 1. 加载车辆模型\n// const vehicleContainer = await loadVehicleModel();\n// 2. 初始化MQTT客户端\ninitMqttClient();// 3. 设置初始位置\n// if (vehicleContainer) {\n//   const initialState = {\n//     longitude: 113.0022348,\n//     latitude: 28.0698301,\n//     heading: 0\n//   };\n//   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n//   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n//   vehicleContainer.position.set(0, 1.0, 0);\n//   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n//   vehicleContainer.updateMatrix();\n//   vehicleContainer.updateMatrixWorld(true);\n//   currentPosition = vehicleContainer.position.clone();\n// }\n}catch(error){console.error('初始化场景失败:',error);}};// 添加重试逻辑的加载函数\nconst loadModelWithRetry=function(url){let maxRetries=arguments.length>1&&arguments[1]!==undefined?arguments[1]:3;return new Promise((resolve,reject)=>{const attemptLoad=retriesLeft=>{console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);const loader=new GLTFLoader();loader.load(url,gltf=>{console.log(`模型加载成功: ${url}`);resolve(gltf);},xhr=>{console.log(`加载进度: ${(xhr.loaded/xhr.total*100).toFixed(2)}%`);},error=>{console.error(`加载失败: ${url}`,error);if(retriesLeft>0){console.log(`将在 1 秒后重试...`);setTimeout(()=>attemptLoad(retriesLeft-1),1000);}else{reject(error);}});};attemptLoad(maxRetries);});};// 使用重试逻辑加载模型\nconst loader=new GLTFLoader();loader.load(`${BASE_URL}/changli2/CLG_ALL_1025.glb`,async gltf=>{try{const model=gltf.scene;model.scale.set(1,1,1);model.position.set(0,0,0);// 检查scene是否初始化\nif(scene){scene.add(model);// 在校园模型加载完成后初始化场景\nawait initializeScene();}else{console.error('无法添加模型：场景未初始化');}}catch(error){console.error('处理模型时出错:',error);}},xhr=>{console.log(`加载进度: ${(xhr.loaded/xhr.total*100).toFixed(2)}%`);},error=>{console.error('模型加载错误:',error);console.error('错误详情:',{错误类型:error.type,错误消息:error.message,加载URL:`${BASE_URL}/changli2/CLG_ALL_1025.glb`,完整URL:`${BASE_URL}/changli2/CLG_ALL_1025.glb`});});// 修改动画循环\nconst animate=()=>{animationFrameRef.current=requestAnimationFrame(animate);// 更新 TWEEN 动画\nTWEEN.update();// 更新行人动画 - 只更新存在的混合器\nconst deltaTime=clock.getDelta();// 使用Map而不是ref.current\nif(peopleAnimationMixers.size>0){peopleAnimationMixers.forEach(mixer=>{mixer.update(deltaTime);});}if(cameraMode==='follow'&&globalVehicleRef){// 在跟随模式下禁用控制器\ncontrols.enabled=false;// 获取车辆当前位置\nconst vehiclePos=globalVehicleRef.position.clone();// 获取车辆旋转角度\nconst vehicleRotation=globalVehicleRef.rotation.y;// 计算相机偏移量\n// 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\nconst adjustedRotation=-(vehicleRotation-Math.PI)+Math.PI/2*3;// 增加距离到 50 单位\nconst cameraOffset=new THREE.Vector3(-50*Math.cos(adjustedRotation),200,-50*Math.sin(adjustedRotation));// 计算目标相机位置和观察点\nconst targetCameraPosition=vehiclePos.clone().add(cameraOffset);const targetLookAt=vehiclePos.clone();// 初始化上一帧数据（如果是首次）\nif(!lastCameraPosition.current){lastCameraPosition.current=targetCameraPosition.clone();}if(!lastCameraTarget.current){lastCameraTarget.current=targetLookAt.clone();}// 应用平滑处理 - 使用lerp进行线性插值\nlastCameraPosition.current.lerp(targetCameraPosition,1-cameraSmoothing);lastCameraTarget.current.lerp(targetLookAt,1-cameraSmoothing);// 设置相机位置为平滑后的位置\ncamera.position.copy(lastCameraPosition.current);// 重置相机方向\ncamera.up.set(0,1,0);// 设置相机观察点为平滑后的目标\ncamera.lookAt(lastCameraTarget.current);// 强制更新相机矩阵\ncamera.updateProjectionMatrix();camera.updateMatrix();camera.updateMatrixWorld(true);// 禁用控制器\ncontrols.enabled=false;// 确保控制器不会覆盖相机设置\ncontrols.target.copy(lastCameraTarget.current);controls.update();console.log('相机设置:',{车辆位置:vehiclePos.toArray(),相机位置:camera.position.toArray(),相机目标:lastCameraTarget.current.toArray(),相机朝向:camera.getWorldDirection(new THREE.Vector3()).toArray()});}else if(cameraMode==='global'){// 在全局模式或切换模式时重置平滑变量\nlastCameraPosition.current=null;lastCameraTarget.current=null;// 在全局模式下启用控制器\ncontrols.enabled=true;// 确保相机的up向量保持正确\ncamera.up.set(0,1,0);// 如果相机位置偏离太多，重置到默认位置\nif(Math.abs(camera.position.y)<50){camera.position.set(0,300,0);controls.target.set(0,0,0);camera.lookAt(controls.target);controls.update();}//         // 强制更新相机矩阵\n// camera.updateProjectionMatrix();\ncamera.updateMatrix();camera.updateMatrixWorld(true);}else if(cameraMode==='intersection'){// 在路口视角模式下也重置平滑变量\nlastCameraPosition.current=null;lastCameraTarget.current=null;// 路口视角模式\ncontrols.update();}if(controls)controls.update();if(scene&&camera){renderer.render(scene,camera);}};animate();// 处理窗口大小变化\nconst handleResize=()=>{camera.aspect=window.innerWidth/window.innerHeight;camera.updateProjectionMatrix();renderer.setSize(window.innerWidth,window.innerHeight);};window.addEventListener('resize',handleResize);// 添加全局函数用于手动切换视角\nwindow.setGlobalView=()=>{if(cameraRef.current){cameraRef.current.position.set(0,300,0);cameraRef.current.lookAt(0,0,0);cameraRef.current.updateMatrix();cameraRef.current.updateMatrixWorld(true);if(controls){controls.target.set(0,0,0);controls.enabled=true;controls.update();}cameraMode='global';console.log('手动切换到全局视角');return true;}return false;};// 修改清理函数\nreturn()=>{console.log('开始清理组件...');// 1. 停止渲染循环\nif(animationFrameRef.current){cancelAnimationFrame(animationFrameRef.current);animationFrameRef.current=null;}// 2. 停止所有 TWEEN 动画\nTWEEN.removeAll();// 3. 清理所有动画混合器\npeopleAnimationMixers.forEach(mixer=>{resourceManager.removeMixer(mixer);});peopleAnimationMixers.clear();// 4. 清理所有其他资源\nresourceManager.cleanup();// 5. 清理场景中的所有对象\nif(scene){const objectsToRemove=[];scene.traverse(object=>{if(object.isMesh){if(object.geometry){object.geometry.dispose();}if(object.material){if(Array.isArray(object.material)){object.material.forEach(material=>{if(material.map)material.map.dispose();material.dispose();});}else{if(object.material.map)object.material.map.dispose();object.material.dispose();}}if(object!==scene){objectsToRemove.push(object);}}});// 从场景中移除对象\nobjectsToRemove.forEach(obj=>{if(obj.parent){obj.parent.remove(obj);}});scene.clear();}// 6. 清理渲染器\nif(renderer){renderer.setAnimationLoop(null);if(containerRef.current&&renderer.domElement&&renderer.domElement.parentNode===containerRef.current){containerRef.current.removeChild(renderer.domElement);}renderer.dispose();renderer.forceContextLoss();}// 7. 清理其他资源\nif(controls){controls.dispose();}// 8. 清理数据结构\nvehicleLastPositions.clear();vehicleLastRotations.clear();deviceTimestamps.clear();vehicleModels.clear();trafficLightsMap.clear();trafficLightStates.clear();console.log('组件清理完成');};},[]);// 在组件挂载时获取主车信息和添加事件监听\nuseEffect(()=>{// 初始获取主车信息\nfetchMainVehicleBsmId();// 添加自定义事件监听，用于接收主车变更通知\nconst handleMainVehicleChange=()=>{console.log('接收到主车变更通知，重新获取主车信息');fetchMainVehicleBsmId();};// 监听主车变更事件\nwindow.addEventListener('mainVehicleChanged',handleMainVehicleChange);// 定时刷新主车信息（每分钟一次）\nconst intervalId=setInterval(()=>{fetchMainVehicleBsmId();},60000);// 组件卸载时清理事件监听和定时器\nreturn()=>{window.removeEventListener('mainVehicleChanged',handleMainVehicleChange);clearInterval(intervalId);};},[]);// 添加一个useEffect钩子在场景初始化完成后创建红绿灯\nuseEffect(()=>{// 在场景加载后初始化红绿灯\nif(scene&&converter.current){console.log('准备创建红绿灯模型');// 延迟一秒创建红绿灯，确保模型已加载\nconst timer=setTimeout(()=>{if(scene&&converter.current){// 再次检查，以防延迟期间组件卸载\ncreateTrafficLights(converter.current);}},2000);return()=>clearTimeout(timer);}else{console.log('场景或坐标转换器未准备好，暂不创建红绿灯');}},[scene]);// 添加点击事件处理\nuseEffect(()=>{if(containerRef.current){// 定义点击处理函数\nconst handleClick=event=>{if(scene&&cameraRef.current){handleMouseClick(event,containerRef.current,scene,cameraRef.current);}};// 添加点击事件监听\ncontainerRef.current.addEventListener('click',handleClick);// 记录到控制台\nconsole.log('已添加点击事件监听器到容器',!!containerRef.current);// 清理函数\nreturn()=>{if(containerRef.current){containerRef.current.removeEventListener('click',handleClick);console.log('已移除点击事件监听器');}};}},[scene,cameraRef.current]);// 初始化场景 - 简化为空函数，避免引用错误\nconst initScene=useCallback(()=>{console.log('initScene函数已禁用');// 原始实现已移除，避免canvasRef未定义的错误\n},[containerRef,setCurrentRSU,trafficLightsMap]);// 创建简单交通灯模型\nconst createSimpleTrafficLight=()=>{const geometry=new THREE.BoxGeometry(4,15,4);const material=new THREE.MeshBasicMaterial({color:0x333333});const trafficLightModel=new THREE.Mesh(geometry,material);// 添加基座\nconst baseGeometry=new THREE.CylinderGeometry(2,2,2,32);const baseMaterial=new THREE.MeshBasicMaterial({color:0x666666});const baseModel=new THREE.Mesh(baseGeometry,baseMaterial);baseModel.position.set(0,-8.5,0);trafficLightModel.add(baseModel);return trafficLightModel;};// 添加额外的点击检测辅助对象\nconst addClickHelpers=()=>{if(!scene)return;// 为每个红绿灯添加一个透明的大型碰撞体，便于点击\ntrafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){// 创建一个较大的碰撞检测几何体\nconst helperGeometry=new THREE.SphereGeometry(3,3,3);const helperMaterial=new THREE.MeshBasicMaterial({color:0xff00ff,//\ntransparent:false,opacity:0.1,// 几乎透明\ndepthWrite:false});const helperMesh=new THREE.Mesh(helperGeometry,helperMaterial);helperMesh.position.set(0,0,0);// 放在红绿灯位置\n// 标记为click helper\nhelperMesh.userData={type:'trafficLight',interId:interId,name:lightObj.intersection.name,isClickHelper:true};// 添加到红绿灯模型\nlightObj.model.add(helperMesh);console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);}});};// 在创建红绿灯之后调用\nuseEffect(()=>{// 等待红绿灯创建完成后添加点击辅助对象\nconst timer=setTimeout(()=>{if(trafficLightsMap.size>0){console.log('添加红绿灯点击辅助对象');// addClickHelpers();\n}},5500);// 延迟略长于debugTrafficLights\nreturn()=>clearTimeout(timer);},[]);// // 在组件加载完毕后调用调试函数\n// useEffect(() => {\n//   // 延迟5秒调用调试函数，确保所有模型都已加载\n//   const timer = setTimeout(() => {\n//     console.log('调用场景调试函数');\n//     if (window.debugScene) {\n//       window.debugScene(); // 使用全局函数\n//     } else {\n//       console.error('debugScene函数未定义');\n//     }\n//   }, 5000);\n//   return () => clearTimeout(timer);\n// }, []);\n// 在useEffect中添加定时器清理逻辑\nuseEffect(()=>{return()=>{// 组件卸载时清理定时器\nif(trafficLightUpdateTimerRef.current){clearInterval(trafficLightUpdateTimerRef.current);trafficLightUpdateTimerRef.current=null;console.log('已清理红绿灯状态更新定时器');}};},[]);// 空依赖数组确保只在组件挂载和卸载时运行\n// 添加关闭弹窗时清理定时器的逻辑\nuseEffect(()=>{if(!trafficLightPopover.visible&&trafficLightUpdateTimerRef.current){clearInterval(trafficLightUpdateTimerRef.current);trafficLightUpdateTimerRef.current=null;currentPopoverIdRef.current=null;console.log('弹窗关闭，已清理红绿灯状态更新定时器');}},[trafficLightPopover.visible]);// 添加自动选择第一个路口的逻辑\nuseEffect(()=>{// 确保路口数据已加载\nif(intersectionsData&&intersectionsData.intersections&&intersectionsData.intersections.length>0){// 确保只在组件初次渲染并且未选择路口时执行\nif(!selectedIntersection){// 查找第一个带有红绿灯的路口\nconst firstTrafficLightIntersection=intersectionsData.intersections.find(intersection=>intersection.hasTrafficLight!==false&&intersection.interId);// 如果找到带红绿灯的路口，优先选择它；否则选择第一个路口\nconst targetIntersection=firstTrafficLightIntersection||intersectionsData.intersections[0];console.log('自动选择路口:',targetIntersection.name,'具有红绿灯:',firstTrafficLightIntersection?'是':'否');// 延迟执行，确保场景和相机已初始化\nconst timer=setTimeout(()=>{handleIntersectionChange(targetIntersection.name);},2000);return()=>clearTimeout(timer);}}},[intersectionsData,selectedIntersection]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{style:labelStyle,children:\"\\u533A\\u57DF\\u9009\\u62E9\\uFF1A\"}),/*#__PURE__*/_jsx(Select,{style:intersectionSelectStyle,placeholder:\"\\u8BF7\\u9009\\u62E9\\u533A\\u57DF\\u4F4D\\u7F6E\",onChange:handleIntersectionChange,options:intersectionsData.intersections.map(intersection=>({value:intersection.name,label:intersection.name})),size:\"large\",bordered:true,dropdownStyle:{zIndex:1002,maxHeight:'300px'},value:selectedIntersection?selectedIntersection.name:undefined}),/*#__PURE__*/_jsx(\"div\",{ref:containerRef,style:{width:'100%',height:'100%'}}),trafficLightPopover.visible&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'absolute',left:`${trafficLightPopover.position.x}px`,top:`${trafficLightPopover.position.y}px`,transform:'translate(-50%, -100%)',zIndex:1003,backgroundColor:'rgba(0, 0, 0, 0.85)',color:'white',borderRadius:'4px',boxShadow:'0 2px 8px rgba(0, 0, 0, 0.3)',padding:'0',maxWidth:'240px',// 缩小最大宽度\nfontSize:'12px'// 缩小字体\n},children:[trafficLightPopover.content,/*#__PURE__*/_jsx(\"button\",{style:{position:'absolute',top:'0px',right:'0px',background:'none',border:'none',color:'white',fontSize:'12px',cursor:'pointer',padding:'2px 6px'},onClick:()=>handleClosePopover(setTrafficLightPopover),children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:buttonContainerStyle,children:[/*#__PURE__*/_jsx(\"button\",{style:{...buttonStyle,backgroundColor:viewMode==='follow'?'#1890ff':'rgba(255, 255, 255, 0.9)',color:viewMode==='follow'?'white':'black'},onClick:switchToFollowView,children:\"\\u8DDF\\u968F\\u89C6\\u89D2\"}),/*#__PURE__*/_jsx(\"button\",{style:{...buttonStyle,backgroundColor:viewMode==='global'?'#1890ff':'rgba(255, 255, 255, 0.9)',color:viewMode==='global'?'white':'black'},onClick:switchToGlobalView,children:\"\\u5168\\u5C40\\u89C6\\u89D2\"})]})]});};// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text){let parameters=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};const params={fontFace:parameters.fontFace||'Arial',fontSize:parameters.fontSize||16,// 从24px调小到16px\nfontWeight:parameters.fontWeight||'bold',borderThickness:parameters.borderThickness||4,borderColor:parameters.borderColor||{r:0,g:0,b:0,a:1.0},backgroundColor:parameters.backgroundColor||{r:255,g:255,b:255,a:0.8},textColor:parameters.textColor||{r:0,g:0,b:0,a:1.0},padding:parameters.padding||5};// 创建画布\nconst canvas=document.createElement('canvas');const context=canvas.getContext('2d');// 设置字体\ncontext.font=`${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;// 测量文本宽度\nconst textWidth=context.measureText(text).width;// 设置画布尺寸，考虑边框和填充\nconst width=textWidth+2*params.padding+2*params.borderThickness;const height=params.fontSize+2*params.padding+2*params.borderThickness;canvas.width=width;canvas.height=height;// 重新设置字体，因为改变画布尺寸会重置上下文\ncontext.font=`${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;context.textBaseline='middle';// 绘制背景和边框（圆角矩形）\nconst radius=8;context.beginPath();context.moveTo(params.borderThickness+radius,params.borderThickness);context.lineTo(width-params.borderThickness-radius,params.borderThickness);context.arcTo(width-params.borderThickness,params.borderThickness,width-params.borderThickness,params.borderThickness+radius,radius);context.lineTo(width-params.borderThickness,height-params.borderThickness-radius);context.arcTo(width-params.borderThickness,height-params.borderThickness,width-params.borderThickness-radius,height-params.borderThickness,radius);context.lineTo(params.borderThickness+radius,height-params.borderThickness);context.arcTo(params.borderThickness,height-params.borderThickness,params.borderThickness,height-params.borderThickness-radius,radius);context.lineTo(params.borderThickness,params.borderThickness+radius);context.arcTo(params.borderThickness,params.borderThickness,params.borderThickness+radius,params.borderThickness,radius);context.closePath();// 设置边框颜色\ncontext.strokeStyle=`rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;context.lineWidth=params.borderThickness;context.stroke();// 设置背景填充\ncontext.fillStyle=`rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;context.fill();// 设置文字颜色\ncontext.fillStyle=`rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;context.textAlign='center';// 绘制文本\ncontext.fillText(text,width/2,height/2);// 创建纹理\nconst texture=new THREE.CanvasTexture(canvas);texture.minFilter=THREE.LinearFilter;texture.needsUpdate=true;// 创建精灵材质\nconst spriteMaterial=new THREE.SpriteMaterial({map:texture,transparent:true});// 创建精灵\nconst sprite=new THREE.Sprite(spriteMaterial);sprite.scale.set(7,3.5,1);// 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\nsprite.material.depthTest=false;// 确保始终可见\n// 保存文本信息到userData，便于后续更新\nsprite.userData={text:text,params:params};return sprite;}// 在文件底部添加这个全局函数\nwindow.forceGlobalView=()=>{try{// 获取当前场景中的相机\nconst camera=document.querySelector('canvas').parentElement.__THREE_camera;if(camera){// 保存旧位置\nconst oldPos=camera.position.clone();// 设置新位置\ncamera.position.set(0,300,0);camera.up.set(0,1,0);camera.lookAt(0,0,0);// 更新矩阵\ncamera.updateMatrix();camera.updateMatrixWorld(true);// 更新控制器\nconst controls=document.querySelector('canvas').parentElement.__THREE_controls;if(controls){controls.target.set(0,0,0);controls.update();}console.log('强制设置全局视角成功',{旧位置:oldPos.toArray(),新位置:camera.position.toArray()});return true;}return false;}catch(e){console.error('强制设置全局视角失败',e);return false;}};// 修改车辆模型预加载函数\nconst preloadModels=async()=>{try{console.log('开始预加载所有模型...');const loader=new GLTFLoader();// 并行加载所有模型\ntry{const[trafficLightGltf,vehicleGltf,cyclistGltf,peopleGltf]=await Promise.all([loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`),loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`),loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),loader.loadAsync(`${BASE_URL}/changli2/people.glb`)]);// 处理机动车模型\nconsole.log('加载车辆模型...');preloadedVehicleModel=vehicleGltf.scene;preloadedVehicleModel.traverse(child=>{if(child.isMesh){const newMaterial=new THREE.MeshStandardMaterial({color:0xff0000,// 0xff0000, //红色 0xffffff,白色\nmetalness:0.2,roughness:0.1,envMapIntensity:1.0});// 保留原始贴图\nif(child.material.map){newMaterial.map=child.material.map;}child.materia=newMaterial;}});console.log('加载非机动车模型...');// 处理非机动车模型\npreloadedCyclistModel=cyclistGltf.scene;// 设置非机动车模型的缩放\npreloadedCyclistModel.scale.set(2,2,2);// 保持原始材质\npreloadedCyclistModel.traverse(child=>{if(child.isMesh&&child.material){// 只调整材质属性，保持原始颜色\nchild.material.metalness=0.1;child.material.roughness=0.8;child.material.envMapIntensity=1.0;}});console.log('加载行人模型...');// 处理行人模型\npreloadedPeopleModel=peopleGltf.scene;// 设置行人模型的缩放\n// preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n// 保持原始材质\npreloadedPeopleModel.traverse(child=>{if(child.isMesh&&child.material){// 只调整材质属性，保持原始颜色\nchild.material.metalness=0.1;child.material.roughness=0.8;child.material.envMapIntensity=1.0;}if(child.isMesh){child.castShadow=true;}});// 保存行人动画数据\nconsole.log('找到行人动画sss:',peopleGltf.animations.length,'个');if(peopleGltf.animations&&peopleGltf.animations.length>0){console.log('找到行人动画:',peopleGltf.animations.length,'个');peopleBaseModel=peopleGltf;}else{console.warn('行人模型没有包含动画数据');}console.log('加载红绿灯模型...');// 处理红绿灯模型\npreloadedTrafficLightModel=trafficLightGltf.scene;console.log('红绿灯模型：',preloadedTrafficLightModel);// 设置红绿灯模型的缩放\npreloadedTrafficLightModel.scale.set(6,6,6);// 保持原始材质\npreloadedTrafficLightModel.traverse(child=>{if(child.isMesh&&child.material){// 设置材质属性\nchild.material.metalness=0.2;child.material.roughness=0.3;child.material.envMapIntensity=1.2;}});console.log('所有模型预加载成功');}catch(error){console.error('加载特定模型失败，尝试单独加载:',error);// 如果整个Promise.all失败，尝试单独加载关键模型\ntry{if(!preloadedVehicleModel){const vehicleGltf=await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);preloadedVehicleModel=vehicleGltf.scene;}// // 尝试单独加载红绿灯模型\n// if (!preloadedTrafficLightModel) {\n//   console.log('正在单独加载红绿灯模型...');\n//   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n//   preloadedTrafficLightModel = trafficLightGltf.scene;\n//   preloadedTrafficLightModel.scale.set(3, 3, 3);\n//   console.log('红绿灯模型加载成功');\n// }\n}catch(err){console.error('单独加载模型也失败:',err);}}}catch(error){console.error('模型预加载失败:',error);}};// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText=type=>{const types={'1':'信号灯保持','2':'绿灯延长','3':'红灯截断','4':'相位插入','5':'相位插入','6':'优先未处理'};return types[type]||'未知类型';};// 添加辅助函数：显示警告标记\nconst showWarningMarker=(position,text,color)=>{// 添加安全检查 - 如果场景不存在，则直接返回\nif(!scene){console.warn('无法显示警告标记：场景不存在或已卸载');return;}try{// 创建一个新的警告标记\nconst sprite=createTextSprite(text);sprite.position.set(position.x,10,-position.y);// 设置位置，稍微升高以便可见\n// 为标记添加一个定时器，在1秒后自动移除\nsetTimeout(()=>{// 再次检查场景是否存在\nif(scene&&sprite.parent){scene.remove(sprite);}},100);// 将标记添加到场景中\nscene.add(sprite);// console.log('添加场景事件标记:', {\n//   位置: position,\n//   文本: text,\n//   颜色: color\n// });\n}catch(error){console.error('显示警告标记时出错:',error);}};// 添加创建红绿灯模型的函数\nconst createTrafficLights=converterInstance=>{if(!scene){console.error('无法创建红绿灯：场景未初始化');return;}if(!converterInstance){console.error('无法创建红绿灯：坐标转换器未初始化');return;}// 检查红绿灯模型是否已加载\nif(!preloadedTrafficLightModel){console.error('红绿灯模型未加载，尝试重新加载...');// 尝试重新加载红绿灯模型\nconst loader=new GLTFLoader();loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`).then(trafficLightGltf=>{preloadedTrafficLightModel=trafficLightGltf.scene;preloadedTrafficLightModel.scale.set(6,6,6);preloadedTrafficLightModel.traverse(child=>{if(child.isMesh&&child.material){child.material.metalness=0.2;child.material.roughness=0.3;child.material.envMapIntensity=1.2;}});console.log('红绿灯模型重新加载成功，开始创建红绿灯...');// 重新调用创建函数\ncreateTrafficLights(converterInstance);}).catch(error=>{console.error('红绿灯模型重新加载失败:',error);// 如果加载失败，使用简单的替代物体\ncreateFallbackTrafficLights(converterInstance);});return;}// 先清除现有的红绿灯\ntrafficLightsMap.forEach(lightObj=>{if(scene&&lightObj.model){scene.remove(lightObj.model);}});trafficLightsMap.clear();// 为每个路口创建红绿灯模型\nintersectionsData.intersections.forEach(intersection=>{if(intersection.hasTrafficLight===false){console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);return;}if(intersection.latitude&&intersection.longitude&&intersection.interId){const modelPos=converterInstance.wgs84ToModel(parseFloat(intersection.longitude),parseFloat(intersection.latitude));console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);try{// 确保模型存在且可以克隆\nif(!preloadedTrafficLightModel||!preloadedTrafficLightModel.clone){throw new Error('红绿灯模型无效或无法克隆');}// 创建红绿灯模型\nconst trafficLightModel=preloadedTrafficLightModel.clone();// 给模型一个名称便于调试\ntrafficLightModel.name=`交通灯-${intersection.name}`;// 设置位置，离地面高度为15米，提高可见性\ntrafficLightModel.position.set(modelPos.x,15,-modelPos.y);// 放大红绿灯模型尺寸，使其更容易被点击\ntrafficLightModel.scale.set(10,10,10);// 确保渲染顺序高，避免被其他对象遮挡\ntrafficLightModel.renderOrder=100;// 设置材质属性\ntrafficLightModel.traverse(child=>{if(child.isMesh){child.material.transparent=false;child.material.opacity=1.0;child.material.side=THREE.DoubleSide;child.material.depthWrite=true;child.material.depthTest=true;child.material.needsUpdate=true;child.renderOrder=100;}});// 添加交互所需的信息\ntrafficLightModel.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name};// 将红绿灯添加到场景中\nscene.add(trafficLightModel);// 存储红绿灯引用\ntrafficLightsMap.set(intersection.interId,{model:trafficLightModel,intersection:intersection,position:modelPos});console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);}catch(error){console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`,error);// 如果克隆失败，创建一个简单的替代物体\ncreateSimpleTrafficLight(intersection,modelPos,converterInstance);}}});// 在控制台输出所有红绿灯的信息\nconsole.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);trafficLightsMap.forEach((lightObj,interId)=>{console.log(`- ID ${interId}: ${lightObj.intersection.name}`);});};// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights=converterInstance=>{intersectionsData.intersections.forEach(intersection=>{// 跳过没有红绿灯的路口\nif(intersection.hasTrafficLight===false){return;}if(intersection.latitude&&intersection.longitude&&intersection.interId){// 转换经纬度到模型坐标\nconst modelPos=converterInstance.wgs84ToModel(parseFloat(intersection.longitude),parseFloat(intersection.latitude));createSimpleTrafficLight(intersection,modelPos,converterInstance);}});};// 创建简单的替代红绿灯\nconst createSimpleTrafficLight=(intersection,modelPos,converterInstance)=>{// 创建一个简单的几何体作为红绿灯 - 增大尺寸\nconst geometry=new THREE.BoxGeometry(10,30,10);const material=new THREE.MeshBasicMaterial({color:0x333333,transparent:false,opacity:1.0});const trafficLightModel=new THREE.Mesh(geometry,material);// 给模型一个名称便于调试\ntrafficLightModel.name=`简易交通灯-${intersection.name}`;// 设置位置 - 提高高度以增加可见性\ntrafficLightModel.position.set(modelPos.x,15,-modelPos.y);// 设置渲染顺序\ntrafficLightModel.renderOrder=100;// 添加交互所需的信息\ntrafficLightModel.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name};// 添加一个专门用于点击的大型碰撞体\nconst colliderGeometry=new THREE.SphereGeometry(3,12,12);const colliderMaterial=new THREE.MeshBasicMaterial({color:0xff00ff,transparent:true,opacity:0.0,// 完全透明\ndepthWrite:false});const collider=new THREE.Mesh(colliderGeometry,colliderMaterial);collider.name=`简易交通灯碰撞体-${intersection.name}`;collider.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name,isCollider:true};trafficLightModel.add(collider);// 将红绿灯添加到场景中\nscene.add(trafficLightModel);// 存储红绿灯引用\ntrafficLightsMap.set(intersection.interId,{model:trafficLightModel,intersection:intersection,position:modelPos});// 添加一个顶部灯光标识，使其更容易被看到\nconst lightGeometry=new THREE.SphereGeometry(5,16,16);const lightMaterial=new THREE.MeshBasicMaterial({color:0xFF0000});const lightMesh=new THREE.Mesh(lightGeometry,lightMaterial);lightMesh.position.set(0,15,0);// 给灯光相同的userData\nlightMesh.userData={type:'trafficLight',interId:intersection.interId,name:intersection.name};trafficLightModel.add(lightMesh);console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);};// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection=phaseId=>{switch(phaseId){case'1':return'北进口左转';case'2':return'北进口直行';case'3':return'北进口右转';case'5':return'东进口左转';case'6':return'东进口直行';case'7':return'东进口右转';case'9':return'南进口左转';case'10':return'南进口直行';case'11':return'南进口右转';case'13':return'西进口左转';case'14':return'西进口直行';case'15':return'西进口右转';default:return`相位${phaseId}`;}};// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode=dirCode=>{switch(dirCode){case'N':return'北向南';case'S':return'南向北';case'E':return'东向西';case'W':return'西向东';case'NE':return'东北向西南';case'NW':return'西北向东南';case'SE':return'东南向西北';case'SW':return'西南向东北';default:return`方向${dirCode}`;}};// 修改点击处理函数\nconst handleMouseClick=(event,container,sceneInstance,cameraInstance)=>{if(!container||!sceneInstance||!cameraInstance)return;console.log('触发点击事件处理函数',event.clientX,event.clientY);// 计算鼠标在容器中的相对位置\nconst rect=container.getBoundingClientRect();const mouseX=(event.clientX-rect.left)/container.clientWidth*2-1;const mouseY=-((event.clientY-rect.top)/container.clientHeight)*2+1;// 创建射线\nconst raycaster=new THREE.Raycaster();// 增加检测阈值，使小物体也能被点击到\nraycaster.params.Points.threshold=1;raycaster.params.Line.threshold=1;const mouseVector=new THREE.Vector2(mouseX,mouseY);raycaster.setFromCamera(mouseVector,cameraInstance);console.log('鼠标点击位置:',mouseX,mouseY);// 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\nconst trafficLightObjects=[];trafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){// 将模型添加到数组中\ntrafficLightObjects.push(lightObj.model);// 确保模型是可见的，并且不会被其他对象遮挡\nlightObj.model.visible=true;lightObj.model.renderOrder=1000;// 设置高渲染优先级\n// 确保模型的所有子对象都是可见的\nlightObj.model.traverse(child=>{child.visible=true;child.renderOrder=1000;});}});console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);// 首先：尝试直接检测红绿灯模型集合\nconst trafficLightIntersects=raycaster.intersectObjects(trafficLightObjects,true);if(trafficLightIntersects.length>0){console.log('直接命中红绿灯模型:',trafficLightIntersects.length);trafficLightIntersects.forEach((intersect,index)=>{console.log(`命中对象 ${index}:`,intersect.object.name||'无名称','距离:',intersect.distance,'userData:',intersect.object.userData);});// 获取第一个交点的对象\nconst obj=getTrafficLightFromObject(trafficLightIntersects[0].object);if(obj&&obj.userData&&obj.userData.type==='trafficLight'){// 获取红绿灯ID\nconst interId=obj.userData.interId;console.log('成功检测到红绿灯点击, 路口ID:',interId,'类型:',typeof interId);// 检查trafficLightsMap中可能的ID类型\nlet correctId=interId;if(typeof interId==='string'&&!trafficLightsMap.has(interId)&&trafficLightsMap.has(parseInt(interId))){correctId=parseInt(interId);console.log(`转换ID类型为数字: ${correctId}`);}else if(typeof interId==='number'&&!trafficLightsMap.has(interId)&&trafficLightsMap.has(String(interId))){correctId=String(interId);console.log(`转换ID类型为字符串: ${correctId}`);}// 显示弹窗\nwindow.showTrafficLightPopup(correctId);return;}}// 第二步：检测所有场景对象\nconst intersects=raycaster.intersectObjects(sceneInstance.children,true);console.log('射线检测到的对象数量:',intersects.length);if(intersects.length>0){// 输出所有检测到的对象信息用于调试\nintersects.forEach((intersect,index)=>{const obj=intersect.object;console.log(`检测到的对象 ${index}:`,obj.name||'无名称','userData:',obj.userData,'距离:',intersect.distance);});// 检查是否点击了红绿灯\nfor(let i=0;i<intersects.length;i++){const obj=getTrafficLightFromObject(intersects[i].object);if(obj&&obj.userData&&obj.userData.type==='trafficLight'){const interId=obj.userData.interId;window.showTrafficLightPopup(interId);return;}}}// 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\nconsole.log('尝试查找最接近点击位置的红绿灯');// 将红绿灯模型投影到屏幕坐标中\nlet closestLight=null;let minDistance=0.1;// 设置一个阈值，只有距离小于此值的才会被选中\ntrafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){const worldPos=new THREE.Vector3();// 获取模型的世界坐标\nlightObj.model.getWorldPosition(worldPos);// 将世界坐标投影到屏幕坐标\nconst screenPos=worldPos.clone();screenPos.project(cameraInstance);// 计算鼠标与红绿灯在屏幕上的距离\nconst dx=screenPos.x-mouseX;const dy=screenPos.y-mouseY;const distance=Math.sqrt(dx*dx+dy*dy);console.log(`路口 ${interId} 的距离:`,distance);if(distance<minDistance){minDistance=distance;closestLight={interId,distance};}}});if(closestLight){console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);// 显示弹窗\nwindow.showTrafficLightPopup(closestLight.interId);return;}console.log('未检测到任何红绿灯点击');};// 关闭弹出窗口的处理函数\nconst handleClosePopover=setPopoverState=>{// 清理定时器\nif(window.trafficLightUpdateTimerRef&&window.trafficLightUpdateTimerRef.current){clearInterval(window.trafficLightUpdateTimerRef.current);window.trafficLightUpdateTimerRef.current=null;console.log('已清理红绿灯状态更新定时器');}// 清理当前弹窗ID\nif(window.currentPopoverIdRef){window.currentPopoverIdRef.current=null;}// 更新弹窗状态为不可见\nsetPopoverState(prev=>({...prev,visible:false,content:null,// 清空内容\nphases:[]// 清空相位信息\n}));console.log('弹窗已关闭，所有相关资源已清理');};// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick=interId=>{try{var _document$querySelect,_document$querySelect2;// 检查是否有该ID的红绿灯\nconst trafficLight=trafficLightsMap.get(interId||'1');if(!trafficLight){console.error('未找到指定ID的红绿灯:',interId);// 输出所有可用ID\nconsole.log('可用的红绿灯ID:');trafficLightsMap.forEach((light,id)=>{console.log(`- ${id}: ${light.intersection.name}`);});return false;}// 获取红绿灯模型\nconst lightModel=trafficLight.model;// 模拟点击事件\nconst stateInfo=trafficLightStates.get(interId);const intersection=trafficLight.intersection;// 创建弹出窗口内容\nlet content;if(stateInfo&&stateInfo.phases){content=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',width:'220px',maxHeight:'300px',overflowY:'auto'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'bold',marginBottom:'6px',fontSize:'14px',borderBottom:'1px solid #eee',paddingBottom:'4px'},children:[intersection.name,\" (ID: \",interId,\")\"]}),/*#__PURE__*/_jsx(\"div\",{children:stateInfo.phases.map((phase,index)=>{let lightColor;switch(phase.trafficLight){case'G':lightColor='#00ff00';break;case'Y':lightColor='#ffff00';break;case'R':default:lightColor='#ff0000';break;}return/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'6px',backgroundColor:'rgba(255,255,255,0.1)',padding:'4px',borderRadius:'4px',fontSize:'12px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'bold'},children:getPhaseDirection(phase.phaseId)}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u706F\\u8272: \"}),/*#__PURE__*/_jsx(\"span\",{style:{color:lightColor,fontWeight:'bold',backgroundColor:'rgba(0,0,0,0.3)',padding:'0 3px',borderRadius:'2px'},children:phase.trafficLight==='R'?'红灯':phase.trafficLight==='Y'?'黄灯':'绿灯'})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u5012\\u8BA1\\u65F6: \"}),/*#__PURE__*/_jsxs(\"span\",{style:{fontWeight:'bold'},children:[phase.remainTime,\" \\u79D2\"]})]})]},index);})}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'6px',fontSize:'10px',color:'#888'},children:[\"\\u66F4\\u65B0\\u65F6\\u95F4: \",new Date().toLocaleTimeString(),\" (\\u81EA\\u52A8\\u5237\\u65B0)\"]})]});}else{content=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',maxWidth:'200px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'bold',marginBottom:'8px'},children:intersection.name}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u8DEF\\u53E3ID: \",interId]}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"})]});}// 获取弹窗位置 - 使用中心位置\nconst centerX=window.innerWidth/2-500;const centerY=window.innerHeight/2-500;// 获取全局的setTrafficLightPopover函数\nconst setPopoverState=(_document$querySelect=document.querySelector('#root'))===null||_document$querySelect===void 0?void 0:(_document$querySelect2=_document$querySelect.__REACT_INSTANCE)===null||_document$querySelect2===void 0?void 0:_document$querySelect2.setTrafficLightPopover;if(setPopoverState){// 直接调用React组件的状态更新函数\nsetPopoverState({visible:true,interId:interId,position:{x:centerX,y:centerY},content:content,phases:(stateInfo===null||stateInfo===void 0?void 0:stateInfo.phases)||[]});console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);return true;}else{// 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\nconst popover=document.createElement('div');popover.style.position='absolute';popover.style.left=`${centerX}px`;popover.style.top=`${centerY}px`;popover.style.transform='translate(-50%, -100%)';popover.style.zIndex='9999';popover.style.backgroundColor='rgba(0, 0, 0, 0.85)';popover.style.color='white';popover.style.borderRadius='4px';popover.style.boxShadow='0 2px 8px rgba(0, 0, 0, 0.3)';popover.style.padding='8px';popover.style.maxWidth='240px';popover.style.fontSize='12px';popover.innerHTML=`\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo?'红绿灯状态已加载':'当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;document.body.appendChild(popover);// 添加关闭按钮点击事件\nconst closeButton=popover.querySelector('button');if(closeButton){closeButton.addEventListener('click',()=>{document.body.removeChild(popover);});}console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);return true;}}catch(error){console.error('测试红绿灯点击失败:',error);return false;}};// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights=()=>{console.log('红绿灯列表:');if(!trafficLightsMap||trafficLightsMap.size===0){console.log('当前没有红绿灯对象');return[];}const list=[];trafficLightsMap.forEach((light,id)=>{console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);list.push({id,name:light.intersection.name,position:light.position});});return list;};// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n//   console.log('开始调试红绿灯模型...');\n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n//       // 添加到场景\n//       scene.add(highlightMesh);\n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup=interId=>{try{// 确保interId为字符串类型\ninterId=String(interId);console.log('调用showTrafficLightPopup函数, 参数ID:',interId,'类型:',typeof interId);console.log('当前trafficLightsMap大小:',trafficLightsMap.size);// 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\nlet trafficLight=trafficLightsMap.get(interId);if(!trafficLight){// 尝试转换为数字查找\nconst numericId=parseInt(interId);trafficLight=trafficLightsMap.get(numericId);if(trafficLight){console.log(`使用数字ID ${numericId} 找到了红绿灯`);interId=numericId;// 更新interId为找到的正确类型\n}}if(!trafficLight){console.error('未找到指定ID的红绿灯:',interId);return false;}const stateInfo=trafficLightStates.get(interId);const intersection=trafficLight.intersection;// 判断是否有相位数据\nconst hasPhaseData=stateInfo&&stateInfo.phases&&stateInfo.phases.length>0;let content;// 指北针样式\nconst compassStyle={position:'absolute',top:'5px',right:'25px',width:'30px',height:'30px',display:'flex',justifyContent:'center',alignItems:'center',borderRadius:'50%',background:'rgba(0,0,0,0.1)',zIndex:10};// 指北针组件\nconst CompassIcon=()=>/*#__PURE__*/_jsx(\"div\",{style:compassStyle,children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',flexDirection:'column',alignItems:'center',transform:'rotate(0deg)'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#ff5252',fontSize:'14px',fontWeight:'bold',lineHeight:'14px'},children:\"N\"}),/*#__PURE__*/_jsx(\"span\",{style:{width:0,height:0,borderLeft:'6px solid transparent',borderRight:'6px solid transparent',borderBottom:'10px solid #ff5252',marginTop:'-2px'}})]})});if(hasPhaseData){// 相位ID与方向/方式的映射\nconst phaseMap={'1':{dir:'N',type:'left'},'2':{dir:'N',type:'straight'},'3':{dir:'N',type:'right'},'5':{dir:'E',type:'left'},'6':{dir:'E',type:'straight'},'7':{dir:'E',type:'right'},'9':{dir:'S',type:'left'},'10':{dir:'S',type:'straight'},'11':{dir:'S',type:'right'},'13':{dir:'W',type:'left'},'14':{dir:'W',type:'straight'},'15':{dir:'W',type:'right'}};const typeOrder=['left','straight','right'];const colorMap={G:'#00ff00',Y:'#ffff00',R:'#ff0000'};const dirData={N:{},E:{},S:{},W:{}};stateInfo.phases.forEach(phase=>{const map=phaseMap[phase.phaseId];if(map){dirData[map.dir][map.type]={color:colorMap[phase.trafficLight]||'#888',remainTime:phase.remainTime};}});content=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'8px',width:'260px',background:'rgba(0,0,0,0.05)',position:'relative'},children:[/*#__PURE__*/_jsx(CompassIcon,{}),/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'bold',marginBottom:'8px',fontSize:'15px',textAlign:'center'},children:[intersection.name,\"\\u706F\\u6001\"]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'grid',gridTemplateRows:'60px 60px 60px',gridTemplateColumns:'60px 60px 60px',justifyContent:'center',alignItems:'center',background:'rgba(255,255,255,0.05)',borderRadius:'8px',margin:'0 auto',position:'relative'},children:[/*#__PURE__*/_jsx(\"div\",{style:{gridRow:1,gridColumn:2,textAlign:'center',display:'flex',justifyContent:'center',width:'100%'},children:typeOrder.map((type,index)=>{// 反转左转和右转在数组中的顺序\nlet displayIndex=index;if(index===0)displayIndex=2;else if(index===2)displayIndex=0;const currentType=typeOrder[displayIndex];// 计算与南边对齐的样式\nconst marginStyle={};if(currentType==='left'){// 左转箭头 (右侧显示)\nmarginStyle.marginRight='0px';}else if(currentType==='straight'){// 直行箭头 (中间显示)\nmarginStyle.marginLeft='10px';marginStyle.marginRight='10px';}else if(currentType==='right'){// 右转箭头 (左侧显示)\nmarginStyle.marginLeft='0px';}return dirData.N[currentType]&&/*#__PURE__*/// <div key={currentType} style={{\n//   display: 'flex', \n//   flexDirection: 'column', \n//   alignItems: 'center',\n//   ...marginStyle\n// }}>\n_jsxs(\"div\",{style:{marginRight:currentType==='left'?0:'10px',display:'flex',flexDirection:'column',alignItems:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:dirData.N[currentType].color,fontWeight:'bold',marginBottom:'3px'},children:dirData.N[currentType].remainTime}),/*#__PURE__*/_jsx(\"span\",{style:{color:dirData.N[currentType].color,fontSize:'20px',lineHeight:'20px'},children:currentType==='left'?'\\u{1F87A}':currentType==='straight'?'\\u{1F87B}':'\\u{1F878}'})]},currentType);})}),/*#__PURE__*/_jsx(\"div\",{style:{gridRow:3,gridColumn:2,textAlign:'center',display:'flex',justifyContent:'center',width:'100%'},children:typeOrder.map(type=>dirData.S[type]&&/*#__PURE__*/_jsxs(\"div\",{style:{marginRight:type==='right'?0:'10px',display:'flex',flexDirection:'column',alignItems:'center'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:dirData.S[type].color,fontSize:'20px',lineHeight:'20px'},children:type==='left'?'\\u{1F878}':type==='straight'?'\\u{1F879}':'\\u{1F87A}'}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:dirData.S[type].color,fontWeight:'bold',marginTop:'3px'},children:dirData.S[type].remainTime})]},type))}),/*#__PURE__*/_jsx(\"div\",{style:{gridRow:2,gridColumn:3,textAlign:'center'},children:typeOrder.map((type,index)=>{// 反转左转和右转在数组中的顺序\nlet displayIndex=index;if(index===0)displayIndex=2;else if(index===2)displayIndex=0;const currentType=typeOrder[displayIndex];return dirData.E[currentType]&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'8px',display:'flex',alignItems:'center',justifyContent:'flex-start'},children:[/*#__PURE__*/_jsx(\"span\",{style:{color:dirData.E[currentType].color,fontSize:'20px',lineHeight:'20px'},children:currentType==='left'?'\\u{1F87B}':currentType==='straight'?'\\u{1F878}':'\\u{1F879}'}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:dirData.E[currentType].color,fontWeight:'bold',marginLeft:'5px'},children:dirData.E[currentType].remainTime})]},currentType);})}),/*#__PURE__*/_jsx(\"div\",{style:{gridRow:2,gridColumn:1,textAlign:'center'},children:typeOrder.map(type=>dirData.W[type]&&/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'8px',display:'flex',alignItems:'center',justifyContent:'flex-end'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:dirData.W[type].color,fontWeight:'bold',marginRight:'5px'},children:dirData.W[type].remainTime}),/*#__PURE__*/_jsx(\"span\",{style:{color:dirData.W[type].color,fontSize:'20px',lineHeight:'20px'},children:type==='left'?'\\u{1F879}':type==='straight'?'\\u{1F87A}':'\\u{1F87B}'})]},type))})]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'8px',fontSize:'11px',color:'#888',textAlign:'center'},children:[\"\\u66F4\\u65B0\\u65F6\\u95F4: \",new Date().toLocaleTimeString(),\" (\\u81EA\\u52A8\\u5237\\u65B0)\"]})]});}else{// 没有相位数据时显示的内容\ncontent=/*#__PURE__*/_jsxs(\"div\",{style:{padding:'15px',width:'260px',background:'rgba(0,0,0,0.05)',position:'relative'},children:[/*#__PURE__*/_jsx(CompassIcon,{}),/*#__PURE__*/_jsx(\"div\",{style:{fontWeight:'bold',marginBottom:'10px',fontSize:'15px',textAlign:'center'},children:intersection.name}),/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'20px 0',color:'#ff9800',fontSize:'14px',fontWeight:'bold',background:'rgba(255,255,255,0.1)',borderRadius:'8px',marginBottom:'10px'},children:\"\\u5F53\\u524D\\u65E0\\u4FE1\\u53F7\\u706F\\u72B6\\u6001\\u4FE1\\u606F\"}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#888',textAlign:'center'},children:[\"\\u8DEF\\u53E3ID: \",interId]}),/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'8px',fontSize:'11px',color:'#888',textAlign:'center'},children:[\"\\u66F4\\u65B0\\u65F6\\u95F4: \",new Date().toLocaleTimeString(),\" (\\u81EA\\u52A8\\u5237\\u65B0)\"]})]});}// 设置弹窗位置在左上角区域\nconst x=445;const y=250;// 更新当前显示的红绿灯ID引用\nif(window.currentPopoverIdRef){window.currentPopoverIdRef.current=interId;}// 取消之前的更新定时器（如果存在）\nif(window.trafficLightUpdateTimerRef&&window.trafficLightUpdateTimerRef.current){clearInterval(window.trafficLightUpdateTimerRef.current);window.trafficLightUpdateTimerRef.current=null;}// 更新弹窗状态\nif(window._setTrafficLightPopover){window._setTrafficLightPopover({visible:true,interId:interId,position:{x,y},content:content,phases:(stateInfo===null||stateInfo===void 0?void 0:stateInfo.phases)||[]});// 设置定时更新\nif(window.trafficLightUpdateTimerRef){window.trafficLightUpdateTimerRef.current=setInterval(()=>{window.showTrafficLightPopup(interId);},1000);}return true;}else{console.error('无法找到setTrafficLightPopover函数');return false;}}catch(error){console.error('显示红绿灯弹窗失败:',error);return false;}};// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n//   console.log('开始生成模拟SPAT数据...');\n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject=object=>{let current=object;// 如果对象本身就有红绿灯数据，直接返回\nif(current&&current.userData&&current.userData.type==='trafficLight'){console.log('直接找到红绿灯对象:',current.name||'无名称');return current;}// 向上查找父对象，直到找到红绿灯或到达顶层\nwhile(current&&current.parent){current=current.parent;if(current.userData&&current.userData.type==='trafficLight'){console.log('从父对象找到红绿灯:',current.name||'无名称');return current;}}return null;};// 添加调试工具：强制进行点击测试\nwindow.testClickDetection=(x,y)=>{try{console.log('执行强制点击测试 @ 位置:',x,y);// 找到渲染器的DOM元素\nconst canvas=document.querySelector('canvas');if(!canvas){console.error('找不到THREE.js的canvas元素');return false;}// 确保scene和camera已定义\nif(!scene||!cameraRef.current){console.error('scene或camera未定义');return false;}// 如果没有传入坐标，使用屏幕中心点\nif(x===undefined||y===undefined){x=window.innerWidth/2;y=window.innerHeight/2;}// 计算归一化设备坐标 (-1 到 +1)\nconst rect=canvas.getBoundingClientRect();const mouseX=(x-rect.left)/canvas.clientWidth*2-1;const mouseY=-((y-rect.top)/canvas.clientHeight)*2+1;console.log('归一化坐标:',mouseX,mouseY);// 创建一个射线\nconst raycaster=new THREE.Raycaster();raycaster.params.Points.threshold=5;raycaster.params.Line.threshold=5;const mouseVector=new THREE.Vector2(mouseX,mouseY);raycaster.setFromCamera(mouseVector,cameraRef.current);// 收集所有红绿灯对象\nconst trafficLightObjects=[];trafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){trafficLightObjects.push(lightObj.model);console.log(`添加红绿灯 ${interId} 到检测列表`);}});// 直接对红绿灯对象进行碰撞检测\nconsole.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);const tlIntersects=raycaster.intersectObjects(trafficLightObjects,true);if(tlIntersects.length>0){console.log('成功点击到红绿灯对象!');tlIntersects.forEach((intersect,i)=>{console.log(`结果 ${i}:`,intersect.object.name||'无名称','距离:',intersect.distance,'position:',intersect.object.position.toArray(),'userData:',intersect.object.userData);// 尝试获取红绿灯ID\nconst obj=getTrafficLightFromObject(intersect.object);if(obj&&obj.userData&&obj.userData.type==='trafficLight'){console.log('找到红绿灯ID:',obj.userData.interId);}});return true;}// 对整个场景进行碰撞检测\nconsole.log('对整个场景进行碰撞检测...');const sceneIntersects=raycaster.intersectObjects(scene.children,true);console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);sceneIntersects.forEach((intersect,i)=>{const obj=intersect.object;console.log(`场景物体 ${i}:`,obj.name||'无名称','类型:',obj.type,'位置:',obj.position.toArray(),'距离:',intersect.distance,'userData:',obj.userData);});// 测试红绿灯的可见性\nconsole.log('检查红绿灯的可见性...');let visibleCount=0;trafficLightsMap.forEach((lightObj,interId)=>{if(lightObj.model){var _lightObj$intersectio;// 检查红绿灯模型是否可见\nlet isVisible=lightObj.model.visible;let frustumVisible=true;// 获取世界位置\nconst worldPos=new THREE.Vector3();lightObj.model.getWorldPosition(worldPos);// 计算到摄像机的距离\nconst distanceToCamera=worldPos.distanceTo(cameraRef.current.position);// 检查是否在视锥体内\nconst screenPos=worldPos.clone().project(cameraRef.current);if(Math.abs(screenPos.x)>1||Math.abs(screenPos.y)>1||screenPos.z<-1||screenPos.z>1){frustumVisible=false;}if(isVisible){visibleCount++;}console.log(`红绿灯 ${interId}:`,{名称:((_lightObj$intersectio=lightObj.intersection)===null||_lightObj$intersectio===void 0?void 0:_lightObj$intersectio.name)||'未知',可见性:isVisible,在视锥体内:frustumVisible,世界位置:worldPos.toArray(),屏幕位置:[screenPos.x,screenPos.y,screenPos.z],与摄像机距离:distanceToCamera});}});console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);// 如果未检测到任何交叉点\nreturn sceneIntersects.length>0;}catch(error){console.error('点击测试失败:',error);return false;}};// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual=(trafficLight,phaseInfo)=>{var _trafficLight$interse,_trafficLight$interse2,_trafficLight$interse3;if(!trafficLight||!trafficLight.model||!phaseInfo){return;}// 移除旧的灯光模型(如果存在)\nconst lightsToRemove=[];trafficLight.model.traverse(child=>{if(child.userData&&child.userData.isLight){lightsToRemove.push(child);}});lightsToRemove.forEach(light=>{trafficLight.model.remove(light);});// 根据状态获取颜色\nlet lightColor;switch(phaseInfo.trafficLight){case'G':lightColor=0x00FF00;// 绿色\nbreak;case'Y':lightColor=0xFFFF00;// 黄色\nbreak;case'R':default:lightColor=0xFF0000;// 红色\nbreak;}// 创建一个球体作为灯光\nconst lightGeometry=new THREE.SphereGeometry(3,16,16);const lightMaterial=new THREE.MeshBasicMaterial({color:lightColor,emissive:lightColor,emissiveIntensity:1});const lightMesh=new THREE.Mesh(lightGeometry,lightMaterial);lightMesh.position.set(0,12,0);// 放在交通灯顶部\nlightMesh.userData={isLight:true,type:'trafficLight',interId:(_trafficLight$interse=trafficLight.intersection)===null||_trafficLight$interse===void 0?void 0:_trafficLight$interse.interId,phaseId:phaseInfo.phaseId,direction:phaseInfo.direction,remainTime:phaseInfo.remainTime};// 添加光源使灯光更明显\nconst light=new THREE.PointLight(lightColor,1,50);light.position.set(0,12,0);light.userData={isLight:true};// 将灯光添加到交通灯模型\ntrafficLight.model.add(lightMesh);trafficLight.model.add(light);console.log(`更新路口 ${((_trafficLight$interse2=trafficLight.intersection)===null||_trafficLight$interse2===void 0?void 0:_trafficLight$interse2.name)||((_trafficLight$interse3=trafficLight.intersection)===null||_trafficLight$interse3===void 0?void 0:_trafficLight$interse3.interId)} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);};export default CampusModel;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "SkeletonUtils", "mqtt", "Select", "Popover", "intersectionsData", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "preloadedVehicleModel", "preloadedCyclistModel", "preloadedPeopleModel", "preloadedTrafficLightModel", "scene", "peopleBaseModel", "skeleton", "lastPosition", "lastRotation", "ALPHA", "vehicleLastPositions", "Map", "vehicleLastRotations", "MQTT_CONFIG", "broker", "window", "location", "hostname", "port", "bsm", "rsm", "rsi", "spat", "BASE_URL", "process", "env", "REACT_APP_API_URL", "console", "log", "vehicleModels", "deviceTimestamps", "mainVehicleBsmId", "trafficLightsMap", "trafficLightStates", "clock", "Clock", "fetchMainVehicleBsmId", "response", "fetch", "data", "json", "vehicles", "Array", "isArray", "mainVehicle", "find", "v", "isMainVehicle", "bsmId", "error", "lowPassFilter", "newValue", "lastValue", "alpha", "filterPosition", "newPos", "vehicleId", "clone", "filteredX", "x", "filteredY", "y", "filteredZ", "z", "set", "has", "lastPos", "get", "distance", "distanceTo", "MAX_DISTANCE_THRESHOLD", "toFixed", "filteredPos", "Vector3", "filterRotation", "newRotation", "diff", "Math", "PI", "filteredRotation", "lastRot", "MAX_ANGLE_THRESHOLD", "abs", "TRAFFIC_LIGHT_UPDATE_INTERVAL", "mixersToCleanup", "Set", "bonesMap", "resourceManager", "mixers", "bones", "actions", "models", "addMixer", "mixer", "model", "add", "traverse", "object", "isBone", "uuid", "addAction", "action", "removeMixer", "for<PERSON>ach", "stop", "delete", "stopAllAction", "root", "getRoot", "animations", "length", "uncacheRoot", "uncacheAction", "uncacheClip", "clips", "_actions", "map", "a", "_clip", "filter", "Boolean", "clip", "e", "cleanup", "clear", "bone", "parent", "remove", "matrix", "identity", "matrixWorld", "<PERSON><PERSON><PERSON>", "geometry", "dispose", "material", "createAnimationMixer", "AnimationMixer", "createAction", "clipAction", "CampusModel", "_ref", "className", "onCurrentRSUChange", "selectedRSUs", "containerRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "animationFrameRef", "mapRef", "lastCameraPosition", "lastCameraTarget", "cameraSmoothing", "prevAnimationTimeRef", "Date", "now", "peopleAnimationMixers", "peopleAnimations", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "heading", "viewMode", "setViewMode", "buttonContainerStyle", "position", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "cameraRef", "selectedIntersection", "setSelectedIntersection", "trafficLightPopover", "setTrafficLightPopover", "visible", "interId", "content", "phases", "currentPopoverIdRef", "trafficLightUpdateTimerRef", "_setTrafficLightPopover", "intersectionSelectStyle", "top", "width", "labelStyle", "lineHeight", "color", "fontWeight", "textShadow", "currentRSU", "setCurrentRSU", "setDeviceTimestamps", "lastMessage", "setLastMessage", "topic", "switchToFollowView", "current", "enabled", "switchToGlobalView", "currentPos", "currentUp", "up", "Tween", "to", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "copy", "start", "target", "currentTarget", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "updateMatrix", "updateMatrixWorld", "目标相机位置", "目标控制点", "动画已启动", "handleIntersectionChange", "value", "intersection", "intersections", "i", "name", "modelCoords", "wgs84ToModel", "parseFloat", "路口名称", "经纬度", "模型坐标", "相机位置", "toArray", "目标点", "hasTrafficLight", "setTimeout", "showTrafficLightPopup", "handleMqttMessage", "message", "payload", "JSON", "parse", "_payload$data", "deviceMac", "mac", "messageTimestamp", "tm", "lastTimestamp", "participants", "rsuid", "participant", "id", "partPtcId", "type", "partPtcType", "state", "partPosLong", "partPosLat", "partSpeed", "partHeading", "modelPos", "preloadedModel", "newModel", "height", "rotation", "scale", "play", "lastUpdate", "CLEANUP_THRESHOLD", "currentIds", "p", "modelData", "bsmData", "bsmid", "newState", "partLong", "partLat", "postMessage", "source", "initialPosition", "initialRotation", "newPosition", "vehicleObj", "newVehicleModel", "child", "newMaterial", "emissive", "Color", "transparent", "needsUpdate", "speedLabel", "createTextSprite", "round", "r", "g", "b", "textColor", "renderOrder", "opacity", "is<PERSON><PERSON>", "Out", "filteredPosition", "timeSinceLastUpdate", "undefined", "originalTran<PERSON>arent", "originalOpacity", "m", "phasesInfo", "phase", "phaseId", "toString", "direction", "trafficDirec", "getDirectionFromCode", "getPhaseDirection", "lightState", "trafficLight", "remainTime", "parseInt", "phaseInfo", "push", "trafficLightKey", "String", "trafficLightModel", "updateTrafficLightVisual", "prev", "<PERSON><PERSON><PERSON>", "strId", "numId", "updateTime", "rsiData", "rsuId", "events", "rtes", "event", "eventId", "rteId", "eventType", "description", "startTime", "endTime", "posLong", "posLat", "warningText", "warningColor", "showWarningMarker", "sceneData", "sceneId", "sceneType", "sceneDesc", "speedLimit", "eventData1", "priorityType", "duration", "eventData2", "getPriorityTypeText", "initMqttClient", "wsUrl", "ws", "WebSocket", "onopen", "onmessage", "stringify", "onerror", "onclose", "preloadModels", "Scene", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "setClearColor", "setPixelRatio", "devicePixelRatio", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "ambientLight", "AmbientLight", "directionalLight1", "DirectionalLight", "directionalLight2", "spotLight", "SpotLight", "angle", "penumbra", "decay", "enableDamping", "dampingFactor", "screenSpacePanning", "loadVehicleModel", "Promise", "resolve", "reject", "vehicle<PERSON>oader", "load", "gltf", "vehicleModel", "vehicleContainer", "Group", "MeshStandardMaterial", "metalness", "roughness", "envMapIntensity", "children", "setIsVehicleLoaded", "xhr", "loaded", "total", "initializeScene", "loadModelWithRetry", "url", "maxRetries", "arguments", "attemptLoad", "retriesLeft", "loader", "错误类型", "错误消息", "加载URL", "完整URL", "animate", "requestAnimationFrame", "deltaTime", "<PERSON><PERSON><PERSON><PERSON>", "size", "vehiclePos", "vehicleRotation", "adjustedRotation", "cameraOffset", "cos", "sin", "targetCameraPosition", "targetLookAt", "lerp", "updateProjectionMatrix", "车辆位置", "相机目标", "相机朝向", "getWorldDirection", "render", "handleResize", "aspect", "addEventListener", "setGlobalView", "cancelAnimationFrame", "removeAll", "objectsToRemove", "obj", "setAnimationLoop", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "forceContextLoss", "handleMainVehicleChange", "intervalId", "setInterval", "removeEventListener", "clearInterval", "timer", "createTrafficLights", "clearTimeout", "handleClick", "handleMouseClick", "initScene", "createSimpleTrafficLight", "BoxGeometry", "MeshBasicMaterial", "<PERSON><PERSON>", "baseGeometry", "CylinderGeometry", "baseMaterial", "baseModel", "addClickHelpers", "lightObj", "helperGeometry", "SphereGeometry", "helperMaterial", "depthWrite", "helper<PERSON><PERSON>", "userData", "isClickHelper", "firstTrafficLightIntersection", "targetIntersection", "style", "placeholder", "onChange", "options", "label", "bordered", "dropdownStyle", "maxHeight", "ref", "max<PERSON><PERSON><PERSON>", "right", "background", "onClick", "handleClosePopover", "text", "parameters", "params", "fontFace", "borderThickness", "borderColor", "canvas", "document", "createElement", "context", "getContext", "font", "textWidth", "measureText", "textBaseline", "radius", "beginPath", "moveTo", "lineTo", "arcTo", "closePath", "strokeStyle", "lineWidth", "stroke", "fillStyle", "fill", "textAlign", "fillText", "texture", "CanvasTexture", "minFilter", "LinearFilter", "spriteMaterial", "SpriteMaterial", "sprite", "Sprite", "depthTest", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "新位置", "trafficLightGltf", "vehicleGltf", "cyclistGltf", "peopleGltf", "all", "loadAsync", "materia", "<PERSON><PERSON><PERSON><PERSON>", "warn", "err", "types", "converterInstance", "then", "catch", "createFallbackTrafficLights", "Error", "side", "DoubleSide", "colliderGeometry", "colliderMaterial", "collider", "isCollider", "lightGeometry", "lightMaterial", "<PERSON><PERSON><PERSON>", "dirCode", "container", "sceneInstance", "cameraInstance", "clientX", "clientY", "rect", "getBoundingClientRect", "mouseX", "clientWidth", "mouseY", "clientHeight", "raycaster", "Raycaster", "Points", "threshold", "Line", "mouseVector", "Vector2", "setFromCamera", "trafficLightObjects", "trafficLightIntersects", "intersectObjects", "intersect", "index", "getTrafficLightFromObject", "correctId", "intersects", "closestLight", "worldPos", "getWorldPosition", "screenPos", "project", "dx", "dy", "sqrt", "setPopoverState", "testTrafficLightClick", "_document$querySelect", "_document$querySelect2", "light", "lightModel", "stateInfo", "overflowY", "marginBottom", "borderBottom", "paddingBottom", "lightColor", "justifyContent", "marginTop", "toLocaleTimeString", "centerX", "centerY", "__REACT_INSTANCE", "popover", "innerHTML", "body", "closeButton", "listTrafficLights", "list", "numericId", "hasPhaseData", "compassStyle", "alignItems", "CompassIcon", "flexDirection", "borderLeft", "borderRight", "phaseMap", "dir", "typeOrder", "colorMap", "G", "Y", "R", "dirD<PERSON>", "N", "E", "S", "W", "gridTemplateRows", "gridTemplateColumns", "margin", "gridRow", "gridColumn", "displayIndex", "currentType", "marginStyle", "marginRight", "marginLeft", "testClickDetection", "tlIntersects", "sceneIntersects", "visibleCount", "_lightObj$intersectio", "isVisible", "frustumVisible", "distanceToCamera", "名称", "可见性", "在视锥体内", "世界位置", "屏幕位置", "与摄像机距离", "_trafficLight$interse", "_trafficLight$interse2", "_trafficLight$interse3", "lightsToRemove", "isLight", "emissiveIntensity", "PointLight"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport * as SkeletonUtils from 'three/examples/jsm/utils/SkeletonUtils.js';\n\n\nimport mqtt from 'mqtt';\nimport { Select, Popover } from 'antd';\nimport intersectionsData from '../data/intersections.json';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\n\n// 添加全局变量来存储预加载的车辆模型\nlet preloadedVehicleModel = null;\nlet preloadedCyclistModel = null;  // 新增：非机动车模型\nlet preloadedPeopleModel = null;   // 新增：行人模型\nlet preloadedTrafficLightModel = null; // 新增：红绿灯模型\nlet scene = null; // 添加scene全局变量\n\nlet peopleBaseModel= null; // 存储原始模型数据\nlet skeleton =null;\n\n// 添加滤波相关的变量\nlet lastPosition = null;\nlet lastRotation = null;\nconst ALPHA = 0.08; // 进一步降低滤波系数，使平滑效果更强（从0.15降到0.08）\n\n// 为每个车辆单独存储上一次的位置和方向\nconst vehicleLastPositions = new Map(); // 使用Map存储每个车辆ID的上一次位置\nconst vehicleLastRotations = new Map(); // 使用Map存储每个车辆ID的上一次旋转角度\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  broker: window.location.hostname,\n  port: 8083,\n  // 修正: 将MQTT主题直接作为属性而非嵌套在topics对象中\n    bsm: 'changli/cloud/v2x/obu/bsm',\n    rsm: 'changli/cloud/v2x/rsu/rsm',\n    scene: 'changli/cloud/v2x/obu/scene',\n  rsi: 'changli/cloud/v2x/rsu/rsi',  // 添加 RSI 主题\n  spat: 'changli/cloud/v2x/rsu/spat'  // 添加 SPAT 主题\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API_URLxxxx:', process.env.REACT_APP_API_URL);\n\n// 添加全局变量来存储所有车辆\nconst vehicleModels = new Map(); // 使用Map存储车辆ID和对应的3D模型\n\n// 添加全局变量来存储设备时间戳缓存\nlet deviceTimestamps = new Map(); // 用于存储每个设备的最新时间戳\n\n// 添加全局变量来存储主车的bsmId\nlet mainVehicleBsmId = null;\n\n// 添加红绿灯相关的全局变量\nlet trafficLightsMap = new Map(); // 存储路口ID与红绿灯模型的映射\nlet trafficLightStates = new Map(); // 存储路口ID与红绿灯状态的映射\n\nconst clock = new THREE.Clock();\n\n// 添加获取主车bsmId的函数\nconst fetchMainVehicleBsmId = async () => {\n  try {\n    const response = await fetch(`${BASE_URL}/api/vehicles/list`);\n    const data = await response.json();\n    \n    if (data && data.vehicles && Array.isArray(data.vehicles)) {\n      const mainVehicle = data.vehicles.find(v => v.isMainVehicle === true);\n      if (mainVehicle && mainVehicle.bsmId) {\n        mainVehicleBsmId = mainVehicle.bsmId;\n        console.log('获取主车bsmId成功:', mainVehicleBsmId);\n        return mainVehicleBsmId;\n      }\n    }\n    console.log('未找到主车，使用默认值 BSM01');\n    mainVehicleBsmId = 'BSM01'; // 默认值\n    return mainVehicleBsmId;\n  } catch (error) {\n    console.error('获取主车信息失败:', error);\n    mainVehicleBsmId = 'BSM01'; // 出错时使用默认值\n    return mainVehicleBsmId;\n  }\n};\n\n// 添加滤波器函数\nconst lowPassFilter = (newValue, lastValue, alpha) => {\n  if (lastValue === null) return newValue;\n  return alpha * newValue + (1 - alpha) * lastValue;\n};\n\n// 修改位置滤波函数，针对特定车辆ID进行滤波\nconst filterPosition = (newPos, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (!lastPosition) {\n    lastPosition = newPos.clone();\n    return newPos;\n  }\n  \n  const filteredX = lowPassFilter(newPos.x, lastPosition.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPosition.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPosition.z, ALPHA);\n  \n  lastPosition.set(filteredX, filteredY, filteredZ);\n  return lastPosition.clone();\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastPositions.has(vehicleId)) {\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  const lastPos = vehicleLastPositions.get(vehicleId);\n  \n  // 计算与上一次位置的距离，如果太远（可能是跳变），直接使用新位置\n  const distance = lastPos.distanceTo(newPos);\n  const MAX_DISTANCE_THRESHOLD = 50; // 最大距离阈值，超过此距离认为是位置跳变\n  \n  if (distance > MAX_DISTANCE_THRESHOLD) {\n    console.log(`车辆${vehicleId}位置跳变过大(${distance.toFixed(2)}米)，不进行滤波`);\n    vehicleLastPositions.set(vehicleId, newPos.clone());\n    return newPos;\n  }\n  \n  // 正常滤波处理\n  const filteredX = lowPassFilter(newPos.x, lastPos.x, ALPHA);\n  const filteredY = lowPassFilter(newPos.y, lastPos.y, ALPHA);\n  const filteredZ = lowPassFilter(newPos.z, lastPos.z, ALPHA);\n  \n  const filteredPos = new THREE.Vector3(filteredX, filteredY, filteredZ);\n  vehicleLastPositions.set(vehicleId, filteredPos.clone());\n  \n  return filteredPos;\n};\n\n// 修改朝向滤波函数，针对特定车辆ID进行滤波\nconst filterRotation = (newRotation, vehicleId) => {\n  // 如果没有提供车辆ID，使用全局变量（用于主车）\n  if (!vehicleId) {\n  if (lastRotation === null) {\n    lastRotation = newRotation;\n    return newRotation;\n  }\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRotation;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  const filteredRotation = lowPassFilter(lastRotation + diff, lastRotation, ALPHA);\n  lastRotation = filteredRotation;\n    return filteredRotation;\n  }\n  \n  // 针对特定车辆ID的滤波\n  if (!vehicleLastRotations.has(vehicleId)) {\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const lastRot = vehicleLastRotations.get(vehicleId);\n  \n  // 处理角度跳变（从360度到0度或反之）\n  let diff = newRotation - lastRot;\n  if (diff > Math.PI) diff -= 2 * Math.PI;\n  if (diff < -Math.PI) diff += 2 * Math.PI;\n  \n  // 检查是否是大角度变化，如果是则不进行过滤\n  const MAX_ANGLE_THRESHOLD = Math.PI / 2; // 90度\n  if (Math.abs(diff) > MAX_ANGLE_THRESHOLD) {\n    console.log(`车辆${vehicleId}朝向变化过大(${(diff * 180 / Math.PI).toFixed(2)}度)，不进行滤波`);\n    vehicleLastRotations.set(vehicleId, newRotation);\n    return newRotation;\n  }\n  \n  const filteredRotation = lowPassFilter(lastRot + diff, lastRot, ALPHA);\n  vehicleLastRotations.set(vehicleId, filteredRotation);\n  \n  return filteredRotation;\n};\n\n// 添加一个常量定义在文件顶部\n// ... existing code ...\n// 修改红绿灯状态更新频率（秒）\nconst TRAFFIC_LIGHT_UPDATE_INTERVAL = 1; // 每秒更新一次\n// ... existing code ...\n\n// 在文件顶部添加\nconst mixersToCleanup = new Set();\nconst bonesMap = new Map();\n\n// 在文件顶部添加资源管理器\nconst resourceManager = {\n  mixers: new Set(),\n  bones: new Map(),\n  actions: new Map(),\n  models: new Set(),\n  \n  addMixer(mixer, model) {\n    this.mixers.add(mixer);\n    if (model) {\n      this.models.add(model);\n      // 记录骨骼\n      model.traverse(object => {\n        if (object.isBone) {\n          this.bones.set(object.uuid, object);\n        }\n      });\n    }\n    return mixer;\n  },\n  \n  addAction(action, mixer) {\n    if (!this.actions.has(mixer)) {\n      this.actions.set(mixer, new Set());\n    }\n    this.actions.get(mixer).add(action);\n    return action;\n  },\n  \n  removeMixer(mixer) {\n    if (this.mixers.has(mixer)) {\n      try {\n        // 停止并清理所有动作\n        if (this.actions.has(mixer)) {\n          this.actions.get(mixer).forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          this.actions.delete(mixer);\n        }\n        \n        // 停止混合器\n        if (typeof mixer.stopAllAction === 'function') {\n          mixer.stopAllAction();\n        }\n        \n        // 清理混合器的根对象\n        const root = mixer.getRoot();\n        if (root) {\n          this.models.delete(root);\n          root.traverse(object => {\n            if (object && object.isBone) {\n              this.bones.delete(object.uuid);\n            }\n            if (object && object.animations) {\n              object.animations.length = 0;\n            }\n          });\n          \n          // 安全地清理缓存\n          try {\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            \n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n            \n            // 这里是发生错误的地方，添加防御性检查\n            if (typeof mixer.uncacheClip === 'function') {\n              // 不直接调用uncacheClip(null)，而是获取混合器中的所有动画片段并逐个清理\n              const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n              clips.forEach(clip => {\n                if (clip && clip.uuid) {\n                  mixer.uncacheClip(clip);\n                }\n              });\n            }\n          } catch (e) {\n            console.log('在清理动画混合器时发生非致命错误:', e);\n            // 继续执行其余清理操作\n          }\n        }\n        \n        this.mixers.delete(mixer);\n      } catch (error) {\n        console.error('清理动画混合器时发生错误:', error);\n        // 确保即使出错也会从集合中移除\n        this.mixers.delete(mixer);\n      }\n    }\n  },\n  \n  cleanup() {\n    try {\n      // 清理动作\n      this.actions.forEach((actions, mixer) => {\n        try {\n          actions.forEach(action => {\n            if (action && typeof action.stop === 'function') {\n              action.stop();\n            }\n          });\n          actions.clear();\n        } catch (e) {\n          console.log('清理动作时发生非致命错误:', e);\n        }\n      });\n      this.actions.clear();\n      \n      // 清理混合器\n      this.mixers.forEach(mixer => {\n        try {\n          if (typeof mixer.stopAllAction === 'function') {\n            mixer.stopAllAction();\n          }\n          \n          const root = mixer.getRoot();\n          if (root) {\n            root.traverse(object => {\n              if (object && object.animations) {\n                object.animations.length = 0;\n              }\n            });\n            \n            // 安全清理\n            if (typeof mixer.uncacheRoot === 'function') {\n              mixer.uncacheRoot(root);\n            }\n            \n            if (typeof mixer.uncacheAction === 'function') {\n              mixer.uncacheAction(null, root);\n            }\n            \n            // 安全清理动画片段\n            try {\n              if (mixer._actions && Array.isArray(mixer._actions)) {\n                const clips = mixer._actions.map(a => a && a._clip).filter(Boolean);\n                clips.forEach(clip => {\n                  if (clip && clip.uuid && typeof mixer.uncacheClip === 'function') {\n                    mixer.uncacheClip(clip);\n                  }\n                });\n              }\n            } catch (e) {\n              console.log('清理动画片段时发生非致命错误:', e);\n            }\n          }\n        } catch (e) {\n          console.log('清理混合器时发生非致命错误:', e);\n        }\n      });\n      this.mixers.clear();\n      \n      // 清理骨骼\n      this.bones.forEach(bone => {\n        if (bone.parent) {\n          bone.parent.remove(bone);\n        }\n        if (bone.matrix) bone.matrix.identity();\n        if (bone.matrixWorld) bone.matrixWorld.identity();\n      });\n      this.bones.clear();\n      \n      // 清理模型\n      this.models.forEach(model => {\n        if (model.parent) {\n          model.parent.remove(model);\n        }\n        model.traverse(object => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n          }\n          if (object.animations) {\n            object.animations.length = 0;\n          }\n        });\n      });\n      this.models.clear();\n    } catch (error) {\n      console.error('全局清理过程中发生错误:', error);\n      // 即使发生错误也尝试清空集合\n      this.actions.clear();\n      this.mixers.clear();\n      this.bones.clear();\n      this.models.clear();\n    }\n  }\n};\n\n// 修改创建动画混合器的函数\nconst createAnimationMixer = (model) => {\n  const mixer = new THREE.AnimationMixer(model);\n  return resourceManager.addMixer(mixer, model);\n};\n\n// 修改动画动作创建的函数\nconst createAction = (clip, mixer, model) => {\n  const action = mixer.clipAction(clip, model);\n  return resourceManager.addAction(action, mixer);\n};\n\nconst CampusModel = ({ className, onCurrentRSUChange, selectedRSUs }) => {\n  const containerRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const animationFrameRef = useRef(null);\n  const mapRef = useRef(null);\n  \n  // 添加相机平滑过渡的变量\n  const lastCameraPosition = useRef(null);\n  const lastCameraTarget = useRef(null);\n  const cameraSmoothing = 0.98; // 平滑系数，值越大越平滑 (0-1之间)\n  \n  // 添加行人动画相关的引用\n  const prevAnimationTimeRef = useRef(Date.now());\n  const peopleAnimationMixers = new Map(); // 使用全局Map存储所有行人的动画混合器（不再使用useRef）\n  const peopleAnimations = useRef([]); // 存储行人动画数据\n\n  \n  \n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 1000,  // 改为1000，避免遮挡点击\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 添加相机引用\n  const cameraRef = useRef(null);\n  \n  // 添加路口选择相关代码\n  const [selectedIntersection, setSelectedIntersection] = useState(null);\n  \n  // 添加红绿灯状态弹出窗口相关状态\n  const [trafficLightPopover, setTrafficLightPopover] = useState({\n    visible: false,\n    interId: null,\n    position: { x: 0, y: 0 },\n    content: null,\n    phases: []\n  });\n  \n  // 添加一个状态用于存储当前弹窗的路口ID，防止被垃圾回收\n  const currentPopoverIdRef = useRef(null);\n  \n  // 添加红绿灯状态自动更新定时器引用\n  const trafficLightUpdateTimerRef = useRef(null);\n  \n  // 全局存储setTrafficLightPopover函数引用\n  window._setTrafficLightPopover = setTrafficLightPopover;\n  \n  // 将Ref暴露给全局以便弹窗函数使用\n  window.currentPopoverIdRef = currentPopoverIdRef;\n  window.trafficLightUpdateTimerRef = trafficLightUpdateTimerRef;\n\n  // 修改路口选择器的样式\n  const intersectionSelectStyle = {\n    position: 'fixed',\n    top: '65px',  // 从 60px 改为 65px\n    left: '50%',\n    transform: 'translateX(-50%)',\n    width: '200px',  // 从 300px 改为 200px\n    zIndex: 1001,\n    backgroundColor: 'white',\n    borderRadius: '4px',\n    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n  };\n\n  // 添加文字标签样式\n  const labelStyle = {\n    position: 'fixed',\n    top: '65px',\n    left: 'calc(50% - 90px)',  // 从 140px 改为 110px，让文字更靠近选择框\n    transform: 'translateX(-100%)',\n    padding: '0 5px',  // 从 10px 改为 5px，减少内边距\n    lineHeight: '32px',\n    color: '#fff',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    textShadow: '0 1px 2px rgba(0,0,0,0.3)',\n    zIndex: 1001,\n  };\n\n  // 添加交通灯映射状态\n  const [trafficLightsMap] = useState(new Map());\n\n  // 添加当前RSU状态\n  const [currentRSU, setCurrentRSU] = useState(null);\n\n  // 添加设备时间戳状态\n  const [deviceTimestamps, setDeviceTimestamps] = useState(new Map());\n\n  // 添加最后一条消息状态\n  const [lastMessage, setLastMessage] = useState({ topic: '', content: '' });\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    if (cameraMode !== 'follow') {\n      console.log('切换到跟随视角');\n      cameraMode = 'follow';\n      \n      // 重置相机平滑变量，确保切换视角时不会有突兀的过渡\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (controls) {\n        controls.enabled = false;\n      }\n    }\n  };\n\n  const switchToGlobalView = () => {\n    if (cameraMode !== 'global') {\n      console.log('切换到全局视角');\n      cameraMode = 'global';\n      \n      // 重置相机平滑变量\n      lastCameraPosition.current = null;\n      lastCameraTarget.current = null;\n      \n      if (cameraRef.current && controls) {\n        // 获取当前相机位置和朝向\n        // const currentPos = cameraRef.current.position.clone();\n        cameraRef.current.position.set(0, 500, 0);\n        const currentPos = cameraRef.current.position.clone();\n        const currentUp = cameraRef.current.up.clone();\n        \n        // 创建相机位置的补间动画\n        new TWEEN.Tween(currentPos)\n          .to({ x: 0, y: 300, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.position.copy(currentPos);\n          })\n          .start();\n\n        // 创建相机上方向的补间动画\n        new TWEEN.Tween(currentUp)\n          .to({ x: 0, y: 1, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            cameraRef.current.up.copy(currentUp);\n          })\n          .start();\n\n        // 获取当前控制器目标点\n        controls.target.set(0, 0, 0);\n        const currentTarget = controls.target.clone();\n        \n        // 创建目标点的补间动画\n        new TWEEN.Tween(currentTarget)\n          .to({ x: 0, y: 0, z: 0 }, 1000)\n          .easing(TWEEN.Easing.Quadratic.InOut)\n          .onUpdate(() => {\n            controls.target.copy(currentTarget);\n            // 确保相机始终朝向目标点\n            cameraRef.current.lookAt(controls.target);\n            controls.update();\n          })\n          .start();\n\n        // 启用控制器\n        controls.enabled = true;\n        \n        // 重置控制器的一些属性\n        controls.minDistance = 50;\n        controls.maxDistance = 500;\n        controls.maxPolarAngle = Math.PI / 2.1;\n        controls.minPolarAngle = 0;\n        controls.update();\n        // 强制更新相机矩阵\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        console.log('切换到全局视角', {\n          目标相机位置: [0, 300, 0],\n          目标控制点: [0, 0, 0],\n          动画已启动: true\n        });\n      }\n    }\n  };\n\n  // 修改处理路口选择的函数\n  const handleIntersectionChange = (value) => {\n    const intersection = intersectionsData.intersections.find(i => i.name === value);\n    if (intersection && cameraRef.current && controls) {\n      setSelectedIntersection(intersection);\n      \n      // 使用 wgs84ToModel 方法转换经纬度到模型坐标\n      const modelCoords = converter.current.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n\n      console.log('路口坐标转换结果:', {\n        路口名称: intersection.name,\n        经纬度: {\n          longitude: intersection.longitude,\n          latitude: intersection.latitude\n        },\n        模型坐标: modelCoords\n      });\n\n      // 设置为路口视角模式\n      cameraMode = 'intersection';\n      setViewMode('intersection');\n\n      // 直接设置相机位置\n      cameraRef.current.position.set(modelCoords.x+50, 70, -modelCoords.y+50);\n      \n      // 直接设置控制器目标点\n      controls.target.set(modelCoords.x, 0, -modelCoords.y);\n      \n      // 确保相机朝向目标点\n      cameraRef.current.lookAt(controls.target);\n      \n      // 更新控制器\n      controls.enabled = true;\n      controls.update();\n      \n      // 强制更新相机矩阵\n      cameraRef.current.updateMatrix();\n      cameraRef.current.updateMatrixWorld(true);\n      \n      console.log('相机已直接移动到路口:', {\n        路口名称: intersection.name,\n        相机位置: cameraRef.current.position.toArray(),\n        目标点: controls.target.toArray(),\n        模型坐标: modelCoords\n      });\n      \n      // 如果该路口有红绿灯，自动显示红绿灯弹窗\n      if (intersection.hasTrafficLight !== false && intersection.interId) {\n        console.log(`路口 ${intersection.name} 有红绿灯，准备显示红绿灯状态`);\n        \n        // 延迟300ms调用以确保场景已更新\n        setTimeout(() => {\n          // 检查多种可能的ID格式\n          let interId = intersection.interId;\n          \n          // 使用全局的showTrafficLightPopup函数显示红绿灯弹窗\n          if (window.showTrafficLightPopup) {\n            window.showTrafficLightPopup(interId);\n            console.log(`已触发路口 ${intersection.name} (ID: ${interId}) 的红绿灯弹窗显示`);\n          } else {\n            console.error('找不到显示红绿灯弹窗的函数');\n          }\n        }, 300);\n      } else {\n        console.log(`路口 ${intersection.name} 没有红绿灯或缺少ID，不显示红绿灯状态`);\n        \n        // 如果有弹窗正在显示，则关闭它\n        if (window._setTrafficLightPopover) {\n          window._setTrafficLightPopover({\n            visible: false\n          });\n        }\n      }\n    }\n  };\n\n  // 修改处理MQTT消息的函数\n  const handleMqttMessage = (topic, message) => {\n    try {\n      const payload = JSON.parse(message);\n      \n      // 处理RSM消息\n      if (topic === MQTT_CONFIG.rsm) {\n        // console.log('收到RSM消息:', payload);\n        \n        // 检查设备时间戳\n        const deviceMac = payload.mac;\n        const messageTimestamp = payload.tm;\n        \n        // 获取该设备的最新时间戳\n        const lastTimestamp = deviceTimestamps.get(deviceMac);\n        \n        // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n        if (lastTimestamp && messageTimestamp < lastTimestamp) {\n          // console.log('忽略过期的RSM消息:', {\n          //   设备MAC: deviceMac,\n          //   消息时间戳: messageTimestamp,\n          //   最新时间戳: lastTimestamp\n          // });\n          return;\n        }\n        \n        // 更新设备的最新时间戳\n        deviceTimestamps.set(deviceMac, messageTimestamp);\n        \n        // console.log('RSM消息时间戳更新:', {\n        //   设备MAC: deviceMac,\n        //   时间戳: messageTimestamp,\n        //   是否为最新: !lastTimestamp || messageTimestamp >= lastTimestamp\n        // });\n        \n        const participants = payload.data?.participants || [];\n        const rsuid = payload.data.rsuid;\n        \n        // 分类处理不同类型的参与者\n        const now = Date.now();\n        \n        // 处理所有参与者\n        participants.forEach(participant => {\n          // const id = participant.partPtcId;\n          // const id =  rsuid + participant.partPtcId;\n          const id =  deviceMac + participant.partPtcId;\n          const type = participant.partPtcType;\n          \n          if(type === '3'||type === '2'||type === '1'){\n          // if(type === '3'){\n          // if(type === '3'||type === '1'){\n            // 解析位置和状态信息\n            const state = {\n              longitude: parseFloat(participant.partPosLong),\n              latitude: parseFloat(participant.partPosLat),\n              speed: parseFloat(participant.partSpeed),\n              heading: parseFloat(participant.partHeading)\n            };\n            \n            const modelPos = converter.current.wgs84ToModel(state.longitude, state.latitude);\n            \n            // 根据类型选择对应的预加载模型\n            let preloadedModel;\n            switch (type) {\n              case '1': // 机动车\n                preloadedModel = preloadedVehicleModel;\n                break;\n              case '2': // 非机动车\n                preloadedModel = preloadedCyclistModel;\n                break;\n              case '3': // 行人\n                preloadedModel = preloadedPeopleModel;\n                break;\n              default:\n                return; // 跳过未知类型\n            }\n            \n            // 获取或创建模型\n          let model = vehicleModels.get(id);\n          \n          if (!model && preloadedModel) {\n              // 创建新模型实例\n              const newModel = type === '3' ? SkeletonUtils.clone(preloadedPeopleModel) : preloadedModel.clone();\n              // 根据类型调整高度和缩放\n              const height = type === '3' ? 2.0 : 1.0;\n              newModel.position.set(modelPos.x, height, -modelPos.y);\n              newModel.rotation.y = Math.PI - state.heading * Math.PI / 180;\n              \n              // 如果是行人类型，设置缩放和创建动画\n              if (type === '3') {\n              // newModel.scale.set(0.005, 0.005, 0.005);\n                newModel.scale.set(4, 4, 4);\n                \n                // 使用resourceManager创建并管理动画混合器\n                const mixer = createAnimationMixer(newModel);\n                \n                if (peopleBaseModel && peopleBaseModel.animations && peopleBaseModel.animations.length > 0) {\n                  // 只创建一个动作并添加到资源管理器\n                  const action = createAction(peopleBaseModel.animations[0], mixer, newModel);\n                  action.play();\n                }\n                \n                // 保存到全局Map中(不再使用useRef)\n                peopleAnimationMixers.set(id, mixer);\n              }\n              \n              scene.add(newModel);\n              \n              vehicleModels.set(id, {\n                model: newModel,\n                lastUpdate: now,\n              type: type\n              });\n          } else if (model) {\n              // 更新现有模型\n            model.model.position.set(modelPos.x, model.type === '3' ? 2.0 : 1.0, -modelPos.y);\n            model.model.rotation.y = Math.PI - state.heading * Math.PI / 180;\n            model.lastUpdate = now;\n            model.model.updateMatrix();\n            model.model.updateMatrixWorld(true);\n            }\n          }\n        });\n        \n        // 清理长时间未更新的模型\n        const CLEANUP_THRESHOLD = 1000;\n        const currentIds = new Set(participants.map(p => deviceMac + p.partPtcId));\n        \n        vehicleModels.forEach((modelData, id) => {\n          if (now - modelData.lastUpdate > CLEANUP_THRESHOLD && !currentIds.has(id)) {\n            // 如果是行人，清理动画混合器\n            if (modelData.type === '3' && peopleAnimationMixers.has(id)) {\n              const mixer = peopleAnimationMixers.get(id);\n              // 使用resourceManager清理混合器\n              resourceManager.removeMixer(mixer);\n              peopleAnimationMixers.delete(id);\n            }\n            \n            // 从场景移除模型\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n          }\n        });\n        return;\n      }\n      \n      // 处理BSM消息\n      if (topic === MQTT_CONFIG.bsm) {\n        // console.log('收到BSM消息:', payload);\n        \n        const bsmData = payload.data;\n        const bsmid = bsmData.bsmId;\n        const newState = {\n          longitude: parseFloat(bsmData.partLong),\n          latitude: parseFloat(bsmData.partLat),\n          speed: parseFloat(bsmData.partSpeed),\n          heading: parseFloat(bsmData.partHeading)\n        };\n        \n        // console.log('解析后的车辆状态:', newState);\n        // console.log('车辆ID:', bsmid);\n        \n        // 通知RealTimeTraffic组件已收到真实BSM消息\n        window.postMessage({\n          type: 'realBsmReceived',\n          source: 'CampusModel'\n        }, '*');\n        \n        // 发送BSM消息到RealTimeTraffic组件，确保包含正确的数据结构\n        window.postMessage({\n          type: 'bsm',\n          bsmId: bsmid, // 直接传递车辆ID\n          data: {       // 同时提供完整的BSM数据\n            bsmId: bsmid,\n            partSpeed: bsmData.partSpeed,\n            partLat: bsmData.partLat,\n            partLong: bsmData.partLong,\n            partHeading: bsmData.partHeading\n          }\n        }, '*');\n        \n        // 获取模型位置坐标\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const initialPosition = new THREE.Vector3(modelPos.x, 1.0, -modelPos.y);\n        const initialRotation = Math.PI - newState.heading * Math.PI / 180;\n        \n        // 应用平滑滤波 - 使用已有的滤波函数\n        const newPosition = filterPosition(initialPosition, bsmid);\n        const newRotation = filterRotation(initialRotation, bsmid);\n        \n        // 检查该车辆是否已存在于场景中\n        let vehicleObj = vehicleModels.get(bsmid);\n        \n        // 检查是否是主车\n        const isMainVehicle = bsmid === mainVehicleBsmId;\n        \n        if (!vehicleObj && preloadedVehicleModel) {\n          // 创建一个新的车辆模型实例\n          const newVehicleModel = preloadedVehicleModel.clone();\n          \n          // 初始化时先将车辆放在地下，然后通过动画渐渐显示出来\n          // 这样可以避免车辆突然出现的视觉冲击\n          newVehicleModel.position.set(newPosition.x, -5, newPosition.z);\n          newVehicleModel.rotation.y = newRotation;\n          \n          // 设置BSM车辆为突出的颜色\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material) {\n              // // 保存原始材质颜色\n              // if (!child.userData.originalColor && child.material.color) {\n              //   child.userData.originalColor = child.material.color.clone();\n              // }\n              // // 设置为更鲜艳的颜色\n              // if (isMainVehicle) {\n              //   child.material.color.set(0x00BFFF); // 主车设为亮蓝色\n              // } else {\n              //   child.material.color.set(0xFF6347); // 其他BSM车辆设为亮红色\n              // }\n              // // 增加材质亮度\n              // child.material.emissive = new THREE.Color(0x222222);\n              \n              // // 初始设置为半透明\n              // child.material.transparent = true;\n              // child.material.opacity = 0.6;\n              \n              // child.material.needsUpdate = true;\n\n              const newMaterial = child.material.clone();\n              child.material = newMaterial;\n          \n              // 修改颜色逻辑（与原模型解耦）\n              if (isMainVehicle) {\n                newMaterial.color.set(0x00BFFF);\n              } else {\n                newMaterial.color.set(0xFF6347);\n              }\n              newMaterial.emissive = new THREE.Color(0x222222);\n              newMaterial.transparent = true;\n              // newMaterial.opacity = 0.6;\n              newMaterial.needsUpdate = true;\n            }\n          });\n          \n          // 创建速度显示标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 7, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          speedLabel.material.opacity = 0.6; // 初始半透明\n          newVehicleModel.add(speedLabel);\n          \n          scene.add(newVehicleModel);\n          \n          // 保存车辆引用到车辆模型集合中\n          vehicleModels.set(bsmid, {\n            model: newVehicleModel,\n            lastUpdate: Date.now(),\n            type: '1', // 设置为机动车类型\n            isMain: isMainVehicle,\n            speedLabel: speedLabel // 保存速度标签引用\n          });\n          \n          // console.log(`创建新车辆: ID ${bsmid}, 位置 (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 使用补间动画使车辆从地下逐渐显示出来\n          new TWEEN.Tween(newVehicleModel.position)\n            .to({ y: 0.5 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .start();\n          \n          // 使用补间动画使车辆从半透明变为完全不透明\n          newVehicleModel.traverse((child) => {\n            if (child.isMesh && child.material && child.material.transparent) {\n              new TWEEN.Tween({ opacity: 0.6 })\n                .to({ opacity: 1.0 }, 500)\n                .easing(TWEEN.Easing.Quadratic.Out)\n                .onUpdate(function() {\n                  child.material.opacity = this.opacity;\n                  child.material.needsUpdate = true;\n                })\n                .start();\n            }\n          });\n          \n          // 为速度标签也添加透明度动画\n          new TWEEN.Tween({ opacity: 0.6 })\n            .to({ opacity: 1.0 }, 500)\n            .easing(TWEEN.Easing.Quadratic.Out)\n            .onUpdate(function() {\n              speedLabel.material.opacity = this.opacity;\n              speedLabel.material.needsUpdate = true;\n            })\n            .start();\n          \n          // 如果是主车，设置全局引用\n          if (isMainVehicle) {\n            globalVehicleRef = newVehicleModel;\n            setVehicleState(newState);\n            console.log('设置全局主车引用:', bsmid);\n          }\n        } else if (vehicleObj) {\n          // 应用滤波\n          const filteredPosition = filterPosition(newPosition, bsmid);\n          const filteredRotation = filterRotation(newRotation, bsmid);\n          \n          // 更新现有车辆位置和朝向\n          vehicleObj.model.position.copy(filteredPosition);\n          vehicleObj.model.rotation.y = filteredRotation;\n          vehicleObj.model.updateMatrix();\n          vehicleObj.model.updateMatrixWorld(true);\n          vehicleObj.lastUpdate = Date.now();\n          vehicleObj.isMain = isMainVehicle; // 更新主车状态\n          \n          // 更新速度标签文本\n          if (vehicleObj.speedLabel) {\n            vehicleObj.speedLabel.material.map.dispose();\n            vehicleObj.model.remove(vehicleObj.speedLabel);\n          }\n          \n          // 创建新的速度标签\n          const speedLabel = createTextSprite(`${Math.round(newState.speed * 3.6)} km/h`, {\n            backgroundColor: isMainVehicle ? \n              { r: 0, g: 191, b: 255, a: 0.8 } : // 主车使用蓝色背景\n              { r: 255, g: 99, b: 71, a: 0.8 },  // 其他车辆使用红色背景\n            textColor: { r: 255, g: 255, b: 255, a: 1.0 }, // 白色文本\n            fontSize: 20,\n            padding: 8\n          });\n          speedLabel.position.set(0, 15, 0); // 位于车辆顶部上方\n          speedLabel.renderOrder = 1000; // 确保在最上层渲染\n          vehicleObj.model.add(speedLabel);\n          vehicleObj.speedLabel = speedLabel;\n          \n          // console.log(`更新车辆位置: ID ${bsmid}, 位置 (${filteredPosition.x.toFixed(2)}, ${filteredPosition.y.toFixed(2)}, ${filteredPosition.z.toFixed(2)}), 是否主车: ${isMainVehicle}`);\n          \n          // 如果是主车，同时更新全局引用和状态（用于相机跟随）\n          if (isMainVehicle) {\n            globalVehicleRef = vehicleObj.model;\n            setVehicleState(newState);\n          }\n        }\n        \n        // 清理长时间未更新的车辆\n        const now = Date.now();\n        const CLEANUP_THRESHOLD = 1000; // 增加到10秒，避免频繁清理造成闪烁\n        \n        vehicleModels.forEach((modelData, id) => {\n          const timeSinceLastUpdate = now - modelData.lastUpdate;\n          \n          // 对于即将超时的车辆，先降低透明度，而不是直接移除\n          if (timeSinceLastUpdate > CLEANUP_THRESHOLD * 0.7 && timeSinceLastUpdate <= CLEANUP_THRESHOLD) {\n            // const opacity = 1 - ((timeSinceLastUpdate - CLEANUP_THRESHOLD * 0.7) / (CLEANUP_THRESHOLD * 0.3));\n            const opacity = 1 ;\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh && child.material) {\n                // 如果材质还没有透明度设置，先保存初始状态\n                if (child.material.transparent === undefined) {\n                  child.material.originalTransparent = child.material.transparent || false;\n                  child.material.originalOpacity = child.material.opacity || 1.0;\n                }\n                \n                // 设置透明度\n                child.material.transparent = true;\n                child.material.opacity = opacity;\n                child.material.needsUpdate = true;\n              }\n            });\n            \n            // 如果有速度标签，也调整其透明度\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.opacity = opacity;\n              modelData.speedLabel.material.needsUpdate = true;\n            }\n          }\n          // 完全超时，移除车辆\n          else if (timeSinceLastUpdate > CLEANUP_THRESHOLD) {\n            // 清理资源\n            if (modelData.speedLabel) {\n              modelData.speedLabel.material.map.dispose();\n              modelData.speedLabel.material.dispose();\n              modelData.model.remove(modelData.speedLabel);\n            }\n            \n            modelData.model.traverse((child) => {\n              if (child.isMesh) {\n                if (child.material) {\n                  if (Array.isArray(child.material)) {\n                    child.material.forEach(m => m.dispose());\n                  } else {\n                    child.material.dispose();\n                  }\n                }\n                if (child.geometry) child.geometry.dispose();\n              }\n            });\n            \n            // 从场景中移除\n            scene.remove(modelData.model);\n            vehicleModels.delete(id);\n            // 同时清除该车辆的滤波缓存\n            vehicleLastPositions.delete(id);\n            vehicleLastRotations.delete(id);\n            \n            console.log(`移除长时间未更新的车辆: ID ${id}`);\n          }\n        });\n        \n        return;\n      }\n      \n      // SPAT消息处理\n      if (topic === MQTT_CONFIG.spat) {\n        // console.log('收到SPAT消息:', message);\n        \n        try {\n          const payload = JSON.parse(message);\n          \n          // 检查设备时间戳\n          const deviceMac = payload.mac;\n          const messageTimestamp = payload.tm;\n          \n          // 获取该设备的最新时间戳\n          const lastTimestamp = deviceTimestamps.get(deviceMac);\n          \n          // 如果存在最新时间戳，且当前消息时间戳小于最新时间戳，则忽略该消息\n          if (lastTimestamp && messageTimestamp < lastTimestamp) {\n            // console.log('忽略过期的SPAT消息:', {\n            //   设备MAC: deviceMac,\n            //   消息时间戳: messageTimestamp,\n            //   最新时间戳: lastTimestamp\n            // });\n            return;\n          }\n          \n          // 更新设备时间戳\n          deviceTimestamps.set(deviceMac, messageTimestamp);\n          \n          // 修改：访问data.intersections而不是直接访问intersections\n          if (payload.data && payload.data.intersections && Array.isArray(payload.data.intersections)) {\n            payload.data.intersections.forEach(intersection => {\n              const interId = intersection.interId;\n              \n              if (!interId) {\n                console.error('SPAT消息缺少interId:', intersection);\n                return;\n              }\n              \n              // console.log(`处理路口ID: ${interId} 的SPAT消息`);\n              \n              // 创建所有相位的数组 - 存储到trafficLightStates\n              if (intersection.phases && Array.isArray(intersection.phases)) {\n                // 构建存储相位信息的数组\n                const phasesInfo = [];\n                \n                intersection.phases.forEach(phase => {\n                  // 修改：使用phaseId而不是id\n                  if (!phase.phaseId) {\n                    console.error('相位信息缺少phaseId:', phase);\n                    return;\n                  }\n                  \n                  const phaseId = phase.phaseId.toString();\n                  // 从phase中获取方向信息，使用trafficDirec和getDirectionFromCode函数\n                  const direction = phase.trafficDirec ? \n                    getDirectionFromCode(phase.trafficDirec) : \n                    getPhaseDirection(phaseId);\n                  \n                  // 修改：直接从phase中获取信号灯状态和剩余时间\n                  const lightState = phase.trafficLight || 'R'; // 默认为红灯\n                  const remainTime = parseInt(phase.remainTime) || 0;\n                  \n                  // console.log(`路口ID: ${interId}, 相位ID: ${phaseId}, 方向: ${direction}, 信号灯状态: ${lightState}, 剩余时间: ${remainTime}秒`);\n                  \n                  // 构建相位信息对象\n                  const phaseInfo = {\n                    phaseId,\n                    direction,\n                    trafficLight: lightState,\n                    remainTime\n                  };\n                  \n                  // 添加到相位信息数组\n                  phasesInfo.push(phaseInfo);\n                  \n                  // 查找红绿灯模型并更新视觉效果\n                  // 尝试使用字符串ID和数字ID查找\n                  let trafficLightKey = String(interId);\n                  let trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  \n                  if (!trafficLightModel) {\n                    // 尝试使用数字ID\n                    trafficLightKey = parseInt(interId);\n                    trafficLightModel = trafficLightsMap.get(trafficLightKey);\n                  }\n                  \n                  if (trafficLightModel) {\n                    // 更新交通灯视觉效果\n                    updateTrafficLightVisual(trafficLightModel, phaseInfo);\n                    \n                    // 更新弹出窗信息\n                    if (selectedIntersection && selectedIntersection.interId === interId) {\n                      setTrafficLightPopover(prev => ({\n                        ...prev,\n                        visible: true,\n                        phaseId,\n                        direction,\n                        state: lightState,\n                        remainTime\n                      }));\n                    }\n                  } else {\n                    // console.warn(`未找到路口ID ${interId} 相位ID ${phaseId} 的交通灯模型`);\n                  }\n                });\n                \n                // 在存储状态信息前，先尝试查找该路口的模型来确认ID类型\n                let modelKey = null;\n                // 尝试字符串ID\n                const strId = String(interId);\n                if (trafficLightsMap.has(strId)) {\n                  modelKey = strId;\n                } else {\n                  // 尝试数字ID\n                  const numId = parseInt(interId);\n                  if (trafficLightsMap.has(numId)) {\n                    modelKey = numId;\n                  }\n                }\n                \n                if (modelKey !== null) {\n                  // 使用正确的ID类型存储状态信息\n                  trafficLightStates.set(modelKey, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  console.log(`使用匹配的ID类型 ${modelKey} (${typeof modelKey}) 保存了路口状态信息`);\n                  \n                  // 如果当前弹窗正在显示这个路口的信息，主动更新弹窗\n                  if (window.currentPopoverIdRef && \n                     (window.currentPopoverIdRef.current === modelKey || \n                      window.currentPopoverIdRef.current === String(modelKey) || \n                      window.currentPopoverIdRef.current === parseInt(modelKey))) {\n                    \n                    console.log(`检测到正在显示路口ID ${modelKey} 的弹窗，主动触发更新`);\n                    // 强制更新弹窗ID为当前数据的正确ID类型\n                    window.currentPopoverIdRef.current = modelKey;\n                    \n                    // 如果没有更新定时器则创建一个\n                    if (window.trafficLightUpdateTimerRef && !window.trafficLightUpdateTimerRef.current) {\n                      console.log(`为路口ID ${modelKey} 的弹窗创建更新定时器`);\n                      setTimeout(() => {\n                        window.showTrafficLightPopup(modelKey);\n                      }, 100);\n                    }\n                  }\n                } else {\n                  // 如果找不到模型，仍使用原始ID存储\n                  trafficLightStates.set(interId, {\n                    updateTime: Date.now(),\n                    phases: phasesInfo\n                  });\n                  // console.log(`找不到匹配的红绿灯模型，使用原始ID ${interId} 保存了路口状态信息`);\n                }\n              } else {\n                console.error('SPAT消息缺少相位信息:', intersection);\n              }\n            });\n          } else {\n            console.error('SPAT消息格式错误，缺少data.intersections数组:', payload);\n          }\n        } catch (error) {\n          console.error('解析SPAT消息出错:', error, message);\n        }\n        return;\n      }\n      \n      // 处理 RSI 消息\n      if (topic === MQTT_CONFIG.rsi && payload.type === 'RSI') {\n        // console.log('收到RSI消息:', payload);\n        \n        // 发送 RSI 消息到 RealTimeTraffic 组件\n        window.postMessage({\n          type: 'RSI',\n          data: payload.data\n        }, '*');\n        \n        const rsiData = payload.data;\n        const rsuId = rsiData.rsuId;\n        const events = rsiData.rtes || [];\n        \n        events.forEach(event => {\n          const eventId = event.rteId;\n          const eventType = event.eventType;\n          const description = event.description;\n          const startTime = event.startTime;\n          const endTime = event.endTime;\n          \n          // 将基准点经纬度转换为模型坐标\n          const modelPos = converter.current.wgs84ToModel(\n            parseFloat(rsiData.posLong),\n            parseFloat(rsiData.posLat)\n          );\n          \n          // 根据事件类型显示不同的提示或标记\n          let warningText = '';\n          let warningColor = '';\n          \n          switch(eventType) {\n            case '401':  // 道路抛洒物\n              warningText = '道路抛洒物';\n              warningColor = '#ff4d4f';\n              break;\n            case '404':  // 道路障碍物\n              warningText = '道路障碍物';\n              warningColor = '#faad14';\n              break;\n            case '405':  // 行人通过马路\n              warningText = '行人通过马路';\n              warningColor = '#1890ff';\n              break;\n            case '904':  // 逆行车辆\n              warningText = '逆行车辆';\n              warningColor = '#f5222d';\n              break;\n            case '910':  // 违停车辆\n              warningText = '违停车辆';\n              warningColor = '#722ed1';\n              break;\n            case '1002': // 道路施工\n              warningText = '道路施工';\n              warningColor = '#fa8c16';\n              break;\n            case '901':  // 车辆超速\n              warningText = '车辆超速';\n              warningColor = '#eb2f96';\n              break;\n            default:\n              warningText = description || '未知事件';\n              warningColor = '#8c8c8c';\n          }\n          \n          // 显示警告标记\n          showWarningMarker(modelPos, warningText, warningColor);\n          \n          // console.log('RSI事件处理:', {\n          //   事件ID: eventId,\n          //   事件类型: eventType,\n          //   事件说明: description,\n          //   开始时间: startTime,\n          //   结束时间: endTime,\n          //   位置: modelPos\n          // });\n        });\n        \n        return;\n      }\n      \n      // 处理场景事件消息\n      if (topic === MQTT_CONFIG.scene && payload.type === 'SCENE') {\n        // console.log('收到场景事件消息:', payload);\n        \n        const sceneData = payload.data;\n        const sceneId = sceneData.sceneId;\n        const sceneType = sceneData.sceneType;\n        const sceneDesc = sceneData.sceneDesc;\n        const position = {\n          latitude: parseFloat(sceneData.partLat),\n          longitude: parseFloat(sceneData.partLong)\n        };\n        \n        // 将经纬度转换为模型坐标\n        const modelPos = converter.current.wgs84ToModel(position.longitude, position.latitude);\n        \n        // 根据场景类型显示不同的提示或标记\n        switch(sceneType) {\n          case '2':  // 交叉路口碰撞预警\n            showWarningMarker(modelPos, '交叉路口碰撞预警', '#ff4d4f');\n            break;\n          case '9-5':  // 道路危险状况预警（施工）\n            showWarningMarker(modelPos, '道路施工', '#faad14');\n            break;\n          case '9-6':  // 前方有障碍物\n            showWarningMarker(modelPos, '前方障碍物', '#ff7a45');\n            break;\n          case '10':  // 限速提醒\n            const speedLimit = sceneData.eventData1;  // 限速值\n            showWarningMarker(modelPos, `限速${speedLimit}km/h`, '#1890ff');\n            break;\n          case '12':  // 交通参与者碰撞预警\n            showWarningMarker(modelPos, '碰撞预警', '#f5222d');\n            break;\n          case '13':  // 绿波车速引导\n            showWarningMarker(modelPos, '绿波引导', '#52c41a');\n            break;\n          case '21-8':  // 禁止鸣笛\n            showWarningMarker(modelPos, '禁止鸣笛', '#722ed1');\n            break;\n          case '34':  // 逆行车辆提醒\n            showWarningMarker(modelPos, '逆行警告', '#eb2f96');\n            break;\n          case '33':  // 违章占道车辆预警\n            showWarningMarker(modelPos, '违章占道', '#fa8c16');\n            break;\n          case '999':  // 信号灯优先\n            const priorityType = sceneData.eventData1;  // 优先类型\n            const duration = sceneData.eventData2;      // 优先时长\n            showWarningMarker(modelPos, `信号优先-${getPriorityTypeText(priorityType)}${duration}秒`, '#13c2c2');\n            break;\n        }\n        \n        return;\n      }\n      // 如果不是RSM或BSM消息，则记录为其他类型\n      // console.log('未知类型消息:', {\n      //   topic,\n      //   type: payload.type,\n      //   data: payload\n      // });\n      \n    } catch (error) {\n      console.error('处理MQTT消息失败:', error);\n      console.error('原始消息内容:', message);\n    }\n  };\n\n  // 修改初始化MQTT连接函数\n  const initMqttClient = () => {\n    console.log('正在连接MQTT服务器...');\n    \n    const wsUrl = `ws://${MQTT_CONFIG.broker}:${MQTT_CONFIG.port}/mqtt`;\n    console.log('尝试连接WebSocket:', wsUrl);\n    \n    // 创建WebSocket连接，不指定协议\n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      console.log('WebSocket连接成功');\n    };\n    \n    ws.onmessage = (event) => {\n      try {\n        const message = JSON.parse(event.data);\n        \n        // 处理连接确认消息\n        if (message.type === 'connect') {\n          console.log('收到连接确认:', message);\n          return;\n        }\n        \n        // 处理心跳消息\n        if (message.type === 'ping') {\n          return;\n        }\n        \n        // 处理MQTT消息\n        if (message.type === 'message' && message.topic && message.payload) {\n          // 直接将消息传递给handleMqttMessage处理\n          handleMqttMessage(message.topic, JSON.stringify(message.payload));\n        }\n      } catch (error) {\n        console.error('处理WebSocket消息失败:', error);\n      }\n    };\n    \n    ws.onerror = (error) => {\n      console.error('WebSocket错误:', error);\n    };\n    \n    ws.onclose = () => {\n      console.log('WebSocket连接关闭');\n      // 5秒后尝试重新连接\n      setTimeout(initMqttClient, 5000);\n    };\n    \n    // 保存WebSocket引用\n    mqttClientRef.current = ws;\n  };\n\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    // 预加载所有模型\n    preloadModels();\n\n    // 创建场景\n    scene = new THREE.Scene(); // 使用全局scene变量\n    \n    // 创建相机\n    const camera = new THREE.PerspectiveCamera(\n      60,\n      window.innerWidth / window.innerHeight,\n      0.1,\n      2000\n    );\n    // camera.position.set(0, 300, 0); // 初始为全局视角\n    camera.position.set(0, 100, 0); // 初始为全局视角\n    camera.lookAt(0, 0, 0);\n    cameraRef.current = camera;\n    \n    // 创建渲染器\n    const renderer = new THREE.WebGLRenderer({ antialias: true });\n    renderer.setSize(window.innerWidth, window.innerHeight);\n    renderer.setClearColor(0x000000);\n    renderer.setPixelRatio(window.devicePixelRatio);\n    containerRef.current.appendChild(renderer.domElement);\n    \n    // 修改光照设置\n    // 添加环境光和平行光\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8); // 增加环境光强度从0.5到0.8\n    scene.add(ambientLight);\n\n    // 添加多个平行光源，从不同角度照亮车辆\n    const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.0); // 增加强度从0.8到1.0\n    directionalLight1.position.set(10, 10, 10);\n    scene.add(directionalLight1);\n\n    // 添加第二个平行光源，从另一个角度照亮\n    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);\n    directionalLight2.position.set(-10, 8, -10);\n    scene.add(directionalLight2);\n\n    // 添加一个聚光灯，专门照亮车辆\n    const spotLight = new THREE.SpotLight(0xffffff, 1.0);\n    spotLight.position.set(0, 50, 0);\n    spotLight.angle = Math.PI / 4;\n    spotLight.penumbra = 0.1;\n    spotLight.decay = 2;\n    spotLight.distance = 200;\n    scene.add(spotLight);\n\n    // 创建控制器\n    controls = new OrbitControls(camera, renderer.domElement);\n    controls.enableDamping = true;\n    controls.dampingFactor = 0.05;\n    controls.screenSpacePanning = false;\n    controls.minDistance = 50;\n    controls.maxDistance = 500;\n    controls.maxPolarAngle = Math.PI / 2.1;\n    controls.minPolarAngle = 0;\n    controls.target.set(0, 0, 0);\n    controls.update();\n    \n    // 打印相机和控制器引用\n    console.log('初始化完成', {\n      camera: !!camera,\n      controls: !!controls,\n      cameraRef: !!cameraRef.current\n    });\n    \n    // 修改加载车辆模型的函数\n    const loadVehicleModel = () => {\n      return new Promise((resolve, reject) => {\n        const vehicleLoader = new GLTFLoader();\n        vehicleLoader.load(\n          `${BASE_URL}/changli2/vehicle.glb`,\n          (gltf) => {\n            const vehicleModel = gltf.scene;\n            \n            // 创建一个新的Group作为根容器\n            const vehicleContainer = new THREE.Group();\n            \n            // 调整模型材质\n            vehicleModel.traverse((child) => {\n              if (child.isMesh) {\n                // 检查并调整材质\n                if (child.material) {\n                  // 创建新的标准材质\n                  const newMaterial = new THREE.MeshStandardMaterial({\n                    color: 0xffffff,      // 白色\n                    metalness: 0.2,       // 降低金属感\n                    roughness: 0.1,       // 降低粗糙度\n                    envMapIntensity: 1.0  // 环境贴图强度\n                  });\n                  \n                  // 保留原始贴图\n                  if (child.material.map) {\n                    newMaterial.map = child.material.map;\n                  }\n                  \n                  // 应用新材质\n                  child.material = newMaterial;\n                  \n                  console.log('已调整车辆材质:', child.name);\n                }\n              }\n            });\n            \n            // 遍历模型的所有子对象，确保它们都被正确添加到容器中\n            while(vehicleModel.children.length > 0) {\n              const child = vehicleModel.children[0];\n              vehicleContainer.add(child);\n            }\n            \n            // 确保容器直接添加到场景根节点\n            scene.add(vehicleContainer);\n            \n            // 保存容器的引用\n            globalVehicleRef = vehicleContainer;\n            \n            console.log('车辆模型加载成功，使用容器包装');\n            setIsVehicleLoaded(true);\n            resolve(vehicleContainer);\n          },\n          (xhr) => {\n            console.log(`车辆模型加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n          },\n          reject\n        );\n      });\n    };\n\n    // 修改初始化流程\n    const initializeScene = async () => {\n      try {\n        // 1. 加载车辆模型\n        // const vehicleContainer = await loadVehicleModel();\n        \n        // 2. 初始化MQTT客户端\n        initMqttClient();\n        \n        // 3. 设置初始位置\n        // if (vehicleContainer) {\n        //   const initialState = {\n        //     longitude: 113.0022348,\n        //     latitude: 28.0698301,\n        //     heading: 0\n        //   };\n\n        //   const initialPos = converter.current.wgs84ToModel(initialState.longitude, initialState.latitude);\n        //   // vehicleContainer.position.set(initialPos.x, 1.0, -initialPos.y);\n        //   vehicleContainer.position.set(0, 1.0, 0);\n        //   vehicleContainer.rotation.y = (Math.PI - initialState.heading * Math.PI / 180);\n        //   vehicleContainer.updateMatrix();\n        //   vehicleContainer.updateMatrixWorld(true);\n        //   currentPosition = vehicleContainer.position.clone();\n        // }\n        \n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    // 添加重试逻辑的加载函数\n    const loadModelWithRetry = (url, maxRetries = 3) => {\n      return new Promise((resolve, reject) => {\n        const attemptLoad = (retriesLeft) => {\n          console.log(`尝试加载模型: ${url}, 剩余重试次数: ${retriesLeft}`);\n          \n          const loader = new GLTFLoader();\n          loader.load(\n            url,\n            (gltf) => {\n              console.log(`模型加载成功: ${url}`);\n              resolve(gltf);\n            },\n            (xhr) => {\n              console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n            },\n            (error) => {\n              console.error(`加载失败: ${url}`, error);\n              if (retriesLeft > 0) {\n                console.log(`将在 1 秒后重试...`);\n                setTimeout(() => attemptLoad(retriesLeft - 1), 1000);\n              } else {\n                reject(error);\n              }\n            }\n          );\n        };\n\n        attemptLoad(maxRetries);\n      });\n    };\n\n    // 使用重试逻辑加载模型\n    const loader = new GLTFLoader();\n    loader.load(\n      `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n      async (gltf) => {\n        try {\n          const model = gltf.scene;\n          model.scale.set(1, 1, 1);\n          model.position.set(0, 0, 0);\n          \n          // 检查scene是否初始化\n          if (scene) {\n          scene.add(model);\n          \n          // 在校园模型加载完成后初始化场景\n          await initializeScene();\n          } else {\n            console.error('无法添加模型：场景未初始化');\n          }\n        } catch (error) {\n          console.error('处理模型时出错:', error);\n        }\n      },\n      (xhr) => {\n        console.log(`加载进度: ${(xhr.loaded / xhr.total * 100).toFixed(2)}%`);\n      },\n      (error) => {\n        console.error('模型加载错误:', error);\n        console.error('错误详情:', {\n          错误类型: error.type,\n          错误消息: error.message,\n          加载URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`,\n          完整URL: `${BASE_URL}/changli2/CLG_ALL_1025.glb`\n        });\n      }\n    );\n    \n    // 修改动画循环\n    const animate = () => {\n      animationFrameRef.current = requestAnimationFrame(animate);\n      \n      // 更新 TWEEN 动画\n      TWEEN.update();\n\n      // 更新行人动画 - 只更新存在的混合器\n      const deltaTime = clock.getDelta();\n      \n      // 使用Map而不是ref.current\n      if (peopleAnimationMixers.size > 0) {\n        peopleAnimationMixers.forEach((mixer) => {\n          mixer.update(deltaTime);\n        });\n      }\n\n      if (cameraMode === 'follow' && globalVehicleRef) {\n        // 在跟随模式下禁用控制器\n        controls.enabled = false;\n        \n        // 获取车辆当前位置\n        const vehiclePos = globalVehicleRef.position.clone();\n\n        // 获取车辆旋转角度\n        const vehicleRotation = globalVehicleRef.rotation.y;\n        \n        // 计算相机偏移量\n        // 添加 180 度（π）的偏移来调整方向，确保相机在车辆后方\n        const adjustedRotation = -(vehicleRotation - Math.PI) + Math.PI/2*3;\n        \n        // 增加距离到 50 单位\n        const cameraOffset = new THREE.Vector3(\n          -50 * Math.cos(adjustedRotation),\n          200,\n          -50 * Math.sin(adjustedRotation)\n        );\n        \n        // 计算目标相机位置和观察点\n        const targetCameraPosition = vehiclePos.clone().add(cameraOffset);\n        const targetLookAt = vehiclePos.clone();\n        \n        // 初始化上一帧数据（如果是首次）\n        if (!lastCameraPosition.current) {\n          lastCameraPosition.current = targetCameraPosition.clone();\n        }\n        \n        if (!lastCameraTarget.current) {\n          lastCameraTarget.current = targetLookAt.clone();\n        }\n        \n        // 应用平滑处理 - 使用lerp进行线性插值\n        lastCameraPosition.current.lerp(targetCameraPosition, 1 - cameraSmoothing);\n        lastCameraTarget.current.lerp(targetLookAt, 1 - cameraSmoothing);\n        \n        // 设置相机位置为平滑后的位置\n        camera.position.copy(lastCameraPosition.current);\n        \n        // 重置相机方向\n        camera.up.set(0, 1, 0);\n        \n        // 设置相机观察点为平滑后的目标\n        camera.lookAt(lastCameraTarget.current);\n        \n        // 强制更新相机矩阵\n        camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n        \n        // 禁用控制器\n        controls.enabled = false;\n        \n        // 确保控制器不会覆盖相机设置\n        controls.target.copy(lastCameraTarget.current);\n        controls.update();\n        \n        console.log('相机设置:', {\n          车辆位置: vehiclePos.toArray(),\n          相机位置: camera.position.toArray(),\n          相机目标: lastCameraTarget.current.toArray(),\n          相机朝向: camera.getWorldDirection(new THREE.Vector3()).toArray()\n        });\n      } else if (cameraMode === 'global') {\n        // 在全局模式或切换模式时重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 在全局模式下启用控制器\n        controls.enabled = true;\n        \n        // 确保相机的up向量保持正确\n        camera.up.set(0, 1, 0);\n        \n        // 如果相机位置偏离太多，重置到默认位置\n        if (Math.abs(camera.position.y) < 50) {\n          camera.position.set(0, 300, 0);\n          controls.target.set(0, 0, 0);\n          camera.lookAt(controls.target);\n          controls.update();\n        }\n\n        //         // 强制更新相机矩阵\n        // camera.updateProjectionMatrix();\n        camera.updateMatrix();\n        camera.updateMatrixWorld(true);\n\n      } else if (cameraMode === 'intersection') {\n        // 在路口视角模式下也重置平滑变量\n        lastCameraPosition.current = null;\n        lastCameraTarget.current = null;\n        \n        // 路口视角模式\n        controls.update();\n      }\n      \n      if (controls) controls.update();\n      if (scene && camera) {\n        renderer.render(scene, camera);\n      }\n    };\n\n    animate();\n    \n    // 处理窗口大小变化\n    const handleResize = () => {\n      camera.aspect = window.innerWidth / window.innerHeight;\n      camera.updateProjectionMatrix();\n      renderer.setSize(window.innerWidth, window.innerHeight);\n    };\n    window.addEventListener('resize', handleResize);\n    \n    // 添加全局函数用于手动切换视角\n    window.setGlobalView = () => {\n      if (cameraRef.current) {\n        cameraRef.current.position.set(0, 300, 0);\n        cameraRef.current.lookAt(0, 0, 0);\n        cameraRef.current.updateMatrix();\n        cameraRef.current.updateMatrixWorld(true);\n        \n        if (controls) {\n          controls.target.set(0, 0, 0);\n          controls.enabled = true;\n          controls.update();\n        }\n        \n        cameraMode = 'global';\n        console.log('手动切换到全局视角');\n        return true;\n      }\n      return false;\n    };\n    \n    // 修改清理函数\n    return () => {\n      console.log('开始清理组件...');\n      \n      // 1. 停止渲染循环\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n        animationFrameRef.current = null;\n      }\n\n      // 2. 停止所有 TWEEN 动画\n      TWEEN.removeAll();\n\n      // 3. 清理所有动画混合器\n      peopleAnimationMixers.forEach(mixer => {\n        resourceManager.removeMixer(mixer);\n      });\n      peopleAnimationMixers.clear();\n\n      // 4. 清理所有其他资源\n      resourceManager.cleanup();\n\n      // 5. 清理场景中的所有对象\n      if (scene) {\n        const objectsToRemove = [];\n        scene.traverse((object) => {\n          if (object.isMesh) {\n            if (object.geometry) {\n              object.geometry.dispose();\n            }\n            if (object.material) {\n              if (Array.isArray(object.material)) {\n                object.material.forEach(material => {\n                  if (material.map) material.map.dispose();\n                  material.dispose();\n                });\n              } else {\n                if (object.material.map) object.material.map.dispose();\n                object.material.dispose();\n              }\n            }\n            if (object !== scene) {\n              objectsToRemove.push(object);\n            }\n          }\n        });\n        \n        // 从场景中移除对象\n        objectsToRemove.forEach(obj => {\n          if (obj.parent) {\n            obj.parent.remove(obj);\n          }\n        });\n        \n        scene.clear();\n      }\n\n      // 6. 清理渲染器\n      if (renderer) {\n        renderer.setAnimationLoop(null);\n        if (containerRef.current && renderer.domElement && renderer.domElement.parentNode === containerRef.current) {\n          containerRef.current.removeChild(renderer.domElement);\n        }\n        renderer.dispose();\n        renderer.forceContextLoss();\n      }\n\n      // 7. 清理其他资源\n      if (controls) {\n        controls.dispose();\n      }\n\n      // 8. 清理数据结构\n      vehicleLastPositions.clear();\n      vehicleLastRotations.clear();\n      deviceTimestamps.clear();\n      vehicleModels.clear();\n      trafficLightsMap.clear();\n      trafficLightStates.clear();\n\n      console.log('组件清理完成');\n    };\n  }, []);\n\n  // 在组件挂载时获取主车信息和添加事件监听\n  useEffect(() => {\n    // 初始获取主车信息\n    fetchMainVehicleBsmId();\n    \n    // 添加自定义事件监听，用于接收主车变更通知\n    const handleMainVehicleChange = () => {\n      console.log('接收到主车变更通知，重新获取主车信息');\n      fetchMainVehicleBsmId();\n    };\n    \n    // 监听主车变更事件\n    window.addEventListener('mainVehicleChanged', handleMainVehicleChange);\n    \n    // 定时刷新主车信息（每分钟一次）\n    const intervalId = setInterval(() => {\n      fetchMainVehicleBsmId();\n    }, 60000);\n    \n    // 组件卸载时清理事件监听和定时器\n    return () => {\n      window.removeEventListener('mainVehicleChanged', handleMainVehicleChange);\n      clearInterval(intervalId);\n    };\n  }, []);\n\n  // 添加一个useEffect钩子在场景初始化完成后创建红绿灯\n  useEffect(() => {\n    // 在场景加载后初始化红绿灯\n    if (scene && converter.current) {\n      console.log('准备创建红绿灯模型');\n      // 延迟一秒创建红绿灯，确保模型已加载\n      const timer = setTimeout(() => {\n        if (scene && converter.current) {  // 再次检查，以防延迟期间组件卸载\n          createTrafficLights(converter.current);\n        }\n      }, 2000);\n      \n      return () => clearTimeout(timer);\n    } else {\n      console.log('场景或坐标转换器未准备好，暂不创建红绿灯');\n    }\n  }, [scene]);\n  \n  // 添加点击事件处理\n  useEffect(() => {\n    if (containerRef.current) {\n      // 定义点击处理函数\n      const handleClick = (event) => {\n        if (scene && cameraRef.current) {\n          handleMouseClick(event, containerRef.current, scene, cameraRef.current);\n        }\n      };\n      \n      // 添加点击事件监听\n      containerRef.current.addEventListener('click', handleClick);\n      \n      // 记录到控制台\n      console.log('已添加点击事件监听器到容器', !!containerRef.current);\n      \n      // 清理函数\n      return () => {\n        if (containerRef.current) {\n          containerRef.current.removeEventListener('click', handleClick);\n          console.log('已移除点击事件监听器');\n        }\n      };\n    }\n  }, [scene, cameraRef.current]);\n\n  // 初始化场景 - 简化为空函数，避免引用错误\n  const initScene = useCallback(() => {\n    console.log('initScene函数已禁用');\n    // 原始实现已移除，避免canvasRef未定义的错误\n  }, [containerRef, setCurrentRSU, trafficLightsMap]);\n\n  // 创建简单交通灯模型\n  const createSimpleTrafficLight = () => {\n    const geometry = new THREE.BoxGeometry(4, 15, 4);\n    const material = new THREE.MeshBasicMaterial({ color: 0x333333 });\n    const trafficLightModel = new THREE.Mesh(geometry, material);\n    \n    // 添加基座\n    const baseGeometry = new THREE.CylinderGeometry(2, 2, 2, 32);\n    const baseMaterial = new THREE.MeshBasicMaterial({ color: 0x666666 });\n    const baseModel = new THREE.Mesh(baseGeometry, baseMaterial);\n    baseModel.position.set(0, -8.5, 0);\n    trafficLightModel.add(baseModel);\n    \n    return trafficLightModel;\n  };\n\n  // 添加额外的点击检测辅助对象\n  const addClickHelpers = () => {\n    if (!scene) return;\n    \n    // 为每个红绿灯添加一个透明的大型碰撞体，便于点击\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 创建一个较大的碰撞检测几何体\n        const helperGeometry = new THREE.SphereGeometry(3, 3, 3);\n        const helperMaterial = new THREE.MeshBasicMaterial({\n          color: 0xff00ff,//\n          transparent: false,\n          opacity: 0.1,  // 几乎透明\n          depthWrite: false\n        });\n        \n        const helperMesh = new THREE.Mesh(helperGeometry, helperMaterial);\n        helperMesh.position.set(0, 0, 0);  // 放在红绿灯位置\n        \n        // 标记为click helper\n        helperMesh.userData = {\n          type: 'trafficLight',\n          interId: interId,\n          name: lightObj.intersection.name,\n          isClickHelper: true\n        };\n        \n        // 添加到红绿灯模型\n        lightObj.model.add(helperMesh);\n        \n        console.log(`为路口 ${lightObj.intersection.name} (${interId}) 添加点击辅助碰撞体`);\n      }\n    });\n  };\n\n  // 在创建红绿灯之后调用\n  useEffect(() => {\n    // 等待红绿灯创建完成后添加点击辅助对象\n    const timer = setTimeout(() => {\n      if (trafficLightsMap.size > 0) {\n        console.log('添加红绿灯点击辅助对象');\n        // addClickHelpers();\n      }\n    }, 5500);  // 延迟略长于debugTrafficLights\n    \n    return () => clearTimeout(timer);\n  }, []);\n\n  // // 在组件加载完毕后调用调试函数\n  // useEffect(() => {\n  //   // 延迟5秒调用调试函数，确保所有模型都已加载\n  //   const timer = setTimeout(() => {\n  //     console.log('调用场景调试函数');\n  //     if (window.debugScene) {\n  //       window.debugScene(); // 使用全局函数\n  //     } else {\n  //       console.error('debugScene函数未定义');\n  //     }\n  //   }, 5000);\n    \n  //   return () => clearTimeout(timer);\n  // }, []);\n\n  // 在useEffect中添加定时器清理逻辑\n  useEffect(() => {\n    return () => {\n      // 组件卸载时清理定时器\n      if (trafficLightUpdateTimerRef.current) {\n        clearInterval(trafficLightUpdateTimerRef.current);\n        trafficLightUpdateTimerRef.current = null;\n        console.log('已清理红绿灯状态更新定时器');\n      }\n    };\n  }, []); // 空依赖数组确保只在组件挂载和卸载时运行\n  \n  // 添加关闭弹窗时清理定时器的逻辑\n  useEffect(() => {\n    if (!trafficLightPopover.visible && trafficLightUpdateTimerRef.current) {\n      clearInterval(trafficLightUpdateTimerRef.current);\n      trafficLightUpdateTimerRef.current = null;\n      currentPopoverIdRef.current = null;\n      console.log('弹窗关闭，已清理红绿灯状态更新定时器');\n    }\n  }, [trafficLightPopover.visible]);\n\n  // 添加自动选择第一个路口的逻辑\n  useEffect(() => {\n    // 确保路口数据已加载\n    if (intersectionsData && intersectionsData.intersections && intersectionsData.intersections.length > 0) {\n      // 确保只在组件初次渲染并且未选择路口时执行\n      if (!selectedIntersection) {\n        // 查找第一个带有红绿灯的路口\n        const firstTrafficLightIntersection = intersectionsData.intersections.find(\n          intersection => intersection.hasTrafficLight !== false && intersection.interId\n        );\n        \n        // 如果找到带红绿灯的路口，优先选择它；否则选择第一个路口\n        const targetIntersection = firstTrafficLightIntersection || intersectionsData.intersections[0];\n        \n        console.log('自动选择路口:', targetIntersection.name, \n                    '具有红绿灯:', firstTrafficLightIntersection ? '是' : '否');\n        \n        // 延迟执行，确保场景和相机已初始化\n        const timer = setTimeout(() => {\n          handleIntersectionChange(targetIntersection.name);\n        }, 2000);\n        \n        return () => clearTimeout(timer);\n      }\n    }\n  }, [intersectionsData, selectedIntersection]);\n\n  return (\n    <>\n      <span style={labelStyle}>区域选择：</span>\n      <Select\n        style={intersectionSelectStyle}\n        placeholder=\"请选择区域位置\"\n        onChange={handleIntersectionChange}\n        options={intersectionsData.intersections.map(intersection => ({\n          value: intersection.name,\n          label: intersection.name\n        }))}\n        size=\"large\"\n        bordered={true}\n        dropdownStyle={{ \n          zIndex: 1002,\n          maxHeight: '300px'\n        }}\n        value={selectedIntersection ? selectedIntersection.name : undefined}\n      />\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      \n      {/* 添加红绿灯状态弹出窗口 */}\n      {trafficLightPopover.visible && (\n        <div \n          style={{\n            position: 'absolute',\n            left: `${trafficLightPopover.position.x}px`,\n            top: `${trafficLightPopover.position.y}px`,\n            transform: 'translate(-50%, -100%)',\n            zIndex: 1003,\n            backgroundColor: 'rgba(0, 0, 0, 0.85)',\n            color: 'white',\n            borderRadius: '4px',\n            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',\n            padding: '0',\n            maxWidth: '240px', // 缩小最大宽度\n            fontSize: '12px' // 缩小字体\n          }}\n        >\n          {trafficLightPopover.content}\n          <button \n            style={{\n              position: 'absolute',\n              top: '0px',\n              right: '0px',\n              background: 'none',\n              border: 'none',\n              color: 'white',\n              fontSize: '12px',\n              cursor: 'pointer',\n              padding: '2px 6px'\n            }}\n            onClick={() => handleClosePopover(setTrafficLightPopover)}\n          >\n            ×\n          </button>\n        </div>\n      )}\n      \n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text, parameters = {}) {\n  const params = {\n    fontFace: parameters.fontFace || 'Arial',\n    fontSize: parameters.fontSize || 16, // 从24px调小到16px\n    fontWeight: parameters.fontWeight || 'bold',\n    borderThickness: parameters.borderThickness || 4,\n    borderColor: parameters.borderColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    backgroundColor: parameters.backgroundColor || { r: 255, g: 255, b: 255, a: 0.8 },\n    textColor: parameters.textColor || { r: 0, g: 0, b: 0, a: 1.0 },\n    padding: parameters.padding || 5\n  };\n\n  // 创建画布\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  \n  // 设置字体\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  \n  // 测量文本宽度\n  const textWidth = context.measureText(text).width;\n  \n  // 设置画布尺寸，考虑边框和填充\n  const width = textWidth + 2 * params.padding + 2 * params.borderThickness;\n  const height = params.fontSize + 2 * params.padding + 2 * params.borderThickness;\n  \n  canvas.width = width;\n  canvas.height = height;\n  \n  // 重新设置字体，因为改变画布尺寸会重置上下文\n  context.font = `${params.fontWeight} ${params.fontSize}px ${params.fontFace}`;\n  context.textBaseline = 'middle';\n  \n  // 绘制背景和边框（圆角矩形）\n  const radius = 8;\n  context.beginPath();\n  context.moveTo(params.borderThickness + radius, params.borderThickness);\n  context.lineTo(width - params.borderThickness - radius, params.borderThickness);\n  context.arcTo(width - params.borderThickness, params.borderThickness, width - params.borderThickness, params.borderThickness + radius, radius);\n  context.lineTo(width - params.borderThickness, height - params.borderThickness - radius);\n  context.arcTo(width - params.borderThickness, height - params.borderThickness, width - params.borderThickness - radius, height - params.borderThickness, radius);\n  context.lineTo(params.borderThickness + radius, height - params.borderThickness);\n  context.arcTo(params.borderThickness, height - params.borderThickness, params.borderThickness, height - params.borderThickness - radius, radius);\n  context.lineTo(params.borderThickness, params.borderThickness + radius);\n  context.arcTo(params.borderThickness, params.borderThickness, params.borderThickness + radius, params.borderThickness, radius);\n  context.closePath();\n  \n  // 设置边框颜色\n  context.strokeStyle = `rgba(${params.borderColor.r}, ${params.borderColor.g}, ${params.borderColor.b}, ${params.borderColor.a})`;\n  context.lineWidth = params.borderThickness;\n  context.stroke();\n  \n  // 设置背景填充\n  context.fillStyle = `rgba(${params.backgroundColor.r}, ${params.backgroundColor.g}, ${params.backgroundColor.b}, ${params.backgroundColor.a})`;\n  context.fill();\n  \n  // 设置文字颜色\n  context.fillStyle = `rgba(${params.textColor.r}, ${params.textColor.g}, ${params.textColor.b}, ${params.textColor.a})`;\n  context.textAlign = 'center';\n  \n  // 绘制文本\n  context.fillText(text, width / 2, height / 2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  texture.minFilter = THREE.LinearFilter;\n  texture.needsUpdate = true;\n  \n  // 创建精灵材质\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  // 创建精灵\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(7, 3.5, 1); // 从(10, 5, 1)调整为(7, 3.5, 1)，整体缩小约30%\n  sprite.material.depthTest = false; // 确保始终可见\n  \n  // 保存文本信息到userData，便于后续更新\n  sprite.userData = { \n    text: text,\n    params: params\n  };\n  \n  return sprite;\n}\n\n\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\n\n// 修改车辆模型预加载函数\nconst preloadModels = async () => {\n  try {\n    console.log('开始预加载所有模型...');\n    const loader = new GLTFLoader();\n    \n    // 并行加载所有模型\n    try {\n      const [ trafficLightGltf, vehicleGltf, cyclistGltf, peopleGltf] = await Promise.all([\n        loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`),\n      loader.loadAsync(`${BASE_URL}/changli2/cyclist.glb`),\n        loader.loadAsync(`${BASE_URL}/changli2/people.glb`)\n        \n    ]);\n\n\n\n    // 处理机动车模型\n    console.log('加载车辆模型...');\n    preloadedVehicleModel = vehicleGltf.scene;\n    preloadedVehicleModel.traverse((child) => {\n      if (child.isMesh) {\n          const newMaterial = new THREE.MeshStandardMaterial({\n          color: 0xff0000,  // 0xff0000, //红色 0xffffff,白色\n          metalness: 0.2,\n          roughness: 0.1,\n          envMapIntensity: 1.0\n        });\n\n          // 保留原始贴图\n          if (child.material.map) {\n            newMaterial.map = child.material.map;\n          }\n          child.materia = newMaterial;\n      }\n    });\n\n    console.log('加载非机动车模型...');\n    // 处理非机动车模型\n    preloadedCyclistModel = cyclistGltf.scene;\n    // 设置非机动车模型的缩放\n    preloadedCyclistModel.scale.set(2, 2, 2);\n    // 保持原始材质\n    preloadedCyclistModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n      }\n    });\n\n    console.log('加载行人模型...');\n    // 处理行人模型\n    preloadedPeopleModel = peopleGltf.scene;\n    // 设置行人模型的缩放\n    // preloadedPeopleModel.scale.set(0.01, 0.01, 0.01);\n    // 保持原始材质\n    preloadedPeopleModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 只调整材质属性，保持原始颜色\n        child.material.metalness = 0.1;\n        child.material.roughness = 0.8;\n        child.material.envMapIntensity = 1.0;\n        \n      }\n      if (child.isMesh){\n        child.castShadow = true;\n      }\n    });\n\n\n\n    // 保存行人动画数据\n    console.log('找到行人动画sss:', peopleGltf.animations.length, '个');\n    if (peopleGltf.animations && peopleGltf.animations.length > 0) {\n      console.log('找到行人动画:', peopleGltf.animations.length, '个');\n      peopleBaseModel = peopleGltf;\n    } else {\n      console.warn('行人模型没有包含动画数据');\n    }\n\n    console.log('加载红绿灯模型...');\n      \n    // 处理红绿灯模型\n    preloadedTrafficLightModel = trafficLightGltf.scene;\n    console.log('红绿灯模型：', preloadedTrafficLightModel);\n    // 设置红绿灯模型的缩放\n    preloadedTrafficLightModel.scale.set(6, 6, 6);\n    // 保持原始材质\n    preloadedTrafficLightModel.traverse((child) => {\n      if (child.isMesh && child.material) {\n        // 设置材质属性\n        child.material.metalness = 0.2;\n        child.material.roughness = 0.3;\n        child.material.envMapIntensity = 1.2;\n    }\n  });\n\n    console.log('所有模型预加载成功');\n    } catch (error) {\n      console.error('加载特定模型失败，尝试单独加载:', error);\n      \n      // 如果整个Promise.all失败，尝试单独加载关键模型\n      try {\n        if (!preloadedVehicleModel) {\n          const vehicleGltf = await loader.loadAsync(`${BASE_URL}/changli2/vehicle.glb`);\n          preloadedVehicleModel = vehicleGltf.scene;\n        }\n        \n        // // 尝试单独加载红绿灯模型\n        // if (!preloadedTrafficLightModel) {\n        //   console.log('正在单独加载红绿灯模型...');\n        //   const trafficLightGltf = await loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`);\n        //   preloadedTrafficLightModel = trafficLightGltf.scene;\n        //   preloadedTrafficLightModel.scale.set(3, 3, 3);\n        //   console.log('红绿灯模型加载成功');\n        // }\n      } catch (err) {\n        console.error('单独加载模型也失败:', err);\n      }\n    }\n  } catch (error) {\n    console.error('模型预加载失败:', error);\n  }\n};\n\n// 添加辅助函数：获取优先类型文本\nconst getPriorityTypeText = (type) => {\n  const types = {\n    '1': '信号灯保持',\n    '2': '绿灯延长',\n    '3': '红灯截断',\n    '4': '相位插入',\n    '5': '相位插入',\n    '6': '优先未处理'\n  };\n  return types[type] || '未知类型';\n};\n\n// 添加辅助函数：显示警告标记\nconst showWarningMarker = (position, text, color) => {\n  // 添加安全检查 - 如果场景不存在，则直接返回\n  if (!scene) {\n    console.warn('无法显示警告标记：场景不存在或已卸载');\n    return;\n  }\n  \n  try {\n  // 创建一个新的警告标记\n  const sprite = createTextSprite(text);\n  sprite.position.set(position.x, 10, -position.y);  // 设置位置，稍微升高以便可见\n  \n  // 为标记添加一个定时器，在1秒后自动移除\n  setTimeout(() => {\n      // 再次检查场景是否存在\n      if (scene && sprite.parent) {\n    scene.remove(sprite);\n      }\n  }, 100);\n  \n  // 将标记添加到场景中\n  scene.add(sprite);\n  \n  // console.log('添加场景事件标记:', {\n  //   位置: position,\n  //   文本: text,\n  //   颜色: color\n  // });\n  } catch (error) {\n    console.error('显示警告标记时出错:', error);\n  }\n};\n\n// 添加创建红绿灯模型的函数\nconst createTrafficLights = (converterInstance) => {\n  if (!scene) {\n    console.error('无法创建红绿灯：场景未初始化');\n    return;\n  }\n  \n  if (!converterInstance) {\n    console.error('无法创建红绿灯：坐标转换器未初始化');\n    return;\n  }\n  \n  // 检查红绿灯模型是否已加载\n  if (!preloadedTrafficLightModel) {\n    console.error('红绿灯模型未加载，尝试重新加载...');\n    // 尝试重新加载红绿灯模型\n    const loader = new GLTFLoader();\n    loader.loadAsync(`${BASE_URL}/changli2/trafficlight.glb`)\n      .then(trafficLightGltf => {\n        preloadedTrafficLightModel = trafficLightGltf.scene;\n        preloadedTrafficLightModel.scale.set(6, 6, 6);\n        preloadedTrafficLightModel.traverse((child) => {\n          if (child.isMesh && child.material) {\n            child.material.metalness = 0.2;\n            child.material.roughness = 0.3;\n            child.material.envMapIntensity = 1.2;\n          }\n        });\n        console.log('红绿灯模型重新加载成功，开始创建红绿灯...');\n        // 重新调用创建函数\n        createTrafficLights(converterInstance);\n      })\n      .catch(error => {\n        console.error('红绿灯模型重新加载失败:', error);\n        // 如果加载失败，使用简单的替代物体\n        createFallbackTrafficLights(converterInstance);\n      });\n    return;\n  }\n  \n  // 先清除现有的红绿灯\n  trafficLightsMap.forEach((lightObj) => {\n    if (scene && lightObj.model) {\n      scene.remove(lightObj.model);\n    }\n  });\n  trafficLightsMap.clear();\n\n  // 为每个路口创建红绿灯模型\n  intersectionsData.intersections.forEach(intersection => {\n    if (intersection.hasTrafficLight === false) {\n      console.log(`跳过创建路口 ${intersection.name} 的红绿灯，因为该路口没有红绿灯`);\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      console.log(`开始为路口 ${intersection.name} (${intersection.interId}) 创建红绿灯, 坐标: (${modelPos.x}, ${modelPos.y})`);\n      \n      try {\n        // 确保模型存在且可以克隆\n        if (!preloadedTrafficLightModel || !preloadedTrafficLightModel.clone) {\n          throw new Error('红绿灯模型无效或无法克隆');\n        }\n        \n        // 创建红绿灯模型\n        const trafficLightModel = preloadedTrafficLightModel.clone();\n        \n        // 给模型一个名称便于调试\n        trafficLightModel.name = `交通灯-${intersection.name}`;\n        \n        // 设置位置，离地面高度为15米，提高可见性\n        trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n        \n        // 放大红绿灯模型尺寸，使其更容易被点击\n        trafficLightModel.scale.set(10, 10, 10);\n        \n        // 确保渲染顺序高，避免被其他对象遮挡\n        trafficLightModel.renderOrder = 100;\n        \n        // 设置材质属性\n        trafficLightModel.traverse(child => {\n          if (child.isMesh) {\n            child.material.transparent = false;\n            child.material.opacity = 1.0;\n            child.material.side = THREE.DoubleSide;\n            child.material.depthWrite = true;\n            child.material.depthTest = true;\n            child.material.needsUpdate = true;\n            child.renderOrder = 100;\n          }\n        });\n        \n        // 添加交互所需的信息\n        trafficLightModel.userData = {\n          type: 'trafficLight',\n          interId: intersection.interId,\n          name: intersection.name\n        };\n        \n        // 将红绿灯添加到场景中\n        scene.add(trafficLightModel);\n        \n        // 存储红绿灯引用\n        trafficLightsMap.set(intersection.interId, {\n          model: trafficLightModel,\n          intersection: intersection,\n          position: modelPos\n        });\n        \n        console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的红绿灯模型, 位置: (${modelPos.x}, ${-modelPos.y})`);\n      } catch (error) {\n        console.error(`创建路口 ${intersection.name} 的红绿灯模型失败:`, error);\n        // 如果克隆失败，创建一个简单的替代物体\n        createSimpleTrafficLight(intersection, modelPos, converterInstance);\n      }\n    }\n  });\n  \n  // 在控制台输出所有红绿灯的信息\n  console.log(`已创建 ${trafficLightsMap.size} 个红绿灯模型`);\n  trafficLightsMap.forEach((lightObj, interId) => {\n    console.log(`- ID ${interId}: ${lightObj.intersection.name}`);\n  });\n};\n\n// 创建所有路口的替代红绿灯\nconst createFallbackTrafficLights = (converterInstance) => {\n  intersectionsData.intersections.forEach(intersection => {\n    // 跳过没有红绿灯的路口\n    if (intersection.hasTrafficLight === false) {\n      return;\n    }\n    \n    if (intersection.latitude && intersection.longitude && intersection.interId) {\n      // 转换经纬度到模型坐标\n      const modelPos = converterInstance.wgs84ToModel(\n        parseFloat(intersection.longitude),\n        parseFloat(intersection.latitude)\n      );\n      \n      createSimpleTrafficLight(intersection, modelPos, converterInstance);\n    }\n  });\n};\n\n// 创建简单的替代红绿灯\nconst createSimpleTrafficLight = (intersection, modelPos, converterInstance) => {\n  // 创建一个简单的几何体作为红绿灯 - 增大尺寸\n  const geometry = new THREE.BoxGeometry(10, 30, 10);\n  const material = new THREE.MeshBasicMaterial({ \n    color: 0x333333,\n    transparent: false,\n    opacity: 1.0\n  });\n  const trafficLightModel = new THREE.Mesh(geometry, material);\n  \n  // 给模型一个名称便于调试\n  trafficLightModel.name = `简易交通灯-${intersection.name}`;\n  \n  // 设置位置 - 提高高度以增加可见性\n  trafficLightModel.position.set(modelPos.x, 15, -modelPos.y);\n  \n  // 设置渲染顺序\n  trafficLightModel.renderOrder = 100;\n  \n  // 添加交互所需的信息\n  trafficLightModel.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  // 添加一个专门用于点击的大型碰撞体\n  const colliderGeometry = new THREE.SphereGeometry(3, 12, 12);\n  const colliderMaterial = new THREE.MeshBasicMaterial({\n    color: 0xff00ff,\n    transparent: true,\n    opacity: 0.0,  // 完全透明\n    depthWrite: false\n  });\n  \n  const collider = new THREE.Mesh(colliderGeometry, colliderMaterial);\n  collider.name = `简易交通灯碰撞体-${intersection.name}`;\n  collider.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name,\n    isCollider: true\n  };\n  \n  trafficLightModel.add(collider);\n  \n  // 将红绿灯添加到场景中\n  scene.add(trafficLightModel);\n  \n  // 存储红绿灯引用\n  trafficLightsMap.set(intersection.interId, {\n    model: trafficLightModel,\n    intersection: intersection,\n    position: modelPos\n  });\n  \n  // 添加一个顶部灯光标识，使其更容易被看到\n  const lightGeometry = new THREE.SphereGeometry(5, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ color: 0xFF0000 });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 15, 0);\n  \n  // 给灯光相同的userData\n  lightMesh.userData = {\n    type: 'trafficLight',\n    interId: intersection.interId,\n    name: intersection.name\n  };\n  \n  trafficLightModel.add(lightMesh);\n  \n  console.log(`已创建路口 ${intersection.name} (${intersection.interId}) 的简易红绿灯, 位置: (${modelPos.x}, ${-modelPos.y})`);\n};\n\n// 添加获取相位方向和名称的辅助函数\nconst getPhaseDirection = (phaseId) => {\n  switch(phaseId) {\n    case '1': return '北进口左转';\n    case '2': return '北进口直行';\n    case '3': return '北进口右转';\n    case '5': return '东进口左转';\n    case '6': return '东进口直行';\n    case '7': return '东进口右转';\n    case '9': return '南进口左转';\n    case '10': return '南进口直行';\n    case '11': return '南进口右转';\n    case '13': return '西进口左转';\n    case '14': return '西进口直行';\n    case '15': return '西进口右转';\n    default: return `相位${phaseId}`;\n  }\n};\n\n// 添加辅助函数，从trafficDirec(如N/S/E/W)获取方向描述\nconst getDirectionFromCode = (dirCode) => {\n  switch(dirCode) {\n    case 'N': return '北向南';\n    case 'S': return '南向北';\n    case 'E': return '东向西';\n    case 'W': return '西向东';\n    case 'NE': return '东北向西南';\n    case 'NW': return '西北向东南';\n    case 'SE': return '东南向西北';\n    case 'SW': return '西南向东北';\n    default: return `方向${dirCode}`;\n  }\n};\n\n// 修改点击处理函数\nconst handleMouseClick = (event, container, sceneInstance, cameraInstance) => {\n  if (!container || !sceneInstance || !cameraInstance) return;\n  \n  console.log('触发点击事件处理函数', event.clientX, event.clientY);\n  \n  // 计算鼠标在容器中的相对位置\n  const rect = container.getBoundingClientRect();\n  const mouseX = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;\n  const mouseY = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;\n  \n  // 创建射线\n  const raycaster = new THREE.Raycaster();\n  // 增加检测阈值，使小物体也能被点击到\n  raycaster.params.Points.threshold = 1;\n  raycaster.params.Line.threshold = 1;\n  \n  const mouseVector = new THREE.Vector2(mouseX, mouseY);\n  raycaster.setFromCamera(mouseVector, cameraInstance);\n  \n  console.log('鼠标点击位置:', mouseX, mouseY);\n  \n  // 创建一个单独的数组来存储所有红绿灯模型，确保它们可以被检测到\n  const trafficLightObjects = [];\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      // 将模型添加到数组中\n      trafficLightObjects.push(lightObj.model);\n      // 确保模型是可见的，并且不会被其他对象遮挡\n      lightObj.model.visible = true;\n      lightObj.model.renderOrder = 1000; // 设置高渲染优先级\n      // 确保模型的所有子对象都是可见的\n      lightObj.model.traverse(child => {\n        child.visible = true;\n        child.renderOrder = 1000;\n      });\n    }\n  });\n  \n  console.log(`场景中有 ${trafficLightObjects.length} 个红绿灯模型可用于点击检测`);\n  \n  // 首先：尝试直接检测红绿灯模型集合\n  const trafficLightIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n  \n  if (trafficLightIntersects.length > 0) {\n    console.log('直接命中红绿灯模型:', trafficLightIntersects.length);\n    trafficLightIntersects.forEach((intersect, index) => {\n      console.log(`命中对象 ${index}:`, \n                 intersect.object.name || '无名称', \n                 '距离:', intersect.distance,\n                 'userData:', intersect.object.userData);\n    });\n    \n    // 获取第一个交点的对象\n    const obj = getTrafficLightFromObject(trafficLightIntersects[0].object);\n    \n    if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n      // 获取红绿灯ID\n      const interId = obj.userData.interId;\n      console.log('成功检测到红绿灯点击, 路口ID:', interId, '类型:', typeof interId);\n      \n      // 检查trafficLightsMap中可能的ID类型\n      let correctId = interId;\n      if (typeof interId === 'string' && !trafficLightsMap.has(interId) && trafficLightsMap.has(parseInt(interId))) {\n        correctId = parseInt(interId);\n        console.log(`转换ID类型为数字: ${correctId}`);\n      } else if (typeof interId === 'number' && !trafficLightsMap.has(interId) && trafficLightsMap.has(String(interId))) {\n        correctId = String(interId);\n        console.log(`转换ID类型为字符串: ${correctId}`);\n      }\n      \n      // 显示弹窗\n      window.showTrafficLightPopup(correctId);\n      return;\n    }\n  }\n  \n  // 第二步：检测所有场景对象\n  const intersects = raycaster.intersectObjects(sceneInstance.children, true);\n  \n  console.log('射线检测到的对象数量:', intersects.length);\n  \n  if (intersects.length > 0) {\n    // 输出所有检测到的对象信息用于调试\n    intersects.forEach((intersect, index) => {\n      const obj = intersect.object;\n      console.log(`检测到的对象 ${index}:`, obj.name || '无名称', \n                  'userData:', obj.userData, \n                  '距离:', intersect.distance);\n    });\n    \n    // 检查是否点击了红绿灯\n    for (let i = 0; i < intersects.length; i++) {\n      const obj = getTrafficLightFromObject(intersects[i].object);\n      if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n        const interId = obj.userData.interId;\n        window.showTrafficLightPopup(interId);\n        return;\n      }\n    }\n  }\n  \n  // 第三步：如果上述方法都失败，尝试找到最接近点击位置的红绿灯\n  console.log('尝试查找最接近点击位置的红绿灯');\n  \n  // 将红绿灯模型投影到屏幕坐标中\n  let closestLight = null;\n  let minDistance = 0.1; // 设置一个阈值，只有距离小于此值的才会被选中\n  \n  trafficLightsMap.forEach((lightObj, interId) => {\n    if (lightObj.model) {\n      const worldPos = new THREE.Vector3();\n      // 获取模型的世界坐标\n      lightObj.model.getWorldPosition(worldPos);\n      \n      // 将世界坐标投影到屏幕坐标\n      const screenPos = worldPos.clone();\n      screenPos.project(cameraInstance);\n      \n      // 计算鼠标与红绿灯在屏幕上的距离\n      const dx = screenPos.x - mouseX;\n      const dy = screenPos.y - mouseY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      \n      console.log(`路口 ${interId} 的距离:`, distance);\n      \n      if (distance < minDistance) {\n        minDistance = distance;\n        closestLight = { interId, distance };\n      }\n    }\n  });\n  \n  if (closestLight) {\n    console.log(`找到最接近的红绿灯, 路口ID: ${closestLight.interId}, 距离: ${closestLight.distance}`);\n    \n    // 显示弹窗\n    window.showTrafficLightPopup(closestLight.interId);\n    return;\n  }\n  \n  console.log('未检测到任何红绿灯点击');\n};\n\n// 关闭弹出窗口的处理函数\nconst handleClosePopover = (setPopoverState) => {\n  // 清理定时器\n  if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n    clearInterval(window.trafficLightUpdateTimerRef.current);\n    window.trafficLightUpdateTimerRef.current = null;\n    console.log('已清理红绿灯状态更新定时器');\n  }\n  \n  // 清理当前弹窗ID\n  if (window.currentPopoverIdRef) {\n    window.currentPopoverIdRef.current = null;\n  }\n  \n  // 更新弹窗状态为不可见\n  setPopoverState(prev => ({ \n    ...prev, \n    visible: false,\n    content: null, // 清空内容\n    phases: []     // 清空相位信息\n  }));\n  \n  console.log('弹窗已关闭，所有相关资源已清理');\n};\n\n// 添加一个全局方法用于测试红绿灯点击\nwindow.testTrafficLightClick = (interId) => {\n  try {\n    // 检查是否有该ID的红绿灯\n    const trafficLight = trafficLightsMap.get(interId || '1');\n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      \n      // 输出所有可用ID\n      console.log('可用的红绿灯ID:');\n      trafficLightsMap.forEach((light, id) => {\n        console.log(`- ${id}: ${light.intersection.name}`);\n      });\n      \n      return false;\n    }\n    \n    // 获取红绿灯模型\n    const lightModel = trafficLight.model;\n    \n    // 模拟点击事件\n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 创建弹出窗口内容\n    let content;\n    \n    if (stateInfo && stateInfo.phases) {\n      content = (\n        <div style={{ padding: '8px', width: '220px', maxHeight: '300px', overflowY: 'auto' }}>\n          <div style={{ \n            fontWeight: 'bold', \n            marginBottom: '6px',\n            fontSize: '14px',\n            borderBottom: '1px solid #eee',\n            paddingBottom: '4px'\n          }}>\n            {intersection.name} (ID: {interId})\n          </div>\n          <div>\n            {stateInfo.phases.map((phase, index) => {\n              let lightColor;\n              switch (phase.trafficLight) {\n                case 'G': lightColor = '#00ff00'; break;\n                case 'Y': lightColor = '#ffff00'; break;\n                case 'R': default: lightColor = '#ff0000'; break;\n              }\n              \n              return (\n                <div key={index} style={{ \n                  marginBottom: '6px', \n                  backgroundColor: 'rgba(255,255,255,0.1)',\n                  padding: '4px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                }}>\n                  <div style={{ fontWeight: 'bold' }}>\n                    {getPhaseDirection(phase.phaseId)}\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>灯色: </span>\n                    <span style={{ \n                      color: lightColor, \n                      fontWeight: 'bold',\n                      backgroundColor: 'rgba(0,0,0,0.3)',\n                      padding: '0 3px',\n                      borderRadius: '2px'\n                    }}>\n                      {phase.trafficLight === 'R' ? '红灯' : phase.trafficLight === 'Y' ? '黄灯' : '绿灯'}\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <span>倒计时: </span>\n                    <span style={{ fontWeight: 'bold' }}>{phase.remainTime} 秒</span>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          <div style={{ marginTop: '6px', fontSize: '10px', color: '#888' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      content = (\n        <div style={{ padding: '8px', maxWidth: '200px' }}>\n          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>{intersection.name}</div>\n          <div>路口ID: {interId}</div>\n          <div>当前无信号灯状态信息</div>\n        </div>\n      );\n    }\n    \n    // 获取弹窗位置 - 使用中心位置\n    const centerX = window.innerWidth / 2 -500;\n    const centerY = window.innerHeight / 2 -500;\n    \n    // 获取全局的setTrafficLightPopover函数\n    const setPopoverState = document.querySelector('#root')?.__REACT_INSTANCE?.setTrafficLightPopover;\n    \n    if (setPopoverState) {\n      // 直接调用React组件的状态更新函数\n      setPopoverState({\n        visible: true,\n        interId: interId,\n        position: { x: centerX, y: centerY },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      console.log(`已触发路口 ${intersection.name} (${interId}) 的红绿灯弹窗`);\n      return true;\n    } else {\n      // 如果无法直接调用React函数，则尝试创建一个DOM元素显示弹窗\n      const popover = document.createElement('div');\n      popover.style.position = 'absolute';\n      popover.style.left = `${centerX}px`;\n      popover.style.top = `${centerY}px`;\n      popover.style.transform = 'translate(-50%, -100%)';\n      popover.style.zIndex = '9999';\n      popover.style.backgroundColor = 'rgba(0, 0, 0, 0.85)';\n      popover.style.color = 'white';\n      popover.style.borderRadius = '4px';\n      popover.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';\n      popover.style.padding = '8px';\n      popover.style.maxWidth = '240px';\n      popover.style.fontSize = '12px';\n      \n      popover.innerHTML = `\n        <div style=\"font-weight: bold; margin-bottom: 6px; font-size: 14px; border-bottom: 1px solid #eee; padding-bottom: 4px;\">\n          ${intersection.name} (ID: ${interId})\n        </div>\n        <div>\n          <p>路口ID: ${interId}</p>\n          <p>${stateInfo ? '红绿灯状态已加载' : '当前无信号灯状态信息'}</p>\n        </div>\n        <button style=\"position: absolute; top: 0px; right: 0px; background: none; border: none; color: white; font-size: 12px; cursor: pointer; padding: 2px 6px;\">×</button>\n      `;\n      \n      document.body.appendChild(popover);\n      \n      // 添加关闭按钮点击事件\n      const closeButton = popover.querySelector('button');\n      if (closeButton) {\n        closeButton.addEventListener('click', () => {\n          document.body.removeChild(popover);\n        });\n      }\n      \n      console.log(`已创建DOM弹窗显示路口 ${intersection.name} (${interId}) 的信息`);\n      return true;\n    }\n  } catch (error) {\n    console.error('测试红绿灯点击失败:', error);\n    return false;\n  }\n};\n\n// 添加一个全局方法用于显示所有红绿灯ID\nwindow.listTrafficLights = () => {\n  console.log('红绿灯列表:');\n  \n  if (!trafficLightsMap || trafficLightsMap.size === 0) {\n    console.log('当前没有红绿灯对象');\n    return [];\n  }\n  \n  const list = [];\n  trafficLightsMap.forEach((light, id) => {\n    console.log(`- ID: ${id}, 名称: ${light.intersection.name}`);\n    list.push({\n      id,\n      name: light.intersection.name,\n      position: light.position\n    });\n  });\n  \n  return list;\n};\n\n// 添加全局调试方法\n// window.debugScene = function() {\n//   if (!scene) {\n//     console.error('场景未初始化，无法调试红绿灯');\n//     return;\n//   }\n\n//   console.log('开始调试红绿灯模型...');\n  \n//   // 检查红绿灯映射\n//   console.log('红绿灯映射数量:', trafficLightsMap.size);\n  \n//   // 统计场景中的可互动对象\n//   let interactiveObjects = 0;\n//   let trafficLightObjects = 0;\n  \n//   // 遍历场景中的所有对象\n//   scene.traverse((object) => {\n//     // 检查是否有userData\n//     if (object.userData && Object.keys(object.userData).length > 0) {\n//       interactiveObjects++;\n      \n//       // 检查是否是红绿灯\n//       if (object.userData.type === 'trafficLight') {\n//         trafficLightObjects++;\n//         console.log('找到红绿灯对象:', {\n//           名称: object.name || '无名称',\n//           类型: object.userData.type,\n//           路口ID: object.userData.interId,\n//           位置: object.position.toArray(),\n//           可见性: object.visible,\n//           是否是网格: object.isMesh,\n//           userData: object.userData\n//         });\n//       }\n//     }\n//   });\n  \n//   console.log('场景中的可互动对象数量:', interactiveObjects);\n//   console.log('红绿灯对象数量:', trafficLightObjects);\n  \n//   // 遍历红绿灯映射，创建高亮标记\n//   trafficLightsMap.forEach((lightObj, interId) => {\n//     if (lightObj.model) {\n//       // 获取红绿灯位置\n//       const position = lightObj.model.position.clone();\n      \n//       // 创建一个高亮球体\n//       const highlightGeometry = new THREE.SphereGeometry(5, 16, 16);\n//       const highlightMaterial = new THREE.MeshBasicMaterial({ \n//         color: 0xff00ff, \n//         transparent: true,\n//         opacity: 0.7\n//       });\n//       const highlightMesh = new THREE.Mesh(highlightGeometry, highlightMaterial);\n      \n//       // 设置位置，稍微偏移，避免遮挡\n//       highlightMesh.position.set(position.x, position.y + 15, position.z);\n      \n//       // 添加到场景\n//       scene.add(highlightMesh);\n      \n//       // 添加用户数据，方便调试\n//       highlightMesh.userData = {\n//         type: 'trafficLightHighlight',\n//         interId: interId,\n//         name: lightObj.intersection.name,\n//         originalPosition: position.toArray()\n//       };\n      \n//       // 5秒后自动移除高亮标记\n//       setTimeout(() => {\n//         scene.remove(highlightMesh);\n//       }, 5000);\n      \n//       console.log(`已为路口 ${lightObj.intersection.name} (${interId}) 的红绿灯添加高亮标记`);\n//     }\n//   });\n  \n//   // 将调试信息显示在控制台\n//   console.log('红绿灯调试信息:', {\n//     红绿灯映射数量: trafficLightsMap.size,\n//     红绿灯状态数量: trafficLightStates.size,\n//     场景中红绿灯对象数量: trafficLightObjects,\n//     射线检测启用: true\n//   });\n// };\n\n// 添加全局测试弹窗函数\nwindow.showTrafficLightPopup = (interId) => {\n  try {\n    // 确保interId为字符串类型\n    interId = String(interId);\n    \n    console.log('调用showTrafficLightPopup函数, 参数ID:', interId, '类型:', typeof interId);\n    console.log('当前trafficLightsMap大小:', trafficLightsMap.size);\n    \n    // 检查是否有该ID的红绿灯 - 尝试数字和字符串两种类型\n    let trafficLight = trafficLightsMap.get(interId);\n    if (!trafficLight) {\n      // 尝试转换为数字查找\n      const numericId = parseInt(interId);\n      trafficLight = trafficLightsMap.get(numericId);\n      \n      if (trafficLight) {\n        console.log(`使用数字ID ${numericId} 找到了红绿灯`);\n        interId = numericId; // 更新interId为找到的正确类型\n      }\n    }\n    \n    if (!trafficLight) {\n      console.error('未找到指定ID的红绿灯:', interId);\n      return false;\n    }\n    \n    const stateInfo = trafficLightStates.get(interId);\n    const intersection = trafficLight.intersection;\n    \n    // 判断是否有相位数据\n    const hasPhaseData = stateInfo && stateInfo.phases && stateInfo.phases.length > 0;\n    \n    let content;\n    \n    // 指北针样式\n    const compassStyle = {\n      position: 'absolute',\n      top: '5px',\n      right: '25px',\n      width: '30px',\n      height: '30px',\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      borderRadius: '50%',\n      background: 'rgba(0,0,0,0.1)',\n      zIndex: 10\n    };\n    \n    // 指北针组件\n    const CompassIcon = () => (\n      <div style={compassStyle}>\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          transform: 'rotate(0deg)'\n        }}>\n          <span style={{\n            color: '#ff5252',\n            fontSize: '14px',\n            fontWeight: 'bold',\n            lineHeight: '14px'\n          }}>N</span>\n          <span style={{\n            width: 0,\n            height: 0,\n            borderLeft: '6px solid transparent',\n            borderRight: '6px solid transparent',\n            borderBottom: '10px solid #ff5252',\n            marginTop: '-2px'\n          }}></span>\n        </div>\n      </div>\n    );\n    \n    if (hasPhaseData) {\n      // 相位ID与方向/方式的映射\n      const phaseMap = {\n        '1': { dir: 'N', type: 'left' }, '2': { dir: 'N', type: 'straight' }, '3': { dir: 'N', type: 'right' },\n        '5': { dir: 'E', type: 'left' }, '6': { dir: 'E', type: 'straight' }, '7': { dir: 'E', type: 'right' },\n        '9': { dir: 'S', type: 'left' }, '10': { dir: 'S', type: 'straight' }, '11': { dir: 'S', type: 'right' },\n        '13': { dir: 'W', type: 'left' }, '14': { dir: 'W', type: 'straight' }, '15': { dir: 'W', type: 'right' }\n      };\n      \n      const typeOrder = ['left', 'straight', 'right'];\n      const colorMap = { G: '#00ff00', Y: '#ffff00', R: '#ff0000' };\n      const dirData = { N: {}, E: {}, S: {}, W: {} };\n      \n      stateInfo.phases.forEach(phase => {\n        const map = phaseMap[phase.phaseId];\n        if (map) {\n          dirData[map.dir][map.type] = {\n            color: colorMap[phase.trafficLight] || '#888',\n            remainTime: phase.remainTime\n          };\n        }\n      });\n      \n      content = (\n        <div style={{ padding: '8px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>\n          <CompassIcon />\n          <div style={{ fontWeight: 'bold', marginBottom: '8px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}灯态</div>\n          <div style={{\n            display: 'grid',\n            gridTemplateRows: '60px 60px 60px',\n            gridTemplateColumns: '60px 60px 60px',\n            justifyContent: 'center',\n            alignItems: 'center',\n            background: 'rgba(255,255,255,0.05)',\n            borderRadius: '8px',\n            margin: '0 auto',\n            position: 'relative'\n          }}>\n            {/* 北 - 左转、直行、右转箭头水平对齐，倒计时在箭头上面 */}\n            {/* <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%', paddingLeft: '5px' }}> */}\n            <div style={{ gridRow: 1, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}> \n              {typeOrder.map((type, index) => {\n                // 反转左转和右转在数组中的顺序\n                let displayIndex = index;\n                if (index === 0) displayIndex = 2;\n                else if (index === 2) displayIndex = 0;\n                \n                const currentType = typeOrder[displayIndex];\n                \n                // 计算与南边对齐的样式\n                const marginStyle = {};\n                if (currentType === 'left') { // 左转箭头 (右侧显示)\n                  marginStyle.marginRight = '0px';\n                } else if (currentType === 'straight') { // 直行箭头 (中间显示)\n                  marginStyle.marginLeft = '10px';\n                  marginStyle.marginRight = '10px';\n                } else if (currentType === 'right') { // 右转箭头 (左侧显示)\n                  marginStyle.marginLeft = '0px';\n                }\n                \n                return dirData.N[currentType] && (\n                  // <div key={currentType} style={{\n                  //   display: 'flex', \n                  //   flexDirection: 'column', \n                  //   alignItems: 'center',\n                  //   ...marginStyle\n                  // }}>\n                  <div key={currentType} style={{marginRight: currentType === 'left' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>\n                    <div style={{fontSize:'14px', color: dirData.N[currentType].color, fontWeight:'bold', marginBottom: '3px'}}>{dirData.N[currentType].remainTime}</div>\n                    <span style={{color: dirData.N[currentType].color, fontSize:'20px', lineHeight: '20px'}}>\n                      {currentType === 'left' ? '\\u{1F87A}' : currentType === 'straight' ? '\\u{1F87B}' : '\\u{1F878}'}\n                    </span>\n                  </div>\n                );\n              })}\n            </div>\n            \n            {/* 南 - 左转、直行、右转箭头水平对齐，倒计时在箭头下面 */}\n            <div style={{ gridRow: 3, gridColumn: 2, textAlign: 'center', display: 'flex', justifyContent: 'center', width: '100%' }}>\n              {typeOrder.map(type => dirData.S[type] && (\n                <div key={type} style={{marginRight: type === 'right' ? 0 : '10px', display: 'flex', flexDirection: 'column', alignItems: 'center'}}>\n                  <span style={{color: dirData.S[type].color, fontSize:'20px', lineHeight: '20px'}}>\n                    {type === 'left' ? '\\u{1F878}' : type === 'straight' ? '\\u{1F879}' : '\\u{1F87A}'}\n                  </span>\n                  <div style={{fontSize:'14px', color: dirData.S[type].color, fontWeight:'bold', marginTop: '3px'}}>{dirData.S[type].remainTime}</div>\n                </div>\n              ))} \n            </div>\n            \n            {/* 东 - 倒计时显示在箭头右边 */}\n\n            <div style={{ gridRow: 2, gridColumn: 3, textAlign: 'center' }}>\n              {typeOrder.map((type, index) => {\n                // 反转左转和右转在数组中的顺序\n                let displayIndex = index;\n                if (index === 0) displayIndex = 2;\n                else if (index === 2) displayIndex = 0;\n                \n                const currentType = typeOrder[displayIndex];\n                \n                return dirData.E[currentType] && (\n                  <div key={currentType} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-start'}}>\n                    <span style={{color: dirData.E[currentType].color, fontSize:'20px', lineHeight: '20px'}}>\n                      {currentType === 'left' ? '\\u{1F87B}' : currentType === 'straight' ? '\\u{1F878}' : '\\u{1F879}'}\n                    </span>\n                    <div style={{\n                      fontSize:'14px', \n                      color: dirData.E[currentType].color, \n                      fontWeight:'bold', \n                      marginLeft: '5px'\n                    }}>{dirData.E[currentType].remainTime}</div>\n                  </div>\n                );\n              })}\n            </div>\n            \n            {/* 西 - 倒计时显示在箭头左边 */}\n            <div style={{ gridRow: 2, gridColumn: 1, textAlign: 'center' }}>\n              {typeOrder.map(type => dirData.W[type] && (\n                <div key={type} style={{marginBottom:'8px', display: 'flex', alignItems: 'center', justifyContent: 'flex-end'}}>\n                  <div style={{\n                    fontSize:'14px', \n                    color: dirData.W[type].color, \n                    fontWeight:'bold', \n                    marginRight: '5px'\n                  }}>{dirData.W[type].remainTime}</div>\n                  <span style={{color: dirData.W[type].color, fontSize:'20px', lineHeight: '20px'}}>\n                    {type === 'left' ? '\\u{1F879}' : type === 'straight' ? '\\u{1F87A}' : '\\u{1F87B}'}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n          <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    } else {\n      // 没有相位数据时显示的内容\n      content = (\n        <div style={{ padding: '15px', width: '260px', background: 'rgba(0,0,0,0.05)', position: 'relative' }}>\n          <CompassIcon />\n          <div style={{ fontWeight: 'bold', marginBottom: '10px', fontSize: '15px', textAlign: 'center' }}>{intersection.name}</div>\n          <div style={{ \n            textAlign: 'center', \n            padding: '20px 0',\n            color: '#ff9800', \n            fontSize: '14px', \n            fontWeight: 'bold',\n            background: 'rgba(255,255,255,0.1)',\n            borderRadius: '8px',\n            marginBottom: '10px'\n          }}>\n            当前无信号灯状态信息\n          </div>\n          <div style={{ fontSize: '12px', color: '#888', textAlign: 'center' }}>\n            路口ID: {interId}\n          </div>\n          <div style={{ marginTop: '8px', fontSize: '11px', color: '#888', textAlign: 'center' }}>\n            更新时间: {new Date().toLocaleTimeString()} (自动刷新)\n          </div>\n        </div>\n      );\n    }\n    \n    // 设置弹窗位置在左上角区域\n    const x = 445;\n    const y = 250;\n    \n    // 更新当前显示的红绿灯ID引用\n    if (window.currentPopoverIdRef) {\n      window.currentPopoverIdRef.current = interId;\n    }\n    \n    // 取消之前的更新定时器（如果存在）\n    if (window.trafficLightUpdateTimerRef && window.trafficLightUpdateTimerRef.current) {\n      clearInterval(window.trafficLightUpdateTimerRef.current);\n      window.trafficLightUpdateTimerRef.current = null;\n    }\n    \n    // 更新弹窗状态\n    if (window._setTrafficLightPopover) {\n      window._setTrafficLightPopover({\n        visible: true,\n        interId: interId,\n        position: { x, y },\n        content: content,\n        phases: stateInfo?.phases || []\n      });\n      \n      // 设置定时更新\n      if (window.trafficLightUpdateTimerRef) {\n        window.trafficLightUpdateTimerRef.current = setInterval(() => {\n          window.showTrafficLightPopup(interId);\n        }, 1000);\n      }\n      \n      return true;\n    } else {\n      console.error('无法找到setTrafficLightPopover函数');\n      return false;\n    }\n  } catch (error) {\n    console.error('显示红绿灯弹窗失败:', error);\n    return false;\n  }\n};\n\n// // 添加全局模拟SPAT数据生成函数\n// window.generateMockSpatData = () => {\n//   if (!trafficLightsMap || trafficLightsMap.size === 0) {\n//     console.error('红绿灯映射为空，无法生成模拟数据');\n//     return false;\n//   }\n  \n//   console.log('开始生成模拟SPAT数据...');\n  \n//   // 可能的相位ID和对应方向\n//   const phaseIds = [\n//     '1', '2', '3',  // 北进口\n//     '5', '6', '7',  // 东进口 \n//     '9', '10', '11', // 南进口\n//     '13', '14', '15' // 西进口\n//   ];\n  \n//   // 准备SPAT数据结构\n//   const spatMessage = {\n//     data: {\n//       intersections: []\n//     },\n//     tm: Date.now(),\n//     source: 2,\n//     type: 'SPAT',\n//     mac: 'mock-device-id'\n//   };\n  \n//   // 为每个红绿灯生成模拟数据\n//   trafficLightsMap.forEach((light, interId) => {\n//     // 为该路口生成3-6个随机相位\n//     const phaseCount = Math.floor(Math.random() * 4) + 3;\n//     const phases = [];\n    \n//     // 随机选择相位ID\n//     const selectedPhaseIds = [];\n//     while (selectedPhaseIds.length < phaseCount && selectedPhaseIds.length < phaseIds.length) {\n//       const randomIndex = Math.floor(Math.random() * phaseIds.length);\n//       const phaseId = phaseIds[randomIndex];\n      \n//       // 避免重复选择同一个ID\n//       if (!selectedPhaseIds.includes(phaseId)) {\n//         selectedPhaseIds.push(phaseId);\n//       }\n//     }\n    \n//     // 为每个选中的相位生成随机状态\n//     selectedPhaseIds.forEach(phaseId => {\n//       // 随机选择灯色: 0=红灯, 1=黄灯, 2=绿灯\n//       const lightIndex = Math.floor(Math.random() * 3);\n//       const stateLabels = ['red', 'yellow', 'green'];\n      \n//       // 随机生成剩余时间 (1-60秒)\n//       const remainTime = Math.floor(Math.random() * 60) + 1;\n      \n//       // 添加相位信息 - 符合实际数据结构\n//       phases.push({\n//         phaseId: phaseId,\n//         state: stateLabels[lightIndex],\n//         remainTime: remainTime\n//       });\n//     });\n    \n//     // 添加交叉口数据到SPAT消息\n//     spatMessage.data.intersections.push({\n//       id: interId,\n//       interId: interId, // 确保包含interId字段\n//       phases: phases\n//     });\n    \n//     // 同时更新本地状态方便测试\n//     const phaseInfos = phases.map(phase => ({\n//       phaseId: phase.phaseId,\n//       trafficLight: phase.state === 'green' ? 'G' : phase.state === 'yellow' ? 'Y' : 'R',\n//       remainTime: phase.remainTime,\n//       lastUpdate: Date.now()\n//     }));\n    \n//     // 使用与trafficLightsMap相同的ID类型存储状态信息\n//     trafficLightStates.set(interId, {\n//       updateTime: Date.now(),\n//       phases: phaseInfos\n//     });\n    \n//     console.log(`为路口 ${light.intersection?.name || interId} (ID类型: ${typeof interId}) 生成了 ${phases.length} 个相位的模拟数据`);\n//   });\n  \n//   // 模拟调用SPAT消息处理函数\n//   try {\n//     console.log('处理模拟SPAT消息:', spatMessage);\n//     const message = JSON.stringify(spatMessage);\n//     // 间接通过handleMqttMessage处理模拟数据\n//     handleMqttMessage(MQTT_CONFIG.spat, message);\n//   } catch (error) {\n//     console.error('处理模拟SPAT消息失败:', error);\n//   }\n  \n//   console.log('模拟SPAT数据生成完成');\n//   return true;\n// };\n\n// // 添加全局函数：生成模拟数据并显示红绿灯弹窗\n// window.testTrafficLightWithMockData = (interId) => {\n//   // 先生成模拟数据\n//   window.generateMockSpatData();\n  \n//   // 延迟100ms确保数据已生成\n//   setTimeout(() => {\n//     // 显示红绿灯弹窗\n//     window.showTrafficLightPopup(interId);\n//   }, 100);\n  \n//   return '模拟数据已生成，正在显示红绿灯弹窗...';\n// };\n\n// 帮助函数：从对象或其父对象中找到红绿灯对象\nconst getTrafficLightFromObject = (object) => {\n  let current = object;\n  \n  // 如果对象本身就有红绿灯数据，直接返回\n  if (current && current.userData && current.userData.type === 'trafficLight') {\n    console.log('直接找到红绿灯对象:', current.name || '无名称');\n    return current;\n  }\n  \n  // 向上查找父对象，直到找到红绿灯或到达顶层\n  while (current && current.parent) {\n    current = current.parent;\n    if (current.userData && current.userData.type === 'trafficLight') {\n      console.log('从父对象找到红绿灯:', current.name || '无名称');\n      return current;\n    }\n  }\n  \n  return null;\n};\n\n// 添加调试工具：强制进行点击测试\nwindow.testClickDetection = (x, y) => {\n  try {\n    console.log('执行强制点击测试 @ 位置:', x, y);\n    \n    // 找到渲染器的DOM元素\n    const canvas = document.querySelector('canvas');\n    if (!canvas) {\n      console.error('找不到THREE.js的canvas元素');\n      return false;\n    }\n    \n    // 确保scene和camera已定义\n    if (!scene || !cameraRef.current) {\n      console.error('scene或camera未定义');\n      return false;\n    }\n    \n    // 如果没有传入坐标，使用屏幕中心点\n    if (x === undefined || y === undefined) {\n      x = window.innerWidth / 2;\n      y = window.innerHeight / 2;\n    }\n    \n    // 计算归一化设备坐标 (-1 到 +1)\n    const rect = canvas.getBoundingClientRect();\n    const mouseX = ((x - rect.left) / canvas.clientWidth) * 2 - 1;\n    const mouseY = -((y - rect.top) / canvas.clientHeight) * 2 + 1;\n    \n    console.log('归一化坐标:', mouseX, mouseY);\n    \n    // 创建一个射线\n    const raycaster = new THREE.Raycaster();\n    raycaster.params.Points.threshold = 5;\n    raycaster.params.Line.threshold = 5;\n    \n    const mouseVector = new THREE.Vector2(mouseX, mouseY);\n    raycaster.setFromCamera(mouseVector, cameraRef.current);\n    \n    // 收集所有红绿灯对象\n    const trafficLightObjects = [];\n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        trafficLightObjects.push(lightObj.model);\n        console.log(`添加红绿灯 ${interId} 到检测列表`);\n      }\n    });\n    \n    // 直接对红绿灯对象进行碰撞检测\n    console.log(`开始检测 ${trafficLightObjects.length} 个红绿灯对象...`);\n    const tlIntersects = raycaster.intersectObjects(trafficLightObjects, true);\n    \n    if (tlIntersects.length > 0) {\n      console.log('成功点击到红绿灯对象!');\n      tlIntersects.forEach((intersect, i) => {\n        console.log(`结果 ${i}:`, intersect.object.name || '无名称', \n                    '距离:', intersect.distance,\n                    'position:', intersect.object.position.toArray(),\n                    'userData:', intersect.object.userData);\n        \n        // 尝试获取红绿灯ID\n        const obj = getTrafficLightFromObject(intersect.object);\n        if (obj && obj.userData && obj.userData.type === 'trafficLight') {\n          console.log('找到红绿灯ID:', obj.userData.interId);\n        }\n      });\n      \n      return true;\n    }\n    \n    // 对整个场景进行碰撞检测\n    console.log('对整个场景进行碰撞检测...');\n    const sceneIntersects = raycaster.intersectObjects(scene.children, true);\n    \n    console.log(`场景检测结果: ${sceneIntersects.length} 个物体`);\n    sceneIntersects.forEach((intersect, i) => {\n      const obj = intersect.object;\n      console.log(`场景物体 ${i}:`, obj.name || '无名称', \n                  '类型:', obj.type,\n                  '位置:', obj.position.toArray(),\n                  '距离:', intersect.distance,\n                  'userData:', obj.userData);\n    });\n    \n    // 测试红绿灯的可见性\n    console.log('检查红绿灯的可见性...');\n    let visibleCount = 0;\n    \n    trafficLightsMap.forEach((lightObj, interId) => {\n      if (lightObj.model) {\n        // 检查红绿灯模型是否可见\n        let isVisible = lightObj.model.visible;\n        let frustumVisible = true;\n        \n        // 获取世界位置\n        const worldPos = new THREE.Vector3();\n        lightObj.model.getWorldPosition(worldPos);\n        \n        // 计算到摄像机的距离\n        const distanceToCamera = worldPos.distanceTo(cameraRef.current.position);\n        \n        // 检查是否在视锥体内\n        const screenPos = worldPos.clone().project(cameraRef.current);\n        if (Math.abs(screenPos.x) > 1 || Math.abs(screenPos.y) > 1 || screenPos.z < -1 || screenPos.z > 1) {\n          frustumVisible = false;\n        }\n        \n        if (isVisible) {\n          visibleCount++;\n        }\n        \n        console.log(`红绿灯 ${interId}:`, {\n          名称: lightObj.intersection?.name || '未知',\n          可见性: isVisible,\n          在视锥体内: frustumVisible,\n          世界位置: worldPos.toArray(),\n          屏幕位置: [screenPos.x, screenPos.y, screenPos.z],\n          与摄像机距离: distanceToCamera\n        });\n      }\n    });\n    \n    console.log(`共有 ${visibleCount}/${trafficLightsMap.size} 个红绿灯是可见的`);\n    \n    // 如果未检测到任何交叉点\n    return sceneIntersects.length > 0;\n  } catch (error) {\n    console.error('点击测试失败:', error);\n    return false;\n  }\n};\n\n\n\n// 添加更新红绿灯视觉效果的函数\nconst updateTrafficLightVisual = (trafficLight, phaseInfo) => {\n  if (!trafficLight || !trafficLight.model || !phaseInfo) {\n    return;\n  }\n\n  // 移除旧的灯光模型(如果存在)\n  const lightsToRemove = [];\n  trafficLight.model.traverse(child => {\n    if (child.userData && child.userData.isLight) {\n      lightsToRemove.push(child);\n    }\n  });\n  \n  lightsToRemove.forEach(light => {\n    trafficLight.model.remove(light);\n  });\n\n  // 根据状态获取颜色\n  let lightColor;\n  switch(phaseInfo.trafficLight) {\n    case 'G':\n      lightColor = 0x00FF00; // 绿色\n      break;\n    case 'Y':\n      lightColor = 0xFFFF00; // 黄色\n      break;\n    case 'R':\n    default:\n      lightColor = 0xFF0000; // 红色\n      break;\n  }\n  \n  // 创建一个球体作为灯光\n  const lightGeometry = new THREE.SphereGeometry(3, 16, 16);\n  const lightMaterial = new THREE.MeshBasicMaterial({ \n    color: lightColor,\n    emissive: lightColor,\n    emissiveIntensity: 1\n  });\n  const lightMesh = new THREE.Mesh(lightGeometry, lightMaterial);\n  lightMesh.position.set(0, 12, 0); // 放在交通灯顶部\n  lightMesh.userData = {\n    isLight: true,\n    type: 'trafficLight',\n    interId: trafficLight.intersection?.interId,\n    phaseId: phaseInfo.phaseId,\n    direction: phaseInfo.direction,\n    remainTime: phaseInfo.remainTime\n  };\n  \n  // 添加光源使灯光更明显\n  const light = new THREE.PointLight(lightColor, 1, 50);\n  light.position.set(0, 12, 0);\n  light.userData = { isLight: true };\n  \n  // 将灯光添加到交通灯模型\n  trafficLight.model.add(lightMesh);\n  trafficLight.model.add(light);\n  \n  console.log(`更新路口 ${trafficLight.intersection?.name || trafficLight.intersection?.interId} 的红绿灯状态为: ${phaseInfo.trafficLight}, 剩余时间: ${phaseInfo.remainTime}秒`);\n};\n\nexport default CampusModel; \n\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,WAAW,KAAQ,OAAO,CACvE,MAAO,GAAK,CAAAC,KAAK,KAAM,OAAO,CAC9B,OAASC,UAAU,KAAQ,uCAAuC,CAClE,OAASC,aAAa,KAAQ,2CAA2C,CACzE,OAASC,mBAAmB,KAAQ,8BAA8B,CAClE,MAAO,GAAK,CAAAC,KAAK,KAAM,mBAAmB,CAC1C,MAAO,GAAK,CAAAC,aAAa,KAAM,2CAA2C,CAG1E,MAAO,CAAAC,IAAI,KAAM,MAAM,CACvB,OAASC,MAAM,CAAEC,OAAO,KAAQ,MAAM,CACtC,MAAO,CAAAC,iBAAiB,KAAM,4BAA4B,CAE1D;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,GAAI,CAAAC,gBAAgB,CAAG,IAAI,CAC3B,GAAI,CAAAC,gBAAgB,CAAG,EAAE,CACzB,GAAI,CAAAC,iBAAiB,CAAG,CAAC,CACzB,GAAI,CAAAC,oBAAoB,CAAG,IAAI,CAC/B,GAAI,CAAAC,cAAc,CAAG,IAAI,CAAG;AAC5B,GAAI,CAAAC,eAAe,CAAG,IAAI,CAAE;AAC5B,GAAI,CAAAC,QAAQ,CAAG,KAAK,CAAO;AAC3B,GAAI,CAAAC,UAAU,CAAG,QAAQ,CAAE;AAC3B,GAAI,CAAAC,QAAQ,CAAG,IAAI,CAAE;AAErB;AACA,GAAI,CAAAC,qBAAqB,CAAG,IAAI,CAChC,GAAI,CAAAC,qBAAqB,CAAG,IAAI,CAAG;AACnC,GAAI,CAAAC,oBAAoB,CAAG,IAAI,CAAI;AACnC,GAAI,CAAAC,0BAA0B,CAAG,IAAI,CAAE;AACvC,GAAI,CAAAC,KAAK,CAAG,IAAI,CAAE;AAElB,GAAI,CAAAC,eAAe,CAAE,IAAI,CAAE;AAC3B,GAAI,CAAAC,QAAQ,CAAE,IAAI,CAElB;AACA,GAAI,CAAAC,YAAY,CAAG,IAAI,CACvB,GAAI,CAAAC,YAAY,CAAG,IAAI,CACvB,KAAM,CAAAC,KAAK,CAAG,IAAI,CAAE;AAEpB;AACA,KAAM,CAAAC,oBAAoB,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAE;AACxC,KAAM,CAAAC,oBAAoB,CAAG,GAAI,CAAAD,GAAG,CAAC,CAAC,CAAE;AAExC;AACA,KAAM,CAAAE,WAAW,CAAG,CAClBC,MAAM,CAAEC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAChCC,IAAI,CAAE,IAAI,CACV;AACEC,GAAG,CAAE,2BAA2B,CAChCC,GAAG,CAAE,2BAA2B,CAChChB,KAAK,CAAE,6BAA6B,CACtCiB,GAAG,CAAE,2BAA2B,CAAG;AACnCC,IAAI,CAAE,4BAA8B;AACtC,CAAC,CAED;AACA,KAAM,CAAAC,QAAQ,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,uBAAuB,CACzEC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEJ,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC,CAE1D;AACA,KAAM,CAAAG,aAAa,CAAG,GAAI,CAAAlB,GAAG,CAAC,CAAC,CAAE;AAEjC;AACA,GAAI,CAAAmB,gBAAgB,CAAG,GAAI,CAAAnB,GAAG,CAAC,CAAC,CAAE;AAElC;AACA,GAAI,CAAAoB,gBAAgB,CAAG,IAAI,CAE3B;AACA,GAAI,CAAAC,gBAAgB,CAAG,GAAI,CAAArB,GAAG,CAAC,CAAC,CAAE;AAClC,GAAI,CAAAsB,kBAAkB,CAAG,GAAI,CAAAtB,GAAG,CAAC,CAAC,CAAE;AAEpC,KAAM,CAAAuB,KAAK,CAAG,GAAI,CAAA3D,KAAK,CAAC4D,KAAK,CAAC,CAAC,CAE/B;AACA,KAAM,CAAAC,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAGf,QAAQ,oBAAoB,CAAC,CAC7D,KAAM,CAAAgB,IAAI,CAAG,KAAM,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,EAAIA,IAAI,CAACE,QAAQ,EAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACE,QAAQ,CAAC,CAAE,CACzD,KAAM,CAAAG,WAAW,CAAGL,IAAI,CAACE,QAAQ,CAACI,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACC,aAAa,GAAK,IAAI,CAAC,CACrE,GAAIH,WAAW,EAAIA,WAAW,CAACI,KAAK,CAAE,CACpCjB,gBAAgB,CAAGa,WAAW,CAACI,KAAK,CACpCrB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEG,gBAAgB,CAAC,CAC7C,MAAO,CAAAA,gBAAgB,CACzB,CACF,CACAJ,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC,CAChCG,gBAAgB,CAAG,OAAO,CAAE;AAC5B,MAAO,CAAAA,gBAAgB,CACzB,CAAE,MAAOkB,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjClB,gBAAgB,CAAG,OAAO,CAAE;AAC5B,MAAO,CAAAA,gBAAgB,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAAmB,aAAa,CAAGA,CAACC,QAAQ,CAAEC,SAAS,CAAEC,KAAK,GAAK,CACpD,GAAID,SAAS,GAAK,IAAI,CAAE,MAAO,CAAAD,QAAQ,CACvC,MAAO,CAAAE,KAAK,CAAGF,QAAQ,CAAG,CAAC,CAAC,CAAGE,KAAK,EAAID,SAAS,CACnD,CAAC,CAED;AACA,KAAM,CAAAE,cAAc,CAAGA,CAACC,MAAM,CAAEC,SAAS,GAAK,CAC5C;AACA,GAAI,CAACA,SAAS,CAAE,CAChB,GAAI,CAACjD,YAAY,CAAE,CACjBA,YAAY,CAAGgD,MAAM,CAACE,KAAK,CAAC,CAAC,CAC7B,MAAO,CAAAF,MAAM,CACf,CAEA,KAAM,CAAAG,SAAS,CAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,CAAEpD,YAAY,CAACoD,CAAC,CAAElD,KAAK,CAAC,CAChE,KAAM,CAAAmD,SAAS,CAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,CAAEtD,YAAY,CAACsD,CAAC,CAAEpD,KAAK,CAAC,CAChE,KAAM,CAAAqD,SAAS,CAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,CAAExD,YAAY,CAACwD,CAAC,CAAEtD,KAAK,CAAC,CAEhEF,YAAY,CAACyD,GAAG,CAACN,SAAS,CAAEE,SAAS,CAAEE,SAAS,CAAC,CACjD,MAAO,CAAAvD,YAAY,CAACkD,KAAK,CAAC,CAAC,CAC3B,CAEA;AACA,GAAI,CAAC/C,oBAAoB,CAACuD,GAAG,CAACT,SAAS,CAAC,CAAE,CACxC9C,oBAAoB,CAACsD,GAAG,CAACR,SAAS,CAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CACnD,MAAO,CAAAF,MAAM,CACf,CAEA,KAAM,CAAAW,OAAO,CAAGxD,oBAAoB,CAACyD,GAAG,CAACX,SAAS,CAAC,CAEnD;AACA,KAAM,CAAAY,QAAQ,CAAGF,OAAO,CAACG,UAAU,CAACd,MAAM,CAAC,CAC3C,KAAM,CAAAe,sBAAsB,CAAG,EAAE,CAAE;AAEnC,GAAIF,QAAQ,CAAGE,sBAAsB,CAAE,CACrC3C,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAUY,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAClE7D,oBAAoB,CAACsD,GAAG,CAACR,SAAS,CAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CACnD,MAAO,CAAAF,MAAM,CACf,CAEA;AACA,KAAM,CAAAG,SAAS,CAAGR,aAAa,CAACK,MAAM,CAACI,CAAC,CAAEO,OAAO,CAACP,CAAC,CAAElD,KAAK,CAAC,CAC3D,KAAM,CAAAmD,SAAS,CAAGV,aAAa,CAACK,MAAM,CAACM,CAAC,CAAEK,OAAO,CAACL,CAAC,CAAEpD,KAAK,CAAC,CAC3D,KAAM,CAAAqD,SAAS,CAAGZ,aAAa,CAACK,MAAM,CAACQ,CAAC,CAAEG,OAAO,CAACH,CAAC,CAAEtD,KAAK,CAAC,CAE3D,KAAM,CAAA+D,WAAW,CAAG,GAAI,CAAAjG,KAAK,CAACkG,OAAO,CAACf,SAAS,CAAEE,SAAS,CAAEE,SAAS,CAAC,CACtEpD,oBAAoB,CAACsD,GAAG,CAACR,SAAS,CAAEgB,WAAW,CAACf,KAAK,CAAC,CAAC,CAAC,CAExD,MAAO,CAAAe,WAAW,CACpB,CAAC,CAED;AACA,KAAM,CAAAE,cAAc,CAAGA,CAACC,WAAW,CAAEnB,SAAS,GAAK,CACjD;AACA,GAAI,CAACA,SAAS,CAAE,CAChB,GAAIhD,YAAY,GAAK,IAAI,CAAE,CACzBA,YAAY,CAAGmE,WAAW,CAC1B,MAAO,CAAAA,WAAW,CACpB,CAEA;AACA,GAAI,CAAAC,IAAI,CAAGD,WAAW,CAAGnE,YAAY,CACrC,GAAIoE,IAAI,CAAGC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CACvC,GAAIF,IAAI,CAAG,CAACC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CAExC,KAAM,CAAAC,gBAAgB,CAAG7B,aAAa,CAAC1C,YAAY,CAAGoE,IAAI,CAAEpE,YAAY,CAAEC,KAAK,CAAC,CAChFD,YAAY,CAAGuE,gBAAgB,CAC7B,MAAO,CAAAA,gBAAgB,CACzB,CAEA;AACA,GAAI,CAACnE,oBAAoB,CAACqD,GAAG,CAACT,SAAS,CAAC,CAAE,CACxC5C,oBAAoB,CAACoD,GAAG,CAACR,SAAS,CAAEmB,WAAW,CAAC,CAChD,MAAO,CAAAA,WAAW,CACpB,CAEA,KAAM,CAAAK,OAAO,CAAGpE,oBAAoB,CAACuD,GAAG,CAACX,SAAS,CAAC,CAEnD;AACA,GAAI,CAAAoB,IAAI,CAAGD,WAAW,CAAGK,OAAO,CAChC,GAAIJ,IAAI,CAAGC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CACvC,GAAIF,IAAI,CAAG,CAACC,IAAI,CAACC,EAAE,CAAEF,IAAI,EAAI,CAAC,CAAGC,IAAI,CAACC,EAAE,CAExC;AACA,KAAM,CAAAG,mBAAmB,CAAGJ,IAAI,CAACC,EAAE,CAAG,CAAC,CAAE;AACzC,GAAID,IAAI,CAACK,GAAG,CAACN,IAAI,CAAC,CAAGK,mBAAmB,CAAE,CACxCtD,OAAO,CAACC,GAAG,CAAC,KAAK4B,SAAS,UAAU,CAACoB,IAAI,CAAG,GAAG,CAAGC,IAAI,CAACC,EAAE,EAAEP,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAChF3D,oBAAoB,CAACoD,GAAG,CAACR,SAAS,CAAEmB,WAAW,CAAC,CAChD,MAAO,CAAAA,WAAW,CACpB,CAEA,KAAM,CAAAI,gBAAgB,CAAG7B,aAAa,CAAC8B,OAAO,CAAGJ,IAAI,CAAEI,OAAO,CAAEvE,KAAK,CAAC,CACtEG,oBAAoB,CAACoD,GAAG,CAACR,SAAS,CAAEuB,gBAAgB,CAAC,CAErD,MAAO,CAAAA,gBAAgB,CACzB,CAAC,CAED;AACA;AACA;AACA,KAAM,CAAAI,6BAA6B,CAAG,CAAC,CAAE;AACzC;AAEA;AACA,KAAM,CAAAC,eAAe,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACjC,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAA3E,GAAG,CAAC,CAAC,CAE1B;AACA,KAAM,CAAA4E,eAAe,CAAG,CACtBC,MAAM,CAAE,GAAI,CAAAH,GAAG,CAAC,CAAC,CACjBI,KAAK,CAAE,GAAI,CAAA9E,GAAG,CAAC,CAAC,CAChB+E,OAAO,CAAE,GAAI,CAAA/E,GAAG,CAAC,CAAC,CAClBgF,MAAM,CAAE,GAAI,CAAAN,GAAG,CAAC,CAAC,CAEjBO,QAAQA,CAACC,KAAK,CAAEC,KAAK,CAAE,CACrB,IAAI,CAACN,MAAM,CAACO,GAAG,CAACF,KAAK,CAAC,CACtB,GAAIC,KAAK,CAAE,CACT,IAAI,CAACH,MAAM,CAACI,GAAG,CAACD,KAAK,CAAC,CACtB;AACAA,KAAK,CAACE,QAAQ,CAACC,MAAM,EAAI,CACvB,GAAIA,MAAM,CAACC,MAAM,CAAE,CACjB,IAAI,CAACT,KAAK,CAACzB,GAAG,CAACiC,MAAM,CAACE,IAAI,CAAEF,MAAM,CAAC,CACrC,CACF,CAAC,CAAC,CACJ,CACA,MAAO,CAAAJ,KAAK,CACd,CAAC,CAEDO,SAASA,CAACC,MAAM,CAAER,KAAK,CAAE,CACvB,GAAI,CAAC,IAAI,CAACH,OAAO,CAACzB,GAAG,CAAC4B,KAAK,CAAC,CAAE,CAC5B,IAAI,CAACH,OAAO,CAAC1B,GAAG,CAAC6B,KAAK,CAAE,GAAI,CAAAR,GAAG,CAAC,CAAC,CAAC,CACpC,CACA,IAAI,CAACK,OAAO,CAACvB,GAAG,CAAC0B,KAAK,CAAC,CAACE,GAAG,CAACM,MAAM,CAAC,CACnC,MAAO,CAAAA,MAAM,CACf,CAAC,CAEDC,WAAWA,CAACT,KAAK,CAAE,CACjB,GAAI,IAAI,CAACL,MAAM,CAACvB,GAAG,CAAC4B,KAAK,CAAC,CAAE,CAC1B,GAAI,CACF;AACA,GAAI,IAAI,CAACH,OAAO,CAACzB,GAAG,CAAC4B,KAAK,CAAC,CAAE,CAC3B,IAAI,CAACH,OAAO,CAACvB,GAAG,CAAC0B,KAAK,CAAC,CAACU,OAAO,CAACF,MAAM,EAAI,CACxC,GAAIA,MAAM,EAAI,MAAO,CAAAA,MAAM,CAACG,IAAI,GAAK,UAAU,CAAE,CAC/CH,MAAM,CAACG,IAAI,CAAC,CAAC,CACf,CACF,CAAC,CAAC,CACF,IAAI,CAACd,OAAO,CAACe,MAAM,CAACZ,KAAK,CAAC,CAC5B,CAEA;AACA,GAAI,MAAO,CAAAA,KAAK,CAACa,aAAa,GAAK,UAAU,CAAE,CAC7Cb,KAAK,CAACa,aAAa,CAAC,CAAC,CACvB,CAEA;AACA,KAAM,CAAAC,IAAI,CAAGd,KAAK,CAACe,OAAO,CAAC,CAAC,CAC5B,GAAID,IAAI,CAAE,CACR,IAAI,CAAChB,MAAM,CAACc,MAAM,CAACE,IAAI,CAAC,CACxBA,IAAI,CAACX,QAAQ,CAACC,MAAM,EAAI,CACtB,GAAIA,MAAM,EAAIA,MAAM,CAACC,MAAM,CAAE,CAC3B,IAAI,CAACT,KAAK,CAACgB,MAAM,CAACR,MAAM,CAACE,IAAI,CAAC,CAChC,CACA,GAAIF,MAAM,EAAIA,MAAM,CAACY,UAAU,CAAE,CAC/BZ,MAAM,CAACY,UAAU,CAACC,MAAM,CAAG,CAAC,CAC9B,CACF,CAAC,CAAC,CAEF;AACA,GAAI,CACF,GAAI,MAAO,CAAAjB,KAAK,CAACkB,WAAW,GAAK,UAAU,CAAE,CAC3ClB,KAAK,CAACkB,WAAW,CAACJ,IAAI,CAAC,CACzB,CAEA,GAAI,MAAO,CAAAd,KAAK,CAACmB,aAAa,GAAK,UAAU,CAAE,CAC7CnB,KAAK,CAACmB,aAAa,CAAC,IAAI,CAAEL,IAAI,CAAC,CACjC,CAEA;AACA,GAAI,MAAO,CAAAd,KAAK,CAACoB,WAAW,GAAK,UAAU,CAAE,CAC3C;AACA,KAAM,CAAAC,KAAK,CAAGrB,KAAK,CAACsB,QAAQ,CAACC,GAAG,CAACC,CAAC,EAAIA,CAAC,EAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CACnEN,KAAK,CAACX,OAAO,CAACkB,IAAI,EAAI,CACpB,GAAIA,IAAI,EAAIA,IAAI,CAACtB,IAAI,CAAE,CACrBN,KAAK,CAACoB,WAAW,CAACQ,IAAI,CAAC,CACzB,CACF,CAAC,CAAC,CACJ,CACF,CAAE,MAAOC,CAAC,CAAE,CACV/F,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAE8F,CAAC,CAAC,CACnC;AACF,CACF,CAEA,IAAI,CAAClC,MAAM,CAACiB,MAAM,CAACZ,KAAK,CAAC,CAC3B,CAAE,MAAO5C,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrC;AACA,IAAI,CAACuC,MAAM,CAACiB,MAAM,CAACZ,KAAK,CAAC,CAC3B,CACF,CACF,CAAC,CAED8B,OAAOA,CAAA,CAAG,CACR,GAAI,CACF;AACA,IAAI,CAACjC,OAAO,CAACa,OAAO,CAAC,CAACb,OAAO,CAAEG,KAAK,GAAK,CACvC,GAAI,CACFH,OAAO,CAACa,OAAO,CAACF,MAAM,EAAI,CACxB,GAAIA,MAAM,EAAI,MAAO,CAAAA,MAAM,CAACG,IAAI,GAAK,UAAU,CAAE,CAC/CH,MAAM,CAACG,IAAI,CAAC,CAAC,CACf,CACF,CAAC,CAAC,CACFd,OAAO,CAACkC,KAAK,CAAC,CAAC,CACjB,CAAE,MAAOF,CAAC,CAAE,CACV/F,OAAO,CAACC,GAAG,CAAC,eAAe,CAAE8F,CAAC,CAAC,CACjC,CACF,CAAC,CAAC,CACF,IAAI,CAAChC,OAAO,CAACkC,KAAK,CAAC,CAAC,CAEpB;AACA,IAAI,CAACpC,MAAM,CAACe,OAAO,CAACV,KAAK,EAAI,CAC3B,GAAI,CACF,GAAI,MAAO,CAAAA,KAAK,CAACa,aAAa,GAAK,UAAU,CAAE,CAC7Cb,KAAK,CAACa,aAAa,CAAC,CAAC,CACvB,CAEA,KAAM,CAAAC,IAAI,CAAGd,KAAK,CAACe,OAAO,CAAC,CAAC,CAC5B,GAAID,IAAI,CAAE,CACRA,IAAI,CAACX,QAAQ,CAACC,MAAM,EAAI,CACtB,GAAIA,MAAM,EAAIA,MAAM,CAACY,UAAU,CAAE,CAC/BZ,MAAM,CAACY,UAAU,CAACC,MAAM,CAAG,CAAC,CAC9B,CACF,CAAC,CAAC,CAEF;AACA,GAAI,MAAO,CAAAjB,KAAK,CAACkB,WAAW,GAAK,UAAU,CAAE,CAC3ClB,KAAK,CAACkB,WAAW,CAACJ,IAAI,CAAC,CACzB,CAEA,GAAI,MAAO,CAAAd,KAAK,CAACmB,aAAa,GAAK,UAAU,CAAE,CAC7CnB,KAAK,CAACmB,aAAa,CAAC,IAAI,CAAEL,IAAI,CAAC,CACjC,CAEA;AACA,GAAI,CACF,GAAId,KAAK,CAACsB,QAAQ,EAAIzE,KAAK,CAACC,OAAO,CAACkD,KAAK,CAACsB,QAAQ,CAAC,CAAE,CACnD,KAAM,CAAAD,KAAK,CAAGrB,KAAK,CAACsB,QAAQ,CAACC,GAAG,CAACC,CAAC,EAAIA,CAAC,EAAIA,CAAC,CAACC,KAAK,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CACnEN,KAAK,CAACX,OAAO,CAACkB,IAAI,EAAI,CACpB,GAAIA,IAAI,EAAIA,IAAI,CAACtB,IAAI,EAAI,MAAO,CAAAN,KAAK,CAACoB,WAAW,GAAK,UAAU,CAAE,CAChEpB,KAAK,CAACoB,WAAW,CAACQ,IAAI,CAAC,CACzB,CACF,CAAC,CAAC,CACJ,CACF,CAAE,MAAOC,CAAC,CAAE,CACV/F,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAE8F,CAAC,CAAC,CACnC,CACF,CACF,CAAE,MAAOA,CAAC,CAAE,CACV/F,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAE8F,CAAC,CAAC,CAClC,CACF,CAAC,CAAC,CACF,IAAI,CAAClC,MAAM,CAACoC,KAAK,CAAC,CAAC,CAEnB;AACA,IAAI,CAACnC,KAAK,CAACc,OAAO,CAACsB,IAAI,EAAI,CACzB,GAAIA,IAAI,CAACC,MAAM,CAAE,CACfD,IAAI,CAACC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAC,CAC1B,CACA,GAAIA,IAAI,CAACG,MAAM,CAAEH,IAAI,CAACG,MAAM,CAACC,QAAQ,CAAC,CAAC,CACvC,GAAIJ,IAAI,CAACK,WAAW,CAAEL,IAAI,CAACK,WAAW,CAACD,QAAQ,CAAC,CAAC,CACnD,CAAC,CAAC,CACF,IAAI,CAACxC,KAAK,CAACmC,KAAK,CAAC,CAAC,CAElB;AACA,IAAI,CAACjC,MAAM,CAACY,OAAO,CAACT,KAAK,EAAI,CAC3B,GAAIA,KAAK,CAACgC,MAAM,CAAE,CAChBhC,KAAK,CAACgC,MAAM,CAACC,MAAM,CAACjC,KAAK,CAAC,CAC5B,CACAA,KAAK,CAACE,QAAQ,CAACC,MAAM,EAAI,CACvB,GAAIA,MAAM,CAACkC,MAAM,CAAE,CACjB,GAAIlC,MAAM,CAACmC,QAAQ,CAAE,CACnBnC,MAAM,CAACmC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAC3B,CACA,GAAIpC,MAAM,CAACqC,QAAQ,CAAE,CACnB,GAAI5F,KAAK,CAACC,OAAO,CAACsD,MAAM,CAACqC,QAAQ,CAAC,CAAE,CAClCrC,MAAM,CAACqC,QAAQ,CAAC/B,OAAO,CAAC+B,QAAQ,EAAI,CAClC,GAAIA,QAAQ,CAAClB,GAAG,CAAEkB,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC,CACxCC,QAAQ,CAACD,OAAO,CAAC,CAAC,CACpB,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,GAAIpC,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAAEnB,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC,CACtDpC,MAAM,CAACqC,QAAQ,CAACD,OAAO,CAAC,CAAC,CAC3B,CACF,CACF,CACA,GAAIpC,MAAM,CAACY,UAAU,CAAE,CACrBZ,MAAM,CAACY,UAAU,CAACC,MAAM,CAAG,CAAC,CAC9B,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CACF,IAAI,CAACnB,MAAM,CAACiC,KAAK,CAAC,CAAC,CACrB,CAAE,MAAO3E,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACpC;AACA,IAAI,CAACyC,OAAO,CAACkC,KAAK,CAAC,CAAC,CACpB,IAAI,CAACpC,MAAM,CAACoC,KAAK,CAAC,CAAC,CACnB,IAAI,CAACnC,KAAK,CAACmC,KAAK,CAAC,CAAC,CAClB,IAAI,CAACjC,MAAM,CAACiC,KAAK,CAAC,CAAC,CACrB,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAW,oBAAoB,CAAIzC,KAAK,EAAK,CACtC,KAAM,CAAAD,KAAK,CAAG,GAAI,CAAAtH,KAAK,CAACiK,cAAc,CAAC1C,KAAK,CAAC,CAC7C,MAAO,CAAAP,eAAe,CAACK,QAAQ,CAACC,KAAK,CAAEC,KAAK,CAAC,CAC/C,CAAC,CAED;AACA,KAAM,CAAA2C,YAAY,CAAGA,CAAChB,IAAI,CAAE5B,KAAK,CAAEC,KAAK,GAAK,CAC3C,KAAM,CAAAO,MAAM,CAAGR,KAAK,CAAC6C,UAAU,CAACjB,IAAI,CAAE3B,KAAK,CAAC,CAC5C,MAAO,CAAAP,eAAe,CAACa,SAAS,CAACC,MAAM,CAAER,KAAK,CAAC,CACjD,CAAC,CAED,KAAM,CAAA8C,WAAW,CAAGC,IAAA,EAAqD,IAApD,CAAEC,SAAS,CAAEC,kBAAkB,CAAEC,YAAa,CAAC,CAAAH,IAAA,CAClE,KAAM,CAAAI,YAAY,CAAG5K,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAA6K,UAAU,CAAG7K,MAAM,CAAC,IAAI,CAAC,CAC/B,KAAM,CAAA8K,SAAS,CAAG9K,MAAM,CAAC,GAAI,CAAAM,mBAAmB,CAAC,CAAC,CAAC,CACnD,KAAM,CAAAyK,aAAa,CAAG/K,MAAM,CAAC,EAAE,CAAC,CAChC,KAAM,CAAAgL,eAAe,CAAGhL,MAAM,CAAC,CAAC,CAAC,CACjC,KAAM,CAAAiL,aAAa,CAAGjL,MAAM,CAAC,IAAI,CAAC,CAClC,KAAM,CAAAkL,iBAAiB,CAAGlL,MAAM,CAAC,IAAI,CAAC,CACtC,KAAM,CAAAmL,MAAM,CAAGnL,MAAM,CAAC,IAAI,CAAC,CAE3B;AACA,KAAM,CAAAoL,kBAAkB,CAAGpL,MAAM,CAAC,IAAI,CAAC,CACvC,KAAM,CAAAqL,gBAAgB,CAAGrL,MAAM,CAAC,IAAI,CAAC,CACrC,KAAM,CAAAsL,eAAe,CAAG,IAAI,CAAE;AAE9B;AACA,KAAM,CAAAC,oBAAoB,CAAGvL,MAAM,CAACwL,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAC/C,KAAM,CAAAC,qBAAqB,CAAG,GAAI,CAAAnJ,GAAG,CAAC,CAAC,CAAE;AACzC,KAAM,CAAAoJ,gBAAgB,CAAG3L,MAAM,CAAC,EAAE,CAAC,CAAE;AAKrC;AACA,KAAM,CAAC4L,YAAY,CAAEC,eAAe,CAAC,CAAG5L,QAAQ,CAAC,CAC/C6L,SAAS,CAAE,CAAC,CACZC,QAAQ,CAAE,CAAC,CACXC,KAAK,CAAE,CAAC,CACRC,OAAO,CAAE,CACX,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGlM,QAAQ,CAAC,QAAQ,CAAC,CAElD;AACA,KAAM,CAAAmM,oBAAoB,CAAG,CAC3BC,QAAQ,CAAE,OAAO,CACjBC,MAAM,CAAE,MAAM,CACdC,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,kBAAkB,CAC7BC,MAAM,CAAE,IAAI,CAAG;AACfC,OAAO,CAAE,MAAM,CACfC,GAAG,CAAE,MACP,CAAC,CAED,KAAM,CAAAC,WAAW,CAAG,CAClBC,OAAO,CAAE,UAAU,CACnBC,eAAe,CAAE,0BAA0B,CAC3CC,MAAM,CAAE,gBAAgB,CACxBC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,SAAS,CACjBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,2BAA2B,CACtCC,UAAU,CAAE,eACd,CAAC,CAED;AACA,KAAM,CAAAC,SAAS,CAAGrN,MAAM,CAAC,IAAI,CAAC,CAE9B;AACA,KAAM,CAACsN,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGtN,QAAQ,CAAC,IAAI,CAAC,CAEtE;AACA,KAAM,CAACuN,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGxN,QAAQ,CAAC,CAC7DyN,OAAO,CAAE,KAAK,CACdC,OAAO,CAAE,IAAI,CACbtB,QAAQ,CAAE,CAAE9G,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CACxBmI,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,EACV,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,mBAAmB,CAAG9N,MAAM,CAAC,IAAI,CAAC,CAExC;AACA,KAAM,CAAA+N,0BAA0B,CAAG/N,MAAM,CAAC,IAAI,CAAC,CAE/C;AACA2C,MAAM,CAACqL,uBAAuB,CAAGP,sBAAsB,CAEvD;AACA9K,MAAM,CAACmL,mBAAmB,CAAGA,mBAAmB,CAChDnL,MAAM,CAACoL,0BAA0B,CAAGA,0BAA0B,CAE9D;AACA,KAAM,CAAAE,uBAAuB,CAAG,CAC9B5B,QAAQ,CAAE,OAAO,CACjB6B,GAAG,CAAE,MAAM,CAAG;AACd3B,IAAI,CAAE,KAAK,CACXC,SAAS,CAAE,kBAAkB,CAC7B2B,KAAK,CAAE,OAAO,CAAG;AACjB1B,MAAM,CAAE,IAAI,CACZK,eAAe,CAAE,OAAO,CACxBE,YAAY,CAAE,KAAK,CACnBG,SAAS,CAAE,2BACb,CAAC,CAED;AACA,KAAM,CAAAiB,UAAU,CAAG,CACjB/B,QAAQ,CAAE,OAAO,CACjB6B,GAAG,CAAE,MAAM,CACX3B,IAAI,CAAE,kBAAkB,CAAG;AAC3BC,SAAS,CAAE,mBAAmB,CAC9BK,OAAO,CAAE,OAAO,CAAG;AACnBwB,UAAU,CAAE,MAAM,CAClBC,KAAK,CAAE,MAAM,CACbpB,QAAQ,CAAE,MAAM,CAChBqB,UAAU,CAAE,MAAM,CAClBC,UAAU,CAAE,2BAA2B,CACvC/B,MAAM,CAAE,IACV,CAAC,CAED;AACA,KAAM,CAAC7I,gBAAgB,CAAC,CAAG3D,QAAQ,CAAC,GAAI,CAAAsC,GAAG,CAAC,CAAC,CAAC,CAE9C;AACA,KAAM,CAACkM,UAAU,CAAEC,aAAa,CAAC,CAAGzO,QAAQ,CAAC,IAAI,CAAC,CAElD;AACA,KAAM,CAACyD,gBAAgB,CAAEiL,mBAAmB,CAAC,CAAG1O,QAAQ,CAAC,GAAI,CAAAsC,GAAG,CAAC,CAAC,CAAC,CAEnE;AACA,KAAM,CAACqM,WAAW,CAAEC,cAAc,CAAC,CAAG5O,QAAQ,CAAC,CAAE6O,KAAK,CAAE,EAAE,CAAElB,OAAO,CAAE,EAAG,CAAC,CAAC,CAE1E;AACA,KAAM,CAAAmB,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAIrN,UAAU,GAAK,QAAQ,CAAE,CAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC,CACtB9B,UAAU,CAAG,QAAQ,CAErB;AACA0J,kBAAkB,CAAC4D,OAAO,CAAG,IAAI,CACjC3D,gBAAgB,CAAC2D,OAAO,CAAG,IAAI,CAE/B,GAAIrN,QAAQ,CAAE,CACZA,QAAQ,CAACsN,OAAO,CAAG,KAAK,CAC1B,CACF,CACF,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,GAAIxN,UAAU,GAAK,QAAQ,CAAE,CAC3B6B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC,CACtB9B,UAAU,CAAG,QAAQ,CAErB;AACA0J,kBAAkB,CAAC4D,OAAO,CAAG,IAAI,CACjC3D,gBAAgB,CAAC2D,OAAO,CAAG,IAAI,CAE/B,GAAI3B,SAAS,CAAC2B,OAAO,EAAIrN,QAAQ,CAAE,CACjC;AACA;AACA0L,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACzG,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CACzC,KAAM,CAAAuJ,UAAU,CAAG9B,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAChH,KAAK,CAAC,CAAC,CACrD,KAAM,CAAA+J,SAAS,CAAG/B,SAAS,CAAC2B,OAAO,CAACK,EAAE,CAAChK,KAAK,CAAC,CAAC,CAE9C;AACA,GAAI,CAAA9E,KAAK,CAAC+O,KAAK,CAACH,UAAU,CAAC,CACxBI,EAAE,CAAC,CAAEhK,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,GAAG,CAAEE,CAAC,CAAE,CAAE,CAAC,CAAE,IAAI,CAAC,CAChC6J,MAAM,CAACjP,KAAK,CAACkP,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,IAAM,CACdvC,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACwD,IAAI,CAACV,UAAU,CAAC,CAC7C,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC,CAEV;AACA,GAAI,CAAAvP,KAAK,CAAC+O,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC,CAAEhK,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CAAE,IAAI,CAAC,CAC9B6J,MAAM,CAACjP,KAAK,CAACkP,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,IAAM,CACdvC,SAAS,CAAC2B,OAAO,CAACK,EAAE,CAACQ,IAAI,CAACT,SAAS,CAAC,CACtC,CAAC,CAAC,CACDU,KAAK,CAAC,CAAC,CAEV;AACAnO,QAAQ,CAACoO,MAAM,CAACnK,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5B,KAAM,CAAAoK,aAAa,CAAGrO,QAAQ,CAACoO,MAAM,CAAC1K,KAAK,CAAC,CAAC,CAE7C;AACA,GAAI,CAAA9E,KAAK,CAAC+O,KAAK,CAACU,aAAa,CAAC,CAC3BT,EAAE,CAAC,CAAEhK,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAC,CAAEE,CAAC,CAAE,CAAE,CAAC,CAAE,IAAI,CAAC,CAC9B6J,MAAM,CAACjP,KAAK,CAACkP,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,IAAM,CACdjO,QAAQ,CAACoO,MAAM,CAACF,IAAI,CAACG,aAAa,CAAC,CACnC;AACA3C,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAACtO,QAAQ,CAACoO,MAAM,CAAC,CACzCpO,QAAQ,CAACuO,MAAM,CAAC,CAAC,CACnB,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC,CAEV;AACAnO,QAAQ,CAACsN,OAAO,CAAG,IAAI,CAEvB;AACAtN,QAAQ,CAACwO,WAAW,CAAG,EAAE,CACzBxO,QAAQ,CAACyO,WAAW,CAAG,GAAG,CAC1BzO,QAAQ,CAAC0O,aAAa,CAAG5J,IAAI,CAACC,EAAE,CAAG,GAAG,CACtC/E,QAAQ,CAAC2O,aAAa,CAAG,CAAC,CAC1B3O,QAAQ,CAACuO,MAAM,CAAC,CAAC,CACjB;AACA7C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC,CAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC,CAEzCjN,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE,CACrBiN,MAAM,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CACnBC,KAAK,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAChBC,KAAK,CAAE,IACT,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAC,wBAAwB,CAAIC,KAAK,EAAK,CAC1C,KAAM,CAAAC,YAAY,CAAGlQ,iBAAiB,CAACmQ,aAAa,CAACtM,IAAI,CAACuM,CAAC,EAAIA,CAAC,CAACC,IAAI,GAAKJ,KAAK,CAAC,CAChF,GAAIC,YAAY,EAAIzD,SAAS,CAAC2B,OAAO,EAAIrN,QAAQ,CAAE,CACjD4L,uBAAuB,CAACuD,YAAY,CAAC,CAErC;AACA,KAAM,CAAAI,WAAW,CAAGpG,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAChDC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,CAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC,CAEDxI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAE,CACvB6N,IAAI,CAAEP,YAAY,CAACG,IAAI,CACvBK,GAAG,CAAE,CACHxF,SAAS,CAAEgF,YAAY,CAAChF,SAAS,CACjCC,QAAQ,CAAE+E,YAAY,CAAC/E,QACzB,CAAC,CACDwF,IAAI,CAAEL,WACR,CAAC,CAAC,CAEF;AACAxP,UAAU,CAAG,cAAc,CAC3ByK,WAAW,CAAC,cAAc,CAAC,CAE3B;AACAkB,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACzG,GAAG,CAACsL,WAAW,CAAC3L,CAAC,CAAC,EAAE,CAAE,EAAE,CAAE,CAAC2L,WAAW,CAACzL,CAAC,CAAC,EAAE,CAAC,CAEvE;AACA9D,QAAQ,CAACoO,MAAM,CAACnK,GAAG,CAACsL,WAAW,CAAC3L,CAAC,CAAE,CAAC,CAAE,CAAC2L,WAAW,CAACzL,CAAC,CAAC,CAErD;AACA4H,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAACtO,QAAQ,CAACoO,MAAM,CAAC,CAEzC;AACApO,QAAQ,CAACsN,OAAO,CAAG,IAAI,CACvBtN,QAAQ,CAACuO,MAAM,CAAC,CAAC,CAEjB;AACA7C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC,CAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC,CAEzCjN,OAAO,CAACC,GAAG,CAAC,aAAa,CAAE,CACzB6N,IAAI,CAAEP,YAAY,CAACG,IAAI,CACvBO,IAAI,CAAEnE,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACoF,OAAO,CAAC,CAAC,CAC1CC,GAAG,CAAE/P,QAAQ,CAACoO,MAAM,CAAC0B,OAAO,CAAC,CAAC,CAC9BF,IAAI,CAAEL,WACR,CAAC,CAAC,CAEF;AACA,GAAIJ,YAAY,CAACa,eAAe,GAAK,KAAK,EAAIb,YAAY,CAACnD,OAAO,CAAE,CAClEpK,OAAO,CAACC,GAAG,CAAC,MAAMsN,YAAY,CAACG,IAAI,iBAAiB,CAAC,CAErD;AACAW,UAAU,CAAC,IAAM,CACf;AACA,GAAI,CAAAjE,OAAO,CAAGmD,YAAY,CAACnD,OAAO,CAElC;AACA,GAAIhL,MAAM,CAACkP,qBAAqB,CAAE,CAChClP,MAAM,CAACkP,qBAAqB,CAAClE,OAAO,CAAC,CACrCpK,OAAO,CAACC,GAAG,CAAC,SAASsN,YAAY,CAACG,IAAI,SAAStD,OAAO,YAAY,CAAC,CACrE,CAAC,IAAM,CACLpK,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC,CAChC,CACF,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,IAAM,CACLtB,OAAO,CAACC,GAAG,CAAC,MAAMsN,YAAY,CAACG,IAAI,sBAAsB,CAAC,CAE1D;AACA,GAAItO,MAAM,CAACqL,uBAAuB,CAAE,CAClCrL,MAAM,CAACqL,uBAAuB,CAAC,CAC7BN,OAAO,CAAE,KACX,CAAC,CAAC,CACJ,CACF,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAoE,iBAAiB,CAAGA,CAAChD,KAAK,CAAEiD,OAAO,GAAK,CAC5C,GAAI,CACF,KAAM,CAAAC,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC,CAEnC;AACA,GAAIjD,KAAK,GAAKrM,WAAW,CAACO,GAAG,CAAE,KAAAmP,aAAA,CAC7B;AAEA;AACA,KAAM,CAAAC,SAAS,CAAGJ,OAAO,CAACK,GAAG,CAC7B,KAAM,CAAAC,gBAAgB,CAAGN,OAAO,CAACO,EAAE,CAEnC;AACA,KAAM,CAAAC,aAAa,CAAG9O,gBAAgB,CAACqC,GAAG,CAACqM,SAAS,CAAC,CAErD;AACA,GAAII,aAAa,EAAIF,gBAAgB,CAAGE,aAAa,CAAE,CACrD;AACA;AACA;AACA;AACA;AACA,OACF,CAEA;AACA9O,gBAAgB,CAACkC,GAAG,CAACwM,SAAS,CAAEE,gBAAgB,CAAC,CAEjD;AACA;AACA;AACA;AACA;AAEA,KAAM,CAAAG,YAAY,CAAG,EAAAN,aAAA,CAAAH,OAAO,CAAC7N,IAAI,UAAAgO,aAAA,iBAAZA,aAAA,CAAcM,YAAY,GAAI,EAAE,CACrD,KAAM,CAAAC,KAAK,CAAGV,OAAO,CAAC7N,IAAI,CAACuO,KAAK,CAEhC;AACA,KAAM,CAAAjH,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CAEtB;AACAgH,YAAY,CAACtK,OAAO,CAACwK,WAAW,EAAI,CAClC;AACA;AACA,KAAM,CAAAC,EAAE,CAAIR,SAAS,CAAGO,WAAW,CAACE,SAAS,CAC7C,KAAM,CAAAC,IAAI,CAAGH,WAAW,CAACI,WAAW,CAEpC,GAAGD,IAAI,GAAK,GAAG,EAAEA,IAAI,GAAK,GAAG,EAAEA,IAAI,GAAK,GAAG,CAAC,CAC5C;AACA;AACE;AACA,KAAM,CAAAE,KAAK,CAAG,CACZlH,SAAS,CAAEsF,UAAU,CAACuB,WAAW,CAACM,WAAW,CAAC,CAC9ClH,QAAQ,CAAEqF,UAAU,CAACuB,WAAW,CAACO,UAAU,CAAC,CAC5ClH,KAAK,CAAEoF,UAAU,CAACuB,WAAW,CAACQ,SAAS,CAAC,CACxClH,OAAO,CAAEmF,UAAU,CAACuB,WAAW,CAACS,WAAW,CAC7C,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGvI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAAC6B,KAAK,CAAClH,SAAS,CAAEkH,KAAK,CAACjH,QAAQ,CAAC,CAEhF;AACA,GAAI,CAAAuH,cAAc,CAClB,OAAQR,IAAI,EACV,IAAK,GAAG,CAAE;AACRQ,cAAc,CAAG1R,qBAAqB,CACtC,MACF,IAAK,GAAG,CAAE;AACR0R,cAAc,CAAGzR,qBAAqB,CACtC,MACF,IAAK,GAAG,CAAE;AACRyR,cAAc,CAAGxR,oBAAoB,CACrC,MACF,QACE,OAAQ;AACZ,CAEA;AACF,GAAI,CAAA4F,KAAK,CAAGjE,aAAa,CAACsC,GAAG,CAAC6M,EAAE,CAAC,CAEjC,GAAI,CAAClL,KAAK,EAAI4L,cAAc,CAAE,CAC1B;AACA,KAAM,CAAAC,QAAQ,CAAGT,IAAI,GAAK,GAAG,CAAGtS,aAAa,CAAC6E,KAAK,CAACvD,oBAAoB,CAAC,CAAGwR,cAAc,CAACjO,KAAK,CAAC,CAAC,CAClG;AACA,KAAM,CAAAmO,MAAM,CAAGV,IAAI,GAAK,GAAG,CAAG,GAAG,CAAG,GAAG,CACvCS,QAAQ,CAAClH,QAAQ,CAACzG,GAAG,CAACyN,QAAQ,CAAC9N,CAAC,CAAEiO,MAAM,CAAE,CAACH,QAAQ,CAAC5N,CAAC,CAAC,CACtD8N,QAAQ,CAACE,QAAQ,CAAChO,CAAC,CAAGgB,IAAI,CAACC,EAAE,CAAGsM,KAAK,CAAC/G,OAAO,CAAGxF,IAAI,CAACC,EAAE,CAAG,GAAG,CAE7D;AACA,GAAIoM,IAAI,GAAK,GAAG,CAAE,CAClB;AACES,QAAQ,CAACG,KAAK,CAAC9N,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAE3B;AACA,KAAM,CAAA6B,KAAK,CAAG0C,oBAAoB,CAACoJ,QAAQ,CAAC,CAE5C,GAAItR,eAAe,EAAIA,eAAe,CAACwG,UAAU,EAAIxG,eAAe,CAACwG,UAAU,CAACC,MAAM,CAAG,CAAC,CAAE,CAC1F;AACA,KAAM,CAAAT,MAAM,CAAGoC,YAAY,CAACpI,eAAe,CAACwG,UAAU,CAAC,CAAC,CAAC,CAAEhB,KAAK,CAAE8L,QAAQ,CAAC,CAC3EtL,MAAM,CAAC0L,IAAI,CAAC,CAAC,CACf,CAEA;AACAjI,qBAAqB,CAAC9F,GAAG,CAACgN,EAAE,CAAEnL,KAAK,CAAC,CACtC,CAEAzF,KAAK,CAAC2F,GAAG,CAAC4L,QAAQ,CAAC,CAEnB9P,aAAa,CAACmC,GAAG,CAACgN,EAAE,CAAE,CACpBlL,KAAK,CAAE6L,QAAQ,CACfK,UAAU,CAAEnI,GAAG,CACjBqH,IAAI,CAAEA,IACN,CAAC,CAAC,CACN,CAAC,IAAM,IAAIpL,KAAK,CAAE,CACd;AACFA,KAAK,CAACA,KAAK,CAAC2E,QAAQ,CAACzG,GAAG,CAACyN,QAAQ,CAAC9N,CAAC,CAAEmC,KAAK,CAACoL,IAAI,GAAK,GAAG,CAAG,GAAG,CAAG,GAAG,CAAE,CAACO,QAAQ,CAAC5N,CAAC,CAAC,CACjFiC,KAAK,CAACA,KAAK,CAAC+L,QAAQ,CAAChO,CAAC,CAAGgB,IAAI,CAACC,EAAE,CAAGsM,KAAK,CAAC/G,OAAO,CAAGxF,IAAI,CAACC,EAAE,CAAG,GAAG,CAChEgB,KAAK,CAACkM,UAAU,CAAGnI,GAAG,CACtB/D,KAAK,CAACA,KAAK,CAAC6I,YAAY,CAAC,CAAC,CAC1B7I,KAAK,CAACA,KAAK,CAAC8I,iBAAiB,CAAC,IAAI,CAAC,CACnC,CACF,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAqD,iBAAiB,CAAG,IAAI,CAC9B,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAA7M,GAAG,CAACwL,YAAY,CAACzJ,GAAG,CAAC+K,CAAC,EAAI3B,SAAS,CAAG2B,CAAC,CAAClB,SAAS,CAAC,CAAC,CAE1EpP,aAAa,CAAC0E,OAAO,CAAC,CAAC6L,SAAS,CAAEpB,EAAE,GAAK,CACvC,GAAInH,GAAG,CAAGuI,SAAS,CAACJ,UAAU,CAAGC,iBAAiB,EAAI,CAACC,UAAU,CAACjO,GAAG,CAAC+M,EAAE,CAAC,CAAE,CACzE;AACA,GAAIoB,SAAS,CAAClB,IAAI,GAAK,GAAG,EAAIpH,qBAAqB,CAAC7F,GAAG,CAAC+M,EAAE,CAAC,CAAE,CAC3D,KAAM,CAAAnL,KAAK,CAAGiE,qBAAqB,CAAC3F,GAAG,CAAC6M,EAAE,CAAC,CAC3C;AACAzL,eAAe,CAACe,WAAW,CAACT,KAAK,CAAC,CAClCiE,qBAAqB,CAACrD,MAAM,CAACuK,EAAE,CAAC,CAClC,CAEA;AACA5Q,KAAK,CAAC2H,MAAM,CAACqK,SAAS,CAACtM,KAAK,CAAC,CAC7BjE,aAAa,CAAC4E,MAAM,CAACuK,EAAE,CAAC,CAC1B,CACF,CAAC,CAAC,CACF,OACF,CAEA;AACA,GAAI9D,KAAK,GAAKrM,WAAW,CAACM,GAAG,CAAE,CAC7B;AAEA,KAAM,CAAAkR,OAAO,CAAGjC,OAAO,CAAC7N,IAAI,CAC5B,KAAM,CAAA+P,KAAK,CAAGD,OAAO,CAACrP,KAAK,CAC3B,KAAM,CAAAuP,QAAQ,CAAG,CACfrI,SAAS,CAAEsF,UAAU,CAAC6C,OAAO,CAACG,QAAQ,CAAC,CACvCrI,QAAQ,CAAEqF,UAAU,CAAC6C,OAAO,CAACI,OAAO,CAAC,CACrCrI,KAAK,CAAEoF,UAAU,CAAC6C,OAAO,CAACd,SAAS,CAAC,CACpClH,OAAO,CAAEmF,UAAU,CAAC6C,OAAO,CAACb,WAAW,CACzC,CAAC,CAED;AACA;AAEA;AACAzQ,MAAM,CAAC2R,WAAW,CAAC,CACjBxB,IAAI,CAAE,iBAAiB,CACvByB,MAAM,CAAE,aACV,CAAC,CAAE,GAAG,CAAC,CAEP;AACA5R,MAAM,CAAC2R,WAAW,CAAC,CACjBxB,IAAI,CAAE,KAAK,CACXlO,KAAK,CAAEsP,KAAK,CAAE;AACd/P,IAAI,CAAE,CAAQ;AACZS,KAAK,CAAEsP,KAAK,CACZf,SAAS,CAAEc,OAAO,CAACd,SAAS,CAC5BkB,OAAO,CAAEJ,OAAO,CAACI,OAAO,CACxBD,QAAQ,CAAEH,OAAO,CAACG,QAAQ,CAC1BhB,WAAW,CAAEa,OAAO,CAACb,WACvB,CACF,CAAC,CAAE,GAAG,CAAC,CAEP;AACA,KAAM,CAAAC,QAAQ,CAAGvI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAACgD,QAAQ,CAACrI,SAAS,CAAEqI,QAAQ,CAACpI,QAAQ,CAAC,CACtF,KAAM,CAAAyI,eAAe,CAAG,GAAI,CAAArU,KAAK,CAACkG,OAAO,CAACgN,QAAQ,CAAC9N,CAAC,CAAE,GAAG,CAAE,CAAC8N,QAAQ,CAAC5N,CAAC,CAAC,CACvE,KAAM,CAAAgP,eAAe,CAAGhO,IAAI,CAACC,EAAE,CAAGyN,QAAQ,CAAClI,OAAO,CAAGxF,IAAI,CAACC,EAAE,CAAG,GAAG,CAElE;AACA,KAAM,CAAAgO,WAAW,CAAGxP,cAAc,CAACsP,eAAe,CAAEN,KAAK,CAAC,CAC1D,KAAM,CAAA3N,WAAW,CAAGD,cAAc,CAACmO,eAAe,CAAEP,KAAK,CAAC,CAE1D;AACA,GAAI,CAAAS,UAAU,CAAGlR,aAAa,CAACsC,GAAG,CAACmO,KAAK,CAAC,CAEzC;AACA,KAAM,CAAAvP,aAAa,CAAGuP,KAAK,GAAKvQ,gBAAgB,CAEhD,GAAI,CAACgR,UAAU,EAAI/S,qBAAqB,CAAE,CACxC;AACA,KAAM,CAAAgT,eAAe,CAAGhT,qBAAqB,CAACyD,KAAK,CAAC,CAAC,CAErD;AACA;AACAuP,eAAe,CAACvI,QAAQ,CAACzG,GAAG,CAAC8O,WAAW,CAACnP,CAAC,CAAE,CAAC,CAAC,CAAEmP,WAAW,CAAC/O,CAAC,CAAC,CAC9DiP,eAAe,CAACnB,QAAQ,CAAChO,CAAC,CAAGc,WAAW,CAExC;AACAqO,eAAe,CAAChN,QAAQ,CAAEiN,KAAK,EAAK,CAClC,GAAIA,KAAK,CAAC9K,MAAM,EAAI8K,KAAK,CAAC3K,QAAQ,CAAE,CAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA,KAAM,CAAA4K,WAAW,CAAGD,KAAK,CAAC3K,QAAQ,CAAC7E,KAAK,CAAC,CAAC,CAC1CwP,KAAK,CAAC3K,QAAQ,CAAG4K,WAAW,CAE5B;AACA,GAAInQ,aAAa,CAAE,CACjBmQ,WAAW,CAACxG,KAAK,CAAC1I,GAAG,CAAC,QAAQ,CAAC,CACjC,CAAC,IAAM,CACLkP,WAAW,CAACxG,KAAK,CAAC1I,GAAG,CAAC,QAAQ,CAAC,CACjC,CACAkP,WAAW,CAACC,QAAQ,CAAG,GAAI,CAAA5U,KAAK,CAAC6U,KAAK,CAAC,QAAQ,CAAC,CAChDF,WAAW,CAACG,WAAW,CAAG,IAAI,CAC9B;AACAH,WAAW,CAACI,WAAW,CAAG,IAAI,CAChC,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,UAAU,CAAGC,gBAAgB,CAAC,GAAG3O,IAAI,CAAC4O,KAAK,CAAClB,QAAQ,CAACnI,KAAK,CAAC,OAAO,CAAE,CACxEc,eAAe,CAAEnI,aAAa,CAC5B,CAAE2Q,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEvM,CAAC,CAAE,GAAI,CAAC,CAAG;AACnC,CAAEqM,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,EAAE,CAAEvM,CAAC,CAAE,GAAI,CAAC,CAAG;AACrCwM,SAAS,CAAE,CAAEH,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEvM,CAAC,CAAE,GAAI,CAAC,CAAE;AAC/CiE,QAAQ,CAAE,EAAE,CACZL,OAAO,CAAE,CACX,CAAC,CAAC,CACFsI,UAAU,CAAC9I,QAAQ,CAACzG,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAE;AAClCuP,UAAU,CAACO,WAAW,CAAG,IAAI,CAAE;AAC/BP,UAAU,CAACjL,QAAQ,CAACyL,OAAO,CAAG,GAAG,CAAE;AACnCf,eAAe,CAACjN,GAAG,CAACwN,UAAU,CAAC,CAE/BnT,KAAK,CAAC2F,GAAG,CAACiN,eAAe,CAAC,CAE1B;AACAnR,aAAa,CAACmC,GAAG,CAACsO,KAAK,CAAE,CACvBxM,KAAK,CAAEkN,eAAe,CACtBhB,UAAU,CAAEpI,IAAI,CAACC,GAAG,CAAC,CAAC,CACtBqH,IAAI,CAAE,GAAG,CAAE;AACX8C,MAAM,CAAEjR,aAAa,CACrBwQ,UAAU,CAAEA,UAAW;AACzB,CAAC,CAAC,CAEF;AAEA;AACA,GAAI,CAAA5U,KAAK,CAAC+O,KAAK,CAACsF,eAAe,CAACvI,QAAQ,CAAC,CACtCkD,EAAE,CAAC,CAAE9J,CAAC,CAAE,GAAI,CAAC,CAAE,GAAG,CAAC,CACnB+J,MAAM,CAACjP,KAAK,CAACkP,MAAM,CAACC,SAAS,CAACmG,GAAG,CAAC,CAClC/F,KAAK,CAAC,CAAC,CAEV;AACA8E,eAAe,CAAChN,QAAQ,CAAEiN,KAAK,EAAK,CAClC,GAAIA,KAAK,CAAC9K,MAAM,EAAI8K,KAAK,CAAC3K,QAAQ,EAAI2K,KAAK,CAAC3K,QAAQ,CAAC+K,WAAW,CAAE,CAChE,GAAI,CAAA1U,KAAK,CAAC+O,KAAK,CAAC,CAAEqG,OAAO,CAAE,GAAI,CAAC,CAAC,CAC9BpG,EAAE,CAAC,CAAEoG,OAAO,CAAE,GAAI,CAAC,CAAE,GAAG,CAAC,CACzBnG,MAAM,CAACjP,KAAK,CAACkP,MAAM,CAACC,SAAS,CAACmG,GAAG,CAAC,CAClCjG,QAAQ,CAAC,UAAW,CACnBiF,KAAK,CAAC3K,QAAQ,CAACyL,OAAO,CAAG,IAAI,CAACA,OAAO,CACrCd,KAAK,CAAC3K,QAAQ,CAACgL,WAAW,CAAG,IAAI,CACnC,CAAC,CAAC,CACDpF,KAAK,CAAC,CAAC,CACZ,CACF,CAAC,CAAC,CAEF;AACA,GAAI,CAAAvP,KAAK,CAAC+O,KAAK,CAAC,CAAEqG,OAAO,CAAE,GAAI,CAAC,CAAC,CAC9BpG,EAAE,CAAC,CAAEoG,OAAO,CAAE,GAAI,CAAC,CAAE,GAAG,CAAC,CACzBnG,MAAM,CAACjP,KAAK,CAACkP,MAAM,CAACC,SAAS,CAACmG,GAAG,CAAC,CAClCjG,QAAQ,CAAC,UAAW,CACnBuF,UAAU,CAACjL,QAAQ,CAACyL,OAAO,CAAG,IAAI,CAACA,OAAO,CAC1CR,UAAU,CAACjL,QAAQ,CAACgL,WAAW,CAAG,IAAI,CACxC,CAAC,CAAC,CACDpF,KAAK,CAAC,CAAC,CAEV;AACA,GAAInL,aAAa,CAAE,CACjBxD,gBAAgB,CAAGyT,eAAe,CAClC/I,eAAe,CAACsI,QAAQ,CAAC,CACzB5Q,OAAO,CAACC,GAAG,CAAC,WAAW,CAAE0Q,KAAK,CAAC,CACjC,CACF,CAAC,IAAM,IAAIS,UAAU,CAAE,CACrB;AACA,KAAM,CAAAmB,gBAAgB,CAAG5Q,cAAc,CAACwP,WAAW,CAAER,KAAK,CAAC,CAC3D,KAAM,CAAAvN,gBAAgB,CAAGL,cAAc,CAACC,WAAW,CAAE2N,KAAK,CAAC,CAE3D;AACAS,UAAU,CAACjN,KAAK,CAAC2E,QAAQ,CAACwD,IAAI,CAACiG,gBAAgB,CAAC,CAChDnB,UAAU,CAACjN,KAAK,CAAC+L,QAAQ,CAAChO,CAAC,CAAGkB,gBAAgB,CAC9CgO,UAAU,CAACjN,KAAK,CAAC6I,YAAY,CAAC,CAAC,CAC/BoE,UAAU,CAACjN,KAAK,CAAC8I,iBAAiB,CAAC,IAAI,CAAC,CACxCmE,UAAU,CAACf,UAAU,CAAGpI,IAAI,CAACC,GAAG,CAAC,CAAC,CAClCkJ,UAAU,CAACiB,MAAM,CAAGjR,aAAa,CAAE;AAEnC;AACA,GAAIgQ,UAAU,CAACQ,UAAU,CAAE,CACzBR,UAAU,CAACQ,UAAU,CAACjL,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC,CAC5C0K,UAAU,CAACjN,KAAK,CAACiC,MAAM,CAACgL,UAAU,CAACQ,UAAU,CAAC,CAChD,CAEA;AACA,KAAM,CAAAA,UAAU,CAAGC,gBAAgB,CAAC,GAAG3O,IAAI,CAAC4O,KAAK,CAAClB,QAAQ,CAACnI,KAAK,CAAG,GAAG,CAAC,OAAO,CAAE,CAC9Ec,eAAe,CAAEnI,aAAa,CAC5B,CAAE2Q,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEvM,CAAC,CAAE,GAAI,CAAC,CAAG;AACnC,CAAEqM,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,EAAE,CAAEC,CAAC,CAAE,EAAE,CAAEvM,CAAC,CAAE,GAAI,CAAC,CAAG;AACrCwM,SAAS,CAAE,CAAEH,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEvM,CAAC,CAAE,GAAI,CAAC,CAAE;AAC/CiE,QAAQ,CAAE,EAAE,CACZL,OAAO,CAAE,CACX,CAAC,CAAC,CACFsI,UAAU,CAAC9I,QAAQ,CAACzG,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAAE;AACnCuP,UAAU,CAACO,WAAW,CAAG,IAAI,CAAE;AAC/Bf,UAAU,CAACjN,KAAK,CAACC,GAAG,CAACwN,UAAU,CAAC,CAChCR,UAAU,CAACQ,UAAU,CAAGA,UAAU,CAElC;AAEA;AACA,GAAIxQ,aAAa,CAAE,CACjBxD,gBAAgB,CAAGwT,UAAU,CAACjN,KAAK,CACnCmE,eAAe,CAACsI,QAAQ,CAAC,CAC3B,CACF,CAEA;AACA,KAAM,CAAA1I,GAAG,CAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,CACtB,KAAM,CAAAoI,iBAAiB,CAAG,IAAI,CAAE;AAEhCpQ,aAAa,CAAC0E,OAAO,CAAC,CAAC6L,SAAS,CAAEpB,EAAE,GAAK,CACvC,KAAM,CAAAmD,mBAAmB,CAAGtK,GAAG,CAAGuI,SAAS,CAACJ,UAAU,CAEtD;AACA,GAAImC,mBAAmB,CAAGlC,iBAAiB,CAAG,GAAG,EAAIkC,mBAAmB,EAAIlC,iBAAiB,CAAE,CAC7F;AACA,KAAM,CAAA8B,OAAO,CAAG,CAAC,CAEjB3B,SAAS,CAACtM,KAAK,CAACE,QAAQ,CAAEiN,KAAK,EAAK,CAClC,GAAIA,KAAK,CAAC9K,MAAM,EAAI8K,KAAK,CAAC3K,QAAQ,CAAE,CAClC;AACA,GAAI2K,KAAK,CAAC3K,QAAQ,CAAC+K,WAAW,GAAKe,SAAS,CAAE,CAC5CnB,KAAK,CAAC3K,QAAQ,CAAC+L,mBAAmB,CAAGpB,KAAK,CAAC3K,QAAQ,CAAC+K,WAAW,EAAI,KAAK,CACxEJ,KAAK,CAAC3K,QAAQ,CAACgM,eAAe,CAAGrB,KAAK,CAAC3K,QAAQ,CAACyL,OAAO,EAAI,GAAG,CAChE,CAEA;AACAd,KAAK,CAAC3K,QAAQ,CAAC+K,WAAW,CAAG,IAAI,CACjCJ,KAAK,CAAC3K,QAAQ,CAACyL,OAAO,CAAGA,OAAO,CAChCd,KAAK,CAAC3K,QAAQ,CAACgL,WAAW,CAAG,IAAI,CACnC,CACF,CAAC,CAAC,CAEF;AACA,GAAIlB,SAAS,CAACmB,UAAU,CAAE,CACxBnB,SAAS,CAACmB,UAAU,CAACjL,QAAQ,CAACyL,OAAO,CAAGA,OAAO,CAC/C3B,SAAS,CAACmB,UAAU,CAACjL,QAAQ,CAACgL,WAAW,CAAG,IAAI,CAClD,CACF,CACA;AAAA,IACK,IAAIa,mBAAmB,CAAGlC,iBAAiB,CAAE,CAChD;AACA,GAAIG,SAAS,CAACmB,UAAU,CAAE,CACxBnB,SAAS,CAACmB,UAAU,CAACjL,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC,CAC3C+J,SAAS,CAACmB,UAAU,CAACjL,QAAQ,CAACD,OAAO,CAAC,CAAC,CACvC+J,SAAS,CAACtM,KAAK,CAACiC,MAAM,CAACqK,SAAS,CAACmB,UAAU,CAAC,CAC9C,CAEAnB,SAAS,CAACtM,KAAK,CAACE,QAAQ,CAAEiN,KAAK,EAAK,CAClC,GAAIA,KAAK,CAAC9K,MAAM,CAAE,CAChB,GAAI8K,KAAK,CAAC3K,QAAQ,CAAE,CAClB,GAAI5F,KAAK,CAACC,OAAO,CAACsQ,KAAK,CAAC3K,QAAQ,CAAC,CAAE,CACjC2K,KAAK,CAAC3K,QAAQ,CAAC/B,OAAO,CAACgO,CAAC,EAAIA,CAAC,CAAClM,OAAO,CAAC,CAAC,CAAC,CAC1C,CAAC,IAAM,CACL4K,KAAK,CAAC3K,QAAQ,CAACD,OAAO,CAAC,CAAC,CAC1B,CACF,CACA,GAAI4K,KAAK,CAAC7K,QAAQ,CAAE6K,KAAK,CAAC7K,QAAQ,CAACC,OAAO,CAAC,CAAC,CAC9C,CACF,CAAC,CAAC,CAEF;AACAjI,KAAK,CAAC2H,MAAM,CAACqK,SAAS,CAACtM,KAAK,CAAC,CAC7BjE,aAAa,CAAC4E,MAAM,CAACuK,EAAE,CAAC,CACxB;AACAtQ,oBAAoB,CAAC+F,MAAM,CAACuK,EAAE,CAAC,CAC/BpQ,oBAAoB,CAAC6F,MAAM,CAACuK,EAAE,CAAC,CAE/BrP,OAAO,CAACC,GAAG,CAAC,mBAAmBoP,EAAE,EAAE,CAAC,CACtC,CACF,CAAC,CAAC,CAEF,OACF,CAEA;AACA,GAAI9D,KAAK,GAAKrM,WAAW,CAACS,IAAI,CAAE,CAC9B;AAEA,GAAI,CACF,KAAM,CAAA8O,OAAO,CAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC,CAEnC;AACA,KAAM,CAAAK,SAAS,CAAGJ,OAAO,CAACK,GAAG,CAC7B,KAAM,CAAAC,gBAAgB,CAAGN,OAAO,CAACO,EAAE,CAEnC;AACA,KAAM,CAAAC,aAAa,CAAG9O,gBAAgB,CAACqC,GAAG,CAACqM,SAAS,CAAC,CAErD;AACA,GAAII,aAAa,EAAIF,gBAAgB,CAAGE,aAAa,CAAE,CACrD;AACA;AACA;AACA;AACA;AACA,OACF,CAEA;AACA9O,gBAAgB,CAACkC,GAAG,CAACwM,SAAS,CAAEE,gBAAgB,CAAC,CAEjD;AACA,GAAIN,OAAO,CAAC7N,IAAI,EAAI6N,OAAO,CAAC7N,IAAI,CAAC4M,aAAa,EAAIzM,KAAK,CAACC,OAAO,CAACyN,OAAO,CAAC7N,IAAI,CAAC4M,aAAa,CAAC,CAAE,CAC3FiB,OAAO,CAAC7N,IAAI,CAAC4M,aAAa,CAAC5I,OAAO,CAAC2I,YAAY,EAAI,CACjD,KAAM,CAAAnD,OAAO,CAAGmD,YAAY,CAACnD,OAAO,CAEpC,GAAI,CAACA,OAAO,CAAE,CACZpK,OAAO,CAACsB,KAAK,CAAC,kBAAkB,CAAEiM,YAAY,CAAC,CAC/C,OACF,CAEA;AAEA;AACA,GAAIA,YAAY,CAACjD,MAAM,EAAIvJ,KAAK,CAACC,OAAO,CAACuM,YAAY,CAACjD,MAAM,CAAC,CAAE,CAC7D;AACA,KAAM,CAAAuI,UAAU,CAAG,EAAE,CAErBtF,YAAY,CAACjD,MAAM,CAAC1F,OAAO,CAACkO,KAAK,EAAI,CACnC;AACA,GAAI,CAACA,KAAK,CAACC,OAAO,CAAE,CAClB/S,OAAO,CAACsB,KAAK,CAAC,gBAAgB,CAAEwR,KAAK,CAAC,CACtC,OACF,CAEA,KAAM,CAAAC,OAAO,CAAGD,KAAK,CAACC,OAAO,CAACC,QAAQ,CAAC,CAAC,CACxC;AACA,KAAM,CAAAC,SAAS,CAAGH,KAAK,CAACI,YAAY,CAClCC,oBAAoB,CAACL,KAAK,CAACI,YAAY,CAAC,CACxCE,iBAAiB,CAACL,OAAO,CAAC,CAE5B;AACA,KAAM,CAAAM,UAAU,CAAGP,KAAK,CAACQ,YAAY,EAAI,GAAG,CAAE;AAC9C,KAAM,CAAAC,UAAU,CAAGC,QAAQ,CAACV,KAAK,CAACS,UAAU,CAAC,EAAI,CAAC,CAElD;AAEA;AACA,KAAM,CAAAE,SAAS,CAAG,CAChBV,OAAO,CACPE,SAAS,CACTK,YAAY,CAAED,UAAU,CACxBE,UACF,CAAC,CAED;AACAV,UAAU,CAACa,IAAI,CAACD,SAAS,CAAC,CAE1B;AACA;AACA,GAAI,CAAAE,eAAe,CAAGC,MAAM,CAACxJ,OAAO,CAAC,CACrC,GAAI,CAAAyJ,iBAAiB,CAAGxT,gBAAgB,CAACmC,GAAG,CAACmR,eAAe,CAAC,CAE7D,GAAI,CAACE,iBAAiB,CAAE,CACtB;AACAF,eAAe,CAAGH,QAAQ,CAACpJ,OAAO,CAAC,CACnCyJ,iBAAiB,CAAGxT,gBAAgB,CAACmC,GAAG,CAACmR,eAAe,CAAC,CAC3D,CAEA,GAAIE,iBAAiB,CAAE,CACrB;AACAC,wBAAwB,CAACD,iBAAiB,CAAEJ,SAAS,CAAC,CAEtD;AACA,GAAI1J,oBAAoB,EAAIA,oBAAoB,CAACK,OAAO,GAAKA,OAAO,CAAE,CACpEF,sBAAsB,CAAC6J,IAAI,GAAK,CAC9B,GAAGA,IAAI,CACP5J,OAAO,CAAE,IAAI,CACb4I,OAAO,CACPE,SAAS,CACTxD,KAAK,CAAE4D,UAAU,CACjBE,UACF,CAAC,CAAC,CAAC,CACL,CACF,CAAC,IAAM,CACL;AAAA,CAEJ,CAAC,CAAC,CAEF;AACA,GAAI,CAAAS,QAAQ,CAAG,IAAI,CACnB;AACA,KAAM,CAAAC,KAAK,CAAGL,MAAM,CAACxJ,OAAO,CAAC,CAC7B,GAAI/J,gBAAgB,CAACiC,GAAG,CAAC2R,KAAK,CAAC,CAAE,CAC/BD,QAAQ,CAAGC,KAAK,CAClB,CAAC,IAAM,CACL;AACA,KAAM,CAAAC,KAAK,CAAGV,QAAQ,CAACpJ,OAAO,CAAC,CAC/B,GAAI/J,gBAAgB,CAACiC,GAAG,CAAC4R,KAAK,CAAC,CAAE,CAC/BF,QAAQ,CAAGE,KAAK,CAClB,CACF,CAEA,GAAIF,QAAQ,GAAK,IAAI,CAAE,CACrB;AACA1T,kBAAkB,CAAC+B,GAAG,CAAC2R,QAAQ,CAAE,CAC/BG,UAAU,CAAElM,IAAI,CAACC,GAAG,CAAC,CAAC,CACtBoC,MAAM,CAAEuI,UACV,CAAC,CAAC,CACF7S,OAAO,CAACC,GAAG,CAAC,aAAa+T,QAAQ,KAAK,MAAO,CAAAA,QAAQ,aAAa,CAAC,CAEnE;AACA,GAAI5U,MAAM,CAACmL,mBAAmB,GAC1BnL,MAAM,CAACmL,mBAAmB,CAACkB,OAAO,GAAKuI,QAAQ,EAC/C5U,MAAM,CAACmL,mBAAmB,CAACkB,OAAO,GAAKmI,MAAM,CAACI,QAAQ,CAAC,EACvD5U,MAAM,CAACmL,mBAAmB,CAACkB,OAAO,GAAK+H,QAAQ,CAACQ,QAAQ,CAAC,CAAC,CAAE,CAE9DhU,OAAO,CAACC,GAAG,CAAC,eAAe+T,QAAQ,aAAa,CAAC,CACjD;AACA5U,MAAM,CAACmL,mBAAmB,CAACkB,OAAO,CAAGuI,QAAQ,CAE7C;AACA,GAAI5U,MAAM,CAACoL,0BAA0B,EAAI,CAACpL,MAAM,CAACoL,0BAA0B,CAACiB,OAAO,CAAE,CACnFzL,OAAO,CAACC,GAAG,CAAC,SAAS+T,QAAQ,aAAa,CAAC,CAC3C3F,UAAU,CAAC,IAAM,CACfjP,MAAM,CAACkP,qBAAqB,CAAC0F,QAAQ,CAAC,CACxC,CAAC,CAAE,GAAG,CAAC,CACT,CACF,CACF,CAAC,IAAM,CACL;AACA1T,kBAAkB,CAAC+B,GAAG,CAAC+H,OAAO,CAAE,CAC9B+J,UAAU,CAAElM,IAAI,CAACC,GAAG,CAAC,CAAC,CACtBoC,MAAM,CAAEuI,UACV,CAAC,CAAC,CACF;AACF,CACF,CAAC,IAAM,CACL7S,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAEiM,YAAY,CAAC,CAC9C,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACLvN,OAAO,CAACsB,KAAK,CAAC,oCAAoC,CAAEmN,OAAO,CAAC,CAC9D,CACF,CAAE,MAAOnN,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAEkN,OAAO,CAAC,CAC9C,CACA,OACF,CAEA;AACA,GAAIjD,KAAK,GAAKrM,WAAW,CAACQ,GAAG,EAAI+O,OAAO,CAACc,IAAI,GAAK,KAAK,CAAE,CACvD;AAEA;AACAnQ,MAAM,CAAC2R,WAAW,CAAC,CACjBxB,IAAI,CAAE,KAAK,CACX3O,IAAI,CAAE6N,OAAO,CAAC7N,IAChB,CAAC,CAAE,GAAG,CAAC,CAEP,KAAM,CAAAwT,OAAO,CAAG3F,OAAO,CAAC7N,IAAI,CAC5B,KAAM,CAAAyT,KAAK,CAAGD,OAAO,CAACC,KAAK,CAC3B,KAAM,CAAAC,MAAM,CAAGF,OAAO,CAACG,IAAI,EAAI,EAAE,CAEjCD,MAAM,CAAC1P,OAAO,CAAC4P,KAAK,EAAI,CACtB,KAAM,CAAAC,OAAO,CAAGD,KAAK,CAACE,KAAK,CAC3B,KAAM,CAAAC,SAAS,CAAGH,KAAK,CAACG,SAAS,CACjC,KAAM,CAAAC,WAAW,CAAGJ,KAAK,CAACI,WAAW,CACrC,KAAM,CAAAC,SAAS,CAAGL,KAAK,CAACK,SAAS,CACjC,KAAM,CAAAC,OAAO,CAAGN,KAAK,CAACM,OAAO,CAE7B;AACA,KAAM,CAAAhF,QAAQ,CAAGvI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAC7CC,UAAU,CAACuG,OAAO,CAACW,OAAO,CAAC,CAC3BlH,UAAU,CAACuG,OAAO,CAACY,MAAM,CAC3B,CAAC,CAED;AACA,GAAI,CAAAC,WAAW,CAAG,EAAE,CACpB,GAAI,CAAAC,YAAY,CAAG,EAAE,CAErB,OAAOP,SAAS,EACd,IAAK,KAAK,CAAG;AACXM,WAAW,CAAG,OAAO,CACrBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,OAAO,CACrBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,QAAQ,CACtBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,MAAM,CAAE;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,IAAK,KAAK,CAAG;AACXD,WAAW,CAAG,MAAM,CACpBC,YAAY,CAAG,SAAS,CACxB,MACF,QACED,WAAW,CAAGL,WAAW,EAAI,MAAM,CACnCM,YAAY,CAAG,SAAS,CAC5B,CAEA;AACAC,iBAAiB,CAACrF,QAAQ,CAAEmF,WAAW,CAAEC,YAAY,CAAC,CAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,CAAC,CAAC,CAEF,OACF,CAEA;AACA,GAAI3J,KAAK,GAAKrM,WAAW,CAACT,KAAK,EAAIgQ,OAAO,CAACc,IAAI,GAAK,OAAO,CAAE,CAC3D;AAEA,KAAM,CAAA6F,SAAS,CAAG3G,OAAO,CAAC7N,IAAI,CAC9B,KAAM,CAAAyU,OAAO,CAAGD,SAAS,CAACC,OAAO,CACjC,KAAM,CAAAC,SAAS,CAAGF,SAAS,CAACE,SAAS,CACrC,KAAM,CAAAC,SAAS,CAAGH,SAAS,CAACG,SAAS,CACrC,KAAM,CAAAzM,QAAQ,CAAG,CACfN,QAAQ,CAAEqF,UAAU,CAACuH,SAAS,CAACtE,OAAO,CAAC,CACvCvI,SAAS,CAAEsF,UAAU,CAACuH,SAAS,CAACvE,QAAQ,CAC1C,CAAC,CAED;AACA,KAAM,CAAAf,QAAQ,CAAGvI,SAAS,CAACkE,OAAO,CAACmC,YAAY,CAAC9E,QAAQ,CAACP,SAAS,CAAEO,QAAQ,CAACN,QAAQ,CAAC,CAEtF;AACA,OAAO8M,SAAS,EACd,IAAK,GAAG,CAAG;AACTH,iBAAiB,CAACrF,QAAQ,CAAE,UAAU,CAAE,SAAS,CAAC,CAClD,MACF,IAAK,KAAK,CAAG;AACXqF,iBAAiB,CAACrF,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,KAAK,CAAG;AACXqF,iBAAiB,CAACrF,QAAQ,CAAE,OAAO,CAAE,SAAS,CAAC,CAC/C,MACF,IAAK,IAAI,CAAG;AACV,KAAM,CAAA0F,UAAU,CAAGJ,SAAS,CAACK,UAAU,CAAG;AAC1CN,iBAAiB,CAACrF,QAAQ,CAAE,KAAK0F,UAAU,MAAM,CAAE,SAAS,CAAC,CAC7D,MACF,IAAK,IAAI,CAAG;AACVL,iBAAiB,CAACrF,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,IAAI,CAAG;AACVqF,iBAAiB,CAACrF,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,MAAM,CAAG;AACZqF,iBAAiB,CAACrF,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,IAAI,CAAG;AACVqF,iBAAiB,CAACrF,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,IAAI,CAAG;AACVqF,iBAAiB,CAACrF,QAAQ,CAAE,MAAM,CAAE,SAAS,CAAC,CAC9C,MACF,IAAK,KAAK,CAAG;AACX,KAAM,CAAA4F,YAAY,CAAGN,SAAS,CAACK,UAAU,CAAG;AAC5C,KAAM,CAAAE,QAAQ,CAAGP,SAAS,CAACQ,UAAU,CAAO;AAC5CT,iBAAiB,CAACrF,QAAQ,CAAE,QAAQ+F,mBAAmB,CAACH,YAAY,CAAC,GAAGC,QAAQ,GAAG,CAAE,SAAS,CAAC,CAC/F,MACJ,CAEA,OACF,CACA;AACA;AACA;AACA;AACA;AACA;AAEF,CAAE,MAAOrU,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnCtB,OAAO,CAACsB,KAAK,CAAC,SAAS,CAAEkN,OAAO,CAAC,CACnC,CACF,CAAC,CAED;AACA,KAAM,CAAAsH,cAAc,CAAGA,CAAA,GAAM,CAC3B9V,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAE7B,KAAM,CAAA8V,KAAK,CAAG,QAAQ7W,WAAW,CAACC,MAAM,IAAID,WAAW,CAACK,IAAI,OAAO,CACnES,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAE8V,KAAK,CAAC,CAEpC;AACA,KAAM,CAAAC,EAAE,CAAG,GAAI,CAAAC,SAAS,CAACF,KAAK,CAAC,CAE/BC,EAAE,CAACE,MAAM,CAAG,IAAM,CAChBlW,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC9B,CAAC,CAED+V,EAAE,CAACG,SAAS,CAAI3B,KAAK,EAAK,CACxB,GAAI,CACF,KAAM,CAAAhG,OAAO,CAAGE,IAAI,CAACC,KAAK,CAAC6F,KAAK,CAAC5T,IAAI,CAAC,CAEtC;AACA,GAAI4N,OAAO,CAACe,IAAI,GAAK,SAAS,CAAE,CAC9BvP,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEuO,OAAO,CAAC,CAC/B,OACF,CAEA;AACA,GAAIA,OAAO,CAACe,IAAI,GAAK,MAAM,CAAE,CAC3B,OACF,CAEA;AACA,GAAIf,OAAO,CAACe,IAAI,GAAK,SAAS,EAAIf,OAAO,CAACjD,KAAK,EAAIiD,OAAO,CAACC,OAAO,CAAE,CAClE;AACAF,iBAAiB,CAACC,OAAO,CAACjD,KAAK,CAAEmD,IAAI,CAAC0H,SAAS,CAAC5H,OAAO,CAACC,OAAO,CAAC,CAAC,CACnE,CACF,CAAE,MAAOnN,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CAC1C,CACF,CAAC,CAED0U,EAAE,CAACK,OAAO,CAAI/U,KAAK,EAAK,CACtBtB,OAAO,CAACsB,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACtC,CAAC,CAED0U,EAAE,CAACM,OAAO,CAAG,IAAM,CACjBtW,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC5B;AACAoO,UAAU,CAACyH,cAAc,CAAE,IAAI,CAAC,CAClC,CAAC,CAED;AACApO,aAAa,CAAC+D,OAAO,CAAGuK,EAAE,CAC5B,CAAC,CAEDxZ,SAAS,CAAC,IAAM,CACd,GAAI,CAAC6K,YAAY,CAACoE,OAAO,CAAE,OAE3B;AACA8K,aAAa,CAAC,CAAC,CAEf;AACA9X,KAAK,CAAG,GAAI,CAAA7B,KAAK,CAAC4Z,KAAK,CAAC,CAAC,CAAE;AAE3B;AACA,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAA7Z,KAAK,CAAC8Z,iBAAiB,CACxC,EAAE,CACFtX,MAAM,CAACuX,UAAU,CAAGvX,MAAM,CAACwX,WAAW,CACtC,GAAG,CACH,IACF,CAAC,CACD;AACAH,MAAM,CAAC3N,QAAQ,CAACzG,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAAE;AAChCoU,MAAM,CAAC/J,MAAM,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACtB5C,SAAS,CAAC2B,OAAO,CAAGgL,MAAM,CAE1B;AACA,KAAM,CAAAI,QAAQ,CAAG,GAAI,CAAAja,KAAK,CAACka,aAAa,CAAC,CAAEC,SAAS,CAAE,IAAK,CAAC,CAAC,CAC7DF,QAAQ,CAACG,OAAO,CAAC5X,MAAM,CAACuX,UAAU,CAAEvX,MAAM,CAACwX,WAAW,CAAC,CACvDC,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC,CAChCJ,QAAQ,CAACK,aAAa,CAAC9X,MAAM,CAAC+X,gBAAgB,CAAC,CAC/C9P,YAAY,CAACoE,OAAO,CAAC2L,WAAW,CAACP,QAAQ,CAACQ,UAAU,CAAC,CAErD;AACA;AACA,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAA1a,KAAK,CAAC2a,YAAY,CAAC,QAAQ,CAAE,GAAG,CAAC,CAAE;AAC5D9Y,KAAK,CAAC2F,GAAG,CAACkT,YAAY,CAAC,CAEvB;AACA,KAAM,CAAAE,iBAAiB,CAAG,GAAI,CAAA5a,KAAK,CAAC6a,gBAAgB,CAAC,QAAQ,CAAE,GAAG,CAAC,CAAE;AACrED,iBAAiB,CAAC1O,QAAQ,CAACzG,GAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAC1C5D,KAAK,CAAC2F,GAAG,CAACoT,iBAAiB,CAAC,CAE5B;AACA,KAAM,CAAAE,iBAAiB,CAAG,GAAI,CAAA9a,KAAK,CAAC6a,gBAAgB,CAAC,QAAQ,CAAE,GAAG,CAAC,CACnEC,iBAAiB,CAAC5O,QAAQ,CAACzG,GAAG,CAAC,CAAC,EAAE,CAAE,CAAC,CAAE,CAAC,EAAE,CAAC,CAC3C5D,KAAK,CAAC2F,GAAG,CAACsT,iBAAiB,CAAC,CAE5B;AACA,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAA/a,KAAK,CAACgb,SAAS,CAAC,QAAQ,CAAE,GAAG,CAAC,CACpDD,SAAS,CAAC7O,QAAQ,CAACzG,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAChCsV,SAAS,CAACE,KAAK,CAAG3U,IAAI,CAACC,EAAE,CAAG,CAAC,CAC7BwU,SAAS,CAACG,QAAQ,CAAG,GAAG,CACxBH,SAAS,CAACI,KAAK,CAAG,CAAC,CACnBJ,SAAS,CAAClV,QAAQ,CAAG,GAAG,CACxBhE,KAAK,CAAC2F,GAAG,CAACuT,SAAS,CAAC,CAEpB;AACAvZ,QAAQ,CAAG,GAAI,CAAAtB,aAAa,CAAC2Z,MAAM,CAAEI,QAAQ,CAACQ,UAAU,CAAC,CACzDjZ,QAAQ,CAAC4Z,aAAa,CAAG,IAAI,CAC7B5Z,QAAQ,CAAC6Z,aAAa,CAAG,IAAI,CAC7B7Z,QAAQ,CAAC8Z,kBAAkB,CAAG,KAAK,CACnC9Z,QAAQ,CAACwO,WAAW,CAAG,EAAE,CACzBxO,QAAQ,CAACyO,WAAW,CAAG,GAAG,CAC1BzO,QAAQ,CAAC0O,aAAa,CAAG5J,IAAI,CAACC,EAAE,CAAG,GAAG,CACtC/E,QAAQ,CAAC2O,aAAa,CAAG,CAAC,CAC1B3O,QAAQ,CAACoO,MAAM,CAACnK,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5BjE,QAAQ,CAACuO,MAAM,CAAC,CAAC,CAEjB;AACA3M,OAAO,CAACC,GAAG,CAAC,OAAO,CAAE,CACnBwW,MAAM,CAAE,CAAC,CAACA,MAAM,CAChBrY,QAAQ,CAAE,CAAC,CAACA,QAAQ,CACpB0L,SAAS,CAAE,CAAC,CAACA,SAAS,CAAC2B,OACzB,CAAC,CAAC,CAEF;AACA,KAAM,CAAA0M,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,MAAO,IAAI,CAAAC,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACtC,KAAM,CAAAC,aAAa,CAAG,GAAI,CAAA1b,UAAU,CAAC,CAAC,CACtC0b,aAAa,CAACC,IAAI,CAChB,GAAG5Y,QAAQ,uBAAuB,CACjC6Y,IAAI,EAAK,CACR,KAAM,CAAAC,YAAY,CAAGD,IAAI,CAACha,KAAK,CAE/B;AACA,KAAM,CAAAka,gBAAgB,CAAG,GAAI,CAAA/b,KAAK,CAACgc,KAAK,CAAC,CAAC,CAE1C;AACAF,YAAY,CAACrU,QAAQ,CAAEiN,KAAK,EAAK,CAC/B,GAAIA,KAAK,CAAC9K,MAAM,CAAE,CAChB;AACA,GAAI8K,KAAK,CAAC3K,QAAQ,CAAE,CAClB;AACA,KAAM,CAAA4K,WAAW,CAAG,GAAI,CAAA3U,KAAK,CAACic,oBAAoB,CAAC,CACjD9N,KAAK,CAAE,QAAQ,CAAO;AACtB+N,SAAS,CAAE,GAAG,CAAQ;AACtBC,SAAS,CAAE,GAAG,CAAQ;AACtBC,eAAe,CAAE,GAAK;AACxB,CAAC,CAAC,CAEF;AACA,GAAI1H,KAAK,CAAC3K,QAAQ,CAAClB,GAAG,CAAE,CACtB8L,WAAW,CAAC9L,GAAG,CAAG6L,KAAK,CAAC3K,QAAQ,CAAClB,GAAG,CACtC,CAEA;AACA6L,KAAK,CAAC3K,QAAQ,CAAG4K,WAAW,CAE5BvR,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEqR,KAAK,CAAC5D,IAAI,CAAC,CACrC,CACF,CACF,CAAC,CAAC,CAEF;AACA,MAAMgL,YAAY,CAACO,QAAQ,CAAC9T,MAAM,CAAG,CAAC,CAAE,CACtC,KAAM,CAAAmM,KAAK,CAAGoH,YAAY,CAACO,QAAQ,CAAC,CAAC,CAAC,CACtCN,gBAAgB,CAACvU,GAAG,CAACkN,KAAK,CAAC,CAC7B,CAEA;AACA7S,KAAK,CAAC2F,GAAG,CAACuU,gBAAgB,CAAC,CAE3B;AACA/a,gBAAgB,CAAG+a,gBAAgB,CAEnC3Y,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAC9BiZ,kBAAkB,CAAC,IAAI,CAAC,CACxBb,OAAO,CAACM,gBAAgB,CAAC,CAC3B,CAAC,CACAQ,GAAG,EAAK,CACPnZ,OAAO,CAACC,GAAG,CAAC,aAAa,CAACkZ,GAAG,CAACC,MAAM,CAAGD,GAAG,CAACE,KAAK,CAAG,GAAG,EAAEzW,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CACxE,CAAC,CACD0V,MACF,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAgB,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACF;AACA;AAEA;AACAxD,cAAc,CAAC,CAAC,CAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEF,CAAE,MAAOxU,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CAED;AACA,KAAM,CAAAiY,kBAAkB,CAAG,QAAAA,CAACC,GAAG,CAAqB,IAAnB,CAAAC,UAAU,CAAAC,SAAA,CAAAvU,MAAA,IAAAuU,SAAA,MAAAjH,SAAA,CAAAiH,SAAA,IAAG,CAAC,CAC7C,MAAO,IAAI,CAAAtB,OAAO,CAAC,CAACC,OAAO,CAAEC,MAAM,GAAK,CACtC,KAAM,CAAAqB,WAAW,CAAIC,WAAW,EAAK,CACnC5Z,OAAO,CAACC,GAAG,CAAC,WAAWuZ,GAAG,aAAaI,WAAW,EAAE,CAAC,CAErD,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAAhd,UAAU,CAAC,CAAC,CAC/Bgd,MAAM,CAACrB,IAAI,CACTgB,GAAG,CACFf,IAAI,EAAK,CACRzY,OAAO,CAACC,GAAG,CAAC,WAAWuZ,GAAG,EAAE,CAAC,CAC7BnB,OAAO,CAACI,IAAI,CAAC,CACf,CAAC,CACAU,GAAG,EAAK,CACPnZ,OAAO,CAACC,GAAG,CAAC,SAAS,CAACkZ,GAAG,CAACC,MAAM,CAAGD,GAAG,CAACE,KAAK,CAAG,GAAG,EAAEzW,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CACpE,CAAC,CACAtB,KAAK,EAAK,CACTtB,OAAO,CAACsB,KAAK,CAAC,SAASkY,GAAG,EAAE,CAAElY,KAAK,CAAC,CACpC,GAAIsY,WAAW,CAAG,CAAC,CAAE,CACnB5Z,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3BoO,UAAU,CAAC,IAAMsL,WAAW,CAACC,WAAW,CAAG,CAAC,CAAC,CAAE,IAAI,CAAC,CACtD,CAAC,IAAM,CACLtB,MAAM,CAAChX,KAAK,CAAC,CACf,CACF,CACF,CAAC,CACH,CAAC,CAEDqY,WAAW,CAACF,UAAU,CAAC,CACzB,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAI,MAAM,CAAG,GAAI,CAAAhd,UAAU,CAAC,CAAC,CAC/Bgd,MAAM,CAACrB,IAAI,CACT,GAAG5Y,QAAQ,4BAA4B,CACvC,KAAO,CAAA6Y,IAAI,EAAK,CACd,GAAI,CACF,KAAM,CAAAtU,KAAK,CAAGsU,IAAI,CAACha,KAAK,CACxB0F,KAAK,CAACgM,KAAK,CAAC9N,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACxB8B,KAAK,CAAC2E,QAAQ,CAACzG,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAE3B;AACA,GAAI5D,KAAK,CAAE,CACXA,KAAK,CAAC2F,GAAG,CAACD,KAAK,CAAC,CAEhB;AACA,KAAM,CAAAmV,eAAe,CAAC,CAAC,CACvB,CAAC,IAAM,CACLtZ,OAAO,CAACsB,KAAK,CAAC,eAAe,CAAC,CAChC,CACF,CAAE,MAAOA,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CACA6X,GAAG,EAAK,CACPnZ,OAAO,CAACC,GAAG,CAAC,SAAS,CAACkZ,GAAG,CAACC,MAAM,CAAGD,GAAG,CAACE,KAAK,CAAG,GAAG,EAAEzW,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CACpE,CAAC,CACAtB,KAAK,EAAK,CACTtB,OAAO,CAACsB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BtB,OAAO,CAACsB,KAAK,CAAC,OAAO,CAAE,CACrBwY,IAAI,CAAExY,KAAK,CAACiO,IAAI,CAChBwK,IAAI,CAAEzY,KAAK,CAACkN,OAAO,CACnBwL,KAAK,CAAE,GAAGpa,QAAQ,4BAA4B,CAC9Cqa,KAAK,CAAE,GAAGra,QAAQ,4BACpB,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAsa,OAAO,CAAGA,CAAA,GAAM,CACpBvS,iBAAiB,CAAC8D,OAAO,CAAG0O,qBAAqB,CAACD,OAAO,CAAC,CAE1D;AACAld,KAAK,CAAC2P,MAAM,CAAC,CAAC,CAEd;AACA,KAAM,CAAAyN,SAAS,CAAG7Z,KAAK,CAAC8Z,QAAQ,CAAC,CAAC,CAElC;AACA,GAAIlS,qBAAqB,CAACmS,IAAI,CAAG,CAAC,CAAE,CAClCnS,qBAAqB,CAACvD,OAAO,CAAEV,KAAK,EAAK,CACvCA,KAAK,CAACyI,MAAM,CAACyN,SAAS,CAAC,CACzB,CAAC,CAAC,CACJ,CAEA,GAAIjc,UAAU,GAAK,QAAQ,EAAIP,gBAAgB,CAAE,CAC/C;AACAQ,QAAQ,CAACsN,OAAO,CAAG,KAAK,CAExB;AACA,KAAM,CAAA6O,UAAU,CAAG3c,gBAAgB,CAACkL,QAAQ,CAAChH,KAAK,CAAC,CAAC,CAEpD;AACA,KAAM,CAAA0Y,eAAe,CAAG5c,gBAAgB,CAACsS,QAAQ,CAAChO,CAAC,CAEnD;AACA;AACA,KAAM,CAAAuY,gBAAgB,CAAG,EAAED,eAAe,CAAGtX,IAAI,CAACC,EAAE,CAAC,CAAGD,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC,CAAC,CAEnE;AACA,KAAM,CAAAuX,YAAY,CAAG,GAAI,CAAA9d,KAAK,CAACkG,OAAO,CACpC,CAAC,EAAE,CAAGI,IAAI,CAACyX,GAAG,CAACF,gBAAgB,CAAC,CAChC,GAAG,CACH,CAAC,EAAE,CAAGvX,IAAI,CAAC0X,GAAG,CAACH,gBAAgB,CACjC,CAAC,CAED;AACA,KAAM,CAAAI,oBAAoB,CAAGN,UAAU,CAACzY,KAAK,CAAC,CAAC,CAACsC,GAAG,CAACsW,YAAY,CAAC,CACjE,KAAM,CAAAI,YAAY,CAAGP,UAAU,CAACzY,KAAK,CAAC,CAAC,CAEvC;AACA,GAAI,CAAC+F,kBAAkB,CAAC4D,OAAO,CAAE,CAC/B5D,kBAAkB,CAAC4D,OAAO,CAAGoP,oBAAoB,CAAC/Y,KAAK,CAAC,CAAC,CAC3D,CAEA,GAAI,CAACgG,gBAAgB,CAAC2D,OAAO,CAAE,CAC7B3D,gBAAgB,CAAC2D,OAAO,CAAGqP,YAAY,CAAChZ,KAAK,CAAC,CAAC,CACjD,CAEA;AACA+F,kBAAkB,CAAC4D,OAAO,CAACsP,IAAI,CAACF,oBAAoB,CAAE,CAAC,CAAG9S,eAAe,CAAC,CAC1ED,gBAAgB,CAAC2D,OAAO,CAACsP,IAAI,CAACD,YAAY,CAAE,CAAC,CAAG/S,eAAe,CAAC,CAEhE;AACA0O,MAAM,CAAC3N,QAAQ,CAACwD,IAAI,CAACzE,kBAAkB,CAAC4D,OAAO,CAAC,CAEhD;AACAgL,MAAM,CAAC3K,EAAE,CAACzJ,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAEtB;AACAoU,MAAM,CAAC/J,MAAM,CAAC5E,gBAAgB,CAAC2D,OAAO,CAAC,CAEvC;AACAgL,MAAM,CAACuE,sBAAsB,CAAC,CAAC,CAC/BvE,MAAM,CAACzJ,YAAY,CAAC,CAAC,CACrByJ,MAAM,CAACxJ,iBAAiB,CAAC,IAAI,CAAC,CAE9B;AACA7O,QAAQ,CAACsN,OAAO,CAAG,KAAK,CAExB;AACAtN,QAAQ,CAACoO,MAAM,CAACF,IAAI,CAACxE,gBAAgB,CAAC2D,OAAO,CAAC,CAC9CrN,QAAQ,CAACuO,MAAM,CAAC,CAAC,CAEjB3M,OAAO,CAACC,GAAG,CAAC,OAAO,CAAE,CACnBgb,IAAI,CAAEV,UAAU,CAACrM,OAAO,CAAC,CAAC,CAC1BD,IAAI,CAAEwI,MAAM,CAAC3N,QAAQ,CAACoF,OAAO,CAAC,CAAC,CAC/BgN,IAAI,CAAEpT,gBAAgB,CAAC2D,OAAO,CAACyC,OAAO,CAAC,CAAC,CACxCiN,IAAI,CAAE1E,MAAM,CAAC2E,iBAAiB,CAAC,GAAI,CAAAxe,KAAK,CAACkG,OAAO,CAAC,CAAC,CAAC,CAACoL,OAAO,CAAC,CAC9D,CAAC,CAAC,CACJ,CAAC,IAAM,IAAI/P,UAAU,GAAK,QAAQ,CAAE,CAClC;AACA0J,kBAAkB,CAAC4D,OAAO,CAAG,IAAI,CACjC3D,gBAAgB,CAAC2D,OAAO,CAAG,IAAI,CAE/B;AACArN,QAAQ,CAACsN,OAAO,CAAG,IAAI,CAEvB;AACA+K,MAAM,CAAC3K,EAAE,CAACzJ,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAEtB;AACA,GAAIa,IAAI,CAACK,GAAG,CAACkT,MAAM,CAAC3N,QAAQ,CAAC5G,CAAC,CAAC,CAAG,EAAE,CAAE,CACpCuU,MAAM,CAAC3N,QAAQ,CAACzG,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAC9BjE,QAAQ,CAACoO,MAAM,CAACnK,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5BoU,MAAM,CAAC/J,MAAM,CAACtO,QAAQ,CAACoO,MAAM,CAAC,CAC9BpO,QAAQ,CAACuO,MAAM,CAAC,CAAC,CACnB,CAEA;AACA;AACA8J,MAAM,CAACzJ,YAAY,CAAC,CAAC,CACrByJ,MAAM,CAACxJ,iBAAiB,CAAC,IAAI,CAAC,CAEhC,CAAC,IAAM,IAAI9O,UAAU,GAAK,cAAc,CAAE,CACxC;AACA0J,kBAAkB,CAAC4D,OAAO,CAAG,IAAI,CACjC3D,gBAAgB,CAAC2D,OAAO,CAAG,IAAI,CAE/B;AACArN,QAAQ,CAACuO,MAAM,CAAC,CAAC,CACnB,CAEA,GAAIvO,QAAQ,CAAEA,QAAQ,CAACuO,MAAM,CAAC,CAAC,CAC/B,GAAIlO,KAAK,EAAIgY,MAAM,CAAE,CACnBI,QAAQ,CAACwE,MAAM,CAAC5c,KAAK,CAAEgY,MAAM,CAAC,CAChC,CACF,CAAC,CAEDyD,OAAO,CAAC,CAAC,CAET;AACA,KAAM,CAAAoB,YAAY,CAAGA,CAAA,GAAM,CACzB7E,MAAM,CAAC8E,MAAM,CAAGnc,MAAM,CAACuX,UAAU,CAAGvX,MAAM,CAACwX,WAAW,CACtDH,MAAM,CAACuE,sBAAsB,CAAC,CAAC,CAC/BnE,QAAQ,CAACG,OAAO,CAAC5X,MAAM,CAACuX,UAAU,CAAEvX,MAAM,CAACwX,WAAW,CAAC,CACzD,CAAC,CACDxX,MAAM,CAACoc,gBAAgB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CAE/C;AACAlc,MAAM,CAACqc,aAAa,CAAG,IAAM,CAC3B,GAAI3R,SAAS,CAAC2B,OAAO,CAAE,CACrB3B,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAACzG,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CACzCyH,SAAS,CAAC2B,OAAO,CAACiB,MAAM,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACjC5C,SAAS,CAAC2B,OAAO,CAACuB,YAAY,CAAC,CAAC,CAChClD,SAAS,CAAC2B,OAAO,CAACwB,iBAAiB,CAAC,IAAI,CAAC,CAEzC,GAAI7O,QAAQ,CAAE,CACZA,QAAQ,CAACoO,MAAM,CAACnK,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5BjE,QAAQ,CAACsN,OAAO,CAAG,IAAI,CACvBtN,QAAQ,CAACuO,MAAM,CAAC,CAAC,CACnB,CAEAxO,UAAU,CAAG,QAAQ,CACrB6B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CAAC,CAED;AACA,MAAO,IAAM,CACXD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CAExB;AACA,GAAI0H,iBAAiB,CAAC8D,OAAO,CAAE,CAC7BiQ,oBAAoB,CAAC/T,iBAAiB,CAAC8D,OAAO,CAAC,CAC/C9D,iBAAiB,CAAC8D,OAAO,CAAG,IAAI,CAClC,CAEA;AACAzO,KAAK,CAAC2e,SAAS,CAAC,CAAC,CAEjB;AACAxT,qBAAqB,CAACvD,OAAO,CAACV,KAAK,EAAI,CACrCN,eAAe,CAACe,WAAW,CAACT,KAAK,CAAC,CACpC,CAAC,CAAC,CACFiE,qBAAqB,CAAClC,KAAK,CAAC,CAAC,CAE7B;AACArC,eAAe,CAACoC,OAAO,CAAC,CAAC,CAEzB;AACA,GAAIvH,KAAK,CAAE,CACT,KAAM,CAAAmd,eAAe,CAAG,EAAE,CAC1Bnd,KAAK,CAAC4F,QAAQ,CAAEC,MAAM,EAAK,CACzB,GAAIA,MAAM,CAACkC,MAAM,CAAE,CACjB,GAAIlC,MAAM,CAACmC,QAAQ,CAAE,CACnBnC,MAAM,CAACmC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAC3B,CACA,GAAIpC,MAAM,CAACqC,QAAQ,CAAE,CACnB,GAAI5F,KAAK,CAACC,OAAO,CAACsD,MAAM,CAACqC,QAAQ,CAAC,CAAE,CAClCrC,MAAM,CAACqC,QAAQ,CAAC/B,OAAO,CAAC+B,QAAQ,EAAI,CAClC,GAAIA,QAAQ,CAAClB,GAAG,CAAEkB,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC,CACxCC,QAAQ,CAACD,OAAO,CAAC,CAAC,CACpB,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,GAAIpC,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAAEnB,MAAM,CAACqC,QAAQ,CAAClB,GAAG,CAACiB,OAAO,CAAC,CAAC,CACtDpC,MAAM,CAACqC,QAAQ,CAACD,OAAO,CAAC,CAAC,CAC3B,CACF,CACA,GAAIpC,MAAM,GAAK7F,KAAK,CAAE,CACpBmd,eAAe,CAAClI,IAAI,CAACpP,MAAM,CAAC,CAC9B,CACF,CACF,CAAC,CAAC,CAEF;AACAsX,eAAe,CAAChX,OAAO,CAACiX,GAAG,EAAI,CAC7B,GAAIA,GAAG,CAAC1V,MAAM,CAAE,CACd0V,GAAG,CAAC1V,MAAM,CAACC,MAAM,CAACyV,GAAG,CAAC,CACxB,CACF,CAAC,CAAC,CAEFpd,KAAK,CAACwH,KAAK,CAAC,CAAC,CACf,CAEA;AACA,GAAI4Q,QAAQ,CAAE,CACZA,QAAQ,CAACiF,gBAAgB,CAAC,IAAI,CAAC,CAC/B,GAAIzU,YAAY,CAACoE,OAAO,EAAIoL,QAAQ,CAACQ,UAAU,EAAIR,QAAQ,CAACQ,UAAU,CAAC0E,UAAU,GAAK1U,YAAY,CAACoE,OAAO,CAAE,CAC1GpE,YAAY,CAACoE,OAAO,CAACuQ,WAAW,CAACnF,QAAQ,CAACQ,UAAU,CAAC,CACvD,CACAR,QAAQ,CAACnQ,OAAO,CAAC,CAAC,CAClBmQ,QAAQ,CAACoF,gBAAgB,CAAC,CAAC,CAC7B,CAEA;AACA,GAAI7d,QAAQ,CAAE,CACZA,QAAQ,CAACsI,OAAO,CAAC,CAAC,CACpB,CAEA;AACA3H,oBAAoB,CAACkH,KAAK,CAAC,CAAC,CAC5BhH,oBAAoB,CAACgH,KAAK,CAAC,CAAC,CAC5B9F,gBAAgB,CAAC8F,KAAK,CAAC,CAAC,CACxB/F,aAAa,CAAC+F,KAAK,CAAC,CAAC,CACrB5F,gBAAgB,CAAC4F,KAAK,CAAC,CAAC,CACxB3F,kBAAkB,CAAC2F,KAAK,CAAC,CAAC,CAE1BjG,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACvB,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACAzD,SAAS,CAAC,IAAM,CACd;AACAiE,qBAAqB,CAAC,CAAC,CAEvB;AACA,KAAM,CAAAyb,uBAAuB,CAAGA,CAAA,GAAM,CACpClc,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjCQ,qBAAqB,CAAC,CAAC,CACzB,CAAC,CAED;AACArB,MAAM,CAACoc,gBAAgB,CAAC,oBAAoB,CAAEU,uBAAuB,CAAC,CAEtE;AACA,KAAM,CAAAC,UAAU,CAAGC,WAAW,CAAC,IAAM,CACnC3b,qBAAqB,CAAC,CAAC,CACzB,CAAC,CAAE,KAAK,CAAC,CAET;AACA,MAAO,IAAM,CACXrB,MAAM,CAACid,mBAAmB,CAAC,oBAAoB,CAAEH,uBAAuB,CAAC,CACzEI,aAAa,CAACH,UAAU,CAAC,CAC3B,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA3f,SAAS,CAAC,IAAM,CACd;AACA,GAAIiC,KAAK,EAAI8I,SAAS,CAACkE,OAAO,CAAE,CAC9BzL,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB;AACA,KAAM,CAAAsc,KAAK,CAAGlO,UAAU,CAAC,IAAM,CAC7B,GAAI5P,KAAK,EAAI8I,SAAS,CAACkE,OAAO,CAAE,CAAG;AACjC+Q,mBAAmB,CAACjV,SAAS,CAACkE,OAAO,CAAC,CACxC,CACF,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMgR,YAAY,CAACF,KAAK,CAAC,CAClC,CAAC,IAAM,CACLvc,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CACrC,CACF,CAAC,CAAE,CAACxB,KAAK,CAAC,CAAC,CAEX;AACAjC,SAAS,CAAC,IAAM,CACd,GAAI6K,YAAY,CAACoE,OAAO,CAAE,CACxB;AACA,KAAM,CAAAiR,WAAW,CAAIlI,KAAK,EAAK,CAC7B,GAAI/V,KAAK,EAAIqL,SAAS,CAAC2B,OAAO,CAAE,CAC9BkR,gBAAgB,CAACnI,KAAK,CAAEnN,YAAY,CAACoE,OAAO,CAAEhN,KAAK,CAAEqL,SAAS,CAAC2B,OAAO,CAAC,CACzE,CACF,CAAC,CAED;AACApE,YAAY,CAACoE,OAAO,CAAC+P,gBAAgB,CAAC,OAAO,CAAEkB,WAAW,CAAC,CAE3D;AACA1c,OAAO,CAACC,GAAG,CAAC,eAAe,CAAE,CAAC,CAACoH,YAAY,CAACoE,OAAO,CAAC,CAEpD;AACA,MAAO,IAAM,CACX,GAAIpE,YAAY,CAACoE,OAAO,CAAE,CACxBpE,YAAY,CAACoE,OAAO,CAAC4Q,mBAAmB,CAAC,OAAO,CAAEK,WAAW,CAAC,CAC9D1c,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC,CAC3B,CACF,CAAC,CACH,CACF,CAAC,CAAE,CAACxB,KAAK,CAAEqL,SAAS,CAAC2B,OAAO,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAmR,SAAS,CAAGjgB,WAAW,CAAC,IAAM,CAClCqD,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAC7B;AACF,CAAC,CAAE,CAACoH,YAAY,CAAE8D,aAAa,CAAE9K,gBAAgB,CAAC,CAAC,CAEnD;AACA,KAAM,CAAAwc,wBAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAApW,QAAQ,CAAG,GAAI,CAAA7J,KAAK,CAACkgB,WAAW,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAChD,KAAM,CAAAnW,QAAQ,CAAG,GAAI,CAAA/J,KAAK,CAACmgB,iBAAiB,CAAC,CAAEhS,KAAK,CAAE,QAAS,CAAC,CAAC,CACjE,KAAM,CAAA8I,iBAAiB,CAAG,GAAI,CAAAjX,KAAK,CAACogB,IAAI,CAACvW,QAAQ,CAAEE,QAAQ,CAAC,CAE5D;AACA,KAAM,CAAAsW,YAAY,CAAG,GAAI,CAAArgB,KAAK,CAACsgB,gBAAgB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,EAAE,CAAC,CAC5D,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAAvgB,KAAK,CAACmgB,iBAAiB,CAAC,CAAEhS,KAAK,CAAE,QAAS,CAAC,CAAC,CACrE,KAAM,CAAAqS,SAAS,CAAG,GAAI,CAAAxgB,KAAK,CAACogB,IAAI,CAACC,YAAY,CAAEE,YAAY,CAAC,CAC5DC,SAAS,CAACtU,QAAQ,CAACzG,GAAG,CAAC,CAAC,CAAE,CAAC,GAAG,CAAE,CAAC,CAAC,CAClCwR,iBAAiB,CAACzP,GAAG,CAACgZ,SAAS,CAAC,CAEhC,MAAO,CAAAvJ,iBAAiB,CAC1B,CAAC,CAED;AACA,KAAM,CAAAwJ,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAI,CAAC5e,KAAK,CAAE,OAEZ;AACA4B,gBAAgB,CAACuE,OAAO,CAAC,CAAC0Y,QAAQ,CAAElT,OAAO,GAAK,CAC9C,GAAIkT,QAAQ,CAACnZ,KAAK,CAAE,CAClB;AACA,KAAM,CAAAoZ,cAAc,CAAG,GAAI,CAAA3gB,KAAK,CAAC4gB,cAAc,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACxD,KAAM,CAAAC,cAAc,CAAG,GAAI,CAAA7gB,KAAK,CAACmgB,iBAAiB,CAAC,CACjDhS,KAAK,CAAE,QAAQ,CAAC;AAChB2G,WAAW,CAAE,KAAK,CAClBU,OAAO,CAAE,GAAG,CAAG;AACfsL,UAAU,CAAE,KACd,CAAC,CAAC,CAEF,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAA/gB,KAAK,CAACogB,IAAI,CAACO,cAAc,CAAEE,cAAc,CAAC,CACjEE,UAAU,CAAC7U,QAAQ,CAACzG,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAG;AAEnC;AACAsb,UAAU,CAACC,QAAQ,CAAG,CACpBrO,IAAI,CAAE,cAAc,CACpBnF,OAAO,CAAEA,OAAO,CAChBsD,IAAI,CAAE4P,QAAQ,CAAC/P,YAAY,CAACG,IAAI,CAChCmQ,aAAa,CAAE,IACjB,CAAC,CAED;AACAP,QAAQ,CAACnZ,KAAK,CAACC,GAAG,CAACuZ,UAAU,CAAC,CAE9B3d,OAAO,CAACC,GAAG,CAAC,OAAOqd,QAAQ,CAAC/P,YAAY,CAACG,IAAI,KAAKtD,OAAO,aAAa,CAAC,CACzE,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA5N,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA+f,KAAK,CAAGlO,UAAU,CAAC,IAAM,CAC7B,GAAIhO,gBAAgB,CAACia,IAAI,CAAG,CAAC,CAAE,CAC7Bta,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1B;AACF,CACF,CAAC,CAAE,IAAI,CAAC,CAAG;AAEX,MAAO,IAAMwc,YAAY,CAACF,KAAK,CAAC,CAClC,CAAC,CAAE,EAAE,CAAC,CAEN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA/f,SAAS,CAAC,IAAM,CACd,MAAO,IAAM,CACX;AACA,GAAIgO,0BAA0B,CAACiB,OAAO,CAAE,CACtC6Q,aAAa,CAAC9R,0BAA0B,CAACiB,OAAO,CAAC,CACjDjB,0BAA0B,CAACiB,OAAO,CAAG,IAAI,CACzCzL,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC9B,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAAE;AAER;AACAzD,SAAS,CAAC,IAAM,CACd,GAAI,CAACyN,mBAAmB,CAACE,OAAO,EAAIK,0BAA0B,CAACiB,OAAO,CAAE,CACtE6Q,aAAa,CAAC9R,0BAA0B,CAACiB,OAAO,CAAC,CACjDjB,0BAA0B,CAACiB,OAAO,CAAG,IAAI,CACzClB,mBAAmB,CAACkB,OAAO,CAAG,IAAI,CAClCzL,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACnC,CACF,CAAC,CAAE,CAACgK,mBAAmB,CAACE,OAAO,CAAC,CAAC,CAEjC;AACA3N,SAAS,CAAC,IAAM,CACd;AACA,GAAIa,iBAAiB,EAAIA,iBAAiB,CAACmQ,aAAa,EAAInQ,iBAAiB,CAACmQ,aAAa,CAACrI,MAAM,CAAG,CAAC,CAAE,CACtG;AACA,GAAI,CAAC4E,oBAAoB,CAAE,CACzB;AACA,KAAM,CAAA+T,6BAA6B,CAAGzgB,iBAAiB,CAACmQ,aAAa,CAACtM,IAAI,CACxEqM,YAAY,EAAIA,YAAY,CAACa,eAAe,GAAK,KAAK,EAAIb,YAAY,CAACnD,OACzE,CAAC,CAED;AACA,KAAM,CAAA2T,kBAAkB,CAAGD,6BAA6B,EAAIzgB,iBAAiB,CAACmQ,aAAa,CAAC,CAAC,CAAC,CAE9FxN,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE8d,kBAAkB,CAACrQ,IAAI,CAClC,QAAQ,CAAEoQ,6BAA6B,CAAG,GAAG,CAAG,GAAG,CAAC,CAEhE;AACA,KAAM,CAAAvB,KAAK,CAAGlO,UAAU,CAAC,IAAM,CAC7BhB,wBAAwB,CAAC0Q,kBAAkB,CAACrQ,IAAI,CAAC,CACnD,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAM+O,YAAY,CAACF,KAAK,CAAC,CAClC,CACF,CACF,CAAC,CAAE,CAAClf,iBAAiB,CAAE0M,oBAAoB,CAAC,CAAC,CAE7C,mBACEtM,KAAA,CAAAE,SAAA,EAAAsb,QAAA,eACE1b,IAAA,SAAMygB,KAAK,CAAEnT,UAAW,CAAAoO,QAAA,CAAC,gCAAK,CAAM,CAAC,cACrC1b,IAAA,CAACJ,MAAM,EACL6gB,KAAK,CAAEtT,uBAAwB,CAC/BuT,WAAW,CAAC,4CAAS,CACrBC,QAAQ,CAAE7Q,wBAAyB,CACnC8Q,OAAO,CAAE9gB,iBAAiB,CAACmQ,aAAa,CAAC/H,GAAG,CAAC8H,YAAY,GAAK,CAC5DD,KAAK,CAAEC,YAAY,CAACG,IAAI,CACxB0Q,KAAK,CAAE7Q,YAAY,CAACG,IACtB,CAAC,CAAC,CAAE,CACJ4M,IAAI,CAAC,OAAO,CACZ+D,QAAQ,CAAE,IAAK,CACfC,aAAa,CAAE,CACbpV,MAAM,CAAE,IAAI,CACZqV,SAAS,CAAE,OACb,CAAE,CACFjR,KAAK,CAAEvD,oBAAoB,CAAGA,oBAAoB,CAAC2D,IAAI,CAAG+E,SAAU,CACrE,CAAC,cACFlV,IAAA,QAAKihB,GAAG,CAAEnX,YAAa,CAAC2W,KAAK,CAAE,CAAEpT,KAAK,CAAE,MAAM,CAAEqF,MAAM,CAAE,MAAO,CAAE,CAAE,CAAC,CAGnEhG,mBAAmB,CAACE,OAAO,eAC1B1M,KAAA,QACEugB,KAAK,CAAE,CACLlV,QAAQ,CAAE,UAAU,CACpBE,IAAI,CAAE,GAAGiB,mBAAmB,CAACnB,QAAQ,CAAC9G,CAAC,IAAI,CAC3C2I,GAAG,CAAE,GAAGV,mBAAmB,CAACnB,QAAQ,CAAC5G,CAAC,IAAI,CAC1C+G,SAAS,CAAE,wBAAwB,CACnCC,MAAM,CAAE,IAAI,CACZK,eAAe,CAAE,qBAAqB,CACtCwB,KAAK,CAAE,OAAO,CACdtB,YAAY,CAAE,KAAK,CACnBG,SAAS,CAAE,8BAA8B,CACzCN,OAAO,CAAE,GAAG,CACZmV,QAAQ,CAAE,OAAO,CAAE;AACnB9U,QAAQ,CAAE,MAAO;AACnB,CAAE,CAAAsP,QAAA,EAEDhP,mBAAmB,CAACI,OAAO,cAC5B9M,IAAA,WACEygB,KAAK,CAAE,CACLlV,QAAQ,CAAE,UAAU,CACpB6B,GAAG,CAAE,KAAK,CACV+T,KAAK,CAAE,KAAK,CACZC,UAAU,CAAE,MAAM,CAClBnV,MAAM,CAAE,MAAM,CACduB,KAAK,CAAE,OAAO,CACdpB,QAAQ,CAAE,MAAM,CAChBD,MAAM,CAAE,SAAS,CACjBJ,OAAO,CAAE,SACX,CAAE,CACFsV,OAAO,CAAEA,CAAA,GAAMC,kBAAkB,CAAC3U,sBAAsB,CAAE,CAAA+O,QAAA,CAC3D,MAED,CAAQ,CAAC,EACN,CACN,cAEDxb,KAAA,QAAKugB,KAAK,CAAEnV,oBAAqB,CAAAoQ,QAAA,eAC/B1b,IAAA,WACEygB,KAAK,CAAE,CACL,GAAG3U,WAAW,CACdE,eAAe,CAAEZ,QAAQ,GAAK,QAAQ,CAAG,SAAS,CAAG,0BAA0B,CAC/EoC,KAAK,CAAEpC,QAAQ,GAAK,QAAQ,CAAG,OAAO,CAAG,OAC3C,CAAE,CACFiW,OAAO,CAAEpT,kBAAmB,CAAAyN,QAAA,CAC7B,0BAED,CAAQ,CAAC,cACT1b,IAAA,WACEygB,KAAK,CAAE,CACL,GAAG3U,WAAW,CACdE,eAAe,CAAEZ,QAAQ,GAAK,QAAQ,CAAG,SAAS,CAAG,0BAA0B,CAC/EoC,KAAK,CAAEpC,QAAQ,GAAK,QAAQ,CAAG,OAAO,CAAG,OAC3C,CAAE,CACFiW,OAAO,CAAEjT,kBAAmB,CAAAsN,QAAA,CAC7B,0BAED,CAAQ,CAAC,EACN,CAAC,EACN,CAAC,CAEP,CAAC,CAED;AACA,QAAS,CAAApH,gBAAgBA,CAACiN,IAAI,CAAmB,IAAjB,CAAAC,UAAU,CAAArF,SAAA,CAAAvU,MAAA,IAAAuU,SAAA,MAAAjH,SAAA,CAAAiH,SAAA,IAAG,CAAC,CAAC,CAC7C,KAAM,CAAAsF,MAAM,CAAG,CACbC,QAAQ,CAAEF,UAAU,CAACE,QAAQ,EAAI,OAAO,CACxCtV,QAAQ,CAAEoV,UAAU,CAACpV,QAAQ,EAAI,EAAE,CAAE;AACrCqB,UAAU,CAAE+T,UAAU,CAAC/T,UAAU,EAAI,MAAM,CAC3CkU,eAAe,CAAEH,UAAU,CAACG,eAAe,EAAI,CAAC,CAChDC,WAAW,CAAEJ,UAAU,CAACI,WAAW,EAAI,CAAEpN,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEvM,CAAC,CAAE,GAAI,CAAC,CACnE6D,eAAe,CAAEwV,UAAU,CAACxV,eAAe,EAAI,CAAEwI,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEC,CAAC,CAAE,GAAG,CAAEvM,CAAC,CAAE,GAAI,CAAC,CACjFwM,SAAS,CAAE6M,UAAU,CAAC7M,SAAS,EAAI,CAAEH,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEvM,CAAC,CAAE,GAAI,CAAC,CAC/D4D,OAAO,CAAEyV,UAAU,CAACzV,OAAO,EAAI,CACjC,CAAC,CAED;AACA,KAAM,CAAA8V,MAAM,CAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC,CAC/C,KAAM,CAAAC,OAAO,CAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC,CAEvC;AACAD,OAAO,CAACE,IAAI,CAAG,GAAGT,MAAM,CAAChU,UAAU,IAAIgU,MAAM,CAACrV,QAAQ,MAAMqV,MAAM,CAACC,QAAQ,EAAE,CAE7E;AACA,KAAM,CAAAS,SAAS,CAAGH,OAAO,CAACI,WAAW,CAACb,IAAI,CAAC,CAAClU,KAAK,CAEjD;AACA,KAAM,CAAAA,KAAK,CAAG8U,SAAS,CAAG,CAAC,CAAGV,MAAM,CAAC1V,OAAO,CAAG,CAAC,CAAG0V,MAAM,CAACE,eAAe,CACzE,KAAM,CAAAjP,MAAM,CAAG+O,MAAM,CAACrV,QAAQ,CAAG,CAAC,CAAGqV,MAAM,CAAC1V,OAAO,CAAG,CAAC,CAAG0V,MAAM,CAACE,eAAe,CAEhFE,MAAM,CAACxU,KAAK,CAAGA,KAAK,CACpBwU,MAAM,CAACnP,MAAM,CAAGA,MAAM,CAEtB;AACAsP,OAAO,CAACE,IAAI,CAAG,GAAGT,MAAM,CAAChU,UAAU,IAAIgU,MAAM,CAACrV,QAAQ,MAAMqV,MAAM,CAACC,QAAQ,EAAE,CAC7EM,OAAO,CAACK,YAAY,CAAG,QAAQ,CAE/B;AACA,KAAM,CAAAC,MAAM,CAAG,CAAC,CAChBN,OAAO,CAACO,SAAS,CAAC,CAAC,CACnBP,OAAO,CAACQ,MAAM,CAACf,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEb,MAAM,CAACE,eAAe,CAAC,CACvEK,OAAO,CAACS,MAAM,CAACpV,KAAK,CAAGoU,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEb,MAAM,CAACE,eAAe,CAAC,CAC/EK,OAAO,CAACU,KAAK,CAACrV,KAAK,CAAGoU,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAEtU,KAAK,CAAGoU,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEA,MAAM,CAAC,CAC9IN,OAAO,CAACS,MAAM,CAACpV,KAAK,CAAGoU,MAAM,CAACE,eAAe,CAAEjP,MAAM,CAAG+O,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAC,CACxFN,OAAO,CAACU,KAAK,CAACrV,KAAK,CAAGoU,MAAM,CAACE,eAAe,CAAEjP,MAAM,CAAG+O,MAAM,CAACE,eAAe,CAAEtU,KAAK,CAAGoU,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAE5P,MAAM,CAAG+O,MAAM,CAACE,eAAe,CAAEW,MAAM,CAAC,CAChKN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAE5P,MAAM,CAAG+O,MAAM,CAACE,eAAe,CAAC,CAChFK,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,CAAEjP,MAAM,CAAG+O,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAEjP,MAAM,CAAG+O,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEA,MAAM,CAAC,CAChJN,OAAO,CAACS,MAAM,CAAChB,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAC,CACvEN,OAAO,CAACU,KAAK,CAACjB,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAEF,MAAM,CAACE,eAAe,CAAGW,MAAM,CAAEb,MAAM,CAACE,eAAe,CAAEW,MAAM,CAAC,CAC9HN,OAAO,CAACW,SAAS,CAAC,CAAC,CAEnB;AACAX,OAAO,CAACY,WAAW,CAAG,QAAQnB,MAAM,CAACG,WAAW,CAACpN,CAAC,KAAKiN,MAAM,CAACG,WAAW,CAACnN,CAAC,KAAKgN,MAAM,CAACG,WAAW,CAAClN,CAAC,KAAK+M,MAAM,CAACG,WAAW,CAACzZ,CAAC,GAAG,CAChI6Z,OAAO,CAACa,SAAS,CAAGpB,MAAM,CAACE,eAAe,CAC1CK,OAAO,CAACc,MAAM,CAAC,CAAC,CAEhB;AACAd,OAAO,CAACe,SAAS,CAAG,QAAQtB,MAAM,CAACzV,eAAe,CAACwI,CAAC,KAAKiN,MAAM,CAACzV,eAAe,CAACyI,CAAC,KAAKgN,MAAM,CAACzV,eAAe,CAAC0I,CAAC,KAAK+M,MAAM,CAACzV,eAAe,CAAC7D,CAAC,GAAG,CAC9I6Z,OAAO,CAACgB,IAAI,CAAC,CAAC,CAEd;AACAhB,OAAO,CAACe,SAAS,CAAG,QAAQtB,MAAM,CAAC9M,SAAS,CAACH,CAAC,KAAKiN,MAAM,CAAC9M,SAAS,CAACF,CAAC,KAAKgN,MAAM,CAAC9M,SAAS,CAACD,CAAC,KAAK+M,MAAM,CAAC9M,SAAS,CAACxM,CAAC,GAAG,CACtH6Z,OAAO,CAACiB,SAAS,CAAG,QAAQ,CAE5B;AACAjB,OAAO,CAACkB,QAAQ,CAAC3B,IAAI,CAAElU,KAAK,CAAG,CAAC,CAAEqF,MAAM,CAAG,CAAC,CAAC,CAE7C;AACA,KAAM,CAAAyQ,OAAO,CAAG,GAAI,CAAA9jB,KAAK,CAAC+jB,aAAa,CAACvB,MAAM,CAAC,CAC/CsB,OAAO,CAACE,SAAS,CAAGhkB,KAAK,CAACikB,YAAY,CACtCH,OAAO,CAAC/O,WAAW,CAAG,IAAI,CAE1B;AACA,KAAM,CAAAmP,cAAc,CAAG,GAAI,CAAAlkB,KAAK,CAACmkB,cAAc,CAAC,CAC9Ctb,GAAG,CAAEib,OAAO,CACZhP,WAAW,CAAE,IACf,CAAC,CAAC,CAEF;AACA,KAAM,CAAAsP,MAAM,CAAG,GAAI,CAAApkB,KAAK,CAACqkB,MAAM,CAACH,cAAc,CAAC,CAC/CE,MAAM,CAAC7Q,KAAK,CAAC9N,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAAE;AAC7B2e,MAAM,CAACra,QAAQ,CAACua,SAAS,CAAG,KAAK,CAAE;AAEnC;AACAF,MAAM,CAACpD,QAAQ,CAAG,CAChBkB,IAAI,CAAEA,IAAI,CACVE,MAAM,CAAEA,MACV,CAAC,CAED,MAAO,CAAAgC,MAAM,CACf,CAIA;AACA5hB,MAAM,CAAC+hB,eAAe,CAAG,IAAM,CAC7B,GAAI,CACF;AACA,KAAM,CAAA1K,MAAM,CAAG4I,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc,CAC5E,GAAI7K,MAAM,CAAE,CACV;AACA,KAAM,CAAA8K,MAAM,CAAG9K,MAAM,CAAC3N,QAAQ,CAAChH,KAAK,CAAC,CAAC,CAEtC;AACA2U,MAAM,CAAC3N,QAAQ,CAACzG,GAAG,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CAAC,CAC9BoU,MAAM,CAAC3K,EAAE,CAACzJ,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACtBoU,MAAM,CAAC/J,MAAM,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAEtB;AACA+J,MAAM,CAACzJ,YAAY,CAAC,CAAC,CACrByJ,MAAM,CAACxJ,iBAAiB,CAAC,IAAI,CAAC,CAE9B;AACA,KAAM,CAAA7O,QAAQ,CAAGihB,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB,CAChF,GAAIpjB,QAAQ,CAAE,CACZA,QAAQ,CAACoO,MAAM,CAACnK,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5BjE,QAAQ,CAACuO,MAAM,CAAC,CAAC,CACnB,CAEA3M,OAAO,CAACC,GAAG,CAAC,YAAY,CAAE,CACxBwhB,GAAG,CAAEF,MAAM,CAACrT,OAAO,CAAC,CAAC,CACrBwT,GAAG,CAAEjL,MAAM,CAAC3N,QAAQ,CAACoF,OAAO,CAAC,CAC/B,CAAC,CAAC,CAEF,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CAAE,MAAOnI,CAAC,CAAE,CACV/F,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAEyE,CAAC,CAAC,CAC9B,MAAO,MAAK,CACd,CACF,CAAC,CAGD;AACA,KAAM,CAAAwQ,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACFvW,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3B,KAAM,CAAA4Z,MAAM,CAAG,GAAI,CAAAhd,UAAU,CAAC,CAAC,CAE/B;AACA,GAAI,CACF,KAAM,CAAE8kB,gBAAgB,CAAEC,WAAW,CAAEC,WAAW,CAAEC,UAAU,CAAC,CAAG,KAAM,CAAA1J,OAAO,CAAC2J,GAAG,CAAC,CAClFlI,MAAM,CAACmI,SAAS,CAAC,GAAGpiB,QAAQ,4BAA4B,CAAC,CAC3Dia,MAAM,CAACmI,SAAS,CAAC,GAAGpiB,QAAQ,uBAAuB,CAAC,CACpDia,MAAM,CAACmI,SAAS,CAAC,GAAGpiB,QAAQ,uBAAuB,CAAC,CAClDia,MAAM,CAACmI,SAAS,CAAC,GAAGpiB,QAAQ,sBAAsB,CAAC,CAEtD,CAAC,CAIF;AACAI,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB5B,qBAAqB,CAAGujB,WAAW,CAACnjB,KAAK,CACzCJ,qBAAqB,CAACgG,QAAQ,CAAEiN,KAAK,EAAK,CACxC,GAAIA,KAAK,CAAC9K,MAAM,CAAE,CACd,KAAM,CAAA+K,WAAW,CAAG,GAAI,CAAA3U,KAAK,CAACic,oBAAoB,CAAC,CACnD9N,KAAK,CAAE,QAAQ,CAAG;AAClB+N,SAAS,CAAE,GAAG,CACdC,SAAS,CAAE,GAAG,CACdC,eAAe,CAAE,GACnB,CAAC,CAAC,CAEA;AACA,GAAI1H,KAAK,CAAC3K,QAAQ,CAAClB,GAAG,CAAE,CACtB8L,WAAW,CAAC9L,GAAG,CAAG6L,KAAK,CAAC3K,QAAQ,CAAClB,GAAG,CACtC,CACA6L,KAAK,CAAC2Q,OAAO,CAAG1Q,WAAW,CAC/B,CACF,CAAC,CAAC,CAEFvR,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1B;AACA3B,qBAAqB,CAAGujB,WAAW,CAACpjB,KAAK,CACzC;AACAH,qBAAqB,CAAC6R,KAAK,CAAC9N,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CACxC;AACA/D,qBAAqB,CAAC+F,QAAQ,CAAEiN,KAAK,EAAK,CACxC,GAAIA,KAAK,CAAC9K,MAAM,EAAI8K,KAAK,CAAC3K,QAAQ,CAAE,CAClC;AACA2K,KAAK,CAAC3K,QAAQ,CAACmS,SAAS,CAAG,GAAG,CAC9BxH,KAAK,CAAC3K,QAAQ,CAACoS,SAAS,CAAG,GAAG,CAC9BzH,KAAK,CAAC3K,QAAQ,CAACqS,eAAe,CAAG,GAAG,CACtC,CACF,CAAC,CAAC,CAEFhZ,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB;AACA1B,oBAAoB,CAAGujB,UAAU,CAACrjB,KAAK,CACvC;AACA;AACA;AACAF,oBAAoB,CAAC8F,QAAQ,CAAEiN,KAAK,EAAK,CACvC,GAAIA,KAAK,CAAC9K,MAAM,EAAI8K,KAAK,CAAC3K,QAAQ,CAAE,CAClC;AACA2K,KAAK,CAAC3K,QAAQ,CAACmS,SAAS,CAAG,GAAG,CAC9BxH,KAAK,CAAC3K,QAAQ,CAACoS,SAAS,CAAG,GAAG,CAC9BzH,KAAK,CAAC3K,QAAQ,CAACqS,eAAe,CAAG,GAAG,CAEtC,CACA,GAAI1H,KAAK,CAAC9K,MAAM,CAAC,CACf8K,KAAK,CAAC4Q,UAAU,CAAG,IAAI,CACzB,CACF,CAAC,CAAC,CAIF;AACAliB,OAAO,CAACC,GAAG,CAAC,YAAY,CAAE6hB,UAAU,CAAC5c,UAAU,CAACC,MAAM,CAAE,GAAG,CAAC,CAC5D,GAAI2c,UAAU,CAAC5c,UAAU,EAAI4c,UAAU,CAAC5c,UAAU,CAACC,MAAM,CAAG,CAAC,CAAE,CAC7DnF,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE6hB,UAAU,CAAC5c,UAAU,CAACC,MAAM,CAAE,GAAG,CAAC,CACzDzG,eAAe,CAAGojB,UAAU,CAC9B,CAAC,IAAM,CACL9hB,OAAO,CAACmiB,IAAI,CAAC,cAAc,CAAC,CAC9B,CAEAniB,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC,CAEzB;AACAzB,0BAA0B,CAAGmjB,gBAAgB,CAACljB,KAAK,CACnDuB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEzB,0BAA0B,CAAC,CACjD;AACAA,0BAA0B,CAAC2R,KAAK,CAAC9N,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC7C;AACA7D,0BAA0B,CAAC6F,QAAQ,CAAEiN,KAAK,EAAK,CAC7C,GAAIA,KAAK,CAAC9K,MAAM,EAAI8K,KAAK,CAAC3K,QAAQ,CAAE,CAClC;AACA2K,KAAK,CAAC3K,QAAQ,CAACmS,SAAS,CAAG,GAAG,CAC9BxH,KAAK,CAAC3K,QAAQ,CAACoS,SAAS,CAAG,GAAG,CAC9BzH,KAAK,CAAC3K,QAAQ,CAACqS,eAAe,CAAG,GAAG,CACxC,CACF,CAAC,CAAC,CAEAhZ,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB,CAAE,MAAOqB,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CAExC;AACA,GAAI,CACF,GAAI,CAACjD,qBAAqB,CAAE,CAC1B,KAAM,CAAAujB,WAAW,CAAG,KAAM,CAAA/H,MAAM,CAACmI,SAAS,CAAC,GAAGpiB,QAAQ,uBAAuB,CAAC,CAC9EvB,qBAAqB,CAAGujB,WAAW,CAACnjB,KAAK,CAC3C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,CAAE,MAAO2jB,GAAG,CAAE,CACZpiB,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAE8gB,GAAG,CAAC,CAClC,CACF,CACF,CAAE,MAAO9gB,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAClC,CACF,CAAC,CAED;AACA,KAAM,CAAAuU,mBAAmB,CAAItG,IAAI,EAAK,CACpC,KAAM,CAAA8S,KAAK,CAAG,CACZ,GAAG,CAAE,OAAO,CACZ,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,MAAM,CACX,GAAG,CAAE,OACP,CAAC,CACD,MAAO,CAAAA,KAAK,CAAC9S,IAAI,CAAC,EAAI,MAAM,CAC9B,CAAC,CAED;AACA,KAAM,CAAA4F,iBAAiB,CAAGA,CAACrM,QAAQ,CAAEgW,IAAI,CAAE/T,KAAK,GAAK,CACnD;AACA,GAAI,CAACtM,KAAK,CAAE,CACVuB,OAAO,CAACmiB,IAAI,CAAC,oBAAoB,CAAC,CAClC,OACF,CAEA,GAAI,CACJ;AACA,KAAM,CAAAnB,MAAM,CAAGnP,gBAAgB,CAACiN,IAAI,CAAC,CACrCkC,MAAM,CAAClY,QAAQ,CAACzG,GAAG,CAACyG,QAAQ,CAAC9G,CAAC,CAAE,EAAE,CAAE,CAAC8G,QAAQ,CAAC5G,CAAC,CAAC,CAAG;AAEnD;AACAmM,UAAU,CAAC,IAAM,CACb;AACA,GAAI5P,KAAK,EAAIuiB,MAAM,CAAC7a,MAAM,CAAE,CAC9B1H,KAAK,CAAC2H,MAAM,CAAC4a,MAAM,CAAC,CAClB,CACJ,CAAC,CAAE,GAAG,CAAC,CAEP;AACAviB,KAAK,CAAC2F,GAAG,CAAC4c,MAAM,CAAC,CAEjB;AACA;AACA;AACA;AACA;AACA,CAAE,MAAO1f,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CACpC,CACF,CAAC,CAED;AACA,KAAM,CAAAkb,mBAAmB,CAAI8F,iBAAiB,EAAK,CACjD,GAAI,CAAC7jB,KAAK,CAAE,CACVuB,OAAO,CAACsB,KAAK,CAAC,gBAAgB,CAAC,CAC/B,OACF,CAEA,GAAI,CAACghB,iBAAiB,CAAE,CACtBtiB,OAAO,CAACsB,KAAK,CAAC,mBAAmB,CAAC,CAClC,OACF,CAEA;AACA,GAAI,CAAC9C,0BAA0B,CAAE,CAC/BwB,OAAO,CAACsB,KAAK,CAAC,oBAAoB,CAAC,CACnC;AACA,KAAM,CAAAuY,MAAM,CAAG,GAAI,CAAAhd,UAAU,CAAC,CAAC,CAC/Bgd,MAAM,CAACmI,SAAS,CAAC,GAAGpiB,QAAQ,4BAA4B,CAAC,CACtD2iB,IAAI,CAACZ,gBAAgB,EAAI,CACxBnjB,0BAA0B,CAAGmjB,gBAAgB,CAACljB,KAAK,CACnDD,0BAA0B,CAAC2R,KAAK,CAAC9N,GAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC7C7D,0BAA0B,CAAC6F,QAAQ,CAAEiN,KAAK,EAAK,CAC7C,GAAIA,KAAK,CAAC9K,MAAM,EAAI8K,KAAK,CAAC3K,QAAQ,CAAE,CAClC2K,KAAK,CAAC3K,QAAQ,CAACmS,SAAS,CAAG,GAAG,CAC9BxH,KAAK,CAAC3K,QAAQ,CAACoS,SAAS,CAAG,GAAG,CAC9BzH,KAAK,CAAC3K,QAAQ,CAACqS,eAAe,CAAG,GAAG,CACtC,CACF,CAAC,CAAC,CACFhZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC,CACrC;AACAuc,mBAAmB,CAAC8F,iBAAiB,CAAC,CACxC,CAAC,CAAC,CACDE,KAAK,CAAClhB,KAAK,EAAI,CACdtB,OAAO,CAACsB,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACpC;AACAmhB,2BAA2B,CAACH,iBAAiB,CAAC,CAChD,CAAC,CAAC,CACJ,OACF,CAEA;AACAjiB,gBAAgB,CAACuE,OAAO,CAAE0Y,QAAQ,EAAK,CACrC,GAAI7e,KAAK,EAAI6e,QAAQ,CAACnZ,KAAK,CAAE,CAC3B1F,KAAK,CAAC2H,MAAM,CAACkX,QAAQ,CAACnZ,KAAK,CAAC,CAC9B,CACF,CAAC,CAAC,CACF9D,gBAAgB,CAAC4F,KAAK,CAAC,CAAC,CAExB;AACA5I,iBAAiB,CAACmQ,aAAa,CAAC5I,OAAO,CAAC2I,YAAY,EAAI,CACtD,GAAIA,YAAY,CAACa,eAAe,GAAK,KAAK,CAAE,CAC1CpO,OAAO,CAACC,GAAG,CAAC,UAAUsN,YAAY,CAACG,IAAI,kBAAkB,CAAC,CAC1D,OACF,CAEA,GAAIH,YAAY,CAAC/E,QAAQ,EAAI+E,YAAY,CAAChF,SAAS,EAAIgF,YAAY,CAACnD,OAAO,CAAE,CAC3E,KAAM,CAAA0F,QAAQ,CAAGwS,iBAAiB,CAAC1U,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,CAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC,CAEDxI,OAAO,CAACC,GAAG,CAAC,SAASsN,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,iBAAiB0F,QAAQ,CAAC9N,CAAC,KAAK8N,QAAQ,CAAC5N,CAAC,GAAG,CAAC,CAE7G,GAAI,CACF;AACA,GAAI,CAAC1D,0BAA0B,EAAI,CAACA,0BAA0B,CAACsD,KAAK,CAAE,CACpE,KAAM,IAAI,CAAA4gB,KAAK,CAAC,cAAc,CAAC,CACjC,CAEA;AACA,KAAM,CAAA7O,iBAAiB,CAAGrV,0BAA0B,CAACsD,KAAK,CAAC,CAAC,CAE5D;AACA+R,iBAAiB,CAACnG,IAAI,CAAG,OAAOH,YAAY,CAACG,IAAI,EAAE,CAEnD;AACAmG,iBAAiB,CAAC/K,QAAQ,CAACzG,GAAG,CAACyN,QAAQ,CAAC9N,CAAC,CAAE,EAAE,CAAE,CAAC8N,QAAQ,CAAC5N,CAAC,CAAC,CAE3D;AACA2R,iBAAiB,CAAC1D,KAAK,CAAC9N,GAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAEvC;AACAwR,iBAAiB,CAAC1B,WAAW,CAAG,GAAG,CAEnC;AACA0B,iBAAiB,CAACxP,QAAQ,CAACiN,KAAK,EAAI,CAClC,GAAIA,KAAK,CAAC9K,MAAM,CAAE,CAChB8K,KAAK,CAAC3K,QAAQ,CAAC+K,WAAW,CAAG,KAAK,CAClCJ,KAAK,CAAC3K,QAAQ,CAACyL,OAAO,CAAG,GAAG,CAC5Bd,KAAK,CAAC3K,QAAQ,CAACgc,IAAI,CAAG/lB,KAAK,CAACgmB,UAAU,CACtCtR,KAAK,CAAC3K,QAAQ,CAAC+W,UAAU,CAAG,IAAI,CAChCpM,KAAK,CAAC3K,QAAQ,CAACua,SAAS,CAAG,IAAI,CAC/B5P,KAAK,CAAC3K,QAAQ,CAACgL,WAAW,CAAG,IAAI,CACjCL,KAAK,CAACa,WAAW,CAAG,GAAG,CACzB,CACF,CAAC,CAAC,CAEF;AACA0B,iBAAiB,CAAC+J,QAAQ,CAAG,CAC3BrO,IAAI,CAAE,cAAc,CACpBnF,OAAO,CAAEmD,YAAY,CAACnD,OAAO,CAC7BsD,IAAI,CAAEH,YAAY,CAACG,IACrB,CAAC,CAED;AACAjP,KAAK,CAAC2F,GAAG,CAACyP,iBAAiB,CAAC,CAE5B;AACAxT,gBAAgB,CAACgC,GAAG,CAACkL,YAAY,CAACnD,OAAO,CAAE,CACzCjG,KAAK,CAAE0P,iBAAiB,CACxBtG,YAAY,CAAEA,YAAY,CAC1BzE,QAAQ,CAAEgH,QACZ,CAAC,CAAC,CAEF9P,OAAO,CAACC,GAAG,CAAC,SAASsN,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkB0F,QAAQ,CAAC9N,CAAC,KAAK,CAAC8N,QAAQ,CAAC5N,CAAC,GAAG,CAAC,CACjH,CAAE,MAAOZ,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,QAAQiM,YAAY,CAACG,IAAI,YAAY,CAAEpM,KAAK,CAAC,CAC3D;AACAub,wBAAwB,CAACtP,YAAY,CAAEuC,QAAQ,CAAEwS,iBAAiB,CAAC,CACrE,CACF,CACF,CAAC,CAAC,CAEF;AACAtiB,OAAO,CAACC,GAAG,CAAC,OAAOI,gBAAgB,CAACia,IAAI,SAAS,CAAC,CAClDja,gBAAgB,CAACuE,OAAO,CAAC,CAAC0Y,QAAQ,CAAElT,OAAO,GAAK,CAC9CpK,OAAO,CAACC,GAAG,CAAC,QAAQmK,OAAO,KAAKkT,QAAQ,CAAC/P,YAAY,CAACG,IAAI,EAAE,CAAC,CAC/D,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA+U,2BAA2B,CAAIH,iBAAiB,EAAK,CACzDjlB,iBAAiB,CAACmQ,aAAa,CAAC5I,OAAO,CAAC2I,YAAY,EAAI,CACtD;AACA,GAAIA,YAAY,CAACa,eAAe,GAAK,KAAK,CAAE,CAC1C,OACF,CAEA,GAAIb,YAAY,CAAC/E,QAAQ,EAAI+E,YAAY,CAAChF,SAAS,EAAIgF,YAAY,CAACnD,OAAO,CAAE,CAC3E;AACA,KAAM,CAAA0F,QAAQ,CAAGwS,iBAAiB,CAAC1U,YAAY,CAC7CC,UAAU,CAACN,YAAY,CAAChF,SAAS,CAAC,CAClCsF,UAAU,CAACN,YAAY,CAAC/E,QAAQ,CAClC,CAAC,CAEDqU,wBAAwB,CAACtP,YAAY,CAAEuC,QAAQ,CAAEwS,iBAAiB,CAAC,CACrE,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAzF,wBAAwB,CAAGA,CAACtP,YAAY,CAAEuC,QAAQ,CAAEwS,iBAAiB,GAAK,CAC9E;AACA,KAAM,CAAA7b,QAAQ,CAAG,GAAI,CAAA7J,KAAK,CAACkgB,WAAW,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAClD,KAAM,CAAAnW,QAAQ,CAAG,GAAI,CAAA/J,KAAK,CAACmgB,iBAAiB,CAAC,CAC3ChS,KAAK,CAAE,QAAQ,CACf2G,WAAW,CAAE,KAAK,CAClBU,OAAO,CAAE,GACX,CAAC,CAAC,CACF,KAAM,CAAAyB,iBAAiB,CAAG,GAAI,CAAAjX,KAAK,CAACogB,IAAI,CAACvW,QAAQ,CAAEE,QAAQ,CAAC,CAE5D;AACAkN,iBAAiB,CAACnG,IAAI,CAAG,SAASH,YAAY,CAACG,IAAI,EAAE,CAErD;AACAmG,iBAAiB,CAAC/K,QAAQ,CAACzG,GAAG,CAACyN,QAAQ,CAAC9N,CAAC,CAAE,EAAE,CAAE,CAAC8N,QAAQ,CAAC5N,CAAC,CAAC,CAE3D;AACA2R,iBAAiB,CAAC1B,WAAW,CAAG,GAAG,CAEnC;AACA0B,iBAAiB,CAAC+J,QAAQ,CAAG,CAC3BrO,IAAI,CAAE,cAAc,CACpBnF,OAAO,CAAEmD,YAAY,CAACnD,OAAO,CAC7BsD,IAAI,CAAEH,YAAY,CAACG,IACrB,CAAC,CAED;AACA,KAAM,CAAAmV,gBAAgB,CAAG,GAAI,CAAAjmB,KAAK,CAAC4gB,cAAc,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CAC5D,KAAM,CAAAsF,gBAAgB,CAAG,GAAI,CAAAlmB,KAAK,CAACmgB,iBAAiB,CAAC,CACnDhS,KAAK,CAAE,QAAQ,CACf2G,WAAW,CAAE,IAAI,CACjBU,OAAO,CAAE,GAAG,CAAG;AACfsL,UAAU,CAAE,KACd,CAAC,CAAC,CAEF,KAAM,CAAAqF,QAAQ,CAAG,GAAI,CAAAnmB,KAAK,CAACogB,IAAI,CAAC6F,gBAAgB,CAAEC,gBAAgB,CAAC,CACnEC,QAAQ,CAACrV,IAAI,CAAG,YAAYH,YAAY,CAACG,IAAI,EAAE,CAC/CqV,QAAQ,CAACnF,QAAQ,CAAG,CAClBrO,IAAI,CAAE,cAAc,CACpBnF,OAAO,CAAEmD,YAAY,CAACnD,OAAO,CAC7BsD,IAAI,CAAEH,YAAY,CAACG,IAAI,CACvBsV,UAAU,CAAE,IACd,CAAC,CAEDnP,iBAAiB,CAACzP,GAAG,CAAC2e,QAAQ,CAAC,CAE/B;AACAtkB,KAAK,CAAC2F,GAAG,CAACyP,iBAAiB,CAAC,CAE5B;AACAxT,gBAAgB,CAACgC,GAAG,CAACkL,YAAY,CAACnD,OAAO,CAAE,CACzCjG,KAAK,CAAE0P,iBAAiB,CACxBtG,YAAY,CAAEA,YAAY,CAC1BzE,QAAQ,CAAEgH,QACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAmT,aAAa,CAAG,GAAI,CAAArmB,KAAK,CAAC4gB,cAAc,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CACzD,KAAM,CAAA0F,aAAa,CAAG,GAAI,CAAAtmB,KAAK,CAACmgB,iBAAiB,CAAC,CAAEhS,KAAK,CAAE,QAAS,CAAC,CAAC,CACtE,KAAM,CAAAoY,SAAS,CAAG,GAAI,CAAAvmB,KAAK,CAACogB,IAAI,CAACiG,aAAa,CAAEC,aAAa,CAAC,CAC9DC,SAAS,CAACra,QAAQ,CAACzG,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAEhC;AACA8gB,SAAS,CAACvF,QAAQ,CAAG,CACnBrO,IAAI,CAAE,cAAc,CACpBnF,OAAO,CAAEmD,YAAY,CAACnD,OAAO,CAC7BsD,IAAI,CAAEH,YAAY,CAACG,IACrB,CAAC,CAEDmG,iBAAiB,CAACzP,GAAG,CAAC+e,SAAS,CAAC,CAEhCnjB,OAAO,CAACC,GAAG,CAAC,SAASsN,YAAY,CAACG,IAAI,KAAKH,YAAY,CAACnD,OAAO,kBAAkB0F,QAAQ,CAAC9N,CAAC,KAAK,CAAC8N,QAAQ,CAAC5N,CAAC,GAAG,CAAC,CACjH,CAAC,CAED;AACA,KAAM,CAAAkR,iBAAiB,CAAIL,OAAO,EAAK,CACrC,OAAOA,OAAO,EACZ,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,GAAG,CAAE,MAAO,OAAO,CACxB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,QAAS,MAAO,KAAKA,OAAO,EAAE,CAChC,CACF,CAAC,CAED;AACA,KAAM,CAAAI,oBAAoB,CAAIiQ,OAAO,EAAK,CACxC,OAAOA,OAAO,EACZ,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,GAAG,CAAE,MAAO,KAAK,CACtB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,IAAK,IAAI,CAAE,MAAO,OAAO,CACzB,QAAS,MAAO,KAAKA,OAAO,EAAE,CAChC,CACF,CAAC,CAED;AACA,KAAM,CAAAzG,gBAAgB,CAAGA,CAACnI,KAAK,CAAE6O,SAAS,CAAEC,aAAa,CAAEC,cAAc,GAAK,CAC5E,GAAI,CAACF,SAAS,EAAI,CAACC,aAAa,EAAI,CAACC,cAAc,CAAE,OAErDvjB,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEuU,KAAK,CAACgP,OAAO,CAAEhP,KAAK,CAACiP,OAAO,CAAC,CAEvD;AACA,KAAM,CAAAC,IAAI,CAAGL,SAAS,CAACM,qBAAqB,CAAC,CAAC,CAC9C,KAAM,CAAAC,MAAM,CAAI,CAACpP,KAAK,CAACgP,OAAO,CAAGE,IAAI,CAAC1a,IAAI,EAAIqa,SAAS,CAACQ,WAAW,CAAI,CAAC,CAAG,CAAC,CAC5E,KAAM,CAAAC,MAAM,CAAG,EAAE,CAACtP,KAAK,CAACiP,OAAO,CAAGC,IAAI,CAAC/Y,GAAG,EAAI0Y,SAAS,CAACU,YAAY,CAAC,CAAG,CAAC,CAAG,CAAC,CAE7E;AACA,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAApnB,KAAK,CAACqnB,SAAS,CAAC,CAAC,CACvC;AACAD,SAAS,CAAChF,MAAM,CAACkF,MAAM,CAACC,SAAS,CAAG,CAAC,CACrCH,SAAS,CAAChF,MAAM,CAACoF,IAAI,CAACD,SAAS,CAAG,CAAC,CAEnC,KAAM,CAAAE,WAAW,CAAG,GAAI,CAAAznB,KAAK,CAAC0nB,OAAO,CAACV,MAAM,CAAEE,MAAM,CAAC,CACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,CAAEd,cAAc,CAAC,CAEpDvjB,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE2jB,MAAM,CAAEE,MAAM,CAAC,CAEtC;AACA,KAAM,CAAAU,mBAAmB,CAAG,EAAE,CAE9BnkB,gBAAgB,CAACuE,OAAO,CAAC,CAAC0Y,QAAQ,CAAElT,OAAO,GAAK,CAC9C,GAAIkT,QAAQ,CAACnZ,KAAK,CAAE,CAClB;AACAqgB,mBAAmB,CAAC9Q,IAAI,CAAC4J,QAAQ,CAACnZ,KAAK,CAAC,CACxC;AACAmZ,QAAQ,CAACnZ,KAAK,CAACgG,OAAO,CAAG,IAAI,CAC7BmT,QAAQ,CAACnZ,KAAK,CAACgO,WAAW,CAAG,IAAI,CAAE;AACnC;AACAmL,QAAQ,CAACnZ,KAAK,CAACE,QAAQ,CAACiN,KAAK,EAAI,CAC/BA,KAAK,CAACnH,OAAO,CAAG,IAAI,CACpBmH,KAAK,CAACa,WAAW,CAAG,IAAI,CAC1B,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEFnS,OAAO,CAACC,GAAG,CAAC,QAAQukB,mBAAmB,CAACrf,MAAM,gBAAgB,CAAC,CAE/D;AACA,KAAM,CAAAsf,sBAAsB,CAAGT,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,CAAE,IAAI,CAAC,CAEpF,GAAIC,sBAAsB,CAACtf,MAAM,CAAG,CAAC,CAAE,CACrCnF,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEwkB,sBAAsB,CAACtf,MAAM,CAAC,CACxDsf,sBAAsB,CAAC7f,OAAO,CAAC,CAAC+f,SAAS,CAAEC,KAAK,GAAK,CACnD5kB,OAAO,CAACC,GAAG,CAAC,QAAQ2kB,KAAK,GAAG,CACjBD,SAAS,CAACrgB,MAAM,CAACoJ,IAAI,EAAI,KAAK,CAC9B,KAAK,CAAEiX,SAAS,CAACliB,QAAQ,CACzB,WAAW,CAAEkiB,SAAS,CAACrgB,MAAM,CAACsZ,QAAQ,CAAC,CACpD,CAAC,CAAC,CAEF;AACA,KAAM,CAAA/B,GAAG,CAAGgJ,yBAAyB,CAACJ,sBAAsB,CAAC,CAAC,CAAC,CAACngB,MAAM,CAAC,CAEvE,GAAIuX,GAAG,EAAIA,GAAG,CAAC+B,QAAQ,EAAI/B,GAAG,CAAC+B,QAAQ,CAACrO,IAAI,GAAK,cAAc,CAAE,CAC/D;AACA,KAAM,CAAAnF,OAAO,CAAGyR,GAAG,CAAC+B,QAAQ,CAACxT,OAAO,CACpCpK,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAEmK,OAAO,CAAE,KAAK,CAAE,MAAO,CAAAA,OAAO,CAAC,CAEhE;AACA,GAAI,CAAA0a,SAAS,CAAG1a,OAAO,CACvB,GAAI,MAAO,CAAAA,OAAO,GAAK,QAAQ,EAAI,CAAC/J,gBAAgB,CAACiC,GAAG,CAAC8H,OAAO,CAAC,EAAI/J,gBAAgB,CAACiC,GAAG,CAACkR,QAAQ,CAACpJ,OAAO,CAAC,CAAC,CAAE,CAC5G0a,SAAS,CAAGtR,QAAQ,CAACpJ,OAAO,CAAC,CAC7BpK,OAAO,CAACC,GAAG,CAAC,cAAc6kB,SAAS,EAAE,CAAC,CACxC,CAAC,IAAM,IAAI,MAAO,CAAA1a,OAAO,GAAK,QAAQ,EAAI,CAAC/J,gBAAgB,CAACiC,GAAG,CAAC8H,OAAO,CAAC,EAAI/J,gBAAgB,CAACiC,GAAG,CAACsR,MAAM,CAACxJ,OAAO,CAAC,CAAC,CAAE,CACjH0a,SAAS,CAAGlR,MAAM,CAACxJ,OAAO,CAAC,CAC3BpK,OAAO,CAACC,GAAG,CAAC,eAAe6kB,SAAS,EAAE,CAAC,CACzC,CAEA;AACA1lB,MAAM,CAACkP,qBAAqB,CAACwW,SAAS,CAAC,CACvC,OACF,CACF,CAEA;AACA,KAAM,CAAAC,UAAU,CAAGf,SAAS,CAACU,gBAAgB,CAACpB,aAAa,CAACrK,QAAQ,CAAE,IAAI,CAAC,CAE3EjZ,OAAO,CAACC,GAAG,CAAC,aAAa,CAAE8kB,UAAU,CAAC5f,MAAM,CAAC,CAE7C,GAAI4f,UAAU,CAAC5f,MAAM,CAAG,CAAC,CAAE,CACzB;AACA4f,UAAU,CAACngB,OAAO,CAAC,CAAC+f,SAAS,CAAEC,KAAK,GAAK,CACvC,KAAM,CAAA/I,GAAG,CAAG8I,SAAS,CAACrgB,MAAM,CAC5BtE,OAAO,CAACC,GAAG,CAAC,UAAU2kB,KAAK,GAAG,CAAE/I,GAAG,CAACnO,IAAI,EAAI,KAAK,CACrC,WAAW,CAAEmO,GAAG,CAAC+B,QAAQ,CACzB,KAAK,CAAE+G,SAAS,CAACliB,QAAQ,CAAC,CACxC,CAAC,CAAC,CAEF;AACA,IAAK,GAAI,CAAAgL,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGsX,UAAU,CAAC5f,MAAM,CAAEsI,CAAC,EAAE,CAAE,CAC1C,KAAM,CAAAoO,GAAG,CAAGgJ,yBAAyB,CAACE,UAAU,CAACtX,CAAC,CAAC,CAACnJ,MAAM,CAAC,CAC3D,GAAIuX,GAAG,EAAIA,GAAG,CAAC+B,QAAQ,EAAI/B,GAAG,CAAC+B,QAAQ,CAACrO,IAAI,GAAK,cAAc,CAAE,CAC/D,KAAM,CAAAnF,OAAO,CAAGyR,GAAG,CAAC+B,QAAQ,CAACxT,OAAO,CACpChL,MAAM,CAACkP,qBAAqB,CAAClE,OAAO,CAAC,CACrC,OACF,CACF,CACF,CAEA;AACApK,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAE9B;AACA,GAAI,CAAA+kB,YAAY,CAAG,IAAI,CACvB,GAAI,CAAApY,WAAW,CAAG,GAAG,CAAE;AAEvBvM,gBAAgB,CAACuE,OAAO,CAAC,CAAC0Y,QAAQ,CAAElT,OAAO,GAAK,CAC9C,GAAIkT,QAAQ,CAACnZ,KAAK,CAAE,CAClB,KAAM,CAAA8gB,QAAQ,CAAG,GAAI,CAAAroB,KAAK,CAACkG,OAAO,CAAC,CAAC,CACpC;AACAwa,QAAQ,CAACnZ,KAAK,CAAC+gB,gBAAgB,CAACD,QAAQ,CAAC,CAEzC;AACA,KAAM,CAAAE,SAAS,CAAGF,QAAQ,CAACnjB,KAAK,CAAC,CAAC,CAClCqjB,SAAS,CAACC,OAAO,CAAC7B,cAAc,CAAC,CAEjC;AACA,KAAM,CAAA8B,EAAE,CAAGF,SAAS,CAACnjB,CAAC,CAAG4hB,MAAM,CAC/B,KAAM,CAAA0B,EAAE,CAAGH,SAAS,CAACjjB,CAAC,CAAG4hB,MAAM,CAC/B,KAAM,CAAArhB,QAAQ,CAAGS,IAAI,CAACqiB,IAAI,CAACF,EAAE,CAAGA,EAAE,CAAGC,EAAE,CAAGA,EAAE,CAAC,CAE7CtlB,OAAO,CAACC,GAAG,CAAC,MAAMmK,OAAO,OAAO,CAAE3H,QAAQ,CAAC,CAE3C,GAAIA,QAAQ,CAAGmK,WAAW,CAAE,CAC1BA,WAAW,CAAGnK,QAAQ,CACtBuiB,YAAY,CAAG,CAAE5a,OAAO,CAAE3H,QAAS,CAAC,CACtC,CACF,CACF,CAAC,CAAC,CAEF,GAAIuiB,YAAY,CAAE,CAChBhlB,OAAO,CAACC,GAAG,CAAC,oBAAoB+kB,YAAY,CAAC5a,OAAO,SAAS4a,YAAY,CAACviB,QAAQ,EAAE,CAAC,CAErF;AACArD,MAAM,CAACkP,qBAAqB,CAAC0W,YAAY,CAAC5a,OAAO,CAAC,CAClD,OACF,CAEApK,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAA4e,kBAAkB,CAAI2G,eAAe,EAAK,CAC9C;AACA,GAAIpmB,MAAM,CAACoL,0BAA0B,EAAIpL,MAAM,CAACoL,0BAA0B,CAACiB,OAAO,CAAE,CAClF6Q,aAAa,CAACld,MAAM,CAACoL,0BAA0B,CAACiB,OAAO,CAAC,CACxDrM,MAAM,CAACoL,0BAA0B,CAACiB,OAAO,CAAG,IAAI,CAChDzL,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC9B,CAEA;AACA,GAAIb,MAAM,CAACmL,mBAAmB,CAAE,CAC9BnL,MAAM,CAACmL,mBAAmB,CAACkB,OAAO,CAAG,IAAI,CAC3C,CAEA;AACA+Z,eAAe,CAACzR,IAAI,GAAK,CACvB,GAAGA,IAAI,CACP5J,OAAO,CAAE,KAAK,CACdE,OAAO,CAAE,IAAI,CAAE;AACfC,MAAM,CAAE,EAAO;AACjB,CAAC,CAAC,CAAC,CAEHtK,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAChC,CAAC,CAED;AACAb,MAAM,CAACqmB,qBAAqB,CAAIrb,OAAO,EAAK,CAC1C,GAAI,KAAAsb,qBAAA,CAAAC,sBAAA,CACF;AACA,KAAM,CAAArS,YAAY,CAAGjT,gBAAgB,CAACmC,GAAG,CAAC4H,OAAO,EAAI,GAAG,CAAC,CACzD,GAAI,CAACkJ,YAAY,CAAE,CACjBtT,OAAO,CAACsB,KAAK,CAAC,cAAc,CAAE8I,OAAO,CAAC,CAEtC;AACApK,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxBI,gBAAgB,CAACuE,OAAO,CAAC,CAACghB,KAAK,CAAEvW,EAAE,GAAK,CACtCrP,OAAO,CAACC,GAAG,CAAC,KAAKoP,EAAE,KAAKuW,KAAK,CAACrY,YAAY,CAACG,IAAI,EAAE,CAAC,CACpD,CAAC,CAAC,CAEF,MAAO,MAAK,CACd,CAEA;AACA,KAAM,CAAAmY,UAAU,CAAGvS,YAAY,CAACnP,KAAK,CAErC;AACA,KAAM,CAAA2hB,SAAS,CAAGxlB,kBAAkB,CAACkC,GAAG,CAAC4H,OAAO,CAAC,CACjD,KAAM,CAAAmD,YAAY,CAAG+F,YAAY,CAAC/F,YAAY,CAE9C;AACA,GAAI,CAAAlD,OAAO,CAEX,GAAIyb,SAAS,EAAIA,SAAS,CAACxb,MAAM,CAAE,CACjCD,OAAO,cACL5M,KAAA,QAAKugB,KAAK,CAAE,CAAE1U,OAAO,CAAE,KAAK,CAAEsB,KAAK,CAAE,OAAO,CAAE2T,SAAS,CAAE,OAAO,CAAEwH,SAAS,CAAE,MAAO,CAAE,CAAA9M,QAAA,eACpFxb,KAAA,QAAKugB,KAAK,CAAE,CACVhT,UAAU,CAAE,MAAM,CAClBgb,YAAY,CAAE,KAAK,CACnBrc,QAAQ,CAAE,MAAM,CAChBsc,YAAY,CAAE,gBAAgB,CAC9BC,aAAa,CAAE,KACjB,CAAE,CAAAjN,QAAA,EACC1L,YAAY,CAACG,IAAI,CAAC,QAAM,CAACtD,OAAO,CAAC,GACpC,EAAK,CAAC,cACN7M,IAAA,QAAA0b,QAAA,CACG6M,SAAS,CAACxb,MAAM,CAAC7E,GAAG,CAAC,CAACqN,KAAK,CAAE8R,KAAK,GAAK,CACtC,GAAI,CAAAuB,UAAU,CACd,OAAQrT,KAAK,CAACQ,YAAY,EACxB,IAAK,GAAG,CAAE6S,UAAU,CAAG,SAAS,CAAE,MAClC,IAAK,GAAG,CAAEA,UAAU,CAAG,SAAS,CAAE,MAClC,IAAK,GAAG,CAAE,QAASA,UAAU,CAAG,SAAS,CAAE,MAC7C,CAEA,mBACE1oB,KAAA,QAAiBugB,KAAK,CAAE,CACtBgI,YAAY,CAAE,KAAK,CACnBzc,eAAe,CAAE,uBAAuB,CACxCD,OAAO,CAAE,KAAK,CACdG,YAAY,CAAE,KAAK,CACnBE,QAAQ,CAAE,MACZ,CAAE,CAAAsP,QAAA,eACA1b,IAAA,QAAKygB,KAAK,CAAE,CAAEhT,UAAU,CAAE,MAAO,CAAE,CAAAiO,QAAA,CAChC7F,iBAAiB,CAACN,KAAK,CAACC,OAAO,CAAC,CAC9B,CAAC,cACNtV,KAAA,QAAKugB,KAAK,CAAE,CAAE7U,OAAO,CAAE,MAAM,CAAEid,cAAc,CAAE,eAAgB,CAAE,CAAAnN,QAAA,eAC/D1b,IAAA,SAAA0b,QAAA,CAAM,gBAAI,CAAM,CAAC,cACjB1b,IAAA,SAAMygB,KAAK,CAAE,CACXjT,KAAK,CAAEob,UAAU,CACjBnb,UAAU,CAAE,MAAM,CAClBzB,eAAe,CAAE,iBAAiB,CAClCD,OAAO,CAAE,OAAO,CAChBG,YAAY,CAAE,KAChB,CAAE,CAAAwP,QAAA,CACCnG,KAAK,CAACQ,YAAY,GAAK,GAAG,CAAG,IAAI,CAAGR,KAAK,CAACQ,YAAY,GAAK,GAAG,CAAG,IAAI,CAAG,IAAI,CACzE,CAAC,EACJ,CAAC,cACN7V,KAAA,QAAKugB,KAAK,CAAE,CAAE7U,OAAO,CAAE,MAAM,CAAEid,cAAc,CAAE,eAAgB,CAAE,CAAAnN,QAAA,eAC/D1b,IAAA,SAAA0b,QAAA,CAAM,sBAAK,CAAM,CAAC,cAClBxb,KAAA,SAAMugB,KAAK,CAAE,CAAEhT,UAAU,CAAE,MAAO,CAAE,CAAAiO,QAAA,EAAEnG,KAAK,CAACS,UAAU,CAAC,SAAE,EAAM,CAAC,EAC7D,CAAC,GAzBEqR,KA0BL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,cACNnnB,KAAA,QAAKugB,KAAK,CAAE,CAAEqI,SAAS,CAAE,KAAK,CAAE1c,QAAQ,CAAE,MAAM,CAAEoB,KAAK,CAAE,MAAO,CAAE,CAAAkO,QAAA,EAAC,4BAC3D,CAAC,GAAI,CAAAhR,IAAI,CAAC,CAAC,CAACqe,kBAAkB,CAAC,CAAC,CAAC,6BACzC,EAAK,CAAC,EACH,CACN,CACH,CAAC,IAAM,CACLjc,OAAO,cACL5M,KAAA,QAAKugB,KAAK,CAAE,CAAE1U,OAAO,CAAE,KAAK,CAAEmV,QAAQ,CAAE,OAAQ,CAAE,CAAAxF,QAAA,eAChD1b,IAAA,QAAKygB,KAAK,CAAE,CAAEhT,UAAU,CAAE,MAAM,CAAEgb,YAAY,CAAE,KAAM,CAAE,CAAA/M,QAAA,CAAE1L,YAAY,CAACG,IAAI,CAAM,CAAC,cAClFjQ,KAAA,QAAAwb,QAAA,EAAK,kBAAM,CAAC7O,OAAO,EAAM,CAAC,cAC1B7M,IAAA,QAAA0b,QAAA,CAAK,8DAAU,CAAK,CAAC,EAClB,CACN,CACH,CAEA;AACA,KAAM,CAAAsN,OAAO,CAAGnnB,MAAM,CAACuX,UAAU,CAAG,CAAC,CAAE,GAAG,CAC1C,KAAM,CAAA6P,OAAO,CAAGpnB,MAAM,CAACwX,WAAW,CAAG,CAAC,CAAE,GAAG,CAE3C;AACA,KAAM,CAAA4O,eAAe,EAAAE,qBAAA,CAAGrG,QAAQ,CAAC+B,aAAa,CAAC,OAAO,CAAC,UAAAsE,qBAAA,kBAAAC,sBAAA,CAA/BD,qBAAA,CAAiCe,gBAAgB,UAAAd,sBAAA,iBAAjDA,sBAAA,CAAmDzb,sBAAsB,CAEjG,GAAIsb,eAAe,CAAE,CACnB;AACAA,eAAe,CAAC,CACdrb,OAAO,CAAE,IAAI,CACbC,OAAO,CAAEA,OAAO,CAChBtB,QAAQ,CAAE,CAAE9G,CAAC,CAAEukB,OAAO,CAAErkB,CAAC,CAAEskB,OAAQ,CAAC,CACpCnc,OAAO,CAAEA,OAAO,CAChBC,MAAM,CAAE,CAAAwb,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAExb,MAAM,GAAI,EAC/B,CAAC,CAAC,CAEFtK,OAAO,CAACC,GAAG,CAAC,SAASsN,YAAY,CAACG,IAAI,KAAKtD,OAAO,UAAU,CAAC,CAC7D,MAAO,KAAI,CACb,CAAC,IAAM,CACL;AACA,KAAM,CAAAsc,OAAO,CAAGrH,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAC7CoH,OAAO,CAAC1I,KAAK,CAAClV,QAAQ,CAAG,UAAU,CACnC4d,OAAO,CAAC1I,KAAK,CAAChV,IAAI,CAAG,GAAGud,OAAO,IAAI,CACnCG,OAAO,CAAC1I,KAAK,CAACrT,GAAG,CAAG,GAAG6b,OAAO,IAAI,CAClCE,OAAO,CAAC1I,KAAK,CAAC/U,SAAS,CAAG,wBAAwB,CAClDyd,OAAO,CAAC1I,KAAK,CAAC9U,MAAM,CAAG,MAAM,CAC7Bwd,OAAO,CAAC1I,KAAK,CAACzU,eAAe,CAAG,qBAAqB,CACrDmd,OAAO,CAAC1I,KAAK,CAACjT,KAAK,CAAG,OAAO,CAC7B2b,OAAO,CAAC1I,KAAK,CAACvU,YAAY,CAAG,KAAK,CAClCid,OAAO,CAAC1I,KAAK,CAACpU,SAAS,CAAG,8BAA8B,CACxD8c,OAAO,CAAC1I,KAAK,CAAC1U,OAAO,CAAG,KAAK,CAC7Bod,OAAO,CAAC1I,KAAK,CAACS,QAAQ,CAAG,OAAO,CAChCiI,OAAO,CAAC1I,KAAK,CAACrU,QAAQ,CAAG,MAAM,CAE/B+c,OAAO,CAACC,SAAS,CAAG;AAC1B;AACA,YAAYpZ,YAAY,CAACG,IAAI,SAAStD,OAAO;AAC7C;AACA;AACA,qBAAqBA,OAAO;AAC5B,eAAe0b,SAAS,CAAG,UAAU,CAAG,YAAY;AACpD;AACA;AACA,OAAO,CAEDzG,QAAQ,CAACuH,IAAI,CAACxP,WAAW,CAACsP,OAAO,CAAC,CAElC;AACA,KAAM,CAAAG,WAAW,CAAGH,OAAO,CAACtF,aAAa,CAAC,QAAQ,CAAC,CACnD,GAAIyF,WAAW,CAAE,CACfA,WAAW,CAACrL,gBAAgB,CAAC,OAAO,CAAE,IAAM,CAC1C6D,QAAQ,CAACuH,IAAI,CAAC5K,WAAW,CAAC0K,OAAO,CAAC,CACpC,CAAC,CAAC,CACJ,CAEA1mB,OAAO,CAACC,GAAG,CAAC,gBAAgBsN,YAAY,CAACG,IAAI,KAAKtD,OAAO,OAAO,CAAC,CACjE,MAAO,KAAI,CACb,CACF,CAAE,MAAO9I,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACAlC,MAAM,CAAC0nB,iBAAiB,CAAG,IAAM,CAC/B9mB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CAErB,GAAI,CAACI,gBAAgB,EAAIA,gBAAgB,CAACia,IAAI,GAAK,CAAC,CAAE,CACpDta,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB,MAAO,EAAE,CACX,CAEA,KAAM,CAAA8mB,IAAI,CAAG,EAAE,CACf1mB,gBAAgB,CAACuE,OAAO,CAAC,CAACghB,KAAK,CAAEvW,EAAE,GAAK,CACtCrP,OAAO,CAACC,GAAG,CAAC,SAASoP,EAAE,SAASuW,KAAK,CAACrY,YAAY,CAACG,IAAI,EAAE,CAAC,CAC1DqZ,IAAI,CAACrT,IAAI,CAAC,CACRrE,EAAE,CACF3B,IAAI,CAAEkY,KAAK,CAACrY,YAAY,CAACG,IAAI,CAC7B5E,QAAQ,CAAE8c,KAAK,CAAC9c,QAClB,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,MAAO,CAAAie,IAAI,CACb,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA3nB,MAAM,CAACkP,qBAAqB,CAAIlE,OAAO,EAAK,CAC1C,GAAI,CACF;AACAA,OAAO,CAAGwJ,MAAM,CAACxJ,OAAO,CAAC,CAEzBpK,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAEmK,OAAO,CAAE,KAAK,CAAE,MAAO,CAAAA,OAAO,CAAC,CAC/EpK,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEI,gBAAgB,CAACia,IAAI,CAAC,CAE3D;AACA,GAAI,CAAAhH,YAAY,CAAGjT,gBAAgB,CAACmC,GAAG,CAAC4H,OAAO,CAAC,CAChD,GAAI,CAACkJ,YAAY,CAAE,CACjB;AACA,KAAM,CAAA0T,SAAS,CAAGxT,QAAQ,CAACpJ,OAAO,CAAC,CACnCkJ,YAAY,CAAGjT,gBAAgB,CAACmC,GAAG,CAACwkB,SAAS,CAAC,CAE9C,GAAI1T,YAAY,CAAE,CAChBtT,OAAO,CAACC,GAAG,CAAC,UAAU+mB,SAAS,SAAS,CAAC,CACzC5c,OAAO,CAAG4c,SAAS,CAAE;AACvB,CACF,CAEA,GAAI,CAAC1T,YAAY,CAAE,CACjBtT,OAAO,CAACsB,KAAK,CAAC,cAAc,CAAE8I,OAAO,CAAC,CACtC,MAAO,MAAK,CACd,CAEA,KAAM,CAAA0b,SAAS,CAAGxlB,kBAAkB,CAACkC,GAAG,CAAC4H,OAAO,CAAC,CACjD,KAAM,CAAAmD,YAAY,CAAG+F,YAAY,CAAC/F,YAAY,CAE9C;AACA,KAAM,CAAA0Z,YAAY,CAAGnB,SAAS,EAAIA,SAAS,CAACxb,MAAM,EAAIwb,SAAS,CAACxb,MAAM,CAACnF,MAAM,CAAG,CAAC,CAEjF,GAAI,CAAAkF,OAAO,CAEX;AACA,KAAM,CAAA6c,YAAY,CAAG,CACnBpe,QAAQ,CAAE,UAAU,CACpB6B,GAAG,CAAE,KAAK,CACV+T,KAAK,CAAE,MAAM,CACb9T,KAAK,CAAE,MAAM,CACbqF,MAAM,CAAE,MAAM,CACd9G,OAAO,CAAE,MAAM,CACfid,cAAc,CAAE,QAAQ,CACxBe,UAAU,CAAE,QAAQ,CACpB1d,YAAY,CAAE,KAAK,CACnBkV,UAAU,CAAE,iBAAiB,CAC7BzV,MAAM,CAAE,EACV,CAAC,CAED;AACA,KAAM,CAAAke,WAAW,CAAGA,CAAA,gBAClB7pB,IAAA,QAAKygB,KAAK,CAAEkJ,YAAa,CAAAjO,QAAA,cACvBxb,KAAA,QAAKugB,KAAK,CAAE,CACV7U,OAAO,CAAE,MAAM,CACfke,aAAa,CAAE,QAAQ,CACvBF,UAAU,CAAE,QAAQ,CACpBle,SAAS,CAAE,cACb,CAAE,CAAAgQ,QAAA,eACA1b,IAAA,SAAMygB,KAAK,CAAE,CACXjT,KAAK,CAAE,SAAS,CAChBpB,QAAQ,CAAE,MAAM,CAChBqB,UAAU,CAAE,MAAM,CAClBF,UAAU,CAAE,MACd,CAAE,CAAAmO,QAAA,CAAC,GAAC,CAAM,CAAC,cACX1b,IAAA,SAAMygB,KAAK,CAAE,CACXpT,KAAK,CAAE,CAAC,CACRqF,MAAM,CAAE,CAAC,CACTqX,UAAU,CAAE,uBAAuB,CACnCC,WAAW,CAAE,uBAAuB,CACpCtB,YAAY,CAAE,oBAAoB,CAClCI,SAAS,CAAE,MACb,CAAE,CAAO,CAAC,EACP,CAAC,CACH,CACN,CAED,GAAIY,YAAY,CAAE,CAChB;AACA,KAAM,CAAAO,QAAQ,CAAG,CACf,GAAG,CAAE,CAAEC,GAAG,CAAE,GAAG,CAAElY,IAAI,CAAE,MAAO,CAAC,CAAE,GAAG,CAAE,CAAEkY,GAAG,CAAE,GAAG,CAAElY,IAAI,CAAE,UAAW,CAAC,CAAE,GAAG,CAAE,CAAEkY,GAAG,CAAE,GAAG,CAAElY,IAAI,CAAE,OAAQ,CAAC,CACtG,GAAG,CAAE,CAAEkY,GAAG,CAAE,GAAG,CAAElY,IAAI,CAAE,MAAO,CAAC,CAAE,GAAG,CAAE,CAAEkY,GAAG,CAAE,GAAG,CAAElY,IAAI,CAAE,UAAW,CAAC,CAAE,GAAG,CAAE,CAAEkY,GAAG,CAAE,GAAG,CAAElY,IAAI,CAAE,OAAQ,CAAC,CACtG,GAAG,CAAE,CAAEkY,GAAG,CAAE,GAAG,CAAElY,IAAI,CAAE,MAAO,CAAC,CAAE,IAAI,CAAE,CAAEkY,GAAG,CAAE,GAAG,CAAElY,IAAI,CAAE,UAAW,CAAC,CAAE,IAAI,CAAE,CAAEkY,GAAG,CAAE,GAAG,CAAElY,IAAI,CAAE,OAAQ,CAAC,CACxG,IAAI,CAAE,CAAEkY,GAAG,CAAE,GAAG,CAAElY,IAAI,CAAE,MAAO,CAAC,CAAE,IAAI,CAAE,CAAEkY,GAAG,CAAE,GAAG,CAAElY,IAAI,CAAE,UAAW,CAAC,CAAE,IAAI,CAAE,CAAEkY,GAAG,CAAE,GAAG,CAAElY,IAAI,CAAE,OAAQ,CAC1G,CAAC,CAED,KAAM,CAAAmY,SAAS,CAAG,CAAC,MAAM,CAAE,UAAU,CAAE,OAAO,CAAC,CAC/C,KAAM,CAAAC,QAAQ,CAAG,CAAEC,CAAC,CAAE,SAAS,CAAEC,CAAC,CAAE,SAAS,CAAEC,CAAC,CAAE,SAAU,CAAC,CAC7D,KAAM,CAAAC,OAAO,CAAG,CAAEC,CAAC,CAAE,CAAC,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAE,CAAC,CAE9CrC,SAAS,CAACxb,MAAM,CAAC1F,OAAO,CAACkO,KAAK,EAAI,CAChC,KAAM,CAAArN,GAAG,CAAG+hB,QAAQ,CAAC1U,KAAK,CAACC,OAAO,CAAC,CACnC,GAAItN,GAAG,CAAE,CACPsiB,OAAO,CAACtiB,GAAG,CAACgiB,GAAG,CAAC,CAAChiB,GAAG,CAAC8J,IAAI,CAAC,CAAG,CAC3BxE,KAAK,CAAE4c,QAAQ,CAAC7U,KAAK,CAACQ,YAAY,CAAC,EAAI,MAAM,CAC7CC,UAAU,CAAET,KAAK,CAACS,UACpB,CAAC,CACH,CACF,CAAC,CAAC,CAEFlJ,OAAO,cACL5M,KAAA,QAAKugB,KAAK,CAAE,CAAE1U,OAAO,CAAE,KAAK,CAAEsB,KAAK,CAAE,OAAO,CAAE+T,UAAU,CAAE,kBAAkB,CAAE7V,QAAQ,CAAE,UAAW,CAAE,CAAAmQ,QAAA,eACnG1b,IAAA,CAAC6pB,WAAW,GAAE,CAAC,cACf3pB,KAAA,QAAKugB,KAAK,CAAE,CAAEhT,UAAU,CAAE,MAAM,CAAEgb,YAAY,CAAE,KAAK,CAAErc,QAAQ,CAAE,MAAM,CAAE6W,SAAS,CAAE,QAAS,CAAE,CAAAvH,QAAA,EAAE1L,YAAY,CAACG,IAAI,CAAC,cAAE,EAAK,CAAC,cAC3HjQ,KAAA,QAAKugB,KAAK,CAAE,CACV7U,OAAO,CAAE,MAAM,CACfif,gBAAgB,CAAE,gBAAgB,CAClCC,mBAAmB,CAAE,gBAAgB,CACrCjC,cAAc,CAAE,QAAQ,CACxBe,UAAU,CAAE,QAAQ,CACpBxI,UAAU,CAAE,wBAAwB,CACpClV,YAAY,CAAE,KAAK,CACnB6e,MAAM,CAAE,QAAQ,CAChBxf,QAAQ,CAAE,UACZ,CAAE,CAAAmQ,QAAA,eAGA1b,IAAA,QAAKygB,KAAK,CAAE,CAAEuK,OAAO,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAEhI,SAAS,CAAE,QAAQ,CAAErX,OAAO,CAAE,MAAM,CAAEid,cAAc,CAAE,QAAQ,CAAExb,KAAK,CAAE,MAAO,CAAE,CAAAqO,QAAA,CACtHyO,SAAS,CAACjiB,GAAG,CAAC,CAAC8J,IAAI,CAAEqV,KAAK,GAAK,CAC9B;AACA,GAAI,CAAA6D,YAAY,CAAG7D,KAAK,CACxB,GAAIA,KAAK,GAAK,CAAC,CAAE6D,YAAY,CAAG,CAAC,CAAC,IAC7B,IAAI7D,KAAK,GAAK,CAAC,CAAE6D,YAAY,CAAG,CAAC,CAEtC,KAAM,CAAAC,WAAW,CAAGhB,SAAS,CAACe,YAAY,CAAC,CAE3C;AACA,KAAM,CAAAE,WAAW,CAAG,CAAC,CAAC,CACtB,GAAID,WAAW,GAAK,MAAM,CAAE,CAAE;AAC5BC,WAAW,CAACC,WAAW,CAAG,KAAK,CACjC,CAAC,IAAM,IAAIF,WAAW,GAAK,UAAU,CAAE,CAAE;AACvCC,WAAW,CAACE,UAAU,CAAG,MAAM,CAC/BF,WAAW,CAACC,WAAW,CAAG,MAAM,CAClC,CAAC,IAAM,IAAIF,WAAW,GAAK,OAAO,CAAE,CAAE;AACpCC,WAAW,CAACE,UAAU,CAAG,KAAK,CAChC,CAEA,MAAO,CAAAd,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,eAC3B;AACA;AACA;AACA;AACA;AACA;AACAjrB,KAAA,QAAuBugB,KAAK,CAAE,CAAC4K,WAAW,CAAEF,WAAW,GAAK,MAAM,CAAG,CAAC,CAAG,MAAM,CAAEvf,OAAO,CAAE,MAAM,CAAEke,aAAa,CAAE,QAAQ,CAAEF,UAAU,CAAE,QAAQ,CAAE,CAAAlO,QAAA,eAC/I1b,IAAA,QAAKygB,KAAK,CAAE,CAACrU,QAAQ,CAAC,MAAM,CAAEoB,KAAK,CAAEgd,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAAC3d,KAAK,CAAEC,UAAU,CAAC,MAAM,CAAEgb,YAAY,CAAE,KAAK,CAAE,CAAA/M,QAAA,CAAE8O,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAACnV,UAAU,CAAM,CAAC,cACrJhW,IAAA,SAAMygB,KAAK,CAAE,CAACjT,KAAK,CAAEgd,OAAO,CAACC,CAAC,CAACU,WAAW,CAAC,CAAC3d,KAAK,CAAEpB,QAAQ,CAAC,MAAM,CAAEmB,UAAU,CAAE,MAAM,CAAE,CAAAmO,QAAA,CACrFyP,WAAW,GAAK,MAAM,CAAG,WAAW,CAAGA,WAAW,GAAK,UAAU,CAAG,WAAW,CAAG,WAAW,CAC1F,CAAC,GAJCA,WAKL,CACN,CACH,CAAC,CAAC,CACC,CAAC,cAGNnrB,IAAA,QAAKygB,KAAK,CAAE,CAAEuK,OAAO,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAEhI,SAAS,CAAE,QAAQ,CAAErX,OAAO,CAAE,MAAM,CAAEid,cAAc,CAAE,QAAQ,CAAExb,KAAK,CAAE,MAAO,CAAE,CAAAqO,QAAA,CACtHyO,SAAS,CAACjiB,GAAG,CAAC8J,IAAI,EAAIwY,OAAO,CAACG,CAAC,CAAC3Y,IAAI,CAAC,eACpC9R,KAAA,QAAgBugB,KAAK,CAAE,CAAC4K,WAAW,CAAErZ,IAAI,GAAK,OAAO,CAAG,CAAC,CAAG,MAAM,CAAEpG,OAAO,CAAE,MAAM,CAAEke,aAAa,CAAE,QAAQ,CAAEF,UAAU,CAAE,QAAQ,CAAE,CAAAlO,QAAA,eAClI1b,IAAA,SAAMygB,KAAK,CAAE,CAACjT,KAAK,CAAEgd,OAAO,CAACG,CAAC,CAAC3Y,IAAI,CAAC,CAACxE,KAAK,CAAEpB,QAAQ,CAAC,MAAM,CAAEmB,UAAU,CAAE,MAAM,CAAE,CAAAmO,QAAA,CAC9E1J,IAAI,GAAK,MAAM,CAAG,WAAW,CAAGA,IAAI,GAAK,UAAU,CAAG,WAAW,CAAG,WAAW,CAC5E,CAAC,cACPhS,IAAA,QAAKygB,KAAK,CAAE,CAACrU,QAAQ,CAAC,MAAM,CAAEoB,KAAK,CAAEgd,OAAO,CAACG,CAAC,CAAC3Y,IAAI,CAAC,CAACxE,KAAK,CAAEC,UAAU,CAAC,MAAM,CAAEqb,SAAS,CAAE,KAAK,CAAE,CAAApN,QAAA,CAAE8O,OAAO,CAACG,CAAC,CAAC3Y,IAAI,CAAC,CAACgE,UAAU,CAAM,CAAC,GAJ5HhE,IAKL,CACN,CAAC,CACC,CAAC,cAINhS,IAAA,QAAKygB,KAAK,CAAE,CAAEuK,OAAO,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAEhI,SAAS,CAAE,QAAS,CAAE,CAAAvH,QAAA,CAC5DyO,SAAS,CAACjiB,GAAG,CAAC,CAAC8J,IAAI,CAAEqV,KAAK,GAAK,CAC9B;AACA,GAAI,CAAA6D,YAAY,CAAG7D,KAAK,CACxB,GAAIA,KAAK,GAAK,CAAC,CAAE6D,YAAY,CAAG,CAAC,CAAC,IAC7B,IAAI7D,KAAK,GAAK,CAAC,CAAE6D,YAAY,CAAG,CAAC,CAEtC,KAAM,CAAAC,WAAW,CAAGhB,SAAS,CAACe,YAAY,CAAC,CAE3C,MAAO,CAAAV,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,eAC3BjrB,KAAA,QAAuBugB,KAAK,CAAE,CAACgI,YAAY,CAAC,KAAK,CAAE7c,OAAO,CAAE,MAAM,CAAEge,UAAU,CAAE,QAAQ,CAAEf,cAAc,CAAE,YAAY,CAAE,CAAAnN,QAAA,eACtH1b,IAAA,SAAMygB,KAAK,CAAE,CAACjT,KAAK,CAAEgd,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAAC3d,KAAK,CAAEpB,QAAQ,CAAC,MAAM,CAAEmB,UAAU,CAAE,MAAM,CAAE,CAAAmO,QAAA,CACrFyP,WAAW,GAAK,MAAM,CAAG,WAAW,CAAGA,WAAW,GAAK,UAAU,CAAG,WAAW,CAAG,WAAW,CAC1F,CAAC,cACPnrB,IAAA,QAAKygB,KAAK,CAAE,CACVrU,QAAQ,CAAC,MAAM,CACfoB,KAAK,CAAEgd,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAAC3d,KAAK,CACnCC,UAAU,CAAC,MAAM,CACjB6d,UAAU,CAAE,KACd,CAAE,CAAA5P,QAAA,CAAE8O,OAAO,CAACE,CAAC,CAACS,WAAW,CAAC,CAACnV,UAAU,CAAM,CAAC,GATpCmV,WAUL,CACN,CACH,CAAC,CAAC,CACC,CAAC,cAGNnrB,IAAA,QAAKygB,KAAK,CAAE,CAAEuK,OAAO,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAC,CAAEhI,SAAS,CAAE,QAAS,CAAE,CAAAvH,QAAA,CAC5DyO,SAAS,CAACjiB,GAAG,CAAC8J,IAAI,EAAIwY,OAAO,CAACI,CAAC,CAAC5Y,IAAI,CAAC,eACpC9R,KAAA,QAAgBugB,KAAK,CAAE,CAACgI,YAAY,CAAC,KAAK,CAAE7c,OAAO,CAAE,MAAM,CAAEge,UAAU,CAAE,QAAQ,CAAEf,cAAc,CAAE,UAAU,CAAE,CAAAnN,QAAA,eAC7G1b,IAAA,QAAKygB,KAAK,CAAE,CACVrU,QAAQ,CAAC,MAAM,CACfoB,KAAK,CAAEgd,OAAO,CAACI,CAAC,CAAC5Y,IAAI,CAAC,CAACxE,KAAK,CAC5BC,UAAU,CAAC,MAAM,CACjB4d,WAAW,CAAE,KACf,CAAE,CAAA3P,QAAA,CAAE8O,OAAO,CAACI,CAAC,CAAC5Y,IAAI,CAAC,CAACgE,UAAU,CAAM,CAAC,cACrChW,IAAA,SAAMygB,KAAK,CAAE,CAACjT,KAAK,CAAEgd,OAAO,CAACI,CAAC,CAAC5Y,IAAI,CAAC,CAACxE,KAAK,CAAEpB,QAAQ,CAAC,MAAM,CAAEmB,UAAU,CAAE,MAAM,CAAE,CAAAmO,QAAA,CAC9E1J,IAAI,GAAK,MAAM,CAAG,WAAW,CAAGA,IAAI,GAAK,UAAU,CAAG,WAAW,CAAG,WAAW,CAC5E,CAAC,GATCA,IAUL,CACN,CAAC,CACC,CAAC,EACH,CAAC,cACN9R,KAAA,QAAKugB,KAAK,CAAE,CAAEqI,SAAS,CAAE,KAAK,CAAE1c,QAAQ,CAAE,MAAM,CAAEoB,KAAK,CAAE,MAAM,CAAEyV,SAAS,CAAE,QAAS,CAAE,CAAAvH,QAAA,EAAC,4BAChF,CAAC,GAAI,CAAAhR,IAAI,CAAC,CAAC,CAACqe,kBAAkB,CAAC,CAAC,CAAC,6BACzC,EAAK,CAAC,EACH,CACN,CACH,CAAC,IAAM,CACL;AACAjc,OAAO,cACL5M,KAAA,QAAKugB,KAAK,CAAE,CAAE1U,OAAO,CAAE,MAAM,CAAEsB,KAAK,CAAE,OAAO,CAAE+T,UAAU,CAAE,kBAAkB,CAAE7V,QAAQ,CAAE,UAAW,CAAE,CAAAmQ,QAAA,eACpG1b,IAAA,CAAC6pB,WAAW,GAAE,CAAC,cACf7pB,IAAA,QAAKygB,KAAK,CAAE,CAAEhT,UAAU,CAAE,MAAM,CAAEgb,YAAY,CAAE,MAAM,CAAErc,QAAQ,CAAE,MAAM,CAAE6W,SAAS,CAAE,QAAS,CAAE,CAAAvH,QAAA,CAAE1L,YAAY,CAACG,IAAI,CAAM,CAAC,cAC1HnQ,IAAA,QAAKygB,KAAK,CAAE,CACVwC,SAAS,CAAE,QAAQ,CACnBlX,OAAO,CAAE,QAAQ,CACjByB,KAAK,CAAE,SAAS,CAChBpB,QAAQ,CAAE,MAAM,CAChBqB,UAAU,CAAE,MAAM,CAClB2T,UAAU,CAAE,uBAAuB,CACnClV,YAAY,CAAE,KAAK,CACnBuc,YAAY,CAAE,MAChB,CAAE,CAAA/M,QAAA,CAAC,8DAEH,CAAK,CAAC,cACNxb,KAAA,QAAKugB,KAAK,CAAE,CAAErU,QAAQ,CAAE,MAAM,CAAEoB,KAAK,CAAE,MAAM,CAAEyV,SAAS,CAAE,QAAS,CAAE,CAAAvH,QAAA,EAAC,kBAC9D,CAAC7O,OAAO,EACX,CAAC,cACN3M,KAAA,QAAKugB,KAAK,CAAE,CAAEqI,SAAS,CAAE,KAAK,CAAE1c,QAAQ,CAAE,MAAM,CAAEoB,KAAK,CAAE,MAAM,CAAEyV,SAAS,CAAE,QAAS,CAAE,CAAAvH,QAAA,EAAC,4BAChF,CAAC,GAAI,CAAAhR,IAAI,CAAC,CAAC,CAACqe,kBAAkB,CAAC,CAAC,CAAC,6BACzC,EAAK,CAAC,EACH,CACN,CACH,CAEA;AACA,KAAM,CAAAtkB,CAAC,CAAG,GAAG,CACb,KAAM,CAAAE,CAAC,CAAG,GAAG,CAEb;AACA,GAAI9C,MAAM,CAACmL,mBAAmB,CAAE,CAC9BnL,MAAM,CAACmL,mBAAmB,CAACkB,OAAO,CAAGrB,OAAO,CAC9C,CAEA;AACA,GAAIhL,MAAM,CAACoL,0BAA0B,EAAIpL,MAAM,CAACoL,0BAA0B,CAACiB,OAAO,CAAE,CAClF6Q,aAAa,CAACld,MAAM,CAACoL,0BAA0B,CAACiB,OAAO,CAAC,CACxDrM,MAAM,CAACoL,0BAA0B,CAACiB,OAAO,CAAG,IAAI,CAClD,CAEA;AACA,GAAIrM,MAAM,CAACqL,uBAAuB,CAAE,CAClCrL,MAAM,CAACqL,uBAAuB,CAAC,CAC7BN,OAAO,CAAE,IAAI,CACbC,OAAO,CAAEA,OAAO,CAChBtB,QAAQ,CAAE,CAAE9G,CAAC,CAAEE,CAAE,CAAC,CAClBmI,OAAO,CAAEA,OAAO,CAChBC,MAAM,CAAE,CAAAwb,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAExb,MAAM,GAAI,EAC/B,CAAC,CAAC,CAEF;AACA,GAAIlL,MAAM,CAACoL,0BAA0B,CAAE,CACrCpL,MAAM,CAACoL,0BAA0B,CAACiB,OAAO,CAAG2Q,WAAW,CAAC,IAAM,CAC5Dhd,MAAM,CAACkP,qBAAqB,CAAClE,OAAO,CAAC,CACvC,CAAC,CAAE,IAAI,CAAC,CACV,CAEA,MAAO,KAAI,CACb,CAAC,IAAM,CACLpK,OAAO,CAACsB,KAAK,CAAC,8BAA8B,CAAC,CAC7C,MAAO,MAAK,CACd,CACF,CAAE,MAAOA,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA,KAAM,CAAAujB,yBAAyB,CAAIvgB,MAAM,EAAK,CAC5C,GAAI,CAAAmH,OAAO,CAAGnH,MAAM,CAEpB;AACA,GAAImH,OAAO,EAAIA,OAAO,CAACmS,QAAQ,EAAInS,OAAO,CAACmS,QAAQ,CAACrO,IAAI,GAAK,cAAc,CAAE,CAC3EvP,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEwL,OAAO,CAACiC,IAAI,EAAI,KAAK,CAAC,CAChD,MAAO,CAAAjC,OAAO,CAChB,CAEA;AACA,MAAOA,OAAO,EAAIA,OAAO,CAACtF,MAAM,CAAE,CAChCsF,OAAO,CAAGA,OAAO,CAACtF,MAAM,CACxB,GAAIsF,OAAO,CAACmS,QAAQ,EAAInS,OAAO,CAACmS,QAAQ,CAACrO,IAAI,GAAK,cAAc,CAAE,CAChEvP,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEwL,OAAO,CAACiC,IAAI,EAAI,KAAK,CAAC,CAChD,MAAO,CAAAjC,OAAO,CAChB,CACF,CAEA,MAAO,KAAI,CACb,CAAC,CAED;AACArM,MAAM,CAAC0pB,kBAAkB,CAAG,CAAC9mB,CAAC,CAAEE,CAAC,GAAK,CACpC,GAAI,CACFlC,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAE+B,CAAC,CAAEE,CAAC,CAAC,CAEnC;AACA,KAAM,CAAAkd,MAAM,CAAGC,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC,CAC/C,GAAI,CAAChC,MAAM,CAAE,CACXpf,OAAO,CAACsB,KAAK,CAAC,sBAAsB,CAAC,CACrC,MAAO,MAAK,CACd,CAEA;AACA,GAAI,CAAC7C,KAAK,EAAI,CAACqL,SAAS,CAAC2B,OAAO,CAAE,CAChCzL,OAAO,CAACsB,KAAK,CAAC,iBAAiB,CAAC,CAChC,MAAO,MAAK,CACd,CAEA;AACA,GAAIU,CAAC,GAAKyQ,SAAS,EAAIvQ,CAAC,GAAKuQ,SAAS,CAAE,CACtCzQ,CAAC,CAAG5C,MAAM,CAACuX,UAAU,CAAG,CAAC,CACzBzU,CAAC,CAAG9C,MAAM,CAACwX,WAAW,CAAG,CAAC,CAC5B,CAEA;AACA,KAAM,CAAA8M,IAAI,CAAGtE,MAAM,CAACuE,qBAAqB,CAAC,CAAC,CAC3C,KAAM,CAAAC,MAAM,CAAI,CAAC5hB,CAAC,CAAG0hB,IAAI,CAAC1a,IAAI,EAAIoW,MAAM,CAACyE,WAAW,CAAI,CAAC,CAAG,CAAC,CAC7D,KAAM,CAAAC,MAAM,CAAG,EAAE,CAAC5hB,CAAC,CAAGwhB,IAAI,CAAC/Y,GAAG,EAAIyU,MAAM,CAAC2E,YAAY,CAAC,CAAG,CAAC,CAAG,CAAC,CAE9D/jB,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAE2jB,MAAM,CAAEE,MAAM,CAAC,CAErC;AACA,KAAM,CAAAE,SAAS,CAAG,GAAI,CAAApnB,KAAK,CAACqnB,SAAS,CAAC,CAAC,CACvCD,SAAS,CAAChF,MAAM,CAACkF,MAAM,CAACC,SAAS,CAAG,CAAC,CACrCH,SAAS,CAAChF,MAAM,CAACoF,IAAI,CAACD,SAAS,CAAG,CAAC,CAEnC,KAAM,CAAAE,WAAW,CAAG,GAAI,CAAAznB,KAAK,CAAC0nB,OAAO,CAACV,MAAM,CAAEE,MAAM,CAAC,CACrDE,SAAS,CAACO,aAAa,CAACF,WAAW,CAAEva,SAAS,CAAC2B,OAAO,CAAC,CAEvD;AACA,KAAM,CAAA+Y,mBAAmB,CAAG,EAAE,CAC9BnkB,gBAAgB,CAACuE,OAAO,CAAC,CAAC0Y,QAAQ,CAAElT,OAAO,GAAK,CAC9C,GAAIkT,QAAQ,CAACnZ,KAAK,CAAE,CAClBqgB,mBAAmB,CAAC9Q,IAAI,CAAC4J,QAAQ,CAACnZ,KAAK,CAAC,CACxCnE,OAAO,CAACC,GAAG,CAAC,SAASmK,OAAO,QAAQ,CAAC,CACvC,CACF,CAAC,CAAC,CAEF;AACApK,OAAO,CAACC,GAAG,CAAC,QAAQukB,mBAAmB,CAACrf,MAAM,YAAY,CAAC,CAC3D,KAAM,CAAA4jB,YAAY,CAAG/E,SAAS,CAACU,gBAAgB,CAACF,mBAAmB,CAAE,IAAI,CAAC,CAE1E,GAAIuE,YAAY,CAAC5jB,MAAM,CAAG,CAAC,CAAE,CAC3BnF,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1B8oB,YAAY,CAACnkB,OAAO,CAAC,CAAC+f,SAAS,CAAElX,CAAC,GAAK,CACrCzN,OAAO,CAACC,GAAG,CAAC,MAAMwN,CAAC,GAAG,CAAEkX,SAAS,CAACrgB,MAAM,CAACoJ,IAAI,EAAI,KAAK,CAC1C,KAAK,CAAEiX,SAAS,CAACliB,QAAQ,CACzB,WAAW,CAAEkiB,SAAS,CAACrgB,MAAM,CAACwE,QAAQ,CAACoF,OAAO,CAAC,CAAC,CAChD,WAAW,CAAEyW,SAAS,CAACrgB,MAAM,CAACsZ,QAAQ,CAAC,CAEnD;AACA,KAAM,CAAA/B,GAAG,CAAGgJ,yBAAyB,CAACF,SAAS,CAACrgB,MAAM,CAAC,CACvD,GAAIuX,GAAG,EAAIA,GAAG,CAAC+B,QAAQ,EAAI/B,GAAG,CAAC+B,QAAQ,CAACrO,IAAI,GAAK,cAAc,CAAE,CAC/DvP,OAAO,CAACC,GAAG,CAAC,UAAU,CAAE4b,GAAG,CAAC+B,QAAQ,CAACxT,OAAO,CAAC,CAC/C,CACF,CAAC,CAAC,CAEF,MAAO,KAAI,CACb,CAEA;AACApK,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAC7B,KAAM,CAAA+oB,eAAe,CAAGhF,SAAS,CAACU,gBAAgB,CAACjmB,KAAK,CAACwa,QAAQ,CAAE,IAAI,CAAC,CAExEjZ,OAAO,CAACC,GAAG,CAAC,WAAW+oB,eAAe,CAAC7jB,MAAM,MAAM,CAAC,CACpD6jB,eAAe,CAACpkB,OAAO,CAAC,CAAC+f,SAAS,CAAElX,CAAC,GAAK,CACxC,KAAM,CAAAoO,GAAG,CAAG8I,SAAS,CAACrgB,MAAM,CAC5BtE,OAAO,CAACC,GAAG,CAAC,QAAQwN,CAAC,GAAG,CAAEoO,GAAG,CAACnO,IAAI,EAAI,KAAK,CAC/B,KAAK,CAAEmO,GAAG,CAACtM,IAAI,CACf,KAAK,CAAEsM,GAAG,CAAC/S,QAAQ,CAACoF,OAAO,CAAC,CAAC,CAC7B,KAAK,CAAEyW,SAAS,CAACliB,QAAQ,CACzB,WAAW,CAAEoZ,GAAG,CAAC+B,QAAQ,CAAC,CACxC,CAAC,CAAC,CAEF;AACA5d,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3B,GAAI,CAAAgpB,YAAY,CAAG,CAAC,CAEpB5oB,gBAAgB,CAACuE,OAAO,CAAC,CAAC0Y,QAAQ,CAAElT,OAAO,GAAK,CAC9C,GAAIkT,QAAQ,CAACnZ,KAAK,CAAE,KAAA+kB,qBAAA,CAClB;AACA,GAAI,CAAAC,SAAS,CAAG7L,QAAQ,CAACnZ,KAAK,CAACgG,OAAO,CACtC,GAAI,CAAAif,cAAc,CAAG,IAAI,CAEzB;AACA,KAAM,CAAAnE,QAAQ,CAAG,GAAI,CAAAroB,KAAK,CAACkG,OAAO,CAAC,CAAC,CACpCwa,QAAQ,CAACnZ,KAAK,CAAC+gB,gBAAgB,CAACD,QAAQ,CAAC,CAEzC;AACA,KAAM,CAAAoE,gBAAgB,CAAGpE,QAAQ,CAACviB,UAAU,CAACoH,SAAS,CAAC2B,OAAO,CAAC3C,QAAQ,CAAC,CAExE;AACA,KAAM,CAAAqc,SAAS,CAAGF,QAAQ,CAACnjB,KAAK,CAAC,CAAC,CAACsjB,OAAO,CAACtb,SAAS,CAAC2B,OAAO,CAAC,CAC7D,GAAIvI,IAAI,CAACK,GAAG,CAAC4hB,SAAS,CAACnjB,CAAC,CAAC,CAAG,CAAC,EAAIkB,IAAI,CAACK,GAAG,CAAC4hB,SAAS,CAACjjB,CAAC,CAAC,CAAG,CAAC,EAAIijB,SAAS,CAAC/iB,CAAC,CAAG,CAAC,CAAC,EAAI+iB,SAAS,CAAC/iB,CAAC,CAAG,CAAC,CAAE,CACjGgnB,cAAc,CAAG,KAAK,CACxB,CAEA,GAAID,SAAS,CAAE,CACbF,YAAY,EAAE,CAChB,CAEAjpB,OAAO,CAACC,GAAG,CAAC,OAAOmK,OAAO,GAAG,CAAE,CAC7Bkf,EAAE,CAAE,EAAAJ,qBAAA,CAAA5L,QAAQ,CAAC/P,YAAY,UAAA2b,qBAAA,iBAArBA,qBAAA,CAAuBxb,IAAI,GAAI,IAAI,CACvC6b,GAAG,CAAEJ,SAAS,CACdK,KAAK,CAAEJ,cAAc,CACrBK,IAAI,CAAExE,QAAQ,CAAC/W,OAAO,CAAC,CAAC,CACxBwb,IAAI,CAAE,CAACvE,SAAS,CAACnjB,CAAC,CAAEmjB,SAAS,CAACjjB,CAAC,CAAEijB,SAAS,CAAC/iB,CAAC,CAAC,CAC7CunB,MAAM,CAAEN,gBACV,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEFrpB,OAAO,CAACC,GAAG,CAAC,MAAMgpB,YAAY,IAAI5oB,gBAAgB,CAACia,IAAI,WAAW,CAAC,CAEnE;AACA,MAAO,CAAA0O,eAAe,CAAC7jB,MAAM,CAAG,CAAC,CACnC,CAAE,MAAO7D,KAAK,CAAE,CACdtB,OAAO,CAACsB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B,MAAO,MAAK,CACd,CACF,CAAC,CAID;AACA,KAAM,CAAAwS,wBAAwB,CAAGA,CAACR,YAAY,CAAEG,SAAS,GAAK,KAAAmW,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAC5D,GAAI,CAACxW,YAAY,EAAI,CAACA,YAAY,CAACnP,KAAK,EAAI,CAACsP,SAAS,CAAE,CACtD,OACF,CAEA;AACA,KAAM,CAAAsW,cAAc,CAAG,EAAE,CACzBzW,YAAY,CAACnP,KAAK,CAACE,QAAQ,CAACiN,KAAK,EAAI,CACnC,GAAIA,KAAK,CAACsM,QAAQ,EAAItM,KAAK,CAACsM,QAAQ,CAACoM,OAAO,CAAE,CAC5CD,cAAc,CAACrW,IAAI,CAACpC,KAAK,CAAC,CAC5B,CACF,CAAC,CAAC,CAEFyY,cAAc,CAACnlB,OAAO,CAACghB,KAAK,EAAI,CAC9BtS,YAAY,CAACnP,KAAK,CAACiC,MAAM,CAACwf,KAAK,CAAC,CAClC,CAAC,CAAC,CAEF;AACA,GAAI,CAAAO,UAAU,CACd,OAAO1S,SAAS,CAACH,YAAY,EAC3B,IAAK,GAAG,CACN6S,UAAU,CAAG,QAAQ,CAAE;AACvB,MACF,IAAK,GAAG,CACNA,UAAU,CAAG,QAAQ,CAAE;AACvB,MACF,IAAK,GAAG,CACR,QACEA,UAAU,CAAG,QAAQ,CAAE;AACvB,MACJ,CAEA;AACA,KAAM,CAAAlD,aAAa,CAAG,GAAI,CAAArmB,KAAK,CAAC4gB,cAAc,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CACzD,KAAM,CAAA0F,aAAa,CAAG,GAAI,CAAAtmB,KAAK,CAACmgB,iBAAiB,CAAC,CAChDhS,KAAK,CAAEob,UAAU,CACjB3U,QAAQ,CAAE2U,UAAU,CACpB8D,iBAAiB,CAAE,CACrB,CAAC,CAAC,CACF,KAAM,CAAA9G,SAAS,CAAG,GAAI,CAAAvmB,KAAK,CAACogB,IAAI,CAACiG,aAAa,CAAEC,aAAa,CAAC,CAC9DC,SAAS,CAACra,QAAQ,CAACzG,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAAE;AAClC8gB,SAAS,CAACvF,QAAQ,CAAG,CACnBoM,OAAO,CAAE,IAAI,CACbza,IAAI,CAAE,cAAc,CACpBnF,OAAO,EAAAwf,qBAAA,CAAEtW,YAAY,CAAC/F,YAAY,UAAAqc,qBAAA,iBAAzBA,qBAAA,CAA2Bxf,OAAO,CAC3C2I,OAAO,CAAEU,SAAS,CAACV,OAAO,CAC1BE,SAAS,CAAEQ,SAAS,CAACR,SAAS,CAC9BM,UAAU,CAAEE,SAAS,CAACF,UACxB,CAAC,CAED;AACA,KAAM,CAAAqS,KAAK,CAAG,GAAI,CAAAhpB,KAAK,CAACstB,UAAU,CAAC/D,UAAU,CAAE,CAAC,CAAE,EAAE,CAAC,CACrDP,KAAK,CAAC9c,QAAQ,CAACzG,GAAG,CAAC,CAAC,CAAE,EAAE,CAAE,CAAC,CAAC,CAC5BujB,KAAK,CAAChI,QAAQ,CAAG,CAAEoM,OAAO,CAAE,IAAK,CAAC,CAElC;AACA1W,YAAY,CAACnP,KAAK,CAACC,GAAG,CAAC+e,SAAS,CAAC,CACjC7P,YAAY,CAACnP,KAAK,CAACC,GAAG,CAACwhB,KAAK,CAAC,CAE7B5lB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAA4pB,sBAAA,CAAAvW,YAAY,CAAC/F,YAAY,UAAAsc,sBAAA,iBAAzBA,sBAAA,CAA2Bnc,IAAI,KAAAoc,sBAAA,CAAIxW,YAAY,CAAC/F,YAAY,UAAAuc,sBAAA,iBAAzBA,sBAAA,CAA2B1f,OAAO,cAAaqJ,SAAS,CAACH,YAAY,WAAWG,SAAS,CAACF,UAAU,GAAG,CAAC,CACjK,CAAC,CAED,cAAe,CAAAvM,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}