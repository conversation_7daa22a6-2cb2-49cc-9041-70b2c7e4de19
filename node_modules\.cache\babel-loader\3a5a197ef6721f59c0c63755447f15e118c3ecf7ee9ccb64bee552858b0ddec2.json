{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { cloneElement } from '../../_util/reactNode';\nimport Looper from './Looper';\nexport default function Indicator(props) {\n  const {\n    prefixCls,\n    indicator,\n    percent\n  } = props;\n  const dotClassName = `${prefixCls}-dot`;\n  if (indicator && /*#__PURE__*/React.isValidElement(indicator)) {\n    return cloneElement(indicator, {\n      className: classNames(indicator.props.className, dotClassName),\n      percent\n    });\n  }\n  return /*#__PURE__*/React.createElement(Looper, {\n    prefixCls: prefixCls,\n    percent: percent\n  });\n}", "map": {"version": 3, "names": ["React", "classNames", "cloneElement", "<PERSON><PERSON>", "Indicator", "props", "prefixCls", "indicator", "percent", "dotClassName", "isValidElement", "className", "createElement"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/spin/Indicator/index.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { cloneElement } from '../../_util/reactNode';\nimport Looper from './Looper';\nexport default function Indicator(props) {\n  const {\n    prefixCls,\n    indicator,\n    percent\n  } = props;\n  const dotClassName = `${prefixCls}-dot`;\n  if (indicator && /*#__PURE__*/React.isValidElement(indicator)) {\n    return cloneElement(indicator, {\n      className: classNames(indicator.props.className, dotClassName),\n      percent\n    });\n  }\n  return /*#__PURE__*/React.createElement(Looper, {\n    prefixCls: prefixCls,\n    percent: percent\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAOC,MAAM,MAAM,UAAU;AAC7B,eAAe,SAASC,SAASA,CAACC,KAAK,EAAE;EACvC,MAAM;IACJC,SAAS;IACTC,SAAS;IACTC;EACF,CAAC,GAAGH,KAAK;EACT,MAAMI,YAAY,GAAG,GAAGH,SAAS,MAAM;EACvC,IAAIC,SAAS,IAAI,aAAaP,KAAK,CAACU,cAAc,CAACH,SAAS,CAAC,EAAE;IAC7D,OAAOL,YAAY,CAACK,SAAS,EAAE;MAC7BI,SAAS,EAAEV,UAAU,CAACM,SAAS,CAACF,KAAK,CAACM,SAAS,EAAEF,YAAY,CAAC;MAC9DD;IACF,CAAC,CAAC;EACJ;EACA,OAAO,aAAaR,KAAK,CAACY,aAAa,CAACT,MAAM,EAAE;IAC9CG,SAAS,EAAEA,SAAS;IACpBE,OAAO,EAAEA;EACX,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}