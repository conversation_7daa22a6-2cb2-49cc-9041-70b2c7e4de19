{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { mergeToken } from '../../theme/internal';\nfunction genSizeStyle(token, suffix) {\n  const {\n    componentCls,\n    inputPaddingHorizontalBase,\n    borderRadius\n  } = token;\n  const selectHeightWithoutBorder = token.calc(token.controlHeight).sub(token.calc(token.lineWidth).mul(2)).equal();\n  const suffixCls = suffix ? `${componentCls}-${suffix}` : '';\n  return {\n    [`${componentCls}-single${suffixCls}`]: {\n      fontSize: token.fontSize,\n      height: token.controlHeight,\n      // ========================= Selector =========================\n      [`${componentCls}-selector`]: Object.assign(Object.assign({}, resetComponent(token, true)), {\n        display: 'flex',\n        borderRadius,\n        flex: '1 1 auto',\n        [`${componentCls}-selection-wrap:after`]: {\n          lineHeight: unit(selectHeightWithoutBorder)\n        },\n        [`${componentCls}-selection-search`]: {\n          position: 'absolute',\n          inset: 0,\n          width: '100%',\n          '&-input': {\n            width: '100%',\n            WebkitAppearance: 'textfield'\n          }\n        },\n        [`\n          ${componentCls}-selection-item,\n          ${componentCls}-selection-placeholder\n        `]: {\n          display: 'block',\n          padding: 0,\n          lineHeight: unit(selectHeightWithoutBorder),\n          transition: `all ${token.motionDurationSlow}, visibility 0s`,\n          alignSelf: 'center'\n        },\n        [`${componentCls}-selection-placeholder`]: {\n          transition: 'none',\n          pointerEvents: 'none'\n        },\n        // For common baseline align\n        [['&:after', /* For '' value baseline align */\n        `${componentCls}-selection-item:empty:after`, /* For undefined value baseline align */\n        `${componentCls}-selection-placeholder:empty:after`].join(',')]: {\n          display: 'inline-block',\n          width: 0,\n          visibility: 'hidden',\n          content: '\"\\\\a0\"'\n        }\n      }),\n      [`\n        &${componentCls}-show-arrow ${componentCls}-selection-item,\n        &${componentCls}-show-arrow ${componentCls}-selection-search,\n        &${componentCls}-show-arrow ${componentCls}-selection-placeholder\n      `]: {\n        paddingInlineEnd: token.showArrowPaddingInlineEnd\n      },\n      // Opacity selection if open\n      [`&${componentCls}-open ${componentCls}-selection-item`]: {\n        color: token.colorTextPlaceholder\n      },\n      // ========================== Input ==========================\n      // We only change the style of non-customize input which is only support by `combobox` mode.\n      // Not customize\n      [`&:not(${componentCls}-customize-input)`]: {\n        [`${componentCls}-selector`]: {\n          width: '100%',\n          height: '100%',\n          alignItems: 'center',\n          padding: `0 ${unit(inputPaddingHorizontalBase)}`,\n          [`${componentCls}-selection-search-input`]: {\n            height: selectHeightWithoutBorder,\n            fontSize: token.fontSize\n          },\n          '&:after': {\n            lineHeight: unit(selectHeightWithoutBorder)\n          }\n        }\n      },\n      [`&${componentCls}-customize-input`]: {\n        [`${componentCls}-selector`]: {\n          '&:after': {\n            display: 'none'\n          },\n          [`${componentCls}-selection-search`]: {\n            position: 'static',\n            width: '100%'\n          },\n          [`${componentCls}-selection-placeholder`]: {\n            position: 'absolute',\n            insetInlineStart: 0,\n            insetInlineEnd: 0,\n            padding: `0 ${unit(inputPaddingHorizontalBase)}`,\n            '&:after': {\n              display: 'none'\n            }\n          }\n        }\n      }\n    }\n  };\n}\nexport default function genSingleStyle(token) {\n  const {\n    componentCls\n  } = token;\n  const inputPaddingHorizontalSM = token.calc(token.controlPaddingHorizontalSM).sub(token.lineWidth).equal();\n  return [genSizeStyle(token),\n  // ======================== Small ========================\n  // Shared\n  genSizeStyle(mergeToken(token, {\n    controlHeight: token.controlHeightSM,\n    borderRadius: token.borderRadiusSM\n  }), 'sm'),\n  // padding\n  {\n    [`${componentCls}-single${componentCls}-sm`]: {\n      [`&:not(${componentCls}-customize-input)`]: {\n        [`${componentCls}-selector`]: {\n          padding: `0 ${unit(inputPaddingHorizontalSM)}`\n        },\n        // With arrow should provides `padding-right` to show the arrow\n        [`&${componentCls}-show-arrow ${componentCls}-selection-search`]: {\n          insetInlineEnd: token.calc(inputPaddingHorizontalSM).add(token.calc(token.fontSize).mul(1.5)).equal()\n        },\n        [`\n            &${componentCls}-show-arrow ${componentCls}-selection-item,\n            &${componentCls}-show-arrow ${componentCls}-selection-placeholder\n          `]: {\n          paddingInlineEnd: token.calc(token.fontSize).mul(1.5).equal()\n        }\n      }\n    }\n  },\n  // ======================== Large ========================\n  // Shared\n  genSizeStyle(mergeToken(token, {\n    controlHeight: token.singleItemHeightLG,\n    fontSize: token.fontSizeLG,\n    borderRadius: token.borderRadiusLG\n  }), 'lg')];\n}", "map": {"version": 3, "names": ["unit", "resetComponent", "mergeToken", "genSizeStyle", "token", "suffix", "componentCls", "inputPaddingHorizontalBase", "borderRadius", "selectHeightWithoutBorder", "calc", "controlHeight", "sub", "lineWidth", "mul", "equal", "suffixCls", "fontSize", "height", "Object", "assign", "display", "flex", "lineHeight", "position", "inset", "width", "WebkitAppearance", "padding", "transition", "motionDurationSlow", "alignSelf", "pointerEvents", "join", "visibility", "content", "paddingInlineEnd", "showArrowPaddingInlineEnd", "color", "colorTextPlaceholder", "alignItems", "insetInlineStart", "insetInlineEnd", "genSingleStyle", "inputPaddingHorizontalSM", "controlPaddingHorizontalSM", "controlHeightSM", "borderRadiusSM", "add", "singleItemHeightLG", "fontSizeLG", "borderRadiusLG"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/select/style/single.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { mergeToken } from '../../theme/internal';\nfunction genSizeStyle(token, suffix) {\n  const {\n    componentCls,\n    inputPaddingHorizontalBase,\n    borderRadius\n  } = token;\n  const selectHeightWithoutBorder = token.calc(token.controlHeight).sub(token.calc(token.lineWidth).mul(2)).equal();\n  const suffixCls = suffix ? `${componentCls}-${suffix}` : '';\n  return {\n    [`${componentCls}-single${suffixCls}`]: {\n      fontSize: token.fontSize,\n      height: token.controlHeight,\n      // ========================= Selector =========================\n      [`${componentCls}-selector`]: Object.assign(Object.assign({}, resetComponent(token, true)), {\n        display: 'flex',\n        borderRadius,\n        flex: '1 1 auto',\n        [`${componentCls}-selection-wrap:after`]: {\n          lineHeight: unit(selectHeightWithoutBorder)\n        },\n        [`${componentCls}-selection-search`]: {\n          position: 'absolute',\n          inset: 0,\n          width: '100%',\n          '&-input': {\n            width: '100%',\n            WebkitAppearance: 'textfield'\n          }\n        },\n        [`\n          ${componentCls}-selection-item,\n          ${componentCls}-selection-placeholder\n        `]: {\n          display: 'block',\n          padding: 0,\n          lineHeight: unit(selectHeightWithoutBorder),\n          transition: `all ${token.motionDurationSlow}, visibility 0s`,\n          alignSelf: 'center'\n        },\n        [`${componentCls}-selection-placeholder`]: {\n          transition: 'none',\n          pointerEvents: 'none'\n        },\n        // For common baseline align\n        [['&:after', /* For '' value baseline align */\n        `${componentCls}-selection-item:empty:after`, /* For undefined value baseline align */\n        `${componentCls}-selection-placeholder:empty:after`].join(',')]: {\n          display: 'inline-block',\n          width: 0,\n          visibility: 'hidden',\n          content: '\"\\\\a0\"'\n        }\n      }),\n      [`\n        &${componentCls}-show-arrow ${componentCls}-selection-item,\n        &${componentCls}-show-arrow ${componentCls}-selection-search,\n        &${componentCls}-show-arrow ${componentCls}-selection-placeholder\n      `]: {\n        paddingInlineEnd: token.showArrowPaddingInlineEnd\n      },\n      // Opacity selection if open\n      [`&${componentCls}-open ${componentCls}-selection-item`]: {\n        color: token.colorTextPlaceholder\n      },\n      // ========================== Input ==========================\n      // We only change the style of non-customize input which is only support by `combobox` mode.\n      // Not customize\n      [`&:not(${componentCls}-customize-input)`]: {\n        [`${componentCls}-selector`]: {\n          width: '100%',\n          height: '100%',\n          alignItems: 'center',\n          padding: `0 ${unit(inputPaddingHorizontalBase)}`,\n          [`${componentCls}-selection-search-input`]: {\n            height: selectHeightWithoutBorder,\n            fontSize: token.fontSize\n          },\n          '&:after': {\n            lineHeight: unit(selectHeightWithoutBorder)\n          }\n        }\n      },\n      [`&${componentCls}-customize-input`]: {\n        [`${componentCls}-selector`]: {\n          '&:after': {\n            display: 'none'\n          },\n          [`${componentCls}-selection-search`]: {\n            position: 'static',\n            width: '100%'\n          },\n          [`${componentCls}-selection-placeholder`]: {\n            position: 'absolute',\n            insetInlineStart: 0,\n            insetInlineEnd: 0,\n            padding: `0 ${unit(inputPaddingHorizontalBase)}`,\n            '&:after': {\n              display: 'none'\n            }\n          }\n        }\n      }\n    }\n  };\n}\nexport default function genSingleStyle(token) {\n  const {\n    componentCls\n  } = token;\n  const inputPaddingHorizontalSM = token.calc(token.controlPaddingHorizontalSM).sub(token.lineWidth).equal();\n  return [genSizeStyle(token),\n  // ======================== Small ========================\n  // Shared\n  genSizeStyle(mergeToken(token, {\n    controlHeight: token.controlHeightSM,\n    borderRadius: token.borderRadiusSM\n  }), 'sm'),\n  // padding\n  {\n    [`${componentCls}-single${componentCls}-sm`]: {\n      [`&:not(${componentCls}-customize-input)`]: {\n        [`${componentCls}-selector`]: {\n          padding: `0 ${unit(inputPaddingHorizontalSM)}`\n        },\n        // With arrow should provides `padding-right` to show the arrow\n        [`&${componentCls}-show-arrow ${componentCls}-selection-search`]: {\n          insetInlineEnd: token.calc(inputPaddingHorizontalSM).add(token.calc(token.fontSize).mul(1.5)).equal()\n        },\n        [`\n            &${componentCls}-show-arrow ${componentCls}-selection-item,\n            &${componentCls}-show-arrow ${componentCls}-selection-placeholder\n          `]: {\n          paddingInlineEnd: token.calc(token.fontSize).mul(1.5).equal()\n        }\n      }\n    }\n  },\n  // ======================== Large ========================\n  // Shared\n  genSizeStyle(mergeToken(token, {\n    controlHeight: token.singleItemHeightLG,\n    fontSize: token.fontSizeLG,\n    borderRadius: token.borderRadiusLG\n  }), 'lg')];\n}"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,YAAYA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACnC,MAAM;IACJC,YAAY;IACZC,0BAA0B;IAC1BC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,yBAAyB,GAAGL,KAAK,CAACM,IAAI,CAACN,KAAK,CAACO,aAAa,CAAC,CAACC,GAAG,CAACR,KAAK,CAACM,IAAI,CAACN,KAAK,CAACS,SAAS,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACjH,MAAMC,SAAS,GAAGX,MAAM,GAAG,GAAGC,YAAY,IAAID,MAAM,EAAE,GAAG,EAAE;EAC3D,OAAO;IACL,CAAC,GAAGC,YAAY,UAAUU,SAAS,EAAE,GAAG;MACtCC,QAAQ,EAAEb,KAAK,CAACa,QAAQ;MACxBC,MAAM,EAAEd,KAAK,CAACO,aAAa;MAC3B;MACA,CAAC,GAAGL,YAAY,WAAW,GAAGa,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnB,cAAc,CAACG,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE;QAC1FiB,OAAO,EAAE,MAAM;QACfb,YAAY;QACZc,IAAI,EAAE,UAAU;QAChB,CAAC,GAAGhB,YAAY,uBAAuB,GAAG;UACxCiB,UAAU,EAAEvB,IAAI,CAACS,yBAAyB;QAC5C,CAAC;QACD,CAAC,GAAGH,YAAY,mBAAmB,GAAG;UACpCkB,QAAQ,EAAE,UAAU;UACpBC,KAAK,EAAE,CAAC;UACRC,KAAK,EAAE,MAAM;UACb,SAAS,EAAE;YACTA,KAAK,EAAE,MAAM;YACbC,gBAAgB,EAAE;UACpB;QACF,CAAC;QACD,CAAC;AACT,YAAYrB,YAAY;AACxB,YAAYA,YAAY;AACxB,SAAS,GAAG;UACFe,OAAO,EAAE,OAAO;UAChBO,OAAO,EAAE,CAAC;UACVL,UAAU,EAAEvB,IAAI,CAACS,yBAAyB,CAAC;UAC3CoB,UAAU,EAAE,OAAOzB,KAAK,CAAC0B,kBAAkB,iBAAiB;UAC5DC,SAAS,EAAE;QACb,CAAC;QACD,CAAC,GAAGzB,YAAY,wBAAwB,GAAG;UACzCuB,UAAU,EAAE,MAAM;UAClBG,aAAa,EAAE;QACjB,CAAC;QACD;QACA,CAAC,CAAC,SAAS,EAAE;QACb,GAAG1B,YAAY,6BAA6B,EAAE;QAC9C,GAAGA,YAAY,oCAAoC,CAAC,CAAC2B,IAAI,CAAC,GAAG,CAAC,GAAG;UAC/DZ,OAAO,EAAE,cAAc;UACvBK,KAAK,EAAE,CAAC;UACRQ,UAAU,EAAE,QAAQ;UACpBC,OAAO,EAAE;QACX;MACF,CAAC,CAAC;MACF,CAAC;AACP,WAAW7B,YAAY,eAAeA,YAAY;AAClD,WAAWA,YAAY,eAAeA,YAAY;AAClD,WAAWA,YAAY,eAAeA,YAAY;AAClD,OAAO,GAAG;QACF8B,gBAAgB,EAAEhC,KAAK,CAACiC;MAC1B,CAAC;MACD;MACA,CAAC,IAAI/B,YAAY,SAASA,YAAY,iBAAiB,GAAG;QACxDgC,KAAK,EAAElC,KAAK,CAACmC;MACf,CAAC;MACD;MACA;MACA;MACA,CAAC,SAASjC,YAAY,mBAAmB,GAAG;QAC1C,CAAC,GAAGA,YAAY,WAAW,GAAG;UAC5BoB,KAAK,EAAE,MAAM;UACbR,MAAM,EAAE,MAAM;UACdsB,UAAU,EAAE,QAAQ;UACpBZ,OAAO,EAAE,KAAK5B,IAAI,CAACO,0BAA0B,CAAC,EAAE;UAChD,CAAC,GAAGD,YAAY,yBAAyB,GAAG;YAC1CY,MAAM,EAAET,yBAAyB;YACjCQ,QAAQ,EAAEb,KAAK,CAACa;UAClB,CAAC;UACD,SAAS,EAAE;YACTM,UAAU,EAAEvB,IAAI,CAACS,yBAAyB;UAC5C;QACF;MACF,CAAC;MACD,CAAC,IAAIH,YAAY,kBAAkB,GAAG;QACpC,CAAC,GAAGA,YAAY,WAAW,GAAG;UAC5B,SAAS,EAAE;YACTe,OAAO,EAAE;UACX,CAAC;UACD,CAAC,GAAGf,YAAY,mBAAmB,GAAG;YACpCkB,QAAQ,EAAE,QAAQ;YAClBE,KAAK,EAAE;UACT,CAAC;UACD,CAAC,GAAGpB,YAAY,wBAAwB,GAAG;YACzCkB,QAAQ,EAAE,UAAU;YACpBiB,gBAAgB,EAAE,CAAC;YACnBC,cAAc,EAAE,CAAC;YACjBd,OAAO,EAAE,KAAK5B,IAAI,CAACO,0BAA0B,CAAC,EAAE;YAChD,SAAS,EAAE;cACTc,OAAO,EAAE;YACX;UACF;QACF;MACF;IACF;EACF,CAAC;AACH;AACA,eAAe,SAASsB,cAAcA,CAACvC,KAAK,EAAE;EAC5C,MAAM;IACJE;EACF,CAAC,GAAGF,KAAK;EACT,MAAMwC,wBAAwB,GAAGxC,KAAK,CAACM,IAAI,CAACN,KAAK,CAACyC,0BAA0B,CAAC,CAACjC,GAAG,CAACR,KAAK,CAACS,SAAS,CAAC,CAACE,KAAK,CAAC,CAAC;EAC1G,OAAO,CAACZ,YAAY,CAACC,KAAK,CAAC;EAC3B;EACA;EACAD,YAAY,CAACD,UAAU,CAACE,KAAK,EAAE;IAC7BO,aAAa,EAAEP,KAAK,CAAC0C,eAAe;IACpCtC,YAAY,EAAEJ,KAAK,CAAC2C;EACtB,CAAC,CAAC,EAAE,IAAI,CAAC;EACT;EACA;IACE,CAAC,GAAGzC,YAAY,UAAUA,YAAY,KAAK,GAAG;MAC5C,CAAC,SAASA,YAAY,mBAAmB,GAAG;QAC1C,CAAC,GAAGA,YAAY,WAAW,GAAG;UAC5BsB,OAAO,EAAE,KAAK5B,IAAI,CAAC4C,wBAAwB,CAAC;QAC9C,CAAC;QACD;QACA,CAAC,IAAItC,YAAY,eAAeA,YAAY,mBAAmB,GAAG;UAChEoC,cAAc,EAAEtC,KAAK,CAACM,IAAI,CAACkC,wBAAwB,CAAC,CAACI,GAAG,CAAC5C,KAAK,CAACM,IAAI,CAACN,KAAK,CAACa,QAAQ,CAAC,CAACH,GAAG,CAAC,GAAG,CAAC,CAAC,CAACC,KAAK,CAAC;QACtG,CAAC;QACD,CAAC;AACT,eAAeT,YAAY,eAAeA,YAAY;AACtD,eAAeA,YAAY,eAAeA,YAAY;AACtD,WAAW,GAAG;UACJ8B,gBAAgB,EAAEhC,KAAK,CAACM,IAAI,CAACN,KAAK,CAACa,QAAQ,CAAC,CAACH,GAAG,CAAC,GAAG,CAAC,CAACC,KAAK,CAAC;QAC9D;MACF;IACF;EACF,CAAC;EACD;EACA;EACAZ,YAAY,CAACD,UAAU,CAACE,KAAK,EAAE;IAC7BO,aAAa,EAAEP,KAAK,CAAC6C,kBAAkB;IACvChC,QAAQ,EAAEb,KAAK,CAAC8C,UAAU;IAC1B1C,YAAY,EAAEJ,KAAK,CAAC+C;EACtB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}