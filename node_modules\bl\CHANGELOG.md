## [6.1.0](https://github.com/rvagg/bl/compare/v6.0.20...v6.1.0) (2025-03-11)

### Features

* Added prepend and getBuffers methods. ([#154](https://github.com/rvagg/bl/issues/154)) ([e9eda95](https://github.com/rvagg/bl/commit/e9eda9549b1235af16afbe0c721f92e705109663))

## [6.0.20](https://github.com/rvagg/bl/compare/v6.0.19...v6.0.20) (2025-03-03)

### Trivial Changes

* **deps-dev:** bump typescript from 5.7.3 to 5.8.2 ([#153](https://github.com/rvagg/bl/issues/153)) ([9291cf9](https://github.com/rvagg/bl/commit/9291cf9ec4b3cdef8c5779c73247844f48943c02))

## [6.0.19](https://github.com/rvagg/bl/compare/v6.0.18...v6.0.19) (2025-01-28)

### Trivial Changes

* **deps:** bump actions/setup-node from 4.1.0 to 4.2.0 ([#151](https://github.com/rvagg/bl/issues/151)) ([2e72553](https://github.com/rvagg/bl/commit/2e7255395260941d199ffe644feecbe6ed2647e4))

## [6.0.18](https://github.com/rvagg/bl/compare/v6.0.17...v6.0.18) (2024-12-30)

### Trivial Changes

* **deps-dev:** bump typescript from 5.6.3 to 5.7.2 ([d28178a](https://github.com/rvagg/bl/commit/d28178ae2e5c740de0e3d891beae77b26f801b0f))
* **deps:** bump actions/setup-node from 4.0.4 to 4.1.0 ([#146](https://github.com/rvagg/bl/issues/146)) ([45c312b](https://github.com/rvagg/bl/commit/45c312b48b150d53336fecce69cb6895c8daaaeb))

## [6.0.17](https://github.com/rvagg/bl/compare/v6.0.16...v6.0.17) (2024-12-30)

### Tests

* ignore TS errors from dependencies ([17e7a10](https://github.com/rvagg/bl/commit/17e7a10c82b07f3ef63a4235a842159d6c08a7d0))

## [6.0.16](https://github.com/rvagg/bl/compare/v6.0.15...v6.0.16) (2024-09-25)

### Trivial Changes

* **deps:** bump actions/setup-node from 4.0.3 to 4.0.4 ([19d67ab](https://github.com/rvagg/bl/commit/19d67ab90e1e49a9b5ebc250969c2c5bde508db9))

## [6.0.15](https://github.com/rvagg/bl/compare/v6.0.14...v6.0.15) (2024-09-10)

### Trivial Changes

* **deps-dev:** bump typescript from 5.5.4 to 5.6.2 ([edfc739](https://github.com/rvagg/bl/commit/edfc73964665530cfcc046319b446c6b6efeebff))

## [6.0.14](https://github.com/rvagg/bl/compare/v6.0.13...v6.0.14) (2024-07-10)

### Trivial Changes

* **deps:** bump actions/setup-node from 4.0.2 to 4.0.3 ([09aa80d](https://github.com/rvagg/bl/commit/09aa80de083f045f0fd92414e97f2c241f8f15bf))

## [6.0.13](https://github.com/rvagg/bl/compare/v6.0.12...v6.0.13) (2024-06-21)

### Trivial Changes

* **deps-dev:** bump typescript from 5.4.5 to 5.5.2 ([41eff82](https://github.com/rvagg/bl/commit/41eff826534912051ab60fe7c36baad7a3c09492))

## [6.0.12](https://github.com/rvagg/bl/compare/v6.0.11...v6.0.12) (2024-03-07)


### Trivial Changes

* **deps-dev:** bump typescript from 5.3.3 to 5.4.2 ([18e99a2](https://github.com/rvagg/bl/commit/18e99a233d82c0ff5f3b00b04aab5a4ce6d37452))

## [6.0.11](https://github.com/rvagg/bl/compare/v6.0.10...v6.0.11) (2024-02-08)


### Trivial Changes

* **deps:** bump actions/setup-node from 4.0.1 to 4.0.2 ([d8ac460](https://github.com/rvagg/bl/commit/d8ac460597a24b0e783da2acd6ab37eacbbb0af5))
* update Node.js versions in CI ([863a5e0](https://github.com/rvagg/bl/commit/863a5e02f2c144c54be88ff962b0a902684c6527))

## [6.0.10](https://github.com/rvagg/bl/compare/v6.0.9...v6.0.10) (2024-01-01)


### Trivial Changes

* **deps:** bump actions/setup-node from 4.0.0 to 4.0.1 ([a018907](https://github.com/rvagg/bl/commit/a0189073aee3e906b135a37595f8b4007e6dd3e7))

## [6.0.9](https://github.com/rvagg/bl/compare/v6.0.8...v6.0.9) (2023-11-27)


### Trivial Changes

* **deps-dev:** bump typescript from 5.2.2 to 5.3.2 ([bb294bd](https://github.com/rvagg/bl/commit/bb294bd7baa5c5e1e062bd23b5d714692e04d414))

## [6.0.8](https://github.com/rvagg/bl/compare/v6.0.7...v6.0.8) (2023-10-25)


### Trivial Changes

* **deps:** bump actions/checkout from 3 to 4 ([a9ad973](https://github.com/rvagg/bl/commit/a9ad973d1fe4e5f673fe3b9b72b4484136e1655d))
* **deps:** bump actions/setup-node from 3.8.1 to 4.0.0 ([5921489](https://github.com/rvagg/bl/commit/59214897520fd6ba6d20a7cf370373275d4cfe1d))

## [6.0.7](https://github.com/rvagg/bl/compare/v6.0.6...v6.0.7) (2023-08-25)


### Trivial Changes

* **deps-dev:** bump typescript from 5.1.6 to 5.2.2 ([7e539ad](https://github.com/rvagg/bl/commit/7e539ad2e9cf959f431e82eaafe137cf33cf22ef))

## [6.0.6](https://github.com/rvagg/bl/compare/v6.0.5...v6.0.6) (2023-08-18)


### Trivial Changes

* **deps:** bump actions/setup-node from 3.8.0 to 3.8.1 ([39d3e17](https://github.com/rvagg/bl/commit/39d3e1729f0a7ddeac21e02b7983b0255ea212a2))

## [6.0.5](https://github.com/rvagg/bl/compare/v6.0.4...v6.0.5) (2023-08-15)


### Trivial Changes

* **deps:** bump actions/setup-node from 3.7.0 to 3.8.0 ([183d80a](https://github.com/rvagg/bl/commit/183d80a616a32e5473ac47e46cecd19ca0dfcf9f))

## [6.0.4](https://github.com/rvagg/bl/compare/v6.0.3...v6.0.4) (2023-08-07)


### Trivial Changes

* **deps-dev:** bump @types/readable-stream from 2.3.15 to 4.0.0 ([dd8cdb0](https://github.com/rvagg/bl/commit/dd8cdb0c64e1272c21d3bb251971afaaabbb0a1b))

## [6.0.3](https://github.com/rvagg/bl/compare/v6.0.2...v6.0.3) (2023-07-07)


### Trivial Changes

* **deps:** bump actions/setup-node from 3.6.0 to 3.7.0 ([40ac0a5](https://github.com/rvagg/bl/commit/40ac0a52e3c1ef83ae95d9433aebe4135f79b761))

## [6.0.2](https://github.com/rvagg/bl/compare/v6.0.1...v6.0.2) (2023-06-05)


### Trivial Changes

* **deps-dev:** bump typescript from 5.0.4 to 5.1.3 ([bea30ad](https://github.com/rvagg/bl/commit/bea30addef635d30f6e97769afacf5049615cdfe))

## [6.0.1](https://github.com/rvagg/bl/compare/v6.0.0...v6.0.1) (2023-03-17)


### Bug Fixes

* release with Node.js 18 ([6965a1d](https://github.com/rvagg/bl/commit/6965a1dee6b2af5bca304c8c9b747b796a652ffd))


### Trivial Changes

* **deps-dev:** bump typescript from 4.9.5 to 5.0.2 ([0885658](https://github.com/rvagg/bl/commit/0885658f7c1696220ac846e5bbc19f8b6ae8d3c0))
* **no-release:** bump actions/setup-node from 3.5.1 to 3.6.0 ([#120](https://github.com/rvagg/bl/issues/120)) ([60bee1b](https://github.com/rvagg/bl/commit/60bee1bd37a9f1a2a128f506f7da008c094db5c4))
* **no-release:** bump typescript from 4.8.4 to 4.9.3 ([#118](https://github.com/rvagg/bl/issues/118)) ([8be6dd6](https://github.com/rvagg/bl/commit/8be6dd62f639fd6c2c2f7d5d6ac4db988adb1886))

## [6.0.0](https://github.com/rvagg/bl/compare/v5.1.0...v6.0.0) (2022-10-19)


### ⚠ BREAKING CHANGES

* **deps:** bump readable-stream from 3.6.0 to 4.2.0
* added bigint (Int64) support

### Features

* added bigint (Int64) support ([131ad32](https://github.com/rvagg/bl/commit/131ad3217b91090323513a8ea3ef179e8427cf47))


### Trivial Changes

* add TypeScript definitions for BigInt ([78c5ff4](https://github.com/rvagg/bl/commit/78c5ff489235a4e4233086c364133123c71acef4))
* **deps-dev:** bump typescript from 4.7.4 to 4.8.4 ([dba13e1](https://github.com/rvagg/bl/commit/dba13e1cadc5857dde6a9425e975faf2abbb270f))
* **deps:** bump readable-stream from 3.6.0 to 4.2.0 ([fa03eda](https://github.com/rvagg/bl/commit/fa03eda54b4412c0fdfc9053bd0b0bebaf80bfd9))
* **docs:** BigInt in API docs ([c68af50](https://github.com/rvagg/bl/commit/c68af500a04b2c3a14132ae6946412d2e39402d0))

## [5.1.0](https://github.com/rvagg/bl/compare/v5.0.0...v5.1.0) (2022-10-18)


### Features

* added integrated TypeScript typings ([#108](https://github.com/rvagg/bl/issues/108)) ([433ff89](https://github.com/rvagg/bl/commit/433ff8942f47fab8a5c9d13b2c00989ccf8d0710))


### Bug Fixes

* windows support in tests ([387dfaf](https://github.com/rvagg/bl/commit/387dfaf9b2bca7849f12785436ceb01e42adac2c))


### Trivial Changes

* GH Actions, Dependabot, auto-release, remove Travis ([997f058](https://github.com/rvagg/bl/commit/997f058357de8f2a7f66998e80a72b491835573f))
* **no-release:** bump standard from 16.0.4 to 17.0.0 ([#112](https://github.com/rvagg/bl/issues/112)) ([078bfe3](https://github.com/rvagg/bl/commit/078bfe33390d125297b1c946e5989c4aa9228961))
