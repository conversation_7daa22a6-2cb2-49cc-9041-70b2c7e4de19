{"ast": null, "code": "var mode = {\n  N_POLE: 0,\n  S_POLE: 1,\n  EQUIT: 2,\n  OBLIQ: 3\n};\nimport { D2R, HALF_PI, EPSLN } from \"../constants/values\";\nimport hypot from \"../common/hypot\";\nvar params = {\n  h: {\n    def: 100000,\n    num: true\n  },\n  // default is Karman line, no default in PROJ.7\n  azi: {\n    def: 0,\n    num: true,\n    degrees: true\n  },\n  // default is North\n  tilt: {\n    def: 0,\n    num: true,\n    degrees: true\n  },\n  // default is Nadir\n  long0: {\n    def: 0,\n    num: true\n  },\n  // default is Greenwich, conversion to rad is automatic\n  lat0: {\n    def: 0,\n    num: true\n  } // default is Equator, conversion to rad is automatic\n};\nexport function init() {\n  Object.keys(params).forEach(function (p) {\n    if (typeof this[p] === \"undefined\") {\n      this[p] = params[p].def;\n    } else if (params[p].num && isNaN(this[p])) {\n      throw new Error(\"Invalid parameter value, must be numeric \" + p + \" = \" + this[p]);\n    } else if (params[p].num) {\n      this[p] = parseFloat(this[p]);\n    }\n    if (params[p].degrees) {\n      this[p] = this[p] * D2R;\n    }\n  }.bind(this));\n  if (Math.abs(Math.abs(this.lat0) - HALF_PI) < EPSLN) {\n    this.mode = this.lat0 < 0 ? mode.S_POLE : mode.N_POLE;\n  } else if (Math.abs(this.lat0) < EPSLN) {\n    this.mode = mode.EQUIT;\n  } else {\n    this.mode = mode.OBLIQ;\n    this.sinph0 = Math.sin(this.lat0);\n    this.cosph0 = Math.cos(this.lat0);\n  }\n  this.pn1 = this.h / this.a; // Normalize relative to the Earth's radius\n\n  if (this.pn1 <= 0 || this.pn1 > 1e10) {\n    throw new Error(\"Invalid height\");\n  }\n  this.p = 1 + this.pn1;\n  this.rp = 1 / this.p;\n  this.h1 = 1 / this.pn1;\n  this.pfact = (this.p + 1) * this.h1;\n  this.es = 0;\n  var omega = this.tilt;\n  var gamma = this.azi;\n  this.cg = Math.cos(gamma);\n  this.sg = Math.sin(gamma);\n  this.cw = Math.cos(omega);\n  this.sw = Math.sin(omega);\n}\nexport function forward(p) {\n  p.x -= this.long0;\n  var sinphi = Math.sin(p.y);\n  var cosphi = Math.cos(p.y);\n  var coslam = Math.cos(p.x);\n  var x, y;\n  switch (this.mode) {\n    case mode.OBLIQ:\n      y = this.sinph0 * sinphi + this.cosph0 * cosphi * coslam;\n      break;\n    case mode.EQUIT:\n      y = cosphi * coslam;\n      break;\n    case mode.S_POLE:\n      y = -sinphi;\n      break;\n    case mode.N_POLE:\n      y = sinphi;\n      break;\n  }\n  y = this.pn1 / (this.p - y);\n  x = y * cosphi * Math.sin(p.x);\n  switch (this.mode) {\n    case mode.OBLIQ:\n      y *= this.cosph0 * sinphi - this.sinph0 * cosphi * coslam;\n      break;\n    case mode.EQUIT:\n      y *= sinphi;\n      break;\n    case mode.N_POLE:\n      y *= -(cosphi * coslam);\n      break;\n    case mode.S_POLE:\n      y *= cosphi * coslam;\n      break;\n  }\n\n  // Tilt \n  var yt, ba;\n  yt = y * this.cg + x * this.sg;\n  ba = 1 / (yt * this.sw * this.h1 + this.cw);\n  x = (x * this.cg - y * this.sg) * this.cw * ba;\n  y = yt * ba;\n  p.x = x * this.a;\n  p.y = y * this.a;\n  return p;\n}\nexport function inverse(p) {\n  p.x /= this.a;\n  p.y /= this.a;\n  var r = {\n    x: p.x,\n    y: p.y\n  };\n\n  // Un-Tilt\n  var bm, bq, yt;\n  yt = 1 / (this.pn1 - p.y * this.sw);\n  bm = this.pn1 * p.x * yt;\n  bq = this.pn1 * p.y * this.cw * yt;\n  p.x = bm * this.cg + bq * this.sg;\n  p.y = bq * this.cg - bm * this.sg;\n  var rh = hypot(p.x, p.y);\n  if (Math.abs(rh) < EPSLN) {\n    r.x = 0;\n    r.y = p.y;\n  } else {\n    var cosz, sinz;\n    sinz = 1 - rh * rh * this.pfact;\n    sinz = (this.p - Math.sqrt(sinz)) / (this.pn1 / rh + rh / this.pn1);\n    cosz = Math.sqrt(1 - sinz * sinz);\n    switch (this.mode) {\n      case mode.OBLIQ:\n        r.y = Math.asin(cosz * this.sinph0 + p.y * sinz * this.cosph0 / rh);\n        p.y = (cosz - this.sinph0 * Math.sin(r.y)) * rh;\n        p.x *= sinz * this.cosph0;\n        break;\n      case mode.EQUIT:\n        r.y = Math.asin(p.y * sinz / rh);\n        p.y = cosz * rh;\n        p.x *= sinz;\n        break;\n      case mode.N_POLE:\n        r.y = Math.asin(cosz);\n        p.y = -p.y;\n        break;\n      case mode.S_POLE:\n        r.y = -Math.asin(cosz);\n        break;\n    }\n    r.x = Math.atan2(p.x, p.y);\n  }\n  p.x = r.x + this.long0;\n  p.y = r.y;\n  return p;\n}\nexport var names = [\"Tilted_Perspective\", \"tpers\"];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};", "map": {"version": 3, "names": ["mode", "N_POLE", "S_POLE", "EQUIT", "OBLIQ", "D2R", "HALF_PI", "EPSLN", "hypot", "params", "h", "def", "num", "azi", "degrees", "tilt", "long0", "lat0", "init", "Object", "keys", "for<PERSON>ach", "p", "isNaN", "Error", "parseFloat", "bind", "Math", "abs", "sinph0", "sin", "cosph0", "cos", "pn1", "a", "rp", "h1", "pfact", "es", "omega", "gamma", "cg", "sg", "cw", "sw", "forward", "x", "sinphi", "y", "cosphi", "coslam", "yt", "ba", "inverse", "r", "bm", "bq", "rh", "co<PERSON>", "sinz", "sqrt", "asin", "atan2", "names"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/proj4/lib/projections/tpers.js"], "sourcesContent": ["\nvar mode = {\n  N_POLE: 0,\n  S_POLE: 1,\n  EQUIT: 2,\n  OBLIQ: 3\n};\n\nimport { D2R, HALF_PI, EPSLN } from \"../constants/values\";\nimport hypot from \"../common/hypot\";\n\nvar params = {\n  h:     { def: 100000, num: true },           // default is Karman line, no default in PROJ.7\n  azi:   { def: 0, num: true, degrees: true }, // default is North\n  tilt:  { def: 0, num: true, degrees: true }, // default is Nadir\n  long0: { def: 0, num: true },                // default is Greenwich, conversion to rad is automatic\n  lat0:  { def: 0, num: true }                 // default is Equator, conversion to rad is automatic\n};\n\nexport function init() {\n  Object.keys(params).forEach(function (p) {\n    if (typeof this[p] === \"undefined\") {\n      this[p] = params[p].def;\n    } else if (params[p].num && isNaN(this[p])) {\n      throw new Error(\"Invalid parameter value, must be numeric \" + p + \" = \" + this[p]);\n    } else if (params[p].num) {\n      this[p] = parseFloat(this[p]);\n    }\n    if (params[p].degrees) {\n      this[p] = this[p] * D2R;\n    }\n  }.bind(this));\n\n  if (Math.abs((Math.abs(this.lat0) - HALF_PI)) < EPSLN) {\n    this.mode = this.lat0 < 0 ? mode.S_POLE : mode.N_POLE;\n  } else if (Math.abs(this.lat0) < EPSLN) {\n    this.mode = mode.EQUIT;\n  } else {\n    this.mode = mode.OBLIQ;\n    this.sinph0 = Math.sin(this.lat0);\n    this.cosph0 = Math.cos(this.lat0);\n  }\n\n  this.pn1 = this.h / this.a;  // Normalize relative to the Earth's radius\n\n  if (this.pn1 <= 0 || this.pn1 > 1e10) {\n    throw new Error(\"Invalid height\");\n  }\n  \n  this.p = 1 + this.pn1;\n  this.rp = 1 / this.p;\n  this.h1 = 1 / this.pn1;\n  this.pfact = (this.p + 1) * this.h1;\n  this.es = 0;\n\n  var omega = this.tilt;\n  var gamma = this.azi;\n  this.cg = Math.cos(gamma);\n  this.sg = Math.sin(gamma);\n  this.cw = Math.cos(omega);\n  this.sw = Math.sin(omega);\n}\n\nexport function forward(p) {\n  p.x -= this.long0;\n  var sinphi = Math.sin(p.y);\n  var cosphi = Math.cos(p.y);\n  var coslam = Math.cos(p.x);\n  var x, y;\n  switch (this.mode) {\n    case mode.OBLIQ:\n      y = this.sinph0 * sinphi + this.cosph0 * cosphi * coslam;\n      break;\n    case mode.EQUIT:\n      y = cosphi * coslam;\n      break;\n    case mode.S_POLE:\n      y = -sinphi;\n      break;\n    case mode.N_POLE:\n      y = sinphi;\n      break;\n  }\n  y = this.pn1 / (this.p - y);\n  x = y * cosphi * Math.sin(p.x);\n\n  switch (this.mode) {\n    case mode.OBLIQ:\n      y *= this.cosph0 * sinphi - this.sinph0 * cosphi * coslam;\n      break;\n    case mode.EQUIT:\n      y *= sinphi;\n      break;\n    case mode.N_POLE:\n      y *= -(cosphi * coslam);\n      break;\n    case mode.S_POLE:\n      y *= cosphi * coslam;\n      break;\n  }\n\n  // Tilt \n  var yt, ba;\n  yt = y * this.cg + x * this.sg;\n  ba = 1 / (yt * this.sw * this.h1 + this.cw);\n  x = (x * this.cg - y * this.sg) * this.cw * ba;\n  y = yt * ba;\n\n  p.x = x * this.a;\n  p.y = y * this.a;\n  return p;\n}\n\nexport function inverse(p) {\n  p.x /= this.a;\n  p.y /= this.a;\n  var r = { x: p.x, y: p.y };\n\n  // Un-Tilt\n  var bm, bq, yt;\n  yt = 1 / (this.pn1 - p.y * this.sw);\n  bm = this.pn1 * p.x * yt;\n  bq = this.pn1 * p.y * this.cw * yt;\n  p.x = bm * this.cg + bq * this.sg;\n  p.y = bq * this.cg - bm * this.sg;\n\n  var rh = hypot(p.x, p.y);\n  if (Math.abs(rh) < EPSLN) {\n    r.x = 0;\n    r.y = p.y;\n  } else {\n    var cosz, sinz;\n    sinz = 1 - rh * rh * this.pfact;\n    sinz = (this.p - Math.sqrt(sinz)) / (this.pn1 / rh + rh / this.pn1);\n    cosz = Math.sqrt(1 - sinz * sinz);\n    switch (this.mode) {\n      case mode.OBLIQ:\n        r.y = Math.asin(cosz * this.sinph0 + p.y * sinz * this.cosph0 / rh);\n        p.y = (cosz - this.sinph0 * Math.sin(r.y)) * rh;\n        p.x *= sinz * this.cosph0;\n        break;\n      case mode.EQUIT:\n        r.y = Math.asin(p.y * sinz / rh);\n        p.y = cosz * rh;\n        p.x *= sinz;\n        break;\n      case mode.N_POLE:\n        r.y = Math.asin(cosz);\n        p.y = -p.y;\n        break;\n      case mode.S_POLE:\n        r.y = -Math.asin(cosz);\n        break;\n    }\n    r.x = Math.atan2(p.x, p.y);\n  }\n\n  p.x = r.x + this.long0;\n  p.y = r.y;\n  return p;\n}\n\nexport var names = [\"Tilted_Perspective\", \"tpers\"];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n"], "mappings": "AACA,IAAIA,IAAI,GAAG;EACTC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE;AACT,CAAC;AAED,SAASC,GAAG,EAAEC,OAAO,EAAEC,KAAK,QAAQ,qBAAqB;AACzD,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,IAAIC,MAAM,GAAG;EACXC,CAAC,EAAM;IAAEC,GAAG,EAAE,MAAM;IAAEC,GAAG,EAAE;EAAK,CAAC;EAAY;EAC7CC,GAAG,EAAI;IAAEF,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,IAAI;IAAEE,OAAO,EAAE;EAAK,CAAC;EAAE;EAC7CC,IAAI,EAAG;IAAEJ,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE,IAAI;IAAEE,OAAO,EAAE;EAAK,CAAC;EAAE;EAC7CE,KAAK,EAAE;IAAEL,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE;EAAK,CAAC;EAAiB;EAC7CK,IAAI,EAAG;IAAEN,GAAG,EAAE,CAAC;IAAEC,GAAG,EAAE;EAAK,CAAC,CAAiB;AAC/C,CAAC;AAED,OAAO,SAASM,IAAIA,CAAA,EAAG;EACrBC,MAAM,CAACC,IAAI,CAACX,MAAM,CAAC,CAACY,OAAO,CAAC,UAAUC,CAAC,EAAE;IACvC,IAAI,OAAO,IAAI,CAACA,CAAC,CAAC,KAAK,WAAW,EAAE;MAClC,IAAI,CAACA,CAAC,CAAC,GAAGb,MAAM,CAACa,CAAC,CAAC,CAACX,GAAG;IACzB,CAAC,MAAM,IAAIF,MAAM,CAACa,CAAC,CAAC,CAACV,GAAG,IAAIW,KAAK,CAAC,IAAI,CAACD,CAAC,CAAC,CAAC,EAAE;MAC1C,MAAM,IAAIE,KAAK,CAAC,2CAA2C,GAAGF,CAAC,GAAG,KAAK,GAAG,IAAI,CAACA,CAAC,CAAC,CAAC;IACpF,CAAC,MAAM,IAAIb,MAAM,CAACa,CAAC,CAAC,CAACV,GAAG,EAAE;MACxB,IAAI,CAACU,CAAC,CAAC,GAAGG,UAAU,CAAC,IAAI,CAACH,CAAC,CAAC,CAAC;IAC/B;IACA,IAAIb,MAAM,CAACa,CAAC,CAAC,CAACR,OAAO,EAAE;MACrB,IAAI,CAACQ,CAAC,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC,GAAGjB,GAAG;IACzB;EACF,CAAC,CAACqB,IAAI,CAAC,IAAI,CAAC,CAAC;EAEb,IAAIC,IAAI,CAACC,GAAG,CAAED,IAAI,CAACC,GAAG,CAAC,IAAI,CAACX,IAAI,CAAC,GAAGX,OAAQ,CAAC,GAAGC,KAAK,EAAE;IACrD,IAAI,CAACP,IAAI,GAAG,IAAI,CAACiB,IAAI,GAAG,CAAC,GAAGjB,IAAI,CAACE,MAAM,GAAGF,IAAI,CAACC,MAAM;EACvD,CAAC,MAAM,IAAI0B,IAAI,CAACC,GAAG,CAAC,IAAI,CAACX,IAAI,CAAC,GAAGV,KAAK,EAAE;IACtC,IAAI,CAACP,IAAI,GAAGA,IAAI,CAACG,KAAK;EACxB,CAAC,MAAM;IACL,IAAI,CAACH,IAAI,GAAGA,IAAI,CAACI,KAAK;IACtB,IAAI,CAACyB,MAAM,GAAGF,IAAI,CAACG,GAAG,CAAC,IAAI,CAACb,IAAI,CAAC;IACjC,IAAI,CAACc,MAAM,GAAGJ,IAAI,CAACK,GAAG,CAAC,IAAI,CAACf,IAAI,CAAC;EACnC;EAEA,IAAI,CAACgB,GAAG,GAAG,IAAI,CAACvB,CAAC,GAAG,IAAI,CAACwB,CAAC,CAAC,CAAE;;EAE7B,IAAI,IAAI,CAACD,GAAG,IAAI,CAAC,IAAI,IAAI,CAACA,GAAG,GAAG,IAAI,EAAE;IACpC,MAAM,IAAIT,KAAK,CAAC,gBAAgB,CAAC;EACnC;EAEA,IAAI,CAACF,CAAC,GAAG,CAAC,GAAG,IAAI,CAACW,GAAG;EACrB,IAAI,CAACE,EAAE,GAAG,CAAC,GAAG,IAAI,CAACb,CAAC;EACpB,IAAI,CAACc,EAAE,GAAG,CAAC,GAAG,IAAI,CAACH,GAAG;EACtB,IAAI,CAACI,KAAK,GAAG,CAAC,IAAI,CAACf,CAAC,GAAG,CAAC,IAAI,IAAI,CAACc,EAAE;EACnC,IAAI,CAACE,EAAE,GAAG,CAAC;EAEX,IAAIC,KAAK,GAAG,IAAI,CAACxB,IAAI;EACrB,IAAIyB,KAAK,GAAG,IAAI,CAAC3B,GAAG;EACpB,IAAI,CAAC4B,EAAE,GAAGd,IAAI,CAACK,GAAG,CAACQ,KAAK,CAAC;EACzB,IAAI,CAACE,EAAE,GAAGf,IAAI,CAACG,GAAG,CAACU,KAAK,CAAC;EACzB,IAAI,CAACG,EAAE,GAAGhB,IAAI,CAACK,GAAG,CAACO,KAAK,CAAC;EACzB,IAAI,CAACK,EAAE,GAAGjB,IAAI,CAACG,GAAG,CAACS,KAAK,CAAC;AAC3B;AAEA,OAAO,SAASM,OAAOA,CAACvB,CAAC,EAAE;EACzBA,CAAC,CAACwB,CAAC,IAAI,IAAI,CAAC9B,KAAK;EACjB,IAAI+B,MAAM,GAAGpB,IAAI,CAACG,GAAG,CAACR,CAAC,CAAC0B,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAGtB,IAAI,CAACK,GAAG,CAACV,CAAC,CAAC0B,CAAC,CAAC;EAC1B,IAAIE,MAAM,GAAGvB,IAAI,CAACK,GAAG,CAACV,CAAC,CAACwB,CAAC,CAAC;EAC1B,IAAIA,CAAC,EAAEE,CAAC;EACR,QAAQ,IAAI,CAAChD,IAAI;IACf,KAAKA,IAAI,CAACI,KAAK;MACb4C,CAAC,GAAG,IAAI,CAACnB,MAAM,GAAGkB,MAAM,GAAG,IAAI,CAAChB,MAAM,GAAGkB,MAAM,GAAGC,MAAM;MACxD;IACF,KAAKlD,IAAI,CAACG,KAAK;MACb6C,CAAC,GAAGC,MAAM,GAAGC,MAAM;MACnB;IACF,KAAKlD,IAAI,CAACE,MAAM;MACd8C,CAAC,GAAG,CAACD,MAAM;MACX;IACF,KAAK/C,IAAI,CAACC,MAAM;MACd+C,CAAC,GAAGD,MAAM;MACV;EACJ;EACAC,CAAC,GAAG,IAAI,CAACf,GAAG,IAAI,IAAI,CAACX,CAAC,GAAG0B,CAAC,CAAC;EAC3BF,CAAC,GAAGE,CAAC,GAAGC,MAAM,GAAGtB,IAAI,CAACG,GAAG,CAACR,CAAC,CAACwB,CAAC,CAAC;EAE9B,QAAQ,IAAI,CAAC9C,IAAI;IACf,KAAKA,IAAI,CAACI,KAAK;MACb4C,CAAC,IAAI,IAAI,CAACjB,MAAM,GAAGgB,MAAM,GAAG,IAAI,CAAClB,MAAM,GAAGoB,MAAM,GAAGC,MAAM;MACzD;IACF,KAAKlD,IAAI,CAACG,KAAK;MACb6C,CAAC,IAAID,MAAM;MACX;IACF,KAAK/C,IAAI,CAACC,MAAM;MACd+C,CAAC,IAAI,EAAEC,MAAM,GAAGC,MAAM,CAAC;MACvB;IACF,KAAKlD,IAAI,CAACE,MAAM;MACd8C,CAAC,IAAIC,MAAM,GAAGC,MAAM;MACpB;EACJ;;EAEA;EACA,IAAIC,EAAE,EAAEC,EAAE;EACVD,EAAE,GAAGH,CAAC,GAAG,IAAI,CAACP,EAAE,GAAGK,CAAC,GAAG,IAAI,CAACJ,EAAE;EAC9BU,EAAE,GAAG,CAAC,IAAID,EAAE,GAAG,IAAI,CAACP,EAAE,GAAG,IAAI,CAACR,EAAE,GAAG,IAAI,CAACO,EAAE,CAAC;EAC3CG,CAAC,GAAG,CAACA,CAAC,GAAG,IAAI,CAACL,EAAE,GAAGO,CAAC,GAAG,IAAI,CAACN,EAAE,IAAI,IAAI,CAACC,EAAE,GAAGS,EAAE;EAC9CJ,CAAC,GAAGG,EAAE,GAAGC,EAAE;EAEX9B,CAAC,CAACwB,CAAC,GAAGA,CAAC,GAAG,IAAI,CAACZ,CAAC;EAChBZ,CAAC,CAAC0B,CAAC,GAAGA,CAAC,GAAG,IAAI,CAACd,CAAC;EAChB,OAAOZ,CAAC;AACV;AAEA,OAAO,SAAS+B,OAAOA,CAAC/B,CAAC,EAAE;EACzBA,CAAC,CAACwB,CAAC,IAAI,IAAI,CAACZ,CAAC;EACbZ,CAAC,CAAC0B,CAAC,IAAI,IAAI,CAACd,CAAC;EACb,IAAIoB,CAAC,GAAG;IAAER,CAAC,EAAExB,CAAC,CAACwB,CAAC;IAAEE,CAAC,EAAE1B,CAAC,CAAC0B;EAAE,CAAC;;EAE1B;EACA,IAAIO,EAAE,EAAEC,EAAE,EAAEL,EAAE;EACdA,EAAE,GAAG,CAAC,IAAI,IAAI,CAAClB,GAAG,GAAGX,CAAC,CAAC0B,CAAC,GAAG,IAAI,CAACJ,EAAE,CAAC;EACnCW,EAAE,GAAG,IAAI,CAACtB,GAAG,GAAGX,CAAC,CAACwB,CAAC,GAAGK,EAAE;EACxBK,EAAE,GAAG,IAAI,CAACvB,GAAG,GAAGX,CAAC,CAAC0B,CAAC,GAAG,IAAI,CAACL,EAAE,GAAGQ,EAAE;EAClC7B,CAAC,CAACwB,CAAC,GAAGS,EAAE,GAAG,IAAI,CAACd,EAAE,GAAGe,EAAE,GAAG,IAAI,CAACd,EAAE;EACjCpB,CAAC,CAAC0B,CAAC,GAAGQ,EAAE,GAAG,IAAI,CAACf,EAAE,GAAGc,EAAE,GAAG,IAAI,CAACb,EAAE;EAEjC,IAAIe,EAAE,GAAGjD,KAAK,CAACc,CAAC,CAACwB,CAAC,EAAExB,CAAC,CAAC0B,CAAC,CAAC;EACxB,IAAIrB,IAAI,CAACC,GAAG,CAAC6B,EAAE,CAAC,GAAGlD,KAAK,EAAE;IACxB+C,CAAC,CAACR,CAAC,GAAG,CAAC;IACPQ,CAAC,CAACN,CAAC,GAAG1B,CAAC,CAAC0B,CAAC;EACX,CAAC,MAAM;IACL,IAAIU,IAAI,EAAEC,IAAI;IACdA,IAAI,GAAG,CAAC,GAAGF,EAAE,GAAGA,EAAE,GAAG,IAAI,CAACpB,KAAK;IAC/BsB,IAAI,GAAG,CAAC,IAAI,CAACrC,CAAC,GAAGK,IAAI,CAACiC,IAAI,CAACD,IAAI,CAAC,KAAK,IAAI,CAAC1B,GAAG,GAAGwB,EAAE,GAAGA,EAAE,GAAG,IAAI,CAACxB,GAAG,CAAC;IACnEyB,IAAI,GAAG/B,IAAI,CAACiC,IAAI,CAAC,CAAC,GAAGD,IAAI,GAAGA,IAAI,CAAC;IACjC,QAAQ,IAAI,CAAC3D,IAAI;MACf,KAAKA,IAAI,CAACI,KAAK;QACbkD,CAAC,CAACN,CAAC,GAAGrB,IAAI,CAACkC,IAAI,CAACH,IAAI,GAAG,IAAI,CAAC7B,MAAM,GAAGP,CAAC,CAAC0B,CAAC,GAAGW,IAAI,GAAG,IAAI,CAAC5B,MAAM,GAAG0B,EAAE,CAAC;QACnEnC,CAAC,CAAC0B,CAAC,GAAG,CAACU,IAAI,GAAG,IAAI,CAAC7B,MAAM,GAAGF,IAAI,CAACG,GAAG,CAACwB,CAAC,CAACN,CAAC,CAAC,IAAIS,EAAE;QAC/CnC,CAAC,CAACwB,CAAC,IAAIa,IAAI,GAAG,IAAI,CAAC5B,MAAM;QACzB;MACF,KAAK/B,IAAI,CAACG,KAAK;QACbmD,CAAC,CAACN,CAAC,GAAGrB,IAAI,CAACkC,IAAI,CAACvC,CAAC,CAAC0B,CAAC,GAAGW,IAAI,GAAGF,EAAE,CAAC;QAChCnC,CAAC,CAAC0B,CAAC,GAAGU,IAAI,GAAGD,EAAE;QACfnC,CAAC,CAACwB,CAAC,IAAIa,IAAI;QACX;MACF,KAAK3D,IAAI,CAACC,MAAM;QACdqD,CAAC,CAACN,CAAC,GAAGrB,IAAI,CAACkC,IAAI,CAACH,IAAI,CAAC;QACrBpC,CAAC,CAAC0B,CAAC,GAAG,CAAC1B,CAAC,CAAC0B,CAAC;QACV;MACF,KAAKhD,IAAI,CAACE,MAAM;QACdoD,CAAC,CAACN,CAAC,GAAG,CAACrB,IAAI,CAACkC,IAAI,CAACH,IAAI,CAAC;QACtB;IACJ;IACAJ,CAAC,CAACR,CAAC,GAAGnB,IAAI,CAACmC,KAAK,CAACxC,CAAC,CAACwB,CAAC,EAAExB,CAAC,CAAC0B,CAAC,CAAC;EAC5B;EAEA1B,CAAC,CAACwB,CAAC,GAAGQ,CAAC,CAACR,CAAC,GAAG,IAAI,CAAC9B,KAAK;EACtBM,CAAC,CAAC0B,CAAC,GAAGM,CAAC,CAACN,CAAC;EACT,OAAO1B,CAAC;AACV;AAEA,OAAO,IAAIyC,KAAK,GAAG,CAAC,oBAAoB,EAAE,OAAO,CAAC;AAClD,eAAe;EACb7C,IAAI,EAAEA,IAAI;EACV2B,OAAO,EAAEA,OAAO;EAChBQ,OAAO,EAAEA,OAAO;EAChBU,KAAK,EAAEA;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}