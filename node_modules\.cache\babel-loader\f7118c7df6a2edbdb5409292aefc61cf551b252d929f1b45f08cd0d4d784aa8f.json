{"ast": null, "code": "var _jsxFileName = \"G:\\\\AI_tools\\\\cursor\\\\projects\\\\education web\\\\src\\\\components\\\\CampusModel.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null; // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false; // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\nlet trafficParticipants = new Map(); // 使用 Map 存储所有交通参与者\nlet participantMeshes = new Map(); // 存储交通参与者的 3D 模型\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  wsUrl: `ws://${window.location.hostname}:8083/mqtt`,\n  username: 'cloud',\n  password: 'A825FBCBB97D1975',\n  clientId: `V2X_CENTER_CSLG_web_client_${Date.now()}`,\n  // 添加时间戳避免ID冲突\n  topics: ['changli/cloud/v2x/rsu/rsm', 'changli/cloud/v2x/obu/bsm']\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 加载车辆模型\nconst loadVehicleModel = (url, position, heading) => {\n  return new Promise((resolve, reject) => {\n    const loader = new GLTFLoader();\n    loader.load(url, gltf => {\n      try {\n        const model = gltf.scene;\n\n        // 调整模型材质\n        model.traverse(child => {\n          if (child.isMesh) {\n            child.material = new THREE.MeshStandardMaterial({\n              color: 0xffffff,\n              metalness: 0.2,\n              roughness: 0.1,\n              envMapIntensity: 1.0\n            });\n          }\n        });\n\n        // 设置位置和朝向\n        model.position.copy(position);\n        model.rotation.y = Math.PI - heading * Math.PI / 180;\n        resolve(model);\n      } catch (error) {\n        reject(error);\n      }\n    }, undefined, reject);\n  });\n};\nconst CampusModel = () => {\n  _s();\n  const containerRef = useRef(null);\n  const sceneRef = useRef(null);\n  const cameraRef = useRef(null);\n  const rendererRef = useRef(null);\n  const controlsRef = useRef(null);\n  const converterRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [pendingMessages, setPendingMessages] = useState([]);\n  const animationFrameRef = useRef(null);\n\n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n\n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    if (controlsRef.current) {\n      controlsRef.current.enabled = false;\n    }\n  };\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    if (cameraRef.current && controlsRef.current) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n\n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos).to({\n        x: 0,\n        y: 300,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.position.copy(currentPos);\n      }).start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp).to({\n        x: 0,\n        y: 1,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        cameraRef.current.up.copy(currentUp);\n      }).start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controlsRef.current.target.clone();\n\n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget).to({\n        x: 0,\n        y: 0,\n        z: 0\n      }, 1000).easing(TWEEN.Easing.Quadratic.InOut).onUpdate(() => {\n        controlsRef.current.target.copy(currentTarget);\n        // 确保相机始终朝向目标点\n        cameraRef.current.lookAt(controlsRef.current.target);\n        controlsRef.current.update();\n      }).start();\n\n      // 启用控制器\n      controlsRef.current.enabled = true;\n\n      // 重置控制器的一些属性\n      controlsRef.current.minDistance = 50;\n      controlsRef.current.maxDistance = 500;\n      controlsRef.current.maxPolarAngle = Math.PI / 2.1;\n      controlsRef.current.minPolarAngle = 0;\n      controlsRef.current.update();\n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改初始化 MQTT 连接\n  const initMqttClient = () => {\n    const wsUrl = MQTT_CONFIG.wsUrl;\n    console.log('正在连接WebSocket...', {\n      url: wsUrl,\n      clientId: MQTT_CONFIG.clientId\n    });\n    try {\n      if (mqttClientRef.current && mqttClientRef.current.readyState === WebSocket.OPEN) {\n        console.log('关闭现有WebSocket连接');\n        mqttClientRef.current.close();\n      }\n      const ws = new WebSocket(wsUrl);\n\n      // 添加连接超时处理\n      const connectionTimeout = setTimeout(() => {\n        if (ws.readyState !== WebSocket.OPEN) {\n          console.error('WebSocket连接超时');\n          ws.close();\n        }\n      }, 5000);\n      ws.onopen = () => {\n        clearTimeout(connectionTimeout);\n        console.log('WebSocket连接成功');\n\n        // 订阅主题\n        MQTT_CONFIG.topics.forEach(topic => {\n          ws.send(JSON.stringify({\n            type: 'subscribe',\n            topic: topic\n          }));\n          console.log('订阅主题:', topic);\n        });\n      };\n      ws.onclose = event => {\n        clearTimeout(connectionTimeout);\n        console.log('WebSocket连接关闭:', {\n          code: event.code,\n          reason: event.reason,\n          wasClean: event.wasClean\n        });\n\n        // 只在页面可见时尝试重新连接\n        setTimeout(() => {\n          if (document.visibilityState === 'visible') {\n            console.log('尝试重新连接...');\n            initMqttClient();\n          }\n        }, 5000);\n      };\n      ws.onerror = error => {\n        console.error('WebSocket错误:', {\n          error: error.message || '未知错误',\n          readyState: ws.readyState,\n          url: wsUrl\n        });\n      };\n\n      // 添加消息处理\n      ws.onmessage = event => {\n        try {\n          if (!event.data) {\n            console.warn('收到空消息');\n            return;\n          }\n          const message = JSON.parse(event.data);\n\n          // 处理连接确认消息\n          if (message.type === 'connection' && message.status === 'connected') {\n            console.log('WebSocket连接已确认:', {\n              状态: message.status,\n              时间: new Date(message.time).toLocaleString()\n            });\n            return;\n          }\n\n          // 处理普通消息\n          if (!message.topic || !message.message) {\n            return; // 忽略非数据消息\n          }\n          let data;\n          try {\n            data = JSON.parse(message.message);\n          } catch (error) {\n            console.error('解析消息内容失败:', {\n              error: error.message,\n              message: message.message\n            });\n            return;\n          }\n          console.log('收到消息:', {\n            主题: message.topic,\n            数据类型: data.type,\n            时间戳: data.tm,\n            数据长度: message.message.length\n          });\n\n          // 如果场景未初始化，将消息存入缓存\n          if (!isInitialized) {\n            if (message.topic === 'changli/cloud/v2x/rsu/rsm') {\n              console.log('场景未初始化，缓存RSM消息');\n              setPendingMessages(prev => [...prev, data]);\n            }\n            return;\n          }\n\n          // 场景已初始化，正常处理消息\n          switch (message.topic) {\n            case 'changli/cloud/v2x/rsu/rsm':\n              handleRSMMessage(data);\n              break;\n            case 'changli/cloud/v2x/obu/bsm':\n              handleBSMMessage(data);\n              break;\n            default:\n              console.warn('未知的消息主题:', message.topic);\n          }\n        } catch (error) {\n          console.error('处理WebSocket消息失败:', error);\n        }\n      };\n      mqttClientRef.current = ws;\n    } catch (error) {\n      console.error('创建WebSocket连接失败:', {\n        error: error.message,\n        stack: error.stack,\n        wsUrl\n      });\n    }\n  };\n\n  // 处理 BSM 消息\n  const handleBSMMessage = data => {\n    if (data.type === 'BSM' && data.data) {\n      const bsmData = data.data;\n      const newState = {\n        longitude: parseFloat(bsmData.partLong),\n        latitude: parseFloat(bsmData.partLat),\n        speed: parseFloat(bsmData.partSpeed),\n        heading: parseFloat(bsmData.partHeading)\n      };\n      console.log('解析后的车辆状态:', newState);\n\n      // 更新车辆位置和状态\n      if (globalVehicleRef) {\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        globalVehicleRef.position.copy(newPosition);\n        globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n        globalVehicleRef.updateMatrix();\n        globalVehicleRef.updateMatrixWorld(true);\n        setVehicleState(newState);\n        console.log('车辆位置已更新:', newPosition);\n      }\n    }\n  };\n\n  // 处理 RSM 消息\n  const handleRSMMessage = data => {\n    try {\n      // 验证消息格式\n      if (!data || !data.data || !Array.isArray(data.data.participants)) {\n        console.warn('无效的RSM消息格式:', data);\n        return;\n      }\n\n      // 如果场景未初始化，缓存消息\n      if (!isInitialized) {\n        console.log('场景未初始化，缓存RSM消息');\n        setPendingMessages(prev => [...prev, data]);\n        return;\n      }\n      console.log('处理RSM消息:', {\n        设备ID: data.data.rsuId,\n        时间戳: data.tm,\n        消息来源: data.source,\n        基准点: {\n          经度: data.data.posLong,\n          纬度: data.data.posLat\n        },\n        参与者数量: data.data.participants.length\n      });\n\n      // 处理每个参与者\n      data.data.participants.forEach(participant => {\n        try {\n          updateTrafficParticipant(participant);\n        } catch (error) {\n          console.error('处理参与者失败:', error);\n        }\n      });\n    } catch (error) {\n      console.error('处理RSM消息失败:', error);\n    }\n  };\n\n  // 获取参与者类型名称\n  const getParticipantTypeName = type => {\n    const types = {\n      '0': '未知',\n      '1': '机动车',\n      '2': '非机动车',\n      '3': '行人'\n    };\n    return types[type] || '未知';\n  };\n\n  // 获取参与者尺寸类型名称\n  const getParticipantSizeTypeName = sizeType => {\n    const types = {\n      '1': '小型车(长度<600cm)',\n      '2': '中型车(600cm≤长度<1300cm)',\n      '3': '大型车(长度≥1300cm)'\n    };\n    return types[sizeType] || '未知';\n  };\n\n  // 更新交通参与者\n  const updateTrafficParticipant = async participant => {\n    if (!sceneRef.current || !converterRef.current) {\n      console.warn('场景或坐标转换器未初始化');\n      return;\n    }\n    const id = participant.partPtcId;\n    const type = parseInt(participant.partPtcType);\n    const sizeType = parseInt(participant.partSizeType || '1');\n    try {\n      // 转换坐标\n      const modelPos = converterRef.current.wgs84ToModel(parseFloat(participant.partPosLong), parseFloat(participant.partPosLat));\n\n      // 设置不同类型参与者的默认尺寸和颜色\n      const participantConfig = {\n        1: {\n          // 机动车\n          model: 'vehicle',\n          size: {\n            // 根据 partSizeType 设置不同尺寸\n            1: {\n              length: 4.5,\n              width: 1.8,\n              height: 1.5\n            },\n            // 小车\n            2: {\n              length: 8.0,\n              width: 2.3,\n              height: 2.5\n            },\n            // 中型车\n            3: {\n              length: 12.0,\n              width: 2.5,\n              height: 3.0\n            } // 大型车\n          },\n          color: 0x4444ff,\n          scale: 0.7\n        },\n        2: {\n          // 非机动车\n          model: 'box',\n          size: {\n            length: 1.8,\n            width: 0.6,\n            height: 1.2\n          },\n          color: 0x44ff44\n        },\n        3: {\n          // 行人\n          model: 'cylinder',\n          size: {\n            length: 0.5,\n            width: 0.5,\n            height: 1.7\n          },\n          color: 0xff4444\n        }\n      };\n      const config = participantConfig[type] || participantConfig[1];\n      // 获取实际尺寸（单位转换：cm -> m）\n      const actualSize = {\n        length: parseFloat(participant.partLength || '0') / 100,\n        width: parseFloat(participant.partWidth || '0') / 100,\n        height: parseFloat(participant.partHeight || '0') / 100\n      };\n\n      // 使用实际尺寸或默认尺寸\n      const size = {\n        length: actualSize.length > 0 ? actualSize.length : type === 1 ? config.size[sizeType].length : config.size.length,\n        width: actualSize.width > 0 ? actualSize.width : type === 1 ? config.size[sizeType].width : config.size.width,\n        height: actualSize.height > 0 ? actualSize.height : type === 1 ? config.size[sizeType].height : config.size.height\n      };\n\n      // 创建或更新参与者数据\n      const participantData = {\n        position: new THREE.Vector3(modelPos.x, 0.5, -modelPos.y),\n        heading: parseFloat(participant.partHeading || 0),\n        speed: parseFloat(participant.partSpeed || 0),\n        type: type,\n        sizeType: sizeType,\n        config: config,\n        size: size,\n        lastUpdate: Date.now()\n      };\n\n      // 更新或创建3D模型\n      if (!participantMeshes.has(id)) {\n        const mesh = await createParticipantMesh(participantData);\n        if (mesh) {\n          mesh.name = `participant_${id}_${type}_${sizeType}`;\n          sceneRef.current.add(mesh);\n          participantMeshes.set(id, mesh);\n          console.log('创建新参与者:', {\n            ID: id,\n            类型: type,\n            尺寸类型: sizeType,\n            实际尺寸: size,\n            位置: modelPos,\n            速度: participantData.speed\n          });\n        }\n      } else {\n        const mesh = participantMeshes.get(id);\n        updateParticipantMesh(mesh, participantData);\n      }\n      trafficParticipants.set(id, participantData);\n    } catch (error) {\n      console.error('更新交通参与者失败:', {\n        错误: error.message,\n        参与者ID: id,\n        类型: type,\n        尺寸类型: sizeType,\n        原始数据: participant\n      });\n    }\n  };\n\n  // 创建交通参与者的3D模型\n  const createParticipantMesh = async data => {\n    let mesh;\n    try {\n      switch (data.config.model) {\n        case 'vehicle':\n          try {\n            // 使用现有的车辆模型\n            const vehicleModel = await loadVehicleModel(`${BASE_URL}/changli2/Audi R8.glb`, data.position, data.heading);\n            mesh = new THREE.Group();\n            mesh.add(vehicleModel);\n            vehicleModel.scale.set(data.config.scale, data.config.scale, data.config.scale);\n          } catch (error) {\n            console.error('加载车辆模型失败:', error);\n            // 使用备用几何体\n            const geometry = new THREE.BoxGeometry(data.size.length, data.size.height, data.size.width);\n            const material = new THREE.MeshPhongMaterial({\n              color: data.config.color,\n              transparent: true,\n              opacity: 0.8\n            });\n            mesh = new THREE.Mesh(geometry, material);\n          }\n          break;\n        case 'box':\n          // 非机动车使用长方体\n          const geometry = new THREE.BoxGeometry(data.size.length, data.size.height, data.size.width);\n          const material = new THREE.MeshPhongMaterial({\n            color: data.config.color,\n            transparent: true,\n            opacity: 0.8\n          });\n          mesh = new THREE.Mesh(geometry, material);\n          break;\n        case 'cylinder':\n          // 行人使用圆柱体\n          const cylGeometry = new THREE.CylinderGeometry(0.25, 0.25, data.size.height, 8);\n          const cylMaterial = new THREE.MeshPhongMaterial({\n            color: data.config.color,\n            transparent: true,\n            opacity: 0.8\n          });\n          mesh = new THREE.Mesh(cylGeometry, cylMaterial);\n          break;\n      }\n      if (mesh) {\n        mesh.position.copy(data.position);\n        mesh.rotation.y = Math.PI - data.heading * Math.PI / 180;\n        mesh.castShadow = true;\n        mesh.receiveShadow = true;\n      }\n      return mesh;\n    } catch (error) {\n      console.error('创建参与者模型失败:', error);\n      return null;\n    }\n  };\n\n  // 更新交通参与者的 3D 模型\n  const updateParticipantMesh = (mesh, data) => {\n    // 使用 TWEEN 创建平滑过渡\n    new TWEEN.Tween(mesh.position).to(data.position, 100).easing(TWEEN.Easing.Linear.None).start();\n    new TWEEN.Tween(mesh.rotation).to({\n      y: Math.PI - data.heading * Math.PI / 180\n    }, 100).easing(TWEEN.Easing.Linear.None).start();\n  };\n\n  // 动画循环\n  const animate = useCallback(() => {\n    if (controlsRef.current) {\n      controlsRef.current.enabled = cameraMode === 'global';\n    }\n\n    // 更新控制器\n    if (controlsRef.current) {\n      controlsRef.current.update();\n    }\n\n    // 更新渲染\n    if (rendererRef.current && sceneRef.current && cameraRef.current) {\n      rendererRef.current.render(sceneRef.current, cameraRef.current);\n    }\n\n    // 更新补间动画\n    TWEEN.update();\n    animationFrameRef.current = requestAnimationFrame(animate);\n  }, []);\n\n  // 修改初始化场景的代码\n  useEffect(() => {\n    const initScene = async () => {\n      try {\n        console.log('开始初始化场景...');\n\n        // 创建场景\n        const scene = new THREE.Scene();\n        sceneRef.current = scene;\n        scene.background = new THREE.Color(0xf0f0f0);\n\n        // 创建相机\n        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);\n        cameraRef.current = camera;\n        camera.position.set(0, 300, 0);\n\n        // 创建渲染器\n        const renderer = new THREE.WebGLRenderer({\n          antialias: true\n        });\n        rendererRef.current = renderer;\n        renderer.setSize(window.innerWidth, window.innerHeight);\n        renderer.shadowMap.enabled = true;\n        containerRef.current.appendChild(renderer.domElement);\n\n        // 创建控制器\n        const controls = new OrbitControls(camera, renderer.domElement);\n        controlsRef.current = controls;\n        controls.enableDamping = true;\n        controls.dampingFactor = 0.05;\n\n        // 初始化坐标转换器\n        converterRef.current = new CoordinateConverter();\n        await converterRef.current.initialize();\n\n        // 添加环境光和平行光\n        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);\n        scene.add(ambientLight);\n        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);\n        directionalLight.position.set(100, 100, 100);\n        directionalLight.castShadow = true;\n        scene.add(directionalLight);\n\n        // 添加地面\n        const groundGeometry = new THREE.PlaneGeometry(1000, 1000);\n        const groundMaterial = new THREE.MeshStandardMaterial({\n          color: 0xcccccc,\n          roughness: 0.8,\n          metalness: 0.2\n        });\n        const ground = new THREE.Mesh(groundGeometry, groundMaterial);\n        ground.rotation.x = -Math.PI / 2;\n        ground.receiveShadow = true;\n        scene.add(ground);\n\n        // 开始动画循环\n        animate();\n\n        // 设置初始化完成标志\n        console.log('场景初始化完成');\n        setIsInitialized(true);\n\n        // 初始化 WebSocket 连接\n        initMqttClient();\n\n        // 处理缓存的消息\n        if (pendingMessages.length > 0) {\n          console.log('处理缓存的消息:', pendingMessages.length);\n          pendingMessages.forEach(message => {\n            handleRSMMessage(message);\n          });\n          setPendingMessages([]);\n        }\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n    if (containerRef.current) {\n      initScene();\n    }\n\n    // 清理函数\n    return () => {\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n      }\n      if (rendererRef.current) {\n        rendererRef.current.dispose();\n      }\n      if (controlsRef.current) {\n        controlsRef.current.dispose();\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n      }\n    };\n  }, [animate]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 757,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: buttonContainerStyle,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'follow' ? 'white' : 'black'\n        },\n        onClick: switchToFollowView,\n        children: \"\\u8DDF\\u968F\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 759,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        style: {\n          ...buttonStyle,\n          backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n          color: viewMode === 'global' ? 'white' : 'black'\n        },\n        onClick: switchToGlobalView,\n        children: \"\\u5168\\u5C40\\u89C6\\u89D2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 769,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 758,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// 添加创建文字精灵的辅助函数\n_s(CampusModel, \"INCsRZUi2WnpuV0iS5eZxHd8YY4=\");\n_c = CampusModel;\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n\n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n\n  // 绘制文字\n  context.fillText(text, canvas.width / 2, canvas.height / 2);\n\n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({\n    map: texture,\n    transparent: true\n  });\n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {\n      x,\n      y,\n      z\n    });\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n\n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n\n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n\n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\nexport default CampusModel;\nvar _c;\n$RefreshReg$(_c, \"CampusModel\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "THREE", "GLTFLoader", "OrbitControls", "CoordinateConverter", "TWEEN", "mqtt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "globalVehicleRef", "globalTrajectory", "currentPointIndex", "globalUpdateInterval", "targetPosition", "currentPosition", "isMoving", "cameraMode", "controls", "trafficParticipants", "Map", "participant<PERSON><PERSON><PERSON>", "MQTT_CONFIG", "wsUrl", "window", "location", "hostname", "username", "password", "clientId", "Date", "now", "topics", "BASE_URL", "loadVehicleModel", "url", "position", "heading", "Promise", "resolve", "reject", "loader", "load", "gltf", "model", "scene", "traverse", "child", "<PERSON><PERSON><PERSON>", "material", "MeshStandardMaterial", "color", "metalness", "roughness", "envMapIntensity", "copy", "rotation", "y", "Math", "PI", "error", "undefined", "CampusModel", "_s", "containerRef", "sceneRef", "cameraRef", "rendererRef", "controlsRef", "converterRef", "vehicleRef", "converter", "trajectoryRef", "currentPointRef", "mqttClientRef", "isVehicleLoaded", "setIsVehicleLoaded", "isInitialized", "setIsInitialized", "pendingMessages", "setPendingMessages", "animationFrameRef", "vehicleState", "setVehicleState", "longitude", "latitude", "speed", "viewMode", "setViewMode", "buttonContainerStyle", "bottom", "left", "transform", "zIndex", "display", "gap", "buttonStyle", "padding", "backgroundColor", "border", "borderRadius", "cursor", "fontSize", "boxShadow", "transition", "switchToFollowView", "current", "enabled", "switchToGlobalView", "currentPos", "clone", "currentUp", "up", "Tween", "to", "x", "z", "easing", "Easing", "Quadratic", "InOut", "onUpdate", "start", "currentTarget", "target", "lookAt", "update", "minDistance", "maxDistance", "maxPolarAngle", "minPolarAngle", "console", "log", "目标相机位置", "目标控制点", "动画已启动", "initMqttClient", "readyState", "WebSocket", "OPEN", "close", "ws", "connectionTimeout", "setTimeout", "onopen", "clearTimeout", "for<PERSON>ach", "topic", "send", "JSON", "stringify", "type", "onclose", "event", "code", "reason", "<PERSON><PERSON><PERSON>", "document", "visibilityState", "onerror", "message", "onmessage", "data", "warn", "parse", "status", "状态", "时间", "time", "toLocaleString", "主题", "数据类型", "时间戳", "tm", "数据长度", "length", "prev", "handleRSMMessage", "handleBSMMessage", "stack", "bsmData", "newState", "parseFloat", "partLong", "partLat", "partSpeed", "partHeading", "modelPos", "wgs84ToModel", "newPosition", "Vector3", "updateMatrix", "updateMatrixWorld", "Array", "isArray", "participants", "设备ID", "rsuId", "消息来源", "source", "基准点", "经度", "posLong", "纬度", "posLat", "参与者数量", "participant", "updateTrafficParticipant", "getParticipantTypeName", "types", "getParticipantSizeTypeName", "sizeType", "id", "partPtcId", "parseInt", "partPtcType", "partSizeType", "partPosLong", "partPosLat", "participantConfig", "size", "width", "height", "scale", "config", "actualSize", "partLength", "partWidth", "partHeight", "participantData", "lastUpdate", "has", "mesh", "createParticipantMesh", "name", "add", "set", "ID", "类型", "尺寸类型", "实际尺寸", "位置", "速度", "get", "updateParticipantMesh", "错误", "参与者ID", "原始数据", "vehicleModel", "Group", "geometry", "BoxGeometry", "MeshPhongMaterial", "transparent", "opacity", "<PERSON><PERSON>", "cylGeometry", "CylinderGeometry", "cylMaterial", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "Linear", "None", "animate", "render", "requestAnimationFrame", "initScene", "Scene", "background", "Color", "camera", "PerspectiveCamera", "innerWidth", "innerHeight", "renderer", "WebGLRenderer", "antialias", "setSize", "shadowMap", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "enableDamping", "dampingFactor", "initialize", "ambientLight", "AmbientLight", "directionalLight", "DirectionalLight", "groundGeometry", "PlaneGeometry", "groundMaterial", "ground", "cancelAnimationFrame", "dispose", "children", "ref", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "createTextSprite", "text", "canvas", "createElement", "context", "getContext", "font", "fillStyle", "textAlign", "fillText", "texture", "CanvasTexture", "spriteMaterial", "SpriteMaterial", "map", "sprite", "Sprite", "moveVehicle", "forceGlobalView", "querySelector", "parentElement", "__THREE_camera", "oldPos", "__THREE_controls", "旧位置", "toArray", "新位置", "e", "$RefreshReg$"], "sources": ["G:/AI_tools/cursor/projects/education web/src/components/CampusModel.jsx"], "sourcesContent": ["import React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as THREE from 'three';\nimport { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';\nimport { CoordinateConverter } from '../utils/CoordinateConverter';\nimport * as TWEEN from '@tweenjs/tween.js';\nimport mqtt from 'mqtt';\n\n// 全局变量\nlet globalVehicleRef = null;\nlet globalTrajectory = [];\nlet currentPointIndex = 0;\nlet globalUpdateInterval = null;\nlet targetPosition = null;  // 新增：目标位置\nlet currentPosition = null; // 新增：当前位置\nlet isMoving = false;      // 新增：移动状态标志\nlet cameraMode = 'global'; // 'global' 或 'follow'\nlet controls = null; // 保存 controls 的引用\nlet trafficParticipants = new Map();  // 使用 Map 存储所有交通参与者\nlet participantMeshes = new Map();    // 存储交通参与者的 3D 模型\n\n// MQTT配置\nconst MQTT_CONFIG = {\n  wsUrl: `ws://${window.location.hostname}:8083/mqtt`,\n  username: 'cloud',\n  password: 'A825FBCBB97D1975',\n  clientId: `V2X_CENTER_CSLG_web_client_${Date.now()}`, // 添加时间戳避免ID冲突\n  topics: [\n    'changli/cloud/v2x/rsu/rsm',\n    'changli/cloud/v2x/obu/bsm'\n  ]\n};\n\n// 修改所有资源的基础URL\nconst BASE_URL = 'http://localhost:5000';\n\n// 加载车辆模型\nconst loadVehicleModel = (url, position, heading) => {\n  return new Promise((resolve, reject) => {\n    const loader = new GLTFLoader();\n    loader.load(\n      url,\n      (gltf) => {\n        try {\n          const model = gltf.scene;\n          \n          // 调整模型材质\n          model.traverse((child) => {\n            if (child.isMesh) {\n              child.material = new THREE.MeshStandardMaterial({\n                color: 0xffffff,\n                metalness: 0.2,\n                roughness: 0.1,\n                envMapIntensity: 1.0\n              });\n            }\n          });\n          \n          // 设置位置和朝向\n          model.position.copy(position);\n          model.rotation.y = Math.PI - heading * Math.PI / 180;\n          \n          resolve(model);\n        } catch (error) {\n          reject(error);\n        }\n      },\n      undefined,\n      reject\n    );\n  });\n};\n\nconst CampusModel = () => {\n  const containerRef = useRef(null);\n  const sceneRef = useRef(null);\n  const cameraRef = useRef(null);\n  const rendererRef = useRef(null);\n  const controlsRef = useRef(null);\n  const converterRef = useRef(null);\n  const vehicleRef = useRef(null);\n  const converter = useRef(new CoordinateConverter());\n  const trajectoryRef = useRef([]);\n  const currentPointRef = useRef(0);\n  const mqttClientRef = useRef(null);\n  const [isVehicleLoaded, setIsVehicleLoaded] = useState(false);\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [pendingMessages, setPendingMessages] = useState([]);\n  const animationFrameRef = useRef(null);\n  \n  // 添加车辆状态\n  const [vehicleState, setVehicleState] = useState({\n    longitude: 0,\n    latitude: 0,\n    speed: 0,\n    heading: 0\n  });\n\n  // 在 CampusModel 组件中添加状态\n  const [viewMode, setViewMode] = useState('global');\n  \n  // 添加视角切换按钮的样式\n  const buttonContainerStyle = {\n    position: 'fixed',\n    bottom: '20px',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    zIndex: 100,\n    display: 'flex',\n    gap: '10px'\n  };\n\n  const buttonStyle = {\n    padding: '8px 16px',\n    backgroundColor: 'rgba(255, 255, 255, 0.9)',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n    transition: 'all 0.3s ease'\n  };\n\n  // 修改视角切换函数\n  const switchToFollowView = () => {\n    setViewMode('follow');\n    cameraMode = 'follow';\n    \n    if (controlsRef.current) {\n      controlsRef.current.enabled = false;\n    }\n  };\n\n  const switchToGlobalView = () => {\n    setViewMode('global');\n    cameraMode = 'global';\n    \n    if (cameraRef.current && controlsRef.current) {\n      // 获取当前相机位置和朝向\n      const currentPos = cameraRef.current.position.clone();\n      const currentUp = cameraRef.current.up.clone();\n      \n      // 创建相机位置的补间动画\n      new TWEEN.Tween(currentPos)\n        .to({ x: 0, y: 300, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.position.copy(currentPos);\n        })\n        .start();\n\n      // 创建相机上方向的补间动画\n      new TWEEN.Tween(currentUp)\n        .to({ x: 0, y: 1, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          cameraRef.current.up.copy(currentUp);\n        })\n        .start();\n\n      // 获取当前控制器目标点\n      const currentTarget = controlsRef.current.target.clone();\n      \n      // 创建目标点的补间动画\n      new TWEEN.Tween(currentTarget)\n        .to({ x: 0, y: 0, z: 0 }, 1000)\n        .easing(TWEEN.Easing.Quadratic.InOut)\n        .onUpdate(() => {\n          controlsRef.current.target.copy(currentTarget);\n          // 确保相机始终朝向目标点\n          cameraRef.current.lookAt(controlsRef.current.target);\n          controlsRef.current.update();\n        })\n        .start();\n\n      // 启用控制器\n      controlsRef.current.enabled = true;\n      \n      // 重置控制器的一些属性\n      controlsRef.current.minDistance = 50;\n      controlsRef.current.maxDistance = 500;\n      controlsRef.current.maxPolarAngle = Math.PI / 2.1;\n      controlsRef.current.minPolarAngle = 0;\n      controlsRef.current.update();\n      \n      console.log('切换到全局视角', {\n        目标相机位置: [0, 300, 0],\n        目标控制点: [0, 0, 0],\n        动画已启动: true\n      });\n    }\n  };\n\n  // 修改初始化 MQTT 连接\n  const initMqttClient = () => {\n    const wsUrl = MQTT_CONFIG.wsUrl;\n    console.log('正在连接WebSocket...', {\n      url: wsUrl,\n      clientId: MQTT_CONFIG.clientId\n    });\n    \n    try {\n      if (mqttClientRef.current && mqttClientRef.current.readyState === WebSocket.OPEN) {\n        console.log('关闭现有WebSocket连接');\n        mqttClientRef.current.close();\n      }\n\n      const ws = new WebSocket(wsUrl);\n      \n      // 添加连接超时处理\n      const connectionTimeout = setTimeout(() => {\n        if (ws.readyState !== WebSocket.OPEN) {\n          console.error('WebSocket连接超时');\n          ws.close();\n        }\n      }, 5000);\n\n      ws.onopen = () => {\n        clearTimeout(connectionTimeout);\n        console.log('WebSocket连接成功');\n        \n        // 订阅主题\n        MQTT_CONFIG.topics.forEach(topic => {\n          ws.send(JSON.stringify({\n            type: 'subscribe',\n            topic: topic\n          }));\n          console.log('订阅主题:', topic);\n        });\n      };\n\n      ws.onclose = (event) => {\n        clearTimeout(connectionTimeout);\n        console.log('WebSocket连接关闭:', {\n          code: event.code,\n          reason: event.reason,\n          wasClean: event.wasClean\n        });\n        \n        // 只在页面可见时尝试重新连接\n        setTimeout(() => {\n          if (document.visibilityState === 'visible') {\n            console.log('尝试重新连接...');\n            initMqttClient();\n          }\n        }, 5000);\n      };\n\n      ws.onerror = (error) => {\n        console.error('WebSocket错误:', {\n          error: error.message || '未知错误',\n          readyState: ws.readyState,\n          url: wsUrl\n        });\n      };\n\n      // 添加消息处理\n      ws.onmessage = (event) => {\n        try {\n          if (!event.data) {\n            console.warn('收到空消息');\n            return;\n          }\n\n          const message = JSON.parse(event.data);\n          \n          // 处理连接确认消息\n          if (message.type === 'connection' && message.status === 'connected') {\n            console.log('WebSocket连接已确认:', {\n              状态: message.status,\n              时间: new Date(message.time).toLocaleString()\n            });\n            return;\n          }\n\n          // 处理普通消息\n          if (!message.topic || !message.message) {\n            return; // 忽略非数据消息\n          }\n\n          let data;\n          try {\n            data = JSON.parse(message.message);\n          } catch (error) {\n            console.error('解析消息内容失败:', {\n              error: error.message,\n              message: message.message\n            });\n            return;\n          }\n\n          console.log('收到消息:', {\n            主题: message.topic,\n            数据类型: data.type,\n            时间戳: data.tm,\n            数据长度: message.message.length\n          });\n\n          // 如果场景未初始化，将消息存入缓存\n          if (!isInitialized) {\n            if (message.topic === 'changli/cloud/v2x/rsu/rsm') {\n              console.log('场景未初始化，缓存RSM消息');\n              setPendingMessages(prev => [...prev, data]);\n            }\n            return;\n          }\n\n          // 场景已初始化，正常处理消息\n          switch(message.topic) {\n            case 'changli/cloud/v2x/rsu/rsm':\n              handleRSMMessage(data);\n              break;\n            case 'changli/cloud/v2x/obu/bsm':\n              handleBSMMessage(data);\n              break;\n            default:\n              console.warn('未知的消息主题:', message.topic);\n          }\n        } catch (error) {\n          console.error('处理WebSocket消息失败:', error);\n        }\n      };\n\n      mqttClientRef.current = ws;\n\n    } catch (error) {\n      console.error('创建WebSocket连接失败:', {\n        error: error.message,\n        stack: error.stack,\n        wsUrl\n      });\n    }\n  };\n\n  // 处理 BSM 消息\n  const handleBSMMessage = (data) => {\n    if (data.type === 'BSM' && data.data) {\n      const bsmData = data.data;\n      const newState = {\n        longitude: parseFloat(bsmData.partLong),\n        latitude: parseFloat(bsmData.partLat),\n        speed: parseFloat(bsmData.partSpeed),\n        heading: parseFloat(bsmData.partHeading)\n      };\n      \n      console.log('解析后的车辆状态:', newState);\n      \n      // 更新车辆位置和状态\n      if (globalVehicleRef) {\n        const modelPos = converter.current.wgs84ToModel(newState.longitude, newState.latitude);\n        const newPosition = new THREE.Vector3(modelPos.x, 0.5, -modelPos.y);\n        \n        globalVehicleRef.position.copy(newPosition);\n        globalVehicleRef.rotation.y = Math.PI - newState.heading * Math.PI / 180;\n        globalVehicleRef.updateMatrix();\n        globalVehicleRef.updateMatrixWorld(true);\n        \n        setVehicleState(newState);\n        console.log('车辆位置已更新:', newPosition);\n      }\n    }\n  };\n\n  // 处理 RSM 消息\n  const handleRSMMessage = (data) => {\n    try {\n      // 验证消息格式\n      if (!data || !data.data || !Array.isArray(data.data.participants)) {\n        console.warn('无效的RSM消息格式:', data);\n        return;\n      }\n\n      // 如果场景未初始化，缓存消息\n      if (!isInitialized) {\n        console.log('场景未初始化，缓存RSM消息');\n        setPendingMessages(prev => [...prev, data]);\n        return;\n      }\n\n      console.log('处理RSM消息:', {\n        设备ID: data.data.rsuId,\n        时间戳: data.tm,\n        消息来源: data.source,\n        基准点: {\n          经度: data.data.posLong,\n          纬度: data.data.posLat\n        },\n        参与者数量: data.data.participants.length\n      });\n\n      // 处理每个参与者\n      data.data.participants.forEach(participant => {\n        try {\n          updateTrafficParticipant(participant);\n        } catch (error) {\n          console.error('处理参与者失败:', error);\n        }\n      });\n\n    } catch (error) {\n      console.error('处理RSM消息失败:', error);\n    }\n  };\n\n  // 获取参与者类型名称\n  const getParticipantTypeName = (type) => {\n    const types = {\n      '0': '未知',\n      '1': '机动车',\n      '2': '非机动车',\n      '3': '行人'\n    };\n    return types[type] || '未知';\n  };\n\n  // 获取参与者尺寸类型名称\n  const getParticipantSizeTypeName = (sizeType) => {\n    const types = {\n      '1': '小型车(长度<600cm)',\n      '2': '中型车(600cm≤长度<1300cm)',\n      '3': '大型车(长度≥1300cm)'\n    };\n    return types[sizeType] || '未知';\n  };\n\n  // 更新交通参与者\n  const updateTrafficParticipant = async (participant) => {\n    if (!sceneRef.current || !converterRef.current) {\n      console.warn('场景或坐标转换器未初始化');\n      return;\n    }\n\n    const id = participant.partPtcId;\n    const type = parseInt(participant.partPtcType);\n    const sizeType = parseInt(participant.partSizeType || '1');\n\n    try {\n      // 转换坐标\n      const modelPos = converterRef.current.wgs84ToModel(\n        parseFloat(participant.partPosLong),\n        parseFloat(participant.partPosLat)\n      );\n\n      // 设置不同类型参与者的默认尺寸和颜色\n      const participantConfig = {\n        1: { // 机动车\n          model: 'vehicle',\n          size: {\n            // 根据 partSizeType 设置不同尺寸\n            1: { length: 4.5, width: 1.8, height: 1.5 },  // 小车\n            2: { length: 8.0, width: 2.3, height: 2.5 },  // 中型车\n            3: { length: 12.0, width: 2.5, height: 3.0 }  // 大型车\n          },\n          color: 0x4444ff,\n          scale: 0.7\n        },\n        2: { // 非机动车\n          model: 'box',\n          size: { length: 1.8, width: 0.6, height: 1.2 },\n          color: 0x44ff44\n        },\n        3: { // 行人\n          model: 'cylinder',\n          size: { length: 0.5, width: 0.5, height: 1.7 },\n          color: 0xff4444\n        }\n      };\n\n      const config = participantConfig[type] || participantConfig[1];\n      // 获取实际尺寸（单位转换：cm -> m）\n      const actualSize = {\n        length: parseFloat(participant.partLength || '0') / 100,\n        width: parseFloat(participant.partWidth || '0') / 100,\n        height: parseFloat(participant.partHeight || '0') / 100\n      };\n\n      // 使用实际尺寸或默认尺寸\n      const size = {\n        length: actualSize.length > 0 ? actualSize.length : \n                (type === 1 ? config.size[sizeType].length : config.size.length),\n        width: actualSize.width > 0 ? actualSize.width :\n               (type === 1 ? config.size[sizeType].width : config.size.width),\n        height: actualSize.height > 0 ? actualSize.height :\n                (type === 1 ? config.size[sizeType].height : config.size.height)\n      };\n\n      // 创建或更新参与者数据\n      const participantData = {\n        position: new THREE.Vector3(modelPos.x, 0.5, -modelPos.y),\n        heading: parseFloat(participant.partHeading || 0),\n        speed: parseFloat(participant.partSpeed || 0),\n        type: type,\n        sizeType: sizeType,\n        config: config,\n        size: size,\n        lastUpdate: Date.now()\n      };\n\n      // 更新或创建3D模型\n      if (!participantMeshes.has(id)) {\n        const mesh = await createParticipantMesh(participantData);\n        if (mesh) {\n          mesh.name = `participant_${id}_${type}_${sizeType}`;\n          sceneRef.current.add(mesh);\n          participantMeshes.set(id, mesh);\n          console.log('创建新参与者:', {\n            ID: id,\n            类型: type,\n            尺寸类型: sizeType,\n            实际尺寸: size,\n            位置: modelPos,\n            速度: participantData.speed\n          });\n        }\n      } else {\n        const mesh = participantMeshes.get(id);\n        updateParticipantMesh(mesh, participantData);\n      }\n\n      trafficParticipants.set(id, participantData);\n    } catch (error) {\n      console.error('更新交通参与者失败:', {\n        错误: error.message,\n        参与者ID: id,\n        类型: type,\n        尺寸类型: sizeType,\n        原始数据: participant\n      });\n    }\n  };\n\n  // 创建交通参与者的3D模型\n  const createParticipantMesh = async (data) => {\n    let mesh;\n\n    try {\n      switch (data.config.model) {\n        case 'vehicle':\n          try {\n            // 使用现有的车辆模型\n            const vehicleModel = await loadVehicleModel(\n              `${BASE_URL}/changli2/Audi R8.glb`,\n              data.position,\n              data.heading\n            );\n            mesh = new THREE.Group();\n            mesh.add(vehicleModel);\n            vehicleModel.scale.set(\n              data.config.scale,\n              data.config.scale,\n              data.config.scale\n            );\n          } catch (error) {\n            console.error('加载车辆模型失败:', error);\n            // 使用备用几何体\n            const geometry = new THREE.BoxGeometry(\n              data.size.length,\n              data.size.height,\n              data.size.width\n            );\n            const material = new THREE.MeshPhongMaterial({\n              color: data.config.color,\n              transparent: true,\n              opacity: 0.8\n            });\n            mesh = new THREE.Mesh(geometry, material);\n          }\n          break;\n\n        case 'box':\n          // 非机动车使用长方体\n          const geometry = new THREE.BoxGeometry(\n            data.size.length,\n            data.size.height,\n            data.size.width\n          );\n          const material = new THREE.MeshPhongMaterial({\n            color: data.config.color,\n            transparent: true,\n            opacity: 0.8\n          });\n          mesh = new THREE.Mesh(geometry, material);\n          break;\n\n        case 'cylinder':\n          // 行人使用圆柱体\n          const cylGeometry = new THREE.CylinderGeometry(\n            0.25, 0.25,\n            data.size.height,\n            8\n          );\n          const cylMaterial = new THREE.MeshPhongMaterial({\n            color: data.config.color,\n            transparent: true,\n            opacity: 0.8\n          });\n          mesh = new THREE.Mesh(cylGeometry, cylMaterial);\n          break;\n      }\n\n      if (mesh) {\n        mesh.position.copy(data.position);\n        mesh.rotation.y = Math.PI - data.heading * Math.PI / 180;\n        mesh.castShadow = true;\n        mesh.receiveShadow = true;\n      }\n\n      return mesh;\n    } catch (error) {\n      console.error('创建参与者模型失败:', error);\n      return null;\n    }\n  };\n\n  // 更新交通参与者的 3D 模型\n  const updateParticipantMesh = (mesh, data) => {\n    // 使用 TWEEN 创建平滑过渡\n    new TWEEN.Tween(mesh.position)\n      .to(data.position, 100)\n      .easing(TWEEN.Easing.Linear.None)\n      .start();\n    \n    new TWEEN.Tween(mesh.rotation)\n      .to({ y: Math.PI - data.heading * Math.PI / 180 }, 100)\n      .easing(TWEEN.Easing.Linear.None)\n      .start();\n  };\n\n  // 动画循环\n  const animate = useCallback(() => {\n    if (controlsRef.current) {\n      controlsRef.current.enabled = cameraMode === 'global';\n    }\n\n    // 更新控制器\n    if (controlsRef.current) {\n      controlsRef.current.update();\n    }\n\n    // 更新渲染\n    if (rendererRef.current && sceneRef.current && cameraRef.current) {\n      rendererRef.current.render(sceneRef.current, cameraRef.current);\n    }\n\n    // 更新补间动画\n    TWEEN.update();\n\n    animationFrameRef.current = requestAnimationFrame(animate);\n  }, []);\n\n  // 修改初始化场景的代码\n  useEffect(() => {\n    const initScene = async () => {\n      try {\n        console.log('开始初始化场景...');\n        \n        // 创建场景\n        const scene = new THREE.Scene();\n        sceneRef.current = scene;\n        scene.background = new THREE.Color(0xf0f0f0);\n\n        // 创建相机\n        const camera = new THREE.PerspectiveCamera(\n          75,\n          window.innerWidth / window.innerHeight,\n          0.1,\n          1000\n        );\n        cameraRef.current = camera;\n        camera.position.set(0, 300, 0);\n\n        // 创建渲染器\n        const renderer = new THREE.WebGLRenderer({ antialias: true });\n        rendererRef.current = renderer;\n        renderer.setSize(window.innerWidth, window.innerHeight);\n        renderer.shadowMap.enabled = true;\n        containerRef.current.appendChild(renderer.domElement);\n\n        // 创建控制器\n        const controls = new OrbitControls(camera, renderer.domElement);\n        controlsRef.current = controls;\n        controls.enableDamping = true;\n        controls.dampingFactor = 0.05;\n\n        // 初始化坐标转换器\n        converterRef.current = new CoordinateConverter();\n        await converterRef.current.initialize();\n\n        // 添加环境光和平行光\n        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);\n        scene.add(ambientLight);\n\n        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);\n        directionalLight.position.set(100, 100, 100);\n        directionalLight.castShadow = true;\n        scene.add(directionalLight);\n\n        // 添加地面\n        const groundGeometry = new THREE.PlaneGeometry(1000, 1000);\n        const groundMaterial = new THREE.MeshStandardMaterial({ \n          color: 0xcccccc,\n          roughness: 0.8,\n          metalness: 0.2\n        });\n        const ground = new THREE.Mesh(groundGeometry, groundMaterial);\n        ground.rotation.x = -Math.PI / 2;\n        ground.receiveShadow = true;\n        scene.add(ground);\n\n        // 开始动画循环\n        animate();\n\n        // 设置初始化完成标志\n        console.log('场景初始化完成');\n        setIsInitialized(true);\n\n        // 初始化 WebSocket 连接\n        initMqttClient();\n\n        // 处理缓存的消息\n        if (pendingMessages.length > 0) {\n          console.log('处理缓存的消息:', pendingMessages.length);\n          pendingMessages.forEach(message => {\n            handleRSMMessage(message);\n          });\n          setPendingMessages([]);\n        }\n\n      } catch (error) {\n        console.error('初始化场景失败:', error);\n      }\n    };\n\n    if (containerRef.current) {\n      initScene();\n    }\n\n    // 清理函数\n    return () => {\n      if (animationFrameRef.current) {\n        cancelAnimationFrame(animationFrameRef.current);\n      }\n      if (rendererRef.current) {\n        rendererRef.current.dispose();\n      }\n      if (controlsRef.current) {\n        controlsRef.current.dispose();\n      }\n      if (mqttClientRef.current) {\n        mqttClientRef.current.close();\n      }\n    };\n  }, [animate]);\n\n  return (\n    <>\n      <div ref={containerRef} style={{ width: '100%', height: '100%' }} />\n      <div style={buttonContainerStyle}>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'follow' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'follow' ? 'white' : 'black'\n          }}\n          onClick={switchToFollowView}\n        >\n          跟随视角\n        </button>\n        <button \n          style={{\n            ...buttonStyle,\n            backgroundColor: viewMode === 'global' ? '#1890ff' : 'rgba(255, 255, 255, 0.9)',\n            color: viewMode === 'global' ? 'white' : 'black'\n          }}\n          onClick={switchToGlobalView}\n        >\n          全局视角\n        </button>\n      </div>\n    </>\n  );\n};\n\n// 添加创建文字精灵的辅助函数\nfunction createTextSprite(text) {\n  const canvas = document.createElement('canvas');\n  const context = canvas.getContext('2d');\n  canvas.width = 256;\n  canvas.height = 64;\n  \n  // 设置文字样式\n  context.font = 'Bold 24px Arial';\n  context.fillStyle = 'rgba(255,255,255,1)';\n  context.textAlign = 'center';\n  \n  // 绘制文字\n  context.fillText(text, canvas.width/2, canvas.height/2);\n  \n  // 创建纹理\n  const texture = new THREE.CanvasTexture(canvas);\n  const spriteMaterial = new THREE.SpriteMaterial({ \n    map: texture,\n    transparent: true\n  });\n  \n  const sprite = new THREE.Sprite(spriteMaterial);\n  sprite.scale.set(10, 2.5, 1);\n  \n  return sprite;\n}\n\n// 添加一个全局函数用于手动更新位置（用于调试）\nwindow.moveVehicle = (x, y, z) => {\n  if (globalVehicleRef) {\n    globalVehicleRef.position.set(x, y, z);\n    globalVehicleRef.updateMatrix();\n    globalVehicleRef.updateMatrixWorld(true);\n    console.log('手动移动车辆到:', {x, y, z});\n    return true;\n  }\n  return false;\n};\n\n// 在文件底部添加这个全局函数\nwindow.forceGlobalView = () => {\n  try {\n    // 获取当前场景中的相机\n    const camera = document.querySelector('canvas').parentElement.__THREE_camera;\n    if (camera) {\n      // 保存旧位置\n      const oldPos = camera.position.clone();\n      \n      // 设置新位置\n      camera.position.set(0, 300, 0);\n      camera.up.set(0, 1, 0);\n      camera.lookAt(0, 0, 0);\n      \n      // 更新矩阵\n      camera.updateMatrix();\n      camera.updateMatrixWorld(true);\n      \n      // 更新控制器\n      const controls = document.querySelector('canvas').parentElement.__THREE_controls;\n      if (controls) {\n        controls.target.set(0, 0, 0);\n        controls.update();\n      }\n      \n      console.log('强制设置全局视角成功', {\n        旧位置: oldPos.toArray(),\n        新位置: camera.position.toArray()\n      });\n      \n      return true;\n    }\n    return false;\n  } catch (e) {\n    console.error('强制设置全局视角失败', e);\n    return false;\n  }\n};\n\nexport default CampusModel; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,uCAAuC;AAClE,SAASC,aAAa,QAAQ,2CAA2C;AACzE,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,IAAIC,cAAc,GAAG,IAAI,CAAC,CAAE;AAC5B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;AAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAM;AAC3B,IAAIC,UAAU,GAAG,QAAQ,CAAC,CAAC;AAC3B,IAAIC,QAAQ,GAAG,IAAI,CAAC,CAAC;AACrB,IAAIC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAE;AACtC,IAAIC,iBAAiB,GAAG,IAAID,GAAG,CAAC,CAAC,CAAC,CAAI;;AAEtC;AACA,MAAME,WAAW,GAAG;EAClBC,KAAK,EAAE,QAAQC,MAAM,CAACC,QAAQ,CAACC,QAAQ,YAAY;EACnDC,QAAQ,EAAE,OAAO;EACjBC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE,8BAA8BC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;EAAE;EACtDC,MAAM,EAAE,CACN,2BAA2B,EAC3B,2BAA2B;AAE/B,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG,uBAAuB;;AAExC;AACA,MAAMC,gBAAgB,GAAGA,CAACC,GAAG,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EACnD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMC,MAAM,GAAG,IAAIxC,UAAU,CAAC,CAAC;IAC/BwC,MAAM,CAACC,IAAI,CACTP,GAAG,EACFQ,IAAI,IAAK;MACR,IAAI;QACF,MAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK;;QAExB;QACAD,KAAK,CAACE,QAAQ,CAAEC,KAAK,IAAK;UACxB,IAAIA,KAAK,CAACC,MAAM,EAAE;YAChBD,KAAK,CAACE,QAAQ,GAAG,IAAIjD,KAAK,CAACkD,oBAAoB,CAAC;cAC9CC,KAAK,EAAE,QAAQ;cACfC,SAAS,EAAE,GAAG;cACdC,SAAS,EAAE,GAAG;cACdC,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;;QAEF;QACAV,KAAK,CAACR,QAAQ,CAACmB,IAAI,CAACnB,QAAQ,CAAC;QAC7BQ,KAAK,CAACY,QAAQ,CAACC,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAGtB,OAAO,GAAGqB,IAAI,CAACC,EAAE,GAAG,GAAG;QAEpDpB,OAAO,CAACK,KAAK,CAAC;MAChB,CAAC,CAAC,OAAOgB,KAAK,EAAE;QACdpB,MAAM,CAACoB,KAAK,CAAC;MACf;IACF,CAAC,EACDC,SAAS,EACTrB,MACF,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AAED,MAAMsB,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,YAAY,GAAGnE,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMoE,QAAQ,GAAGpE,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMqE,SAAS,GAAGrE,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMsE,WAAW,GAAGtE,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMuE,WAAW,GAAGvE,MAAM,CAAC,IAAI,CAAC;EAChC,MAAMwE,YAAY,GAAGxE,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMyE,UAAU,GAAGzE,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAM0E,SAAS,GAAG1E,MAAM,CAAC,IAAIM,mBAAmB,CAAC,CAAC,CAAC;EACnD,MAAMqE,aAAa,GAAG3E,MAAM,CAAC,EAAE,CAAC;EAChC,MAAM4E,eAAe,GAAG5E,MAAM,CAAC,CAAC,CAAC;EACjC,MAAM6E,aAAa,GAAG7E,MAAM,CAAC,IAAI,CAAC;EAClC,MAAM,CAAC8E,eAAe,EAAEC,kBAAkB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiF,eAAe,EAAEC,kBAAkB,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAMmF,iBAAiB,GAAGpF,MAAM,CAAC,IAAI,CAAC;;EAEtC;EACA,MAAM,CAACqF,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAC;IAC/CsF,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,KAAK,EAAE,CAAC;IACRjD,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAG1F,QAAQ,CAAC,QAAQ,CAAC;;EAElD;EACA,MAAM2F,oBAAoB,GAAG;IAC3BrD,QAAQ,EAAE,OAAO;IACjBsD,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAE,kBAAkB;IAC7BC,MAAM,EAAE,GAAG;IACXC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE;EACP,CAAC;EAED,MAAMC,WAAW,GAAG;IAClBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,0BAA0B;IAC3CC,MAAM,EAAE,gBAAgB;IACxBC,YAAY,EAAE,KAAK;IACnBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,2BAA2B;IACtCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BjB,WAAW,CAAC,QAAQ,CAAC;IACrBvE,UAAU,GAAG,QAAQ;IAErB,IAAImD,WAAW,CAACsC,OAAO,EAAE;MACvBtC,WAAW,CAACsC,OAAO,CAACC,OAAO,GAAG,KAAK;IACrC;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BpB,WAAW,CAAC,QAAQ,CAAC;IACrBvE,UAAU,GAAG,QAAQ;IAErB,IAAIiD,SAAS,CAACwC,OAAO,IAAItC,WAAW,CAACsC,OAAO,EAAE;MAC5C;MACA,MAAMG,UAAU,GAAG3C,SAAS,CAACwC,OAAO,CAACtE,QAAQ,CAAC0E,KAAK,CAAC,CAAC;MACrD,MAAMC,SAAS,GAAG7C,SAAS,CAACwC,OAAO,CAACM,EAAE,CAACF,KAAK,CAAC,CAAC;;MAE9C;MACA,IAAI1G,KAAK,CAAC6G,KAAK,CAACJ,UAAU,CAAC,CACxBK,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAE1D,CAAC,EAAE,GAAG;QAAE2D,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAChCC,MAAM,CAACjH,KAAK,CAACkH,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvD,SAAS,CAACwC,OAAO,CAACtE,QAAQ,CAACmB,IAAI,CAACsD,UAAU,CAAC;MAC7C,CAAC,CAAC,CACDa,KAAK,CAAC,CAAC;;MAEV;MACA,IAAItH,KAAK,CAAC6G,KAAK,CAACF,SAAS,CAAC,CACvBG,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAE1D,CAAC,EAAE,CAAC;QAAE2D,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAACjH,KAAK,CAACkH,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdvD,SAAS,CAACwC,OAAO,CAACM,EAAE,CAACzD,IAAI,CAACwD,SAAS,CAAC;MACtC,CAAC,CAAC,CACDW,KAAK,CAAC,CAAC;;MAEV;MACA,MAAMC,aAAa,GAAGvD,WAAW,CAACsC,OAAO,CAACkB,MAAM,CAACd,KAAK,CAAC,CAAC;;MAExD;MACA,IAAI1G,KAAK,CAAC6G,KAAK,CAACU,aAAa,CAAC,CAC3BT,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAE1D,CAAC,EAAE,CAAC;QAAE2D,CAAC,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC,CAC9BC,MAAM,CAACjH,KAAK,CAACkH,MAAM,CAACC,SAAS,CAACC,KAAK,CAAC,CACpCC,QAAQ,CAAC,MAAM;QACdrD,WAAW,CAACsC,OAAO,CAACkB,MAAM,CAACrE,IAAI,CAACoE,aAAa,CAAC;QAC9C;QACAzD,SAAS,CAACwC,OAAO,CAACmB,MAAM,CAACzD,WAAW,CAACsC,OAAO,CAACkB,MAAM,CAAC;QACpDxD,WAAW,CAACsC,OAAO,CAACoB,MAAM,CAAC,CAAC;MAC9B,CAAC,CAAC,CACDJ,KAAK,CAAC,CAAC;;MAEV;MACAtD,WAAW,CAACsC,OAAO,CAACC,OAAO,GAAG,IAAI;;MAElC;MACAvC,WAAW,CAACsC,OAAO,CAACqB,WAAW,GAAG,EAAE;MACpC3D,WAAW,CAACsC,OAAO,CAACsB,WAAW,GAAG,GAAG;MACrC5D,WAAW,CAACsC,OAAO,CAACuB,aAAa,GAAGvE,IAAI,CAACC,EAAE,GAAG,GAAG;MACjDS,WAAW,CAACsC,OAAO,CAACwB,aAAa,GAAG,CAAC;MACrC9D,WAAW,CAACsC,OAAO,CAACoB,MAAM,CAAC,CAAC;MAE5BK,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;QACrBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMjH,KAAK,GAAGD,WAAW,CAACC,KAAK;IAC/B4G,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;MAC9BjG,GAAG,EAAEZ,KAAK;MACVM,QAAQ,EAAEP,WAAW,CAACO;IACxB,CAAC,CAAC;IAEF,IAAI;MACF,IAAI6C,aAAa,CAACgC,OAAO,IAAIhC,aAAa,CAACgC,OAAO,CAAC+B,UAAU,KAAKC,SAAS,CAACC,IAAI,EAAE;QAChFR,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;QAC9B1D,aAAa,CAACgC,OAAO,CAACkC,KAAK,CAAC,CAAC;MAC/B;MAEA,MAAMC,EAAE,GAAG,IAAIH,SAAS,CAACnH,KAAK,CAAC;;MAE/B;MACA,MAAMuH,iBAAiB,GAAGC,UAAU,CAAC,MAAM;QACzC,IAAIF,EAAE,CAACJ,UAAU,KAAKC,SAAS,CAACC,IAAI,EAAE;UACpCR,OAAO,CAACvE,KAAK,CAAC,eAAe,CAAC;UAC9BiF,EAAE,CAACD,KAAK,CAAC,CAAC;QACZ;MACF,CAAC,EAAE,IAAI,CAAC;MAERC,EAAE,CAACG,MAAM,GAAG,MAAM;QAChBC,YAAY,CAACH,iBAAiB,CAAC;QAC/BX,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;;QAE5B;QACA9G,WAAW,CAACU,MAAM,CAACkH,OAAO,CAACC,KAAK,IAAI;UAClCN,EAAE,CAACO,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;YACrBC,IAAI,EAAE,WAAW;YACjBJ,KAAK,EAAEA;UACT,CAAC,CAAC,CAAC;UACHhB,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEe,KAAK,CAAC;QAC7B,CAAC,CAAC;MACJ,CAAC;MAEDN,EAAE,CAACW,OAAO,GAAIC,KAAK,IAAK;QACtBR,YAAY,CAACH,iBAAiB,CAAC;QAC/BX,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;UAC5BsB,IAAI,EAAED,KAAK,CAACC,IAAI;UAChBC,MAAM,EAAEF,KAAK,CAACE,MAAM;UACpBC,QAAQ,EAAEH,KAAK,CAACG;QAClB,CAAC,CAAC;;QAEF;QACAb,UAAU,CAAC,MAAM;UACf,IAAIc,QAAQ,CAACC,eAAe,KAAK,SAAS,EAAE;YAC1C3B,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;YACxBI,cAAc,CAAC,CAAC;UAClB;QACF,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MAEDK,EAAE,CAACkB,OAAO,GAAInG,KAAK,IAAK;QACtBuE,OAAO,CAACvE,KAAK,CAAC,cAAc,EAAE;UAC5BA,KAAK,EAAEA,KAAK,CAACoG,OAAO,IAAI,MAAM;UAC9BvB,UAAU,EAAEI,EAAE,CAACJ,UAAU;UACzBtG,GAAG,EAAEZ;QACP,CAAC,CAAC;MACJ,CAAC;;MAED;MACAsH,EAAE,CAACoB,SAAS,GAAIR,KAAK,IAAK;QACxB,IAAI;UACF,IAAI,CAACA,KAAK,CAACS,IAAI,EAAE;YACf/B,OAAO,CAACgC,IAAI,CAAC,OAAO,CAAC;YACrB;UACF;UAEA,MAAMH,OAAO,GAAGX,IAAI,CAACe,KAAK,CAACX,KAAK,CAACS,IAAI,CAAC;;UAEtC;UACA,IAAIF,OAAO,CAACT,IAAI,KAAK,YAAY,IAAIS,OAAO,CAACK,MAAM,KAAK,WAAW,EAAE;YACnElC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;cAC7BkC,EAAE,EAAEN,OAAO,CAACK,MAAM;cAClBE,EAAE,EAAE,IAAIzI,IAAI,CAACkI,OAAO,CAACQ,IAAI,CAAC,CAACC,cAAc,CAAC;YAC5C,CAAC,CAAC;YACF;UACF;;UAEA;UACA,IAAI,CAACT,OAAO,CAACb,KAAK,IAAI,CAACa,OAAO,CAACA,OAAO,EAAE;YACtC,OAAO,CAAC;UACV;UAEA,IAAIE,IAAI;UACR,IAAI;YACFA,IAAI,GAAGb,IAAI,CAACe,KAAK,CAACJ,OAAO,CAACA,OAAO,CAAC;UACpC,CAAC,CAAC,OAAOpG,KAAK,EAAE;YACduE,OAAO,CAACvE,KAAK,CAAC,WAAW,EAAE;cACzBA,KAAK,EAAEA,KAAK,CAACoG,OAAO;cACpBA,OAAO,EAAEA,OAAO,CAACA;YACnB,CAAC,CAAC;YACF;UACF;UAEA7B,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;YACnBsC,EAAE,EAAEV,OAAO,CAACb,KAAK;YACjBwB,IAAI,EAAET,IAAI,CAACX,IAAI;YACfqB,GAAG,EAAEV,IAAI,CAACW,EAAE;YACZC,IAAI,EAAEd,OAAO,CAACA,OAAO,CAACe;UACxB,CAAC,CAAC;;UAEF;UACA,IAAI,CAAClG,aAAa,EAAE;YAClB,IAAImF,OAAO,CAACb,KAAK,KAAK,2BAA2B,EAAE;cACjDhB,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;cAC7BpD,kBAAkB,CAACgG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEd,IAAI,CAAC,CAAC;YAC7C;YACA;UACF;;UAEA;UACA,QAAOF,OAAO,CAACb,KAAK;YAClB,KAAK,2BAA2B;cAC9B8B,gBAAgB,CAACf,IAAI,CAAC;cACtB;YACF,KAAK,2BAA2B;cAC9BgB,gBAAgB,CAAChB,IAAI,CAAC;cACtB;YACF;cACE/B,OAAO,CAACgC,IAAI,CAAC,UAAU,EAAEH,OAAO,CAACb,KAAK,CAAC;UAC3C;QACF,CAAC,CAAC,OAAOvF,KAAK,EAAE;UACduE,OAAO,CAACvE,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;QAC1C;MACF,CAAC;MAEDc,aAAa,CAACgC,OAAO,GAAGmC,EAAE;IAE5B,CAAC,CAAC,OAAOjF,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,kBAAkB,EAAE;QAChCA,KAAK,EAAEA,KAAK,CAACoG,OAAO;QACpBmB,KAAK,EAAEvH,KAAK,CAACuH,KAAK;QAClB5J;MACF,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAM2J,gBAAgB,GAAIhB,IAAI,IAAK;IACjC,IAAIA,IAAI,CAACX,IAAI,KAAK,KAAK,IAAIW,IAAI,CAACA,IAAI,EAAE;MACpC,MAAMkB,OAAO,GAAGlB,IAAI,CAACA,IAAI;MACzB,MAAMmB,QAAQ,GAAG;QACfjG,SAAS,EAAEkG,UAAU,CAACF,OAAO,CAACG,QAAQ,CAAC;QACvClG,QAAQ,EAAEiG,UAAU,CAACF,OAAO,CAACI,OAAO,CAAC;QACrClG,KAAK,EAAEgG,UAAU,CAACF,OAAO,CAACK,SAAS,CAAC;QACpCpJ,OAAO,EAAEiJ,UAAU,CAACF,OAAO,CAACM,WAAW;MACzC,CAAC;MAEDvD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEiD,QAAQ,CAAC;;MAElC;MACA,IAAI3K,gBAAgB,EAAE;QACpB,MAAMiL,QAAQ,GAAGpH,SAAS,CAACmC,OAAO,CAACkF,YAAY,CAACP,QAAQ,CAACjG,SAAS,EAAEiG,QAAQ,CAAChG,QAAQ,CAAC;QACtF,MAAMwG,WAAW,GAAG,IAAI7L,KAAK,CAAC8L,OAAO,CAACH,QAAQ,CAACxE,CAAC,EAAE,GAAG,EAAE,CAACwE,QAAQ,CAAClI,CAAC,CAAC;QAEnE/C,gBAAgB,CAAC0B,QAAQ,CAACmB,IAAI,CAACsI,WAAW,CAAC;QAC3CnL,gBAAgB,CAAC8C,QAAQ,CAACC,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAG0H,QAAQ,CAAChJ,OAAO,GAAGqB,IAAI,CAACC,EAAE,GAAG,GAAG;QACxEjD,gBAAgB,CAACqL,YAAY,CAAC,CAAC;QAC/BrL,gBAAgB,CAACsL,iBAAiB,CAAC,IAAI,CAAC;QAExC7G,eAAe,CAACkG,QAAQ,CAAC;QACzBlD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEyD,WAAW,CAAC;MACtC;IACF;EACF,CAAC;;EAED;EACA,MAAMZ,gBAAgB,GAAIf,IAAI,IAAK;IACjC,IAAI;MACF;MACA,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACA,IAAI,IAAI,CAAC+B,KAAK,CAACC,OAAO,CAAChC,IAAI,CAACA,IAAI,CAACiC,YAAY,CAAC,EAAE;QACjEhE,OAAO,CAACgC,IAAI,CAAC,aAAa,EAAED,IAAI,CAAC;QACjC;MACF;;MAEA;MACA,IAAI,CAACrF,aAAa,EAAE;QAClBsD,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;QAC7BpD,kBAAkB,CAACgG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEd,IAAI,CAAC,CAAC;QAC3C;MACF;MAEA/B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;QACtBgE,IAAI,EAAElC,IAAI,CAACA,IAAI,CAACmC,KAAK;QACrBzB,GAAG,EAAEV,IAAI,CAACW,EAAE;QACZyB,IAAI,EAAEpC,IAAI,CAACqC,MAAM;QACjBC,GAAG,EAAE;UACHC,EAAE,EAAEvC,IAAI,CAACA,IAAI,CAACwC,OAAO;UACrBC,EAAE,EAAEzC,IAAI,CAACA,IAAI,CAAC0C;QAChB,CAAC;QACDC,KAAK,EAAE3C,IAAI,CAACA,IAAI,CAACiC,YAAY,CAACpB;MAChC,CAAC,CAAC;;MAEF;MACAb,IAAI,CAACA,IAAI,CAACiC,YAAY,CAACjD,OAAO,CAAC4D,WAAW,IAAI;QAC5C,IAAI;UACFC,wBAAwB,CAACD,WAAW,CAAC;QACvC,CAAC,CAAC,OAAOlJ,KAAK,EAAE;UACduE,OAAO,CAACvE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;QAClC;MACF,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMoJ,sBAAsB,GAAIzD,IAAI,IAAK;IACvC,MAAM0D,KAAK,GAAG;MACZ,GAAG,EAAE,IAAI;MACT,GAAG,EAAE,KAAK;MACV,GAAG,EAAE,MAAM;MACX,GAAG,EAAE;IACP,CAAC;IACD,OAAOA,KAAK,CAAC1D,IAAI,CAAC,IAAI,IAAI;EAC5B,CAAC;;EAED;EACA,MAAM2D,0BAA0B,GAAIC,QAAQ,IAAK;IAC/C,MAAMF,KAAK,GAAG;MACZ,GAAG,EAAE,eAAe;MACpB,GAAG,EAAE,sBAAsB;MAC3B,GAAG,EAAE;IACP,CAAC;IACD,OAAOA,KAAK,CAACE,QAAQ,CAAC,IAAI,IAAI;EAChC,CAAC;;EAED;EACA,MAAMJ,wBAAwB,GAAG,MAAOD,WAAW,IAAK;IACtD,IAAI,CAAC7I,QAAQ,CAACyC,OAAO,IAAI,CAACrC,YAAY,CAACqC,OAAO,EAAE;MAC9CyB,OAAO,CAACgC,IAAI,CAAC,cAAc,CAAC;MAC5B;IACF;IAEA,MAAMiD,EAAE,GAAGN,WAAW,CAACO,SAAS;IAChC,MAAM9D,IAAI,GAAG+D,QAAQ,CAACR,WAAW,CAACS,WAAW,CAAC;IAC9C,MAAMJ,QAAQ,GAAGG,QAAQ,CAACR,WAAW,CAACU,YAAY,IAAI,GAAG,CAAC;IAE1D,IAAI;MACF;MACA,MAAM7B,QAAQ,GAAGtH,YAAY,CAACqC,OAAO,CAACkF,YAAY,CAChDN,UAAU,CAACwB,WAAW,CAACW,WAAW,CAAC,EACnCnC,UAAU,CAACwB,WAAW,CAACY,UAAU,CACnC,CAAC;;MAED;MACA,MAAMC,iBAAiB,GAAG;QACxB,CAAC,EAAE;UAAE;UACH/K,KAAK,EAAE,SAAS;UAChBgL,IAAI,EAAE;YACJ;YACA,CAAC,EAAE;cAAE7C,MAAM,EAAE,GAAG;cAAE8C,KAAK,EAAE,GAAG;cAAEC,MAAM,EAAE;YAAI,CAAC;YAAG;YAC9C,CAAC,EAAE;cAAE/C,MAAM,EAAE,GAAG;cAAE8C,KAAK,EAAE,GAAG;cAAEC,MAAM,EAAE;YAAI,CAAC;YAAG;YAC9C,CAAC,EAAE;cAAE/C,MAAM,EAAE,IAAI;cAAE8C,KAAK,EAAE,GAAG;cAAEC,MAAM,EAAE;YAAI,CAAC,CAAE;UAChD,CAAC;UACD3K,KAAK,EAAE,QAAQ;UACf4K,KAAK,EAAE;QACT,CAAC;QACD,CAAC,EAAE;UAAE;UACHnL,KAAK,EAAE,KAAK;UACZgL,IAAI,EAAE;YAAE7C,MAAM,EAAE,GAAG;YAAE8C,KAAK,EAAE,GAAG;YAAEC,MAAM,EAAE;UAAI,CAAC;UAC9C3K,KAAK,EAAE;QACT,CAAC;QACD,CAAC,EAAE;UAAE;UACHP,KAAK,EAAE,UAAU;UACjBgL,IAAI,EAAE;YAAE7C,MAAM,EAAE,GAAG;YAAE8C,KAAK,EAAE,GAAG;YAAEC,MAAM,EAAE;UAAI,CAAC;UAC9C3K,KAAK,EAAE;QACT;MACF,CAAC;MAED,MAAM6K,MAAM,GAAGL,iBAAiB,CAACpE,IAAI,CAAC,IAAIoE,iBAAiB,CAAC,CAAC,CAAC;MAC9D;MACA,MAAMM,UAAU,GAAG;QACjBlD,MAAM,EAAEO,UAAU,CAACwB,WAAW,CAACoB,UAAU,IAAI,GAAG,CAAC,GAAG,GAAG;QACvDL,KAAK,EAAEvC,UAAU,CAACwB,WAAW,CAACqB,SAAS,IAAI,GAAG,CAAC,GAAG,GAAG;QACrDL,MAAM,EAAExC,UAAU,CAACwB,WAAW,CAACsB,UAAU,IAAI,GAAG,CAAC,GAAG;MACtD,CAAC;;MAED;MACA,MAAMR,IAAI,GAAG;QACX7C,MAAM,EAAEkD,UAAU,CAAClD,MAAM,GAAG,CAAC,GAAGkD,UAAU,CAAClD,MAAM,GACxCxB,IAAI,KAAK,CAAC,GAAGyE,MAAM,CAACJ,IAAI,CAACT,QAAQ,CAAC,CAACpC,MAAM,GAAGiD,MAAM,CAACJ,IAAI,CAAC7C,MAAO;QACxE8C,KAAK,EAAEI,UAAU,CAACJ,KAAK,GAAG,CAAC,GAAGI,UAAU,CAACJ,KAAK,GACtCtE,IAAI,KAAK,CAAC,GAAGyE,MAAM,CAACJ,IAAI,CAACT,QAAQ,CAAC,CAACU,KAAK,GAAGG,MAAM,CAACJ,IAAI,CAACC,KAAM;QACrEC,MAAM,EAAEG,UAAU,CAACH,MAAM,GAAG,CAAC,GAAGG,UAAU,CAACH,MAAM,GACxCvE,IAAI,KAAK,CAAC,GAAGyE,MAAM,CAACJ,IAAI,CAACT,QAAQ,CAAC,CAACW,MAAM,GAAGE,MAAM,CAACJ,IAAI,CAACE;MACnE,CAAC;;MAED;MACA,MAAMO,eAAe,GAAG;QACtBjM,QAAQ,EAAE,IAAIpC,KAAK,CAAC8L,OAAO,CAACH,QAAQ,CAACxE,CAAC,EAAE,GAAG,EAAE,CAACwE,QAAQ,CAAClI,CAAC,CAAC;QACzDpB,OAAO,EAAEiJ,UAAU,CAACwB,WAAW,CAACpB,WAAW,IAAI,CAAC,CAAC;QACjDpG,KAAK,EAAEgG,UAAU,CAACwB,WAAW,CAACrB,SAAS,IAAI,CAAC,CAAC;QAC7ClC,IAAI,EAAEA,IAAI;QACV4D,QAAQ,EAAEA,QAAQ;QAClBa,MAAM,EAAEA,MAAM;QACdJ,IAAI,EAAEA,IAAI;QACVU,UAAU,EAAExM,IAAI,CAACC,GAAG,CAAC;MACvB,CAAC;;MAED;MACA,IAAI,CAACV,iBAAiB,CAACkN,GAAG,CAACnB,EAAE,CAAC,EAAE;QAC9B,MAAMoB,IAAI,GAAG,MAAMC,qBAAqB,CAACJ,eAAe,CAAC;QACzD,IAAIG,IAAI,EAAE;UACRA,IAAI,CAACE,IAAI,GAAG,eAAetB,EAAE,IAAI7D,IAAI,IAAI4D,QAAQ,EAAE;UACnDlJ,QAAQ,CAACyC,OAAO,CAACiI,GAAG,CAACH,IAAI,CAAC;UAC1BnN,iBAAiB,CAACuN,GAAG,CAACxB,EAAE,EAAEoB,IAAI,CAAC;UAC/BrG,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;YACrByG,EAAE,EAAEzB,EAAE;YACN0B,EAAE,EAAEvF,IAAI;YACRwF,IAAI,EAAE5B,QAAQ;YACd6B,IAAI,EAAEpB,IAAI;YACVqB,EAAE,EAAEtD,QAAQ;YACZuD,EAAE,EAAEb,eAAe,CAAC/I;UACtB,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,MAAMkJ,IAAI,GAAGnN,iBAAiB,CAAC8N,GAAG,CAAC/B,EAAE,CAAC;QACtCgC,qBAAqB,CAACZ,IAAI,EAAEH,eAAe,CAAC;MAC9C;MAEAlN,mBAAmB,CAACyN,GAAG,CAACxB,EAAE,EAAEiB,eAAe,CAAC;IAC9C,CAAC,CAAC,OAAOzK,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,YAAY,EAAE;QAC1ByL,EAAE,EAAEzL,KAAK,CAACoG,OAAO;QACjBsF,KAAK,EAAElC,EAAE;QACT0B,EAAE,EAAEvF,IAAI;QACRwF,IAAI,EAAE5B,QAAQ;QACdoC,IAAI,EAAEzC;MACR,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAM2B,qBAAqB,GAAG,MAAOvE,IAAI,IAAK;IAC5C,IAAIsE,IAAI;IAER,IAAI;MACF,QAAQtE,IAAI,CAAC8D,MAAM,CAACpL,KAAK;QACvB,KAAK,SAAS;UACZ,IAAI;YACF;YACA,MAAM4M,YAAY,GAAG,MAAMtN,gBAAgB,CACzC,GAAGD,QAAQ,uBAAuB,EAClCiI,IAAI,CAAC9H,QAAQ,EACb8H,IAAI,CAAC7H,OACP,CAAC;YACDmM,IAAI,GAAG,IAAIxO,KAAK,CAACyP,KAAK,CAAC,CAAC;YACxBjB,IAAI,CAACG,GAAG,CAACa,YAAY,CAAC;YACtBA,YAAY,CAACzB,KAAK,CAACa,GAAG,CACpB1E,IAAI,CAAC8D,MAAM,CAACD,KAAK,EACjB7D,IAAI,CAAC8D,MAAM,CAACD,KAAK,EACjB7D,IAAI,CAAC8D,MAAM,CAACD,KACd,CAAC;UACH,CAAC,CAAC,OAAOnK,KAAK,EAAE;YACduE,OAAO,CAACvE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;YACjC;YACA,MAAM8L,QAAQ,GAAG,IAAI1P,KAAK,CAAC2P,WAAW,CACpCzF,IAAI,CAAC0D,IAAI,CAAC7C,MAAM,EAChBb,IAAI,CAAC0D,IAAI,CAACE,MAAM,EAChB5D,IAAI,CAAC0D,IAAI,CAACC,KACZ,CAAC;YACD,MAAM5K,QAAQ,GAAG,IAAIjD,KAAK,CAAC4P,iBAAiB,CAAC;cAC3CzM,KAAK,EAAE+G,IAAI,CAAC8D,MAAM,CAAC7K,KAAK;cACxB0M,WAAW,EAAE,IAAI;cACjBC,OAAO,EAAE;YACX,CAAC,CAAC;YACFtB,IAAI,GAAG,IAAIxO,KAAK,CAAC+P,IAAI,CAACL,QAAQ,EAAEzM,QAAQ,CAAC;UAC3C;UACA;QAEF,KAAK,KAAK;UACR;UACA,MAAMyM,QAAQ,GAAG,IAAI1P,KAAK,CAAC2P,WAAW,CACpCzF,IAAI,CAAC0D,IAAI,CAAC7C,MAAM,EAChBb,IAAI,CAAC0D,IAAI,CAACE,MAAM,EAChB5D,IAAI,CAAC0D,IAAI,CAACC,KACZ,CAAC;UACD,MAAM5K,QAAQ,GAAG,IAAIjD,KAAK,CAAC4P,iBAAiB,CAAC;YAC3CzM,KAAK,EAAE+G,IAAI,CAAC8D,MAAM,CAAC7K,KAAK;YACxB0M,WAAW,EAAE,IAAI;YACjBC,OAAO,EAAE;UACX,CAAC,CAAC;UACFtB,IAAI,GAAG,IAAIxO,KAAK,CAAC+P,IAAI,CAACL,QAAQ,EAAEzM,QAAQ,CAAC;UACzC;QAEF,KAAK,UAAU;UACb;UACA,MAAM+M,WAAW,GAAG,IAAIhQ,KAAK,CAACiQ,gBAAgB,CAC5C,IAAI,EAAE,IAAI,EACV/F,IAAI,CAAC0D,IAAI,CAACE,MAAM,EAChB,CACF,CAAC;UACD,MAAMoC,WAAW,GAAG,IAAIlQ,KAAK,CAAC4P,iBAAiB,CAAC;YAC9CzM,KAAK,EAAE+G,IAAI,CAAC8D,MAAM,CAAC7K,KAAK;YACxB0M,WAAW,EAAE,IAAI;YACjBC,OAAO,EAAE;UACX,CAAC,CAAC;UACFtB,IAAI,GAAG,IAAIxO,KAAK,CAAC+P,IAAI,CAACC,WAAW,EAAEE,WAAW,CAAC;UAC/C;MACJ;MAEA,IAAI1B,IAAI,EAAE;QACRA,IAAI,CAACpM,QAAQ,CAACmB,IAAI,CAAC2G,IAAI,CAAC9H,QAAQ,CAAC;QACjCoM,IAAI,CAAChL,QAAQ,CAACC,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAGuG,IAAI,CAAC7H,OAAO,GAAGqB,IAAI,CAACC,EAAE,GAAG,GAAG;QACxD6K,IAAI,CAAC2B,UAAU,GAAG,IAAI;QACtB3B,IAAI,CAAC4B,aAAa,GAAG,IAAI;MAC3B;MAEA,OAAO5B,IAAI;IACb,CAAC,CAAC,OAAO5K,KAAK,EAAE;MACduE,OAAO,CAACvE,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EACA,MAAMwL,qBAAqB,GAAGA,CAACZ,IAAI,EAAEtE,IAAI,KAAK;IAC5C;IACA,IAAI9J,KAAK,CAAC6G,KAAK,CAACuH,IAAI,CAACpM,QAAQ,CAAC,CAC3B8E,EAAE,CAACgD,IAAI,CAAC9H,QAAQ,EAAE,GAAG,CAAC,CACtBiF,MAAM,CAACjH,KAAK,CAACkH,MAAM,CAAC+I,MAAM,CAACC,IAAI,CAAC,CAChC5I,KAAK,CAAC,CAAC;IAEV,IAAItH,KAAK,CAAC6G,KAAK,CAACuH,IAAI,CAAChL,QAAQ,CAAC,CAC3B0D,EAAE,CAAC;MAAEzD,CAAC,EAAEC,IAAI,CAACC,EAAE,GAAGuG,IAAI,CAAC7H,OAAO,GAAGqB,IAAI,CAACC,EAAE,GAAG;IAAI,CAAC,EAAE,GAAG,CAAC,CACtD0D,MAAM,CAACjH,KAAK,CAACkH,MAAM,CAAC+I,MAAM,CAACC,IAAI,CAAC,CAChC5I,KAAK,CAAC,CAAC;EACZ,CAAC;;EAED;EACA,MAAM6I,OAAO,GAAGxQ,WAAW,CAAC,MAAM;IAChC,IAAIqE,WAAW,CAACsC,OAAO,EAAE;MACvBtC,WAAW,CAACsC,OAAO,CAACC,OAAO,GAAG1F,UAAU,KAAK,QAAQ;IACvD;;IAEA;IACA,IAAImD,WAAW,CAACsC,OAAO,EAAE;MACvBtC,WAAW,CAACsC,OAAO,CAACoB,MAAM,CAAC,CAAC;IAC9B;;IAEA;IACA,IAAI3D,WAAW,CAACuC,OAAO,IAAIzC,QAAQ,CAACyC,OAAO,IAAIxC,SAAS,CAACwC,OAAO,EAAE;MAChEvC,WAAW,CAACuC,OAAO,CAAC8J,MAAM,CAACvM,QAAQ,CAACyC,OAAO,EAAExC,SAAS,CAACwC,OAAO,CAAC;IACjE;;IAEA;IACAtG,KAAK,CAAC0H,MAAM,CAAC,CAAC;IAEd7C,iBAAiB,CAACyB,OAAO,GAAG+J,qBAAqB,CAACF,OAAO,CAAC;EAC5D,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3Q,SAAS,CAAC,MAAM;IACd,MAAM8Q,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFvI,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;;QAEzB;QACA,MAAMvF,KAAK,GAAG,IAAI7C,KAAK,CAAC2Q,KAAK,CAAC,CAAC;QAC/B1M,QAAQ,CAACyC,OAAO,GAAG7D,KAAK;QACxBA,KAAK,CAAC+N,UAAU,GAAG,IAAI5Q,KAAK,CAAC6Q,KAAK,CAAC,QAAQ,CAAC;;QAE5C;QACA,MAAMC,MAAM,GAAG,IAAI9Q,KAAK,CAAC+Q,iBAAiB,CACxC,EAAE,EACFvP,MAAM,CAACwP,UAAU,GAAGxP,MAAM,CAACyP,WAAW,EACtC,GAAG,EACH,IACF,CAAC;QACD/M,SAAS,CAACwC,OAAO,GAAGoK,MAAM;QAC1BA,MAAM,CAAC1O,QAAQ,CAACwM,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;;QAE9B;QACA,MAAMsC,QAAQ,GAAG,IAAIlR,KAAK,CAACmR,aAAa,CAAC;UAAEC,SAAS,EAAE;QAAK,CAAC,CAAC;QAC7DjN,WAAW,CAACuC,OAAO,GAAGwK,QAAQ;QAC9BA,QAAQ,CAACG,OAAO,CAAC7P,MAAM,CAACwP,UAAU,EAAExP,MAAM,CAACyP,WAAW,CAAC;QACvDC,QAAQ,CAACI,SAAS,CAAC3K,OAAO,GAAG,IAAI;QACjC3C,YAAY,CAAC0C,OAAO,CAAC6K,WAAW,CAACL,QAAQ,CAACM,UAAU,CAAC;;QAErD;QACA,MAAMtQ,QAAQ,GAAG,IAAIhB,aAAa,CAAC4Q,MAAM,EAAEI,QAAQ,CAACM,UAAU,CAAC;QAC/DpN,WAAW,CAACsC,OAAO,GAAGxF,QAAQ;QAC9BA,QAAQ,CAACuQ,aAAa,GAAG,IAAI;QAC7BvQ,QAAQ,CAACwQ,aAAa,GAAG,IAAI;;QAE7B;QACArN,YAAY,CAACqC,OAAO,GAAG,IAAIvG,mBAAmB,CAAC,CAAC;QAChD,MAAMkE,YAAY,CAACqC,OAAO,CAACiL,UAAU,CAAC,CAAC;;QAEvC;QACA,MAAMC,YAAY,GAAG,IAAI5R,KAAK,CAAC6R,YAAY,CAAC,QAAQ,EAAE,GAAG,CAAC;QAC1DhP,KAAK,CAAC8L,GAAG,CAACiD,YAAY,CAAC;QAEvB,MAAME,gBAAgB,GAAG,IAAI9R,KAAK,CAAC+R,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;QAClED,gBAAgB,CAAC1P,QAAQ,CAACwM,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC5CkD,gBAAgB,CAAC3B,UAAU,GAAG,IAAI;QAClCtN,KAAK,CAAC8L,GAAG,CAACmD,gBAAgB,CAAC;;QAE3B;QACA,MAAME,cAAc,GAAG,IAAIhS,KAAK,CAACiS,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC;QAC1D,MAAMC,cAAc,GAAG,IAAIlS,KAAK,CAACkD,oBAAoB,CAAC;UACpDC,KAAK,EAAE,QAAQ;UACfE,SAAS,EAAE,GAAG;UACdD,SAAS,EAAE;QACb,CAAC,CAAC;QACF,MAAM+O,MAAM,GAAG,IAAInS,KAAK,CAAC+P,IAAI,CAACiC,cAAc,EAAEE,cAAc,CAAC;QAC7DC,MAAM,CAAC3O,QAAQ,CAAC2D,CAAC,GAAG,CAACzD,IAAI,CAACC,EAAE,GAAG,CAAC;QAChCwO,MAAM,CAAC/B,aAAa,GAAG,IAAI;QAC3BvN,KAAK,CAAC8L,GAAG,CAACwD,MAAM,CAAC;;QAEjB;QACA5B,OAAO,CAAC,CAAC;;QAET;QACApI,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;QACtBtD,gBAAgB,CAAC,IAAI,CAAC;;QAEtB;QACA0D,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAIzD,eAAe,CAACgG,MAAM,GAAG,CAAC,EAAE;UAC9B5C,OAAO,CAACC,GAAG,CAAC,UAAU,EAAErD,eAAe,CAACgG,MAAM,CAAC;UAC/ChG,eAAe,CAACmE,OAAO,CAACc,OAAO,IAAI;YACjCiB,gBAAgB,CAACjB,OAAO,CAAC;UAC3B,CAAC,CAAC;UACFhF,kBAAkB,CAAC,EAAE,CAAC;QACxB;MAEF,CAAC,CAAC,OAAOpB,KAAK,EAAE;QACduE,OAAO,CAACvE,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAClC;IACF,CAAC;IAED,IAAII,YAAY,CAAC0C,OAAO,EAAE;MACxBgK,SAAS,CAAC,CAAC;IACb;;IAEA;IACA,OAAO,MAAM;MACX,IAAIzL,iBAAiB,CAACyB,OAAO,EAAE;QAC7B0L,oBAAoB,CAACnN,iBAAiB,CAACyB,OAAO,CAAC;MACjD;MACA,IAAIvC,WAAW,CAACuC,OAAO,EAAE;QACvBvC,WAAW,CAACuC,OAAO,CAAC2L,OAAO,CAAC,CAAC;MAC/B;MACA,IAAIjO,WAAW,CAACsC,OAAO,EAAE;QACvBtC,WAAW,CAACsC,OAAO,CAAC2L,OAAO,CAAC,CAAC;MAC/B;MACA,IAAI3N,aAAa,CAACgC,OAAO,EAAE;QACzBhC,aAAa,CAACgC,OAAO,CAACkC,KAAK,CAAC,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAAC2H,OAAO,CAAC,CAAC;EAEb,oBACEhQ,OAAA,CAAAE,SAAA;IAAA6R,QAAA,gBACE/R,OAAA;MAAKgS,GAAG,EAAEvO,YAAa;MAACwO,KAAK,EAAE;QAAE3E,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAO;IAAE;MAAA2E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpErS,OAAA;MAAKiS,KAAK,EAAE/M,oBAAqB;MAAA6M,QAAA,gBAC/B/R,OAAA;QACEiS,KAAK,EAAE;UACL,GAAGxM,WAAW;UACdE,eAAe,EAAEX,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EpC,KAAK,EAAEoC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFsN,OAAO,EAAEpM,kBAAmB;QAAA6L,QAAA,EAC7B;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrS,OAAA;QACEiS,KAAK,EAAE;UACL,GAAGxM,WAAW;UACdE,eAAe,EAAEX,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,0BAA0B;UAC/EpC,KAAK,EAAEoC,QAAQ,KAAK,QAAQ,GAAG,OAAO,GAAG;QAC3C,CAAE;QACFsN,OAAO,EAAEjM,kBAAmB;QAAA0L,QAAA,EAC7B;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;;AAED;AAAA7O,EAAA,CAtsBMD,WAAW;AAAAgP,EAAA,GAAXhP,WAAW;AAusBjB,SAASiP,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,MAAMC,MAAM,GAAGpJ,QAAQ,CAACqJ,aAAa,CAAC,QAAQ,CAAC;EAC/C,MAAMC,OAAO,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;EACvCH,MAAM,CAACpF,KAAK,GAAG,GAAG;EAClBoF,MAAM,CAACnF,MAAM,GAAG,EAAE;;EAElB;EACAqF,OAAO,CAACE,IAAI,GAAG,iBAAiB;EAChCF,OAAO,CAACG,SAAS,GAAG,qBAAqB;EACzCH,OAAO,CAACI,SAAS,GAAG,QAAQ;;EAE5B;EACAJ,OAAO,CAACK,QAAQ,CAACR,IAAI,EAAEC,MAAM,CAACpF,KAAK,GAAC,CAAC,EAAEoF,MAAM,CAACnF,MAAM,GAAC,CAAC,CAAC;;EAEvD;EACA,MAAM2F,OAAO,GAAG,IAAIzT,KAAK,CAAC0T,aAAa,CAACT,MAAM,CAAC;EAC/C,MAAMU,cAAc,GAAG,IAAI3T,KAAK,CAAC4T,cAAc,CAAC;IAC9CC,GAAG,EAAEJ,OAAO;IACZ5D,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAMiE,MAAM,GAAG,IAAI9T,KAAK,CAAC+T,MAAM,CAACJ,cAAc,CAAC;EAC/CG,MAAM,CAAC/F,KAAK,CAACa,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;EAE5B,OAAOkF,MAAM;AACf;;AAEA;AACAtS,MAAM,CAACwS,WAAW,GAAG,CAAC7M,CAAC,EAAE1D,CAAC,EAAE2D,CAAC,KAAK;EAChC,IAAI1G,gBAAgB,EAAE;IACpBA,gBAAgB,CAAC0B,QAAQ,CAACwM,GAAG,CAACzH,CAAC,EAAE1D,CAAC,EAAE2D,CAAC,CAAC;IACtC1G,gBAAgB,CAACqL,YAAY,CAAC,CAAC;IAC/BrL,gBAAgB,CAACsL,iBAAiB,CAAC,IAAI,CAAC;IACxC7D,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE;MAACjB,CAAC;MAAE1D,CAAC;MAAE2D;IAAC,CAAC,CAAC;IAClC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA5F,MAAM,CAACyS,eAAe,GAAG,MAAM;EAC7B,IAAI;IACF;IACA,MAAMnD,MAAM,GAAGjH,QAAQ,CAACqK,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACC,cAAc;IAC5E,IAAItD,MAAM,EAAE;MACV;MACA,MAAMuD,MAAM,GAAGvD,MAAM,CAAC1O,QAAQ,CAAC0E,KAAK,CAAC,CAAC;;MAEtC;MACAgK,MAAM,CAAC1O,QAAQ,CAACwM,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;MAC9BkC,MAAM,CAAC9J,EAAE,CAAC4H,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACtBkC,MAAM,CAACjJ,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAEtB;MACAiJ,MAAM,CAAC/E,YAAY,CAAC,CAAC;MACrB+E,MAAM,CAAC9E,iBAAiB,CAAC,IAAI,CAAC;;MAE9B;MACA,MAAM9K,QAAQ,GAAG2I,QAAQ,CAACqK,aAAa,CAAC,QAAQ,CAAC,CAACC,aAAa,CAACG,gBAAgB;MAChF,IAAIpT,QAAQ,EAAE;QACZA,QAAQ,CAAC0G,MAAM,CAACgH,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC5B1N,QAAQ,CAAC4G,MAAM,CAAC,CAAC;MACnB;MAEAK,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;QACxBmM,GAAG,EAAEF,MAAM,CAACG,OAAO,CAAC,CAAC;QACrBC,GAAG,EAAE3D,MAAM,CAAC1O,QAAQ,CAACoS,OAAO,CAAC;MAC/B,CAAC,CAAC;MAEF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOE,CAAC,EAAE;IACVvM,OAAO,CAACvE,KAAK,CAAC,YAAY,EAAE8Q,CAAC,CAAC;IAC9B,OAAO,KAAK;EACd;AACF,CAAC;AAED,eAAe5Q,WAAW;AAAC,IAAAgP,EAAA;AAAA6B,YAAA,CAAA7B,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}