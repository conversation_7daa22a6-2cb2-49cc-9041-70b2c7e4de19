{"ast": null, "code": "import adjust_lon from '../common/adjust_lon';\nimport asinz from '../common/asinz';\nimport { EPSLN } from '../constants/values';\n\n/*\n  reference:\n    Wolfram Mathworld \"Gnomonic Projection\"\n    http://mathworld.wolfram.com/GnomonicProjection.html\n    Accessed: 12th November 2009\n  */\nexport function init() {\n  /* Place parameters in static storage for common use\n      -------------------------------------------------*/\n  this.sin_p14 = Math.sin(this.lat0);\n  this.cos_p14 = Math.cos(this.lat0);\n  // Approximation for projecting points to the horizon (infinity)\n  this.infinity_dist = 1000 * this.a;\n  this.rc = 1;\n}\n\n/* Gnomonic forward equations--mapping lat,long to x,y\n    ---------------------------------------------------*/\nexport function forward(p) {\n  var sinphi, cosphi; /* sin and cos value        */\n  var dlon; /* delta longitude value      */\n  var coslon; /* cos of longitude        */\n  var ksp; /* scale factor          */\n  var g;\n  var x, y;\n  var lon = p.x;\n  var lat = p.y;\n  /* Forward equations\n      -----------------*/\n  dlon = adjust_lon(lon - this.long0);\n  sinphi = Math.sin(lat);\n  cosphi = Math.cos(lat);\n  coslon = Math.cos(dlon);\n  g = this.sin_p14 * sinphi + this.cos_p14 * cosphi * coslon;\n  ksp = 1;\n  if (g > 0 || Math.abs(g) <= EPSLN) {\n    x = this.x0 + this.a * ksp * cosphi * Math.sin(dlon) / g;\n    y = this.y0 + this.a * ksp * (this.cos_p14 * sinphi - this.sin_p14 * cosphi * coslon) / g;\n  } else {\n    // Point is in the opposing hemisphere and is unprojectable\n    // We still need to return a reasonable point, so we project\n    // to infinity, on a bearing\n    // equivalent to the northern hemisphere equivalent\n    // This is a reasonable approximation for short shapes and lines that\n    // straddle the horizon.\n\n    x = this.x0 + this.infinity_dist * cosphi * Math.sin(dlon);\n    y = this.y0 + this.infinity_dist * (this.cos_p14 * sinphi - this.sin_p14 * cosphi * coslon);\n  }\n  p.x = x;\n  p.y = y;\n  return p;\n}\nexport function inverse(p) {\n  var rh; /* Rho */\n  var sinc, cosc;\n  var c;\n  var lon, lat;\n\n  /* Inverse equations\n      -----------------*/\n  p.x = (p.x - this.x0) / this.a;\n  p.y = (p.y - this.y0) / this.a;\n  p.x /= this.k0;\n  p.y /= this.k0;\n  if (rh = Math.sqrt(p.x * p.x + p.y * p.y)) {\n    c = Math.atan2(rh, this.rc);\n    sinc = Math.sin(c);\n    cosc = Math.cos(c);\n    lat = asinz(cosc * this.sin_p14 + p.y * sinc * this.cos_p14 / rh);\n    lon = Math.atan2(p.x * sinc, rh * this.cos_p14 * cosc - p.y * this.sin_p14 * sinc);\n    lon = adjust_lon(this.long0 + lon);\n  } else {\n    lat = this.phic0;\n    lon = 0;\n  }\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\nexport var names = [\"gnom\"];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};", "map": {"version": 3, "names": ["adjust_lon", "asinz", "EPSLN", "init", "sin_p14", "Math", "sin", "lat0", "cos_p14", "cos", "infinity_dist", "a", "rc", "forward", "p", "sinphi", "cosphi", "dlon", "coslon", "ksp", "g", "x", "y", "lon", "lat", "long0", "abs", "x0", "y0", "inverse", "rh", "sinc", "cosc", "c", "k0", "sqrt", "atan2", "phic0", "names"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/proj4/lib/projections/gnom.js"], "sourcesContent": ["import adjust_lon from '../common/adjust_lon';\nimport asinz from '../common/asinz';\nimport {EPSLN} from '../constants/values';\n\n/*\n  reference:\n    Wolfram Mathworld \"Gnomonic Projection\"\n    http://mathworld.wolfram.com/GnomonicProjection.html\n    Accessed: 12th November 2009\n  */\nexport function init() {\n\n  /* Place parameters in static storage for common use\n      -------------------------------------------------*/\n  this.sin_p14 = Math.sin(this.lat0);\n  this.cos_p14 = Math.cos(this.lat0);\n  // Approximation for projecting points to the horizon (infinity)\n  this.infinity_dist = 1000 * this.a;\n  this.rc = 1;\n}\n\n/* Gnomonic forward equations--mapping lat,long to x,y\n    ---------------------------------------------------*/\nexport function forward(p) {\n  var sinphi, cosphi; /* sin and cos value        */\n  var dlon; /* delta longitude value      */\n  var coslon; /* cos of longitude        */\n  var ksp; /* scale factor          */\n  var g;\n  var x, y;\n  var lon = p.x;\n  var lat = p.y;\n  /* Forward equations\n      -----------------*/\n  dlon = adjust_lon(lon - this.long0);\n\n  sinphi = Math.sin(lat);\n  cosphi = Math.cos(lat);\n\n  coslon = Math.cos(dlon);\n  g = this.sin_p14 * sinphi + this.cos_p14 * cosphi * coslon;\n  ksp = 1;\n  if ((g > 0) || (Math.abs(g) <= EPSLN)) {\n    x = this.x0 + this.a * ksp * cosphi * Math.sin(dlon) / g;\n    y = this.y0 + this.a * ksp * (this.cos_p14 * sinphi - this.sin_p14 * cosphi * coslon) / g;\n  }\n  else {\n\n    // Point is in the opposing hemisphere and is unprojectable\n    // We still need to return a reasonable point, so we project\n    // to infinity, on a bearing\n    // equivalent to the northern hemisphere equivalent\n    // This is a reasonable approximation for short shapes and lines that\n    // straddle the horizon.\n\n    x = this.x0 + this.infinity_dist * cosphi * Math.sin(dlon);\n    y = this.y0 + this.infinity_dist * (this.cos_p14 * sinphi - this.sin_p14 * cosphi * coslon);\n\n  }\n  p.x = x;\n  p.y = y;\n  return p;\n}\n\nexport function inverse(p) {\n  var rh; /* Rho */\n  var sinc, cosc;\n  var c;\n  var lon, lat;\n\n  /* Inverse equations\n      -----------------*/\n  p.x = (p.x - this.x0) / this.a;\n  p.y = (p.y - this.y0) / this.a;\n\n  p.x /= this.k0;\n  p.y /= this.k0;\n\n  if ((rh = Math.sqrt(p.x * p.x + p.y * p.y))) {\n    c = Math.atan2(rh, this.rc);\n    sinc = Math.sin(c);\n    cosc = Math.cos(c);\n\n    lat = asinz(cosc * this.sin_p14 + (p.y * sinc * this.cos_p14) / rh);\n    lon = Math.atan2(p.x * sinc, rh * this.cos_p14 * cosc - p.y * this.sin_p14 * sinc);\n    lon = adjust_lon(this.long0 + lon);\n  }\n  else {\n    lat = this.phic0;\n    lon = 0;\n  }\n\n  p.x = lon;\n  p.y = lat;\n  return p;\n}\n\nexport var names = [\"gnom\"];\nexport default {\n  init: init,\n  forward: forward,\n  inverse: inverse,\n  names: names\n};\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,sBAAsB;AAC7C,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAAQC,KAAK,QAAO,qBAAqB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,IAAIA,CAAA,EAAG;EAErB;AACF;EACE,IAAI,CAACC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACC,IAAI,CAAC;EAClC,IAAI,CAACC,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC,IAAI,CAACF,IAAI,CAAC;EAClC;EACA,IAAI,CAACG,aAAa,GAAG,IAAI,GAAG,IAAI,CAACC,CAAC;EAClC,IAAI,CAACC,EAAE,GAAG,CAAC;AACb;;AAEA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,CAAC,EAAE;EACzB,IAAIC,MAAM,EAAEC,MAAM,CAAC,CAAC;EACpB,IAAIC,IAAI,CAAC,CAAC;EACV,IAAIC,MAAM,CAAC,CAAC;EACZ,IAAIC,GAAG,CAAC,CAAC;EACT,IAAIC,CAAC;EACL,IAAIC,CAAC,EAAEC,CAAC;EACR,IAAIC,GAAG,GAAGT,CAAC,CAACO,CAAC;EACb,IAAIG,GAAG,GAAGV,CAAC,CAACQ,CAAC;EACb;AACF;EACEL,IAAI,GAAGjB,UAAU,CAACuB,GAAG,GAAG,IAAI,CAACE,KAAK,CAAC;EAEnCV,MAAM,GAAGV,IAAI,CAACC,GAAG,CAACkB,GAAG,CAAC;EACtBR,MAAM,GAAGX,IAAI,CAACI,GAAG,CAACe,GAAG,CAAC;EAEtBN,MAAM,GAAGb,IAAI,CAACI,GAAG,CAACQ,IAAI,CAAC;EACvBG,CAAC,GAAG,IAAI,CAAChB,OAAO,GAAGW,MAAM,GAAG,IAAI,CAACP,OAAO,GAAGQ,MAAM,GAAGE,MAAM;EAC1DC,GAAG,GAAG,CAAC;EACP,IAAKC,CAAC,GAAG,CAAC,IAAMf,IAAI,CAACqB,GAAG,CAACN,CAAC,CAAC,IAAIlB,KAAM,EAAE;IACrCmB,CAAC,GAAG,IAAI,CAACM,EAAE,GAAG,IAAI,CAAChB,CAAC,GAAGQ,GAAG,GAAGH,MAAM,GAAGX,IAAI,CAACC,GAAG,CAACW,IAAI,CAAC,GAAGG,CAAC;IACxDE,CAAC,GAAG,IAAI,CAACM,EAAE,GAAG,IAAI,CAACjB,CAAC,GAAGQ,GAAG,IAAI,IAAI,CAACX,OAAO,GAAGO,MAAM,GAAG,IAAI,CAACX,OAAO,GAAGY,MAAM,GAAGE,MAAM,CAAC,GAAGE,CAAC;EAC3F,CAAC,MACI;IAEH;IACA;IACA;IACA;IACA;IACA;;IAEAC,CAAC,GAAG,IAAI,CAACM,EAAE,GAAG,IAAI,CAACjB,aAAa,GAAGM,MAAM,GAAGX,IAAI,CAACC,GAAG,CAACW,IAAI,CAAC;IAC1DK,CAAC,GAAG,IAAI,CAACM,EAAE,GAAG,IAAI,CAAClB,aAAa,IAAI,IAAI,CAACF,OAAO,GAAGO,MAAM,GAAG,IAAI,CAACX,OAAO,GAAGY,MAAM,GAAGE,MAAM,CAAC;EAE7F;EACAJ,CAAC,CAACO,CAAC,GAAGA,CAAC;EACPP,CAAC,CAACQ,CAAC,GAAGA,CAAC;EACP,OAAOR,CAAC;AACV;AAEA,OAAO,SAASe,OAAOA,CAACf,CAAC,EAAE;EACzB,IAAIgB,EAAE,CAAC,CAAC;EACR,IAAIC,IAAI,EAAEC,IAAI;EACd,IAAIC,CAAC;EACL,IAAIV,GAAG,EAAEC,GAAG;;EAEZ;AACF;EACEV,CAAC,CAACO,CAAC,GAAG,CAACP,CAAC,CAACO,CAAC,GAAG,IAAI,CAACM,EAAE,IAAI,IAAI,CAAChB,CAAC;EAC9BG,CAAC,CAACQ,CAAC,GAAG,CAACR,CAAC,CAACQ,CAAC,GAAG,IAAI,CAACM,EAAE,IAAI,IAAI,CAACjB,CAAC;EAE9BG,CAAC,CAACO,CAAC,IAAI,IAAI,CAACa,EAAE;EACdpB,CAAC,CAACQ,CAAC,IAAI,IAAI,CAACY,EAAE;EAEd,IAAKJ,EAAE,GAAGzB,IAAI,CAAC8B,IAAI,CAACrB,CAAC,CAACO,CAAC,GAAGP,CAAC,CAACO,CAAC,GAAGP,CAAC,CAACQ,CAAC,GAAGR,CAAC,CAACQ,CAAC,CAAC,EAAG;IAC3CW,CAAC,GAAG5B,IAAI,CAAC+B,KAAK,CAACN,EAAE,EAAE,IAAI,CAAClB,EAAE,CAAC;IAC3BmB,IAAI,GAAG1B,IAAI,CAACC,GAAG,CAAC2B,CAAC,CAAC;IAClBD,IAAI,GAAG3B,IAAI,CAACI,GAAG,CAACwB,CAAC,CAAC;IAElBT,GAAG,GAAGvB,KAAK,CAAC+B,IAAI,GAAG,IAAI,CAAC5B,OAAO,GAAIU,CAAC,CAACQ,CAAC,GAAGS,IAAI,GAAG,IAAI,CAACvB,OAAO,GAAIsB,EAAE,CAAC;IACnEP,GAAG,GAAGlB,IAAI,CAAC+B,KAAK,CAACtB,CAAC,CAACO,CAAC,GAAGU,IAAI,EAAED,EAAE,GAAG,IAAI,CAACtB,OAAO,GAAGwB,IAAI,GAAGlB,CAAC,CAACQ,CAAC,GAAG,IAAI,CAAClB,OAAO,GAAG2B,IAAI,CAAC;IAClFR,GAAG,GAAGvB,UAAU,CAAC,IAAI,CAACyB,KAAK,GAAGF,GAAG,CAAC;EACpC,CAAC,MACI;IACHC,GAAG,GAAG,IAAI,CAACa,KAAK;IAChBd,GAAG,GAAG,CAAC;EACT;EAEAT,CAAC,CAACO,CAAC,GAAGE,GAAG;EACTT,CAAC,CAACQ,CAAC,GAAGE,GAAG;EACT,OAAOV,CAAC;AACV;AAEA,OAAO,IAAIwB,KAAK,GAAG,CAAC,MAAM,CAAC;AAC3B,eAAe;EACbnC,IAAI,EAAEA,IAAI;EACVU,OAAO,EAAEA,OAAO;EAChBgB,OAAO,EAAEA,OAAO;EAChBS,KAAK,EAAEA;AACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}