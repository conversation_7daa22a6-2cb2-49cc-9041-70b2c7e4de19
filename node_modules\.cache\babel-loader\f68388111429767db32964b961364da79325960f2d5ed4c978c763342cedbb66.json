{"ast": null, "code": "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useStyle from './style';\nconst Typography = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      component: Component = 'article',\n      className,\n      rootClassName,\n      setContentRef,\n      children,\n      direction: typographyDirection,\n      style\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"component\", \"className\", \"rootClassName\", \"setContentRef\", \"children\", \"direction\", \"style\"]);\n  const {\n    getPrefixCls,\n    direction: contextDirection,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('typography');\n  const direction = typographyDirection !== null && typographyDirection !== void 0 ? typographyDirection : contextDirection;\n  const mergedRef = setContentRef ? composeRef(ref, setContentRef) : ref;\n  const prefixCls = getPrefixCls('typography', customizePrefixCls);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography');\n    warning.deprecated(!setContentRef, 'setContentRef', 'ref');\n  }\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const componentClassName = classNames(prefixCls, contextClassName, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, rootClassName, hashId, cssVarCls);\n  const mergedStyle = Object.assign(Object.assign({}, contextStyle), style);\n  return wrapCSSVar(/*#__PURE__*/\n  // @ts-expect-error: Expression produces a union type that is too complex to represent.\n  React.createElement(Component, Object.assign({\n    className: componentClassName,\n    style: mergedStyle,\n    ref: mergedRef\n  }, restProps), children));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Typography.displayName = 'Typography';\n}\nexport default Typography;", "map": {"version": 3, "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "React", "classNames", "composeRef", "devUseW<PERSON>ning", "useComponentConfig", "useStyle", "Typography", "forwardRef", "props", "ref", "prefixCls", "customizePrefixCls", "component", "Component", "className", "rootClassName", "setContentRef", "children", "direction", "typographyDirection", "style", "restProps", "getPrefixCls", "contextDirection", "contextClassName", "contextStyle", "mergedRef", "process", "env", "NODE_ENV", "warning", "deprecated", "wrapCSSVar", "hashId", "cssVarCls", "componentClassName", "mergedStyle", "assign", "createElement", "displayName"], "sources": ["G:/AI_tools/cursor/projects/education web/node_modules/antd/es/typography/Typography.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { devUseWarning } from '../_util/warning';\nimport { useComponentConfig } from '../config-provider/context';\nimport useStyle from './style';\nconst Typography = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      component: Component = 'article',\n      className,\n      rootClassName,\n      setContentRef,\n      children,\n      direction: typographyDirection,\n      style\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"component\", \"className\", \"rootClassName\", \"setContentRef\", \"children\", \"direction\", \"style\"]);\n  const {\n    getPrefixCls,\n    direction: contextDirection,\n    className: contextClassName,\n    style: contextStyle\n  } = useComponentConfig('typography');\n  const direction = typographyDirection !== null && typographyDirection !== void 0 ? typographyDirection : contextDirection;\n  const mergedRef = setContentRef ? composeRef(ref, setContentRef) : ref;\n  const prefixCls = getPrefixCls('typography', customizePrefixCls);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Typography');\n    warning.deprecated(!setContentRef, 'setContentRef', 'ref');\n  }\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const componentClassName = classNames(prefixCls, contextClassName, {\n    [`${prefixCls}-rtl`]: direction === 'rtl'\n  }, className, rootClassName, hashId, cssVarCls);\n  const mergedStyle = Object.assign(Object.assign({}, contextStyle), style);\n  return wrapCSSVar(\n  /*#__PURE__*/\n  // @ts-expect-error: Expression produces a union type that is too complex to represent.\n  React.createElement(Component, Object.assign({\n    className: componentClassName,\n    style: mergedStyle,\n    ref: mergedRef\n  }, restProps), children));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Typography.displayName = 'Typography';\n}\nexport default Typography;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAG,IAAI,IAAI,IAAI,CAACA,MAAM,IAAI,UAAUC,CAAC,EAAEC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,CAAC,EAAEG,CAAC,CAAC,IAAIF,CAAC,CAACO,OAAO,CAACL,CAAC,CAAC,GAAG,CAAC,EAAED,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAChG,IAAIH,CAAC,IAAI,IAAI,IAAI,OAAOI,MAAM,CAACK,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGC,MAAM,CAACK,qBAAqB,CAACT,CAAC,CAAC,EAAEU,CAAC,GAAGP,CAAC,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3I,IAAIT,CAAC,CAACO,OAAO,CAACL,CAAC,CAACO,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIN,MAAM,CAACC,SAAS,CAACO,oBAAoB,CAACL,IAAI,CAACP,CAAC,EAAEG,CAAC,CAACO,CAAC,CAAC,CAAC,EAAER,CAAC,CAACC,CAAC,CAACO,CAAC,CAAC,CAAC,GAAGV,CAAC,CAACG,CAAC,CAACO,CAAC,CAAC,CAAC;EACnG;EACA,OAAOR,CAAC;AACV,CAAC;AACD,OAAO,KAAKW,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,OAAOC,QAAQ,MAAM,SAAS;AAC9B,MAAMC,UAAU,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC/D,MAAM;MACFC,SAAS,EAAEC,kBAAkB;MAC7BC,SAAS,EAAEC,SAAS,GAAG,SAAS;MAChCC,SAAS;MACTC,aAAa;MACbC,aAAa;MACbC,QAAQ;MACRC,SAAS,EAAEC,mBAAmB;MAC9BC;IACF,CAAC,GAAGZ,KAAK;IACTa,SAAS,GAAGnC,MAAM,CAACsB,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;EACxI,MAAM;IACJc,YAAY;IACZJ,SAAS,EAAEK,gBAAgB;IAC3BT,SAAS,EAAEU,gBAAgB;IAC3BJ,KAAK,EAAEK;EACT,CAAC,GAAGrB,kBAAkB,CAAC,YAAY,CAAC;EACpC,MAAMc,SAAS,GAAGC,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAGA,mBAAmB,GAAGI,gBAAgB;EACzH,MAAMG,SAAS,GAAGV,aAAa,GAAGd,UAAU,CAACO,GAAG,EAAEO,aAAa,CAAC,GAAGP,GAAG;EACtE,MAAMC,SAAS,GAAGY,YAAY,CAAC,YAAY,EAAEX,kBAAkB,CAAC;EAChE,IAAIgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,OAAO,GAAG3B,aAAa,CAAC,YAAY,CAAC;IAC3C2B,OAAO,CAACC,UAAU,CAAC,CAACf,aAAa,EAAE,eAAe,EAAE,KAAK,CAAC;EAC5D;EACA;EACA,MAAM,CAACgB,UAAU,EAAEC,MAAM,EAAEC,SAAS,CAAC,GAAG7B,QAAQ,CAACK,SAAS,CAAC;EAC3D,MAAMyB,kBAAkB,GAAGlC,UAAU,CAACS,SAAS,EAAEc,gBAAgB,EAAE;IACjE,CAAC,GAAGd,SAAS,MAAM,GAAGQ,SAAS,KAAK;EACtC,CAAC,EAAEJ,SAAS,EAAEC,aAAa,EAAEkB,MAAM,EAAEC,SAAS,CAAC;EAC/C,MAAME,WAAW,GAAG7C,MAAM,CAAC8C,MAAM,CAAC9C,MAAM,CAAC8C,MAAM,CAAC,CAAC,CAAC,EAAEZ,YAAY,CAAC,EAAEL,KAAK,CAAC;EACzE,OAAOY,UAAU,CACjB;EACA;EACAhC,KAAK,CAACsC,aAAa,CAACzB,SAAS,EAAEtB,MAAM,CAAC8C,MAAM,CAAC;IAC3CvB,SAAS,EAAEqB,kBAAkB;IAC7Bf,KAAK,EAAEgB,WAAW;IAClB3B,GAAG,EAAEiB;EACP,CAAC,EAAEL,SAAS,CAAC,EAAEJ,QAAQ,CAAC,CAAC;AAC3B,CAAC,CAAC;AACF,IAAIU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCvB,UAAU,CAACiC,WAAW,GAAG,YAAY;AACvC;AACA,eAAejC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}